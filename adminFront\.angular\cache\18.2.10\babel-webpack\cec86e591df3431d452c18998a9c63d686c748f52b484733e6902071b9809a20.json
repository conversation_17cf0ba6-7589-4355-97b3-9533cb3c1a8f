{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { finalize, mergeMap, tap } from 'rxjs';\nimport * as XLSX from 'xlsx';\nimport { SharedModule } from '../../components/shared.module';\nimport { BaseComponent } from '../../components/base/baseComponent';\nimport { EnumStatus, EnumStatusHelper } from 'src/app/shared/enum/enumStatus';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/shared/helper/allowHelper\";\nimport * as i2 from \"@nebular/theme\";\nimport * as i3 from \"src/app/shared/services/message.service\";\nimport * as i4 from \"src/app/shared/helper/validationHelper\";\nimport * as i5 from \"src/services/api/services\";\nimport * as i6 from \"src/app/shared/services/utility.service\";\nimport * as i7 from \"@angular/common\";\nimport * as i8 from \"@angular/forms\";\nimport * as i9 from \"../../components/breadcrumb/breadcrumb.component\";\nimport * as i10 from \"../../components/pagination/pagination.component\";\nfunction BuildingMaterialComponent_nb_option_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 36);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const buildCase_r2 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", buildCase_r2.cID);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", buildCase_r2.CBuildCaseName, \" \");\n  }\n}\nfunction BuildingMaterialComponent_button_27_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 37);\n    i0.ɵɵlistener(\"click\", function BuildingMaterialComponent_button_27_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.exportExelMaterialList());\n    });\n    i0.ɵɵtext(1, \"\\u532F\\u51FA \");\n    i0.ɵɵelement(2, \"i\", 24);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction BuildingMaterialComponent_button_28_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 38);\n    i0.ɵɵlistener(\"click\", function BuildingMaterialComponent_button_28_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.search());\n    });\n    i0.ɵɵtext(1, \" \\u67E5\\u8A62 \");\n    i0.ɵɵelement(2, \"i\", 39);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction BuildingMaterialComponent_button_29_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 40);\n    i0.ɵɵlistener(\"click\", function BuildingMaterialComponent_button_29_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r3 = i0.ɵɵnextContext();\n      const dialog_r7 = i0.ɵɵreference(61);\n      return i0.ɵɵresetView(ctx_r3.addNew(dialog_r7));\n    });\n    i0.ɵɵtext(1, \"\\u55AE\\u7B46\\u65B0\\u589E \");\n    i0.ɵɵelement(2, \"i\", 41);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction BuildingMaterialComponent_button_30_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 42);\n    i0.ɵɵlistener(\"click\", function BuildingMaterialComponent_button_30_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r8);\n      i0.ɵɵnextContext();\n      const inputFile_r9 = i0.ɵɵreference(32);\n      return i0.ɵɵresetView(inputFile_r9.click());\n    });\n    i0.ɵɵtext(1, \" \\u6279\\u6B21\\u532F\\u5165 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction BuildingMaterialComponent_th_54_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 28);\n    i0.ɵɵtext(1, \"\\u50F9\\u683C\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction BuildingMaterialComponent_tbody_57_tr_1_span_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 50);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r10 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(item_r10.CSelectPictureId.length);\n  }\n}\nfunction BuildingMaterialComponent_tbody_57_tr_1_span_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 51);\n    i0.ɵɵtext(1, \"0\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction BuildingMaterialComponent_tbody_57_tr_1_td_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r10 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(item_r10.CPrice);\n  }\n}\nfunction BuildingMaterialComponent_tbody_57_tr_1_button_19_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 52);\n    i0.ɵɵlistener(\"click\", function BuildingMaterialComponent_tbody_57_tr_1_button_19_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const item_r10 = i0.ɵɵnextContext().$implicit;\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      const dialog_r7 = i0.ɵɵreference(61);\n      return i0.ɵɵresetView(ctx_r3.onSelectedMaterial(item_r10, dialog_r7));\n    });\n    i0.ɵɵtext(1, \"\\u7DE8\\u8F2F\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction BuildingMaterialComponent_tbody_57_tr_1_button_20_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 53);\n    i0.ɵɵlistener(\"click\", function BuildingMaterialComponent_tbody_57_tr_1_button_20_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r12);\n      const item_r10 = i0.ɵɵnextContext().$implicit;\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      const imageBinder_r13 = i0.ɵɵreference(63);\n      return i0.ɵɵresetView(ctx_r3.bindImageForMaterial(item_r10, imageBinder_r13));\n    });\n    i0.ɵɵelement(1, \"i\", 54);\n    i0.ɵɵtext(2, \" \\u7D81\\u5B9A \");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r10 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵproperty(\"title\", \"\\u70BA \" + item_r10.CName + \" \\u7D81\\u5B9A\\u5716\\u7247\");\n  }\n}\nfunction BuildingMaterialComponent_tbody_57_tr_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"td\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"td\");\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"td\")(12, \"div\", 44);\n    i0.ɵɵtemplate(13, BuildingMaterialComponent_tbody_57_tr_1_span_13_Template, 2, 1, \"span\", 45)(14, BuildingMaterialComponent_tbody_57_tr_1_span_14_Template, 2, 0, \"span\", 46);\n    i0.ɵɵelementStart(15, \"span\");\n    i0.ɵɵtext(16);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(17, BuildingMaterialComponent_tbody_57_tr_1_td_17_Template, 2, 1, \"td\", 33);\n    i0.ɵɵelementStart(18, \"td\", 47);\n    i0.ɵɵtemplate(19, BuildingMaterialComponent_tbody_57_tr_1_button_19_Template, 2, 0, \"button\", 48)(20, BuildingMaterialComponent_tbody_57_tr_1_button_20_Template, 3, 1, \"button\", 49);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const item_r10 = ctx.$implicit;\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r10.CId);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r10.CImageCode || \"\\u5F85\\u8A2D\\u5B9A\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate3(\"\", item_r10.CName, \" - \", item_r10.CPart, \" - \", item_r10.CLocation, \"\");\n    i0.ɵɵadvance();\n    i0.ɵɵstyleMap(!item_r10.CIsMapping ? \"color: red\" : \"\");\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(item_r10.CSelectName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r10.CDescription);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", item_r10.CSelectPictureId && item_r10.CSelectPictureId.length > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !item_r10.CSelectPictureId || item_r10.CSelectPictureId.length === 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r10.CSelectPictureId && item_r10.CSelectPictureId.length > 0 ? \"\\u5DF2\\u7D81\\u5B9A\" : \"\\u672A\\u7D81\\u5B9A\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", item_r10.CShowPrice == true);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.isRead);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.isRead);\n  }\n}\nfunction BuildingMaterialComponent_tbody_57_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tbody\");\n    i0.ɵɵtemplate(1, BuildingMaterialComponent_tbody_57_tr_1_Template, 21, 15, \"tr\", 43);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.materialList);\n  }\n}\nfunction BuildingMaterialComponent_ng_template_60_div_42_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 75);\n    i0.ɵɵelement(1, \"i\", 76);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \\u5DF2\\u8A2D\\u5B9A\\u5EFA\\u6750\\u4EE3\\u865F: \", ctx_r3.selectedMaterial.CImageCode, \" \");\n  }\n}\nfunction BuildingMaterialComponent_ng_template_60_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r14 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-card\", 55)(1, \"nb-card-header\");\n    i0.ɵɵtext(2, \" \\u5EFA\\u6750\\u7BA1\\u7406 > \\u65B0\\u589E\\u5EFA\\u6750 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"nb-card-body\", 56)(4, \"h5\", 57);\n    i0.ɵɵtext(5, \"\\u8ACB\\u8F38\\u5165\\u4E0B\\u65B9\\u5167\\u5BB9\\u65B0\\u589E\\u5EFA\\u6750\\u3002\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 58)(7, \"div\", 59)(8, \"label\", 60);\n    i0.ɵɵtext(9, \"\\u5EFA\\u6750\\u4EE3\\u865F\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"input\", 61);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function BuildingMaterialComponent_ng_template_60_Template_input_ngModelChange_10_listener($event) {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r3 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r3.selectedMaterial.CImageCode, $event) || (ctx_r3.selectedMaterial.CImageCode = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"div\", 62)(12, \"label\", 60);\n    i0.ɵɵtext(13, \"\\u540D\\u7A31\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"input\", 63);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function BuildingMaterialComponent_ng_template_60_Template_input_ngModelChange_14_listener($event) {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r3 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r3.selectedMaterial.CName, $event) || (ctx_r3.selectedMaterial.CName = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(15, \"div\", 62)(16, \"label\", 60);\n    i0.ɵɵtext(17, \"\\u9805\\u76EE\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"input\", 63);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function BuildingMaterialComponent_ng_template_60_Template_input_ngModelChange_18_listener($event) {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r3 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r3.selectedMaterial.CPart, $event) || (ctx_r3.selectedMaterial.CPart = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(19, \"div\", 62)(20, \"label\", 60);\n    i0.ɵɵtext(21, \"\\u4F4D\\u7F6E\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"input\", 63);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function BuildingMaterialComponent_ng_template_60_Template_input_ngModelChange_22_listener($event) {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r3 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r3.selectedMaterial.CLocation, $event) || (ctx_r3.selectedMaterial.CLocation = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(23, \"div\", 62)(24, \"label\", 60);\n    i0.ɵɵtext(25, \"\\u5EFA\\u6750\\u9078\\u9805\\u540D\\u7A31\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"input\", 64);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function BuildingMaterialComponent_ng_template_60_Template_input_ngModelChange_26_listener($event) {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r3 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r3.selectedMaterial.CSelectName, $event) || (ctx_r3.selectedMaterial.CSelectName = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(27, \"div\", 62)(28, \"label\", 65);\n    i0.ɵɵtext(29, \"\\u5EFA\\u6750\\u8AAA\\u660E\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(30, \"textarea\", 66);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function BuildingMaterialComponent_ng_template_60_Template_textarea_ngModelChange_30_listener($event) {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r3 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r3.selectedMaterial.CDescription, $event) || (ctx_r3.selectedMaterial.CDescription = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(31, \"div\", 62)(32, \"label\", 65);\n    i0.ɵɵtext(33, \"\\u50F9\\u683C\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(34, \"input\", 67);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function BuildingMaterialComponent_ng_template_60_Template_input_ngModelChange_34_listener($event) {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r3 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r3.selectedMaterial.CPrice, $event) || (ctx_r3.selectedMaterial.CPrice = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(35, \"div\", 68)(36, \"label\", 65);\n    i0.ɵɵtext(37, \"\\u5716\\u7247\\u7D81\\u5B9A\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(38, \"div\", 69)(39, \"button\", 70);\n    i0.ɵɵlistener(\"click\", function BuildingMaterialComponent_ng_template_60_Template_button_click_39_listener() {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r3 = i0.ɵɵnextContext();\n      const imageBinder_r13 = i0.ɵɵreference(63);\n      return i0.ɵɵresetView(ctx_r3.openImageBinder(imageBinder_r13));\n    });\n    i0.ɵɵelement(40, \"i\", 71);\n    i0.ɵɵtext(41, \"\\u9078\\u64C7\\u5716\\u7247 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(42, BuildingMaterialComponent_ng_template_60_div_42_Template, 3, 1, \"div\", 72);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(43, \"nb-card-footer\", 34)(44, \"button\", 73);\n    i0.ɵɵlistener(\"click\", function BuildingMaterialComponent_ng_template_60_Template_button_click_44_listener() {\n      const ref_r15 = i0.ɵɵrestoreView(_r14).dialogRef;\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.onClose(ref_r15));\n    });\n    i0.ɵɵtext(45, \"\\u95DC\\u9589\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(46, \"button\", 74);\n    i0.ɵɵlistener(\"click\", function BuildingMaterialComponent_ng_template_60_Template_button_click_46_listener() {\n      const ref_r15 = i0.ɵɵrestoreView(_r14).dialogRef;\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.onSubmit(ref_r15));\n    });\n    i0.ɵɵtext(47, \"\\u5132\\u5B58\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(10);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r3.selectedMaterial.CImageCode);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r3.selectedMaterial.CName);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r3.selectedMaterial.CPart);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r3.selectedMaterial.CLocation);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r3.selectedMaterial.CSelectName);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r3.selectedMaterial.CDescription);\n    i0.ɵɵproperty(\"rows\", 4);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r3.selectedMaterial.CPrice);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"title\", \"\\u70BA\\u5EFA\\u6750\\u7D81\\u5B9A\\u5716\\u7247\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.selectedMaterial.CImageCode);\n  }\n}\nfunction BuildingMaterialComponent_ng_template_62_nb_option_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 36);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const option_r17 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", option_r17.value);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", option_r17.label, \" \");\n  }\n}\nfunction BuildingMaterialComponent_ng_template_62_nb_option_27_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 36);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const option_r18 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", option_r18.value);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", option_r18.label, \" \");\n  }\n}\nfunction BuildingMaterialComponent_ng_template_62_div_46_i_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 123);\n  }\n}\nfunction BuildingMaterialComponent_ng_template_62_div_46_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 124);\n    i0.ɵɵtext(1, \" \\u5DF2\\u7D81\\u5B9A \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction BuildingMaterialComponent_ng_template_62_div_46_img_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 125);\n  }\n  if (rf & 2) {\n    const image_r20 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵproperty(\"src\", image_r20.thumbnailUrl, i0.ɵɵsanitizeUrl)(\"alt\", image_r20.name);\n  }\n}\nfunction BuildingMaterialComponent_ng_template_62_div_46_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 126);\n    i0.ɵɵelement(1, \"i\", 127);\n    i0.ɵɵelementStart(2, \"div\", 128);\n    i0.ɵɵtext(3, \"\\u7121\\u9810\\u89BD\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction BuildingMaterialComponent_ng_template_62_div_46_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r19 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 110);\n    i0.ɵɵlistener(\"click\", function BuildingMaterialComponent_ng_template_62_div_46_Template_div_click_0_listener() {\n      const image_r20 = i0.ɵɵrestoreView(_r19).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r3.toggleImageSelection(image_r20));\n    });\n    i0.ɵɵelementStart(1, \"div\", 111)(2, \"div\", 112);\n    i0.ɵɵtemplate(3, BuildingMaterialComponent_ng_template_62_div_46_i_3_Template, 1, 0, \"i\", 113);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, BuildingMaterialComponent_ng_template_62_div_46_div_4_Template, 2, 0, \"div\", 114);\n    i0.ɵɵelementStart(5, \"button\", 115);\n    i0.ɵɵlistener(\"click\", function BuildingMaterialComponent_ng_template_62_div_46_Template_button_click_5_listener($event) {\n      const image_r20 = i0.ɵɵrestoreView(_r19).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      const imagePreview_r21 = i0.ɵɵreference(65);\n      return i0.ɵɵresetView(ctx_r3.previewImage(image_r20, imagePreview_r21, $event));\n    });\n    i0.ɵɵelement(6, \"i\", 116);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 117);\n    i0.ɵɵtemplate(8, BuildingMaterialComponent_ng_template_62_div_46_img_8_Template, 1, 2, \"img\", 118)(9, BuildingMaterialComponent_ng_template_62_div_46_div_9_Template, 4, 0, \"div\", 119);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"div\", 120)(11, \"div\", 121);\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"div\", 122);\n    i0.ɵɵtext(14);\n    i0.ɵɵpipe(15, \"number\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const image_r20 = ctx.$implicit;\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassProp(\"selected\", ctx_r3.isImageSelected(image_r20));\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"checked\", ctx_r3.isImageSelected(image_r20));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.isImageSelected(image_r20));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.selectedMaterial && ctx_r3.selectedMaterial.CSelectPictureId && ctx_r3.selectedMaterial.CSelectPictureId.indexOf(image_r20.id) > -1);\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassProp(\"border-success\", ctx_r3.selectedMaterial && ctx_r3.selectedMaterial.CSelectPictureId && ctx_r3.selectedMaterial.CSelectPictureId.indexOf(image_r20.id) > -1);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", image_r20.thumbnailUrl);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !image_r20.thumbnailUrl);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"title\", image_r20.name);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(image_r20.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", i0.ɵɵpipeBind1(15, 13, image_r20.size), \" KB\");\n  }\n}\nfunction BuildingMaterialComponent_ng_template_62_div_47_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 129);\n    i0.ɵɵelement(1, \"i\", 130);\n    i0.ɵɵelementStart(2, \"div\");\n    i0.ɵɵtext(3, \"\\u627E\\u4E0D\\u5230\\u76F8\\u7B26\\u7684\\u5716\\u7247\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction BuildingMaterialComponent_ng_template_62_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r16 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-card\", 77)(1, \"nb-card-header\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"nb-card-body\", 78)(4, \"div\", 79)(5, \"div\", 80);\n    i0.ɵɵelement(6, \"i\", 81);\n    i0.ɵɵelementStart(7, \"div\", 82)(8, \"div\", 83);\n    i0.ɵɵtext(9, \"\\u5EFA\\u6750\\u4EE3\\u865F\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"div\", 84)(11, \"span\", 85);\n    i0.ɵɵtext(12, \"\\u7576\\u524D\\u5EFA\\u6750\\u4EE3\\u865F\\uFF1A\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"span\", 86);\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(15, \"div\");\n    i0.ɵɵtext(16, \"\\u9078\\u64C7\\u5716\\u7247\\u5F8C\\uFF0C\\u5EFA\\u6750\\u4EE3\\u865F\\u5C07\\u6703\\u81EA\\u52D5\\u8A2D\\u5B9A\\u70BA\\u6240\\u9078\\u5716\\u7247\\u7684\\u6A94\\u540D\\uFF0C\\u4E26\\u5EFA\\u7ACB\\u5716\\u7247\\u8207\\u5EFA\\u6750\\u7684\\u7D81\\u5B9A\\u95DC\\u4FC2\\u3002\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(17, \"div\", 87)(18, \"div\", 88)(19, \"label\", 89);\n    i0.ɵɵtext(20, \"\\u5716\\u7247\\u985E\\u5225\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"nb-select\", 90);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function BuildingMaterialComponent_ng_template_62_Template_nb_select_ngModelChange_21_listener($event) {\n      i0.ɵɵrestoreView(_r16);\n      const ctx_r3 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r3.selectedCategory, $event) || (ctx_r3.selectedCategory = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"selectedChange\", function BuildingMaterialComponent_ng_template_62_Template_nb_select_selectedChange_21_listener($event) {\n      i0.ɵɵrestoreView(_r16);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.categoryChanged($event));\n    });\n    i0.ɵɵtemplate(22, BuildingMaterialComponent_ng_template_62_nb_option_22_Template, 2, 2, \"nb-option\", 12);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(23, \"div\", 88)(24, \"label\", 89);\n    i0.ɵɵtext(25, \"\\u7BE9\\u9078\\u7BC4\\u570D\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"nb-select\", 90);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function BuildingMaterialComponent_ng_template_62_Template_nb_select_ngModelChange_26_listener($event) {\n      i0.ɵɵrestoreView(_r16);\n      const ctx_r3 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r3.filterBindingOption, $event) || (ctx_r3.filterBindingOption = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"selectedChange\", function BuildingMaterialComponent_ng_template_62_Template_nb_select_selectedChange_26_listener() {\n      i0.ɵɵrestoreView(_r16);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.filterImages());\n    });\n    i0.ɵɵtemplate(27, BuildingMaterialComponent_ng_template_62_nb_option_27_Template, 2, 2, \"nb-option\", 12);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(28, \"div\", 91)(29, \"label\", 89);\n    i0.ɵɵtext(30, \"\\u641C\\u5C0B\\u5716\\u7247\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(31, \"input\", 92);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function BuildingMaterialComponent_ng_template_62_Template_input_ngModelChange_31_listener($event) {\n      i0.ɵɵrestoreView(_r16);\n      const ctx_r3 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r3.imageSearchTerm, $event) || (ctx_r3.imageSearchTerm = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"input\", function BuildingMaterialComponent_ng_template_62_Template_input_input_31_listener() {\n      i0.ɵɵrestoreView(_r16);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.filterImages());\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(32, \"div\", 93)(33, \"button\", 94);\n    i0.ɵɵlistener(\"click\", function BuildingMaterialComponent_ng_template_62_Template_button_click_33_listener() {\n      i0.ɵɵrestoreView(_r16);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.loadImages());\n    });\n    i0.ɵɵtext(34, \" \\u91CD\\u65B0\\u8F09\\u5165 \");\n    i0.ɵɵelement(35, \"i\", 95);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(36, \"div\", 96)(37, \"button\", 97);\n    i0.ɵɵlistener(\"click\", function BuildingMaterialComponent_ng_template_62_Template_button_click_37_listener() {\n      i0.ɵɵrestoreView(_r16);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.selectAllImages());\n    });\n    i0.ɵɵtext(38, \" \\u5168\\u9078 \");\n    i0.ɵɵelement(39, \"i\", 98);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(40, \"button\", 99);\n    i0.ɵɵlistener(\"click\", function BuildingMaterialComponent_ng_template_62_Template_button_click_40_listener() {\n      i0.ɵɵrestoreView(_r16);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.clearAllSelection());\n    });\n    i0.ɵɵtext(41, \" \\u6E05\\u9664\\u9078\\u53D6 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(42, \"div\", 100);\n    i0.ɵɵtext(43);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(44, \"div\", 101)(45, \"div\", 102);\n    i0.ɵɵtemplate(46, BuildingMaterialComponent_ng_template_62_div_46_Template, 16, 15, \"div\", 103);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(47, BuildingMaterialComponent_ng_template_62_div_47_Template, 4, 0, \"div\", 104);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(48, \"nb-card-footer\", 105)(49, \"div\", 106);\n    i0.ɵɵtext(50);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(51, \"ngx-pagination\", 35);\n    i0.ɵɵtwoWayListener(\"CollectionSizeChange\", function BuildingMaterialComponent_ng_template_62_Template_ngx_pagination_CollectionSizeChange_51_listener($event) {\n      i0.ɵɵrestoreView(_r16);\n      const ctx_r3 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r3.imageTotalRecords, $event) || (ctx_r3.imageTotalRecords = $event);\n      return i0.ɵɵresetView($event);\n    })(\"PageSizeChange\", function BuildingMaterialComponent_ng_template_62_Template_ngx_pagination_PageSizeChange_51_listener($event) {\n      i0.ɵɵrestoreView(_r16);\n      const ctx_r3 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r3.imagePageSize, $event) || (ctx_r3.imagePageSize = $event);\n      return i0.ɵɵresetView($event);\n    })(\"PageChange\", function BuildingMaterialComponent_ng_template_62_Template_ngx_pagination_PageChange_51_listener($event) {\n      i0.ɵɵrestoreView(_r16);\n      const ctx_r3 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r3.imageCurrentPage, $event) || (ctx_r3.imageCurrentPage = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"PageChange\", function BuildingMaterialComponent_ng_template_62_Template_ngx_pagination_PageChange_51_listener($event) {\n      i0.ɵɵrestoreView(_r16);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.imagePageChanged($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(52, \"div\", 107)(53, \"button\", 108);\n    i0.ɵɵlistener(\"click\", function BuildingMaterialComponent_ng_template_62_Template_button_click_53_listener() {\n      const ref_r22 = i0.ɵɵrestoreView(_r16).dialogRef;\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.onCloseImageBinder(ref_r22));\n    });\n    i0.ɵɵtext(54, \"\\u53D6\\u6D88\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(55, \"button\", 109);\n    i0.ɵɵlistener(\"click\", function BuildingMaterialComponent_ng_template_62_Template_button_click_55_listener() {\n      const ref_r22 = i0.ɵɵrestoreView(_r16).dialogRef;\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.onConfirmImageSelection(ref_r22));\n    });\n    i0.ɵɵtext(56);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \\u5716\\u7247\\u7D81\\u5B9A - \", ctx_r3.selectedMaterial.CName ? \"\\u70BA \" + ctx_r3.selectedMaterial.CName + \" \\u9078\\u64C7\\u5EFA\\u6750\\u5716\\u7247\" : \"\\u9078\\u64C7\\u5EFA\\u6750\\u5716\\u7247\", \" \");\n    i0.ɵɵadvance(12);\n    i0.ɵɵtextInterpolate(ctx_r3.selectedMaterial.CImageCode || \"\\u672A\\u8A2D\\u5B9A\");\n    i0.ɵɵadvance(7);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r3.selectedCategory);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.categoryOptions);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r3.filterBindingOption);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.filterBindingOptions);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r3.imageSearchTerm);\n    i0.ɵɵadvance(12);\n    i0.ɵɵtextInterpolate1(\" \\u5DF2\\u9078\\u53D6: \", ctx_r3.selectedImages.length, \" \\u5F35\\u5716\\u7247 \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.filteredImages);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.filteredImages.length === 0);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \\u5171 \", ctx_r3.imageTotalRecords, \" \\u5F35\\u5716\\u7247 \");\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"CollectionSize\", ctx_r3.imageTotalRecords)(\"PageSize\", ctx_r3.imagePageSize)(\"Page\", ctx_r3.imageCurrentPage);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"disabled\", ctx_r3.selectedImages.length === 0);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \\u78BA\\u5B9A\\u9078\\u64C7 (\", ctx_r3.selectedImages.length, \") \");\n  }\n}\nfunction BuildingMaterialComponent_ng_template_64_img_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 138);\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"src\", ctx_r3.previewingImage.fullUrl, i0.ɵɵsanitizeUrl)(\"alt\", ctx_r3.previewingImage.name);\n  }\n}\nfunction BuildingMaterialComponent_ng_template_64_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 126);\n    i0.ɵɵelement(1, \"i\", 139);\n    i0.ɵɵelementStart(2, \"div\");\n    i0.ɵɵtext(3, \"\\u5716\\u7247\\u8F09\\u5165\\u5931\\u6557\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction BuildingMaterialComponent_ng_template_64_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r23 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-card\", 131)(1, \"nb-card-header\", 105)(2, \"span\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 107)(5, \"button\", 132);\n    i0.ɵɵlistener(\"click\", function BuildingMaterialComponent_ng_template_64_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r23);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.previousImage());\n    });\n    i0.ɵɵelement(6, \"i\", 133);\n    i0.ɵɵtext(7, \" \\u4E0A\\u4E00\\u5F35 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"button\", 132);\n    i0.ɵɵlistener(\"click\", function BuildingMaterialComponent_ng_template_64_Template_button_click_8_listener() {\n      i0.ɵɵrestoreView(_r23);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.nextImage());\n    });\n    i0.ɵɵtext(9, \" \\u4E0B\\u4E00\\u5F35 \");\n    i0.ɵɵelement(10, \"i\", 134);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(11, \"nb-card-body\", 135);\n    i0.ɵɵtemplate(12, BuildingMaterialComponent_ng_template_64_img_12_Template, 1, 2, \"img\", 136)(13, BuildingMaterialComponent_ng_template_64_div_13_Template, 4, 0, \"div\", 119);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"nb-card-footer\", 105)(15, \"div\", 106);\n    i0.ɵɵtext(16);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"div\", 107)(18, \"button\", 137);\n    i0.ɵɵlistener(\"click\", function BuildingMaterialComponent_ng_template_64_Template_button_click_18_listener() {\n      i0.ɵɵrestoreView(_r23);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.toggleImageSelectionInPreview());\n    });\n    i0.ɵɵtext(19);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"button\", 108);\n    i0.ɵɵlistener(\"click\", function BuildingMaterialComponent_ng_template_64_Template_button_click_20_listener() {\n      const ref_r24 = i0.ɵɵrestoreView(_r23).dialogRef;\n      return i0.ɵɵresetView(ref_r24.close());\n    });\n    i0.ɵɵtext(21, \"\\u95DC\\u9589\");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\\u5716\\u7247\\u9810\\u89BD - \", ctx_r3.previewingImage == null ? null : ctx_r3.previewingImage.name, \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", ctx_r3.currentPreviewIndex <= 0);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"disabled\", ctx_r3.currentPreviewIndex >= ctx_r3.filteredImages.length - 1);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.previewingImage && ctx_r3.previewingImage.fullUrl);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r3.previewingImage || !ctx_r3.previewingImage.fullUrl);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate2(\" \", ctx_r3.currentPreviewIndex + 1, \" / \", ctx_r3.filteredImages.length, \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.previewingImage && ctx_r3.isImageSelected(ctx_r3.previewingImage) ? \"\\u53D6\\u6D88\\u9078\\u53D6\" : \"\\u9078\\u53D6\\u6B64\\u5716\\u7247\", \" \");\n  }\n}\nfunction BuildingMaterialComponent_ng_template_66_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r25 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-card\", 140)(1, \"nb-card-header\")(2, \"span\");\n    i0.ɵɵtext(3, \" \\u6AA2\\u8996 \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"nb-card-body\", 141)(5, \"div\", 142);\n    i0.ɵɵelement(6, \"img\", 143);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"nb-card-footer\")(8, \"div\", 144)(9, \"button\", 145);\n    i0.ɵɵlistener(\"click\", function BuildingMaterialComponent_ng_template_66_Template_button_click_9_listener() {\n      const ref_r26 = i0.ɵɵrestoreView(_r25).dialogRef;\n      return i0.ɵɵresetView(ref_r26.close());\n    });\n    i0.ɵɵtext(10, \"\\u53D6\\u6D88\");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"src\", ctx_r3.currentImageShowing, i0.ɵɵsanitizeUrl);\n  }\n}\n// 圖片類別枚舉\nvar PictureCategory;\n(function (PictureCategory) {\n  PictureCategory[PictureCategory[\"NONE\"] = 0] = \"NONE\";\n  PictureCategory[PictureCategory[\"BUILDING_MATERIAL\"] = 1] = \"BUILDING_MATERIAL\";\n  PictureCategory[PictureCategory[\"SCHEMATIC\"] = 2] = \"SCHEMATIC\"; // 示意圖片\n})(PictureCategory || (PictureCategory = {}));\nexport class BuildingMaterialComponent extends BaseComponent {\n  constructor(_allow, dialogService, message, valid, _buildCaseService, _materialService, _utilityService, _pictureService) {\n    super(_allow);\n    this._allow = _allow;\n    this.dialogService = dialogService;\n    this.message = message;\n    this.valid = valid;\n    this._buildCaseService = _buildCaseService;\n    this._materialService = _materialService;\n    this._utilityService = _utilityService;\n    this._pictureService = _pictureService;\n    this.isNew = true;\n    this.listBuildCases = [];\n    this.materialOptions = [{\n      value: null,\n      label: '全部'\n    }, {\n      value: false,\n      label: '方案'\n    }, {\n      value: true,\n      label: '選樣'\n    }];\n    this.materialOptionsId = null;\n    this.CSelectName = \"\";\n    // 移除圖片檔名相關欄位\n    // CImageCode: string = \"\"\n    // CInfoImageCode: string = \"\"\n    // 啟用建材代號欄位\n    this.CImageCode = \"\";\n    this.ShowPrice = false;\n    this.currentImageShowing = \"\";\n    this.filterMapping = false;\n    this.CIsMapping = true;\n    // 圖片綁定相關屬性\n    this.availableImages = [];\n    this.filteredImages = [];\n    this.selectedImages = [];\n    this.imageSearchTerm = \"\";\n    this.previewingImage = null;\n    this.currentPreviewIndex = 0;\n    // 圖片綁定篩選選項\n    this.filterBindingOption = \"all\"; // 預設顯示全部圖片\n    this.filterBindingOptions = [{\n      value: \"all\",\n      label: \"顯示全部\"\n    }, {\n      value: \"bound\",\n      label: \"僅顯示已綁定\"\n    }];\n    // 圖片綁定分頁屬性\n    this.imageCurrentPage = 1;\n    this.imagePageSize = 50;\n    this.imageTotalRecords = 0;\n    // 類別選項\n    this.categoryOptions = [{\n      value: PictureCategory.BUILDING_MATERIAL,\n      label: '建材圖片'\n    }, {\n      value: PictureCategory.SCHEMATIC,\n      label: '示意圖片'\n    }];\n    this.selectedCategory = PictureCategory.BUILDING_MATERIAL;\n    this.isCategorySelected = true; // 預設選擇建材圖片\n    // 讓模板可以使用 enum\n    this.PictureCategory = PictureCategory;\n    // 狀態相關屬性\n    this.EnumStatus = EnumStatus;\n    this.EnumStatusHelper = EnumStatusHelper;\n    this.statusOptions = EnumStatusHelper.getStatusList();\n  }\n  ngOnInit() {\n    this.getListBuildCase();\n  }\n  getListBuildCase() {\n    this._buildCaseService.apiBuildCaseGetAllBuildCaseForSelectPost$Json({\n      body: {\n        CIsPagi: false,\n        CStatus: 1\n      }\n    }).pipe(tap(res => {\n      if (res.StatusCode == 0) {\n        this.listBuildCases = res.Entries?.length ? res.Entries : [];\n        this.selectedBuildCaseId = this.listBuildCases[0].cID;\n      }\n    }), mergeMap(() => this.getMaterialList())).subscribe();\n  }\n  getMaterialList(pageIndex = 1) {\n    return this._materialService.apiMaterialGetMaterialListPost$Json({\n      body: {\n        CBuildCaseId: this.selectedBuildCaseId,\n        CPlanUse: this.materialOptionsId,\n        CSelectName: this.CSelectName,\n        // 啟用建材代號查詢條件\n        CImageCode: this.CImageCode,\n        // CInfoImageCode: this.CInfoImageCode,\n        PageSize: this.pageSize,\n        PageIndex: pageIndex,\n        CIsMapping: this.CIsMapping\n      }\n    }).pipe(tap(res => {\n      if (res.StatusCode == 0) {\n        this.materialList = res.Entries ?? [];\n        this.totalRecords = res.TotalItems;\n        if (this.materialList.length > 0) {\n          this.ShowPrice = this.materialList[0].CShowPrice;\n        }\n      }\n    }));\n  }\n  search() {\n    this.getMaterialList().subscribe();\n  }\n  pageChanged(pageIndex) {\n    this.getMaterialList(pageIndex).subscribe();\n  }\n  exportExelMaterialList() {\n    this._materialService.apiMaterialExportExcelMaterialListPost$Json({\n      body: this.selectedBuildCaseId\n    }).subscribe(res => {\n      if (res.StatusCode == 0) {\n        if (res.Entries.FileByte) {\n          this._utilityService.downloadExcelFile(res.Entries.FileByte, '建材管理');\n        }\n      }\n    });\n  }\n  exportExelMaterialTemplate() {\n    this._materialService.apiMaterialExportExcelMaterialTemplatePost$Json({\n      body: this.selectedBuildCaseId\n    }).subscribe(res => {\n      if (res.StatusCode == 0) {\n        if (res.Entries.FileByte) {\n          this._utilityService.downloadExcelFile(res.Entries.FileByte, '建材管理');\n        }\n      }\n    });\n  }\n  addNew(ref) {\n    this.isNew = true;\n    this.selectedMaterial = {\n      CStatus: EnumStatus.Enable // 預設狀態為啟用\n    };\n    this.dialogService.open(ref);\n  }\n  onSelectedMaterial(data, ref) {\n    this.isNew = false;\n    this.selectedMaterial = {\n      ...data\n    };\n    this.dialogService.open(ref);\n  }\n  bindImageForMaterial(data, ref) {\n    this.selectedMaterial = {\n      ...data\n    };\n    this.loadImages();\n    // 清空當前選擇的圖片，避免舊數據干擾\n    this.clearAllSelection();\n    // 重設篩選選項為顯示全部\n    this.filterBindingOption = \"all\";\n    this.imageSearchTerm = \"\";\n    this.dialogService.open(ref, {\n      closeOnBackdropClick: false\n    });\n  }\n  validation() {\n    this.valid.clear();\n    this.valid.required('[名稱]', this.selectedMaterial.CName);\n    this.valid.required('[項目]', this.selectedMaterial.CPart);\n    this.valid.required('[位置]', this.selectedMaterial.CLocation);\n    this.valid.required('[建材選項名稱]', this.selectedMaterial.CSelectName);\n    // 啟用建材代號驗證\n    this.valid.required('[建材代號]', this.selectedMaterial.CImageCode);\n    this.valid.isStringMaxLength('[名稱]', this.selectedMaterial.CName, 30);\n    this.valid.isStringMaxLength('[項目]', this.selectedMaterial.CPart, 30);\n    this.valid.isStringMaxLength('[位置]', this.selectedMaterial.CLocation, 30);\n    this.valid.isStringMaxLength('[建材選項名稱]', this.selectedMaterial.CSelectName, 30);\n    // 啟用建材代號長度驗證\n    this.valid.isStringMaxLength('[建材代號]', this.selectedMaterial.CImageCode, 30);\n  }\n  onSubmit(ref) {\n    this.validation();\n    if (this.valid.errorMessages.length > 0) {\n      this.message.showErrorMSGs(this.valid.errorMessages);\n      return;\n    }\n    this._materialService.apiMaterialSaveMaterialAdminPost$Json({\n      body: {\n        CBuildCaseId: this.selectedBuildCaseId,\n        // 暫時保留 CImageCode 給圖片綁定功能使用\n        CImageCode: this.selectedMaterial.CImageCode,\n        CName: this.selectedMaterial.CName,\n        CPart: this.selectedMaterial.CPart,\n        CLocation: this.selectedMaterial.CLocation,\n        CSelectName: this.selectedMaterial.CSelectName,\n        CDescription: this.selectedMaterial.CDescription,\n        CMaterialId: this.isNew ? null : this.selectedMaterial.CId,\n        CPrice: this.selectedMaterial.CPrice,\n        CStatus: this.selectedMaterial.CStatus || EnumStatus.Enable,\n        CPictureId: this.selectedMaterial.selectedImageIds || [] // 使用暫存的圖片 ID 陣列，預設為空陣列\n      }\n    }).pipe(tap(res => {\n      if (res.StatusCode === 0) {\n        this.message.showSucessMSG(\"執行成功\");\n      } else {\n        this.message.showErrorMSG(res.Message);\n      }\n    }), mergeMap(() => this.getMaterialList()), finalize(() => ref.close())).subscribe();\n  }\n  onClose(ref) {\n    ref.close();\n  }\n  detectFileExcel(event) {\n    const target = event.target;\n    const reader = new FileReader();\n    reader.readAsBinaryString(target.files[0]);\n    reader.onload = e => {\n      const binarystr = e.target.result;\n      const wb = XLSX.read(binarystr, {\n        type: 'binary'\n      });\n      const wsname = wb.SheetNames[0];\n      const ws = wb.Sheets[wsname];\n      let isValidFile = true;\n      const data = XLSX.utils.sheet_to_json(ws);\n      if (data && data.length > 0) {\n        data.forEach(x => {\n          if (!(x['名稱'] && x['項目'] && x['位置'] && x['建材選項名稱'] && (x['建材圖片檔名'] || x['建材說明'] || x['示意圖片檔名']))) {\n            isValidFile = false;\n          }\n        });\n        if (!isValidFile) {\n          this.message.showErrorMSG(\"导入文件时出现错误\");\n        } else {\n          this._materialService.apiMaterialImportExcelMaterialListPost$Json({\n            body: {\n              CBuildCaseId: this.selectedBuildCaseId,\n              CFile: target.files[0]\n            }\n          }).pipe(tap(res => {\n            if (res.StatusCode == 0) {\n              this.message.showSucessMSG(\"執行成功\");\n            } else {\n              this.message.showErrorMSG(res.Message);\n            }\n          }), mergeMap(() => this.getMaterialList(1))).subscribe();\n        }\n      } else {\n        this.message.showErrorMSG(\"匯入的檔案內容為空，請檢查檔案並重新上傳。\");\n      }\n      event.target.value = null;\n    };\n  }\n  showImage(imageUrl, dialog) {\n    this.currentImageShowing = imageUrl;\n    this.dialogService.open(dialog);\n  }\n  changeFilter() {\n    if (this.filterMapping) {\n      this.CIsMapping = false;\n      this.getMaterialList().subscribe();\n    } else {\n      this.CIsMapping = true;\n      this.getMaterialList().subscribe();\n    }\n  }\n  // 圖片綁定功能方法\n  openImageBinder(ref) {\n    this.loadImages();\n    // 重設篩選選項為顯示全部\n    this.filterBindingOption = \"all\";\n    this.imageSearchTerm = \"\";\n    this.dialogService.open(ref, {\n      closeOnBackdropClick: false\n    });\n  }\n  loadImages() {\n    // 使用 PictureService API 載入圖片列表\n    if (this.isCategorySelected && this.selectedBuildCaseId) {\n      this._pictureService.apiPictureGetPicturelListPost$Json({\n        body: {\n          CBuildCaseId: this.selectedBuildCaseId,\n          cPictureType: this.selectedCategory,\n          PageIndex: this.imageCurrentPage,\n          // 使用圖片當前頁碼\n          PageSize: this.imagePageSize // 使用圖片每頁筆數\n        }\n      }).subscribe(res => {\n        if (res.StatusCode === 0) {\n          // 將 API 回應轉換為 ImageItem 格式\n          this.availableImages = res.Entries?.map(picture => ({\n            id: picture.CId || 0,\n            // 修復類型錯誤，直接使用數字\n            name: picture.CPictureCode || picture.CName || '',\n            size: 0,\n            // API 中沒有檔案大小資訊\n            thumbnailUrl: picture.CBase64 ? `data:image/jpeg;base64,${picture.CBase64}` : '',\n            fullUrl: picture.CBase64 ? `data:image/jpeg;base64,${picture.CBase64}` : '',\n            lastModified: picture.CUpdateDT ? new Date(picture.CUpdateDT) : new Date()\n          })) || [];\n          this.filteredImages = [...this.availableImages];\n          this.imageTotalRecords = res.TotalItems || 0; // 更新圖片總筆數\n          // 如果有已綁定的圖片，自動選中\n          if (this.selectedMaterial && this.selectedMaterial.CSelectPictureId && this.selectedMaterial.CSelectPictureId.length > 0) {\n            // 將已綁定的圖片ID轉換為數字陣列\n            const boundImageIds = this.selectedMaterial.CSelectPictureId.map(id => typeof id === 'string' ? parseInt(id) : id);\n            // 篩選出已綁定的圖片項目\n            const boundImages = this.availableImages.filter(image => boundImageIds.includes(image.id));\n            // 將已綁定的圖片設為選中狀態\n            this.selectedImages = [...boundImages];\n          }\n        } else {\n          this.message.showErrorMSG(res.Message || '載入圖片失敗');\n          this.availableImages = [];\n          this.filteredImages = [];\n        }\n      });\n    } else {\n      // 如果沒有選擇類別或建案，清空圖片列表\n      this.availableImages = [];\n      this.filteredImages = [];\n    }\n  }\n  filterImages() {\n    // 首先根據搜尋詞進行篩選\n    let filtered = [...this.availableImages];\n    if (this.imageSearchTerm.trim()) {\n      const searchTerm = this.imageSearchTerm.toLowerCase();\n      filtered = filtered.filter(image => image.name.toLowerCase().includes(searchTerm));\n    }\n    // 然後根據綁定狀態進行篩選\n    if (this.filterBindingOption === \"bound\" && this.selectedMaterial && this.selectedMaterial.CSelectPictureId) {\n      // 轉換已綁定的圖片ID為數字陣列\n      const boundImageIds = this.selectedMaterial.CSelectPictureId.map(id => typeof id === 'string' ? parseInt(id) : id);\n      // 只顯示已綁定的圖片\n      filtered = filtered.filter(image => boundImageIds.includes(image.id));\n    }\n    this.filteredImages = filtered;\n  }\n  toggleImageSelection(image) {\n    const index = this.selectedImages.findIndex(selected => selected.id === image.id);\n    if (index > -1) {\n      this.selectedImages.splice(index, 1);\n    } else {\n      this.selectedImages.push(image);\n    }\n  }\n  isImageSelected(image) {\n    return this.selectedImages.some(selected => selected.id === image.id);\n  }\n  selectAllImages() {\n    this.selectedImages = [...this.filteredImages];\n  }\n  clearAllSelection() {\n    this.selectedImages = [];\n  }\n  previewImage(image, imagePreviewRef, event) {\n    event.stopPropagation();\n    this.previewingImage = image;\n    this.currentPreviewIndex = this.filteredImages.findIndex(img => img.id === image.id);\n    this.dialogService.open(imagePreviewRef);\n  }\n  previousImage() {\n    if (this.currentPreviewIndex > 0) {\n      this.currentPreviewIndex--;\n      this.previewingImage = this.filteredImages[this.currentPreviewIndex];\n    }\n  }\n  nextImage() {\n    if (this.currentPreviewIndex < this.filteredImages.length - 1) {\n      this.currentPreviewIndex++;\n      this.previewingImage = this.filteredImages[this.currentPreviewIndex];\n    }\n  }\n  toggleImageSelectionInPreview() {\n    if (this.previewingImage) {\n      this.toggleImageSelection(this.previewingImage);\n    }\n  }\n  onConfirmImageSelection(ref) {\n    if (this.selectedImages.length > 0) {\n      // 收集選中圖片的 ID\n      const selectedImageIds = this.selectedImages.map(img => img.id); // 如果只選取一張圖片，直接設定圖片 ID\n      if (this.selectedImages.length === 1) {\n        // 設定第一張圖片的 ID 到 CPictureId（單一數字）\n        this.selectedMaterial.CPictureId = this.selectedImages[0].id;\n      } else {\n        // 多張圖片的話，可以設定第一張，或者讓使用者選擇主要圖片\n        const imageNames = this.selectedImages.map(img => img.name).join(', ');\n        this.message.showSucessMSG(`已選取 ${this.selectedImages.length} 張圖片: ${imageNames}`);\n        // 設定第一張圖片的 ID 到 CPictureId（單一數字）\n        this.selectedMaterial.CPictureId = this.selectedImages[0].id;\n      }\n      // 暫存所有選中的圖片 ID，供 API 呼叫使用\n      this.selectedMaterial.selectedImageIds = selectedImageIds;\n      // 如果是從表格直接綁定圖片（有 CId），則直接保存到資料庫\n      if (this.selectedMaterial.CId) {\n        this.saveImageBinding();\n      }\n    }\n    this.clearAllSelection();\n    ref.close();\n  }\n  // 新增方法：保存圖片綁定\n  saveImageBinding() {\n    this._materialService.apiMaterialSaveMaterialAdminPost$Json({\n      body: {\n        CBuildCaseId: this.selectedBuildCaseId,\n        CImageCode: this.selectedMaterial.CImageCode,\n        CName: this.selectedMaterial.CName,\n        CPart: this.selectedMaterial.CPart,\n        CLocation: this.selectedMaterial.CLocation,\n        CSelectName: this.selectedMaterial.CSelectName,\n        CDescription: this.selectedMaterial.CDescription,\n        CMaterialId: this.selectedMaterial.CId,\n        CPrice: this.selectedMaterial.CPrice,\n        CStatus: this.selectedMaterial.CStatus || EnumStatus.Enable,\n        CPictureId: this.selectedMaterial.selectedImageIds || [] // 選中的圖片 ID 陣列，預設為空陣列\n      }\n    }).pipe(tap(res => {\n      if (res.StatusCode === 0) {\n        this.message.showSucessMSG(`圖片綁定成功`);\n      } else {\n        this.message.showErrorMSG(res.Message);\n      }\n    }), mergeMap(() => this.getMaterialList()), finalize(() => {\n      // 清空選取的建材\n      this.selectedMaterial = {};\n    })).subscribe();\n  }\n  onCloseImageBinder(ref) {\n    this.clearAllSelection();\n    this.imageSearchTerm = \"\";\n    this.filterBindingOption = \"all\"; // 重設篩選選項為顯示全部\n    this.imageCurrentPage = 1; // 重設圖片頁碼\n    ref.close();\n  } // 類別變更處理方法\n  categoryChanged(category) {\n    this.selectedCategory = category;\n    this.isCategorySelected = true;\n    // 當類別變更時重設頁碼並重新載入圖片\n    this.imageCurrentPage = 1;\n    if (this.selectedBuildCaseId) {\n      this.loadImages();\n    }\n  }\n  // 獲取類別標籤的方法\n  getCategoryLabel(category) {\n    const option = this.categoryOptions.find(opt => opt.value === category);\n    return option ? option.label : '未知類別';\n  }\n  // 圖片分頁變更處理方法\n  imagePageChanged(page) {\n    this.imageCurrentPage = page;\n    this.loadImages();\n  }\n  static {\n    this.ɵfac = function BuildingMaterialComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || BuildingMaterialComponent)(i0.ɵɵdirectiveInject(i1.AllowHelper), i0.ɵɵdirectiveInject(i2.NbDialogService), i0.ɵɵdirectiveInject(i3.MessageService), i0.ɵɵdirectiveInject(i4.ValidationHelper), i0.ɵɵdirectiveInject(i5.BuildCaseService), i0.ɵɵdirectiveInject(i5.MaterialService), i0.ɵɵdirectiveInject(i6.UtilityService), i0.ɵɵdirectiveInject(i5.PictureService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: BuildingMaterialComponent,\n      selectors: [[\"ngx-building-material\"]],\n      standalone: true,\n      features: [i0.ɵɵInheritDefinitionFeature, i0.ɵɵStandaloneFeature],\n      decls: 68,\n      vars: 14,\n      consts: [[\"inputFile\", \"\"], [\"dialog\", \"\"], [\"imageBinder\", \"\"], [\"imagePreview\", \"\"], [\"dialogImage\", \"\"], [\"accent\", \"success\"], [1, \"font-bold\", \"text-[#818181]\"], [1, \"d-flex\", \"flex-wrap\"], [1, \"col-md-6\"], [1, \"form-group\", \"d-flex\", \"align-items-center\", \"w-full\"], [\"for\", \"buildingName\", 1, \"label\", \"mr-2\", \"w-[22%]\"], [\"placeholder\", \"\\u5EFA\\u6848\", 1, \"w-full\", 3, \"ngModelChange\", \"ngModel\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"type\", \"text\", \"nbInput\", \"\", \"placeholder\", \"\\u5EFA\\u6750\\u9078\\u9805\\u540D\\u7A31\", \"maxlength\", \"50\", 1, \"w-full\", 3, \"ngModelChange\", \"ngModel\"], [\"type\", \"text\", \"nbInput\", \"\", \"placeholder\", \"\\u5EFA\\u6750\\u4EE3\\u865F\", \"maxlength\", \"20\", 1, \"w-full\", 3, \"ngModelChange\", \"ngModel\"], [1, \"col-md-12\"], [1, \"d-flex\", \"justify-content-end\", \"w-full\"], [\"status\", \"basic\", 1, \"flex\", 2, \"flex\", \"auto\", 3, \"checkedChange\", \"change\", \"checked\"], [\"class\", \"btn btn-success mr-2\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"btn btn-info mr-2 text-white ml-2\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"btn btn-info mx-1 ml-2 mr-2\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"btn btn-info mx-1\", 3, \"click\", 4, \"ngIf\"], [\"type\", \"file\", \"accept\", \".xls, .xlsx\", 1, \"hidden\", 3, \"change\"], [1, \"btn\", \"btn-success\", \"ml-2\", 3, \"click\"], [1, \"fas\", \"fa-file-download\"], [1, \"table-responsive\", \"mt-4\"], [1, \"table\", \"table-striped\", \"border\", 2, \"min-width\", \"1200px\", \"background-color\", \"#f3f3f3\"], [2, \"background-color\", \"#27ae60\", \"color\", \"white\"], [\"scope\", \"col\", 1, \"col-1\"], [\"scope\", \"col\", 1, \"col-2\"], [\"scope\", \"col\", 1, \"col-3\"], [\"scope\", \"col\", \"class\", \"col-1\", 4, \"ngIf\"], [\"scope\", \"col\", 1, \"col-1\", \"text-center\"], [4, \"ngIf\"], [1, \"d-flex\", \"justify-content-center\"], [3, \"CollectionSizeChange\", \"PageSizeChange\", \"PageChange\", \"CollectionSize\", \"PageSize\", \"Page\"], [3, \"value\"], [1, \"btn\", \"btn-success\", \"mr-2\", 3, \"click\"], [1, \"btn\", \"btn-info\", \"mr-2\", \"text-white\", \"ml-2\", 3, \"click\"], [1, \"fas\", \"fa-search\"], [1, \"btn\", \"btn-info\", \"mx-1\", \"ml-2\", \"mr-2\", 3, \"click\"], [1, \"fas\", \"fa-plus\"], [1, \"btn\", \"btn-info\", \"mx-1\", 3, \"click\"], [4, \"ngFor\", \"ngForOf\"], [1, \"d-flex\", \"align-items-center\"], [\"class\", \"badge badge-success mr-2\", 4, \"ngIf\"], [\"class\", \"badge badge-danger mr-2\", 4, \"ngIf\"], [1, \"text-center\", \"w-32\"], [\"class\", \"btn btn-outline-primary btn-sm m-1\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"btn btn-outline-info btn-sm m-1\", 3, \"title\", \"click\", 4, \"ngIf\"], [1, \"badge\", \"badge-success\", \"mr-2\"], [1, \"badge\", \"badge-danger\", \"mr-2\"], [1, \"btn\", \"btn-outline-primary\", \"btn-sm\", \"m-1\", 3, \"click\"], [1, \"btn\", \"btn-outline-info\", \"btn-sm\", \"m-1\", 3, \"click\", \"title\"], [1, \"fas\", \"fa-images\"], [1, \"w-[700px]\"], [1, \"px-4\"], [1, \"text-base\"], [1, \"w-full\", \"mt-3\"], [1, \"flex\", \"items-center\"], [1, \"required-field\", \"w-[150px]\"], [\"type\", \"text\", \"nbInput\", \"\", \"maxlength\", \"20\", 1, \"w-full\", \"!max-w-full\", \"p-2\", \"rounded\", \"text-[13px]\", 3, \"ngModelChange\", \"ngModel\"], [1, \"flex\", \"items-center\", \"mt-3\"], [\"type\", \"text\", \"nbInput\", \"\", \"maxlength\", \"30\", 1, \"w-full\", \"!max-w-full\", \"p-2\", \"rounded\", \"text-[13px]\", 3, \"ngModelChange\", \"ngModel\"], [\"type\", \"text\", \"nbInput\", \"\", \"maxlength\", \"50\", 1, \"w-full\", \"!max-w-full\", \"p-2\", \"rounded\", \"text-[13px]\", 3, \"ngModelChange\", \"ngModel\"], [1, \"w-[150px]\"], [\"nbInput\", \"\", 1, \"resize-none\", \"w-full\", \"!max-w-full\", \"p-2\", \"rounded\", \"text-[13px]\", 3, \"ngModelChange\", \"ngModel\", \"rows\"], [\"type\", \"number\", \"nbInput\", \"\", \"maxlength\", \"30\", 1, \"w-full\", \"!max-w-full\", \"p-2\", \"rounded\", \"text-[13px]\", 3, \"ngModelChange\", \"ngModel\"], [1, \"flex\", \"items-center\", \"mt-4\", \"pt-3\", \"border-t\", \"border-gray-200\"], [1, \"flex\", \"gap-2\", \"w-full\"], [\"type\", \"button\", 1, \"btn\", \"btn-outline-info\", \"btn-sm\", 3, \"click\", \"title\"], [1, \"fas\", \"fa-images\", \"mr-2\"], [\"class\", \"text-sm text-gray-600 flex items-center\", 4, \"ngIf\"], [1, \"btn\", \"btn-danger\", \"btn-sm\", \"mr-4\", 3, \"click\"], [1, \"btn\", \"btn-success\", \"btn-sm\", 3, \"click\"], [1, \"text-sm\", \"text-gray-600\", \"flex\", \"items-center\"], [1, \"fas\", \"fa-check-circle\", \"text-green-500\", \"mr-2\"], [1, \"w-[80vw]\", \"max-w-[1000px]\", \"h-[85vh]\"], [1, \"px-4\", \"d-flex\", \"flex-column\", 2, \"height\", \"calc(100% - 120px)\", \"overflow\", \"hidden\", \"padding-bottom\", \"0\"], [1, \"bg-blue-50\", \"border\", \"border-blue-200\", \"rounded-lg\", \"p-3\", \"mb-4\", \"flex-shrink-0\"], [1, \"flex\", \"items-start\", \"gap-2\"], [1, \"fas\", \"fa-info-circle\", \"text-blue-500\", \"mt-1\"], [1, \"text-sm\", \"text-blue-700\"], [1, \"font-medium\", \"mb-1\"], [1, \"mb-2\"], [1, \"font-medium\"], [1, \"bg-white\", \"px-2\", \"py-1\", \"rounded\", \"border\"], [1, \"flex\", \"gap-3\", \"mb-4\", \"flex-shrink-0\"], [1, \"w-48\"], [1, \"text-sm\", \"font-medium\", \"text-gray-700\", \"mb-2\", \"block\"], [1, \"w-full\", 3, \"ngModelChange\", \"selectedChange\", \"ngModel\"], [1, \"flex-1\"], [\"type\", \"text\", \"placeholder\", \"\\u641C\\u5C0B\\u5716\\u7247\\u540D\\u7A31...\", 1, \"w-full\", \"search-input\", 3, \"ngModelChange\", \"input\", \"ngModel\"], [1, \"flex\", \"flex-col\", \"justify-end\"], [1, \"btn\", \"btn-info\", \"btn-image-action\", 3, \"click\"], [1, \"fas\", \"fa-refresh\"], [1, \"flex\", \"gap-2\", \"mb-3\", \"flex-shrink-0\"], [1, \"btn\", \"btn-outline-primary\", \"btn-sm\", 3, \"click\"], [1, \"fas\", \"fa-check-square\"], [1, \"btn\", \"btn-outline-secondary\", \"btn-sm\", 3, \"click\"], [1, \"ml-auto\", \"text-sm\", \"text-gray-600\"], [1, \"image-preview-container\", \"border\", \"rounded\", \"p-3\", \"flex-1\"], [1, \"grid\", \"grid-cols-4\", \"gap-3\"], [\"class\", \"image-grid-item border rounded p-2 cursor-pointer\", 3, \"selected\", \"click\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"text-center text-gray-500 py-20\", 4, \"ngIf\"], [1, \"d-flex\", \"justify-content-between\", \"align-items-center\"], [1, \"text-sm\", \"text-gray-600\"], [1, \"d-flex\", \"gap-2\"], [1, \"btn\", \"btn-danger\", \"btn-sm\", 3, \"click\"], [1, \"btn\", \"btn-success\", \"btn-sm\", 3, \"click\", \"disabled\"], [1, \"image-grid-item\", \"border\", \"rounded\", \"p-2\", \"cursor-pointer\", 3, \"click\"], [1, \"flex\", \"justify-between\", \"items-center\", \"mb-2\"], [1, \"image-checkbox\", \"w-5\", \"h-5\", \"border-2\", \"rounded\", \"flex\", \"items-center\", \"justify-center\"], [\"class\", \"fas fa-check text-white text-xs\", 4, \"ngIf\"], [\"class\", \"badge badge-success text-xs px-2 py-1\", \"title\", \"\\u6B64\\u5716\\u7247\\u5DF2\\u7D93\\u7D81\\u5B9A\\u5230\\u6B64\\u5EFA\\u6750\", 4, \"ngIf\"], [1, \"btn\", \"btn-outline-info\", \"btn-xs\", \"btn-image-action\", 3, \"click\"], [1, \"fas\", \"fa-eye\"], [1, \"w-full\", \"h-32\", \"bg-gray-100\", \"rounded\", \"mb-2\", \"flex\", \"items-center\", \"justify-center\", \"overflow-hidden\"], [\"class\", \"image-thumbnail max-w-full max-h-full object-contain\", 3, \"src\", \"alt\", 4, \"ngIf\"], [\"class\", \"text-gray-400 text-center\", 4, \"ngIf\"], [1, \"text-xs\", \"text-gray-600\"], [1, \"font-medium\", \"truncate\", 3, \"title\"], [1, \"text-gray-400\"], [1, \"fas\", \"fa-check\", \"text-white\", \"text-xs\"], [\"title\", \"\\u6B64\\u5716\\u7247\\u5DF2\\u7D93\\u7D81\\u5B9A\\u5230\\u6B64\\u5EFA\\u6750\", 1, \"badge\", \"badge-success\", \"text-xs\", \"px-2\", \"py-1\"], [1, \"image-thumbnail\", \"max-w-full\", \"max-h-full\", \"object-contain\", 3, \"src\", \"alt\"], [1, \"text-gray-400\", \"text-center\"], [1, \"fas\", \"fa-image\", \"text-2xl\", \"mb-1\"], [1, \"text-xs\"], [1, \"text-center\", \"text-gray-500\", \"py-20\"], [1, \"fas\", \"fa-images\", \"text-4xl\", \"mb-3\"], [1, \"w-[800px]\", \"h-[600px]\"], [1, \"btn\", \"btn-outline-primary\", \"btn-sm\", 3, \"click\", \"disabled\"], [1, \"fas\", \"fa-chevron-left\"], [1, \"fas\", \"fa-chevron-right\"], [1, \"p-0\", \"d-flex\", \"justify-content-center\", \"align-items-center\", 2, \"height\", \"500px\"], [\"class\", \"max-w-full max-h-full object-contain\", 3, \"src\", \"alt\", 4, \"ngIf\"], [1, \"btn\", \"btn-outline-info\", \"btn-sm\", 3, \"click\"], [1, \"max-w-full\", \"max-h-full\", \"object-contain\", 3, \"src\", \"alt\"], [1, \"fas\", \"fa-image\", \"text-4xl\", \"mb-3\"], [1, \"mr-md-5\", \"ml-md-5\", 2, \"height\", \"100%\", \"overflow\", \"auto\", \"width\", \"700px\"], [2, \"padding\", \"1rem 2rem\"], [1, \"w-full\", \"h-auto\"], [1, \"fit-size\", 3, \"src\"], [1, \"flex\", \"justify-center\", \"items-center\"], [1, \"btn\", \"btn-danger\", \"mr-2\", 3, \"click\"]],\n      template: function BuildingMaterialComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"nb-card\", 5)(1, \"nb-card-header\");\n          i0.ɵɵelement(2, \"ngx-breadcrumb\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(3, \"nb-card-body\")(4, \"h1\", 6);\n          i0.ɵɵtext(5, \" \\u53EF\\u8A2D\\u5B9A\\u55AE\\u7B46\\u6216\\u6279\\u6B21\\u532F\\u5165\\u8A2D\\u5B9A\\u5404\\u5340\\u57DF\\u53CA\\u65B9\\u6848\\u5C0D\\u61C9\\u4E4B\\u5EFA\\u6750\\u3002 \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(6, \"div\", 7)(7, \"div\", 8)(8, \"div\", 9)(9, \"label\", 10);\n          i0.ɵɵtext(10, \"\\u5EFA\\u6848\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(11, \"nb-select\", 11);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function BuildingMaterialComponent_Template_nb_select_ngModelChange_11_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.selectedBuildCaseId, $event) || (ctx.selectedBuildCaseId = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵlistener(\"ngModelChange\", function BuildingMaterialComponent_Template_nb_select_ngModelChange_11_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.search());\n          });\n          i0.ɵɵtemplate(12, BuildingMaterialComponent_nb_option_12_Template, 2, 2, \"nb-option\", 12);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(13, \"div\", 8)(14, \"div\", 9)(15, \"label\", 10);\n          i0.ɵɵtext(16, \"\\u5EFA\\u6750\\u9078\\u9805\\u540D\\u7A31 \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(17, \"input\", 13);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function BuildingMaterialComponent_Template_input_ngModelChange_17_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.CSelectName, $event) || (ctx.CSelectName = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(18, \"div\", 8)(19, \"div\", 9)(20, \"label\", 10);\n          i0.ɵɵtext(21, \"\\u5EFA\\u6750\\u4EE3\\u865F\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(22, \"input\", 14);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function BuildingMaterialComponent_Template_input_ngModelChange_22_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.CImageCode, $event) || (ctx.CImageCode = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(23, \"div\", 15)(24, \"div\", 16)(25, \"nb-checkbox\", 17);\n          i0.ɵɵtwoWayListener(\"checkedChange\", function BuildingMaterialComponent_Template_nb_checkbox_checkedChange_25_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.filterMapping, $event) || (ctx.filterMapping = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵlistener(\"change\", function BuildingMaterialComponent_Template_nb_checkbox_change_25_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.changeFilter());\n          });\n          i0.ɵɵtext(26, \" \\u53EA\\u986F\\u793A\\u7F3A\\u5C11\\u5EFA\\u6750\\u5716\\u7247\\u6216\\u793A\\u610F\\u5716\\u7247\\u7684\\u5EFA\\u6750 \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(27, BuildingMaterialComponent_button_27_Template, 3, 0, \"button\", 18)(28, BuildingMaterialComponent_button_28_Template, 3, 0, \"button\", 19)(29, BuildingMaterialComponent_button_29_Template, 3, 0, \"button\", 20)(30, BuildingMaterialComponent_button_30_Template, 2, 0, \"button\", 21);\n          i0.ɵɵelementStart(31, \"input\", 22, 0);\n          i0.ɵɵlistener(\"change\", function BuildingMaterialComponent_Template_input_change_31_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.detectFileExcel($event));\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(33, \"button\", 23);\n          i0.ɵɵlistener(\"click\", function BuildingMaterialComponent_Template_button_click_33_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.exportExelMaterialTemplate());\n          });\n          i0.ɵɵtext(34, \"\\u4E0B\\u8F09\\u7BC4\\u4F8B\\u6A94\\u6848 \");\n          i0.ɵɵelement(35, \"i\", 24);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(36, \"div\", 25)(37, \"table\", 26)(38, \"thead\")(39, \"tr\", 27)(40, \"th\", 28);\n          i0.ɵɵtext(41, \"\\u9805\\u6B21\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(42, \"th\", 28);\n          i0.ɵɵtext(43, \"\\u5EFA\\u6750\\u4EE3\\u865F\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(44, \"th\", 29);\n          i0.ɵɵtext(45, \"\\u9078\\u9805\\u984C\\u76EE\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(46, \"th\", 28);\n          i0.ɵɵtext(47, \"\\u9078\\u9805\\u540D\\u7A31\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(48, \"th\", 30);\n          i0.ɵɵtext(49, \"\\u5EFA\\u6750\\u8AAA\\u660E\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(50, \"th\", 28);\n          i0.ɵɵtext(51, \"\\u5DF2\\u7D81\\u5B9A\\u5716\\u7247\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(52, \"th\", 28);\n          i0.ɵɵtext(53, \"\\u72C0\\u614B\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(54, BuildingMaterialComponent_th_54_Template, 2, 0, \"th\", 31);\n          i0.ɵɵelementStart(55, \"th\", 32);\n          i0.ɵɵtext(56, \"\\u64CD\\u4F5C\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(57, BuildingMaterialComponent_tbody_57_Template, 2, 1, \"tbody\", 33);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(58, \"nb-card-footer\", 34)(59, \"ngx-pagination\", 35);\n          i0.ɵɵtwoWayListener(\"CollectionSizeChange\", function BuildingMaterialComponent_Template_ngx_pagination_CollectionSizeChange_59_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.totalRecords, $event) || (ctx.totalRecords = $event);\n            return i0.ɵɵresetView($event);\n          })(\"PageSizeChange\", function BuildingMaterialComponent_Template_ngx_pagination_PageSizeChange_59_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.pageSize, $event) || (ctx.pageSize = $event);\n            return i0.ɵɵresetView($event);\n          })(\"PageChange\", function BuildingMaterialComponent_Template_ngx_pagination_PageChange_59_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.pageIndex, $event) || (ctx.pageIndex = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵlistener(\"PageChange\", function BuildingMaterialComponent_Template_ngx_pagination_PageChange_59_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.pageChanged($event));\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(60, BuildingMaterialComponent_ng_template_60_Template, 48, 10, \"ng-template\", null, 1, i0.ɵɵtemplateRefExtractor)(62, BuildingMaterialComponent_ng_template_62_Template, 57, 16, \"ng-template\", null, 2, i0.ɵɵtemplateRefExtractor)(64, BuildingMaterialComponent_ng_template_64_Template, 22, 8, \"ng-template\", null, 3, i0.ɵɵtemplateRefExtractor)(66, BuildingMaterialComponent_ng_template_66_Template, 11, 1, \"ng-template\", null, 4, i0.ɵɵtemplateRefExtractor);\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(11);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.selectedBuildCaseId);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngForOf\", ctx.listBuildCases);\n          i0.ɵɵadvance(5);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.CSelectName);\n          i0.ɵɵadvance(5);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.CImageCode);\n          i0.ɵɵadvance(3);\n          i0.ɵɵtwoWayProperty(\"checked\", ctx.filterMapping);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.isExcelExport);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isRead);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isCreate);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isExcelImport);\n          i0.ɵɵadvance(24);\n          i0.ɵɵproperty(\"ngIf\", ctx.ShowPrice == true);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", ctx.materialList != null && ctx.materialList.length > 0);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtwoWayProperty(\"CollectionSize\", ctx.totalRecords)(\"PageSize\", ctx.pageSize)(\"Page\", ctx.pageIndex);\n        }\n      },\n      dependencies: [CommonModule, i7.NgForOf, i7.NgIf, i7.DecimalPipe, SharedModule, i8.DefaultValueAccessor, i8.NumberValueAccessor, i8.NgControlStatus, i8.MaxLengthValidator, i8.NgModel, i2.NbCardComponent, i2.NbCardBodyComponent, i2.NbCardFooterComponent, i2.NbCardHeaderComponent, i2.NbCheckboxComponent, i2.NbInputDirective, i2.NbSelectComponent, i2.NbOptionComponent, i9.BreadcrumbComponent, i10.PaginationComponent],\n      styles: [\"@charset \\\"UTF-8\\\";\\n.image-table[_ngcontent-%COMP%] {\\n  width: 50px;\\n  height: 50px;\\n  cursor: pointer;\\n}\\n\\n.empty-image[_ngcontent-%COMP%] {\\n  color: red;\\n}\\n\\n.fit-size[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: auto;\\n  max-width: 100%;\\n  max-height: 500px;\\n  object-fit: contain;\\n}\\n\\n.image-grid-item[_ngcontent-%COMP%] {\\n  transition: all 0.3s ease;\\n}\\n.image-grid-item[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-2px);\\n  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);\\n}\\n.image-grid-item.selected[_ngcontent-%COMP%] {\\n  border-color: #3366ff;\\n  background-color: #f0f7ff;\\n  box-shadow: 0 0 0 2px rgba(51, 102, 255, 0.2);\\n}\\n\\n.image-checkbox[_ngcontent-%COMP%] {\\n  border-color: #ccc;\\n  background-color: white;\\n  transition: all 0.2s ease;\\n}\\n.image-checkbox.checked[_ngcontent-%COMP%] {\\n  background-color: #3366ff;\\n  border-color: #3366ff;\\n}\\n\\n.image-thumbnail[_ngcontent-%COMP%] {\\n  transition: transform 0.2s ease;\\n}\\n.image-thumbnail[_ngcontent-%COMP%]:hover {\\n  transform: scale(1.05);\\n}\\n\\n.image-preview-container[_ngcontent-%COMP%] {\\n  max-height: 480px;\\n  overflow-y: auto !important;\\n  overflow-x: hidden;\\n  \\n\\n  \\n\\n  -webkit-overflow-scrolling: touch;\\n  scrollbar-width: thin;\\n  scrollbar-color: #c1c1c1 #f1f1f1;\\n}\\n.image-preview-container[_ngcontent-%COMP%]::-webkit-scrollbar {\\n  width: 8px;\\n}\\n.image-preview-container[_ngcontent-%COMP%]::-webkit-scrollbar-track {\\n  background: #f1f1f1;\\n  border-radius: 4px;\\n}\\n.image-preview-container[_ngcontent-%COMP%]::-webkit-scrollbar-thumb {\\n  background: #c1c1c1;\\n  border-radius: 4px;\\n}\\n.image-preview-container[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover {\\n  background: #a8a8a8;\\n}\\n\\n.search-input[_ngcontent-%COMP%] {\\n  padding: 8px 12px;\\n  border: 1px solid #ddd;\\n  border-radius: 4px;\\n  font-size: 14px;\\n  transition: border-color 0.2s ease-in-out;\\n}\\n.search-input[_ngcontent-%COMP%]:focus {\\n  outline: none;\\n  border-color: #3366ff;\\n  box-shadow: 0 0 0 2px rgba(51, 102, 255, 0.1);\\n}\\n\\n.btn-image-action[_ngcontent-%COMP%] {\\n  padding: 4px 8px;\\n  font-size: 12px;\\n  border-radius: 4px;\\n  transition: all 0.2s ease-in-out;\\n}\\n.btn-image-action[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-1px);\\n}\\n.btn-image-action.btn-xs[_ngcontent-%COMP%] {\\n  padding: 2px 6px;\\n  font-size: 10px;\\n}\\n\\n\\n\\n.d-flex.flex-column[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n}\\n\\n.flex-1[_ngcontent-%COMP%] {\\n  flex: 1;\\n  min-height: 0;\\n}\\n\\n\\n\\nnb-card.w-\\\\__ph-0__[_ngcontent-%COMP%]   .nb-card-body[_ngcontent-%COMP%] {\\n  height: 580px !important;\\n  overflow: hidden !important;\\n  display: flex !important;\\n  flex-direction: column !important;\\n}\\n\\n\\n\\n.flex-shrink-0[_ngcontent-%COMP%] {\\n  flex-shrink: 0;\\n}\\n\\n\\n\\n.image-preview-container.flex-1[_ngcontent-%COMP%] {\\n  flex: 1 1 auto;\\n  min-height: 0;\\n  height: auto;\\n  overflow-y: scroll !important;\\n  overflow-x: hidden !important;\\n}\\n\\n\\n\\n.grid.grid-cols-4[_ngcontent-%COMP%] {\\n  min-height: min-content;\\n}\\n\\n\\n\\n  nb-card-body .image-preview-container {\\n  max-height: none !important;\\n  height: 100% !important;\\n}\\n/*# sourceMappingURL=data:application/json;base64,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 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvcGFnZXMvc2VsZWN0aW9uLW1hbmFnZW1lbnQvYnVpbGRpbmctbWF0ZXJpYWwvYnVpbGRpbmctbWF0ZXJpYWwuY29tcG9uZW50LnNjc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUEsZ0JBQWdCO0FBQWhCO0VBQ0UsV0FBQTtFQUNBLFlBQUE7RUFDQSxlQUFBO0FBRUY7O0FBQ0E7RUFDRSxVQUFBO0FBRUY7O0FBQ0E7RUFDRSxXQUFBO0VBQ0EsWUFBQTtFQUNBLGVBQUE7RUFDQSxpQkFBQTtFQUNBLG1CQUFBO0FBRUY7O0FBRUE7RUFDRSx5QkFBQTtBQUNGO0FBQ0U7RUFDRSwyQkFBQTtFQUNBLHdDQUFBO0FBQ0o7QUFFRTtFQUNFLHFCQUFBO0VBQ0EseUJBQUE7RUFDQSw2Q0FBQTtBQUFKOztBQUlBO0VBQ0Usa0JBQUE7RUFDQSx1QkFBQTtFQUNBLHlCQUFBO0FBREY7QUFHRTtFQUNFLHlCQUFBO0VBQ0EscUJBQUE7QUFESjs7QUFLQTtFQUNFLCtCQUFBO0FBRkY7QUFJRTtFQUNFLHNCQUFBO0FBRko7O0FBTUE7RUFDRSxpQkFBQTtFQUNBLDJCQUFBO0VBQ0Esa0JBQUE7RUFFQSxZQUFBO0VBbUJBLGtCQUFBO0VBQ0EsaUNBQUE7RUFDQSxxQkFBQTtFQUNBLGdDQUFBO0FBdEJGO0FBQ0U7RUFDRSxVQUFBO0FBQ0o7QUFFRTtFQUNFLG1CQUFBO0VBQ0Esa0JBQUE7QUFBSjtBQUdFO0VBQ0UsbUJBQUE7RUFDQSxrQkFBQTtBQURKO0FBR0k7RUFDRSxtQkFBQTtBQUROOztBQVdBO0VBQ0UsaUJBQUE7RUFDQSxzQkFBQTtFQUNBLGtCQUFBO0VBQ0EsZUFBQTtFQUNBLHlDQUFBO0FBUkY7QUFVRTtFQUNFLGFBQUE7RUFDQSxxQkFBQTtFQUNBLDZDQUFBO0FBUko7O0FBWUE7RUFDRSxnQkFBQTtFQUNBLGVBQUE7RUFDQSxrQkFBQTtFQUNBLGdDQUFBO0FBVEY7QUFXRTtFQUNFLDJCQUFBO0FBVEo7QUFZRTtFQUNFLGdCQUFBO0VBQ0EsZUFBQTtBQVZKOztBQWNBLG9CQUFBO0FBQ0E7RUFDRSxhQUFBO0VBQ0Esc0JBQUE7QUFYRjs7QUFjQTtFQUNFLE9BQUE7RUFDQSxhQUFBO0FBWEY7O0FBY0EsZ0JBQUE7QUFFRTtFQUNFLHdCQUFBO0VBQ0EsMkJBQUE7RUFDQSx3QkFBQTtFQUNBLGlDQUFBO0FBWko7O0FBZ0JBLGlCQUFBO0FBQ0E7RUFDRSxjQUFBO0FBYkY7O0FBZ0JBLGFBQUE7QUFDQTtFQUNFLGNBQUE7RUFDQSxhQUFBO0VBQ0EsWUFBQTtFQUNBLDZCQUFBO0VBQ0EsNkJBQUE7QUFiRjs7QUFnQkEscUJBQUE7QUFDQTtFQUNFLHVCQUFBO0FBYkY7O0FBZ0JBLDBCQUFBO0FBRUU7RUFDRSwyQkFBQTtFQUNBLHVCQUFBO0FBZEo7QUFDQSxnNExBQWc0TCIsInNvdXJjZXNDb250ZW50IjpbIi5pbWFnZS10YWJsZSB7XHJcbiAgd2lkdGg6IDUwcHg7XHJcbiAgaGVpZ2h0OiA1MHB4O1xyXG4gIGN1cnNvcjogcG9pbnRlcjtcclxufVxyXG5cclxuLmVtcHR5LWltYWdlIHtcclxuICBjb2xvcjogcmVkO1xyXG59XHJcblxyXG4uZml0LXNpemUge1xyXG4gIHdpZHRoOiAxMDAlO1xyXG4gIGhlaWdodDogYXV0bztcclxuICBtYXgtd2lkdGg6IDEwMCU7XHJcbiAgbWF4LWhlaWdodDogNTAwcHg7XHJcbiAgb2JqZWN0LWZpdDogY29udGFpbjtcclxufVxyXG5cclxuLy8gw6XCnMKWw6fCicKHw6fCtsKBw6XCrsKaw6XCisKfw6jCg8K9w6bCqMKjw6XCvMKPXHJcbi5pbWFnZS1ncmlkLWl0ZW0ge1xyXG4gIHRyYW5zaXRpb246IGFsbCAwLjNzIGVhc2U7XHJcblxyXG4gICY6aG92ZXIge1xyXG4gICAgdHJhbnNmb3JtOiB0cmFuc2xhdGVZKC0ycHgpO1xyXG4gICAgYm94LXNoYWRvdzogMCA0cHggOHB4IHJnYmEoMCwgMCwgMCwgMC4xKTtcclxuICB9XHJcblxyXG4gICYuc2VsZWN0ZWQge1xyXG4gICAgYm9yZGVyLWNvbG9yOiAjMzM2NmZmO1xyXG4gICAgYmFja2dyb3VuZC1jb2xvcjogI2YwZjdmZjtcclxuICAgIGJveC1zaGFkb3c6IDAgMCAwIDJweCByZ2JhKDUxLCAxMDIsIDI1NSwgMC4yKTtcclxuICB9XHJcbn1cclxuXHJcbi5pbWFnZS1jaGVja2JveCB7XHJcbiAgYm9yZGVyLWNvbG9yOiAjY2NjO1xyXG4gIGJhY2tncm91bmQtY29sb3I6IHdoaXRlO1xyXG4gIHRyYW5zaXRpb246IGFsbCAwLjJzIGVhc2U7XHJcblxyXG4gICYuY2hlY2tlZCB7XHJcbiAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjMzM2NmZmO1xyXG4gICAgYm9yZGVyLWNvbG9yOiAjMzM2NmZmO1xyXG4gIH1cclxufVxyXG5cclxuLmltYWdlLXRodW1ibmFpbCB7XHJcbiAgdHJhbnNpdGlvbjogdHJhbnNmb3JtIDAuMnMgZWFzZTtcclxuXHJcbiAgJjpob3ZlciB7XHJcbiAgICB0cmFuc2Zvcm06IHNjYWxlKDEuMDUpO1xyXG4gIH1cclxufVxyXG5cclxuLmltYWdlLXByZXZpZXctY29udGFpbmVyIHtcclxuICBtYXgtaGVpZ2h0OiA0ODBweDtcclxuICBvdmVyZmxvdy15OiBhdXRvICFpbXBvcnRhbnQ7XHJcbiAgb3ZlcmZsb3cteDogaGlkZGVuO1xyXG5cclxuICAvKiDDqMKHwqrDpcKuwprDp8K+wqnDpsK7wpHDqMK7wozDpsKowqPDpcK8wo8gKi9cclxuICAmOjotd2Via2l0LXNjcm9sbGJhciB7XHJcbiAgICB3aWR0aDogOHB4O1xyXG4gIH1cclxuXHJcbiAgJjo6LXdlYmtpdC1zY3JvbGxiYXItdHJhY2sge1xyXG4gICAgYmFja2dyb3VuZDogI2YxZjFmMTtcclxuICAgIGJvcmRlci1yYWRpdXM6IDRweDtcclxuICB9XHJcblxyXG4gICY6Oi13ZWJraXQtc2Nyb2xsYmFyLXRodW1iIHtcclxuICAgIGJhY2tncm91bmQ6ICNjMWMxYzE7XHJcbiAgICBib3JkZXItcmFkaXVzOiA0cHg7XHJcblxyXG4gICAgJjpob3ZlciB7XHJcbiAgICAgIGJhY2tncm91bmQ6ICNhOGE4YTg7XHJcbiAgICB9XHJcbiAgfVxyXG5cclxuICAvKiDDp8KiwrrDpMK/wp3DpcKcwqjDpcKQwoTDp8Kowq7Dp8KAwo/DqMKmwr3DpcKZwqjDpMK4wq3DqcKDwr3DqMKDwr3DpsK7wpHDpcKLwpUgKi9cclxuICAtd2Via2l0LW92ZXJmbG93LXNjcm9sbGluZzogdG91Y2g7XHJcbiAgc2Nyb2xsYmFyLXdpZHRoOiB0aGluO1xyXG4gIHNjcm9sbGJhci1jb2xvcjogI2MxYzFjMSAjZjFmMWYxO1xyXG59XHJcblxyXG4uc2VhcmNoLWlucHV0IHtcclxuICBwYWRkaW5nOiA4cHggMTJweDtcclxuICBib3JkZXI6IDFweCBzb2xpZCAjZGRkO1xyXG4gIGJvcmRlci1yYWRpdXM6IDRweDtcclxuICBmb250LXNpemU6IDE0cHg7XHJcbiAgdHJhbnNpdGlvbjogYm9yZGVyLWNvbG9yIDAuMnMgZWFzZS1pbi1vdXQ7XHJcblxyXG4gICY6Zm9jdXMge1xyXG4gICAgb3V0bGluZTogbm9uZTtcclxuICAgIGJvcmRlci1jb2xvcjogIzMzNjZmZjtcclxuICAgIGJveC1zaGFkb3c6IDAgMCAwIDJweCByZ2JhKDUxLCAxMDIsIDI1NSwgMC4xKTtcclxuICB9XHJcbn1cclxuXHJcbi5idG4taW1hZ2UtYWN0aW9uIHtcclxuICBwYWRkaW5nOiA0cHggOHB4O1xyXG4gIGZvbnQtc2l6ZTogMTJweDtcclxuICBib3JkZXItcmFkaXVzOiA0cHg7XHJcbiAgdHJhbnNpdGlvbjogYWxsIDAuMnMgZWFzZS1pbi1vdXQ7XHJcblxyXG4gICY6aG92ZXIge1xyXG4gICAgdHJhbnNmb3JtOiB0cmFuc2xhdGVZKC0xcHgpO1xyXG4gIH1cclxuXHJcbiAgJi5idG4teHMge1xyXG4gICAgcGFkZGluZzogMnB4IDZweDtcclxuICAgIGZvbnQtc2l6ZTogMTBweDtcclxuICB9XHJcbn1cclxuXHJcbi8qIMOnwqLCusOkwr/CnSBGbGV4Ym94IMOmwq3Co8OnwqLCusOpwoHCi8Okwr3CnCAqL1xyXG4uZC1mbGV4LmZsZXgtY29sdW1uIHtcclxuICBkaXNwbGF5OiBmbGV4O1xyXG4gIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47XHJcbn1cclxuXHJcbi5mbGV4LTEge1xyXG4gIGZsZXg6IDE7XHJcbiAgbWluLWhlaWdodDogMDtcclxufVxyXG5cclxuLyogw6XCnMKWw6fCicKHw6fCtsKBw6XCrsKaw6XCsMKNw6jCqcKxw6bCocKGw6fCicK5w6XCrsKaw6bCqMKjw6XCvMKPICovXHJcbm5iLWNhcmQudy1cXFs5MDBweFxcXSB7XHJcbiAgLm5iLWNhcmQtYm9keSB7XHJcbiAgICBoZWlnaHQ6IGNhbGMoNzAwcHggLSAxMjBweCkgIWltcG9ydGFudDtcclxuICAgIG92ZXJmbG93OiBoaWRkZW4gIWltcG9ydGFudDtcclxuICAgIGRpc3BsYXk6IGZsZXggIWltcG9ydGFudDtcclxuICAgIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW4gIWltcG9ydGFudDtcclxuICB9XHJcbn1cclxuXHJcbi8qIMOnwqLCusOkwr/CncOmwrvCkcOlwovClcOlwq7CucOlwpnCqMOlwo/Cr8OkwrvCpcOmwq3Co8OlwrjCuMOpwoHCi8Okwr3CnCAqL1xyXG4uZmxleC1zaHJpbmstMCB7XHJcbiAgZmxleC1zaHJpbms6IDA7XHJcbn1cclxuXHJcbi8qIMOlwrzCt8OlwojCtsOmwrvCkcOlwovClcOlwq7CucOlwpnCqMOmwqjCo8OlwrzCjyAqL1xyXG4uaW1hZ2UtcHJldmlldy1jb250YWluZXIuZmxleC0xIHtcclxuICBmbGV4OiAxIDEgYXV0bztcclxuICBtaW4taGVpZ2h0OiAwO1xyXG4gIGhlaWdodDogYXV0bztcclxuICBvdmVyZmxvdy15OiBzY3JvbGwgIWltcG9ydGFudDtcclxuICBvdmVyZmxvdy14OiBoaWRkZW4gIWltcG9ydGFudDtcclxufVxyXG5cclxuLyogR3JpZCDDpcKuwrnDpcKZwqjDp8KiwrrDpMK/wp3DpcKFwqfDpcKuwrnDqMKDwr3DpcKkwqDDqMKiwqvDpsK7wpHDpcKLwpUgKi9cclxuLmdyaWQuZ3JpZC1jb2xzLTQge1xyXG4gIG1pbi1oZWlnaHQ6IG1pbi1jb250ZW50O1xyXG59XHJcblxyXG4vKiDDp8KiwrrDpMK/wp3DpcKcwqggQW5ndWxhciDDpsKdwpDDqMKzwqrDqMKowq3DqMKowojDpMK4wq3DpsKtwqPDpcK4wrjDqcKBwovDpMK9wpwgKi9cclxuOjpuZy1kZWVwIG5iLWNhcmQtYm9keSB7XHJcbiAgLmltYWdlLXByZXZpZXctY29udGFpbmVyIHtcclxuICAgIG1heC1oZWlnaHQ6IG5vbmUgIWltcG9ydGFudDtcclxuICAgIGhlaWdodDogMTAwJSAhaW1wb3J0YW50O1xyXG4gIH1cclxufVxyXG4iXSwic291cmNlUm9vdCI6IiJ9 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "finalize", "mergeMap", "tap", "XLSX", "SharedModule", "BaseComponent", "EnumStatus", "EnumStatusHelper", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵproperty", "buildCase_r2", "cID", "ɵɵadvance", "ɵɵtextInterpolate1", "CBuildCaseName", "ɵɵlistener", "BuildingMaterialComponent_button_27_Template_button_click_0_listener", "ɵɵrestoreView", "_r3", "ctx_r3", "ɵɵnextContext", "ɵɵresetView", "exportExelMaterialList", "ɵɵelement", "BuildingMaterialComponent_button_28_Template_button_click_0_listener", "_r5", "search", "BuildingMaterialComponent_button_29_Template_button_click_0_listener", "_r6", "dialog_r7", "ɵɵreference", "addNew", "BuildingMaterialComponent_button_30_Template_button_click_0_listener", "_r8", "inputFile_r9", "click", "ɵɵtextInterpolate", "item_r10", "CSelectPictureId", "length", "CPrice", "BuildingMaterialComponent_tbody_57_tr_1_button_19_Template_button_click_0_listener", "_r11", "$implicit", "onSelectedMaterial", "BuildingMaterialComponent_tbody_57_tr_1_button_20_Template_button_click_0_listener", "_r12", "imageBinder_r13", "bindImageForMaterial", "CName", "ɵɵtemplate", "BuildingMaterialComponent_tbody_57_tr_1_span_13_Template", "BuildingMaterialComponent_tbody_57_tr_1_span_14_Template", "BuildingMaterialComponent_tbody_57_tr_1_td_17_Template", "BuildingMaterialComponent_tbody_57_tr_1_button_19_Template", "BuildingMaterialComponent_tbody_57_tr_1_button_20_Template", "CId", "CImageCode", "ɵɵtextInterpolate3", "<PERSON>art", "CLocation", "ɵɵstyleMap", "CIsMapping", "CSelectName", "CDescription", "CShowPrice", "isRead", "BuildingMaterialComponent_tbody_57_tr_1_Template", "materialList", "selectedMaterial", "ɵɵtwoWayListener", "BuildingMaterialComponent_ng_template_60_Template_input_ngModelChange_10_listener", "$event", "_r14", "ɵɵtwoWayBindingSet", "BuildingMaterialComponent_ng_template_60_Template_input_ngModelChange_14_listener", "BuildingMaterialComponent_ng_template_60_Template_input_ngModelChange_18_listener", "BuildingMaterialComponent_ng_template_60_Template_input_ngModelChange_22_listener", "BuildingMaterialComponent_ng_template_60_Template_input_ngModelChange_26_listener", "BuildingMaterialComponent_ng_template_60_Template_textarea_ngModelChange_30_listener", "BuildingMaterialComponent_ng_template_60_Template_input_ngModelChange_34_listener", "BuildingMaterialComponent_ng_template_60_Template_button_click_39_listener", "openImageBinder", "BuildingMaterialComponent_ng_template_60_div_42_Template", "BuildingMaterialComponent_ng_template_60_Template_button_click_44_listener", "ref_r15", "dialogRef", "onClose", "BuildingMaterialComponent_ng_template_60_Template_button_click_46_listener", "onSubmit", "ɵɵtwoWayProperty", "option_r17", "value", "label", "option_r18", "image_r20", "thumbnailUrl", "ɵɵsanitizeUrl", "name", "BuildingMaterialComponent_ng_template_62_div_46_Template_div_click_0_listener", "_r19", "toggleImageSelection", "BuildingMaterialComponent_ng_template_62_div_46_i_3_Template", "BuildingMaterialComponent_ng_template_62_div_46_div_4_Template", "BuildingMaterialComponent_ng_template_62_div_46_Template_button_click_5_listener", "imagePreview_r21", "previewImage", "BuildingMaterialComponent_ng_template_62_div_46_img_8_Template", "BuildingMaterialComponent_ng_template_62_div_46_div_9_Template", "ɵɵclassProp", "isImageSelected", "indexOf", "id", "ɵɵpipeBind1", "size", "BuildingMaterialComponent_ng_template_62_Template_nb_select_ngModelChange_21_listener", "_r16", "selectedCate<PERSON><PERSON>", "BuildingMaterialComponent_ng_template_62_Template_nb_select_selectedChange_21_listener", "categoryChanged", "BuildingMaterialComponent_ng_template_62_nb_option_22_Template", "BuildingMaterialComponent_ng_template_62_Template_nb_select_ngModelChange_26_listener", "filterBindingOption", "BuildingMaterialComponent_ng_template_62_Template_nb_select_selectedChange_26_listener", "filterImages", "BuildingMaterialComponent_ng_template_62_nb_option_27_Template", "BuildingMaterialComponent_ng_template_62_Template_input_ngModelChange_31_listener", "imageSearchTerm", "BuildingMaterialComponent_ng_template_62_Template_input_input_31_listener", "BuildingMaterialComponent_ng_template_62_Template_button_click_33_listener", "loadImages", "BuildingMaterialComponent_ng_template_62_Template_button_click_37_listener", "selectAllImages", "BuildingMaterialComponent_ng_template_62_Template_button_click_40_listener", "clearAllSelection", "BuildingMaterialComponent_ng_template_62_div_46_Template", "BuildingMaterialComponent_ng_template_62_div_47_Template", "BuildingMaterialComponent_ng_template_62_Template_ngx_pagination_CollectionSizeChange_51_listener", "imageTotalRecords", "BuildingMaterialComponent_ng_template_62_Template_ngx_pagination_PageSizeChange_51_listener", "imagePageSize", "BuildingMaterialComponent_ng_template_62_Template_ngx_pagination_PageChange_51_listener", "imageCurrentPage", "imagePageChanged", "BuildingMaterialComponent_ng_template_62_Template_button_click_53_listener", "ref_r22", "onCloseImageBinder", "BuildingMaterialComponent_ng_template_62_Template_button_click_55_listener", "onConfirmImageSelection", "categoryOptions", "filterBindingOptions", "selectedImages", "filteredImages", "previewingImage", "fullUrl", "BuildingMaterialComponent_ng_template_64_Template_button_click_5_listener", "_r23", "previousImage", "BuildingMaterialComponent_ng_template_64_Template_button_click_8_listener", "nextImage", "BuildingMaterialComponent_ng_template_64_img_12_Template", "BuildingMaterialComponent_ng_template_64_div_13_Template", "BuildingMaterialComponent_ng_template_64_Template_button_click_18_listener", "toggleImageSelectionInPreview", "BuildingMaterialComponent_ng_template_64_Template_button_click_20_listener", "ref_r24", "close", "currentPreviewIndex", "ɵɵtextInterpolate2", "BuildingMaterialComponent_ng_template_66_Template_button_click_9_listener", "ref_r26", "_r25", "currentImageShowing", "PictureCategory", "BuildingMaterialComponent", "constructor", "_allow", "dialogService", "message", "valid", "_buildCaseService", "_materialService", "_utilityService", "_pictureService", "isNew", "listBuildCases", "materialOptions", "materialOptionsId", "ShowPrice", "filterMapping", "availableImages", "BUILDING_MATERIAL", "SCHEMATIC", "isCategorySelected", "statusOptions", "getStatusList", "ngOnInit", "getListBuildCase", "apiBuildCaseGetAllBuildCaseForSelectPost$Json", "body", "CIsPagi", "CStatus", "pipe", "res", "StatusCode", "Entries", "selectedBuildCaseId", "getMaterialList", "subscribe", "pageIndex", "apiMaterialGetMaterialListPost$Json", "CBuildCaseId", "CPlanUse", "PageSize", "pageSize", "PageIndex", "totalRecords", "TotalItems", "pageChanged", "apiMaterialExportExcelMaterialListPost$Json", "FileByte", "downloadExcelFile", "exportExelMaterialTemplate", "apiMaterialExportExcelMaterialTemplatePost$Json", "ref", "Enable", "open", "data", "closeOnBackdropClick", "validation", "clear", "required", "isStringMaxLength", "errorMessages", "showErrorMSGs", "apiMaterialSaveMaterialAdminPost$Json", "CMaterialId", "CPictureId", "selectedImageIds", "showSucessMSG", "showErrorMSG", "Message", "detectFileExcel", "event", "target", "reader", "FileReader", "readAsBinaryString", "files", "onload", "e", "binarystr", "result", "wb", "read", "type", "wsname", "SheetNames", "ws", "Sheets", "isValidFile", "utils", "sheet_to_json", "for<PERSON>ach", "x", "apiMaterialImportExcelMaterialListPost$Json", "CFile", "showImage", "imageUrl", "dialog", "changeFilter", "apiPictureGetPicturelListPost$Json", "cPictureType", "map", "picture", "CPictureCode", "CBase64", "lastModified", "CUpdateDT", "Date", "boundImageIds", "parseInt", "boundImages", "filter", "image", "includes", "filtered", "trim", "searchTerm", "toLowerCase", "index", "findIndex", "selected", "splice", "push", "some", "imagePreviewRef", "stopPropagation", "img", "imageNames", "join", "saveImageBinding", "category", "getCategoryLabel", "option", "find", "opt", "page", "ɵɵdirectiveInject", "i1", "AllowHelper", "i2", "NbDialogService", "i3", "MessageService", "i4", "ValidationHelper", "i5", "BuildCaseService", "MaterialService", "i6", "UtilityService", "PictureService", "selectors", "standalone", "features", "ɵɵInheritDefinitionFeature", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "BuildingMaterialComponent_Template", "rf", "ctx", "BuildingMaterialComponent_Template_nb_select_ngModelChange_11_listener", "_r1", "BuildingMaterialComponent_nb_option_12_Template", "BuildingMaterialComponent_Template_input_ngModelChange_17_listener", "BuildingMaterialComponent_Template_input_ngModelChange_22_listener", "BuildingMaterialComponent_Template_nb_checkbox_checkedChange_25_listener", "BuildingMaterialComponent_Template_nb_checkbox_change_25_listener", "BuildingMaterialComponent_button_27_Template", "BuildingMaterialComponent_button_28_Template", "BuildingMaterialComponent_button_29_Template", "BuildingMaterialComponent_button_30_Template", "BuildingMaterialComponent_Template_input_change_31_listener", "BuildingMaterialComponent_Template_button_click_33_listener", "BuildingMaterialComponent_th_54_Template", "BuildingMaterialComponent_tbody_57_Template", "BuildingMaterialComponent_Template_ngx_pagination_CollectionSizeChange_59_listener", "BuildingMaterialComponent_Template_ngx_pagination_PageSizeChange_59_listener", "BuildingMaterialComponent_Template_ngx_pagination_PageChange_59_listener", "BuildingMaterialComponent_ng_template_60_Template", "ɵɵtemplateRefExtractor", "BuildingMaterialComponent_ng_template_62_Template", "BuildingMaterialComponent_ng_template_64_Template", "BuildingMaterialComponent_ng_template_66_Template", "isExcelExport", "isCreate", "isExcelImport", "i7", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "DecimalPipe", "i8", "DefaultValueAccessor", "NumberValueAccessor", "NgControlStatus", "MaxLengthValidator", "NgModel", "NbCardComponent", "NbCardBodyComponent", "NbCardFooterComponent", "NbCardHeaderComponent", "NbCheckboxComponent", "NbInputDirective", "NbSelectComponent", "NbOptionComponent", "i9", "BreadcrumbComponent", "i10", "PaginationComponent", "styles"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\selection-management\\building-material\\building-material.component.ts", "C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\selection-management\\building-material\\building-material.component.html"], "sourcesContent": ["import { Component, OnInit, TemplateRef, } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { NbDialogService } from '@nebular/theme';\r\nimport { MessageService } from 'src/app/shared/services/message.service';\r\nimport { ValidationHelper } from 'src/app/shared/helper/validationHelper';\r\nimport { BuildCaseService, MaterialService, PictureService } from 'src/services/api/services';\r\nimport { BuildCaseGetListReponse, SaveMaterialArgs, GetMaterialListResponse } from 'src/services/api/models';\r\nimport { finalize, mergeMap, tap } from 'rxjs';\r\nimport { AllowHelper } from 'src/app/shared/helper/allowHelper';\r\nimport { UtilityService } from 'src/app/shared/services/utility.service';\r\nimport * as XLSX from 'xlsx';\r\nimport { SharedModule } from '../../components/shared.module';\r\nimport { BaseComponent } from '../../components/base/baseComponent';\r\nimport { EnumStatus, EnumStatusHelper } from 'src/app/shared/enum/enumStatus';\r\n\r\n// 圖片類別枚舉\r\nenum PictureCategory {\r\n  NONE = 0,           // 未選擇\r\n  BUILDING_MATERIAL = 1,  // 建材圖片\r\n  SCHEMATIC = 2       // 示意圖片\r\n}\r\n\r\n// 圖片項目介面\r\ninterface ImageItem {\r\n  id: number;\r\n  name: string;\r\n  size: number;\r\n  thumbnailUrl?: string;\r\n  fullUrl?: string;\r\n  lastModified?: Date;\r\n}\r\n\r\n@Component({\r\n  selector: 'ngx-building-material',\r\n  templateUrl: './building-material.component.html',\r\n  styleUrls: ['./building-material.component.scss'],\r\n  standalone: true,\r\n  imports: [CommonModule, SharedModule],\r\n})\r\n\r\nexport class BuildingMaterialComponent extends BaseComponent implements OnInit {\r\n  isNew = true\r\n\r\n  materialList: GetMaterialListResponse[]\r\n  selectedMaterial: GetMaterialListResponse\r\n\r\n  listBuildCases: BuildCaseGetListReponse[] = []\r\n  selectedBuildCaseId: number\r\n\r\n  materialOptions = [\r\n    {\r\n      value: null,\r\n      label: '全部',\r\n    },\r\n    {\r\n      value: false,\r\n      label: '方案',\r\n    },\r\n    {\r\n      value: true,\r\n      label: '選樣',\r\n    }]; materialOptionsId = null;\r\n  CSelectName: string = \"\"\r\n  // 移除圖片檔名相關欄位\r\n  // CImageCode: string = \"\"\r\n  // CInfoImageCode: string = \"\"\r\n  // 啟用建材代號欄位\r\n  CImageCode: string = \"\"\r\n  ShowPrice: boolean = false\r\n  currentImageShowing: string = \"\"\r\n  filterMapping: boolean = false\r\n  CIsMapping: boolean = true\r\n  // 圖片綁定相關屬性\r\n  availableImages: ImageItem[] = []\r\n  filteredImages: ImageItem[] = []\r\n  selectedImages: ImageItem[] = []\r\n  imageSearchTerm: string = \"\"\r\n  previewingImage: ImageItem | null = null\r\n  currentPreviewIndex: number = 0\r\n\r\n  // 圖片綁定篩選選項\r\n  filterBindingOption: string = \"all\" // 預設顯示全部圖片\r\n  filterBindingOptions = [\r\n    { value: \"all\", label: \"顯示全部\" },\r\n    { value: \"bound\", label: \"僅顯示已綁定\" }\r\n  ]\r\n\r\n  // 圖片綁定分頁屬性\r\n  imageCurrentPage: number = 1\r\n  imagePageSize: number = 50\r\n  imageTotalRecords: number = 0\r\n\r\n  // 類別選項\r\n  categoryOptions = [\r\n    { value: PictureCategory.BUILDING_MATERIAL, label: '建材圖片' },\r\n    { value: PictureCategory.SCHEMATIC, label: '示意圖片' }\r\n  ]\r\n  selectedCategory: PictureCategory = PictureCategory.BUILDING_MATERIAL\r\n  isCategorySelected: boolean = true // 預設選擇建材圖片\r\n  // 讓模板可以使用 enum\r\n  PictureCategory = PictureCategory;\r\n  \r\n  // 狀態相關屬性\r\n  EnumStatus = EnumStatus;\r\n  EnumStatusHelper = EnumStatusHelper;\r\n  statusOptions = EnumStatusHelper.getStatusList();\r\n\r\n  constructor(\r\n    private _allow: AllowHelper,\r\n    private dialogService: NbDialogService,\r\n    private message: MessageService,\r\n    private valid: ValidationHelper,\r\n    private _buildCaseService: BuildCaseService,\r\n    private _materialService: MaterialService,\r\n    private _utilityService: UtilityService,\r\n    private _pictureService: PictureService\r\n  ) {\r\n    super(_allow)\r\n  }\r\n\r\n  override ngOnInit(): void {\r\n    this.getListBuildCase()\r\n  }\r\n\r\n  getListBuildCase() {\r\n    this._buildCaseService.apiBuildCaseGetAllBuildCaseForSelectPost$Json({\r\n      body: {\r\n        CIsPagi: false,\r\n        CStatus: 1,\r\n      }\r\n    })\r\n      .pipe(\r\n        tap(res => {\r\n          if (res.StatusCode == 0) {\r\n            this.listBuildCases = res.Entries?.length ? res.Entries : []\r\n            this.selectedBuildCaseId = this.listBuildCases[0].cID!\r\n          }\r\n        }),\r\n        mergeMap(() => this.getMaterialList())\r\n      ).subscribe()\r\n  } getMaterialList(pageIndex: number = 1) {\r\n    return this._materialService.apiMaterialGetMaterialListPost$Json({\r\n      body: {\r\n        CBuildCaseId: this.selectedBuildCaseId,\r\n        CPlanUse: this.materialOptionsId,\r\n        CSelectName: this.CSelectName,\r\n        // 啟用建材代號查詢條件\r\n        CImageCode: this.CImageCode,\r\n        // CInfoImageCode: this.CInfoImageCode,\r\n        PageSize: this.pageSize,\r\n        PageIndex: pageIndex,\r\n        CIsMapping: this.CIsMapping\r\n      }\r\n    }).pipe(\r\n      tap(res => {\r\n        if (res.StatusCode == 0) {\r\n          this.materialList = res.Entries! ?? []\r\n          this.totalRecords = res.TotalItems!\r\n\r\n          if (this.materialList.length > 0) {\r\n            this.ShowPrice = this.materialList[0].CShowPrice!;\r\n          }\r\n        }\r\n      })\r\n    )\r\n  }\r\n\r\n  search() {\r\n    this.getMaterialList().subscribe()\r\n  }\r\n\r\n  pageChanged(pageIndex: number) {\r\n    this.getMaterialList(pageIndex).subscribe()\r\n  }\r\n\r\n  exportExelMaterialList() {\r\n    this._materialService.apiMaterialExportExcelMaterialListPost$Json({\r\n      body: this.selectedBuildCaseId\r\n    }).subscribe(res => {\r\n      if (res.StatusCode == 0) {\r\n        if (res.Entries!.FileByte) {\r\n          this._utilityService.downloadExcelFile(res.Entries!.FileByte, '建材管理')\r\n        }\r\n      }\r\n    })\r\n  }\r\n\r\n  exportExelMaterialTemplate() {\r\n    this._materialService.apiMaterialExportExcelMaterialTemplatePost$Json({\r\n      body: this.selectedBuildCaseId\r\n    }).subscribe(res => {\r\n      if (res.StatusCode == 0) {\r\n        if (res.Entries!.FileByte) {\r\n          this._utilityService.downloadExcelFile(res.Entries!.FileByte, '建材管理')\r\n        }\r\n      }\r\n    })\r\n  }\r\n  addNew(ref: any) {\r\n    this.isNew = true\r\n    this.selectedMaterial = {\r\n      CStatus: EnumStatus.Enable // 預設狀態為啟用\r\n    }\r\n    this.dialogService.open(ref)\r\n  }\r\n  onSelectedMaterial(data: GetMaterialListResponse, ref: any) {\r\n    this.isNew = false\r\n    this.selectedMaterial = { ...data }\r\n    this.dialogService.open(ref)\r\n  }\r\n  bindImageForMaterial(data: GetMaterialListResponse, ref: TemplateRef<any>) {\r\n    this.selectedMaterial = { ...data }\r\n    this.loadImages()\r\n    // 清空當前選擇的圖片，避免舊數據干擾\r\n    this.clearAllSelection()\r\n    // 重設篩選選項為顯示全部\r\n    this.filterBindingOption = \"all\"\r\n    this.imageSearchTerm = \"\"\r\n    this.dialogService.open(ref, { closeOnBackdropClick: false })\r\n  } validation() {\r\n    this.valid.clear();\r\n    this.valid.required('[名稱]', this.selectedMaterial.CName)\r\n    this.valid.required('[項目]', this.selectedMaterial.CPart)\r\n    this.valid.required('[位置]', this.selectedMaterial.CLocation)\r\n    this.valid.required('[建材選項名稱]', this.selectedMaterial.CSelectName)\r\n    // 啟用建材代號驗證\r\n    this.valid.required('[建材代號]', this.selectedMaterial.CImageCode)\r\n    this.valid.isStringMaxLength('[名稱]', this.selectedMaterial.CName, 30)\r\n    this.valid.isStringMaxLength('[項目]', this.selectedMaterial.CPart, 30)\r\n    this.valid.isStringMaxLength('[位置]', this.selectedMaterial.CLocation, 30)\r\n    this.valid.isStringMaxLength('[建材選項名稱]', this.selectedMaterial.CSelectName, 30)\r\n    // 啟用建材代號長度驗證\r\n    this.valid.isStringMaxLength('[建材代號]', this.selectedMaterial.CImageCode, 30)\r\n  }\r\n\r\n  onSubmit(ref: any) {\r\n    this.validation()\r\n    if (this.valid.errorMessages.length > 0) {\r\n      this.message.showErrorMSGs(this.valid.errorMessages);\r\n      return\r\n    } this._materialService.apiMaterialSaveMaterialAdminPost$Json({\r\n      body: {\r\n        CBuildCaseId: this.selectedBuildCaseId,\r\n        // 暫時保留 CImageCode 給圖片綁定功能使用\r\n        CImageCode: this.selectedMaterial.CImageCode,\r\n        CName: this.selectedMaterial.CName,\r\n        CPart: this.selectedMaterial.CPart,\r\n        CLocation: this.selectedMaterial.CLocation,        CSelectName: this.selectedMaterial.CSelectName,\r\n        CDescription: this.selectedMaterial.CDescription,\r\n        CMaterialId: this.isNew ? null : this.selectedMaterial.CId!,\r\n        CPrice: this.selectedMaterial.CPrice,\r\n        CStatus: this.selectedMaterial.CStatus || EnumStatus.Enable,\r\n        CPictureId: (this.selectedMaterial as any).selectedImageIds || [] // 使用暫存的圖片 ID 陣列，預設為空陣列\r\n      }\r\n    })\r\n      .pipe(\r\n        tap(res => {\r\n          if (res.StatusCode === 0) {\r\n            this.message.showSucessMSG(\"執行成功\");\r\n          } else {\r\n            this.message.showErrorMSG(res.Message!);\r\n          }\r\n        }),\r\n        mergeMap(() => this.getMaterialList()),\r\n        finalize(() => ref.close())\r\n      ).subscribe()\r\n  }\r\n\r\n  onClose(ref: any) {\r\n    ref.close();\r\n  }\r\n\r\n  detectFileExcel(event: any) {\r\n    const target: DataTransfer = <DataTransfer>(event.target);\r\n    const reader: FileReader = new FileReader();\r\n    reader.readAsBinaryString(target.files[0]);\r\n    reader.onload = (e: any) => {\r\n      const binarystr: string = e.target.result;\r\n      const wb: XLSX.WorkBook = XLSX.read(binarystr, { type: 'binary' });\r\n\r\n      const wsname: string = wb.SheetNames[0];\r\n      const ws: XLSX.WorkSheet = wb.Sheets[wsname];\r\n\r\n      let isValidFile: boolean = true;\r\n      const data = XLSX.utils.sheet_to_json(ws);\r\n      if (data && data.length > 0) {\r\n        data.forEach((x: any) => {\r\n          if (!(x['名稱'] && x['項目'] && x['位置'] && x['建材選項名稱'] &&\r\n            (x['建材圖片檔名'] || x['建材說明'] || x['示意圖片檔名']))) {\r\n            isValidFile = false;\r\n          }\r\n        })\r\n\r\n        if (!isValidFile) {\r\n          this.message.showErrorMSG(\"导入文件时出现错误\")\r\n        } else {\r\n          this._materialService.apiMaterialImportExcelMaterialListPost$Json({\r\n            body: {\r\n              CBuildCaseId: this.selectedBuildCaseId,\r\n              CFile: target.files[0]\r\n            }\r\n          }).pipe(\r\n            tap(res => {\r\n              if (res.StatusCode == 0) {\r\n                this.message.showSucessMSG(\"執行成功\")\r\n              } else {\r\n                this.message.showErrorMSG(res.Message!)\r\n              }\r\n            }),\r\n            mergeMap(() => this.getMaterialList(1))\r\n          ).subscribe();\r\n        }\r\n      } else {\r\n        this.message.showErrorMSG(\"匯入的檔案內容為空，請檢查檔案並重新上傳。\")\r\n      }\r\n      event.target.value = null;\r\n    };\r\n  }\r\n\r\n  showImage(imageUrl: string, dialog: TemplateRef<any>) {\r\n    this.currentImageShowing = imageUrl;\r\n    this.dialogService.open(dialog);\r\n  }\r\n  changeFilter() {\r\n    if (this.filterMapping) {\r\n      this.CIsMapping = false;\r\n      this.getMaterialList().subscribe();\r\n    }\r\n    else {\r\n      this.CIsMapping = true;\r\n      this.getMaterialList().subscribe();\r\n    }\r\n  }\r\n  // 圖片綁定功能方法\r\n  openImageBinder(ref: TemplateRef<any>) {\r\n    this.loadImages();\r\n    // 重設篩選選項為顯示全部\r\n    this.filterBindingOption = \"all\";\r\n    this.imageSearchTerm = \"\";\r\n    this.dialogService.open(ref, { closeOnBackdropClick: false });\r\n  } loadImages() {\r\n    // 使用 PictureService API 載入圖片列表\r\n    if (this.isCategorySelected && this.selectedBuildCaseId) {\r\n      this._pictureService.apiPictureGetPicturelListPost$Json({\r\n        body: {\r\n          CBuildCaseId: this.selectedBuildCaseId,\r\n          cPictureType: this.selectedCategory,\r\n          PageIndex: this.imageCurrentPage, // 使用圖片當前頁碼\r\n          PageSize: this.imagePageSize // 使用圖片每頁筆數\r\n        }\r\n      }).subscribe(res => {\r\n        if (res.StatusCode === 0) {\r\n          // 將 API 回應轉換為 ImageItem 格式\r\n          this.availableImages = res.Entries?.map(picture => ({\r\n            id: picture.CId || 0, // 修復類型錯誤，直接使用數字\r\n            name: picture.CPictureCode || picture.CName || '',\r\n            size: 0, // API 中沒有檔案大小資訊\r\n            thumbnailUrl: picture.CBase64 ? `data:image/jpeg;base64,${picture.CBase64}` : '',\r\n            fullUrl: picture.CBase64 ? `data:image/jpeg;base64,${picture.CBase64}` : '',\r\n            lastModified: picture.CUpdateDT ? new Date(picture.CUpdateDT) : new Date()\r\n          })) || [];\r\n          this.filteredImages = [...this.availableImages];\r\n          this.imageTotalRecords = res.TotalItems || 0; // 更新圖片總筆數\r\n\r\n          // 如果有已綁定的圖片，自動選中\r\n          if (this.selectedMaterial && this.selectedMaterial.CSelectPictureId && this.selectedMaterial.CSelectPictureId.length > 0) {\r\n            // 將已綁定的圖片ID轉換為數字陣列\r\n            const boundImageIds = this.selectedMaterial.CSelectPictureId.map(id => typeof id === 'string' ? parseInt(id) : id);\r\n\r\n            // 篩選出已綁定的圖片項目\r\n            const boundImages = this.availableImages.filter(image =>\r\n              boundImageIds.includes(image.id)\r\n            );\r\n\r\n            // 將已綁定的圖片設為選中狀態\r\n            this.selectedImages = [...boundImages];\r\n          }\r\n        } else {\r\n          this.message.showErrorMSG(res.Message || '載入圖片失敗');\r\n          this.availableImages = [];\r\n          this.filteredImages = [];\r\n        }\r\n      });\r\n    } else {\r\n      // 如果沒有選擇類別或建案，清空圖片列表\r\n      this.availableImages = [];\r\n      this.filteredImages = [];\r\n    }\r\n  }\r\n  filterImages() {\r\n    // 首先根據搜尋詞進行篩選\r\n    let filtered = [...this.availableImages];\r\n\r\n    if (this.imageSearchTerm.trim()) {\r\n      const searchTerm = this.imageSearchTerm.toLowerCase();\r\n      filtered = filtered.filter(image =>\r\n        image.name.toLowerCase().includes(searchTerm)\r\n      );\r\n    }\r\n\r\n    // 然後根據綁定狀態進行篩選\r\n    if (this.filterBindingOption === \"bound\" && this.selectedMaterial && this.selectedMaterial.CSelectPictureId) {\r\n      // 轉換已綁定的圖片ID為數字陣列\r\n      const boundImageIds = this.selectedMaterial.CSelectPictureId.map(id =>\r\n        typeof id === 'string' ? parseInt(id) : id\r\n      );\r\n\r\n      // 只顯示已綁定的圖片\r\n      filtered = filtered.filter(image => boundImageIds.includes(image.id));\r\n    }\r\n\r\n    this.filteredImages = filtered;\r\n  }\r\n\r\n  toggleImageSelection(image: ImageItem) {\r\n    const index = this.selectedImages.findIndex(selected => selected.id === image.id);\r\n    if (index > -1) {\r\n      this.selectedImages.splice(index, 1);\r\n    } else {\r\n      this.selectedImages.push(image);\r\n    }\r\n  }\r\n\r\n  isImageSelected(image: ImageItem): boolean {\r\n    return this.selectedImages.some(selected => selected.id === image.id);\r\n  }\r\n\r\n  selectAllImages() {\r\n    this.selectedImages = [...this.filteredImages];\r\n  }\r\n\r\n  clearAllSelection() {\r\n    this.selectedImages = [];\r\n  } previewImage(image: ImageItem, imagePreviewRef: TemplateRef<any>, event: Event) {\r\n    event.stopPropagation();\r\n    this.previewingImage = image;\r\n    this.currentPreviewIndex = this.filteredImages.findIndex(img => img.id === image.id);\r\n    this.dialogService.open(imagePreviewRef);\r\n  }\r\n\r\n  previousImage() {\r\n    if (this.currentPreviewIndex > 0) {\r\n      this.currentPreviewIndex--;\r\n      this.previewingImage = this.filteredImages[this.currentPreviewIndex];\r\n    }\r\n  }\r\n\r\n  nextImage() {\r\n    if (this.currentPreviewIndex < this.filteredImages.length - 1) {\r\n      this.currentPreviewIndex++;\r\n      this.previewingImage = this.filteredImages[this.currentPreviewIndex];\r\n    }\r\n  }\r\n\r\n  toggleImageSelectionInPreview() {\r\n    if (this.previewingImage) {\r\n      this.toggleImageSelection(this.previewingImage);\r\n    }\r\n  }\r\n  onConfirmImageSelection(ref: any) {\r\n    if (this.selectedImages.length > 0) {\r\n      // 收集選中圖片的 ID\r\n      const selectedImageIds = this.selectedImages.map(img => img.id);      // 如果只選取一張圖片，直接設定圖片 ID\r\n      if (this.selectedImages.length === 1) {\r\n        // 設定第一張圖片的 ID 到 CPictureId（單一數字）\r\n        this.selectedMaterial.CPictureId = this.selectedImages[0].id;\r\n      } else {\r\n        // 多張圖片的話，可以設定第一張，或者讓使用者選擇主要圖片\r\n        const imageNames = this.selectedImages.map(img => img.name).join(', ');\r\n        this.message.showSucessMSG(`已選取 ${this.selectedImages.length} 張圖片: ${imageNames}`);\r\n        // 設定第一張圖片的 ID 到 CPictureId（單一數字）\r\n        this.selectedMaterial.CPictureId = this.selectedImages[0].id;\r\n      }\r\n\r\n      // 暫存所有選中的圖片 ID，供 API 呼叫使用\r\n      (this.selectedMaterial as any).selectedImageIds = selectedImageIds;\r\n\r\n      // 如果是從表格直接綁定圖片（有 CId），則直接保存到資料庫\r\n      if (this.selectedMaterial.CId) {\r\n        this.saveImageBinding();\r\n      }\r\n    }\r\n\r\n    this.clearAllSelection();\r\n    ref.close();\r\n  }\r\n  // 新增方法：保存圖片綁定\r\n  saveImageBinding() {\r\n    this._materialService.apiMaterialSaveMaterialAdminPost$Json({\r\n      body: {\r\n        CBuildCaseId: this.selectedBuildCaseId,\r\n        CImageCode: this.selectedMaterial.CImageCode,\r\n        CName: this.selectedMaterial.CName,\r\n        CPart: this.selectedMaterial.CPart,\r\n        CLocation: this.selectedMaterial.CLocation,        CSelectName: this.selectedMaterial.CSelectName,\r\n        CDescription: this.selectedMaterial.CDescription,\r\n        CMaterialId: this.selectedMaterial.CId!,\r\n        CPrice: this.selectedMaterial.CPrice,\r\n        CStatus: this.selectedMaterial.CStatus || EnumStatus.Enable,\r\n        CPictureId: (this.selectedMaterial as any).selectedImageIds || [] // 選中的圖片 ID 陣列，預設為空陣列\r\n      }\r\n    }).pipe(\r\n      tap(res => {\r\n        if (res.StatusCode === 0) {\r\n          this.message.showSucessMSG(`圖片綁定成功`);\r\n        } else {\r\n          this.message.showErrorMSG(res.Message!);\r\n        }\r\n      }),\r\n      mergeMap(() => this.getMaterialList()),\r\n      finalize(() => {\r\n        // 清空選取的建材\r\n        this.selectedMaterial = {};\r\n      })\r\n    ).subscribe()\r\n  }\r\n  onCloseImageBinder(ref: any) {\r\n    this.clearAllSelection();\r\n    this.imageSearchTerm = \"\";\r\n    this.filterBindingOption = \"all\"; // 重設篩選選項為顯示全部\r\n    this.imageCurrentPage = 1; // 重設圖片頁碼\r\n    ref.close();\r\n  }// 類別變更處理方法\r\n  categoryChanged(category: PictureCategory) {\r\n    this.selectedCategory = category;\r\n    this.isCategorySelected = true;\r\n    // 當類別變更時重設頁碼並重新載入圖片\r\n    this.imageCurrentPage = 1;\r\n    if (this.selectedBuildCaseId) {\r\n      this.loadImages();\r\n    }\r\n  }\r\n\r\n  // 獲取類別標籤的方法\r\n  getCategoryLabel(category: number): string {\r\n    const option = this.categoryOptions.find(opt => opt.value === category);\r\n    return option ? option.label : '未知類別';\r\n  }\r\n\r\n  // 圖片分頁變更處理方法\r\n  imagePageChanged(page: number) {\r\n    this.imageCurrentPage = page;\r\n    this.loadImages();\r\n  }\r\n}\r\n", "<nb-card accent=\"success\">\r\n  <nb-card-header>\r\n    <ngx-breadcrumb></ngx-breadcrumb>\r\n  </nb-card-header>\r\n  <nb-card-body>\r\n    <h1 class=\"font-bold text-[#818181]\"> 可設定單筆或批次匯入設定各區域及方案對應之建材。\r\n    </h1>\r\n    <div class=\"d-flex flex-wrap\">\r\n      <div class=\"col-md-6\">\r\n        <div class=\"form-group d-flex align-items-center w-full\">\r\n          <label for=\"buildingName\" class=\"label mr-2 w-[22%]\">建案</label> <nb-select placeholder=\"建案\"\r\n            [(ngModel)]=\"selectedBuildCaseId\" (ngModelChange)=\"search()\" class=\"w-full\">\r\n            <nb-option *ngFor=\"let buildCase of listBuildCases\" [value]=\"buildCase.cID\">\r\n              {{ buildCase.CBuildCaseName }}\r\n            </nb-option>\r\n          </nb-select>\r\n        </div>\r\n      </div>\r\n      <!-- <div class=\"col-md-6\">\r\n        <div class=\"form-group d-flex align-items-center w-full\">\r\n          <label for=\"buildingName\" class=\"label mr-2  w-[22%]\">建材類別</label>\r\n          <nb-select placeholder=\"建材類別\" [(ngModel)]=\"materialOptionsId\" class=\"w-full\">\r\n            <nb-option *ngFor=\"let option of materialOptions\" [value]=\"option.value\">\r\n              {{ option.label }}\r\n            </nb-option>\r\n          </nb-select>\r\n        </div>\r\n      </div> -->\r\n      <div class=\"col-md-6\">\r\n        <div class=\"form-group d-flex align-items-center w-full\">\r\n          <label for=\"buildingName\" class=\"label mr-2 w-[22%]\">建材選項名稱 </label>\r\n          <input type=\"text\" nbInput placeholder=\"建材選項名稱\" [(ngModel)]=\"CSelectName\" class=\"w-full\" maxlength=\"50\">\r\n        </div>\r\n      </div>\r\n      <!-- 啟用建材代號欄位 -->\r\n      <div class=\"col-md-6\">\r\n        <div class=\"form-group d-flex align-items-center w-full\">\r\n          <label for=\"buildingName\" class=\"label mr-2 w-[22%]\">建材代號</label>\r\n          <input type=\"text\" nbInput placeholder=\"建材代號\" [(ngModel)]=\"CImageCode\" class=\"w-full\" maxlength=\"20\">\r\n        </div>\r\n      </div>\r\n      <div class=\"col-md-12\">\r\n        <div class=\"d-flex justify-content-end w-full\">\r\n          <nb-checkbox status=\"basic\" class=\"flex\" style=\"flex:auto\" [(checked)]=\"filterMapping\"\r\n            (change)=\"changeFilter()\">\r\n            只顯示缺少建材圖片或示意圖片的建材\r\n          </nb-checkbox>\r\n          <button *ngIf=\"isExcelExport\" class=\"btn btn-success mr-2\" (click)=\"exportExelMaterialList()\">匯出 <i\r\n              class=\"fas fa-file-download\"></i></button>\r\n          <button *ngIf=\"isRead\" class=\"btn btn-info mr-2 text-white ml-2\" (click)=\"search()\">\r\n            查詢 <i class=\"fas fa-search\"></i></button>\r\n          <button *ngIf=\"isCreate\" class=\"btn btn-info mx-1 ml-2 mr-2\" (click)=\"addNew(dialog)\">單筆新增 <i\r\n              class=\"fas fa-plus\"></i></button>\r\n          <button class=\"btn btn-info mx-1\" *ngIf=\"isExcelImport\" (click)=\"inputFile.click()\"> 批次匯入 </button>\r\n          <input class=\"hidden\" type=\"file\" accept=\".xls, .xlsx\" #inputFile (change)=\"detectFileExcel($event)\">\r\n          <button class=\"btn btn-success ml-2\" (click)=\"exportExelMaterialTemplate()\">下載範例檔案 <i\r\n              class=\"fas fa-file-download\"></i></button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <div class=\"table-responsive mt-4\">\r\n      <table class=\"table table-striped border \" style=\"min-width: 1200px; background-color:#f3f3f3;\">\r\n        <thead>          <tr style=\"background-color: #27ae60; color: white;\">\r\n            <th scope=\"col\" class=\"col-1\">項次</th>\r\n            <th scope=\"col\" class=\"col-1\">建材代號</th>\r\n            <th scope=\"col\" class=\"col-2\">選項題目</th>\r\n            <th scope=\"col\" class=\"col-1\">選項名稱</th>\r\n            <th scope=\"col\" class=\"col-3\">建材說明</th>\r\n            <th scope=\"col\" class=\"col-1\">已綁定圖片</th>\r\n            <th scope=\"col\" class=\"col-1\">狀態</th>\r\n            <th scope=\"col\" class=\"col-1\" *ngIf=\"ShowPrice == true\">價格</th>\r\n            <th scope=\"col\" class=\"col-1 text-center\">操作</th>\r\n          </tr>\r\n        </thead>\r\n        <tbody *ngIf=\"materialList != null && materialList.length > 0\">\r\n          <tr *ngFor=\"let item of materialList ; let i = index\">\r\n            <td>{{ item.CId}}</td>\r\n            <td>{{ item.CImageCode || '待設定' }}</td>\r\n            <td>{{ item.CName }} - {{ item.CPart }} - {{ item.CLocation }}</td>\r\n            <td [style]=\"!item.CIsMapping ? 'color: red' : ''\">{{ item.CSelectName}}</td>\r\n            <td>{{ item.CDescription}}</td>\r\n            <td>\r\n              <div class=\"d-flex align-items-center\">\r\n                <span *ngIf=\"item.CSelectPictureId && item.CSelectPictureId.length > 0\"\r\n                  class=\"badge badge-success mr-2\">{{ item.CSelectPictureId.length }}</span>\r\n                <span *ngIf=\"!item.CSelectPictureId || item.CSelectPictureId.length === 0\"\r\n                  class=\"badge badge-danger mr-2\">0</span>\r\n                <span>{{ (item.CSelectPictureId && item.CSelectPictureId.length > 0) ? '已綁定' : '未綁定' }}</span>\r\n              </div>\r\n            </td>\r\n            <td *ngIf=\"item.CShowPrice == true\">{{ item.CPrice}}</td>\r\n            <td class=\"text-center w-32\">\r\n              <button class=\"btn btn-outline-primary btn-sm m-1\" (click)=\"onSelectedMaterial(item, dialog)\"\r\n                *ngIf=\"isRead\">編輯</button> <button class=\"btn btn-outline-info btn-sm m-1\"\r\n                (click)=\"bindImageForMaterial(item, imageBinder)\" *ngIf=\"isRead\" [title]=\"'為 ' + item.CName + ' 綁定圖片'\">\r\n                <i class=\"fas fa-images\"></i> 綁定\r\n              </button>\r\n            </td>\r\n          </tr>\r\n        </tbody>\r\n      </table>\r\n    </div>\r\n  </nb-card-body>\r\n  <nb-card-footer class=\"d-flex justify-content-center\">\r\n    <ngx-pagination [(CollectionSize)]=\"totalRecords\" [(PageSize)]=\"pageSize\" [(Page)]=\"pageIndex\"\r\n      (PageChange)=\"pageChanged($event)\">\r\n    </ngx-pagination>\r\n  </nb-card-footer>\r\n</nb-card>\r\n\r\n<ng-template #dialog let-dialog let-ref=\"dialogRef\">\r\n  <nb-card class=\"w-[700px]\">\r\n    <nb-card-header>\r\n      建材管理 > 新增建材\r\n    </nb-card-header>\r\n    <nb-card-body class=\"px-4\">\r\n      <h5 class=\"text-base\">請輸入下方內容新增建材。</h5>\r\n      <div class=\"w-full mt-3\">\r\n        <!-- 啟用建材代號欄位 -->\r\n        <div class=\"flex items-center\">\r\n          <label class=\"required-field w-[150px]\">建材代號</label>\r\n          <input type=\"text\" class=\"w-full !max-w-full p-2 rounded text-[13px]\" nbInput maxlength=\"20\"\r\n            [(ngModel)]=\"selectedMaterial.CImageCode\" />\r\n        </div>\r\n\r\n        <div class=\"flex items-center mt-3\">\r\n          <label class=\"required-field w-[150px]\">名稱</label>\r\n          <input type=\"text\" class=\"w-full !max-w-full p-2 rounded text-[13px]\" nbInput maxlength=\"30\"\r\n            [(ngModel)]=\"selectedMaterial.CName\" />\r\n        </div>\r\n\r\n        <div class=\"flex items-center mt-3\">\r\n          <label class=\"required-field w-[150px]\">項目</label>\r\n          <input type=\"text\" class=\"w-full !max-w-full p-2 rounded text-[13px]\" nbInput maxlength=\"30\"\r\n            [(ngModel)]=\"selectedMaterial.CPart\" />\r\n        </div>\r\n        <div class=\"flex items-center mt-3\">\r\n          <label class=\"required-field w-[150px]\">位置</label>\r\n          <input type=\"text\" class=\"w-full !max-w-full p-2 rounded text-[13px]\" nbInput maxlength=\"30\"\r\n            [(ngModel)]=\"selectedMaterial.CLocation\" />\r\n        </div>\r\n\r\n        <div class=\"flex items-center mt-3\">\r\n          <label class=\"required-field w-[150px]\">建材選項名稱</label>\r\n          <input type=\"text\" nbInput class=\"w-full !max-w-full p-2 rounded text-[13px]\" maxlength=\"50\"\r\n            [(ngModel)]=\"selectedMaterial.CSelectName\" />\r\n        </div>\r\n\r\n        <div class=\"flex items-center mt-3\">\r\n          <label class=\"w-[150px]\">建材說明</label>\r\n          <textarea nbInput [(ngModel)]=\"selectedMaterial.CDescription\" [rows]=\"4\"\r\n            class=\"resize-none w-full !max-w-full p-2 rounded text-[13px]\"></textarea>\r\n        </div>\r\n        <div class=\"flex items-center mt-3\">\r\n          <label class=\"w-[150px]\">價格</label>\r\n          <input type=\"number\" nbInput class=\"w-full !max-w-full p-2 rounded text-[13px]\" maxlength=\"30\"\r\n            [(ngModel)]=\"selectedMaterial.CPrice\" />\r\n        </div>\r\n\r\n        <!-- 圖片綁定按鈕 -->\r\n        <div class=\"flex items-center mt-4 pt-3 border-t border-gray-200\">\r\n          <label class=\"w-[150px]\">圖片綁定</label>\r\n          <div class=\"flex gap-2 w-full\">\r\n            <button type=\"button\" class=\"btn btn-outline-info btn-sm\" (click)=\"openImageBinder(imageBinder)\"\r\n              [title]=\"'為建材綁定圖片'\">\r\n              <i class=\"fas fa-images mr-2\"></i>選擇圖片\r\n            </button>\r\n            <div class=\"text-sm text-gray-600 flex items-center\" *ngIf=\"selectedMaterial.CImageCode\">\r\n              <i class=\"fas fa-check-circle text-green-500 mr-2\"></i>\r\n              已設定建材代號: {{ selectedMaterial.CImageCode }}\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </nb-card-body>\r\n    <nb-card-footer class=\"d-flex justify-content-center\">\r\n      <button class=\"btn btn-danger btn-sm mr-4\" (click)=\"onClose(ref)\">關閉</button>\r\n      <button class=\"btn btn-success btn-sm\" (click)=\"onSubmit(ref)\">儲存</button>\r\n    </nb-card-footer>\r\n  </nb-card>\r\n</ng-template>\r\n\r\n<ng-template #imageBinder let-dialog let-ref=\"dialogRef\">\r\n  <nb-card class=\"w-[80vw] max-w-[1000px] h-[85vh]\">\r\n    <nb-card-header>\r\n      圖片綁定 - {{ selectedMaterial.CName ? '為 ' + selectedMaterial.CName + ' 選擇建材圖片' : '選擇建材圖片' }}\r\n    </nb-card-header>\r\n    <nb-card-body class=\"px-4 d-flex flex-column\"\r\n      style=\"height: calc(100% - 120px); overflow: hidden; padding-bottom: 0;\"> <!-- 自動綁定說明文案 -->\r\n      <div class=\"bg-blue-50 border border-blue-200 rounded-lg p-3 mb-4 flex-shrink-0\">\r\n        <div class=\"flex items-start gap-2\">\r\n          <i class=\"fas fa-info-circle text-blue-500 mt-1\"></i>\r\n          <div class=\"text-sm text-blue-700\">\r\n            <div class=\"font-medium mb-1\">建材代號</div>\r\n            <div class=\"mb-2\">\r\n              <span class=\"font-medium\">當前建材代號：</span>\r\n              <span class=\"bg-white px-2 py-1 rounded border\">{{ selectedMaterial.CImageCode || '未設定' }}</span>\r\n            </div>\r\n            <div>選擇圖片後，建材代號將會自動設定為所選圖片的檔名，並建立圖片與建材的綁定關係。</div>\r\n          </div>\r\n        </div>\r\n      </div> <!-- 類別選擇 -->\r\n      <div class=\"flex gap-3 mb-4 flex-shrink-0\">\r\n        <div class=\"w-48\">\r\n          <label class=\"text-sm font-medium text-gray-700 mb-2 block\">圖片類別</label>\r\n          <nb-select [(ngModel)]=\"selectedCategory\" (selectedChange)=\"categoryChanged($event)\" class=\"w-full\">\r\n            <nb-option *ngFor=\"let option of categoryOptions\" [value]=\"option.value\">\r\n              {{ option.label }}\r\n            </nb-option>\r\n          </nb-select>\r\n        </div>\r\n        <div class=\"w-48\">\r\n          <label class=\"text-sm font-medium text-gray-700 mb-2 block\">篩選範圍</label>\r\n          <nb-select [(ngModel)]=\"filterBindingOption\" (selectedChange)=\"filterImages()\" class=\"w-full\">\r\n            <nb-option *ngFor=\"let option of filterBindingOptions\" [value]=\"option.value\">\r\n              {{ option.label }}\r\n            </nb-option>\r\n          </nb-select>\r\n        </div>\r\n        <div class=\"flex-1\">\r\n          <label class=\"text-sm font-medium text-gray-700 mb-2 block\">搜尋圖片</label>\r\n          <input type=\"text\" class=\"w-full search-input\" placeholder=\"搜尋圖片名稱...\" [(ngModel)]=\"imageSearchTerm\"\r\n            (input)=\"filterImages()\" />\r\n        </div>\r\n        <div class=\"flex flex-col justify-end\">\r\n          <button class=\"btn btn-info btn-image-action\" (click)=\"loadImages()\">\r\n            重新載入 <i class=\"fas fa-refresh\"></i>\r\n          </button>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 批次操作按鈕 -->\r\n      <div class=\"flex gap-2 mb-3 flex-shrink-0\"> <button class=\"btn btn-outline-primary btn-sm\"\r\n          (click)=\"selectAllImages()\">\r\n          全選 <i class=\"fas fa-check-square\"></i>\r\n        </button>\r\n        <button class=\"btn btn-outline-secondary btn-sm\" (click)=\"clearAllSelection()\">\r\n          清除選取\r\n        </button>\r\n        <div class=\"ml-auto text-sm text-gray-600\">\r\n          已選取: {{ selectedImages.length }} 張圖片\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 圖片列表 -->\r\n      <div class=\"image-preview-container border rounded p-3 flex-1\">\r\n        <div class=\"grid grid-cols-4 gap-3\">\r\n          <div *ngFor=\"let image of filteredImages\" class=\"image-grid-item border rounded p-2 cursor-pointer\"\r\n            [class.selected]=\"isImageSelected(image)\" (click)=\"toggleImageSelection(image)\">\r\n\r\n            <!-- 選取指示器 -->\r\n            <div class=\"flex justify-between items-center mb-2\">\r\n              <div class=\"image-checkbox w-5 h-5 border-2 rounded flex items-center justify-center\"\r\n                [class.checked]=\"isImageSelected(image)\">\r\n                <i *ngIf=\"isImageSelected(image)\" class=\"fas fa-check text-white text-xs\"></i>\r\n              </div>\r\n              <!-- 判斷是否為已綁定的圖片 -->\r\n              <div\r\n                *ngIf=\"selectedMaterial && selectedMaterial.CSelectPictureId && selectedMaterial.CSelectPictureId.indexOf(image.id) > -1\"\r\n                class=\"badge badge-success text-xs px-2 py-1\" title=\"此圖片已經綁定到此建材\">\r\n                已綁定\r\n              </div>\r\n              <button class=\"btn btn-outline-info btn-xs btn-image-action\"\r\n                (click)=\"previewImage(image, imagePreview, $event)\">\r\n                <i class=\"fas fa-eye\"></i>\r\n              </button>\r\n            </div>\r\n\r\n            <!-- 圖片預覽 -->\r\n            <div class=\"w-full h-32 bg-gray-100 rounded mb-2 flex items-center justify-center overflow-hidden\"\r\n              [class.border-success]=\"selectedMaterial && selectedMaterial.CSelectPictureId && selectedMaterial.CSelectPictureId.indexOf(image.id) > -1\">\r\n              <img *ngIf=\"image.thumbnailUrl\" [src]=\"image.thumbnailUrl\" [alt]=\"image.name\"\r\n                class=\"image-thumbnail max-w-full max-h-full object-contain\" />\r\n              <div *ngIf=\"!image.thumbnailUrl\" class=\"text-gray-400 text-center\">\r\n                <i class=\"fas fa-image text-2xl mb-1\"></i>\r\n                <div class=\"text-xs\">無預覽</div>\r\n              </div>\r\n            </div>\r\n\r\n            <!-- 圖片資訊 -->\r\n            <div class=\"text-xs text-gray-600\">\r\n              <div class=\"font-medium truncate\" [title]=\"image.name\">{{ image.name }}</div>\r\n              <div class=\"text-gray-400\">{{ image.size | number }} KB</div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 空狀態 -->\r\n        <div *ngIf=\"filteredImages.length === 0\" class=\"text-center text-gray-500 py-20\">\r\n          <i class=\"fas fa-images text-4xl mb-3\"></i>\r\n          <div>找不到相符的圖片</div>\r\n        </div>\r\n      </div>\r\n    </nb-card-body>\r\n    <nb-card-footer class=\"d-flex justify-content-between align-items-center\">\r\n      <div class=\"text-sm text-gray-600\">\r\n        共 {{ imageTotalRecords }} 張圖片\r\n      </div>\r\n      <ngx-pagination [(CollectionSize)]=\"imageTotalRecords\" [(PageSize)]=\"imagePageSize\" [(Page)]=\"imageCurrentPage\"\r\n        (PageChange)=\"imagePageChanged($event)\">\r\n      </ngx-pagination>\r\n      <div class=\"d-flex gap-2\">\r\n        <button class=\"btn btn-danger btn-sm\" (click)=\"onCloseImageBinder(ref)\">取消</button>\r\n        <button class=\"btn btn-success btn-sm\" [disabled]=\"selectedImages.length === 0\"\r\n          (click)=\"onConfirmImageSelection(ref)\">\r\n          確定選擇 ({{ selectedImages.length }})\r\n        </button>\r\n      </div>\r\n    </nb-card-footer>\r\n  </nb-card>\r\n</ng-template>\r\n\r\n<!-- 圖片預覽對話框 -->\r\n<ng-template #imagePreview let-dialog let-ref=\"dialogRef\">\r\n  <nb-card class=\"w-[800px] h-[600px]\">\r\n    <nb-card-header class=\"d-flex justify-content-between align-items-center\">\r\n      <span>圖片預覽 - {{ previewingImage?.name }}</span>\r\n      <div class=\"d-flex gap-2\">\r\n        <button class=\"btn btn-outline-primary btn-sm\" [disabled]=\"currentPreviewIndex <= 0\" (click)=\"previousImage()\">\r\n          <i class=\"fas fa-chevron-left\"></i> 上一張\r\n        </button>\r\n        <button class=\"btn btn-outline-primary btn-sm\" [disabled]=\"currentPreviewIndex >= filteredImages.length - 1\"\r\n          (click)=\"nextImage()\">\r\n          下一張 <i class=\"fas fa-chevron-right\"></i>\r\n        </button>\r\n      </div>\r\n    </nb-card-header> <nb-card-body class=\"p-0 d-flex justify-content-center align-items-center\" style=\"height: 500px;\">\r\n      <img *ngIf=\"previewingImage && previewingImage.fullUrl\" [src]=\"previewingImage.fullUrl\"\r\n        [alt]=\"previewingImage.name\" class=\"max-w-full max-h-full object-contain\" />\r\n      <div *ngIf=\"!previewingImage || !previewingImage.fullUrl\" class=\"text-gray-400 text-center\">\r\n        <i class=\"fas fa-image text-4xl mb-3\"></i>\r\n        <div>圖片載入失敗</div>\r\n      </div>\r\n    </nb-card-body>\r\n    <nb-card-footer class=\"d-flex justify-content-between align-items-center\">\r\n      <div class=\"text-sm text-gray-600\">\r\n        {{ currentPreviewIndex + 1 }} / {{ filteredImages.length }}\r\n      </div>\r\n      <div class=\"d-flex gap-2\"> <button class=\"btn btn-outline-info btn-sm\" (click)=\"toggleImageSelectionInPreview()\">\r\n          {{ (previewingImage && isImageSelected(previewingImage)) ? '取消選取' : '選取此圖片' }}\r\n        </button>\r\n        <button class=\"btn btn-danger btn-sm\" (click)=\"ref.close()\">關閉</button>\r\n      </div>\r\n    </nb-card-footer>\r\n  </nb-card>\r\n</ng-template>\r\n\r\n<!-- 原有的圖片檢視對話框 -->\r\n<ng-template #dialogImage let-data let-ref=\"dialogRef\">\r\n  <nb-card style=\"height: 100%; overflow: auto; width: 700px;\" class=\"mr-md-5 ml-md-5\">\r\n    <nb-card-header>\r\n      <span>\r\n        檢視\r\n      </span>\r\n    </nb-card-header>\r\n    <nb-card-body style=\"padding:1rem 2rem\">\r\n      <div class=\"w-full h-auto\">\r\n        <img class=\"fit-size\" [src]=\"currentImageShowing\">\r\n      </div>\r\n    </nb-card-body>\r\n    <nb-card-footer>\r\n      <div class=\"flex justify-center items-center\">\r\n        <button class=\"btn btn-danger mr-2\" (click)=\"ref.close()\">取消</button>\r\n      </div>\r\n    </nb-card-footer>\r\n  </nb-card>\r\n</ng-template>\r\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAM9C,SAASC,QAAQ,EAAEC,QAAQ,EAAEC,GAAG,QAAQ,MAAM;AAG9C,OAAO,KAAKC,IAAI,MAAM,MAAM;AAC5B,SAASC,YAAY,QAAQ,gCAAgC;AAC7D,SAASC,aAAa,QAAQ,qCAAqC;AACnE,SAASC,UAAU,EAAEC,gBAAgB,QAAQ,gCAAgC;;;;;;;;;;;;;;ICDjEC,EAAA,CAAAC,cAAA,oBAA4E;IAC1ED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAFwCH,EAAA,CAAAI,UAAA,UAAAC,YAAA,CAAAC,GAAA,CAAuB;IACzEN,EAAA,CAAAO,SAAA,EACF;IADEP,EAAA,CAAAQ,kBAAA,MAAAH,YAAA,CAAAI,cAAA,MACF;;;;;;IAiCFT,EAAA,CAAAC,cAAA,iBAA8F;IAAnCD,EAAA,CAAAU,UAAA,mBAAAC,qEAAA;MAAAX,EAAA,CAAAY,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CAASF,MAAA,CAAAG,sBAAA,EAAwB;IAAA,EAAC;IAACjB,EAAA,CAAAE,MAAA,oBAAG;IAAAF,EAAA,CAAAkB,SAAA,YAC5D;IAAAlB,EAAA,CAAAG,YAAA,EAAS;;;;;;IAC9CH,EAAA,CAAAC,cAAA,iBAAoF;IAAnBD,EAAA,CAAAU,UAAA,mBAAAS,qEAAA;MAAAnB,EAAA,CAAAY,aAAA,CAAAQ,GAAA;MAAA,MAAAN,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CAASF,MAAA,CAAAO,MAAA,EAAQ;IAAA,EAAC;IACjFrB,EAAA,CAAAE,MAAA,qBAAG;IAAAF,EAAA,CAAAkB,SAAA,YAA6B;IAAAlB,EAAA,CAAAG,YAAA,EAAS;;;;;;IAC3CH,EAAA,CAAAC,cAAA,iBAAsF;IAAzBD,EAAA,CAAAU,UAAA,mBAAAY,qEAAA;MAAAtB,EAAA,CAAAY,aAAA,CAAAW,GAAA;MAAA,MAAAT,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,MAAAS,SAAA,GAAAxB,EAAA,CAAAyB,WAAA;MAAA,OAAAzB,EAAA,CAAAgB,WAAA,CAASF,MAAA,CAAAY,MAAA,CAAAF,SAAA,CAAc;IAAA,EAAC;IAACxB,EAAA,CAAAE,MAAA,gCAAK;IAAAF,EAAA,CAAAkB,SAAA,YAC/D;IAAAlB,EAAA,CAAAG,YAAA,EAAS;;;;;;IACrCH,EAAA,CAAAC,cAAA,iBAAoF;IAA5BD,EAAA,CAAAU,UAAA,mBAAAiB,qEAAA;MAAA3B,EAAA,CAAAY,aAAA,CAAAgB,GAAA;MAAA5B,EAAA,CAAAe,aAAA;MAAA,MAAAc,YAAA,GAAA7B,EAAA,CAAAyB,WAAA;MAAA,OAAAzB,EAAA,CAAAgB,WAAA,CAASa,YAAA,CAAAC,KAAA,EAAiB;IAAA,EAAC;IAAE9B,EAAA,CAAAE,MAAA,iCAAK;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;IAiBjGH,EAAA,CAAAC,cAAA,aAAwD;IAAAD,EAAA,CAAAE,MAAA,mBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;;IAa3DH,EAAA,CAAAC,cAAA,eACmC;IAAAD,EAAA,CAAAE,MAAA,GAAkC;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAAzCH,EAAA,CAAAO,SAAA,EAAkC;IAAlCP,EAAA,CAAA+B,iBAAA,CAAAC,QAAA,CAAAC,gBAAA,CAAAC,MAAA,CAAkC;;;;;IACrElC,EAAA,CAAAC,cAAA,eACkC;IAAAD,EAAA,CAAAE,MAAA,QAAC;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAI9CH,EAAA,CAAAC,cAAA,SAAoC;IAAAD,EAAA,CAAAE,MAAA,GAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;IAArBH,EAAA,CAAAO,SAAA,EAAgB;IAAhBP,EAAA,CAAA+B,iBAAA,CAAAC,QAAA,CAAAG,MAAA,CAAgB;;;;;;IAElDnC,EAAA,CAAAC,cAAA,iBACiB;IADkCD,EAAA,CAAAU,UAAA,mBAAA0B,mFAAA;MAAApC,EAAA,CAAAY,aAAA,CAAAyB,IAAA;MAAA,MAAAL,QAAA,GAAAhC,EAAA,CAAAe,aAAA,GAAAuB,SAAA;MAAA,MAAAxB,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,MAAAS,SAAA,GAAAxB,EAAA,CAAAyB,WAAA;MAAA,OAAAzB,EAAA,CAAAgB,WAAA,CAASF,MAAA,CAAAyB,kBAAA,CAAAP,QAAA,EAAAR,SAAA,CAAgC;IAAA,EAAC;IAC5ExB,EAAA,CAAAE,MAAA,mBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;IAACH,EAAA,CAAAC,cAAA,iBAC4E;IAAvGD,EAAA,CAAAU,UAAA,mBAAA8B,mFAAA;MAAAxC,EAAA,CAAAY,aAAA,CAAA6B,IAAA;MAAA,MAAAT,QAAA,GAAAhC,EAAA,CAAAe,aAAA,GAAAuB,SAAA;MAAA,MAAAxB,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,MAAA2B,eAAA,GAAA1C,EAAA,CAAAyB,WAAA;MAAA,OAAAzB,EAAA,CAAAgB,WAAA,CAASF,MAAA,CAAA6B,oBAAA,CAAAX,QAAA,EAAAU,eAAA,CAAuC;IAAA,EAAC;IACjD1C,EAAA,CAAAkB,SAAA,YAA6B;IAAClB,EAAA,CAAAE,MAAA,qBAChC;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IAF0DH,EAAA,CAAAI,UAAA,sBAAA4B,QAAA,CAAAY,KAAA,+BAAqC;;;;;IAlB1G5C,EADF,CAAAC,cAAA,SAAsD,SAChD;IAAAD,EAAA,CAAAE,MAAA,GAAa;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACtBH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,GAA8B;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACvCH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,GAA0D;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACnEH,EAAA,CAAAC,cAAA,SAAmD;IAAAD,EAAA,CAAAE,MAAA,GAAqB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC7EH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,IAAsB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAE7BH,EADF,CAAAC,cAAA,UAAI,eACqC;IAGrCD,EAFA,CAAA6C,UAAA,KAAAC,wDAAA,mBACmC,KAAAC,wDAAA,mBAED;IAClC/C,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAE,MAAA,IAAiF;IAE3FF,EAF2F,CAAAG,YAAA,EAAO,EAC1F,EACH;IACLH,EAAA,CAAA6C,UAAA,KAAAG,sDAAA,iBAAoC;IACpChD,EAAA,CAAAC,cAAA,cAA6B;IAEED,EAD7B,CAAA6C,UAAA,KAAAI,0DAAA,qBACiB,KAAAC,0DAAA,qBACwF;IAI7GlD,EADE,CAAAG,YAAA,EAAK,EACF;;;;;IAtBCH,EAAA,CAAAO,SAAA,GAAa;IAAbP,EAAA,CAAA+B,iBAAA,CAAAC,QAAA,CAAAmB,GAAA,CAAa;IACbnD,EAAA,CAAAO,SAAA,GAA8B;IAA9BP,EAAA,CAAA+B,iBAAA,CAAAC,QAAA,CAAAoB,UAAA,yBAA8B;IAC9BpD,EAAA,CAAAO,SAAA,GAA0D;IAA1DP,EAAA,CAAAqD,kBAAA,KAAArB,QAAA,CAAAY,KAAA,SAAAZ,QAAA,CAAAsB,KAAA,SAAAtB,QAAA,CAAAuB,SAAA,KAA0D;IAC1DvD,EAAA,CAAAO,SAAA,EAA8C;IAA9CP,EAAA,CAAAwD,UAAA,EAAAxB,QAAA,CAAAyB,UAAA,qBAA8C;IAACzD,EAAA,CAAAO,SAAA,EAAqB;IAArBP,EAAA,CAAA+B,iBAAA,CAAAC,QAAA,CAAA0B,WAAA,CAAqB;IACpE1D,EAAA,CAAAO,SAAA,GAAsB;IAAtBP,EAAA,CAAA+B,iBAAA,CAAAC,QAAA,CAAA2B,YAAA,CAAsB;IAGf3D,EAAA,CAAAO,SAAA,GAA+D;IAA/DP,EAAA,CAAAI,UAAA,SAAA4B,QAAA,CAAAC,gBAAA,IAAAD,QAAA,CAAAC,gBAAA,CAAAC,MAAA,KAA+D;IAE/DlC,EAAA,CAAAO,SAAA,EAAkE;IAAlEP,EAAA,CAAAI,UAAA,UAAA4B,QAAA,CAAAC,gBAAA,IAAAD,QAAA,CAAAC,gBAAA,CAAAC,MAAA,OAAkE;IAEnElC,EAAA,CAAAO,SAAA,GAAiF;IAAjFP,EAAA,CAAA+B,iBAAA,CAAAC,QAAA,CAAAC,gBAAA,IAAAD,QAAA,CAAAC,gBAAA,CAAAC,MAAA,mDAAiF;IAGtFlC,EAAA,CAAAO,SAAA,EAA6B;IAA7BP,EAAA,CAAAI,UAAA,SAAA4B,QAAA,CAAA4B,UAAA,SAA6B;IAG7B5D,EAAA,CAAAO,SAAA,GAAY;IAAZP,EAAA,CAAAI,UAAA,SAAAU,MAAA,CAAA+C,MAAA,CAAY;IACsC7D,EAAA,CAAAO,SAAA,EAAY;IAAZP,EAAA,CAAAI,UAAA,SAAAU,MAAA,CAAA+C,MAAA,CAAY;;;;;IApBvE7D,EAAA,CAAAC,cAAA,YAA+D;IAC7DD,EAAA,CAAA6C,UAAA,IAAAiB,gDAAA,mBAAsD;IAwBxD9D,EAAA,CAAAG,YAAA,EAAQ;;;;IAxBeH,EAAA,CAAAO,SAAA,EAAkB;IAAlBP,EAAA,CAAAI,UAAA,YAAAU,MAAA,CAAAiD,YAAA,CAAkB;;;;;IA4FrC/D,EAAA,CAAAC,cAAA,cAAyF;IACvFD,EAAA,CAAAkB,SAAA,YAAuD;IACvDlB,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADJH,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAAQ,kBAAA,kDAAAM,MAAA,CAAAkD,gBAAA,CAAAZ,UAAA,MACF;;;;;;IA1DRpD,EADF,CAAAC,cAAA,kBAA2B,qBACT;IACdD,EAAA,CAAAE,MAAA,4DACF;IAAAF,EAAA,CAAAG,YAAA,EAAiB;IAEfH,EADF,CAAAC,cAAA,uBAA2B,aACH;IAAAD,EAAA,CAAAE,MAAA,+EAAY;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAInCH,EAHJ,CAAAC,cAAA,cAAyB,cAEQ,gBACW;IAAAD,EAAA,CAAAE,MAAA,+BAAI;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACpDH,EAAA,CAAAC,cAAA,iBAC8C;IAA5CD,EAAA,CAAAiE,gBAAA,2BAAAC,kFAAAC,MAAA;MAAAnE,EAAA,CAAAY,aAAA,CAAAwD,IAAA;MAAA,MAAAtD,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAAf,EAAA,CAAAqE,kBAAA,CAAAvD,MAAA,CAAAkD,gBAAA,CAAAZ,UAAA,EAAAe,MAAA,MAAArD,MAAA,CAAAkD,gBAAA,CAAAZ,UAAA,GAAAe,MAAA;MAAA,OAAAnE,EAAA,CAAAgB,WAAA,CAAAmD,MAAA;IAAA,EAAyC;IAC7CnE,EAFE,CAAAG,YAAA,EAC8C,EAC1C;IAGJH,EADF,CAAAC,cAAA,eAAoC,iBACM;IAAAD,EAAA,CAAAE,MAAA,oBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAClDH,EAAA,CAAAC,cAAA,iBACyC;IAAvCD,EAAA,CAAAiE,gBAAA,2BAAAK,kFAAAH,MAAA;MAAAnE,EAAA,CAAAY,aAAA,CAAAwD,IAAA;MAAA,MAAAtD,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAAf,EAAA,CAAAqE,kBAAA,CAAAvD,MAAA,CAAAkD,gBAAA,CAAApB,KAAA,EAAAuB,MAAA,MAAArD,MAAA,CAAAkD,gBAAA,CAAApB,KAAA,GAAAuB,MAAA;MAAA,OAAAnE,EAAA,CAAAgB,WAAA,CAAAmD,MAAA;IAAA,EAAoC;IACxCnE,EAFE,CAAAG,YAAA,EACyC,EACrC;IAGJH,EADF,CAAAC,cAAA,eAAoC,iBACM;IAAAD,EAAA,CAAAE,MAAA,oBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAClDH,EAAA,CAAAC,cAAA,iBACyC;IAAvCD,EAAA,CAAAiE,gBAAA,2BAAAM,kFAAAJ,MAAA;MAAAnE,EAAA,CAAAY,aAAA,CAAAwD,IAAA;MAAA,MAAAtD,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAAf,EAAA,CAAAqE,kBAAA,CAAAvD,MAAA,CAAAkD,gBAAA,CAAAV,KAAA,EAAAa,MAAA,MAAArD,MAAA,CAAAkD,gBAAA,CAAAV,KAAA,GAAAa,MAAA;MAAA,OAAAnE,EAAA,CAAAgB,WAAA,CAAAmD,MAAA;IAAA,EAAoC;IACxCnE,EAFE,CAAAG,YAAA,EACyC,EACrC;IAEJH,EADF,CAAAC,cAAA,eAAoC,iBACM;IAAAD,EAAA,CAAAE,MAAA,oBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAClDH,EAAA,CAAAC,cAAA,iBAC6C;IAA3CD,EAAA,CAAAiE,gBAAA,2BAAAO,kFAAAL,MAAA;MAAAnE,EAAA,CAAAY,aAAA,CAAAwD,IAAA;MAAA,MAAAtD,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAAf,EAAA,CAAAqE,kBAAA,CAAAvD,MAAA,CAAAkD,gBAAA,CAAAT,SAAA,EAAAY,MAAA,MAAArD,MAAA,CAAAkD,gBAAA,CAAAT,SAAA,GAAAY,MAAA;MAAA,OAAAnE,EAAA,CAAAgB,WAAA,CAAAmD,MAAA;IAAA,EAAwC;IAC5CnE,EAFE,CAAAG,YAAA,EAC6C,EACzC;IAGJH,EADF,CAAAC,cAAA,eAAoC,iBACM;IAAAD,EAAA,CAAAE,MAAA,4CAAM;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACtDH,EAAA,CAAAC,cAAA,iBAC+C;IAA7CD,EAAA,CAAAiE,gBAAA,2BAAAQ,kFAAAN,MAAA;MAAAnE,EAAA,CAAAY,aAAA,CAAAwD,IAAA;MAAA,MAAAtD,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAAf,EAAA,CAAAqE,kBAAA,CAAAvD,MAAA,CAAAkD,gBAAA,CAAAN,WAAA,EAAAS,MAAA,MAAArD,MAAA,CAAAkD,gBAAA,CAAAN,WAAA,GAAAS,MAAA;MAAA,OAAAnE,EAAA,CAAAgB,WAAA,CAAAmD,MAAA;IAAA,EAA0C;IAC9CnE,EAFE,CAAAG,YAAA,EAC+C,EAC3C;IAGJH,EADF,CAAAC,cAAA,eAAoC,iBACT;IAAAD,EAAA,CAAAE,MAAA,gCAAI;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACrCH,EAAA,CAAAC,cAAA,oBACiE;IAD/CD,EAAA,CAAAiE,gBAAA,2BAAAS,qFAAAP,MAAA;MAAAnE,EAAA,CAAAY,aAAA,CAAAwD,IAAA;MAAA,MAAAtD,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAAf,EAAA,CAAAqE,kBAAA,CAAAvD,MAAA,CAAAkD,gBAAA,CAAAL,YAAA,EAAAQ,MAAA,MAAArD,MAAA,CAAAkD,gBAAA,CAAAL,YAAA,GAAAQ,MAAA;MAAA,OAAAnE,EAAA,CAAAgB,WAAA,CAAAmD,MAAA;IAAA,EAA2C;IAE/DnE,EADmE,CAAAG,YAAA,EAAW,EACxE;IAEJH,EADF,CAAAC,cAAA,eAAoC,iBACT;IAAAD,EAAA,CAAAE,MAAA,oBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACnCH,EAAA,CAAAC,cAAA,iBAC0C;IAAxCD,EAAA,CAAAiE,gBAAA,2BAAAU,kFAAAR,MAAA;MAAAnE,EAAA,CAAAY,aAAA,CAAAwD,IAAA;MAAA,MAAAtD,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAAf,EAAA,CAAAqE,kBAAA,CAAAvD,MAAA,CAAAkD,gBAAA,CAAA7B,MAAA,EAAAgC,MAAA,MAAArD,MAAA,CAAAkD,gBAAA,CAAA7B,MAAA,GAAAgC,MAAA;MAAA,OAAAnE,EAAA,CAAAgB,WAAA,CAAAmD,MAAA;IAAA,EAAqC;IACzCnE,EAFE,CAAAG,YAAA,EAC0C,EACtC;IAIJH,EADF,CAAAC,cAAA,eAAkE,iBACvC;IAAAD,EAAA,CAAAE,MAAA,gCAAI;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAEnCH,EADF,CAAAC,cAAA,eAA+B,kBAEP;IADoCD,EAAA,CAAAU,UAAA,mBAAAkE,2EAAA;MAAA5E,EAAA,CAAAY,aAAA,CAAAwD,IAAA;MAAA,MAAAtD,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,MAAA2B,eAAA,GAAA1C,EAAA,CAAAyB,WAAA;MAAA,OAAAzB,EAAA,CAAAgB,WAAA,CAASF,MAAA,CAAA+D,eAAA,CAAAnC,eAAA,CAA4B;IAAA,EAAC;IAE9F1C,EAAA,CAAAkB,SAAA,aAAkC;IAAAlB,EAAA,CAAAE,MAAA,iCACpC;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAA6C,UAAA,KAAAiC,wDAAA,kBAAyF;IAOjG9E,EAHM,CAAAG,YAAA,EAAM,EACF,EACF,EACO;IAEbH,EADF,CAAAC,cAAA,0BAAsD,kBACc;IAAvBD,EAAA,CAAAU,UAAA,mBAAAqE,2EAAA;MAAA,MAAAC,OAAA,GAAAhF,EAAA,CAAAY,aAAA,CAAAwD,IAAA,EAAAa,SAAA;MAAA,MAAAnE,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CAASF,MAAA,CAAAoE,OAAA,CAAAF,OAAA,CAAY;IAAA,EAAC;IAAChF,EAAA,CAAAE,MAAA,oBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAC7EH,EAAA,CAAAC,cAAA,kBAA+D;IAAxBD,EAAA,CAAAU,UAAA,mBAAAyE,2EAAA;MAAA,MAAAH,OAAA,GAAAhF,EAAA,CAAAY,aAAA,CAAAwD,IAAA,EAAAa,SAAA;MAAA,MAAAnE,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CAASF,MAAA,CAAAsE,QAAA,CAAAJ,OAAA,CAAa;IAAA,EAAC;IAAChF,EAAA,CAAAE,MAAA,oBAAE;IAErEF,EAFqE,CAAAG,YAAA,EAAS,EAC3D,EACT;;;;IAzDAH,EAAA,CAAAO,SAAA,IAAyC;IAAzCP,EAAA,CAAAqF,gBAAA,YAAAvE,MAAA,CAAAkD,gBAAA,CAAAZ,UAAA,CAAyC;IAMzCpD,EAAA,CAAAO,SAAA,GAAoC;IAApCP,EAAA,CAAAqF,gBAAA,YAAAvE,MAAA,CAAAkD,gBAAA,CAAApB,KAAA,CAAoC;IAMpC5C,EAAA,CAAAO,SAAA,GAAoC;IAApCP,EAAA,CAAAqF,gBAAA,YAAAvE,MAAA,CAAAkD,gBAAA,CAAAV,KAAA,CAAoC;IAKpCtD,EAAA,CAAAO,SAAA,GAAwC;IAAxCP,EAAA,CAAAqF,gBAAA,YAAAvE,MAAA,CAAAkD,gBAAA,CAAAT,SAAA,CAAwC;IAMxCvD,EAAA,CAAAO,SAAA,GAA0C;IAA1CP,EAAA,CAAAqF,gBAAA,YAAAvE,MAAA,CAAAkD,gBAAA,CAAAN,WAAA,CAA0C;IAK1B1D,EAAA,CAAAO,SAAA,GAA2C;IAA3CP,EAAA,CAAAqF,gBAAA,YAAAvE,MAAA,CAAAkD,gBAAA,CAAAL,YAAA,CAA2C;IAAC3D,EAAA,CAAAI,UAAA,WAAU;IAMtEJ,EAAA,CAAAO,SAAA,GAAqC;IAArCP,EAAA,CAAAqF,gBAAA,YAAAvE,MAAA,CAAAkD,gBAAA,CAAA7B,MAAA,CAAqC;IAQnCnC,EAAA,CAAAO,SAAA,GAAmB;IAAnBP,EAAA,CAAAI,UAAA,uDAAmB;IAGiCJ,EAAA,CAAAO,SAAA,GAAiC;IAAjCP,EAAA,CAAAI,UAAA,SAAAU,MAAA,CAAAkD,gBAAA,CAAAZ,UAAA,CAAiC;;;;;IAuCvFpD,EAAA,CAAAC,cAAA,oBAAyE;IACvED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAFsCH,EAAA,CAAAI,UAAA,UAAAkF,UAAA,CAAAC,KAAA,CAAsB;IACtEvF,EAAA,CAAAO,SAAA,EACF;IADEP,EAAA,CAAAQ,kBAAA,MAAA8E,UAAA,CAAAE,KAAA,MACF;;;;;IAMAxF,EAAA,CAAAC,cAAA,oBAA8E;IAC5ED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAF2CH,EAAA,CAAAI,UAAA,UAAAqF,UAAA,CAAAF,KAAA,CAAsB;IAC3EvF,EAAA,CAAAO,SAAA,EACF;IADEP,EAAA,CAAAQ,kBAAA,MAAAiF,UAAA,CAAAD,KAAA,MACF;;;;;IAsCIxF,EAAA,CAAAkB,SAAA,aAA8E;;;;;IAGhFlB,EAAA,CAAAC,cAAA,eAEoE;IAClED,EAAA,CAAAE,MAAA,2BACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAUNH,EAAA,CAAAkB,SAAA,eACiE;;;;IADNlB,EAA3B,CAAAI,UAAA,QAAAsF,SAAA,CAAAC,YAAA,EAAA3F,EAAA,CAAA4F,aAAA,CAA0B,QAAAF,SAAA,CAAAG,IAAA,CAAmB;;;;;IAE7E7F,EAAA,CAAAC,cAAA,eAAmE;IACjED,EAAA,CAAAkB,SAAA,aAA0C;IAC1ClB,EAAA,CAAAC,cAAA,eAAqB;IAAAD,EAAA,CAAAE,MAAA,yBAAG;IAC1BF,EAD0B,CAAAG,YAAA,EAAM,EAC1B;;;;;;IA7BVH,EAAA,CAAAC,cAAA,eACkF;IAAtCD,EAAA,CAAAU,UAAA,mBAAAoF,8EAAA;MAAA,MAAAJ,SAAA,GAAA1F,EAAA,CAAAY,aAAA,CAAAmF,IAAA,EAAAzD,SAAA;MAAA,MAAAxB,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CAASF,MAAA,CAAAkF,oBAAA,CAAAN,SAAA,CAA2B;IAAA,EAAC;IAI7E1F,EADF,CAAAC,cAAA,eAAoD,eAEP;IACzCD,EAAA,CAAA6C,UAAA,IAAAoD,4DAAA,iBAA0E;IAC5EjG,EAAA,CAAAG,YAAA,EAAM;IAENH,EAAA,CAAA6C,UAAA,IAAAqD,8DAAA,mBAEoE;IAGpElG,EAAA,CAAAC,cAAA,kBACsD;IAApDD,EAAA,CAAAU,UAAA,mBAAAyF,iFAAAhC,MAAA;MAAA,MAAAuB,SAAA,GAAA1F,EAAA,CAAAY,aAAA,CAAAmF,IAAA,EAAAzD,SAAA;MAAA,MAAAxB,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,MAAAqF,gBAAA,GAAApG,EAAA,CAAAyB,WAAA;MAAA,OAAAzB,EAAA,CAAAgB,WAAA,CAASF,MAAA,CAAAuF,YAAA,CAAAX,SAAA,EAAAU,gBAAA,EAAAjC,MAAA,CAAyC;IAAA,EAAC;IACnDnE,EAAA,CAAAkB,SAAA,aAA0B;IAE9BlB,EADE,CAAAG,YAAA,EAAS,EACL;IAGNH,EAAA,CAAAC,cAAA,eAC6I;IAG3ID,EAFA,CAAA6C,UAAA,IAAAyD,8DAAA,mBACiE,IAAAC,8DAAA,mBACE;IAIrEvG,EAAA,CAAAG,YAAA,EAAM;IAIJH,EADF,CAAAC,cAAA,gBAAmC,gBACsB;IAAAD,EAAA,CAAAE,MAAA,IAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAC7EH,EAAA,CAAAC,cAAA,gBAA2B;IAAAD,EAAA,CAAAE,MAAA,IAA4B;;IAE3DF,EAF2D,CAAAG,YAAA,EAAM,EACzD,EACF;;;;;IApCJH,EAAA,CAAAwG,WAAA,aAAA1F,MAAA,CAAA2F,eAAA,CAAAf,SAAA,EAAyC;IAKrC1F,EAAA,CAAAO,SAAA,GAAwC;IAAxCP,EAAA,CAAAwG,WAAA,YAAA1F,MAAA,CAAA2F,eAAA,CAAAf,SAAA,EAAwC;IACpC1F,EAAA,CAAAO,SAAA,EAA4B;IAA5BP,EAAA,CAAAI,UAAA,SAAAU,MAAA,CAAA2F,eAAA,CAAAf,SAAA,EAA4B;IAI/B1F,EAAA,CAAAO,SAAA,EAAuH;IAAvHP,EAAA,CAAAI,UAAA,SAAAU,MAAA,CAAAkD,gBAAA,IAAAlD,MAAA,CAAAkD,gBAAA,CAAA/B,gBAAA,IAAAnB,MAAA,CAAAkD,gBAAA,CAAA/B,gBAAA,CAAAyE,OAAA,CAAAhB,SAAA,CAAAiB,EAAA,OAAuH;IAY1H3G,EAAA,CAAAO,SAAA,GAA0I;IAA1IP,EAAA,CAAAwG,WAAA,mBAAA1F,MAAA,CAAAkD,gBAAA,IAAAlD,MAAA,CAAAkD,gBAAA,CAAA/B,gBAAA,IAAAnB,MAAA,CAAAkD,gBAAA,CAAA/B,gBAAA,CAAAyE,OAAA,CAAAhB,SAAA,CAAAiB,EAAA,OAA0I;IACpI3G,EAAA,CAAAO,SAAA,EAAwB;IAAxBP,EAAA,CAAAI,UAAA,SAAAsF,SAAA,CAAAC,YAAA,CAAwB;IAExB3F,EAAA,CAAAO,SAAA,EAAyB;IAAzBP,EAAA,CAAAI,UAAA,UAAAsF,SAAA,CAAAC,YAAA,CAAyB;IAQG3F,EAAA,CAAAO,SAAA,GAAoB;IAApBP,EAAA,CAAAI,UAAA,UAAAsF,SAAA,CAAAG,IAAA,CAAoB;IAAC7F,EAAA,CAAAO,SAAA,EAAgB;IAAhBP,EAAA,CAAA+B,iBAAA,CAAA2D,SAAA,CAAAG,IAAA,CAAgB;IAC5C7F,EAAA,CAAAO,SAAA,GAA4B;IAA5BP,EAAA,CAAAQ,kBAAA,KAAAR,EAAA,CAAA4G,WAAA,SAAAlB,SAAA,CAAAmB,IAAA,SAA4B;;;;;IAM7D7G,EAAA,CAAAC,cAAA,eAAiF;IAC/ED,EAAA,CAAAkB,SAAA,aAA2C;IAC3ClB,EAAA,CAAAC,cAAA,UAAK;IAAAD,EAAA,CAAAE,MAAA,uDAAQ;IACfF,EADe,CAAAG,YAAA,EAAM,EACf;;;;;;IA3GVH,EADF,CAAAC,cAAA,kBAAkD,qBAChC;IACdD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAiB;IAIbH,EAHJ,CAAAC,cAAA,uBAC2E,cACQ,cAC3C;IAClCD,EAAA,CAAAkB,SAAA,YAAqD;IAEnDlB,EADF,CAAAC,cAAA,cAAmC,cACH;IAAAD,EAAA,CAAAE,MAAA,+BAAI;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAEtCH,EADF,CAAAC,cAAA,eAAkB,gBACU;IAAAD,EAAA,CAAAE,MAAA,kDAAO;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACxCH,EAAA,CAAAC,cAAA,gBAAgD;IAAAD,EAAA,CAAAE,MAAA,IAA0C;IAC5FF,EAD4F,CAAAG,YAAA,EAAO,EAC7F;IACNH,EAAA,CAAAC,cAAA,WAAK;IAAAD,EAAA,CAAAE,MAAA,kPAAuC;IAGlDF,EAHkD,CAAAG,YAAA,EAAM,EAC9C,EACF,EACF;IAGFH,EAFJ,CAAAC,cAAA,eAA2C,eACvB,iBAC4C;IAAAD,EAAA,CAAAE,MAAA,gCAAI;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACxEH,EAAA,CAAAC,cAAA,qBAAoG;IAAzFD,EAAA,CAAAiE,gBAAA,2BAAA6C,sFAAA3C,MAAA;MAAAnE,EAAA,CAAAY,aAAA,CAAAmG,IAAA;MAAA,MAAAjG,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAAf,EAAA,CAAAqE,kBAAA,CAAAvD,MAAA,CAAAkG,gBAAA,EAAA7C,MAAA,MAAArD,MAAA,CAAAkG,gBAAA,GAAA7C,MAAA;MAAA,OAAAnE,EAAA,CAAAgB,WAAA,CAAAmD,MAAA;IAAA,EAA8B;IAACnE,EAAA,CAAAU,UAAA,4BAAAuG,uFAAA9C,MAAA;MAAAnE,EAAA,CAAAY,aAAA,CAAAmG,IAAA;MAAA,MAAAjG,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CAAkBF,MAAA,CAAAoG,eAAA,CAAA/C,MAAA,CAAuB;IAAA,EAAC;IAClFnE,EAAA,CAAA6C,UAAA,KAAAsE,8DAAA,wBAAyE;IAI7EnH,EADE,CAAAG,YAAA,EAAY,EACR;IAEJH,EADF,CAAAC,cAAA,eAAkB,iBAC4C;IAAAD,EAAA,CAAAE,MAAA,gCAAI;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACxEH,EAAA,CAAAC,cAAA,qBAA8F;IAAnFD,EAAA,CAAAiE,gBAAA,2BAAAmD,sFAAAjD,MAAA;MAAAnE,EAAA,CAAAY,aAAA,CAAAmG,IAAA;MAAA,MAAAjG,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAAf,EAAA,CAAAqE,kBAAA,CAAAvD,MAAA,CAAAuG,mBAAA,EAAAlD,MAAA,MAAArD,MAAA,CAAAuG,mBAAA,GAAAlD,MAAA;MAAA,OAAAnE,EAAA,CAAAgB,WAAA,CAAAmD,MAAA;IAAA,EAAiC;IAACnE,EAAA,CAAAU,UAAA,4BAAA4G,uFAAA;MAAAtH,EAAA,CAAAY,aAAA,CAAAmG,IAAA;MAAA,MAAAjG,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CAAkBF,MAAA,CAAAyG,YAAA,EAAc;IAAA,EAAC;IAC5EvH,EAAA,CAAA6C,UAAA,KAAA2E,8DAAA,wBAA8E;IAIlFxH,EADE,CAAAG,YAAA,EAAY,EACR;IAEJH,EADF,CAAAC,cAAA,eAAoB,iBAC0C;IAAAD,EAAA,CAAAE,MAAA,gCAAI;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACxEH,EAAA,CAAAC,cAAA,iBAC6B;IAD0CD,EAAA,CAAAiE,gBAAA,2BAAAwD,kFAAAtD,MAAA;MAAAnE,EAAA,CAAAY,aAAA,CAAAmG,IAAA;MAAA,MAAAjG,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAAf,EAAA,CAAAqE,kBAAA,CAAAvD,MAAA,CAAA4G,eAAA,EAAAvD,MAAA,MAAArD,MAAA,CAAA4G,eAAA,GAAAvD,MAAA;MAAA,OAAAnE,EAAA,CAAAgB,WAAA,CAAAmD,MAAA;IAAA,EAA6B;IAClGnE,EAAA,CAAAU,UAAA,mBAAAiH,0EAAA;MAAA3H,EAAA,CAAAY,aAAA,CAAAmG,IAAA;MAAA,MAAAjG,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CAASF,MAAA,CAAAyG,YAAA,EAAc;IAAA,EAAC;IAC5BvH,EAFE,CAAAG,YAAA,EAC6B,EACzB;IAEJH,EADF,CAAAC,cAAA,eAAuC,kBACgC;IAAvBD,EAAA,CAAAU,UAAA,mBAAAkH,2EAAA;MAAA5H,EAAA,CAAAY,aAAA,CAAAmG,IAAA;MAAA,MAAAjG,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CAASF,MAAA,CAAA+G,UAAA,EAAY;IAAA,EAAC;IAClE7H,EAAA,CAAAE,MAAA,kCAAK;IAAAF,EAAA,CAAAkB,SAAA,aAA8B;IAGzClB,EAFI,CAAAG,YAAA,EAAS,EACL,EACF;IAGsCH,EAA5C,CAAAC,cAAA,eAA2C,kBACX;IAA5BD,EAAA,CAAAU,UAAA,mBAAAoH,2EAAA;MAAA9H,EAAA,CAAAY,aAAA,CAAAmG,IAAA;MAAA,MAAAjG,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CAASF,MAAA,CAAAiH,eAAA,EAAiB;IAAA,EAAC;IAC3B/H,EAAA,CAAAE,MAAA,sBAAG;IAAAF,EAAA,CAAAkB,SAAA,aAAmC;IACxClB,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,kBAA+E;IAA9BD,EAAA,CAAAU,UAAA,mBAAAsH,2EAAA;MAAAhI,EAAA,CAAAY,aAAA,CAAAmG,IAAA;MAAA,MAAAjG,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CAASF,MAAA,CAAAmH,iBAAA,EAAmB;IAAA,EAAC;IAC5EjI,EAAA,CAAAE,MAAA,kCACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,gBAA2C;IACzCD,EAAA,CAAAE,MAAA,IACF;IACFF,EADE,CAAAG,YAAA,EAAM,EACF;IAIJH,EADF,CAAAC,cAAA,gBAA+D,gBACzB;IAClCD,EAAA,CAAA6C,UAAA,KAAAqF,wDAAA,qBACkF;IAqCpFlI,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAA6C,UAAA,KAAAsF,wDAAA,mBAAiF;IAKrFnI,EADE,CAAAG,YAAA,EAAM,EACO;IAEbH,EADF,CAAAC,cAAA,2BAA0E,gBACrC;IACjCD,EAAA,CAAAE,MAAA,IACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,0BAC0C;IAD0CD,EAApE,CAAAiE,gBAAA,kCAAAmE,kGAAAjE,MAAA;MAAAnE,EAAA,CAAAY,aAAA,CAAAmG,IAAA;MAAA,MAAAjG,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAAf,EAAA,CAAAqE,kBAAA,CAAAvD,MAAA,CAAAuH,iBAAA,EAAAlE,MAAA,MAAArD,MAAA,CAAAuH,iBAAA,GAAAlE,MAAA;MAAA,OAAAnE,EAAA,CAAAgB,WAAA,CAAAmD,MAAA;IAAA,EAAsC,4BAAAmE,4FAAAnE,MAAA;MAAAnE,EAAA,CAAAY,aAAA,CAAAmG,IAAA;MAAA,MAAAjG,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAAf,EAAA,CAAAqE,kBAAA,CAAAvD,MAAA,CAAAyH,aAAA,EAAApE,MAAA,MAAArD,MAAA,CAAAyH,aAAA,GAAApE,MAAA;MAAA,OAAAnE,EAAA,CAAAgB,WAAA,CAAAmD,MAAA;IAAA,EAA6B,wBAAAqE,wFAAArE,MAAA;MAAAnE,EAAA,CAAAY,aAAA,CAAAmG,IAAA;MAAA,MAAAjG,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAAf,EAAA,CAAAqE,kBAAA,CAAAvD,MAAA,CAAA2H,gBAAA,EAAAtE,MAAA,MAAArD,MAAA,CAAA2H,gBAAA,GAAAtE,MAAA;MAAA,OAAAnE,EAAA,CAAAgB,WAAA,CAAAmD,MAAA;IAAA,EAA4B;IAC7GnE,EAAA,CAAAU,UAAA,wBAAA8H,wFAAArE,MAAA;MAAAnE,EAAA,CAAAY,aAAA,CAAAmG,IAAA;MAAA,MAAAjG,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CAAcF,MAAA,CAAA4H,gBAAA,CAAAvE,MAAA,CAAwB;IAAA,EAAC;IACzCnE,EAAA,CAAAG,YAAA,EAAiB;IAEfH,EADF,CAAAC,cAAA,gBAA0B,mBACgD;IAAlCD,EAAA,CAAAU,UAAA,mBAAAiI,2EAAA;MAAA,MAAAC,OAAA,GAAA5I,EAAA,CAAAY,aAAA,CAAAmG,IAAA,EAAA9B,SAAA;MAAA,MAAAnE,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CAASF,MAAA,CAAA+H,kBAAA,CAAAD,OAAA,CAAuB;IAAA,EAAC;IAAC5I,EAAA,CAAAE,MAAA,oBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACnFH,EAAA,CAAAC,cAAA,mBACyC;IAAvCD,EAAA,CAAAU,UAAA,mBAAAoI,2EAAA;MAAA,MAAAF,OAAA,GAAA5I,EAAA,CAAAY,aAAA,CAAAmG,IAAA,EAAA9B,SAAA;MAAA,MAAAnE,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CAASF,MAAA,CAAAiI,uBAAA,CAAAH,OAAA,CAA4B;IAAA,EAAC;IACtC5I,EAAA,CAAAE,MAAA,IACF;IAGNF,EAHM,CAAAG,YAAA,EAAS,EACL,EACS,EACT;;;;IA5HNH,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAAQ,kBAAA,iCAAAM,MAAA,CAAAkD,gBAAA,CAAApB,KAAA,eAAA9B,MAAA,CAAAkD,gBAAA,CAAApB,KAAA,yFACF;IAU0D5C,EAAA,CAAAO,SAAA,IAA0C;IAA1CP,EAAA,CAAA+B,iBAAA,CAAAjB,MAAA,CAAAkD,gBAAA,CAAAZ,UAAA,yBAA0C;IASnFpD,EAAA,CAAAO,SAAA,GAA8B;IAA9BP,EAAA,CAAAqF,gBAAA,YAAAvE,MAAA,CAAAkG,gBAAA,CAA8B;IACThH,EAAA,CAAAO,SAAA,EAAkB;IAAlBP,EAAA,CAAAI,UAAA,YAAAU,MAAA,CAAAkI,eAAA,CAAkB;IAOvChJ,EAAA,CAAAO,SAAA,GAAiC;IAAjCP,EAAA,CAAAqF,gBAAA,YAAAvE,MAAA,CAAAuG,mBAAA,CAAiC;IACZrH,EAAA,CAAAO,SAAA,EAAuB;IAAvBP,EAAA,CAAAI,UAAA,YAAAU,MAAA,CAAAmI,oBAAA,CAAuB;IAOgBjJ,EAAA,CAAAO,SAAA,GAA6B;IAA7BP,EAAA,CAAAqF,gBAAA,YAAAvE,MAAA,CAAA4G,eAAA,CAA6B;IAmBpG1H,EAAA,CAAAO,SAAA,IACF;IADEP,EAAA,CAAAQ,kBAAA,0BAAAM,MAAA,CAAAoI,cAAA,CAAAhH,MAAA,yBACF;IAMyBlC,EAAA,CAAAO,SAAA,GAAiB;IAAjBP,EAAA,CAAAI,UAAA,YAAAU,MAAA,CAAAqI,cAAA,CAAiB;IAyCpCnJ,EAAA,CAAAO,SAAA,EAAiC;IAAjCP,EAAA,CAAAI,UAAA,SAAAU,MAAA,CAAAqI,cAAA,CAAAjH,MAAA,OAAiC;IAQvClC,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAAQ,kBAAA,aAAAM,MAAA,CAAAuH,iBAAA,yBACF;IACgBrI,EAAA,CAAAO,SAAA,EAAsC;IAA8BP,EAApE,CAAAqF,gBAAA,mBAAAvE,MAAA,CAAAuH,iBAAA,CAAsC,aAAAvH,MAAA,CAAAyH,aAAA,CAA6B,SAAAzH,MAAA,CAAA2H,gBAAA,CAA4B;IAKtEzI,EAAA,CAAAO,SAAA,GAAwC;IAAxCP,EAAA,CAAAI,UAAA,aAAAU,MAAA,CAAAoI,cAAA,CAAAhH,MAAA,OAAwC;IAE7ElC,EAAA,CAAAO,SAAA,EACF;IADEP,EAAA,CAAAQ,kBAAA,gCAAAM,MAAA,CAAAoI,cAAA,CAAAhH,MAAA,OACF;;;;;IAqBFlC,EAAA,CAAAkB,SAAA,eAC8E;;;;IAA5ElB,EADsD,CAAAI,UAAA,QAAAU,MAAA,CAAAsI,eAAA,CAAAC,OAAA,EAAArJ,EAAA,CAAA4F,aAAA,CAA+B,QAAA9E,MAAA,CAAAsI,eAAA,CAAAvD,IAAA,CACzD;;;;;IAC9B7F,EAAA,CAAAC,cAAA,eAA4F;IAC1FD,EAAA,CAAAkB,SAAA,aAA0C;IAC1ClB,EAAA,CAAAC,cAAA,UAAK;IAAAD,EAAA,CAAAE,MAAA,2CAAM;IACbF,EADa,CAAAG,YAAA,EAAM,EACb;;;;;;IAhBNH,EAFJ,CAAAC,cAAA,mBAAqC,0BACuC,WAClE;IAAAD,EAAA,CAAAE,MAAA,GAAkC;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAE7CH,EADF,CAAAC,cAAA,eAA0B,kBACuF;IAA1BD,EAAA,CAAAU,UAAA,mBAAA4I,0EAAA;MAAAtJ,EAAA,CAAAY,aAAA,CAAA2I,IAAA;MAAA,MAAAzI,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CAASF,MAAA,CAAA0I,aAAA,EAAe;IAAA,EAAC;IAC5GxJ,EAAA,CAAAkB,SAAA,aAAmC;IAAClB,EAAA,CAAAE,MAAA,2BACtC;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,kBACwB;IAAtBD,EAAA,CAAAU,UAAA,mBAAA+I,0EAAA;MAAAzJ,EAAA,CAAAY,aAAA,CAAA2I,IAAA;MAAA,MAAAzI,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CAASF,MAAA,CAAA4I,SAAA,EAAW;IAAA,EAAC;IACrB1J,EAAA,CAAAE,MAAA,2BAAI;IAAAF,EAAA,CAAAkB,SAAA,cAAoC;IAG9ClB,EAFI,CAAAG,YAAA,EAAS,EACL,EACS;IAACH,EAAA,CAAAC,cAAA,yBAAkG;IAGlHD,EAFA,CAAA6C,UAAA,KAAA8G,wDAAA,mBAC8E,KAAAC,wDAAA,mBACc;IAI9F5J,EAAA,CAAAG,YAAA,EAAe;IAEbH,EADF,CAAAC,cAAA,2BAA0E,gBACrC;IACjCD,EAAA,CAAAE,MAAA,IACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACqBH,EAA3B,CAAAC,cAAA,gBAA0B,mBAAuF;IAA1CD,EAAA,CAAAU,UAAA,mBAAAmJ,2EAAA;MAAA7J,EAAA,CAAAY,aAAA,CAAA2I,IAAA;MAAA,MAAAzI,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CAASF,MAAA,CAAAgJ,6BAAA,EAA+B;IAAA,EAAC;IAC5G9J,EAAA,CAAAE,MAAA,IACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,mBAA4D;IAAtBD,EAAA,CAAAU,UAAA,mBAAAqJ,2EAAA;MAAA,MAAAC,OAAA,GAAAhK,EAAA,CAAAY,aAAA,CAAA2I,IAAA,EAAAtE,SAAA;MAAA,OAAAjF,EAAA,CAAAgB,WAAA,CAASgJ,OAAA,CAAAC,KAAA,EAAW;IAAA,EAAC;IAACjK,EAAA,CAAAE,MAAA,oBAAE;IAGpEF,EAHoE,CAAAG,YAAA,EAAS,EACnE,EACS,EACT;;;;IA5BAH,EAAA,CAAAO,SAAA,GAAkC;IAAlCP,EAAA,CAAAQ,kBAAA,gCAAAM,MAAA,CAAAsI,eAAA,kBAAAtI,MAAA,CAAAsI,eAAA,CAAAvD,IAAA,KAAkC;IAES7F,EAAA,CAAAO,SAAA,GAAqC;IAArCP,EAAA,CAAAI,UAAA,aAAAU,MAAA,CAAAoJ,mBAAA,MAAqC;IAGrClK,EAAA,CAAAO,SAAA,GAA6D;IAA7DP,EAAA,CAAAI,UAAA,aAAAU,MAAA,CAAAoJ,mBAAA,IAAApJ,MAAA,CAAAqI,cAAA,CAAAjH,MAAA,KAA6D;IAMxGlC,EAAA,CAAAO,SAAA,GAAgD;IAAhDP,EAAA,CAAAI,UAAA,SAAAU,MAAA,CAAAsI,eAAA,IAAAtI,MAAA,CAAAsI,eAAA,CAAAC,OAAA,CAAgD;IAEhDrJ,EAAA,CAAAO,SAAA,EAAkD;IAAlDP,EAAA,CAAAI,UAAA,UAAAU,MAAA,CAAAsI,eAAA,KAAAtI,MAAA,CAAAsI,eAAA,CAAAC,OAAA,CAAkD;IAOtDrJ,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAAmK,kBAAA,MAAArJ,MAAA,CAAAoJ,mBAAA,aAAApJ,MAAA,CAAAqI,cAAA,CAAAjH,MAAA,MACF;IAEIlC,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAAQ,kBAAA,MAAAM,MAAA,CAAAsI,eAAA,IAAAtI,MAAA,CAAA2F,eAAA,CAAA3F,MAAA,CAAAsI,eAAA,uEACF;;;;;;IAWFpJ,EAFJ,CAAAC,cAAA,mBAAqF,qBACnE,WACR;IACJD,EAAA,CAAAE,MAAA,qBACF;IACFF,EADE,CAAAG,YAAA,EAAO,EACQ;IAEfH,EADF,CAAAC,cAAA,wBAAwC,eACX;IACzBD,EAAA,CAAAkB,SAAA,eAAkD;IAEtDlB,EADE,CAAAG,YAAA,EAAM,EACO;IAGXH,EAFJ,CAAAC,cAAA,qBAAgB,eACgC,kBACc;IAAtBD,EAAA,CAAAU,UAAA,mBAAA0J,0EAAA;MAAA,MAAAC,OAAA,GAAArK,EAAA,CAAAY,aAAA,CAAA0J,IAAA,EAAArF,SAAA;MAAA,OAAAjF,EAAA,CAAAgB,WAAA,CAASqJ,OAAA,CAAAJ,KAAA,EAAW;IAAA,EAAC;IAACjK,EAAA,CAAAE,MAAA,oBAAE;IAGlEF,EAHkE,CAAAG,YAAA,EAAS,EACjE,EACS,EACT;;;;IARkBH,EAAA,CAAAO,SAAA,GAA2B;IAA3BP,EAAA,CAAAI,UAAA,QAAAU,MAAA,CAAAyJ,mBAAA,EAAAvK,EAAA,CAAA4F,aAAA,CAA2B;;;ADtVzD;AACA,IAAK4E,eAIJ;AAJD,WAAKA,eAAe;EAClBA,eAAA,CAAAA,eAAA,sBAAQ;EACRA,eAAA,CAAAA,eAAA,gDAAqB;EACrBA,eAAA,CAAAA,eAAA,gCAAa,EAAO;AACtB,CAAC,EAJIA,eAAe,KAAfA,eAAe;AAwBpB,OAAM,MAAOC,yBAA0B,SAAQ5K,aAAa;EAmE1D6K,YACUC,MAAmB,EACnBC,aAA8B,EAC9BC,OAAuB,EACvBC,KAAuB,EACvBC,iBAAmC,EACnCC,gBAAiC,EACjCC,eAA+B,EAC/BC,eAA+B;IAEvC,KAAK,CAACP,MAAM,CAAC;IATL,KAAAA,MAAM,GAANA,MAAM;IACN,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,eAAe,GAAfA,eAAe;IA1EzB,KAAAC,KAAK,GAAG,IAAI;IAKZ,KAAAC,cAAc,GAA8B,EAAE;IAG9C,KAAAC,eAAe,GAAG,CAChB;MACE9F,KAAK,EAAE,IAAI;MACXC,KAAK,EAAE;KACR,EACD;MACED,KAAK,EAAE,KAAK;MACZC,KAAK,EAAE;KACR,EACD;MACED,KAAK,EAAE,IAAI;MACXC,KAAK,EAAE;KACR,CAAC;IAAE,KAAA8F,iBAAiB,GAAG,IAAI;IAC9B,KAAA5H,WAAW,GAAW,EAAE;IACxB;IACA;IACA;IACA;IACA,KAAAN,UAAU,GAAW,EAAE;IACvB,KAAAmI,SAAS,GAAY,KAAK;IAC1B,KAAAhB,mBAAmB,GAAW,EAAE;IAChC,KAAAiB,aAAa,GAAY,KAAK;IAC9B,KAAA/H,UAAU,GAAY,IAAI;IAC1B;IACA,KAAAgI,eAAe,GAAgB,EAAE;IACjC,KAAAtC,cAAc,GAAgB,EAAE;IAChC,KAAAD,cAAc,GAAgB,EAAE;IAChC,KAAAxB,eAAe,GAAW,EAAE;IAC5B,KAAA0B,eAAe,GAAqB,IAAI;IACxC,KAAAc,mBAAmB,GAAW,CAAC;IAE/B;IACA,KAAA7C,mBAAmB,GAAW,KAAK,EAAC;IACpC,KAAA4B,oBAAoB,GAAG,CACrB;MAAE1D,KAAK,EAAE,KAAK;MAAEC,KAAK,EAAE;IAAM,CAAE,EAC/B;MAAED,KAAK,EAAE,OAAO;MAAEC,KAAK,EAAE;IAAQ,CAAE,CACpC;IAED;IACA,KAAAiD,gBAAgB,GAAW,CAAC;IAC5B,KAAAF,aAAa,GAAW,EAAE;IAC1B,KAAAF,iBAAiB,GAAW,CAAC;IAE7B;IACA,KAAAW,eAAe,GAAG,CAChB;MAAEzD,KAAK,EAAEiF,eAAe,CAACkB,iBAAiB;MAAElG,KAAK,EAAE;IAAM,CAAE,EAC3D;MAAED,KAAK,EAAEiF,eAAe,CAACmB,SAAS;MAAEnG,KAAK,EAAE;IAAM,CAAE,CACpD;IACD,KAAAwB,gBAAgB,GAAoBwD,eAAe,CAACkB,iBAAiB;IACrE,KAAAE,kBAAkB,GAAY,IAAI,EAAC;IACnC;IACA,KAAApB,eAAe,GAAGA,eAAe;IAEjC;IACA,KAAA1K,UAAU,GAAGA,UAAU;IACvB,KAAAC,gBAAgB,GAAGA,gBAAgB;IACnC,KAAA8L,aAAa,GAAG9L,gBAAgB,CAAC+L,aAAa,EAAE;EAahD;EAESC,QAAQA,CAAA;IACf,IAAI,CAACC,gBAAgB,EAAE;EACzB;EAEAA,gBAAgBA,CAAA;IACd,IAAI,CAACjB,iBAAiB,CAACkB,6CAA6C,CAAC;MACnEC,IAAI,EAAE;QACJC,OAAO,EAAE,KAAK;QACdC,OAAO,EAAE;;KAEZ,CAAC,CACCC,IAAI,CACH3M,GAAG,CAAC4M,GAAG,IAAG;MACR,IAAIA,GAAG,CAACC,UAAU,IAAI,CAAC,EAAE;QACvB,IAAI,CAACnB,cAAc,GAAGkB,GAAG,CAACE,OAAO,EAAEtK,MAAM,GAAGoK,GAAG,CAACE,OAAO,GAAG,EAAE;QAC5D,IAAI,CAACC,mBAAmB,GAAG,IAAI,CAACrB,cAAc,CAAC,CAAC,CAAC,CAAC9K,GAAI;MACxD;IACF,CAAC,CAAC,EACFb,QAAQ,CAAC,MAAM,IAAI,CAACiN,eAAe,EAAE,CAAC,CACvC,CAACC,SAAS,EAAE;EACjB;EAAED,eAAeA,CAACE,SAAA,GAAoB,CAAC;IACrC,OAAO,IAAI,CAAC5B,gBAAgB,CAAC6B,mCAAmC,CAAC;MAC/DX,IAAI,EAAE;QACJY,YAAY,EAAE,IAAI,CAACL,mBAAmB;QACtCM,QAAQ,EAAE,IAAI,CAACzB,iBAAiB;QAChC5H,WAAW,EAAE,IAAI,CAACA,WAAW;QAC7B;QACAN,UAAU,EAAE,IAAI,CAACA,UAAU;QAC3B;QACA4J,QAAQ,EAAE,IAAI,CAACC,QAAQ;QACvBC,SAAS,EAAEN,SAAS;QACpBnJ,UAAU,EAAE,IAAI,CAACA;;KAEpB,CAAC,CAAC4I,IAAI,CACL3M,GAAG,CAAC4M,GAAG,IAAG;MACR,IAAIA,GAAG,CAACC,UAAU,IAAI,CAAC,EAAE;QACvB,IAAI,CAACxI,YAAY,GAAGuI,GAAG,CAACE,OAAQ,IAAI,EAAE;QACtC,IAAI,CAACW,YAAY,GAAGb,GAAG,CAACc,UAAW;QAEnC,IAAI,IAAI,CAACrJ,YAAY,CAAC7B,MAAM,GAAG,CAAC,EAAE;UAChC,IAAI,CAACqJ,SAAS,GAAG,IAAI,CAACxH,YAAY,CAAC,CAAC,CAAC,CAACH,UAAW;QACnD;MACF;IACF,CAAC,CAAC,CACH;EACH;EAEAvC,MAAMA,CAAA;IACJ,IAAI,CAACqL,eAAe,EAAE,CAACC,SAAS,EAAE;EACpC;EAEAU,WAAWA,CAACT,SAAiB;IAC3B,IAAI,CAACF,eAAe,CAACE,SAAS,CAAC,CAACD,SAAS,EAAE;EAC7C;EAEA1L,sBAAsBA,CAAA;IACpB,IAAI,CAAC+J,gBAAgB,CAACsC,2CAA2C,CAAC;MAChEpB,IAAI,EAAE,IAAI,CAACO;KACZ,CAAC,CAACE,SAAS,CAACL,GAAG,IAAG;MACjB,IAAIA,GAAG,CAACC,UAAU,IAAI,CAAC,EAAE;QACvB,IAAID,GAAG,CAACE,OAAQ,CAACe,QAAQ,EAAE;UACzB,IAAI,CAACtC,eAAe,CAACuC,iBAAiB,CAAClB,GAAG,CAACE,OAAQ,CAACe,QAAQ,EAAE,MAAM,CAAC;QACvE;MACF;IACF,CAAC,CAAC;EACJ;EAEAE,0BAA0BA,CAAA;IACxB,IAAI,CAACzC,gBAAgB,CAAC0C,+CAA+C,CAAC;MACpExB,IAAI,EAAE,IAAI,CAACO;KACZ,CAAC,CAACE,SAAS,CAACL,GAAG,IAAG;MACjB,IAAIA,GAAG,CAACC,UAAU,IAAI,CAAC,EAAE;QACvB,IAAID,GAAG,CAACE,OAAQ,CAACe,QAAQ,EAAE;UACzB,IAAI,CAACtC,eAAe,CAACuC,iBAAiB,CAAClB,GAAG,CAACE,OAAQ,CAACe,QAAQ,EAAE,MAAM,CAAC;QACvE;MACF;IACF,CAAC,CAAC;EACJ;EACA7L,MAAMA,CAACiM,GAAQ;IACb,IAAI,CAACxC,KAAK,GAAG,IAAI;IACjB,IAAI,CAACnH,gBAAgB,GAAG;MACtBoI,OAAO,EAAEtM,UAAU,CAAC8N,MAAM,CAAC;KAC5B;IACD,IAAI,CAAChD,aAAa,CAACiD,IAAI,CAACF,GAAG,CAAC;EAC9B;EACApL,kBAAkBA,CAACuL,IAA6B,EAAEH,GAAQ;IACxD,IAAI,CAACxC,KAAK,GAAG,KAAK;IAClB,IAAI,CAACnH,gBAAgB,GAAG;MAAE,GAAG8J;IAAI,CAAE;IACnC,IAAI,CAAClD,aAAa,CAACiD,IAAI,CAACF,GAAG,CAAC;EAC9B;EACAhL,oBAAoBA,CAACmL,IAA6B,EAAEH,GAAqB;IACvE,IAAI,CAAC3J,gBAAgB,GAAG;MAAE,GAAG8J;IAAI,CAAE;IACnC,IAAI,CAACjG,UAAU,EAAE;IACjB;IACA,IAAI,CAACI,iBAAiB,EAAE;IACxB;IACA,IAAI,CAACZ,mBAAmB,GAAG,KAAK;IAChC,IAAI,CAACK,eAAe,GAAG,EAAE;IACzB,IAAI,CAACkD,aAAa,CAACiD,IAAI,CAACF,GAAG,EAAE;MAAEI,oBAAoB,EAAE;IAAK,CAAE,CAAC;EAC/D;EAAEC,UAAUA,CAAA;IACV,IAAI,CAAClD,KAAK,CAACmD,KAAK,EAAE;IAClB,IAAI,CAACnD,KAAK,CAACoD,QAAQ,CAAC,MAAM,EAAE,IAAI,CAAClK,gBAAgB,CAACpB,KAAK,CAAC;IACxD,IAAI,CAACkI,KAAK,CAACoD,QAAQ,CAAC,MAAM,EAAE,IAAI,CAAClK,gBAAgB,CAACV,KAAK,CAAC;IACxD,IAAI,CAACwH,KAAK,CAACoD,QAAQ,CAAC,MAAM,EAAE,IAAI,CAAClK,gBAAgB,CAACT,SAAS,CAAC;IAC5D,IAAI,CAACuH,KAAK,CAACoD,QAAQ,CAAC,UAAU,EAAE,IAAI,CAAClK,gBAAgB,CAACN,WAAW,CAAC;IAClE;IACA,IAAI,CAACoH,KAAK,CAACoD,QAAQ,CAAC,QAAQ,EAAE,IAAI,CAAClK,gBAAgB,CAACZ,UAAU,CAAC;IAC/D,IAAI,CAAC0H,KAAK,CAACqD,iBAAiB,CAAC,MAAM,EAAE,IAAI,CAACnK,gBAAgB,CAACpB,KAAK,EAAE,EAAE,CAAC;IACrE,IAAI,CAACkI,KAAK,CAACqD,iBAAiB,CAAC,MAAM,EAAE,IAAI,CAACnK,gBAAgB,CAACV,KAAK,EAAE,EAAE,CAAC;IACrE,IAAI,CAACwH,KAAK,CAACqD,iBAAiB,CAAC,MAAM,EAAE,IAAI,CAACnK,gBAAgB,CAACT,SAAS,EAAE,EAAE,CAAC;IACzE,IAAI,CAACuH,KAAK,CAACqD,iBAAiB,CAAC,UAAU,EAAE,IAAI,CAACnK,gBAAgB,CAACN,WAAW,EAAE,EAAE,CAAC;IAC/E;IACA,IAAI,CAACoH,KAAK,CAACqD,iBAAiB,CAAC,QAAQ,EAAE,IAAI,CAACnK,gBAAgB,CAACZ,UAAU,EAAE,EAAE,CAAC;EAC9E;EAEAgC,QAAQA,CAACuI,GAAQ;IACf,IAAI,CAACK,UAAU,EAAE;IACjB,IAAI,IAAI,CAAClD,KAAK,CAACsD,aAAa,CAAClM,MAAM,GAAG,CAAC,EAAE;MACvC,IAAI,CAAC2I,OAAO,CAACwD,aAAa,CAAC,IAAI,CAACvD,KAAK,CAACsD,aAAa,CAAC;MACpD;IACF;IAAE,IAAI,CAACpD,gBAAgB,CAACsD,qCAAqC,CAAC;MAC5DpC,IAAI,EAAE;QACJY,YAAY,EAAE,IAAI,CAACL,mBAAmB;QACtC;QACArJ,UAAU,EAAE,IAAI,CAACY,gBAAgB,CAACZ,UAAU;QAC5CR,KAAK,EAAE,IAAI,CAACoB,gBAAgB,CAACpB,KAAK;QAClCU,KAAK,EAAE,IAAI,CAACU,gBAAgB,CAACV,KAAK;QAClCC,SAAS,EAAE,IAAI,CAACS,gBAAgB,CAACT,SAAS;QAASG,WAAW,EAAE,IAAI,CAACM,gBAAgB,CAACN,WAAW;QACjGC,YAAY,EAAE,IAAI,CAACK,gBAAgB,CAACL,YAAY;QAChD4K,WAAW,EAAE,IAAI,CAACpD,KAAK,GAAG,IAAI,GAAG,IAAI,CAACnH,gBAAgB,CAACb,GAAI;QAC3DhB,MAAM,EAAE,IAAI,CAAC6B,gBAAgB,CAAC7B,MAAM;QACpCiK,OAAO,EAAE,IAAI,CAACpI,gBAAgB,CAACoI,OAAO,IAAItM,UAAU,CAAC8N,MAAM;QAC3DY,UAAU,EAAG,IAAI,CAACxK,gBAAwB,CAACyK,gBAAgB,IAAI,EAAE,CAAC;;KAErE,CAAC,CACCpC,IAAI,CACH3M,GAAG,CAAC4M,GAAG,IAAG;MACR,IAAIA,GAAG,CAACC,UAAU,KAAK,CAAC,EAAE;QACxB,IAAI,CAAC1B,OAAO,CAAC6D,aAAa,CAAC,MAAM,CAAC;MACpC,CAAC,MAAM;QACL,IAAI,CAAC7D,OAAO,CAAC8D,YAAY,CAACrC,GAAG,CAACsC,OAAQ,CAAC;MACzC;IACF,CAAC,CAAC,EACFnP,QAAQ,CAAC,MAAM,IAAI,CAACiN,eAAe,EAAE,CAAC,EACtClN,QAAQ,CAAC,MAAMmO,GAAG,CAAC1D,KAAK,EAAE,CAAC,CAC5B,CAAC0C,SAAS,EAAE;EACjB;EAEAzH,OAAOA,CAACyI,GAAQ;IACdA,GAAG,CAAC1D,KAAK,EAAE;EACb;EAEA4E,eAAeA,CAACC,KAAU;IACxB,MAAMC,MAAM,GAAgCD,KAAK,CAACC,MAAO;IACzD,MAAMC,MAAM,GAAe,IAAIC,UAAU,EAAE;IAC3CD,MAAM,CAACE,kBAAkB,CAACH,MAAM,CAACI,KAAK,CAAC,CAAC,CAAC,CAAC;IAC1CH,MAAM,CAACI,MAAM,GAAIC,CAAM,IAAI;MACzB,MAAMC,SAAS,GAAWD,CAAC,CAACN,MAAM,CAACQ,MAAM;MACzC,MAAMC,EAAE,GAAkB7P,IAAI,CAAC8P,IAAI,CAACH,SAAS,EAAE;QAAEI,IAAI,EAAE;MAAQ,CAAE,CAAC;MAElE,MAAMC,MAAM,GAAWH,EAAE,CAACI,UAAU,CAAC,CAAC,CAAC;MACvC,MAAMC,EAAE,GAAmBL,EAAE,CAACM,MAAM,CAACH,MAAM,CAAC;MAE5C,IAAII,WAAW,GAAY,IAAI;MAC/B,MAAMjC,IAAI,GAAGnO,IAAI,CAACqQ,KAAK,CAACC,aAAa,CAACJ,EAAE,CAAC;MACzC,IAAI/B,IAAI,IAAIA,IAAI,CAAC5L,MAAM,GAAG,CAAC,EAAE;QAC3B4L,IAAI,CAACoC,OAAO,CAAEC,CAAM,IAAI;UACtB,IAAI,EAAEA,CAAC,CAAC,IAAI,CAAC,IAAIA,CAAC,CAAC,IAAI,CAAC,IAAIA,CAAC,CAAC,IAAI,CAAC,IAAIA,CAAC,CAAC,QAAQ,CAAC,KAC/CA,CAAC,CAAC,QAAQ,CAAC,IAAIA,CAAC,CAAC,MAAM,CAAC,IAAIA,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE;YAC5CJ,WAAW,GAAG,KAAK;UACrB;QACF,CAAC,CAAC;QAEF,IAAI,CAACA,WAAW,EAAE;UAChB,IAAI,CAAClF,OAAO,CAAC8D,YAAY,CAAC,WAAW,CAAC;QACxC,CAAC,MAAM;UACL,IAAI,CAAC3D,gBAAgB,CAACoF,2CAA2C,CAAC;YAChElE,IAAI,EAAE;cACJY,YAAY,EAAE,IAAI,CAACL,mBAAmB;cACtC4D,KAAK,EAAEtB,MAAM,CAACI,KAAK,CAAC,CAAC;;WAExB,CAAC,CAAC9C,IAAI,CACL3M,GAAG,CAAC4M,GAAG,IAAG;YACR,IAAIA,GAAG,CAACC,UAAU,IAAI,CAAC,EAAE;cACvB,IAAI,CAAC1B,OAAO,CAAC6D,aAAa,CAAC,MAAM,CAAC;YACpC,CAAC,MAAM;cACL,IAAI,CAAC7D,OAAO,CAAC8D,YAAY,CAACrC,GAAG,CAACsC,OAAQ,CAAC;YACzC;UACF,CAAC,CAAC,EACFnP,QAAQ,CAAC,MAAM,IAAI,CAACiN,eAAe,CAAC,CAAC,CAAC,CAAC,CACxC,CAACC,SAAS,EAAE;QACf;MACF,CAAC,MAAM;QACL,IAAI,CAAC9B,OAAO,CAAC8D,YAAY,CAAC,uBAAuB,CAAC;MACpD;MACAG,KAAK,CAACC,MAAM,CAACxJ,KAAK,GAAG,IAAI;IAC3B,CAAC;EACH;EAEA+K,SAASA,CAACC,QAAgB,EAAEC,MAAwB;IAClD,IAAI,CAACjG,mBAAmB,GAAGgG,QAAQ;IACnC,IAAI,CAAC3F,aAAa,CAACiD,IAAI,CAAC2C,MAAM,CAAC;EACjC;EACAC,YAAYA,CAAA;IACV,IAAI,IAAI,CAACjF,aAAa,EAAE;MACtB,IAAI,CAAC/H,UAAU,GAAG,KAAK;MACvB,IAAI,CAACiJ,eAAe,EAAE,CAACC,SAAS,EAAE;IACpC,CAAC,MACI;MACH,IAAI,CAAClJ,UAAU,GAAG,IAAI;MACtB,IAAI,CAACiJ,eAAe,EAAE,CAACC,SAAS,EAAE;IACpC;EACF;EACA;EACA9H,eAAeA,CAAC8I,GAAqB;IACnC,IAAI,CAAC9F,UAAU,EAAE;IACjB;IACA,IAAI,CAACR,mBAAmB,GAAG,KAAK;IAChC,IAAI,CAACK,eAAe,GAAG,EAAE;IACzB,IAAI,CAACkD,aAAa,CAACiD,IAAI,CAACF,GAAG,EAAE;MAAEI,oBAAoB,EAAE;IAAK,CAAE,CAAC;EAC/D;EAAElG,UAAUA,CAAA;IACV;IACA,IAAI,IAAI,CAAC+D,kBAAkB,IAAI,IAAI,CAACa,mBAAmB,EAAE;MACvD,IAAI,CAACvB,eAAe,CAACwF,kCAAkC,CAAC;QACtDxE,IAAI,EAAE;UACJY,YAAY,EAAE,IAAI,CAACL,mBAAmB;UACtCkE,YAAY,EAAE,IAAI,CAAC3J,gBAAgB;UACnCkG,SAAS,EAAE,IAAI,CAACzE,gBAAgB;UAAE;UAClCuE,QAAQ,EAAE,IAAI,CAACzE,aAAa,CAAC;;OAEhC,CAAC,CAACoE,SAAS,CAACL,GAAG,IAAG;QACjB,IAAIA,GAAG,CAACC,UAAU,KAAK,CAAC,EAAE;UACxB;UACA,IAAI,CAACd,eAAe,GAAGa,GAAG,CAACE,OAAO,EAAEoE,GAAG,CAACC,OAAO,KAAK;YAClDlK,EAAE,EAAEkK,OAAO,CAAC1N,GAAG,IAAI,CAAC;YAAE;YACtB0C,IAAI,EAAEgL,OAAO,CAACC,YAAY,IAAID,OAAO,CAACjO,KAAK,IAAI,EAAE;YACjDiE,IAAI,EAAE,CAAC;YAAE;YACTlB,YAAY,EAAEkL,OAAO,CAACE,OAAO,GAAG,0BAA0BF,OAAO,CAACE,OAAO,EAAE,GAAG,EAAE;YAChF1H,OAAO,EAAEwH,OAAO,CAACE,OAAO,GAAG,0BAA0BF,OAAO,CAACE,OAAO,EAAE,GAAG,EAAE;YAC3EC,YAAY,EAAEH,OAAO,CAACI,SAAS,GAAG,IAAIC,IAAI,CAACL,OAAO,CAACI,SAAS,CAAC,GAAG,IAAIC,IAAI;WACzE,CAAC,CAAC,IAAI,EAAE;UACT,IAAI,CAAC/H,cAAc,GAAG,CAAC,GAAG,IAAI,CAACsC,eAAe,CAAC;UAC/C,IAAI,CAACpD,iBAAiB,GAAGiE,GAAG,CAACc,UAAU,IAAI,CAAC,CAAC,CAAC;UAE9C;UACA,IAAI,IAAI,CAACpJ,gBAAgB,IAAI,IAAI,CAACA,gBAAgB,CAAC/B,gBAAgB,IAAI,IAAI,CAAC+B,gBAAgB,CAAC/B,gBAAgB,CAACC,MAAM,GAAG,CAAC,EAAE;YACxH;YACA,MAAMiP,aAAa,GAAG,IAAI,CAACnN,gBAAgB,CAAC/B,gBAAgB,CAAC2O,GAAG,CAACjK,EAAE,IAAI,OAAOA,EAAE,KAAK,QAAQ,GAAGyK,QAAQ,CAACzK,EAAE,CAAC,GAAGA,EAAE,CAAC;YAElH;YACA,MAAM0K,WAAW,GAAG,IAAI,CAAC5F,eAAe,CAAC6F,MAAM,CAACC,KAAK,IACnDJ,aAAa,CAACK,QAAQ,CAACD,KAAK,CAAC5K,EAAE,CAAC,CACjC;YAED;YACA,IAAI,CAACuC,cAAc,GAAG,CAAC,GAAGmI,WAAW,CAAC;UACxC;QACF,CAAC,MAAM;UACL,IAAI,CAACxG,OAAO,CAAC8D,YAAY,CAACrC,GAAG,CAACsC,OAAO,IAAI,QAAQ,CAAC;UAClD,IAAI,CAACnD,eAAe,GAAG,EAAE;UACzB,IAAI,CAACtC,cAAc,GAAG,EAAE;QAC1B;MACF,CAAC,CAAC;IACJ,CAAC,MAAM;MACL;MACA,IAAI,CAACsC,eAAe,GAAG,EAAE;MACzB,IAAI,CAACtC,cAAc,GAAG,EAAE;IAC1B;EACF;EACA5B,YAAYA,CAAA;IACV;IACA,IAAIkK,QAAQ,GAAG,CAAC,GAAG,IAAI,CAAChG,eAAe,CAAC;IAExC,IAAI,IAAI,CAAC/D,eAAe,CAACgK,IAAI,EAAE,EAAE;MAC/B,MAAMC,UAAU,GAAG,IAAI,CAACjK,eAAe,CAACkK,WAAW,EAAE;MACrDH,QAAQ,GAAGA,QAAQ,CAACH,MAAM,CAACC,KAAK,IAC9BA,KAAK,CAAC1L,IAAI,CAAC+L,WAAW,EAAE,CAACJ,QAAQ,CAACG,UAAU,CAAC,CAC9C;IACH;IAEA;IACA,IAAI,IAAI,CAACtK,mBAAmB,KAAK,OAAO,IAAI,IAAI,CAACrD,gBAAgB,IAAI,IAAI,CAACA,gBAAgB,CAAC/B,gBAAgB,EAAE;MAC3G;MACA,MAAMkP,aAAa,GAAG,IAAI,CAACnN,gBAAgB,CAAC/B,gBAAgB,CAAC2O,GAAG,CAACjK,EAAE,IACjE,OAAOA,EAAE,KAAK,QAAQ,GAAGyK,QAAQ,CAACzK,EAAE,CAAC,GAAGA,EAAE,CAC3C;MAED;MACA8K,QAAQ,GAAGA,QAAQ,CAACH,MAAM,CAACC,KAAK,IAAIJ,aAAa,CAACK,QAAQ,CAACD,KAAK,CAAC5K,EAAE,CAAC,CAAC;IACvE;IAEA,IAAI,CAACwC,cAAc,GAAGsI,QAAQ;EAChC;EAEAzL,oBAAoBA,CAACuL,KAAgB;IACnC,MAAMM,KAAK,GAAG,IAAI,CAAC3I,cAAc,CAAC4I,SAAS,CAACC,QAAQ,IAAIA,QAAQ,CAACpL,EAAE,KAAK4K,KAAK,CAAC5K,EAAE,CAAC;IACjF,IAAIkL,KAAK,GAAG,CAAC,CAAC,EAAE;MACd,IAAI,CAAC3I,cAAc,CAAC8I,MAAM,CAACH,KAAK,EAAE,CAAC,CAAC;IACtC,CAAC,MAAM;MACL,IAAI,CAAC3I,cAAc,CAAC+I,IAAI,CAACV,KAAK,CAAC;IACjC;EACF;EAEA9K,eAAeA,CAAC8K,KAAgB;IAC9B,OAAO,IAAI,CAACrI,cAAc,CAACgJ,IAAI,CAACH,QAAQ,IAAIA,QAAQ,CAACpL,EAAE,KAAK4K,KAAK,CAAC5K,EAAE,CAAC;EACvE;EAEAoB,eAAeA,CAAA;IACb,IAAI,CAACmB,cAAc,GAAG,CAAC,GAAG,IAAI,CAACC,cAAc,CAAC;EAChD;EAEAlB,iBAAiBA,CAAA;IACf,IAAI,CAACiB,cAAc,GAAG,EAAE;EAC1B;EAAE7C,YAAYA,CAACkL,KAAgB,EAAEY,eAAiC,EAAErD,KAAY;IAC9EA,KAAK,CAACsD,eAAe,EAAE;IACvB,IAAI,CAAChJ,eAAe,GAAGmI,KAAK;IAC5B,IAAI,CAACrH,mBAAmB,GAAG,IAAI,CAACf,cAAc,CAAC2I,SAAS,CAACO,GAAG,IAAIA,GAAG,CAAC1L,EAAE,KAAK4K,KAAK,CAAC5K,EAAE,CAAC;IACpF,IAAI,CAACiE,aAAa,CAACiD,IAAI,CAACsE,eAAe,CAAC;EAC1C;EAEA3I,aAAaA,CAAA;IACX,IAAI,IAAI,CAACU,mBAAmB,GAAG,CAAC,EAAE;MAChC,IAAI,CAACA,mBAAmB,EAAE;MAC1B,IAAI,CAACd,eAAe,GAAG,IAAI,CAACD,cAAc,CAAC,IAAI,CAACe,mBAAmB,CAAC;IACtE;EACF;EAEAR,SAASA,CAAA;IACP,IAAI,IAAI,CAACQ,mBAAmB,GAAG,IAAI,CAACf,cAAc,CAACjH,MAAM,GAAG,CAAC,EAAE;MAC7D,IAAI,CAACgI,mBAAmB,EAAE;MAC1B,IAAI,CAACd,eAAe,GAAG,IAAI,CAACD,cAAc,CAAC,IAAI,CAACe,mBAAmB,CAAC;IACtE;EACF;EAEAJ,6BAA6BA,CAAA;IAC3B,IAAI,IAAI,CAACV,eAAe,EAAE;MACxB,IAAI,CAACpD,oBAAoB,CAAC,IAAI,CAACoD,eAAe,CAAC;IACjD;EACF;EACAL,uBAAuBA,CAAC4E,GAAQ;IAC9B,IAAI,IAAI,CAACzE,cAAc,CAAChH,MAAM,GAAG,CAAC,EAAE;MAClC;MACA,MAAMuM,gBAAgB,GAAG,IAAI,CAACvF,cAAc,CAAC0H,GAAG,CAACyB,GAAG,IAAIA,GAAG,CAAC1L,EAAE,CAAC,CAAC,CAAM;MACtE,IAAI,IAAI,CAACuC,cAAc,CAAChH,MAAM,KAAK,CAAC,EAAE;QACpC;QACA,IAAI,CAAC8B,gBAAgB,CAACwK,UAAU,GAAG,IAAI,CAACtF,cAAc,CAAC,CAAC,CAAC,CAACvC,EAAE;MAC9D,CAAC,MAAM;QACL;QACA,MAAM2L,UAAU,GAAG,IAAI,CAACpJ,cAAc,CAAC0H,GAAG,CAACyB,GAAG,IAAIA,GAAG,CAACxM,IAAI,CAAC,CAAC0M,IAAI,CAAC,IAAI,CAAC;QACtE,IAAI,CAAC1H,OAAO,CAAC6D,aAAa,CAAC,OAAO,IAAI,CAACxF,cAAc,CAAChH,MAAM,SAASoQ,UAAU,EAAE,CAAC;QAClF;QACA,IAAI,CAACtO,gBAAgB,CAACwK,UAAU,GAAG,IAAI,CAACtF,cAAc,CAAC,CAAC,CAAC,CAACvC,EAAE;MAC9D;MAEA;MACC,IAAI,CAAC3C,gBAAwB,CAACyK,gBAAgB,GAAGA,gBAAgB;MAElE;MACA,IAAI,IAAI,CAACzK,gBAAgB,CAACb,GAAG,EAAE;QAC7B,IAAI,CAACqP,gBAAgB,EAAE;MACzB;IACF;IAEA,IAAI,CAACvK,iBAAiB,EAAE;IACxB0F,GAAG,CAAC1D,KAAK,EAAE;EACb;EACA;EACAuI,gBAAgBA,CAAA;IACd,IAAI,CAACxH,gBAAgB,CAACsD,qCAAqC,CAAC;MAC1DpC,IAAI,EAAE;QACJY,YAAY,EAAE,IAAI,CAACL,mBAAmB;QACtCrJ,UAAU,EAAE,IAAI,CAACY,gBAAgB,CAACZ,UAAU;QAC5CR,KAAK,EAAE,IAAI,CAACoB,gBAAgB,CAACpB,KAAK;QAClCU,KAAK,EAAE,IAAI,CAACU,gBAAgB,CAACV,KAAK;QAClCC,SAAS,EAAE,IAAI,CAACS,gBAAgB,CAACT,SAAS;QAASG,WAAW,EAAE,IAAI,CAACM,gBAAgB,CAACN,WAAW;QACjGC,YAAY,EAAE,IAAI,CAACK,gBAAgB,CAACL,YAAY;QAChD4K,WAAW,EAAE,IAAI,CAACvK,gBAAgB,CAACb,GAAI;QACvChB,MAAM,EAAE,IAAI,CAAC6B,gBAAgB,CAAC7B,MAAM;QACpCiK,OAAO,EAAE,IAAI,CAACpI,gBAAgB,CAACoI,OAAO,IAAItM,UAAU,CAAC8N,MAAM;QAC3DY,UAAU,EAAG,IAAI,CAACxK,gBAAwB,CAACyK,gBAAgB,IAAI,EAAE,CAAC;;KAErE,CAAC,CAACpC,IAAI,CACL3M,GAAG,CAAC4M,GAAG,IAAG;MACR,IAAIA,GAAG,CAACC,UAAU,KAAK,CAAC,EAAE;QACxB,IAAI,CAAC1B,OAAO,CAAC6D,aAAa,CAAC,QAAQ,CAAC;MACtC,CAAC,MAAM;QACL,IAAI,CAAC7D,OAAO,CAAC8D,YAAY,CAACrC,GAAG,CAACsC,OAAQ,CAAC;MACzC;IACF,CAAC,CAAC,EACFnP,QAAQ,CAAC,MAAM,IAAI,CAACiN,eAAe,EAAE,CAAC,EACtClN,QAAQ,CAAC,MAAK;MACZ;MACA,IAAI,CAACwE,gBAAgB,GAAG,EAAE;IAC5B,CAAC,CAAC,CACH,CAAC2I,SAAS,EAAE;EACf;EACA9D,kBAAkBA,CAAC8E,GAAQ;IACzB,IAAI,CAAC1F,iBAAiB,EAAE;IACxB,IAAI,CAACP,eAAe,GAAG,EAAE;IACzB,IAAI,CAACL,mBAAmB,GAAG,KAAK,CAAC,CAAC;IAClC,IAAI,CAACoB,gBAAgB,GAAG,CAAC,CAAC,CAAC;IAC3BkF,GAAG,CAAC1D,KAAK,EAAE;EACb,CAAC;EACD/C,eAAeA,CAACuL,QAAyB;IACvC,IAAI,CAACzL,gBAAgB,GAAGyL,QAAQ;IAChC,IAAI,CAAC7G,kBAAkB,GAAG,IAAI;IAC9B;IACA,IAAI,CAACnD,gBAAgB,GAAG,CAAC;IACzB,IAAI,IAAI,CAACgE,mBAAmB,EAAE;MAC5B,IAAI,CAAC5E,UAAU,EAAE;IACnB;EACF;EAEA;EACA6K,gBAAgBA,CAACD,QAAgB;IAC/B,MAAME,MAAM,GAAG,IAAI,CAAC3J,eAAe,CAAC4J,IAAI,CAACC,GAAG,IAAIA,GAAG,CAACtN,KAAK,KAAKkN,QAAQ,CAAC;IACvE,OAAOE,MAAM,GAAGA,MAAM,CAACnN,KAAK,GAAG,MAAM;EACvC;EAEA;EACAkD,gBAAgBA,CAACoK,IAAY;IAC3B,IAAI,CAACrK,gBAAgB,GAAGqK,IAAI;IAC5B,IAAI,CAACjL,UAAU,EAAE;EACnB;;;uCAvfW4C,yBAAyB,EAAAzK,EAAA,CAAA+S,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAjT,EAAA,CAAA+S,iBAAA,CAAAG,EAAA,CAAAC,eAAA,GAAAnT,EAAA,CAAA+S,iBAAA,CAAAK,EAAA,CAAAC,cAAA,GAAArT,EAAA,CAAA+S,iBAAA,CAAAO,EAAA,CAAAC,gBAAA,GAAAvT,EAAA,CAAA+S,iBAAA,CAAAS,EAAA,CAAAC,gBAAA,GAAAzT,EAAA,CAAA+S,iBAAA,CAAAS,EAAA,CAAAE,eAAA,GAAA1T,EAAA,CAAA+S,iBAAA,CAAAY,EAAA,CAAAC,cAAA,GAAA5T,EAAA,CAAA+S,iBAAA,CAAAS,EAAA,CAAAK,cAAA;IAAA;EAAA;;;YAAzBpJ,yBAAyB;MAAAqJ,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAAhU,EAAA,CAAAiU,0BAAA,EAAAjU,EAAA,CAAAkU,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,mCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;UCvCpCxU,EADF,CAAAC,cAAA,iBAA0B,qBACR;UACdD,EAAA,CAAAkB,SAAA,qBAAiC;UACnClB,EAAA,CAAAG,YAAA,EAAiB;UAEfH,EADF,CAAAC,cAAA,mBAAc,YACyB;UAACD,EAAA,CAAAE,MAAA,yJACtC;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAICH,EAHN,CAAAC,cAAA,aAA8B,aACN,aACqC,gBACF;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAACH,EAAA,CAAAC,cAAA,qBACc;UAA5ED,EAAA,CAAAiE,gBAAA,2BAAAyQ,uEAAAvQ,MAAA;YAAAnE,EAAA,CAAAY,aAAA,CAAA+T,GAAA;YAAA3U,EAAA,CAAAqE,kBAAA,CAAAoQ,GAAA,CAAAhI,mBAAA,EAAAtI,MAAA,MAAAsQ,GAAA,CAAAhI,mBAAA,GAAAtI,MAAA;YAAA,OAAAnE,EAAA,CAAAgB,WAAA,CAAAmD,MAAA;UAAA,EAAiC;UAACnE,EAAA,CAAAU,UAAA,2BAAAgU,uEAAA;YAAA1U,EAAA,CAAAY,aAAA,CAAA+T,GAAA;YAAA,OAAA3U,EAAA,CAAAgB,WAAA,CAAiByT,GAAA,CAAApT,MAAA,EAAQ;UAAA,EAAC;UAC5DrB,EAAA,CAAA6C,UAAA,KAAA+R,+CAAA,wBAA4E;UAKlF5U,EAFI,CAAAG,YAAA,EAAY,EACR,EACF;UAaFH,EAFJ,CAAAC,cAAA,cAAsB,cACqC,iBACF;UAAAD,EAAA,CAAAE,MAAA,6CAAO;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACpEH,EAAA,CAAAC,cAAA,iBAAwG;UAAxDD,EAAA,CAAAiE,gBAAA,2BAAA4Q,mEAAA1Q,MAAA;YAAAnE,EAAA,CAAAY,aAAA,CAAA+T,GAAA;YAAA3U,EAAA,CAAAqE,kBAAA,CAAAoQ,GAAA,CAAA/Q,WAAA,EAAAS,MAAA,MAAAsQ,GAAA,CAAA/Q,WAAA,GAAAS,MAAA;YAAA,OAAAnE,EAAA,CAAAgB,WAAA,CAAAmD,MAAA;UAAA,EAAyB;UAE7EnE,EAFI,CAAAG,YAAA,EAAwG,EACpG,EACF;UAIFH,EAFJ,CAAAC,cAAA,cAAsB,cACqC,iBACF;UAAAD,EAAA,CAAAE,MAAA,gCAAI;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACjEH,EAAA,CAAAC,cAAA,iBAAqG;UAAvDD,EAAA,CAAAiE,gBAAA,2BAAA6Q,mEAAA3Q,MAAA;YAAAnE,EAAA,CAAAY,aAAA,CAAA+T,GAAA;YAAA3U,EAAA,CAAAqE,kBAAA,CAAAoQ,GAAA,CAAArR,UAAA,EAAAe,MAAA,MAAAsQ,GAAA,CAAArR,UAAA,GAAAe,MAAA;YAAA,OAAAnE,EAAA,CAAAgB,WAAA,CAAAmD,MAAA;UAAA,EAAwB;UAE1EnE,EAFI,CAAAG,YAAA,EAAqG,EACjG,EACF;UAGFH,EAFJ,CAAAC,cAAA,eAAuB,eAC0B,uBAEjB;UAD+BD,EAAA,CAAAiE,gBAAA,2BAAA8Q,yEAAA5Q,MAAA;YAAAnE,EAAA,CAAAY,aAAA,CAAA+T,GAAA;YAAA3U,EAAA,CAAAqE,kBAAA,CAAAoQ,GAAA,CAAAjJ,aAAA,EAAArH,MAAA,MAAAsQ,GAAA,CAAAjJ,aAAA,GAAArH,MAAA;YAAA,OAAAnE,EAAA,CAAAgB,WAAA,CAAAmD,MAAA;UAAA,EAA2B;UACpFnE,EAAA,CAAAU,UAAA,oBAAAsU,kEAAA;YAAAhV,EAAA,CAAAY,aAAA,CAAA+T,GAAA;YAAA,OAAA3U,EAAA,CAAAgB,WAAA,CAAUyT,GAAA,CAAAhE,YAAA,EAAc;UAAA,EAAC;UACzBzQ,EAAA,CAAAE,MAAA,gHACF;UAAAF,EAAA,CAAAG,YAAA,EAAc;UAOdH,EANA,CAAA6C,UAAA,KAAAoS,4CAAA,qBAA8F,KAAAC,4CAAA,qBAEV,KAAAC,4CAAA,qBAEE,KAAAC,4CAAA,qBAEF;UACpFpV,EAAA,CAAAC,cAAA,oBAAqG;UAAnCD,EAAA,CAAAU,UAAA,oBAAA2U,4DAAAlR,MAAA;YAAAnE,EAAA,CAAAY,aAAA,CAAA+T,GAAA;YAAA,OAAA3U,EAAA,CAAAgB,WAAA,CAAUyT,GAAA,CAAA5F,eAAA,CAAA1K,MAAA,CAAuB;UAAA,EAAC;UAApGnE,EAAA,CAAAG,YAAA,EAAqG;UACrGH,EAAA,CAAAC,cAAA,kBAA4E;UAAvCD,EAAA,CAAAU,UAAA,mBAAA4U,4DAAA;YAAAtV,EAAA,CAAAY,aAAA,CAAA+T,GAAA;YAAA,OAAA3U,EAAA,CAAAgB,WAAA,CAASyT,GAAA,CAAAhH,0BAAA,EAA4B;UAAA,EAAC;UAACzN,EAAA,CAAAE,MAAA,6CAAO;UAAAF,EAAA,CAAAkB,SAAA,aAC9C;UAG3ClB,EAH2C,CAAAG,YAAA,EAAS,EAC1C,EACF,EACF;UAIEH,EAHR,CAAAC,cAAA,eAAmC,iBAC+D,aACvF,cAA+D,cACpC;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACrCH,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAE,MAAA,gCAAI;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACvCH,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAE,MAAA,gCAAI;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACvCH,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAE,MAAA,gCAAI;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACvCH,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAE,MAAA,gCAAI;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACvCH,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAE,MAAA,sCAAK;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACxCH,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACrCH,EAAA,CAAA6C,UAAA,KAAA0S,wCAAA,iBAAwD;UACxDvV,EAAA,CAAAC,cAAA,cAA0C;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAEhDF,EAFgD,CAAAG,YAAA,EAAK,EAC9C,EACC;UACRH,EAAA,CAAA6C,UAAA,KAAA2S,2CAAA,oBAA+D;UA4BrExV,EAFI,CAAAG,YAAA,EAAQ,EACJ,EACO;UAEbH,EADF,CAAAC,cAAA,0BAAsD,0BAEf;UADqCD,EAA1D,CAAAiE,gBAAA,kCAAAwR,mFAAAtR,MAAA;YAAAnE,EAAA,CAAAY,aAAA,CAAA+T,GAAA;YAAA3U,EAAA,CAAAqE,kBAAA,CAAAoQ,GAAA,CAAAtH,YAAA,EAAAhJ,MAAA,MAAAsQ,GAAA,CAAAtH,YAAA,GAAAhJ,MAAA;YAAA,OAAAnE,EAAA,CAAAgB,WAAA,CAAAmD,MAAA;UAAA,EAAiC,4BAAAuR,6EAAAvR,MAAA;YAAAnE,EAAA,CAAAY,aAAA,CAAA+T,GAAA;YAAA3U,EAAA,CAAAqE,kBAAA,CAAAoQ,GAAA,CAAAxH,QAAA,EAAA9I,MAAA,MAAAsQ,GAAA,CAAAxH,QAAA,GAAA9I,MAAA;YAAA,OAAAnE,EAAA,CAAAgB,WAAA,CAAAmD,MAAA;UAAA,EAAwB,wBAAAwR,yEAAAxR,MAAA;YAAAnE,EAAA,CAAAY,aAAA,CAAA+T,GAAA;YAAA3U,EAAA,CAAAqE,kBAAA,CAAAoQ,GAAA,CAAA7H,SAAA,EAAAzI,MAAA,MAAAsQ,GAAA,CAAA7H,SAAA,GAAAzI,MAAA;YAAA,OAAAnE,EAAA,CAAAgB,WAAA,CAAAmD,MAAA;UAAA,EAAqB;UAC5FnE,EAAA,CAAAU,UAAA,wBAAAiV,yEAAAxR,MAAA;YAAAnE,EAAA,CAAAY,aAAA,CAAA+T,GAAA;YAAA,OAAA3U,EAAA,CAAAgB,WAAA,CAAcyT,GAAA,CAAApH,WAAA,CAAAlJ,MAAA,CAAmB;UAAA,EAAC;UAGxCnE,EAFI,CAAAG,YAAA,EAAiB,EACF,EACT;UAgPVH,EA9OA,CAAA6C,UAAA,KAAA+S,iDAAA,kCAAA5V,EAAA,CAAA6V,sBAAA,CAAoD,KAAAC,iDAAA,kCAAA9V,EAAA,CAAA6V,sBAAA,CAwEK,KAAAE,iDAAA,iCAAA/V,EAAA,CAAA6V,sBAAA,CAmIC,KAAAG,iDAAA,iCAAAhW,EAAA,CAAA6V,sBAAA,CAmCH;;;UAjV3C7V,EAAA,CAAAO,SAAA,IAAiC;UAAjCP,EAAA,CAAAqF,gBAAA,YAAAoP,GAAA,CAAAhI,mBAAA,CAAiC;UACAzM,EAAA,CAAAO,SAAA,EAAiB;UAAjBP,EAAA,CAAAI,UAAA,YAAAqU,GAAA,CAAArJ,cAAA,CAAiB;UAmBJpL,EAAA,CAAAO,SAAA,GAAyB;UAAzBP,EAAA,CAAAqF,gBAAA,YAAAoP,GAAA,CAAA/Q,WAAA,CAAyB;UAO3B1D,EAAA,CAAAO,SAAA,GAAwB;UAAxBP,EAAA,CAAAqF,gBAAA,YAAAoP,GAAA,CAAArR,UAAA,CAAwB;UAKXpD,EAAA,CAAAO,SAAA,GAA2B;UAA3BP,EAAA,CAAAqF,gBAAA,YAAAoP,GAAA,CAAAjJ,aAAA,CAA2B;UAI7ExL,EAAA,CAAAO,SAAA,GAAmB;UAAnBP,EAAA,CAAAI,UAAA,SAAAqU,GAAA,CAAAwB,aAAA,CAAmB;UAEnBjW,EAAA,CAAAO,SAAA,EAAY;UAAZP,EAAA,CAAAI,UAAA,SAAAqU,GAAA,CAAA5Q,MAAA,CAAY;UAEZ7D,EAAA,CAAAO,SAAA,EAAc;UAAdP,EAAA,CAAAI,UAAA,SAAAqU,GAAA,CAAAyB,QAAA,CAAc;UAEYlW,EAAA,CAAAO,SAAA,EAAmB;UAAnBP,EAAA,CAAAI,UAAA,SAAAqU,GAAA,CAAA0B,aAAA,CAAmB;UAiBrBnW,EAAA,CAAAO,SAAA,IAAuB;UAAvBP,EAAA,CAAAI,UAAA,SAAAqU,GAAA,CAAAlJ,SAAA,SAAuB;UAIlDvL,EAAA,CAAAO,SAAA,GAAqD;UAArDP,EAAA,CAAAI,UAAA,SAAAqU,GAAA,CAAA1Q,YAAA,YAAA0Q,GAAA,CAAA1Q,YAAA,CAAA7B,MAAA,KAAqD;UA8BjDlC,EAAA,CAAAO,SAAA,GAAiC;UAAyBP,EAA1D,CAAAqF,gBAAA,mBAAAoP,GAAA,CAAAtH,YAAA,CAAiC,aAAAsH,GAAA,CAAAxH,QAAA,CAAwB,SAAAwH,GAAA,CAAA7H,SAAA,CAAqB;;;qBDnEtFrN,YAAY,EAAA6W,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EAAAF,EAAA,CAAAG,WAAA,EAAE3W,YAAY,EAAA4W,EAAA,CAAAC,oBAAA,EAAAD,EAAA,CAAAE,mBAAA,EAAAF,EAAA,CAAAG,eAAA,EAAAH,EAAA,CAAAI,kBAAA,EAAAJ,EAAA,CAAAK,OAAA,EAAA3D,EAAA,CAAA4D,eAAA,EAAA5D,EAAA,CAAA6D,mBAAA,EAAA7D,EAAA,CAAA8D,qBAAA,EAAA9D,EAAA,CAAA+D,qBAAA,EAAA/D,EAAA,CAAAgE,mBAAA,EAAAhE,EAAA,CAAAiE,gBAAA,EAAAjE,EAAA,CAAAkE,iBAAA,EAAAlE,EAAA,CAAAmE,iBAAA,EAAAC,EAAA,CAAAC,mBAAA,EAAAC,GAAA,CAAAC,mBAAA;MAAAC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}