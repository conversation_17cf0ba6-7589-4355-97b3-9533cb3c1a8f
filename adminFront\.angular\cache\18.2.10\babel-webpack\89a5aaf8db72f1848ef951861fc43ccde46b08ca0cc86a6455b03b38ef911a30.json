{"ast": null, "code": "import { isObservable, of } from 'rxjs';\n\n/**\n * Given either an Observable or non-Observable value, returns either the original\n * Observable, or wraps it in an Observable that emits the non-Observable value.\n */\nfunction coerceObservable(data) {\n  if (!isObservable(data)) {\n    return of(data);\n  }\n  return data;\n}\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { coerceObservable };\n//# sourceMappingURL=private.mjs.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}