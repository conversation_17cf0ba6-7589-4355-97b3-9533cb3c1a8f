{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { NbThemeModule, NbLayoutModule, NbCardModule, NbButtonModule, NbSelectModule, NbInputModule, NbCheckboxModule, NbIconModule, NbListModule, NbTagModule } from '@nebular/theme';\nimport { HouseholdBindingComponent } from './components/household-binding/household-binding.component';\nimport { HouseholdBindingDemoComponent } from './components/household-binding/household-binding-demo.component';\nimport { TestDropdownComponent } from './components/household-binding/test-dropdown.component';\nimport { SimpleDropdownTestComponent } from './components/household-binding/simple-dropdown-test.component';\nlet SharedModule = class SharedModule {};\nSharedModule = __decorate([NgModule({\n  declarations: [HouseholdBindingComponent, HouseholdBindingDemoComponent, TestDropdownComponent, SimpleDropdownTestComponent],\n  imports: [CommonModule, FormsModule, ReactiveFormsModule, NbThemeModule, NbLayoutModule, NbCardModule, NbButtonModule, NbSelectModule, NbInputModule, NbCheckboxModule, NbIconModule, NbListModule, NbTagModule],\n  exports: [HouseholdBindingComponent, HouseholdBindingDemoComponent, TestDropdownComponent, SimpleDropdownTestComponent,\n  // 也可以導出常用的模組供其他地方使用\n  CommonModule, FormsModule, ReactiveFormsModule, NbThemeModule, NbLayoutModule, NbCardModule, NbButtonModule, NbSelectModule, NbInputModule, NbCheckboxModule, NbIconModule, NbListModule, NbTagModule]\n})], SharedModule);\nexport { SharedModule };", "map": {"version": 3, "names": ["NgModule", "CommonModule", "FormsModule", "ReactiveFormsModule", "NbThemeModule", "NbLayoutModule", "NbCardModule", "NbButtonModule", "NbSelectModule", "NbInputModule", "NbCheckboxModule", "NbIconModule", "NbListModule", "NbTagModule", "HouseholdBindingComponent", "HouseholdBindingDemoComponent", "TestDropdownComponent", "SimpleDropdownTestComponent", "SharedModule", "__decorate", "declarations", "imports", "exports"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\shared\\shared.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\r\nimport {\r\n    NbThemeModule,\r\n    NbLayoutModule,\r\n    NbCardModule,\r\n    NbButtonModule,\r\n    NbSelectModule,\r\n    NbInputModule,\r\n    NbCheckboxModule,\r\n    NbIconModule,\r\n    NbListModule,\r\n    NbTagModule\r\n} from '@nebular/theme';\r\n\r\nimport { HouseholdBindingComponent } from './components/household-binding/household-binding.component';\r\nimport { HouseholdBindingDemoComponent } from './components/household-binding/household-binding-demo.component';\r\nimport { TestDropdownComponent } from './components/household-binding/test-dropdown.component';\r\nimport { SimpleDropdownTestComponent } from './components/household-binding/simple-dropdown-test.component';\r\n\r\n@NgModule({\r\n    declarations: [\r\n        HouseholdBindingComponent,\r\n        HouseholdBindingDemoComponent,\r\n        TestDropdownComponent,\r\n        SimpleDropdownTestComponent\r\n    ],\r\n    imports: [\r\n        CommonModule,\r\n        FormsModule,\r\n        ReactiveFormsModule,\r\n        NbThemeModule,\r\n        NbLayoutModule,\r\n        NbCardModule,\r\n        NbButtonModule,\r\n        NbSelectModule,\r\n        NbInputModule,\r\n        NbCheckboxModule,\r\n        NbIconModule,\r\n        NbListModule,\r\n        NbTagModule], exports: [\r\n            HouseholdBindingComponent,\r\n            HouseholdBindingDemoComponent,\r\n            TestDropdownComponent,\r\n            SimpleDropdownTestComponent,\r\n            // 也可以導出常用的模組供其他地方使用\r\n            CommonModule,\r\n            FormsModule,\r\n            ReactiveFormsModule,\r\n            NbThemeModule,\r\n            NbLayoutModule,\r\n            NbCardModule,\r\n            NbButtonModule,\r\n            NbSelectModule,\r\n            NbInputModule,\r\n            NbCheckboxModule,\r\n            NbIconModule,\r\n            NbListModule,\r\n            NbTagModule\r\n        ]\r\n})\r\nexport class SharedModule { }\r\n"], "mappings": ";AAAA,SAASA,QAAQ,QAAQ,eAAe;AACxC,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,EAAEC,mBAAmB,QAAQ,gBAAgB;AACjE,SACIC,aAAa,EACbC,cAAc,EACdC,YAAY,EACZC,cAAc,EACdC,cAAc,EACdC,aAAa,EACbC,gBAAgB,EAChBC,YAAY,EACZC,YAAY,EACZC,WAAW,QACR,gBAAgB;AAEvB,SAASC,yBAAyB,QAAQ,4DAA4D;AACtG,SAASC,6BAA6B,QAAQ,iEAAiE;AAC/G,SAASC,qBAAqB,QAAQ,wDAAwD;AAC9F,SAASC,2BAA2B,QAAQ,+DAA+D;AA2CpG,IAAMC,YAAY,GAAlB,MAAMA,YAAY,GAAI;AAAhBA,YAAY,GAAAC,UAAA,EAzCxBnB,QAAQ,CAAC;EACNoB,YAAY,EAAE,CACVN,yBAAyB,EACzBC,6BAA6B,EAC7BC,qBAAqB,EACrBC,2BAA2B,CAC9B;EACDI,OAAO,EAAE,CACLpB,YAAY,EACZC,WAAW,EACXC,mBAAmB,EACnBC,aAAa,EACbC,cAAc,EACdC,YAAY,EACZC,cAAc,EACdC,cAAc,EACdC,aAAa,EACbC,gBAAgB,EAChBC,YAAY,EACZC,YAAY,EACZC,WAAW,CAAC;EAAES,OAAO,EAAE,CACnBR,yBAAyB,EACzBC,6BAA6B,EAC7BC,qBAAqB,EACrBC,2BAA2B;EAC3B;EACAhB,YAAY,EACZC,WAAW,EACXC,mBAAmB,EACnBC,aAAa,EACbC,cAAc,EACdC,YAAY,EACZC,cAAc,EACdC,cAAc,EACdC,aAAa,EACbC,gBAAgB,EAChBC,YAAY,EACZC,YAAY,EACZC,WAAW;CAEtB,CAAC,C,EACWK,YAAY,CAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}