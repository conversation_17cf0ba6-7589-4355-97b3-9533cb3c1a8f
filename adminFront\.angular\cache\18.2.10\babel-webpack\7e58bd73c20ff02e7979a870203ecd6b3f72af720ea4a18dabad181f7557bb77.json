{"ast": null, "code": "/* tslint:disable */\n/* eslint-disable */\nimport { HttpResponse } from '@angular/common/http';\nimport { filter, map } from 'rxjs/operators';\nimport { RequestBuilder } from '../../request-builder';\nexport function apiHouseHoldDetailGetHouseHoldDetailListPost$Json(http, rootUrl, params, context) {\n  const rb = new RequestBuilder(rootUrl, apiHouseHoldDetailGetHouseHoldDetailListPost$Json.PATH, 'post');\n  if (params) {\n    rb.body(params.body, 'application/*+json');\n  }\n  return http.request(rb.build({\n    responseType: 'json',\n    accept: 'text/json',\n    context\n  })).pipe(filter(r => r instanceof HttpResponse), map(r => {\n    return r;\n  }));\n}\napiHouseHoldDetailGetHouseHoldDetailListPost$Json.PATH = '/api/HouseHoldDetail/GetHouseHoldDetailList';", "map": {"version": 3, "names": ["HttpResponse", "filter", "map", "RequestBuilder", "apiHouseHoldDetailGetHouseHoldDetailListPost$Json", "http", "rootUrl", "params", "context", "rb", "PATH", "body", "request", "build", "responseType", "accept", "pipe", "r"], "sources": ["C:\\Users\\<USER>\\Documents\\jeansalechange\\adminFront\\src\\services\\api\\fn\\house-hold-detail\\api-house-hold-detail-get-house-hold-detail-list-post-json.ts"], "sourcesContent": ["/* tslint:disable */\r\n/* eslint-disable */\r\nimport { HttpClient, HttpContext, HttpResponse } from '@angular/common/http';\r\nimport { Observable } from 'rxjs';\r\nimport { filter, map } from 'rxjs/operators';\r\nimport { StrictHttpResponse } from '../../strict-http-response';\r\nimport { RequestBuilder } from '../../request-builder';\r\n\r\nimport { HoldNameReponseListResponseBase } from '../../models/hold-name-reponse-list-response-base';\r\n\r\nexport interface ApiHouseHoldDetailGetHouseHoldDetailListPost$Json$Params {\r\n      body?: number\r\n}\r\n\r\nexport function apiHouseHoldDetailGetHouseHoldDetailListPost$Json(http: HttpClient, rootUrl: string, params?: ApiHouseHoldDetailGetHouseHoldDetailListPost$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<HoldNameReponseListResponseBase>> {\r\n  const rb = new RequestBuilder(rootUrl, apiHouseHoldDetailGetHouseHoldDetailListPost$Json.PATH, 'post');\r\n  if (params) {\r\n    rb.body(params.body, 'application/*+json');\r\n  }\r\n\r\n  return http.request(\r\n    rb.build({ responseType: 'json', accept: 'text/json', context })\r\n  ).pipe(\r\n    filter((r: any): r is HttpResponse<any> => r instanceof HttpResponse),\r\n    map((r: HttpResponse<any>) => {\r\n      return r as StrictHttpResponse<HoldNameReponseListResponseBase>;\r\n    })\r\n  );\r\n}\r\n\r\napiHouseHoldDetailGetHouseHoldDetailListPost$Json.PATH = '/api/HouseHoldDetail/GetHouseHoldDetailList';\r\n"], "mappings": "AAAA;AACA;AACA,SAAkCA,YAAY,QAAQ,sBAAsB;AAE5E,SAASC,MAAM,EAAEC,GAAG,QAAQ,gBAAgB;AAE5C,SAASC,cAAc,QAAQ,uBAAuB;AAQtD,OAAM,SAAUC,iDAAiDA,CAACC,IAAgB,EAAEC,OAAe,EAAEC,MAAiE,EAAEC,OAAqB;EAC3L,MAAMC,EAAE,GAAG,IAAIN,cAAc,CAACG,OAAO,EAAEF,iDAAiD,CAACM,IAAI,EAAE,MAAM,CAAC;EACtG,IAAIH,MAAM,EAAE;IACVE,EAAE,CAACE,IAAI,CAACJ,MAAM,CAACI,IAAI,EAAE,oBAAoB,CAAC;EAC5C;EAEA,OAAON,IAAI,CAACO,OAAO,CACjBH,EAAE,CAACI,KAAK,CAAC;IAAEC,YAAY,EAAE,MAAM;IAAEC,MAAM,EAAE,WAAW;IAAEP;EAAO,CAAE,CAAC,CACjE,CAACQ,IAAI,CACJf,MAAM,CAAEgB,CAAM,IAA6BA,CAAC,YAAYjB,YAAY,CAAC,EACrEE,GAAG,CAAEe,CAAoB,IAAI;IAC3B,OAAOA,CAAwD;EACjE,CAAC,CAAC,CACH;AACH;AAEAb,iDAAiD,CAACM,IAAI,GAAG,6CAA6C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}