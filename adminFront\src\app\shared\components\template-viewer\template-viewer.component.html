<nb-card style="width: 90vw; max-width: 1200px; height: 80vh;">
  <nb-card-header>
    <div class="d-flex justify-content-between align-items-center">
      <h5 class="mb-0">模板管理</h5>
      <button class="btn btn-success btn-sm" (click)="onAddTemplate()">
        <i class="fas fa-plus mr-1"></i>新增模板
      </button>
    </div>
  </nb-card-header>
  <nb-card-body style="overflow: auto;">
    <!-- 搜尋功能 -->
    <div class="search-container mb-3">
      <div class="input-group">
        <input type="text" class="form-control" placeholder="搜尋模板名稱或描述..." [(ngModel)]="searchKeyword"
          (input)="onSearch()" (keyup.enter)="onSearch()">
        <div class="input-group-append">
          <button class="btn btn-outline-secondary" type="button" (click)="onSearch()">
            <i class="fas fa-search"></i>
          </button>
          <button class="btn btn-outline-secondary" type="button" (click)="clearSearch()" *ngIf="searchKeyword">
            <i class="fas fa-times"></i>
          </button>
        </div>
      </div>
    </div>

    <!-- 新增模板表單 -->
    <div *ngIf="showAddForm" class="add-template-form mb-4">
      <div class="form-container">
        <div class="form-header">
          <div class="form-title">
            <i class="fas fa-plus"></i>
            <span>新增模板</span>
          </div>
        </div>

        <form (ngSubmit)="saveNewTemplate()" class="form-content">
          <div class="input-row">
            <div class="input-group">
              <label class="input-label">
                模板名稱 <span class="required">*</span>
              </label>
              <input type="text" class="input-field" [(ngModel)]="newTemplate.name" name="templateName"
                placeholder="請輸入模板名稱" required maxlength="50">
            </div>
            <div class="input-group">
              <label class="input-label">模板描述</label>
              <input type="text" class="input-field" [(ngModel)]="newTemplate.description" name="templateDescription"
                placeholder="請輸入模板描述（可選）" maxlength="100">
            </div>
          </div>

          <div class="input-group full-width">
            <label class="input-label">
              選擇要加入模板的項目 <span class="required">*</span>
            </label>
            <div class="items-selector">
              <div *ngIf="availableData.length === 0" class="empty-items">
                <i class="fas fa-info-circle"></i>
                <span>暫無可選項目</span>
              </div>
              <div *ngFor="let item of availableData; let i = index" class="item-option">
                <label class="item-label">
                  <input type="checkbox" class="item-checkbox" [(ngModel)]="item.selected" name="item_{{i}}">
                  <div class="item-content">
                    <div class="item-title">{{ item.CRequirement || item.name }}</div>
                    <div class="item-desc">{{ item.CGroupName || item.description }}</div>
                  </div>
                </label>
              </div>
            </div>
          </div>

          <div class="form-actions">
            <button type="button" class="btn-cancel" (click)="cancelAddTemplate()">
              取消
            </button>
            <button type="submit" class="btn-save">
              儲存模板
            </button>
          </div>
        </form>
      </div>
    </div>

    <!-- 模板列表（僅在未選擇模板細節時顯示） -->
    <div class="template-list" *ngIf="!selectedTemplate">
      <!-- 搜尋結果統計 -->
      <div class="search-results-info mb-2" *ngIf="searchKeyword">
        <small class="text-muted">
          找到 {{ filteredTemplates.length }} 個符合「{{ searchKeyword }}」的模板
        </small>
      </div>

      <!-- 分頁資訊 -->
      <div class="pagination-info mb-2" *ngIf="templatePagination.totalItems > 0">
        <small class="text-muted">
          顯示第 {{ (templatePagination.currentPage - 1) * templatePagination.pageSize + 1 }} -
          {{ Math.min(templatePagination.currentPage * templatePagination.pageSize, templatePagination.totalItems) }} 項，
          共 {{ templatePagination.totalItems }} 項模板
        </small>
      </div>

      <div class="table-responsive">
        <table class="table table-striped table-hover">
          <thead class="thead-light">
            <tr>
              <th width="30%">模板名稱</th>
              <th width="50%">描述</th>
              <th width="20%" class="text-center">操作</th>
            </tr>
          </thead>
          <tbody>
            <tr *ngFor="let tpl of paginatedTemplates; trackBy: trackByTemplateId">
              <td>
                <strong>{{ tpl.TemplateName }}</strong>
              </td>
              <td>
                <span class="text-muted">{{ tpl.Description || '無描述' }}</span>
              </td>
              <td class="text-center">
                <div class="btn-group btn-group-sm" role="group">
                  <button class="btn btn-info" (click)="onSelectTemplate(tpl)" title="查看詳情">
                    <i class="fas fa-eye"></i> 查看
                  </button>
                  <button class="btn btn-danger" (click)="tpl.TemplateID && onDeleteTemplate(tpl.TemplateID)"
                    *ngIf="tpl.TemplateID" title="刪除模板">
                    <i class="fas fa-trash"></i> 刪除
                  </button>
                </div>
              </td>
            </tr>
            <tr *ngIf="!paginatedTemplates || paginatedTemplates.length === 0">
              <td colspan="3" class="text-center py-4">
                <div class="empty-state">
                  <i class="fas fa-folder-open fa-2x text-muted mb-2"></i>
                  <p class="text-muted mb-0">
                    {{ searchKeyword ? '找不到符合條件的模板' : '暫無模板' }}
                  </p>
                  <small class="text-muted" *ngIf="searchKeyword">
                    請嘗試其他關鍵字或 <a href="javascript:void(0)" (click)="clearSearch()">清除搜尋</a>
                  </small>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- 模板列表分頁控制器 -->
      <div class="pagination-container mt-3" *ngIf="templatePagination.totalPages > 1">
        <nav aria-label="模板列表分頁">
          <ul class="pagination pagination-sm justify-content-center mb-0">
            <!-- 上一頁 -->
            <li class="page-item" [class.disabled]="templatePagination.currentPage === 1">
              <button class="page-link" (click)="goToTemplatePage(templatePagination.currentPage - 1)"
                [disabled]="templatePagination.currentPage === 1">
                <i class="fas fa-chevron-left"></i>
              </button>
            </li>

            <!-- 頁碼 -->
            <li class="page-item" *ngFor="let page of getTemplatePageNumbers()"
              [class.active]="page === templatePagination.currentPage">
              <button class="page-link" (click)="goToTemplatePage(page)">{{ page }}</button>
            </li>

            <!-- 下一頁 -->
            <li class="page-item" [class.disabled]="templatePagination.currentPage === templatePagination.totalPages">
              <button class="page-link" (click)="goToTemplatePage(templatePagination.currentPage + 1)"
                [disabled]="templatePagination.currentPage === templatePagination.totalPages">
                <i class="fas fa-chevron-right"></i>
              </button>
            </li>
          </ul>
        </nav>
      </div>
    </div>

    <!-- 查看模板細節 -->
    <div *ngIf="selectedTemplate" class="template-detail-modal">
      <div class="template-detail-header d-flex justify-content-between align-items-center">
        <h6 class="mb-0">
          <i class="fas fa-file-alt mr-2"></i>
          模板細節：{{ selectedTemplate!.TemplateName }}
        </h6>
        <button class="btn btn-outline-secondary btn-sm" (click)="closeTemplateDetail()">
          <i class="fas fa-times"></i> 關閉
        </button>
      </div>

      <div class="template-detail-content">
        <div *ngIf="selectedTemplate.Description" class="template-description mb-3">
          <strong>描述：</strong>
          <span class="text-muted">{{ selectedTemplate.Description }}</span>
        </div>

        <div class="template-items">
          <div class="d-flex justify-content-between align-items-center mb-2">
            <h6 class="mb-0">
              <i class="fas fa-list mr-1"></i>
              模板內容 ({{ currentTemplateDetails.length }} 項)
            </h6>
            <small class="text-muted" *ngIf="detailPagination.totalPages > 1">
              第 {{ detailPagination.currentPage }} / {{ detailPagination.totalPages }} 頁
            </small>
          </div>

          <div *ngIf="currentTemplateDetails.length > 0; else noDetails" class="detail-list">
            <div *ngFor="let detail of paginatedDetails; let i = index"
              class="detail-item d-flex align-items-center py-2 border-bottom">
              <div class="detail-index">
                <span class="badge badge-light">{{ (detailPagination.currentPage - 1) * detailPagination.pageSize + i +
                  1 }}</span>
              </div>
              <div class="detail-content flex-grow-1 ml-2">
                <div class="detail-field">
                  <strong>{{ detail.FieldName }}:</strong>
                </div>
                <div class="detail-value text-muted">
                  {{ detail.FieldValue }}
                </div>
              </div>
            </div>
          </div>

          <!-- 詳情分頁控制器 -->
          <div class="detail-pagination mt-3" *ngIf="detailPagination.totalPages > 1">
            <nav aria-label="模板詳情分頁">
              <ul class="pagination pagination-sm justify-content-center mb-0">
                <!-- 上一頁 -->
                <li class="page-item" [class.disabled]="detailPagination.currentPage === 1">
                  <button class="page-link" (click)="goToDetailPage(detailPagination.currentPage - 1)"
                    [disabled]="detailPagination.currentPage === 1">
                    <i class="fas fa-chevron-left"></i>
                  </button>
                </li>

                <!-- 頁碼 -->
                <li class="page-item" *ngFor="let page of getDetailPageNumbers()"
                  [class.active]="page === detailPagination.currentPage">
                  <button class="page-link" (click)="goToDetailPage(page)">{{ page }}</button>
                </li>

                <!-- 下一頁 -->
                <li class="page-item" [class.disabled]="detailPagination.currentPage === detailPagination.totalPages">
                  <button class="page-link" (click)="goToDetailPage(detailPagination.currentPage + 1)"
                    [disabled]="detailPagination.currentPage === detailPagination.totalPages">
                    <i class="fas fa-chevron-right"></i>
                  </button>
                </li>
              </ul>
            </nav>
          </div>

          <ng-template #noDetails>
            <div class="text-center py-3">
              <i class="fas fa-inbox fa-2x text-muted mb-2"></i>
              <p class="text-muted mb-0">此模板暫無內容</p>
            </div>
          </ng-template>
        </div>
      </div>
    </div>
  </nb-card-body>
  <nb-card-footer>
    <div class="text-center">
      <button class="btn btn-secondary" (click)="onClose()">
        <i class="fas fa-times mr-1"></i>關閉
      </button>
    </div>
  </nb-card-footer>
</nb-card>