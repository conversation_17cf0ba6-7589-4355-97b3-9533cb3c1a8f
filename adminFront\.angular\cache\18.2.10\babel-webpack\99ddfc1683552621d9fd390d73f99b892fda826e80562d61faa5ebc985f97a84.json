{"ast": null, "code": "import { BehaviorSubject } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nexport class SharedObservable {\n  constructor() {\n    this.Menu = new BehaviorSubject({});\n    this.User = new BehaviorSubject([]);\n    this.UserGroup = new BehaviorSubject([]);\n    this.UserGroupFunction = new BehaviorSubject({});\n    this.UserLog = new BehaviorSubject([]);\n    this.FunctionModel = new BehaviorSubject([]);\n    this.RefBase = new BehaviorSubject([]);\n    this.Tag = new BehaviorSubject([]);\n    this.Member = new BehaviorSubject([]);\n    this.MemberTag = new BehaviorSubject([]);\n    this.Task = new BehaviorSubject({});\n    this.MenuTab = new BehaviorSubject({});\n    this.SharedMenu = this.Menu.asObservable();\n    this.SharedUser = this.User.asObservable();\n    this.SharedUserGroup = this.UserGroup.asObservable();\n    this.SharedUserGroupFunction = this.UserGroupFunction.asObservable();\n    this.SharedUserLog = this.UserLog.asObservable();\n    this.SharedFunctionModel = this.FunctionModel.asObservable();\n    this.SharedRefBase = this.RefBase.asObservable();\n    this.SharedTag = this.Tag.asObservable();\n    this.SharedMember = this.Member.asObservable();\n    this.SharedMemberTag = this.MemberTag.asObservable();\n    this.SharedTask = this.Task.asObservable();\n    this.SharedMenuTab = this.MenuTab.asObservable();\n  }\n  SetMenu(Data) {\n    this.Menu.next(Data);\n  }\n  SetUser(Data) {\n    this.User.next(Data);\n  }\n  SetUserGroup(Data) {\n    this.UserGroup.next(Data);\n  }\n  SetUserGroupFunction(Data) {\n    this.UserGroupFunction.next(Data);\n  }\n  SetUserLog(Data) {\n    this.UserLog.next(Data);\n  }\n  SetFunctionModel(Data) {\n    this.FunctionModel.next(Data);\n  }\n  SetRefBase(Data) {\n    this.RefBase.next(Data);\n  }\n  SetTag(Data) {\n    this.Tag.next(Data);\n  }\n  SetMember(Data) {\n    this.Member.next(Data);\n  }\n  SetMemberTag(Data) {\n    this.MemberTag.next(Data);\n  }\n  SetTask(Data) {\n    this.Task.next(Data);\n  }\n  SetMenuTab(Data) {\n    this.MenuTab.next(Data);\n  }\n  static {\n    this.ɵfac = function SharedObservable_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || SharedObservable)();\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: SharedObservable,\n      factory: SharedObservable.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["BehaviorSubject", "SharedObservable", "constructor", "<PERSON><PERSON>", "User", "UserGroup", "UserGroupFunction", "UserLog", "FunctionModel", "RefBase", "Tag", "Member", "MemberTag", "Task", "MenuTab", "SharedMenu", "asObservable", "SharedUser", "SharedUserGroup", "SharedUserGroupFunction", "SharedUserLog", "SharedFunctionModel", "SharedRefBase", "SharedTag", "SharedMember", "SharedMemberTag", "SharedTask", "SharedMenuTab", "SetMenu", "Data", "next", "SetUser", "SetUserGroup", "SetUserGroupFunction", "SetUserLog", "SetFunctionModel", "SetRefBase", "SetTag", "SetMember", "SetMemberTag", "SetTask", "SetMenuTab", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\components\\shared.observable.ts"], "sourcesContent": ["import { FunctionModel } from 'src/app/shared/model/function.model';\r\nimport { UserGroupFunction } from 'src/app/shared/model/userGroupFunction.model';\r\nimport { Injectable } from '@angular/core';\r\nimport { BehaviorSubject } from 'rxjs';\r\nimport { UserGroup } from 'src/app/shared/model/userGroup.model';\r\nimport { User } from 'src/app/shared/model/user.model';\r\nimport { UserLog } from 'src/app/shared/model/userLog.model';\r\nimport { RefBase } from 'src/app/shared/model/refBase.model';\r\nimport { Tag } from 'src/app/shared/model/tag.model';\r\nimport { Member } from 'src/app/shared/model/member.model';\r\nimport { MemberTag } from 'src/app/shared/model/memberTag.model';\r\nimport { Task } from 'src/app/shared/model/task.model';\r\nimport { Menu } from 'src/app/shared/model/menu.model';\r\nimport { EnumResponse, GetMenuResponse, UserGetListResponse, UserGetUserLogResponse, UserGroupGetDataResponse, UserGroupGetListResponse } from 'src/services/api/models';\r\nimport { NbMenuItem } from '@nebular/theme';\r\n\r\n\r\n@Injectable({\r\n  providedIn: 'root',\r\n})\r\nexport class SharedObservable {\r\n\r\n  private Menu = new BehaviorSubject({} as GetMenuResponse);\r\n  private User = new BehaviorSubject([] as UserGetListResponse[]);\r\n  private UserGroup = new BehaviorSubject([] as UserGroupGetListResponse[]);\r\n  private UserGroupFunction = new BehaviorSubject({} as UserGroupGetDataResponse);\r\n  private UserLog = new BehaviorSubject([] as UserGetUserLogResponse[]);\r\n  private FunctionModel = new BehaviorSubject([] as EnumResponse[]);\r\n  private RefBase = new BehaviorSubject([] as RefBase[]);\r\n  private Tag = new BehaviorSubject([] as Tag[]);\r\n  private Member = new BehaviorSubject([] as Member[]);\r\n  private MemberTag = new BehaviorSubject([] as MemberTag[]);\r\n  private Task = new BehaviorSubject({} as Task);\r\n  private MenuTab = new BehaviorSubject({} as NbMenuItem[])\r\n\r\n  SharedMenu = this.Menu.asObservable();\r\n  SharedUser = this.User.asObservable();\r\n  SharedUserGroup = this.UserGroup.asObservable();\r\n  SharedUserGroupFunction = this.UserGroupFunction.asObservable();\r\n  SharedUserLog = this.UserLog.asObservable();\r\n  SharedFunctionModel = this.FunctionModel.asObservable();\r\n  SharedRefBase = this.RefBase.asObservable();\r\n  SharedTag = this.Tag.asObservable();\r\n  SharedMember = this.Member.asObservable();\r\n  SharedMemberTag = this.MemberTag.asObservable();\r\n  SharedTask = this.Task.asObservable();\r\n  SharedMenuTab = this.MenuTab.asObservable();\r\n\r\n  SetMenu(Data: GetMenuResponse) {\r\n    this.Menu.next(Data);\r\n  }\r\n  SetUser(Data: UserGetListResponse[]) {\r\n    this.User.next(Data);\r\n  }\r\n  SetUserGroup(Data: UserGroupGetListResponse[]) {\r\n    this.UserGroup.next(Data);\r\n  }\r\n  SetUserGroupFunction(Data: UserGroupGetDataResponse) {\r\n    this.UserGroupFunction.next(Data);\r\n  }\r\n  SetUserLog(Data: UserLog[]) {\r\n    this.UserLog.next(Data);\r\n  }\r\n  SetFunctionModel(Data: EnumResponse[]) {\r\n    this.FunctionModel.next(Data);\r\n  }\r\n  SetRefBase(Data: RefBase[]) {\r\n    this.RefBase.next(Data);\r\n  }\r\n  SetTag(Data: Tag[]) {\r\n    this.Tag.next(Data);\r\n  }\r\n  SetMember(Data: Member[]) {\r\n    this.Member.next(Data);\r\n  }\r\n  SetMemberTag(Data: MemberTag[]) {\r\n    this.MemberTag.next(Data);\r\n  }\r\n  SetTask(Data: Task) {\r\n    this.Task.next(Data);\r\n  }\r\n  SetMenuTab(Data: NbMenuItem[]) {\r\n    this.MenuTab.next(Data);\r\n  }\r\n\r\n}\r\n"], "mappings": "AAGA,SAASA,eAAe,QAAQ,MAAM;;AAiBtC,OAAM,MAAOC,gBAAgB;EAH7BC,YAAA;IAKU,KAAAC,IAAI,GAAG,IAAIH,eAAe,CAAC,EAAqB,CAAC;IACjD,KAAAI,IAAI,GAAG,IAAIJ,eAAe,CAAC,EAA2B,CAAC;IACvD,KAAAK,SAAS,GAAG,IAAIL,eAAe,CAAC,EAAgC,CAAC;IACjE,KAAAM,iBAAiB,GAAG,IAAIN,eAAe,CAAC,EAA8B,CAAC;IACvE,KAAAO,OAAO,GAAG,IAAIP,eAAe,CAAC,EAA8B,CAAC;IAC7D,KAAAQ,aAAa,GAAG,IAAIR,eAAe,CAAC,EAAoB,CAAC;IACzD,KAAAS,OAAO,GAAG,IAAIT,eAAe,CAAC,EAAe,CAAC;IAC9C,KAAAU,GAAG,GAAG,IAAIV,eAAe,CAAC,EAAW,CAAC;IACtC,KAAAW,MAAM,GAAG,IAAIX,eAAe,CAAC,EAAc,CAAC;IAC5C,KAAAY,SAAS,GAAG,IAAIZ,eAAe,CAAC,EAAiB,CAAC;IAClD,KAAAa,IAAI,GAAG,IAAIb,eAAe,CAAC,EAAU,CAAC;IACtC,KAAAc,OAAO,GAAG,IAAId,eAAe,CAAC,EAAkB,CAAC;IAEzD,KAAAe,UAAU,GAAG,IAAI,CAACZ,IAAI,CAACa,YAAY,EAAE;IACrC,KAAAC,UAAU,GAAG,IAAI,CAACb,IAAI,CAACY,YAAY,EAAE;IACrC,KAAAE,eAAe,GAAG,IAAI,CAACb,SAAS,CAACW,YAAY,EAAE;IAC/C,KAAAG,uBAAuB,GAAG,IAAI,CAACb,iBAAiB,CAACU,YAAY,EAAE;IAC/D,KAAAI,aAAa,GAAG,IAAI,CAACb,OAAO,CAACS,YAAY,EAAE;IAC3C,KAAAK,mBAAmB,GAAG,IAAI,CAACb,aAAa,CAACQ,YAAY,EAAE;IACvD,KAAAM,aAAa,GAAG,IAAI,CAACb,OAAO,CAACO,YAAY,EAAE;IAC3C,KAAAO,SAAS,GAAG,IAAI,CAACb,GAAG,CAACM,YAAY,EAAE;IACnC,KAAAQ,YAAY,GAAG,IAAI,CAACb,MAAM,CAACK,YAAY,EAAE;IACzC,KAAAS,eAAe,GAAG,IAAI,CAACb,SAAS,CAACI,YAAY,EAAE;IAC/C,KAAAU,UAAU,GAAG,IAAI,CAACb,IAAI,CAACG,YAAY,EAAE;IACrC,KAAAW,aAAa,GAAG,IAAI,CAACb,OAAO,CAACE,YAAY,EAAE;;EAE3CY,OAAOA,CAACC,IAAqB;IAC3B,IAAI,CAAC1B,IAAI,CAAC2B,IAAI,CAACD,IAAI,CAAC;EACtB;EACAE,OAAOA,CAACF,IAA2B;IACjC,IAAI,CAACzB,IAAI,CAAC0B,IAAI,CAACD,IAAI,CAAC;EACtB;EACAG,YAAYA,CAACH,IAAgC;IAC3C,IAAI,CAACxB,SAAS,CAACyB,IAAI,CAACD,IAAI,CAAC;EAC3B;EACAI,oBAAoBA,CAACJ,IAA8B;IACjD,IAAI,CAACvB,iBAAiB,CAACwB,IAAI,CAACD,IAAI,CAAC;EACnC;EACAK,UAAUA,CAACL,IAAe;IACxB,IAAI,CAACtB,OAAO,CAACuB,IAAI,CAACD,IAAI,CAAC;EACzB;EACAM,gBAAgBA,CAACN,IAAoB;IACnC,IAAI,CAACrB,aAAa,CAACsB,IAAI,CAACD,IAAI,CAAC;EAC/B;EACAO,UAAUA,CAACP,IAAe;IACxB,IAAI,CAACpB,OAAO,CAACqB,IAAI,CAACD,IAAI,CAAC;EACzB;EACAQ,MAAMA,CAACR,IAAW;IAChB,IAAI,CAACnB,GAAG,CAACoB,IAAI,CAACD,IAAI,CAAC;EACrB;EACAS,SAASA,CAACT,IAAc;IACtB,IAAI,CAAClB,MAAM,CAACmB,IAAI,CAACD,IAAI,CAAC;EACxB;EACAU,YAAYA,CAACV,IAAiB;IAC5B,IAAI,CAACjB,SAAS,CAACkB,IAAI,CAACD,IAAI,CAAC;EAC3B;EACAW,OAAOA,CAACX,IAAU;IAChB,IAAI,CAAChB,IAAI,CAACiB,IAAI,CAACD,IAAI,CAAC;EACtB;EACAY,UAAUA,CAACZ,IAAkB;IAC3B,IAAI,CAACf,OAAO,CAACgB,IAAI,CAACD,IAAI,CAAC;EACzB;;;uCA/DW5B,gBAAgB;IAAA;EAAA;;;aAAhBA,gBAAgB;MAAAyC,OAAA,EAAhBzC,gBAAgB,CAAA0C,IAAA;MAAAC,UAAA,EAFf;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}