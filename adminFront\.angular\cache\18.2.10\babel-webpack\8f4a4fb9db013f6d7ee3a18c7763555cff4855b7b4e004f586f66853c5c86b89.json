{"ast": null, "code": "import toDate from \"../toDate/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name getQuarter\n * @category Quarter Helpers\n * @summary Get the year quarter of the given date.\n *\n * @description\n * Get the year quarter of the given date.\n *\n * @param {Date|Number} date - the given date\n * @returns {Number} the quarter\n * @throws {TypeError} 1 argument required\n *\n * @example\n * // Which quarter is 2 July 2014?\n * const result = getQuarter(new Date(2014, 6, 2))\n * //=> 3\n */\nexport default function getQuarter(dirtyDate) {\n  requiredArgs(1, arguments);\n  var date = toDate(dirtyDate);\n  var quarter = Math.floor(date.getMonth() / 3) + 1;\n  return quarter;\n}", "map": {"version": 3, "names": ["toDate", "requiredArgs", "getQuarter", "dirtyDate", "arguments", "date", "quarter", "Math", "floor", "getMonth"], "sources": ["C:/Users/<USER>/Documents/jeansalechange/adminFront/node_modules/date-fns/esm/getQuarter/index.js"], "sourcesContent": ["import toDate from \"../toDate/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name getQuarter\n * @category Quarter Helpers\n * @summary Get the year quarter of the given date.\n *\n * @description\n * Get the year quarter of the given date.\n *\n * @param {Date|Number} date - the given date\n * @returns {Number} the quarter\n * @throws {TypeError} 1 argument required\n *\n * @example\n * // Which quarter is 2 July 2014?\n * const result = getQuarter(new Date(2014, 6, 2))\n * //=> 3\n */\nexport default function getQuarter(dirtyDate) {\n  requiredArgs(1, arguments);\n  var date = toDate(dirtyDate);\n  var quarter = Math.floor(date.getMonth() / 3) + 1;\n  return quarter;\n}"], "mappings": "AAAA,OAAOA,MAAM,MAAM,oBAAoB;AACvC,OAAOC,YAAY,MAAM,+BAA+B;AACxD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,SAASC,UAAUA,CAACC,SAAS,EAAE;EAC5CF,YAAY,CAAC,CAAC,EAAEG,SAAS,CAAC;EAC1B,IAAIC,IAAI,GAAGL,MAAM,CAACG,SAAS,CAAC;EAC5B,IAAIG,OAAO,GAAGC,IAAI,CAACC,KAAK,CAACH,IAAI,CAACI,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC;EACjD,OAAOH,OAAO;AAChB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}