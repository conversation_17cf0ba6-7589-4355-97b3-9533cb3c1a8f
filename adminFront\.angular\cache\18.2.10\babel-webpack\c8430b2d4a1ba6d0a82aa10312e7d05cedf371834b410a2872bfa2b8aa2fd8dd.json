{"ast": null, "code": "import { EnumAllowType } from '../enum/enumAllowType';\nimport { LocalStorageService } from '../services/local-storage.service';\nimport { STORAGE_KEY } from '../constant/constant';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nexport class AllowHelper {\n  constructor(router) {\n    this.router = router;\n    this.allow = [];\n  }\n  isCreate() {\n    this.getAllow();\n    return this.allow.filter(x => this.router.url.indexOf(x.CPageUrl) !== -1 && x.CCompetenceType === EnumAllowType.Create).length > 0;\n  }\n  isUpdate() {\n    this.getAllow();\n    return this.allow.filter(x => this.router.url.indexOf(x.CPageUrl) !== -1 && x.CCompetenceType === EnumAllowType.Update || x.CCompetenceType === EnumAllowType.Approve).length > 0;\n  }\n  isDelete() {\n    this.getAllow();\n    return this.allow.filter(x => this.router.url.indexOf(x.CPageUrl) !== -1 && x.CCompetenceType === EnumAllowType.Delete).length > 0;\n  }\n  isRead() {\n    this.getAllow();\n    return this.allow.filter(x => this.router.url.indexOf(x.CPageUrl) !== -1 && x.CCompetenceType === EnumAllowType.Read).length > 0;\n  }\n  isExcelImport() {\n    this.getAllow();\n    return this.allow.filter(x => this.router.url.indexOf(x.CPageUrl) !== -1 && x.CCompetenceType === EnumAllowType.ExcelImport).length > 0;\n  }\n  isExcelExport() {\n    this.getAllow();\n    return this.allow.filter(x => this.router.url.indexOf(x.CPageUrl) !== -1 && x.CCompetenceType === EnumAllowType.ExcelExport).length > 0;\n  }\n  isReport() {\n    this.getAllow();\n    return this.allow.filter(x => this.router.url.indexOf(x.CPageUrl) !== -1 && x.CCompetenceType === EnumAllowType.Report).length > 0;\n  }\n  isApiImport() {\n    this.getAllow();\n    return this.allow.filter(x => this.router.url.indexOf(x.CPageUrl) !== -1 && x.CCompetenceType === EnumAllowType.ApiImport).length > 0;\n  }\n  getAllow() {\n    const allowJson = LocalStorageService.GetLocalStorage(STORAGE_KEY.ALLOW);\n    if (allowJson !== null && allowJson !== '') {\n      this.allow = JSON.parse(allowJson);\n    }\n  }\n  static {\n    this.ɵfac = function AllowHelper_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || AllowHelper)(i0.ɵɵinject(i1.Router));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: AllowHelper,\n      factory: AllowHelper.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["EnumAllowType", "LocalStorageService", "STORAGE_KEY", "AllowHelper", "constructor", "router", "allow", "isCreate", "getAllow", "filter", "x", "url", "indexOf", "CPageUrl", "CCompetenceType", "Create", "length", "isUpdate", "Update", "Approve", "isDelete", "Delete", "isRead", "Read", "isExcelImport", "ExcelImport", "isExcelExport", "ExcelExport", "isReport", "Report", "isApiImport", "ApiImport", "<PERSON><PERSON><PERSON>", "GetLocalStorage", "ALLOW", "JSON", "parse", "i0", "ɵɵinject", "i1", "Router", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\Documents\\jeansalechange\\adminFront\\src\\app\\shared\\helper\\allowHelper.ts"], "sourcesContent": ["import { filter } from 'rxjs/operators';\r\nimport { Injectable } from '@angular/core';\r\nimport { Router } from '@angular/router';\r\nimport { EnumAllowType } from '../enum/enumAllowType';\r\nimport { MenuAllow } from '../model/menu.model';\r\nimport { LocalStorageService } from '../services/local-storage.service';\r\nimport { STORAGE_KEY } from '../constant/constant';\r\n\r\n@Injectable({ providedIn: 'root' })\r\nexport class AllowHelper {\r\n\r\n  allow = [] as MenuAllow[];\r\n  constructor(private router: Router) {\r\n\r\n\r\n  }\r\n  isCreate() {\r\n    this.getAllow();\r\n    return this.allow.filter(x => this.router.url.indexOf(x.CPageUrl)\r\n      !== -1 && x.CCompetenceType === EnumAllowType.Create).length > 0;\r\n  }\r\n  isUpdate() {\r\n    this.getAllow();\r\n    return this.allow.filter(x => this.router.url.indexOf(x.CPageUrl)\r\n      !== -1 && x.CCompetenceType === EnumAllowType.Update || x.CCompetenceType === EnumAllowType.Approve).length > 0;\r\n  }\r\n  isDelete() {\r\n    this.getAllow();\r\n    return this.allow.filter(x => this.router.url.indexOf(x.CPageUrl)\r\n      !== -1 && x.CCompetenceType === EnumAllowType.Delete).length > 0;\r\n  }\r\n  isRead() {\r\n    this.getAllow();\r\n    return this.allow.filter(x => this.router.url.indexOf(x.CPageUrl)\r\n      !== -1 && x.CCompetenceType === EnumAllowType.Read).length > 0;\r\n  }\r\n  isExcelImport() {\r\n    this.getAllow();\r\n    return this.allow.filter(x => this.router.url.indexOf(x.CPageUrl)\r\n      !== -1 && x.CCompetenceType === EnumAllowType.ExcelImport).length > 0;\r\n  }\r\n  isExcelExport() {\r\n    this.getAllow();\r\n    return this.allow.filter(x => this.router.url.indexOf(x.CPageUrl)\r\n      !== -1 && x.CCompetenceType === EnumAllowType.ExcelExport).length > 0;\r\n  }\r\n  isReport() {\r\n    this.getAllow();\r\n    return this.allow.filter(x => this.router.url.indexOf(x.CPageUrl)\r\n      !== -1 && x.CCompetenceType === EnumAllowType.Report).length > 0;\r\n  }\r\n  isApiImport() {\r\n    this.getAllow();\r\n    return this.allow.filter(x => this.router.url.indexOf(x.CPageUrl)\r\n      !== -1 && x.CCompetenceType === EnumAllowType.ApiImport).length > 0;\r\n  }\r\n\r\n  getAllow() {\r\n    const allowJson = LocalStorageService.GetLocalStorage(STORAGE_KEY.ALLOW);\r\n    if (allowJson !== null && allowJson !== '') {\r\n      this.allow = JSON.parse(allowJson);\r\n    }\r\n  }\r\n}\r\n"], "mappings": "AAGA,SAASA,aAAa,QAAQ,uBAAuB;AAErD,SAASC,mBAAmB,QAAQ,mCAAmC;AACvE,SAASC,WAAW,QAAQ,sBAAsB;;;AAGlD,OAAM,MAAOC,WAAW;EAGtBC,YAAoBC,MAAc;IAAd,KAAAA,MAAM,GAANA,MAAM;IAD1B,KAAAC,KAAK,GAAG,EAAiB;EAIzB;EACAC,QAAQA,CAAA;IACN,IAAI,CAACC,QAAQ,EAAE;IACf,OAAO,IAAI,CAACF,KAAK,CAACG,MAAM,CAACC,CAAC,IAAI,IAAI,CAACL,MAAM,CAACM,GAAG,CAACC,OAAO,CAACF,CAAC,CAACG,QAAQ,CAAC,KAC3D,CAAC,CAAC,IAAIH,CAAC,CAACI,eAAe,KAAKd,aAAa,CAACe,MAAM,CAAC,CAACC,MAAM,GAAG,CAAC;EACpE;EACAC,QAAQA,CAAA;IACN,IAAI,CAACT,QAAQ,EAAE;IACf,OAAO,IAAI,CAACF,KAAK,CAACG,MAAM,CAACC,CAAC,IAAI,IAAI,CAACL,MAAM,CAACM,GAAG,CAACC,OAAO,CAACF,CAAC,CAACG,QAAQ,CAAC,KAC3D,CAAC,CAAC,IAAIH,CAAC,CAACI,eAAe,KAAKd,aAAa,CAACkB,MAAM,IAAIR,CAAC,CAACI,eAAe,KAAKd,aAAa,CAACmB,OAAO,CAAC,CAACH,MAAM,GAAG,CAAC;EACnH;EACAI,QAAQA,CAAA;IACN,IAAI,CAACZ,QAAQ,EAAE;IACf,OAAO,IAAI,CAACF,KAAK,CAACG,MAAM,CAACC,CAAC,IAAI,IAAI,CAACL,MAAM,CAACM,GAAG,CAACC,OAAO,CAACF,CAAC,CAACG,QAAQ,CAAC,KAC3D,CAAC,CAAC,IAAIH,CAAC,CAACI,eAAe,KAAKd,aAAa,CAACqB,MAAM,CAAC,CAACL,MAAM,GAAG,CAAC;EACpE;EACAM,MAAMA,CAAA;IACJ,IAAI,CAACd,QAAQ,EAAE;IACf,OAAO,IAAI,CAACF,KAAK,CAACG,MAAM,CAACC,CAAC,IAAI,IAAI,CAACL,MAAM,CAACM,GAAG,CAACC,OAAO,CAACF,CAAC,CAACG,QAAQ,CAAC,KAC3D,CAAC,CAAC,IAAIH,CAAC,CAACI,eAAe,KAAKd,aAAa,CAACuB,IAAI,CAAC,CAACP,MAAM,GAAG,CAAC;EAClE;EACAQ,aAAaA,CAAA;IACX,IAAI,CAAChB,QAAQ,EAAE;IACf,OAAO,IAAI,CAACF,KAAK,CAACG,MAAM,CAACC,CAAC,IAAI,IAAI,CAACL,MAAM,CAACM,GAAG,CAACC,OAAO,CAACF,CAAC,CAACG,QAAQ,CAAC,KAC3D,CAAC,CAAC,IAAIH,CAAC,CAACI,eAAe,KAAKd,aAAa,CAACyB,WAAW,CAAC,CAACT,MAAM,GAAG,CAAC;EACzE;EACAU,aAAaA,CAAA;IACX,IAAI,CAAClB,QAAQ,EAAE;IACf,OAAO,IAAI,CAACF,KAAK,CAACG,MAAM,CAACC,CAAC,IAAI,IAAI,CAACL,MAAM,CAACM,GAAG,CAACC,OAAO,CAACF,CAAC,CAACG,QAAQ,CAAC,KAC3D,CAAC,CAAC,IAAIH,CAAC,CAACI,eAAe,KAAKd,aAAa,CAAC2B,WAAW,CAAC,CAACX,MAAM,GAAG,CAAC;EACzE;EACAY,QAAQA,CAAA;IACN,IAAI,CAACpB,QAAQ,EAAE;IACf,OAAO,IAAI,CAACF,KAAK,CAACG,MAAM,CAACC,CAAC,IAAI,IAAI,CAACL,MAAM,CAACM,GAAG,CAACC,OAAO,CAACF,CAAC,CAACG,QAAQ,CAAC,KAC3D,CAAC,CAAC,IAAIH,CAAC,CAACI,eAAe,KAAKd,aAAa,CAAC6B,MAAM,CAAC,CAACb,MAAM,GAAG,CAAC;EACpE;EACAc,WAAWA,CAAA;IACT,IAAI,CAACtB,QAAQ,EAAE;IACf,OAAO,IAAI,CAACF,KAAK,CAACG,MAAM,CAACC,CAAC,IAAI,IAAI,CAACL,MAAM,CAACM,GAAG,CAACC,OAAO,CAACF,CAAC,CAACG,QAAQ,CAAC,KAC3D,CAAC,CAAC,IAAIH,CAAC,CAACI,eAAe,KAAKd,aAAa,CAAC+B,SAAS,CAAC,CAACf,MAAM,GAAG,CAAC;EACvE;EAEAR,QAAQA,CAAA;IACN,MAAMwB,SAAS,GAAG/B,mBAAmB,CAACgC,eAAe,CAAC/B,WAAW,CAACgC,KAAK,CAAC;IACxE,IAAIF,SAAS,KAAK,IAAI,IAAIA,SAAS,KAAK,EAAE,EAAE;MAC1C,IAAI,CAAC1B,KAAK,GAAG6B,IAAI,CAACC,KAAK,CAACJ,SAAS,CAAC;IACpC;EACF;;;uCArDW7B,WAAW,EAAAkC,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,MAAA;IAAA;EAAA;;;aAAXrC,WAAW;MAAAsC,OAAA,EAAXtC,WAAW,CAAAuC,IAAA;MAAAC,UAAA,EADE;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}