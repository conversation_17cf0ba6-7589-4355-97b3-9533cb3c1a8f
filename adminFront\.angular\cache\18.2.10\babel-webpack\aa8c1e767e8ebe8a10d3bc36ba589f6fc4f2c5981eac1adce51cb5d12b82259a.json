{"ast": null, "code": "export class OrdersChartData {}", "map": {"version": 3, "names": ["OrdersChartData"], "sources": ["C:\\Users\\<USER>\\Documents\\jeansalechange\\adminFront\\src\\app\\@core\\data\\orders-chart.ts"], "sourcesContent": ["export interface OrdersChart {\r\n  chartLabel: string[];\r\n  linesData: number[][];\r\n}\r\n\r\nexport abstract class OrdersChartData {\r\n  abstract getOrdersChartData(period: string): OrdersChart;\r\n}\r\n"], "mappings": "AAKA,OAAM,MAAgBA,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}