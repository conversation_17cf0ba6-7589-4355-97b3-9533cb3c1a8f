{"ast": null, "code": "import getQuarter from \"../getQuarter/index.js\";\nimport toDate from \"../toDate/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name differenceInCalendarQuarters\n * @category Quarter Helpers\n * @summary Get the number of calendar quarters between the given dates.\n *\n * @description\n * Get the number of calendar quarters between the given dates.\n *\n * @param {Date|Number} dateLeft - the later date\n * @param {Date|Number} dateRight - the earlier date\n * @returns {Number} the number of calendar quarters\n * @throws {TypeError} 2 arguments required\n *\n * @example\n * // How many calendar quarters are between 31 December 2013 and 2 July 2014?\n * const result = differenceInCalendarQuarters(\n *   new Date(2014, 6, 2),\n *   new Date(2013, 11, 31)\n * )\n * //=> 3\n */\nexport default function differenceInCalendarQuarters(dirtyDateLeft, dirtyDateRight) {\n  requiredArgs(2, arguments);\n  var dateLeft = toDate(dirtyDateLeft);\n  var dateRight = toDate(dirtyDateRight);\n  var yearDiff = dateLeft.getFullYear() - dateRight.getFullYear();\n  var quarterDiff = getQuarter(dateLeft) - getQuarter(dateRight);\n  return yearDiff * 4 + quarterDiff;\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}