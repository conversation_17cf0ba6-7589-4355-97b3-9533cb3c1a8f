{"ast": null, "code": "import { map } from 'rxjs/operators';\nimport { BaseService } from '../base-service';\nimport { apiHouseHoldMainAddHouseHoldMainPost$Json } from '../fn/house-hold-main/api-house-hold-main-add-house-hold-main-post-json';\nimport { apiHouseHoldMainAddHouseHoldMainPost$Plain } from '../fn/house-hold-main/api-house-hold-main-add-house-hold-main-post-plain';\nimport { apiHouseHoldMainGetFloorAvailablePost$Json } from '../fn/house-hold-main/api-house-hold-main-get-floor-available-post-json';\nimport { apiHouseHoldMainGetFloorAvailablePost$Plain } from '../fn/house-hold-main/api-house-hold-main-get-floor-available-post-plain';\nimport { apiHouseHoldMainGetHighestFloorPost$Json } from '../fn/house-hold-main/api-house-hold-main-get-highest-floor-post-json';\nimport { apiHouseHoldMainGetHighestFloorPost$Plain } from '../fn/house-hold-main/api-house-hold-main-get-highest-floor-post-plain';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../api-configuration\";\nimport * as i2 from \"@angular/common/http\";\nexport let HouseHoldMainService = /*#__PURE__*/(() => {\n  class HouseHoldMainService extends BaseService {\n    constructor(config, http) {\n      super(config, http);\n    }\n    /** Path part for operation `apiHouseHoldMainGetHighestFloorPost()` */\n    static {\n      this.ApiHouseHoldMainGetHighestFloorPostPath = '/api/HouseHoldMain/GetHighestFloor';\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiHouseHoldMainGetHighestFloorPost$Plain()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiHouseHoldMainGetHighestFloorPost$Plain$Response(params, context) {\n      return apiHouseHoldMainGetHighestFloorPost$Plain(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiHouseHoldMainGetHighestFloorPost$Plain$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiHouseHoldMainGetHighestFloorPost$Plain(params, context) {\n      return this.apiHouseHoldMainGetHighestFloorPost$Plain$Response(params, context).pipe(map(r => r.body));\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiHouseHoldMainGetHighestFloorPost$Json()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiHouseHoldMainGetHighestFloorPost$Json$Response(params, context) {\n      return apiHouseHoldMainGetHighestFloorPost$Json(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiHouseHoldMainGetHighestFloorPost$Json$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiHouseHoldMainGetHighestFloorPost$Json(params, context) {\n      return this.apiHouseHoldMainGetHighestFloorPost$Json$Response(params, context).pipe(map(r => r.body));\n    }\n    /** Path part for operation `apiHouseHoldMainGetFloorAvailablePost()` */\n    static {\n      this.ApiHouseHoldMainGetFloorAvailablePostPath = '/api/HouseHoldMain/GetFloorAvailable';\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiHouseHoldMainGetFloorAvailablePost$Plain()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiHouseHoldMainGetFloorAvailablePost$Plain$Response(params, context) {\n      return apiHouseHoldMainGetFloorAvailablePost$Plain(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiHouseHoldMainGetFloorAvailablePost$Plain$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiHouseHoldMainGetFloorAvailablePost$Plain(params, context) {\n      return this.apiHouseHoldMainGetFloorAvailablePost$Plain$Response(params, context).pipe(map(r => r.body));\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiHouseHoldMainGetFloorAvailablePost$Json()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiHouseHoldMainGetFloorAvailablePost$Json$Response(params, context) {\n      return apiHouseHoldMainGetFloorAvailablePost$Json(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiHouseHoldMainGetFloorAvailablePost$Json$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiHouseHoldMainGetFloorAvailablePost$Json(params, context) {\n      return this.apiHouseHoldMainGetFloorAvailablePost$Json$Response(params, context).pipe(map(r => r.body));\n    }\n    /** Path part for operation `apiHouseHoldMainAddHouseHoldMainPost()` */\n    static {\n      this.ApiHouseHoldMainAddHouseHoldMainPostPath = '/api/HouseHoldMain/AddHouseHoldMain';\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiHouseHoldMainAddHouseHoldMainPost$Plain()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiHouseHoldMainAddHouseHoldMainPost$Plain$Response(params, context) {\n      return apiHouseHoldMainAddHouseHoldMainPost$Plain(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiHouseHoldMainAddHouseHoldMainPost$Plain$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiHouseHoldMainAddHouseHoldMainPost$Plain(params, context) {\n      return this.apiHouseHoldMainAddHouseHoldMainPost$Plain$Response(params, context).pipe(map(r => r.body));\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiHouseHoldMainAddHouseHoldMainPost$Json()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiHouseHoldMainAddHouseHoldMainPost$Json$Response(params, context) {\n      return apiHouseHoldMainAddHouseHoldMainPost$Json(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiHouseHoldMainAddHouseHoldMainPost$Json$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiHouseHoldMainAddHouseHoldMainPost$Json(params, context) {\n      return this.apiHouseHoldMainAddHouseHoldMainPost$Json$Response(params, context).pipe(map(r => r.body));\n    }\n    static {\n      this.ɵfac = function HouseHoldMainService_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || HouseHoldMainService)(i0.ɵɵinject(i1.ApiConfiguration), i0.ɵɵinject(i2.HttpClient));\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: HouseHoldMainService,\n        factory: HouseHoldMainService.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return HouseHoldMainService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}