{"ast": null, "code": ";\n(function (root, factory, undef) {\n  if (typeof exports === \"object\") {\n    // CommonJS\n    module.exports = exports = factory(require(\"./core\"), require(\"./enc-base64\"), require(\"./md5\"), require(\"./evpkdf\"), require(\"./cipher-core\"));\n  } else if (typeof define === \"function\" && define.amd) {\n    // AMD\n    define([\"./core\", \"./enc-base64\", \"./md5\", \"./evpkdf\", \"./cipher-core\"], factory);\n  } else {\n    // Global (browser)\n    factory(root.CryptoJS);\n  }\n})(this, function (CryptoJS) {\n  (function () {\n    // Shortcuts\n    var C = CryptoJS;\n    var C_lib = C.lib;\n    var StreamCipher = C_lib.StreamCipher;\n    var C_algo = C.algo;\n\n    /**\n     * RC4 stream cipher algorithm.\n     */\n    var RC4 = C_algo.RC4 = StreamCipher.extend({\n      _doReset: function () {\n        // Shortcuts\n        var key = this._key;\n        var keyWords = key.words;\n        var keySigBytes = key.sigBytes;\n\n        // Init sbox\n        var S = this._S = [];\n        for (var i = 0; i < 256; i++) {\n          S[i] = i;\n        }\n\n        // Key setup\n        for (var i = 0, j = 0; i < 256; i++) {\n          var keyByteIndex = i % keySigBytes;\n          var keyByte = keyWords[keyByteIndex >>> 2] >>> 24 - keyByteIndex % 4 * 8 & 0xff;\n          j = (j + S[i] + keyByte) % 256;\n\n          // Swap\n          var t = S[i];\n          S[i] = S[j];\n          S[j] = t;\n        }\n\n        // Counters\n        this._i = this._j = 0;\n      },\n      _doProcessBlock: function (M, offset) {\n        M[offset] ^= generateKeystreamWord.call(this);\n      },\n      keySize: 256 / 32,\n      ivSize: 0\n    });\n    function generateKeystreamWord() {\n      // Shortcuts\n      var S = this._S;\n      var i = this._i;\n      var j = this._j;\n\n      // Generate keystream word\n      var keystreamWord = 0;\n      for (var n = 0; n < 4; n++) {\n        i = (i + 1) % 256;\n        j = (j + S[i]) % 256;\n\n        // Swap\n        var t = S[i];\n        S[i] = S[j];\n        S[j] = t;\n        keystreamWord |= S[(S[i] + S[j]) % 256] << 24 - n * 8;\n      }\n\n      // Update counters\n      this._i = i;\n      this._j = j;\n      return keystreamWord;\n    }\n\n    /**\n     * Shortcut functions to the cipher's object interface.\n     *\n     * @example\n     *\n     *     var ciphertext = CryptoJS.RC4.encrypt(message, key, cfg);\n     *     var plaintext  = CryptoJS.RC4.decrypt(ciphertext, key, cfg);\n     */\n    C.RC4 = StreamCipher._createHelper(RC4);\n\n    /**\n     * Modified RC4 stream cipher algorithm.\n     */\n    var RC4Drop = C_algo.RC4Drop = RC4.extend({\n      /**\n       * Configuration options.\n       *\n       * @property {number} drop The number of keystream words to drop. Default 192\n       */\n      cfg: RC4.cfg.extend({\n        drop: 192\n      }),\n      _doReset: function () {\n        RC4._doReset.call(this);\n\n        // Drop\n        for (var i = this.cfg.drop; i > 0; i--) {\n          generateKeystreamWord.call(this);\n        }\n      }\n    });\n\n    /**\n     * Shortcut functions to the cipher's object interface.\n     *\n     * @example\n     *\n     *     var ciphertext = CryptoJS.RC4Drop.encrypt(message, key, cfg);\n     *     var plaintext  = CryptoJS.RC4Drop.decrypt(ciphertext, key, cfg);\n     */\n    C.RC4Drop = StreamCipher._createHelper(RC4Drop);\n  })();\n  return CryptoJS.RC4;\n});", "map": {"version": 3, "names": ["root", "factory", "undef", "exports", "module", "require", "define", "amd", "CryptoJS", "C", "C_lib", "lib", "StreamCipher", "C_algo", "algo", "RC4", "extend", "_doReset", "key", "_key", "key<PERSON>ords", "words", "keySigBytes", "sigBytes", "S", "_S", "i", "j", "keyByteIndex", "keyByte", "t", "_i", "_j", "_doProcessBlock", "M", "offset", "generateKeystreamWord", "call", "keySize", "ivSize", "keystreamWord", "n", "_createHelper", "RC4Drop", "cfg", "drop"], "sources": ["C:/Users/<USER>/Documents/salechange-product/adminFront/node_modules/crypto-js/rc4.js"], "sourcesContent": [";(function (root, factory, undef) {\n\tif (typeof exports === \"object\") {\n\t\t// CommonJS\n\t\tmodule.exports = exports = factory(require(\"./core\"), require(\"./enc-base64\"), require(\"./md5\"), require(\"./evpkdf\"), require(\"./cipher-core\"));\n\t}\n\telse if (typeof define === \"function\" && define.amd) {\n\t\t// AMD\n\t\tdefine([\"./core\", \"./enc-base64\", \"./md5\", \"./evpkdf\", \"./cipher-core\"], factory);\n\t}\n\telse {\n\t\t// Global (browser)\n\t\tfactory(root.CryptoJS);\n\t}\n}(this, function (CryptoJS) {\n\n\t(function () {\n\t    // Shortcuts\n\t    var C = CryptoJS;\n\t    var C_lib = C.lib;\n\t    var StreamCipher = C_lib.StreamCipher;\n\t    var C_algo = C.algo;\n\n\t    /**\n\t     * RC4 stream cipher algorithm.\n\t     */\n\t    var RC4 = C_algo.RC4 = StreamCipher.extend({\n\t        _doReset: function () {\n\t            // Shortcuts\n\t            var key = this._key;\n\t            var keyWords = key.words;\n\t            var keySigBytes = key.sigBytes;\n\n\t            // Init sbox\n\t            var S = this._S = [];\n\t            for (var i = 0; i < 256; i++) {\n\t                S[i] = i;\n\t            }\n\n\t            // Key setup\n\t            for (var i = 0, j = 0; i < 256; i++) {\n\t                var keyByteIndex = i % keySigBytes;\n\t                var keyByte = (keyWords[keyByteIndex >>> 2] >>> (24 - (keyByteIndex % 4) * 8)) & 0xff;\n\n\t                j = (j + S[i] + keyByte) % 256;\n\n\t                // Swap\n\t                var t = S[i];\n\t                S[i] = S[j];\n\t                S[j] = t;\n\t            }\n\n\t            // Counters\n\t            this._i = this._j = 0;\n\t        },\n\n\t        _doProcessBlock: function (M, offset) {\n\t            M[offset] ^= generateKeystreamWord.call(this);\n\t        },\n\n\t        keySize: 256/32,\n\n\t        ivSize: 0\n\t    });\n\n\t    function generateKeystreamWord() {\n\t        // Shortcuts\n\t        var S = this._S;\n\t        var i = this._i;\n\t        var j = this._j;\n\n\t        // Generate keystream word\n\t        var keystreamWord = 0;\n\t        for (var n = 0; n < 4; n++) {\n\t            i = (i + 1) % 256;\n\t            j = (j + S[i]) % 256;\n\n\t            // Swap\n\t            var t = S[i];\n\t            S[i] = S[j];\n\t            S[j] = t;\n\n\t            keystreamWord |= S[(S[i] + S[j]) % 256] << (24 - n * 8);\n\t        }\n\n\t        // Update counters\n\t        this._i = i;\n\t        this._j = j;\n\n\t        return keystreamWord;\n\t    }\n\n\t    /**\n\t     * Shortcut functions to the cipher's object interface.\n\t     *\n\t     * @example\n\t     *\n\t     *     var ciphertext = CryptoJS.RC4.encrypt(message, key, cfg);\n\t     *     var plaintext  = CryptoJS.RC4.decrypt(ciphertext, key, cfg);\n\t     */\n\t    C.RC4 = StreamCipher._createHelper(RC4);\n\n\t    /**\n\t     * Modified RC4 stream cipher algorithm.\n\t     */\n\t    var RC4Drop = C_algo.RC4Drop = RC4.extend({\n\t        /**\n\t         * Configuration options.\n\t         *\n\t         * @property {number} drop The number of keystream words to drop. Default 192\n\t         */\n\t        cfg: RC4.cfg.extend({\n\t            drop: 192\n\t        }),\n\n\t        _doReset: function () {\n\t            RC4._doReset.call(this);\n\n\t            // Drop\n\t            for (var i = this.cfg.drop; i > 0; i--) {\n\t                generateKeystreamWord.call(this);\n\t            }\n\t        }\n\t    });\n\n\t    /**\n\t     * Shortcut functions to the cipher's object interface.\n\t     *\n\t     * @example\n\t     *\n\t     *     var ciphertext = CryptoJS.RC4Drop.encrypt(message, key, cfg);\n\t     *     var plaintext  = CryptoJS.RC4Drop.decrypt(ciphertext, key, cfg);\n\t     */\n\t    C.RC4Drop = StreamCipher._createHelper(RC4Drop);\n\t}());\n\n\n\treturn CryptoJS.RC4;\n\n}));"], "mappings": "AAAA;AAAE,WAAUA,IAAI,EAAEC,OAAO,EAAEC,KAAK,EAAE;EACjC,IAAI,OAAOC,OAAO,KAAK,QAAQ,EAAE;IAChC;IACAC,MAAM,CAACD,OAAO,GAAGA,OAAO,GAAGF,OAAO,CAACI,OAAO,CAAC,QAAQ,CAAC,EAAEA,OAAO,CAAC,cAAc,CAAC,EAAEA,OAAO,CAAC,OAAO,CAAC,EAAEA,OAAO,CAAC,UAAU,CAAC,EAAEA,OAAO,CAAC,eAAe,CAAC,CAAC;EAChJ,CAAC,MACI,IAAI,OAAOC,MAAM,KAAK,UAAU,IAAIA,MAAM,CAACC,GAAG,EAAE;IACpD;IACAD,MAAM,CAAC,CAAC,QAAQ,EAAE,cAAc,EAAE,OAAO,EAAE,UAAU,EAAE,eAAe,CAAC,EAAEL,OAAO,CAAC;EAClF,CAAC,MACI;IACJ;IACAA,OAAO,CAACD,IAAI,CAACQ,QAAQ,CAAC;EACvB;AACD,CAAC,EAAC,IAAI,EAAE,UAAUA,QAAQ,EAAE;EAE1B,aAAY;IACT;IACA,IAAIC,CAAC,GAAGD,QAAQ;IAChB,IAAIE,KAAK,GAAGD,CAAC,CAACE,GAAG;IACjB,IAAIC,YAAY,GAAGF,KAAK,CAACE,YAAY;IACrC,IAAIC,MAAM,GAAGJ,CAAC,CAACK,IAAI;;IAEnB;AACL;AACA;IACK,IAAIC,GAAG,GAAGF,MAAM,CAACE,GAAG,GAAGH,YAAY,CAACI,MAAM,CAAC;MACvCC,QAAQ,EAAE,SAAAA,CAAA,EAAY;QAClB;QACA,IAAIC,GAAG,GAAG,IAAI,CAACC,IAAI;QACnB,IAAIC,QAAQ,GAAGF,GAAG,CAACG,KAAK;QACxB,IAAIC,WAAW,GAAGJ,GAAG,CAACK,QAAQ;;QAE9B;QACA,IAAIC,CAAC,GAAG,IAAI,CAACC,EAAE,GAAG,EAAE;QACpB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,GAAG,EAAEA,CAAC,EAAE,EAAE;UAC1BF,CAAC,CAACE,CAAC,CAAC,GAAGA,CAAC;QACZ;;QAEA;QACA,KAAK,IAAIA,CAAC,GAAG,CAAC,EAAEC,CAAC,GAAG,CAAC,EAAED,CAAC,GAAG,GAAG,EAAEA,CAAC,EAAE,EAAE;UACjC,IAAIE,YAAY,GAAGF,CAAC,GAAGJ,WAAW;UAClC,IAAIO,OAAO,GAAIT,QAAQ,CAACQ,YAAY,KAAK,CAAC,CAAC,KAAM,EAAE,GAAIA,YAAY,GAAG,CAAC,GAAI,CAAE,GAAI,IAAI;UAErFD,CAAC,GAAG,CAACA,CAAC,GAAGH,CAAC,CAACE,CAAC,CAAC,GAAGG,OAAO,IAAI,GAAG;;UAE9B;UACA,IAAIC,CAAC,GAAGN,CAAC,CAACE,CAAC,CAAC;UACZF,CAAC,CAACE,CAAC,CAAC,GAAGF,CAAC,CAACG,CAAC,CAAC;UACXH,CAAC,CAACG,CAAC,CAAC,GAAGG,CAAC;QACZ;;QAEA;QACA,IAAI,CAACC,EAAE,GAAG,IAAI,CAACC,EAAE,GAAG,CAAC;MACzB,CAAC;MAEDC,eAAe,EAAE,SAAAA,CAAUC,CAAC,EAAEC,MAAM,EAAE;QAClCD,CAAC,CAACC,MAAM,CAAC,IAAIC,qBAAqB,CAACC,IAAI,CAAC,IAAI,CAAC;MACjD,CAAC;MAEDC,OAAO,EAAE,GAAG,GAAC,EAAE;MAEfC,MAAM,EAAE;IACZ,CAAC,CAAC;IAEF,SAASH,qBAAqBA,CAAA,EAAG;MAC7B;MACA,IAAIZ,CAAC,GAAG,IAAI,CAACC,EAAE;MACf,IAAIC,CAAC,GAAG,IAAI,CAACK,EAAE;MACf,IAAIJ,CAAC,GAAG,IAAI,CAACK,EAAE;;MAEf;MACA,IAAIQ,aAAa,GAAG,CAAC;MACrB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAE,EAAE;QACxBf,CAAC,GAAG,CAACA,CAAC,GAAG,CAAC,IAAI,GAAG;QACjBC,CAAC,GAAG,CAACA,CAAC,GAAGH,CAAC,CAACE,CAAC,CAAC,IAAI,GAAG;;QAEpB;QACA,IAAII,CAAC,GAAGN,CAAC,CAACE,CAAC,CAAC;QACZF,CAAC,CAACE,CAAC,CAAC,GAAGF,CAAC,CAACG,CAAC,CAAC;QACXH,CAAC,CAACG,CAAC,CAAC,GAAGG,CAAC;QAERU,aAAa,IAAIhB,CAAC,CAAC,CAACA,CAAC,CAACE,CAAC,CAAC,GAAGF,CAAC,CAACG,CAAC,CAAC,IAAI,GAAG,CAAC,IAAK,EAAE,GAAGc,CAAC,GAAG,CAAE;MAC3D;;MAEA;MACA,IAAI,CAACV,EAAE,GAAGL,CAAC;MACX,IAAI,CAACM,EAAE,GAAGL,CAAC;MAEX,OAAOa,aAAa;IACxB;;IAEA;AACL;AACA;AACA;AACA;AACA;AACA;AACA;IACK/B,CAAC,CAACM,GAAG,GAAGH,YAAY,CAAC8B,aAAa,CAAC3B,GAAG,CAAC;;IAEvC;AACL;AACA;IACK,IAAI4B,OAAO,GAAG9B,MAAM,CAAC8B,OAAO,GAAG5B,GAAG,CAACC,MAAM,CAAC;MACtC;AACT;AACA;AACA;AACA;MACS4B,GAAG,EAAE7B,GAAG,CAAC6B,GAAG,CAAC5B,MAAM,CAAC;QAChB6B,IAAI,EAAE;MACV,CAAC,CAAC;MAEF5B,QAAQ,EAAE,SAAAA,CAAA,EAAY;QAClBF,GAAG,CAACE,QAAQ,CAACoB,IAAI,CAAC,IAAI,CAAC;;QAEvB;QACA,KAAK,IAAIX,CAAC,GAAG,IAAI,CAACkB,GAAG,CAACC,IAAI,EAAEnB,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAE,EAAE;UACpCU,qBAAqB,CAACC,IAAI,CAAC,IAAI,CAAC;QACpC;MACJ;IACJ,CAAC,CAAC;;IAEF;AACL;AACA;AACA;AACA;AACA;AACA;AACA;IACK5B,CAAC,CAACkC,OAAO,GAAG/B,YAAY,CAAC8B,aAAa,CAACC,OAAO,CAAC;EACnD,CAAC,EAAC,CAAC;EAGH,OAAOnC,QAAQ,CAACO,GAAG;AAEpB,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}