{"ast": null, "code": "//! moment.js locale configuration\n//! locale : Turkish [tr]\n//! authors : <PERSON><PERSON><PERSON> : https://github.com/erhangundogan,\n//!           <PERSON><PERSON><PERSON>: https://github.com/BYK\n\n;\n(function (global, factory) {\n  typeof exports === 'object' && typeof module !== 'undefined' && typeof require === 'function' ? factory(require('../moment')) : typeof define === 'function' && define.amd ? define(['../moment'], factory) : factory(global.moment);\n})(this, function (moment) {\n  'use strict';\n\n  //! moment.js locale configuration\n  var suffixes = {\n    1: \"'inci\",\n    5: \"'inci\",\n    8: \"'inci\",\n    70: \"'inci\",\n    80: \"'inci\",\n    2: \"'nci\",\n    7: \"'nci\",\n    20: \"'nci\",\n    50: \"'nci\",\n    3: \"'üncü\",\n    4: \"'üncü\",\n    100: \"'üncü\",\n    6: \"'ncı\",\n    9: \"'uncu\",\n    10: \"'uncu\",\n    30: \"'uncu\",\n    60: \"'ıncı\",\n    90: \"'ıncı\"\n  };\n  var tr = moment.defineLocale('tr', {\n    months: 'Ocak_Şubat_Mart_Nisan_Mayıs_Haziran_Temmuz_Ağustos_Eylül_Ekim_Kasım_Aralık'.split('_'),\n    monthsShort: 'Oca_Şub_Mar_Nis_May_Haz_Tem_Ağu_Eyl_Eki_Kas_Ara'.split('_'),\n    weekdays: 'Pazar_Pazartesi_Salı_Çarşamba_Perşembe_Cuma_Cumartesi'.split('_'),\n    weekdaysShort: 'Paz_Pzt_Sal_Çar_Per_Cum_Cmt'.split('_'),\n    weekdaysMin: 'Pz_Pt_Sa_Ça_Pe_Cu_Ct'.split('_'),\n    meridiem: function (hours, minutes, isLower) {\n      if (hours < 12) {\n        return isLower ? 'öö' : 'ÖÖ';\n      } else {\n        return isLower ? 'ös' : 'ÖS';\n      }\n    },\n    meridiemParse: /öö|ÖÖ|ös|ÖS/,\n    isPM: function (input) {\n      return input === 'ös' || input === 'ÖS';\n    },\n    longDateFormat: {\n      LT: 'HH:mm',\n      LTS: 'HH:mm:ss',\n      L: 'DD.MM.YYYY',\n      LL: 'D MMMM YYYY',\n      LLL: 'D MMMM YYYY HH:mm',\n      LLLL: 'dddd, D MMMM YYYY HH:mm'\n    },\n    calendar: {\n      sameDay: '[bugün saat] LT',\n      nextDay: '[yarın saat] LT',\n      nextWeek: '[gelecek] dddd [saat] LT',\n      lastDay: '[dün] LT',\n      lastWeek: '[geçen] dddd [saat] LT',\n      sameElse: 'L'\n    },\n    relativeTime: {\n      future: '%s sonra',\n      past: '%s önce',\n      s: 'birkaç saniye',\n      ss: '%d saniye',\n      m: 'bir dakika',\n      mm: '%d dakika',\n      h: 'bir saat',\n      hh: '%d saat',\n      d: 'bir gün',\n      dd: '%d gün',\n      w: 'bir hafta',\n      ww: '%d hafta',\n      M: 'bir ay',\n      MM: '%d ay',\n      y: 'bir yıl',\n      yy: '%d yıl'\n    },\n    ordinal: function (number, period) {\n      switch (period) {\n        case 'd':\n        case 'D':\n        case 'Do':\n        case 'DD':\n          return number;\n        default:\n          if (number === 0) {\n            // special case for zero\n            return number + \"'ıncı\";\n          }\n          var a = number % 10,\n            b = number % 100 - a,\n            c = number >= 100 ? 100 : null;\n          return number + (suffixes[a] || suffixes[b] || suffixes[c]);\n      }\n    },\n    week: {\n      dow: 1,\n      // Monday is the first day of the week.\n      doy: 7 // The week that contains Jan 7th is the first week of the year.\n    }\n  });\n  return tr;\n});", "map": {"version": 3, "names": ["global", "factory", "exports", "module", "require", "define", "amd", "moment", "suffixes", "tr", "defineLocale", "months", "split", "monthsShort", "weekdays", "weekdaysShort", "weekdaysMin", "meridiem", "hours", "minutes", "isLower", "meridiemParse", "isPM", "input", "longDateFormat", "LT", "LTS", "L", "LL", "LLL", "LLLL", "calendar", "sameDay", "nextDay", "nextWeek", "lastDay", "lastWeek", "same<PERSON><PERSON><PERSON>", "relativeTime", "future", "past", "s", "ss", "m", "mm", "h", "hh", "d", "dd", "w", "ww", "M", "MM", "y", "yy", "ordinal", "number", "period", "a", "b", "c", "week", "dow", "doy"], "sources": ["C:/Users/<USER>/Documents/salechange-product/adminFront/node_modules/moment/locale/tr.js"], "sourcesContent": ["//! moment.js locale configuration\n//! locale : Turkish [tr]\n//! authors : <PERSON><PERSON><PERSON> : https://github.com/erhangundogan,\n//!           <PERSON><PERSON><PERSON>: https://github.com/BYK\n\n;(function (global, factory) {\n   typeof exports === 'object' && typeof module !== 'undefined'\n       && typeof require === 'function' ? factory(require('../moment')) :\n   typeof define === 'function' && define.amd ? define(['../moment'], factory) :\n   factory(global.moment)\n}(this, (function (moment) { 'use strict';\n\n    //! moment.js locale configuration\n\n    var suffixes = {\n        1: \"'inci\",\n        5: \"'inci\",\n        8: \"'inci\",\n        70: \"'inci\",\n        80: \"'inci\",\n        2: \"'nci\",\n        7: \"'nci\",\n        20: \"'nci\",\n        50: \"'nci\",\n        3: \"'üncü\",\n        4: \"'üncü\",\n        100: \"'üncü\",\n        6: \"'ncı\",\n        9: \"'uncu\",\n        10: \"'uncu\",\n        30: \"'uncu\",\n        60: \"'ıncı\",\n        90: \"'ıncı\",\n    };\n\n    var tr = moment.defineLocale('tr', {\n        months: 'Ocak_Şubat_Mart_Nisan_Mayıs_Haziran_Temmuz_Ağustos_Eylül_Ekim_Kasım_Aralık'.split(\n            '_'\n        ),\n        monthsShort: 'Oca_Şub_Mar_Nis_May_Haz_Tem_Ağu_Eyl_Eki_Kas_Ara'.split('_'),\n        weekdays: 'Pazar_Pazartesi_Salı_Çarşamba_Perşembe_Cuma_Cumartesi'.split(\n            '_'\n        ),\n        weekdaysShort: 'Paz_Pzt_Sal_Çar_Per_Cum_Cmt'.split('_'),\n        weekdaysMin: 'Pz_Pt_Sa_Ça_Pe_Cu_Ct'.split('_'),\n        meridiem: function (hours, minutes, isLower) {\n            if (hours < 12) {\n                return isLower ? 'öö' : 'ÖÖ';\n            } else {\n                return isLower ? 'ös' : 'ÖS';\n            }\n        },\n        meridiemParse: /öö|ÖÖ|ös|ÖS/,\n        isPM: function (input) {\n            return input === 'ös' || input === 'ÖS';\n        },\n        longDateFormat: {\n            LT: 'HH:mm',\n            LTS: 'HH:mm:ss',\n            L: 'DD.MM.YYYY',\n            LL: 'D MMMM YYYY',\n            LLL: 'D MMMM YYYY HH:mm',\n            LLLL: 'dddd, D MMMM YYYY HH:mm',\n        },\n        calendar: {\n            sameDay: '[bugün saat] LT',\n            nextDay: '[yarın saat] LT',\n            nextWeek: '[gelecek] dddd [saat] LT',\n            lastDay: '[dün] LT',\n            lastWeek: '[geçen] dddd [saat] LT',\n            sameElse: 'L',\n        },\n        relativeTime: {\n            future: '%s sonra',\n            past: '%s önce',\n            s: 'birkaç saniye',\n            ss: '%d saniye',\n            m: 'bir dakika',\n            mm: '%d dakika',\n            h: 'bir saat',\n            hh: '%d saat',\n            d: 'bir gün',\n            dd: '%d gün',\n            w: 'bir hafta',\n            ww: '%d hafta',\n            M: 'bir ay',\n            MM: '%d ay',\n            y: 'bir yıl',\n            yy: '%d yıl',\n        },\n        ordinal: function (number, period) {\n            switch (period) {\n                case 'd':\n                case 'D':\n                case 'Do':\n                case 'DD':\n                    return number;\n                default:\n                    if (number === 0) {\n                        // special case for zero\n                        return number + \"'ıncı\";\n                    }\n                    var a = number % 10,\n                        b = (number % 100) - a,\n                        c = number >= 100 ? 100 : null;\n                    return number + (suffixes[a] || suffixes[b] || suffixes[c]);\n            }\n        },\n        week: {\n            dow: 1, // Monday is the first day of the week.\n            doy: 7, // The week that contains Jan 7th is the first week of the year.\n        },\n    });\n\n    return tr;\n\n})));\n"], "mappings": "AAAA;AACA;AACA;AACA;;AAEA;AAAE,WAAUA,MAAM,EAAEC,OAAO,EAAE;EAC1B,OAAOC,OAAO,KAAK,QAAQ,IAAI,OAAOC,MAAM,KAAK,WAAW,IACrD,OAAOC,OAAO,KAAK,UAAU,GAAGH,OAAO,CAACG,OAAO,CAAC,WAAW,CAAC,CAAC,GACpE,OAAOC,MAAM,KAAK,UAAU,IAAIA,MAAM,CAACC,GAAG,GAAGD,MAAM,CAAC,CAAC,WAAW,CAAC,EAAEJ,OAAO,CAAC,GAC3EA,OAAO,CAACD,MAAM,CAACO,MAAM,CAAC;AACzB,CAAC,EAAC,IAAI,EAAG,UAAUA,MAAM,EAAE;EAAE,YAAY;;EAErC;EAEA,IAAIC,QAAQ,GAAG;IACX,CAAC,EAAE,OAAO;IACV,CAAC,EAAE,OAAO;IACV,CAAC,EAAE,OAAO;IACV,EAAE,EAAE,OAAO;IACX,EAAE,EAAE,OAAO;IACX,CAAC,EAAE,MAAM;IACT,CAAC,EAAE,MAAM;IACT,EAAE,EAAE,MAAM;IACV,EAAE,EAAE,MAAM;IACV,CAAC,EAAE,OAAO;IACV,CAAC,EAAE,OAAO;IACV,GAAG,EAAE,OAAO;IACZ,CAAC,EAAE,MAAM;IACT,CAAC,EAAE,OAAO;IACV,EAAE,EAAE,OAAO;IACX,EAAE,EAAE,OAAO;IACX,EAAE,EAAE,OAAO;IACX,EAAE,EAAE;EACR,CAAC;EAED,IAAIC,EAAE,GAAGF,MAAM,CAACG,YAAY,CAAC,IAAI,EAAE;IAC/BC,MAAM,EAAE,4EAA4E,CAACC,KAAK,CACtF,GACJ,CAAC;IACDC,WAAW,EAAE,iDAAiD,CAACD,KAAK,CAAC,GAAG,CAAC;IACzEE,QAAQ,EAAE,uDAAuD,CAACF,KAAK,CACnE,GACJ,CAAC;IACDG,aAAa,EAAE,6BAA6B,CAACH,KAAK,CAAC,GAAG,CAAC;IACvDI,WAAW,EAAE,sBAAsB,CAACJ,KAAK,CAAC,GAAG,CAAC;IAC9CK,QAAQ,EAAE,SAAAA,CAAUC,KAAK,EAAEC,OAAO,EAAEC,OAAO,EAAE;MACzC,IAAIF,KAAK,GAAG,EAAE,EAAE;QACZ,OAAOE,OAAO,GAAG,IAAI,GAAG,IAAI;MAChC,CAAC,MAAM;QACH,OAAOA,OAAO,GAAG,IAAI,GAAG,IAAI;MAChC;IACJ,CAAC;IACDC,aAAa,EAAE,aAAa;IAC5BC,IAAI,EAAE,SAAAA,CAAUC,KAAK,EAAE;MACnB,OAAOA,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK,IAAI;IAC3C,CAAC;IACDC,cAAc,EAAE;MACZC,EAAE,EAAE,OAAO;MACXC,GAAG,EAAE,UAAU;MACfC,CAAC,EAAE,YAAY;MACfC,EAAE,EAAE,aAAa;MACjBC,GAAG,EAAE,mBAAmB;MACxBC,IAAI,EAAE;IACV,CAAC;IACDC,QAAQ,EAAE;MACNC,OAAO,EAAE,iBAAiB;MAC1BC,OAAO,EAAE,iBAAiB;MAC1BC,QAAQ,EAAE,0BAA0B;MACpCC,OAAO,EAAE,UAAU;MACnBC,QAAQ,EAAE,wBAAwB;MAClCC,QAAQ,EAAE;IACd,CAAC;IACDC,YAAY,EAAE;MACVC,MAAM,EAAE,UAAU;MAClBC,IAAI,EAAE,SAAS;MACfC,CAAC,EAAE,eAAe;MAClBC,EAAE,EAAE,WAAW;MACfC,CAAC,EAAE,YAAY;MACfC,EAAE,EAAE,WAAW;MACfC,CAAC,EAAE,UAAU;MACbC,EAAE,EAAE,SAAS;MACbC,CAAC,EAAE,SAAS;MACZC,EAAE,EAAE,QAAQ;MACZC,CAAC,EAAE,WAAW;MACdC,EAAE,EAAE,UAAU;MACdC,CAAC,EAAE,QAAQ;MACXC,EAAE,EAAE,OAAO;MACXC,CAAC,EAAE,SAAS;MACZC,EAAE,EAAE;IACR,CAAC;IACDC,OAAO,EAAE,SAAAA,CAAUC,MAAM,EAAEC,MAAM,EAAE;MAC/B,QAAQA,MAAM;QACV,KAAK,GAAG;QACR,KAAK,GAAG;QACR,KAAK,IAAI;QACT,KAAK,IAAI;UACL,OAAOD,MAAM;QACjB;UACI,IAAIA,MAAM,KAAK,CAAC,EAAE;YACd;YACA,OAAOA,MAAM,GAAG,OAAO;UAC3B;UACA,IAAIE,CAAC,GAAGF,MAAM,GAAG,EAAE;YACfG,CAAC,GAAIH,MAAM,GAAG,GAAG,GAAIE,CAAC;YACtBE,CAAC,GAAGJ,MAAM,IAAI,GAAG,GAAG,GAAG,GAAG,IAAI;UAClC,OAAOA,MAAM,IAAIhD,QAAQ,CAACkD,CAAC,CAAC,IAAIlD,QAAQ,CAACmD,CAAC,CAAC,IAAInD,QAAQ,CAACoD,CAAC,CAAC,CAAC;MACnE;IACJ,CAAC;IACDC,IAAI,EAAE;MACFC,GAAG,EAAE,CAAC;MAAE;MACRC,GAAG,EAAE,CAAC,CAAE;IACZ;EACJ,CAAC,CAAC;EAEF,OAAOtD,EAAE;AAEb,CAAE,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}