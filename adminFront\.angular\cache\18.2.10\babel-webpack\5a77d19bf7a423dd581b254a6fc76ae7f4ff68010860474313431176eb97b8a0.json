{"ast": null, "code": "import { Subject } from 'rxjs';\nimport { delay, shareReplay, debounceTime } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nexport let LayoutService = /*#__PURE__*/(() => {\n  class LayoutService {\n    constructor() {\n      this.layoutSize$ = new Subject();\n      this.layoutSizeChange$ = this.layoutSize$.pipe(shareReplay({\n        refCount: true\n      }));\n    }\n    changeLayoutSize() {\n      this.layoutSize$.next(null);\n    }\n    onChangeLayoutSize() {\n      return this.layoutSizeChange$.pipe(delay(1));\n    }\n    onSafeChangeLayoutSize() {\n      return this.layoutSizeChange$.pipe(debounceTime(350));\n    }\n    static {\n      this.ɵfac = function LayoutService_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || LayoutService)();\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: LayoutService,\n        factory: LayoutService.ɵfac\n      });\n    }\n  }\n  return LayoutService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}