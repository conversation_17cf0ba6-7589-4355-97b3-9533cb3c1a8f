{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nfunction ImageModalComponent_div_0_img_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"img\", 14);\n    i0.ɵɵlistener(\"click\", function ImageModalComponent_div_0_img_6_Template_img_click_0_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onImageClick());\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    let tmp_2_0;\n    let tmp_3_0;\n    let tmp_4_0;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"src\", (tmp_2_0 = ctx_r1.getCurrentImage()) == null ? null : tmp_2_0.url, i0.ɵɵsanitizeUrl)(\"alt\", ((tmp_3_0 = ctx_r1.getCurrentImage()) == null ? null : tmp_3_0.alt) || ((tmp_3_0 = ctx_r1.getCurrentImage()) == null ? null : tmp_3_0.name) || \"\\u5716\\u7247\")(\"title\", ((tmp_4_0 = ctx_r1.getCurrentImage()) == null ? null : tmp_4_0.title) || ((tmp_4_0 = ctx_r1.getCurrentImage()) == null ? null : tmp_4_0.description));\n  }\n}\nfunction ImageModalComponent_div_0_button_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 15);\n    i0.ɵɵlistener(\"click\", function ImageModalComponent_div_0_button_7_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.goToPrevious());\n    });\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(1, \"svg\", 16);\n    i0.ɵɵelement(2, \"path\", 17);\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ImageModalComponent_div_0_button_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 18);\n    i0.ɵɵlistener(\"click\", function ImageModalComponent_div_0_button_8_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.goToNext());\n    });\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(1, \"svg\", 16);\n    i0.ɵɵelement(2, \"path\", 19);\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ImageModalComponent_div_0_div_9_span_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 26);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    let tmp_3_0;\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" (\", (tmp_3_0 = ctx_r1.getCurrentImage()) == null ? null : tmp_3_0.name, \") \");\n  }\n}\nfunction ImageModalComponent_div_0_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 20)(1, \"div\", 21);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(2, \"svg\", 22);\n    i0.ɵɵelement(3, \"path\", 23);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(4, \"span\", 24);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(6, ImageModalComponent_div_0_div_9_span_6_Template, 2, 1, \"span\", 25);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    let tmp_3_0;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate2(\"\", ctx_r1.currentIndex + 1, \" / \", ctx_r1.images.length, \"\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (tmp_3_0 = ctx_r1.getCurrentImage()) == null ? null : tmp_3_0.name);\n  }\n}\nfunction ImageModalComponent_div_0_div_10_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 33);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    let tmp_3_0;\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", (tmp_3_0 = ctx_r1.getCurrentImage()) == null ? null : tmp_3_0.title, \" \");\n  }\n}\nfunction ImageModalComponent_div_0_div_10_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 34);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    let tmp_3_0;\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", (tmp_3_0 = ctx_r1.getCurrentImage()) == null ? null : tmp_3_0.description, \" \");\n  }\n}\nfunction ImageModalComponent_div_0_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 27)(1, \"div\", 28);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(2, \"svg\", 29);\n    i0.ɵɵelement(3, \"path\", 30);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(4, \"div\");\n    i0.ɵɵtemplate(5, ImageModalComponent_div_0_div_10_div_5_Template, 2, 1, \"div\", 31)(6, ImageModalComponent_div_0_div_10_div_6_Template, 2, 1, \"div\", 32);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    let tmp_2_0;\n    let tmp_3_0;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", (tmp_2_0 = ctx_r1.getCurrentImage()) == null ? null : tmp_2_0.title);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (tmp_3_0 = ctx_r1.getCurrentImage()) == null ? null : tmp_3_0.description);\n  }\n}\nfunction ImageModalComponent_div_0_div_11_button_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 38);\n    i0.ɵɵlistener(\"click\", function ImageModalComponent_div_0_div_11_button_2_Template_button_click_0_listener() {\n      const i_r7 = i0.ɵɵrestoreView(_r6).index;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.goToIndex(i_r7));\n    });\n    i0.ɵɵelement(1, \"img\", 39);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const image_r8 = ctx.$implicit;\n    const i_r7 = ctx.index;\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵclassProp(\"border-white\", i_r7 === ctx_r1.currentIndex)(\"border-gray-400\", i_r7 !== ctx_r1.currentIndex)(\"ring-3\", i_r7 === ctx_r1.currentIndex)(\"ring-white\", i_r7 === ctx_r1.currentIndex)(\"ring-opacity-50\", i_r7 === ctx_r1.currentIndex);\n    i0.ɵɵproperty(\"title\", \"\\u8DF3\\u81F3\\u7B2C \" + (i_r7 + 1) + \" \\u5F35\\u5716\\u7247\" + (image_r8.name ? \": \" + image_r8.name : \"\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"src\", image_r8.url, i0.ɵɵsanitizeUrl)(\"alt\", image_r8.alt || image_r8.name || \"\\u7E2E\\u7565\\u5716\");\n  }\n}\nfunction ImageModalComponent_div_0_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 35)(1, \"div\", 36);\n    i0.ɵɵtemplate(2, ImageModalComponent_div_0_div_11_button_2_Template, 2, 13, \"button\", 37);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.images);\n  }\n}\nfunction ImageModalComponent_div_0_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 40)(1, \"div\", 41);\n    i0.ɵɵelement(2, \"div\", 42);\n    i0.ɵɵelementStart(3, \"span\", 43);\n    i0.ɵɵtext(4, \"\\u8F09\\u5165\\u4E2D...\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction ImageModalComponent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 1);\n    i0.ɵɵlistener(\"click\", function ImageModalComponent_div_0_Template_div_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onBackdropClick($event));\n    })(\"keydown\", function ImageModalComponent_div_0_Template_div_keydown_0_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.handleKeyboardEvent($event));\n    });\n    i0.ɵɵelementStart(1, \"button\", 2);\n    i0.ɵɵlistener(\"click\", function ImageModalComponent_div_0_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.closeModal());\n    });\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(2, \"svg\", 3);\n    i0.ɵɵelement(3, \"path\", 4);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(4, \"div\", 5);\n    i0.ɵɵlistener(\"click\", function ImageModalComponent_div_0_Template_div_click_4_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.stopPropagation($event));\n    });\n    i0.ɵɵelementStart(5, \"div\", 6);\n    i0.ɵɵtemplate(6, ImageModalComponent_div_0_img_6_Template, 1, 3, \"img\", 7)(7, ImageModalComponent_div_0_button_7_Template, 3, 0, \"button\", 8)(8, ImageModalComponent_div_0_button_8_Template, 3, 0, \"button\", 9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(9, ImageModalComponent_div_0_div_9_Template, 7, 3, \"div\", 10)(10, ImageModalComponent_div_0_div_10_Template, 7, 2, \"div\", 11)(11, ImageModalComponent_div_0_div_11_Template, 3, 1, \"div\", 12)(12, ImageModalComponent_div_0_div_12_Template, 5, 0, \"div\", 13);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    let tmp_6_0;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵstyleProp(\"z-index\", ctx_r1.zIndex);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.getCurrentImage());\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.showNavigation && ctx_r1.images.length > 1);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.showNavigation && ctx_r1.images.length > 1);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.showCounter && ctx_r1.images.length > 1);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.showImageInfo && ctx_r1.getCurrentImage() && (((tmp_6_0 = ctx_r1.getCurrentImage()) == null ? null : tmp_6_0.description) || ((tmp_6_0 = ctx_r1.getCurrentImage()) == null ? null : tmp_6_0.title)));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.showThumbnails && ctx_r1.images.length > 1);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.images.length === 0);\n  }\n}\nexport class ImageModalComponent {\n  constructor(cdr, elementRef) {\n    this.cdr = cdr;\n    this.elementRef = elementRef;\n    this.images = [];\n    this.currentIndex = 0;\n    this.isVisible = false;\n    this.showThumbnails = true;\n    this.showCounter = true;\n    this.showNavigation = true;\n    this.showImageInfo = true;\n    this.enableKeyboard = true;\n    this.enableClickOutsideToClose = true;\n    this.zIndex = 9999;\n    this.close = new EventEmitter();\n    this.indexChange = new EventEmitter();\n    this.imageClick = new EventEmitter();\n  }\n  ngOnInit() {\n    if (this.currentIndex >= this.images.length) {\n      this.currentIndex = 0;\n    }\n    // 監聽 isVisible 變化\n    this.handleModalVisibility();\n  }\n  ngOnDestroy() {\n    // 確保清理\n    if (this.isVisible) {\n      document.body.classList.remove('modal-open');\n      document.body.style.overflow = 'auto';\n    }\n  }\n  handleKeyboardEvent(event) {\n    if (!this.enableKeyboard || !this.isVisible) return;\n    switch (event.key) {\n      case 'ArrowLeft':\n        event.preventDefault();\n        this.goToPrevious();\n        break;\n      case 'ArrowRight':\n        event.preventDefault();\n        this.goToNext();\n        break;\n      case 'Escape':\n        event.preventDefault();\n        this.closeModal();\n        break;\n    }\n  }\n  getCurrentImage() {\n    if (this.images.length === 0 || this.currentIndex < 0 || this.currentIndex >= this.images.length) {\n      return null;\n    }\n    return this.images[this.currentIndex];\n  }\n  goToNext() {\n    if (this.images.length <= 1) return;\n    const newIndex = (this.currentIndex + 1) % this.images.length;\n    this.setCurrentIndex(newIndex);\n  }\n  goToPrevious() {\n    if (this.images.length <= 1) return;\n    const newIndex = this.currentIndex === 0 ? this.images.length - 1 : this.currentIndex - 1;\n    this.setCurrentIndex(newIndex);\n  }\n  goToIndex(index) {\n    if (index >= 0 && index < this.images.length && index !== this.currentIndex) {\n      this.setCurrentIndex(index);\n    }\n  }\n  onBackdropClick(event) {\n    if (this.enableClickOutsideToClose && event.target === event.currentTarget) {\n      this.closeModal();\n    }\n  }\n  onImageClick() {\n    const currentImage = this.getCurrentImage();\n    if (currentImage) {\n      this.imageClick.emit({\n        index: this.currentIndex,\n        image: currentImage\n      });\n    }\n  }\n  closeModal() {\n    this.isVisible = false;\n    this.close.emit();\n    this.handleModalVisibility();\n  }\n  setCurrentIndex(index) {\n    this.currentIndex = index;\n    this.indexChange.emit(index);\n    this.cdr.detectChanges();\n  }\n  handleModalVisibility() {\n    if (this.isVisible) {\n      document.body.classList.add('modal-open');\n      document.body.style.overflow = 'hidden';\n      // 聚焦到模態窗口以支持鍵盤導航\n      setTimeout(() => {\n        const modalElement = this.elementRef.nativeElement.querySelector('.image-modal-container');\n        if (modalElement) {\n          modalElement.focus();\n        }\n      }, 100);\n    } else {\n      document.body.classList.remove('modal-open');\n      document.body.style.overflow = 'auto';\n    }\n  }\n  // 阻止事件冒泡\n  stopPropagation(event) {\n    event.stopPropagation();\n  }\n  static {\n    this.ɵfac = function ImageModalComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || ImageModalComponent)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i0.ElementRef));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ImageModalComponent,\n      selectors: [[\"ngx-image-modal\"]],\n      hostBindings: function ImageModalComponent_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"keydown\", function ImageModalComponent_keydown_HostBindingHandler($event) {\n            return ctx.handleKeyboardEvent($event);\n          }, false, i0.ɵɵresolveDocument);\n        }\n      },\n      inputs: {\n        images: \"images\",\n        currentIndex: \"currentIndex\",\n        isVisible: \"isVisible\",\n        showThumbnails: \"showThumbnails\",\n        showCounter: \"showCounter\",\n        showNavigation: \"showNavigation\",\n        showImageInfo: \"showImageInfo\",\n        enableKeyboard: \"enableKeyboard\",\n        enableClickOutsideToClose: \"enableClickOutsideToClose\",\n        zIndex: \"zIndex\"\n      },\n      outputs: {\n        close: \"close\",\n        indexChange: \"indexChange\",\n        imageClick: \"imageClick\"\n      },\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 1,\n      vars: 1,\n      consts: [[\"class\", \"image-modal-container fixed inset-0 flex items-center justify-center bg-black bg-opacity-80 backdrop-blur-sm p-4 animate-fade-in-up\", \"tabindex\", \"0\", 3, \"z-index\", \"click\", \"keydown\", 4, \"ngIf\"], [\"tabindex\", \"0\", 1, \"image-modal-container\", \"fixed\", \"inset-0\", \"flex\", \"items-center\", \"justify-center\", \"bg-black\", \"bg-opacity-80\", \"backdrop-blur-sm\", \"p-4\", \"animate-fade-in-up\", 3, \"click\", \"keydown\"], [\"title\", \"\\u95DC\\u9589\\u5716\\u7247\\u6AA2\\u8996 (\\u6309 ESC \\u9375)\", 1, \"modal-close-btn\", \"fixed\", \"top-6\", \"right-6\", \"bg-red-500\", \"bg-opacity-95\", \"hover:bg-red-600\", \"hover:bg-opacity-100\", \"text-white\", \"rounded-full\", \"w-14\", \"h-14\", \"flex\", \"items-center\", \"justify-center\", \"shadow-2xl\", \"transition-all\", \"duration-200\", \"z-50\", 3, \"click\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-7\", \"h-7\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2.5\", \"d\", \"M6 18L18 6M6 6l12 12\"], [1, \"relative\", \"max-w-7xl\", \"max-h-full\", \"w-full\", \"h-full\", \"flex\", \"items-center\", \"justify-center\", \"animate-slide-in-left\", 3, \"click\"], [1, \"relative\", \"max-w-full\", \"max-h-full\", \"bg-white\", \"rounded-2xl\", \"p-2\", \"shadow-2xl\"], [\"class\", \"max-w-full max-h-[85vh] object-contain rounded-xl animate-fade-in-up cursor-pointer\", 3, \"src\", \"alt\", \"title\", \"click\", 4, \"ngIf\"], [\"class\", \"modal-nav-btn absolute left-4 top-1/2 transform -translate-y-1/2 bg-white bg-opacity-95 hover:bg-opacity-100 text-gray-800 rounded-full w-16 h-16 flex items-center justify-center shadow-lg z-40 transition-all duration-200\", \"title\", \"\\u4E0A\\u4E00\\u5F35\\u5716\\u7247 (\\u6309 \\u2190 \\u9375)\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"modal-nav-btn absolute right-4 top-1/2 transform -translate-y-1/2 bg-white bg-opacity-95 hover:bg-opacity-100 text-gray-800 rounded-full w-16 h-16 flex items-center justify-center shadow-lg z-40 transition-all duration-200\", \"title\", \"\\u4E0B\\u4E00\\u5F35\\u5716\\u7247 (\\u6309 \\u2192 \\u9375)\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"absolute bottom-24 left-1/2 transform -translate-x-1/2 bg-black bg-opacity-90 text-white px-6 py-3 rounded-full backdrop-blur-sm shadow-lg\", 4, \"ngIf\"], [\"class\", \"absolute bottom-6 right-6 bg-gradient-to-r from-blue-600 to-blue-700 text-white px-4 py-3 rounded-lg text-sm backdrop-blur-sm shadow-lg max-w-md\", 4, \"ngIf\"], [\"class\", \"absolute bottom-32 left-1/2 transform -translate-x-1/2 bg-black bg-opacity-80 backdrop-blur-md p-4 rounded-xl shadow-2xl max-w-full\", 4, \"ngIf\"], [\"class\", \"flex items-center justify-center bg-white rounded-xl p-8 shadow-2xl\", 4, \"ngIf\"], [1, \"max-w-full\", \"max-h-[85vh]\", \"object-contain\", \"rounded-xl\", \"animate-fade-in-up\", \"cursor-pointer\", 3, \"click\", \"src\", \"alt\", \"title\"], [\"title\", \"\\u4E0A\\u4E00\\u5F35\\u5716\\u7247 (\\u6309 \\u2190 \\u9375)\", 1, \"modal-nav-btn\", \"absolute\", \"left-4\", \"top-1/2\", \"transform\", \"-translate-y-1/2\", \"bg-white\", \"bg-opacity-95\", \"hover:bg-opacity-100\", \"text-gray-800\", \"rounded-full\", \"w-16\", \"h-16\", \"flex\", \"items-center\", \"justify-center\", \"shadow-lg\", \"z-40\", \"transition-all\", \"duration-200\", 3, \"click\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-8\", \"h-8\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2.5\", \"d\", \"M15 19l-7-7 7-7\"], [\"title\", \"\\u4E0B\\u4E00\\u5F35\\u5716\\u7247 (\\u6309 \\u2192 \\u9375)\", 1, \"modal-nav-btn\", \"absolute\", \"right-4\", \"top-1/2\", \"transform\", \"-translate-y-1/2\", \"bg-white\", \"bg-opacity-95\", \"hover:bg-opacity-100\", \"text-gray-800\", \"rounded-full\", \"w-16\", \"h-16\", \"flex\", \"items-center\", \"justify-center\", \"shadow-lg\", \"z-40\", \"transition-all\", \"duration-200\", 3, \"click\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2.5\", \"d\", \"M9 5l7 7-7 7\"], [1, \"absolute\", \"bottom-24\", \"left-1/2\", \"transform\", \"-translate-x-1/2\", \"bg-black\", \"bg-opacity-90\", \"text-white\", \"px-6\", \"py-3\", \"rounded-full\", \"backdrop-blur-sm\", \"shadow-lg\"], [1, \"flex\", \"items-center\", \"space-x-3\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-5\", \"h-5\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z\"], [1, \"font-medium\", \"text-lg\"], [\"class\", \"text-sm opacity-75\", 4, \"ngIf\"], [1, \"text-sm\", \"opacity-75\"], [1, \"absolute\", \"bottom-6\", \"right-6\", \"bg-gradient-to-r\", \"from-blue-600\", \"to-blue-700\", \"text-white\", \"px-4\", \"py-3\", \"rounded-lg\", \"text-sm\", \"backdrop-blur-sm\", \"shadow-lg\", \"max-w-md\"], [1, \"flex\", \"items-start\", \"space-x-2\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-4\", \"h-4\", \"mt-0.5\", \"flex-shrink-0\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"], [\"class\", \"font-medium\", 4, \"ngIf\"], [\"class\", \"text-xs opacity-90 mt-1\", 4, \"ngIf\"], [1, \"font-medium\"], [1, \"text-xs\", \"opacity-90\", \"mt-1\"], [1, \"absolute\", \"bottom-32\", \"left-1/2\", \"transform\", \"-translate-x-1/2\", \"bg-black\", \"bg-opacity-80\", \"backdrop-blur-md\", \"p-4\", \"rounded-xl\", \"shadow-2xl\", \"max-w-full\"], [1, \"flex\", \"gap-3\", \"overflow-x-auto\", \"max-w-[80vw]\", \"modal-thumbnails\"], [\"class\", \"flex-shrink-0 w-20 h-20 border-3 rounded-xl overflow-hidden hover:border-white transition-all duration-200 hover:scale-105\", 3, \"border-white\", \"border-gray-400\", \"ring-3\", \"ring-white\", \"ring-opacity-50\", \"title\", \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"flex-shrink-0\", \"w-20\", \"h-20\", \"border-3\", \"rounded-xl\", \"overflow-hidden\", \"hover:border-white\", \"transition-all\", \"duration-200\", \"hover:scale-105\", 3, \"click\", \"title\"], [1, \"w-full\", \"h-full\", \"object-cover\", \"transition-transform\", \"duration-200\", 3, \"src\", \"alt\"], [1, \"flex\", \"items-center\", \"justify-center\", \"bg-white\", \"rounded-xl\", \"p-8\", \"shadow-2xl\"], [1, \"flex\", \"flex-col\", \"items-center\", \"space-y-4\"], [1, \"animate-spin\", \"rounded-full\", \"h-8\", \"w-8\", \"border-b-2\", \"border-blue-600\"], [1, \"text-gray-600\"]],\n      template: function ImageModalComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, ImageModalComponent_div_0_Template, 13, 9, \"div\", 0);\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngIf\", ctx.isVisible);\n        }\n      },\n      dependencies: [CommonModule, i1.NgForOf, i1.NgIf],\n      styles: [\"/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsImZpbGUiOiJpbWFnZS1tb2RhbC5jb21wb25lbnQuc2NzcyJ9 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvQHRoZW1lL2NvbXBvbmVudHMvaW1hZ2UtbW9kYWwvaW1hZ2UtbW9kYWwuY29tcG9uZW50LnNjc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IjtBQUNBLHdLQUF3SyIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["EventEmitter", "CommonModule", "i0", "ɵɵelementStart", "ɵɵlistener", "ImageModalComponent_div_0_img_6_Template_img_click_0_listener", "ɵɵrestoreView", "_r3", "ctx_r1", "ɵɵnextContext", "ɵɵresetView", "onImageClick", "ɵɵelementEnd", "ɵɵproperty", "tmp_2_0", "getCurrentImage", "url", "ɵɵsanitizeUrl", "tmp_3_0", "alt", "name", "tmp_4_0", "title", "description", "ImageModalComponent_div_0_button_7_Template_button_click_0_listener", "_r4", "goToPrevious", "ɵɵelement", "ImageModalComponent_div_0_button_8_Template_button_click_0_listener", "_r5", "goToNext", "ɵɵtext", "ɵɵadvance", "ɵɵtextInterpolate1", "ɵɵtemplate", "ImageModalComponent_div_0_div_9_span_6_Template", "ɵɵtextInterpolate2", "currentIndex", "images", "length", "ImageModalComponent_div_0_div_10_div_5_Template", "ImageModalComponent_div_0_div_10_div_6_Template", "ImageModalComponent_div_0_div_11_button_2_Template_button_click_0_listener", "i_r7", "_r6", "index", "goToIndex", "ɵɵclassProp", "image_r8", "ImageModalComponent_div_0_div_11_button_2_Template", "ImageModalComponent_div_0_Template_div_click_0_listener", "$event", "_r1", "onBackdropClick", "ImageModalComponent_div_0_Template_div_keydown_0_listener", "handleKeyboardEvent", "ImageModalComponent_div_0_Template_button_click_1_listener", "closeModal", "ImageModalComponent_div_0_Template_div_click_4_listener", "stopPropagation", "ImageModalComponent_div_0_img_6_Template", "ImageModalComponent_div_0_button_7_Template", "ImageModalComponent_div_0_button_8_Template", "ImageModalComponent_div_0_div_9_Template", "ImageModalComponent_div_0_div_10_Template", "ImageModalComponent_div_0_div_11_Template", "ImageModalComponent_div_0_div_12_Template", "ɵɵstyleProp", "zIndex", "showNavigation", "showCounter", "showImageInfo", "tmp_6_0", "showThumbnails", "ImageModalComponent", "constructor", "cdr", "elementRef", "isVisible", "enableKeyboard", "enableClickOutsideToClose", "close", "indexChange", "imageClick", "ngOnInit", "handleModalVisibility", "ngOnDestroy", "document", "body", "classList", "remove", "style", "overflow", "event", "key", "preventDefault", "newIndex", "setCurrentIndex", "target", "currentTarget", "currentImage", "emit", "image", "detectChanges", "add", "setTimeout", "modalElement", "nativeElement", "querySelector", "focus", "ɵɵdirectiveInject", "ChangeDetectorRef", "ElementRef", "selectors", "hostBindings", "ImageModalComponent_HostBindings", "rf", "ctx", "ImageModalComponent_keydown_HostBindingHandler", "ɵɵresolveDocument", "ImageModalComponent_div_0_Template", "i1", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "styles"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\@theme\\components\\image-modal\\image-modal.component.ts", "C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\@theme\\components\\image-modal\\image-modal.component.html"], "sourcesContent": ["import { Component, Input, Output, EventEmitter, OnInit, On<PERSON><PERSON>roy, HostListener, ChangeDetectorRef, ElementRef } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\n\r\nexport interface ImageModalItem {\r\n  url: string;\r\n  name?: string;\r\n  description?: string;\r\n  alt?: string;\r\n  title?: string;\r\n}\r\n\r\n@Component({\r\n  selector: 'ngx-image-modal',\r\n  standalone: true,\r\n  imports: [CommonModule],\r\n  templateUrl: './image-modal.component.html',\r\n  styleUrls: ['./image-modal.component.scss']\r\n})\r\nexport class ImageModalComponent implements OnInit, OnDestroy {\r\n  @Input() images: ImageModalItem[] = [];\r\n  @Input() currentIndex: number = 0;\r\n  @Input() isVisible: boolean = false;\r\n  @Input() showThumbnails: boolean = true;\r\n  @Input() showCounter: boolean = true;\r\n  @Input() showNavigation: boolean = true;\r\n  @Input() showImageInfo: boolean = true;\r\n  @Input() enableKeyboard: boolean = true;\r\n  @Input() enableClickOutsideToClose: boolean = true;\r\n  @Input() zIndex: number = 9999;\r\n\r\n  @Output() close = new EventEmitter<void>();\r\n  @Output() indexChange = new EventEmitter<number>();\r\n  @Output() imageClick = new EventEmitter<{ index: number; image: ImageModalItem }>();\r\n\r\n  constructor(\r\n    private cdr: ChangeDetectorRef,\r\n    private elementRef: ElementRef\r\n  ) {}\r\n\r\n  ngOnInit(): void {\r\n    if (this.currentIndex >= this.images.length) {\r\n      this.currentIndex = 0;\r\n    }\r\n    // 監聽 isVisible 變化\r\n    this.handleModalVisibility();\r\n  }\r\n\r\n  ngOnDestroy(): void {\r\n    // 確保清理\r\n    if (this.isVisible) {\r\n      document.body.classList.remove('modal-open');\r\n      document.body.style.overflow = 'auto';\r\n    }\r\n  }\r\n\r\n  @HostListener('document:keydown', ['$event'])\r\n  handleKeyboardEvent(event: KeyboardEvent): void {\r\n    if (!this.enableKeyboard || !this.isVisible) return;\r\n\r\n    switch (event.key) {\r\n      case 'ArrowLeft':\r\n        event.preventDefault();\r\n        this.goToPrevious();\r\n        break;\r\n      case 'ArrowRight':\r\n        event.preventDefault();\r\n        this.goToNext();\r\n        break;\r\n      case 'Escape':\r\n        event.preventDefault();\r\n        this.closeModal();\r\n        break;\r\n    }\r\n  }\r\n\r\n  getCurrentImage(): ImageModalItem | null {\r\n    if (this.images.length === 0 || this.currentIndex < 0 || this.currentIndex >= this.images.length) {\r\n      return null;\r\n    }\r\n    return this.images[this.currentIndex];\r\n  }\r\n\r\n  goToNext(): void {\r\n    if (this.images.length <= 1) return;\r\n    \r\n    const newIndex = (this.currentIndex + 1) % this.images.length;\r\n    this.setCurrentIndex(newIndex);\r\n  }\r\n\r\n  goToPrevious(): void {\r\n    if (this.images.length <= 1) return;\r\n    \r\n    const newIndex = this.currentIndex === 0 ? this.images.length - 1 : this.currentIndex - 1;\r\n    this.setCurrentIndex(newIndex);\r\n  }\r\n\r\n  goToIndex(index: number): void {\r\n    if (index >= 0 && index < this.images.length && index !== this.currentIndex) {\r\n      this.setCurrentIndex(index);\r\n    }\r\n  }\r\n\r\n  onBackdropClick(event: Event): void {\r\n    if (this.enableClickOutsideToClose && event.target === event.currentTarget) {\r\n      this.closeModal();\r\n    }\r\n  }\r\n\r\n  onImageClick(): void {\r\n    const currentImage = this.getCurrentImage();\r\n    if (currentImage) {\r\n      this.imageClick.emit({ \r\n        index: this.currentIndex, \r\n        image: currentImage \r\n      });\r\n    }\r\n  }\r\n\r\n  closeModal(): void {\r\n    this.isVisible = false;\r\n    this.close.emit();\r\n    this.handleModalVisibility();\r\n  }\r\n\r\n  private setCurrentIndex(index: number): void {\r\n    this.currentIndex = index;\r\n    this.indexChange.emit(index);\r\n    this.cdr.detectChanges();\r\n  }\r\n\r\n  private handleModalVisibility(): void {\r\n    if (this.isVisible) {\r\n      document.body.classList.add('modal-open');\r\n      document.body.style.overflow = 'hidden';\r\n      // 聚焦到模態窗口以支持鍵盤導航\r\n      setTimeout(() => {\r\n        const modalElement = this.elementRef.nativeElement.querySelector('.image-modal-container');\r\n        if (modalElement) {\r\n          modalElement.focus();\r\n        }\r\n      }, 100);\r\n    } else {\r\n      document.body.classList.remove('modal-open');\r\n      document.body.style.overflow = 'auto';\r\n    }\r\n  }\r\n\r\n  // 阻止事件冒泡\r\n  stopPropagation(event: Event): void {\r\n    event.stopPropagation();\r\n  }\r\n}\r\n", "<!-- 模態窗口容器 -->\r\n<div *ngIf=\"isVisible\" \r\n     class=\"image-modal-container fixed inset-0 flex items-center justify-center bg-black bg-opacity-80 backdrop-blur-sm p-4 animate-fade-in-up\"\r\n     [style.z-index]=\"zIndex\"\r\n     (click)=\"onBackdropClick($event)\"\r\n     (keydown)=\"handleKeyboardEvent($event)\"\r\n     tabindex=\"0\">\r\n\r\n  <!-- 關閉按鈕 -->\r\n  <button class=\"modal-close-btn fixed top-6 right-6 bg-red-500 bg-opacity-95 hover:bg-red-600 hover:bg-opacity-100 text-white rounded-full w-14 h-14 flex items-center justify-center shadow-2xl transition-all duration-200 z-50\"\r\n          (click)=\"closeModal()\" \r\n          title=\"關閉圖片檢視 (按 ESC 鍵)\">\r\n    <svg class=\"w-7 h-7\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n      <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2.5\" d=\"M6 18L18 6M6 6l12 12\"></path>\r\n    </svg>\r\n  </button>\r\n\r\n  <!-- 主要內容區域 -->\r\n  <div class=\"relative max-w-7xl max-h-full w-full h-full flex items-center justify-center animate-slide-in-left\"\r\n       (click)=\"stopPropagation($event)\">\r\n\r\n    <!-- 主圖片容器 -->\r\n    <div class=\"relative max-w-full max-h-full bg-white rounded-2xl p-2 shadow-2xl\">\r\n      <img *ngIf=\"getCurrentImage()\" \r\n           class=\"max-w-full max-h-[85vh] object-contain rounded-xl animate-fade-in-up cursor-pointer\"\r\n           [src]=\"getCurrentImage()?.url\"\r\n           [alt]=\"getCurrentImage()?.alt || getCurrentImage()?.name || '圖片'\"\r\n           [title]=\"getCurrentImage()?.title || getCurrentImage()?.description\"\r\n           (click)=\"onImageClick()\">\r\n\r\n      <!-- 導航按鈕 -->\r\n      <button *ngIf=\"showNavigation && images.length > 1\"\r\n              class=\"modal-nav-btn absolute left-4 top-1/2 transform -translate-y-1/2 bg-white bg-opacity-95 hover:bg-opacity-100 text-gray-800 rounded-full w-16 h-16 flex items-center justify-center shadow-lg z-40 transition-all duration-200\"\r\n              (click)=\"goToPrevious()\" \r\n              title=\"上一張圖片 (按 ← 鍵)\">\r\n        <svg class=\"w-8 h-8\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n          <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2.5\" d=\"M15 19l-7-7 7-7\"></path>\r\n        </svg>\r\n      </button>\r\n\r\n      <button *ngIf=\"showNavigation && images.length > 1\"\r\n              class=\"modal-nav-btn absolute right-4 top-1/2 transform -translate-y-1/2 bg-white bg-opacity-95 hover:bg-opacity-100 text-gray-800 rounded-full w-16 h-16 flex items-center justify-center shadow-lg z-40 transition-all duration-200\"\r\n              (click)=\"goToNext()\" \r\n              title=\"下一張圖片 (按 → 鍵)\">\r\n        <svg class=\"w-8 h-8\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n          <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2.5\" d=\"M9 5l7 7-7 7\"></path>\r\n        </svg>\r\n      </button>\r\n    </div>\r\n\r\n    <!-- 圖片計數器 -->\r\n    <div *ngIf=\"showCounter && images.length > 1\"\r\n         class=\"absolute bottom-24 left-1/2 transform -translate-x-1/2 bg-black bg-opacity-90 text-white px-6 py-3 rounded-full backdrop-blur-sm shadow-lg\">\r\n      <div class=\"flex items-center space-x-3\">\r\n        <svg class=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n          <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\"\r\n                d=\"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z\">\r\n          </path>\r\n        </svg>\r\n        <span class=\"font-medium text-lg\">{{currentIndex + 1}} / {{images.length}}</span>\r\n        <span *ngIf=\"getCurrentImage()?.name\" class=\"text-sm opacity-75\">\r\n          ({{getCurrentImage()?.name}})\r\n        </span>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 圖片資訊 -->\r\n    <div *ngIf=\"showImageInfo && getCurrentImage() && (getCurrentImage()?.description || getCurrentImage()?.title)\"\r\n         class=\"absolute bottom-6 right-6 bg-gradient-to-r from-blue-600 to-blue-700 text-white px-4 py-3 rounded-lg text-sm backdrop-blur-sm shadow-lg max-w-md\">\r\n      <div class=\"flex items-start space-x-2\">\r\n        <svg class=\"w-4 h-4 mt-0.5 flex-shrink-0\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n          <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\"\r\n                d=\"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"></path>\r\n        </svg>\r\n        <div>\r\n          <div *ngIf=\"getCurrentImage()?.title\" class=\"font-medium\">\r\n            {{getCurrentImage()?.title}}\r\n          </div>\r\n          <div *ngIf=\"getCurrentImage()?.description\" class=\"text-xs opacity-90 mt-1\">\r\n            {{getCurrentImage()?.description}}\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 縮略圖條 -->\r\n    <div *ngIf=\"showThumbnails && images.length > 1\"\r\n         class=\"absolute bottom-32 left-1/2 transform -translate-x-1/2 bg-black bg-opacity-80 backdrop-blur-md p-4 rounded-xl shadow-2xl max-w-full\">\r\n      <div class=\"flex gap-3 overflow-x-auto max-w-[80vw] modal-thumbnails\">\r\n        <button *ngFor=\"let image of images; let i = index\"\r\n                class=\"flex-shrink-0 w-20 h-20 border-3 rounded-xl overflow-hidden hover:border-white transition-all duration-200 hover:scale-105\"\r\n                [class.border-white]=\"i === currentIndex\"\r\n                [class.border-gray-400]=\"i !== currentIndex\"\r\n                [class.ring-3]=\"i === currentIndex\"\r\n                [class.ring-white]=\"i === currentIndex\"\r\n                [class.ring-opacity-50]=\"i === currentIndex\"\r\n                (click)=\"goToIndex(i)\" \r\n                [title]=\"'跳至第 ' + (i + 1) + ' 張圖片' + (image.name ? ': ' + image.name : '')\">\r\n          <img class=\"w-full h-full object-cover transition-transform duration-200\" \r\n               [src]=\"image.url\"\r\n               [alt]=\"image.alt || image.name || '縮略圖'\">\r\n        </button>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 載入指示器 -->\r\n    <div *ngIf=\"images.length === 0\" \r\n         class=\"flex items-center justify-center bg-white rounded-xl p-8 shadow-2xl\">\r\n      <div class=\"flex flex-col items-center space-y-4\">\r\n        <div class=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600\"></div>\r\n        <span class=\"text-gray-600\">載入中...</span>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</div>\r\n"], "mappings": "AAAA,SAAmCA,YAAY,QAAwE,eAAe;AACtI,SAASC,YAAY,QAAQ,iBAAiB;;;;;;ICsBxCC,EAAA,CAAAC,cAAA,cAK8B;IAAzBD,EAAA,CAAAE,UAAA,mBAAAC,8DAAA;MAAAH,EAAA,CAAAI,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAG,YAAA,EAAc;IAAA,EAAC;IAL7BT,EAAA,CAAAU,YAAA,EAK8B;;;;;;;IADzBV,EAFA,CAAAW,UAAA,SAAAC,OAAA,GAAAN,MAAA,CAAAO,eAAA,qBAAAD,OAAA,CAAAE,GAAA,EAAAd,EAAA,CAAAe,aAAA,CAA8B,UAAAC,OAAA,GAAAV,MAAA,CAAAO,eAAA,qBAAAG,OAAA,CAAAC,GAAA,OAAAD,OAAA,GAAAV,MAAA,CAAAO,eAAA,qBAAAG,OAAA,CAAAE,IAAA,oBACmC,YAAAC,OAAA,GAAAb,MAAA,CAAAO,eAAA,qBAAAM,OAAA,CAAAC,KAAA,OAAAD,OAAA,GAAAb,MAAA,CAAAO,eAAA,qBAAAM,OAAA,CAAAE,WAAA,EACG;;;;;;IAIzErB,EAAA,CAAAC,cAAA,iBAG8B;IADtBD,EAAA,CAAAE,UAAA,mBAAAoB,oEAAA;MAAAtB,EAAA,CAAAI,aAAA,CAAAmB,GAAA;MAAA,MAAAjB,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAkB,YAAA,EAAc;IAAA,EAAC;;IAE9BxB,EAAA,CAAAC,cAAA,cAA2E;IACzED,EAAA,CAAAyB,SAAA,eAAmG;IAEvGzB,EADE,CAAAU,YAAA,EAAM,EACC;;;;;;IAETV,EAAA,CAAAC,cAAA,iBAG8B;IADtBD,EAAA,CAAAE,UAAA,mBAAAwB,oEAAA;MAAA1B,EAAA,CAAAI,aAAA,CAAAuB,GAAA;MAAA,MAAArB,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAsB,QAAA,EAAU;IAAA,EAAC;;IAE1B5B,EAAA,CAAAC,cAAA,cAA2E;IACzED,EAAA,CAAAyB,SAAA,eAAgG;IAEpGzB,EADE,CAAAU,YAAA,EAAM,EACC;;;;;IAaPV,EAAA,CAAAC,cAAA,eAAiE;IAC/DD,EAAA,CAAA6B,MAAA,GACF;IAAA7B,EAAA,CAAAU,YAAA,EAAO;;;;;IADLV,EAAA,CAAA8B,SAAA,EACF;IADE9B,EAAA,CAAA+B,kBAAA,QAAAf,OAAA,GAAAV,MAAA,CAAAO,eAAA,qBAAAG,OAAA,CAAAE,IAAA,OACF;;;;;IATFlB,EAFF,CAAAC,cAAA,cACwJ,cAC7G;;IACvCD,EAAA,CAAAC,cAAA,cAA2E;IACzED,EAAA,CAAAyB,SAAA,eAEO;IACTzB,EAAA,CAAAU,YAAA,EAAM;;IACNV,EAAA,CAAAC,cAAA,eAAkC;IAAAD,EAAA,CAAA6B,MAAA,GAAwC;IAAA7B,EAAA,CAAAU,YAAA,EAAO;IACjFV,EAAA,CAAAgC,UAAA,IAAAC,+CAAA,mBAAiE;IAIrEjC,EADE,CAAAU,YAAA,EAAM,EACF;;;;;IALgCV,EAAA,CAAA8B,SAAA,GAAwC;IAAxC9B,EAAA,CAAAkC,kBAAA,KAAA5B,MAAA,CAAA6B,YAAA,aAAA7B,MAAA,CAAA8B,MAAA,CAAAC,MAAA,KAAwC;IACnErC,EAAA,CAAA8B,SAAA,EAA6B;IAA7B9B,EAAA,CAAAW,UAAA,UAAAK,OAAA,GAAAV,MAAA,CAAAO,eAAA,qBAAAG,OAAA,CAAAE,IAAA,CAA6B;;;;;IAelClB,EAAA,CAAAC,cAAA,cAA0D;IACxDD,EAAA,CAAA6B,MAAA,GACF;IAAA7B,EAAA,CAAAU,YAAA,EAAM;;;;;IADJV,EAAA,CAAA8B,SAAA,EACF;IADE9B,EAAA,CAAA+B,kBAAA,OAAAf,OAAA,GAAAV,MAAA,CAAAO,eAAA,qBAAAG,OAAA,CAAAI,KAAA,MACF;;;;;IACApB,EAAA,CAAAC,cAAA,cAA4E;IAC1ED,EAAA,CAAA6B,MAAA,GACF;IAAA7B,EAAA,CAAAU,YAAA,EAAM;;;;;IADJV,EAAA,CAAA8B,SAAA,EACF;IADE9B,EAAA,CAAA+B,kBAAA,OAAAf,OAAA,GAAAV,MAAA,CAAAO,eAAA,qBAAAG,OAAA,CAAAK,WAAA,MACF;;;;;IAXJrB,EAFF,CAAAC,cAAA,cAC8J,cACpH;;IACtCD,EAAA,CAAAC,cAAA,cAAgG;IAC9FD,EAAA,CAAAyB,SAAA,eAC2E;IAC7EzB,EAAA,CAAAU,YAAA,EAAM;;IACNV,EAAA,CAAAC,cAAA,UAAK;IAIHD,EAHA,CAAAgC,UAAA,IAAAM,+CAAA,kBAA0D,IAAAC,+CAAA,kBAGkB;IAKlFvC,EAFI,CAAAU,YAAA,EAAM,EACF,EACF;;;;;;IARMV,EAAA,CAAA8B,SAAA,GAA8B;IAA9B9B,EAAA,CAAAW,UAAA,UAAAC,OAAA,GAAAN,MAAA,CAAAO,eAAA,qBAAAD,OAAA,CAAAQ,KAAA,CAA8B;IAG9BpB,EAAA,CAAA8B,SAAA,EAAoC;IAApC9B,EAAA,CAAAW,UAAA,UAAAK,OAAA,GAAAV,MAAA,CAAAO,eAAA,qBAAAG,OAAA,CAAAK,WAAA,CAAoC;;;;;;IAW5CrB,EAAA,CAAAC,cAAA,iBAQoF;IAD5ED,EAAA,CAAAE,UAAA,mBAAAsC,2EAAA;MAAA,MAAAC,IAAA,GAAAzC,EAAA,CAAAI,aAAA,CAAAsC,GAAA,EAAAC,KAAA;MAAA,MAAArC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAsC,SAAA,CAAAH,IAAA,CAAY;IAAA,EAAC;IAE5BzC,EAAA,CAAAyB,SAAA,cAE8C;IAChDzB,EAAA,CAAAU,YAAA,EAAS;;;;;;IANDV,EAJA,CAAA6C,WAAA,iBAAAJ,IAAA,KAAAnC,MAAA,CAAA6B,YAAA,CAAyC,oBAAAM,IAAA,KAAAnC,MAAA,CAAA6B,YAAA,CACG,WAAAM,IAAA,KAAAnC,MAAA,CAAA6B,YAAA,CACT,eAAAM,IAAA,KAAAnC,MAAA,CAAA6B,YAAA,CACI,oBAAAM,IAAA,KAAAnC,MAAA,CAAA6B,YAAA,CACK;IAE5CnC,EAAA,CAAAW,UAAA,mCAAA8B,IAAA,iCAAAK,QAAA,CAAA5B,IAAA,UAAA4B,QAAA,CAAA5B,IAAA,OAA2E;IAE5ElB,EAAA,CAAA8B,SAAA,EAAiB;IACjB9B,EADA,CAAAW,UAAA,QAAAmC,QAAA,CAAAhC,GAAA,EAAAd,EAAA,CAAAe,aAAA,CAAiB,QAAA+B,QAAA,CAAA7B,GAAA,IAAA6B,QAAA,CAAA5B,IAAA,yBACuB;;;;;IAZjDlB,EAFF,CAAAC,cAAA,cACiJ,cACzE;IACpED,EAAA,CAAAgC,UAAA,IAAAe,kDAAA,sBAQoF;IAMxF/C,EADE,CAAAU,YAAA,EAAM,EACF;;;;IAdwBV,EAAA,CAAA8B,SAAA,GAAW;IAAX9B,EAAA,CAAAW,UAAA,YAAAL,MAAA,CAAA8B,MAAA,CAAW;;;;;IAmBvCpC,EAFF,CAAAC,cAAA,cACiF,cAC7B;IAChDD,EAAA,CAAAyB,SAAA,cAAgF;IAChFzB,EAAA,CAAAC,cAAA,eAA4B;IAAAD,EAAA,CAAA6B,MAAA,4BAAM;IAEtC7B,EAFsC,CAAAU,YAAA,EAAO,EACrC,EACF;;;;;;IA/GVV,EAAA,CAAAC,cAAA,aAKkB;IADbD,EADA,CAAAE,UAAA,mBAAA8C,wDAAAC,MAAA;MAAAjD,EAAA,CAAAI,aAAA,CAAA8C,GAAA;MAAA,MAAA5C,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAA6C,eAAA,CAAAF,MAAA,CAAuB;IAAA,EAAC,qBAAAG,0DAAAH,MAAA;MAAAjD,EAAA,CAAAI,aAAA,CAAA8C,GAAA;MAAA,MAAA5C,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CACtBF,MAAA,CAAA+C,mBAAA,CAAAJ,MAAA,CAA2B;IAAA,EAAC;IAI1CjD,EAAA,CAAAC,cAAA,gBAEiC;IADzBD,EAAA,CAAAE,UAAA,mBAAAoD,2DAAA;MAAAtD,EAAA,CAAAI,aAAA,CAAA8C,GAAA;MAAA,MAAA5C,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAiD,UAAA,EAAY;IAAA,EAAC;;IAE5BvD,EAAA,CAAAC,cAAA,aAA2E;IACzED,EAAA,CAAAyB,SAAA,cAAwG;IAE5GzB,EADE,CAAAU,YAAA,EAAM,EACC;;IAGTV,EAAA,CAAAC,cAAA,aACuC;IAAlCD,EAAA,CAAAE,UAAA,mBAAAsD,wDAAAP,MAAA;MAAAjD,EAAA,CAAAI,aAAA,CAAA8C,GAAA;MAAA,MAAA5C,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAmD,eAAA,CAAAR,MAAA,CAAuB;IAAA,EAAC;IAGpCjD,EAAA,CAAAC,cAAA,aAAgF;IAkB9ED,EAjBA,CAAAgC,UAAA,IAAA0B,wCAAA,iBAK8B,IAAAC,2CAAA,oBAMA,IAAAC,2CAAA,oBASA;IAKhC5D,EAAA,CAAAU,YAAA,EAAM;IA0DNV,EAvDA,CAAAgC,UAAA,IAAA6B,wCAAA,kBACwJ,KAAAC,yCAAA,kBAgBM,KAAAC,yCAAA,kBAmBb,KAAAC,yCAAA,kBAoBhE;IAOrFhE,EADE,CAAAU,YAAA,EAAM,EACF;;;;;IA/GDV,EAAA,CAAAiE,WAAA,YAAA3D,MAAA,CAAA4D,MAAA,CAAwB;IAoBjBlE,EAAA,CAAA8B,SAAA,GAAuB;IAAvB9B,EAAA,CAAAW,UAAA,SAAAL,MAAA,CAAAO,eAAA,GAAuB;IAQpBb,EAAA,CAAA8B,SAAA,EAAyC;IAAzC9B,EAAA,CAAAW,UAAA,SAAAL,MAAA,CAAA6D,cAAA,IAAA7D,MAAA,CAAA8B,MAAA,CAAAC,MAAA,KAAyC;IASzCrC,EAAA,CAAA8B,SAAA,EAAyC;IAAzC9B,EAAA,CAAAW,UAAA,SAAAL,MAAA,CAAA6D,cAAA,IAAA7D,MAAA,CAAA8B,MAAA,CAAAC,MAAA,KAAyC;IAW9CrC,EAAA,CAAA8B,SAAA,EAAsC;IAAtC9B,EAAA,CAAAW,UAAA,SAAAL,MAAA,CAAA8D,WAAA,IAAA9D,MAAA,CAAA8B,MAAA,CAAAC,MAAA,KAAsC;IAgBtCrC,EAAA,CAAA8B,SAAA,EAAwG;IAAxG9B,EAAA,CAAAW,UAAA,SAAAL,MAAA,CAAA+D,aAAA,IAAA/D,MAAA,CAAAO,eAAA,SAAAyD,OAAA,GAAAhE,MAAA,CAAAO,eAAA,qBAAAyD,OAAA,CAAAjD,WAAA,OAAAiD,OAAA,GAAAhE,MAAA,CAAAO,eAAA,qBAAAyD,OAAA,CAAAlD,KAAA,GAAwG;IAmBxGpB,EAAA,CAAA8B,SAAA,EAAyC;IAAzC9B,EAAA,CAAAW,UAAA,SAAAL,MAAA,CAAAiE,cAAA,IAAAjE,MAAA,CAAA8B,MAAA,CAAAC,MAAA,KAAyC;IAoBzCrC,EAAA,CAAA8B,SAAA,EAAyB;IAAzB9B,EAAA,CAAAW,UAAA,SAAAL,MAAA,CAAA8B,MAAA,CAAAC,MAAA,OAAyB;;;ADxFnC,OAAM,MAAOmC,mBAAmB;EAgB9BC,YACUC,GAAsB,EACtBC,UAAsB;IADtB,KAAAD,GAAG,GAAHA,GAAG;IACH,KAAAC,UAAU,GAAVA,UAAU;IAjBX,KAAAvC,MAAM,GAAqB,EAAE;IAC7B,KAAAD,YAAY,GAAW,CAAC;IACxB,KAAAyC,SAAS,GAAY,KAAK;IAC1B,KAAAL,cAAc,GAAY,IAAI;IAC9B,KAAAH,WAAW,GAAY,IAAI;IAC3B,KAAAD,cAAc,GAAY,IAAI;IAC9B,KAAAE,aAAa,GAAY,IAAI;IAC7B,KAAAQ,cAAc,GAAY,IAAI;IAC9B,KAAAC,yBAAyB,GAAY,IAAI;IACzC,KAAAZ,MAAM,GAAW,IAAI;IAEpB,KAAAa,KAAK,GAAG,IAAIjF,YAAY,EAAQ;IAChC,KAAAkF,WAAW,GAAG,IAAIlF,YAAY,EAAU;IACxC,KAAAmF,UAAU,GAAG,IAAInF,YAAY,EAA4C;EAKhF;EAEHoF,QAAQA,CAAA;IACN,IAAI,IAAI,CAAC/C,YAAY,IAAI,IAAI,CAACC,MAAM,CAACC,MAAM,EAAE;MAC3C,IAAI,CAACF,YAAY,GAAG,CAAC;IACvB;IACA;IACA,IAAI,CAACgD,qBAAqB,EAAE;EAC9B;EAEAC,WAAWA,CAAA;IACT;IACA,IAAI,IAAI,CAACR,SAAS,EAAE;MAClBS,QAAQ,CAACC,IAAI,CAACC,SAAS,CAACC,MAAM,CAAC,YAAY,CAAC;MAC5CH,QAAQ,CAACC,IAAI,CAACG,KAAK,CAACC,QAAQ,GAAG,MAAM;IACvC;EACF;EAGArC,mBAAmBA,CAACsC,KAAoB;IACtC,IAAI,CAAC,IAAI,CAACd,cAAc,IAAI,CAAC,IAAI,CAACD,SAAS,EAAE;IAE7C,QAAQe,KAAK,CAACC,GAAG;MACf,KAAK,WAAW;QACdD,KAAK,CAACE,cAAc,EAAE;QACtB,IAAI,CAACrE,YAAY,EAAE;QACnB;MACF,KAAK,YAAY;QACfmE,KAAK,CAACE,cAAc,EAAE;QACtB,IAAI,CAACjE,QAAQ,EAAE;QACf;MACF,KAAK,QAAQ;QACX+D,KAAK,CAACE,cAAc,EAAE;QACtB,IAAI,CAACtC,UAAU,EAAE;QACjB;IACJ;EACF;EAEA1C,eAAeA,CAAA;IACb,IAAI,IAAI,CAACuB,MAAM,CAACC,MAAM,KAAK,CAAC,IAAI,IAAI,CAACF,YAAY,GAAG,CAAC,IAAI,IAAI,CAACA,YAAY,IAAI,IAAI,CAACC,MAAM,CAACC,MAAM,EAAE;MAChG,OAAO,IAAI;IACb;IACA,OAAO,IAAI,CAACD,MAAM,CAAC,IAAI,CAACD,YAAY,CAAC;EACvC;EAEAP,QAAQA,CAAA;IACN,IAAI,IAAI,CAACQ,MAAM,CAACC,MAAM,IAAI,CAAC,EAAE;IAE7B,MAAMyD,QAAQ,GAAG,CAAC,IAAI,CAAC3D,YAAY,GAAG,CAAC,IAAI,IAAI,CAACC,MAAM,CAACC,MAAM;IAC7D,IAAI,CAAC0D,eAAe,CAACD,QAAQ,CAAC;EAChC;EAEAtE,YAAYA,CAAA;IACV,IAAI,IAAI,CAACY,MAAM,CAACC,MAAM,IAAI,CAAC,EAAE;IAE7B,MAAMyD,QAAQ,GAAG,IAAI,CAAC3D,YAAY,KAAK,CAAC,GAAG,IAAI,CAACC,MAAM,CAACC,MAAM,GAAG,CAAC,GAAG,IAAI,CAACF,YAAY,GAAG,CAAC;IACzF,IAAI,CAAC4D,eAAe,CAACD,QAAQ,CAAC;EAChC;EAEAlD,SAASA,CAACD,KAAa;IACrB,IAAIA,KAAK,IAAI,CAAC,IAAIA,KAAK,GAAG,IAAI,CAACP,MAAM,CAACC,MAAM,IAAIM,KAAK,KAAK,IAAI,CAACR,YAAY,EAAE;MAC3E,IAAI,CAAC4D,eAAe,CAACpD,KAAK,CAAC;IAC7B;EACF;EAEAQ,eAAeA,CAACwC,KAAY;IAC1B,IAAI,IAAI,CAACb,yBAAyB,IAAIa,KAAK,CAACK,MAAM,KAAKL,KAAK,CAACM,aAAa,EAAE;MAC1E,IAAI,CAAC1C,UAAU,EAAE;IACnB;EACF;EAEA9C,YAAYA,CAAA;IACV,MAAMyF,YAAY,GAAG,IAAI,CAACrF,eAAe,EAAE;IAC3C,IAAIqF,YAAY,EAAE;MAChB,IAAI,CAACjB,UAAU,CAACkB,IAAI,CAAC;QACnBxD,KAAK,EAAE,IAAI,CAACR,YAAY;QACxBiE,KAAK,EAAEF;OACR,CAAC;IACJ;EACF;EAEA3C,UAAUA,CAAA;IACR,IAAI,CAACqB,SAAS,GAAG,KAAK;IACtB,IAAI,CAACG,KAAK,CAACoB,IAAI,EAAE;IACjB,IAAI,CAAChB,qBAAqB,EAAE;EAC9B;EAEQY,eAAeA,CAACpD,KAAa;IACnC,IAAI,CAACR,YAAY,GAAGQ,KAAK;IACzB,IAAI,CAACqC,WAAW,CAACmB,IAAI,CAACxD,KAAK,CAAC;IAC5B,IAAI,CAAC+B,GAAG,CAAC2B,aAAa,EAAE;EAC1B;EAEQlB,qBAAqBA,CAAA;IAC3B,IAAI,IAAI,CAACP,SAAS,EAAE;MAClBS,QAAQ,CAACC,IAAI,CAACC,SAAS,CAACe,GAAG,CAAC,YAAY,CAAC;MACzCjB,QAAQ,CAACC,IAAI,CAACG,KAAK,CAACC,QAAQ,GAAG,QAAQ;MACvC;MACAa,UAAU,CAAC,MAAK;QACd,MAAMC,YAAY,GAAG,IAAI,CAAC7B,UAAU,CAAC8B,aAAa,CAACC,aAAa,CAAC,wBAAwB,CAAC;QAC1F,IAAIF,YAAY,EAAE;UAChBA,YAAY,CAACG,KAAK,EAAE;QACtB;MACF,CAAC,EAAE,GAAG,CAAC;IACT,CAAC,MAAM;MACLtB,QAAQ,CAACC,IAAI,CAACC,SAAS,CAACC,MAAM,CAAC,YAAY,CAAC;MAC5CH,QAAQ,CAACC,IAAI,CAACG,KAAK,CAACC,QAAQ,GAAG,MAAM;IACvC;EACF;EAEA;EACAjC,eAAeA,CAACkC,KAAY;IAC1BA,KAAK,CAAClC,eAAe,EAAE;EACzB;;;uCApIWe,mBAAmB,EAAAxE,EAAA,CAAA4G,iBAAA,CAAA5G,EAAA,CAAA6G,iBAAA,GAAA7G,EAAA,CAAA4G,iBAAA,CAAA5G,EAAA,CAAA8G,UAAA;IAAA;EAAA;;;YAAnBtC,mBAAmB;MAAAuC,SAAA;MAAAC,YAAA,WAAAC,iCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAnBlH,EAAA,CAAAE,UAAA,qBAAAkH,+CAAAnE,MAAA;YAAA,OAAAkE,GAAA,CAAA9D,mBAAA,CAAAJ,MAAA,CAA2B;UAAA,UAAAjD,EAAA,CAAAqH,iBAAA,CAAR;;;;;;;;;;;;;;;;;;;;;;;;;;;UCjBhCrH,EAAA,CAAAgC,UAAA,IAAAsF,kCAAA,kBAKkB;;;UALZtH,EAAA,CAAAW,UAAA,SAAAwG,GAAA,CAAAvC,SAAA,CAAe;;;qBDaT7E,YAAY,EAAAwH,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA;MAAAC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}