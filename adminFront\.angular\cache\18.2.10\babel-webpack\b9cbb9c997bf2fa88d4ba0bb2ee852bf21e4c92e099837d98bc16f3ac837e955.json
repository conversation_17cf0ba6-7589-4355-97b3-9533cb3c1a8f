{"ast": null, "code": "import toDate from \"../toDate/index.js\";\nimport isValid from \"../isValid/index.js\";\nimport addLeadingZeros from \"../_lib/addLeadingZeros/index.js\";\nimport toInteger from \"../_lib/toInteger/index.js\";\n/**\n * @name formatRFC3339\n * @category Common Helpers\n * @summary Format the date according to the RFC 3339 standard (https://tools.ietf.org/html/rfc3339#section-5.6).\n *\n * @description\n * Return the formatted date string in RFC 3339 format. Options may be passed to control the parts and notations of the date.\n *\n * @param {Date|Number} date - the original date\n * @param {Object} [options] - an object with options.\n * @param {0|1|2|3} [options.fractionDigits=0] - number of digits after the decimal point after seconds\n * @returns {String} the formatted date string\n * @throws {TypeError} 1 argument required\n * @throws {RangeError} `date` must not be Invalid Date\n * @throws {RangeError} `options.fractionDigits` must be between 0 and 3\n *\n * @example\n * // Represent 18 September 2019 in RFC 3339 format:\n * const result = formatRFC3339(new Date(2019, 8, 18, 19, 0, 52))\n * //=> '2019-09-18T19:00:52Z'\n *\n * @example\n * // Represent 18 September 2019 in RFC 3339 format, 2 digits of second fraction:\n * const result = formatRFC3339(new Date(2019, 8, 18, 19, 0, 52, 234), { fractionDigits: 2 })\n * //=> '2019-09-18T19:00:52.23Z'\n *\n * @example\n * // Represent 18 September 2019 in RFC 3339 format, 3 digits of second fraction\n * const result = formatRFC3339(new Date(2019, 8, 18, 19, 0, 52, 234), { fractionDigits: 3 })\n * //=> '2019-09-18T19:00:52.234Z'\n */\nexport default function formatRFC3339(dirtyDate, options) {\n  var _options$fractionDigi;\n  if (arguments.length < 1) {\n    throw new TypeError(\"1 arguments required, but only \".concat(arguments.length, \" present\"));\n  }\n  var originalDate = toDate(dirtyDate);\n  if (!isValid(originalDate)) {\n    throw new RangeError('Invalid time value');\n  }\n  var fractionDigits = Number((_options$fractionDigi = options === null || options === void 0 ? void 0 : options.fractionDigits) !== null && _options$fractionDigi !== void 0 ? _options$fractionDigi : 0);\n\n  // Test if fractionDigits is between 0 and 3 _and_ is not NaN\n  if (!(fractionDigits >= 0 && fractionDigits <= 3)) {\n    throw new RangeError('fractionDigits must be between 0 and 3 inclusively');\n  }\n  var day = addLeadingZeros(originalDate.getDate(), 2);\n  var month = addLeadingZeros(originalDate.getMonth() + 1, 2);\n  var year = originalDate.getFullYear();\n  var hour = addLeadingZeros(originalDate.getHours(), 2);\n  var minute = addLeadingZeros(originalDate.getMinutes(), 2);\n  var second = addLeadingZeros(originalDate.getSeconds(), 2);\n  var fractionalSecond = '';\n  if (fractionDigits > 0) {\n    var milliseconds = originalDate.getMilliseconds();\n    var fractionalSeconds = Math.floor(milliseconds * Math.pow(10, fractionDigits - 3));\n    fractionalSecond = '.' + addLeadingZeros(fractionalSeconds, fractionDigits);\n  }\n  var offset = '';\n  var tzOffset = originalDate.getTimezoneOffset();\n  if (tzOffset !== 0) {\n    var absoluteOffset = Math.abs(tzOffset);\n    var hourOffset = addLeadingZeros(toInteger(absoluteOffset / 60), 2);\n    var minuteOffset = addLeadingZeros(absoluteOffset % 60, 2);\n    // If less than 0, the sign is +, because it is ahead of time.\n    var sign = tzOffset < 0 ? '+' : '-';\n    offset = \"\".concat(sign).concat(hourOffset, \":\").concat(minuteOffset);\n  } else {\n    offset = 'Z';\n  }\n  return \"\".concat(year, \"-\").concat(month, \"-\").concat(day, \"T\").concat(hour, \":\").concat(minute, \":\").concat(second).concat(fractionalSecond).concat(offset);\n}", "map": {"version": 3, "names": ["toDate", "<PERSON><PERSON><PERSON><PERSON>", "addLeadingZeros", "toInteger", "formatRFC3339", "dirtyDate", "options", "_options$fractionDigi", "arguments", "length", "TypeError", "concat", "originalDate", "RangeError", "fractionDigits", "Number", "day", "getDate", "month", "getMonth", "year", "getFullYear", "hour", "getHours", "minute", "getMinutes", "second", "getSeconds", "fractionalSecond", "milliseconds", "getMilliseconds", "fractionalSeconds", "Math", "floor", "pow", "offset", "tzOffset", "getTimezoneOffset", "absoluteOffset", "abs", "hourOffset", "minuteOffset", "sign"], "sources": ["C:/Users/<USER>/Documents/jeansalechange/adminFront/node_modules/date-fns/esm/formatRFC3339/index.js"], "sourcesContent": ["import toDate from \"../toDate/index.js\";\nimport isValid from \"../isValid/index.js\";\nimport addLeadingZeros from \"../_lib/addLeadingZeros/index.js\";\nimport toInteger from \"../_lib/toInteger/index.js\";\n/**\n * @name formatRFC3339\n * @category Common Helpers\n * @summary Format the date according to the RFC 3339 standard (https://tools.ietf.org/html/rfc3339#section-5.6).\n *\n * @description\n * Return the formatted date string in RFC 3339 format. Options may be passed to control the parts and notations of the date.\n *\n * @param {Date|Number} date - the original date\n * @param {Object} [options] - an object with options.\n * @param {0|1|2|3} [options.fractionDigits=0] - number of digits after the decimal point after seconds\n * @returns {String} the formatted date string\n * @throws {TypeError} 1 argument required\n * @throws {RangeError} `date` must not be Invalid Date\n * @throws {RangeError} `options.fractionDigits` must be between 0 and 3\n *\n * @example\n * // Represent 18 September 2019 in RFC 3339 format:\n * const result = formatRFC3339(new Date(2019, 8, 18, 19, 0, 52))\n * //=> '2019-09-18T19:00:52Z'\n *\n * @example\n * // Represent 18 September 2019 in RFC 3339 format, 2 digits of second fraction:\n * const result = formatRFC3339(new Date(2019, 8, 18, 19, 0, 52, 234), { fractionDigits: 2 })\n * //=> '2019-09-18T19:00:52.23Z'\n *\n * @example\n * // Represent 18 September 2019 in RFC 3339 format, 3 digits of second fraction\n * const result = formatRFC3339(new Date(2019, 8, 18, 19, 0, 52, 234), { fractionDigits: 3 })\n * //=> '2019-09-18T19:00:52.234Z'\n */\nexport default function formatRFC3339(dirtyDate, options) {\n  var _options$fractionDigi;\n  if (arguments.length < 1) {\n    throw new TypeError(\"1 arguments required, but only \".concat(arguments.length, \" present\"));\n  }\n  var originalDate = toDate(dirtyDate);\n  if (!isValid(originalDate)) {\n    throw new RangeError('Invalid time value');\n  }\n  var fractionDigits = Number((_options$fractionDigi = options === null || options === void 0 ? void 0 : options.fractionDigits) !== null && _options$fractionDigi !== void 0 ? _options$fractionDigi : 0);\n\n  // Test if fractionDigits is between 0 and 3 _and_ is not NaN\n  if (!(fractionDigits >= 0 && fractionDigits <= 3)) {\n    throw new RangeError('fractionDigits must be between 0 and 3 inclusively');\n  }\n  var day = addLeadingZeros(originalDate.getDate(), 2);\n  var month = addLeadingZeros(originalDate.getMonth() + 1, 2);\n  var year = originalDate.getFullYear();\n  var hour = addLeadingZeros(originalDate.getHours(), 2);\n  var minute = addLeadingZeros(originalDate.getMinutes(), 2);\n  var second = addLeadingZeros(originalDate.getSeconds(), 2);\n  var fractionalSecond = '';\n  if (fractionDigits > 0) {\n    var milliseconds = originalDate.getMilliseconds();\n    var fractionalSeconds = Math.floor(milliseconds * Math.pow(10, fractionDigits - 3));\n    fractionalSecond = '.' + addLeadingZeros(fractionalSeconds, fractionDigits);\n  }\n  var offset = '';\n  var tzOffset = originalDate.getTimezoneOffset();\n  if (tzOffset !== 0) {\n    var absoluteOffset = Math.abs(tzOffset);\n    var hourOffset = addLeadingZeros(toInteger(absoluteOffset / 60), 2);\n    var minuteOffset = addLeadingZeros(absoluteOffset % 60, 2);\n    // If less than 0, the sign is +, because it is ahead of time.\n    var sign = tzOffset < 0 ? '+' : '-';\n    offset = \"\".concat(sign).concat(hourOffset, \":\").concat(minuteOffset);\n  } else {\n    offset = 'Z';\n  }\n  return \"\".concat(year, \"-\").concat(month, \"-\").concat(day, \"T\").concat(hour, \":\").concat(minute, \":\").concat(second).concat(fractionalSecond).concat(offset);\n}"], "mappings": "AAAA,OAAOA,MAAM,MAAM,oBAAoB;AACvC,OAAOC,OAAO,MAAM,qBAAqB;AACzC,OAAOC,eAAe,MAAM,kCAAkC;AAC9D,OAAOC,SAAS,MAAM,4BAA4B;AAClD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,SAASC,aAAaA,CAACC,SAAS,EAAEC,OAAO,EAAE;EACxD,IAAIC,qBAAqB;EACzB,IAAIC,SAAS,CAACC,MAAM,GAAG,CAAC,EAAE;IACxB,MAAM,IAAIC,SAAS,CAAC,iCAAiC,CAACC,MAAM,CAACH,SAAS,CAACC,MAAM,EAAE,UAAU,CAAC,CAAC;EAC7F;EACA,IAAIG,YAAY,GAAGZ,MAAM,CAACK,SAAS,CAAC;EACpC,IAAI,CAACJ,OAAO,CAACW,YAAY,CAAC,EAAE;IAC1B,MAAM,IAAIC,UAAU,CAAC,oBAAoB,CAAC;EAC5C;EACA,IAAIC,cAAc,GAAGC,MAAM,CAAC,CAACR,qBAAqB,GAAGD,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACQ,cAAc,MAAM,IAAI,IAAIP,qBAAqB,KAAK,KAAK,CAAC,GAAGA,qBAAqB,GAAG,CAAC,CAAC;;EAExM;EACA,IAAI,EAAEO,cAAc,IAAI,CAAC,IAAIA,cAAc,IAAI,CAAC,CAAC,EAAE;IACjD,MAAM,IAAID,UAAU,CAAC,oDAAoD,CAAC;EAC5E;EACA,IAAIG,GAAG,GAAGd,eAAe,CAACU,YAAY,CAACK,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC;EACpD,IAAIC,KAAK,GAAGhB,eAAe,CAACU,YAAY,CAACO,QAAQ,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;EAC3D,IAAIC,IAAI,GAAGR,YAAY,CAACS,WAAW,CAAC,CAAC;EACrC,IAAIC,IAAI,GAAGpB,eAAe,CAACU,YAAY,CAACW,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC;EACtD,IAAIC,MAAM,GAAGtB,eAAe,CAACU,YAAY,CAACa,UAAU,CAAC,CAAC,EAAE,CAAC,CAAC;EAC1D,IAAIC,MAAM,GAAGxB,eAAe,CAACU,YAAY,CAACe,UAAU,CAAC,CAAC,EAAE,CAAC,CAAC;EAC1D,IAAIC,gBAAgB,GAAG,EAAE;EACzB,IAAId,cAAc,GAAG,CAAC,EAAE;IACtB,IAAIe,YAAY,GAAGjB,YAAY,CAACkB,eAAe,CAAC,CAAC;IACjD,IAAIC,iBAAiB,GAAGC,IAAI,CAACC,KAAK,CAACJ,YAAY,GAAGG,IAAI,CAACE,GAAG,CAAC,EAAE,EAAEpB,cAAc,GAAG,CAAC,CAAC,CAAC;IACnFc,gBAAgB,GAAG,GAAG,GAAG1B,eAAe,CAAC6B,iBAAiB,EAAEjB,cAAc,CAAC;EAC7E;EACA,IAAIqB,MAAM,GAAG,EAAE;EACf,IAAIC,QAAQ,GAAGxB,YAAY,CAACyB,iBAAiB,CAAC,CAAC;EAC/C,IAAID,QAAQ,KAAK,CAAC,EAAE;IAClB,IAAIE,cAAc,GAAGN,IAAI,CAACO,GAAG,CAACH,QAAQ,CAAC;IACvC,IAAII,UAAU,GAAGtC,eAAe,CAACC,SAAS,CAACmC,cAAc,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC;IACnE,IAAIG,YAAY,GAAGvC,eAAe,CAACoC,cAAc,GAAG,EAAE,EAAE,CAAC,CAAC;IAC1D;IACA,IAAII,IAAI,GAAGN,QAAQ,GAAG,CAAC,GAAG,GAAG,GAAG,GAAG;IACnCD,MAAM,GAAG,EAAE,CAACxB,MAAM,CAAC+B,IAAI,CAAC,CAAC/B,MAAM,CAAC6B,UAAU,EAAE,GAAG,CAAC,CAAC7B,MAAM,CAAC8B,YAAY,CAAC;EACvE,CAAC,MAAM;IACLN,MAAM,GAAG,GAAG;EACd;EACA,OAAO,EAAE,CAACxB,MAAM,CAACS,IAAI,EAAE,GAAG,CAAC,CAACT,MAAM,CAACO,KAAK,EAAE,GAAG,CAAC,CAACP,MAAM,CAACK,GAAG,EAAE,GAAG,CAAC,CAACL,MAAM,CAACW,IAAI,EAAE,GAAG,CAAC,CAACX,MAAM,CAACa,MAAM,EAAE,GAAG,CAAC,CAACb,MAAM,CAACe,MAAM,CAAC,CAACf,MAAM,CAACiB,gBAAgB,CAAC,CAACjB,MAAM,CAACwB,MAAM,CAAC;AAC9J", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}