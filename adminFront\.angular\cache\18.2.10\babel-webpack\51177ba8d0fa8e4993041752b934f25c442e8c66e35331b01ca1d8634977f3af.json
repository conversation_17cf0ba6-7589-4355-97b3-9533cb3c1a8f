{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nfunction SimpleDropdownTestComponent_div_6_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\")(1, \"button\", 12);\n    i0.ɵɵlistener(\"click\", function SimpleDropdownTestComponent_div_6_div_9_Template_button_click_1_listener() {\n      const building_r3 = i0.ɵɵrestoreView(_r2).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r3.selectBuilding(building_r3));\n    });\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const building_r3 = ctx.$implicit;\n    const i_r5 = ctx.index;\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵstyleMapInterpolate1(\"\\n                  display: block; \\n                  width: 100%; \\n                  padding: 8px; \\n                  margin: 2px 0; \\n                  border: 1px solid #333; \\n                  background: \", ctx_r3.selectedBuilding === building_r3 ? \"green\" : \"white\", \";\\n                  cursor: pointer;\\n                \");\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate2(\" \", building_r3, \" (\", i_r5, \") \");\n  }\n}\nfunction SimpleDropdownTestComponent_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 6)(1, \"div\", 7)(2, \"h4\");\n    i0.ɵɵtext(3, \"\\u6E2C\\u8A66\\u5167\\u5BB9\\u5340\\u57DF\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"p\");\n    i0.ɵɵtext(5, \"\\u5982\\u679C\\u4F60\\u80FD\\u770B\\u5230\\u9019\\u500B\\uFF0C\\u8AAA\\u660E\\u4E0B\\u62C9\\u9078\\u55AE\\u6B63\\u5E38\\u986F\\u793A\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 8)(7, \"h5\");\n    i0.ɵɵtext(8, \"\\u68DF\\u5225\\u5217\\u8868\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(9, SimpleDropdownTestComponent_div_6_div_9_Template, 3, 5, \"div\", 9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"div\", 10)(11, \"h5\");\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"button\", 11);\n    i0.ɵɵlistener(\"click\", function SimpleDropdownTestComponent_div_6_Template_button_click_13_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.close());\n    });\n    i0.ɵɵtext(14, \" \\u95DC\\u9589 \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.buildings);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\\u7576\\u524D\\u9078\\u64C7: \", ctx_r3.selectedBuilding || \"\\u7121\", \"\");\n  }\n}\nexport class SimpleDropdownTestComponent {\n  constructor() {\n    this.isOpen = false;\n    this.selectedBuilding = '';\n    this.buildings = ['A棟', 'B棟', 'C棟', 'D棟', 'E棟'];\n  }\n  toggle() {\n    this.isOpen = !this.isOpen;\n    console.log('Simple dropdown toggled:', this.isOpen);\n  }\n  selectBuilding(building) {\n    this.selectedBuilding = building;\n    console.log('Building selected:', building);\n  }\n  close() {\n    this.isOpen = false;\n    console.log('Simple dropdown closed');\n  }\n  forceUpdate() {\n    console.log('Force update triggered');\n    // 觸發變更檢測\n  }\n  static {\n    this.ɵfac = function SimpleDropdownTestComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || SimpleDropdownTestComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: SimpleDropdownTestComponent,\n      selectors: [[\"app-simple-dropdown-test\"]],\n      decls: 18,\n      vars: 5,\n      consts: [[2, \"margin\", \"20px\", \"padding\", \"20px\", \"border\", \"2px solid red\"], [2, \"position\", \"relative\", \"width\", \"300px\"], [\"type\", \"button\", 2, \"width\", \"100%\", \"padding\", \"10px\", \"border\", \"1px solid #ccc\", \"background\", \"white\", \"cursor\", \"pointer\", 3, \"click\"], [\"style\", \"\\n            position: absolute !important;\\n            top: 100% !important;\\n            left: 0 !important;\\n            right: 0 !important;\\n            z-index: 99999 !important;\\n            background: white !important;\\n            border: 2px solid red !important;\\n            padding: 20px !important;\\n            box-shadow: 0 4px 8px rgba(0,0,0,0.3) !important;\\n            display: block !important;\\n            visibility: visible !important;\\n            opacity: 1 !important;\\n            transform: none !important;\\n            overflow: visible !important;\\n            max-height: none !important;\\n            height: auto !important;\\n            width: auto !important;\\n          \", 4, \"ngIf\"], [2, \"margin-top\", \"20px\", \"padding\", \"10px\", \"background\", \"#f0f0f0\"], [\"type\", \"button\", 2, \"padding\", \"5px 10px\", 3, \"click\"], [2, \"position\", \"absolute !important\", \"top\", \"100% !important\", \"left\", \"0 !important\", \"right\", \"0 !important\", \"z-index\", \"99999 !important\", \"background\", \"white !important\", \"border\", \"2px solid red !important\", \"padding\", \"20px !important\", \"box-shadow\", \"0 4px 8px rgba(0,0,0,0.3) !important\", \"display\", \"block !important\", \"visibility\", \"visible !important\", \"opacity\", \"1 !important\", \"transform\", \"none !important\", \"overflow\", \"visible !important\", \"max-height\", \"none !important\", \"height\", \"auto !important\", \"width\", \"auto !important\"], [2, \"background\", \"yellow\", \"padding\", \"10px\", \"margin\", \"5px\"], [2, \"background\", \"lightblue\", \"padding\", \"10px\", \"margin\", \"5px\"], [4, \"ngFor\", \"ngForOf\"], [2, \"background\", \"lightgreen\", \"padding\", \"10px\", \"margin\", \"5px\"], [\"type\", \"button\", 2, \"padding\", \"5px 10px\", \"background\", \"red\", \"color\", \"white\", \"border\", \"none\", \"cursor\", \"pointer\", 3, \"click\"], [\"type\", \"button\", 3, \"click\"]],\n      template: function SimpleDropdownTestComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"h3\");\n          i0.ɵɵtext(2, \"\\u6975\\u7C21\\u4E0B\\u62C9\\u9078\\u55AE\\u6E2C\\u8A66\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(3, \"div\", 1)(4, \"button\", 2);\n          i0.ɵɵlistener(\"click\", function SimpleDropdownTestComponent_Template_button_click_4_listener() {\n            return ctx.toggle();\n          });\n          i0.ɵɵtext(5);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(6, SimpleDropdownTestComponent_div_6_Template, 15, 2, \"div\", 3);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(7, \"div\", 4)(8, \"h4\");\n          i0.ɵɵtext(9, \"\\u9664\\u932F\\u8CC7\\u8A0A\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(10, \"p\");\n          i0.ɵɵtext(11);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(12, \"p\");\n          i0.ɵɵtext(13);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(14, \"p\");\n          i0.ɵɵtext(15);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(16, \"button\", 5);\n          i0.ɵɵlistener(\"click\", function SimpleDropdownTestComponent_Template_button_click_16_listener() {\n            return ctx.forceUpdate();\n          });\n          i0.ɵɵtext(17, \"\\u5F37\\u5236\\u66F4\\u65B0\");\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(5);\n          i0.ɵɵtextInterpolate1(\" \\u9EDE\\u64CA\\u6E2C\\u8A66 (isOpen: \", ctx.isOpen, \") \");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isOpen);\n          i0.ɵɵadvance(5);\n          i0.ɵɵtextInterpolate1(\"isOpen: \", ctx.isOpen, \"\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate1(\"selectedBuilding: \", ctx.selectedBuilding, \"\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate1(\"buildings.length: \", ctx.buildings.length, \"\");\n        }\n      },\n      dependencies: [i1.NgForOf, i1.NgIf],\n      encapsulation: 2\n    });\n  }\n}", "map": {"version": 3, "names": ["i0", "ɵɵelementStart", "ɵɵlistener", "SimpleDropdownTestComponent_div_6_div_9_Template_button_click_1_listener", "building_r3", "ɵɵrestoreView", "_r2", "$implicit", "ctx_r3", "ɵɵnextContext", "ɵɵresetView", "selectBuilding", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵstyleMapInterpolate1", "selectedBuilding", "ɵɵtextInterpolate2", "i_r5", "ɵɵtemplate", "SimpleDropdownTestComponent_div_6_div_9_Template", "SimpleDropdownTestComponent_div_6_Template_button_click_13_listener", "_r1", "close", "ɵɵproperty", "buildings", "ɵɵtextInterpolate1", "SimpleDropdownTestComponent", "constructor", "isOpen", "toggle", "console", "log", "building", "forceUpdate", "selectors", "decls", "vars", "consts", "template", "SimpleDropdownTestComponent_Template", "rf", "ctx", "SimpleDropdownTestComponent_Template_button_click_4_listener", "SimpleDropdownTestComponent_div_6_Template", "SimpleDropdownTestComponent_Template_button_click_16_listener", "length"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\shared\\components\\household-binding\\simple-dropdown-test.component.ts"], "sourcesContent": ["import { Component } from '@angular/core';\r\n\r\n@Component({\r\n  selector: 'app-simple-dropdown-test',\r\n  template: `\r\n    <div style=\"margin: 20px; padding: 20px; border: 2px solid red;\">\r\n      <h3>極簡下拉選單測試</h3>\r\n      \r\n      <div style=\"position: relative; width: 300px;\">\r\n        <button \r\n          type=\"button\" \r\n          (click)=\"toggle()\" \r\n          style=\"width: 100%; padding: 10px; border: 1px solid #ccc; background: white; cursor: pointer;\">\r\n          點擊測試 (isOpen: {{isOpen}})\r\n        </button>\r\n        \r\n        <!-- 強制覆蓋所有可能的樣式 -->\r\n        <div \r\n          *ngIf=\"isOpen\" \r\n          style=\"\r\n            position: absolute !important;\r\n            top: 100% !important;\r\n            left: 0 !important;\r\n            right: 0 !important;\r\n            z-index: 99999 !important;\r\n            background: white !important;\r\n            border: 2px solid red !important;\r\n            padding: 20px !important;\r\n            box-shadow: 0 4px 8px rgba(0,0,0,0.3) !important;\r\n            display: block !important;\r\n            visibility: visible !important;\r\n            opacity: 1 !important;\r\n            transform: none !important;\r\n            overflow: visible !important;\r\n            max-height: none !important;\r\n            height: auto !important;\r\n            width: auto !important;\r\n          \">\r\n          \r\n          <div style=\"background: yellow; padding: 10px; margin: 5px;\">\r\n            <h4>測試內容區域</h4>\r\n            <p>如果你能看到這個，說明下拉選單正常顯示</p>\r\n          </div>\r\n          \r\n          <div style=\"background: lightblue; padding: 10px; margin: 5px;\">\r\n            <h5>棟別列表</h5>\r\n            <div *ngFor=\"let building of buildings; let i = index\">\r\n              <button \r\n                type=\"button\" \r\n                (click)=\"selectBuilding(building)\"\r\n                style=\"\r\n                  display: block; \r\n                  width: 100%; \r\n                  padding: 8px; \r\n                  margin: 2px 0; \r\n                  border: 1px solid #333; \r\n                  background: {{selectedBuilding === building ? 'green' : 'white'}};\r\n                  cursor: pointer;\r\n                \">\r\n                {{building}} ({{i}})\r\n              </button>\r\n            </div>\r\n          </div>\r\n          \r\n          <div style=\"background: lightgreen; padding: 10px; margin: 5px;\">\r\n            <h5>當前選擇: {{selectedBuilding || '無'}}</h5>\r\n            <button type=\"button\" (click)=\"close()\" style=\"padding: 5px 10px; background: red; color: white; border: none; cursor: pointer;\">\r\n              關閉\r\n            </button>\r\n          </div>\r\n          \r\n        </div>\r\n      </div>\r\n      \r\n      <div style=\"margin-top: 20px; padding: 10px; background: #f0f0f0;\">\r\n        <h4>除錯資訊</h4>\r\n        <p>isOpen: {{isOpen}}</p>\r\n        <p>selectedBuilding: {{selectedBuilding}}</p>\r\n        <p>buildings.length: {{buildings.length}}</p>\r\n        <button type=\"button\" (click)=\"forceUpdate()\" style=\"padding: 5px 10px;\">強制更新</button>\r\n      </div>\r\n    </div>\r\n  `\r\n})\r\nexport class SimpleDropdownTestComponent {\r\n  isOpen = false;\r\n  selectedBuilding = '';\r\n  buildings = ['A棟', 'B棟', 'C棟', 'D棟', 'E棟'];\r\n  \r\n  toggle() {\r\n    this.isOpen = !this.isOpen;\r\n    console.log('Simple dropdown toggled:', this.isOpen);\r\n  }\r\n  \r\n  selectBuilding(building: string) {\r\n    this.selectedBuilding = building;\r\n    console.log('Building selected:', building);\r\n  }\r\n  \r\n  close() {\r\n    this.isOpen = false;\r\n    console.log('Simple dropdown closed');\r\n  }\r\n  \r\n  forceUpdate() {\r\n    console.log('Force update triggered');\r\n    // 觸發變更檢測\r\n  }\r\n}\r\n"], "mappings": ";;;;;IA+CcA,EADF,CAAAC,cAAA,UAAuD,iBAYjD;IATFD,EAAA,CAAAE,UAAA,mBAAAC,yEAAA;MAAA,MAAAC,WAAA,GAAAJ,EAAA,CAAAK,aAAA,CAAAC,GAAA,EAAAC,SAAA;MAAA,MAAAC,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASF,MAAA,CAAAG,cAAA,CAAAP,WAAA,CAAwB;IAAA,EAAC;IAUlCJ,EAAA,CAAAY,MAAA,GACF;IACFZ,EADE,CAAAa,YAAA,EAAS,EACL;;;;;;IAXFb,EAAA,CAAAc,SAAA,EAQC;IARDd,EAAA,CAAAe,sBAAA,2NAAAP,MAAA,CAAAQ,gBAAA,KAAAZ,WAAA,gFAQC;IACDJ,EAAA,CAAAc,SAAA,EACF;IADEd,EAAA,CAAAiB,kBAAA,MAAAb,WAAA,QAAAc,IAAA,OACF;;;;;;IApBFlB,EAvBJ,CAAAC,cAAA,aAoBI,aAE2D,SACvD;IAAAD,EAAA,CAAAY,MAAA,2CAAM;IAAAZ,EAAA,CAAAa,YAAA,EAAK;IACfb,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAY,MAAA,yHAAmB;IACxBZ,EADwB,CAAAa,YAAA,EAAI,EACtB;IAGJb,EADF,CAAAC,cAAA,aAAgE,SAC1D;IAAAD,EAAA,CAAAY,MAAA,+BAAI;IAAAZ,EAAA,CAAAa,YAAA,EAAK;IACbb,EAAA,CAAAmB,UAAA,IAAAC,gDAAA,iBAAuD;IAgBzDpB,EAAA,CAAAa,YAAA,EAAM;IAGJb,EADF,CAAAC,cAAA,eAAiE,UAC3D;IAAAD,EAAA,CAAAY,MAAA,IAAiC;IAAAZ,EAAA,CAAAa,YAAA,EAAK;IAC1Cb,EAAA,CAAAC,cAAA,kBAAiI;IAA3GD,EAAA,CAAAE,UAAA,mBAAAmB,oEAAA;MAAArB,EAAA,CAAAK,aAAA,CAAAiB,GAAA;MAAA,MAAAd,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASF,MAAA,CAAAe,KAAA,EAAO;IAAA,EAAC;IACrCvB,EAAA,CAAAY,MAAA,sBACF;IAGJZ,EAHI,CAAAa,YAAA,EAAS,EACL,EAEF;;;;IAzBwBb,EAAA,CAAAc,SAAA,GAAc;IAAdd,EAAA,CAAAwB,UAAA,YAAAhB,MAAA,CAAAiB,SAAA,CAAc;IAmBpCzB,EAAA,CAAAc,SAAA,GAAiC;IAAjCd,EAAA,CAAA0B,kBAAA,+BAAAlB,MAAA,CAAAQ,gBAAA,iBAAiC;;;AAmBjD,OAAM,MAAOW,2BAA2B;EAlFxCC,YAAA;IAmFE,KAAAC,MAAM,GAAG,KAAK;IACd,KAAAb,gBAAgB,GAAG,EAAE;IACrB,KAAAS,SAAS,GAAG,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;;EAE1CK,MAAMA,CAAA;IACJ,IAAI,CAACD,MAAM,GAAG,CAAC,IAAI,CAACA,MAAM;IAC1BE,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAE,IAAI,CAACH,MAAM,CAAC;EACtD;EAEAlB,cAAcA,CAACsB,QAAgB;IAC7B,IAAI,CAACjB,gBAAgB,GAAGiB,QAAQ;IAChCF,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEC,QAAQ,CAAC;EAC7C;EAEAV,KAAKA,CAAA;IACH,IAAI,CAACM,MAAM,GAAG,KAAK;IACnBE,OAAO,CAACC,GAAG,CAAC,wBAAwB,CAAC;EACvC;EAEAE,WAAWA,CAAA;IACTH,OAAO,CAACC,GAAG,CAAC,wBAAwB,CAAC;IACrC;EACF;;;uCAvBWL,2BAA2B;IAAA;EAAA;;;YAA3BA,2BAA2B;MAAAQ,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,qCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UA9ElCzC,EADF,CAAAC,cAAA,aAAiE,SAC3D;UAAAD,EAAA,CAAAY,MAAA,uDAAQ;UAAAZ,EAAA,CAAAa,YAAA,EAAK;UAGfb,EADF,CAAAC,cAAA,aAA+C,gBAIqD;UADhGD,EAAA,CAAAE,UAAA,mBAAAyC,6DAAA;YAAA,OAASD,GAAA,CAAAZ,MAAA,EAAQ;UAAA,EAAC;UAElB9B,EAAA,CAAAY,MAAA,GACF;UAAAZ,EAAA,CAAAa,YAAA,EAAS;UAGTb,EAAA,CAAAmB,UAAA,IAAAyB,0CAAA,kBAoBI;UAmCN5C,EAAA,CAAAa,YAAA,EAAM;UAGJb,EADF,CAAAC,cAAA,aAAmE,SAC7D;UAAAD,EAAA,CAAAY,MAAA,+BAAI;UAAAZ,EAAA,CAAAa,YAAA,EAAK;UACbb,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAY,MAAA,IAAkB;UAAAZ,EAAA,CAAAa,YAAA,EAAI;UACzBb,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAY,MAAA,IAAsC;UAAAZ,EAAA,CAAAa,YAAA,EAAI;UAC7Cb,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAY,MAAA,IAAsC;UAAAZ,EAAA,CAAAa,YAAA,EAAI;UAC7Cb,EAAA,CAAAC,cAAA,iBAAyE;UAAnDD,EAAA,CAAAE,UAAA,mBAAA2C,8DAAA;YAAA,OAASH,GAAA,CAAAR,WAAA,EAAa;UAAA,EAAC;UAA4BlC,EAAA,CAAAY,MAAA,gCAAI;UAEjFZ,EAFiF,CAAAa,YAAA,EAAS,EAClF,EACF;;;UApEAb,EAAA,CAAAc,SAAA,GACF;UADEd,EAAA,CAAA0B,kBAAA,wCAAAgB,GAAA,CAAAb,MAAA,OACF;UAIG7B,EAAA,CAAAc,SAAA,EAAY;UAAZd,EAAA,CAAAwB,UAAA,SAAAkB,GAAA,CAAAb,MAAA,CAAY;UA0DZ7B,EAAA,CAAAc,SAAA,GAAkB;UAAlBd,EAAA,CAAA0B,kBAAA,aAAAgB,GAAA,CAAAb,MAAA,KAAkB;UAClB7B,EAAA,CAAAc,SAAA,GAAsC;UAAtCd,EAAA,CAAA0B,kBAAA,uBAAAgB,GAAA,CAAA1B,gBAAA,KAAsC;UACtChB,EAAA,CAAAc,SAAA,GAAsC;UAAtCd,EAAA,CAAA0B,kBAAA,uBAAAgB,GAAA,CAAAjB,SAAA,CAAAqB,MAAA,KAAsC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}