{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { CommonModule } from '@angular/common';\nimport { Component } from '@angular/core';\nimport { tap } from 'rxjs';\nimport { SharedModule } from '../../components/shared.module';\nimport { decodeJwtPayload } from '@nebular/auth';\nimport { LocalStorageService } from 'src/app/shared/services/local-storage.service';\nimport { STORAGE_KEY } from 'src/app/shared/constant/constant';\nlet DetailApprovalWaitingComponent = class DetailApprovalWaitingComponent {\n  constructor(_specialChangeService, _activatedRoute, _ultilityService, _location, message, _validationHelper, _eventService, fileService) {\n    this._specialChangeService = _specialChangeService;\n    this._activatedRoute = _activatedRoute;\n    this._ultilityService = _ultilityService;\n    this._location = _location;\n    this.message = message;\n    this._validationHelper = _validationHelper;\n    this._eventService = _eventService;\n    this.fileService = fileService;\n    this.CType = 1;\n    this.remark = \"\";\n    this.decodeJWT = decodeJwtPayload(LocalStorageService.GetLocalStorage(STORAGE_KEY.TOKEN));\n    this.CID = parseInt(this._activatedRoute.snapshot.paramMap.get(\"id\"));\n    this.buildCaseID = parseInt(this._activatedRoute.snapshot.paramMap.get(\"buildCaseId\"));\n    this._activatedRoute.queryParams.pipe(tap(p => {\n      this.CType = p[\"type\"];\n    })).subscribe();\n  }\n  ngOnInit() {\n    this.getApprovalWaitingById();\n  }\n  getApprovalWaitingById() {\n    this._specialChangeService.apiSpecialChangeGetApproveWaitingByIdPost$Json({\n      body: {\n        CID: this.CID,\n        CType: this.CType.toString()\n      }\n    }).pipe(tap(res => {\n      if (res.StatusCode === 0) {\n        this.approvalWaiting = res.Entries;\n      }\n    })).subscribe();\n  }\n  downloadFile(CFile, CFileName) {\n    // if (CFile && CFileName) {\n    //   this._ultilityService.downloadFileFullUrl(\n    //     CFile, CFileName\n    //   )\n    // }\n    window.open(CFile, \"_blank\");\n  }\n  handleAction(isApprove) {\n    if (!isApprove) {\n      this.validation();\n      if (this._validationHelper.errorMessages.length > 0) {\n        this.message.showErrorMSGs(this._validationHelper.errorMessages);\n        return;\n      }\n    }\n    this._specialChangeService.apiSpecialChangeUpdateApproveWaitingPost$Json({\n      body: {\n        CID: this.CID,\n        CType: this.CType,\n        CIsApprove: isApprove,\n        CRemark: this.remark\n      }\n    }).pipe(tap(res => {\n      if (res.StatusCode === 0) {\n        this.message.showSucessMSG(\"執行成功\");\n        this.getApprovalWaitingById();\n        if (this.approvalWaiting.CApproveRecord?.length == 0) {\n          this.approvalWaiting.CApproveRecord?.push({\n            CCreator: this.decodeJWT.userName,\n            CRecordDate: new Date().toISOString(),\n            CRemark: this.remark\n          });\n        }\n        this.remark = \"\";\n      }\n      this.goBack();\n    })).subscribe();\n  }\n  goBack() {\n    this._eventService.push({\n      action: \"GET_BUILDCASE\" /* EEvent.GET_BUILDCASE */,\n      payload: this.buildCaseID\n    });\n    this._location.back();\n  }\n  validation() {\n    this._validationHelper.clear();\n    this._validationHelper.required(\"[備註]\", this.remark);\n  }\n  // 檢查是否有有效的檔案\n  hasValidFiles() {\n    if (!this.approvalWaiting?.CFileApproves) {\n      return false;\n    }\n    return this.approvalWaiting.CFileApproves.some(file => file.CFile && (file.CFileName || file.CFile.length > 0));\n  }\n};\nDetailApprovalWaitingComponent = __decorate([Component({\n  selector: 'app-detail-approval-waiting',\n  standalone: true,\n  imports: [CommonModule, SharedModule],\n  templateUrl: './detail-approval-waiting.component.html',\n  styleUrls: ['./detail-approval-waiting.component.scss']\n})], DetailApprovalWaitingComponent);\nexport { DetailApprovalWaitingComponent };", "map": {"version": 3, "names": ["CommonModule", "Component", "tap", "SharedModule", "decodeJwtPayload", "LocalStorageService", "STORAGE_KEY", "DetailApprovalWaitingComponent", "constructor", "_specialChangeService", "_activatedRoute", "_ultilityService", "_location", "message", "_validationHelper", "_eventService", "fileService", "CType", "remark", "decodeJWT", "GetLocalStorage", "TOKEN", "CID", "parseInt", "snapshot", "paramMap", "get", "buildCaseID", "queryParams", "pipe", "p", "subscribe", "ngOnInit", "getApprovalWaitingById", "apiSpecialChangeGetApproveWaitingByIdPost$Json", "body", "toString", "res", "StatusCode", "approvalWaiting", "Entries", "downloadFile", "CFile", "CFileName", "window", "open", "handleAction", "isApprove", "validation", "errorMessages", "length", "showErrorMSGs", "apiSpecialChangeUpdateApproveWaitingPost$Json", "CIsApprove", "CRemark", "showSucessMSG", "CApproveRecord", "push", "CCreator", "userName", "CRecordDate", "Date", "toISOString", "goBack", "action", "payload", "back", "clear", "required", "hasValidFiles", "CFileApproves", "some", "file", "__decorate", "selector", "standalone", "imports", "templateUrl", "styleUrls"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\approve-waiting\\detail-approval-waiting\\detail-approval-waiting.component.ts"], "sourcesContent": ["import { CommonModule, Location } from '@angular/common';\r\nimport { Component, OnInit } from '@angular/core';\r\nimport { ActivatedRoute } from '@angular/router';\r\nimport { tap } from 'rxjs';\r\nimport { ApproveWaitingByIdRes } from 'src/services/api/models';\r\nimport { SpecialChangeService } from 'src/services/api/services';\r\nimport { SharedModule } from '../../components/shared.module';\r\nimport { UtilityService } from 'src/app/shared/services/utility.service';\r\nimport { decodeJwtPayload } from '@nebular/auth';\r\nimport { LocalStorageService } from 'src/app/shared/services/local-storage.service';\r\nimport { STORAGE_KEY } from 'src/app/shared/constant/constant';\r\nimport { BaseFilePipe } from 'src/app/@theme/pipes/base-file.pipe';\r\nimport { MessageService } from 'src/app/shared/services/message.service';\r\nimport { ValidationHelper } from 'src/app/shared/helper/validationHelper';\r\nimport { EEvent, EventService } from 'src/app/shared/services/event.service';\r\nimport { FileUploadComponent } from '../../components/file-upload/file-upload.component';\r\nimport { FileService } from 'src/services/api/services/File.service';\r\n\r\n@Component({\r\n  selector: 'app-detail-approval-waiting',\r\n  standalone: true,\r\n  imports: [\r\n    CommonModule,\r\n    SharedModule\r\n  ],\r\n  templateUrl: './detail-approval-waiting.component.html',\r\n  styleUrls: ['./detail-approval-waiting.component.scss']\r\n})\r\nexport class DetailApprovalWaitingComponent implements OnInit {\r\n\r\n  CType: number = 1;\r\n  CID: number\r\n  remark: string = \"\"\r\n  buildCaseID: number\r\n\r\n  approvalWaiting: ApproveWaitingByIdRes\r\n  decodeJWT: any\r\n  constructor(\r\n    private _specialChangeService: SpecialChangeService,\r\n    private _activatedRoute: ActivatedRoute,\r\n    private _ultilityService: UtilityService,\r\n    private _location: Location,\r\n    private message: MessageService,\r\n    private _validationHelper: ValidationHelper,\r\n    private _eventService: EventService,\r\n    private fileService: FileService,\r\n  ) {\r\n    this.decodeJWT = decodeJwtPayload(LocalStorageService.GetLocalStorage(STORAGE_KEY.TOKEN))\r\n    this.CID = parseInt(this._activatedRoute.snapshot.paramMap!.get(\"id\")!);\r\n    this.buildCaseID = parseInt(this._activatedRoute.snapshot.paramMap!.get(\"buildCaseId\")!)\r\n    this._activatedRoute.queryParams.pipe(\r\n      tap(p => {\r\n        this.CType = p[\"type\"]\r\n      })\r\n    ).subscribe();\r\n  }\r\n\r\n  ngOnInit(): void {\r\n    this.getApprovalWaitingById()\r\n  }\r\n  getApprovalWaitingById() {\r\n    this._specialChangeService.apiSpecialChangeGetApproveWaitingByIdPost$Json({\r\n      body: {\r\n        CID: this.CID,\r\n        CType: this.CType.toString()\r\n      }\r\n    }).pipe(\r\n      tap(res => {\r\n        if (res.StatusCode === 0) {\r\n          this.approvalWaiting = res.Entries!\r\n        }\r\n      })\r\n    ).subscribe();\r\n  }\r\n\r\n  downloadFile(CFile: any, CFileName: any) {\r\n    // if (CFile && CFileName) {\r\n    //   this._ultilityService.downloadFileFullUrl(\r\n    //     CFile, CFileName\r\n    //   )\r\n    // }\r\n    window.open(CFile, \"_blank\");\r\n  }\r\n\r\n  handleAction(isApprove: boolean) {\r\n    if (!isApprove) {\r\n      this.validation()\r\n      if (this._validationHelper.errorMessages.length > 0) {\r\n        this.message.showErrorMSGs(this._validationHelper.errorMessages);\r\n        return\r\n      }\r\n    }\r\n    this._specialChangeService.apiSpecialChangeUpdateApproveWaitingPost$Json({\r\n      body: {\r\n        CID: this.CID,\r\n        CType: this.CType,\r\n        CIsApprove: isApprove,\r\n        CRemark: this.remark\r\n      }\r\n    }).pipe(\r\n      tap(res => {\r\n        if (res.StatusCode === 0) {\r\n          this.message.showSucessMSG(\"執行成功\");\r\n          this.getApprovalWaitingById()\r\n          if (this.approvalWaiting.CApproveRecord?.length == 0) {\r\n            this.approvalWaiting.CApproveRecord?.push({\r\n              CCreator: this.decodeJWT.userName,\r\n              CRecordDate: new Date().toISOString(),\r\n              CRemark: this.remark\r\n            })\r\n          }\r\n          this.remark = \"\"\r\n        }\r\n        this.goBack();\r\n      })\r\n    ).subscribe();\r\n  }\r\n\r\n  goBack() {\r\n    this._eventService.push({\r\n      action: EEvent.GET_BUILDCASE,\r\n      payload: this.buildCaseID\r\n    })\r\n    this._location.back()\r\n  }\r\n\r\n  validation() {\r\n    this._validationHelper.clear();\r\n    this._validationHelper.required(\"[備註]\", this.remark)\r\n  }\r\n\r\n  // 檢查是否有有效的檔案\r\n  hasValidFiles(): boolean {\r\n    if (!this.approvalWaiting?.CFileApproves) {\r\n      return false;\r\n    }\r\n    \r\n    return this.approvalWaiting.CFileApproves.some(file => \r\n      file.CFile && (file.CFileName || file.CFile.length > 0)\r\n    );\r\n  }\r\n}\r\n"], "mappings": ";AAAA,SAASA,YAAY,QAAkB,iBAAiB;AACxD,SAASC,SAAS,QAAgB,eAAe;AAEjD,SAASC,GAAG,QAAQ,MAAM;AAG1B,SAASC,YAAY,QAAQ,gCAAgC;AAE7D,SAASC,gBAAgB,QAAQ,eAAe;AAChD,SAASC,mBAAmB,QAAQ,+CAA+C;AACnF,SAASC,WAAW,QAAQ,kCAAkC;AAkBvD,IAAMC,8BAA8B,GAApC,MAAMA,8BAA8B;EASzCC,YACUC,qBAA2C,EAC3CC,eAA+B,EAC/BC,gBAAgC,EAChCC,SAAmB,EACnBC,OAAuB,EACvBC,iBAAmC,EACnCC,aAA2B,EAC3BC,WAAwB;IAPxB,KAAAP,qBAAqB,GAArBA,qBAAqB;IACrB,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,SAAS,GAATA,SAAS;IACT,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAC,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,WAAW,GAAXA,WAAW;IAfrB,KAAAC,KAAK,GAAW,CAAC;IAEjB,KAAAC,MAAM,GAAW,EAAE;IAejB,IAAI,CAACC,SAAS,GAAGf,gBAAgB,CAACC,mBAAmB,CAACe,eAAe,CAACd,WAAW,CAACe,KAAK,CAAC,CAAC;IACzF,IAAI,CAACC,GAAG,GAAGC,QAAQ,CAAC,IAAI,CAACb,eAAe,CAACc,QAAQ,CAACC,QAAS,CAACC,GAAG,CAAC,IAAI,CAAE,CAAC;IACvE,IAAI,CAACC,WAAW,GAAGJ,QAAQ,CAAC,IAAI,CAACb,eAAe,CAACc,QAAQ,CAACC,QAAS,CAACC,GAAG,CAAC,aAAa,CAAE,CAAC;IACxF,IAAI,CAAChB,eAAe,CAACkB,WAAW,CAACC,IAAI,CACnC3B,GAAG,CAAC4B,CAAC,IAAG;MACN,IAAI,CAACb,KAAK,GAAGa,CAAC,CAAC,MAAM,CAAC;IACxB,CAAC,CAAC,CACH,CAACC,SAAS,EAAE;EACf;EAEAC,QAAQA,CAAA;IACN,IAAI,CAACC,sBAAsB,EAAE;EAC/B;EACAA,sBAAsBA,CAAA;IACpB,IAAI,CAACxB,qBAAqB,CAACyB,8CAA8C,CAAC;MACxEC,IAAI,EAAE;QACJb,GAAG,EAAE,IAAI,CAACA,GAAG;QACbL,KAAK,EAAE,IAAI,CAACA,KAAK,CAACmB,QAAQ;;KAE7B,CAAC,CAACP,IAAI,CACL3B,GAAG,CAACmC,GAAG,IAAG;MACR,IAAIA,GAAG,CAACC,UAAU,KAAK,CAAC,EAAE;QACxB,IAAI,CAACC,eAAe,GAAGF,GAAG,CAACG,OAAQ;MACrC;IACF,CAAC,CAAC,CACH,CAACT,SAAS,EAAE;EACf;EAEAU,YAAYA,CAACC,KAAU,EAAEC,SAAc;IACrC;IACA;IACA;IACA;IACA;IACAC,MAAM,CAACC,IAAI,CAACH,KAAK,EAAE,QAAQ,CAAC;EAC9B;EAEAI,YAAYA,CAACC,SAAkB;IAC7B,IAAI,CAACA,SAAS,EAAE;MACd,IAAI,CAACC,UAAU,EAAE;MACjB,IAAI,IAAI,CAAClC,iBAAiB,CAACmC,aAAa,CAACC,MAAM,GAAG,CAAC,EAAE;QACnD,IAAI,CAACrC,OAAO,CAACsC,aAAa,CAAC,IAAI,CAACrC,iBAAiB,CAACmC,aAAa,CAAC;QAChE;MACF;IACF;IACA,IAAI,CAACxC,qBAAqB,CAAC2C,6CAA6C,CAAC;MACvEjB,IAAI,EAAE;QACJb,GAAG,EAAE,IAAI,CAACA,GAAG;QACbL,KAAK,EAAE,IAAI,CAACA,KAAK;QACjBoC,UAAU,EAAEN,SAAS;QACrBO,OAAO,EAAE,IAAI,CAACpC;;KAEjB,CAAC,CAACW,IAAI,CACL3B,GAAG,CAACmC,GAAG,IAAG;MACR,IAAIA,GAAG,CAACC,UAAU,KAAK,CAAC,EAAE;QACxB,IAAI,CAACzB,OAAO,CAAC0C,aAAa,CAAC,MAAM,CAAC;QAClC,IAAI,CAACtB,sBAAsB,EAAE;QAC7B,IAAI,IAAI,CAACM,eAAe,CAACiB,cAAc,EAAEN,MAAM,IAAI,CAAC,EAAE;UACpD,IAAI,CAACX,eAAe,CAACiB,cAAc,EAAEC,IAAI,CAAC;YACxCC,QAAQ,EAAE,IAAI,CAACvC,SAAS,CAACwC,QAAQ;YACjCC,WAAW,EAAE,IAAIC,IAAI,EAAE,CAACC,WAAW,EAAE;YACrCR,OAAO,EAAE,IAAI,CAACpC;WACf,CAAC;QACJ;QACA,IAAI,CAACA,MAAM,GAAG,EAAE;MAClB;MACA,IAAI,CAAC6C,MAAM,EAAE;IACf,CAAC,CAAC,CACH,CAAChC,SAAS,EAAE;EACf;EAEAgC,MAAMA,CAAA;IACJ,IAAI,CAAChD,aAAa,CAAC0C,IAAI,CAAC;MACtBO,MAAM;MACNC,OAAO,EAAE,IAAI,CAACtC;KACf,CAAC;IACF,IAAI,CAACf,SAAS,CAACsD,IAAI,EAAE;EACvB;EAEAlB,UAAUA,CAAA;IACR,IAAI,CAAClC,iBAAiB,CAACqD,KAAK,EAAE;IAC9B,IAAI,CAACrD,iBAAiB,CAACsD,QAAQ,CAAC,MAAM,EAAE,IAAI,CAAClD,MAAM,CAAC;EACtD;EAEA;EACAmD,aAAaA,CAAA;IACX,IAAI,CAAC,IAAI,CAAC9B,eAAe,EAAE+B,aAAa,EAAE;MACxC,OAAO,KAAK;IACd;IAEA,OAAO,IAAI,CAAC/B,eAAe,CAAC+B,aAAa,CAACC,IAAI,CAACC,IAAI,IACjDA,IAAI,CAAC9B,KAAK,KAAK8B,IAAI,CAAC7B,SAAS,IAAI6B,IAAI,CAAC9B,KAAK,CAACQ,MAAM,GAAG,CAAC,CAAC,CACxD;EACH;CACD;AAjHY3C,8BAA8B,GAAAkE,UAAA,EAV1CxE,SAAS,CAAC;EACTyE,QAAQ,EAAE,6BAA6B;EACvCC,UAAU,EAAE,IAAI;EAChBC,OAAO,EAAE,CACP5E,YAAY,EACZG,YAAY,CACb;EACD0E,WAAW,EAAE,0CAA0C;EACvDC,SAAS,EAAE,CAAC,0CAA0C;CACvD,CAAC,C,EACWvE,8BAA8B,CAiH1C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}