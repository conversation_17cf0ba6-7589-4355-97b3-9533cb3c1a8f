{"ast": null, "code": "import { of as observableOf } from 'rxjs';\nimport { TrafficChartData } from '../data/traffic-chart';\nimport * as i0 from \"@angular/core\";\nexport class TrafficChartService extends TrafficChartData {\n  constructor() {\n    super(...arguments);\n    this.data = [300, 520, 435, 530, 730, 620, 660, 860];\n  }\n  getTrafficChartData() {\n    return observableOf(this.data);\n  }\n  static {\n    this.ɵfac = /*@__PURE__*/(() => {\n      let ɵTrafficChartService_BaseFactory;\n      return function TrafficChartService_Factory(__ngFactoryType__) {\n        return (ɵTrafficChartService_BaseFactory || (ɵTrafficChartService_BaseFactory = i0.ɵɵgetInheritedFactory(TrafficChartService)))(__ngFactoryType__ || TrafficChartService);\n      };\n    })();\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: TrafficChartService,\n      factory: TrafficChartService.ɵfac\n    });\n  }\n}", "map": {"version": 3, "names": ["of", "observableOf", "TrafficChartData", "TrafficChartService", "constructor", "data", "getTrafficChartData", "__ngFactoryType__", "factory", "ɵfac"], "sources": ["C:\\Users\\<USER>\\Documents\\jeansalechange\\adminFront\\src\\app\\@core\\mock\\traffic-chart.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport { of as observableOf,  Observable } from 'rxjs';\r\nimport { TrafficChartData } from '../data/traffic-chart';\r\n\r\n@Injectable()\r\nexport class TrafficChartService extends TrafficChartData {\r\n\r\n  private data: number[] = [\r\n    300, 520, 435, 530,\r\n    730, 620, 660, 860,\r\n  ];\r\n\r\n  getTrafficChartData(): Observable<number[]> {\r\n    return observableOf(this.data);\r\n  }\r\n}\r\n"], "mappings": "AACA,SAASA,EAAE,IAAIC,YAAY,QAAqB,MAAM;AACtD,SAASC,gBAAgB,QAAQ,uBAAuB;;AAGxD,OAAM,MAAOC,mBAAoB,SAAQD,gBAAgB;EADzDE,YAAA;;IAGU,KAAAC,IAAI,GAAa,CACvB,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAClB,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CACnB;;EAEDC,mBAAmBA,CAAA;IACjB,OAAOL,YAAY,CAAC,IAAI,CAACI,IAAI,CAAC;EAChC;;;;;iHATWF,mBAAmB,IAAAI,iBAAA,IAAnBJ,mBAAmB;MAAA;IAAA;EAAA;;;aAAnBA,mBAAmB;MAAAK,OAAA,EAAnBL,mBAAmB,CAAAM;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}