{"ast": null, "code": "import { map } from 'rxjs/operators';\nimport { BaseServiceAPI } from '../base-service';\nimport { apiRegularNoticeFileDeleteRegularNoticeFilePost$Json } from '../fn/regular-notice-file/api-regular-notice-file-delete-regular-notice-file-post-json';\nimport { apiRegularNoticeFileDeleteRegularNoticeFilePost$Plain } from '../fn/regular-notice-file/api-regular-notice-file-delete-regular-notice-file-post-plain';\nimport { apiRegularNoticeFileGetListRegularNoticeFileHouseHoldPost$Json } from '../fn/regular-notice-file/api-regular-notice-file-get-list-regular-notice-file-house-hold-post-json';\nimport { apiRegularNoticeFileGetListRegularNoticeFileHouseHoldPost$Plain } from '../fn/regular-notice-file/api-regular-notice-file-get-list-regular-notice-file-house-hold-post-plain';\nimport { apiRegularNoticeFileGetRegularNoticeFileByIdPost$Json } from '../fn/regular-notice-file/api-regular-notice-file-get-regular-notice-file-by-id-post-json';\nimport { apiRegularNoticeFileGetRegularNoticeFileByIdPost$Plain } from '../fn/regular-notice-file/api-regular-notice-file-get-regular-notice-file-by-id-post-plain';\nimport { apiRegularNoticeFileGetRegularNoticeFileListPost$Json } from '../fn/regular-notice-file/api-regular-notice-file-get-regular-notice-file-list-post-json';\nimport { apiRegularNoticeFileGetRegularNoticeFileListPost$Plain } from '../fn/regular-notice-file/api-regular-notice-file-get-regular-notice-file-list-post-plain';\nimport { apiRegularNoticeFileSaveRegularNoticeFilePost$Json } from '../fn/regular-notice-file/api-regular-notice-file-save-regular-notice-file-post-json';\nimport { apiRegularNoticeFileSaveRegularNoticeFilePost$Plain } from '../fn/regular-notice-file/api-regular-notice-file-save-regular-notice-file-post-plain';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../api-configuration\";\nimport * as i2 from \"@angular/common/http\";\nexport class RegularNoticeFileService extends BaseServiceAPI {\n  constructor(config, http) {\n    super(config, http);\n  }\n  /** Path part for operation `apiRegularNoticeFileDeleteRegularNoticeFilePost()` */\n  static {\n    this.ApiRegularNoticeFileDeleteRegularNoticeFilePostPath = '/api/RegularNoticeFile/DeleteRegularNoticeFile';\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiRegularNoticeFileDeleteRegularNoticeFilePost$Plain()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiRegularNoticeFileDeleteRegularNoticeFilePost$Plain$Response(params, context) {\n    return apiRegularNoticeFileDeleteRegularNoticeFilePost$Plain(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiRegularNoticeFileDeleteRegularNoticeFilePost$Plain$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiRegularNoticeFileDeleteRegularNoticeFilePost$Plain(params, context) {\n    return this.apiRegularNoticeFileDeleteRegularNoticeFilePost$Plain$Response(params, context).pipe(map(r => r.body));\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiRegularNoticeFileDeleteRegularNoticeFilePost$Json()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiRegularNoticeFileDeleteRegularNoticeFilePost$Json$Response(params, context) {\n    return apiRegularNoticeFileDeleteRegularNoticeFilePost$Json(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiRegularNoticeFileDeleteRegularNoticeFilePost$Json$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiRegularNoticeFileDeleteRegularNoticeFilePost$Json(params, context) {\n    return this.apiRegularNoticeFileDeleteRegularNoticeFilePost$Json$Response(params, context).pipe(map(r => r.body));\n  }\n  /** Path part for operation `apiRegularNoticeFileGetRegularNoticeFileListPost()` */\n  static {\n    this.ApiRegularNoticeFileGetRegularNoticeFileListPostPath = '/api/RegularNoticeFile/GetRegularNoticeFileList';\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiRegularNoticeFileGetRegularNoticeFileListPost$Plain()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiRegularNoticeFileGetRegularNoticeFileListPost$Plain$Response(params, context) {\n    return apiRegularNoticeFileGetRegularNoticeFileListPost$Plain(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiRegularNoticeFileGetRegularNoticeFileListPost$Plain$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiRegularNoticeFileGetRegularNoticeFileListPost$Plain(params, context) {\n    return this.apiRegularNoticeFileGetRegularNoticeFileListPost$Plain$Response(params, context).pipe(map(r => r.body));\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiRegularNoticeFileGetRegularNoticeFileListPost$Json()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiRegularNoticeFileGetRegularNoticeFileListPost$Json$Response(params, context) {\n    return apiRegularNoticeFileGetRegularNoticeFileListPost$Json(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiRegularNoticeFileGetRegularNoticeFileListPost$Json$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiRegularNoticeFileGetRegularNoticeFileListPost$Json(params, context) {\n    return this.apiRegularNoticeFileGetRegularNoticeFileListPost$Json$Response(params, context).pipe(map(r => r.body));\n  }\n  /** Path part for operation `apiRegularNoticeFileGetRegularNoticeFileByIdPost()` */\n  static {\n    this.ApiRegularNoticeFileGetRegularNoticeFileByIdPostPath = '/api/RegularNoticeFile/GetRegularNoticeFileById';\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiRegularNoticeFileGetRegularNoticeFileByIdPost$Plain()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiRegularNoticeFileGetRegularNoticeFileByIdPost$Plain$Response(params, context) {\n    return apiRegularNoticeFileGetRegularNoticeFileByIdPost$Plain(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiRegularNoticeFileGetRegularNoticeFileByIdPost$Plain$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiRegularNoticeFileGetRegularNoticeFileByIdPost$Plain(params, context) {\n    return this.apiRegularNoticeFileGetRegularNoticeFileByIdPost$Plain$Response(params, context).pipe(map(r => r.body));\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiRegularNoticeFileGetRegularNoticeFileByIdPost$Json()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiRegularNoticeFileGetRegularNoticeFileByIdPost$Json$Response(params, context) {\n    return apiRegularNoticeFileGetRegularNoticeFileByIdPost$Json(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiRegularNoticeFileGetRegularNoticeFileByIdPost$Json$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiRegularNoticeFileGetRegularNoticeFileByIdPost$Json(params, context) {\n    return this.apiRegularNoticeFileGetRegularNoticeFileByIdPost$Json$Response(params, context).pipe(map(r => r.body));\n  }\n  /** Path part for operation `apiRegularNoticeFileSaveRegularNoticeFilePost()` */\n  static {\n    this.ApiRegularNoticeFileSaveRegularNoticeFilePostPath = '/api/RegularNoticeFile/SaveRegularNoticeFile';\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiRegularNoticeFileSaveRegularNoticeFilePost$Plain()` instead.\n   *\n   * This method sends `multipart/form-data` and handles request body of type `multipart/form-data`.\n   */\n  apiRegularNoticeFileSaveRegularNoticeFilePost$Plain$Response(params, context) {\n    return apiRegularNoticeFileSaveRegularNoticeFilePost$Plain(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiRegularNoticeFileSaveRegularNoticeFilePost$Plain$Response()` instead.\n   *\n   * This method sends `multipart/form-data` and handles request body of type `multipart/form-data`.\n   */\n  apiRegularNoticeFileSaveRegularNoticeFilePost$Plain(params, context) {\n    return this.apiRegularNoticeFileSaveRegularNoticeFilePost$Plain$Response(params, context).pipe(map(r => r.body));\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiRegularNoticeFileSaveRegularNoticeFilePost$Json()` instead.\n   *\n   * This method sends `multipart/form-data` and handles request body of type `multipart/form-data`.\n   */\n  apiRegularNoticeFileSaveRegularNoticeFilePost$Json$Response(params, context) {\n    return apiRegularNoticeFileSaveRegularNoticeFilePost$Json(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiRegularNoticeFileSaveRegularNoticeFilePost$Json$Response()` instead.\n   *\n   * This method sends `multipart/form-data` and handles request body of type `multipart/form-data`.\n   */\n  apiRegularNoticeFileSaveRegularNoticeFilePost$Json(params, context) {\n    return this.apiRegularNoticeFileSaveRegularNoticeFilePost$Json$Response(params, context).pipe(map(r => r.body));\n  }\n  /** Path part for operation `apiRegularNoticeFileGetListRegularNoticeFileHouseHoldPost()` */\n  static {\n    this.ApiRegularNoticeFileGetListRegularNoticeFileHouseHoldPostPath = '/api/RegularNoticeFile/GetListRegularNoticeFileHouseHold';\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiRegularNoticeFileGetListRegularNoticeFileHouseHoldPost$Plain()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiRegularNoticeFileGetListRegularNoticeFileHouseHoldPost$Plain$Response(params, context) {\n    return apiRegularNoticeFileGetListRegularNoticeFileHouseHoldPost$Plain(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiRegularNoticeFileGetListRegularNoticeFileHouseHoldPost$Plain$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiRegularNoticeFileGetListRegularNoticeFileHouseHoldPost$Plain(params, context) {\n    return this.apiRegularNoticeFileGetListRegularNoticeFileHouseHoldPost$Plain$Response(params, context).pipe(map(r => r.body));\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiRegularNoticeFileGetListRegularNoticeFileHouseHoldPost$Json()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiRegularNoticeFileGetListRegularNoticeFileHouseHoldPost$Json$Response(params, context) {\n    return apiRegularNoticeFileGetListRegularNoticeFileHouseHoldPost$Json(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiRegularNoticeFileGetListRegularNoticeFileHouseHoldPost$Json$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiRegularNoticeFileGetListRegularNoticeFileHouseHoldPost$Json(params, context) {\n    return this.apiRegularNoticeFileGetListRegularNoticeFileHouseHoldPost$Json$Response(params, context).pipe(map(r => r.body));\n  }\n  static {\n    this.ɵfac = function RegularNoticeFileService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || RegularNoticeFileService)(i0.ɵɵinject(i1.ApiConfiguration), i0.ɵɵinject(i2.HttpClient));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: RegularNoticeFileService,\n      factory: RegularNoticeFileService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["map", "BaseServiceAPI", "apiRegularNoticeFileDeleteRegularNoticeFilePost$Json", "apiRegularNoticeFileDeleteRegularNoticeFilePost$Plain", "apiRegularNoticeFileGetListRegularNoticeFileHouseHoldPost$Json", "apiRegularNoticeFileGetListRegularNoticeFileHouseHoldPost$Plain", "apiRegularNoticeFileGetRegularNoticeFileByIdPost$Json", "apiRegularNoticeFileGetRegularNoticeFileByIdPost$Plain", "apiRegularNoticeFileGetRegularNoticeFileListPost$Json", "apiRegularNoticeFileGetRegularNoticeFileListPost$Plain", "apiRegularNoticeFileSaveRegularNoticeFilePost$Json", "apiRegularNoticeFileSaveRegularNoticeFilePost$Plain", "RegularNoticeFileService", "constructor", "config", "http", "ApiRegularNoticeFileDeleteRegularNoticeFilePostPath", "apiRegularNoticeFileDeleteRegularNoticeFilePost$Plain$Response", "params", "context", "rootUrl", "pipe", "r", "body", "apiRegularNoticeFileDeleteRegularNoticeFilePost$Json$Response", "ApiRegularNoticeFileGetRegularNoticeFileListPostPath", "apiRegularNoticeFileGetRegularNoticeFileListPost$Plain$Response", "apiRegularNoticeFileGetRegularNoticeFileListPost$Json$Response", "ApiRegularNoticeFileGetRegularNoticeFileByIdPostPath", "apiRegularNoticeFileGetRegularNoticeFileByIdPost$Plain$Response", "apiRegularNoticeFileGetRegularNoticeFileByIdPost$Json$Response", "ApiRegularNoticeFileSaveRegularNoticeFilePostPath", "apiRegularNoticeFileSaveRegularNoticeFilePost$Plain$Response", "apiRegularNoticeFileSaveRegularNoticeFilePost$Json$Response", "ApiRegularNoticeFileGetListRegularNoticeFileHouseHoldPostPath", "apiRegularNoticeFileGetListRegularNoticeFileHouseHoldPost$Plain$Response", "apiRegularNoticeFileGetListRegularNoticeFileHouseHoldPost$Json$Response", "i0", "ɵɵinject", "i1", "ApiConfiguration", "i2", "HttpClient", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\Documents\\jeansalechange\\adminFront\\src\\services\\api\\services\\regular-notice-file.service.ts"], "sourcesContent": ["/* tslint:disable */\r\n/* eslint-disable */\r\nimport { HttpClient, HttpContext } from '@angular/common/http';\r\nimport { Injectable } from '@angular/core';\r\nimport { Observable } from 'rxjs';\r\nimport { map } from 'rxjs/operators';\r\n\r\nimport { BaseServiceAPI } from '../base-service';\r\nimport { ApiConfiguration } from '../api-configuration';\r\nimport { StrictHttpResponse } from '../strict-http-response';\r\n\r\nimport { apiRegularNoticeFileDeleteRegularNoticeFilePost$Json } from '../fn/regular-notice-file/api-regular-notice-file-delete-regular-notice-file-post-json';\r\nimport { ApiRegularNoticeFileDeleteRegularNoticeFilePost$Json$Params } from '../fn/regular-notice-file/api-regular-notice-file-delete-regular-notice-file-post-json';\r\nimport { apiRegularNoticeFileDeleteRegularNoticeFilePost$Plain } from '../fn/regular-notice-file/api-regular-notice-file-delete-regular-notice-file-post-plain';\r\nimport { ApiRegularNoticeFileDeleteRegularNoticeFilePost$Plain$Params } from '../fn/regular-notice-file/api-regular-notice-file-delete-regular-notice-file-post-plain';\r\nimport { apiRegularNoticeFileGetListRegularNoticeFileHouseHoldPost$Json } from '../fn/regular-notice-file/api-regular-notice-file-get-list-regular-notice-file-house-hold-post-json';\r\nimport { ApiRegularNoticeFileGetListRegularNoticeFileHouseHoldPost$Json$Params } from '../fn/regular-notice-file/api-regular-notice-file-get-list-regular-notice-file-house-hold-post-json';\r\nimport { apiRegularNoticeFileGetListRegularNoticeFileHouseHoldPost$Plain } from '../fn/regular-notice-file/api-regular-notice-file-get-list-regular-notice-file-house-hold-post-plain';\r\nimport { ApiRegularNoticeFileGetListRegularNoticeFileHouseHoldPost$Plain$Params } from '../fn/regular-notice-file/api-regular-notice-file-get-list-regular-notice-file-house-hold-post-plain';\r\nimport { apiRegularNoticeFileGetRegularNoticeFileByIdPost$Json } from '../fn/regular-notice-file/api-regular-notice-file-get-regular-notice-file-by-id-post-json';\r\nimport { ApiRegularNoticeFileGetRegularNoticeFileByIdPost$Json$Params } from '../fn/regular-notice-file/api-regular-notice-file-get-regular-notice-file-by-id-post-json';\r\nimport { apiRegularNoticeFileGetRegularNoticeFileByIdPost$Plain } from '../fn/regular-notice-file/api-regular-notice-file-get-regular-notice-file-by-id-post-plain';\r\nimport { ApiRegularNoticeFileGetRegularNoticeFileByIdPost$Plain$Params } from '../fn/regular-notice-file/api-regular-notice-file-get-regular-notice-file-by-id-post-plain';\r\nimport { apiRegularNoticeFileGetRegularNoticeFileListPost$Json } from '../fn/regular-notice-file/api-regular-notice-file-get-regular-notice-file-list-post-json';\r\nimport { ApiRegularNoticeFileGetRegularNoticeFileListPost$Json$Params } from '../fn/regular-notice-file/api-regular-notice-file-get-regular-notice-file-list-post-json';\r\nimport { apiRegularNoticeFileGetRegularNoticeFileListPost$Plain } from '../fn/regular-notice-file/api-regular-notice-file-get-regular-notice-file-list-post-plain';\r\nimport { ApiRegularNoticeFileGetRegularNoticeFileListPost$Plain$Params } from '../fn/regular-notice-file/api-regular-notice-file-get-regular-notice-file-list-post-plain';\r\nimport { apiRegularNoticeFileSaveRegularNoticeFilePost$Json } from '../fn/regular-notice-file/api-regular-notice-file-save-regular-notice-file-post-json';\r\nimport { ApiRegularNoticeFileSaveRegularNoticeFilePost$Json$Params } from '../fn/regular-notice-file/api-regular-notice-file-save-regular-notice-file-post-json';\r\nimport { apiRegularNoticeFileSaveRegularNoticeFilePost$Plain } from '../fn/regular-notice-file/api-regular-notice-file-save-regular-notice-file-post-plain';\r\nimport { ApiRegularNoticeFileSaveRegularNoticeFilePost$Plain$Params } from '../fn/regular-notice-file/api-regular-notice-file-save-regular-notice-file-post-plain';\r\nimport { GetListHouseHoldResListResponseBase } from '../models/get-list-house-hold-res-list-response-base';\r\nimport { GetRegularNoticeFileByIdResResponseBase } from '../models/get-regular-notice-file-by-id-res-response-base';\r\nimport { GetRegularNoticeFileListResResponseBase } from '../models/get-regular-notice-file-list-res-response-base';\r\nimport { StringResponseBase } from '../models/string-response-base';\r\n\r\n@Injectable({ providedIn: 'root' })\r\nexport class RegularNoticeFileService extends BaseServiceAPI {\r\n  constructor(config: ApiConfiguration, http: HttpClient) {\r\n    super(config, http);\r\n  }\r\n\r\n  /** Path part for operation `apiRegularNoticeFileDeleteRegularNoticeFilePost()` */\r\n  static readonly ApiRegularNoticeFileDeleteRegularNoticeFilePostPath = '/api/RegularNoticeFile/DeleteRegularNoticeFile';\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiRegularNoticeFileDeleteRegularNoticeFilePost$Plain()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiRegularNoticeFileDeleteRegularNoticeFilePost$Plain$Response(params?: ApiRegularNoticeFileDeleteRegularNoticeFilePost$Plain$Params, context?: HttpContext): Observable<StrictHttpResponse<StringResponseBase>> {\r\n    return apiRegularNoticeFileDeleteRegularNoticeFilePost$Plain(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiRegularNoticeFileDeleteRegularNoticeFilePost$Plain$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiRegularNoticeFileDeleteRegularNoticeFilePost$Plain(params?: ApiRegularNoticeFileDeleteRegularNoticeFilePost$Plain$Params, context?: HttpContext): Observable<StringResponseBase> {\r\n    return this.apiRegularNoticeFileDeleteRegularNoticeFilePost$Plain$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<StringResponseBase>): StringResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiRegularNoticeFileDeleteRegularNoticeFilePost$Json()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiRegularNoticeFileDeleteRegularNoticeFilePost$Json$Response(params?: ApiRegularNoticeFileDeleteRegularNoticeFilePost$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<StringResponseBase>> {\r\n    return apiRegularNoticeFileDeleteRegularNoticeFilePost$Json(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiRegularNoticeFileDeleteRegularNoticeFilePost$Json$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiRegularNoticeFileDeleteRegularNoticeFilePost$Json(params?: ApiRegularNoticeFileDeleteRegularNoticeFilePost$Json$Params, context?: HttpContext): Observable<StringResponseBase> {\r\n    return this.apiRegularNoticeFileDeleteRegularNoticeFilePost$Json$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<StringResponseBase>): StringResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /** Path part for operation `apiRegularNoticeFileGetRegularNoticeFileListPost()` */\r\n  static readonly ApiRegularNoticeFileGetRegularNoticeFileListPostPath = '/api/RegularNoticeFile/GetRegularNoticeFileList';\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiRegularNoticeFileGetRegularNoticeFileListPost$Plain()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiRegularNoticeFileGetRegularNoticeFileListPost$Plain$Response(params?: ApiRegularNoticeFileGetRegularNoticeFileListPost$Plain$Params, context?: HttpContext): Observable<StrictHttpResponse<GetRegularNoticeFileListResResponseBase>> {\r\n    return apiRegularNoticeFileGetRegularNoticeFileListPost$Plain(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiRegularNoticeFileGetRegularNoticeFileListPost$Plain$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiRegularNoticeFileGetRegularNoticeFileListPost$Plain(params?: ApiRegularNoticeFileGetRegularNoticeFileListPost$Plain$Params, context?: HttpContext): Observable<GetRegularNoticeFileListResResponseBase> {\r\n    return this.apiRegularNoticeFileGetRegularNoticeFileListPost$Plain$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<GetRegularNoticeFileListResResponseBase>): GetRegularNoticeFileListResResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiRegularNoticeFileGetRegularNoticeFileListPost$Json()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiRegularNoticeFileGetRegularNoticeFileListPost$Json$Response(params?: ApiRegularNoticeFileGetRegularNoticeFileListPost$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<GetRegularNoticeFileListResResponseBase>> {\r\n    return apiRegularNoticeFileGetRegularNoticeFileListPost$Json(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiRegularNoticeFileGetRegularNoticeFileListPost$Json$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiRegularNoticeFileGetRegularNoticeFileListPost$Json(params?: ApiRegularNoticeFileGetRegularNoticeFileListPost$Json$Params, context?: HttpContext): Observable<GetRegularNoticeFileListResResponseBase> {\r\n    return this.apiRegularNoticeFileGetRegularNoticeFileListPost$Json$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<GetRegularNoticeFileListResResponseBase>): GetRegularNoticeFileListResResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /** Path part for operation `apiRegularNoticeFileGetRegularNoticeFileByIdPost()` */\r\n  static readonly ApiRegularNoticeFileGetRegularNoticeFileByIdPostPath = '/api/RegularNoticeFile/GetRegularNoticeFileById';\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiRegularNoticeFileGetRegularNoticeFileByIdPost$Plain()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiRegularNoticeFileGetRegularNoticeFileByIdPost$Plain$Response(params?: ApiRegularNoticeFileGetRegularNoticeFileByIdPost$Plain$Params, context?: HttpContext): Observable<StrictHttpResponse<GetRegularNoticeFileByIdResResponseBase>> {\r\n    return apiRegularNoticeFileGetRegularNoticeFileByIdPost$Plain(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiRegularNoticeFileGetRegularNoticeFileByIdPost$Plain$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiRegularNoticeFileGetRegularNoticeFileByIdPost$Plain(params?: ApiRegularNoticeFileGetRegularNoticeFileByIdPost$Plain$Params, context?: HttpContext): Observable<GetRegularNoticeFileByIdResResponseBase> {\r\n    return this.apiRegularNoticeFileGetRegularNoticeFileByIdPost$Plain$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<GetRegularNoticeFileByIdResResponseBase>): GetRegularNoticeFileByIdResResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiRegularNoticeFileGetRegularNoticeFileByIdPost$Json()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiRegularNoticeFileGetRegularNoticeFileByIdPost$Json$Response(params?: ApiRegularNoticeFileGetRegularNoticeFileByIdPost$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<GetRegularNoticeFileByIdResResponseBase>> {\r\n    return apiRegularNoticeFileGetRegularNoticeFileByIdPost$Json(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiRegularNoticeFileGetRegularNoticeFileByIdPost$Json$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiRegularNoticeFileGetRegularNoticeFileByIdPost$Json(params?: ApiRegularNoticeFileGetRegularNoticeFileByIdPost$Json$Params, context?: HttpContext): Observable<GetRegularNoticeFileByIdResResponseBase> {\r\n    return this.apiRegularNoticeFileGetRegularNoticeFileByIdPost$Json$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<GetRegularNoticeFileByIdResResponseBase>): GetRegularNoticeFileByIdResResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /** Path part for operation `apiRegularNoticeFileSaveRegularNoticeFilePost()` */\r\n  static readonly ApiRegularNoticeFileSaveRegularNoticeFilePostPath = '/api/RegularNoticeFile/SaveRegularNoticeFile';\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiRegularNoticeFileSaveRegularNoticeFilePost$Plain()` instead.\r\n   *\r\n   * This method sends `multipart/form-data` and handles request body of type `multipart/form-data`.\r\n   */\r\n  apiRegularNoticeFileSaveRegularNoticeFilePost$Plain$Response(params?: ApiRegularNoticeFileSaveRegularNoticeFilePost$Plain$Params, context?: HttpContext): Observable<StrictHttpResponse<StringResponseBase>> {\r\n    return apiRegularNoticeFileSaveRegularNoticeFilePost$Plain(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiRegularNoticeFileSaveRegularNoticeFilePost$Plain$Response()` instead.\r\n   *\r\n   * This method sends `multipart/form-data` and handles request body of type `multipart/form-data`.\r\n   */\r\n  apiRegularNoticeFileSaveRegularNoticeFilePost$Plain(params?: ApiRegularNoticeFileSaveRegularNoticeFilePost$Plain$Params, context?: HttpContext): Observable<StringResponseBase> {\r\n    return this.apiRegularNoticeFileSaveRegularNoticeFilePost$Plain$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<StringResponseBase>): StringResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiRegularNoticeFileSaveRegularNoticeFilePost$Json()` instead.\r\n   *\r\n   * This method sends `multipart/form-data` and handles request body of type `multipart/form-data`.\r\n   */\r\n  apiRegularNoticeFileSaveRegularNoticeFilePost$Json$Response(params?: ApiRegularNoticeFileSaveRegularNoticeFilePost$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<StringResponseBase>> {\r\n    return apiRegularNoticeFileSaveRegularNoticeFilePost$Json(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiRegularNoticeFileSaveRegularNoticeFilePost$Json$Response()` instead.\r\n   *\r\n   * This method sends `multipart/form-data` and handles request body of type `multipart/form-data`.\r\n   */\r\n  apiRegularNoticeFileSaveRegularNoticeFilePost$Json(params?: ApiRegularNoticeFileSaveRegularNoticeFilePost$Json$Params, context?: HttpContext): Observable<StringResponseBase> {\r\n    return this.apiRegularNoticeFileSaveRegularNoticeFilePost$Json$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<StringResponseBase>): StringResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /** Path part for operation `apiRegularNoticeFileGetListRegularNoticeFileHouseHoldPost()` */\r\n  static readonly ApiRegularNoticeFileGetListRegularNoticeFileHouseHoldPostPath = '/api/RegularNoticeFile/GetListRegularNoticeFileHouseHold';\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiRegularNoticeFileGetListRegularNoticeFileHouseHoldPost$Plain()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiRegularNoticeFileGetListRegularNoticeFileHouseHoldPost$Plain$Response(params?: ApiRegularNoticeFileGetListRegularNoticeFileHouseHoldPost$Plain$Params, context?: HttpContext): Observable<StrictHttpResponse<GetListHouseHoldResListResponseBase>> {\r\n    return apiRegularNoticeFileGetListRegularNoticeFileHouseHoldPost$Plain(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiRegularNoticeFileGetListRegularNoticeFileHouseHoldPost$Plain$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiRegularNoticeFileGetListRegularNoticeFileHouseHoldPost$Plain(params?: ApiRegularNoticeFileGetListRegularNoticeFileHouseHoldPost$Plain$Params, context?: HttpContext): Observable<GetListHouseHoldResListResponseBase> {\r\n    return this.apiRegularNoticeFileGetListRegularNoticeFileHouseHoldPost$Plain$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<GetListHouseHoldResListResponseBase>): GetListHouseHoldResListResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiRegularNoticeFileGetListRegularNoticeFileHouseHoldPost$Json()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiRegularNoticeFileGetListRegularNoticeFileHouseHoldPost$Json$Response(params?: ApiRegularNoticeFileGetListRegularNoticeFileHouseHoldPost$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<GetListHouseHoldResListResponseBase>> {\r\n    return apiRegularNoticeFileGetListRegularNoticeFileHouseHoldPost$Json(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiRegularNoticeFileGetListRegularNoticeFileHouseHoldPost$Json$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiRegularNoticeFileGetListRegularNoticeFileHouseHoldPost$Json(params?: ApiRegularNoticeFileGetListRegularNoticeFileHouseHoldPost$Json$Params, context?: HttpContext): Observable<GetListHouseHoldResListResponseBase> {\r\n    return this.apiRegularNoticeFileGetListRegularNoticeFileHouseHoldPost$Json$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<GetListHouseHoldResListResponseBase>): GetListHouseHoldResListResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n}\r\n"], "mappings": "AAKA,SAASA,GAAG,QAAQ,gBAAgB;AAEpC,SAASC,cAAc,QAAQ,iBAAiB;AAIhD,SAASC,oDAAoD,QAAQ,wFAAwF;AAE7J,SAASC,qDAAqD,QAAQ,yFAAyF;AAE/J,SAASC,8DAA8D,QAAQ,qGAAqG;AAEpL,SAASC,+DAA+D,QAAQ,sGAAsG;AAEtL,SAASC,qDAAqD,QAAQ,2FAA2F;AAEjK,SAASC,sDAAsD,QAAQ,4FAA4F;AAEnK,SAASC,qDAAqD,QAAQ,0FAA0F;AAEhK,SAASC,sDAAsD,QAAQ,2FAA2F;AAElK,SAASC,kDAAkD,QAAQ,sFAAsF;AAEzJ,SAASC,mDAAmD,QAAQ,uFAAuF;;;;AAQ3J,OAAM,MAAOC,wBAAyB,SAAQX,cAAc;EAC1DY,YAAYC,MAAwB,EAAEC,IAAgB;IACpD,KAAK,CAACD,MAAM,EAAEC,IAAI,CAAC;EACrB;EAEA;;IACgB,KAAAC,mDAAmD,GAAG,gDAAgD;EAAC;EAEvH;;;;;;EAMAC,8DAA8DA,CAACC,MAAqE,EAAEC,OAAqB;IACzJ,OAAOhB,qDAAqD,CAAC,IAAI,CAACY,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EACxG;EAEA;;;;;;EAMAhB,qDAAqDA,CAACe,MAAqE,EAAEC,OAAqB;IAChJ,OAAO,IAAI,CAACF,8DAA8D,CAACC,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CAC9FrB,GAAG,CAAEsB,CAAyC,IAAyBA,CAAC,CAACC,IAAI,CAAC,CAC/E;EACH;EAEA;;;;;;EAMAC,6DAA6DA,CAACN,MAAoE,EAAEC,OAAqB;IACvJ,OAAOjB,oDAAoD,CAAC,IAAI,CAACa,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EACvG;EAEA;;;;;;EAMAjB,oDAAoDA,CAACgB,MAAoE,EAAEC,OAAqB;IAC9I,OAAO,IAAI,CAACK,6DAA6D,CAACN,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CAC7FrB,GAAG,CAAEsB,CAAyC,IAAyBA,CAAC,CAACC,IAAI,CAAC,CAC/E;EACH;EAEA;;IACgB,KAAAE,oDAAoD,GAAG,iDAAiD;EAAC;EAEzH;;;;;;EAMAC,+DAA+DA,CAACR,MAAsE,EAAEC,OAAqB;IAC3J,OAAOV,sDAAsD,CAAC,IAAI,CAACM,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EACzG;EAEA;;;;;;EAMAV,sDAAsDA,CAACS,MAAsE,EAAEC,OAAqB;IAClJ,OAAO,IAAI,CAACO,+DAA+D,CAACR,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CAC/FrB,GAAG,CAAEsB,CAA8D,IAA8CA,CAAC,CAACC,IAAI,CAAC,CACzH;EACH;EAEA;;;;;;EAMAI,8DAA8DA,CAACT,MAAqE,EAAEC,OAAqB;IACzJ,OAAOX,qDAAqD,CAAC,IAAI,CAACO,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EACxG;EAEA;;;;;;EAMAX,qDAAqDA,CAACU,MAAqE,EAAEC,OAAqB;IAChJ,OAAO,IAAI,CAACQ,8DAA8D,CAACT,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CAC9FrB,GAAG,CAAEsB,CAA8D,IAA8CA,CAAC,CAACC,IAAI,CAAC,CACzH;EACH;EAEA;;IACgB,KAAAK,oDAAoD,GAAG,iDAAiD;EAAC;EAEzH;;;;;;EAMAC,+DAA+DA,CAACX,MAAsE,EAAEC,OAAqB;IAC3J,OAAOZ,sDAAsD,CAAC,IAAI,CAACQ,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EACzG;EAEA;;;;;;EAMAZ,sDAAsDA,CAACW,MAAsE,EAAEC,OAAqB;IAClJ,OAAO,IAAI,CAACU,+DAA+D,CAACX,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CAC/FrB,GAAG,CAAEsB,CAA8D,IAA8CA,CAAC,CAACC,IAAI,CAAC,CACzH;EACH;EAEA;;;;;;EAMAO,8DAA8DA,CAACZ,MAAqE,EAAEC,OAAqB;IACzJ,OAAOb,qDAAqD,CAAC,IAAI,CAACS,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EACxG;EAEA;;;;;;EAMAb,qDAAqDA,CAACY,MAAqE,EAAEC,OAAqB;IAChJ,OAAO,IAAI,CAACW,8DAA8D,CAACZ,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CAC9FrB,GAAG,CAAEsB,CAA8D,IAA8CA,CAAC,CAACC,IAAI,CAAC,CACzH;EACH;EAEA;;IACgB,KAAAQ,iDAAiD,GAAG,8CAA8C;EAAC;EAEnH;;;;;;EAMAC,4DAA4DA,CAACd,MAAmE,EAAEC,OAAqB;IACrJ,OAAOR,mDAAmD,CAAC,IAAI,CAACI,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EACtG;EAEA;;;;;;EAMAR,mDAAmDA,CAACO,MAAmE,EAAEC,OAAqB;IAC5I,OAAO,IAAI,CAACa,4DAA4D,CAACd,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CAC5FrB,GAAG,CAAEsB,CAAyC,IAAyBA,CAAC,CAACC,IAAI,CAAC,CAC/E;EACH;EAEA;;;;;;EAMAU,2DAA2DA,CAACf,MAAkE,EAAEC,OAAqB;IACnJ,OAAOT,kDAAkD,CAAC,IAAI,CAACK,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EACrG;EAEA;;;;;;EAMAT,kDAAkDA,CAACQ,MAAkE,EAAEC,OAAqB;IAC1I,OAAO,IAAI,CAACc,2DAA2D,CAACf,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CAC3FrB,GAAG,CAAEsB,CAAyC,IAAyBA,CAAC,CAACC,IAAI,CAAC,CAC/E;EACH;EAEA;;IACgB,KAAAW,6DAA6D,GAAG,0DAA0D;EAAC;EAE3I;;;;;;EAMAC,wEAAwEA,CAACjB,MAA+E,EAAEC,OAAqB;IAC7K,OAAOd,+DAA+D,CAAC,IAAI,CAACU,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EAClH;EAEA;;;;;;EAMAd,+DAA+DA,CAACa,MAA+E,EAAEC,OAAqB;IACpK,OAAO,IAAI,CAACgB,wEAAwE,CAACjB,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CACxGrB,GAAG,CAAEsB,CAA0D,IAA0CA,CAAC,CAACC,IAAI,CAAC,CACjH;EACH;EAEA;;;;;;EAMAa,uEAAuEA,CAAClB,MAA8E,EAAEC,OAAqB;IAC3K,OAAOf,8DAA8D,CAAC,IAAI,CAACW,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EACjH;EAEA;;;;;;EAMAf,8DAA8DA,CAACc,MAA8E,EAAEC,OAAqB;IAClK,OAAO,IAAI,CAACiB,uEAAuE,CAAClB,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CACvGrB,GAAG,CAAEsB,CAA0D,IAA0CA,CAAC,CAACC,IAAI,CAAC,CACjH;EACH;;;uCA9OWX,wBAAwB,EAAAyB,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,gBAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,UAAA;IAAA;EAAA;;;aAAxB9B,wBAAwB;MAAA+B,OAAA,EAAxB/B,wBAAwB,CAAAgC,IAAA;MAAAC,UAAA,EADX;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}