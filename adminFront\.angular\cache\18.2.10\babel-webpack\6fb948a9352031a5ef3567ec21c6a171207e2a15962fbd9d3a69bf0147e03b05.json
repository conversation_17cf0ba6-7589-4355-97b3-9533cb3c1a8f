{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { PagesComponent } from './pages/pages.component';\nimport { HomeComponent } from './pages/home/<USER>';\nimport { LoginComponent } from './pages/login/login.component';\nimport { LogoutComponent } from './pages/logout/logout.component';\nimport { AuthGuard } from './shared/auth/guards/auth.guard';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\n//_import保留字\nexport const routes = [{\n  path: 'login',\n  component: LoginComponent\n}, {\n  path: 'logout',\n  component: LogoutComponent\n}, {\n  path: '',\n  component: PagesComponent,\n  canActivate: [AuthGuard],\n  children: [{\n    path: '',\n    component: HomeComponent\n  },\n  // { path: 'TempSaveSampleComponent', component: TempSaveSampleComponent },\n  {\n    path: 'pages',\n    loadChildren: () => import('./pages/system-management/system-management.module').then(mod => mod.SystemManagementModule)\n  }]\n}, {\n  path: '**',\n  redirectTo: '/'\n}];\nconst config = {\n  useHash: false\n};\nexport class AppRoutingModule {\n  static {\n    this.ɵfac = function AppRoutingModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || AppRoutingModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: AppRoutingModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [RouterModule.forRoot(routes, {\n        onSameUrlNavigation: `reload`\n      }), RouterModule.forChild(routes), RouterModule]\n    });\n  }\n}", "map": {"version": 3, "names": ["RouterModule", "PagesComponent", "HomeComponent", "LoginComponent", "LogoutComponent", "<PERSON><PERSON><PERSON><PERSON>", "routes", "path", "component", "canActivate", "children", "loadChildren", "then", "mod", "SystemManagementModule", "redirectTo", "config", "useHash", "AppRoutingModule", "forRoot", "onSameUrlNavigation", "<PERSON><PERSON><PERSON><PERSON>"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\app-routing.module.ts"], "sourcesContent": ["import { ExtraOptions, RouterModule, Routes } from '@angular/router';\r\nimport { NgModule } from '@angular/core';\r\nimport { PagesComponent } from './pages/pages.component';\r\nimport { HomeComponent } from './pages/home/<USER>';\r\nimport { LoginComponent } from './pages/login/login.component';\r\nimport { LogoutComponent } from './pages/logout/logout.component';\r\nimport { AuthGuard } from './shared/auth/guards/auth.guard';\r\n//_import保留字\r\n\r\nexport const routes: Routes = [\r\n  { path: 'login', component: LoginComponent },\r\n  { path: 'logout', component: LogoutComponent },\r\n  {\r\n    path: '',\r\n    component: PagesComponent,\r\n    canActivate: [AuthGuard],\r\n    children: [\r\n\r\n      { path: '', component: HomeComponent },\r\n      // { path: 'TempSaveSampleComponent', component: TempSaveSampleComponent },\r\n      {\r\n        path: 'pages',\r\n        loadChildren: () => import('./pages/system-management/system-management.module')\r\n          .then(mod => mod.SystemManagementModule),\r\n      },\r\n    ],\r\n  },\r\n  { path: '**', redirectTo: '/' },\r\n];\r\n\r\nconst config: ExtraOptions = {\r\n  useHash: false,\r\n};\r\n\r\n@NgModule({\r\n  imports: [\r\n    RouterModule.forRoot(routes, { onSameUrlNavigation: `reload` }),\r\n    RouterModule.forChild(routes)],\r\n  exports: [RouterModule],\r\n  declarations: [\r\n  ],\r\n})\r\nexport class AppRoutingModule {\r\n}\r\n"], "mappings": "AAAA,SAAuBA,YAAY,QAAgB,iBAAiB;AAEpE,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,aAAa,QAAQ,6BAA6B;AAC3D,SAASC,cAAc,QAAQ,+BAA+B;AAC9D,SAASC,eAAe,QAAQ,iCAAiC;AACjE,SAASC,SAAS,QAAQ,iCAAiC;;;AAC3D;AAEA,OAAO,MAAMC,MAAM,GAAW,CAC5B;EAAEC,IAAI,EAAE,OAAO;EAAEC,SAAS,EAAEL;AAAc,CAAE,EAC5C;EAAEI,IAAI,EAAE,QAAQ;EAAEC,SAAS,EAAEJ;AAAe,CAAE,EAC9C;EACEG,IAAI,EAAE,EAAE;EACRC,SAAS,EAAEP,cAAc;EACzBQ,WAAW,EAAE,CAACJ,SAAS,CAAC;EACxBK,QAAQ,EAAE,CAER;IAAEH,IAAI,EAAE,EAAE;IAAEC,SAAS,EAAEN;EAAa,CAAE;EACtC;EACA;IACEK,IAAI,EAAE,OAAO;IACbI,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,oDAAoD,CAAC,CAC7EC,IAAI,CAACC,GAAG,IAAIA,GAAG,CAACC,sBAAsB;GAC1C;CAEJ,EACD;EAAEP,IAAI,EAAE,IAAI;EAAEQ,UAAU,EAAE;AAAG,CAAE,CAChC;AAED,MAAMC,MAAM,GAAiB;EAC3BC,OAAO,EAAE;CACV;AAUD,OAAM,MAAOC,gBAAgB;;;uCAAhBA,gBAAgB;IAAA;EAAA;;;YAAhBA;IAAgB;EAAA;;;gBANzBlB,YAAY,CAACmB,OAAO,CAACb,MAAM,EAAE;QAAEc,mBAAmB,EAAE;MAAQ,CAAE,CAAC,EAC/DpB,YAAY,CAACqB,QAAQ,CAACf,MAAM,CAAC,EACrBN,YAAY;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}