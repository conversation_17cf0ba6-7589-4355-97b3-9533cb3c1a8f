{"ast": null, "code": "import { ReplaySubject } from \"rxjs\";\nimport * as i0 from \"@angular/core\";\nexport let EventService = /*#__PURE__*/(() => {\n  class EventService {\n    constructor() {\n      this.subjet = new ReplaySubject();\n    }\n    push(data) {\n      this.subjet.next(data);\n    }\n    receive() {\n      return this.subjet;\n    }\n    static {\n      this.ɵfac = function EventService_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || EventService)();\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: EventService,\n        factory: EventService.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return EventService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}