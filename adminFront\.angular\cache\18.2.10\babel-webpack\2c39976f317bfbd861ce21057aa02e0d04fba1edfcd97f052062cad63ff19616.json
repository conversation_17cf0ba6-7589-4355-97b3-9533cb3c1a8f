{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { Component, Input, Output, EventEmitter, forwardRef, ViewChild } from '@angular/core';\nimport { NG_VALUE_ACCESSOR } from '@angular/forms';\nlet HouseholdBindingComponent = class HouseholdBindingComponent {\n  constructor(cdr, houseCustomService, dialogService) {\n    this.cdr = cdr;\n    this.houseCustomService = houseCustomService;\n    this.dialogService = dialogService;\n    this.placeholder = '請選擇戶別';\n    this.maxSelections = null;\n    this.disabled = false;\n    this.buildCaseId = null; // 新增：建案ID\n    this.buildingData = {};\n    this.showSelectedArea = true;\n    this.allowSearch = true;\n    this.allowBatchSelect = true;\n    this.excludedHouseIds = []; // 改為：排除的戶別ID（已被其他元件選擇）\n    this.useHouseNameMode = false; // 新增：是否使用 houseName 模式（預設為 houseId 模式）\n    this.selectionChange = new EventEmitter();\n    this.houseIdChange = new EventEmitter(); // 新增：回傳 houseId 陣列\n    this.houseNameChange = new EventEmitter(); // 新增：回傳 houseName 陣列（houseName 模式專用）\n    this.isOpen = false;\n    this.selectedBuilding = '';\n    this.searchTerm = '';\n    this.selectedFloor = ''; // 新增：選中的樓層\n    this.selectedHouseIds = []; // 改為：使用 houseId 作為選擇的key\n    this.selectedHouseNames = []; // 新增：houseName 模式下使用的選擇列表\n    this.buildings = [];\n    this.floors = []; // 新增：當前棧別的樓層列表\n    this.filteredHouseholds = []; // 保持為字串陣列用於UI顯示\n    this.selectedByBuilding = {}; // 改為：儲存 houseId\n    this.selectedByBuildingNames = {}; // 新增：houseName 模式下儲存 houseName\n    this.isLoading = false; // 新增：載入狀態  isLoading: boolean = false; // 新增：載入狀態\n    // ControlValueAccessor implementation\n    this.onChange = value => {};\n    this.onTouched = () => {};\n  }\n  writeValue(value) {\n    console.log('writeValue called with:', value, 'type:', typeof value?.[0], 'useHouseNameMode:', this.useHouseNameMode);\n    if (!value || value.length === 0) {\n      this.selectedHouseIds = [];\n      this.selectedHouseNames = [];\n    } else {\n      const firstItem = value[0];\n      if (this.useHouseNameMode) {\n        // houseName 模式\n        if (typeof firstItem === 'string') {\n          this.selectedHouseNames = value;\n          // 同時更新 houseIds 以保持內部邏輯一致\n          this.selectedHouseIds = this.convertHouseNamesToIds(this.selectedHouseNames);\n          console.log('houseName 模式：設定 selectedHouseNames:', this.selectedHouseNames);\n          console.log('對應的 houseIds:', this.selectedHouseIds);\n        } else if (typeof firstItem === 'number') {\n          // 如果在 houseName 模式下收到 houseId，進行轉換\n          console.warn('⚠️ houseName 模式下收到 houseId 陣列，進行轉換');\n          this.selectedHouseIds = value;\n          this.selectedHouseNames = this.convertIdsToHouseNames(this.selectedHouseIds);\n          console.log('轉換後的 selectedHouseNames:', this.selectedHouseNames);\n        } else {\n          console.error('houseName 模式下收到未知格式的資料:', value);\n          this.selectedHouseIds = [];\n          this.selectedHouseNames = [];\n        }\n      } else {\n        // houseId 模式（原有邏輯）\n        if (typeof firstItem === 'number') {\n          this.selectedHouseIds = value;\n          this.selectedHouseNames = this.convertIdsToHouseNames(this.selectedHouseIds);\n          console.log('houseId 模式：設定 selectedHouseIds:', this.selectedHouseIds);\n        } else if (typeof firstItem === 'string') {\n          console.warn('⚠️ houseId 模式下收到 houseName 陣列，進行轉換');\n          this.selectedHouseNames = value;\n          this.selectedHouseIds = this.convertHouseNamesToIds(this.selectedHouseNames);\n          console.log('轉換後的 selectedHouseIds:', this.selectedHouseIds);\n        } else {\n          console.error('houseId 模式下收到未知格式的資料:', value);\n          this.selectedHouseIds = [];\n          this.selectedHouseNames = [];\n        }\n      }\n    }\n    this.updateSelectedByBuilding();\n  }\n  registerOnChange(fn) {\n    this.onChange = fn;\n  }\n  registerOnTouched(fn) {\n    this.onTouched = fn;\n  }\n  setDisabledState(isDisabled) {\n    this.disabled = isDisabled;\n  }\n  ngOnInit() {\n    this.initializeData();\n  }\n  ngOnChanges(changes) {\n    if (changes['buildCaseId'] && this.buildCaseId) {\n      // 當建案ID變更時，重新載入資料\n      this.initializeData();\n    }\n    if (changes['buildingData']) {\n      // 當 buildingData 變更時，重新初始化\n      this.buildings = Object.keys(this.buildingData || {});\n      console.log('buildingData updated:', this.buildingData);\n      this.updateFilteredHouseholds();\n      this.updateSelectedByBuilding();\n    }\n    if (changes['excludedHouseIds']) {\n      // 當排除列表變化時，重新更新顯示\n      this.updateFilteredHouseholds();\n      console.log('Excluded house IDs updated:', this.excludedHouseIds);\n    }\n  }\n  initializeData() {\n    // 優先檢查是否有傳入 buildingData\n    if (this.buildingData && Object.keys(this.buildingData).length > 0) {\n      // 使用傳入的 buildingData\n      this.buildings = Object.keys(this.buildingData);\n      console.log('Component initialized with provided buildingData:', this.buildings);\n      this.updateSelectedByBuilding();\n    } else if (this.buildCaseId) {\n      // 只有在沒有傳入 buildingData 且有建案ID時才呼叫API\n      this.loadBuildingDataFromApi();\n    } else {\n      // 既沒有 buildingData 也沒有 buildCaseId，保持空狀態\n      this.buildings = [];\n      console.log('No buildingData or buildCaseId provided');\n    }\n  }\n  loadBuildingDataFromApi() {\n    if (!this.buildCaseId) return;\n    this.isLoading = true;\n    this.houseCustomService.getDropDown(this.buildCaseId).subscribe({\n      next: response => {\n        console.log('API response:', response);\n        this.buildingData = this.convertApiResponseToBuildingData(response.Entries);\n        this.buildings = Object.keys(this.buildingData);\n        this.updateSelectedByBuilding();\n        this.isLoading = false;\n        this.cdr.detectChanges();\n      },\n      error: error => {\n        console.error('Error loading building data:', error);\n        // API載入失敗時，不使用備援資料，保持空狀態\n        this.buildingData = {};\n        this.buildings = [];\n        this.isLoading = false;\n        this.cdr.detectChanges();\n      }\n    });\n  }\n  convertApiResponseToBuildingData(entries) {\n    const buildingData = {};\n    Object.entries(entries).forEach(([building, houses]) => {\n      buildingData[building] = houses.map(house => ({\n        houseName: house.houseName || house.HouseName || house.code,\n        building: house.building || house.Building || building,\n        floor: house.floor || house.Floor,\n        houseId: house.houseId || house.HouseId,\n        isSelected: false,\n        isDisabled: false\n      }));\n    });\n    return buildingData;\n  }\n  updateSelectedByBuilding() {\n    // 更新 houseId 分組\n    const grouped = {};\n    this.selectedHouseIds.forEach(houseId => {\n      for (const building of this.buildings) {\n        const item = this.buildingData[building]?.find(h => h.houseId === houseId);\n        if (item) {\n          if (!grouped[building]) grouped[building] = [];\n          grouped[building].push(houseId);\n          break;\n        }\n      }\n    });\n    this.selectedByBuilding = grouped;\n    // 如果是 houseName 模式，也更新 houseName 分組\n    if (this.useHouseNameMode) {\n      this.updateSelectedByBuildingNames();\n    }\n  }\n  onBuildingSelect(building) {\n    console.log('Building selected:', building);\n    this.selectedBuilding = building;\n    this.selectedFloor = ''; // 重置樓層選擇\n    this.searchTerm = '';\n    this.updateFloorsForBuilding(); // 更新樓層列表\n    this.updateFilteredHouseholds();\n    console.log('Filtered households count:', this.filteredHouseholds.length);\n    // 手動觸發變更偵測\n    this.cdr.detectChanges();\n  }\n  onBuildingClick(building) {\n    console.log('Building clicked (mousedown):', building);\n  }\n  updateFilteredHouseholds() {\n    console.log('Updating filtered households for building:', this.selectedBuilding, 'floor:', this.selectedFloor);\n    if (!this.selectedBuilding) {\n      this.filteredHouseholds = [];\n      return;\n    }\n    const households = this.buildingData[this.selectedBuilding] || [];\n    console.log('Available households for building:', households.length);\n    // 提取戶別代碼並進行搜尋和樓層過濾\n    const filteredItems = households.filter(h => {\n      // 樓層篩選：如果有選擇樓層，只顯示該樓層的戶別\n      const floorMatch = !this.selectedFloor || h.floor === this.selectedFloor;\n      // 搜尋篩選：戶別代碼包含搜尋詞\n      const searchMatch = !this.searchTerm || h.houseName.toLowerCase().includes(this.searchTerm.toLowerCase());\n      return floorMatch && searchMatch;\n    });\n    // 只提取戶別名稱用於UI顯示（但onSelectAllFiltered會直接使用物件）\n    this.filteredHouseholds = filteredItems.map(h => h.houseName);\n    console.log('Filtered households result:', this.filteredHouseholds.length);\n    console.log('Filtered items details:', filteredItems.map(h => `${h.houseName}-${h.floor} (ID:${h.houseId})`));\n  }\n  onSearchChange(event) {\n    this.searchTerm = event.target.value;\n    this.updateFilteredHouseholds();\n    console.log('Search term changed:', this.searchTerm);\n  }\n  resetSearch() {\n    this.searchTerm = '';\n    this.updateFilteredHouseholds();\n    console.log('Search reset');\n  }\n  onHouseholdToggle(houseId) {\n    console.log('onHouseholdToggle called with houseId:', houseId);\n    console.log('Current selectedHouseIds:', this.selectedHouseIds);\n    if (!houseId) {\n      console.log(`無效的 houseId: ${houseId}`);\n      return;\n    }\n    // 防止選擇已排除的戶別\n    if (this.isHouseIdExcluded(houseId)) {\n      console.log(`戶別 ID ${houseId} 已被其他元件選擇，無法重複選擇`);\n      return;\n    }\n    const isSelected = this.isHouseIdSelected(houseId);\n    let newHouseIdSelection;\n    let newHouseNameSelection;\n    if (isSelected) {\n      newHouseIdSelection = this.selectedHouseIds.filter(id => id !== houseId);\n      // 同時更新 houseName 選擇\n      const household = this.getHouseholdByHouseId(houseId);\n      if (household) {\n        newHouseNameSelection = this.selectedHouseNames.filter(name => name !== household.houseName);\n      } else {\n        newHouseNameSelection = [...this.selectedHouseNames];\n      }\n    } else {\n      if (this.maxSelections && this.getCurrentSelectionCount() >= this.maxSelections) {\n        console.log('已達到最大選擇數量');\n        return; // 達到最大選擇數量\n      }\n      newHouseIdSelection = [...this.selectedHouseIds, houseId];\n      // 同時更新 houseName 選擇\n      const household = this.getHouseholdByHouseId(houseId);\n      if (household) {\n        newHouseNameSelection = [...this.selectedHouseNames, household.houseName];\n      } else {\n        newHouseNameSelection = [...this.selectedHouseNames];\n      }\n    }\n    this.selectedHouseIds = newHouseIdSelection;\n    this.selectedHouseNames = newHouseNameSelection;\n    this.emitChanges();\n  }\n  onRemoveHousehold(houseId) {\n    this.selectedHouseIds = this.selectedHouseIds.filter(id => id !== houseId);\n    // 同時更新 houseName 選擇\n    const household = this.getHouseholdByHouseId(houseId);\n    if (household) {\n      this.selectedHouseNames = this.selectedHouseNames.filter(name => name !== household.houseName);\n    }\n    this.emitChanges();\n  }\n  onSelectAllFiltered() {\n    console.log('onSelectAllFiltered called');\n    console.log('selectedBuilding:', this.selectedBuilding);\n    console.log('selectedFloor:', this.selectedFloor);\n    console.log('searchTerm:', this.searchTerm);\n    if (!this.selectedBuilding) {\n      console.log('No building selected');\n      return;\n    }\n    const households = this.buildingData[this.selectedBuilding] || [];\n    console.log('Available households for building:', households.length);\n    // 直接過濾戶別物件，而不是使用 filteredHouseholds 字串陣列\n    const filteredHouseholdItems = households.filter(h => {\n      // 樓層篩選：如果有選擇樓層，只顯示該樓層的戶別\n      const floorMatch = !this.selectedFloor || h.floor === this.selectedFloor;\n      // 搜尋篩選：戶別代碼包含搜尋詞\n      const searchMatch = !this.searchTerm || h.houseName.toLowerCase().includes(this.searchTerm.toLowerCase());\n      return floorMatch && searchMatch;\n    });\n    console.log('Filtered household items:', filteredHouseholdItems.map(h => `${h.houseName}-${h.floor} (ID:${h.houseId})`));\n    if (filteredHouseholdItems.length === 0) {\n      console.log('No filtered households found');\n      return;\n    }\n    // 計算可以新增的戶別數量\n    const currentCount = this.selectedHouseIds.length;\n    const maxAllowed = this.maxSelections || Infinity;\n    const remainingSlots = maxAllowed - currentCount;\n    // 取得尚未選擇且未被排除的過濾戶別ID\n    const unselectedFilteredIds = [];\n    for (const household of filteredHouseholdItems) {\n      if (household.houseId && !this.isHouseIdSelected(household.houseId) && !this.isHouseIdExcluded(household.houseId)) {\n        unselectedFilteredIds.push(household.houseId);\n      }\n    }\n    console.log('Unselected filtered IDs:', unselectedFilteredIds);\n    // 根據剩餘空間決定要新增的戶別\n    const toAdd = unselectedFilteredIds.slice(0, remainingSlots);\n    if (toAdd.length > 0) {\n      this.selectedHouseIds = [...this.selectedHouseIds, ...toAdd];\n      this.emitChanges();\n      console.log(`全選當前: 新增了 ${toAdd.length} 個戶別，IDs:`, toAdd);\n    } else {\n      console.log('No households to add');\n    }\n  }\n  onSelectAllBuilding() {\n    if (!this.selectedBuilding) return;\n    // 取得當前棟別的所有戶別\n    const buildingHouseholds = this.buildingData[this.selectedBuilding] || [];\n    // 計算可以新增的戶別數量\n    const currentCount = this.selectedHouseIds.length;\n    const maxAllowed = this.maxSelections || Infinity;\n    const remainingSlots = maxAllowed - currentCount;\n    // 取得尚未選擇且未被排除的棟別戶別 ID\n    const unselectedBuildingIds = [];\n    for (const household of buildingHouseholds) {\n      if (household.houseId && !this.selectedHouseIds.includes(household.houseId) && !this.isHouseholdExcluded(household.houseId)) {\n        unselectedBuildingIds.push(household.houseId);\n      }\n    }\n    // 根據剩餘空間決定要新增的戶別\n    const toAdd = unselectedBuildingIds.slice(0, remainingSlots);\n    if (toAdd.length > 0) {\n      this.selectedHouseIds = [...this.selectedHouseIds, ...toAdd];\n      this.emitChanges();\n      console.log(`全選棟別: 新增了 ${toAdd.length} 個戶別`);\n    }\n  }\n  onUnselectAllBuilding() {\n    if (!this.selectedBuilding) return;\n    const buildingHouseholds = this.buildingData[this.selectedBuilding] || [];\n    const buildingHouseIds = buildingHouseholds.map(h => h.houseId).filter(id => id !== undefined);\n    this.selectedHouseIds = this.selectedHouseIds.filter(id => !buildingHouseIds.includes(id));\n    this.emitChanges();\n  }\n  onClearAll() {\n    this.selectedHouseIds = [];\n    this.selectedHouseNames = [];\n    this.emitChanges();\n  }\n  emitChanges() {\n    this.updateSelectedByBuilding();\n    if (this.useHouseNameMode) {\n      console.log('Emitting changes (houseName mode) - selectedHouseNames:', this.selectedHouseNames);\n      this.onChange([...this.selectedHouseNames]);\n    } else {\n      console.log('Emitting changes (houseId mode) - selectedHouseIds:', this.selectedHouseIds);\n      this.onChange([...this.selectedHouseIds]);\n    }\n    this.onTouched();\n    const selectedItems = this.selectedHouseIds.map(houseId => {\n      for (const building of this.buildings) {\n        const item = this.buildingData[building]?.find(h => h.houseId === houseId);\n        if (item) return item;\n      }\n      return null;\n    }).filter(item => item !== null);\n    console.log('Selected items to emit:', selectedItems);\n    this.selectionChange.emit(selectedItems);\n    // 回傳 houseId 陣列\n    const houseIds = selectedItems.map(item => item.houseId).filter(id => id !== undefined);\n    console.log('House IDs to emit:', houseIds);\n    this.houseIdChange.emit(houseIds);\n    // 回傳 houseName 陣列\n    const houseNames = selectedItems.map(item => item.houseName);\n    console.log('House Names to emit:', houseNames);\n    this.houseNameChange.emit(houseNames);\n  }\n  toggleDropdown() {\n    if (!this.disabled) {\n      this.openDialog();\n      console.log('Opening household selection dialog');\n      console.log('Buildings available:', this.buildings);\n    }\n  }\n  openDialog() {\n    this.dialogService.open(this.householdDialog, {\n      context: {},\n      closeOnBackdropClick: false,\n      closeOnEsc: true,\n      autoFocus: false\n    });\n  }\n  closeDropdown() {\n    // 這個方法現在用於關閉對話框\n    // 對話框的關閉將由 NbDialogRef 處理\n  }\n  isHouseholdSelected(houseId) {\n    if (!houseId) return false;\n    return this.selectedHouseIds.includes(houseId);\n  }\n  isHouseholdExcluded(houseId) {\n    if (!houseId) return false;\n    return this.excludedHouseIds.includes(houseId);\n  }\n  isHouseholdDisabled(houseId) {\n    if (!houseId) return true;\n    return this.isHouseholdExcluded(houseId) || !this.canSelectMore() && !this.isHouseholdSelected(houseId);\n  }\n  canSelectMore() {\n    return this.canSelectMoreItems();\n  }\n  isAllBuildingSelected() {\n    if (!this.selectedBuilding) return false;\n    const buildingHouseholds = this.buildingData[this.selectedBuilding].filter(h => !h.isDisabled && h.houseId !== undefined);\n    return buildingHouseholds.length > 0 && buildingHouseholds.every(household => household.houseId && this.selectedHouseIds.includes(household.houseId));\n  }\n  isSomeBuildingSelected() {\n    if (!this.selectedBuilding) return false;\n    const buildingHouseholds = this.buildingData[this.selectedBuilding] || [];\n    return buildingHouseholds.some(household => household.houseId && this.selectedHouseIds.includes(household.houseId));\n  }\n  getSelectedByBuilding() {\n    return this.selectedByBuilding;\n  }\n  getBuildingCount(building) {\n    return this.buildingData[building]?.length || 0;\n  }\n  getSelectedCount() {\n    return this.getCurrentSelectionCount();\n  }\n  // 輔助方法：安全地獲取建築物的已選戶別 ID\n  getBuildingSelectedHouseIds(building) {\n    return this.selectedByBuilding[building] || [];\n  }\n  // 輔助方法：檢查建築物是否有已選戶別\n  hasBuildingSelected(building) {\n    return !!(this.selectedByBuilding[building] && this.selectedByBuilding[building].length > 0);\n  }\n  // 新增：更新當前棧別的樓層計數\n  updateFloorsForBuilding() {\n    if (!this.selectedBuilding) {\n      this.floors = [];\n      return;\n    }\n    const households = this.buildingData[this.selectedBuilding] || [];\n    const floorSet = new Set();\n    households.forEach(household => {\n      if (household.floor) {\n        floorSet.add(household.floor);\n      }\n    });\n    // 對樓層進行自然排序\n    this.floors = Array.from(floorSet).sort((a, b) => {\n      const numA = parseInt(a.replace(/[^0-9]/g, ''));\n      const numB = parseInt(b.replace(/[^0-9]/g, ''));\n      return numA - numB;\n    });\n    console.log('Updated floors for building:', this.selectedBuilding, this.floors);\n  }\n  // 新增：樓層選擇處理\n  onFloorSelect(floor) {\n    console.log('Floor selected:', floor);\n    this.selectedFloor = this.selectedFloor === floor ? '' : floor; // 切換選擇\n    this.updateFilteredHouseholds();\n    this.cdr.detectChanges();\n  }\n  // 新增：取得當前棧別的樓層計數\n  getFloorCount(floor) {\n    if (!this.selectedBuilding) return 0;\n    const households = this.buildingData[this.selectedBuilding] || [];\n    return households.filter(h => h.floor === floor).length;\n  }\n  // 新增：取得戶別的樓層資訊\n  getHouseholdFloor(householdCode) {\n    if (!this.selectedBuilding) return '';\n    const households = this.buildingData[this.selectedBuilding] || [];\n    const household = households.find(h => h.houseName === householdCode);\n    return household?.floor || '';\n  }\n  // 新增：取得戶別的完整資訊（包含樓層）\n  getHouseholdInfo(householdCode) {\n    for (const building of this.buildings) {\n      const households = this.buildingData[building] || [];\n      const household = households.find(h => h.houseName === householdCode);\n      if (household) {\n        return {\n          houseName: household.houseName,\n          floor: household.floor || ''\n        };\n      }\n    }\n    return {\n      houseName: householdCode,\n      floor: ''\n    };\n  }\n  // 新增：根據 houseId 取得戶別的完整資訊\n  getHouseholdInfoById(houseId) {\n    for (const building of this.buildings) {\n      const households = this.buildingData[building] || [];\n      const household = households.find(h => h.houseId === houseId);\n      if (household) {\n        return {\n          houseName: household.houseName,\n          floor: household.floor || ''\n        };\n      }\n    }\n    return {\n      houseName: `ID:${houseId}`,\n      floor: ''\n    };\n  }\n  // 新增：檢查搜尋是否有結果\n  hasNoSearchResults() {\n    if (!this.searchTerm || !this.selectedBuilding || !this.buildingData[this.selectedBuilding]) {\n      return false;\n    }\n    const filtered = this.buildingData[this.selectedBuilding].filter(h => {\n      const floorMatch = !this.selectedFloor || h.floor === this.selectedFloor;\n      const searchMatch = h.houseName.toLowerCase().includes(this.searchTerm.toLowerCase());\n      return floorMatch && searchMatch;\n    });\n    return filtered.length === 0;\n  }\n  // 新增：取得過濾後的戶別數量\n  getFilteredHouseholdsCount() {\n    if (!this.selectedBuilding || !this.buildingData[this.selectedBuilding]) {\n      return 0;\n    }\n    const filtered = this.buildingData[this.selectedBuilding].filter(h => {\n      const floorMatch = !this.selectedFloor || h.floor === this.selectedFloor;\n      const searchMatch = !this.searchTerm || h.houseName.toLowerCase().includes(this.searchTerm.toLowerCase());\n      return floorMatch && searchMatch;\n    });\n    return filtered.length;\n  }\n  // 新增：產生戶別的唯一識別符\n  getHouseholdUniqueId(household) {\n    return household.houseId ? household.houseId.toString() : `${household.houseName}_${household.floor}`;\n  }\n  // 新增：輔助方法 - 根據 houseId 查找 HouseholdItem\n  getHouseholdByHouseId(houseId) {\n    for (const building of this.buildings) {\n      const households = this.buildingData[building] || [];\n      const household = households.find(h => h.houseId === houseId);\n      if (household) return household;\n    }\n    return null;\n  }\n  // 新增：輔助方法 - 根據 houseName 查找 houseId\n  getHouseIdByHouseName(houseName) {\n    const matchingHouseholds = [];\n    // 收集所有符合名稱的戶別\n    for (const building of this.buildings) {\n      const households = this.buildingData[building] || [];\n      const matches = households.filter(h => h.houseName === houseName);\n      matches.forEach(household => {\n        matchingHouseholds.push({\n          building,\n          household\n        });\n      });\n    }\n    console.log(`查找 houseName \"${houseName}\" 的結果:`, matchingHouseholds);\n    if (matchingHouseholds.length === 0) {\n      console.warn(`找不到 houseName \"${houseName}\" 對應的戶別`);\n      return null;\n    }\n    if (matchingHouseholds.length > 1) {\n      console.warn(`發現多個同名戶別 \"${houseName}\":`, matchingHouseholds.map(m => `${m.building}-${m.household.floor}`));\n      console.warn(`將返回第一個找到的: ${matchingHouseholds[0].building}-${matchingHouseholds[0].household.floor}`);\n    }\n    const firstMatch = matchingHouseholds[0];\n    return firstMatch.household.houseId || null;\n  }\n  // 新增：輔助方法 - 檢查 houseId 是否被選中\n  isHouseIdSelected(houseId) {\n    return this.selectedHouseIds.includes(houseId);\n  }\n  // 新增：輔助方法 - 檢查 houseId 是否被排除\n  isHouseIdExcluded(houseId) {\n    return this.excludedHouseIds.includes(houseId);\n  }\n  // 新增：從唯一識別符獲取戶別物件\n  getHouseholdFromUniqueId(uniqueId) {\n    for (const building of this.buildings) {\n      const households = this.buildingData[building] || [];\n      const household = households.find(h => this.getHouseholdUniqueId(h) === uniqueId);\n      if (household) return household;\n    }\n    return null;\n  }\n  // 新增：houseName 模式相關的轉換方法\n  convertHouseNamesToIds(houseNames) {\n    const houseIds = [];\n    const duplicateWarnings = [];\n    houseNames.forEach(houseName => {\n      const matchingHouseholds = [];\n      // 收集所有符合名稱的戶別\n      for (const building of this.buildings) {\n        const households = this.buildingData[building] || [];\n        const matches = households.filter(h => h.houseName === houseName && h.houseId);\n        matches.forEach(household => {\n          matchingHouseholds.push({\n            building,\n            household\n          });\n        });\n      }\n      if (matchingHouseholds.length === 0) {\n        console.warn(`找不到 houseName \"${houseName}\" 對應的戶別`);\n      } else if (matchingHouseholds.length > 1) {\n        const buildings = matchingHouseholds.map(m => `${m.building}-${m.household.floor}`);\n        duplicateWarnings.push({\n          name: houseName,\n          count: matchingHouseholds.length,\n          buildings\n        });\n        // 取第一個匹配的\n        const firstMatch = matchingHouseholds[0];\n        if (firstMatch.household.houseId) {\n          houseIds.push(firstMatch.household.houseId);\n        }\n      } else {\n        const household = matchingHouseholds[0].household;\n        if (household.houseId) {\n          houseIds.push(household.houseId);\n        }\n      }\n    });\n    // 統一顯示重複名稱警告\n    if (duplicateWarnings.length > 0) {\n      console.group('⚠️ houseName 模式重複名稱警告');\n      duplicateWarnings.forEach(warning => {\n        console.warn(`戶名 \"${warning.name}\" 在多個位置重複 (${warning.count}個):`, warning.buildings);\n        console.warn('已選擇第一個匹配項，建議使用 houseId 模式以避免混淆');\n      });\n      console.groupEnd();\n    }\n    return houseIds;\n  }\n  convertIdsToHouseNames(houseIds) {\n    const houseNames = [];\n    houseIds.forEach(houseId => {\n      const household = this.getHouseholdByHouseId(houseId);\n      if (household) {\n        houseNames.push(household.houseName);\n      } else {\n        console.warn(`找不到 houseId ${houseId} 對應的戶別`);\n      }\n    });\n    return houseNames;\n  }\n  // 新增：更新 houseName 模式的分組選擇\n  updateSelectedByBuildingNames() {\n    const grouped = {};\n    this.selectedHouseNames.forEach(houseName => {\n      for (const building of this.buildings) {\n        const households = this.buildingData[building] || [];\n        const item = households.find(h => h.houseName === houseName);\n        if (item) {\n          if (!grouped[building]) grouped[building] = [];\n          grouped[building].push(houseName);\n          break; // 找到第一個匹配就停止\n        }\n      }\n    });\n    this.selectedByBuildingNames = grouped;\n  }\n  // 新增：檢查 houseName 是否被選中\n  isHouseNameSelected(houseName) {\n    return this.selectedHouseNames.includes(houseName);\n  }\n  // 新增：獲取當前選擇的總數（支援兩種模式）\n  getCurrentSelectionCount() {\n    return this.useHouseNameMode ? this.selectedHouseNames.length : this.selectedHouseIds.length;\n  }\n  // 新增：檢查是否可以選擇更多（支援兩種模式）\n  canSelectMoreItems() {\n    const currentCount = this.getCurrentSelectionCount();\n    return !this.maxSelections || currentCount < this.maxSelections;\n  }\n};\n__decorate([ViewChild('householdDialog', {\n  static: false\n})], HouseholdBindingComponent.prototype, \"householdDialog\", void 0);\n__decorate([Input()], HouseholdBindingComponent.prototype, \"placeholder\", void 0);\n__decorate([Input()], HouseholdBindingComponent.prototype, \"maxSelections\", void 0);\n__decorate([Input()], HouseholdBindingComponent.prototype, \"disabled\", void 0);\n__decorate([Input()], HouseholdBindingComponent.prototype, \"buildCaseId\", void 0);\n__decorate([Input()], HouseholdBindingComponent.prototype, \"buildingData\", void 0);\n__decorate([Input()], HouseholdBindingComponent.prototype, \"showSelectedArea\", void 0);\n__decorate([Input()], HouseholdBindingComponent.prototype, \"allowSearch\", void 0);\n__decorate([Input()], HouseholdBindingComponent.prototype, \"allowBatchSelect\", void 0);\n__decorate([Input()], HouseholdBindingComponent.prototype, \"excludedHouseIds\", void 0);\n__decorate([Input()], HouseholdBindingComponent.prototype, \"useHouseNameMode\", void 0);\n__decorate([Output()], HouseholdBindingComponent.prototype, \"selectionChange\", void 0);\n__decorate([Output()], HouseholdBindingComponent.prototype, \"houseIdChange\", void 0);\n__decorate([Output()], HouseholdBindingComponent.prototype, \"houseNameChange\", void 0);\nHouseholdBindingComponent = __decorate([Component({\n  selector: 'app-household-binding',\n  templateUrl: './household-binding.component.html',\n  styleUrls: ['./household-binding.component.scss'],\n  providers: [{\n    provide: NG_VALUE_ACCESSOR,\n    useExisting: forwardRef(() => HouseholdBindingComponent),\n    multi: true\n  }]\n})], HouseholdBindingComponent);\nexport { HouseholdBindingComponent };", "map": {"version": 3, "names": ["Component", "Input", "Output", "EventEmitter", "forwardRef", "ViewChild", "NG_VALUE_ACCESSOR", "HouseholdBindingComponent", "constructor", "cdr", "houseCustomService", "dialogService", "placeholder", "maxSelections", "disabled", "buildCaseId", "buildingData", "showSelectedArea", "allowSearch", "allowBatchSelect", "excludedHouseIds", "useHouseNameMode", "selectionChange", "houseIdChange", "houseNameChange", "isOpen", "selectedBuilding", "searchTerm", "selectedF<PERSON>or", "selectedHouseIds", "selectedHouseNames", "buildings", "floors", "filteredHouseholds", "selectedByBuilding", "selectedByBuildingNames", "isLoading", "onChange", "value", "onTouched", "writeValue", "console", "log", "length", "firstItem", "convertHouseNamesToIds", "warn", "convertIdsToHouseNames", "error", "updateSelectedByBuilding", "registerOnChange", "fn", "registerOnTouched", "setDisabledState", "isDisabled", "ngOnInit", "initializeData", "ngOnChanges", "changes", "Object", "keys", "updateFilteredHouseholds", "loadBuildingDataFromApi", "getDropDown", "subscribe", "next", "response", "convertApiResponseToBuildingData", "Entries", "detectChanges", "entries", "for<PERSON>ach", "building", "houses", "map", "house", "houseName", "HouseName", "code", "Building", "floor", "Floor", "houseId", "HouseId", "isSelected", "grouped", "item", "find", "h", "push", "updateSelectedByBuildingNames", "onBuildingSelect", "updateFloorsForBuilding", "onBuildingClick", "households", "filteredItems", "filter", "floorMatch", "searchMatch", "toLowerCase", "includes", "onSearchChange", "event", "target", "resetSearch", "onHouseholdToggle", "isHouseIdExcluded", "isHouseIdSelected", "newHouseIdSelection", "newHouseNameSelection", "id", "household", "getHouseholdByHouseId", "name", "getCurrentSelectionCount", "emitChanges", "onRemoveHousehold", "onSelectAllFiltered", "filteredHouseholdItems", "currentCount", "maxAllowed", "Infinity", "remainingSlots", "unselectedFilteredIds", "toAdd", "slice", "onSelectAllBuilding", "buildingHouseholds", "unselectedBuildingIds", "isHouseholdExcluded", "onUnselectAllBuilding", "buildingHouseIds", "undefined", "onClearAll", "selectedItems", "emit", "houseIds", "houseNames", "toggleDropdown", "openDialog", "open", "householdDialog", "context", "closeOnBackdropClick", "closeOnEsc", "autoFocus", "closeDropdown", "isHouseholdSelected", "isHouseholdDisabled", "canSelectMore", "canSelectMoreItems", "isAllBuildingSelected", "every", "isSomeBuildingSelected", "some", "getSelectedByBuilding", "getBuildingCount", "getSelectedCount", "getBuildingSelectedHouseIds", "hasBuildingSelected", "floorSet", "Set", "add", "Array", "from", "sort", "a", "b", "numA", "parseInt", "replace", "numB", "onFloorSelect", "getFloorCount", "getHouseholdFloor", "householdCode", "getHouseholdInfo", "getHouseholdInfoById", "hasNoSearchResults", "filtered", "getFilteredHouseholdsCount", "getHouseholdUniqueId", "toString", "getHouseIdByHouseName", "matchingHouseholds", "matches", "m", "firstMatch", "getHouseholdFromUniqueId", "uniqueId", "duplicateWarnings", "count", "group", "warning", "groupEnd", "isHouseNameSelected", "__decorate", "static", "selector", "templateUrl", "styleUrls", "providers", "provide", "useExisting", "multi"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\shared\\components\\household-binding\\household-binding.component.ts"], "sourcesContent": ["import { Component, Input, Output, EventEmitter, OnInit, OnChanges, SimpleChanges, forwardRef, ChangeDetectorRef, TemplateRef, ViewChild } from '@angular/core';\r\nimport { ControlValueAccessor, NG_VALUE_ACCESSOR } from '@angular/forms';\r\nimport { NbDialogService } from '@nebular/theme';\r\nimport { HouseCustomService } from '../../../../services/api/services/HouseCustom.service';\r\nimport { HouseItem } from '../../../../services/api/models/house.model';\r\n\r\nexport interface HouseholdItem {\r\n  houseName: string;\r\n  building: string;\r\n  floor?: string;\r\n  houseId?: number;\r\n  isSelected?: boolean;\r\n  isDisabled?: boolean;\r\n}\r\n\r\nexport interface BuildingData {\r\n  [key: string]: HouseholdItem[];\r\n}\r\n\r\n@Component({\r\n  selector: 'app-household-binding',\r\n  templateUrl: './household-binding.component.html',\r\n  styleUrls: ['./household-binding.component.scss'],\r\n  providers: [\r\n    {\r\n      provide: NG_VALUE_ACCESSOR,\r\n      useExisting: forwardRef(() => HouseholdBindingComponent),\r\n      multi: true,\r\n    },\r\n  ],\r\n})\r\nexport class HouseholdBindingComponent implements OnInit, OnChanges, ControlValueAccessor {\r\n  @ViewChild('householdDialog', { static: false }) householdDialog!: TemplateRef<any>;\r\n\r\n  @Input() placeholder: string = '請選擇戶別';\r\n  @Input() maxSelections: number | null = null;\r\n  @Input() disabled: boolean = false;\r\n  @Input() buildCaseId: number | null = null; // 新增：建案ID\r\n  @Input() buildingData: BuildingData = {}; @Input() showSelectedArea: boolean = true;\r\n  @Input() allowSearch: boolean = true;\r\n  @Input() allowBatchSelect: boolean = true;  @Input() excludedHouseIds: number[] = []; // 改為：排除的戶別ID（已被其他元件選擇）\r\n  @Input() useHouseNameMode: boolean = false; // 新增：是否使用 houseName 模式（預設為 houseId 模式）\r\n\r\n  @Output() selectionChange = new EventEmitter<HouseholdItem[]>();\r\n  @Output() houseIdChange = new EventEmitter<number[]>(); // 新增：回傳 houseId 陣列\r\n  @Output() houseNameChange = new EventEmitter<string[]>(); // 新增：回傳 houseName 陣列（houseName 模式專用）\r\n  isOpen = false;\r\n  selectedBuilding = '';\r\n  searchTerm = '';  selectedFloor = ''; // 新增：選中的樓層\r\n  selectedHouseIds: number[] = []; // 改為：使用 houseId 作為選擇的key\r\n  selectedHouseNames: string[] = []; // 新增：houseName 模式下使用的選擇列表\r\n  buildings: string[] = [];\r\n  floors: string[] = []; // 新增：當前棧別的樓層列表\r\n  filteredHouseholds: string[] = [];  // 保持為字串陣列用於UI顯示\r\n  selectedByBuilding: { [building: string]: number[] } = {}; // 改為：儲存 houseId\r\n  selectedByBuildingNames: { [building: string]: string[] } = {}; // 新增：houseName 模式下儲存 houseName\r\n  isLoading: boolean = false; // 新增：載入狀態  isLoading: boolean = false; // 新增：載入狀態\r\n  \r\n  // ControlValueAccessor implementation\r\n  private onChange = (value: number[] | string[]) => { };\r\n  private onTouched = () => { };\r\n  constructor(\r\n    private cdr: ChangeDetectorRef,\r\n    private houseCustomService: HouseCustomService,\r\n    private dialogService: NbDialogService\r\n  ) { }  writeValue(value: any[]): void {\r\n    console.log('writeValue called with:', value, 'type:', typeof value?.[0], 'useHouseNameMode:', this.useHouseNameMode);\r\n\r\n    if (!value || value.length === 0) {\r\n      this.selectedHouseIds = [];\r\n      this.selectedHouseNames = [];\r\n    } else {\r\n      const firstItem = value[0];\r\n      \r\n      if (this.useHouseNameMode) {\r\n        // houseName 模式\r\n        if (typeof firstItem === 'string') {\r\n          this.selectedHouseNames = value as string[];\r\n          // 同時更新 houseIds 以保持內部邏輯一致\r\n          this.selectedHouseIds = this.convertHouseNamesToIds(this.selectedHouseNames);\r\n          console.log('houseName 模式：設定 selectedHouseNames:', this.selectedHouseNames);\r\n          console.log('對應的 houseIds:', this.selectedHouseIds);\r\n        } else if (typeof firstItem === 'number') {\r\n          // 如果在 houseName 模式下收到 houseId，進行轉換\r\n          console.warn('⚠️ houseName 模式下收到 houseId 陣列，進行轉換');\r\n          this.selectedHouseIds = value as number[];\r\n          this.selectedHouseNames = this.convertIdsToHouseNames(this.selectedHouseIds);\r\n          console.log('轉換後的 selectedHouseNames:', this.selectedHouseNames);\r\n        } else {\r\n          console.error('houseName 模式下收到未知格式的資料:', value);\r\n          this.selectedHouseIds = [];\r\n          this.selectedHouseNames = [];\r\n        }\r\n      } else {\r\n        // houseId 模式（原有邏輯）\r\n        if (typeof firstItem === 'number') {\r\n          this.selectedHouseIds = value as number[];\r\n          this.selectedHouseNames = this.convertIdsToHouseNames(this.selectedHouseIds);\r\n          console.log('houseId 模式：設定 selectedHouseIds:', this.selectedHouseIds);\r\n        } else if (typeof firstItem === 'string') {\r\n          console.warn('⚠️ houseId 模式下收到 houseName 陣列，進行轉換');\r\n          this.selectedHouseNames = value as string[];\r\n          this.selectedHouseIds = this.convertHouseNamesToIds(this.selectedHouseNames);\r\n          console.log('轉換後的 selectedHouseIds:', this.selectedHouseIds);\r\n        } else {\r\n          console.error('houseId 模式下收到未知格式的資料:', value);\r\n          this.selectedHouseIds = [];\r\n          this.selectedHouseNames = [];\r\n        }\r\n      }\r\n    }\r\n\r\n    this.updateSelectedByBuilding();\r\n  }\r\n  registerOnChange(fn: (value: number[] | string[]) => void): void {\r\n    this.onChange = fn;\r\n  }\r\n\r\n  registerOnTouched(fn: () => void): void {\r\n    this.onTouched = fn;\r\n  }\r\n  setDisabledState(isDisabled: boolean): void {\r\n    this.disabled = isDisabled;\r\n  } ngOnInit() {\r\n    this.initializeData();\r\n  }\r\n  ngOnChanges(changes: SimpleChanges) {\r\n    if (changes['buildCaseId'] && this.buildCaseId) {\r\n      // 當建案ID變更時，重新載入資料\r\n      this.initializeData();\r\n    }\r\n    if (changes['buildingData']) {\r\n      // 當 buildingData 變更時，重新初始化\r\n      this.buildings = Object.keys(this.buildingData || {});\r\n      console.log('buildingData updated:', this.buildingData);\r\n      this.updateFilteredHouseholds();\r\n      this.updateSelectedByBuilding();\r\n    } if (changes['excludedHouseIds']) {\r\n      // 當排除列表變化時，重新更新顯示\r\n      this.updateFilteredHouseholds();\r\n      console.log('Excluded house IDs updated:', this.excludedHouseIds);\r\n    }\r\n  } private initializeData() {\r\n    // 優先檢查是否有傳入 buildingData\r\n    if (this.buildingData && Object.keys(this.buildingData).length > 0) {\r\n      // 使用傳入的 buildingData\r\n      this.buildings = Object.keys(this.buildingData);\r\n      console.log('Component initialized with provided buildingData:', this.buildings);\r\n      this.updateSelectedByBuilding();\r\n    } else if (this.buildCaseId) {\r\n      // 只有在沒有傳入 buildingData 且有建案ID時才呼叫API\r\n      this.loadBuildingDataFromApi();\r\n    } else {\r\n      // 既沒有 buildingData 也沒有 buildCaseId，保持空狀態\r\n      this.buildings = [];\r\n      console.log('No buildingData or buildCaseId provided');\r\n    }\r\n  }\r\n\r\n  private loadBuildingDataFromApi() {\r\n    if (!this.buildCaseId) return;\r\n\r\n    this.isLoading = true;\r\n    this.houseCustomService.getDropDown(this.buildCaseId).subscribe({\r\n      next: (response) => {\r\n        console.log('API response:', response);\r\n        this.buildingData = this.convertApiResponseToBuildingData(response.Entries);\r\n        this.buildings = Object.keys(this.buildingData);\r\n        this.updateSelectedByBuilding();\r\n        this.isLoading = false;\r\n        this.cdr.detectChanges();\r\n      }, error: (error) => {\r\n        console.error('Error loading building data:', error);\r\n        // API載入失敗時，不使用備援資料，保持空狀態\r\n        this.buildingData = {};\r\n        this.buildings = [];\r\n        this.isLoading = false;\r\n        this.cdr.detectChanges();\r\n      }\r\n    });\r\n  }\r\n  private convertApiResponseToBuildingData(entries: { [key: string]: any[] }): BuildingData {\r\n    const buildingData: BuildingData = {};\r\n\r\n    Object.entries(entries).forEach(([building, houses]) => {\r\n      buildingData[building] = houses.map(house => ({\r\n        houseName: house.houseName || house.HouseName || house.code,\r\n        building: house.building || house.Building || building,\r\n        floor: house.floor || house.Floor,\r\n        houseId: house.houseId || house.HouseId,\r\n        isSelected: false,\r\n        isDisabled: false\r\n      }));\r\n    });\r\n\r\n    return buildingData;\r\n  }  private updateSelectedByBuilding() {\r\n    // 更新 houseId 分組\r\n    const grouped: { [building: string]: number[] } = {};\r\n\r\n    this.selectedHouseIds.forEach(houseId => {\r\n      for (const building of this.buildings) {\r\n        const item = this.buildingData[building]?.find(h => h.houseId === houseId);\r\n        if (item) {\r\n          if (!grouped[building]) grouped[building] = [];\r\n          grouped[building].push(houseId);\r\n          break;\r\n        }\r\n      }\r\n    });\r\n\r\n    this.selectedByBuilding = grouped;\r\n\r\n    // 如果是 houseName 模式，也更新 houseName 分組\r\n    if (this.useHouseNameMode) {\r\n      this.updateSelectedByBuildingNames();\r\n    }\r\n  }onBuildingSelect(building: string) {\r\n    console.log('Building selected:', building);\r\n    this.selectedBuilding = building;\r\n    this.selectedFloor = ''; // 重置樓層選擇\r\n    this.searchTerm = '';\r\n    this.updateFloorsForBuilding(); // 更新樓層列表\r\n    this.updateFilteredHouseholds();\r\n    console.log('Filtered households count:', this.filteredHouseholds.length);\r\n    // 手動觸發變更偵測\r\n    this.cdr.detectChanges();\r\n  }\r\n\r\n  onBuildingClick(building: string) {\r\n    console.log('Building clicked (mousedown):', building);\r\n  } updateFilteredHouseholds() {\r\n    console.log('Updating filtered households for building:', this.selectedBuilding, 'floor:', this.selectedFloor);\r\n    if (!this.selectedBuilding) {\r\n      this.filteredHouseholds = [];\r\n      return;\r\n    }\r\n\r\n    const households = this.buildingData[this.selectedBuilding] || [];\r\n    console.log('Available households for building:', households.length);\r\n\r\n    // 提取戶別代碼並進行搜尋和樓層過濾\r\n    const filteredItems = households.filter(h => {\r\n      // 樓層篩選：如果有選擇樓層，只顯示該樓層的戶別\r\n      const floorMatch = !this.selectedFloor || h.floor === this.selectedFloor;\r\n      // 搜尋篩選：戶別代碼包含搜尋詞\r\n      const searchMatch = !this.searchTerm || h.houseName.toLowerCase().includes(this.searchTerm.toLowerCase());\r\n      return floorMatch && searchMatch;\r\n    });\r\n\r\n    // 只提取戶別名稱用於UI顯示（但onSelectAllFiltered會直接使用物件）\r\n    this.filteredHouseholds = filteredItems.map(h => h.houseName);\r\n\r\n    console.log('Filtered households result:', this.filteredHouseholds.length);\r\n    console.log('Filtered items details:', filteredItems.map(h => `${h.houseName}-${h.floor} (ID:${h.houseId})`));\r\n  }\r\n\r\n  onSearchChange(event: any) {\r\n    this.searchTerm = event.target.value;\r\n    this.updateFilteredHouseholds();\r\n    console.log('Search term changed:', this.searchTerm);\r\n  }\r\n  resetSearch() {\r\n    this.searchTerm = '';\r\n    this.updateFilteredHouseholds();\r\n    console.log('Search reset');\r\n  }  onHouseholdToggle(houseId: number | undefined) {\r\n    console.log('onHouseholdToggle called with houseId:', houseId);\r\n    console.log('Current selectedHouseIds:', this.selectedHouseIds);\r\n\r\n    if (!houseId) {\r\n      console.log(`無效的 houseId: ${houseId}`);\r\n      return;\r\n    }\r\n\r\n    // 防止選擇已排除的戶別\r\n    if (this.isHouseIdExcluded(houseId)) {\r\n      console.log(`戶別 ID ${houseId} 已被其他元件選擇，無法重複選擇`);\r\n      return;\r\n    }\r\n\r\n    const isSelected = this.isHouseIdSelected(houseId);\r\n    let newHouseIdSelection: number[];\r\n    let newHouseNameSelection: string[];\r\n\r\n    if (isSelected) {\r\n      newHouseIdSelection = this.selectedHouseIds.filter(id => id !== houseId);\r\n      // 同時更新 houseName 選擇\r\n      const household = this.getHouseholdByHouseId(houseId);\r\n      if (household) {\r\n        newHouseNameSelection = this.selectedHouseNames.filter(name => name !== household.houseName);\r\n      } else {\r\n        newHouseNameSelection = [...this.selectedHouseNames];\r\n      }\r\n    } else {\r\n      if (this.maxSelections && this.getCurrentSelectionCount() >= this.maxSelections) {\r\n        console.log('已達到最大選擇數量');\r\n        return; // 達到最大選擇數量\r\n      }\r\n      newHouseIdSelection = [...this.selectedHouseIds, houseId];\r\n      // 同時更新 houseName 選擇\r\n      const household = this.getHouseholdByHouseId(houseId);\r\n      if (household) {\r\n        newHouseNameSelection = [...this.selectedHouseNames, household.houseName];\r\n      } else {\r\n        newHouseNameSelection = [...this.selectedHouseNames];\r\n      }\r\n    }\r\n\r\n    this.selectedHouseIds = newHouseIdSelection;\r\n    this.selectedHouseNames = newHouseNameSelection;\r\n    this.emitChanges();\r\n  }  onRemoveHousehold(houseId: number) {\r\n    this.selectedHouseIds = this.selectedHouseIds.filter(id => id !== houseId);\r\n    \r\n    // 同時更新 houseName 選擇\r\n    const household = this.getHouseholdByHouseId(houseId);\r\n    if (household) {\r\n      this.selectedHouseNames = this.selectedHouseNames.filter(name => name !== household.houseName);\r\n    }\r\n    \r\n    this.emitChanges();\r\n  }onSelectAllFiltered() {\r\n    console.log('onSelectAllFiltered called');\r\n    console.log('selectedBuilding:', this.selectedBuilding);\r\n    console.log('selectedFloor:', this.selectedFloor);\r\n    console.log('searchTerm:', this.searchTerm);\r\n\r\n    if (!this.selectedBuilding) {\r\n      console.log('No building selected');\r\n      return;\r\n    }\r\n\r\n    const households = this.buildingData[this.selectedBuilding] || [];\r\n    console.log('Available households for building:', households.length);\r\n\r\n    // 直接過濾戶別物件，而不是使用 filteredHouseholds 字串陣列\r\n    const filteredHouseholdItems = households.filter(h => {\r\n      // 樓層篩選：如果有選擇樓層，只顯示該樓層的戶別\r\n      const floorMatch = !this.selectedFloor || h.floor === this.selectedFloor;\r\n      // 搜尋篩選：戶別代碼包含搜尋詞\r\n      const searchMatch = !this.searchTerm || h.houseName.toLowerCase().includes(this.searchTerm.toLowerCase());\r\n      return floorMatch && searchMatch;\r\n    });\r\n\r\n    console.log('Filtered household items:', filteredHouseholdItems.map(h => `${h.houseName}-${h.floor} (ID:${h.houseId})`));\r\n\r\n    if (filteredHouseholdItems.length === 0) {\r\n      console.log('No filtered households found');\r\n      return;\r\n    }\r\n\r\n    // 計算可以新增的戶別數量\r\n    const currentCount = this.selectedHouseIds.length;\r\n    const maxAllowed = this.maxSelections || Infinity;\r\n    const remainingSlots = maxAllowed - currentCount;\r\n\r\n    // 取得尚未選擇且未被排除的過濾戶別ID\r\n    const unselectedFilteredIds: number[] = [];\r\n    for (const household of filteredHouseholdItems) {\r\n      if (household.houseId &&\r\n        !this.isHouseIdSelected(household.houseId) &&\r\n        !this.isHouseIdExcluded(household.houseId)) {\r\n        unselectedFilteredIds.push(household.houseId);\r\n      }\r\n    }\r\n\r\n    console.log('Unselected filtered IDs:', unselectedFilteredIds);\r\n\r\n    // 根據剩餘空間決定要新增的戶別\r\n    const toAdd = unselectedFilteredIds.slice(0, remainingSlots);\r\n\r\n    if (toAdd.length > 0) {\r\n      this.selectedHouseIds = [...this.selectedHouseIds, ...toAdd];\r\n      this.emitChanges();\r\n      console.log(`全選當前: 新增了 ${toAdd.length} 個戶別，IDs:`, toAdd);\r\n    } else {\r\n      console.log('No households to add');\r\n    }\r\n  } onSelectAllBuilding() {\r\n    if (!this.selectedBuilding) return;\r\n\r\n    // 取得當前棟別的所有戶別\r\n    const buildingHouseholds = this.buildingData[this.selectedBuilding] || [];\r\n\r\n    // 計算可以新增的戶別數量\r\n    const currentCount = this.selectedHouseIds.length;\r\n    const maxAllowed = this.maxSelections || Infinity;\r\n    const remainingSlots = maxAllowed - currentCount;\r\n\r\n    // 取得尚未選擇且未被排除的棟別戶別 ID\r\n    const unselectedBuildingIds: number[] = [];\r\n    for (const household of buildingHouseholds) {\r\n      if (household.houseId &&\r\n        !this.selectedHouseIds.includes(household.houseId) &&\r\n        !this.isHouseholdExcluded(household.houseId)) {\r\n        unselectedBuildingIds.push(household.houseId);\r\n      }\r\n    }\r\n\r\n    // 根據剩餘空間決定要新增的戶別\r\n    const toAdd = unselectedBuildingIds.slice(0, remainingSlots); if (toAdd.length > 0) {\r\n      this.selectedHouseIds = [...this.selectedHouseIds, ...toAdd];\r\n      this.emitChanges();\r\n      console.log(`全選棟別: 新增了 ${toAdd.length} 個戶別`);\r\n    }\r\n  }\r\n\r\n  onUnselectAllBuilding() {\r\n    if (!this.selectedBuilding) return;\r\n\r\n    const buildingHouseholds = this.buildingData[this.selectedBuilding] || [];\r\n    const buildingHouseIds = buildingHouseholds.map(h => h.houseId).filter(id => id !== undefined) as number[];\r\n    this.selectedHouseIds = this.selectedHouseIds.filter(id => !buildingHouseIds.includes(id));\r\n    this.emitChanges();\r\n  }\r\n  onClearAll() {\r\n    this.selectedHouseIds = [];\r\n    this.selectedHouseNames = [];\r\n    this.emitChanges();\r\n  }private emitChanges() {\r\n    this.updateSelectedByBuilding();\r\n    \r\n    if (this.useHouseNameMode) {\r\n      console.log('Emitting changes (houseName mode) - selectedHouseNames:', this.selectedHouseNames);\r\n      this.onChange([...this.selectedHouseNames]);\r\n    } else {\r\n      console.log('Emitting changes (houseId mode) - selectedHouseIds:', this.selectedHouseIds);\r\n      this.onChange([...this.selectedHouseIds]);\r\n    }\r\n    \r\n    this.onTouched();\r\n\r\n    const selectedItems = this.selectedHouseIds.map(houseId => {\r\n      for (const building of this.buildings) {\r\n        const item = this.buildingData[building]?.find(h => h.houseId === houseId);\r\n        if (item) return item;\r\n      }\r\n      return null;\r\n    }).filter(item => item !== null) as HouseholdItem[];\r\n\r\n    console.log('Selected items to emit:', selectedItems);\r\n    this.selectionChange.emit(selectedItems);\r\n\r\n    // 回傳 houseId 陣列\r\n    const houseIds = selectedItems.map(item => item.houseId!).filter(id => id !== undefined);\r\n    console.log('House IDs to emit:', houseIds);\r\n    this.houseIdChange.emit(houseIds);\r\n\r\n    // 回傳 houseName 陣列\r\n    const houseNames = selectedItems.map(item => item.houseName);\r\n    console.log('House Names to emit:', houseNames);\r\n    this.houseNameChange.emit(houseNames);\r\n  }toggleDropdown() {\r\n    if (!this.disabled) {\r\n      this.openDialog();\r\n      console.log('Opening household selection dialog');\r\n      console.log('Buildings available:', this.buildings);\r\n    }\r\n  }\r\n\r\n  openDialog() {\r\n    this.dialogService.open(this.householdDialog, {\r\n      context: {},\r\n      closeOnBackdropClick: false,\r\n      closeOnEsc: true,\r\n      autoFocus: false,\r\n    });\r\n  }\r\n\r\n  closeDropdown() {\r\n    // 這個方法現在用於關閉對話框\r\n    // 對話框的關閉將由 NbDialogRef 處理\r\n  }\r\n  isHouseholdSelected(houseId: number | undefined): boolean {\r\n    if (!houseId) return false;\r\n    return this.selectedHouseIds.includes(houseId);\r\n  }\r\n\r\n  isHouseholdExcluded(houseId: number | undefined): boolean {\r\n    if (!houseId) return false;\r\n    return this.excludedHouseIds.includes(houseId);\r\n  }\r\n\r\n  isHouseholdDisabled(houseId: number | undefined): boolean {\r\n    if (!houseId) return true;\r\n    return this.isHouseholdExcluded(houseId) ||\r\n      (!this.canSelectMore() && !this.isHouseholdSelected(houseId));\r\n  }  canSelectMore(): boolean {\r\n    return this.canSelectMoreItems();\r\n  }isAllBuildingSelected(): boolean {\r\n    if (!this.selectedBuilding) return false;\r\n    const buildingHouseholds = this.buildingData[this.selectedBuilding]\r\n      .filter(h => !h.isDisabled && h.houseId !== undefined);\r\n    return buildingHouseholds.length > 0 &&\r\n      buildingHouseholds.every(household => household.houseId && this.selectedHouseIds.includes(household.houseId));\r\n  }\r\n\r\n  isSomeBuildingSelected(): boolean {\r\n    if (!this.selectedBuilding) return false;\r\n    const buildingHouseholds = this.buildingData[this.selectedBuilding] || [];\r\n    return buildingHouseholds.some(household => household.houseId && this.selectedHouseIds.includes(household.houseId));\r\n  } getSelectedByBuilding(): { [building: string]: number[] } {\r\n    return this.selectedByBuilding;\r\n  }\r\n\r\n  getBuildingCount(building: string): number {\r\n    return this.buildingData[building]?.length || 0;\r\n  }\r\n  getSelectedCount(): number {\r\n    return this.getCurrentSelectionCount();\r\n  }\r\n\r\n  // 輔助方法：安全地獲取建築物的已選戶別 ID\r\n  getBuildingSelectedHouseIds(building: string): number[] {\r\n    return this.selectedByBuilding[building] || [];\r\n  }\r\n\r\n  // 輔助方法：檢查建築物是否有已選戶別\r\n  hasBuildingSelected(building: string): boolean {\r\n    return !!(this.selectedByBuilding[building] && this.selectedByBuilding[building].length > 0);\r\n  }\r\n\r\n  // 新增：更新當前棧別的樓層計數\r\n  private updateFloorsForBuilding() {\r\n    if (!this.selectedBuilding) {\r\n      this.floors = [];\r\n      return;\r\n    }\r\n\r\n    const households = this.buildingData[this.selectedBuilding] || [];\r\n    const floorSet = new Set<string>();\r\n\r\n    households.forEach(household => {\r\n      if (household.floor) {\r\n        floorSet.add(household.floor);\r\n      }\r\n    });\r\n\r\n    // 對樓層進行自然排序\r\n    this.floors = Array.from(floorSet).sort((a, b) => {\r\n      const numA = parseInt(a.replace(/[^0-9]/g, ''));\r\n      const numB = parseInt(b.replace(/[^0-9]/g, ''));\r\n      return numA - numB;\r\n    });\r\n\r\n    console.log('Updated floors for building:', this.selectedBuilding, this.floors);\r\n  }\r\n\r\n  // 新增：樓層選擇處理\r\n  onFloorSelect(floor: string) {\r\n    console.log('Floor selected:', floor);\r\n    this.selectedFloor = this.selectedFloor === floor ? '' : floor; // 切換選擇\r\n    this.updateFilteredHouseholds();\r\n    this.cdr.detectChanges();\r\n  }\r\n\r\n  // 新增：取得當前棧別的樓層計數\r\n  getFloorCount(floor: string): number {\r\n    if (!this.selectedBuilding) return 0;\r\n    const households = this.buildingData[this.selectedBuilding] || [];\r\n    return households.filter(h => h.floor === floor).length;\r\n  }\r\n  // 新增：取得戶別的樓層資訊\r\n  getHouseholdFloor(householdCode: string): string {\r\n    if (!this.selectedBuilding) return '';\r\n    const households = this.buildingData[this.selectedBuilding] || [];\r\n    const household = households.find(h => h.houseName === householdCode);\r\n    return household?.floor || '';\r\n  }\r\n  // 新增：取得戶別的完整資訊（包含樓層）\r\n  getHouseholdInfo(householdCode: string): { houseName: string, floor: string } {\r\n    for (const building of this.buildings) {\r\n      const households = this.buildingData[building] || [];\r\n      const household = households.find(h => h.houseName === householdCode);\r\n      if (household) {\r\n        return {\r\n          houseName: household.houseName,\r\n          floor: household.floor || ''\r\n        };\r\n      }\r\n    }\r\n    return { houseName: householdCode, floor: '' };\r\n  }\r\n\r\n  // 新增：根據 houseId 取得戶別的完整資訊\r\n  getHouseholdInfoById(houseId: number): { houseName: string, floor: string } {\r\n    for (const building of this.buildings) {\r\n      const households = this.buildingData[building] || [];\r\n      const household = households.find(h => h.houseId === houseId);\r\n      if (household) {\r\n        return {\r\n          houseName: household.houseName,\r\n          floor: household.floor || ''\r\n        };\r\n      }\r\n    }\r\n    return { houseName: `ID:${houseId}`, floor: '' };\r\n  }\r\n\r\n  // 新增：檢查搜尋是否有結果\r\n  hasNoSearchResults(): boolean {\r\n    if (!this.searchTerm || !this.selectedBuilding || !this.buildingData[this.selectedBuilding]) {\r\n      return false;\r\n    }\r\n\r\n    const filtered = this.buildingData[this.selectedBuilding].filter(h => {\r\n      const floorMatch = !this.selectedFloor || h.floor === this.selectedFloor;\r\n      const searchMatch = h.houseName.toLowerCase().includes(this.searchTerm.toLowerCase());\r\n      return floorMatch && searchMatch;\r\n    });\r\n\r\n    return filtered.length === 0;\r\n  }\r\n\r\n  // 新增：取得過濾後的戶別數量\r\n  getFilteredHouseholdsCount(): number {\r\n    if (!this.selectedBuilding || !this.buildingData[this.selectedBuilding]) {\r\n      return 0;\r\n    }\r\n\r\n    const filtered = this.buildingData[this.selectedBuilding].filter(h => {\r\n      const floorMatch = !this.selectedFloor || h.floor === this.selectedFloor;\r\n      const searchMatch = !this.searchTerm || h.houseName.toLowerCase().includes(this.searchTerm.toLowerCase());\r\n      return floorMatch && searchMatch;\r\n    });\r\n\r\n    return filtered.length;\r\n  }\r\n\r\n  // 新增：產生戶別的唯一識別符\r\n  getHouseholdUniqueId(household: HouseholdItem): string {\r\n    return household.houseId ? household.houseId.toString() : `${household.houseName}_${household.floor}`;\r\n  }\r\n  // 新增：輔助方法 - 根據 houseId 查找 HouseholdItem\r\n  private getHouseholdByHouseId(houseId: number): HouseholdItem | null {\r\n    for (const building of this.buildings) {\r\n      const households = this.buildingData[building] || [];\r\n      const household = households.find(h => h.houseId === houseId);\r\n      if (household) return household;\r\n    }\r\n    return null;\r\n  }\r\n  // 新增：輔助方法 - 根據 houseName 查找 houseId\r\n  private getHouseIdByHouseName(houseName: string): number | null {\r\n    const matchingHouseholds: { building: string, household: HouseholdItem }[] = [];\r\n\r\n    // 收集所有符合名稱的戶別\r\n    for (const building of this.buildings) {\r\n      const households = this.buildingData[building] || [];\r\n      const matches = households.filter(h => h.houseName === houseName);\r\n      matches.forEach(household => {\r\n        matchingHouseholds.push({ building, household });\r\n      });\r\n    }\r\n\r\n    console.log(`查找 houseName \"${houseName}\" 的結果:`, matchingHouseholds);\r\n\r\n    if (matchingHouseholds.length === 0) {\r\n      console.warn(`找不到 houseName \"${houseName}\" 對應的戶別`);\r\n      return null;\r\n    }\r\n\r\n    if (matchingHouseholds.length > 1) {\r\n      console.warn(`發現多個同名戶別 \"${houseName}\":`, matchingHouseholds.map(m => `${m.building}-${m.household.floor}`));\r\n      console.warn(`將返回第一個找到的: ${matchingHouseholds[0].building}-${matchingHouseholds[0].household.floor}`);\r\n    }\r\n\r\n    const firstMatch = matchingHouseholds[0];\r\n    return firstMatch.household.houseId || null;\r\n  }\r\n\r\n  // 新增：輔助方法 - 檢查 houseId 是否被選中\r\n  private isHouseIdSelected(houseId: number): boolean {\r\n    return this.selectedHouseIds.includes(houseId);\r\n  }\r\n\r\n  // 新增：輔助方法 - 檢查 houseId 是否被排除\r\n  private isHouseIdExcluded(houseId: number): boolean {\r\n    return this.excludedHouseIds.includes(houseId);\r\n  }\r\n\r\n  // 新增：從唯一識別符獲取戶別物件\r\n  getHouseholdFromUniqueId(uniqueId: string): HouseholdItem | null {\r\n    for (const building of this.buildings) {\r\n      const households = this.buildingData[building] || [];\r\n      const household = households.find(h => this.getHouseholdUniqueId(h) === uniqueId);\r\n      if (household) return household;\r\n    }\r\n    return null;\r\n  }\r\n\r\n  // 新增：houseName 模式相關的轉換方法\r\n  private convertHouseNamesToIds(houseNames: string[]): number[] {\r\n    const houseIds: number[] = [];\r\n    const duplicateWarnings: { name: string, count: number, buildings: string[] }[] = [];\r\n\r\n    houseNames.forEach(houseName => {\r\n      const matchingHouseholds: { building: string, household: HouseholdItem }[] = [];\r\n\r\n      // 收集所有符合名稱的戶別\r\n      for (const building of this.buildings) {\r\n        const households = this.buildingData[building] || [];\r\n        const matches = households.filter(h => h.houseName === houseName && h.houseId);\r\n        matches.forEach(household => {\r\n          matchingHouseholds.push({ building, household });\r\n        });\r\n      }\r\n\r\n      if (matchingHouseholds.length === 0) {\r\n        console.warn(`找不到 houseName \"${houseName}\" 對應的戶別`);\r\n      } else if (matchingHouseholds.length > 1) {\r\n        const buildings = matchingHouseholds.map(m => `${m.building}-${m.household.floor}`);\r\n        duplicateWarnings.push({\r\n          name: houseName,\r\n          count: matchingHouseholds.length,\r\n          buildings\r\n        });\r\n        // 取第一個匹配的\r\n        const firstMatch = matchingHouseholds[0];\r\n        if (firstMatch.household.houseId) {\r\n          houseIds.push(firstMatch.household.houseId);\r\n        }\r\n      } else {\r\n        const household = matchingHouseholds[0].household;\r\n        if (household.houseId) {\r\n          houseIds.push(household.houseId);\r\n        }\r\n      }\r\n    });\r\n\r\n    // 統一顯示重複名稱警告\r\n    if (duplicateWarnings.length > 0) {\r\n      console.group('⚠️ houseName 模式重複名稱警告');\r\n      duplicateWarnings.forEach(warning => {\r\n        console.warn(`戶名 \"${warning.name}\" 在多個位置重複 (${warning.count}個):`, warning.buildings);\r\n        console.warn('已選擇第一個匹配項，建議使用 houseId 模式以避免混淆');\r\n      });\r\n      console.groupEnd();\r\n    }\r\n\r\n    return houseIds;\r\n  }\r\n\r\n  private convertIdsToHouseNames(houseIds: number[]): string[] {\r\n    const houseNames: string[] = [];\r\n\r\n    houseIds.forEach(houseId => {\r\n      const household = this.getHouseholdByHouseId(houseId);\r\n      if (household) {\r\n        houseNames.push(household.houseName);\r\n      } else {\r\n        console.warn(`找不到 houseId ${houseId} 對應的戶別`);\r\n      }\r\n    });\r\n\r\n    return houseNames;\r\n  }\r\n\r\n  // 新增：更新 houseName 模式的分組選擇\r\n  private updateSelectedByBuildingNames() {\r\n    const grouped: { [building: string]: string[] } = {};\r\n\r\n    this.selectedHouseNames.forEach(houseName => {\r\n      for (const building of this.buildings) {\r\n        const households = this.buildingData[building] || [];\r\n        const item = households.find(h => h.houseName === houseName);\r\n        if (item) {\r\n          if (!grouped[building]) grouped[building] = [];\r\n          grouped[building].push(houseName);\r\n          break; // 找到第一個匹配就停止\r\n        }\r\n      }\r\n    });\r\n\r\n    this.selectedByBuildingNames = grouped;\r\n  }\r\n\r\n  // 新增：檢查 houseName 是否被選中\r\n  private isHouseNameSelected(houseName: string): boolean {\r\n    return this.selectedHouseNames.includes(houseName);\r\n  }\r\n\r\n  // 新增：獲取當前選擇的總數（支援兩種模式）\r\n  getCurrentSelectionCount(): number {\r\n    return this.useHouseNameMode ? this.selectedHouseNames.length : this.selectedHouseIds.length;\r\n  }\r\n\r\n  // 新增：檢查是否可以選擇更多（支援兩種模式）\r\n  canSelectMoreItems(): boolean {\r\n    const currentCount = this.getCurrentSelectionCount();\r\n    return !this.maxSelections || currentCount < this.maxSelections;\r\n  }\r\n}\r\n"], "mappings": ";AAAA,SAASA,SAAS,EAAEC,KAAK,EAAEC,MAAM,EAAEC,YAAY,EAAoCC,UAAU,EAAkCC,SAAS,QAAQ,eAAe;AAC/J,SAA+BC,iBAAiB,QAAQ,gBAAgB;AA8BjE,IAAMC,yBAAyB,GAA/B,MAAMA,yBAAyB;EA8BpCC,YACUC,GAAsB,EACtBC,kBAAsC,EACtCC,aAA8B;IAF9B,KAAAF,GAAG,GAAHA,GAAG;IACH,KAAAC,kBAAkB,GAAlBA,kBAAkB;IAClB,KAAAC,aAAa,GAAbA,aAAa;IA9Bd,KAAAC,WAAW,GAAW,OAAO;IAC7B,KAAAC,aAAa,GAAkB,IAAI;IACnC,KAAAC,QAAQ,GAAY,KAAK;IACzB,KAAAC,WAAW,GAAkB,IAAI,CAAC,CAAC;IACnC,KAAAC,YAAY,GAAiB,EAAE;IAAW,KAAAC,gBAAgB,GAAY,IAAI;IAC1E,KAAAC,WAAW,GAAY,IAAI;IAC3B,KAAAC,gBAAgB,GAAY,IAAI;IAAY,KAAAC,gBAAgB,GAAa,EAAE,CAAC,CAAC;IAC7E,KAAAC,gBAAgB,GAAY,KAAK,CAAC,CAAC;IAElC,KAAAC,eAAe,GAAG,IAAInB,YAAY,EAAmB;IACrD,KAAAoB,aAAa,GAAG,IAAIpB,YAAY,EAAY,CAAC,CAAC;IAC9C,KAAAqB,eAAe,GAAG,IAAIrB,YAAY,EAAY,CAAC,CAAC;IAC1D,KAAAsB,MAAM,GAAG,KAAK;IACd,KAAAC,gBAAgB,GAAG,EAAE;IACrB,KAAAC,UAAU,GAAG,EAAE;IAAG,KAAAC,aAAa,GAAG,EAAE,CAAC,CAAC;IACtC,KAAAC,gBAAgB,GAAa,EAAE,CAAC,CAAC;IACjC,KAAAC,kBAAkB,GAAa,EAAE,CAAC,CAAC;IACnC,KAAAC,SAAS,GAAa,EAAE;IACxB,KAAAC,MAAM,GAAa,EAAE,CAAC,CAAC;IACvB,KAAAC,kBAAkB,GAAa,EAAE,CAAC,CAAE;IACpC,KAAAC,kBAAkB,GAAqC,EAAE,CAAC,CAAC;IAC3D,KAAAC,uBAAuB,GAAqC,EAAE,CAAC,CAAC;IAChE,KAAAC,SAAS,GAAY,KAAK,CAAC,CAAC;IAE5B;IACQ,KAAAC,QAAQ,GAAIC,KAA0B,IAAI,CAAG,CAAC;IAC9C,KAAAC,SAAS,GAAG,MAAK,CAAG,CAAC;EAKzB;EAAGC,UAAUA,CAACF,KAAY;IAC5BG,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEJ,KAAK,EAAE,OAAO,EAAE,OAAOA,KAAK,GAAG,CAAC,CAAC,EAAE,mBAAmB,EAAE,IAAI,CAACjB,gBAAgB,CAAC;IAErH,IAAI,CAACiB,KAAK,IAAIA,KAAK,CAACK,MAAM,KAAK,CAAC,EAAE;MAChC,IAAI,CAACd,gBAAgB,GAAG,EAAE;MAC1B,IAAI,CAACC,kBAAkB,GAAG,EAAE;IAC9B,CAAC,MAAM;MACL,MAAMc,SAAS,GAAGN,KAAK,CAAC,CAAC,CAAC;MAE1B,IAAI,IAAI,CAACjB,gBAAgB,EAAE;QACzB;QACA,IAAI,OAAOuB,SAAS,KAAK,QAAQ,EAAE;UACjC,IAAI,CAACd,kBAAkB,GAAGQ,KAAiB;UAC3C;UACA,IAAI,CAACT,gBAAgB,GAAG,IAAI,CAACgB,sBAAsB,CAAC,IAAI,CAACf,kBAAkB,CAAC;UAC5EW,OAAO,CAACC,GAAG,CAAC,qCAAqC,EAAE,IAAI,CAACZ,kBAAkB,CAAC;UAC3EW,OAAO,CAACC,GAAG,CAAC,eAAe,EAAE,IAAI,CAACb,gBAAgB,CAAC;QACrD,CAAC,MAAM,IAAI,OAAOe,SAAS,KAAK,QAAQ,EAAE;UACxC;UACAH,OAAO,CAACK,IAAI,CAAC,oCAAoC,CAAC;UAClD,IAAI,CAACjB,gBAAgB,GAAGS,KAAiB;UACzC,IAAI,CAACR,kBAAkB,GAAG,IAAI,CAACiB,sBAAsB,CAAC,IAAI,CAAClB,gBAAgB,CAAC;UAC5EY,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAE,IAAI,CAACZ,kBAAkB,CAAC;QAClE,CAAC,MAAM;UACLW,OAAO,CAACO,KAAK,CAAC,yBAAyB,EAAEV,KAAK,CAAC;UAC/C,IAAI,CAACT,gBAAgB,GAAG,EAAE;UAC1B,IAAI,CAACC,kBAAkB,GAAG,EAAE;QAC9B;MACF,CAAC,MAAM;QACL;QACA,IAAI,OAAOc,SAAS,KAAK,QAAQ,EAAE;UACjC,IAAI,CAACf,gBAAgB,GAAGS,KAAiB;UACzC,IAAI,CAACR,kBAAkB,GAAG,IAAI,CAACiB,sBAAsB,CAAC,IAAI,CAAClB,gBAAgB,CAAC;UAC5EY,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAE,IAAI,CAACb,gBAAgB,CAAC;QACvE,CAAC,MAAM,IAAI,OAAOe,SAAS,KAAK,QAAQ,EAAE;UACxCH,OAAO,CAACK,IAAI,CAAC,oCAAoC,CAAC;UAClD,IAAI,CAAChB,kBAAkB,GAAGQ,KAAiB;UAC3C,IAAI,CAACT,gBAAgB,GAAG,IAAI,CAACgB,sBAAsB,CAAC,IAAI,CAACf,kBAAkB,CAAC;UAC5EW,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAE,IAAI,CAACb,gBAAgB,CAAC;QAC9D,CAAC,MAAM;UACLY,OAAO,CAACO,KAAK,CAAC,uBAAuB,EAAEV,KAAK,CAAC;UAC7C,IAAI,CAACT,gBAAgB,GAAG,EAAE;UAC1B,IAAI,CAACC,kBAAkB,GAAG,EAAE;QAC9B;MACF;IACF;IAEA,IAAI,CAACmB,wBAAwB,EAAE;EACjC;EACAC,gBAAgBA,CAACC,EAAwC;IACvD,IAAI,CAACd,QAAQ,GAAGc,EAAE;EACpB;EAEAC,iBAAiBA,CAACD,EAAc;IAC9B,IAAI,CAACZ,SAAS,GAAGY,EAAE;EACrB;EACAE,gBAAgBA,CAACC,UAAmB;IAClC,IAAI,CAACxC,QAAQ,GAAGwC,UAAU;EAC5B;EAAEC,QAAQA,CAAA;IACR,IAAI,CAACC,cAAc,EAAE;EACvB;EACAC,WAAWA,CAACC,OAAsB;IAChC,IAAIA,OAAO,CAAC,aAAa,CAAC,IAAI,IAAI,CAAC3C,WAAW,EAAE;MAC9C;MACA,IAAI,CAACyC,cAAc,EAAE;IACvB;IACA,IAAIE,OAAO,CAAC,cAAc,CAAC,EAAE;MAC3B;MACA,IAAI,CAAC3B,SAAS,GAAG4B,MAAM,CAACC,IAAI,CAAC,IAAI,CAAC5C,YAAY,IAAI,EAAE,CAAC;MACrDyB,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAE,IAAI,CAAC1B,YAAY,CAAC;MACvD,IAAI,CAAC6C,wBAAwB,EAAE;MAC/B,IAAI,CAACZ,wBAAwB,EAAE;IACjC;IAAE,IAAIS,OAAO,CAAC,kBAAkB,CAAC,EAAE;MACjC;MACA,IAAI,CAACG,wBAAwB,EAAE;MAC/BpB,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAE,IAAI,CAACtB,gBAAgB,CAAC;IACnE;EACF;EAAUoC,cAAcA,CAAA;IACtB;IACA,IAAI,IAAI,CAACxC,YAAY,IAAI2C,MAAM,CAACC,IAAI,CAAC,IAAI,CAAC5C,YAAY,CAAC,CAAC2B,MAAM,GAAG,CAAC,EAAE;MAClE;MACA,IAAI,CAACZ,SAAS,GAAG4B,MAAM,CAACC,IAAI,CAAC,IAAI,CAAC5C,YAAY,CAAC;MAC/CyB,OAAO,CAACC,GAAG,CAAC,mDAAmD,EAAE,IAAI,CAACX,SAAS,CAAC;MAChF,IAAI,CAACkB,wBAAwB,EAAE;IACjC,CAAC,MAAM,IAAI,IAAI,CAAClC,WAAW,EAAE;MAC3B;MACA,IAAI,CAAC+C,uBAAuB,EAAE;IAChC,CAAC,MAAM;MACL;MACA,IAAI,CAAC/B,SAAS,GAAG,EAAE;MACnBU,OAAO,CAACC,GAAG,CAAC,yCAAyC,CAAC;IACxD;EACF;EAEQoB,uBAAuBA,CAAA;IAC7B,IAAI,CAAC,IAAI,CAAC/C,WAAW,EAAE;IAEvB,IAAI,CAACqB,SAAS,GAAG,IAAI;IACrB,IAAI,CAAC1B,kBAAkB,CAACqD,WAAW,CAAC,IAAI,CAAChD,WAAW,CAAC,CAACiD,SAAS,CAAC;MAC9DC,IAAI,EAAGC,QAAQ,IAAI;QACjBzB,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEwB,QAAQ,CAAC;QACtC,IAAI,CAAClD,YAAY,GAAG,IAAI,CAACmD,gCAAgC,CAACD,QAAQ,CAACE,OAAO,CAAC;QAC3E,IAAI,CAACrC,SAAS,GAAG4B,MAAM,CAACC,IAAI,CAAC,IAAI,CAAC5C,YAAY,CAAC;QAC/C,IAAI,CAACiC,wBAAwB,EAAE;QAC/B,IAAI,CAACb,SAAS,GAAG,KAAK;QACtB,IAAI,CAAC3B,GAAG,CAAC4D,aAAa,EAAE;MAC1B,CAAC;MAAErB,KAAK,EAAGA,KAAK,IAAI;QAClBP,OAAO,CAACO,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;QACpD;QACA,IAAI,CAAChC,YAAY,GAAG,EAAE;QACtB,IAAI,CAACe,SAAS,GAAG,EAAE;QACnB,IAAI,CAACK,SAAS,GAAG,KAAK;QACtB,IAAI,CAAC3B,GAAG,CAAC4D,aAAa,EAAE;MAC1B;KACD,CAAC;EACJ;EACQF,gCAAgCA,CAACG,OAAiC;IACxE,MAAMtD,YAAY,GAAiB,EAAE;IAErC2C,MAAM,CAACW,OAAO,CAACA,OAAO,CAAC,CAACC,OAAO,CAAC,CAAC,CAACC,QAAQ,EAAEC,MAAM,CAAC,KAAI;MACrDzD,YAAY,CAACwD,QAAQ,CAAC,GAAGC,MAAM,CAACC,GAAG,CAACC,KAAK,KAAK;QAC5CC,SAAS,EAAED,KAAK,CAACC,SAAS,IAAID,KAAK,CAACE,SAAS,IAAIF,KAAK,CAACG,IAAI;QAC3DN,QAAQ,EAAEG,KAAK,CAACH,QAAQ,IAAIG,KAAK,CAACI,QAAQ,IAAIP,QAAQ;QACtDQ,KAAK,EAAEL,KAAK,CAACK,KAAK,IAAIL,KAAK,CAACM,KAAK;QACjCC,OAAO,EAAEP,KAAK,CAACO,OAAO,IAAIP,KAAK,CAACQ,OAAO;QACvCC,UAAU,EAAE,KAAK;QACjB9B,UAAU,EAAE;OACb,CAAC,CAAC;IACL,CAAC,CAAC;IAEF,OAAOtC,YAAY;EACrB;EAAWiC,wBAAwBA,CAAA;IACjC;IACA,MAAMoC,OAAO,GAAqC,EAAE;IAEpD,IAAI,CAACxD,gBAAgB,CAAC0C,OAAO,CAACW,OAAO,IAAG;MACtC,KAAK,MAAMV,QAAQ,IAAI,IAAI,CAACzC,SAAS,EAAE;QACrC,MAAMuD,IAAI,GAAG,IAAI,CAACtE,YAAY,CAACwD,QAAQ,CAAC,EAAEe,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACN,OAAO,KAAKA,OAAO,CAAC;QAC1E,IAAII,IAAI,EAAE;UACR,IAAI,CAACD,OAAO,CAACb,QAAQ,CAAC,EAAEa,OAAO,CAACb,QAAQ,CAAC,GAAG,EAAE;UAC9Ca,OAAO,CAACb,QAAQ,CAAC,CAACiB,IAAI,CAACP,OAAO,CAAC;UAC/B;QACF;MACF;IACF,CAAC,CAAC;IAEF,IAAI,CAAChD,kBAAkB,GAAGmD,OAAO;IAEjC;IACA,IAAI,IAAI,CAAChE,gBAAgB,EAAE;MACzB,IAAI,CAACqE,6BAA6B,EAAE;IACtC;EACF;EAACC,gBAAgBA,CAACnB,QAAgB;IAChC/B,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAE8B,QAAQ,CAAC;IAC3C,IAAI,CAAC9C,gBAAgB,GAAG8C,QAAQ;IAChC,IAAI,CAAC5C,aAAa,GAAG,EAAE,CAAC,CAAC;IACzB,IAAI,CAACD,UAAU,GAAG,EAAE;IACpB,IAAI,CAACiE,uBAAuB,EAAE,CAAC,CAAC;IAChC,IAAI,CAAC/B,wBAAwB,EAAE;IAC/BpB,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAE,IAAI,CAACT,kBAAkB,CAACU,MAAM,CAAC;IACzE;IACA,IAAI,CAAClC,GAAG,CAAC4D,aAAa,EAAE;EAC1B;EAEAwB,eAAeA,CAACrB,QAAgB;IAC9B/B,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAE8B,QAAQ,CAAC;EACxD;EAAEX,wBAAwBA,CAAA;IACxBpB,OAAO,CAACC,GAAG,CAAC,4CAA4C,EAAE,IAAI,CAAChB,gBAAgB,EAAE,QAAQ,EAAE,IAAI,CAACE,aAAa,CAAC;IAC9G,IAAI,CAAC,IAAI,CAACF,gBAAgB,EAAE;MAC1B,IAAI,CAACO,kBAAkB,GAAG,EAAE;MAC5B;IACF;IAEA,MAAM6D,UAAU,GAAG,IAAI,CAAC9E,YAAY,CAAC,IAAI,CAACU,gBAAgB,CAAC,IAAI,EAAE;IACjEe,OAAO,CAACC,GAAG,CAAC,oCAAoC,EAAEoD,UAAU,CAACnD,MAAM,CAAC;IAEpE;IACA,MAAMoD,aAAa,GAAGD,UAAU,CAACE,MAAM,CAACR,CAAC,IAAG;MAC1C;MACA,MAAMS,UAAU,GAAG,CAAC,IAAI,CAACrE,aAAa,IAAI4D,CAAC,CAACR,KAAK,KAAK,IAAI,CAACpD,aAAa;MACxE;MACA,MAAMsE,WAAW,GAAG,CAAC,IAAI,CAACvE,UAAU,IAAI6D,CAAC,CAACZ,SAAS,CAACuB,WAAW,EAAE,CAACC,QAAQ,CAAC,IAAI,CAACzE,UAAU,CAACwE,WAAW,EAAE,CAAC;MACzG,OAAOF,UAAU,IAAIC,WAAW;IAClC,CAAC,CAAC;IAEF;IACA,IAAI,CAACjE,kBAAkB,GAAG8D,aAAa,CAACrB,GAAG,CAACc,CAAC,IAAIA,CAAC,CAACZ,SAAS,CAAC;IAE7DnC,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAE,IAAI,CAACT,kBAAkB,CAACU,MAAM,CAAC;IAC1EF,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEqD,aAAa,CAACrB,GAAG,CAACc,CAAC,IAAI,GAAGA,CAAC,CAACZ,SAAS,IAAIY,CAAC,CAACR,KAAK,QAAQQ,CAAC,CAACN,OAAO,GAAG,CAAC,CAAC;EAC/G;EAEAmB,cAAcA,CAACC,KAAU;IACvB,IAAI,CAAC3E,UAAU,GAAG2E,KAAK,CAACC,MAAM,CAACjE,KAAK;IACpC,IAAI,CAACuB,wBAAwB,EAAE;IAC/BpB,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAE,IAAI,CAACf,UAAU,CAAC;EACtD;EACA6E,WAAWA,CAAA;IACT,IAAI,CAAC7E,UAAU,GAAG,EAAE;IACpB,IAAI,CAACkC,wBAAwB,EAAE;IAC/BpB,OAAO,CAACC,GAAG,CAAC,cAAc,CAAC;EAC7B;EAAG+D,iBAAiBA,CAACvB,OAA2B;IAC9CzC,OAAO,CAACC,GAAG,CAAC,wCAAwC,EAAEwC,OAAO,CAAC;IAC9DzC,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAE,IAAI,CAACb,gBAAgB,CAAC;IAE/D,IAAI,CAACqD,OAAO,EAAE;MACZzC,OAAO,CAACC,GAAG,CAAC,gBAAgBwC,OAAO,EAAE,CAAC;MACtC;IACF;IAEA;IACA,IAAI,IAAI,CAACwB,iBAAiB,CAACxB,OAAO,CAAC,EAAE;MACnCzC,OAAO,CAACC,GAAG,CAAC,SAASwC,OAAO,kBAAkB,CAAC;MAC/C;IACF;IAEA,MAAME,UAAU,GAAG,IAAI,CAACuB,iBAAiB,CAACzB,OAAO,CAAC;IAClD,IAAI0B,mBAA6B;IACjC,IAAIC,qBAA+B;IAEnC,IAAIzB,UAAU,EAAE;MACdwB,mBAAmB,GAAG,IAAI,CAAC/E,gBAAgB,CAACmE,MAAM,CAACc,EAAE,IAAIA,EAAE,KAAK5B,OAAO,CAAC;MACxE;MACA,MAAM6B,SAAS,GAAG,IAAI,CAACC,qBAAqB,CAAC9B,OAAO,CAAC;MACrD,IAAI6B,SAAS,EAAE;QACbF,qBAAqB,GAAG,IAAI,CAAC/E,kBAAkB,CAACkE,MAAM,CAACiB,IAAI,IAAIA,IAAI,KAAKF,SAAS,CAACnC,SAAS,CAAC;MAC9F,CAAC,MAAM;QACLiC,qBAAqB,GAAG,CAAC,GAAG,IAAI,CAAC/E,kBAAkB,CAAC;MACtD;IACF,CAAC,MAAM;MACL,IAAI,IAAI,CAACjB,aAAa,IAAI,IAAI,CAACqG,wBAAwB,EAAE,IAAI,IAAI,CAACrG,aAAa,EAAE;QAC/E4B,OAAO,CAACC,GAAG,CAAC,WAAW,CAAC;QACxB,OAAO,CAAC;MACV;MACAkE,mBAAmB,GAAG,CAAC,GAAG,IAAI,CAAC/E,gBAAgB,EAAEqD,OAAO,CAAC;MACzD;MACA,MAAM6B,SAAS,GAAG,IAAI,CAACC,qBAAqB,CAAC9B,OAAO,CAAC;MACrD,IAAI6B,SAAS,EAAE;QACbF,qBAAqB,GAAG,CAAC,GAAG,IAAI,CAAC/E,kBAAkB,EAAEiF,SAAS,CAACnC,SAAS,CAAC;MAC3E,CAAC,MAAM;QACLiC,qBAAqB,GAAG,CAAC,GAAG,IAAI,CAAC/E,kBAAkB,CAAC;MACtD;IACF;IAEA,IAAI,CAACD,gBAAgB,GAAG+E,mBAAmB;IAC3C,IAAI,CAAC9E,kBAAkB,GAAG+E,qBAAqB;IAC/C,IAAI,CAACM,WAAW,EAAE;EACpB;EAAGC,iBAAiBA,CAAClC,OAAe;IAClC,IAAI,CAACrD,gBAAgB,GAAG,IAAI,CAACA,gBAAgB,CAACmE,MAAM,CAACc,EAAE,IAAIA,EAAE,KAAK5B,OAAO,CAAC;IAE1E;IACA,MAAM6B,SAAS,GAAG,IAAI,CAACC,qBAAqB,CAAC9B,OAAO,CAAC;IACrD,IAAI6B,SAAS,EAAE;MACb,IAAI,CAACjF,kBAAkB,GAAG,IAAI,CAACA,kBAAkB,CAACkE,MAAM,CAACiB,IAAI,IAAIA,IAAI,KAAKF,SAAS,CAACnC,SAAS,CAAC;IAChG;IAEA,IAAI,CAACuC,WAAW,EAAE;EACpB;EAACE,mBAAmBA,CAAA;IAClB5E,OAAO,CAACC,GAAG,CAAC,4BAA4B,CAAC;IACzCD,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAE,IAAI,CAAChB,gBAAgB,CAAC;IACvDe,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAE,IAAI,CAACd,aAAa,CAAC;IACjDa,OAAO,CAACC,GAAG,CAAC,aAAa,EAAE,IAAI,CAACf,UAAU,CAAC;IAE3C,IAAI,CAAC,IAAI,CAACD,gBAAgB,EAAE;MAC1Be,OAAO,CAACC,GAAG,CAAC,sBAAsB,CAAC;MACnC;IACF;IAEA,MAAMoD,UAAU,GAAG,IAAI,CAAC9E,YAAY,CAAC,IAAI,CAACU,gBAAgB,CAAC,IAAI,EAAE;IACjEe,OAAO,CAACC,GAAG,CAAC,oCAAoC,EAAEoD,UAAU,CAACnD,MAAM,CAAC;IAEpE;IACA,MAAM2E,sBAAsB,GAAGxB,UAAU,CAACE,MAAM,CAACR,CAAC,IAAG;MACnD;MACA,MAAMS,UAAU,GAAG,CAAC,IAAI,CAACrE,aAAa,IAAI4D,CAAC,CAACR,KAAK,KAAK,IAAI,CAACpD,aAAa;MACxE;MACA,MAAMsE,WAAW,GAAG,CAAC,IAAI,CAACvE,UAAU,IAAI6D,CAAC,CAACZ,SAAS,CAACuB,WAAW,EAAE,CAACC,QAAQ,CAAC,IAAI,CAACzE,UAAU,CAACwE,WAAW,EAAE,CAAC;MACzG,OAAOF,UAAU,IAAIC,WAAW;IAClC,CAAC,CAAC;IAEFzD,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAE4E,sBAAsB,CAAC5C,GAAG,CAACc,CAAC,IAAI,GAAGA,CAAC,CAACZ,SAAS,IAAIY,CAAC,CAACR,KAAK,QAAQQ,CAAC,CAACN,OAAO,GAAG,CAAC,CAAC;IAExH,IAAIoC,sBAAsB,CAAC3E,MAAM,KAAK,CAAC,EAAE;MACvCF,OAAO,CAACC,GAAG,CAAC,8BAA8B,CAAC;MAC3C;IACF;IAEA;IACA,MAAM6E,YAAY,GAAG,IAAI,CAAC1F,gBAAgB,CAACc,MAAM;IACjD,MAAM6E,UAAU,GAAG,IAAI,CAAC3G,aAAa,IAAI4G,QAAQ;IACjD,MAAMC,cAAc,GAAGF,UAAU,GAAGD,YAAY;IAEhD;IACA,MAAMI,qBAAqB,GAAa,EAAE;IAC1C,KAAK,MAAMZ,SAAS,IAAIO,sBAAsB,EAAE;MAC9C,IAAIP,SAAS,CAAC7B,OAAO,IACnB,CAAC,IAAI,CAACyB,iBAAiB,CAACI,SAAS,CAAC7B,OAAO,CAAC,IAC1C,CAAC,IAAI,CAACwB,iBAAiB,CAACK,SAAS,CAAC7B,OAAO,CAAC,EAAE;QAC5CyC,qBAAqB,CAAClC,IAAI,CAACsB,SAAS,CAAC7B,OAAO,CAAC;MAC/C;IACF;IAEAzC,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEiF,qBAAqB,CAAC;IAE9D;IACA,MAAMC,KAAK,GAAGD,qBAAqB,CAACE,KAAK,CAAC,CAAC,EAAEH,cAAc,CAAC;IAE5D,IAAIE,KAAK,CAACjF,MAAM,GAAG,CAAC,EAAE;MACpB,IAAI,CAACd,gBAAgB,GAAG,CAAC,GAAG,IAAI,CAACA,gBAAgB,EAAE,GAAG+F,KAAK,CAAC;MAC5D,IAAI,CAACT,WAAW,EAAE;MAClB1E,OAAO,CAACC,GAAG,CAAC,aAAakF,KAAK,CAACjF,MAAM,WAAW,EAAEiF,KAAK,CAAC;IAC1D,CAAC,MAAM;MACLnF,OAAO,CAACC,GAAG,CAAC,sBAAsB,CAAC;IACrC;EACF;EAAEoF,mBAAmBA,CAAA;IACnB,IAAI,CAAC,IAAI,CAACpG,gBAAgB,EAAE;IAE5B;IACA,MAAMqG,kBAAkB,GAAG,IAAI,CAAC/G,YAAY,CAAC,IAAI,CAACU,gBAAgB,CAAC,IAAI,EAAE;IAEzE;IACA,MAAM6F,YAAY,GAAG,IAAI,CAAC1F,gBAAgB,CAACc,MAAM;IACjD,MAAM6E,UAAU,GAAG,IAAI,CAAC3G,aAAa,IAAI4G,QAAQ;IACjD,MAAMC,cAAc,GAAGF,UAAU,GAAGD,YAAY;IAEhD;IACA,MAAMS,qBAAqB,GAAa,EAAE;IAC1C,KAAK,MAAMjB,SAAS,IAAIgB,kBAAkB,EAAE;MAC1C,IAAIhB,SAAS,CAAC7B,OAAO,IACnB,CAAC,IAAI,CAACrD,gBAAgB,CAACuE,QAAQ,CAACW,SAAS,CAAC7B,OAAO,CAAC,IAClD,CAAC,IAAI,CAAC+C,mBAAmB,CAAClB,SAAS,CAAC7B,OAAO,CAAC,EAAE;QAC9C8C,qBAAqB,CAACvC,IAAI,CAACsB,SAAS,CAAC7B,OAAO,CAAC;MAC/C;IACF;IAEA;IACA,MAAM0C,KAAK,GAAGI,qBAAqB,CAACH,KAAK,CAAC,CAAC,EAAEH,cAAc,CAAC;IAAE,IAAIE,KAAK,CAACjF,MAAM,GAAG,CAAC,EAAE;MAClF,IAAI,CAACd,gBAAgB,GAAG,CAAC,GAAG,IAAI,CAACA,gBAAgB,EAAE,GAAG+F,KAAK,CAAC;MAC5D,IAAI,CAACT,WAAW,EAAE;MAClB1E,OAAO,CAACC,GAAG,CAAC,aAAakF,KAAK,CAACjF,MAAM,MAAM,CAAC;IAC9C;EACF;EAEAuF,qBAAqBA,CAAA;IACnB,IAAI,CAAC,IAAI,CAACxG,gBAAgB,EAAE;IAE5B,MAAMqG,kBAAkB,GAAG,IAAI,CAAC/G,YAAY,CAAC,IAAI,CAACU,gBAAgB,CAAC,IAAI,EAAE;IACzE,MAAMyG,gBAAgB,GAAGJ,kBAAkB,CAACrD,GAAG,CAACc,CAAC,IAAIA,CAAC,CAACN,OAAO,CAAC,CAACc,MAAM,CAACc,EAAE,IAAIA,EAAE,KAAKsB,SAAS,CAAa;IAC1G,IAAI,CAACvG,gBAAgB,GAAG,IAAI,CAACA,gBAAgB,CAACmE,MAAM,CAACc,EAAE,IAAI,CAACqB,gBAAgB,CAAC/B,QAAQ,CAACU,EAAE,CAAC,CAAC;IAC1F,IAAI,CAACK,WAAW,EAAE;EACpB;EACAkB,UAAUA,CAAA;IACR,IAAI,CAACxG,gBAAgB,GAAG,EAAE;IAC1B,IAAI,CAACC,kBAAkB,GAAG,EAAE;IAC5B,IAAI,CAACqF,WAAW,EAAE;EACpB;EAASA,WAAWA,CAAA;IAClB,IAAI,CAAClE,wBAAwB,EAAE;IAE/B,IAAI,IAAI,CAAC5B,gBAAgB,EAAE;MACzBoB,OAAO,CAACC,GAAG,CAAC,yDAAyD,EAAE,IAAI,CAACZ,kBAAkB,CAAC;MAC/F,IAAI,CAACO,QAAQ,CAAC,CAAC,GAAG,IAAI,CAACP,kBAAkB,CAAC,CAAC;IAC7C,CAAC,MAAM;MACLW,OAAO,CAACC,GAAG,CAAC,qDAAqD,EAAE,IAAI,CAACb,gBAAgB,CAAC;MACzF,IAAI,CAACQ,QAAQ,CAAC,CAAC,GAAG,IAAI,CAACR,gBAAgB,CAAC,CAAC;IAC3C;IAEA,IAAI,CAACU,SAAS,EAAE;IAEhB,MAAM+F,aAAa,GAAG,IAAI,CAACzG,gBAAgB,CAAC6C,GAAG,CAACQ,OAAO,IAAG;MACxD,KAAK,MAAMV,QAAQ,IAAI,IAAI,CAACzC,SAAS,EAAE;QACrC,MAAMuD,IAAI,GAAG,IAAI,CAACtE,YAAY,CAACwD,QAAQ,CAAC,EAAEe,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACN,OAAO,KAAKA,OAAO,CAAC;QAC1E,IAAII,IAAI,EAAE,OAAOA,IAAI;MACvB;MACA,OAAO,IAAI;IACb,CAAC,CAAC,CAACU,MAAM,CAACV,IAAI,IAAIA,IAAI,KAAK,IAAI,CAAoB;IAEnD7C,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAE4F,aAAa,CAAC;IACrD,IAAI,CAAChH,eAAe,CAACiH,IAAI,CAACD,aAAa,CAAC;IAExC;IACA,MAAME,QAAQ,GAAGF,aAAa,CAAC5D,GAAG,CAACY,IAAI,IAAIA,IAAI,CAACJ,OAAQ,CAAC,CAACc,MAAM,CAACc,EAAE,IAAIA,EAAE,KAAKsB,SAAS,CAAC;IACxF3F,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAE8F,QAAQ,CAAC;IAC3C,IAAI,CAACjH,aAAa,CAACgH,IAAI,CAACC,QAAQ,CAAC;IAEjC;IACA,MAAMC,UAAU,GAAGH,aAAa,CAAC5D,GAAG,CAACY,IAAI,IAAIA,IAAI,CAACV,SAAS,CAAC;IAC5DnC,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAE+F,UAAU,CAAC;IAC/C,IAAI,CAACjH,eAAe,CAAC+G,IAAI,CAACE,UAAU,CAAC;EACvC;EAACC,cAAcA,CAAA;IACb,IAAI,CAAC,IAAI,CAAC5H,QAAQ,EAAE;MAClB,IAAI,CAAC6H,UAAU,EAAE;MACjBlG,OAAO,CAACC,GAAG,CAAC,oCAAoC,CAAC;MACjDD,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAE,IAAI,CAACX,SAAS,CAAC;IACrD;EACF;EAEA4G,UAAUA,CAAA;IACR,IAAI,CAAChI,aAAa,CAACiI,IAAI,CAAC,IAAI,CAACC,eAAe,EAAE;MAC5CC,OAAO,EAAE,EAAE;MACXC,oBAAoB,EAAE,KAAK;MAC3BC,UAAU,EAAE,IAAI;MAChBC,SAAS,EAAE;KACZ,CAAC;EACJ;EAEAC,aAAaA,CAAA;IACX;IACA;EAAA;EAEFC,mBAAmBA,CAACjE,OAA2B;IAC7C,IAAI,CAACA,OAAO,EAAE,OAAO,KAAK;IAC1B,OAAO,IAAI,CAACrD,gBAAgB,CAACuE,QAAQ,CAAClB,OAAO,CAAC;EAChD;EAEA+C,mBAAmBA,CAAC/C,OAA2B;IAC7C,IAAI,CAACA,OAAO,EAAE,OAAO,KAAK;IAC1B,OAAO,IAAI,CAAC9D,gBAAgB,CAACgF,QAAQ,CAAClB,OAAO,CAAC;EAChD;EAEAkE,mBAAmBA,CAAClE,OAA2B;IAC7C,IAAI,CAACA,OAAO,EAAE,OAAO,IAAI;IACzB,OAAO,IAAI,CAAC+C,mBAAmB,CAAC/C,OAAO,CAAC,IACrC,CAAC,IAAI,CAACmE,aAAa,EAAE,IAAI,CAAC,IAAI,CAACF,mBAAmB,CAACjE,OAAO,CAAE;EACjE;EAAGmE,aAAaA,CAAA;IACd,OAAO,IAAI,CAACC,kBAAkB,EAAE;EAClC;EAACC,qBAAqBA,CAAA;IACpB,IAAI,CAAC,IAAI,CAAC7H,gBAAgB,EAAE,OAAO,KAAK;IACxC,MAAMqG,kBAAkB,GAAG,IAAI,CAAC/G,YAAY,CAAC,IAAI,CAACU,gBAAgB,CAAC,CAChEsE,MAAM,CAACR,CAAC,IAAI,CAACA,CAAC,CAAClC,UAAU,IAAIkC,CAAC,CAACN,OAAO,KAAKkD,SAAS,CAAC;IACxD,OAAOL,kBAAkB,CAACpF,MAAM,GAAG,CAAC,IAClCoF,kBAAkB,CAACyB,KAAK,CAACzC,SAAS,IAAIA,SAAS,CAAC7B,OAAO,IAAI,IAAI,CAACrD,gBAAgB,CAACuE,QAAQ,CAACW,SAAS,CAAC7B,OAAO,CAAC,CAAC;EACjH;EAEAuE,sBAAsBA,CAAA;IACpB,IAAI,CAAC,IAAI,CAAC/H,gBAAgB,EAAE,OAAO,KAAK;IACxC,MAAMqG,kBAAkB,GAAG,IAAI,CAAC/G,YAAY,CAAC,IAAI,CAACU,gBAAgB,CAAC,IAAI,EAAE;IACzE,OAAOqG,kBAAkB,CAAC2B,IAAI,CAAC3C,SAAS,IAAIA,SAAS,CAAC7B,OAAO,IAAI,IAAI,CAACrD,gBAAgB,CAACuE,QAAQ,CAACW,SAAS,CAAC7B,OAAO,CAAC,CAAC;EACrH;EAAEyE,qBAAqBA,CAAA;IACrB,OAAO,IAAI,CAACzH,kBAAkB;EAChC;EAEA0H,gBAAgBA,CAACpF,QAAgB;IAC/B,OAAO,IAAI,CAACxD,YAAY,CAACwD,QAAQ,CAAC,EAAE7B,MAAM,IAAI,CAAC;EACjD;EACAkH,gBAAgBA,CAAA;IACd,OAAO,IAAI,CAAC3C,wBAAwB,EAAE;EACxC;EAEA;EACA4C,2BAA2BA,CAACtF,QAAgB;IAC1C,OAAO,IAAI,CAACtC,kBAAkB,CAACsC,QAAQ,CAAC,IAAI,EAAE;EAChD;EAEA;EACAuF,mBAAmBA,CAACvF,QAAgB;IAClC,OAAO,CAAC,EAAE,IAAI,CAACtC,kBAAkB,CAACsC,QAAQ,CAAC,IAAI,IAAI,CAACtC,kBAAkB,CAACsC,QAAQ,CAAC,CAAC7B,MAAM,GAAG,CAAC,CAAC;EAC9F;EAEA;EACQiD,uBAAuBA,CAAA;IAC7B,IAAI,CAAC,IAAI,CAAClE,gBAAgB,EAAE;MAC1B,IAAI,CAACM,MAAM,GAAG,EAAE;MAChB;IACF;IAEA,MAAM8D,UAAU,GAAG,IAAI,CAAC9E,YAAY,CAAC,IAAI,CAACU,gBAAgB,CAAC,IAAI,EAAE;IACjE,MAAMsI,QAAQ,GAAG,IAAIC,GAAG,EAAU;IAElCnE,UAAU,CAACvB,OAAO,CAACwC,SAAS,IAAG;MAC7B,IAAIA,SAAS,CAAC/B,KAAK,EAAE;QACnBgF,QAAQ,CAACE,GAAG,CAACnD,SAAS,CAAC/B,KAAK,CAAC;MAC/B;IACF,CAAC,CAAC;IAEF;IACA,IAAI,CAAChD,MAAM,GAAGmI,KAAK,CAACC,IAAI,CAACJ,QAAQ,CAAC,CAACK,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAI;MAC/C,MAAMC,IAAI,GAAGC,QAAQ,CAACH,CAAC,CAACI,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC,CAAC;MAC/C,MAAMC,IAAI,GAAGF,QAAQ,CAACF,CAAC,CAACG,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC,CAAC;MAC/C,OAAOF,IAAI,GAAGG,IAAI;IACpB,CAAC,CAAC;IAEFlI,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAE,IAAI,CAAChB,gBAAgB,EAAE,IAAI,CAACM,MAAM,CAAC;EACjF;EAEA;EACA4I,aAAaA,CAAC5F,KAAa;IACzBvC,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEsC,KAAK,CAAC;IACrC,IAAI,CAACpD,aAAa,GAAG,IAAI,CAACA,aAAa,KAAKoD,KAAK,GAAG,EAAE,GAAGA,KAAK,CAAC,CAAC;IAChE,IAAI,CAACnB,wBAAwB,EAAE;IAC/B,IAAI,CAACpD,GAAG,CAAC4D,aAAa,EAAE;EAC1B;EAEA;EACAwG,aAAaA,CAAC7F,KAAa;IACzB,IAAI,CAAC,IAAI,CAACtD,gBAAgB,EAAE,OAAO,CAAC;IACpC,MAAMoE,UAAU,GAAG,IAAI,CAAC9E,YAAY,CAAC,IAAI,CAACU,gBAAgB,CAAC,IAAI,EAAE;IACjE,OAAOoE,UAAU,CAACE,MAAM,CAACR,CAAC,IAAIA,CAAC,CAACR,KAAK,KAAKA,KAAK,CAAC,CAACrC,MAAM;EACzD;EACA;EACAmI,iBAAiBA,CAACC,aAAqB;IACrC,IAAI,CAAC,IAAI,CAACrJ,gBAAgB,EAAE,OAAO,EAAE;IACrC,MAAMoE,UAAU,GAAG,IAAI,CAAC9E,YAAY,CAAC,IAAI,CAACU,gBAAgB,CAAC,IAAI,EAAE;IACjE,MAAMqF,SAAS,GAAGjB,UAAU,CAACP,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACZ,SAAS,KAAKmG,aAAa,CAAC;IACrE,OAAOhE,SAAS,EAAE/B,KAAK,IAAI,EAAE;EAC/B;EACA;EACAgG,gBAAgBA,CAACD,aAAqB;IACpC,KAAK,MAAMvG,QAAQ,IAAI,IAAI,CAACzC,SAAS,EAAE;MACrC,MAAM+D,UAAU,GAAG,IAAI,CAAC9E,YAAY,CAACwD,QAAQ,CAAC,IAAI,EAAE;MACpD,MAAMuC,SAAS,GAAGjB,UAAU,CAACP,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACZ,SAAS,KAAKmG,aAAa,CAAC;MACrE,IAAIhE,SAAS,EAAE;QACb,OAAO;UACLnC,SAAS,EAAEmC,SAAS,CAACnC,SAAS;UAC9BI,KAAK,EAAE+B,SAAS,CAAC/B,KAAK,IAAI;SAC3B;MACH;IACF;IACA,OAAO;MAAEJ,SAAS,EAAEmG,aAAa;MAAE/F,KAAK,EAAE;IAAE,CAAE;EAChD;EAEA;EACAiG,oBAAoBA,CAAC/F,OAAe;IAClC,KAAK,MAAMV,QAAQ,IAAI,IAAI,CAACzC,SAAS,EAAE;MACrC,MAAM+D,UAAU,GAAG,IAAI,CAAC9E,YAAY,CAACwD,QAAQ,CAAC,IAAI,EAAE;MACpD,MAAMuC,SAAS,GAAGjB,UAAU,CAACP,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACN,OAAO,KAAKA,OAAO,CAAC;MAC7D,IAAI6B,SAAS,EAAE;QACb,OAAO;UACLnC,SAAS,EAAEmC,SAAS,CAACnC,SAAS;UAC9BI,KAAK,EAAE+B,SAAS,CAAC/B,KAAK,IAAI;SAC3B;MACH;IACF;IACA,OAAO;MAAEJ,SAAS,EAAE,MAAMM,OAAO,EAAE;MAAEF,KAAK,EAAE;IAAE,CAAE;EAClD;EAEA;EACAkG,kBAAkBA,CAAA;IAChB,IAAI,CAAC,IAAI,CAACvJ,UAAU,IAAI,CAAC,IAAI,CAACD,gBAAgB,IAAI,CAAC,IAAI,CAACV,YAAY,CAAC,IAAI,CAACU,gBAAgB,CAAC,EAAE;MAC3F,OAAO,KAAK;IACd;IAEA,MAAMyJ,QAAQ,GAAG,IAAI,CAACnK,YAAY,CAAC,IAAI,CAACU,gBAAgB,CAAC,CAACsE,MAAM,CAACR,CAAC,IAAG;MACnE,MAAMS,UAAU,GAAG,CAAC,IAAI,CAACrE,aAAa,IAAI4D,CAAC,CAACR,KAAK,KAAK,IAAI,CAACpD,aAAa;MACxE,MAAMsE,WAAW,GAAGV,CAAC,CAACZ,SAAS,CAACuB,WAAW,EAAE,CAACC,QAAQ,CAAC,IAAI,CAACzE,UAAU,CAACwE,WAAW,EAAE,CAAC;MACrF,OAAOF,UAAU,IAAIC,WAAW;IAClC,CAAC,CAAC;IAEF,OAAOiF,QAAQ,CAACxI,MAAM,KAAK,CAAC;EAC9B;EAEA;EACAyI,0BAA0BA,CAAA;IACxB,IAAI,CAAC,IAAI,CAAC1J,gBAAgB,IAAI,CAAC,IAAI,CAACV,YAAY,CAAC,IAAI,CAACU,gBAAgB,CAAC,EAAE;MACvE,OAAO,CAAC;IACV;IAEA,MAAMyJ,QAAQ,GAAG,IAAI,CAACnK,YAAY,CAAC,IAAI,CAACU,gBAAgB,CAAC,CAACsE,MAAM,CAACR,CAAC,IAAG;MACnE,MAAMS,UAAU,GAAG,CAAC,IAAI,CAACrE,aAAa,IAAI4D,CAAC,CAACR,KAAK,KAAK,IAAI,CAACpD,aAAa;MACxE,MAAMsE,WAAW,GAAG,CAAC,IAAI,CAACvE,UAAU,IAAI6D,CAAC,CAACZ,SAAS,CAACuB,WAAW,EAAE,CAACC,QAAQ,CAAC,IAAI,CAACzE,UAAU,CAACwE,WAAW,EAAE,CAAC;MACzG,OAAOF,UAAU,IAAIC,WAAW;IAClC,CAAC,CAAC;IAEF,OAAOiF,QAAQ,CAACxI,MAAM;EACxB;EAEA;EACA0I,oBAAoBA,CAACtE,SAAwB;IAC3C,OAAOA,SAAS,CAAC7B,OAAO,GAAG6B,SAAS,CAAC7B,OAAO,CAACoG,QAAQ,EAAE,GAAG,GAAGvE,SAAS,CAACnC,SAAS,IAAImC,SAAS,CAAC/B,KAAK,EAAE;EACvG;EACA;EACQgC,qBAAqBA,CAAC9B,OAAe;IAC3C,KAAK,MAAMV,QAAQ,IAAI,IAAI,CAACzC,SAAS,EAAE;MACrC,MAAM+D,UAAU,GAAG,IAAI,CAAC9E,YAAY,CAACwD,QAAQ,CAAC,IAAI,EAAE;MACpD,MAAMuC,SAAS,GAAGjB,UAAU,CAACP,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACN,OAAO,KAAKA,OAAO,CAAC;MAC7D,IAAI6B,SAAS,EAAE,OAAOA,SAAS;IACjC;IACA,OAAO,IAAI;EACb;EACA;EACQwE,qBAAqBA,CAAC3G,SAAiB;IAC7C,MAAM4G,kBAAkB,GAAqD,EAAE;IAE/E;IACA,KAAK,MAAMhH,QAAQ,IAAI,IAAI,CAACzC,SAAS,EAAE;MACrC,MAAM+D,UAAU,GAAG,IAAI,CAAC9E,YAAY,CAACwD,QAAQ,CAAC,IAAI,EAAE;MACpD,MAAMiH,OAAO,GAAG3F,UAAU,CAACE,MAAM,CAACR,CAAC,IAAIA,CAAC,CAACZ,SAAS,KAAKA,SAAS,CAAC;MACjE6G,OAAO,CAAClH,OAAO,CAACwC,SAAS,IAAG;QAC1ByE,kBAAkB,CAAC/F,IAAI,CAAC;UAAEjB,QAAQ;UAAEuC;QAAS,CAAE,CAAC;MAClD,CAAC,CAAC;IACJ;IAEAtE,OAAO,CAACC,GAAG,CAAC,iBAAiBkC,SAAS,QAAQ,EAAE4G,kBAAkB,CAAC;IAEnE,IAAIA,kBAAkB,CAAC7I,MAAM,KAAK,CAAC,EAAE;MACnCF,OAAO,CAACK,IAAI,CAAC,kBAAkB8B,SAAS,SAAS,CAAC;MAClD,OAAO,IAAI;IACb;IAEA,IAAI4G,kBAAkB,CAAC7I,MAAM,GAAG,CAAC,EAAE;MACjCF,OAAO,CAACK,IAAI,CAAC,aAAa8B,SAAS,IAAI,EAAE4G,kBAAkB,CAAC9G,GAAG,CAACgH,CAAC,IAAI,GAAGA,CAAC,CAAClH,QAAQ,IAAIkH,CAAC,CAAC3E,SAAS,CAAC/B,KAAK,EAAE,CAAC,CAAC;MAC3GvC,OAAO,CAACK,IAAI,CAAC,cAAc0I,kBAAkB,CAAC,CAAC,CAAC,CAAChH,QAAQ,IAAIgH,kBAAkB,CAAC,CAAC,CAAC,CAACzE,SAAS,CAAC/B,KAAK,EAAE,CAAC;IACvG;IAEA,MAAM2G,UAAU,GAAGH,kBAAkB,CAAC,CAAC,CAAC;IACxC,OAAOG,UAAU,CAAC5E,SAAS,CAAC7B,OAAO,IAAI,IAAI;EAC7C;EAEA;EACQyB,iBAAiBA,CAACzB,OAAe;IACvC,OAAO,IAAI,CAACrD,gBAAgB,CAACuE,QAAQ,CAAClB,OAAO,CAAC;EAChD;EAEA;EACQwB,iBAAiBA,CAACxB,OAAe;IACvC,OAAO,IAAI,CAAC9D,gBAAgB,CAACgF,QAAQ,CAAClB,OAAO,CAAC;EAChD;EAEA;EACA0G,wBAAwBA,CAACC,QAAgB;IACvC,KAAK,MAAMrH,QAAQ,IAAI,IAAI,CAACzC,SAAS,EAAE;MACrC,MAAM+D,UAAU,GAAG,IAAI,CAAC9E,YAAY,CAACwD,QAAQ,CAAC,IAAI,EAAE;MACpD,MAAMuC,SAAS,GAAGjB,UAAU,CAACP,IAAI,CAACC,CAAC,IAAI,IAAI,CAAC6F,oBAAoB,CAAC7F,CAAC,CAAC,KAAKqG,QAAQ,CAAC;MACjF,IAAI9E,SAAS,EAAE,OAAOA,SAAS;IACjC;IACA,OAAO,IAAI;EACb;EAEA;EACQlE,sBAAsBA,CAAC4F,UAAoB;IACjD,MAAMD,QAAQ,GAAa,EAAE;IAC7B,MAAMsD,iBAAiB,GAA2D,EAAE;IAEpFrD,UAAU,CAAClE,OAAO,CAACK,SAAS,IAAG;MAC7B,MAAM4G,kBAAkB,GAAqD,EAAE;MAE/E;MACA,KAAK,MAAMhH,QAAQ,IAAI,IAAI,CAACzC,SAAS,EAAE;QACrC,MAAM+D,UAAU,GAAG,IAAI,CAAC9E,YAAY,CAACwD,QAAQ,CAAC,IAAI,EAAE;QACpD,MAAMiH,OAAO,GAAG3F,UAAU,CAACE,MAAM,CAACR,CAAC,IAAIA,CAAC,CAACZ,SAAS,KAAKA,SAAS,IAAIY,CAAC,CAACN,OAAO,CAAC;QAC9EuG,OAAO,CAAClH,OAAO,CAACwC,SAAS,IAAG;UAC1ByE,kBAAkB,CAAC/F,IAAI,CAAC;YAAEjB,QAAQ;YAAEuC;UAAS,CAAE,CAAC;QAClD,CAAC,CAAC;MACJ;MAEA,IAAIyE,kBAAkB,CAAC7I,MAAM,KAAK,CAAC,EAAE;QACnCF,OAAO,CAACK,IAAI,CAAC,kBAAkB8B,SAAS,SAAS,CAAC;MACpD,CAAC,MAAM,IAAI4G,kBAAkB,CAAC7I,MAAM,GAAG,CAAC,EAAE;QACxC,MAAMZ,SAAS,GAAGyJ,kBAAkB,CAAC9G,GAAG,CAACgH,CAAC,IAAI,GAAGA,CAAC,CAAClH,QAAQ,IAAIkH,CAAC,CAAC3E,SAAS,CAAC/B,KAAK,EAAE,CAAC;QACnF8G,iBAAiB,CAACrG,IAAI,CAAC;UACrBwB,IAAI,EAAErC,SAAS;UACfmH,KAAK,EAAEP,kBAAkB,CAAC7I,MAAM;UAChCZ;SACD,CAAC;QACF;QACA,MAAM4J,UAAU,GAAGH,kBAAkB,CAAC,CAAC,CAAC;QACxC,IAAIG,UAAU,CAAC5E,SAAS,CAAC7B,OAAO,EAAE;UAChCsD,QAAQ,CAAC/C,IAAI,CAACkG,UAAU,CAAC5E,SAAS,CAAC7B,OAAO,CAAC;QAC7C;MACF,CAAC,MAAM;QACL,MAAM6B,SAAS,GAAGyE,kBAAkB,CAAC,CAAC,CAAC,CAACzE,SAAS;QACjD,IAAIA,SAAS,CAAC7B,OAAO,EAAE;UACrBsD,QAAQ,CAAC/C,IAAI,CAACsB,SAAS,CAAC7B,OAAO,CAAC;QAClC;MACF;IACF,CAAC,CAAC;IAEF;IACA,IAAI4G,iBAAiB,CAACnJ,MAAM,GAAG,CAAC,EAAE;MAChCF,OAAO,CAACuJ,KAAK,CAAC,uBAAuB,CAAC;MACtCF,iBAAiB,CAACvH,OAAO,CAAC0H,OAAO,IAAG;QAClCxJ,OAAO,CAACK,IAAI,CAAC,OAAOmJ,OAAO,CAAChF,IAAI,cAAcgF,OAAO,CAACF,KAAK,KAAK,EAAEE,OAAO,CAAClK,SAAS,CAAC;QACpFU,OAAO,CAACK,IAAI,CAAC,gCAAgC,CAAC;MAChD,CAAC,CAAC;MACFL,OAAO,CAACyJ,QAAQ,EAAE;IACpB;IAEA,OAAO1D,QAAQ;EACjB;EAEQzF,sBAAsBA,CAACyF,QAAkB;IAC/C,MAAMC,UAAU,GAAa,EAAE;IAE/BD,QAAQ,CAACjE,OAAO,CAACW,OAAO,IAAG;MACzB,MAAM6B,SAAS,GAAG,IAAI,CAACC,qBAAqB,CAAC9B,OAAO,CAAC;MACrD,IAAI6B,SAAS,EAAE;QACb0B,UAAU,CAAChD,IAAI,CAACsB,SAAS,CAACnC,SAAS,CAAC;MACtC,CAAC,MAAM;QACLnC,OAAO,CAACK,IAAI,CAAC,eAAeoC,OAAO,QAAQ,CAAC;MAC9C;IACF,CAAC,CAAC;IAEF,OAAOuD,UAAU;EACnB;EAEA;EACQ/C,6BAA6BA,CAAA;IACnC,MAAML,OAAO,GAAqC,EAAE;IAEpD,IAAI,CAACvD,kBAAkB,CAACyC,OAAO,CAACK,SAAS,IAAG;MAC1C,KAAK,MAAMJ,QAAQ,IAAI,IAAI,CAACzC,SAAS,EAAE;QACrC,MAAM+D,UAAU,GAAG,IAAI,CAAC9E,YAAY,CAACwD,QAAQ,CAAC,IAAI,EAAE;QACpD,MAAMc,IAAI,GAAGQ,UAAU,CAACP,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACZ,SAAS,KAAKA,SAAS,CAAC;QAC5D,IAAIU,IAAI,EAAE;UACR,IAAI,CAACD,OAAO,CAACb,QAAQ,CAAC,EAAEa,OAAO,CAACb,QAAQ,CAAC,GAAG,EAAE;UAC9Ca,OAAO,CAACb,QAAQ,CAAC,CAACiB,IAAI,CAACb,SAAS,CAAC;UACjC,MAAM,CAAC;QACT;MACF;IACF,CAAC,CAAC;IAEF,IAAI,CAACzC,uBAAuB,GAAGkD,OAAO;EACxC;EAEA;EACQ8G,mBAAmBA,CAACvH,SAAiB;IAC3C,OAAO,IAAI,CAAC9C,kBAAkB,CAACsE,QAAQ,CAACxB,SAAS,CAAC;EACpD;EAEA;EACAsC,wBAAwBA,CAAA;IACtB,OAAO,IAAI,CAAC7F,gBAAgB,GAAG,IAAI,CAACS,kBAAkB,CAACa,MAAM,GAAG,IAAI,CAACd,gBAAgB,CAACc,MAAM;EAC9F;EAEA;EACA2G,kBAAkBA,CAAA;IAChB,MAAM/B,YAAY,GAAG,IAAI,CAACL,wBAAwB,EAAE;IACpD,OAAO,CAAC,IAAI,CAACrG,aAAa,IAAI0G,YAAY,GAAG,IAAI,CAAC1G,aAAa;EACjE;CACD;AAzvBkDuL,UAAA,EAAhD/L,SAAS,CAAC,iBAAiB,EAAE;EAAEgM,MAAM,EAAE;AAAK,CAAE,CAAC,C,iEAAoC;AAE3ED,UAAA,EAARnM,KAAK,EAAE,C,6DAA+B;AAC9BmM,UAAA,EAARnM,KAAK,EAAE,C,+DAAqC;AACpCmM,UAAA,EAARnM,KAAK,EAAE,C,0DAA2B;AAC1BmM,UAAA,EAARnM,KAAK,EAAE,C,6DAAmC;AAClCmM,UAAA,EAARnM,KAAK,EAAE,C,8DAAiC;AAAUmM,UAAA,EAARnM,KAAK,EAAE,C,kEAAkC;AAC3EmM,UAAA,EAARnM,KAAK,EAAE,C,6DAA6B;AAC5BmM,UAAA,EAARnM,KAAK,EAAE,C,kEAAkC;AAAWmM,UAAA,EAARnM,KAAK,EAAE,C,kEAAiC;AAC5EmM,UAAA,EAARnM,KAAK,EAAE,C,kEAAmC;AAEjCmM,UAAA,EAATlM,MAAM,EAAE,C,iEAAuD;AACtDkM,UAAA,EAATlM,MAAM,EAAE,C,+DAA8C;AAC7CkM,UAAA,EAATlM,MAAM,EAAE,C,iEAAgD;AAd9CK,yBAAyB,GAAA6L,UAAA,EAZrCpM,SAAS,CAAC;EACTsM,QAAQ,EAAE,uBAAuB;EACjCC,WAAW,EAAE,oCAAoC;EACjDC,SAAS,EAAE,CAAC,oCAAoC,CAAC;EACjDC,SAAS,EAAE,CACT;IACEC,OAAO,EAAEpM,iBAAiB;IAC1BqM,WAAW,EAAEvM,UAAU,CAAC,MAAMG,yBAAyB,CAAC;IACxDqM,KAAK,EAAE;GACR;CAEJ,CAAC,C,EACWrM,yBAAyB,CA0vBrC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}