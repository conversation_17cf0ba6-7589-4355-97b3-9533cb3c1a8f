{"ast": null, "code": "import toDate from \"../toDate/index.js\";\nimport differenceInCalendarMonths from \"../differenceInCalendarMonths/index.js\";\nimport compareAsc from \"../compareAsc/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\nimport isLastDayOfMonth from \"../isLastDayOfMonth/index.js\";\n/**\n * @name differenceInMonths\n * @category Month Helpers\n * @summary Get the number of full months between the given dates.\n *\n * @description\n * Get the number of full months between the given dates using trunc as a default rounding method.\n *\n * @param {Date|Number} dateLeft - the later date\n * @param {Date|Number} dateRight - the earlier date\n * @returns {Number} the number of full months\n * @throws {TypeError} 2 arguments required\n *\n * @example\n * // How many full months are between 31 January 2014 and 1 September 2014?\n * const result = differenceInMonths(new Date(2014, 8, 1), new Date(2014, 0, 31))\n * //=> 7\n */\nexport default function differenceInMonths(dirtyDateLeft, dirtyDateRight) {\n  requiredArgs(2, arguments);\n  var dateLeft = toDate(dirtyDateLeft);\n  var dateRight = toDate(dirtyDateRight);\n  var sign = compareAsc(dateLeft, dateRight);\n  var difference = Math.abs(differenceInCalendarMonths(dateLeft, dateRight));\n  var result;\n\n  // Check for the difference of less than month\n  if (difference < 1) {\n    result = 0;\n  } else {\n    if (dateLeft.getMonth() === 1 && dateLeft.getDate() > 27) {\n      // This will check if the date is end of Feb and assign a higher end of month date\n      // to compare it with Jan\n      dateLeft.setDate(30);\n    }\n    dateLeft.setMonth(dateLeft.getMonth() - sign * difference);\n\n    // Math.abs(diff in full months - diff in calendar months) === 1 if last calendar month is not full\n    // If so, result must be decreased by 1 in absolute value\n    var isLastMonthNotFull = compareAsc(dateLeft, dateRight) === -sign;\n\n    // Check for cases of one full calendar month\n    if (isLastDayOfMonth(toDate(dirtyDateLeft)) && difference === 1 && compareAsc(dirtyDateLeft, dateRight) === 1) {\n      isLastMonthNotFull = false;\n    }\n    result = sign * (difference - Number(isLastMonthNotFull));\n  }\n\n  // Prevent negative zero\n  return result === 0 ? 0 : result;\n}", "map": {"version": 3, "names": ["toDate", "differenceInCalendarMonths", "compareAsc", "requiredArgs", "isLastDayOfMonth", "differenceInMonths", "dirtyDateLeft", "dirtyDateRight", "arguments", "dateLeft", "dateRight", "sign", "difference", "Math", "abs", "result", "getMonth", "getDate", "setDate", "setMonth", "isLastMonthNotFull", "Number"], "sources": ["C:/Users/<USER>/Documents/jeansalechange/adminFront/node_modules/date-fns/esm/differenceInMonths/index.js"], "sourcesContent": ["import toDate from \"../toDate/index.js\";\nimport differenceInCalendarMonths from \"../differenceInCalendarMonths/index.js\";\nimport compareAsc from \"../compareAsc/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\nimport isLastDayOfMonth from \"../isLastDayOfMonth/index.js\";\n/**\n * @name differenceInMonths\n * @category Month Helpers\n * @summary Get the number of full months between the given dates.\n *\n * @description\n * Get the number of full months between the given dates using trunc as a default rounding method.\n *\n * @param {Date|Number} dateLeft - the later date\n * @param {Date|Number} dateRight - the earlier date\n * @returns {Number} the number of full months\n * @throws {TypeError} 2 arguments required\n *\n * @example\n * // How many full months are between 31 January 2014 and 1 September 2014?\n * const result = differenceInMonths(new Date(2014, 8, 1), new Date(2014, 0, 31))\n * //=> 7\n */\nexport default function differenceInMonths(dirtyDateLeft, dirtyDateRight) {\n  requiredArgs(2, arguments);\n  var dateLeft = toDate(dirtyDateLeft);\n  var dateRight = toDate(dirtyDateRight);\n  var sign = compareAsc(dateLeft, dateRight);\n  var difference = Math.abs(differenceInCalendarMonths(dateLeft, dateRight));\n  var result;\n\n  // Check for the difference of less than month\n  if (difference < 1) {\n    result = 0;\n  } else {\n    if (dateLeft.getMonth() === 1 && dateLeft.getDate() > 27) {\n      // This will check if the date is end of Feb and assign a higher end of month date\n      // to compare it with Jan\n      dateLeft.setDate(30);\n    }\n    dateLeft.setMonth(dateLeft.getMonth() - sign * difference);\n\n    // Math.abs(diff in full months - diff in calendar months) === 1 if last calendar month is not full\n    // If so, result must be decreased by 1 in absolute value\n    var isLastMonthNotFull = compareAsc(dateLeft, dateRight) === -sign;\n\n    // Check for cases of one full calendar month\n    if (isLastDayOfMonth(toDate(dirtyDateLeft)) && difference === 1 && compareAsc(dirtyDateLeft, dateRight) === 1) {\n      isLastMonthNotFull = false;\n    }\n    result = sign * (difference - Number(isLastMonthNotFull));\n  }\n\n  // Prevent negative zero\n  return result === 0 ? 0 : result;\n}"], "mappings": "AAAA,OAAOA,MAAM,MAAM,oBAAoB;AACvC,OAAOC,0BAA0B,MAAM,wCAAwC;AAC/E,OAAOC,UAAU,MAAM,wBAAwB;AAC/C,OAAOC,YAAY,MAAM,+BAA+B;AACxD,OAAOC,gBAAgB,MAAM,8BAA8B;AAC3D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,SAASC,kBAAkBA,CAACC,aAAa,EAAEC,cAAc,EAAE;EACxEJ,YAAY,CAAC,CAAC,EAAEK,SAAS,CAAC;EAC1B,IAAIC,QAAQ,GAAGT,MAAM,CAACM,aAAa,CAAC;EACpC,IAAII,SAAS,GAAGV,MAAM,CAACO,cAAc,CAAC;EACtC,IAAII,IAAI,GAAGT,UAAU,CAACO,QAAQ,EAAEC,SAAS,CAAC;EAC1C,IAAIE,UAAU,GAAGC,IAAI,CAACC,GAAG,CAACb,0BAA0B,CAACQ,QAAQ,EAAEC,SAAS,CAAC,CAAC;EAC1E,IAAIK,MAAM;;EAEV;EACA,IAAIH,UAAU,GAAG,CAAC,EAAE;IAClBG,MAAM,GAAG,CAAC;EACZ,CAAC,MAAM;IACL,IAAIN,QAAQ,CAACO,QAAQ,CAAC,CAAC,KAAK,CAAC,IAAIP,QAAQ,CAACQ,OAAO,CAAC,CAAC,GAAG,EAAE,EAAE;MACxD;MACA;MACAR,QAAQ,CAACS,OAAO,CAAC,EAAE,CAAC;IACtB;IACAT,QAAQ,CAACU,QAAQ,CAACV,QAAQ,CAACO,QAAQ,CAAC,CAAC,GAAGL,IAAI,GAAGC,UAAU,CAAC;;IAE1D;IACA;IACA,IAAIQ,kBAAkB,GAAGlB,UAAU,CAACO,QAAQ,EAAEC,SAAS,CAAC,KAAK,CAACC,IAAI;;IAElE;IACA,IAAIP,gBAAgB,CAACJ,MAAM,CAACM,aAAa,CAAC,CAAC,IAAIM,UAAU,KAAK,CAAC,IAAIV,UAAU,CAACI,aAAa,EAAEI,SAAS,CAAC,KAAK,CAAC,EAAE;MAC7GU,kBAAkB,GAAG,KAAK;IAC5B;IACAL,MAAM,GAAGJ,IAAI,IAAIC,UAAU,GAAGS,MAAM,CAACD,kBAAkB,CAAC,CAAC;EAC3D;;EAEA;EACA,OAAOL,MAAM,KAAK,CAAC,GAAG,CAAC,GAAGA,MAAM;AAClC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}