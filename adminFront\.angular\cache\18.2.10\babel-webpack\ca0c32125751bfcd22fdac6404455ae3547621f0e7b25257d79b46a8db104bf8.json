{"ast": null, "code": "import { SharedModule } from '../../../components/shared.module';\nimport { CommonModule } from '@angular/common';\nimport { NbCheckboxModule } from '@nebular/theme';\nimport { BaseComponent } from '../../../components/base/baseComponent';\nimport { tap } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/shared/helper/allowHelper\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"src/app/shared/services/message.service\";\nimport * as i4 from \"src/services/api/services\";\nimport * as i5 from \"src/app/shared/services/utility.service\";\nimport * as i6 from \"src/app/shared/helper/validationHelper\";\nimport * as i7 from \"@angular/common\";\nimport * as i8 from \"@angular/forms\";\nimport * as i9 from \"@nebular/theme\";\nimport * as i10 from \"../../../components/breadcrumb/breadcrumb.component\";\nimport * as i11 from \"../../../../@theme/pipes/base-file.pipe\";\nfunction DetailContentManagementLandownerComponent_ng_container_9_tr_17_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 32)(2, \"label\");\n    i0.ɵɵtext(3, \"\\u6587\\u4EF6\\u540D \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(4, \" \\u00A0 \");\n    i0.ɵɵelementStart(5, \"input\", 33);\n    i0.ɵɵlistener(\"blur\", function DetailContentManagementLandownerComponent_ng_container_9_tr_17_Template_input_blur_5_listener($event) {\n      const i_r6 = i0.ɵɵrestoreView(_r5).index;\n      const formItemReq_r2 = i0.ɵɵnextContext().$implicit;\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.renameFile($event, i_r6, formItemReq_r2));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"td\", 34);\n    i0.ɵɵelement(7, \"img\", 35);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"td\", 36)(9, \"button\", 37);\n    i0.ɵɵlistener(\"click\", function DetailContentManagementLandownerComponent_ng_container_9_tr_17_Template_button_click_9_listener() {\n      const picture_r7 = i0.ɵɵrestoreView(_r5).$implicit;\n      const formItemReq_r2 = i0.ɵɵnextContext().$implicit;\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.removeImage(picture_r7.id, formItemReq_r2));\n    });\n    i0.ɵɵtext(10, \"\\u5220\\u9664\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const picture_r7 = ctx.$implicit;\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"value\", picture_r7.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", picture_r7.data, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction DetailContentManagementLandownerComponent_ng_container_9_div_18_img_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 42);\n    i0.ɵɵpipe(1, \"addBaseFile\");\n  }\n  if (rf & 2) {\n    const formItemReq_r2 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵproperty(\"src\", i0.ɵɵpipeBind1(1, 1, formItemReq_r2.CDesignFileUrl), i0.ɵɵsanitizeUrl);\n  }\n}\nfunction DetailContentManagementLandownerComponent_ng_container_9_div_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 38)(1, \"div\", 20)(2, \"table\", 39)(3, \"tbody\")(4, \"tr\");\n    i0.ɵɵelement(5, \"td\", 32);\n    i0.ɵɵelementStart(6, \"td\", 40);\n    i0.ɵɵtemplate(7, DetailContentManagementLandownerComponent_ng_container_9_div_18_img_7_Template, 2, 3, \"img\", 41);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(8, \"td\", 36);\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const formItemReq_r2 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵproperty(\"ngClass\", formItemReq_r2.listPictures.length ? \"hidden\" : \"\");\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngIf\", formItemReq_r2.CDesignFileUrl);\n  }\n}\nfunction DetailContentManagementLandownerComponent_ng_container_9_nb_option_30_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 43);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const case_r8 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", case_r8);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", case_r8.label, \" \");\n  }\n}\nfunction DetailContentManagementLandownerComponent_ng_container_9_label_36_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"label\", 44)(1, \"nb-checkbox\", 45);\n    i0.ɵɵtwoWayListener(\"checkedChange\", function DetailContentManagementLandownerComponent_ng_container_9_label_36_Template_nb_checkbox_checkedChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r9);\n      const formItemReq_r2 = i0.ɵɵnextContext().$implicit;\n      i0.ɵɵtwoWayBindingSet(formItemReq_r2.allSelected, $event) || (formItemReq_r2.allSelected = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"checkedChange\", function DetailContentManagementLandownerComponent_ng_container_9_label_36_Template_nb_checkbox_checkedChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r9);\n      const formItemReq_r2 = i0.ɵɵnextContext().$implicit;\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.onCheckAllChange($event, formItemReq_r2));\n    });\n    i0.ɵɵtext(2, \" \\u5168\\u9078 \");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const formItemReq_r2 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"checked\", formItemReq_r2.allSelected);\n  }\n}\nfunction DetailContentManagementLandownerComponent_ng_container_9_label_37_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"label\", 44)(1, \"nb-checkbox\", 46);\n    i0.ɵɵtwoWayListener(\"checkedChange\", function DetailContentManagementLandownerComponent_ng_container_9_label_37_Template_nb_checkbox_checkedChange_1_listener($event) {\n      const item_r11 = i0.ɵɵrestoreView(_r10).$implicit;\n      const formItemReq_r2 = i0.ɵɵnextContext().$implicit;\n      i0.ɵɵtwoWayBindingSet(formItemReq_r2.selectedItems[item_r11], $event) || (formItemReq_r2.selectedItems[item_r11] = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"checkedChange\", function DetailContentManagementLandownerComponent_ng_container_9_label_37_Template_nb_checkbox_checkedChange_1_listener($event) {\n      const item_r11 = i0.ɵɵrestoreView(_r10).$implicit;\n      const formItemReq_r2 = i0.ɵɵnextContext().$implicit;\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.onCheckboxHouseHoldListChange($event, item_r11, formItemReq_r2));\n    });\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const item_r11 = ctx.$implicit;\n    const formItemReq_r2 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"checked\", formItemReq_r2.selectedItems[item_r11]);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", item_r11, \" \");\n  }\n}\nfunction DetailContentManagementLandownerComponent_ng_container_9_div_38_label_4_nb_checkbox_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-checkbox\", 46);\n    i0.ɵɵtwoWayListener(\"checkedChange\", function DetailContentManagementLandownerComponent_ng_container_9_div_38_label_4_nb_checkbox_1_Template_nb_checkbox_checkedChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r12);\n      const remark_r13 = i0.ɵɵnextContext().$implicit;\n      const formItemReq_r2 = i0.ɵɵnextContext(2).$implicit;\n      i0.ɵɵtwoWayBindingSet(formItemReq_r2.selectedRemarkType[remark_r13], $event) || (formItemReq_r2.selectedRemarkType[remark_r13] = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"checkedChange\", function DetailContentManagementLandownerComponent_ng_container_9_div_38_label_4_nb_checkbox_1_Template_nb_checkbox_checkedChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r12);\n      const remark_r13 = i0.ɵɵnextContext().$implicit;\n      const formItemReq_r2 = i0.ɵɵnextContext(2).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.onCheckboxRemarkChange($event, remark_r13, formItemReq_r2));\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const remark_r13 = i0.ɵɵnextContext().$implicit;\n    const formItemReq_r2 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵtwoWayProperty(\"checked\", formItemReq_r2.selectedRemarkType[remark_r13]);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", remark_r13, \" \");\n  }\n}\nfunction DetailContentManagementLandownerComponent_ng_container_9_div_38_label_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"label\", 44);\n    i0.ɵɵtemplate(1, DetailContentManagementLandownerComponent_ng_container_9_div_38_label_4_nb_checkbox_1_Template, 2, 2, \"nb-checkbox\", 47);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const formItemReq_r2 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", formItemReq_r2.selectedRemarkType);\n  }\n}\nfunction DetailContentManagementLandownerComponent_ng_container_9_div_38_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 4)(1, \"div\", 11)(2, \"label\", 28);\n    i0.ɵɵtext(3, \"\\u5099\\u8A3B\\u9078\\u9805\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, DetailContentManagementLandownerComponent_ng_container_9_div_38_label_4_Template, 2, 1, \"label\", 30);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.CRemarkTypeOptions);\n  }\n}\nfunction DetailContentManagementLandownerComponent_ng_container_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 10)(2, \"div\", 11)(3, \"label\", 12);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 13)(6, \"input\", 14);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function DetailContentManagementLandownerComponent_ng_container_9_Template_input_ngModelChange_6_listener($event) {\n      const formItemReq_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      i0.ɵɵtwoWayBindingSet(formItemReq_r2.CItemName, $event) || (formItemReq_r2.CItemName = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(7, \"div\", 15)(8, \"div\", 16)(9, \"button\", 17);\n    i0.ɵɵlistener(\"click\", function DetailContentManagementLandownerComponent_ng_container_9_Template_button_click_9_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const inputFile_r3 = i0.ɵɵreference(13);\n      return i0.ɵɵresetView(inputFile_r3.click());\n    });\n    i0.ɵɵtext(10, \"\\u4E0A\\u50B3\\u6982\\u5FF5\\u8A2D\\u8A08\\u5716\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(11, \"div\", 18)(12, \"input\", 19, 0);\n    i0.ɵɵlistener(\"change\", function DetailContentManagementLandownerComponent_ng_container_9_Template_input_change_12_listener($event) {\n      const formItemReq_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.detectFiles($event, formItemReq_r2));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"div\", 20)(15, \"table\", 21)(16, \"tbody\");\n    i0.ɵɵtemplate(17, DetailContentManagementLandownerComponent_ng_container_9_tr_17_Template, 11, 2, \"tr\", 6);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵtemplate(18, DetailContentManagementLandownerComponent_ng_container_9_div_18_Template, 9, 2, \"div\", 22);\n    i0.ɵɵelementStart(19, \"div\", 10)(20, \"div\", 11)(21, \"label\", 23);\n    i0.ɵɵtext(22, \"\\u5FC5\\u586B\\u6578\\u91CF\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"input\", 24);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function DetailContentManagementLandownerComponent_ng_container_9_Template_input_ngModelChange_23_listener($event) {\n      const formItemReq_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      i0.ɵɵtwoWayBindingSet(formItemReq_r2.CRequireAnswer, $event) || (formItemReq_r2.CRequireAnswer = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelement(24, \"div\", 15);\n    i0.ɵɵelementStart(25, \"div\", 10)(26, \"div\", 11)(27, \"label\", 25);\n    i0.ɵɵtext(28, \"\\u524D\\u53F0UI\\u985E\\u578B\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(29, \"nb-select\", 26);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function DetailContentManagementLandownerComponent_ng_container_9_Template_nb_select_ngModelChange_29_listener($event) {\n      const formItemReq_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      i0.ɵɵtwoWayBindingSet(formItemReq_r2.selectedCUiType, $event) || (formItemReq_r2.selectedCUiType = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"selectedChange\", function DetailContentManagementLandownerComponent_ng_container_9_Template_nb_select_selectedChange_29_listener() {\n      const formItemReq_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.changeSelectCUiType(formItemReq_r2));\n    });\n    i0.ɵɵtemplate(30, DetailContentManagementLandownerComponent_ng_container_9_nb_option_30_Template, 2, 2, \"nb-option\", 27);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelement(31, \"div\", 15);\n    i0.ɵɵelementStart(32, \"div\", 4)(33, \"div\", 11)(34, \"label\", 28);\n    i0.ɵɵtext(35, \"\\u9069\\u7528\\u6236\\u5225 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(36, DetailContentManagementLandownerComponent_ng_container_9_label_36_Template, 3, 1, \"label\", 29)(37, DetailContentManagementLandownerComponent_ng_container_9_label_37_Template, 3, 2, \"label\", 30);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(38, DetailContentManagementLandownerComponent_ng_container_9_div_38_Template, 5, 1, \"div\", 31);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const formItemReq_r2 = ctx.$implicit;\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate3(\" \", formItemReq_r2.CName, \"-\", formItemReq_r2.CPart, \"-\", formItemReq_r2.CLocation, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtwoWayProperty(\"ngModel\", formItemReq_r2.CItemName);\n    i0.ɵɵproperty(\"placeholder\", formItemReq_r2.CItemName)(\"disabled\", ctx_r3.listFormItem.CIsLock);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"disabled\", ctx_r3.listFormItem.CIsLock);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", formItemReq_r2.listPictures.length ? \"\" : \"hidden\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r3.listFormItem.CIsLock);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngForOf\", formItemReq_r2.listPictures);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", formItemReq_r2.CDesignFileUrl);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtwoWayProperty(\"ngModel\", formItemReq_r2.CRequireAnswer);\n    i0.ɵɵproperty(\"disabled\", formItemReq_r2.selectedCUiType.value === 3);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtwoWayProperty(\"ngModel\", formItemReq_r2.selectedCUiType);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.CUiTypeOptions);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.houseHoldList.length);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.houseHoldList);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", formItemReq_r2.selectedCUiType.value === 3);\n  }\n}\nexport class DetailContentManagementLandownerComponent extends BaseComponent {\n  constructor(_allow, route, message, _formItemService, _regularNoticeFileService, _utilityService, valid, location, _materialService) {\n    super(_allow);\n    this._allow = _allow;\n    this.route = route;\n    this.message = message;\n    this._formItemService = _formItemService;\n    this._regularNoticeFileService = _regularNoticeFileService;\n    this._utilityService = _utilityService;\n    this.valid = valid;\n    this.location = location;\n    this._materialService = _materialService;\n    this.typeContentManagementLandowner = {\n      CFormType: 1,\n      CNoticeType: 1\n    };\n    this.CUiTypeOptions = [{\n      value: 1,\n      label: '建材選色'\n    }, {\n      value: 2,\n      label: '群組選樣_選色'\n    }, {\n      value: 3,\n      label: '建材選樣'\n    }];\n    this.CRemarkTypeOptions = [\"正常\", \"留料\"];\n    this.selectedItems = {};\n    this.selectedRemarkType = {};\n    this.isNew = true;\n  }\n  ngOnInit() {\n    this.route.paramMap.subscribe(params => {\n      if (params) {\n        const idParam = params.get('id');\n        const id = idParam ? +idParam : 0;\n        this.buildCaseId = id;\n        if (this.buildCaseId) {\n          this.getListRegularNoticeFileHouseHold();\n        }\n      }\n    });\n  }\n  getItemByValue(value, options) {\n    for (const item of options) {\n      if (item.value === value) {\n        return item;\n      }\n    }\n    return null;\n  }\n  onCheckAllChange(checked, formItemReq_) {\n    formItemReq_.allSelected = checked;\n    this.houseHoldList.forEach(item => {\n      formItemReq_.selectedItems[item] = checked;\n    });\n  }\n  detectFiles(event, formItemReq_) {\n    const file = event.target.files[0];\n    if (file) {\n      let reader = new FileReader();\n      reader.readAsDataURL(file);\n      reader.onload = () => {\n        let base64Str = reader.result;\n        if (!base64Str) {\n          return;\n        }\n        if (formItemReq_.listPictures.length > 0) {\n          formItemReq_.listPictures[0] = {\n            id: new Date().getTime(),\n            name: file.name.split('.')[0],\n            data: base64Str,\n            extension: this._utilityService.getFileExtension(file.name),\n            CFile: file\n          };\n        } else {\n          formItemReq_.listPictures.push({\n            id: new Date().getTime(),\n            name: file.name.split('.')[0],\n            data: base64Str,\n            extension: this._utilityService.getFileExtension(file.name),\n            CFile: file\n          });\n        }\n        event.target.value = null;\n      };\n    }\n  }\n  removeImage(pictureId, formItemReq_) {\n    if (formItemReq_.listPictures.length) {\n      formItemReq_.listPictures = formItemReq_.listPictures.filter(x => x.id != pictureId);\n    }\n  }\n  renameFile(event, index, formItemReq_) {\n    var blob = formItemReq_.listPictures[index].CFile.slice(0, formItemReq_.listPictures[index].CFile.size, formItemReq_.listPictures[index].CFile.type);\n    var newFile = new File([blob], `${event.target.value + '.' + formItemReq_.listPictures[index].extension}`, {\n      type: formItemReq_.listPictures[index].CFile.type\n    });\n    formItemReq_.listPictures[index].CFile = newFile;\n  }\n  onCheckboxHouseHoldListChange(checked, item, formItemReq_) {\n    if (checked) {\n      formItemReq_.selectedItems[item] = checked;\n      formItemReq_.allSelected = this.houseHoldList.every(item => formItemReq_.selectedItems[item] == checked);\n    } else {\n      formItemReq_.allSelected = false;\n    }\n  }\n  onCheckboxRemarkChange(checked, item, formItemReq_) {\n    formItemReq_.selectedRemarkType[item] = checked;\n  }\n  createRemarkObject(CRemarkTypeOptions, CRemarkType) {\n    const remarkObject = {};\n    for (const option of CRemarkTypeOptions) {\n      remarkObject[option] = false;\n    }\n    const remarkTypes = CRemarkType.split('-');\n    for (const type of remarkTypes) {\n      if (CRemarkTypeOptions.includes(type)) {\n        remarkObject[type] = true;\n      }\n    }\n    return remarkObject;\n  }\n  getMaterialList() {\n    this._materialService.apiMaterialGetMaterialListPost$Json({\n      body: {\n        CBuildCaseId: this.buildCaseId,\n        CPagi: false\n      }\n    }).pipe(tap(res => {\n      if (res.Entries && res.StatusCode == 0) {\n        this.houseHoldList.forEach(item => this.selectedItems[item] = false);\n        this.CRemarkTypeOptions.forEach(item => this.selectedRemarkType[item] = false);\n        this.arrListFormItemReq = res.Entries.map(o => {\n          return {\n            CDesignFileUrl: null,\n            CFormItemHouseHold: null,\n            CFormId: null,\n            CLocation: o.CLocation,\n            CName: o.CName,\n            CPart: o.CPart,\n            CItemName: `${o.CName}-${o.CPart}-${o.CLocation}`,\n            CRemarkType: null,\n            CTotalAnswer: 0,\n            CRequireAnswer: undefined,\n            CUiType: 0,\n            selectedItems: {},\n            selectedRemarkType: this.selectedRemarkType,\n            allSelected: false,\n            listPictures: [],\n            selectedCUiType: this.CUiTypeOptions[0]\n          };\n        });\n        this.arrListFormItemReq = [...this.mergeItems(this.arrListFormItemReq)];\n      }\n    })).subscribe();\n  }\n  getListFormItem() {\n    this._formItemService.apiFormItemGetListFormItemPost$Json({\n      body: {\n        CBuildCaseId: this.buildCaseId,\n        CFormType: this.typeContentManagementLandowner.CFormType,\n        CIsPaging: false\n      }\n    }).pipe(tap(res => {\n      if (res.Entries && res.StatusCode == 0) {\n        this.listFormItem = res.Entries;\n        this.isNew = res.Entries.formItems ? false : true;\n        if (res.Entries.formItems) {\n          this.houseHoldList.forEach(item => this.selectedItems[item] = false);\n          this.CRemarkTypeOptions.forEach(item => this.selectedRemarkType[item] = false);\n          this.arrListFormItemReq = res.Entries.formItems.map(o => {\n            return {\n              CFormId: this.listFormItem.CFormId,\n              CDesignFileUrl: o.CDesignFileUrl,\n              CFile: o.CFile,\n              CFormItemHouseHold: o.CFormItemHouseHold,\n              CFormItemId: o.CFormItemId,\n              CLocation: o.CLocation,\n              CName: o.CName,\n              CPart: o.CPart,\n              CItemName: o.CItemName ? o.CItemName : `${o.CName}-${o.CPart}-${o.CLocation}`,\n              CRemarkType: o.CRemarkType,\n              CTotalAnswer: o.CTotalAnswer,\n              CRequireAnswer: o.CUiType === 3 ? 1 : o.CRequireAnswer,\n              CUiType: o.CUiType,\n              selectedItems: o.tblFormItemHouseholds.length ? this.createArrayObjectFromArray(this.houseHoldList, o.tblFormItemHouseholds) : {\n                ...this.selectedItems\n              },\n              selectedRemarkType: o.CRemarkType ? this.createRemarkObject(this.CRemarkTypeOptions, o.CRemarkType) : {\n                ...this.selectedRemarkType\n              },\n              allSelected: o.tblFormItemHouseholds.length == this.houseHoldList.length,\n              listPictures: [],\n              selectedCUiType: o.CUiType ? this.getItemByValue(o.CUiType, this.CUiTypeOptions) : this.CUiTypeOptions[0]\n            };\n          });\n        } else {\n          this.getMaterialList();\n        }\n      }\n    })).subscribe();\n  }\n  changeSelectCUiType(formItemReq) {\n    if (formItemReq.selectedCUiType && formItemReq.selectedCUiType.value === 3) {\n      formItemReq.CRequireAnswer = 1;\n    }\n  }\n  getHouseHoldListByNoticeType(data) {\n    for (let item of data) {\n      if (item.CNoticeType === this.typeContentManagementLandowner.CNoticeType) {\n        return item.CHouseHoldList;\n      }\n    }\n    return [];\n  }\n  getKeysWithTrueValue(obj) {\n    return Object.keys(obj).filter(key => obj[key]);\n  }\n  getKeysWithTrueValueJoined(obj) {\n    return Object.keys(obj).filter(key => obj[key]).join('-');\n  }\n  getCRemarkType(selectedCUiType, selectedRemarkType) {\n    if (selectedCUiType && selectedCUiType.value == 3) {\n      return this.getKeysWithTrueValueJoined(selectedRemarkType);\n    }\n  }\n  getStringAfterComma(inputString) {\n    const parts = inputString.split(',');\n    if (parts.length > 1) {\n      return parts[1];\n    } else return \"\";\n  }\n  formatFile(listPictures) {\n    if (listPictures && listPictures.length > 0) {\n      return {\n        Base64String: this.getStringAfterComma(listPictures[0].data) || null,\n        FileExtension: listPictures[0].extension || null,\n        FileName: listPictures[0].CFile.name || listPictures[0].name || null\n      };\n    } else return undefined;\n  }\n  // formatParam() {\n  //   const result: SaveListFormItemReq[] = \n  //   })\n  //   return result\n  // }\n  mergeItems(items) {\n    const map = new Map();\n    items.forEach(item => {\n      const key = `${item.CLocation}_${item.CName}_${item.CPart}`;\n      if (map.has(key)) {\n        const existing = map.get(key);\n        existing.count += 1;\n      } else {\n        map.set(key, {\n          item: {\n            ...item\n          },\n          count: 1\n        });\n      }\n    });\n    return Array.from(map.values()).map(({\n      item,\n      count\n    }) => ({\n      ...item,\n      CTotalAnswer: count\n    }));\n  }\n  validation() {\n    this.valid.clear();\n    let hasInvalidCUiType = false;\n    let hasInvalidCRequireAnswer = false;\n    let hasInvalidItemName = false;\n    for (const item of this.saveListFormItemReq) {\n      if (!hasInvalidCUiType && !item.CUiType) {\n        hasInvalidCUiType = true;\n      }\n      if (!hasInvalidCRequireAnswer && !item.CRequireAnswer) {\n        hasInvalidCRequireAnswer = true;\n      }\n      if (item.CTotalAnswer && item.CRequireAnswer) {\n        if (item.CRequireAnswer > item.CTotalAnswer) {\n          this.valid.addErrorMessage('[必填數量]' + ' <= ' + item.CTotalAnswer + ` (${item.CItemName}) `);\n        }\n      }\n      if (!hasInvalidItemName && !item.CItemName) {\n        hasInvalidItemName = true;\n      }\n    }\n    if (hasInvalidCUiType) {\n      this.valid.addErrorMessage('[前台UI類型]' + ' 必填');\n    }\n    if (hasInvalidCRequireAnswer) {\n      this.valid.addErrorMessage('[必填數量]' + ' 必填且>0');\n    }\n    if (hasInvalidItemName) {\n      this.valid.addErrorMessage('[廚房-廚具-櫃體]' + ' 必填');\n    }\n  }\n  onSubmit() {\n    this.saveListFormItemReq = this.arrListFormItemReq.map(e => {\n      return {\n        CDesignFileUrl: e.CDesignFileUrl ? e.CDesignFileUrl : null,\n        CFile: e.listPictures ? this.formatFile(e.listPictures) : undefined,\n        CFormItemHouseHold: this.getKeysWithTrueValue(e.selectedItems),\n        CFormItemId: e.CFormItemId ? e.CFormItemId : null,\n        CFormID: this.isNew ? null : this.listFormItem.CFormId,\n        CName: e.CName,\n        CPart: e.CPart,\n        CLocation: e.CLocation,\n        CItemName: e.CItemName,\n        // ? e.CItemName : `${e.CName}-${e.CPart}-${e.CLocation}`,\n        CRemarkType: e.selectedCUiType.value !== 3 ? null : this.getCRemarkType(e.selectedCUiType, e.selectedRemarkType) || null,\n        CTotalAnswer: e.CTotalAnswer,\n        CRequireAnswer: e.CRequireAnswer,\n        CUiType: e.selectedCUiType.value\n      };\n    });\n    this.validation();\n    if (this.valid.errorMessages.length > 0) {\n      this.message.showErrorMSGs(this.valid.errorMessages);\n      return;\n    }\n    if (this.isNew) {\n      this.createListFormItem();\n    } else {\n      this.saveListFormItem();\n    }\n  }\n  saveListFormItem() {\n    this._formItemService.apiFormItemSaveListFormItemPost$Json({\n      body: this.saveListFormItemReq\n    }).subscribe(res => {\n      if (res.StatusCode == 0) {\n        this.message.showSucessMSG(\"執行成功\");\n        // this.getListFormItem()\n        this.goBack();\n      }\n    });\n  }\n  createListFormItem() {\n    this.creatListFormItem = {\n      CBuildCaseId: this.buildCaseId,\n      CFormItem: this.saveListFormItemReq || null,\n      CFormType: this.typeContentManagementLandowner.CFormType\n    };\n    this._formItemService.apiFormItemCreateListFormItemPost$Json({\n      body: this.creatListFormItem\n    }).subscribe(res => {\n      if (res.StatusCode == 0) {\n        this.message.showSucessMSG(\"執行成功\");\n        // this.getListFormItem()\n        this.goBack();\n      }\n    });\n  }\n  createArrayObjectFromArray(a, b) {\n    const c = {};\n    for (const item of a) {\n      const matchingItem = b.find(bItem => bItem.CHousehold === item && bItem.CIsSelect);\n      c[item] = !!matchingItem;\n    }\n    return c;\n  } //[\"House1\", \"House2\", \"House3\"] => [{CHousehold: \"House1\", CIsSelect: true,... }, ... ]\n  getListRegularNoticeFileHouseHold() {\n    this._regularNoticeFileService.apiRegularNoticeFileGetListRegularNoticeFileHouseHoldPost$Json({\n      body: this.buildCaseId\n    }).pipe(tap(res => {\n      if (res.Entries && res.StatusCode == 0) {\n        this.houseHoldList = this.getHouseHoldListByNoticeType(res.Entries);\n        this.getListFormItem();\n      }\n    })).subscribe();\n  }\n  goBack() {\n    this.location.back();\n  }\n  static {\n    this.ɵfac = function DetailContentManagementLandownerComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || DetailContentManagementLandownerComponent)(i0.ɵɵdirectiveInject(i1.AllowHelper), i0.ɵɵdirectiveInject(i2.ActivatedRoute), i0.ɵɵdirectiveInject(i3.MessageService), i0.ɵɵdirectiveInject(i4.FormItemService), i0.ɵɵdirectiveInject(i4.RegularNoticeFileService), i0.ɵɵdirectiveInject(i5.UtilityService), i0.ɵɵdirectiveInject(i6.ValidationHelper), i0.ɵɵdirectiveInject(i7.Location), i0.ɵɵdirectiveInject(i4.MaterialService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: DetailContentManagementLandownerComponent,\n      selectors: [[\"ngx-detail-content-management-landowner\"]],\n      standalone: true,\n      features: [i0.ɵɵInheritDefinitionFeature, i0.ɵɵStandaloneFeature],\n      decls: 15,\n      vars: 1,\n      consts: [[\"inputFile\", \"\"], [\"accent\", \"success\"], [1, \"font-bold\", \"text-[#818181]\"], [1, \"d-flex\", \"flex-wrap\"], [1, \"col-md-12\"], [1, \"font-bold\", \"text-lg\", \"px-3\", \"pb-3\"], [4, \"ngFor\", \"ngForOf\"], [1, \"d-flex\", \"justify-content-center\"], [1, \"btn\", \"btn-secondary\", \"mx-2\", 3, \"click\"], [1, \"btn\", \"btn-info\", 3, \"click\"], [1, \"col-md-9\"], [1, \"form-group\", \"d-flex\", \"align-items-center\", \"w-full\"], [\"for\", \"CItemName\", 1, \"label\", \"col-4\", \"text-base\"], [1, \"input-group\", \"items-center\", \"w-full\", \"col-8\", \"px-0\"], [\"type\", \"text\", \"nbInput\", \"\", 1, \"w-full\", \"col-12\", 3, \"ngModelChange\", \"ngModel\", \"placeholder\", \"disabled\"], [1, \"col-md-3\"], [1, \"d-flex\", \"justify-content-end\", \"w-full\"], [1, \"btn\", \"btn-info\", 3, \"click\", \"disabled\"], [1, \"col-md-12\", 3, \"ngClass\"], [\"type\", \"file\", \"accept\", \"image/png, image/gif, image/jpeg\", 1, \"hidden\", 3, \"change\", \"disabled\"], [1, \"mt-3\", \"w-full\", \"flex\", \"flex-col\"], [1, \"table\", \"table-striped\", \"border\", 2, \"background-color\", \"#f3f3f3\"], [\"class\", \"col-md-12 text-center\", 3, \"ngClass\", 4, \"ngIf\"], [\"for\", \"cRequireAnswer\", 1, \"label\", \"col-4\"], [\"type\", \"number\", \"nbInput\", \"\", \"placeholder\", \"\\u5FC5\\u586B\\u6578\\u91CF\", 1, \"col-8\", 3, \"ngModelChange\", \"ngModel\", \"disabled\"], [\"for\", \"buildingName\", 1, \"label\", \"col-4\"], [\"placeholder\", \"\\u5EFA\\u6848\", 1, \"col-8\", \"px-0\", 3, \"ngModelChange\", \"selectedChange\", \"ngModel\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"for\", \"buildingName\", 1, \"label\", \"col-3\"], [\"class\", \"mr-2\", 4, \"ngIf\"], [\"class\", \"mr-2\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"col-md-12\", 4, \"ngIf\"], [1, \"align-middle\"], [\"nbInput\", \"\", \"type\", \"text\", 1, \"w-100\", \"p-2\", \"text-[13px]\", 3, \"blur\", \"value\"], [1, \"w-[100px]\", \"h-auto\"], [1, \"fit-size\", 3, \"src\"], [1, \"text-center\", \"w-32\", \"align-middle\"], [1, \"btn\", \"btn-outline-danger\", \"btn-sm\", \"m-1\", 3, \"click\"], [1, \"col-md-12\", \"text-center\", 3, \"ngClass\"], [1, \"table\", \"table-striped\", \"border\"], [1, \"w-[80px]\", \"h-auto\"], [\"class\", \"w-14 h-14\", 3, \"src\", 4, \"ngIf\"], [1, \"w-14\", \"h-14\", 3, \"src\"], [3, \"value\"], [1, \"mr-2\"], [3, \"checkedChange\", \"checked\"], [\"value\", \"item\", 3, \"checkedChange\", \"checked\"], [\"value\", \"item\", 3, \"checked\", \"checkedChange\", 4, \"ngIf\"]],\n      template: function DetailContentManagementLandownerComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"nb-card\", 1)(1, \"nb-card-header\");\n          i0.ɵɵelement(2, \"ngx-breadcrumb\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(3, \"nb-card-body\");\n          i0.ɵɵelement(4, \"h1\", 2);\n          i0.ɵɵelementStart(5, \"div\", 3)(6, \"div\", 4)(7, \"h4\", 5);\n          i0.ɵɵtext(8, \"\\u985E\\u578B-\\u7368\\u7ACB\\u9078\\u6A23\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(9, DetailContentManagementLandownerComponent_ng_container_9_Template, 39, 18, \"ng-container\", 6);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(10, \"nb-card-footer\", 7)(11, \"button\", 8);\n          i0.ɵɵlistener(\"click\", function DetailContentManagementLandownerComponent_Template_button_click_11_listener() {\n            return ctx.goBack();\n          });\n          i0.ɵɵtext(12, \" \\u53D6\\u6D88 \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(13, \"button\", 9);\n          i0.ɵɵlistener(\"click\", function DetailContentManagementLandownerComponent_Template_button_click_13_listener() {\n            return ctx.onSubmit();\n          });\n          i0.ɵɵtext(14, \" \\u5132\\u5B58 \");\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(9);\n          i0.ɵɵproperty(\"ngForOf\", ctx.arrListFormItemReq);\n        }\n      },\n      dependencies: [CommonModule, i7.NgClass, i7.NgForOf, i7.NgIf, SharedModule, i8.DefaultValueAccessor, i8.NumberValueAccessor, i8.NgControlStatus, i8.NgModel, i9.NbCardComponent, i9.NbCardBodyComponent, i9.NbCardFooterComponent, i9.NbCardHeaderComponent, i9.NbCheckboxComponent, i9.NbInputDirective, i9.NbSelectComponent, i9.NbOptionComponent, i10.BreadcrumbComponent, i11.BaseFilePipe, NbCheckboxModule],\n      styles: [\"/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsImZpbGUiOiJkZXRhaWwtY29udGVudC1tYW5hZ2VtZW50LWxhbmRvd25lci5jb21wb25lbnQuc2NzcyJ9 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvcGFnZXMvc2VsZWN0aW9uLW1hbmFnZW1lbnQvY29udGVudC1tYW5hZ2VtZW50LWxhbmRvd25lci9kZXRhaWwtY29udGVudC1tYW5hZ2VtZW50LWxhbmRvd25lci9kZXRhaWwtY29udGVudC1tYW5hZ2VtZW50LWxhbmRvd25lci5jb21wb25lbnQuc2NzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiO0FBQ0Esd01BQXdNIiwic291cmNlUm9vdCI6IiJ9 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["SharedModule", "CommonModule", "NbCheckboxModule", "BaseComponent", "tap", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵlistener", "DetailContentManagementLandownerComponent_ng_container_9_tr_17_Template_input_blur_5_listener", "$event", "i_r6", "ɵɵrestoreView", "_r5", "index", "formItemReq_r2", "ɵɵnextContext", "$implicit", "ctx_r3", "ɵɵresetView", "renameFile", "ɵɵelement", "DetailContentManagementLandownerComponent_ng_container_9_tr_17_Template_button_click_9_listener", "picture_r7", "removeImage", "id", "ɵɵadvance", "ɵɵproperty", "name", "data", "ɵɵsanitizeUrl", "ɵɵpipeBind1", "CDesignFileUrl", "ɵɵtemplate", "DetailContentManagementLandownerComponent_ng_container_9_div_18_img_7_Template", "listPictures", "length", "case_r8", "ɵɵtextInterpolate1", "label", "ɵɵtwoWayListener", "DetailContentManagementLandownerComponent_ng_container_9_label_36_Template_nb_checkbox_checkedChange_1_listener", "_r9", "ɵɵtwoWayBindingSet", "allSelected", "onCheckAllChange", "ɵɵtwoWayProperty", "DetailContentManagementLandownerComponent_ng_container_9_label_37_Template_nb_checkbox_checkedChange_1_listener", "item_r11", "_r10", "selectedItems", "onCheckboxHouseHoldListChange", "DetailContentManagementLandownerComponent_ng_container_9_div_38_label_4_nb_checkbox_1_Template_nb_checkbox_checkedChange_0_listener", "_r12", "remark_r13", "selectedRemarkType", "onCheckboxRemarkChange", "DetailContentManagementLandownerComponent_ng_container_9_div_38_label_4_nb_checkbox_1_Template", "DetailContentManagementLandownerComponent_ng_container_9_div_38_label_4_Template", "CRemarkTypeOptions", "ɵɵelementContainerStart", "DetailContentManagementLandownerComponent_ng_container_9_Template_input_ngModelChange_6_listener", "_r1", "CItemName", "DetailContentManagementLandownerComponent_ng_container_9_Template_button_click_9_listener", "inputFile_r3", "ɵɵreference", "click", "DetailContentManagementLandownerComponent_ng_container_9_Template_input_change_12_listener", "detectFiles", "DetailContentManagementLandownerComponent_ng_container_9_tr_17_Template", "DetailContentManagementLandownerComponent_ng_container_9_div_18_Template", "DetailContentManagementLandownerComponent_ng_container_9_Template_input_ngModelChange_23_listener", "CRequireAnswer", "DetailContentManagementLandownerComponent_ng_container_9_Template_nb_select_ngModelChange_29_listener", "selectedCUiType", "DetailContentManagementLandownerComponent_ng_container_9_Template_nb_select_selectedChange_29_listener", "changeSelectCUiType", "DetailContentManagementLandownerComponent_ng_container_9_nb_option_30_Template", "DetailContentManagementLandownerComponent_ng_container_9_label_36_Template", "DetailContentManagementLandownerComponent_ng_container_9_label_37_Template", "DetailContentManagementLandownerComponent_ng_container_9_div_38_Template", "ɵɵtextInterpolate3", "CName", "<PERSON>art", "CLocation", "listFormItem", "CIsLock", "value", "CUiTypeOptions", "houseHoldList", "DetailContentManagementLandownerComponent", "constructor", "_allow", "route", "message", "_formItemService", "_regularNoticeFileService", "_utilityService", "valid", "location", "_materialService", "typeContentManagementLandowner", "CFormType", "CNoticeType", "isNew", "ngOnInit", "paramMap", "subscribe", "params", "idParam", "get", "buildCaseId", "getListRegularNoticeFileHouseHold", "getItemByValue", "options", "item", "checked", "formItemReq_", "for<PERSON>ach", "event", "file", "target", "files", "reader", "FileReader", "readAsDataURL", "onload", "base64Str", "result", "Date", "getTime", "split", "extension", "getFileExtension", "CFile", "push", "pictureId", "filter", "x", "blob", "slice", "size", "type", "newFile", "File", "every", "createRemarkObject", "CRemarkType", "remarkObject", "option", "remarkTypes", "includes", "getMaterialList", "apiMaterialGetMaterialListPost$Json", "body", "CBuildCaseId", "CPagi", "pipe", "res", "Entries", "StatusCode", "arrListFormItemReq", "map", "o", "CFormItemHouseHold", "CFormId", "CTotalAnswer", "undefined", "CUiType", "mergeItems", "getListFormItem", "apiFormItemGetListFormItemPost$Json", "CIsPaging", "formItems", "CFormItemId", "tblFormItemHouseholds", "createArrayObjectFromArray", "formItemReq", "getHouseHoldListByNoticeType", "CHouseHoldList", "getKeysWithTrueValue", "obj", "Object", "keys", "key", "getKeysWithTrueValueJoined", "join", "getCRemarkType", "getStringAfterComma", "inputString", "parts", "formatFile", "Base64String", "FileExtension", "FileName", "items", "Map", "has", "existing", "count", "set", "Array", "from", "values", "validation", "clear", "hasInvalidCUiType", "hasInvalidCRequireAnswer", "hasInvalidItemName", "saveListFormItemReq", "addErrorMessage", "onSubmit", "e", "CFormID", "errorMessages", "showErrorMSGs", "createListFormItem", "saveListFormItem", "apiFormItemSaveListFormItemPost$Json", "showSucessMSG", "goBack", "creatListFormItem", "CFormItem", "apiFormItemCreateListFormItemPost$Json", "a", "b", "c", "matchingItem", "find", "bItem", "CHousehold", "CIsSelect", "apiRegularNoticeFileGetListRegularNoticeFileHouseHoldPost$Json", "back", "ɵɵdirectiveInject", "i1", "AllowHelper", "i2", "ActivatedRoute", "i3", "MessageService", "i4", "FormItemService", "RegularNoticeFileService", "i5", "UtilityService", "i6", "ValidationHelper", "i7", "Location", "MaterialService", "selectors", "standalone", "features", "ɵɵInheritDefinitionFeature", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "DetailContentManagementLandownerComponent_Template", "rf", "ctx", "DetailContentManagementLandownerComponent_ng_container_9_Template", "DetailContentManagementLandownerComponent_Template_button_click_11_listener", "DetailContentManagementLandownerComponent_Template_button_click_13_listener", "Ng<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "i8", "DefaultValueAccessor", "NumberValueAccessor", "NgControlStatus", "NgModel", "i9", "NbCardComponent", "NbCardBodyComponent", "NbCardFooterComponent", "NbCardHeaderComponent", "NbCheckboxComponent", "NbInputDirective", "NbSelectComponent", "NbOptionComponent", "i10", "BreadcrumbComponent", "i11", "BaseFilePipe", "styles"], "sources": ["C:\\Users\\<USER>\\Documents\\jeansalechange\\adminFront\\src\\app\\pages\\selection-management\\content-management-landowner\\detail-content-management-landowner\\detail-content-management-landowner.component.ts", "C:\\Users\\<USER>\\Documents\\jeansalechange\\adminFront\\src\\app\\pages\\selection-management\\content-management-landowner\\detail-content-management-landowner\\detail-content-management-landowner.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { SharedModule } from '../../../components/shared.module';\r\nimport { CommonModule, Location } from '@angular/common';\r\nimport { NbCheckboxModule } from '@nebular/theme';\r\nimport { BaseComponent } from '../../../components/base/baseComponent';\r\nimport { AllowHelper } from 'src/app/shared/helper/allowHelper';\r\nimport { FormItemService, MaterialService, RegularNoticeFileService } from 'src/services/api/services';\r\nimport { ActivatedRoute } from '@angular/router';\r\nimport { MessageService } from 'src/app/shared/services/message.service';\r\n\r\nimport { tap } from 'rxjs';\r\nimport { CreateListFormItem, FileViewModel, GetListFormItemRes, SaveListFormItemReq } from 'src/services/api/models';\r\nimport { BaseFilePipe } from 'src/app/@theme/pipes/base-file.pipe';\r\nimport { UtilityService } from 'src/app/shared/services/utility.service';\r\nimport { ValidationHelper } from 'src/app/shared/helper/validationHelper';\r\n\r\nexport interface ExtendedSaveListFormItemReq {\r\n  CDesignFileUrl?: string | null;\r\n  CFile?: FileViewModel,\r\n  CFormItemHouseHold?: Array<string> | null;\r\n  CFormItemId?: number;\r\n  CItemName?: string;\r\n  CFormID?: number;\r\n  CPart?: string | null;\r\n  CName?: string | null;\r\n  CLocation?: string | null;\r\n  CRemarkType?: string | null;\r\n  CTotalAnswer?: number;\r\n  CRequireAnswer?: number;\r\n  CUiType?: number;\r\n  selectedCUiType: any | null;\r\n  selectedItems: { [key: string]: boolean }\r\n  selectedRemarkType?: { [key: string]: boolean }\r\n  allSelected: boolean,\r\n  listPictures: any[]\r\n}\r\n\r\n@Component({\r\n  selector: 'ngx-detail-content-management-landowner',\r\n  templateUrl: './detail-content-management-landowner.component.html',\r\n  styleUrls: ['./detail-content-management-landowner.component.scss'],\r\n  standalone: true,\r\n  imports: [CommonModule, SharedModule, NbCheckboxModule, BaseFilePipe],\r\n\r\n})\r\n\r\nexport class DetailContentManagementLandownerComponent extends BaseComponent implements OnInit {\r\n  constructor(\r\n    private _allow: AllowHelper,\r\n    private route: ActivatedRoute,\r\n    private message: MessageService,\r\n    private _formItemService: FormItemService,\r\n    private _regularNoticeFileService: RegularNoticeFileService,\r\n    private _utilityService: UtilityService,\r\n    private valid: ValidationHelper,\r\n    private location: Location,\r\n    private _materialService: MaterialService\r\n  ) {\r\n    super(_allow)\r\n  }\r\n\r\n  typeContentManagementLandowner = {\r\n    CFormType: 1,\r\n    CNoticeType: 1\r\n  }\r\n  CUiTypeOptions: any[] = [\r\n    {\r\n      value: 1, label: '建材選色'\r\n    },\r\n    {\r\n      value: 2, label: '群組選樣_選色'\r\n    }, {\r\n      value: 3, label: '建材選樣'\r\n    }\r\n  ]\r\n  CRemarkTypeOptions = [\"正常\", \"留料\"]\r\n  buildCaseId: number\r\n\r\n  override ngOnInit(): void {\r\n    this.route.paramMap.subscribe(params => {\r\n      if (params) {\r\n        const idParam = params.get('id');\r\n        const id = idParam ? +idParam : 0;\r\n        this.buildCaseId = id\r\n        if (this.buildCaseId) {\r\n          this.getListRegularNoticeFileHouseHold()\r\n        }\r\n      }\r\n    })\r\n  }\r\n\r\n\r\n  getItemByValue(value: any, options: any[]) {\r\n    for (const item of options) {\r\n      if (item.value === value) {\r\n        return item;\r\n      }\r\n    }\r\n    return null;\r\n  }\r\n\r\n\r\n  selectedItems: { [key: string]: boolean } = {};\r\n  selectedRemarkType: { [key: string]: boolean } = {};\r\n\r\n  onCheckAllChange(checked: boolean, formItemReq_: any) {\r\n    formItemReq_.allSelected = checked;\r\n    this.houseHoldList.forEach(item => {\r\n      formItemReq_.selectedItems[item] = checked;\r\n    });\r\n  }\r\n\r\n\r\n  detectFiles(event: any, formItemReq_: any) {\r\n    const file = event.target.files[0]\r\n    if (file) {\r\n      let reader = new FileReader();\r\n      reader.readAsDataURL(file);\r\n      reader.onload = () => {\r\n        let base64Str: string = reader.result as string;\r\n        if (!base64Str) {\r\n          return;\r\n        }\r\n        if (formItemReq_.listPictures.length > 0) {\r\n          formItemReq_.listPictures[0] = {\r\n            id: new Date().getTime(),\r\n            name: file.name.split('.')[0],\r\n            data: base64Str,\r\n            extension: this._utilityService.getFileExtension(file.name),\r\n            CFile: file\r\n          };\r\n        } else {\r\n          formItemReq_.listPictures.push({\r\n            id: new Date().getTime(),\r\n            name: file.name.split('.')[0],\r\n            data: base64Str,\r\n            extension: this._utilityService.getFileExtension(file.name),\r\n            CFile: file\r\n          });\r\n        }\r\n        event.target.value = null;\r\n      };\r\n    }\r\n  }\r\n\r\n  removeImage(pictureId: number, formItemReq_: any) {\r\n    if (formItemReq_.listPictures.length) {\r\n      formItemReq_.listPictures = formItemReq_.listPictures.filter((x: any) => x.id != pictureId)\r\n    }\r\n  }\r\n\r\n  renameFile(event: any, index: number, formItemReq_: any) {\r\n    var blob = formItemReq_.listPictures[index].CFile.slice(0, formItemReq_.listPictures[index].CFile.size, formItemReq_.listPictures[index].CFile.type);\r\n    var newFile = new File([blob], `${event.target.value + '.' + formItemReq_.listPictures[index].extension}`, { type: formItemReq_.listPictures[index].CFile.type });\r\n    formItemReq_.listPictures[index].CFile = newFile\r\n  }\r\n\r\n  onCheckboxHouseHoldListChange(checked: boolean, item: string, formItemReq_: any) {\r\n    if (checked) {\r\n      formItemReq_.selectedItems[item] = checked;\r\n      formItemReq_.allSelected = this.houseHoldList.every(item => formItemReq_.selectedItems[item] == checked);\r\n    } else {\r\n      formItemReq_.allSelected = false\r\n    }\r\n  }\r\n\r\n\r\n\r\n  onCheckboxRemarkChange(checked: boolean, item: string, formItemReq_: any) {\r\n    formItemReq_.selectedRemarkType[item] = checked;\r\n  }\r\n\r\n  createRemarkObject(CRemarkTypeOptions: string[], CRemarkType: string): { [key: string]: boolean } {\r\n    const remarkObject: { [key: string]: boolean } = {};\r\n    for (const option of CRemarkTypeOptions) {\r\n      remarkObject[option] = false;\r\n    }\r\n    const remarkTypes = CRemarkType.split('-');\r\n    for (const type of remarkTypes) {\r\n      if (CRemarkTypeOptions.includes(type)) {\r\n        remarkObject[type] = true;\r\n      }\r\n    }\r\n    return remarkObject;\r\n  }\r\n\r\n  getMaterialList() { // call when create\r\n    this._materialService.apiMaterialGetMaterialListPost$Json({\r\n      body: {\r\n        CBuildCaseId: this.buildCaseId,\r\n        CPagi: false\r\n      }\r\n    }).pipe(\r\n      tap(res => {\r\n        if (res.Entries && res.StatusCode == 0) {\r\n\r\n          this.houseHoldList.forEach(item => this.selectedItems[item] = false)\r\n\r\n          this.CRemarkTypeOptions.forEach(item => this.selectedRemarkType[item] = false)\r\n          this.arrListFormItemReq = res.Entries.map((o: any) => {\r\n            return {\r\n              CDesignFileUrl: null,\r\n              CFormItemHouseHold: null,\r\n              CFormId: null,\r\n              CLocation: o.CLocation,\r\n              CName: o.CName,\r\n              CPart: o.CPart,\r\n              CItemName: `${o.CName}-${o.CPart}-${o.CLocation}`,\r\n              CRemarkType: null,\r\n              CTotalAnswer: 0,\r\n              CRequireAnswer: undefined,\r\n              CUiType: 0,\r\n              selectedItems: {},\r\n              selectedRemarkType: this.selectedRemarkType,\r\n              allSelected: false,\r\n              listPictures: [],\r\n              selectedCUiType: this.CUiTypeOptions[0]\r\n            }\r\n          })\r\n          this.arrListFormItemReq = [...this.mergeItems(this.arrListFormItemReq)]\r\n        }\r\n      })\r\n    ).subscribe();\r\n  }\r\n\r\n  listFormItem: GetListFormItemRes\r\n  isNew: boolean = true\r\n\r\n  getListFormItem() {\r\n    this._formItemService.apiFormItemGetListFormItemPost$Json({\r\n      body: {\r\n        CBuildCaseId: this.buildCaseId,\r\n        CFormType: this.typeContentManagementLandowner.CFormType,\r\n        CIsPaging: false\r\n      }\r\n    }).pipe(\r\n      tap(res => {\r\n        if (res.Entries && res.StatusCode == 0) {\r\n          this.listFormItem = res.Entries\r\n          this.isNew = res.Entries.formItems ? false : true\r\n          if (res.Entries.formItems) {\r\n\r\n            this.houseHoldList.forEach(item => this.selectedItems[item] = false)\r\n            this.CRemarkTypeOptions.forEach(item => this.selectedRemarkType[item] = false)\r\n\r\n            this.arrListFormItemReq = res.Entries.formItems.map((o: any) => {\r\n              return {\r\n                CFormId: this.listFormItem.CFormId,\r\n                CDesignFileUrl: o.CDesignFileUrl,\r\n                CFile: o.CFile,\r\n                CFormItemHouseHold: o.CFormItemHouseHold,\r\n                CFormItemId: o.CFormItemId,\r\n                CLocation: o.CLocation,\r\n                CName: o.CName,\r\n                CPart: o.CPart,\r\n                CItemName: o.CItemName ? o.CItemName : `${o.CName}-${o.CPart}-${o.CLocation}`,\r\n                CRemarkType: o.CRemarkType,\r\n                CTotalAnswer: o.CTotalAnswer,\r\n                CRequireAnswer: o.CUiType===3 ? 1: o.CRequireAnswer,\r\n                CUiType: o.CUiType,\r\n                selectedItems: o.tblFormItemHouseholds.length ? this.createArrayObjectFromArray(this.houseHoldList, o.tblFormItemHouseholds) : { ...this.selectedItems },\r\n                selectedRemarkType: o.CRemarkType ? this.createRemarkObject(this.CRemarkTypeOptions, o.CRemarkType) : { ...this.selectedRemarkType },\r\n                allSelected: o.tblFormItemHouseholds.length == this.houseHoldList.length,\r\n                listPictures: [],\r\n                selectedCUiType: o.CUiType ? this.getItemByValue(o.CUiType, this.CUiTypeOptions) : this.CUiTypeOptions[0]\r\n              }\r\n            })\r\n          } else {\r\n            this.getMaterialList()\r\n          }\r\n        }\r\n      })\r\n    ).subscribe()\r\n  }\r\n\r\n  changeSelectCUiType(formItemReq: any) {\r\n    if(formItemReq.selectedCUiType && formItemReq.selectedCUiType.value ===3) {\r\n      formItemReq.CRequireAnswer = 1\r\n    }\r\n  }\r\n\r\n  getHouseHoldListByNoticeType(data: any[]) {\r\n    for (let item of data) {\r\n      if (item.CNoticeType === this.typeContentManagementLandowner.CNoticeType) {\r\n        return item.CHouseHoldList;\r\n      }\r\n    }\r\n    return [];\r\n  }\r\n\r\n  arrListFormItemReq: ExtendedSaveListFormItemReq[]\r\n\r\n  getKeysWithTrueValue(obj: Record<string, boolean>): string[] { // {\"key\": true } => [\"key\"]\r\n    return Object.keys(obj).filter(key => obj[key]);\r\n  }\r\n\r\n  getKeysWithTrueValueJoined(obj: Record<string, boolean>): string {\r\n    return Object.keys(obj)\r\n      .filter(key => obj[key])\r\n      .join('-');\r\n  }\r\n\r\n  getCRemarkType(selectedCUiType: any, selectedRemarkType: any): any {\r\n    if (selectedCUiType && selectedCUiType.value == 3) {\r\n      return this.getKeysWithTrueValueJoined(selectedRemarkType)\r\n    }\r\n  }\r\n\r\n  getStringAfterComma(inputString: string): string {\r\n    const parts = inputString.split(',');\r\n    if (parts.length > 1) {\r\n      return parts[1];\r\n    } else return \"\"\r\n  }\r\n\r\n  formatFile(listPictures: any) {\r\n    if (listPictures && listPictures.length > 0) {\r\n      return {\r\n        Base64String: this.getStringAfterComma(listPictures[0].data) || null,\r\n        FileExtension: listPictures[0].extension || null,\r\n        FileName: listPictures[0].CFile.name || listPictures[0].name || null,\r\n      }\r\n    } else return undefined\r\n\r\n  }\r\n\r\n  // formatParam() {\r\n  //   const result: SaveListFormItemReq[] = \r\n  //   })\r\n  //   return result\r\n  // }\r\n  mergeItems(items: ExtendedSaveListFormItemReq[]): ExtendedSaveListFormItemReq[] {\r\n    const map = new Map<string, { item: ExtendedSaveListFormItemReq, count: number }>();\r\n\r\n    items.forEach(item => {\r\n      const key = `${item.CLocation}_${item.CName}_${item.CPart}`;\r\n      if (map.has(key)) {\r\n        const existing = map.get(key)!;\r\n        existing.count += 1;\r\n      } else {\r\n        map.set(key, { item: { ...item }, count: 1 });\r\n      }\r\n    });\r\n\r\n    return Array.from(map.values()).map(({ item, count }) => ({\r\n      ...item,\r\n      CTotalAnswer: count\r\n    }));\r\n  }\r\n\r\n\r\n  validation() {\r\n    this.valid.clear();\r\n    let hasInvalidCUiType = false\r\n    let hasInvalidCRequireAnswer = false\r\n    let hasInvalidItemName = false;\r\n    for (const item of this.saveListFormItemReq) {\r\n      if (!hasInvalidCUiType && (!item.CUiType)) {\r\n        hasInvalidCUiType = true;\r\n      }\r\n      if (!hasInvalidCRequireAnswer && (!item.CRequireAnswer)) {\r\n        hasInvalidCRequireAnswer = true;\r\n      }\r\n\r\n      if (item.CTotalAnswer && item.CRequireAnswer) {\r\n        if (item.CRequireAnswer > item.CTotalAnswer) {\r\n          this.valid.addErrorMessage('[必填數量]' + ' <= ' + item.CTotalAnswer + ` (${item.CItemName}) `);\r\n        }\r\n      }\r\n      if (!hasInvalidItemName && (!item.CItemName)) {\r\n        hasInvalidItemName = true;\r\n      }\r\n    }\r\n    if (hasInvalidCUiType) {\r\n      this.valid.addErrorMessage('[前台UI類型]' + ' 必填');\r\n    }\r\n    if (hasInvalidCRequireAnswer) {\r\n      this.valid.addErrorMessage('[必填數量]' + ' 必填且>0');\r\n    }\r\n    if (hasInvalidItemName) {\r\n      this.valid.addErrorMessage('[廚房-廚具-櫃體]' + ' 必填');\r\n    }\r\n  }\r\n\r\n  saveListFormItemReq: SaveListFormItemReq[]\r\n\r\n  onSubmit() {\r\n    this.saveListFormItemReq = this.arrListFormItemReq.map((e: any) => {\r\n      return {\r\n        CDesignFileUrl: e.CDesignFileUrl ? e.CDesignFileUrl : null,\r\n        CFile: e.listPictures ? this.formatFile(e.listPictures) : undefined,\r\n        CFormItemHouseHold: this.getKeysWithTrueValue(e.selectedItems),\r\n        CFormItemId: e.CFormItemId ? e.CFormItemId : null,\r\n        CFormID: this.isNew ? null : this.listFormItem.CFormId,\r\n        CName: e.CName,\r\n        CPart: e.CPart,\r\n        CLocation: e.CLocation,\r\n        CItemName: e.CItemName,// ? e.CItemName : `${e.CName}-${e.CPart}-${e.CLocation}`,\r\n        CRemarkType: e.selectedCUiType.value !== 3 ? null : this.getCRemarkType(e.selectedCUiType, e.selectedRemarkType) || null,\r\n        CTotalAnswer: e.CTotalAnswer,\r\n        CRequireAnswer: e.CRequireAnswer,\r\n        CUiType: e.selectedCUiType.value,\r\n      }\r\n    })\r\n    this.validation()\r\n    if (this.valid.errorMessages.length > 0) {\r\n      this.message.showErrorMSGs(this.valid.errorMessages);\r\n      return\r\n    }\r\n    if (this.isNew) {\r\n      this.createListFormItem()\r\n\r\n    } else {\r\n      this.saveListFormItem()\r\n    }\r\n  }\r\n\r\n  saveListFormItem() {\r\n    this._formItemService.apiFormItemSaveListFormItemPost$Json({\r\n      body: this.saveListFormItemReq\r\n    }).subscribe(res => {\r\n      if (res.StatusCode == 0) {\r\n        this.message.showSucessMSG(\"執行成功\");\r\n        // this.getListFormItem()\r\n        this.goBack()\r\n      }\r\n    })\r\n  }\r\n\r\n  creatListFormItem: CreateListFormItem\r\n\r\n  createListFormItem() {\r\n    this.creatListFormItem = {\r\n      CBuildCaseId: this.buildCaseId,\r\n      CFormItem: this.saveListFormItemReq || null,\r\n      CFormType: this.typeContentManagementLandowner.CFormType,\r\n    }\r\n    this._formItemService.apiFormItemCreateListFormItemPost$Json({\r\n      body: this.creatListFormItem\r\n    }).subscribe(res => {\r\n      if (res.StatusCode == 0) {\r\n        this.message.showSucessMSG(\"執行成功\");\r\n        // this.getListFormItem()\r\n        this.goBack()\r\n      }\r\n    })\r\n  }\r\n\r\n  createArrayObjectFromArray(a: string[], b: { CFormItemHouseholdId: number, CFormItemId: number, CHousehold: string, CIsSelect: boolean }[]): { [key: string]: boolean } {\r\n    const c: { [key: string]: boolean } = {};\r\n    for (const item of a) {\r\n      const matchingItem = b.find(bItem => bItem.CHousehold === item && bItem.CIsSelect);\r\n      c[item] = !!matchingItem;\r\n    }\r\n    return c;\r\n  } //[\"House1\", \"House2\", \"House3\"] => [{CHousehold: \"House1\", CIsSelect: true,... }, ... ]\r\n\r\n\r\n  houseHoldList: any[]\r\n\r\n  getListRegularNoticeFileHouseHold() {\r\n    this._regularNoticeFileService.apiRegularNoticeFileGetListRegularNoticeFileHouseHoldPost$Json({\r\n      body: this.buildCaseId\r\n    }).pipe(\r\n      tap(res => {\r\n        if (res.Entries && res.StatusCode == 0) {\r\n          this.houseHoldList = this.getHouseHoldListByNoticeType(res.Entries)\r\n          this.getListFormItem()\r\n        }\r\n      })\r\n    ).subscribe()\r\n  }\r\n  goBack() { this.location.back() }\r\n}\r\n", "<!-- 3.7.1  CNoticeType = 1 -->\r\n<nb-card accent=\"success\">\r\n  <nb-card-header>\r\n    <ngx-breadcrumb></ngx-breadcrumb>\r\n  </nb-card-header>\r\n  <nb-card-body>\r\n    <h1 class=\"font-bold text-[#818181]\"></h1>\r\n    <div class=\"d-flex flex-wrap\">\r\n      <div class=\"col-md-12\">\r\n        <h4 class=\"font-bold text-lg px-3 pb-3\">類型-獨立選樣</h4>\r\n      </div>\r\n      <ng-container *ngFor=\"let formItemReq of arrListFormItemReq; let idx = index\">\r\n        <div class=\"col-md-9\">\r\n          <div class=\"form-group d-flex align-items-center w-full\">\r\n            <label for=\"CItemName\" class=\"label col-4 text-base\">\r\n              {{formItemReq.CName}}-{{formItemReq.CPart}}-{{formItemReq.CLocation}}\r\n            </label>\r\n            <div class=\"input-group items-center w-full col-8 px-0\">\r\n              <input type=\"text\" class=\"w-full col-12\" nbInput [(ngModel)]=\"formItemReq.CItemName\"\r\n                [placeholder]=\"formItemReq.CItemName\" [disabled]=\"listFormItem.CIsLock\" />\r\n            </div>\r\n          </div>\r\n        </div>\r\n        <div class=\"col-md-3\">\r\n          <div class=\"d-flex justify-content-end w-full\">\r\n            <button class=\"btn btn-info\" (click)=\"inputFile.click()\" [disabled]=\"listFormItem.CIsLock\">上傳概念設計圖</button>\r\n          </div>\r\n        </div>\r\n        <div class=\"col-md-12\" [ngClass]=\"formItemReq.listPictures.length ? '':'hidden'\">\r\n          <input #inputFile type=\"file\" class=\"hidden\" (change)=\"detectFiles($event, formItemReq)\"\r\n            accept=\"image/png, image/gif, image/jpeg\" [disabled]=\"listFormItem.CIsLock\">\r\n          <div class=\"mt-3 w-full flex flex-col\">\r\n            <table class=\"table table-striped border\" style=\"background-color:#f3f3f3;\">\r\n              <tbody>\r\n                <tr *ngFor=\"let picture of formItemReq.listPictures; let i = index\">\r\n                  <td class=\"align-middle\">\r\n                    <label>文件名 </label> &nbsp;\r\n                    <input nbInput class=\"w-100 p-2 text-[13px]\" type=\"text\" [value]=\"picture.name\"\r\n                      (blur)=\"renameFile($event, i, formItemReq)\">\r\n                  </td>\r\n                  <td class=\"w-[100px] h-auto\">\r\n                    <img class=\"fit-size\" [src]=\"picture.data\">\r\n                  </td>\r\n                  <td class=\"text-center w-32 align-middle\">\r\n                    <button class=\"btn btn-outline-danger btn-sm m-1\"\r\n                      (click)=\"removeImage(picture.id, formItemReq)\">删除</button>\r\n                  </td>\r\n                </tr>\r\n              </tbody>\r\n            </table>\r\n          </div>\r\n        </div>\r\n        <div class=\"col-md-12 text-center\" *ngIf=\"formItemReq.CDesignFileUrl\"\r\n          [ngClass]=\"formItemReq.listPictures.length ? 'hidden':''\">\r\n          <div class=\"mt-3 w-full flex flex-col\">\r\n            <table class=\"table table-striped border\">\r\n              <tbody>\r\n                <tr>\r\n                  <td class=\"align-middle\">\r\n                  </td>\r\n                  <td class=\"w-[80px] h-auto\">\r\n                    <img *ngIf=\"formItemReq.CDesignFileUrl\" class=\"w-14 h-14\"\r\n                      [src]=\"formItemReq.CDesignFileUrl | addBaseFile\">\r\n                  </td>\r\n                  <td class=\"text-center w-32 align-middle\">\r\n                  </td>\r\n                </tr>\r\n              </tbody>\r\n            </table>\r\n          </div>\r\n        </div>\r\n        <div class=\"col-md-9\">\r\n          <div class=\"form-group d-flex align-items-center w-full\">\r\n            <label for=\"cRequireAnswer\" class=\"label col-4\">必填數量</label>\r\n            <input type=\"number\" class=\"col-8\" nbInput placeholder=\"必填數量\" [(ngModel)]=\"formItemReq.CRequireAnswer \"\r\n              [disabled]=\"formItemReq.selectedCUiType.value===3\" />\r\n          </div>\r\n        </div>\r\n        <div class=\"col-md-3\">\r\n\r\n        </div>\r\n\r\n        <div class=\"col-md-9\">\r\n          <div class=\"form-group d-flex align-items-center w-full\">\r\n            <label for=\"buildingName\" class=\"label col-4\">前台UI類型</label>\r\n            <nb-select placeholder=\"建案\" [(ngModel)]=\"formItemReq.selectedCUiType\"\r\n              (selectedChange)=\"changeSelectCUiType(formItemReq)\" class=\"col-8 px-0\">\r\n              <nb-option *ngFor=\"let case of CUiTypeOptions\" [value]=\"case\">\r\n                {{ case.label }}\r\n              </nb-option>\r\n            </nb-select>\r\n          </div>\r\n        </div>\r\n        <div class=\"col-md-3\"></div>\r\n        <div class=\"col-md-12\">\r\n          <div class=\"form-group d-flex align-items-center w-full\">\r\n            <label for=\"buildingName\" class=\"label col-3\">適用戶別 </label>\r\n            <label class=\"mr-2\" *ngIf=\"houseHoldList.length\">\r\n              <nb-checkbox [(checked)]=\"formItemReq.allSelected\"\r\n                (checkedChange)=\"onCheckAllChange($event, formItemReq)\">\r\n                全選\r\n              </nb-checkbox>\r\n            </label>\r\n            <label *ngFor=\"let item of houseHoldList\" class=\"mr-2\">\r\n              <nb-checkbox [(checked)]=\"formItemReq.selectedItems[item]\" value=\"item\"\r\n                (checkedChange)=\"onCheckboxHouseHoldListChange($event, item, formItemReq)\">\r\n                {{ item }}\r\n              </nb-checkbox>\r\n            </label>\r\n          </div>\r\n        </div>\r\n        <div class=\"col-md-12\" *ngIf=\"formItemReq.selectedCUiType.value===3\">\r\n          <div class=\"form-group d-flex align-items-center w-full\">\r\n            <label for=\"buildingName\" class=\"label col-3\">備註選項</label>\r\n            <label *ngFor=\"let remark of CRemarkTypeOptions\" class=\"mr-2\">\r\n              <nb-checkbox *ngIf=\"formItemReq.selectedRemarkType\" [(checked)]=\"formItemReq.selectedRemarkType[remark]\"\r\n                value=\"item\" (checkedChange)=\"onCheckboxRemarkChange($event, remark, formItemReq)\">\r\n                {{ remark }}\r\n              </nb-checkbox>\r\n            </label>\r\n          </div>\r\n        </div>\r\n      </ng-container>\r\n    </div>\r\n  </nb-card-body>\r\n  <nb-card-footer class=\"d-flex justify-content-center\">\r\n    <button class=\"btn btn-secondary mx-2\" (click)=\"goBack()\">\r\n      取消\r\n    </button>\r\n    <button class=\"btn btn-info\" (click)=\"onSubmit()\">\r\n      儲存\r\n    </button>\r\n  </nb-card-footer>\r\n</nb-card>"], "mappings": "AACA,SAASA,YAAY,QAAQ,mCAAmC;AAChE,SAASC,YAAY,QAAkB,iBAAiB;AACxD,SAASC,gBAAgB,QAAQ,gBAAgB;AACjD,SAASC,aAAa,QAAQ,wCAAwC;AAMtE,SAASC,GAAG,QAAQ,MAAM;;;;;;;;;;;;;;;;IC0BNC,EAFJ,CAAAC,cAAA,SAAoE,aACzC,YAChB;IAAAD,EAAA,CAAAE,MAAA,0BAAI;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAACH,EAAA,CAAAE,MAAA,eACpB;IAAAF,EAAA,CAAAC,cAAA,gBAC8C;IAA5CD,EAAA,CAAAI,UAAA,kBAAAC,8FAAAC,MAAA;MAAA,MAAAC,IAAA,GAAAP,EAAA,CAAAQ,aAAA,CAAAC,GAAA,EAAAC,KAAA;MAAA,MAAAC,cAAA,GAAAX,EAAA,CAAAY,aAAA,GAAAC,SAAA;MAAA,MAAAC,MAAA,GAAAd,EAAA,CAAAY,aAAA;MAAA,OAAAZ,EAAA,CAAAe,WAAA,CAAQD,MAAA,CAAAE,UAAA,CAAAV,MAAA,EAAAC,IAAA,EAAAI,cAAA,CAAkC;IAAA,EAAC;IAC/CX,EAFE,CAAAG,YAAA,EAC8C,EAC3C;IACLH,EAAA,CAAAC,cAAA,aAA6B;IAC3BD,EAAA,CAAAiB,SAAA,cAA2C;IAC7CjB,EAAA,CAAAG,YAAA,EAAK;IAEHH,EADF,CAAAC,cAAA,aAA0C,iBAES;IAA/CD,EAAA,CAAAI,UAAA,mBAAAc,gGAAA;MAAA,MAAAC,UAAA,GAAAnB,EAAA,CAAAQ,aAAA,CAAAC,GAAA,EAAAI,SAAA;MAAA,MAAAF,cAAA,GAAAX,EAAA,CAAAY,aAAA,GAAAC,SAAA;MAAA,MAAAC,MAAA,GAAAd,EAAA,CAAAY,aAAA;MAAA,OAAAZ,EAAA,CAAAe,WAAA,CAASD,MAAA,CAAAM,WAAA,CAAAD,UAAA,CAAAE,EAAA,EAAAV,cAAA,CAAoC;IAAA,EAAC;IAACX,EAAA,CAAAE,MAAA,oBAAE;IAEvDF,EAFuD,CAAAG,YAAA,EAAS,EACzD,EACF;;;;IAVwDH,EAAA,CAAAsB,SAAA,GAAsB;IAAtBtB,EAAA,CAAAuB,UAAA,UAAAJ,UAAA,CAAAK,IAAA,CAAsB;IAIzDxB,EAAA,CAAAsB,SAAA,GAAoB;IAApBtB,EAAA,CAAAuB,UAAA,QAAAJ,UAAA,CAAAM,IAAA,EAAAzB,EAAA,CAAA0B,aAAA,CAAoB;;;;;IAoB1C1B,EAAA,CAAAiB,SAAA,cACmD;;;;;IAAjDjB,EAAA,CAAAuB,UAAA,QAAAvB,EAAA,CAAA2B,WAAA,OAAAhB,cAAA,CAAAiB,cAAA,GAAA5B,EAAA,CAAA0B,aAAA,CAAgD;;;;;IALtD1B,EALR,CAAAC,cAAA,cAC4D,cACnB,gBACK,YACjC,SACD;IACFD,EAAA,CAAAiB,SAAA,aACK;IACLjB,EAAA,CAAAC,cAAA,aAA4B;IAC1BD,EAAA,CAAA6B,UAAA,IAAAC,8EAAA,kBACmD;IACrD9B,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAiB,SAAA,aACK;IAKfjB,EAJQ,CAAAG,YAAA,EAAK,EACC,EACF,EACJ,EACF;;;;IAjBJH,EAAA,CAAAuB,UAAA,YAAAZ,cAAA,CAAAoB,YAAA,CAAAC,MAAA,iBAAyD;IAQzChC,EAAA,CAAAsB,SAAA,GAAgC;IAAhCtB,EAAA,CAAAuB,UAAA,SAAAZ,cAAA,CAAAiB,cAAA,CAAgC;;;;;IA0B5C5B,EAAA,CAAAC,cAAA,oBAA8D;IAC5DD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAFmCH,EAAA,CAAAuB,UAAA,UAAAU,OAAA,CAAc;IAC3DjC,EAAA,CAAAsB,SAAA,EACF;IADEtB,EAAA,CAAAkC,kBAAA,MAAAD,OAAA,CAAAE,KAAA,MACF;;;;;;IASAnC,EADF,CAAAC,cAAA,gBAAiD,sBAEW;IAD7CD,EAAA,CAAAoC,gBAAA,2BAAAC,gHAAA/B,MAAA;MAAAN,EAAA,CAAAQ,aAAA,CAAA8B,GAAA;MAAA,MAAA3B,cAAA,GAAAX,EAAA,CAAAY,aAAA,GAAAC,SAAA;MAAAb,EAAA,CAAAuC,kBAAA,CAAA5B,cAAA,CAAA6B,WAAA,EAAAlC,MAAA,MAAAK,cAAA,CAAA6B,WAAA,GAAAlC,MAAA;MAAA,OAAAN,EAAA,CAAAe,WAAA,CAAAT,MAAA;IAAA,EAAqC;IAChDN,EAAA,CAAAI,UAAA,2BAAAiC,gHAAA/B,MAAA;MAAAN,EAAA,CAAAQ,aAAA,CAAA8B,GAAA;MAAA,MAAA3B,cAAA,GAAAX,EAAA,CAAAY,aAAA,GAAAC,SAAA;MAAA,MAAAC,MAAA,GAAAd,EAAA,CAAAY,aAAA;MAAA,OAAAZ,EAAA,CAAAe,WAAA,CAAiBD,MAAA,CAAA2B,gBAAA,CAAAnC,MAAA,EAAAK,cAAA,CAAqC;IAAA,EAAC;IACvDX,EAAA,CAAAE,MAAA,qBACF;IACFF,EADE,CAAAG,YAAA,EAAc,EACR;;;;IAJOH,EAAA,CAAAsB,SAAA,EAAqC;IAArCtB,EAAA,CAAA0C,gBAAA,YAAA/B,cAAA,CAAA6B,WAAA,CAAqC;;;;;;IAMlDxC,EADF,CAAAC,cAAA,gBAAuD,sBAEwB;IADhED,EAAA,CAAAoC,gBAAA,2BAAAO,gHAAArC,MAAA;MAAA,MAAAsC,QAAA,GAAA5C,EAAA,CAAAQ,aAAA,CAAAqC,IAAA,EAAAhC,SAAA;MAAA,MAAAF,cAAA,GAAAX,EAAA,CAAAY,aAAA,GAAAC,SAAA;MAAAb,EAAA,CAAAuC,kBAAA,CAAA5B,cAAA,CAAAmC,aAAA,CAAAF,QAAA,GAAAtC,MAAA,MAAAK,cAAA,CAAAmC,aAAA,CAAAF,QAAA,IAAAtC,MAAA;MAAA,OAAAN,EAAA,CAAAe,WAAA,CAAAT,MAAA;IAAA,EAA6C;IACxDN,EAAA,CAAAI,UAAA,2BAAAuC,gHAAArC,MAAA;MAAA,MAAAsC,QAAA,GAAA5C,EAAA,CAAAQ,aAAA,CAAAqC,IAAA,EAAAhC,SAAA;MAAA,MAAAF,cAAA,GAAAX,EAAA,CAAAY,aAAA,GAAAC,SAAA;MAAA,MAAAC,MAAA,GAAAd,EAAA,CAAAY,aAAA;MAAA,OAAAZ,EAAA,CAAAe,WAAA,CAAiBD,MAAA,CAAAiC,6BAAA,CAAAzC,MAAA,EAAAsC,QAAA,EAAAjC,cAAA,CAAwD;IAAA,EAAC;IAC1EX,EAAA,CAAAE,MAAA,GACF;IACFF,EADE,CAAAG,YAAA,EAAc,EACR;;;;;IAJOH,EAAA,CAAAsB,SAAA,EAA6C;IAA7CtB,EAAA,CAAA0C,gBAAA,YAAA/B,cAAA,CAAAmC,aAAA,CAAAF,QAAA,EAA6C;IAExD5C,EAAA,CAAAsB,SAAA,EACF;IADEtB,EAAA,CAAAkC,kBAAA,MAAAU,QAAA,MACF;;;;;;IAQA5C,EAAA,CAAAC,cAAA,sBACqF;IADjCD,EAAA,CAAAoC,gBAAA,2BAAAY,oIAAA1C,MAAA;MAAAN,EAAA,CAAAQ,aAAA,CAAAyC,IAAA;MAAA,MAAAC,UAAA,GAAAlD,EAAA,CAAAY,aAAA,GAAAC,SAAA;MAAA,MAAAF,cAAA,GAAAX,EAAA,CAAAY,aAAA,IAAAC,SAAA;MAAAb,EAAA,CAAAuC,kBAAA,CAAA5B,cAAA,CAAAwC,kBAAA,CAAAD,UAAA,GAAA5C,MAAA,MAAAK,cAAA,CAAAwC,kBAAA,CAAAD,UAAA,IAAA5C,MAAA;MAAA,OAAAN,EAAA,CAAAe,WAAA,CAAAT,MAAA;IAAA,EAAoD;IACzFN,EAAA,CAAAI,UAAA,2BAAA4C,oIAAA1C,MAAA;MAAAN,EAAA,CAAAQ,aAAA,CAAAyC,IAAA;MAAA,MAAAC,UAAA,GAAAlD,EAAA,CAAAY,aAAA,GAAAC,SAAA;MAAA,MAAAF,cAAA,GAAAX,EAAA,CAAAY,aAAA,IAAAC,SAAA;MAAA,MAAAC,MAAA,GAAAd,EAAA,CAAAY,aAAA;MAAA,OAAAZ,EAAA,CAAAe,WAAA,CAAiBD,MAAA,CAAAsC,sBAAA,CAAA9C,MAAA,EAAA4C,UAAA,EAAAvC,cAAA,CAAmD;IAAA,EAAC;IAClFX,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAc;;;;;IAHsCH,EAAA,CAAA0C,gBAAA,YAAA/B,cAAA,CAAAwC,kBAAA,CAAAD,UAAA,EAAoD;IAEtGlD,EAAA,CAAAsB,SAAA,EACF;IADEtB,EAAA,CAAAkC,kBAAA,MAAAgB,UAAA,MACF;;;;;IAJFlD,EAAA,CAAAC,cAAA,gBAA8D;IAC5DD,EAAA,CAAA6B,UAAA,IAAAwB,8FAAA,0BACqF;IAGvFrD,EAAA,CAAAG,YAAA,EAAQ;;;;IAJQH,EAAA,CAAAsB,SAAA,EAAoC;IAApCtB,EAAA,CAAAuB,UAAA,SAAAZ,cAAA,CAAAwC,kBAAA,CAAoC;;;;;IAFpDnD,EAFJ,CAAAC,cAAA,aAAqE,cACV,gBACT;IAAAD,EAAA,CAAAE,MAAA,+BAAI;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAC1DH,EAAA,CAAA6B,UAAA,IAAAyB,gFAAA,oBAA8D;IAOlEtD,EADE,CAAAG,YAAA,EAAM,EACF;;;;IAPwBH,EAAA,CAAAsB,SAAA,GAAqB;IAArBtB,EAAA,CAAAuB,UAAA,YAAAT,MAAA,CAAAyC,kBAAA,CAAqB;;;;;;IAvGrDvD,EAAA,CAAAwD,uBAAA,GAA8E;IAGxExD,EAFJ,CAAAC,cAAA,cAAsB,cACqC,gBACF;IACnDD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAENH,EADF,CAAAC,cAAA,cAAwD,gBAEsB;IAD3BD,EAAA,CAAAoC,gBAAA,2BAAAqB,iGAAAnD,MAAA;MAAA,MAAAK,cAAA,GAAAX,EAAA,CAAAQ,aAAA,CAAAkD,GAAA,EAAA7C,SAAA;MAAAb,EAAA,CAAAuC,kBAAA,CAAA5B,cAAA,CAAAgD,SAAA,EAAArD,MAAA,MAAAK,cAAA,CAAAgD,SAAA,GAAArD,MAAA;MAAA,OAAAN,EAAA,CAAAe,WAAA,CAAAT,MAAA;IAAA,EAAmC;IAI1FN,EAJM,CAAAG,YAAA,EAC4E,EACxE,EACF,EACF;IAGFH,EAFJ,CAAAC,cAAA,cAAsB,cAC2B,iBAC8C;IAA9DD,EAAA,CAAAI,UAAA,mBAAAwD,0FAAA;MAAA5D,EAAA,CAAAQ,aAAA,CAAAkD,GAAA;MAAA,MAAAG,YAAA,GAAA7D,EAAA,CAAA8D,WAAA;MAAA,OAAA9D,EAAA,CAAAe,WAAA,CAAS8C,YAAA,CAAAE,KAAA,EAAiB;IAAA,EAAC;IAAmC/D,EAAA,CAAAE,MAAA,kDAAO;IAEtGF,EAFsG,CAAAG,YAAA,EAAS,EACvG,EACF;IAEJH,EADF,CAAAC,cAAA,eAAiF,oBAED;IADjCD,EAAA,CAAAI,UAAA,oBAAA4D,2FAAA1D,MAAA;MAAA,MAAAK,cAAA,GAAAX,EAAA,CAAAQ,aAAA,CAAAkD,GAAA,EAAA7C,SAAA;MAAA,MAAAC,MAAA,GAAAd,EAAA,CAAAY,aAAA;MAAA,OAAAZ,EAAA,CAAAe,WAAA,CAAUD,MAAA,CAAAmD,WAAA,CAAA3D,MAAA,EAAAK,cAAA,CAAgC;IAAA,EAAC;IAAxFX,EAAA,CAAAG,YAAA,EAC8E;IAG1EH,EAFJ,CAAAC,cAAA,eAAuC,iBACuC,aACnE;IACLD,EAAA,CAAA6B,UAAA,KAAAqC,uEAAA,iBAAoE;IAiB5ElE,EAHM,CAAAG,YAAA,EAAQ,EACF,EACJ,EACF;IACNH,EAAA,CAAA6B,UAAA,KAAAsC,wEAAA,kBAC4D;IAoBxDnE,EAFJ,CAAAC,cAAA,eAAsB,eACqC,iBACP;IAAAD,EAAA,CAAAE,MAAA,gCAAI;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAC5DH,EAAA,CAAAC,cAAA,iBACuD;IADOD,EAAA,CAAAoC,gBAAA,2BAAAgC,kGAAA9D,MAAA;MAAA,MAAAK,cAAA,GAAAX,EAAA,CAAAQ,aAAA,CAAAkD,GAAA,EAAA7C,SAAA;MAAAb,EAAA,CAAAuC,kBAAA,CAAA5B,cAAA,CAAA0D,cAAA,EAAA/D,MAAA,MAAAK,cAAA,CAAA0D,cAAA,GAAA/D,MAAA;MAAA,OAAAN,EAAA,CAAAe,WAAA,CAAAT,MAAA;IAAA,EAAyC;IAG3GN,EAHI,CAAAG,YAAA,EACuD,EACnD,EACF;IACNH,EAAA,CAAAiB,SAAA,eAEM;IAIFjB,EAFJ,CAAAC,cAAA,eAAsB,eACqC,iBACT;IAAAD,EAAA,CAAAE,MAAA,kCAAM;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAC5DH,EAAA,CAAAC,cAAA,qBACyE;IAD7CD,EAAA,CAAAoC,gBAAA,2BAAAkC,sGAAAhE,MAAA;MAAA,MAAAK,cAAA,GAAAX,EAAA,CAAAQ,aAAA,CAAAkD,GAAA,EAAA7C,SAAA;MAAAb,EAAA,CAAAuC,kBAAA,CAAA5B,cAAA,CAAA4D,eAAA,EAAAjE,MAAA,MAAAK,cAAA,CAAA4D,eAAA,GAAAjE,MAAA;MAAA,OAAAN,EAAA,CAAAe,WAAA,CAAAT,MAAA;IAAA,EAAyC;IACnEN,EAAA,CAAAI,UAAA,4BAAAoE,uGAAA;MAAA,MAAA7D,cAAA,GAAAX,EAAA,CAAAQ,aAAA,CAAAkD,GAAA,EAAA7C,SAAA;MAAA,MAAAC,MAAA,GAAAd,EAAA,CAAAY,aAAA;MAAA,OAAAZ,EAAA,CAAAe,WAAA,CAAkBD,MAAA,CAAA2D,mBAAA,CAAA9D,cAAA,CAAgC;IAAA,EAAC;IACnDX,EAAA,CAAA6B,UAAA,KAAA6C,8EAAA,wBAA8D;IAKpE1E,EAFI,CAAAG,YAAA,EAAY,EACR,EACF;IACNH,EAAA,CAAAiB,SAAA,eAA4B;IAGxBjB,EAFJ,CAAAC,cAAA,cAAuB,eACoC,iBACT;IAAAD,EAAA,CAAAE,MAAA,iCAAK;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAO3DH,EANA,CAAA6B,UAAA,KAAA8C,0EAAA,oBAAiD,KAAAC,0EAAA,oBAMM;IAO3D5E,EADE,CAAAG,YAAA,EAAM,EACF;IACNH,EAAA,CAAA6B,UAAA,KAAAgD,wEAAA,kBAAqE;;;;;;IAhG/D7E,EAAA,CAAAsB,SAAA,GACF;IADEtB,EAAA,CAAA8E,kBAAA,MAAAnE,cAAA,CAAAoE,KAAA,OAAApE,cAAA,CAAAqE,KAAA,OAAArE,cAAA,CAAAsE,SAAA,MACF;IAEmDjF,EAAA,CAAAsB,SAAA,GAAmC;IAAnCtB,EAAA,CAAA0C,gBAAA,YAAA/B,cAAA,CAAAgD,SAAA,CAAmC;IAC5C3D,EAAtC,CAAAuB,UAAA,gBAAAZ,cAAA,CAAAgD,SAAA,CAAqC,aAAA7C,MAAA,CAAAoE,YAAA,CAAAC,OAAA,CAAkC;IAMlBnF,EAAA,CAAAsB,SAAA,GAAiC;IAAjCtB,EAAA,CAAAuB,UAAA,aAAAT,MAAA,CAAAoE,YAAA,CAAAC,OAAA,CAAiC;IAGvEnF,EAAA,CAAAsB,SAAA,GAAyD;IAAzDtB,EAAA,CAAAuB,UAAA,YAAAZ,cAAA,CAAAoB,YAAA,CAAAC,MAAA,iBAAyD;IAElChC,EAAA,CAAAsB,SAAA,EAAiC;IAAjCtB,EAAA,CAAAuB,UAAA,aAAAT,MAAA,CAAAoE,YAAA,CAAAC,OAAA,CAAiC;IAI/CnF,EAAA,CAAAsB,SAAA,GAA6B;IAA7BtB,EAAA,CAAAuB,UAAA,YAAAZ,cAAA,CAAAoB,YAAA,CAA6B;IAkBzB/B,EAAA,CAAAsB,SAAA,EAAgC;IAAhCtB,EAAA,CAAAuB,UAAA,SAAAZ,cAAA,CAAAiB,cAAA,CAAgC;IAsBF5B,EAAA,CAAAsB,SAAA,GAAyC;IAAzCtB,EAAA,CAAA0C,gBAAA,YAAA/B,cAAA,CAAA0D,cAAA,CAAyC;IACrGrE,EAAA,CAAAuB,UAAA,aAAAZ,cAAA,CAAA4D,eAAA,CAAAa,KAAA,OAAkD;IAUxBpF,EAAA,CAAAsB,SAAA,GAAyC;IAAzCtB,EAAA,CAAA0C,gBAAA,YAAA/B,cAAA,CAAA4D,eAAA,CAAyC;IAEvCvE,EAAA,CAAAsB,SAAA,EAAiB;IAAjBtB,EAAA,CAAAuB,UAAA,YAAAT,MAAA,CAAAuE,cAAA,CAAiB;IAU1BrF,EAAA,CAAAsB,SAAA,GAA0B;IAA1BtB,EAAA,CAAAuB,UAAA,SAAAT,MAAA,CAAAwE,aAAA,CAAAtD,MAAA,CAA0B;IAMvBhC,EAAA,CAAAsB,SAAA,EAAgB;IAAhBtB,EAAA,CAAAuB,UAAA,YAAAT,MAAA,CAAAwE,aAAA,CAAgB;IAQpBtF,EAAA,CAAAsB,SAAA,EAA2C;IAA3CtB,EAAA,CAAAuB,UAAA,SAAAZ,cAAA,CAAA4D,eAAA,CAAAa,KAAA,OAA2C;;;ADjE3E,OAAM,MAAOG,yCAA0C,SAAQzF,aAAa;EAC1E0F,YACUC,MAAmB,EACnBC,KAAqB,EACrBC,OAAuB,EACvBC,gBAAiC,EACjCC,yBAAmD,EACnDC,eAA+B,EAC/BC,KAAuB,EACvBC,QAAkB,EAClBC,gBAAiC;IAEzC,KAAK,CAACR,MAAM,CAAC;IAVL,KAAAA,MAAM,GAANA,MAAM;IACN,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,yBAAyB,GAAzBA,yBAAyB;IACzB,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,QAAQ,GAARA,QAAQ;IACR,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAK1B,KAAAC,8BAA8B,GAAG;MAC/BC,SAAS,EAAE,CAAC;MACZC,WAAW,EAAE;KACd;IACD,KAAAf,cAAc,GAAU,CACtB;MACED,KAAK,EAAE,CAAC;MAAEjD,KAAK,EAAE;KAClB,EACD;MACEiD,KAAK,EAAE,CAAC;MAAEjD,KAAK,EAAE;KAClB,EAAE;MACDiD,KAAK,EAAE,CAAC;MAAEjD,KAAK,EAAE;KAClB,CACF;IACD,KAAAoB,kBAAkB,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC;IA2BjC,KAAAT,aAAa,GAA+B,EAAE;IAC9C,KAAAK,kBAAkB,GAA+B,EAAE;IA2HnD,KAAAkD,KAAK,GAAY,IAAI;EAvKrB;EAmBSC,QAAQA,CAAA;IACf,IAAI,CAACZ,KAAK,CAACa,QAAQ,CAACC,SAAS,CAACC,MAAM,IAAG;MACrC,IAAIA,MAAM,EAAE;QACV,MAAMC,OAAO,GAAGD,MAAM,CAACE,GAAG,CAAC,IAAI,CAAC;QAChC,MAAMtF,EAAE,GAAGqF,OAAO,GAAG,CAACA,OAAO,GAAG,CAAC;QACjC,IAAI,CAACE,WAAW,GAAGvF,EAAE;QACrB,IAAI,IAAI,CAACuF,WAAW,EAAE;UACpB,IAAI,CAACC,iCAAiC,EAAE;QAC1C;MACF;IACF,CAAC,CAAC;EACJ;EAGAC,cAAcA,CAAC1B,KAAU,EAAE2B,OAAc;IACvC,KAAK,MAAMC,IAAI,IAAID,OAAO,EAAE;MAC1B,IAAIC,IAAI,CAAC5B,KAAK,KAAKA,KAAK,EAAE;QACxB,OAAO4B,IAAI;MACb;IACF;IACA,OAAO,IAAI;EACb;EAMAvE,gBAAgBA,CAACwE,OAAgB,EAAEC,YAAiB;IAClDA,YAAY,CAAC1E,WAAW,GAAGyE,OAAO;IAClC,IAAI,CAAC3B,aAAa,CAAC6B,OAAO,CAACH,IAAI,IAAG;MAChCE,YAAY,CAACpE,aAAa,CAACkE,IAAI,CAAC,GAAGC,OAAO;IAC5C,CAAC,CAAC;EACJ;EAGAhD,WAAWA,CAACmD,KAAU,EAAEF,YAAiB;IACvC,MAAMG,IAAI,GAAGD,KAAK,CAACE,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC;IAClC,IAAIF,IAAI,EAAE;MACR,IAAIG,MAAM,GAAG,IAAIC,UAAU,EAAE;MAC7BD,MAAM,CAACE,aAAa,CAACL,IAAI,CAAC;MAC1BG,MAAM,CAACG,MAAM,GAAG,MAAK;QACnB,IAAIC,SAAS,GAAWJ,MAAM,CAACK,MAAgB;QAC/C,IAAI,CAACD,SAAS,EAAE;UACd;QACF;QACA,IAAIV,YAAY,CAACnF,YAAY,CAACC,MAAM,GAAG,CAAC,EAAE;UACxCkF,YAAY,CAACnF,YAAY,CAAC,CAAC,CAAC,GAAG;YAC7BV,EAAE,EAAE,IAAIyG,IAAI,EAAE,CAACC,OAAO,EAAE;YACxBvG,IAAI,EAAE6F,IAAI,CAAC7F,IAAI,CAACwG,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;YAC7BvG,IAAI,EAAEmG,SAAS;YACfK,SAAS,EAAE,IAAI,CAACnC,eAAe,CAACoC,gBAAgB,CAACb,IAAI,CAAC7F,IAAI,CAAC;YAC3D2G,KAAK,EAAEd;WACR;QACH,CAAC,MAAM;UACLH,YAAY,CAACnF,YAAY,CAACqG,IAAI,CAAC;YAC7B/G,EAAE,EAAE,IAAIyG,IAAI,EAAE,CAACC,OAAO,EAAE;YACxBvG,IAAI,EAAE6F,IAAI,CAAC7F,IAAI,CAACwG,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;YAC7BvG,IAAI,EAAEmG,SAAS;YACfK,SAAS,EAAE,IAAI,CAACnC,eAAe,CAACoC,gBAAgB,CAACb,IAAI,CAAC7F,IAAI,CAAC;YAC3D2G,KAAK,EAAEd;WACR,CAAC;QACJ;QACAD,KAAK,CAACE,MAAM,CAAClC,KAAK,GAAG,IAAI;MAC3B,CAAC;IACH;EACF;EAEAhE,WAAWA,CAACiH,SAAiB,EAAEnB,YAAiB;IAC9C,IAAIA,YAAY,CAACnF,YAAY,CAACC,MAAM,EAAE;MACpCkF,YAAY,CAACnF,YAAY,GAAGmF,YAAY,CAACnF,YAAY,CAACuG,MAAM,CAAEC,CAAM,IAAKA,CAAC,CAAClH,EAAE,IAAIgH,SAAS,CAAC;IAC7F;EACF;EAEArH,UAAUA,CAACoG,KAAU,EAAE1G,KAAa,EAAEwG,YAAiB;IACrD,IAAIsB,IAAI,GAAGtB,YAAY,CAACnF,YAAY,CAACrB,KAAK,CAAC,CAACyH,KAAK,CAACM,KAAK,CAAC,CAAC,EAAEvB,YAAY,CAACnF,YAAY,CAACrB,KAAK,CAAC,CAACyH,KAAK,CAACO,IAAI,EAAExB,YAAY,CAACnF,YAAY,CAACrB,KAAK,CAAC,CAACyH,KAAK,CAACQ,IAAI,CAAC;IACpJ,IAAIC,OAAO,GAAG,IAAIC,IAAI,CAAC,CAACL,IAAI,CAAC,EAAE,GAAGpB,KAAK,CAACE,MAAM,CAAClC,KAAK,GAAG,GAAG,GAAG8B,YAAY,CAACnF,YAAY,CAACrB,KAAK,CAAC,CAACuH,SAAS,EAAE,EAAE;MAAEU,IAAI,EAAEzB,YAAY,CAACnF,YAAY,CAACrB,KAAK,CAAC,CAACyH,KAAK,CAACQ;IAAI,CAAE,CAAC;IACjKzB,YAAY,CAACnF,YAAY,CAACrB,KAAK,CAAC,CAACyH,KAAK,GAAGS,OAAO;EAClD;EAEA7F,6BAA6BA,CAACkE,OAAgB,EAAED,IAAY,EAAEE,YAAiB;IAC7E,IAAID,OAAO,EAAE;MACXC,YAAY,CAACpE,aAAa,CAACkE,IAAI,CAAC,GAAGC,OAAO;MAC1CC,YAAY,CAAC1E,WAAW,GAAG,IAAI,CAAC8C,aAAa,CAACwD,KAAK,CAAC9B,IAAI,IAAIE,YAAY,CAACpE,aAAa,CAACkE,IAAI,CAAC,IAAIC,OAAO,CAAC;IAC1G,CAAC,MAAM;MACLC,YAAY,CAAC1E,WAAW,GAAG,KAAK;IAClC;EACF;EAIAY,sBAAsBA,CAAC6D,OAAgB,EAAED,IAAY,EAAEE,YAAiB;IACtEA,YAAY,CAAC/D,kBAAkB,CAAC6D,IAAI,CAAC,GAAGC,OAAO;EACjD;EAEA8B,kBAAkBA,CAACxF,kBAA4B,EAAEyF,WAAmB;IAClE,MAAMC,YAAY,GAA+B,EAAE;IACnD,KAAK,MAAMC,MAAM,IAAI3F,kBAAkB,EAAE;MACvC0F,YAAY,CAACC,MAAM,CAAC,GAAG,KAAK;IAC9B;IACA,MAAMC,WAAW,GAAGH,WAAW,CAAChB,KAAK,CAAC,GAAG,CAAC;IAC1C,KAAK,MAAMW,IAAI,IAAIQ,WAAW,EAAE;MAC9B,IAAI5F,kBAAkB,CAAC6F,QAAQ,CAACT,IAAI,CAAC,EAAE;QACrCM,YAAY,CAACN,IAAI,CAAC,GAAG,IAAI;MAC3B;IACF;IACA,OAAOM,YAAY;EACrB;EAEAI,eAAeA,CAAA;IACb,IAAI,CAACpD,gBAAgB,CAACqD,mCAAmC,CAAC;MACxDC,IAAI,EAAE;QACJC,YAAY,EAAE,IAAI,CAAC5C,WAAW;QAC9B6C,KAAK,EAAE;;KAEV,CAAC,CAACC,IAAI,CACL3J,GAAG,CAAC4J,GAAG,IAAG;MACR,IAAIA,GAAG,CAACC,OAAO,IAAID,GAAG,CAACE,UAAU,IAAI,CAAC,EAAE;QAEtC,IAAI,CAACvE,aAAa,CAAC6B,OAAO,CAACH,IAAI,IAAI,IAAI,CAAClE,aAAa,CAACkE,IAAI,CAAC,GAAG,KAAK,CAAC;QAEpE,IAAI,CAACzD,kBAAkB,CAAC4D,OAAO,CAACH,IAAI,IAAI,IAAI,CAAC7D,kBAAkB,CAAC6D,IAAI,CAAC,GAAG,KAAK,CAAC;QAC9E,IAAI,CAAC8C,kBAAkB,GAAGH,GAAG,CAACC,OAAO,CAACG,GAAG,CAAEC,CAAM,IAAI;UACnD,OAAO;YACLpI,cAAc,EAAE,IAAI;YACpBqI,kBAAkB,EAAE,IAAI;YACxBC,OAAO,EAAE,IAAI;YACbjF,SAAS,EAAE+E,CAAC,CAAC/E,SAAS;YACtBF,KAAK,EAAEiF,CAAC,CAACjF,KAAK;YACdC,KAAK,EAAEgF,CAAC,CAAChF,KAAK;YACdrB,SAAS,EAAE,GAAGqG,CAAC,CAACjF,KAAK,IAAIiF,CAAC,CAAChF,KAAK,IAAIgF,CAAC,CAAC/E,SAAS,EAAE;YACjD+D,WAAW,EAAE,IAAI;YACjBmB,YAAY,EAAE,CAAC;YACf9F,cAAc,EAAE+F,SAAS;YACzBC,OAAO,EAAE,CAAC;YACVvH,aAAa,EAAE,EAAE;YACjBK,kBAAkB,EAAE,IAAI,CAACA,kBAAkB;YAC3CX,WAAW,EAAE,KAAK;YAClBT,YAAY,EAAE,EAAE;YAChBwC,eAAe,EAAE,IAAI,CAACc,cAAc,CAAC,CAAC;WACvC;QACH,CAAC,CAAC;QACF,IAAI,CAACyE,kBAAkB,GAAG,CAAC,GAAG,IAAI,CAACQ,UAAU,CAAC,IAAI,CAACR,kBAAkB,CAAC,CAAC;MACzE;IACF,CAAC,CAAC,CACH,CAACtD,SAAS,EAAE;EACf;EAKA+D,eAAeA,CAAA;IACb,IAAI,CAAC3E,gBAAgB,CAAC4E,mCAAmC,CAAC;MACxDjB,IAAI,EAAE;QACJC,YAAY,EAAE,IAAI,CAAC5C,WAAW;QAC9BT,SAAS,EAAE,IAAI,CAACD,8BAA8B,CAACC,SAAS;QACxDsE,SAAS,EAAE;;KAEd,CAAC,CAACf,IAAI,CACL3J,GAAG,CAAC4J,GAAG,IAAG;MACR,IAAIA,GAAG,CAACC,OAAO,IAAID,GAAG,CAACE,UAAU,IAAI,CAAC,EAAE;QACtC,IAAI,CAAC3E,YAAY,GAAGyE,GAAG,CAACC,OAAO;QAC/B,IAAI,CAACvD,KAAK,GAAGsD,GAAG,CAACC,OAAO,CAACc,SAAS,GAAG,KAAK,GAAG,IAAI;QACjD,IAAIf,GAAG,CAACC,OAAO,CAACc,SAAS,EAAE;UAEzB,IAAI,CAACpF,aAAa,CAAC6B,OAAO,CAACH,IAAI,IAAI,IAAI,CAAClE,aAAa,CAACkE,IAAI,CAAC,GAAG,KAAK,CAAC;UACpE,IAAI,CAACzD,kBAAkB,CAAC4D,OAAO,CAACH,IAAI,IAAI,IAAI,CAAC7D,kBAAkB,CAAC6D,IAAI,CAAC,GAAG,KAAK,CAAC;UAE9E,IAAI,CAAC8C,kBAAkB,GAAGH,GAAG,CAACC,OAAO,CAACc,SAAS,CAACX,GAAG,CAAEC,CAAM,IAAI;YAC7D,OAAO;cACLE,OAAO,EAAE,IAAI,CAAChF,YAAY,CAACgF,OAAO;cAClCtI,cAAc,EAAEoI,CAAC,CAACpI,cAAc;cAChCuG,KAAK,EAAE6B,CAAC,CAAC7B,KAAK;cACd8B,kBAAkB,EAAED,CAAC,CAACC,kBAAkB;cACxCU,WAAW,EAAEX,CAAC,CAACW,WAAW;cAC1B1F,SAAS,EAAE+E,CAAC,CAAC/E,SAAS;cACtBF,KAAK,EAAEiF,CAAC,CAACjF,KAAK;cACdC,KAAK,EAAEgF,CAAC,CAAChF,KAAK;cACdrB,SAAS,EAAEqG,CAAC,CAACrG,SAAS,GAAGqG,CAAC,CAACrG,SAAS,GAAG,GAAGqG,CAAC,CAACjF,KAAK,IAAIiF,CAAC,CAAChF,KAAK,IAAIgF,CAAC,CAAC/E,SAAS,EAAE;cAC7E+D,WAAW,EAAEgB,CAAC,CAAChB,WAAW;cAC1BmB,YAAY,EAAEH,CAAC,CAACG,YAAY;cAC5B9F,cAAc,EAAE2F,CAAC,CAACK,OAAO,KAAG,CAAC,GAAG,CAAC,GAAEL,CAAC,CAAC3F,cAAc;cACnDgG,OAAO,EAAEL,CAAC,CAACK,OAAO;cAClBvH,aAAa,EAAEkH,CAAC,CAACY,qBAAqB,CAAC5I,MAAM,GAAG,IAAI,CAAC6I,0BAA0B,CAAC,IAAI,CAACvF,aAAa,EAAE0E,CAAC,CAACY,qBAAqB,CAAC,GAAG;gBAAE,GAAG,IAAI,CAAC9H;cAAa,CAAE;cACxJK,kBAAkB,EAAE6G,CAAC,CAAChB,WAAW,GAAG,IAAI,CAACD,kBAAkB,CAAC,IAAI,CAACxF,kBAAkB,EAAEyG,CAAC,CAAChB,WAAW,CAAC,GAAG;gBAAE,GAAG,IAAI,CAAC7F;cAAkB,CAAE;cACpIX,WAAW,EAAEwH,CAAC,CAACY,qBAAqB,CAAC5I,MAAM,IAAI,IAAI,CAACsD,aAAa,CAACtD,MAAM;cACxED,YAAY,EAAE,EAAE;cAChBwC,eAAe,EAAEyF,CAAC,CAACK,OAAO,GAAG,IAAI,CAACvD,cAAc,CAACkD,CAAC,CAACK,OAAO,EAAE,IAAI,CAAChF,cAAc,CAAC,GAAG,IAAI,CAACA,cAAc,CAAC,CAAC;aACzG;UACH,CAAC,CAAC;QACJ,CAAC,MAAM;UACL,IAAI,CAACgE,eAAe,EAAE;QACxB;MACF;IACF,CAAC,CAAC,CACH,CAAC7C,SAAS,EAAE;EACf;EAEA/B,mBAAmBA,CAACqG,WAAgB;IAClC,IAAGA,WAAW,CAACvG,eAAe,IAAIuG,WAAW,CAACvG,eAAe,CAACa,KAAK,KAAI,CAAC,EAAE;MACxE0F,WAAW,CAACzG,cAAc,GAAG,CAAC;IAChC;EACF;EAEA0G,4BAA4BA,CAACtJ,IAAW;IACtC,KAAK,IAAIuF,IAAI,IAAIvF,IAAI,EAAE;MACrB,IAAIuF,IAAI,CAACZ,WAAW,KAAK,IAAI,CAACF,8BAA8B,CAACE,WAAW,EAAE;QACxE,OAAOY,IAAI,CAACgE,cAAc;MAC5B;IACF;IACA,OAAO,EAAE;EACX;EAIAC,oBAAoBA,CAACC,GAA4B;IAC/C,OAAOC,MAAM,CAACC,IAAI,CAACF,GAAG,CAAC,CAAC5C,MAAM,CAAC+C,GAAG,IAAIH,GAAG,CAACG,GAAG,CAAC,CAAC;EACjD;EAEAC,0BAA0BA,CAACJ,GAA4B;IACrD,OAAOC,MAAM,CAACC,IAAI,CAACF,GAAG,CAAC,CACpB5C,MAAM,CAAC+C,GAAG,IAAIH,GAAG,CAACG,GAAG,CAAC,CAAC,CACvBE,IAAI,CAAC,GAAG,CAAC;EACd;EAEAC,cAAcA,CAACjH,eAAoB,EAAEpB,kBAAuB;IAC1D,IAAIoB,eAAe,IAAIA,eAAe,CAACa,KAAK,IAAI,CAAC,EAAE;MACjD,OAAO,IAAI,CAACkG,0BAA0B,CAACnI,kBAAkB,CAAC;IAC5D;EACF;EAEAsI,mBAAmBA,CAACC,WAAmB;IACrC,MAAMC,KAAK,GAAGD,WAAW,CAAC1D,KAAK,CAAC,GAAG,CAAC;IACpC,IAAI2D,KAAK,CAAC3J,MAAM,GAAG,CAAC,EAAE;MACpB,OAAO2J,KAAK,CAAC,CAAC,CAAC;IACjB,CAAC,MAAM,OAAO,EAAE;EAClB;EAEAC,UAAUA,CAAC7J,YAAiB;IAC1B,IAAIA,YAAY,IAAIA,YAAY,CAACC,MAAM,GAAG,CAAC,EAAE;MAC3C,OAAO;QACL6J,YAAY,EAAE,IAAI,CAACJ,mBAAmB,CAAC1J,YAAY,CAAC,CAAC,CAAC,CAACN,IAAI,CAAC,IAAI,IAAI;QACpEqK,aAAa,EAAE/J,YAAY,CAAC,CAAC,CAAC,CAACkG,SAAS,IAAI,IAAI;QAChD8D,QAAQ,EAAEhK,YAAY,CAAC,CAAC,CAAC,CAACoG,KAAK,CAAC3G,IAAI,IAAIO,YAAY,CAAC,CAAC,CAAC,CAACP,IAAI,IAAI;OACjE;IACH,CAAC,MAAM,OAAO4I,SAAS;EAEzB;EAEA;EACA;EACA;EACA;EACA;EACAE,UAAUA,CAAC0B,KAAoC;IAC7C,MAAMjC,GAAG,GAAG,IAAIkC,GAAG,EAAgE;IAEnFD,KAAK,CAAC7E,OAAO,CAACH,IAAI,IAAG;MACnB,MAAMqE,GAAG,GAAG,GAAGrE,IAAI,CAAC/B,SAAS,IAAI+B,IAAI,CAACjC,KAAK,IAAIiC,IAAI,CAAChC,KAAK,EAAE;MAC3D,IAAI+E,GAAG,CAACmC,GAAG,CAACb,GAAG,CAAC,EAAE;QAChB,MAAMc,QAAQ,GAAGpC,GAAG,CAACpD,GAAG,CAAC0E,GAAG,CAAE;QAC9Bc,QAAQ,CAACC,KAAK,IAAI,CAAC;MACrB,CAAC,MAAM;QACLrC,GAAG,CAACsC,GAAG,CAAChB,GAAG,EAAE;UAAErE,IAAI,EAAE;YAAE,GAAGA;UAAI,CAAE;UAAEoF,KAAK,EAAE;QAAC,CAAE,CAAC;MAC/C;IACF,CAAC,CAAC;IAEF,OAAOE,KAAK,CAACC,IAAI,CAACxC,GAAG,CAACyC,MAAM,EAAE,CAAC,CAACzC,GAAG,CAAC,CAAC;MAAE/C,IAAI;MAAEoF;IAAK,CAAE,MAAM;MACxD,GAAGpF,IAAI;MACPmD,YAAY,EAAEiC;KACf,CAAC,CAAC;EACL;EAGAK,UAAUA,CAAA;IACR,IAAI,CAAC1G,KAAK,CAAC2G,KAAK,EAAE;IAClB,IAAIC,iBAAiB,GAAG,KAAK;IAC7B,IAAIC,wBAAwB,GAAG,KAAK;IACpC,IAAIC,kBAAkB,GAAG,KAAK;IAC9B,KAAK,MAAM7F,IAAI,IAAI,IAAI,CAAC8F,mBAAmB,EAAE;MAC3C,IAAI,CAACH,iBAAiB,IAAK,CAAC3F,IAAI,CAACqD,OAAQ,EAAE;QACzCsC,iBAAiB,GAAG,IAAI;MAC1B;MACA,IAAI,CAACC,wBAAwB,IAAK,CAAC5F,IAAI,CAAC3C,cAAe,EAAE;QACvDuI,wBAAwB,GAAG,IAAI;MACjC;MAEA,IAAI5F,IAAI,CAACmD,YAAY,IAAInD,IAAI,CAAC3C,cAAc,EAAE;QAC5C,IAAI2C,IAAI,CAAC3C,cAAc,GAAG2C,IAAI,CAACmD,YAAY,EAAE;UAC3C,IAAI,CAACpE,KAAK,CAACgH,eAAe,CAAC,QAAQ,GAAG,MAAM,GAAG/F,IAAI,CAACmD,YAAY,GAAG,KAAKnD,IAAI,CAACrD,SAAS,IAAI,CAAC;QAC7F;MACF;MACA,IAAI,CAACkJ,kBAAkB,IAAK,CAAC7F,IAAI,CAACrD,SAAU,EAAE;QAC5CkJ,kBAAkB,GAAG,IAAI;MAC3B;IACF;IACA,IAAIF,iBAAiB,EAAE;MACrB,IAAI,CAAC5G,KAAK,CAACgH,eAAe,CAAC,UAAU,GAAG,KAAK,CAAC;IAChD;IACA,IAAIH,wBAAwB,EAAE;MAC5B,IAAI,CAAC7G,KAAK,CAACgH,eAAe,CAAC,QAAQ,GAAG,QAAQ,CAAC;IACjD;IACA,IAAIF,kBAAkB,EAAE;MACtB,IAAI,CAAC9G,KAAK,CAACgH,eAAe,CAAC,YAAY,GAAG,KAAK,CAAC;IAClD;EACF;EAIAC,QAAQA,CAAA;IACN,IAAI,CAACF,mBAAmB,GAAG,IAAI,CAAChD,kBAAkB,CAACC,GAAG,CAAEkD,CAAM,IAAI;MAChE,OAAO;QACLrL,cAAc,EAAEqL,CAAC,CAACrL,cAAc,GAAGqL,CAAC,CAACrL,cAAc,GAAG,IAAI;QAC1DuG,KAAK,EAAE8E,CAAC,CAAClL,YAAY,GAAG,IAAI,CAAC6J,UAAU,CAACqB,CAAC,CAAClL,YAAY,CAAC,GAAGqI,SAAS;QACnEH,kBAAkB,EAAE,IAAI,CAACgB,oBAAoB,CAACgC,CAAC,CAACnK,aAAa,CAAC;QAC9D6H,WAAW,EAAEsC,CAAC,CAACtC,WAAW,GAAGsC,CAAC,CAACtC,WAAW,GAAG,IAAI;QACjDuC,OAAO,EAAE,IAAI,CAAC7G,KAAK,GAAG,IAAI,GAAG,IAAI,CAACnB,YAAY,CAACgF,OAAO;QACtDnF,KAAK,EAAEkI,CAAC,CAAClI,KAAK;QACdC,KAAK,EAAEiI,CAAC,CAACjI,KAAK;QACdC,SAAS,EAAEgI,CAAC,CAAChI,SAAS;QACtBtB,SAAS,EAAEsJ,CAAC,CAACtJ,SAAS;QAAC;QACvBqF,WAAW,EAAEiE,CAAC,CAAC1I,eAAe,CAACa,KAAK,KAAK,CAAC,GAAG,IAAI,GAAG,IAAI,CAACoG,cAAc,CAACyB,CAAC,CAAC1I,eAAe,EAAE0I,CAAC,CAAC9J,kBAAkB,CAAC,IAAI,IAAI;QACxHgH,YAAY,EAAE8C,CAAC,CAAC9C,YAAY;QAC5B9F,cAAc,EAAE4I,CAAC,CAAC5I,cAAc;QAChCgG,OAAO,EAAE4C,CAAC,CAAC1I,eAAe,CAACa;OAC5B;IACH,CAAC,CAAC;IACF,IAAI,CAACqH,UAAU,EAAE;IACjB,IAAI,IAAI,CAAC1G,KAAK,CAACoH,aAAa,CAACnL,MAAM,GAAG,CAAC,EAAE;MACvC,IAAI,CAAC2D,OAAO,CAACyH,aAAa,CAAC,IAAI,CAACrH,KAAK,CAACoH,aAAa,CAAC;MACpD;IACF;IACA,IAAI,IAAI,CAAC9G,KAAK,EAAE;MACd,IAAI,CAACgH,kBAAkB,EAAE;IAE3B,CAAC,MAAM;MACL,IAAI,CAACC,gBAAgB,EAAE;IACzB;EACF;EAEAA,gBAAgBA,CAAA;IACd,IAAI,CAAC1H,gBAAgB,CAAC2H,oCAAoC,CAAC;MACzDhE,IAAI,EAAE,IAAI,CAACuD;KACZ,CAAC,CAACtG,SAAS,CAACmD,GAAG,IAAG;MACjB,IAAIA,GAAG,CAACE,UAAU,IAAI,CAAC,EAAE;QACvB,IAAI,CAAClE,OAAO,CAAC6H,aAAa,CAAC,MAAM,CAAC;QAClC;QACA,IAAI,CAACC,MAAM,EAAE;MACf;IACF,CAAC,CAAC;EACJ;EAIAJ,kBAAkBA,CAAA;IAChB,IAAI,CAACK,iBAAiB,GAAG;MACvBlE,YAAY,EAAE,IAAI,CAAC5C,WAAW;MAC9B+G,SAAS,EAAE,IAAI,CAACb,mBAAmB,IAAI,IAAI;MAC3C3G,SAAS,EAAE,IAAI,CAACD,8BAA8B,CAACC;KAChD;IACD,IAAI,CAACP,gBAAgB,CAACgI,sCAAsC,CAAC;MAC3DrE,IAAI,EAAE,IAAI,CAACmE;KACZ,CAAC,CAAClH,SAAS,CAACmD,GAAG,IAAG;MACjB,IAAIA,GAAG,CAACE,UAAU,IAAI,CAAC,EAAE;QACvB,IAAI,CAAClE,OAAO,CAAC6H,aAAa,CAAC,MAAM,CAAC;QAClC;QACA,IAAI,CAACC,MAAM,EAAE;MACf;IACF,CAAC,CAAC;EACJ;EAEA5C,0BAA0BA,CAACgD,CAAW,EAAEC,CAAkG;IACxI,MAAMC,CAAC,GAA+B,EAAE;IACxC,KAAK,MAAM/G,IAAI,IAAI6G,CAAC,EAAE;MACpB,MAAMG,YAAY,GAAGF,CAAC,CAACG,IAAI,CAACC,KAAK,IAAIA,KAAK,CAACC,UAAU,KAAKnH,IAAI,IAAIkH,KAAK,CAACE,SAAS,CAAC;MAClFL,CAAC,CAAC/G,IAAI,CAAC,GAAG,CAAC,CAACgH,YAAY;IAC1B;IACA,OAAOD,CAAC;EACV,CAAC,CAAC;EAKFlH,iCAAiCA,CAAA;IAC/B,IAAI,CAAChB,yBAAyB,CAACwI,8DAA8D,CAAC;MAC5F9E,IAAI,EAAE,IAAI,CAAC3C;KACZ,CAAC,CAAC8C,IAAI,CACL3J,GAAG,CAAC4J,GAAG,IAAG;MACR,IAAIA,GAAG,CAACC,OAAO,IAAID,GAAG,CAACE,UAAU,IAAI,CAAC,EAAE;QACtC,IAAI,CAACvE,aAAa,GAAG,IAAI,CAACyF,4BAA4B,CAACpB,GAAG,CAACC,OAAO,CAAC;QACnE,IAAI,CAACW,eAAe,EAAE;MACxB;IACF,CAAC,CAAC,CACH,CAAC/D,SAAS,EAAE;EACf;EACAiH,MAAMA,CAAA;IAAK,IAAI,CAACzH,QAAQ,CAACsI,IAAI,EAAE;EAAC;;;uCA1arB/I,yCAAyC,EAAAvF,EAAA,CAAAuO,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAzO,EAAA,CAAAuO,iBAAA,CAAAG,EAAA,CAAAC,cAAA,GAAA3O,EAAA,CAAAuO,iBAAA,CAAAK,EAAA,CAAAC,cAAA,GAAA7O,EAAA,CAAAuO,iBAAA,CAAAO,EAAA,CAAAC,eAAA,GAAA/O,EAAA,CAAAuO,iBAAA,CAAAO,EAAA,CAAAE,wBAAA,GAAAhP,EAAA,CAAAuO,iBAAA,CAAAU,EAAA,CAAAC,cAAA,GAAAlP,EAAA,CAAAuO,iBAAA,CAAAY,EAAA,CAAAC,gBAAA,GAAApP,EAAA,CAAAuO,iBAAA,CAAAc,EAAA,CAAAC,QAAA,GAAAtP,EAAA,CAAAuO,iBAAA,CAAAO,EAAA,CAAAS,eAAA;IAAA;EAAA;;;YAAzChK,yCAAyC;MAAAiK,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAA1P,EAAA,CAAA2P,0BAAA,EAAA3P,EAAA,CAAA4P,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,mDAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UC5CpDlQ,EADF,CAAAC,cAAA,iBAA0B,qBACR;UACdD,EAAA,CAAAiB,SAAA,qBAAiC;UACnCjB,EAAA,CAAAG,YAAA,EAAiB;UACjBH,EAAA,CAAAC,cAAA,mBAAc;UACZD,EAAA,CAAAiB,SAAA,YAA0C;UAGtCjB,EAFJ,CAAAC,cAAA,aAA8B,aACL,YACmB;UAAAD,EAAA,CAAAE,MAAA,4CAAO;UACjDF,EADiD,CAAAG,YAAA,EAAK,EAChD;UACNH,EAAA,CAAA6B,UAAA,IAAAuO,iEAAA,4BAA8E;UAiHlFpQ,EADE,CAAAG,YAAA,EAAM,EACO;UAEbH,EADF,CAAAC,cAAA,yBAAsD,iBACM;UAAnBD,EAAA,CAAAI,UAAA,mBAAAiQ,4EAAA;YAAA,OAASF,GAAA,CAAA1C,MAAA,EAAQ;UAAA,EAAC;UACvDzN,EAAA,CAAAE,MAAA,sBACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACTH,EAAA,CAAAC,cAAA,iBAAkD;UAArBD,EAAA,CAAAI,UAAA,mBAAAkQ,4EAAA;YAAA,OAASH,GAAA,CAAAnD,QAAA,EAAU;UAAA,EAAC;UAC/ChN,EAAA,CAAAE,MAAA,sBACF;UAEJF,EAFI,CAAAG,YAAA,EAAS,EACM,EACT;;;UA1HkCH,EAAA,CAAAsB,SAAA,GAAuB;UAAvBtB,EAAA,CAAAuB,UAAA,YAAA4O,GAAA,CAAArG,kBAAA,CAAuB;;;qBD+BvDlK,YAAY,EAAAyP,EAAA,CAAAkB,OAAA,EAAAlB,EAAA,CAAAmB,OAAA,EAAAnB,EAAA,CAAAoB,IAAA,EAAE9Q,YAAY,EAAA+Q,EAAA,CAAAC,oBAAA,EAAAD,EAAA,CAAAE,mBAAA,EAAAF,EAAA,CAAAG,eAAA,EAAAH,EAAA,CAAAI,OAAA,EAAAC,EAAA,CAAAC,eAAA,EAAAD,EAAA,CAAAE,mBAAA,EAAAF,EAAA,CAAAG,qBAAA,EAAAH,EAAA,CAAAI,qBAAA,EAAAJ,EAAA,CAAAK,mBAAA,EAAAL,EAAA,CAAAM,gBAAA,EAAAN,EAAA,CAAAO,iBAAA,EAAAP,EAAA,CAAAQ,iBAAA,EAAAC,GAAA,CAAAC,mBAAA,EAAAC,GAAA,CAAAC,YAAA,EAAE9R,gBAAgB;MAAA+R,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}