{"ast": null, "code": "import * as i0 from \"@angular/core\";\nexport let FileNamePipe = /*#__PURE__*/(() => {\n  class FileNamePipe {\n    transform(value) {\n      if (!value) return value;\n      const segments = value.split('/');\n      return segments.pop() || '';\n    }\n    static {\n      this.ɵfac = function FileNamePipe_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || FileNamePipe)();\n      };\n    }\n    static {\n      this.ɵpipe = /*@__PURE__*/i0.ɵɵdefinePipe({\n        name: \"fileName\",\n        type: FileNamePipe,\n        pure: true,\n        standalone: true\n      });\n    }\n  }\n  return FileNamePipe;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}