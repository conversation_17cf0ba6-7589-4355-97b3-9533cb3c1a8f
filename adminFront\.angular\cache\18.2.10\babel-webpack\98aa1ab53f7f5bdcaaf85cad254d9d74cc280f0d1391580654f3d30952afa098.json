{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nimport * as i2 from \"@angular/forms\";\nfunction TemplateViewerComponent_button_14_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 10);\n    i0.ɵɵlistener(\"click\", function TemplateViewerComponent_button_14_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.clearSearch());\n    });\n    i0.ɵɵelement(1, \"i\", 18);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction TemplateViewerComponent_tr_26_button_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 21);\n    i0.ɵɵlistener(\"click\", function TemplateViewerComponent_tr_26_button_8_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const tpl_r4 = i0.ɵɵnextContext().$implicit;\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(tpl_r4.TemplateID && ctx_r1.onDeleteTemplate(tpl_r4.TemplateID));\n    });\n    i0.ɵɵtext(1, \"\\u522A\\u9664\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction TemplateViewerComponent_tr_26_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\")(6, \"button\", 19);\n    i0.ɵɵlistener(\"click\", function TemplateViewerComponent_tr_26_Template_button_click_6_listener() {\n      const tpl_r4 = i0.ɵɵrestoreView(_r3).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onSelectTemplate(tpl_r4));\n    });\n    i0.ɵɵtext(7, \"\\u67E5\\u770B\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(8, TemplateViewerComponent_tr_26_button_8_Template, 2, 0, \"button\", 20);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const tpl_r4 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(tpl_r4.TemplateName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(tpl_r4.Description);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", tpl_r4.TemplateID);\n  }\n}\nfunction TemplateViewerComponent_tr_27_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 22);\n    i0.ɵɵtext(2, \"\\u66AB\\u7121\\u6A21\\u677F\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction TemplateViewerComponent_div_28_li_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const detail_r7 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate2(\" \", detail_r7.FieldName, \": \", detail_r7.FieldValue, \" \");\n  }\n}\nfunction TemplateViewerComponent_div_28_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 23)(1, \"h6\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"ul\");\n    i0.ɵɵtemplate(4, TemplateViewerComponent_div_28_li_4_Template, 2, 2, \"li\", 15);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 24);\n    i0.ɵɵlistener(\"click\", function TemplateViewerComponent_div_28_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.closeTemplateDetail());\n    });\n    i0.ɵɵtext(6, \"\\u95DC\\u9589\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\\u6A21\\u677F\\u7D30\\u7BC0\\uFF1A\", ctx_r1.selectedTemplate.TemplateName, \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.currentTemplateDetails);\n  }\n}\nexport class TemplateViewerComponent {\n  constructor() {\n    this.templates = [];\n    this.templateDetails = [];\n    this.sharedData = [];\n    this.addTemplate = new EventEmitter();\n    this.selectTemplate = new EventEmitter();\n    this.saveTemplate = new EventEmitter();\n    this.deleteTemplate = new EventEmitter();\n    this.showCreateTemplate = false;\n    this.newTemplateName = '';\n    this.newTemplateDesc = '';\n    this.selectedTemplate = null;\n    // 查詢功能\n    this.searchKeyword = '';\n    this.filteredTemplates = [];\n  }\n  ngOnInit() {\n    this.updateFilteredTemplates();\n  }\n  ngOnChanges() {\n    this.updateFilteredTemplates();\n  }\n  // 更新過濾後的模板列表\n  updateFilteredTemplates() {\n    if (!this.searchKeyword.trim()) {\n      this.filteredTemplates = [...this.templates];\n    } else {\n      const keyword = this.searchKeyword.toLowerCase();\n      this.filteredTemplates = this.templates.filter(template => template.TemplateName.toLowerCase().includes(keyword) || template.Description && template.Description.toLowerCase().includes(keyword));\n    }\n  }\n  // 搜尋模板\n  onSearch() {\n    this.updateFilteredTemplates();\n  }\n  // 清除搜尋\n  clearSearch() {\n    this.searchKeyword = '';\n    this.updateFilteredTemplates();\n  }\n  // 建立模板\n  createTemplate() {\n    const selected = (this.sharedData || []).filter(x => x.selected);\n    if (!this.newTemplateName || selected.length === 0) {\n      alert('請輸入模板名稱並選擇資料');\n      return;\n    }\n    const template = {\n      TemplateName: this.newTemplateName,\n      Description: this.newTemplateDesc\n    };\n    // details 依據選擇資料組成\n    const details = selected.map(x => ({\n      TemplateID: 0,\n      // 新增時由後端補上\n      RefID: x.ID || x.CRequirementID || 0,\n      ModuleType: x.ModuleType || 'Requirement',\n      FieldName: 'CRequirement',\n      FieldValue: x.CRequirement\n    }));\n    this.saveTemplate.emit({\n      template,\n      details\n    });\n    this.showCreateTemplate = false;\n    this.newTemplateName = '';\n    this.newTemplateDesc = '';\n    (this.sharedData || []).forEach(x => x.selected = false);\n  }\n  // 新增模板\n  onAddTemplate() {\n    this.addTemplate.emit();\n  }\n  // 查看模板\n  onSelectTemplate(template) {\n    this.selectedTemplate = template;\n    this.selectTemplate.emit(template);\n  }\n  // 刪除模板\n  onDeleteTemplate(templateID) {\n    if (confirm('確定刪除此模板？')) {\n      this.deleteTemplate.emit(templateID);\n    }\n  }\n  // 關閉模板詳情\n  closeTemplateDetail() {\n    this.selectedTemplate = null;\n  }\n  // 取得當前選中模板的詳情\n  get currentTemplateDetails() {\n    if (!this.selectedTemplate) {\n      return [];\n    }\n    return this.templateDetails.filter(d => d.TemplateID === this.selectedTemplate.TemplateID);\n  }\n  static {\n    this.ɵfac = function TemplateViewerComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || TemplateViewerComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: TemplateViewerComponent,\n      selectors: [[\"app-template-viewer\"]],\n      inputs: {\n        templates: \"templates\",\n        templateDetails: \"templateDetails\",\n        sharedData: \"sharedData\"\n      },\n      outputs: {\n        addTemplate: \"addTemplate\",\n        selectTemplate: \"selectTemplate\",\n        saveTemplate: \"saveTemplate\",\n        deleteTemplate: \"deleteTemplate\"\n      },\n      standalone: true,\n      features: [i0.ɵɵNgOnChangesFeature, i0.ɵɵStandaloneFeature],\n      decls: 29,\n      vars: 5,\n      consts: [[1, \"template-viewer-modal\"], [1, \"template-viewer-header\"], [1, \"d-flex\", \"justify-content-between\", \"align-items-center\", \"mb-3\"], [1, \"mb-0\"], [1, \"btn\", \"btn-success\", \"btn-sm\", 3, \"click\"], [1, \"fas\", \"fa-plus\", \"mr-1\"], [1, \"search-container\", \"mb-3\"], [1, \"input-group\"], [\"type\", \"text\", \"placeholder\", \"\\u641C\\u5C0B\\u6A21\\u677F\\u540D\\u7A31\\u6216\\u63CF\\u8FF0...\", 1, \"form-control\", 3, \"ngModelChange\", \"input\", \"keyup.enter\", \"ngModel\"], [1, \"input-group-append\"], [\"type\", \"button\", 1, \"btn\", \"btn-outline-secondary\", 3, \"click\"], [1, \"fas\", \"fa-search\"], [\"class\", \"btn btn-outline-secondary\", \"type\", \"button\", 3, \"click\", 4, \"ngIf\"], [1, \"template-list\"], [1, \"table\", \"table-bordered\", \"table-hover\"], [4, \"ngFor\", \"ngForOf\"], [4, \"ngIf\"], [\"class\", \"mt-3 p-2 border rounded bg-white\", 4, \"ngIf\"], [1, \"fas\", \"fa-times\"], [1, \"btn\", \"btn-info\", \"btn-sm\", \"mr-1\", 3, \"click\"], [\"class\", \"btn btn-danger btn-sm\", 3, \"click\", 4, \"ngIf\"], [1, \"btn\", \"btn-danger\", \"btn-sm\", 3, \"click\"], [\"colspan\", \"3\", 1, \"text-center\"], [1, \"mt-3\", \"p-2\", \"border\", \"rounded\", \"bg-white\"], [1, \"btn\", \"btn-secondary\", \"btn-sm\", 3, \"click\"]],\n      template: function TemplateViewerComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"h5\", 3);\n          i0.ɵɵtext(4, \"\\u6A21\\u677F\\u7BA1\\u7406\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(5, \"button\", 4);\n          i0.ɵɵlistener(\"click\", function TemplateViewerComponent_Template_button_click_5_listener() {\n            return ctx.onAddTemplate();\n          });\n          i0.ɵɵelement(6, \"i\", 5);\n          i0.ɵɵtext(7, \"\\u65B0\\u589E \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(8, \"div\", 6)(9, \"div\", 7)(10, \"input\", 8);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function TemplateViewerComponent_Template_input_ngModelChange_10_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.searchKeyword, $event) || (ctx.searchKeyword = $event);\n            return $event;\n          });\n          i0.ɵɵlistener(\"input\", function TemplateViewerComponent_Template_input_input_10_listener() {\n            return ctx.onSearch();\n          })(\"keyup.enter\", function TemplateViewerComponent_Template_input_keyup_enter_10_listener() {\n            return ctx.onSearch();\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(11, \"div\", 9)(12, \"button\", 10);\n          i0.ɵɵlistener(\"click\", function TemplateViewerComponent_Template_button_click_12_listener() {\n            return ctx.onSearch();\n          });\n          i0.ɵɵelement(13, \"i\", 11);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(14, TemplateViewerComponent_button_14_Template, 2, 0, \"button\", 12);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(15, \"div\", 13)(16, \"table\", 14)(17, \"thead\")(18, \"tr\")(19, \"th\");\n          i0.ɵɵtext(20, \"\\u6A21\\u677F\\u540D\\u7A31\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(21, \"th\");\n          i0.ɵɵtext(22, \"\\u63CF\\u8FF0\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(23, \"th\");\n          i0.ɵɵtext(24, \"\\u64CD\\u4F5C\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(25, \"tbody\");\n          i0.ɵɵtemplate(26, TemplateViewerComponent_tr_26_Template, 9, 3, \"tr\", 15)(27, TemplateViewerComponent_tr_27_Template, 3, 0, \"tr\", 16);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(28, TemplateViewerComponent_div_28_Template, 7, 2, \"div\", 17);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(10);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchKeyword);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", ctx.searchKeyword);\n          i0.ɵɵadvance(12);\n          i0.ɵɵproperty(\"ngForOf\", ctx.templates);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.templates || ctx.templates.length === 0);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.selectedTemplate);\n        }\n      },\n      dependencies: [CommonModule, i1.NgForOf, i1.NgIf, FormsModule, i2.DefaultValueAccessor, i2.NgControlStatus, i2.NgModel],\n      styles: [\".template-viewer-modal[_ngcontent-%COMP%] {\\n  background: #fff;\\n  border-radius: 8px;\\n  padding: 1.5rem;\\n  min-width: 400px;\\n  max-width: 600px;\\n}\\n\\n.template-viewer-header[_ngcontent-%COMP%] {\\n  border-bottom: 1px solid #e0e0e0;\\n  padding-bottom: 0.5rem;\\n  margin-bottom: 1rem;\\n}\\n\\n.table[_ngcontent-%COMP%] {\\n  background: #f9f9f9;\\n}\\n/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbInRlbXBsYXRlLXZpZXdlci5jb21wb25lbnQuc2NzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFBQTtFQUNJLGdCQUFBO0VBQ0Esa0JBQUE7RUFDQSxlQUFBO0VBQ0EsZ0JBQUE7RUFDQSxnQkFBQTtBQUNKOztBQUVBO0VBQ0ksZ0NBQUE7RUFDQSxzQkFBQTtFQUNBLG1CQUFBO0FBQ0o7O0FBRUE7RUFDSSxtQkFBQTtBQUNKIiwiZmlsZSI6InRlbXBsYXRlLXZpZXdlci5jb21wb25lbnQuc2NzcyIsInNvdXJjZXNDb250ZW50IjpbIi50ZW1wbGF0ZS12aWV3ZXItbW9kYWwge1xyXG4gICAgYmFja2dyb3VuZDogI2ZmZjtcclxuICAgIGJvcmRlci1yYWRpdXM6IDhweDtcclxuICAgIHBhZGRpbmc6IDEuNXJlbTtcclxuICAgIG1pbi13aWR0aDogNDAwcHg7XHJcbiAgICBtYXgtd2lkdGg6IDYwMHB4O1xyXG59XHJcblxyXG4udGVtcGxhdGUtdmlld2VyLWhlYWRlciB7XHJcbiAgICBib3JkZXItYm90dG9tOiAxcHggc29saWQgI2UwZTBlMDtcclxuICAgIHBhZGRpbmctYm90dG9tOiAwLjVyZW07XHJcbiAgICBtYXJnaW4tYm90dG9tOiAxcmVtO1xyXG59XHJcblxyXG4udGFibGUge1xyXG4gICAgYmFja2dyb3VuZDogI2Y5ZjlmOTtcclxufSJdfQ== */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvc2hhcmVkL2NvbXBvbmVudHMvdGVtcGxhdGUtdmlld2VyL3RlbXBsYXRlLXZpZXdlci5jb21wb25lbnQuc2NzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFBQTtFQUNJLGdCQUFBO0VBQ0Esa0JBQUE7RUFDQSxlQUFBO0VBQ0EsZ0JBQUE7RUFDQSxnQkFBQTtBQUNKOztBQUVBO0VBQ0ksZ0NBQUE7RUFDQSxzQkFBQTtFQUNBLG1CQUFBO0FBQ0o7O0FBRUE7RUFDSSxtQkFBQTtBQUNKO0FBQ0EsNDJCQUE0MkIiLCJzb3VyY2VzQ29udGVudCI6WyIudGVtcGxhdGUtdmlld2VyLW1vZGFsIHtcclxuICAgIGJhY2tncm91bmQ6ICNmZmY7XHJcbiAgICBib3JkZXItcmFkaXVzOiA4cHg7XHJcbiAgICBwYWRkaW5nOiAxLjVyZW07XHJcbiAgICBtaW4td2lkdGg6IDQwMHB4O1xyXG4gICAgbWF4LXdpZHRoOiA2MDBweDtcclxufVxyXG5cclxuLnRlbXBsYXRlLXZpZXdlci1oZWFkZXIge1xyXG4gICAgYm9yZGVyLWJvdHRvbTogMXB4IHNvbGlkICNlMGUwZTA7XHJcbiAgICBwYWRkaW5nLWJvdHRvbTogMC41cmVtO1xyXG4gICAgbWFyZ2luLWJvdHRvbTogMXJlbTtcclxufVxyXG5cclxuLnRhYmxlIHtcclxuICAgIGJhY2tncm91bmQ6ICNmOWY5Zjk7XHJcbn0iXSwic291cmNlUm9vdCI6IiJ9 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["EventEmitter", "CommonModule", "FormsModule", "i0", "ɵɵelementStart", "ɵɵlistener", "TemplateViewerComponent_button_14_Template_button_click_0_listener", "ɵɵrestoreView", "_r1", "ctx_r1", "ɵɵnextContext", "ɵɵresetView", "clearSearch", "ɵɵelement", "ɵɵelementEnd", "TemplateViewerComponent_tr_26_button_8_Template_button_click_0_listener", "_r5", "tpl_r4", "$implicit", "TemplateID", "onDeleteTemplate", "ɵɵtext", "TemplateViewerComponent_tr_26_Template_button_click_6_listener", "_r3", "onSelectTemplate", "ɵɵtemplate", "TemplateViewerComponent_tr_26_button_8_Template", "ɵɵadvance", "ɵɵtextInterpolate", "TemplateName", "Description", "ɵɵproperty", "ɵɵtextInterpolate2", "detail_r7", "FieldName", "FieldValue", "TemplateViewerComponent_div_28_li_4_Template", "TemplateViewerComponent_div_28_Template_button_click_5_listener", "_r6", "closeTemplateDetail", "ɵɵtextInterpolate1", "selectedTemplate", "currentTemplateDetails", "TemplateViewerComponent", "constructor", "templates", "templateDetails", "sharedData", "addTemplate", "selectTemplate", "saveTemplate", "deleteTemplate", "showCreateTemplate", "newTemplateName", "newTemplateDesc", "searchKeyword", "filteredTemplates", "ngOnInit", "updateFilteredTemplates", "ngOnChanges", "trim", "keyword", "toLowerCase", "filter", "template", "includes", "onSearch", "createTemplate", "selected", "x", "length", "alert", "details", "map", "RefID", "ID", "CRequirementID", "ModuleType", "CRequirement", "emit", "for<PERSON>ach", "onAddTemplate", "templateID", "confirm", "d", "selectors", "inputs", "outputs", "standalone", "features", "ɵɵNgOnChangesFeature", "ɵɵStandaloneFeature", "decls", "vars", "consts", "TemplateViewerComponent_Template", "rf", "ctx", "TemplateViewerComponent_Template_button_click_5_listener", "ɵɵtwoWayListener", "TemplateViewerComponent_Template_input_ngModelChange_10_listener", "$event", "ɵɵtwoWayBindingSet", "TemplateViewerComponent_Template_input_input_10_listener", "TemplateViewerComponent_Template_input_keyup_enter_10_listener", "TemplateViewerComponent_Template_button_click_12_listener", "TemplateViewerComponent_button_14_Template", "TemplateViewerComponent_tr_26_Template", "TemplateViewerComponent_tr_27_Template", "TemplateViewerComponent_div_28_Template", "ɵɵtwoWayProperty", "i1", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "i2", "DefaultValueAccessor", "NgControlStatus", "NgModel", "styles"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\shared\\components\\template-viewer\\template-viewer.component.ts", "C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\shared\\components\\template-viewer\\template-viewer.component.html"], "sourcesContent": ["import { Component, EventEmitter, Input, Output, OnInit, OnChanges } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { FormsModule } from '@angular/forms';\r\n\r\n@Component({\r\n  selector: 'app-template-viewer',\r\n  templateUrl: './template-viewer.component.html',\r\n  styleUrls: ['./template-viewer.component.scss'],\r\n  standalone: true,\r\n  imports: [CommonModule, FormsModule],\r\n})\r\nexport class TemplateViewerComponent implements OnInit, OnChanges {\r\n  @Input() templates: Template[] = [];\r\n  @Input() templateDetails: TemplateDetail[] = [];\r\n  @Input() sharedData: any[] = [];\r\n  @Output() addTemplate = new EventEmitter<Template>();\r\n  @Output() selectTemplate = new EventEmitter<Template>();\r\n  @Output() saveTemplate = new EventEmitter<{ template: Template, details: TemplateDetail[] }>();\r\n  @Output() deleteTemplate = new EventEmitter<number>();\r\n\r\n  showCreateTemplate = false;\r\n  newTemplateName = '';\r\n  newTemplateDesc = '';\r\n  selectedTemplate: Template | null = null;\r\n\r\n  // 查詢功能\r\n  searchKeyword = '';\r\n  filteredTemplates: Template[] = [];\r\n\r\n  ngOnInit() {\r\n    this.updateFilteredTemplates();\r\n  }\r\n\r\n  ngOnChanges() {\r\n    this.updateFilteredTemplates();\r\n  }\r\n\r\n  // 更新過濾後的模板列表\r\n  updateFilteredTemplates() {\r\n    if (!this.searchKeyword.trim()) {\r\n      this.filteredTemplates = [...this.templates];\r\n    } else {\r\n      const keyword = this.searchKeyword.toLowerCase();\r\n      this.filteredTemplates = this.templates.filter(template =>\r\n        template.TemplateName.toLowerCase().includes(keyword) ||\r\n        (template.Description && template.Description.toLowerCase().includes(keyword))\r\n      );\r\n    }\r\n  }\r\n\r\n  // 搜尋模板\r\n  onSearch() {\r\n    this.updateFilteredTemplates();\r\n  }\r\n\r\n  // 清除搜尋\r\n  clearSearch() {\r\n    this.searchKeyword = '';\r\n    this.updateFilteredTemplates();\r\n  }\r\n\r\n  // 建立模板\r\n  createTemplate() {\r\n    const selected = (this.sharedData || []).filter(x => x.selected);\r\n    if (!this.newTemplateName || selected.length === 0) {\r\n      alert('請輸入模板名稱並選擇資料');\r\n      return;\r\n    }\r\n    const template: Template = {\r\n      TemplateName: this.newTemplateName,\r\n      Description: this.newTemplateDesc\r\n    };\r\n    // details 依據選擇資料組成\r\n    const details: TemplateDetail[] = selected.map(x => ({\r\n      TemplateID: 0, // 新增時由後端補上\r\n      RefID: x.ID || x.CRequirementID || 0,\r\n      ModuleType: x.ModuleType || 'Requirement',\r\n      FieldName: 'CRequirement',\r\n      FieldValue: x.CRequirement\r\n    }));\r\n    this.saveTemplate.emit({ template, details });\r\n    this.showCreateTemplate = false;\r\n    this.newTemplateName = '';\r\n    this.newTemplateDesc = '';\r\n    (this.sharedData || []).forEach(x => x.selected = false);\r\n  }\r\n\r\n  // 新增模板\r\n  onAddTemplate() {\r\n    this.addTemplate.emit();\r\n  }\r\n\r\n  // 查看模板\r\n  onSelectTemplate(template: Template) {\r\n    this.selectedTemplate = template;\r\n    this.selectTemplate.emit(template);\r\n  }\r\n\r\n  // 刪除模板\r\n  onDeleteTemplate(templateID: number) {\r\n    if (confirm('確定刪除此模板？')) {\r\n      this.deleteTemplate.emit(templateID);\r\n    }\r\n  }\r\n\r\n  // 關閉模板詳情\r\n  closeTemplateDetail() {\r\n    this.selectedTemplate = null;\r\n  }\r\n\r\n  // 取得當前選中模板的詳情\r\n  get currentTemplateDetails(): TemplateDetail[] {\r\n    if (!this.selectedTemplate) {\r\n      return [];\r\n    }\r\n    return this.templateDetails.filter(d => d.TemplateID === this.selectedTemplate!.TemplateID);\r\n  }\r\n}\r\n\r\n// DB 對應型別\r\nexport interface Template {\r\n  TemplateID?: number;\r\n  TemplateName: string;\r\n  Description?: string;\r\n}\r\nexport interface TemplateDetail {\r\n  TemplateDetailID?: number;\r\n  TemplateID: number;\r\n  RefID: number; // 關聯主檔ID\r\n  ModuleType: string;\r\n  FieldName: string;\r\n  FieldValue: string;\r\n}\r\n", "<div class=\"template-viewer-modal\">\r\n  <div class=\"template-viewer-header\">\r\n    <div class=\"d-flex justify-content-between align-items-center mb-3\">\r\n      <h5 class=\"mb-0\">模板管理</h5>\r\n      <button class=\"btn btn-success btn-sm\" (click)=\"onAddTemplate()\">\r\n        <i class=\"fas fa-plus mr-1\"></i>新增\r\n      </button>\r\n    </div>\r\n\r\n    <!-- 搜尋功能 -->\r\n    <div class=\"search-container mb-3\">\r\n      <div class=\"input-group\">\r\n        <input type=\"text\" class=\"form-control\" placeholder=\"搜尋模板名稱或描述...\" [(ngModel)]=\"searchKeyword\"\r\n          (input)=\"onSearch()\" (keyup.enter)=\"onSearch()\">\r\n        <div class=\"input-group-append\">\r\n          <button class=\"btn btn-outline-secondary\" type=\"button\" (click)=\"onSearch()\">\r\n            <i class=\"fas fa-search\"></i>\r\n          </button>\r\n          <button class=\"btn btn-outline-secondary\" type=\"button\" (click)=\"clearSearch()\" *ngIf=\"searchKeyword\">\r\n            <i class=\"fas fa-times\"></i>\r\n          </button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n\r\n\r\n\r\n  <div class=\"template-list\">\r\n    <table class=\"table table-bordered table-hover\">\r\n      <thead>\r\n        <tr>\r\n          <th>模板名稱</th>\r\n          <th>描述</th>\r\n          <th>操作</th>\r\n        </tr>\r\n      </thead>\r\n      <tbody>\r\n        <tr *ngFor=\"let tpl of templates\">\r\n          <td>{{ tpl.TemplateName }}</td>\r\n          <td>{{ tpl.Description }}</td>\r\n          <td>\r\n            <button class=\"btn btn-info btn-sm mr-1\" (click)=\"onSelectTemplate(tpl)\">查看</button>\r\n            <button class=\"btn btn-danger btn-sm\" (click)=\"tpl.TemplateID && onDeleteTemplate(tpl.TemplateID)\"\r\n              *ngIf=\"tpl.TemplateID\">刪除</button>\r\n          </td>\r\n        </tr>\r\n        <tr *ngIf=\"!templates || templates.length === 0\">\r\n          <td colspan=\"3\" class=\"text-center\">暫無模板</td>\r\n        </tr>\r\n      </tbody>\r\n    </table>\r\n  </div>\r\n\r\n  <!-- 查看模板細節 -->\r\n  <div *ngIf=\"selectedTemplate\" class=\"mt-3 p-2 border rounded bg-white\">\r\n    <h6>模板細節：{{selectedTemplate!.TemplateName}}</h6>\r\n    <ul>\r\n      <li *ngFor=\"let detail of currentTemplateDetails\">\r\n        {{detail.FieldName}}: {{detail.FieldValue}}\r\n      </li>\r\n    </ul>\r\n    <button class=\"btn btn-secondary btn-sm\" (click)=\"closeTemplateDetail()\">關閉</button>\r\n  </div>\r\n</div>"], "mappings": "AAAA,SAAoBA,YAAY,QAA0C,eAAe;AACzF,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,QAAQ,gBAAgB;;;;;;;ICgBlCC,EAAA,CAAAC,cAAA,iBAAsG;IAA9CD,EAAA,CAAAE,UAAA,mBAAAC,mEAAA;MAAAH,EAAA,CAAAI,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAG,WAAA,EAAa;IAAA,EAAC;IAC7ET,EAAA,CAAAU,SAAA,YAA4B;IAC9BV,EAAA,CAAAW,YAAA,EAAS;;;;;;IAuBPX,EAAA,CAAAC,cAAA,iBACyB;IADaD,EAAA,CAAAE,UAAA,mBAAAU,wEAAA;MAAAZ,EAAA,CAAAI,aAAA,CAAAS,GAAA;MAAA,MAAAC,MAAA,GAAAd,EAAA,CAAAO,aAAA,GAAAQ,SAAA;MAAA,MAAAT,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAAM,MAAA,CAAAE,UAAA,IAA2BV,MAAA,CAAAW,gBAAA,CAAAH,MAAA,CAAAE,UAAA,CAAgC;IAAA,EAAC;IACzEhB,EAAA,CAAAkB,MAAA,mBAAE;IAAAlB,EAAA,CAAAW,YAAA,EAAS;;;;;;IALtCX,EADF,CAAAC,cAAA,SAAkC,SAC5B;IAAAD,EAAA,CAAAkB,MAAA,GAAsB;IAAAlB,EAAA,CAAAW,YAAA,EAAK;IAC/BX,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAkB,MAAA,GAAqB;IAAAlB,EAAA,CAAAW,YAAA,EAAK;IAE5BX,EADF,CAAAC,cAAA,SAAI,iBACuE;IAAhCD,EAAA,CAAAE,UAAA,mBAAAiB,+DAAA;MAAA,MAAAL,MAAA,GAAAd,EAAA,CAAAI,aAAA,CAAAgB,GAAA,EAAAL,SAAA;MAAA,MAAAT,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAe,gBAAA,CAAAP,MAAA,CAAqB;IAAA,EAAC;IAACd,EAAA,CAAAkB,MAAA,mBAAE;IAAAlB,EAAA,CAAAW,YAAA,EAAS;IACpFX,EAAA,CAAAsB,UAAA,IAAAC,+CAAA,qBACyB;IAE7BvB,EADE,CAAAW,YAAA,EAAK,EACF;;;;IAPCX,EAAA,CAAAwB,SAAA,GAAsB;IAAtBxB,EAAA,CAAAyB,iBAAA,CAAAX,MAAA,CAAAY,YAAA,CAAsB;IACtB1B,EAAA,CAAAwB,SAAA,GAAqB;IAArBxB,EAAA,CAAAyB,iBAAA,CAAAX,MAAA,CAAAa,WAAA,CAAqB;IAIpB3B,EAAA,CAAAwB,SAAA,GAAoB;IAApBxB,EAAA,CAAA4B,UAAA,SAAAd,MAAA,CAAAE,UAAA,CAAoB;;;;;IAIzBhB,EADF,CAAAC,cAAA,SAAiD,aACX;IAAAD,EAAA,CAAAkB,MAAA,+BAAI;IAC1ClB,EAD0C,CAAAW,YAAA,EAAK,EAC1C;;;;;IASPX,EAAA,CAAAC,cAAA,SAAkD;IAChDD,EAAA,CAAAkB,MAAA,GACF;IAAAlB,EAAA,CAAAW,YAAA,EAAK;;;;IADHX,EAAA,CAAAwB,SAAA,EACF;IADExB,EAAA,CAAA6B,kBAAA,MAAAC,SAAA,CAAAC,SAAA,QAAAD,SAAA,CAAAE,UAAA,MACF;;;;;;IAJFhC,EADF,CAAAC,cAAA,cAAuE,SACjE;IAAAD,EAAA,CAAAkB,MAAA,GAAuC;IAAAlB,EAAA,CAAAW,YAAA,EAAK;IAChDX,EAAA,CAAAC,cAAA,SAAI;IACFD,EAAA,CAAAsB,UAAA,IAAAW,4CAAA,iBAAkD;IAGpDjC,EAAA,CAAAW,YAAA,EAAK;IACLX,EAAA,CAAAC,cAAA,iBAAyE;IAAhCD,EAAA,CAAAE,UAAA,mBAAAgC,gEAAA;MAAAlC,EAAA,CAAAI,aAAA,CAAA+B,GAAA;MAAA,MAAA7B,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAA8B,mBAAA,EAAqB;IAAA,EAAC;IAACpC,EAAA,CAAAkB,MAAA,mBAAE;IAC7ElB,EAD6E,CAAAW,YAAA,EAAS,EAChF;;;;IAPAX,EAAA,CAAAwB,SAAA,GAAuC;IAAvCxB,EAAA,CAAAqC,kBAAA,mCAAA/B,MAAA,CAAAgC,gBAAA,CAAAZ,YAAA,KAAuC;IAElB1B,EAAA,CAAAwB,SAAA,GAAyB;IAAzBxB,EAAA,CAAA4B,UAAA,YAAAtB,MAAA,CAAAiC,sBAAA,CAAyB;;;AD/CtD,OAAM,MAAOC,uBAAuB;EAPpCC,YAAA;IAQW,KAAAC,SAAS,GAAe,EAAE;IAC1B,KAAAC,eAAe,GAAqB,EAAE;IACtC,KAAAC,UAAU,GAAU,EAAE;IACrB,KAAAC,WAAW,GAAG,IAAIhD,YAAY,EAAY;IAC1C,KAAAiD,cAAc,GAAG,IAAIjD,YAAY,EAAY;IAC7C,KAAAkD,YAAY,GAAG,IAAIlD,YAAY,EAAqD;IACpF,KAAAmD,cAAc,GAAG,IAAInD,YAAY,EAAU;IAErD,KAAAoD,kBAAkB,GAAG,KAAK;IAC1B,KAAAC,eAAe,GAAG,EAAE;IACpB,KAAAC,eAAe,GAAG,EAAE;IACpB,KAAAb,gBAAgB,GAAoB,IAAI;IAExC;IACA,KAAAc,aAAa,GAAG,EAAE;IAClB,KAAAC,iBAAiB,GAAe,EAAE;;EAElCC,QAAQA,CAAA;IACN,IAAI,CAACC,uBAAuB,EAAE;EAChC;EAEAC,WAAWA,CAAA;IACT,IAAI,CAACD,uBAAuB,EAAE;EAChC;EAEA;EACAA,uBAAuBA,CAAA;IACrB,IAAI,CAAC,IAAI,CAACH,aAAa,CAACK,IAAI,EAAE,EAAE;MAC9B,IAAI,CAACJ,iBAAiB,GAAG,CAAC,GAAG,IAAI,CAACX,SAAS,CAAC;IAC9C,CAAC,MAAM;MACL,MAAMgB,OAAO,GAAG,IAAI,CAACN,aAAa,CAACO,WAAW,EAAE;MAChD,IAAI,CAACN,iBAAiB,GAAG,IAAI,CAACX,SAAS,CAACkB,MAAM,CAACC,QAAQ,IACrDA,QAAQ,CAACnC,YAAY,CAACiC,WAAW,EAAE,CAACG,QAAQ,CAACJ,OAAO,CAAC,IACpDG,QAAQ,CAAClC,WAAW,IAAIkC,QAAQ,CAAClC,WAAW,CAACgC,WAAW,EAAE,CAACG,QAAQ,CAACJ,OAAO,CAAE,CAC/E;IACH;EACF;EAEA;EACAK,QAAQA,CAAA;IACN,IAAI,CAACR,uBAAuB,EAAE;EAChC;EAEA;EACA9C,WAAWA,CAAA;IACT,IAAI,CAAC2C,aAAa,GAAG,EAAE;IACvB,IAAI,CAACG,uBAAuB,EAAE;EAChC;EAEA;EACAS,cAAcA,CAAA;IACZ,MAAMC,QAAQ,GAAG,CAAC,IAAI,CAACrB,UAAU,IAAI,EAAE,EAAEgB,MAAM,CAACM,CAAC,IAAIA,CAAC,CAACD,QAAQ,CAAC;IAChE,IAAI,CAAC,IAAI,CAACf,eAAe,IAAIe,QAAQ,CAACE,MAAM,KAAK,CAAC,EAAE;MAClDC,KAAK,CAAC,cAAc,CAAC;MACrB;IACF;IACA,MAAMP,QAAQ,GAAa;MACzBnC,YAAY,EAAE,IAAI,CAACwB,eAAe;MAClCvB,WAAW,EAAE,IAAI,CAACwB;KACnB;IACD;IACA,MAAMkB,OAAO,GAAqBJ,QAAQ,CAACK,GAAG,CAACJ,CAAC,KAAK;MACnDlD,UAAU,EAAE,CAAC;MAAE;MACfuD,KAAK,EAAEL,CAAC,CAACM,EAAE,IAAIN,CAAC,CAACO,cAAc,IAAI,CAAC;MACpCC,UAAU,EAAER,CAAC,CAACQ,UAAU,IAAI,aAAa;MACzC3C,SAAS,EAAE,cAAc;MACzBC,UAAU,EAAEkC,CAAC,CAACS;KACf,CAAC,CAAC;IACH,IAAI,CAAC5B,YAAY,CAAC6B,IAAI,CAAC;MAAEf,QAAQ;MAAEQ;IAAO,CAAE,CAAC;IAC7C,IAAI,CAACpB,kBAAkB,GAAG,KAAK;IAC/B,IAAI,CAACC,eAAe,GAAG,EAAE;IACzB,IAAI,CAACC,eAAe,GAAG,EAAE;IACzB,CAAC,IAAI,CAACP,UAAU,IAAI,EAAE,EAAEiC,OAAO,CAACX,CAAC,IAAIA,CAAC,CAACD,QAAQ,GAAG,KAAK,CAAC;EAC1D;EAEA;EACAa,aAAaA,CAAA;IACX,IAAI,CAACjC,WAAW,CAAC+B,IAAI,EAAE;EACzB;EAEA;EACAvD,gBAAgBA,CAACwC,QAAkB;IACjC,IAAI,CAACvB,gBAAgB,GAAGuB,QAAQ;IAChC,IAAI,CAACf,cAAc,CAAC8B,IAAI,CAACf,QAAQ,CAAC;EACpC;EAEA;EACA5C,gBAAgBA,CAAC8D,UAAkB;IACjC,IAAIC,OAAO,CAAC,UAAU,CAAC,EAAE;MACvB,IAAI,CAAChC,cAAc,CAAC4B,IAAI,CAACG,UAAU,CAAC;IACtC;EACF;EAEA;EACA3C,mBAAmBA,CAAA;IACjB,IAAI,CAACE,gBAAgB,GAAG,IAAI;EAC9B;EAEA;EACA,IAAIC,sBAAsBA,CAAA;IACxB,IAAI,CAAC,IAAI,CAACD,gBAAgB,EAAE;MAC1B,OAAO,EAAE;IACX;IACA,OAAO,IAAI,CAACK,eAAe,CAACiB,MAAM,CAACqB,CAAC,IAAIA,CAAC,CAACjE,UAAU,KAAK,IAAI,CAACsB,gBAAiB,CAACtB,UAAU,CAAC;EAC7F;;;uCAzGWwB,uBAAuB;IAAA;EAAA;;;YAAvBA,uBAAuB;MAAA0C,SAAA;MAAAC,MAAA;QAAAzC,SAAA;QAAAC,eAAA;QAAAC,UAAA;MAAA;MAAAwC,OAAA;QAAAvC,WAAA;QAAAC,cAAA;QAAAC,YAAA;QAAAC,cAAA;MAAA;MAAAqC,UAAA;MAAAC,QAAA,GAAAtF,EAAA,CAAAuF,oBAAA,EAAAvF,EAAA,CAAAwF,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAA9B,QAAA,WAAA+B,iCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCR9B7F,EAHN,CAAAC,cAAA,aAAmC,aACG,aACkC,YACjD;UAAAD,EAAA,CAAAkB,MAAA,+BAAI;UAAAlB,EAAA,CAAAW,YAAA,EAAK;UAC1BX,EAAA,CAAAC,cAAA,gBAAiE;UAA1BD,EAAA,CAAAE,UAAA,mBAAA6F,yDAAA;YAAA,OAASD,GAAA,CAAAhB,aAAA,EAAe;UAAA,EAAC;UAC9D9E,EAAA,CAAAU,SAAA,WAAgC;UAAAV,EAAA,CAAAkB,MAAA,oBAClC;UACFlB,EADE,CAAAW,YAAA,EAAS,EACL;UAKFX,EAFJ,CAAAC,cAAA,aAAmC,aACR,gBAE2B;UADiBD,EAAA,CAAAgG,gBAAA,2BAAAC,iEAAAC,MAAA;YAAAlG,EAAA,CAAAmG,kBAAA,CAAAL,GAAA,CAAA1C,aAAA,EAAA8C,MAAA,MAAAJ,GAAA,CAAA1C,aAAA,GAAA8C,MAAA;YAAA,OAAAA,MAAA;UAAA,EAA2B;UACvElG,EAArB,CAAAE,UAAA,mBAAAkG,yDAAA;YAAA,OAASN,GAAA,CAAA/B,QAAA,EAAU;UAAA,EAAC,yBAAAsC,+DAAA;YAAA,OAAgBP,GAAA,CAAA/B,QAAA,EAAU;UAAA,EAAC;UADjD/D,EAAA,CAAAW,YAAA,EACkD;UAEhDX,EADF,CAAAC,cAAA,cAAgC,kBAC+C;UAArBD,EAAA,CAAAE,UAAA,mBAAAoG,0DAAA;YAAA,OAASR,GAAA,CAAA/B,QAAA,EAAU;UAAA,EAAC;UAC1E/D,EAAA,CAAAU,SAAA,aAA6B;UAC/BV,EAAA,CAAAW,YAAA,EAAS;UACTX,EAAA,CAAAsB,UAAA,KAAAiF,0CAAA,qBAAsG;UAM9GvG,EAHM,CAAAW,YAAA,EAAM,EACF,EACF,EACF;UAQEX,EAJR,CAAAC,cAAA,eAA2B,iBACuB,aACvC,UACD,UACE;UAAAD,EAAA,CAAAkB,MAAA,gCAAI;UAAAlB,EAAA,CAAAW,YAAA,EAAK;UACbX,EAAA,CAAAC,cAAA,UAAI;UAAAD,EAAA,CAAAkB,MAAA,oBAAE;UAAAlB,EAAA,CAAAW,YAAA,EAAK;UACXX,EAAA,CAAAC,cAAA,UAAI;UAAAD,EAAA,CAAAkB,MAAA,oBAAE;UAEVlB,EAFU,CAAAW,YAAA,EAAK,EACR,EACC;UACRX,EAAA,CAAAC,cAAA,aAAO;UAULD,EATA,CAAAsB,UAAA,KAAAkF,sCAAA,iBAAkC,KAAAC,sCAAA,iBASe;UAKvDzG,EAFI,CAAAW,YAAA,EAAQ,EACF,EACJ;UAGNX,EAAA,CAAAsB,UAAA,KAAAoF,uCAAA,kBAAuE;UASzE1G,EAAA,CAAAW,YAAA,EAAM;;;UApDqEX,EAAA,CAAAwB,SAAA,IAA2B;UAA3BxB,EAAA,CAAA2G,gBAAA,YAAAb,GAAA,CAAA1C,aAAA,CAA2B;UAMXpD,EAAA,CAAAwB,SAAA,GAAmB;UAAnBxB,EAAA,CAAA4B,UAAA,SAAAkE,GAAA,CAAA1C,aAAA,CAAmB;UAoBlFpD,EAAA,CAAAwB,SAAA,IAAY;UAAZxB,EAAA,CAAA4B,UAAA,YAAAkE,GAAA,CAAApD,SAAA,CAAY;UAS3B1C,EAAA,CAAAwB,SAAA,EAA0C;UAA1CxB,EAAA,CAAA4B,UAAA,UAAAkE,GAAA,CAAApD,SAAA,IAAAoD,GAAA,CAAApD,SAAA,CAAAyB,MAAA,OAA0C;UAQ/CnE,EAAA,CAAAwB,SAAA,EAAsB;UAAtBxB,EAAA,CAAA4B,UAAA,SAAAkE,GAAA,CAAAxD,gBAAA,CAAsB;;;qBD9ClBxC,YAAY,EAAA8G,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EAAE/G,WAAW,EAAAgH,EAAA,CAAAC,oBAAA,EAAAD,EAAA,CAAAE,eAAA,EAAAF,EAAA,CAAAG,OAAA;MAAAC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}