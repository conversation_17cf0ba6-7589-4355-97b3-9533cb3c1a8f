{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { concatMap, of, tap } from 'rxjs';\nimport { SharedModule } from '../../components/shared.module';\nimport { BaseComponent } from '../../components/base/baseComponent';\nimport * as J<PERSON><PERSON><PERSON> from 'jszip';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/shared/helper/allowHelper\";\nimport * as i2 from \"@nebular/theme\";\nimport * as i3 from \"src/app/shared/helper/validationHelper\";\nimport * as i4 from \"src/services/api/services\";\nimport * as i5 from \"src/app/shared/services/message.service\";\nimport * as i6 from \"src/app/shared/services/utility.service\";\nimport * as i7 from \"@angular/common\";\nimport * as i8 from \"@angular/forms\";\nimport * as i9 from \"../../components/breadcrumb/breadcrumb.component\";\nimport * as i10 from \"../../components/pagination/pagination.component\";\nimport * as i11 from \"../../../@theme/pipes/base-file.pipe\";\nfunction PictureMaterialComponent_nb_option_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 23);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const buildCase_r2 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", buildCase_r2.cID);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", buildCase_r2.CBuildCaseName, \" \");\n  }\n}\nfunction PictureMaterialComponent_nb_option_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 23);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const option_r3 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", option_r3.value);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", option_r3.label, \" \");\n  }\n}\nfunction PictureMaterialComponent_button_21_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 24);\n    i0.ɵɵlistener(\"click\", function PictureMaterialComponent_button_21_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r4 = i0.ɵɵnextContext();\n      const dialog_r6 = i0.ɵɵreference(46);\n      return i0.ɵɵresetView(ctx_r4.addNew(dialog_r6));\n    });\n    i0.ɵɵtext(1, \" \\u5716\\u7247\\u4E0A\\u50B3\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction PictureMaterialComponent_tr_42_button_17_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 27);\n    i0.ɵɵlistener(\"click\", function PictureMaterialComponent_tr_42_button_17_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const item_r8 = i0.ɵɵnextContext().$implicit;\n      const ctx_r4 = i0.ɵɵnextContext();\n      const dialog_r6 = i0.ɵɵreference(46);\n      return i0.ɵɵresetView(ctx_r4.addNew(dialog_r6, item_r8));\n    });\n    i0.ɵɵtext(1, \"\\u6AA2\\u8996\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction PictureMaterialComponent_tr_42_button_18_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 27);\n    i0.ɵɵlistener(\"click\", function PictureMaterialComponent_tr_42_button_18_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const item_r8 = i0.ɵɵnextContext().$implicit;\n      const ctx_r4 = i0.ɵɵnextContext();\n      const dialog_r6 = i0.ɵɵreference(46);\n      return i0.ɵɵresetView(ctx_r4.changePicture(dialog_r6, item_r8));\n    });\n    i0.ɵɵtext(1, \"\\u6539\\u8B8A\\u5716\\u7247\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction PictureMaterialComponent_tr_42_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"td\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"td\");\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"td\");\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"td\");\n    i0.ɵɵtext(14);\n    i0.ɵɵpipe(15, \"date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"td\", 25);\n    i0.ɵɵtemplate(17, PictureMaterialComponent_tr_42_button_17_Template, 2, 0, \"button\", 26)(18, PictureMaterialComponent_tr_42_button_18_Template, 2, 0, \"button\", 26);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const item_r8 = ctx.$implicit;\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r8.CId);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", item_r8 == null ? null : item_r8.CName, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r8.CPart);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r8.CLocation);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r8.CSelectName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r8.CPictureCode);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(15, 9, item_r8 == null ? null : item_r8.CUpdateDT, \"yyyy/MM/dd HH:mm:ss\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.isRead);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.isRead);\n  }\n}\nfunction PictureMaterialComponent_ng_template_45_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 34);\n    i0.ɵɵelement(1, \"img\", 35);\n    i0.ɵɵpipe(2, \"addBaseFile\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"src\", i0.ɵɵpipeBind1(2, 1, ctx_r4.currentImageShowing), i0.ɵɵsanitizeUrl);\n  }\n}\nfunction PictureMaterialComponent_ng_template_45_ng_template_6_tr_21_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r14 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\")(2, \"input\", 44);\n    i0.ɵɵlistener(\"blur\", function PictureMaterialComponent_ng_template_45_ng_template_6_tr_21_Template_input_blur_2_listener($event) {\n      const i_r15 = i0.ɵɵrestoreView(_r14).index;\n      const ctx_r4 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r4.renameFile($event, i_r15));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(3, \"td\", 45);\n    i0.ɵɵelement(4, \"img\", 35);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\", 25)(6, \"button\", 46);\n    i0.ɵɵlistener(\"click\", function PictureMaterialComponent_ng_template_45_ng_template_6_tr_21_Template_button_click_6_listener() {\n      const picture_r16 = i0.ɵɵrestoreView(_r14).$implicit;\n      const ctx_r4 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r4.removeImage(picture_r16.id));\n    });\n    i0.ɵɵtext(7, \"\\u5220\\u9664\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const picture_r16 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"value\", picture_r16.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", picture_r16.data, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction PictureMaterialComponent_ng_template_45_ng_template_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 36)(1, \"h6\");\n    i0.ɵɵtext(2, \"\\u4E0A\\u50B3\\u65B9\\u5F0F\\uFF1A\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"button\", 37);\n    i0.ɵɵlistener(\"click\", function PictureMaterialComponent_ng_template_45_ng_template_6_Template_button_click_3_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const inputFile_r12 = i0.ɵɵreference(8);\n      return i0.ɵɵresetView(inputFile_r12.click());\n    });\n    i0.ɵɵtext(4, \"\\u55AE\\u5F35\\u5716\\u7247\\u4E0A\\u50B3\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 38);\n    i0.ɵɵlistener(\"click\", function PictureMaterialComponent_ng_template_45_ng_template_6_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const zipInputFile_r13 = i0.ɵɵreference(10);\n      return i0.ɵɵresetView(zipInputFile_r13.click());\n    });\n    i0.ɵɵtext(6, \"ZIP \\u6279\\u91CF\\u532F\\u5165\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"input\", 39, 2);\n    i0.ɵɵlistener(\"change\", function PictureMaterialComponent_ng_template_45_ng_template_6_Template_input_change_7_listener($event) {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r4 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r4.detectFiles($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"input\", 40, 3);\n    i0.ɵɵlistener(\"change\", function PictureMaterialComponent_ng_template_45_ng_template_6_Template_input_change_9_listener($event) {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r4 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r4.detectFiles($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"div\", 41)(12, \"table\", 42)(13, \"thead\")(14, \"tr\", 18)(15, \"th\", 43);\n    i0.ɵɵtext(16, \"\\u6587\\u4EF6\\u540D\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"th\", 19);\n    i0.ɵɵtext(18, \"\\u6AA2\\u8996\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(19, \"th\", 19);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(20, \"tbody\");\n    i0.ɵɵtemplate(21, PictureMaterialComponent_ng_template_45_ng_template_6_tr_21_Template, 8, 2, \"tr\", 20);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassMap(!ctx_r4.isEdit ? \"btn btn-info mr-2\" : ctx_r4.listPictures.length < 1 ? \"btn btn-info mr-2\" : \"btn btn-info disable mr-2\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassMap(!ctx_r4.isEdit ? \"btn btn-success\" : \"btn btn-success disable\");\n    i0.ɵɵproperty(\"disabled\", ctx_r4.isEdit);\n    i0.ɵɵadvance(16);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r4.listPictures);\n  }\n}\nfunction PictureMaterialComponent_ng_template_45_button_12_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r18 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 47);\n    i0.ɵɵlistener(\"click\", function PictureMaterialComponent_ng_template_45_button_12_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r18);\n      const ref_r17 = i0.ɵɵnextContext().dialogRef;\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.uploadImage(ref_r17));\n    });\n    i0.ɵɵtext(1, \"\\u5132\\u5B58\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction PictureMaterialComponent_ng_template_45_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-card\", 28)(1, \"nb-card-header\")(2, \"span\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"nb-card-body\", 29);\n    i0.ɵɵtemplate(5, PictureMaterialComponent_ng_template_45_div_5_Template, 3, 3, \"div\", 30)(6, PictureMaterialComponent_ng_template_45_ng_template_6_Template, 22, 6, \"ng-template\", null, 1, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"nb-card-footer\")(9, \"div\", 31)(10, \"button\", 32);\n    i0.ɵɵlistener(\"click\", function PictureMaterialComponent_ng_template_45_Template_button_click_10_listener() {\n      const ref_r17 = i0.ɵɵrestoreView(_r10).dialogRef;\n      const ctx_r4 = i0.ɵɵnextContext();\n      ref_r17.close();\n      return i0.ɵɵresetView(ctx_r4.currentImageShowing = \"\");\n    });\n    i0.ɵɵtext(11, \"\\u53D6\\u6D88\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(12, PictureMaterialComponent_ng_template_45_button_12_Template, 2, 0, \"button\", 33);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const upload_r19 = i0.ɵɵreference(7);\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r4.currentImageShowing ? \"\\u6AA2\\u8996\" : (ctx_r4.selectedCategory === 1 ? \"\\u5EFA\\u6750\\u5716\\u7247\" : \"\\u793A\\u610F\\u5716\\u7247\") + \"\\u4E0A\\u50B3\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.currentImageShowing)(\"ngIfElse\", upload_r19);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r4.currentImageShowing);\n  }\n}\n// 圖片類別枚舉\nvar PictureCategory;\n(function (PictureCategory) {\n  PictureCategory[PictureCategory[\"NONE\"] = 0] = \"NONE\";\n  PictureCategory[PictureCategory[\"BUILDING_MATERIAL\"] = 1] = \"BUILDING_MATERIAL\";\n  PictureCategory[PictureCategory[\"SCHEMATIC\"] = 2] = \"SCHEMATIC\"; // 示意圖片\n})(PictureCategory || (PictureCategory = {}));\nexport class PictureMaterialComponent extends BaseComponent {\n  constructor(_allow, dialogService, valid, _pictureService, _infoPictureService, _buildCaseService, message, _utilityService) {\n    super(_allow);\n    this._allow = _allow;\n    this.dialogService = dialogService;\n    this.valid = valid;\n    this._pictureService = _pictureService;\n    this._infoPictureService = _infoPictureService;\n    this._buildCaseService = _buildCaseService;\n    this.message = message;\n    this._utilityService = _utilityService;\n    this.images = [];\n    this.listUserBuildCases = [];\n    this.selectedCategory = PictureCategory.NONE;\n    this.isCategorySelected = false; // 追蹤用戶是否已經明確選擇類別\n    this.currentImageShowing = \"\";\n    this.listPictures = [];\n    this.isEdit = false;\n    // 類別選項\n    this.categoryOptions = [{\n      value: PictureCategory.BUILDING_MATERIAL,\n      label: '建材圖片'\n    }, {\n      value: PictureCategory.SCHEMATIC,\n      label: '示意圖片'\n    }];\n    // 讓模板可以使用 enum\n    this.PictureCategory = PictureCategory;\n  }\n  ngOnInit() {\n    this.getListBuildCase();\n  }\n  getListBuildCase() {\n    this._buildCaseService.apiBuildCaseGetUserBuildCasePost$Json({}).pipe(tap(res => {\n      if (res.StatusCode === 0) {\n        this.listUserBuildCases = res.Entries ?? [];\n        this.selectedBuildCaseId = this.listUserBuildCases[0].cID;\n      }\n    })\n    // 移除自動呼叫 getPicturelList，讓用戶先選擇類別\n    ).subscribe();\n  }\n  openPdfInNewTab(CFileUrl) {\n    if (CFileUrl) {\n      this._utilityService.openFileInNewTab(CFileUrl);\n    }\n  }\n  getPicturelList(pageIndex) {\n    if (this.selectedCategory === 1) {\n      // 建材圖片\n      return this._pictureService.apiPictureGetPicturelListPost$Json({\n        body: {\n          PageIndex: pageIndex,\n          PageSize: this.pageSize,\n          CBuildCaseId: this.selectedBuildCaseId\n        }\n      }).pipe(tap(res => {\n        if (res.StatusCode == 0) {\n          this.images = res.Entries ?? [];\n          this.totalRecords = res.TotalItems;\n        }\n      }));\n    } else if (this.selectedCategory === 2) {\n      // 示意圖片\n      return this._infoPictureService.apiInfoPictureGetInfoPicturelListPost$Json({\n        body: {\n          PageIndex: pageIndex,\n          PageSize: this.pageSize,\n          CBuildCaseId: this.selectedBuildCaseId\n        }\n      }).pipe(tap(res => {\n        if (res.StatusCode == 0) {\n          this.images = res.Entries ?? [];\n          this.totalRecords = res.TotalItems;\n        }\n      }));\n    } else {\n      // 如果沒有選擇類別，清空數據並返回預設的空 observable\n      this.images = [];\n      this.totalRecords = 0;\n      return this._pictureService.apiPictureGetPicturelListPost$Json({\n        body: {\n          PageIndex: 1,\n          PageSize: 1,\n          CBuildCaseId: -1 // 使用無效 ID 確保返回空結果\n        }\n      }).pipe(tap(() => {\n        this.images = [];\n        this.totalRecords = 0;\n      }));\n    }\n  }\n  pageChanged(pageIndex) {\n    // this.pageIndex = newPage;\n    this.getPicturelList(pageIndex).subscribe();\n  }\n  selectedChange(buildCaseId) {\n    this.selectedBuildCaseId = buildCaseId;\n    this.getPicturelList(1).subscribe();\n  }\n  categoryChanged(category) {\n    this.selectedCategory = category;\n    this.isCategorySelected = true; // 標記用戶已經選擇了類別\n    this.getPicturelList(1).subscribe();\n  }\n  addNew(ref, item) {\n    // 如果是新增圖片（沒有傳入 item），則檢查是否已選擇類別\n    if (!item && !this.isCategorySelected) {\n      this.message.showErrorMSG('請先選擇圖片類別');\n      return;\n    }\n    this.listPictures = [];\n    this.dialogService.open(ref);\n    this.isEdit = false;\n    if (!!item) {\n      this.currentImageShowing = item.CFile;\n    } else {\n      this.listPictures = [];\n    }\n  }\n  changePicture(ref, item) {\n    // 檢查是否已選擇類別\n    if (!this.isCategorySelected) {\n      this.message.showErrorMSG('請先選擇圖片類別');\n      return;\n    }\n    if (!!item && item.CId) {\n      this.dialogService.open(ref);\n      this.isEdit = true;\n      this.currentEditItem = item.CId;\n      this.listPictures = [];\n    }\n  }\n  validation(CFile) {\n    this.valid.clear();\n    const nameSet = new Set();\n    for (const item of CFile) {\n      if (nameSet.has(item.name)) {\n        this.valid.addErrorMessage('檔名不可重複');\n        return;\n      }\n      nameSet.add(item.name);\n    }\n  }\n  onSubmit(ref) {}\n  detectFiles(event) {\n    for (let index = 0; index < event.target.files.length; index++) {\n      const file = event.target.files[index];\n      if (file) {\n        // 檢查是否為 ZIP 檔案\n        if (file.type === 'application/zip' || file.name.toLowerCase().endsWith('.zip')) {\n          this.processZipFile(file);\n        } else {\n          this.processSingleImageFile(file);\n        }\n      }\n    }\n    // Reset input file to be able to select the old file again\n    event.target.value = null;\n  }\n  processZipFile(zipFile) {\n    const zip = new JSZip();\n    zip.loadAsync(zipFile).then(contents => {\n      const imageFiles = [];\n      contents.forEach((relativePath, file) => {\n        // 只處理圖片檔案，跳過資料夾\n        if (!file.dir && this.isImageFile(relativePath)) {\n          imageFiles.push(file.async('blob').then(blob => {\n            // 建立 File 物件\n            const imageFile = new File([blob], relativePath, {\n              type: this.getImageMimeType(relativePath)\n            });\n            return this.processImageFileFromZip(imageFile, relativePath);\n          }));\n        }\n      });\n      // 處理所有圖片檔案\n      Promise.all(imageFiles).then(() => {\n        this.message.showSucessMSG(`成功從 ZIP 檔案中匯入 ${imageFiles.length} 張圖片`);\n      }).catch(error => {\n        console.error('處理 ZIP 檔案中的圖片時發生錯誤:', error);\n        this.message.showErrorMSG('處理 ZIP 檔案中的圖片時發生錯誤');\n      });\n    }).catch(error => {\n      console.error('讀取 ZIP 檔案時發生錯誤:', error);\n      this.message.showErrorMSG('無法讀取 ZIP 檔案，請確認檔案格式正確');\n    });\n  }\n  processSingleImageFile(file) {\n    let reader = new FileReader();\n    reader.readAsDataURL(file);\n    reader.onload = () => {\n      let base64Str = reader.result;\n      if (!base64Str) {\n        return;\n      }\n      this.addImageToList(file, base64Str);\n    };\n  }\n  processImageFileFromZip(file, originalPath) {\n    return new Promise((resolve, reject) => {\n      let reader = new FileReader();\n      reader.readAsDataURL(file);\n      reader.onload = () => {\n        let base64Str = reader.result;\n        if (!base64Str) {\n          reject('無法讀取圖片檔案');\n          return;\n        }\n        this.addImageToList(file, base64Str, originalPath);\n        resolve();\n      };\n      reader.onerror = () => {\n        reject('讀取圖片檔案時發生錯誤');\n      };\n    });\n  }\n  addImageToList(file, base64Str, originalPath) {\n    // Get name file ( no extension)\n    const fileName = originalPath || file.name;\n    const fileNameWithoutExtension = fileName.split('.')[0];\n    // Find files with duplicate names\n    const existingFileIndex = this.listPictures.findIndex(picture => picture.name === fileNameWithoutExtension);\n    if (existingFileIndex !== -1) {\n      // If name is duplicate, update file data\n      this.listPictures[existingFileIndex] = {\n        ...this.listPictures[existingFileIndex],\n        data: base64Str,\n        CFile: file,\n        extension: this._utilityService.getFileExtension(fileName)\n      };\n    } else {\n      // If not duplicate, add new file\n      this.listPictures.push({\n        id: new Date().getTime() + Math.random(),\n        name: fileNameWithoutExtension,\n        data: base64Str,\n        extension: this._utilityService.getFileExtension(fileName),\n        CFile: file\n      });\n    }\n  }\n  isImageFile(fileName) {\n    const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp'];\n    const extension = fileName.toLowerCase().substring(fileName.lastIndexOf('.'));\n    return imageExtensions.includes(extension);\n  }\n  getImageMimeType(fileName) {\n    const extension = fileName.toLowerCase().substring(fileName.lastIndexOf('.'));\n    switch (extension) {\n      case '.jpg':\n      case '.jpeg':\n        return 'image/jpeg';\n      case '.png':\n        return 'image/png';\n      case '.gif':\n        return 'image/gif';\n      case '.bmp':\n        return 'image/bmp';\n      case '.webp':\n        return 'image/webp';\n      default:\n        return 'image/jpeg';\n    }\n  }\n  removeImage(pictureId) {\n    this.listPictures = this.listPictures.filter(x => x.id != pictureId);\n  }\n  uploadImage(ref) {\n    if (!this.isEdit) {\n      const CFile = this.listPictures.map(x => x.CFile);\n      this.validation(CFile);\n      if (this.valid.errorMessages.length > 0) {\n        this.message.showErrorMSGs(this.valid.errorMessages);\n        return;\n      } // 根據選擇的類別使用不同的服務\n      const uploadRequest = this.selectedCategory === 1 // 建材圖片\n      ? this._pictureService.apiPictureUploadListPicturePost$Json({\n        body: {\n          CBuildCaseId: this.selectedBuildCaseId,\n          CPath: \"picture\",\n          CFile: CFile\n        }\n      }) : this._infoPictureService.apiInfoPictureUploadListInfoPicturePost$Json({\n        body: {\n          CBuildCaseId: this.selectedBuildCaseId,\n          CPath: \"infoPicture\",\n          CFile: CFile\n        }\n      });\n      uploadRequest.pipe(tap(res => {\n        if (res.StatusCode == 0) {\n          this.message.showSucessMSG('執行成功');\n          this.listPictures = [];\n        } else {\n          this.message.showErrorMSG(res.Message);\n        }\n        ref.close();\n      }), concatMap(res => res.StatusCode == 0 ? this.getPicturelList(1) : of(null))).subscribe();\n    } else {\n      if (this.listPictures.length > 0 && this.listPictures[0].CFile) {\n        // 根據選擇的類別使用不同的更新服務\n        const updateRequest = this.selectedCategory === 1 // 建材圖片\n        ? this._pictureService.apiPictureUpdatePicturePost$Json({\n          body: {\n            CBuildCaseID: this.selectedBuildCaseId,\n            CPictureID: this.currentEditItem,\n            CFile: this.listPictures[0].CFile\n          }\n        }) : this._infoPictureService.apiInfoPictureUpdateInfoPicturePost$Json({\n          body: {\n            CBuildCaseId: this.selectedBuildCaseId,\n            CInfoPictureID: this.currentEditItem,\n            CFile: this.listPictures[0].CFile\n          }\n        });\n        updateRequest.pipe(tap(res => {\n          if (res.StatusCode == 0) {\n            this.message.showSucessMSG('執行成功');\n            this.listPictures = [];\n          } else {\n            this.message.showErrorMSG(res.Message);\n          }\n          ref.close();\n        }), concatMap(res => res.StatusCode == 0 ? this.getPicturelList(1) : of(null))).subscribe();\n      }\n    }\n  }\n  renameFile(event, index) {\n    var blob = this.listPictures[index].CFile.slice(0, this.listPictures[index].CFile.size, this.listPictures[index].CFile.type);\n    var newFile = new File([blob], `${event.target.value + '.' + this.listPictures[index].extension}`, {\n      type: this.listPictures[index].CFile.type\n    });\n    this.listPictures[index].CFile = newFile;\n  }\n  static {\n    this.ɵfac = function PictureMaterialComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || PictureMaterialComponent)(i0.ɵɵdirectiveInject(i1.AllowHelper), i0.ɵɵdirectiveInject(i2.NbDialogService), i0.ɵɵdirectiveInject(i3.ValidationHelper), i0.ɵɵdirectiveInject(i4.PictureService), i0.ɵɵdirectiveInject(i4.InfoPictureService), i0.ɵɵdirectiveInject(i4.BuildCaseService), i0.ɵɵdirectiveInject(i5.MessageService), i0.ɵɵdirectiveInject(i6.UtilityService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: PictureMaterialComponent,\n      selectors: [[\"ngx-picture-material\"]],\n      standalone: true,\n      features: [i0.ɵɵInheritDefinitionFeature, i0.ɵɵStandaloneFeature],\n      decls: 47,\n      vars: 9,\n      consts: [[\"dialog\", \"\"], [\"upload\", \"\"], [\"inputFile\", \"\"], [\"zipInputFile\", \"\"], [\"accent\", \"success\"], [1, \"font-bold\", \"text-[#818181]\"], [1, \"d-flex\", \"flex-wrap\"], [1, \"col-md-4\"], [1, \"form-group\", \"d-flex\", \"align-items-center\", \"w-full\"], [\"for\", \"buildingName\", 1, \"label\", \"mr-2\"], [\"placeholder\", \"\\u5EFA\\u6848\", 1, \"w-full\", 3, \"ngModelChange\", \"selectedChange\", \"ngModel\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"for\", \"category\", 1, \"label\", \"mr-2\"], [\"placeholder\", \"\\u5716\\u7247\\u985E\\u5225\", 1, \"w-full\", 3, \"ngModelChange\", \"selectedChange\", \"ngModel\"], [1, \"d-flex\", \"justify-content-end\", \"w-full\"], [\"class\", \"btn btn-info\", 3, \"click\", 4, \"ngIf\"], [1, \"table-responsive\", \"mt-4\"], [1, \"table\", \"table-striped\", \"border\", 2, \"min-width\", \"1000px\", \"background-color\", \"#f3f3f3\"], [2, \"background-color\", \"#27ae60\", \"color\", \"white\"], [\"scope\", \"col\", 1, \"col-1\"], [4, \"ngFor\", \"ngForOf\"], [1, \"d-flex\", \"justify-content-center\"], [3, \"CollectionSizeChange\", \"PageSizeChange\", \"PageChange\", \"CollectionSize\", \"PageSize\", \"Page\"], [3, \"value\"], [1, \"btn\", \"btn-info\", 3, \"click\"], [1, \"text-center\", \"w-32\"], [\"class\", \"btn btn-outline-primary btn-sm m-1\", 3, \"click\", 4, \"ngIf\"], [1, \"btn\", \"btn-outline-primary\", \"btn-sm\", \"m-1\", 3, \"click\"], [1, \"mr-md-5\", \"ml-md-5\", 2, \"height\", \"100%\", \"overflow\", \"auto\", \"width\", \"700px\"], [2, \"padding\", \"1rem 2rem\"], [\"class\", \"w-full h-auto\", 4, \"ngIf\", \"ngIfElse\"], [1, \"flex\", \"justify-center\", \"items-center\"], [1, \"btn\", \"btn-danger\", \"mr-2\", 3, \"click\"], [\"class\", \"btn btn-success\", 3, \"click\", 4, \"ngIf\"], [1, \"w-full\", \"h-auto\"], [1, \"fit-size\", 3, \"src\"], [1, \"mb-3\"], [3, \"click\"], [3, \"click\", \"disabled\"], [\"type\", \"file\", \"accept\", \"image/png, image/gif, image/jpeg\", \"multiple\", \"\", 1, \"hidden\", 3, \"change\"], [\"type\", \"file\", \"accept\", \".zip,application/zip\", 1, \"hidden\", 3, \"change\"], [1, \"mt-3\", \"w-full\", \"flex\", \"flex-col\"], [1, \"table\", \"table-striped\", \"border\", 2, \"background-color\", \"#f3f3f3\"], [\"scope\", \"col\", 1, \"col-4\"], [\"nbInput\", \"\", \"type\", \"text\", 1, \"w-100\", \"p-2\", \"text-[13px]\", 3, \"blur\", \"value\"], [1, \"w-[100px]\", \"h-auto\"], [1, \"btn\", \"btn-outline-danger\", \"btn-sm\", \"m-1\", 3, \"click\"], [1, \"btn\", \"btn-success\", 3, \"click\"]],\n      template: function PictureMaterialComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"nb-card\", 4)(1, \"nb-card-header\");\n          i0.ɵɵelement(2, \"ngx-breadcrumb\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(3, \"nb-card-body\")(4, \"h1\", 5);\n          i0.ɵɵtext(5, \"\\u53EF\\u8A2D\\u5B9A\\u4E0A\\u50B3\\u5EFA\\u6750\\u793A\\u610F\\u5716\\u7247\\uFF0C\\u4E0A\\u50B3\\u524D\\u8ACB\\u5C07\\u5716\\u7247\\u6A94\\u6848\\u6539\\u70BA\\u5EFA\\u6750\\u5716\\u7247\\u6A94\\u540D\\u3002\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(6, \"div\", 6)(7, \"div\", 7)(8, \"div\", 8)(9, \"label\", 9);\n          i0.ɵɵtext(10, \"\\u5EFA\\u6848\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(11, \"nb-select\", 10);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function PictureMaterialComponent_Template_nb_select_ngModelChange_11_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.selectedBuildCaseId, $event) || (ctx.selectedBuildCaseId = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵlistener(\"selectedChange\", function PictureMaterialComponent_Template_nb_select_selectedChange_11_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.selectedChange($event));\n          });\n          i0.ɵɵtemplate(12, PictureMaterialComponent_nb_option_12_Template, 2, 2, \"nb-option\", 11);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(13, \"div\", 7)(14, \"div\", 8)(15, \"label\", 12);\n          i0.ɵɵtext(16, \"\\u985E\\u5225\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(17, \"nb-select\", 13);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function PictureMaterialComponent_Template_nb_select_ngModelChange_17_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.selectedCategory, $event) || (ctx.selectedCategory = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵlistener(\"selectedChange\", function PictureMaterialComponent_Template_nb_select_selectedChange_17_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.categoryChanged($event));\n          });\n          i0.ɵɵtemplate(18, PictureMaterialComponent_nb_option_18_Template, 2, 2, \"nb-option\", 11);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(19, \"div\", 7)(20, \"div\", 14);\n          i0.ɵɵtemplate(21, PictureMaterialComponent_button_21_Template, 2, 0, \"button\", 15);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(22, \"div\", 16)(23, \"table\", 17)(24, \"thead\")(25, \"tr\", 18)(26, \"th\", 19);\n          i0.ɵɵtext(27, \"\\u9805\\u6B21\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(28, \"th\", 19);\n          i0.ɵɵtext(29, \"\\u540D\\u7A31\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(30, \"th\", 19);\n          i0.ɵɵtext(31, \"\\u9805\\u76EE\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(32, \"th\", 19);\n          i0.ɵɵtext(33, \"\\u4F4D\\u7F6E\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(34, \"th\", 19);\n          i0.ɵɵtext(35, \"\\u5EFA\\u6750\\u9078\\u9805\\u540D\\u7A31\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(36, \"th\", 19);\n          i0.ɵɵtext(37, \"\\u5EFA\\u6750\\u5716\\u7247\\u6A94\\u540D\\uFF08\\u76F8\\u540C\\u5EFA\\u6848\\u4E0D\\u53EF\\u91CD\\u8907\\uFF09\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(38, \"th\", 19);\n          i0.ɵɵtext(39, \"\\u6700\\u65B0\\u5716\\u7247\\u4E0A\\u50B3\\u6642\\u9593\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(40, \"th\", 19);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(41, \"tbody\");\n          i0.ɵɵtemplate(42, PictureMaterialComponent_tr_42_Template, 19, 12, \"tr\", 20);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(43, \"nb-card-footer\", 21)(44, \"ngx-pagination\", 22);\n          i0.ɵɵtwoWayListener(\"CollectionSizeChange\", function PictureMaterialComponent_Template_ngx_pagination_CollectionSizeChange_44_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.totalRecords, $event) || (ctx.totalRecords = $event);\n            return i0.ɵɵresetView($event);\n          })(\"PageSizeChange\", function PictureMaterialComponent_Template_ngx_pagination_PageSizeChange_44_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.pageSize, $event) || (ctx.pageSize = $event);\n            return i0.ɵɵresetView($event);\n          })(\"PageChange\", function PictureMaterialComponent_Template_ngx_pagination_PageChange_44_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.pageIndex, $event) || (ctx.pageIndex = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵlistener(\"PageChange\", function PictureMaterialComponent_Template_ngx_pagination_PageChange_44_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.pageChanged($event));\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(45, PictureMaterialComponent_ng_template_45_Template, 13, 4, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor);\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(11);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.selectedBuildCaseId);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngForOf\", ctx.listUserBuildCases);\n          i0.ɵɵadvance(5);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.selectedCategory);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngForOf\", ctx.categoryOptions);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", ctx.isCreate);\n          i0.ɵɵadvance(21);\n          i0.ɵɵproperty(\"ngForOf\", ctx.images);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtwoWayProperty(\"CollectionSize\", ctx.totalRecords)(\"PageSize\", ctx.pageSize)(\"Page\", ctx.pageIndex);\n        }\n      },\n      dependencies: [CommonModule, i7.NgForOf, i7.NgIf, i7.DatePipe, SharedModule, i8.NgControlStatus, i8.NgModel, i2.NbCardComponent, i2.NbCardBodyComponent, i2.NbCardFooterComponent, i2.NbCardHeaderComponent, i2.NbInputDirective, i2.NbSelectComponent, i2.NbOptionComponent, i9.BreadcrumbComponent, i10.PaginationComponent, i11.BaseFilePipe],\n      styles: [\".disable[_ngcontent-%COMP%] {\\n  opacity: 0.5;\\n  pointer-events: none;\\n}\\n/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbInBpY3R1cmUtbWF0ZXJpYWwuY29tcG9uZW50LnNjc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUE7RUFDRSxZQUFBO0VBQ0Esb0JBQUE7QUFDRiIsImZpbGUiOiJwaWN0dXJlLW1hdGVyaWFsLmNvbXBvbmVudC5zY3NzIiwic291cmNlc0NvbnRlbnQiOlsiLmRpc2FibGV7XHJcbiAgb3BhY2l0eTogLjU7XHJcbiAgcG9pbnRlci1ldmVudHM6IG5vbmU7XHJcbn1cclxuIl19 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvcGFnZXMvc2VsZWN0aW9uLW1hbmFnZW1lbnQvcGljdHVyZS1tYXRlcmlhbC9waWN0dXJlLW1hdGVyaWFsLmNvbXBvbmVudC5zY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUFBO0VBQ0UsWUFBQTtFQUNBLG9CQUFBO0FBQ0Y7QUFDQSxvWEFBb1giLCJzb3VyY2VzQ29udGVudCI6WyIuZGlzYWJsZXtcclxuICBvcGFjaXR5OiAuNTtcclxuICBwb2ludGVyLWV2ZW50czogbm9uZTtcclxufVxyXG4iXSwic291cmNlUm9vdCI6IiJ9 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "concatMap", "of", "tap", "SharedModule", "BaseComponent", "JSZip", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵproperty", "buildCase_r2", "cID", "ɵɵadvance", "ɵɵtextInterpolate1", "CBuildCaseName", "option_r3", "value", "label", "ɵɵlistener", "PictureMaterialComponent_button_21_Template_button_click_0_listener", "ɵɵrestoreView", "_r4", "ctx_r4", "ɵɵnextContext", "dialog_r6", "ɵɵreference", "ɵɵresetView", "addNew", "PictureMaterialComponent_tr_42_button_17_Template_button_click_0_listener", "_r7", "item_r8", "$implicit", "PictureMaterialComponent_tr_42_button_18_Template_button_click_0_listener", "_r9", "changePicture", "ɵɵtemplate", "PictureMaterialComponent_tr_42_button_17_Template", "PictureMaterialComponent_tr_42_button_18_Template", "ɵɵtextInterpolate", "CId", "CName", "<PERSON>art", "CLocation", "CSelectName", "CPictureCode", "ɵɵpipeBind2", "CUpdateDT", "isRead", "ɵɵelement", "ɵɵpipeBind1", "currentImageShowing", "ɵɵsanitizeUrl", "PictureMaterialComponent_ng_template_45_ng_template_6_tr_21_Template_input_blur_2_listener", "$event", "i_r15", "_r14", "index", "renameFile", "PictureMaterialComponent_ng_template_45_ng_template_6_tr_21_Template_button_click_6_listener", "picture_r16", "removeImage", "id", "name", "data", "PictureMaterialComponent_ng_template_45_ng_template_6_Template_button_click_3_listener", "_r11", "inputFile_r12", "click", "PictureMaterialComponent_ng_template_45_ng_template_6_Template_button_click_5_listener", "zipInputFile_r13", "PictureMaterialComponent_ng_template_45_ng_template_6_Template_input_change_7_listener", "detectFiles", "PictureMaterialComponent_ng_template_45_ng_template_6_Template_input_change_9_listener", "PictureMaterialComponent_ng_template_45_ng_template_6_tr_21_Template", "ɵɵclassMap", "isEdit", "listPictures", "length", "PictureMaterialComponent_ng_template_45_button_12_Template_button_click_0_listener", "_r18", "ref_r17", "dialogRef", "uploadImage", "PictureMaterialComponent_ng_template_45_div_5_Template", "PictureMaterialComponent_ng_template_45_ng_template_6_Template", "ɵɵtemplateRefExtractor", "PictureMaterialComponent_ng_template_45_Template_button_click_10_listener", "_r10", "close", "PictureMaterialComponent_ng_template_45_button_12_Template", "selectedCate<PERSON><PERSON>", "upload_r19", "PictureCategory", "PictureMaterialComponent", "constructor", "_allow", "dialogService", "valid", "_pictureService", "_infoPictureService", "_buildCaseService", "message", "_utilityService", "images", "listUserBuildCases", "NONE", "isCategorySelected", "categoryOptions", "BUILDING_MATERIAL", "SCHEMATIC", "ngOnInit", "getListBuildCase", "apiBuildCaseGetUserBuildCasePost$Json", "pipe", "res", "StatusCode", "Entries", "selectedBuildCaseId", "subscribe", "openPdfInNewTab", "CFileUrl", "openFileInNewTab", "getPicturelList", "pageIndex", "apiPictureGetPicturelListPost$Json", "body", "PageIndex", "PageSize", "pageSize", "CBuildCaseId", "totalRecords", "TotalItems", "apiInfoPictureGetInfoPicturelListPost$Json", "pageChanged", "<PERSON><PERSON><PERSON><PERSON>", "buildCaseId", "categoryChanged", "category", "ref", "item", "showErrorMSG", "open", "CFile", "currentEditItem", "validation", "clear", "nameSet", "Set", "has", "addErrorMessage", "add", "onSubmit", "event", "target", "files", "file", "type", "toLowerCase", "endsWith", "processZipFile", "processSingleImageFile", "zipFile", "zip", "loadAsync", "then", "contents", "imageFiles", "for<PERSON>ach", "relativePath", "dir", "isImageFile", "push", "async", "blob", "imageFile", "File", "getImageMimeType", "processImageFileFromZip", "Promise", "all", "showSucessMSG", "catch", "error", "console", "reader", "FileReader", "readAsDataURL", "onload", "base64Str", "result", "addImageToList", "originalPath", "resolve", "reject", "onerror", "fileName", "fileNameWithoutExtension", "split", "existingFileIndex", "findIndex", "picture", "extension", "getFileExtension", "Date", "getTime", "Math", "random", "imageExtensions", "substring", "lastIndexOf", "includes", "pictureId", "filter", "x", "map", "errorMessages", "showErrorMSGs", "uploadRequest", "apiPictureUploadListPicturePost$Json", "CPath", "apiInfoPictureUploadListInfoPicturePost$Json", "Message", "updateRequest", "apiPictureUpdatePicturePost$Json", "CBuildCaseID", "CPictureID", "apiInfoPictureUpdateInfoPicturePost$Json", "CInfoPictureID", "slice", "size", "newFile", "ɵɵdirectiveInject", "i1", "AllowHelper", "i2", "NbDialogService", "i3", "ValidationHelper", "i4", "PictureService", "InfoPictureService", "BuildCaseService", "i5", "MessageService", "i6", "UtilityService", "selectors", "standalone", "features", "ɵɵInheritDefinitionFeature", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "PictureMaterialComponent_Template", "rf", "ctx", "ɵɵtwoWayListener", "PictureMaterialComponent_Template_nb_select_ngModelChange_11_listener", "_r1", "ɵɵtwoWayBindingSet", "PictureMaterialComponent_Template_nb_select_selected<PERSON><PERSON>e_11_listener", "PictureMaterialComponent_nb_option_12_Template", "PictureMaterialComponent_Template_nb_select_ngModelChange_17_listener", "PictureMaterialComponent_Template_nb_select_selected<PERSON><PERSON>e_17_listener", "PictureMaterialComponent_nb_option_18_Template", "PictureMaterialComponent_button_21_Template", "PictureMaterialComponent_tr_42_Template", "PictureMaterialComponent_Template_ngx_pagination_CollectionSizeChange_44_listener", "PictureMaterialComponent_Template_ngx_pagination_PageSizeChange_44_listener", "PictureMaterialComponent_Template_ngx_pagination_PageChange_44_listener", "PictureMaterialComponent_ng_template_45_Template", "ɵɵtwoWayProperty", "isCreate", "i7", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "DatePipe", "i8", "NgControlStatus", "NgModel", "NbCardComponent", "NbCardBodyComponent", "NbCardFooterComponent", "NbCardHeaderComponent", "NbInputDirective", "NbSelectComponent", "NbOptionComponent", "i9", "BreadcrumbComponent", "i10", "PaginationComponent", "i11", "BaseFilePipe", "styles"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\selection-management\\picture-material\\picture-material.component.ts", "C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\selection-management\\picture-material\\picture-material.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { CommonModule, formatDate } from '@angular/common';\r\nimport { NbDialogService } from '@nebular/theme';\r\nimport { ValidationHelper } from 'src/app/shared/helper/validationHelper';\r\nimport { BuildCaseService, PictureService, InfoPictureService } from 'src/services/api/services';\r\nimport { BuildCaseGetListReponse, GetPictureListResponse, GetInfoPictureListResponse } from 'src/services/api/models';\r\nimport { AllowHelper } from 'src/app/shared/helper/allowHelper';\r\nimport { concatMap, finalize, of, tap } from 'rxjs';\r\nimport { BaseFilePipe } from 'src/app/@theme/pipes/base-file.pipe';\r\nimport { MessageService } from 'src/app/shared/services/message.service';\r\nimport { UtilityService } from 'src/app/shared/services/utility.service';\r\nimport { SharedModule } from '../../components/shared.module';\r\nimport { BaseComponent } from '../../components/base/baseComponent';\r\nimport * as JSZip from 'jszip';\r\n\r\n// 圖片類別枚舉\r\nenum PictureCategory {\r\n  NONE = 0,           // 未選擇\r\n  BUILDING_MATERIAL = 1,  // 建材圖片\r\n  SCHEMATIC = 2       // 示意圖片\r\n}\r\n\r\n@Component({\r\n  selector: 'ngx-picture-material',\r\n  templateUrl: './picture-material.component.html',\r\n  styleUrls: ['./picture-material.component.scss'],\r\n  standalone: true,\r\n  imports: [\r\n    CommonModule,\r\n    SharedModule,\r\n    BaseFilePipe\r\n  ],\r\n})\r\n\r\nexport class PictureMaterialComponent extends BaseComponent implements OnInit {\r\n\r\n  images: (GetPictureListResponse | GetInfoPictureListResponse)[] = [];\r\n  listUserBuildCases: BuildCaseGetListReponse[] = [];\r\n  selectedBuildCaseId: number;  selectedCategory: PictureCategory = PictureCategory.NONE;\r\n  isCategorySelected: boolean = false; // 追蹤用戶是否已經明確選擇類別\r\n\r\n  currentImageShowing: string = \"\"\r\n\r\n  listPictures: any[] = []\r\n\r\n  isEdit: boolean = false;\r\n  currentEditItem: number;\r\n\r\n  // 類別選項\r\n  categoryOptions = [\r\n    { value: PictureCategory.BUILDING_MATERIAL, label: '建材圖片' },\r\n    { value: PictureCategory.SCHEMATIC, label: '示意圖片' }\r\n  ]\r\n\r\n  // 讓模板可以使用 enum\r\n  PictureCategory = PictureCategory;\r\n\r\n  constructor(\r\n    private _allow: AllowHelper,\r\n    private dialogService: NbDialogService,\r\n    private valid: ValidationHelper,\r\n    private _pictureService: PictureService,\r\n    private _infoPictureService: InfoPictureService,\r\n    private _buildCaseService: BuildCaseService,\r\n    private message: MessageService,\r\n    private _utilityService: UtilityService\r\n  ) {\r\n    super(_allow)\r\n  }\r\n\r\n  override ngOnInit(): void {\r\n    this.getListBuildCase();\r\n  }\r\n  getListBuildCase() {\r\n    this._buildCaseService.apiBuildCaseGetUserBuildCasePost$Json({})\r\n      .pipe(\r\n        tap(res => {\r\n          if (res.StatusCode === 0) {\r\n            this.listUserBuildCases = res.Entries! ?? []\r\n            this.selectedBuildCaseId = this.listUserBuildCases[0].cID!\r\n          }\r\n        })\r\n        // 移除自動呼叫 getPicturelList，讓用戶先選擇類別\r\n      ).subscribe()\r\n  }\r\n\r\n  openPdfInNewTab(CFileUrl?: any) {\r\n    if (CFileUrl) {\r\n      this._utilityService.openFileInNewTab(CFileUrl)\r\n    }\r\n  } getPicturelList(pageIndex: number) {\r\n    if (this.selectedCategory === 1) { // 建材圖片\r\n      return this._pictureService.apiPictureGetPicturelListPost$Json({\r\n        body: {\r\n          PageIndex: pageIndex,\r\n          PageSize: this.pageSize,\r\n          CBuildCaseId: this.selectedBuildCaseId\r\n        }\r\n      }).pipe(\r\n        tap(res => {\r\n          if (res.StatusCode == 0) {\r\n            this.images = res.Entries! ?? []\r\n            this.totalRecords = res.TotalItems!;\r\n          }\r\n        })\r\n      )\r\n    } else if (this.selectedCategory === 2) { // 示意圖片\r\n      return this._infoPictureService.apiInfoPictureGetInfoPicturelListPost$Json({\r\n        body: {\r\n          PageIndex: pageIndex,\r\n          PageSize: this.pageSize,\r\n          CBuildCaseId: this.selectedBuildCaseId\r\n        }\r\n      }).pipe(\r\n        tap(res => {\r\n          if (res.StatusCode == 0) {\r\n            this.images = res.Entries! ?? []\r\n            this.totalRecords = res.TotalItems!;\r\n          }\r\n        })\r\n      )\r\n    } else {\r\n      // 如果沒有選擇類別，清空數據並返回預設的空 observable\r\n      this.images = [];\r\n      this.totalRecords = 0;\r\n      return this._pictureService.apiPictureGetPicturelListPost$Json({\r\n        body: {\r\n          PageIndex: 1,\r\n          PageSize: 1,\r\n          CBuildCaseId: -1 // 使用無效 ID 確保返回空結果\r\n        }\r\n      }).pipe(\r\n        tap(() => {\r\n          this.images = [];\r\n          this.totalRecords = 0;\r\n        })\r\n      )\r\n    }\r\n  } pageChanged(pageIndex: number) {\r\n    // this.pageIndex = newPage;\r\n    this.getPicturelList(pageIndex).subscribe();\r\n  }\r\n  selectedChange(buildCaseId: number) {\r\n    this.selectedBuildCaseId = buildCaseId;\r\n    this.getPicturelList(1).subscribe();\r\n  } categoryChanged(category: number) {\r\n    this.selectedCategory = category;\r\n    this.isCategorySelected = true; // 標記用戶已經選擇了類別\r\n    this.getPicturelList(1).subscribe();\r\n  } addNew(ref: any, item?: GetPictureListResponse | GetInfoPictureListResponse) {\r\n    // 如果是新增圖片（沒有傳入 item），則檢查是否已選擇類別\r\n    if (!item && !this.isCategorySelected) {\r\n      this.message.showErrorMSG('請先選擇圖片類別');\r\n      return;\r\n    }\r\n\r\n    this.listPictures = []\r\n    this.dialogService.open(ref)\r\n    this.isEdit = false;\r\n    if (!!item) {\r\n      this.currentImageShowing = item.CFile!\r\n    }\r\n    else {\r\n      this.listPictures = [];\r\n    }\r\n  } changePicture(ref: any, item?: GetPictureListResponse | GetInfoPictureListResponse) {\r\n    // 檢查是否已選擇類別\r\n    if (!this.isCategorySelected) {\r\n      this.message.showErrorMSG('請先選擇圖片類別');\r\n      return;\r\n    }\r\n\r\n    if (!!item && item.CId) {\r\n      this.dialogService.open(ref)\r\n      this.isEdit = true;\r\n      this.currentEditItem = item.CId;\r\n      this.listPictures = [];\r\n    }\r\n  }\r\n\r\n  validation(CFile: any) {\r\n    this.valid.clear();\r\n    const nameSet = new Set();\r\n    for (const item of CFile) {\r\n      if (nameSet.has(item.name)) {\r\n        this.valid.addErrorMessage('檔名不可重複')\r\n        return;\r\n      }\r\n      nameSet.add(item.name);\r\n    }\r\n  }\r\n\r\n  onSubmit(ref: any) {\r\n  }\r\n  detectFiles(event: any) {\r\n    for (let index = 0; index < event.target.files.length; index++) {\r\n      const file = event.target.files[index];\r\n      if (file) {\r\n        // 檢查是否為 ZIP 檔案\r\n        if (file.type === 'application/zip' || file.name.toLowerCase().endsWith('.zip')) {\r\n          this.processZipFile(file);\r\n        } else {\r\n          this.processSingleImageFile(file);\r\n        }\r\n      }\r\n    }\r\n    // Reset input file to be able to select the old file again\r\n    event.target.value = null;\r\n  }\r\n\r\n  processZipFile(zipFile: File) {\r\n    const zip = new JSZip();\r\n\r\n    zip.loadAsync(zipFile).then((contents) => {\r\n      const imageFiles: Promise<any>[] = [];\r\n\r\n      contents.forEach((relativePath, file) => {\r\n        // 只處理圖片檔案，跳過資料夾\r\n        if (!file.dir && this.isImageFile(relativePath)) {\r\n          imageFiles.push(\r\n            file.async('blob').then((blob) => {\r\n              // 建立 File 物件\r\n              const imageFile = new File([blob], relativePath, { type: this.getImageMimeType(relativePath) });\r\n              return this.processImageFileFromZip(imageFile, relativePath);\r\n            })\r\n          );\r\n        }\r\n      });\r\n\r\n      // 處理所有圖片檔案\r\n      Promise.all(imageFiles).then(() => {\r\n        this.message.showSucessMSG(`成功從 ZIP 檔案中匯入 ${imageFiles.length} 張圖片`);\r\n      }).catch((error) => {\r\n        console.error('處理 ZIP 檔案中的圖片時發生錯誤:', error);\r\n        this.message.showErrorMSG('處理 ZIP 檔案中的圖片時發生錯誤');\r\n      });\r\n\r\n    }).catch((error) => {\r\n      console.error('讀取 ZIP 檔案時發生錯誤:', error);\r\n      this.message.showErrorMSG('無法讀取 ZIP 檔案，請確認檔案格式正確');\r\n    });\r\n  }\r\n\r\n  processSingleImageFile(file: File) {\r\n    let reader = new FileReader();\r\n    reader.readAsDataURL(file);\r\n    reader.onload = () => {\r\n      let base64Str: string = reader.result as string;\r\n      if (!base64Str) {\r\n        return;\r\n      }\r\n      this.addImageToList(file, base64Str);\r\n    };\r\n  }\r\n\r\n  processImageFileFromZip(file: File, originalPath: string): Promise<void> {\r\n    return new Promise((resolve, reject) => {\r\n      let reader = new FileReader();\r\n      reader.readAsDataURL(file);\r\n      reader.onload = () => {\r\n        let base64Str: string = reader.result as string;\r\n        if (!base64Str) {\r\n          reject('無法讀取圖片檔案');\r\n          return;\r\n        }\r\n        this.addImageToList(file, base64Str, originalPath);\r\n        resolve();\r\n      };\r\n      reader.onerror = () => {\r\n        reject('讀取圖片檔案時發生錯誤');\r\n      };\r\n    });\r\n  }\r\n\r\n  addImageToList(file: File, base64Str: string, originalPath?: string) {\r\n    // Get name file ( no extension)\r\n    const fileName = originalPath || file.name;\r\n    const fileNameWithoutExtension = fileName.split('.')[0];\r\n\r\n    // Find files with duplicate names\r\n    const existingFileIndex = this.listPictures.findIndex(picture => picture.name === fileNameWithoutExtension);\r\n    if (existingFileIndex !== -1) {\r\n      // If name is duplicate, update file data\r\n      this.listPictures[existingFileIndex] = {\r\n        ...this.listPictures[existingFileIndex],\r\n        data: base64Str,\r\n        CFile: file,\r\n        extension: this._utilityService.getFileExtension(fileName)\r\n      };\r\n    } else {\r\n      // If not duplicate, add new file\r\n      this.listPictures.push({\r\n        id: new Date().getTime() + Math.random(),\r\n        name: fileNameWithoutExtension,\r\n        data: base64Str,\r\n        extension: this._utilityService.getFileExtension(fileName),\r\n        CFile: file\r\n      });\r\n    }\r\n  }\r\n\r\n  isImageFile(fileName: string): boolean {\r\n    const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp'];\r\n    const extension = fileName.toLowerCase().substring(fileName.lastIndexOf('.'));\r\n    return imageExtensions.includes(extension);\r\n  }\r\n\r\n  getImageMimeType(fileName: string): string {\r\n    const extension = fileName.toLowerCase().substring(fileName.lastIndexOf('.'));\r\n    switch (extension) {\r\n      case '.jpg':\r\n      case '.jpeg':\r\n        return 'image/jpeg';\r\n      case '.png':\r\n        return 'image/png';\r\n      case '.gif':\r\n        return 'image/gif';\r\n      case '.bmp':\r\n        return 'image/bmp';\r\n      case '.webp':\r\n        return 'image/webp';\r\n      default:\r\n        return 'image/jpeg';\r\n    }\r\n  }\r\n\r\n  removeImage(pictureId: number) {\r\n    this.listPictures = this.listPictures.filter(x => x.id != pictureId)\r\n  }\r\n  uploadImage(ref: any) {\r\n    if (!this.isEdit) {\r\n      const CFile = this.listPictures.map(x => x.CFile)\r\n      this.validation(CFile)\r\n      if (this.valid.errorMessages.length > 0) {\r\n        this.message.showErrorMSGs(this.valid.errorMessages);\r\n        return\r\n      }      // 根據選擇的類別使用不同的服務\r\n      const uploadRequest = this.selectedCategory === 1 // 建材圖片\r\n        ? this._pictureService.apiPictureUploadListPicturePost$Json({\r\n          body: {\r\n            CBuildCaseId: this.selectedBuildCaseId,\r\n            CPath: \"picture\",\r\n            CFile: CFile\r\n          }\r\n        })\r\n        : this._infoPictureService.apiInfoPictureUploadListInfoPicturePost$Json({\r\n          body: {\r\n            CBuildCaseId: this.selectedBuildCaseId,\r\n            CPath: \"infoPicture\",\r\n            CFile: CFile\r\n          }\r\n        });\r\n\r\n      uploadRequest.pipe(\r\n        tap(res => {\r\n          if (res.StatusCode == 0) {\r\n            this.message.showSucessMSG('執行成功')\r\n            this.listPictures = []\r\n          } else {\r\n            this.message.showErrorMSG(res.Message!)\r\n          }\r\n          ref.close();\r\n        }),\r\n        concatMap((res) => res.StatusCode! == 0 ? this.getPicturelList(1) : of(null)),\r\n      ).subscribe()\r\n    }\r\n    else {\r\n      if (this.listPictures.length > 0 && this.listPictures[0].CFile) {\r\n        // 根據選擇的類別使用不同的更新服務\r\n        const updateRequest = this.selectedCategory === 1 // 建材圖片\r\n          ? this._pictureService.apiPictureUpdatePicturePost$Json({\r\n            body: {\r\n              CBuildCaseID: this.selectedBuildCaseId,\r\n              CPictureID: this.currentEditItem,\r\n              CFile: this.listPictures[0].CFile\r\n            }\r\n          })\r\n          : this._infoPictureService.apiInfoPictureUpdateInfoPicturePost$Json({\r\n            body: {\r\n              CBuildCaseId: this.selectedBuildCaseId,\r\n              CInfoPictureID: this.currentEditItem,\r\n              CFile: this.listPictures[0].CFile\r\n            }\r\n          });\r\n\r\n        updateRequest.pipe(\r\n          tap(res => {\r\n            if (res.StatusCode == 0) {\r\n              this.message.showSucessMSG('執行成功')\r\n              this.listPictures = []\r\n            } else {\r\n              this.message.showErrorMSG(res.Message!)\r\n            }\r\n            ref.close();\r\n          }),\r\n          concatMap((res) => res.StatusCode! == 0 ? this.getPicturelList(1) : of(null)),\r\n        ).subscribe()\r\n      }\r\n    }\r\n  }\r\n\r\n  renameFile(event: any, index: number) {\r\n    var blob = this.listPictures[index].CFile.slice(0, this.listPictures[index].CFile.size, this.listPictures[index].CFile.type);\r\n    var newFile = new File([blob], `${event.target.value + '.' + this.listPictures[index].extension}`, { type: this.listPictures[index].CFile.type });\r\n\r\n    this.listPictures[index].CFile = newFile\r\n  }\r\n\r\n}\r\n", "<nb-card accent=\"success\">\r\n  <nb-card-header>\r\n    <ngx-breadcrumb></ngx-breadcrumb>\r\n  </nb-card-header>\r\n  <nb-card-body>\r\n    <h1 class=\"font-bold text-[#818181]\">可設定上傳建材示意圖片，上傳前請將圖片檔案改為建材圖片檔名。</h1>\r\n    <div class=\"d-flex flex-wrap\">\r\n      <div class=\"col-md-4\">\r\n        <div class=\"form-group d-flex align-items-center w-full\">\r\n          <label for=\"buildingName\" class=\"label mr-2\">建案</label>\r\n          <nb-select placeholder=\"建案\" [(ngModel)]=\"selectedBuildCaseId\" (selectedChange)=\"selectedChange($event)\"\r\n            class=\"w-full\">\r\n            <nb-option *ngFor=\"let buildCase of listUserBuildCases\" [value]=\"buildCase.cID\">\r\n              {{ buildCase.CBuildCaseName }}\r\n            </nb-option>\r\n          </nb-select>\r\n        </div>\r\n      </div>\r\n      <div class=\"col-md-4\">\r\n        <div class=\"form-group d-flex align-items-center w-full\">\r\n          <label for=\"category\" class=\"label mr-2\">類別</label>\r\n          <nb-select placeholder=\"圖片類別\" [(ngModel)]=\"selectedCategory\" (selectedChange)=\"categoryChanged($event)\"\r\n            class=\"w-full\">\r\n            <nb-option *ngFor=\"let option of categoryOptions\" [value]=\"option.value\">\r\n              {{ option.label }}\r\n            </nb-option>\r\n          </nb-select>\r\n        </div>\r\n      </div>\r\n      <div class=\"col-md-4\">\r\n        <div class=\"d-flex justify-content-end w-full\">\r\n          <button class=\"btn btn-info\" (click)=\"addNew(dialog)\" *ngIf=\"isCreate\">\r\n            圖片上傳</button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <div class=\"table-responsive mt-4\">\r\n      <table class=\"table table-striped border \" style=\"min-width: 1000px; background-color:#f3f3f3;\">\r\n        <thead>\r\n          <tr style=\"background-color: #27ae60; color: white;\">\r\n            <th scope=\"col\" class=\"col-1\">項次</th>\r\n            <th scope=\"col\" class=\"col-1\">名稱</th>\r\n            <th scope=\"col\" class=\"col-1\">項目</th>\r\n            <th scope=\"col\" class=\"col-1\">位置</th>\r\n            <th scope=\"col\" class=\"col-1\">建材選項名稱</th>\r\n            <th scope=\"col\" class=\"col-1\">建材圖片檔名（相同建案不可重複）</th>\r\n            <th scope=\"col\" class=\"col-1\">最新圖片上傳時間</th>\r\n            <th scope=\"col\" class=\"col-1\"></th>\r\n          </tr>\r\n        </thead>\r\n        <tbody>\r\n          <tr *ngFor=\"let item of images ; let i = index\">\r\n            <td>{{ item.CId}}</td>\r\n            <td>\r\n              <!-- <a class=\"cursor-pointer text-blue-500\" (click)=\"openPdfInNewTab(item?.CFile)\">{{ item?.CName}}</a> -->\r\n              {{ item?.CName}}\r\n            </td>\r\n            <td>{{ item.CPart}}</td>\r\n            <td>{{ item.CLocation}}</td>\r\n            <td>{{ item.CSelectName}}</td>\r\n            <td>{{ item.CPictureCode}}</td>\r\n            <td>{{ item?.CUpdateDT | date: \"yyyy/MM/dd HH:mm:ss\"}}</td>\r\n            <td class=\"text-center w-32\">\r\n              <button class=\"btn btn-outline-primary btn-sm m-1\" *ngIf=\"isRead\"\r\n                (click)=\"addNew(dialog, item)\">檢視</button>\r\n              <button class=\"btn btn-outline-primary btn-sm m-1\" *ngIf=\"isRead\"\r\n                (click)=\"changePicture(dialog, item)\">改變圖片</button>\r\n            </td>\r\n          </tr>\r\n        </tbody>\r\n      </table>\r\n    </div>\r\n  </nb-card-body>\r\n  <nb-card-footer class=\"d-flex justify-content-center\">\r\n    <ngx-pagination [(CollectionSize)]=\"totalRecords\" [(PageSize)]=\"pageSize\" [(Page)]=\"pageIndex\"\r\n      (PageChange)=\"pageChanged($event)\">\r\n    </ngx-pagination>\r\n  </nb-card-footer>\r\n</nb-card>\r\n\r\n<ng-template #dialog let-data let-ref=\"dialogRef\">\r\n  <nb-card style=\"height: 100%; overflow: auto; width: 700px;\" class=\"mr-md-5 ml-md-5\"> <nb-card-header>\r\n      <span>\r\n        {{currentImageShowing ? '檢視' : (selectedCategory === 1 ? '建材圖片' : '示意圖片') + '上傳'}}\r\n      </span>\r\n    </nb-card-header>\r\n    <nb-card-body style=\"padding:1rem 2rem\">\r\n      <div class=\"w-full h-auto\" *ngIf=\"currentImageShowing else upload\">\r\n        <img class=\"fit-size\" [src]=\"currentImageShowing | addBaseFile\">\r\n      </div> <ng-template #upload>\r\n        <div class=\"mb-3\">\r\n          <h6>上傳方式：</h6>\r\n          <button\r\n            [class]=\"!isEdit ? 'btn btn-info mr-2' : listPictures.length < 1 ? 'btn btn-info mr-2' : 'btn btn-info disable mr-2'\"\r\n            (click)=\"inputFile.click()\">單張圖片上傳</button>\r\n          <button [class]=\"!isEdit ? 'btn btn-success' : 'btn btn-success disable'\" (click)=\"zipInputFile.click()\"\r\n            [disabled]=\"isEdit\">ZIP 批量匯入</button>\r\n        </div>\r\n        <input #inputFile type=\"file\" class=\"hidden\" (change)=\"detectFiles($event)\"\r\n          accept=\"image/png, image/gif, image/jpeg\" multiple>\r\n        <input #zipInputFile type=\"file\" class=\"hidden\" (change)=\"detectFiles($event)\" accept=\".zip,application/zip\">\r\n        <div class=\"mt-3 w-full flex flex-col\">\r\n          <table class=\"table table-striped border\" style=\"background-color:#f3f3f3;\">\r\n            <thead>\r\n              <tr style=\"background-color: #27ae60; color: white;\">\r\n                <th scope=\"col\" class=\"col-4\">文件名</th>\r\n                <th scope=\"col\" class=\"col-1\">檢視</th>\r\n                <th scope=\"col\" class=\"col-1\"></th>\r\n              </tr>\r\n            </thead>\r\n            <tbody>\r\n              <tr *ngFor=\"let picture of listPictures; let i = index\">\r\n                <td>\r\n                  <input nbInput class=\"w-100 p-2 text-[13px]\" type=\"text\" [value]=\"picture.name\"\r\n                    (blur)=\"renameFile($event, i)\">\r\n                </td>\r\n                <td class=\"w-[100px] h-auto\">\r\n                  <img class=\"fit-size\" [src]=\"picture.data\">\r\n                </td>\r\n                <td class=\"text-center w-32\">\r\n                  <button class=\"btn btn-outline-danger btn-sm m-1\" (click)=\"removeImage(picture.id)\">删除</button>\r\n                </td>\r\n              </tr>\r\n            </tbody>\r\n          </table>\r\n        </div>\r\n      </ng-template>\r\n    </nb-card-body>\r\n    <nb-card-footer>\r\n      <div class=\"flex justify-center items-center\">\r\n        <button class=\"btn btn-danger mr-2\" (click)=\"ref.close(); this.currentImageShowing = ''\">取消</button>\r\n        <button *ngIf=\"!currentImageShowing\" class=\"btn btn-success\" (click)=\"uploadImage(ref)\">儲存</button>\r\n      </div>\r\n    </nb-card-footer>\r\n  </nb-card>\r\n</ng-template>\r\n"], "mappings": "AACA,SAASA,YAAY,QAAoB,iBAAiB;AAM1D,SAASC,SAAS,EAAYC,EAAE,EAAEC,GAAG,QAAQ,MAAM;AAInD,SAASC,YAAY,QAAQ,gCAAgC;AAC7D,SAASC,aAAa,QAAQ,qCAAqC;AACnE,OAAO,KAAKC,KAAK,MAAM,OAAO;;;;;;;;;;;;;;;ICDlBC,EAAA,CAAAC,cAAA,oBAAgF;IAC9ED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAF4CH,EAAA,CAAAI,UAAA,UAAAC,YAAA,CAAAC,GAAA,CAAuB;IAC7EN,EAAA,CAAAO,SAAA,EACF;IADEP,EAAA,CAAAQ,kBAAA,MAAAH,YAAA,CAAAI,cAAA,MACF;;;;;IASAT,EAAA,CAAAC,cAAA,oBAAyE;IACvED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAFsCH,EAAA,CAAAI,UAAA,UAAAM,SAAA,CAAAC,KAAA,CAAsB;IACtEX,EAAA,CAAAO,SAAA,EACF;IADEP,EAAA,CAAAQ,kBAAA,MAAAE,SAAA,CAAAE,KAAA,MACF;;;;;;IAMFZ,EAAA,CAAAC,cAAA,iBAAuE;IAA1CD,EAAA,CAAAa,UAAA,mBAAAC,oEAAA;MAAAd,EAAA,CAAAe,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAAA,MAAAC,SAAA,GAAAnB,EAAA,CAAAoB,WAAA;MAAA,OAAApB,EAAA,CAAAqB,WAAA,CAASJ,MAAA,CAAAK,MAAA,CAAAH,SAAA,CAAc;IAAA,EAAC;IACnDnB,EAAA,CAAAE,MAAA,gCAAI;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;IAgCXH,EAAA,CAAAC,cAAA,iBACiC;IAA/BD,EAAA,CAAAa,UAAA,mBAAAU,0EAAA;MAAAvB,EAAA,CAAAe,aAAA,CAAAS,GAAA;MAAA,MAAAC,OAAA,GAAAzB,EAAA,CAAAkB,aAAA,GAAAQ,SAAA;MAAA,MAAAT,MAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAAA,MAAAC,SAAA,GAAAnB,EAAA,CAAAoB,WAAA;MAAA,OAAApB,EAAA,CAAAqB,WAAA,CAASJ,MAAA,CAAAK,MAAA,CAAAH,SAAA,EAAAM,OAAA,CAAoB;IAAA,EAAC;IAACzB,EAAA,CAAAE,MAAA,mBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;IAC5CH,EAAA,CAAAC,cAAA,iBACwC;IAAtCD,EAAA,CAAAa,UAAA,mBAAAc,0EAAA;MAAA3B,EAAA,CAAAe,aAAA,CAAAa,GAAA;MAAA,MAAAH,OAAA,GAAAzB,EAAA,CAAAkB,aAAA,GAAAQ,SAAA;MAAA,MAAAT,MAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAAA,MAAAC,SAAA,GAAAnB,EAAA,CAAAoB,WAAA;MAAA,OAAApB,EAAA,CAAAqB,WAAA,CAASJ,MAAA,CAAAY,aAAA,CAAAV,SAAA,EAAAM,OAAA,CAA2B;IAAA,EAAC;IAACzB,EAAA,CAAAE,MAAA,+BAAI;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;IAdvDH,EADF,CAAAC,cAAA,SAAgD,SAC1C;IAAAD,EAAA,CAAAE,MAAA,GAAa;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACtBH,EAAA,CAAAC,cAAA,SAAI;IAEFD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,GAAe;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACxBH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,GAAmB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC5BH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,IAAqB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC9BH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAE,MAAA,IAAsB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC/BH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAE,MAAA,IAAkD;;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC3DH,EAAA,CAAAC,cAAA,cAA6B;IAG3BD,EAFA,CAAA8B,UAAA,KAAAC,iDAAA,qBACiC,KAAAC,iDAAA,qBAEO;IAE5ChC,EADE,CAAAG,YAAA,EAAK,EACF;;;;;IAhBCH,EAAA,CAAAO,SAAA,GAAa;IAAbP,EAAA,CAAAiC,iBAAA,CAAAR,OAAA,CAAAS,GAAA,CAAa;IAGflC,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAAQ,kBAAA,MAAAiB,OAAA,kBAAAA,OAAA,CAAAU,KAAA,MACF;IACInC,EAAA,CAAAO,SAAA,GAAe;IAAfP,EAAA,CAAAiC,iBAAA,CAAAR,OAAA,CAAAW,KAAA,CAAe;IACfpC,EAAA,CAAAO,SAAA,GAAmB;IAAnBP,EAAA,CAAAiC,iBAAA,CAAAR,OAAA,CAAAY,SAAA,CAAmB;IACnBrC,EAAA,CAAAO,SAAA,GAAqB;IAArBP,EAAA,CAAAiC,iBAAA,CAAAR,OAAA,CAAAa,WAAA,CAAqB;IACrBtC,EAAA,CAAAO,SAAA,GAAsB;IAAtBP,EAAA,CAAAiC,iBAAA,CAAAR,OAAA,CAAAc,YAAA,CAAsB;IACtBvC,EAAA,CAAAO,SAAA,GAAkD;IAAlDP,EAAA,CAAAiC,iBAAA,CAAAjC,EAAA,CAAAwC,WAAA,QAAAf,OAAA,kBAAAA,OAAA,CAAAgB,SAAA,yBAAkD;IAEAzC,EAAA,CAAAO,SAAA,GAAY;IAAZP,EAAA,CAAAI,UAAA,SAAAa,MAAA,CAAAyB,MAAA,CAAY;IAEZ1C,EAAA,CAAAO,SAAA,EAAY;IAAZP,EAAA,CAAAI,UAAA,SAAAa,MAAA,CAAAyB,MAAA,CAAY;;;;;IAsBxE1C,EAAA,CAAAC,cAAA,cAAmE;IACjED,EAAA,CAAA2C,SAAA,cAAgE;;IAClE3C,EAAA,CAAAG,YAAA,EAAM;;;;IADkBH,EAAA,CAAAO,SAAA,EAAyC;IAAzCP,EAAA,CAAAI,UAAA,QAAAJ,EAAA,CAAA4C,WAAA,OAAA3B,MAAA,CAAA4B,mBAAA,GAAA7C,EAAA,CAAA8C,aAAA,CAAyC;;;;;;IAyBrD9C,EAFJ,CAAAC,cAAA,SAAwD,SAClD,gBAE+B;IAA/BD,EAAA,CAAAa,UAAA,kBAAAkC,2FAAAC,MAAA;MAAA,MAAAC,KAAA,GAAAjD,EAAA,CAAAe,aAAA,CAAAmC,IAAA,EAAAC,KAAA;MAAA,MAAAlC,MAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAAA,OAAAlB,EAAA,CAAAqB,WAAA,CAAQJ,MAAA,CAAAmC,UAAA,CAAAJ,MAAA,EAAAC,KAAA,CAAqB;IAAA,EAAC;IAClCjD,EAFE,CAAAG,YAAA,EACiC,EAC9B;IACLH,EAAA,CAAAC,cAAA,aAA6B;IAC3BD,EAAA,CAAA2C,SAAA,cAA2C;IAC7C3C,EAAA,CAAAG,YAAA,EAAK;IAEHH,EADF,CAAAC,cAAA,aAA6B,iBACyD;IAAlCD,EAAA,CAAAa,UAAA,mBAAAwC,6FAAA;MAAA,MAAAC,WAAA,GAAAtD,EAAA,CAAAe,aAAA,CAAAmC,IAAA,EAAAxB,SAAA;MAAA,MAAAT,MAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAAA,OAAAlB,EAAA,CAAAqB,WAAA,CAASJ,MAAA,CAAAsC,WAAA,CAAAD,WAAA,CAAAE,EAAA,CAAuB;IAAA,EAAC;IAACxD,EAAA,CAAAE,MAAA,mBAAE;IAE1FF,EAF0F,CAAAG,YAAA,EAAS,EAC5F,EACF;;;;IATwDH,EAAA,CAAAO,SAAA,GAAsB;IAAtBP,EAAA,CAAAI,UAAA,UAAAkD,WAAA,CAAAG,IAAA,CAAsB;IAIzDzD,EAAA,CAAAO,SAAA,GAAoB;IAApBP,EAAA,CAAAI,UAAA,QAAAkD,WAAA,CAAAI,IAAA,EAAA1D,EAAA,CAAA8C,aAAA,CAAoB;;;;;;IA1BlD9C,EADF,CAAAC,cAAA,cAAkB,SACZ;IAAAD,EAAA,CAAAE,MAAA,qCAAK;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACdH,EAAA,CAAAC,cAAA,iBAE8B;IAA5BD,EAAA,CAAAa,UAAA,mBAAA8C,uFAAA;MAAA3D,EAAA,CAAAe,aAAA,CAAA6C,IAAA;MAAA,MAAAC,aAAA,GAAA7D,EAAA,CAAAoB,WAAA;MAAA,OAAApB,EAAA,CAAAqB,WAAA,CAASwC,aAAA,CAAAC,KAAA,EAAiB;IAAA,EAAC;IAAC9D,EAAA,CAAAE,MAAA,2CAAM;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAC7CH,EAAA,CAAAC,cAAA,iBACsB;IADoDD,EAAA,CAAAa,UAAA,mBAAAkD,uFAAA;MAAA/D,EAAA,CAAAe,aAAA,CAAA6C,IAAA;MAAA,MAAAI,gBAAA,GAAAhE,EAAA,CAAAoB,WAAA;MAAA,OAAApB,EAAA,CAAAqB,WAAA,CAAS2C,gBAAA,CAAAF,KAAA,EAAoB;IAAA,EAAC;IAClF9D,EAAA,CAAAE,MAAA,mCAAQ;IAChCF,EADgC,CAAAG,YAAA,EAAS,EACnC;IACNH,EAAA,CAAAC,cAAA,mBACqD;IADRD,EAAA,CAAAa,UAAA,oBAAAoD,uFAAAjB,MAAA;MAAAhD,EAAA,CAAAe,aAAA,CAAA6C,IAAA;MAAA,MAAA3C,MAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAAA,OAAAlB,EAAA,CAAAqB,WAAA,CAAUJ,MAAA,CAAAiD,WAAA,CAAAlB,MAAA,CAAmB;IAAA,EAAC;IAA3EhD,EAAA,CAAAG,YAAA,EACqD;IACrDH,EAAA,CAAAC,cAAA,mBAA6G;IAA7DD,EAAA,CAAAa,UAAA,oBAAAsD,uFAAAnB,MAAA;MAAAhD,EAAA,CAAAe,aAAA,CAAA6C,IAAA;MAAA,MAAA3C,MAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAAA,OAAAlB,EAAA,CAAAqB,WAAA,CAAUJ,MAAA,CAAAiD,WAAA,CAAAlB,MAAA,CAAmB;IAAA,EAAC;IAA9EhD,EAAA,CAAAG,YAAA,EAA6G;IAKrGH,EAJR,CAAAC,cAAA,eAAuC,iBACuC,aACnE,cACgD,cACrB;IAAAD,EAAA,CAAAE,MAAA,0BAAG;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACtCH,EAAA,CAAAC,cAAA,cAA8B;IAAAD,EAAA,CAAAE,MAAA,oBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACrCH,EAAA,CAAA2C,SAAA,cAAmC;IAEvC3C,EADE,CAAAG,YAAA,EAAK,EACC;IACRH,EAAA,CAAAC,cAAA,aAAO;IACLD,EAAA,CAAA8B,UAAA,KAAAsC,oEAAA,iBAAwD;IAc9DpE,EAFI,CAAAG,YAAA,EAAQ,EACF,EACJ;;;;IAhCFH,EAAA,CAAAO,SAAA,GAAqH;IAArHP,EAAA,CAAAqE,UAAA,EAAApD,MAAA,CAAAqD,MAAA,yBAAArD,MAAA,CAAAsD,YAAA,CAAAC,MAAA,yDAAqH;IAE/GxE,EAAA,CAAAO,SAAA,GAAiE;IAAjEP,EAAA,CAAAqE,UAAA,EAAApD,MAAA,CAAAqD,MAAA,iDAAiE;IACvEtE,EAAA,CAAAI,UAAA,aAAAa,MAAA,CAAAqD,MAAA,CAAmB;IAeOtE,EAAA,CAAAO,SAAA,IAAiB;IAAjBP,EAAA,CAAAI,UAAA,YAAAa,MAAA,CAAAsD,YAAA,CAAiB;;;;;;IAoB/CvE,EAAA,CAAAC,cAAA,iBAAwF;IAA3BD,EAAA,CAAAa,UAAA,mBAAA4D,mFAAA;MAAAzE,EAAA,CAAAe,aAAA,CAAA2D,IAAA;MAAA,MAAAC,OAAA,GAAA3E,EAAA,CAAAkB,aAAA,GAAA0D,SAAA;MAAA,MAAA3D,MAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAAA,OAAAlB,EAAA,CAAAqB,WAAA,CAASJ,MAAA,CAAA4D,WAAA,CAAAF,OAAA,CAAgB;IAAA,EAAC;IAAC3E,EAAA,CAAAE,MAAA,mBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;IAjDrGH,EADJ,CAAAC,cAAA,kBAAqF,qBAAiB,WAC5F;IACJD,EAAA,CAAAE,MAAA,GACF;IACFF,EADE,CAAAG,YAAA,EAAO,EACQ;IACjBH,EAAA,CAAAC,cAAA,uBAAwC;IAG/BD,EAFP,CAAA8B,UAAA,IAAAgD,sDAAA,kBAAmE,IAAAC,8DAAA,iCAAA/E,EAAA,CAAAgF,sBAAA,CAEvC;IAsC9BhF,EAAA,CAAAG,YAAA,EAAe;IAGXH,EAFJ,CAAAC,cAAA,qBAAgB,cACgC,kBAC6C;IAArDD,EAAA,CAAAa,UAAA,mBAAAoE,0EAAA;MAAA,MAAAN,OAAA,GAAA3E,EAAA,CAAAe,aAAA,CAAAmE,IAAA,EAAAN,SAAA;MAAA,MAAA3D,MAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAASyD,OAAA,CAAAQ,KAAA,EAAW;MAAA,OAAAnF,EAAA,CAAAqB,WAAA,CAAAJ,MAAA,CAAA4B,mBAAA,GAA6B,EAAE;IAAA,EAAC;IAAC7C,EAAA,CAAAE,MAAA,oBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACpGH,EAAA,CAAA8B,UAAA,KAAAsD,0DAAA,qBAAwF;IAG9FpF,EAFI,CAAAG,YAAA,EAAM,EACS,EACT;;;;;IAnDJH,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAAQ,kBAAA,MAAAS,MAAA,CAAA4B,mBAAA,qBAAA5B,MAAA,CAAAoE,gBAAA,wFACF;IAG4BrF,EAAA,CAAAO,SAAA,GAA0B;IAAAP,EAA1B,CAAAI,UAAA,SAAAa,MAAA,CAAA4B,mBAAA,CAA0B,aAAAyC,UAAA,CAAW;IA4CtDtF,EAAA,CAAAO,SAAA,GAA0B;IAA1BP,EAAA,CAAAI,UAAA,UAAAa,MAAA,CAAA4B,mBAAA,CAA0B;;;ADrH3C;AACA,IAAK0C,eAIJ;AAJD,WAAKA,eAAe;EAClBA,eAAA,CAAAA,eAAA,sBAAQ;EACRA,eAAA,CAAAA,eAAA,gDAAqB;EACrBA,eAAA,CAAAA,eAAA,gCAAa,EAAO;AACtB,CAAC,EAJIA,eAAe,KAAfA,eAAe;AAkBpB,OAAM,MAAOC,wBAAyB,SAAQ1F,aAAa;EAuBzD2F,YACUC,MAAmB,EACnBC,aAA8B,EAC9BC,KAAuB,EACvBC,eAA+B,EAC/BC,mBAAuC,EACvCC,iBAAmC,EACnCC,OAAuB,EACvBC,eAA+B;IAEvC,KAAK,CAACP,MAAM,CAAC;IATL,KAAAA,MAAM,GAANA,MAAM;IACN,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,mBAAmB,GAAnBA,mBAAmB;IACnB,KAAAC,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAC,eAAe,GAAfA,eAAe;IA7BzB,KAAAC,MAAM,GAA4D,EAAE;IACpE,KAAAC,kBAAkB,GAA8B,EAAE;IACpB,KAAAd,gBAAgB,GAAoBE,eAAe,CAACa,IAAI;IACtF,KAAAC,kBAAkB,GAAY,KAAK,CAAC,CAAC;IAErC,KAAAxD,mBAAmB,GAAW,EAAE;IAEhC,KAAA0B,YAAY,GAAU,EAAE;IAExB,KAAAD,MAAM,GAAY,KAAK;IAGvB;IACA,KAAAgC,eAAe,GAAG,CAChB;MAAE3F,KAAK,EAAE4E,eAAe,CAACgB,iBAAiB;MAAE3F,KAAK,EAAE;IAAM,CAAE,EAC3D;MAAED,KAAK,EAAE4E,eAAe,CAACiB,SAAS;MAAE5F,KAAK,EAAE;IAAM,CAAE,CACpD;IAED;IACA,KAAA2E,eAAe,GAAGA,eAAe;EAajC;EAESkB,QAAQA,CAAA;IACf,IAAI,CAACC,gBAAgB,EAAE;EACzB;EACAA,gBAAgBA,CAAA;IACd,IAAI,CAACX,iBAAiB,CAACY,qCAAqC,CAAC,EAAE,CAAC,CAC7DC,IAAI,CACHhH,GAAG,CAACiH,GAAG,IAAG;MACR,IAAIA,GAAG,CAACC,UAAU,KAAK,CAAC,EAAE;QACxB,IAAI,CAACX,kBAAkB,GAAGU,GAAG,CAACE,OAAQ,IAAI,EAAE;QAC5C,IAAI,CAACC,mBAAmB,GAAG,IAAI,CAACb,kBAAkB,CAAC,CAAC,CAAC,CAAC7F,GAAI;MAC5D;IACF,CAAC;IACD;KACD,CAAC2G,SAAS,EAAE;EACjB;EAEAC,eAAeA,CAACC,QAAc;IAC5B,IAAIA,QAAQ,EAAE;MACZ,IAAI,CAAClB,eAAe,CAACmB,gBAAgB,CAACD,QAAQ,CAAC;IACjD;EACF;EAAEE,eAAeA,CAACC,SAAiB;IACjC,IAAI,IAAI,CAACjC,gBAAgB,KAAK,CAAC,EAAE;MAAE;MACjC,OAAO,IAAI,CAACQ,eAAe,CAAC0B,kCAAkC,CAAC;QAC7DC,IAAI,EAAE;UACJC,SAAS,EAAEH,SAAS;UACpBI,QAAQ,EAAE,IAAI,CAACC,QAAQ;UACvBC,YAAY,EAAE,IAAI,CAACZ;;OAEtB,CAAC,CAACJ,IAAI,CACLhH,GAAG,CAACiH,GAAG,IAAG;QACR,IAAIA,GAAG,CAACC,UAAU,IAAI,CAAC,EAAE;UACvB,IAAI,CAACZ,MAAM,GAAGW,GAAG,CAACE,OAAQ,IAAI,EAAE;UAChC,IAAI,CAACc,YAAY,GAAGhB,GAAG,CAACiB,UAAW;QACrC;MACF,CAAC,CAAC,CACH;IACH,CAAC,MAAM,IAAI,IAAI,CAACzC,gBAAgB,KAAK,CAAC,EAAE;MAAE;MACxC,OAAO,IAAI,CAACS,mBAAmB,CAACiC,0CAA0C,CAAC;QACzEP,IAAI,EAAE;UACJC,SAAS,EAAEH,SAAS;UACpBI,QAAQ,EAAE,IAAI,CAACC,QAAQ;UACvBC,YAAY,EAAE,IAAI,CAACZ;;OAEtB,CAAC,CAACJ,IAAI,CACLhH,GAAG,CAACiH,GAAG,IAAG;QACR,IAAIA,GAAG,CAACC,UAAU,IAAI,CAAC,EAAE;UACvB,IAAI,CAACZ,MAAM,GAAGW,GAAG,CAACE,OAAQ,IAAI,EAAE;UAChC,IAAI,CAACc,YAAY,GAAGhB,GAAG,CAACiB,UAAW;QACrC;MACF,CAAC,CAAC,CACH;IACH,CAAC,MAAM;MACL;MACA,IAAI,CAAC5B,MAAM,GAAG,EAAE;MAChB,IAAI,CAAC2B,YAAY,GAAG,CAAC;MACrB,OAAO,IAAI,CAAChC,eAAe,CAAC0B,kCAAkC,CAAC;QAC7DC,IAAI,EAAE;UACJC,SAAS,EAAE,CAAC;UACZC,QAAQ,EAAE,CAAC;UACXE,YAAY,EAAE,CAAC,CAAC,CAAC;;OAEpB,CAAC,CAAChB,IAAI,CACLhH,GAAG,CAAC,MAAK;QACP,IAAI,CAACsG,MAAM,GAAG,EAAE;QAChB,IAAI,CAAC2B,YAAY,GAAG,CAAC;MACvB,CAAC,CAAC,CACH;IACH;EACF;EAAEG,WAAWA,CAACV,SAAiB;IAC7B;IACA,IAAI,CAACD,eAAe,CAACC,SAAS,CAAC,CAACL,SAAS,EAAE;EAC7C;EACAgB,cAAcA,CAACC,WAAmB;IAChC,IAAI,CAAClB,mBAAmB,GAAGkB,WAAW;IACtC,IAAI,CAACb,eAAe,CAAC,CAAC,CAAC,CAACJ,SAAS,EAAE;EACrC;EAAEkB,eAAeA,CAACC,QAAgB;IAChC,IAAI,CAAC/C,gBAAgB,GAAG+C,QAAQ;IAChC,IAAI,CAAC/B,kBAAkB,GAAG,IAAI,CAAC,CAAC;IAChC,IAAI,CAACgB,eAAe,CAAC,CAAC,CAAC,CAACJ,SAAS,EAAE;EACrC;EAAE3F,MAAMA,CAAC+G,GAAQ,EAAEC,IAA0D;IAC3E;IACA,IAAI,CAACA,IAAI,IAAI,CAAC,IAAI,CAACjC,kBAAkB,EAAE;MACrC,IAAI,CAACL,OAAO,CAACuC,YAAY,CAAC,UAAU,CAAC;MACrC;IACF;IAEA,IAAI,CAAChE,YAAY,GAAG,EAAE;IACtB,IAAI,CAACoB,aAAa,CAAC6C,IAAI,CAACH,GAAG,CAAC;IAC5B,IAAI,CAAC/D,MAAM,GAAG,KAAK;IACnB,IAAI,CAAC,CAACgE,IAAI,EAAE;MACV,IAAI,CAACzF,mBAAmB,GAAGyF,IAAI,CAACG,KAAM;IACxC,CAAC,MACI;MACH,IAAI,CAAClE,YAAY,GAAG,EAAE;IACxB;EACF;EAAE1C,aAAaA,CAACwG,GAAQ,EAAEC,IAA0D;IAClF;IACA,IAAI,CAAC,IAAI,CAACjC,kBAAkB,EAAE;MAC5B,IAAI,CAACL,OAAO,CAACuC,YAAY,CAAC,UAAU,CAAC;MACrC;IACF;IAEA,IAAI,CAAC,CAACD,IAAI,IAAIA,IAAI,CAACpG,GAAG,EAAE;MACtB,IAAI,CAACyD,aAAa,CAAC6C,IAAI,CAACH,GAAG,CAAC;MAC5B,IAAI,CAAC/D,MAAM,GAAG,IAAI;MAClB,IAAI,CAACoE,eAAe,GAAGJ,IAAI,CAACpG,GAAG;MAC/B,IAAI,CAACqC,YAAY,GAAG,EAAE;IACxB;EACF;EAEAoE,UAAUA,CAACF,KAAU;IACnB,IAAI,CAAC7C,KAAK,CAACgD,KAAK,EAAE;IAClB,MAAMC,OAAO,GAAG,IAAIC,GAAG,EAAE;IACzB,KAAK,MAAMR,IAAI,IAAIG,KAAK,EAAE;MACxB,IAAII,OAAO,CAACE,GAAG,CAACT,IAAI,CAAC7E,IAAI,CAAC,EAAE;QAC1B,IAAI,CAACmC,KAAK,CAACoD,eAAe,CAAC,QAAQ,CAAC;QACpC;MACF;MACAH,OAAO,CAACI,GAAG,CAACX,IAAI,CAAC7E,IAAI,CAAC;IACxB;EACF;EAEAyF,QAAQA,CAACb,GAAQ,GACjB;EACAnE,WAAWA,CAACiF,KAAU;IACpB,KAAK,IAAIhG,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGgG,KAAK,CAACC,MAAM,CAACC,KAAK,CAAC7E,MAAM,EAAErB,KAAK,EAAE,EAAE;MAC9D,MAAMmG,IAAI,GAAGH,KAAK,CAACC,MAAM,CAACC,KAAK,CAAClG,KAAK,CAAC;MACtC,IAAImG,IAAI,EAAE;QACR;QACA,IAAIA,IAAI,CAACC,IAAI,KAAK,iBAAiB,IAAID,IAAI,CAAC7F,IAAI,CAAC+F,WAAW,EAAE,CAACC,QAAQ,CAAC,MAAM,CAAC,EAAE;UAC/E,IAAI,CAACC,cAAc,CAACJ,IAAI,CAAC;QAC3B,CAAC,MAAM;UACL,IAAI,CAACK,sBAAsB,CAACL,IAAI,CAAC;QACnC;MACF;IACF;IACA;IACAH,KAAK,CAACC,MAAM,CAACzI,KAAK,GAAG,IAAI;EAC3B;EAEA+I,cAAcA,CAACE,OAAa;IAC1B,MAAMC,GAAG,GAAG,IAAI9J,KAAK,EAAE;IAEvB8J,GAAG,CAACC,SAAS,CAACF,OAAO,CAAC,CAACG,IAAI,CAAEC,QAAQ,IAAI;MACvC,MAAMC,UAAU,GAAmB,EAAE;MAErCD,QAAQ,CAACE,OAAO,CAAC,CAACC,YAAY,EAAEb,IAAI,KAAI;QACtC;QACA,IAAI,CAACA,IAAI,CAACc,GAAG,IAAI,IAAI,CAACC,WAAW,CAACF,YAAY,CAAC,EAAE;UAC/CF,UAAU,CAACK,IAAI,CACbhB,IAAI,CAACiB,KAAK,CAAC,MAAM,CAAC,CAACR,IAAI,CAAES,IAAI,IAAI;YAC/B;YACA,MAAMC,SAAS,GAAG,IAAIC,IAAI,CAAC,CAACF,IAAI,CAAC,EAAEL,YAAY,EAAE;cAAEZ,IAAI,EAAE,IAAI,CAACoB,gBAAgB,CAACR,YAAY;YAAC,CAAE,CAAC;YAC/F,OAAO,IAAI,CAACS,uBAAuB,CAACH,SAAS,EAAEN,YAAY,CAAC;UAC9D,CAAC,CAAC,CACH;QACH;MACF,CAAC,CAAC;MAEF;MACAU,OAAO,CAACC,GAAG,CAACb,UAAU,CAAC,CAACF,IAAI,CAAC,MAAK;QAChC,IAAI,CAAC/D,OAAO,CAAC+E,aAAa,CAAC,iBAAiBd,UAAU,CAACzF,MAAM,MAAM,CAAC;MACtE,CAAC,CAAC,CAACwG,KAAK,CAAEC,KAAK,IAAI;QACjBC,OAAO,CAACD,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;QAC3C,IAAI,CAACjF,OAAO,CAACuC,YAAY,CAAC,oBAAoB,CAAC;MACjD,CAAC,CAAC;IAEJ,CAAC,CAAC,CAACyC,KAAK,CAAEC,KAAK,IAAI;MACjBC,OAAO,CAACD,KAAK,CAAC,iBAAiB,EAAEA,KAAK,CAAC;MACvC,IAAI,CAACjF,OAAO,CAACuC,YAAY,CAAC,uBAAuB,CAAC;IACpD,CAAC,CAAC;EACJ;EAEAoB,sBAAsBA,CAACL,IAAU;IAC/B,IAAI6B,MAAM,GAAG,IAAIC,UAAU,EAAE;IAC7BD,MAAM,CAACE,aAAa,CAAC/B,IAAI,CAAC;IAC1B6B,MAAM,CAACG,MAAM,GAAG,MAAK;MACnB,IAAIC,SAAS,GAAWJ,MAAM,CAACK,MAAgB;MAC/C,IAAI,CAACD,SAAS,EAAE;QACd;MACF;MACA,IAAI,CAACE,cAAc,CAACnC,IAAI,EAAEiC,SAAS,CAAC;IACtC,CAAC;EACH;EAEAX,uBAAuBA,CAACtB,IAAU,EAAEoC,YAAoB;IACtD,OAAO,IAAIb,OAAO,CAAC,CAACc,OAAO,EAAEC,MAAM,KAAI;MACrC,IAAIT,MAAM,GAAG,IAAIC,UAAU,EAAE;MAC7BD,MAAM,CAACE,aAAa,CAAC/B,IAAI,CAAC;MAC1B6B,MAAM,CAACG,MAAM,GAAG,MAAK;QACnB,IAAIC,SAAS,GAAWJ,MAAM,CAACK,MAAgB;QAC/C,IAAI,CAACD,SAAS,EAAE;UACdK,MAAM,CAAC,UAAU,CAAC;UAClB;QACF;QACA,IAAI,CAACH,cAAc,CAACnC,IAAI,EAAEiC,SAAS,EAAEG,YAAY,CAAC;QAClDC,OAAO,EAAE;MACX,CAAC;MACDR,MAAM,CAACU,OAAO,GAAG,MAAK;QACpBD,MAAM,CAAC,aAAa,CAAC;MACvB,CAAC;IACH,CAAC,CAAC;EACJ;EAEAH,cAAcA,CAACnC,IAAU,EAAEiC,SAAiB,EAAEG,YAAqB;IACjE;IACA,MAAMI,QAAQ,GAAGJ,YAAY,IAAIpC,IAAI,CAAC7F,IAAI;IAC1C,MAAMsI,wBAAwB,GAAGD,QAAQ,CAACE,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IAEvD;IACA,MAAMC,iBAAiB,GAAG,IAAI,CAAC1H,YAAY,CAAC2H,SAAS,CAACC,OAAO,IAAIA,OAAO,CAAC1I,IAAI,KAAKsI,wBAAwB,CAAC;IAC3G,IAAIE,iBAAiB,KAAK,CAAC,CAAC,EAAE;MAC5B;MACA,IAAI,CAAC1H,YAAY,CAAC0H,iBAAiB,CAAC,GAAG;QACrC,GAAG,IAAI,CAAC1H,YAAY,CAAC0H,iBAAiB,CAAC;QACvCvI,IAAI,EAAE6H,SAAS;QACf9C,KAAK,EAAEa,IAAI;QACX8C,SAAS,EAAE,IAAI,CAACnG,eAAe,CAACoG,gBAAgB,CAACP,QAAQ;OAC1D;IACH,CAAC,MAAM;MACL;MACA,IAAI,CAACvH,YAAY,CAAC+F,IAAI,CAAC;QACrB9G,EAAE,EAAE,IAAI8I,IAAI,EAAE,CAACC,OAAO,EAAE,GAAGC,IAAI,CAACC,MAAM,EAAE;QACxChJ,IAAI,EAAEsI,wBAAwB;QAC9BrI,IAAI,EAAE6H,SAAS;QACfa,SAAS,EAAE,IAAI,CAACnG,eAAe,CAACoG,gBAAgB,CAACP,QAAQ,CAAC;QAC1DrD,KAAK,EAAEa;OACR,CAAC;IACJ;EACF;EAEAe,WAAWA,CAACyB,QAAgB;IAC1B,MAAMY,eAAe,GAAG,CAAC,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO,CAAC;IAC1E,MAAMN,SAAS,GAAGN,QAAQ,CAACtC,WAAW,EAAE,CAACmD,SAAS,CAACb,QAAQ,CAACc,WAAW,CAAC,GAAG,CAAC,CAAC;IAC7E,OAAOF,eAAe,CAACG,QAAQ,CAACT,SAAS,CAAC;EAC5C;EAEAzB,gBAAgBA,CAACmB,QAAgB;IAC/B,MAAMM,SAAS,GAAGN,QAAQ,CAACtC,WAAW,EAAE,CAACmD,SAAS,CAACb,QAAQ,CAACc,WAAW,CAAC,GAAG,CAAC,CAAC;IAC7E,QAAQR,SAAS;MACf,KAAK,MAAM;MACX,KAAK,OAAO;QACV,OAAO,YAAY;MACrB,KAAK,MAAM;QACT,OAAO,WAAW;MACpB,KAAK,MAAM;QACT,OAAO,WAAW;MACpB,KAAK,MAAM;QACT,OAAO,WAAW;MACpB,KAAK,OAAO;QACV,OAAO,YAAY;MACrB;QACE,OAAO,YAAY;IACvB;EACF;EAEA7I,WAAWA,CAACuJ,SAAiB;IAC3B,IAAI,CAACvI,YAAY,GAAG,IAAI,CAACA,YAAY,CAACwI,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACxJ,EAAE,IAAIsJ,SAAS,CAAC;EACtE;EACAjI,WAAWA,CAACwD,GAAQ;IAClB,IAAI,CAAC,IAAI,CAAC/D,MAAM,EAAE;MAChB,MAAMmE,KAAK,GAAG,IAAI,CAAClE,YAAY,CAAC0I,GAAG,CAACD,CAAC,IAAIA,CAAC,CAACvE,KAAK,CAAC;MACjD,IAAI,CAACE,UAAU,CAACF,KAAK,CAAC;MACtB,IAAI,IAAI,CAAC7C,KAAK,CAACsH,aAAa,CAAC1I,MAAM,GAAG,CAAC,EAAE;QACvC,IAAI,CAACwB,OAAO,CAACmH,aAAa,CAAC,IAAI,CAACvH,KAAK,CAACsH,aAAa,CAAC;QACpD;MACF,CAAC,CAAM;MACP,MAAME,aAAa,GAAG,IAAI,CAAC/H,gBAAgB,KAAK,CAAC,CAAC;MAAA,EAC9C,IAAI,CAACQ,eAAe,CAACwH,oCAAoC,CAAC;QAC1D7F,IAAI,EAAE;UACJI,YAAY,EAAE,IAAI,CAACZ,mBAAmB;UACtCsG,KAAK,EAAE,SAAS;UAChB7E,KAAK,EAAEA;;OAEV,CAAC,GACA,IAAI,CAAC3C,mBAAmB,CAACyH,4CAA4C,CAAC;QACtE/F,IAAI,EAAE;UACJI,YAAY,EAAE,IAAI,CAACZ,mBAAmB;UACtCsG,KAAK,EAAE,aAAa;UACpB7E,KAAK,EAAEA;;OAEV,CAAC;MAEJ2E,aAAa,CAACxG,IAAI,CAChBhH,GAAG,CAACiH,GAAG,IAAG;QACR,IAAIA,GAAG,CAACC,UAAU,IAAI,CAAC,EAAE;UACvB,IAAI,CAACd,OAAO,CAAC+E,aAAa,CAAC,MAAM,CAAC;UAClC,IAAI,CAACxG,YAAY,GAAG,EAAE;QACxB,CAAC,MAAM;UACL,IAAI,CAACyB,OAAO,CAACuC,YAAY,CAAC1B,GAAG,CAAC2G,OAAQ,CAAC;QACzC;QACAnF,GAAG,CAAClD,KAAK,EAAE;MACb,CAAC,CAAC,EACFzF,SAAS,CAAEmH,GAAG,IAAKA,GAAG,CAACC,UAAW,IAAI,CAAC,GAAG,IAAI,CAACO,eAAe,CAAC,CAAC,CAAC,GAAG1H,EAAE,CAAC,IAAI,CAAC,CAAC,CAC9E,CAACsH,SAAS,EAAE;IACf,CAAC,MACI;MACH,IAAI,IAAI,CAAC1C,YAAY,CAACC,MAAM,GAAG,CAAC,IAAI,IAAI,CAACD,YAAY,CAAC,CAAC,CAAC,CAACkE,KAAK,EAAE;QAC9D;QACA,MAAMgF,aAAa,GAAG,IAAI,CAACpI,gBAAgB,KAAK,CAAC,CAAC;QAAA,EAC9C,IAAI,CAACQ,eAAe,CAAC6H,gCAAgC,CAAC;UACtDlG,IAAI,EAAE;YACJmG,YAAY,EAAE,IAAI,CAAC3G,mBAAmB;YACtC4G,UAAU,EAAE,IAAI,CAAClF,eAAe;YAChCD,KAAK,EAAE,IAAI,CAAClE,YAAY,CAAC,CAAC,CAAC,CAACkE;;SAE/B,CAAC,GACA,IAAI,CAAC3C,mBAAmB,CAAC+H,wCAAwC,CAAC;UAClErG,IAAI,EAAE;YACJI,YAAY,EAAE,IAAI,CAACZ,mBAAmB;YACtC8G,cAAc,EAAE,IAAI,CAACpF,eAAe;YACpCD,KAAK,EAAE,IAAI,CAAClE,YAAY,CAAC,CAAC,CAAC,CAACkE;;SAE/B,CAAC;QAEJgF,aAAa,CAAC7G,IAAI,CAChBhH,GAAG,CAACiH,GAAG,IAAG;UACR,IAAIA,GAAG,CAACC,UAAU,IAAI,CAAC,EAAE;YACvB,IAAI,CAACd,OAAO,CAAC+E,aAAa,CAAC,MAAM,CAAC;YAClC,IAAI,CAACxG,YAAY,GAAG,EAAE;UACxB,CAAC,MAAM;YACL,IAAI,CAACyB,OAAO,CAACuC,YAAY,CAAC1B,GAAG,CAAC2G,OAAQ,CAAC;UACzC;UACAnF,GAAG,CAAClD,KAAK,EAAE;QACb,CAAC,CAAC,EACFzF,SAAS,CAAEmH,GAAG,IAAKA,GAAG,CAACC,UAAW,IAAI,CAAC,GAAG,IAAI,CAACO,eAAe,CAAC,CAAC,CAAC,GAAG1H,EAAE,CAAC,IAAI,CAAC,CAAC,CAC9E,CAACsH,SAAS,EAAE;MACf;IACF;EACF;EAEA7D,UAAUA,CAAC+F,KAAU,EAAEhG,KAAa;IAClC,IAAIqH,IAAI,GAAG,IAAI,CAACjG,YAAY,CAACpB,KAAK,CAAC,CAACsF,KAAK,CAACsF,KAAK,CAAC,CAAC,EAAE,IAAI,CAACxJ,YAAY,CAACpB,KAAK,CAAC,CAACsF,KAAK,CAACuF,IAAI,EAAE,IAAI,CAACzJ,YAAY,CAACpB,KAAK,CAAC,CAACsF,KAAK,CAACc,IAAI,CAAC;IAC5H,IAAI0E,OAAO,GAAG,IAAIvD,IAAI,CAAC,CAACF,IAAI,CAAC,EAAE,GAAGrB,KAAK,CAACC,MAAM,CAACzI,KAAK,GAAG,GAAG,GAAG,IAAI,CAAC4D,YAAY,CAACpB,KAAK,CAAC,CAACiJ,SAAS,EAAE,EAAE;MAAE7C,IAAI,EAAE,IAAI,CAAChF,YAAY,CAACpB,KAAK,CAAC,CAACsF,KAAK,CAACc;IAAI,CAAE,CAAC;IAEjJ,IAAI,CAAChF,YAAY,CAACpB,KAAK,CAAC,CAACsF,KAAK,GAAGwF,OAAO;EAC1C;;;uCApXWzI,wBAAwB,EAAAxF,EAAA,CAAAkO,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAApO,EAAA,CAAAkO,iBAAA,CAAAG,EAAA,CAAAC,eAAA,GAAAtO,EAAA,CAAAkO,iBAAA,CAAAK,EAAA,CAAAC,gBAAA,GAAAxO,EAAA,CAAAkO,iBAAA,CAAAO,EAAA,CAAAC,cAAA,GAAA1O,EAAA,CAAAkO,iBAAA,CAAAO,EAAA,CAAAE,kBAAA,GAAA3O,EAAA,CAAAkO,iBAAA,CAAAO,EAAA,CAAAG,gBAAA,GAAA5O,EAAA,CAAAkO,iBAAA,CAAAW,EAAA,CAAAC,cAAA,GAAA9O,EAAA,CAAAkO,iBAAA,CAAAa,EAAA,CAAAC,cAAA;IAAA;EAAA;;;YAAxBxJ,wBAAwB;MAAAyJ,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAAnP,EAAA,CAAAoP,0BAAA,EAAApP,EAAA,CAAAqP,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,kCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;UCjCnC3P,EADF,CAAAC,cAAA,iBAA0B,qBACR;UACdD,EAAA,CAAA2C,SAAA,qBAAiC;UACnC3C,EAAA,CAAAG,YAAA,EAAiB;UAEfH,EADF,CAAAC,cAAA,mBAAc,YACyB;UAAAD,EAAA,CAAAE,MAAA,2LAA8B;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAIlEH,EAHN,CAAAC,cAAA,aAA8B,aACN,aACqC,eACV;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACvDH,EAAA,CAAAC,cAAA,qBACiB;UADWD,EAAA,CAAA6P,gBAAA,2BAAAC,sEAAA9M,MAAA;YAAAhD,EAAA,CAAAe,aAAA,CAAAgP,GAAA;YAAA/P,EAAA,CAAAgQ,kBAAA,CAAAJ,GAAA,CAAA5I,mBAAA,EAAAhE,MAAA,MAAA4M,GAAA,CAAA5I,mBAAA,GAAAhE,MAAA;YAAA,OAAAhD,EAAA,CAAAqB,WAAA,CAAA2B,MAAA;UAAA,EAAiC;UAAChD,EAAA,CAAAa,UAAA,4BAAAoP,uEAAAjN,MAAA;YAAAhD,EAAA,CAAAe,aAAA,CAAAgP,GAAA;YAAA,OAAA/P,EAAA,CAAAqB,WAAA,CAAkBuO,GAAA,CAAA3H,cAAA,CAAAjF,MAAA,CAAsB;UAAA,EAAC;UAErGhD,EAAA,CAAA8B,UAAA,KAAAoO,8CAAA,wBAAgF;UAKtFlQ,EAFI,CAAAG,YAAA,EAAY,EACR,EACF;UAGFH,EAFJ,CAAAC,cAAA,cAAsB,cACqC,iBACd;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACnDH,EAAA,CAAAC,cAAA,qBACiB;UADaD,EAAA,CAAA6P,gBAAA,2BAAAM,sEAAAnN,MAAA;YAAAhD,EAAA,CAAAe,aAAA,CAAAgP,GAAA;YAAA/P,EAAA,CAAAgQ,kBAAA,CAAAJ,GAAA,CAAAvK,gBAAA,EAAArC,MAAA,MAAA4M,GAAA,CAAAvK,gBAAA,GAAArC,MAAA;YAAA,OAAAhD,EAAA,CAAAqB,WAAA,CAAA2B,MAAA;UAAA,EAA8B;UAAChD,EAAA,CAAAa,UAAA,4BAAAuP,uEAAApN,MAAA;YAAAhD,EAAA,CAAAe,aAAA,CAAAgP,GAAA;YAAA,OAAA/P,EAAA,CAAAqB,WAAA,CAAkBuO,GAAA,CAAAzH,eAAA,CAAAnF,MAAA,CAAuB;UAAA,EAAC;UAErGhD,EAAA,CAAA8B,UAAA,KAAAuO,8CAAA,wBAAyE;UAK/ErQ,EAFI,CAAAG,YAAA,EAAY,EACR,EACF;UAEJH,EADF,CAAAC,cAAA,cAAsB,eAC2B;UAC7CD,EAAA,CAAA8B,UAAA,KAAAwO,2CAAA,qBAAuE;UAI7EtQ,EAFI,CAAAG,YAAA,EAAM,EACF,EACF;UAMEH,EAJR,CAAAC,cAAA,eAAmC,iBAC+D,aACvF,cACgD,cACrB;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACrCH,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACrCH,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACrCH,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACrCH,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAE,MAAA,4CAAM;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACzCH,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAE,MAAA,wGAAgB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACnDH,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAE,MAAA,wDAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAC3CH,EAAA,CAAA2C,SAAA,cAAmC;UAEvC3C,EADE,CAAAG,YAAA,EAAK,EACC;UACRH,EAAA,CAAAC,cAAA,aAAO;UACLD,EAAA,CAAA8B,UAAA,KAAAyO,uCAAA,mBAAgD;UAqBxDvQ,EAHM,CAAAG,YAAA,EAAQ,EACF,EACJ,EACO;UAEbH,EADF,CAAAC,cAAA,0BAAsD,0BAEf;UADqCD,EAA1D,CAAA6P,gBAAA,kCAAAW,kFAAAxN,MAAA;YAAAhD,EAAA,CAAAe,aAAA,CAAAgP,GAAA;YAAA/P,EAAA,CAAAgQ,kBAAA,CAAAJ,GAAA,CAAA/H,YAAA,EAAA7E,MAAA,MAAA4M,GAAA,CAAA/H,YAAA,GAAA7E,MAAA;YAAA,OAAAhD,EAAA,CAAAqB,WAAA,CAAA2B,MAAA;UAAA,EAAiC,4BAAAyN,4EAAAzN,MAAA;YAAAhD,EAAA,CAAAe,aAAA,CAAAgP,GAAA;YAAA/P,EAAA,CAAAgQ,kBAAA,CAAAJ,GAAA,CAAAjI,QAAA,EAAA3E,MAAA,MAAA4M,GAAA,CAAAjI,QAAA,GAAA3E,MAAA;YAAA,OAAAhD,EAAA,CAAAqB,WAAA,CAAA2B,MAAA;UAAA,EAAwB,wBAAA0N,wEAAA1N,MAAA;YAAAhD,EAAA,CAAAe,aAAA,CAAAgP,GAAA;YAAA/P,EAAA,CAAAgQ,kBAAA,CAAAJ,GAAA,CAAAtI,SAAA,EAAAtE,MAAA,MAAA4M,GAAA,CAAAtI,SAAA,GAAAtE,MAAA;YAAA,OAAAhD,EAAA,CAAAqB,WAAA,CAAA2B,MAAA;UAAA,EAAqB;UAC5FhD,EAAA,CAAAa,UAAA,wBAAA6P,wEAAA1N,MAAA;YAAAhD,EAAA,CAAAe,aAAA,CAAAgP,GAAA;YAAA,OAAA/P,EAAA,CAAAqB,WAAA,CAAcuO,GAAA,CAAA5H,WAAA,CAAAhF,MAAA,CAAmB;UAAA,EAAC;UAGxChD,EAFI,CAAAG,YAAA,EAAiB,EACF,EACT;UAEVH,EAAA,CAAA8B,UAAA,KAAA6O,gDAAA,iCAAA3Q,EAAA,CAAAgF,sBAAA,CAAkD;;;UAvEZhF,EAAA,CAAAO,SAAA,IAAiC;UAAjCP,EAAA,CAAA4Q,gBAAA,YAAAhB,GAAA,CAAA5I,mBAAA,CAAiC;UAE1BhH,EAAA,CAAAO,SAAA,EAAqB;UAArBP,EAAA,CAAAI,UAAA,YAAAwP,GAAA,CAAAzJ,kBAAA,CAAqB;UAS1BnG,EAAA,CAAAO,SAAA,GAA8B;UAA9BP,EAAA,CAAA4Q,gBAAA,YAAAhB,GAAA,CAAAvK,gBAAA,CAA8B;UAE5BrF,EAAA,CAAAO,SAAA,EAAkB;UAAlBP,EAAA,CAAAI,UAAA,YAAAwP,GAAA,CAAAtJ,eAAA,CAAkB;UAQKtG,EAAA,CAAAO,SAAA,GAAc;UAAdP,EAAA,CAAAI,UAAA,SAAAwP,GAAA,CAAAiB,QAAA,CAAc;UAqBhD7Q,EAAA,CAAAO,SAAA,IAAY;UAAZP,EAAA,CAAAI,UAAA,YAAAwP,GAAA,CAAA1J,MAAA,CAAY;UAuBvBlG,EAAA,CAAAO,SAAA,GAAiC;UAAyBP,EAA1D,CAAA4Q,gBAAA,mBAAAhB,GAAA,CAAA/H,YAAA,CAAiC,aAAA+H,GAAA,CAAAjI,QAAA,CAAwB,SAAAiI,GAAA,CAAAtI,SAAA,CAAqB;;;qBD/C9F7H,YAAY,EAAAqR,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EAAAF,EAAA,CAAAG,QAAA,EACZpR,YAAY,EAAAqR,EAAA,CAAAC,eAAA,EAAAD,EAAA,CAAAE,OAAA,EAAA/C,EAAA,CAAAgD,eAAA,EAAAhD,EAAA,CAAAiD,mBAAA,EAAAjD,EAAA,CAAAkD,qBAAA,EAAAlD,EAAA,CAAAmD,qBAAA,EAAAnD,EAAA,CAAAoD,gBAAA,EAAApD,EAAA,CAAAqD,iBAAA,EAAArD,EAAA,CAAAsD,iBAAA,EAAAC,EAAA,CAAAC,mBAAA,EAAAC,GAAA,CAAAC,mBAAA,EAAAC,GAAA,CAAAC,YAAA;MAAAC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}