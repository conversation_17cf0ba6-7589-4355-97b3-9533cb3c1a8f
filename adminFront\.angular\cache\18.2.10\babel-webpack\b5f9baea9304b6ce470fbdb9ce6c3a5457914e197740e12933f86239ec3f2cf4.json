{"ast": null, "code": "import { LocalStorageService } from 'src/app/shared/services/local-storage.service';\nimport * as moment from 'moment';\nimport { CommonModule } from '@angular/common';\nimport { CalendarModule, CalendarView } from 'angular-calendar';\nimport { DialogModule } from 'primeng/dialog';\nimport { concatMap, of, tap } from 'rxjs';\nimport { SharedModule } from 'src/app/pages/components/shared.module';\nimport { EEvent } from 'src/app/shared/services/event.service';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/services/api/services\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"src/app/shared/services/message.service\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"@nebular/theme\";\nimport * as i6 from \"src/app/shared/services/event.service\";\nimport * as i7 from \"../../../components/breadcrumb/breadcrumb.component\";\nimport * as i8 from \"angular-calendar\";\nfunction EditAvailableTimeSlotComponent_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 20);\n    i0.ɵɵlistener(\"click\", function EditAvailableTimeSlotComponent_div_5_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      const dialogConfirm_r4 = i0.ɵɵreference(32);\n      return i0.ɵɵresetView(ctx_r2.addNew(dialogConfirm_r4));\n    });\n    i0.ɵɵtext(1, \" \\u8907\\u88FD\\u4E0A\\u4E00\\u5468\\u8A2D\\u5B9A \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction EditAvailableTimeSlotComponent_ng_template_27_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"date\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const segment_r5 = i0.ɵɵnextContext().segment;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(2, 1, segment_r5.date, \"HH:mm\"));\n  }\n}\nfunction EditAvailableTimeSlotComponent_ng_template_27_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 24)(1, \"input\", 25);\n    i0.ɵɵlistener(\"click\", function EditAvailableTimeSlotComponent_ng_template_27_div_2_Template_input_click_1_listener($event) {\n      i0.ɵɵrestoreView(_r6);\n      const segment_r5 = i0.ɵɵnextContext().segment;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onCheck($event, segment_r5.date));\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const segment_r5 = i0.ɵɵnextContext().segment;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"className\", ctx_r2.isCellEmpty(segment_r5.date) ? \"bg-event\" : \"bg-inherit\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"id\", segment_r5.date.toISOString())(\"checked\", ctx_r2.isCellEmpty(segment_r5.date))(\"disabled\", ctx_r2.checkDisable(segment_r5.date));\n  }\n}\nfunction EditAvailableTimeSlotComponent_ng_template_27_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 21);\n    i0.ɵɵtemplate(1, EditAvailableTimeSlotComponent_ng_template_27_span_1_Template, 3, 4, \"span\", 22)(2, EditAvailableTimeSlotComponent_ng_template_27_div_2_Template, 2, 4, \"div\", 23);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const isTimeLabel_r7 = ctx.isTimeLabel;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", isTimeLabel_r7);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !isTimeLabel_r7);\n  }\n}\nfunction EditAvailableTimeSlotComponent_ng_template_29_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 26);\n  }\n}\nfunction EditAvailableTimeSlotComponent_ng_template_31_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-card\")(1, \"nb-card-body\", 27);\n    i0.ɵɵtext(2, \" \\u8ACB\\u78BA\\u8A8D\\u662F\\u5426\\u8A2D\\u5B9A\\u70BA\\u4E0A\\u5468\\u6642\\u6BB5\\u8A2D\\u5B9A \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"nb-card-footer\", 28)(4, \"button\", 29);\n    i0.ɵɵlistener(\"click\", function EditAvailableTimeSlotComponent_ng_template_31_Template_button_click_4_listener() {\n      const ref_r9 = i0.ɵɵrestoreView(_r8).dialogRef;\n      return i0.ɵɵresetView(ref_r9.close());\n    });\n    i0.ɵɵtext(5, \"\\u53D6\\u6D88\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"button\", 30);\n    i0.ɵɵlistener(\"click\", function EditAvailableTimeSlotComponent_ng_template_31_Template_button_click_6_listener() {\n      const ref_r9 = i0.ɵɵrestoreView(_r8).dialogRef;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.confirmCopy(ref_r9));\n    });\n    i0.ɵɵtext(7, \"\\u78BA\\u8A8D\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nexport class EditAvailableTimeSlotComponent {\n  constructor(changeDetector, preOderSettingService, route, _router, messageService, _location, dialogService, _eventService) {\n    this.changeDetector = changeDetector;\n    this.preOderSettingService = preOderSettingService;\n    this.route = route;\n    this._router = _router;\n    this.messageService = messageService;\n    this._location = _location;\n    this.dialogService = dialogService;\n    this._eventService = _eventService;\n    this.locale = 'zh';\n    this.getPreOderSettingRes = [];\n    this.activeDayIsOpen = true;\n    this.savePreOrderSetting = [];\n    this.listEvent = [];\n    this.view = CalendarView.Week;\n    this.viewDate = new Date();\n    this.paramInfo = null;\n    this.listDate = [];\n    this.buildCaseId = this.route.snapshot.paramMap.get('id');\n    this.paramInfo = JSON.parse(LocalStorageService.GetLocalStorage('paramInfo'));\n  }\n  ngOnInit() {\n    if (this.paramInfo) {\n      this.viewDate = new Date(this.paramInfo.CDateStart);\n    }\n    this.getPreOrderSetting().subscribe();\n  }\n  getPreOrderSetting() {\n    return this.preOderSettingService.apiPreOrderSettingGetPreOrderSettingPost$Json({\n      body: {\n        CBuildCaseID: this.buildCaseId\n      }\n    }).pipe(tap(res => {\n      if (res.StatusCode === 0) {\n        this.getPreOderSettingRes = res.Entries ? res.Entries.filter(x => x.CHour ? x.CHour > 8 && x.CHour < 22 : null) : [];\n        this.initEvent();\n      }\n    }));\n  }\n  initEvent() {\n    this.listEvent = [];\n    if (this.getPreOderSettingRes && this.getPreOderSettingRes.length > 0) {\n      this.getPreOderSettingRes.forEach(e => {\n        if (e.CHour && e.CHour > 8 && e.CHour < 22) {\n          let date = e.CDate ? new Date(e.CDate) : undefined;\n          date = date ? new Date(date.setMinutes(0)) : date;\n          date = date ? new Date(date.setSeconds(0)) : date;\n          let startDate = date && e.CHour ? new Date(date.setHours(e.CHour)) : 0;\n          let endDate = date && e.CHour ? new Date(date.setHours(e.CHour + 1)) : 0;\n          this.listEvent.push({\n            id: e.CID,\n            CBuildCaseId: this.buildCaseId,\n            CDate: e.CDate,\n            CHour: e.CHour,\n            CIsDelete: false,\n            isChange: false,\n            start: startDate,\n            end: endDate,\n            display: \"background\",\n            IsOld: true\n          });\n        }\n      });\n    }\n  }\n  handleDateSelect(selectInfo) {\n    if (selectInfo.start.getDate() === selectInfo.end.getDate() && selectInfo.end.getHours() - selectInfo.start.getHours() === 1) {\n      const calendarApi = selectInfo.view.calendar;\n      calendarApi.unselect(); // clear date selection\n      calendarApi.addEvent({\n        start: selectInfo.startStr,\n        end: selectInfo.endStr,\n        display: 'background'\n      });\n      this.listEvent.push({\n        CBuildCaseId: this.buildCaseId,\n        CDate: selectInfo.startStr,\n        CHour: selectInfo.start.getHours(),\n        CIsDelete: false,\n        isChange: true,\n        start: selectInfo.start,\n        end: selectInfo.end\n      });\n    }\n  }\n  save() {\n    this.savedData(true).subscribe(res => {\n      this.backToList();\n    });\n  }\n  backToList() {\n    this._eventService.push({\n      action: \"GET_BUILDCASE\" /* EEvent.GET_BUILDCASE */,\n      payload: this.buildCaseId\n    });\n    this._location.back();\n  }\n  // Function to check if the cell is empty\n  isCellEmpty(day) {\n    const startHour = day.getHours();\n    return this.listEvent.some(event => {\n      return event.CHour === startHour && new Date(event.start).getDate() === day.getDate() && new Date(event.start).getMonth() === day.getMonth() && new Date(event.start).getFullYear() === day.getFullYear() && event.IsOld;\n    });\n  }\n  checkDisable(day) {\n    if (day.getTime() < new Date().getTime()) {\n      return true;\n    }\n    return false;\n  }\n  handleEventClick(clickInfo) {\n    let eventIndex = this.listEvent.findIndex(x => typeof x.start === 'number' ? x.start === clickInfo.event.start.getTime() : x.start.getTime() === clickInfo.event.start.getTime() && typeof x.end === 'number' ? x.end === clickInfo.event.end.getTime() : x.end.getTime() === clickInfo.event.end.getTime());\n    if (eventIndex !== -1) {\n      this.listEvent[eventIndex].CIsDelete = true;\n      this.listEvent[eventIndex].isChange = true;\n      clickInfo.event.remove();\n    }\n  }\n  onCheck(e, segment) {\n    let eventIndex = this.listEvent.findIndex(x => x.start.getFullYear() === segment.getFullYear() && x.start.getMonth() === segment.getMonth() && x.start.getDate() === segment.getDate() && x.CHour === segment.getHours());\n    // click empty checkbox\n    if (eventIndex === -1) {\n      // create new event\n      if (e.target.checked) {\n        this.listEvent.push({\n          CBuildCaseId: this.buildCaseId,\n          CDate: moment(segment).format('YYYY-MM-DDTHH:mm:ss'),\n          CHour: segment.getHours(),\n          CIsDelete: false,\n          isChange: true,\n          start: segment,\n          IsOld: false\n        });\n      }\n    } else {\n      // unchecked checkbox\n      if (e.target.checked) {\n        this.listEvent[eventIndex].CIsDelete = false;\n        this.listEvent[eventIndex].isChange = true;\n      } else {\n        this.listEvent[eventIndex].CIsDelete = true;\n        this.listEvent[eventIndex].isChange = true;\n      }\n    }\n  }\n  addNew(ref) {\n    if (this.handleDataWeek().dataFromCurrent.length > 0) {\n      this.dialogService.open(ref);\n    } else {\n      this.savedData(false).subscribe();\n    }\n  }\n  onClose(ref) {\n    ref.close();\n  }\n  confirmCopy(ref) {\n    this.savedData(false).subscribe(() => this.onClose(ref));\n  }\n  handleDataWeek() {\n    let firstDateofLastWeek = this.sub_weeks(this.viewDate, 1);\n    let lastDateofCurrentWeek = this.add_weeks(this.viewDate, 1);\n    let dataFromPrevious = this.listEvent.filter(x => new Date(x.CDate).getTime() >= new Date(firstDateofLastWeek).getTime() && new Date(x.CDate).getTime() <= new Date(this.viewDate).getTime());\n    let dataFromCurrent = this.listEvent.filter(x => new Date(x.CDate).getTime() <= new Date(lastDateofCurrentWeek).getTime() && new Date(x.CDate).getTime() >= new Date(this.viewDate).getTime());\n    return {\n      dataFromCurrent,\n      dataFromPrevious\n    };\n  }\n  handleShowButton() {\n    var result = new Date(this.viewDate);\n    result.setDate(result.getDate() + 7);\n    if (result.getTime() < Date.now()) {\n      return false;\n    }\n    if (this.handleDataWeek().dataFromCurrent.length == 0 && this.handleDataWeek().dataFromPrevious.length == 0) {\n      return false;\n    }\n    return true;\n  }\n  savedData(needToFilter) {\n    if (needToFilter) {\n      this.savePreOrderSetting = this.listEvent.filter(x => x.isChange === true).map(e => {\n        this.listDate.push(new Date(e.CDate));\n        return {\n          CBuildCaseID: parseInt(e.CBuildCaseId),\n          CDate: e.CDate,\n          CHour: e.CHour,\n          CIsDelete: e.CIsDelete\n        };\n      });\n    } else {\n      this.handleDataWeek().dataFromCurrent.forEach(e => {\n        if (this.handleDataWeek().dataFromPrevious.some(x => new Date(e.CDate).getDate() == this.add_weeks(x.CDate, 1).getDate() && x.CHour === e.CHour)) {\n          this.savePreOrderSetting.push({\n            CBuildCaseID: parseInt(e.CBuildCaseId),\n            CDate: this.fromDateToString(this.get_date(e.CDate)),\n            CHour: e.CHour,\n            CIsDelete: e.CIsDelete\n          });\n        } else {\n          this.savePreOrderSetting.push({\n            CBuildCaseID: parseInt(e.CBuildCaseId),\n            CDate: this.fromDateToString(this.get_date(e.CDate)),\n            CHour: e.CHour,\n            CIsDelete: true\n          });\n        }\n      });\n      this.handleDataWeek().dataFromPrevious.forEach(e => {\n        this.savePreOrderSetting.push({\n          CBuildCaseID: parseInt(e.CBuildCaseId),\n          CDate: this.fromDateToString(this.add_weeks(e.CDate, 1)),\n          CHour: e.CHour,\n          CIsDelete: e.CIsDelete\n        });\n      });\n      this.savePreOrderSetting.filter(x => !x.CIsDelete).forEach(x => {\n        this.listDate.push(new Date(x.CDate));\n      });\n    }\n    if (this.savePreOrderSetting.length == 0) {\n      this.backToList();\n      return of();\n    }\n    let minDate = new Date(Math.min.apply(null, this.listDate));\n    let paramSave = {\n      buildCaseId: this.buildCaseId,\n      minDate: minDate\n    };\n    return this.preOderSettingService.apiPreOrderSettingSavePreOrderSettingPost$Json({\n      body: {\n        CSavePreOrderSetting: this.savePreOrderSetting\n      }\n    }).pipe(tap(res => {\n      if (res.StatusCode === 0) {\n        this.messageService.showSucessMSG('Save Successfully!');\n        this.savePreOrderSetting = [];\n        this.listDate = [];\n        this.listEvent.forEach(x => {\n          if (x.isChange) {\n            let id = document.getElementById(x.start.toISOString());\n            setTimeout(() => {\n              id?.click();\n            }, 200);\n          }\n        });\n        LocalStorageService.AddLocalStorage('paramSave', JSON.stringify(paramSave));\n      }\n    }), concatMap(() => this.getPreOrderSetting()));\n  }\n  add_weeks(dt, n) {\n    return new Date(new Date(dt).setDate(new Date(dt).getDate() + n * 7));\n  }\n  sub_weeks(dt, n) {\n    return new Date(new Date(dt).setDate(new Date(dt).getDate() - n * 7));\n  }\n  get_date(dt) {\n    return new Date(new Date(dt).setDate(new Date(dt).getDate()));\n  }\n  fromDateToString(date) {\n    date = new Date(+date);\n    date.setTime(date.getTime() - date.getTimezoneOffset() * 60000);\n    let dateAsString = date.toISOString().substr(0, 19);\n    return dateAsString;\n  }\n  static {\n    this.ɵfac = function EditAvailableTimeSlotComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || EditAvailableTimeSlotComponent)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i1.PreOrderSettingService), i0.ɵɵdirectiveInject(i2.ActivatedRoute), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i3.MessageService), i0.ɵɵdirectiveInject(i4.Location), i0.ɵɵdirectiveInject(i5.NbDialogService), i0.ɵɵdirectiveInject(i6.EventService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: EditAvailableTimeSlotComponent,\n      selectors: [[\"app-edit-available-time-slot\"]],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 33,\n      vars: 19,\n      consts: [[\"hourSegmentTemplate\", \"\"], [\"currentTimeMarkerTemplate\", \"\"], [\"dialogConfirm\", \"\"], [\"accent\", \"success\"], [1, \"w-full\", \"flex\", \"justify-end\", \"mb-4\"], [\"class\", \"text-white p-3 bg-[#169bd5] cursor-pointer rounded-md\", 3, \"click\", 4, \"ngIf\"], [1, \"row\", \"text-left\", \"mb-4\"], [1, \"col-md-4\"], [1, \"btn-group\"], [\"mwlCalendarPreviousView\", \"\", 1, \"btn\", \"btn-success\", 3, \"viewDateChange\", \"view\", \"viewDate\"], [1, \"fa\", \"fa-chevron-left\"], [1, \"col-md-4\", \"text-center\"], [1, \"col-md-4\", \"text-right\"], [\"mwlCalendarNextView\", \"\", 1, \"btn\", \"btn-success\", 3, \"viewDateChange\", \"view\", \"viewDate\"], [1, \"fa\", \"fa-chevron-right\"], [3, \"hourDuration\", \"hourSegments\", \"viewDate\", \"locale\", \"dayStartHour\", \"dayEndHour\", \"hourSegmentTemplate\", \"currentTimeMarkerTemplate\", \"eventSnapSize\"], [1, \"row\"], [1, \"col-md-12\", \"text-center\"], [1, \"btn\", \"btn-secondary\", \"m-1\", 3, \"click\"], [1, \"btn\", \"btn-success\", \"m-1\", 3, \"click\"], [1, \"text-white\", \"p-3\", \"bg-[#169bd5]\", \"cursor-pointer\", \"rounded-md\", 3, \"click\"], [1, \"hour-segment\", \"text-center\", \"border\", 2, \"height\", \"100%\"], [4, \"ngIf\"], [\"style\", \"height: 100%;\", 3, \"className\", 4, \"ngIf\"], [2, \"height\", \"100%\", 3, \"className\"], [\"type\", \"checkbox\", 1, \"align-middle\", 3, \"click\", \"id\", \"checked\", \"disabled\"], [2, \"display\", \"none\"], [1, \"px-4\"], [1, \"flex\", \"justify-center\", \"w-full\"], [1, \"btn\", \"btn-outline-secondary\", \"m-2\", 3, \"click\"], [1, \"btn\", \"btn-success\", \"m-2\", 3, \"click\"]],\n      template: function EditAvailableTimeSlotComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"nb-card\", 3)(1, \"nb-card-header\");\n          i0.ɵɵelement(2, \"ngx-breadcrumb\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(3, \"nb-card-body\")(4, \"div\", 4);\n          i0.ɵɵtemplate(5, EditAvailableTimeSlotComponent_div_5_Template, 2, 0, \"div\", 5);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(6, \"div\", 6)(7, \"div\", 7)(8, \"div\", 8)(9, \"div\", 9);\n          i0.ɵɵtwoWayListener(\"viewDateChange\", function EditAvailableTimeSlotComponent_Template_div_viewDateChange_9_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.viewDate, $event) || (ctx.viewDate = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵelement(10, \"i\", 10);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(11, \"div\", 11)(12, \"h2\");\n          i0.ɵɵtext(13);\n          i0.ɵɵpipe(14, \"calendarDate\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(15, \"div\", 12)(16, \"div\", 8)(17, \"div\", 13);\n          i0.ɵɵtwoWayListener(\"viewDateChange\", function EditAvailableTimeSlotComponent_Template_div_viewDateChange_17_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.viewDate, $event) || (ctx.viewDate = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵelement(18, \"i\", 14);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelement(19, \"mwl-calendar-week-view\", 15);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(20, \"nb-card-body\")(21, \"div\", 16)(22, \"div\", 17)(23, \"button\", 18);\n          i0.ɵɵlistener(\"click\", function EditAvailableTimeSlotComponent_Template_button_click_23_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.backToList());\n          });\n          i0.ɵɵtext(24, \"\\u53D6\\u6D88\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(25, \"button\", 19);\n          i0.ɵɵlistener(\"click\", function EditAvailableTimeSlotComponent_Template_button_click_25_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.save());\n          });\n          i0.ɵɵtext(26, \"\\u5132\\u5B58\");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵtemplate(27, EditAvailableTimeSlotComponent_ng_template_27_Template, 3, 2, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor)(29, EditAvailableTimeSlotComponent_ng_template_29_Template, 1, 0, \"ng-template\", null, 1, i0.ɵɵtemplateRefExtractor)(31, EditAvailableTimeSlotComponent_ng_template_31_Template, 8, 0, \"ng-template\", null, 2, i0.ɵɵtemplateRefExtractor);\n        }\n        if (rf & 2) {\n          const hourSegmentTemplate_r10 = i0.ɵɵreference(28);\n          const currentTimeMarkerTemplate_r11 = i0.ɵɵreference(30);\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"ngIf\", ctx.handleShowButton());\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"view\", ctx.view);\n          i0.ɵɵtwoWayProperty(\"viewDate\", ctx.viewDate);\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate(i0.ɵɵpipeBind3(14, 15, ctx.viewDate, ctx.view + \"ViewTitle\", \"zh\"));\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"view\", ctx.view);\n          i0.ɵɵtwoWayProperty(\"viewDate\", ctx.viewDate);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"hourDuration\", 60)(\"hourSegments\", 1)(\"viewDate\", ctx.viewDate)(\"locale\", ctx.locale)(\"dayStartHour\", 9)(\"dayEndHour\", 21)(\"hourSegmentTemplate\", hourSegmentTemplate_r10)(\"currentTimeMarkerTemplate\", currentTimeMarkerTemplate_r11)(\"eventSnapSize\", 0);\n        }\n      },\n      dependencies: [CommonModule, i4.NgIf, i4.DatePipe, SharedModule, i5.NbCardComponent, i5.NbCardBodyComponent, i5.NbCardFooterComponent, i5.NbCardHeaderComponent, i7.BreadcrumbComponent, CalendarModule, i8.ɵCalendarPreviousViewDirective, i8.ɵCalendarNextViewDirective, i8.ɵCalendarDatePipe, i8.CalendarWeekViewComponent, DialogModule],\n      styles: [\".empty-cell[_ngcontent-%COMP%] {\\n  position: relative;\\n}\\n\\n.empty-cell[_ngcontent-%COMP%]   a[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 50%;\\n  left: 50%;\\n  transform: translate(-50%, -50%);\\n  color: blue;\\n  text-decoration: underline;\\n  cursor: pointer;\\n}\\n\\ninput[type=checkbox][_ngcontent-%COMP%] {\\n  width: 20px;\\n  height: 20px;\\n  color: rgba(255, 255, 255, 0.8);\\n  box-shadow: none;\\n}\\n\\ninput[type=checkbox][_ngcontent-%COMP%]:after {\\n  box-shadow: none;\\n  border: 1px solid black;\\n}\\n\\ninput[type=checkbox][_ngcontent-%COMP%]:before {\\n  color: transparent !important;\\n  border: 2px solid black;\\n}\\n\\ninput[type=checkbox][_ngcontent-%COMP%]:checked:before {\\n  color: black !important;\\n}\\n\\n.bg-inherit[_ngcontent-%COMP%] {\\n  background-color: inherit !important;\\n}\\n\\n.bg-event[_ngcontent-%COMP%] {\\n  background-color: #89d0ff !important;\\n}\\n/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImVkaXQtYXZhaWxhYmxlLXRpbWUtc2xvdC5jb21wb25lbnQuc2NzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFBQTtFQUNJLGtCQUFBO0FBQ0o7O0FBRUE7RUFDSSxrQkFBQTtFQUNBLFFBQUE7RUFDQSxTQUFBO0VBQ0EsZ0NBQUE7RUFDQSxXQUFBO0VBQ0EsMEJBQUE7RUFDQSxlQUFBO0FBQ0o7O0FBRUE7RUFDSSxXQUFBO0VBQ0EsWUFBQTtFQUNBLCtCQUFBO0VBQ0EsZ0JBQUE7QUFDSjs7QUFFQTtFQUNJLGdCQUFBO0VBQ0EsdUJBQUE7QUFDSjs7QUFFQTtFQUNJLDZCQUFBO0VBQ0EsdUJBQUE7QUFDSjs7QUFFQTtFQUNDLHVCQUFBO0FBQ0Q7O0FBRUE7RUFDSSxvQ0FBQTtBQUNKOztBQUVBO0VBQ0ksb0NBQUE7QUFDSiIsImZpbGUiOiJlZGl0LWF2YWlsYWJsZS10aW1lLXNsb3QuY29tcG9uZW50LnNjc3MiLCJzb3VyY2VzQ29udGVudCI6WyIuZW1wdHktY2VsbCB7XHJcbiAgICBwb3NpdGlvbjogcmVsYXRpdmU7XHJcbn1cclxuXHJcbi5lbXB0eS1jZWxsIGEge1xyXG4gICAgcG9zaXRpb246IGFic29sdXRlO1xyXG4gICAgdG9wOiA1MCU7XHJcbiAgICBsZWZ0OiA1MCU7XHJcbiAgICB0cmFuc2Zvcm06IHRyYW5zbGF0ZSgtNTAlLCAtNTAlKTtcclxuICAgIGNvbG9yOiBibHVlO1xyXG4gICAgdGV4dC1kZWNvcmF0aW9uOiB1bmRlcmxpbmU7XHJcbiAgICBjdXJzb3I6IHBvaW50ZXI7XHJcbn1cclxuXHJcbmlucHV0W3R5cGU9Y2hlY2tib3hdIHtcclxuICAgIHdpZHRoOiAyMHB4O1xyXG4gICAgaGVpZ2h0OiAyMHB4O1xyXG4gICAgY29sb3I6ICNmZmZmZmZjYztcclxuICAgIGJveC1zaGFkb3c6IG5vbmU7XHJcbn1cclxuXHJcbmlucHV0W3R5cGU9Y2hlY2tib3hdOmFmdGVyIHtcclxuICAgIGJveC1zaGFkb3c6IG5vbmU7XHJcbiAgICBib3JkZXI6IDFweCBzb2xpZCBibGFjaztcclxufVxyXG5cclxuaW5wdXRbdHlwZT1jaGVja2JveF06YmVmb3JlIHtcclxuICAgIGNvbG9yOiB0cmFuc3BhcmVudCAhaW1wb3J0YW50O1xyXG4gICAgYm9yZGVyOiAycHggc29saWQgYmxhY2s7XHJcbn1cclxuXHJcbmlucHV0W3R5cGU9Y2hlY2tib3hdOmNoZWNrZWQ6YmVmb3JlIHtcclxuXHRjb2xvcjogYmxhY2sgIWltcG9ydGFudDtcclxufVxyXG5cclxuLmJnLWluaGVyaXR7XHJcbiAgICBiYWNrZ3JvdW5kLWNvbG9yOiBpbmhlcml0ICFpbXBvcnRhbnQ7XHJcbn1cclxuXHJcbi5iZy1ldmVudCB7XHJcbiAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjODlkMGZmICFpbXBvcnRhbnQ7XHJcbn0iXX0= */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvcGFnZXMvcmVzZXJ2YXRpb24tdGltZS1tYW5hZ2VtZW50L2F2YWlsYWJsZS10aW1lLXNsb3QvZWRpdC1hdmFpbGFibGUtdGltZS1zbG90L2VkaXQtYXZhaWxhYmxlLXRpbWUtc2xvdC5jb21wb25lbnQuc2NzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFBQTtFQUNJLGtCQUFBO0FBQ0o7O0FBRUE7RUFDSSxrQkFBQTtFQUNBLFFBQUE7RUFDQSxTQUFBO0VBQ0EsZ0NBQUE7RUFDQSxXQUFBO0VBQ0EsMEJBQUE7RUFDQSxlQUFBO0FBQ0o7O0FBRUE7RUFDSSxXQUFBO0VBQ0EsWUFBQTtFQUNBLCtCQUFBO0VBQ0EsZ0JBQUE7QUFDSjs7QUFFQTtFQUNJLGdCQUFBO0VBQ0EsdUJBQUE7QUFDSjs7QUFFQTtFQUNJLDZCQUFBO0VBQ0EsdUJBQUE7QUFDSjs7QUFFQTtFQUNDLHVCQUFBO0FBQ0Q7O0FBRUE7RUFDSSxvQ0FBQTtBQUNKOztBQUVBO0VBQ0ksb0NBQUE7QUFDSjtBQUNBLHdzREFBd3NEIiwic291cmNlc0NvbnRlbnQiOlsiLmVtcHR5LWNlbGwge1xyXG4gICAgcG9zaXRpb246IHJlbGF0aXZlO1xyXG59XHJcblxyXG4uZW1wdHktY2VsbCBhIHtcclxuICAgIHBvc2l0aW9uOiBhYnNvbHV0ZTtcclxuICAgIHRvcDogNTAlO1xyXG4gICAgbGVmdDogNTAlO1xyXG4gICAgdHJhbnNmb3JtOiB0cmFuc2xhdGUoLTUwJSwgLTUwJSk7XHJcbiAgICBjb2xvcjogYmx1ZTtcclxuICAgIHRleHQtZGVjb3JhdGlvbjogdW5kZXJsaW5lO1xyXG4gICAgY3Vyc29yOiBwb2ludGVyO1xyXG59XHJcblxyXG5pbnB1dFt0eXBlPWNoZWNrYm94XSB7XHJcbiAgICB3aWR0aDogMjBweDtcclxuICAgIGhlaWdodDogMjBweDtcclxuICAgIGNvbG9yOiAjZmZmZmZmY2M7XHJcbiAgICBib3gtc2hhZG93OiBub25lO1xyXG59XHJcblxyXG5pbnB1dFt0eXBlPWNoZWNrYm94XTphZnRlciB7XHJcbiAgICBib3gtc2hhZG93OiBub25lO1xyXG4gICAgYm9yZGVyOiAxcHggc29saWQgYmxhY2s7XHJcbn1cclxuXHJcbmlucHV0W3R5cGU9Y2hlY2tib3hdOmJlZm9yZSB7XHJcbiAgICBjb2xvcjogdHJhbnNwYXJlbnQgIWltcG9ydGFudDtcclxuICAgIGJvcmRlcjogMnB4IHNvbGlkIGJsYWNrO1xyXG59XHJcblxyXG5pbnB1dFt0eXBlPWNoZWNrYm94XTpjaGVja2VkOmJlZm9yZSB7XHJcblx0Y29sb3I6IGJsYWNrICFpbXBvcnRhbnQ7XHJcbn1cclxuXHJcbi5iZy1pbmhlcml0e1xyXG4gICAgYmFja2dyb3VuZC1jb2xvcjogaW5oZXJpdCAhaW1wb3J0YW50O1xyXG59XHJcblxyXG4uYmctZXZlbnQge1xyXG4gICAgYmFja2dyb3VuZC1jb2xvcjogIzg5ZDBmZiAhaW1wb3J0YW50O1xyXG59Il0sInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["LocalStorageService", "moment", "CommonModule", "CalendarModule", "CalendarView", "DialogModule", "concatMap", "of", "tap", "SharedModule", "EEvent", "i0", "ɵɵelementStart", "ɵɵlistener", "EditAvailableTimeSlotComponent_div_5_Template_div_click_0_listener", "ɵɵrestoreView", "_r2", "ctx_r2", "ɵɵnextContext", "dialogConfirm_r4", "ɵɵreference", "ɵɵresetView", "addNew", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate", "ɵɵpipeBind2", "segment_r5", "date", "EditAvailableTimeSlotComponent_ng_template_27_div_2_Template_input_click_1_listener", "$event", "_r6", "segment", "onCheck", "ɵɵproperty", "isCellEmpty", "toISOString", "checkDisable", "ɵɵtemplate", "EditAvailableTimeSlotComponent_ng_template_27_span_1_Template", "EditAvailableTimeSlotComponent_ng_template_27_div_2_Template", "isTimeLabel_r7", "ɵɵelement", "EditAvailableTimeSlotComponent_ng_template_31_Template_button_click_4_listener", "ref_r9", "_r8", "dialogRef", "close", "EditAvailableTimeSlotComponent_ng_template_31_Template_button_click_6_listener", "confirmCopy", "EditAvailableTimeSlotComponent", "constructor", "changeDetector", "preOderSettingService", "route", "_router", "messageService", "_location", "dialogService", "_eventService", "locale", "getPreOderSettingRes", "activeDayIsOpen", "savePreOrderSetting", "listEvent", "view", "Week", "viewDate", "Date", "paramInfo", "listDate", "buildCaseId", "snapshot", "paramMap", "get", "JSON", "parse", "GetLocalStorage", "ngOnInit", "CDateStart", "getPreOrderSetting", "subscribe", "apiPreOrderSettingGetPreOrderSettingPost$Json", "body", "CBuildCaseID", "pipe", "res", "StatusCode", "Entries", "filter", "x", "CHour", "initEvent", "length", "for<PERSON>ach", "e", "CDate", "undefined", "setMinutes", "setSeconds", "startDate", "setHours", "endDate", "push", "id", "CID", "CBuildCaseId", "CIsDelete", "isChange", "start", "end", "display", "IsOld", "handleDateSelect", "selectInfo", "getDate", "getHours", "calendarApi", "calendar", "unselect", "addEvent", "startStr", "endStr", "save", "savedData", "backToList", "action", "payload", "back", "day", "startHour", "some", "event", "getMonth", "getFullYear", "getTime", "handleEventClick", "clickInfo", "eventIndex", "findIndex", "remove", "target", "checked", "format", "ref", "handleDataWeek", "dataFromCurrent", "open", "onClose", "firstDateofLastWeek", "sub_weeks", "lastDateofCurrentWeek", "add_weeks", "dataFromPrevious", "handleShowButton", "result", "setDate", "now", "needToFilter", "map", "parseInt", "fromDateToString", "get_date", "minDate", "Math", "min", "apply", "paramSave", "apiPreOrderSettingSavePreOrderSettingPost$Json", "CSavePreOrderSetting", "showSucessMSG", "document", "getElementById", "setTimeout", "click", "AddLocalStorage", "stringify", "dt", "n", "setTime", "getTimezoneOffset", "dateAsString", "substr", "ɵɵdirectiveInject", "ChangeDetectorRef", "i1", "PreOrderSettingService", "i2", "ActivatedRoute", "Router", "i3", "MessageService", "i4", "Location", "i5", "NbDialogService", "i6", "EventService", "selectors", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "EditAvailableTimeSlotComponent_Template", "rf", "ctx", "EditAvailableTimeSlotComponent_div_5_Template", "ɵɵtwoWayListener", "EditAvailableTimeSlotComponent_Template_div_viewDateChange_9_listener", "_r1", "ɵɵtwoWayBindingSet", "EditAvailableTimeSlotComponent_Template_div_viewDateChange_17_listener", "EditAvailableTimeSlotComponent_Template_button_click_23_listener", "EditAvailableTimeSlotComponent_Template_button_click_25_listener", "EditAvailableTimeSlotComponent_ng_template_27_Template", "ɵɵtemplateRefExtractor", "EditAvailableTimeSlotComponent_ng_template_29_Template", "EditAvailableTimeSlotComponent_ng_template_31_Template", "ɵɵtwoWayProperty", "ɵɵpipeBind3", "hourSegmentTemplate_r10", "currentTimeMarkerTemplate_r11", "NgIf", "DatePipe", "NbCardComponent", "NbCardBodyComponent", "NbCardFooterComponent", "NbCardHeaderComponent", "i7", "BreadcrumbComponent", "i8", "ɵCalendarPreviousViewDirective", "ɵCalendarNextViewDirective", "ɵCalendarDatePipe", "CalendarWeekViewComponent", "styles"], "sources": ["C:\\Users\\<USER>\\Documents\\jeansalechange\\adminFront\\src\\app\\pages\\reservation-time-management\\available-time-slot\\edit-available-time-slot\\edit-available-time-slot.component.ts", "C:\\Users\\<USER>\\Documents\\jeansalechange\\adminFront\\src\\app\\pages\\reservation-time-management\\available-time-slot\\edit-available-time-slot\\edit-available-time-slot.component.html"], "sourcesContent": ["import { ChangeDetector<PERSON><PERSON>, Component, <PERSON><PERSON><PERSON>, On<PERSON><PERSON>roy, OnInit, signal } from '@angular/core';\r\nimport { PreOrderSettingService } from 'src/services/api/services';\r\nimport { LocalStorageService } from 'src/app/shared/services/local-storage.service';\r\nimport { GetPreOrderSettingResponse, PreOrderSetting } from 'src/services/api/models';\r\nimport { ActivatedRoute, Router } from '@angular/router';\r\nimport { MessageService } from 'src/app/shared/services/message.service';\r\nimport * as moment from 'moment';\r\nimport { CommonModule, Location } from '@angular/common';\r\nimport { CalendarModule, CalendarView } from 'angular-calendar';\r\nimport { DialogModule } from 'primeng/dialog'\r\nimport { NbDialogService } from '@nebular/theme';\r\nimport { concatMap, mergeMap, of, tap } from 'rxjs';\r\nimport { SharedModule } from 'src/app/pages/components/shared.module';\r\nimport { EventService, EEvent } from 'src/app/shared/services/event.service';\r\n\r\n@Component({\r\n  selector: 'app-edit-available-time-slot',\r\n  standalone: true,\r\n  imports: [\r\n    CommonModule,\r\n    SharedModule,\r\n    CalendarModule,\r\n    DialogModule\r\n  ],\r\n  templateUrl: './edit-available-time-slot.component.html',\r\n  styleUrls: ['./edit-available-time-slot.component.scss']\r\n})\r\nexport class EditAvailableTimeSlotComponent implements OnInit {\r\n  locale: string = 'zh'\r\n  buildCaseId: any | null\r\n  getPreOderSettingRes = [] as GetPreOrderSettingResponse[]\r\n  activeDayIsOpen: boolean = true;\r\n  savePreOrderSetting = [] as PreOrderSetting[]\r\n\r\n  listEvent: any[] = []\r\n\r\n  view: CalendarView = CalendarView.Week;\r\n  viewDate: Date = new Date();\r\n  paramInfo: any = null\r\n  listDate: any = []\r\n\r\n  constructor(\r\n    private changeDetector: ChangeDetectorRef,\r\n    private preOderSettingService: PreOrderSettingService,\r\n    private route: ActivatedRoute,\r\n    private _router: Router,\r\n    private messageService: MessageService,\r\n    private _location: Location,\r\n    private dialogService: NbDialogService,\r\n    private _eventService: EventService\r\n  ) {\r\n    this.buildCaseId = this.route.snapshot.paramMap.get('id')\r\n    this.paramInfo = JSON.parse(LocalStorageService.GetLocalStorage('paramInfo'))\r\n  }\r\n\r\n  ngOnInit() {\r\n    if (this.paramInfo) {\r\n      this.viewDate = new Date(this.paramInfo.CDateStart)\r\n    }\r\n    this.getPreOrderSetting().subscribe()\r\n  }\r\n\r\n  getPreOrderSetting() {\r\n    return this.preOderSettingService.apiPreOrderSettingGetPreOrderSettingPost$Json({\r\n      body: {\r\n        CBuildCaseID: this.buildCaseId,\r\n      }\r\n    }).pipe(\r\n      tap(res => {\r\n        if (res.StatusCode === 0) {\r\n          this.getPreOderSettingRes = res.Entries ? res.Entries.filter(x => x.CHour ? x.CHour > 8 && x.CHour < 22 : null) : []\r\n          this.initEvent()\r\n        }\r\n      })\r\n    )\r\n  }\r\n\r\n  initEvent() {\r\n    this.listEvent = []\r\n    if (this.getPreOderSettingRes && this.getPreOderSettingRes.length > 0) {\r\n      this.getPreOderSettingRes.forEach(e => {\r\n        if (e.CHour && e.CHour > 8 && e.CHour < 22) {\r\n          let date = e.CDate ? new Date(e.CDate) : undefined;\r\n          date = date ? new Date(date.setMinutes(0)) : date\r\n          date = date ? new Date(date.setSeconds(0)) : date\r\n          let startDate = date && e.CHour ? new Date(date.setHours(e.CHour)) : 0\r\n          let endDate = date && e.CHour ? new Date(date.setHours(e.CHour + 1)) : 0\r\n          this.listEvent.push({\r\n            id: e.CID,\r\n            CBuildCaseId: this.buildCaseId,\r\n            CDate: e.CDate,\r\n            CHour: e.CHour,\r\n            CIsDelete: false,\r\n            isChange: false,\r\n            start: startDate,\r\n            end: endDate,\r\n            display: \"background\",\r\n            IsOld: true\r\n          })\r\n        }\r\n      })\r\n    }\r\n  }\r\n\r\n  handleDateSelect(selectInfo: any) {\r\n    if (selectInfo.start.getDate() === selectInfo.end.getDate() && selectInfo.end.getHours() - selectInfo.start.getHours() === 1) {\r\n      const calendarApi = selectInfo.view.calendar;\r\n\r\n      calendarApi.unselect(); // clear date selection\r\n\r\n      calendarApi.addEvent({\r\n        start: selectInfo.startStr,\r\n        end: selectInfo.endStr,\r\n        display: 'background',\r\n      });\r\n\r\n      this.listEvent.push({\r\n        CBuildCaseId: this.buildCaseId,\r\n        CDate: selectInfo.startStr,\r\n        CHour: selectInfo.start.getHours(),\r\n        CIsDelete: false,\r\n        isChange: true,\r\n        start: selectInfo.start,\r\n        end: selectInfo.end,\r\n      })\r\n    }\r\n  }\r\n\r\n  save() {\r\n    this.savedData(true).subscribe(res => {\r\n      this.backToList()\r\n    })\r\n  }\r\n\r\n  backToList() {\r\n    this._eventService.push({\r\n      action: EEvent.GET_BUILDCASE,\r\n      payload: this.buildCaseId\r\n    })\r\n    this._location.back()\r\n  }\r\n\r\n  // Function to check if the cell is empty\r\n  isCellEmpty(day: Date): boolean {\r\n    const startHour = day.getHours();\r\n    return this.listEvent.some(event => {\r\n      return event.CHour === startHour\r\n        && new Date(event.start).getDate() === day.getDate()\r\n        && new Date(event.start).getMonth() === day.getMonth()\r\n        && new Date(event.start).getFullYear() === day.getFullYear()\r\n        && event.IsOld;\r\n    });\r\n  }\r\n\r\n  checkDisable(day: Date) {\r\n    if (day.getTime() < new Date().getTime()) {\r\n      return true;\r\n    }\r\n\r\n    return false;\r\n  }\r\n\r\n  handleEventClick(clickInfo: any) {\r\n    let eventIndex = this.listEvent.findIndex(x => typeof (x.start) === 'number' ? x.start === clickInfo.event.start.getTime() : x.start.getTime() === clickInfo.event.start.getTime()\r\n      && typeof (x.end) === 'number' ? x.end === clickInfo.event.end.getTime() : x.end.getTime() === clickInfo.event.end.getTime())\r\n    if (eventIndex !== -1) {\r\n      this.listEvent[eventIndex].CIsDelete = true\r\n      this.listEvent[eventIndex].isChange = true\r\n      clickInfo.event.remove();\r\n    }\r\n  }\r\n\r\n  onCheck(e: any, segment: Date) {\r\n    let eventIndex = this.listEvent.findIndex(x => x.start.getFullYear() === segment.getFullYear()\r\n      && x.start.getMonth() === segment.getMonth()\r\n      && x.start.getDate() === segment.getDate()\r\n      && x.CHour === segment.getHours())\r\n\r\n    // click empty checkbox\r\n    if (eventIndex === -1) {\r\n      // create new event\r\n      if (e.target.checked) {\r\n        this.listEvent.push({\r\n          CBuildCaseId: this.buildCaseId,\r\n          CDate: moment(segment).format('YYYY-MM-DDTHH:mm:ss'),\r\n          CHour: segment.getHours(),\r\n          CIsDelete: false,\r\n          isChange: true,\r\n          start: segment,\r\n          IsOld: false\r\n        })\r\n      }\r\n    } else { // unchecked checkbox\r\n      if (e.target.checked) {\r\n        this.listEvent[eventIndex].CIsDelete = false\r\n        this.listEvent[eventIndex].isChange = true\r\n      } else {\r\n        this.listEvent[eventIndex].CIsDelete = true\r\n        this.listEvent[eventIndex].isChange = true\r\n      }\r\n    }\r\n  }\r\n\r\n  addNew(ref: any) {\r\n    if (this.handleDataWeek().dataFromCurrent.length > 0) {\r\n      this.dialogService.open(ref)\r\n    } else {\r\n      this.savedData(false).subscribe()\r\n    }\r\n  }\r\n\r\n  onClose(ref: any) {\r\n    ref.close()\r\n  }\r\n\r\n  confirmCopy(ref: any) {\r\n    this.savedData(false).subscribe(() => this.onClose(ref))\r\n  }\r\n\r\n  handleDataWeek() {\r\n    let firstDateofLastWeek = this.sub_weeks(this.viewDate, 1)\r\n    let lastDateofCurrentWeek = this.add_weeks(this.viewDate, 1)\r\n\r\n    let dataFromPrevious = this.listEvent.filter(x => new Date(x.CDate).getTime() >= new Date(firstDateofLastWeek).getTime()\r\n      && new Date(x.CDate).getTime() <= new Date(this.viewDate).getTime())\r\n\r\n    let dataFromCurrent = this.listEvent.filter(x => new Date(x.CDate).getTime() <= new Date(lastDateofCurrentWeek).getTime()\r\n      && new Date(x.CDate).getTime() >= new Date(this.viewDate).getTime())\r\n\r\n    return { dataFromCurrent, dataFromPrevious }\r\n  }\r\n\r\n  handleShowButton() {\r\n    var result = new Date(this.viewDate);\r\n    result.setDate(result.getDate() + 7);\r\n    if (result.getTime() < Date.now()) {\r\n      return false;\r\n    }\r\n    if (this.handleDataWeek().dataFromCurrent.length == 0 && this.handleDataWeek().dataFromPrevious.length == 0) {\r\n      return false;\r\n    }\r\n    return true\r\n  }\r\n\r\n  savedData(needToFilter: boolean) {\r\n    if (needToFilter) {\r\n      this.savePreOrderSetting = this.listEvent.filter(x => x.isChange === true).map(e => {\r\n        this.listDate.push(new Date(e.CDate))\r\n        return {\r\n          CBuildCaseID: parseInt(e.CBuildCaseId),\r\n          CDate: e.CDate,\r\n          CHour: e.CHour,\r\n          CIsDelete: e.CIsDelete\r\n        }\r\n      })\r\n    } else {\r\n      this.handleDataWeek().dataFromCurrent.forEach(e => {\r\n        if (this.handleDataWeek().dataFromPrevious.some(x => new Date(e.CDate).getDate() == this.add_weeks(x.CDate, 1).getDate() && x.CHour === e.CHour)) {\r\n          this.savePreOrderSetting.push({\r\n            CBuildCaseID: parseInt(e.CBuildCaseId),\r\n            CDate: this.fromDateToString(this.get_date(e.CDate)),\r\n            CHour: e.CHour,\r\n            CIsDelete: e.CIsDelete\r\n          })\r\n        } else {\r\n          this.savePreOrderSetting.push({\r\n            CBuildCaseID: parseInt(e.CBuildCaseId),\r\n            CDate: this.fromDateToString(this.get_date(e.CDate)),\r\n            CHour: e.CHour,\r\n            CIsDelete: true\r\n          })\r\n        }\r\n      })\r\n      this.handleDataWeek().dataFromPrevious.forEach(e => {\r\n        this.savePreOrderSetting.push({\r\n          CBuildCaseID: parseInt(e.CBuildCaseId),\r\n          CDate: this.fromDateToString(this.add_weeks(e.CDate, 1)),\r\n          CHour: e.CHour,\r\n          CIsDelete: e.CIsDelete\r\n        })\r\n      })\r\n\r\n      this.savePreOrderSetting.filter(x => !x.CIsDelete).forEach(x => {\r\n        this.listDate.push(new Date(x.CDate!))\r\n      })\r\n    }\r\n    if (this.savePreOrderSetting.length == 0) {\r\n      this.backToList()\r\n      return of();\r\n    }\r\n    let minDate = new Date(Math.min.apply(null, this.listDate))\r\n    let paramSave = {\r\n      buildCaseId: this.buildCaseId,\r\n      minDate: minDate\r\n    }\r\n    return this.preOderSettingService.apiPreOrderSettingSavePreOrderSettingPost$Json({\r\n      body: {\r\n        CSavePreOrderSetting: this.savePreOrderSetting\r\n      }\r\n    }).pipe(\r\n      tap(res => {\r\n        if (res.StatusCode === 0) {\r\n          this.messageService.showSucessMSG('Save Successfully!')\r\n          this.savePreOrderSetting = []\r\n          this.listDate = []\r\n          this.listEvent.forEach(x => {\r\n            if (x.isChange) {\r\n              let id = document.getElementById(x.start.toISOString())\r\n              setTimeout(() => {\r\n                id?.click()\r\n              }, 200);\r\n            }\r\n          });\r\n          LocalStorageService.AddLocalStorage('paramSave', JSON.stringify(paramSave))\r\n        }\r\n      }),\r\n      concatMap(() => this.getPreOrderSetting()),\r\n    )\r\n  }\r\n\r\n  add_weeks(dt: Date, n: number) {\r\n    return new Date(new Date(dt).setDate(new Date(dt).getDate() + (n * 7)));\r\n  }\r\n\r\n  sub_weeks(dt: Date, n: number) {\r\n    return new Date(new Date(dt).setDate(new Date(dt).getDate() - (n * 7)));\r\n  }\r\n\r\n  get_date(dt: Date) {\r\n    return new Date(new Date(dt).setDate(new Date(dt).getDate()));\r\n  }\r\n\r\n  fromDateToString(date: any) {\r\n    date = new Date(+date);\r\n    date.setTime(date.getTime() - (date.getTimezoneOffset() * 60000));\r\n    let dateAsString = date.toISOString().substr(0, 19);\r\n    return dateAsString;\r\n  }\r\n\r\n}\r\n", "<nb-card accent=\"success\">\r\n  <nb-card-header>\r\n    <ngx-breadcrumb></ngx-breadcrumb>\r\n  </nb-card-header>\r\n  <nb-card-body>\r\n    <div class=\"w-full flex justify-end mb-4\">\r\n      <div *ngIf=\"handleShowButton()\" class=\"text-white p-3 bg-[#169bd5] cursor-pointer rounded-md\"\r\n        (click)=\"addNew(dialogConfirm)\">\r\n        複製上一周設定\r\n      </div>\r\n    </div>\r\n    <div class=\"row text-left mb-4\">\r\n      <div class=\"col-md-4\">\r\n        <div class=\"btn-group\">\r\n          <div class=\"btn btn-success\" mwlCalendarPreviousView [view]=\"view\" [(viewDate)]=\"viewDate\">\r\n            <i class=\"fa fa-chevron-left\"></i>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <div class=\"col-md-4 text-center\">\r\n        <h2>{{ viewDate | calendarDate:(view + 'ViewTitle'):'zh' }}</h2>\r\n      </div>\r\n      <div class=\"col-md-4 text-right\">\r\n        <div class=\"btn-group\">\r\n          <div class=\"btn btn-success\" mwlCalendarNextView [view]=\"view\" [(viewDate)]=\"viewDate\">\r\n            <i class=\"fa fa-chevron-right\"></i>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <mwl-calendar-week-view [hourDuration]=\"60\" [hourSegments]=\"1\" [viewDate]=\"viewDate\" [locale]=\"locale\"\r\n      [dayStartHour]=\"9\" [dayEndHour]=\"21\" [hourSegmentTemplate]=\"hourSegmentTemplate\"\r\n      [currentTimeMarkerTemplate]=\"currentTimeMarkerTemplate\" [eventSnapSize]=\"0\">\r\n    </mwl-calendar-week-view>\r\n  </nb-card-body>\r\n  <nb-card-body>\r\n    <div class=\"row\">\r\n      <div class=\"col-md-12 text-center\">\r\n        <button class=\"btn btn-secondary m-1\" (click)=\"backToList()\">取消</button>\r\n        <button class=\"btn btn-success m-1\" (click)=\"save()\">儲存</button>\r\n      </div>\r\n    </div>\r\n  </nb-card-body>\r\n</nb-card>\r\n\r\n<ng-template #hourSegmentTemplate let-isTimeLabel=\"isTimeLabel\" let-segment=\"segment\">\r\n  <div class=\"hour-segment text-center border\" style=\"height: 100%;\">\r\n    <span *ngIf=\"isTimeLabel\">{{ segment.date | date: 'HH:mm' }}</span>\r\n    <div *ngIf=\"!isTimeLabel\" [className]=\"isCellEmpty(segment.date) ? 'bg-event' : 'bg-inherit'\" style=\"height: 100%;\">\r\n      <input [id]=\"segment.date.toISOString()\" class=\"align-middle\" type=\"checkbox\" [checked]=\"isCellEmpty(segment.date)\"\r\n        [disabled]=\"checkDisable(segment.date)\" (click)=\"onCheck($event, segment.date)\" />\r\n    </div>\r\n  </div>\r\n</ng-template>\r\n\r\n<ng-template #currentTimeMarkerTemplate>\r\n  <div style=\"display: none;\">\r\n\r\n  </div>\r\n</ng-template>\r\n\r\n<ng-template #dialogConfirm let-dialog let-ref=\"dialogRef\">\r\n  <nb-card>\r\n    <nb-card-body class=\"px-4\">\r\n      請確認是否設定為上周時段設定\r\n    </nb-card-body>\r\n    <nb-card-footer class=\"flex justify-center w-full\">\r\n      <button class=\"btn btn-outline-secondary m-2\" (click)=\"ref.close()\">取消</button>\r\n      <button class=\"btn btn-success m-2\" (click)=\"confirmCopy(ref)\">確認</button>\r\n    </nb-card-footer>\r\n  </nb-card>\r\n</ng-template>"], "mappings": "AAEA,SAASA,mBAAmB,QAAQ,+CAA+C;AAInF,OAAO,KAAKC,MAAM,MAAM,QAAQ;AAChC,SAASC,YAAY,QAAkB,iBAAiB;AACxD,SAASC,cAAc,EAAEC,YAAY,QAAQ,kBAAkB;AAC/D,SAASC,YAAY,QAAQ,gBAAgB;AAE7C,SAASC,SAAS,EAAYC,EAAE,EAAEC,GAAG,QAAQ,MAAM;AACnD,SAASC,YAAY,QAAQ,wCAAwC;AACrE,SAAuBC,MAAM,QAAQ,uCAAuC;;;;;;;;;;;;;ICPtEC,EAAA,CAAAC,cAAA,cACkC;IAAhCD,EAAA,CAAAE,UAAA,mBAAAC,mEAAA;MAAAH,EAAA,CAAAI,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,MAAAC,gBAAA,GAAAR,EAAA,CAAAS,WAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASJ,MAAA,CAAAK,MAAA,CAAAH,gBAAA,CAAqB;IAAA,EAAC;IAC/BR,EAAA,CAAAY,MAAA,mDACF;IAAAZ,EAAA,CAAAa,YAAA,EAAM;;;;;IAsCRb,EAAA,CAAAC,cAAA,WAA0B;IAAAD,EAAA,CAAAY,MAAA,GAAkC;;IAAAZ,EAAA,CAAAa,YAAA,EAAO;;;;IAAzCb,EAAA,CAAAc,SAAA,EAAkC;IAAlCd,EAAA,CAAAe,iBAAA,CAAAf,EAAA,CAAAgB,WAAA,OAAAC,UAAA,CAAAC,IAAA,WAAkC;;;;;;IAE1DlB,EADF,CAAAC,cAAA,cAAoH,gBAE9B;IAA1CD,EAAA,CAAAE,UAAA,mBAAAiB,oFAAAC,MAAA;MAAApB,EAAA,CAAAI,aAAA,CAAAiB,GAAA;MAAA,MAAAJ,UAAA,GAAAjB,EAAA,CAAAO,aAAA,GAAAe,OAAA;MAAA,MAAAhB,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAU,WAAA,CAASJ,MAAA,CAAAiB,OAAA,CAAAH,MAAA,EAAAH,UAAA,CAAAC,IAAA,CAA6B;IAAA,EAAC;IACnFlB,EAFE,CAAAa,YAAA,EACoF,EAChF;;;;;IAHoBb,EAAA,CAAAwB,UAAA,cAAAlB,MAAA,CAAAmB,WAAA,CAAAR,UAAA,CAAAC,IAAA,8BAAmE;IACpFlB,EAAA,CAAAc,SAAA,EAAiC;IACtCd,EADK,CAAAwB,UAAA,OAAAP,UAAA,CAAAC,IAAA,CAAAQ,WAAA,GAAiC,YAAApB,MAAA,CAAAmB,WAAA,CAAAR,UAAA,CAAAC,IAAA,EAA2E,aAAAZ,MAAA,CAAAqB,YAAA,CAAAV,UAAA,CAAAC,IAAA,EAC1E;;;;;IAJ7ClB,EAAA,CAAAC,cAAA,cAAmE;IAEjED,EADA,CAAA4B,UAAA,IAAAC,6DAAA,mBAA0B,IAAAC,4DAAA,kBAC0F;IAItH9B,EAAA,CAAAa,YAAA,EAAM;;;;IALGb,EAAA,CAAAc,SAAA,EAAiB;IAAjBd,EAAA,CAAAwB,UAAA,SAAAO,cAAA,CAAiB;IAClB/B,EAAA,CAAAc,SAAA,EAAkB;IAAlBd,EAAA,CAAAwB,UAAA,UAAAO,cAAA,CAAkB;;;;;IAQ1B/B,EAAA,CAAAgC,SAAA,cAEM;;;;;;IAKJhC,EADF,CAAAC,cAAA,cAAS,uBACoB;IACzBD,EAAA,CAAAY,MAAA,6FACF;IAAAZ,EAAA,CAAAa,YAAA,EAAe;IAEbb,EADF,CAAAC,cAAA,yBAAmD,iBACmB;IAAtBD,EAAA,CAAAE,UAAA,mBAAA+B,+EAAA;MAAA,MAAAC,MAAA,GAAAlC,EAAA,CAAAI,aAAA,CAAA+B,GAAA,EAAAC,SAAA;MAAA,OAAApC,EAAA,CAAAU,WAAA,CAASwB,MAAA,CAAAG,KAAA,EAAW;IAAA,EAAC;IAACrC,EAAA,CAAAY,MAAA,mBAAE;IAAAZ,EAAA,CAAAa,YAAA,EAAS;IAC/Eb,EAAA,CAAAC,cAAA,iBAA+D;IAA3BD,EAAA,CAAAE,UAAA,mBAAAoC,+EAAA;MAAA,MAAAJ,MAAA,GAAAlC,EAAA,CAAAI,aAAA,CAAA+B,GAAA,EAAAC,SAAA;MAAA,MAAA9B,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAU,WAAA,CAASJ,MAAA,CAAAiC,WAAA,CAAAL,MAAA,CAAgB;IAAA,EAAC;IAAClC,EAAA,CAAAY,MAAA,mBAAE;IAErEZ,EAFqE,CAAAa,YAAA,EAAS,EAC3D,EACT;;;AD3CZ,OAAM,MAAO2B,8BAA8B;EAczCC,YACUC,cAAiC,EACjCC,qBAA6C,EAC7CC,KAAqB,EACrBC,OAAe,EACfC,cAA8B,EAC9BC,SAAmB,EACnBC,aAA8B,EAC9BC,aAA2B;IAP3B,KAAAP,cAAc,GAAdA,cAAc;IACd,KAAAC,qBAAqB,GAArBA,qBAAqB;IACrB,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,SAAS,GAATA,SAAS;IACT,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,aAAa,GAAbA,aAAa;IArBvB,KAAAC,MAAM,GAAW,IAAI;IAErB,KAAAC,oBAAoB,GAAG,EAAkC;IACzD,KAAAC,eAAe,GAAY,IAAI;IAC/B,KAAAC,mBAAmB,GAAG,EAAuB;IAE7C,KAAAC,SAAS,GAAU,EAAE;IAErB,KAAAC,IAAI,GAAiB9D,YAAY,CAAC+D,IAAI;IACtC,KAAAC,QAAQ,GAAS,IAAIC,IAAI,EAAE;IAC3B,KAAAC,SAAS,GAAQ,IAAI;IACrB,KAAAC,QAAQ,GAAQ,EAAE;IAYhB,IAAI,CAACC,WAAW,GAAG,IAAI,CAACjB,KAAK,CAACkB,QAAQ,CAACC,QAAQ,CAACC,GAAG,CAAC,IAAI,CAAC;IACzD,IAAI,CAACL,SAAS,GAAGM,IAAI,CAACC,KAAK,CAAC7E,mBAAmB,CAAC8E,eAAe,CAAC,WAAW,CAAC,CAAC;EAC/E;EAEAC,QAAQA,CAAA;IACN,IAAI,IAAI,CAACT,SAAS,EAAE;MAClB,IAAI,CAACF,QAAQ,GAAG,IAAIC,IAAI,CAAC,IAAI,CAACC,SAAS,CAACU,UAAU,CAAC;IACrD;IACA,IAAI,CAACC,kBAAkB,EAAE,CAACC,SAAS,EAAE;EACvC;EAEAD,kBAAkBA,CAAA;IAChB,OAAO,IAAI,CAAC3B,qBAAqB,CAAC6B,6CAA6C,CAAC;MAC9EC,IAAI,EAAE;QACJC,YAAY,EAAE,IAAI,CAACb;;KAEtB,CAAC,CAACc,IAAI,CACL9E,GAAG,CAAC+E,GAAG,IAAG;MACR,IAAIA,GAAG,CAACC,UAAU,KAAK,CAAC,EAAE;QACxB,IAAI,CAAC1B,oBAAoB,GAAGyB,GAAG,CAACE,OAAO,GAAGF,GAAG,CAACE,OAAO,CAACC,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACC,KAAK,GAAGD,CAAC,CAACC,KAAK,GAAG,CAAC,IAAID,CAAC,CAACC,KAAK,GAAG,EAAE,GAAG,IAAI,CAAC,GAAG,EAAE;QACpH,IAAI,CAACC,SAAS,EAAE;MAClB;IACF,CAAC,CAAC,CACH;EACH;EAEAA,SAASA,CAAA;IACP,IAAI,CAAC5B,SAAS,GAAG,EAAE;IACnB,IAAI,IAAI,CAACH,oBAAoB,IAAI,IAAI,CAACA,oBAAoB,CAACgC,MAAM,GAAG,CAAC,EAAE;MACrE,IAAI,CAAChC,oBAAoB,CAACiC,OAAO,CAACC,CAAC,IAAG;QACpC,IAAIA,CAAC,CAACJ,KAAK,IAAII,CAAC,CAACJ,KAAK,GAAG,CAAC,IAAII,CAAC,CAACJ,KAAK,GAAG,EAAE,EAAE;UAC1C,IAAI/D,IAAI,GAAGmE,CAAC,CAACC,KAAK,GAAG,IAAI5B,IAAI,CAAC2B,CAAC,CAACC,KAAK,CAAC,GAAGC,SAAS;UAClDrE,IAAI,GAAGA,IAAI,GAAG,IAAIwC,IAAI,CAACxC,IAAI,CAACsE,UAAU,CAAC,CAAC,CAAC,CAAC,GAAGtE,IAAI;UACjDA,IAAI,GAAGA,IAAI,GAAG,IAAIwC,IAAI,CAACxC,IAAI,CAACuE,UAAU,CAAC,CAAC,CAAC,CAAC,GAAGvE,IAAI;UACjD,IAAIwE,SAAS,GAAGxE,IAAI,IAAImE,CAAC,CAACJ,KAAK,GAAG,IAAIvB,IAAI,CAACxC,IAAI,CAACyE,QAAQ,CAACN,CAAC,CAACJ,KAAK,CAAC,CAAC,GAAG,CAAC;UACtE,IAAIW,OAAO,GAAG1E,IAAI,IAAImE,CAAC,CAACJ,KAAK,GAAG,IAAIvB,IAAI,CAACxC,IAAI,CAACyE,QAAQ,CAACN,CAAC,CAACJ,KAAK,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC;UACxE,IAAI,CAAC3B,SAAS,CAACuC,IAAI,CAAC;YAClBC,EAAE,EAAET,CAAC,CAACU,GAAG;YACTC,YAAY,EAAE,IAAI,CAACnC,WAAW;YAC9ByB,KAAK,EAAED,CAAC,CAACC,KAAK;YACdL,KAAK,EAAEI,CAAC,CAACJ,KAAK;YACdgB,SAAS,EAAE,KAAK;YAChBC,QAAQ,EAAE,KAAK;YACfC,KAAK,EAAET,SAAS;YAChBU,GAAG,EAAER,OAAO;YACZS,OAAO,EAAE,YAAY;YACrBC,KAAK,EAAE;WACR,CAAC;QACJ;MACF,CAAC,CAAC;IACJ;EACF;EAEAC,gBAAgBA,CAACC,UAAe;IAC9B,IAAIA,UAAU,CAACL,KAAK,CAACM,OAAO,EAAE,KAAKD,UAAU,CAACJ,GAAG,CAACK,OAAO,EAAE,IAAID,UAAU,CAACJ,GAAG,CAACM,QAAQ,EAAE,GAAGF,UAAU,CAACL,KAAK,CAACO,QAAQ,EAAE,KAAK,CAAC,EAAE;MAC5H,MAAMC,WAAW,GAAGH,UAAU,CAACjD,IAAI,CAACqD,QAAQ;MAE5CD,WAAW,CAACE,QAAQ,EAAE,CAAC,CAAC;MAExBF,WAAW,CAACG,QAAQ,CAAC;QACnBX,KAAK,EAAEK,UAAU,CAACO,QAAQ;QAC1BX,GAAG,EAAEI,UAAU,CAACQ,MAAM;QACtBX,OAAO,EAAE;OACV,CAAC;MAEF,IAAI,CAAC/C,SAAS,CAACuC,IAAI,CAAC;QAClBG,YAAY,EAAE,IAAI,CAACnC,WAAW;QAC9ByB,KAAK,EAAEkB,UAAU,CAACO,QAAQ;QAC1B9B,KAAK,EAAEuB,UAAU,CAACL,KAAK,CAACO,QAAQ,EAAE;QAClCT,SAAS,EAAE,KAAK;QAChBC,QAAQ,EAAE,IAAI;QACdC,KAAK,EAAEK,UAAU,CAACL,KAAK;QACvBC,GAAG,EAAEI,UAAU,CAACJ;OACjB,CAAC;IACJ;EACF;EAEAa,IAAIA,CAAA;IACF,IAAI,CAACC,SAAS,CAAC,IAAI,CAAC,CAAC3C,SAAS,CAACK,GAAG,IAAG;MACnC,IAAI,CAACuC,UAAU,EAAE;IACnB,CAAC,CAAC;EACJ;EAEAA,UAAUA,CAAA;IACR,IAAI,CAAClE,aAAa,CAAC4C,IAAI,CAAC;MACtBuB,MAAM;MACNC,OAAO,EAAE,IAAI,CAACxD;KACf,CAAC;IACF,IAAI,CAACd,SAAS,CAACuE,IAAI,EAAE;EACvB;EAEA;EACA7F,WAAWA,CAAC8F,GAAS;IACnB,MAAMC,SAAS,GAAGD,GAAG,CAACb,QAAQ,EAAE;IAChC,OAAO,IAAI,CAACpD,SAAS,CAACmE,IAAI,CAACC,KAAK,IAAG;MACjC,OAAOA,KAAK,CAACzC,KAAK,KAAKuC,SAAS,IAC3B,IAAI9D,IAAI,CAACgE,KAAK,CAACvB,KAAK,CAAC,CAACM,OAAO,EAAE,KAAKc,GAAG,CAACd,OAAO,EAAE,IACjD,IAAI/C,IAAI,CAACgE,KAAK,CAACvB,KAAK,CAAC,CAACwB,QAAQ,EAAE,KAAKJ,GAAG,CAACI,QAAQ,EAAE,IACnD,IAAIjE,IAAI,CAACgE,KAAK,CAACvB,KAAK,CAAC,CAACyB,WAAW,EAAE,KAAKL,GAAG,CAACK,WAAW,EAAE,IACzDF,KAAK,CAACpB,KAAK;IAClB,CAAC,CAAC;EACJ;EAEA3E,YAAYA,CAAC4F,GAAS;IACpB,IAAIA,GAAG,CAACM,OAAO,EAAE,GAAG,IAAInE,IAAI,EAAE,CAACmE,OAAO,EAAE,EAAE;MACxC,OAAO,IAAI;IACb;IAEA,OAAO,KAAK;EACd;EAEAC,gBAAgBA,CAACC,SAAc;IAC7B,IAAIC,UAAU,GAAG,IAAI,CAAC1E,SAAS,CAAC2E,SAAS,CAACjD,CAAC,IAAI,OAAQA,CAAC,CAACmB,KAAM,KAAK,QAAQ,GAAGnB,CAAC,CAACmB,KAAK,KAAK4B,SAAS,CAACL,KAAK,CAACvB,KAAK,CAAC0B,OAAO,EAAE,GAAG7C,CAAC,CAACmB,KAAK,CAAC0B,OAAO,EAAE,KAAKE,SAAS,CAACL,KAAK,CAACvB,KAAK,CAAC0B,OAAO,EAAE,IAC7K,OAAQ7C,CAAC,CAACoB,GAAI,KAAK,QAAQ,GAAGpB,CAAC,CAACoB,GAAG,KAAK2B,SAAS,CAACL,KAAK,CAACtB,GAAG,CAACyB,OAAO,EAAE,GAAG7C,CAAC,CAACoB,GAAG,CAACyB,OAAO,EAAE,KAAKE,SAAS,CAACL,KAAK,CAACtB,GAAG,CAACyB,OAAO,EAAE,CAAC;IAC/H,IAAIG,UAAU,KAAK,CAAC,CAAC,EAAE;MACrB,IAAI,CAAC1E,SAAS,CAAC0E,UAAU,CAAC,CAAC/B,SAAS,GAAG,IAAI;MAC3C,IAAI,CAAC3C,SAAS,CAAC0E,UAAU,CAAC,CAAC9B,QAAQ,GAAG,IAAI;MAC1C6B,SAAS,CAACL,KAAK,CAACQ,MAAM,EAAE;IAC1B;EACF;EAEA3G,OAAOA,CAAC8D,CAAM,EAAE/D,OAAa;IAC3B,IAAI0G,UAAU,GAAG,IAAI,CAAC1E,SAAS,CAAC2E,SAAS,CAACjD,CAAC,IAAIA,CAAC,CAACmB,KAAK,CAACyB,WAAW,EAAE,KAAKtG,OAAO,CAACsG,WAAW,EAAE,IACzF5C,CAAC,CAACmB,KAAK,CAACwB,QAAQ,EAAE,KAAKrG,OAAO,CAACqG,QAAQ,EAAE,IACzC3C,CAAC,CAACmB,KAAK,CAACM,OAAO,EAAE,KAAKnF,OAAO,CAACmF,OAAO,EAAE,IACvCzB,CAAC,CAACC,KAAK,KAAK3D,OAAO,CAACoF,QAAQ,EAAE,CAAC;IAEpC;IACA,IAAIsB,UAAU,KAAK,CAAC,CAAC,EAAE;MACrB;MACA,IAAI3C,CAAC,CAAC8C,MAAM,CAACC,OAAO,EAAE;QACpB,IAAI,CAAC9E,SAAS,CAACuC,IAAI,CAAC;UAClBG,YAAY,EAAE,IAAI,CAACnC,WAAW;UAC9ByB,KAAK,EAAEhG,MAAM,CAACgC,OAAO,CAAC,CAAC+G,MAAM,CAAC,qBAAqB,CAAC;UACpDpD,KAAK,EAAE3D,OAAO,CAACoF,QAAQ,EAAE;UACzBT,SAAS,EAAE,KAAK;UAChBC,QAAQ,EAAE,IAAI;UACdC,KAAK,EAAE7E,OAAO;UACdgF,KAAK,EAAE;SACR,CAAC;MACJ;IACF,CAAC,MAAM;MAAE;MACP,IAAIjB,CAAC,CAAC8C,MAAM,CAACC,OAAO,EAAE;QACpB,IAAI,CAAC9E,SAAS,CAAC0E,UAAU,CAAC,CAAC/B,SAAS,GAAG,KAAK;QAC5C,IAAI,CAAC3C,SAAS,CAAC0E,UAAU,CAAC,CAAC9B,QAAQ,GAAG,IAAI;MAC5C,CAAC,MAAM;QACL,IAAI,CAAC5C,SAAS,CAAC0E,UAAU,CAAC,CAAC/B,SAAS,GAAG,IAAI;QAC3C,IAAI,CAAC3C,SAAS,CAAC0E,UAAU,CAAC,CAAC9B,QAAQ,GAAG,IAAI;MAC5C;IACF;EACF;EAEAvF,MAAMA,CAAC2H,GAAQ;IACb,IAAI,IAAI,CAACC,cAAc,EAAE,CAACC,eAAe,CAACrD,MAAM,GAAG,CAAC,EAAE;MACpD,IAAI,CAACnC,aAAa,CAACyF,IAAI,CAACH,GAAG,CAAC;IAC9B,CAAC,MAAM;MACL,IAAI,CAACpB,SAAS,CAAC,KAAK,CAAC,CAAC3C,SAAS,EAAE;IACnC;EACF;EAEAmE,OAAOA,CAACJ,GAAQ;IACdA,GAAG,CAACjG,KAAK,EAAE;EACb;EAEAE,WAAWA,CAAC+F,GAAQ;IAClB,IAAI,CAACpB,SAAS,CAAC,KAAK,CAAC,CAAC3C,SAAS,CAAC,MAAM,IAAI,CAACmE,OAAO,CAACJ,GAAG,CAAC,CAAC;EAC1D;EAEAC,cAAcA,CAAA;IACZ,IAAII,mBAAmB,GAAG,IAAI,CAACC,SAAS,CAAC,IAAI,CAACnF,QAAQ,EAAE,CAAC,CAAC;IAC1D,IAAIoF,qBAAqB,GAAG,IAAI,CAACC,SAAS,CAAC,IAAI,CAACrF,QAAQ,EAAE,CAAC,CAAC;IAE5D,IAAIsF,gBAAgB,GAAG,IAAI,CAACzF,SAAS,CAACyB,MAAM,CAACC,CAAC,IAAI,IAAItB,IAAI,CAACsB,CAAC,CAACM,KAAK,CAAC,CAACuC,OAAO,EAAE,IAAI,IAAInE,IAAI,CAACiF,mBAAmB,CAAC,CAACd,OAAO,EAAE,IACnH,IAAInE,IAAI,CAACsB,CAAC,CAACM,KAAK,CAAC,CAACuC,OAAO,EAAE,IAAI,IAAInE,IAAI,CAAC,IAAI,CAACD,QAAQ,CAAC,CAACoE,OAAO,EAAE,CAAC;IAEtE,IAAIW,eAAe,GAAG,IAAI,CAAClF,SAAS,CAACyB,MAAM,CAACC,CAAC,IAAI,IAAItB,IAAI,CAACsB,CAAC,CAACM,KAAK,CAAC,CAACuC,OAAO,EAAE,IAAI,IAAInE,IAAI,CAACmF,qBAAqB,CAAC,CAAChB,OAAO,EAAE,IACpH,IAAInE,IAAI,CAACsB,CAAC,CAACM,KAAK,CAAC,CAACuC,OAAO,EAAE,IAAI,IAAInE,IAAI,CAAC,IAAI,CAACD,QAAQ,CAAC,CAACoE,OAAO,EAAE,CAAC;IAEtE,OAAO;MAAEW,eAAe;MAAEO;IAAgB,CAAE;EAC9C;EAEAC,gBAAgBA,CAAA;IACd,IAAIC,MAAM,GAAG,IAAIvF,IAAI,CAAC,IAAI,CAACD,QAAQ,CAAC;IACpCwF,MAAM,CAACC,OAAO,CAACD,MAAM,CAACxC,OAAO,EAAE,GAAG,CAAC,CAAC;IACpC,IAAIwC,MAAM,CAACpB,OAAO,EAAE,GAAGnE,IAAI,CAACyF,GAAG,EAAE,EAAE;MACjC,OAAO,KAAK;IACd;IACA,IAAI,IAAI,CAACZ,cAAc,EAAE,CAACC,eAAe,CAACrD,MAAM,IAAI,CAAC,IAAI,IAAI,CAACoD,cAAc,EAAE,CAACQ,gBAAgB,CAAC5D,MAAM,IAAI,CAAC,EAAE;MAC3G,OAAO,KAAK;IACd;IACA,OAAO,IAAI;EACb;EAEA+B,SAASA,CAACkC,YAAqB;IAC7B,IAAIA,YAAY,EAAE;MAChB,IAAI,CAAC/F,mBAAmB,GAAG,IAAI,CAACC,SAAS,CAACyB,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACkB,QAAQ,KAAK,IAAI,CAAC,CAACmD,GAAG,CAAChE,CAAC,IAAG;QACjF,IAAI,CAACzB,QAAQ,CAACiC,IAAI,CAAC,IAAInC,IAAI,CAAC2B,CAAC,CAACC,KAAK,CAAC,CAAC;QACrC,OAAO;UACLZ,YAAY,EAAE4E,QAAQ,CAACjE,CAAC,CAACW,YAAY,CAAC;UACtCV,KAAK,EAAED,CAAC,CAACC,KAAK;UACdL,KAAK,EAAEI,CAAC,CAACJ,KAAK;UACdgB,SAAS,EAAEZ,CAAC,CAACY;SACd;MACH,CAAC,CAAC;IACJ,CAAC,MAAM;MACL,IAAI,CAACsC,cAAc,EAAE,CAACC,eAAe,CAACpD,OAAO,CAACC,CAAC,IAAG;QAChD,IAAI,IAAI,CAACkD,cAAc,EAAE,CAACQ,gBAAgB,CAACtB,IAAI,CAACzC,CAAC,IAAI,IAAItB,IAAI,CAAC2B,CAAC,CAACC,KAAK,CAAC,CAACmB,OAAO,EAAE,IAAI,IAAI,CAACqC,SAAS,CAAC9D,CAAC,CAACM,KAAK,EAAE,CAAC,CAAC,CAACmB,OAAO,EAAE,IAAIzB,CAAC,CAACC,KAAK,KAAKI,CAAC,CAACJ,KAAK,CAAC,EAAE;UAChJ,IAAI,CAAC5B,mBAAmB,CAACwC,IAAI,CAAC;YAC5BnB,YAAY,EAAE4E,QAAQ,CAACjE,CAAC,CAACW,YAAY,CAAC;YACtCV,KAAK,EAAE,IAAI,CAACiE,gBAAgB,CAAC,IAAI,CAACC,QAAQ,CAACnE,CAAC,CAACC,KAAK,CAAC,CAAC;YACpDL,KAAK,EAAEI,CAAC,CAACJ,KAAK;YACdgB,SAAS,EAAEZ,CAAC,CAACY;WACd,CAAC;QACJ,CAAC,MAAM;UACL,IAAI,CAAC5C,mBAAmB,CAACwC,IAAI,CAAC;YAC5BnB,YAAY,EAAE4E,QAAQ,CAACjE,CAAC,CAACW,YAAY,CAAC;YACtCV,KAAK,EAAE,IAAI,CAACiE,gBAAgB,CAAC,IAAI,CAACC,QAAQ,CAACnE,CAAC,CAACC,KAAK,CAAC,CAAC;YACpDL,KAAK,EAAEI,CAAC,CAACJ,KAAK;YACdgB,SAAS,EAAE;WACZ,CAAC;QACJ;MACF,CAAC,CAAC;MACF,IAAI,CAACsC,cAAc,EAAE,CAACQ,gBAAgB,CAAC3D,OAAO,CAACC,CAAC,IAAG;QACjD,IAAI,CAAChC,mBAAmB,CAACwC,IAAI,CAAC;UAC5BnB,YAAY,EAAE4E,QAAQ,CAACjE,CAAC,CAACW,YAAY,CAAC;UACtCV,KAAK,EAAE,IAAI,CAACiE,gBAAgB,CAAC,IAAI,CAACT,SAAS,CAACzD,CAAC,CAACC,KAAK,EAAE,CAAC,CAAC,CAAC;UACxDL,KAAK,EAAEI,CAAC,CAACJ,KAAK;UACdgB,SAAS,EAAEZ,CAAC,CAACY;SACd,CAAC;MACJ,CAAC,CAAC;MAEF,IAAI,CAAC5C,mBAAmB,CAAC0B,MAAM,CAACC,CAAC,IAAI,CAACA,CAAC,CAACiB,SAAS,CAAC,CAACb,OAAO,CAACJ,CAAC,IAAG;QAC7D,IAAI,CAACpB,QAAQ,CAACiC,IAAI,CAAC,IAAInC,IAAI,CAACsB,CAAC,CAACM,KAAM,CAAC,CAAC;MACxC,CAAC,CAAC;IACJ;IACA,IAAI,IAAI,CAACjC,mBAAmB,CAAC8B,MAAM,IAAI,CAAC,EAAE;MACxC,IAAI,CAACgC,UAAU,EAAE;MACjB,OAAOvH,EAAE,EAAE;IACb;IACA,IAAI6J,OAAO,GAAG,IAAI/F,IAAI,CAACgG,IAAI,CAACC,GAAG,CAACC,KAAK,CAAC,IAAI,EAAE,IAAI,CAAChG,QAAQ,CAAC,CAAC;IAC3D,IAAIiG,SAAS,GAAG;MACdhG,WAAW,EAAE,IAAI,CAACA,WAAW;MAC7B4F,OAAO,EAAEA;KACV;IACD,OAAO,IAAI,CAAC9G,qBAAqB,CAACmH,8CAA8C,CAAC;MAC/ErF,IAAI,EAAE;QACJsF,oBAAoB,EAAE,IAAI,CAAC1G;;KAE9B,CAAC,CAACsB,IAAI,CACL9E,GAAG,CAAC+E,GAAG,IAAG;MACR,IAAIA,GAAG,CAACC,UAAU,KAAK,CAAC,EAAE;QACxB,IAAI,CAAC/B,cAAc,CAACkH,aAAa,CAAC,oBAAoB,CAAC;QACvD,IAAI,CAAC3G,mBAAmB,GAAG,EAAE;QAC7B,IAAI,CAACO,QAAQ,GAAG,EAAE;QAClB,IAAI,CAACN,SAAS,CAAC8B,OAAO,CAACJ,CAAC,IAAG;UACzB,IAAIA,CAAC,CAACkB,QAAQ,EAAE;YACd,IAAIJ,EAAE,GAAGmE,QAAQ,CAACC,cAAc,CAAClF,CAAC,CAACmB,KAAK,CAACzE,WAAW,EAAE,CAAC;YACvDyI,UAAU,CAAC,MAAK;cACdrE,EAAE,EAAEsE,KAAK,EAAE;YACb,CAAC,EAAE,GAAG,CAAC;UACT;QACF,CAAC,CAAC;QACF/K,mBAAmB,CAACgL,eAAe,CAAC,WAAW,EAAEpG,IAAI,CAACqG,SAAS,CAACT,SAAS,CAAC,CAAC;MAC7E;IACF,CAAC,CAAC,EACFlK,SAAS,CAAC,MAAM,IAAI,CAAC2E,kBAAkB,EAAE,CAAC,CAC3C;EACH;EAEAwE,SAASA,CAACyB,EAAQ,EAAEC,CAAS;IAC3B,OAAO,IAAI9G,IAAI,CAAC,IAAIA,IAAI,CAAC6G,EAAE,CAAC,CAACrB,OAAO,CAAC,IAAIxF,IAAI,CAAC6G,EAAE,CAAC,CAAC9D,OAAO,EAAE,GAAI+D,CAAC,GAAG,CAAE,CAAC,CAAC;EACzE;EAEA5B,SAASA,CAAC2B,EAAQ,EAAEC,CAAS;IAC3B,OAAO,IAAI9G,IAAI,CAAC,IAAIA,IAAI,CAAC6G,EAAE,CAAC,CAACrB,OAAO,CAAC,IAAIxF,IAAI,CAAC6G,EAAE,CAAC,CAAC9D,OAAO,EAAE,GAAI+D,CAAC,GAAG,CAAE,CAAC,CAAC;EACzE;EAEAhB,QAAQA,CAACe,EAAQ;IACf,OAAO,IAAI7G,IAAI,CAAC,IAAIA,IAAI,CAAC6G,EAAE,CAAC,CAACrB,OAAO,CAAC,IAAIxF,IAAI,CAAC6G,EAAE,CAAC,CAAC9D,OAAO,EAAE,CAAC,CAAC;EAC/D;EAEA8C,gBAAgBA,CAACrI,IAAS;IACxBA,IAAI,GAAG,IAAIwC,IAAI,CAAC,CAACxC,IAAI,CAAC;IACtBA,IAAI,CAACuJ,OAAO,CAACvJ,IAAI,CAAC2G,OAAO,EAAE,GAAI3G,IAAI,CAACwJ,iBAAiB,EAAE,GAAG,KAAM,CAAC;IACjE,IAAIC,YAAY,GAAGzJ,IAAI,CAACQ,WAAW,EAAE,CAACkJ,MAAM,CAAC,CAAC,EAAE,EAAE,CAAC;IACnD,OAAOD,YAAY;EACrB;;;uCAtTWnI,8BAA8B,EAAAxC,EAAA,CAAA6K,iBAAA,CAAA7K,EAAA,CAAA8K,iBAAA,GAAA9K,EAAA,CAAA6K,iBAAA,CAAAE,EAAA,CAAAC,sBAAA,GAAAhL,EAAA,CAAA6K,iBAAA,CAAAI,EAAA,CAAAC,cAAA,GAAAlL,EAAA,CAAA6K,iBAAA,CAAAI,EAAA,CAAAE,MAAA,GAAAnL,EAAA,CAAA6K,iBAAA,CAAAO,EAAA,CAAAC,cAAA,GAAArL,EAAA,CAAA6K,iBAAA,CAAAS,EAAA,CAAAC,QAAA,GAAAvL,EAAA,CAAA6K,iBAAA,CAAAW,EAAA,CAAAC,eAAA,GAAAzL,EAAA,CAAA6K,iBAAA,CAAAa,EAAA,CAAAC,YAAA;IAAA;EAAA;;;YAA9BnJ,8BAA8B;MAAAoJ,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAA9L,EAAA,CAAA+L,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,wCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;UC1BzCrM,EADF,CAAAC,cAAA,iBAA0B,qBACR;UACdD,EAAA,CAAAgC,SAAA,qBAAiC;UACnChC,EAAA,CAAAa,YAAA,EAAiB;UAEfb,EADF,CAAAC,cAAA,mBAAc,aAC8B;UACxCD,EAAA,CAAA4B,UAAA,IAAA2K,6CAAA,iBACkC;UAGpCvM,EAAA,CAAAa,YAAA,EAAM;UAIAb,EAHN,CAAAC,cAAA,aAAgC,aACR,aACG,aACsE;UAAxBD,EAAA,CAAAwM,gBAAA,4BAAAC,sEAAArL,MAAA;YAAApB,EAAA,CAAAI,aAAA,CAAAsM,GAAA;YAAA1M,EAAA,CAAA2M,kBAAA,CAAAL,GAAA,CAAA7I,QAAA,EAAArC,MAAA,MAAAkL,GAAA,CAAA7I,QAAA,GAAArC,MAAA;YAAA,OAAApB,EAAA,CAAAU,WAAA,CAAAU,MAAA;UAAA,EAAuB;UACxFpB,EAAA,CAAAgC,SAAA,aAAkC;UAGxChC,EAFI,CAAAa,YAAA,EAAM,EACF,EACF;UAEJb,EADF,CAAAC,cAAA,eAAkC,UAC5B;UAAAD,EAAA,CAAAY,MAAA,IAAuD;;UAC7DZ,EAD6D,CAAAa,YAAA,EAAK,EAC5D;UAGFb,EAFJ,CAAAC,cAAA,eAAiC,cACR,eACkE;UAAxBD,EAAA,CAAAwM,gBAAA,4BAAAI,uEAAAxL,MAAA;YAAApB,EAAA,CAAAI,aAAA,CAAAsM,GAAA;YAAA1M,EAAA,CAAA2M,kBAAA,CAAAL,GAAA,CAAA7I,QAAA,EAAArC,MAAA,MAAAkL,GAAA,CAAA7I,QAAA,GAAArC,MAAA;YAAA,OAAApB,EAAA,CAAAU,WAAA,CAAAU,MAAA;UAAA,EAAuB;UACpFpB,EAAA,CAAAgC,SAAA,aAAmC;UAI3ChC,EAHM,CAAAa,YAAA,EAAM,EACF,EACF,EACF;UACNb,EAAA,CAAAgC,SAAA,kCAGyB;UAC3BhC,EAAA,CAAAa,YAAA,EAAe;UAITb,EAHN,CAAAC,cAAA,oBAAc,eACK,eACoB,kBAC4B;UAAvBD,EAAA,CAAAE,UAAA,mBAAA2M,iEAAA;YAAA7M,EAAA,CAAAI,aAAA,CAAAsM,GAAA;YAAA,OAAA1M,EAAA,CAAAU,WAAA,CAAS4L,GAAA,CAAAnF,UAAA,EAAY;UAAA,EAAC;UAACnH,EAAA,CAAAY,MAAA,oBAAE;UAAAZ,EAAA,CAAAa,YAAA,EAAS;UACxEb,EAAA,CAAAC,cAAA,kBAAqD;UAAjBD,EAAA,CAAAE,UAAA,mBAAA4M,iEAAA;YAAA9M,EAAA,CAAAI,aAAA,CAAAsM,GAAA;YAAA,OAAA1M,EAAA,CAAAU,WAAA,CAAS4L,GAAA,CAAArF,IAAA,EAAM;UAAA,EAAC;UAACjH,EAAA,CAAAY,MAAA,oBAAE;UAI/DZ,EAJ+D,CAAAa,YAAA,EAAS,EAC5D,EACF,EACO,EACP;UAkBVb,EAhBA,CAAA4B,UAAA,KAAAmL,sDAAA,gCAAA/M,EAAA,CAAAgN,sBAAA,CAAsF,KAAAC,sDAAA,gCAAAjN,EAAA,CAAAgN,sBAAA,CAU9C,KAAAE,sDAAA,gCAAAlN,EAAA,CAAAgN,sBAAA,CAMmB;;;;;UAvD/ChN,EAAA,CAAAc,SAAA,GAAwB;UAAxBd,EAAA,CAAAwB,UAAA,SAAA8K,GAAA,CAAAtD,gBAAA,GAAwB;UAQ2BhJ,EAAA,CAAAc,SAAA,GAAa;UAAbd,EAAA,CAAAwB,UAAA,SAAA8K,GAAA,CAAA/I,IAAA,CAAa;UAACvD,EAAA,CAAAmN,gBAAA,aAAAb,GAAA,CAAA7I,QAAA,CAAuB;UAMxFzD,EAAA,CAAAc,SAAA,GAAuD;UAAvDd,EAAA,CAAAe,iBAAA,CAAAf,EAAA,CAAAoN,WAAA,SAAAd,GAAA,CAAA7I,QAAA,EAAA6I,GAAA,CAAA/I,IAAA,sBAAuD;UAIRvD,EAAA,CAAAc,SAAA,GAAa;UAAbd,EAAA,CAAAwB,UAAA,SAAA8K,GAAA,CAAA/I,IAAA,CAAa;UAACvD,EAAA,CAAAmN,gBAAA,aAAAb,GAAA,CAAA7I,QAAA,CAAuB;UAMpEzD,EAAA,CAAAc,SAAA,GAAmB;UAEed,EAFlC,CAAAwB,UAAA,oBAAmB,mBAAmB,aAAA8K,GAAA,CAAA7I,QAAA,CAAsB,WAAA6I,GAAA,CAAApJ,MAAA,CAAkB,mBAClF,kBAAkB,wBAAAmK,uBAAA,CAA4C,8BAAAC,6BAAA,CACzB,oBAAoB;;;qBDb7E/N,YAAY,EAAA+L,EAAA,CAAAiC,IAAA,EAAAjC,EAAA,CAAAkC,QAAA,EACZ1N,YAAY,EAAA0L,EAAA,CAAAiC,eAAA,EAAAjC,EAAA,CAAAkC,mBAAA,EAAAlC,EAAA,CAAAmC,qBAAA,EAAAnC,EAAA,CAAAoC,qBAAA,EAAAC,EAAA,CAAAC,mBAAA,EACZtO,cAAc,EAAAuO,EAAA,CAAAC,8BAAA,EAAAD,EAAA,CAAAE,0BAAA,EAAAF,EAAA,CAAAG,iBAAA,EAAAH,EAAA,CAAAI,yBAAA,EACdzO,YAAY;MAAA0O,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}