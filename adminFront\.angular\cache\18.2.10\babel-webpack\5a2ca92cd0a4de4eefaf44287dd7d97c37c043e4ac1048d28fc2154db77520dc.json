{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { Injectable, InjectionToken, Pipe, Component, ChangeDetectionStrategy, Optional, Inject, Input, ViewChild, NgModule } from '@angular/core';\nimport { BehaviorSubject, Subject } from 'rxjs';\nimport { filter, takeUntil } from 'rxjs/operators';\nimport { trigger, state, style, transition, animate } from '@angular/animations';\nimport * as i2 from '@angular/common';\nimport { CommonModule } from '@angular/common';\nimport * as i1 from '@angular/platform-browser';\nconst _c0 = [\"overlay\"];\nconst _c1 = [\"*\"];\nfunction NgxSpinnerComponent_div_0_div_2_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\");\n  }\n}\nfunction NgxSpinnerComponent_div_0_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtemplate(1, NgxSpinnerComponent_div_0_div_2_div_1_Template, 1, 0, \"div\", 6);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassMap(ctx_r0.spinner.class);\n    i0.ɵɵstyleProp(\"color\", ctx_r0.spinner.color);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r0.spinner.divArray);\n  }\n}\nfunction NgxSpinnerComponent_div_0_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 7);\n    i0.ɵɵpipe(1, \"safeHtml\");\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"innerHTML\", i0.ɵɵpipeBind1(1, 1, ctx_r0.template), i0.ɵɵsanitizeHtml);\n  }\n}\nfunction NgxSpinnerComponent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 2, 0);\n    i0.ɵɵtemplate(2, NgxSpinnerComponent_div_0_div_2_Template, 2, 5, \"div\", 3)(3, NgxSpinnerComponent_div_0_div_3_Template, 2, 3, \"div\", 4);\n    i0.ɵɵelementStart(4, \"div\", 5);\n    i0.ɵɵprojection(5);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵstyleProp(\"background-color\", ctx_r0.spinner.bdColor)(\"z-index\", ctx_r0.spinner.zIndex)(\"position\", ctx_r0.spinner.fullScreen ? \"fixed\" : \"absolute\");\n    i0.ɵɵproperty(\"@.disabled\", ctx_r0.disableAnimation)(\"@fadeIn\", \"in\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r0.template);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.template);\n    i0.ɵɵadvance();\n    i0.ɵɵstyleProp(\"z-index\", ctx_r0.spinner.zIndex);\n  }\n}\nconst LOADERS = {\n  \"ball-8bits\": 16,\n  \"ball-atom\": 4,\n  \"ball-beat\": 3,\n  \"ball-circus\": 5,\n  \"ball-climbing-dot\": 4,\n  \"ball-clip-rotate\": 1,\n  \"ball-clip-rotate-multiple\": 2,\n  \"ball-clip-rotate-pulse\": 2,\n  \"ball-elastic-dots\": 5,\n  \"ball-fall\": 3,\n  \"ball-fussion\": 4,\n  \"ball-grid-beat\": 9,\n  \"ball-grid-pulse\": 9,\n  \"ball-newton-cradle\": 4,\n  \"ball-pulse\": 3,\n  \"ball-pulse-rise\": 5,\n  \"ball-pulse-sync\": 3,\n  \"ball-rotate\": 1,\n  \"ball-running-dots\": 5,\n  \"ball-scale\": 1,\n  \"ball-scale-multiple\": 3,\n  \"ball-scale-pulse\": 2,\n  \"ball-scale-ripple\": 1,\n  \"ball-scale-ripple-multiple\": 3,\n  \"ball-spin\": 8,\n  \"ball-spin-clockwise\": 8,\n  \"ball-spin-clockwise-fade\": 8,\n  \"ball-spin-clockwise-fade-rotating\": 8,\n  \"ball-spin-fade\": 8,\n  \"ball-spin-fade-rotating\": 8,\n  \"ball-spin-rotate\": 2,\n  \"ball-square-clockwise-spin\": 8,\n  \"ball-square-spin\": 8,\n  \"ball-triangle-path\": 3,\n  \"ball-zig-zag\": 2,\n  \"ball-zig-zag-deflect\": 2,\n  cog: 1,\n  \"cube-transition\": 2,\n  fire: 3,\n  \"line-scale\": 5,\n  \"line-scale-party\": 5,\n  \"line-scale-pulse-out\": 5,\n  \"line-scale-pulse-out-rapid\": 5,\n  \"line-spin-clockwise-fade\": 8,\n  \"line-spin-clockwise-fade-rotating\": 8,\n  \"line-spin-fade\": 8,\n  \"line-spin-fade-rotating\": 8,\n  pacman: 6,\n  \"square-jelly-box\": 2,\n  \"square-loader\": 1,\n  \"square-spin\": 1,\n  timer: 1,\n  \"triangle-skew-spin\": 1\n};\nconst DEFAULTS = {\n  BD_COLOR: \"rgba(51,51,51,0.8)\",\n  SPINNER_COLOR: \"#fff\",\n  Z_INDEX: 99999\n};\nconst PRIMARY_SPINNER = \"primary\";\nclass NgxSpinner {\n  constructor(init) {\n    Object.assign(this, init);\n  }\n  static create(init) {\n    if (!init?.template && !init?.type) {\n      console.warn(`[ngx-spinner]: Property \"type\" is missed. Please, provide animation type to <ngx-spinner> component\n        and ensure css is added to angular.json file`);\n    }\n    return new NgxSpinner(init);\n  }\n}\nlet NgxSpinnerService = /*#__PURE__*/(() => {\n  class NgxSpinnerService {\n    /**\n     * Creates an instance of NgxSpinnerService.\n     * @memberof NgxSpinnerService\n     */\n    constructor() {\n      /**\n       * Spinner observable\n       *\n       * @memberof NgxSpinnerService\n       */\n      // private spinnerObservable = new ReplaySubject<NgxSpinner>(1);\n      this.spinnerObservable = new BehaviorSubject(null);\n    }\n    /**\n     * Get subscription of desired spinner\n     * @memberof NgxSpinnerService\n     **/\n    getSpinner(name) {\n      return this.spinnerObservable.asObservable().pipe(filter(x => x && x.name === name));\n    }\n    /**\n     * To show spinner\n     *\n     * @memberof NgxSpinnerService\n     */\n    show(name = PRIMARY_SPINNER, spinner) {\n      return new Promise((resolve, _reject) => {\n        setTimeout(() => {\n          if (spinner && Object.keys(spinner).length) {\n            spinner[\"name\"] = name;\n            this.spinnerObservable.next(new NgxSpinner({\n              ...spinner,\n              show: true\n            }));\n            resolve(true);\n          } else {\n            this.spinnerObservable.next(new NgxSpinner({\n              name,\n              show: true\n            }));\n            resolve(true);\n          }\n        }, 10);\n      });\n    }\n    /**\n     * To hide spinner\n     *\n     * @memberof NgxSpinnerService\n     */\n    hide(name = PRIMARY_SPINNER, debounce = 10) {\n      return new Promise((resolve, _reject) => {\n        setTimeout(() => {\n          this.spinnerObservable.next(new NgxSpinner({\n            name,\n            show: false\n          }));\n          resolve(true);\n        }, debounce);\n      });\n    }\n    static {\n      this.ɵfac = function NgxSpinnerService_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || NgxSpinnerService)();\n      };\n    }\n    static {\n      this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n        token: NgxSpinnerService,\n        factory: NgxSpinnerService.ɵfac,\n        providedIn: \"root\"\n      });\n    }\n  }\n  return NgxSpinnerService;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nconst NGX_SPINNER_CONFIG = new InjectionToken(\"NGX_SPINNER_CONFIG\");\nlet SafeHtmlPipe = /*#__PURE__*/(() => {\n  class SafeHtmlPipe {\n    constructor(_sanitizer) {\n      this._sanitizer = _sanitizer;\n    }\n    transform(v) {\n      if (v) {\n        return this._sanitizer.bypassSecurityTrustHtml(v);\n      }\n    }\n    static {\n      this.ɵfac = function SafeHtmlPipe_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || SafeHtmlPipe)(i0.ɵɵdirectiveInject(i1.DomSanitizer, 16));\n      };\n    }\n    static {\n      this.ɵpipe = /* @__PURE__ */i0.ɵɵdefinePipe({\n        name: \"safeHtml\",\n        type: SafeHtmlPipe,\n        pure: true\n      });\n    }\n  }\n  return SafeHtmlPipe;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet NgxSpinnerComponent = /*#__PURE__*/(() => {\n  class NgxSpinnerComponent {\n    // TODO: https://github.com/Napster2210/ngx-spinner/issues/259\n    // @HostListener(\"document:keydown\", [\"$event\"])\n    // handleKeyboardEvent(event: KeyboardEvent) {\n    //   if (this.spinnerDOM && this.spinnerDOM.nativeElement) {\n    //     if (\n    //       this.fullScreen ||\n    //       (!this.fullScreen && this.isSpinnerZone(event.target))\n    //     ) {\n    //       event.returnValue = false;\n    //       event.preventDefault();\n    //     }\n    //   }\n    // }\n    /**\n     * Creates an instance of NgxSpinnerComponent.\n     *\n     * @memberof NgxSpinnerComponent\n     */\n    constructor(spinnerService, changeDetector, elementRef, globalConfig) {\n      this.spinnerService = spinnerService;\n      this.changeDetector = changeDetector;\n      this.elementRef = elementRef;\n      this.globalConfig = globalConfig;\n      /**\n       * To enable/disable animation\n       *\n       * @type {boolean}\n       * @memberof NgxSpinnerComponent\n       */\n      this.disableAnimation = false;\n      /**\n       * Spinner Object\n       *\n       * @memberof NgxSpinnerComponent\n       */\n      this.spinner = new NgxSpinner();\n      /**\n       * Unsubscribe from spinner's observable\n       *\n       * @memberof NgxSpinnerComponent\n       **/\n      this.ngUnsubscribe = new Subject();\n      /**\n       * To set default ngx-spinner options\n       *\n       * @memberof NgxSpinnerComponent\n       */\n      this.setDefaultOptions = () => {\n        const {\n          type\n        } = this.globalConfig ?? {};\n        this.spinner = NgxSpinner.create({\n          name: this.name,\n          bdColor: this.bdColor,\n          size: this.size,\n          color: this.color,\n          type: this.type ?? type,\n          fullScreen: this.fullScreen,\n          divArray: this.divArray,\n          divCount: this.divCount,\n          show: this.show,\n          zIndex: this.zIndex,\n          template: this.template,\n          showSpinner: this.showSpinner\n        });\n      };\n      this.bdColor = DEFAULTS.BD_COLOR;\n      this.zIndex = DEFAULTS.Z_INDEX;\n      this.color = DEFAULTS.SPINNER_COLOR;\n      this.size = \"large\";\n      this.fullScreen = true;\n      this.name = PRIMARY_SPINNER;\n      this.template = null;\n      this.showSpinner = false;\n      this.divArray = [];\n      this.divCount = 0;\n      this.show = false;\n    }\n    initObservable() {\n      this.spinnerService.getSpinner(this.name).pipe(takeUntil(this.ngUnsubscribe)).subscribe(spinner => {\n        this.setDefaultOptions();\n        Object.assign(this.spinner, spinner);\n        if (spinner.show) {\n          this.onInputChange();\n        }\n        this.changeDetector.detectChanges();\n      });\n    }\n    /**\n     * Initialization method\n     *\n     * @memberof NgxSpinnerComponent\n     */\n    ngOnInit() {\n      this.setDefaultOptions();\n      this.initObservable();\n    }\n    /**\n     * To check event triggers inside the Spinner Zone\n     *\n     * @param {*} element\n     * @returns {boolean}\n     * @memberof NgxSpinnerComponent\n     */\n    isSpinnerZone(element) {\n      if (element === this.elementRef.nativeElement.parentElement) {\n        return true;\n      }\n      return element.parentNode && this.isSpinnerZone(element.parentNode);\n    }\n    /**\n     * On changes event for input variables\n     *\n     * @memberof NgxSpinnerComponent\n     */\n    ngOnChanges(changes) {\n      for (const propName in changes) {\n        if (propName) {\n          const changedProp = changes[propName];\n          if (changedProp.isFirstChange()) {\n            return;\n          } else if (typeof changedProp.currentValue !== \"undefined\" && changedProp.currentValue !== changedProp.previousValue) {\n            if (changedProp.currentValue !== \"\") {\n              this.spinner[propName] = changedProp.currentValue;\n              if (propName === \"showSpinner\") {\n                if (changedProp.currentValue) {\n                  this.spinnerService.show(this.spinner.name, this.spinner);\n                } else {\n                  this.spinnerService.hide(this.spinner.name);\n                }\n              }\n              if (propName === \"name\") {\n                this.initObservable();\n              }\n            }\n          }\n        }\n      }\n    }\n    /**\n     * To get class for spinner\n     *\n     * @memberof NgxSpinnerComponent\n     */\n    getClass(type, size) {\n      this.spinner.divCount = LOADERS[type];\n      this.spinner.divArray = Array(this.spinner.divCount).fill(0).map((_, i) => i);\n      let sizeClass = \"\";\n      switch (size.toLowerCase()) {\n        case \"small\":\n          sizeClass = \"la-sm\";\n          break;\n        case \"medium\":\n          sizeClass = \"la-2x\";\n          break;\n        case \"large\":\n          sizeClass = \"la-3x\";\n          break;\n        default:\n          break;\n      }\n      return \"la-\" + type + \" \" + sizeClass;\n    }\n    /**\n     * Check if input variables have changed\n     *\n     * @memberof NgxSpinnerComponent\n     */\n    onInputChange() {\n      this.spinner.class = this.getClass(this.spinner.type, this.spinner.size);\n    }\n    /**\n     * Component destroy event\n     *\n     * @memberof NgxSpinnerComponent\n     */\n    ngOnDestroy() {\n      this.ngUnsubscribe.next();\n      this.ngUnsubscribe.complete();\n    }\n    static {\n      this.ɵfac = function NgxSpinnerComponent_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || NgxSpinnerComponent)(i0.ɵɵdirectiveInject(NgxSpinnerService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(NGX_SPINNER_CONFIG, 8));\n      };\n    }\n    static {\n      this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n        type: NgxSpinnerComponent,\n        selectors: [[\"ngx-spinner\"]],\n        viewQuery: function NgxSpinnerComponent_Query(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵviewQuery(_c0, 5);\n          }\n          if (rf & 2) {\n            let _t;\n            i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.spinnerDOM = _t.first);\n          }\n        },\n        inputs: {\n          bdColor: \"bdColor\",\n          size: \"size\",\n          color: \"color\",\n          type: \"type\",\n          fullScreen: \"fullScreen\",\n          name: \"name\",\n          zIndex: \"zIndex\",\n          template: \"template\",\n          showSpinner: \"showSpinner\",\n          disableAnimation: \"disableAnimation\"\n        },\n        features: [i0.ɵɵNgOnChangesFeature],\n        ngContentSelectors: _c1,\n        decls: 1,\n        vars: 1,\n        consts: [[\"overlay\", \"\"], [\"class\", \"ngx-spinner-overlay\", 3, \"background-color\", \"z-index\", \"position\", 4, \"ngIf\"], [1, \"ngx-spinner-overlay\"], [3, \"class\", \"color\", 4, \"ngIf\"], [3, \"innerHTML\", 4, \"ngIf\"], [1, \"loading-text\"], [4, \"ngFor\", \"ngForOf\"], [3, \"innerHTML\"]],\n        template: function NgxSpinnerComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵprojectionDef();\n            i0.ɵɵtemplate(0, NgxSpinnerComponent_div_0_Template, 6, 12, \"div\", 1);\n          }\n          if (rf & 2) {\n            i0.ɵɵproperty(\"ngIf\", ctx.spinner.show);\n          }\n        },\n        dependencies: [i2.NgForOf, i2.NgIf, SafeHtmlPipe],\n        styles: [\".ngx-spinner-overlay[_ngcontent-%COMP%]{position:fixed;top:0;left:0;width:100%;height:100%}.ngx-spinner-overlay[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]:not(.loading-text){top:50%;left:50%;margin:0;position:absolute;transform:translate(-50%,-50%)}.loading-text[_ngcontent-%COMP%]{position:absolute;top:60%;left:50%;transform:translate(-50%,-60%)}\"],\n        data: {\n          animation: [trigger(\"fadeIn\", [state(\"in\", style({\n            opacity: 1\n          })), transition(\":enter\", [style({\n            opacity: 0\n          }), animate(300)]), transition(\":leave\", animate(200, style({\n            opacity: 0\n          })))])]\n        },\n        changeDetection: 0\n      });\n    }\n  }\n  return NgxSpinnerComponent;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet NgxSpinnerModule = /*#__PURE__*/(() => {\n  class NgxSpinnerModule {\n    static forRoot(config) {\n      return {\n        ngModule: NgxSpinnerModule,\n        providers: [{\n          provide: NGX_SPINNER_CONFIG,\n          useValue: config\n        }]\n      };\n    }\n    static {\n      this.ɵfac = function NgxSpinnerModule_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || NgxSpinnerModule)();\n      };\n    }\n    static {\n      this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n        type: NgxSpinnerModule\n      });\n    }\n    static {\n      this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n        imports: [CommonModule]\n      });\n    }\n  }\n  return NgxSpinnerModule;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/*\n * Public API Surface of ngx-spinner\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { DEFAULTS, LOADERS, NgxSpinner, NgxSpinnerComponent, NgxSpinnerModule, NgxSpinnerService, PRIMARY_SPINNER };\n//# sourceMappingURL=ngx-spinner.mjs.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}