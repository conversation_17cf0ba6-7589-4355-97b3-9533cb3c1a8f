{"ast": null, "code": "import { map } from 'rxjs/operators';\nimport { BaseService } from '../base-service';\nimport { apiBuildCaseFileDeleteBuildCaseFilePost$Json } from '../fn/build-case-file/api-build-case-file-delete-build-case-file-post-json';\nimport { apiBuildCaseFileDeleteBuildCaseFilePost$Plain } from '../fn/build-case-file/api-build-case-file-delete-build-case-file-post-plain';\nimport { apiBuildCaseFileGetBuildCaseFileByIdPost$Json } from '../fn/build-case-file/api-build-case-file-get-build-case-file-by-id-post-json';\nimport { apiBuildCaseFileGetBuildCaseFileByIdPost$Plain } from '../fn/build-case-file/api-build-case-file-get-build-case-file-by-id-post-plain';\nimport { apiBuildCaseFileGetListBuildCaseFilePost$Json } from '../fn/build-case-file/api-build-case-file-get-list-build-case-file-post-json';\nimport { apiBuildCaseFileGetListBuildCaseFilePost$Plain } from '../fn/build-case-file/api-build-case-file-get-list-build-case-file-post-plain';\nimport { apiBuildCaseFileSaveBuildCaseFilePost$Json } from '../fn/build-case-file/api-build-case-file-save-build-case-file-post-json';\nimport { apiBuildCaseFileSaveBuildCaseFilePost$Plain } from '../fn/build-case-file/api-build-case-file-save-build-case-file-post-plain';\nimport { apiBuildCaseFileSaveMultipleBuildCaseFilePost$Json } from '../fn/build-case-file/api-build-case-file-save-multiple-build-case-file-post-json';\nimport { apiBuildCaseFileSaveMultipleBuildCaseFilePost$Plain } from '../fn/build-case-file/api-build-case-file-save-multiple-build-case-file-post-plain';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../api-configuration\";\nimport * as i2 from \"@angular/common/http\";\nexport class BuildCaseFileService extends BaseService {\n  constructor(config, http) {\n    super(config, http);\n  }\n  /** Path part for operation `apiBuildCaseFileGetListBuildCaseFilePost()` */\n  static {\n    this.ApiBuildCaseFileGetListBuildCaseFilePostPath = '/api/BuildCaseFile/GetListBuildCaseFile';\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiBuildCaseFileGetListBuildCaseFilePost$Plain()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiBuildCaseFileGetListBuildCaseFilePost$Plain$Response(params, context) {\n    return apiBuildCaseFileGetListBuildCaseFilePost$Plain(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiBuildCaseFileGetListBuildCaseFilePost$Plain$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiBuildCaseFileGetListBuildCaseFilePost$Plain(params, context) {\n    return this.apiBuildCaseFileGetListBuildCaseFilePost$Plain$Response(params, context).pipe(map(r => r.body));\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiBuildCaseFileGetListBuildCaseFilePost$Json()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiBuildCaseFileGetListBuildCaseFilePost$Json$Response(params, context) {\n    return apiBuildCaseFileGetListBuildCaseFilePost$Json(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiBuildCaseFileGetListBuildCaseFilePost$Json$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiBuildCaseFileGetListBuildCaseFilePost$Json(params, context) {\n    return this.apiBuildCaseFileGetListBuildCaseFilePost$Json$Response(params, context).pipe(map(r => r.body));\n  }\n  /** Path part for operation `apiBuildCaseFileGetBuildCaseFileByIdPost()` */\n  static {\n    this.ApiBuildCaseFileGetBuildCaseFileByIdPostPath = '/api/BuildCaseFile/GetBuildCaseFileByID';\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiBuildCaseFileGetBuildCaseFileByIdPost$Plain()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiBuildCaseFileGetBuildCaseFileByIdPost$Plain$Response(params, context) {\n    return apiBuildCaseFileGetBuildCaseFileByIdPost$Plain(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiBuildCaseFileGetBuildCaseFileByIdPost$Plain$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiBuildCaseFileGetBuildCaseFileByIdPost$Plain(params, context) {\n    return this.apiBuildCaseFileGetBuildCaseFileByIdPost$Plain$Response(params, context).pipe(map(r => r.body));\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiBuildCaseFileGetBuildCaseFileByIdPost$Json()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiBuildCaseFileGetBuildCaseFileByIdPost$Json$Response(params, context) {\n    return apiBuildCaseFileGetBuildCaseFileByIdPost$Json(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiBuildCaseFileGetBuildCaseFileByIdPost$Json$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiBuildCaseFileGetBuildCaseFileByIdPost$Json(params, context) {\n    return this.apiBuildCaseFileGetBuildCaseFileByIdPost$Json$Response(params, context).pipe(map(r => r.body));\n  }\n  /** Path part for operation `apiBuildCaseFileDeleteBuildCaseFilePost()` */\n  static {\n    this.ApiBuildCaseFileDeleteBuildCaseFilePostPath = '/api/BuildCaseFile/DeleteBuildCaseFile';\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiBuildCaseFileDeleteBuildCaseFilePost$Plain()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiBuildCaseFileDeleteBuildCaseFilePost$Plain$Response(params, context) {\n    return apiBuildCaseFileDeleteBuildCaseFilePost$Plain(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiBuildCaseFileDeleteBuildCaseFilePost$Plain$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiBuildCaseFileDeleteBuildCaseFilePost$Plain(params, context) {\n    return this.apiBuildCaseFileDeleteBuildCaseFilePost$Plain$Response(params, context).pipe(map(r => r.body));\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiBuildCaseFileDeleteBuildCaseFilePost$Json()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiBuildCaseFileDeleteBuildCaseFilePost$Json$Response(params, context) {\n    return apiBuildCaseFileDeleteBuildCaseFilePost$Json(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiBuildCaseFileDeleteBuildCaseFilePost$Json$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiBuildCaseFileDeleteBuildCaseFilePost$Json(params, context) {\n    return this.apiBuildCaseFileDeleteBuildCaseFilePost$Json$Response(params, context).pipe(map(r => r.body));\n  }\n  /** Path part for operation `apiBuildCaseFileSaveBuildCaseFilePost()` */\n  static {\n    this.ApiBuildCaseFileSaveBuildCaseFilePostPath = '/api/BuildCaseFile/SaveBuildCaseFile';\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiBuildCaseFileSaveBuildCaseFilePost$Plain()` instead.\n   *\n   * This method sends `multipart/form-data` and handles request body of type `multipart/form-data`.\n   */\n  apiBuildCaseFileSaveBuildCaseFilePost$Plain$Response(params, context) {\n    return apiBuildCaseFileSaveBuildCaseFilePost$Plain(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiBuildCaseFileSaveBuildCaseFilePost$Plain$Response()` instead.\n   *\n   * This method sends `multipart/form-data` and handles request body of type `multipart/form-data`.\n   */\n  apiBuildCaseFileSaveBuildCaseFilePost$Plain(params, context) {\n    return this.apiBuildCaseFileSaveBuildCaseFilePost$Plain$Response(params, context).pipe(map(r => r.body));\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiBuildCaseFileSaveBuildCaseFilePost$Json()` instead.\n   *\n   * This method sends `multipart/form-data` and handles request body of type `multipart/form-data`.\n   */\n  apiBuildCaseFileSaveBuildCaseFilePost$Json$Response(params, context) {\n    return apiBuildCaseFileSaveBuildCaseFilePost$Json(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiBuildCaseFileSaveBuildCaseFilePost$Json$Response()` instead.\n   *\n   * This method sends `multipart/form-data` and handles request body of type `multipart/form-data`.\n   */\n  apiBuildCaseFileSaveBuildCaseFilePost$Json(params, context) {\n    return this.apiBuildCaseFileSaveBuildCaseFilePost$Json$Response(params, context).pipe(map(r => r.body));\n  }\n  /** Path part for operation `apiBuildCaseFileSaveMultipleBuildCaseFilePost()` */\n  static {\n    this.ApiBuildCaseFileSaveMultipleBuildCaseFilePostPath = '/api/BuildCaseFile/SaveMultipleBuildCaseFile';\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiBuildCaseFileSaveMultipleBuildCaseFilePost$Plain()` instead.\n   *\n   * This method sends `multipart/form-data` and handles request body of type `multipart/form-data`.\n   */\n  apiBuildCaseFileSaveMultipleBuildCaseFilePost$Plain$Response(params, context) {\n    return apiBuildCaseFileSaveMultipleBuildCaseFilePost$Plain(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiBuildCaseFileSaveMultipleBuildCaseFilePost$Plain$Response()` instead.\n   *\n   * This method sends `multipart/form-data` and handles request body of type `multipart/form-data`.\n   */\n  apiBuildCaseFileSaveMultipleBuildCaseFilePost$Plain(params, context) {\n    return this.apiBuildCaseFileSaveMultipleBuildCaseFilePost$Plain$Response(params, context).pipe(map(r => r.body));\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiBuildCaseFileSaveMultipleBuildCaseFilePost$Json()` instead.\n   *\n   * This method sends `multipart/form-data` and handles request body of type `multipart/form-data`.\n   */\n  apiBuildCaseFileSaveMultipleBuildCaseFilePost$Json$Response(params, context) {\n    return apiBuildCaseFileSaveMultipleBuildCaseFilePost$Json(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiBuildCaseFileSaveMultipleBuildCaseFilePost$Json$Response()` instead.\n   *\n   * This method sends `multipart/form-data` and handles request body of type `multipart/form-data`.\n   */\n  apiBuildCaseFileSaveMultipleBuildCaseFilePost$Json(params, context) {\n    return this.apiBuildCaseFileSaveMultipleBuildCaseFilePost$Json$Response(params, context).pipe(map(r => r.body));\n  }\n  static {\n    this.ɵfac = function BuildCaseFileService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || BuildCaseFileService)(i0.ɵɵinject(i1.ApiConfiguration), i0.ɵɵinject(i2.HttpClient));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: BuildCaseFileService,\n      factory: BuildCaseFileService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["map", "BaseService", "apiBuildCaseFileDeleteBuildCaseFilePost$Json", "apiBuildCaseFileDeleteBuildCaseFilePost$Plain", "apiBuildCaseFileGetBuildCaseFileByIdPost$Json", "apiBuildCaseFileGetBuildCaseFileByIdPost$Plain", "apiBuildCaseFileGetListBuildCaseFilePost$Json", "apiBuildCaseFileGetListBuildCaseFilePost$Plain", "apiBuildCaseFileSaveBuildCaseFilePost$Json", "apiBuildCaseFileSaveBuildCaseFilePost$Plain", "apiBuildCaseFileSaveMultipleBuildCaseFilePost$Json", "apiBuildCaseFileSaveMultipleBuildCaseFilePost$Plain", "BuildCaseFileService", "constructor", "config", "http", "ApiBuildCaseFileGetListBuildCaseFilePostPath", "apiBuildCaseFileGetListBuildCaseFilePost$Plain$Response", "params", "context", "rootUrl", "pipe", "r", "body", "apiBuildCaseFileGetListBuildCaseFilePost$Json$Response", "ApiBuildCaseFileGetBuildCaseFileByIdPostPath", "apiBuildCaseFileGetBuildCaseFileByIdPost$Plain$Response", "apiBuildCaseFileGetBuildCaseFileByIdPost$Json$Response", "ApiBuildCaseFileDeleteBuildCaseFilePostPath", "apiBuildCaseFileDeleteBuildCaseFilePost$Plain$Response", "apiBuildCaseFileDeleteBuildCaseFilePost$Json$Response", "ApiBuildCaseFileSaveBuildCaseFilePostPath", "apiBuildCaseFileSaveBuildCaseFilePost$Plain$Response", "apiBuildCaseFileSaveBuildCaseFilePost$Json$Response", "ApiBuildCaseFileSaveMultipleBuildCaseFilePostPath", "apiBuildCaseFileSaveMultipleBuildCaseFilePost$Plain$Response", "apiBuildCaseFileSaveMultipleBuildCaseFilePost$Json$Response", "i0", "ɵɵinject", "i1", "ApiConfiguration", "i2", "HttpClient", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\services\\api\\services\\build-case-file.service.ts"], "sourcesContent": ["/* tslint:disable */\r\n/* eslint-disable */\r\n/* Code generated by ng-openapi-gen DO NOT EDIT. */\r\n\r\nimport { HttpClient, HttpContext } from '@angular/common/http';\r\nimport { Injectable } from '@angular/core';\r\nimport { Observable } from 'rxjs';\r\nimport { map } from 'rxjs/operators';\r\n\r\nimport { BaseService } from '../base-service';\r\nimport { ApiConfiguration } from '../api-configuration';\r\nimport { StrictHttpResponse } from '../strict-http-response';\r\n\r\nimport { apiBuildCaseFileDeleteBuildCaseFilePost$Json } from '../fn/build-case-file/api-build-case-file-delete-build-case-file-post-json';\r\nimport { ApiBuildCaseFileDeleteBuildCaseFilePost$Json$Params } from '../fn/build-case-file/api-build-case-file-delete-build-case-file-post-json';\r\nimport { apiBuildCaseFileDeleteBuildCaseFilePost$Plain } from '../fn/build-case-file/api-build-case-file-delete-build-case-file-post-plain';\r\nimport { ApiBuildCaseFileDeleteBuildCaseFilePost$Plain$Params } from '../fn/build-case-file/api-build-case-file-delete-build-case-file-post-plain';\r\nimport { apiBuildCaseFileGetBuildCaseFileByIdPost$Json } from '../fn/build-case-file/api-build-case-file-get-build-case-file-by-id-post-json';\r\nimport { ApiBuildCaseFileGetBuildCaseFileByIdPost$Json$Params } from '../fn/build-case-file/api-build-case-file-get-build-case-file-by-id-post-json';\r\nimport { apiBuildCaseFileGetBuildCaseFileByIdPost$Plain } from '../fn/build-case-file/api-build-case-file-get-build-case-file-by-id-post-plain';\r\nimport { ApiBuildCaseFileGetBuildCaseFileByIdPost$Plain$Params } from '../fn/build-case-file/api-build-case-file-get-build-case-file-by-id-post-plain';\r\nimport { apiBuildCaseFileGetListBuildCaseFilePost$Json } from '../fn/build-case-file/api-build-case-file-get-list-build-case-file-post-json';\r\nimport { ApiBuildCaseFileGetListBuildCaseFilePost$Json$Params } from '../fn/build-case-file/api-build-case-file-get-list-build-case-file-post-json';\r\nimport { apiBuildCaseFileGetListBuildCaseFilePost$Plain } from '../fn/build-case-file/api-build-case-file-get-list-build-case-file-post-plain';\r\nimport { ApiBuildCaseFileGetListBuildCaseFilePost$Plain$Params } from '../fn/build-case-file/api-build-case-file-get-list-build-case-file-post-plain';\r\nimport { apiBuildCaseFileSaveBuildCaseFilePost$Json } from '../fn/build-case-file/api-build-case-file-save-build-case-file-post-json';\r\nimport { ApiBuildCaseFileSaveBuildCaseFilePost$Json$Params } from '../fn/build-case-file/api-build-case-file-save-build-case-file-post-json';\r\nimport { apiBuildCaseFileSaveBuildCaseFilePost$Plain } from '../fn/build-case-file/api-build-case-file-save-build-case-file-post-plain';\r\nimport { ApiBuildCaseFileSaveBuildCaseFilePost$Plain$Params } from '../fn/build-case-file/api-build-case-file-save-build-case-file-post-plain';\r\nimport { apiBuildCaseFileSaveMultipleBuildCaseFilePost$Json } from '../fn/build-case-file/api-build-case-file-save-multiple-build-case-file-post-json';\r\nimport { ApiBuildCaseFileSaveMultipleBuildCaseFilePost$Json$Params } from '../fn/build-case-file/api-build-case-file-save-multiple-build-case-file-post-json';\r\nimport { apiBuildCaseFileSaveMultipleBuildCaseFilePost$Plain } from '../fn/build-case-file/api-build-case-file-save-multiple-build-case-file-post-plain';\r\nimport { ApiBuildCaseFileSaveMultipleBuildCaseFilePost$Plain$Params } from '../fn/build-case-file/api-build-case-file-save-multiple-build-case-file-post-plain';\r\nimport { BuildCaseFileResListResponseBase } from '../models/build-case-file-res-list-response-base';\r\nimport { BuildCaseFileResResponseBase } from '../models/build-case-file-res-response-base';\r\nimport { StringResponseBase } from '../models/string-response-base';\r\n\r\n@Injectable({ providedIn: 'root' })\r\nexport class BuildCaseFileService extends BaseService {\r\n  constructor(config: ApiConfiguration, http: HttpClient) {\r\n    super(config, http);\r\n  }\r\n\r\n  /** Path part for operation `apiBuildCaseFileGetListBuildCaseFilePost()` */\r\n  static readonly ApiBuildCaseFileGetListBuildCaseFilePostPath = '/api/BuildCaseFile/GetListBuildCaseFile';\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiBuildCaseFileGetListBuildCaseFilePost$Plain()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiBuildCaseFileGetListBuildCaseFilePost$Plain$Response(params?: ApiBuildCaseFileGetListBuildCaseFilePost$Plain$Params, context?: HttpContext): Observable<StrictHttpResponse<BuildCaseFileResListResponseBase>> {\r\n    return apiBuildCaseFileGetListBuildCaseFilePost$Plain(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiBuildCaseFileGetListBuildCaseFilePost$Plain$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiBuildCaseFileGetListBuildCaseFilePost$Plain(params?: ApiBuildCaseFileGetListBuildCaseFilePost$Plain$Params, context?: HttpContext): Observable<BuildCaseFileResListResponseBase> {\r\n    return this.apiBuildCaseFileGetListBuildCaseFilePost$Plain$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<BuildCaseFileResListResponseBase>): BuildCaseFileResListResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiBuildCaseFileGetListBuildCaseFilePost$Json()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiBuildCaseFileGetListBuildCaseFilePost$Json$Response(params?: ApiBuildCaseFileGetListBuildCaseFilePost$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<BuildCaseFileResListResponseBase>> {\r\n    return apiBuildCaseFileGetListBuildCaseFilePost$Json(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiBuildCaseFileGetListBuildCaseFilePost$Json$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiBuildCaseFileGetListBuildCaseFilePost$Json(params?: ApiBuildCaseFileGetListBuildCaseFilePost$Json$Params, context?: HttpContext): Observable<BuildCaseFileResListResponseBase> {\r\n    return this.apiBuildCaseFileGetListBuildCaseFilePost$Json$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<BuildCaseFileResListResponseBase>): BuildCaseFileResListResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /** Path part for operation `apiBuildCaseFileGetBuildCaseFileByIdPost()` */\r\n  static readonly ApiBuildCaseFileGetBuildCaseFileByIdPostPath = '/api/BuildCaseFile/GetBuildCaseFileByID';\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiBuildCaseFileGetBuildCaseFileByIdPost$Plain()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiBuildCaseFileGetBuildCaseFileByIdPost$Plain$Response(params?: ApiBuildCaseFileGetBuildCaseFileByIdPost$Plain$Params, context?: HttpContext): Observable<StrictHttpResponse<BuildCaseFileResResponseBase>> {\r\n    return apiBuildCaseFileGetBuildCaseFileByIdPost$Plain(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiBuildCaseFileGetBuildCaseFileByIdPost$Plain$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiBuildCaseFileGetBuildCaseFileByIdPost$Plain(params?: ApiBuildCaseFileGetBuildCaseFileByIdPost$Plain$Params, context?: HttpContext): Observable<BuildCaseFileResResponseBase> {\r\n    return this.apiBuildCaseFileGetBuildCaseFileByIdPost$Plain$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<BuildCaseFileResResponseBase>): BuildCaseFileResResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiBuildCaseFileGetBuildCaseFileByIdPost$Json()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiBuildCaseFileGetBuildCaseFileByIdPost$Json$Response(params?: ApiBuildCaseFileGetBuildCaseFileByIdPost$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<BuildCaseFileResResponseBase>> {\r\n    return apiBuildCaseFileGetBuildCaseFileByIdPost$Json(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiBuildCaseFileGetBuildCaseFileByIdPost$Json$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiBuildCaseFileGetBuildCaseFileByIdPost$Json(params?: ApiBuildCaseFileGetBuildCaseFileByIdPost$Json$Params, context?: HttpContext): Observable<BuildCaseFileResResponseBase> {\r\n    return this.apiBuildCaseFileGetBuildCaseFileByIdPost$Json$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<BuildCaseFileResResponseBase>): BuildCaseFileResResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /** Path part for operation `apiBuildCaseFileDeleteBuildCaseFilePost()` */\r\n  static readonly ApiBuildCaseFileDeleteBuildCaseFilePostPath = '/api/BuildCaseFile/DeleteBuildCaseFile';\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiBuildCaseFileDeleteBuildCaseFilePost$Plain()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiBuildCaseFileDeleteBuildCaseFilePost$Plain$Response(params?: ApiBuildCaseFileDeleteBuildCaseFilePost$Plain$Params, context?: HttpContext): Observable<StrictHttpResponse<StringResponseBase>> {\r\n    return apiBuildCaseFileDeleteBuildCaseFilePost$Plain(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiBuildCaseFileDeleteBuildCaseFilePost$Plain$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiBuildCaseFileDeleteBuildCaseFilePost$Plain(params?: ApiBuildCaseFileDeleteBuildCaseFilePost$Plain$Params, context?: HttpContext): Observable<StringResponseBase> {\r\n    return this.apiBuildCaseFileDeleteBuildCaseFilePost$Plain$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<StringResponseBase>): StringResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiBuildCaseFileDeleteBuildCaseFilePost$Json()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiBuildCaseFileDeleteBuildCaseFilePost$Json$Response(params?: ApiBuildCaseFileDeleteBuildCaseFilePost$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<StringResponseBase>> {\r\n    return apiBuildCaseFileDeleteBuildCaseFilePost$Json(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiBuildCaseFileDeleteBuildCaseFilePost$Json$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiBuildCaseFileDeleteBuildCaseFilePost$Json(params?: ApiBuildCaseFileDeleteBuildCaseFilePost$Json$Params, context?: HttpContext): Observable<StringResponseBase> {\r\n    return this.apiBuildCaseFileDeleteBuildCaseFilePost$Json$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<StringResponseBase>): StringResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /** Path part for operation `apiBuildCaseFileSaveBuildCaseFilePost()` */\r\n  static readonly ApiBuildCaseFileSaveBuildCaseFilePostPath = '/api/BuildCaseFile/SaveBuildCaseFile';\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiBuildCaseFileSaveBuildCaseFilePost$Plain()` instead.\r\n   *\r\n   * This method sends `multipart/form-data` and handles request body of type `multipart/form-data`.\r\n   */\r\n  apiBuildCaseFileSaveBuildCaseFilePost$Plain$Response(params?: ApiBuildCaseFileSaveBuildCaseFilePost$Plain$Params, context?: HttpContext): Observable<StrictHttpResponse<StringResponseBase>> {\r\n    return apiBuildCaseFileSaveBuildCaseFilePost$Plain(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiBuildCaseFileSaveBuildCaseFilePost$Plain$Response()` instead.\r\n   *\r\n   * This method sends `multipart/form-data` and handles request body of type `multipart/form-data`.\r\n   */\r\n  apiBuildCaseFileSaveBuildCaseFilePost$Plain(params?: ApiBuildCaseFileSaveBuildCaseFilePost$Plain$Params, context?: HttpContext): Observable<StringResponseBase> {\r\n    return this.apiBuildCaseFileSaveBuildCaseFilePost$Plain$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<StringResponseBase>): StringResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiBuildCaseFileSaveBuildCaseFilePost$Json()` instead.\r\n   *\r\n   * This method sends `multipart/form-data` and handles request body of type `multipart/form-data`.\r\n   */\r\n  apiBuildCaseFileSaveBuildCaseFilePost$Json$Response(params?: ApiBuildCaseFileSaveBuildCaseFilePost$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<StringResponseBase>> {\r\n    return apiBuildCaseFileSaveBuildCaseFilePost$Json(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiBuildCaseFileSaveBuildCaseFilePost$Json$Response()` instead.\r\n   *\r\n   * This method sends `multipart/form-data` and handles request body of type `multipart/form-data`.\r\n   */\r\n  apiBuildCaseFileSaveBuildCaseFilePost$Json(params?: ApiBuildCaseFileSaveBuildCaseFilePost$Json$Params, context?: HttpContext): Observable<StringResponseBase> {\r\n    return this.apiBuildCaseFileSaveBuildCaseFilePost$Json$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<StringResponseBase>): StringResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /** Path part for operation `apiBuildCaseFileSaveMultipleBuildCaseFilePost()` */\r\n  static readonly ApiBuildCaseFileSaveMultipleBuildCaseFilePostPath = '/api/BuildCaseFile/SaveMultipleBuildCaseFile';\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiBuildCaseFileSaveMultipleBuildCaseFilePost$Plain()` instead.\r\n   *\r\n   * This method sends `multipart/form-data` and handles request body of type `multipart/form-data`.\r\n   */\r\n  apiBuildCaseFileSaveMultipleBuildCaseFilePost$Plain$Response(params?: ApiBuildCaseFileSaveMultipleBuildCaseFilePost$Plain$Params, context?: HttpContext): Observable<StrictHttpResponse<StringResponseBase>> {\r\n    return apiBuildCaseFileSaveMultipleBuildCaseFilePost$Plain(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiBuildCaseFileSaveMultipleBuildCaseFilePost$Plain$Response()` instead.\r\n   *\r\n   * This method sends `multipart/form-data` and handles request body of type `multipart/form-data`.\r\n   */\r\n  apiBuildCaseFileSaveMultipleBuildCaseFilePost$Plain(params?: ApiBuildCaseFileSaveMultipleBuildCaseFilePost$Plain$Params, context?: HttpContext): Observable<StringResponseBase> {\r\n    return this.apiBuildCaseFileSaveMultipleBuildCaseFilePost$Plain$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<StringResponseBase>): StringResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiBuildCaseFileSaveMultipleBuildCaseFilePost$Json()` instead.\r\n   *\r\n   * This method sends `multipart/form-data` and handles request body of type `multipart/form-data`.\r\n   */\r\n  apiBuildCaseFileSaveMultipleBuildCaseFilePost$Json$Response(params?: ApiBuildCaseFileSaveMultipleBuildCaseFilePost$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<StringResponseBase>> {\r\n    return apiBuildCaseFileSaveMultipleBuildCaseFilePost$Json(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiBuildCaseFileSaveMultipleBuildCaseFilePost$Json$Response()` instead.\r\n   *\r\n   * This method sends `multipart/form-data` and handles request body of type `multipart/form-data`.\r\n   */\r\n  apiBuildCaseFileSaveMultipleBuildCaseFilePost$Json(params?: ApiBuildCaseFileSaveMultipleBuildCaseFilePost$Json$Params, context?: HttpContext): Observable<StringResponseBase> {\r\n    return this.apiBuildCaseFileSaveMultipleBuildCaseFilePost$Json$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<StringResponseBase>): StringResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n}\r\n"], "mappings": "AAOA,SAASA,GAAG,QAAQ,gBAAgB;AAEpC,SAASC,WAAW,QAAQ,iBAAiB;AAI7C,SAASC,4CAA4C,QAAQ,4EAA4E;AAEzI,SAASC,6CAA6C,QAAQ,6EAA6E;AAE3I,SAASC,6CAA6C,QAAQ,+EAA+E;AAE7I,SAASC,8CAA8C,QAAQ,gFAAgF;AAE/I,SAASC,6CAA6C,QAAQ,8EAA8E;AAE5I,SAASC,8CAA8C,QAAQ,+EAA+E;AAE9I,SAASC,0CAA0C,QAAQ,0EAA0E;AAErI,SAASC,2CAA2C,QAAQ,2EAA2E;AAEvI,SAASC,kDAAkD,QAAQ,mFAAmF;AAEtJ,SAASC,mDAAmD,QAAQ,oFAAoF;;;;AAOxJ,OAAM,MAAOC,oBAAqB,SAAQX,WAAW;EACnDY,YAAYC,MAAwB,EAAEC,IAAgB;IACpD,KAAK,CAACD,MAAM,EAAEC,IAAI,CAAC;EACrB;EAEA;;IACgB,KAAAC,4CAA4C,GAAG,yCAAyC;EAAC;EAEzG;;;;;;EAMAC,uDAAuDA,CAACC,MAA8D,EAAEC,OAAqB;IAC3I,OAAOZ,8CAA8C,CAAC,IAAI,CAACQ,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EACjG;EAEA;;;;;;EAMAZ,8CAA8CA,CAACW,MAA8D,EAAEC,OAAqB;IAClI,OAAO,IAAI,CAACF,uDAAuD,CAACC,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CACvFrB,GAAG,CAAEsB,CAAuD,IAAuCA,CAAC,CAACC,IAAI,CAAC,CAC3G;EACH;EAEA;;;;;;EAMAC,sDAAsDA,CAACN,MAA6D,EAAEC,OAAqB;IACzI,OAAOb,6CAA6C,CAAC,IAAI,CAACS,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EAChG;EAEA;;;;;;EAMAb,6CAA6CA,CAACY,MAA6D,EAAEC,OAAqB;IAChI,OAAO,IAAI,CAACK,sDAAsD,CAACN,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CACtFrB,GAAG,CAAEsB,CAAuD,IAAuCA,CAAC,CAACC,IAAI,CAAC,CAC3G;EACH;EAEA;;IACgB,KAAAE,4CAA4C,GAAG,yCAAyC;EAAC;EAEzG;;;;;;EAMAC,uDAAuDA,CAACR,MAA8D,EAAEC,OAAqB;IAC3I,OAAOd,8CAA8C,CAAC,IAAI,CAACU,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EACjG;EAEA;;;;;;EAMAd,8CAA8CA,CAACa,MAA8D,EAAEC,OAAqB;IAClI,OAAO,IAAI,CAACO,uDAAuD,CAACR,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CACvFrB,GAAG,CAAEsB,CAAmD,IAAmCA,CAAC,CAACC,IAAI,CAAC,CACnG;EACH;EAEA;;;;;;EAMAI,sDAAsDA,CAACT,MAA6D,EAAEC,OAAqB;IACzI,OAAOf,6CAA6C,CAAC,IAAI,CAACW,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EAChG;EAEA;;;;;;EAMAf,6CAA6CA,CAACc,MAA6D,EAAEC,OAAqB;IAChI,OAAO,IAAI,CAACQ,sDAAsD,CAACT,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CACtFrB,GAAG,CAAEsB,CAAmD,IAAmCA,CAAC,CAACC,IAAI,CAAC,CACnG;EACH;EAEA;;IACgB,KAAAK,2CAA2C,GAAG,wCAAwC;EAAC;EAEvG;;;;;;EAMAC,sDAAsDA,CAACX,MAA6D,EAAEC,OAAqB;IACzI,OAAOhB,6CAA6C,CAAC,IAAI,CAACY,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EAChG;EAEA;;;;;;EAMAhB,6CAA6CA,CAACe,MAA6D,EAAEC,OAAqB;IAChI,OAAO,IAAI,CAACU,sDAAsD,CAACX,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CACtFrB,GAAG,CAAEsB,CAAyC,IAAyBA,CAAC,CAACC,IAAI,CAAC,CAC/E;EACH;EAEA;;;;;;EAMAO,qDAAqDA,CAACZ,MAA4D,EAAEC,OAAqB;IACvI,OAAOjB,4CAA4C,CAAC,IAAI,CAACa,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EAC/F;EAEA;;;;;;EAMAjB,4CAA4CA,CAACgB,MAA4D,EAAEC,OAAqB;IAC9H,OAAO,IAAI,CAACW,qDAAqD,CAACZ,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CACrFrB,GAAG,CAAEsB,CAAyC,IAAyBA,CAAC,CAACC,IAAI,CAAC,CAC/E;EACH;EAEA;;IACgB,KAAAQ,yCAAyC,GAAG,sCAAsC;EAAC;EAEnG;;;;;;EAMAC,oDAAoDA,CAACd,MAA2D,EAAEC,OAAqB;IACrI,OAAOV,2CAA2C,CAAC,IAAI,CAACM,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EAC9F;EAEA;;;;;;EAMAV,2CAA2CA,CAACS,MAA2D,EAAEC,OAAqB;IAC5H,OAAO,IAAI,CAACa,oDAAoD,CAACd,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CACpFrB,GAAG,CAAEsB,CAAyC,IAAyBA,CAAC,CAACC,IAAI,CAAC,CAC/E;EACH;EAEA;;;;;;EAMAU,mDAAmDA,CAACf,MAA0D,EAAEC,OAAqB;IACnI,OAAOX,0CAA0C,CAAC,IAAI,CAACO,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EAC7F;EAEA;;;;;;EAMAX,0CAA0CA,CAACU,MAA0D,EAAEC,OAAqB;IAC1H,OAAO,IAAI,CAACc,mDAAmD,CAACf,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CACnFrB,GAAG,CAAEsB,CAAyC,IAAyBA,CAAC,CAACC,IAAI,CAAC,CAC/E;EACH;EAEA;;IACgB,KAAAW,iDAAiD,GAAG,8CAA8C;EAAC;EAEnH;;;;;;EAMAC,4DAA4DA,CAACjB,MAAmE,EAAEC,OAAqB;IACrJ,OAAOR,mDAAmD,CAAC,IAAI,CAACI,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EACtG;EAEA;;;;;;EAMAR,mDAAmDA,CAACO,MAAmE,EAAEC,OAAqB;IAC5I,OAAO,IAAI,CAACgB,4DAA4D,CAACjB,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CAC5FrB,GAAG,CAAEsB,CAAyC,IAAyBA,CAAC,CAACC,IAAI,CAAC,CAC/E;EACH;EAEA;;;;;;EAMAa,2DAA2DA,CAAClB,MAAkE,EAAEC,OAAqB;IACnJ,OAAOT,kDAAkD,CAAC,IAAI,CAACK,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EACrG;EAEA;;;;;;EAMAT,kDAAkDA,CAACQ,MAAkE,EAAEC,OAAqB;IAC1I,OAAO,IAAI,CAACiB,2DAA2D,CAAClB,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CAC3FrB,GAAG,CAAEsB,CAAyC,IAAyBA,CAAC,CAACC,IAAI,CAAC,CAC/E;EACH;;;uCA9OWX,oBAAoB,EAAAyB,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,gBAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,UAAA;IAAA;EAAA;;;aAApB9B,oBAAoB;MAAA+B,OAAA,EAApB/B,oBAAoB,CAAAgC,IAAA;MAAAC,UAAA,EADP;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}