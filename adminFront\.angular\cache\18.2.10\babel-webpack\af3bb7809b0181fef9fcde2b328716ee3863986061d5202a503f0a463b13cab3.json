{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { tap } from 'rxjs';\nimport { SharedModule } from '../../components/shared.module';\nimport { BaseComponent } from '../../components/base/baseComponent';\nimport { EEvent } from 'src/app/shared/services/event.service';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/shared/helper/allowHelper\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"src/app/shared/services/message.service\";\nimport * as i4 from \"src/services/api/services\";\nimport * as i5 from \"src/app/shared/services/event.service\";\nimport * as i6 from \"@angular/common\";\nimport * as i7 from \"@angular/forms\";\nimport * as i8 from \"@nebular/theme\";\nimport * as i9 from \"@ng-bootstrap/ng-bootstrap\";\nimport * as i10 from \"../../components/breadcrumb/breadcrumb.component\";\nfunction ContentManagementLandownerComponent_nb_option_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 19);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const case_r1 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", case_r1);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", case_r1.CBuildCaseName, \" \");\n  }\n}\nfunction ContentManagementLandownerComponent_button_15_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 20);\n    i0.ɵɵlistener(\"click\", function ContentManagementLandownerComponent_button_15_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.unLock());\n    });\n    i0.ɵɵtext(1, \" \\u89E3\\u9664 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ContentManagementLandownerComponent_button_16_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 21);\n    i0.ɵɵlistener(\"click\", function ContentManagementLandownerComponent_button_16_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onLock());\n    });\n    i0.ɵɵtext(1, \" \\u9396\\u5B9A \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ContentManagementLandownerComponent_button_17_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 22);\n    i0.ɵɵlistener(\"click\", function ContentManagementLandownerComponent_button_17_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.navidateDetai());\n    });\n    i0.ɵɵtext(1, \" \\u7DE8\\u8F2F\\u5167\\u5BB9 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ContentManagementLandownerComponent_button_18_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 22);\n    i0.ɵɵlistener(\"click\", function ContentManagementLandownerComponent_button_18_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.navidateDetai());\n    });\n    i0.ɵɵtext(1, \" \\u65B0\\u589E \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ContentManagementLandownerComponent_tr_28_span_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const i_r7 = ctx.$implicit;\n    const ix_r8 = ctx.index;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate2(\" \", ix_r8 > 0 ? \"\\u3001\" : \"\", \" \", i_r7.CHousehold, \" \");\n  }\n}\nfunction ContentManagementLandownerComponent_tr_28_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\");\n    i0.ɵɵtemplate(4, ContentManagementLandownerComponent_tr_28_span_4_Template, 2, 2, \"span\", 16);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const item_r9 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r9.CItemName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", item_r9.tblFormItemHouseholds);\n  }\n}\nexport class ContentManagementLandownerComponent extends BaseComponent {\n  constructor(_allow, router, message, _buildCaseService, _formItemService, _eventService) {\n    super(_allow);\n    this._allow = _allow;\n    this.router = router;\n    this.message = message;\n    this._buildCaseService = _buildCaseService;\n    this._formItemService = _formItemService;\n    this._eventService = _eventService;\n    this.tempBuildCaseID = -1;\n    this.buildingSelectedOptions = [{\n      value: '',\n      label: '全部'\n    }];\n    this.pageSize = 20;\n    this.typeContentManagementLandowner = {\n      CFormType: 1,\n      CNoticeType: 1\n    };\n    this._eventService.receive().pipe(tap(res => {\n      if (res.action == \"GET_BUILDCASE\" /* EEvent.GET_BUILDCASE */ && !!res.payload) {\n        this.tempBuildCaseID = res.payload;\n      }\n    })).subscribe();\n  }\n  navidateDetai() {\n    this.router.navigate([`pages/content-management-landowner/${this.cBuildCaseSelected.cID}`]);\n  }\n  getListFormItem() {\n    this._formItemService.apiFormItemGetListFormItemPost$Json({\n      body: {\n        CBuildCaseId: this.cBuildCaseSelected.cID,\n        CFormType: this.typeContentManagementLandowner.CFormType,\n        PageIndex: this.pageIndex,\n        PageSize: this.pageSize,\n        CIsPaging: true\n      }\n    }).pipe(tap(res => {\n      if (res.Entries && res.StatusCode == 0) {\n        this.formItems = res.Entries.formItems;\n        this.listFormItem = res.Entries;\n        this.totalRecords = res.TotalItems ? res.TotalItems : 0;\n      }\n    })).subscribe();\n  }\n  ngOnInit() {\n    this.cBuildCaseSelected = null;\n    this.getUserBuildCase();\n  }\n  onSelectionChangeBuildCase() {\n    this.getListFormItem();\n  }\n  getUserBuildCase() {\n    this._buildCaseService.apiBuildCaseGetUserBuildCasePost$Json({\n      body: {}\n    }).pipe(tap(res => {\n      // if (res.Entries && res.StatusCode == 0) {\n      //   this.userBuildCaseOptions = res.Entries.map(res => {\n      //     return {\n      //       CBuildCaseName: res.CBuildCaseName,\n      //       cID: res.cID\n      //     }\n      //   })\n      //   this.cBuildCaseSelected = this.userBuildCaseOptions[0]\n      //   if (this.cBuildCaseSelected.cID) {\n      //     this.getListFormItem()\n      //   }\n      // }\n      if (res.Entries && res.StatusCode == 0) {\n        this.userBuildCaseOptions = res.Entries.map(res => {\n          return {\n            CBuildCaseName: res.CBuildCaseName,\n            cID: res.cID\n          };\n        });\n        if (this.tempBuildCaseID != -1) {\n          let index = this.userBuildCaseOptions.findIndex(x => x.cID == this.tempBuildCaseID);\n          this.cBuildCaseSelected = this.userBuildCaseOptions[index];\n        } else {\n          this.cBuildCaseSelected = this.userBuildCaseOptions[0];\n        }\n        if (this.cBuildCaseSelected.cID) {\n          this.getListFormItem();\n        }\n      }\n    })).subscribe();\n  }\n  pageChanged(newPage) {\n    this.pageIndex = newPage;\n    this.getListFormItem();\n  }\n  onLock() {\n    this._formItemService.apiFormItemLockFormItemPost$Json({\n      body: {\n        CBuildCaseId: this.cBuildCaseSelected.cID,\n        CFormId: this.listFormItem.CFormId\n      }\n    }).subscribe(res => {\n      if (res.StatusCode == 0) {\n        this.message.showSucessMSG(\"執行成功\");\n      } else {\n        this.message.showErrorMSG(res.Message);\n      }\n      this.getListFormItem();\n    });\n  }\n  unLock() {\n    this._formItemService.apiFormItemUnlockFormItemPost$Json({\n      body: {\n        CBuildCaseID: this.cBuildCaseSelected.cID,\n        CFormId: this.listFormItem.CFormId\n      }\n    }).subscribe(res => {\n      if (res.StatusCode == 0) {\n        this.message.showSucessMSG(\"執行成功\");\n      } else {\n        this.message.showErrorMSG(res.Message);\n      }\n      this.getListFormItem();\n    });\n  }\n  static {\n    this.ɵfac = function ContentManagementLandownerComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || ContentManagementLandownerComponent)(i0.ɵɵdirectiveInject(i1.AllowHelper), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i3.MessageService), i0.ɵɵdirectiveInject(i4.BuildCaseService), i0.ɵɵdirectiveInject(i4.FormItemService), i0.ɵɵdirectiveInject(i5.EventService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ContentManagementLandownerComponent,\n      selectors: [[\"ngx-content-management-landowner\"]],\n      standalone: true,\n      features: [i0.ɵɵInheritDefinitionFeature, i0.ɵɵStandaloneFeature],\n      decls: 31,\n      vars: 10,\n      consts: [[\"accent\", \"success\"], [1, \"font-bold\", \"text-[#818181]\"], [1, \"d-flex\", \"flex-wrap\"], [1, \"col-md-6\"], [1, \"form-group\", \"d-flex\", \"align-items-center\", \"w-full\"], [\"for\", \"buildingName\", 1, \"label\", \"col-3\"], [\"placeholder\", \"\\u5EFA\\u6848\", 1, \"col-9\", 3, \"ngModelChange\", \"selectedChange\", \"ngModel\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [1, \"d-flex\", \"justify-content-end\", \"w-full\"], [\"class\", \"btn btn-warning mx-2\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"btn btn-secondary mx-2\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"btn btn-info\", 3, \"click\", 4, \"ngIf\"], [1, \"table-responsive\", \"mt-4\"], [1, \"table\", \"table-striped\", \"border\", 2, \"background-color\", \"#f3f3f3\"], [2, \"background-color\", \"#27ae60\", \"color\", \"white\"], [\"scope\", \"col\", 1, \"col-1\"], [4, \"ngFor\", \"ngForOf\"], [1, \"d-flex\", \"justify-content-center\"], [\"aria-label\", \"Pagination\", 3, \"pageChange\", \"page\", \"pageSize\", \"collectionSize\"], [3, \"value\"], [1, \"btn\", \"btn-warning\", \"mx-2\", 3, \"click\"], [1, \"btn\", \"btn-secondary\", \"mx-2\", 3, \"click\"], [1, \"btn\", \"btn-info\", 3, \"click\"]],\n      template: function ContentManagementLandownerComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"nb-card\", 0)(1, \"nb-card-header\");\n          i0.ɵɵelement(2, \"ngx-breadcrumb\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(3, \"nb-card-body\")(4, \"h1\", 1);\n          i0.ɵɵtext(5, \" \\u60A8\\u53EF\\u5C07\\u65BC\\u5EFA\\u6750\\u7BA1\\u7406\\u53CA\\u65B9\\u6848\\u7BA1\\u7406\\u8A2D\\u5B9A\\u597D\\u7684\\u65B9\\u6848\\u53CA\\u6750\\u6599\\uFF0C\\u65BC\\u6B64\\u7D44\\u5408\\u6210\\u9078\\u6A23\\u5167\\u5BB9\\uFF0C\\u4E26\\u53EF\\u8A2D\\u5B9A\\u5404\\u65B9\\u6848\\u3001\\u6750\\u6599\\u53EF\\u9078\\u64C7\\u4E4B\\u6236\\u578B\\u3002 \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(6, \"div\", 2)(7, \"div\", 3)(8, \"div\", 4)(9, \"label\", 5);\n          i0.ɵɵtext(10, \"\\u5EFA\\u6848\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(11, \"nb-select\", 6);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function ContentManagementLandownerComponent_Template_nb_select_ngModelChange_11_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.cBuildCaseSelected, $event) || (ctx.cBuildCaseSelected = $event);\n            return $event;\n          });\n          i0.ɵɵlistener(\"selectedChange\", function ContentManagementLandownerComponent_Template_nb_select_selectedChange_11_listener() {\n            return ctx.onSelectionChangeBuildCase();\n          });\n          i0.ɵɵtemplate(12, ContentManagementLandownerComponent_nb_option_12_Template, 2, 2, \"nb-option\", 7);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(13, \"div\", 3)(14, \"div\", 8);\n          i0.ɵɵtemplate(15, ContentManagementLandownerComponent_button_15_Template, 2, 0, \"button\", 9)(16, ContentManagementLandownerComponent_button_16_Template, 2, 0, \"button\", 10)(17, ContentManagementLandownerComponent_button_17_Template, 2, 0, \"button\", 11)(18, ContentManagementLandownerComponent_button_18_Template, 2, 0, \"button\", 11);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(19, \"div\", 12)(20, \"table\", 13)(21, \"thead\")(22, \"tr\", 14)(23, \"th\", 15);\n          i0.ɵɵtext(24, \"\\u65B9\\u6848\\u540D\\u7A31/\\u5EFA\\u6750\\u4F4D\\u7F6E\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(25, \"th\", 15);\n          i0.ɵɵtext(26, \"\\u9069\\u7528\\u6236\\u5225 \");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(27, \"tbody\");\n          i0.ɵɵtemplate(28, ContentManagementLandownerComponent_tr_28_Template, 5, 2, \"tr\", 16);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(29, \"nb-card-footer\", 17)(30, \"ngb-pagination\", 18);\n          i0.ɵɵtwoWayListener(\"pageChange\", function ContentManagementLandownerComponent_Template_ngb_pagination_pageChange_30_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.pageIndex, $event) || (ctx.pageIndex = $event);\n            return $event;\n          });\n          i0.ɵɵlistener(\"pageChange\", function ContentManagementLandownerComponent_Template_ngb_pagination_pageChange_30_listener($event) {\n            return ctx.pageChanged($event);\n          });\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(11);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.cBuildCaseSelected);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngForOf\", ctx.userBuildCaseOptions);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", ctx.listFormItem && !ctx.listFormItem.CIsLock === false && ctx.isUpdate);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.listFormItem && ctx.listFormItem.CIsLock == false && ctx.isUpdate);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.listFormItem && ctx.listFormItem.formItems && ctx.isUpdate);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.listFormItem && !ctx.listFormItem.formItems && ctx.isCreate);\n          i0.ɵɵadvance(10);\n          i0.ɵɵproperty(\"ngForOf\", ctx.formItems);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtwoWayProperty(\"page\", ctx.pageIndex);\n          i0.ɵɵproperty(\"pageSize\", ctx.pageSize)(\"collectionSize\", ctx.totalRecords);\n        }\n      },\n      dependencies: [CommonModule, i6.NgForOf, i6.NgIf, SharedModule, i7.NgControlStatus, i7.NgModel, i8.NbCardComponent, i8.NbCardBodyComponent, i8.NbCardFooterComponent, i8.NbCardHeaderComponent, i8.NbSelectComponent, i8.NbOptionComponent, i9.NgbPagination, i10.BreadcrumbComponent],\n      styles: [\"/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsImZpbGUiOiJjb250ZW50LW1hbmFnZW1lbnQtbGFuZG93bmVyLmNvbXBvbmVudC5zY3NzIn0= */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvcGFnZXMvc2VsZWN0aW9uLW1hbmFnZW1lbnQvY29udGVudC1tYW5hZ2VtZW50LWxhbmRvd25lci9jb250ZW50LW1hbmFnZW1lbnQtbGFuZG93bmVyLmNvbXBvbmVudC5zY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiI7QUFDQSxnTUFBZ00iLCJzb3VyY2VSb290IjoiIn0= */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "tap", "SharedModule", "BaseComponent", "EEvent", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵproperty", "case_r1", "ɵɵadvance", "ɵɵtextInterpolate1", "CBuildCaseName", "ɵɵlistener", "ContentManagementLandownerComponent_button_15_Template_button_click_0_listener", "ɵɵrestoreView", "_r2", "ctx_r2", "ɵɵnextContext", "ɵɵresetView", "unLock", "ContentManagementLandownerComponent_button_16_Template_button_click_0_listener", "_r4", "onLock", "ContentManagementLandownerComponent_button_17_Template_button_click_0_listener", "_r5", "navid<PERSON><PERSON><PERSON><PERSON>", "ContentManagementLandownerComponent_button_18_Template_button_click_0_listener", "_r6", "ɵɵtextInterpolate2", "ix_r8", "i_r7", "CHousehold", "ɵɵtemplate", "ContentManagementLandownerComponent_tr_28_span_4_Template", "ɵɵtextInterpolate", "item_r9", "CItemName", "tblFormItemHouseholds", "ContentManagementLandownerComponent", "constructor", "_allow", "router", "message", "_buildCaseService", "_formItemService", "_eventService", "tempBuildCaseID", "buildingSelectedOptions", "value", "label", "pageSize", "typeContentManagementLandowner", "CFormType", "CNoticeType", "receive", "pipe", "res", "action", "payload", "subscribe", "navigate", "cBuildCaseSelected", "cID", "getListFormItem", "apiFormItemGetListFormItemPost$Json", "body", "CBuildCaseId", "PageIndex", "pageIndex", "PageSize", "CIsPaging", "Entries", "StatusCode", "formItems", "listFormItem", "totalRecords", "TotalItems", "ngOnInit", "getUserBuildCase", "onSelectionChangeBuildCase", "apiBuildCaseGetUserBuildCasePost$Json", "userBuildCaseOptions", "map", "index", "findIndex", "x", "pageChanged", "newPage", "apiFormItemLockFormItemPost$Json", "CFormId", "showSucessMSG", "showErrorMSG", "Message", "apiFormItemUnlockFormItemPost$Json", "CBuildCaseID", "ɵɵdirectiveInject", "i1", "AllowHelper", "i2", "Router", "i3", "MessageService", "i4", "BuildCaseService", "FormItemService", "i5", "EventService", "selectors", "standalone", "features", "ɵɵInheritDefinitionFeature", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "ContentManagementLandownerComponent_Template", "rf", "ctx", "ɵɵelement", "ɵɵtwoWayListener", "ContentManagementLandownerComponent_Template_nb_select_ngModelChange_11_listener", "$event", "ɵɵtwoWayBindingSet", "ContentManagementLandownerComponent_Template_nb_select_selected<PERSON><PERSON>e_11_listener", "ContentManagementLandownerComponent_nb_option_12_Template", "ContentManagementLandownerComponent_button_15_Template", "ContentManagementLandownerComponent_button_16_Template", "ContentManagementLandownerComponent_button_17_Template", "ContentManagementLandownerComponent_button_18_Template", "ContentManagementLandownerComponent_tr_28_Template", "ContentManagementLandownerComponent_Template_ngb_pagination_pageChange_30_listener", "ɵɵtwoWayProperty", "CIsLock", "isUpdate", "isCreate", "i6", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "i7", "NgControlStatus", "NgModel", "i8", "NbCardComponent", "NbCardBodyComponent", "NbCardFooterComponent", "NbCardHeaderComponent", "NbSelectComponent", "NbOptionComponent", "i9", "NgbPagination", "i10", "BreadcrumbComponent", "styles"], "sources": ["C:\\Users\\<USER>\\Documents\\jeansalechange\\adminFront\\src\\app\\pages\\selection-management\\content-management-landowner\\content-management-landowner.component.ts", "C:\\Users\\<USER>\\Documents\\jeansalechange\\adminFront\\src\\app\\pages\\selection-management\\content-management-landowner\\content-management-landowner.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { AllowHelper } from 'src/app/shared/helper/allowHelper';\r\nimport { BuildCaseService, FormItemService } from 'src/services/api/services';\r\nimport { Router } from '@angular/router';\r\nimport { MessageService } from 'src/app/shared/services/message.service';\r\nimport { tap } from 'rxjs';\r\nimport { GetListFormItemRes } from 'src/services/api/models';\r\nimport { SharedModule } from '../../components/shared.module';\r\nimport { BaseComponent } from '../../components/base/baseComponent';\r\nimport { EEvent, EventService, IEvent } from 'src/app/shared/services/event.service';\r\n\r\nexport interface selectItem {\r\n  label: string,\r\n  value: number,\r\n  key?: string\r\n}\r\n\r\n@Component({\r\n  selector: 'ngx-content-management-landowner',\r\n  templateUrl: './content-management-landowner.component.html',\r\n  styleUrls: ['./content-management-landowner.component.scss'],\r\n  standalone: true,\r\n  imports: [CommonModule, SharedModule],\r\n})\r\n\r\nexport class ContentManagementLandownerComponent extends BaseComponent implements OnInit {\r\n  constructor(\r\n    private _allow: AllowHelper,\r\n    private router: Router,\r\n    private message: MessageService,\r\n    private _buildCaseService: BuildCaseService,\r\n    private _formItemService: FormItemService,\r\n    private _eventService: EventService,\r\n  ) {\r\n    super(_allow)\r\n    this._eventService.receive().pipe(\r\n      tap((res: IEvent) => {\r\n        if (res.action == EEvent.GET_BUILDCASE && !!res.payload) {\r\n          this.tempBuildCaseID = res.payload\r\n        }\r\n      })\r\n    ).subscribe()\r\n  }\r\n\r\n  tempBuildCaseID: number = -1\r\n  selectedBuilding: any\r\n  buildingSelectedOptions: any[] = [{value: '', label: '全部'}]\r\n\r\n  formItems: any\r\n  listFormItem: GetListFormItemRes\r\n  override pageSize = 20\r\n\r\n  navidateDetai() {\r\n    this.router.navigate([`pages/content-management-landowner/${this.cBuildCaseSelected.cID}`])\r\n  }\r\n  typeContentManagementLandowner = {\r\n    CFormType: 1,\r\n    CNoticeType: 1\r\n  }\r\n  getListFormItem() {\r\n    this._formItemService.apiFormItemGetListFormItemPost$Json({\r\n      body: {\r\n        CBuildCaseId: this.cBuildCaseSelected.cID,\r\n        CFormType: this.typeContentManagementLandowner.CFormType,\r\n        PageIndex: this.pageIndex,\r\n        PageSize: this.pageSize,\r\n        CIsPaging: true\r\n      }\r\n    }).pipe(\r\n      tap(res => {\r\n        if (res.Entries && res.StatusCode == 0) {\r\n          this.formItems = res.Entries.formItems\r\n          this.listFormItem = res.Entries\r\n          this.totalRecords = res.TotalItems ? res.TotalItems : 0\r\n        }\r\n      })\r\n    ).subscribe()\r\n  }\r\n\r\n\r\n\r\n  buildCaseId: number\r\n  cBuildCaseSelected: any\r\n\r\n  override ngOnInit(): void {\r\n    this.cBuildCaseSelected = null\r\n    this.getUserBuildCase()\r\n  }\r\n\r\n  userBuildCaseOptions: any\r\n\r\n  onSelectionChangeBuildCase() {\r\n    this.getListFormItem()\r\n  }\r\n\r\n  getUserBuildCase() {\r\n    this._buildCaseService.apiBuildCaseGetUserBuildCasePost$Json({ body: {} }).pipe(\r\n      tap(res => {\r\n        // if (res.Entries && res.StatusCode == 0) {\r\n        //   this.userBuildCaseOptions = res.Entries.map(res => {\r\n        //     return {\r\n        //       CBuildCaseName: res.CBuildCaseName,\r\n        //       cID: res.cID\r\n        //     }\r\n        //   })\r\n        //   this.cBuildCaseSelected = this.userBuildCaseOptions[0]\r\n        //   if (this.cBuildCaseSelected.cID) {\r\n        //     this.getListFormItem()\r\n        //   }\r\n        // }\r\n        if (res.Entries && res.StatusCode == 0) {\r\n          this.userBuildCaseOptions = res.Entries.map(res => {\r\n            return {\r\n              CBuildCaseName: res.CBuildCaseName,\r\n              cID: res.cID\r\n            };\r\n          });\r\n\r\n          if (this.tempBuildCaseID != -1) {\r\n            let index = this.userBuildCaseOptions.findIndex((x: any) => x.cID == this.tempBuildCaseID)\r\n            this.cBuildCaseSelected = this.userBuildCaseOptions[index]\r\n          } else {\r\n            this.cBuildCaseSelected = this.userBuildCaseOptions[0];\r\n          }\r\n          if (this.cBuildCaseSelected.cID) {\r\n            this.getListFormItem();\r\n          }\r\n        }\r\n      })\r\n    ).subscribe()\r\n  }\r\n\r\n  pageChanged(newPage: number) {\r\n    this.pageIndex = newPage;\r\n    this.getListFormItem()\r\n  }\r\n\r\n  onLock() {\r\n    this._formItemService.apiFormItemLockFormItemPost$Json({\r\n      body: {\r\n        CBuildCaseId: this.cBuildCaseSelected.cID,\r\n        CFormId: this.listFormItem.CFormId\r\n      }\r\n    }).subscribe(res => {\r\n      if (res.StatusCode == 0) {\r\n        this.message.showSucessMSG(\"執行成功\");\r\n      } else {\r\n        this.message.showErrorMSG(res.Message!);\r\n      }\r\n      this.getListFormItem()\r\n    })\r\n  }\r\n\r\n  unLock() {\r\n    this._formItemService.apiFormItemUnlockFormItemPost$Json({\r\n      body: {\r\n        CBuildCaseID: this.cBuildCaseSelected.cID,\r\n        CFormId: this.listFormItem.CFormId\r\n      }\r\n    }).subscribe(res => {\r\n      if (res.StatusCode == 0) {\r\n        this.message.showSucessMSG(\"執行成功\");\r\n      } else {\r\n        this.message.showErrorMSG(res.Message!);\r\n      }\r\n      this.getListFormItem();\r\n    });\r\n  }\r\n\r\n}\r\n", "<!-- 3.7 -->\r\n<nb-card accent=\"success\">\r\n  <nb-card-header>\r\n    <ngx-breadcrumb></ngx-breadcrumb>\r\n  </nb-card-header>\r\n  <nb-card-body>\r\n    <h1 class=\"font-bold text-[#818181]\">\r\n      您可將於建材管理及方案管理設定好的方案及材料，於此組合成選樣內容，並可設定各方案、材料可選擇之戶型。\r\n    </h1>\r\n    <div class=\"d-flex flex-wrap\">\r\n      <div class=\"col-md-6\">\r\n        <div class=\"form-group d-flex align-items-center w-full\">\r\n          <label for=\"buildingName\" class=\"label col-3\">建案</label>\r\n          <nb-select placeholder=\"建案\" [(ngModel)]=\"cBuildCaseSelected\" class=\"col-9\"\r\n            (selectedChange)=\"onSelectionChangeBuildCase()\">\r\n            <nb-option *ngFor=\"let case of userBuildCaseOptions\" [value]=\"case\">\r\n              {{ case.CBuildCaseName }}\r\n            </nb-option>\r\n          </nb-select>\r\n        </div>\r\n      </div>\r\n      <div class=\"col-md-6\">\r\n        <div class=\"d-flex justify-content-end w-full\">\r\n          <button class=\"btn btn-warning mx-2\" *ngIf=\"listFormItem && !listFormItem.CIsLock === false && isUpdate\"\r\n            (click)=\"unLock()\">\r\n            解除\r\n          </button>\r\n          <button class=\"btn btn-secondary mx-2\" *ngIf=\"listFormItem && listFormItem.CIsLock == false && isUpdate\"\r\n            (click)=\"onLock()\">\r\n            鎖定\r\n          </button>\r\n          <button class=\"btn btn-info\" *ngIf=\"listFormItem && listFormItem.formItems && isUpdate\"\r\n            (click)=\"navidateDetai()\">\r\n            編輯內容\r\n          </button>\r\n          <button class=\"btn btn-info\" *ngIf=\"listFormItem && !listFormItem.formItems && isCreate\"\r\n            (click)=\"navidateDetai()\">\r\n            新增\r\n          </button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <div class=\"table-responsive mt-4\">\r\n      <table class=\"table table-striped border \" style=\"background-color:#f3f3f3;\">\r\n        <thead>\r\n          <tr style=\"background-color: #27ae60; color: white;\">\r\n            <th scope=\"col\" class=\"col-1\">方案名稱/建材位置</th>\r\n            <th scope=\"col\" class=\"col-1\">適用戶別 </th>\r\n          </tr>\r\n        </thead>\r\n        <tbody>\r\n          <tr *ngFor=\"let item of formItems ; let i = index\">\r\n            <td>{{ item.CItemName}}</td>\r\n            <td>\r\n              <span *ngFor=\"let i of item.tblFormItemHouseholds; let ix = index\">\r\n                {{ix > 0 ? '、' :''}} {{i.CHousehold}}\r\n              </span>\r\n            </td>\r\n          </tr>\r\n        </tbody>\r\n      </table>\r\n    </div>\r\n  </nb-card-body>\r\n  <nb-card-footer class=\"d-flex justify-content-center\">\r\n    <ngb-pagination [(page)]=\"pageIndex\" [pageSize]=\"pageSize\" [collectionSize]=\"totalRecords\"\r\n      (pageChange)=\"pageChanged($event)\" aria-label=\"Pagination\">\r\n    </ngb-pagination>\r\n  </nb-card-footer>\r\n</nb-card>"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAK9C,SAASC,GAAG,QAAQ,MAAM;AAE1B,SAASC,YAAY,QAAQ,gCAAgC;AAC7D,SAASC,aAAa,QAAQ,qCAAqC;AACnE,SAASC,MAAM,QAA8B,uCAAuC;;;;;;;;;;;;;;ICKxEC,EAAA,CAAAC,cAAA,oBAAoE;IAClED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAFyCH,EAAA,CAAAI,UAAA,UAAAC,OAAA,CAAc;IACjEL,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAAO,kBAAA,MAAAF,OAAA,CAAAG,cAAA,MACF;;;;;;IAMFR,EAAA,CAAAC,cAAA,iBACqB;IAAnBD,EAAA,CAAAS,UAAA,mBAAAC,+EAAA;MAAAV,EAAA,CAAAW,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAb,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAe,WAAA,CAASF,MAAA,CAAAG,MAAA,EAAQ;IAAA,EAAC;IAClBhB,EAAA,CAAAE,MAAA,qBACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;IACTH,EAAA,CAAAC,cAAA,iBACqB;IAAnBD,EAAA,CAAAS,UAAA,mBAAAQ,+EAAA;MAAAjB,EAAA,CAAAW,aAAA,CAAAO,GAAA;MAAA,MAAAL,MAAA,GAAAb,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAe,WAAA,CAASF,MAAA,CAAAM,MAAA,EAAQ;IAAA,EAAC;IAClBnB,EAAA,CAAAE,MAAA,qBACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;IACTH,EAAA,CAAAC,cAAA,iBAC4B;IAA1BD,EAAA,CAAAS,UAAA,mBAAAW,+EAAA;MAAApB,EAAA,CAAAW,aAAA,CAAAU,GAAA;MAAA,MAAAR,MAAA,GAAAb,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAe,WAAA,CAASF,MAAA,CAAAS,aAAA,EAAe;IAAA,EAAC;IACzBtB,EAAA,CAAAE,MAAA,iCACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;IACTH,EAAA,CAAAC,cAAA,iBAC4B;IAA1BD,EAAA,CAAAS,UAAA,mBAAAc,+EAAA;MAAAvB,EAAA,CAAAW,aAAA,CAAAa,GAAA;MAAA,MAAAX,MAAA,GAAAb,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAe,WAAA,CAASF,MAAA,CAAAS,aAAA,EAAe;IAAA,EAAC;IACzBtB,EAAA,CAAAE,MAAA,qBACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;IAiBLH,EAAA,CAAAC,cAAA,WAAmE;IACjED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IADLH,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAAyB,kBAAA,MAAAC,KAAA,2BAAAC,IAAA,CAAAC,UAAA,MACF;;;;;IAJF5B,EADF,CAAAC,cAAA,SAAmD,SAC7C;IAAAD,EAAA,CAAAE,MAAA,GAAmB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC5BH,EAAA,CAAAC,cAAA,SAAI;IACFD,EAAA,CAAA6B,UAAA,IAAAC,yDAAA,mBAAmE;IAIvE9B,EADE,CAAAG,YAAA,EAAK,EACF;;;;IANCH,EAAA,CAAAM,SAAA,GAAmB;IAAnBN,EAAA,CAAA+B,iBAAA,CAAAC,OAAA,CAAAC,SAAA,CAAmB;IAEDjC,EAAA,CAAAM,SAAA,GAA+B;IAA/BN,EAAA,CAAAI,UAAA,YAAA4B,OAAA,CAAAE,qBAAA,CAA+B;;;AD7BjE,OAAM,MAAOC,mCAAoC,SAAQrC,aAAa;EACpEsC,YACUC,MAAmB,EACnBC,MAAc,EACdC,OAAuB,EACvBC,iBAAmC,EACnCC,gBAAiC,EACjCC,aAA2B;IAEnC,KAAK,CAACL,MAAM,CAAC;IAPL,KAAAA,MAAM,GAANA,MAAM;IACN,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAC,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,aAAa,GAAbA,aAAa;IAYvB,KAAAC,eAAe,GAAW,CAAC,CAAC;IAE5B,KAAAC,uBAAuB,GAAU,CAAC;MAACC,KAAK,EAAE,EAAE;MAAEC,KAAK,EAAE;IAAI,CAAC,CAAC;IAIlD,KAAAC,QAAQ,GAAG,EAAE;IAKtB,KAAAC,8BAA8B,GAAG;MAC/BC,SAAS,EAAE,CAAC;MACZC,WAAW,EAAE;KACd;IAvBC,IAAI,CAACR,aAAa,CAACS,OAAO,EAAE,CAACC,IAAI,CAC/BxD,GAAG,CAAEyD,GAAW,IAAI;MAClB,IAAIA,GAAG,CAACC,MAAM,kDAA4B,CAAC,CAACD,GAAG,CAACE,OAAO,EAAE;QACvD,IAAI,CAACZ,eAAe,GAAGU,GAAG,CAACE,OAAO;MACpC;IACF,CAAC,CAAC,CACH,CAACC,SAAS,EAAE;EACf;EAUAlC,aAAaA,CAAA;IACX,IAAI,CAACgB,MAAM,CAACmB,QAAQ,CAAC,CAAC,sCAAsC,IAAI,CAACC,kBAAkB,CAACC,GAAG,EAAE,CAAC,CAAC;EAC7F;EAKAC,eAAeA,CAAA;IACb,IAAI,CAACnB,gBAAgB,CAACoB,mCAAmC,CAAC;MACxDC,IAAI,EAAE;QACJC,YAAY,EAAE,IAAI,CAACL,kBAAkB,CAACC,GAAG;QACzCV,SAAS,EAAE,IAAI,CAACD,8BAA8B,CAACC,SAAS;QACxDe,SAAS,EAAE,IAAI,CAACC,SAAS;QACzBC,QAAQ,EAAE,IAAI,CAACnB,QAAQ;QACvBoB,SAAS,EAAE;;KAEd,CAAC,CAACf,IAAI,CACLxD,GAAG,CAACyD,GAAG,IAAG;MACR,IAAIA,GAAG,CAACe,OAAO,IAAIf,GAAG,CAACgB,UAAU,IAAI,CAAC,EAAE;QACtC,IAAI,CAACC,SAAS,GAAGjB,GAAG,CAACe,OAAO,CAACE,SAAS;QACtC,IAAI,CAACC,YAAY,GAAGlB,GAAG,CAACe,OAAO;QAC/B,IAAI,CAACI,YAAY,GAAGnB,GAAG,CAACoB,UAAU,GAAGpB,GAAG,CAACoB,UAAU,GAAG,CAAC;MACzD;IACF,CAAC,CAAC,CACH,CAACjB,SAAS,EAAE;EACf;EAOSkB,QAAQA,CAAA;IACf,IAAI,CAAChB,kBAAkB,GAAG,IAAI;IAC9B,IAAI,CAACiB,gBAAgB,EAAE;EACzB;EAIAC,0BAA0BA,CAAA;IACxB,IAAI,CAAChB,eAAe,EAAE;EACxB;EAEAe,gBAAgBA,CAAA;IACd,IAAI,CAACnC,iBAAiB,CAACqC,qCAAqC,CAAC;MAAEf,IAAI,EAAE;IAAE,CAAE,CAAC,CAACV,IAAI,CAC7ExD,GAAG,CAACyD,GAAG,IAAG;MACR;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA,IAAIA,GAAG,CAACe,OAAO,IAAIf,GAAG,CAACgB,UAAU,IAAI,CAAC,EAAE;QACtC,IAAI,CAACS,oBAAoB,GAAGzB,GAAG,CAACe,OAAO,CAACW,GAAG,CAAC1B,GAAG,IAAG;UAChD,OAAO;YACL7C,cAAc,EAAE6C,GAAG,CAAC7C,cAAc;YAClCmD,GAAG,EAAEN,GAAG,CAACM;WACV;QACH,CAAC,CAAC;QAEF,IAAI,IAAI,CAAChB,eAAe,IAAI,CAAC,CAAC,EAAE;UAC9B,IAAIqC,KAAK,GAAG,IAAI,CAACF,oBAAoB,CAACG,SAAS,CAAEC,CAAM,IAAKA,CAAC,CAACvB,GAAG,IAAI,IAAI,CAAChB,eAAe,CAAC;UAC1F,IAAI,CAACe,kBAAkB,GAAG,IAAI,CAACoB,oBAAoB,CAACE,KAAK,CAAC;QAC5D,CAAC,MAAM;UACL,IAAI,CAACtB,kBAAkB,GAAG,IAAI,CAACoB,oBAAoB,CAAC,CAAC,CAAC;QACxD;QACA,IAAI,IAAI,CAACpB,kBAAkB,CAACC,GAAG,EAAE;UAC/B,IAAI,CAACC,eAAe,EAAE;QACxB;MACF;IACF,CAAC,CAAC,CACH,CAACJ,SAAS,EAAE;EACf;EAEA2B,WAAWA,CAACC,OAAe;IACzB,IAAI,CAACnB,SAAS,GAAGmB,OAAO;IACxB,IAAI,CAACxB,eAAe,EAAE;EACxB;EAEAzC,MAAMA,CAAA;IACJ,IAAI,CAACsB,gBAAgB,CAAC4C,gCAAgC,CAAC;MACrDvB,IAAI,EAAE;QACJC,YAAY,EAAE,IAAI,CAACL,kBAAkB,CAACC,GAAG;QACzC2B,OAAO,EAAE,IAAI,CAACf,YAAY,CAACe;;KAE9B,CAAC,CAAC9B,SAAS,CAACH,GAAG,IAAG;MACjB,IAAIA,GAAG,CAACgB,UAAU,IAAI,CAAC,EAAE;QACvB,IAAI,CAAC9B,OAAO,CAACgD,aAAa,CAAC,MAAM,CAAC;MACpC,CAAC,MAAM;QACL,IAAI,CAAChD,OAAO,CAACiD,YAAY,CAACnC,GAAG,CAACoC,OAAQ,CAAC;MACzC;MACA,IAAI,CAAC7B,eAAe,EAAE;IACxB,CAAC,CAAC;EACJ;EAEA5C,MAAMA,CAAA;IACJ,IAAI,CAACyB,gBAAgB,CAACiD,kCAAkC,CAAC;MACvD5B,IAAI,EAAE;QACJ6B,YAAY,EAAE,IAAI,CAACjC,kBAAkB,CAACC,GAAG;QACzC2B,OAAO,EAAE,IAAI,CAACf,YAAY,CAACe;;KAE9B,CAAC,CAAC9B,SAAS,CAACH,GAAG,IAAG;MACjB,IAAIA,GAAG,CAACgB,UAAU,IAAI,CAAC,EAAE;QACvB,IAAI,CAAC9B,OAAO,CAACgD,aAAa,CAAC,MAAM,CAAC;MACpC,CAAC,MAAM;QACL,IAAI,CAAChD,OAAO,CAACiD,YAAY,CAACnC,GAAG,CAACoC,OAAQ,CAAC;MACzC;MACA,IAAI,CAAC7B,eAAe,EAAE;IACxB,CAAC,CAAC;EACJ;;;uCA9IWzB,mCAAmC,EAAAnC,EAAA,CAAA4F,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAA9F,EAAA,CAAA4F,iBAAA,CAAAG,EAAA,CAAAC,MAAA,GAAAhG,EAAA,CAAA4F,iBAAA,CAAAK,EAAA,CAAAC,cAAA,GAAAlG,EAAA,CAAA4F,iBAAA,CAAAO,EAAA,CAAAC,gBAAA,GAAApG,EAAA,CAAA4F,iBAAA,CAAAO,EAAA,CAAAE,eAAA,GAAArG,EAAA,CAAA4F,iBAAA,CAAAU,EAAA,CAAAC,YAAA;IAAA;EAAA;;;YAAnCpE,mCAAmC;MAAAqE,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAA1G,EAAA,CAAA2G,0BAAA,EAAA3G,EAAA,CAAA4G,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,6CAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCxB9ClH,EADF,CAAAC,cAAA,iBAA0B,qBACR;UACdD,EAAA,CAAAoH,SAAA,qBAAiC;UACnCpH,EAAA,CAAAG,YAAA,EAAiB;UAEfH,EADF,CAAAC,cAAA,mBAAc,YACyB;UACnCD,EAAA,CAAAE,MAAA,qTACF;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAICH,EAHN,CAAAC,cAAA,aAA8B,aACN,aACqC,eACT;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACxDH,EAAA,CAAAC,cAAA,oBACkD;UADtBD,EAAA,CAAAqH,gBAAA,2BAAAC,iFAAAC,MAAA;YAAAvH,EAAA,CAAAwH,kBAAA,CAAAL,GAAA,CAAAzD,kBAAA,EAAA6D,MAAA,MAAAJ,GAAA,CAAAzD,kBAAA,GAAA6D,MAAA;YAAA,OAAAA,MAAA;UAAA,EAAgC;UAC1DvH,EAAA,CAAAS,UAAA,4BAAAgH,kFAAA;YAAA,OAAkBN,GAAA,CAAAvC,0BAAA,EAA4B;UAAA,EAAC;UAC/C5E,EAAA,CAAA6B,UAAA,KAAA6F,yDAAA,uBAAoE;UAK1E1H,EAFI,CAAAG,YAAA,EAAY,EACR,EACF;UAEJH,EADF,CAAAC,cAAA,cAAsB,cAC2B;UAa7CD,EAZA,CAAA6B,UAAA,KAAA8F,sDAAA,oBACqB,KAAAC,sDAAA,qBAIA,KAAAC,sDAAA,qBAIO,KAAAC,sDAAA,qBAIA;UAKlC9H,EAFI,CAAAG,YAAA,EAAM,EACF,EACF;UAMEH,EAJR,CAAAC,cAAA,eAAmC,iBAC4C,aACpE,cACgD,cACrB;UAAAD,EAAA,CAAAE,MAAA,yDAAS;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAC5CH,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAE,MAAA,iCAAK;UAEvCF,EAFuC,CAAAG,YAAA,EAAK,EACrC,EACC;UACRH,EAAA,CAAAC,cAAA,aAAO;UACLD,EAAA,CAAA6B,UAAA,KAAAkG,kDAAA,iBAAmD;UAW3D/H,EAHM,CAAAG,YAAA,EAAQ,EACF,EACJ,EACO;UAEbH,EADF,CAAAC,cAAA,0BAAsD,0BAES;UAD7CD,EAAA,CAAAqH,gBAAA,wBAAAW,mFAAAT,MAAA;YAAAvH,EAAA,CAAAwH,kBAAA,CAAAL,GAAA,CAAAlD,SAAA,EAAAsD,MAAA,MAAAJ,GAAA,CAAAlD,SAAA,GAAAsD,MAAA;YAAA,OAAAA,MAAA;UAAA,EAAoB;UAClCvH,EAAA,CAAAS,UAAA,wBAAAuH,mFAAAT,MAAA;YAAA,OAAcJ,GAAA,CAAAhC,WAAA,CAAAoC,MAAA,CAAmB;UAAA,EAAC;UAGxCvH,EAFI,CAAAG,YAAA,EAAiB,EACF,EACT;;;UAxD4BH,EAAA,CAAAM,SAAA,IAAgC;UAAhCN,EAAA,CAAAiI,gBAAA,YAAAd,GAAA,CAAAzD,kBAAA,CAAgC;UAE9B1D,EAAA,CAAAM,SAAA,EAAuB;UAAvBN,EAAA,CAAAI,UAAA,YAAA+G,GAAA,CAAArC,oBAAA,CAAuB;UAQf9E,EAAA,CAAAM,SAAA,GAAiE;UAAjEN,EAAA,CAAAI,UAAA,SAAA+G,GAAA,CAAA5C,YAAA,KAAA4C,GAAA,CAAA5C,YAAA,CAAA2D,OAAA,cAAAf,GAAA,CAAAgB,QAAA,CAAiE;UAI/DnI,EAAA,CAAAM,SAAA,EAA+D;UAA/DN,EAAA,CAAAI,UAAA,SAAA+G,GAAA,CAAA5C,YAAA,IAAA4C,GAAA,CAAA5C,YAAA,CAAA2D,OAAA,aAAAf,GAAA,CAAAgB,QAAA,CAA+D;UAIzEnI,EAAA,CAAAM,SAAA,EAAwD;UAAxDN,EAAA,CAAAI,UAAA,SAAA+G,GAAA,CAAA5C,YAAA,IAAA4C,GAAA,CAAA5C,YAAA,CAAAD,SAAA,IAAA6C,GAAA,CAAAgB,QAAA,CAAwD;UAIxDnI,EAAA,CAAAM,SAAA,EAAyD;UAAzDN,EAAA,CAAAI,UAAA,SAAA+G,GAAA,CAAA5C,YAAA,KAAA4C,GAAA,CAAA5C,YAAA,CAAAD,SAAA,IAAA6C,GAAA,CAAAiB,QAAA,CAAyD;UAiBlEpI,EAAA,CAAAM,SAAA,IAAe;UAAfN,EAAA,CAAAI,UAAA,YAAA+G,GAAA,CAAA7C,SAAA,CAAe;UAa1BtE,EAAA,CAAAM,SAAA,GAAoB;UAApBN,EAAA,CAAAiI,gBAAA,SAAAd,GAAA,CAAAlD,SAAA,CAAoB;UAAuBjE,EAAtB,CAAAI,UAAA,aAAA+G,GAAA,CAAApE,QAAA,CAAqB,mBAAAoE,GAAA,CAAA3C,YAAA,CAAgC;;;qBD1ClF7E,YAAY,EAAA0I,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EAAE1I,YAAY,EAAA2I,EAAA,CAAAC,eAAA,EAAAD,EAAA,CAAAE,OAAA,EAAAC,EAAA,CAAAC,eAAA,EAAAD,EAAA,CAAAE,mBAAA,EAAAF,EAAA,CAAAG,qBAAA,EAAAH,EAAA,CAAAI,qBAAA,EAAAJ,EAAA,CAAAK,iBAAA,EAAAL,EAAA,CAAAM,iBAAA,EAAAC,EAAA,CAAAC,aAAA,EAAAC,GAAA,CAAAC,mBAAA;MAAAC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}