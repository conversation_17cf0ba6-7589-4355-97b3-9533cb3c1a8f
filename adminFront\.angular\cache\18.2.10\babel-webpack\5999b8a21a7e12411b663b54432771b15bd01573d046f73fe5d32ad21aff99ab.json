{"ast": null, "code": "import * as i0 from \"@angular/core\";\nexport class ValidationHelper {\n  constructor() {\n    this.errorMessages = [];\n  }\n  required(name, value) {\n    if (value == null || value === undefined) {\n      this.addErrorMessage(name + ' 必填');\n    }\n    if (typeof String) {\n      if (value === '') {\n        this.addErrorMessage(name + ' 必填');\n      }\n    }\n  }\n  checkStartBeforeEnd(name, start, end) {\n    if (start && end) {\n      const startDate = new Date(start);\n      const endDate = new Date(end);\n      if (startDate > endDate) {\n        this.addErrorMessage(name + ' 开始日期不能大于结束日期');\n      }\n    }\n  }\n  isPhoneNumber(name, value) {\n    if (value !== null && value !== undefined && value !== '') {\n      const phoneRegex = /^\\+?\\d{1,3}[-.\\s]?\\(?\\d{1,3}\\)?[-.\\s]?\\d{3,4}[-.\\s]?\\d{4}$/;\n      if (!phoneRegex.test(value)) {\n        this.addErrorMessage(name + '電話號碼格式不正確');\n      }\n      if (value.length !== 10 || isNaN(value)) {\n        this.addErrorMessage(name + ' 長度=10');\n      }\n    }\n  }\n  isNaturalNumberInRange(name, value, min, max) {\n    if (value == null || value === undefined || value === '') {\n      this.addErrorMessage(name + ' 必填');\n    } else if (typeof value === 'string' && !/^\\d+$/.test(value)) {\n      this.addErrorMessage(name + ' 必須是數字');\n    } else {\n      const numValue = parseInt(value, 10);\n      if (numValue < min || numValue > max) {\n        this.addErrorMessage(name + ` 必須介於${min}到${max}之間`);\n      }\n    }\n  }\n  isStringMaxLength(name, value, maxLength) {\n    if (typeof value === 'string' && value.length > maxLength) {\n      this.addErrorMessage(name + ` 長度不能超過 ${maxLength} 個字元`);\n    }\n  }\n  pattern(name, value, pattern, errorDetail = '') {\n    if (!value) return;\n    const regex = new RegExp(pattern);\n    if (regex.test(value) === false) {\n      this.addErrorMessage(name + ' 格式錯誤' + errorDetail);\n    }\n  }\n  regExp(name, value, regEx) {\n    if (regEx.test(value) === false) {\n      this.addErrorMessage(name + ' 格式錯誤');\n    }\n  }\n  equal(name1, name2, value1, value2) {\n    if (value1 !== value2) {\n      this.addErrorMessage(name1 + ' 與 ' + name2 + ' 不相同');\n    }\n  }\n  selected(name, value) {\n    if (value.filter(x => x === '' || x === null || x === undefined).length > 0) {\n      this.addErrorMessage(name + ' 尚未全部選擇');\n    }\n  }\n  CheckTaiwanID(userid) {\n    const reg = /^[A-Z]{1}[1-2]{1}[0-9]{8}$/; //身份證的正規表示式\n    if (reg.test(userid)) {\n      const s = \"ABCDEFGHIJKLMNOPQRSTUVWXYZ\"; //把A取代成10,把B取代成11...的作法\n      const ct = [\"10\", \"11\", \"12\", \"13\", \"14\", \"15\", \"16\", \"17\", \"34\", \"18\", \"19\", \"20\", \"21\", \"22\", \"35\", \"23\", \"24\", \"25\", \"26\", \"27\", \"28\", \"29\", \"32\", \"30\", \"31\", \"33\"];\n      let i = s.indexOf(userid.charAt(0));\n      const tempuserid = ct[i] + userid.substr(1, 9); //若此身份證號若是A123456789=>10123456789\n      let num = parseInt(tempuserid.charAt(0)) * 1;\n      for (i = 1; i <= 9; i++) {\n        num = num + parseInt(tempuserid.charAt(i)) * (10 - i);\n      }\n      num += parseInt(tempuserid.charAt(10)) * 1;\n      if (num % 10 == 0) {\n        console.log(\"next step\");\n      } else {\n        this.addErrorMessage('身份證字號輸入錯誤');\n      }\n    } else {\n      this.addErrorMessage('身份證字號輸入錯誤');\n    }\n  }\n  Date(StrDt, EndDt) {\n    if (EndDt != null) {\n      if (EndDt < StrDt) {\n        this.addErrorMessage(\"起始時間不能大於結束時間！\");\n      }\n    }\n  }\n  addErrorMessage(message) {\n    this.errorMessages.push(message);\n  }\n  clear() {\n    this.errorMessages = [];\n  }\n  static {\n    this.ɵfac = function ValidationHelper_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || ValidationHelper)();\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: ValidationHelper,\n      factory: ValidationHelper.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["ValidationHelper", "constructor", "errorMessages", "required", "name", "value", "undefined", "addErrorMessage", "String", "checkStartBeforeEnd", "start", "end", "startDate", "Date", "endDate", "isPhoneNumber", "phoneRegex", "test", "length", "isNaN", "isNaturalNumberInRange", "min", "max", "numValue", "parseInt", "isStringMaxLength", "max<PERSON><PERSON><PERSON>", "pattern", "errorDetail", "regex", "RegExp", "regExp", "regEx", "equal", "name1", "name2", "value1", "value2", "selected", "filter", "x", "CheckTaiwanID", "userid", "reg", "s", "ct", "i", "indexOf", "char<PERSON>t", "tempuserid", "substr", "num", "console", "log", "StrDt", "EndDt", "message", "push", "clear", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\Documents\\jeansalechange\\adminFront\\src\\app\\shared\\helper\\validationHelper.ts"], "sourcesContent": ["import { filter } from 'rxjs/operators';\r\nimport { Injectable } from '@angular/core';\r\nimport { Router } from '@angular/router';\r\n\r\n@Injectable({ providedIn: 'root' })\r\nexport class ValidationHelper {\r\n\r\n  errorMessages = [] as string[];\r\n\r\n  constructor() {}\r\n\r\n  required(name: string, value: any) {\r\n    if (value == null || value === undefined) {\r\n      this.addErrorMessage(name + ' 必填');\r\n    }\r\n    if (typeof (String)) {\r\n      if (value === '') {\r\n        this.addErrorMessage(name + ' 必填');\r\n      }\r\n    }\r\n  }\r\n\r\n  checkStartBeforeEnd (name: string, start?: string, end?: string) {\r\n    if (start && end) {\r\n      const startDate = new Date(start);\r\n      const endDate = new Date(end);\r\n      if (startDate > endDate) {\r\n        this.addErrorMessage(name + ' 开始日期不能大于结束日期');\r\n      }\r\n    } \r\n  }\r\n\r\n  isPhoneNumber(name: string, value: any) {\r\n    if (value !== null && value !== undefined && value !== '') {\r\n      const phoneRegex = /^\\+?\\d{1,3}[-.\\s]?\\(?\\d{1,3}\\)?[-.\\s]?\\d{3,4}[-.\\s]?\\d{4}$/; \r\n      if (!phoneRegex.test(value)) {\r\n        this.addErrorMessage(name + '電話號碼格式不正確');\r\n      }\r\n      if (value.length !== 10 || isNaN(value)) {\r\n        this.addErrorMessage(name + ' 長度=10');\r\n      }\r\n    }\r\n  }\r\n\r\n  isNaturalNumberInRange(name: string, value: any, min: number, max: number) {\r\n    if (value == null || value === undefined || value === '') {\r\n      this.addErrorMessage(name + ' 必填'); \r\n    } else if (typeof value === 'string' && !/^\\d+$/.test(value)) {\r\n      this.addErrorMessage(name + ' 必須是數字'); \r\n    } else {\r\n      const numValue = parseInt(value, 10);\r\n      if (numValue < min || numValue > max) {\r\n        this.addErrorMessage(name + ` 必須介於${min}到${max}之間`); \r\n      }\r\n    }\r\n  }\r\n\r\n  isStringMaxLength(name: string, value: any, maxLength: number) {\r\n    if (typeof value === 'string' && value.length > maxLength) {\r\n      this.addErrorMessage(name + ` 長度不能超過 ${maxLength} 個字元`); \r\n    }\r\n  }\r\n\r\n  pattern(name: string, value: string | null | undefined, pattern: string, errorDetail: string = '') {\r\n    if(!value) return\r\n    const regex = new RegExp(pattern);\r\n\r\n    if (regex.test(value!) === false) {\r\n      this.addErrorMessage(name + ' 格式錯誤' + errorDetail);\r\n    }\r\n  }\r\n\r\n  regExp(name: string, value: string, regEx: RegExp) {\r\n    if (regEx.test(value) === false) {\r\n      this.addErrorMessage(name + ' 格式錯誤');\r\n    }\r\n  }\r\n\r\n  equal(name1: string, name2: string, value1: string | undefined | null, value2: string | undefined | null) {\r\n    if (value1 !== value2) {\r\n      this.addErrorMessage(name1 + ' 與 ' + name2 + ' 不相同');\r\n    }\r\n  }\r\n\r\n\r\n  selected(name: string, value: string[]) {\r\n    if (value.filter(x => x === '' || x === null || x === undefined).length > 0) {\r\n      this.addErrorMessage(name + ' 尚未全部選擇');\r\n    }\r\n  }\r\n\r\n  CheckTaiwanID(userid: string) { //身份證檢查函式\r\n    const reg = /^[A-Z]{1}[1-2]{1}[0-9]{8}$/;  //身份證的正規表示式\r\n    if (reg.test(userid)) {\r\n      const s = \"ABCDEFGHIJKLMNOPQRSTUVWXYZ\";  //把A取代成10,把B取代成11...的作法\r\n      const ct = [\"10\", \"11\", \"12\", \"13\", \"14\", \"15\", \"16\", \"17\", \"34\", \"18\", \"19\", \"20\", \"21\",\r\n        \"22\", \"35\", \"23\", \"24\", \"25\", \"26\", \"27\", \"28\", \"29\", \"32\", \"30\", \"31\", \"33\"];\r\n      let i: number = s.indexOf(userid.charAt(0));\r\n      const tempuserid = ct[i] + userid.substr(1, 9); //若此身份證號若是A123456789=>10123456789\r\n      let num = parseInt(tempuserid.charAt(0)) * 1;\r\n      for (i = 1; i <= 9; i++) {\r\n        num = num + parseInt(tempuserid.charAt(i)) * (10 - i);\r\n      }\r\n      num += parseInt(tempuserid.charAt(10)) * 1;\r\n\r\n      if ((num % 10) == 0) {\r\n        console.log(\"next step\");\r\n      }\r\n      else {\r\n        this.addErrorMessage('身份證字號輸入錯誤');\r\n      }\r\n    }\r\n    else {\r\n      this.addErrorMessage('身份證字號輸入錯誤');\r\n    }\r\n  }\r\n\r\n  Date(StrDt: Date, EndDt: Date) {\r\n    if (EndDt != null) {\r\n      if (EndDt < StrDt) {\r\n        this.addErrorMessage(\"起始時間不能大於結束時間！\");\r\n      }\r\n    }\r\n  }\r\n\r\n\r\n  addErrorMessage(message: string) {\r\n    this.errorMessages.push(message);\r\n  }\r\n\r\n  clear() {\r\n    this.errorMessages = [];\r\n  }\r\n}\r\n"], "mappings": ";AAKA,OAAM,MAAOA,gBAAgB;EAI3BC,YAAA;IAFA,KAAAC,aAAa,GAAG,EAAc;EAEf;EAEfC,QAAQA,CAACC,IAAY,EAAEC,KAAU;IAC/B,IAAIA,KAAK,IAAI,IAAI,IAAIA,KAAK,KAAKC,SAAS,EAAE;MACxC,IAAI,CAACC,eAAe,CAACH,IAAI,GAAG,KAAK,CAAC;IACpC;IACA,IAAI,OAAQI,MAAO,EAAE;MACnB,IAAIH,KAAK,KAAK,EAAE,EAAE;QAChB,IAAI,CAACE,eAAe,CAACH,IAAI,GAAG,KAAK,CAAC;MACpC;IACF;EACF;EAEAK,mBAAmBA,CAAEL,IAAY,EAAEM,KAAc,EAAEC,GAAY;IAC7D,IAAID,KAAK,IAAIC,GAAG,EAAE;MAChB,MAAMC,SAAS,GAAG,IAAIC,IAAI,CAACH,KAAK,CAAC;MACjC,MAAMI,OAAO,GAAG,IAAID,IAAI,CAACF,GAAG,CAAC;MAC7B,IAAIC,SAAS,GAAGE,OAAO,EAAE;QACvB,IAAI,CAACP,eAAe,CAACH,IAAI,GAAG,eAAe,CAAC;MAC9C;IACF;EACF;EAEAW,aAAaA,CAACX,IAAY,EAAEC,KAAU;IACpC,IAAIA,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAKC,SAAS,IAAID,KAAK,KAAK,EAAE,EAAE;MACzD,MAAMW,UAAU,GAAG,4DAA4D;MAC/E,IAAI,CAACA,UAAU,CAACC,IAAI,CAACZ,KAAK,CAAC,EAAE;QAC3B,IAAI,CAACE,eAAe,CAACH,IAAI,GAAG,WAAW,CAAC;MAC1C;MACA,IAAIC,KAAK,CAACa,MAAM,KAAK,EAAE,IAAIC,KAAK,CAACd,KAAK,CAAC,EAAE;QACvC,IAAI,CAACE,eAAe,CAACH,IAAI,GAAG,QAAQ,CAAC;MACvC;IACF;EACF;EAEAgB,sBAAsBA,CAAChB,IAAY,EAAEC,KAAU,EAAEgB,GAAW,EAAEC,GAAW;IACvE,IAAIjB,KAAK,IAAI,IAAI,IAAIA,KAAK,KAAKC,SAAS,IAAID,KAAK,KAAK,EAAE,EAAE;MACxD,IAAI,CAACE,eAAe,CAACH,IAAI,GAAG,KAAK,CAAC;IACpC,CAAC,MAAM,IAAI,OAAOC,KAAK,KAAK,QAAQ,IAAI,CAAC,OAAO,CAACY,IAAI,CAACZ,KAAK,CAAC,EAAE;MAC5D,IAAI,CAACE,eAAe,CAACH,IAAI,GAAG,QAAQ,CAAC;IACvC,CAAC,MAAM;MACL,MAAMmB,QAAQ,GAAGC,QAAQ,CAACnB,KAAK,EAAE,EAAE,CAAC;MACpC,IAAIkB,QAAQ,GAAGF,GAAG,IAAIE,QAAQ,GAAGD,GAAG,EAAE;QACpC,IAAI,CAACf,eAAe,CAACH,IAAI,GAAG,QAAQiB,GAAG,IAAIC,GAAG,IAAI,CAAC;MACrD;IACF;EACF;EAEAG,iBAAiBA,CAACrB,IAAY,EAAEC,KAAU,EAAEqB,SAAiB;IAC3D,IAAI,OAAOrB,KAAK,KAAK,QAAQ,IAAIA,KAAK,CAACa,MAAM,GAAGQ,SAAS,EAAE;MACzD,IAAI,CAACnB,eAAe,CAACH,IAAI,GAAG,WAAWsB,SAAS,MAAM,CAAC;IACzD;EACF;EAEAC,OAAOA,CAACvB,IAAY,EAAEC,KAAgC,EAAEsB,OAAe,EAAEC,WAAA,GAAsB,EAAE;IAC/F,IAAG,CAACvB,KAAK,EAAE;IACX,MAAMwB,KAAK,GAAG,IAAIC,MAAM,CAACH,OAAO,CAAC;IAEjC,IAAIE,KAAK,CAACZ,IAAI,CAACZ,KAAM,CAAC,KAAK,KAAK,EAAE;MAChC,IAAI,CAACE,eAAe,CAACH,IAAI,GAAG,OAAO,GAAGwB,WAAW,CAAC;IACpD;EACF;EAEAG,MAAMA,CAAC3B,IAAY,EAAEC,KAAa,EAAE2B,KAAa;IAC/C,IAAIA,KAAK,CAACf,IAAI,CAACZ,KAAK,CAAC,KAAK,KAAK,EAAE;MAC/B,IAAI,CAACE,eAAe,CAACH,IAAI,GAAG,OAAO,CAAC;IACtC;EACF;EAEA6B,KAAKA,CAACC,KAAa,EAAEC,KAAa,EAAEC,MAAiC,EAAEC,MAAiC;IACtG,IAAID,MAAM,KAAKC,MAAM,EAAE;MACrB,IAAI,CAAC9B,eAAe,CAAC2B,KAAK,GAAG,KAAK,GAAGC,KAAK,GAAG,MAAM,CAAC;IACtD;EACF;EAGAG,QAAQA,CAAClC,IAAY,EAAEC,KAAe;IACpC,IAAIA,KAAK,CAACkC,MAAM,CAACC,CAAC,IAAIA,CAAC,KAAK,EAAE,IAAIA,CAAC,KAAK,IAAI,IAAIA,CAAC,KAAKlC,SAAS,CAAC,CAACY,MAAM,GAAG,CAAC,EAAE;MAC3E,IAAI,CAACX,eAAe,CAACH,IAAI,GAAG,SAAS,CAAC;IACxC;EACF;EAEAqC,aAAaA,CAACC,MAAc;IAC1B,MAAMC,GAAG,GAAG,4BAA4B,CAAC,CAAE;IAC3C,IAAIA,GAAG,CAAC1B,IAAI,CAACyB,MAAM,CAAC,EAAE;MACpB,MAAME,CAAC,GAAG,4BAA4B,CAAC,CAAE;MACzC,MAAMC,EAAE,GAAG,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EACtF,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;MAC/E,IAAIC,CAAC,GAAWF,CAAC,CAACG,OAAO,CAACL,MAAM,CAACM,MAAM,CAAC,CAAC,CAAC,CAAC;MAC3C,MAAMC,UAAU,GAAGJ,EAAE,CAACC,CAAC,CAAC,GAAGJ,MAAM,CAACQ,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MAChD,IAAIC,GAAG,GAAG3B,QAAQ,CAACyB,UAAU,CAACD,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC;MAC5C,KAAKF,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;QACvBK,GAAG,GAAGA,GAAG,GAAG3B,QAAQ,CAACyB,UAAU,CAACD,MAAM,CAACF,CAAC,CAAC,CAAC,IAAI,EAAE,GAAGA,CAAC,CAAC;MACvD;MACAK,GAAG,IAAI3B,QAAQ,CAACyB,UAAU,CAACD,MAAM,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC;MAE1C,IAAKG,GAAG,GAAG,EAAE,IAAK,CAAC,EAAE;QACnBC,OAAO,CAACC,GAAG,CAAC,WAAW,CAAC;MAC1B,CAAC,MACI;QACH,IAAI,CAAC9C,eAAe,CAAC,WAAW,CAAC;MACnC;IACF,CAAC,MACI;MACH,IAAI,CAACA,eAAe,CAAC,WAAW,CAAC;IACnC;EACF;EAEAM,IAAIA,CAACyC,KAAW,EAAEC,KAAW;IAC3B,IAAIA,KAAK,IAAI,IAAI,EAAE;MACjB,IAAIA,KAAK,GAAGD,KAAK,EAAE;QACjB,IAAI,CAAC/C,eAAe,CAAC,eAAe,CAAC;MACvC;IACF;EACF;EAGAA,eAAeA,CAACiD,OAAe;IAC7B,IAAI,CAACtD,aAAa,CAACuD,IAAI,CAACD,OAAO,CAAC;EAClC;EAEAE,KAAKA,CAAA;IACH,IAAI,CAACxD,aAAa,GAAG,EAAE;EACzB;;;uCA/HWF,gBAAgB;IAAA;EAAA;;;aAAhBA,gBAAgB;MAAA2D,OAAA,EAAhB3D,gBAAgB,CAAA4D,IAAA;MAAAC,UAAA,EADH;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}