{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { LOCALE_ID, Injectable, Inject, Optional, NgModule } from '@angular/core';\nimport { NbNativeDateService, NB_DATE_SERVICE_OPTIONS, NbDateService } from '@nebular/theme';\nimport parse from 'date-fns/parse';\nimport formatDate from 'date-fns/format';\nimport getWeek from 'date-fns/getWeek';\n\n/*\n * @license\n * Copyright Akveo. All Rights Reserved.\n * Licensed under the MIT License. See License.txt in the project root for license information.\n */\nlet NbDateFnsDateService = /*#__PURE__*/(() => {\n  class NbDateFnsDateService extends NbNativeDateService {\n    constructor(locale, options) {\n      super(locale);\n      this.options = options || {};\n    }\n    format(date, format) {\n      if (date) {\n        return formatDate(date, format || this.options.format, this.options.formatOptions);\n      }\n      return '';\n    }\n    parse(date, format) {\n      return parse(date, format || this.options.format, new Date(), this.options.parseOptions);\n    }\n    getId() {\n      return 'date-fns';\n    }\n    getWeekNumber(date) {\n      return getWeek(date, this.options.getWeekOptions);\n    }\n    getDateFormat() {\n      return 'YYYY-MM-dd';\n    }\n    static {\n      this.ɵfac = function NbDateFnsDateService_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || NbDateFnsDateService)(i0.ɵɵinject(LOCALE_ID), i0.ɵɵinject(NB_DATE_SERVICE_OPTIONS, 8));\n      };\n    }\n    static {\n      this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n        token: NbDateFnsDateService,\n        factory: NbDateFnsDateService.ɵfac\n      });\n    }\n  }\n  return NbDateFnsDateService;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/*\n * @license\n * Copyright Akveo. All Rights Reserved.\n * Licensed under the MIT License. See License.txt in the project root for license information.\n */\nconst dateFnsServiceProvider = {\n  provide: NbDateService,\n  useClass: NbDateFnsDateService\n};\nlet NbDateFnsDateModule = /*#__PURE__*/(() => {\n  class NbDateFnsDateModule {\n    static forRoot(options) {\n      return {\n        ngModule: NbDateFnsDateModule,\n        providers: [dateFnsServiceProvider, {\n          provide: NB_DATE_SERVICE_OPTIONS,\n          useValue: options\n        }]\n      };\n    }\n    static forChild(options) {\n      return {\n        ngModule: NbDateFnsDateModule,\n        providers: [dateFnsServiceProvider, {\n          provide: NB_DATE_SERVICE_OPTIONS,\n          useValue: options\n        }]\n      };\n    }\n    static {\n      this.ɵfac = function NbDateFnsDateModule_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || NbDateFnsDateModule)();\n      };\n    }\n    static {\n      this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n        type: NbDateFnsDateModule\n      });\n    }\n    static {\n      this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n        providers: [dateFnsServiceProvider]\n      });\n    }\n  }\n  return NbDateFnsDateModule;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/*\n * @license\n * Copyright Akveo. All Rights Reserved.\n * Licensed under the MIT License. See License.txt in the project root for license information.\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { NbDateFnsDateModule, NbDateFnsDateService };\n//# sourceMappingURL=nebular-date-fns.mjs.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}