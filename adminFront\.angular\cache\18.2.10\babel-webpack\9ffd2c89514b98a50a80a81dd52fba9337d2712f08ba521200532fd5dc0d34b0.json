{"ast": null, "code": "/* tslint:disable */\n/* eslint-disable */\n/* Code generated by ng-openapi-gen DO NOT EDIT. */\nimport { HttpResponse } from '@angular/common/http';\nimport { filter, map } from 'rxjs/operators';\nimport { RequestBuilder } from '../../request-builder';\nexport function apiUserGetMenuPost$Json(http, rootUrl, params, context) {\n  const rb = new RequestBuilder(rootUrl, apiUserGetMenuPost$Json.PATH, 'post');\n  if (params) {\n    rb.body(params.body, 'application/*+json');\n  }\n  return http.request(rb.build({\n    responseType: 'json',\n    accept: 'text/json',\n    context\n  })).pipe(filter(r => r instanceof HttpResponse), map(r => {\n    return r;\n  }));\n}\napiUserGetMenuPost$Json.PATH = '/api/User/GetMenu';", "map": {"version": 3, "names": ["HttpResponse", "filter", "map", "RequestBuilder", "apiUserGetMenuPost$Json", "http", "rootUrl", "params", "context", "rb", "PATH", "body", "request", "build", "responseType", "accept", "pipe", "r"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\services\\api\\fn\\user\\api-user-get-menu-post-json.ts"], "sourcesContent": ["/* tslint:disable */\r\n/* eslint-disable */\r\n/* Code generated by ng-openapi-gen DO NOT EDIT. */\r\n\r\nimport { HttpClient, HttpContext, HttpResponse } from '@angular/common/http';\r\nimport { Observable } from 'rxjs';\r\nimport { filter, map } from 'rxjs/operators';\r\nimport { StrictHttpResponse } from '../../strict-http-response';\r\nimport { RequestBuilder } from '../../request-builder';\r\n\r\nimport { GetMenuArgs } from '../../models/get-menu-args';\r\nimport { GetMenuResponseResponseBase } from '../../models/get-menu-response-response-base';\r\n\r\nexport interface ApiUserGetMenuPost$Json$Params {\r\n      body?: GetMenuArgs\r\n}\r\n\r\nexport function apiUserGetMenuPost$Json(http: HttpClient, rootUrl: string, params?: ApiUserGetMenuPost$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<GetMenuResponseResponseBase>> {\r\n  const rb = new RequestBuilder(rootUrl, apiUserGetMenuPost$Json.PATH, 'post');\r\n  if (params) {\r\n    rb.body(params.body, 'application/*+json');\r\n  }\r\n\r\n  return http.request(\r\n    rb.build({ responseType: 'json', accept: 'text/json', context })\r\n  ).pipe(\r\n    filter((r: any): r is HttpResponse<any> => r instanceof HttpResponse),\r\n    map((r: HttpResponse<any>) => {\r\n      return r as StrictHttpResponse<GetMenuResponseResponseBase>;\r\n    })\r\n  );\r\n}\r\n\r\napiUserGetMenuPost$Json.PATH = '/api/User/GetMenu';\r\n"], "mappings": "AAAA;AACA;AACA;AAEA,SAAkCA,YAAY,QAAQ,sBAAsB;AAE5E,SAASC,MAAM,EAAEC,GAAG,QAAQ,gBAAgB;AAE5C,SAASC,cAAc,QAAQ,uBAAuB;AAStD,OAAM,SAAUC,uBAAuBA,CAACC,IAAgB,EAAEC,OAAe,EAAEC,MAAuC,EAAEC,OAAqB;EACvI,MAAMC,EAAE,GAAG,IAAIN,cAAc,CAACG,OAAO,EAAEF,uBAAuB,CAACM,IAAI,EAAE,MAAM,CAAC;EAC5E,IAAIH,MAAM,EAAE;IACVE,EAAE,CAACE,IAAI,CAACJ,MAAM,CAACI,IAAI,EAAE,oBAAoB,CAAC;EAC5C;EAEA,OAAON,IAAI,CAACO,OAAO,CACjBH,EAAE,CAACI,KAAK,CAAC;IAAEC,YAAY,EAAE,MAAM;IAAEC,MAAM,EAAE,WAAW;IAAEP;EAAO,CAAE,CAAC,CACjE,CAACQ,IAAI,CACJf,MAAM,CAAEgB,CAAM,IAA6BA,CAAC,YAAYjB,YAAY,CAAC,EACrEE,GAAG,CAAEe,CAAoB,IAAI;IAC3B,OAAOA,CAAoD;EAC7D,CAAC,CAAC,CACH;AACH;AAEAb,uBAAuB,CAACM,IAAI,GAAG,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}