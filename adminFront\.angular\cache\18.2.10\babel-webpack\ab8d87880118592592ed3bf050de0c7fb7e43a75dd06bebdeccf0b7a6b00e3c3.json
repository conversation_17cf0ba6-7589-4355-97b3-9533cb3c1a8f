{"ast": null, "code": "import toInteger from \"../_lib/toInteger/index.js\";\nimport toDate from \"../toDate/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name setDate\n * @category Day Helpers\n * @summary Set the day of the month to the given date.\n *\n * @description\n * Set the day of the month to the given date.\n *\n * @param {Date|Number} date - the date to be changed\n * @param {Number} dayOfMonth - the day of the month of the new date\n * @returns {Date} the new date with the day of the month set\n * @throws {TypeError} 2 arguments required\n *\n * @example\n * // Set the 30th day of the month to 1 September 2014:\n * const result = setDate(new Date(2014, 8, 1), 30)\n * //=> Tue Sep 30 2014 00:00:00\n */\nexport default function setDate(dirtyDate, dirtyDayOfMonth) {\n  requiredArgs(2, arguments);\n  var date = toDate(dirtyDate);\n  var dayOfMonth = toInteger(dirtyDayOfMonth);\n  date.setDate(dayOfMonth);\n  return date;\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}