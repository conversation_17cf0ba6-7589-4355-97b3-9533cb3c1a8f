{"ast": null, "code": "export default function buildMatchPatternFn(args) {\n  return function (string) {\n    var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    var matchResult = string.match(args.matchPattern);\n    if (!matchResult) return null;\n    var matchedString = matchResult[0];\n    var parseResult = string.match(args.parsePattern);\n    if (!parseResult) return null;\n    var value = args.valueCallback ? args.valueCallback(parseResult[0]) : parseResult[0];\n    value = options.valueCallback ? options.valueCallback(value) : value;\n    var rest = string.slice(matchedString.length);\n    return {\n      value: value,\n      rest: rest\n    };\n  };\n}", "map": {"version": 3, "names": ["buildMatchPatternFn", "args", "string", "options", "arguments", "length", "undefined", "matchResult", "match", "matchPattern", "matchedString", "parseResult", "parsePattern", "value", "valueCallback", "rest", "slice"], "sources": ["C:/Users/<USER>/Documents/salechange-product/adminFront/node_modules/date-fns/esm/locale/_lib/buildMatchPatternFn/index.js"], "sourcesContent": ["export default function buildMatchPatternFn(args) {\n  return function (string) {\n    var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    var matchResult = string.match(args.matchPattern);\n    if (!matchResult) return null;\n    var matchedString = matchResult[0];\n    var parseResult = string.match(args.parsePattern);\n    if (!parseResult) return null;\n    var value = args.valueCallback ? args.valueCallback(parseResult[0]) : parseResult[0];\n    value = options.valueCallback ? options.valueCallback(value) : value;\n    var rest = string.slice(matchedString.length);\n    return {\n      value: value,\n      rest: rest\n    };\n  };\n}"], "mappings": "AAAA,eAAe,SAASA,mBAAmBA,CAACC,IAAI,EAAE;EAChD,OAAO,UAAUC,MAAM,EAAE;IACvB,IAAIC,OAAO,GAAGC,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;IACpF,IAAIG,WAAW,GAAGL,MAAM,CAACM,KAAK,CAACP,IAAI,CAACQ,YAAY,CAAC;IACjD,IAAI,CAACF,WAAW,EAAE,OAAO,IAAI;IAC7B,IAAIG,aAAa,GAAGH,WAAW,CAAC,CAAC,CAAC;IAClC,IAAII,WAAW,GAAGT,MAAM,CAACM,KAAK,CAACP,IAAI,CAACW,YAAY,CAAC;IACjD,IAAI,CAACD,WAAW,EAAE,OAAO,IAAI;IAC7B,IAAIE,KAAK,GAAGZ,IAAI,CAACa,aAAa,GAAGb,IAAI,CAACa,aAAa,CAACH,WAAW,CAAC,CAAC,CAAC,CAAC,GAAGA,WAAW,CAAC,CAAC,CAAC;IACpFE,KAAK,GAAGV,OAAO,CAACW,aAAa,GAAGX,OAAO,CAACW,aAAa,CAACD,KAAK,CAAC,GAAGA,KAAK;IACpE,IAAIE,IAAI,GAAGb,MAAM,CAACc,KAAK,CAACN,aAAa,CAACL,MAAM,CAAC;IAC7C,OAAO;MACLQ,KAAK,EAAEA,KAAK;MACZE,IAAI,EAAEA;IACR,CAAC;EACH,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}