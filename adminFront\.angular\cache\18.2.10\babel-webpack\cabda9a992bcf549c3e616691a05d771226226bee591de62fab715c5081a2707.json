{"ast": null, "code": "import { of as observableOf } from 'rxjs';\nimport { TemperatureHumidityData } from '../data/temperature-humidity';\nimport * as i0 from \"@angular/core\";\nexport let TemperatureHumidityService = /*#__PURE__*/(() => {\n  class TemperatureHumidityService extends TemperatureHumidityData {\n    constructor() {\n      super(...arguments);\n      this.temperatureDate = {\n        value: 24,\n        min: 12,\n        max: 30\n      };\n      this.humidityDate = {\n        value: 87,\n        min: 0,\n        max: 100\n      };\n    }\n    getTemperatureData() {\n      return observableOf(this.temperatureDate);\n    }\n    getHumidityData() {\n      return observableOf(this.humidityDate);\n    }\n    static {\n      this.ɵfac = /*@__PURE__*/(() => {\n        let ɵTemperatureHumidityService_BaseFactory;\n        return function TemperatureHumidityService_Factory(__ngFactoryType__) {\n          return (ɵTemperatureHumidityService_BaseFactory || (ɵTemperatureHumidityService_BaseFactory = i0.ɵɵgetInheritedFactory(TemperatureHumidityService)))(__ngFactoryType__ || TemperatureHumidityService);\n        };\n      })();\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: TemperatureHumidityService,\n        factory: TemperatureHumidityService.ɵfac\n      });\n    }\n  }\n  return TemperatureHumidityService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}