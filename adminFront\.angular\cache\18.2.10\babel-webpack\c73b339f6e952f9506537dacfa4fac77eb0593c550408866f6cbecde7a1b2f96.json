{"ast": null, "code": ";\n(function (root, factory) {\n  if (typeof exports === \"object\") {\n    // CommonJS\n    module.exports = exports = factory(require(\"./core\"));\n  } else if (typeof define === \"function\" && define.amd) {\n    // AMD\n    define([\"./core\"], factory);\n  } else {\n    // Global (browser)\n    factory(root.CryptoJS);\n  }\n})(this, function (CryptoJS) {\n  (function () {\n    // Shortcuts\n    var C = CryptoJS;\n    var C_lib = C.lib;\n    var WordArray = C_lib.WordArray;\n    var C_enc = C.enc;\n\n    /**\n     * Base64 encoding strategy.\n     */\n    var Base64 = C_enc.Base64 = {\n      /**\n       * Converts a word array to a Base64 string.\n       *\n       * @param {WordArray} wordArray The word array.\n       *\n       * @return {string} The Base64 string.\n       *\n       * @static\n       *\n       * @example\n       *\n       *     var base64String = CryptoJS.enc.Base64.stringify(wordArray);\n       */\n      stringify: function (wordArray) {\n        // Shortcuts\n        var words = wordArray.words;\n        var sigBytes = wordArray.sigBytes;\n        var map = this._map;\n\n        // Clamp excess bits\n        wordArray.clamp();\n\n        // Convert\n        var base64Chars = [];\n        for (var i = 0; i < sigBytes; i += 3) {\n          var byte1 = words[i >>> 2] >>> 24 - i % 4 * 8 & 0xff;\n          var byte2 = words[i + 1 >>> 2] >>> 24 - (i + 1) % 4 * 8 & 0xff;\n          var byte3 = words[i + 2 >>> 2] >>> 24 - (i + 2) % 4 * 8 & 0xff;\n          var triplet = byte1 << 16 | byte2 << 8 | byte3;\n          for (var j = 0; j < 4 && i + j * 0.75 < sigBytes; j++) {\n            base64Chars.push(map.charAt(triplet >>> 6 * (3 - j) & 0x3f));\n          }\n        }\n\n        // Add padding\n        var paddingChar = map.charAt(64);\n        if (paddingChar) {\n          while (base64Chars.length % 4) {\n            base64Chars.push(paddingChar);\n          }\n        }\n        return base64Chars.join('');\n      },\n      /**\n       * Converts a Base64 string to a word array.\n       *\n       * @param {string} base64Str The Base64 string.\n       *\n       * @return {WordArray} The word array.\n       *\n       * @static\n       *\n       * @example\n       *\n       *     var wordArray = CryptoJS.enc.Base64.parse(base64String);\n       */\n      parse: function (base64Str) {\n        // Shortcuts\n        var base64StrLength = base64Str.length;\n        var map = this._map;\n        var reverseMap = this._reverseMap;\n        if (!reverseMap) {\n          reverseMap = this._reverseMap = [];\n          for (var j = 0; j < map.length; j++) {\n            reverseMap[map.charCodeAt(j)] = j;\n          }\n        }\n\n        // Ignore padding\n        var paddingChar = map.charAt(64);\n        if (paddingChar) {\n          var paddingIndex = base64Str.indexOf(paddingChar);\n          if (paddingIndex !== -1) {\n            base64StrLength = paddingIndex;\n          }\n        }\n\n        // Convert\n        return parseLoop(base64Str, base64StrLength, reverseMap);\n      },\n      _map: 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/='\n    };\n    function parseLoop(base64Str, base64StrLength, reverseMap) {\n      var words = [];\n      var nBytes = 0;\n      for (var i = 0; i < base64StrLength; i++) {\n        if (i % 4) {\n          var bits1 = reverseMap[base64Str.charCodeAt(i - 1)] << i % 4 * 2;\n          var bits2 = reverseMap[base64Str.charCodeAt(i)] >>> 6 - i % 4 * 2;\n          var bitsCombined = bits1 | bits2;\n          words[nBytes >>> 2] |= bitsCombined << 24 - nBytes % 4 * 8;\n          nBytes++;\n        }\n      }\n      return WordArray.create(words, nBytes);\n    }\n  })();\n  return CryptoJS.enc.Base64;\n});", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}