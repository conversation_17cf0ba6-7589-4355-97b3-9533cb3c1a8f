{"ast": null, "code": "/* tslint:disable */\n/* eslint-disable */\nimport { HttpResponse } from '@angular/common/http';\nimport { filter, map } from 'rxjs/operators';\nimport { RequestBuilder } from '../../request-builder';\nexport function apiUserRemoveUserPost$Plain(http, rootUrl, params, context) {\n  const rb = new RequestBuilder(rootUrl, apiUserRemoveUserPost$Plain.PATH, 'post');\n  if (params) {\n    rb.body(params.body, 'application/*+json');\n  }\n  return http.request(rb.build({\n    responseType: 'text',\n    accept: 'text/plain',\n    context\n  })).pipe(filter(r => r instanceof HttpResponse), map(r => {\n    return r;\n  }));\n}\napiUserRemoveUserPost$Plain.PATH = '/api/User/RemoveUser';", "map": {"version": 3, "names": ["HttpResponse", "filter", "map", "RequestBuilder", "apiUserRemoveUserPost$Plain", "http", "rootUrl", "params", "context", "rb", "PATH", "body", "request", "build", "responseType", "accept", "pipe", "r"], "sources": ["C:\\Users\\<USER>\\Documents\\jeansalechange\\adminFront\\src\\services\\api\\fn\\user\\api-user-remove-user-post-plain.ts"], "sourcesContent": ["/* tslint:disable */\r\n/* eslint-disable */\r\nimport { HttpClient, HttpContext, HttpResponse } from '@angular/common/http';\r\nimport { Observable } from 'rxjs';\r\nimport { filter, map } from 'rxjs/operators';\r\nimport { StrictHttpResponse } from '../../strict-http-response';\r\nimport { RequestBuilder } from '../../request-builder';\r\n\r\nimport { UserRemoveDataArgs } from '../../models/user-remove-data-args';\r\nimport { UserRemoveDataResponseResponseBase } from '../../models/user-remove-data-response-response-base';\r\n\r\nexport interface ApiUserRemoveUserPost$Plain$Params {\r\n      body?: UserRemoveDataArgs\r\n}\r\n\r\nexport function apiUserRemoveUserPost$Plain(http: HttpClient, rootUrl: string, params?: ApiUserRemoveUserPost$Plain$Params, context?: HttpContext): Observable<StrictHttpResponse<UserRemoveDataResponseResponseBase>> {\r\n  const rb = new RequestBuilder(rootUrl, apiUserRemoveUserPost$Plain.PATH, 'post');\r\n  if (params) {\r\n    rb.body(params.body, 'application/*+json');\r\n  }\r\n\r\n  return http.request(\r\n    rb.build({ responseType: 'text', accept: 'text/plain', context })\r\n  ).pipe(\r\n    filter((r: any): r is HttpResponse<any> => r instanceof HttpResponse),\r\n    map((r: HttpResponse<any>) => {\r\n      return r as StrictHttpResponse<UserRemoveDataResponseResponseBase>;\r\n    })\r\n  );\r\n}\r\n\r\napiUserRemoveUserPost$Plain.PATH = '/api/User/RemoveUser';\r\n"], "mappings": "AAAA;AACA;AACA,SAAkCA,YAAY,QAAQ,sBAAsB;AAE5E,SAASC,MAAM,EAAEC,GAAG,QAAQ,gBAAgB;AAE5C,SAASC,cAAc,QAAQ,uBAAuB;AAStD,OAAM,SAAUC,2BAA2BA,CAACC,IAAgB,EAAEC,OAAe,EAAEC,MAA2C,EAAEC,OAAqB;EAC/I,MAAMC,EAAE,GAAG,IAAIN,cAAc,CAACG,OAAO,EAAEF,2BAA2B,CAACM,IAAI,EAAE,MAAM,CAAC;EAChF,IAAIH,MAAM,EAAE;IACVE,EAAE,CAACE,IAAI,CAACJ,MAAM,CAACI,IAAI,EAAE,oBAAoB,CAAC;EAC5C;EAEA,OAAON,IAAI,CAACO,OAAO,CACjBH,EAAE,CAACI,KAAK,CAAC;IAAEC,YAAY,EAAE,MAAM;IAAEC,MAAM,EAAE,YAAY;IAAEP;EAAO,CAAE,CAAC,CAClE,CAACQ,IAAI,CACJf,MAAM,CAAEgB,CAAM,IAA6BA,CAAC,YAAYjB,YAAY,CAAC,EACrEE,GAAG,CAAEe,CAAoB,IAAI;IAC3B,OAAOA,CAA2D;EACpE,CAAC,CAAC,CACH;AACH;AAEAb,2BAA2B,CAACM,IAAI,GAAG,sBAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}