{"ast": null, "code": "/* tslint:disable */\n/* eslint-disable */\nimport { HttpResponse } from '@angular/common/http';\nimport { filter, map } from 'rxjs/operators';\nimport { RequestBuilder } from '../../request-builder';\nexport function apiBuildCaseGetHouseAndFloorByBuildCaseIdPost$Plain(http, rootUrl, params, context) {\n  const rb = new RequestBuilder(rootUrl, apiBuildCaseGetHouseAndFloorByBuildCaseIdPost$Plain.PATH, 'post');\n  if (params) {\n    rb.query('buildCaseId', params.buildCaseId, {});\n  }\n  return http.request(rb.build({\n    responseType: 'text',\n    accept: 'text/plain',\n    context\n  })).pipe(filter(r => r instanceof HttpResponse), map(r => {\n    return r;\n  }));\n}\napiBuildCaseGetHouseAndFloorByBuildCaseIdPost$Plain.PATH = '/api/BuildCase/GetHouseAndFloorByBuildCaseId';", "map": {"version": 3, "names": ["HttpResponse", "filter", "map", "RequestBuilder", "apiBuildCaseGetHouseAndFloorByBuildCaseIdPost$Plain", "http", "rootUrl", "params", "context", "rb", "PATH", "query", "buildCaseId", "request", "build", "responseType", "accept", "pipe", "r"], "sources": ["C:\\Users\\<USER>\\Documents\\jeansalechange\\adminFront\\src\\services\\api\\fn\\build-case\\api-build-case-get-house-and-floor-by-build-case-id-post-plain.ts"], "sourcesContent": ["/* tslint:disable */\r\n/* eslint-disable */\r\nimport { HttpClient, HttpContext, HttpResponse } from '@angular/common/http';\r\nimport { Observable } from 'rxjs';\r\nimport { filter, map } from 'rxjs/operators';\r\nimport { StrictHttpResponse } from '../../strict-http-response';\r\nimport { RequestBuilder } from '../../request-builder';\r\n\r\nimport { GetHouseAndFloorByBuildCaseIdResListResponseBase } from '../../models/get-house-and-floor-by-build-case-id-res-list-response-base';\r\n\r\nexport interface ApiBuildCaseGetHouseAndFloorByBuildCaseIdPost$Plain$Params {\r\n  buildCaseId?: number;\r\n}\r\n\r\nexport function apiBuildCaseGetHouseAndFloorByBuildCaseIdPost$Plain(http: HttpClient, rootUrl: string, params?: ApiBuildCaseGetHouseAndFloorByBuildCaseIdPost$Plain$Params, context?: HttpContext): Observable<StrictHttpResponse<GetHouseAndFloorByBuildCaseIdResListResponseBase>> {\r\n  const rb = new RequestBuilder(rootUrl, apiBuildCaseGetHouseAndFloorByBuildCaseIdPost$Plain.PATH, 'post');\r\n  if (params) {\r\n    rb.query('buildCaseId', params.buildCaseId, {});\r\n  }\r\n\r\n  return http.request(\r\n    rb.build({ responseType: 'text', accept: 'text/plain', context })\r\n  ).pipe(\r\n    filter((r: any): r is HttpResponse<any> => r instanceof HttpResponse),\r\n    map((r: HttpResponse<any>) => {\r\n      return r as StrictHttpResponse<GetHouseAndFloorByBuildCaseIdResListResponseBase>;\r\n    })\r\n  );\r\n}\r\n\r\napiBuildCaseGetHouseAndFloorByBuildCaseIdPost$Plain.PATH = '/api/BuildCase/GetHouseAndFloorByBuildCaseId';\r\n"], "mappings": "AAAA;AACA;AACA,SAAkCA,YAAY,QAAQ,sBAAsB;AAE5E,SAASC,MAAM,EAAEC,GAAG,QAAQ,gBAAgB;AAE5C,SAASC,cAAc,QAAQ,uBAAuB;AAQtD,OAAM,SAAUC,mDAAmDA,CAACC,IAAgB,EAAEC,OAAe,EAAEC,MAAmE,EAAEC,OAAqB;EAC/L,MAAMC,EAAE,GAAG,IAAIN,cAAc,CAACG,OAAO,EAAEF,mDAAmD,CAACM,IAAI,EAAE,MAAM,CAAC;EACxG,IAAIH,MAAM,EAAE;IACVE,EAAE,CAACE,KAAK,CAAC,aAAa,EAAEJ,MAAM,CAACK,WAAW,EAAE,EAAE,CAAC;EACjD;EAEA,OAAOP,IAAI,CAACQ,OAAO,CACjBJ,EAAE,CAACK,KAAK,CAAC;IAAEC,YAAY,EAAE,MAAM;IAAEC,MAAM,EAAE,YAAY;IAAER;EAAO,CAAE,CAAC,CAClE,CAACS,IAAI,CACJhB,MAAM,CAAEiB,CAAM,IAA6BA,CAAC,YAAYlB,YAAY,CAAC,EACrEE,GAAG,CAAEgB,CAAoB,IAAI;IAC3B,OAAOA,CAAyE;EAClF,CAAC,CAAC,CACH;AACH;AAEAd,mDAAmD,CAACM,IAAI,GAAG,8CAA8C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}