{"ast": null, "code": "import _classCallCheck from \"@babel/runtime/helpers/esm/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/esm/createClass\";\nimport _assertThisInitialized from \"@babel/runtime/helpers/esm/assertThisInitialized\";\nimport _inherits from \"@babel/runtime/helpers/esm/inherits\";\nimport _createSuper from \"@babel/runtime/helpers/esm/createSuper\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport { Parser } from \"../Parser.js\";\nimport { numericPatterns } from \"../constants.js\";\nimport { parseNumericPattern, parseNDigits } from \"../utils.js\";\nimport setUTCISOWeek from \"../../../_lib/setUTCISOWeek/index.js\";\nimport startOfUTCISOWeek from \"../../../_lib/startOfUTCISOWeek/index.js\"; // ISO week of year\nexport var ISOWeekParser = /*#__PURE__*/function (_Parser) {\n  _inherits(ISOWeekParser, _Parser);\n  var _super = _createSuper(ISOWeekParser);\n  function ISOWeekParser() {\n    var _this;\n    _classCallCheck(this, ISOWeekParser);\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    _this = _super.call.apply(_super, [this].concat(args));\n    _defineProperty(_assertThisInitialized(_this), \"priority\", 100);\n    _defineProperty(_assertThisInitialized(_this), \"incompatibleTokens\", ['y', 'Y', 'u', 'q', 'Q', 'M', 'L', 'w', 'd', 'D', 'e', 'c', 't', 'T']);\n    return _this;\n  }\n  _createClass(ISOWeekParser, [{\n    key: \"parse\",\n    value: function parse(dateString, token, match) {\n      switch (token) {\n        case 'I':\n          return parseNumericPattern(numericPatterns.week, dateString);\n        case 'Io':\n          return match.ordinalNumber(dateString, {\n            unit: 'week'\n          });\n        default:\n          return parseNDigits(token.length, dateString);\n      }\n    }\n  }, {\n    key: \"validate\",\n    value: function validate(_date, value) {\n      return value >= 1 && value <= 53;\n    }\n  }, {\n    key: \"set\",\n    value: function set(date, _flags, value) {\n      return startOfUTCISOWeek(setUTCISOWeek(date, value));\n    }\n  }]);\n  return ISOWeekParser;\n}(Parser);", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}