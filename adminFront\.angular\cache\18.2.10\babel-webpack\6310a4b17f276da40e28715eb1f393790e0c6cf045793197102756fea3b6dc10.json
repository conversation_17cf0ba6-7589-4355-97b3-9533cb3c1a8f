{"ast": null, "code": "import { importProvidersFrom } from '@angular/core';\nimport { provideRouter } from '@angular/router';\nimport { routes } from './app-routing.module';\nimport { provideNoopAnimations } from '@angular/platform-browser/animations';\nimport { HTTP_INTERCEPTORS, provideHttpClient, withInterceptorsFromDi } from '@angular/common/http';\nimport { ApiModule } from '../services/api/api.module';\nimport { environment } from '../environments/environment';\nimport { TokenInterceptor } from './shared/auth/token.interceptor';\nexport const appConfig = {\n  providers: [provideRouter(routes), provideHttpClient(withInterceptorsFromDi()), {\n    provide: HTTP_INTERCEPTORS,\n    useClass: TokenInterceptor,\n    multi: true\n  }, importProvidersFrom(ApiModule.forRoot({\n    rootUrl: environment.BASE_URL_API\n  })), provideNoopAnimations()]\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}