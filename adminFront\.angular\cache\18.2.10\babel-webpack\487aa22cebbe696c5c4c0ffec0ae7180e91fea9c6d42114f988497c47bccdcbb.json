{"ast": null, "code": "import _classCallCheck from \"@babel/runtime/helpers/esm/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/esm/createClass\";\nimport _assertThisInitialized from \"@babel/runtime/helpers/esm/assertThisInitialized\";\nimport _inherits from \"@babel/runtime/helpers/esm/inherits\";\nimport _createSuper from \"@babel/runtime/helpers/esm/createSuper\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport { Parser } from \"../Parser.js\";\nimport { mapValue, normalizeTwoDigitYear, parseNDigits } from \"../utils.js\";\n// From http://www.unicode.org/reports/tr35/tr35-31/tr35-dates.html#Date_Format_Patterns\n// | Year     |     y | yy |   yyy |  yyyy | yyyyy |\n// |----------|-------|----|-------|-------|-------|\n// | AD 1     |     1 | 01 |   001 |  0001 | 00001 |\n// | AD 12    |    12 | 12 |   012 |  0012 | 00012 |\n// | AD 123   |   123 | 23 |   123 |  0123 | 00123 |\n// | AD 1234  |  1234 | 34 |  1234 |  1234 | 01234 |\n// | AD 12345 | 12345 | 45 | 12345 | 12345 | 12345 |\nexport var YearParser = /*#__PURE__*/function (_Parser) {\n  _inherits(YearParser, _Parser);\n  var _super = _createSuper(YearParser);\n  function YearParser() {\n    var _this;\n    _classCallCheck(this, YearParser);\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    _this = _super.call.apply(_super, [this].concat(args));\n    _defineProperty(_assertThisInitialized(_this), \"priority\", 130);\n    _defineProperty(_assertThisInitialized(_this), \"incompatibleTokens\", ['Y', 'R', 'u', 'w', 'I', 'i', 'e', 'c', 't', 'T']);\n    return _this;\n  }\n  _createClass(YearParser, [{\n    key: \"parse\",\n    value: function parse(dateString, token, match) {\n      var valueCallback = function valueCallback(year) {\n        return {\n          year: year,\n          isTwoDigitYear: token === 'yy'\n        };\n      };\n      switch (token) {\n        case 'y':\n          return mapValue(parseNDigits(4, dateString), valueCallback);\n        case 'yo':\n          return mapValue(match.ordinalNumber(dateString, {\n            unit: 'year'\n          }), valueCallback);\n        default:\n          return mapValue(parseNDigits(token.length, dateString), valueCallback);\n      }\n    }\n  }, {\n    key: \"validate\",\n    value: function validate(_date, value) {\n      return value.isTwoDigitYear || value.year > 0;\n    }\n  }, {\n    key: \"set\",\n    value: function set(date, flags, value) {\n      var currentYear = date.getUTCFullYear();\n      if (value.isTwoDigitYear) {\n        var normalizedTwoDigitYear = normalizeTwoDigitYear(value.year, currentYear);\n        date.setUTCFullYear(normalizedTwoDigitYear, 0, 1);\n        date.setUTCHours(0, 0, 0, 0);\n        return date;\n      }\n      var year = !('era' in flags) || flags.era === 1 ? value.year : 1 - value.year;\n      date.setUTCFullYear(year, 0, 1);\n      date.setUTCHours(0, 0, 0, 0);\n      return date;\n    }\n  }]);\n  return YearParser;\n}(Parser);", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}