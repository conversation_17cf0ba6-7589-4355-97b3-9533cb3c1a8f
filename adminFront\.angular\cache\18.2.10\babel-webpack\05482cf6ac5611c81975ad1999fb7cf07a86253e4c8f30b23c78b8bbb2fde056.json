{"ast": null, "code": "import { LogsManagementComponent } from './logs-management/logs-management.component';\nimport { UserManagementComponent } from './user-management/user-management.component';\nimport { RouterModule } from '@angular/router';\nimport { RolePermissionsComponent } from './role-permissions/role-permissions.component';\nimport { ApproveWaitingComponent } from '../approve-waiting/approve-waiting.component';\nimport { DetailApprovalWaitingComponent } from '../approve-waiting/detail-approval-waiting/detail-approval-waiting.component';\nimport { CategoryManagementComponent } from '../category-management/category-management.component';\nimport { ReviewDocumentManagementComponent } from '../construction-project-management/notice-management/review-document-management/review-document-management.component';\nimport { NotificationSettingComponent } from './notification-setting/notification-setting.component';\nimport { ProjectManagementComponent } from '../construction-project-management/project-management/project-management.component';\nimport { SettingTimePeriodComponent } from '../selection-management/setting-time-period/setting-time-period.component';\nimport { EditSettingTimePeriodComponent } from '../selection-management/setting-time-period/edit-setting-time-period/edit-setting-time-period.component';\nimport { BuildingMaterialComponent } from '../selection-management/building-material/building-material.component';\nimport { ContentManagementLandownerComponent } from '../selection-management/content-management-landowner/content-management-landowner.component';\nimport { DetailContentManagementLandownerComponent } from '../selection-management/content-management-landowner/detail-content-management-landowner/detail-content-management-landowner.component';\nimport { ContentManagementSalesAccountComponent } from '../selection-management/content-management-sales-account/content-management-sales-account.component';\nimport { DetailContentManagementSalesAccountComponent } from '../selection-management/content-management-sales-account/detail-content-management-sales-account/detail-content-management-sales-account.component';\nimport { SchematicPictureComponent } from '../selection-management/schematic-picture/schematic-picture.component';\nimport { PictureMaterialComponent } from '../selection-management/picture-material/picture-material.component';\nimport { AvailableTimeSlotComponent } from '../reservation-time-management/available-time-slot/available-time-slot.component';\nimport { EditAvailableTimeSlotComponent } from '../reservation-time-management/available-time-slot/edit-available-time-slot/edit-available-time-slot.component';\nimport { CalendarComponent } from '../reservation-time-management/calendar/calendar.component';\nimport { PreOrderComponent } from '../reservation-time-management/pre-order/pre-order.component';\nimport { RelatedDocumentsComponent } from '../construction-project-management/related-documents/related-documents.component';\nimport { NoticeManagementComponent } from '../construction-project-management/notice-management/notice-management.component';\n// import { ReviewDocumentComponent } from '../construction-project-management/notice-management/review-document/review-document.component';\nimport { RegularNoticeComponent } from '../construction-project-management/notice-management/regular-notice/regular-notice.component';\nimport { RequirementManagementComponent } from '../requirement-management/requirement-management.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [\n//system module\n{\n  path: 'user-management',\n  component: UserManagementComponent\n}, {\n  path: 'notification-setting',\n  component: NotificationSettingComponent\n}, {\n  path: 'role-permissions',\n  component: RolePermissionsComponent\n}, {\n  path: 'logs-management',\n  component: LogsManagementComponent\n},\n//construction project module\n{\n  path: 'project-management',\n  component: ProjectManagementComponent\n}, {\n  path: 'review-document',\n  component: ReviewDocumentManagementComponent\n}, {\n  path: 'related-documents/:id',\n  component: RelatedDocumentsComponent\n}, {\n  path: 'notice-management',\n  component: NoticeManagementComponent\n}, {\n  path: 'notice-management/regular-notice',\n  component: RegularNoticeComponent\n}, {\n  path: 'requirement-management',\n  component: RequirementManagementComponent\n},\n//household module\n{\n  path: \"household-management\",\n  loadChildren: () => import('../household-management/household-management.module').then(mod => mod.HouseholdManagementModule)\n},\n//selection management\n{\n  path: 'setting-time-period',\n  component: SettingTimePeriodComponent\n}, {\n  path: 'setting-time-period/:id',\n  component: EditSettingTimePeriodComponent\n}, {\n  path: 'building-material',\n  component: BuildingMaterialComponent\n}, {\n  path: 'content-management-landowner',\n  component: ContentManagementLandownerComponent\n}, {\n  path: 'content-management-landowner/:id',\n  component: DetailContentManagementLandownerComponent\n}, {\n  path: 'content-management-sales-account',\n  component: ContentManagementSalesAccountComponent\n}, {\n  path: 'content-management-sales-account/:id',\n  component: DetailContentManagementSalesAccountComponent\n}, {\n  path: 'schematic-picture',\n  component: SchematicPictureComponent\n}, {\n  path: 'picture-material',\n  component: PictureMaterialComponent\n},\n//reservation management\n{\n  path: 'available-time-slot',\n  component: AvailableTimeSlotComponent\n}, {\n  path: 'available-time-slot/:id',\n  component: EditAvailableTimeSlotComponent\n}, {\n  path: 'approve-waiting',\n  component: ApproveWaitingComponent\n}, {\n  path: 'approve-waiting/:buildCaseId/:id',\n  component: DetailApprovalWaitingComponent\n}, {\n  path: 'calendar',\n  component: CalendarComponent\n}, {\n  path: 'preOder',\n  component: PreOrderComponent\n}, {\n  path: 'category-management',\n  component: CategoryManagementComponent\n}];\nexport let SystemManagementRoutingModule = /*#__PURE__*/(() => {\n  class SystemManagementRoutingModule {\n    static {\n      this.ɵfac = function SystemManagementRoutingModule_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || SystemManagementRoutingModule)();\n      };\n    }\n    static {\n      this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n        type: SystemManagementRoutingModule\n      });\n    }\n    static {\n      this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n        imports: [RouterModule.forChild(routes), RouterModule]\n      });\n    }\n  }\n  return SystemManagementRoutingModule;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}