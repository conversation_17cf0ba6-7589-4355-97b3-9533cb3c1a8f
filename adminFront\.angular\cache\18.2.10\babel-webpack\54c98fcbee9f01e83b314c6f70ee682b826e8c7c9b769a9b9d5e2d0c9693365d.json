{"ast": null, "code": "import { of as observableOf } from 'rxjs';\nimport { CountryOrderData } from '../data/country-order';\nimport * as i0 from \"@angular/core\";\nexport class CountryOrderService extends CountryOrderData {\n  constructor() {\n    super(...arguments);\n    this.countriesCategories = ['Sofas', 'Furniture', 'Lighting', 'Tables', 'Textiles'];\n    this.countriesCategoriesLength = this.countriesCategories.length;\n  }\n  generateRandomData(nPoints) {\n    return Array.from(Array(nPoints)).map(() => {\n      return Math.round(Math.random() * 20);\n    });\n  }\n  getCountriesCategories() {\n    return observableOf(this.countriesCategories);\n  }\n  getCountriesCategoriesData(country) {\n    return observableOf(this.generateRandomData(this.countriesCategoriesLength));\n  }\n  static {\n    this.ɵfac = /*@__PURE__*/(() => {\n      let ɵCountryOrderService_BaseFactory;\n      return function CountryOrderService_Factory(__ngFactoryType__) {\n        return (ɵCountryOrderService_BaseFactory || (ɵCountryOrderService_BaseFactory = i0.ɵɵgetInheritedFactory(CountryOrderService)))(__ngFactoryType__ || CountryOrderService);\n      };\n    })();\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: CountryOrderService,\n      factory: CountryOrderService.ɵfac\n    });\n  }\n}", "map": {"version": 3, "names": ["of", "observableOf", "CountryOrderData", "CountryOrderService", "constructor", "countriesCategories", "countriesCategoriesLength", "length", "generateRandomData", "nPoints", "Array", "from", "map", "Math", "round", "random", "getCountriesCategories", "getCountriesCategoriesData", "country", "__ngFactoryType__", "factory", "ɵfac"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\@core\\mock\\country-order.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport { of as observableOf, Observable } from 'rxjs';\r\nimport { CountryOrderData } from '../data/country-order';\r\n\r\n@Injectable()\r\nexport class CountryOrderService extends CountryOrderData {\r\n\r\n  private countriesCategories = [\r\n    'Sofas',\r\n    'Furniture',\r\n    'Lighting',\r\n    'Tables',\r\n    'Textiles',\r\n  ];\r\n  private countriesCategoriesLength = this.countriesCategories.length;\r\n  private generateRandomData(nPoints: number): number[] {\r\n    return Array.from(Array(nPoints)).map(() => {\r\n      return Math.round(Math.random() * 20);\r\n    });\r\n  }\r\n\r\n  getCountriesCategories(): Observable<string[]> {\r\n    return observableOf(this.countriesCategories);\r\n  }\r\n\r\n  getCountriesCategoriesData(country: string): Observable<number[]> {\r\n    return observableOf(this.generateRandomData(this.countriesCategoriesLength));\r\n  }\r\n}\r\n"], "mappings": "AACA,SAASA,EAAE,IAAIC,YAAY,QAAoB,MAAM;AACrD,SAASC,gBAAgB,QAAQ,uBAAuB;;AAGxD,OAAM,MAAOC,mBAAoB,SAAQD,gBAAgB;EADzDE,YAAA;;IAGU,KAAAC,mBAAmB,GAAG,CAC5B,OAAO,EACP,WAAW,EACX,UAAU,EACV,QAAQ,EACR,UAAU,CACX;IACO,KAAAC,yBAAyB,GAAG,IAAI,CAACD,mBAAmB,CAACE,MAAM;;EAC3DC,kBAAkBA,CAACC,OAAe;IACxC,OAAOC,KAAK,CAACC,IAAI,CAACD,KAAK,CAACD,OAAO,CAAC,CAAC,CAACG,GAAG,CAAC,MAAK;MACzC,OAAOC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,EAAE,GAAG,EAAE,CAAC;IACvC,CAAC,CAAC;EACJ;EAEAC,sBAAsBA,CAAA;IACpB,OAAOf,YAAY,CAAC,IAAI,CAACI,mBAAmB,CAAC;EAC/C;EAEAY,0BAA0BA,CAACC,OAAe;IACxC,OAAOjB,YAAY,CAAC,IAAI,CAACO,kBAAkB,CAAC,IAAI,CAACF,yBAAyB,CAAC,CAAC;EAC9E;;;;;iHAtBWH,mBAAmB,IAAAgB,iBAAA,IAAnBhB,mBAAmB;MAAA;IAAA;EAAA;;;aAAnBA,mBAAmB;MAAAiB,OAAA,EAAnBjB,mBAAmB,CAAAkB;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}