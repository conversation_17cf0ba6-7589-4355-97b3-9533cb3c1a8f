{"ast": null, "code": "import toInteger from \"../toInteger/index.js\";\nimport toDate from \"../../toDate/index.js\";\nimport getUTCISOWeek from \"../getUTCISOWeek/index.js\";\nimport requiredArgs from \"../requiredArgs/index.js\";\nexport default function setUTCISOWeek(dirtyDate, dirtyISOWeek) {\n  requiredArgs(2, arguments);\n  var date = toDate(dirtyDate);\n  var isoWeek = toInteger(dirtyISOWeek);\n  var diff = getUTCISOWeek(date) - isoWeek;\n  date.setUTCDate(date.getUTCDate() - diff * 7);\n  return date;\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}