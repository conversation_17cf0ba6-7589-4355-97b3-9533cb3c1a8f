{"ast": null, "code": "import addHours from \"../addHours/index.js\";\nimport toDate from \"../toDate/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name eachHourOfInterval\n * @category Interval Helpers\n * @summary Return the array of hours within the specified time interval.\n *\n * @description\n * Return the array of hours within the specified time interval.\n *\n * @param {Interval} interval - the interval. See [Interval]{@link https://date-fns.org/docs/Interval}\n * @param {Object} [options] - an object with options.\n * @param {Number} [options.step=1] - the step to increment by. The value should be more than 1.\n * @returns {Date[]} the array with starts of hours from the hour of the interval start to the hour of the interval end\n * @throws {TypeError} 1 argument required\n * @throws {RangeError} `options.step` must be a number greater than 1\n * @throws {RangeError} The start of an interval cannot be after its end\n * @throws {RangeError} Date in interval cannot be `Invalid Date`\n *\n * @example\n * // Each hour between 6 October 2014, 12:00 and 6 October 2014, 15:00\n * const result = eachHourOfInterval({\n *   start: new Date(2014, 9, 6, 12),\n *   end: new Date(2014, 9, 6, 15)\n * })\n * //=> [\n * //   Mon Oct 06 2014 12:00:00,\n * //   Mon Oct 06 2014 13:00:00,\n * //   Mon Oct 06 2014 14:00:00,\n * //   Mon Oct 06 2014 15:00:00\n * // ]\n */\nexport default function eachHourOfInterval(dirtyInterval, options) {\n  var _options$step;\n  requiredArgs(1, arguments);\n  var interval = dirtyInterval || {};\n  var startDate = toDate(interval.start);\n  var endDate = toDate(interval.end);\n  var startTime = startDate.getTime();\n  var endTime = endDate.getTime();\n\n  // Throw an exception if start date is after end date or if any date is `Invalid Date`\n  if (!(startTime <= endTime)) {\n    throw new RangeError('Invalid interval');\n  }\n  var dates = [];\n  var currentDate = startDate;\n  currentDate.setMinutes(0, 0, 0);\n  var step = Number((_options$step = options === null || options === void 0 ? void 0 : options.step) !== null && _options$step !== void 0 ? _options$step : 1);\n  if (step < 1 || isNaN(step)) throw new RangeError('`options.step` must be a number greater than 1');\n  while (currentDate.getTime() <= endTime) {\n    dates.push(toDate(currentDate));\n    currentDate = addHours(currentDate, step);\n  }\n  return dates;\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}