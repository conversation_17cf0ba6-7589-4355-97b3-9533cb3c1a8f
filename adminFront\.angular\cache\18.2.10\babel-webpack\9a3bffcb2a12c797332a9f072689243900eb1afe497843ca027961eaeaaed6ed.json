{"ast": null, "code": "/* tslint:disable */\n/* eslint-disable */\nimport { HttpResponse } from '@angular/common/http';\nimport { filter, map } from 'rxjs/operators';\nimport { RequestBuilder } from '../../request-builder';\nexport function apiRegularChangeItemGetBuildingSampleSelectionPost$Json(http, rootUrl, params, context) {\n  const rb = new RequestBuilder(rootUrl, apiRegularChangeItemGetBuildingSampleSelectionPost$Json.PATH, 'post');\n  if (params) {\n    rb.body(params.body, 'application/*+json');\n  }\n  return http.request(rb.build({\n    responseType: 'json',\n    accept: 'text/json',\n    context\n  })).pipe(filter(r => r instanceof HttpResponse), map(r => {\n    return r;\n  }));\n}\napiRegularChangeItemGetBuildingSampleSelectionPost$Json.PATH = '/api/RegularChangeItem/GetBuildingSampleSelection';", "map": {"version": 3, "names": ["HttpResponse", "filter", "map", "RequestBuilder", "apiRegularChangeItemGetBuildingSampleSelectionPost$Json", "http", "rootUrl", "params", "context", "rb", "PATH", "body", "request", "build", "responseType", "accept", "pipe", "r"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\services\\api\\fn\\regular-change-item\\api-regular-change-item-get-building-sample-selection-post-json.ts"], "sourcesContent": ["/* tslint:disable */\r\n/* eslint-disable */\r\nimport { HttpClient, HttpContext, HttpResponse } from '@angular/common/http';\r\nimport { Observable } from 'rxjs';\r\nimport { filter, map } from 'rxjs/operators';\r\nimport { StrictHttpResponse } from '../../strict-http-response';\r\nimport { RequestBuilder } from '../../request-builder';\r\n\r\nimport { GetBuildingSampleSelectionResResponseBase } from '../../models/get-building-sample-selection-res-response-base';\r\n\r\nexport interface ApiRegularChangeItemGetBuildingSampleSelectionPost$Json$Params {\r\n      body?: number\r\n}\r\n\r\nexport function apiRegularChangeItemGetBuildingSampleSelectionPost$Json(http: HttpClient, rootUrl: string, params?: ApiRegularChangeItemGetBuildingSampleSelectionPost$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<GetBuildingSampleSelectionResResponseBase>> {\r\n  const rb = new RequestBuilder(rootUrl, apiRegularChangeItemGetBuildingSampleSelectionPost$Json.PATH, 'post');\r\n  if (params) {\r\n    rb.body(params.body, 'application/*+json');\r\n  }\r\n\r\n  return http.request(\r\n    rb.build({ responseType: 'json', accept: 'text/json', context })\r\n  ).pipe(\r\n    filter((r: any): r is HttpResponse<any> => r instanceof HttpResponse),\r\n    map((r: HttpResponse<any>) => {\r\n      return r as StrictHttpResponse<GetBuildingSampleSelectionResResponseBase>;\r\n    })\r\n  );\r\n}\r\n\r\napiRegularChangeItemGetBuildingSampleSelectionPost$Json.PATH = '/api/RegularChangeItem/GetBuildingSampleSelection';\r\n"], "mappings": "AAAA;AACA;AACA,SAAkCA,YAAY,QAAQ,sBAAsB;AAE5E,SAASC,MAAM,EAAEC,GAAG,QAAQ,gBAAgB;AAE5C,SAASC,cAAc,QAAQ,uBAAuB;AAQtD,OAAM,SAAUC,uDAAuDA,CAACC,IAAgB,EAAEC,OAAe,EAAEC,MAAuE,EAAEC,OAAqB;EACvM,MAAMC,EAAE,GAAG,IAAIN,cAAc,CAACG,OAAO,EAAEF,uDAAuD,CAACM,IAAI,EAAE,MAAM,CAAC;EAC5G,IAAIH,MAAM,EAAE;IACVE,EAAE,CAACE,IAAI,CAACJ,MAAM,CAACI,IAAI,EAAE,oBAAoB,CAAC;EAC5C;EAEA,OAAON,IAAI,CAACO,OAAO,CACjBH,EAAE,CAACI,KAAK,CAAC;IAAEC,YAAY,EAAE,MAAM;IAAEC,MAAM,EAAE,WAAW;IAAEP;EAAO,CAAE,CAAC,CACjE,CAACQ,IAAI,CACJf,MAAM,CAAEgB,CAAM,IAA6BA,CAAC,YAAYjB,YAAY,CAAC,EACrEE,GAAG,CAAEe,CAAoB,IAAI;IAC3B,OAAOA,CAAkE;EAC3E,CAAC,CAAC,CACH;AACH;AAEAb,uDAAuD,CAACM,IAAI,GAAG,mDAAmD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}