{"ast": null, "code": "import { SmartTableData } from '../data/smart-table';\nimport * as i0 from \"@angular/core\";\nexport class SmartTableService extends SmartTableData {\n  constructor() {\n    super(...arguments);\n    this.data = [{\n      id: 1,\n      firstName: '<PERSON>',\n      lastName: '<PERSON>',\n      username: '@mdo',\n      email: '<EMAIL>',\n      age: '28'\n    }, {\n      id: 2,\n      firstName: '<PERSON>',\n      lastName: '<PERSON>',\n      username: '@fat',\n      email: '<EMAIL>',\n      age: '45'\n    }, {\n      id: 3,\n      firstName: '<PERSON>',\n      lastName: '<PERSON>',\n      username: '@twitter',\n      email: '<EMAIL>',\n      age: '18'\n    }, {\n      id: 4,\n      firstName: '<PERSON>',\n      lastName: '<PERSON>',\n      username: '@snow',\n      email: '<EMAIL>',\n      age: '20'\n    }, {\n      id: 5,\n      firstName: 'Jack',\n      lastName: 'Sparrow',\n      username: '@jack',\n      email: '<EMAIL>',\n      age: '30'\n    }, {\n      id: 6,\n      firstName: '<PERSON>',\n      lastName: '<PERSON>',\n      username: '@ann',\n      email: '<EMAIL>',\n      age: '21'\n    }, {\n      id: 7,\n      firstName: '<PERSON>',\n      lastName: '<PERSON>',\n      username: '@barbara',\n      email: '<EMAIL>',\n      age: '43'\n    }, {\n      id: 8,\n      firstName: 'Sevan',\n      lastName: 'Bagrat',\n      username: '@sevan',\n      email: '<EMAIL>',\n      age: '13'\n    }, {\n      id: 9,\n      firstName: 'Ruben',\n      lastName: 'Vardan',\n      username: '@ruben',\n      email: '<EMAIL>',\n      age: '22'\n    }, {\n      id: 10,\n      firstName: 'Karen',\n      lastName: 'Sevan',\n      username: '@karen',\n      email: '<EMAIL>',\n      age: '33'\n    }, {\n      id: 11,\n      firstName: 'Mark',\n      lastName: 'Otto',\n      username: '@mark',\n      email: '<EMAIL>',\n      age: '38'\n    }, {\n      id: 12,\n      firstName: 'Jacob',\n      lastName: 'Thornton',\n      username: '@jacob',\n      email: '<EMAIL>',\n      age: '48'\n    }, {\n      id: 13,\n      firstName: 'Haik',\n      lastName: 'Hakob',\n      username: '@haik',\n      email: '<EMAIL>',\n      age: '48'\n    }, {\n      id: 14,\n      firstName: 'Garegin',\n      lastName: 'Jirair',\n      username: '@garegin',\n      email: '<EMAIL>',\n      age: '40'\n    }, {\n      id: 15,\n      firstName: 'Krikor',\n      lastName: 'Bedros',\n      username: '@krikor',\n      email: '<EMAIL>',\n      age: '32'\n    }, {\n      'id': 16,\n      'firstName': 'Francisca',\n      'lastName': 'Brady',\n      'username': '@Gibson',\n      'email': '<EMAIL>',\n      'age': 11\n    }, {\n      'id': 17,\n      'firstName': 'Tillman',\n      'lastName': 'Figueroa',\n      'username': '@Snow',\n      'email': '<EMAIL>',\n      'age': 34\n    }, {\n      'id': 18,\n      'firstName': 'Jimenez',\n      'lastName': 'Morris',\n      'username': '@Bryant',\n      'email': '<EMAIL>',\n      'age': 45\n    }, {\n      'id': 19,\n      'firstName': 'Sandoval',\n      'lastName': 'Jacobson',\n      'username': '@Mcbride',\n      'email': '<EMAIL>',\n      'age': 32\n    }, {\n      'id': 20,\n      'firstName': 'Griffin',\n      'lastName': 'Torres',\n      'username': '@Charles',\n      'email': '<EMAIL>',\n      'age': 19\n    }, {\n      'id': 21,\n      'firstName': 'Cora',\n      'lastName': 'Parker',\n      'username': '@Caldwell',\n      'email': '<EMAIL>',\n      'age': 27\n    }, {\n      'id': 22,\n      'firstName': 'Cindy',\n      'lastName': 'Bond',\n      'username': '@Velez',\n      'email': '<EMAIL>',\n      'age': 24\n    }, {\n      'id': 23,\n      'firstName': 'Frieda',\n      'lastName': 'Tyson',\n      'username': '@Craig',\n      'email': '<EMAIL>',\n      'age': 45\n    }, {\n      'id': 24,\n      'firstName': 'Cote',\n      'lastName': 'Holcomb',\n      'username': '@Rowe',\n      'email': '<EMAIL>',\n      'age': 20\n    }, {\n      'id': 25,\n      'firstName': 'Trujillo',\n      'lastName': 'Mejia',\n      'username': '@Valenzuela',\n      'email': '<EMAIL>',\n      'age': 16\n    }, {\n      'id': 26,\n      'firstName': 'Pruitt',\n      'lastName': 'Shepard',\n      'username': '@Sloan',\n      'email': '<EMAIL>',\n      'age': 44\n    }, {\n      'id': 27,\n      'firstName': 'Sutton',\n      'lastName': 'Ortega',\n      'username': '@Black',\n      'email': '<EMAIL>',\n      'age': 42\n    }, {\n      'id': 28,\n      'firstName': 'Marion',\n      'lastName': 'Heath',\n      'username': '@Espinoza',\n      'email': '<EMAIL>',\n      'age': 47\n    }, {\n      'id': 29,\n      'firstName': 'Newman',\n      'lastName': 'Hicks',\n      'username': '@Keith',\n      'email': '<EMAIL>',\n      'age': 15\n    }, {\n      'id': 30,\n      'firstName': 'Boyle',\n      'lastName': 'Larson',\n      'username': '@Summers',\n      'email': '<EMAIL>',\n      'age': 32\n    }, {\n      'id': 31,\n      'firstName': 'Haynes',\n      'lastName': 'Vinson',\n      'username': '@Mckenzie',\n      'email': '<EMAIL>',\n      'age': 15\n    }, {\n      'id': 32,\n      'firstName': 'Miller',\n      'lastName': 'Acosta',\n      'username': '@Young',\n      'email': '<EMAIL>',\n      'age': 55\n    }, {\n      'id': 33,\n      'firstName': 'Johnston',\n      'lastName': 'Brown',\n      'username': '@Knight',\n      'email': '<EMAIL>',\n      'age': 29\n    }, {\n      'id': 34,\n      'firstName': 'Lena',\n      'lastName': 'Pitts',\n      'username': '@Forbes',\n      'email': '<EMAIL>',\n      'age': 25\n    }, {\n      'id': 35,\n      'firstName': 'Terrie',\n      'lastName': 'Kennedy',\n      'username': '@Branch',\n      'email': '<EMAIL>',\n      'age': 37\n    }, {\n      'id': 36,\n      'firstName': 'Louise',\n      'lastName': 'Aguirre',\n      'username': '@Kirby',\n      'email': '<EMAIL>',\n      'age': 44\n    }, {\n      'id': 37,\n      'firstName': 'David',\n      'lastName': 'Patton',\n      'username': '@Sanders',\n      'email': '<EMAIL>',\n      'age': 26\n    }, {\n      'id': 38,\n      'firstName': 'Holden',\n      'lastName': 'Barlow',\n      'username': '@Mckinney',\n      'email': '<EMAIL>',\n      'age': 11\n    }, {\n      'id': 39,\n      'firstName': 'Baker',\n      'lastName': 'Rivera',\n      'username': '@Montoya',\n      'email': '<EMAIL>',\n      'age': 47\n    }, {\n      'id': 40,\n      'firstName': 'Belinda',\n      'lastName': 'Lloyd',\n      'username': '@Calderon',\n      'email': '<EMAIL>',\n      'age': 21\n    }, {\n      'id': 41,\n      'firstName': 'Pearson',\n      'lastName': 'Patrick',\n      'username': '@Clements',\n      'email': '<EMAIL>',\n      'age': 42\n    }, {\n      'id': 42,\n      'firstName': 'Alyce',\n      'lastName': 'Mckee',\n      'username': '@Daugherty',\n      'email': '<EMAIL>',\n      'age': 55\n    }, {\n      'id': 43,\n      'firstName': 'Valencia',\n      'lastName': 'Spence',\n      'username': '@Olsen',\n      'email': '<EMAIL>',\n      'age': 20\n    }, {\n      'id': 44,\n      'firstName': 'Leach',\n      'lastName': 'Holcomb',\n      'username': '@Humphrey',\n      'email': '<EMAIL>',\n      'age': 28\n    }, {\n      'id': 45,\n      'firstName': 'Moss',\n      'lastName': 'Baxter',\n      'username': '@Fitzpatrick',\n      'email': '<EMAIL>',\n      'age': 51\n    }, {\n      'id': 46,\n      'firstName': 'Jeanne',\n      'lastName': 'Cooke',\n      'username': '@Ward',\n      'email': '<EMAIL>',\n      'age': 59\n    }, {\n      'id': 47,\n      'firstName': 'Wilma',\n      'lastName': 'Briggs',\n      'username': '@Kidd',\n      'email': '<EMAIL>',\n      'age': 53\n    }, {\n      'id': 48,\n      'firstName': 'Beatrice',\n      'lastName': 'Perry',\n      'username': '@Gilbert',\n      'email': '<EMAIL>',\n      'age': 39\n    }, {\n      'id': 49,\n      'firstName': 'Whitaker',\n      'lastName': 'Hyde',\n      'username': '@Mcdonald',\n      'email': '<EMAIL>',\n      'age': 35\n    }, {\n      'id': 50,\n      'firstName': 'Rebekah',\n      'lastName': 'Duran',\n      'username': '@Gross',\n      'email': '<EMAIL>',\n      'age': 40\n    }, {\n      'id': 51,\n      'firstName': 'Earline',\n      'lastName': 'Mayer',\n      'username': '@Woodward',\n      'email': '<EMAIL>',\n      'age': 52\n    }, {\n      'id': 52,\n      'firstName': 'Moran',\n      'lastName': 'Baxter',\n      'username': '@Johns',\n      'email': '<EMAIL>',\n      'age': 20\n    }, {\n      'id': 53,\n      'firstName': 'Nanette',\n      'lastName': 'Hubbard',\n      'username': '@Cooke',\n      'email': '<EMAIL>',\n      'age': 55\n    }, {\n      'id': 54,\n      'firstName': 'Dalton',\n      'lastName': 'Walker',\n      'username': '@Hendricks',\n      'email': '<EMAIL>',\n      'age': 25\n    }, {\n      'id': 55,\n      'firstName': 'Bennett',\n      'lastName': 'Blake',\n      'username': '@Pena',\n      'email': '<EMAIL>',\n      'age': 13\n    }, {\n      'id': 56,\n      'firstName': 'Kellie',\n      'lastName': 'Horton',\n      'username': '@Weiss',\n      'email': '<EMAIL>',\n      'age': 48\n    }, {\n      'id': 57,\n      'firstName': 'Hobbs',\n      'lastName': 'Talley',\n      'username': '@Sanford',\n      'email': '<EMAIL>',\n      'age': 28\n    }, {\n      'id': 58,\n      'firstName': 'Mcguire',\n      'lastName': 'Donaldson',\n      'username': '@Roman',\n      'email': '<EMAIL>',\n      'age': 38\n    }, {\n      'id': 59,\n      'firstName': 'Rodriquez',\n      'lastName': 'Saunders',\n      'username': '@Harper',\n      'email': '<EMAIL>',\n      'age': 20\n    }, {\n      'id': 60,\n      'firstName': 'Lou',\n      'lastName': 'Conner',\n      'username': '@Sanchez',\n      'email': '<EMAIL>',\n      'age': 16\n    }];\n  }\n  getData() {\n    return this.data;\n  }\n  static {\n    this.ɵfac = /*@__PURE__*/(() => {\n      let ɵSmartTableService_BaseFactory;\n      return function SmartTableService_Factory(__ngFactoryType__) {\n        return (ɵSmartTableService_BaseFactory || (ɵSmartTableService_BaseFactory = i0.ɵɵgetInheritedFactory(SmartTableService)))(__ngFactoryType__ || SmartTableService);\n      };\n    })();\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: SmartTableService,\n      factory: SmartTableService.ɵfac\n    });\n  }\n}", "map": {"version": 3, "names": ["SmartTableData", "SmartTableService", "constructor", "data", "id", "firstName", "lastName", "username", "email", "age", "getData", "__ngFactoryType__", "factory", "ɵfac"], "sources": ["C:\\Users\\<USER>\\Documents\\jeansalechange\\adminFront\\src\\app\\@core\\mock\\smart-table.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport { SmartTableData } from '../data/smart-table';\r\n\r\n@Injectable()\r\nexport class SmartTableService extends SmartTableData {\r\n\r\n  data = [{\r\n    id: 1,\r\n    firstName: '<PERSON>',\r\n    lastName: '<PERSON>',\r\n    username: '@mdo',\r\n    email: '<EMAIL>',\r\n    age: '28',\r\n  }, {\r\n    id: 2,\r\n    firstName: '<PERSON>',\r\n    lastName: '<PERSON>',\r\n    username: '@fat',\r\n    email: '<EMAIL>',\r\n    age: '45',\r\n  }, {\r\n    id: 3,\r\n    firstName: '<PERSON>',\r\n    lastName: '<PERSON>',\r\n    username: '@twitter',\r\n    email: '<EMAIL>',\r\n    age: '18',\r\n  }, {\r\n    id: 4,\r\n    firstName: '<PERSON>',\r\n    lastName: '<PERSON>',\r\n    username: '@snow',\r\n    email: '<EMAIL>',\r\n    age: '20',\r\n  }, {\r\n    id: 5,\r\n    firstName: '<PERSON>',\r\n    lastName: 'Sparrow',\r\n    username: '@jack',\r\n    email: '<EMAIL>',\r\n    age: '30',\r\n  }, {\r\n    id: 6,\r\n    firstName: '<PERSON>',\r\n    lastName: '<PERSON>',\r\n    username: '@ann',\r\n    email: '<EMAIL>',\r\n    age: '21',\r\n  }, {\r\n    id: 7,\r\n    firstName: '<PERSON>',\r\n    lastName: '<PERSON>',\r\n    username: '@barbara',\r\n    email: '<EMAIL>',\r\n    age: '43',\r\n  }, {\r\n    id: 8,\r\n    firstName: 'Sevan',\r\n    lastName: 'Bagrat',\r\n    username: '@sevan',\r\n    email: '<EMAIL>',\r\n    age: '13',\r\n  }, {\r\n    id: 9,\r\n    firstName: 'Ruben',\r\n    lastName: 'Vardan',\r\n    username: '@ruben',\r\n    email: '<EMAIL>',\r\n    age: '22',\r\n  }, {\r\n    id: 10,\r\n    firstName: 'Karen',\r\n    lastName: 'Sevan',\r\n    username: '@karen',\r\n    email: '<EMAIL>',\r\n    age: '33',\r\n  }, {\r\n    id: 11,\r\n    firstName: 'Mark',\r\n    lastName: 'Otto',\r\n    username: '@mark',\r\n    email: '<EMAIL>',\r\n    age: '38',\r\n  }, {\r\n    id: 12,\r\n    firstName: 'Jacob',\r\n    lastName: 'Thornton',\r\n    username: '@jacob',\r\n    email: '<EMAIL>',\r\n    age: '48',\r\n  }, {\r\n    id: 13,\r\n    firstName: 'Haik',\r\n    lastName: 'Hakob',\r\n    username: '@haik',\r\n    email: '<EMAIL>',\r\n    age: '48',\r\n  }, {\r\n    id: 14,\r\n    firstName: 'Garegin',\r\n    lastName: 'Jirair',\r\n    username: '@garegin',\r\n    email: '<EMAIL>',\r\n    age: '40',\r\n  }, {\r\n    id: 15,\r\n    firstName: 'Krikor',\r\n    lastName: 'Bedros',\r\n    username: '@krikor',\r\n    email: '<EMAIL>',\r\n    age: '32',\r\n  }, {\r\n    'id': 16,\r\n    'firstName': 'Francisca',\r\n    'lastName': 'Brady',\r\n    'username': '@Gibson',\r\n    'email': '<EMAIL>',\r\n    'age': 11,\r\n  }, {\r\n    'id': 17,\r\n    'firstName': 'Tillman',\r\n    'lastName': 'Figueroa',\r\n    'username': '@Snow',\r\n    'email': '<EMAIL>',\r\n    'age': 34,\r\n  }, {\r\n    'id': 18,\r\n    'firstName': 'Jimenez',\r\n    'lastName': 'Morris',\r\n    'username': '@Bryant',\r\n    'email': '<EMAIL>',\r\n    'age': 45,\r\n  }, {\r\n    'id': 19,\r\n    'firstName': 'Sandoval',\r\n    'lastName': 'Jacobson',\r\n    'username': '@Mcbride',\r\n    'email': '<EMAIL>',\r\n    'age': 32,\r\n  }, {\r\n    'id': 20,\r\n    'firstName': 'Griffin',\r\n    'lastName': 'Torres',\r\n    'username': '@Charles',\r\n    'email': '<EMAIL>',\r\n    'age': 19,\r\n  }, {\r\n    'id': 21,\r\n    'firstName': 'Cora',\r\n    'lastName': 'Parker',\r\n    'username': '@Caldwell',\r\n    'email': '<EMAIL>',\r\n    'age': 27,\r\n  }, {\r\n    'id': 22,\r\n    'firstName': 'Cindy',\r\n    'lastName': 'Bond',\r\n    'username': '@Velez',\r\n    'email': '<EMAIL>',\r\n    'age': 24,\r\n  }, {\r\n    'id': 23,\r\n    'firstName': 'Frieda',\r\n    'lastName': 'Tyson',\r\n    'username': '@Craig',\r\n    'email': '<EMAIL>',\r\n    'age': 45,\r\n  }, {\r\n    'id': 24,\r\n    'firstName': 'Cote',\r\n    'lastName': 'Holcomb',\r\n    'username': '@Rowe',\r\n    'email': '<EMAIL>',\r\n    'age': 20,\r\n  }, {\r\n    'id': 25,\r\n    'firstName': 'Trujillo',\r\n    'lastName': 'Mejia',\r\n    'username': '@Valenzuela',\r\n    'email': '<EMAIL>',\r\n    'age': 16,\r\n  }, {\r\n    'id': 26,\r\n    'firstName': 'Pruitt',\r\n    'lastName': 'Shepard',\r\n    'username': '@Sloan',\r\n    'email': '<EMAIL>',\r\n    'age': 44,\r\n  }, {\r\n    'id': 27,\r\n    'firstName': 'Sutton',\r\n    'lastName': 'Ortega',\r\n    'username': '@Black',\r\n    'email': '<EMAIL>',\r\n    'age': 42,\r\n  }, {\r\n    'id': 28,\r\n    'firstName': 'Marion',\r\n    'lastName': 'Heath',\r\n    'username': '@Espinoza',\r\n    'email': '<EMAIL>',\r\n    'age': 47,\r\n  }, {\r\n    'id': 29,\r\n    'firstName': 'Newman',\r\n    'lastName': 'Hicks',\r\n    'username': '@Keith',\r\n    'email': '<EMAIL>',\r\n    'age': 15,\r\n  }, {\r\n    'id': 30,\r\n    'firstName': 'Boyle',\r\n    'lastName': 'Larson',\r\n    'username': '@Summers',\r\n    'email': '<EMAIL>',\r\n    'age': 32,\r\n  }, {\r\n    'id': 31,\r\n    'firstName': 'Haynes',\r\n    'lastName': 'Vinson',\r\n    'username': '@Mckenzie',\r\n    'email': '<EMAIL>',\r\n    'age': 15,\r\n  }, {\r\n    'id': 32,\r\n    'firstName': 'Miller',\r\n    'lastName': 'Acosta',\r\n    'username': '@Young',\r\n    'email': '<EMAIL>',\r\n    'age': 55,\r\n  }, {\r\n    'id': 33,\r\n    'firstName': 'Johnston',\r\n    'lastName': 'Brown',\r\n    'username': '@Knight',\r\n    'email': '<EMAIL>',\r\n    'age': 29,\r\n  }, {\r\n    'id': 34,\r\n    'firstName': 'Lena',\r\n    'lastName': 'Pitts',\r\n    'username': '@Forbes',\r\n    'email': '<EMAIL>',\r\n    'age': 25,\r\n  }, {\r\n    'id': 35,\r\n    'firstName': 'Terrie',\r\n    'lastName': 'Kennedy',\r\n    'username': '@Branch',\r\n    'email': '<EMAIL>',\r\n    'age': 37,\r\n  }, {\r\n    'id': 36,\r\n    'firstName': 'Louise',\r\n    'lastName': 'Aguirre',\r\n    'username': '@Kirby',\r\n    'email': '<EMAIL>',\r\n    'age': 44,\r\n  }, {\r\n    'id': 37,\r\n    'firstName': 'David',\r\n    'lastName': 'Patton',\r\n    'username': '@Sanders',\r\n    'email': '<EMAIL>',\r\n    'age': 26,\r\n  }, {\r\n    'id': 38,\r\n    'firstName': 'Holden',\r\n    'lastName': 'Barlow',\r\n    'username': '@Mckinney',\r\n    'email': '<EMAIL>',\r\n    'age': 11,\r\n  }, {\r\n    'id': 39,\r\n    'firstName': 'Baker',\r\n    'lastName': 'Rivera',\r\n    'username': '@Montoya',\r\n    'email': '<EMAIL>',\r\n    'age': 47,\r\n  }, {\r\n    'id': 40,\r\n    'firstName': 'Belinda',\r\n    'lastName': 'Lloyd',\r\n    'username': '@Calderon',\r\n    'email': '<EMAIL>',\r\n    'age': 21,\r\n  }, {\r\n    'id': 41,\r\n    'firstName': 'Pearson',\r\n    'lastName': 'Patrick',\r\n    'username': '@Clements',\r\n    'email': '<EMAIL>',\r\n    'age': 42,\r\n  }, {\r\n    'id': 42,\r\n    'firstName': 'Alyce',\r\n    'lastName': 'Mckee',\r\n    'username': '@Daugherty',\r\n    'email': '<EMAIL>',\r\n    'age': 55,\r\n  }, {\r\n    'id': 43,\r\n    'firstName': 'Valencia',\r\n    'lastName': 'Spence',\r\n    'username': '@Olsen',\r\n    'email': '<EMAIL>',\r\n    'age': 20,\r\n  }, {\r\n    'id': 44,\r\n    'firstName': 'Leach',\r\n    'lastName': 'Holcomb',\r\n    'username': '@Humphrey',\r\n    'email': '<EMAIL>',\r\n    'age': 28,\r\n  }, {\r\n    'id': 45,\r\n    'firstName': 'Moss',\r\n    'lastName': 'Baxter',\r\n    'username': '@Fitzpatrick',\r\n    'email': '<EMAIL>',\r\n    'age': 51,\r\n  }, {\r\n    'id': 46,\r\n    'firstName': 'Jeanne',\r\n    'lastName': 'Cooke',\r\n    'username': '@Ward',\r\n    'email': '<EMAIL>',\r\n    'age': 59,\r\n  }, {\r\n    'id': 47,\r\n    'firstName': 'Wilma',\r\n    'lastName': 'Briggs',\r\n    'username': '@Kidd',\r\n    'email': '<EMAIL>',\r\n    'age': 53,\r\n  }, {\r\n    'id': 48,\r\n    'firstName': 'Beatrice',\r\n    'lastName': 'Perry',\r\n    'username': '@Gilbert',\r\n    'email': '<EMAIL>',\r\n    'age': 39,\r\n  }, {\r\n    'id': 49,\r\n    'firstName': 'Whitaker',\r\n    'lastName': 'Hyde',\r\n    'username': '@Mcdonald',\r\n    'email': '<EMAIL>',\r\n    'age': 35,\r\n  }, {\r\n    'id': 50,\r\n    'firstName': 'Rebekah',\r\n    'lastName': 'Duran',\r\n    'username': '@Gross',\r\n    'email': '<EMAIL>',\r\n    'age': 40,\r\n  }, {\r\n    'id': 51,\r\n    'firstName': 'Earline',\r\n    'lastName': 'Mayer',\r\n    'username': '@Woodward',\r\n    'email': '<EMAIL>',\r\n    'age': 52,\r\n  }, {\r\n    'id': 52,\r\n    'firstName': 'Moran',\r\n    'lastName': 'Baxter',\r\n    'username': '@Johns',\r\n    'email': '<EMAIL>',\r\n    'age': 20,\r\n  }, {\r\n    'id': 53,\r\n    'firstName': 'Nanette',\r\n    'lastName': 'Hubbard',\r\n    'username': '@Cooke',\r\n    'email': '<EMAIL>',\r\n    'age': 55,\r\n  }, {\r\n    'id': 54,\r\n    'firstName': 'Dalton',\r\n    'lastName': 'Walker',\r\n    'username': '@Hendricks',\r\n    'email': '<EMAIL>',\r\n    'age': 25,\r\n  }, {\r\n    'id': 55,\r\n    'firstName': 'Bennett',\r\n    'lastName': 'Blake',\r\n    'username': '@Pena',\r\n    'email': '<EMAIL>',\r\n    'age': 13,\r\n  }, {\r\n    'id': 56,\r\n    'firstName': 'Kellie',\r\n    'lastName': 'Horton',\r\n    'username': '@Weiss',\r\n    'email': '<EMAIL>',\r\n    'age': 48,\r\n  }, {\r\n    'id': 57,\r\n    'firstName': 'Hobbs',\r\n    'lastName': 'Talley',\r\n    'username': '@Sanford',\r\n    'email': '<EMAIL>',\r\n    'age': 28,\r\n  }, {\r\n    'id': 58,\r\n    'firstName': 'Mcguire',\r\n    'lastName': 'Donaldson',\r\n    'username': '@Roman',\r\n    'email': '<EMAIL>',\r\n    'age': 38,\r\n  }, {\r\n    'id': 59,\r\n    'firstName': 'Rodriquez',\r\n    'lastName': 'Saunders',\r\n    'username': '@Harper',\r\n    'email': '<EMAIL>',\r\n    'age': 20,\r\n  }, {\r\n    'id': 60,\r\n    'firstName': 'Lou',\r\n    'lastName': 'Conner',\r\n    'username': '@Sanchez',\r\n    'email': '<EMAIL>',\r\n    'age': 16,\r\n  }];\r\n\r\n  getData() {\r\n    return this.data;\r\n  }\r\n}\r\n"], "mappings": "AACA,SAASA,cAAc,QAAQ,qBAAqB;;AAGpD,OAAM,MAAOC,iBAAkB,SAAQD,cAAc;EADrDE,YAAA;;IAGE,KAAAC,IAAI,GAAG,CAAC;MACNC,EAAE,EAAE,CAAC;MACLC,SAAS,EAAE,MAAM;MACjBC,QAAQ,EAAE,MAAM;MAChBC,QAAQ,EAAE,MAAM;MAChBC,KAAK,EAAE,eAAe;MACtBC,GAAG,EAAE;KACN,EAAE;MACDL,EAAE,EAAE,CAAC;MACLC,SAAS,EAAE,OAAO;MAClBC,QAAQ,EAAE,UAAU;MACpBC,QAAQ,EAAE,MAAM;MAChBC,KAAK,EAAE,eAAe;MACtBC,GAAG,EAAE;KACN,EAAE;MACDL,EAAE,EAAE,CAAC;MACLC,SAAS,EAAE,OAAO;MAClBC,QAAQ,EAAE,MAAM;MAChBC,QAAQ,EAAE,UAAU;MACpBC,KAAK,EAAE,qBAAqB;MAC5BC,GAAG,EAAE;KACN,EAAE;MACDL,EAAE,EAAE,CAAC;MACLC,SAAS,EAAE,MAAM;MACjBC,QAAQ,EAAE,MAAM;MAChBC,QAAQ,EAAE,OAAO;MACjBC,KAAK,EAAE,gBAAgB;MACvBC,GAAG,EAAE;KACN,EAAE;MACDL,EAAE,EAAE,CAAC;MACLC,SAAS,EAAE,MAAM;MACjBC,QAAQ,EAAE,SAAS;MACnBC,QAAQ,EAAE,OAAO;MACjBC,KAAK,EAAE,gBAAgB;MACvBC,GAAG,EAAE;KACN,EAAE;MACDL,EAAE,EAAE,CAAC;MACLC,SAAS,EAAE,KAAK;MAChBC,QAAQ,EAAE,OAAO;MACjBC,QAAQ,EAAE,MAAM;MAChBC,KAAK,EAAE,eAAe;MACtBC,GAAG,EAAE;KACN,EAAE;MACDL,EAAE,EAAE,CAAC;MACLC,SAAS,EAAE,SAAS;MACpBC,QAAQ,EAAE,OAAO;MACjBC,QAAQ,EAAE,UAAU;MACpBC,KAAK,EAAE,mBAAmB;MAC1BC,GAAG,EAAE;KACN,EAAE;MACDL,EAAE,EAAE,CAAC;MACLC,SAAS,EAAE,OAAO;MAClBC,QAAQ,EAAE,QAAQ;MAClBC,QAAQ,EAAE,QAAQ;MAClBC,KAAK,EAAE,mBAAmB;MAC1BC,GAAG,EAAE;KACN,EAAE;MACDL,EAAE,EAAE,CAAC;MACLC,SAAS,EAAE,OAAO;MAClBC,QAAQ,EAAE,QAAQ;MAClBC,QAAQ,EAAE,QAAQ;MAClBC,KAAK,EAAE,iBAAiB;MACxBC,GAAG,EAAE;KACN,EAAE;MACDL,EAAE,EAAE,EAAE;MACNC,SAAS,EAAE,OAAO;MAClBC,QAAQ,EAAE,OAAO;MACjBC,QAAQ,EAAE,QAAQ;MAClBC,KAAK,EAAE,iBAAiB;MACxBC,GAAG,EAAE;KACN,EAAE;MACDL,EAAE,EAAE,EAAE;MACNC,SAAS,EAAE,MAAM;MACjBC,QAAQ,EAAE,MAAM;MAChBC,QAAQ,EAAE,OAAO;MACjBC,KAAK,EAAE,gBAAgB;MACvBC,GAAG,EAAE;KACN,EAAE;MACDL,EAAE,EAAE,EAAE;MACNC,SAAS,EAAE,OAAO;MAClBC,QAAQ,EAAE,UAAU;MACpBC,QAAQ,EAAE,QAAQ;MAClBC,KAAK,EAAE,iBAAiB;MACxBC,GAAG,EAAE;KACN,EAAE;MACDL,EAAE,EAAE,EAAE;MACNC,SAAS,EAAE,MAAM;MACjBC,QAAQ,EAAE,OAAO;MACjBC,QAAQ,EAAE,OAAO;MACjBC,KAAK,EAAE,kBAAkB;MACzBC,GAAG,EAAE;KACN,EAAE;MACDL,EAAE,EAAE,EAAE;MACNC,SAAS,EAAE,SAAS;MACpBC,QAAQ,EAAE,QAAQ;MAClBC,QAAQ,EAAE,UAAU;MACpBC,KAAK,EAAE,mBAAmB;MAC1BC,GAAG,EAAE;KACN,EAAE;MACDL,EAAE,EAAE,EAAE;MACNC,SAAS,EAAE,QAAQ;MACnBC,QAAQ,EAAE,QAAQ;MAClBC,QAAQ,EAAE,SAAS;MACnBC,KAAK,EAAE,kBAAkB;MACzBC,GAAG,EAAE;KACN,EAAE;MACD,IAAI,EAAE,EAAE;MACR,WAAW,EAAE,WAAW;MACxB,UAAU,EAAE,OAAO;MACnB,UAAU,EAAE,SAAS;MACrB,OAAO,EAAE,8BAA8B;MACvC,KAAK,EAAE;KACR,EAAE;MACD,IAAI,EAAE,EAAE;MACR,WAAW,EAAE,SAAS;MACtB,UAAU,EAAE,UAAU;MACtB,UAAU,EAAE,OAAO;MACnB,OAAO,EAAE,0BAA0B;MACnC,KAAK,EAAE;KACR,EAAE;MACD,IAAI,EAAE,EAAE;MACR,WAAW,EAAE,SAAS;MACtB,UAAU,EAAE,QAAQ;MACpB,UAAU,EAAE,SAAS;MACrB,OAAO,EAAE,4BAA4B;MACrC,KAAK,EAAE;KACR,EAAE;MACD,IAAI,EAAE,EAAE;MACR,WAAW,EAAE,UAAU;MACvB,UAAU,EAAE,UAAU;MACtB,UAAU,EAAE,UAAU;MACtB,OAAO,EAAE,8BAA8B;MACvC,KAAK,EAAE;KACR,EAAE;MACD,IAAI,EAAE,EAAE;MACR,WAAW,EAAE,SAAS;MACtB,UAAU,EAAE,QAAQ;MACpB,UAAU,EAAE,UAAU;MACtB,OAAO,EAAE,6BAA6B;MACtC,KAAK,EAAE;KACR,EAAE;MACD,IAAI,EAAE,EAAE;MACR,WAAW,EAAE,MAAM;MACnB,UAAU,EAAE,QAAQ;MACpB,UAAU,EAAE,WAAW;MACvB,OAAO,EAAE,2BAA2B;MACpC,KAAK,EAAE;KACR,EAAE;MACD,IAAI,EAAE,EAAE;MACR,WAAW,EAAE,OAAO;MACpB,UAAU,EAAE,MAAM;MAClB,UAAU,EAAE,QAAQ;MACpB,OAAO,EAAE,yBAAyB;MAClC,KAAK,EAAE;KACR,EAAE;MACD,IAAI,EAAE,EAAE;MACR,WAAW,EAAE,QAAQ;MACrB,UAAU,EAAE,OAAO;MACnB,UAAU,EAAE,QAAQ;MACpB,OAAO,EAAE,0BAA0B;MACnC,KAAK,EAAE;KACR,EAAE;MACD,IAAI,EAAE,EAAE;MACR,WAAW,EAAE,MAAM;MACnB,UAAU,EAAE,SAAS;MACrB,UAAU,EAAE,OAAO;MACnB,OAAO,EAAE,uBAAuB;MAChC,KAAK,EAAE;KACR,EAAE;MACD,IAAI,EAAE,EAAE;MACR,WAAW,EAAE,UAAU;MACvB,UAAU,EAAE,OAAO;MACnB,UAAU,EAAE,aAAa;MACzB,OAAO,EAAE,iCAAiC;MAC1C,KAAK,EAAE;KACR,EAAE;MACD,IAAI,EAAE,EAAE;MACR,WAAW,EAAE,QAAQ;MACrB,UAAU,EAAE,SAAS;MACrB,UAAU,EAAE,QAAQ;MACpB,OAAO,EAAE,0BAA0B;MACnC,KAAK,EAAE;KACR,EAAE;MACD,IAAI,EAAE,EAAE;MACR,WAAW,EAAE,QAAQ;MACrB,UAAU,EAAE,QAAQ;MACpB,UAAU,EAAE,QAAQ;MACpB,OAAO,EAAE,0BAA0B;MACnC,KAAK,EAAE;KACR,EAAE;MACD,IAAI,EAAE,EAAE;MACR,WAAW,EAAE,QAAQ;MACrB,UAAU,EAAE,OAAO;MACnB,UAAU,EAAE,WAAW;MACvB,OAAO,EAAE,6BAA6B;MACtC,KAAK,EAAE;KACR,EAAE;MACD,IAAI,EAAE,EAAE;MACR,WAAW,EAAE,QAAQ;MACrB,UAAU,EAAE,OAAO;MACnB,UAAU,EAAE,QAAQ;MACpB,OAAO,EAAE,0BAA0B;MACnC,KAAK,EAAE;KACR,EAAE;MACD,IAAI,EAAE,EAAE;MACR,WAAW,EAAE,OAAO;MACpB,UAAU,EAAE,QAAQ;MACpB,UAAU,EAAE,UAAU;MACtB,OAAO,EAAE,2BAA2B;MACpC,KAAK,EAAE;KACR,EAAE;MACD,IAAI,EAAE,EAAE;MACR,WAAW,EAAE,QAAQ;MACrB,UAAU,EAAE,QAAQ;MACpB,UAAU,EAAE,WAAW;MACvB,OAAO,EAAE,6BAA6B;MACtC,KAAK,EAAE;KACR,EAAE;MACD,IAAI,EAAE,EAAE;MACR,WAAW,EAAE,QAAQ;MACrB,UAAU,EAAE,QAAQ;MACpB,UAAU,EAAE,QAAQ;MACpB,OAAO,EAAE,0BAA0B;MACnC,KAAK,EAAE;KACR,EAAE;MACD,IAAI,EAAE,EAAE;MACR,WAAW,EAAE,UAAU;MACvB,UAAU,EAAE,OAAO;MACnB,UAAU,EAAE,SAAS;MACrB,OAAO,EAAE,6BAA6B;MACtC,KAAK,EAAE;KACR,EAAE;MACD,IAAI,EAAE,EAAE;MACR,WAAW,EAAE,MAAM;MACnB,UAAU,EAAE,OAAO;MACnB,UAAU,EAAE,SAAS;MACrB,OAAO,EAAE,yBAAyB;MAClC,KAAK,EAAE;KACR,EAAE;MACD,IAAI,EAAE,EAAE;MACR,WAAW,EAAE,QAAQ;MACrB,UAAU,EAAE,SAAS;MACrB,UAAU,EAAE,SAAS;MACrB,OAAO,EAAE,2BAA2B;MACpC,KAAK,EAAE;KACR,EAAE;MACD,IAAI,EAAE,EAAE;MACR,WAAW,EAAE,QAAQ;MACrB,UAAU,EAAE,SAAS;MACrB,UAAU,EAAE,QAAQ;MACpB,OAAO,EAAE,0BAA0B;MACnC,KAAK,EAAE;KACR,EAAE;MACD,IAAI,EAAE,EAAE;MACR,WAAW,EAAE,OAAO;MACpB,UAAU,EAAE,QAAQ;MACpB,UAAU,EAAE,UAAU;MACtB,OAAO,EAAE,2BAA2B;MACpC,KAAK,EAAE;KACR,EAAE;MACD,IAAI,EAAE,EAAE;MACR,WAAW,EAAE,QAAQ;MACrB,UAAU,EAAE,QAAQ;MACpB,UAAU,EAAE,WAAW;MACvB,OAAO,EAAE,6BAA6B;MACtC,KAAK,EAAE;KACR,EAAE;MACD,IAAI,EAAE,EAAE;MACR,WAAW,EAAE,OAAO;MACpB,UAAU,EAAE,QAAQ;MACpB,UAAU,EAAE,UAAU;MACtB,OAAO,EAAE,2BAA2B;MACpC,KAAK,EAAE;KACR,EAAE;MACD,IAAI,EAAE,EAAE;MACR,WAAW,EAAE,SAAS;MACtB,UAAU,EAAE,OAAO;MACnB,UAAU,EAAE,WAAW;MACvB,OAAO,EAAE,8BAA8B;MACvC,KAAK,EAAE;KACR,EAAE;MACD,IAAI,EAAE,EAAE;MACR,WAAW,EAAE,SAAS;MACtB,UAAU,EAAE,SAAS;MACrB,UAAU,EAAE,WAAW;MACvB,OAAO,EAAE,8BAA8B;MACvC,KAAK,EAAE;KACR,EAAE;MACD,IAAI,EAAE,EAAE;MACR,WAAW,EAAE,OAAO;MACpB,UAAU,EAAE,OAAO;MACnB,UAAU,EAAE,YAAY;MACxB,OAAO,EAAE,6BAA6B;MACtC,KAAK,EAAE;KACR,EAAE;MACD,IAAI,EAAE,EAAE;MACR,WAAW,EAAE,UAAU;MACvB,UAAU,EAAE,QAAQ;MACpB,UAAU,EAAE,QAAQ;MACpB,OAAO,EAAE,4BAA4B;MACrC,KAAK,EAAE;KACR,EAAE;MACD,IAAI,EAAE,EAAE;MACR,WAAW,EAAE,OAAO;MACpB,UAAU,EAAE,SAAS;MACrB,UAAU,EAAE,WAAW;MACvB,OAAO,EAAE,4BAA4B;MACrC,KAAK,EAAE;KACR,EAAE;MACD,IAAI,EAAE,EAAE;MACR,WAAW,EAAE,MAAM;MACnB,UAAU,EAAE,QAAQ;MACpB,UAAU,EAAE,cAAc;MAC1B,OAAO,EAAE,8BAA8B;MACvC,KAAK,EAAE;KACR,EAAE;MACD,IAAI,EAAE,EAAE;MACR,WAAW,EAAE,QAAQ;MACrB,UAAU,EAAE,OAAO;MACnB,UAAU,EAAE,OAAO;MACnB,OAAO,EAAE,yBAAyB;MAClC,KAAK,EAAE;KACR,EAAE;MACD,IAAI,EAAE,EAAE;MACR,WAAW,EAAE,OAAO;MACpB,UAAU,EAAE,QAAQ;MACpB,UAAU,EAAE,OAAO;MACnB,OAAO,EAAE,wBAAwB;MACjC,KAAK,EAAE;KACR,EAAE;MACD,IAAI,EAAE,EAAE;MACR,WAAW,EAAE,UAAU;MACvB,UAAU,EAAE,OAAO;MACnB,UAAU,EAAE,UAAU;MACtB,OAAO,EAAE,8BAA8B;MACvC,KAAK,EAAE;KACR,EAAE;MACD,IAAI,EAAE,EAAE;MACR,WAAW,EAAE,UAAU;MACvB,UAAU,EAAE,MAAM;MAClB,UAAU,EAAE,WAAW;MACvB,OAAO,EAAE,+BAA+B;MACxC,KAAK,EAAE;KACR,EAAE;MACD,IAAI,EAAE,EAAE;MACR,WAAW,EAAE,SAAS;MACtB,UAAU,EAAE,OAAO;MACnB,UAAU,EAAE,QAAQ;MACpB,OAAO,EAAE,2BAA2B;MACpC,KAAK,EAAE;KACR,EAAE;MACD,IAAI,EAAE,EAAE;MACR,WAAW,EAAE,SAAS;MACtB,UAAU,EAAE,OAAO;MACnB,UAAU,EAAE,WAAW;MACvB,OAAO,EAAE,8BAA8B;MACvC,KAAK,EAAE;KACR,EAAE;MACD,IAAI,EAAE,EAAE;MACR,WAAW,EAAE,OAAO;MACpB,UAAU,EAAE,QAAQ;MACpB,UAAU,EAAE,QAAQ;MACpB,OAAO,EAAE,yBAAyB;MAClC,KAAK,EAAE;KACR,EAAE;MACD,IAAI,EAAE,EAAE;MACR,WAAW,EAAE,SAAS;MACtB,UAAU,EAAE,SAAS;MACrB,UAAU,EAAE,QAAQ;MACpB,OAAO,EAAE,2BAA2B;MACpC,KAAK,EAAE;KACR,EAAE;MACD,IAAI,EAAE,EAAE;MACR,WAAW,EAAE,QAAQ;MACrB,UAAU,EAAE,QAAQ;MACpB,UAAU,EAAE,YAAY;MACxB,OAAO,EAAE,8BAA8B;MACvC,KAAK,EAAE;KACR,EAAE;MACD,IAAI,EAAE,EAAE;MACR,WAAW,EAAE,SAAS;MACtB,UAAU,EAAE,OAAO;MACnB,UAAU,EAAE,OAAO;MACnB,OAAO,EAAE,0BAA0B;MACnC,KAAK,EAAE;KACR,EAAE;MACD,IAAI,EAAE,EAAE;MACR,WAAW,EAAE,QAAQ;MACrB,UAAU,EAAE,QAAQ;MACpB,UAAU,EAAE,QAAQ;MACpB,OAAO,EAAE,0BAA0B;MACnC,KAAK,EAAE;KACR,EAAE;MACD,IAAI,EAAE,EAAE;MACR,WAAW,EAAE,OAAO;MACpB,UAAU,EAAE,QAAQ;MACpB,UAAU,EAAE,UAAU;MACtB,OAAO,EAAE,2BAA2B;MACpC,KAAK,EAAE;KACR,EAAE;MACD,IAAI,EAAE,EAAE;MACR,WAAW,EAAE,SAAS;MACtB,UAAU,EAAE,WAAW;MACvB,UAAU,EAAE,QAAQ;MACpB,OAAO,EAAE,2BAA2B;MACpC,KAAK,EAAE;KACR,EAAE;MACD,IAAI,EAAE,EAAE;MACR,WAAW,EAAE,WAAW;MACxB,UAAU,EAAE,UAAU;MACtB,UAAU,EAAE,SAAS;MACrB,OAAO,EAAE,8BAA8B;MACvC,KAAK,EAAE;KACR,EAAE;MACD,IAAI,EAAE,EAAE;MACR,WAAW,EAAE,KAAK;MAClB,UAAU,EAAE,QAAQ;MACpB,UAAU,EAAE,UAAU;MACtB,OAAO,EAAE,yBAAyB;MAClC,KAAK,EAAE;KACR,CAAC;;EAEFC,OAAOA,CAAA;IACL,OAAO,IAAI,CAACP,IAAI;EAClB;;;;;6GA1aWF,iBAAiB,IAAAU,iBAAA,IAAjBV,iBAAiB;MAAA;IAAA;EAAA;;;aAAjBA,iBAAiB;MAAAW,OAAA,EAAjBX,iBAAiB,CAAAY;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}