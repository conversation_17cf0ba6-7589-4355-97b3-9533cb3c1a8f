{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nimport * as i2 from \"@angular/forms\";\nfunction TemplateViewerComponent_button_14_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 11);\n    i0.ɵɵlistener(\"click\", function TemplateViewerComponent_button_14_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.clearSearch());\n    });\n    i0.ɵɵelement(1, \"i\", 25);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction TemplateViewerComponent_div_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 26)(1, \"small\", 27);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\" \\u627E\\u5230 \", ctx_r1.filteredTemplates.length, \" \\u500B\\u7B26\\u5408\\u300C\", ctx_r1.searchKeyword, \"\\u300D\\u7684\\u6A21\\u677F \");\n  }\n}\nfunction TemplateViewerComponent_tr_28_button_12_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 33);\n    i0.ɵɵlistener(\"click\", function TemplateViewerComponent_tr_28_button_12_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const tpl_r4 = i0.ɵɵnextContext().$implicit;\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(tpl_r4.TemplateID && ctx_r1.onDeleteTemplate(tpl_r4.TemplateID));\n    });\n    i0.ɵɵelement(1, \"i\", 34);\n    i0.ɵɵtext(2, \" \\u522A\\u9664 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction TemplateViewerComponent_tr_28_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\")(2, \"strong\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"td\")(5, \"span\", 27);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"td\", 28)(8, \"div\", 29)(9, \"button\", 30);\n    i0.ɵɵlistener(\"click\", function TemplateViewerComponent_tr_28_Template_button_click_9_listener() {\n      const tpl_r4 = i0.ɵɵrestoreView(_r3).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onSelectTemplate(tpl_r4));\n    });\n    i0.ɵɵelement(10, \"i\", 31);\n    i0.ɵɵtext(11, \" \\u67E5\\u770B \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(12, TemplateViewerComponent_tr_28_button_12_Template, 3, 0, \"button\", 32);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const tpl_r4 = ctx.$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(tpl_r4.TemplateName);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(tpl_r4.Description || \"\\u7121\\u63CF\\u8FF0\");\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngIf\", tpl_r4.TemplateID);\n  }\n}\nfunction TemplateViewerComponent_tr_29_small_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"small\", 27);\n    i0.ɵɵtext(1, \" \\u8ACB\\u5617\\u8A66\\u5176\\u4ED6\\u95DC\\u9375\\u5B57\\u6216 \");\n    i0.ɵɵelementStart(2, \"a\", 40);\n    i0.ɵɵlistener(\"click\", function TemplateViewerComponent_tr_29_small_6_Template_a_click_2_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.clearSearch());\n    });\n    i0.ɵɵtext(3, \"\\u6E05\\u9664\\u641C\\u5C0B\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction TemplateViewerComponent_tr_29_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 35)(2, \"div\", 36);\n    i0.ɵɵelement(3, \"i\", 37);\n    i0.ɵɵelementStart(4, \"p\", 38);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(6, TemplateViewerComponent_tr_29_small_6_Template, 4, 0, \"small\", 39);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.searchKeyword ? \"\\u627E\\u4E0D\\u5230\\u7B26\\u5408\\u689D\\u4EF6\\u7684\\u6A21\\u677F\" : \"\\u66AB\\u7121\\u6A21\\u677F\", \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.searchKeyword);\n  }\n}\nfunction TemplateViewerComponent_div_30_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 51)(1, \"strong\");\n    i0.ɵɵtext(2, \"\\u63CF\\u8FF0\\uFF1A\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\", 27);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r1.selectedTemplate.Description);\n  }\n}\nfunction TemplateViewerComponent_div_30_div_14_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 54)(1, \"div\", 55)(2, \"span\", 56);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"div\", 57)(5, \"div\", 58)(6, \"strong\");\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 59);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const detail_r8 = ctx.$implicit;\n    const i_r9 = ctx.index;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i_r9 + 1);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\"\", detail_r8.FieldName, \":\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", detail_r8.FieldValue, \" \");\n  }\n}\nfunction TemplateViewerComponent_div_30_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 52);\n    i0.ɵɵtemplate(1, TemplateViewerComponent_div_30_div_14_div_1_Template, 10, 3, \"div\", 53);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.currentTemplateDetails);\n  }\n}\nfunction TemplateViewerComponent_div_30_ng_template_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 60);\n    i0.ɵɵelement(1, \"i\", 61);\n    i0.ɵɵelementStart(2, \"p\", 38);\n    i0.ɵɵtext(3, \"\\u6B64\\u6A21\\u677F\\u66AB\\u7121\\u5167\\u5BB9\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction TemplateViewerComponent_div_30_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 41)(1, \"div\", 42)(2, \"h6\", 4);\n    i0.ɵɵelement(3, \"i\", 43);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 44);\n    i0.ɵɵlistener(\"click\", function TemplateViewerComponent_div_30_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.closeTemplateDetail());\n    });\n    i0.ɵɵelement(6, \"i\", 25);\n    i0.ɵɵtext(7, \" \\u95DC\\u9589 \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 45);\n    i0.ɵɵtemplate(9, TemplateViewerComponent_div_30_div_9_Template, 5, 1, \"div\", 46);\n    i0.ɵɵelementStart(10, \"div\", 47)(11, \"h6\", 48);\n    i0.ɵɵelement(12, \"i\", 49);\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(14, TemplateViewerComponent_div_30_div_14_Template, 2, 1, \"div\", 50)(15, TemplateViewerComponent_div_30_ng_template_15_Template, 4, 0, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const noDetails_r10 = i0.ɵɵreference(16);\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \\u6A21\\u677F\\u7D30\\u7BC0\\uFF1A\", ctx_r1.selectedTemplate.TemplateName, \" \");\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.selectedTemplate.Description);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \\u6A21\\u677F\\u5167\\u5BB9 (\", ctx_r1.currentTemplateDetails.length, \" \\u9805) \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.currentTemplateDetails.length > 0)(\"ngIfElse\", noDetails_r10);\n  }\n}\nexport class TemplateViewerComponent {\n  constructor() {\n    this.availableData = []; // 父元件傳入的可選資料\n    this.selectTemplate = new EventEmitter();\n    // 內部管理的模板資料\n    this.templates = [];\n    this.templateDetails = [];\n    this.selectedTemplate = null;\n    // 查詢功能\n    this.searchKeyword = '';\n    this.filteredTemplates = [];\n    // 新增模板表單\n    this.showAddForm = false;\n    this.newTemplate = {\n      name: '',\n      description: '',\n      selectedItems: []\n    };\n  }\n  ngOnInit() {\n    this.loadTemplates();\n    this.updateFilteredTemplates();\n  }\n  ngOnChanges() {\n    this.updateFilteredTemplates();\n  }\n  // TODO: 替換為實際的API調用\n  loadTemplates() {\n    // 模擬API調用 - 載入模板列表\n    this.templates = [{\n      TemplateID: 1,\n      TemplateName: '模板A',\n      Description: '範例模板A'\n    }, {\n      TemplateID: 2,\n      TemplateName: '模板B',\n      Description: '範例模板B'\n    }];\n    this.templateDetails = [{\n      TemplateDetailID: 1,\n      TemplateID: 1,\n      RefID: 101,\n      ModuleType: 'Requirement',\n      FieldName: 'CRequirement',\n      FieldValue: '工程項目A'\n    }, {\n      TemplateDetailID: 2,\n      TemplateID: 1,\n      RefID: 102,\n      ModuleType: 'Requirement',\n      FieldName: 'CRequirement',\n      FieldValue: '工程項目B'\n    }, {\n      TemplateDetailID: 3,\n      TemplateID: 2,\n      RefID: 201,\n      ModuleType: 'Requirement',\n      FieldName: 'CRequirement',\n      FieldValue: '工程項目C'\n    }];\n  }\n  // 更新過濾後的模板列表\n  updateFilteredTemplates() {\n    if (!this.searchKeyword.trim()) {\n      this.filteredTemplates = [...this.templates];\n    } else {\n      const keyword = this.searchKeyword.toLowerCase();\n      this.filteredTemplates = this.templates.filter(template => template.TemplateName.toLowerCase().includes(keyword) || template.Description && template.Description.toLowerCase().includes(keyword));\n    }\n  }\n  // 搜尋模板\n  onSearch() {\n    this.updateFilteredTemplates();\n  }\n  // 清除搜尋\n  clearSearch() {\n    this.searchKeyword = '';\n    this.updateFilteredTemplates();\n  }\n  // 顯示新增模板表單\n  onAddTemplate() {\n    this.showAddForm = true;\n    this.newTemplate = {\n      name: '',\n      description: '',\n      selectedItems: []\n    };\n    // 重置可選資料的選擇狀態\n    this.availableData.forEach(item => item.selected = false);\n  }\n  // 取消新增模板\n  cancelAddTemplate() {\n    this.showAddForm = false;\n    this.newTemplate = {\n      name: '',\n      description: '',\n      selectedItems: []\n    };\n  }\n  // 儲存新模板\n  saveNewTemplate() {\n    if (!this.newTemplate.name.trim()) {\n      alert('請輸入模板名稱');\n      return;\n    }\n    const selectedItems = this.availableData.filter(item => item.selected);\n    if (selectedItems.length === 0) {\n      alert('請至少選擇一個項目');\n      return;\n    }\n    // TODO: 替換為實際的API調用\n    this.createTemplate(this.newTemplate.name, this.newTemplate.description, selectedItems);\n  }\n  // TODO: 替換為實際的API調用\n  createTemplate(name, description, selectedItems) {\n    // 生成新的模板ID\n    const newId = Math.max(...this.templates.map(t => t.TemplateID || 0), 0) + 1;\n    // 創建新模板\n    const newTemplate = {\n      TemplateID: newId,\n      TemplateName: name.trim(),\n      Description: description.trim()\n    };\n    // 添加到模板列表\n    this.templates.push(newTemplate);\n    // 創建模板詳情\n    selectedItems.forEach((item, index) => {\n      const detail = {\n        TemplateDetailID: this.templateDetails.length + index + 1,\n        TemplateID: newId,\n        RefID: item.CRequirementID || item.ID || 0,\n        ModuleType: 'Requirement',\n        FieldName: 'CRequirement',\n        FieldValue: item.CRequirement || item.name || ''\n      };\n      this.templateDetails.push(detail);\n    });\n    // 更新過濾列表\n    this.updateFilteredTemplates();\n    // 關閉表單\n    this.showAddForm = false;\n    alert(`模板 \"${name}\" 已成功創建！`);\n  }\n  // 查看模板\n  onSelectTemplate(template) {\n    this.selectedTemplate = template;\n    this.selectTemplate.emit(template);\n  }\n  // 刪除模板\n  onDeleteTemplate(templateID) {\n    if (confirm('確定刪除此模板？')) {\n      // TODO: 替換為實際的API調用\n      this.deleteTemplateById(templateID);\n    }\n  }\n  // TODO: 替換為實際的API調用\n  deleteTemplateById(templateID) {\n    this.templates = this.templates.filter(t => t.TemplateID !== templateID);\n    this.templateDetails = this.templateDetails.filter(d => d.TemplateID !== templateID);\n    this.updateFilteredTemplates();\n    // 如果當前查看的模板被刪除，關閉詳情\n    if (this.selectedTemplate?.TemplateID === templateID) {\n      this.selectedTemplate = null;\n    }\n    alert('模板已刪除');\n  }\n  // 關閉模板詳情\n  closeTemplateDetail() {\n    this.selectedTemplate = null;\n  }\n  // 取得當前選中模板的詳情\n  get currentTemplateDetails() {\n    if (!this.selectedTemplate) {\n      return [];\n    }\n    return this.templateDetails.filter(d => d.TemplateID === this.selectedTemplate.TemplateID);\n  }\n  // TrackBy函數用於優化ngFor性能\n  trackByTemplateId(index, template) {\n    return template.TemplateID || index;\n  }\n  static {\n    this.ɵfac = function TemplateViewerComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || TemplateViewerComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: TemplateViewerComponent,\n      selectors: [[\"app-template-viewer\"]],\n      inputs: {\n        availableData: \"availableData\"\n      },\n      outputs: {\n        selectTemplate: \"selectTemplate\"\n      },\n      standalone: true,\n      features: [i0.ɵɵNgOnChangesFeature, i0.ɵɵStandaloneFeature],\n      decls: 31,\n      vars: 7,\n      consts: [[\"noDetails\", \"\"], [1, \"template-viewer-modal\"], [1, \"template-viewer-header\"], [1, \"d-flex\", \"justify-content-between\", \"align-items-center\", \"mb-3\"], [1, \"mb-0\"], [1, \"btn\", \"btn-success\", \"btn-sm\", 3, \"click\"], [1, \"fas\", \"fa-plus\", \"mr-1\"], [1, \"search-container\", \"mb-3\"], [1, \"input-group\"], [\"type\", \"text\", \"placeholder\", \"\\u641C\\u5C0B\\u6A21\\u677F\\u540D\\u7A31\\u6216\\u63CF\\u8FF0...\", 1, \"form-control\", 3, \"ngModelChange\", \"input\", \"keyup.enter\", \"ngModel\"], [1, \"input-group-append\"], [\"type\", \"button\", 1, \"btn\", \"btn-outline-secondary\", 3, \"click\"], [1, \"fas\", \"fa-search\"], [\"class\", \"btn btn-outline-secondary\", \"type\", \"button\", 3, \"click\", 4, \"ngIf\"], [1, \"template-list\"], [\"class\", \"search-results-info mb-2\", 4, \"ngIf\"], [1, \"table-responsive\"], [1, \"table\", \"table-striped\", \"table-hover\"], [1, \"thead-light\"], [\"width\", \"30%\"], [\"width\", \"50%\"], [\"width\", \"20%\", 1, \"text-center\"], [4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [4, \"ngIf\"], [\"class\", \"template-detail-modal\", 4, \"ngIf\"], [1, \"fas\", \"fa-times\"], [1, \"search-results-info\", \"mb-2\"], [1, \"text-muted\"], [1, \"text-center\"], [\"role\", \"group\", 1, \"btn-group\", \"btn-group-sm\"], [\"title\", \"\\u67E5\\u770B\\u8A73\\u60C5\", 1, \"btn\", \"btn-info\", 3, \"click\"], [1, \"fas\", \"fa-eye\"], [\"class\", \"btn btn-danger\", \"title\", \"\\u522A\\u9664\\u6A21\\u677F\", 3, \"click\", 4, \"ngIf\"], [\"title\", \"\\u522A\\u9664\\u6A21\\u677F\", 1, \"btn\", \"btn-danger\", 3, \"click\"], [1, \"fas\", \"fa-trash\"], [\"colspan\", \"3\", 1, \"text-center\", \"py-4\"], [1, \"empty-state\"], [1, \"fas\", \"fa-folder-open\", \"fa-2x\", \"text-muted\", \"mb-2\"], [1, \"text-muted\", \"mb-0\"], [\"class\", \"text-muted\", 4, \"ngIf\"], [\"href\", \"javascript:void(0)\", 3, \"click\"], [1, \"template-detail-modal\"], [1, \"template-detail-header\", \"d-flex\", \"justify-content-between\", \"align-items-center\"], [1, \"fas\", \"fa-file-alt\", \"mr-2\"], [1, \"btn\", \"btn-outline-secondary\", \"btn-sm\", 3, \"click\"], [1, \"template-detail-content\"], [\"class\", \"template-description mb-3\", 4, \"ngIf\"], [1, \"template-items\"], [1, \"mb-2\"], [1, \"fas\", \"fa-list\", \"mr-1\"], [\"class\", \"detail-list\", 4, \"ngIf\", \"ngIfElse\"], [1, \"template-description\", \"mb-3\"], [1, \"detail-list\"], [\"class\", \"detail-item d-flex align-items-center py-2 border-bottom\", 4, \"ngFor\", \"ngForOf\"], [1, \"detail-item\", \"d-flex\", \"align-items-center\", \"py-2\", \"border-bottom\"], [1, \"detail-index\"], [1, \"badge\", \"badge-light\"], [1, \"detail-content\", \"flex-grow-1\", \"ml-2\"], [1, \"detail-field\"], [1, \"detail-value\", \"text-muted\"], [1, \"text-center\", \"py-3\"], [1, \"fas\", \"fa-inbox\", \"fa-2x\", \"text-muted\", \"mb-2\"]],\n      template: function TemplateViewerComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 1)(1, \"div\", 2)(2, \"div\", 3)(3, \"h5\", 4);\n          i0.ɵɵtext(4, \"\\u6A21\\u677F\\u7BA1\\u7406\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(5, \"button\", 5);\n          i0.ɵɵlistener(\"click\", function TemplateViewerComponent_Template_button_click_5_listener() {\n            return ctx.onAddTemplate();\n          });\n          i0.ɵɵelement(6, \"i\", 6);\n          i0.ɵɵtext(7, \"\\u65B0\\u589E \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(8, \"div\", 7)(9, \"div\", 8)(10, \"input\", 9);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function TemplateViewerComponent_Template_input_ngModelChange_10_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.searchKeyword, $event) || (ctx.searchKeyword = $event);\n            return $event;\n          });\n          i0.ɵɵlistener(\"input\", function TemplateViewerComponent_Template_input_input_10_listener() {\n            return ctx.onSearch();\n          })(\"keyup.enter\", function TemplateViewerComponent_Template_input_keyup_enter_10_listener() {\n            return ctx.onSearch();\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(11, \"div\", 10)(12, \"button\", 11);\n          i0.ɵɵlistener(\"click\", function TemplateViewerComponent_Template_button_click_12_listener() {\n            return ctx.onSearch();\n          });\n          i0.ɵɵelement(13, \"i\", 12);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(14, TemplateViewerComponent_button_14_Template, 2, 0, \"button\", 13);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(15, \"div\", 14);\n          i0.ɵɵtemplate(16, TemplateViewerComponent_div_16_Template, 3, 2, \"div\", 15);\n          i0.ɵɵelementStart(17, \"div\", 16)(18, \"table\", 17)(19, \"thead\", 18)(20, \"tr\")(21, \"th\", 19);\n          i0.ɵɵtext(22, \"\\u6A21\\u677F\\u540D\\u7A31\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(23, \"th\", 20);\n          i0.ɵɵtext(24, \"\\u63CF\\u8FF0\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(25, \"th\", 21);\n          i0.ɵɵtext(26, \"\\u64CD\\u4F5C\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(27, \"tbody\");\n          i0.ɵɵtemplate(28, TemplateViewerComponent_tr_28_Template, 13, 3, \"tr\", 22)(29, TemplateViewerComponent_tr_29_Template, 7, 2, \"tr\", 23);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵtemplate(30, TemplateViewerComponent_div_30_Template, 17, 5, \"div\", 24);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(10);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchKeyword);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", ctx.searchKeyword);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.searchKeyword);\n          i0.ɵɵadvance(12);\n          i0.ɵɵproperty(\"ngForOf\", ctx.filteredTemplates)(\"ngForTrackBy\", ctx.trackByTemplateId);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.filteredTemplates || ctx.filteredTemplates.length === 0);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.selectedTemplate);\n        }\n      },\n      dependencies: [CommonModule, i1.NgForOf, i1.NgIf, FormsModule, i2.DefaultValueAccessor, i2.NgControlStatus, i2.NgModel],\n      styles: [\".template-viewer-modal[_ngcontent-%COMP%] {\\n  background: #fff;\\n  border-radius: 12px;\\n  padding: 2rem;\\n  min-width: 500px;\\n  max-width: 800px;\\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);\\n}\\n\\n.template-viewer-header[_ngcontent-%COMP%] {\\n  border-bottom: 2px solid #f0f0f0;\\n  padding-bottom: 1rem;\\n  margin-bottom: 1.5rem;\\n}\\n.template-viewer-header[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%] {\\n  color: #333;\\n  font-weight: 600;\\n}\\n\\n.search-container[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%] {\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\\n  border-radius: 8px;\\n  overflow: hidden;\\n}\\n.search-container[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .form-control[_ngcontent-%COMP%] {\\n  border: none;\\n  padding: 0.75rem 1rem;\\n  font-size: 0.95rem;\\n}\\n.search-container[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .form-control[_ngcontent-%COMP%]:focus {\\n  box-shadow: none;\\n  border-color: transparent;\\n}\\n.search-container[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .form-control[_ngcontent-%COMP%]::placeholder {\\n  color: #999;\\n  font-style: italic;\\n}\\n.search-container[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .input-group-append[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%] {\\n  border: none;\\n  background: #f8f9fa;\\n  color: #6c757d;\\n  padding: 0.75rem 1rem;\\n  transition: all 0.2s ease;\\n}\\n.search-container[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .input-group-append[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]:hover {\\n  background: #e9ecef;\\n  color: #495057;\\n}\\n.search-container[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .input-group-append[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]:focus {\\n  box-shadow: none;\\n}\\n\\n.search-results-info[_ngcontent-%COMP%] {\\n  padding: 0.5rem 0;\\n  border-left: 3px solid #007bff;\\n  padding-left: 0.75rem;\\n  background: #f8f9ff;\\n  border-radius: 4px;\\n}\\n\\n.table-responsive[_ngcontent-%COMP%] {\\n  border-radius: 8px;\\n  overflow: hidden;\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);\\n}\\n\\n.table[_ngcontent-%COMP%] {\\n  margin-bottom: 0;\\n}\\n.table[_ngcontent-%COMP%]   thead.thead-light[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);\\n}\\n.table[_ngcontent-%COMP%]   thead.thead-light[_ngcontent-%COMP%]   th[_ngcontent-%COMP%] {\\n  border: none;\\n  font-weight: 600;\\n  color: #495057;\\n  padding: 1rem 0.75rem;\\n  font-size: 0.9rem;\\n  text-transform: uppercase;\\n  letter-spacing: 0.5px;\\n}\\n.table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%] {\\n  transition: all 0.2s ease;\\n}\\n.table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]:hover {\\n  background-color: #f8f9ff;\\n  transform: translateY(-1px);\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\\n}\\n.table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   td[_ngcontent-%COMP%] {\\n  padding: 1rem 0.75rem;\\n  border-color: #f0f0f0;\\n  vertical-align: middle;\\n}\\n.table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%] {\\n  color: #333;\\n  font-weight: 600;\\n}\\n.table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]   .text-muted[_ngcontent-%COMP%] {\\n  font-size: 0.9rem;\\n}\\n\\n.btn-group-sm[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%] {\\n  padding: 0.375rem 0.75rem;\\n  font-size: 0.875rem;\\n  border-radius: 4px;\\n  font-weight: 500;\\n  transition: all 0.2s ease;\\n}\\n.btn-group-sm[_ngcontent-%COMP%]   .btn.btn-info[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);\\n  border: none;\\n}\\n.btn-group-sm[_ngcontent-%COMP%]   .btn.btn-info[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-1px);\\n  box-shadow: 0 4px 12px rgba(23, 162, 184, 0.3);\\n}\\n.btn-group-sm[_ngcontent-%COMP%]   .btn.btn-danger[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);\\n  border: none;\\n}\\n.btn-group-sm[_ngcontent-%COMP%]   .btn.btn-danger[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-1px);\\n  box-shadow: 0 4px 12px rgba(220, 53, 69, 0.3);\\n}\\n.btn-group-sm[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  margin-right: 0.25rem;\\n}\\n\\n.empty-state[_ngcontent-%COMP%] {\\n  padding: 2rem;\\n}\\n.empty-state[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  display: block;\\n  margin: 0 auto 1rem;\\n  opacity: 0.5;\\n}\\n.empty-state[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  font-size: 1.1rem;\\n  margin-bottom: 0.5rem;\\n}\\n.empty-state[_ngcontent-%COMP%]   a[_ngcontent-%COMP%] {\\n  color: #007bff;\\n  text-decoration: none;\\n}\\n.empty-state[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]:hover {\\n  text-decoration: underline;\\n}\\n\\n.template-detail-modal[_ngcontent-%COMP%] {\\n  margin-top: 1.5rem;\\n  background: #fff;\\n  border: 2px solid #e9ecef;\\n  border-radius: 12px;\\n  overflow: hidden;\\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);\\n  animation: _ngcontent-%COMP%_slideDown 0.3s ease-out;\\n}\\n\\n@keyframes _ngcontent-%COMP%_slideDown {\\n  from {\\n    opacity: 0;\\n    transform: translateY(-20px);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: translateY(0);\\n  }\\n}\\n.template-detail-header[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);\\n  padding: 1rem 1.5rem;\\n  border-bottom: 1px solid #dee2e6;\\n}\\n.template-detail-header[_ngcontent-%COMP%]   h6[_ngcontent-%COMP%] {\\n  color: #495057;\\n  font-weight: 600;\\n}\\n.template-detail-header[_ngcontent-%COMP%]   h6[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  color: #6c757d;\\n}\\n.template-detail-header[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  padding: 0.375rem 0.75rem;\\n}\\n\\n.template-detail-content[_ngcontent-%COMP%] {\\n  padding: 1.5rem;\\n  max-height: 400px;\\n  overflow-y: auto;\\n}\\n.template-detail-content[_ngcontent-%COMP%]   .template-description[_ngcontent-%COMP%] {\\n  background: #f8f9ff;\\n  padding: 1rem;\\n  border-radius: 8px;\\n  border-left: 4px solid #007bff;\\n}\\n.template-detail-content[_ngcontent-%COMP%]   .template-description[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%] {\\n  color: #495057;\\n}\\n.template-detail-content[_ngcontent-%COMP%]   .template-items[_ngcontent-%COMP%]   h6[_ngcontent-%COMP%] {\\n  color: #495057;\\n  font-weight: 600;\\n  padding-bottom: 0.5rem;\\n  border-bottom: 1px solid #e9ecef;\\n}\\n.template-detail-content[_ngcontent-%COMP%]   .template-items[_ngcontent-%COMP%]   h6[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  color: #6c757d;\\n}\\n\\n.detail-list[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%] {\\n  transition: all 0.2s ease;\\n}\\n.detail-list[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]:hover {\\n  background: #f8f9ff;\\n  border-radius: 6px;\\n  margin: 0 -0.5rem;\\n  padding-left: 0.5rem !important;\\n  padding-right: 0.5rem !important;\\n}\\n.detail-list[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]:last-child {\\n  border-bottom: none;\\n}\\n.detail-list[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   .detail-index[_ngcontent-%COMP%]   .badge[_ngcontent-%COMP%] {\\n  background: #e9ecef;\\n  color: #6c757d;\\n  font-weight: 500;\\n  padding: 0.375rem 0.5rem;\\n  border-radius: 50%;\\n  min-width: 2rem;\\n  text-align: center;\\n}\\n.detail-list[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   .detail-content[_ngcontent-%COMP%]   .detail-field[_ngcontent-%COMP%] {\\n  font-size: 0.9rem;\\n  margin-bottom: 0.25rem;\\n}\\n.detail-list[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   .detail-content[_ngcontent-%COMP%]   .detail-field[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%] {\\n  color: #495057;\\n}\\n.detail-list[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   .detail-content[_ngcontent-%COMP%]   .detail-value[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  line-height: 1.4;\\n  word-break: break-word;\\n}\\n/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbInRlbXBsYXRlLXZpZXdlci5jb21wb25lbnQuc2NzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFBQTtFQUNJLGdCQUFBO0VBQ0EsbUJBQUE7RUFDQSxhQUFBO0VBQ0EsZ0JBQUE7RUFDQSxnQkFBQTtFQUNBLHlDQUFBO0FBQ0o7O0FBRUE7RUFDSSxnQ0FBQTtFQUNBLG9CQUFBO0VBQ0EscUJBQUE7QUFDSjtBQUNJO0VBQ0ksV0FBQTtFQUNBLGdCQUFBO0FBQ1I7O0FBS0k7RUFDSSx3Q0FBQTtFQUNBLGtCQUFBO0VBQ0EsZ0JBQUE7QUFGUjtBQUlRO0VBQ0ksWUFBQTtFQUNBLHFCQUFBO0VBQ0Esa0JBQUE7QUFGWjtBQUlZO0VBQ0ksZ0JBQUE7RUFDQSx5QkFBQTtBQUZoQjtBQUtZO0VBQ0ksV0FBQTtFQUNBLGtCQUFBO0FBSGhCO0FBUVk7RUFDSSxZQUFBO0VBQ0EsbUJBQUE7RUFDQSxjQUFBO0VBQ0EscUJBQUE7RUFDQSx5QkFBQTtBQU5oQjtBQVFnQjtFQUNJLG1CQUFBO0VBQ0EsY0FBQTtBQU5wQjtBQVNnQjtFQUNJLGdCQUFBO0FBUHBCOztBQWVBO0VBQ0ksaUJBQUE7RUFDQSw4QkFBQTtFQUNBLHFCQUFBO0VBQ0EsbUJBQUE7RUFDQSxrQkFBQTtBQVpKOztBQWdCQTtFQUNJLGtCQUFBO0VBQ0EsZ0JBQUE7RUFDQSx5Q0FBQTtBQWJKOztBQWdCQTtFQUNJLGdCQUFBO0FBYko7QUFlSTtFQUNJLDZEQUFBO0FBYlI7QUFlUTtFQUNJLFlBQUE7RUFDQSxnQkFBQTtFQUNBLGNBQUE7RUFDQSxxQkFBQTtFQUNBLGlCQUFBO0VBQ0EseUJBQUE7RUFDQSxxQkFBQTtBQWJaO0FBa0JRO0VBQ0kseUJBQUE7QUFoQlo7QUFrQlk7RUFDSSx5QkFBQTtFQUNBLDJCQUFBO0VBQ0Esd0NBQUE7QUFoQmhCO0FBbUJZO0VBQ0kscUJBQUE7RUFDQSxxQkFBQTtFQUNBLHNCQUFBO0FBakJoQjtBQW1CZ0I7RUFDSSxXQUFBO0VBQ0EsZ0JBQUE7QUFqQnBCO0FBb0JnQjtFQUNJLGlCQUFBO0FBbEJwQjs7QUEyQkk7RUFDSSx5QkFBQTtFQUNBLG1CQUFBO0VBQ0Esa0JBQUE7RUFDQSxnQkFBQTtFQUNBLHlCQUFBO0FBeEJSO0FBMEJRO0VBQ0ksNkRBQUE7RUFDQSxZQUFBO0FBeEJaO0FBMEJZO0VBQ0ksMkJBQUE7RUFDQSw4Q0FBQTtBQXhCaEI7QUE0QlE7RUFDSSw2REFBQTtFQUNBLFlBQUE7QUExQlo7QUE0Qlk7RUFDSSwyQkFBQTtFQUNBLDZDQUFBO0FBMUJoQjtBQThCUTtFQUNJLHFCQUFBO0FBNUJaOztBQWtDQTtFQUNJLGFBQUE7QUEvQko7QUFpQ0k7RUFDSSxjQUFBO0VBQ0EsbUJBQUE7RUFDQSxZQUFBO0FBL0JSO0FBa0NJO0VBQ0ksaUJBQUE7RUFDQSxxQkFBQTtBQWhDUjtBQW1DSTtFQUNJLGNBQUE7RUFDQSxxQkFBQTtBQWpDUjtBQW1DUTtFQUNJLDBCQUFBO0FBakNaOztBQXVDQTtFQUNJLGtCQUFBO0VBQ0EsZ0JBQUE7RUFDQSx5QkFBQTtFQUNBLG1CQUFBO0VBQ0EsZ0JBQUE7RUFDQSx5Q0FBQTtFQUNBLGtDQUFBO0FBcENKOztBQXVDQTtFQUNJO0lBQ0ksVUFBQTtJQUNBLDRCQUFBO0VBcENOO0VBdUNFO0lBQ0ksVUFBQTtJQUNBLHdCQUFBO0VBckNOO0FBQ0Y7QUF3Q0E7RUFDSSw2REFBQTtFQUNBLG9CQUFBO0VBQ0EsZ0NBQUE7QUF0Q0o7QUF3Q0k7RUFDSSxjQUFBO0VBQ0EsZ0JBQUE7QUF0Q1I7QUF3Q1E7RUFDSSxjQUFBO0FBdENaO0FBMENJO0VBQ0ksbUJBQUE7RUFDQSx5QkFBQTtBQXhDUjs7QUE0Q0E7RUFDSSxlQUFBO0VBQ0EsaUJBQUE7RUFDQSxnQkFBQTtBQXpDSjtBQTJDSTtFQUNJLG1CQUFBO0VBQ0EsYUFBQTtFQUNBLGtCQUFBO0VBQ0EsOEJBQUE7QUF6Q1I7QUEyQ1E7RUFDSSxjQUFBO0FBekNaO0FBOENRO0VBQ0ksY0FBQTtFQUNBLGdCQUFBO0VBQ0Esc0JBQUE7RUFDQSxnQ0FBQTtBQTVDWjtBQThDWTtFQUNJLGNBQUE7QUE1Q2hCOztBQW1ESTtFQUNJLHlCQUFBO0FBaERSO0FBa0RRO0VBQ0ksbUJBQUE7RUFDQSxrQkFBQTtFQUNBLGlCQUFBO0VBQ0EsK0JBQUE7RUFDQSxnQ0FBQTtBQWhEWjtBQW1EUTtFQUNJLG1CQUFBO0FBakRaO0FBcURZO0VBQ0ksbUJBQUE7RUFDQSxjQUFBO0VBQ0EsZ0JBQUE7RUFDQSx3QkFBQTtFQUNBLGtCQUFBO0VBQ0EsZUFBQTtFQUNBLGtCQUFBO0FBbkRoQjtBQXdEWTtFQUNJLGlCQUFBO0VBQ0Esc0JBQUE7QUF0RGhCO0FBd0RnQjtFQUNJLGNBQUE7QUF0RHBCO0FBMERZO0VBQ0ksbUJBQUE7RUFDQSxnQkFBQTtFQUNBLHNCQUFBO0FBeERoQiIsImZpbGUiOiJ0ZW1wbGF0ZS12aWV3ZXIuY29tcG9uZW50LnNjc3MiLCJzb3VyY2VzQ29udGVudCI6WyIudGVtcGxhdGUtdmlld2VyLW1vZGFsIHtcclxuICAgIGJhY2tncm91bmQ6ICNmZmY7XHJcbiAgICBib3JkZXItcmFkaXVzOiAxMnB4O1xyXG4gICAgcGFkZGluZzogMnJlbTtcclxuICAgIG1pbi13aWR0aDogNTAwcHg7XHJcbiAgICBtYXgtd2lkdGg6IDgwMHB4O1xyXG4gICAgYm94LXNoYWRvdzogMCA0cHggMjBweCByZ2JhKDAsIDAsIDAsIDAuMSk7XHJcbn1cclxuXHJcbi50ZW1wbGF0ZS12aWV3ZXItaGVhZGVyIHtcclxuICAgIGJvcmRlci1ib3R0b206IDJweCBzb2xpZCAjZjBmMGYwO1xyXG4gICAgcGFkZGluZy1ib3R0b206IDFyZW07XHJcbiAgICBtYXJnaW4tYm90dG9tOiAxLjVyZW07XHJcblxyXG4gICAgaDUge1xyXG4gICAgICAgIGNvbG9yOiAjMzMzO1xyXG4gICAgICAgIGZvbnQtd2VpZ2h0OiA2MDA7XHJcbiAgICB9XHJcbn1cclxuXHJcbi8vIOaQnOWwi+WuueWZqOaoo+W8j1xyXG4uc2VhcmNoLWNvbnRhaW5lciB7XHJcbiAgICAuaW5wdXQtZ3JvdXAge1xyXG4gICAgICAgIGJveC1zaGFkb3c6IDAgMnB4IDhweCByZ2JhKDAsIDAsIDAsIDAuMSk7XHJcbiAgICAgICAgYm9yZGVyLXJhZGl1czogOHB4O1xyXG4gICAgICAgIG92ZXJmbG93OiBoaWRkZW47XHJcblxyXG4gICAgICAgIC5mb3JtLWNvbnRyb2wge1xyXG4gICAgICAgICAgICBib3JkZXI6IG5vbmU7XHJcbiAgICAgICAgICAgIHBhZGRpbmc6IDAuNzVyZW0gMXJlbTtcclxuICAgICAgICAgICAgZm9udC1zaXplOiAwLjk1cmVtO1xyXG5cclxuICAgICAgICAgICAgJjpmb2N1cyB7XHJcbiAgICAgICAgICAgICAgICBib3gtc2hhZG93OiBub25lO1xyXG4gICAgICAgICAgICAgICAgYm9yZGVyLWNvbG9yOiB0cmFuc3BhcmVudDtcclxuICAgICAgICAgICAgfVxyXG5cclxuICAgICAgICAgICAgJjo6cGxhY2Vob2xkZXIge1xyXG4gICAgICAgICAgICAgICAgY29sb3I6ICM5OTk7XHJcbiAgICAgICAgICAgICAgICBmb250LXN0eWxlOiBpdGFsaWM7XHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICB9XHJcblxyXG4gICAgICAgIC5pbnB1dC1ncm91cC1hcHBlbmQge1xyXG4gICAgICAgICAgICAuYnRuIHtcclxuICAgICAgICAgICAgICAgIGJvcmRlcjogbm9uZTtcclxuICAgICAgICAgICAgICAgIGJhY2tncm91bmQ6ICNmOGY5ZmE7XHJcbiAgICAgICAgICAgICAgICBjb2xvcjogIzZjNzU3ZDtcclxuICAgICAgICAgICAgICAgIHBhZGRpbmc6IDAuNzVyZW0gMXJlbTtcclxuICAgICAgICAgICAgICAgIHRyYW5zaXRpb246IGFsbCAwLjJzIGVhc2U7XHJcblxyXG4gICAgICAgICAgICAgICAgJjpob3ZlciB7XHJcbiAgICAgICAgICAgICAgICAgICAgYmFja2dyb3VuZDogI2U5ZWNlZjtcclxuICAgICAgICAgICAgICAgICAgICBjb2xvcjogIzQ5NTA1NztcclxuICAgICAgICAgICAgICAgIH1cclxuXHJcbiAgICAgICAgICAgICAgICAmOmZvY3VzIHtcclxuICAgICAgICAgICAgICAgICAgICBib3gtc2hhZG93OiBub25lO1xyXG4gICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgfVxyXG4gICAgfVxyXG59XHJcblxyXG4vLyDmkJzlsIvntZDmnpzos4foqIpcclxuLnNlYXJjaC1yZXN1bHRzLWluZm8ge1xyXG4gICAgcGFkZGluZzogMC41cmVtIDA7XHJcbiAgICBib3JkZXItbGVmdDogM3B4IHNvbGlkICMwMDdiZmY7XHJcbiAgICBwYWRkaW5nLWxlZnQ6IDAuNzVyZW07XHJcbiAgICBiYWNrZ3JvdW5kOiAjZjhmOWZmO1xyXG4gICAgYm9yZGVyLXJhZGl1czogNHB4O1xyXG59XHJcblxyXG4vLyDooajmoLzmqKPlvI9cclxuLnRhYmxlLXJlc3BvbnNpdmUge1xyXG4gICAgYm9yZGVyLXJhZGl1czogOHB4O1xyXG4gICAgb3ZlcmZsb3c6IGhpZGRlbjtcclxuICAgIGJveC1zaGFkb3c6IDAgMnB4IDhweCByZ2JhKDAsIDAsIDAsIDAuMDUpO1xyXG59XHJcblxyXG4udGFibGUge1xyXG4gICAgbWFyZ2luLWJvdHRvbTogMDtcclxuXHJcbiAgICB0aGVhZC50aGVhZC1saWdodCB7XHJcbiAgICAgICAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDEzNWRlZywgI2Y4ZjlmYSAwJSwgI2U5ZWNlZiAxMDAlKTtcclxuXHJcbiAgICAgICAgdGgge1xyXG4gICAgICAgICAgICBib3JkZXI6IG5vbmU7XHJcbiAgICAgICAgICAgIGZvbnQtd2VpZ2h0OiA2MDA7XHJcbiAgICAgICAgICAgIGNvbG9yOiAjNDk1MDU3O1xyXG4gICAgICAgICAgICBwYWRkaW5nOiAxcmVtIDAuNzVyZW07XHJcbiAgICAgICAgICAgIGZvbnQtc2l6ZTogMC45cmVtO1xyXG4gICAgICAgICAgICB0ZXh0LXRyYW5zZm9ybTogdXBwZXJjYXNlO1xyXG4gICAgICAgICAgICBsZXR0ZXItc3BhY2luZzogMC41cHg7XHJcbiAgICAgICAgfVxyXG4gICAgfVxyXG5cclxuICAgIHRib2R5IHtcclxuICAgICAgICB0ciB7XHJcbiAgICAgICAgICAgIHRyYW5zaXRpb246IGFsbCAwLjJzIGVhc2U7XHJcblxyXG4gICAgICAgICAgICAmOmhvdmVyIHtcclxuICAgICAgICAgICAgICAgIGJhY2tncm91bmQtY29sb3I6ICNmOGY5ZmY7XHJcbiAgICAgICAgICAgICAgICB0cmFuc2Zvcm06IHRyYW5zbGF0ZVkoLTFweCk7XHJcbiAgICAgICAgICAgICAgICBib3gtc2hhZG93OiAwIDJweCA4cHggcmdiYSgwLCAwLCAwLCAwLjEpO1xyXG4gICAgICAgICAgICB9XHJcblxyXG4gICAgICAgICAgICB0ZCB7XHJcbiAgICAgICAgICAgICAgICBwYWRkaW5nOiAxcmVtIDAuNzVyZW07XHJcbiAgICAgICAgICAgICAgICBib3JkZXItY29sb3I6ICNmMGYwZjA7XHJcbiAgICAgICAgICAgICAgICB2ZXJ0aWNhbC1hbGlnbjogbWlkZGxlO1xyXG5cclxuICAgICAgICAgICAgICAgIHN0cm9uZyB7XHJcbiAgICAgICAgICAgICAgICAgICAgY29sb3I6ICMzMzM7XHJcbiAgICAgICAgICAgICAgICAgICAgZm9udC13ZWlnaHQ6IDYwMDtcclxuICAgICAgICAgICAgICAgIH1cclxuXHJcbiAgICAgICAgICAgICAgICAudGV4dC1tdXRlZCB7XHJcbiAgICAgICAgICAgICAgICAgICAgZm9udC1zaXplOiAwLjlyZW07XHJcbiAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICB9XHJcbiAgICB9XHJcbn1cclxuXHJcbi8vIOaMiemIlee1hOaoo+W8j1xyXG4uYnRuLWdyb3VwLXNtIHtcclxuICAgIC5idG4ge1xyXG4gICAgICAgIHBhZGRpbmc6IDAuMzc1cmVtIDAuNzVyZW07XHJcbiAgICAgICAgZm9udC1zaXplOiAwLjg3NXJlbTtcclxuICAgICAgICBib3JkZXItcmFkaXVzOiA0cHg7XHJcbiAgICAgICAgZm9udC13ZWlnaHQ6IDUwMDtcclxuICAgICAgICB0cmFuc2l0aW9uOiBhbGwgMC4ycyBlYXNlO1xyXG5cclxuICAgICAgICAmLmJ0bi1pbmZvIHtcclxuICAgICAgICAgICAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDEzNWRlZywgIzE3YTJiOCAwJSwgIzEzODQ5NiAxMDAlKTtcclxuICAgICAgICAgICAgYm9yZGVyOiBub25lO1xyXG5cclxuICAgICAgICAgICAgJjpob3ZlciB7XHJcbiAgICAgICAgICAgICAgICB0cmFuc2Zvcm06IHRyYW5zbGF0ZVkoLTFweCk7XHJcbiAgICAgICAgICAgICAgICBib3gtc2hhZG93OiAwIDRweCAxMnB4IHJnYmEoMjMsIDE2MiwgMTg0LCAwLjMpO1xyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgfVxyXG5cclxuICAgICAgICAmLmJ0bi1kYW5nZXIge1xyXG4gICAgICAgICAgICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCAjZGMzNTQ1IDAlLCAjYzgyMzMzIDEwMCUpO1xyXG4gICAgICAgICAgICBib3JkZXI6IG5vbmU7XHJcblxyXG4gICAgICAgICAgICAmOmhvdmVyIHtcclxuICAgICAgICAgICAgICAgIHRyYW5zZm9ybTogdHJhbnNsYXRlWSgtMXB4KTtcclxuICAgICAgICAgICAgICAgIGJveC1zaGFkb3c6IDAgNHB4IDEycHggcmdiYSgyMjAsIDUzLCA2OSwgMC4zKTtcclxuICAgICAgICAgICAgfVxyXG4gICAgICAgIH1cclxuXHJcbiAgICAgICAgaSB7XHJcbiAgICAgICAgICAgIG1hcmdpbi1yaWdodDogMC4yNXJlbTtcclxuICAgICAgICB9XHJcbiAgICB9XHJcbn1cclxuXHJcbi8vIOepuueLgOaFi+aoo+W8j1xyXG4uZW1wdHktc3RhdGUge1xyXG4gICAgcGFkZGluZzogMnJlbTtcclxuXHJcbiAgICBpIHtcclxuICAgICAgICBkaXNwbGF5OiBibG9jaztcclxuICAgICAgICBtYXJnaW46IDAgYXV0byAxcmVtO1xyXG4gICAgICAgIG9wYWNpdHk6IDAuNTtcclxuICAgIH1cclxuXHJcbiAgICBwIHtcclxuICAgICAgICBmb250LXNpemU6IDEuMXJlbTtcclxuICAgICAgICBtYXJnaW4tYm90dG9tOiAwLjVyZW07XHJcbiAgICB9XHJcblxyXG4gICAgYSB7XHJcbiAgICAgICAgY29sb3I6ICMwMDdiZmY7XHJcbiAgICAgICAgdGV4dC1kZWNvcmF0aW9uOiBub25lO1xyXG5cclxuICAgICAgICAmOmhvdmVyIHtcclxuICAgICAgICAgICAgdGV4dC1kZWNvcmF0aW9uOiB1bmRlcmxpbmU7XHJcbiAgICAgICAgfVxyXG4gICAgfVxyXG59XHJcblxyXG4vLyDmqKHmnb/oqbPmg4XmqKHmhYvmoYZcclxuLnRlbXBsYXRlLWRldGFpbC1tb2RhbCB7XHJcbiAgICBtYXJnaW4tdG9wOiAxLjVyZW07XHJcbiAgICBiYWNrZ3JvdW5kOiAjZmZmO1xyXG4gICAgYm9yZGVyOiAycHggc29saWQgI2U5ZWNlZjtcclxuICAgIGJvcmRlci1yYWRpdXM6IDEycHg7XHJcbiAgICBvdmVyZmxvdzogaGlkZGVuO1xyXG4gICAgYm94LXNoYWRvdzogMCA0cHggMjBweCByZ2JhKDAsIDAsIDAsIDAuMSk7XHJcbiAgICBhbmltYXRpb246IHNsaWRlRG93biAwLjNzIGVhc2Utb3V0O1xyXG59XHJcblxyXG5Aa2V5ZnJhbWVzIHNsaWRlRG93biB7XHJcbiAgICBmcm9tIHtcclxuICAgICAgICBvcGFjaXR5OiAwO1xyXG4gICAgICAgIHRyYW5zZm9ybTogdHJhbnNsYXRlWSgtMjBweCk7XHJcbiAgICB9XHJcblxyXG4gICAgdG8ge1xyXG4gICAgICAgIG9wYWNpdHk6IDE7XHJcbiAgICAgICAgdHJhbnNmb3JtOiB0cmFuc2xhdGVZKDApO1xyXG4gICAgfVxyXG59XHJcblxyXG4udGVtcGxhdGUtZGV0YWlsLWhlYWRlciB7XHJcbiAgICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCAjZjhmOWZhIDAlLCAjZTllY2VmIDEwMCUpO1xyXG4gICAgcGFkZGluZzogMXJlbSAxLjVyZW07XHJcbiAgICBib3JkZXItYm90dG9tOiAxcHggc29saWQgI2RlZTJlNjtcclxuXHJcbiAgICBoNiB7XHJcbiAgICAgICAgY29sb3I6ICM0OTUwNTc7XHJcbiAgICAgICAgZm9udC13ZWlnaHQ6IDYwMDtcclxuXHJcbiAgICAgICAgaSB7XHJcbiAgICAgICAgICAgIGNvbG9yOiAjNmM3NTdkO1xyXG4gICAgICAgIH1cclxuICAgIH1cclxuXHJcbiAgICAuYnRuIHtcclxuICAgICAgICBmb250LXNpemU6IDAuODc1cmVtO1xyXG4gICAgICAgIHBhZGRpbmc6IDAuMzc1cmVtIDAuNzVyZW07XHJcbiAgICB9XHJcbn1cclxuXHJcbi50ZW1wbGF0ZS1kZXRhaWwtY29udGVudCB7XHJcbiAgICBwYWRkaW5nOiAxLjVyZW07XHJcbiAgICBtYXgtaGVpZ2h0OiA0MDBweDtcclxuICAgIG92ZXJmbG93LXk6IGF1dG87XHJcblxyXG4gICAgLnRlbXBsYXRlLWRlc2NyaXB0aW9uIHtcclxuICAgICAgICBiYWNrZ3JvdW5kOiAjZjhmOWZmO1xyXG4gICAgICAgIHBhZGRpbmc6IDFyZW07XHJcbiAgICAgICAgYm9yZGVyLXJhZGl1czogOHB4O1xyXG4gICAgICAgIGJvcmRlci1sZWZ0OiA0cHggc29saWQgIzAwN2JmZjtcclxuXHJcbiAgICAgICAgc3Ryb25nIHtcclxuICAgICAgICAgICAgY29sb3I6ICM0OTUwNTc7XHJcbiAgICAgICAgfVxyXG4gICAgfVxyXG5cclxuICAgIC50ZW1wbGF0ZS1pdGVtcyB7XHJcbiAgICAgICAgaDYge1xyXG4gICAgICAgICAgICBjb2xvcjogIzQ5NTA1NztcclxuICAgICAgICAgICAgZm9udC13ZWlnaHQ6IDYwMDtcclxuICAgICAgICAgICAgcGFkZGluZy1ib3R0b206IDAuNXJlbTtcclxuICAgICAgICAgICAgYm9yZGVyLWJvdHRvbTogMXB4IHNvbGlkICNlOWVjZWY7XHJcblxyXG4gICAgICAgICAgICBpIHtcclxuICAgICAgICAgICAgICAgIGNvbG9yOiAjNmM3NTdkO1xyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgfVxyXG4gICAgfVxyXG59XHJcblxyXG4uZGV0YWlsLWxpc3Qge1xyXG4gICAgLmRldGFpbC1pdGVtIHtcclxuICAgICAgICB0cmFuc2l0aW9uOiBhbGwgMC4ycyBlYXNlO1xyXG5cclxuICAgICAgICAmOmhvdmVyIHtcclxuICAgICAgICAgICAgYmFja2dyb3VuZDogI2Y4ZjlmZjtcclxuICAgICAgICAgICAgYm9yZGVyLXJhZGl1czogNnB4O1xyXG4gICAgICAgICAgICBtYXJnaW46IDAgLTAuNXJlbTtcclxuICAgICAgICAgICAgcGFkZGluZy1sZWZ0OiAwLjVyZW0gIWltcG9ydGFudDtcclxuICAgICAgICAgICAgcGFkZGluZy1yaWdodDogMC41cmVtICFpbXBvcnRhbnQ7XHJcbiAgICAgICAgfVxyXG5cclxuICAgICAgICAmOmxhc3QtY2hpbGQge1xyXG4gICAgICAgICAgICBib3JkZXItYm90dG9tOiBub25lO1xyXG4gICAgICAgIH1cclxuXHJcbiAgICAgICAgLmRldGFpbC1pbmRleCB7XHJcbiAgICAgICAgICAgIC5iYWRnZSB7XHJcbiAgICAgICAgICAgICAgICBiYWNrZ3JvdW5kOiAjZTllY2VmO1xyXG4gICAgICAgICAgICAgICAgY29sb3I6ICM2Yzc1N2Q7XHJcbiAgICAgICAgICAgICAgICBmb250LXdlaWdodDogNTAwO1xyXG4gICAgICAgICAgICAgICAgcGFkZGluZzogMC4zNzVyZW0gMC41cmVtO1xyXG4gICAgICAgICAgICAgICAgYm9yZGVyLXJhZGl1czogNTAlO1xyXG4gICAgICAgICAgICAgICAgbWluLXdpZHRoOiAycmVtO1xyXG4gICAgICAgICAgICAgICAgdGV4dC1hbGlnbjogY2VudGVyO1xyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgfVxyXG5cclxuICAgICAgICAuZGV0YWlsLWNvbnRlbnQge1xyXG4gICAgICAgICAgICAuZGV0YWlsLWZpZWxkIHtcclxuICAgICAgICAgICAgICAgIGZvbnQtc2l6ZTogMC45cmVtO1xyXG4gICAgICAgICAgICAgICAgbWFyZ2luLWJvdHRvbTogMC4yNXJlbTtcclxuXHJcbiAgICAgICAgICAgICAgICBzdHJvbmcge1xyXG4gICAgICAgICAgICAgICAgICAgIGNvbG9yOiAjNDk1MDU3O1xyXG4gICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICB9XHJcblxyXG4gICAgICAgICAgICAuZGV0YWlsLXZhbHVlIHtcclxuICAgICAgICAgICAgICAgIGZvbnQtc2l6ZTogMC44NzVyZW07XHJcbiAgICAgICAgICAgICAgICBsaW5lLWhlaWdodDogMS40O1xyXG4gICAgICAgICAgICAgICAgd29yZC1icmVhazogYnJlYWstd29yZDtcclxuICAgICAgICAgICAgfVxyXG4gICAgICAgIH1cclxuICAgIH1cclxufSJdfQ== */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["EventEmitter", "CommonModule", "FormsModule", "i0", "ɵɵelementStart", "ɵɵlistener", "TemplateViewerComponent_button_14_Template_button_click_0_listener", "ɵɵrestoreView", "_r1", "ctx_r1", "ɵɵnextContext", "ɵɵresetView", "clearSearch", "ɵɵelement", "ɵɵelementEnd", "ɵɵtext", "ɵɵadvance", "ɵɵtextInterpolate2", "filteredTemplates", "length", "searchKeyword", "TemplateViewerComponent_tr_28_button_12_Template_button_click_0_listener", "_r5", "tpl_r4", "$implicit", "TemplateID", "onDeleteTemplate", "TemplateViewerComponent_tr_28_Template_button_click_9_listener", "_r3", "onSelectTemplate", "ɵɵtemplate", "TemplateViewerComponent_tr_28_button_12_Template", "ɵɵtextInterpolate", "TemplateName", "Description", "ɵɵproperty", "TemplateViewerComponent_tr_29_small_6_Template_a_click_2_listener", "_r6", "TemplateViewerComponent_tr_29_small_6_Template", "ɵɵtextInterpolate1", "selectedTemplate", "i_r9", "detail_r8", "FieldName", "FieldValue", "TemplateViewerComponent_div_30_div_14_div_1_Template", "currentTemplateDetails", "TemplateViewerComponent_div_30_Template_button_click_5_listener", "_r7", "closeTemplateDetail", "TemplateViewerComponent_div_30_div_9_Template", "TemplateViewerComponent_div_30_div_14_Template", "TemplateViewerComponent_div_30_ng_template_15_Template", "ɵɵtemplateRefExtractor", "noDetails_r10", "TemplateViewerComponent", "constructor", "availableData", "selectTemplate", "templates", "templateDetails", "showAddForm", "newTemplate", "name", "description", "selectedItems", "ngOnInit", "loadTemplates", "updateFilteredTemplates", "ngOnChanges", "TemplateDetailID", "RefID", "ModuleType", "trim", "keyword", "toLowerCase", "filter", "template", "includes", "onSearch", "onAddTemplate", "for<PERSON>ach", "item", "selected", "cancelAddTemplate", "saveNewTemplate", "alert", "createTemplate", "newId", "Math", "max", "map", "t", "push", "index", "detail", "CRequirementID", "ID", "CRequirement", "emit", "templateID", "confirm", "deleteTemplateById", "d", "trackByTemplateId", "selectors", "inputs", "outputs", "standalone", "features", "ɵɵNgOnChangesFeature", "ɵɵStandaloneFeature", "decls", "vars", "consts", "TemplateViewerComponent_Template", "rf", "ctx", "TemplateViewerComponent_Template_button_click_5_listener", "ɵɵtwoWayListener", "TemplateViewerComponent_Template_input_ngModelChange_10_listener", "$event", "ɵɵtwoWayBindingSet", "TemplateViewerComponent_Template_input_input_10_listener", "TemplateViewerComponent_Template_input_keyup_enter_10_listener", "TemplateViewerComponent_Template_button_click_12_listener", "TemplateViewerComponent_button_14_Template", "TemplateViewerComponent_div_16_Template", "TemplateViewerComponent_tr_28_Template", "TemplateViewerComponent_tr_29_Template", "TemplateViewerComponent_div_30_Template", "ɵɵtwoWayProperty", "i1", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "i2", "DefaultValueAccessor", "NgControlStatus", "NgModel", "styles"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\shared\\components\\template-viewer\\template-viewer.component.ts", "C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\shared\\components\\template-viewer\\template-viewer.component.html"], "sourcesContent": ["import { Component, EventEmitter, Input, Output, OnInit, OnChanges } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { FormsModule } from '@angular/forms';\r\n\r\n@Component({\r\n  selector: 'app-template-viewer',\r\n  templateUrl: './template-viewer.component.html',\r\n  styleUrls: ['./template-viewer.component.scss'],\r\n  standalone: true,\r\n  imports: [CommonModule, FormsModule],\r\n})\r\nexport class TemplateViewerComponent implements OnInit, OnChanges {\r\n  @Input() availableData: any[] = []; // 父元件傳入的可選資料\r\n  @Output() selectTemplate = new EventEmitter<Template>();\r\n\r\n  // 內部管理的模板資料\r\n  templates: Template[] = [];\r\n  templateDetails: TemplateDetail[] = [];\r\n  selectedTemplate: Template | null = null;\r\n\r\n  // 查詢功能\r\n  searchKeyword = '';\r\n  filteredTemplates: Template[] = [];\r\n\r\n  // 新增模板表單\r\n  showAddForm = false;\r\n  newTemplate = {\r\n    name: '',\r\n    description: '',\r\n    selectedItems: [] as any[]\r\n  };\r\n\r\n  ngOnInit() {\r\n    this.loadTemplates();\r\n    this.updateFilteredTemplates();\r\n  }\r\n\r\n  ngOnChanges() {\r\n    this.updateFilteredTemplates();\r\n  }\r\n\r\n  // TODO: 替換為實際的API調用\r\n  loadTemplates() {\r\n    // 模擬API調用 - 載入模板列表\r\n    this.templates = [\r\n      { TemplateID: 1, TemplateName: '模板A', Description: '範例模板A' },\r\n      { TemplateID: 2, TemplateName: '模板B', Description: '範例模板B' }\r\n    ];\r\n\r\n    this.templateDetails = [\r\n      { TemplateDetailID: 1, TemplateID: 1, RefID: 101, ModuleType: 'Requirement', FieldName: 'CRequirement', FieldValue: '工程項目A' },\r\n      { TemplateDetailID: 2, TemplateID: 1, RefID: 102, ModuleType: 'Requirement', FieldName: 'CRequirement', FieldValue: '工程項目B' },\r\n      { TemplateDetailID: 3, TemplateID: 2, RefID: 201, ModuleType: 'Requirement', FieldName: 'CRequirement', FieldValue: '工程項目C' }\r\n    ];\r\n  }\r\n\r\n  // 更新過濾後的模板列表\r\n  updateFilteredTemplates() {\r\n    if (!this.searchKeyword.trim()) {\r\n      this.filteredTemplates = [...this.templates];\r\n    } else {\r\n      const keyword = this.searchKeyword.toLowerCase();\r\n      this.filteredTemplates = this.templates.filter(template =>\r\n        template.TemplateName.toLowerCase().includes(keyword) ||\r\n        (template.Description && template.Description.toLowerCase().includes(keyword))\r\n      );\r\n    }\r\n  }\r\n\r\n  // 搜尋模板\r\n  onSearch() {\r\n    this.updateFilteredTemplates();\r\n  }\r\n\r\n  // 清除搜尋\r\n  clearSearch() {\r\n    this.searchKeyword = '';\r\n    this.updateFilteredTemplates();\r\n  }\r\n\r\n\r\n\r\n  // 顯示新增模板表單\r\n  onAddTemplate() {\r\n    this.showAddForm = true;\r\n    this.newTemplate = {\r\n      name: '',\r\n      description: '',\r\n      selectedItems: []\r\n    };\r\n    // 重置可選資料的選擇狀態\r\n    this.availableData.forEach(item => item.selected = false);\r\n  }\r\n\r\n  // 取消新增模板\r\n  cancelAddTemplate() {\r\n    this.showAddForm = false;\r\n    this.newTemplate = {\r\n      name: '',\r\n      description: '',\r\n      selectedItems: []\r\n    };\r\n  }\r\n\r\n  // 儲存新模板\r\n  saveNewTemplate() {\r\n    if (!this.newTemplate.name.trim()) {\r\n      alert('請輸入模板名稱');\r\n      return;\r\n    }\r\n\r\n    const selectedItems = this.availableData.filter(item => item.selected);\r\n    if (selectedItems.length === 0) {\r\n      alert('請至少選擇一個項目');\r\n      return;\r\n    }\r\n\r\n    // TODO: 替換為實際的API調用\r\n    this.createTemplate(this.newTemplate.name, this.newTemplate.description, selectedItems);\r\n  }\r\n\r\n  // TODO: 替換為實際的API調用\r\n  createTemplate(name: string, description: string, selectedItems: any[]) {\r\n    // 生成新的模板ID\r\n    const newId = Math.max(...this.templates.map(t => t.TemplateID || 0), 0) + 1;\r\n\r\n    // 創建新模板\r\n    const newTemplate: Template = {\r\n      TemplateID: newId,\r\n      TemplateName: name.trim(),\r\n      Description: description.trim()\r\n    };\r\n\r\n    // 添加到模板列表\r\n    this.templates.push(newTemplate);\r\n\r\n    // 創建模板詳情\r\n    selectedItems.forEach((item, index) => {\r\n      const detail: TemplateDetail = {\r\n        TemplateDetailID: this.templateDetails.length + index + 1,\r\n        TemplateID: newId,\r\n        RefID: item.CRequirementID || item.ID || 0,\r\n        ModuleType: 'Requirement',\r\n        FieldName: 'CRequirement',\r\n        FieldValue: item.CRequirement || item.name || ''\r\n      };\r\n      this.templateDetails.push(detail);\r\n    });\r\n\r\n    // 更新過濾列表\r\n    this.updateFilteredTemplates();\r\n\r\n    // 關閉表單\r\n    this.showAddForm = false;\r\n    alert(`模板 \"${name}\" 已成功創建！`);\r\n  }\r\n\r\n  // 查看模板\r\n  onSelectTemplate(template: Template) {\r\n    this.selectedTemplate = template;\r\n    this.selectTemplate.emit(template);\r\n  }\r\n\r\n  // 刪除模板\r\n  onDeleteTemplate(templateID: number) {\r\n    if (confirm('確定刪除此模板？')) {\r\n      // TODO: 替換為實際的API調用\r\n      this.deleteTemplateById(templateID);\r\n    }\r\n  }\r\n\r\n  // TODO: 替換為實際的API調用\r\n  deleteTemplateById(templateID: number) {\r\n    this.templates = this.templates.filter(t => t.TemplateID !== templateID);\r\n    this.templateDetails = this.templateDetails.filter(d => d.TemplateID !== templateID);\r\n    this.updateFilteredTemplates();\r\n\r\n    // 如果當前查看的模板被刪除，關閉詳情\r\n    if (this.selectedTemplate?.TemplateID === templateID) {\r\n      this.selectedTemplate = null;\r\n    }\r\n\r\n    alert('模板已刪除');\r\n  }\r\n\r\n  // 關閉模板詳情\r\n  closeTemplateDetail() {\r\n    this.selectedTemplate = null;\r\n  }\r\n\r\n  // 取得當前選中模板的詳情\r\n  get currentTemplateDetails(): TemplateDetail[] {\r\n    if (!this.selectedTemplate) {\r\n      return [];\r\n    }\r\n    return this.templateDetails.filter(d => d.TemplateID === this.selectedTemplate!.TemplateID);\r\n  }\r\n\r\n  // TrackBy函數用於優化ngFor性能\r\n  trackByTemplateId(index: number, template: Template): number {\r\n    return template.TemplateID || index;\r\n  }\r\n}\r\n\r\n// DB 對應型別\r\nexport interface Template {\r\n  TemplateID?: number;\r\n  TemplateName: string;\r\n  Description?: string;\r\n}\r\nexport interface TemplateDetail {\r\n  TemplateDetailID?: number;\r\n  TemplateID: number;\r\n  RefID: number; // 關聯主檔ID\r\n  ModuleType: string;\r\n  FieldName: string;\r\n  FieldValue: string;\r\n}\r\n", "<div class=\"template-viewer-modal\">\r\n  <div class=\"template-viewer-header\">\r\n    <div class=\"d-flex justify-content-between align-items-center mb-3\">\r\n      <h5 class=\"mb-0\">模板管理</h5>\r\n      <button class=\"btn btn-success btn-sm\" (click)=\"onAddTemplate()\">\r\n        <i class=\"fas fa-plus mr-1\"></i>新增\r\n      </button>\r\n    </div>\r\n\r\n    <!-- 搜尋功能 -->\r\n    <div class=\"search-container mb-3\">\r\n      <div class=\"input-group\">\r\n        <input type=\"text\" class=\"form-control\" placeholder=\"搜尋模板名稱或描述...\" [(ngModel)]=\"searchKeyword\"\r\n          (input)=\"onSearch()\" (keyup.enter)=\"onSearch()\">\r\n        <div class=\"input-group-append\">\r\n          <button class=\"btn btn-outline-secondary\" type=\"button\" (click)=\"onSearch()\">\r\n            <i class=\"fas fa-search\"></i>\r\n          </button>\r\n          <button class=\"btn btn-outline-secondary\" type=\"button\" (click)=\"clearSearch()\" *ngIf=\"searchKeyword\">\r\n            <i class=\"fas fa-times\"></i>\r\n          </button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n\r\n\r\n\r\n  <div class=\"template-list\">\r\n    <!-- 搜尋結果統計 -->\r\n    <div class=\"search-results-info mb-2\" *ngIf=\"searchKeyword\">\r\n      <small class=\"text-muted\">\r\n        找到 {{ filteredTemplates.length }} 個符合「{{ searchKeyword }}」的模板\r\n      </small>\r\n    </div>\r\n\r\n    <div class=\"table-responsive\">\r\n      <table class=\"table table-striped table-hover\">\r\n        <thead class=\"thead-light\">\r\n          <tr>\r\n            <th width=\"30%\">模板名稱</th>\r\n            <th width=\"50%\">描述</th>\r\n            <th width=\"20%\" class=\"text-center\">操作</th>\r\n          </tr>\r\n        </thead>\r\n        <tbody>\r\n          <tr *ngFor=\"let tpl of filteredTemplates; trackBy: trackByTemplateId\">\r\n            <td>\r\n              <strong>{{ tpl.TemplateName }}</strong>\r\n            </td>\r\n            <td>\r\n              <span class=\"text-muted\">{{ tpl.Description || '無描述' }}</span>\r\n            </td>\r\n            <td class=\"text-center\">\r\n              <div class=\"btn-group btn-group-sm\" role=\"group\">\r\n                <button class=\"btn btn-info\" (click)=\"onSelectTemplate(tpl)\" title=\"查看詳情\">\r\n                  <i class=\"fas fa-eye\"></i> 查看\r\n                </button>\r\n                <button class=\"btn btn-danger\" (click)=\"tpl.TemplateID && onDeleteTemplate(tpl.TemplateID)\"\r\n                  *ngIf=\"tpl.TemplateID\" title=\"刪除模板\">\r\n                  <i class=\"fas fa-trash\"></i> 刪除\r\n                </button>\r\n              </div>\r\n            </td>\r\n          </tr>\r\n          <tr *ngIf=\"!filteredTemplates || filteredTemplates.length === 0\">\r\n            <td colspan=\"3\" class=\"text-center py-4\">\r\n              <div class=\"empty-state\">\r\n                <i class=\"fas fa-folder-open fa-2x text-muted mb-2\"></i>\r\n                <p class=\"text-muted mb-0\">\r\n                  {{ searchKeyword ? '找不到符合條件的模板' : '暫無模板' }}\r\n                </p>\r\n                <small class=\"text-muted\" *ngIf=\"searchKeyword\">\r\n                  請嘗試其他關鍵字或 <a href=\"javascript:void(0)\" (click)=\"clearSearch()\">清除搜尋</a>\r\n                </small>\r\n              </div>\r\n            </td>\r\n          </tr>\r\n        </tbody>\r\n      </table>\r\n    </div>\r\n  </div>\r\n\r\n  <!-- 查看模板細節 -->\r\n  <div *ngIf=\"selectedTemplate\" class=\"template-detail-modal\">\r\n    <div class=\"template-detail-header d-flex justify-content-between align-items-center\">\r\n      <h6 class=\"mb-0\">\r\n        <i class=\"fas fa-file-alt mr-2\"></i>\r\n        模板細節：{{ selectedTemplate!.TemplateName }}\r\n      </h6>\r\n      <button class=\"btn btn-outline-secondary btn-sm\" (click)=\"closeTemplateDetail()\">\r\n        <i class=\"fas fa-times\"></i> 關閉\r\n      </button>\r\n    </div>\r\n\r\n    <div class=\"template-detail-content\">\r\n      <div *ngIf=\"selectedTemplate.Description\" class=\"template-description mb-3\">\r\n        <strong>描述：</strong>\r\n        <span class=\"text-muted\">{{ selectedTemplate.Description }}</span>\r\n      </div>\r\n\r\n      <div class=\"template-items\">\r\n        <h6 class=\"mb-2\">\r\n          <i class=\"fas fa-list mr-1\"></i>\r\n          模板內容 ({{ currentTemplateDetails.length }} 項)\r\n        </h6>\r\n\r\n        <div *ngIf=\"currentTemplateDetails.length > 0; else noDetails\" class=\"detail-list\">\r\n          <div *ngFor=\"let detail of currentTemplateDetails; let i = index\"\r\n            class=\"detail-item d-flex align-items-center py-2 border-bottom\">\r\n            <div class=\"detail-index\">\r\n              <span class=\"badge badge-light\">{{ i + 1 }}</span>\r\n            </div>\r\n            <div class=\"detail-content flex-grow-1 ml-2\">\r\n              <div class=\"detail-field\">\r\n                <strong>{{ detail.FieldName }}:</strong>\r\n              </div>\r\n              <div class=\"detail-value text-muted\">\r\n                {{ detail.FieldValue }}\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <ng-template #noDetails>\r\n          <div class=\"text-center py-3\">\r\n            <i class=\"fas fa-inbox fa-2x text-muted mb-2\"></i>\r\n            <p class=\"text-muted mb-0\">此模板暫無內容</p>\r\n          </div>\r\n        </ng-template>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</div>"], "mappings": "AAAA,SAAoBA,YAAY,QAA0C,eAAe;AACzF,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,QAAQ,gBAAgB;;;;;;;ICgBlCC,EAAA,CAAAC,cAAA,iBAAsG;IAA9CD,EAAA,CAAAE,UAAA,mBAAAC,mEAAA;MAAAH,EAAA,CAAAI,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAG,WAAA,EAAa;IAAA,EAAC;IAC7ET,EAAA,CAAAU,SAAA,YAA4B;IAC9BV,EAAA,CAAAW,YAAA,EAAS;;;;;IAWbX,EADF,CAAAC,cAAA,cAA4D,gBAChC;IACxBD,EAAA,CAAAY,MAAA,GACF;IACFZ,EADE,CAAAW,YAAA,EAAQ,EACJ;;;;IAFFX,EAAA,CAAAa,SAAA,GACF;IADEb,EAAA,CAAAc,kBAAA,mBAAAR,MAAA,CAAAS,iBAAA,CAAAC,MAAA,+BAAAV,MAAA,CAAAW,aAAA,8BACF;;;;;;IAyBUjB,EAAA,CAAAC,cAAA,iBACsC;IADPD,EAAA,CAAAE,UAAA,mBAAAgB,yEAAA;MAAAlB,EAAA,CAAAI,aAAA,CAAAe,GAAA;MAAA,MAAAC,MAAA,GAAApB,EAAA,CAAAO,aAAA,GAAAc,SAAA;MAAA,MAAAf,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAAY,MAAA,CAAAE,UAAA,IAA2BhB,MAAA,CAAAiB,gBAAA,CAAAH,MAAA,CAAAE,UAAA,CAAgC;IAAA,EAAC;IAEzFtB,EAAA,CAAAU,SAAA,YAA4B;IAACV,EAAA,CAAAY,MAAA,qBAC/B;IAAAZ,EAAA,CAAAW,YAAA,EAAS;;;;;;IAbXX,EAFJ,CAAAC,cAAA,SAAsE,SAChE,aACM;IAAAD,EAAA,CAAAY,MAAA,GAAsB;IAChCZ,EADgC,CAAAW,YAAA,EAAS,EACpC;IAEHX,EADF,CAAAC,cAAA,SAAI,eACuB;IAAAD,EAAA,CAAAY,MAAA,GAA8B;IACzDZ,EADyD,CAAAW,YAAA,EAAO,EAC3D;IAGDX,EAFJ,CAAAC,cAAA,aAAwB,cAC2B,iBAC2B;IAA7CD,EAAA,CAAAE,UAAA,mBAAAsB,+DAAA;MAAA,MAAAJ,MAAA,GAAApB,EAAA,CAAAI,aAAA,CAAAqB,GAAA,EAAAJ,SAAA;MAAA,MAAAf,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAoB,gBAAA,CAAAN,MAAA,CAAqB;IAAA,EAAC;IAC1DpB,EAAA,CAAAU,SAAA,aAA0B;IAACV,EAAA,CAAAY,MAAA,sBAC7B;IAAAZ,EAAA,CAAAW,YAAA,EAAS;IACTX,EAAA,CAAA2B,UAAA,KAAAC,gDAAA,qBACsC;IAK5C5B,EAFI,CAAAW,YAAA,EAAM,EACH,EACF;;;;IAhBOX,EAAA,CAAAa,SAAA,GAAsB;IAAtBb,EAAA,CAAA6B,iBAAA,CAAAT,MAAA,CAAAU,YAAA,CAAsB;IAGL9B,EAAA,CAAAa,SAAA,GAA8B;IAA9Bb,EAAA,CAAA6B,iBAAA,CAAAT,MAAA,CAAAW,WAAA,yBAA8B;IAQlD/B,EAAA,CAAAa,SAAA,GAAoB;IAApBb,EAAA,CAAAgC,UAAA,SAAAZ,MAAA,CAAAE,UAAA,CAAoB;;;;;;IAavBtB,EAAA,CAAAC,cAAA,gBAAgD;IAC9CD,EAAA,CAAAY,MAAA,+DAAU;IAAAZ,EAAA,CAAAC,cAAA,YAAqD;IAAxBD,EAAA,CAAAE,UAAA,mBAAA+B,kEAAA;MAAAjC,EAAA,CAAAI,aAAA,CAAA8B,GAAA;MAAA,MAAA5B,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAG,WAAA,EAAa;IAAA,EAAC;IAACT,EAAA,CAAAY,MAAA,+BAAI;IACrEZ,EADqE,CAAAW,YAAA,EAAI,EACjE;;;;;IAPVX,EAFJ,CAAAC,cAAA,SAAiE,aACtB,cACd;IACvBD,EAAA,CAAAU,SAAA,YAAwD;IACxDV,EAAA,CAAAC,cAAA,YAA2B;IACzBD,EAAA,CAAAY,MAAA,GACF;IAAAZ,EAAA,CAAAW,YAAA,EAAI;IACJX,EAAA,CAAA2B,UAAA,IAAAQ,8CAAA,oBAAgD;IAKtDnC,EAFI,CAAAW,YAAA,EAAM,EACH,EACF;;;;IAPGX,EAAA,CAAAa,SAAA,GACF;IADEb,EAAA,CAAAoC,kBAAA,MAAA9B,MAAA,CAAAW,aAAA,oGACF;IAC2BjB,EAAA,CAAAa,SAAA,EAAmB;IAAnBb,EAAA,CAAAgC,UAAA,SAAA1B,MAAA,CAAAW,aAAA,CAAmB;;;;;IAyBtDjB,EADF,CAAAC,cAAA,cAA4E,aAClE;IAAAD,EAAA,CAAAY,MAAA,yBAAG;IAAAZ,EAAA,CAAAW,YAAA,EAAS;IACpBX,EAAA,CAAAC,cAAA,eAAyB;IAAAD,EAAA,CAAAY,MAAA,GAAkC;IAC7DZ,EAD6D,CAAAW,YAAA,EAAO,EAC9D;;;;IADqBX,EAAA,CAAAa,SAAA,GAAkC;IAAlCb,EAAA,CAAA6B,iBAAA,CAAAvB,MAAA,CAAA+B,gBAAA,CAAAN,WAAA,CAAkC;;;;;IAarD/B,EAHJ,CAAAC,cAAA,cACmE,cACvC,eACQ;IAAAD,EAAA,CAAAY,MAAA,GAAW;IAC7CZ,EAD6C,CAAAW,YAAA,EAAO,EAC9C;IAGFX,EAFJ,CAAAC,cAAA,cAA6C,cACjB,aAChB;IAAAD,EAAA,CAAAY,MAAA,GAAuB;IACjCZ,EADiC,CAAAW,YAAA,EAAS,EACpC;IACNX,EAAA,CAAAC,cAAA,cAAqC;IACnCD,EAAA,CAAAY,MAAA,GACF;IAEJZ,EAFI,CAAAW,YAAA,EAAM,EACF,EACF;;;;;IAV8BX,EAAA,CAAAa,SAAA,GAAW;IAAXb,EAAA,CAAA6B,iBAAA,CAAAS,IAAA,KAAW;IAIjCtC,EAAA,CAAAa,SAAA,GAAuB;IAAvBb,EAAA,CAAAoC,kBAAA,KAAAG,SAAA,CAAAC,SAAA,MAAuB;IAG/BxC,EAAA,CAAAa,SAAA,GACF;IADEb,EAAA,CAAAoC,kBAAA,MAAAG,SAAA,CAAAE,UAAA,MACF;;;;;IAZNzC,EAAA,CAAAC,cAAA,cAAmF;IACjFD,EAAA,CAAA2B,UAAA,IAAAe,oDAAA,mBACmE;IAarE1C,EAAA,CAAAW,YAAA,EAAM;;;;IAdoBX,EAAA,CAAAa,SAAA,EAA2B;IAA3Bb,EAAA,CAAAgC,UAAA,YAAA1B,MAAA,CAAAqC,sBAAA,CAA2B;;;;;IAiBnD3C,EAAA,CAAAC,cAAA,cAA8B;IAC5BD,EAAA,CAAAU,SAAA,YAAkD;IAClDV,EAAA,CAAAC,cAAA,YAA2B;IAAAD,EAAA,CAAAY,MAAA,iDAAO;IACpCZ,EADoC,CAAAW,YAAA,EAAI,EAClC;;;;;;IA1CVX,EAFJ,CAAAC,cAAA,cAA4D,cAC4B,YACnE;IACfD,EAAA,CAAAU,SAAA,YAAoC;IACpCV,EAAA,CAAAY,MAAA,GACF;IAAAZ,EAAA,CAAAW,YAAA,EAAK;IACLX,EAAA,CAAAC,cAAA,iBAAiF;IAAhCD,EAAA,CAAAE,UAAA,mBAAA0C,gEAAA;MAAA5C,EAAA,CAAAI,aAAA,CAAAyC,GAAA;MAAA,MAAAvC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAwC,mBAAA,EAAqB;IAAA,EAAC;IAC9E9C,EAAA,CAAAU,SAAA,YAA4B;IAACV,EAAA,CAAAY,MAAA,qBAC/B;IACFZ,EADE,CAAAW,YAAA,EAAS,EACL;IAENX,EAAA,CAAAC,cAAA,cAAqC;IACnCD,EAAA,CAAA2B,UAAA,IAAAoB,6CAAA,kBAA4E;IAM1E/C,EADF,CAAAC,cAAA,eAA4B,cACT;IACfD,EAAA,CAAAU,SAAA,aAAgC;IAChCV,EAAA,CAAAY,MAAA,IACF;IAAAZ,EAAA,CAAAW,YAAA,EAAK;IAmBLX,EAjBA,CAAA2B,UAAA,KAAAqB,8CAAA,kBAAmF,KAAAC,sDAAA,gCAAAjD,EAAA,CAAAkD,sBAAA,CAiB3D;IAQ9BlD,EAFI,CAAAW,YAAA,EAAM,EACF,EACF;;;;;IA5CAX,EAAA,CAAAa,SAAA,GACF;IADEb,EAAA,CAAAoC,kBAAA,oCAAA9B,MAAA,CAAA+B,gBAAA,CAAAP,YAAA,MACF;IAOM9B,EAAA,CAAAa,SAAA,GAAkC;IAAlCb,EAAA,CAAAgC,UAAA,SAAA1B,MAAA,CAAA+B,gBAAA,CAAAN,WAAA,CAAkC;IAQpC/B,EAAA,CAAAa,SAAA,GACF;IADEb,EAAA,CAAAoC,kBAAA,gCAAA9B,MAAA,CAAAqC,sBAAA,CAAA3B,MAAA,cACF;IAEMhB,EAAA,CAAAa,SAAA,EAAyC;IAAAb,EAAzC,CAAAgC,UAAA,SAAA1B,MAAA,CAAAqC,sBAAA,CAAA3B,MAAA,KAAyC,aAAAmC,aAAA,CAAc;;;ADhGrE,OAAM,MAAOC,uBAAuB;EAPpCC,YAAA;IAQW,KAAAC,aAAa,GAAU,EAAE,CAAC,CAAC;IAC1B,KAAAC,cAAc,GAAG,IAAI1D,YAAY,EAAY;IAEvD;IACA,KAAA2D,SAAS,GAAe,EAAE;IAC1B,KAAAC,eAAe,GAAqB,EAAE;IACtC,KAAApB,gBAAgB,GAAoB,IAAI;IAExC;IACA,KAAApB,aAAa,GAAG,EAAE;IAClB,KAAAF,iBAAiB,GAAe,EAAE;IAElC;IACA,KAAA2C,WAAW,GAAG,KAAK;IACnB,KAAAC,WAAW,GAAG;MACZC,IAAI,EAAE,EAAE;MACRC,WAAW,EAAE,EAAE;MACfC,aAAa,EAAE;KAChB;;EAEDC,QAAQA,CAAA;IACN,IAAI,CAACC,aAAa,EAAE;IACpB,IAAI,CAACC,uBAAuB,EAAE;EAChC;EAEAC,WAAWA,CAAA;IACT,IAAI,CAACD,uBAAuB,EAAE;EAChC;EAEA;EACAD,aAAaA,CAAA;IACX;IACA,IAAI,CAACR,SAAS,GAAG,CACf;MAAElC,UAAU,EAAE,CAAC;MAAEQ,YAAY,EAAE,KAAK;MAAEC,WAAW,EAAE;IAAO,CAAE,EAC5D;MAAET,UAAU,EAAE,CAAC;MAAEQ,YAAY,EAAE,KAAK;MAAEC,WAAW,EAAE;IAAO,CAAE,CAC7D;IAED,IAAI,CAAC0B,eAAe,GAAG,CACrB;MAAEU,gBAAgB,EAAE,CAAC;MAAE7C,UAAU,EAAE,CAAC;MAAE8C,KAAK,EAAE,GAAG;MAAEC,UAAU,EAAE,aAAa;MAAE7B,SAAS,EAAE,cAAc;MAAEC,UAAU,EAAE;IAAO,CAAE,EAC7H;MAAE0B,gBAAgB,EAAE,CAAC;MAAE7C,UAAU,EAAE,CAAC;MAAE8C,KAAK,EAAE,GAAG;MAAEC,UAAU,EAAE,aAAa;MAAE7B,SAAS,EAAE,cAAc;MAAEC,UAAU,EAAE;IAAO,CAAE,EAC7H;MAAE0B,gBAAgB,EAAE,CAAC;MAAE7C,UAAU,EAAE,CAAC;MAAE8C,KAAK,EAAE,GAAG;MAAEC,UAAU,EAAE,aAAa;MAAE7B,SAAS,EAAE,cAAc;MAAEC,UAAU,EAAE;IAAO,CAAE,CAC9H;EACH;EAEA;EACAwB,uBAAuBA,CAAA;IACrB,IAAI,CAAC,IAAI,CAAChD,aAAa,CAACqD,IAAI,EAAE,EAAE;MAC9B,IAAI,CAACvD,iBAAiB,GAAG,CAAC,GAAG,IAAI,CAACyC,SAAS,CAAC;IAC9C,CAAC,MAAM;MACL,MAAMe,OAAO,GAAG,IAAI,CAACtD,aAAa,CAACuD,WAAW,EAAE;MAChD,IAAI,CAACzD,iBAAiB,GAAG,IAAI,CAACyC,SAAS,CAACiB,MAAM,CAACC,QAAQ,IACrDA,QAAQ,CAAC5C,YAAY,CAAC0C,WAAW,EAAE,CAACG,QAAQ,CAACJ,OAAO,CAAC,IACpDG,QAAQ,CAAC3C,WAAW,IAAI2C,QAAQ,CAAC3C,WAAW,CAACyC,WAAW,EAAE,CAACG,QAAQ,CAACJ,OAAO,CAAE,CAC/E;IACH;EACF;EAEA;EACAK,QAAQA,CAAA;IACN,IAAI,CAACX,uBAAuB,EAAE;EAChC;EAEA;EACAxD,WAAWA,CAAA;IACT,IAAI,CAACQ,aAAa,GAAG,EAAE;IACvB,IAAI,CAACgD,uBAAuB,EAAE;EAChC;EAIA;EACAY,aAAaA,CAAA;IACX,IAAI,CAACnB,WAAW,GAAG,IAAI;IACvB,IAAI,CAACC,WAAW,GAAG;MACjBC,IAAI,EAAE,EAAE;MACRC,WAAW,EAAE,EAAE;MACfC,aAAa,EAAE;KAChB;IACD;IACA,IAAI,CAACR,aAAa,CAACwB,OAAO,CAACC,IAAI,IAAIA,IAAI,CAACC,QAAQ,GAAG,KAAK,CAAC;EAC3D;EAEA;EACAC,iBAAiBA,CAAA;IACf,IAAI,CAACvB,WAAW,GAAG,KAAK;IACxB,IAAI,CAACC,WAAW,GAAG;MACjBC,IAAI,EAAE,EAAE;MACRC,WAAW,EAAE,EAAE;MACfC,aAAa,EAAE;KAChB;EACH;EAEA;EACAoB,eAAeA,CAAA;IACb,IAAI,CAAC,IAAI,CAACvB,WAAW,CAACC,IAAI,CAACU,IAAI,EAAE,EAAE;MACjCa,KAAK,CAAC,SAAS,CAAC;MAChB;IACF;IAEA,MAAMrB,aAAa,GAAG,IAAI,CAACR,aAAa,CAACmB,MAAM,CAACM,IAAI,IAAIA,IAAI,CAACC,QAAQ,CAAC;IACtE,IAAIlB,aAAa,CAAC9C,MAAM,KAAK,CAAC,EAAE;MAC9BmE,KAAK,CAAC,WAAW,CAAC;MAClB;IACF;IAEA;IACA,IAAI,CAACC,cAAc,CAAC,IAAI,CAACzB,WAAW,CAACC,IAAI,EAAE,IAAI,CAACD,WAAW,CAACE,WAAW,EAAEC,aAAa,CAAC;EACzF;EAEA;EACAsB,cAAcA,CAACxB,IAAY,EAAEC,WAAmB,EAAEC,aAAoB;IACpE;IACA,MAAMuB,KAAK,GAAGC,IAAI,CAACC,GAAG,CAAC,GAAG,IAAI,CAAC/B,SAAS,CAACgC,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACnE,UAAU,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC;IAE5E;IACA,MAAMqC,WAAW,GAAa;MAC5BrC,UAAU,EAAE+D,KAAK;MACjBvD,YAAY,EAAE8B,IAAI,CAACU,IAAI,EAAE;MACzBvC,WAAW,EAAE8B,WAAW,CAACS,IAAI;KAC9B;IAED;IACA,IAAI,CAACd,SAAS,CAACkC,IAAI,CAAC/B,WAAW,CAAC;IAEhC;IACAG,aAAa,CAACgB,OAAO,CAAC,CAACC,IAAI,EAAEY,KAAK,KAAI;MACpC,MAAMC,MAAM,GAAmB;QAC7BzB,gBAAgB,EAAE,IAAI,CAACV,eAAe,CAACzC,MAAM,GAAG2E,KAAK,GAAG,CAAC;QACzDrE,UAAU,EAAE+D,KAAK;QACjBjB,KAAK,EAAEW,IAAI,CAACc,cAAc,IAAId,IAAI,CAACe,EAAE,IAAI,CAAC;QAC1CzB,UAAU,EAAE,aAAa;QACzB7B,SAAS,EAAE,cAAc;QACzBC,UAAU,EAAEsC,IAAI,CAACgB,YAAY,IAAIhB,IAAI,CAACnB,IAAI,IAAI;OAC/C;MACD,IAAI,CAACH,eAAe,CAACiC,IAAI,CAACE,MAAM,CAAC;IACnC,CAAC,CAAC;IAEF;IACA,IAAI,CAAC3B,uBAAuB,EAAE;IAE9B;IACA,IAAI,CAACP,WAAW,GAAG,KAAK;IACxByB,KAAK,CAAC,OAAOvB,IAAI,UAAU,CAAC;EAC9B;EAEA;EACAlC,gBAAgBA,CAACgD,QAAkB;IACjC,IAAI,CAACrC,gBAAgB,GAAGqC,QAAQ;IAChC,IAAI,CAACnB,cAAc,CAACyC,IAAI,CAACtB,QAAQ,CAAC;EACpC;EAEA;EACAnD,gBAAgBA,CAAC0E,UAAkB;IACjC,IAAIC,OAAO,CAAC,UAAU,CAAC,EAAE;MACvB;MACA,IAAI,CAACC,kBAAkB,CAACF,UAAU,CAAC;IACrC;EACF;EAEA;EACAE,kBAAkBA,CAACF,UAAkB;IACnC,IAAI,CAACzC,SAAS,GAAG,IAAI,CAACA,SAAS,CAACiB,MAAM,CAACgB,CAAC,IAAIA,CAAC,CAACnE,UAAU,KAAK2E,UAAU,CAAC;IACxE,IAAI,CAACxC,eAAe,GAAG,IAAI,CAACA,eAAe,CAACgB,MAAM,CAAC2B,CAAC,IAAIA,CAAC,CAAC9E,UAAU,KAAK2E,UAAU,CAAC;IACpF,IAAI,CAAChC,uBAAuB,EAAE;IAE9B;IACA,IAAI,IAAI,CAAC5B,gBAAgB,EAAEf,UAAU,KAAK2E,UAAU,EAAE;MACpD,IAAI,CAAC5D,gBAAgB,GAAG,IAAI;IAC9B;IAEA8C,KAAK,CAAC,OAAO,CAAC;EAChB;EAEA;EACArC,mBAAmBA,CAAA;IACjB,IAAI,CAACT,gBAAgB,GAAG,IAAI;EAC9B;EAEA;EACA,IAAIM,sBAAsBA,CAAA;IACxB,IAAI,CAAC,IAAI,CAACN,gBAAgB,EAAE;MAC1B,OAAO,EAAE;IACX;IACA,OAAO,IAAI,CAACoB,eAAe,CAACgB,MAAM,CAAC2B,CAAC,IAAIA,CAAC,CAAC9E,UAAU,KAAK,IAAI,CAACe,gBAAiB,CAACf,UAAU,CAAC;EAC7F;EAEA;EACA+E,iBAAiBA,CAACV,KAAa,EAAEjB,QAAkB;IACjD,OAAOA,QAAQ,CAACpD,UAAU,IAAIqE,KAAK;EACrC;;;uCA9LWvC,uBAAuB;IAAA;EAAA;;;YAAvBA,uBAAuB;MAAAkD,SAAA;MAAAC,MAAA;QAAAjD,aAAA;MAAA;MAAAkD,OAAA;QAAAjD,cAAA;MAAA;MAAAkD,UAAA;MAAAC,QAAA,GAAA1G,EAAA,CAAA2G,oBAAA,EAAA3G,EAAA,CAAA4G,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAArC,QAAA,WAAAsC,iCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCR9BjH,EAHN,CAAAC,cAAA,aAAmC,aACG,aACkC,YACjD;UAAAD,EAAA,CAAAY,MAAA,+BAAI;UAAAZ,EAAA,CAAAW,YAAA,EAAK;UAC1BX,EAAA,CAAAC,cAAA,gBAAiE;UAA1BD,EAAA,CAAAE,UAAA,mBAAAiH,yDAAA;YAAA,OAASD,GAAA,CAAArC,aAAA,EAAe;UAAA,EAAC;UAC9D7E,EAAA,CAAAU,SAAA,WAAgC;UAAAV,EAAA,CAAAY,MAAA,oBAClC;UACFZ,EADE,CAAAW,YAAA,EAAS,EACL;UAKFX,EAFJ,CAAAC,cAAA,aAAmC,aACR,gBAE2B;UADiBD,EAAA,CAAAoH,gBAAA,2BAAAC,iEAAAC,MAAA;YAAAtH,EAAA,CAAAuH,kBAAA,CAAAL,GAAA,CAAAjG,aAAA,EAAAqG,MAAA,MAAAJ,GAAA,CAAAjG,aAAA,GAAAqG,MAAA;YAAA,OAAAA,MAAA;UAAA,EAA2B;UACvEtH,EAArB,CAAAE,UAAA,mBAAAsH,yDAAA;YAAA,OAASN,GAAA,CAAAtC,QAAA,EAAU;UAAA,EAAC,yBAAA6C,+DAAA;YAAA,OAAgBP,GAAA,CAAAtC,QAAA,EAAU;UAAA,EAAC;UADjD5E,EAAA,CAAAW,YAAA,EACkD;UAEhDX,EADF,CAAAC,cAAA,eAAgC,kBAC+C;UAArBD,EAAA,CAAAE,UAAA,mBAAAwH,0DAAA;YAAA,OAASR,GAAA,CAAAtC,QAAA,EAAU;UAAA,EAAC;UAC1E5E,EAAA,CAAAU,SAAA,aAA6B;UAC/BV,EAAA,CAAAW,YAAA,EAAS;UACTX,EAAA,CAAA2B,UAAA,KAAAgG,0CAAA,qBAAsG;UAM9G3H,EAHM,CAAAW,YAAA,EAAM,EACF,EACF,EACF;UAINX,EAAA,CAAAC,cAAA,eAA2B;UAEzBD,EAAA,CAAA2B,UAAA,KAAAiG,uCAAA,kBAA4D;UAUpD5H,EAJR,CAAAC,cAAA,eAA8B,iBACmB,iBAClB,UACrB,cACc;UAAAD,EAAA,CAAAY,MAAA,gCAAI;UAAAZ,EAAA,CAAAW,YAAA,EAAK;UACzBX,EAAA,CAAAC,cAAA,cAAgB;UAAAD,EAAA,CAAAY,MAAA,oBAAE;UAAAZ,EAAA,CAAAW,YAAA,EAAK;UACvBX,EAAA,CAAAC,cAAA,cAAoC;UAAAD,EAAA,CAAAY,MAAA,oBAAE;UAE1CZ,EAF0C,CAAAW,YAAA,EAAK,EACxC,EACC;UACRX,EAAA,CAAAC,cAAA,aAAO;UAoBLD,EAnBA,CAAA2B,UAAA,KAAAkG,sCAAA,kBAAsE,KAAAC,sCAAA,iBAmBL;UAgBzE9H,EAHM,CAAAW,YAAA,EAAQ,EACF,EACJ,EACF;UAGNX,EAAA,CAAA2B,UAAA,KAAAoG,uCAAA,mBAA4D;UAiD9D/H,EAAA,CAAAW,YAAA,EAAM;;;UAzHqEX,EAAA,CAAAa,SAAA,IAA2B;UAA3Bb,EAAA,CAAAgI,gBAAA,YAAAd,GAAA,CAAAjG,aAAA,CAA2B;UAMXjB,EAAA,CAAAa,SAAA,GAAmB;UAAnBb,EAAA,CAAAgC,UAAA,SAAAkF,GAAA,CAAAjG,aAAA,CAAmB;UAYnEjB,EAAA,CAAAa,SAAA,GAAmB;UAAnBb,EAAA,CAAAgC,UAAA,SAAAkF,GAAA,CAAAjG,aAAA,CAAmB;UAgBhCjB,EAAA,CAAAa,SAAA,IAAsB;UAAAb,EAAtB,CAAAgC,UAAA,YAAAkF,GAAA,CAAAnG,iBAAA,CAAsB,iBAAAmG,GAAA,CAAAb,iBAAA,CAA0B;UAmB/DrG,EAAA,CAAAa,SAAA,EAA0D;UAA1Db,EAAA,CAAAgC,UAAA,UAAAkF,GAAA,CAAAnG,iBAAA,IAAAmG,GAAA,CAAAnG,iBAAA,CAAAC,MAAA,OAA0D;UAmBjEhB,EAAA,CAAAa,SAAA,EAAsB;UAAtBb,EAAA,CAAAgC,UAAA,SAAAkF,GAAA,CAAA7E,gBAAA,CAAsB;;;qBD3ElBvC,YAAY,EAAAmI,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EAAEpI,WAAW,EAAAqI,EAAA,CAAAC,oBAAA,EAAAD,EAAA,CAAAE,eAAA,EAAAF,EAAA,CAAAG,OAAA;MAAAC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}