{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/shared/helper/allowHelper\";\nexport let BaseComponent = /*#__PURE__*/(() => {\n  class BaseComponent {\n    constructor(allow) {\n      this.allow = allow;\n      this.pageIndex = 1;\n      this.pageSize = 10;\n      this.totalRecords = 0;\n      this.pageFirst = 0;\n      this.isRead = this.allow.isRead();\n      this.isCreate = this.allow.isCreate();\n      this.isUpdate = this.allow.isUpdate();\n      this.isDelete = this.allow.isDelete();\n      this.isExcelImport = this.allow.isExcelImport();\n      this.isExcelExport = this.allow.isExcelExport();\n      this.isChangePayStatus = this.allow.isChangePayStatus();\n      this.isChangeProgress = this.allow.isChangeProgress();\n    }\n    ngOnInit() {}\n    getSubMenu(menu, routerUrl) {\n      return menu.Menu.map(function (p) {\n        return p.Child;\n      }).reduce(function (a, b) {\n        return a.concat(b);\n      }).find(x => routerUrl.indexOf(x.CPageUrl) !== -1);\n    }\n    isNullOrEmpty(value) {\n      if (value === undefined) {\n        return true;\n      }\n      if (value == null) {\n        return true;\n      }\n      if (value === '') {\n        return true;\n      }\n      return false;\n    }\n    static {\n      this.ɵfac = function BaseComponent_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || BaseComponent)(i0.ɵɵinject(i1.AllowHelper));\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: BaseComponent,\n        factory: BaseComponent.ɵfac\n      });\n    }\n  }\n  return BaseComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}