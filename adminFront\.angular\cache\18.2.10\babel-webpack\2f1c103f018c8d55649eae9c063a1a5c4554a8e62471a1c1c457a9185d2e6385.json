{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/Documents/salechange-product/adminFront/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { SharedModule } from '../components/shared.module';\nimport { CommonModule } from '@angular/common';\nimport { NbDatepickerModule } from '@nebular/theme';\nimport { BaseComponent } from '../components/base/baseComponent';\nimport { concatMap, tap } from 'rxjs';\nimport { NbDateFnsDateModule } from '@nebular/date-fns';\nimport * as moment from 'moment';\nimport { EEvent } from 'src/app/shared/services/event.service';\nimport { EnumHouseProgress } from 'src/app/shared/enum/enumHouseProgress';\nimport { EnumHouseType } from 'src/app/shared/enum/enumHouseType';\nimport { EnumPayStatus } from 'src/app/shared/enum/enumPayStatus';\nimport { EnumSignStatus } from 'src/app/shared/enum/enumSignStatus';\nimport { LocalStorageService } from 'src/app/shared/services/local-storage.service';\nimport { STORAGE_KEY } from 'src/app/shared/constant/constant';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/shared/helper/allowHelper\";\nimport * as i2 from \"src/app/shared/helper/enumHelper\";\nimport * as i3 from \"@nebular/theme\";\nimport * as i4 from \"src/app/shared/services/message.service\";\nimport * as i5 from \"src/app/shared/helper/validationHelper\";\nimport * as i6 from \"src/services/api/services\";\nimport * as i7 from \"src/app/shared/helper/petternHelper\";\nimport * as i8 from \"@angular/router\";\nimport * as i9 from \"src/app/shared/services/event.service\";\nimport * as i10 from \"src/app/shared/services/utility.service\";\nimport * as i11 from \"src/app/services/quotation.service\";\nimport * as i12 from \"@angular/common\";\nimport * as i13 from \"@angular/forms\";\nimport * as i14 from \"@ng-bootstrap/ng-bootstrap\";\nimport * as i15 from \"../components/breadcrumb/breadcrumb.component\";\nimport * as i16 from \"../../@theme/directives/label.directive\";\nconst _c0 = [\"fileInput\"];\nfunction HouseholdManagementComponent_nb_option_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 49);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const case_r2 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", case_r2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", case_r2.CBuildCaseName, \" \");\n  }\n}\nfunction HouseholdManagementComponent_nb_option_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 49);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const case_r3 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", case_r3);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", case_r3.label, \" \");\n  }\n}\nfunction HouseholdManagementComponent_nb_option_35_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 49);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const case_r4 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", case_r4);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", case_r4.label, \" \");\n  }\n}\nfunction HouseholdManagementComponent_nb_option_41_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 49);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const case_r5 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", case_r5);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", case_r5.label, \" \");\n  }\n}\nfunction HouseholdManagementComponent_nb_option_47_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 49);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const case_r6 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", case_r6);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", case_r6.label, \" \");\n  }\n}\nfunction HouseholdManagementComponent_nb_option_53_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 49);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const case_r7 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", case_r7);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", case_r7.label, \" \");\n  }\n}\nfunction HouseholdManagementComponent_nb_option_59_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 49);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const case_r8 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", case_r8);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", case_r8.label, \" \");\n  }\n}\nfunction HouseholdManagementComponent_button_67_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 38);\n    i0.ɵɵlistener(\"click\", function HouseholdManagementComponent_button_67_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r9 = i0.ɵɵnextContext();\n      const dialogHouseholdMain_r11 = i0.ɵɵreference(105);\n      return i0.ɵɵresetView(ctx_r9.openModel(dialogHouseholdMain_r11));\n    });\n    i0.ɵɵtext(1, \" \\u6279\\u6B21\\u65B0\\u589E\\u6236\\u5225\\u8CC7\\u6599 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction HouseholdManagementComponent_tr_99_button_18_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r13 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 53);\n    i0.ɵɵlistener(\"click\", function HouseholdManagementComponent_tr_99_button_18_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r13);\n      const item_r14 = i0.ɵɵnextContext().$implicit;\n      const ctx_r9 = i0.ɵɵnextContext();\n      const dialogUpdateHousehold_r15 = i0.ɵɵreference(103);\n      return i0.ɵɵresetView(ctx_r9.openModelDetail(dialogUpdateHousehold_r15, item_r14));\n    });\n    i0.ɵɵtext(1, \" \\u7DE8\\u8F2F \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction HouseholdManagementComponent_tr_99_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"td\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"td\");\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"td\");\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"td\");\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"td\");\n    i0.ɵɵtext(16);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"td\", 50);\n    i0.ɵɵtemplate(18, HouseholdManagementComponent_tr_99_button_18_Template, 2, 0, \"button\", 51);\n    i0.ɵɵelementStart(19, \"button\", 52);\n    i0.ɵɵlistener(\"click\", function HouseholdManagementComponent_tr_99_Template_button_click_19_listener() {\n      const item_r14 = i0.ɵɵrestoreView(_r12).$implicit;\n      const ctx_r9 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r9.onNavidateBuildCaseIdHouseId(\"customer-change-picture\", ctx_r9.searchQuery.CBuildCaseSelected.cID, item_r14.CID));\n    });\n    i0.ɵɵtext(20, \" \\u6D3D\\u8AC7\\u7D00\\u9304 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"button\", 52);\n    i0.ɵɵlistener(\"click\", function HouseholdManagementComponent_tr_99_Template_button_click_21_listener() {\n      const item_r14 = i0.ɵɵrestoreView(_r12).$implicit;\n      const ctx_r9 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r9.onNavidateBuildCaseIdHouseId(\"sample-selection-result\", ctx_r9.searchQuery.CBuildCaseSelected.cID, item_r14.CID));\n    });\n    i0.ɵɵtext(22, \" \\u5BA2\\u8B8A\\u78BA\\u8A8D\\u5716\\u8AAA \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"button\", 52);\n    i0.ɵɵlistener(\"click\", function HouseholdManagementComponent_tr_99_Template_button_click_23_listener() {\n      const item_r14 = i0.ɵɵrestoreView(_r12).$implicit;\n      const ctx_r9 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r9.onNavidateBuildCaseIdHouseId(\"finaldochouse_management\", ctx_r9.searchQuery.CBuildCaseSelected.cID, item_r14.CID));\n    });\n    i0.ɵɵtext(24, \" \\u7C3D\\u7F72\\u6587\\u4EF6\\u6B77\\u7A0B \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(25, \"button\", 52);\n    i0.ɵɵlistener(\"click\", function HouseholdManagementComponent_tr_99_Template_button_click_25_listener() {\n      const item_r14 = i0.ɵɵrestoreView(_r12).$implicit;\n      const ctx_r9 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r9.resetSecureKey(item_r14));\n    });\n    i0.ɵɵtext(26, \" \\u91CD\\u7F6E\\u5BC6\\u78BC \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(27, \"button\", 52);\n    i0.ɵɵlistener(\"click\", function HouseholdManagementComponent_tr_99_Template_button_click_27_listener() {\n      const item_r14 = i0.ɵɵrestoreView(_r12).$implicit;\n      const ctx_r9 = i0.ɵɵnextContext();\n      const dialogQuotation_r16 = i0.ɵɵreference(107);\n      return i0.ɵɵresetView(ctx_r9.openQuotation(dialogQuotation_r16, item_r14));\n    });\n    i0.ɵɵtext(28, \" \\u5831\\u50F9\\u55AE \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const item_r14 = ctx.$implicit;\n    const ctx_r9 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r14.CHouseHold);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r14.CFloor);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\" \", item_r14.CHouseType === 2 ? \"\\u92B7\\u552E\\u6236\" : \"\", \" \", item_r14.CHouseType === 1 ? \"\\u5730\\u4E3B\\u6236\" : \"\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r14.CIsChange === true ? \"\\u5BA2\\u8B8A\" : item_r14.CIsChange === false ? \"\\u6A19\\u6E96\" : \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r14.CProgressName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate3(\" \", item_r14.CPayStatus === 0 ? \"\\u672A\\u4ED8\\u6B3E\" : \"\", \" \", item_r14.CPayStatus === 1 ? \"\\u5DF2\\u4ED8\\u6B3E\" : \"\", \" \", item_r14.CPayStatus === 2 ? \"\\u7121\\u9808\\u4ED8\\u6B3E\" : \"\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r14.CSignStatus === 0 || item_r14.CSignStatus == null ? \"\\u672A\\u7C3D\\u56DE\" : \"\\u5DF2\\u7C3D\\u56DE\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r14.CIsEnable ? \"\\u555F\\u7528\" : \"\\u505C\\u7528\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r9.isUpdate);\n  }\n}\nfunction HouseholdManagementComponent_ng_template_102_nb_card_body_1_nb_option_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 49);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const status_r19 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", status_r19);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", status_r19.CBuildCaseName, \" \");\n  }\n}\nfunction HouseholdManagementComponent_ng_template_102_nb_card_body_1_nb_option_34_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 49);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const status_r20 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", status_r20);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", status_r20.label, \" \");\n  }\n}\nfunction HouseholdManagementComponent_ng_template_102_nb_card_body_1_div_35_nb_option_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 49);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const status_r22 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", status_r22);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", status_r22.label, \" \");\n  }\n}\nfunction HouseholdManagementComponent_ng_template_102_nb_card_body_1_div_35_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r21 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 59)(1, \"label\", 88);\n    i0.ɵɵtext(2, \" \\u4ED8\\u6B3E\\u72C0\\u614B \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"nb-select\", 89);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_ng_template_102_nb_card_body_1_div_35_Template_nb_select_ngModelChange_3_listener($event) {\n      i0.ɵɵrestoreView(_r21);\n      const ctx_r9 = i0.ɵɵnextContext(3);\n      i0.ɵɵtwoWayBindingSet(ctx_r9.detailSelected.CPayStatusSelected, $event) || (ctx_r9.detailSelected.CPayStatusSelected = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtemplate(4, HouseholdManagementComponent_ng_template_102_nb_card_body_1_div_35_nb_option_4_Template, 2, 2, \"nb-option\", 13);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r9 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r9.detailSelected.CPayStatusSelected);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r9.options.payStatusOptions);\n  }\n}\nfunction HouseholdManagementComponent_ng_template_102_nb_card_body_1_div_36_nb_option_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 49);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const status_r24 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", status_r24);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", status_r24.label, \" \");\n  }\n}\nfunction HouseholdManagementComponent_ng_template_102_nb_card_body_1_div_36_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r23 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 59)(1, \"label\", 90);\n    i0.ɵɵtext(2, \" \\u9032\\u5EA6 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"nb-select\", 91);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_ng_template_102_nb_card_body_1_div_36_Template_nb_select_ngModelChange_3_listener($event) {\n      i0.ɵɵrestoreView(_r23);\n      const ctx_r9 = i0.ɵɵnextContext(3);\n      i0.ɵɵtwoWayBindingSet(ctx_r9.detailSelected.CProgressSelected, $event) || (ctx_r9.detailSelected.CProgressSelected = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtemplate(4, HouseholdManagementComponent_ng_template_102_nb_card_body_1_div_36_nb_option_4_Template, 2, 2, \"nb-option\", 13);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r9 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r9.detailSelected.CProgressSelected);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r9.options.progressOptions);\n  }\n}\nfunction HouseholdManagementComponent_ng_template_102_nb_card_body_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r18 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-card-body\", 58)(1, \"div\", 59)(2, \"label\", 60);\n    i0.ɵɵtext(3, \" \\u5EFA\\u6848\\u540D\\u7A31 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"nb-select\", 61);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_ng_template_102_nb_card_body_1_Template_nb_select_ngModelChange_4_listener($event) {\n      i0.ɵɵrestoreView(_r18);\n      const ctx_r9 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r9.detailSelected.CBuildCaseSelected, $event) || (ctx_r9.detailSelected.CBuildCaseSelected = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtemplate(5, HouseholdManagementComponent_ng_template_102_nb_card_body_1_nb_option_5_Template, 2, 2, \"nb-option\", 13);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 59)(7, \"label\", 62);\n    i0.ɵɵtext(8, \" \\u6236\\u578B\\u540D\\u7A31 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"input\", 63);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_ng_template_102_nb_card_body_1_Template_input_ngModelChange_9_listener($event) {\n      i0.ɵɵrestoreView(_r18);\n      const ctx_r9 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r9.houseDetail.CHousehold, $event) || (ctx_r9.houseDetail.CHousehold = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"div\", 59)(11, \"label\", 64);\n    i0.ɵɵtext(12, \" \\u6A13\\u5C64 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"input\", 65);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_ng_template_102_nb_card_body_1_Template_input_ngModelChange_13_listener($event) {\n      i0.ɵɵrestoreView(_r18);\n      const ctx_r9 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r9.houseDetail.CFloor, $event) || (ctx_r9.houseDetail.CFloor = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(14, \"div\", 59)(15, \"label\", 66);\n    i0.ɵɵtext(16, \" \\u5BA2\\u6236\\u59D3\\u540D \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"input\", 67);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_ng_template_102_nb_card_body_1_Template_input_ngModelChange_17_listener($event) {\n      i0.ɵɵrestoreView(_r18);\n      const ctx_r9 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r9.houseDetail.CCustomerName, $event) || (ctx_r9.houseDetail.CCustomerName = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(18, \"div\", 59)(19, \"label\", 68);\n    i0.ɵɵtext(20, \" \\u8EAB\\u5206\\u8B49\\u5B57\\u865F \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"input\", 69);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_ng_template_102_nb_card_body_1_Template_input_ngModelChange_21_listener($event) {\n      i0.ɵɵrestoreView(_r18);\n      const ctx_r9 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r9.houseDetail.CNationalId, $event) || (ctx_r9.houseDetail.CNationalId = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(22, \"div\", 59)(23, \"label\", 70);\n    i0.ɵɵtext(24, \" \\u96FB\\u5B50\\u90F5\\u4EF6 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(25, \"input\", 71);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_ng_template_102_nb_card_body_1_Template_input_ngModelChange_25_listener($event) {\n      i0.ɵɵrestoreView(_r18);\n      const ctx_r9 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r9.houseDetail.CMail, $event) || (ctx_r9.houseDetail.CMail = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(26, \"div\", 59)(27, \"label\", 72);\n    i0.ɵɵtext(28, \" \\u806F\\u7D61\\u96FB\\u8A71 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(29, \"input\", 73);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_ng_template_102_nb_card_body_1_Template_input_ngModelChange_29_listener($event) {\n      i0.ɵɵrestoreView(_r18);\n      const ctx_r9 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r9.houseDetail.CPhone, $event) || (ctx_r9.houseDetail.CPhone = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(30, \"div\", 59)(31, \"label\", 74);\n    i0.ɵɵtext(32, \" \\u6236\\u5225\\u985E\\u578B \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(33, \"nb-select\", 75);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_ng_template_102_nb_card_body_1_Template_nb_select_ngModelChange_33_listener($event) {\n      i0.ɵɵrestoreView(_r18);\n      const ctx_r9 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r9.detailSelected.CHouseTypeSelected, $event) || (ctx_r9.detailSelected.CHouseTypeSelected = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtemplate(34, HouseholdManagementComponent_ng_template_102_nb_card_body_1_nb_option_34_Template, 2, 2, \"nb-option\", 13);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(35, HouseholdManagementComponent_ng_template_102_nb_card_body_1_div_35_Template, 5, 2, \"div\", 76)(36, HouseholdManagementComponent_ng_template_102_nb_card_body_1_div_36_Template, 5, 2, \"div\", 76);\n    i0.ɵɵelementStart(37, \"div\", 59)(38, \"label\", 77);\n    i0.ɵɵtext(39, \" \\u662F\\u5426\\u5BA2\\u8B8A \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(40, \"nb-checkbox\", 78);\n    i0.ɵɵtwoWayListener(\"checkedChange\", function HouseholdManagementComponent_ng_template_102_nb_card_body_1_Template_nb_checkbox_checkedChange_40_listener($event) {\n      i0.ɵɵrestoreView(_r18);\n      const ctx_r9 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r9.houseDetail.CIsChange, $event) || (ctx_r9.houseDetail.CIsChange = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtext(41, \"\\u662F \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(42, \"div\", 59)(43, \"label\", 79);\n    i0.ɵɵtext(44, \" \\u662F\\u5426\\u555F\\u7528 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(45, \"nb-checkbox\", 78);\n    i0.ɵɵtwoWayListener(\"checkedChange\", function HouseholdManagementComponent_ng_template_102_nb_card_body_1_Template_nb_checkbox_checkedChange_45_listener($event) {\n      i0.ɵɵrestoreView(_r18);\n      const ctx_r9 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r9.houseDetail.CIsEnable, $event) || (ctx_r9.houseDetail.CIsEnable = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtext(46, \"\\u662F \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(47, \"div\", 80)(48, \"label\", 81);\n    i0.ɵɵtext(49, \" \\u5BA2\\u8B8A\\u6642\\u6BB5 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(50, \"div\", 82)(51, \"nb-form-field\", 83);\n    i0.ɵɵelement(52, \"nb-icon\", 84);\n    i0.ɵɵelementStart(53, \"input\", 85);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_ng_template_102_nb_card_body_1_Template_input_ngModelChange_53_listener($event) {\n      i0.ɵɵrestoreView(_r18);\n      const ctx_r9 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r9.houseDetail.changeStartDate, $event) || (ctx_r9.houseDetail.changeStartDate = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(54, \"nb-datepicker\", 86, 4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(56, \"nb-form-field\", 83);\n    i0.ɵɵelement(57, \"nb-icon\", 84);\n    i0.ɵɵelementStart(58, \"input\", 87);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_ng_template_102_nb_card_body_1_Template_input_ngModelChange_58_listener($event) {\n      i0.ɵɵrestoreView(_r18);\n      const ctx_r9 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r9.houseDetail.changeEndDate, $event) || (ctx_r9.houseDetail.changeEndDate = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(59, \"nb-datepicker\", 86, 5);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const StartDate_r25 = i0.ɵɵreference(55);\n    const EndDate_r26 = i0.ɵɵreference(60);\n    const ctx_r9 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r9.detailSelected.CBuildCaseSelected);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r9.userBuildCaseOptions);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r9.houseDetail.CHousehold);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r9.houseDetail.CFloor);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r9.houseDetail.CCustomerName);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r9.houseDetail.CNationalId);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r9.houseDetail.CMail);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r9.houseDetail.CPhone);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r9.detailSelected.CHouseTypeSelected);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r9.options.houseTypeOptions);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r9.isChangePayStatus);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r9.isChangeProgress);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"checked\", ctx_r9.houseDetail.CIsChange);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtwoWayProperty(\"checked\", ctx_r9.houseDetail.CIsEnable);\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"nbDatepicker\", StartDate_r25);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r9.houseDetail.changeStartDate);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"nbDatepicker\", EndDate_r26);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r9.houseDetail.changeEndDate);\n  }\n}\nfunction HouseholdManagementComponent_ng_template_102_button_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r28 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 92);\n    i0.ɵɵlistener(\"click\", function HouseholdManagementComponent_ng_template_102_button_5_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r28);\n      const ref_r27 = i0.ɵɵnextContext().dialogRef;\n      const ctx_r9 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r9.onSubmitDetail(ref_r27));\n    });\n    i0.ɵɵtext(1, \"\\u9001\\u51FA\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction HouseholdManagementComponent_ng_template_102_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r17 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-card\", 54);\n    i0.ɵɵtemplate(1, HouseholdManagementComponent_ng_template_102_nb_card_body_1_Template, 61, 18, \"nb-card-body\", 55);\n    i0.ɵɵelementStart(2, \"nb-card-footer\", 47)(3, \"button\", 56);\n    i0.ɵɵlistener(\"click\", function HouseholdManagementComponent_ng_template_102_Template_button_click_3_listener() {\n      const ref_r27 = i0.ɵɵrestoreView(_r17).dialogRef;\n      const ctx_r9 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r9.onClose(ref_r27));\n    });\n    i0.ɵɵtext(4, \"\\u53D6\\u6D88\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(5, HouseholdManagementComponent_ng_template_102_button_5_Template, 2, 0, \"button\", 57);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r9 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r9.houseDetail);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r9.isCreate);\n  }\n}\nfunction HouseholdManagementComponent_ng_template_104_button_19_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r31 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 101);\n    i0.ɵɵlistener(\"click\", function HouseholdManagementComponent_ng_template_104_button_19_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r31);\n      const ref_r30 = i0.ɵɵnextContext().dialogRef;\n      const ctx_r9 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r9.addHouseHoldMain(ref_r30));\n    });\n    i0.ɵɵtext(1, \"\\u5132\\u5B58\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction HouseholdManagementComponent_ng_template_104_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r29 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-card\", 54)(1, \"nb-card-header\");\n    i0.ɵɵtext(2, \" \\u6236\\u5225\\u7BA1\\u7406 \\u300B\\u6279\\u6B21\\u65B0\\u589E\\u6236\\u5225\\u8CC7\\u6599 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"nb-card-body\", 58)(4, \"div\", 59)(5, \"label\", 93);\n    i0.ɵɵtext(6, \" \\u68DF\\u5225 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"input\", 94);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_ng_template_104_Template_input_ngModelChange_7_listener($event) {\n      i0.ɵɵrestoreView(_r29);\n      const ctx_r9 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r9.houseHoldMain.CBuildingName, $event) || (ctx_r9.houseHoldMain.CBuildingName = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 59)(9, \"label\", 95);\n    i0.ɵɵtext(10, \"\\u7576\\u5C64\\u6700\\u591A\\u6236\\u6578 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"input\", 96);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_ng_template_104_Template_input_ngModelChange_11_listener($event) {\n      i0.ɵɵrestoreView(_r29);\n      const ctx_r9 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r9.houseHoldMain.CHouseHoldCount, $event) || (ctx_r9.houseHoldMain.CHouseHoldCount = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(12, \"div\", 59)(13, \"label\", 97);\n    i0.ɵɵtext(14, \"\\u672C\\u68E0\\u7E3D\\u6A13\\u5C64 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"input\", 98);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_ng_template_104_Template_input_ngModelChange_15_listener($event) {\n      i0.ɵɵrestoreView(_r29);\n      const ctx_r9 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r9.houseHoldMain.CFloor, $event) || (ctx_r9.houseHoldMain.CFloor = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(16, \"nb-card-footer\", 47)(17, \"button\", 99);\n    i0.ɵɵlistener(\"click\", function HouseholdManagementComponent_ng_template_104_Template_button_click_17_listener() {\n      const ref_r30 = i0.ɵɵrestoreView(_r29).dialogRef;\n      const ctx_r9 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r9.onClose(ref_r30));\n    });\n    i0.ɵɵtext(18);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(19, HouseholdManagementComponent_ng_template_104_button_19_Template, 2, 0, \"button\", 100);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r9 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(7);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r9.houseHoldMain.CBuildingName);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r9.houseHoldMain.CHouseHoldCount);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r9.houseHoldMain.CFloor);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(\"\\u95DC\\u9589\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r9.isCreate);\n  }\n}\nfunction HouseholdManagementComponent_ng_template_106_tr_26_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r33 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\")(2, \"input\", 115);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_ng_template_106_tr_26_Template_input_ngModelChange_2_listener($event) {\n      const item_r34 = i0.ɵɵrestoreView(_r33).$implicit;\n      i0.ɵɵtwoWayBindingSet(item_r34.cItemName, $event) || (item_r34.cItemName = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(3, \"td\")(4, \"input\", 116);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_ng_template_106_tr_26_Template_input_ngModelChange_4_listener($event) {\n      const item_r34 = i0.ɵɵrestoreView(_r33).$implicit;\n      i0.ɵɵtwoWayBindingSet(item_r34.cUnitPrice, $event) || (item_r34.cUnitPrice = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"ngModelChange\", function HouseholdManagementComponent_ng_template_106_tr_26_Template_input_ngModelChange_4_listener() {\n      i0.ɵɵrestoreView(_r33);\n      const ctx_r9 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r9.calculateTotal());\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"td\")(6, \"input\", 117);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_ng_template_106_tr_26_Template_input_ngModelChange_6_listener($event) {\n      const item_r34 = i0.ɵɵrestoreView(_r33).$implicit;\n      i0.ɵɵtwoWayBindingSet(item_r34.cCount, $event) || (item_r34.cCount = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"ngModelChange\", function HouseholdManagementComponent_ng_template_106_tr_26_Template_input_ngModelChange_6_listener() {\n      i0.ɵɵrestoreView(_r33);\n      const ctx_r9 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r9.calculateTotal());\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"td\", 118);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"td\")(10, \"span\", 119);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(12, \"td\")(13, \"button\", 120);\n    i0.ɵɵlistener(\"click\", function HouseholdManagementComponent_ng_template_106_tr_26_Template_button_click_13_listener() {\n      const i_r35 = i0.ɵɵrestoreView(_r33).index;\n      const ctx_r9 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r9.removeQuotationItem(i_r35));\n    });\n    i0.ɵɵtext(14, \" \\u522A\\u9664 \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const item_r34 = ctx.$implicit;\n    const ctx_r9 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"bg-light\", item_r34.cIsDefault);\n    i0.ɵɵtwoWayProperty(\"ngModel\", item_r34.cItemName);\n    i0.ɵɵproperty(\"disabled\", item_r34.cIsDefault);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtwoWayProperty(\"ngModel\", item_r34.cUnitPrice);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtwoWayProperty(\"ngModel\", item_r34.cCount);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r9.formatCurrency(item_r34.cUnitPrice * item_r34.cCount), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"badge-primary\", item_r34.cIsDefault)(\"badge-secondary\", !item_r34.cIsDefault);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", item_r34.cIsDefault ? \"\\u9810\\u8A2D\" : \"\\u81EA\\u5B9A\\u7FA9\", \" \");\n  }\n}\nfunction HouseholdManagementComponent_ng_template_106_tr_27_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 121);\n    i0.ɵɵtext(2, \" \\u8ACB\\u9EDE\\u64CA\\u300C\\u65B0\\u589E\\u81EA\\u5B9A\\u7FA9\\u9805\\u76EE\\u300D\\u6216\\u300C\\u8F09\\u5165\\u9810\\u8A2D\\u9805\\u76EE\\u300D\\u958B\\u59CB\\u5EFA\\u7ACB\\u5831\\u50F9\\u55AE \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction HouseholdManagementComponent_ng_template_106_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r32 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-card\", 102)(1, \"nb-card-header\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"nb-card-body\")(4, \"div\", 103)(5, \"button\", 40);\n    i0.ɵɵlistener(\"click\", function HouseholdManagementComponent_ng_template_106_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r32);\n      const ctx_r9 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r9.addQuotationItem());\n    });\n    i0.ɵɵtext(6, \" + \\u65B0\\u589E\\u81EA\\u5B9A\\u7FA9\\u9805\\u76EE \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"button\", 34);\n    i0.ɵɵlistener(\"click\", function HouseholdManagementComponent_ng_template_106_Template_button_click_7_listener() {\n      i0.ɵɵrestoreView(_r32);\n      const ctx_r9 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r9.loadDefaultItems());\n    });\n    i0.ɵɵtext(8, \" \\u8F09\\u5165\\u9810\\u8A2D\\u9805\\u76EE \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"div\", 104)(10, \"table\", 105)(11, \"thead\")(12, \"tr\")(13, \"th\", 106);\n    i0.ɵɵtext(14, \"\\u9805\\u76EE\\u540D\\u7A31\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"th\", 107);\n    i0.ɵɵtext(16, \"\\u55AE\\u50F9 (\\u5143)\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"th\", 108);\n    i0.ɵɵtext(18, \"\\u6578\\u91CF\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"th\", 107);\n    i0.ɵɵtext(20, \"\\u5C0F\\u8A08 (\\u5143)\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"th\", 108);\n    i0.ɵɵtext(22, \"\\u985E\\u578B\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"th\", 108);\n    i0.ɵɵtext(24, \"\\u64CD\\u4F5C\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(25, \"tbody\");\n    i0.ɵɵtemplate(26, HouseholdManagementComponent_ng_template_106_tr_26_Template, 15, 12, \"tr\", 46)(27, HouseholdManagementComponent_ng_template_106_tr_27_Template, 3, 0, \"tr\", 109);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(28, \"div\", 110)(29, \"h4\", 111);\n    i0.ɵɵtext(30);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(31, \"nb-card-footer\", 112);\n    i0.ɵɵelement(32, \"div\");\n    i0.ɵɵelementStart(33, \"div\")(34, \"button\", 113);\n    i0.ɵɵlistener(\"click\", function HouseholdManagementComponent_ng_template_106_Template_button_click_34_listener() {\n      const ref_r36 = i0.ɵɵrestoreView(_r32).dialogRef;\n      const ctx_r9 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r9.onClose(ref_r36));\n    });\n    i0.ɵɵtext(35, \"\\u53D6\\u6D88\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(36, \"button\", 114);\n    i0.ɵɵlistener(\"click\", function HouseholdManagementComponent_ng_template_106_Template_button_click_36_listener() {\n      const ref_r36 = i0.ɵɵrestoreView(_r32).dialogRef;\n      const ctx_r9 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r9.saveQuotation(ref_r36));\n    });\n    i0.ɵɵtext(37, \" \\u5132\\u5B58\\u5831\\u50F9\\u55AE \");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r9 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\" \\u5831\\u50F9\\u55AE - \", ctx_r9.currentHouse == null ? null : ctx_r9.currentHouse.CHouseHold, \" (\", ctx_r9.currentHouse == null ? null : ctx_r9.currentHouse.CFloor, \"\\u6A13) \");\n    i0.ɵɵadvance(24);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r9.quotationItems);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r9.quotationItems.length === 0);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\\u7E3D\\u91D1\\u984D: \", ctx_r9.formatCurrency(ctx_r9.totalAmount), \"\");\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"disabled\", ctx_r9.quotationItems.length === 0);\n  }\n}\nexport class HouseholdManagementComponent extends BaseComponent {\n  constructor(_allow, enumHelper, dialogService, message, valid, _houseService, _houseHoldMainService, _buildCaseService, pettern, router, _eventService, _ultilityService, quotationService) {\n    super(_allow);\n    this._allow = _allow;\n    this.enumHelper = enumHelper;\n    this.dialogService = dialogService;\n    this.message = message;\n    this.valid = valid;\n    this._houseService = _houseService;\n    this._houseHoldMainService = _houseHoldMainService;\n    this._buildCaseService = _buildCaseService;\n    this.pettern = pettern;\n    this.router = router;\n    this._eventService = _eventService;\n    this._ultilityService = _ultilityService;\n    this.quotationService = quotationService;\n    this.tempBuildCaseID = -1;\n    this.pageFirst = 1;\n    this.pageSize = 10;\n    this.pageIndex = 1;\n    this.totalRecords = 0;\n    this.statusOptions = [{\n      value: 0,\n      key: 'allow',\n      label: '允許'\n    }, {\n      value: 1,\n      key: 'not allowed',\n      label: '不允許'\n    }];\n    this.cIsEnableOptions = [{\n      value: null,\n      key: 'all',\n      label: '全部'\n    }, {\n      value: true,\n      key: 'enable',\n      label: '啟用'\n    }, {\n      value: false,\n      key: 'deactivate',\n      label: '停用'\n    }];\n    this.buildCaseOptions = [{\n      label: '全部',\n      value: ''\n    }];\n    this.houseHoldOptions = [{\n      label: '全部',\n      value: ''\n    }];\n    this.progressOptions = [{\n      label: '全部',\n      value: -1\n    }];\n    this.houseTypeOptions = [{\n      label: '全部',\n      value: -1\n    }];\n    this.payStatusOptions = [{\n      label: '全部',\n      value: -1\n    }];\n    this.signStatusOptions = [{\n      label: '全部',\n      value: -1\n    }];\n    this.options = {\n      progressOptions: this.enumHelper.getEnumOptions(EnumHouseProgress),\n      payStatusOptions: this.enumHelper.getEnumOptions(EnumPayStatus),\n      houseTypeOptions: this.enumHelper.getEnumOptions(EnumHouseType)\n    };\n    this.initDetail = {\n      CHouseID: 0,\n      CMail: \"\",\n      CIsChange: false,\n      CPayStatus: 0,\n      CIsEnable: false,\n      CCustomerName: \"\",\n      CNationalID: \"\",\n      CProgress: \"\",\n      CHouseType: 0,\n      CHouseHold: \"\",\n      CPhone: \"\"\n    };\n    this.quotationItems = [];\n    this.totalAmount = 0;\n    this.currentHouse = null;\n    this.currentQuotationId = 0;\n    this.selectedFile = null;\n    this.buildingSelectedOptions = [{\n      value: '',\n      label: '全部'\n    }];\n    this._eventService.receive().pipe(tap(res => {\n      if (res.action == \"GET_BUILDCASE\" /* EEvent.GET_BUILDCASE */ && !!res.payload) {\n        this.tempBuildCaseID = res.payload;\n      }\n    })).subscribe();\n  }\n  ngOnInit() {\n    this.progressOptions = [...this.progressOptions, ...this.enumHelper.getEnumOptions(EnumHouseProgress)];\n    this.houseTypeOptions = [...this.houseTypeOptions, ...this.enumHelper.getEnumOptions(EnumHouseType)];\n    this.payStatusOptions = [...this.payStatusOptions, ...this.enumHelper.getEnumOptions(EnumPayStatus)];\n    this.signStatusOptions = [...this.signStatusOptions, ...this.enumHelper.getEnumOptions(EnumSignStatus)];\n    if (LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH) != null && LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH) != undefined && LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH) != \"\") {\n      let previous_search = JSON.parse(LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH));\n      this.searchQuery = {\n        CBuildCaseSelected: null,\n        // CBuildingNameSelected: previous_search.CBuildingNameSelected != null && previous_search.CBuildingNameSelected != undefined\n        //   ? this.buildingSelectedOptions.find(x => x.value == previous_search.CBuildingNameSelected.value)\n        //   : this.buildingSelectedOptions[0],\n        CHouseHoldSelected: previous_search.CHouseHoldSelected != null && previous_search.CHouseHoldSelected != undefined ? this.houseHoldOptions.find(x => x.value == previous_search.CHouseHoldSelected.value) : this.houseHoldOptions[0],\n        CHouseTypeSelected: previous_search.CHouseTypeSelected != null && previous_search.CHouseTypeSelected != undefined ? this.houseTypeOptions.find(x => x.value == previous_search.CHouseTypeSelected.value) : this.houseTypeOptions[0],\n        CPayStatusSelected: previous_search.CPayStatusSelected != null && previous_search.CPayStatusSelected != undefined ? this.payStatusOptions.find(x => x.value == previous_search.CPayStatusSelected.value) : this.payStatusOptions[0],\n        CProgressSelected: previous_search.CProgressSelected != null && previous_search.CProgressSelected != undefined ? this.progressOptions.find(x => x.value == previous_search.CProgressSelected.value) : this.progressOptions[0],\n        CSignStatusSelected: previous_search.CSignStatusSelected != null && previous_search.CSignStatusSelected != undefined ? this.signStatusOptions.find(x => x.value == previous_search.CSignStatusSelected.value) : this.signStatusOptions[0],\n        CIsEnableSeleted: previous_search.CIsEnableSeleted != null && previous_search.CIsEnableSeleted != undefined ? this.cIsEnableOptions.find(x => x.value == previous_search.CIsEnableSeleted.value) : this.cIsEnableOptions[0],\n        CFrom: previous_search.CFrom != null && previous_search.CFrom != undefined ? previous_search.CFrom : '',\n        CTo: previous_search.CTo != null && previous_search.CTo != undefined ? previous_search.CTo : ''\n      };\n    } else {\n      this.searchQuery = {\n        CBuildCaseSelected: null,\n        CBuildingNameSelected: this.buildingSelectedOptions[0],\n        CHouseHoldSelected: this.houseHoldOptions[0],\n        CHouseTypeSelected: this.houseTypeOptions[0],\n        CPayStatusSelected: this.payStatusOptions[0],\n        CProgressSelected: this.progressOptions[0],\n        CSignStatusSelected: this.signStatusOptions[0],\n        CIsEnableSeleted: this.cIsEnableOptions[0],\n        CFrom: '',\n        CTo: ''\n      };\n    }\n    this.getListBuildCase();\n  }\n  onSearch() {\n    let sessionSave = {\n      CBuildCaseSelected: this.searchQuery.CBuildCaseSelected,\n      // CBuildingNameSelected: this.searchQuery.CBuildingNameSelected,\n      CFrom: this.searchQuery.CFrom,\n      CTo: this.searchQuery.CTo,\n      CHouseHoldSelected: this.searchQuery.CHouseHoldSelected,\n      CHouseTypeSelected: this.searchQuery.CHouseTypeSelected,\n      CIsEnableSeleted: this.searchQuery.CIsEnableSeleted,\n      CPayStatusSelected: this.searchQuery.CPayStatusSelected,\n      CProgressSelected: this.searchQuery.CProgressSelected,\n      CSignStatusSelected: this.searchQuery.CSignStatusSelected\n    };\n    LocalStorageService.AddSessionStorage(STORAGE_KEY.HOUSE_SEARCH, JSON.stringify(sessionSave));\n    this.getHouseList().subscribe();\n  }\n  pageChanged(newPage) {\n    this.pageIndex = newPage;\n    this.getHouseList().subscribe();\n  }\n  exportHouse() {\n    if (this.searchQuery.CBuildCaseSelected.cID) {\n      this._houseService.apiHouseExportHousePost$Json({\n        CBuildCaseID: this.searchQuery.CBuildCaseSelected.cID\n      }).subscribe(res => {\n        if (res.Entries && res.StatusCode == 0) {\n          this._ultilityService.downloadExcelFile(res.Entries, '戶別資訊範本');\n        } else {\n          this.message.showErrorMSG(res.Message);\n        }\n      });\n    }\n  }\n  triggerFileInput() {\n    this.fileInput.nativeElement.click();\n  }\n  onFileSelected(event) {\n    const input = event.target;\n    if (input.files && input.files.length > 0) {\n      this.selectedFile = input.files[0];\n      this.importExcel();\n    }\n  }\n  importExcel() {\n    if (this.selectedFile) {\n      const formData = new FormData();\n      formData.append('CFile', this.selectedFile);\n      this._houseService.apiHouseImportHousePost$Json({\n        body: {\n          CBuildCaseID: this.searchQuery.CBuildCaseSelected.cID,\n          CFile: this.selectedFile\n        }\n      }).subscribe(res => {\n        if (res.StatusCode === 0) {\n          this.message.showSucessMSG(res.Message);\n          this.getHouseList().subscribe();\n        } else {\n          this.message.showErrorMSG(res.Message);\n        }\n      });\n    }\n  }\n  getListHouseHold() {\n    this._houseService.apiHouseGetListHouseHoldPost$Json({\n      body: {\n        CBuildCaseID: this.searchQuery.CBuildCaseSelected.cID\n      }\n    }).subscribe(res => {\n      if (res.Entries && res.StatusCode == 0) {\n        this.houseHoldOptions = [{\n          value: '',\n          label: '全部'\n        }, ...res.Entries.map(e => {\n          return {\n            value: e,\n            label: e\n          };\n        })];\n        if (LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH) != null && LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH) != undefined && LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH) != \"\") {\n          let previous_search = JSON.parse(LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH));\n          if (previous_search.CHouseHoldSelected != null && previous_search.CHouseHoldSelected != undefined) {\n            let index = this.houseHoldOptions.findIndex(x => x.value == previous_search.CHouseHoldSelected.value);\n            this.searchQuery.CHouseHoldSelected = this.houseHoldOptions[index];\n          } else {\n            this.searchQuery.CHouseHoldSelected = this.houseHoldOptions[0];\n          }\n        }\n      }\n    });\n  }\n  formatQuery() {\n    this.bodyRequest = {\n      CBuildCaseID: this.searchQuery.CBuildCaseSelected.cID,\n      PageIndex: this.pageIndex,\n      PageSize: this.pageSize\n    };\n    // if (this.searchQuery.CBuildingNameSelected) {\n    //   this.bodyRequest['CBuildingName'] = this.searchQuery.CBuildingNameSelected.value\n    // }\n    if (this.searchQuery.CFrom && this.searchQuery.CTo) {\n      this.bodyRequest['CFloor'] = {\n        CFrom: this.searchQuery.CFrom,\n        CTo: this.searchQuery.CTo\n      };\n    }\n    if (this.searchQuery.CHouseHoldSelected) {\n      this.bodyRequest['CHouseHold'] = this.searchQuery.CHouseHoldSelected.value;\n    }\n    if (this.searchQuery.CHouseTypeSelected.value) {\n      this.bodyRequest['CHouseType'] = this.searchQuery.CHouseTypeSelected.value;\n    }\n    if (typeof this.searchQuery.CIsEnableSeleted.value === \"boolean\") {\n      this.bodyRequest['CIsEnable'] = this.searchQuery.CIsEnableSeleted.value;\n    }\n    if (this.searchQuery.CPayStatusSelected.value) {\n      this.bodyRequest['CPayStatus'] = this.searchQuery.CPayStatusSelected.value;\n    }\n    if (this.searchQuery.CProgressSelected.value) {\n      this.bodyRequest['CProgress'] = this.searchQuery.CProgressSelected.value;\n    }\n    if (this.searchQuery.CSignStatusSelected.value) {\n      this.bodyRequest['CSignStatus'] = this.searchQuery.CSignStatusSelected.value;\n    }\n    return this.bodyRequest;\n  }\n  sortByFloorDescending(arr) {\n    return arr.sort((a, b) => (b.CFloor ?? 0) - (a.CFloor ?? 0));\n  }\n  getHouseList() {\n    return this._houseService.apiHouseGetHouseListPost$Json({\n      body: this.formatQuery()\n    }).pipe(tap(res => {\n      if (res.Entries && res.StatusCode == 0) {\n        this.houseList = res.Entries;\n        this.totalRecords = res.TotalItems;\n      }\n    }));\n  }\n  onSelectionChangeBuildCase() {\n    // this.getListBuilding()\n    this.getListHouseHold();\n    this.getHouseList().subscribe();\n  }\n  getListBuildCase() {\n    this._buildCaseService.apiBuildCaseGetAllBuildCaseForSelectPost$Json({\n      body: {\n        CIsPagi: false,\n        CStatus: 1\n      }\n    }).pipe(tap(res => {\n      if (res.Entries && res.StatusCode == 0) {\n        this.userBuildCaseOptions = res.Entries?.length ? res.Entries.map(res => {\n          return {\n            CBuildCaseName: res.CBuildCaseName,\n            cID: res.cID\n          };\n        }) : [];\n        if (LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH) != null && LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH) != undefined && LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH) != \"\") {\n          let previous_search = JSON.parse(LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH));\n          if (previous_search.CBuildCaseSelected != null && previous_search.CBuildCaseSelected != undefined) {\n            let index = this.userBuildCaseOptions.findIndex(x => x.cID == previous_search.CBuildCaseSelected.cID);\n            this.searchQuery.CBuildCaseSelected = this.userBuildCaseOptions[index];\n          } else {\n            this.searchQuery.CBuildCaseSelected = this.userBuildCaseOptions[0];\n          }\n        } else {\n          this.searchQuery.CBuildCaseSelected = this.userBuildCaseOptions[0];\n        }\n      }\n    }), tap(() => {\n      // this.getListBuilding()\n      this.getListHouseHold();\n      setTimeout(() => {\n        this.getHouseList().subscribe();\n      }, 500);\n    })).subscribe();\n  }\n  getHouseById(CID, ref) {\n    this.detailSelected = {};\n    this._houseService.apiHouseGetHouseByIdPost$Json({\n      body: {\n        CHouseID: CID\n      }\n    }).subscribe(res => {\n      if (res.Entries && res.StatusCode == 0) {\n        this.houseDetail = {\n          ...res.Entries,\n          changeStartDate: res.Entries.CChangeStartDate ? new Date(res.Entries.CChangeStartDate) : undefined,\n          changeEndDate: res.Entries.CChangeEndDate ? new Date(res.Entries.CChangeEndDate) : undefined\n        };\n        if (res.Entries.CBuildCaseId) {\n          this.detailSelected.CBuildCaseSelected = this.findItemInArray(this.userBuildCaseOptions, 'cID', res.Entries.CBuildCaseId);\n        }\n        this.detailSelected.CPayStatusSelected = this.findItemInArray(this.options.payStatusOptions, 'value', res.Entries.CPayStatus);\n        if (res.Entries.CHouseType) {\n          this.detailSelected.CHouseTypeSelected = this.findItemInArray(this.options.houseTypeOptions, 'value', res.Entries.CHouseType);\n        } else {\n          this.detailSelected.CHouseTypeSelected = this.options.houseTypeOptions[1];\n        }\n        this.detailSelected.CProgressSelected = this.findItemInArray(this.options.progressOptions, 'value', res.Entries.CProgress);\n        if (res.Entries.CBuildCaseId) {\n          if (this.houseHoldMain) {\n            this.houseHoldMain.CBuildCaseID = res.Entries.CBuildCaseId;\n          }\n        }\n        this.dialogService.open(ref);\n      }\n    });\n  }\n  findItemInArray(array, key, value) {\n    return array.find(item => item[key] === value);\n  }\n  openModelDetail(ref, item) {\n    this.getHouseById(item.CID, ref);\n  }\n  openModel(ref) {\n    this.houseHoldMain = {\n      CBuildingName: '',\n      CFloor: undefined,\n      CHouseHoldCount: undefined\n    };\n    this.dialogService.open(ref);\n  }\n  formatDate(CChangeDate) {\n    if (CChangeDate) {\n      return moment(CChangeDate).format('YYYY-MM-DDTHH:mm:ss');\n    }\n    return '';\n  }\n  onSubmitDetail(ref) {\n    this.houseDetail.CChangeStartDate = this.houseDetail.changeStartDate ? this.formatDate(this.houseDetail.changeStartDate) : '', this.houseDetail.CChangeEndDate = this.houseDetail.changeEndDate ? this.formatDate(this.houseDetail.changeEndDate) : '', this.editHouseArgsParam = {\n      CCustomerName: this.houseDetail.CCustomerName,\n      CHouseHold: this.houseDetail.CHousehold,\n      CHouseID: this.houseDetail.CId,\n      CHouseType: this.detailSelected.CHouseTypeSelected ? this.detailSelected.CHouseTypeSelected.value : null,\n      CIsChange: this.houseDetail.CIsChange,\n      CIsEnable: this.houseDetail.CIsEnable,\n      CMail: this.houseDetail.CMail,\n      CNationalID: this.houseDetail.CNationalId,\n      CPayStatus: this.detailSelected.CPayStatusSelected ? this.detailSelected.CPayStatusSelected.value : null,\n      CPhone: this.houseDetail.CPhone,\n      CProgress: this.detailSelected.CProgressSelected ? this.detailSelected.CProgressSelected.value : null,\n      CChangeStartDate: this.houseDetail.CChangeStartDate,\n      CChangeEndDate: this.houseDetail.CChangeEndDate\n    };\n    this.validation();\n    if (this.valid.errorMessages.length > 0) {\n      this.message.showErrorMSGs(this.valid.errorMessages);\n      return;\n    }\n    this._houseService.apiHouseEditHousePost$Json({\n      body: this.editHouseArgsParam\n    }).pipe(tap(res => {\n      if (res.StatusCode === 0) {\n        this.message.showSucessMSG(\"執行成功\");\n        ref.close();\n      } else {\n        this.message.showErrorMSG(res.Message);\n        ref.close();\n      }\n    }), concatMap(() => this.getHouseList())).subscribe();\n  }\n  onSubmit(ref) {\n    let bodyReq = {\n      CCustomerName: this.houseDetail.CCustomerName,\n      CHouseHold: this.houseDetail.CHousehold,\n      CHouseID: this.houseDetail.CId,\n      CHouseType: this.houseDetail.CHouseType,\n      CIsChange: this.houseDetail.CIsChange,\n      CIsEnable: this.houseDetail.CIsEnable,\n      CMail: this.houseDetail.CMail,\n      CNationalID: this.houseDetail.CNationalId,\n      CPayStatus: this.houseDetail.CPayStatus,\n      CPhone: this.houseDetail.CPhone,\n      CProgress: this.houseDetail.CProgress\n    };\n    this._houseService.apiHouseEditHousePost$Json({\n      body: bodyReq\n    }).subscribe(res => {\n      if (res.StatusCode === 0) {\n        this.message.showSucessMSG(\"執行成功\");\n        ref.close();\n      }\n    });\n  }\n  onClose(ref) {\n    ref.close();\n  }\n  onNavidateId(type, id) {\n    const idURL = id ? id : this.searchQuery.CBuildCaseSelected.cID;\n    this.router.navigate([`/pages/household-management/${type}`, idURL]);\n  }\n  onNavidateBuildCaseIdHouseId(type, buildCaseId, houseId) {\n    this.router.navigate([`/pages/household-management/${type}`, buildCaseId, houseId]);\n  }\n  resetSecureKey(item) {\n    if (confirm(\"您想重設密碼嗎？\")) {\n      this._houseService.apiHouseResetHouseSecureKeyPost$Json({\n        body: item.CID\n      }).subscribe(res => {\n        if (res.StatusCode == 0) {\n          this.message.showSucessMSG(\"執行成功\");\n        }\n      });\n    }\n  }\n  validation() {\n    this.valid.clear();\n    this.valid.required('[建案名稱]', this.houseDetail.CId);\n    this.valid.required('[戶型名稱]', this.editHouseArgsParam.CHouseHold);\n    this.valid.isStringMaxLength('[棟別]', this.editHouseArgsParam.CHouseHold, 50);\n    this.valid.required('[樓層]', this.houseDetail.CFloor);\n    this.valid.isStringMaxLength('[客戶姓名]', this.editHouseArgsParam.CCustomerName, 50);\n    // if (this.editHouseArgsParam.CNationalID) {\n    //   this.valid.CheckTaiwanID(this.editHouseArgsParam.CNationalID)\n    // }\n    this.valid.pattern('[電子郵件]', this.editHouseArgsParam.CMail, this.pettern.MailPettern);\n    this.valid.isPhoneNumber('[聯絡電話]', this.editHouseArgsParam.CPhone);\n    this.valid.required('[進度]', this.editHouseArgsParam.CProgress);\n    this.valid.required('[戶別類型]', this.detailSelected.CHouseTypeSelected.value);\n    this.valid.required('[付款狀態]', this.detailSelected.CPayStatusSelected.value);\n    if (this.houseDetail.CChangeStartDate) {\n      this.valid.required('[客變結束日期]', this.houseDetail.CChangeEndDate);\n    }\n    if (this.houseDetail.CChangeEndDate) {\n      this.valid.required('[客變開始日期]', this.houseDetail.CChangeStartDate);\n    }\n    this.valid.checkStartBeforeEnd('[開放日期]', this.houseDetail.CChangeStartDate ? this.houseDetail.CChangeStartDate : '', this.houseDetail.CChangeEndDate ? this.houseDetail.CChangeEndDate : '');\n  }\n  validationHouseHoldMain() {\n    this.valid.clear();\n    this.valid.required('[建案]', this.houseHoldMain.CBuildCaseID);\n    this.valid.required('[棟別]', this.houseHoldMain.CBuildingName);\n    this.valid.isStringMaxLength('[棟別]', this.houseHoldMain.CBuildingName, 10);\n    this.valid.isNaturalNumberInRange('[當層最多戶數]', this.houseHoldMain.CFloor, 1, 100);\n    this.valid.isNaturalNumberInRange('[本棟總樓層]', this.houseHoldMain.CHouseHoldCount, 1, 100);\n  }\n  addHouseHoldMain(ref) {\n    this.houseHoldMain.CBuildCaseID = this.searchQuery.CBuildCaseSelected.cID, this.validationHouseHoldMain();\n    if (this.valid.errorMessages.length > 0) {\n      this.message.showErrorMSGs(this.valid.errorMessages);\n      return;\n    }\n    this._houseHoldMainService.apiHouseHoldMainAddHouseHoldMainPost$Json({\n      body: this.houseHoldMain\n    }).pipe(tap(res => {\n      if (res.StatusCode === 0) {\n        this.message.showSucessMSG(\"執行成功\");\n        ref.close();\n      }\n    }), concatMap(() => this.getHouseList())).subscribe();\n  } // 開啟報價單對話框\n  openQuotation(dialog, item) {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      _this.currentHouse = item;\n      _this.quotationItems = [];\n      _this.totalAmount = 0;\n      _this.currentQuotationId = 0; // 重置報價單ID\n      // 載入現有報價資料\n      try {\n        const response = yield _this.quotationService.getQuotationByHouseId(item.CID).toPromise();\n        console.log('getQuotationByHouseId response:', response); // 除錯用\n        if (response && response.StatusCode === 0 && response.Entries) {\n          // 保存當前的報價單ID\n          _this.currentQuotationId = response.Entries.CQuotationID || 0;\n          console.log('載入報價單ID:', _this.currentQuotationId); // 除錯用\n          // 檢查 Entries 是否有 Items 陣列\n          if (response.Entries.Items && Array.isArray(response.Entries.Items)) {\n            // 將 API 回傳的資料轉換為 QuotationItem 格式\n            _this.quotationItems = response.Entries.Items.map(entry => ({\n              cHouseID: response.Entries.CHouseID || item.CID,\n              cQuotationID: response.Entries.CQuotationID,\n              cItemName: entry.CItemName || '',\n              cUnitPrice: entry.CUnitPrice || 0,\n              cCount: entry.CCount || 1,\n              cStatus: entry.CStatus || 1,\n              cIsDefault: entry.CIsDefault || false\n            }));\n            _this.calculateTotal();\n            console.log('成功載入報價項目:', _this.quotationItems.length, '項'); // 除錯用\n          } else {\n            console.warn('Entries.Items 不是陣列或不存在:', response.Entries);\n          }\n        } else {\n          console.warn('API 回傳資料格式不正確或 StatusCode 不是 0:', response);\n        }\n      } catch (error) {\n        console.error('載入報價資料失敗:', error);\n      }\n      _this.dialogService.open(dialog, {\n        context: item,\n        closeOnBackdropClick: false\n      });\n    })();\n  }\n  // 新增自定義報價項目\n  addQuotationItem() {\n    this.quotationItems.push({\n      cHouseID: this.currentHouse?.CID || 0,\n      cItemName: '',\n      cUnitPrice: 0,\n      cCount: 1,\n      cStatus: 1,\n      cIsDefault: false\n    });\n  }\n  // 載入預設項目\n  loadDefaultItems() {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      try {\n        if (!_this2.currentHouse?.CID) {\n          _this2.message.showErrorMSG('請先選擇戶別');\n          return;\n        }\n        const request = {\n          CBuildCaseID: _this2.currentHouse.CBuildCaseID || 0,\n          CHouseID: _this2.currentHouse.CID\n        };\n        const response = yield _this2.quotationService.loadDefaultItems(request).toPromise();\n        if (response?.success && response.data) {\n          const defaultItems = response.data.map(x => ({\n            cQuotationID: x.CQuotationID,\n            cHouseID: _this2.currentHouse?.CID,\n            cItemName: x.CItemName,\n            cUnitPrice: x.CUnitPrice,\n            cCount: x.CCount,\n            cStatus: x.CStatus,\n            cIsDefault: true\n          }));\n          _this2.quotationItems.push(...defaultItems);\n          _this2.calculateTotal();\n          _this2.message.showSucessMSG('載入預設項目成功');\n        } else {\n          _this2.message.showErrorMSG(response?.message || '載入預設項目失敗');\n        }\n      } catch (error) {\n        console.error('載入預設項目錯誤:', error);\n        _this2.message.showErrorMSG('載入預設項目失敗');\n      }\n    })();\n  }\n  // 移除報價項目\n  removeQuotationItem(index) {\n    const item = this.quotationItems[index];\n    this.quotationItems.splice(index, 1);\n    this.calculateTotal();\n  }\n  // 計算總金額\n  calculateTotal() {\n    this.totalAmount = this.quotationItems.reduce((sum, item) => {\n      return sum + item.cUnitPrice * item.cCount;\n    }, 0);\n  }\n  // 格式化金額\n  formatCurrency(amount) {\n    return new Intl.NumberFormat('zh-TW', {\n      style: 'currency',\n      currency: 'TWD',\n      minimumFractionDigits: 0\n    }).format(amount);\n  }\n  // 儲存報價單\n  saveQuotation(ref) {\n    var _this3 = this;\n    return _asyncToGenerator(function* () {\n      if (_this3.quotationItems.length === 0) {\n        _this3.message.showErrorMSG('請先新增報價項目');\n        return;\n      }\n      // 驗證必填欄位\n      const invalidItems = _this3.quotationItems.filter(item => !item.cItemName.trim() || item.cUnitPrice < 0 || item.cCount < 1);\n      if (invalidItems.length > 0) {\n        _this3.message.showErrorMSG('請確認所有項目名稱、單價及數量都已正確填寫');\n        return;\n      }\n      try {\n        const request = {\n          houseId: _this3.currentHouse.CID,\n          items: _this3.quotationItems,\n          quotationId: _this3.currentQuotationId // 傳遞當前的報價單ID\n        };\n        console.log('儲存報價單請求:', request); // 除錯用\n        const response = yield _this3.quotationService.saveQuotation(request).toPromise();\n        if (response?.success) {\n          _this3.message.showSucessMSG('報價單儲存成功');\n          ref.close();\n        } else {\n          _this3.message.showErrorMSG(response?.message || '儲存失敗');\n        }\n      } catch (error) {\n        _this3.message.showErrorMSG('報價單儲存失敗');\n      }\n    })();\n  }\n  // 匯出報價單\n  exportQuotation() {\n    var _this4 = this;\n    return _asyncToGenerator(function* () {\n      try {\n        const blob = yield _this4.quotationService.exportQuotation(_this4.currentHouse.CID).toPromise();\n        if (blob) {\n          const url = window.URL.createObjectURL(blob);\n          const link = document.createElement('a');\n          link.href = url;\n          link.download = `報價單_${_this4.currentHouse.CHouseHold}_${_this4.currentHouse.CFloor}樓.pdf`;\n          link.click();\n          window.URL.revokeObjectURL(url);\n        } else {\n          _this4.message.showErrorMSG('匯出報價單失敗：未收到檔案資料');\n        }\n      } catch (error) {\n        _this4.message.showErrorMSG('匯出報價單失敗');\n      }\n    })();\n  }\n  static {\n    this.ɵfac = function HouseholdManagementComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || HouseholdManagementComponent)(i0.ɵɵdirectiveInject(i1.AllowHelper), i0.ɵɵdirectiveInject(i2.EnumHelper), i0.ɵɵdirectiveInject(i3.NbDialogService), i0.ɵɵdirectiveInject(i4.MessageService), i0.ɵɵdirectiveInject(i5.ValidationHelper), i0.ɵɵdirectiveInject(i6.HouseService), i0.ɵɵdirectiveInject(i6.HouseHoldMainService), i0.ɵɵdirectiveInject(i6.BuildCaseService), i0.ɵɵdirectiveInject(i7.PetternHelper), i0.ɵɵdirectiveInject(i8.Router), i0.ɵɵdirectiveInject(i9.EventService), i0.ɵɵdirectiveInject(i10.UtilityService), i0.ɵɵdirectiveInject(i11.QuotationService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: HouseholdManagementComponent,\n      selectors: [[\"ngx-household-management\"]],\n      viewQuery: function HouseholdManagementComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.fileInput = _t.first);\n        }\n      },\n      standalone: true,\n      features: [i0.ɵɵInheritDefinitionFeature, i0.ɵɵStandaloneFeature],\n      decls: 108,\n      vars: 21,\n      consts: [[\"fileInput\", \"\"], [\"dialogUpdateHousehold\", \"\"], [\"dialogHouseholdMain\", \"\"], [\"dialogQuotation\", \"\"], [\"StartDate\", \"\"], [\"EndDate\", \"\"], [\"accent\", \"success\"], [1, \"font-bold\", \"text-[#818181]\"], [1, \"d-flex\", \"flex-wrap\"], [1, \"col-md-6\"], [1, \"form-group\", \"d-flex\", \"align-items-center\", \"w-full\"], [\"for\", \"buildingName\", 1, \"label\", \"col-3\"], [\"placeholder\", \"\\u5EFA\\u6848\", 1, \"col-9\", 3, \"ngModelChange\", \"selectedChange\", \"ngModel\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"for\", \"cHouseType\", 1, \"label\", \"col-3\"], [1, \"col-9\", 3, \"ngModelChange\", \"ngModel\"], [1, \"form-group\", \"d-flex\", \"align-items-center\"], [\"for\", \"cFloorFrom\", 1, \"label\", \"col-3\"], [1, \"ml-3\"], [\"type\", \"text\", \"id\", \"CFrom\", \"nbInput\", \"\", 1, \"w-full\", \"col-4\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"cFloorTo\", 1, \"label\", \"col-1\"], [1, \"mr-3\"], [\"type\", \"text\", \"id\", \"CTo\", \"nbInput\", \"\", 1, \"w-full\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"cHousehold\", 1, \"label\", \"col-3\"], [\"placeholder\", \"\\u6236\\u578B\", 1, \"col-9\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"cPayStatus\", 1, \"label\", \"col-3\"], [\"placeholder\", \"\\u7E73\\u6B3E\\u72C0\\u614B\", 1, \"col-9\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"cProgress\", 1, \"label\", \"col-3\"], [\"placeholder\", \"\\u9032\\u5EA6\", 1, \"col-9\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"cStatus\", 1, \"label\", \"col-3\"], [\"placeholder\", \"\\u72C0\\u614B\", 1, \"col-9\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"cSignStatus\", 1, \"label\", \"col-3\"], [\"placeholder\", \"\\u7C3D\\u56DE\\u72C0\\u614B\", 1, \"col-9\", 3, \"ngModelChange\", \"ngModel\"], [1, \"d-flex\", \"justify-content-end\", \"w-full\"], [1, \"btn\", \"btn-secondary\", \"btn-sm\", 3, \"click\"], [1, \"fas\", \"fa-search\"], [1, \"col-md-12\"], [\"class\", \"btn btn-info mx-1 btn-sm\", 3, \"click\", 4, \"ngIf\"], [1, \"btn\", \"btn-info\", \"mx-1\", \"btn-sm\", 3, \"click\"], [\"type\", \"file\", \"accept\", \".xlsx, .xls\", 2, \"display\", \"none\", 3, \"change\"], [1, \"btn\", \"btn-info\", \"btn-sm\", 3, \"click\"], [1, \"table-responsive\", \"mt-4\"], [1, \"table\", \"table-striped\", \"border\", 2, \"min-width\", \"1000px\", \"background-color\", \"#f3f3f3\"], [2, \"background-color\", \"#27ae60\", \"color\", \"white\"], [\"scope\", \"col\", 1, \"col-1\"], [\"scope\", \"col\", 1, \"col-4\"], [4, \"ngFor\", \"ngForOf\"], [1, \"d-flex\", \"justify-content-center\"], [\"aria-label\", \"Pagination\", 3, \"pageChange\", \"page\", \"pageSize\", \"collectionSize\"], [3, \"value\"], [1, \"text-center\", \"w-32\", \"px-0\"], [\"class\", \"btn btn-outline-success btn-sm text-left m-[2px]\", 3, \"click\", 4, \"ngIf\"], [1, \"btn\", \"btn-outline-success\", \"btn-sm\", \"m-[2px]\", 3, \"click\"], [1, \"btn\", \"btn-outline-success\", \"btn-sm\", \"text-left\", \"m-[2px]\", 3, \"click\"], [2, \"width\", \"500px\", \"max-height\", \"95vh\"], [\"class\", \"px-4\", 4, \"ngIf\"], [1, \"btn\", \"btn-outline-secondary\", \"m-2\", \"px-8\", 3, \"click\"], [\"class\", \"btn btn-primary m-2 bg-[#169BD5] px-8\", 3, \"click\", 4, \"ngIf\"], [1, \"px-4\"], [1, \"form-group\"], [\"for\", \"cBuildCaseId\", \"baseLabel\", \"\", 1, \"required-field\", \"mr-4\", 2, \"min-width\", \"75px\"], [\"placeholder\", \"\\u5EFA\\u6848\\u540D\\u7A31\", \"disabled\", \"true\", 1, \"w-full\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"cHousehold\", \"baseLabel\", \"\", 1, \"required-field\", \"mr-4\", 2, \"min-width\", \"75px\"], [\"type\", \"text\", \"nbInput\", \"\", \"placeholder\", \"\\u6236\\u578B\\u540D\\u7A31\", 1, \"w-full\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"cFloor\", \"baseLabel\", \"\", 1, \"required-field\", \"mr-4\", 2, \"min-width\", \"75px\"], [\"type\", \"number\", \"nbInput\", \"\", \"placeholder\", \"\\u6A13\\u5C64\", \"min\", \"1\", \"max\", \"100\", \"disabled\", \"true\", 1, \"w-full\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"cCustomerName\", \"baseLabel\", \"\", 1, \"mr-4\", 2, \"min-width\", \"75px\"], [\"type\", \"text\", \"nbInput\", \"\", \"placeholder\", \"\\u5BA2\\u6236\\u59D3\\u540D\", 1, \"w-full\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"cNationalId\", \"baseLabel\", \"\", 1, \"mr-4\", 2, \"min-width\", \"75px\"], [\"type\", \"text\", \"nbInput\", \"\", \"placeholder\", \"\\u8EAB\\u5206\\u8B49\\u5B57\\u865F\", \"maxlength\", \"20\", 1, \"w-full\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"cMail\", \"baseLabel\", \"\", 1, \"mr-4\", 2, \"min-width\", \"75px\"], [\"type\", \"text\", \"nbInput\", \"\", \"placeholder\", \"\\u96FB\\u5B50\\u90F5\\u4EF6\", 1, \"w-full\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"cPhone\", \"baseLabel\", \"\", 1, \"mr-4\", 2, \"min-width\", \"75px\"], [\"type\", \"text\", \"nbInput\", \"\", \"placeholder\", \"\\u806F\\u7D61\\u96FB\\u8A71\", 1, \"w-full\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"cHouseType\", \"baseLabel\", \"\", 1, \"required-field\", \"mr-4\", 2, \"min-width\", \"75px\"], [\"placeholder\", \"\\u6236\\u5225\\u985E\\u578B\", 1, \"w-full\", 3, \"ngModelChange\", \"ngModel\"], [\"class\", \"form-group\", 4, \"ngIf\"], [\"for\", \"cIsChange\", \"baseLabel\", \"\", 1, \"mr-4\", 2, \"min-width\", \"75px\"], [\"status\", \"basic\", 3, \"checkedChange\", \"checked\"], [\"for\", \"cIsEnable\", \"baseLabel\", \"\", 1, \"mr-4\", 2, \"min-width\", \"75px\"], [1, \"form-group\", \"flex\", \"flex-row\"], [\"for\", \"cIsEnable\", \"baseLabel\", \"\", 1, \"mr-4\", \"content-center\", 2, \"min-width\", \"75px\"], [1, \"max-w-xs\", \"flex\", \"flex-row\"], [1, \"w-1/2\"], [\"nbPrefix\", \"\", \"icon\", \"calendar-outline\"], [\"nbInput\", \"\", \"type\", \"text\", \"id\", \"StartDate\", \"placeholder\", \"yyyy-mm-dd\", 1, \"w-[42%]\", \"mr-2\", 3, \"ngModelChange\", \"nbDatepicker\", \"ngModel\"], [\"format\", \"yyyy-MM-dd\"], [\"nbInput\", \"\", \"type\", \"text\", \"id\", \"EndDate\", \"placeholder\", \"yyyy-mm-dd\", 1, \"w-[42%]\", \"ml-2\", 3, \"ngModelChange\", \"nbDatepicker\", \"ngModel\"], [\"for\", \"cPayStatus\", \"baseLabel\", \"\", 1, \"required-field\", \"mr-4\", 2, \"min-width\", \"75px\"], [\"placeholder\", \"\\u4ED8\\u6B3E\\u72C0\\u614B\", 1, \"w-full\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"cProgress\", \"baseLabel\", \"\", 1, \"required-field\", \"mr-4\", 2, \"min-width\", \"75px\"], [\"placeholder\", \"\\u9032\\u5EA6\", 1, \"w-full\", 3, \"ngModelChange\", \"ngModel\"], [1, \"btn\", \"btn-primary\", \"m-2\", \"bg-[#169BD5]\", \"px-8\", 3, \"click\"], [\"for\", \"cBuildingName\", \"baseLabel\", \"\", 1, \"required-field\", \"mr-4\", 2, \"min-width\", \"75px\"], [\"type\", \"text\", \"nbInput\", \"\", \"placeholder\", \"\\u68DF\\u5225\", 1, \"w-full\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"CHouseHoldCount\", \"baseLabel\", \"\", 1, \"required-field\", \"mr-4\", 2, \"min-width\", \"75px\"], [\"type\", \"number\", \"nbInput\", \"\", \"placeholder\", \"\\u7576\\u5C64\\u6700\\u591A\\u6236\\u6578\", 1, \"w-full\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"CFloor\", \"baseLabel\", \"\", 1, \"required-field\", \"mr-4\", 2, \"min-width\", \"75px\"], [\"type\", \"number\", \"nbInput\", \"\", \"placeholder\", \"\\u672C\\u68E0\\u7E3D\\u6A13\\u5C64\", 1, \"w-full\", 3, \"ngModelChange\", \"ngModel\"], [1, \"btn\", \"btn-primary\", \"mr-4\", 3, \"click\"], [\"class\", \"btn btn-primary\", 3, \"click\", 4, \"ngIf\"], [1, \"btn\", \"btn-primary\", 3, \"click\"], [2, \"width\", \"900px\", \"max-height\", \"95vh\"], [1, \"mb-4\", \"d-flex\", \"justify-content-between\"], [1, \"table-responsive\"], [1, \"table\", \"table-bordered\"], [\"width\", \"30%\"], [\"width\", \"20%\"], [\"width\", \"10%\"], [4, \"ngIf\"], [1, \"text-right\", \"mt-4\"], [1, \"text-primary\"], [1, \"d-flex\", \"justify-content-between\"], [1, \"btn\", \"btn-outline-secondary\", \"m-2\", 3, \"click\"], [1, \"btn\", \"btn-primary\", \"m-2\", 3, \"click\", \"disabled\"], [\"type\", \"text\", \"nbInput\", \"\", 1, \"w-full\", 3, \"ngModelChange\", \"ngModel\", \"disabled\"], [\"type\", \"number\", \"nbInput\", \"\", \"min\", \"0\", \"step\", \"0.01\", 1, \"w-full\", 3, \"ngModelChange\", \"ngModel\"], [\"type\", \"number\", \"nbInput\", \"\", \"min\", \"1\", \"step\", \"1\", 1, \"w-full\", 3, \"ngModelChange\", \"ngModel\"], [1, \"text-right\"], [1, \"badge\"], [1, \"btn\", \"btn-danger\", \"btn-sm\", 3, \"click\"], [\"colspan\", \"6\", 1, \"text-center\", \"text-muted\", \"py-4\"]],\n      template: function HouseholdManagementComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"nb-card\", 6)(1, \"nb-card-header\");\n          i0.ɵɵelement(2, \"ngx-breadcrumb\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(3, \"nb-card-body\")(4, \"h1\", 7);\n          i0.ɵɵtext(5, \"\\u60A8\\u53EF\\u8207\\u6B64\\u7BA1\\u7406\\u5404\\u6236\\u5225\\u5167\\u4E4B\\u76F8\\u95DC\\u8CC7\\u8A0A\\uFF0C\\u5305\\u542B\\u57FA\\u672C\\u8CC7\\u6599\\u3001\\u7E73\\u6B3E\\u72C0\\u6CC1\\u3001\\u4E0A\\u50B3\\u5BA2\\u8B8A\\u5716\\u9762\\u3001\\u6AA2\\u8996\\u5BA2\\u8B8A\\u7D50\\u679C\\u7B49\\u7B49\\u3002 \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(6, \"div\", 8)(7, \"div\", 9)(8, \"div\", 10)(9, \"label\", 11);\n          i0.ɵɵtext(10, \"\\u5EFA\\u6848\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(11, \"nb-select\", 12);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_Template_nb_select_ngModelChange_11_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.searchQuery.CBuildCaseSelected, $event) || (ctx.searchQuery.CBuildCaseSelected = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵlistener(\"selectedChange\", function HouseholdManagementComponent_Template_nb_select_selectedChange_11_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onSelectionChangeBuildCase());\n          });\n          i0.ɵɵtemplate(12, HouseholdManagementComponent_nb_option_12_Template, 2, 2, \"nb-option\", 13);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(13, \"div\", 9)(14, \"div\", 10)(15, \"label\", 14);\n          i0.ɵɵtext(16, \"\\u985E\\u578B\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(17, \"nb-select\", 15);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_Template_nb_select_ngModelChange_17_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.searchQuery.CHouseTypeSelected, $event) || (ctx.searchQuery.CHouseTypeSelected = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵtemplate(18, HouseholdManagementComponent_nb_option_18_Template, 2, 2, \"nb-option\", 13);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(19, \"div\", 9)(20, \"div\", 16)(21, \"label\", 17);\n          i0.ɵɵtext(22, \"\\u6A13 \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(23, \"nb-form-field\", 18)(24, \"input\", 19);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_Template_input_ngModelChange_24_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.searchQuery.CFrom, $event) || (ctx.searchQuery.CFrom = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(25, \"label\", 20);\n          i0.ɵɵtext(26, \"~ \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(27, \"nb-form-field\", 21)(28, \"input\", 22);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_Template_input_ngModelChange_28_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.searchQuery.CTo, $event) || (ctx.searchQuery.CTo = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelement(29, \"div\", 9);\n          i0.ɵɵelementStart(30, \"div\", 9)(31, \"div\", 10)(32, \"label\", 23);\n          i0.ɵɵtext(33, \" \\u6236\\u578B \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(34, \"nb-select\", 24);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_Template_nb_select_ngModelChange_34_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.searchQuery.CHouseHoldSelected, $event) || (ctx.searchQuery.CHouseHoldSelected = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵtemplate(35, HouseholdManagementComponent_nb_option_35_Template, 2, 2, \"nb-option\", 13);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(36, \"div\", 9)(37, \"div\", 10)(38, \"label\", 25);\n          i0.ɵɵtext(39, \" \\u7E73\\u6B3E\\u72C0\\u614B \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(40, \"nb-select\", 26);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_Template_nb_select_ngModelChange_40_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.searchQuery.CPayStatusSelected, $event) || (ctx.searchQuery.CPayStatusSelected = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵtemplate(41, HouseholdManagementComponent_nb_option_41_Template, 2, 2, \"nb-option\", 13);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(42, \"div\", 9)(43, \"div\", 10)(44, \"label\", 27);\n          i0.ɵɵtext(45, \" \\u9032\\u5EA6 \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(46, \"nb-select\", 28);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_Template_nb_select_ngModelChange_46_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.searchQuery.CProgressSelected, $event) || (ctx.searchQuery.CProgressSelected = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵtemplate(47, HouseholdManagementComponent_nb_option_47_Template, 2, 2, \"nb-option\", 13);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(48, \"div\", 9)(49, \"div\", 10)(50, \"label\", 29);\n          i0.ɵɵtext(51, \" \\u72C0\\u614B \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(52, \"nb-select\", 30);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_Template_nb_select_ngModelChange_52_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.searchQuery.CIsEnableSeleted, $event) || (ctx.searchQuery.CIsEnableSeleted = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵtemplate(53, HouseholdManagementComponent_nb_option_53_Template, 2, 2, \"nb-option\", 13);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(54, \"div\", 9)(55, \"div\", 10)(56, \"label\", 31);\n          i0.ɵɵtext(57, \" \\u7C3D\\u56DE\\u72C0\\u614B \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(58, \"nb-select\", 32);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_Template_nb_select_ngModelChange_58_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.searchQuery.CSignStatusSelected, $event) || (ctx.searchQuery.CSignStatusSelected = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵtemplate(59, HouseholdManagementComponent_nb_option_59_Template, 2, 2, \"nb-option\", 13);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(60, \"div\", 9)(61, \"div\", 33)(62, \"button\", 34);\n          i0.ɵɵlistener(\"click\", function HouseholdManagementComponent_Template_button_click_62_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onSearch());\n          });\n          i0.ɵɵtext(63, \" \\u67E5\\u8A62 \");\n          i0.ɵɵelement(64, \"i\", 35);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(65, \"div\", 36)(66, \"div\", 33);\n          i0.ɵɵtemplate(67, HouseholdManagementComponent_button_67_Template, 2, 0, \"button\", 37);\n          i0.ɵɵelementStart(68, \"button\", 38);\n          i0.ɵɵlistener(\"click\", function HouseholdManagementComponent_Template_button_click_68_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onNavidateId(\"modify-floor-plan\"));\n          });\n          i0.ɵɵtext(69, \" \\u4FEE\\u6539\\u6A13\\u5C64\\u6236\\u578B \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(70, \"button\", 38);\n          i0.ɵɵlistener(\"click\", function HouseholdManagementComponent_Template_button_click_70_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.exportHouse());\n          });\n          i0.ɵɵtext(71, \" \\u532F\\u51FA\\u6236\\u5225\\u660E\\u7D30\\u6A94 \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(72, \"input\", 39, 0);\n          i0.ɵɵlistener(\"change\", function HouseholdManagementComponent_Template_input_change_72_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onFileSelected($event));\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(74, \"button\", 40);\n          i0.ɵɵlistener(\"click\", function HouseholdManagementComponent_Template_button_click_74_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.triggerFileInput());\n          });\n          i0.ɵɵtext(75, \" \\u532F\\u5165\\u66F4\\u65B0\\u6236\\u5225\\u660E\\u7D30\\u6A94 \");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(76, \"div\", 41)(77, \"table\", 42)(78, \"thead\")(79, \"tr\", 43)(80, \"th\", 44);\n          i0.ɵɵtext(81, \"\\u6236\\u578B\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(82, \"th\", 44);\n          i0.ɵɵtext(83, \"\\u6A13\\u5C64\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(84, \"th\", 44);\n          i0.ɵɵtext(85, \"\\u6236\\u5225\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(86, \"th\", 44);\n          i0.ɵɵtext(87, \"\\u985E\\u578B\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(88, \"th\", 44);\n          i0.ɵɵtext(89, \"\\u9032\\u5EA6\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(90, \"th\", 44);\n          i0.ɵɵtext(91, \"\\u7E73\\u6B3E\\u72C0\\u614B\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(92, \"th\", 44);\n          i0.ɵɵtext(93, \"\\u7C3D\\u56DE\\u72C0\\u614B\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(94, \"th\", 44);\n          i0.ɵɵtext(95, \"\\u72C0\\u614B\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(96, \"th\", 45);\n          i0.ɵɵtext(97, \"\\u64CD\\u4F5C\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(98, \"tbody\");\n          i0.ɵɵtemplate(99, HouseholdManagementComponent_tr_99_Template, 29, 12, \"tr\", 46);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(100, \"nb-card-footer\", 47)(101, \"ngb-pagination\", 48);\n          i0.ɵɵtwoWayListener(\"pageChange\", function HouseholdManagementComponent_Template_ngb_pagination_pageChange_101_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.pageIndex, $event) || (ctx.pageIndex = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵlistener(\"pageChange\", function HouseholdManagementComponent_Template_ngb_pagination_pageChange_101_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.pageChanged($event));\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(102, HouseholdManagementComponent_ng_template_102_Template, 6, 2, \"ng-template\", null, 1, i0.ɵɵtemplateRefExtractor)(104, HouseholdManagementComponent_ng_template_104_Template, 20, 5, \"ng-template\", null, 2, i0.ɵɵtemplateRefExtractor)(106, HouseholdManagementComponent_ng_template_106_Template, 38, 6, \"ng-template\", null, 3, i0.ɵɵtemplateRefExtractor);\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(11);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchQuery.CBuildCaseSelected);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngForOf\", ctx.userBuildCaseOptions);\n          i0.ɵɵadvance(5);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchQuery.CHouseTypeSelected);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngForOf\", ctx.houseTypeOptions);\n          i0.ɵɵadvance(6);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchQuery.CFrom);\n          i0.ɵɵadvance(4);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchQuery.CTo);\n          i0.ɵɵadvance(6);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchQuery.CHouseHoldSelected);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngForOf\", ctx.houseHoldOptions);\n          i0.ɵɵadvance(5);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchQuery.CPayStatusSelected);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngForOf\", ctx.payStatusOptions);\n          i0.ɵɵadvance(5);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchQuery.CProgressSelected);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngForOf\", ctx.progressOptions);\n          i0.ɵɵadvance(5);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchQuery.CIsEnableSeleted);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngForOf\", ctx.cIsEnableOptions);\n          i0.ɵɵadvance(5);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchQuery.CSignStatusSelected);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngForOf\", ctx.signStatusOptions);\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"ngIf\", ctx.isCreate);\n          i0.ɵɵadvance(32);\n          i0.ɵɵproperty(\"ngForOf\", ctx.houseList);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtwoWayProperty(\"page\", ctx.pageIndex);\n          i0.ɵɵproperty(\"pageSize\", ctx.pageSize)(\"collectionSize\", ctx.totalRecords);\n        }\n      },\n      dependencies: [CommonModule, i12.NgForOf, i12.NgIf, SharedModule, i13.DefaultValueAccessor, i13.NumberValueAccessor, i13.NgControlStatus, i13.MaxLengthValidator, i13.MinValidator, i13.MaxValidator, i13.NgModel, i3.NbCardComponent, i3.NbCardBodyComponent, i3.NbCardFooterComponent, i3.NbCardHeaderComponent, i3.NbCheckboxComponent, i3.NbInputDirective, i3.NbSelectComponent, i3.NbOptionComponent, i3.NbFormFieldComponent, i3.NbPrefixDirective, i3.NbIconComponent, i3.NbDatepickerDirective, i3.NbDatepickerComponent, i14.NgbPagination, i15.BreadcrumbComponent, i16.BaseLabelDirective, NbDatepickerModule, NbDateFnsDateModule],\n      styles: [\"/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsImZpbGUiOiJob3VzZWhvbGQtbWFuYWdlbWVudC5jb21wb25lbnQuc2NzcyJ9 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvcGFnZXMvaG91c2Vob2xkLW1hbmFnZW1lbnQvaG91c2Vob2xkLW1hbmFnZW1lbnQuY29tcG9uZW50LnNjc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IjtBQUNBLG9MQUFvTCIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["SharedModule", "CommonModule", "NbDatepickerModule", "BaseComponent", "concatMap", "tap", "NbDateFnsDateModule", "moment", "EEvent", "EnumHouseProgress", "EnumHouseType", "EnumPayStatus", "EnumSignStatus", "LocalStorageService", "STORAGE_KEY", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵproperty", "case_r2", "ɵɵadvance", "ɵɵtextInterpolate1", "CBuildCaseName", "case_r3", "label", "case_r4", "case_r5", "case_r6", "case_r7", "case_r8", "ɵɵlistener", "HouseholdManagementComponent_button_67_Template_button_click_0_listener", "ɵɵrestoreView", "_r9", "ctx_r9", "ɵɵnextContext", "dialogHouseholdMain_r11", "ɵɵreference", "ɵɵresetView", "openModel", "HouseholdManagementComponent_tr_99_button_18_Template_button_click_0_listener", "_r13", "item_r14", "$implicit", "dialogUpdateHousehold_r15", "openModelDetail", "ɵɵtemplate", "HouseholdManagementComponent_tr_99_button_18_Template", "HouseholdManagementComponent_tr_99_Template_button_click_19_listener", "_r12", "onNavidateBuildCaseIdHouseId", "searchQuery", "CBuildCaseSelected", "cID", "CID", "HouseholdManagementComponent_tr_99_Template_button_click_21_listener", "HouseholdManagementComponent_tr_99_Template_button_click_23_listener", "HouseholdManagementComponent_tr_99_Template_button_click_25_listener", "resetSecureKey", "HouseholdManagementComponent_tr_99_Template_button_click_27_listener", "dialogQuotation_r16", "openQuotation", "ɵɵtextInterpolate", "CHouseHold", "CFloor", "ɵɵtextInterpolate2", "CHouseType", "CIsChange", "CProgressName", "ɵɵtextInterpolate3", "CPayStatus", "CSignStatus", "CIsEnable", "isUpdate", "status_r19", "status_r20", "status_r22", "ɵɵtwoWayListener", "HouseholdManagementComponent_ng_template_102_nb_card_body_1_div_35_Template_nb_select_ngModelChange_3_listener", "$event", "_r21", "ɵɵtwoWayBindingSet", "detailSelected", "CPayStatusSelected", "HouseholdManagementComponent_ng_template_102_nb_card_body_1_div_35_nb_option_4_Template", "ɵɵtwoWayProperty", "options", "payStatusOptions", "status_r24", "HouseholdManagementComponent_ng_template_102_nb_card_body_1_div_36_Template_nb_select_ngModelChange_3_listener", "_r23", "CProgressSelected", "HouseholdManagementComponent_ng_template_102_nb_card_body_1_div_36_nb_option_4_Template", "progressOptions", "HouseholdManagementComponent_ng_template_102_nb_card_body_1_Template_nb_select_ngModelChange_4_listener", "_r18", "HouseholdManagementComponent_ng_template_102_nb_card_body_1_nb_option_5_Template", "HouseholdManagementComponent_ng_template_102_nb_card_body_1_Template_input_ngModelChange_9_listener", "houseDetail", "CHousehold", "HouseholdManagementComponent_ng_template_102_nb_card_body_1_Template_input_ngModelChange_13_listener", "HouseholdManagementComponent_ng_template_102_nb_card_body_1_Template_input_ngModelChange_17_listener", "CCustomerName", "HouseholdManagementComponent_ng_template_102_nb_card_body_1_Template_input_ngModelChange_21_listener", "CNationalId", "HouseholdManagementComponent_ng_template_102_nb_card_body_1_Template_input_ngModelChange_25_listener", "CMail", "HouseholdManagementComponent_ng_template_102_nb_card_body_1_Template_input_ngModelChange_29_listener", "CPhone", "HouseholdManagementComponent_ng_template_102_nb_card_body_1_Template_nb_select_ngModelChange_33_listener", "CHouseTypeSelected", "HouseholdManagementComponent_ng_template_102_nb_card_body_1_nb_option_34_Template", "HouseholdManagementComponent_ng_template_102_nb_card_body_1_div_35_Template", "HouseholdManagementComponent_ng_template_102_nb_card_body_1_div_36_Template", "HouseholdManagementComponent_ng_template_102_nb_card_body_1_Template_nb_checkbox_checkedChange_40_listener", "HouseholdManagementComponent_ng_template_102_nb_card_body_1_Template_nb_checkbox_checkedChange_45_listener", "ɵɵelement", "HouseholdManagementComponent_ng_template_102_nb_card_body_1_Template_input_ngModelChange_53_listener", "changeStartDate", "HouseholdManagementComponent_ng_template_102_nb_card_body_1_Template_input_ngModelChange_58_listener", "changeEndDate", "userBuildCaseOptions", "houseTypeOptions", "isChangePayStatus", "isChangeProgress", "StartDate_r25", "EndDate_r26", "HouseholdManagementComponent_ng_template_102_button_5_Template_button_click_0_listener", "_r28", "ref_r27", "dialogRef", "onSubmitDetail", "HouseholdManagementComponent_ng_template_102_nb_card_body_1_Template", "HouseholdManagementComponent_ng_template_102_Template_button_click_3_listener", "_r17", "onClose", "HouseholdManagementComponent_ng_template_102_button_5_Template", "isCreate", "HouseholdManagementComponent_ng_template_104_button_19_Template_button_click_0_listener", "_r31", "ref_r30", "addHouseHoldMain", "HouseholdManagementComponent_ng_template_104_Template_input_ngModelChange_7_listener", "_r29", "houseHoldMain", "CBuildingName", "HouseholdManagementComponent_ng_template_104_Template_input_ngModelChange_11_listener", "CHouseHoldCount", "HouseholdManagementComponent_ng_template_104_Template_input_ngModelChange_15_listener", "HouseholdManagementComponent_ng_template_104_Template_button_click_17_listener", "HouseholdManagementComponent_ng_template_104_button_19_Template", "HouseholdManagementComponent_ng_template_106_tr_26_Template_input_ngModelChange_2_listener", "item_r34", "_r33", "cItemName", "HouseholdManagementComponent_ng_template_106_tr_26_Template_input_ngModelChange_4_listener", "cUnitPrice", "calculateTotal", "HouseholdManagementComponent_ng_template_106_tr_26_Template_input_ngModelChange_6_listener", "cCount", "HouseholdManagementComponent_ng_template_106_tr_26_Template_button_click_13_listener", "i_r35", "index", "removeQuotationItem", "ɵɵclassProp", "cIsDefault", "formatCurrency", "HouseholdManagementComponent_ng_template_106_Template_button_click_5_listener", "_r32", "addQuotationItem", "HouseholdManagementComponent_ng_template_106_Template_button_click_7_listener", "loadDefaultItems", "HouseholdManagementComponent_ng_template_106_tr_26_Template", "HouseholdManagementComponent_ng_template_106_tr_27_Template", "HouseholdManagementComponent_ng_template_106_Template_button_click_34_listener", "ref_r36", "HouseholdManagementComponent_ng_template_106_Template_button_click_36_listener", "saveQuotation", "currentHouse", "quotationItems", "length", "totalAmount", "HouseholdManagementComponent", "constructor", "_allow", "enum<PERSON>elper", "dialogService", "message", "valid", "_houseService", "_houseHoldMainService", "_buildCaseService", "pettern", "router", "_eventService", "_ultilityService", "quotationService", "tempBuildCaseID", "pageFirst", "pageSize", "pageIndex", "totalRecords", "statusOptions", "value", "key", "cIsEnableOptions", "buildCaseOptions", "houseHoldOptions", "signStatusOptions", "getEnumOptions", "initDetail", "CHouseID", "CNationalID", "CProgress", "currentQuotationId", "selectedFile", "buildingSelectedOptions", "receive", "pipe", "res", "action", "payload", "subscribe", "ngOnInit", "GetSessionStorage", "HOUSE_SEARCH", "undefined", "previous_search", "JSON", "parse", "CHouseHoldSelected", "find", "x", "CSignStatusSelected", "CIsEnableSeleted", "CFrom", "CTo", "CBuildingNameSelected", "getListBuildCase", "onSearch", "sessionSave", "AddSessionStorage", "stringify", "getHouseList", "pageChanged", "newPage", "exportHouse", "apiHouseExportHousePost$Json", "CBuildCaseID", "Entries", "StatusCode", "downloadExcelFile", "showErrorMSG", "Message", "triggerFileInput", "fileInput", "nativeElement", "click", "onFileSelected", "event", "input", "target", "files", "importExcel", "formData", "FormData", "append", "apiHouseImportHousePost$Json", "body", "CFile", "showSucessMSG", "getListHouseHold", "apiHouseGetListHouseHoldPost$Json", "map", "e", "findIndex", "formatQuery", "bodyRequest", "PageIndex", "PageSize", "sortByFloorDescending", "arr", "sort", "a", "b", "apiHouseGetHouseListPost$Json", "houseList", "TotalItems", "onSelectionChangeBuildCase", "apiBuildCaseGetAllBuildCaseForSelectPost$Json", "CIsPagi", "CStatus", "setTimeout", "getHouseById", "ref", "apiHouseGetHouseByIdPost$Json", "CChangeStartDate", "Date", "CChangeEndDate", "CBuildCaseId", "findItemInArray", "open", "array", "item", "formatDate", "CChangeDate", "format", "editHouseArgsParam", "CId", "validation", "errorMessages", "showErrorMSGs", "apiHouseEditHousePost$Json", "close", "onSubmit", "bodyReq", "onNavidateId", "type", "id", "idURL", "navigate", "buildCaseId", "houseId", "confirm", "apiHouseResetHouseSecureKeyPost$Json", "clear", "required", "isStringMaxLength", "pattern", "MailPettern", "isPhoneNumber", "checkStartBeforeEnd", "validationHouseHoldMain", "isNaturalNumberInRange", "apiHouseHoldMainAddHouseHoldMainPost$Json", "dialog", "_this", "_asyncToGenerator", "response", "getQuotationByHouseId", "to<PERSON>romise", "console", "log", "CQuotationID", "Items", "Array", "isArray", "entry", "cHouseID", "cQuotationID", "CItemName", "CUnitPrice", "CCount", "cStatus", "CIsDefault", "warn", "error", "context", "closeOnBackdropClick", "push", "_this2", "request", "success", "data", "defaultItems", "splice", "reduce", "sum", "amount", "Intl", "NumberFormat", "style", "currency", "minimumFractionDigits", "_this3", "invalidItems", "filter", "trim", "items", "quotationId", "exportQuotation", "_this4", "blob", "url", "window", "URL", "createObjectURL", "link", "document", "createElement", "href", "download", "revokeObjectURL", "ɵɵdirectiveInject", "i1", "AllowHelper", "i2", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "i3", "NbDialogService", "i4", "MessageService", "i5", "ValidationHelper", "i6", "HouseService", "HouseHoldMainService", "BuildCaseService", "i7", "PetternHelper", "i8", "Router", "i9", "EventService", "i10", "UtilityService", "i11", "QuotationService", "selectors", "viewQuery", "HouseholdManagementComponent_Query", "rf", "ctx", "HouseholdManagementComponent_Template_nb_select_ngModelChange_11_listener", "_r1", "HouseholdManagementComponent_Template_nb_select_selected<PERSON><PERSON>e_11_listener", "HouseholdManagementComponent_nb_option_12_Template", "HouseholdManagementComponent_Template_nb_select_ngModelChange_17_listener", "HouseholdManagementComponent_nb_option_18_Template", "HouseholdManagementComponent_Template_input_ngModelChange_24_listener", "HouseholdManagementComponent_Template_input_ngModelChange_28_listener", "HouseholdManagementComponent_Template_nb_select_ngModelChange_34_listener", "HouseholdManagementComponent_nb_option_35_Template", "HouseholdManagementComponent_Template_nb_select_ngModelChange_40_listener", "HouseholdManagementComponent_nb_option_41_Template", "HouseholdManagementComponent_Template_nb_select_ngModelChange_46_listener", "HouseholdManagementComponent_nb_option_47_Template", "HouseholdManagementComponent_Template_nb_select_ngModelChange_52_listener", "HouseholdManagementComponent_nb_option_53_Template", "HouseholdManagementComponent_Template_nb_select_ngModelChange_58_listener", "HouseholdManagementComponent_nb_option_59_Template", "HouseholdManagementComponent_Template_button_click_62_listener", "HouseholdManagementComponent_button_67_Template", "HouseholdManagementComponent_Template_button_click_68_listener", "HouseholdManagementComponent_Template_button_click_70_listener", "HouseholdManagementComponent_Template_input_change_72_listener", "HouseholdManagementComponent_Template_button_click_74_listener", "HouseholdManagementComponent_tr_99_Template", "HouseholdManagementComponent_Template_ngb_pagination_pageChange_101_listener", "HouseholdManagementComponent_ng_template_102_Template", "ɵɵtemplateRefExtractor", "HouseholdManagementComponent_ng_template_104_Template", "HouseholdManagementComponent_ng_template_106_Template", "i12", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "i13", "DefaultValueAccessor", "NumberValueAccessor", "NgControlStatus", "MaxLengthValidator", "MinValidator", "MaxValidator", "NgModel", "NbCardComponent", "NbCardBodyComponent", "NbCardFooterComponent", "NbCardHeaderComponent", "NbCheckboxComponent", "NbInputDirective", "NbSelectComponent", "NbOptionComponent", "NbFormFieldComponent", "NbPrefixDirective", "NbIconComponent", "NbDatepickerDirective", "NbDatepickerComponent", "i14", "NgbPagination", "i15", "BreadcrumbComponent", "i16", "BaseLabelDirective", "styles"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\household-management\\household-management.component.ts", "C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\household-management\\household-management.component.html"], "sourcesContent": ["import { Component, ElementRef, OnInit, ViewChild } from '@angular/core';\r\nimport { SharedModule } from '../components/shared.module';\r\nimport { CommonModule } from '@angular/common';\r\nimport { NbDatepickerModule, NbDialogService } from '@nebular/theme';\r\nimport { MessageService } from 'src/app/shared/services/message.service';\r\nimport { ValidationHelper } from 'src/app/shared/helper/validationHelper';\r\nimport { BuildCaseService, HouseHoldMainService, HouseService } from 'src/services/api/services';\r\nimport { PetternHelper } from 'src/app/shared/helper/petternHelper';\r\n// import { TypeMailPipe } from 'src/app/@theme/pipes/mapping.pipe';\r\nimport { Router } from '@angular/router';\r\nimport { BaseComponent } from '../components/base/baseComponent';\r\nimport { AllowHelper } from 'src/app/shared/helper/allowHelper';\r\nimport { AddHouseHoldMain, EditHouseArgs, GetHouseList<PERSON>rgs, GetHouseListRes, TblHouse } from 'src/services/api/models';\r\nimport { concatMap, tap } from 'rxjs';\r\nimport { NbDateFnsDateModule } from '@nebular/date-fns';\r\nimport * as moment from 'moment';\r\nimport { EEvent, EventService, IEvent } from 'src/app/shared/services/event.service';\r\nimport { UtilityService } from 'src/app/shared/services/utility.service';\r\nimport { EnumHouseProgress } from 'src/app/shared/enum/enumHouseProgress';\r\nimport { EnumHelper } from 'src/app/shared/helper/enumHelper';\r\nimport { EnumHouseType } from 'src/app/shared/enum/enumHouseType';\r\nimport { EnumPayStatus } from 'src/app/shared/enum/enumPayStatus';\r\nimport { EnumSignStatus } from 'src/app/shared/enum/enumSignStatus';\r\nimport { LocalStorageService } from 'src/app/shared/services/local-storage.service';\r\nimport { STORAGE_KEY } from 'src/app/shared/constant/constant';\r\nimport { QuotationItem } from 'src/app/models/quotation.model';\r\nimport { QuotationService } from 'src/app/services/quotation.service';\r\n\r\n\r\nexport interface selectItem {\r\n  label: string,\r\n  value: number | string,\r\n  key?: string\r\n}\r\n\r\n// interface HouseDetailExtension {\r\n//   changeStartDate: string;\r\n//   changeEndDate: string;\r\n// }\r\nexport interface SearchQuery {\r\n  CBuildCaseSelected?: any | null;\r\n  CHouseTypeSelected?: any | null;\r\n  CBuildingNameSelected?: any | null;\r\n  CHouseHoldSelected?: any | null;\r\n  CPayStatusSelected?: any | null;\r\n  CProgressSelected?: any | null;\r\n  CSignStatusSelected?: any | null;\r\n  CIsEnableSeleted?: any | null;\r\n  CFrom?: any | null;\r\n  CTo?: any | null;\r\n}\r\n\r\n@Component({\r\n  selector: 'ngx-household-management',\r\n  templateUrl: './household-management.component.html',\r\n  styleUrls: ['./household-management.component.scss'],\r\n  standalone: true,\r\n  imports: [CommonModule, SharedModule, NbDatepickerModule, NbDateFnsDateModule,],\r\n})\r\n\r\nexport class HouseholdManagementComponent extends BaseComponent implements OnInit {\r\n  tempBuildCaseID: number = -1\r\n  constructor(\r\n    private _allow: AllowHelper,\r\n    private enumHelper: EnumHelper,\r\n    private dialogService: NbDialogService,\r\n    private message: MessageService,\r\n    private valid: ValidationHelper,\r\n    private _houseService: HouseService,\r\n    private _houseHoldMainService: HouseHoldMainService,\r\n    private _buildCaseService: BuildCaseService,\r\n    private pettern: PetternHelper,\r\n    private router: Router,\r\n    private _eventService: EventService,\r\n    private _ultilityService: UtilityService,\r\n    private quotationService: QuotationService\r\n  ) {\r\n    super(_allow)\r\n    this._eventService.receive().pipe(\r\n      tap((res: IEvent) => {\r\n        if (res.action == EEvent.GET_BUILDCASE && !!res.payload) {\r\n          this.tempBuildCaseID = res.payload\r\n        }\r\n      })\r\n    ).subscribe()\r\n  }\r\n\r\n  override pageFirst = 1;\r\n  override pageSize = 10;\r\n  override pageIndex = 1;\r\n  override totalRecords = 0;\r\n\r\n  statusOptions: selectItem[] = [\r\n    {\r\n      value: 0,\r\n      key: 'allow',\r\n      label: '允許',\r\n    },\r\n    {\r\n      value: 1,\r\n      key: 'not allowed',\r\n      label: '不允許',\r\n    }\r\n  ]\r\n\r\n  cIsEnableOptions = [\r\n    {\r\n      value: null,\r\n      key: 'all',\r\n      label: '全部',\r\n    },\r\n    {\r\n      value: true,\r\n      key: 'enable',\r\n      label: '啟用',\r\n    },\r\n    {\r\n      value: false,\r\n      key: 'deactivate',\r\n      label: '停用',\r\n    }\r\n  ]\r\n\r\n  searchQuery: SearchQuery\r\n  detailSelected: SearchQuery\r\n\r\n  buildCaseOptions: any[] = [{ label: '全部', value: '' }]\r\n  houseHoldOptions: any[] = [{ label: '全部', value: '' }]\r\n  progressOptions: any[] = [{ label: '全部', value: -1 }]\r\n  houseTypeOptions: any[] = [{ label: '全部', value: -1 }]\r\n  payStatusOptions: any[] = [{ label: '全部', value: -1 }]\r\n  signStatusOptions: any[] = [{ label: '全部', value: -1 }]\r\n\r\n  options = {\r\n    progressOptions: this.enumHelper.getEnumOptions(EnumHouseProgress),\r\n    payStatusOptions: this.enumHelper.getEnumOptions(EnumPayStatus),\r\n    houseTypeOptions: this.enumHelper.getEnumOptions(EnumHouseType),\r\n  }\r\n\r\n  userBuildCaseOptions: any\r\n  initDetail = {\r\n    CHouseID: 0,\r\n    CMail: \"\",\r\n    CIsChange: false,\r\n    CPayStatus: 0,\r\n    CIsEnable: false,\r\n    CCustomerName: \"\",\r\n    CNationalID: \"\",\r\n    CProgress: \"\",\r\n    CHouseType: 0,\r\n    CHouseHold: \"\",\r\n    CPhone: \"\"\r\n  }\r\n  quotationItems: QuotationItem[] = [];\r\n  totalAmount: number = 0;\r\n  currentHouse: any = null;\r\n  currentQuotationId: number = 0;\r\n\r\n  override ngOnInit(): void {\r\n    this.progressOptions = [\r\n      ...this.progressOptions,\r\n      ...this.enumHelper.getEnumOptions(EnumHouseProgress)\r\n    ]\r\n    this.houseTypeOptions = [\r\n      ...this.houseTypeOptions,\r\n      ...this.enumHelper.getEnumOptions(EnumHouseType)\r\n    ]\r\n    this.payStatusOptions = [\r\n      ...this.payStatusOptions,\r\n      ...this.enumHelper.getEnumOptions(EnumPayStatus)\r\n    ]\r\n    this.signStatusOptions = [\r\n      ...this.signStatusOptions,\r\n      ...this.enumHelper.getEnumOptions(EnumSignStatus)\r\n    ]\r\n\r\n    if (LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH) != null\r\n      && LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH) != undefined\r\n      && LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH) != \"\") {\r\n      let previous_search = JSON.parse(LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH));\r\n      this.searchQuery = {\r\n        CBuildCaseSelected: null,\r\n        // CBuildingNameSelected: previous_search.CBuildingNameSelected != null && previous_search.CBuildingNameSelected != undefined\r\n        //   ? this.buildingSelectedOptions.find(x => x.value == previous_search.CBuildingNameSelected.value)\r\n        //   : this.buildingSelectedOptions[0],\r\n        CHouseHoldSelected: previous_search.CHouseHoldSelected != null && previous_search.CHouseHoldSelected != undefined\r\n          ? this.houseHoldOptions.find(x => x.value == previous_search.CHouseHoldSelected.value)\r\n          : this.houseHoldOptions[0],\r\n        CHouseTypeSelected: previous_search.CHouseTypeSelected != null && previous_search.CHouseTypeSelected != undefined\r\n          ? this.houseTypeOptions.find(x => x.value == previous_search.CHouseTypeSelected.value)\r\n          : this.houseTypeOptions[0],\r\n        CPayStatusSelected: previous_search.CPayStatusSelected != null && previous_search.CPayStatusSelected != undefined\r\n          ? this.payStatusOptions.find(x => x.value == previous_search.CPayStatusSelected.value)\r\n          : this.payStatusOptions[0],\r\n        CProgressSelected: previous_search.CProgressSelected != null && previous_search.CProgressSelected != undefined\r\n          ? this.progressOptions.find(x => x.value == previous_search.CProgressSelected.value)\r\n          : this.progressOptions[0],\r\n        CSignStatusSelected: previous_search.CSignStatusSelected != null && previous_search.CSignStatusSelected != undefined\r\n          ? this.signStatusOptions.find(x => x.value == previous_search.CSignStatusSelected.value)\r\n          : this.signStatusOptions[0],\r\n        CIsEnableSeleted: previous_search.CIsEnableSeleted != null && previous_search.CIsEnableSeleted != undefined\r\n          ? this.cIsEnableOptions.find(x => x.value == previous_search.CIsEnableSeleted.value)\r\n          : this.cIsEnableOptions[0],\r\n        CFrom: previous_search.CFrom != null && previous_search.CFrom != undefined\r\n          ? previous_search.CFrom\r\n          : '',\r\n        CTo: previous_search.CTo != null && previous_search.CTo != undefined\r\n          ? previous_search.CTo\r\n          : ''\r\n      }\r\n    }\r\n    else {\r\n      this.searchQuery = {\r\n        CBuildCaseSelected: null,\r\n        CBuildingNameSelected: this.buildingSelectedOptions[0],\r\n        CHouseHoldSelected: this.houseHoldOptions[0],\r\n        CHouseTypeSelected: this.houseTypeOptions[0],\r\n        CPayStatusSelected: this.payStatusOptions[0],\r\n        CProgressSelected: this.progressOptions[0],\r\n        CSignStatusSelected: this.signStatusOptions[0],\r\n        CIsEnableSeleted: this.cIsEnableOptions[0],\r\n        CFrom: '',\r\n        CTo: ''\r\n      }\r\n    }\r\n    this.getListBuildCase()\r\n  }\r\n\r\n  onSearch() {\r\n    let sessionSave = {\r\n      CBuildCaseSelected: this.searchQuery.CBuildCaseSelected,\r\n      // CBuildingNameSelected: this.searchQuery.CBuildingNameSelected,\r\n      CFrom: this.searchQuery.CFrom,\r\n      CTo: this.searchQuery.CTo,\r\n      CHouseHoldSelected: this.searchQuery.CHouseHoldSelected,\r\n      CHouseTypeSelected: this.searchQuery.CHouseTypeSelected,\r\n      CIsEnableSeleted: this.searchQuery.CIsEnableSeleted,\r\n      CPayStatusSelected: this.searchQuery.CPayStatusSelected,\r\n      CProgressSelected: this.searchQuery.CProgressSelected,\r\n      CSignStatusSelected: this.searchQuery.CSignStatusSelected\r\n    }\r\n    LocalStorageService.AddSessionStorage(STORAGE_KEY.HOUSE_SEARCH, JSON.stringify(sessionSave));\r\n    this.getHouseList().subscribe()\r\n  }\r\n\r\n  pageChanged(newPage: number) {\r\n    this.pageIndex = newPage;\r\n    this.getHouseList().subscribe()\r\n  }\r\n\r\n  exportHouse() {\r\n    if (this.searchQuery.CBuildCaseSelected.cID) {\r\n      this._houseService.apiHouseExportHousePost$Json({\r\n        CBuildCaseID: this.searchQuery.CBuildCaseSelected.cID\r\n      }).subscribe(res => {\r\n        if (res.Entries && res.StatusCode == 0) {\r\n          this._ultilityService.downloadExcelFile(\r\n            res.Entries, '戶別資訊範本'\r\n          )\r\n        } else {\r\n          this.message.showErrorMSG(res.Message!);\r\n        }\r\n      })\r\n    }\r\n  }\r\n\r\n  selectedFile: File | null = null;\r\n  @ViewChild('fileInput') fileInput: ElementRef<HTMLInputElement>;\r\n\r\n\r\n  triggerFileInput(): void {\r\n    this.fileInput.nativeElement.click();\r\n  }\r\n\r\n  onFileSelected(event: Event): void {\r\n    const input = event.target as HTMLInputElement;\r\n    if (input.files && input.files.length > 0) {\r\n      this.selectedFile = input.files[0];\r\n      this.importExcel();\r\n    }\r\n  }\r\n\r\n  importExcel(): void {\r\n    if (this.selectedFile) {\r\n      const formData = new FormData();\r\n      formData.append('CFile', this.selectedFile);\r\n      this._houseService.apiHouseImportHousePost$Json({\r\n        body: {\r\n          CBuildCaseID: this.searchQuery.CBuildCaseSelected.cID,\r\n          CFile: this.selectedFile\r\n        }\r\n      }).subscribe(res => {\r\n        if (res.StatusCode === 0) {\r\n          this.message.showSucessMSG(res.Message!);\r\n          this.getHouseList().subscribe()\r\n        } else {\r\n          this.message.showErrorMSG(res.Message!);\r\n        }\r\n      });\r\n    }\r\n  }\r\n\r\n\r\n  getListHouseHold() {\r\n    this._houseService.apiHouseGetListHouseHoldPost$Json({\r\n      body: { CBuildCaseID: this.searchQuery.CBuildCaseSelected.cID }\r\n    }).subscribe(res => {\r\n      if (res.Entries && res.StatusCode == 0) {\r\n        this.houseHoldOptions = [{\r\n          value: '', label: '全部'\r\n        }, ...res.Entries.map(e => {\r\n          return { value: e, label: e }\r\n        })]\r\n        if (LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH) != null\r\n          && LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH) != undefined\r\n          && LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH) != \"\") {\r\n          let previous_search = JSON.parse(LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH));\r\n          if (previous_search.CHouseHoldSelected != null && previous_search.CHouseHoldSelected != undefined) {\r\n            let index = this.houseHoldOptions.findIndex((x: any) => x.value == previous_search.CHouseHoldSelected.value)\r\n            this.searchQuery.CHouseHoldSelected = this.houseHoldOptions[index]\r\n          } else {\r\n            this.searchQuery.CHouseHoldSelected = this.houseHoldOptions[0]\r\n          }\r\n        }\r\n      }\r\n    })\r\n  }\r\n\r\n  houseList: any\r\n  bodyRequest: GetHouseListArgs\r\n\r\n  formatQuery() {\r\n    this.bodyRequest = {\r\n      CBuildCaseID: this.searchQuery.CBuildCaseSelected.cID,\r\n      PageIndex: this.pageIndex,\r\n      PageSize: this.pageSize\r\n    }\r\n\r\n    // if (this.searchQuery.CBuildingNameSelected) {\r\n    //   this.bodyRequest['CBuildingName'] = this.searchQuery.CBuildingNameSelected.value\r\n    // }\r\n    if (this.searchQuery.CFrom && this.searchQuery.CTo) {\r\n      this.bodyRequest['CFloor'] = { CFrom: this.searchQuery.CFrom, CTo: this.searchQuery.CTo }\r\n    }\r\n    if (this.searchQuery.CHouseHoldSelected) {\r\n      this.bodyRequest['CHouseHold'] = this.searchQuery.CHouseHoldSelected.value\r\n    }\r\n    if (this.searchQuery.CHouseTypeSelected.value) {\r\n      this.bodyRequest['CHouseType'] = this.searchQuery.CHouseTypeSelected.value\r\n    }\r\n    if (typeof this.searchQuery.CIsEnableSeleted.value === \"boolean\") {\r\n      this.bodyRequest['CIsEnable'] = this.searchQuery.CIsEnableSeleted.value\r\n    }\r\n    if (this.searchQuery.CPayStatusSelected.value) {\r\n      this.bodyRequest['CPayStatus'] = this.searchQuery.CPayStatusSelected.value\r\n    }\r\n    if (this.searchQuery.CProgressSelected.value) {\r\n      this.bodyRequest['CProgress'] = this.searchQuery.CProgressSelected.value\r\n    }\r\n    if (this.searchQuery.CSignStatusSelected.value) {\r\n      this.bodyRequest['CSignStatus'] = this.searchQuery.CSignStatusSelected.value\r\n    }\r\n\r\n    return this.bodyRequest\r\n  }\r\n\r\n  sortByFloorDescending(arr: GetHouseListRes[]): GetHouseListRes[] {\r\n    return arr.sort((a, b) => (b.CFloor ?? 0) - (a.CFloor ?? 0));\r\n  }\r\n\r\n  getHouseList() {\r\n    return this._houseService.apiHouseGetHouseListPost$Json({\r\n      body: this.formatQuery()\r\n    }).pipe(\r\n      tap(res => {\r\n        if (res.Entries && res.StatusCode == 0) {\r\n          this.houseList = res.Entries;\r\n          this.totalRecords = res.TotalItems!;\r\n        }\r\n      })\r\n    )\r\n  }\r\n\r\n\r\n  userBuildCaseSelected: any\r\n  onSelectionChangeBuildCase() {\r\n    // this.getListBuilding()\r\n    this.getListHouseHold()\r\n    this.getHouseList().subscribe()\r\n  }\r\n  getListBuildCase() {\r\n    this._buildCaseService.apiBuildCaseGetAllBuildCaseForSelectPost$Json({\r\n      body: {\r\n        CIsPagi: false,\r\n        CStatus: 1,\r\n      }\r\n    }).pipe(\r\n      tap(res => {\r\n        if (res.Entries && res.StatusCode == 0) {\r\n          this.userBuildCaseOptions = res.Entries?.length ? res.Entries.map(res => {\r\n            return {\r\n              CBuildCaseName: res.CBuildCaseName,\r\n              cID: res.cID\r\n            }\r\n          }) : []\r\n\r\n          if (LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH) != null\r\n            && LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH) != undefined\r\n            && LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH) != \"\") {\r\n            let previous_search = JSON.parse(LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH));\r\n            if (previous_search.CBuildCaseSelected != null && previous_search.CBuildCaseSelected != undefined) {\r\n              let index = this.userBuildCaseOptions.findIndex((x: any) => x.cID == previous_search.CBuildCaseSelected.cID)\r\n              this.searchQuery.CBuildCaseSelected = this.userBuildCaseOptions[index]\r\n            } else {\r\n              this.searchQuery.CBuildCaseSelected = this.userBuildCaseOptions[0]\r\n            }\r\n          }\r\n          else {\r\n            this.searchQuery.CBuildCaseSelected = this.userBuildCaseOptions[0]\r\n          }\r\n        }\r\n      }),\r\n      tap(() => {\r\n        // this.getListBuilding()\r\n        this.getListHouseHold()\r\n        setTimeout(() => {\r\n          this.getHouseList().subscribe();\r\n        }, 500)\r\n      })\r\n    ).subscribe()\r\n  }\r\n\r\n  buildingSelected: any\r\n\r\n  buildingSelectedOptions: any[] = [\r\n    {\r\n      value: '', label: '全部'\r\n    }\r\n  ]\r\n\r\n  // getListBuilding() {\r\n  //   this._houseService.apiHouseGetListBuildingPost$Json({\r\n  //     body: {\r\n  //       CBuildCaseID: this.searchQuery.CBuildCaseSelected.cID\r\n  //     }\r\n  //   }).subscribe(res => {\r\n  //     if (res.Entries && res.StatusCode == 0) {\r\n  //       this.buildingSelectedOptions = [{\r\n  //         value: '', label: '全部'\r\n  //       }, ...res.Entries.map(e => {\r\n  //         return { value: e, label: e }\r\n  //       })]\r\n  //       if (LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH) != null\r\n  //         && LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH) != undefined\r\n  //         && LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH) != \"\") {\r\n  //         let previous_search = JSON.parse(LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH));\r\n  //         if (previous_search.CBuildingNameSelected != null && previous_search.CBuildingNameSelected != undefined) {\r\n  //           let index = this.buildingSelectedOptions.findIndex((x: any) => x.value == previous_search.CBuildingNameSelected.value)\r\n  //           this.searchQuery.CBuildingNameSelected = this.buildingSelectedOptions[index]\r\n  //         } else {\r\n  //           this.searchQuery.CBuildingNameSelected = this.buildingSelectedOptions[0]\r\n  //         }\r\n  //       }\r\n  //       else {\r\n  //         this.searchQuery.CBuildingNameSelected = this.buildingSelectedOptions[0]\r\n  //       }\r\n  //     }\r\n  //   })\r\n  // }\r\n\r\n  houseDetail: TblHouse & {\r\n    changeStartDate?: any;\r\n    changeEndDate?: any\r\n  }\r\n\r\n  getHouseById(CID: any, ref: any) {\r\n    this.detailSelected = {}\r\n    this._houseService.apiHouseGetHouseByIdPost$Json({\r\n      body: { CHouseID: CID }\r\n    }).subscribe(res => {\r\n      if (res.Entries && res.StatusCode == 0) {\r\n        this.houseDetail = {\r\n          ...res.Entries,\r\n          changeStartDate: res.Entries.CChangeStartDate ? new Date(res.Entries.CChangeStartDate) : undefined,\r\n          changeEndDate: res.Entries.CChangeEndDate ? new Date(res.Entries.CChangeEndDate) : undefined,\r\n        }\r\n\r\n        if (res.Entries.CBuildCaseId) {\r\n          this.detailSelected.CBuildCaseSelected = this.findItemInArray(this.userBuildCaseOptions, 'cID', res.Entries.CBuildCaseId)\r\n        }\r\n        this.detailSelected.CPayStatusSelected = this.findItemInArray(this.options.payStatusOptions, 'value', res.Entries.CPayStatus)\r\n        if (res.Entries.CHouseType) {\r\n          this.detailSelected.CHouseTypeSelected = this.findItemInArray(this.options.houseTypeOptions, 'value', res.Entries.CHouseType)\r\n        } else {\r\n          this.detailSelected.CHouseTypeSelected = this.options.houseTypeOptions[1]\r\n        }\r\n        this.detailSelected.CProgressSelected = this.findItemInArray(this.options.progressOptions, 'value', res.Entries.CProgress)\r\n\r\n        if (res.Entries.CBuildCaseId) {\r\n          if (this.houseHoldMain) {\r\n            this.houseHoldMain.CBuildCaseID = res.Entries.CBuildCaseId\r\n          }\r\n        }\r\n        this.dialogService.open(ref)\r\n      }\r\n\r\n    })\r\n  }\r\n\r\n\r\n  findItemInArray(array: any[], key: string, value: any) {\r\n    return array.find(item => item[key] === value);\r\n  }\r\n\r\n\r\n  openModelDetail(ref: any, item: any) {\r\n    this.getHouseById(item.CID, ref)\r\n  }\r\n\r\n  openModel(ref: any) {\r\n    this.houseHoldMain = {\r\n      CBuildingName: '',\r\n      CFloor: undefined,\r\n      CHouseHoldCount: undefined\r\n    }\r\n    this.dialogService.open(ref)\r\n  }\r\n\r\n  editHouseArgsParam: EditHouseArgs\r\n\r\n\r\n  formatDate(CChangeDate: string): string {\r\n    if (CChangeDate) {\r\n      return moment(CChangeDate).format('YYYY-MM-DDTHH:mm:ss')\r\n    }\r\n    return ''\r\n  }\r\n\r\n  onSubmitDetail(ref: any) {\r\n    this.houseDetail.CChangeStartDate = this.houseDetail.changeStartDate ? this.formatDate(this.houseDetail.changeStartDate) : '',\r\n      this.houseDetail.CChangeEndDate = this.houseDetail.changeEndDate ? this.formatDate(this.houseDetail.changeEndDate) : '',\r\n\r\n      this.editHouseArgsParam = {\r\n        CCustomerName: this.houseDetail.CCustomerName,\r\n        CHouseHold: this.houseDetail.CHousehold,\r\n        CHouseID: this.houseDetail.CId,\r\n        CHouseType: this.detailSelected.CHouseTypeSelected ? this.detailSelected.CHouseTypeSelected.value : null,\r\n        CIsChange: this.houseDetail.CIsChange,\r\n        CIsEnable: this.houseDetail.CIsEnable,\r\n        CMail: this.houseDetail.CMail,\r\n        CNationalID: this.houseDetail.CNationalId,\r\n        CPayStatus: this.detailSelected.CPayStatusSelected ? this.detailSelected.CPayStatusSelected.value : null,\r\n        CPhone: this.houseDetail.CPhone,\r\n        CProgress: this.detailSelected.CProgressSelected ? this.detailSelected.CProgressSelected.value : null,\r\n        CChangeStartDate: this.houseDetail.CChangeStartDate,\r\n        CChangeEndDate: this.houseDetail.CChangeEndDate\r\n      }\r\n    this.validation()\r\n    if (this.valid.errorMessages.length > 0) {\r\n      this.message.showErrorMSGs(this.valid.errorMessages);\r\n      return\r\n    }\r\n\r\n    this._houseService.apiHouseEditHousePost$Json({\r\n      body: this.editHouseArgsParam\r\n    }).pipe(\r\n      tap(res => {\r\n        if (res.StatusCode === 0) {\r\n          this.message.showSucessMSG(\"執行成功\");\r\n          ref.close();\r\n        } else {\r\n          this.message.showErrorMSG(res.Message!);\r\n          ref.close();\r\n        }\r\n      }),\r\n      concatMap(() => this.getHouseList())\r\n    ).subscribe();\r\n  }\r\n\r\n\r\n  onSubmit(ref: any) {\r\n    let bodyReq: EditHouseArgs = {\r\n      CCustomerName: this.houseDetail.CCustomerName,\r\n      CHouseHold: this.houseDetail.CHousehold,\r\n      CHouseID: this.houseDetail.CId,\r\n      CHouseType: this.houseDetail.CHouseType,\r\n      CIsChange: this.houseDetail.CIsChange,\r\n      CIsEnable: this.houseDetail.CIsEnable,\r\n      CMail: this.houseDetail.CMail,\r\n      CNationalID: this.houseDetail.CNationalId,\r\n      CPayStatus: this.houseDetail.CPayStatus,\r\n      CPhone: this.houseDetail.CPhone,\r\n      CProgress: this.houseDetail.CProgress,\r\n    }\r\n    this._houseService.apiHouseEditHousePost$Json({\r\n      body: bodyReq\r\n    }).subscribe(res => {\r\n      if (res.StatusCode === 0) {\r\n        this.message.showSucessMSG(\"執行成功\");\r\n        ref.close();\r\n      }\r\n    })\r\n\r\n  }\r\n\r\n  onClose(ref: any) {\r\n    ref.close();\r\n  }\r\n\r\n  onNavidateId(type: any, id?: any) {\r\n    const idURL = id ? id : this.searchQuery.CBuildCaseSelected.cID\r\n    this.router.navigate([`/pages/household-management/${type}`, idURL])\r\n  }\r\n\r\n  onNavidateBuildCaseIdHouseId(type: any, buildCaseId: any, houseId: any) {\r\n    this.router.navigate([`/pages/household-management/${type}`, buildCaseId, houseId])\r\n  }\r\n\r\n  resetSecureKey(item: any) {\r\n    if (confirm(\"您想重設密碼嗎？\")) {\r\n      this._houseService.apiHouseResetHouseSecureKeyPost$Json({\r\n        body: item.CID\r\n      }).subscribe(res => {\r\n        if (res.StatusCode == 0) {\r\n          this.message.showSucessMSG(\"執行成功\");\r\n        }\r\n      })\r\n    }\r\n  }\r\n\r\n  houseHoldMain: AddHouseHoldMain\r\n\r\n  validation() {\r\n    this.valid.clear();\r\n    this.valid.required('[建案名稱]', this.houseDetail.CId)\r\n    this.valid.required('[戶型名稱]', this.editHouseArgsParam.CHouseHold)\r\n    this.valid.isStringMaxLength('[棟別]', this.editHouseArgsParam.CHouseHold, 50)\r\n    this.valid.required('[樓層]', this.houseDetail.CFloor)\r\n    this.valid.isStringMaxLength('[客戶姓名]', this.editHouseArgsParam.CCustomerName, 50)\r\n    // if (this.editHouseArgsParam.CNationalID) {\r\n    //   this.valid.CheckTaiwanID(this.editHouseArgsParam.CNationalID)\r\n    // }\r\n    this.valid.pattern('[電子郵件]', this.editHouseArgsParam.CMail, this.pettern.MailPettern)\r\n    this.valid.isPhoneNumber('[聯絡電話]', this.editHouseArgsParam.CPhone)\r\n    this.valid.required('[進度]', this.editHouseArgsParam.CProgress)\r\n    this.valid.required('[戶別類型]', this.detailSelected.CHouseTypeSelected.value)\r\n    this.valid.required('[付款狀態]', this.detailSelected.CPayStatusSelected.value)\r\n    if (this.houseDetail.CChangeStartDate) {\r\n      this.valid.required('[客變結束日期]', this.houseDetail.CChangeEndDate)\r\n    }\r\n    if (this.houseDetail.CChangeEndDate) {\r\n      this.valid.required('[客變開始日期]', this.houseDetail.CChangeStartDate)\r\n    }\r\n    this.valid.checkStartBeforeEnd('[開放日期]', this.houseDetail.CChangeStartDate ? this.houseDetail.CChangeStartDate : '', this.houseDetail.CChangeEndDate ? this.houseDetail.CChangeEndDate : '')\r\n  }\r\n\r\n  validationHouseHoldMain() {\r\n    this.valid.clear();\r\n    this.valid.required('[建案]', this.houseHoldMain.CBuildCaseID)\r\n    this.valid.required('[棟別]', this.houseHoldMain.CBuildingName)\r\n    this.valid.isStringMaxLength('[棟別]', this.houseHoldMain.CBuildingName, 10)\r\n    this.valid.isNaturalNumberInRange('[當層最多戶數]', this.houseHoldMain.CFloor, 1, 100)\r\n    this.valid.isNaturalNumberInRange('[本棟總樓層]', this.houseHoldMain.CHouseHoldCount, 1, 100)\r\n  }\r\n\r\n\r\n  addHouseHoldMain(ref: any) {\r\n    this.houseHoldMain.CBuildCaseID = this.searchQuery.CBuildCaseSelected.cID,\r\n      this.validationHouseHoldMain()\r\n    if (this.valid.errorMessages.length > 0) {\r\n      this.message.showErrorMSGs(this.valid.errorMessages);\r\n      return\r\n    }\r\n    this._houseHoldMainService.apiHouseHoldMainAddHouseHoldMainPost$Json({\r\n      body: this.houseHoldMain\r\n    }).pipe(\r\n      tap(res => {\r\n        if (res.StatusCode === 0) {\r\n          this.message.showSucessMSG(\"執行成功\");\r\n          ref.close();\r\n        }\r\n      }),\r\n      concatMap(() => this.getHouseList())\r\n    ).subscribe();\r\n  }  // 開啟報價單對話框\r\n  async openQuotation(dialog: any, item: any) {\r\n    this.currentHouse = item;\r\n    this.quotationItems = [];\r\n    this.totalAmount = 0;\r\n    this.currentQuotationId = 0; // 重置報價單ID\r\n\r\n    // 載入現有報價資料\r\n    try {\r\n      const response = await this.quotationService.getQuotationByHouseId(item.CID).toPromise();\r\n      console.log('getQuotationByHouseId response:', response); // 除錯用\r\n\r\n      if (response && response.StatusCode === 0 && response.Entries) {\r\n        // 保存當前的報價單ID\r\n        this.currentQuotationId = response.Entries.CQuotationID || 0;\r\n        console.log('載入報價單ID:', this.currentQuotationId); // 除錯用\r\n\r\n        // 檢查 Entries 是否有 Items 陣列\r\n        if (response.Entries.Items && Array.isArray(response.Entries.Items)) {\r\n          // 將 API 回傳的資料轉換為 QuotationItem 格式\r\n          this.quotationItems = response.Entries.Items.map((entry: any) => ({\r\n            cHouseID: response.Entries.CHouseID || item.CID,\r\n            cQuotationID: response.Entries.CQuotationID,\r\n            cItemName: entry.CItemName || '',\r\n            cUnitPrice: entry.CUnitPrice || 0,\r\n            cCount: entry.CCount || 1,\r\n            cStatus: entry.CStatus || 1,\r\n            cIsDefault: entry.CIsDefault || false\r\n          }));\r\n          this.calculateTotal();\r\n          console.log('成功載入報價項目:', this.quotationItems.length, '項'); // 除錯用\r\n        } else {\r\n          console.warn('Entries.Items 不是陣列或不存在:', response.Entries);\r\n        }\r\n      } else {\r\n        console.warn('API 回傳資料格式不正確或 StatusCode 不是 0:', response);\r\n      }\r\n    } catch (error) {\r\n      console.error('載入報價資料失敗:', error);\r\n    }\r\n\r\n    this.dialogService.open(dialog, {\r\n      context: item,\r\n      closeOnBackdropClick: false\r\n    });\r\n  }\r\n  // 新增自定義報價項目\r\n  addQuotationItem() {\r\n    this.quotationItems.push({\r\n      cHouseID: this.currentHouse?.CID || 0,\r\n      cItemName: '', cUnitPrice: 0,\r\n      cCount: 1,\r\n      cStatus: 1,\r\n      cIsDefault: false\r\n    });\r\n  }\r\n  // 載入預設項目\r\n  async loadDefaultItems() {\r\n    try {\r\n      if (!this.currentHouse?.CID) {\r\n        this.message.showErrorMSG('請先選擇戶別');\r\n        return;\r\n      }\r\n\r\n      const request = {\r\n        CBuildCaseID: this.currentHouse.CBuildCaseID || 0,\r\n        CHouseID: this.currentHouse.CID\r\n      };\r\n\r\n      const response = await this.quotationService.loadDefaultItems(request).toPromise();\r\n      if (response?.success && response.data) {\r\n        const defaultItems = response.data.map((x: any) => ({\r\n          cQuotationID: x.CQuotationID,\r\n          cHouseID: this.currentHouse?.CID,\r\n          cItemName: x.CItemName,\r\n          cUnitPrice: x.CUnitPrice,\r\n          cCount: x.CCount,\r\n          cStatus: x.CStatus,\r\n          cIsDefault: true\r\n        }));\r\n        this.quotationItems.push(...defaultItems);\r\n        this.calculateTotal();\r\n        this.message.showSucessMSG('載入預設項目成功');\r\n      } else {\r\n        this.message.showErrorMSG(response?.message || '載入預設項目失敗');\r\n      }\r\n    } catch (error) {\r\n      console.error('載入預設項目錯誤:', error);\r\n      this.message.showErrorMSG('載入預設項目失敗');\r\n    }\r\n  }\r\n\r\n  // 移除報價項目\r\n  removeQuotationItem(index: number) {\r\n    const item = this.quotationItems[index];\r\n    this.quotationItems.splice(index, 1);\r\n    this.calculateTotal();\r\n  }\r\n\r\n  // 計算總金額\r\n  calculateTotal() {\r\n    this.totalAmount = this.quotationItems.reduce((sum, item) => {\r\n      return sum + (item.cUnitPrice * item.cCount);\r\n    }, 0);\r\n  }\r\n\r\n  // 格式化金額\r\n  formatCurrency(amount: number): string {\r\n    return new Intl.NumberFormat('zh-TW', {\r\n      style: 'currency',\r\n      currency: 'TWD',\r\n      minimumFractionDigits: 0\r\n    }).format(amount);\r\n  }\r\n\r\n  // 儲存報價單\r\n  async saveQuotation(ref: any) {\r\n    if (this.quotationItems.length === 0) {\r\n      this.message.showErrorMSG('請先新增報價項目');\r\n      return;\r\n    }\r\n\r\n    // 驗證必填欄位\r\n    const invalidItems = this.quotationItems.filter(item =>\r\n      !item.cItemName.trim() || item.cUnitPrice < 0 || item.cCount < 1\r\n    );\r\n\r\n    if (invalidItems.length > 0) {\r\n      this.message.showErrorMSG('請確認所有項目名稱、單價及數量都已正確填寫');\r\n      return;\r\n    } try {\r\n      const request = {\r\n        houseId: this.currentHouse.CID,\r\n        items: this.quotationItems,\r\n        quotationId: this.currentQuotationId // 傳遞當前的報價單ID\r\n      };\r\n\r\n      console.log('儲存報價單請求:', request); // 除錯用\r\n      const response = await this.quotationService.saveQuotation(request).toPromise();\r\n      if (response?.success) {\r\n        this.message.showSucessMSG('報價單儲存成功');\r\n        ref.close();\r\n      } else {\r\n        this.message.showErrorMSG(response?.message || '儲存失敗');\r\n      }\r\n    } catch (error) {\r\n      this.message.showErrorMSG('報價單儲存失敗');\r\n    }\r\n  }\r\n\r\n  // 匯出報價單\r\n  async exportQuotation() {\r\n    try {\r\n      const blob: Blob | undefined = await this.quotationService.exportQuotation(this.currentHouse.CID).toPromise();\r\n      if (blob) {\r\n        const url = window.URL.createObjectURL(blob);\r\n        const link = document.createElement('a');\r\n        link.href = url;\r\n        link.download = `報價單_${this.currentHouse.CHouseHold}_${this.currentHouse.CFloor}樓.pdf`;\r\n        link.click();\r\n        window.URL.revokeObjectURL(url);\r\n      } else {\r\n        this.message.showErrorMSG('匯出報價單失敗：未收到檔案資料');\r\n      }\r\n    } catch (error) {\r\n      this.message.showErrorMSG('匯出報價單失敗');\r\n    }\r\n  }\r\n}\r\n", "<nb-card accent=\"success\">\r\n  <nb-card-header>\r\n    <ngx-breadcrumb></ngx-breadcrumb>\r\n  </nb-card-header>\r\n  <nb-card-body>\r\n    <h1 class=\"font-bold text-[#818181]\">您可與此管理各戶別內之相關資訊，包含基本資料、繳款狀況、上傳客變圖面、檢視客變結果等等。 </h1>\r\n    <div class=\"d-flex flex-wrap\">\r\n      <div class=\"col-md-6\">\r\n        <div class=\"form-group d-flex align-items-center w-full\">\r\n          <label for=\"buildingName\" class=\"label col-3\">建案</label>\r\n          <nb-select placeholder=\"建案\" [(ngModel)]=\"searchQuery.CBuildCaseSelected\" class=\"col-9\"\r\n            (selectedChange)=\"onSelectionChangeBuildCase()\">\r\n            <nb-option *ngFor=\"let case of userBuildCaseOptions\" [value]=\"case\">\r\n              {{ case.CBuildCaseName }}\r\n            </nb-option>\r\n          </nb-select>\r\n        </div>\r\n      </div>\r\n      <div class=\"col-md-6\">\r\n        <div class=\"form-group d-flex align-items-center w-full\">\r\n          <label for=\"cHouseType\" class=\"label col-3\">類型</label>\r\n          <nb-select [(ngModel)]=\"searchQuery.CHouseTypeSelected\" class=\"col-9\">\r\n            <nb-option *ngFor=\"let case of houseTypeOptions\" [value]=\"case\">\r\n              {{ case.label }}\r\n            </nb-option>\r\n          </nb-select>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"col-md-6\">\r\n        <div class=\"form-group d-flex align-items-center\">\r\n          <label for=\"cFloorFrom\" class=\"label col-3\">樓\r\n          </label>\r\n          <nb-form-field class=\"ml-3\">\r\n            <input type=\"text\" id=\"CFrom\" nbInput class=\"w-full col-4\" [(ngModel)]=\"searchQuery.CFrom\">\r\n          </nb-form-field>\r\n          <label for=\"cFloorTo\" class=\"label col-1\">~\r\n          </label>\r\n          <nb-form-field class=\"mr-3\">\r\n            <input type=\"text\" id=\"CTo\" nbInput class=\"w-full\" [(ngModel)]=\"searchQuery.CTo\">\r\n          </nb-form-field>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"col-md-6\">\r\n        <!-- <div class=\"form-group d-flex align-items-center w-full\">\r\n          <label for=\"buildingName\" class=\"label col-3\">\r\n            棟別\r\n          </label>\r\n          <nb-select [(ngModel)]=\"searchQuery.CBuildingNameSelected\" class=\"col-9\">\r\n            <nb-option *ngFor=\"let case of buildingSelectedOptions\" [value]=\"case\">\r\n              {{ case.label }}\r\n            </nb-option>\r\n          </nb-select>\r\n        </div> -->\r\n      </div>\r\n\r\n      <div class=\"col-md-6\">\r\n        <div class=\"form-group d-flex align-items-center w-full\">\r\n          <label for=\"cHousehold\" class=\"label col-3\">\r\n            戶型\r\n          </label>\r\n          <nb-select placeholder=\"戶型\" [(ngModel)]=\"searchQuery.CHouseHoldSelected\" class=\"col-9\">\r\n            <nb-option *ngFor=\"let case of houseHoldOptions\" [value]=\"case\">\r\n              {{ case.label }}\r\n            </nb-option>\r\n          </nb-select>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"col-md-6\">\r\n        <div class=\"form-group d-flex align-items-center w-full\">\r\n          <label for=\"cPayStatus\" class=\"label col-3\">\r\n            繳款狀態\r\n          </label>\r\n          <nb-select placeholder=\"繳款狀態\" [(ngModel)]=\"searchQuery.CPayStatusSelected\" class=\"col-9\">\r\n            <nb-option *ngFor=\"let case of payStatusOptions\" [value]=\"case\">\r\n              {{ case.label }}\r\n            </nb-option>\r\n          </nb-select>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"col-md-6\">\r\n        <div class=\"form-group d-flex align-items-center w-full\">\r\n          <label for=\"cProgress\" class=\"label col-3\">\r\n            進度\r\n          </label>\r\n          <nb-select placeholder=\"進度\" [(ngModel)]=\"searchQuery.CProgressSelected\" class=\"col-9\">\r\n            <nb-option *ngFor=\"let case of progressOptions\" [value]=\"case\">\r\n              {{ case.label }}\r\n            </nb-option>\r\n          </nb-select>\r\n        </div>\r\n      </div>\r\n      <div class=\"col-md-6\">\r\n        <div class=\"form-group d-flex align-items-center w-full\">\r\n          <label for=\"cStatus\" class=\"label col-3\">\r\n            狀態\r\n          </label>\r\n          <nb-select placeholder=\"狀態\" [(ngModel)]=\"searchQuery.CIsEnableSeleted\" class=\"col-9\">\r\n            <nb-option *ngFor=\"let case of cIsEnableOptions\" [value]=\"case\">\r\n              {{ case.label }}\r\n            </nb-option>\r\n          </nb-select>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"col-md-6\">\r\n        <div class=\"form-group d-flex align-items-center w-full\">\r\n          <label for=\"cSignStatus\" class=\"label col-3\">\r\n            簽回狀態\r\n          </label>\r\n          <nb-select placeholder=\"簽回狀態\" [(ngModel)]=\"searchQuery.CSignStatusSelected\" class=\"col-9\">\r\n            <nb-option *ngFor=\"let case of signStatusOptions\" [value]=\"case\">\r\n              {{ case.label }}\r\n            </nb-option>\r\n          </nb-select>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"col-md-6\">\r\n        <div class=\"d-flex justify-content-end w-full\">\r\n\r\n          <button class=\"btn btn-secondary btn-sm\" (click)=\"onSearch()\">\r\n            查詢 <i class=\"fas fa-search\"></i>\r\n          </button>\r\n        </div>\r\n      </div>\r\n      <div class=\"col-md-12\">\r\n        <div class=\"d-flex justify-content-end w-full\">\r\n          <button class=\"btn btn-info mx-1 btn-sm\" *ngIf=\"isCreate\" (click)=\"openModel(dialogHouseholdMain)\">\r\n            批次新增戶別資料\r\n          </button>\r\n          <button class=\"btn btn-info mx-1 btn-sm\" (click)=\"onNavidateId('modify-floor-plan')\">\r\n            修改樓層戶型\r\n          </button>\r\n          <!-- <button class=\"btn btn-info mx-1 btn-sm\" (click)=\"onNavidateId('standard-house-plan')\">\r\n            3.設定戶型標準圖\r\n          </button> -->\r\n          <button class=\"btn btn-info mx-1 btn-sm\" (click)=\"exportHouse()\">\r\n            匯出戶別明細檔\r\n          </button>\r\n          <input type=\"file\" #fileInput style=\"display:none\" (change)=\"onFileSelected($event)\" accept=\".xlsx, .xls\" />\r\n          <button class=\"btn btn-info btn-sm\" (click)=\"triggerFileInput()\">\r\n            匯入更新戶別明細檔\r\n          </button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <div class=\"table-responsive mt-4\">\r\n      <table class=\"table table-striped border \" style=\"min-width: 1000px; background-color:#f3f3f3;\">\r\n        <thead>\r\n          <tr style=\"background-color: #27ae60; color: white;\">\r\n            <!-- <th scope=\"col\" class=\"col-1\">棟別</th> -->\r\n            <th scope=\"col\" class=\"col-1\">戶型</th>\r\n            <th scope=\"col\" class=\"col-1\">樓層</th>\r\n            <th scope=\"col\" class=\"col-1\">戶別</th>\r\n            <th scope=\"col\" class=\"col-1\">類型</th>\r\n            <th scope=\"col\" class=\"col-1\">進度</th>\r\n            <th scope=\"col\" class=\"col-1\">繳款狀態</th>\r\n            <th scope=\"col\" class=\"col-1\">簽回狀態</th>\r\n            <th scope=\"col\" class=\"col-1\">狀態</th>\r\n            <th scope=\"col\" class=\"col-4\">操作</th>\r\n          </tr>\r\n        </thead>\r\n        <tbody>\r\n          <tr *ngFor=\"let item of houseList ; let i = index\">\r\n            <!-- <td>{{ item.CBuildingName}}</td> -->\r\n            <td>{{ item.CHouseHold}}</td>\r\n            <td>{{ item.CFloor}}</td>\r\n            <td>\r\n              {{ item.CHouseType === 2 ? '銷售戶' : ''}}\r\n              {{item.CHouseType === 1 ? '地主戶' : ''}}\r\n            </td>\r\n            <td>{{ item.CIsChange === true ? '客變' : (item.CIsChange === false ? '標準' :'') }}</td>\r\n            <td>{{ item.CProgressName}}</td>\r\n            <td>\r\n              {{item.CPayStatus === 0 ? '未付款': ''}}\r\n              {{item.CPayStatus === 1 ? '已付款': ''}}\r\n              {{item.CPayStatus === 2 ? '無須付款': ''}}\r\n            </td>\r\n            <td>{{ (item.CSignStatus === 0 || item.CSignStatus == null) ? '未簽回' : '已簽回' }}</td>\r\n            <td>{{ item.CIsEnable ? '啟用' : '停用'}}</td>\r\n            <td class=\"text-center w-32 px-0\">\r\n              <button *ngIf=\"isUpdate\" class=\"btn btn-outline-success btn-sm text-left m-[2px]\"\r\n                (click)=\"openModelDetail(dialogUpdateHousehold, item)\">\r\n                編輯\r\n              </button>\r\n              <button class=\"btn btn-outline-success btn-sm m-[2px]\"\r\n                (click)=\"onNavidateBuildCaseIdHouseId('customer-change-picture', searchQuery.CBuildCaseSelected.cID, item.CID )\">\r\n                洽談紀錄\r\n              </button>\r\n              <button class=\"btn btn-outline-success btn-sm m-[2px]\"\r\n                (click)=\"onNavidateBuildCaseIdHouseId('sample-selection-result', searchQuery.CBuildCaseSelected.cID, item.CID )\">\r\n                客變確認圖說\r\n              </button>\r\n              <button class=\"btn btn-outline-success btn-sm m-[2px]\"\r\n                (click)=\"onNavidateBuildCaseIdHouseId('finaldochouse_management', searchQuery.CBuildCaseSelected.cID, item.CID )\">\r\n                簽署文件歷程\r\n              </button>\r\n              <button class=\"btn btn-outline-success btn-sm m-[2px]\" (click)=\"resetSecureKey(item)\">\r\n                重置密碼\r\n              </button>\r\n              <button class=\"btn btn-outline-success btn-sm m-[2px]\" (click)=\"openQuotation(dialogQuotation, item)\">\r\n                報價單\r\n              </button>\r\n            </td>\r\n          </tr>\r\n        </tbody>\r\n      </table>\r\n    </div>\r\n  </nb-card-body>\r\n  <nb-card-footer class=\"d-flex justify-content-center\">\r\n    <ngb-pagination [(page)]=\"pageIndex\" [pageSize]=\"pageSize\" [collectionSize]=\"totalRecords\"\r\n      (pageChange)=\"pageChanged($event)\" aria-label=\"Pagination\">\r\n    </ngb-pagination>\r\n  </nb-card-footer>\r\n</nb-card>\r\n\r\n<ng-template #dialogUpdateHousehold let-dialog let-ref=\"dialogRef\">\r\n  <nb-card style=\"width:500px; max-height: 95vh\">\r\n    <!-- <nb-card-header>\r\n    </nb-card-header> -->\r\n    <nb-card-body class=\"px-4\" *ngIf=\"houseDetail\">\r\n      <div class=\"form-group\">\r\n        <label for=\"cBuildCaseId\" class=\"required-field mr-4\" style=\"min-width:75px\" baseLabel>\r\n          建案名稱\r\n        </label>\r\n        <nb-select placeholder=\"建案名稱\" [(ngModel)]=\"detailSelected.CBuildCaseSelected\" class=\"w-full\" disabled=\"true\">\r\n          <nb-option *ngFor=\"let status of userBuildCaseOptions\" [value]=\"status\">\r\n            {{ status.CBuildCaseName }}\r\n          </nb-option>\r\n        </nb-select>\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label for=\"cHousehold\" class=\"required-field mr-4\" style=\"min-width:75px\" baseLabel>\r\n          戶型名稱\r\n        </label>\r\n        <input type=\"text\" class=\"w-full\" nbInput placeholder=\"戶型名稱\" [(ngModel)]=\"houseDetail.CHousehold\" />\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label for=\"cFloor\" class=\"required-field mr-4\" style=\"min-width:75px\" baseLabel>\r\n          樓層\r\n        </label>\r\n        <input type=\"number\" class=\"w-full\" nbInput placeholder=\"樓層\" [(ngModel)]=\"houseDetail.CFloor\" min=\"1\" max=\"100\"\r\n          disabled=\"true\" />\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label for=\"cCustomerName\" class=\"mr-4\" style=\"min-width:75px\" baseLabel>\r\n          客戶姓名\r\n        </label>\r\n        <input type=\"text\" class=\"w-full\" nbInput placeholder=\"客戶姓名\" [(ngModel)]=\"houseDetail.CCustomerName\" />\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label for=\"cNationalId\" class=\"mr-4\" style=\"min-width:75px\" baseLabel>\r\n          身分證字號\r\n        </label>\r\n        <input type=\"text\" class=\"w-full\" nbInput placeholder=\"身分證字號\" [(ngModel)]=\"houseDetail.CNationalId\"\r\n          maxlength=\"20\" />\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label for=\"cMail\" class=\"mr-4\" style=\"min-width:75px\" baseLabel>\r\n          電子郵件\r\n        </label>\r\n        <input type=\"text\" class=\"w-full\" nbInput placeholder=\"電子郵件\" [(ngModel)]=\"houseDetail.CMail\" />\r\n      </div>\r\n      <div class=\"form-group\">\r\n        <label for=\"cPhone\" class=\"mr-4\" style=\"min-width:75px\" baseLabel>\r\n          聯絡電話\r\n        </label>\r\n        <input type=\"text\" class=\"w-full\" nbInput placeholder=\"聯絡電話\" [(ngModel)]=\"houseDetail.CPhone\" />\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label for=\"cHouseType\" class=\"required-field mr-4\" style=\"min-width:75px\" baseLabel>\r\n          戶別類型\r\n        </label>\r\n        <nb-select placeholder=\"戶別類型\" [(ngModel)]=\"detailSelected.CHouseTypeSelected\" class=\"w-full\">\r\n          <nb-option *ngFor=\"let status of options.houseTypeOptions\" [value]=\"status\">\r\n            {{ status.label }}\r\n          </nb-option>\r\n        </nb-select>\r\n      </div>\r\n\r\n      <div class=\"form-group\" *ngIf=\"isChangePayStatus\">\r\n        <label for=\"cPayStatus\" class=\"required-field mr-4\" style=\"min-width:75px\" baseLabel>\r\n          付款狀態\r\n        </label>\r\n        <nb-select placeholder=\"付款狀態\" [(ngModel)]=\"detailSelected.CPayStatusSelected\" class=\"w-full\">\r\n          <nb-option *ngFor=\"let status of options.payStatusOptions\" [value]=\"status\">\r\n            {{ status.label }}\r\n          </nb-option>\r\n        </nb-select>\r\n      </div>\r\n      <div class=\"form-group\" *ngIf=\"isChangeProgress\">\r\n        <label for=\"cProgress\" class=\"required-field mr-4\" style=\"min-width:75px\" baseLabel>\r\n          進度\r\n        </label>\r\n        <nb-select placeholder=\"進度\" [(ngModel)]=\"detailSelected.CProgressSelected\" class=\"w-full\">\r\n          <nb-option *ngFor=\"let status of options.progressOptions\" [value]=\"status\">\r\n            {{ status.label }}\r\n          </nb-option>\r\n        </nb-select>\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label for=\"cIsChange\" class=\"mr-4\" style=\"min-width:75px\" baseLabel>\r\n          是否客變\r\n        </label>\r\n        <nb-checkbox status=\"basic\" [(checked)]=\"houseDetail.CIsChange\">是\r\n        </nb-checkbox>\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label for=\"cIsEnable\" class=\"mr-4\" style=\"min-width:75px\" baseLabel>\r\n          是否啟用\r\n        </label>\r\n        <nb-checkbox status=\"basic\" [(checked)]=\"houseDetail.CIsEnable\">是\r\n        </nb-checkbox>\r\n      </div>\r\n\r\n      <div class=\"form-group flex flex-row\">\r\n        <label for=\"cIsEnable\" class=\"mr-4 content-center\" style=\"min-width:75px\" baseLabel>\r\n          客變時段\r\n        </label>\r\n        <div class=\"max-w-xs flex flex-row\">\r\n          <nb-form-field class=\"w-1/2\">\r\n            <nb-icon nbPrefix icon=\"calendar-outline\"></nb-icon>\r\n            <input nbInput type=\"text\" id=\"StartDate\" placeholder=\"yyyy-mm-dd\" [nbDatepicker]=\"StartDate\"\r\n              class=\"w-[42%] mr-2\" [(ngModel)]=\"houseDetail.changeStartDate\">\r\n            <nb-datepicker #StartDate format=\"yyyy-MM-dd\"></nb-datepicker>\r\n          </nb-form-field>\r\n          <nb-form-field class=\"w-1/2\">\r\n            <nb-icon nbPrefix icon=\"calendar-outline\"></nb-icon>\r\n            <input nbInput type=\"text\" id=\"EndDate\" placeholder=\"yyyy-mm-dd\" [nbDatepicker]=\"EndDate\"\r\n              class=\"w-[42%] ml-2\" [(ngModel)]=\"houseDetail.changeEndDate\">\r\n            <nb-datepicker #EndDate format=\"yyyy-MM-dd\"></nb-datepicker>\r\n          </nb-form-field>\r\n        </div>\r\n      </div>\r\n    </nb-card-body>\r\n    <nb-card-footer class=\"d-flex justify-content-center\">\r\n      <button class=\"btn btn-outline-secondary m-2 px-8\" (click)=\"onClose(ref)\">取消</button>\r\n      <button class=\"btn btn-primary m-2 bg-[#169BD5] px-8\" *ngIf=\"isCreate\" (click)=\"onSubmitDetail(ref)\">送出</button>\r\n    </nb-card-footer>\r\n  </nb-card>\r\n</ng-template>\r\n\r\n<ng-template #dialogHouseholdMain let-dialog let-ref=\"dialogRef\">\r\n  <nb-card style=\"width:500px; max-height: 95vh\">\r\n    <nb-card-header>\r\n      戶別管理 》批次新增戶別資料\r\n    </nb-card-header>\r\n    <nb-card-body class=\"px-4\">\r\n      <div class=\"form-group\">\r\n        <label for=\"cBuildingName\" class=\"required-field mr-4\" style=\"min-width:75px\" baseLabel>\r\n          棟別\r\n        </label>\r\n        <input type=\"text\" class=\"w-full\" nbInput placeholder=\"棟別\" [(ngModel)]=\"houseHoldMain.CBuildingName\" />\r\n      </div>\r\n      <div class=\"form-group\">\r\n        <label for=\"CHouseHoldCount\" class=\"required-field mr-4\" style=\"min-width:75px\" baseLabel>當層最多戶數\r\n        </label>\r\n        <input type=\"number\" class=\"w-full\" nbInput placeholder=\"當層最多戶數\" [(ngModel)]=\"houseHoldMain.CHouseHoldCount\" />\r\n      </div>\r\n      <div class=\"form-group\">\r\n        <label for=\"CFloor\" class=\"required-field mr-4\" style=\"min-width:75px\" baseLabel>本棠總樓層\r\n        </label>\r\n        <input type=\"number\" class=\"w-full\" nbInput placeholder=\"本棠總樓層\" [(ngModel)]=\"houseHoldMain.CFloor\" />\r\n      </div>\r\n    </nb-card-body>\r\n    <nb-card-footer class=\"d-flex justify-content-center\">\r\n      <button class=\"btn btn-primary mr-4\" (click)=\"onClose(ref)\">{{ '關閉'}}</button>\r\n      <button class=\"btn btn-primary\" *ngIf=\"isCreate\" (click)=\"addHouseHoldMain(ref)\">儲存</button>\r\n    </nb-card-footer>\r\n  </nb-card>\r\n</ng-template>\r\n\r\n<!-- 報價單對話框 -->\r\n<ng-template #dialogQuotation let-dialog let-ref=\"dialogRef\">\r\n  <nb-card style=\"width:900px; max-height: 95vh\">\r\n    <nb-card-header>\r\n      報價單 - {{ currentHouse?.CHouseHold }} ({{ currentHouse?.CFloor }}樓)\r\n    </nb-card-header>\r\n    <nb-card-body>\r\n      <div class=\"mb-4 d-flex justify-content-between\">\r\n        <button class=\"btn btn-info btn-sm\" (click)=\"addQuotationItem()\">\r\n          + 新增自定義項目\r\n        </button>\r\n        <button class=\"btn btn-secondary btn-sm\" (click)=\"loadDefaultItems()\">\r\n          載入預設項目\r\n        </button>\r\n      </div>\r\n\r\n      <div class=\"table-responsive\">\r\n        <table class=\"table table-bordered\">\r\n          <thead>\r\n            <tr>\r\n              <th width=\"30%\">項目名稱</th>\r\n              <th width=\"20%\">單價 (元)</th>\r\n              <th width=\"10%\">數量</th>\r\n              <th width=\"20%\">小計 (元)</th>\r\n              <th width=\"10%\">類型</th>\r\n              <th width=\"10%\">操作</th>\r\n            </tr>\r\n          </thead>\r\n          <tbody>\r\n            <tr *ngFor=\"let item of quotationItems; let i = index\">\r\n              <td>\r\n                <input type=\"text\" nbInput [(ngModel)]=\"item.cItemName\" [disabled]=\"item.cIsDefault\" class=\"w-full\"\r\n                  [class.bg-light]=\"item.cIsDefault\">\r\n              </td>\r\n              <td>\r\n                <input type=\"number\" nbInput [(ngModel)]=\"item.cUnitPrice\" (ngModelChange)=\"calculateTotal()\"\r\n                  class=\"w-full\" min=\"0\" step=\"0.01\">\r\n              </td>\r\n              <td>\r\n                <input type=\"number\" nbInput [(ngModel)]=\"item.cCount\" (ngModelChange)=\"calculateTotal()\" class=\"w-full\"\r\n                  min=\"1\" step=\"1\">\r\n              </td>\r\n              <td class=\"text-right\">\r\n                {{ formatCurrency(item.cUnitPrice * item.cCount) }}\r\n              </td>\r\n              <td>\r\n                <span class=\"badge\" [class.badge-primary]=\"item.cIsDefault\" [class.badge-secondary]=\"!item.cIsDefault\">\r\n                  {{ item.cIsDefault ? '預設' : '自定義' }}\r\n                </span>\r\n              </td>\r\n              <td>\r\n                <button class=\"btn btn-danger btn-sm\" (click)=\"removeQuotationItem(i)\">\r\n                  刪除\r\n                </button>\r\n              </td>\r\n            </tr>\r\n            <tr *ngIf=\"quotationItems.length === 0\">\r\n              <td colspan=\"6\" class=\"text-center text-muted py-4\">\r\n                請點擊「新增自定義項目」或「載入預設項目」開始建立報價單\r\n              </td>\r\n            </tr>\r\n          </tbody>\r\n        </table>\r\n      </div>\r\n\r\n      <div class=\"text-right mt-4\">\r\n        <h4 class=\"text-primary\">總金額: {{ formatCurrency(totalAmount) }}</h4>\r\n      </div>\r\n    </nb-card-body>\r\n    <nb-card-footer class=\"d-flex justify-content-between\">\r\n      <div>\r\n        <!-- <button class=\"btn btn-outline-info btn-sm\" (click)=\"exportQuotation()\"\r\n          [disabled]=\"quotationItems.length === 0\">\r\n          匯出報價單\r\n        </button> -->\r\n      </div>\r\n      <div>\r\n        <button class=\"btn btn-outline-secondary m-2\" (click)=\"onClose(ref)\">取消</button>\r\n        <button class=\"btn btn-primary m-2\" (click)=\"saveQuotation(ref)\" [disabled]=\"quotationItems.length === 0\">\r\n          儲存報價單\r\n        </button>\r\n      </div>\r\n    </nb-card-footer>\r\n  </nb-card>\r\n</ng-template>\r\n"], "mappings": ";AACA,SAASA,YAAY,QAAQ,6BAA6B;AAC1D,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,kBAAkB,QAAyB,gBAAgB;AAOpE,SAASC,aAAa,QAAQ,kCAAkC;AAGhE,SAASC,SAAS,EAAEC,GAAG,QAAQ,MAAM;AACrC,SAASC,mBAAmB,QAAQ,mBAAmB;AACvD,OAAO,KAAKC,MAAM,MAAM,QAAQ;AAChC,SAASC,MAAM,QAA8B,uCAAuC;AAEpF,SAASC,iBAAiB,QAAQ,uCAAuC;AAEzE,SAASC,aAAa,QAAQ,mCAAmC;AACjE,SAASC,aAAa,QAAQ,mCAAmC;AACjE,SAASC,cAAc,QAAQ,oCAAoC;AACnE,SAASC,mBAAmB,QAAQ,+CAA+C;AACnF,SAASC,WAAW,QAAQ,kCAAkC;;;;;;;;;;;;;;;;;;;;;ICZlDC,EAAA,CAAAC,cAAA,oBAAoE;IAClED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAFyCH,EAAA,CAAAI,UAAA,UAAAC,OAAA,CAAc;IACjEL,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAAO,kBAAA,MAAAF,OAAA,CAAAG,cAAA,MACF;;;;;IAQAR,EAAA,CAAAC,cAAA,oBAAgE;IAC9DD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAFqCH,EAAA,CAAAI,UAAA,UAAAK,OAAA,CAAc;IAC7DT,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAAO,kBAAA,MAAAE,OAAA,CAAAC,KAAA,MACF;;;;;IAuCAV,EAAA,CAAAC,cAAA,oBAAgE;IAC9DD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAFqCH,EAAA,CAAAI,UAAA,UAAAO,OAAA,CAAc;IAC7DX,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAAO,kBAAA,MAAAI,OAAA,CAAAD,KAAA,MACF;;;;;IAWAV,EAAA,CAAAC,cAAA,oBAAgE;IAC9DD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAFqCH,EAAA,CAAAI,UAAA,UAAAQ,OAAA,CAAc;IAC7DZ,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAAO,kBAAA,MAAAK,OAAA,CAAAF,KAAA,MACF;;;;;IAWAV,EAAA,CAAAC,cAAA,oBAA+D;IAC7DD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAFoCH,EAAA,CAAAI,UAAA,UAAAS,OAAA,CAAc;IAC5Db,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAAO,kBAAA,MAAAM,OAAA,CAAAH,KAAA,MACF;;;;;IAUAV,EAAA,CAAAC,cAAA,oBAAgE;IAC9DD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAFqCH,EAAA,CAAAI,UAAA,UAAAU,OAAA,CAAc;IAC7Dd,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAAO,kBAAA,MAAAO,OAAA,CAAAJ,KAAA,MACF;;;;;IAWAV,EAAA,CAAAC,cAAA,oBAAiE;IAC/DD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAFsCH,EAAA,CAAAI,UAAA,UAAAW,OAAA,CAAc;IAC9Df,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAAO,kBAAA,MAAAQ,OAAA,CAAAL,KAAA,MACF;;;;;;IAeFV,EAAA,CAAAC,cAAA,iBAAmG;IAAzCD,EAAA,CAAAgB,UAAA,mBAAAC,wEAAA;MAAAjB,EAAA,CAAAkB,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAApB,EAAA,CAAAqB,aAAA;MAAA,MAAAC,uBAAA,GAAAtB,EAAA,CAAAuB,WAAA;MAAA,OAAAvB,EAAA,CAAAwB,WAAA,CAASJ,MAAA,CAAAK,SAAA,CAAAH,uBAAA,CAA8B;IAAA,EAAC;IAChGtB,EAAA,CAAAE,MAAA,yDACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;IAqDLH,EAAA,CAAAC,cAAA,iBACyD;IAAvDD,EAAA,CAAAgB,UAAA,mBAAAU,8EAAA;MAAA1B,EAAA,CAAAkB,aAAA,CAAAS,IAAA;MAAA,MAAAC,QAAA,GAAA5B,EAAA,CAAAqB,aAAA,GAAAQ,SAAA;MAAA,MAAAT,MAAA,GAAApB,EAAA,CAAAqB,aAAA;MAAA,MAAAS,yBAAA,GAAA9B,EAAA,CAAAuB,WAAA;MAAA,OAAAvB,EAAA,CAAAwB,WAAA,CAASJ,MAAA,CAAAW,eAAA,CAAAD,yBAAA,EAAAF,QAAA,CAA4C;IAAA,EAAC;IACtD5B,EAAA,CAAAE,MAAA,qBACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;IAnBXH,EAFF,CAAAC,cAAA,SAAmD,SAE7C;IAAAD,EAAA,CAAAE,MAAA,GAAoB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC7BH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,GAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACzBH,EAAA,CAAAC,cAAA,SAAI;IACFD,EAAA,CAAAE,MAAA,GAEF;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,GAA4E;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACrFH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,IAAuB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAChCH,EAAA,CAAAC,cAAA,UAAI;IACFD,EAAA,CAAAE,MAAA,IAGF;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAE,MAAA,IAA0E;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACnFH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAE,MAAA,IAAiC;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC1CH,EAAA,CAAAC,cAAA,cAAkC;IAChCD,EAAA,CAAAgC,UAAA,KAAAC,qDAAA,qBACyD;IAGzDjC,EAAA,CAAAC,cAAA,kBACmH;IAAjHD,EAAA,CAAAgB,UAAA,mBAAAkB,qEAAA;MAAA,MAAAN,QAAA,GAAA5B,EAAA,CAAAkB,aAAA,CAAAiB,IAAA,EAAAN,SAAA;MAAA,MAAAT,MAAA,GAAApB,EAAA,CAAAqB,aAAA;MAAA,OAAArB,EAAA,CAAAwB,WAAA,CAASJ,MAAA,CAAAgB,4BAAA,CAA6B,yBAAyB,EAAAhB,MAAA,CAAAiB,WAAA,CAAAC,kBAAA,CAAAC,GAAA,EAAAX,QAAA,CAAAY,GAAA,CAAgD;IAAA,EAAC;IAChHxC,EAAA,CAAAE,MAAA,kCACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,kBACmH;IAAjHD,EAAA,CAAAgB,UAAA,mBAAAyB,qEAAA;MAAA,MAAAb,QAAA,GAAA5B,EAAA,CAAAkB,aAAA,CAAAiB,IAAA,EAAAN,SAAA;MAAA,MAAAT,MAAA,GAAApB,EAAA,CAAAqB,aAAA;MAAA,OAAArB,EAAA,CAAAwB,WAAA,CAASJ,MAAA,CAAAgB,4BAAA,CAA6B,yBAAyB,EAAAhB,MAAA,CAAAiB,WAAA,CAAAC,kBAAA,CAAAC,GAAA,EAAAX,QAAA,CAAAY,GAAA,CAAgD;IAAA,EAAC;IAChHxC,EAAA,CAAAE,MAAA,8CACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,kBACoH;IAAlHD,EAAA,CAAAgB,UAAA,mBAAA0B,qEAAA;MAAA,MAAAd,QAAA,GAAA5B,EAAA,CAAAkB,aAAA,CAAAiB,IAAA,EAAAN,SAAA;MAAA,MAAAT,MAAA,GAAApB,EAAA,CAAAqB,aAAA;MAAA,OAAArB,EAAA,CAAAwB,WAAA,CAASJ,MAAA,CAAAgB,4BAAA,CAA6B,0BAA0B,EAAAhB,MAAA,CAAAiB,WAAA,CAAAC,kBAAA,CAAAC,GAAA,EAAAX,QAAA,CAAAY,GAAA,CAAgD;IAAA,EAAC;IACjHxC,EAAA,CAAAE,MAAA,8CACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,kBAAsF;IAA/BD,EAAA,CAAAgB,UAAA,mBAAA2B,qEAAA;MAAA,MAAAf,QAAA,GAAA5B,EAAA,CAAAkB,aAAA,CAAAiB,IAAA,EAAAN,SAAA;MAAA,MAAAT,MAAA,GAAApB,EAAA,CAAAqB,aAAA;MAAA,OAAArB,EAAA,CAAAwB,WAAA,CAASJ,MAAA,CAAAwB,cAAA,CAAAhB,QAAA,CAAoB;IAAA,EAAC;IACnF5B,EAAA,CAAAE,MAAA,kCACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,kBAAsG;IAA/CD,EAAA,CAAAgB,UAAA,mBAAA6B,qEAAA;MAAA,MAAAjB,QAAA,GAAA5B,EAAA,CAAAkB,aAAA,CAAAiB,IAAA,EAAAN,SAAA;MAAA,MAAAT,MAAA,GAAApB,EAAA,CAAAqB,aAAA;MAAA,MAAAyB,mBAAA,GAAA9C,EAAA,CAAAuB,WAAA;MAAA,OAAAvB,EAAA,CAAAwB,WAAA,CAASJ,MAAA,CAAA2B,aAAA,CAAAD,mBAAA,EAAAlB,QAAA,CAAoC;IAAA,EAAC;IACnG5B,EAAA,CAAAE,MAAA,4BACF;IAEJF,EAFI,CAAAG,YAAA,EAAS,EACN,EACF;;;;;IAvCCH,EAAA,CAAAM,SAAA,GAAoB;IAApBN,EAAA,CAAAgD,iBAAA,CAAApB,QAAA,CAAAqB,UAAA,CAAoB;IACpBjD,EAAA,CAAAM,SAAA,GAAgB;IAAhBN,EAAA,CAAAgD,iBAAA,CAAApB,QAAA,CAAAsB,MAAA,CAAgB;IAElBlD,EAAA,CAAAM,SAAA,GAEF;IAFEN,EAAA,CAAAmD,kBAAA,MAAAvB,QAAA,CAAAwB,UAAA,yCAAAxB,QAAA,CAAAwB,UAAA,wCAEF;IACIpD,EAAA,CAAAM,SAAA,GAA4E;IAA5EN,EAAA,CAAAgD,iBAAA,CAAApB,QAAA,CAAAyB,SAAA,6BAAAzB,QAAA,CAAAyB,SAAA,iCAA4E;IAC5ErD,EAAA,CAAAM,SAAA,GAAuB;IAAvBN,EAAA,CAAAgD,iBAAA,CAAApB,QAAA,CAAA0B,aAAA,CAAuB;IAEzBtD,EAAA,CAAAM,SAAA,GAGF;IAHEN,EAAA,CAAAuD,kBAAA,MAAA3B,QAAA,CAAA4B,UAAA,yCAAA5B,QAAA,CAAA4B,UAAA,yCAAA5B,QAAA,CAAA4B,UAAA,8CAGF;IACIxD,EAAA,CAAAM,SAAA,GAA0E;IAA1EN,EAAA,CAAAgD,iBAAA,CAAApB,QAAA,CAAA6B,WAAA,UAAA7B,QAAA,CAAA6B,WAAA,uDAA0E;IAC1EzD,EAAA,CAAAM,SAAA,GAAiC;IAAjCN,EAAA,CAAAgD,iBAAA,CAAApB,QAAA,CAAA8B,SAAA,mCAAiC;IAE1B1D,EAAA,CAAAM,SAAA,GAAc;IAAdN,EAAA,CAAAI,UAAA,SAAAgB,MAAA,CAAAuC,QAAA,CAAc;;;;;IA6C3B3D,EAAA,CAAAC,cAAA,oBAAwE;IACtED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAF2CH,EAAA,CAAAI,UAAA,UAAAwD,UAAA,CAAgB;IACrE5D,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAAO,kBAAA,MAAAqD,UAAA,CAAApD,cAAA,MACF;;;;;IAoDAR,EAAA,CAAAC,cAAA,oBAA4E;IAC1ED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAF+CH,EAAA,CAAAI,UAAA,UAAAyD,UAAA,CAAgB;IACzE7D,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAAO,kBAAA,MAAAsD,UAAA,CAAAnD,KAAA,MACF;;;;;IASAV,EAAA,CAAAC,cAAA,oBAA4E;IAC1ED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAF+CH,EAAA,CAAAI,UAAA,UAAA0D,UAAA,CAAgB;IACzE9D,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAAO,kBAAA,MAAAuD,UAAA,CAAApD,KAAA,MACF;;;;;;IANFV,EADF,CAAAC,cAAA,cAAkD,gBACqC;IACnFD,EAAA,CAAAE,MAAA,iCACF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,oBAA6F;IAA/DD,EAAA,CAAA+D,gBAAA,2BAAAC,+GAAAC,MAAA;MAAAjE,EAAA,CAAAkB,aAAA,CAAAgD,IAAA;MAAA,MAAA9C,MAAA,GAAApB,EAAA,CAAAqB,aAAA;MAAArB,EAAA,CAAAmE,kBAAA,CAAA/C,MAAA,CAAAgD,cAAA,CAAAC,kBAAA,EAAAJ,MAAA,MAAA7C,MAAA,CAAAgD,cAAA,CAAAC,kBAAA,GAAAJ,MAAA;MAAA,OAAAjE,EAAA,CAAAwB,WAAA,CAAAyC,MAAA;IAAA,EAA+C;IAC3EjE,EAAA,CAAAgC,UAAA,IAAAsC,uFAAA,wBAA4E;IAIhFtE,EADE,CAAAG,YAAA,EAAY,EACR;;;;IAL0BH,EAAA,CAAAM,SAAA,GAA+C;IAA/CN,EAAA,CAAAuE,gBAAA,YAAAnD,MAAA,CAAAgD,cAAA,CAAAC,kBAAA,CAA+C;IAC7CrE,EAAA,CAAAM,SAAA,EAA2B;IAA3BN,EAAA,CAAAI,UAAA,YAAAgB,MAAA,CAAAoD,OAAA,CAAAC,gBAAA,CAA2B;;;;;IAUzDzE,EAAA,CAAAC,cAAA,oBAA2E;IACzED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAF8CH,EAAA,CAAAI,UAAA,UAAAsE,UAAA,CAAgB;IACxE1E,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAAO,kBAAA,MAAAmE,UAAA,CAAAhE,KAAA,MACF;;;;;;IANFV,EADF,CAAAC,cAAA,cAAiD,gBACqC;IAClFD,EAAA,CAAAE,MAAA,qBACF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,oBAA0F;IAA9DD,EAAA,CAAA+D,gBAAA,2BAAAY,+GAAAV,MAAA;MAAAjE,EAAA,CAAAkB,aAAA,CAAA0D,IAAA;MAAA,MAAAxD,MAAA,GAAApB,EAAA,CAAAqB,aAAA;MAAArB,EAAA,CAAAmE,kBAAA,CAAA/C,MAAA,CAAAgD,cAAA,CAAAS,iBAAA,EAAAZ,MAAA,MAAA7C,MAAA,CAAAgD,cAAA,CAAAS,iBAAA,GAAAZ,MAAA;MAAA,OAAAjE,EAAA,CAAAwB,WAAA,CAAAyC,MAAA;IAAA,EAA8C;IACxEjE,EAAA,CAAAgC,UAAA,IAAA8C,uFAAA,wBAA2E;IAI/E9E,EADE,CAAAG,YAAA,EAAY,EACR;;;;IALwBH,EAAA,CAAAM,SAAA,GAA8C;IAA9CN,EAAA,CAAAuE,gBAAA,YAAAnD,MAAA,CAAAgD,cAAA,CAAAS,iBAAA,CAA8C;IAC1C7E,EAAA,CAAAM,SAAA,EAA0B;IAA1BN,EAAA,CAAAI,UAAA,YAAAgB,MAAA,CAAAoD,OAAA,CAAAO,eAAA,CAA0B;;;;;;IA/E1D/E,EAFJ,CAAAC,cAAA,uBAA+C,cACrB,gBACiE;IACrFD,EAAA,CAAAE,MAAA,iCACF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,oBAA6G;IAA/ED,EAAA,CAAA+D,gBAAA,2BAAAiB,wGAAAf,MAAA;MAAAjE,EAAA,CAAAkB,aAAA,CAAA+D,IAAA;MAAA,MAAA7D,MAAA,GAAApB,EAAA,CAAAqB,aAAA;MAAArB,EAAA,CAAAmE,kBAAA,CAAA/C,MAAA,CAAAgD,cAAA,CAAA9B,kBAAA,EAAA2B,MAAA,MAAA7C,MAAA,CAAAgD,cAAA,CAAA9B,kBAAA,GAAA2B,MAAA;MAAA,OAAAjE,EAAA,CAAAwB,WAAA,CAAAyC,MAAA;IAAA,EAA+C;IAC3EjE,EAAA,CAAAgC,UAAA,IAAAkD,gFAAA,wBAAwE;IAI5ElF,EADE,CAAAG,YAAA,EAAY,EACR;IAGJH,EADF,CAAAC,cAAA,cAAwB,gBAC+D;IACnFD,EAAA,CAAAE,MAAA,iCACF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,gBAAoG;IAAvCD,EAAA,CAAA+D,gBAAA,2BAAAoB,oGAAAlB,MAAA;MAAAjE,EAAA,CAAAkB,aAAA,CAAA+D,IAAA;MAAA,MAAA7D,MAAA,GAAApB,EAAA,CAAAqB,aAAA;MAAArB,EAAA,CAAAmE,kBAAA,CAAA/C,MAAA,CAAAgE,WAAA,CAAAC,UAAA,EAAApB,MAAA,MAAA7C,MAAA,CAAAgE,WAAA,CAAAC,UAAA,GAAApB,MAAA;MAAA,OAAAjE,EAAA,CAAAwB,WAAA,CAAAyC,MAAA;IAAA,EAAoC;IACnGjE,EADE,CAAAG,YAAA,EAAoG,EAChG;IAGJH,EADF,CAAAC,cAAA,eAAwB,iBAC2D;IAC/ED,EAAA,CAAAE,MAAA,sBACF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,iBACoB;IADyCD,EAAA,CAAA+D,gBAAA,2BAAAuB,qGAAArB,MAAA;MAAAjE,EAAA,CAAAkB,aAAA,CAAA+D,IAAA;MAAA,MAAA7D,MAAA,GAAApB,EAAA,CAAAqB,aAAA;MAAArB,EAAA,CAAAmE,kBAAA,CAAA/C,MAAA,CAAAgE,WAAA,CAAAlC,MAAA,EAAAe,MAAA,MAAA7C,MAAA,CAAAgE,WAAA,CAAAlC,MAAA,GAAAe,MAAA;MAAA,OAAAjE,EAAA,CAAAwB,WAAA,CAAAyC,MAAA;IAAA,EAAgC;IAE/FjE,EAFE,CAAAG,YAAA,EACoB,EAChB;IAGJH,EADF,CAAAC,cAAA,eAAwB,iBACmD;IACvED,EAAA,CAAAE,MAAA,kCACF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,iBAAuG;IAA1CD,EAAA,CAAA+D,gBAAA,2BAAAwB,qGAAAtB,MAAA;MAAAjE,EAAA,CAAAkB,aAAA,CAAA+D,IAAA;MAAA,MAAA7D,MAAA,GAAApB,EAAA,CAAAqB,aAAA;MAAArB,EAAA,CAAAmE,kBAAA,CAAA/C,MAAA,CAAAgE,WAAA,CAAAI,aAAA,EAAAvB,MAAA,MAAA7C,MAAA,CAAAgE,WAAA,CAAAI,aAAA,GAAAvB,MAAA;MAAA,OAAAjE,EAAA,CAAAwB,WAAA,CAAAyC,MAAA;IAAA,EAAuC;IACtGjE,EADE,CAAAG,YAAA,EAAuG,EACnG;IAGJH,EADF,CAAAC,cAAA,eAAwB,iBACiD;IACrED,EAAA,CAAAE,MAAA,wCACF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,iBACmB;IAD2CD,EAAA,CAAA+D,gBAAA,2BAAA0B,qGAAAxB,MAAA;MAAAjE,EAAA,CAAAkB,aAAA,CAAA+D,IAAA;MAAA,MAAA7D,MAAA,GAAApB,EAAA,CAAAqB,aAAA;MAAArB,EAAA,CAAAmE,kBAAA,CAAA/C,MAAA,CAAAgE,WAAA,CAAAM,WAAA,EAAAzB,MAAA,MAAA7C,MAAA,CAAAgE,WAAA,CAAAM,WAAA,GAAAzB,MAAA;MAAA,OAAAjE,EAAA,CAAAwB,WAAA,CAAAyC,MAAA;IAAA,EAAqC;IAErGjE,EAFE,CAAAG,YAAA,EACmB,EACf;IAGJH,EADF,CAAAC,cAAA,eAAwB,iBAC2C;IAC/DD,EAAA,CAAAE,MAAA,kCACF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,iBAA+F;IAAlCD,EAAA,CAAA+D,gBAAA,2BAAA4B,qGAAA1B,MAAA;MAAAjE,EAAA,CAAAkB,aAAA,CAAA+D,IAAA;MAAA,MAAA7D,MAAA,GAAApB,EAAA,CAAAqB,aAAA;MAAArB,EAAA,CAAAmE,kBAAA,CAAA/C,MAAA,CAAAgE,WAAA,CAAAQ,KAAA,EAAA3B,MAAA,MAAA7C,MAAA,CAAAgE,WAAA,CAAAQ,KAAA,GAAA3B,MAAA;MAAA,OAAAjE,EAAA,CAAAwB,WAAA,CAAAyC,MAAA;IAAA,EAA+B;IAC9FjE,EADE,CAAAG,YAAA,EAA+F,EAC3F;IAEJH,EADF,CAAAC,cAAA,eAAwB,iBAC4C;IAChED,EAAA,CAAAE,MAAA,kCACF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,iBAAgG;IAAnCD,EAAA,CAAA+D,gBAAA,2BAAA8B,qGAAA5B,MAAA;MAAAjE,EAAA,CAAAkB,aAAA,CAAA+D,IAAA;MAAA,MAAA7D,MAAA,GAAApB,EAAA,CAAAqB,aAAA;MAAArB,EAAA,CAAAmE,kBAAA,CAAA/C,MAAA,CAAAgE,WAAA,CAAAU,MAAA,EAAA7B,MAAA,MAAA7C,MAAA,CAAAgE,WAAA,CAAAU,MAAA,GAAA7B,MAAA;MAAA,OAAAjE,EAAA,CAAAwB,WAAA,CAAAyC,MAAA;IAAA,EAAgC;IAC/FjE,EADE,CAAAG,YAAA,EAAgG,EAC5F;IAGJH,EADF,CAAAC,cAAA,eAAwB,iBAC+D;IACnFD,EAAA,CAAAE,MAAA,kCACF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,qBAA6F;IAA/DD,EAAA,CAAA+D,gBAAA,2BAAAgC,yGAAA9B,MAAA;MAAAjE,EAAA,CAAAkB,aAAA,CAAA+D,IAAA;MAAA,MAAA7D,MAAA,GAAApB,EAAA,CAAAqB,aAAA;MAAArB,EAAA,CAAAmE,kBAAA,CAAA/C,MAAA,CAAAgD,cAAA,CAAA4B,kBAAA,EAAA/B,MAAA,MAAA7C,MAAA,CAAAgD,cAAA,CAAA4B,kBAAA,GAAA/B,MAAA;MAAA,OAAAjE,EAAA,CAAAwB,WAAA,CAAAyC,MAAA;IAAA,EAA+C;IAC3EjE,EAAA,CAAAgC,UAAA,KAAAiE,iFAAA,wBAA4E;IAIhFjG,EADE,CAAAG,YAAA,EAAY,EACR;IAYNH,EAVA,CAAAgC,UAAA,KAAAkE,2EAAA,kBAAkD,KAAAC,2EAAA,kBAUD;IAY/CnG,EADF,CAAAC,cAAA,eAAwB,iBAC+C;IACnED,EAAA,CAAAE,MAAA,kCACF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,uBAAgE;IAApCD,EAAA,CAAA+D,gBAAA,2BAAAqC,2GAAAnC,MAAA;MAAAjE,EAAA,CAAAkB,aAAA,CAAA+D,IAAA;MAAA,MAAA7D,MAAA,GAAApB,EAAA,CAAAqB,aAAA;MAAArB,EAAA,CAAAmE,kBAAA,CAAA/C,MAAA,CAAAgE,WAAA,CAAA/B,SAAA,EAAAY,MAAA,MAAA7C,MAAA,CAAAgE,WAAA,CAAA/B,SAAA,GAAAY,MAAA;MAAA,OAAAjE,EAAA,CAAAwB,WAAA,CAAAyC,MAAA;IAAA,EAAmC;IAACjE,EAAA,CAAAE,MAAA,eAChE;IACFF,EADE,CAAAG,YAAA,EAAc,EACV;IAGJH,EADF,CAAAC,cAAA,eAAwB,iBAC+C;IACnED,EAAA,CAAAE,MAAA,kCACF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,uBAAgE;IAApCD,EAAA,CAAA+D,gBAAA,2BAAAsC,2GAAApC,MAAA;MAAAjE,EAAA,CAAAkB,aAAA,CAAA+D,IAAA;MAAA,MAAA7D,MAAA,GAAApB,EAAA,CAAAqB,aAAA;MAAArB,EAAA,CAAAmE,kBAAA,CAAA/C,MAAA,CAAAgE,WAAA,CAAA1B,SAAA,EAAAO,MAAA,MAAA7C,MAAA,CAAAgE,WAAA,CAAA1B,SAAA,GAAAO,MAAA;MAAA,OAAAjE,EAAA,CAAAwB,WAAA,CAAAyC,MAAA;IAAA,EAAmC;IAACjE,EAAA,CAAAE,MAAA,eAChE;IACFF,EADE,CAAAG,YAAA,EAAc,EACV;IAGJH,EADF,CAAAC,cAAA,eAAsC,iBACgD;IAClFD,EAAA,CAAAE,MAAA,kCACF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAENH,EADF,CAAAC,cAAA,eAAoC,yBACL;IAC3BD,EAAA,CAAAsG,SAAA,mBAAoD;IACpDtG,EAAA,CAAAC,cAAA,iBACiE;IAA1CD,EAAA,CAAA+D,gBAAA,2BAAAwC,qGAAAtC,MAAA;MAAAjE,EAAA,CAAAkB,aAAA,CAAA+D,IAAA;MAAA,MAAA7D,MAAA,GAAApB,EAAA,CAAAqB,aAAA;MAAArB,EAAA,CAAAmE,kBAAA,CAAA/C,MAAA,CAAAgE,WAAA,CAAAoB,eAAA,EAAAvC,MAAA,MAAA7C,MAAA,CAAAgE,WAAA,CAAAoB,eAAA,GAAAvC,MAAA;MAAA,OAAAjE,EAAA,CAAAwB,WAAA,CAAAyC,MAAA;IAAA,EAAyC;IADhEjE,EAAA,CAAAG,YAAA,EACiE;IACjEH,EAAA,CAAAsG,SAAA,4BAA8D;IAChEtG,EAAA,CAAAG,YAAA,EAAgB;IAChBH,EAAA,CAAAC,cAAA,yBAA6B;IAC3BD,EAAA,CAAAsG,SAAA,mBAAoD;IACpDtG,EAAA,CAAAC,cAAA,iBAC+D;IAAxCD,EAAA,CAAA+D,gBAAA,2BAAA0C,qGAAAxC,MAAA;MAAAjE,EAAA,CAAAkB,aAAA,CAAA+D,IAAA;MAAA,MAAA7D,MAAA,GAAApB,EAAA,CAAAqB,aAAA;MAAArB,EAAA,CAAAmE,kBAAA,CAAA/C,MAAA,CAAAgE,WAAA,CAAAsB,aAAA,EAAAzC,MAAA,MAAA7C,MAAA,CAAAgE,WAAA,CAAAsB,aAAA,GAAAzC,MAAA;MAAA,OAAAjE,EAAA,CAAAwB,WAAA,CAAAyC,MAAA;IAAA,EAAuC;IAD9DjE,EAAA,CAAAG,YAAA,EAC+D;IAC/DH,EAAA,CAAAsG,SAAA,4BAA4D;IAIpEtG,EAHM,CAAAG,YAAA,EAAgB,EACZ,EACF,EACO;;;;;;IArHmBH,EAAA,CAAAM,SAAA,GAA+C;IAA/CN,EAAA,CAAAuE,gBAAA,YAAAnD,MAAA,CAAAgD,cAAA,CAAA9B,kBAAA,CAA+C;IAC7CtC,EAAA,CAAAM,SAAA,EAAuB;IAAvBN,EAAA,CAAAI,UAAA,YAAAgB,MAAA,CAAAuF,oBAAA,CAAuB;IAUM3G,EAAA,CAAAM,SAAA,GAAoC;IAApCN,EAAA,CAAAuE,gBAAA,YAAAnD,MAAA,CAAAgE,WAAA,CAAAC,UAAA,CAAoC;IAOpCrF,EAAA,CAAAM,SAAA,GAAgC;IAAhCN,EAAA,CAAAuE,gBAAA,YAAAnD,MAAA,CAAAgE,WAAA,CAAAlC,MAAA,CAAgC;IAQhClD,EAAA,CAAAM,SAAA,GAAuC;IAAvCN,EAAA,CAAAuE,gBAAA,YAAAnD,MAAA,CAAAgE,WAAA,CAAAI,aAAA,CAAuC;IAOtCxF,EAAA,CAAAM,SAAA,GAAqC;IAArCN,EAAA,CAAAuE,gBAAA,YAAAnD,MAAA,CAAAgE,WAAA,CAAAM,WAAA,CAAqC;IAQtC1F,EAAA,CAAAM,SAAA,GAA+B;IAA/BN,EAAA,CAAAuE,gBAAA,YAAAnD,MAAA,CAAAgE,WAAA,CAAAQ,KAAA,CAA+B;IAM/B5F,EAAA,CAAAM,SAAA,GAAgC;IAAhCN,EAAA,CAAAuE,gBAAA,YAAAnD,MAAA,CAAAgE,WAAA,CAAAU,MAAA,CAAgC;IAO/D9F,EAAA,CAAAM,SAAA,GAA+C;IAA/CN,EAAA,CAAAuE,gBAAA,YAAAnD,MAAA,CAAAgD,cAAA,CAAA4B,kBAAA,CAA+C;IAC7ChG,EAAA,CAAAM,SAAA,EAA2B;IAA3BN,EAAA,CAAAI,UAAA,YAAAgB,MAAA,CAAAoD,OAAA,CAAAoC,gBAAA,CAA2B;IAMpC5G,EAAA,CAAAM,SAAA,EAAuB;IAAvBN,EAAA,CAAAI,UAAA,SAAAgB,MAAA,CAAAyF,iBAAA,CAAuB;IAUvB7G,EAAA,CAAAM,SAAA,EAAsB;IAAtBN,EAAA,CAAAI,UAAA,SAAAgB,MAAA,CAAA0F,gBAAA,CAAsB;IAejB9G,EAAA,CAAAM,SAAA,GAAmC;IAAnCN,EAAA,CAAAuE,gBAAA,YAAAnD,MAAA,CAAAgE,WAAA,CAAA/B,SAAA,CAAmC;IAQnCrD,EAAA,CAAAM,SAAA,GAAmC;IAAnCN,EAAA,CAAAuE,gBAAA,YAAAnD,MAAA,CAAAgE,WAAA,CAAA1B,SAAA,CAAmC;IAWQ1D,EAAA,CAAAM,SAAA,GAA0B;IAA1BN,EAAA,CAAAI,UAAA,iBAAA2G,aAAA,CAA0B;IACtE/G,EAAA,CAAAuE,gBAAA,YAAAnD,MAAA,CAAAgE,WAAA,CAAAoB,eAAA,CAAyC;IAKCxG,EAAA,CAAAM,SAAA,GAAwB;IAAxBN,EAAA,CAAAI,UAAA,iBAAA4G,WAAA,CAAwB;IAClEhH,EAAA,CAAAuE,gBAAA,YAAAnD,MAAA,CAAAgE,WAAA,CAAAsB,aAAA,CAAuC;;;;;;IAQpE1G,EAAA,CAAAC,cAAA,iBAAqG;IAA9BD,EAAA,CAAAgB,UAAA,mBAAAiG,uFAAA;MAAAjH,EAAA,CAAAkB,aAAA,CAAAgG,IAAA;MAAA,MAAAC,OAAA,GAAAnH,EAAA,CAAAqB,aAAA,GAAA+F,SAAA;MAAA,MAAAhG,MAAA,GAAApB,EAAA,CAAAqB,aAAA;MAAA,OAAArB,EAAA,CAAAwB,WAAA,CAASJ,MAAA,CAAAiG,cAAA,CAAAF,OAAA,CAAmB;IAAA,EAAC;IAACnH,EAAA,CAAAE,MAAA,mBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;IAhIpHH,EAAA,CAAAC,cAAA,kBAA+C;IAG7CD,EAAA,CAAAgC,UAAA,IAAAsF,oEAAA,6BAA+C;IA4H7CtH,EADF,CAAAC,cAAA,yBAAsD,iBACsB;IAAvBD,EAAA,CAAAgB,UAAA,mBAAAuG,8EAAA;MAAA,MAAAJ,OAAA,GAAAnH,EAAA,CAAAkB,aAAA,CAAAsG,IAAA,EAAAJ,SAAA;MAAA,MAAAhG,MAAA,GAAApB,EAAA,CAAAqB,aAAA;MAAA,OAAArB,EAAA,CAAAwB,WAAA,CAASJ,MAAA,CAAAqG,OAAA,CAAAN,OAAA,CAAY;IAAA,EAAC;IAACnH,EAAA,CAAAE,MAAA,mBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACrFH,EAAA,CAAAgC,UAAA,IAAA0F,8DAAA,qBAAqG;IAEzG1H,EADE,CAAAG,YAAA,EAAiB,EACT;;;;IA/HoBH,EAAA,CAAAM,SAAA,EAAiB;IAAjBN,EAAA,CAAAI,UAAA,SAAAgB,MAAA,CAAAgE,WAAA,CAAiB;IA6HYpF,EAAA,CAAAM,SAAA,GAAc;IAAdN,EAAA,CAAAI,UAAA,SAAAgB,MAAA,CAAAuG,QAAA,CAAc;;;;;;IA8BrE3H,EAAA,CAAAC,cAAA,kBAAiF;IAAhCD,EAAA,CAAAgB,UAAA,mBAAA4G,wFAAA;MAAA5H,EAAA,CAAAkB,aAAA,CAAA2G,IAAA;MAAA,MAAAC,OAAA,GAAA9H,EAAA,CAAAqB,aAAA,GAAA+F,SAAA;MAAA,MAAAhG,MAAA,GAAApB,EAAA,CAAAqB,aAAA;MAAA,OAAArB,EAAA,CAAAwB,WAAA,CAASJ,MAAA,CAAA2G,gBAAA,CAAAD,OAAA,CAAqB;IAAA,EAAC;IAAC9H,EAAA,CAAAE,MAAA,mBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;IAvB9FH,EADF,CAAAC,cAAA,kBAA+C,qBAC7B;IACdD,EAAA,CAAAE,MAAA,wFACF;IAAAF,EAAA,CAAAG,YAAA,EAAiB;IAGbH,EAFJ,CAAAC,cAAA,uBAA2B,cACD,gBACkE;IACtFD,EAAA,CAAAE,MAAA,qBACF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,gBAAuG;IAA5CD,EAAA,CAAA+D,gBAAA,2BAAAiE,qFAAA/D,MAAA;MAAAjE,EAAA,CAAAkB,aAAA,CAAA+G,IAAA;MAAA,MAAA7G,MAAA,GAAApB,EAAA,CAAAqB,aAAA;MAAArB,EAAA,CAAAmE,kBAAA,CAAA/C,MAAA,CAAA8G,aAAA,CAAAC,aAAA,EAAAlE,MAAA,MAAA7C,MAAA,CAAA8G,aAAA,CAAAC,aAAA,GAAAlE,MAAA;MAAA,OAAAjE,EAAA,CAAAwB,WAAA,CAAAyC,MAAA;IAAA,EAAyC;IACtGjE,EADE,CAAAG,YAAA,EAAuG,EACnG;IAEJH,EADF,CAAAC,cAAA,cAAwB,gBACoE;IAAAD,EAAA,CAAAE,MAAA,6CAC1F;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,iBAA+G;IAA9CD,EAAA,CAAA+D,gBAAA,2BAAAqE,sFAAAnE,MAAA;MAAAjE,EAAA,CAAAkB,aAAA,CAAA+G,IAAA;MAAA,MAAA7G,MAAA,GAAApB,EAAA,CAAAqB,aAAA;MAAArB,EAAA,CAAAmE,kBAAA,CAAA/C,MAAA,CAAA8G,aAAA,CAAAG,eAAA,EAAApE,MAAA,MAAA7C,MAAA,CAAA8G,aAAA,CAAAG,eAAA,GAAApE,MAAA;MAAA,OAAAjE,EAAA,CAAAwB,WAAA,CAAAyC,MAAA;IAAA,EAA2C;IAC9GjE,EADE,CAAAG,YAAA,EAA+G,EAC3G;IAEJH,EADF,CAAAC,cAAA,eAAwB,iBAC2D;IAAAD,EAAA,CAAAE,MAAA,uCACjF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,iBAAqG;IAArCD,EAAA,CAAA+D,gBAAA,2BAAAuE,sFAAArE,MAAA;MAAAjE,EAAA,CAAAkB,aAAA,CAAA+G,IAAA;MAAA,MAAA7G,MAAA,GAAApB,EAAA,CAAAqB,aAAA;MAAArB,EAAA,CAAAmE,kBAAA,CAAA/C,MAAA,CAAA8G,aAAA,CAAAhF,MAAA,EAAAe,MAAA,MAAA7C,MAAA,CAAA8G,aAAA,CAAAhF,MAAA,GAAAe,MAAA;MAAA,OAAAjE,EAAA,CAAAwB,WAAA,CAAAyC,MAAA;IAAA,EAAkC;IAEtGjE,EAFI,CAAAG,YAAA,EAAqG,EACjG,EACO;IAEbH,EADF,CAAAC,cAAA,0BAAsD,kBACQ;IAAvBD,EAAA,CAAAgB,UAAA,mBAAAuH,+EAAA;MAAA,MAAAT,OAAA,GAAA9H,EAAA,CAAAkB,aAAA,CAAA+G,IAAA,EAAAb,SAAA;MAAA,MAAAhG,MAAA,GAAApB,EAAA,CAAAqB,aAAA;MAAA,OAAArB,EAAA,CAAAwB,WAAA,CAASJ,MAAA,CAAAqG,OAAA,CAAAK,OAAA,CAAY;IAAA,EAAC;IAAC9H,EAAA,CAAAE,MAAA,IAAS;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAC9EH,EAAA,CAAAgC,UAAA,KAAAwG,+DAAA,sBAAiF;IAErFxI,EADE,CAAAG,YAAA,EAAiB,EACT;;;;IAjBuDH,EAAA,CAAAM,SAAA,GAAyC;IAAzCN,EAAA,CAAAuE,gBAAA,YAAAnD,MAAA,CAAA8G,aAAA,CAAAC,aAAA,CAAyC;IAKnCnI,EAAA,CAAAM,SAAA,GAA2C;IAA3CN,EAAA,CAAAuE,gBAAA,YAAAnD,MAAA,CAAA8G,aAAA,CAAAG,eAAA,CAA2C;IAK5CrI,EAAA,CAAAM,SAAA,GAAkC;IAAlCN,EAAA,CAAAuE,gBAAA,YAAAnD,MAAA,CAAA8G,aAAA,CAAAhF,MAAA,CAAkC;IAIxClD,EAAA,CAAAM,SAAA,GAAS;IAATN,EAAA,CAAAgD,iBAAA,gBAAS;IACpChD,EAAA,CAAAM,SAAA,EAAc;IAAdN,EAAA,CAAAI,UAAA,SAAAgB,MAAA,CAAAuG,QAAA,CAAc;;;;;;IAoCrC3H,EAFJ,CAAAC,cAAA,SAAuD,SACjD,iBAEmC;IADVD,EAAA,CAAA+D,gBAAA,2BAAA0E,2FAAAxE,MAAA;MAAA,MAAAyE,QAAA,GAAA1I,EAAA,CAAAkB,aAAA,CAAAyH,IAAA,EAAA9G,SAAA;MAAA7B,EAAA,CAAAmE,kBAAA,CAAAuE,QAAA,CAAAE,SAAA,EAAA3E,MAAA,MAAAyE,QAAA,CAAAE,SAAA,GAAA3E,MAAA;MAAA,OAAAjE,EAAA,CAAAwB,WAAA,CAAAyC,MAAA;IAAA,EAA4B;IAEzDjE,EAFE,CAAAG,YAAA,EACqC,EAClC;IAEHH,EADF,CAAAC,cAAA,SAAI,iBAEmC;IADRD,EAAA,CAAA+D,gBAAA,2BAAA8E,2FAAA5E,MAAA;MAAA,MAAAyE,QAAA,GAAA1I,EAAA,CAAAkB,aAAA,CAAAyH,IAAA,EAAA9G,SAAA;MAAA7B,EAAA,CAAAmE,kBAAA,CAAAuE,QAAA,CAAAI,UAAA,EAAA7E,MAAA,MAAAyE,QAAA,CAAAI,UAAA,GAAA7E,MAAA;MAAA,OAAAjE,EAAA,CAAAwB,WAAA,CAAAyC,MAAA;IAAA,EAA6B;IAACjE,EAAA,CAAAgB,UAAA,2BAAA6H,2FAAA;MAAA7I,EAAA,CAAAkB,aAAA,CAAAyH,IAAA;MAAA,MAAAvH,MAAA,GAAApB,EAAA,CAAAqB,aAAA;MAAA,OAAArB,EAAA,CAAAwB,WAAA,CAAiBJ,MAAA,CAAA2H,cAAA,EAAgB;IAAA,EAAC;IAE/F/I,EAFE,CAAAG,YAAA,EACqC,EAClC;IAEHH,EADF,CAAAC,cAAA,SAAI,iBAEiB;IADUD,EAAA,CAAA+D,gBAAA,2BAAAiF,2FAAA/E,MAAA;MAAA,MAAAyE,QAAA,GAAA1I,EAAA,CAAAkB,aAAA,CAAAyH,IAAA,EAAA9G,SAAA;MAAA7B,EAAA,CAAAmE,kBAAA,CAAAuE,QAAA,CAAAO,MAAA,EAAAhF,MAAA,MAAAyE,QAAA,CAAAO,MAAA,GAAAhF,MAAA;MAAA,OAAAjE,EAAA,CAAAwB,WAAA,CAAAyC,MAAA;IAAA,EAAyB;IAACjE,EAAA,CAAAgB,UAAA,2BAAAgI,2FAAA;MAAAhJ,EAAA,CAAAkB,aAAA,CAAAyH,IAAA;MAAA,MAAAvH,MAAA,GAAApB,EAAA,CAAAqB,aAAA;MAAA,OAAArB,EAAA,CAAAwB,WAAA,CAAiBJ,MAAA,CAAA2H,cAAA,EAAgB;IAAA,EAAC;IAE3F/I,EAFE,CAAAG,YAAA,EACmB,EAChB;IACLH,EAAA,CAAAC,cAAA,cAAuB;IACrBD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAEHH,EADF,CAAAC,cAAA,SAAI,iBACqG;IACrGD,EAAA,CAAAE,MAAA,IACF;IACFF,EADE,CAAAG,YAAA,EAAO,EACJ;IAEHH,EADF,CAAAC,cAAA,UAAI,mBACqE;IAAjCD,EAAA,CAAAgB,UAAA,mBAAAkI,qFAAA;MAAA,MAAAC,KAAA,GAAAnJ,EAAA,CAAAkB,aAAA,CAAAyH,IAAA,EAAAS,KAAA;MAAA,MAAAhI,MAAA,GAAApB,EAAA,CAAAqB,aAAA;MAAA,OAAArB,EAAA,CAAAwB,WAAA,CAASJ,MAAA,CAAAiI,mBAAA,CAAAF,KAAA,CAAsB;IAAA,EAAC;IACpEnJ,EAAA,CAAAE,MAAA,sBACF;IAEJF,EAFI,CAAAG,YAAA,EAAS,EACN,EACF;;;;;IAvBCH,EAAA,CAAAM,SAAA,GAAkC;IAAlCN,EAAA,CAAAsJ,WAAA,aAAAZ,QAAA,CAAAa,UAAA,CAAkC;IADTvJ,EAAA,CAAAuE,gBAAA,YAAAmE,QAAA,CAAAE,SAAA,CAA4B;IAAC5I,EAAA,CAAAI,UAAA,aAAAsI,QAAA,CAAAa,UAAA,CAA4B;IAIvDvJ,EAAA,CAAAM,SAAA,GAA6B;IAA7BN,EAAA,CAAAuE,gBAAA,YAAAmE,QAAA,CAAAI,UAAA,CAA6B;IAI7B9I,EAAA,CAAAM,SAAA,GAAyB;IAAzBN,EAAA,CAAAuE,gBAAA,YAAAmE,QAAA,CAAAO,MAAA,CAAyB;IAItDjJ,EAAA,CAAAM,SAAA,GACF;IADEN,EAAA,CAAAO,kBAAA,MAAAa,MAAA,CAAAoI,cAAA,CAAAd,QAAA,CAAAI,UAAA,GAAAJ,QAAA,CAAAO,MAAA,OACF;IAEsBjJ,EAAA,CAAAM,SAAA,GAAuC;IAACN,EAAxC,CAAAsJ,WAAA,kBAAAZ,QAAA,CAAAa,UAAA,CAAuC,qBAAAb,QAAA,CAAAa,UAAA,CAA2C;IACpGvJ,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAAO,kBAAA,MAAAmI,QAAA,CAAAa,UAAA,8CACF;;;;;IASFvJ,EADF,CAAAC,cAAA,SAAwC,cACc;IAClDD,EAAA,CAAAE,MAAA,iLACF;IACFF,EADE,CAAAG,YAAA,EAAK,EACF;;;;;;IAzDbH,EADF,CAAAC,cAAA,mBAA+C,qBAC7B;IACdD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAiB;IAGbH,EAFJ,CAAAC,cAAA,mBAAc,eACqC,iBACkB;IAA7BD,EAAA,CAAAgB,UAAA,mBAAAyI,8EAAA;MAAAzJ,EAAA,CAAAkB,aAAA,CAAAwI,IAAA;MAAA,MAAAtI,MAAA,GAAApB,EAAA,CAAAqB,aAAA;MAAA,OAAArB,EAAA,CAAAwB,WAAA,CAASJ,MAAA,CAAAuI,gBAAA,EAAkB;IAAA,EAAC;IAC9D3J,EAAA,CAAAE,MAAA,qDACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,iBAAsE;IAA7BD,EAAA,CAAAgB,UAAA,mBAAA4I,8EAAA;MAAA5J,EAAA,CAAAkB,aAAA,CAAAwI,IAAA;MAAA,MAAAtI,MAAA,GAAApB,EAAA,CAAAqB,aAAA;MAAA,OAAArB,EAAA,CAAAwB,WAAA,CAASJ,MAAA,CAAAyI,gBAAA,EAAkB;IAAA,EAAC;IACnE7J,EAAA,CAAAE,MAAA,6CACF;IACFF,EADE,CAAAG,YAAA,EAAS,EACL;IAMEH,EAJR,CAAAC,cAAA,eAA8B,kBACQ,aAC3B,UACD,eACc;IAAAD,EAAA,CAAAE,MAAA,gCAAI;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACzBH,EAAA,CAAAC,cAAA,eAAgB;IAAAD,EAAA,CAAAE,MAAA,6BAAM;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC3BH,EAAA,CAAAC,cAAA,eAAgB;IAAAD,EAAA,CAAAE,MAAA,oBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACvBH,EAAA,CAAAC,cAAA,eAAgB;IAAAD,EAAA,CAAAE,MAAA,6BAAM;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC3BH,EAAA,CAAAC,cAAA,eAAgB;IAAAD,EAAA,CAAAE,MAAA,oBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACvBH,EAAA,CAAAC,cAAA,eAAgB;IAAAD,EAAA,CAAAE,MAAA,oBAAE;IAEtBF,EAFsB,CAAAG,YAAA,EAAK,EACpB,EACC;IACRH,EAAA,CAAAC,cAAA,aAAO;IA4BLD,EA3BA,CAAAgC,UAAA,KAAA8H,2DAAA,mBAAuD,KAAAC,2DAAA,kBA2Bf;IAO9C/J,EAFI,CAAAG,YAAA,EAAQ,EACF,EACJ;IAGJH,EADF,CAAAC,cAAA,gBAA6B,eACF;IAAAD,EAAA,CAAAE,MAAA,IAAsC;IAEnEF,EAFmE,CAAAG,YAAA,EAAK,EAChE,EACO;IACfH,EAAA,CAAAC,cAAA,2BAAuD;IACrDD,EAAA,CAAAsG,SAAA,WAKM;IAEJtG,EADF,CAAAC,cAAA,WAAK,mBACkE;IAAvBD,EAAA,CAAAgB,UAAA,mBAAAgJ,+EAAA;MAAA,MAAAC,OAAA,GAAAjK,EAAA,CAAAkB,aAAA,CAAAwI,IAAA,EAAAtC,SAAA;MAAA,MAAAhG,MAAA,GAAApB,EAAA,CAAAqB,aAAA;MAAA,OAAArB,EAAA,CAAAwB,WAAA,CAASJ,MAAA,CAAAqG,OAAA,CAAAwC,OAAA,CAAY;IAAA,EAAC;IAACjK,EAAA,CAAAE,MAAA,oBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAChFH,EAAA,CAAAC,cAAA,mBAA0G;IAAtED,EAAA,CAAAgB,UAAA,mBAAAkJ,+EAAA;MAAA,MAAAD,OAAA,GAAAjK,EAAA,CAAAkB,aAAA,CAAAwI,IAAA,EAAAtC,SAAA;MAAA,MAAAhG,MAAA,GAAApB,EAAA,CAAAqB,aAAA;MAAA,OAAArB,EAAA,CAAAwB,WAAA,CAASJ,MAAA,CAAA+I,aAAA,CAAAF,OAAA,CAAkB;IAAA,EAAC;IAC9DjK,EAAA,CAAAE,MAAA,wCACF;IAGNF,EAHM,CAAAG,YAAA,EAAS,EACL,EACS,EACT;;;;IA/ENH,EAAA,CAAAM,SAAA,GACF;IADEN,EAAA,CAAAmD,kBAAA,2BAAA/B,MAAA,CAAAgJ,YAAA,kBAAAhJ,MAAA,CAAAgJ,YAAA,CAAAnH,UAAA,QAAA7B,MAAA,CAAAgJ,YAAA,kBAAAhJ,MAAA,CAAAgJ,YAAA,CAAAlH,MAAA,aACF;IAwB6BlD,EAAA,CAAAM,SAAA,IAAmB;IAAnBN,EAAA,CAAAI,UAAA,YAAAgB,MAAA,CAAAiJ,cAAA,CAAmB;IA2BnCrK,EAAA,CAAAM,SAAA,EAAiC;IAAjCN,EAAA,CAAAI,UAAA,SAAAgB,MAAA,CAAAiJ,cAAA,CAAAC,MAAA,OAAiC;IAUjBtK,EAAA,CAAAM,SAAA,GAAsC;IAAtCN,EAAA,CAAAO,kBAAA,yBAAAa,MAAA,CAAAoI,cAAA,CAAApI,MAAA,CAAAmJ,WAAA,MAAsC;IAYEvK,EAAA,CAAAM,SAAA,GAAwC;IAAxCN,EAAA,CAAAI,UAAA,aAAAgB,MAAA,CAAAiJ,cAAA,CAAAC,MAAA,OAAwC;;;ADnZjH,OAAM,MAAOE,4BAA6B,SAAQpL,aAAa;EAE7DqL,YACUC,MAAmB,EACnBC,UAAsB,EACtBC,aAA8B,EAC9BC,OAAuB,EACvBC,KAAuB,EACvBC,aAA2B,EAC3BC,qBAA2C,EAC3CC,iBAAmC,EACnCC,OAAsB,EACtBC,MAAc,EACdC,aAA2B,EAC3BC,gBAAgC,EAChCC,gBAAkC;IAE1C,KAAK,CAACZ,MAAM,CAAC;IAdL,KAAAA,MAAM,GAANA,MAAM;IACN,KAAAC,UAAU,GAAVA,UAAU;IACV,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,qBAAqB,GAArBA,qBAAqB;IACrB,KAAAC,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAd1B,KAAAC,eAAe,GAAW,CAAC,CAAC;IA0BnB,KAAAC,SAAS,GAAG,CAAC;IACb,KAAAC,QAAQ,GAAG,EAAE;IACb,KAAAC,SAAS,GAAG,CAAC;IACb,KAAAC,YAAY,GAAG,CAAC;IAEzB,KAAAC,aAAa,GAAiB,CAC5B;MACEC,KAAK,EAAE,CAAC;MACRC,GAAG,EAAE,OAAO;MACZpL,KAAK,EAAE;KACR,EACD;MACEmL,KAAK,EAAE,CAAC;MACRC,GAAG,EAAE,aAAa;MAClBpL,KAAK,EAAE;KACR,CACF;IAED,KAAAqL,gBAAgB,GAAG,CACjB;MACEF,KAAK,EAAE,IAAI;MACXC,GAAG,EAAE,KAAK;MACVpL,KAAK,EAAE;KACR,EACD;MACEmL,KAAK,EAAE,IAAI;MACXC,GAAG,EAAE,QAAQ;MACbpL,KAAK,EAAE;KACR,EACD;MACEmL,KAAK,EAAE,KAAK;MACZC,GAAG,EAAE,YAAY;MACjBpL,KAAK,EAAE;KACR,CACF;IAKD,KAAAsL,gBAAgB,GAAU,CAAC;MAAEtL,KAAK,EAAE,IAAI;MAAEmL,KAAK,EAAE;IAAE,CAAE,CAAC;IACtD,KAAAI,gBAAgB,GAAU,CAAC;MAAEvL,KAAK,EAAE,IAAI;MAAEmL,KAAK,EAAE;IAAE,CAAE,CAAC;IACtD,KAAA9G,eAAe,GAAU,CAAC;MAAErE,KAAK,EAAE,IAAI;MAAEmL,KAAK,EAAE,CAAC;IAAC,CAAE,CAAC;IACrD,KAAAjF,gBAAgB,GAAU,CAAC;MAAElG,KAAK,EAAE,IAAI;MAAEmL,KAAK,EAAE,CAAC;IAAC,CAAE,CAAC;IACtD,KAAApH,gBAAgB,GAAU,CAAC;MAAE/D,KAAK,EAAE,IAAI;MAAEmL,KAAK,EAAE,CAAC;IAAC,CAAE,CAAC;IACtD,KAAAK,iBAAiB,GAAU,CAAC;MAAExL,KAAK,EAAE,IAAI;MAAEmL,KAAK,EAAE,CAAC;IAAC,CAAE,CAAC;IAEvD,KAAArH,OAAO,GAAG;MACRO,eAAe,EAAE,IAAI,CAAC4F,UAAU,CAACwB,cAAc,CAACzM,iBAAiB,CAAC;MAClE+E,gBAAgB,EAAE,IAAI,CAACkG,UAAU,CAACwB,cAAc,CAACvM,aAAa,CAAC;MAC/DgH,gBAAgB,EAAE,IAAI,CAAC+D,UAAU,CAACwB,cAAc,CAACxM,aAAa;KAC/D;IAGD,KAAAyM,UAAU,GAAG;MACXC,QAAQ,EAAE,CAAC;MACXzG,KAAK,EAAE,EAAE;MACTvC,SAAS,EAAE,KAAK;MAChBG,UAAU,EAAE,CAAC;MACbE,SAAS,EAAE,KAAK;MAChB8B,aAAa,EAAE,EAAE;MACjB8G,WAAW,EAAE,EAAE;MACfC,SAAS,EAAE,EAAE;MACbnJ,UAAU,EAAE,CAAC;MACbH,UAAU,EAAE,EAAE;MACd6C,MAAM,EAAE;KACT;IACD,KAAAuE,cAAc,GAAoB,EAAE;IACpC,KAAAE,WAAW,GAAW,CAAC;IACvB,KAAAH,YAAY,GAAQ,IAAI;IACxB,KAAAoC,kBAAkB,GAAW,CAAC;IA8G9B,KAAAC,YAAY,GAAgB,IAAI;IAwKhC,KAAAC,uBAAuB,GAAU,CAC/B;MACEb,KAAK,EAAE,EAAE;MAAEnL,KAAK,EAAE;KACnB,CACF;IAxWC,IAAI,CAAC0K,aAAa,CAACuB,OAAO,EAAE,CAACC,IAAI,CAC/BtN,GAAG,CAAEuN,GAAW,IAAI;MAClB,IAAIA,GAAG,CAACC,MAAM,kDAA4B,CAAC,CAACD,GAAG,CAACE,OAAO,EAAE;QACvD,IAAI,CAACxB,eAAe,GAAGsB,GAAG,CAACE,OAAO;MACpC;IACF,CAAC,CAAC,CACH,CAACC,SAAS,EAAE;EACf;EAyESC,QAAQA,CAAA;IACf,IAAI,CAAClI,eAAe,GAAG,CACrB,GAAG,IAAI,CAACA,eAAe,EACvB,GAAG,IAAI,CAAC4F,UAAU,CAACwB,cAAc,CAACzM,iBAAiB,CAAC,CACrD;IACD,IAAI,CAACkH,gBAAgB,GAAG,CACtB,GAAG,IAAI,CAACA,gBAAgB,EACxB,GAAG,IAAI,CAAC+D,UAAU,CAACwB,cAAc,CAACxM,aAAa,CAAC,CACjD;IACD,IAAI,CAAC8E,gBAAgB,GAAG,CACtB,GAAG,IAAI,CAACA,gBAAgB,EACxB,GAAG,IAAI,CAACkG,UAAU,CAACwB,cAAc,CAACvM,aAAa,CAAC,CACjD;IACD,IAAI,CAACsM,iBAAiB,GAAG,CACvB,GAAG,IAAI,CAACA,iBAAiB,EACzB,GAAG,IAAI,CAACvB,UAAU,CAACwB,cAAc,CAACtM,cAAc,CAAC,CAClD;IAED,IAAIC,mBAAmB,CAACoN,iBAAiB,CAACnN,WAAW,CAACoN,YAAY,CAAC,IAAI,IAAI,IACtErN,mBAAmB,CAACoN,iBAAiB,CAACnN,WAAW,CAACoN,YAAY,CAAC,IAAIC,SAAS,IAC5EtN,mBAAmB,CAACoN,iBAAiB,CAACnN,WAAW,CAACoN,YAAY,CAAC,IAAI,EAAE,EAAE;MAC1E,IAAIE,eAAe,GAAGC,IAAI,CAACC,KAAK,CAACzN,mBAAmB,CAACoN,iBAAiB,CAACnN,WAAW,CAACoN,YAAY,CAAC,CAAC;MACjG,IAAI,CAAC9K,WAAW,GAAG;QACjBC,kBAAkB,EAAE,IAAI;QACxB;QACA;QACA;QACAkL,kBAAkB,EAAEH,eAAe,CAACG,kBAAkB,IAAI,IAAI,IAAIH,eAAe,CAACG,kBAAkB,IAAIJ,SAAS,GAC7G,IAAI,CAACnB,gBAAgB,CAACwB,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC7B,KAAK,IAAIwB,eAAe,CAACG,kBAAkB,CAAC3B,KAAK,CAAC,GACpF,IAAI,CAACI,gBAAgB,CAAC,CAAC,CAAC;QAC5BjG,kBAAkB,EAAEqH,eAAe,CAACrH,kBAAkB,IAAI,IAAI,IAAIqH,eAAe,CAACrH,kBAAkB,IAAIoH,SAAS,GAC7G,IAAI,CAACxG,gBAAgB,CAAC6G,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC7B,KAAK,IAAIwB,eAAe,CAACrH,kBAAkB,CAAC6F,KAAK,CAAC,GACpF,IAAI,CAACjF,gBAAgB,CAAC,CAAC,CAAC;QAC5BvC,kBAAkB,EAAEgJ,eAAe,CAAChJ,kBAAkB,IAAI,IAAI,IAAIgJ,eAAe,CAAChJ,kBAAkB,IAAI+I,SAAS,GAC7G,IAAI,CAAC3I,gBAAgB,CAACgJ,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC7B,KAAK,IAAIwB,eAAe,CAAChJ,kBAAkB,CAACwH,KAAK,CAAC,GACpF,IAAI,CAACpH,gBAAgB,CAAC,CAAC,CAAC;QAC5BI,iBAAiB,EAAEwI,eAAe,CAACxI,iBAAiB,IAAI,IAAI,IAAIwI,eAAe,CAACxI,iBAAiB,IAAIuI,SAAS,GAC1G,IAAI,CAACrI,eAAe,CAAC0I,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC7B,KAAK,IAAIwB,eAAe,CAACxI,iBAAiB,CAACgH,KAAK,CAAC,GAClF,IAAI,CAAC9G,eAAe,CAAC,CAAC,CAAC;QAC3B4I,mBAAmB,EAAEN,eAAe,CAACM,mBAAmB,IAAI,IAAI,IAAIN,eAAe,CAACM,mBAAmB,IAAIP,SAAS,GAChH,IAAI,CAAClB,iBAAiB,CAACuB,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC7B,KAAK,IAAIwB,eAAe,CAACM,mBAAmB,CAAC9B,KAAK,CAAC,GACtF,IAAI,CAACK,iBAAiB,CAAC,CAAC,CAAC;QAC7B0B,gBAAgB,EAAEP,eAAe,CAACO,gBAAgB,IAAI,IAAI,IAAIP,eAAe,CAACO,gBAAgB,IAAIR,SAAS,GACvG,IAAI,CAACrB,gBAAgB,CAAC0B,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC7B,KAAK,IAAIwB,eAAe,CAACO,gBAAgB,CAAC/B,KAAK,CAAC,GAClF,IAAI,CAACE,gBAAgB,CAAC,CAAC,CAAC;QAC5B8B,KAAK,EAAER,eAAe,CAACQ,KAAK,IAAI,IAAI,IAAIR,eAAe,CAACQ,KAAK,IAAIT,SAAS,GACtEC,eAAe,CAACQ,KAAK,GACrB,EAAE;QACNC,GAAG,EAAET,eAAe,CAACS,GAAG,IAAI,IAAI,IAAIT,eAAe,CAACS,GAAG,IAAIV,SAAS,GAChEC,eAAe,CAACS,GAAG,GACnB;OACL;IACH,CAAC,MACI;MACH,IAAI,CAACzL,WAAW,GAAG;QACjBC,kBAAkB,EAAE,IAAI;QACxByL,qBAAqB,EAAE,IAAI,CAACrB,uBAAuB,CAAC,CAAC,CAAC;QACtDc,kBAAkB,EAAE,IAAI,CAACvB,gBAAgB,CAAC,CAAC,CAAC;QAC5CjG,kBAAkB,EAAE,IAAI,CAACY,gBAAgB,CAAC,CAAC,CAAC;QAC5CvC,kBAAkB,EAAE,IAAI,CAACI,gBAAgB,CAAC,CAAC,CAAC;QAC5CI,iBAAiB,EAAE,IAAI,CAACE,eAAe,CAAC,CAAC,CAAC;QAC1C4I,mBAAmB,EAAE,IAAI,CAACzB,iBAAiB,CAAC,CAAC,CAAC;QAC9C0B,gBAAgB,EAAE,IAAI,CAAC7B,gBAAgB,CAAC,CAAC,CAAC;QAC1C8B,KAAK,EAAE,EAAE;QACTC,GAAG,EAAE;OACN;IACH;IACA,IAAI,CAACE,gBAAgB,EAAE;EACzB;EAEAC,QAAQA,CAAA;IACN,IAAIC,WAAW,GAAG;MAChB5L,kBAAkB,EAAE,IAAI,CAACD,WAAW,CAACC,kBAAkB;MACvD;MACAuL,KAAK,EAAE,IAAI,CAACxL,WAAW,CAACwL,KAAK;MAC7BC,GAAG,EAAE,IAAI,CAACzL,WAAW,CAACyL,GAAG;MACzBN,kBAAkB,EAAE,IAAI,CAACnL,WAAW,CAACmL,kBAAkB;MACvDxH,kBAAkB,EAAE,IAAI,CAAC3D,WAAW,CAAC2D,kBAAkB;MACvD4H,gBAAgB,EAAE,IAAI,CAACvL,WAAW,CAACuL,gBAAgB;MACnDvJ,kBAAkB,EAAE,IAAI,CAAChC,WAAW,CAACgC,kBAAkB;MACvDQ,iBAAiB,EAAE,IAAI,CAACxC,WAAW,CAACwC,iBAAiB;MACrD8I,mBAAmB,EAAE,IAAI,CAACtL,WAAW,CAACsL;KACvC;IACD7N,mBAAmB,CAACqO,iBAAiB,CAACpO,WAAW,CAACoN,YAAY,EAAEG,IAAI,CAACc,SAAS,CAACF,WAAW,CAAC,CAAC;IAC5F,IAAI,CAACG,YAAY,EAAE,CAACrB,SAAS,EAAE;EACjC;EAEAsB,WAAWA,CAACC,OAAe;IACzB,IAAI,CAAC7C,SAAS,GAAG6C,OAAO;IACxB,IAAI,CAACF,YAAY,EAAE,CAACrB,SAAS,EAAE;EACjC;EAEAwB,WAAWA,CAAA;IACT,IAAI,IAAI,CAACnM,WAAW,CAACC,kBAAkB,CAACC,GAAG,EAAE;MAC3C,IAAI,CAACwI,aAAa,CAAC0D,4BAA4B,CAAC;QAC9CC,YAAY,EAAE,IAAI,CAACrM,WAAW,CAACC,kBAAkB,CAACC;OACnD,CAAC,CAACyK,SAAS,CAACH,GAAG,IAAG;QACjB,IAAIA,GAAG,CAAC8B,OAAO,IAAI9B,GAAG,CAAC+B,UAAU,IAAI,CAAC,EAAE;UACtC,IAAI,CAACvD,gBAAgB,CAACwD,iBAAiB,CACrChC,GAAG,CAAC8B,OAAO,EAAE,QAAQ,CACtB;QACH,CAAC,MAAM;UACL,IAAI,CAAC9D,OAAO,CAACiE,YAAY,CAACjC,GAAG,CAACkC,OAAQ,CAAC;QACzC;MACF,CAAC,CAAC;IACJ;EACF;EAMAC,gBAAgBA,CAAA;IACd,IAAI,CAACC,SAAS,CAACC,aAAa,CAACC,KAAK,EAAE;EACtC;EAEAC,cAAcA,CAACC,KAAY;IACzB,MAAMC,KAAK,GAAGD,KAAK,CAACE,MAA0B;IAC9C,IAAID,KAAK,CAACE,KAAK,IAAIF,KAAK,CAACE,KAAK,CAAClF,MAAM,GAAG,CAAC,EAAE;MACzC,IAAI,CAACmC,YAAY,GAAG6C,KAAK,CAACE,KAAK,CAAC,CAAC,CAAC;MAClC,IAAI,CAACC,WAAW,EAAE;IACpB;EACF;EAEAA,WAAWA,CAAA;IACT,IAAI,IAAI,CAAChD,YAAY,EAAE;MACrB,MAAMiD,QAAQ,GAAG,IAAIC,QAAQ,EAAE;MAC/BD,QAAQ,CAACE,MAAM,CAAC,OAAO,EAAE,IAAI,CAACnD,YAAY,CAAC;MAC3C,IAAI,CAAC1B,aAAa,CAAC8E,4BAA4B,CAAC;QAC9CC,IAAI,EAAE;UACJpB,YAAY,EAAE,IAAI,CAACrM,WAAW,CAACC,kBAAkB,CAACC,GAAG;UACrDwN,KAAK,EAAE,IAAI,CAACtD;;OAEf,CAAC,CAACO,SAAS,CAACH,GAAG,IAAG;QACjB,IAAIA,GAAG,CAAC+B,UAAU,KAAK,CAAC,EAAE;UACxB,IAAI,CAAC/D,OAAO,CAACmF,aAAa,CAACnD,GAAG,CAACkC,OAAQ,CAAC;UACxC,IAAI,CAACV,YAAY,EAAE,CAACrB,SAAS,EAAE;QACjC,CAAC,MAAM;UACL,IAAI,CAACnC,OAAO,CAACiE,YAAY,CAACjC,GAAG,CAACkC,OAAQ,CAAC;QACzC;MACF,CAAC,CAAC;IACJ;EACF;EAGAkB,gBAAgBA,CAAA;IACd,IAAI,CAAClF,aAAa,CAACmF,iCAAiC,CAAC;MACnDJ,IAAI,EAAE;QAAEpB,YAAY,EAAE,IAAI,CAACrM,WAAW,CAACC,kBAAkB,CAACC;MAAG;KAC9D,CAAC,CAACyK,SAAS,CAACH,GAAG,IAAG;MACjB,IAAIA,GAAG,CAAC8B,OAAO,IAAI9B,GAAG,CAAC+B,UAAU,IAAI,CAAC,EAAE;QACtC,IAAI,CAAC3C,gBAAgB,GAAG,CAAC;UACvBJ,KAAK,EAAE,EAAE;UAAEnL,KAAK,EAAE;SACnB,EAAE,GAAGmM,GAAG,CAAC8B,OAAO,CAACwB,GAAG,CAACC,CAAC,IAAG;UACxB,OAAO;YAAEvE,KAAK,EAAEuE,CAAC;YAAE1P,KAAK,EAAE0P;UAAC,CAAE;QAC/B,CAAC,CAAC,CAAC;QACH,IAAItQ,mBAAmB,CAACoN,iBAAiB,CAACnN,WAAW,CAACoN,YAAY,CAAC,IAAI,IAAI,IACtErN,mBAAmB,CAACoN,iBAAiB,CAACnN,WAAW,CAACoN,YAAY,CAAC,IAAIC,SAAS,IAC5EtN,mBAAmB,CAACoN,iBAAiB,CAACnN,WAAW,CAACoN,YAAY,CAAC,IAAI,EAAE,EAAE;UAC1E,IAAIE,eAAe,GAAGC,IAAI,CAACC,KAAK,CAACzN,mBAAmB,CAACoN,iBAAiB,CAACnN,WAAW,CAACoN,YAAY,CAAC,CAAC;UACjG,IAAIE,eAAe,CAACG,kBAAkB,IAAI,IAAI,IAAIH,eAAe,CAACG,kBAAkB,IAAIJ,SAAS,EAAE;YACjG,IAAIhE,KAAK,GAAG,IAAI,CAAC6C,gBAAgB,CAACoE,SAAS,CAAE3C,CAAM,IAAKA,CAAC,CAAC7B,KAAK,IAAIwB,eAAe,CAACG,kBAAkB,CAAC3B,KAAK,CAAC;YAC5G,IAAI,CAACxJ,WAAW,CAACmL,kBAAkB,GAAG,IAAI,CAACvB,gBAAgB,CAAC7C,KAAK,CAAC;UACpE,CAAC,MAAM;YACL,IAAI,CAAC/G,WAAW,CAACmL,kBAAkB,GAAG,IAAI,CAACvB,gBAAgB,CAAC,CAAC,CAAC;UAChE;QACF;MACF;IACF,CAAC,CAAC;EACJ;EAKAqE,WAAWA,CAAA;IACT,IAAI,CAACC,WAAW,GAAG;MACjB7B,YAAY,EAAE,IAAI,CAACrM,WAAW,CAACC,kBAAkB,CAACC,GAAG;MACrDiO,SAAS,EAAE,IAAI,CAAC9E,SAAS;MACzB+E,QAAQ,EAAE,IAAI,CAAChF;KAChB;IAED;IACA;IACA;IACA,IAAI,IAAI,CAACpJ,WAAW,CAACwL,KAAK,IAAI,IAAI,CAACxL,WAAW,CAACyL,GAAG,EAAE;MAClD,IAAI,CAACyC,WAAW,CAAC,QAAQ,CAAC,GAAG;QAAE1C,KAAK,EAAE,IAAI,CAACxL,WAAW,CAACwL,KAAK;QAAEC,GAAG,EAAE,IAAI,CAACzL,WAAW,CAACyL;MAAG,CAAE;IAC3F;IACA,IAAI,IAAI,CAACzL,WAAW,CAACmL,kBAAkB,EAAE;MACvC,IAAI,CAAC+C,WAAW,CAAC,YAAY,CAAC,GAAG,IAAI,CAAClO,WAAW,CAACmL,kBAAkB,CAAC3B,KAAK;IAC5E;IACA,IAAI,IAAI,CAACxJ,WAAW,CAAC2D,kBAAkB,CAAC6F,KAAK,EAAE;MAC7C,IAAI,CAAC0E,WAAW,CAAC,YAAY,CAAC,GAAG,IAAI,CAAClO,WAAW,CAAC2D,kBAAkB,CAAC6F,KAAK;IAC5E;IACA,IAAI,OAAO,IAAI,CAACxJ,WAAW,CAACuL,gBAAgB,CAAC/B,KAAK,KAAK,SAAS,EAAE;MAChE,IAAI,CAAC0E,WAAW,CAAC,WAAW,CAAC,GAAG,IAAI,CAAClO,WAAW,CAACuL,gBAAgB,CAAC/B,KAAK;IACzE;IACA,IAAI,IAAI,CAACxJ,WAAW,CAACgC,kBAAkB,CAACwH,KAAK,EAAE;MAC7C,IAAI,CAAC0E,WAAW,CAAC,YAAY,CAAC,GAAG,IAAI,CAAClO,WAAW,CAACgC,kBAAkB,CAACwH,KAAK;IAC5E;IACA,IAAI,IAAI,CAACxJ,WAAW,CAACwC,iBAAiB,CAACgH,KAAK,EAAE;MAC5C,IAAI,CAAC0E,WAAW,CAAC,WAAW,CAAC,GAAG,IAAI,CAAClO,WAAW,CAACwC,iBAAiB,CAACgH,KAAK;IAC1E;IACA,IAAI,IAAI,CAACxJ,WAAW,CAACsL,mBAAmB,CAAC9B,KAAK,EAAE;MAC9C,IAAI,CAAC0E,WAAW,CAAC,aAAa,CAAC,GAAG,IAAI,CAAClO,WAAW,CAACsL,mBAAmB,CAAC9B,KAAK;IAC9E;IAEA,OAAO,IAAI,CAAC0E,WAAW;EACzB;EAEAG,qBAAqBA,CAACC,GAAsB;IAC1C,OAAOA,GAAG,CAACC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK,CAACA,CAAC,CAAC5N,MAAM,IAAI,CAAC,KAAK2N,CAAC,CAAC3N,MAAM,IAAI,CAAC,CAAC,CAAC;EAC9D;EAEAmL,YAAYA,CAAA;IACV,OAAO,IAAI,CAACtD,aAAa,CAACgG,6BAA6B,CAAC;MACtDjB,IAAI,EAAE,IAAI,CAACQ,WAAW;KACvB,CAAC,CAAC1D,IAAI,CACLtN,GAAG,CAACuN,GAAG,IAAG;MACR,IAAIA,GAAG,CAAC8B,OAAO,IAAI9B,GAAG,CAAC+B,UAAU,IAAI,CAAC,EAAE;QACtC,IAAI,CAACoC,SAAS,GAAGnE,GAAG,CAAC8B,OAAO;QAC5B,IAAI,CAAChD,YAAY,GAAGkB,GAAG,CAACoE,UAAW;MACrC;IACF,CAAC,CAAC,CACH;EACH;EAIAC,0BAA0BA,CAAA;IACxB;IACA,IAAI,CAACjB,gBAAgB,EAAE;IACvB,IAAI,CAAC5B,YAAY,EAAE,CAACrB,SAAS,EAAE;EACjC;EACAgB,gBAAgBA,CAAA;IACd,IAAI,CAAC/C,iBAAiB,CAACkG,6CAA6C,CAAC;MACnErB,IAAI,EAAE;QACJsB,OAAO,EAAE,KAAK;QACdC,OAAO,EAAE;;KAEZ,CAAC,CAACzE,IAAI,CACLtN,GAAG,CAACuN,GAAG,IAAG;MACR,IAAIA,GAAG,CAAC8B,OAAO,IAAI9B,GAAG,CAAC+B,UAAU,IAAI,CAAC,EAAE;QACtC,IAAI,CAACjI,oBAAoB,GAAGkG,GAAG,CAAC8B,OAAO,EAAErE,MAAM,GAAGuC,GAAG,CAAC8B,OAAO,CAACwB,GAAG,CAACtD,GAAG,IAAG;UACtE,OAAO;YACLrM,cAAc,EAAEqM,GAAG,CAACrM,cAAc;YAClC+B,GAAG,EAAEsK,GAAG,CAACtK;WACV;QACH,CAAC,CAAC,GAAG,EAAE;QAEP,IAAIzC,mBAAmB,CAACoN,iBAAiB,CAACnN,WAAW,CAACoN,YAAY,CAAC,IAAI,IAAI,IACtErN,mBAAmB,CAACoN,iBAAiB,CAACnN,WAAW,CAACoN,YAAY,CAAC,IAAIC,SAAS,IAC5EtN,mBAAmB,CAACoN,iBAAiB,CAACnN,WAAW,CAACoN,YAAY,CAAC,IAAI,EAAE,EAAE;UAC1E,IAAIE,eAAe,GAAGC,IAAI,CAACC,KAAK,CAACzN,mBAAmB,CAACoN,iBAAiB,CAACnN,WAAW,CAACoN,YAAY,CAAC,CAAC;UACjG,IAAIE,eAAe,CAAC/K,kBAAkB,IAAI,IAAI,IAAI+K,eAAe,CAAC/K,kBAAkB,IAAI8K,SAAS,EAAE;YACjG,IAAIhE,KAAK,GAAG,IAAI,CAACzC,oBAAoB,CAAC0J,SAAS,CAAE3C,CAAM,IAAKA,CAAC,CAACnL,GAAG,IAAI8K,eAAe,CAAC/K,kBAAkB,CAACC,GAAG,CAAC;YAC5G,IAAI,CAACF,WAAW,CAACC,kBAAkB,GAAG,IAAI,CAACqE,oBAAoB,CAACyC,KAAK,CAAC;UACxE,CAAC,MAAM;YACL,IAAI,CAAC/G,WAAW,CAACC,kBAAkB,GAAG,IAAI,CAACqE,oBAAoB,CAAC,CAAC,CAAC;UACpE;QACF,CAAC,MACI;UACH,IAAI,CAACtE,WAAW,CAACC,kBAAkB,GAAG,IAAI,CAACqE,oBAAoB,CAAC,CAAC,CAAC;QACpE;MACF;IACF,CAAC,CAAC,EACFrH,GAAG,CAAC,MAAK;MACP;MACA,IAAI,CAAC2Q,gBAAgB,EAAE;MACvBqB,UAAU,CAAC,MAAK;QACd,IAAI,CAACjD,YAAY,EAAE,CAACrB,SAAS,EAAE;MACjC,CAAC,EAAE,GAAG,CAAC;IACT,CAAC,CAAC,CACH,CAACA,SAAS,EAAE;EACf;EA6CAuE,YAAYA,CAAC/O,GAAQ,EAAEgP,GAAQ;IAC7B,IAAI,CAACpN,cAAc,GAAG,EAAE;IACxB,IAAI,CAAC2G,aAAa,CAAC0G,6BAA6B,CAAC;MAC/C3B,IAAI,EAAE;QAAEzD,QAAQ,EAAE7J;MAAG;KACtB,CAAC,CAACwK,SAAS,CAACH,GAAG,IAAG;MACjB,IAAIA,GAAG,CAAC8B,OAAO,IAAI9B,GAAG,CAAC+B,UAAU,IAAI,CAAC,EAAE;QACtC,IAAI,CAACxJ,WAAW,GAAG;UACjB,GAAGyH,GAAG,CAAC8B,OAAO;UACdnI,eAAe,EAAEqG,GAAG,CAAC8B,OAAO,CAAC+C,gBAAgB,GAAG,IAAIC,IAAI,CAAC9E,GAAG,CAAC8B,OAAO,CAAC+C,gBAAgB,CAAC,GAAGtE,SAAS;UAClG1G,aAAa,EAAEmG,GAAG,CAAC8B,OAAO,CAACiD,cAAc,GAAG,IAAID,IAAI,CAAC9E,GAAG,CAAC8B,OAAO,CAACiD,cAAc,CAAC,GAAGxE;SACpF;QAED,IAAIP,GAAG,CAAC8B,OAAO,CAACkD,YAAY,EAAE;UAC5B,IAAI,CAACzN,cAAc,CAAC9B,kBAAkB,GAAG,IAAI,CAACwP,eAAe,CAAC,IAAI,CAACnL,oBAAoB,EAAE,KAAK,EAAEkG,GAAG,CAAC8B,OAAO,CAACkD,YAAY,CAAC;QAC3H;QACA,IAAI,CAACzN,cAAc,CAACC,kBAAkB,GAAG,IAAI,CAACyN,eAAe,CAAC,IAAI,CAACtN,OAAO,CAACC,gBAAgB,EAAE,OAAO,EAAEoI,GAAG,CAAC8B,OAAO,CAACnL,UAAU,CAAC;QAC7H,IAAIqJ,GAAG,CAAC8B,OAAO,CAACvL,UAAU,EAAE;UAC1B,IAAI,CAACgB,cAAc,CAAC4B,kBAAkB,GAAG,IAAI,CAAC8L,eAAe,CAAC,IAAI,CAACtN,OAAO,CAACoC,gBAAgB,EAAE,OAAO,EAAEiG,GAAG,CAAC8B,OAAO,CAACvL,UAAU,CAAC;QAC/H,CAAC,MAAM;UACL,IAAI,CAACgB,cAAc,CAAC4B,kBAAkB,GAAG,IAAI,CAACxB,OAAO,CAACoC,gBAAgB,CAAC,CAAC,CAAC;QAC3E;QACA,IAAI,CAACxC,cAAc,CAACS,iBAAiB,GAAG,IAAI,CAACiN,eAAe,CAAC,IAAI,CAACtN,OAAO,CAACO,eAAe,EAAE,OAAO,EAAE8H,GAAG,CAAC8B,OAAO,CAACpC,SAAS,CAAC;QAE1H,IAAIM,GAAG,CAAC8B,OAAO,CAACkD,YAAY,EAAE;UAC5B,IAAI,IAAI,CAAC3J,aAAa,EAAE;YACtB,IAAI,CAACA,aAAa,CAACwG,YAAY,GAAG7B,GAAG,CAAC8B,OAAO,CAACkD,YAAY;UAC5D;QACF;QACA,IAAI,CAACjH,aAAa,CAACmH,IAAI,CAACP,GAAG,CAAC;MAC9B;IAEF,CAAC,CAAC;EACJ;EAGAM,eAAeA,CAACE,KAAY,EAAElG,GAAW,EAAED,KAAU;IACnD,OAAOmG,KAAK,CAACvE,IAAI,CAACwE,IAAI,IAAIA,IAAI,CAACnG,GAAG,CAAC,KAAKD,KAAK,CAAC;EAChD;EAGA9J,eAAeA,CAACyP,GAAQ,EAAES,IAAS;IACjC,IAAI,CAACV,YAAY,CAACU,IAAI,CAACzP,GAAG,EAAEgP,GAAG,CAAC;EAClC;EAEA/P,SAASA,CAAC+P,GAAQ;IAChB,IAAI,CAACtJ,aAAa,GAAG;MACnBC,aAAa,EAAE,EAAE;MACjBjF,MAAM,EAAEkK,SAAS;MACjB/E,eAAe,EAAE+E;KAClB;IACD,IAAI,CAACxC,aAAa,CAACmH,IAAI,CAACP,GAAG,CAAC;EAC9B;EAKAU,UAAUA,CAACC,WAAmB;IAC5B,IAAIA,WAAW,EAAE;MACf,OAAO3S,MAAM,CAAC2S,WAAW,CAAC,CAACC,MAAM,CAAC,qBAAqB,CAAC;IAC1D;IACA,OAAO,EAAE;EACX;EAEA/K,cAAcA,CAACmK,GAAQ;IACrB,IAAI,CAACpM,WAAW,CAACsM,gBAAgB,GAAG,IAAI,CAACtM,WAAW,CAACoB,eAAe,GAAG,IAAI,CAAC0L,UAAU,CAAC,IAAI,CAAC9M,WAAW,CAACoB,eAAe,CAAC,GAAG,EAAE,EAC3H,IAAI,CAACpB,WAAW,CAACwM,cAAc,GAAG,IAAI,CAACxM,WAAW,CAACsB,aAAa,GAAG,IAAI,CAACwL,UAAU,CAAC,IAAI,CAAC9M,WAAW,CAACsB,aAAa,CAAC,GAAG,EAAE,EAEvH,IAAI,CAAC2L,kBAAkB,GAAG;MACxB7M,aAAa,EAAE,IAAI,CAACJ,WAAW,CAACI,aAAa;MAC7CvC,UAAU,EAAE,IAAI,CAACmC,WAAW,CAACC,UAAU;MACvCgH,QAAQ,EAAE,IAAI,CAACjH,WAAW,CAACkN,GAAG;MAC9BlP,UAAU,EAAE,IAAI,CAACgB,cAAc,CAAC4B,kBAAkB,GAAG,IAAI,CAAC5B,cAAc,CAAC4B,kBAAkB,CAAC6F,KAAK,GAAG,IAAI;MACxGxI,SAAS,EAAE,IAAI,CAAC+B,WAAW,CAAC/B,SAAS;MACrCK,SAAS,EAAE,IAAI,CAAC0B,WAAW,CAAC1B,SAAS;MACrCkC,KAAK,EAAE,IAAI,CAACR,WAAW,CAACQ,KAAK;MAC7B0G,WAAW,EAAE,IAAI,CAAClH,WAAW,CAACM,WAAW;MACzClC,UAAU,EAAE,IAAI,CAACY,cAAc,CAACC,kBAAkB,GAAG,IAAI,CAACD,cAAc,CAACC,kBAAkB,CAACwH,KAAK,GAAG,IAAI;MACxG/F,MAAM,EAAE,IAAI,CAACV,WAAW,CAACU,MAAM;MAC/ByG,SAAS,EAAE,IAAI,CAACnI,cAAc,CAACS,iBAAiB,GAAG,IAAI,CAACT,cAAc,CAACS,iBAAiB,CAACgH,KAAK,GAAG,IAAI;MACrG6F,gBAAgB,EAAE,IAAI,CAACtM,WAAW,CAACsM,gBAAgB;MACnDE,cAAc,EAAE,IAAI,CAACxM,WAAW,CAACwM;KAClC;IACH,IAAI,CAACW,UAAU,EAAE;IACjB,IAAI,IAAI,CAACzH,KAAK,CAAC0H,aAAa,CAAClI,MAAM,GAAG,CAAC,EAAE;MACvC,IAAI,CAACO,OAAO,CAAC4H,aAAa,CAAC,IAAI,CAAC3H,KAAK,CAAC0H,aAAa,CAAC;MACpD;IACF;IAEA,IAAI,CAACzH,aAAa,CAAC2H,0BAA0B,CAAC;MAC5C5C,IAAI,EAAE,IAAI,CAACuC;KACZ,CAAC,CAACzF,IAAI,CACLtN,GAAG,CAACuN,GAAG,IAAG;MACR,IAAIA,GAAG,CAAC+B,UAAU,KAAK,CAAC,EAAE;QACxB,IAAI,CAAC/D,OAAO,CAACmF,aAAa,CAAC,MAAM,CAAC;QAClCwB,GAAG,CAACmB,KAAK,EAAE;MACb,CAAC,MAAM;QACL,IAAI,CAAC9H,OAAO,CAACiE,YAAY,CAACjC,GAAG,CAACkC,OAAQ,CAAC;QACvCyC,GAAG,CAACmB,KAAK,EAAE;MACb;IACF,CAAC,CAAC,EACFtT,SAAS,CAAC,MAAM,IAAI,CAACgP,YAAY,EAAE,CAAC,CACrC,CAACrB,SAAS,EAAE;EACf;EAGA4F,QAAQA,CAACpB,GAAQ;IACf,IAAIqB,OAAO,GAAkB;MAC3BrN,aAAa,EAAE,IAAI,CAACJ,WAAW,CAACI,aAAa;MAC7CvC,UAAU,EAAE,IAAI,CAACmC,WAAW,CAACC,UAAU;MACvCgH,QAAQ,EAAE,IAAI,CAACjH,WAAW,CAACkN,GAAG;MAC9BlP,UAAU,EAAE,IAAI,CAACgC,WAAW,CAAChC,UAAU;MACvCC,SAAS,EAAE,IAAI,CAAC+B,WAAW,CAAC/B,SAAS;MACrCK,SAAS,EAAE,IAAI,CAAC0B,WAAW,CAAC1B,SAAS;MACrCkC,KAAK,EAAE,IAAI,CAACR,WAAW,CAACQ,KAAK;MAC7B0G,WAAW,EAAE,IAAI,CAAClH,WAAW,CAACM,WAAW;MACzClC,UAAU,EAAE,IAAI,CAAC4B,WAAW,CAAC5B,UAAU;MACvCsC,MAAM,EAAE,IAAI,CAACV,WAAW,CAACU,MAAM;MAC/ByG,SAAS,EAAE,IAAI,CAACnH,WAAW,CAACmH;KAC7B;IACD,IAAI,CAACxB,aAAa,CAAC2H,0BAA0B,CAAC;MAC5C5C,IAAI,EAAE+C;KACP,CAAC,CAAC7F,SAAS,CAACH,GAAG,IAAG;MACjB,IAAIA,GAAG,CAAC+B,UAAU,KAAK,CAAC,EAAE;QACxB,IAAI,CAAC/D,OAAO,CAACmF,aAAa,CAAC,MAAM,CAAC;QAClCwB,GAAG,CAACmB,KAAK,EAAE;MACb;IACF,CAAC,CAAC;EAEJ;EAEAlL,OAAOA,CAAC+J,GAAQ;IACdA,GAAG,CAACmB,KAAK,EAAE;EACb;EAEAG,YAAYA,CAACC,IAAS,EAAEC,EAAQ;IAC9B,MAAMC,KAAK,GAAGD,EAAE,GAAGA,EAAE,GAAG,IAAI,CAAC3Q,WAAW,CAACC,kBAAkB,CAACC,GAAG;IAC/D,IAAI,CAAC4I,MAAM,CAAC+H,QAAQ,CAAC,CAAC,+BAA+BH,IAAI,EAAE,EAAEE,KAAK,CAAC,CAAC;EACtE;EAEA7Q,4BAA4BA,CAAC2Q,IAAS,EAAEI,WAAgB,EAAEC,OAAY;IACpE,IAAI,CAACjI,MAAM,CAAC+H,QAAQ,CAAC,CAAC,+BAA+BH,IAAI,EAAE,EAAEI,WAAW,EAAEC,OAAO,CAAC,CAAC;EACrF;EAEAxQ,cAAcA,CAACqP,IAAS;IACtB,IAAIoB,OAAO,CAAC,UAAU,CAAC,EAAE;MACvB,IAAI,CAACtI,aAAa,CAACuI,oCAAoC,CAAC;QACtDxD,IAAI,EAAEmC,IAAI,CAACzP;OACZ,CAAC,CAACwK,SAAS,CAACH,GAAG,IAAG;QACjB,IAAIA,GAAG,CAAC+B,UAAU,IAAI,CAAC,EAAE;UACvB,IAAI,CAAC/D,OAAO,CAACmF,aAAa,CAAC,MAAM,CAAC;QACpC;MACF,CAAC,CAAC;IACJ;EACF;EAIAuC,UAAUA,CAAA;IACR,IAAI,CAACzH,KAAK,CAACyI,KAAK,EAAE;IAClB,IAAI,CAACzI,KAAK,CAAC0I,QAAQ,CAAC,QAAQ,EAAE,IAAI,CAACpO,WAAW,CAACkN,GAAG,CAAC;IACnD,IAAI,CAACxH,KAAK,CAAC0I,QAAQ,CAAC,QAAQ,EAAE,IAAI,CAACnB,kBAAkB,CAACpP,UAAU,CAAC;IACjE,IAAI,CAAC6H,KAAK,CAAC2I,iBAAiB,CAAC,MAAM,EAAE,IAAI,CAACpB,kBAAkB,CAACpP,UAAU,EAAE,EAAE,CAAC;IAC5E,IAAI,CAAC6H,KAAK,CAAC0I,QAAQ,CAAC,MAAM,EAAE,IAAI,CAACpO,WAAW,CAAClC,MAAM,CAAC;IACpD,IAAI,CAAC4H,KAAK,CAAC2I,iBAAiB,CAAC,QAAQ,EAAE,IAAI,CAACpB,kBAAkB,CAAC7M,aAAa,EAAE,EAAE,CAAC;IACjF;IACA;IACA;IACA,IAAI,CAACsF,KAAK,CAAC4I,OAAO,CAAC,QAAQ,EAAE,IAAI,CAACrB,kBAAkB,CAACzM,KAAK,EAAE,IAAI,CAACsF,OAAO,CAACyI,WAAW,CAAC;IACrF,IAAI,CAAC7I,KAAK,CAAC8I,aAAa,CAAC,QAAQ,EAAE,IAAI,CAACvB,kBAAkB,CAACvM,MAAM,CAAC;IAClE,IAAI,CAACgF,KAAK,CAAC0I,QAAQ,CAAC,MAAM,EAAE,IAAI,CAACnB,kBAAkB,CAAC9F,SAAS,CAAC;IAC9D,IAAI,CAACzB,KAAK,CAAC0I,QAAQ,CAAC,QAAQ,EAAE,IAAI,CAACpP,cAAc,CAAC4B,kBAAkB,CAAC6F,KAAK,CAAC;IAC3E,IAAI,CAACf,KAAK,CAAC0I,QAAQ,CAAC,QAAQ,EAAE,IAAI,CAACpP,cAAc,CAACC,kBAAkB,CAACwH,KAAK,CAAC;IAC3E,IAAI,IAAI,CAACzG,WAAW,CAACsM,gBAAgB,EAAE;MACrC,IAAI,CAAC5G,KAAK,CAAC0I,QAAQ,CAAC,UAAU,EAAE,IAAI,CAACpO,WAAW,CAACwM,cAAc,CAAC;IAClE;IACA,IAAI,IAAI,CAACxM,WAAW,CAACwM,cAAc,EAAE;MACnC,IAAI,CAAC9G,KAAK,CAAC0I,QAAQ,CAAC,UAAU,EAAE,IAAI,CAACpO,WAAW,CAACsM,gBAAgB,CAAC;IACpE;IACA,IAAI,CAAC5G,KAAK,CAAC+I,mBAAmB,CAAC,QAAQ,EAAE,IAAI,CAACzO,WAAW,CAACsM,gBAAgB,GAAG,IAAI,CAACtM,WAAW,CAACsM,gBAAgB,GAAG,EAAE,EAAE,IAAI,CAACtM,WAAW,CAACwM,cAAc,GAAG,IAAI,CAACxM,WAAW,CAACwM,cAAc,GAAG,EAAE,CAAC;EAC9L;EAEAkC,uBAAuBA,CAAA;IACrB,IAAI,CAAChJ,KAAK,CAACyI,KAAK,EAAE;IAClB,IAAI,CAACzI,KAAK,CAAC0I,QAAQ,CAAC,MAAM,EAAE,IAAI,CAACtL,aAAa,CAACwG,YAAY,CAAC;IAC5D,IAAI,CAAC5D,KAAK,CAAC0I,QAAQ,CAAC,MAAM,EAAE,IAAI,CAACtL,aAAa,CAACC,aAAa,CAAC;IAC7D,IAAI,CAAC2C,KAAK,CAAC2I,iBAAiB,CAAC,MAAM,EAAE,IAAI,CAACvL,aAAa,CAACC,aAAa,EAAE,EAAE,CAAC;IAC1E,IAAI,CAAC2C,KAAK,CAACiJ,sBAAsB,CAAC,UAAU,EAAE,IAAI,CAAC7L,aAAa,CAAChF,MAAM,EAAE,CAAC,EAAE,GAAG,CAAC;IAChF,IAAI,CAAC4H,KAAK,CAACiJ,sBAAsB,CAAC,SAAS,EAAE,IAAI,CAAC7L,aAAa,CAACG,eAAe,EAAE,CAAC,EAAE,GAAG,CAAC;EAC1F;EAGAN,gBAAgBA,CAACyJ,GAAQ;IACvB,IAAI,CAACtJ,aAAa,CAACwG,YAAY,GAAG,IAAI,CAACrM,WAAW,CAACC,kBAAkB,CAACC,GAAG,EACvE,IAAI,CAACuR,uBAAuB,EAAE;IAChC,IAAI,IAAI,CAAChJ,KAAK,CAAC0H,aAAa,CAAClI,MAAM,GAAG,CAAC,EAAE;MACvC,IAAI,CAACO,OAAO,CAAC4H,aAAa,CAAC,IAAI,CAAC3H,KAAK,CAAC0H,aAAa,CAAC;MACpD;IACF;IACA,IAAI,CAACxH,qBAAqB,CAACgJ,yCAAyC,CAAC;MACnElE,IAAI,EAAE,IAAI,CAAC5H;KACZ,CAAC,CAAC0E,IAAI,CACLtN,GAAG,CAACuN,GAAG,IAAG;MACR,IAAIA,GAAG,CAAC+B,UAAU,KAAK,CAAC,EAAE;QACxB,IAAI,CAAC/D,OAAO,CAACmF,aAAa,CAAC,MAAM,CAAC;QAClCwB,GAAG,CAACmB,KAAK,EAAE;MACb;IACF,CAAC,CAAC,EACFtT,SAAS,CAAC,MAAM,IAAI,CAACgP,YAAY,EAAE,CAAC,CACrC,CAACrB,SAAS,EAAE;EACf,CAAC,CAAE;EACGjK,aAAaA,CAACkR,MAAW,EAAEhC,IAAS;IAAA,IAAAiC,KAAA;IAAA,OAAAC,iBAAA;MACxCD,KAAI,CAAC9J,YAAY,GAAG6H,IAAI;MACxBiC,KAAI,CAAC7J,cAAc,GAAG,EAAE;MACxB6J,KAAI,CAAC3J,WAAW,GAAG,CAAC;MACpB2J,KAAI,CAAC1H,kBAAkB,GAAG,CAAC,CAAC,CAAC;MAE7B;MACA,IAAI;QACF,MAAM4H,QAAQ,SAASF,KAAI,CAAC5I,gBAAgB,CAAC+I,qBAAqB,CAACpC,IAAI,CAACzP,GAAG,CAAC,CAAC8R,SAAS,EAAE;QACxFC,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAEJ,QAAQ,CAAC,CAAC,CAAC;QAE1D,IAAIA,QAAQ,IAAIA,QAAQ,CAACxF,UAAU,KAAK,CAAC,IAAIwF,QAAQ,CAACzF,OAAO,EAAE;UAC7D;UACAuF,KAAI,CAAC1H,kBAAkB,GAAG4H,QAAQ,CAACzF,OAAO,CAAC8F,YAAY,IAAI,CAAC;UAC5DF,OAAO,CAACC,GAAG,CAAC,UAAU,EAAEN,KAAI,CAAC1H,kBAAkB,CAAC,CAAC,CAAC;UAElD;UACA,IAAI4H,QAAQ,CAACzF,OAAO,CAAC+F,KAAK,IAAIC,KAAK,CAACC,OAAO,CAACR,QAAQ,CAACzF,OAAO,CAAC+F,KAAK,CAAC,EAAE;YACnE;YACAR,KAAI,CAAC7J,cAAc,GAAG+J,QAAQ,CAACzF,OAAO,CAAC+F,KAAK,CAACvE,GAAG,CAAE0E,KAAU,KAAM;cAChEC,QAAQ,EAAEV,QAAQ,CAACzF,OAAO,CAACtC,QAAQ,IAAI4F,IAAI,CAACzP,GAAG;cAC/CuS,YAAY,EAAEX,QAAQ,CAACzF,OAAO,CAAC8F,YAAY;cAC3C7L,SAAS,EAAEiM,KAAK,CAACG,SAAS,IAAI,EAAE;cAChClM,UAAU,EAAE+L,KAAK,CAACI,UAAU,IAAI,CAAC;cACjChM,MAAM,EAAE4L,KAAK,CAACK,MAAM,IAAI,CAAC;cACzBC,OAAO,EAAEN,KAAK,CAACxD,OAAO,IAAI,CAAC;cAC3B9H,UAAU,EAAEsL,KAAK,CAACO,UAAU,IAAI;aACjC,CAAC,CAAC;YACHlB,KAAI,CAACnL,cAAc,EAAE;YACrBwL,OAAO,CAACC,GAAG,CAAC,WAAW,EAAEN,KAAI,CAAC7J,cAAc,CAACC,MAAM,EAAE,GAAG,CAAC,CAAC,CAAC;UAC7D,CAAC,MAAM;YACLiK,OAAO,CAACc,IAAI,CAAC,yBAAyB,EAAEjB,QAAQ,CAACzF,OAAO,CAAC;UAC3D;QACF,CAAC,MAAM;UACL4F,OAAO,CAACc,IAAI,CAAC,iCAAiC,EAAEjB,QAAQ,CAAC;QAC3D;MACF,CAAC,CAAC,OAAOkB,KAAK,EAAE;QACdf,OAAO,CAACe,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;MACnC;MAEApB,KAAI,CAACtJ,aAAa,CAACmH,IAAI,CAACkC,MAAM,EAAE;QAC9BsB,OAAO,EAAEtD,IAAI;QACbuD,oBAAoB,EAAE;OACvB,CAAC;IAAC;EACL;EACA;EACA7L,gBAAgBA,CAAA;IACd,IAAI,CAACU,cAAc,CAACoL,IAAI,CAAC;MACvBX,QAAQ,EAAE,IAAI,CAAC1K,YAAY,EAAE5H,GAAG,IAAI,CAAC;MACrCoG,SAAS,EAAE,EAAE;MAAEE,UAAU,EAAE,CAAC;MAC5BG,MAAM,EAAE,CAAC;MACTkM,OAAO,EAAE,CAAC;MACV5L,UAAU,EAAE;KACb,CAAC;EACJ;EACA;EACMM,gBAAgBA,CAAA;IAAA,IAAA6L,MAAA;IAAA,OAAAvB,iBAAA;MACpB,IAAI;QACF,IAAI,CAACuB,MAAI,CAACtL,YAAY,EAAE5H,GAAG,EAAE;UAC3BkT,MAAI,CAAC7K,OAAO,CAACiE,YAAY,CAAC,QAAQ,CAAC;UACnC;QACF;QAEA,MAAM6G,OAAO,GAAG;UACdjH,YAAY,EAAEgH,MAAI,CAACtL,YAAY,CAACsE,YAAY,IAAI,CAAC;UACjDrC,QAAQ,EAAEqJ,MAAI,CAACtL,YAAY,CAAC5H;SAC7B;QAED,MAAM4R,QAAQ,SAASsB,MAAI,CAACpK,gBAAgB,CAACzB,gBAAgB,CAAC8L,OAAO,CAAC,CAACrB,SAAS,EAAE;QAClF,IAAIF,QAAQ,EAAEwB,OAAO,IAAIxB,QAAQ,CAACyB,IAAI,EAAE;UACtC,MAAMC,YAAY,GAAG1B,QAAQ,CAACyB,IAAI,CAAC1F,GAAG,CAAEzC,CAAM,KAAM;YAClDqH,YAAY,EAAErH,CAAC,CAAC+G,YAAY;YAC5BK,QAAQ,EAAEY,MAAI,CAACtL,YAAY,EAAE5H,GAAG;YAChCoG,SAAS,EAAE8E,CAAC,CAACsH,SAAS;YACtBlM,UAAU,EAAE4E,CAAC,CAACuH,UAAU;YACxBhM,MAAM,EAAEyE,CAAC,CAACwH,MAAM;YAChBC,OAAO,EAAEzH,CAAC,CAAC2D,OAAO;YAClB9H,UAAU,EAAE;WACb,CAAC,CAAC;UACHmM,MAAI,CAACrL,cAAc,CAACoL,IAAI,CAAC,GAAGK,YAAY,CAAC;UACzCJ,MAAI,CAAC3M,cAAc,EAAE;UACrB2M,MAAI,CAAC7K,OAAO,CAACmF,aAAa,CAAC,UAAU,CAAC;QACxC,CAAC,MAAM;UACL0F,MAAI,CAAC7K,OAAO,CAACiE,YAAY,CAACsF,QAAQ,EAAEvJ,OAAO,IAAI,UAAU,CAAC;QAC5D;MACF,CAAC,CAAC,OAAOyK,KAAK,EAAE;QACdf,OAAO,CAACe,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;QACjCI,MAAI,CAAC7K,OAAO,CAACiE,YAAY,CAAC,UAAU,CAAC;MACvC;IAAC;EACH;EAEA;EACAzF,mBAAmBA,CAACD,KAAa;IAC/B,MAAM6I,IAAI,GAAG,IAAI,CAAC5H,cAAc,CAACjB,KAAK,CAAC;IACvC,IAAI,CAACiB,cAAc,CAAC0L,MAAM,CAAC3M,KAAK,EAAE,CAAC,CAAC;IACpC,IAAI,CAACL,cAAc,EAAE;EACvB;EAEA;EACAA,cAAcA,CAAA;IACZ,IAAI,CAACwB,WAAW,GAAG,IAAI,CAACF,cAAc,CAAC2L,MAAM,CAAC,CAACC,GAAG,EAAEhE,IAAI,KAAI;MAC1D,OAAOgE,GAAG,GAAIhE,IAAI,CAACnJ,UAAU,GAAGmJ,IAAI,CAAChJ,MAAO;IAC9C,CAAC,EAAE,CAAC,CAAC;EACP;EAEA;EACAO,cAAcA,CAAC0M,MAAc;IAC3B,OAAO,IAAIC,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;MACpCC,KAAK,EAAE,UAAU;MACjBC,QAAQ,EAAE,KAAK;MACfC,qBAAqB,EAAE;KACxB,CAAC,CAACnE,MAAM,CAAC8D,MAAM,CAAC;EACnB;EAEA;EACM/L,aAAaA,CAACqH,GAAQ;IAAA,IAAAgF,MAAA;IAAA,OAAArC,iBAAA;MAC1B,IAAIqC,MAAI,CAACnM,cAAc,CAACC,MAAM,KAAK,CAAC,EAAE;QACpCkM,MAAI,CAAC3L,OAAO,CAACiE,YAAY,CAAC,UAAU,CAAC;QACrC;MACF;MAEA;MACA,MAAM2H,YAAY,GAAGD,MAAI,CAACnM,cAAc,CAACqM,MAAM,CAACzE,IAAI,IAClD,CAACA,IAAI,CAACrJ,SAAS,CAAC+N,IAAI,EAAE,IAAI1E,IAAI,CAACnJ,UAAU,GAAG,CAAC,IAAImJ,IAAI,CAAChJ,MAAM,GAAG,CAAC,CACjE;MAED,IAAIwN,YAAY,CAACnM,MAAM,GAAG,CAAC,EAAE;QAC3BkM,MAAI,CAAC3L,OAAO,CAACiE,YAAY,CAAC,uBAAuB,CAAC;QAClD;MACF;MAAE,IAAI;QACJ,MAAM6G,OAAO,GAAG;UACdvC,OAAO,EAAEoD,MAAI,CAACpM,YAAY,CAAC5H,GAAG;UAC9BoU,KAAK,EAAEJ,MAAI,CAACnM,cAAc;UAC1BwM,WAAW,EAAEL,MAAI,CAAChK,kBAAkB,CAAC;SACtC;QAED+H,OAAO,CAACC,GAAG,CAAC,UAAU,EAAEmB,OAAO,CAAC,CAAC,CAAC;QAClC,MAAMvB,QAAQ,SAASoC,MAAI,CAAClL,gBAAgB,CAACnB,aAAa,CAACwL,OAAO,CAAC,CAACrB,SAAS,EAAE;QAC/E,IAAIF,QAAQ,EAAEwB,OAAO,EAAE;UACrBY,MAAI,CAAC3L,OAAO,CAACmF,aAAa,CAAC,SAAS,CAAC;UACrCwB,GAAG,CAACmB,KAAK,EAAE;QACb,CAAC,MAAM;UACL6D,MAAI,CAAC3L,OAAO,CAACiE,YAAY,CAACsF,QAAQ,EAAEvJ,OAAO,IAAI,MAAM,CAAC;QACxD;MACF,CAAC,CAAC,OAAOyK,KAAK,EAAE;QACdkB,MAAI,CAAC3L,OAAO,CAACiE,YAAY,CAAC,SAAS,CAAC;MACtC;IAAC;EACH;EAEA;EACMgI,eAAeA,CAAA;IAAA,IAAAC,MAAA;IAAA,OAAA5C,iBAAA;MACnB,IAAI;QACF,MAAM6C,IAAI,SAA2BD,MAAI,CAACzL,gBAAgB,CAACwL,eAAe,CAACC,MAAI,CAAC3M,YAAY,CAAC5H,GAAG,CAAC,CAAC8R,SAAS,EAAE;QAC7G,IAAI0C,IAAI,EAAE;UACR,MAAMC,GAAG,GAAGC,MAAM,CAACC,GAAG,CAACC,eAAe,CAACJ,IAAI,CAAC;UAC5C,MAAMK,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;UACxCF,IAAI,CAACG,IAAI,GAAGP,GAAG;UACfI,IAAI,CAACI,QAAQ,GAAG,OAAOV,MAAI,CAAC3M,YAAY,CAACnH,UAAU,IAAI8T,MAAI,CAAC3M,YAAY,CAAClH,MAAM,OAAO;UACtFmU,IAAI,CAAClI,KAAK,EAAE;UACZ+H,MAAM,CAACC,GAAG,CAACO,eAAe,CAACT,GAAG,CAAC;QACjC,CAAC,MAAM;UACLF,MAAI,CAAClM,OAAO,CAACiE,YAAY,CAAC,iBAAiB,CAAC;QAC9C;MACF,CAAC,CAAC,OAAOwG,KAAK,EAAE;QACdyB,MAAI,CAAClM,OAAO,CAACiE,YAAY,CAAC,SAAS,CAAC;MACtC;IAAC;EACH;;;uCAvxBWtE,4BAA4B,EAAAxK,EAAA,CAAA2X,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAA7X,EAAA,CAAA2X,iBAAA,CAAAG,EAAA,CAAAC,UAAA,GAAA/X,EAAA,CAAA2X,iBAAA,CAAAK,EAAA,CAAAC,eAAA,GAAAjY,EAAA,CAAA2X,iBAAA,CAAAO,EAAA,CAAAC,cAAA,GAAAnY,EAAA,CAAA2X,iBAAA,CAAAS,EAAA,CAAAC,gBAAA,GAAArY,EAAA,CAAA2X,iBAAA,CAAAW,EAAA,CAAAC,YAAA,GAAAvY,EAAA,CAAA2X,iBAAA,CAAAW,EAAA,CAAAE,oBAAA,GAAAxY,EAAA,CAAA2X,iBAAA,CAAAW,EAAA,CAAAG,gBAAA,GAAAzY,EAAA,CAAA2X,iBAAA,CAAAe,EAAA,CAAAC,aAAA,GAAA3Y,EAAA,CAAA2X,iBAAA,CAAAiB,EAAA,CAAAC,MAAA,GAAA7Y,EAAA,CAAA2X,iBAAA,CAAAmB,EAAA,CAAAC,YAAA,GAAA/Y,EAAA,CAAA2X,iBAAA,CAAAqB,GAAA,CAAAC,cAAA,GAAAjZ,EAAA,CAAA2X,iBAAA,CAAAuB,GAAA,CAAAC,gBAAA;IAAA;EAAA;;;YAA5B3O,4BAA4B;MAAA4O,SAAA;MAAAC,SAAA,WAAAC,mCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;;;;;;;;;;;;;;;UC3DvCvZ,EADF,CAAAC,cAAA,iBAA0B,qBACR;UACdD,EAAA,CAAAsG,SAAA,qBAAiC;UACnCtG,EAAA,CAAAG,YAAA,EAAiB;UAEfH,EADF,CAAAC,cAAA,mBAAc,YACyB;UAAAD,EAAA,CAAAE,MAAA,gRAA6C;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAIjFH,EAHN,CAAAC,cAAA,aAA8B,aACN,cACqC,gBACT;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACxDH,EAAA,CAAAC,cAAA,qBACkD;UADtBD,EAAA,CAAA+D,gBAAA,2BAAA0V,0EAAAxV,MAAA;YAAAjE,EAAA,CAAAkB,aAAA,CAAAwY,GAAA;YAAA1Z,EAAA,CAAAmE,kBAAA,CAAAqV,GAAA,CAAAnX,WAAA,CAAAC,kBAAA,EAAA2B,MAAA,MAAAuV,GAAA,CAAAnX,WAAA,CAAAC,kBAAA,GAAA2B,MAAA;YAAA,OAAAjE,EAAA,CAAAwB,WAAA,CAAAyC,MAAA;UAAA,EAA4C;UACtEjE,EAAA,CAAAgB,UAAA,4BAAA2Y,2EAAA;YAAA3Z,EAAA,CAAAkB,aAAA,CAAAwY,GAAA;YAAA,OAAA1Z,EAAA,CAAAwB,WAAA,CAAkBgY,GAAA,CAAAtI,0BAAA,EAA4B;UAAA,EAAC;UAC/ClR,EAAA,CAAAgC,UAAA,KAAA4X,kDAAA,wBAAoE;UAK1E5Z,EAFI,CAAAG,YAAA,EAAY,EACR,EACF;UAGFH,EAFJ,CAAAC,cAAA,cAAsB,eACqC,iBACX;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACtDH,EAAA,CAAAC,cAAA,qBAAsE;UAA3DD,EAAA,CAAA+D,gBAAA,2BAAA8V,0EAAA5V,MAAA;YAAAjE,EAAA,CAAAkB,aAAA,CAAAwY,GAAA;YAAA1Z,EAAA,CAAAmE,kBAAA,CAAAqV,GAAA,CAAAnX,WAAA,CAAA2D,kBAAA,EAAA/B,MAAA,MAAAuV,GAAA,CAAAnX,WAAA,CAAA2D,kBAAA,GAAA/B,MAAA;YAAA,OAAAjE,EAAA,CAAAwB,WAAA,CAAAyC,MAAA;UAAA,EAA4C;UACrDjE,EAAA,CAAAgC,UAAA,KAAA8X,kDAAA,wBAAgE;UAKtE9Z,EAFI,CAAAG,YAAA,EAAY,EACR,EACF;UAIFH,EAFJ,CAAAC,cAAA,cAAsB,eAC8B,iBACJ;UAAAD,EAAA,CAAAE,MAAA,eAC5C;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAENH,EADF,CAAAC,cAAA,yBAA4B,iBACiE;UAAhCD,EAAA,CAAA+D,gBAAA,2BAAAgW,sEAAA9V,MAAA;YAAAjE,EAAA,CAAAkB,aAAA,CAAAwY,GAAA;YAAA1Z,EAAA,CAAAmE,kBAAA,CAAAqV,GAAA,CAAAnX,WAAA,CAAAwL,KAAA,EAAA5J,MAAA,MAAAuV,GAAA,CAAAnX,WAAA,CAAAwL,KAAA,GAAA5J,MAAA;YAAA,OAAAjE,EAAA,CAAAwB,WAAA,CAAAyC,MAAA;UAAA,EAA+B;UAC5FjE,EADE,CAAAG,YAAA,EAA2F,EAC7E;UAChBH,EAAA,CAAAC,cAAA,iBAA0C;UAAAD,EAAA,CAAAE,MAAA,UAC1C;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAENH,EADF,CAAAC,cAAA,yBAA4B,iBACuD;UAA9BD,EAAA,CAAA+D,gBAAA,2BAAAiW,sEAAA/V,MAAA;YAAAjE,EAAA,CAAAkB,aAAA,CAAAwY,GAAA;YAAA1Z,EAAA,CAAAmE,kBAAA,CAAAqV,GAAA,CAAAnX,WAAA,CAAAyL,GAAA,EAAA7J,MAAA,MAAAuV,GAAA,CAAAnX,WAAA,CAAAyL,GAAA,GAAA7J,MAAA;YAAA,OAAAjE,EAAA,CAAAwB,WAAA,CAAAyC,MAAA;UAAA,EAA6B;UAGtFjE,EAHM,CAAAG,YAAA,EAAiF,EACnE,EACZ,EACF;UAENH,EAAA,CAAAsG,SAAA,cAWM;UAIFtG,EAFJ,CAAAC,cAAA,cAAsB,eACqC,iBACX;UAC1CD,EAAA,CAAAE,MAAA,sBACF;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACRH,EAAA,CAAAC,cAAA,qBAAuF;UAA3DD,EAAA,CAAA+D,gBAAA,2BAAAkW,0EAAAhW,MAAA;YAAAjE,EAAA,CAAAkB,aAAA,CAAAwY,GAAA;YAAA1Z,EAAA,CAAAmE,kBAAA,CAAAqV,GAAA,CAAAnX,WAAA,CAAAmL,kBAAA,EAAAvJ,MAAA,MAAAuV,GAAA,CAAAnX,WAAA,CAAAmL,kBAAA,GAAAvJ,MAAA;YAAA,OAAAjE,EAAA,CAAAwB,WAAA,CAAAyC,MAAA;UAAA,EAA4C;UACtEjE,EAAA,CAAAgC,UAAA,KAAAkY,kDAAA,wBAAgE;UAKtEla,EAFI,CAAAG,YAAA,EAAY,EACR,EACF;UAIFH,EAFJ,CAAAC,cAAA,cAAsB,eACqC,iBACX;UAC1CD,EAAA,CAAAE,MAAA,kCACF;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACRH,EAAA,CAAAC,cAAA,qBAAyF;UAA3DD,EAAA,CAAA+D,gBAAA,2BAAAoW,0EAAAlW,MAAA;YAAAjE,EAAA,CAAAkB,aAAA,CAAAwY,GAAA;YAAA1Z,EAAA,CAAAmE,kBAAA,CAAAqV,GAAA,CAAAnX,WAAA,CAAAgC,kBAAA,EAAAJ,MAAA,MAAAuV,GAAA,CAAAnX,WAAA,CAAAgC,kBAAA,GAAAJ,MAAA;YAAA,OAAAjE,EAAA,CAAAwB,WAAA,CAAAyC,MAAA;UAAA,EAA4C;UACxEjE,EAAA,CAAAgC,UAAA,KAAAoY,kDAAA,wBAAgE;UAKtEpa,EAFI,CAAAG,YAAA,EAAY,EACR,EACF;UAIFH,EAFJ,CAAAC,cAAA,cAAsB,eACqC,iBACZ;UACzCD,EAAA,CAAAE,MAAA,sBACF;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACRH,EAAA,CAAAC,cAAA,qBAAsF;UAA1DD,EAAA,CAAA+D,gBAAA,2BAAAsW,0EAAApW,MAAA;YAAAjE,EAAA,CAAAkB,aAAA,CAAAwY,GAAA;YAAA1Z,EAAA,CAAAmE,kBAAA,CAAAqV,GAAA,CAAAnX,WAAA,CAAAwC,iBAAA,EAAAZ,MAAA,MAAAuV,GAAA,CAAAnX,WAAA,CAAAwC,iBAAA,GAAAZ,MAAA;YAAA,OAAAjE,EAAA,CAAAwB,WAAA,CAAAyC,MAAA;UAAA,EAA2C;UACrEjE,EAAA,CAAAgC,UAAA,KAAAsY,kDAAA,wBAA+D;UAKrEta,EAFI,CAAAG,YAAA,EAAY,EACR,EACF;UAGFH,EAFJ,CAAAC,cAAA,cAAsB,eACqC,iBACd;UACvCD,EAAA,CAAAE,MAAA,sBACF;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACRH,EAAA,CAAAC,cAAA,qBAAqF;UAAzDD,EAAA,CAAA+D,gBAAA,2BAAAwW,0EAAAtW,MAAA;YAAAjE,EAAA,CAAAkB,aAAA,CAAAwY,GAAA;YAAA1Z,EAAA,CAAAmE,kBAAA,CAAAqV,GAAA,CAAAnX,WAAA,CAAAuL,gBAAA,EAAA3J,MAAA,MAAAuV,GAAA,CAAAnX,WAAA,CAAAuL,gBAAA,GAAA3J,MAAA;YAAA,OAAAjE,EAAA,CAAAwB,WAAA,CAAAyC,MAAA;UAAA,EAA0C;UACpEjE,EAAA,CAAAgC,UAAA,KAAAwY,kDAAA,wBAAgE;UAKtExa,EAFI,CAAAG,YAAA,EAAY,EACR,EACF;UAIFH,EAFJ,CAAAC,cAAA,cAAsB,eACqC,iBACV;UAC3CD,EAAA,CAAAE,MAAA,kCACF;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACRH,EAAA,CAAAC,cAAA,qBAA0F;UAA5DD,EAAA,CAAA+D,gBAAA,2BAAA0W,0EAAAxW,MAAA;YAAAjE,EAAA,CAAAkB,aAAA,CAAAwY,GAAA;YAAA1Z,EAAA,CAAAmE,kBAAA,CAAAqV,GAAA,CAAAnX,WAAA,CAAAsL,mBAAA,EAAA1J,MAAA,MAAAuV,GAAA,CAAAnX,WAAA,CAAAsL,mBAAA,GAAA1J,MAAA;YAAA,OAAAjE,EAAA,CAAAwB,WAAA,CAAAyC,MAAA;UAAA,EAA6C;UACzEjE,EAAA,CAAAgC,UAAA,KAAA0Y,kDAAA,wBAAiE;UAKvE1a,EAFI,CAAAG,YAAA,EAAY,EACR,EACF;UAKFH,EAHJ,CAAAC,cAAA,cAAsB,eAC2B,kBAEiB;UAArBD,EAAA,CAAAgB,UAAA,mBAAA2Z,+DAAA;YAAA3a,EAAA,CAAAkB,aAAA,CAAAwY,GAAA;YAAA,OAAA1Z,EAAA,CAAAwB,WAAA,CAASgY,GAAA,CAAAvL,QAAA,EAAU;UAAA,EAAC;UAC3DjO,EAAA,CAAAE,MAAA,sBAAG;UAAAF,EAAA,CAAAsG,SAAA,aAA6B;UAGtCtG,EAFI,CAAAG,YAAA,EAAS,EACL,EACF;UAEJH,EADF,CAAAC,cAAA,eAAuB,eAC0B;UAC7CD,EAAA,CAAAgC,UAAA,KAAA4Y,+CAAA,qBAAmG;UAGnG5a,EAAA,CAAAC,cAAA,kBAAqF;UAA5CD,EAAA,CAAAgB,UAAA,mBAAA6Z,+DAAA;YAAA7a,EAAA,CAAAkB,aAAA,CAAAwY,GAAA;YAAA,OAAA1Z,EAAA,CAAAwB,WAAA,CAASgY,GAAA,CAAA1G,YAAA,CAAa,mBAAmB,CAAC;UAAA,EAAC;UAClF9S,EAAA,CAAAE,MAAA,8CACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAITH,EAAA,CAAAC,cAAA,kBAAiE;UAAxBD,EAAA,CAAAgB,UAAA,mBAAA8Z,+DAAA;YAAA9a,EAAA,CAAAkB,aAAA,CAAAwY,GAAA;YAAA,OAAA1Z,EAAA,CAAAwB,WAAA,CAASgY,GAAA,CAAAhL,WAAA,EAAa;UAAA,EAAC;UAC9DxO,EAAA,CAAAE,MAAA,oDACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACTH,EAAA,CAAAC,cAAA,oBAA4G;UAAzDD,EAAA,CAAAgB,UAAA,oBAAA+Z,+DAAA9W,MAAA;YAAAjE,EAAA,CAAAkB,aAAA,CAAAwY,GAAA;YAAA,OAAA1Z,EAAA,CAAAwB,WAAA,CAAUgY,GAAA,CAAApK,cAAA,CAAAnL,MAAA,CAAsB;UAAA,EAAC;UAApFjE,EAAA,CAAAG,YAAA,EAA4G;UAC5GH,EAAA,CAAAC,cAAA,kBAAiE;UAA7BD,EAAA,CAAAgB,UAAA,mBAAAga,+DAAA;YAAAhb,EAAA,CAAAkB,aAAA,CAAAwY,GAAA;YAAA,OAAA1Z,EAAA,CAAAwB,WAAA,CAASgY,GAAA,CAAAxK,gBAAA,EAAkB;UAAA,EAAC;UAC9DhP,EAAA,CAAAE,MAAA,gEACF;UAGNF,EAHM,CAAAG,YAAA,EAAS,EACL,EACF,EACF;UAOEH,EALR,CAAAC,cAAA,eAAmC,iBAC+D,aACvF,cACgD,cAErB;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACrCH,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACrCH,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACrCH,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACrCH,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACrCH,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAE,MAAA,gCAAI;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACvCH,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAE,MAAA,gCAAI;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACvCH,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACrCH,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAEpCF,EAFoC,CAAAG,YAAA,EAAK,EAClC,EACC;UACRH,EAAA,CAAAC,cAAA,aAAO;UACLD,EAAA,CAAAgC,UAAA,KAAAiZ,2CAAA,mBAAmD;UA6C3Djb,EAHM,CAAAG,YAAA,EAAQ,EACF,EACJ,EACO;UAEbH,EADF,CAAAC,cAAA,2BAAsD,2BAES;UAD7CD,EAAA,CAAA+D,gBAAA,wBAAAmX,6EAAAjX,MAAA;YAAAjE,EAAA,CAAAkB,aAAA,CAAAwY,GAAA;YAAA1Z,EAAA,CAAAmE,kBAAA,CAAAqV,GAAA,CAAA9N,SAAA,EAAAzH,MAAA,MAAAuV,GAAA,CAAA9N,SAAA,GAAAzH,MAAA;YAAA,OAAAjE,EAAA,CAAAwB,WAAA,CAAAyC,MAAA;UAAA,EAAoB;UAClCjE,EAAA,CAAAgB,UAAA,wBAAAka,6EAAAjX,MAAA;YAAAjE,EAAA,CAAAkB,aAAA,CAAAwY,GAAA;YAAA,OAAA1Z,EAAA,CAAAwB,WAAA,CAAcgY,GAAA,CAAAlL,WAAA,CAAArK,MAAA,CAAmB;UAAA,EAAC;UAGxCjE,EAFI,CAAAG,YAAA,EAAiB,EACF,EACT;UAuKVH,EArKA,CAAAgC,UAAA,MAAAmZ,qDAAA,gCAAAnb,EAAA,CAAAob,sBAAA,CAAmE,MAAAC,qDAAA,iCAAArb,EAAA,CAAAob,sBAAA,CAsIF,MAAAE,qDAAA,iCAAAtb,EAAA,CAAAob,sBAAA,CA+BJ;;;UAxXvBpb,EAAA,CAAAM,SAAA,IAA4C;UAA5CN,EAAA,CAAAuE,gBAAA,YAAAiV,GAAA,CAAAnX,WAAA,CAAAC,kBAAA,CAA4C;UAE1CtC,EAAA,CAAAM,SAAA,EAAuB;UAAvBN,EAAA,CAAAI,UAAA,YAAAoZ,GAAA,CAAA7S,oBAAA,CAAuB;UAS1C3G,EAAA,CAAAM,SAAA,GAA4C;UAA5CN,EAAA,CAAAuE,gBAAA,YAAAiV,GAAA,CAAAnX,WAAA,CAAA2D,kBAAA,CAA4C;UACzBhG,EAAA,CAAAM,SAAA,EAAmB;UAAnBN,EAAA,CAAAI,UAAA,YAAAoZ,GAAA,CAAA5S,gBAAA,CAAmB;UAYY5G,EAAA,CAAAM,SAAA,GAA+B;UAA/BN,EAAA,CAAAuE,gBAAA,YAAAiV,GAAA,CAAAnX,WAAA,CAAAwL,KAAA,CAA+B;UAKvC7N,EAAA,CAAAM,SAAA,GAA6B;UAA7BN,EAAA,CAAAuE,gBAAA,YAAAiV,GAAA,CAAAnX,WAAA,CAAAyL,GAAA,CAA6B;UAuBtD9N,EAAA,CAAAM,SAAA,GAA4C;UAA5CN,EAAA,CAAAuE,gBAAA,YAAAiV,GAAA,CAAAnX,WAAA,CAAAmL,kBAAA,CAA4C;UAC1CxN,EAAA,CAAAM,SAAA,EAAmB;UAAnBN,EAAA,CAAAI,UAAA,YAAAoZ,GAAA,CAAAvN,gBAAA,CAAmB;UAYnBjM,EAAA,CAAAM,SAAA,GAA4C;UAA5CN,EAAA,CAAAuE,gBAAA,YAAAiV,GAAA,CAAAnX,WAAA,CAAAgC,kBAAA,CAA4C;UAC5CrE,EAAA,CAAAM,SAAA,EAAmB;UAAnBN,EAAA,CAAAI,UAAA,YAAAoZ,GAAA,CAAA/U,gBAAA,CAAmB;UAYrBzE,EAAA,CAAAM,SAAA,GAA2C;UAA3CN,EAAA,CAAAuE,gBAAA,YAAAiV,GAAA,CAAAnX,WAAA,CAAAwC,iBAAA,CAA2C;UACzC7E,EAAA,CAAAM,SAAA,EAAkB;UAAlBN,EAAA,CAAAI,UAAA,YAAAoZ,GAAA,CAAAzU,eAAA,CAAkB;UAWpB/E,EAAA,CAAAM,SAAA,GAA0C;UAA1CN,EAAA,CAAAuE,gBAAA,YAAAiV,GAAA,CAAAnX,WAAA,CAAAuL,gBAAA,CAA0C;UACxC5N,EAAA,CAAAM,SAAA,EAAmB;UAAnBN,EAAA,CAAAI,UAAA,YAAAoZ,GAAA,CAAAzN,gBAAA,CAAmB;UAYnB/L,EAAA,CAAAM,SAAA,GAA6C;UAA7CN,EAAA,CAAAuE,gBAAA,YAAAiV,GAAA,CAAAnX,WAAA,CAAAsL,mBAAA,CAA6C;UAC7C3N,EAAA,CAAAM,SAAA,EAAoB;UAApBN,EAAA,CAAAI,UAAA,YAAAoZ,GAAA,CAAAtN,iBAAA,CAAoB;UAiBRlM,EAAA,CAAAM,SAAA,GAAc;UAAdN,EAAA,CAAAI,UAAA,SAAAoZ,GAAA,CAAA7R,QAAA,CAAc;UAqCnC3H,EAAA,CAAAM,SAAA,IAAe;UAAfN,EAAA,CAAAI,UAAA,YAAAoZ,GAAA,CAAAxI,SAAA,CAAe;UA+C1BhR,EAAA,CAAAM,SAAA,GAAoB;UAApBN,EAAA,CAAAuE,gBAAA,SAAAiV,GAAA,CAAA9N,SAAA,CAAoB;UAAuB1L,EAAtB,CAAAI,UAAA,aAAAoZ,GAAA,CAAA/N,QAAA,CAAqB,mBAAA+N,GAAA,CAAA7N,YAAA,CAAgC;;;qBD9JlFzM,YAAY,EAAAqc,GAAA,CAAAC,OAAA,EAAAD,GAAA,CAAAE,IAAA,EAAExc,YAAY,EAAAyc,GAAA,CAAAC,oBAAA,EAAAD,GAAA,CAAAE,mBAAA,EAAAF,GAAA,CAAAG,eAAA,EAAAH,GAAA,CAAAI,kBAAA,EAAAJ,GAAA,CAAAK,YAAA,EAAAL,GAAA,CAAAM,YAAA,EAAAN,GAAA,CAAAO,OAAA,EAAAjE,EAAA,CAAAkE,eAAA,EAAAlE,EAAA,CAAAmE,mBAAA,EAAAnE,EAAA,CAAAoE,qBAAA,EAAApE,EAAA,CAAAqE,qBAAA,EAAArE,EAAA,CAAAsE,mBAAA,EAAAtE,EAAA,CAAAuE,gBAAA,EAAAvE,EAAA,CAAAwE,iBAAA,EAAAxE,EAAA,CAAAyE,iBAAA,EAAAzE,EAAA,CAAA0E,oBAAA,EAAA1E,EAAA,CAAA2E,iBAAA,EAAA3E,EAAA,CAAA4E,eAAA,EAAA5E,EAAA,CAAA6E,qBAAA,EAAA7E,EAAA,CAAA8E,qBAAA,EAAAC,GAAA,CAAAC,aAAA,EAAAC,GAAA,CAAAC,mBAAA,EAAAC,GAAA,CAAAC,kBAAA,EAAEje,kBAAkB,EAAEI,mBAAmB;MAAA8d,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}