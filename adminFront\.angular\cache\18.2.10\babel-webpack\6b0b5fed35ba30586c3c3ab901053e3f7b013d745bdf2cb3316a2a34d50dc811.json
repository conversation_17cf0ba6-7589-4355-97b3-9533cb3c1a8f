{"ast": null, "code": "import toDate from \"../toDate/index.js\";\nimport { getRoundingMethod } from \"../_lib/roundingMethods/index.js\";\nimport toInteger from \"../_lib/toInteger/index.js\";\n/**\n * @name roundToNearestMinutes\n * @category Minute Helpers\n * @summary Rounds the given date to the nearest minute\n *\n * @description\n * Rounds the given date to the nearest minute (or number of minutes).\n * Rounds up when the given date is exactly between the nearest round minutes.\n *\n * @param {Date|Number} date - the date to round\n * @param {Object} [options] - an object with options.\n * @param {Number} [options.nearestTo=1] - nearest number of minutes to round to. E.g. `15` to round to quarter hours.\n * @param {String} [options.roundingMethod='trunc'] - a rounding method (`ceil`, `floor`, `round` or `trunc`)\n * @returns {Date} the new date rounded to the closest minute\n * @throws {TypeError} 1 argument required\n * @throws {RangeError} `options.nearestTo` must be between 1 and 30\n *\n * @example\n * // Round 10 July 2014 12:12:34 to nearest minute:\n * const result = roundToNearestMinutes(new Date(2014, 6, 10, 12, 12, 34))\n * //=> Thu Jul 10 2014 12:13:00\n *\n * @example\n * // Round 10 July 2014 12:07:30 to nearest quarter hour:\n * const result = roundToNearestMinutes(new Date(2014, 6, 10, 12, 12, 34), { nearestTo: 15 })\n * // rounds up because given date is exactly between 12:00:00 and 12:15:00\n * //=> Thu Jul 10 2014 12:15:00\n */\nexport default function roundToNearestMinutes(dirtyDate, options) {\n  var _options$nearestTo;\n  if (arguments.length < 1) {\n    throw new TypeError('1 argument required, but only none provided present');\n  }\n  var nearestTo = toInteger((_options$nearestTo = options === null || options === void 0 ? void 0 : options.nearestTo) !== null && _options$nearestTo !== void 0 ? _options$nearestTo : 1);\n  if (nearestTo < 1 || nearestTo > 30) {\n    throw new RangeError('`options.nearestTo` must be between 1 and 30');\n  }\n  var date = toDate(dirtyDate);\n  var seconds = date.getSeconds(); // relevant if nearestTo is 1, which is the default case\n  var minutes = date.getMinutes() + seconds / 60;\n  var roundingMethod = getRoundingMethod(options === null || options === void 0 ? void 0 : options.roundingMethod);\n  var roundedMinutes = roundingMethod(minutes / nearestTo) * nearestTo;\n  var remainderMinutes = minutes % nearestTo;\n  var addedMinutes = Math.round(remainderMinutes / nearestTo) * nearestTo;\n  return new Date(date.getFullYear(), date.getMonth(), date.getDate(), date.getHours(), roundedMinutes + addedMinutes);\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}