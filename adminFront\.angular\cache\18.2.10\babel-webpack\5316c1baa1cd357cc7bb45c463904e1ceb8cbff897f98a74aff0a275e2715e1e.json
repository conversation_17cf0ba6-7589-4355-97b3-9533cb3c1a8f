{"ast": null, "code": "import { of as observableOf } from 'rxjs';\nimport { UserData } from '../data/users';\nimport * as i0 from \"@angular/core\";\nexport class UserService extends UserData {\n  constructor() {\n    super(...arguments);\n    this.time = new Date();\n    this.users = {\n      nick: {\n        name: '<PERSON>',\n        picture: 'assets/images/nick.png'\n      },\n      eva: {\n        name: '<PERSON>',\n        picture: 'assets/images/eva.png'\n      },\n      jack: {\n        name: '<PERSON>',\n        picture: 'assets/images/jack.png'\n      },\n      lee: {\n        name: '<PERSON>',\n        picture: 'assets/images/lee.png'\n      },\n      alan: {\n        name: '<PERSON>',\n        picture: 'assets/images/alan.png'\n      },\n      kate: {\n        name: '<PERSON>',\n        picture: 'assets/images/kate.png'\n      }\n    };\n    this.types = {\n      mobile: 'mobile',\n      home: 'home',\n      work: 'work'\n    };\n    this.contacts = [{\n      user: this.users.nick,\n      type: this.types.mobile\n    }, {\n      user: this.users.eva,\n      type: this.types.home\n    }, {\n      user: this.users.jack,\n      type: this.types.mobile\n    }, {\n      user: this.users.lee,\n      type: this.types.mobile\n    }, {\n      user: this.users.alan,\n      type: this.types.home\n    }, {\n      user: this.users.kate,\n      type: this.types.work\n    }];\n    this.recentUsers = [{\n      user: this.users.alan,\n      type: this.types.home,\n      time: this.time.setHours(21, 12)\n    }, {\n      user: this.users.eva,\n      type: this.types.home,\n      time: this.time.setHours(17, 45)\n    }, {\n      user: this.users.nick,\n      type: this.types.mobile,\n      time: this.time.setHours(5, 29)\n    }, {\n      user: this.users.lee,\n      type: this.types.mobile,\n      time: this.time.setHours(11, 24)\n    }, {\n      user: this.users.jack,\n      type: this.types.mobile,\n      time: this.time.setHours(10, 45)\n    }, {\n      user: this.users.kate,\n      type: this.types.work,\n      time: this.time.setHours(9, 42)\n    }, {\n      user: this.users.kate,\n      type: this.types.work,\n      time: this.time.setHours(9, 31)\n    }, {\n      user: this.users.jack,\n      type: this.types.mobile,\n      time: this.time.setHours(8, 0)\n    }];\n  }\n  getUsers() {\n    return observableOf(this.users);\n  }\n  getContacts() {\n    return observableOf(this.contacts);\n  }\n  getRecentUsers() {\n    return observableOf(this.recentUsers);\n  }\n  static {\n    this.ɵfac = /*@__PURE__*/(() => {\n      let ɵUserService_BaseFactory;\n      return function UserService_Factory(__ngFactoryType__) {\n        return (ɵUserService_BaseFactory || (ɵUserService_BaseFactory = i0.ɵɵgetInheritedFactory(UserService)))(__ngFactoryType__ || UserService);\n      };\n    })();\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: UserService,\n      factory: UserService.ɵfac\n    });\n  }\n}", "map": {"version": 3, "names": ["of", "observableOf", "UserData", "UserService", "constructor", "time", "Date", "users", "nick", "name", "picture", "eva", "jack", "lee", "alan", "kate", "types", "mobile", "home", "work", "contacts", "user", "type", "recentUsers", "setHours", "getUsers", "getContacts", "getRecentUsers", "__ngFactoryType__", "factory", "ɵfac"], "sources": ["C:\\Users\\<USER>\\Documents\\jeansalechange\\adminFront\\src\\app\\@core\\mock\\users.service.ts"], "sourcesContent": ["import { of as observableOf,  Observable } from 'rxjs';\r\nimport { Injectable } from '@angular/core';\r\nimport { Contacts, RecentUsers, UserData } from '../data/users';\r\n\r\n@Injectable()\r\nexport class UserService extends UserData {\r\n\r\n  private time: Date = new Date;\r\n\r\n  private users = {\r\n    nick: { name: '<PERSON>', picture: 'assets/images/nick.png' },\r\n    eva: { name: '<PERSON>', picture: 'assets/images/eva.png' },\r\n    jack: { name: '<PERSON>', picture: 'assets/images/jack.png' },\r\n    lee: { name: '<PERSON>', picture: 'assets/images/lee.png' },\r\n    alan: { name: '<PERSON>', picture: 'assets/images/alan.png' },\r\n    kate: { name: '<PERSON>', picture: 'assets/images/kate.png' },\r\n  };\r\n  private types = {\r\n    mobile: 'mobile',\r\n    home: 'home',\r\n    work: 'work',\r\n  };\r\n  private contacts: Contacts[] = [\r\n    { user: this.users.nick, type: this.types.mobile },\r\n    { user: this.users.eva, type: this.types.home },\r\n    { user: this.users.jack, type: this.types.mobile },\r\n    { user: this.users.lee, type: this.types.mobile },\r\n    { user: this.users.alan, type: this.types.home },\r\n    { user: this.users.kate, type: this.types.work },\r\n  ];\r\n  private recentUsers: RecentUsers[]  = [\r\n    { user: this.users.alan, type: this.types.home, time: this.time.setHours(21, 12)},\r\n    { user: this.users.eva, type: this.types.home, time: this.time.setHours(17, 45)},\r\n    { user: this.users.nick, type: this.types.mobile, time: this.time.setHours(5, 29)},\r\n    { user: this.users.lee, type: this.types.mobile, time: this.time.setHours(11, 24)},\r\n    { user: this.users.jack, type: this.types.mobile, time: this.time.setHours(10, 45)},\r\n    { user: this.users.kate, type: this.types.work, time: this.time.setHours(9, 42)},\r\n    { user: this.users.kate, type: this.types.work, time: this.time.setHours(9, 31)},\r\n    { user: this.users.jack, type: this.types.mobile, time: this.time.setHours(8, 0)},\r\n  ];\r\n\r\n  getUsers(): Observable<any> {\r\n    return observableOf(this.users);\r\n  }\r\n\r\n  getContacts(): Observable<Contacts[]> {\r\n    return observableOf(this.contacts);\r\n  }\r\n\r\n  getRecentUsers(): Observable<RecentUsers[]> {\r\n    return observableOf(this.recentUsers);\r\n  }\r\n}\r\n"], "mappings": "AAAA,SAASA,EAAE,IAAIC,YAAY,QAAqB,MAAM;AAEtD,SAAgCC,QAAQ,QAAQ,eAAe;;AAG/D,OAAM,MAAOC,WAAY,SAAQD,QAAQ;EADzCE,YAAA;;IAGU,KAAAC,IAAI,GAAS,IAAIC,IAAI,CAAJ,CAAI;IAErB,KAAAC,KAAK,GAAG;MACdC,IAAI,EAAE;QAAEC,IAAI,EAAE,YAAY;QAAEC,OAAO,EAAE;MAAwB,CAAE;MAC/DC,GAAG,EAAE;QAAEF,IAAI,EAAE,UAAU;QAAEC,OAAO,EAAE;MAAuB,CAAE;MAC3DE,IAAI,EAAE;QAAEH,IAAI,EAAE,eAAe;QAAEC,OAAO,EAAE;MAAwB,CAAE;MAClEG,GAAG,EAAE;QAAEJ,IAAI,EAAE,UAAU;QAAEC,OAAO,EAAE;MAAuB,CAAE;MAC3DI,IAAI,EAAE;QAAEL,IAAI,EAAE,eAAe;QAAEC,OAAO,EAAE;MAAwB,CAAE;MAClEK,IAAI,EAAE;QAAEN,IAAI,EAAE,eAAe;QAAEC,OAAO,EAAE;MAAwB;KACjE;IACO,KAAAM,KAAK,GAAG;MACdC,MAAM,EAAE,QAAQ;MAChBC,IAAI,EAAE,MAAM;MACZC,IAAI,EAAE;KACP;IACO,KAAAC,QAAQ,GAAe,CAC7B;MAAEC,IAAI,EAAE,IAAI,CAACd,KAAK,CAACC,IAAI;MAAEc,IAAI,EAAE,IAAI,CAACN,KAAK,CAACC;IAAM,CAAE,EAClD;MAAEI,IAAI,EAAE,IAAI,CAACd,KAAK,CAACI,GAAG;MAAEW,IAAI,EAAE,IAAI,CAACN,KAAK,CAACE;IAAI,CAAE,EAC/C;MAAEG,IAAI,EAAE,IAAI,CAACd,KAAK,CAACK,IAAI;MAAEU,IAAI,EAAE,IAAI,CAACN,KAAK,CAACC;IAAM,CAAE,EAClD;MAAEI,IAAI,EAAE,IAAI,CAACd,KAAK,CAACM,GAAG;MAAES,IAAI,EAAE,IAAI,CAACN,KAAK,CAACC;IAAM,CAAE,EACjD;MAAEI,IAAI,EAAE,IAAI,CAACd,KAAK,CAACO,IAAI;MAAEQ,IAAI,EAAE,IAAI,CAACN,KAAK,CAACE;IAAI,CAAE,EAChD;MAAEG,IAAI,EAAE,IAAI,CAACd,KAAK,CAACQ,IAAI;MAAEO,IAAI,EAAE,IAAI,CAACN,KAAK,CAACG;IAAI,CAAE,CACjD;IACO,KAAAI,WAAW,GAAmB,CACpC;MAAEF,IAAI,EAAE,IAAI,CAACd,KAAK,CAACO,IAAI;MAAEQ,IAAI,EAAE,IAAI,CAACN,KAAK,CAACE,IAAI;MAAEb,IAAI,EAAE,IAAI,CAACA,IAAI,CAACmB,QAAQ,CAAC,EAAE,EAAE,EAAE;IAAC,CAAC,EACjF;MAAEH,IAAI,EAAE,IAAI,CAACd,KAAK,CAACI,GAAG;MAAEW,IAAI,EAAE,IAAI,CAACN,KAAK,CAACE,IAAI;MAAEb,IAAI,EAAE,IAAI,CAACA,IAAI,CAACmB,QAAQ,CAAC,EAAE,EAAE,EAAE;IAAC,CAAC,EAChF;MAAEH,IAAI,EAAE,IAAI,CAACd,KAAK,CAACC,IAAI;MAAEc,IAAI,EAAE,IAAI,CAACN,KAAK,CAACC,MAAM;MAAEZ,IAAI,EAAE,IAAI,CAACA,IAAI,CAACmB,QAAQ,CAAC,CAAC,EAAE,EAAE;IAAC,CAAC,EAClF;MAAEH,IAAI,EAAE,IAAI,CAACd,KAAK,CAACM,GAAG;MAAES,IAAI,EAAE,IAAI,CAACN,KAAK,CAACC,MAAM;MAAEZ,IAAI,EAAE,IAAI,CAACA,IAAI,CAACmB,QAAQ,CAAC,EAAE,EAAE,EAAE;IAAC,CAAC,EAClF;MAAEH,IAAI,EAAE,IAAI,CAACd,KAAK,CAACK,IAAI;MAAEU,IAAI,EAAE,IAAI,CAACN,KAAK,CAACC,MAAM;MAAEZ,IAAI,EAAE,IAAI,CAACA,IAAI,CAACmB,QAAQ,CAAC,EAAE,EAAE,EAAE;IAAC,CAAC,EACnF;MAAEH,IAAI,EAAE,IAAI,CAACd,KAAK,CAACQ,IAAI;MAAEO,IAAI,EAAE,IAAI,CAACN,KAAK,CAACG,IAAI;MAAEd,IAAI,EAAE,IAAI,CAACA,IAAI,CAACmB,QAAQ,CAAC,CAAC,EAAE,EAAE;IAAC,CAAC,EAChF;MAAEH,IAAI,EAAE,IAAI,CAACd,KAAK,CAACQ,IAAI;MAAEO,IAAI,EAAE,IAAI,CAACN,KAAK,CAACG,IAAI;MAAEd,IAAI,EAAE,IAAI,CAACA,IAAI,CAACmB,QAAQ,CAAC,CAAC,EAAE,EAAE;IAAC,CAAC,EAChF;MAAEH,IAAI,EAAE,IAAI,CAACd,KAAK,CAACK,IAAI;MAAEU,IAAI,EAAE,IAAI,CAACN,KAAK,CAACC,MAAM;MAAEZ,IAAI,EAAE,IAAI,CAACA,IAAI,CAACmB,QAAQ,CAAC,CAAC,EAAE,CAAC;IAAC,CAAC,CAClF;;EAEDC,QAAQA,CAAA;IACN,OAAOxB,YAAY,CAAC,IAAI,CAACM,KAAK,CAAC;EACjC;EAEAmB,WAAWA,CAAA;IACT,OAAOzB,YAAY,CAAC,IAAI,CAACmB,QAAQ,CAAC;EACpC;EAEAO,cAAcA,CAAA;IACZ,OAAO1B,YAAY,CAAC,IAAI,CAACsB,WAAW,CAAC;EACvC;;;;;iGA9CWpB,WAAW,IAAAyB,iBAAA,IAAXzB,WAAW;MAAA;IAAA;EAAA;;;aAAXA,WAAW;MAAA0B,OAAA,EAAX1B,WAAW,CAAA2B;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}