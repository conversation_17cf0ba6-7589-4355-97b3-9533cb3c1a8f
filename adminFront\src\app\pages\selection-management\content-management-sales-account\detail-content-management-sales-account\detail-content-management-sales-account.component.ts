import { Component, OnInit } from '@angular/core';
import { CommonModule, Location } from '@angular/common';
import { NbCheckboxModule } from '@nebular/theme';
import { AllowHelper } from 'src/app/shared/helper/allowHelper';
import { FormItemService, MaterialService, RegularNoticeFileService } from 'src/services/api/services';
import { ActivatedRoute } from '@angular/router';
import { MessageService } from 'src/app/shared/services/message.service';
import { tap } from 'rxjs';
import { CreateListFormItem, FileViewModel, GetListFormItemRes, SaveListFormItemReq } from 'src/services/api/models';
import { BaseFilePipe } from 'src/app/@theme/pipes/base-file.pipe';
import { UtilityService } from 'src/app/shared/services/utility.service';
import { ValidationHelper } from 'src/app/shared/helper/validationHelper';
import { SharedModule } from 'src/app/pages/components/shared.module';
import { BaseComponent } from 'src/app/pages/components/base/baseComponent';
import { EventService, EEvent } from 'src/app/shared/services/event.service';


export interface ExtendedSaveListFormItemReq {
  CDesignFileUrl?: string | null;
  CFirstMatrialUrl?: string | null;
  CFile?: FileViewModel,
  CFormItemHouseHold?: Array<string> | null;
  CFormItemId?: number;
  CItemName?: string;
  CFormID?: number;
  CPart?: string | null;
  CName?: string | null;
  CLocation?: string | null;
  CRemarkType?: string | null;
  CTotalAnswer?: number;
  CRequireAnswer?: number;
  CUiType?: number;
  selectedCUiType: any | null;
  selectedItems: { [key: string]: boolean }
  selectedRemarkType?: { [key: string]: boolean }
  allSelected: boolean,
  listPictures: any[],
}

@Component({
  selector: 'ngx-detail-content-management-sales-account',
  templateUrl: './detail-content-management-sales-account.component.html',
  styleUrls: ['./detail-content-management-sales-account.component.scss'],
  standalone: true,
  imports: [CommonModule, SharedModule, NbCheckboxModule, BaseFilePipe],
})

export class DetailContentManagementSalesAccountComponent extends BaseComponent implements OnInit {
  constructor(
    private _allow: AllowHelper,
    private route: ActivatedRoute,
    private message: MessageService,
    private _formItemService: FormItemService,
    private _regularNoticeFileService: RegularNoticeFileService,
    private _utilityService: UtilityService,
    private valid: ValidationHelper,
    private location: Location,
    private _materialService: MaterialService,
    private _eventService: EventService
  ) {
    super(_allow)
  }

  typeContentManagementSalesAccount = {
    CFormType: 2,
    CNoticeType: 2
  }

  CUiTypeOptions: any[] = [
    {
      value: 1, label: '建材選色'
    },
    {
      value: 2, label: '群組選樣_選色'
    }, {
      value: 3, label: '建材選樣'
    }
  ]
  CRemarkTypeOptions = ["正常", "留料"]
  buildCaseId: number

  override ngOnInit(): void {
    this.route.paramMap.subscribe(params => {
      if (params) {
        const idParam = params.get('id');
        const id = idParam ? +idParam : 0;
        this.buildCaseId = id
        if (this.buildCaseId) {
          this.getListRegularNoticeFileHouseHold()
        }
      }
    })
  }


  getItemByValue(value: any, options: any[]) {
    for (const item of options) {
      if (item.value === value) {
        return item;
      }
    }
    return null;
  }


  selectedItems: { [key: string]: boolean } = {};
  selectedRemarkType: { [key: string]: boolean } = {};



  detectFiles(event: any, formItemReq_: any) {
    const file = event.target.files[0]
    if (file) {
      let reader = new FileReader();
      reader.readAsDataURL(file);
      reader.onload = () => {
        let base64Str: string = reader.result as string;
        if (!base64Str) {
          return;
        }
        if (formItemReq_.listPictures.length > 0) {
          formItemReq_.listPictures[0] = {
            id: new Date().getTime(),
            name: file.name.split('.')[0],
            data: base64Str,
            extension: this._utilityService.getFileExtension(file.name),
            CFile: file
          };
        } else {
          formItemReq_.listPictures.push({
            id: new Date().getTime(),
            name: file.name.split('.')[0],
            data: base64Str,
            extension: this._utilityService.getFileExtension(file.name),
            CFile: file
          });
        }
        event.target.value = null;
      };
    }
  }

  removeImage(pictureId: number, formItemReq_: any) {
    if (formItemReq_.listPictures.length) {
      formItemReq_.listPictures = formItemReq_.listPictures.filter((x: any) => x.id != pictureId)
    }
  }

  renameFile(event: any, index: number, formItemReq_: any) {
    var blob = formItemReq_.listPictures[index].CFile.slice(0, formItemReq_.listPictures[index].CFile.size, formItemReq_.listPictures[index].CFile.type);
    var newFile = new File([blob], `${event.target.value + '.' + formItemReq_.listPictures[index].extension}`, { type: formItemReq_.listPictures[index].CFile.type });
    formItemReq_.listPictures[index].CFile = newFile
  }


  onCheckAllChange(checked: boolean, formItemReq_: any) {
    formItemReq_.allSelected = checked;
    this.houseHoldList.forEach(item => {
      formItemReq_.selectedItems[item] = checked;
    });
  }

  onCheckboxHouseHoldListChange(checked: boolean, item: string, formItemReq_: any) {
    if (checked) {
      formItemReq_.selectedItems[item] = checked;
      formItemReq_.allSelected = this.houseHoldList.every(item => formItemReq_.selectedItems[item] == checked);
    } else {
      formItemReq_.allSelected = false
    }
  }



  onCheckboxRemarkChange(checked: boolean, item: string, formItemReq_: any) {
    formItemReq_.selectedRemarkType[item] = checked;
  }

  createRemarkObject(CRemarkTypeOptions: string[], CRemarkType: string): { [key: string]: boolean } {
    const remarkObject: { [key: string]: boolean } = {};
    for (const option of CRemarkTypeOptions) {
      remarkObject[option] = false;
    }
    const remarkTypes = CRemarkType.split('-');
    for (const type of remarkTypes) {
      if (CRemarkTypeOptions.includes(type)) {
        remarkObject[type] = true;
      }
    }
    return remarkObject;
  }

  mergeItems(items: ExtendedSaveListFormItemReq[]): ExtendedSaveListFormItemReq[] {
    const map = new Map<string, { item: ExtendedSaveListFormItemReq, count: number }>();

    items.forEach(item => {
      const key = `${item.CLocation}_${item.CName}_${item.CPart}`;
      if (map.has(key)) {
        const existing = map.get(key)!;
        existing.count += 1;
      } else {
        map.set(key, { item: { ...item }, count: 1 });
      }
    });

    return Array.from(map.values()).map(({ item, count }) => ({
      ...item,
      CTotalAnswer: count
    }));
  }


  getMaterialList() { // call when create
    this._materialService.apiMaterialGetMaterialListPost$Json({
      body: {
        CBuildCaseId: this.buildCaseId,
        CPagi: false
      }
    }).pipe(
      tap(res => {
        if (res.Entries && res.StatusCode == 0) {

          this.houseHoldList.forEach(item => this.selectedItems[item] = false)

          this.CRemarkTypeOptions.forEach(item => this.selectedRemarkType[item] = false)

          this.arrListFormItemReq = res.Entries.map((o: any) => {
            return {
              CDesignFileUrl: null,
              CFormItemHouseHold: null,
              CFormId: null,
              CLocation: o.CLocation,
              CName: o.CName,
              CPart: o.CPart,
              CItemName: `${o.CName}-${o.CPart}-${o.CLocation}`,
              CRemarkType: null,
              CTotalAnswer: 0,
              CRequireAnswer: 1,
              CUiType: 0,
              selectedItems: {},
              selectedRemarkType: this.selectedRemarkType,
              allSelected: false,
              listPictures: [],
              selectedCUiType: this.CUiTypeOptions[0],
              CFirstMatrialUrl: o.CPicture,
            }
          })
          this.arrListFormItemReq = [...this.mergeItems(this.arrListFormItemReq)]
        }
      })
    ).subscribe();
  }

  listFormItem: GetListFormItemRes
  isNew: boolean = true

  getListFormItem() {
    this._formItemService.apiFormItemGetListFormItemPost$Json({
      body: {
        CBuildCaseId: this.buildCaseId,
        CFormType: this.typeContentManagementSalesAccount.CFormType,
        CIsPaging: false
      }
    }).pipe(
      tap(res => {
        if (res.Entries && res.StatusCode == 0) {
          this.listFormItem = res.Entries
          this.isNew = res.Entries.formItems ? false : true
          if (res.Entries.formItems) {

            this.houseHoldList.forEach(item => this.selectedItems[item] = false)
            this.CRemarkTypeOptions.forEach(item => this.selectedRemarkType[item] = false)

            this.arrListFormItemReq = res.Entries.formItems.map((o: any) => {
              return {
                CFormId: this.listFormItem.CFormId,
                CDesignFileUrl: o.CDesignFileUrl,
                CFirstMatrialUrl: o.CFirstMatrialUrl,
                CFile: o.CFile,
                CFormItemHouseHold: o.CFormItemHouseHold,
                CFormItemId: o.CFormItemId,
                CLocation: o.CLocation,
                CName: o.CName,
                CPart: o.CPart,
                CItemName: o.CItemName ? o.CItemName : `${o.CName}-${o.CPart}-${o.CLocation}`,
                CRemarkType: o.CRemarkType,
                CTotalAnswer: o.CTotalAnswer,
                CRequireAnswer: o.CUiType===3 ? 1: o.CRequireAnswer,
                CUiType: o.CUiType,
                selectedItems: o.tblFormItemHouseholds.length ? this.createArrayObjectFromArray(this.houseHoldList, o.tblFormItemHouseholds) : { ...this.selectedItems },
                selectedRemarkType: o.CRemarkType ? this.createRemarkObject(this.CRemarkTypeOptions, o.CRemarkType) : { ...this.selectedRemarkType },
                allSelected: o.tblFormItemHouseholds.length === this.houseHoldList.length,
                listPictures: [],
                selectedCUiType: o.CUiType ? this.getItemByValue(o.CUiType, this.CUiTypeOptions) : this.CUiTypeOptions[0]
              }
            })
          } else {
            this.getMaterialList()
          }
        }
      })
    ).subscribe()
  }

  changeSelectCUiType(formItemReq: any) {
    if(formItemReq.selectedCUiType && formItemReq.selectedCUiType.value ===3) {
      formItemReq.CRequireAnswer = 1
    }
  }
  getHouseHoldListByNoticeType(data: any[]) {
    for (let item of data) {
      if (item.CNoticeType === this.typeContentManagementSalesAccount.CNoticeType) {
        return item.CHouseHoldList;
      }
    }
    return [];
  }

  arrListFormItemReq: ExtendedSaveListFormItemReq[]

  getKeysWithTrueValue(obj: Record<string, boolean>): string[] { // {"key": true } => ["key"]
    return Object.keys(obj).filter(key => obj[key]);
  }

  getKeysWithTrueValueJoined(obj: Record<string, boolean>): string {
    return Object.keys(obj)
      .filter(key => obj[key])
      .join('-');
  }

  getCRemarkType(selectedCUiType: any, selectedRemarkType: any): any {
    if (selectedCUiType && selectedCUiType.value == 3) {
      return this.getKeysWithTrueValueJoined(selectedRemarkType)
    }
  }

  getStringAfterComma(inputString: string): string {
    const parts = inputString.split(',');
    if (parts.length > 1) {
      return parts[1];
    } else return ""
  }

  formatFile(listPictures: any) {
    if (listPictures && listPictures.length > 0) {
      return {
        Base64String: this.getStringAfterComma(listPictures[0].data) || null,
        FileExtension: listPictures[0].extension || null,
        FileName: listPictures[0].CFile.name || listPictures[0].name || null,
      }
    } else return undefined

  }


  validation() {
    this.valid.clear();
    let hasInvalidCUiType = false;
    let hasInvalidCRequireAnswer = false;
    let hasInvalidItemName = false;

    for (const item of this.saveListFormItemReq) {
      if (!hasInvalidCUiType && (!item.CUiType)) {
        hasInvalidCUiType = true;
      }
      if (!hasInvalidCRequireAnswer && (!item.CRequireAnswer)) {
        hasInvalidCRequireAnswer = true;
      }
      if (item.CTotalAnswer && item.CRequireAnswer) {
        if (item.CRequireAnswer > item.CTotalAnswer || item.CRequireAnswer < 0) {
          this.valid.addErrorMessage('[必填數量]' + ' <= ' + item.CTotalAnswer + ` (${item.CItemName}) `);
        }
      }

      if (!hasInvalidItemName && (!item.CItemName)) {
        hasInvalidItemName = true;
      }
    }
    if (hasInvalidCUiType) {
      this.valid.addErrorMessage('[前台UI類型]' + ' 必填');
    }
    if (hasInvalidCRequireAnswer) {
      this.valid.addErrorMessage('[必填數量]' + ' 必填且>0');
    }
    if (hasInvalidItemName) {
      this.valid.addErrorMessage('[廚房-廚具-櫃體]' + ' 必填');
    }
  }

  saveListFormItemReq: SaveListFormItemReq[]

  onSubmit() {
    this.saveListFormItemReq = this.arrListFormItemReq.map((e: any) => {
      return {
        CDesignFileUrl: e.CDesignFileUrl ? e.CDesignFileUrl : null,
        CFile: e.listPictures ? this.formatFile(e.listPictures) : undefined,
        CFormItemHouseHold: this.getKeysWithTrueValue(e.selectedItems),
        CFormItemId: e.CFormItemId ? e.CFormItemId : null,
        CFormID: this.isNew ? null : this.listFormItem.CFormId,
        CName: e.CName,
        CPart: e.CPart,
        CLocation: e.CLocation,
        CItemName: e.CItemName, //? e.CItemName : `${e.CName}-${e.CPart}-${e.CLocation}`,
        CRemarkType: e.selectedCUiType.value !== 3 ? null : this.getCRemarkType(e.selectedCUiType, e.selectedRemarkType) || null,
        CTotalAnswer: e.CTotalAnswer,
        CRequireAnswer: e.CRequireAnswer,
        CUiType: e.selectedCUiType.value,
      }
    })
    this.validation()
    if (this.valid.errorMessages.length > 0) {
      this.message.showErrorMSGs(this.valid.errorMessages);
      return
    }
    if (this.isNew) {
      this.createListFormItem()

    } else {
      this.saveListFormItem()
    }
  }

  saveListFormItem() {
    this._formItemService.apiFormItemSaveListFormItemPost$Json({
      body: this.saveListFormItemReq
    }).subscribe(res => {
      if (res.StatusCode == 0) {
        this.message.showSucessMSG("執行成功");
        // this.getListFormItem()
        this.goBack()
      }
    })
  }

  creatListFormItem: CreateListFormItem

  createListFormItem() {
    this.creatListFormItem = {
      CBuildCaseId: this.buildCaseId,
      CFormItem: this.saveListFormItemReq || null,
      CFormType: this.typeContentManagementSalesAccount.CFormType,
    }

    this._formItemService.apiFormItemCreateListFormItemPost$Json({
      body: this.creatListFormItem
    }).subscribe(res => {
      if (res.StatusCode == 0) {
        this.message.showSucessMSG("執行成功");
        // this.getListFormItem()
        this.goBack()
      }
    })
  }

  createArrayObjectFromArray(a: string[], b: { CFormItemHouseholdId: number, CFormItemId: number, CHousehold: string, CIsSelect: boolean }[]): { [key: string]: boolean } {
    const c: { [key: string]: boolean } = {};
    for (const item of a) {
      const matchingItem = b.find(bItem => bItem.CHousehold === item && bItem.CIsSelect);
      c[item] = !!matchingItem;
    }
    return c;
  } //["House1", "House2", "House3"] => [{CHousehold: "House1", CIsSelect: true,... }, ... ]


  houseHoldList: any[]

  getListRegularNoticeFileHouseHold() {
    this._regularNoticeFileService.apiRegularNoticeFileGetListRegularNoticeFileHouseHoldPost$Json({
      body: this.buildCaseId
    }).pipe(
      tap(res => {
        if (res.Entries && res.StatusCode == 0) {
          this.houseHoldList = this.getHouseHoldListByNoticeType(res.Entries)
          this.getListFormItem()
        }
      })
    ).subscribe()
  }
  goBack() {
    this._eventService.push({
      action: EEvent.GET_BUILDCASE,
      payload: this.buildCaseId
    })
    this.location.back()
  }
}
