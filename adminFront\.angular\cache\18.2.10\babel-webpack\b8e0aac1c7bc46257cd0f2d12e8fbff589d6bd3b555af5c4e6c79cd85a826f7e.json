{"ast": null, "code": ";\n(function (root, factory, undef) {\n  if (typeof exports === \"object\") {\n    // CommonJS\n    module.exports = exports = factory(require(\"./core\"), require(\"./cipher-core\"));\n  } else if (typeof define === \"function\" && define.amd) {\n    // AMD\n    define([\"./core\", \"./cipher-core\"], factory);\n  } else {\n    // Global (browser)\n    factory(root.CryptoJS);\n  }\n})(this, function (CryptoJS) {\n  /**\n   * Electronic Codebook block mode.\n   */\n  CryptoJS.mode.ECB = function () {\n    var ECB = CryptoJS.lib.BlockCipherMode.extend();\n    ECB.Encryptor = ECB.extend({\n      processBlock: function (words, offset) {\n        this._cipher.encryptBlock(words, offset);\n      }\n    });\n    ECB.Decryptor = ECB.extend({\n      processBlock: function (words, offset) {\n        this._cipher.decryptBlock(words, offset);\n      }\n    });\n    return ECB;\n  }();\n  return CryptoJS.mode.ECB;\n});", "map": {"version": 3, "names": ["root", "factory", "undef", "exports", "module", "require", "define", "amd", "CryptoJS", "mode", "ECB", "lib", "BlockCipherMode", "extend", "Encryptor", "processBlock", "words", "offset", "_cipher", "encryptBlock", "Decryptor", "decryptBlock"], "sources": ["C:/Users/<USER>/Documents/salechange-product/adminFront/node_modules/crypto-js/mode-ecb.js"], "sourcesContent": [";(function (root, factory, undef) {\n\tif (typeof exports === \"object\") {\n\t\t// CommonJS\n\t\tmodule.exports = exports = factory(require(\"./core\"), require(\"./cipher-core\"));\n\t}\n\telse if (typeof define === \"function\" && define.amd) {\n\t\t// AMD\n\t\tdefine([\"./core\", \"./cipher-core\"], factory);\n\t}\n\telse {\n\t\t// Global (browser)\n\t\tfactory(root.CryptoJS);\n\t}\n}(this, function (CryptoJS) {\n\n\t/**\n\t * Electronic Codebook block mode.\n\t */\n\tCryptoJS.mode.ECB = (function () {\n\t    var ECB = CryptoJS.lib.BlockCipherMode.extend();\n\n\t    ECB.Encryptor = ECB.extend({\n\t        processBlock: function (words, offset) {\n\t            this._cipher.encryptBlock(words, offset);\n\t        }\n\t    });\n\n\t    ECB.Decryptor = ECB.extend({\n\t        processBlock: function (words, offset) {\n\t            this._cipher.decryptBlock(words, offset);\n\t        }\n\t    });\n\n\t    return ECB;\n\t}());\n\n\n\treturn CryptoJS.mode.ECB;\n\n}));"], "mappings": "AAAA;AAAE,WAAUA,IAAI,EAAEC,OAAO,EAAEC,KAAK,EAAE;EACjC,IAAI,OAAOC,OAAO,KAAK,QAAQ,EAAE;IAChC;IACAC,MAAM,CAACD,OAAO,GAAGA,OAAO,GAAGF,OAAO,CAACI,OAAO,CAAC,QAAQ,CAAC,EAAEA,OAAO,CAAC,eAAe,CAAC,CAAC;EAChF,CAAC,MACI,IAAI,OAAOC,MAAM,KAAK,UAAU,IAAIA,MAAM,CAACC,GAAG,EAAE;IACpD;IACAD,MAAM,CAAC,CAAC,QAAQ,EAAE,eAAe,CAAC,EAAEL,OAAO,CAAC;EAC7C,CAAC,MACI;IACJ;IACAA,OAAO,CAACD,IAAI,CAACQ,QAAQ,CAAC;EACvB;AACD,CAAC,EAAC,IAAI,EAAE,UAAUA,QAAQ,EAAE;EAE3B;AACD;AACA;EACCA,QAAQ,CAACC,IAAI,CAACC,GAAG,GAAI,YAAY;IAC7B,IAAIA,GAAG,GAAGF,QAAQ,CAACG,GAAG,CAACC,eAAe,CAACC,MAAM,CAAC,CAAC;IAE/CH,GAAG,CAACI,SAAS,GAAGJ,GAAG,CAACG,MAAM,CAAC;MACvBE,YAAY,EAAE,SAAAA,CAAUC,KAAK,EAAEC,MAAM,EAAE;QACnC,IAAI,CAACC,OAAO,CAACC,YAAY,CAACH,KAAK,EAAEC,MAAM,CAAC;MAC5C;IACJ,CAAC,CAAC;IAEFP,GAAG,CAACU,SAAS,GAAGV,GAAG,CAACG,MAAM,CAAC;MACvBE,YAAY,EAAE,SAAAA,CAAUC,KAAK,EAAEC,MAAM,EAAE;QACnC,IAAI,CAACC,OAAO,CAACG,YAAY,CAACL,KAAK,EAAEC,MAAM,CAAC;MAC5C;IACJ,CAAC,CAAC;IAEF,OAAOP,GAAG;EACd,CAAC,CAAC,CAAE;EAGJ,OAAOF,QAAQ,CAACC,IAAI,CAACC,GAAG;AAEzB,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}