{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { NbCheckboxModule } from '@nebular/theme';\nimport { tap } from 'rxjs';\nimport { SharedModule } from 'src/app/pages/components/shared.module';\nimport { BaseComponent } from 'src/app/pages/components/base/baseComponent';\nimport { EEvent } from 'src/app/shared/services/event.service';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/shared/helper/allowHelper\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"src/app/shared/services/message.service\";\nimport * as i4 from \"src/services/api/services\";\nimport * as i5 from \"src/app/shared/services/utility.service\";\nimport * as i6 from \"src/app/shared/helper/validationHelper\";\nimport * as i7 from \"@angular/common\";\nimport * as i8 from \"src/app/shared/services/event.service\";\nimport * as i9 from \"@angular/forms\";\nimport * as i10 from \"@nebular/theme\";\nimport * as i11 from \"../../../components/breadcrumb/breadcrumb.component\";\nimport * as i12 from \"../../../../@theme/pipes/base-file.pipe\";\nfunction DetailContentManagementSalesAccountComponent_ng_container_8_div_3_img_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 39);\n    i0.ɵɵpipe(1, \"addBaseFile\");\n  }\n  if (rf & 2) {\n    const formItemReq_r2 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵproperty(\"src\", i0.ɵɵpipeBind1(1, 1, formItemReq_r2 == null ? null : formItemReq_r2.CFirstMatrialUrl), i0.ɵɵsanitizeUrl);\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_8_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 37)(1, \"div\", 29)(2, \"div\", 13);\n    i0.ɵɵtemplate(3, DetailContentManagementSalesAccountComponent_ng_container_8_div_3_img_3_Template, 2, 3, \"img\", 38);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const formItemReq_r2 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", formItemReq_r2 == null ? null : formItemReq_r2.CFirstMatrialUrl);\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_8_nb_option_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 40);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const case_r4 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", case_r4);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", case_r4.label, \" \");\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_8_ng_container_33_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"tr\")(2, \"td\", 41)(3, \"label\");\n    i0.ɵɵtext(4, \"\\u6587\\u4EF6\\u540D \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(5, \" \\u00A0 \");\n    i0.ɵɵelementStart(6, \"input\", 42);\n    i0.ɵɵlistener(\"blur\", function DetailContentManagementSalesAccountComponent_ng_container_8_ng_container_33_Template_input_blur_6_listener($event) {\n      const i_r7 = i0.ɵɵrestoreView(_r6).index;\n      const formItemReq_r2 = i0.ɵɵnextContext().$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.renameFile($event, i_r7, formItemReq_r2));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"button\", 43);\n    i0.ɵɵlistener(\"click\", function DetailContentManagementSalesAccountComponent_ng_container_8_ng_container_33_Template_button_click_7_listener() {\n      const picture_r8 = i0.ɵɵrestoreView(_r6).$implicit;\n      const formItemReq_r2 = i0.ɵɵnextContext().$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.removeImage(picture_r8.id, formItemReq_r2));\n    });\n    i0.ɵɵtext(8, \"\\u5220\\u9664\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"td\");\n    i0.ɵɵelement(10, \"img\", 44);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const picture_r8 = ctx.$implicit;\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"value\", picture_r8.name);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"src\", picture_r8.data, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_8_div_34_img_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 48);\n    i0.ɵɵpipe(1, \"addBaseFile\");\n  }\n  if (rf & 2) {\n    const formItemReq_r2 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵproperty(\"src\", i0.ɵɵpipeBind1(1, 1, formItemReq_r2.CDesignFileUrl), i0.ɵɵsanitizeUrl);\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_8_div_34_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 45)(1, \"div\", 29)(2, \"div\", 46);\n    i0.ɵɵtemplate(3, DetailContentManagementSalesAccountComponent_ng_container_8_div_34_img_3_Template, 2, 3, \"img\", 47);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const formItemReq_r2 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵproperty(\"ngClass\", formItemReq_r2.listPictures.length ? \"hidden\" : \"\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", formItemReq_r2.CDesignFileUrl);\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_8_label_40_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"label\", 49)(1, \"nb-checkbox\", 50);\n    i0.ɵɵtwoWayListener(\"checkedChange\", function DetailContentManagementSalesAccountComponent_ng_container_8_label_40_Template_nb_checkbox_checkedChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r9);\n      const formItemReq_r2 = i0.ɵɵnextContext().$implicit;\n      i0.ɵɵtwoWayBindingSet(formItemReq_r2.allSelected, $event) || (formItemReq_r2.allSelected = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"checkedChange\", function DetailContentManagementSalesAccountComponent_ng_container_8_label_40_Template_nb_checkbox_checkedChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r9);\n      const formItemReq_r2 = i0.ɵɵnextContext().$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onCheckAllChange($event, formItemReq_r2));\n    });\n    i0.ɵɵtext(2, \" \\u5168\\u9078 \");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const formItemReq_r2 = i0.ɵɵnextContext().$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"checked\", formItemReq_r2.allSelected);\n    i0.ɵɵproperty(\"disabled\", ctx_r2.listFormItem.CIsLock);\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_8_label_41_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"label\", 49)(1, \"nb-checkbox\", 51);\n    i0.ɵɵtwoWayListener(\"checkedChange\", function DetailContentManagementSalesAccountComponent_ng_container_8_label_41_Template_nb_checkbox_checkedChange_1_listener($event) {\n      const item_r11 = i0.ɵɵrestoreView(_r10).$implicit;\n      const formItemReq_r2 = i0.ɵɵnextContext().$implicit;\n      i0.ɵɵtwoWayBindingSet(formItemReq_r2.selectedItems[item_r11], $event) || (formItemReq_r2.selectedItems[item_r11] = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"checkedChange\", function DetailContentManagementSalesAccountComponent_ng_container_8_label_41_Template_nb_checkbox_checkedChange_1_listener($event) {\n      const item_r11 = i0.ɵɵrestoreView(_r10).$implicit;\n      const formItemReq_r2 = i0.ɵɵnextContext().$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onCheckboxHouseHoldListChange($event, item_r11, formItemReq_r2));\n    });\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const item_r11 = ctx.$implicit;\n    const formItemReq_r2 = i0.ɵɵnextContext().$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"checked\", formItemReq_r2.selectedItems[item_r11]);\n    i0.ɵɵproperty(\"disabled\", ctx_r2.listFormItem.CIsLock);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", item_r11, \" \");\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_8_div_42_label_5_nb_checkbox_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-checkbox\", 51);\n    i0.ɵɵtwoWayListener(\"checkedChange\", function DetailContentManagementSalesAccountComponent_ng_container_8_div_42_label_5_nb_checkbox_1_Template_nb_checkbox_checkedChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r12);\n      const remark_r13 = i0.ɵɵnextContext().$implicit;\n      const formItemReq_r2 = i0.ɵɵnextContext(2).$implicit;\n      i0.ɵɵtwoWayBindingSet(formItemReq_r2.selectedRemarkType[remark_r13], $event) || (formItemReq_r2.selectedRemarkType[remark_r13] = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"checkedChange\", function DetailContentManagementSalesAccountComponent_ng_container_8_div_42_label_5_nb_checkbox_1_Template_nb_checkbox_checkedChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r12);\n      const remark_r13 = i0.ɵɵnextContext().$implicit;\n      const formItemReq_r2 = i0.ɵɵnextContext(2).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onCheckboxRemarkChange($event, remark_r13, formItemReq_r2));\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const remark_r13 = i0.ɵɵnextContext().$implicit;\n    const formItemReq_r2 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵtwoWayProperty(\"checked\", formItemReq_r2.selectedRemarkType[remark_r13]);\n    i0.ɵɵproperty(\"disabled\", ctx_r2.listFormItem.CIsLock);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", remark_r13, \" \");\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_8_div_42_label_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"label\", 49);\n    i0.ɵɵtemplate(1, DetailContentManagementSalesAccountComponent_ng_container_8_div_42_label_5_nb_checkbox_1_Template, 2, 3, \"nb-checkbox\", 52);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const formItemReq_r2 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", formItemReq_r2.selectedRemarkType);\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_8_div_42_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 9)(1, \"div\", 23)(2, \"label\", 32);\n    i0.ɵɵtext(3, \"\\u5099\\u8A3B\\u9078\\u9805\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"div\", 33);\n    i0.ɵɵtemplate(5, DetailContentManagementSalesAccountComponent_ng_container_8_div_42_label_5_Template, 2, 1, \"label\", 35);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.CRemarkTypeOptions);\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 9)(2, \"div\", 10);\n    i0.ɵɵtemplate(3, DetailContentManagementSalesAccountComponent_ng_container_8_div_3_Template, 4, 1, \"div\", 11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 12)(5, \"div\", 13)(6, \"div\", 14)(7, \"label\", 15);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"div\", 16)(10, \"input\", 17);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function DetailContentManagementSalesAccountComponent_ng_container_8_Template_input_ngModelChange_10_listener($event) {\n      const formItemReq_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      i0.ɵɵtwoWayBindingSet(formItemReq_r2.CItemName, $event) || (formItemReq_r2.CItemName = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(11, \"div\", 13)(12, \"div\", 14)(13, \"label\", 18);\n    i0.ɵɵtext(14, \"\\u5FC5\\u586B\\u6578\\u91CF\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"input\", 19);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function DetailContentManagementSalesAccountComponent_ng_container_8_Template_input_ngModelChange_15_listener($event) {\n      const formItemReq_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      i0.ɵɵtwoWayBindingSet(formItemReq_r2.CRequireAnswer, $event) || (formItemReq_r2.CRequireAnswer = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(16, \"div\", 13)(17, \"div\", 14)(18, \"label\", 20);\n    i0.ɵɵtext(19, \"\\u524D\\u53F0UI\\u985E\\u578B\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"nb-select\", 21);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function DetailContentManagementSalesAccountComponent_ng_container_8_Template_nb_select_ngModelChange_20_listener($event) {\n      const formItemReq_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      i0.ɵɵtwoWayBindingSet(formItemReq_r2.selectedCUiType, $event) || (formItemReq_r2.selectedCUiType = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"selectedChange\", function DetailContentManagementSalesAccountComponent_ng_container_8_Template_nb_select_selectedChange_20_listener() {\n      const formItemReq_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.changeSelectCUiType(formItemReq_r2));\n    });\n    i0.ɵɵtemplate(21, DetailContentManagementSalesAccountComponent_ng_container_8_nb_option_21_Template, 2, 2, \"nb-option\", 22);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(22, \"div\", 23)(23, \"div\", 24)(24, \"div\", 25)(25, \"button\", 26);\n    i0.ɵɵlistener(\"click\", function DetailContentManagementSalesAccountComponent_ng_container_8_Template_button_click_25_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const inputFile_r5 = i0.ɵɵreference(29);\n      return i0.ɵɵresetView(inputFile_r5.click());\n    });\n    i0.ɵɵtext(26, \"\\u4E0A\\u50B3\\u6982\\u5FF5\\u8A2D\\u8A08\\u5716\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(27, \"div\", 27)(28, \"input\", 28, 0);\n    i0.ɵɵlistener(\"change\", function DetailContentManagementSalesAccountComponent_ng_container_8_Template_input_change_28_listener($event) {\n      const formItemReq_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.detectFiles($event, formItemReq_r2));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(30, \"div\", 29)(31, \"table\", 30)(32, \"tbody\");\n    i0.ɵɵtemplate(33, DetailContentManagementSalesAccountComponent_ng_container_8_ng_container_33_Template, 11, 2, \"ng-container\", 5);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵtemplate(34, DetailContentManagementSalesAccountComponent_ng_container_8_div_34_Template, 4, 2, \"div\", 31);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(35, \"div\", 9)(36, \"div\", 23)(37, \"label\", 32);\n    i0.ɵɵtext(38, \"\\u9069\\u7528\\u6236\\u5225\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(39, \"div\", 33);\n    i0.ɵɵtemplate(40, DetailContentManagementSalesAccountComponent_ng_container_8_label_40_Template, 3, 2, \"label\", 34)(41, DetailContentManagementSalesAccountComponent_ng_container_8_label_41_Template, 3, 3, \"label\", 35);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(42, DetailContentManagementSalesAccountComponent_ng_container_8_div_42_Template, 6, 1, \"div\", 36);\n    i0.ɵɵelement(43, \"hr\");\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    let tmp_7_0;\n    let tmp_9_0;\n    let tmp_11_0;\n    const formItemReq_r2 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", formItemReq_r2.CFirstMatrialUrl);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate3(\" \", formItemReq_r2.CName, \"-\", formItemReq_r2.CPart, \"-\", formItemReq_r2.CLocation, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtwoWayProperty(\"ngModel\", formItemReq_r2.CItemName);\n    i0.ɵɵproperty(\"disabled\", (tmp_7_0 = ctx_r2.listFormItem.CIsLock) !== null && tmp_7_0 !== undefined ? tmp_7_0 : false);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtwoWayProperty(\"ngModel\", formItemReq_r2.CRequireAnswer);\n    i0.ɵɵproperty(\"disabled\", formItemReq_r2.selectedCUiType.value === 3 || ((tmp_9_0 = ctx_r2.listFormItem.CIsLock) !== null && tmp_9_0 !== undefined ? tmp_9_0 : false));\n    i0.ɵɵadvance(5);\n    i0.ɵɵtwoWayProperty(\"ngModel\", formItemReq_r2.selectedCUiType);\n    i0.ɵɵproperty(\"disabled\", (tmp_11_0 = ctx_r2.listFormItem.CIsLock) !== null && tmp_11_0 !== undefined ? tmp_11_0 : false);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.CUiTypeOptions);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"disabled\", ctx_r2.listFormItem.CIsLock);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", formItemReq_r2.listPictures.length ? \"\" : \"hidden\");\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngForOf\", formItemReq_r2.listPictures);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", formItemReq_r2.CDesignFileUrl);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.houseHoldList.length);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.houseHoldList);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", formItemReq_r2.selectedCUiType.value === 3);\n  }\n}\nexport class DetailContentManagementSalesAccountComponent extends BaseComponent {\n  constructor(_allow, route, message, _formItemService, _regularNoticeFileService, _utilityService, valid, location, _materialService, _eventService) {\n    super(_allow);\n    this._allow = _allow;\n    this.route = route;\n    this.message = message;\n    this._formItemService = _formItemService;\n    this._regularNoticeFileService = _regularNoticeFileService;\n    this._utilityService = _utilityService;\n    this.valid = valid;\n    this.location = location;\n    this._materialService = _materialService;\n    this._eventService = _eventService;\n    this.typeContentManagementSalesAccount = {\n      CFormType: 2,\n      CNoticeType: 2\n    };\n    this.CUiTypeOptions = [{\n      value: 1,\n      label: '建材選色'\n    }, {\n      value: 2,\n      label: '群組選樣_選色'\n    }, {\n      value: 3,\n      label: '建材選樣'\n    }];\n    this.CRemarkTypeOptions = [\"正常\", \"留料\"];\n    this.selectedItems = {};\n    this.selectedRemarkType = {};\n    this.isNew = true;\n  }\n  ngOnInit() {\n    this.route.paramMap.subscribe(params => {\n      if (params) {\n        const idParam = params.get('id');\n        const id = idParam ? +idParam : 0;\n        this.buildCaseId = id;\n        if (this.buildCaseId) {\n          this.getListRegularNoticeFileHouseHold();\n        }\n      }\n    });\n  }\n  getItemByValue(value, options) {\n    for (const item of options) {\n      if (item.value === value) {\n        return item;\n      }\n    }\n    return null;\n  }\n  detectFiles(event, formItemReq_) {\n    const file = event.target.files[0];\n    if (file) {\n      let reader = new FileReader();\n      reader.readAsDataURL(file);\n      reader.onload = () => {\n        let base64Str = reader.result;\n        if (!base64Str) {\n          return;\n        }\n        if (formItemReq_.listPictures.length > 0) {\n          formItemReq_.listPictures[0] = {\n            id: new Date().getTime(),\n            name: file.name.split('.')[0],\n            data: base64Str,\n            extension: this._utilityService.getFileExtension(file.name),\n            CFile: file\n          };\n        } else {\n          formItemReq_.listPictures.push({\n            id: new Date().getTime(),\n            name: file.name.split('.')[0],\n            data: base64Str,\n            extension: this._utilityService.getFileExtension(file.name),\n            CFile: file\n          });\n        }\n        event.target.value = null;\n      };\n    }\n  }\n  removeImage(pictureId, formItemReq_) {\n    if (formItemReq_.listPictures.length) {\n      formItemReq_.listPictures = formItemReq_.listPictures.filter(x => x.id != pictureId);\n    }\n  }\n  renameFile(event, index, formItemReq_) {\n    var blob = formItemReq_.listPictures[index].CFile.slice(0, formItemReq_.listPictures[index].CFile.size, formItemReq_.listPictures[index].CFile.type);\n    var newFile = new File([blob], `${event.target.value + '.' + formItemReq_.listPictures[index].extension}`, {\n      type: formItemReq_.listPictures[index].CFile.type\n    });\n    formItemReq_.listPictures[index].CFile = newFile;\n  }\n  onCheckAllChange(checked, formItemReq_) {\n    formItemReq_.allSelected = checked;\n    this.houseHoldList.forEach(item => {\n      formItemReq_.selectedItems[item] = checked;\n    });\n  }\n  onCheckboxHouseHoldListChange(checked, item, formItemReq_) {\n    if (checked) {\n      formItemReq_.selectedItems[item] = checked;\n      formItemReq_.allSelected = this.houseHoldList.every(item => formItemReq_.selectedItems[item] == checked);\n    } else {\n      formItemReq_.allSelected = false;\n    }\n  }\n  onCheckboxRemarkChange(checked, item, formItemReq_) {\n    formItemReq_.selectedRemarkType[item] = checked;\n  }\n  createRemarkObject(CRemarkTypeOptions, CRemarkType) {\n    const remarkObject = {};\n    for (const option of CRemarkTypeOptions) {\n      remarkObject[option] = false;\n    }\n    const remarkTypes = CRemarkType.split('-');\n    for (const type of remarkTypes) {\n      if (CRemarkTypeOptions.includes(type)) {\n        remarkObject[type] = true;\n      }\n    }\n    return remarkObject;\n  }\n  mergeItems(items) {\n    const map = new Map();\n    items.forEach(item => {\n      const key = `${item.CLocation}_${item.CName}_${item.CPart}`;\n      if (map.has(key)) {\n        const existing = map.get(key);\n        existing.count += 1;\n      } else {\n        map.set(key, {\n          item: {\n            ...item\n          },\n          count: 1\n        });\n      }\n    });\n    return Array.from(map.values()).map(({\n      item,\n      count\n    }) => ({\n      ...item,\n      CTotalAnswer: count\n    }));\n  }\n  getMaterialList() {\n    this._materialService.apiMaterialGetMaterialListPost$Json({\n      body: {\n        CBuildCaseId: this.buildCaseId,\n        CPagi: false\n      }\n    }).pipe(tap(res => {\n      if (res.Entries && res.StatusCode == 0) {\n        this.houseHoldList.forEach(item => this.selectedItems[item] = false);\n        this.CRemarkTypeOptions.forEach(item => this.selectedRemarkType[item] = false);\n        this.arrListFormItemReq = res.Entries.map(o => {\n          return {\n            CDesignFileUrl: null,\n            CFormItemHouseHold: null,\n            CFormId: null,\n            CLocation: o.CLocation,\n            CName: o.CName,\n            CPart: o.CPart,\n            CItemName: `${o.CName}-${o.CPart}-${o.CLocation}`,\n            CRemarkType: null,\n            CTotalAnswer: 0,\n            CRequireAnswer: 1,\n            CUiType: 0,\n            selectedItems: {},\n            selectedRemarkType: this.selectedRemarkType,\n            allSelected: false,\n            listPictures: [],\n            selectedCUiType: this.CUiTypeOptions[0],\n            CFirstMatrialUrl: o.CPicture\n          };\n        });\n        this.arrListFormItemReq = [...this.mergeItems(this.arrListFormItemReq)];\n      }\n    })).subscribe();\n  }\n  getListFormItem() {\n    this._formItemService.apiFormItemGetListFormItemPost$Json({\n      body: {\n        CBuildCaseId: this.buildCaseId,\n        CFormType: this.typeContentManagementSalesAccount.CFormType,\n        CIsPaging: false\n      }\n    }).pipe(tap(res => {\n      if (res.Entries && res.StatusCode == 0) {\n        this.listFormItem = res.Entries;\n        this.isNew = res.Entries.formItems ? false : true;\n        if (res.Entries.formItems) {\n          this.houseHoldList.forEach(item => this.selectedItems[item] = false);\n          this.CRemarkTypeOptions.forEach(item => this.selectedRemarkType[item] = false);\n          this.arrListFormItemReq = res.Entries.formItems.map(o => {\n            return {\n              CFormId: this.listFormItem.CFormId,\n              CDesignFileUrl: o.CDesignFileUrl,\n              CFirstMatrialUrl: o.CFirstMatrialUrl,\n              CFile: o.CFile,\n              CFormItemHouseHold: o.CFormItemHouseHold,\n              CFormItemId: o.CFormItemId,\n              CLocation: o.CLocation,\n              CName: o.CName,\n              CPart: o.CPart,\n              CItemName: o.CItemName ? o.CItemName : `${o.CName}-${o.CPart}-${o.CLocation}`,\n              CRemarkType: o.CRemarkType,\n              CTotalAnswer: o.CTotalAnswer,\n              CRequireAnswer: o.CUiType === 3 ? 1 : o.CRequireAnswer,\n              CUiType: o.CUiType,\n              selectedItems: o.tblFormItemHouseholds.length ? this.createArrayObjectFromArray(this.houseHoldList, o.tblFormItemHouseholds) : {\n                ...this.selectedItems\n              },\n              selectedRemarkType: o.CRemarkType ? this.createRemarkObject(this.CRemarkTypeOptions, o.CRemarkType) : {\n                ...this.selectedRemarkType\n              },\n              allSelected: o.tblFormItemHouseholds.length === this.houseHoldList.length,\n              listPictures: [],\n              selectedCUiType: o.CUiType ? this.getItemByValue(o.CUiType, this.CUiTypeOptions) : this.CUiTypeOptions[0]\n            };\n          });\n        } else {\n          this.getMaterialList();\n        }\n      }\n    })).subscribe();\n  }\n  changeSelectCUiType(formItemReq) {\n    if (formItemReq.selectedCUiType && formItemReq.selectedCUiType.value === 3) {\n      formItemReq.CRequireAnswer = 1;\n    }\n  }\n  getHouseHoldListByNoticeType(data) {\n    for (let item of data) {\n      if (item.CNoticeType === this.typeContentManagementSalesAccount.CNoticeType) {\n        return item.CHouseHoldList;\n      }\n    }\n    return [];\n  }\n  getKeysWithTrueValue(obj) {\n    return Object.keys(obj).filter(key => obj[key]);\n  }\n  getKeysWithTrueValueJoined(obj) {\n    return Object.keys(obj).filter(key => obj[key]).join('-');\n  }\n  getCRemarkType(selectedCUiType, selectedRemarkType) {\n    if (selectedCUiType && selectedCUiType.value == 3) {\n      return this.getKeysWithTrueValueJoined(selectedRemarkType);\n    }\n  }\n  getStringAfterComma(inputString) {\n    const parts = inputString.split(',');\n    if (parts.length > 1) {\n      return parts[1];\n    } else return \"\";\n  }\n  formatFile(listPictures) {\n    if (listPictures && listPictures.length > 0) {\n      return {\n        Base64String: this.getStringAfterComma(listPictures[0].data) || null,\n        FileExtension: listPictures[0].extension || null,\n        FileName: listPictures[0].CFile.name || listPictures[0].name || null\n      };\n    } else return undefined;\n  }\n  validation() {\n    this.valid.clear();\n    let hasInvalidCUiType = false;\n    let hasInvalidCRequireAnswer = false;\n    let hasInvalidItemName = false;\n    for (const item of this.saveListFormItemReq) {\n      if (!hasInvalidCUiType && !item.CUiType) {\n        hasInvalidCUiType = true;\n      }\n      if (!hasInvalidCRequireAnswer && !item.CRequireAnswer) {\n        hasInvalidCRequireAnswer = true;\n      }\n      if (item.CTotalAnswer && item.CRequireAnswer) {\n        if (item.CRequireAnswer > item.CTotalAnswer || item.CRequireAnswer < 0) {\n          this.valid.addErrorMessage('[必填數量]' + ' <= ' + item.CTotalAnswer + ` (${item.CItemName}) `);\n        }\n      }\n      if (!hasInvalidItemName && !item.CItemName) {\n        hasInvalidItemName = true;\n      }\n    }\n    if (hasInvalidCUiType) {\n      this.valid.addErrorMessage('[前台UI類型]' + ' 必填');\n    }\n    if (hasInvalidCRequireAnswer) {\n      this.valid.addErrorMessage('[必填數量]' + ' 必填且>0');\n    }\n    if (hasInvalidItemName) {\n      this.valid.addErrorMessage('[廚房-廚具-櫃體]' + ' 必填');\n    }\n  }\n  onSubmit() {\n    this.saveListFormItemReq = this.arrListFormItemReq.map(e => {\n      return {\n        CDesignFileUrl: e.CDesignFileUrl ? e.CDesignFileUrl : null,\n        CFile: e.listPictures ? this.formatFile(e.listPictures) : undefined,\n        CFormItemHouseHold: this.getKeysWithTrueValue(e.selectedItems),\n        CFormItemId: e.CFormItemId ? e.CFormItemId : null,\n        CFormID: this.isNew ? null : this.listFormItem.CFormId,\n        CName: e.CName,\n        CPart: e.CPart,\n        CLocation: e.CLocation,\n        CItemName: e.CItemName,\n        //? e.CItemName : `${e.CName}-${e.CPart}-${e.CLocation}`,\n        CRemarkType: e.selectedCUiType.value !== 3 ? null : this.getCRemarkType(e.selectedCUiType, e.selectedRemarkType) || null,\n        CTotalAnswer: e.CTotalAnswer,\n        CRequireAnswer: e.CRequireAnswer,\n        CUiType: e.selectedCUiType.value\n      };\n    });\n    this.validation();\n    if (this.valid.errorMessages.length > 0) {\n      this.message.showErrorMSGs(this.valid.errorMessages);\n      return;\n    }\n    if (this.isNew) {\n      this.createListFormItem();\n    } else {\n      this.saveListFormItem();\n    }\n  }\n  saveListFormItem() {\n    this._formItemService.apiFormItemSaveListFormItemPost$Json({\n      body: this.saveListFormItemReq\n    }).subscribe(res => {\n      if (res.StatusCode == 0) {\n        this.message.showSucessMSG(\"執行成功\");\n        // this.getListFormItem()\n        this.goBack();\n      }\n    });\n  }\n  createListFormItem() {\n    this.creatListFormItem = {\n      CBuildCaseId: this.buildCaseId,\n      CFormItem: this.saveListFormItemReq || null,\n      CFormType: this.typeContentManagementSalesAccount.CFormType\n    };\n    this._formItemService.apiFormItemCreateListFormItemPost$Json({\n      body: this.creatListFormItem\n    }).subscribe(res => {\n      if (res.StatusCode == 0) {\n        this.message.showSucessMSG(\"執行成功\");\n        // this.getListFormItem()\n        this.goBack();\n      }\n    });\n  }\n  createArrayObjectFromArray(a, b) {\n    const c = {};\n    for (const item of a) {\n      const matchingItem = b.find(bItem => bItem.CHousehold === item && bItem.CIsSelect);\n      c[item] = !!matchingItem;\n    }\n    return c;\n  } //[\"House1\", \"House2\", \"House3\"] => [{CHousehold: \"House1\", CIsSelect: true,... }, ... ]\n  getListRegularNoticeFileHouseHold() {\n    this._regularNoticeFileService.apiRegularNoticeFileGetListRegularNoticeFileHouseHoldPost$Json({\n      body: this.buildCaseId\n    }).pipe(tap(res => {\n      if (res.Entries && res.StatusCode == 0) {\n        this.houseHoldList = this.getHouseHoldListByNoticeType(res.Entries);\n        this.getListFormItem();\n      }\n    })).subscribe();\n  }\n  goBack() {\n    this._eventService.push({\n      action: \"GET_BUILDCASE\" /* EEvent.GET_BUILDCASE */,\n      payload: this.buildCaseId\n    });\n    this.location.back();\n  }\n  static {\n    this.ɵfac = function DetailContentManagementSalesAccountComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || DetailContentManagementSalesAccountComponent)(i0.ɵɵdirectiveInject(i1.AllowHelper), i0.ɵɵdirectiveInject(i2.ActivatedRoute), i0.ɵɵdirectiveInject(i3.MessageService), i0.ɵɵdirectiveInject(i4.FormItemService), i0.ɵɵdirectiveInject(i4.RegularNoticeFileService), i0.ɵɵdirectiveInject(i5.UtilityService), i0.ɵɵdirectiveInject(i6.ValidationHelper), i0.ɵɵdirectiveInject(i7.Location), i0.ɵɵdirectiveInject(i4.MaterialService), i0.ɵɵdirectiveInject(i8.EventService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: DetailContentManagementSalesAccountComponent,\n      selectors: [[\"ngx-detail-content-management-sales-account\"]],\n      standalone: true,\n      features: [i0.ɵɵInheritDefinitionFeature, i0.ɵɵStandaloneFeature],\n      decls: 14,\n      vars: 1,\n      consts: [[\"inputFile\", \"\"], [\"accent\", \"success\"], [1, \"font-bold\", \"text-[#818181]\"], [1, \"d-flex\", \"flex-wrap\"], [1, \"font-bold\", \"text-lg\", \"pb-3\"], [4, \"ngFor\", \"ngForOf\"], [1, \"d-flex\", \"justify-content-center\"], [1, \"btn\", \"btn-secondary\", \"mx-2\", 3, \"click\"], [1, \"btn\", \"btn-info\", 3, \"click\"], [1, \"flex\", \"flex-wrap\", \"w-full\"], [1, \"w-full\", \"md:w-1/4\", \"px-2\", \"pb-2\"], [\"class\", \"flex flex-col items-center\", 4, \"ngIf\"], [1, \"w-full\", \"md:w-1/2\", \"px-2\"], [1, \"w-full\"], [1, \"form-group\", \"flex\", \"items-center\", \"w-full\"], [\"for\", \"CItemName\", 1, \"label\", \"w-1/2\", \"text-base\"], [1, \"input-group\", \"items-center\", \"w-1/2\", \"px-0\"], [\"type\", \"text\", \"nbInput\", \"\", \"placeholder\", \"\\u5EDA\\u623F\", 1, \"w-full\", 3, \"ngModelChange\", \"ngModel\", \"disabled\"], [\"for\", \"cRequireAnswer\", 1, \"label\", \"w-1/2\"], [\"type\", \"number\", \"nbInput\", \"\", \"placeholder\", \"\\u5FC5\\u586B\\u6578\\u91CF\", 1, \"w-1/2\", 3, \"ngModelChange\", \"ngModel\", \"disabled\"], [\"for\", \"buildingName\", 1, \"label\", \"w-1/2\"], [\"placeholder\", \"\\u5EFA\\u6848\", 1, \"w-1/2\", \"px-0\", 3, \"ngModelChange\", \"selectedChange\", \"ngModel\", \"disabled\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [1, \"w-full\", \"md:w-1/4\", \"px-2\"], [1, \"w-full\", \"flex\", \"flex-col\"], [1, \"flex\", \"justify-end\", \"w-full\"], [1, \"btn\", \"btn-info\", \"h-fit\", 3, \"click\", \"disabled\"], [1, \"w-full\", 3, \"ngClass\"], [\"type\", \"file\", \"accept\", \"image/png, image/gif, image/jpeg\", 1, \"hidden\", 3, \"change\"], [1, \"mt-3\", \"w-full\", \"flex\", \"flex-col\"], [1, \"table\", \"table-striped\", \"border\", 2, \"background-color\", \"#f3f3f3\"], [\"class\", \"w-full text-center\", 3, \"ngClass\", 4, \"ngIf\"], [\"for\", \"buildingName\", 1, \"label\"], [1, \"w-full\", \"md:w-3/4\", \"px-2\"], [\"class\", \"mr-2\", 4, \"ngIf\"], [\"class\", \"mr-2\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"flex flex-wrap w-full\", 4, \"ngIf\"], [1, \"flex\", \"flex-col\", \"items-center\"], [\"class\", \"h-[140px] w-[300px]\", 3, \"src\", 4, \"ngIf\"], [1, \"h-[140px]\", \"w-[300px]\", 3, \"src\"], [3, \"value\"], [1, \"align-middle\"], [\"nbInput\", \"\", \"type\", \"text\", 1, \"w-100\", \"p-2\", \"text-[13px]\", 3, \"blur\", \"value\"], [1, \"btn\", \"btn-outline-danger\", \"btn-sm\", \"m-1\", 3, \"click\"], [1, \"h-32\", \"w-32\", 3, \"src\"], [1, \"w-full\", \"text-center\", 3, \"ngClass\"], [1, \"w-full\", \"justify-items-end\"], [\"class\", \" h-28 w-40\", 3, \"src\", 4, \"ngIf\"], [1, \"h-28\", \"w-40\", 3, \"src\"], [1, \"mr-2\"], [3, \"checkedChange\", \"checked\", \"disabled\"], [\"value\", \"item\", 3, \"checkedChange\", \"checked\", \"disabled\"], [\"value\", \"item\", 3, \"checked\", \"disabled\", \"checkedChange\", 4, \"ngIf\"]],\n      template: function DetailContentManagementSalesAccountComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"nb-card\", 1)(1, \"nb-card-header\");\n          i0.ɵɵelement(2, \"ngx-breadcrumb\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(3, \"nb-card-body\");\n          i0.ɵɵelement(4, \"h1\", 2);\n          i0.ɵɵelementStart(5, \"div\", 3)(6, \"h4\", 4);\n          i0.ɵɵtext(7, \"\\u985E\\u578B-\\u7368\\u7ACB\\u9078\\u6A23\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(8, DetailContentManagementSalesAccountComponent_ng_container_8_Template, 44, 18, \"ng-container\", 5);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(9, \"nb-card-footer\", 6)(10, \"button\", 7);\n          i0.ɵɵlistener(\"click\", function DetailContentManagementSalesAccountComponent_Template_button_click_10_listener() {\n            return ctx.goBack();\n          });\n          i0.ɵɵtext(11, \" \\u53D6\\u6D88 \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(12, \"button\", 8);\n          i0.ɵɵlistener(\"click\", function DetailContentManagementSalesAccountComponent_Template_button_click_12_listener() {\n            return ctx.onSubmit();\n          });\n          i0.ɵɵtext(13, \" \\u5132\\u5B58 \");\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"ngForOf\", ctx.arrListFormItemReq);\n        }\n      },\n      dependencies: [CommonModule, i7.NgClass, i7.NgForOf, i7.NgIf, SharedModule, i9.DefaultValueAccessor, i9.NumberValueAccessor, i9.NgControlStatus, i9.NgModel, i10.NbCardComponent, i10.NbCardBodyComponent, i10.NbCardFooterComponent, i10.NbCardHeaderComponent, i10.NbCheckboxComponent, i10.NbInputDirective, i10.NbSelectComponent, i10.NbOptionComponent, i11.BreadcrumbComponent, i12.BaseFilePipe, NbCheckboxModule],\n      styles: [\"/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsImZpbGUiOiJkZXRhaWwtY29udGVudC1tYW5hZ2VtZW50LXNhbGVzLWFjY291bnQuY29tcG9uZW50LnNjc3MifQ== */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvcGFnZXMvc2VsZWN0aW9uLW1hbmFnZW1lbnQvY29udGVudC1tYW5hZ2VtZW50LXNhbGVzLWFjY291bnQvZGV0YWlsLWNvbnRlbnQtbWFuYWdlbWVudC1zYWxlcy1hY2NvdW50L2RldGFpbC1jb250ZW50LW1hbmFnZW1lbnQtc2FsZXMtYWNjb3VudC5jb21wb25lbnQuc2NzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiO0FBQ0EsZ05BQWdOIiwic291cmNlUm9vdCI6IiJ9 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "NbCheckboxModule", "tap", "SharedModule", "BaseComponent", "EEvent", "i0", "ɵɵelement", "ɵɵproperty", "ɵɵpipeBind1", "formItemReq_r2", "CFirstMatrialUrl", "ɵɵsanitizeUrl", "ɵɵelementStart", "ɵɵtemplate", "DetailContentManagementSalesAccountComponent_ng_container_8_div_3_img_3_Template", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtext", "case_r4", "ɵɵtextInterpolate1", "label", "ɵɵelementContainerStart", "ɵɵlistener", "DetailContentManagementSalesAccountComponent_ng_container_8_ng_container_33_Template_input_blur_6_listener", "$event", "i_r7", "ɵɵrestoreView", "_r6", "index", "ɵɵnextContext", "$implicit", "ctx_r2", "ɵɵresetView", "renameFile", "DetailContentManagementSalesAccountComponent_ng_container_8_ng_container_33_Template_button_click_7_listener", "picture_r8", "removeImage", "id", "name", "data", "CDesignFileUrl", "DetailContentManagementSalesAccountComponent_ng_container_8_div_34_img_3_Template", "listPictures", "length", "ɵɵtwoWayListener", "DetailContentManagementSalesAccountComponent_ng_container_8_label_40_Template_nb_checkbox_checkedChange_1_listener", "_r9", "ɵɵtwoWayBindingSet", "allSelected", "onCheckAllChange", "ɵɵtwoWayProperty", "listFormItem", "CIsLock", "DetailContentManagementSalesAccountComponent_ng_container_8_label_41_Template_nb_checkbox_checkedChange_1_listener", "item_r11", "_r10", "selectedItems", "onCheckboxHouseHoldListChange", "DetailContentManagementSalesAccountComponent_ng_container_8_div_42_label_5_nb_checkbox_1_Template_nb_checkbox_checkedChange_0_listener", "_r12", "remark_r13", "selectedRemarkType", "onCheckboxRemarkChange", "DetailContentManagementSalesAccountComponent_ng_container_8_div_42_label_5_nb_checkbox_1_Template", "DetailContentManagementSalesAccountComponent_ng_container_8_div_42_label_5_Template", "CRemarkTypeOptions", "DetailContentManagementSalesAccountComponent_ng_container_8_div_3_Template", "DetailContentManagementSalesAccountComponent_ng_container_8_Template_input_ngModelChange_10_listener", "_r1", "CItemName", "DetailContentManagementSalesAccountComponent_ng_container_8_Template_input_ngModelChange_15_listener", "CRequireAnswer", "DetailContentManagementSalesAccountComponent_ng_container_8_Template_nb_select_ngModelChange_20_listener", "selectedCUiType", "DetailContentManagementSalesAccountComponent_ng_container_8_Template_nb_select_selectedChange_20_listener", "changeSelectCUiType", "DetailContentManagementSalesAccountComponent_ng_container_8_nb_option_21_Template", "DetailContentManagementSalesAccountComponent_ng_container_8_Template_button_click_25_listener", "inputFile_r5", "ɵɵreference", "click", "DetailContentManagementSalesAccountComponent_ng_container_8_Template_input_change_28_listener", "detectFiles", "DetailContentManagementSalesAccountComponent_ng_container_8_ng_container_33_Template", "DetailContentManagementSalesAccountComponent_ng_container_8_div_34_Template", "DetailContentManagementSalesAccountComponent_ng_container_8_label_40_Template", "DetailContentManagementSalesAccountComponent_ng_container_8_label_41_Template", "DetailContentManagementSalesAccountComponent_ng_container_8_div_42_Template", "ɵɵtextInterpolate3", "CName", "<PERSON>art", "CLocation", "tmp_7_0", "undefined", "value", "tmp_9_0", "tmp_11_0", "CUiTypeOptions", "houseHoldList", "DetailContentManagementSalesAccountComponent", "constructor", "_allow", "route", "message", "_formItemService", "_regularNoticeFileService", "_utilityService", "valid", "location", "_materialService", "_eventService", "typeContentManagementSalesAccount", "CFormType", "CNoticeType", "isNew", "ngOnInit", "paramMap", "subscribe", "params", "idParam", "get", "buildCaseId", "getListRegularNoticeFileHouseHold", "getItemByValue", "options", "item", "event", "formItemReq_", "file", "target", "files", "reader", "FileReader", "readAsDataURL", "onload", "base64Str", "result", "Date", "getTime", "split", "extension", "getFileExtension", "CFile", "push", "pictureId", "filter", "x", "blob", "slice", "size", "type", "newFile", "File", "checked", "for<PERSON>ach", "every", "createRemarkObject", "CRemarkType", "remarkObject", "option", "remarkTypes", "includes", "mergeItems", "items", "map", "Map", "key", "has", "existing", "count", "set", "Array", "from", "values", "CTotalAnswer", "getMaterialList", "apiMaterialGetMaterialListPost$Json", "body", "CBuildCaseId", "CPagi", "pipe", "res", "Entries", "StatusCode", "arrListFormItemReq", "o", "CFormItemHouseHold", "CFormId", "CUiType", "CPicture", "getListFormItem", "apiFormItemGetListFormItemPost$Json", "CIsPaging", "formItems", "CFormItemId", "tblFormItemHouseholds", "createArrayObjectFromArray", "formItemReq", "getHouseHoldListByNoticeType", "CHouseHoldList", "getKeysWithTrueValue", "obj", "Object", "keys", "getKeysWithTrueValueJoined", "join", "getCRemarkType", "getStringAfterComma", "inputString", "parts", "formatFile", "Base64String", "FileExtension", "FileName", "validation", "clear", "hasInvalidCUiType", "hasInvalidCRequireAnswer", "hasInvalidItemName", "saveListFormItemReq", "addErrorMessage", "onSubmit", "e", "CFormID", "errorMessages", "showErrorMSGs", "createListFormItem", "saveListFormItem", "apiFormItemSaveListFormItemPost$Json", "showSucessMSG", "goBack", "creatListFormItem", "CFormItem", "apiFormItemCreateListFormItemPost$Json", "a", "b", "c", "matchingItem", "find", "bItem", "CHousehold", "CIsSelect", "apiRegularNoticeFileGetListRegularNoticeFileHouseHoldPost$Json", "action", "payload", "back", "ɵɵdirectiveInject", "i1", "AllowHelper", "i2", "ActivatedRoute", "i3", "MessageService", "i4", "FormItemService", "RegularNoticeFileService", "i5", "UtilityService", "i6", "ValidationHelper", "i7", "Location", "MaterialService", "i8", "EventService", "selectors", "standalone", "features", "ɵɵInheritDefinitionFeature", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "DetailContentManagementSalesAccountComponent_Template", "rf", "ctx", "DetailContentManagementSalesAccountComponent_ng_container_8_Template", "DetailContentManagementSalesAccountComponent_Template_button_click_10_listener", "DetailContentManagementSalesAccountComponent_Template_button_click_12_listener", "Ng<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "i9", "DefaultValueAccessor", "NumberValueAccessor", "NgControlStatus", "NgModel", "i10", "NbCardComponent", "NbCardBodyComponent", "NbCardFooterComponent", "NbCardHeaderComponent", "NbCheckboxComponent", "NbInputDirective", "NbSelectComponent", "NbOptionComponent", "i11", "BreadcrumbComponent", "i12", "BaseFilePipe", "styles"], "sources": ["C:\\Users\\<USER>\\Documents\\jeansalechange\\adminFront\\src\\app\\pages\\selection-management\\content-management-sales-account\\detail-content-management-sales-account\\detail-content-management-sales-account.component.ts", "C:\\Users\\<USER>\\Documents\\jeansalechange\\adminFront\\src\\app\\pages\\selection-management\\content-management-sales-account\\detail-content-management-sales-account\\detail-content-management-sales-account.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { CommonModule, Location } from '@angular/common';\r\nimport { NbCheckboxModule } from '@nebular/theme';\r\nimport { AllowHelper } from 'src/app/shared/helper/allowHelper';\r\nimport { FormItemService, MaterialService, RegularNoticeFileService } from 'src/services/api/services';\r\nimport { ActivatedRoute } from '@angular/router';\r\nimport { MessageService } from 'src/app/shared/services/message.service';\r\nimport { tap } from 'rxjs';\r\nimport { CreateListFormItem, FileViewModel, GetListFormItemRes, SaveListFormItemReq } from 'src/services/api/models';\r\nimport { BaseFilePipe } from 'src/app/@theme/pipes/base-file.pipe';\r\nimport { UtilityService } from 'src/app/shared/services/utility.service';\r\nimport { ValidationHelper } from 'src/app/shared/helper/validationHelper';\r\nimport { SharedModule } from 'src/app/pages/components/shared.module';\r\nimport { BaseComponent } from 'src/app/pages/components/base/baseComponent';\r\nimport { EventService, EEvent } from 'src/app/shared/services/event.service';\r\n\r\n\r\nexport interface ExtendedSaveListFormItemReq {\r\n  CDesignFileUrl?: string | null;\r\n  CFirstMatrialUrl?: string | null;\r\n  CFile?: FileViewModel,\r\n  CFormItemHouseHold?: Array<string> | null;\r\n  CFormItemId?: number;\r\n  CItemName?: string;\r\n  CFormID?: number;\r\n  CPart?: string | null;\r\n  CName?: string | null;\r\n  CLocation?: string | null;\r\n  CRemarkType?: string | null;\r\n  CTotalAnswer?: number;\r\n  CRequireAnswer?: number;\r\n  CUiType?: number;\r\n  selectedCUiType: any | null;\r\n  selectedItems: { [key: string]: boolean }\r\n  selectedRemarkType?: { [key: string]: boolean }\r\n  allSelected: boolean,\r\n  listPictures: any[],\r\n}\r\n\r\n@Component({\r\n  selector: 'ngx-detail-content-management-sales-account',\r\n  templateUrl: './detail-content-management-sales-account.component.html',\r\n  styleUrls: ['./detail-content-management-sales-account.component.scss'],\r\n  standalone: true,\r\n  imports: [CommonModule, SharedModule, NbCheckboxModule, BaseFilePipe],\r\n})\r\n\r\nexport class DetailContentManagementSalesAccountComponent extends BaseComponent implements OnInit {\r\n  constructor(\r\n    private _allow: AllowHelper,\r\n    private route: ActivatedRoute,\r\n    private message: MessageService,\r\n    private _formItemService: FormItemService,\r\n    private _regularNoticeFileService: RegularNoticeFileService,\r\n    private _utilityService: UtilityService,\r\n    private valid: ValidationHelper,\r\n    private location: Location,\r\n    private _materialService: MaterialService,\r\n    private _eventService: EventService\r\n  ) {\r\n    super(_allow)\r\n  }\r\n\r\n  typeContentManagementSalesAccount = {\r\n    CFormType: 2,\r\n    CNoticeType: 2\r\n  }\r\n\r\n  CUiTypeOptions: any[] = [\r\n    {\r\n      value: 1, label: '建材選色'\r\n    },\r\n    {\r\n      value: 2, label: '群組選樣_選色'\r\n    }, {\r\n      value: 3, label: '建材選樣'\r\n    }\r\n  ]\r\n  CRemarkTypeOptions = [\"正常\", \"留料\"]\r\n  buildCaseId: number\r\n\r\n  override ngOnInit(): void {\r\n    this.route.paramMap.subscribe(params => {\r\n      if (params) {\r\n        const idParam = params.get('id');\r\n        const id = idParam ? +idParam : 0;\r\n        this.buildCaseId = id\r\n        if (this.buildCaseId) {\r\n          this.getListRegularNoticeFileHouseHold()\r\n        }\r\n      }\r\n    })\r\n  }\r\n\r\n\r\n  getItemByValue(value: any, options: any[]) {\r\n    for (const item of options) {\r\n      if (item.value === value) {\r\n        return item;\r\n      }\r\n    }\r\n    return null;\r\n  }\r\n\r\n\r\n  selectedItems: { [key: string]: boolean } = {};\r\n  selectedRemarkType: { [key: string]: boolean } = {};\r\n\r\n\r\n\r\n  detectFiles(event: any, formItemReq_: any) {\r\n    const file = event.target.files[0]\r\n    if (file) {\r\n      let reader = new FileReader();\r\n      reader.readAsDataURL(file);\r\n      reader.onload = () => {\r\n        let base64Str: string = reader.result as string;\r\n        if (!base64Str) {\r\n          return;\r\n        }\r\n        if (formItemReq_.listPictures.length > 0) {\r\n          formItemReq_.listPictures[0] = {\r\n            id: new Date().getTime(),\r\n            name: file.name.split('.')[0],\r\n            data: base64Str,\r\n            extension: this._utilityService.getFileExtension(file.name),\r\n            CFile: file\r\n          };\r\n        } else {\r\n          formItemReq_.listPictures.push({\r\n            id: new Date().getTime(),\r\n            name: file.name.split('.')[0],\r\n            data: base64Str,\r\n            extension: this._utilityService.getFileExtension(file.name),\r\n            CFile: file\r\n          });\r\n        }\r\n        event.target.value = null;\r\n      };\r\n    }\r\n  }\r\n\r\n  removeImage(pictureId: number, formItemReq_: any) {\r\n    if (formItemReq_.listPictures.length) {\r\n      formItemReq_.listPictures = formItemReq_.listPictures.filter((x: any) => x.id != pictureId)\r\n    }\r\n  }\r\n\r\n  renameFile(event: any, index: number, formItemReq_: any) {\r\n    var blob = formItemReq_.listPictures[index].CFile.slice(0, formItemReq_.listPictures[index].CFile.size, formItemReq_.listPictures[index].CFile.type);\r\n    var newFile = new File([blob], `${event.target.value + '.' + formItemReq_.listPictures[index].extension}`, { type: formItemReq_.listPictures[index].CFile.type });\r\n    formItemReq_.listPictures[index].CFile = newFile\r\n  }\r\n\r\n\r\n  onCheckAllChange(checked: boolean, formItemReq_: any) {\r\n    formItemReq_.allSelected = checked;\r\n    this.houseHoldList.forEach(item => {\r\n      formItemReq_.selectedItems[item] = checked;\r\n    });\r\n  }\r\n\r\n  onCheckboxHouseHoldListChange(checked: boolean, item: string, formItemReq_: any) {\r\n    if (checked) {\r\n      formItemReq_.selectedItems[item] = checked;\r\n      formItemReq_.allSelected = this.houseHoldList.every(item => formItemReq_.selectedItems[item] == checked);\r\n    } else {\r\n      formItemReq_.allSelected = false\r\n    }\r\n  }\r\n\r\n\r\n\r\n  onCheckboxRemarkChange(checked: boolean, item: string, formItemReq_: any) {\r\n    formItemReq_.selectedRemarkType[item] = checked;\r\n  }\r\n\r\n  createRemarkObject(CRemarkTypeOptions: string[], CRemarkType: string): { [key: string]: boolean } {\r\n    const remarkObject: { [key: string]: boolean } = {};\r\n    for (const option of CRemarkTypeOptions) {\r\n      remarkObject[option] = false;\r\n    }\r\n    const remarkTypes = CRemarkType.split('-');\r\n    for (const type of remarkTypes) {\r\n      if (CRemarkTypeOptions.includes(type)) {\r\n        remarkObject[type] = true;\r\n      }\r\n    }\r\n    return remarkObject;\r\n  }\r\n\r\n  mergeItems(items: ExtendedSaveListFormItemReq[]): ExtendedSaveListFormItemReq[] {\r\n    const map = new Map<string, { item: ExtendedSaveListFormItemReq, count: number }>();\r\n\r\n    items.forEach(item => {\r\n      const key = `${item.CLocation}_${item.CName}_${item.CPart}`;\r\n      if (map.has(key)) {\r\n        const existing = map.get(key)!;\r\n        existing.count += 1;\r\n      } else {\r\n        map.set(key, { item: { ...item }, count: 1 });\r\n      }\r\n    });\r\n\r\n    return Array.from(map.values()).map(({ item, count }) => ({\r\n      ...item,\r\n      CTotalAnswer: count\r\n    }));\r\n  }\r\n\r\n\r\n  getMaterialList() { // call when create\r\n    this._materialService.apiMaterialGetMaterialListPost$Json({\r\n      body: {\r\n        CBuildCaseId: this.buildCaseId,\r\n        CPagi: false\r\n      }\r\n    }).pipe(\r\n      tap(res => {\r\n        if (res.Entries && res.StatusCode == 0) {\r\n\r\n          this.houseHoldList.forEach(item => this.selectedItems[item] = false)\r\n\r\n          this.CRemarkTypeOptions.forEach(item => this.selectedRemarkType[item] = false)\r\n\r\n          this.arrListFormItemReq = res.Entries.map((o: any) => {\r\n            return {\r\n              CDesignFileUrl: null,\r\n              CFormItemHouseHold: null,\r\n              CFormId: null,\r\n              CLocation: o.CLocation,\r\n              CName: o.CName,\r\n              CPart: o.CPart,\r\n              CItemName: `${o.CName}-${o.CPart}-${o.CLocation}`,\r\n              CRemarkType: null,\r\n              CTotalAnswer: 0,\r\n              CRequireAnswer: 1,\r\n              CUiType: 0,\r\n              selectedItems: {},\r\n              selectedRemarkType: this.selectedRemarkType,\r\n              allSelected: false,\r\n              listPictures: [],\r\n              selectedCUiType: this.CUiTypeOptions[0],\r\n              CFirstMatrialUrl: o.CPicture,\r\n            }\r\n          })\r\n          this.arrListFormItemReq = [...this.mergeItems(this.arrListFormItemReq)]\r\n        }\r\n      })\r\n    ).subscribe();\r\n  }\r\n\r\n  listFormItem: GetListFormItemRes\r\n  isNew: boolean = true\r\n\r\n  getListFormItem() {\r\n    this._formItemService.apiFormItemGetListFormItemPost$Json({\r\n      body: {\r\n        CBuildCaseId: this.buildCaseId,\r\n        CFormType: this.typeContentManagementSalesAccount.CFormType,\r\n        CIsPaging: false\r\n      }\r\n    }).pipe(\r\n      tap(res => {\r\n        if (res.Entries && res.StatusCode == 0) {\r\n          this.listFormItem = res.Entries\r\n          this.isNew = res.Entries.formItems ? false : true\r\n          if (res.Entries.formItems) {\r\n\r\n            this.houseHoldList.forEach(item => this.selectedItems[item] = false)\r\n            this.CRemarkTypeOptions.forEach(item => this.selectedRemarkType[item] = false)\r\n\r\n            this.arrListFormItemReq = res.Entries.formItems.map((o: any) => {\r\n              return {\r\n                CFormId: this.listFormItem.CFormId,\r\n                CDesignFileUrl: o.CDesignFileUrl,\r\n                CFirstMatrialUrl: o.CFirstMatrialUrl,\r\n                CFile: o.CFile,\r\n                CFormItemHouseHold: o.CFormItemHouseHold,\r\n                CFormItemId: o.CFormItemId,\r\n                CLocation: o.CLocation,\r\n                CName: o.CName,\r\n                CPart: o.CPart,\r\n                CItemName: o.CItemName ? o.CItemName : `${o.CName}-${o.CPart}-${o.CLocation}`,\r\n                CRemarkType: o.CRemarkType,\r\n                CTotalAnswer: o.CTotalAnswer,\r\n                CRequireAnswer: o.CUiType===3 ? 1: o.CRequireAnswer,\r\n                CUiType: o.CUiType,\r\n                selectedItems: o.tblFormItemHouseholds.length ? this.createArrayObjectFromArray(this.houseHoldList, o.tblFormItemHouseholds) : { ...this.selectedItems },\r\n                selectedRemarkType: o.CRemarkType ? this.createRemarkObject(this.CRemarkTypeOptions, o.CRemarkType) : { ...this.selectedRemarkType },\r\n                allSelected: o.tblFormItemHouseholds.length === this.houseHoldList.length,\r\n                listPictures: [],\r\n                selectedCUiType: o.CUiType ? this.getItemByValue(o.CUiType, this.CUiTypeOptions) : this.CUiTypeOptions[0]\r\n              }\r\n            })\r\n          } else {\r\n            this.getMaterialList()\r\n          }\r\n        }\r\n      })\r\n    ).subscribe()\r\n  }\r\n\r\n  changeSelectCUiType(formItemReq: any) {\r\n    if(formItemReq.selectedCUiType && formItemReq.selectedCUiType.value ===3) {\r\n      formItemReq.CRequireAnswer = 1\r\n    }\r\n  }\r\n  getHouseHoldListByNoticeType(data: any[]) {\r\n    for (let item of data) {\r\n      if (item.CNoticeType === this.typeContentManagementSalesAccount.CNoticeType) {\r\n        return item.CHouseHoldList;\r\n      }\r\n    }\r\n    return [];\r\n  }\r\n\r\n  arrListFormItemReq: ExtendedSaveListFormItemReq[]\r\n\r\n  getKeysWithTrueValue(obj: Record<string, boolean>): string[] { // {\"key\": true } => [\"key\"]\r\n    return Object.keys(obj).filter(key => obj[key]);\r\n  }\r\n\r\n  getKeysWithTrueValueJoined(obj: Record<string, boolean>): string {\r\n    return Object.keys(obj)\r\n      .filter(key => obj[key])\r\n      .join('-');\r\n  }\r\n\r\n  getCRemarkType(selectedCUiType: any, selectedRemarkType: any): any {\r\n    if (selectedCUiType && selectedCUiType.value == 3) {\r\n      return this.getKeysWithTrueValueJoined(selectedRemarkType)\r\n    }\r\n  }\r\n\r\n  getStringAfterComma(inputString: string): string {\r\n    const parts = inputString.split(',');\r\n    if (parts.length > 1) {\r\n      return parts[1];\r\n    } else return \"\"\r\n  }\r\n\r\n  formatFile(listPictures: any) {\r\n    if (listPictures && listPictures.length > 0) {\r\n      return {\r\n        Base64String: this.getStringAfterComma(listPictures[0].data) || null,\r\n        FileExtension: listPictures[0].extension || null,\r\n        FileName: listPictures[0].CFile.name || listPictures[0].name || null,\r\n      }\r\n    } else return undefined\r\n\r\n  }\r\n\r\n\r\n  validation() {\r\n    this.valid.clear();\r\n    let hasInvalidCUiType = false;\r\n    let hasInvalidCRequireAnswer = false;\r\n    let hasInvalidItemName = false;\r\n\r\n    for (const item of this.saveListFormItemReq) {\r\n      if (!hasInvalidCUiType && (!item.CUiType)) {\r\n        hasInvalidCUiType = true;\r\n      }\r\n      if (!hasInvalidCRequireAnswer && (!item.CRequireAnswer)) {\r\n        hasInvalidCRequireAnswer = true;\r\n      }\r\n      if (item.CTotalAnswer && item.CRequireAnswer) {\r\n        if (item.CRequireAnswer > item.CTotalAnswer || item.CRequireAnswer < 0) {\r\n          this.valid.addErrorMessage('[必填數量]' + ' <= ' + item.CTotalAnswer + ` (${item.CItemName}) `);\r\n        }\r\n      }\r\n\r\n      if (!hasInvalidItemName && (!item.CItemName)) {\r\n        hasInvalidItemName = true;\r\n      }\r\n    }\r\n    if (hasInvalidCUiType) {\r\n      this.valid.addErrorMessage('[前台UI類型]' + ' 必填');\r\n    }\r\n    if (hasInvalidCRequireAnswer) {\r\n      this.valid.addErrorMessage('[必填數量]' + ' 必填且>0');\r\n    }\r\n    if (hasInvalidItemName) {\r\n      this.valid.addErrorMessage('[廚房-廚具-櫃體]' + ' 必填');\r\n    }\r\n  }\r\n\r\n  saveListFormItemReq: SaveListFormItemReq[]\r\n\r\n  onSubmit() {\r\n    this.saveListFormItemReq = this.arrListFormItemReq.map((e: any) => {\r\n      return {\r\n        CDesignFileUrl: e.CDesignFileUrl ? e.CDesignFileUrl : null,\r\n        CFile: e.listPictures ? this.formatFile(e.listPictures) : undefined,\r\n        CFormItemHouseHold: this.getKeysWithTrueValue(e.selectedItems),\r\n        CFormItemId: e.CFormItemId ? e.CFormItemId : null,\r\n        CFormID: this.isNew ? null : this.listFormItem.CFormId,\r\n        CName: e.CName,\r\n        CPart: e.CPart,\r\n        CLocation: e.CLocation,\r\n        CItemName: e.CItemName, //? e.CItemName : `${e.CName}-${e.CPart}-${e.CLocation}`,\r\n        CRemarkType: e.selectedCUiType.value !== 3 ? null : this.getCRemarkType(e.selectedCUiType, e.selectedRemarkType) || null,\r\n        CTotalAnswer: e.CTotalAnswer,\r\n        CRequireAnswer: e.CRequireAnswer,\r\n        CUiType: e.selectedCUiType.value,\r\n      }\r\n    })\r\n    this.validation()\r\n    if (this.valid.errorMessages.length > 0) {\r\n      this.message.showErrorMSGs(this.valid.errorMessages);\r\n      return\r\n    }\r\n    if (this.isNew) {\r\n      this.createListFormItem()\r\n\r\n    } else {\r\n      this.saveListFormItem()\r\n    }\r\n  }\r\n\r\n  saveListFormItem() {\r\n    this._formItemService.apiFormItemSaveListFormItemPost$Json({\r\n      body: this.saveListFormItemReq\r\n    }).subscribe(res => {\r\n      if (res.StatusCode == 0) {\r\n        this.message.showSucessMSG(\"執行成功\");\r\n        // this.getListFormItem()\r\n        this.goBack()\r\n      }\r\n    })\r\n  }\r\n\r\n  creatListFormItem: CreateListFormItem\r\n\r\n  createListFormItem() {\r\n    this.creatListFormItem = {\r\n      CBuildCaseId: this.buildCaseId,\r\n      CFormItem: this.saveListFormItemReq || null,\r\n      CFormType: this.typeContentManagementSalesAccount.CFormType,\r\n    }\r\n\r\n    this._formItemService.apiFormItemCreateListFormItemPost$Json({\r\n      body: this.creatListFormItem\r\n    }).subscribe(res => {\r\n      if (res.StatusCode == 0) {\r\n        this.message.showSucessMSG(\"執行成功\");\r\n        // this.getListFormItem()\r\n        this.goBack()\r\n      }\r\n    })\r\n  }\r\n\r\n  createArrayObjectFromArray(a: string[], b: { CFormItemHouseholdId: number, CFormItemId: number, CHousehold: string, CIsSelect: boolean }[]): { [key: string]: boolean } {\r\n    const c: { [key: string]: boolean } = {};\r\n    for (const item of a) {\r\n      const matchingItem = b.find(bItem => bItem.CHousehold === item && bItem.CIsSelect);\r\n      c[item] = !!matchingItem;\r\n    }\r\n    return c;\r\n  } //[\"House1\", \"House2\", \"House3\"] => [{CHousehold: \"House1\", CIsSelect: true,... }, ... ]\r\n\r\n\r\n  houseHoldList: any[]\r\n\r\n  getListRegularNoticeFileHouseHold() {\r\n    this._regularNoticeFileService.apiRegularNoticeFileGetListRegularNoticeFileHouseHoldPost$Json({\r\n      body: this.buildCaseId\r\n    }).pipe(\r\n      tap(res => {\r\n        if (res.Entries && res.StatusCode == 0) {\r\n          this.houseHoldList = this.getHouseHoldListByNoticeType(res.Entries)\r\n          this.getListFormItem()\r\n        }\r\n      })\r\n    ).subscribe()\r\n  }\r\n  goBack() {\r\n    this._eventService.push({\r\n      action: EEvent.GET_BUILDCASE,\r\n      payload: this.buildCaseId\r\n    })\r\n    this.location.back()\r\n  }\r\n}\r\n", "<!-- 3.7.1  CNoticeType = 1 -->\r\n<nb-card accent=\"success\">\r\n  <nb-card-header>\r\n    <ngx-breadcrumb></ngx-breadcrumb>\r\n  </nb-card-header>\r\n  <nb-card-body>\r\n    <h1 class=\"font-bold text-[#818181]\"></h1>\r\n    <div class=\"d-flex flex-wrap\">\r\n      <h4 class=\"font-bold text-lg pb-3\">類型-獨立選樣</h4>\r\n\r\n      <ng-container *ngFor=\"let formItemReq of arrListFormItemReq; let idx = index\">\r\n        <div class=\"flex flex-wrap w-full\">\r\n\r\n          <div class=\"w-full md:w-1/4 px-2 pb-2\">\r\n            <div class=\"flex flex-col items-center\" *ngIf=\"formItemReq.CFirstMatrialUrl\">\r\n              <div class=\"mt-3 w-full flex flex-col\">\r\n                <div class=\"w-full\">\r\n                  <img *ngIf=\"formItemReq?.CFirstMatrialUrl\" class=\"h-[140px] w-[300px]\"\r\n                    [src]=\"formItemReq?.CFirstMatrialUrl | addBaseFile\">\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <div class=\"w-full md:w-1/2 px-2\">\r\n            <div class=\"w-full\">\r\n              <div class=\"form-group flex items-center w-full\">\r\n                <label for=\"CItemName\" class=\"label w-1/2 text-base\">\r\n                  {{formItemReq.CName}}-{{formItemReq.CPart}}-{{formItemReq.CLocation}}\r\n                </label>\r\n                <div class=\"input-group items-center w-1/2 px-0\">\r\n                  <input type=\"text\" class=\"w-full\" nbInput [(ngModel)]=\"formItemReq.CItemName\" placeholder=\"廚房\"\r\n                    [disabled]=\"listFormItem.CIsLock ?? false\" />\r\n                </div>\r\n              </div>\r\n            </div>\r\n            <div class=\"w-full\">\r\n              <div class=\"form-group flex items-center w-full\">\r\n                <label for=\"cRequireAnswer\" class=\"label w-1/2\">必填數量</label>\r\n                <input type=\"number\" class=\"w-1/2\" nbInput placeholder=\"必填數量\" [(ngModel)]=\"formItemReq.CRequireAnswer\"\r\n                  [disabled]=\"formItemReq.selectedCUiType.value === 3 || listFormItem.CIsLock ?? false\" />\r\n              </div>\r\n            </div>\r\n            <div class=\"w-full\">\r\n              <div class=\"form-group flex items-center w-full\">\r\n                <label for=\"buildingName\" class=\"label w-1/2\">前台UI類型</label>\r\n                <nb-select placeholder=\"建案\" [(ngModel)]=\"formItemReq.selectedCUiType\" class=\"w-1/2 px-0\"\r\n                  (selectedChange)=\"changeSelectCUiType(formItemReq)\" [disabled]=\"listFormItem.CIsLock ?? false\">\r\n                  <nb-option *ngFor=\"let case of CUiTypeOptions\" [value]=\"case\">\r\n                    {{ case.label }}\r\n                  </nb-option>\r\n                </nb-select>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <div class=\"w-full md:w-1/4 px-2\">\r\n            <div class=\"w-full flex flex-col\">\r\n              <div class=\"flex justify-end w-full\">\r\n                <button class=\"btn btn-info h-fit\" [disabled]=\"listFormItem.CIsLock\"\r\n                  (click)=\"inputFile.click()\">上傳概念設計圖</button>\r\n              </div>\r\n              <div class=\"w-full\" [ngClass]=\"formItemReq.listPictures.length ? '':'hidden'\">\r\n                <input #inputFile type=\"file\" class=\"hidden\" (change)=\"detectFiles($event, formItemReq)\"\r\n                  accept=\"image/png, image/gif, image/jpeg\">\r\n                <div class=\"mt-3 w-full flex flex-col\">\r\n                  <table class=\"table table-striped border\" style=\"background-color:#f3f3f3;\">\r\n                    <tbody>\r\n                      <ng-container *ngFor=\"let picture of formItemReq.listPictures; let i = index\">\r\n                        <tr>\r\n                          <td class=\"align-middle\">\r\n                            <label>文件名 </label> &nbsp;\r\n                            <input nbInput class=\"w-100 p-2 text-[13px]\" type=\"text\" [value]=\"picture.name\"\r\n                              (blur)=\"renameFile($event, i, formItemReq)\">\r\n                            <button class=\"btn btn-outline-danger btn-sm m-1\"\r\n                              (click)=\"removeImage(picture.id, formItemReq)\">删除</button>\r\n                          </td>\r\n                          <td>\r\n                            <img class=\"h-32 w-32\" [src]=\"picture.data\">\r\n                          </td>\r\n                        </tr>\r\n                      </ng-container>\r\n                    </tbody>\r\n                  </table>\r\n                </div>\r\n              </div>\r\n\r\n              <div class=\"w-full text-center\" *ngIf=\"formItemReq.CDesignFileUrl\"\r\n                [ngClass]=\"formItemReq.listPictures.length ? 'hidden':''\">\r\n                <div class=\"mt-3 w-full flex flex-col\">\r\n                  <div class=\"w-full justify-items-end\">\r\n                    <img *ngIf=\"formItemReq.CDesignFileUrl\" class=\" h-28 w-40\"\r\n                      [src]=\"formItemReq.CDesignFileUrl | addBaseFile\">\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <div class=\"flex flex-wrap w-full\">\r\n          <div class=\"w-full md:w-1/4 px-2\"><label for=\"buildingName\" class=\"label\">適用戶別</label></div>\r\n          <div class=\"w-full md:w-3/4 px-2\">\r\n            <label class=\"mr-2\" *ngIf=\"houseHoldList.length\">\r\n              <nb-checkbox [(checked)]=\"formItemReq.allSelected\" [disabled]=\"listFormItem.CIsLock\"\r\n                (checkedChange)=\"onCheckAllChange($event, formItemReq)\">\r\n                全選\r\n              </nb-checkbox>\r\n            </label>\r\n            <label *ngFor=\"let item of houseHoldList\" class=\"mr-2\">\r\n              <nb-checkbox [(checked)]=\"formItemReq.selectedItems[item]\" value=\"item\" [disabled]=\"listFormItem.CIsLock\"\r\n                (checkedChange)=\"onCheckboxHouseHoldListChange($event, item, formItemReq)\">\r\n                {{ item }}\r\n              </nb-checkbox>\r\n            </label>\r\n          </div>\r\n        </div>\r\n\r\n        <div class=\"flex flex-wrap w-full\" *ngIf=\"formItemReq.selectedCUiType.value === 3\">\r\n          <div class=\"w-full md:w-1/4 px-2\">\r\n            <label for=\"buildingName\" class=\"label\">備註選項</label>\r\n          </div>\r\n          <div class=\"w-full md:w-3/4 px-2\">\r\n            <label *ngFor=\"let remark of CRemarkTypeOptions\" class=\"mr-2\">\r\n              <nb-checkbox *ngIf=\"formItemReq.selectedRemarkType\" [(checked)]=\"formItemReq.selectedRemarkType[remark]\"\r\n                [disabled]=\"listFormItem.CIsLock\" value=\"item\"\r\n                (checkedChange)=\"onCheckboxRemarkChange($event, remark, formItemReq)\">\r\n                {{ remark }}\r\n              </nb-checkbox>\r\n            </label>\r\n          </div>\r\n        </div>\r\n        <hr>\r\n      </ng-container>\r\n\r\n      <!-- <ng-container *ngFor=\"let formItemReq of arrListFormItemReq; let idx = index\">\r\n        <div class=\"col-md-9\">\r\n          <div class=\"form-group d-flex align-items-center w-full\">\r\n            <label for=\"CItemName\" class=\"label col-4 text-base\">\r\n              {{formItemReq.CName}}-{{formItemReq.CPart}}-{{formItemReq.CLocation}}\r\n            </label>\r\n            <div class=\"input-group items-center w-full col-8 px-0\">\r\n              <input type=\"text\" class=\"w-full col-12\" nbInput [(ngModel)]=\"formItemReq.CItemName\" placeholder=\"廚房\"\r\n                [disabled]=\"listFormItem.CIsLock ?? false\" />\r\n            </div>\r\n          </div>\r\n        </div>\r\n        <div class=\"col-md-3\">\r\n          <div class=\"d-flex justify-content-end w-full\">\r\n            <button class=\"btn btn-info\" (click)=\"inputFile.click()\" [disabled]=\"listFormItem.CIsLock\">上傳概念設計圖</button>\r\n          </div>\r\n        </div>\r\n\r\n        <div class=\"col-md-12\" [ngClass]=\"formItemReq.listPictures.length ? '':'hidden'\">\r\n          <input #inputFile type=\"file\" class=\"hidden\" (change)=\"detectFiles($event, formItemReq)\"\r\n            accept=\"image/png, image/gif, image/jpeg\">\r\n          <div class=\"mt-3 w-full flex flex-col\">\r\n            <table class=\"table table-striped border\" style=\"background-color:#f3f3f3;\">\r\n              <tbody>\r\n                <tr *ngFor=\"let picture of formItemReq.listPictures; let i = index\">\r\n                  <td class=\"align-middle\">\r\n                    <label>文件名 </label> &nbsp;\r\n                    <input nbInput class=\"w-100 p-2 text-[13px]\" type=\"text\" [value]=\"picture.name\"\r\n                      (blur)=\"renameFile($event, i, formItemReq)\">\r\n                  </td>\r\n                  <td class=\"w-[100px] h-auto\">\r\n                    <img class=\"fit-size\" [src]=\"picture.data\">\r\n                  </td>\r\n                  <td class=\"text-center w-32 align-middle\">\r\n                    <button class=\"btn btn-outline-danger btn-sm m-1\"\r\n                      (click)=\"removeImage(picture.id, formItemReq)\">删除</button>\r\n                  </td>\r\n                </tr>\r\n              </tbody>\r\n            </table>\r\n          </div>\r\n        </div>\r\n\r\n        <div class=\"col-md-12 text-center\" *ngIf=\"formItemReq.CDesignFileUrl\"\r\n          [ngClass]=\"formItemReq.listPictures.length ? 'hidden':''\">\r\n          <div class=\"mt-3 w-full flex flex-col\">\r\n            <table class=\"table table-striped border\">\r\n              <tbody>\r\n                <tr>\r\n                  <td class=\"align-middle\">\r\n                  </td>\r\n                  <td class=\"w-[80px] h-auto\">\r\n                    <img *ngIf=\"formItemReq.CDesignFileUrl\" class=\"w-14 h-14\"\r\n                      [src]=\"formItemReq.CDesignFileUrl | addBaseFile\">\r\n                  </td>\r\n                  <td class=\"text-center w-32 align-middle\">\r\n                  </td>\r\n                </tr>\r\n              </tbody>\r\n            </table>\r\n          </div>\r\n        </div>\r\n        <div class=\"col-md-9\">\r\n          <div class=\"form-group d-flex align-items-center w-full\">\r\n            <label for=\"cRequireAnswer\" class=\"label col-4\">必填數量</label>\r\n            <input type=\"number\" class=\"col-8\" nbInput placeholder=\"必填數量\" [(ngModel)]=\"formItemReq.CRequireAnswer\"\r\n              [disabled]=\"formItemReq.selectedCUiType.value === 3 || listFormItem.CIsLock ?? false\" />\r\n          </div>\r\n        </div>\r\n        <div class=\"col-md-3\">\r\n\r\n        </div>\r\n\r\n        <div class=\"col-md-9\">\r\n          <div class=\"form-group d-flex align-items-center w-full\">\r\n            <label for=\"buildingName\" class=\"label col-4\">前台UI類型</label>\r\n            <nb-select placeholder=\"建案\" [(ngModel)]=\"formItemReq.selectedCUiType\" class=\"col-8 px-0\"\r\n              (selectedChange)=\"changeSelectCUiType(formItemReq)\" [disabled]=\"listFormItem.CIsLock ?? false\">\r\n              <nb-option *ngFor=\"let case of CUiTypeOptions\" [value]=\"case\">\r\n                {{ case.label }}\r\n              </nb-option>\r\n            </nb-select>\r\n          </div>\r\n        </div>\r\n\r\n\r\n        <div class=\"col-md-3\"></div>\r\n        <div class=\"col-md-12\">\r\n          <div class=\"form-group d-flex align-items-center w-full\">\r\n            <label for=\"buildingName\" class=\"label col-3\">適用戶別 </label>\r\n            <label class=\"mr-2\" *ngIf=\"houseHoldList.length\">\r\n              <nb-checkbox [(checked)]=\"formItemReq.allSelected\" [disabled]=\"listFormItem.CIsLock\"\r\n                (checkedChange)=\"onCheckAllChange($event, formItemReq)\">\r\n                全選\r\n              </nb-checkbox>\r\n            </label>\r\n            <label *ngFor=\"let item of houseHoldList\" class=\"mr-2\">\r\n              <nb-checkbox [(checked)]=\"formItemReq.selectedItems[item]\" value=\"item\" [disabled]=\"listFormItem.CIsLock\"\r\n                (checkedChange)=\"onCheckboxHouseHoldListChange($event, item, formItemReq)\">\r\n                {{ item }}\r\n              </nb-checkbox>\r\n            </label>\r\n          </div>\r\n        </div>\r\n        <div class=\"col-md-12\" *ngIf=\"formItemReq.selectedCUiType.value===3\">\r\n          <div class=\"form-group d-flex align-items-center w-full\">\r\n            <label for=\"buildingName\" class=\"label col-3\">備註選項</label>\r\n            <label *ngFor=\"let remark of CRemarkTypeOptions\" class=\"mr-2\">\r\n              <nb-checkbox *ngIf=\"formItemReq.selectedRemarkType\" [(checked)]=\"formItemReq.selectedRemarkType[remark]\"\r\n                [disabled]=\"listFormItem.CIsLock\" value=\"item\"\r\n                (checkedChange)=\"onCheckboxRemarkChange($event, remark, formItemReq)\">\r\n                {{ remark }}\r\n              </nb-checkbox>\r\n            </label>\r\n          </div>\r\n        </div>\r\n      </ng-container> -->\r\n    </div>\r\n  </nb-card-body>\r\n  <nb-card-footer class=\"d-flex justify-content-center\">\r\n    <button class=\"btn btn-secondary mx-2\" (click)=\"goBack()\">\r\n      取消\r\n    </button>\r\n    <button class=\"btn btn-info\" (click)=\"onSubmit()\">\r\n      儲存\r\n    </button>\r\n  </nb-card-footer>\r\n</nb-card>"], "mappings": "AACA,SAASA,YAAY,QAAkB,iBAAiB;AACxD,SAASC,gBAAgB,QAAQ,gBAAgB;AAKjD,SAASC,GAAG,QAAQ,MAAM;AAK1B,SAASC,YAAY,QAAQ,wCAAwC;AACrE,SAASC,aAAa,QAAQ,6CAA6C;AAC3E,SAAuBC,MAAM,QAAQ,uCAAuC;;;;;;;;;;;;;;;;ICG1DC,EAAA,CAAAC,SAAA,cACsD;;;;;IAApDD,EAAA,CAAAE,UAAA,QAAAF,EAAA,CAAAG,WAAA,OAAAC,cAAA,kBAAAA,cAAA,CAAAC,gBAAA,GAAAL,EAAA,CAAAM,aAAA,CAAmD;;;;;IAFvDN,EAFJ,CAAAO,cAAA,cAA6E,cACpC,cACjB;IAClBP,EAAA,CAAAQ,UAAA,IAAAC,gFAAA,kBACsD;IAG5DT,EAFI,CAAAU,YAAA,EAAM,EACF,EACF;;;;IAJMV,EAAA,CAAAW,SAAA,GAAmC;IAAnCX,EAAA,CAAAE,UAAA,SAAAE,cAAA,kBAAAA,cAAA,CAAAC,gBAAA,CAAmC;;;;;IA+BzCL,EAAA,CAAAO,cAAA,oBAA8D;IAC5DP,EAAA,CAAAY,MAAA,GACF;IAAAZ,EAAA,CAAAU,YAAA,EAAY;;;;IAFmCV,EAAA,CAAAE,UAAA,UAAAW,OAAA,CAAc;IAC3Db,EAAA,CAAAW,SAAA,EACF;IADEX,EAAA,CAAAc,kBAAA,MAAAD,OAAA,CAAAE,KAAA,MACF;;;;;;IAkBIf,EAAA,CAAAgB,uBAAA,GAA8E;IAGxEhB,EAFJ,CAAAO,cAAA,SAAI,aACuB,YAChB;IAAAP,EAAA,CAAAY,MAAA,0BAAI;IAAAZ,EAAA,CAAAU,YAAA,EAAQ;IAACV,EAAA,CAAAY,MAAA,eACpB;IAAAZ,EAAA,CAAAO,cAAA,gBAC8C;IAA5CP,EAAA,CAAAiB,UAAA,kBAAAC,2GAAAC,MAAA;MAAA,MAAAC,IAAA,GAAApB,EAAA,CAAAqB,aAAA,CAAAC,GAAA,EAAAC,KAAA;MAAA,MAAAnB,cAAA,GAAAJ,EAAA,CAAAwB,aAAA,GAAAC,SAAA;MAAA,MAAAC,MAAA,GAAA1B,EAAA,CAAAwB,aAAA;MAAA,OAAAxB,EAAA,CAAA2B,WAAA,CAAQD,MAAA,CAAAE,UAAA,CAAAT,MAAA,EAAAC,IAAA,EAAAhB,cAAA,CAAkC;IAAA,EAAC;IAD7CJ,EAAA,CAAAU,YAAA,EAC8C;IAC9CV,EAAA,CAAAO,cAAA,iBACiD;IAA/CP,EAAA,CAAAiB,UAAA,mBAAAY,6GAAA;MAAA,MAAAC,UAAA,GAAA9B,EAAA,CAAAqB,aAAA,CAAAC,GAAA,EAAAG,SAAA;MAAA,MAAArB,cAAA,GAAAJ,EAAA,CAAAwB,aAAA,GAAAC,SAAA;MAAA,MAAAC,MAAA,GAAA1B,EAAA,CAAAwB,aAAA;MAAA,OAAAxB,EAAA,CAAA2B,WAAA,CAASD,MAAA,CAAAK,WAAA,CAAAD,UAAA,CAAAE,EAAA,EAAA5B,cAAA,CAAoC;IAAA,EAAC;IAACJ,EAAA,CAAAY,MAAA,mBAAE;IACrDZ,EADqD,CAAAU,YAAA,EAAS,EACzD;IACLV,EAAA,CAAAO,cAAA,SAAI;IACFP,EAAA,CAAAC,SAAA,eAA4C;IAEhDD,EADE,CAAAU,YAAA,EAAK,EACF;;;;;IARwDV,EAAA,CAAAW,SAAA,GAAsB;IAAtBX,EAAA,CAAAE,UAAA,UAAA4B,UAAA,CAAAG,IAAA,CAAsB;IAMxDjC,EAAA,CAAAW,SAAA,GAAoB;IAApBX,EAAA,CAAAE,UAAA,QAAA4B,UAAA,CAAAI,IAAA,EAAAlC,EAAA,CAAAM,aAAA,CAAoB;;;;;IAanDN,EAAA,CAAAC,SAAA,cACmD;;;;;IAAjDD,EAAA,CAAAE,UAAA,QAAAF,EAAA,CAAAG,WAAA,OAAAC,cAAA,CAAA+B,cAAA,GAAAnC,EAAA,CAAAM,aAAA,CAAgD;;;;;IAFpDN,EAHJ,CAAAO,cAAA,cAC4D,cACnB,cACC;IACpCP,EAAA,CAAAQ,UAAA,IAAA4B,iFAAA,kBACmD;IAGzDpC,EAFI,CAAAU,YAAA,EAAM,EACF,EACF;;;;IAPJV,EAAA,CAAAE,UAAA,YAAAE,cAAA,CAAAiC,YAAA,CAAAC,MAAA,iBAAyD;IAG/CtC,EAAA,CAAAW,SAAA,GAAgC;IAAhCX,EAAA,CAAAE,UAAA,SAAAE,cAAA,CAAA+B,cAAA,CAAgC;;;;;;IAa5CnC,EADF,CAAAO,cAAA,gBAAiD,sBAEW;IAD7CP,EAAA,CAAAuC,gBAAA,2BAAAC,mHAAArB,MAAA;MAAAnB,EAAA,CAAAqB,aAAA,CAAAoB,GAAA;MAAA,MAAArC,cAAA,GAAAJ,EAAA,CAAAwB,aAAA,GAAAC,SAAA;MAAAzB,EAAA,CAAA0C,kBAAA,CAAAtC,cAAA,CAAAuC,WAAA,EAAAxB,MAAA,MAAAf,cAAA,CAAAuC,WAAA,GAAAxB,MAAA;MAAA,OAAAnB,EAAA,CAAA2B,WAAA,CAAAR,MAAA;IAAA,EAAqC;IAChDnB,EAAA,CAAAiB,UAAA,2BAAAuB,mHAAArB,MAAA;MAAAnB,EAAA,CAAAqB,aAAA,CAAAoB,GAAA;MAAA,MAAArC,cAAA,GAAAJ,EAAA,CAAAwB,aAAA,GAAAC,SAAA;MAAA,MAAAC,MAAA,GAAA1B,EAAA,CAAAwB,aAAA;MAAA,OAAAxB,EAAA,CAAA2B,WAAA,CAAiBD,MAAA,CAAAkB,gBAAA,CAAAzB,MAAA,EAAAf,cAAA,CAAqC;IAAA,EAAC;IACvDJ,EAAA,CAAAY,MAAA,qBACF;IACFZ,EADE,CAAAU,YAAA,EAAc,EACR;;;;;IAJOV,EAAA,CAAAW,SAAA,EAAqC;IAArCX,EAAA,CAAA6C,gBAAA,YAAAzC,cAAA,CAAAuC,WAAA,CAAqC;IAAC3C,EAAA,CAAAE,UAAA,aAAAwB,MAAA,CAAAoB,YAAA,CAAAC,OAAA,CAAiC;;;;;;IAMpF/C,EADF,CAAAO,cAAA,gBAAuD,sBAEwB;IADhEP,EAAA,CAAAuC,gBAAA,2BAAAS,mHAAA7B,MAAA;MAAA,MAAA8B,QAAA,GAAAjD,EAAA,CAAAqB,aAAA,CAAA6B,IAAA,EAAAzB,SAAA;MAAA,MAAArB,cAAA,GAAAJ,EAAA,CAAAwB,aAAA,GAAAC,SAAA;MAAAzB,EAAA,CAAA0C,kBAAA,CAAAtC,cAAA,CAAA+C,aAAA,CAAAF,QAAA,GAAA9B,MAAA,MAAAf,cAAA,CAAA+C,aAAA,CAAAF,QAAA,IAAA9B,MAAA;MAAA,OAAAnB,EAAA,CAAA2B,WAAA,CAAAR,MAAA;IAAA,EAA6C;IACxDnB,EAAA,CAAAiB,UAAA,2BAAA+B,mHAAA7B,MAAA;MAAA,MAAA8B,QAAA,GAAAjD,EAAA,CAAAqB,aAAA,CAAA6B,IAAA,EAAAzB,SAAA;MAAA,MAAArB,cAAA,GAAAJ,EAAA,CAAAwB,aAAA,GAAAC,SAAA;MAAA,MAAAC,MAAA,GAAA1B,EAAA,CAAAwB,aAAA;MAAA,OAAAxB,EAAA,CAAA2B,WAAA,CAAiBD,MAAA,CAAA0B,6BAAA,CAAAjC,MAAA,EAAA8B,QAAA,EAAA7C,cAAA,CAAwD;IAAA,EAAC;IAC1EJ,EAAA,CAAAY,MAAA,GACF;IACFZ,EADE,CAAAU,YAAA,EAAc,EACR;;;;;;IAJOV,EAAA,CAAAW,SAAA,EAA6C;IAA7CX,EAAA,CAAA6C,gBAAA,YAAAzC,cAAA,CAAA+C,aAAA,CAAAF,QAAA,EAA6C;IAAcjD,EAAA,CAAAE,UAAA,aAAAwB,MAAA,CAAAoB,YAAA,CAAAC,OAAA,CAAiC;IAEvG/C,EAAA,CAAAW,SAAA,EACF;IADEX,EAAA,CAAAc,kBAAA,MAAAmC,QAAA,MACF;;;;;;IAWAjD,EAAA,CAAAO,cAAA,sBAEwE;IAFpBP,EAAA,CAAAuC,gBAAA,2BAAAc,uIAAAlC,MAAA;MAAAnB,EAAA,CAAAqB,aAAA,CAAAiC,IAAA;MAAA,MAAAC,UAAA,GAAAvD,EAAA,CAAAwB,aAAA,GAAAC,SAAA;MAAA,MAAArB,cAAA,GAAAJ,EAAA,CAAAwB,aAAA,IAAAC,SAAA;MAAAzB,EAAA,CAAA0C,kBAAA,CAAAtC,cAAA,CAAAoD,kBAAA,CAAAD,UAAA,GAAApC,MAAA,MAAAf,cAAA,CAAAoD,kBAAA,CAAAD,UAAA,IAAApC,MAAA;MAAA,OAAAnB,EAAA,CAAA2B,WAAA,CAAAR,MAAA;IAAA,EAAoD;IAEtGnB,EAAA,CAAAiB,UAAA,2BAAAoC,uIAAAlC,MAAA;MAAAnB,EAAA,CAAAqB,aAAA,CAAAiC,IAAA;MAAA,MAAAC,UAAA,GAAAvD,EAAA,CAAAwB,aAAA,GAAAC,SAAA;MAAA,MAAArB,cAAA,GAAAJ,EAAA,CAAAwB,aAAA,IAAAC,SAAA;MAAA,MAAAC,MAAA,GAAA1B,EAAA,CAAAwB,aAAA;MAAA,OAAAxB,EAAA,CAAA2B,WAAA,CAAiBD,MAAA,CAAA+B,sBAAA,CAAAtC,MAAA,EAAAoC,UAAA,EAAAnD,cAAA,CAAmD;IAAA,EAAC;IACrEJ,EAAA,CAAAY,MAAA,GACF;IAAAZ,EAAA,CAAAU,YAAA,EAAc;;;;;;IAJsCV,EAAA,CAAA6C,gBAAA,YAAAzC,cAAA,CAAAoD,kBAAA,CAAAD,UAAA,EAAoD;IACtGvD,EAAA,CAAAE,UAAA,aAAAwB,MAAA,CAAAoB,YAAA,CAAAC,OAAA,CAAiC;IAEjC/C,EAAA,CAAAW,SAAA,EACF;IADEX,EAAA,CAAAc,kBAAA,MAAAyC,UAAA,MACF;;;;;IALFvD,EAAA,CAAAO,cAAA,gBAA8D;IAC5DP,EAAA,CAAAQ,UAAA,IAAAkD,iGAAA,0BAEwE;IAG1E1D,EAAA,CAAAU,YAAA,EAAQ;;;;IALQV,EAAA,CAAAW,SAAA,EAAoC;IAApCX,EAAA,CAAAE,UAAA,SAAAE,cAAA,CAAAoD,kBAAA,CAAoC;;;;;IAJpDxD,EAFJ,CAAAO,cAAA,aAAmF,cAC/C,gBACQ;IAAAP,EAAA,CAAAY,MAAA,+BAAI;IAC9CZ,EAD8C,CAAAU,YAAA,EAAQ,EAChD;IACNV,EAAA,CAAAO,cAAA,cAAkC;IAChCP,EAAA,CAAAQ,UAAA,IAAAmD,mFAAA,oBAA8D;IAQlE3D,EADE,CAAAU,YAAA,EAAM,EACF;;;;IARwBV,EAAA,CAAAW,SAAA,GAAqB;IAArBX,EAAA,CAAAE,UAAA,YAAAwB,MAAA,CAAAkC,kBAAA,CAAqB;;;;;;IAjHrD5D,EAAA,CAAAgB,uBAAA,GAA8E;IAG1EhB,EAFF,CAAAO,cAAA,aAAmC,cAEM;IACrCP,EAAA,CAAAQ,UAAA,IAAAqD,0EAAA,kBAA6E;IAQ/E7D,EAAA,CAAAU,YAAA,EAAM;IAKAV,EAHN,CAAAO,cAAA,cAAkC,cACZ,cAC+B,gBACM;IACnDP,EAAA,CAAAY,MAAA,GACF;IAAAZ,EAAA,CAAAU,YAAA,EAAQ;IAENV,EADF,CAAAO,cAAA,cAAiD,iBAEA;IADLP,EAAA,CAAAuC,gBAAA,2BAAAuB,qGAAA3C,MAAA;MAAA,MAAAf,cAAA,GAAAJ,EAAA,CAAAqB,aAAA,CAAA0C,GAAA,EAAAtC,SAAA;MAAAzB,EAAA,CAAA0C,kBAAA,CAAAtC,cAAA,CAAA4D,SAAA,EAAA7C,MAAA,MAAAf,cAAA,CAAA4D,SAAA,GAAA7C,MAAA;MAAA,OAAAnB,EAAA,CAAA2B,WAAA,CAAAR,MAAA;IAAA,EAAmC;IAInFnB,EAJM,CAAAU,YAAA,EAC+C,EAC3C,EACF,EACF;IAGFV,EAFJ,CAAAO,cAAA,eAAoB,eAC+B,iBACC;IAAAP,EAAA,CAAAY,MAAA,gCAAI;IAAAZ,EAAA,CAAAU,YAAA,EAAQ;IAC5DV,EAAA,CAAAO,cAAA,iBAC0F;IAD5BP,EAAA,CAAAuC,gBAAA,2BAAA0B,qGAAA9C,MAAA;MAAA,MAAAf,cAAA,GAAAJ,EAAA,CAAAqB,aAAA,CAAA0C,GAAA,EAAAtC,SAAA;MAAAzB,EAAA,CAAA0C,kBAAA,CAAAtC,cAAA,CAAA8D,cAAA,EAAA/C,MAAA,MAAAf,cAAA,CAAA8D,cAAA,GAAA/C,MAAA;MAAA,OAAAnB,EAAA,CAAA2B,WAAA,CAAAR,MAAA;IAAA,EAAwC;IAG1GnB,EAHI,CAAAU,YAAA,EAC0F,EACtF,EACF;IAGFV,EAFJ,CAAAO,cAAA,eAAoB,eAC+B,iBACD;IAAAP,EAAA,CAAAY,MAAA,kCAAM;IAAAZ,EAAA,CAAAU,YAAA,EAAQ;IAC5DV,EAAA,CAAAO,cAAA,qBACiG;IADrEP,EAAA,CAAAuC,gBAAA,2BAAA4B,yGAAAhD,MAAA;MAAA,MAAAf,cAAA,GAAAJ,EAAA,CAAAqB,aAAA,CAAA0C,GAAA,EAAAtC,SAAA;MAAAzB,EAAA,CAAA0C,kBAAA,CAAAtC,cAAA,CAAAgE,eAAA,EAAAjD,MAAA,MAAAf,cAAA,CAAAgE,eAAA,GAAAjD,MAAA;MAAA,OAAAnB,EAAA,CAAA2B,WAAA,CAAAR,MAAA;IAAA,EAAyC;IACnEnB,EAAA,CAAAiB,UAAA,4BAAAoD,0GAAA;MAAA,MAAAjE,cAAA,GAAAJ,EAAA,CAAAqB,aAAA,CAAA0C,GAAA,EAAAtC,SAAA;MAAA,MAAAC,MAAA,GAAA1B,EAAA,CAAAwB,aAAA;MAAA,OAAAxB,EAAA,CAAA2B,WAAA,CAAkBD,MAAA,CAAA4C,mBAAA,CAAAlE,cAAA,CAAgC;IAAA,EAAC;IACnDJ,EAAA,CAAAQ,UAAA,KAAA+D,iFAAA,wBAA8D;IAMtEvE,EAHM,CAAAU,YAAA,EAAY,EACR,EACF,EACF;IAKAV,EAHN,CAAAO,cAAA,eAAkC,eACE,eACK,kBAEL;IAA5BP,EAAA,CAAAiB,UAAA,mBAAAuD,8FAAA;MAAAxE,EAAA,CAAAqB,aAAA,CAAA0C,GAAA;MAAA,MAAAU,YAAA,GAAAzE,EAAA,CAAA0E,WAAA;MAAA,OAAA1E,EAAA,CAAA2B,WAAA,CAAS8C,YAAA,CAAAE,KAAA,EAAiB;IAAA,EAAC;IAAC3E,EAAA,CAAAY,MAAA,kDAAO;IACvCZ,EADuC,CAAAU,YAAA,EAAS,EAC1C;IAEJV,EADF,CAAAO,cAAA,eAA8E,oBAEhC;IADCP,EAAA,CAAAiB,UAAA,oBAAA2D,8FAAAzD,MAAA;MAAA,MAAAf,cAAA,GAAAJ,EAAA,CAAAqB,aAAA,CAAA0C,GAAA,EAAAtC,SAAA;MAAA,MAAAC,MAAA,GAAA1B,EAAA,CAAAwB,aAAA;MAAA,OAAAxB,EAAA,CAAA2B,WAAA,CAAUD,MAAA,CAAAmD,WAAA,CAAA1D,MAAA,EAAAf,cAAA,CAAgC;IAAA,EAAC;IAAxFJ,EAAA,CAAAU,YAAA,EAC4C;IAGxCV,EAFJ,CAAAO,cAAA,eAAuC,iBACuC,aACnE;IACLP,EAAA,CAAAQ,UAAA,KAAAsE,oFAAA,2BAA8E;IAiBtF9E,EAHM,CAAAU,YAAA,EAAQ,EACF,EACJ,EACF;IAENV,EAAA,CAAAQ,UAAA,KAAAuE,2EAAA,kBAC4D;IAUlE/E,EAFI,CAAAU,YAAA,EAAM,EACF,EACF;IAG8BV,EADpC,CAAAO,cAAA,cAAmC,eACC,iBAAwC;IAAAP,EAAA,CAAAY,MAAA,gCAAI;IAAQZ,EAAR,CAAAU,YAAA,EAAQ,EAAM;IAC5FV,EAAA,CAAAO,cAAA,eAAkC;IAOhCP,EANA,CAAAQ,UAAA,KAAAwE,6EAAA,oBAAiD,KAAAC,6EAAA,oBAMM;IAO3DjF,EADE,CAAAU,YAAA,EAAM,EACF;IAENV,EAAA,CAAAQ,UAAA,KAAA0E,2EAAA,kBAAmF;IAcnFlF,EAAA,CAAAC,SAAA,UAAI;;;;;;;;;IAtHyCD,EAAA,CAAAW,SAAA,GAAkC;IAAlCX,EAAA,CAAAE,UAAA,SAAAE,cAAA,CAAAC,gBAAA,CAAkC;IAcrEL,EAAA,CAAAW,SAAA,GACF;IADEX,EAAA,CAAAmF,kBAAA,MAAA/E,cAAA,CAAAgF,KAAA,OAAAhF,cAAA,CAAAiF,KAAA,OAAAjF,cAAA,CAAAkF,SAAA,MACF;IAE4CtF,EAAA,CAAAW,SAAA,GAAmC;IAAnCX,EAAA,CAAA6C,gBAAA,YAAAzC,cAAA,CAAA4D,SAAA,CAAmC;IAC3EhE,EAAA,CAAAE,UAAA,cAAAqF,OAAA,GAAA7D,MAAA,CAAAoB,YAAA,CAAAC,OAAA,cAAAwC,OAAA,KAAAC,SAAA,GAAAD,OAAA,SAA0C;IAOgBvF,EAAA,CAAAW,SAAA,GAAwC;IAAxCX,EAAA,CAAA6C,gBAAA,YAAAzC,cAAA,CAAA8D,cAAA,CAAwC;IACpGlE,EAAA,CAAAE,UAAA,aAAAE,cAAA,CAAAgE,eAAA,CAAAqB,KAAA,YAAAC,OAAA,GAAAhE,MAAA,CAAAoB,YAAA,CAAAC,OAAA,cAAA2C,OAAA,KAAAF,SAAA,GAAAE,OAAA,UAAqF;IAM3D1F,EAAA,CAAAW,SAAA,GAAyC;IAAzCX,EAAA,CAAA6C,gBAAA,YAAAzC,cAAA,CAAAgE,eAAA,CAAyC;IACfpE,EAAA,CAAAE,UAAA,cAAAyF,QAAA,GAAAjE,MAAA,CAAAoB,YAAA,CAAAC,OAAA,cAAA4C,QAAA,KAAAH,SAAA,GAAAG,QAAA,SAA0C;IAClE3F,EAAA,CAAAW,SAAA,EAAiB;IAAjBX,EAAA,CAAAE,UAAA,YAAAwB,MAAA,CAAAkE,cAAA,CAAiB;IAWZ5F,EAAA,CAAAW,SAAA,GAAiC;IAAjCX,EAAA,CAAAE,UAAA,aAAAwB,MAAA,CAAAoB,YAAA,CAAAC,OAAA,CAAiC;IAGlD/C,EAAA,CAAAW,SAAA,GAAyD;IAAzDX,EAAA,CAAAE,UAAA,YAAAE,cAAA,CAAAiC,YAAA,CAAAC,MAAA,iBAAyD;IAMnCtC,EAAA,CAAAW,SAAA,GAA6B;IAA7BX,EAAA,CAAAE,UAAA,YAAAE,cAAA,CAAAiC,YAAA,CAA6B;IAmBtCrC,EAAA,CAAAW,SAAA,EAAgC;IAAhCX,EAAA,CAAAE,UAAA,SAAAE,cAAA,CAAA+B,cAAA,CAAgC;IAgB9CnC,EAAA,CAAAW,SAAA,GAA0B;IAA1BX,EAAA,CAAAE,UAAA,SAAAwB,MAAA,CAAAmE,aAAA,CAAAvD,MAAA,CAA0B;IAMvBtC,EAAA,CAAAW,SAAA,EAAgB;IAAhBX,EAAA,CAAAE,UAAA,YAAAwB,MAAA,CAAAmE,aAAA,CAAgB;IASR7F,EAAA,CAAAW,SAAA,EAA6C;IAA7CX,EAAA,CAAAE,UAAA,SAAAE,cAAA,CAAAgE,eAAA,CAAAqB,KAAA,OAA6C;;;ADvEzF,OAAM,MAAOK,4CAA6C,SAAQhG,aAAa;EAC7EiG,YACUC,MAAmB,EACnBC,KAAqB,EACrBC,OAAuB,EACvBC,gBAAiC,EACjCC,yBAAmD,EACnDC,eAA+B,EAC/BC,KAAuB,EACvBC,QAAkB,EAClBC,gBAAiC,EACjCC,aAA2B;IAEnC,KAAK,CAACT,MAAM,CAAC;IAXL,KAAAA,MAAM,GAANA,MAAM;IACN,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,yBAAyB,GAAzBA,yBAAyB;IACzB,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,QAAQ,GAARA,QAAQ;IACR,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,aAAa,GAAbA,aAAa;IAKvB,KAAAC,iCAAiC,GAAG;MAClCC,SAAS,EAAE,CAAC;MACZC,WAAW,EAAE;KACd;IAED,KAAAhB,cAAc,GAAU,CACtB;MACEH,KAAK,EAAE,CAAC;MAAE1E,KAAK,EAAE;KAClB,EACD;MACE0E,KAAK,EAAE,CAAC;MAAE1E,KAAK,EAAE;KAClB,EAAE;MACD0E,KAAK,EAAE,CAAC;MAAE1E,KAAK,EAAE;KAClB,CACF;IACD,KAAA6C,kBAAkB,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC;IA2BjC,KAAAT,aAAa,GAA+B,EAAE;IAC9C,KAAAK,kBAAkB,GAA+B,EAAE;IAmJnD,KAAAqD,KAAK,GAAY,IAAI;EAhMrB;EAoBSC,QAAQA,CAAA;IACf,IAAI,CAACb,KAAK,CAACc,QAAQ,CAACC,SAAS,CAACC,MAAM,IAAG;MACrC,IAAIA,MAAM,EAAE;QACV,MAAMC,OAAO,GAAGD,MAAM,CAACE,GAAG,CAAC,IAAI,CAAC;QAChC,MAAMnF,EAAE,GAAGkF,OAAO,GAAG,CAACA,OAAO,GAAG,CAAC;QACjC,IAAI,CAACE,WAAW,GAAGpF,EAAE;QACrB,IAAI,IAAI,CAACoF,WAAW,EAAE;UACpB,IAAI,CAACC,iCAAiC,EAAE;QAC1C;MACF;IACF,CAAC,CAAC;EACJ;EAGAC,cAAcA,CAAC7B,KAAU,EAAE8B,OAAc;IACvC,KAAK,MAAMC,IAAI,IAAID,OAAO,EAAE;MAC1B,IAAIC,IAAI,CAAC/B,KAAK,KAAKA,KAAK,EAAE;QACxB,OAAO+B,IAAI;MACb;IACF;IACA,OAAO,IAAI;EACb;EAQA3C,WAAWA,CAAC4C,KAAU,EAAEC,YAAiB;IACvC,MAAMC,IAAI,GAAGF,KAAK,CAACG,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC;IAClC,IAAIF,IAAI,EAAE;MACR,IAAIG,MAAM,GAAG,IAAIC,UAAU,EAAE;MAC7BD,MAAM,CAACE,aAAa,CAACL,IAAI,CAAC;MAC1BG,MAAM,CAACG,MAAM,GAAG,MAAK;QACnB,IAAIC,SAAS,GAAWJ,MAAM,CAACK,MAAgB;QAC/C,IAAI,CAACD,SAAS,EAAE;UACd;QACF;QACA,IAAIR,YAAY,CAACrF,YAAY,CAACC,MAAM,GAAG,CAAC,EAAE;UACxCoF,YAAY,CAACrF,YAAY,CAAC,CAAC,CAAC,GAAG;YAC7BL,EAAE,EAAE,IAAIoG,IAAI,EAAE,CAACC,OAAO,EAAE;YACxBpG,IAAI,EAAE0F,IAAI,CAAC1F,IAAI,CAACqG,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;YAC7BpG,IAAI,EAAEgG,SAAS;YACfK,SAAS,EAAE,IAAI,CAAClC,eAAe,CAACmC,gBAAgB,CAACb,IAAI,CAAC1F,IAAI,CAAC;YAC3DwG,KAAK,EAAEd;WACR;QACH,CAAC,MAAM;UACLD,YAAY,CAACrF,YAAY,CAACqG,IAAI,CAAC;YAC7B1G,EAAE,EAAE,IAAIoG,IAAI,EAAE,CAACC,OAAO,EAAE;YACxBpG,IAAI,EAAE0F,IAAI,CAAC1F,IAAI,CAACqG,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;YAC7BpG,IAAI,EAAEgG,SAAS;YACfK,SAAS,EAAE,IAAI,CAAClC,eAAe,CAACmC,gBAAgB,CAACb,IAAI,CAAC1F,IAAI,CAAC;YAC3DwG,KAAK,EAAEd;WACR,CAAC;QACJ;QACAF,KAAK,CAACG,MAAM,CAACnC,KAAK,GAAG,IAAI;MAC3B,CAAC;IACH;EACF;EAEA1D,WAAWA,CAAC4G,SAAiB,EAAEjB,YAAiB;IAC9C,IAAIA,YAAY,CAACrF,YAAY,CAACC,MAAM,EAAE;MACpCoF,YAAY,CAACrF,YAAY,GAAGqF,YAAY,CAACrF,YAAY,CAACuG,MAAM,CAAEC,CAAM,IAAKA,CAAC,CAAC7G,EAAE,IAAI2G,SAAS,CAAC;IAC7F;EACF;EAEA/G,UAAUA,CAAC6F,KAAU,EAAElG,KAAa,EAAEmG,YAAiB;IACrD,IAAIoB,IAAI,GAAGpB,YAAY,CAACrF,YAAY,CAACd,KAAK,CAAC,CAACkH,KAAK,CAACM,KAAK,CAAC,CAAC,EAAErB,YAAY,CAACrF,YAAY,CAACd,KAAK,CAAC,CAACkH,KAAK,CAACO,IAAI,EAAEtB,YAAY,CAACrF,YAAY,CAACd,KAAK,CAAC,CAACkH,KAAK,CAACQ,IAAI,CAAC;IACpJ,IAAIC,OAAO,GAAG,IAAIC,IAAI,CAAC,CAACL,IAAI,CAAC,EAAE,GAAGrB,KAAK,CAACG,MAAM,CAACnC,KAAK,GAAG,GAAG,GAAGiC,YAAY,CAACrF,YAAY,CAACd,KAAK,CAAC,CAACgH,SAAS,EAAE,EAAE;MAAEU,IAAI,EAAEvB,YAAY,CAACrF,YAAY,CAACd,KAAK,CAAC,CAACkH,KAAK,CAACQ;IAAI,CAAE,CAAC;IACjKvB,YAAY,CAACrF,YAAY,CAACd,KAAK,CAAC,CAACkH,KAAK,GAAGS,OAAO;EAClD;EAGAtG,gBAAgBA,CAACwG,OAAgB,EAAE1B,YAAiB;IAClDA,YAAY,CAAC/E,WAAW,GAAGyG,OAAO;IAClC,IAAI,CAACvD,aAAa,CAACwD,OAAO,CAAC7B,IAAI,IAAG;MAChCE,YAAY,CAACvE,aAAa,CAACqE,IAAI,CAAC,GAAG4B,OAAO;IAC5C,CAAC,CAAC;EACJ;EAEAhG,6BAA6BA,CAACgG,OAAgB,EAAE5B,IAAY,EAAEE,YAAiB;IAC7E,IAAI0B,OAAO,EAAE;MACX1B,YAAY,CAACvE,aAAa,CAACqE,IAAI,CAAC,GAAG4B,OAAO;MAC1C1B,YAAY,CAAC/E,WAAW,GAAG,IAAI,CAACkD,aAAa,CAACyD,KAAK,CAAC9B,IAAI,IAAIE,YAAY,CAACvE,aAAa,CAACqE,IAAI,CAAC,IAAI4B,OAAO,CAAC;IAC1G,CAAC,MAAM;MACL1B,YAAY,CAAC/E,WAAW,GAAG,KAAK;IAClC;EACF;EAIAc,sBAAsBA,CAAC2F,OAAgB,EAAE5B,IAAY,EAAEE,YAAiB;IACtEA,YAAY,CAAClE,kBAAkB,CAACgE,IAAI,CAAC,GAAG4B,OAAO;EACjD;EAEAG,kBAAkBA,CAAC3F,kBAA4B,EAAE4F,WAAmB;IAClE,MAAMC,YAAY,GAA+B,EAAE;IACnD,KAAK,MAAMC,MAAM,IAAI9F,kBAAkB,EAAE;MACvC6F,YAAY,CAACC,MAAM,CAAC,GAAG,KAAK;IAC9B;IACA,MAAMC,WAAW,GAAGH,WAAW,CAAClB,KAAK,CAAC,GAAG,CAAC;IAC1C,KAAK,MAAMW,IAAI,IAAIU,WAAW,EAAE;MAC9B,IAAI/F,kBAAkB,CAACgG,QAAQ,CAACX,IAAI,CAAC,EAAE;QACrCQ,YAAY,CAACR,IAAI,CAAC,GAAG,IAAI;MAC3B;IACF;IACA,OAAOQ,YAAY;EACrB;EAEAI,UAAUA,CAACC,KAAoC;IAC7C,MAAMC,GAAG,GAAG,IAAIC,GAAG,EAAgE;IAEnFF,KAAK,CAACT,OAAO,CAAC7B,IAAI,IAAG;MACnB,MAAMyC,GAAG,GAAG,GAAGzC,IAAI,CAAClC,SAAS,IAAIkC,IAAI,CAACpC,KAAK,IAAIoC,IAAI,CAACnC,KAAK,EAAE;MAC3D,IAAI0E,GAAG,CAACG,GAAG,CAACD,GAAG,CAAC,EAAE;QAChB,MAAME,QAAQ,GAAGJ,GAAG,CAAC5C,GAAG,CAAC8C,GAAG,CAAE;QAC9BE,QAAQ,CAACC,KAAK,IAAI,CAAC;MACrB,CAAC,MAAM;QACLL,GAAG,CAACM,GAAG,CAACJ,GAAG,EAAE;UAAEzC,IAAI,EAAE;YAAE,GAAGA;UAAI,CAAE;UAAE4C,KAAK,EAAE;QAAC,CAAE,CAAC;MAC/C;IACF,CAAC,CAAC;IAEF,OAAOE,KAAK,CAACC,IAAI,CAACR,GAAG,CAACS,MAAM,EAAE,CAAC,CAACT,GAAG,CAAC,CAAC;MAAEvC,IAAI;MAAE4C;IAAK,CAAE,MAAM;MACxD,GAAG5C,IAAI;MACPiD,YAAY,EAAEL;KACf,CAAC,CAAC;EACL;EAGAM,eAAeA,CAAA;IACb,IAAI,CAAClE,gBAAgB,CAACmE,mCAAmC,CAAC;MACxDC,IAAI,EAAE;QACJC,YAAY,EAAE,IAAI,CAACzD,WAAW;QAC9B0D,KAAK,EAAE;;KAEV,CAAC,CAACC,IAAI,CACLnL,GAAG,CAACoL,GAAG,IAAG;MACR,IAAIA,GAAG,CAACC,OAAO,IAAID,GAAG,CAACE,UAAU,IAAI,CAAC,EAAE;QAEtC,IAAI,CAACrF,aAAa,CAACwD,OAAO,CAAC7B,IAAI,IAAI,IAAI,CAACrE,aAAa,CAACqE,IAAI,CAAC,GAAG,KAAK,CAAC;QAEpE,IAAI,CAAC5D,kBAAkB,CAACyF,OAAO,CAAC7B,IAAI,IAAI,IAAI,CAAChE,kBAAkB,CAACgE,IAAI,CAAC,GAAG,KAAK,CAAC;QAE9E,IAAI,CAAC2D,kBAAkB,GAAGH,GAAG,CAACC,OAAO,CAAClB,GAAG,CAAEqB,CAAM,IAAI;UACnD,OAAO;YACLjJ,cAAc,EAAE,IAAI;YACpBkJ,kBAAkB,EAAE,IAAI;YACxBC,OAAO,EAAE,IAAI;YACbhG,SAAS,EAAE8F,CAAC,CAAC9F,SAAS;YACtBF,KAAK,EAAEgG,CAAC,CAAChG,KAAK;YACdC,KAAK,EAAE+F,CAAC,CAAC/F,KAAK;YACdrB,SAAS,EAAE,GAAGoH,CAAC,CAAChG,KAAK,IAAIgG,CAAC,CAAC/F,KAAK,IAAI+F,CAAC,CAAC9F,SAAS,EAAE;YACjDkE,WAAW,EAAE,IAAI;YACjBiB,YAAY,EAAE,CAAC;YACfvG,cAAc,EAAE,CAAC;YACjBqH,OAAO,EAAE,CAAC;YACVpI,aAAa,EAAE,EAAE;YACjBK,kBAAkB,EAAE,IAAI,CAACA,kBAAkB;YAC3Cb,WAAW,EAAE,KAAK;YAClBN,YAAY,EAAE,EAAE;YAChB+B,eAAe,EAAE,IAAI,CAACwB,cAAc,CAAC,CAAC,CAAC;YACvCvF,gBAAgB,EAAE+K,CAAC,CAACI;WACrB;QACH,CAAC,CAAC;QACF,IAAI,CAACL,kBAAkB,GAAG,CAAC,GAAG,IAAI,CAACtB,UAAU,CAAC,IAAI,CAACsB,kBAAkB,CAAC,CAAC;MACzE;IACF,CAAC,CAAC,CACH,CAACnE,SAAS,EAAE;EACf;EAKAyE,eAAeA,CAAA;IACb,IAAI,CAACtF,gBAAgB,CAACuF,mCAAmC,CAAC;MACxDd,IAAI,EAAE;QACJC,YAAY,EAAE,IAAI,CAACzD,WAAW;QAC9BT,SAAS,EAAE,IAAI,CAACD,iCAAiC,CAACC,SAAS;QAC3DgF,SAAS,EAAE;;KAEd,CAAC,CAACZ,IAAI,CACLnL,GAAG,CAACoL,GAAG,IAAG;MACR,IAAIA,GAAG,CAACC,OAAO,IAAID,GAAG,CAACE,UAAU,IAAI,CAAC,EAAE;QACtC,IAAI,CAACpI,YAAY,GAAGkI,GAAG,CAACC,OAAO;QAC/B,IAAI,CAACpE,KAAK,GAAGmE,GAAG,CAACC,OAAO,CAACW,SAAS,GAAG,KAAK,GAAG,IAAI;QACjD,IAAIZ,GAAG,CAACC,OAAO,CAACW,SAAS,EAAE;UAEzB,IAAI,CAAC/F,aAAa,CAACwD,OAAO,CAAC7B,IAAI,IAAI,IAAI,CAACrE,aAAa,CAACqE,IAAI,CAAC,GAAG,KAAK,CAAC;UACpE,IAAI,CAAC5D,kBAAkB,CAACyF,OAAO,CAAC7B,IAAI,IAAI,IAAI,CAAChE,kBAAkB,CAACgE,IAAI,CAAC,GAAG,KAAK,CAAC;UAE9E,IAAI,CAAC2D,kBAAkB,GAAGH,GAAG,CAACC,OAAO,CAACW,SAAS,CAAC7B,GAAG,CAAEqB,CAAM,IAAI;YAC7D,OAAO;cACLE,OAAO,EAAE,IAAI,CAACxI,YAAY,CAACwI,OAAO;cAClCnJ,cAAc,EAAEiJ,CAAC,CAACjJ,cAAc;cAChC9B,gBAAgB,EAAE+K,CAAC,CAAC/K,gBAAgB;cACpCoI,KAAK,EAAE2C,CAAC,CAAC3C,KAAK;cACd4C,kBAAkB,EAAED,CAAC,CAACC,kBAAkB;cACxCQ,WAAW,EAAET,CAAC,CAACS,WAAW;cAC1BvG,SAAS,EAAE8F,CAAC,CAAC9F,SAAS;cACtBF,KAAK,EAAEgG,CAAC,CAAChG,KAAK;cACdC,KAAK,EAAE+F,CAAC,CAAC/F,KAAK;cACdrB,SAAS,EAAEoH,CAAC,CAACpH,SAAS,GAAGoH,CAAC,CAACpH,SAAS,GAAG,GAAGoH,CAAC,CAAChG,KAAK,IAAIgG,CAAC,CAAC/F,KAAK,IAAI+F,CAAC,CAAC9F,SAAS,EAAE;cAC7EkE,WAAW,EAAE4B,CAAC,CAAC5B,WAAW;cAC1BiB,YAAY,EAAEW,CAAC,CAACX,YAAY;cAC5BvG,cAAc,EAAEkH,CAAC,CAACG,OAAO,KAAG,CAAC,GAAG,CAAC,GAAEH,CAAC,CAAClH,cAAc;cACnDqH,OAAO,EAAEH,CAAC,CAACG,OAAO;cAClBpI,aAAa,EAAEiI,CAAC,CAACU,qBAAqB,CAACxJ,MAAM,GAAG,IAAI,CAACyJ,0BAA0B,CAAC,IAAI,CAAClG,aAAa,EAAEuF,CAAC,CAACU,qBAAqB,CAAC,GAAG;gBAAE,GAAG,IAAI,CAAC3I;cAAa,CAAE;cACxJK,kBAAkB,EAAE4H,CAAC,CAAC5B,WAAW,GAAG,IAAI,CAACD,kBAAkB,CAAC,IAAI,CAAC3F,kBAAkB,EAAEwH,CAAC,CAAC5B,WAAW,CAAC,GAAG;gBAAE,GAAG,IAAI,CAAChG;cAAkB,CAAE;cACpIb,WAAW,EAAEyI,CAAC,CAACU,qBAAqB,CAACxJ,MAAM,KAAK,IAAI,CAACuD,aAAa,CAACvD,MAAM;cACzED,YAAY,EAAE,EAAE;cAChB+B,eAAe,EAAEgH,CAAC,CAACG,OAAO,GAAG,IAAI,CAACjE,cAAc,CAAC8D,CAAC,CAACG,OAAO,EAAE,IAAI,CAAC3F,cAAc,CAAC,GAAG,IAAI,CAACA,cAAc,CAAC,CAAC;aACzG;UACH,CAAC,CAAC;QACJ,CAAC,MAAM;UACL,IAAI,CAAC8E,eAAe,EAAE;QACxB;MACF;IACF,CAAC,CAAC,CACH,CAAC1D,SAAS,EAAE;EACf;EAEA1C,mBAAmBA,CAAC0H,WAAgB;IAClC,IAAGA,WAAW,CAAC5H,eAAe,IAAI4H,WAAW,CAAC5H,eAAe,CAACqB,KAAK,KAAI,CAAC,EAAE;MACxEuG,WAAW,CAAC9H,cAAc,GAAG,CAAC;IAChC;EACF;EACA+H,4BAA4BA,CAAC/J,IAAW;IACtC,KAAK,IAAIsF,IAAI,IAAItF,IAAI,EAAE;MACrB,IAAIsF,IAAI,CAACZ,WAAW,KAAK,IAAI,CAACF,iCAAiC,CAACE,WAAW,EAAE;QAC3E,OAAOY,IAAI,CAAC0E,cAAc;MAC5B;IACF;IACA,OAAO,EAAE;EACX;EAIAC,oBAAoBA,CAACC,GAA4B;IAC/C,OAAOC,MAAM,CAACC,IAAI,CAACF,GAAG,CAAC,CAACxD,MAAM,CAACqB,GAAG,IAAImC,GAAG,CAACnC,GAAG,CAAC,CAAC;EACjD;EAEAsC,0BAA0BA,CAACH,GAA4B;IACrD,OAAOC,MAAM,CAACC,IAAI,CAACF,GAAG,CAAC,CACpBxD,MAAM,CAACqB,GAAG,IAAImC,GAAG,CAACnC,GAAG,CAAC,CAAC,CACvBuC,IAAI,CAAC,GAAG,CAAC;EACd;EAEAC,cAAcA,CAACrI,eAAoB,EAAEZ,kBAAuB;IAC1D,IAAIY,eAAe,IAAIA,eAAe,CAACqB,KAAK,IAAI,CAAC,EAAE;MACjD,OAAO,IAAI,CAAC8G,0BAA0B,CAAC/I,kBAAkB,CAAC;IAC5D;EACF;EAEAkJ,mBAAmBA,CAACC,WAAmB;IACrC,MAAMC,KAAK,GAAGD,WAAW,CAACrE,KAAK,CAAC,GAAG,CAAC;IACpC,IAAIsE,KAAK,CAACtK,MAAM,GAAG,CAAC,EAAE;MACpB,OAAOsK,KAAK,CAAC,CAAC,CAAC;IACjB,CAAC,MAAM,OAAO,EAAE;EAClB;EAEAC,UAAUA,CAACxK,YAAiB;IAC1B,IAAIA,YAAY,IAAIA,YAAY,CAACC,MAAM,GAAG,CAAC,EAAE;MAC3C,OAAO;QACLwK,YAAY,EAAE,IAAI,CAACJ,mBAAmB,CAACrK,YAAY,CAAC,CAAC,CAAC,CAACH,IAAI,CAAC,IAAI,IAAI;QACpE6K,aAAa,EAAE1K,YAAY,CAAC,CAAC,CAAC,CAACkG,SAAS,IAAI,IAAI;QAChDyE,QAAQ,EAAE3K,YAAY,CAAC,CAAC,CAAC,CAACoG,KAAK,CAACxG,IAAI,IAAII,YAAY,CAAC,CAAC,CAAC,CAACJ,IAAI,IAAI;OACjE;IACH,CAAC,MAAM,OAAOuD,SAAS;EAEzB;EAGAyH,UAAUA,CAAA;IACR,IAAI,CAAC3G,KAAK,CAAC4G,KAAK,EAAE;IAClB,IAAIC,iBAAiB,GAAG,KAAK;IAC7B,IAAIC,wBAAwB,GAAG,KAAK;IACpC,IAAIC,kBAAkB,GAAG,KAAK;IAE9B,KAAK,MAAM7F,IAAI,IAAI,IAAI,CAAC8F,mBAAmB,EAAE;MAC3C,IAAI,CAACH,iBAAiB,IAAK,CAAC3F,IAAI,CAAC+D,OAAQ,EAAE;QACzC4B,iBAAiB,GAAG,IAAI;MAC1B;MACA,IAAI,CAACC,wBAAwB,IAAK,CAAC5F,IAAI,CAACtD,cAAe,EAAE;QACvDkJ,wBAAwB,GAAG,IAAI;MACjC;MACA,IAAI5F,IAAI,CAACiD,YAAY,IAAIjD,IAAI,CAACtD,cAAc,EAAE;QAC5C,IAAIsD,IAAI,CAACtD,cAAc,GAAGsD,IAAI,CAACiD,YAAY,IAAIjD,IAAI,CAACtD,cAAc,GAAG,CAAC,EAAE;UACtE,IAAI,CAACoC,KAAK,CAACiH,eAAe,CAAC,QAAQ,GAAG,MAAM,GAAG/F,IAAI,CAACiD,YAAY,GAAG,KAAKjD,IAAI,CAACxD,SAAS,IAAI,CAAC;QAC7F;MACF;MAEA,IAAI,CAACqJ,kBAAkB,IAAK,CAAC7F,IAAI,CAACxD,SAAU,EAAE;QAC5CqJ,kBAAkB,GAAG,IAAI;MAC3B;IACF;IACA,IAAIF,iBAAiB,EAAE;MACrB,IAAI,CAAC7G,KAAK,CAACiH,eAAe,CAAC,UAAU,GAAG,KAAK,CAAC;IAChD;IACA,IAAIH,wBAAwB,EAAE;MAC5B,IAAI,CAAC9G,KAAK,CAACiH,eAAe,CAAC,QAAQ,GAAG,QAAQ,CAAC;IACjD;IACA,IAAIF,kBAAkB,EAAE;MACtB,IAAI,CAAC/G,KAAK,CAACiH,eAAe,CAAC,YAAY,GAAG,KAAK,CAAC;IAClD;EACF;EAIAC,QAAQA,CAAA;IACN,IAAI,CAACF,mBAAmB,GAAG,IAAI,CAACnC,kBAAkB,CAACpB,GAAG,CAAE0D,CAAM,IAAI;MAChE,OAAO;QACLtL,cAAc,EAAEsL,CAAC,CAACtL,cAAc,GAAGsL,CAAC,CAACtL,cAAc,GAAG,IAAI;QAC1DsG,KAAK,EAAEgF,CAAC,CAACpL,YAAY,GAAG,IAAI,CAACwK,UAAU,CAACY,CAAC,CAACpL,YAAY,CAAC,GAAGmD,SAAS;QACnE6F,kBAAkB,EAAE,IAAI,CAACc,oBAAoB,CAACsB,CAAC,CAACtK,aAAa,CAAC;QAC9D0I,WAAW,EAAE4B,CAAC,CAAC5B,WAAW,GAAG4B,CAAC,CAAC5B,WAAW,GAAG,IAAI;QACjD6B,OAAO,EAAE,IAAI,CAAC7G,KAAK,GAAG,IAAI,GAAG,IAAI,CAAC/D,YAAY,CAACwI,OAAO;QACtDlG,KAAK,EAAEqI,CAAC,CAACrI,KAAK;QACdC,KAAK,EAAEoI,CAAC,CAACpI,KAAK;QACdC,SAAS,EAAEmI,CAAC,CAACnI,SAAS;QACtBtB,SAAS,EAAEyJ,CAAC,CAACzJ,SAAS;QAAE;QACxBwF,WAAW,EAAEiE,CAAC,CAACrJ,eAAe,CAACqB,KAAK,KAAK,CAAC,GAAG,IAAI,GAAG,IAAI,CAACgH,cAAc,CAACgB,CAAC,CAACrJ,eAAe,EAAEqJ,CAAC,CAACjK,kBAAkB,CAAC,IAAI,IAAI;QACxHiH,YAAY,EAAEgD,CAAC,CAAChD,YAAY;QAC5BvG,cAAc,EAAEuJ,CAAC,CAACvJ,cAAc;QAChCqH,OAAO,EAAEkC,CAAC,CAACrJ,eAAe,CAACqB;OAC5B;IACH,CAAC,CAAC;IACF,IAAI,CAACwH,UAAU,EAAE;IACjB,IAAI,IAAI,CAAC3G,KAAK,CAACqH,aAAa,CAACrL,MAAM,GAAG,CAAC,EAAE;MACvC,IAAI,CAAC4D,OAAO,CAAC0H,aAAa,CAAC,IAAI,CAACtH,KAAK,CAACqH,aAAa,CAAC;MACpD;IACF;IACA,IAAI,IAAI,CAAC9G,KAAK,EAAE;MACd,IAAI,CAACgH,kBAAkB,EAAE;IAE3B,CAAC,MAAM;MACL,IAAI,CAACC,gBAAgB,EAAE;IACzB;EACF;EAEAA,gBAAgBA,CAAA;IACd,IAAI,CAAC3H,gBAAgB,CAAC4H,oCAAoC,CAAC;MACzDnD,IAAI,EAAE,IAAI,CAAC0C;KACZ,CAAC,CAACtG,SAAS,CAACgE,GAAG,IAAG;MACjB,IAAIA,GAAG,CAACE,UAAU,IAAI,CAAC,EAAE;QACvB,IAAI,CAAChF,OAAO,CAAC8H,aAAa,CAAC,MAAM,CAAC;QAClC;QACA,IAAI,CAACC,MAAM,EAAE;MACf;IACF,CAAC,CAAC;EACJ;EAIAJ,kBAAkBA,CAAA;IAChB,IAAI,CAACK,iBAAiB,GAAG;MACvBrD,YAAY,EAAE,IAAI,CAACzD,WAAW;MAC9B+G,SAAS,EAAE,IAAI,CAACb,mBAAmB,IAAI,IAAI;MAC3C3G,SAAS,EAAE,IAAI,CAACD,iCAAiC,CAACC;KACnD;IAED,IAAI,CAACR,gBAAgB,CAACiI,sCAAsC,CAAC;MAC3DxD,IAAI,EAAE,IAAI,CAACsD;KACZ,CAAC,CAAClH,SAAS,CAACgE,GAAG,IAAG;MACjB,IAAIA,GAAG,CAACE,UAAU,IAAI,CAAC,EAAE;QACvB,IAAI,CAAChF,OAAO,CAAC8H,aAAa,CAAC,MAAM,CAAC;QAClC;QACA,IAAI,CAACC,MAAM,EAAE;MACf;IACF,CAAC,CAAC;EACJ;EAEAlC,0BAA0BA,CAACsC,CAAW,EAAEC,CAAkG;IACxI,MAAMC,CAAC,GAA+B,EAAE;IACxC,KAAK,MAAM/G,IAAI,IAAI6G,CAAC,EAAE;MACpB,MAAMG,YAAY,GAAGF,CAAC,CAACG,IAAI,CAACC,KAAK,IAAIA,KAAK,CAACC,UAAU,KAAKnH,IAAI,IAAIkH,KAAK,CAACE,SAAS,CAAC;MAClFL,CAAC,CAAC/G,IAAI,CAAC,GAAG,CAAC,CAACgH,YAAY;IAC1B;IACA,OAAOD,CAAC;EACV,CAAC,CAAC;EAKFlH,iCAAiCA,CAAA;IAC/B,IAAI,CAACjB,yBAAyB,CAACyI,8DAA8D,CAAC;MAC5FjE,IAAI,EAAE,IAAI,CAACxD;KACZ,CAAC,CAAC2D,IAAI,CACLnL,GAAG,CAACoL,GAAG,IAAG;MACR,IAAIA,GAAG,CAACC,OAAO,IAAID,GAAG,CAACE,UAAU,IAAI,CAAC,EAAE;QACtC,IAAI,CAACrF,aAAa,GAAG,IAAI,CAACoG,4BAA4B,CAACjB,GAAG,CAACC,OAAO,CAAC;QACnE,IAAI,CAACQ,eAAe,EAAE;MACxB;IACF,CAAC,CAAC,CACH,CAACzE,SAAS,EAAE;EACf;EACAiH,MAAMA,CAAA;IACJ,IAAI,CAACxH,aAAa,CAACiC,IAAI,CAAC;MACtBoG,MAAM;MACNC,OAAO,EAAE,IAAI,CAAC3H;KACf,CAAC;IACF,IAAI,CAACb,QAAQ,CAACyI,IAAI,EAAE;EACtB;;;uCApbWlJ,4CAA4C,EAAA9F,EAAA,CAAAiP,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAnP,EAAA,CAAAiP,iBAAA,CAAAG,EAAA,CAAAC,cAAA,GAAArP,EAAA,CAAAiP,iBAAA,CAAAK,EAAA,CAAAC,cAAA,GAAAvP,EAAA,CAAAiP,iBAAA,CAAAO,EAAA,CAAAC,eAAA,GAAAzP,EAAA,CAAAiP,iBAAA,CAAAO,EAAA,CAAAE,wBAAA,GAAA1P,EAAA,CAAAiP,iBAAA,CAAAU,EAAA,CAAAC,cAAA,GAAA5P,EAAA,CAAAiP,iBAAA,CAAAY,EAAA,CAAAC,gBAAA,GAAA9P,EAAA,CAAAiP,iBAAA,CAAAc,EAAA,CAAAC,QAAA,GAAAhQ,EAAA,CAAAiP,iBAAA,CAAAO,EAAA,CAAAS,eAAA,GAAAjQ,EAAA,CAAAiP,iBAAA,CAAAiB,EAAA,CAAAC,YAAA;IAAA;EAAA;;;YAA5CrK,4CAA4C;MAAAsK,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAAtQ,EAAA,CAAAuQ,0BAAA,EAAAvQ,EAAA,CAAAwQ,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,sDAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UC7CvD9Q,EADF,CAAAO,cAAA,iBAA0B,qBACR;UACdP,EAAA,CAAAC,SAAA,qBAAiC;UACnCD,EAAA,CAAAU,YAAA,EAAiB;UACjBV,EAAA,CAAAO,cAAA,mBAAc;UACZP,EAAA,CAAAC,SAAA,YAA0C;UAExCD,EADF,CAAAO,cAAA,aAA8B,YACO;UAAAP,EAAA,CAAAY,MAAA,4CAAO;UAAAZ,EAAA,CAAAU,YAAA,EAAK;UAE/CV,EAAA,CAAAQ,UAAA,IAAAwQ,oEAAA,4BAA8E;UAmPlFhR,EADE,CAAAU,YAAA,EAAM,EACO;UAEbV,EADF,CAAAO,cAAA,wBAAsD,iBACM;UAAnBP,EAAA,CAAAiB,UAAA,mBAAAgQ,+EAAA;YAAA,OAASF,GAAA,CAAA9C,MAAA,EAAQ;UAAA,EAAC;UACvDjO,EAAA,CAAAY,MAAA,sBACF;UAAAZ,EAAA,CAAAU,YAAA,EAAS;UACTV,EAAA,CAAAO,cAAA,iBAAkD;UAArBP,EAAA,CAAAiB,UAAA,mBAAAiQ,+EAAA;YAAA,OAASH,GAAA,CAAAvD,QAAA,EAAU;UAAA,EAAC;UAC/CxN,EAAA,CAAAY,MAAA,sBACF;UAEJZ,EAFI,CAAAU,YAAA,EAAS,EACM,EACT;;;UA5PkCV,EAAA,CAAAW,SAAA,GAAuB;UAAvBX,EAAA,CAAAE,UAAA,YAAA6Q,GAAA,CAAA5F,kBAAA,CAAuB;;;qBDkCvDzL,YAAY,EAAAqQ,EAAA,CAAAoB,OAAA,EAAApB,EAAA,CAAAqB,OAAA,EAAArB,EAAA,CAAAsB,IAAA,EAAExR,YAAY,EAAAyR,EAAA,CAAAC,oBAAA,EAAAD,EAAA,CAAAE,mBAAA,EAAAF,EAAA,CAAAG,eAAA,EAAAH,EAAA,CAAAI,OAAA,EAAAC,GAAA,CAAAC,eAAA,EAAAD,GAAA,CAAAE,mBAAA,EAAAF,GAAA,CAAAG,qBAAA,EAAAH,GAAA,CAAAI,qBAAA,EAAAJ,GAAA,CAAAK,mBAAA,EAAAL,GAAA,CAAAM,gBAAA,EAAAN,GAAA,CAAAO,iBAAA,EAAAP,GAAA,CAAAQ,iBAAA,EAAAC,GAAA,CAAAC,mBAAA,EAAAC,GAAA,CAAAC,YAAA,EAAE5S,gBAAgB;MAAA6S,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}