{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/Documents/salechange-product/adminFront/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { NbCardModule, NbCheckboxModule, NbInputModule, NbOptionModule, NbSelectModule, NbTabsetModule } from '@nebular/theme';\nimport { BaseComponent } from '../components/base/baseComponent';\nimport { takeUntilDestroyed } from '@angular/core/rxjs-interop';\nimport { BreadcrumbComponent } from '../components/breadcrumb/breadcrumb.component';\nimport { FormsModule } from '@angular/forms';\nimport { NgFor, NgIf } from '@angular/common';\nimport { PaginationComponent } from '../components/pagination/pagination.component';\nimport { StatusPipe } from 'src/app/@theme/pipes/mapping.pipe';\nimport { FormGroupComponent } from 'src/app/@theme/components/form-group/form-group.component';\nimport { NumberWithCommasPipe } from 'src/app/@theme/pipes/number-with-commas.pipe';\nimport { EnumHouseType } from 'src/app/shared/enum/enumHouseType';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/shared/helper/allowHelper\";\nimport * as i2 from \"src/app/shared/helper/enumHelper\";\nimport * as i3 from \"@nebular/theme\";\nimport * as i4 from \"src/app/shared/services/message.service\";\nimport * as i5 from \"src/app/shared/helper/validationHelper\";\nimport * as i6 from \"src/services/api/services\";\nimport * as i7 from \"src/app/shared/helper/petternHelper\";\nimport * as i8 from \"@angular/router\";\nimport * as i9 from \"@angular/forms\";\nconst _c0 = () => [];\nfunction RequirementManagementComponent_div_6_nb_option_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 17);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const case_r4 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", case_r4.cID);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", case_r4.CBuildCaseName, \" \");\n  }\n}\nfunction RequirementManagementComponent_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 7)(1, \"label\", 39);\n    i0.ɵɵtext(2, \"\\u5EFA\\u6848\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"nb-select\", 16);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_div_6_Template_nb_select_ngModelChange_3_listener($event) {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.getListRequirementRequest.CBuildCaseID, $event) || (ctx_r2.getListRequirementRequest.CBuildCaseID = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtemplate(4, RequirementManagementComponent_div_6_nb_option_4_Template, 2, 2, \"nb-option\", 14);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.getListRequirementRequest.CBuildCaseID);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.buildCaseList);\n  }\n}\nfunction RequirementManagementComponent_nb_option_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 17);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const type_r5 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", type_r5.value);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", type_r5.label, \" \");\n  }\n}\nfunction RequirementManagementComponent_button_50_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 40);\n    i0.ɵɵlistener(\"click\", function RequirementManagementComponent_button_50_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r2 = i0.ɵɵnextContext();\n      const dialog_r7 = i0.ɵɵreference(108);\n      return i0.ɵɵresetView(ctx_r2.add(dialog_r7));\n    });\n    i0.ɵɵelement(1, \"i\", 41);\n    i0.ɵɵtext(2, \"\\u65B0\\u589E\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RequirementManagementComponent_tr_79_button_20_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 47);\n    i0.ɵɵlistener(\"click\", function RequirementManagementComponent_tr_79_button_20_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const data_r9 = i0.ɵɵnextContext().$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      const dialog_r7 = i0.ɵɵreference(108);\n      return i0.ɵɵresetView(ctx_r2.onEdit(data_r9, dialog_r7));\n    });\n    i0.ɵɵelement(1, \"i\", 48);\n    i0.ɵɵtext(2, \"\\u7DE8\\u8F2F\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RequirementManagementComponent_tr_79_button_21_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 49);\n    i0.ɵɵlistener(\"click\", function RequirementManagementComponent_tr_79_button_21_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const data_r9 = i0.ɵɵnextContext().$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onDelete(data_r9));\n    });\n    i0.ɵɵelement(1, \"i\", 50);\n    i0.ɵɵtext(2, \"\\u522A\\u9664\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RequirementManagementComponent_tr_79_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\", 42)(1, \"td\", 43);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\", 43);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\", 44);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"td\", 44);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"td\", 44);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"td\", 44);\n    i0.ɵɵtext(12);\n    i0.ɵɵpipe(13, \"getStatusName\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"td\", 44);\n    i0.ɵɵtext(15);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"td\", 44);\n    i0.ɵɵtext(17);\n    i0.ɵɵpipe(18, \"ngxNumberWithCommas\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"td\", 43);\n    i0.ɵɵtemplate(20, RequirementManagementComponent_tr_79_button_20_Template, 3, 0, \"button\", 45)(21, RequirementManagementComponent_tr_79_button_21_Template, 3, 0, \"button\", 46);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const data_r9 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(data_r9.CBuildCaseName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(data_r9.CRequirement);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(data_r9.CGroupName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r2.getHouseType(data_r9.CHouseType || i0.ɵɵpureFunction0(14, _c0)));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(data_r9.CSort);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(13, 10, data_r9.CStatus));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r2.getCIsShowText(data_r9));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(18, 12, data_r9.CUnitPrice || 0));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isUpdate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isDelete);\n  }\n}\nfunction RequirementManagementComponent_tr_105_button_18_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 47);\n    i0.ɵɵlistener(\"click\", function RequirementManagementComponent_tr_105_button_18_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const data_r12 = i0.ɵɵnextContext().$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      const dialog_r7 = i0.ɵɵreference(108);\n      return i0.ɵɵresetView(ctx_r2.onEdit(data_r12, dialog_r7));\n    });\n    i0.ɵɵelement(1, \"i\", 48);\n    i0.ɵɵtext(2, \"\\u7DE8\\u8F2F\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RequirementManagementComponent_tr_105_button_19_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r13 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 49);\n    i0.ɵɵlistener(\"click\", function RequirementManagementComponent_tr_105_button_19_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r13);\n      const data_r12 = i0.ɵɵnextContext().$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onDelete(data_r12));\n    });\n    i0.ɵɵelement(1, \"i\", 50);\n    i0.ɵɵtext(2, \"\\u522A\\u9664\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RequirementManagementComponent_tr_105_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\", 42)(1, \"td\", 43);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\", 43);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\", 44);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"td\", 44);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"td\", 44);\n    i0.ɵɵtext(10);\n    i0.ɵɵpipe(11, \"getStatusName\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"td\", 44);\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"td\", 44);\n    i0.ɵɵtext(15);\n    i0.ɵɵpipe(16, \"ngxNumberWithCommas\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"td\", 51);\n    i0.ɵɵtemplate(18, RequirementManagementComponent_tr_105_button_18_Template, 3, 0, \"button\", 45)(19, RequirementManagementComponent_tr_105_button_19_Template, 3, 0, \"button\", 46);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const data_r12 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(data_r12.CRequirement);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(data_r12.CGroupName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r2.getHouseType(data_r12.CHouseType || i0.ɵɵpureFunction0(13, _c0)));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(data_r12.CSort);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(11, 9, data_r12.CStatus));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r2.getCIsShowText(data_r12));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(16, 11, data_r12.CUnitPrice || 0));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isUpdate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isDelete);\n  }\n}\nfunction RequirementManagementComponent_ng_template_107_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"\\u65B0\\u589E\\u5EFA\\u6848\\u9700\\u6C42\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RequirementManagementComponent_ng_template_107_span_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"\\u7DE8\\u8F2F\\u5EFA\\u6848\\u9700\\u6C42\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RequirementManagementComponent_ng_template_107_span_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"\\u65B0\\u589E\\u6A21\\u677F\\u9700\\u6C42\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RequirementManagementComponent_ng_template_107_span_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"\\u7DE8\\u8F2F\\u6A21\\u677F\\u9700\\u6C42\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RequirementManagementComponent_ng_template_107_app_form_group_10_nb_option_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 71);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const b_r16 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", b_r16.cID);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", b_r16.CBuildCaseName, \"\");\n  }\n}\nfunction RequirementManagementComponent_ng_template_107_app_form_group_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r15 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"app-form-group\", 57)(1, \"nb-select\", 70);\n    i0.ɵɵtwoWayListener(\"selectedChange\", function RequirementManagementComponent_ng_template_107_app_form_group_10_Template_nb_select_selectedChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r15);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r2.saveRequirement.CBuildCaseID, $event) || (ctx_r2.saveRequirement.CBuildCaseID = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtemplate(2, RequirementManagementComponent_ng_template_107_app_form_group_10_nb_option_2_Template, 2, 2, \"nb-option\", 62);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"label\", \"\\u5EFA\\u6848\\u540D\\u7A31\")(\"labelFor\", \"CBuildCaseID\")(\"isRequired\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"selected\", ctx_r2.saveRequirement.CBuildCaseID);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.buildCaseList);\n  }\n}\nfunction RequirementManagementComponent_ng_template_107_nb_option_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 71);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const type_r17 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", type_r17.value);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", type_r17.label, \"\");\n  }\n}\nfunction RequirementManagementComponent_ng_template_107_nb_option_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 71);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const status_r18 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", status_r18.value);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", status_r18.label, \"\");\n  }\n}\nfunction RequirementManagementComponent_ng_template_107_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r14 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-card\", 52)(1, \"nb-card-header\");\n    i0.ɵɵtemplate(2, RequirementManagementComponent_ng_template_107_span_2_Template, 2, 0, \"span\", 53)(3, RequirementManagementComponent_ng_template_107_span_3_Template, 2, 0, \"span\", 53)(4, RequirementManagementComponent_ng_template_107_span_4_Template, 2, 0, \"span\", 53)(5, RequirementManagementComponent_ng_template_107_span_5_Template, 2, 0, \"span\", 53);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"nb-card-body\", 54)(7, \"div\", 5)(8, \"div\", 55)(9, \"div\", 5);\n    i0.ɵɵtemplate(10, RequirementManagementComponent_ng_template_107_app_form_group_10_Template, 3, 5, \"app-form-group\", 56);\n    i0.ɵɵelementStart(11, \"app-form-group\", 57)(12, \"input\", 58);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_ng_template_107_Template_input_ngModelChange_12_listener($event) {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.saveRequirement.CRequirement, $event) || (ctx_r2.saveRequirement.CRequirement = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"app-form-group\", 57)(14, \"input\", 59);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_ng_template_107_Template_input_ngModelChange_14_listener($event) {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.saveRequirement.CGroupName, $event) || (ctx_r2.saveRequirement.CGroupName = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(15, \"app-form-group\", 57)(16, \"input\", 60);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_ng_template_107_Template_input_ngModelChange_16_listener($event) {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.saveRequirement.CSort, $event) || (ctx_r2.saveRequirement.CSort = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(17, \"app-form-group\", 57)(18, \"nb-select\", 61);\n    i0.ɵɵtwoWayListener(\"selectedChange\", function RequirementManagementComponent_ng_template_107_Template_nb_select_selectedChange_18_listener($event) {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.saveRequirement.CHouseType, $event) || (ctx_r2.saveRequirement.CHouseType = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtemplate(19, RequirementManagementComponent_ng_template_107_nb_option_19_Template, 2, 2, \"nb-option\", 62);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(20, \"app-form-group\", 57)(21, \"nb-select\", 63);\n    i0.ɵɵtwoWayListener(\"selectedChange\", function RequirementManagementComponent_ng_template_107_Template_nb_select_selectedChange_21_listener($event) {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.saveRequirement.CStatus, $event) || (ctx_r2.saveRequirement.CStatus = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtemplate(22, RequirementManagementComponent_ng_template_107_nb_option_22_Template, 2, 2, \"nb-option\", 62);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(23, \"app-form-group\", 57)(24, \"input\", 64);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_ng_template_107_Template_input_ngModelChange_24_listener($event) {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.saveRequirement.CUnitPrice, $event) || (ctx_r2.saveRequirement.CUnitPrice = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(25, \"app-form-group\", 57)(26, \"input\", 65);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_ng_template_107_Template_input_ngModelChange_26_listener($event) {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.saveRequirement.CUnit, $event) || (ctx_r2.saveRequirement.CUnit = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(27, \"app-form-group\", 57)(28, \"nb-checkbox\", 66);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_ng_template_107_Template_nb_checkbox_ngModelChange_28_listener($event) {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.saveRequirement.CIsShow, $event) || (ctx_r2.saveRequirement.CIsShow = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtext(29, \" \\u986F\\u793A\\u5728\\u5BA2\\u8B8A\\u9700\\u6C42 \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(30, \"app-form-group\", 57)(31, \"textarea\", 67);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_ng_template_107_Template_textarea_ngModelChange_31_listener($event) {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.saveRequirement.CRemark, $event) || (ctx_r2.saveRequirement.CRemark = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()()()()()();\n    i0.ɵɵelementStart(32, \"nb-card-footer\")(33, \"div\", 5)(34, \"div\", 68)(35, \"button\", 40);\n    i0.ɵɵlistener(\"click\", function RequirementManagementComponent_ng_template_107_Template_button_click_35_listener() {\n      const ref_r19 = i0.ɵɵrestoreView(_r14).dialogRef;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.save(ref_r19));\n    });\n    i0.ɵɵtext(36, \"\\u78BA\\u5B9A\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(37, \"button\", 69);\n    i0.ɵɵlistener(\"click\", function RequirementManagementComponent_ng_template_107_Template_button_click_37_listener() {\n      const ref_r19 = i0.ɵɵrestoreView(_r14).dialogRef;\n      return i0.ɵɵresetView(ref_r19.close());\n    });\n    i0.ɵɵtext(38, \"\\u53D6\\u6D88\");\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isNew === true && ctx_r2.currentTab === 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isNew === false && ctx_r2.currentTab === 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isNew === true && ctx_r2.currentTab === 1);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isNew === false && ctx_r2.currentTab === 1);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.currentTab === 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"label\", \"\\u5DE5\\u7A0B\\u9805\\u76EE\")(\"labelFor\", \"CRequirement\")(\"isRequired\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.saveRequirement.CRequirement);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"label\", \"\\u7FA4\\u7D44\\u985E\\u5225\")(\"labelFor\", \"CGroupName\")(\"isRequired\", false);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.saveRequirement.CGroupName);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"label\", \"\\u6392\\u5E8F\")(\"labelFor\", \"CSort\")(\"isRequired\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.saveRequirement.CSort);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"label\", \"\\u985E\\u578B\")(\"labelFor\", \"CHouseType\")(\"isRequired\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"selected\", ctx_r2.saveRequirement.CHouseType);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.houseType);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"label\", \"\\u72C0\\u614B\")(\"labelFor\", \"CStatus\")(\"isRequired\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"selected\", ctx_r2.saveRequirement.CStatus);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.statusOptions);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"label\", \"\\u55AE\\u50F9\")(\"labelFor\", \"CUnitPrice\")(\"isRequired\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.saveRequirement.CUnitPrice);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"label\", \"\\u55AE\\u4F4D\")(\"labelFor\", \"CUnit\")(\"isRequired\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.saveRequirement.CUnit);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"label\", \"\\u5BA2\\u8B8A\\u9700\\u6C42\\u986F\\u793A\")(\"labelFor\", \"CIsShow\")(\"isRequired\", false);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.saveRequirement.CIsShow);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"label\", \"\\u5099\\u8A3B\\u8AAA\\u660E\")(\"labelFor\", \"CRemark\")(\"isRequired\", false);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.saveRequirement.CRemark);\n  }\n}\nfunction RequirementManagementComponent_ng_template_109_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"\\u65B0\\u589E\\u6A21\\u677F\\u9700\\u6C42\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RequirementManagementComponent_ng_template_109_span_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"\\u7DE8\\u8F2F\\u6A21\\u677F\\u9700\\u6C42\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RequirementManagementComponent_ng_template_109_nb_option_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 71);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const type_r21 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", type_r21.value);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", type_r21.label, \"\");\n  }\n}\nfunction RequirementManagementComponent_ng_template_109_nb_option_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 71);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const status_r22 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", status_r22.value);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", status_r22.label, \"\");\n  }\n}\nfunction RequirementManagementComponent_ng_template_109_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r20 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-card\", 52)(1, \"nb-card-header\");\n    i0.ɵɵtemplate(2, RequirementManagementComponent_ng_template_109_span_2_Template, 2, 0, \"span\", 53)(3, RequirementManagementComponent_ng_template_109_span_3_Template, 2, 0, \"span\", 53);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"nb-card-body\", 54)(5, \"div\", 5)(6, \"div\", 55)(7, \"div\", 5)(8, \"app-form-group\", 57)(9, \"input\", 58);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_ng_template_109_Template_input_ngModelChange_9_listener($event) {\n      i0.ɵɵrestoreView(_r20);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.saveRequirement.CRequirement, $event) || (ctx_r2.saveRequirement.CRequirement = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"app-form-group\", 57)(11, \"input\", 59);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_ng_template_109_Template_input_ngModelChange_11_listener($event) {\n      i0.ɵɵrestoreView(_r20);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.saveRequirement.CGroupName, $event) || (ctx_r2.saveRequirement.CGroupName = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(12, \"app-form-group\", 57)(13, \"input\", 60);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_ng_template_109_Template_input_ngModelChange_13_listener($event) {\n      i0.ɵɵrestoreView(_r20);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.saveRequirement.CSort, $event) || (ctx_r2.saveRequirement.CSort = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(14, \"app-form-group\", 57)(15, \"nb-select\", 61);\n    i0.ɵɵtwoWayListener(\"selectedChange\", function RequirementManagementComponent_ng_template_109_Template_nb_select_selectedChange_15_listener($event) {\n      i0.ɵɵrestoreView(_r20);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.saveRequirement.CHouseType, $event) || (ctx_r2.saveRequirement.CHouseType = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtemplate(16, RequirementManagementComponent_ng_template_109_nb_option_16_Template, 2, 2, \"nb-option\", 62);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(17, \"app-form-group\", 57)(18, \"nb-select\", 63);\n    i0.ɵɵtwoWayListener(\"selectedChange\", function RequirementManagementComponent_ng_template_109_Template_nb_select_selectedChange_18_listener($event) {\n      i0.ɵɵrestoreView(_r20);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.saveRequirement.CStatus, $event) || (ctx_r2.saveRequirement.CStatus = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtemplate(19, RequirementManagementComponent_ng_template_109_nb_option_19_Template, 2, 2, \"nb-option\", 62);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(20, \"app-form-group\", 57)(21, \"input\", 64);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_ng_template_109_Template_input_ngModelChange_21_listener($event) {\n      i0.ɵɵrestoreView(_r20);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.saveRequirement.CUnitPrice, $event) || (ctx_r2.saveRequirement.CUnitPrice = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(22, \"app-form-group\", 57)(23, \"input\", 65);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_ng_template_109_Template_input_ngModelChange_23_listener($event) {\n      i0.ɵɵrestoreView(_r20);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.saveRequirement.CUnit, $event) || (ctx_r2.saveRequirement.CUnit = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(24, \"app-form-group\", 57)(25, \"nb-checkbox\", 66);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_ng_template_109_Template_nb_checkbox_ngModelChange_25_listener($event) {\n      i0.ɵɵrestoreView(_r20);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.saveRequirement.CIsShow, $event) || (ctx_r2.saveRequirement.CIsShow = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtext(26, \" \\u986F\\u793A\\u5728\\u5BA2\\u8B8A\\u9700\\u6C42 \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(27, \"app-form-group\", 57)(28, \"textarea\", 67);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_ng_template_109_Template_textarea_ngModelChange_28_listener($event) {\n      i0.ɵɵrestoreView(_r20);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.saveRequirement.CRemark, $event) || (ctx_r2.saveRequirement.CRemark = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()()()()()();\n    i0.ɵɵelementStart(29, \"nb-card-footer\")(30, \"div\", 5)(31, \"div\", 68)(32, \"button\", 40);\n    i0.ɵɵlistener(\"click\", function RequirementManagementComponent_ng_template_109_Template_button_click_32_listener() {\n      const ref_r23 = i0.ɵɵrestoreView(_r20).dialogRef;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.saveTemplate(ref_r23));\n    });\n    i0.ɵɵtext(33, \"\\u78BA\\u5B9A\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(34, \"button\", 69);\n    i0.ɵɵlistener(\"click\", function RequirementManagementComponent_ng_template_109_Template_button_click_34_listener() {\n      const ref_r23 = i0.ɵɵrestoreView(_r20).dialogRef;\n      return i0.ɵɵresetView(ref_r23.close());\n    });\n    i0.ɵɵtext(35, \"\\u53D6\\u6D88\");\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isNew === true);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isNew === false);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"label\", \"\\u5DE5\\u7A0B\\u9805\\u76EE\")(\"labelFor\", \"CRequirement\")(\"isRequired\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.saveRequirement.CRequirement);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"label\", \"\\u7FA4\\u7D44\\u985E\\u5225\")(\"labelFor\", \"CGroupName\")(\"isRequired\", false);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.saveRequirement.CGroupName);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"label\", \"\\u6392\\u5E8F\")(\"labelFor\", \"CSort\")(\"isRequired\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.saveRequirement.CSort);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"label\", \"\\u985E\\u578B\")(\"labelFor\", \"CHouseType\")(\"isRequired\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"selected\", ctx_r2.saveRequirement.CHouseType);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.houseType);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"label\", \"\\u72C0\\u614B\")(\"labelFor\", \"CStatus\")(\"isRequired\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"selected\", ctx_r2.saveRequirement.CStatus);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.statusOptions);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"label\", \"\\u55AE\\u50F9\")(\"labelFor\", \"CUnitPrice\")(\"isRequired\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.saveRequirement.CUnitPrice);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"label\", \"\\u55AE\\u4F4D\")(\"labelFor\", \"CUnit\")(\"isRequired\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.saveRequirement.CUnit);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"label\", \"\\u5BA2\\u8B8A\\u9700\\u6C42\\u986F\\u793A\")(\"labelFor\", \"CIsShow\")(\"isRequired\", false);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.saveRequirement.CIsShow);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"label\", \"\\u5099\\u8A3B\\u8AAA\\u660E\")(\"labelFor\", \"CRemark\")(\"isRequired\", false);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.saveRequirement.CRemark);\n  }\n}\nexport class RequirementManagementComponent extends BaseComponent {\n  constructor(_allow, enumHelper, dialogService, message, valid, buildCaseService, requirementService, pettern, router, destroyref) {\n    super(_allow);\n    this._allow = _allow;\n    this.enumHelper = enumHelper;\n    this.dialogService = dialogService;\n    this.message = message;\n    this.valid = valid;\n    this.buildCaseService = buildCaseService;\n    this.requirementService = requirementService;\n    this.pettern = pettern;\n    this.router = router;\n    this.destroyref = destroyref;\n    // request\n    this.getListRequirementRequest = {};\n    this.getRequirementRequest = {};\n    // response\n    this.buildCaseList = [];\n    this.requirementList = [];\n    this.saveRequirement = {\n      CHouseType: []\n    };\n    this.statusOptions = [{\n      value: 0,\n      label: '停用'\n    }, {\n      value: 1,\n      label: '啟用'\n    }];\n    this.houseType = this.enumHelper.getEnumOptions(EnumHouseType);\n    this.isNew = false;\n    this.currentBuildCase = 0;\n    this.currentTab = 0; // 追蹤當前選中的 tab\n    this.initializeSearchForm();\n    this.getBuildCaseList();\n  }\n  ngOnInit() {}\n  // 初始化搜尋表單\n  initializeSearchForm() {\n    this.getListRequirementRequest.CBuildCaseID = -1;\n    this.getListRequirementRequest.CStatus = -1;\n    this.getListRequirementRequest.CIsShow = null;\n    this.getListRequirementRequest.CRequirement = '';\n    this.getListRequirementRequest.CGroupName = '';\n    // 預設全選所有房屋類型\n    this.getListRequirementRequest.CHouseType = this.houseType.map(type => type.value);\n  }\n  // 重置搜尋\n  resetSearch() {\n    this.initializeSearchForm();\n    // 重置後如果有建案資料，重新設定預設選擇第一個建案\n    if (this.buildCaseList && this.buildCaseList.length > 0) {\n      setTimeout(() => {\n        this.getListRequirementRequest.CBuildCaseID = this.buildCaseList[0].cID;\n        this.getList();\n      }, 0);\n    } else {\n      this.getList();\n    }\n  }\n  getHouseType(hTypes) {\n    if (!hTypes) {\n      return '';\n    }\n    if (!Array.isArray(hTypes)) {\n      hTypes = [hTypes];\n    }\n    let labels = [];\n    hTypes.forEach(htype => {\n      let findH = this.houseType.find(x => x.value == htype);\n      if (findH) {\n        labels.push(findH.label);\n      }\n    });\n    return labels.join(', ');\n  }\n  validation() {\n    this.valid.clear();\n    // 根據當前 tab 決定是否需要驗證建案名稱\n    if (this.currentTab === 0) {\n      // 建案頁面需要驗證建案名稱\n      this.valid.required('[建案名稱]', this.saveRequirement.CBuildCaseID);\n    }\n    this.valid.required('[需求]', this.saveRequirement.CRequirement);\n    this.valid.required('[地主戶]', this.saveRequirement.CHouseType);\n    this.valid.required('[安排]', this.saveRequirement.CSort);\n    this.valid.required('[狀態]', this.saveRequirement.CStatus);\n    this.valid.required('[單價]', this.saveRequirement.CUnitPrice);\n    this.valid.required('[單位]', this.saveRequirement.CUnit);\n    // 群組名稱長度驗證\n    if (this.saveRequirement.CGroupName && this.saveRequirement.CGroupName.length > 20) {\n      this.valid.errorMessages.push('[群組名稱] 不能超過20個字');\n    }\n    // 備註說明長度驗證\n    if (this.saveRequirement.CRemark && this.saveRequirement.CRemark.length > 100) {\n      this.valid.errorMessages.push('[備註說明] 不能超過100個字');\n    }\n  }\n  add(dialog) {\n    this.isNew = true;\n    this.saveRequirement = {\n      CHouseType: [],\n      CIsShow: false\n    };\n    this.saveRequirement.CStatus = 1;\n    this.saveRequirement.CUnitPrice = 0;\n    // 根據當前 tab 決定是否需要建案ID\n    if (this.currentTab === 0) {\n      // 建案頁面 - 使用當前選擇的建案或第一個建案\n      if (this.currentBuildCase != 0) {\n        this.saveRequirement.CBuildCaseID = this.currentBuildCase;\n      } else if (this.buildCaseList && this.buildCaseList.length > 0) {\n        this.saveRequirement.CBuildCaseID = this.buildCaseList[0].cID;\n      }\n    } else {\n      // 模板頁面 - 設定建案ID為0\n      this.saveRequirement.CBuildCaseID = 0;\n    }\n    this.dialogService.open(dialog);\n  }\n  onEdit(data, dialog) {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      _this.getRequirementRequest.CRequirementID = data.CRequirementID;\n      _this.isNew = false;\n      try {\n        yield _this.getData();\n        _this.dialogService.open(dialog);\n      } catch (error) {\n        console.log(\"Failed to get function data\", error);\n      }\n    })();\n  }\n  save(ref) {\n    this.validation();\n    if (this.valid.errorMessages.length > 0) {\n      this.message.showErrorMSGs(this.valid.errorMessages);\n      return;\n    }\n    // 如果是模板頁面，確保 CBuildCaseID 設定為 0\n    if (this.currentTab === 1) {\n      this.saveRequirement.CBuildCaseID = 0;\n    }\n    this.requirementService.apiRequirementSaveDataPost$Json({\n      body: this.saveRequirement\n    }).subscribe(res => {\n      if (res.StatusCode === 0) {\n        this.message.showSucessMSG('執行成功');\n        this.getList();\n      } else {\n        this.message.showErrorMSG(res.Message);\n      }\n    });\n    ref.close();\n  }\n  onDelete(data) {\n    this.saveRequirement.CRequirementID = data.CRequirementID;\n    this.isNew = false;\n    if (window.confirm('是否確定刪除?')) {\n      this.remove();\n    } else {\n      return;\n    }\n  }\n  remove() {\n    this.requirementService.apiRequirementDeleteDataPost$Json({\n      body: {\n        CRequirementID: this.saveRequirement.CRequirementID\n      }\n    }).subscribe(res => {\n      this.message.showSucessMSG('執行成功');\n      this.getList();\n    });\n  }\n  getBuildCaseList() {\n    this.buildCaseService.apiBuildCaseGetUserBuildCasePost$Json({\n      body: {}\n    }).pipe(takeUntilDestroyed(this.destroyref)).subscribe(res => {\n      this.buildCaseList = res.Entries;\n      // 載入建案列表後，如果有建案資料，設定預設值並查詢一次\n      if (this.buildCaseList.length > 0) {\n        this.getListRequirementRequest.CBuildCaseID = this.buildCaseList[0].cID;\n      }\n      this.getList();\n    });\n  }\n  getList() {\n    this.getListRequirementRequest.PageSize = this.pageSize;\n    this.getListRequirementRequest.PageIndex = this.pageIndex;\n    this.requirementList = [];\n    this.totalRecords = 0;\n    // 當選擇模板頁面時，強制設定 CBuildCaseID = 0 且不可異動\n    if (this.currentTab === 1) {\n      this.getListRequirementRequest.CBuildCaseID = 0;\n    } else {\n      // 建案頁面的邏輯保持不變\n      if (this.getListRequirementRequest.CBuildCaseID && this.getListRequirementRequest.CBuildCaseID != 0) {\n        this.currentBuildCase = this.getListRequirementRequest.CBuildCaseID;\n      }\n    }\n    this.requirementService.apiRequirementGetListPost$Json({\n      body: this.getListRequirementRequest\n    }).pipe().subscribe(res => {\n      if (res.StatusCode == 0) {\n        if (res.Entries) {\n          this.requirementList = res.Entries;\n          this.totalRecords = res.TotalItems;\n        }\n      }\n    });\n  }\n  getData() {\n    this.requirementService.apiRequirementGetDataPost$Json({\n      body: this.getRequirementRequest\n    }).pipe().subscribe(res => {\n      if (res.StatusCode == 0) {\n        if (res.Entries) {\n          this.saveRequirement = {\n            CHouseType: [],\n            CIsShow: false\n          };\n          this.saveRequirement.CBuildCaseID = res.Entries.CBuildCaseID;\n          this.saveRequirement.CGroupName = res.Entries.CGroupName;\n          this.saveRequirement.CHouseType = res.Entries.CHouseType ? Array.isArray(res.Entries.CHouseType) ? res.Entries.CHouseType : [res.Entries.CHouseType] : [];\n          this.saveRequirement.CRemark = res.Entries.CRemark;\n          this.saveRequirement.CRequirement = res.Entries.CRequirement;\n          this.saveRequirement.CRequirementID = res.Entries.CRequirementID;\n          this.saveRequirement.CSort = res.Entries.CSort;\n          this.saveRequirement.CStatus = res.Entries.CStatus;\n          this.saveRequirement.CUnitPrice = res.Entries.CUnitPrice;\n          this.saveRequirement.CUnit = res.Entries.CUnit;\n          // TODO: 等後端API更新後啟用這行\n          this.saveRequirement.CIsShow = res.Entries.CIsShow || false;\n        }\n      }\n    });\n  }\n  onHouseTypeChange(value, checked) {\n    console.log(checked);\n    if (checked) {\n      if (!this.saveRequirement.CHouseType?.includes(value)) {\n        this.saveRequirement.CHouseType?.push(value);\n      }\n      console.log(this.saveRequirement.CHouseType);\n    } else {\n      this.saveRequirement.CHouseType = this.saveRequirement.CHouseType?.filter(v => v !== value);\n    }\n  }\n  getCIsShowText(data) {\n    return data.CIsShow ? '是' : '否';\n  }\n  // Tab 切換事件處理\n  onTabChange(event) {\n    // 根據 tabTitle 來判斷當前頁面\n    if (event.tabTitle === '共用') {\n      this.currentTab = 1;\n      this.getListRequirementRequest.CBuildCaseID = 0;\n    } else {\n      this.currentTab = 0;\n      // 切換回建案頁面時，如果沒有選擇建案且有建案資料，預設選擇第一個\n      if ((this.getListRequirementRequest.CBuildCaseID === 0 || this.getListRequirementRequest.CBuildCaseID === -1) && this.buildCaseList && this.buildCaseList.length > 0) {\n        this.getListRequirementRequest.CBuildCaseID = this.buildCaseList[0].cID;\n      }\n    }\n    // 切換頁籤後重新載入列表\n    this.getList();\n  }\n  // 新增模板\n  addTemplate(dialog) {\n    this.isNew = true;\n    this.saveRequirement = {\n      CHouseType: [],\n      CIsShow: false\n    };\n    this.saveRequirement.CStatus = 1;\n    this.saveRequirement.CUnitPrice = 0;\n    // 模板設定建案ID為0\n    this.saveRequirement.CBuildCaseID = 0;\n    this.dialogService.open(dialog);\n  }\n  // 編輯模板\n  onEditTemplate(data, dialog) {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      _this2.getRequirementRequest.CRequirementID = data.CRequirementID;\n      _this2.isNew = false;\n      try {\n        yield _this2.getData();\n        _this2.dialogService.open(dialog);\n      } catch (error) {\n        console.log(\"Failed to get template data\", error);\n      }\n    })();\n  }\n  // 保存模板\n  saveTemplate(ref) {\n    // 模板驗證（不包含建案名稱）\n    this.valid.clear();\n    this.valid.required('[需求]', this.saveRequirement.CRequirement);\n    this.valid.required('[地主戶]', this.saveRequirement.CHouseType);\n    this.valid.required('[安排]', this.saveRequirement.CSort);\n    this.valid.required('[狀態]', this.saveRequirement.CStatus);\n    this.valid.required('[單價]', this.saveRequirement.CUnitPrice);\n    this.valid.required('[單位]', this.saveRequirement.CUnit);\n    // 群組名稱長度驗證\n    if (this.saveRequirement.CGroupName && this.saveRequirement.CGroupName.length > 20) {\n      this.valid.errorMessages.push('[群組名稱] 不能超過20個字');\n    }\n    // 備註說明長度驗證\n    if (this.saveRequirement.CRemark && this.saveRequirement.CRemark.length > 100) {\n      this.valid.errorMessages.push('[備註說明] 不能超過100個字');\n    }\n    if (this.valid.errorMessages.length > 0) {\n      this.message.showErrorMSGs(this.valid.errorMessages);\n      return;\n    }\n    // 確保模板建案ID為0\n    const templateData = {\n      ...this.saveRequirement\n    };\n    templateData.CBuildCaseID = 0;\n    this.requirementService.apiRequirementSaveDataPost$Json({\n      body: templateData\n    }).subscribe(res => {\n      if (res.StatusCode === 0) {\n        this.message.showSucessMSG('執行成功');\n        this.getList();\n      } else {\n        this.message.showErrorMSG(res.Message);\n      }\n    });\n    ref.close();\n  }\n  static {\n    this.ɵfac = function RequirementManagementComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || RequirementManagementComponent)(i0.ɵɵdirectiveInject(i1.AllowHelper), i0.ɵɵdirectiveInject(i2.EnumHelper), i0.ɵɵdirectiveInject(i3.NbDialogService), i0.ɵɵdirectiveInject(i4.MessageService), i0.ɵɵdirectiveInject(i5.ValidationHelper), i0.ɵɵdirectiveInject(i6.BuildCaseService), i0.ɵɵdirectiveInject(i6.RequirementService), i0.ɵɵdirectiveInject(i7.PetternHelper), i0.ɵɵdirectiveInject(i8.Router), i0.ɵɵdirectiveInject(i0.DestroyRef));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: RequirementManagementComponent,\n      selectors: [[\"app-requirement-management\"]],\n      standalone: true,\n      features: [i0.ɵɵInheritDefinitionFeature, i0.ɵɵStandaloneFeature],\n      decls: 111,\n      vars: 22,\n      consts: [[\"dialog\", \"\"], [\"templateDialog\", \"\"], [\"accent\", \"success\"], [1, \"bg-white\", \"pb-0\"], [1, \"col-12\"], [1, \"row\"], [\"class\", \"form-group col-12 col-md-4\", 4, \"ngIf\"], [1, \"form-group\", \"col-12\", \"col-md-4\"], [\"for\", \"requirement\", 1, \"label\", \"mr-2\"], [\"type\", \"text\", \"nbInput\", \"\", \"id\", \"requirement\", \"name\", \"requirement\", \"placeholder\", \"\\u5DE5\\u7A0B\\u9805\\u76EE\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"groupName\", 1, \"label\", \"mr-2\"], [\"type\", \"text\", \"nbInput\", \"\", \"id\", \"groupName\", \"name\", \"groupName\", \"placeholder\", \"\\u7FA4\\u7D44\\u985E\\u5225\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"houseType\", 1, \"label\", \"mr-2\"], [\"multiple\", \"\", 1, \"col-9\", 3, \"ngModelChange\", \"ngModel\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"for\", \"status\", 1, \"label\", \"mr-2\"], [1, \"col-9\", 3, \"ngModelChange\", \"ngModel\"], [3, \"value\"], [\"for\", \"isShow\", 1, \"label\", \"mr-2\"], [1, \"col-md-6\"], [1, \"form-group\", \"col-12\", \"col-md-6\", \"text-right\"], [1, \"btn\", \"btn-secondary\", \"mr-2\", 3, \"click\"], [1, \"fas\", \"fa-undo\", \"mr-1\"], [1, \"btn\", \"btn-info\", \"mr-2\", 3, \"click\"], [1, \"fas\", \"fa-search\", \"mr-1\"], [\"class\", \"btn btn-success mr-2\", 3, \"click\", 4, \"ngIf\"], [3, \"changeTab\"], [\"tabTitle\", \"\\u5EFA\\u6848\"], [1, \"pt-3\"], [1, \"col-12\", \"mt-3\"], [1, \"table-responsive\"], [1, \"table\", \"table-striped\", \"border\", 2, \"min-width\", \"800px\", \"background-color\", \"#f3f3f3\"], [1, \"d-flex\", \"text-white\", 2, \"background-color\", \"#27ae60\"], [\"scope\", \"col\", 1, \"col-2\"], [\"scope\", \"col\", 1, \"col-1\"], [\"class\", \"d-flex\", 4, \"ngFor\", \"ngForOf\"], [3, \"PageChange\", \"CollectionSize\", \"Page\", \"PageSize\"], [\"tabTitle\", \"\\u5171\\u7528\"], [\"scope\", \"col\", 1, \"col-3\"], [\"for\", \"buildCase\", 1, \"label\", \"mr-2\"], [1, \"btn\", \"btn-success\", \"mr-2\", 3, \"click\"], [1, \"fas\", \"fa-plus\", \"mr-1\"], [1, \"d-flex\"], [1, \"col-2\"], [1, \"col-1\"], [\"type\", \"button\", \"class\", \"btn btn-outline-success m-1  btn-sm\", 3, \"click\", 4, \"ngIf\"], [\"type\", \"button\", \"class\", \"btn btn-outline-danger m-1  btn-sm\", 3, \"click\", 4, \"ngIf\"], [\"type\", \"button\", 1, \"btn\", \"btn-outline-success\", \"m-1\", \"btn-sm\", 3, \"click\"], [1, \"fas\", \"fa-edit\", \"mr-1\"], [\"type\", \"button\", 1, \"btn\", \"btn-outline-danger\", \"m-1\", \"btn-sm\", 3, \"click\"], [1, \"far\", \"fa-trash-alt\", \"mr-1\"], [1, \"col-3\"], [1, \"mr-md-5\", \"ml-md-5\", 2, \"height\", \"100%\", \"overflow\", \"auto\", \"max-width\", \"500px\"], [4, \"ngIf\"], [2, \"padding\", \"1rem 2rem\"], [1, \"col-12\", \"col-md-12\"], [3, \"label\", \"labelFor\", \"isRequired\", 4, \"ngIf\"], [3, \"label\", \"labelFor\", \"isRequired\"], [\"type\", \"text\", \"nbInput\", \"\", \"id\", \"CRequirement\", \"name\", \"CRequirement\", \"placeholder\", \"\\u5DE5\\u7A0B\\u9805\\u76EE\", \"maxlength\", \"50\", 1, \"flex-grow-1\", 3, \"ngModelChange\", \"ngModel\"], [\"type\", \"text\", \"nbInput\", \"\", \"id\", \"CGroupName\", \"name\", \"CGroupName\", \"placeholder\", \"\\u7FA4\\u7D44\\u985E\\u5225\", \"maxlength\", \"20\", 1, \"flex-grow-1\", 3, \"ngModelChange\", \"ngModel\"], [\"type\", \"number\", \"nbInput\", \"\", \"id\", \"CSort\", \"name\", \"CSort\", \"placeholder\", \"\\u6392\\u5E8F\", 1, \"flex-grow-1\", 3, \"ngModelChange\", \"ngModel\"], [\"placeholder\", \"\\u8ACB\\u9078\\u64C7\", \"id\", \"CHouseType\", \"name\", \"CHouseType\", \"multiple\", \"\", 1, \"flex-grow-1\", 3, \"selectedChange\", \"selected\"], [\"langg\", \"\", 3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"placeholder\", \"\\u8ACB\\u9078\\u64C7\", \"id\", \"CStatus\", \"name\", \"CStatus\", 1, \"flex-grow-1\", 3, \"selectedChange\", \"selected\"], [\"type\", \"number\", \"nbInput\", \"\", \"id\", \"CUnitPrice\", \"name\", \"CUnitPrice\", \"placeholder\", \"\\u55AE\\u50F9\", 1, \"flex-grow-1\", 3, \"ngModelChange\", \"ngModel\"], [\"type\", \"text\", \"nbInput\", \"\", \"id\", \"CUnit\", \"name\", \"CUnit\", \"placeholder\", \"\\u55AE\\u4F4D\", 1, \"flex-grow-1\", 3, \"ngModelChange\", \"ngModel\"], [\"id\", \"CIsShow\", \"name\", \"CIsShow\", 1, \"flex-grow-1\", 3, \"ngModelChange\", \"ngModel\"], [\"nbInput\", \"\", \"id\", \"CRemark\", \"name\", \"CRemark\", \"placeholder\", \"\\u5099\\u8A3B\\u8AAA\\u660E\", \"maxlength\", \"100\", \"rows\", \"3\", 1, \"flex-grow-1\", 3, \"ngModelChange\", \"ngModel\"], [1, \"col-12\", \"text-center\"], [1, \"btn\", \"btn-danger\", \"mr-2\", 3, \"click\"], [\"placeholder\", \"\\u8ACB\\u9078\\u64C7\", \"id\", \"CBuildCaseID\", \"name\", \"CBuildCaseID\", 1, \"flex-grow-1\", 3, \"selectedChange\", \"selected\"], [\"langg\", \"\", 3, \"value\"]],\n      template: function RequirementManagementComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"nb-card\", 2)(1, \"nb-card-header\");\n          i0.ɵɵelement(2, \"ngx-breadcrumb\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(3, \"nb-card-body\", 3)(4, \"div\", 4)(5, \"div\", 5);\n          i0.ɵɵtemplate(6, RequirementManagementComponent_div_6_Template, 5, 2, \"div\", 6);\n          i0.ɵɵelementStart(7, \"div\", 7)(8, \"label\", 8);\n          i0.ɵɵtext(9, \"\\u5DE5\\u7A0B\\u9805\\u76EE\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(10, \"input\", 9);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_Template_input_ngModelChange_10_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.getListRequirementRequest.CRequirement, $event) || (ctx.getListRequirementRequest.CRequirement = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(11, \"div\", 7)(12, \"label\", 10);\n          i0.ɵɵtext(13, \"\\u7FA4\\u7D44\\u985E\\u5225\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(14, \"input\", 11);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_Template_input_ngModelChange_14_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.getListRequirementRequest.CGroupName, $event) || (ctx.getListRequirementRequest.CGroupName = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(15, \"div\", 5)(16, \"div\", 7)(17, \"label\", 12);\n          i0.ɵɵtext(18, \"\\u985E\\u578B\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(19, \"nb-select\", 13);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_Template_nb_select_ngModelChange_19_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.getListRequirementRequest.CHouseType, $event) || (ctx.getListRequirementRequest.CHouseType = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵtemplate(20, RequirementManagementComponent_nb_option_20_Template, 2, 2, \"nb-option\", 14);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(21, \"div\", 7)(22, \"label\", 15);\n          i0.ɵɵtext(23, \"\\u72C0\\u614B\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(24, \"nb-select\", 16);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_Template_nb_select_ngModelChange_24_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.getListRequirementRequest.CStatus, $event) || (ctx.getListRequirementRequest.CStatus = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵelementStart(25, \"nb-option\", 17);\n          i0.ɵɵtext(26, \"\\u5168\\u90E8\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(27, \"nb-option\", 17);\n          i0.ɵɵtext(28, \"\\u555F\\u7528\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(29, \"nb-option\", 17);\n          i0.ɵɵtext(30, \"\\u505C\\u7528\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(31, \"div\", 7)(32, \"label\", 18);\n          i0.ɵɵtext(33, \"\\u5BA2\\u8B8A\\u9700\\u6C42\\u986F\\u793A\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(34, \"nb-select\", 16);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_Template_nb_select_ngModelChange_34_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.getListRequirementRequest.CIsShow, $event) || (ctx.getListRequirementRequest.CIsShow = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵelementStart(35, \"nb-option\", 17);\n          i0.ɵɵtext(36, \"\\u5168\\u90E8\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(37, \"nb-option\", 17);\n          i0.ɵɵtext(38, \"\\u662F\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(39, \"nb-option\", 17);\n          i0.ɵɵtext(40, \"\\u5426\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(41, \"div\", 5);\n          i0.ɵɵelement(42, \"div\", 19);\n          i0.ɵɵelementStart(43, \"div\", 20)(44, \"button\", 21);\n          i0.ɵɵlistener(\"click\", function RequirementManagementComponent_Template_button_click_44_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.resetSearch());\n          });\n          i0.ɵɵelement(45, \"i\", 22);\n          i0.ɵɵtext(46, \"\\u91CD\\u7F6E\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(47, \"button\", 23);\n          i0.ɵɵlistener(\"click\", function RequirementManagementComponent_Template_button_click_47_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.getList());\n          });\n          i0.ɵɵelement(48, \"i\", 24);\n          i0.ɵɵtext(49, \"\\u67E5\\u8A62\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(50, RequirementManagementComponent_button_50_Template, 3, 0, \"button\", 25);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(51, \"nb-card-body\", 3)(52, \"nb-tabset\", 26);\n          i0.ɵɵlistener(\"changeTab\", function RequirementManagementComponent_Template_nb_tabset_changeTab_52_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onTabChange($event));\n          });\n          i0.ɵɵelementStart(53, \"nb-tab\", 27)(54, \"div\", 28)(55, \"div\", 29)(56, \"div\", 30)(57, \"table\", 31)(58, \"thead\")(59, \"tr\", 32)(60, \"th\", 33);\n          i0.ɵɵtext(61, \"\\u5EFA\\u6848\\u540D\\u7A31\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(62, \"th\", 33);\n          i0.ɵɵtext(63, \"\\u5DE5\\u7A0B\\u9805\\u76EE\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(64, \"th\", 34);\n          i0.ɵɵtext(65, \"\\u7FA4\\u7D44\\u985E\\u5225\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(66, \"th\", 34);\n          i0.ɵɵtext(67, \"\\u985E\\u578B\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(68, \"th\", 34);\n          i0.ɵɵtext(69, \"\\u6392\\u5E8F\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(70, \"th\", 34);\n          i0.ɵɵtext(71, \"\\u72C0\\u614B\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(72, \"th\", 34);\n          i0.ɵɵtext(73, \"\\u5BA2\\u8B8A\\u9700\\u6C42\\u986F\\u793A\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(74, \"th\", 34);\n          i0.ɵɵtext(75, \"\\u55AE\\u50F9\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(76, \"th\", 33);\n          i0.ɵɵtext(77, \"\\u64CD\\u4F5C\\u529F\\u80FD\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(78, \"tbody\");\n          i0.ɵɵtemplate(79, RequirementManagementComponent_tr_79_Template, 22, 15, \"tr\", 35);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(80, \"ngx-pagination\", 36);\n          i0.ɵɵtwoWayListener(\"PageChange\", function RequirementManagementComponent_Template_ngx_pagination_PageChange_80_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.pageIndex, $event) || (ctx.pageIndex = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵlistener(\"PageChange\", function RequirementManagementComponent_Template_ngx_pagination_PageChange_80_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.getList());\n          });\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(81, \"nb-tab\", 37)(82, \"div\", 28)(83, \"div\", 29)(84, \"div\", 30)(85, \"table\", 31)(86, \"thead\")(87, \"tr\", 32)(88, \"th\", 33);\n          i0.ɵɵtext(89, \"\\u5DE5\\u7A0B\\u9805\\u76EE\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(90, \"th\", 33);\n          i0.ɵɵtext(91, \"\\u7FA4\\u7D44\\u985E\\u5225\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(92, \"th\", 34);\n          i0.ɵɵtext(93, \"\\u985E\\u578B\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(94, \"th\", 34);\n          i0.ɵɵtext(95, \"\\u6392\\u5E8F\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(96, \"th\", 34);\n          i0.ɵɵtext(97, \"\\u72C0\\u614B\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(98, \"th\", 34);\n          i0.ɵɵtext(99, \"\\u5BA2\\u8B8A\\u9700\\u6C42\\u986F\\u793A\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(100, \"th\", 34);\n          i0.ɵɵtext(101, \"\\u55AE\\u50F9\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(102, \"th\", 38);\n          i0.ɵɵtext(103, \"\\u64CD\\u4F5C\\u529F\\u80FD\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(104, \"tbody\");\n          i0.ɵɵtemplate(105, RequirementManagementComponent_tr_105_Template, 20, 14, \"tr\", 35);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(106, \"ngx-pagination\", 36);\n          i0.ɵɵtwoWayListener(\"PageChange\", function RequirementManagementComponent_Template_ngx_pagination_PageChange_106_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.pageIndex, $event) || (ctx.pageIndex = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵlistener(\"PageChange\", function RequirementManagementComponent_Template_ngx_pagination_PageChange_106_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.getList());\n          });\n          i0.ɵɵelementEnd()()()()()()();\n          i0.ɵɵtemplate(107, RequirementManagementComponent_ng_template_107_Template, 39, 43, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor)(109, RequirementManagementComponent_ng_template_109_Template, 36, 40, \"ng-template\", null, 1, i0.ɵɵtemplateRefExtractor);\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"ngIf\", ctx.currentTab === 0);\n          i0.ɵɵadvance(4);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.getListRequirementRequest.CRequirement);\n          i0.ɵɵadvance(4);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.getListRequirementRequest.CGroupName);\n          i0.ɵɵadvance(5);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.getListRequirementRequest.CHouseType);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngForOf\", ctx.houseType);\n          i0.ɵɵadvance(4);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.getListRequirementRequest.CStatus);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"value\", -1);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"value\", 1);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"value\", 0);\n          i0.ɵɵadvance(5);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.getListRequirementRequest.CIsShow);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"value\", null);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"value\", true);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"value\", false);\n          i0.ɵɵadvance(11);\n          i0.ɵɵproperty(\"ngIf\", ctx.isCreate);\n          i0.ɵɵadvance(29);\n          i0.ɵɵproperty(\"ngForOf\", ctx.requirementList);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"CollectionSize\", ctx.totalRecords);\n          i0.ɵɵtwoWayProperty(\"Page\", ctx.pageIndex);\n          i0.ɵɵproperty(\"PageSize\", ctx.pageSize);\n          i0.ɵɵadvance(25);\n          i0.ɵɵproperty(\"ngForOf\", ctx.requirementList);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"CollectionSize\", ctx.totalRecords);\n          i0.ɵɵtwoWayProperty(\"Page\", ctx.pageIndex);\n          i0.ɵɵproperty(\"PageSize\", ctx.pageSize);\n        }\n      },\n      dependencies: [NbCardModule, i3.NbCardComponent, i3.NbCardBodyComponent, i3.NbCardFooterComponent, i3.NbCardHeaderComponent, BreadcrumbComponent, NbInputModule, i3.NbInputDirective, FormsModule, i9.DefaultValueAccessor, i9.NumberValueAccessor, i9.NgControlStatus, i9.MaxLengthValidator, i9.NgModel, NbSelectModule, i3.NbSelectComponent, i3.NbOptionComponent, NbOptionModule, NgIf, NgFor, PaginationComponent, StatusPipe, NbCheckboxModule, i3.NbCheckboxComponent, FormGroupComponent, NumberWithCommasPipe, NbTabsetModule, i3.NbTabsetComponent, i3.NbTabComponent],\n      styles: [\"/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsImZpbGUiOiJyZXF1aXJlbWVudC1tYW5hZ2VtZW50LmNvbXBvbmVudC5zY3NzIn0= */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvcGFnZXMvcmVxdWlyZW1lbnQtbWFuYWdlbWVudC9yZXF1aXJlbWVudC1tYW5hZ2VtZW50LmNvbXBvbmVudC5zY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiI7QUFDQSx3TEFBd0wiLCJzb3VyY2VSb290IjoiIn0= */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["NbCardModule", "NbCheckboxModule", "NbInputModule", "NbOptionModule", "NbSelectModule", "NbTabsetModule", "BaseComponent", "takeUntilDestroyed", "BreadcrumbComponent", "FormsModule", "<PERSON><PERSON><PERSON>", "NgIf", "PaginationComponent", "StatusPipe", "FormGroupComponent", "NumberWithCommasPipe", "EnumHouseType", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵproperty", "case_r4", "cID", "ɵɵadvance", "ɵɵtextInterpolate1", "CBuildCaseName", "ɵɵtwoWayListener", "RequirementManagementComponent_div_6_Template_nb_select_ngModelChange_3_listener", "$event", "ɵɵrestoreView", "_r2", "ctx_r2", "ɵɵnextContext", "ɵɵtwoWayBindingSet", "getListRequirementRequest", "CBuildCaseID", "ɵɵresetView", "ɵɵtemplate", "RequirementManagementComponent_div_6_nb_option_4_Template", "ɵɵtwoWayProperty", "buildCaseList", "type_r5", "value", "label", "ɵɵlistener", "RequirementManagementComponent_button_50_Template_button_click_0_listener", "_r6", "dialog_r7", "ɵɵreference", "add", "ɵɵelement", "RequirementManagementComponent_tr_79_button_20_Template_button_click_0_listener", "_r8", "data_r9", "$implicit", "onEdit", "RequirementManagementComponent_tr_79_button_21_Template_button_click_0_listener", "_r10", "onDelete", "RequirementManagementComponent_tr_79_button_20_Template", "RequirementManagementComponent_tr_79_button_21_Template", "ɵɵtextInterpolate", "CRequirement", "CGroupName", "getHouseType", "CHouseType", "ɵɵpureFunction0", "_c0", "CSort", "ɵɵpipeBind1", "CStatus", "getCIsShowText", "CUnitPrice", "isUpdate", "isDelete", "RequirementManagementComponent_tr_105_button_18_Template_button_click_0_listener", "_r11", "data_r12", "RequirementManagementComponent_tr_105_button_19_Template_button_click_0_listener", "_r13", "RequirementManagementComponent_tr_105_button_18_Template", "RequirementManagementComponent_tr_105_button_19_Template", "b_r16", "RequirementManagementComponent_ng_template_107_app_form_group_10_Template_nb_select_selectedChange_1_listener", "_r15", "saveRequirement", "RequirementManagementComponent_ng_template_107_app_form_group_10_nb_option_2_Template", "type_r17", "status_r18", "RequirementManagementComponent_ng_template_107_span_2_Template", "RequirementManagementComponent_ng_template_107_span_3_Template", "RequirementManagementComponent_ng_template_107_span_4_Template", "RequirementManagementComponent_ng_template_107_span_5_Template", "RequirementManagementComponent_ng_template_107_app_form_group_10_Template", "RequirementManagementComponent_ng_template_107_Template_input_ngModelChange_12_listener", "_r14", "RequirementManagementComponent_ng_template_107_Template_input_ngModelChange_14_listener", "RequirementManagementComponent_ng_template_107_Template_input_ngModelChange_16_listener", "RequirementManagementComponent_ng_template_107_Template_nb_select_selectedChange_18_listener", "RequirementManagementComponent_ng_template_107_nb_option_19_Template", "RequirementManagementComponent_ng_template_107_Template_nb_select_selectedChange_21_listener", "RequirementManagementComponent_ng_template_107_nb_option_22_Template", "RequirementManagementComponent_ng_template_107_Template_input_ngModelChange_24_listener", "RequirementManagementComponent_ng_template_107_Template_input_ngModelChange_26_listener", "CUnit", "RequirementManagementComponent_ng_template_107_Template_nb_checkbox_ngModelChange_28_listener", "CIsShow", "RequirementManagementComponent_ng_template_107_Template_textarea_ngModelChange_31_listener", "CRemark", "RequirementManagementComponent_ng_template_107_Template_button_click_35_listener", "ref_r19", "dialogRef", "save", "RequirementManagementComponent_ng_template_107_Template_button_click_37_listener", "close", "isNew", "currentTab", "houseType", "statusOptions", "type_r21", "status_r22", "RequirementManagementComponent_ng_template_109_span_2_Template", "RequirementManagementComponent_ng_template_109_span_3_Template", "RequirementManagementComponent_ng_template_109_Template_input_ngModelChange_9_listener", "_r20", "RequirementManagementComponent_ng_template_109_Template_input_ngModelChange_11_listener", "RequirementManagementComponent_ng_template_109_Template_input_ngModelChange_13_listener", "RequirementManagementComponent_ng_template_109_Template_nb_select_selectedChange_15_listener", "RequirementManagementComponent_ng_template_109_nb_option_16_Template", "RequirementManagementComponent_ng_template_109_Template_nb_select_selectedChange_18_listener", "RequirementManagementComponent_ng_template_109_nb_option_19_Template", "RequirementManagementComponent_ng_template_109_Template_input_ngModelChange_21_listener", "RequirementManagementComponent_ng_template_109_Template_input_ngModelChange_23_listener", "RequirementManagementComponent_ng_template_109_Template_nb_checkbox_ngModelChange_25_listener", "RequirementManagementComponent_ng_template_109_Template_textarea_ngModelChange_28_listener", "RequirementManagementComponent_ng_template_109_Template_button_click_32_listener", "ref_r23", "saveTemplate", "RequirementManagementComponent_ng_template_109_Template_button_click_34_listener", "RequirementManagementComponent", "constructor", "_allow", "enum<PERSON>elper", "dialogService", "message", "valid", "buildCaseService", "requirementService", "pettern", "router", "destroyref", "getRequirementRequest", "requirementList", "getEnumOptions", "currentBuildCase", "initializeSearchForm", "getBuildCaseList", "ngOnInit", "map", "type", "resetSearch", "length", "setTimeout", "getList", "hTypes", "Array", "isArray", "labels", "for<PERSON>ach", "htype", "findH", "find", "x", "push", "join", "validation", "clear", "required", "errorMessages", "dialog", "open", "data", "_this", "_asyncToGenerator", "CRequirementID", "getData", "error", "console", "log", "ref", "showErrorMSGs", "apiRequirementSaveDataPost$Json", "body", "subscribe", "res", "StatusCode", "showSucessMSG", "showErrorMSG", "Message", "window", "confirm", "remove", "apiRequirementDeleteDataPost$Json", "apiBuildCaseGetUserBuildCasePost$Json", "pipe", "Entries", "PageSize", "pageSize", "PageIndex", "pageIndex", "totalRecords", "apiRequirementGetListPost$Json", "TotalItems", "apiRequirementGetDataPost$Json", "onHouseTypeChange", "checked", "includes", "filter", "v", "onTabChange", "event", "tabTitle", "addTemplate", "onEditTemplate", "_this2", "templateData", "ɵɵdirectiveInject", "i1", "AllowHelper", "i2", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "i3", "NbDialogService", "i4", "MessageService", "i5", "ValidationHelper", "i6", "BuildCaseService", "RequirementService", "i7", "PetternHelper", "i8", "Router", "DestroyRef", "selectors", "standalone", "features", "ɵɵInheritDefinitionFeature", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "RequirementManagementComponent_Template", "rf", "ctx", "RequirementManagementComponent_div_6_Template", "RequirementManagementComponent_Template_input_ngModelChange_10_listener", "_r1", "RequirementManagementComponent_Template_input_ngModelChange_14_listener", "RequirementManagementComponent_Template_nb_select_ngModelChange_19_listener", "RequirementManagementComponent_nb_option_20_Template", "RequirementManagementComponent_Template_nb_select_ngModelChange_24_listener", "RequirementManagementComponent_Template_nb_select_ngModelChange_34_listener", "RequirementManagementComponent_Template_button_click_44_listener", "RequirementManagementComponent_Template_button_click_47_listener", "RequirementManagementComponent_button_50_Template", "RequirementManagementComponent_Template_nb_tabset_changeTab_52_listener", "RequirementManagementComponent_tr_79_Template", "RequirementManagementComponent_Template_ngx_pagination_PageChange_80_listener", "RequirementManagementComponent_tr_105_Template", "RequirementManagementComponent_Template_ngx_pagination_PageChange_106_listener", "RequirementManagementComponent_ng_template_107_Template", "ɵɵtemplateRefExtractor", "RequirementManagementComponent_ng_template_109_Template", "isCreate", "NbCardComponent", "NbCardBodyComponent", "NbCardFooterComponent", "NbCardHeaderComponent", "NbInputDirective", "i9", "DefaultValueAccessor", "NumberValueAccessor", "NgControlStatus", "MaxLengthValidator", "NgModel", "NbSelectComponent", "NbOptionComponent", "NbCheckboxComponent", "NbTabsetComponent", "NbTabComponent", "styles"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\requirement-management\\requirement-management.component.ts", "C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\requirement-management\\requirement-management.component.html"], "sourcesContent": ["import { Component, DestroyRef, OnInit, TemplateRef } from '@angular/core';\r\nimport { Router } from '@angular/router';\r\nimport { NbCardModule, NbCheckboxModule, NbDialogService, NbInputModule, NbOptionModule, NbSelectModule, NbTabsetModule } from '@nebular/theme';\r\nimport { MessageService } from 'src/app/shared/services/message.service';\r\nimport { AllowHelper } from 'src/app/shared/helper/allowHelper';\r\nimport { EnumHelper } from 'src/app/shared/helper/enumHelper';\r\nimport { PetternHelper } from 'src/app/shared/helper/petternHelper';\r\nimport { ValidationHelper } from 'src/app/shared/helper/validationHelper';\r\nimport { BuildCaseService, RequirementService } from 'src/services/api/services';\r\nimport { BaseComponent } from '../components/base/baseComponent';\r\nimport { takeUntilDestroyed } from '@angular/core/rxjs-interop';\r\nimport { BuildCaseGetListReponse, GetListRequirementRequest, GetRequirement, GetRequirementByIdRequest, SaveDataRequirement } from 'src/services/api/models';\r\nimport { BreadcrumbComponent } from '../components/breadcrumb/breadcrumb.component';\r\nimport { FormsModule } from '@angular/forms';\r\nimport { NgFor, NgIf } from '@angular/common';\r\nimport { PaginationComponent } from '../components/pagination/pagination.component';\r\nimport { StatusPipe } from 'src/app/@theme/pipes/mapping.pipe';\r\nimport { FormGroupComponent } from 'src/app/@theme/components/form-group/form-group.component';\r\nimport { NumberWithCommasPipe } from 'src/app/@theme/pipes/number-with-commas.pipe';\r\nimport { EnumHouseType } from 'src/app/shared/enum/enumHouseType';\r\n\r\n@Component({\r\n  selector: 'app-requirement-management',\r\n  standalone: true, imports: [\r\n    NbCardModule,\r\n    BreadcrumbComponent,\r\n    NbInputModule,\r\n    FormsModule,\r\n    NbSelectModule,\r\n    NbOptionModule,\r\n    NgIf,\r\n    NgFor,\r\n    PaginationComponent,\r\n    StatusPipe,\r\n    NbCheckboxModule,\r\n    FormGroupComponent,\r\n    NumberWithCommasPipe,\r\n    NbTabsetModule\r\n  ],\r\n  templateUrl: './requirement-management.component.html',\r\n  styleUrl: './requirement-management.component.scss'\r\n})\r\nexport class RequirementManagementComponent extends BaseComponent implements OnInit {\r\n  constructor(\r\n    private _allow: AllowHelper,\r\n    private enumHelper: EnumHelper,\r\n    private dialogService: NbDialogService,\r\n    private message: MessageService,\r\n    private valid: ValidationHelper,\r\n    private buildCaseService: BuildCaseService,\r\n    private requirementService: RequirementService,\r\n    private pettern: PetternHelper,\r\n    private router: Router,\r\n    private destroyref: DestroyRef\r\n  ) {\r\n    super(_allow);\r\n    this.initializeSearchForm();\r\n    this.getBuildCaseList();\r\n  }\r\n  // request\r\n  getListRequirementRequest = {} as GetListRequirementRequest & { CIsShow?: boolean | null };\r\n  getRequirementRequest: GetRequirementByIdRequest = {};\r\n  // response\r\n  buildCaseList: BuildCaseGetListReponse[] = [];\r\n  requirementList: GetRequirement[] = [];\r\n  saveRequirement: SaveDataRequirement & { CIsShow?: boolean } = { CHouseType: [] };\r\n\r\n  statusOptions = [\r\n    { value: 0, label: '停用' },\r\n    { value: 1, label: '啟用' },\r\n  ];\r\n  houseType = this.enumHelper.getEnumOptions(EnumHouseType);\r\n  isNew = false;\r\n  currentBuildCase = 0;\r\n  currentTab = 0; // 追蹤當前選中的 tab\r\n\r\n  override ngOnInit(): void { }\r\n  // 初始化搜尋表單\r\n  initializeSearchForm() {\r\n    this.getListRequirementRequest.CBuildCaseID = -1;\r\n    this.getListRequirementRequest.CStatus = -1;\r\n    this.getListRequirementRequest.CIsShow = null;\r\n    this.getListRequirementRequest.CRequirement = '';\r\n    this.getListRequirementRequest.CGroupName = '';\r\n    // 預設全選所有房屋類型\r\n    this.getListRequirementRequest.CHouseType = this.houseType.map(type => type.value);\r\n  }\r\n\r\n  // 重置搜尋\r\n  resetSearch() {\r\n    this.initializeSearchForm();\r\n    // 重置後如果有建案資料，重新設定預設選擇第一個建案\r\n    if (this.buildCaseList && this.buildCaseList.length > 0) {\r\n      setTimeout(() => {\r\n        this.getListRequirementRequest.CBuildCaseID = this.buildCaseList[0].cID;\r\n        this.getList();\r\n      }, 0);\r\n    } else {\r\n      this.getList();\r\n    }\r\n  }\r\n\r\n  getHouseType(hTypes: number[] | number | null | undefined): string {\r\n    if (!hTypes) {\r\n      return '';\r\n    }\r\n\r\n    if (!Array.isArray(hTypes)) {\r\n      hTypes = [hTypes];\r\n    }\r\n\r\n    let labels: string[] = [];\r\n    hTypes.forEach(htype => {\r\n      let findH = this.houseType.find(x => x.value == htype);\r\n      if (findH) {\r\n        labels.push(findH.label);\r\n      }\r\n    });\r\n    return labels.join(', ');\r\n  } validation() {\r\n    this.valid.clear();\r\n\r\n    // 根據當前 tab 決定是否需要驗證建案名稱\r\n    if (this.currentTab === 0) {\r\n      // 建案頁面需要驗證建案名稱\r\n      this.valid.required('[建案名稱]', this.saveRequirement.CBuildCaseID);\r\n    }\r\n\r\n    this.valid.required('[需求]', this.saveRequirement.CRequirement);\r\n    this.valid.required('[地主戶]', this.saveRequirement.CHouseType);\r\n    this.valid.required('[安排]', this.saveRequirement.CSort);\r\n    this.valid.required('[狀態]', this.saveRequirement.CStatus);\r\n    this.valid.required('[單價]', this.saveRequirement.CUnitPrice);\r\n    this.valid.required('[單位]', this.saveRequirement.CUnit);\r\n\r\n    // 群組名稱長度驗證\r\n    if (this.saveRequirement.CGroupName && this.saveRequirement.CGroupName.length > 20) {\r\n      this.valid.errorMessages.push('[群組名稱] 不能超過20個字');\r\n    }\r\n\r\n    // 備註說明長度驗證\r\n    if (this.saveRequirement.CRemark && this.saveRequirement.CRemark.length > 100) {\r\n      this.valid.errorMessages.push('[備註說明] 不能超過100個字');\r\n    }\r\n  }\r\n\r\n  add(dialog: TemplateRef<any>) {\r\n    this.isNew = true;\r\n    this.saveRequirement = { CHouseType: [], CIsShow: false };\r\n    this.saveRequirement.CStatus = 1;\r\n    this.saveRequirement.CUnitPrice = 0;\r\n\r\n    // 根據當前 tab 決定是否需要建案ID\r\n    if (this.currentTab === 0) {\r\n      // 建案頁面 - 使用當前選擇的建案或第一個建案\r\n      if (this.currentBuildCase != 0) {\r\n        this.saveRequirement.CBuildCaseID = this.currentBuildCase;\r\n      } else if (this.buildCaseList && this.buildCaseList.length > 0) {\r\n        this.saveRequirement.CBuildCaseID = this.buildCaseList[0].cID;\r\n      }\r\n    } else {\r\n      // 模板頁面 - 設定建案ID為0\r\n      this.saveRequirement.CBuildCaseID = 0;\r\n    }\r\n\r\n    this.dialogService.open(dialog);\r\n  }\r\n\r\n  async onEdit(data: GetRequirement, dialog: TemplateRef<any>) {\r\n    this.getRequirementRequest.CRequirementID = data.CRequirementID!;\r\n    this.isNew = false;\r\n    try {\r\n      await this.getData();\r\n      this.dialogService.open(dialog);\r\n    } catch (error) {\r\n      console.log(\"Failed to get function data\", error)\r\n    }\r\n  }\r\n\r\n  save(ref: any) {\r\n    this.validation();\r\n    if (this.valid.errorMessages.length > 0) {\r\n      this.message.showErrorMSGs(this.valid.errorMessages);\r\n      return;\r\n    }\r\n\r\n    // 如果是模板頁面，確保 CBuildCaseID 設定為 0\r\n    if (this.currentTab === 1) {\r\n      this.saveRequirement.CBuildCaseID = 0;\r\n    }\r\n\r\n    this.requirementService.apiRequirementSaveDataPost$Json({\r\n      body: this.saveRequirement\r\n    }).subscribe(res => {\r\n      if (res.StatusCode === 0) {\r\n        this.message.showSucessMSG('執行成功');\r\n        this.getList();\r\n      } else {\r\n        this.message.showErrorMSG(res.Message!)\r\n      }\r\n    });\r\n    ref.close();\r\n  }\r\n\r\n  onDelete(data: GetRequirement) {\r\n    this.saveRequirement.CRequirementID = data.CRequirementID!;\r\n    this.isNew = false;\r\n    if (window.confirm('是否確定刪除?')) {\r\n      this.remove();\r\n    } else {\r\n      return;\r\n    }\r\n  }\r\n\r\n  remove() {\r\n    this.requirementService.apiRequirementDeleteDataPost$Json({\r\n      body: {\r\n        CRequirementID: this.saveRequirement.CRequirementID!\r\n      }\r\n    }).subscribe(res => {\r\n      this.message.showSucessMSG('執行成功');\r\n      this.getList();\r\n    });\r\n  }\r\n\r\n  getBuildCaseList() {\r\n    this.buildCaseService.apiBuildCaseGetUserBuildCasePost$Json({ body: {} })\r\n      .pipe(takeUntilDestroyed(this.destroyref)).subscribe(res => {\r\n        this.buildCaseList = res.Entries!;\r\n        // 載入建案列表後，如果有建案資料，設定預設值並查詢一次\r\n        if (this.buildCaseList.length > 0) {\r\n          this.getListRequirementRequest.CBuildCaseID = this.buildCaseList[0].cID;\r\n        }\r\n        this.getList();\r\n      })\r\n  }\r\n\r\n  getList() {\r\n    this.getListRequirementRequest.PageSize = this.pageSize;\r\n    this.getListRequirementRequest.PageIndex = this.pageIndex;\r\n    this.requirementList = [] as GetRequirement[];\r\n    this.totalRecords = 0;\r\n    // 當選擇模板頁面時，強制設定 CBuildCaseID = 0 且不可異動\r\n    if (this.currentTab === 1) {\r\n      this.getListRequirementRequest.CBuildCaseID = 0;\r\n    } else {\r\n      // 建案頁面的邏輯保持不變\r\n      if (this.getListRequirementRequest.CBuildCaseID && this.getListRequirementRequest.CBuildCaseID != 0) {\r\n        this.currentBuildCase = this.getListRequirementRequest.CBuildCaseID;\r\n      }\r\n    }\r\n\r\n    this.requirementService.apiRequirementGetListPost$Json({ body: this.getListRequirementRequest })\r\n      .pipe()\r\n      .subscribe(res => {\r\n        if (res.StatusCode == 0) {\r\n          if (res.Entries) {\r\n            this.requirementList = res.Entries;\r\n            this.totalRecords = res.TotalItems!;\r\n          }\r\n        }\r\n      })\r\n  } getData() {\r\n    this.requirementService.apiRequirementGetDataPost$Json({ body: this.getRequirementRequest })\r\n      .pipe()\r\n      .subscribe(res => {\r\n        if (res.StatusCode == 0) {\r\n          if (res.Entries) {\r\n            this.saveRequirement = { CHouseType: [], CIsShow: false };\r\n            this.saveRequirement.CBuildCaseID = res.Entries.CBuildCaseID;\r\n            this.saveRequirement.CGroupName = res.Entries.CGroupName;\r\n            this.saveRequirement.CHouseType = res.Entries.CHouseType ? (Array.isArray(res.Entries.CHouseType) ? res.Entries.CHouseType : [res.Entries.CHouseType]) : [];\r\n            this.saveRequirement.CRemark = res.Entries.CRemark;\r\n            this.saveRequirement.CRequirement = res.Entries.CRequirement;\r\n            this.saveRequirement.CRequirementID = res.Entries.CRequirementID;\r\n            this.saveRequirement.CSort = res.Entries.CSort;\r\n            this.saveRequirement.CStatus = res.Entries.CStatus;\r\n            this.saveRequirement.CUnitPrice = res.Entries.CUnitPrice;\r\n            this.saveRequirement.CUnit = res.Entries.CUnit;\r\n            // TODO: 等後端API更新後啟用這行\r\n            this.saveRequirement.CIsShow = (res.Entries as any).CIsShow || false;\r\n          }\r\n        }\r\n      })\r\n  }\r\n\r\n  onHouseTypeChange(value: number, checked: any) {\r\n    console.log(checked);\r\n\r\n    if (checked) {\r\n      if (!this.saveRequirement.CHouseType?.includes(value)) {\r\n        this.saveRequirement.CHouseType?.push(value);\r\n      }\r\n      console.log(this.saveRequirement.CHouseType);\r\n    } else {\r\n      this.saveRequirement.CHouseType = this.saveRequirement.CHouseType?.filter(v => v !== value);\r\n    }\r\n  }\r\n\r\n  getCIsShowText(data: any): string {\r\n    return data.CIsShow ? '是' : '否';\r\n  }\r\n\r\n  // Tab 切換事件處理\r\n  onTabChange(event: any) {\r\n    // 根據 tabTitle 來判斷當前頁面\r\n    if (event.tabTitle === '共用') {\r\n      this.currentTab = 1;\r\n      this.getListRequirementRequest.CBuildCaseID = 0;\r\n    } else {\r\n      this.currentTab = 0;\r\n      // 切換回建案頁面時，如果沒有選擇建案且有建案資料，預設選擇第一個\r\n      if ((this.getListRequirementRequest.CBuildCaseID === 0 || this.getListRequirementRequest.CBuildCaseID === -1)\r\n        && this.buildCaseList\r\n        && this.buildCaseList.length > 0) {\r\n        this.getListRequirementRequest.CBuildCaseID = this.buildCaseList[0].cID;\r\n      }\r\n    }\r\n    // 切換頁籤後重新載入列表\r\n    this.getList();\r\n  }\r\n\r\n  // 新增模板\r\n  addTemplate(dialog: TemplateRef<any>) {\r\n    this.isNew = true;\r\n    this.saveRequirement = { CHouseType: [], CIsShow: false };\r\n    this.saveRequirement.CStatus = 1;\r\n    this.saveRequirement.CUnitPrice = 0;\r\n    // 模板設定建案ID為0\r\n    this.saveRequirement.CBuildCaseID = 0;\r\n    this.dialogService.open(dialog);\r\n  }\r\n\r\n  // 編輯模板\r\n  async onEditTemplate(data: GetRequirement, dialog: TemplateRef<any>) {\r\n    this.getRequirementRequest.CRequirementID = data.CRequirementID!;\r\n    this.isNew = false;\r\n    try {\r\n      await this.getData();\r\n      this.dialogService.open(dialog);\r\n    } catch (error) {\r\n      console.log(\"Failed to get template data\", error);\r\n    }\r\n  }\r\n\r\n  // 保存模板\r\n  saveTemplate(ref: any) {\r\n    // 模板驗證（不包含建案名稱）\r\n    this.valid.clear();\r\n    this.valid.required('[需求]', this.saveRequirement.CRequirement);\r\n    this.valid.required('[地主戶]', this.saveRequirement.CHouseType);\r\n    this.valid.required('[安排]', this.saveRequirement.CSort);\r\n    this.valid.required('[狀態]', this.saveRequirement.CStatus);\r\n    this.valid.required('[單價]', this.saveRequirement.CUnitPrice);\r\n    this.valid.required('[單位]', this.saveRequirement.CUnit);\r\n\r\n    // 群組名稱長度驗證\r\n    if (this.saveRequirement.CGroupName && this.saveRequirement.CGroupName.length > 20) {\r\n      this.valid.errorMessages.push('[群組名稱] 不能超過20個字');\r\n    }\r\n\r\n    // 備註說明長度驗證\r\n    if (this.saveRequirement.CRemark && this.saveRequirement.CRemark.length > 100) {\r\n      this.valid.errorMessages.push('[備註說明] 不能超過100個字');\r\n    }\r\n\r\n    if (this.valid.errorMessages.length > 0) {\r\n      this.message.showErrorMSGs(this.valid.errorMessages);\r\n      return;\r\n    }\r\n\r\n    // 確保模板建案ID為0\r\n    const templateData = { ...this.saveRequirement };\r\n    templateData.CBuildCaseID = 0;\r\n\r\n    this.requirementService.apiRequirementSaveDataPost$Json({\r\n      body: templateData\r\n    }).subscribe(res => {\r\n      if (res.StatusCode === 0) {\r\n        this.message.showSucessMSG('執行成功');\r\n        this.getList();\r\n      } else {\r\n        this.message.showErrorMSG(res.Message!)\r\n      }\r\n    });\r\n    ref.close();\r\n  }\r\n}\r\n", "<nb-card accent=\"success\">\r\n  <nb-card-header>\r\n    <ngx-breadcrumb></ngx-breadcrumb>\r\n  </nb-card-header>\r\n\r\n  <!-- 共用搜尋區域 -->\r\n  <nb-card-body class=\"bg-white pb-0\">\r\n    <div class=\"col-12\">\r\n      <div class=\"row\">\r\n        <div class=\"form-group col-12 col-md-4\" *ngIf=\"currentTab === 0\">\r\n          <label for=\"buildCase\" class=\"label mr-2\">建案</label>\r\n          <nb-select [(ngModel)]=\"getListRequirementRequest.CBuildCaseID\" class=\"col-9\">\r\n            <nb-option *ngFor=\"let case of buildCaseList\" [value]=\"case.cID\">\r\n              {{ case.CBuildCaseName }}\r\n            </nb-option>\r\n          </nb-select>\r\n        </div>\r\n        <div class=\"form-group col-12 col-md-4\">\r\n          <label for=\"requirement\" class=\"label mr-2\">工程項目</label>\r\n          <input type=\"text\" nbInput id=\"requirement\" name=\"requirement\" placeholder=\"工程項目\"\r\n            [(ngModel)]=\"getListRequirementRequest.CRequirement\">\r\n        </div>\r\n        <div class=\"form-group col-12 col-md-4\">\r\n          <label for=\"groupName\" class=\"label mr-2\">群組類別</label>\r\n          <input type=\"text\" nbInput id=\"groupName\" name=\"groupName\" placeholder=\"群組類別\"\r\n            [(ngModel)]=\"getListRequirementRequest.CGroupName\">\r\n        </div>\r\n      </div>\r\n      <div class=\"row\">\r\n        <div class=\"form-group col-12 col-md-4\">\r\n          <label for=\"houseType\" class=\"label mr-2\">類型</label>\r\n          <nb-select [(ngModel)]=\"getListRequirementRequest.CHouseType\" class=\"col-9\" multiple>\r\n            <nb-option *ngFor=\"let type of houseType\" [value]=\"type.value\">\r\n              {{ type.label }}\r\n            </nb-option>\r\n          </nb-select>\r\n        </div>\r\n        <div class=\"form-group col-12 col-md-4\">\r\n          <label for=\"status\" class=\"label mr-2\">狀態</label>\r\n          <nb-select [(ngModel)]=\"getListRequirementRequest.CStatus\" class=\"col-9\">\r\n            <nb-option [value]=\"-1\">全部</nb-option>\r\n            <nb-option [value]=\"1\">啟用</nb-option>\r\n            <nb-option [value]=\"0\">停用</nb-option>\r\n          </nb-select>\r\n        </div>\r\n        <div class=\"form-group col-12 col-md-4\">\r\n          <label for=\"isShow\" class=\"label mr-2\">客變需求顯示</label>\r\n          <nb-select [(ngModel)]=\"getListRequirementRequest.CIsShow\" class=\"col-9\">\r\n            <nb-option [value]=\"null\">全部</nb-option>\r\n            <nb-option [value]=\"true\">是</nb-option>\r\n            <nb-option [value]=\"false\">否</nb-option>\r\n          </nb-select>\r\n        </div>\r\n      </div>\r\n      <div class=\"row\">\r\n        <div class=\"col-md-6\"></div>\r\n        <div class=\"form-group col-12 col-md-6 text-right\">\r\n          <button class=\"btn btn-secondary mr-2\" (click)=\"resetSearch()\"><i class=\"fas fa-undo mr-1\"></i>重置</button>\r\n          <button class=\"btn btn-info mr-2\" (click)=\"getList()\"><i class=\"fas fa-search mr-1\"></i>查詢</button>\r\n          <button class=\"btn btn-success mr-2\" (click)=\"add(dialog)\" *ngIf=\"isCreate\"><i\r\n              class=\"fas fa-plus mr-1\"></i>新增</button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </nb-card-body>\r\n\r\n  <!-- Tab 導航 -->\r\n  <nb-card-body class=\"bg-white pb-0\">\r\n    <nb-tabset (changeTab)=\"onTabChange($event)\">\r\n      <nb-tab tabTitle=\"建案\">\r\n        <div class=\"pt-3\">\r\n          <!-- 建案列表 -->\r\n          <div class=\"col-12 mt-3\">\r\n            <div class=\"table-responsive\">\r\n              <table class=\"table table-striped border \" style=\"min-width: 800px; background-color:#f3f3f3;\">\r\n                <thead>\r\n                  <tr style=\"background-color: #27ae60;\" class=\"d-flex text-white\">\r\n                    <th scope=\"col\" class=\"col-2\">建案名稱</th>\r\n                    <th scope=\"col\" class=\"col-2\">工程項目</th>\r\n                    <th scope=\"col\" class=\"col-1\">群組類別</th>\r\n                    <th scope=\"col\" class=\"col-1\">類型</th>\r\n                    <th scope=\"col\" class=\"col-1\">排序</th>\r\n                    <th scope=\"col\" class=\"col-1\">狀態</th>\r\n                    <th scope=\"col\" class=\"col-1\">客變需求顯示</th>\r\n                    <th scope=\"col\" class=\"col-1\">單價</th>\r\n                    <th scope=\"col\" class=\"col-2\">操作功能</th>\r\n                  </tr>\r\n                </thead>\r\n                <tbody>\r\n                  <tr *ngFor=\"let data of requirementList; let i = index\" class=\"d-flex\">\r\n                    <td class=\"col-2\">{{ data.CBuildCaseName }}</td>\r\n                    <td class=\"col-2\">{{ data.CRequirement }}</td>\r\n                    <td class=\"col-1\">{{ data.CGroupName }}</td>\r\n                    <td class=\"col-1\">{{ getHouseType(data.CHouseType || []) }}</td>\r\n                    <td class=\"col-1\">{{ data.CSort }}</td>\r\n                    <td class=\"col-1\">{{ data.CStatus | getStatusName }}</td>\r\n                    <td class=\"col-1\">{{ getCIsShowText(data) }}</td>\r\n                    <td class=\"col-1\">{{ (data.CUnitPrice || 0) | ngxNumberWithCommas }}</td>\r\n                    <td class=\"col-2\">\r\n                      <button *ngIf=\"isUpdate\" type=\"button\" class=\"btn btn-outline-success m-1  btn-sm\"\r\n                        (click)=\"onEdit(data,dialog)\"><i class=\"fas fa-edit mr-1\"></i>編輯</button>\r\n                      <button *ngIf=\"isDelete\" type=\"button\" class=\"btn btn-outline-danger m-1  btn-sm\"\r\n                        (click)=\"onDelete(data)\"><i class=\"far fa-trash-alt mr-1\"></i>刪除</button>\r\n                    </td>\r\n                  </tr>\r\n                </tbody>\r\n              </table>\r\n            </div>\r\n            <ngx-pagination [CollectionSize]=\"totalRecords\" [(Page)]=\"pageIndex\" [PageSize]=\"pageSize\"\r\n              (PageChange)=\"getList()\">\r\n            </ngx-pagination>\r\n          </div>\r\n        </div>\r\n      </nb-tab>\r\n\r\n      <nb-tab tabTitle=\"共用\">\r\n        <div class=\"pt-3\">\r\n          <!-- 模板列表 -->\r\n          <div class=\"col-12 mt-3\">\r\n            <div class=\"table-responsive\">\r\n              <table class=\"table table-striped border \" style=\"min-width: 800px; background-color:#f3f3f3;\">\r\n                <thead>\r\n                  <tr style=\"background-color: #27ae60;\" class=\"d-flex text-white\">\r\n                    <th scope=\"col\" class=\"col-2\">工程項目</th>\r\n                    <th scope=\"col\" class=\"col-2\">群組類別</th>\r\n                    <th scope=\"col\" class=\"col-1\">類型</th>\r\n                    <th scope=\"col\" class=\"col-1\">排序</th>\r\n                    <th scope=\"col\" class=\"col-1\">狀態</th>\r\n                    <th scope=\"col\" class=\"col-1\">客變需求顯示</th>\r\n                    <th scope=\"col\" class=\"col-1\">單價</th>\r\n                    <th scope=\"col\" class=\"col-3\">操作功能</th>\r\n                  </tr>\r\n                </thead>\r\n                <tbody>\r\n                  <tr *ngFor=\"let data of requirementList; let i = index\" class=\"d-flex\">\r\n                    <td class=\"col-2\">{{ data.CRequirement }}</td>\r\n                    <td class=\"col-2\">{{ data.CGroupName }}</td>\r\n                    <td class=\"col-1\">{{ getHouseType(data.CHouseType || []) }}</td>\r\n                    <td class=\"col-1\">{{ data.CSort }}</td>\r\n                    <td class=\"col-1\">{{ data.CStatus | getStatusName }}</td>\r\n                    <td class=\"col-1\">{{ getCIsShowText(data) }}</td>\r\n                    <td class=\"col-1\">{{ (data.CUnitPrice || 0) | ngxNumberWithCommas }}</td>\r\n                    <td class=\"col-3\">\r\n                      <button *ngIf=\"isUpdate\" type=\"button\" class=\"btn btn-outline-success m-1  btn-sm\"\r\n                        (click)=\"onEdit(data,dialog)\"><i class=\"fas fa-edit mr-1\"></i>編輯</button>\r\n                      <button *ngIf=\"isDelete\" type=\"button\" class=\"btn btn-outline-danger m-1  btn-sm\"\r\n                        (click)=\"onDelete(data)\"><i class=\"far fa-trash-alt mr-1\"></i>刪除</button>\r\n                    </td>\r\n                  </tr>\r\n                </tbody>\r\n              </table>\r\n            </div>\r\n            <ngx-pagination [CollectionSize]=\"totalRecords\" [(Page)]=\"pageIndex\" [PageSize]=\"pageSize\"\r\n              (PageChange)=\"getList()\">\r\n            </ngx-pagination>\r\n          </div>\r\n        </div>\r\n      </nb-tab>\r\n    </nb-tabset>\r\n  </nb-card-body>\r\n</nb-card>\r\n\r\n<!-- 建案對話框 -->\r\n<ng-template #dialog let-data let-ref=\"dialogRef\">\r\n  <nb-card style=\"height: 100%; overflow: auto; max-width: 500px;\" class=\"mr-md-5 ml-md-5\">\r\n    <nb-card-header>\r\n      <span *ngIf=\"isNew===true && currentTab === 0\">新增建案需求</span>\r\n      <span *ngIf=\"isNew===false && currentTab === 0\">編輯建案需求</span>\r\n      <span *ngIf=\"isNew===true && currentTab === 1\">新增模板需求</span>\r\n      <span *ngIf=\"isNew===false && currentTab === 1\">編輯模板需求</span>\r\n    </nb-card-header>\r\n    <nb-card-body style=\"padding:1rem 2rem\">\r\n      <div class=\"row\">\r\n        <div class=\"col-12 col-md-12\">\r\n          <div class=\"row\">\r\n            <app-form-group [label]=\"'建案名稱'\" [labelFor]=\"'CBuildCaseID'\" [isRequired]=\"true\" *ngIf=\"currentTab === 0\">\r\n              <nb-select class=\"flex-grow-1\" placeholder=\"請選擇\" id=\"CBuildCaseID\" name=\"CBuildCaseID\"\r\n                [(selected)]=\"saveRequirement.CBuildCaseID\">\r\n                <nb-option langg *ngFor=\"let b of buildCaseList\" [value]=\"b.cID\"> {{b.CBuildCaseName}}</nb-option>\r\n              </nb-select>\r\n            </app-form-group>\r\n            <app-form-group [label]=\"'工程項目'\" [labelFor]=\"'CRequirement'\" [isRequired]=\"true\">\r\n              <input type=\"text\" nbInput class=\"flex-grow-1\" id=\"CRequirement\" name=\"CRequirement\" placeholder=\"工程項目\"\r\n                [(ngModel)]=\"saveRequirement.CRequirement\" maxlength=\"50\">\r\n            </app-form-group>\r\n            <app-form-group [label]=\"'群組類別'\" [labelFor]=\"'CGroupName'\" [isRequired]=\"false\">\r\n              <input type=\"text\" nbInput class=\"flex-grow-1\" id=\"CGroupName\" name=\"CGroupName\" placeholder=\"群組類別\"\r\n                [(ngModel)]=\"saveRequirement.CGroupName\" maxlength=\"20\">\r\n            </app-form-group>\r\n            <app-form-group [label]=\"'排序'\" [labelFor]=\"'CSort'\" [isRequired]=\"true\">\r\n              <input type=\"number\" nbInput class=\"flex-grow-1\" id=\"CSort\" name=\"CSort\" placeholder=\"排序\"\r\n                [(ngModel)]=\"saveRequirement.CSort\">\r\n            </app-form-group>\r\n            <app-form-group [label]=\"'類型'\" [labelFor]=\"'CHouseType'\" [isRequired]=\"true\">\r\n              <nb-select class=\"flex-grow-1\" placeholder=\"請選擇\" id=\"CHouseType\" name=\"CHouseType\"\r\n                [(selected)]=\"saveRequirement.CHouseType\" multiple>\r\n                <nb-option langg *ngFor=\"let type of houseType\" [value]=\"type.value\"> {{type.label}}</nb-option>\r\n              </nb-select>\r\n            </app-form-group>\r\n            <app-form-group [label]=\"'狀態'\" [labelFor]=\"'CStatus'\" [isRequired]=\"true\">\r\n              <nb-select class=\"flex-grow-1\" placeholder=\"請選擇\" id=\"CStatus\" name=\"CStatus\"\r\n                [(selected)]=\"saveRequirement.CStatus\">\r\n                <nb-option langg *ngFor=\"let status of statusOptions\" [value]=\"status.value\">\r\n                  {{status.label}}</nb-option>\r\n              </nb-select>\r\n            </app-form-group>\r\n            <app-form-group [label]=\"'單價'\" [labelFor]=\"'CUnitPrice'\" [isRequired]=\"true\">\r\n              <input type=\"number\" nbInput class=\"flex-grow-1\" id=\"CUnitPrice\" name=\"CUnitPrice\" placeholder=\"單價\"\r\n                [(ngModel)]=\"saveRequirement.CUnitPrice\">\r\n            </app-form-group>\r\n            <app-form-group [label]=\"'單位'\" [labelFor]=\"'CUnit'\" [isRequired]=\"true\">\r\n              <input type=\"text\" nbInput class=\"flex-grow-1\" id=\"CUnit\" name=\"CUnit\" placeholder=\"單位\"\r\n                [(ngModel)]=\"saveRequirement.CUnit\">\r\n            </app-form-group>\r\n            <app-form-group [label]=\"'客變需求顯示'\" [labelFor]=\"'CIsShow'\" [isRequired]=\"false\">\r\n              <nb-checkbox id=\"CIsShow\" name=\"CIsShow\" [(ngModel)]=\"saveRequirement.CIsShow\" class=\"flex-grow-1\">\r\n                顯示在客變需求\r\n              </nb-checkbox>\r\n            </app-form-group>\r\n            <app-form-group [label]=\"'備註說明'\" [labelFor]=\"'CRemark'\" [isRequired]=\"false\">\r\n              <textarea nbInput class=\"flex-grow-1\" id=\"CRemark\" name=\"CRemark\" placeholder=\"備註說明\"\r\n                [(ngModel)]=\"saveRequirement.CRemark\" maxlength=\"100\" rows=\"3\"></textarea>\r\n            </app-form-group>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </nb-card-body>\r\n    <nb-card-footer>\r\n      <div class=\"row\">\r\n        <div class=\"col-12 text-center\">\r\n          <button class=\"btn btn-success mr-2\" (click)=\"save(ref)\">確定</button>\r\n          <button class=\"btn btn-danger mr-2\" (click)=\"ref.close()\">取消</button>\r\n        </div>\r\n      </div>\r\n    </nb-card-footer>\r\n  </nb-card>\r\n</ng-template>\r\n\r\n<!-- 模板對話框 -->\r\n<ng-template #templateDialog let-data let-ref=\"dialogRef\">\r\n  <nb-card style=\"height: 100%; overflow: auto; max-width: 500px;\" class=\"mr-md-5 ml-md-5\">\r\n    <nb-card-header>\r\n      <span *ngIf=\"isNew===true\">新增模板需求</span>\r\n      <span *ngIf=\"isNew===false\">編輯模板需求</span>\r\n    </nb-card-header>\r\n    <nb-card-body style=\"padding:1rem 2rem\">\r\n      <div class=\"row\">\r\n        <div class=\"col-12 col-md-12\">\r\n          <div class=\"row\">\r\n            <app-form-group [label]=\"'工程項目'\" [labelFor]=\"'CRequirement'\" [isRequired]=\"true\">\r\n              <input type=\"text\" nbInput class=\"flex-grow-1\" id=\"CRequirement\" name=\"CRequirement\" placeholder=\"工程項目\"\r\n                [(ngModel)]=\"saveRequirement.CRequirement\" maxlength=\"50\">\r\n            </app-form-group>\r\n            <app-form-group [label]=\"'群組類別'\" [labelFor]=\"'CGroupName'\" [isRequired]=\"false\">\r\n              <input type=\"text\" nbInput class=\"flex-grow-1\" id=\"CGroupName\" name=\"CGroupName\" placeholder=\"群組類別\"\r\n                [(ngModel)]=\"saveRequirement.CGroupName\" maxlength=\"20\">\r\n            </app-form-group>\r\n            <app-form-group [label]=\"'排序'\" [labelFor]=\"'CSort'\" [isRequired]=\"true\">\r\n              <input type=\"number\" nbInput class=\"flex-grow-1\" id=\"CSort\" name=\"CSort\" placeholder=\"排序\"\r\n                [(ngModel)]=\"saveRequirement.CSort\">\r\n            </app-form-group>\r\n            <app-form-group [label]=\"'類型'\" [labelFor]=\"'CHouseType'\" [isRequired]=\"true\">\r\n              <nb-select class=\"flex-grow-1\" placeholder=\"請選擇\" id=\"CHouseType\" name=\"CHouseType\"\r\n                [(selected)]=\"saveRequirement.CHouseType\" multiple>\r\n                <nb-option langg *ngFor=\"let type of houseType\" [value]=\"type.value\"> {{type.label}}</nb-option>\r\n              </nb-select>\r\n            </app-form-group>\r\n            <app-form-group [label]=\"'狀態'\" [labelFor]=\"'CStatus'\" [isRequired]=\"true\">\r\n              <nb-select class=\"flex-grow-1\" placeholder=\"請選擇\" id=\"CStatus\" name=\"CStatus\"\r\n                [(selected)]=\"saveRequirement.CStatus\">\r\n                <nb-option langg *ngFor=\"let status of statusOptions\" [value]=\"status.value\">\r\n                  {{status.label}}</nb-option>\r\n              </nb-select>\r\n            </app-form-group>\r\n            <app-form-group [label]=\"'單價'\" [labelFor]=\"'CUnitPrice'\" [isRequired]=\"true\">\r\n              <input type=\"number\" nbInput class=\"flex-grow-1\" id=\"CUnitPrice\" name=\"CUnitPrice\" placeholder=\"單價\"\r\n                [(ngModel)]=\"saveRequirement.CUnitPrice\">\r\n            </app-form-group>\r\n            <app-form-group [label]=\"'單位'\" [labelFor]=\"'CUnit'\" [isRequired]=\"true\">\r\n              <input type=\"text\" nbInput class=\"flex-grow-1\" id=\"CUnit\" name=\"CUnit\" placeholder=\"單位\"\r\n                [(ngModel)]=\"saveRequirement.CUnit\">\r\n            </app-form-group>\r\n            <app-form-group [label]=\"'客變需求顯示'\" [labelFor]=\"'CIsShow'\" [isRequired]=\"false\">\r\n              <nb-checkbox id=\"CIsShow\" name=\"CIsShow\" [(ngModel)]=\"saveRequirement.CIsShow\" class=\"flex-grow-1\">\r\n                顯示在客變需求\r\n              </nb-checkbox>\r\n            </app-form-group>\r\n            <app-form-group [label]=\"'備註說明'\" [labelFor]=\"'CRemark'\" [isRequired]=\"false\">\r\n              <textarea nbInput class=\"flex-grow-1\" id=\"CRemark\" name=\"CRemark\" placeholder=\"備註說明\"\r\n                [(ngModel)]=\"saveRequirement.CRemark\" maxlength=\"100\" rows=\"3\"></textarea>\r\n            </app-form-group>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </nb-card-body>\r\n    <nb-card-footer>\r\n      <div class=\"row\">\r\n        <div class=\"col-12 text-center\">\r\n          <button class=\"btn btn-success mr-2\" (click)=\"saveTemplate(ref)\">確定</button>\r\n          <button class=\"btn btn-danger mr-2\" (click)=\"ref.close()\">取消</button>\r\n        </div>\r\n      </div>\r\n    </nb-card-footer>\r\n  </nb-card>\r\n</ng-template>"], "mappings": ";AAEA,SAASA,YAAY,EAAEC,gBAAgB,EAAmBC,aAAa,EAAEC,cAAc,EAAEC,cAAc,EAAEC,cAAc,QAAQ,gBAAgB;AAO/I,SAASC,aAAa,QAAQ,kCAAkC;AAChE,SAASC,kBAAkB,QAAQ,4BAA4B;AAE/D,SAASC,mBAAmB,QAAQ,+CAA+C;AACnF,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,KAAK,EAAEC,IAAI,QAAQ,iBAAiB;AAC7C,SAASC,mBAAmB,QAAQ,+CAA+C;AACnF,SAASC,UAAU,QAAQ,mCAAmC;AAC9D,SAASC,kBAAkB,QAAQ,2DAA2D;AAC9F,SAASC,oBAAoB,QAAQ,8CAA8C;AACnF,SAASC,aAAa,QAAQ,mCAAmC;;;;;;;;;;;;;;ICPrDC,EAAA,CAAAC,cAAA,oBAAiE;IAC/DD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAFkCH,EAAA,CAAAI,UAAA,UAAAC,OAAA,CAAAC,GAAA,CAAkB;IAC9DN,EAAA,CAAAO,SAAA,EACF;IADEP,EAAA,CAAAQ,kBAAA,MAAAH,OAAA,CAAAI,cAAA,MACF;;;;;;IAJFT,EADF,CAAAC,cAAA,aAAiE,gBACrB;IAAAD,EAAA,CAAAE,MAAA,mBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACpDH,EAAA,CAAAC,cAAA,oBAA8E;IAAnED,EAAA,CAAAU,gBAAA,2BAAAC,iFAAAC,MAAA;MAAAZ,EAAA,CAAAa,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAAhB,EAAA,CAAAiB,kBAAA,CAAAF,MAAA,CAAAG,yBAAA,CAAAC,YAAA,EAAAP,MAAA,MAAAG,MAAA,CAAAG,yBAAA,CAAAC,YAAA,GAAAP,MAAA;MAAA,OAAAZ,EAAA,CAAAoB,WAAA,CAAAR,MAAA;IAAA,EAAoD;IAC7DZ,EAAA,CAAAqB,UAAA,IAAAC,yDAAA,wBAAiE;IAIrEtB,EADE,CAAAG,YAAA,EAAY,EACR;;;;IALOH,EAAA,CAAAO,SAAA,GAAoD;IAApDP,EAAA,CAAAuB,gBAAA,YAAAR,MAAA,CAAAG,yBAAA,CAAAC,YAAA,CAAoD;IACjCnB,EAAA,CAAAO,SAAA,EAAgB;IAAhBP,EAAA,CAAAI,UAAA,YAAAW,MAAA,CAAAS,aAAA,CAAgB;;;;;IAoB5CxB,EAAA,CAAAC,cAAA,oBAA+D;IAC7DD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAF8BH,EAAA,CAAAI,UAAA,UAAAqB,OAAA,CAAAC,KAAA,CAAoB;IAC5D1B,EAAA,CAAAO,SAAA,EACF;IADEP,EAAA,CAAAQ,kBAAA,MAAAiB,OAAA,CAAAE,KAAA,MACF;;;;;;IAyBF3B,EAAA,CAAAC,cAAA,iBAA4E;IAAvCD,EAAA,CAAA4B,UAAA,mBAAAC,0EAAA;MAAA7B,EAAA,CAAAa,aAAA,CAAAiB,GAAA;MAAA,MAAAf,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAA,MAAAe,SAAA,GAAA/B,EAAA,CAAAgC,WAAA;MAAA,OAAAhC,EAAA,CAAAoB,WAAA,CAASL,MAAA,CAAAkB,GAAA,CAAAF,SAAA,CAAW;IAAA,EAAC;IAAkB/B,EAAA,CAAAkC,SAAA,YAC3C;IAAAlC,EAAA,CAAAE,MAAA,mBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;IAuChCH,EAAA,CAAAC,cAAA,iBACgC;IAA9BD,EAAA,CAAA4B,UAAA,mBAAAO,gFAAA;MAAAnC,EAAA,CAAAa,aAAA,CAAAuB,GAAA;MAAA,MAAAC,OAAA,GAAArC,EAAA,CAAAgB,aAAA,GAAAsB,SAAA;MAAA,MAAAvB,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAA,MAAAe,SAAA,GAAA/B,EAAA,CAAAgC,WAAA;MAAA,OAAAhC,EAAA,CAAAoB,WAAA,CAASL,MAAA,CAAAwB,MAAA,CAAAF,OAAA,EAAAN,SAAA,CAAmB;IAAA,EAAC;IAAC/B,EAAA,CAAAkC,SAAA,YAAgC;IAAAlC,EAAA,CAAAE,MAAA,mBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;IAC3EH,EAAA,CAAAC,cAAA,iBAC2B;IAAzBD,EAAA,CAAA4B,UAAA,mBAAAY,gFAAA;MAAAxC,EAAA,CAAAa,aAAA,CAAA4B,IAAA;MAAA,MAAAJ,OAAA,GAAArC,EAAA,CAAAgB,aAAA,GAAAsB,SAAA;MAAA,MAAAvB,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAoB,WAAA,CAASL,MAAA,CAAA2B,QAAA,CAAAL,OAAA,CAAc;IAAA,EAAC;IAACrC,EAAA,CAAAkC,SAAA,YAAqC;IAAAlC,EAAA,CAAAE,MAAA,mBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;IAZ7EH,EADF,CAAAC,cAAA,aAAuE,aACnD;IAAAD,EAAA,CAAAE,MAAA,GAAyB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAChDH,EAAA,CAAAC,cAAA,aAAkB;IAAAD,EAAA,CAAAE,MAAA,GAAuB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC9CH,EAAA,CAAAC,cAAA,aAAkB;IAAAD,EAAA,CAAAE,MAAA,GAAqB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC5CH,EAAA,CAAAC,cAAA,aAAkB;IAAAD,EAAA,CAAAE,MAAA,GAAyC;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAChEH,EAAA,CAAAC,cAAA,aAAkB;IAAAD,EAAA,CAAAE,MAAA,IAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACvCH,EAAA,CAAAC,cAAA,cAAkB;IAAAD,EAAA,CAAAE,MAAA,IAAkC;;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACzDH,EAAA,CAAAC,cAAA,cAAkB;IAAAD,EAAA,CAAAE,MAAA,IAA0B;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACjDH,EAAA,CAAAC,cAAA,cAAkB;IAAAD,EAAA,CAAAE,MAAA,IAAkD;;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACzEH,EAAA,CAAAC,cAAA,cAAkB;IAGhBD,EAFA,CAAAqB,UAAA,KAAAsB,uDAAA,qBACgC,KAAAC,uDAAA,qBAEL;IAE/B5C,EADE,CAAAG,YAAA,EAAK,EACF;;;;;IAdeH,EAAA,CAAAO,SAAA,GAAyB;IAAzBP,EAAA,CAAA6C,iBAAA,CAAAR,OAAA,CAAA5B,cAAA,CAAyB;IACzBT,EAAA,CAAAO,SAAA,GAAuB;IAAvBP,EAAA,CAAA6C,iBAAA,CAAAR,OAAA,CAAAS,YAAA,CAAuB;IACvB9C,EAAA,CAAAO,SAAA,GAAqB;IAArBP,EAAA,CAAA6C,iBAAA,CAAAR,OAAA,CAAAU,UAAA,CAAqB;IACrB/C,EAAA,CAAAO,SAAA,GAAyC;IAAzCP,EAAA,CAAA6C,iBAAA,CAAA9B,MAAA,CAAAiC,YAAA,CAAAX,OAAA,CAAAY,UAAA,IAAAjD,EAAA,CAAAkD,eAAA,KAAAC,GAAA,GAAyC;IACzCnD,EAAA,CAAAO,SAAA,GAAgB;IAAhBP,EAAA,CAAA6C,iBAAA,CAAAR,OAAA,CAAAe,KAAA,CAAgB;IAChBpD,EAAA,CAAAO,SAAA,GAAkC;IAAlCP,EAAA,CAAA6C,iBAAA,CAAA7C,EAAA,CAAAqD,WAAA,SAAAhB,OAAA,CAAAiB,OAAA,EAAkC;IAClCtD,EAAA,CAAAO,SAAA,GAA0B;IAA1BP,EAAA,CAAA6C,iBAAA,CAAA9B,MAAA,CAAAwC,cAAA,CAAAlB,OAAA,EAA0B;IAC1BrC,EAAA,CAAAO,SAAA,GAAkD;IAAlDP,EAAA,CAAA6C,iBAAA,CAAA7C,EAAA,CAAAqD,WAAA,SAAAhB,OAAA,CAAAmB,UAAA,OAAkD;IAEzDxD,EAAA,CAAAO,SAAA,GAAc;IAAdP,EAAA,CAAAI,UAAA,SAAAW,MAAA,CAAA0C,QAAA,CAAc;IAEdzD,EAAA,CAAAO,SAAA,EAAc;IAAdP,EAAA,CAAAI,UAAA,SAAAW,MAAA,CAAA2C,QAAA,CAAc;;;;;;IA0CvB1D,EAAA,CAAAC,cAAA,iBACgC;IAA9BD,EAAA,CAAA4B,UAAA,mBAAA+B,iFAAA;MAAA3D,EAAA,CAAAa,aAAA,CAAA+C,IAAA;MAAA,MAAAC,QAAA,GAAA7D,EAAA,CAAAgB,aAAA,GAAAsB,SAAA;MAAA,MAAAvB,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAA,MAAAe,SAAA,GAAA/B,EAAA,CAAAgC,WAAA;MAAA,OAAAhC,EAAA,CAAAoB,WAAA,CAASL,MAAA,CAAAwB,MAAA,CAAAsB,QAAA,EAAA9B,SAAA,CAAmB;IAAA,EAAC;IAAC/B,EAAA,CAAAkC,SAAA,YAAgC;IAAAlC,EAAA,CAAAE,MAAA,mBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;IAC3EH,EAAA,CAAAC,cAAA,iBAC2B;IAAzBD,EAAA,CAAA4B,UAAA,mBAAAkC,iFAAA;MAAA9D,EAAA,CAAAa,aAAA,CAAAkD,IAAA;MAAA,MAAAF,QAAA,GAAA7D,EAAA,CAAAgB,aAAA,GAAAsB,SAAA;MAAA,MAAAvB,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAoB,WAAA,CAASL,MAAA,CAAA2B,QAAA,CAAAmB,QAAA,CAAc;IAAA,EAAC;IAAC7D,EAAA,CAAAkC,SAAA,YAAqC;IAAAlC,EAAA,CAAAE,MAAA,mBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;IAX7EH,EADF,CAAAC,cAAA,aAAuE,aACnD;IAAAD,EAAA,CAAAE,MAAA,GAAuB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC9CH,EAAA,CAAAC,cAAA,aAAkB;IAAAD,EAAA,CAAAE,MAAA,GAAqB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC5CH,EAAA,CAAAC,cAAA,aAAkB;IAAAD,EAAA,CAAAE,MAAA,GAAyC;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAChEH,EAAA,CAAAC,cAAA,aAAkB;IAAAD,EAAA,CAAAE,MAAA,GAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACvCH,EAAA,CAAAC,cAAA,aAAkB;IAAAD,EAAA,CAAAE,MAAA,IAAkC;;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACzDH,EAAA,CAAAC,cAAA,cAAkB;IAAAD,EAAA,CAAAE,MAAA,IAA0B;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACjDH,EAAA,CAAAC,cAAA,cAAkB;IAAAD,EAAA,CAAAE,MAAA,IAAkD;;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACzEH,EAAA,CAAAC,cAAA,cAAkB;IAGhBD,EAFA,CAAAqB,UAAA,KAAA2C,wDAAA,qBACgC,KAAAC,wDAAA,qBAEL;IAE/BjE,EADE,CAAAG,YAAA,EAAK,EACF;;;;;IAbeH,EAAA,CAAAO,SAAA,GAAuB;IAAvBP,EAAA,CAAA6C,iBAAA,CAAAgB,QAAA,CAAAf,YAAA,CAAuB;IACvB9C,EAAA,CAAAO,SAAA,GAAqB;IAArBP,EAAA,CAAA6C,iBAAA,CAAAgB,QAAA,CAAAd,UAAA,CAAqB;IACrB/C,EAAA,CAAAO,SAAA,GAAyC;IAAzCP,EAAA,CAAA6C,iBAAA,CAAA9B,MAAA,CAAAiC,YAAA,CAAAa,QAAA,CAAAZ,UAAA,IAAAjD,EAAA,CAAAkD,eAAA,KAAAC,GAAA,GAAyC;IACzCnD,EAAA,CAAAO,SAAA,GAAgB;IAAhBP,EAAA,CAAA6C,iBAAA,CAAAgB,QAAA,CAAAT,KAAA,CAAgB;IAChBpD,EAAA,CAAAO,SAAA,GAAkC;IAAlCP,EAAA,CAAA6C,iBAAA,CAAA7C,EAAA,CAAAqD,WAAA,QAAAQ,QAAA,CAAAP,OAAA,EAAkC;IAClCtD,EAAA,CAAAO,SAAA,GAA0B;IAA1BP,EAAA,CAAA6C,iBAAA,CAAA9B,MAAA,CAAAwC,cAAA,CAAAM,QAAA,EAA0B;IAC1B7D,EAAA,CAAAO,SAAA,GAAkD;IAAlDP,EAAA,CAAA6C,iBAAA,CAAA7C,EAAA,CAAAqD,WAAA,SAAAQ,QAAA,CAAAL,UAAA,OAAkD;IAEzDxD,EAAA,CAAAO,SAAA,GAAc;IAAdP,EAAA,CAAAI,UAAA,SAAAW,MAAA,CAAA0C,QAAA,CAAc;IAEdzD,EAAA,CAAAO,SAAA,EAAc;IAAdP,EAAA,CAAAI,UAAA,SAAAW,MAAA,CAAA2C,QAAA,CAAc;;;;;IAqBvC1D,EAAA,CAAAC,cAAA,WAA+C;IAAAD,EAAA,CAAAE,MAAA,2CAAM;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAC5DH,EAAA,CAAAC,cAAA,WAAgD;IAAAD,EAAA,CAAAE,MAAA,2CAAM;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAC7DH,EAAA,CAAAC,cAAA,WAA+C;IAAAD,EAAA,CAAAE,MAAA,2CAAM;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAC5DH,EAAA,CAAAC,cAAA,WAAgD;IAAAD,EAAA,CAAAE,MAAA,2CAAM;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IASnDH,EAAA,CAAAC,cAAA,oBAAiE;IAACD,EAAA,CAAAE,MAAA,GAAoB;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAAjDH,EAAA,CAAAI,UAAA,UAAA8D,KAAA,CAAA5D,GAAA,CAAe;IAAEN,EAAA,CAAAO,SAAA,EAAoB;IAApBP,EAAA,CAAAQ,kBAAA,MAAA0D,KAAA,CAAAzD,cAAA,KAAoB;;;;;;IAFxFT,EADF,CAAAC,cAAA,yBAA0G,oBAE1D;IAA5CD,EAAA,CAAAU,gBAAA,4BAAAyD,8GAAAvD,MAAA;MAAAZ,EAAA,CAAAa,aAAA,CAAAuD,IAAA;MAAA,MAAArD,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAAhB,EAAA,CAAAiB,kBAAA,CAAAF,MAAA,CAAAsD,eAAA,CAAAlD,YAAA,EAAAP,MAAA,MAAAG,MAAA,CAAAsD,eAAA,CAAAlD,YAAA,GAAAP,MAAA;MAAA,OAAAZ,EAAA,CAAAoB,WAAA,CAAAR,MAAA;IAAA,EAA2C;IAC3CZ,EAAA,CAAAqB,UAAA,IAAAiD,qFAAA,wBAAiE;IAErEtE,EADE,CAAAG,YAAA,EAAY,EACG;;;;IAL4CH,EAA7C,CAAAI,UAAA,qCAAgB,4BAA4B,oBAAoB;IAE5EJ,EAAA,CAAAO,SAAA,EAA2C;IAA3CP,EAAA,CAAAuB,gBAAA,aAAAR,MAAA,CAAAsD,eAAA,CAAAlD,YAAA,CAA2C;IACZnB,EAAA,CAAAO,SAAA,EAAgB;IAAhBP,EAAA,CAAAI,UAAA,YAAAW,MAAA,CAAAS,aAAA,CAAgB;;;;;IAkB/CxB,EAAA,CAAAC,cAAA,oBAAqE;IAACD,EAAA,CAAAE,MAAA,GAAc;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAAhDH,EAAA,CAAAI,UAAA,UAAAmE,QAAA,CAAA7C,KAAA,CAAoB;IAAE1B,EAAA,CAAAO,SAAA,EAAc;IAAdP,EAAA,CAAAQ,kBAAA,MAAA+D,QAAA,CAAA5C,KAAA,KAAc;;;;;IAMpF3B,EAAA,CAAAC,cAAA,oBAA6E;IAC3ED,EAAA,CAAAE,MAAA,GAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IADwBH,EAAA,CAAAI,UAAA,UAAAoE,UAAA,CAAA9C,KAAA,CAAsB;IAC1E1B,EAAA,CAAAO,SAAA,EAAgB;IAAhBP,EAAA,CAAAQ,kBAAA,MAAAgE,UAAA,CAAA7C,KAAA,KAAgB;;;;;;IAtC9B3B,EADF,CAAAC,cAAA,kBAAyF,qBACvE;IAIdD,EAHA,CAAAqB,UAAA,IAAAoD,8DAAA,mBAA+C,IAAAC,8DAAA,mBACC,IAAAC,8DAAA,mBACD,IAAAC,8DAAA,mBACC;IAClD5E,EAAA,CAAAG,YAAA,EAAiB;IAIXH,EAHN,CAAAC,cAAA,uBAAwC,aACrB,cACe,aACX;IACfD,EAAA,CAAAqB,UAAA,KAAAwD,yEAAA,6BAA0G;IAOxG7E,EADF,CAAAC,cAAA,0BAAiF,iBAEnB;IAA1DD,EAAA,CAAAU,gBAAA,2BAAAoE,wFAAAlE,MAAA;MAAAZ,EAAA,CAAAa,aAAA,CAAAkE,IAAA;MAAA,MAAAhE,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAAhB,EAAA,CAAAiB,kBAAA,CAAAF,MAAA,CAAAsD,eAAA,CAAAvB,YAAA,EAAAlC,MAAA,MAAAG,MAAA,CAAAsD,eAAA,CAAAvB,YAAA,GAAAlC,MAAA;MAAA,OAAAZ,EAAA,CAAAoB,WAAA,CAAAR,MAAA;IAAA,EAA0C;IAC9CZ,EAFE,CAAAG,YAAA,EAC4D,EAC7C;IAEfH,EADF,CAAAC,cAAA,0BAAgF,iBAEpB;IAAxDD,EAAA,CAAAU,gBAAA,2BAAAsE,wFAAApE,MAAA;MAAAZ,EAAA,CAAAa,aAAA,CAAAkE,IAAA;MAAA,MAAAhE,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAAhB,EAAA,CAAAiB,kBAAA,CAAAF,MAAA,CAAAsD,eAAA,CAAAtB,UAAA,EAAAnC,MAAA,MAAAG,MAAA,CAAAsD,eAAA,CAAAtB,UAAA,GAAAnC,MAAA;MAAA,OAAAZ,EAAA,CAAAoB,WAAA,CAAAR,MAAA;IAAA,EAAwC;IAC5CZ,EAFE,CAAAG,YAAA,EAC0D,EAC3C;IAEfH,EADF,CAAAC,cAAA,0BAAwE,iBAEhC;IAApCD,EAAA,CAAAU,gBAAA,2BAAAuE,wFAAArE,MAAA;MAAAZ,EAAA,CAAAa,aAAA,CAAAkE,IAAA;MAAA,MAAAhE,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAAhB,EAAA,CAAAiB,kBAAA,CAAAF,MAAA,CAAAsD,eAAA,CAAAjB,KAAA,EAAAxC,MAAA,MAAAG,MAAA,CAAAsD,eAAA,CAAAjB,KAAA,GAAAxC,MAAA;MAAA,OAAAZ,EAAA,CAAAoB,WAAA,CAAAR,MAAA;IAAA,EAAmC;IACvCZ,EAFE,CAAAG,YAAA,EACsC,EACvB;IAEfH,EADF,CAAAC,cAAA,0BAA6E,qBAEtB;IAAnDD,EAAA,CAAAU,gBAAA,4BAAAwE,6FAAAtE,MAAA;MAAAZ,EAAA,CAAAa,aAAA,CAAAkE,IAAA;MAAA,MAAAhE,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAAhB,EAAA,CAAAiB,kBAAA,CAAAF,MAAA,CAAAsD,eAAA,CAAApB,UAAA,EAAArC,MAAA,MAAAG,MAAA,CAAAsD,eAAA,CAAApB,UAAA,GAAArC,MAAA;MAAA,OAAAZ,EAAA,CAAAoB,WAAA,CAAAR,MAAA;IAAA,EAAyC;IACzCZ,EAAA,CAAAqB,UAAA,KAAA8D,oEAAA,wBAAqE;IAEzEnF,EADE,CAAAG,YAAA,EAAY,EACG;IAEfH,EADF,CAAAC,cAAA,0BAA0E,qBAE/B;IAAvCD,EAAA,CAAAU,gBAAA,4BAAA0E,6FAAAxE,MAAA;MAAAZ,EAAA,CAAAa,aAAA,CAAAkE,IAAA;MAAA,MAAAhE,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAAhB,EAAA,CAAAiB,kBAAA,CAAAF,MAAA,CAAAsD,eAAA,CAAAf,OAAA,EAAA1C,MAAA,MAAAG,MAAA,CAAAsD,eAAA,CAAAf,OAAA,GAAA1C,MAAA;MAAA,OAAAZ,EAAA,CAAAoB,WAAA,CAAAR,MAAA;IAAA,EAAsC;IACtCZ,EAAA,CAAAqB,UAAA,KAAAgE,oEAAA,wBAA6E;IAGjFrF,EADE,CAAAG,YAAA,EAAY,EACG;IAEfH,EADF,CAAAC,cAAA,0BAA6E,iBAEhC;IAAzCD,EAAA,CAAAU,gBAAA,2BAAA4E,wFAAA1E,MAAA;MAAAZ,EAAA,CAAAa,aAAA,CAAAkE,IAAA;MAAA,MAAAhE,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAAhB,EAAA,CAAAiB,kBAAA,CAAAF,MAAA,CAAAsD,eAAA,CAAAb,UAAA,EAAA5C,MAAA,MAAAG,MAAA,CAAAsD,eAAA,CAAAb,UAAA,GAAA5C,MAAA;MAAA,OAAAZ,EAAA,CAAAoB,WAAA,CAAAR,MAAA;IAAA,EAAwC;IAC5CZ,EAFE,CAAAG,YAAA,EAC2C,EAC5B;IAEfH,EADF,CAAAC,cAAA,0BAAwE,iBAEhC;IAApCD,EAAA,CAAAU,gBAAA,2BAAA6E,wFAAA3E,MAAA;MAAAZ,EAAA,CAAAa,aAAA,CAAAkE,IAAA;MAAA,MAAAhE,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAAhB,EAAA,CAAAiB,kBAAA,CAAAF,MAAA,CAAAsD,eAAA,CAAAmB,KAAA,EAAA5E,MAAA,MAAAG,MAAA,CAAAsD,eAAA,CAAAmB,KAAA,GAAA5E,MAAA;MAAA,OAAAZ,EAAA,CAAAoB,WAAA,CAAAR,MAAA;IAAA,EAAmC;IACvCZ,EAFE,CAAAG,YAAA,EACsC,EACvB;IAEfH,EADF,CAAAC,cAAA,0BAA+E,uBACsB;IAA1DD,EAAA,CAAAU,gBAAA,2BAAA+E,8FAAA7E,MAAA;MAAAZ,EAAA,CAAAa,aAAA,CAAAkE,IAAA;MAAA,MAAAhE,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAAhB,EAAA,CAAAiB,kBAAA,CAAAF,MAAA,CAAAsD,eAAA,CAAAqB,OAAA,EAAA9E,MAAA,MAAAG,MAAA,CAAAsD,eAAA,CAAAqB,OAAA,GAAA9E,MAAA;MAAA,OAAAZ,EAAA,CAAAoB,WAAA,CAAAR,MAAA;IAAA,EAAqC;IAC5EZ,EAAA,CAAAE,MAAA,oDACF;IACFF,EADE,CAAAG,YAAA,EAAc,EACC;IAEfH,EADF,CAAAC,cAAA,0BAA6E,oBAEV;IAA/DD,EAAA,CAAAU,gBAAA,2BAAAiF,2FAAA/E,MAAA;MAAAZ,EAAA,CAAAa,aAAA,CAAAkE,IAAA;MAAA,MAAAhE,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAAhB,EAAA,CAAAiB,kBAAA,CAAAF,MAAA,CAAAsD,eAAA,CAAAuB,OAAA,EAAAhF,MAAA,MAAAG,MAAA,CAAAsD,eAAA,CAAAuB,OAAA,GAAAhF,MAAA;MAAA,OAAAZ,EAAA,CAAAoB,WAAA,CAAAR,MAAA;IAAA,EAAqC;IAKjDZ,EAL2E,CAAAG,YAAA,EAAW,EAC7D,EACb,EACF,EACF,EACO;IAITH,EAHN,CAAAC,cAAA,sBAAgB,cACG,eACiB,kBAC2B;IAApBD,EAAA,CAAA4B,UAAA,mBAAAiE,iFAAA;MAAA,MAAAC,OAAA,GAAA9F,EAAA,CAAAa,aAAA,CAAAkE,IAAA,EAAAgB,SAAA;MAAA,MAAAhF,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAoB,WAAA,CAASL,MAAA,CAAAiF,IAAA,CAAAF,OAAA,CAAS;IAAA,EAAC;IAAC9F,EAAA,CAAAE,MAAA,oBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACpEH,EAAA,CAAAC,cAAA,kBAA0D;IAAtBD,EAAA,CAAA4B,UAAA,mBAAAqE,iFAAA;MAAA,MAAAH,OAAA,GAAA9F,EAAA,CAAAa,aAAA,CAAAkE,IAAA,EAAAgB,SAAA;MAAA,OAAA/F,EAAA,CAAAoB,WAAA,CAAS0E,OAAA,CAAAI,KAAA,EAAW;IAAA,EAAC;IAAClG,EAAA,CAAAE,MAAA,oBAAE;IAIpEF,EAJoE,CAAAG,YAAA,EAAS,EACjE,EACF,EACS,EACT;;;;IArECH,EAAA,CAAAO,SAAA,GAAsC;IAAtCP,EAAA,CAAAI,UAAA,SAAAW,MAAA,CAAAoF,KAAA,aAAApF,MAAA,CAAAqF,UAAA,OAAsC;IACtCpG,EAAA,CAAAO,SAAA,EAAuC;IAAvCP,EAAA,CAAAI,UAAA,SAAAW,MAAA,CAAAoF,KAAA,cAAApF,MAAA,CAAAqF,UAAA,OAAuC;IACvCpG,EAAA,CAAAO,SAAA,EAAsC;IAAtCP,EAAA,CAAAI,UAAA,SAAAW,MAAA,CAAAoF,KAAA,aAAApF,MAAA,CAAAqF,UAAA,OAAsC;IACtCpG,EAAA,CAAAO,SAAA,EAAuC;IAAvCP,EAAA,CAAAI,UAAA,SAAAW,MAAA,CAAAoF,KAAA,cAAApF,MAAA,CAAAqF,UAAA,OAAuC;IAM0CpG,EAAA,CAAAO,SAAA,GAAsB;IAAtBP,EAAA,CAAAI,UAAA,SAAAW,MAAA,CAAAqF,UAAA,OAAsB;IAMxFpG,EAAA,CAAAO,SAAA,EAAgB;IAA6BP,EAA7C,CAAAI,UAAA,qCAAgB,4BAA4B,oBAAoB;IAE5EJ,EAAA,CAAAO,SAAA,EAA0C;IAA1CP,EAAA,CAAAuB,gBAAA,YAAAR,MAAA,CAAAsD,eAAA,CAAAvB,YAAA,CAA0C;IAE9B9C,EAAA,CAAAO,SAAA,EAAgB;IAA2BP,EAA3C,CAAAI,UAAA,qCAAgB,0BAA0B,qBAAqB;IAE3EJ,EAAA,CAAAO,SAAA,EAAwC;IAAxCP,EAAA,CAAAuB,gBAAA,YAAAR,MAAA,CAAAsD,eAAA,CAAAtB,UAAA,CAAwC;IAE5B/C,EAAA,CAAAO,SAAA,EAAc;IAAsBP,EAApC,CAAAI,UAAA,yBAAc,qBAAqB,oBAAoB;IAEnEJ,EAAA,CAAAO,SAAA,EAAmC;IAAnCP,EAAA,CAAAuB,gBAAA,YAAAR,MAAA,CAAAsD,eAAA,CAAAjB,KAAA,CAAmC;IAEvBpD,EAAA,CAAAO,SAAA,EAAc;IAA2BP,EAAzC,CAAAI,UAAA,yBAAc,0BAA0B,oBAAoB;IAExEJ,EAAA,CAAAO,SAAA,EAAyC;IAAzCP,EAAA,CAAAuB,gBAAA,aAAAR,MAAA,CAAAsD,eAAA,CAAApB,UAAA,CAAyC;IACPjD,EAAA,CAAAO,SAAA,EAAY;IAAZP,EAAA,CAAAI,UAAA,YAAAW,MAAA,CAAAsF,SAAA,CAAY;IAGlCrG,EAAA,CAAAO,SAAA,EAAc;IAAwBP,EAAtC,CAAAI,UAAA,yBAAc,uBAAuB,oBAAoB;IAErEJ,EAAA,CAAAO,SAAA,EAAsC;IAAtCP,EAAA,CAAAuB,gBAAA,aAAAR,MAAA,CAAAsD,eAAA,CAAAf,OAAA,CAAsC;IACFtD,EAAA,CAAAO,SAAA,EAAgB;IAAhBP,EAAA,CAAAI,UAAA,YAAAW,MAAA,CAAAuF,aAAA,CAAgB;IAIxCtG,EAAA,CAAAO,SAAA,EAAc;IAA2BP,EAAzC,CAAAI,UAAA,yBAAc,0BAA0B,oBAAoB;IAExEJ,EAAA,CAAAO,SAAA,EAAwC;IAAxCP,EAAA,CAAAuB,gBAAA,YAAAR,MAAA,CAAAsD,eAAA,CAAAb,UAAA,CAAwC;IAE5BxD,EAAA,CAAAO,SAAA,EAAc;IAAsBP,EAApC,CAAAI,UAAA,yBAAc,qBAAqB,oBAAoB;IAEnEJ,EAAA,CAAAO,SAAA,EAAmC;IAAnCP,EAAA,CAAAuB,gBAAA,YAAAR,MAAA,CAAAsD,eAAA,CAAAmB,KAAA,CAAmC;IAEvBxF,EAAA,CAAAO,SAAA,EAAkB;IAAwBP,EAA1C,CAAAI,UAAA,iDAAkB,uBAAuB,qBAAqB;IACnCJ,EAAA,CAAAO,SAAA,EAAqC;IAArCP,EAAA,CAAAuB,gBAAA,YAAAR,MAAA,CAAAsD,eAAA,CAAAqB,OAAA,CAAqC;IAIhE1F,EAAA,CAAAO,SAAA,GAAgB;IAAwBP,EAAxC,CAAAI,UAAA,qCAAgB,uBAAuB,qBAAqB;IAExEJ,EAAA,CAAAO,SAAA,EAAqC;IAArCP,EAAA,CAAAuB,gBAAA,YAAAR,MAAA,CAAAsD,eAAA,CAAAuB,OAAA,CAAqC;;;;;IAqB/C5F,EAAA,CAAAC,cAAA,WAA2B;IAAAD,EAAA,CAAAE,MAAA,2CAAM;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IACxCH,EAAA,CAAAC,cAAA,WAA4B;IAAAD,EAAA,CAAAE,MAAA,2CAAM;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAqB/BH,EAAA,CAAAC,cAAA,oBAAqE;IAACD,EAAA,CAAAE,MAAA,GAAc;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAAhDH,EAAA,CAAAI,UAAA,UAAAmG,QAAA,CAAA7E,KAAA,CAAoB;IAAE1B,EAAA,CAAAO,SAAA,EAAc;IAAdP,EAAA,CAAAQ,kBAAA,MAAA+F,QAAA,CAAA5E,KAAA,KAAc;;;;;IAMpF3B,EAAA,CAAAC,cAAA,oBAA6E;IAC3ED,EAAA,CAAAE,MAAA,GAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IADwBH,EAAA,CAAAI,UAAA,UAAAoG,UAAA,CAAA9E,KAAA,CAAsB;IAC1E1B,EAAA,CAAAO,SAAA,EAAgB;IAAhBP,EAAA,CAAAQ,kBAAA,MAAAgG,UAAA,CAAA7E,KAAA,KAAgB;;;;;;IA9B9B3B,EADF,CAAAC,cAAA,kBAAyF,qBACvE;IAEdD,EADA,CAAAqB,UAAA,IAAAoF,8DAAA,mBAA2B,IAAAC,8DAAA,mBACC;IAC9B1G,EAAA,CAAAG,YAAA,EAAiB;IAMPH,EALV,CAAAC,cAAA,uBAAwC,aACrB,cACe,aACX,yBACkE,gBAEnB;IAA1DD,EAAA,CAAAU,gBAAA,2BAAAiG,uFAAA/F,MAAA;MAAAZ,EAAA,CAAAa,aAAA,CAAA+F,IAAA;MAAA,MAAA7F,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAAhB,EAAA,CAAAiB,kBAAA,CAAAF,MAAA,CAAAsD,eAAA,CAAAvB,YAAA,EAAAlC,MAAA,MAAAG,MAAA,CAAAsD,eAAA,CAAAvB,YAAA,GAAAlC,MAAA;MAAA,OAAAZ,EAAA,CAAAoB,WAAA,CAAAR,MAAA;IAAA,EAA0C;IAC9CZ,EAFE,CAAAG,YAAA,EAC4D,EAC7C;IAEfH,EADF,CAAAC,cAAA,0BAAgF,iBAEpB;IAAxDD,EAAA,CAAAU,gBAAA,2BAAAmG,wFAAAjG,MAAA;MAAAZ,EAAA,CAAAa,aAAA,CAAA+F,IAAA;MAAA,MAAA7F,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAAhB,EAAA,CAAAiB,kBAAA,CAAAF,MAAA,CAAAsD,eAAA,CAAAtB,UAAA,EAAAnC,MAAA,MAAAG,MAAA,CAAAsD,eAAA,CAAAtB,UAAA,GAAAnC,MAAA;MAAA,OAAAZ,EAAA,CAAAoB,WAAA,CAAAR,MAAA;IAAA,EAAwC;IAC5CZ,EAFE,CAAAG,YAAA,EAC0D,EAC3C;IAEfH,EADF,CAAAC,cAAA,0BAAwE,iBAEhC;IAApCD,EAAA,CAAAU,gBAAA,2BAAAoG,wFAAAlG,MAAA;MAAAZ,EAAA,CAAAa,aAAA,CAAA+F,IAAA;MAAA,MAAA7F,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAAhB,EAAA,CAAAiB,kBAAA,CAAAF,MAAA,CAAAsD,eAAA,CAAAjB,KAAA,EAAAxC,MAAA,MAAAG,MAAA,CAAAsD,eAAA,CAAAjB,KAAA,GAAAxC,MAAA;MAAA,OAAAZ,EAAA,CAAAoB,WAAA,CAAAR,MAAA;IAAA,EAAmC;IACvCZ,EAFE,CAAAG,YAAA,EACsC,EACvB;IAEfH,EADF,CAAAC,cAAA,0BAA6E,qBAEtB;IAAnDD,EAAA,CAAAU,gBAAA,4BAAAqG,6FAAAnG,MAAA;MAAAZ,EAAA,CAAAa,aAAA,CAAA+F,IAAA;MAAA,MAAA7F,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAAhB,EAAA,CAAAiB,kBAAA,CAAAF,MAAA,CAAAsD,eAAA,CAAApB,UAAA,EAAArC,MAAA,MAAAG,MAAA,CAAAsD,eAAA,CAAApB,UAAA,GAAArC,MAAA;MAAA,OAAAZ,EAAA,CAAAoB,WAAA,CAAAR,MAAA;IAAA,EAAyC;IACzCZ,EAAA,CAAAqB,UAAA,KAAA2F,oEAAA,wBAAqE;IAEzEhH,EADE,CAAAG,YAAA,EAAY,EACG;IAEfH,EADF,CAAAC,cAAA,0BAA0E,qBAE/B;IAAvCD,EAAA,CAAAU,gBAAA,4BAAAuG,6FAAArG,MAAA;MAAAZ,EAAA,CAAAa,aAAA,CAAA+F,IAAA;MAAA,MAAA7F,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAAhB,EAAA,CAAAiB,kBAAA,CAAAF,MAAA,CAAAsD,eAAA,CAAAf,OAAA,EAAA1C,MAAA,MAAAG,MAAA,CAAAsD,eAAA,CAAAf,OAAA,GAAA1C,MAAA;MAAA,OAAAZ,EAAA,CAAAoB,WAAA,CAAAR,MAAA;IAAA,EAAsC;IACtCZ,EAAA,CAAAqB,UAAA,KAAA6F,oEAAA,wBAA6E;IAGjFlH,EADE,CAAAG,YAAA,EAAY,EACG;IAEfH,EADF,CAAAC,cAAA,0BAA6E,iBAEhC;IAAzCD,EAAA,CAAAU,gBAAA,2BAAAyG,wFAAAvG,MAAA;MAAAZ,EAAA,CAAAa,aAAA,CAAA+F,IAAA;MAAA,MAAA7F,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAAhB,EAAA,CAAAiB,kBAAA,CAAAF,MAAA,CAAAsD,eAAA,CAAAb,UAAA,EAAA5C,MAAA,MAAAG,MAAA,CAAAsD,eAAA,CAAAb,UAAA,GAAA5C,MAAA;MAAA,OAAAZ,EAAA,CAAAoB,WAAA,CAAAR,MAAA;IAAA,EAAwC;IAC5CZ,EAFE,CAAAG,YAAA,EAC2C,EAC5B;IAEfH,EADF,CAAAC,cAAA,0BAAwE,iBAEhC;IAApCD,EAAA,CAAAU,gBAAA,2BAAA0G,wFAAAxG,MAAA;MAAAZ,EAAA,CAAAa,aAAA,CAAA+F,IAAA;MAAA,MAAA7F,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAAhB,EAAA,CAAAiB,kBAAA,CAAAF,MAAA,CAAAsD,eAAA,CAAAmB,KAAA,EAAA5E,MAAA,MAAAG,MAAA,CAAAsD,eAAA,CAAAmB,KAAA,GAAA5E,MAAA;MAAA,OAAAZ,EAAA,CAAAoB,WAAA,CAAAR,MAAA;IAAA,EAAmC;IACvCZ,EAFE,CAAAG,YAAA,EACsC,EACvB;IAEfH,EADF,CAAAC,cAAA,0BAA+E,uBACsB;IAA1DD,EAAA,CAAAU,gBAAA,2BAAA2G,8FAAAzG,MAAA;MAAAZ,EAAA,CAAAa,aAAA,CAAA+F,IAAA;MAAA,MAAA7F,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAAhB,EAAA,CAAAiB,kBAAA,CAAAF,MAAA,CAAAsD,eAAA,CAAAqB,OAAA,EAAA9E,MAAA,MAAAG,MAAA,CAAAsD,eAAA,CAAAqB,OAAA,GAAA9E,MAAA;MAAA,OAAAZ,EAAA,CAAAoB,WAAA,CAAAR,MAAA;IAAA,EAAqC;IAC5EZ,EAAA,CAAAE,MAAA,oDACF;IACFF,EADE,CAAAG,YAAA,EAAc,EACC;IAEfH,EADF,CAAAC,cAAA,0BAA6E,oBAEV;IAA/DD,EAAA,CAAAU,gBAAA,2BAAA4G,2FAAA1G,MAAA;MAAAZ,EAAA,CAAAa,aAAA,CAAA+F,IAAA;MAAA,MAAA7F,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAAhB,EAAA,CAAAiB,kBAAA,CAAAF,MAAA,CAAAsD,eAAA,CAAAuB,OAAA,EAAAhF,MAAA,MAAAG,MAAA,CAAAsD,eAAA,CAAAuB,OAAA,GAAAhF,MAAA;MAAA,OAAAZ,EAAA,CAAAoB,WAAA,CAAAR,MAAA;IAAA,EAAqC;IAKjDZ,EAL2E,CAAAG,YAAA,EAAW,EAC7D,EACb,EACF,EACF,EACO;IAITH,EAHN,CAAAC,cAAA,sBAAgB,cACG,eACiB,kBACmC;IAA5BD,EAAA,CAAA4B,UAAA,mBAAA2F,iFAAA;MAAA,MAAAC,OAAA,GAAAxH,EAAA,CAAAa,aAAA,CAAA+F,IAAA,EAAAb,SAAA;MAAA,MAAAhF,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAoB,WAAA,CAASL,MAAA,CAAA0G,YAAA,CAAAD,OAAA,CAAiB;IAAA,EAAC;IAACxH,EAAA,CAAAE,MAAA,oBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAC5EH,EAAA,CAAAC,cAAA,kBAA0D;IAAtBD,EAAA,CAAA4B,UAAA,mBAAA8F,iFAAA;MAAA,MAAAF,OAAA,GAAAxH,EAAA,CAAAa,aAAA,CAAA+F,IAAA,EAAAb,SAAA;MAAA,OAAA/F,EAAA,CAAAoB,WAAA,CAASoG,OAAA,CAAAtB,KAAA,EAAW;IAAA,EAAC;IAAClG,EAAA,CAAAE,MAAA,oBAAE;IAIpEF,EAJoE,CAAAG,YAAA,EAAS,EACjE,EACF,EACS,EACT;;;;IA7DCH,EAAA,CAAAO,SAAA,GAAkB;IAAlBP,EAAA,CAAAI,UAAA,SAAAW,MAAA,CAAAoF,KAAA,UAAkB;IAClBnG,EAAA,CAAAO,SAAA,EAAmB;IAAnBP,EAAA,CAAAI,UAAA,SAAAW,MAAA,CAAAoF,KAAA,WAAmB;IAMJnG,EAAA,CAAAO,SAAA,GAAgB;IAA6BP,EAA7C,CAAAI,UAAA,qCAAgB,4BAA4B,oBAAoB;IAE5EJ,EAAA,CAAAO,SAAA,EAA0C;IAA1CP,EAAA,CAAAuB,gBAAA,YAAAR,MAAA,CAAAsD,eAAA,CAAAvB,YAAA,CAA0C;IAE9B9C,EAAA,CAAAO,SAAA,EAAgB;IAA2BP,EAA3C,CAAAI,UAAA,qCAAgB,0BAA0B,qBAAqB;IAE3EJ,EAAA,CAAAO,SAAA,EAAwC;IAAxCP,EAAA,CAAAuB,gBAAA,YAAAR,MAAA,CAAAsD,eAAA,CAAAtB,UAAA,CAAwC;IAE5B/C,EAAA,CAAAO,SAAA,EAAc;IAAsBP,EAApC,CAAAI,UAAA,yBAAc,qBAAqB,oBAAoB;IAEnEJ,EAAA,CAAAO,SAAA,EAAmC;IAAnCP,EAAA,CAAAuB,gBAAA,YAAAR,MAAA,CAAAsD,eAAA,CAAAjB,KAAA,CAAmC;IAEvBpD,EAAA,CAAAO,SAAA,EAAc;IAA2BP,EAAzC,CAAAI,UAAA,yBAAc,0BAA0B,oBAAoB;IAExEJ,EAAA,CAAAO,SAAA,EAAyC;IAAzCP,EAAA,CAAAuB,gBAAA,aAAAR,MAAA,CAAAsD,eAAA,CAAApB,UAAA,CAAyC;IACPjD,EAAA,CAAAO,SAAA,EAAY;IAAZP,EAAA,CAAAI,UAAA,YAAAW,MAAA,CAAAsF,SAAA,CAAY;IAGlCrG,EAAA,CAAAO,SAAA,EAAc;IAAwBP,EAAtC,CAAAI,UAAA,yBAAc,uBAAuB,oBAAoB;IAErEJ,EAAA,CAAAO,SAAA,EAAsC;IAAtCP,EAAA,CAAAuB,gBAAA,aAAAR,MAAA,CAAAsD,eAAA,CAAAf,OAAA,CAAsC;IACFtD,EAAA,CAAAO,SAAA,EAAgB;IAAhBP,EAAA,CAAAI,UAAA,YAAAW,MAAA,CAAAuF,aAAA,CAAgB;IAIxCtG,EAAA,CAAAO,SAAA,EAAc;IAA2BP,EAAzC,CAAAI,UAAA,yBAAc,0BAA0B,oBAAoB;IAExEJ,EAAA,CAAAO,SAAA,EAAwC;IAAxCP,EAAA,CAAAuB,gBAAA,YAAAR,MAAA,CAAAsD,eAAA,CAAAb,UAAA,CAAwC;IAE5BxD,EAAA,CAAAO,SAAA,EAAc;IAAsBP,EAApC,CAAAI,UAAA,yBAAc,qBAAqB,oBAAoB;IAEnEJ,EAAA,CAAAO,SAAA,EAAmC;IAAnCP,EAAA,CAAAuB,gBAAA,YAAAR,MAAA,CAAAsD,eAAA,CAAAmB,KAAA,CAAmC;IAEvBxF,EAAA,CAAAO,SAAA,EAAkB;IAAwBP,EAA1C,CAAAI,UAAA,iDAAkB,uBAAuB,qBAAqB;IACnCJ,EAAA,CAAAO,SAAA,EAAqC;IAArCP,EAAA,CAAAuB,gBAAA,YAAAR,MAAA,CAAAsD,eAAA,CAAAqB,OAAA,CAAqC;IAIhE1F,EAAA,CAAAO,SAAA,GAAgB;IAAwBP,EAAxC,CAAAI,UAAA,qCAAgB,uBAAuB,qBAAqB;IAExEJ,EAAA,CAAAO,SAAA,EAAqC;IAArCP,EAAA,CAAAuB,gBAAA,YAAAR,MAAA,CAAAsD,eAAA,CAAAuB,OAAA,CAAqC;;;ADvPrD,OAAM,MAAO+B,8BAA+B,SAAQtI,aAAa;EAC/DuI,YACUC,MAAmB,EACnBC,UAAsB,EACtBC,aAA8B,EAC9BC,OAAuB,EACvBC,KAAuB,EACvBC,gBAAkC,EAClCC,kBAAsC,EACtCC,OAAsB,EACtBC,MAAc,EACdC,UAAsB;IAE9B,KAAK,CAACT,MAAM,CAAC;IAXL,KAAAA,MAAM,GAANA,MAAM;IACN,KAAAC,UAAU,GAAVA,UAAU;IACV,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,kBAAkB,GAAlBA,kBAAkB;IAClB,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,UAAU,GAAVA,UAAU;IAMpB;IACA,KAAApH,yBAAyB,GAAG,EAA8D;IAC1F,KAAAqH,qBAAqB,GAA8B,EAAE;IACrD;IACA,KAAA/G,aAAa,GAA8B,EAAE;IAC7C,KAAAgH,eAAe,GAAqB,EAAE;IACtC,KAAAnE,eAAe,GAAgD;MAAEpB,UAAU,EAAE;IAAE,CAAE;IAEjF,KAAAqD,aAAa,GAAG,CACd;MAAE5E,KAAK,EAAE,CAAC;MAAEC,KAAK,EAAE;IAAI,CAAE,EACzB;MAAED,KAAK,EAAE,CAAC;MAAEC,KAAK,EAAE;IAAI,CAAE,CAC1B;IACD,KAAA0E,SAAS,GAAG,IAAI,CAACyB,UAAU,CAACW,cAAc,CAAC1I,aAAa,CAAC;IACzD,KAAAoG,KAAK,GAAG,KAAK;IACb,KAAAuC,gBAAgB,GAAG,CAAC;IACpB,KAAAtC,UAAU,GAAG,CAAC,CAAC,CAAC;IAlBd,IAAI,CAACuC,oBAAoB,EAAE;IAC3B,IAAI,CAACC,gBAAgB,EAAE;EACzB;EAkBSC,QAAQA,CAAA,GAAW;EAC5B;EACAF,oBAAoBA,CAAA;IAClB,IAAI,CAACzH,yBAAyB,CAACC,YAAY,GAAG,CAAC,CAAC;IAChD,IAAI,CAACD,yBAAyB,CAACoC,OAAO,GAAG,CAAC,CAAC;IAC3C,IAAI,CAACpC,yBAAyB,CAACwE,OAAO,GAAG,IAAI;IAC7C,IAAI,CAACxE,yBAAyB,CAAC4B,YAAY,GAAG,EAAE;IAChD,IAAI,CAAC5B,yBAAyB,CAAC6B,UAAU,GAAG,EAAE;IAC9C;IACA,IAAI,CAAC7B,yBAAyB,CAAC+B,UAAU,GAAG,IAAI,CAACoD,SAAS,CAACyC,GAAG,CAACC,IAAI,IAAIA,IAAI,CAACrH,KAAK,CAAC;EACpF;EAEA;EACAsH,WAAWA,CAAA;IACT,IAAI,CAACL,oBAAoB,EAAE;IAC3B;IACA,IAAI,IAAI,CAACnH,aAAa,IAAI,IAAI,CAACA,aAAa,CAACyH,MAAM,GAAG,CAAC,EAAE;MACvDC,UAAU,CAAC,MAAK;QACd,IAAI,CAAChI,yBAAyB,CAACC,YAAY,GAAG,IAAI,CAACK,aAAa,CAAC,CAAC,CAAC,CAAClB,GAAG;QACvE,IAAI,CAAC6I,OAAO,EAAE;MAChB,CAAC,EAAE,CAAC,CAAC;IACP,CAAC,MAAM;MACL,IAAI,CAACA,OAAO,EAAE;IAChB;EACF;EAEAnG,YAAYA,CAACoG,MAA4C;IACvD,IAAI,CAACA,MAAM,EAAE;MACX,OAAO,EAAE;IACX;IAEA,IAAI,CAACC,KAAK,CAACC,OAAO,CAACF,MAAM,CAAC,EAAE;MAC1BA,MAAM,GAAG,CAACA,MAAM,CAAC;IACnB;IAEA,IAAIG,MAAM,GAAa,EAAE;IACzBH,MAAM,CAACI,OAAO,CAACC,KAAK,IAAG;MACrB,IAAIC,KAAK,GAAG,IAAI,CAACrD,SAAS,CAACsD,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAClI,KAAK,IAAI+H,KAAK,CAAC;MACtD,IAAIC,KAAK,EAAE;QACTH,MAAM,CAACM,IAAI,CAACH,KAAK,CAAC/H,KAAK,CAAC;MAC1B;IACF,CAAC,CAAC;IACF,OAAO4H,MAAM,CAACO,IAAI,CAAC,IAAI,CAAC;EAC1B;EAAEC,UAAUA,CAAA;IACV,IAAI,CAAC9B,KAAK,CAAC+B,KAAK,EAAE;IAElB;IACA,IAAI,IAAI,CAAC5D,UAAU,KAAK,CAAC,EAAE;MACzB;MACA,IAAI,CAAC6B,KAAK,CAACgC,QAAQ,CAAC,QAAQ,EAAE,IAAI,CAAC5F,eAAe,CAAClD,YAAY,CAAC;IAClE;IAEA,IAAI,CAAC8G,KAAK,CAACgC,QAAQ,CAAC,MAAM,EAAE,IAAI,CAAC5F,eAAe,CAACvB,YAAY,CAAC;IAC9D,IAAI,CAACmF,KAAK,CAACgC,QAAQ,CAAC,OAAO,EAAE,IAAI,CAAC5F,eAAe,CAACpB,UAAU,CAAC;IAC7D,IAAI,CAACgF,KAAK,CAACgC,QAAQ,CAAC,MAAM,EAAE,IAAI,CAAC5F,eAAe,CAACjB,KAAK,CAAC;IACvD,IAAI,CAAC6E,KAAK,CAACgC,QAAQ,CAAC,MAAM,EAAE,IAAI,CAAC5F,eAAe,CAACf,OAAO,CAAC;IACzD,IAAI,CAAC2E,KAAK,CAACgC,QAAQ,CAAC,MAAM,EAAE,IAAI,CAAC5F,eAAe,CAACb,UAAU,CAAC;IAC5D,IAAI,CAACyE,KAAK,CAACgC,QAAQ,CAAC,MAAM,EAAE,IAAI,CAAC5F,eAAe,CAACmB,KAAK,CAAC;IAEvD;IACA,IAAI,IAAI,CAACnB,eAAe,CAACtB,UAAU,IAAI,IAAI,CAACsB,eAAe,CAACtB,UAAU,CAACkG,MAAM,GAAG,EAAE,EAAE;MAClF,IAAI,CAAChB,KAAK,CAACiC,aAAa,CAACL,IAAI,CAAC,iBAAiB,CAAC;IAClD;IAEA;IACA,IAAI,IAAI,CAACxF,eAAe,CAACuB,OAAO,IAAI,IAAI,CAACvB,eAAe,CAACuB,OAAO,CAACqD,MAAM,GAAG,GAAG,EAAE;MAC7E,IAAI,CAAChB,KAAK,CAACiC,aAAa,CAACL,IAAI,CAAC,kBAAkB,CAAC;IACnD;EACF;EAEA5H,GAAGA,CAACkI,MAAwB;IAC1B,IAAI,CAAChE,KAAK,GAAG,IAAI;IACjB,IAAI,CAAC9B,eAAe,GAAG;MAAEpB,UAAU,EAAE,EAAE;MAAEyC,OAAO,EAAE;IAAK,CAAE;IACzD,IAAI,CAACrB,eAAe,CAACf,OAAO,GAAG,CAAC;IAChC,IAAI,CAACe,eAAe,CAACb,UAAU,GAAG,CAAC;IAEnC;IACA,IAAI,IAAI,CAAC4C,UAAU,KAAK,CAAC,EAAE;MACzB;MACA,IAAI,IAAI,CAACsC,gBAAgB,IAAI,CAAC,EAAE;QAC9B,IAAI,CAACrE,eAAe,CAAClD,YAAY,GAAG,IAAI,CAACuH,gBAAgB;MAC3D,CAAC,MAAM,IAAI,IAAI,CAAClH,aAAa,IAAI,IAAI,CAACA,aAAa,CAACyH,MAAM,GAAG,CAAC,EAAE;QAC9D,IAAI,CAAC5E,eAAe,CAAClD,YAAY,GAAG,IAAI,CAACK,aAAa,CAAC,CAAC,CAAC,CAAClB,GAAG;MAC/D;IACF,CAAC,MAAM;MACL;MACA,IAAI,CAAC+D,eAAe,CAAClD,YAAY,GAAG,CAAC;IACvC;IAEA,IAAI,CAAC4G,aAAa,CAACqC,IAAI,CAACD,MAAM,CAAC;EACjC;EAEM5H,MAAMA,CAAC8H,IAAoB,EAAEF,MAAwB;IAAA,IAAAG,KAAA;IAAA,OAAAC,iBAAA;MACzDD,KAAI,CAAC/B,qBAAqB,CAACiC,cAAc,GAAGH,IAAI,CAACG,cAAe;MAChEF,KAAI,CAACnE,KAAK,GAAG,KAAK;MAClB,IAAI;QACF,MAAMmE,KAAI,CAACG,OAAO,EAAE;QACpBH,KAAI,CAACvC,aAAa,CAACqC,IAAI,CAACD,MAAM,CAAC;MACjC,CAAC,CAAC,OAAOO,KAAK,EAAE;QACdC,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAEF,KAAK,CAAC;MACnD;IAAC;EACH;EAEA1E,IAAIA,CAAC6E,GAAQ;IACX,IAAI,CAACd,UAAU,EAAE;IACjB,IAAI,IAAI,CAAC9B,KAAK,CAACiC,aAAa,CAACjB,MAAM,GAAG,CAAC,EAAE;MACvC,IAAI,CAACjB,OAAO,CAAC8C,aAAa,CAAC,IAAI,CAAC7C,KAAK,CAACiC,aAAa,CAAC;MACpD;IACF;IAEA;IACA,IAAI,IAAI,CAAC9D,UAAU,KAAK,CAAC,EAAE;MACzB,IAAI,CAAC/B,eAAe,CAAClD,YAAY,GAAG,CAAC;IACvC;IAEA,IAAI,CAACgH,kBAAkB,CAAC4C,+BAA+B,CAAC;MACtDC,IAAI,EAAE,IAAI,CAAC3G;KACZ,CAAC,CAAC4G,SAAS,CAACC,GAAG,IAAG;MACjB,IAAIA,GAAG,CAACC,UAAU,KAAK,CAAC,EAAE;QACxB,IAAI,CAACnD,OAAO,CAACoD,aAAa,CAAC,MAAM,CAAC;QAClC,IAAI,CAACjC,OAAO,EAAE;MAChB,CAAC,MAAM;QACL,IAAI,CAACnB,OAAO,CAACqD,YAAY,CAACH,GAAG,CAACI,OAAQ,CAAC;MACzC;IACF,CAAC,CAAC;IACFT,GAAG,CAAC3E,KAAK,EAAE;EACb;EAEAxD,QAAQA,CAAC2H,IAAoB;IAC3B,IAAI,CAAChG,eAAe,CAACmG,cAAc,GAAGH,IAAI,CAACG,cAAe;IAC1D,IAAI,CAACrE,KAAK,GAAG,KAAK;IAClB,IAAIoF,MAAM,CAACC,OAAO,CAAC,SAAS,CAAC,EAAE;MAC7B,IAAI,CAACC,MAAM,EAAE;IACf,CAAC,MAAM;MACL;IACF;EACF;EAEAA,MAAMA,CAAA;IACJ,IAAI,CAACtD,kBAAkB,CAACuD,iCAAiC,CAAC;MACxDV,IAAI,EAAE;QACJR,cAAc,EAAE,IAAI,CAACnG,eAAe,CAACmG;;KAExC,CAAC,CAACS,SAAS,CAACC,GAAG,IAAG;MACjB,IAAI,CAAClD,OAAO,CAACoD,aAAa,CAAC,MAAM,CAAC;MAClC,IAAI,CAACjC,OAAO,EAAE;IAChB,CAAC,CAAC;EACJ;EAEAP,gBAAgBA,CAAA;IACd,IAAI,CAACV,gBAAgB,CAACyD,qCAAqC,CAAC;MAAEX,IAAI,EAAE;IAAE,CAAE,CAAC,CACtEY,IAAI,CAACtM,kBAAkB,CAAC,IAAI,CAACgJ,UAAU,CAAC,CAAC,CAAC2C,SAAS,CAACC,GAAG,IAAG;MACzD,IAAI,CAAC1J,aAAa,GAAG0J,GAAG,CAACW,OAAQ;MACjC;MACA,IAAI,IAAI,CAACrK,aAAa,CAACyH,MAAM,GAAG,CAAC,EAAE;QACjC,IAAI,CAAC/H,yBAAyB,CAACC,YAAY,GAAG,IAAI,CAACK,aAAa,CAAC,CAAC,CAAC,CAAClB,GAAG;MACzE;MACA,IAAI,CAAC6I,OAAO,EAAE;IAChB,CAAC,CAAC;EACN;EAEAA,OAAOA,CAAA;IACL,IAAI,CAACjI,yBAAyB,CAAC4K,QAAQ,GAAG,IAAI,CAACC,QAAQ;IACvD,IAAI,CAAC7K,yBAAyB,CAAC8K,SAAS,GAAG,IAAI,CAACC,SAAS;IACzD,IAAI,CAACzD,eAAe,GAAG,EAAsB;IAC7C,IAAI,CAAC0D,YAAY,GAAG,CAAC;IACrB;IACA,IAAI,IAAI,CAAC9F,UAAU,KAAK,CAAC,EAAE;MACzB,IAAI,CAAClF,yBAAyB,CAACC,YAAY,GAAG,CAAC;IACjD,CAAC,MAAM;MACL;MACA,IAAI,IAAI,CAACD,yBAAyB,CAACC,YAAY,IAAI,IAAI,CAACD,yBAAyB,CAACC,YAAY,IAAI,CAAC,EAAE;QACnG,IAAI,CAACuH,gBAAgB,GAAG,IAAI,CAACxH,yBAAyB,CAACC,YAAY;MACrE;IACF;IAEA,IAAI,CAACgH,kBAAkB,CAACgE,8BAA8B,CAAC;MAAEnB,IAAI,EAAE,IAAI,CAAC9J;IAAyB,CAAE,CAAC,CAC7F0K,IAAI,EAAE,CACNX,SAAS,CAACC,GAAG,IAAG;MACf,IAAIA,GAAG,CAACC,UAAU,IAAI,CAAC,EAAE;QACvB,IAAID,GAAG,CAACW,OAAO,EAAE;UACf,IAAI,CAACrD,eAAe,GAAG0C,GAAG,CAACW,OAAO;UAClC,IAAI,CAACK,YAAY,GAAGhB,GAAG,CAACkB,UAAW;QACrC;MACF;IACF,CAAC,CAAC;EACN;EAAE3B,OAAOA,CAAA;IACP,IAAI,CAACtC,kBAAkB,CAACkE,8BAA8B,CAAC;MAAErB,IAAI,EAAE,IAAI,CAACzC;IAAqB,CAAE,CAAC,CACzFqD,IAAI,EAAE,CACNX,SAAS,CAACC,GAAG,IAAG;MACf,IAAIA,GAAG,CAACC,UAAU,IAAI,CAAC,EAAE;QACvB,IAAID,GAAG,CAACW,OAAO,EAAE;UACf,IAAI,CAACxH,eAAe,GAAG;YAAEpB,UAAU,EAAE,EAAE;YAAEyC,OAAO,EAAE;UAAK,CAAE;UACzD,IAAI,CAACrB,eAAe,CAAClD,YAAY,GAAG+J,GAAG,CAACW,OAAO,CAAC1K,YAAY;UAC5D,IAAI,CAACkD,eAAe,CAACtB,UAAU,GAAGmI,GAAG,CAACW,OAAO,CAAC9I,UAAU;UACxD,IAAI,CAACsB,eAAe,CAACpB,UAAU,GAAGiI,GAAG,CAACW,OAAO,CAAC5I,UAAU,GAAIoG,KAAK,CAACC,OAAO,CAAC4B,GAAG,CAACW,OAAO,CAAC5I,UAAU,CAAC,GAAGiI,GAAG,CAACW,OAAO,CAAC5I,UAAU,GAAG,CAACiI,GAAG,CAACW,OAAO,CAAC5I,UAAU,CAAC,GAAI,EAAE;UAC3J,IAAI,CAACoB,eAAe,CAACuB,OAAO,GAAGsF,GAAG,CAACW,OAAO,CAACjG,OAAO;UAClD,IAAI,CAACvB,eAAe,CAACvB,YAAY,GAAGoI,GAAG,CAACW,OAAO,CAAC/I,YAAY;UAC5D,IAAI,CAACuB,eAAe,CAACmG,cAAc,GAAGU,GAAG,CAACW,OAAO,CAACrB,cAAc;UAChE,IAAI,CAACnG,eAAe,CAACjB,KAAK,GAAG8H,GAAG,CAACW,OAAO,CAACzI,KAAK;UAC9C,IAAI,CAACiB,eAAe,CAACf,OAAO,GAAG4H,GAAG,CAACW,OAAO,CAACvI,OAAO;UAClD,IAAI,CAACe,eAAe,CAACb,UAAU,GAAG0H,GAAG,CAACW,OAAO,CAACrI,UAAU;UACxD,IAAI,CAACa,eAAe,CAACmB,KAAK,GAAG0F,GAAG,CAACW,OAAO,CAACrG,KAAK;UAC9C;UACA,IAAI,CAACnB,eAAe,CAACqB,OAAO,GAAIwF,GAAG,CAACW,OAAe,CAACnG,OAAO,IAAI,KAAK;QACtE;MACF;IACF,CAAC,CAAC;EACN;EAEA4G,iBAAiBA,CAAC5K,KAAa,EAAE6K,OAAY;IAC3C5B,OAAO,CAACC,GAAG,CAAC2B,OAAO,CAAC;IAEpB,IAAIA,OAAO,EAAE;MACX,IAAI,CAAC,IAAI,CAAClI,eAAe,CAACpB,UAAU,EAAEuJ,QAAQ,CAAC9K,KAAK,CAAC,EAAE;QACrD,IAAI,CAAC2C,eAAe,CAACpB,UAAU,EAAE4G,IAAI,CAACnI,KAAK,CAAC;MAC9C;MACAiJ,OAAO,CAACC,GAAG,CAAC,IAAI,CAACvG,eAAe,CAACpB,UAAU,CAAC;IAC9C,CAAC,MAAM;MACL,IAAI,CAACoB,eAAe,CAACpB,UAAU,GAAG,IAAI,CAACoB,eAAe,CAACpB,UAAU,EAAEwJ,MAAM,CAACC,CAAC,IAAIA,CAAC,KAAKhL,KAAK,CAAC;IAC7F;EACF;EAEA6B,cAAcA,CAAC8G,IAAS;IACtB,OAAOA,IAAI,CAAC3E,OAAO,GAAG,GAAG,GAAG,GAAG;EACjC;EAEA;EACAiH,WAAWA,CAACC,KAAU;IACpB;IACA,IAAIA,KAAK,CAACC,QAAQ,KAAK,IAAI,EAAE;MAC3B,IAAI,CAACzG,UAAU,GAAG,CAAC;MACnB,IAAI,CAAClF,yBAAyB,CAACC,YAAY,GAAG,CAAC;IACjD,CAAC,MAAM;MACL,IAAI,CAACiF,UAAU,GAAG,CAAC;MACnB;MACA,IAAI,CAAC,IAAI,CAAClF,yBAAyB,CAACC,YAAY,KAAK,CAAC,IAAI,IAAI,CAACD,yBAAyB,CAACC,YAAY,KAAK,CAAC,CAAC,KACvG,IAAI,CAACK,aAAa,IAClB,IAAI,CAACA,aAAa,CAACyH,MAAM,GAAG,CAAC,EAAE;QAClC,IAAI,CAAC/H,yBAAyB,CAACC,YAAY,GAAG,IAAI,CAACK,aAAa,CAAC,CAAC,CAAC,CAAClB,GAAG;MACzE;IACF;IACA;IACA,IAAI,CAAC6I,OAAO,EAAE;EAChB;EAEA;EACA2D,WAAWA,CAAC3C,MAAwB;IAClC,IAAI,CAAChE,KAAK,GAAG,IAAI;IACjB,IAAI,CAAC9B,eAAe,GAAG;MAAEpB,UAAU,EAAE,EAAE;MAAEyC,OAAO,EAAE;IAAK,CAAE;IACzD,IAAI,CAACrB,eAAe,CAACf,OAAO,GAAG,CAAC;IAChC,IAAI,CAACe,eAAe,CAACb,UAAU,GAAG,CAAC;IACnC;IACA,IAAI,CAACa,eAAe,CAAClD,YAAY,GAAG,CAAC;IACrC,IAAI,CAAC4G,aAAa,CAACqC,IAAI,CAACD,MAAM,CAAC;EACjC;EAEA;EACM4C,cAAcA,CAAC1C,IAAoB,EAAEF,MAAwB;IAAA,IAAA6C,MAAA;IAAA,OAAAzC,iBAAA;MACjEyC,MAAI,CAACzE,qBAAqB,CAACiC,cAAc,GAAGH,IAAI,CAACG,cAAe;MAChEwC,MAAI,CAAC7G,KAAK,GAAG,KAAK;MAClB,IAAI;QACF,MAAM6G,MAAI,CAACvC,OAAO,EAAE;QACpBuC,MAAI,CAACjF,aAAa,CAACqC,IAAI,CAACD,MAAM,CAAC;MACjC,CAAC,CAAC,OAAOO,KAAK,EAAE;QACdC,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAEF,KAAK,CAAC;MACnD;IAAC;EACH;EAEA;EACAjD,YAAYA,CAACoD,GAAQ;IACnB;IACA,IAAI,CAAC5C,KAAK,CAAC+B,KAAK,EAAE;IAClB,IAAI,CAAC/B,KAAK,CAACgC,QAAQ,CAAC,MAAM,EAAE,IAAI,CAAC5F,eAAe,CAACvB,YAAY,CAAC;IAC9D,IAAI,CAACmF,KAAK,CAACgC,QAAQ,CAAC,OAAO,EAAE,IAAI,CAAC5F,eAAe,CAACpB,UAAU,CAAC;IAC7D,IAAI,CAACgF,KAAK,CAACgC,QAAQ,CAAC,MAAM,EAAE,IAAI,CAAC5F,eAAe,CAACjB,KAAK,CAAC;IACvD,IAAI,CAAC6E,KAAK,CAACgC,QAAQ,CAAC,MAAM,EAAE,IAAI,CAAC5F,eAAe,CAACf,OAAO,CAAC;IACzD,IAAI,CAAC2E,KAAK,CAACgC,QAAQ,CAAC,MAAM,EAAE,IAAI,CAAC5F,eAAe,CAACb,UAAU,CAAC;IAC5D,IAAI,CAACyE,KAAK,CAACgC,QAAQ,CAAC,MAAM,EAAE,IAAI,CAAC5F,eAAe,CAACmB,KAAK,CAAC;IAEvD;IACA,IAAI,IAAI,CAACnB,eAAe,CAACtB,UAAU,IAAI,IAAI,CAACsB,eAAe,CAACtB,UAAU,CAACkG,MAAM,GAAG,EAAE,EAAE;MAClF,IAAI,CAAChB,KAAK,CAACiC,aAAa,CAACL,IAAI,CAAC,iBAAiB,CAAC;IAClD;IAEA;IACA,IAAI,IAAI,CAACxF,eAAe,CAACuB,OAAO,IAAI,IAAI,CAACvB,eAAe,CAACuB,OAAO,CAACqD,MAAM,GAAG,GAAG,EAAE;MAC7E,IAAI,CAAChB,KAAK,CAACiC,aAAa,CAACL,IAAI,CAAC,kBAAkB,CAAC;IACnD;IAEA,IAAI,IAAI,CAAC5B,KAAK,CAACiC,aAAa,CAACjB,MAAM,GAAG,CAAC,EAAE;MACvC,IAAI,CAACjB,OAAO,CAAC8C,aAAa,CAAC,IAAI,CAAC7C,KAAK,CAACiC,aAAa,CAAC;MACpD;IACF;IAEA;IACA,MAAM+C,YAAY,GAAG;MAAE,GAAG,IAAI,CAAC5I;IAAe,CAAE;IAChD4I,YAAY,CAAC9L,YAAY,GAAG,CAAC;IAE7B,IAAI,CAACgH,kBAAkB,CAAC4C,+BAA+B,CAAC;MACtDC,IAAI,EAAEiC;KACP,CAAC,CAAChC,SAAS,CAACC,GAAG,IAAG;MACjB,IAAIA,GAAG,CAACC,UAAU,KAAK,CAAC,EAAE;QACxB,IAAI,CAACnD,OAAO,CAACoD,aAAa,CAAC,MAAM,CAAC;QAClC,IAAI,CAACjC,OAAO,EAAE;MAChB,CAAC,MAAM;QACL,IAAI,CAACnB,OAAO,CAACqD,YAAY,CAACH,GAAG,CAACI,OAAQ,CAAC;MACzC;IACF,CAAC,CAAC;IACFT,GAAG,CAAC3E,KAAK,EAAE;EACb;;;uCAxVWyB,8BAA8B,EAAA3H,EAAA,CAAAkN,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAApN,EAAA,CAAAkN,iBAAA,CAAAG,EAAA,CAAAC,UAAA,GAAAtN,EAAA,CAAAkN,iBAAA,CAAAK,EAAA,CAAAC,eAAA,GAAAxN,EAAA,CAAAkN,iBAAA,CAAAO,EAAA,CAAAC,cAAA,GAAA1N,EAAA,CAAAkN,iBAAA,CAAAS,EAAA,CAAAC,gBAAA,GAAA5N,EAAA,CAAAkN,iBAAA,CAAAW,EAAA,CAAAC,gBAAA,GAAA9N,EAAA,CAAAkN,iBAAA,CAAAW,EAAA,CAAAE,kBAAA,GAAA/N,EAAA,CAAAkN,iBAAA,CAAAc,EAAA,CAAAC,aAAA,GAAAjO,EAAA,CAAAkN,iBAAA,CAAAgB,EAAA,CAAAC,MAAA,GAAAnO,EAAA,CAAAkN,iBAAA,CAAAlN,EAAA,CAAAoO,UAAA;IAAA;EAAA;;;YAA9BzG,8BAA8B;MAAA0G,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAAvO,EAAA,CAAAwO,0BAAA,EAAAxO,EAAA,CAAAyO,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,wCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;UCzCzC/O,EADF,CAAAC,cAAA,iBAA0B,qBACR;UACdD,EAAA,CAAAkC,SAAA,qBAAiC;UACnClC,EAAA,CAAAG,YAAA,EAAiB;UAKbH,EAFJ,CAAAC,cAAA,sBAAoC,aACd,aACD;UACfD,EAAA,CAAAqB,UAAA,IAAA4N,6CAAA,iBAAiE;UAS/DjP,EADF,CAAAC,cAAA,aAAwC,eACM;UAAAD,EAAA,CAAAE,MAAA,+BAAI;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACxDH,EAAA,CAAAC,cAAA,gBACuD;UAArDD,EAAA,CAAAU,gBAAA,2BAAAwO,wEAAAtO,MAAA;YAAAZ,EAAA,CAAAa,aAAA,CAAAsO,GAAA;YAAAnP,EAAA,CAAAiB,kBAAA,CAAA+N,GAAA,CAAA9N,yBAAA,CAAA4B,YAAA,EAAAlC,MAAA,MAAAoO,GAAA,CAAA9N,yBAAA,CAAA4B,YAAA,GAAAlC,MAAA;YAAA,OAAAZ,EAAA,CAAAoB,WAAA,CAAAR,MAAA;UAAA,EAAoD;UACxDZ,EAFE,CAAAG,YAAA,EACuD,EACnD;UAEJH,EADF,CAAAC,cAAA,cAAwC,iBACI;UAAAD,EAAA,CAAAE,MAAA,gCAAI;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACtDH,EAAA,CAAAC,cAAA,iBACqD;UAAnDD,EAAA,CAAAU,gBAAA,2BAAA0O,wEAAAxO,MAAA;YAAAZ,EAAA,CAAAa,aAAA,CAAAsO,GAAA;YAAAnP,EAAA,CAAAiB,kBAAA,CAAA+N,GAAA,CAAA9N,yBAAA,CAAA6B,UAAA,EAAAnC,MAAA,MAAAoO,GAAA,CAAA9N,yBAAA,CAAA6B,UAAA,GAAAnC,MAAA;YAAA,OAAAZ,EAAA,CAAAoB,WAAA,CAAAR,MAAA;UAAA,EAAkD;UAExDZ,EAHI,CAAAG,YAAA,EACqD,EACjD,EACF;UAGFH,EAFJ,CAAAC,cAAA,cAAiB,cACyB,iBACI;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACpDH,EAAA,CAAAC,cAAA,qBAAqF;UAA1ED,EAAA,CAAAU,gBAAA,2BAAA2O,4EAAAzO,MAAA;YAAAZ,EAAA,CAAAa,aAAA,CAAAsO,GAAA;YAAAnP,EAAA,CAAAiB,kBAAA,CAAA+N,GAAA,CAAA9N,yBAAA,CAAA+B,UAAA,EAAArC,MAAA,MAAAoO,GAAA,CAAA9N,yBAAA,CAAA+B,UAAA,GAAArC,MAAA;YAAA,OAAAZ,EAAA,CAAAoB,WAAA,CAAAR,MAAA;UAAA,EAAkD;UAC3DZ,EAAA,CAAAqB,UAAA,KAAAiO,oDAAA,wBAA+D;UAInEtP,EADE,CAAAG,YAAA,EAAY,EACR;UAEJH,EADF,CAAAC,cAAA,cAAwC,iBACC;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACjDH,EAAA,CAAAC,cAAA,qBAAyE;UAA9DD,EAAA,CAAAU,gBAAA,2BAAA6O,4EAAA3O,MAAA;YAAAZ,EAAA,CAAAa,aAAA,CAAAsO,GAAA;YAAAnP,EAAA,CAAAiB,kBAAA,CAAA+N,GAAA,CAAA9N,yBAAA,CAAAoC,OAAA,EAAA1C,MAAA,MAAAoO,GAAA,CAAA9N,yBAAA,CAAAoC,OAAA,GAAA1C,MAAA;YAAA,OAAAZ,EAAA,CAAAoB,WAAA,CAAAR,MAAA;UAAA,EAA+C;UACxDZ,EAAA,CAAAC,cAAA,qBAAwB;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAY;UACtCH,EAAA,CAAAC,cAAA,qBAAuB;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAY;UACrCH,EAAA,CAAAC,cAAA,qBAAuB;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAE7BF,EAF6B,CAAAG,YAAA,EAAY,EAC3B,EACR;UAEJH,EADF,CAAAC,cAAA,cAAwC,iBACC;UAAAD,EAAA,CAAAE,MAAA,4CAAM;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACrDH,EAAA,CAAAC,cAAA,qBAAyE;UAA9DD,EAAA,CAAAU,gBAAA,2BAAA8O,4EAAA5O,MAAA;YAAAZ,EAAA,CAAAa,aAAA,CAAAsO,GAAA;YAAAnP,EAAA,CAAAiB,kBAAA,CAAA+N,GAAA,CAAA9N,yBAAA,CAAAwE,OAAA,EAAA9E,MAAA,MAAAoO,GAAA,CAAA9N,yBAAA,CAAAwE,OAAA,GAAA9E,MAAA;YAAA,OAAAZ,EAAA,CAAAoB,WAAA,CAAAR,MAAA;UAAA,EAA+C;UACxDZ,EAAA,CAAAC,cAAA,qBAA0B;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAY;UACxCH,EAAA,CAAAC,cAAA,qBAA0B;UAAAD,EAAA,CAAAE,MAAA,cAAC;UAAAF,EAAA,CAAAG,YAAA,EAAY;UACvCH,EAAA,CAAAC,cAAA,qBAA2B;UAAAD,EAAA,CAAAE,MAAA,cAAC;UAGlCF,EAHkC,CAAAG,YAAA,EAAY,EAC9B,EACR,EACF;UACNH,EAAA,CAAAC,cAAA,cAAiB;UACfD,EAAA,CAAAkC,SAAA,eAA4B;UAE1BlC,EADF,CAAAC,cAAA,eAAmD,kBACc;UAAxBD,EAAA,CAAA4B,UAAA,mBAAA6N,iEAAA;YAAAzP,EAAA,CAAAa,aAAA,CAAAsO,GAAA;YAAA,OAAAnP,EAAA,CAAAoB,WAAA,CAAS4N,GAAA,CAAAhG,WAAA,EAAa;UAAA,EAAC;UAAChJ,EAAA,CAAAkC,SAAA,aAAgC;UAAAlC,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAC1GH,EAAA,CAAAC,cAAA,kBAAsD;UAApBD,EAAA,CAAA4B,UAAA,mBAAA8N,iEAAA;YAAA1P,EAAA,CAAAa,aAAA,CAAAsO,GAAA;YAAA,OAAAnP,EAAA,CAAAoB,WAAA,CAAS4N,GAAA,CAAA7F,OAAA,EAAS;UAAA,EAAC;UAACnJ,EAAA,CAAAkC,SAAA,aAAkC;UAAAlC,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACnGH,EAAA,CAAAqB,UAAA,KAAAsO,iDAAA,qBAA4E;UAKpF3P,EAHM,CAAAG,YAAA,EAAM,EACF,EACF,EACO;UAIbH,EADF,CAAAC,cAAA,uBAAoC,qBACW;UAAlCD,EAAA,CAAA4B,UAAA,uBAAAgO,wEAAAhP,MAAA;YAAAZ,EAAA,CAAAa,aAAA,CAAAsO,GAAA;YAAA,OAAAnP,EAAA,CAAAoB,WAAA,CAAa4N,GAAA,CAAArC,WAAA,CAAA/L,MAAA,CAAmB;UAAA,EAAC;UAS5BZ,EARd,CAAAC,cAAA,kBAAsB,eACF,eAES,eACO,iBACmE,aACtF,cAC4D,cACjC;UAAAD,EAAA,CAAAE,MAAA,gCAAI;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACvCH,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAE,MAAA,gCAAI;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACvCH,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAE,MAAA,gCAAI;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACvCH,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACrCH,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACrCH,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACrCH,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAE,MAAA,4CAAM;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACzCH,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACrCH,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAE,MAAA,gCAAI;UAEtCF,EAFsC,CAAAG,YAAA,EAAK,EACpC,EACC;UACRH,EAAA,CAAAC,cAAA,aAAO;UACLD,EAAA,CAAAqB,UAAA,KAAAwO,6CAAA,mBAAuE;UAkB7E7P,EAFI,CAAAG,YAAA,EAAQ,EACF,EACJ;UACNH,EAAA,CAAAC,cAAA,0BAC2B;UADqBD,EAAA,CAAAU,gBAAA,wBAAAoP,8EAAAlP,MAAA;YAAAZ,EAAA,CAAAa,aAAA,CAAAsO,GAAA;YAAAnP,EAAA,CAAAiB,kBAAA,CAAA+N,GAAA,CAAA/C,SAAA,EAAArL,MAAA,MAAAoO,GAAA,CAAA/C,SAAA,GAAArL,MAAA;YAAA,OAAAZ,EAAA,CAAAoB,WAAA,CAAAR,MAAA;UAAA,EAAoB;UAClEZ,EAAA,CAAA4B,UAAA,wBAAAkO,8EAAA;YAAA9P,EAAA,CAAAa,aAAA,CAAAsO,GAAA;YAAA,OAAAnP,EAAA,CAAAoB,WAAA,CAAc4N,GAAA,CAAA7F,OAAA,EAAS;UAAA,EAAC;UAIhCnJ,EAHM,CAAAG,YAAA,EAAiB,EACb,EACF,EACC;UAUKH,EARd,CAAAC,cAAA,kBAAsB,eACF,eAES,eACO,iBACmE,aACtF,cAC4D,cACjC;UAAAD,EAAA,CAAAE,MAAA,gCAAI;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACvCH,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAE,MAAA,gCAAI;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACvCH,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACrCH,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACrCH,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACrCH,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAE,MAAA,4CAAM;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACzCH,EAAA,CAAAC,cAAA,eAA8B;UAAAD,EAAA,CAAAE,MAAA,qBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACrCH,EAAA,CAAAC,cAAA,eAA8B;UAAAD,EAAA,CAAAE,MAAA,iCAAI;UAEtCF,EAFsC,CAAAG,YAAA,EAAK,EACpC,EACC;UACRH,EAAA,CAAAC,cAAA,cAAO;UACLD,EAAA,CAAAqB,UAAA,MAAA0O,8CAAA,mBAAuE;UAiB7E/P,EAFI,CAAAG,YAAA,EAAQ,EACF,EACJ;UACNH,EAAA,CAAAC,cAAA,2BAC2B;UADqBD,EAAA,CAAAU,gBAAA,wBAAAsP,+EAAApP,MAAA;YAAAZ,EAAA,CAAAa,aAAA,CAAAsO,GAAA;YAAAnP,EAAA,CAAAiB,kBAAA,CAAA+N,GAAA,CAAA/C,SAAA,EAAArL,MAAA,MAAAoO,GAAA,CAAA/C,SAAA,GAAArL,MAAA;YAAA,OAAAZ,EAAA,CAAAoB,WAAA,CAAAR,MAAA;UAAA,EAAoB;UAClEZ,EAAA,CAAA4B,UAAA,wBAAAoO,+EAAA;YAAAhQ,EAAA,CAAAa,aAAA,CAAAsO,GAAA;YAAA,OAAAnP,EAAA,CAAAoB,WAAA,CAAc4N,GAAA,CAAA7F,OAAA,EAAS;UAAA,EAAC;UAOtCnJ,EANY,CAAAG,YAAA,EAAiB,EACb,EACF,EACC,EACC,EACC,EACP;UA+EVH,EA5EA,CAAAqB,UAAA,MAAA4O,uDAAA,kCAAAjQ,EAAA,CAAAkQ,sBAAA,CAAkD,MAAAC,uDAAA,kCAAAnQ,EAAA,CAAAkQ,sBAAA,CA4EQ;;;UAtOTlQ,EAAA,CAAAO,SAAA,GAAsB;UAAtBP,EAAA,CAAAI,UAAA,SAAA4O,GAAA,CAAA5I,UAAA,OAAsB;UAW3DpG,EAAA,CAAAO,SAAA,GAAoD;UAApDP,EAAA,CAAAuB,gBAAA,YAAAyN,GAAA,CAAA9N,yBAAA,CAAA4B,YAAA,CAAoD;UAKpD9C,EAAA,CAAAO,SAAA,GAAkD;UAAlDP,EAAA,CAAAuB,gBAAA,YAAAyN,GAAA,CAAA9N,yBAAA,CAAA6B,UAAA,CAAkD;UAMzC/C,EAAA,CAAAO,SAAA,GAAkD;UAAlDP,EAAA,CAAAuB,gBAAA,YAAAyN,GAAA,CAAA9N,yBAAA,CAAA+B,UAAA,CAAkD;UAC/BjD,EAAA,CAAAO,SAAA,EAAY;UAAZP,EAAA,CAAAI,UAAA,YAAA4O,GAAA,CAAA3I,SAAA,CAAY;UAO/BrG,EAAA,CAAAO,SAAA,GAA+C;UAA/CP,EAAA,CAAAuB,gBAAA,YAAAyN,GAAA,CAAA9N,yBAAA,CAAAoC,OAAA,CAA+C;UAC7CtD,EAAA,CAAAO,SAAA,EAAY;UAAZP,EAAA,CAAAI,UAAA,aAAY;UACZJ,EAAA,CAAAO,SAAA,GAAW;UAAXP,EAAA,CAAAI,UAAA,YAAW;UACXJ,EAAA,CAAAO,SAAA,GAAW;UAAXP,EAAA,CAAAI,UAAA,YAAW;UAKbJ,EAAA,CAAAO,SAAA,GAA+C;UAA/CP,EAAA,CAAAuB,gBAAA,YAAAyN,GAAA,CAAA9N,yBAAA,CAAAwE,OAAA,CAA+C;UAC7C1F,EAAA,CAAAO,SAAA,EAAc;UAAdP,EAAA,CAAAI,UAAA,eAAc;UACdJ,EAAA,CAAAO,SAAA,GAAc;UAAdP,EAAA,CAAAI,UAAA,eAAc;UACdJ,EAAA,CAAAO,SAAA,GAAe;UAAfP,EAAA,CAAAI,UAAA,gBAAe;UASgCJ,EAAA,CAAAO,SAAA,IAAc;UAAdP,EAAA,CAAAI,UAAA,SAAA4O,GAAA,CAAAoB,QAAA,CAAc;UA8B7CpQ,EAAA,CAAAO,SAAA,IAAoB;UAApBP,EAAA,CAAAI,UAAA,YAAA4O,GAAA,CAAAxG,eAAA,CAAoB;UAmB/BxI,EAAA,CAAAO,SAAA,EAA+B;UAA/BP,EAAA,CAAAI,UAAA,mBAAA4O,GAAA,CAAA9C,YAAA,CAA+B;UAAClM,EAAA,CAAAuB,gBAAA,SAAAyN,GAAA,CAAA/C,SAAA,CAAoB;UAACjM,EAAA,CAAAI,UAAA,aAAA4O,GAAA,CAAAjD,QAAA,CAAqB;UA0B/D/L,EAAA,CAAAO,SAAA,IAAoB;UAApBP,EAAA,CAAAI,UAAA,YAAA4O,GAAA,CAAAxG,eAAA,CAAoB;UAkB/BxI,EAAA,CAAAO,SAAA,EAA+B;UAA/BP,EAAA,CAAAI,UAAA,mBAAA4O,GAAA,CAAA9C,YAAA,CAA+B;UAAClM,EAAA,CAAAuB,gBAAA,SAAAyN,GAAA,CAAA/C,SAAA,CAAoB;UAACjM,EAAA,CAAAI,UAAA,aAAA4O,GAAA,CAAAjD,QAAA,CAAqB;;;qBDhIlGhN,YAAY,EAAAwO,EAAA,CAAA8C,eAAA,EAAA9C,EAAA,CAAA+C,mBAAA,EAAA/C,EAAA,CAAAgD,qBAAA,EAAAhD,EAAA,CAAAiD,qBAAA,EACZjR,mBAAmB,EACnBN,aAAa,EAAAsO,EAAA,CAAAkD,gBAAA,EACbjR,WAAW,EAAAkR,EAAA,CAAAC,oBAAA,EAAAD,EAAA,CAAAE,mBAAA,EAAAF,EAAA,CAAAG,eAAA,EAAAH,EAAA,CAAAI,kBAAA,EAAAJ,EAAA,CAAAK,OAAA,EACX5R,cAAc,EAAAoO,EAAA,CAAAyD,iBAAA,EAAAzD,EAAA,CAAA0D,iBAAA,EACd/R,cAAc,EACdQ,IAAI,EACJD,KAAK,EACLE,mBAAmB,EACnBC,UAAU,EACVZ,gBAAgB,EAAAuO,EAAA,CAAA2D,mBAAA,EAChBrR,kBAAkB,EAClBC,oBAAoB,EACpBV,cAAc,EAAAmO,EAAA,CAAA4D,iBAAA,EAAA5D,EAAA,CAAA6D,cAAA;MAAAC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}