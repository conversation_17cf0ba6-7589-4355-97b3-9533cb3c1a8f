{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { tap } from 'rxjs/operators';\nimport { SharedModule } from '../../components/shared.module';\nimport { BaseComponent } from '../../components/base/baseComponent';\nimport { EEvent } from 'src/app/shared/services/event.service';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/shared/helper/allowHelper\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"src/app/shared/services/message.service\";\nimport * as i4 from \"src/services/api/services\";\nimport * as i5 from \"src/app/shared/services/event.service\";\nimport * as i6 from \"@angular/common\";\nimport * as i7 from \"@angular/forms\";\nimport * as i8 from \"@nebular/theme\";\nimport * as i9 from \"@ng-bootstrap/ng-bootstrap\";\nimport * as i10 from \"../../components/breadcrumb/breadcrumb.component\";\nfunction ContentManagementSalesAccountComponent_nb_option_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 19);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const case_r1 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", case_r1);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", case_r1.CBuildCaseName, \" \");\n  }\n}\nfunction ContentManagementSalesAccountComponent_nb_option_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 19);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const option_r2 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", option_r2.value);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", option_r2.label, \" \");\n  }\n}\nfunction ContentManagementSalesAccountComponent_ng_container_21_button_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 22);\n    i0.ɵɵlistener(\"click\", function ContentManagementSalesAccountComponent_ng_container_21_button_1_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r3.toggleSwitch(ctx_r3.listFormItem.CIsLock));\n    });\n    i0.ɵɵelement(1, \"i\", 23);\n    i0.ɵɵtext(2, \" \\u9396\\u5B9A \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ContentManagementSalesAccountComponent_ng_container_21_button_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 24);\n    i0.ɵɵlistener(\"click\", function ContentManagementSalesAccountComponent_ng_container_21_button_2_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r3.navidateDetai());\n    });\n    i0.ɵɵtext(1, \" \\u7DE8\\u8F2F\\u5167\\u5BB9 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ContentManagementSalesAccountComponent_ng_container_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, ContentManagementSalesAccountComponent_ng_container_21_button_1_Template, 3, 0, \"button\", 20)(2, ContentManagementSalesAccountComponent_ng_container_21_button_2_Template, 2, 0, \"button\", 21);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.isUpdate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.listFormItem.formItems && ctx_r3.isUpdate);\n  }\n}\nfunction ContentManagementSalesAccountComponent_ng_container_22_button_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 26);\n    i0.ɵɵlistener(\"click\", function ContentManagementSalesAccountComponent_ng_container_22_button_1_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r3.navidateDetai());\n    });\n    i0.ɵɵtext(1, \" \\u67E5\\u770B\\u5167\\u5BB9 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ContentManagementSalesAccountComponent_ng_container_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, ContentManagementSalesAccountComponent_ng_container_22_button_1_Template, 2, 0, \"button\", 25);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.listFormItem.formItems && ctx_r3.isUpdate);\n  }\n}\nfunction ContentManagementSalesAccountComponent_tr_32_span_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const i_r7 = ctx.$implicit;\n    const ix_r8 = ctx.index;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate2(\" \", ix_r8 > 0 ? \"\\u3001\" : \"\", \" \", i_r7.CHousehold, \" \");\n  }\n}\nfunction ContentManagementSalesAccountComponent_tr_32_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\");\n    i0.ɵɵtemplate(4, ContentManagementSalesAccountComponent_tr_32_span_4_Template, 2, 2, \"span\", 16);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const item_r9 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r9.CItemName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", item_r9.tblFormItemHouseholds);\n  }\n}\nexport class ContentManagementSalesAccountComponent extends BaseComponent {\n  toggleSwitch(CIsLock) {\n    if (CIsLock) {\n      this.unLock();\n    } else {\n      this.onLock();\n    }\n  }\n  constructor(_allow, router, message, _buildCaseService, _formItemService, _eventService) {\n    super(_allow);\n    this._allow = _allow;\n    this.router = router;\n    this.message = message;\n    this._buildCaseService = _buildCaseService;\n    this._formItemService = _formItemService;\n    this._eventService = _eventService;\n    this.tempBuildCaseID = -1;\n    this.buildingSelectedOptions = [{\n      value: '',\n      label: '全部'\n    }];\n    this.pageSize = 20;\n    this.typeContentManagementSalesAccount = {\n      CFormType: 2\n    };\n    this._eventService.receive().pipe(tap(res => {\n      if (res.action == \"GET_BUILDCASE\" /* EEvent.GET_BUILDCASE */ && !!res.payload) {\n        this.tempBuildCaseID = res.payload;\n      }\n    })).subscribe();\n  }\n  ngOnInit() {\n    this.cBuildCaseSelected = null;\n    this.getUserBuildCase();\n  }\n  getUserBuildCase() {\n    this._buildCaseService.apiBuildCaseGetUserBuildCasePost$Json({\n      body: {\n        CBuildCaseId: this.buildCaseId\n      }\n    }).pipe(tap(res => {\n      if (res.Entries && res.StatusCode == 0) {\n        this.userBuildCaseOptions = res.Entries.map(res => {\n          return {\n            CBuildCaseName: res.CBuildCaseName,\n            cID: res.cID\n          };\n        });\n        if (this.tempBuildCaseID != -1) {\n          let index = this.userBuildCaseOptions.findIndex(x => x.cID == this.tempBuildCaseID);\n          this.cBuildCaseSelected = this.userBuildCaseOptions[index];\n        } else {\n          this.cBuildCaseSelected = this.userBuildCaseOptions[0];\n        }\n        if (this.cBuildCaseSelected.cID) {\n          this.getListFormItem();\n        }\n      }\n    })).subscribe();\n  }\n  getListFormItem() {\n    this._formItemService.apiFormItemGetListFormItemPost$Json({\n      body: {\n        CBuildCaseId: this.cBuildCaseSelected.cID,\n        CFormType: this.typeContentManagementSalesAccount.CFormType,\n        PageIndex: this.pageIndex,\n        PageSize: this.pageSize,\n        CIsPaging: true\n      }\n    }).pipe(tap(res => {\n      if (res.Entries && res.StatusCode == 0) {\n        this.formItems = res.Entries.formItems;\n        this.listFormItem = res.Entries;\n        this.totalRecords = res.TotalItems ? res.TotalItems : 0;\n      }\n    })).subscribe();\n  }\n  onSelectionChangeBuildCase() {\n    this.getListFormItem();\n  }\n  pageChanged(newPage) {\n    this.pageIndex = newPage;\n    this.getListFormItem();\n  }\n  onLock() {\n    this._formItemService.apiFormItemLockFormItemPost$Json({\n      body: {\n        CBuildCaseId: this.cBuildCaseSelected.cID,\n        CFormId: this.listFormItem.CFormId\n      }\n    }).subscribe(res => {\n      if (res.StatusCode == 0) {\n        this.message.showSucessMSG(\"執行成功\");\n      } else {\n        // this.message.showErrorMSG(res.Message!);\n        this.message.showErrorMSG(\"無資料，不可鎖定\");\n      }\n      this.getListFormItem();\n    });\n  }\n  unLock() {\n    this._formItemService.apiFormItemUnlockFormItemPost$Json({\n      body: {\n        CBuildCaseID: this.cBuildCaseSelected.cID,\n        CFormId: this.listFormItem.CFormId\n      }\n    }).subscribe(res => {\n      if (res.StatusCode == 0) {\n        this.message.showSucessMSG(\"執行成功\");\n      } else {\n        this.message.showErrorMSG(res.Message);\n      }\n      this.getListFormItem();\n    });\n  }\n  navidateDetai() {\n    this.router.navigate([`pages/content-management-sales-account/${this.cBuildCaseSelected.cID}`]);\n  }\n  static {\n    this.ɵfac = function ContentManagementSalesAccountComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || ContentManagementSalesAccountComponent)(i0.ɵɵdirectiveInject(i1.AllowHelper), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i3.MessageService), i0.ɵɵdirectiveInject(i4.BuildCaseService), i0.ɵɵdirectiveInject(i4.FormItemService), i0.ɵɵdirectiveInject(i5.EventService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ContentManagementSalesAccountComponent,\n      selectors: [[\"ngx-content-management-sales-account\"]],\n      standalone: true,\n      features: [i0.ɵɵInheritDefinitionFeature, i0.ɵɵStandaloneFeature],\n      decls: 35,\n      vars: 10,\n      consts: [[\"accent\", \"success\"], [1, \"font-bold\", \"text-[#818181]\"], [1, \"d-flex\", \"flex-wrap\"], [1, \"col-md-4\"], [1, \"form-group\", \"d-flex\", \"align-items-center\", \"w-full\"], [\"for\", \"buildingName\", 1, \"label\", \"col-3\"], [\"placeholder\", \"\\u5EFA\\u6848\", 1, \"col-9\", 3, \"ngModelChange\", \"selectedChange\", \"ngModel\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"for\", \"houseType\", 1, \"label\", \"col-3\"], [\"placeholder\", \"\\u9078\\u64C7\\u6236\\u578B\", 1, \"col-9\", 3, \"ngModelChange\", \"selectedChange\", \"ngModel\"], [1, \"d-flex\", \"justify-content-end\", \"w-full\"], [4, \"ngIf\"], [1, \"table-responsive\", \"mt-4\"], [1, \"table\", \"table-striped\", \"border\", 2, \"background-color\", \"#f3f3f3\"], [2, \"background-color\", \"#27ae60\", \"color\", \"white\"], [\"scope\", \"col\", 1, \"col-1\"], [4, \"ngFor\", \"ngForOf\"], [1, \"d-flex\", \"justify-content-center\"], [\"aria-label\", \"Pagination\", 3, \"pageChange\", \"page\", \"pageSize\", \"collectionSize\"], [3, \"value\"], [\"class\", \"btn btn-danger mx-1\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"btn btn-info\", 3, \"click\", 4, \"ngIf\"], [1, \"btn\", \"btn-danger\", \"mx-1\", 3, \"click\"], [1, \"fas\", \"fa-lock\"], [1, \"btn\", \"btn-info\", 3, \"click\"], [\"class\", \"btn btn-secondary\", 3, \"click\", 4, \"ngIf\"], [1, \"btn\", \"btn-secondary\", 3, \"click\"]],\n      template: function ContentManagementSalesAccountComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"nb-card\", 0)(1, \"nb-card-header\");\n          i0.ɵɵelement(2, \"ngx-breadcrumb\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(3, \"nb-card-body\")(4, \"h1\", 1);\n          i0.ɵɵtext(5, \" \\u60A8\\u53EF\\u5C07\\u65BC\\u5EFA\\u6750\\u7BA1\\u7406\\u53CA\\u65B9\\u6848\\u7BA1\\u7406\\u8A2D\\u5B9A\\u597D\\u7684\\u65B9\\u6848\\u53CA\\u6750\\u6599\\uFF0C\\u65BC\\u6B64\\u7D44\\u5408\\u6210\\u9078\\u6A23\\u5167\\u5BB9\\uFF0C\\u4E26\\u53EF\\u8A2D\\u5B9A\\u5404\\u65B9\\u6848\\u3001\\u6750\\u6599\\u53EF\\u9078\\u64C7\\u4E4B\\u6236\\u578B\\u3002 \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(6, \"div\", 2)(7, \"div\", 3)(8, \"div\", 4)(9, \"label\", 5);\n          i0.ɵɵtext(10, \"\\u5EFA\\u6848\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(11, \"nb-select\", 6);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function ContentManagementSalesAccountComponent_Template_nb_select_ngModelChange_11_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.cBuildCaseSelected, $event) || (ctx.cBuildCaseSelected = $event);\n            return $event;\n          });\n          i0.ɵɵlistener(\"selectedChange\", function ContentManagementSalesAccountComponent_Template_nb_select_selectedChange_11_listener() {\n            return ctx.onSelectionChangeBuildCase();\n          });\n          i0.ɵɵtemplate(12, ContentManagementSalesAccountComponent_nb_option_12_Template, 2, 2, \"nb-option\", 7);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(13, \"div\", 3)(14, \"div\", 4)(15, \"label\", 8);\n          i0.ɵɵtext(16, \"\\u985E\\u578B\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(17, \"nb-select\", 9);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function ContentManagementSalesAccountComponent_Template_nb_select_ngModelChange_17_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.selectedHouseType, $event) || (ctx.selectedHouseType = $event);\n            return $event;\n          });\n          i0.ɵɵlistener(\"selectedChange\", function ContentManagementSalesAccountComponent_Template_nb_select_selectedChange_17_listener() {\n            return ctx.onHouseTypeChange();\n          });\n          i0.ɵɵtemplate(18, ContentManagementSalesAccountComponent_nb_option_18_Template, 2, 2, \"nb-option\", 7);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(19, \"div\", 3)(20, \"div\", 10);\n          i0.ɵɵtemplate(21, ContentManagementSalesAccountComponent_ng_container_21_Template, 3, 2, \"ng-container\", 11)(22, ContentManagementSalesAccountComponent_ng_container_22_Template, 2, 1, \"ng-container\", 11);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(23, \"div\", 12)(24, \"table\", 13)(25, \"thead\")(26, \"tr\", 14)(27, \"th\", 15);\n          i0.ɵɵtext(28, \"\\u65B9\\u6848\\u540D\\u7A31/\\u5EFA\\u6750\\u4F4D\\u7F6E\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(29, \"th\", 15);\n          i0.ɵɵtext(30, \"\\u9069\\u7528\\u6236\\u5225 \");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(31, \"tbody\");\n          i0.ɵɵtemplate(32, ContentManagementSalesAccountComponent_tr_32_Template, 5, 2, \"tr\", 16);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(33, \"nb-card-footer\", 17)(34, \"ngb-pagination\", 18);\n          i0.ɵɵtwoWayListener(\"pageChange\", function ContentManagementSalesAccountComponent_Template_ngb_pagination_pageChange_34_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.pageIndex, $event) || (ctx.pageIndex = $event);\n            return $event;\n          });\n          i0.ɵɵlistener(\"pageChange\", function ContentManagementSalesAccountComponent_Template_ngb_pagination_pageChange_34_listener($event) {\n            return ctx.pageChanged($event);\n          });\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(11);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.cBuildCaseSelected);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngForOf\", ctx.userBuildCaseOptions);\n          i0.ɵɵadvance(5);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.selectedHouseType);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngForOf\", ctx.houseTypeOptions);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", ctx.formItems && ctx.formItems.length > 0 && ctx.listFormItem && !ctx.listFormItem.CIsLock && ctx.cBuildCaseSelected && ctx.cBuildCaseSelected.cID > 0);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.formItems && ctx.formItems.length > 0 && ctx.listFormItem && ctx.listFormItem.CIsLock && ctx.cBuildCaseSelected && ctx.cBuildCaseSelected.cID > 0);\n          i0.ɵɵadvance(10);\n          i0.ɵɵproperty(\"ngForOf\", ctx.formItems);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtwoWayProperty(\"page\", ctx.pageIndex);\n          i0.ɵɵproperty(\"pageSize\", ctx.pageSize)(\"collectionSize\", ctx.totalRecords);\n        }\n      },\n      dependencies: [CommonModule, i6.NgForOf, i6.NgIf, SharedModule, i7.NgControlStatus, i7.NgModel, i8.NbCardComponent, i8.NbCardBodyComponent, i8.NbCardFooterComponent, i8.NbCardHeaderComponent, i8.NbSelectComponent, i8.NbOptionComponent, i9.NgbPagination, i10.BreadcrumbComponent],\n      styles: [\"/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsImZpbGUiOiJjb250ZW50LW1hbmFnZW1lbnQtc2FsZXMtYWNjb3VudC5jb21wb25lbnQuc2NzcyJ9 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvcGFnZXMvc2VsZWN0aW9uLW1hbmFnZW1lbnQvY29udGVudC1tYW5hZ2VtZW50LXNhbGVzLWFjY291bnQvY29udGVudC1tYW5hZ2VtZW50LXNhbGVzLWFjY291bnQuY29tcG9uZW50LnNjc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IjtBQUNBLG9NQUFvTSIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "tap", "SharedModule", "BaseComponent", "EEvent", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵproperty", "case_r1", "ɵɵadvance", "ɵɵtextInterpolate1", "CBuildCaseName", "option_r2", "value", "label", "ɵɵlistener", "ContentManagementSalesAccountComponent_ng_container_21_button_1_Template_button_click_0_listener", "ɵɵrestoreView", "_r3", "ctx_r3", "ɵɵnextContext", "ɵɵresetView", "toggleSwitch", "listFormItem", "CIsLock", "ɵɵelement", "ContentManagementSalesAccountComponent_ng_container_21_button_2_Template_button_click_0_listener", "_r5", "navid<PERSON><PERSON><PERSON><PERSON>", "ɵɵelementContainerStart", "ɵɵtemplate", "ContentManagementSalesAccountComponent_ng_container_21_button_1_Template", "ContentManagementSalesAccountComponent_ng_container_21_button_2_Template", "isUpdate", "formItems", "ContentManagementSalesAccountComponent_ng_container_22_button_1_Template_button_click_0_listener", "_r6", "ContentManagementSalesAccountComponent_ng_container_22_button_1_Template", "ɵɵtextInterpolate2", "ix_r8", "i_r7", "CHousehold", "ContentManagementSalesAccountComponent_tr_32_span_4_Template", "ɵɵtextInterpolate", "item_r9", "CItemName", "tblFormItemHouseholds", "ContentManagementSalesAccountComponent", "unLock", "onLock", "constructor", "_allow", "router", "message", "_buildCaseService", "_formItemService", "_eventService", "tempBuildCaseID", "buildingSelectedOptions", "pageSize", "typeContentManagementSalesAccount", "CFormType", "receive", "pipe", "res", "action", "payload", "subscribe", "ngOnInit", "cBuildCaseSelected", "getUserBuildCase", "apiBuildCaseGetUserBuildCasePost$Json", "body", "CBuildCaseId", "buildCaseId", "Entries", "StatusCode", "userBuildCaseOptions", "map", "cID", "index", "findIndex", "x", "getListFormItem", "apiFormItemGetListFormItemPost$Json", "PageIndex", "pageIndex", "PageSize", "CIsPaging", "totalRecords", "TotalItems", "onSelectionChangeBuildCase", "pageChanged", "newPage", "apiFormItemLockFormItemPost$Json", "CFormId", "showSucessMSG", "showErrorMSG", "apiFormItemUnlockFormItemPost$Json", "CBuildCaseID", "Message", "navigate", "ɵɵdirectiveInject", "i1", "AllowHelper", "i2", "Router", "i3", "MessageService", "i4", "BuildCaseService", "FormItemService", "i5", "EventService", "selectors", "standalone", "features", "ɵɵInheritDefinitionFeature", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "ContentManagementSalesAccountComponent_Template", "rf", "ctx", "ɵɵtwoWayListener", "ContentManagementSalesAccountComponent_Template_nb_select_ngModelChange_11_listener", "$event", "ɵɵtwoWayBindingSet", "ContentManagementSalesAccountComponent_Template_nb_select_selectedChange_11_listener", "ContentManagementSalesAccountComponent_nb_option_12_Template", "ContentManagementSalesAccountComponent_Template_nb_select_ngModelChange_17_listener", "selectedHouseType", "ContentManagementSalesAccountComponent_Template_nb_select_selectedChange_17_listener", "onHouseTypeChange", "ContentManagementSalesAccountComponent_nb_option_18_Template", "ContentManagementSalesAccountComponent_ng_container_21_Template", "ContentManagementSalesAccountComponent_ng_container_22_Template", "ContentManagementSalesAccountComponent_tr_32_Template", "ContentManagementSalesAccountComponent_Template_ngb_pagination_pageChange_34_listener", "ɵɵtwoWayProperty", "houseTypeOptions", "length", "i6", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "i7", "NgControlStatus", "NgModel", "i8", "NbCardComponent", "NbCardBodyComponent", "NbCardFooterComponent", "NbCardHeaderComponent", "NbSelectComponent", "NbOptionComponent", "i9", "NgbPagination", "i10", "BreadcrumbComponent", "styles"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\selection-management\\content-management-sales-account\\content-management-sales-account.component.ts", "C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\selection-management\\content-management-sales-account\\content-management-sales-account.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { Router } from '@angular/router';\r\nimport { tap } from 'rxjs/operators';\r\nimport { AllowHelper } from 'src/app/shared/helper/allowHelper';\r\nimport { BuildCaseService, FormItemService } from 'src/services/api/services';\r\nimport { MessageService } from 'src/app/shared/services/message.service';\r\nimport { GetListFormItemRes } from 'src/services/api/models';\r\nimport { SharedModule } from '../../components/shared.module';\r\nimport { BaseComponent } from '../../components/base/baseComponent';\r\nimport { EventService, IEvent, EEvent } from 'src/app/shared/services/event.service';\r\n\r\nexport interface selectItem {\r\n  label: string,\r\n  value: number,\r\n  key?: string\r\n}\r\n\r\n@Component({\r\n  selector: 'ngx-content-management-sales-account',\r\n  templateUrl: './content-management-sales-account.component.html',\r\n  styleUrls: ['./content-management-sales-account.component.scss'],\r\n  standalone: true,\r\n  imports: [CommonModule, SharedModule,],\r\n})\r\nexport class ContentManagementSalesAccountComponent extends BaseComponent implements OnInit {\r\n\r\n\r\n  toggleSwitch(CIsLock: any) {\r\n    if(CIsLock) {\r\n      this.unLock()\r\n    } else {\r\n      this.onLock()\r\n    }\r\n  }\r\n\r\n  tempBuildCaseID: number = -1\r\n  selectedBuilding: any;\r\n  buildingSelectedOptions: any[] = [{ value: '', label: '全部' }];\r\n\r\n  formItems: any;\r\n  listFormItem: GetListFormItemRes;\r\n  override pageSize = 20;\r\n\r\n  buildCaseId: number;\r\n  cBuildCaseSelected: any;\r\n  userBuildCaseOptions: any;\r\n\r\n  constructor(\r\n    private _allow: AllowHelper,\r\n    private router: Router,\r\n    private message: MessageService,\r\n    private _buildCaseService: BuildCaseService,\r\n    private _formItemService: FormItemService,\r\n    private _eventService: EventService,\r\n  ) {\r\n    super(_allow);\r\n    this._eventService.receive().pipe(\r\n      tap((res: IEvent) => {\r\n        if (res.action == EEvent.GET_BUILDCASE && !!res.payload) {\r\n          this.tempBuildCaseID = res.payload\r\n        }\r\n      })\r\n    ).subscribe()\r\n  }\r\n\r\n  override ngOnInit(): void {\r\n    this.cBuildCaseSelected = null;\r\n    this.getUserBuildCase()\r\n  }\r\n\r\n  getUserBuildCase() {\r\n    this._buildCaseService.apiBuildCaseGetUserBuildCasePost$Json({\r\n      body: {\r\n        CBuildCaseId: this.buildCaseId\r\n      }\r\n    }).pipe(\r\n      tap(res => {\r\n        if (res.Entries && res.StatusCode == 0) {\r\n          this.userBuildCaseOptions = res.Entries.map(res => {\r\n            return {\r\n              CBuildCaseName: res.CBuildCaseName,\r\n              cID: res.cID\r\n            };\r\n          });\r\n\r\n          if (this.tempBuildCaseID != -1) {\r\n            let index = this.userBuildCaseOptions.findIndex((x: any) => x.cID == this.tempBuildCaseID)\r\n            this.cBuildCaseSelected = this.userBuildCaseOptions[index]\r\n          } else {\r\n            this.cBuildCaseSelected = this.userBuildCaseOptions[0];\r\n          }\r\n          if (this.cBuildCaseSelected.cID) {\r\n            this.getListFormItem();\r\n          }\r\n        }\r\n      })\r\n    ).subscribe();\r\n  }\r\n\r\n  typeContentManagementSalesAccount = {\r\n    CFormType: 2,\r\n  }\r\n\r\n\r\n  getListFormItem() {\r\n    this._formItemService.apiFormItemGetListFormItemPost$Json({\r\n      body: {\r\n        CBuildCaseId: this.cBuildCaseSelected.cID,\r\n        CFormType: this.typeContentManagementSalesAccount.CFormType,\r\n        PageIndex: this.pageIndex,\r\n        PageSize: this.pageSize,\r\n        CIsPaging: true\r\n      }\r\n    }).pipe(\r\n      tap(res => {\r\n        if (res.Entries && res.StatusCode == 0) {\r\n          this.formItems = res.Entries.formItems;\r\n          this.listFormItem = res.Entries;\r\n          this.totalRecords = res.TotalItems ? res.TotalItems : 0\r\n        }\r\n      })\r\n    ).subscribe();\r\n  }\r\n\r\n  onSelectionChangeBuildCase() {\r\n    this.getListFormItem();\r\n  }\r\n\r\n  pageChanged(newPage: number) {\r\n    this.pageIndex = newPage;\r\n    this.getListFormItem();\r\n  }\r\n\r\n  onLock() {\r\n    this._formItemService.apiFormItemLockFormItemPost$Json({\r\n      body: {\r\n        CBuildCaseId: this.cBuildCaseSelected.cID,\r\n        CFormId: this.listFormItem.CFormId\r\n      }\r\n    }).subscribe(res => {\r\n      if (res.StatusCode == 0) {\r\n        this.message.showSucessMSG(\"執行成功\");\r\n      } else {\r\n        // this.message.showErrorMSG(res.Message!);\r\n        this.message.showErrorMSG(\"無資料，不可鎖定\");\r\n      }\r\n      this.getListFormItem();\r\n    });\r\n  }\r\n\r\n  unLock() {\r\n    this._formItemService.apiFormItemUnlockFormItemPost$Json({\r\n      body: {\r\n        CBuildCaseID: this.cBuildCaseSelected.cID,\r\n        CFormId: this.listFormItem.CFormId\r\n      }\r\n    }).subscribe(res => {\r\n      if (res.StatusCode == 0) {\r\n        this.message.showSucessMSG(\"執行成功\");\r\n      } else {\r\n        this.message.showErrorMSG(res.Message!);\r\n      }\r\n      this.getListFormItem();\r\n    });\r\n  }\r\n\r\n  navidateDetai() {\r\n    this.router.navigate([`pages/content-management-sales-account/${this.cBuildCaseSelected.cID}`]);\r\n  }\r\n}\r\n", "<!-- 3.6  3.7 = 1, 3.6 = 2-->\r\n<nb-card accent=\"success\">\r\n  <nb-card-header>\r\n    <ngx-breadcrumb></ngx-breadcrumb>\r\n  </nb-card-header>\r\n  <nb-card-body>\r\n    <h1 class=\"font-bold text-[#818181]\">\r\n      您可將於建材管理及方案管理設定好的方案及材料，於此組合成選樣內容，並可設定各方案、材料可選擇之戶型。\r\n    </h1>\r\n    <div class=\"d-flex flex-wrap\">\r\n      <div class=\"col-md-4\">\r\n        <div class=\"form-group d-flex align-items-center w-full\">\r\n          <label for=\"buildingName\" class=\"label col-3\">建案</label>\r\n          <nb-select placeholder=\"建案\" [(ngModel)]=\"cBuildCaseSelected\" class=\"col-9\"\r\n            (selectedChange)=\"onSelectionChangeBuildCase()\">\r\n            <nb-option *ngFor=\"let case of userBuildCaseOptions\" [value]=\"case\">\r\n              {{ case.CBuildCaseName }}\r\n            </nb-option>\r\n          </nb-select>\r\n        </div>\r\n      </div>\r\n      <div class=\"col-md-4\">\r\n        <div class=\"form-group d-flex align-items-center w-full\">\r\n          <label for=\"houseType\" class=\"label col-3\">類型</label>\r\n          <nb-select placeholder=\"選擇戶型\" [(ngModel)]=\"selectedHouseType\" class=\"col-9\"\r\n            (selectedChange)=\"onHouseTypeChange()\">\r\n            <nb-option *ngFor=\"let option of houseTypeOptions\" [value]=\"option.value\">\r\n              {{ option.label }}\r\n            </nb-option>\r\n          </nb-select>\r\n        </div>\r\n      </div>\r\n      <div class=\"col-md-4\">\r\n        <div class=\"d-flex justify-content-end w-full\">\r\n\r\n          <!-- 當有資料且未鎖定時顯示的按鈕 -->\r\n          <ng-container\r\n            *ngIf=\"formItems && formItems.length > 0 && listFormItem && !listFormItem.CIsLock && cBuildCaseSelected && cBuildCaseSelected.cID > 0\">\r\n            <!-- 鎖定按鈕 -->\r\n            <button class=\"btn btn-danger mx-1\" *ngIf=\"isUpdate\" (click)=\"toggleSwitch(listFormItem.CIsLock)\">\r\n              <i class=\"fas fa-lock\"></i> 鎖定\r\n            </button>\r\n\r\n            <!-- 編輯按鈕 -->\r\n            <button class=\"btn btn-info\" *ngIf=\"listFormItem.formItems && isUpdate\" (click)=\"navidateDetai()\">\r\n              編輯內容\r\n            </button>\r\n          </ng-container>\r\n\r\n          <!-- 當有資料且已鎖定時顯示的按鈕 -->\r\n          <ng-container\r\n            *ngIf=\"formItems && formItems.length > 0 && listFormItem && listFormItem.CIsLock && cBuildCaseSelected && cBuildCaseSelected.cID > 0\">\r\n            <!-- 查看按鈕 -->\r\n            <button class=\"btn btn-secondary\" *ngIf=\"listFormItem.formItems && isUpdate\" (click)=\"navidateDetai()\">\r\n              查看內容\r\n            </button>\r\n          </ng-container>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <div class=\"table-responsive mt-4\">\r\n      <table class=\"table table-striped border \" style=\"background-color:#f3f3f3;\">\r\n        <thead>\r\n          <tr style=\"background-color: #27ae60; color: white;\">\r\n            <th scope=\"col\" class=\"col-1\">方案名稱/建材位置</th>\r\n            <th scope=\"col\" class=\"col-1\">適用戶別 </th>\r\n          </tr>\r\n        </thead>\r\n        <tbody>\r\n          <tr *ngFor=\"let item of formItems ; let i = index\">\r\n            <td>{{ item.CItemName}}</td>\r\n            <td>\r\n              <span *ngFor=\"let i of item.tblFormItemHouseholds ; let ix = index\">\r\n                {{ix > 0 ? '、' :''}} {{i.CHousehold}}\r\n              </span>\r\n            </td>\r\n          </tr>\r\n        </tbody>\r\n      </table>\r\n    </div>\r\n  </nb-card-body>\r\n  <nb-card-footer class=\"d-flex justify-content-center\">\r\n    <ngb-pagination [(page)]=\"pageIndex\" [pageSize]=\"pageSize\" [collectionSize]=\"totalRecords\"\r\n      (pageChange)=\"pageChanged($event)\" aria-label=\"Pagination\">\r\n    </ngb-pagination>\r\n  </nb-card-footer>\r\n</nb-card>"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAE9C,SAASC,GAAG,QAAQ,gBAAgB;AAKpC,SAASC,YAAY,QAAQ,gCAAgC;AAC7D,SAASC,aAAa,QAAQ,qCAAqC;AACnE,SAA+BC,MAAM,QAAQ,uCAAuC;;;;;;;;;;;;;;ICKxEC,EAAA,CAAAC,cAAA,oBAAoE;IAClED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAFyCH,EAAA,CAAAI,UAAA,UAAAC,OAAA,CAAc;IACjEL,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAAO,kBAAA,MAAAF,OAAA,CAAAG,cAAA,MACF;;;;;IASAR,EAAA,CAAAC,cAAA,oBAA0E;IACxED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAFuCH,EAAA,CAAAI,UAAA,UAAAK,SAAA,CAAAC,KAAA,CAAsB;IACvEV,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAAO,kBAAA,MAAAE,SAAA,CAAAE,KAAA,MACF;;;;;;IAWAX,EAAA,CAAAC,cAAA,iBAAkG;IAA7CD,EAAA,CAAAY,UAAA,mBAAAC,iGAAA;MAAAb,EAAA,CAAAc,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAhB,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAASF,MAAA,CAAAG,YAAA,CAAAH,MAAA,CAAAI,YAAA,CAAAC,OAAA,CAAkC;IAAA,EAAC;IAC/FrB,EAAA,CAAAsB,SAAA,YAA2B;IAACtB,EAAA,CAAAE,MAAA,qBAC9B;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;IAGTH,EAAA,CAAAC,cAAA,iBAAkG;IAA1BD,EAAA,CAAAY,UAAA,mBAAAW,iGAAA;MAAAvB,EAAA,CAAAc,aAAA,CAAAU,GAAA;MAAA,MAAAR,MAAA,GAAAhB,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAASF,MAAA,CAAAS,aAAA,EAAe;IAAA,EAAC;IAC/FzB,EAAA,CAAAE,MAAA,iCACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;IAVXH,EAAA,CAAA0B,uBAAA,GACyI;IAOvI1B,EALA,CAAA2B,UAAA,IAAAC,wEAAA,qBAAkG,IAAAC,wEAAA,qBAKA;;;;;IAL7D7B,EAAA,CAAAM,SAAA,EAAc;IAAdN,EAAA,CAAAI,UAAA,SAAAY,MAAA,CAAAc,QAAA,CAAc;IAKrB9B,EAAA,CAAAM,SAAA,EAAwC;IAAxCN,EAAA,CAAAI,UAAA,SAAAY,MAAA,CAAAI,YAAA,CAAAW,SAAA,IAAAf,MAAA,CAAAc,QAAA,CAAwC;;;;;;IAStE9B,EAAA,CAAAC,cAAA,iBAAuG;IAA1BD,EAAA,CAAAY,UAAA,mBAAAoB,iGAAA;MAAAhC,EAAA,CAAAc,aAAA,CAAAmB,GAAA;MAAA,MAAAjB,MAAA,GAAAhB,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAASF,MAAA,CAAAS,aAAA,EAAe;IAAA,EAAC;IACpGzB,EAAA,CAAAE,MAAA,iCACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;IALXH,EAAA,CAAA0B,uBAAA,GACwI;IAEtI1B,EAAA,CAAA2B,UAAA,IAAAO,wEAAA,qBAAuG;;;;;IAApElC,EAAA,CAAAM,SAAA,EAAwC;IAAxCN,EAAA,CAAAI,UAAA,SAAAY,MAAA,CAAAI,YAAA,CAAAW,SAAA,IAAAf,MAAA,CAAAc,QAAA,CAAwC;;;;;IAoBzE9B,EAAA,CAAAC,cAAA,WAAoE;IAClED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IADLH,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAAmC,kBAAA,MAAAC,KAAA,2BAAAC,IAAA,CAAAC,UAAA,MACF;;;;;IAJFtC,EADF,CAAAC,cAAA,SAAmD,SAC7C;IAAAD,EAAA,CAAAE,MAAA,GAAmB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC5BH,EAAA,CAAAC,cAAA,SAAI;IACFD,EAAA,CAAA2B,UAAA,IAAAY,4DAAA,mBAAoE;IAIxEvC,EADE,CAAAG,YAAA,EAAK,EACF;;;;IANCH,EAAA,CAAAM,SAAA,GAAmB;IAAnBN,EAAA,CAAAwC,iBAAA,CAAAC,OAAA,CAAAC,SAAA,CAAmB;IAED1C,EAAA,CAAAM,SAAA,GAAgC;IAAhCN,EAAA,CAAAI,UAAA,YAAAqC,OAAA,CAAAE,qBAAA,CAAgC;;;ADhDlE,OAAM,MAAOC,sCAAuC,SAAQ9C,aAAa;EAGvEqB,YAAYA,CAACE,OAAY;IACvB,IAAGA,OAAO,EAAE;MACV,IAAI,CAACwB,MAAM,EAAE;IACf,CAAC,MAAM;MACL,IAAI,CAACC,MAAM,EAAE;IACf;EACF;EAcAC,YACUC,MAAmB,EACnBC,MAAc,EACdC,OAAuB,EACvBC,iBAAmC,EACnCC,gBAAiC,EACjCC,aAA2B;IAEnC,KAAK,CAACL,MAAM,CAAC;IAPL,KAAAA,MAAM,GAANA,MAAM;IACN,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAC,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,aAAa,GAAbA,aAAa;IAlBvB,KAAAC,eAAe,GAAW,CAAC,CAAC;IAE5B,KAAAC,uBAAuB,GAAU,CAAC;MAAE7C,KAAK,EAAE,EAAE;MAAEC,KAAK,EAAE;IAAI,CAAE,CAAC;IAIpD,KAAA6C,QAAQ,GAAG,EAAE;IA0DtB,KAAAC,iCAAiC,GAAG;MAClCC,SAAS,EAAE;KACZ;IA7CC,IAAI,CAACL,aAAa,CAACM,OAAO,EAAE,CAACC,IAAI,CAC/BhE,GAAG,CAAEiE,GAAW,IAAI;MAClB,IAAIA,GAAG,CAACC,MAAM,kDAA4B,CAAC,CAACD,GAAG,CAACE,OAAO,EAAE;QACvD,IAAI,CAACT,eAAe,GAAGO,GAAG,CAACE,OAAO;MACpC;IACF,CAAC,CAAC,CACH,CAACC,SAAS,EAAE;EACf;EAESC,QAAQA,CAAA;IACf,IAAI,CAACC,kBAAkB,GAAG,IAAI;IAC9B,IAAI,CAACC,gBAAgB,EAAE;EACzB;EAEAA,gBAAgBA,CAAA;IACd,IAAI,CAAChB,iBAAiB,CAACiB,qCAAqC,CAAC;MAC3DC,IAAI,EAAE;QACJC,YAAY,EAAE,IAAI,CAACC;;KAEtB,CAAC,CAACX,IAAI,CACLhE,GAAG,CAACiE,GAAG,IAAG;MACR,IAAIA,GAAG,CAACW,OAAO,IAAIX,GAAG,CAACY,UAAU,IAAI,CAAC,EAAE;QACtC,IAAI,CAACC,oBAAoB,GAAGb,GAAG,CAACW,OAAO,CAACG,GAAG,CAACd,GAAG,IAAG;UAChD,OAAO;YACLrD,cAAc,EAAEqD,GAAG,CAACrD,cAAc;YAClCoE,GAAG,EAAEf,GAAG,CAACe;WACV;QACH,CAAC,CAAC;QAEF,IAAI,IAAI,CAACtB,eAAe,IAAI,CAAC,CAAC,EAAE;UAC9B,IAAIuB,KAAK,GAAG,IAAI,CAACH,oBAAoB,CAACI,SAAS,CAAEC,CAAM,IAAKA,CAAC,CAACH,GAAG,IAAI,IAAI,CAACtB,eAAe,CAAC;UAC1F,IAAI,CAACY,kBAAkB,GAAG,IAAI,CAACQ,oBAAoB,CAACG,KAAK,CAAC;QAC5D,CAAC,MAAM;UACL,IAAI,CAACX,kBAAkB,GAAG,IAAI,CAACQ,oBAAoB,CAAC,CAAC,CAAC;QACxD;QACA,IAAI,IAAI,CAACR,kBAAkB,CAACU,GAAG,EAAE;UAC/B,IAAI,CAACI,eAAe,EAAE;QACxB;MACF;IACF,CAAC,CAAC,CACH,CAAChB,SAAS,EAAE;EACf;EAOAgB,eAAeA,CAAA;IACb,IAAI,CAAC5B,gBAAgB,CAAC6B,mCAAmC,CAAC;MACxDZ,IAAI,EAAE;QACJC,YAAY,EAAE,IAAI,CAACJ,kBAAkB,CAACU,GAAG;QACzClB,SAAS,EAAE,IAAI,CAACD,iCAAiC,CAACC,SAAS;QAC3DwB,SAAS,EAAE,IAAI,CAACC,SAAS;QACzBC,QAAQ,EAAE,IAAI,CAAC5B,QAAQ;QACvB6B,SAAS,EAAE;;KAEd,CAAC,CAACzB,IAAI,CACLhE,GAAG,CAACiE,GAAG,IAAG;MACR,IAAIA,GAAG,CAACW,OAAO,IAAIX,GAAG,CAACY,UAAU,IAAI,CAAC,EAAE;QACtC,IAAI,CAAC1C,SAAS,GAAG8B,GAAG,CAACW,OAAO,CAACzC,SAAS;QACtC,IAAI,CAACX,YAAY,GAAGyC,GAAG,CAACW,OAAO;QAC/B,IAAI,CAACc,YAAY,GAAGzB,GAAG,CAAC0B,UAAU,GAAG1B,GAAG,CAAC0B,UAAU,GAAG,CAAC;MACzD;IACF,CAAC,CAAC,CACH,CAACvB,SAAS,EAAE;EACf;EAEAwB,0BAA0BA,CAAA;IACxB,IAAI,CAACR,eAAe,EAAE;EACxB;EAEAS,WAAWA,CAACC,OAAe;IACzB,IAAI,CAACP,SAAS,GAAGO,OAAO;IACxB,IAAI,CAACV,eAAe,EAAE;EACxB;EAEAlC,MAAMA,CAAA;IACJ,IAAI,CAACM,gBAAgB,CAACuC,gCAAgC,CAAC;MACrDtB,IAAI,EAAE;QACJC,YAAY,EAAE,IAAI,CAACJ,kBAAkB,CAACU,GAAG;QACzCgB,OAAO,EAAE,IAAI,CAACxE,YAAY,CAACwE;;KAE9B,CAAC,CAAC5B,SAAS,CAACH,GAAG,IAAG;MACjB,IAAIA,GAAG,CAACY,UAAU,IAAI,CAAC,EAAE;QACvB,IAAI,CAACvB,OAAO,CAAC2C,aAAa,CAAC,MAAM,CAAC;MACpC,CAAC,MAAM;QACL;QACA,IAAI,CAAC3C,OAAO,CAAC4C,YAAY,CAAC,UAAU,CAAC;MACvC;MACA,IAAI,CAACd,eAAe,EAAE;IACxB,CAAC,CAAC;EACJ;EAEAnC,MAAMA,CAAA;IACJ,IAAI,CAACO,gBAAgB,CAAC2C,kCAAkC,CAAC;MACvD1B,IAAI,EAAE;QACJ2B,YAAY,EAAE,IAAI,CAAC9B,kBAAkB,CAACU,GAAG;QACzCgB,OAAO,EAAE,IAAI,CAACxE,YAAY,CAACwE;;KAE9B,CAAC,CAAC5B,SAAS,CAACH,GAAG,IAAG;MACjB,IAAIA,GAAG,CAACY,UAAU,IAAI,CAAC,EAAE;QACvB,IAAI,CAACvB,OAAO,CAAC2C,aAAa,CAAC,MAAM,CAAC;MACpC,CAAC,MAAM;QACL,IAAI,CAAC3C,OAAO,CAAC4C,YAAY,CAACjC,GAAG,CAACoC,OAAQ,CAAC;MACzC;MACA,IAAI,CAACjB,eAAe,EAAE;IACxB,CAAC,CAAC;EACJ;EAEAvD,aAAaA,CAAA;IACX,IAAI,CAACwB,MAAM,CAACiD,QAAQ,CAAC,CAAC,0CAA0C,IAAI,CAAChC,kBAAkB,CAACU,GAAG,EAAE,CAAC,CAAC;EACjG;;;uCAhJWhC,sCAAsC,EAAA5C,EAAA,CAAAmG,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAArG,EAAA,CAAAmG,iBAAA,CAAAG,EAAA,CAAAC,MAAA,GAAAvG,EAAA,CAAAmG,iBAAA,CAAAK,EAAA,CAAAC,cAAA,GAAAzG,EAAA,CAAAmG,iBAAA,CAAAO,EAAA,CAAAC,gBAAA,GAAA3G,EAAA,CAAAmG,iBAAA,CAAAO,EAAA,CAAAE,eAAA,GAAA5G,EAAA,CAAAmG,iBAAA,CAAAU,EAAA,CAAAC,YAAA;IAAA;EAAA;;;YAAtClE,sCAAsC;MAAAmE,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAAjH,EAAA,CAAAkH,0BAAA,EAAAlH,EAAA,CAAAmH,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,gDAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCvBjDzH,EADF,CAAAC,cAAA,iBAA0B,qBACR;UACdD,EAAA,CAAAsB,SAAA,qBAAiC;UACnCtB,EAAA,CAAAG,YAAA,EAAiB;UAEfH,EADF,CAAAC,cAAA,mBAAc,YACyB;UACnCD,EAAA,CAAAE,MAAA,qTACF;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAICH,EAHN,CAAAC,cAAA,aAA8B,aACN,aACqC,eACT;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACxDH,EAAA,CAAAC,cAAA,oBACkD;UADtBD,EAAA,CAAA2H,gBAAA,2BAAAC,oFAAAC,MAAA;YAAA7H,EAAA,CAAA8H,kBAAA,CAAAJ,GAAA,CAAAxD,kBAAA,EAAA2D,MAAA,MAAAH,GAAA,CAAAxD,kBAAA,GAAA2D,MAAA;YAAA,OAAAA,MAAA;UAAA,EAAgC;UAC1D7H,EAAA,CAAAY,UAAA,4BAAAmH,qFAAA;YAAA,OAAkBL,GAAA,CAAAlC,0BAAA,EAA4B;UAAA,EAAC;UAC/CxF,EAAA,CAAA2B,UAAA,KAAAqG,4DAAA,uBAAoE;UAK1EhI,EAFI,CAAAG,YAAA,EAAY,EACR,EACF;UAGFH,EAFJ,CAAAC,cAAA,cAAsB,cACqC,gBACZ;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACrDH,EAAA,CAAAC,cAAA,oBACyC;UADXD,EAAA,CAAA2H,gBAAA,2BAAAM,oFAAAJ,MAAA;YAAA7H,EAAA,CAAA8H,kBAAA,CAAAJ,GAAA,CAAAQ,iBAAA,EAAAL,MAAA,MAAAH,GAAA,CAAAQ,iBAAA,GAAAL,MAAA;YAAA,OAAAA,MAAA;UAAA,EAA+B;UAC3D7H,EAAA,CAAAY,UAAA,4BAAAuH,qFAAA;YAAA,OAAkBT,GAAA,CAAAU,iBAAA,EAAmB;UAAA,EAAC;UACtCpI,EAAA,CAAA2B,UAAA,KAAA0G,4DAAA,uBAA0E;UAKhFrI,EAFI,CAAAG,YAAA,EAAY,EACR,EACF;UAEJH,EADF,CAAAC,cAAA,cAAsB,eAC2B;UAiB7CD,EAdA,CAAA2B,UAAA,KAAA2G,+DAAA,2BACyI,KAAAC,+DAAA,2BAcD;UAQ9IvI,EAFI,CAAAG,YAAA,EAAM,EACF,EACF;UAMEH,EAJR,CAAAC,cAAA,eAAmC,iBAC4C,aACpE,cACgD,cACrB;UAAAD,EAAA,CAAAE,MAAA,yDAAS;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAC5CH,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAE,MAAA,iCAAK;UAEvCF,EAFuC,CAAAG,YAAA,EAAK,EACrC,EACC;UACRH,EAAA,CAAAC,cAAA,aAAO;UACLD,EAAA,CAAA2B,UAAA,KAAA6G,qDAAA,iBAAmD;UAW3DxI,EAHM,CAAAG,YAAA,EAAQ,EACF,EACJ,EACO;UAEbH,EADF,CAAAC,cAAA,0BAAsD,0BAES;UAD7CD,EAAA,CAAA2H,gBAAA,wBAAAc,sFAAAZ,MAAA;YAAA7H,EAAA,CAAA8H,kBAAA,CAAAJ,GAAA,CAAAvC,SAAA,EAAA0C,MAAA,MAAAH,GAAA,CAAAvC,SAAA,GAAA0C,MAAA;YAAA,OAAAA,MAAA;UAAA,EAAoB;UAClC7H,EAAA,CAAAY,UAAA,wBAAA6H,sFAAAZ,MAAA;YAAA,OAAcH,GAAA,CAAAjC,WAAA,CAAAoC,MAAA,CAAmB;UAAA,EAAC;UAGxC7H,EAFI,CAAAG,YAAA,EAAiB,EACF,EACT;;;UA1E4BH,EAAA,CAAAM,SAAA,IAAgC;UAAhCN,EAAA,CAAA0I,gBAAA,YAAAhB,GAAA,CAAAxD,kBAAA,CAAgC;UAE9BlE,EAAA,CAAAM,SAAA,EAAuB;UAAvBN,EAAA,CAAAI,UAAA,YAAAsH,GAAA,CAAAhD,oBAAA,CAAuB;UASvB1E,EAAA,CAAAM,SAAA,GAA+B;UAA/BN,EAAA,CAAA0I,gBAAA,YAAAhB,GAAA,CAAAQ,iBAAA,CAA+B;UAE7BlI,EAAA,CAAAM,SAAA,EAAmB;UAAnBN,EAAA,CAAAI,UAAA,YAAAsH,GAAA,CAAAiB,gBAAA,CAAmB;UAWhD3I,EAAA,CAAAM,SAAA,GAAoI;UAApIN,EAAA,CAAAI,UAAA,SAAAsH,GAAA,CAAA3F,SAAA,IAAA2F,GAAA,CAAA3F,SAAA,CAAA6G,MAAA,QAAAlB,GAAA,CAAAtG,YAAA,KAAAsG,GAAA,CAAAtG,YAAA,CAAAC,OAAA,IAAAqG,GAAA,CAAAxD,kBAAA,IAAAwD,GAAA,CAAAxD,kBAAA,CAAAU,GAAA,KAAoI;UAcpI5E,EAAA,CAAAM,SAAA,EAAmI;UAAnIN,EAAA,CAAAI,UAAA,SAAAsH,GAAA,CAAA3F,SAAA,IAAA2F,GAAA,CAAA3F,SAAA,CAAA6G,MAAA,QAAAlB,GAAA,CAAAtG,YAAA,IAAAsG,GAAA,CAAAtG,YAAA,CAAAC,OAAA,IAAAqG,GAAA,CAAAxD,kBAAA,IAAAwD,GAAA,CAAAxD,kBAAA,CAAAU,GAAA,KAAmI;UAmBjH5E,EAAA,CAAAM,SAAA,IAAe;UAAfN,EAAA,CAAAI,UAAA,YAAAsH,GAAA,CAAA3F,SAAA,CAAe;UAa1B/B,EAAA,CAAAM,SAAA,GAAoB;UAApBN,EAAA,CAAA0I,gBAAA,SAAAhB,GAAA,CAAAvC,SAAA,CAAoB;UAAuBnF,EAAtB,CAAAI,UAAA,aAAAsH,GAAA,CAAAlE,QAAA,CAAqB,mBAAAkE,GAAA,CAAApC,YAAA,CAAgC;;;qBD5DlF3F,YAAY,EAAAkJ,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EAAElJ,YAAY,EAAAmJ,EAAA,CAAAC,eAAA,EAAAD,EAAA,CAAAE,OAAA,EAAAC,EAAA,CAAAC,eAAA,EAAAD,EAAA,CAAAE,mBAAA,EAAAF,EAAA,CAAAG,qBAAA,EAAAH,EAAA,CAAAI,qBAAA,EAAAJ,EAAA,CAAAK,iBAAA,EAAAL,EAAA,CAAAM,iBAAA,EAAAC,EAAA,CAAAC,aAAA,EAAAC,GAAA,CAAAC,mBAAA;MAAAC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}