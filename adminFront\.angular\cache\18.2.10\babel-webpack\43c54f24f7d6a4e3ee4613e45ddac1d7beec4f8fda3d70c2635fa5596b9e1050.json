{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport * as i0 from \"@angular/core\";\nconst _c0 = [\"input\"];\nexport class SearchInputComponent {\n  constructor() {\n    this.search = new EventEmitter();\n    this.isInputShown = false;\n  }\n  showInput() {\n    this.isInputShown = true;\n    this.input.nativeElement.focus();\n  }\n  hideInput() {\n    this.isInputShown = false;\n  }\n  onInput(val) {\n    this.search.emit(val);\n  }\n  static {\n    this.ɵfac = function SearchInputComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || SearchInputComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: SearchInputComponent,\n      selectors: [[\"ngx-search-input\"]],\n      viewQuery: function SearchInputComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 7);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.input = _t.first);\n        }\n      },\n      outputs: {\n        search: \"search\"\n      },\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 3,\n      vars: 2,\n      consts: [[\"input\", \"\"], [1, \"control-icon\", \"ion\", \"ion-ios-search\", 3, \"click\"], [\"placeholder\", \"Type your search request here...\", 3, \"blur\", \"input\"]],\n      template: function SearchInputComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"i\", 1);\n          i0.ɵɵlistener(\"click\", function SearchInputComponent_Template_i_click_0_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.showInput());\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(1, \"input\", 2, 0);\n          i0.ɵɵlistener(\"blur\", function SearchInputComponent_Template_input_blur_1_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.hideInput());\n          })(\"input\", function SearchInputComponent_Template_input_input_1_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onInput($event));\n          });\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance();\n          i0.ɵɵclassProp(\"hidden\", !ctx.isInputShown);\n        }\n      },\n      styles: [\"[_nghost-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n}\\n[_nghost-%COMP%]   i.control-icon[_ngcontent-%COMP%]::before {\\n  font-size: 2.3rem;\\n}\\n[_nghost-%COMP%]   i.control-icon[_ngcontent-%COMP%]:hover {\\n  cursor: pointer;\\n}\\n[_nghost-%COMP%]   input[_ngcontent-%COMP%] {\\n  border: none;\\n  outline: none;\\n  margin-left: 1rem;\\n  width: 15rem;\\n  transition: width 0.2s ease;\\n}\\n[_nghost-%COMP%]   input.hidden[_ngcontent-%COMP%] {\\n  width: 0;\\n  margin: 0;\\n}\\n[_nghost-%COMP%]     search-input input {\\n  background: transparent;\\n}\\n/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbInNlYXJjaC1pbnB1dC5jb21wb25lbnQuc2NzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFBQTtFQUNFLGFBQUE7RUFDQSxtQkFBQTtBQUNGO0FBRUk7RUFDRSxpQkFBQTtBQUFOO0FBR0k7RUFDRSxlQUFBO0FBRE47QUFLRTtFQUNFLFlBQUE7RUFDQSxhQUFBO0VBQ0EsaUJBQUE7RUFDQSxZQUFBO0VBQ0EsMkJBQUE7QUFISjtBQUtJO0VBQ0UsUUFBQTtFQUNBLFNBQUE7QUFITjtBQVFJO0VBQ0UsdUJBQUE7QUFOTiIsImZpbGUiOiJzZWFyY2gtaW5wdXQuY29tcG9uZW50LnNjc3MiLCJzb3VyY2VzQ29udGVudCI6WyI6aG9zdCB7XHJcbiAgZGlzcGxheTogZmxleDtcclxuICBhbGlnbi1pdGVtczogY2VudGVyO1xyXG5cclxuICBpLmNvbnRyb2wtaWNvbiB7XHJcbiAgICAmOjpiZWZvcmUge1xyXG4gICAgICBmb250LXNpemU6IDIuM3JlbTtcclxuICAgIH1cclxuXHJcbiAgICAmOmhvdmVyIHtcclxuICAgICAgY3Vyc29yOiBwb2ludGVyO1xyXG4gICAgfVxyXG4gIH1cclxuXHJcbiAgaW5wdXQge1xyXG4gICAgYm9yZGVyOiBub25lO1xyXG4gICAgb3V0bGluZTogbm9uZTtcclxuICAgIG1hcmdpbi1sZWZ0OiAxcmVtO1xyXG4gICAgd2lkdGg6IDE1cmVtO1xyXG4gICAgdHJhbnNpdGlvbjogd2lkdGggMC4ycyBlYXNlO1xyXG5cclxuICAgICYuaGlkZGVuIHtcclxuICAgICAgd2lkdGg6IDA7XHJcbiAgICAgIG1hcmdpbjogMDtcclxuICAgIH1cclxuICB9XHJcblxyXG4gIDo6bmctZGVlcCBzZWFyY2gtaW5wdXQge1xyXG4gICAgaW5wdXQge1xyXG4gICAgICBiYWNrZ3JvdW5kOiB0cmFuc3BhcmVudDtcclxuICAgIH1cclxuICB9XHJcbn1cclxuIl19 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvQHRoZW1lL2NvbXBvbmVudHMvc2VhcmNoLWlucHV0L3NlYXJjaC1pbnB1dC5jb21wb25lbnQuc2NzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFBQTtFQUNFLGFBQUE7RUFDQSxtQkFBQTtBQUNGO0FBRUk7RUFDRSxpQkFBQTtBQUFOO0FBR0k7RUFDRSxlQUFBO0FBRE47QUFLRTtFQUNFLFlBQUE7RUFDQSxhQUFBO0VBQ0EsaUJBQUE7RUFDQSxZQUFBO0VBQ0EsMkJBQUE7QUFISjtBQUtJO0VBQ0UsUUFBQTtFQUNBLFNBQUE7QUFITjtBQVFJO0VBQ0UsdUJBQUE7QUFOTjtBQUNBLGdxQ0FBZ3FDIiwic291cmNlc0NvbnRlbnQiOlsiOmhvc3Qge1xyXG4gIGRpc3BsYXk6IGZsZXg7XHJcbiAgYWxpZ24taXRlbXM6IGNlbnRlcjtcclxuXHJcbiAgaS5jb250cm9sLWljb24ge1xyXG4gICAgJjo6YmVmb3JlIHtcclxuICAgICAgZm9udC1zaXplOiAyLjNyZW07XHJcbiAgICB9XHJcblxyXG4gICAgJjpob3ZlciB7XHJcbiAgICAgIGN1cnNvcjogcG9pbnRlcjtcclxuICAgIH1cclxuICB9XHJcblxyXG4gIGlucHV0IHtcclxuICAgIGJvcmRlcjogbm9uZTtcclxuICAgIG91dGxpbmU6IG5vbmU7XHJcbiAgICBtYXJnaW4tbGVmdDogMXJlbTtcclxuICAgIHdpZHRoOiAxNXJlbTtcclxuICAgIHRyYW5zaXRpb246IHdpZHRoIDAuMnMgZWFzZTtcclxuXHJcbiAgICAmLmhpZGRlbiB7XHJcbiAgICAgIHdpZHRoOiAwO1xyXG4gICAgICBtYXJnaW46IDA7XHJcbiAgICB9XHJcbiAgfVxyXG5cclxuICA6Om5nLWRlZXAgc2VhcmNoLWlucHV0IHtcclxuICAgIGlucHV0IHtcclxuICAgICAgYmFja2dyb3VuZDogdHJhbnNwYXJlbnQ7XHJcbiAgICB9XHJcbiAgfVxyXG59XHJcbiJdLCJzb3VyY2VSb290IjoiIn0= */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["EventEmitter", "SearchInputComponent", "constructor", "search", "isInputShown", "showInput", "input", "nativeElement", "focus", "hideInput", "onInput", "val", "emit", "selectors", "viewQuery", "SearchInputComponent_Query", "rf", "ctx", "i0", "ɵɵelementStart", "ɵɵlistener", "SearchInputComponent_Template_i_click_0_listener", "ɵɵrestoreView", "_r1", "ɵɵresetView", "ɵɵelementEnd", "SearchInputComponent_Template_input_blur_1_listener", "SearchInputComponent_Template_input_input_1_listener", "$event", "ɵɵadvance", "ɵɵclassProp"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\@theme\\components\\search-input\\search-input.component.ts"], "sourcesContent": ["import { Component, ElementRef, EventEmitter, Output, ViewChild } from '@angular/core';\r\n\r\n@Component({\r\n    selector: 'ngx-search-input',\r\n    styleUrls: ['./search-input.component.scss'],\r\n    template: `\r\n    <i class=\"control-icon ion ion-ios-search\"\r\n       (click)=\"showInput()\"></i>\r\n    <input placeholder=\"Type your search request here...\"\r\n           #input\r\n           [class.hidden]=\"!isInputShown\"\r\n           (blur)=\"hideInput()\"\r\n           (input)=\"onInput($event)\">\r\n  `,\r\n    standalone: true,\r\n})\r\nexport class SearchInputComponent {\r\n  @ViewChild('input', { static: true }) input?: ElementRef;\r\n\r\n  @Output() search: EventEmitter<string> = new EventEmitter<string>();\r\n\r\n  isInputShown = false;\r\n\r\n  showInput() {\r\n    this.isInputShown = true;\r\n    this.input!.nativeElement.focus();\r\n  }\r\n\r\n  hideInput() {\r\n    this.isInputShown = false;\r\n  }\r\n\r\n  onInput(val: any) {\r\n    this.search.emit(val);\r\n  }\r\n}\r\n"], "mappings": "AAAA,SAAgCA,YAAY,QAA2B,eAAe;;;AAgBtF,OAAM,MAAOC,oBAAoB;EAdjCC,YAAA;IAiBY,KAAAC,MAAM,GAAyB,IAAIH,YAAY,EAAU;IAEnE,KAAAI,YAAY,GAAG,KAAK;;EAEpBC,SAASA,CAAA;IACP,IAAI,CAACD,YAAY,GAAG,IAAI;IACxB,IAAI,CAACE,KAAM,CAACC,aAAa,CAACC,KAAK,EAAE;EACnC;EAEAC,SAASA,CAAA;IACP,IAAI,CAACL,YAAY,GAAG,KAAK;EAC3B;EAEAM,OAAOA,CAACC,GAAQ;IACd,IAAI,CAACR,MAAM,CAACS,IAAI,CAACD,GAAG,CAAC;EACvB;;;uCAlBWV,oBAAoB;IAAA;EAAA;;;YAApBA,oBAAoB;MAAAY,SAAA;MAAAC,SAAA,WAAAC,2BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;;;;;;;;;;;;;;;;;;UAV7BE,EAAA,CAAAC,cAAA,WACyB;UAAtBD,EAAA,CAAAE,UAAA,mBAAAC,iDAAA;YAAAH,EAAA,CAAAI,aAAA,CAAAC,GAAA;YAAA,OAAAL,EAAA,CAAAM,WAAA,CAASP,GAAA,CAAAZ,SAAA,EAAW;UAAA,EAAC;UAACa,EAAA,CAAAO,YAAA,EAAI;UAC7BP,EAAA,CAAAC,cAAA,kBAIiC;UAA1BD,EADA,CAAAE,UAAA,kBAAAM,oDAAA;YAAAR,EAAA,CAAAI,aAAA,CAAAC,GAAA;YAAA,OAAAL,EAAA,CAAAM,WAAA,CAAQP,GAAA,CAAAR,SAAA,EAAW;UAAA,EAAC,mBAAAkB,qDAAAC,MAAA;YAAAV,EAAA,CAAAI,aAAA,CAAAC,GAAA;YAAA,OAAAL,EAAA,CAAAM,WAAA,CACXP,GAAA,CAAAP,OAAA,CAAAkB,MAAA,CAAe;UAAA,EAAC;UAJhCV,EAAA,CAAAO,YAAA,EAIiC;;;UAF1BP,EAAA,CAAAW,SAAA,EAA8B;UAA9BX,EAAA,CAAAY,WAAA,YAAAb,GAAA,CAAAb,YAAA,CAA8B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}