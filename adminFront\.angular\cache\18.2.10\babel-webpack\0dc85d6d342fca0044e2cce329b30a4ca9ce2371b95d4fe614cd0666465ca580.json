{"ast": null, "code": "import { LocalStorageService } from 'src/app/shared/services/local-storage.service';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nexport class LogoutComponent {\n  constructor(router) {\n    this.router = router;\n  }\n  ngOnInit() {\n    LocalStorageService.ClearLocalStorage();\n    this.router.navigateByUrl('login');\n  }\n  static {\n    this.ɵfac = function LogoutComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || LogoutComponent)(i0.ɵɵdirectiveInject(i1.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: LogoutComponent,\n      selectors: [[\"ngx-logout\"]],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 0,\n      vars: 0,\n      template: function LogoutComponent_Template(rf, ctx) {}\n    });\n  }\n}", "map": {"version": 3, "names": ["LocalStorageService", "LogoutComponent", "constructor", "router", "ngOnInit", "ClearLocalStorage", "navigateByUrl", "i0", "ɵɵdirectiveInject", "i1", "Router", "selectors", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "template", "LogoutComponent_Template", "rf", "ctx"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\logout\\logout.component.ts"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { Router } from '@angular/router';\r\nimport { LocalStorageService } from 'src/app/shared/services/local-storage.service';\r\n\r\n@Component({\r\n    selector: 'ngx-logout',\r\n    templateUrl: './logout.component.html',\r\n    styleUrls: ['./logout.component.scss'],\r\n    standalone: true\r\n})\r\nexport class LogoutComponent implements OnInit {\r\n\r\n  constructor(\r\n    private router: Router\r\n  ) { }\r\n\r\n  ngOnInit(): void {\r\n    LocalStorageService.ClearLocalStorage()\r\n    this.router.navigateByUrl('login');\r\n  }\r\n\r\n}\r\n"], "mappings": "AAEA,SAASA,mBAAmB,QAAQ,+CAA+C;;;AAQnF,OAAM,MAAOC,eAAe;EAE1BC,YACUC,MAAc;IAAd,KAAAA,MAAM,GAANA,MAAM;EACZ;EAEJC,QAAQA,CAAA;IACNJ,mBAAmB,CAACK,iBAAiB,EAAE;IACvC,IAAI,CAACF,MAAM,CAACG,aAAa,CAAC,OAAO,CAAC;EACpC;;;uCATWL,eAAe,EAAAM,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAC,MAAA;IAAA;EAAA;;;YAAfT,eAAe;MAAAU,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAAN,EAAA,CAAAO,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,QAAA,WAAAC,yBAAAC,EAAA,EAAAC,GAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}