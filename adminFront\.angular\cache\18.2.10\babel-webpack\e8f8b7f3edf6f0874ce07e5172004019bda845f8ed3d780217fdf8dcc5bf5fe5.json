{"ast": null, "code": "import toDate from \"../../toDate/index.js\";\nimport requiredArgs from \"../requiredArgs/index.js\";\nimport startOfUTCISOWeek from \"../startOfUTCISOWeek/index.js\";\nexport default function getUTCISOWeekYear(dirtyDate) {\n  requiredArgs(1, arguments);\n  var date = toDate(dirtyDate);\n  var year = date.getUTCFullYear();\n  var fourthOfJanuaryOfNextYear = new Date(0);\n  fourthOfJanuaryOfNextYear.setUTCFullYear(year + 1, 0, 4);\n  fourthOfJanuaryOfNextYear.setUTCHours(0, 0, 0, 0);\n  var startOfNextYear = startOfUTCISOWeek(fourthOfJanuaryOfNextYear);\n  var fourthOfJanuaryOfThisYear = new Date(0);\n  fourthOfJanuaryOfThisYear.setUTCFullYear(year, 0, 4);\n  fourthOfJanuaryOfThisYear.setUTCHours(0, 0, 0, 0);\n  var startOfThisYear = startOfUTCISOWeek(fourthOfJanuaryOfThisYear);\n  if (date.getTime() >= startOfNextYear.getTime()) {\n    return year + 1;\n  } else if (date.getTime() >= startOfThisYear.getTime()) {\n    return year;\n  } else {\n    return year - 1;\n  }\n}", "map": {"version": 3, "names": ["toDate", "requiredArgs", "startOfUTCISOWeek", "getUTCISOWeekYear", "dirtyDate", "arguments", "date", "year", "getUTCFullYear", "fourthOfJanuaryOfNextYear", "Date", "setUTCFullYear", "setUTCHours", "startOfNextYear", "fourthOfJanuaryOfThisYear", "startOfThisYear", "getTime"], "sources": ["C:/Users/<USER>/Documents/salechange-product/adminFront/node_modules/date-fns/esm/_lib/getUTCISOWeekYear/index.js"], "sourcesContent": ["import toDate from \"../../toDate/index.js\";\nimport requiredArgs from \"../requiredArgs/index.js\";\nimport startOfUTCISOWeek from \"../startOfUTCISOWeek/index.js\";\nexport default function getUTCISOWeekYear(dirtyDate) {\n  requiredArgs(1, arguments);\n  var date = toDate(dirtyDate);\n  var year = date.getUTCFullYear();\n  var fourthOfJanuaryOfNextYear = new Date(0);\n  fourthOfJanuaryOfNextYear.setUTCFullYear(year + 1, 0, 4);\n  fourthOfJanuaryOfNextYear.setUTCHours(0, 0, 0, 0);\n  var startOfNextYear = startOfUTCISOWeek(fourthOfJanuaryOfNextYear);\n  var fourthOfJanuaryOfThisYear = new Date(0);\n  fourthOfJanuaryOfThisYear.setUTCFullYear(year, 0, 4);\n  fourthOfJanuaryOfThisYear.setUTCHours(0, 0, 0, 0);\n  var startOfThisYear = startOfUTCISOWeek(fourthOfJanuaryOfThisYear);\n  if (date.getTime() >= startOfNextYear.getTime()) {\n    return year + 1;\n  } else if (date.getTime() >= startOfThisYear.getTime()) {\n    return year;\n  } else {\n    return year - 1;\n  }\n}"], "mappings": "AAAA,OAAOA,MAAM,MAAM,uBAAuB;AAC1C,OAAOC,YAAY,MAAM,0BAA0B;AACnD,OAAOC,iBAAiB,MAAM,+BAA+B;AAC7D,eAAe,SAASC,iBAAiBA,CAACC,SAAS,EAAE;EACnDH,YAAY,CAAC,CAAC,EAAEI,SAAS,CAAC;EAC1B,IAAIC,IAAI,GAAGN,MAAM,CAACI,SAAS,CAAC;EAC5B,IAAIG,IAAI,GAAGD,IAAI,CAACE,cAAc,CAAC,CAAC;EAChC,IAAIC,yBAAyB,GAAG,IAAIC,IAAI,CAAC,CAAC,CAAC;EAC3CD,yBAAyB,CAACE,cAAc,CAACJ,IAAI,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;EACxDE,yBAAyB,CAACG,WAAW,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;EACjD,IAAIC,eAAe,GAAGX,iBAAiB,CAACO,yBAAyB,CAAC;EAClE,IAAIK,yBAAyB,GAAG,IAAIJ,IAAI,CAAC,CAAC,CAAC;EAC3CI,yBAAyB,CAACH,cAAc,CAACJ,IAAI,EAAE,CAAC,EAAE,CAAC,CAAC;EACpDO,yBAAyB,CAACF,WAAW,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;EACjD,IAAIG,eAAe,GAAGb,iBAAiB,CAACY,yBAAyB,CAAC;EAClE,IAAIR,IAAI,CAACU,OAAO,CAAC,CAAC,IAAIH,eAAe,CAACG,OAAO,CAAC,CAAC,EAAE;IAC/C,OAAOT,IAAI,GAAG,CAAC;EACjB,CAAC,MAAM,IAAID,IAAI,CAACU,OAAO,CAAC,CAAC,IAAID,eAAe,CAACC,OAAO,CAAC,CAAC,EAAE;IACtD,OAAOT,IAAI;EACb,CAAC,MAAM;IACL,OAAOA,IAAI,GAAG,CAAC;EACjB;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}