{"ast": null, "code": "import _classCallCheck from \"@babel/runtime/helpers/esm/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/esm/createClass\";\nimport _assertThisInitialized from \"@babel/runtime/helpers/esm/assertThisInitialized\";\nimport _inherits from \"@babel/runtime/helpers/esm/inherits\";\nimport _createSuper from \"@babel/runtime/helpers/esm/createSuper\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport { Parser } from \"../Parser.js\";\nimport { parseNDigits, normalizeTwoDigitYear, mapValue } from \"../utils.js\";\nimport getUTCWeekYear from \"../../../_lib/getUTCWeekYear/index.js\";\nimport startOfUTCWeek from \"../../../_lib/startOfUTCWeek/index.js\";\n// Local week-numbering year\nexport var LocalWeekYearParser = /*#__PURE__*/function (_Parser) {\n  _inherits(LocalWeekYearParser, _Parser);\n  var _super = _createSuper(LocalWeekYearParser);\n  function LocalWeekYearParser() {\n    var _this;\n    _classCallCheck(this, LocalWeekYearParser);\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    _this = _super.call.apply(_super, [this].concat(args));\n    _defineProperty(_assertThisInitialized(_this), \"priority\", 130);\n    _defineProperty(_assertThisInitialized(_this), \"incompatibleTokens\", ['y', 'R', 'u', 'Q', 'q', 'M', 'L', 'I', 'd', 'D', 'i', 't', 'T']);\n    return _this;\n  }\n  _createClass(LocalWeekYearParser, [{\n    key: \"parse\",\n    value: function parse(dateString, token, match) {\n      var valueCallback = function valueCallback(year) {\n        return {\n          year: year,\n          isTwoDigitYear: token === 'YY'\n        };\n      };\n      switch (token) {\n        case 'Y':\n          return mapValue(parseNDigits(4, dateString), valueCallback);\n        case 'Yo':\n          return mapValue(match.ordinalNumber(dateString, {\n            unit: 'year'\n          }), valueCallback);\n        default:\n          return mapValue(parseNDigits(token.length, dateString), valueCallback);\n      }\n    }\n  }, {\n    key: \"validate\",\n    value: function validate(_date, value) {\n      return value.isTwoDigitYear || value.year > 0;\n    }\n  }, {\n    key: \"set\",\n    value: function set(date, flags, value, options) {\n      var currentYear = getUTCWeekYear(date, options);\n      if (value.isTwoDigitYear) {\n        var normalizedTwoDigitYear = normalizeTwoDigitYear(value.year, currentYear);\n        date.setUTCFullYear(normalizedTwoDigitYear, 0, options.firstWeekContainsDate);\n        date.setUTCHours(0, 0, 0, 0);\n        return startOfUTCWeek(date, options);\n      }\n      var year = !('era' in flags) || flags.era === 1 ? value.year : 1 - value.year;\n      date.setUTCFullYear(year, 0, options.firstWeekContainsDate);\n      date.setUTCHours(0, 0, 0, 0);\n      return startOfUTCWeek(date, options);\n    }\n  }]);\n  return LocalWeekYearParser;\n}(Parser);", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}