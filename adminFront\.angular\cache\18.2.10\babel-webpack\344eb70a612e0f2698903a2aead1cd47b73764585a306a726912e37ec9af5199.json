{"ast": null, "code": "import _classCallCheck from \"@babel/runtime/helpers/esm/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/esm/createClass\";\nimport _assertThisInitialized from \"@babel/runtime/helpers/esm/assertThisInitialized\";\nimport _inherits from \"@babel/runtime/helpers/esm/inherits\";\nimport _createSuper from \"@babel/runtime/helpers/esm/createSuper\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport { Parser } from \"../Parser.js\";\nimport { dayPeriodEnumToHours } from \"../utils.js\"; // in the morning, in the afternoon, in the evening, at night\nexport var DayPeriodParser = /*#__PURE__*/function (_Parser) {\n  _inherits(DayPeriodParser, _Parser);\n  var _super = _createSuper(DayPeriodParser);\n  function DayPeriodParser() {\n    var _this;\n    _classCallCheck(this, DayPeriodParser);\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    _this = _super.call.apply(_super, [this].concat(args));\n    _defineProperty(_assertThisInitialized(_this), \"priority\", 80);\n    _defineProperty(_assertThisInitialized(_this), \"incompatibleTokens\", ['a', 'b', 't', 'T']);\n    return _this;\n  }\n  _createClass(DayPeriodParser, [{\n    key: \"parse\",\n    value: function parse(dateString, token, match) {\n      switch (token) {\n        case 'B':\n        case 'BB':\n        case 'BBB':\n          return match.dayPeriod(dateString, {\n            width: 'abbreviated',\n            context: 'formatting'\n          }) || match.dayPeriod(dateString, {\n            width: 'narrow',\n            context: 'formatting'\n          });\n        case 'BBBBB':\n          return match.dayPeriod(dateString, {\n            width: 'narrow',\n            context: 'formatting'\n          });\n        case 'BBBB':\n        default:\n          return match.dayPeriod(dateString, {\n            width: 'wide',\n            context: 'formatting'\n          }) || match.dayPeriod(dateString, {\n            width: 'abbreviated',\n            context: 'formatting'\n          }) || match.dayPeriod(dateString, {\n            width: 'narrow',\n            context: 'formatting'\n          });\n      }\n    }\n  }, {\n    key: \"set\",\n    value: function set(date, _flags, value) {\n      date.setUTCHours(dayPeriodEnumToHours(value), 0, 0, 0);\n      return date;\n    }\n  }]);\n  return DayPeriodParser;\n}(Parser);", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}