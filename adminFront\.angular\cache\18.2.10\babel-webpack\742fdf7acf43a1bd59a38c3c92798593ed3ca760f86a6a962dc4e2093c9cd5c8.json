{"ast": null, "code": "import { createPlugin } from '@fullcalendar/core/index.js';\nimport { DayGridView as DayTableView, TableDateProfileGenerator } from './internal.js';\nimport '@fullcalendar/core/internal.js';\nimport '@fullcalendar/core/preact.js';\nvar index = createPlugin({\n  name: '@fullcalendar/daygrid',\n  initialView: 'dayGridMonth',\n  views: {\n    dayGrid: {\n      component: DayTableView,\n      dateProfileGeneratorClass: TableDateProfileGenerator\n    },\n    dayGridDay: {\n      type: 'dayGrid',\n      duration: {\n        days: 1\n      }\n    },\n    dayGridWeek: {\n      type: 'dayGrid',\n      duration: {\n        weeks: 1\n      }\n    },\n    dayGridMonth: {\n      type: 'dayGrid',\n      duration: {\n        months: 1\n      },\n      fixedWeekCount: true\n    },\n    dayGridYear: {\n      type: 'dayGrid',\n      duration: {\n        years: 1\n      }\n    }\n  }\n});\nexport { index as default };", "map": {"version": 3, "names": ["createPlugin", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "DayTableView", "TableDateProfileGenerator", "index", "name", "initialView", "views", "<PERSON><PERSON><PERSON>", "component", "dateProfileGeneratorClass", "dayGridDay", "type", "duration", "days", "dayGridWeek", "weeks", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "months", "fixedWeekCount", "dayGridYear", "years", "default"], "sources": ["C:/Users/<USER>/Documents/jeansalechange/adminFront/node_modules/@fullcalendar/daygrid/index.js"], "sourcesContent": ["import { createPlugin } from '@fullcalendar/core/index.js';\nimport { DayGridView as DayTableView, TableDateProfileGenerator } from './internal.js';\nimport '@fullcalendar/core/internal.js';\nimport '@fullcalendar/core/preact.js';\n\nvar index = createPlugin({\n    name: '@fullcalendar/daygrid',\n    initialView: 'dayGridMonth',\n    views: {\n        dayGrid: {\n            component: DayTableView,\n            dateProfileGeneratorClass: TableDateProfileGenerator,\n        },\n        dayGridDay: {\n            type: 'dayGrid',\n            duration: { days: 1 },\n        },\n        dayGridWeek: {\n            type: 'dayGrid',\n            duration: { weeks: 1 },\n        },\n        dayGridMonth: {\n            type: 'dayGrid',\n            duration: { months: 1 },\n            fixedWeekCount: true,\n        },\n        dayGridYear: {\n            type: 'dayGrid',\n            duration: { years: 1 },\n        },\n    },\n});\n\nexport { index as default };\n"], "mappings": "AAAA,SAASA,YAAY,QAAQ,6BAA6B;AAC1D,SAASC,WAAW,IAAIC,YAAY,EAAEC,yBAAyB,QAAQ,eAAe;AACtF,OAAO,gCAAgC;AACvC,OAAO,8BAA8B;AAErC,IAAIC,KAAK,GAAGJ,YAAY,CAAC;EACrBK,IAAI,EAAE,uBAAuB;EAC7BC,WAAW,EAAE,cAAc;EAC3BC,KAAK,EAAE;IACHC,OAAO,EAAE;MACLC,SAAS,EAAEP,YAAY;MACvBQ,yBAAyB,EAAEP;IAC/B,CAAC;IACDQ,UAAU,EAAE;MACRC,IAAI,EAAE,SAAS;MACfC,QAAQ,EAAE;QAAEC,IAAI,EAAE;MAAE;IACxB,CAAC;IACDC,WAAW,EAAE;MACTH,IAAI,EAAE,SAAS;MACfC,QAAQ,EAAE;QAAEG,KAAK,EAAE;MAAE;IACzB,CAAC;IACDC,YAAY,EAAE;MACVL,IAAI,EAAE,SAAS;MACfC,QAAQ,EAAE;QAAEK,MAAM,EAAE;MAAE,CAAC;MACvBC,cAAc,EAAE;IACpB,CAAC;IACDC,WAAW,EAAE;MACTR,IAAI,EAAE,SAAS;MACfC,QAAQ,EAAE;QAAEQ,KAAK,EAAE;MAAE;IACzB;EACJ;AACJ,CAAC,CAAC;AAEF,SAASjB,KAAK,IAAIkB,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}