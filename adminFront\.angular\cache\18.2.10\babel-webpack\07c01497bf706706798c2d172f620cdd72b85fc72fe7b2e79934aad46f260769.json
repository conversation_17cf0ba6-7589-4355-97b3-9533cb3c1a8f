{"ast": null, "code": "/* tslint:disable */\n/* eslint-disable */\nimport { HttpResponse } from '@angular/common/http';\nimport { filter, map } from 'rxjs/operators';\nimport { RequestBuilder } from '../../request-builder';\nexport function apiSpecialNoticeFileGetSpecialNoticeFileListPost$Plain(http, rootUrl, params, context) {\n  const rb = new RequestBuilder(rootUrl, apiSpecialNoticeFileGetSpecialNoticeFileListPost$Plain.PATH, 'post');\n  if (params) {\n    rb.body(params.body, 'application/*+json');\n  }\n  return http.request(rb.build({\n    responseType: 'text',\n    accept: 'text/plain',\n    context\n  })).pipe(filter(r => r instanceof HttpResponse), map(r => {\n    return r;\n  }));\n}\napiSpecialNoticeFileGetSpecialNoticeFileListPost$Plain.PATH = '/api/SpecialNoticeFile/GetSpecialNoticeFileList';", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}