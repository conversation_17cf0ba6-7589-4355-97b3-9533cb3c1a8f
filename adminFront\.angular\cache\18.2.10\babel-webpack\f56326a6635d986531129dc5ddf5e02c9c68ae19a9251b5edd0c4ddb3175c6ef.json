{"ast": null, "code": "class ObjectUtils {\n  static isArray(value, empty = true) {\n    return Array.isArray(value) && (empty || value.length !== 0);\n  }\n  static isObject(value, empty = true) {\n    return typeof value === 'object' && !Array.isArray(value) && value != null && (empty || Object.keys(value).length !== 0);\n  }\n  static equals(obj1, obj2, field) {\n    if (field) return this.resolveFieldData(obj1, field) === this.resolveFieldData(obj2, field);else return this.equalsByValue(obj1, obj2);\n  }\n  static equalsByValue(obj1, obj2) {\n    if (obj1 === obj2) return true;\n    if (obj1 && obj2 && typeof obj1 == 'object' && typeof obj2 == 'object') {\n      var arrA = Array.isArray(obj1),\n        arrB = Array.isArray(obj2),\n        i,\n        length,\n        key;\n      if (arrA && arrB) {\n        length = obj1.length;\n        if (length != obj2.length) return false;\n        for (i = length; i-- !== 0;) if (!this.equalsByValue(obj1[i], obj2[i])) return false;\n        return true;\n      }\n      if (arrA != arrB) return false;\n      var dateA = this.isDate(obj1),\n        dateB = this.isDate(obj2);\n      if (dateA != dateB) return false;\n      if (dateA && dateB) return obj1.getTime() == obj2.getTime();\n      var regexpA = obj1 instanceof RegExp,\n        regexpB = obj2 instanceof RegExp;\n      if (regexpA != regexpB) return false;\n      if (regexpA && regexpB) return obj1.toString() == obj2.toString();\n      var keys = Object.keys(obj1);\n      length = keys.length;\n      if (length !== Object.keys(obj2).length) return false;\n      for (i = length; i-- !== 0;) if (!Object.prototype.hasOwnProperty.call(obj2, keys[i])) return false;\n      for (i = length; i-- !== 0;) {\n        key = keys[i];\n        if (!this.equalsByValue(obj1[key], obj2[key])) return false;\n      }\n      return true;\n    }\n    return obj1 !== obj1 && obj2 !== obj2;\n  }\n  static resolveFieldData(data, field) {\n    if (data && field) {\n      if (this.isFunction(field)) {\n        return field(data);\n      } else if (field.indexOf('.') == -1) {\n        return data[field];\n      } else {\n        let fields = field.split('.');\n        let value = data;\n        for (let i = 0, len = fields.length; i < len; ++i) {\n          if (value == null) {\n            return null;\n          }\n          value = value[fields[i]];\n        }\n        return value;\n      }\n    } else {\n      return null;\n    }\n  }\n  static isFunction(obj) {\n    return !!(obj && obj.constructor && obj.call && obj.apply);\n  }\n  static reorderArray(value, from, to) {\n    let target;\n    if (value && from !== to) {\n      if (to >= value.length) {\n        to %= value.length;\n        from %= value.length;\n      }\n      value.splice(to, 0, value.splice(from, 1)[0]);\n    }\n  }\n  static insertIntoOrderedArray(item, index, arr, sourceArr) {\n    if (arr.length > 0) {\n      let injected = false;\n      for (let i = 0; i < arr.length; i++) {\n        let currentItemIndex = this.findIndexInList(arr[i], sourceArr);\n        if (currentItemIndex > index) {\n          arr.splice(i, 0, item);\n          injected = true;\n          break;\n        }\n      }\n      if (!injected) {\n        arr.push(item);\n      }\n    } else {\n      arr.push(item);\n    }\n  }\n  static findIndexInList(item, list) {\n    let index = -1;\n    if (list) {\n      for (let i = 0; i < list.length; i++) {\n        if (list[i] == item) {\n          index = i;\n          break;\n        }\n      }\n    }\n    return index;\n  }\n  static contains(value, list) {\n    if (value != null && list && list.length) {\n      for (let val of list) {\n        if (this.equals(value, val)) return true;\n      }\n    }\n    return false;\n  }\n  static removeAccents(str) {\n    if (str) {\n      str = str.normalize('NFKD').replace(/\\p{Diacritic}/gu, '');\n    }\n    return str;\n  }\n  static isDate(input) {\n    return Object.prototype.toString.call(input) === '[object Date]';\n  }\n  static isEmpty(value) {\n    return value === null || value === undefined || value === '' || Array.isArray(value) && value.length === 0 || !this.isDate(value) && typeof value === 'object' && Object.keys(value).length === 0;\n  }\n  static isNotEmpty(value) {\n    return !this.isEmpty(value);\n  }\n  static compare(value1, value2, locale, order = 1) {\n    let result = -1;\n    const emptyValue1 = this.isEmpty(value1);\n    const emptyValue2 = this.isEmpty(value2);\n    if (emptyValue1 && emptyValue2) result = 0;else if (emptyValue1) result = order;else if (emptyValue2) result = -order;else if (typeof value1 === 'string' && typeof value2 === 'string') result = value1.localeCompare(value2, locale, {\n      numeric: true\n    });else result = value1 < value2 ? -1 : value1 > value2 ? 1 : 0;\n    return result;\n  }\n  static sort(value1, value2, order = 1, locale, nullSortOrder = 1) {\n    const result = ObjectUtils.compare(value1, value2, locale, order);\n    let finalSortOrder = order;\n    // nullSortOrder == 1 means Excel like sort nulls at bottom\n    if (ObjectUtils.isEmpty(value1) || ObjectUtils.isEmpty(value2)) {\n      finalSortOrder = nullSortOrder === 1 ? order : nullSortOrder;\n    }\n    return finalSortOrder * result;\n  }\n  static merge(obj1, obj2) {\n    if (obj1 == undefined && obj2 == undefined) {\n      return undefined;\n    } else if ((obj1 == undefined || typeof obj1 === 'object') && (obj2 == undefined || typeof obj2 === 'object')) {\n      return {\n        ...(obj1 || {}),\n        ...(obj2 || {})\n      };\n    } else if ((obj1 == undefined || typeof obj1 === 'string') && (obj2 == undefined || typeof obj2 === 'string')) {\n      return [obj1 || '', obj2 || ''].join(' ');\n    }\n    return obj2 || obj1;\n  }\n  static isPrintableCharacter(char = '') {\n    return this.isNotEmpty(char) && char.length === 1 && char.match(/\\S| /);\n  }\n  static getItemValue(obj, ...params) {\n    return this.isFunction(obj) ? obj(...params) : obj;\n  }\n  static findLastIndex(arr, callback) {\n    let index = -1;\n    if (this.isNotEmpty(arr)) {\n      try {\n        index = arr.findLastIndex(callback);\n      } catch {\n        index = arr.lastIndexOf([...arr].reverse().find(callback));\n      }\n    }\n    return index;\n  }\n  static findLast(arr, callback) {\n    let item;\n    if (this.isNotEmpty(arr)) {\n      try {\n        item = arr.findLast(callback);\n      } catch {\n        item = [...arr].reverse().find(callback);\n      }\n    }\n    return item;\n  }\n  static deepEquals(a, b) {\n    if (a === b) return true;\n    if (a && b && typeof a == 'object' && typeof b == 'object') {\n      var arrA = Array.isArray(a),\n        arrB = Array.isArray(b),\n        i,\n        length,\n        key;\n      if (arrA && arrB) {\n        length = a.length;\n        if (length != b.length) return false;\n        for (i = length; i-- !== 0;) if (!this.deepEquals(a[i], b[i])) return false;\n        return true;\n      }\n      if (arrA != arrB) return false;\n      var dateA = a instanceof Date,\n        dateB = b instanceof Date;\n      if (dateA != dateB) return false;\n      if (dateA && dateB) return a.getTime() == b.getTime();\n      var regexpA = a instanceof RegExp,\n        regexpB = b instanceof RegExp;\n      if (regexpA != regexpB) return false;\n      if (regexpA && regexpB) return a.toString() == b.toString();\n      var keys = Object.keys(a);\n      length = keys.length;\n      if (length !== Object.keys(b).length) return false;\n      for (i = length; i-- !== 0;) if (!Object.prototype.hasOwnProperty.call(b, keys[i])) return false;\n      for (i = length; i-- !== 0;) {\n        key = keys[i];\n        if (!this.deepEquals(a[key], b[key])) return false;\n      }\n      return true;\n    }\n    return a !== a && b !== b;\n  }\n}\nvar lastId = 0;\nfunction UniqueComponentId(prefix = 'pn_id_') {\n  lastId++;\n  return `${prefix}${lastId}`;\n}\nfunction ZIndexUtils() {\n  let zIndexes = [];\n  const generateZIndex = (key, baseZIndex) => {\n    let lastZIndex = zIndexes.length > 0 ? zIndexes[zIndexes.length - 1] : {\n      key,\n      value: baseZIndex\n    };\n    let newZIndex = lastZIndex.value + (lastZIndex.key === key ? 0 : baseZIndex) + 2;\n    zIndexes.push({\n      key,\n      value: newZIndex\n    });\n    return newZIndex;\n  };\n  const revertZIndex = zIndex => {\n    zIndexes = zIndexes.filter(obj => obj.value !== zIndex);\n  };\n  const getCurrentZIndex = () => {\n    return zIndexes.length > 0 ? zIndexes[zIndexes.length - 1].value : 0;\n  };\n  const getZIndex = el => {\n    return el ? parseInt(el.style.zIndex, 10) || 0 : 0;\n  };\n  return {\n    get: getZIndex,\n    set: (key, el, baseZIndex) => {\n      if (el) {\n        el.style.zIndex = String(generateZIndex(key, baseZIndex));\n      }\n    },\n    clear: el => {\n      if (el) {\n        revertZIndex(getZIndex(el));\n        el.style.zIndex = '';\n      }\n    },\n    getCurrent: () => getCurrentZIndex()\n  };\n}\nvar zindexutils = ZIndexUtils();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { ObjectUtils, UniqueComponentId, zindexutils as ZIndexUtils };\n//# sourceMappingURL=primeng-utils.mjs.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}