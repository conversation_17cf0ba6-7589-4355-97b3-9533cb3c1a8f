{"ast": null, "code": "import { EventEmitter, forwardRef } from '@angular/core';\nimport { NG_VALUE_ACCESSOR } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../../../services/api/services/HouseCustom.service\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"@angular/forms\";\nimport * as i4 from \"@nebular/theme\";\nfunction HouseholdBindingComponent_div_1_div_9_ng_container_1_span_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"span\", 21);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementStart(2, \"button\", 22);\n    i0.ɵɵlistener(\"click\", function HouseholdBindingComponent_div_1_div_9_ng_container_1_span_4_Template_button_click_2_listener() {\n      const householdCode_r4 = i0.ɵɵrestoreView(_r3).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r1.onRemoveHousehold(householdCode_r4));\n    });\n    i0.ɵɵelement(3, \"nb-icon\", 23);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const householdCode_r4 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", householdCode_r4, \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r1.disabled);\n  }\n}\nfunction HouseholdBindingComponent_div_1_div_9_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 18);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 19);\n    i0.ɵɵtemplate(4, HouseholdBindingComponent_div_1_div_9_ng_container_1_span_4_Template, 4, 2, \"span\", 20);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const building_r5 = i0.ɵɵnextContext().$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", building_r5, \":\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.getBuildingSelectedHouseholds(building_r5));\n  }\n}\nfunction HouseholdBindingComponent_div_1_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 17);\n    i0.ɵɵtemplate(1, HouseholdBindingComponent_div_1_div_9_ng_container_1_Template, 5, 2, \"ng-container\", 5);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const building_r5 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.hasBuildingSelected(building_r5));\n  }\n}\nfunction HouseholdBindingComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 9)(1, \"div\", 10)(2, \"div\", 11);\n    i0.ɵɵelement(3, \"nb-icon\", 12);\n    i0.ɵɵelementStart(4, \"span\", 13);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"button\", 14);\n    i0.ɵɵlistener(\"click\", function HouseholdBindingComponent_div_1_Template_button_click_6_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onClearAll());\n    });\n    i0.ɵɵtext(7, \" \\u6E05\\u7A7A\\u5168\\u90E8 \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 15);\n    i0.ɵɵtemplate(9, HouseholdBindingComponent_div_1_div_9_Template, 2, 1, \"div\", 16);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\"\\u5DF2\\u9078\\u64C7\\u6236\\u5225 (\", ctx_r1.getSelectedCount(), \")\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r1.disabled);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.buildings);\n  }\n}\nfunction HouseholdBindingComponent_ng_container_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelement(1, \"nb-icon\", 24);\n    i0.ɵɵtext(2, \" \\u8F09\\u5165\\u4E2D... \");\n    i0.ɵɵelementContainerEnd();\n  }\n}\nfunction HouseholdBindingComponent_ng_container_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.getSelectedCount() > 0 ? \"\\u5DF2\\u9078\\u64C7 \" + ctx_r1.getSelectedCount() + \" \\u500B\\u6236\\u5225\" : ctx_r1.placeholder, \" \");\n  }\n}\nfunction HouseholdBindingComponent_div_8_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 48)(1, \"div\", 49);\n    i0.ɵɵelement(2, \"nb-icon\", 50);\n    i0.ɵɵelementStart(3, \"p\", 51);\n    i0.ɵɵtext(4, \"\\u8F09\\u5165\\u6236\\u5225\\u8CC7\\u6599\\u4E2D...\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction HouseholdBindingComponent_div_8_ng_container_13_button_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 67);\n    i0.ɵɵlistener(\"click\", function HouseholdBindingComponent_div_8_ng_container_13_button_6_Template_button_click_0_listener() {\n      const building_r8 = i0.ɵɵrestoreView(_r7).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.onBuildingSelect(building_r8));\n    });\n    i0.ɵɵelementStart(1, \"span\", 30);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\", 68);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const building_r8 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵstyleProp(\"background-color\", ctx_r1.selectedBuilding === building_r8 ? \"#e3f2fd\" : \"transparent\")(\"border-left\", ctx_r1.selectedBuilding === building_r8 ? \"3px solid #007bff\" : \"3px solid transparent\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(building_r8);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r1.getBuildingCount(building_r8), \"\\u6236\");\n  }\n}\nfunction HouseholdBindingComponent_div_8_ng_container_13_span_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 69);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\"(\", ctx_r1.selectedBuilding, \")\");\n  }\n}\nfunction HouseholdBindingComponent_div_8_ng_container_13_span_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 70);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" - \", ctx_r1.selectedFloor, \"\");\n  }\n}\nfunction HouseholdBindingComponent_div_8_ng_container_13_div_14_button_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 75);\n    i0.ɵɵlistener(\"click\", function HouseholdBindingComponent_div_8_ng_container_13_div_14_button_5_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r1 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r1.onUnselectAllBuilding());\n    });\n    i0.ɵɵtext(1, \" \\u6E05\\u9664 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction HouseholdBindingComponent_div_8_ng_container_13_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 71)(1, \"button\", 72);\n    i0.ɵɵlistener(\"click\", function HouseholdBindingComponent_div_8_ng_container_13_div_14_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.onSelectAllFiltered());\n    });\n    i0.ɵɵtext(2, \" \\u5168\\u9078\\u7576\\u524D \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"button\", 73);\n    i0.ɵɵlistener(\"click\", function HouseholdBindingComponent_div_8_ng_container_13_div_14_Template_button_click_3_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.onSelectAllBuilding());\n    });\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(5, HouseholdBindingComponent_div_8_ng_container_13_div_14_button_5_Template, 2, 0, \"button\", 74);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵstyleProp(\"opacity\", ctx_r1.canSelectMore() ? \"1\" : \"0.5\");\n    i0.ɵɵproperty(\"disabled\", !ctx_r1.canSelectMore());\n    i0.ɵɵadvance(2);\n    i0.ɵɵstyleProp(\"opacity\", ctx_r1.canSelectMore() ? \"1\" : \"0.5\");\n    i0.ɵɵproperty(\"disabled\", !ctx_r1.canSelectMore());\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \\u5168\\u9078\", ctx_r1.selectedBuilding, \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isSomeBuildingSelected());\n  }\n}\nfunction HouseholdBindingComponent_div_8_ng_container_13_div_15_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 81);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \\u627E\\u4E0D\\u5230\\u7B26\\u5408 \\\"\", ctx_r1.searchTerm, \"\\\" \\u7684\\u6236\\u5225 \");\n  }\n}\nfunction HouseholdBindingComponent_div_8_ng_container_13_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 76)(1, \"div\", 77)(2, \"input\", 78);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdBindingComponent_div_8_ng_container_13_div_15_Template_input_ngModelChange_2_listener($event) {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      i0.ɵɵtwoWayBindingSet(ctx_r1.searchTerm, $event) || (ctx_r1.searchTerm = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"input\", function HouseholdBindingComponent_div_8_ng_container_13_div_15_Template_input_input_2_listener($event) {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.onSearchChange($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(3, \"nb-icon\", 79);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, HouseholdBindingComponent_div_8_ng_container_13_div_15_div_4_Template, 2, 1, \"div\", 80);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.searchTerm);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.searchTerm && ctx_r1.filteredHouseholds.length === 0);\n  }\n}\nfunction HouseholdBindingComponent_div_8_ng_container_13_div_16_button_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 89);\n    i0.ɵɵlistener(\"click\", function HouseholdBindingComponent_div_8_ng_container_13_div_16_button_5_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r1 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r1.onFloorSelect(\"\"));\n    });\n    i0.ɵɵtext(1, \" \\u6E05\\u9664\\u7BE9\\u9078 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction HouseholdBindingComponent_div_8_ng_container_13_div_16_button_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r13 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 90);\n    i0.ɵɵlistener(\"click\", function HouseholdBindingComponent_div_8_ng_container_13_div_16_button_7_Template_button_click_0_listener() {\n      const floor_r14 = i0.ɵɵrestoreView(_r13).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r1.onFloorSelect(floor_r14));\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const floor_r14 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(4);\n    i0.ɵɵstyleProp(\"background-color\", ctx_r1.selectedFloor === floor_r14 ? \"#007bff\" : \"#f8f9fa\")(\"color\", ctx_r1.selectedFloor === floor_r14 ? \"#fff\" : \"#495057\")(\"border-color\", ctx_r1.selectedFloor === floor_r14 ? \"#007bff\" : \"#ced4da\");\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate2(\" \", floor_r14, \" (\", ctx_r1.getFloorCount(floor_r14), \") \");\n  }\n}\nfunction HouseholdBindingComponent_div_8_ng_container_13_div_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 82)(1, \"div\", 83);\n    i0.ɵɵelement(2, \"nb-icon\", 84);\n    i0.ɵɵelementStart(3, \"span\", 85);\n    i0.ɵɵtext(4, \"\\u6A13\\u5C64\\u7BE9\\u9078:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(5, HouseholdBindingComponent_div_8_ng_container_13_div_16_button_5_Template, 2, 0, \"button\", 86);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 87);\n    i0.ɵɵtemplate(7, HouseholdBindingComponent_div_8_ng_container_13_div_16_button_7_Template, 2, 8, \"button\", 88);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.selectedFloor);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.floors);\n  }\n}\nfunction HouseholdBindingComponent_div_8_ng_container_13_div_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 91);\n    i0.ɵɵelement(1, \"nb-icon\", 92);\n    i0.ɵɵelementStart(2, \"p\", 51);\n    i0.ɵɵtext(3, \"\\u8ACB\\u5148\\u9078\\u64C7\\u68DF\\u5225\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction HouseholdBindingComponent_div_8_ng_container_13_div_19_button_1_span_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 99);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const householdCode_r16 = i0.ɵɵnextContext().$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.getHouseholdFloor(householdCode_r16), \" \");\n  }\n}\nfunction HouseholdBindingComponent_div_8_ng_container_13_div_19_button_1_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 100);\n    i0.ɵɵtext(1, \" \\u2715 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction HouseholdBindingComponent_div_8_ng_container_13_div_19_button_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r15 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 95);\n    i0.ɵɵlistener(\"click\", function HouseholdBindingComponent_div_8_ng_container_13_div_19_button_1_Template_button_click_0_listener() {\n      const householdCode_r16 = i0.ɵɵrestoreView(_r15).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r1.onHouseholdToggle(householdCode_r16));\n    });\n    i0.ɵɵelementStart(1, \"span\", 96);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, HouseholdBindingComponent_div_8_ng_container_13_div_19_button_1_span_3_Template, 2, 1, \"span\", 97)(4, HouseholdBindingComponent_div_8_ng_container_13_div_19_button_1_div_4_Template, 2, 0, \"div\", 98);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const householdCode_r16 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(4);\n    i0.ɵɵstyleProp(\"background-color\", ctx_r1.isHouseholdSelected(householdCode_r16) ? \"#007bff\" : ctx_r1.isHouseholdExcluded(householdCode_r16) ? \"#f8f9fa\" : \"#fff\")(\"color\", ctx_r1.isHouseholdSelected(householdCode_r16) ? \"#fff\" : ctx_r1.isHouseholdExcluded(householdCode_r16) ? \"#6c757d\" : \"#495057\")(\"border-color\", ctx_r1.isHouseholdSelected(householdCode_r16) ? \"#007bff\" : ctx_r1.isHouseholdExcluded(householdCode_r16) ? \"#dee2e6\" : \"#ced4da\")(\"opacity\", ctx_r1.isHouseholdDisabled(householdCode_r16) ? \"0.6\" : \"1\")(\"cursor\", ctx_r1.isHouseholdDisabled(householdCode_r16) ? \"not-allowed\" : \"pointer\");\n    i0.ɵɵproperty(\"disabled\", ctx_r1.isHouseholdDisabled(householdCode_r16));\n    i0.ɵɵadvance();\n    i0.ɵɵstyleProp(\"text-decoration\", ctx_r1.isHouseholdExcluded(householdCode_r16) ? \"line-through\" : \"none\");\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", householdCode_r16, \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.getHouseholdFloor(householdCode_r16));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isHouseholdExcluded(householdCode_r16));\n  }\n}\nfunction HouseholdBindingComponent_div_8_ng_container_13_div_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 93);\n    i0.ɵɵtemplate(1, HouseholdBindingComponent_div_8_ng_container_13_div_19_button_1_Template, 5, 16, \"button\", 94);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.filteredHouseholds);\n  }\n}\nfunction HouseholdBindingComponent_div_8_ng_container_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 52)(2, \"div\", 53)(3, \"h6\", 54);\n    i0.ɵɵtext(4, \"\\u68DF\\u5225\\u5217\\u8868\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"div\", 55);\n    i0.ɵɵtemplate(6, HouseholdBindingComponent_div_8_ng_container_13_button_6_Template, 5, 6, \"button\", 56);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 57)(8, \"div\", 53)(9, \"div\", 58)(10, \"h6\", 54);\n    i0.ɵɵtext(11, \" \\u6236\\u5225\\u9078\\u64C7 \");\n    i0.ɵɵtemplate(12, HouseholdBindingComponent_div_8_ng_container_13_span_12_Template, 2, 1, \"span\", 59)(13, HouseholdBindingComponent_div_8_ng_container_13_span_13_Template, 2, 1, \"span\", 60);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(14, HouseholdBindingComponent_div_8_ng_container_13_div_14_Template, 6, 8, \"div\", 61);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(15, HouseholdBindingComponent_div_8_ng_container_13_div_15_Template, 5, 2, \"div\", 62)(16, HouseholdBindingComponent_div_8_ng_container_13_div_16_Template, 8, 2, \"div\", 63);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"div\", 64);\n    i0.ɵɵtemplate(18, HouseholdBindingComponent_div_8_ng_container_13_div_18_Template, 4, 0, \"div\", 65)(19, HouseholdBindingComponent_div_8_ng_container_13_div_19_Template, 2, 1, \"div\", 66);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.buildings);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.selectedBuilding);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.selectedFloor);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.allowBatchSelect && ctx_r1.selectedBuilding && ctx_r1.filteredHouseholds.length > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.allowSearch && ctx_r1.selectedBuilding);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.selectedBuilding && ctx_r1.floors.length > 1);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.selectedBuilding);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.selectedBuilding && ctx_r1.filteredHouseholds.length > 0);\n  }\n}\nfunction HouseholdBindingComponent_div_8_div_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 37);\n    i0.ɵɵelement(1, \"nb-icon\", 101);\n    i0.ɵɵelementStart(2, \"span\");\n    i0.ɵɵtext(3, \"\\u9650\\u5236: \\u6700\\u591A \");\n    i0.ɵɵelementStart(4, \"strong\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(6, \" \\u500B\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r1.maxSelections);\n  }\n}\nfunction HouseholdBindingComponent_div_8_div_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 37);\n    i0.ɵɵelement(1, \"nb-icon\", 29);\n    i0.ɵɵelementStart(2, \"span\");\n    i0.ɵɵtext(3, \"\\u7576\\u524D\\u68DF\\u5225: \");\n    i0.ɵɵelementStart(4, \"strong\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r1.selectedBuilding);\n  }\n}\nfunction HouseholdBindingComponent_div_8_div_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 102);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate2(\" \\u641C\\u5C0B: \\\"\", ctx_r1.searchTerm, \"\\\" (\", ctx_r1.filteredHouseholds.length, \" \\u500B\\u7D50\\u679C) \");\n  }\n}\nfunction HouseholdBindingComponent_div_8_button_29_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r17 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 103);\n    i0.ɵɵlistener(\"click\", function HouseholdBindingComponent_div_8_button_29_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r17);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onClearAll());\n    });\n    i0.ɵɵelement(1, \"nb-icon\", 104);\n    i0.ɵɵtext(2, \" \\u6E05\\u7A7A\\u5168\\u90E8 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction HouseholdBindingComponent_div_8_button_30_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r18 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 105);\n    i0.ɵɵlistener(\"click\", function HouseholdBindingComponent_div_8_button_30_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r18);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.resetSearch());\n    });\n    i0.ɵɵelement(1, \"nb-icon\", 106);\n    i0.ɵɵtext(2, \" \\u91CD\\u7F6E\\u641C\\u5C0B \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction HouseholdBindingComponent_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 25)(1, \"div\", 26)(2, \"div\", 27)(3, \"div\", 28);\n    i0.ɵɵelement(4, \"nb-icon\", 29);\n    i0.ɵɵelementStart(5, \"span\", 30);\n    i0.ɵɵtext(6, \"\\u9078\\u64C7\\u6236\\u5225\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"span\", 31);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"span\", 31);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(11, \"div\", 32);\n    i0.ɵɵtemplate(12, HouseholdBindingComponent_div_8_div_12_Template, 5, 0, \"div\", 33)(13, HouseholdBindingComponent_div_8_ng_container_13_Template, 20, 8, \"ng-container\", 5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"div\", 34)(15, \"div\", 35)(16, \"div\", 36)(17, \"div\", 37);\n    i0.ɵɵelement(18, \"nb-icon\", 38);\n    i0.ɵɵelementStart(19, \"span\");\n    i0.ɵɵtext(20, \"\\u5DF2\\u9078\\u64C7: \");\n    i0.ɵɵelementStart(21, \"strong\");\n    i0.ɵɵtext(22);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(23, \" \\u500B\\u6236\\u5225\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(24, HouseholdBindingComponent_div_8_div_24_Template, 7, 1, \"div\", 39)(25, HouseholdBindingComponent_div_8_div_25_Template, 6, 1, \"div\", 39);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(26, HouseholdBindingComponent_div_8_div_26_Template, 2, 2, \"div\", 40);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(27, \"div\", 41)(28, \"div\", 42);\n    i0.ɵɵtemplate(29, HouseholdBindingComponent_div_8_button_29_Template, 3, 0, \"button\", 43)(30, HouseholdBindingComponent_div_8_button_30_Template, 3, 0, \"button\", 44);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(31, \"div\", 42)(32, \"button\", 45);\n    i0.ɵɵlistener(\"click\", function HouseholdBindingComponent_div_8_Template_button_click_32_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.closeDropdown());\n    });\n    i0.ɵɵtext(33, \" \\u53D6\\u6D88 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(34, \"button\", 46);\n    i0.ɵɵlistener(\"click\", function HouseholdBindingComponent_div_8_Template_button_click_34_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.closeDropdown());\n    });\n    i0.ɵɵelement(35, \"nb-icon\", 47);\n    i0.ɵɵtext(36, \" \\u78BA\\u5B9A\\u9078\\u64C7 \");\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate1(\"(\", ctx_r1.buildings.length, \" \\u500B\\u68DF\\u5225)\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\\u5DF2\\u9078\\u64C7: \", ctx_r1.getSelectedCount(), \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isLoading);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.isLoading);\n    i0.ɵɵadvance(9);\n    i0.ɵɵtextInterpolate(ctx_r1.getSelectedCount());\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.maxSelections);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.selectedBuilding);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.searchTerm);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.selectedHouseholds.length > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.allowSearch && ctx_r1.searchTerm);\n  }\n}\nfunction HouseholdBindingComponent_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r19 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 107);\n    i0.ɵɵlistener(\"click\", function HouseholdBindingComponent_div_9_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.closeDropdown());\n    });\n    i0.ɵɵelementEnd();\n  }\n}\nexport class HouseholdBindingComponent {\n  constructor(cdr, houseCustomService) {\n    this.cdr = cdr;\n    this.houseCustomService = houseCustomService;\n    this.placeholder = '請選擇戶別';\n    this.maxSelections = null;\n    this.disabled = false;\n    this.buildCaseId = null; // 新增：建案ID\n    this.buildingData = {};\n    this.showSelectedArea = true;\n    this.allowSearch = true;\n    this.allowBatchSelect = true;\n    this.excludedHouseholds = []; // 新增：排除的戶別（已被其他元件選擇）\n    this.selectionChange = new EventEmitter();\n    this.isOpen = false;\n    this.selectedBuilding = '';\n    this.searchTerm = '';\n    this.selectedFloor = ''; // 新增：選中的樓層\n    this.selectedHouseholds = [];\n    this.buildings = [];\n    this.floors = []; // 新增：當前棟別的樓層列表\n    this.filteredHouseholds = []; // 簡化為字串陣列\n    this.selectedByBuilding = {};\n    this.isLoading = false; // 新增：載入狀態\n    // ControlValueAccessor implementation\n    this.onChange = value => {};\n    this.onTouched = () => {};\n  }\n  writeValue(value) {\n    this.selectedHouseholds = value || [];\n    this.updateSelectedByBuilding();\n  }\n  registerOnChange(fn) {\n    this.onChange = fn;\n  }\n  registerOnTouched(fn) {\n    this.onTouched = fn;\n  }\n  setDisabledState(isDisabled) {\n    this.disabled = isDisabled;\n  }\n  ngOnInit() {\n    this.initializeData();\n  }\n  ngOnChanges(changes) {\n    if (changes['buildCaseId'] && this.buildCaseId) {\n      // 當建案ID變更時，重新載入資料\n      this.initializeData();\n    }\n    if (changes['buildingData']) {\n      this.buildings = Object.keys(this.buildingData);\n      this.updateFilteredHouseholds();\n      this.updateSelectedByBuilding();\n    }\n    if (changes['excludedHouseholds']) {\n      // 當排除列表變化時，重新更新顯示\n      this.updateFilteredHouseholds();\n      console.log('Excluded households updated:', this.excludedHouseholds);\n    }\n  }\n  initializeData() {\n    if (this.buildCaseId) {\n      // 使用API載入資料\n      this.loadBuildingDataFromApi();\n    } else {\n      // 如果沒有提供建案ID且沒有提供 buildingData，使用 mock 資料\n      if (!this.buildingData || Object.keys(this.buildingData).length === 0) {\n        this.buildingData = this.generateMockData();\n      }\n      this.buildings = Object.keys(this.buildingData);\n      console.log('Component initialized with buildings:', this.buildings);\n      this.updateSelectedByBuilding();\n    }\n  }\n  loadBuildingDataFromApi() {\n    if (!this.buildCaseId) return;\n    this.isLoading = true;\n    this.houseCustomService.getDropDown(this.buildCaseId).subscribe({\n      next: response => {\n        console.log('API response:', response);\n        this.buildingData = this.convertApiResponseToBuildingData(response.Entries);\n        this.buildings = Object.keys(this.buildingData);\n        this.updateSelectedByBuilding();\n        this.isLoading = false;\n        this.cdr.detectChanges();\n      },\n      error: error => {\n        console.error('Error loading building data:', error);\n        // 如果API載入失敗，使用mock資料作為備援\n        this.buildingData = this.generateMockData();\n        this.buildings = Object.keys(this.buildingData);\n        this.updateSelectedByBuilding();\n        this.isLoading = false;\n        this.cdr.detectChanges();\n      }\n    });\n  }\n  convertApiResponseToBuildingData(entries) {\n    const buildingData = {};\n    Object.entries(entries).forEach(([building, houses]) => {\n      buildingData[building] = houses.map(house => ({\n        code: house.HouseName,\n        building: house.Building,\n        floor: house.Floor,\n        houseId: house.HouseId,\n        houseName: house.HouseName,\n        isSelected: false,\n        isDisabled: false\n      }));\n    });\n    return buildingData;\n  }\n  updateSelectedByBuilding() {\n    const grouped = {};\n    this.selectedHouseholds.forEach(code => {\n      for (const building of this.buildings) {\n        const item = this.buildingData[building]?.find(h => h.code === code);\n        if (item) {\n          if (!grouped[building]) grouped[building] = [];\n          grouped[building].push(code);\n          break;\n        }\n      }\n    });\n    this.selectedByBuilding = grouped;\n  }\n  generateMockData() {\n    // 簡化版本 - 直接生成字串陣列\n    const simpleMockData = {\n      'A棟': Array.from({\n        length: 50\n      }, (_, i) => `A${String(i + 1).padStart(3, '0')}`),\n      'B棟': Array.from({\n        length: 40\n      }, (_, i) => `B${String(i + 1).padStart(3, '0')}`),\n      'C棟': Array.from({\n        length: 60\n      }, (_, i) => `C${String(i + 1).padStart(3, '0')}`),\n      'D棟': Array.from({\n        length: 35\n      }, (_, i) => `D${String(i + 1).padStart(3, '0')}`),\n      'E棟': Array.from({\n        length: 45\n      }, (_, i) => `E${String(i + 1).padStart(3, '0')}`)\n    };\n    // 轉換為 BuildingData 格式\n    const buildingData = {};\n    Object.entries(simpleMockData).forEach(([building, codes]) => {\n      buildingData[building] = codes.map(code => ({\n        code,\n        building,\n        floor: `${Math.floor((parseInt(code.slice(1)) - 1) / 4) + 1}F`,\n        isSelected: false,\n        isDisabled: false\n      }));\n    });\n    return buildingData;\n  }\n  onBuildingSelect(building) {\n    console.log('Building selected:', building);\n    this.selectedBuilding = building;\n    this.selectedFloor = ''; // 重置樓層選擇\n    this.searchTerm = '';\n    this.updateFloorsForBuilding(); // 更新樓層列表\n    this.updateFilteredHouseholds();\n    console.log('Filtered households count:', this.filteredHouseholds.length);\n    // 手動觸發變更偵測\n    this.cdr.detectChanges();\n  }\n  onBuildingClick(building) {\n    console.log('Building clicked (mousedown):', building);\n  }\n  updateFilteredHouseholds() {\n    console.log('Updating filtered households for building:', this.selectedBuilding, 'floor:', this.selectedFloor);\n    if (!this.selectedBuilding) {\n      this.filteredHouseholds = [];\n      return;\n    }\n    const households = this.buildingData[this.selectedBuilding] || [];\n    console.log('Available households for building:', households.length);\n    // 提取戶別代碼並進行搜尋和樓層過濾\n    this.filteredHouseholds = households.filter(h => {\n      // 樓層篩選：如果有選擇樓層，只顯示該樓層的戶別\n      const floorMatch = !this.selectedFloor || h.floor === this.selectedFloor;\n      // 搜尋篩選：戶別代碼包含搜尋詞\n      const searchMatch = h.code.toLowerCase().includes(this.searchTerm.toLowerCase());\n      return floorMatch && searchMatch;\n    }).map(h => h.code);\n    console.log('Filtered households result:', this.filteredHouseholds.length);\n  }\n  onSearchChange(event) {\n    this.searchTerm = event.target.value;\n    this.updateFilteredHouseholds();\n    console.log('Search term changed:', this.searchTerm);\n  }\n  resetSearch() {\n    this.searchTerm = '';\n    this.updateFilteredHouseholds();\n    console.log('Search reset');\n  }\n  onHouseholdToggle(householdCode) {\n    // 防止選擇已排除的戶別\n    if (this.isHouseholdExcluded(householdCode)) {\n      console.log(`戶別 ${householdCode} 已被其他元件選擇，無法重複選擇`);\n      return;\n    }\n    const isSelected = this.selectedHouseholds.includes(householdCode);\n    let newSelection;\n    if (isSelected) {\n      newSelection = this.selectedHouseholds.filter(h => h !== householdCode);\n    } else {\n      if (this.maxSelections && this.selectedHouseholds.length >= this.maxSelections) {\n        console.log('已達到最大選擇數量');\n        return; // 達到最大選擇數量\n      }\n      newSelection = [...this.selectedHouseholds, householdCode];\n    }\n    this.selectedHouseholds = newSelection;\n    this.emitChanges();\n  }\n  onRemoveHousehold(householdCode) {\n    this.selectedHouseholds = this.selectedHouseholds.filter(h => h !== householdCode);\n    this.emitChanges();\n  }\n  onSelectAllFiltered() {\n    if (!this.selectedBuilding || this.filteredHouseholds.length === 0) return;\n    // 計算可以新增的戶別數量\n    const currentCount = this.selectedHouseholds.length;\n    const maxAllowed = this.maxSelections || Infinity;\n    const remainingSlots = maxAllowed - currentCount; // 取得尚未選擇且未被排除的過濾戶別\n    const unselectedFiltered = this.filteredHouseholds.filter(code => !this.selectedHouseholds.includes(code) && !this.isHouseholdExcluded(code));\n    // 根據剩餘空間決定要新增的戶別\n    const toAdd = unselectedFiltered.slice(0, remainingSlots);\n    if (toAdd.length > 0) {\n      this.selectedHouseholds = [...this.selectedHouseholds, ...toAdd];\n      this.emitChanges();\n      console.log(`全選當前: 新增了 ${toAdd.length} 個戶別`);\n    }\n  }\n  onSelectAllBuilding() {\n    if (!this.selectedBuilding) return;\n    // 取得當前棟別的所有戶別\n    const buildingHouseholds = this.buildingData[this.selectedBuilding]?.map(h => h.code) || [];\n    // 計算可以新增的戶別數量\n    const currentCount = this.selectedHouseholds.length;\n    const maxAllowed = this.maxSelections || Infinity;\n    const remainingSlots = maxAllowed - currentCount; // 取得尚未選擇且未被排除的棟別戶別\n    const unselectedBuilding = buildingHouseholds.filter(code => !this.selectedHouseholds.includes(code) && !this.isHouseholdExcluded(code));\n    // 根據剩餘空間決定要新增的戶別\n    const toAdd = unselectedBuilding.slice(0, remainingSlots);\n    if (toAdd.length > 0) {\n      this.selectedHouseholds = [...this.selectedHouseholds, ...toAdd];\n      this.emitChanges();\n      console.log(`全選棟別: 新增了 ${toAdd.length} 個戶別`);\n    }\n  }\n  onUnselectAllBuilding() {\n    if (!this.selectedBuilding) return;\n    const buildingHouseholds = this.buildingData[this.selectedBuilding]?.map(h => h.code) || [];\n    this.selectedHouseholds = this.selectedHouseholds.filter(h => !buildingHouseholds.includes(h));\n    this.emitChanges();\n  }\n  onClearAll() {\n    this.selectedHouseholds = [];\n    this.emitChanges();\n  }\n  emitChanges() {\n    this.updateSelectedByBuilding();\n    this.onChange([...this.selectedHouseholds]);\n    this.onTouched();\n    const selectedItems = this.selectedHouseholds.map(code => {\n      for (const building of this.buildings) {\n        const item = this.buildingData[building]?.find(h => h.code === code);\n        if (item) return item;\n      }\n      return null;\n    }).filter(item => item !== null);\n    this.selectionChange.emit(selectedItems);\n  }\n  toggleDropdown() {\n    if (!this.disabled) {\n      this.isOpen = !this.isOpen;\n      console.log('Dropdown toggled. isOpen:', this.isOpen);\n      console.log('Buildings available:', this.buildings);\n    }\n  }\n  closeDropdown() {\n    this.isOpen = false;\n  }\n  isHouseholdSelected(householdCode) {\n    return this.selectedHouseholds.includes(householdCode);\n  }\n  isHouseholdExcluded(householdCode) {\n    return this.excludedHouseholds.includes(householdCode);\n  }\n  isHouseholdDisabled(householdCode) {\n    return this.isHouseholdExcluded(householdCode) || !this.canSelectMore() && !this.isHouseholdSelected(householdCode);\n  }\n  canSelectMore() {\n    return !this.maxSelections || this.selectedHouseholds.length < this.maxSelections;\n  }\n  isAllBuildingSelected() {\n    if (!this.selectedBuilding) return false;\n    const buildingHouseholds = this.buildingData[this.selectedBuilding].filter(h => !h.isDisabled).map(h => h.code);\n    return buildingHouseholds.length > 0 && buildingHouseholds.every(code => this.selectedHouseholds.includes(code));\n  }\n  isSomeBuildingSelected() {\n    if (!this.selectedBuilding) return false;\n    const buildingHouseholds = this.buildingData[this.selectedBuilding]?.map(h => h.code) || [];\n    return buildingHouseholds.some(code => this.selectedHouseholds.includes(code));\n  }\n  getSelectedByBuilding() {\n    return this.selectedByBuilding;\n  }\n  getBuildingCount(building) {\n    return this.buildingData[building]?.length || 0;\n  }\n  getSelectedCount() {\n    return this.selectedHouseholds.length;\n  }\n  // 輔助方法：安全地獲取建築物的已選戶別\n  getBuildingSelectedHouseholds(building) {\n    return this.selectedByBuilding[building] || [];\n  }\n  // 輔助方法：檢查建築物是否有已選戶別\n  hasBuildingSelected(building) {\n    return !!(this.selectedByBuilding[building] && this.selectedByBuilding[building].length > 0);\n  }\n  // 新增：更新當前棟別的樓層列表\n  updateFloorsForBuilding() {\n    if (!this.selectedBuilding) {\n      this.floors = [];\n      return;\n    }\n    const households = this.buildingData[this.selectedBuilding] || [];\n    const floorSet = new Set();\n    households.forEach(household => {\n      if (household.floor) {\n        floorSet.add(household.floor);\n      }\n    });\n    // 對樓層進行自然排序\n    this.floors = Array.from(floorSet).sort((a, b) => {\n      const numA = parseInt(a.replace(/[^0-9]/g, ''));\n      const numB = parseInt(b.replace(/[^0-9]/g, ''));\n      return numA - numB;\n    });\n    console.log('Updated floors for building:', this.selectedBuilding, this.floors);\n  }\n  // 新增：樓層選擇處理\n  onFloorSelect(floor) {\n    console.log('Floor selected:', floor);\n    this.selectedFloor = this.selectedFloor === floor ? '' : floor; // 切換選擇\n    this.updateFilteredHouseholds();\n    this.cdr.detectChanges();\n  }\n  // 新增：取得當前棟別的樓層計數\n  getFloorCount(floor) {\n    if (!this.selectedBuilding) return 0;\n    const households = this.buildingData[this.selectedBuilding] || [];\n    return households.filter(h => h.floor === floor).length;\n  }\n  // 新增：取得戶別的樓層資訊\n  getHouseholdFloor(householdCode) {\n    if (!this.selectedBuilding) return '';\n    const households = this.buildingData[this.selectedBuilding] || [];\n    const household = households.find(h => h.code === householdCode);\n    return household?.floor || '';\n  }\n  static {\n    this.ɵfac = function HouseholdBindingComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || HouseholdBindingComponent)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i1.HouseCustomService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: HouseholdBindingComponent,\n      selectors: [[\"app-household-binding\"]],\n      inputs: {\n        placeholder: \"placeholder\",\n        maxSelections: \"maxSelections\",\n        disabled: \"disabled\",\n        buildCaseId: \"buildCaseId\",\n        buildingData: \"buildingData\",\n        showSelectedArea: \"showSelectedArea\",\n        allowSearch: \"allowSearch\",\n        allowBatchSelect: \"allowBatchSelect\",\n        excludedHouseholds: \"excludedHouseholds\"\n      },\n      outputs: {\n        selectionChange: \"selectionChange\"\n      },\n      features: [i0.ɵɵProvidersFeature([{\n        provide: NG_VALUE_ACCESSOR,\n        useExisting: forwardRef(() => HouseholdBindingComponent),\n        multi: true\n      }]), i0.ɵɵNgOnChangesFeature],\n      decls: 10,\n      vars: 10,\n      consts: [[1, \"household-binding-container\"], [\"class\", \"selected-households-area\", 4, \"ngIf\"], [1, \"selector-container\", 2, \"position\", \"relative\"], [\"type\", \"button\", 1, \"selector-button\", 2, \"width\", \"100%\", \"display\", \"flex\", \"align-items\", \"center\", \"justify-content\", \"space-between\", \"padding\", \"0.5rem 0.75rem\", \"border\", \"1px solid #ced4da\", \"border-radius\", \"0.375rem\", \"background-color\", \"#fff\", \"cursor\", \"pointer\", 3, \"click\", \"disabled\"], [1, \"selector-text\"], [4, \"ngIf\"], [\"icon\", \"chevron-down-outline\", 1, \"chevron-icon\"], [\"style\", \"position: absolute; top: calc(100% + 4px); left: 0; right: 0; z-index: 99999; background: white; border: 1px solid #ced4da; border-radius: 8px; box-shadow: 0 4px 12px rgba(0,0,0,0.15); padding: 0; margin: 0; max-height: min(80vh, 600px); overflow: hidden;\", 4, \"ngIf\"], [\"class\", \"backdrop\", 3, \"click\", 4, \"ngIf\"], [1, \"selected-households-area\"], [1, \"selected-header\"], [1, \"selected-info\"], [\"icon\", \"people-outline\", 1, \"text-primary\"], [1, \"selected-count\"], [\"type\", \"button\", 1, \"btn\", \"btn-outline-danger\", \"btn-sm\", 3, \"click\", \"disabled\"], [1, \"selected-content\"], [\"class\", \"building-group\", 4, \"ngFor\", \"ngForOf\"], [1, \"building-group\"], [1, \"building-label\"], [1, \"households-tags\"], [\"class\", \"household-tag\", 4, \"ngFor\", \"ngForOf\"], [1, \"household-tag\"], [\"type\", \"button\", 1, \"remove-btn\", 3, \"click\", \"disabled\"], [\"icon\", \"close-outline\"], [\"icon\", \"loader-outline\", 1, \"spin\"], [2, \"position\", \"absolute\", \"top\", \"calc(100% + 4px)\", \"left\", \"0\", \"right\", \"0\", \"z-index\", \"99999\", \"background\", \"white\", \"border\", \"1px solid #ced4da\", \"border-radius\", \"8px\", \"box-shadow\", \"0 4px 12px rgba(0,0,0,0.15)\", \"padding\", \"0\", \"margin\", \"0\", \"max-height\", \"min(80vh, 600px)\", \"overflow\", \"hidden\"], [2, \"padding\", \"12px 16px\", \"border-bottom\", \"1px solid #e9ecef\", \"background-color\", \"#f8f9fa\"], [2, \"display\", \"flex\", \"align-items\", \"center\", \"justify-content\", \"space-between\"], [2, \"display\", \"flex\", \"align-items\", \"center\", \"gap\", \"8px\"], [\"icon\", \"home-outline\", 2, \"color\", \"#007bff\"], [2, \"font-weight\", \"500\", \"color\", \"#495057\"], [2, \"font-size\", \"0.875rem\", \"color\", \"#6c757d\"], [2, \"display\", \"flex\", \"height\", \"calc(100% - 160px)\", \"min-height\", \"300px\", \"max-height\", \"400px\"], [\"style\", \"width: 100%; display: flex; align-items: center; justify-content: center; padding: 40px;\", 4, \"ngIf\"], [2, \"padding\", \"16px\", \"border-top\", \"1px solid #e9ecef\", \"background-color\", \"#f8f9fa\"], [2, \"display\", \"flex\", \"align-items\", \"center\", \"justify-content\", \"space-between\", \"margin-bottom\", \"12px\"], [2, \"display\", \"flex\", \"align-items\", \"center\", \"gap\", \"16px\", \"font-size\", \"0.875rem\", \"color\", \"#495057\"], [2, \"display\", \"flex\", \"align-items\", \"center\", \"gap\", \"4px\"], [\"icon\", \"checkmark-circle-outline\", 2, \"color\", \"#28a745\"], [\"style\", \"display: flex; align-items: center; gap: 4px;\", 4, \"ngIf\"], [\"style\", \"font-size: 0.75rem; color: #6c757d; background-color: #e9ecef; padding: 4px 8px; border-radius: 12px;\", 4, \"ngIf\"], [2, \"display\", \"flex\", \"align-items\", \"center\", \"justify-content\", \"space-between\", \"gap\", \"8px\"], [2, \"display\", \"flex\", \"gap\", \"8px\"], [\"type\", \"button\", \"style\", \"padding: 8px 12px; background: #dc3545; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 0.875rem; display: flex; align-items: center; gap: 4px;\", 3, \"click\", 4, \"ngIf\"], [\"type\", \"button\", \"style\", \"padding: 8px 12px; background: #6c757d; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 0.875rem; display: flex; align-items: center; gap: 4px;\", 3, \"click\", 4, \"ngIf\"], [\"type\", \"button\", 2, \"padding\", \"8px 16px\", \"background\", \"#6c757d\", \"color\", \"white\", \"border\", \"none\", \"border-radius\", \"4px\", \"cursor\", \"pointer\", \"font-size\", \"0.875rem\", 3, \"click\"], [\"type\", \"button\", 2, \"padding\", \"8px 20px\", \"background\", \"#007bff\", \"color\", \"white\", \"border\", \"none\", \"border-radius\", \"4px\", \"cursor\", \"pointer\", \"font-size\", \"0.875rem\", \"font-weight\", \"500\", \"display\", \"flex\", \"align-items\", \"center\", \"gap\", \"4px\", 3, \"click\"], [\"icon\", \"checkmark-outline\"], [2, \"width\", \"100%\", \"display\", \"flex\", \"align-items\", \"center\", \"justify-content\", \"center\", \"padding\", \"40px\"], [2, \"text-align\", \"center\", \"color\", \"#6c757d\"], [\"icon\", \"loader-outline\", 1, \"spin\", 2, \"font-size\", \"2rem\", \"margin-bottom\", \"8px\"], [2, \"margin\", \"0\", \"font-size\", \"0.875rem\"], [2, \"width\", \"40%\", \"border-right\", \"1px solid #e9ecef\", \"background-color\", \"#f8f9fa\"], [2, \"padding\", \"12px 16px\", \"border-bottom\", \"1px solid #e9ecef\"], [2, \"margin\", \"0\", \"font-size\", \"0.875rem\", \"font-weight\", \"500\", \"color\", \"#495057\"], [2, \"max-height\", \"calc(100% - 52px)\", \"overflow-y\", \"auto\"], [\"type\", \"button\", \"style\", \"width: 100%; text-align: left; padding: 12px 16px; border: none; cursor: pointer; transition: all 0.15s ease; display: flex; align-items: center; justify-content: space-between;\", 3, \"background-color\", \"border-left\", \"click\", 4, \"ngFor\", \"ngForOf\"], [2, \"flex\", \"1\", \"display\", \"flex\", \"flex-direction\", \"column\"], [2, \"display\", \"flex\", \"align-items\", \"center\", \"justify-content\", \"space-between\", \"margin-bottom\", \"8px\"], [\"style\", \"color: #007bff;\", 4, \"ngIf\"], [\"style\", \"color: #28a745; font-size: 0.75rem;\", 4, \"ngIf\"], [\"style\", \"display: flex; gap: 4px;\", 4, \"ngIf\"], [\"style\", \"margin-top: 8px;\", 4, \"ngIf\"], [\"style\", \"margin-top: 12px;\", 4, \"ngIf\"], [2, \"flex\", \"1\", \"padding\", \"16px\", \"overflow-y\", \"auto\"], [\"style\", \"text-align: center; padding: 40px 20px; color: #6c757d;\", 4, \"ngIf\"], [\"style\", \"display: grid; grid-template-columns: repeat(auto-fill, minmax(90px, 1fr)); gap: 8px;\", 4, \"ngIf\"], [\"type\", \"button\", 2, \"width\", \"100%\", \"text-align\", \"left\", \"padding\", \"12px 16px\", \"border\", \"none\", \"cursor\", \"pointer\", \"transition\", \"all 0.15s ease\", \"display\", \"flex\", \"align-items\", \"center\", \"justify-content\", \"space-between\", 3, \"click\"], [2, \"font-size\", \"0.75rem\", \"color\", \"#6c757d\", \"background-color\", \"#e9ecef\", \"padding\", \"2px 6px\", \"border-radius\", \"10px\"], [2, \"color\", \"#007bff\"], [2, \"color\", \"#28a745\", \"font-size\", \"0.75rem\"], [2, \"display\", \"flex\", \"gap\", \"4px\"], [\"type\", \"button\", 2, \"padding\", \"4px 8px\", \"font-size\", \"0.75rem\", \"background\", \"#007bff\", \"color\", \"white\", \"border\", \"none\", \"border-radius\", \"4px\", \"cursor\", \"pointer\", 3, \"click\", \"disabled\"], [\"type\", \"button\", 2, \"padding\", \"4px 8px\", \"font-size\", \"0.75rem\", \"background\", \"#28a745\", \"color\", \"white\", \"border\", \"none\", \"border-radius\", \"4px\", \"cursor\", \"pointer\", 3, \"click\", \"disabled\"], [\"type\", \"button\", \"style\", \"padding: 4px 8px; font-size: 0.75rem; background: #6c757d; color: white; border: none; border-radius: 4px; cursor: pointer;\", 3, \"click\", 4, \"ngIf\"], [\"type\", \"button\", 2, \"padding\", \"4px 8px\", \"font-size\", \"0.75rem\", \"background\", \"#6c757d\", \"color\", \"white\", \"border\", \"none\", \"border-radius\", \"4px\", \"cursor\", \"pointer\", 3, \"click\"], [2, \"margin-top\", \"8px\"], [2, \"position\", \"relative\"], [\"type\", \"text\", \"placeholder\", \"\\u641C\\u5C0B\\u6236\\u5225\\u4EE3\\u78BC...\", 2, \"width\", \"100%\", \"padding\", \"6px 32px 6px 12px\", \"border\", \"1px solid #ced4da\", \"border-radius\", \"4px\", \"font-size\", \"0.875rem\", \"outline\", \"none\", 3, \"ngModelChange\", \"input\", \"ngModel\"], [\"icon\", \"search-outline\", 2, \"position\", \"absolute\", \"right\", \"10px\", \"top\", \"50%\", \"transform\", \"translateY(-50%)\", \"color\", \"#6c757d\", \"font-size\", \"0.875rem\"], [\"style\", \"font-size: 0.75rem; color: #dc3545; margin-top: 4px;\", 4, \"ngIf\"], [2, \"font-size\", \"0.75rem\", \"color\", \"#dc3545\", \"margin-top\", \"4px\"], [2, \"margin-top\", \"12px\"], [2, \"display\", \"flex\", \"align-items\", \"center\", \"gap\", \"8px\", \"margin-bottom\", \"8px\"], [\"icon\", \"layers-outline\", 2, \"color\", \"#6c757d\", \"font-size\", \"0.875rem\"], [2, \"font-size\", \"0.875rem\", \"font-weight\", \"500\", \"color\", \"#495057\"], [\"type\", \"button\", \"style\", \"font-size: 0.75rem; color: #007bff; background: none; border: none; text-decoration: underline; cursor: pointer;\", 3, \"click\", 4, \"ngIf\"], [2, \"display\", \"flex\", \"flex-wrap\", \"wrap\", \"gap\", \"4px\", \"max-height\", \"100px\", \"overflow-y\", \"auto\"], [\"type\", \"button\", \"style\", \"padding: 4px 8px; border: 1px solid; border-radius: 4px; font-size: 0.75rem; cursor: pointer; transition: all 0.15s ease; white-space: nowrap;\", 3, \"background-color\", \"color\", \"border-color\", \"click\", 4, \"ngFor\", \"ngForOf\"], [\"type\", \"button\", 2, \"font-size\", \"0.75rem\", \"color\", \"#007bff\", \"background\", \"none\", \"border\", \"none\", \"text-decoration\", \"underline\", \"cursor\", \"pointer\", 3, \"click\"], [\"type\", \"button\", 2, \"padding\", \"4px 8px\", \"border\", \"1px solid\", \"border-radius\", \"4px\", \"font-size\", \"0.75rem\", \"cursor\", \"pointer\", \"transition\", \"all 0.15s ease\", \"white-space\", \"nowrap\", 3, \"click\"], [2, \"text-align\", \"center\", \"padding\", \"40px 20px\", \"color\", \"#6c757d\"], [\"icon\", \"home-outline\", 2, \"font-size\", \"2rem\", \"margin-bottom\", \"8px\", \"opacity\", \"0.5\"], [2, \"display\", \"grid\", \"grid-template-columns\", \"repeat(auto-fill, minmax(90px, 1fr))\", \"gap\", \"8px\"], [\"type\", \"button\", \"style\", \"padding: 6px 4px; border: 1px solid; border-radius: 4px; transition: all 0.15s ease; font-size: 0.75rem; text-align: center; min-height: 40px; position: relative; display: flex; flex-direction: column; justify-content: center;\", 3, \"disabled\", \"background-color\", \"color\", \"border-color\", \"opacity\", \"cursor\", \"click\", 4, \"ngFor\", \"ngForOf\"], [\"type\", \"button\", 2, \"padding\", \"6px 4px\", \"border\", \"1px solid\", \"border-radius\", \"4px\", \"transition\", \"all 0.15s ease\", \"font-size\", \"0.75rem\", \"text-align\", \"center\", \"min-height\", \"40px\", \"position\", \"relative\", \"display\", \"flex\", \"flex-direction\", \"column\", \"justify-content\", \"center\", 3, \"click\", \"disabled\"], [2, \"font-weight\", \"500\", \"line-height\", \"1.2\"], [\"style\", \"font-size: 0.6rem; opacity: 0.8; margin-top: 2px;\", 4, \"ngIf\"], [\"style\", \"position: absolute; top: -8px; right: -8px; background: #dc3545; color: white; border-radius: 50%; width: 16px; height: 16px; font-size: 10px; display: flex; align-items: center; justify-content: center;\", 4, \"ngIf\"], [2, \"font-size\", \"0.6rem\", \"opacity\", \"0.8\", \"margin-top\", \"2px\"], [2, \"position\", \"absolute\", \"top\", \"-8px\", \"right\", \"-8px\", \"background\", \"#dc3545\", \"color\", \"white\", \"border-radius\", \"50%\", \"width\", \"16px\", \"height\", \"16px\", \"font-size\", \"10px\", \"display\", \"flex\", \"align-items\", \"center\", \"justify-content\", \"center\"], [\"icon\", \"alert-circle-outline\", 2, \"color\", \"#ffc107\"], [2, \"font-size\", \"0.75rem\", \"color\", \"#6c757d\", \"background-color\", \"#e9ecef\", \"padding\", \"4px 8px\", \"border-radius\", \"12px\"], [\"type\", \"button\", 2, \"padding\", \"8px 12px\", \"background\", \"#dc3545\", \"color\", \"white\", \"border\", \"none\", \"border-radius\", \"4px\", \"cursor\", \"pointer\", \"font-size\", \"0.875rem\", \"display\", \"flex\", \"align-items\", \"center\", \"gap\", \"4px\", 3, \"click\"], [\"icon\", \"trash-2-outline\"], [\"type\", \"button\", 2, \"padding\", \"8px 12px\", \"background\", \"#6c757d\", \"color\", \"white\", \"border\", \"none\", \"border-radius\", \"4px\", \"cursor\", \"pointer\", \"font-size\", \"0.875rem\", \"display\", \"flex\", \"align-items\", \"center\", \"gap\", \"4px\", 3, \"click\"], [\"icon\", \"refresh-outline\"], [1, \"backdrop\", 3, \"click\"]],\n      template: function HouseholdBindingComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵtemplate(1, HouseholdBindingComponent_div_1_Template, 10, 3, \"div\", 1);\n          i0.ɵɵelementStart(2, \"div\", 2)(3, \"button\", 3);\n          i0.ɵɵlistener(\"click\", function HouseholdBindingComponent_Template_button_click_3_listener() {\n            return ctx.toggleDropdown();\n          });\n          i0.ɵɵelementStart(4, \"span\", 4);\n          i0.ɵɵtemplate(5, HouseholdBindingComponent_ng_container_5_Template, 3, 0, \"ng-container\", 5)(6, HouseholdBindingComponent_ng_container_6_Template, 2, 1, \"ng-container\", 5);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(7, \"nb-icon\", 6);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(8, HouseholdBindingComponent_div_8_Template, 37, 10, \"div\", 7);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(9, HouseholdBindingComponent_div_9_Template, 1, 0, \"div\", 8);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.showSelectedArea && ctx.selectedHouseholds.length > 0);\n          i0.ɵɵadvance(2);\n          i0.ɵɵclassProp(\"disabled\", ctx.disabled || ctx.isLoading);\n          i0.ɵɵproperty(\"disabled\", ctx.disabled || ctx.isLoading);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading);\n          i0.ɵɵadvance();\n          i0.ɵɵclassProp(\"rotated\", ctx.isOpen);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isOpen);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isOpen);\n        }\n      },\n      dependencies: [i2.NgForOf, i2.NgIf, i3.DefaultValueAccessor, i3.NgControlStatus, i3.NgModel, i4.NbIconComponent],\n      styles: [\".household-binding-container[_ngcontent-%COMP%] {\\n  position: relative;\\n  width: 100%;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .spin[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_spin 1s linear infinite;\\n}\\n@keyframes _ngcontent-%COMP%_spin {\\n  from {\\n    transform: rotate(0deg);\\n  }\\n  to {\\n    transform: rotate(360deg);\\n  }\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%] {\\n  margin-bottom: 1rem;\\n  padding: 1rem;\\n  background-color: #f8f9fa;\\n  border: 1px solid #e9ecef;\\n  border-radius: 0.375rem;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%]   .selected-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 0.75rem;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%]   .selected-header[_ngcontent-%COMP%]   .selected-info[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.5rem;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%]   .selected-header[_ngcontent-%COMP%]   .selected-info[_ngcontent-%COMP%]   .selected-count[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  font-weight: 500;\\n  color: #495057;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%]   .selected-content[_ngcontent-%COMP%]   .building-group[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: flex-start;\\n  gap: 0.5rem;\\n  margin-bottom: 0.5rem;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%]   .selected-content[_ngcontent-%COMP%]   .building-group[_ngcontent-%COMP%]:last-child {\\n  margin-bottom: 0;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%]   .selected-content[_ngcontent-%COMP%]   .building-group[_ngcontent-%COMP%]   .building-label[_ngcontent-%COMP%] {\\n  min-width: 3rem;\\n  font-size: 0.75rem;\\n  color: #6c757d;\\n  font-weight: 500;\\n  margin-top: 0.25rem;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%]   .selected-content[_ngcontent-%COMP%]   .building-group[_ngcontent-%COMP%]   .households-tags[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-wrap: wrap;\\n  gap: 0.25rem;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%]   .selected-content[_ngcontent-%COMP%]   .building-group[_ngcontent-%COMP%]   .households-tags[_ngcontent-%COMP%]   .household-tag[_ngcontent-%COMP%] {\\n  display: inline-flex;\\n  align-items: center;\\n  gap: 0.25rem;\\n  padding: 0.25rem 0.5rem;\\n  background-color: #e3f2fd;\\n  color: #1976d2;\\n  font-size: 0.75rem;\\n  border-radius: 1rem;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%]   .selected-content[_ngcontent-%COMP%]   .building-group[_ngcontent-%COMP%]   .households-tags[_ngcontent-%COMP%]   .household-tag[_ngcontent-%COMP%]   .remove-btn[_ngcontent-%COMP%] {\\n  background: none;\\n  border: none;\\n  padding: 0;\\n  margin: 0;\\n  cursor: pointer;\\n  color: #1976d2;\\n  border-radius: 50%;\\n  width: 1rem;\\n  height: 1rem;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%]   .selected-content[_ngcontent-%COMP%]   .building-group[_ngcontent-%COMP%]   .households-tags[_ngcontent-%COMP%]   .household-tag[_ngcontent-%COMP%]   .remove-btn[_ngcontent-%COMP%]:hover {\\n  background-color: #bbdefb;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%]   .selected-content[_ngcontent-%COMP%]   .building-group[_ngcontent-%COMP%]   .households-tags[_ngcontent-%COMP%]   .household-tag[_ngcontent-%COMP%]   .remove-btn[_ngcontent-%COMP%]:disabled {\\n  opacity: 0.5;\\n  cursor: not-allowed;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%]   .selected-content[_ngcontent-%COMP%]   .building-group[_ngcontent-%COMP%]   .households-tags[_ngcontent-%COMP%]   .household-tag[_ngcontent-%COMP%]   .remove-btn[_ngcontent-%COMP%]   nb-icon[_ngcontent-%COMP%] {\\n  font-size: 0.75rem;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%] {\\n  position: relative;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .selector-button[_ngcontent-%COMP%] {\\n  width: 100%;\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between;\\n  padding: 0.5rem 0.75rem;\\n  border: 1px solid #ced4da;\\n  border-radius: 0.375rem;\\n  background-color: #fff;\\n  cursor: pointer;\\n  transition: all 0.15s ease-in-out;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .selector-button[_ngcontent-%COMP%]:hover:not(.disabled) {\\n  background-color: #f8f9fa;\\n  border-color: #adb5bd;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .selector-button[_ngcontent-%COMP%]:focus {\\n  outline: none;\\n  border-color: #80bdff;\\n  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .selector-button.disabled[_ngcontent-%COMP%] {\\n  opacity: 0.65;\\n  cursor: not-allowed;\\n  background-color: #e9ecef;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .selector-button[_ngcontent-%COMP%]   .selector-text[_ngcontent-%COMP%] {\\n  color: #495057;\\n  font-size: 0.875rem;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .selector-button[_ngcontent-%COMP%]   .chevron-icon[_ngcontent-%COMP%] {\\n  color: #6c757d;\\n  transition: transform 0.15s ease-in-out;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .selector-button[_ngcontent-%COMP%]   .chevron-icon.rotated[_ngcontent-%COMP%] {\\n  transform: rotate(180deg);\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: calc(100% + 0.25rem);\\n  left: 0;\\n  right: 0;\\n  z-index: 1050;\\n  background-color: #fff;\\n  border: 1px solid #ced4da;\\n  border-radius: 0.375rem;\\n  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);\\n  max-height: 24rem;\\n  overflow: hidden;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  height: 20rem;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .buildings-sidebar[_ngcontent-%COMP%] {\\n  width: 33.333%;\\n  border-right: 1px solid #e9ecef;\\n  background-color: #f8f9fa;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .buildings-sidebar[_ngcontent-%COMP%]   .sidebar-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.5rem;\\n  padding: 0.75rem;\\n  border-bottom: 1px solid #e9ecef;\\n  background-color: #e9ecef;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .buildings-sidebar[_ngcontent-%COMP%]   .sidebar-header[_ngcontent-%COMP%]   .sidebar-title[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  font-weight: 500;\\n  color: #495057;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .buildings-sidebar[_ngcontent-%COMP%]   .buildings-list[_ngcontent-%COMP%] {\\n  max-height: 17rem;\\n  overflow-y: auto;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .buildings-sidebar[_ngcontent-%COMP%]   .buildings-list[_ngcontent-%COMP%]   .building-item[_ngcontent-%COMP%] {\\n  width: 100%;\\n  text-align: left;\\n  padding: 0.75rem;\\n  border: none;\\n  background: none;\\n  cursor: pointer;\\n  transition: background-color 0.15s ease-in-out;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .buildings-sidebar[_ngcontent-%COMP%]   .buildings-list[_ngcontent-%COMP%]   .building-item[_ngcontent-%COMP%]:hover {\\n  background-color: #e3f2fd;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .buildings-sidebar[_ngcontent-%COMP%]   .buildings-list[_ngcontent-%COMP%]   .building-item.active[_ngcontent-%COMP%] {\\n  background-color: #e3f2fd;\\n  color: #1976d2;\\n  font-weight: 500;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .buildings-sidebar[_ngcontent-%COMP%]   .buildings-list[_ngcontent-%COMP%]   .building-item[_ngcontent-%COMP%]   .building-info[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .buildings-sidebar[_ngcontent-%COMP%]   .buildings-list[_ngcontent-%COMP%]   .building-item[_ngcontent-%COMP%]   .building-info[_ngcontent-%COMP%]   .building-name[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .buildings-sidebar[_ngcontent-%COMP%]   .buildings-list[_ngcontent-%COMP%]   .building-item[_ngcontent-%COMP%]   .building-info[_ngcontent-%COMP%]   .building-count[_ngcontent-%COMP%] {\\n  font-size: 0.75rem;\\n  color: #6c757d;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .households-main[_ngcontent-%COMP%] {\\n  width: 66.667%;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .households-main[_ngcontent-%COMP%]   .main-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  padding: 0.75rem;\\n  border-bottom: 1px solid #e9ecef;\\n  background-color: #e9ecef;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .households-main[_ngcontent-%COMP%]   .main-header[_ngcontent-%COMP%]   .header-left[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.5rem;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .households-main[_ngcontent-%COMP%]   .main-header[_ngcontent-%COMP%]   .header-left[_ngcontent-%COMP%]   .main-title[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  font-weight: 500;\\n  color: #495057;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .households-main[_ngcontent-%COMP%]   .main-header[_ngcontent-%COMP%]   .header-left[_ngcontent-%COMP%]   .main-title[_ngcontent-%COMP%]   .building-indicator[_ngcontent-%COMP%] {\\n  color: #1976d2;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .households-main[_ngcontent-%COMP%]   .main-header[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 0.25rem;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .households-main[_ngcontent-%COMP%]   .search-box[_ngcontent-%COMP%] {\\n  padding: 0.5rem;\\n  border-bottom: 1px solid #e9ecef;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .households-main[_ngcontent-%COMP%]   .households-grid[_ngcontent-%COMP%] {\\n  padding: 0.5rem;\\n  max-height: 14rem;\\n  overflow-y: auto;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .households-main[_ngcontent-%COMP%]   .households-grid[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  padding: 2rem;\\n  color: #6c757d;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .households-main[_ngcontent-%COMP%]   .households-grid[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%]   .empty-icon[_ngcontent-%COMP%] {\\n  font-size: 2rem;\\n  margin-bottom: 0.5rem;\\n  color: #adb5bd;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .households-main[_ngcontent-%COMP%]   .households-grid[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%]   .empty-text[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  margin: 0;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .households-main[_ngcontent-%COMP%]   .households-grid[_ngcontent-%COMP%]   .grid-container[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(3, 1fr);\\n  gap: 0.25rem;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .households-main[_ngcontent-%COMP%]   .households-grid[_ngcontent-%COMP%]   .grid-container[_ngcontent-%COMP%]   .household-button[_ngcontent-%COMP%] {\\n  padding: 0.5rem;\\n  border: 1px solid #ced4da;\\n  border-radius: 0.25rem;\\n  background-color: #fff;\\n  cursor: pointer;\\n  transition: all 0.15s ease-in-out;\\n  text-align: center;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .households-main[_ngcontent-%COMP%]   .households-grid[_ngcontent-%COMP%]   .grid-container[_ngcontent-%COMP%]   .household-button[_ngcontent-%COMP%]:hover:not(.disabled) {\\n  background-color: #f8f9fa;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .households-main[_ngcontent-%COMP%]   .households-grid[_ngcontent-%COMP%]   .grid-container[_ngcontent-%COMP%]   .household-button.selected[_ngcontent-%COMP%] {\\n  background-color: #e3f2fd;\\n  border-color: #1976d2;\\n  color: #1976d2;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .households-main[_ngcontent-%COMP%]   .households-grid[_ngcontent-%COMP%]   .grid-container[_ngcontent-%COMP%]   .household-button.disabled[_ngcontent-%COMP%] {\\n  background-color: #f8f9fa;\\n  color: #6c757d;\\n  cursor: not-allowed;\\n  opacity: 0.65;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .households-main[_ngcontent-%COMP%]   .households-grid[_ngcontent-%COMP%]   .grid-container[_ngcontent-%COMP%]   .household-button[_ngcontent-%COMP%]   .household-code[_ngcontent-%COMP%] {\\n  display: block;\\n  font-size: 0.875rem;\\n  font-weight: 500;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .households-main[_ngcontent-%COMP%]   .households-grid[_ngcontent-%COMP%]   .grid-container[_ngcontent-%COMP%]   .household-button[_ngcontent-%COMP%]   .household-floor[_ngcontent-%COMP%] {\\n  display: block;\\n  font-size: 0.75rem;\\n  color: #6c757d;\\n  margin-top: 0.125rem;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-footer[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  padding: 0.75rem;\\n  border-top: 1px solid #e9ecef;\\n  background-color: #f8f9fa;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-footer[_ngcontent-%COMP%]   .footer-info[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 1rem;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-footer[_ngcontent-%COMP%]   .footer-info[_ngcontent-%COMP%]   .max-selections-text[_ngcontent-%COMP%], \\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-footer[_ngcontent-%COMP%]   .footer-info[_ngcontent-%COMP%]   .current-selections-text[_ngcontent-%COMP%] {\\n  font-size: 0.75rem;\\n  color: #6c757d;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .backdrop[_ngcontent-%COMP%] {\\n  position: fixed;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  z-index: 1040;\\n  background: transparent;\\n}\\n\\n@media (max-width: 768px) {\\n  .household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    height: auto;\\n    max-height: 20rem;\\n  }\\n  .household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .buildings-sidebar[_ngcontent-%COMP%] {\\n    width: 100%;\\n    max-height: 8rem;\\n    border-right: none;\\n    border-bottom: 1px solid #e9ecef;\\n  }\\n  .household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .buildings-sidebar[_ngcontent-%COMP%]   .buildings-list[_ngcontent-%COMP%] {\\n    max-height: 5rem;\\n  }\\n  .household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .households-main[_ngcontent-%COMP%] {\\n    width: 100%;\\n  }\\n  .household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .households-main[_ngcontent-%COMP%]   .households-grid[_ngcontent-%COMP%]   .grid-container[_ngcontent-%COMP%] {\\n    grid-template-columns: repeat(2, 1fr);\\n  }\\n}\\n@media (prefers-color-scheme: dark) {\\n  .household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%] {\\n    background-color: #343a40;\\n    border-color: #495057;\\n    color: #f8f9fa;\\n  }\\n  .household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%]   .selected-header[_ngcontent-%COMP%]   .selected-info[_ngcontent-%COMP%]   .selected-count[_ngcontent-%COMP%] {\\n    color: #f8f9fa;\\n  }\\n  .household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%]   .selected-content[_ngcontent-%COMP%]   .building-group[_ngcontent-%COMP%]   .building-label[_ngcontent-%COMP%] {\\n    color: #adb5bd;\\n  }\\n  .household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .selector-button[_ngcontent-%COMP%] {\\n    background-color: #495057;\\n    border-color: #6c757d;\\n    color: #f8f9fa;\\n  }\\n  .household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .selector-button[_ngcontent-%COMP%]:hover:not(.disabled) {\\n    background-color: #5a6268;\\n  }\\n  .household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .selector-button.disabled[_ngcontent-%COMP%] {\\n    background-color: #6c757d;\\n  }\\n  .household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%] {\\n    background-color: #495057;\\n    border-color: #6c757d;\\n  }\\n  .household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .buildings-sidebar[_ngcontent-%COMP%] {\\n    background-color: #343a40;\\n    border-color: #495057;\\n  }\\n  .household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .buildings-sidebar[_ngcontent-%COMP%]   .sidebar-header[_ngcontent-%COMP%] {\\n    background-color: #495057;\\n    border-color: #6c757d;\\n    color: #f8f9fa;\\n  }\\n  .household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .buildings-sidebar[_ngcontent-%COMP%]   .buildings-list[_ngcontent-%COMP%]   .building-item[_ngcontent-%COMP%] {\\n    color: #f8f9fa;\\n  }\\n  .household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .buildings-sidebar[_ngcontent-%COMP%]   .buildings-list[_ngcontent-%COMP%]   .building-item[_ngcontent-%COMP%]:hover {\\n    background-color: #495057;\\n  }\\n  .household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .buildings-sidebar[_ngcontent-%COMP%]   .buildings-list[_ngcontent-%COMP%]   .building-item.active[_ngcontent-%COMP%] {\\n    background-color: #0d6efd;\\n    color: #fff;\\n  }\\n  .household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .households-main[_ngcontent-%COMP%]   .main-header[_ngcontent-%COMP%] {\\n    background-color: #495057;\\n    border-color: #6c757d;\\n    color: #f8f9fa;\\n  }\\n  .household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .households-main[_ngcontent-%COMP%]   .search-box[_ngcontent-%COMP%] {\\n    border-color: #6c757d;\\n  }\\n  .household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .households-main[_ngcontent-%COMP%]   .households-grid[_ngcontent-%COMP%]   .grid-container[_ngcontent-%COMP%]   .household-button[_ngcontent-%COMP%] {\\n    background-color: #6c757d;\\n    border-color: #adb5bd;\\n    color: #f8f9fa;\\n  }\\n  .household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .households-main[_ngcontent-%COMP%]   .households-grid[_ngcontent-%COMP%]   .grid-container[_ngcontent-%COMP%]   .household-button[_ngcontent-%COMP%]:hover:not(.disabled) {\\n    background-color: #5a6268;\\n  }\\n  .household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .households-main[_ngcontent-%COMP%]   .households-grid[_ngcontent-%COMP%]   .grid-container[_ngcontent-%COMP%]   .household-button.selected[_ngcontent-%COMP%] {\\n    background-color: #0d6efd;\\n    border-color: #0d6efd;\\n    color: #fff;\\n  }\\n  .household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-footer[_ngcontent-%COMP%] {\\n    background-color: #343a40;\\n    border-color: #495057;\\n    color: #f8f9fa;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;base64,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 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["EventEmitter", "forwardRef", "NG_VALUE_ACCESSOR", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵlistener", "HouseholdBindingComponent_div_1_div_9_ng_container_1_span_4_Template_button_click_2_listener", "householdCode_r4", "ɵɵrestoreView", "_r3", "$implicit", "ctx_r1", "ɵɵnextContext", "ɵɵresetView", "onRemoveHousehold", "ɵɵelement", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate1", "ɵɵproperty", "disabled", "ɵɵelementContainerStart", "ɵɵtemplate", "HouseholdBindingComponent_div_1_div_9_ng_container_1_span_4_Template", "building_r5", "getBuildingSelectedHouseholds", "HouseholdBindingComponent_div_1_div_9_ng_container_1_Template", "hasBuildingSelected", "HouseholdBindingComponent_div_1_Template_button_click_6_listener", "_r1", "onClearAll", "HouseholdBindingComponent_div_1_div_9_Template", "getSelectedCount", "buildings", "placeholder", "HouseholdBindingComponent_div_8_ng_container_13_button_6_Template_button_click_0_listener", "building_r8", "_r7", "onBuildingSelect", "ɵɵstyleProp", "selectedBuilding", "ɵɵtextInterpolate", "getBuildingCount", "selectedF<PERSON>or", "HouseholdBindingComponent_div_8_ng_container_13_div_14_button_5_Template_button_click_0_listener", "_r10", "onUnselectAllBuilding", "HouseholdBindingComponent_div_8_ng_container_13_div_14_Template_button_click_1_listener", "_r9", "onSelectAllFiltered", "HouseholdBindingComponent_div_8_ng_container_13_div_14_Template_button_click_3_listener", "onSelectAllBuilding", "HouseholdBindingComponent_div_8_ng_container_13_div_14_button_5_Template", "canSelectMore", "isSomeBuildingSelected", "searchTerm", "ɵɵtwoWayListener", "HouseholdBindingComponent_div_8_ng_container_13_div_15_Template_input_ngModelChange_2_listener", "$event", "_r11", "ɵɵtwoWayBindingSet", "HouseholdBindingComponent_div_8_ng_container_13_div_15_Template_input_input_2_listener", "onSearchChange", "HouseholdBindingComponent_div_8_ng_container_13_div_15_div_4_Template", "ɵɵtwoWayProperty", "filteredHouseholds", "length", "HouseholdBindingComponent_div_8_ng_container_13_div_16_button_5_Template_button_click_0_listener", "_r12", "onFloorSelect", "HouseholdBindingComponent_div_8_ng_container_13_div_16_button_7_Template_button_click_0_listener", "floor_r14", "_r13", "ɵɵtextInterpolate2", "getFloorCount", "HouseholdBindingComponent_div_8_ng_container_13_div_16_button_5_Template", "HouseholdBindingComponent_div_8_ng_container_13_div_16_button_7_Template", "floors", "getHouseholdFloor", "householdCode_r16", "HouseholdBindingComponent_div_8_ng_container_13_div_19_button_1_Template_button_click_0_listener", "_r15", "onHouseholdToggle", "HouseholdBindingComponent_div_8_ng_container_13_div_19_button_1_span_3_Template", "HouseholdBindingComponent_div_8_ng_container_13_div_19_button_1_div_4_Template", "isHouseholdSelected", "isHouseholdExcluded", "isHouseholdDisabled", "HouseholdBindingComponent_div_8_ng_container_13_div_19_button_1_Template", "HouseholdBindingComponent_div_8_ng_container_13_button_6_Template", "HouseholdBindingComponent_div_8_ng_container_13_span_12_Template", "HouseholdBindingComponent_div_8_ng_container_13_span_13_Template", "HouseholdBindingComponent_div_8_ng_container_13_div_14_Template", "HouseholdBindingComponent_div_8_ng_container_13_div_15_Template", "HouseholdBindingComponent_div_8_ng_container_13_div_16_Template", "HouseholdBindingComponent_div_8_ng_container_13_div_18_Template", "HouseholdBindingComponent_div_8_ng_container_13_div_19_Template", "allowBatchSelect", "allowSearch", "maxSelections", "HouseholdBindingComponent_div_8_button_29_Template_button_click_0_listener", "_r17", "HouseholdBindingComponent_div_8_button_30_Template_button_click_0_listener", "_r18", "resetSearch", "HouseholdBindingComponent_div_8_div_12_Template", "HouseholdBindingComponent_div_8_ng_container_13_Template", "HouseholdBindingComponent_div_8_div_24_Template", "HouseholdBindingComponent_div_8_div_25_Template", "HouseholdBindingComponent_div_8_div_26_Template", "HouseholdBindingComponent_div_8_button_29_Template", "HouseholdBindingComponent_div_8_button_30_Template", "HouseholdBindingComponent_div_8_Template_button_click_32_listener", "_r6", "closeDropdown", "HouseholdBindingComponent_div_8_Template_button_click_34_listener", "isLoading", "selectedHouseholds", "HouseholdBindingComponent_div_9_Template_div_click_0_listener", "_r19", "HouseholdBindingComponent", "constructor", "cdr", "houseCustomService", "buildCaseId", "buildingData", "showSelectedArea", "excludedHouseholds", "selectionChange", "isOpen", "selectedByBuilding", "onChange", "value", "onTouched", "writeValue", "updateSelectedByBuilding", "registerOnChange", "fn", "registerOnTouched", "setDisabledState", "isDisabled", "ngOnInit", "initializeData", "ngOnChanges", "changes", "Object", "keys", "updateFilteredHouseholds", "console", "log", "loadBuildingDataFromApi", "generateMockData", "getDropDown", "subscribe", "next", "response", "convertApiResponseToBuildingData", "Entries", "detectChanges", "error", "entries", "for<PERSON>ach", "building", "houses", "map", "house", "code", "HouseName", "Building", "floor", "Floor", "houseId", "HouseId", "houseName", "isSelected", "grouped", "item", "find", "h", "push", "simpleMockData", "Array", "from", "_", "i", "String", "padStart", "codes", "Math", "parseInt", "slice", "updateFloorsForBuilding", "onBuildingClick", "households", "filter", "floorMatch", "searchMatch", "toLowerCase", "includes", "event", "target", "householdCode", "newSelection", "emitChanges", "currentCount", "maxAllowed", "Infinity", "remainingSlots", "unselectedFiltered", "toAdd", "buildingHouseholds", "unselectedBuilding", "selectedItems", "emit", "toggleDropdown", "isAllBuildingSelected", "every", "some", "getSelectedByBuilding", "floorSet", "Set", "household", "add", "sort", "a", "b", "numA", "replace", "numB", "ɵɵdirectiveInject", "ChangeDetectorRef", "i1", "HouseCustomService", "selectors", "inputs", "outputs", "features", "ɵɵProvidersFeature", "provide", "useExisting", "multi", "ɵɵNgOnChangesFeature", "decls", "vars", "consts", "template", "HouseholdBindingComponent_Template", "rf", "ctx", "HouseholdBindingComponent_div_1_Template", "HouseholdBindingComponent_Template_button_click_3_listener", "HouseholdBindingComponent_ng_container_5_Template", "HouseholdBindingComponent_ng_container_6_Template", "HouseholdBindingComponent_div_8_Template", "HouseholdBindingComponent_div_9_Template", "ɵɵclassProp"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\shared\\components\\household-binding\\household-binding.component.ts", "C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\shared\\components\\household-binding\\household-binding.component.html"], "sourcesContent": ["import { Component, Input, Output, EventEmitter, OnInit, OnChanges, SimpleChanges, forwardRef, ChangeDetectorRef } from '@angular/core';\r\nimport { ControlValueAccessor, NG_VALUE_ACCESSOR } from '@angular/forms';\r\nimport { HouseCustomService } from '../../../../services/api/services/HouseCustom.service';\r\nimport { HouseItem } from '../../../../services/api/models/house.model';\r\n\r\nexport interface HouseholdItem {\r\n  code: string;\r\n  building: string;\r\n  floor?: string;\r\n  houseId?: number;\r\n  houseName?: string;\r\n  isSelected?: boolean;\r\n  isDisabled?: boolean;\r\n}\r\n\r\nexport interface BuildingData {\r\n  [key: string]: HouseholdItem[];\r\n}\r\n\r\n// 簡化版本 - 使用字串陣列\r\nexport interface SimpleBuildingData {\r\n  [key: string]: string[];\r\n}\r\n\r\n@Component({\r\n  selector: 'app-household-binding',\r\n  templateUrl: './household-binding.component.html',\r\n  styleUrls: ['./household-binding.component.scss'],\r\n  providers: [\r\n    {\r\n      provide: NG_VALUE_ACCESSOR,\r\n      useExisting: forwardRef(() => HouseholdBindingComponent),\r\n      multi: true,\r\n    },\r\n  ],\r\n})\r\nexport class HouseholdBindingComponent implements OnInit, OnChanges, ControlValueAccessor {\r\n  @Input() placeholder: string = '請選擇戶別';\r\n  @Input() maxSelections: number | null = null;\r\n  @Input() disabled: boolean = false;\r\n  @Input() buildCaseId: number | null = null; // 新增：建案ID\r\n  @Input() buildingData: BuildingData = {};\r\n  @Input() showSelectedArea: boolean = true;\r\n  @Input() allowSearch: boolean = true;\r\n  @Input() allowBatchSelect: boolean = true;\r\n  @Input() excludedHouseholds: string[] = []; // 新增：排除的戶別（已被其他元件選擇）\r\n\r\n  @Output() selectionChange = new EventEmitter<HouseholdItem[]>();\r\n  isOpen = false;\r\n  selectedBuilding = '';\r\n  searchTerm = '';\r\n  selectedFloor = ''; // 新增：選中的樓層\r\n  selectedHouseholds: string[] = [];\r\n  buildings: string[] = [];\r\n  floors: string[] = []; // 新增：當前棟別的樓層列表\r\n  filteredHouseholds: string[] = [];  // 簡化為字串陣列\r\n  selectedByBuilding: { [building: string]: string[] } = {};\r\n  isLoading: boolean = false; // 新增：載入狀態\r\n\r\n  // ControlValueAccessor implementation\r\n  private onChange = (value: string[]) => { };\r\n  private onTouched = () => { };\r\n\r\n  constructor(\r\n    private cdr: ChangeDetectorRef,\r\n    private houseCustomService: HouseCustomService\r\n  ) { }\r\n\r\n  writeValue(value: string[]): void {\r\n    this.selectedHouseholds = value || [];\r\n    this.updateSelectedByBuilding();\r\n  }\r\n\r\n  registerOnChange(fn: (value: string[]) => void): void {\r\n    this.onChange = fn;\r\n  }\r\n\r\n  registerOnTouched(fn: () => void): void {\r\n    this.onTouched = fn;\r\n  }\r\n  setDisabledState(isDisabled: boolean): void {\r\n    this.disabled = isDisabled;\r\n  } ngOnInit() {\r\n    this.initializeData();\r\n  }\r\n\r\n  ngOnChanges(changes: SimpleChanges) {\r\n    if (changes['buildCaseId'] && this.buildCaseId) {\r\n      // 當建案ID變更時，重新載入資料\r\n      this.initializeData();\r\n    }\r\n    if (changes['buildingData']) {\r\n      this.buildings = Object.keys(this.buildingData);\r\n      this.updateFilteredHouseholds();\r\n      this.updateSelectedByBuilding();\r\n    }\r\n    if (changes['excludedHouseholds']) {\r\n      // 當排除列表變化時，重新更新顯示\r\n      this.updateFilteredHouseholds();\r\n      console.log('Excluded households updated:', this.excludedHouseholds);\r\n    }\r\n  }\r\n\r\n  private initializeData() {\r\n    if (this.buildCaseId) {\r\n      // 使用API載入資料\r\n      this.loadBuildingDataFromApi();\r\n    } else {\r\n      // 如果沒有提供建案ID且沒有提供 buildingData，使用 mock 資料\r\n      if (!this.buildingData || Object.keys(this.buildingData).length === 0) {\r\n        this.buildingData = this.generateMockData();\r\n      }\r\n      this.buildings = Object.keys(this.buildingData);\r\n      console.log('Component initialized with buildings:', this.buildings);\r\n      this.updateSelectedByBuilding();\r\n    }\r\n  }\r\n\r\n  private loadBuildingDataFromApi() {\r\n    if (!this.buildCaseId) return;\r\n\r\n    this.isLoading = true;\r\n    this.houseCustomService.getDropDown(this.buildCaseId).subscribe({\r\n      next: (response) => {\r\n        console.log('API response:', response);\r\n        this.buildingData = this.convertApiResponseToBuildingData(response.Entries);\r\n        this.buildings = Object.keys(this.buildingData);\r\n        this.updateSelectedByBuilding();\r\n        this.isLoading = false;\r\n        this.cdr.detectChanges();\r\n      },\r\n      error: (error) => {\r\n        console.error('Error loading building data:', error);\r\n        // 如果API載入失敗，使用mock資料作為備援\r\n        this.buildingData = this.generateMockData();\r\n        this.buildings = Object.keys(this.buildingData);\r\n        this.updateSelectedByBuilding();\r\n        this.isLoading = false;\r\n        this.cdr.detectChanges();\r\n      }\r\n    });\r\n  }\r\n\r\n  private convertApiResponseToBuildingData(entries: { [key: string]: HouseItem[] }): BuildingData {\r\n    const buildingData: BuildingData = {};\r\n\r\n    Object.entries(entries).forEach(([building, houses]) => {\r\n      buildingData[building] = houses.map(house => ({\r\n        code: house.HouseName,\r\n        building: house.Building,\r\n        floor: house.Floor,\r\n        houseId: house.HouseId,\r\n        houseName: house.HouseName,\r\n        isSelected: false,\r\n        isDisabled: false\r\n      }));\r\n    });\r\n\r\n    return buildingData;\r\n  }\r\n\r\n  private updateSelectedByBuilding() {\r\n    const grouped: { [building: string]: string[] } = {};\r\n\r\n    this.selectedHouseholds.forEach(code => {\r\n      for (const building of this.buildings) {\r\n        const item = this.buildingData[building]?.find(h => h.code === code);\r\n        if (item) {\r\n          if (!grouped[building]) grouped[building] = [];\r\n          grouped[building].push(code);\r\n          break;\r\n        }\r\n      }\r\n    });\r\n\r\n    this.selectedByBuilding = grouped;\r\n  }\r\n  private generateMockData(): BuildingData {\r\n    // 簡化版本 - 直接生成字串陣列\r\n    const simpleMockData = {\r\n      'A棟': Array.from({ length: 50 }, (_, i) => `A${String(i + 1).padStart(3, '0')}`),\r\n      'B棟': Array.from({ length: 40 }, (_, i) => `B${String(i + 1).padStart(3, '0')}`),\r\n      'C棟': Array.from({ length: 60 }, (_, i) => `C${String(i + 1).padStart(3, '0')}`),\r\n      'D棟': Array.from({ length: 35 }, (_, i) => `D${String(i + 1).padStart(3, '0')}`),\r\n      'E棟': Array.from({ length: 45 }, (_, i) => `E${String(i + 1).padStart(3, '0')}`)\r\n    };\r\n\r\n    // 轉換為 BuildingData 格式\r\n    const buildingData: BuildingData = {};\r\n    Object.entries(simpleMockData).forEach(([building, codes]) => {\r\n      buildingData[building] = codes.map(code => ({\r\n        code,\r\n        building,\r\n        floor: `${Math.floor((parseInt(code.slice(1)) - 1) / 4) + 1}F`,\r\n        isSelected: false,\r\n        isDisabled: false\r\n      }));\r\n    });\r\n\r\n    return buildingData;\r\n  }  onBuildingSelect(building: string) {\r\n    console.log('Building selected:', building);\r\n    this.selectedBuilding = building;\r\n    this.selectedFloor = ''; // 重置樓層選擇\r\n    this.searchTerm = '';\r\n    this.updateFloorsForBuilding(); // 更新樓層列表\r\n    this.updateFilteredHouseholds();\r\n    console.log('Filtered households count:', this.filteredHouseholds.length);\r\n    // 手動觸發變更偵測\r\n    this.cdr.detectChanges();\r\n  }\r\n\r\n  onBuildingClick(building: string) {\r\n    console.log('Building clicked (mousedown):', building);\r\n  }  updateFilteredHouseholds() {\r\n    console.log('Updating filtered households for building:', this.selectedBuilding, 'floor:', this.selectedFloor);\r\n    if (!this.selectedBuilding) {\r\n      this.filteredHouseholds = [];\r\n      return;\r\n    }\r\n\r\n    const households = this.buildingData[this.selectedBuilding] || [];\r\n    console.log('Available households for building:', households.length);\r\n\r\n    // 提取戶別代碼並進行搜尋和樓層過濾\r\n    this.filteredHouseholds = households\r\n      .filter(h => {\r\n        // 樓層篩選：如果有選擇樓層，只顯示該樓層的戶別\r\n        const floorMatch = !this.selectedFloor || h.floor === this.selectedFloor;\r\n        // 搜尋篩選：戶別代碼包含搜尋詞\r\n        const searchMatch = h.code.toLowerCase().includes(this.searchTerm.toLowerCase());\r\n        return floorMatch && searchMatch;\r\n      })\r\n      .map(h => h.code);\r\n\r\n    console.log('Filtered households result:', this.filteredHouseholds.length);\r\n  }\r\n\r\n  onSearchChange(event: any) {\r\n    this.searchTerm = event.target.value;\r\n    this.updateFilteredHouseholds();\r\n    console.log('Search term changed:', this.searchTerm);\r\n  }\r\n\r\n  resetSearch() {\r\n    this.searchTerm = '';\r\n    this.updateFilteredHouseholds();\r\n    console.log('Search reset');\r\n  }\r\n  onHouseholdToggle(householdCode: string) {\r\n    // 防止選擇已排除的戶別\r\n    if (this.isHouseholdExcluded(householdCode)) {\r\n      console.log(`戶別 ${householdCode} 已被其他元件選擇，無法重複選擇`);\r\n      return;\r\n    }\r\n\r\n    const isSelected = this.selectedHouseholds.includes(householdCode);\r\n    let newSelection: string[];\r\n\r\n    if (isSelected) {\r\n      newSelection = this.selectedHouseholds.filter(h => h !== householdCode);\r\n    } else {\r\n      if (this.maxSelections && this.selectedHouseholds.length >= this.maxSelections) {\r\n        console.log('已達到最大選擇數量');\r\n        return; // 達到最大選擇數量\r\n      }\r\n      newSelection = [...this.selectedHouseholds, householdCode];\r\n    }\r\n\r\n    this.selectedHouseholds = newSelection;\r\n    this.emitChanges();\r\n  }\r\n\r\n  onRemoveHousehold(householdCode: string) {\r\n    this.selectedHouseholds = this.selectedHouseholds.filter(h => h !== householdCode);\r\n    this.emitChanges();\r\n  } onSelectAllFiltered() {\r\n    if (!this.selectedBuilding || this.filteredHouseholds.length === 0) return;\r\n\r\n    // 計算可以新增的戶別數量\r\n    const currentCount = this.selectedHouseholds.length;\r\n    const maxAllowed = this.maxSelections || Infinity;\r\n    const remainingSlots = maxAllowed - currentCount;    // 取得尚未選擇且未被排除的過濾戶別\r\n    const unselectedFiltered = this.filteredHouseholds.filter(code =>\r\n      !this.selectedHouseholds.includes(code) && !this.isHouseholdExcluded(code)\r\n    );\r\n\r\n    // 根據剩餘空間決定要新增的戶別\r\n    const toAdd = unselectedFiltered.slice(0, remainingSlots);\r\n\r\n    if (toAdd.length > 0) {\r\n      this.selectedHouseholds = [...this.selectedHouseholds, ...toAdd];\r\n      this.emitChanges();\r\n      console.log(`全選當前: 新增了 ${toAdd.length} 個戶別`);\r\n    }\r\n  }\r\n\r\n  onSelectAllBuilding() {\r\n    if (!this.selectedBuilding) return;\r\n\r\n    // 取得當前棟別的所有戶別\r\n    const buildingHouseholds = this.buildingData[this.selectedBuilding]?.map(h => h.code) || [];\r\n\r\n    // 計算可以新增的戶別數量\r\n    const currentCount = this.selectedHouseholds.length;\r\n    const maxAllowed = this.maxSelections || Infinity;\r\n    const remainingSlots = maxAllowed - currentCount;    // 取得尚未選擇且未被排除的棟別戶別\r\n    const unselectedBuilding = buildingHouseholds.filter(code =>\r\n      !this.selectedHouseholds.includes(code) && !this.isHouseholdExcluded(code)\r\n    );\r\n\r\n    // 根據剩餘空間決定要新增的戶別\r\n    const toAdd = unselectedBuilding.slice(0, remainingSlots);\r\n\r\n    if (toAdd.length > 0) {\r\n      this.selectedHouseholds = [...this.selectedHouseholds, ...toAdd];\r\n      this.emitChanges();\r\n      console.log(`全選棟別: 新增了 ${toAdd.length} 個戶別`);\r\n    }\r\n  }\r\n  onUnselectAllBuilding() {\r\n    if (!this.selectedBuilding) return;\r\n\r\n    const buildingHouseholds = this.buildingData[this.selectedBuilding]?.map(h => h.code) || [];\r\n    this.selectedHouseholds = this.selectedHouseholds.filter(h => !buildingHouseholds.includes(h));\r\n    this.emitChanges();\r\n  }\r\n  onClearAll() {\r\n    this.selectedHouseholds = [];\r\n    this.emitChanges();\r\n  }\r\n\r\n  private emitChanges() {\r\n    this.updateSelectedByBuilding();\r\n    this.onChange([...this.selectedHouseholds]);\r\n    this.onTouched();\r\n\r\n    const selectedItems = this.selectedHouseholds.map(code => {\r\n      for (const building of this.buildings) {\r\n        const item = this.buildingData[building]?.find(h => h.code === code);\r\n        if (item) return item;\r\n      }\r\n      return null;\r\n    }).filter(item => item !== null) as HouseholdItem[];\r\n\r\n    this.selectionChange.emit(selectedItems);\r\n  }\r\n  toggleDropdown() {\r\n    if (!this.disabled) {\r\n      this.isOpen = !this.isOpen;\r\n      console.log('Dropdown toggled. isOpen:', this.isOpen);\r\n      console.log('Buildings available:', this.buildings);\r\n    }\r\n  }\r\n\r\n  closeDropdown() {\r\n    this.isOpen = false;\r\n  }\r\n\r\n  isHouseholdSelected(householdCode: string): boolean {\r\n    return this.selectedHouseholds.includes(householdCode);\r\n  }\r\n\r\n  isHouseholdExcluded(householdCode: string): boolean {\r\n    return this.excludedHouseholds.includes(householdCode);\r\n  }\r\n\r\n  isHouseholdDisabled(householdCode: string): boolean {\r\n    return this.isHouseholdExcluded(householdCode) ||\r\n      (!this.canSelectMore() && !this.isHouseholdSelected(householdCode));\r\n  }\r\n\r\n  canSelectMore(): boolean {\r\n    return !this.maxSelections || this.selectedHouseholds.length < this.maxSelections;\r\n  }\r\n\r\n  isAllBuildingSelected(): boolean {\r\n    if (!this.selectedBuilding) return false;\r\n    const buildingHouseholds = this.buildingData[this.selectedBuilding]\r\n      .filter(h => !h.isDisabled)\r\n      .map(h => h.code);\r\n    return buildingHouseholds.length > 0 &&\r\n      buildingHouseholds.every(code => this.selectedHouseholds.includes(code));\r\n  }\r\n  isSomeBuildingSelected(): boolean {\r\n    if (!this.selectedBuilding) return false;\r\n    const buildingHouseholds = this.buildingData[this.selectedBuilding]?.map(h => h.code) || [];\r\n    return buildingHouseholds.some(code => this.selectedHouseholds.includes(code));\r\n  }\r\n  getSelectedByBuilding(): { [building: string]: string[] } {\r\n    return this.selectedByBuilding;\r\n  }\r\n\r\n  getBuildingCount(building: string): number {\r\n    return this.buildingData[building]?.length || 0;\r\n  }\r\n\r\n  getSelectedCount(): number {\r\n    return this.selectedHouseholds.length;\r\n  }\r\n\r\n  // 輔助方法：安全地獲取建築物的已選戶別\r\n  getBuildingSelectedHouseholds(building: string): string[] {\r\n    return this.selectedByBuilding[building] || [];\r\n  }\r\n\r\n  // 輔助方法：檢查建築物是否有已選戶別\r\n  hasBuildingSelected(building: string): boolean {\r\n    return !!(this.selectedByBuilding[building] && this.selectedByBuilding[building].length > 0);\r\n  }\r\n\r\n  // 新增：更新當前棟別的樓層列表\r\n  private updateFloorsForBuilding() {\r\n    if (!this.selectedBuilding) {\r\n      this.floors = [];\r\n      return;\r\n    }\r\n\r\n    const households = this.buildingData[this.selectedBuilding] || [];\r\n    const floorSet = new Set<string>();\r\n    \r\n    households.forEach(household => {\r\n      if (household.floor) {\r\n        floorSet.add(household.floor);\r\n      }\r\n    });\r\n\r\n    // 對樓層進行自然排序\r\n    this.floors = Array.from(floorSet).sort((a, b) => {\r\n      const numA = parseInt(a.replace(/[^0-9]/g, ''));\r\n      const numB = parseInt(b.replace(/[^0-9]/g, ''));\r\n      return numA - numB;\r\n    });\r\n\r\n    console.log('Updated floors for building:', this.selectedBuilding, this.floors);\r\n  }\r\n\r\n  // 新增：樓層選擇處理\r\n  onFloorSelect(floor: string) {\r\n    console.log('Floor selected:', floor);\r\n    this.selectedFloor = this.selectedFloor === floor ? '' : floor; // 切換選擇\r\n    this.updateFilteredHouseholds();\r\n    this.cdr.detectChanges();\r\n  }\r\n\r\n  // 新增：取得當前棟別的樓層計數\r\n  getFloorCount(floor: string): number {\r\n    if (!this.selectedBuilding) return 0;\r\n    const households = this.buildingData[this.selectedBuilding] || [];\r\n    return households.filter(h => h.floor === floor).length;\r\n  }\r\n\r\n  // 新增：取得戶別的樓層資訊\r\n  getHouseholdFloor(householdCode: string): string {\r\n    if (!this.selectedBuilding) return '';\r\n    const households = this.buildingData[this.selectedBuilding] || [];\r\n    const household = households.find(h => h.code === householdCode);\r\n    return household?.floor || '';\r\n  }\r\n}\r\n", "<div class=\"household-binding-container\">\r\n  <!-- 已選擇戶別顯示區域 -->\r\n  <div *ngIf=\"showSelectedArea && selectedHouseholds.length > 0\" class=\"selected-households-area\">\r\n    <div class=\"selected-header\">\r\n      <div class=\"selected-info\">\r\n        <nb-icon icon=\"people-outline\" class=\"text-primary\"></nb-icon>\r\n        <span class=\"selected-count\">已選擇戶別 ({{getSelectedCount()}})</span>\r\n      </div>\r\n      <button type=\"button\" class=\"btn btn-outline-danger btn-sm\" [disabled]=\"disabled\" (click)=\"onClearAll()\">\r\n        清空全部\r\n      </button>\r\n    </div>\r\n    <div class=\"selected-content\">\r\n      <div *ngFor=\"let building of buildings\" class=\"building-group\">\r\n        <ng-container *ngIf=\"hasBuildingSelected(building)\">\r\n          <div class=\"building-label\">{{building}}:</div>\r\n          <div class=\"households-tags\">\r\n            <span *ngFor=\"let householdCode of getBuildingSelectedHouseholds(building)\" class=\"household-tag\">\r\n              {{householdCode}}\r\n              <button type=\"button\" class=\"remove-btn\" [disabled]=\"disabled\" (click)=\"onRemoveHousehold(householdCode)\">\r\n                <nb-icon icon=\"close-outline\"></nb-icon>\r\n              </button>\r\n            </span>\r\n          </div>\r\n        </ng-container>\r\n      </div>\r\n    </div>\r\n  </div> <!-- 選擇器 -->\r\n  <div class=\"selector-container\" style=\"position: relative;\">\r\n    <button type=\"button\" class=\"selector-button\" [class.disabled]=\"disabled || isLoading\"\r\n      [disabled]=\"disabled || isLoading\" (click)=\"toggleDropdown()\"\r\n      style=\"width: 100%; display: flex; align-items: center; justify-content: space-between; padding: 0.5rem 0.75rem; border: 1px solid #ced4da; border-radius: 0.375rem; background-color: #fff; cursor: pointer;\">\r\n      <span class=\"selector-text\">\r\n        <ng-container *ngIf=\"isLoading\">\r\n          <nb-icon icon=\"loader-outline\" class=\"spin\"></nb-icon>\r\n          載入中...\r\n        </ng-container>\r\n        <ng-container *ngIf=\"!isLoading\">\r\n          {{getSelectedCount() > 0 ? '已選擇 ' + getSelectedCount() + ' 個戶別' : placeholder}}\r\n        </ng-container>\r\n      </span>\r\n      <nb-icon icon=\"chevron-down-outline\" [class.rotated]=\"isOpen\" class=\"chevron-icon\">\r\n      </nb-icon>\r\n    </button> <!-- 下拉選單 --> <!-- 下拉選單 - 完全簡化版本 --> <!-- 下拉選單 -->\r\n    <div *ngIf=\"isOpen\"\r\n      style=\"position: absolute; top: calc(100% + 4px); left: 0; right: 0; z-index: 99999; background: white; border: 1px solid #ced4da; border-radius: 8px; box-shadow: 0 4px 12px rgba(0,0,0,0.15); padding: 0; margin: 0; max-height: min(80vh, 600px); overflow: hidden;\">\r\n\r\n      <!-- 標題區域 -->\r\n      <div style=\"padding: 12px 16px; border-bottom: 1px solid #e9ecef; background-color: #f8f9fa;\">\r\n        <div style=\"display: flex; align-items: center; justify-content: space-between;\">\r\n          <div style=\"display: flex; align-items: center; gap: 8px;\">\r\n            <nb-icon icon=\"home-outline\" style=\"color: #007bff;\"></nb-icon>\r\n            <span style=\"font-weight: 500; color: #495057;\">選擇戶別</span>\r\n            <span style=\"font-size: 0.875rem; color: #6c757d;\">({{buildings.length}} 個棟別)</span>\r\n          </div>\r\n          <span style=\"font-size: 0.875rem; color: #6c757d;\">已選擇: {{getSelectedCount()}}</span>\r\n        </div>\r\n      </div> <!-- 主要內容區域 -->\r\n      <div style=\"display: flex; height: calc(100% - 160px); min-height: 300px; max-height: 400px;\">\r\n\r\n        <!-- 載入狀態 -->\r\n        <div *ngIf=\"isLoading\"\r\n          style=\"width: 100%; display: flex; align-items: center; justify-content: center; padding: 40px;\">\r\n          <div style=\"text-align: center; color: #6c757d;\">\r\n            <nb-icon icon=\"loader-outline\" class=\"spin\" style=\"font-size: 2rem; margin-bottom: 8px;\"></nb-icon>\r\n            <p style=\"margin: 0; font-size: 0.875rem;\">載入戶別資料中...</p>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 正常內容 -->\r\n        <ng-container *ngIf=\"!isLoading\">\r\n          <!-- 棟別選擇側邊欄 -->\r\n          <div style=\"width: 40%; border-right: 1px solid #e9ecef; background-color: #f8f9fa;\">\r\n            <div style=\"padding: 12px 16px; border-bottom: 1px solid #e9ecef;\">\r\n              <h6 style=\"margin: 0; font-size: 0.875rem; font-weight: 500; color: #495057;\">棟別列表</h6>\r\n            </div>\r\n            <div style=\"max-height: calc(100% - 52px); overflow-y: auto;\">\r\n              <button *ngFor=\"let building of buildings\" type=\"button\" (click)=\"onBuildingSelect(building)\"\r\n                [style.background-color]=\"selectedBuilding === building ? '#e3f2fd' : 'transparent'\"\r\n                [style.border-left]=\"selectedBuilding === building ? '3px solid #007bff' : '3px solid transparent'\"\r\n                style=\"width: 100%; text-align: left; padding: 12px 16px; border: none; cursor: pointer; transition: all 0.15s ease; display: flex; align-items: center; justify-content: space-between;\">\r\n                <span style=\"font-weight: 500; color: #495057;\">{{building}}</span>\r\n                <span\r\n                  style=\"font-size: 0.75rem; color: #6c757d; background-color: #e9ecef; padding: 2px 6px; border-radius: 10px;\">{{getBuildingCount(building)}}戶</span>\r\n              </button>\r\n            </div>\r\n          </div><!-- 戶別選擇主區域 -->\r\n          <div style=\"flex: 1; display: flex; flex-direction: column;\">\r\n            <div style=\"padding: 12px 16px; border-bottom: 1px solid #e9ecef;\">\r\n              <div style=\"display: flex; align-items: center; justify-content: space-between; margin-bottom: 8px;\">                <h6 style=\"margin: 0; font-size: 0.875rem; font-weight: 500; color: #495057;\">\r\n                  戶別選擇\r\n                  <span *ngIf=\"selectedBuilding\" style=\"color: #007bff;\">({{selectedBuilding}})</span>\r\n                  <span *ngIf=\"selectedFloor\" style=\"color: #28a745; font-size: 0.75rem;\"> - {{selectedFloor}}</span>\r\n                </h6>\r\n                <div *ngIf=\"allowBatchSelect && selectedBuilding && filteredHouseholds.length > 0\"\r\n                  style=\"display: flex; gap: 4px;\">\r\n                  <button type=\"button\" [disabled]=\"!canSelectMore()\" (click)=\"onSelectAllFiltered()\"\r\n                    [style.opacity]=\"canSelectMore() ? '1' : '0.5'\"\r\n                    style=\"padding: 4px 8px; font-size: 0.75rem; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer;\">\r\n                    全選當前\r\n                  </button>\r\n                  <button type=\"button\" [disabled]=\"!canSelectMore()\" (click)=\"onSelectAllBuilding()\"\r\n                    [style.opacity]=\"canSelectMore() ? '1' : '0.5'\"\r\n                    style=\"padding: 4px 8px; font-size: 0.75rem; background: #28a745; color: white; border: none; border-radius: 4px; cursor: pointer;\">\r\n                    全選{{selectedBuilding}}\r\n                  </button>\r\n                  <button type=\"button\" *ngIf=\"isSomeBuildingSelected()\" (click)=\"onUnselectAllBuilding()\"\r\n                    style=\"padding: 4px 8px; font-size: 0.75rem; background: #6c757d; color: white; border: none; border-radius: 4px; cursor: pointer;\">\r\n                    清除\r\n                  </button>\r\n                </div>\r\n              </div>              <!-- 搜尋框 -->\r\n              <div *ngIf=\"allowSearch && selectedBuilding\" style=\"margin-top: 8px;\">\r\n                <div style=\"position: relative;\">\r\n                  <input type=\"text\" [(ngModel)]=\"searchTerm\" (input)=\"onSearchChange($event)\" placeholder=\"搜尋戶別代碼...\"\r\n                    style=\"width: 100%; padding: 6px 32px 6px 12px; border: 1px solid #ced4da; border-radius: 4px; font-size: 0.875rem; outline: none;\">\r\n                  <nb-icon icon=\"search-outline\"\r\n                    style=\"position: absolute; right: 10px; top: 50%; transform: translateY(-50%); color: #6c757d; font-size: 0.875rem;\"></nb-icon>\r\n                </div>\r\n                <div *ngIf=\"searchTerm && filteredHouseholds.length === 0\"\r\n                  style=\"font-size: 0.75rem; color: #dc3545; margin-top: 4px;\">\r\n                  找不到符合 \"{{searchTerm}}\" 的戶別\r\n                </div>\r\n              </div>\r\n\r\n              <!-- 樓層篩選器 -->\r\n              <div *ngIf=\"selectedBuilding && floors.length > 1\" style=\"margin-top: 12px;\">\r\n                <div style=\"display: flex; align-items: center; gap: 8px; margin-bottom: 8px;\">\r\n                  <nb-icon icon=\"layers-outline\" style=\"color: #6c757d; font-size: 0.875rem;\"></nb-icon>\r\n                  <span style=\"font-size: 0.875rem; font-weight: 500; color: #495057;\">樓層篩選:</span>\r\n                  <button type=\"button\" \r\n                    *ngIf=\"selectedFloor\" \r\n                    (click)=\"onFloorSelect('')\"\r\n                    style=\"font-size: 0.75rem; color: #007bff; background: none; border: none; text-decoration: underline; cursor: pointer;\">\r\n                    清除篩選\r\n                  </button>\r\n                </div>\r\n                <div style=\"display: flex; flex-wrap: wrap; gap: 4px; max-height: 100px; overflow-y: auto;\">\r\n                  <button type=\"button\" \r\n                    *ngFor=\"let floor of floors\" \r\n                    (click)=\"onFloorSelect(floor)\"\r\n                    [style.background-color]=\"selectedFloor === floor ? '#007bff' : '#f8f9fa'\"\r\n                    [style.color]=\"selectedFloor === floor ? '#fff' : '#495057'\"\r\n                    [style.border-color]=\"selectedFloor === floor ? '#007bff' : '#ced4da'\"\r\n                    style=\"padding: 4px 8px; border: 1px solid; border-radius: 4px; font-size: 0.75rem; cursor: pointer; transition: all 0.15s ease; white-space: nowrap;\">\r\n                    {{floor}} ({{getFloorCount(floor)}})\r\n                  </button>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            <!-- 戶別網格或空狀態 -->\r\n            <div style=\"flex: 1; padding: 16px; overflow-y: auto;\">\r\n              <div *ngIf=\"!selectedBuilding\" style=\"text-align: center; padding: 40px 20px; color: #6c757d;\">\r\n                <nb-icon icon=\"home-outline\" style=\"font-size: 2rem; margin-bottom: 8px; opacity: 0.5;\"></nb-icon>\r\n                <p style=\"margin: 0; font-size: 0.875rem;\">請先選擇棟別</p>\r\n              </div>              <div *ngIf=\"selectedBuilding && filteredHouseholds.length > 0\"\r\n                style=\"display: grid; grid-template-columns: repeat(auto-fill, minmax(90px, 1fr)); gap: 8px;\">\r\n                <button *ngFor=\"let householdCode of filteredHouseholds\" type=\"button\"\r\n                  (click)=\"onHouseholdToggle(householdCode)\" [disabled]=\"isHouseholdDisabled(householdCode)\"\r\n                  [style.background-color]=\"isHouseholdSelected(householdCode) ? '#007bff' : (isHouseholdExcluded(householdCode) ? '#f8f9fa' : '#fff')\"\r\n                  [style.color]=\"isHouseholdSelected(householdCode) ? '#fff' : (isHouseholdExcluded(householdCode) ? '#6c757d' : '#495057')\"\r\n                  [style.border-color]=\"isHouseholdSelected(householdCode) ? '#007bff' : (isHouseholdExcluded(householdCode) ? '#dee2e6' : '#ced4da')\"\r\n                  [style.opacity]=\"isHouseholdDisabled(householdCode) ? '0.6' : '1'\"\r\n                  [style.cursor]=\"isHouseholdDisabled(householdCode) ? 'not-allowed' : 'pointer'\"\r\n                  style=\"padding: 6px 4px; border: 1px solid; border-radius: 4px; transition: all 0.15s ease; font-size: 0.75rem; text-align: center; min-height: 40px; position: relative; display: flex; flex-direction: column; justify-content: center;\">\r\n                  <span [style.text-decoration]=\"isHouseholdExcluded(householdCode) ? 'line-through' : 'none'\"\r\n                    style=\"font-weight: 500; line-height: 1.2;\">\r\n                    {{householdCode}}\r\n                  </span>\r\n                  <span *ngIf=\"getHouseholdFloor(householdCode)\" \r\n                    style=\"font-size: 0.6rem; opacity: 0.8; margin-top: 2px;\">\r\n                    {{getHouseholdFloor(householdCode)}}\r\n                  </span>\r\n                  <div *ngIf=\"isHouseholdExcluded(householdCode)\"\r\n                    style=\"position: absolute; top: -8px; right: -8px; background: #dc3545; color: white; border-radius: 50%; width: 16px; height: 16px; font-size: 10px; display: flex; align-items: center; justify-content: center;\">\r\n                    ✕\r\n                  </div>\r\n                </button>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </ng-container>\r\n      </div> <!-- 優化的底部操作區 -->\r\n      <div style=\"padding: 16px; border-top: 1px solid #e9ecef; background-color: #f8f9fa;\">\r\n        <!-- 統計資訊行 -->\r\n        <div style=\"display: flex; align-items: center; justify-content: space-between; margin-bottom: 12px;\">\r\n          <div style=\"display: flex; align-items: center; gap: 16px; font-size: 0.875rem; color: #495057;\">\r\n            <div style=\"display: flex; align-items: center; gap: 4px;\">\r\n              <nb-icon icon=\"checkmark-circle-outline\" style=\"color: #28a745;\"></nb-icon>\r\n              <span>已選擇: <strong>{{getSelectedCount()}}</strong> 個戶別</span>\r\n            </div>\r\n            <div *ngIf=\"maxSelections\" style=\"display: flex; align-items: center; gap: 4px;\">\r\n              <nb-icon icon=\"alert-circle-outline\" style=\"color: #ffc107;\"></nb-icon>\r\n              <span>限制: 最多 <strong>{{maxSelections}}</strong> 個</span>\r\n            </div>\r\n            <div *ngIf=\"selectedBuilding\" style=\"display: flex; align-items: center; gap: 4px;\">\r\n              <nb-icon icon=\"home-outline\" style=\"color: #007bff;\"></nb-icon>\r\n              <span>當前棟別: <strong>{{selectedBuilding}}</strong></span>\r\n            </div>\r\n          </div>\r\n          <div *ngIf=\"searchTerm\"\r\n            style=\"font-size: 0.75rem; color: #6c757d; background-color: #e9ecef; padding: 4px 8px; border-radius: 12px;\">\r\n            搜尋: \"{{searchTerm}}\" ({{filteredHouseholds.length}} 個結果)\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 操作按鈕行 -->\r\n        <div style=\"display: flex; align-items: center; justify-content: space-between; gap: 8px;\">\r\n          <div style=\"display: flex; gap: 8px;\">\r\n            <button type=\"button\" *ngIf=\"selectedHouseholds.length > 0\" (click)=\"onClearAll()\"\r\n              style=\"padding: 8px 12px; background: #dc3545; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 0.875rem; display: flex; align-items: center; gap: 4px;\">\r\n              <nb-icon icon=\"trash-2-outline\"></nb-icon>\r\n              清空全部\r\n            </button>\r\n            <button type=\"button\" *ngIf=\"allowSearch && searchTerm\" (click)=\"resetSearch()\"\r\n              style=\"padding: 8px 12px; background: #6c757d; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 0.875rem; display: flex; align-items: center; gap: 4px;\">\r\n              <nb-icon icon=\"refresh-outline\"></nb-icon>\r\n              重置搜尋\r\n            </button>\r\n          </div>\r\n\r\n          <div style=\"display: flex; gap: 8px;\">\r\n            <button type=\"button\" (click)=\"closeDropdown()\"\r\n              style=\"padding: 8px 16px; background: #6c757d; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 0.875rem;\">\r\n              取消\r\n            </button>\r\n            <button type=\"button\" (click)=\"closeDropdown()\"\r\n              style=\"padding: 8px 20px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 0.875rem; font-weight: 500; display: flex; align-items: center; gap: 4px;\">\r\n              <nb-icon icon=\"checkmark-outline\"></nb-icon>\r\n              確定選擇\r\n            </button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n\r\n  <!-- 點擊外部關閉下拉選單 -->\r\n  <div *ngIf=\"isOpen\" class=\"backdrop\" (click)=\"closeDropdown()\"></div>"], "mappings": "AAAA,SAAmCA,YAAY,EAAoCC,UAAU,QAA2B,eAAe;AACvI,SAA+BC,iBAAiB,QAAQ,gBAAgB;;;;;;;;;ICgB5DC,EAAA,CAAAC,cAAA,eAAkG;IAChGD,EAAA,CAAAE,MAAA,GACA;IAAAF,EAAA,CAAAC,cAAA,iBAA0G;IAA3CD,EAAA,CAAAG,UAAA,mBAAAC,6FAAA;MAAA,MAAAC,gBAAA,GAAAL,EAAA,CAAAM,aAAA,CAAAC,GAAA,EAAAC,SAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAG,iBAAA,CAAAP,gBAAA,CAAgC;IAAA,EAAC;IACvGL,EAAA,CAAAa,SAAA,kBAAwC;IAE5Cb,EADE,CAAAc,YAAA,EAAS,EACJ;;;;;IAJLd,EAAA,CAAAe,SAAA,EACA;IADAf,EAAA,CAAAgB,kBAAA,MAAAX,gBAAA,MACA;IAAyCL,EAAA,CAAAe,SAAA,EAAqB;IAArBf,EAAA,CAAAiB,UAAA,aAAAR,MAAA,CAAAS,QAAA,CAAqB;;;;;IALpElB,EAAA,CAAAmB,uBAAA,GAAoD;IAClDnB,EAAA,CAAAC,cAAA,cAA4B;IAAAD,EAAA,CAAAE,MAAA,GAAa;IAAAF,EAAA,CAAAc,YAAA,EAAM;IAC/Cd,EAAA,CAAAC,cAAA,cAA6B;IAC3BD,EAAA,CAAAoB,UAAA,IAAAC,oEAAA,mBAAkG;IAMpGrB,EAAA,CAAAc,YAAA,EAAM;;;;;;IARsBd,EAAA,CAAAe,SAAA,GAAa;IAAbf,EAAA,CAAAgB,kBAAA,KAAAM,WAAA,MAAa;IAEPtB,EAAA,CAAAe,SAAA,GAA0C;IAA1Cf,EAAA,CAAAiB,UAAA,YAAAR,MAAA,CAAAc,6BAAA,CAAAD,WAAA,EAA0C;;;;;IAJhFtB,EAAA,CAAAC,cAAA,cAA+D;IAC7DD,EAAA,CAAAoB,UAAA,IAAAI,6DAAA,0BAAoD;IAWtDxB,EAAA,CAAAc,YAAA,EAAM;;;;;IAXWd,EAAA,CAAAe,SAAA,EAAmC;IAAnCf,EAAA,CAAAiB,UAAA,SAAAR,MAAA,CAAAgB,mBAAA,CAAAH,WAAA,EAAmC;;;;;;IAVpDtB,EAFJ,CAAAC,cAAA,aAAgG,cACjE,cACA;IACzBD,EAAA,CAAAa,SAAA,kBAA8D;IAC9Db,EAAA,CAAAC,cAAA,eAA6B;IAAAD,EAAA,CAAAE,MAAA,GAA8B;IAC7DF,EAD6D,CAAAc,YAAA,EAAO,EAC9D;IACNd,EAAA,CAAAC,cAAA,iBAAyG;IAAvBD,EAAA,CAAAG,UAAA,mBAAAuB,iEAAA;MAAA1B,EAAA,CAAAM,aAAA,CAAAqB,GAAA;MAAA,MAAAlB,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAmB,UAAA,EAAY;IAAA,EAAC;IACtG5B,EAAA,CAAAE,MAAA,iCACF;IACFF,EADE,CAAAc,YAAA,EAAS,EACL;IACNd,EAAA,CAAAC,cAAA,cAA8B;IAC5BD,EAAA,CAAAoB,UAAA,IAAAS,8CAAA,kBAA+D;IAcnE7B,EADE,CAAAc,YAAA,EAAM,EACF;;;;IArB6Bd,EAAA,CAAAe,SAAA,GAA8B;IAA9Bf,EAAA,CAAAgB,kBAAA,qCAAAP,MAAA,CAAAqB,gBAAA,QAA8B;IAED9B,EAAA,CAAAe,SAAA,EAAqB;IAArBf,EAAA,CAAAiB,UAAA,aAAAR,MAAA,CAAAS,QAAA,CAAqB;IAKvDlB,EAAA,CAAAe,SAAA,GAAY;IAAZf,EAAA,CAAAiB,UAAA,YAAAR,MAAA,CAAAsB,SAAA,CAAY;;;;;IAoBpC/B,EAAA,CAAAmB,uBAAA,GAAgC;IAC9BnB,EAAA,CAAAa,SAAA,kBAAsD;IACtDb,EAAA,CAAAE,MAAA,8BACF;;;;;;IACAF,EAAA,CAAAmB,uBAAA,GAAiC;IAC/BnB,EAAA,CAAAE,MAAA,GACF;;;;;IADEF,EAAA,CAAAe,SAAA,EACF;IADEf,EAAA,CAAAgB,kBAAA,MAAAP,MAAA,CAAAqB,gBAAA,iCAAArB,MAAA,CAAAqB,gBAAA,6BAAArB,MAAA,CAAAuB,WAAA,MACF;;;;;IAwBEhC,EAFF,CAAAC,cAAA,cACmG,cAChD;IAC/CD,EAAA,CAAAa,SAAA,kBAAmG;IACnGb,EAAA,CAAAC,cAAA,YAA2C;IAAAD,EAAA,CAAAE,MAAA,oDAAU;IAEzDF,EAFyD,CAAAc,YAAA,EAAI,EACrD,EACF;;;;;;IAUAd,EAAA,CAAAC,cAAA,iBAG4L;IAHnID,EAAA,CAAAG,UAAA,mBAAA8B,0FAAA;MAAA,MAAAC,WAAA,GAAAlC,EAAA,CAAAM,aAAA,CAAA6B,GAAA,EAAA3B,SAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAA2B,gBAAA,CAAAF,WAAA,CAA0B;IAAA,EAAC;IAI3FlC,EAAA,CAAAC,cAAA,eAAgD;IAAAD,EAAA,CAAAE,MAAA,GAAY;IAAAF,EAAA,CAAAc,YAAA,EAAO;IACnEd,EAAA,CAAAC,cAAA,eACgH;IAAAD,EAAA,CAAAE,MAAA,GAA+B;IACjJF,EADiJ,CAAAc,YAAA,EAAO,EAC/I;;;;;IALPd,EADA,CAAAqC,WAAA,qBAAA5B,MAAA,CAAA6B,gBAAA,KAAAJ,WAAA,6BAAoF,gBAAAzB,MAAA,CAAA6B,gBAAA,KAAAJ,WAAA,iDACe;IAEnDlC,EAAA,CAAAe,SAAA,GAAY;IAAZf,EAAA,CAAAuC,iBAAA,CAAAL,WAAA,CAAY;IAEoDlC,EAAA,CAAAe,SAAA,GAA+B;IAA/Bf,EAAA,CAAAgB,kBAAA,KAAAP,MAAA,CAAA+B,gBAAA,CAAAN,WAAA,YAA+B;;;;;IAQ7IlC,EAAA,CAAAC,cAAA,eAAuD;IAAAD,EAAA,CAAAE,MAAA,GAAsB;IAAAF,EAAA,CAAAc,YAAA,EAAO;;;;IAA7Bd,EAAA,CAAAe,SAAA,EAAsB;IAAtBf,EAAA,CAAAgB,kBAAA,MAAAP,MAAA,CAAA6B,gBAAA,MAAsB;;;;;IAC7EtC,EAAA,CAAAC,cAAA,eAAwE;IAACD,EAAA,CAAAE,MAAA,GAAmB;IAAAF,EAAA,CAAAc,YAAA,EAAO;;;;IAA1Bd,EAAA,CAAAe,SAAA,EAAmB;IAAnBf,EAAA,CAAAgB,kBAAA,QAAAP,MAAA,CAAAgC,aAAA,KAAmB;;;;;;IAc5FzC,EAAA,CAAAC,cAAA,iBACsI;IAD/ED,EAAA,CAAAG,UAAA,mBAAAuC,iGAAA;MAAA1C,EAAA,CAAAM,aAAA,CAAAqC,IAAA;MAAA,MAAAlC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAmC,qBAAA,EAAuB;IAAA,EAAC;IAEtF5C,EAAA,CAAAE,MAAA,qBACF;IAAAF,EAAA,CAAAc,YAAA,EAAS;;;;;;IAbTd,EAFF,CAAAC,cAAA,cACmC,iBAGqG;IAFlFD,EAAA,CAAAG,UAAA,mBAAA0C,wFAAA;MAAA7C,EAAA,CAAAM,aAAA,CAAAwC,GAAA;MAAA,MAAArC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAsC,mBAAA,EAAqB;IAAA,EAAC;IAGjF/C,EAAA,CAAAE,MAAA,iCACF;IAAAF,EAAA,CAAAc,YAAA,EAAS;IACTd,EAAA,CAAAC,cAAA,iBAEsI;IAFlFD,EAAA,CAAAG,UAAA,mBAAA6C,wFAAA;MAAAhD,EAAA,CAAAM,aAAA,CAAAwC,GAAA;MAAA,MAAArC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAwC,mBAAA,EAAqB;IAAA,EAAC;IAGjFjD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAc,YAAA,EAAS;IACTd,EAAA,CAAAoB,UAAA,IAAA8B,wEAAA,qBACsI;IAGxIlD,EAAA,CAAAc,YAAA,EAAM;;;;IAbFd,EAAA,CAAAe,SAAA,EAA+C;IAA/Cf,EAAA,CAAAqC,WAAA,YAAA5B,MAAA,CAAA0C,aAAA,iBAA+C;IAD3BnD,EAAA,CAAAiB,UAAA,cAAAR,MAAA,CAAA0C,aAAA,GAA6B;IAMjDnD,EAAA,CAAAe,SAAA,GAA+C;IAA/Cf,EAAA,CAAAqC,WAAA,YAAA5B,MAAA,CAAA0C,aAAA,iBAA+C;IAD3BnD,EAAA,CAAAiB,UAAA,cAAAR,MAAA,CAAA0C,aAAA,GAA6B;IAGjDnD,EAAA,CAAAe,SAAA,EACF;IADEf,EAAA,CAAAgB,kBAAA,kBAAAP,MAAA,CAAA6B,gBAAA,MACF;IACuBtC,EAAA,CAAAe,SAAA,EAA8B;IAA9Bf,EAAA,CAAAiB,UAAA,SAAAR,MAAA,CAAA2C,sBAAA,GAA8B;;;;;IAavDpD,EAAA,CAAAC,cAAA,cAC+D;IAC7DD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAc,YAAA,EAAM;;;;IADJd,EAAA,CAAAe,SAAA,EACF;IADEf,EAAA,CAAAgB,kBAAA,uCAAAP,MAAA,CAAA4C,UAAA,2BACF;;;;;;IARErD,EAFJ,CAAAC,cAAA,cAAsE,cACnC,gBAEuG;IADnHD,EAAA,CAAAsD,gBAAA,2BAAAC,+FAAAC,MAAA;MAAAxD,EAAA,CAAAM,aAAA,CAAAmD,IAAA;MAAA,MAAAhD,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAAV,EAAA,CAAA0D,kBAAA,CAAAjD,MAAA,CAAA4C,UAAA,EAAAG,MAAA,MAAA/C,MAAA,CAAA4C,UAAA,GAAAG,MAAA;MAAA,OAAAxD,EAAA,CAAAW,WAAA,CAAA6C,MAAA;IAAA,EAAwB;IAACxD,EAAA,CAAAG,UAAA,mBAAAwD,uFAAAH,MAAA;MAAAxD,EAAA,CAAAM,aAAA,CAAAmD,IAAA;MAAA,MAAAhD,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAmD,cAAA,CAAAJ,MAAA,CAAsB;IAAA,EAAC;IAA5ExD,EAAA,CAAAc,YAAA,EACsI;IACtId,EAAA,CAAAa,SAAA,kBACiI;IACnIb,EAAA,CAAAc,YAAA,EAAM;IACNd,EAAA,CAAAoB,UAAA,IAAAyC,qEAAA,kBAC+D;IAGjE7D,EAAA,CAAAc,YAAA,EAAM;;;;IATiBd,EAAA,CAAAe,SAAA,GAAwB;IAAxBf,EAAA,CAAA8D,gBAAA,YAAArD,MAAA,CAAA4C,UAAA,CAAwB;IAKvCrD,EAAA,CAAAe,SAAA,GAAmD;IAAnDf,EAAA,CAAAiB,UAAA,SAAAR,MAAA,CAAA4C,UAAA,IAAA5C,MAAA,CAAAsD,kBAAA,CAAAC,MAAA,OAAmD;;;;;;IAWvDhE,EAAA,CAAAC,cAAA,iBAG2H;IADzHD,EAAA,CAAAG,UAAA,mBAAA8D,iGAAA;MAAAjE,EAAA,CAAAM,aAAA,CAAA4D,IAAA;MAAA,MAAAzD,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAA0D,aAAA,CAAc,EAAE,CAAC;IAAA,EAAC;IAE3BnE,EAAA,CAAAE,MAAA,iCACF;IAAAF,EAAA,CAAAc,YAAA,EAAS;;;;;;IAGTd,EAAA,CAAAC,cAAA,iBAMyJ;IAJvJD,EAAA,CAAAG,UAAA,mBAAAiE,iGAAA;MAAA,MAAAC,SAAA,GAAArE,EAAA,CAAAM,aAAA,CAAAgE,IAAA,EAAA9D,SAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAA0D,aAAA,CAAAE,SAAA,CAAoB;IAAA,EAAC;IAK9BrE,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAc,YAAA,EAAS;;;;;IAHPd,EAFA,CAAAqC,WAAA,qBAAA5B,MAAA,CAAAgC,aAAA,KAAA4B,SAAA,yBAA0E,UAAA5D,MAAA,CAAAgC,aAAA,KAAA4B,SAAA,sBACd,iBAAA5D,MAAA,CAAAgC,aAAA,KAAA4B,SAAA,yBACU;IAEtErE,EAAA,CAAAe,SAAA,EACF;IADEf,EAAA,CAAAuE,kBAAA,MAAAF,SAAA,QAAA5D,MAAA,CAAA+D,aAAA,CAAAH,SAAA,QACF;;;;;IAnBFrE,EADF,CAAAC,cAAA,cAA6E,cACI;IAC7ED,EAAA,CAAAa,SAAA,kBAAsF;IACtFb,EAAA,CAAAC,cAAA,eAAqE;IAAAD,EAAA,CAAAE,MAAA,gCAAK;IAAAF,EAAA,CAAAc,YAAA,EAAO;IACjFd,EAAA,CAAAoB,UAAA,IAAAqD,wEAAA,qBAG2H;IAG7HzE,EAAA,CAAAc,YAAA,EAAM;IACNd,EAAA,CAAAC,cAAA,cAA4F;IAC1FD,EAAA,CAAAoB,UAAA,IAAAsD,wEAAA,qBAMyJ;IAI7J1E,EADE,CAAAc,YAAA,EAAM,EACF;;;;IAjBCd,EAAA,CAAAe,SAAA,GAAmB;IAAnBf,EAAA,CAAAiB,UAAA,SAAAR,MAAA,CAAAgC,aAAA,CAAmB;IAQFzC,EAAA,CAAAe,SAAA,GAAS;IAATf,EAAA,CAAAiB,UAAA,YAAAR,MAAA,CAAAkE,MAAA,CAAS;;;;;IAcjC3E,EAAA,CAAAC,cAAA,cAA+F;IAC7FD,EAAA,CAAAa,SAAA,kBAAkG;IAClGb,EAAA,CAAAC,cAAA,YAA2C;IAAAD,EAAA,CAAAE,MAAA,2CAAM;IACnDF,EADmD,CAAAc,YAAA,EAAI,EACjD;;;;;IAcFd,EAAA,CAAAC,cAAA,eAC4D;IAC1DD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAc,YAAA,EAAO;;;;;IADLd,EAAA,CAAAe,SAAA,EACF;IADEf,EAAA,CAAAgB,kBAAA,MAAAP,MAAA,CAAAmE,iBAAA,CAAAC,iBAAA,OACF;;;;;IACA7E,EAAA,CAAAC,cAAA,eACsN;IACpND,EAAA,CAAAE,MAAA,eACF;IAAAF,EAAA,CAAAc,YAAA,EAAM;;;;;;IAnBRd,EAAA,CAAAC,cAAA,iBAO6O;IAN3OD,EAAA,CAAAG,UAAA,mBAAA2E,iGAAA;MAAA,MAAAD,iBAAA,GAAA7E,EAAA,CAAAM,aAAA,CAAAyE,IAAA,EAAAvE,SAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAuE,iBAAA,CAAAH,iBAAA,CAAgC;IAAA,EAAC;IAO1C7E,EAAA,CAAAC,cAAA,eAC8C;IAC5CD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAc,YAAA,EAAO;IAKPd,EAJA,CAAAoB,UAAA,IAAA6D,+EAAA,mBAC4D,IAAAC,8EAAA,kBAI0J;IAGxNlF,EAAA,CAAAc,YAAA,EAAS;;;;;IAdPd,EAJA,CAAAqC,WAAA,qBAAA5B,MAAA,CAAA0E,mBAAA,CAAAN,iBAAA,gBAAApE,MAAA,CAAA2E,mBAAA,CAAAP,iBAAA,uBAAqI,UAAApE,MAAA,CAAA0E,mBAAA,CAAAN,iBAAA,aAAApE,MAAA,CAAA2E,mBAAA,CAAAP,iBAAA,0BACX,iBAAApE,MAAA,CAAA0E,mBAAA,CAAAN,iBAAA,gBAAApE,MAAA,CAAA2E,mBAAA,CAAAP,iBAAA,0BACU,YAAApE,MAAA,CAAA4E,mBAAA,CAAAR,iBAAA,gBAClE,WAAApE,MAAA,CAAA4E,mBAAA,CAAAR,iBAAA,8BACa;IALpC7E,EAAA,CAAAiB,UAAA,aAAAR,MAAA,CAAA4E,mBAAA,CAAAR,iBAAA,EAA+C;IAOpF7E,EAAA,CAAAe,SAAA,EAAsF;IAAtFf,EAAA,CAAAqC,WAAA,oBAAA5B,MAAA,CAAA2E,mBAAA,CAAAP,iBAAA,4BAAsF;IAE1F7E,EAAA,CAAAe,SAAA,EACF;IADEf,EAAA,CAAAgB,kBAAA,MAAA6D,iBAAA,MACF;IACO7E,EAAA,CAAAe,SAAA,EAAsC;IAAtCf,EAAA,CAAAiB,UAAA,SAAAR,MAAA,CAAAmE,iBAAA,CAAAC,iBAAA,EAAsC;IAIvC7E,EAAA,CAAAe,SAAA,EAAwC;IAAxCf,EAAA,CAAAiB,UAAA,SAAAR,MAAA,CAAA2E,mBAAA,CAAAP,iBAAA,EAAwC;;;;;IAlB9B7E,EAAA,CAAAC,cAAA,cAC4E;IAC9FD,EAAA,CAAAoB,UAAA,IAAAkE,wEAAA,sBAO6O;IAc/OtF,EAAA,CAAAc,YAAA,EAAM;;;;IArB8Bd,EAAA,CAAAe,SAAA,EAAqB;IAArBf,EAAA,CAAAiB,UAAA,YAAAR,MAAA,CAAAsD,kBAAA,CAAqB;;;;;IAxF/D/D,EAAA,CAAAmB,uBAAA,GAAiC;IAI3BnB,EAFJ,CAAAC,cAAA,cAAqF,cAChB,aACa;IAAAD,EAAA,CAAAE,MAAA,+BAAI;IACpFF,EADoF,CAAAc,YAAA,EAAK,EACnF;IACNd,EAAA,CAAAC,cAAA,cAA8D;IAC5DD,EAAA,CAAAoB,UAAA,IAAAmE,iEAAA,qBAG4L;IAMhMvF,EADE,CAAAc,YAAA,EAAM,EACF;IAGmHd,EAFzH,CAAAC,cAAA,cAA6D,cACQ,cACoC,cAA8F;IAC/LD,EAAA,CAAAE,MAAA,kCACA;IACAF,EADA,CAAAoB,UAAA,KAAAoE,gEAAA,mBAAuD,KAAAC,gEAAA,mBACiB;IAC1EzF,EAAA,CAAAc,YAAA,EAAK;IACLd,EAAA,CAAAoB,UAAA,KAAAsE,+DAAA,kBACmC;IAgBrC1F,EAAA,CAAAc,YAAA,EAAM;IAeNd,EAdA,CAAAoB,UAAA,KAAAuE,+DAAA,kBAAsE,KAAAC,+DAAA,kBAcO;IAuB/E5F,EAAA,CAAAc,YAAA,EAAM;IAGNd,EAAA,CAAAC,cAAA,eAAuD;IAIjCD,EAHpB,CAAAoB,UAAA,KAAAyE,+DAAA,kBAA+F,KAAAC,+DAAA,kBAIC;IAwBpG9F,EADE,CAAAc,YAAA,EAAM,EACF;;;;;IAxG2Bd,EAAA,CAAAe,SAAA,GAAY;IAAZf,EAAA,CAAAiB,UAAA,YAAAR,MAAA,CAAAsB,SAAA,CAAY;IAc9B/B,EAAA,CAAAe,SAAA,GAAsB;IAAtBf,EAAA,CAAAiB,UAAA,SAAAR,MAAA,CAAA6B,gBAAA,CAAsB;IACtBtC,EAAA,CAAAe,SAAA,EAAmB;IAAnBf,EAAA,CAAAiB,UAAA,SAAAR,MAAA,CAAAgC,aAAA,CAAmB;IAEtBzC,EAAA,CAAAe,SAAA,EAA2E;IAA3Ef,EAAA,CAAAiB,UAAA,SAAAR,MAAA,CAAAsF,gBAAA,IAAAtF,MAAA,CAAA6B,gBAAA,IAAA7B,MAAA,CAAAsD,kBAAA,CAAAC,MAAA,KAA2E;IAkB7EhE,EAAA,CAAAe,SAAA,EAAqC;IAArCf,EAAA,CAAAiB,UAAA,SAAAR,MAAA,CAAAuF,WAAA,IAAAvF,MAAA,CAAA6B,gBAAA,CAAqC;IAcrCtC,EAAA,CAAAe,SAAA,EAA2C;IAA3Cf,EAAA,CAAAiB,UAAA,SAAAR,MAAA,CAAA6B,gBAAA,IAAA7B,MAAA,CAAAkE,MAAA,CAAAX,MAAA,KAA2C;IA2B3ChE,EAAA,CAAAe,SAAA,GAAuB;IAAvBf,EAAA,CAAAiB,UAAA,UAAAR,MAAA,CAAA6B,gBAAA,CAAuB;IAGHtC,EAAA,CAAAe,SAAA,EAAuD;IAAvDf,EAAA,CAAAiB,UAAA,SAAAR,MAAA,CAAA6B,gBAAA,IAAA7B,MAAA,CAAAsD,kBAAA,CAAAC,MAAA,KAAuD;;;;;IAoCnFhE,EAAA,CAAAC,cAAA,cAAiF;IAC/ED,EAAA,CAAAa,SAAA,mBAAuE;IACvEb,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,kCAAO;IAAAF,EAAA,CAAAC,cAAA,aAAQ;IAAAD,EAAA,CAAAE,MAAA,GAAiB;IAAAF,EAAA,CAAAc,YAAA,EAAS;IAACd,EAAA,CAAAE,MAAA,cAAC;IACnDF,EADmD,CAAAc,YAAA,EAAO,EACpD;;;;IADiBd,EAAA,CAAAe,SAAA,GAAiB;IAAjBf,EAAA,CAAAuC,iBAAA,CAAA9B,MAAA,CAAAwF,aAAA,CAAiB;;;;;IAExCjG,EAAA,CAAAC,cAAA,cAAoF;IAClFD,EAAA,CAAAa,SAAA,kBAA+D;IAC/Db,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,iCAAM;IAAAF,EAAA,CAAAC,cAAA,aAAQ;IAAAD,EAAA,CAAAE,MAAA,GAAoB;IAC1CF,EAD0C,CAAAc,YAAA,EAAS,EAAO,EACpD;;;;IADgBd,EAAA,CAAAe,SAAA,GAAoB;IAApBf,EAAA,CAAAuC,iBAAA,CAAA9B,MAAA,CAAA6B,gBAAA,CAAoB;;;;;IAG5CtC,EAAA,CAAAC,cAAA,eACgH;IAC9GD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAc,YAAA,EAAM;;;;IADJd,EAAA,CAAAe,SAAA,EACF;IADEf,EAAA,CAAAuE,kBAAA,sBAAA9D,MAAA,CAAA4C,UAAA,UAAA5C,MAAA,CAAAsD,kBAAA,CAAAC,MAAA,0BACF;;;;;;IAMEhE,EAAA,CAAAC,cAAA,kBACsL;IAD1HD,EAAA,CAAAG,UAAA,mBAAA+F,2EAAA;MAAAlG,EAAA,CAAAM,aAAA,CAAA6F,IAAA;MAAA,MAAA1F,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAmB,UAAA,EAAY;IAAA,EAAC;IAEhF5B,EAAA,CAAAa,SAAA,mBAA0C;IAC1Cb,EAAA,CAAAE,MAAA,iCACF;IAAAF,EAAA,CAAAc,YAAA,EAAS;;;;;;IACTd,EAAA,CAAAC,cAAA,kBACsL;IAD9HD,EAAA,CAAAG,UAAA,mBAAAiG,2EAAA;MAAApG,EAAA,CAAAM,aAAA,CAAA+F,IAAA;MAAA,MAAA5F,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAA6F,WAAA,EAAa;IAAA,EAAC;IAE7EtG,EAAA,CAAAa,SAAA,mBAA0C;IAC1Cb,EAAA,CAAAE,MAAA,iCACF;IAAAF,EAAA,CAAAc,YAAA,EAAS;;;;;;IAzKXd,EANN,CAAAC,cAAA,cAC0Q,cAG1K,cACX,cACpB;IACzDD,EAAA,CAAAa,SAAA,kBAA+D;IAC/Db,EAAA,CAAAC,cAAA,eAAgD;IAAAD,EAAA,CAAAE,MAAA,+BAAI;IAAAF,EAAA,CAAAc,YAAA,EAAO;IAC3Dd,EAAA,CAAAC,cAAA,eAAmD;IAAAD,EAAA,CAAAE,MAAA,GAA0B;IAC/EF,EAD+E,CAAAc,YAAA,EAAO,EAChF;IACNd,EAAA,CAAAC,cAAA,eAAmD;IAAAD,EAAA,CAAAE,MAAA,IAA2B;IAElFF,EAFkF,CAAAc,YAAA,EAAO,EACjF,EACF;IACNd,EAAA,CAAAC,cAAA,eAA8F;IAY5FD,EATA,CAAAoB,UAAA,KAAAmF,+CAAA,kBACmG,KAAAC,wDAAA,2BAQlE;IAiHnCxG,EAAA,CAAAc,YAAA,EAAM;IAKAd,EAJN,CAAAC,cAAA,eAAsF,eAEkB,eACH,eACpC;IACzDD,EAAA,CAAAa,SAAA,mBAA2E;IAC3Eb,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAE,MAAA,4BAAK;IAAAF,EAAA,CAAAC,cAAA,cAAQ;IAAAD,EAAA,CAAAE,MAAA,IAAsB;IAAAF,EAAA,CAAAc,YAAA,EAAS;IAACd,EAAA,CAAAE,MAAA,2BAAG;IACxDF,EADwD,CAAAc,YAAA,EAAO,EACzD;IAKNd,EAJA,CAAAoB,UAAA,KAAAqF,+CAAA,kBAAiF,KAAAC,+CAAA,kBAIG;IAItF1G,EAAA,CAAAc,YAAA,EAAM;IACNd,EAAA,CAAAoB,UAAA,KAAAuF,+CAAA,kBACgH;IAGlH3G,EAAA,CAAAc,YAAA,EAAM;IAIJd,EADF,CAAAC,cAAA,eAA2F,eACnD;IAMpCD,EALA,CAAAoB,UAAA,KAAAwF,kDAAA,qBACsL,KAAAC,kDAAA,qBAKA;IAIxL7G,EAAA,CAAAc,YAAA,EAAM;IAGJd,EADF,CAAAC,cAAA,eAAsC,kBAEoG;IADlHD,EAAA,CAAAG,UAAA,mBAAA2G,kEAAA;MAAA9G,EAAA,CAAAM,aAAA,CAAAyG,GAAA;MAAA,MAAAtG,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAuG,aAAA,EAAe;IAAA,EAAC;IAE7ChH,EAAA,CAAAE,MAAA,sBACF;IAAAF,EAAA,CAAAc,YAAA,EAAS;IACTd,EAAA,CAAAC,cAAA,kBACwM;IADlLD,EAAA,CAAAG,UAAA,mBAAA8G,kEAAA;MAAAjH,EAAA,CAAAM,aAAA,CAAAyG,GAAA;MAAA,MAAAtG,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAuG,aAAA,EAAe;IAAA,EAAC;IAE7ChH,EAAA,CAAAa,SAAA,mBAA4C;IAC5Cb,EAAA,CAAAE,MAAA,kCACF;IAIRF,EAJQ,CAAAc,YAAA,EAAS,EACL,EACF,EACF,EACF;;;;IAtLqDd,EAAA,CAAAe,SAAA,GAA0B;IAA1Bf,EAAA,CAAAgB,kBAAA,MAAAP,MAAA,CAAAsB,SAAA,CAAAiC,MAAA,yBAA0B;IAE5BhE,EAAA,CAAAe,SAAA,GAA2B;IAA3Bf,EAAA,CAAAgB,kBAAA,yBAAAP,MAAA,CAAAqB,gBAAA,OAA2B;IAM1E9B,EAAA,CAAAe,SAAA,GAAe;IAAff,EAAA,CAAAiB,UAAA,SAAAR,MAAA,CAAAyG,SAAA,CAAe;IASNlH,EAAA,CAAAe,SAAA,EAAgB;IAAhBf,EAAA,CAAAiB,UAAA,UAAAR,MAAA,CAAAyG,SAAA,CAAgB;IAwHNlH,EAAA,CAAAe,SAAA,GAAsB;IAAtBf,EAAA,CAAAuC,iBAAA,CAAA9B,MAAA,CAAAqB,gBAAA,GAAsB;IAErC9B,EAAA,CAAAe,SAAA,GAAmB;IAAnBf,EAAA,CAAAiB,UAAA,SAAAR,MAAA,CAAAwF,aAAA,CAAmB;IAInBjG,EAAA,CAAAe,SAAA,EAAsB;IAAtBf,EAAA,CAAAiB,UAAA,SAAAR,MAAA,CAAA6B,gBAAA,CAAsB;IAKxBtC,EAAA,CAAAe,SAAA,EAAgB;IAAhBf,EAAA,CAAAiB,UAAA,SAAAR,MAAA,CAAA4C,UAAA,CAAgB;IASGrD,EAAA,CAAAe,SAAA,GAAmC;IAAnCf,EAAA,CAAAiB,UAAA,SAAAR,MAAA,CAAA0G,kBAAA,CAAAnD,MAAA,KAAmC;IAKnChE,EAAA,CAAAe,SAAA,EAA+B;IAA/Bf,EAAA,CAAAiB,UAAA,SAAAR,MAAA,CAAAuF,WAAA,IAAAvF,MAAA,CAAA4C,UAAA,CAA+B;;;;;;IAwBhErD,EAAA,CAAAC,cAAA,eAA+D;IAA1BD,EAAA,CAAAG,UAAA,mBAAAiH,8DAAA;MAAApH,EAAA,CAAAM,aAAA,CAAA+G,IAAA;MAAA,MAAA5G,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAuG,aAAA,EAAe;IAAA,EAAC;IAAChH,EAAA,CAAAc,YAAA,EAAM;;;AD3MvE,OAAM,MAAOwG,yBAAyB;EA2BpCC,YACUC,GAAsB,EACtBC,kBAAsC;IADtC,KAAAD,GAAG,GAAHA,GAAG;IACH,KAAAC,kBAAkB,GAAlBA,kBAAkB;IA5BnB,KAAAzF,WAAW,GAAW,OAAO;IAC7B,KAAAiE,aAAa,GAAkB,IAAI;IACnC,KAAA/E,QAAQ,GAAY,KAAK;IACzB,KAAAwG,WAAW,GAAkB,IAAI,CAAC,CAAC;IACnC,KAAAC,YAAY,GAAiB,EAAE;IAC/B,KAAAC,gBAAgB,GAAY,IAAI;IAChC,KAAA5B,WAAW,GAAY,IAAI;IAC3B,KAAAD,gBAAgB,GAAY,IAAI;IAChC,KAAA8B,kBAAkB,GAAa,EAAE,CAAC,CAAC;IAElC,KAAAC,eAAe,GAAG,IAAIjI,YAAY,EAAmB;IAC/D,KAAAkI,MAAM,GAAG,KAAK;IACd,KAAAzF,gBAAgB,GAAG,EAAE;IACrB,KAAAe,UAAU,GAAG,EAAE;IACf,KAAAZ,aAAa,GAAG,EAAE,CAAC,CAAC;IACpB,KAAA0E,kBAAkB,GAAa,EAAE;IACjC,KAAApF,SAAS,GAAa,EAAE;IACxB,KAAA4C,MAAM,GAAa,EAAE,CAAC,CAAC;IACvB,KAAAZ,kBAAkB,GAAa,EAAE,CAAC,CAAE;IACpC,KAAAiE,kBAAkB,GAAqC,EAAE;IACzD,KAAAd,SAAS,GAAY,KAAK,CAAC,CAAC;IAE5B;IACQ,KAAAe,QAAQ,GAAIC,KAAe,IAAI,CAAG,CAAC;IACnC,KAAAC,SAAS,GAAG,MAAK,CAAG,CAAC;EAKzB;EAEJC,UAAUA,CAACF,KAAe;IACxB,IAAI,CAACf,kBAAkB,GAAGe,KAAK,IAAI,EAAE;IACrC,IAAI,CAACG,wBAAwB,EAAE;EACjC;EAEAC,gBAAgBA,CAACC,EAA6B;IAC5C,IAAI,CAACN,QAAQ,GAAGM,EAAE;EACpB;EAEAC,iBAAiBA,CAACD,EAAc;IAC9B,IAAI,CAACJ,SAAS,GAAGI,EAAE;EACrB;EACAE,gBAAgBA,CAACC,UAAmB;IAClC,IAAI,CAACxH,QAAQ,GAAGwH,UAAU;EAC5B;EAAEC,QAAQA,CAAA;IACR,IAAI,CAACC,cAAc,EAAE;EACvB;EAEAC,WAAWA,CAACC,OAAsB;IAChC,IAAIA,OAAO,CAAC,aAAa,CAAC,IAAI,IAAI,CAACpB,WAAW,EAAE;MAC9C;MACA,IAAI,CAACkB,cAAc,EAAE;IACvB;IACA,IAAIE,OAAO,CAAC,cAAc,CAAC,EAAE;MAC3B,IAAI,CAAC/G,SAAS,GAAGgH,MAAM,CAACC,IAAI,CAAC,IAAI,CAACrB,YAAY,CAAC;MAC/C,IAAI,CAACsB,wBAAwB,EAAE;MAC/B,IAAI,CAACZ,wBAAwB,EAAE;IACjC;IACA,IAAIS,OAAO,CAAC,oBAAoB,CAAC,EAAE;MACjC;MACA,IAAI,CAACG,wBAAwB,EAAE;MAC/BC,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAE,IAAI,CAACtB,kBAAkB,CAAC;IACtE;EACF;EAEQe,cAAcA,CAAA;IACpB,IAAI,IAAI,CAAClB,WAAW,EAAE;MACpB;MACA,IAAI,CAAC0B,uBAAuB,EAAE;IAChC,CAAC,MAAM;MACL;MACA,IAAI,CAAC,IAAI,CAACzB,YAAY,IAAIoB,MAAM,CAACC,IAAI,CAAC,IAAI,CAACrB,YAAY,CAAC,CAAC3D,MAAM,KAAK,CAAC,EAAE;QACrE,IAAI,CAAC2D,YAAY,GAAG,IAAI,CAAC0B,gBAAgB,EAAE;MAC7C;MACA,IAAI,CAACtH,SAAS,GAAGgH,MAAM,CAACC,IAAI,CAAC,IAAI,CAACrB,YAAY,CAAC;MAC/CuB,OAAO,CAACC,GAAG,CAAC,uCAAuC,EAAE,IAAI,CAACpH,SAAS,CAAC;MACpE,IAAI,CAACsG,wBAAwB,EAAE;IACjC;EACF;EAEQe,uBAAuBA,CAAA;IAC7B,IAAI,CAAC,IAAI,CAAC1B,WAAW,EAAE;IAEvB,IAAI,CAACR,SAAS,GAAG,IAAI;IACrB,IAAI,CAACO,kBAAkB,CAAC6B,WAAW,CAAC,IAAI,CAAC5B,WAAW,CAAC,CAAC6B,SAAS,CAAC;MAC9DC,IAAI,EAAGC,QAAQ,IAAI;QACjBP,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEM,QAAQ,CAAC;QACtC,IAAI,CAAC9B,YAAY,GAAG,IAAI,CAAC+B,gCAAgC,CAACD,QAAQ,CAACE,OAAO,CAAC;QAC3E,IAAI,CAAC5H,SAAS,GAAGgH,MAAM,CAACC,IAAI,CAAC,IAAI,CAACrB,YAAY,CAAC;QAC/C,IAAI,CAACU,wBAAwB,EAAE;QAC/B,IAAI,CAACnB,SAAS,GAAG,KAAK;QACtB,IAAI,CAACM,GAAG,CAACoC,aAAa,EAAE;MAC1B,CAAC;MACDC,KAAK,EAAGA,KAAK,IAAI;QACfX,OAAO,CAACW,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;QACpD;QACA,IAAI,CAAClC,YAAY,GAAG,IAAI,CAAC0B,gBAAgB,EAAE;QAC3C,IAAI,CAACtH,SAAS,GAAGgH,MAAM,CAACC,IAAI,CAAC,IAAI,CAACrB,YAAY,CAAC;QAC/C,IAAI,CAACU,wBAAwB,EAAE;QAC/B,IAAI,CAACnB,SAAS,GAAG,KAAK;QACtB,IAAI,CAACM,GAAG,CAACoC,aAAa,EAAE;MAC1B;KACD,CAAC;EACJ;EAEQF,gCAAgCA,CAACI,OAAuC;IAC9E,MAAMnC,YAAY,GAAiB,EAAE;IAErCoB,MAAM,CAACe,OAAO,CAACA,OAAO,CAAC,CAACC,OAAO,CAAC,CAAC,CAACC,QAAQ,EAAEC,MAAM,CAAC,KAAI;MACrDtC,YAAY,CAACqC,QAAQ,CAAC,GAAGC,MAAM,CAACC,GAAG,CAACC,KAAK,KAAK;QAC5CC,IAAI,EAAED,KAAK,CAACE,SAAS;QACrBL,QAAQ,EAAEG,KAAK,CAACG,QAAQ;QACxBC,KAAK,EAAEJ,KAAK,CAACK,KAAK;QAClBC,OAAO,EAAEN,KAAK,CAACO,OAAO;QACtBC,SAAS,EAAER,KAAK,CAACE,SAAS;QAC1BO,UAAU,EAAE,KAAK;QACjBlC,UAAU,EAAE;OACb,CAAC,CAAC;IACL,CAAC,CAAC;IAEF,OAAOf,YAAY;EACrB;EAEQU,wBAAwBA,CAAA;IAC9B,MAAMwC,OAAO,GAAqC,EAAE;IAEpD,IAAI,CAAC1D,kBAAkB,CAAC4C,OAAO,CAACK,IAAI,IAAG;MACrC,KAAK,MAAMJ,QAAQ,IAAI,IAAI,CAACjI,SAAS,EAAE;QACrC,MAAM+I,IAAI,GAAG,IAAI,CAACnD,YAAY,CAACqC,QAAQ,CAAC,EAAEe,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACZ,IAAI,KAAKA,IAAI,CAAC;QACpE,IAAIU,IAAI,EAAE;UACR,IAAI,CAACD,OAAO,CAACb,QAAQ,CAAC,EAAEa,OAAO,CAACb,QAAQ,CAAC,GAAG,EAAE;UAC9Ca,OAAO,CAACb,QAAQ,CAAC,CAACiB,IAAI,CAACb,IAAI,CAAC;UAC5B;QACF;MACF;IACF,CAAC,CAAC;IAEF,IAAI,CAACpC,kBAAkB,GAAG6C,OAAO;EACnC;EACQxB,gBAAgBA,CAAA;IACtB;IACA,MAAM6B,cAAc,GAAG;MACrB,IAAI,EAAEC,KAAK,CAACC,IAAI,CAAC;QAAEpH,MAAM,EAAE;MAAE,CAAE,EAAE,CAACqH,CAAC,EAAEC,CAAC,KAAK,IAAIC,MAAM,CAACD,CAAC,GAAG,CAAC,CAAC,CAACE,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE,CAAC;MAChF,IAAI,EAAEL,KAAK,CAACC,IAAI,CAAC;QAAEpH,MAAM,EAAE;MAAE,CAAE,EAAE,CAACqH,CAAC,EAAEC,CAAC,KAAK,IAAIC,MAAM,CAACD,CAAC,GAAG,CAAC,CAAC,CAACE,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE,CAAC;MAChF,IAAI,EAAEL,KAAK,CAACC,IAAI,CAAC;QAAEpH,MAAM,EAAE;MAAE,CAAE,EAAE,CAACqH,CAAC,EAAEC,CAAC,KAAK,IAAIC,MAAM,CAACD,CAAC,GAAG,CAAC,CAAC,CAACE,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE,CAAC;MAChF,IAAI,EAAEL,KAAK,CAACC,IAAI,CAAC;QAAEpH,MAAM,EAAE;MAAE,CAAE,EAAE,CAACqH,CAAC,EAAEC,CAAC,KAAK,IAAIC,MAAM,CAACD,CAAC,GAAG,CAAC,CAAC,CAACE,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE,CAAC;MAChF,IAAI,EAAEL,KAAK,CAACC,IAAI,CAAC;QAAEpH,MAAM,EAAE;MAAE,CAAE,EAAE,CAACqH,CAAC,EAAEC,CAAC,KAAK,IAAIC,MAAM,CAACD,CAAC,GAAG,CAAC,CAAC,CAACE,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;KAChF;IAED;IACA,MAAM7D,YAAY,GAAiB,EAAE;IACrCoB,MAAM,CAACe,OAAO,CAACoB,cAAc,CAAC,CAACnB,OAAO,CAAC,CAAC,CAACC,QAAQ,EAAEyB,KAAK,CAAC,KAAI;MAC3D9D,YAAY,CAACqC,QAAQ,CAAC,GAAGyB,KAAK,CAACvB,GAAG,CAACE,IAAI,KAAK;QAC1CA,IAAI;QACJJ,QAAQ;QACRO,KAAK,EAAE,GAAGmB,IAAI,CAACnB,KAAK,CAAC,CAACoB,QAAQ,CAACvB,IAAI,CAACwB,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,GAAG;QAC9DhB,UAAU,EAAE,KAAK;QACjBlC,UAAU,EAAE;OACb,CAAC,CAAC;IACL,CAAC,CAAC;IAEF,OAAOf,YAAY;EACrB;EAAGvF,gBAAgBA,CAAC4H,QAAgB;IAClCd,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEa,QAAQ,CAAC;IAC3C,IAAI,CAAC1H,gBAAgB,GAAG0H,QAAQ;IAChC,IAAI,CAACvH,aAAa,GAAG,EAAE,CAAC,CAAC;IACzB,IAAI,CAACY,UAAU,GAAG,EAAE;IACpB,IAAI,CAACwI,uBAAuB,EAAE,CAAC,CAAC;IAChC,IAAI,CAAC5C,wBAAwB,EAAE;IAC/BC,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAE,IAAI,CAACpF,kBAAkB,CAACC,MAAM,CAAC;IACzE;IACA,IAAI,CAACwD,GAAG,CAACoC,aAAa,EAAE;EAC1B;EAEAkC,eAAeA,CAAC9B,QAAgB;IAC9Bd,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAEa,QAAQ,CAAC;EACxD;EAAGf,wBAAwBA,CAAA;IACzBC,OAAO,CAACC,GAAG,CAAC,4CAA4C,EAAE,IAAI,CAAC7G,gBAAgB,EAAE,QAAQ,EAAE,IAAI,CAACG,aAAa,CAAC;IAC9G,IAAI,CAAC,IAAI,CAACH,gBAAgB,EAAE;MAC1B,IAAI,CAACyB,kBAAkB,GAAG,EAAE;MAC5B;IACF;IAEA,MAAMgI,UAAU,GAAG,IAAI,CAACpE,YAAY,CAAC,IAAI,CAACrF,gBAAgB,CAAC,IAAI,EAAE;IACjE4G,OAAO,CAACC,GAAG,CAAC,oCAAoC,EAAE4C,UAAU,CAAC/H,MAAM,CAAC;IAEpE;IACA,IAAI,CAACD,kBAAkB,GAAGgI,UAAU,CACjCC,MAAM,CAAChB,CAAC,IAAG;MACV;MACA,MAAMiB,UAAU,GAAG,CAAC,IAAI,CAACxJ,aAAa,IAAIuI,CAAC,CAACT,KAAK,KAAK,IAAI,CAAC9H,aAAa;MACxE;MACA,MAAMyJ,WAAW,GAAGlB,CAAC,CAACZ,IAAI,CAAC+B,WAAW,EAAE,CAACC,QAAQ,CAAC,IAAI,CAAC/I,UAAU,CAAC8I,WAAW,EAAE,CAAC;MAChF,OAAOF,UAAU,IAAIC,WAAW;IAClC,CAAC,CAAC,CACDhC,GAAG,CAACc,CAAC,IAAIA,CAAC,CAACZ,IAAI,CAAC;IAEnBlB,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAE,IAAI,CAACpF,kBAAkB,CAACC,MAAM,CAAC;EAC5E;EAEAJ,cAAcA,CAACyI,KAAU;IACvB,IAAI,CAAChJ,UAAU,GAAGgJ,KAAK,CAACC,MAAM,CAACpE,KAAK;IACpC,IAAI,CAACe,wBAAwB,EAAE;IAC/BC,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAE,IAAI,CAAC9F,UAAU,CAAC;EACtD;EAEAiD,WAAWA,CAAA;IACT,IAAI,CAACjD,UAAU,GAAG,EAAE;IACpB,IAAI,CAAC4F,wBAAwB,EAAE;IAC/BC,OAAO,CAACC,GAAG,CAAC,cAAc,CAAC;EAC7B;EACAnE,iBAAiBA,CAACuH,aAAqB;IACrC;IACA,IAAI,IAAI,CAACnH,mBAAmB,CAACmH,aAAa,CAAC,EAAE;MAC3CrD,OAAO,CAACC,GAAG,CAAC,MAAMoD,aAAa,kBAAkB,CAAC;MAClD;IACF;IAEA,MAAM3B,UAAU,GAAG,IAAI,CAACzD,kBAAkB,CAACiF,QAAQ,CAACG,aAAa,CAAC;IAClE,IAAIC,YAAsB;IAE1B,IAAI5B,UAAU,EAAE;MACd4B,YAAY,GAAG,IAAI,CAACrF,kBAAkB,CAAC6E,MAAM,CAAChB,CAAC,IAAIA,CAAC,KAAKuB,aAAa,CAAC;IACzE,CAAC,MAAM;MACL,IAAI,IAAI,CAACtG,aAAa,IAAI,IAAI,CAACkB,kBAAkB,CAACnD,MAAM,IAAI,IAAI,CAACiC,aAAa,EAAE;QAC9EiD,OAAO,CAACC,GAAG,CAAC,WAAW,CAAC;QACxB,OAAO,CAAC;MACV;MACAqD,YAAY,GAAG,CAAC,GAAG,IAAI,CAACrF,kBAAkB,EAAEoF,aAAa,CAAC;IAC5D;IAEA,IAAI,CAACpF,kBAAkB,GAAGqF,YAAY;IACtC,IAAI,CAACC,WAAW,EAAE;EACpB;EAEA7L,iBAAiBA,CAAC2L,aAAqB;IACrC,IAAI,CAACpF,kBAAkB,GAAG,IAAI,CAACA,kBAAkB,CAAC6E,MAAM,CAAChB,CAAC,IAAIA,CAAC,KAAKuB,aAAa,CAAC;IAClF,IAAI,CAACE,WAAW,EAAE;EACpB;EAAE1J,mBAAmBA,CAAA;IACnB,IAAI,CAAC,IAAI,CAACT,gBAAgB,IAAI,IAAI,CAACyB,kBAAkB,CAACC,MAAM,KAAK,CAAC,EAAE;IAEpE;IACA,MAAM0I,YAAY,GAAG,IAAI,CAACvF,kBAAkB,CAACnD,MAAM;IACnD,MAAM2I,UAAU,GAAG,IAAI,CAAC1G,aAAa,IAAI2G,QAAQ;IACjD,MAAMC,cAAc,GAAGF,UAAU,GAAGD,YAAY,CAAC,CAAI;IACrD,MAAMI,kBAAkB,GAAG,IAAI,CAAC/I,kBAAkB,CAACiI,MAAM,CAAC5B,IAAI,IAC5D,CAAC,IAAI,CAACjD,kBAAkB,CAACiF,QAAQ,CAAChC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAChF,mBAAmB,CAACgF,IAAI,CAAC,CAC3E;IAED;IACA,MAAM2C,KAAK,GAAGD,kBAAkB,CAAClB,KAAK,CAAC,CAAC,EAAEiB,cAAc,CAAC;IAEzD,IAAIE,KAAK,CAAC/I,MAAM,GAAG,CAAC,EAAE;MACpB,IAAI,CAACmD,kBAAkB,GAAG,CAAC,GAAG,IAAI,CAACA,kBAAkB,EAAE,GAAG4F,KAAK,CAAC;MAChE,IAAI,CAACN,WAAW,EAAE;MAClBvD,OAAO,CAACC,GAAG,CAAC,aAAa4D,KAAK,CAAC/I,MAAM,MAAM,CAAC;IAC9C;EACF;EAEAf,mBAAmBA,CAAA;IACjB,IAAI,CAAC,IAAI,CAACX,gBAAgB,EAAE;IAE5B;IACA,MAAM0K,kBAAkB,GAAG,IAAI,CAACrF,YAAY,CAAC,IAAI,CAACrF,gBAAgB,CAAC,EAAE4H,GAAG,CAACc,CAAC,IAAIA,CAAC,CAACZ,IAAI,CAAC,IAAI,EAAE;IAE3F;IACA,MAAMsC,YAAY,GAAG,IAAI,CAACvF,kBAAkB,CAACnD,MAAM;IACnD,MAAM2I,UAAU,GAAG,IAAI,CAAC1G,aAAa,IAAI2G,QAAQ;IACjD,MAAMC,cAAc,GAAGF,UAAU,GAAGD,YAAY,CAAC,CAAI;IACrD,MAAMO,kBAAkB,GAAGD,kBAAkB,CAAChB,MAAM,CAAC5B,IAAI,IACvD,CAAC,IAAI,CAACjD,kBAAkB,CAACiF,QAAQ,CAAChC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAChF,mBAAmB,CAACgF,IAAI,CAAC,CAC3E;IAED;IACA,MAAM2C,KAAK,GAAGE,kBAAkB,CAACrB,KAAK,CAAC,CAAC,EAAEiB,cAAc,CAAC;IAEzD,IAAIE,KAAK,CAAC/I,MAAM,GAAG,CAAC,EAAE;MACpB,IAAI,CAACmD,kBAAkB,GAAG,CAAC,GAAG,IAAI,CAACA,kBAAkB,EAAE,GAAG4F,KAAK,CAAC;MAChE,IAAI,CAACN,WAAW,EAAE;MAClBvD,OAAO,CAACC,GAAG,CAAC,aAAa4D,KAAK,CAAC/I,MAAM,MAAM,CAAC;IAC9C;EACF;EACApB,qBAAqBA,CAAA;IACnB,IAAI,CAAC,IAAI,CAACN,gBAAgB,EAAE;IAE5B,MAAM0K,kBAAkB,GAAG,IAAI,CAACrF,YAAY,CAAC,IAAI,CAACrF,gBAAgB,CAAC,EAAE4H,GAAG,CAACc,CAAC,IAAIA,CAAC,CAACZ,IAAI,CAAC,IAAI,EAAE;IAC3F,IAAI,CAACjD,kBAAkB,GAAG,IAAI,CAACA,kBAAkB,CAAC6E,MAAM,CAAChB,CAAC,IAAI,CAACgC,kBAAkB,CAACZ,QAAQ,CAACpB,CAAC,CAAC,CAAC;IAC9F,IAAI,CAACyB,WAAW,EAAE;EACpB;EACA7K,UAAUA,CAAA;IACR,IAAI,CAACuF,kBAAkB,GAAG,EAAE;IAC5B,IAAI,CAACsF,WAAW,EAAE;EACpB;EAEQA,WAAWA,CAAA;IACjB,IAAI,CAACpE,wBAAwB,EAAE;IAC/B,IAAI,CAACJ,QAAQ,CAAC,CAAC,GAAG,IAAI,CAACd,kBAAkB,CAAC,CAAC;IAC3C,IAAI,CAACgB,SAAS,EAAE;IAEhB,MAAM+E,aAAa,GAAG,IAAI,CAAC/F,kBAAkB,CAAC+C,GAAG,CAACE,IAAI,IAAG;MACvD,KAAK,MAAMJ,QAAQ,IAAI,IAAI,CAACjI,SAAS,EAAE;QACrC,MAAM+I,IAAI,GAAG,IAAI,CAACnD,YAAY,CAACqC,QAAQ,CAAC,EAAEe,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACZ,IAAI,KAAKA,IAAI,CAAC;QACpE,IAAIU,IAAI,EAAE,OAAOA,IAAI;MACvB;MACA,OAAO,IAAI;IACb,CAAC,CAAC,CAACkB,MAAM,CAAClB,IAAI,IAAIA,IAAI,KAAK,IAAI,CAAoB;IAEnD,IAAI,CAAChD,eAAe,CAACqF,IAAI,CAACD,aAAa,CAAC;EAC1C;EACAE,cAAcA,CAAA;IACZ,IAAI,CAAC,IAAI,CAAClM,QAAQ,EAAE;MAClB,IAAI,CAAC6G,MAAM,GAAG,CAAC,IAAI,CAACA,MAAM;MAC1BmB,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAE,IAAI,CAACpB,MAAM,CAAC;MACrDmB,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAE,IAAI,CAACpH,SAAS,CAAC;IACrD;EACF;EAEAiF,aAAaA,CAAA;IACX,IAAI,CAACe,MAAM,GAAG,KAAK;EACrB;EAEA5C,mBAAmBA,CAACoH,aAAqB;IACvC,OAAO,IAAI,CAACpF,kBAAkB,CAACiF,QAAQ,CAACG,aAAa,CAAC;EACxD;EAEAnH,mBAAmBA,CAACmH,aAAqB;IACvC,OAAO,IAAI,CAAC1E,kBAAkB,CAACuE,QAAQ,CAACG,aAAa,CAAC;EACxD;EAEAlH,mBAAmBA,CAACkH,aAAqB;IACvC,OAAO,IAAI,CAACnH,mBAAmB,CAACmH,aAAa,CAAC,IAC3C,CAAC,IAAI,CAACpJ,aAAa,EAAE,IAAI,CAAC,IAAI,CAACgC,mBAAmB,CAACoH,aAAa,CAAE;EACvE;EAEApJ,aAAaA,CAAA;IACX,OAAO,CAAC,IAAI,CAAC8C,aAAa,IAAI,IAAI,CAACkB,kBAAkB,CAACnD,MAAM,GAAG,IAAI,CAACiC,aAAa;EACnF;EAEAoH,qBAAqBA,CAAA;IACnB,IAAI,CAAC,IAAI,CAAC/K,gBAAgB,EAAE,OAAO,KAAK;IACxC,MAAM0K,kBAAkB,GAAG,IAAI,CAACrF,YAAY,CAAC,IAAI,CAACrF,gBAAgB,CAAC,CAChE0J,MAAM,CAAChB,CAAC,IAAI,CAACA,CAAC,CAACtC,UAAU,CAAC,CAC1BwB,GAAG,CAACc,CAAC,IAAIA,CAAC,CAACZ,IAAI,CAAC;IACnB,OAAO4C,kBAAkB,CAAChJ,MAAM,GAAG,CAAC,IAClCgJ,kBAAkB,CAACM,KAAK,CAAClD,IAAI,IAAI,IAAI,CAACjD,kBAAkB,CAACiF,QAAQ,CAAChC,IAAI,CAAC,CAAC;EAC5E;EACAhH,sBAAsBA,CAAA;IACpB,IAAI,CAAC,IAAI,CAACd,gBAAgB,EAAE,OAAO,KAAK;IACxC,MAAM0K,kBAAkB,GAAG,IAAI,CAACrF,YAAY,CAAC,IAAI,CAACrF,gBAAgB,CAAC,EAAE4H,GAAG,CAACc,CAAC,IAAIA,CAAC,CAACZ,IAAI,CAAC,IAAI,EAAE;IAC3F,OAAO4C,kBAAkB,CAACO,IAAI,CAACnD,IAAI,IAAI,IAAI,CAACjD,kBAAkB,CAACiF,QAAQ,CAAChC,IAAI,CAAC,CAAC;EAChF;EACAoD,qBAAqBA,CAAA;IACnB,OAAO,IAAI,CAACxF,kBAAkB;EAChC;EAEAxF,gBAAgBA,CAACwH,QAAgB;IAC/B,OAAO,IAAI,CAACrC,YAAY,CAACqC,QAAQ,CAAC,EAAEhG,MAAM,IAAI,CAAC;EACjD;EAEAlC,gBAAgBA,CAAA;IACd,OAAO,IAAI,CAACqF,kBAAkB,CAACnD,MAAM;EACvC;EAEA;EACAzC,6BAA6BA,CAACyI,QAAgB;IAC5C,OAAO,IAAI,CAAChC,kBAAkB,CAACgC,QAAQ,CAAC,IAAI,EAAE;EAChD;EAEA;EACAvI,mBAAmBA,CAACuI,QAAgB;IAClC,OAAO,CAAC,EAAE,IAAI,CAAChC,kBAAkB,CAACgC,QAAQ,CAAC,IAAI,IAAI,CAAChC,kBAAkB,CAACgC,QAAQ,CAAC,CAAChG,MAAM,GAAG,CAAC,CAAC;EAC9F;EAEA;EACQ6H,uBAAuBA,CAAA;IAC7B,IAAI,CAAC,IAAI,CAACvJ,gBAAgB,EAAE;MAC1B,IAAI,CAACqC,MAAM,GAAG,EAAE;MAChB;IACF;IAEA,MAAMoH,UAAU,GAAG,IAAI,CAACpE,YAAY,CAAC,IAAI,CAACrF,gBAAgB,CAAC,IAAI,EAAE;IACjE,MAAMmL,QAAQ,GAAG,IAAIC,GAAG,EAAU;IAElC3B,UAAU,CAAChC,OAAO,CAAC4D,SAAS,IAAG;MAC7B,IAAIA,SAAS,CAACpD,KAAK,EAAE;QACnBkD,QAAQ,CAACG,GAAG,CAACD,SAAS,CAACpD,KAAK,CAAC;MAC/B;IACF,CAAC,CAAC;IAEF;IACA,IAAI,CAAC5F,MAAM,GAAGwG,KAAK,CAACC,IAAI,CAACqC,QAAQ,CAAC,CAACI,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAI;MAC/C,MAAMC,IAAI,GAAGrC,QAAQ,CAACmC,CAAC,CAACG,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC,CAAC;MAC/C,MAAMC,IAAI,GAAGvC,QAAQ,CAACoC,CAAC,CAACE,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC,CAAC;MAC/C,OAAOD,IAAI,GAAGE,IAAI;IACpB,CAAC,CAAC;IAEFhF,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAE,IAAI,CAAC7G,gBAAgB,EAAE,IAAI,CAACqC,MAAM,CAAC;EACjF;EAEA;EACAR,aAAaA,CAACoG,KAAa;IACzBrB,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEoB,KAAK,CAAC;IACrC,IAAI,CAAC9H,aAAa,GAAG,IAAI,CAACA,aAAa,KAAK8H,KAAK,GAAG,EAAE,GAAGA,KAAK,CAAC,CAAC;IAChE,IAAI,CAACtB,wBAAwB,EAAE;IAC/B,IAAI,CAACzB,GAAG,CAACoC,aAAa,EAAE;EAC1B;EAEA;EACApF,aAAaA,CAAC+F,KAAa;IACzB,IAAI,CAAC,IAAI,CAACjI,gBAAgB,EAAE,OAAO,CAAC;IACpC,MAAMyJ,UAAU,GAAG,IAAI,CAACpE,YAAY,CAAC,IAAI,CAACrF,gBAAgB,CAAC,IAAI,EAAE;IACjE,OAAOyJ,UAAU,CAACC,MAAM,CAAChB,CAAC,IAAIA,CAAC,CAACT,KAAK,KAAKA,KAAK,CAAC,CAACvG,MAAM;EACzD;EAEA;EACAY,iBAAiBA,CAAC2H,aAAqB;IACrC,IAAI,CAAC,IAAI,CAACjK,gBAAgB,EAAE,OAAO,EAAE;IACrC,MAAMyJ,UAAU,GAAG,IAAI,CAACpE,YAAY,CAAC,IAAI,CAACrF,gBAAgB,CAAC,IAAI,EAAE;IACjE,MAAMqL,SAAS,GAAG5B,UAAU,CAAChB,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACZ,IAAI,KAAKmC,aAAa,CAAC;IAChE,OAAOoB,SAAS,EAAEpD,KAAK,IAAI,EAAE;EAC/B;;;uCAtaWjD,yBAAyB,EAAAtH,EAAA,CAAAmO,iBAAA,CAAAnO,EAAA,CAAAoO,iBAAA,GAAApO,EAAA,CAAAmO,iBAAA,CAAAE,EAAA,CAAAC,kBAAA;IAAA;EAAA;;;YAAzBhH,yBAAyB;MAAAiH,SAAA;MAAAC,MAAA;QAAAxM,WAAA;QAAAiE,aAAA;QAAA/E,QAAA;QAAAwG,WAAA;QAAAC,YAAA;QAAAC,gBAAA;QAAA5B,WAAA;QAAAD,gBAAA;QAAA8B,kBAAA;MAAA;MAAA4G,OAAA;QAAA3G,eAAA;MAAA;MAAA4G,QAAA,GAAA1O,EAAA,CAAA2O,kBAAA,CARzB,CACT;QACEC,OAAO,EAAE7O,iBAAiB;QAC1B8O,WAAW,EAAE/O,UAAU,CAAC,MAAMwH,yBAAyB,CAAC;QACxDwH,KAAK,EAAE;OACR,CACF,GAAA9O,EAAA,CAAA+O,oBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,mCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UClCHrP,EAAA,CAAAC,cAAA,aAAyC;UAEvCD,EAAA,CAAAoB,UAAA,IAAAmO,wCAAA,kBAAgG;UA2B9FvP,EADF,CAAAC,cAAA,aAA4D,gBAGuJ;UAD5KD,EAAA,CAAAG,UAAA,mBAAAqP,2DAAA;YAAA,OAASF,GAAA,CAAAlC,cAAA,EAAgB;UAAA,EAAC;UAE7DpN,EAAA,CAAAC,cAAA,cAA4B;UAK1BD,EAJA,CAAAoB,UAAA,IAAAqO,iDAAA,0BAAgC,IAAAC,iDAAA,0BAIC;UAGnC1P,EAAA,CAAAc,YAAA,EAAO;UACPd,EAAA,CAAAa,SAAA,iBACU;UACZb,EAAA,CAAAc,YAAA,EAAS;UACTd,EAAA,CAAAoB,UAAA,IAAAuO,wCAAA,mBAC0Q;UA+L5Q3P,EAAA,CAAAc,YAAA,EAAM;UAGNd,EAAA,CAAAoB,UAAA,IAAAwO,wCAAA,iBAA+D;UA/OjE5P,EAAA,CAAAc,YAAA,EAAyC;;;UAEjCd,EAAA,CAAAe,SAAA,EAAuD;UAAvDf,EAAA,CAAAiB,UAAA,SAAAqO,GAAA,CAAA1H,gBAAA,IAAA0H,GAAA,CAAAnI,kBAAA,CAAAnD,MAAA,KAAuD;UA2BbhE,EAAA,CAAAe,SAAA,GAAwC;UAAxCf,EAAA,CAAA6P,WAAA,aAAAP,GAAA,CAAApO,QAAA,IAAAoO,GAAA,CAAApI,SAAA,CAAwC;UACpFlH,EAAA,CAAAiB,UAAA,aAAAqO,GAAA,CAAApO,QAAA,IAAAoO,GAAA,CAAApI,SAAA,CAAkC;UAGjBlH,EAAA,CAAAe,SAAA,GAAe;UAAff,EAAA,CAAAiB,UAAA,SAAAqO,GAAA,CAAApI,SAAA,CAAe;UAIflH,EAAA,CAAAe,SAAA,EAAgB;UAAhBf,EAAA,CAAAiB,UAAA,UAAAqO,GAAA,CAAApI,SAAA,CAAgB;UAIIlH,EAAA,CAAAe,SAAA,EAAwB;UAAxBf,EAAA,CAAA6P,WAAA,YAAAP,GAAA,CAAAvH,MAAA,CAAwB;UAGzD/H,EAAA,CAAAe,SAAA,EAAY;UAAZf,EAAA,CAAAiB,UAAA,SAAAqO,GAAA,CAAAvH,MAAA,CAAY;UAmMd/H,EAAA,CAAAe,SAAA,EAAY;UAAZf,EAAA,CAAAiB,UAAA,SAAAqO,GAAA,CAAAvH,MAAA,CAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}