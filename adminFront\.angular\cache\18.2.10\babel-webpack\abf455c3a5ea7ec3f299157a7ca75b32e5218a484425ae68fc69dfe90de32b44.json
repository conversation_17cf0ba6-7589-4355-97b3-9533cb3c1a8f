{"ast": null, "code": "/**\n * @license Angular v18.2.9\n * (c) 2010-2024 Google LLC. https://angular.io/\n * License: MIT\n */\n\n/**\n * The character used to mark the start and end of a \"block\" in a `$localize` tagged string.\n * A block can indicate metadata about the message or specify a name of a placeholder for a\n * substitution expressions.\n *\n * For example:\n *\n * ```ts\n * $localize`Hello, ${title}:title:!`;\n * $localize`:meaning|description@@id:source message text`;\n * ```\n */\nconst BLOCK_MARKER$1 = ':';\n/**\n * The marker used to separate a message's \"meaning\" from its \"description\" in a metadata block.\n *\n * For example:\n *\n * ```ts\n * $localize `:correct|Indicates that the user got the answer correct: Right!`;\n * $localize `:movement|Button label for moving to the right: Right!`;\n * ```\n */\nconst MEANING_SEPARATOR = '|';\n/**\n * The marker used to separate a message's custom \"id\" from its \"description\" in a metadata block.\n *\n * For example:\n *\n * ```ts\n * $localize `:A welcome message on the home page@@myApp-homepage-welcome: Welcome!`;\n * ```\n */\nconst ID_SEPARATOR = '@@';\n/**\n * The marker used to separate legacy message ids from the rest of a metadata block.\n *\n * For example:\n *\n * ```ts\n * $localize `:@@custom-id␟2df64767cd895a8fabe3e18b94b5b6b6f9e2e3f0: Welcome!`;\n * ```\n *\n * Note that this character is the \"symbol for the unit separator\" (␟) not the \"unit separator\n * character\" itself, since that has no visual representation. See https://graphemica.com/%E2%90%9F.\n *\n * Here is some background for the original \"unit separator character\":\n * https://stackoverflow.com/questions/8695118/whats-the-file-group-record-unit-separator-control-characters-and-its-usage\n */\nconst LEGACY_ID_INDICATOR = '\\u241F';\n\n/**\n * A lazily created TextEncoder instance for converting strings into UTF-8 bytes\n */\nlet textEncoder;\n/**\n * Return the message id or compute it using the XLIFF1 digest.\n */\nfunction digest(message) {\n  return message.id || computeDigest(message);\n}\n/**\n * Compute the message id using the XLIFF1 digest.\n */\nfunction computeDigest(message) {\n  return sha1(serializeNodes(message.nodes).join('') + `[${message.meaning}]`);\n}\n/**\n * Return the message id or compute it using the XLIFF2/XMB/$localize digest.\n */\nfunction decimalDigest(message, preservePlaceholders) {\n  return message.id || computeDecimalDigest(message, preservePlaceholders);\n}\n/**\n * Compute the message id using the XLIFF2/XMB/$localize digest.\n */\nfunction computeDecimalDigest(message, preservePlaceholders) {\n  const visitor = new _SerializerIgnoreExpVisitor(preservePlaceholders);\n  const parts = message.nodes.map(a => a.visit(visitor, null));\n  return computeMsgId(parts.join(''), message.meaning);\n}\n/**\n * Serialize the i18n ast to something xml-like in order to generate an UID.\n *\n * The visitor is also used in the i18n parser tests\n *\n * @internal\n */\nclass _SerializerVisitor {\n  visitText(text, context) {\n    return text.value;\n  }\n  visitContainer(container, context) {\n    return `[${container.children.map(child => child.visit(this)).join(', ')}]`;\n  }\n  visitIcu(icu, context) {\n    const strCases = Object.keys(icu.cases).map(k => `${k} {${icu.cases[k].visit(this)}}`);\n    return `{${icu.expression}, ${icu.type}, ${strCases.join(', ')}}`;\n  }\n  visitTagPlaceholder(ph, context) {\n    return ph.isVoid ? `<ph tag name=\"${ph.startName}\"/>` : `<ph tag name=\"${ph.startName}\">${ph.children.map(child => child.visit(this)).join(', ')}</ph name=\"${ph.closeName}\">`;\n  }\n  visitPlaceholder(ph, context) {\n    return ph.value ? `<ph name=\"${ph.name}\">${ph.value}</ph>` : `<ph name=\"${ph.name}\"/>`;\n  }\n  visitIcuPlaceholder(ph, context) {\n    return `<ph icu name=\"${ph.name}\">${ph.value.visit(this)}</ph>`;\n  }\n  visitBlockPlaceholder(ph, context) {\n    return `<ph block name=\"${ph.startName}\">${ph.children.map(child => child.visit(this)).join(', ')}</ph name=\"${ph.closeName}\">`;\n  }\n}\nconst serializerVisitor = new _SerializerVisitor();\nfunction serializeNodes(nodes) {\n  return nodes.map(a => a.visit(serializerVisitor, null));\n}\n/**\n * Serialize the i18n ast to something xml-like in order to generate an UID.\n *\n * Ignore the expressions so that message IDs stays identical if only the expression changes.\n *\n * @internal\n */\nclass _SerializerIgnoreExpVisitor extends _SerializerVisitor {\n  constructor(preservePlaceholders) {\n    super();\n    this.preservePlaceholders = preservePlaceholders;\n  }\n  visitPlaceholder(ph, context) {\n    // Do not take the expression into account when `preservePlaceholders` is disabled.\n    return this.preservePlaceholders ? super.visitPlaceholder(ph, context) : `<ph name=\"${ph.name}\"/>`;\n  }\n  visitIcu(icu) {\n    let strCases = Object.keys(icu.cases).map(k => `${k} {${icu.cases[k].visit(this)}}`);\n    // Do not take the expression into account\n    return `{${icu.type}, ${strCases.join(', ')}}`;\n  }\n}\n/**\n * Compute the SHA1 of the given string\n *\n * see https://csrc.nist.gov/publications/fips/fips180-4/fips-180-4.pdf\n *\n * WARNING: this function has not been designed not tested with security in mind.\n *          DO NOT USE IT IN A SECURITY SENSITIVE CONTEXT.\n */\nfunction sha1(str) {\n  textEncoder ??= new TextEncoder();\n  const utf8 = [...textEncoder.encode(str)];\n  const words32 = bytesToWords32(utf8, Endian.Big);\n  const len = utf8.length * 8;\n  const w = new Uint32Array(80);\n  let a = 0x67452301,\n    b = 0xefcdab89,\n    c = 0x98badcfe,\n    d = 0x10325476,\n    e = 0xc3d2e1f0;\n  words32[len >> 5] |= 0x80 << 24 - len % 32;\n  words32[(len + 64 >> 9 << 4) + 15] = len;\n  for (let i = 0; i < words32.length; i += 16) {\n    const h0 = a,\n      h1 = b,\n      h2 = c,\n      h3 = d,\n      h4 = e;\n    for (let j = 0; j < 80; j++) {\n      if (j < 16) {\n        w[j] = words32[i + j];\n      } else {\n        w[j] = rol32(w[j - 3] ^ w[j - 8] ^ w[j - 14] ^ w[j - 16], 1);\n      }\n      const fkVal = fk(j, b, c, d);\n      const f = fkVal[0];\n      const k = fkVal[1];\n      const temp = [rol32(a, 5), f, e, k, w[j]].reduce(add32);\n      e = d;\n      d = c;\n      c = rol32(b, 30);\n      b = a;\n      a = temp;\n    }\n    a = add32(a, h0);\n    b = add32(b, h1);\n    c = add32(c, h2);\n    d = add32(d, h3);\n    e = add32(e, h4);\n  }\n  // Convert the output parts to a 160-bit hexadecimal string\n  return toHexU32(a) + toHexU32(b) + toHexU32(c) + toHexU32(d) + toHexU32(e);\n}\n/**\n * Convert and format a number as a string representing a 32-bit unsigned hexadecimal number.\n * @param value The value to format as a string.\n * @returns A hexadecimal string representing the value.\n */\nfunction toHexU32(value) {\n  // unsigned right shift of zero ensures an unsigned 32-bit number\n  return (value >>> 0).toString(16).padStart(8, '0');\n}\nfunction fk(index, b, c, d) {\n  if (index < 20) {\n    return [b & c | ~b & d, 0x5a827999];\n  }\n  if (index < 40) {\n    return [b ^ c ^ d, 0x6ed9eba1];\n  }\n  if (index < 60) {\n    return [b & c | b & d | c & d, 0x8f1bbcdc];\n  }\n  return [b ^ c ^ d, 0xca62c1d6];\n}\n/**\n * Compute the fingerprint of the given string\n *\n * The output is 64 bit number encoded as a decimal string\n *\n * based on:\n * https://github.com/google/closure-compiler/blob/master/src/com/google/javascript/jscomp/GoogleJsMessageIdGenerator.java\n */\nfunction fingerprint(str) {\n  textEncoder ??= new TextEncoder();\n  const utf8 = textEncoder.encode(str);\n  const view = new DataView(utf8.buffer, utf8.byteOffset, utf8.byteLength);\n  let hi = hash32(view, utf8.length, 0);\n  let lo = hash32(view, utf8.length, 102072);\n  if (hi == 0 && (lo == 0 || lo == 1)) {\n    hi = hi ^ 0x130f9bef;\n    lo = lo ^ -0x6b5f56d8;\n  }\n  return BigInt.asUintN(32, BigInt(hi)) << BigInt(32) | BigInt.asUintN(32, BigInt(lo));\n}\nfunction computeMsgId(msg, meaning = '') {\n  let msgFingerprint = fingerprint(msg);\n  if (meaning) {\n    // Rotate the 64-bit message fingerprint one bit to the left and then add the meaning\n    // fingerprint.\n    msgFingerprint = BigInt.asUintN(64, msgFingerprint << BigInt(1)) | msgFingerprint >> BigInt(63) & BigInt(1);\n    msgFingerprint += fingerprint(meaning);\n  }\n  return BigInt.asUintN(63, msgFingerprint).toString();\n}\nfunction hash32(view, length, c) {\n  let a = 0x9e3779b9,\n    b = 0x9e3779b9;\n  let index = 0;\n  const end = length - 12;\n  for (; index <= end; index += 12) {\n    a += view.getUint32(index, true);\n    b += view.getUint32(index + 4, true);\n    c += view.getUint32(index + 8, true);\n    const res = mix(a, b, c);\n    a = res[0], b = res[1], c = res[2];\n  }\n  const remainder = length - index;\n  // the first byte of c is reserved for the length\n  c += length;\n  if (remainder >= 4) {\n    a += view.getUint32(index, true);\n    index += 4;\n    if (remainder >= 8) {\n      b += view.getUint32(index, true);\n      index += 4;\n      // Partial 32-bit word for c\n      if (remainder >= 9) {\n        c += view.getUint8(index++) << 8;\n      }\n      if (remainder >= 10) {\n        c += view.getUint8(index++) << 16;\n      }\n      if (remainder === 11) {\n        c += view.getUint8(index++) << 24;\n      }\n    } else {\n      // Partial 32-bit word for b\n      if (remainder >= 5) {\n        b += view.getUint8(index++);\n      }\n      if (remainder >= 6) {\n        b += view.getUint8(index++) << 8;\n      }\n      if (remainder === 7) {\n        b += view.getUint8(index++) << 16;\n      }\n    }\n  } else {\n    // Partial 32-bit word for a\n    if (remainder >= 1) {\n      a += view.getUint8(index++);\n    }\n    if (remainder >= 2) {\n      a += view.getUint8(index++) << 8;\n    }\n    if (remainder === 3) {\n      a += view.getUint8(index++) << 16;\n    }\n  }\n  return mix(a, b, c)[2];\n}\nfunction mix(a, b, c) {\n  a -= b;\n  a -= c;\n  a ^= c >>> 13;\n  b -= c;\n  b -= a;\n  b ^= a << 8;\n  c -= a;\n  c -= b;\n  c ^= b >>> 13;\n  a -= b;\n  a -= c;\n  a ^= c >>> 12;\n  b -= c;\n  b -= a;\n  b ^= a << 16;\n  c -= a;\n  c -= b;\n  c ^= b >>> 5;\n  a -= b;\n  a -= c;\n  a ^= c >>> 3;\n  b -= c;\n  b -= a;\n  b ^= a << 10;\n  c -= a;\n  c -= b;\n  c ^= b >>> 15;\n  return [a, b, c];\n}\n// Utils\nvar Endian;\n(function (Endian) {\n  Endian[Endian[\"Little\"] = 0] = \"Little\";\n  Endian[Endian[\"Big\"] = 1] = \"Big\";\n})(Endian || (Endian = {}));\nfunction add32(a, b) {\n  return add32to64(a, b)[1];\n}\nfunction add32to64(a, b) {\n  const low = (a & 0xffff) + (b & 0xffff);\n  const high = (a >>> 16) + (b >>> 16) + (low >>> 16);\n  return [high >>> 16, high << 16 | low & 0xffff];\n}\n// Rotate a 32b number left `count` position\nfunction rol32(a, count) {\n  return a << count | a >>> 32 - count;\n}\nfunction bytesToWords32(bytes, endian) {\n  const size = bytes.length + 3 >>> 2;\n  const words32 = [];\n  for (let i = 0; i < size; i++) {\n    words32[i] = wordAt(bytes, i * 4, endian);\n  }\n  return words32;\n}\nfunction byteAt(bytes, index) {\n  return index >= bytes.length ? 0 : bytes[index];\n}\nfunction wordAt(bytes, index, endian) {\n  let word = 0;\n  if (endian === Endian.Big) {\n    for (let i = 0; i < 4; i++) {\n      word += byteAt(bytes, index + i) << 24 - 8 * i;\n    }\n  } else {\n    for (let i = 0; i < 4; i++) {\n      word += byteAt(bytes, index + i) << 8 * i;\n    }\n  }\n  return word;\n}\n\n// This module specifier is intentionally a relative path to allow bundling the code directly\n/**\n * Parse a `$localize` tagged string into a structure that can be used for translation or\n * extraction.\n *\n * See `ParsedMessage` for an example.\n */\nfunction parseMessage(messageParts, expressions, location, messagePartLocations, expressionLocations = []) {\n  const substitutions = {};\n  const substitutionLocations = {};\n  const associatedMessageIds = {};\n  const metadata = parseMetadata(messageParts[0], messageParts.raw[0]);\n  const cleanedMessageParts = [metadata.text];\n  const placeholderNames = [];\n  let messageString = metadata.text;\n  for (let i = 1; i < messageParts.length; i++) {\n    const {\n      messagePart,\n      placeholderName = computePlaceholderName(i),\n      associatedMessageId\n    } = parsePlaceholder(messageParts[i], messageParts.raw[i]);\n    messageString += `{$${placeholderName}}${messagePart}`;\n    if (expressions !== undefined) {\n      substitutions[placeholderName] = expressions[i - 1];\n      substitutionLocations[placeholderName] = expressionLocations[i - 1];\n    }\n    placeholderNames.push(placeholderName);\n    if (associatedMessageId !== undefined) {\n      associatedMessageIds[placeholderName] = associatedMessageId;\n    }\n    cleanedMessageParts.push(messagePart);\n  }\n  const messageId = metadata.customId || computeMsgId(messageString, metadata.meaning || '');\n  const legacyIds = metadata.legacyIds ? metadata.legacyIds.filter(id => id !== messageId) : [];\n  return {\n    id: messageId,\n    legacyIds,\n    substitutions,\n    substitutionLocations,\n    text: messageString,\n    customId: metadata.customId,\n    meaning: metadata.meaning || '',\n    description: metadata.description || '',\n    messageParts: cleanedMessageParts,\n    messagePartLocations,\n    placeholderNames,\n    associatedMessageIds,\n    location\n  };\n}\n/**\n * Parse the given message part (`cooked` + `raw`) to extract the message metadata from the text.\n *\n * If the message part has a metadata block this function will extract the `meaning`,\n * `description`, `customId` and `legacyId` (if provided) from the block. These metadata properties\n * are serialized in the string delimited by `|`, `@@` and `␟` respectively.\n *\n * (Note that `␟` is the `LEGACY_ID_INDICATOR` - see `constants.ts`.)\n *\n * For example:\n *\n * ```ts\n * `:meaning|description@@custom-id:`\n * `:meaning|@@custom-id:`\n * `:meaning|description:`\n * `:description@@custom-id:`\n * `:meaning|:`\n * `:description:`\n * `:@@custom-id:`\n * `:meaning|description@@custom-id␟legacy-id-1␟legacy-id-2:`\n * ```\n *\n * @param cooked The cooked version of the message part to parse.\n * @param raw The raw version of the message part to parse.\n * @returns A object containing any metadata that was parsed from the message part.\n */\nfunction parseMetadata(cooked, raw) {\n  const {\n    text: messageString,\n    block\n  } = splitBlock(cooked, raw);\n  if (block === undefined) {\n    return {\n      text: messageString\n    };\n  } else {\n    const [meaningDescAndId, ...legacyIds] = block.split(LEGACY_ID_INDICATOR);\n    const [meaningAndDesc, customId] = meaningDescAndId.split(ID_SEPARATOR, 2);\n    let [meaning, description] = meaningAndDesc.split(MEANING_SEPARATOR, 2);\n    if (description === undefined) {\n      description = meaning;\n      meaning = undefined;\n    }\n    if (description === '') {\n      description = undefined;\n    }\n    return {\n      text: messageString,\n      meaning,\n      description,\n      customId,\n      legacyIds\n    };\n  }\n}\n/**\n * Parse the given message part (`cooked` + `raw`) to extract any placeholder metadata from the\n * text.\n *\n * If the message part has a metadata block this function will extract the `placeholderName` and\n * `associatedMessageId` (if provided) from the block.\n *\n * These metadata properties are serialized in the string delimited by `@@`.\n *\n * For example:\n *\n * ```ts\n * `:placeholder-name@@associated-id:`\n * ```\n *\n * @param cooked The cooked version of the message part to parse.\n * @param raw The raw version of the message part to parse.\n * @returns A object containing the metadata (`placeholderName` and `associatedMessageId`) of the\n *     preceding placeholder, along with the static text that follows.\n */\nfunction parsePlaceholder(cooked, raw) {\n  const {\n    text: messagePart,\n    block\n  } = splitBlock(cooked, raw);\n  if (block === undefined) {\n    return {\n      messagePart\n    };\n  } else {\n    const [placeholderName, associatedMessageId] = block.split(ID_SEPARATOR);\n    return {\n      messagePart,\n      placeholderName,\n      associatedMessageId\n    };\n  }\n}\n/**\n * Split a message part (`cooked` + `raw`) into an optional delimited \"block\" off the front and the\n * rest of the text of the message part.\n *\n * Blocks appear at the start of message parts. They are delimited by a colon `:` character at the\n * start and end of the block.\n *\n * If the block is in the first message part then it will be metadata about the whole message:\n * meaning, description, id.  Otherwise it will be metadata about the immediately preceding\n * substitution: placeholder name.\n *\n * Since blocks are optional, it is possible that the content of a message block actually starts\n * with a block marker. In this case the marker must be escaped `\\:`.\n *\n * @param cooked The cooked version of the message part to parse.\n * @param raw The raw version of the message part to parse.\n * @returns An object containing the `text` of the message part and the text of the `block`, if it\n * exists.\n * @throws an error if the `block` is unterminated\n */\nfunction splitBlock(cooked, raw) {\n  if (raw.charAt(0) !== BLOCK_MARKER$1) {\n    return {\n      text: cooked\n    };\n  } else {\n    const endOfBlock = findEndOfBlock(cooked, raw);\n    return {\n      block: cooked.substring(1, endOfBlock),\n      text: cooked.substring(endOfBlock + 1)\n    };\n  }\n}\nfunction computePlaceholderName(index) {\n  return index === 1 ? 'PH' : `PH_${index - 1}`;\n}\n/**\n * Find the end of a \"marked block\" indicated by the first non-escaped colon.\n *\n * @param cooked The cooked string (where escaped chars have been processed)\n * @param raw The raw string (where escape sequences are still in place)\n *\n * @returns the index of the end of block marker\n * @throws an error if the block is unterminated\n */\nfunction findEndOfBlock(cooked, raw) {\n  for (let cookedIndex = 1, rawIndex = 1; cookedIndex < cooked.length; cookedIndex++, rawIndex++) {\n    if (raw[rawIndex] === '\\\\') {\n      rawIndex++;\n    } else if (cooked[cookedIndex] === BLOCK_MARKER$1) {\n      return cookedIndex;\n    }\n  }\n  throw new Error(`Unterminated $localize metadata block in \"${raw}\".`);\n}\nclass MissingTranslationError extends Error {\n  constructor(parsedMessage) {\n    super(`No translation found for ${describeMessage(parsedMessage)}.`);\n    this.parsedMessage = parsedMessage;\n    this.type = 'MissingTranslationError';\n  }\n}\nfunction isMissingTranslationError(e) {\n  return e.type === 'MissingTranslationError';\n}\n/**\n * Translate the text of the `$localize` tagged-string (i.e. `messageParts` and\n * `substitutions`) using the given `translations`.\n *\n * The tagged-string is parsed to extract its `messageId` which is used to find an appropriate\n * `ParsedTranslation`. If this doesn't match and there are legacy ids then try matching a\n * translation using those.\n *\n * If one is found then it is used to translate the message into a new set of `messageParts` and\n * `substitutions`.\n * The translation may reorder (or remove) substitutions as appropriate.\n *\n * If there is no translation with a matching message id then an error is thrown.\n * If a translation contains a placeholder that is not found in the message being translated then an\n * error is thrown.\n */\nfunction translate$1(translations, messageParts, substitutions) {\n  const message = parseMessage(messageParts, substitutions);\n  // Look up the translation using the messageId, and then the legacyId if available.\n  let translation = translations[message.id];\n  // If the messageId did not match a translation, try matching the legacy ids instead\n  if (message.legacyIds !== undefined) {\n    for (let i = 0; i < message.legacyIds.length && translation === undefined; i++) {\n      translation = translations[message.legacyIds[i]];\n    }\n  }\n  if (translation === undefined) {\n    throw new MissingTranslationError(message);\n  }\n  return [translation.messageParts, translation.placeholderNames.map(placeholder => {\n    if (message.substitutions.hasOwnProperty(placeholder)) {\n      return message.substitutions[placeholder];\n    } else {\n      throw new Error(`There is a placeholder name mismatch with the translation provided for the message ${describeMessage(message)}.\\n` + `The translation contains a placeholder with name ${placeholder}, which does not exist in the message.`);\n    }\n  })];\n}\n/**\n * Parse the `messageParts` and `placeholderNames` out of a target `message`.\n *\n * Used by `loadTranslations()` to convert target message strings into a structure that is more\n * appropriate for doing translation.\n *\n * @param message the message to be parsed.\n */\nfunction parseTranslation(messageString) {\n  const parts = messageString.split(/{\\$([^}]*)}/);\n  const messageParts = [parts[0]];\n  const placeholderNames = [];\n  for (let i = 1; i < parts.length - 1; i += 2) {\n    placeholderNames.push(parts[i]);\n    messageParts.push(`${parts[i + 1]}`);\n  }\n  const rawMessageParts = messageParts.map(part => part.charAt(0) === BLOCK_MARKER$1 ? '\\\\' + part : part);\n  return {\n    text: messageString,\n    messageParts: makeTemplateObject(messageParts, rawMessageParts),\n    placeholderNames\n  };\n}\n/**\n * Create a `ParsedTranslation` from a set of `messageParts` and `placeholderNames`.\n *\n * @param messageParts The message parts to appear in the ParsedTranslation.\n * @param placeholderNames The names of the placeholders to intersperse between the `messageParts`.\n */\nfunction makeParsedTranslation(messageParts, placeholderNames = []) {\n  let messageString = messageParts[0];\n  for (let i = 0; i < placeholderNames.length; i++) {\n    messageString += `{$${placeholderNames[i]}}${messageParts[i + 1]}`;\n  }\n  return {\n    text: messageString,\n    messageParts: makeTemplateObject(messageParts, messageParts),\n    placeholderNames\n  };\n}\n/**\n * Create the specialized array that is passed to tagged-string tag functions.\n *\n * @param cooked The message parts with their escape codes processed.\n * @param raw The message parts with their escaped codes as-is.\n */\nfunction makeTemplateObject(cooked, raw) {\n  Object.defineProperty(cooked, 'raw', {\n    value: raw\n  });\n  return cooked;\n}\nfunction describeMessage(message) {\n  const meaningString = message.meaning && ` - \"${message.meaning}\"`;\n  const legacy = message.legacyIds && message.legacyIds.length > 0 ? ` [${message.legacyIds.map(l => `\"${l}\"`).join(', ')}]` : '';\n  return `\"${message.id}\"${legacy} (\"${message.text}\"${meaningString})`;\n}\n\n/**\n * Load translations for use by `$localize`, if doing runtime translation.\n *\n * If the `$localize` tagged strings are not going to be replaced at compiled time, it is possible\n * to load a set of translations that will be applied to the `$localize` tagged strings at runtime,\n * in the browser.\n *\n * Loading a new translation will overwrite a previous translation if it has the same `MessageId`.\n *\n * Note that `$localize` messages are only processed once, when the tagged string is first\n * encountered, and does not provide dynamic language changing without refreshing the browser.\n * Loading new translations later in the application life-cycle will not change the translated text\n * of messages that have already been translated.\n *\n * The message IDs and translations are in the same format as that rendered to \"simple JSON\"\n * translation files when extracting messages. In particular, placeholders in messages are rendered\n * using the `{$PLACEHOLDER_NAME}` syntax. For example the message from the following template:\n *\n * ```html\n * <div i18n>pre<span>inner-pre<b>bold</b>inner-post</span>post</div>\n * ```\n *\n * would have the following form in the `translations` map:\n *\n * ```ts\n * {\n *   \"2932901491976224757\":\n *      \"pre{$START_TAG_SPAN}inner-pre{$START_BOLD_TEXT}bold{$CLOSE_BOLD_TEXT}inner-post{$CLOSE_TAG_SPAN}post\"\n * }\n * ```\n *\n * @param translations A map from message ID to translated message.\n *\n * These messages are processed and added to a lookup based on their `MessageId`.\n *\n * @see {@link clearTranslations} for removing translations loaded using this function.\n * @see {@link $localize} for tagging messages as needing to be translated.\n * @publicApi\n */\nfunction loadTranslations(translations) {\n  // Ensure the translate function exists\n  if (!$localize.translate) {\n    $localize.translate = translate;\n  }\n  if (!$localize.TRANSLATIONS) {\n    $localize.TRANSLATIONS = {};\n  }\n  Object.keys(translations).forEach(key => {\n    $localize.TRANSLATIONS[key] = parseTranslation(translations[key]);\n  });\n}\n/**\n * Remove all translations for `$localize`, if doing runtime translation.\n *\n * All translations that had been loading into memory using `loadTranslations()` will be removed.\n *\n * @see {@link loadTranslations} for loading translations at runtime.\n * @see {@link $localize} for tagging messages as needing to be translated.\n *\n * @publicApi\n */\nfunction clearTranslations() {\n  $localize.translate = undefined;\n  $localize.TRANSLATIONS = {};\n}\n/**\n * Translate the text of the given message, using the loaded translations.\n *\n * This function may reorder (or remove) substitutions as indicated in the matching translation.\n */\nfunction translate(messageParts, substitutions) {\n  try {\n    return translate$1($localize.TRANSLATIONS, messageParts, substitutions);\n  } catch (e) {\n    console.warn(e.message);\n    return [messageParts, substitutions];\n  }\n}\n\n/**\n * Tag a template literal string for localization.\n *\n * For example:\n *\n * ```ts\n * $localize `some string to localize`\n * ```\n *\n * **Providing meaning, description and id**\n *\n * You can optionally specify one or more of `meaning`, `description` and `id` for a localized\n * string by pre-pending it with a colon delimited block of the form:\n *\n * ```ts\n * $localize`:meaning|description@@id:source message text`;\n *\n * $localize`:meaning|:source message text`;\n * $localize`:description:source message text`;\n * $localize`:@@id:source message text`;\n * ```\n *\n * This format is the same as that used for `i18n` markers in Angular templates. See the\n * [Angular i18n guide](guide/i18n/prepare#mark-text-in-component-template).\n *\n * **Naming placeholders**\n *\n * If the template literal string contains expressions, then the expressions will be automatically\n * associated with placeholder names for you.\n *\n * For example:\n *\n * ```ts\n * $localize `Hi ${name}! There are ${items.length} items.`;\n * ```\n *\n * will generate a message-source of `Hi {$PH}! There are {$PH_1} items`.\n *\n * The recommended practice is to name the placeholder associated with each expression though.\n *\n * Do this by providing the placeholder name wrapped in `:` characters directly after the\n * expression. These placeholder names are stripped out of the rendered localized string.\n *\n * For example, to name the `items.length` expression placeholder `itemCount` you write:\n *\n * ```ts\n * $localize `There are ${items.length}:itemCount: items`;\n * ```\n *\n * **Escaping colon markers**\n *\n * If you need to use a `:` character directly at the start of a tagged string that has no\n * metadata block, or directly after a substitution expression that has no name you must escape\n * the `:` by preceding it with a backslash:\n *\n * For example:\n *\n * ```ts\n * // message has a metadata block so no need to escape colon\n * $localize `:some description::this message starts with a colon (:)`;\n * // no metadata block so the colon must be escaped\n * $localize `\\:this message starts with a colon (:)`;\n * ```\n *\n * ```ts\n * // named substitution so no need to escape colon\n * $localize `${label}:label:: ${}`\n * // anonymous substitution so colon must be escaped\n * $localize `${label}\\: ${}`\n * ```\n *\n * **Processing localized strings:**\n *\n * There are three scenarios:\n *\n * * **compile-time inlining**: the `$localize` tag is transformed at compile time by a\n * transpiler, removing the tag and replacing the template literal string with a translated\n * literal string from a collection of translations provided to the transpilation tool.\n *\n * * **run-time evaluation**: the `$localize` tag is a run-time function that replaces and\n * reorders the parts (static strings and expressions) of the template literal string with strings\n * from a collection of translations loaded at run-time.\n *\n * * **pass-through evaluation**: the `$localize` tag is a run-time function that simply evaluates\n * the original template literal string without applying any translations to the parts. This\n * version is used during development or where there is no need to translate the localized\n * template literals.\n *\n * @param messageParts a collection of the static parts of the template string.\n * @param expressions a collection of the values of each placeholder in the template string.\n * @returns the translated string, with the `messageParts` and `expressions` interleaved together.\n *\n * @globalApi\n * @publicApi\n */\nconst $localize$1 = function (messageParts, ...expressions) {\n  if ($localize$1.translate) {\n    // Don't use array expansion here to avoid the compiler adding `__read()` helper unnecessarily.\n    const translation = $localize$1.translate(messageParts, expressions);\n    messageParts = translation[0];\n    expressions = translation[1];\n  }\n  let message = stripBlock(messageParts[0], messageParts.raw[0]);\n  for (let i = 1; i < messageParts.length; i++) {\n    message += expressions[i - 1] + stripBlock(messageParts[i], messageParts.raw[i]);\n  }\n  return message;\n};\nconst BLOCK_MARKER = ':';\n/**\n * Strip a delimited \"block\" from the start of the `messagePart`, if it is found.\n *\n * If a marker character (:) actually appears in the content at the start of a tagged string or\n * after a substitution expression, where a block has not been provided the character must be\n * escaped with a backslash, `\\:`. This function checks for this by looking at the `raw`\n * messagePart, which should still contain the backslash.\n *\n * @param messagePart The cooked message part to process.\n * @param rawMessagePart The raw message part to check.\n * @returns the message part with the placeholder name stripped, if found.\n * @throws an error if the block is unterminated\n */\nfunction stripBlock(messagePart, rawMessagePart) {\n  return rawMessagePart.charAt(0) === BLOCK_MARKER ? messagePart.substring(findEndOfBlock(messagePart, rawMessagePart) + 1) : messagePart;\n}\n\n// This file exports all the `utils` as private exports so that other parts of `@angular/localize`\n\n// This file contains the public API of the `@angular/localize` entry-point\n\n// DO NOT ADD public exports to this file.\n\nexport { clearTranslations, loadTranslations, $localize$1 as ɵ$localize, MissingTranslationError as ɵMissingTranslationError, computeMsgId as ɵcomputeMsgId, findEndOfBlock as ɵfindEndOfBlock, isMissingTranslationError as ɵisMissingTranslationError, makeParsedTranslation as ɵmakeParsedTranslation, makeTemplateObject as ɵmakeTemplateObject, parseMessage as ɵparseMessage, parseMetadata as ɵparseMetadata, parseTranslation as ɵparseTranslation, splitBlock as ɵsplitBlock, translate$1 as ɵtranslate };", "map": {"version": 3, "names": ["BLOCK_MARKER$1", "MEANING_SEPARATOR", "ID_SEPARATOR", "LEGACY_ID_INDICATOR", "textEncoder", "digest", "message", "id", "computeDigest", "sha1", "serializeNodes", "nodes", "join", "meaning", "decimalDigest", "preservePlaceholders", "computeDecimalDigest", "visitor", "_SerializerIgnoreExpVisitor", "parts", "map", "a", "visit", "computeMsgId", "_SerializerVisitor", "visitText", "text", "context", "value", "visitContainer", "container", "children", "child", "visitIcu", "icu", "strCases", "Object", "keys", "cases", "k", "expression", "type", "visitTagPlaceholder", "ph", "isVoid", "startName", "closeName", "visitPlaceholder", "name", "visitIcuPlaceholder", "visitBlockPlaceholder", "serializerVisitor", "constructor", "str", "TextEncoder", "utf8", "encode", "words32", "bytesToWords32", "<PERSON><PERSON>", "Big", "len", "length", "w", "Uint32Array", "b", "c", "d", "e", "i", "h0", "h1", "h2", "h3", "h4", "j", "rol32", "fkVal", "fk", "f", "temp", "reduce", "add32", "toHexU32", "toString", "padStart", "index", "fingerprint", "view", "DataView", "buffer", "byteOffset", "byteLength", "hi", "hash32", "lo", "BigInt", "asUintN", "msg", "msgFingerprint", "end", "getUint32", "res", "mix", "remainder", "getUint8", "add32to64", "low", "high", "count", "bytes", "endian", "size", "wordAt", "byteAt", "word", "parseMessage", "messageParts", "expressions", "location", "messagePartLocations", "expressionLocations", "substitutions", "substitutionLocations", "associatedMessageIds", "metadata", "parseMetadata", "raw", "cleanedMessageParts", "placeholder<PERSON><PERSON><PERSON>", "messageString", "messagePart", "placeholder<PERSON><PERSON>", "computePlaceholderName", "associatedMessageId", "parsePlaceholder", "undefined", "push", "messageId", "customId", "legacyIds", "filter", "description", "cooked", "block", "splitBlock", "meaningDescAndId", "split", "meaningAndDesc", "char<PERSON>t", "endOfBlock", "findEndOfBlock", "substring", "cookedIndex", "rawIndex", "Error", "MissingTranslationError", "parsedMessage", "describeMessage", "isMissingTranslationError", "translate$1", "translations", "translation", "placeholder", "hasOwnProperty", "parseTranslation", "rawMessageParts", "part", "makeTemplateObject", "makeParsedTranslation", "defineProperty", "meaningString", "legacy", "l", "loadTranslations", "$localize", "translate", "TRANSLATIONS", "for<PERSON>ach", "key", "clearTranslations", "console", "warn", "$localize$1", "stripBlock", "BLOCK_MARKER", "rawMessagePart", "ɵ$localize", "ɵMissingTranslationError", "ɵcomputeMsgId", "ɵfindEndOfBlock", "ɵisMissingTranslationError", "ɵmakeParsedTranslation", "ɵmakeTemplateObject", "ɵparseMessage", "ɵparseMetadata", "ɵparseTranslation", "ɵsplitBlock", "ɵtranslate"], "sources": ["C:/Users/<USER>/Documents/salechange-product/adminFront/node_modules/@angular/localize/fesm2022/localize.mjs"], "sourcesContent": ["/**\n * @license Angular v18.2.9\n * (c) 2010-2024 Google LLC. https://angular.io/\n * License: MIT\n */\n\n/**\n * The character used to mark the start and end of a \"block\" in a `$localize` tagged string.\n * A block can indicate metadata about the message or specify a name of a placeholder for a\n * substitution expressions.\n *\n * For example:\n *\n * ```ts\n * $localize`Hello, ${title}:title:!`;\n * $localize`:meaning|description@@id:source message text`;\n * ```\n */\nconst BLOCK_MARKER$1 = ':';\n/**\n * The marker used to separate a message's \"meaning\" from its \"description\" in a metadata block.\n *\n * For example:\n *\n * ```ts\n * $localize `:correct|Indicates that the user got the answer correct: Right!`;\n * $localize `:movement|Button label for moving to the right: Right!`;\n * ```\n */\nconst MEANING_SEPARATOR = '|';\n/**\n * The marker used to separate a message's custom \"id\" from its \"description\" in a metadata block.\n *\n * For example:\n *\n * ```ts\n * $localize `:A welcome message on the home page@@myApp-homepage-welcome: Welcome!`;\n * ```\n */\nconst ID_SEPARATOR = '@@';\n/**\n * The marker used to separate legacy message ids from the rest of a metadata block.\n *\n * For example:\n *\n * ```ts\n * $localize `:@@custom-id␟2df64767cd895a8fabe3e18b94b5b6b6f9e2e3f0: Welcome!`;\n * ```\n *\n * Note that this character is the \"symbol for the unit separator\" (␟) not the \"unit separator\n * character\" itself, since that has no visual representation. See https://graphemica.com/%E2%90%9F.\n *\n * Here is some background for the original \"unit separator character\":\n * https://stackoverflow.com/questions/8695118/whats-the-file-group-record-unit-separator-control-characters-and-its-usage\n */\nconst LEGACY_ID_INDICATOR = '\\u241F';\n\n/**\n * A lazily created TextEncoder instance for converting strings into UTF-8 bytes\n */\nlet textEncoder;\n/**\n * Return the message id or compute it using the XLIFF1 digest.\n */\nfunction digest(message) {\n    return message.id || computeDigest(message);\n}\n/**\n * Compute the message id using the XLIFF1 digest.\n */\nfunction computeDigest(message) {\n    return sha1(serializeNodes(message.nodes).join('') + `[${message.meaning}]`);\n}\n/**\n * Return the message id or compute it using the XLIFF2/XMB/$localize digest.\n */\nfunction decimalDigest(message, preservePlaceholders) {\n    return message.id || computeDecimalDigest(message, preservePlaceholders);\n}\n/**\n * Compute the message id using the XLIFF2/XMB/$localize digest.\n */\nfunction computeDecimalDigest(message, preservePlaceholders) {\n    const visitor = new _SerializerIgnoreExpVisitor(preservePlaceholders);\n    const parts = message.nodes.map((a) => a.visit(visitor, null));\n    return computeMsgId(parts.join(''), message.meaning);\n}\n/**\n * Serialize the i18n ast to something xml-like in order to generate an UID.\n *\n * The visitor is also used in the i18n parser tests\n *\n * @internal\n */\nclass _SerializerVisitor {\n    visitText(text, context) {\n        return text.value;\n    }\n    visitContainer(container, context) {\n        return `[${container.children.map((child) => child.visit(this)).join(', ')}]`;\n    }\n    visitIcu(icu, context) {\n        const strCases = Object.keys(icu.cases).map((k) => `${k} {${icu.cases[k].visit(this)}}`);\n        return `{${icu.expression}, ${icu.type}, ${strCases.join(', ')}}`;\n    }\n    visitTagPlaceholder(ph, context) {\n        return ph.isVoid\n            ? `<ph tag name=\"${ph.startName}\"/>`\n            : `<ph tag name=\"${ph.startName}\">${ph.children\n                .map((child) => child.visit(this))\n                .join(', ')}</ph name=\"${ph.closeName}\">`;\n    }\n    visitPlaceholder(ph, context) {\n        return ph.value ? `<ph name=\"${ph.name}\">${ph.value}</ph>` : `<ph name=\"${ph.name}\"/>`;\n    }\n    visitIcuPlaceholder(ph, context) {\n        return `<ph icu name=\"${ph.name}\">${ph.value.visit(this)}</ph>`;\n    }\n    visitBlockPlaceholder(ph, context) {\n        return `<ph block name=\"${ph.startName}\">${ph.children\n            .map((child) => child.visit(this))\n            .join(', ')}</ph name=\"${ph.closeName}\">`;\n    }\n}\nconst serializerVisitor = new _SerializerVisitor();\nfunction serializeNodes(nodes) {\n    return nodes.map((a) => a.visit(serializerVisitor, null));\n}\n/**\n * Serialize the i18n ast to something xml-like in order to generate an UID.\n *\n * Ignore the expressions so that message IDs stays identical if only the expression changes.\n *\n * @internal\n */\nclass _SerializerIgnoreExpVisitor extends _SerializerVisitor {\n    constructor(preservePlaceholders) {\n        super();\n        this.preservePlaceholders = preservePlaceholders;\n    }\n    visitPlaceholder(ph, context) {\n        // Do not take the expression into account when `preservePlaceholders` is disabled.\n        return this.preservePlaceholders\n            ? super.visitPlaceholder(ph, context)\n            : `<ph name=\"${ph.name}\"/>`;\n    }\n    visitIcu(icu) {\n        let strCases = Object.keys(icu.cases).map((k) => `${k} {${icu.cases[k].visit(this)}}`);\n        // Do not take the expression into account\n        return `{${icu.type}, ${strCases.join(', ')}}`;\n    }\n}\n/**\n * Compute the SHA1 of the given string\n *\n * see https://csrc.nist.gov/publications/fips/fips180-4/fips-180-4.pdf\n *\n * WARNING: this function has not been designed not tested with security in mind.\n *          DO NOT USE IT IN A SECURITY SENSITIVE CONTEXT.\n */\nfunction sha1(str) {\n    textEncoder ??= new TextEncoder();\n    const utf8 = [...textEncoder.encode(str)];\n    const words32 = bytesToWords32(utf8, Endian.Big);\n    const len = utf8.length * 8;\n    const w = new Uint32Array(80);\n    let a = 0x67452301, b = 0xefcdab89, c = 0x98badcfe, d = 0x10325476, e = 0xc3d2e1f0;\n    words32[len >> 5] |= 0x80 << (24 - (len % 32));\n    words32[(((len + 64) >> 9) << 4) + 15] = len;\n    for (let i = 0; i < words32.length; i += 16) {\n        const h0 = a, h1 = b, h2 = c, h3 = d, h4 = e;\n        for (let j = 0; j < 80; j++) {\n            if (j < 16) {\n                w[j] = words32[i + j];\n            }\n            else {\n                w[j] = rol32(w[j - 3] ^ w[j - 8] ^ w[j - 14] ^ w[j - 16], 1);\n            }\n            const fkVal = fk(j, b, c, d);\n            const f = fkVal[0];\n            const k = fkVal[1];\n            const temp = [rol32(a, 5), f, e, k, w[j]].reduce(add32);\n            e = d;\n            d = c;\n            c = rol32(b, 30);\n            b = a;\n            a = temp;\n        }\n        a = add32(a, h0);\n        b = add32(b, h1);\n        c = add32(c, h2);\n        d = add32(d, h3);\n        e = add32(e, h4);\n    }\n    // Convert the output parts to a 160-bit hexadecimal string\n    return toHexU32(a) + toHexU32(b) + toHexU32(c) + toHexU32(d) + toHexU32(e);\n}\n/**\n * Convert and format a number as a string representing a 32-bit unsigned hexadecimal number.\n * @param value The value to format as a string.\n * @returns A hexadecimal string representing the value.\n */\nfunction toHexU32(value) {\n    // unsigned right shift of zero ensures an unsigned 32-bit number\n    return (value >>> 0).toString(16).padStart(8, '0');\n}\nfunction fk(index, b, c, d) {\n    if (index < 20) {\n        return [(b & c) | (~b & d), 0x5a827999];\n    }\n    if (index < 40) {\n        return [b ^ c ^ d, 0x6ed9eba1];\n    }\n    if (index < 60) {\n        return [(b & c) | (b & d) | (c & d), 0x8f1bbcdc];\n    }\n    return [b ^ c ^ d, 0xca62c1d6];\n}\n/**\n * Compute the fingerprint of the given string\n *\n * The output is 64 bit number encoded as a decimal string\n *\n * based on:\n * https://github.com/google/closure-compiler/blob/master/src/com/google/javascript/jscomp/GoogleJsMessageIdGenerator.java\n */\nfunction fingerprint(str) {\n    textEncoder ??= new TextEncoder();\n    const utf8 = textEncoder.encode(str);\n    const view = new DataView(utf8.buffer, utf8.byteOffset, utf8.byteLength);\n    let hi = hash32(view, utf8.length, 0);\n    let lo = hash32(view, utf8.length, 102072);\n    if (hi == 0 && (lo == 0 || lo == 1)) {\n        hi = hi ^ 0x130f9bef;\n        lo = lo ^ -0x6b5f56d8;\n    }\n    return (BigInt.asUintN(32, BigInt(hi)) << BigInt(32)) | BigInt.asUintN(32, BigInt(lo));\n}\nfunction computeMsgId(msg, meaning = '') {\n    let msgFingerprint = fingerprint(msg);\n    if (meaning) {\n        // Rotate the 64-bit message fingerprint one bit to the left and then add the meaning\n        // fingerprint.\n        msgFingerprint =\n            BigInt.asUintN(64, msgFingerprint << BigInt(1)) |\n                ((msgFingerprint >> BigInt(63)) & BigInt(1));\n        msgFingerprint += fingerprint(meaning);\n    }\n    return BigInt.asUintN(63, msgFingerprint).toString();\n}\nfunction hash32(view, length, c) {\n    let a = 0x9e3779b9, b = 0x9e3779b9;\n    let index = 0;\n    const end = length - 12;\n    for (; index <= end; index += 12) {\n        a += view.getUint32(index, true);\n        b += view.getUint32(index + 4, true);\n        c += view.getUint32(index + 8, true);\n        const res = mix(a, b, c);\n        (a = res[0]), (b = res[1]), (c = res[2]);\n    }\n    const remainder = length - index;\n    // the first byte of c is reserved for the length\n    c += length;\n    if (remainder >= 4) {\n        a += view.getUint32(index, true);\n        index += 4;\n        if (remainder >= 8) {\n            b += view.getUint32(index, true);\n            index += 4;\n            // Partial 32-bit word for c\n            if (remainder >= 9) {\n                c += view.getUint8(index++) << 8;\n            }\n            if (remainder >= 10) {\n                c += view.getUint8(index++) << 16;\n            }\n            if (remainder === 11) {\n                c += view.getUint8(index++) << 24;\n            }\n        }\n        else {\n            // Partial 32-bit word for b\n            if (remainder >= 5) {\n                b += view.getUint8(index++);\n            }\n            if (remainder >= 6) {\n                b += view.getUint8(index++) << 8;\n            }\n            if (remainder === 7) {\n                b += view.getUint8(index++) << 16;\n            }\n        }\n    }\n    else {\n        // Partial 32-bit word for a\n        if (remainder >= 1) {\n            a += view.getUint8(index++);\n        }\n        if (remainder >= 2) {\n            a += view.getUint8(index++) << 8;\n        }\n        if (remainder === 3) {\n            a += view.getUint8(index++) << 16;\n        }\n    }\n    return mix(a, b, c)[2];\n}\nfunction mix(a, b, c) {\n    a -= b;\n    a -= c;\n    a ^= c >>> 13;\n    b -= c;\n    b -= a;\n    b ^= a << 8;\n    c -= a;\n    c -= b;\n    c ^= b >>> 13;\n    a -= b;\n    a -= c;\n    a ^= c >>> 12;\n    b -= c;\n    b -= a;\n    b ^= a << 16;\n    c -= a;\n    c -= b;\n    c ^= b >>> 5;\n    a -= b;\n    a -= c;\n    a ^= c >>> 3;\n    b -= c;\n    b -= a;\n    b ^= a << 10;\n    c -= a;\n    c -= b;\n    c ^= b >>> 15;\n    return [a, b, c];\n}\n// Utils\nvar Endian;\n(function (Endian) {\n    Endian[Endian[\"Little\"] = 0] = \"Little\";\n    Endian[Endian[\"Big\"] = 1] = \"Big\";\n})(Endian || (Endian = {}));\nfunction add32(a, b) {\n    return add32to64(a, b)[1];\n}\nfunction add32to64(a, b) {\n    const low = (a & 0xffff) + (b & 0xffff);\n    const high = (a >>> 16) + (b >>> 16) + (low >>> 16);\n    return [high >>> 16, (high << 16) | (low & 0xffff)];\n}\n// Rotate a 32b number left `count` position\nfunction rol32(a, count) {\n    return (a << count) | (a >>> (32 - count));\n}\nfunction bytesToWords32(bytes, endian) {\n    const size = (bytes.length + 3) >>> 2;\n    const words32 = [];\n    for (let i = 0; i < size; i++) {\n        words32[i] = wordAt(bytes, i * 4, endian);\n    }\n    return words32;\n}\nfunction byteAt(bytes, index) {\n    return index >= bytes.length ? 0 : bytes[index];\n}\nfunction wordAt(bytes, index, endian) {\n    let word = 0;\n    if (endian === Endian.Big) {\n        for (let i = 0; i < 4; i++) {\n            word += byteAt(bytes, index + i) << (24 - 8 * i);\n        }\n    }\n    else {\n        for (let i = 0; i < 4; i++) {\n            word += byteAt(bytes, index + i) << (8 * i);\n        }\n    }\n    return word;\n}\n\n// This module specifier is intentionally a relative path to allow bundling the code directly\n/**\n * Parse a `$localize` tagged string into a structure that can be used for translation or\n * extraction.\n *\n * See `ParsedMessage` for an example.\n */\nfunction parseMessage(messageParts, expressions, location, messagePartLocations, expressionLocations = []) {\n    const substitutions = {};\n    const substitutionLocations = {};\n    const associatedMessageIds = {};\n    const metadata = parseMetadata(messageParts[0], messageParts.raw[0]);\n    const cleanedMessageParts = [metadata.text];\n    const placeholderNames = [];\n    let messageString = metadata.text;\n    for (let i = 1; i < messageParts.length; i++) {\n        const { messagePart, placeholderName = computePlaceholderName(i), associatedMessageId, } = parsePlaceholder(messageParts[i], messageParts.raw[i]);\n        messageString += `{$${placeholderName}}${messagePart}`;\n        if (expressions !== undefined) {\n            substitutions[placeholderName] = expressions[i - 1];\n            substitutionLocations[placeholderName] = expressionLocations[i - 1];\n        }\n        placeholderNames.push(placeholderName);\n        if (associatedMessageId !== undefined) {\n            associatedMessageIds[placeholderName] = associatedMessageId;\n        }\n        cleanedMessageParts.push(messagePart);\n    }\n    const messageId = metadata.customId || computeMsgId(messageString, metadata.meaning || '');\n    const legacyIds = metadata.legacyIds ? metadata.legacyIds.filter((id) => id !== messageId) : [];\n    return {\n        id: messageId,\n        legacyIds,\n        substitutions,\n        substitutionLocations,\n        text: messageString,\n        customId: metadata.customId,\n        meaning: metadata.meaning || '',\n        description: metadata.description || '',\n        messageParts: cleanedMessageParts,\n        messagePartLocations,\n        placeholderNames,\n        associatedMessageIds,\n        location,\n    };\n}\n/**\n * Parse the given message part (`cooked` + `raw`) to extract the message metadata from the text.\n *\n * If the message part has a metadata block this function will extract the `meaning`,\n * `description`, `customId` and `legacyId` (if provided) from the block. These metadata properties\n * are serialized in the string delimited by `|`, `@@` and `␟` respectively.\n *\n * (Note that `␟` is the `LEGACY_ID_INDICATOR` - see `constants.ts`.)\n *\n * For example:\n *\n * ```ts\n * `:meaning|description@@custom-id:`\n * `:meaning|@@custom-id:`\n * `:meaning|description:`\n * `:description@@custom-id:`\n * `:meaning|:`\n * `:description:`\n * `:@@custom-id:`\n * `:meaning|description@@custom-id␟legacy-id-1␟legacy-id-2:`\n * ```\n *\n * @param cooked The cooked version of the message part to parse.\n * @param raw The raw version of the message part to parse.\n * @returns A object containing any metadata that was parsed from the message part.\n */\nfunction parseMetadata(cooked, raw) {\n    const { text: messageString, block } = splitBlock(cooked, raw);\n    if (block === undefined) {\n        return { text: messageString };\n    }\n    else {\n        const [meaningDescAndId, ...legacyIds] = block.split(LEGACY_ID_INDICATOR);\n        const [meaningAndDesc, customId] = meaningDescAndId.split(ID_SEPARATOR, 2);\n        let [meaning, description] = meaningAndDesc.split(MEANING_SEPARATOR, 2);\n        if (description === undefined) {\n            description = meaning;\n            meaning = undefined;\n        }\n        if (description === '') {\n            description = undefined;\n        }\n        return { text: messageString, meaning, description, customId, legacyIds };\n    }\n}\n/**\n * Parse the given message part (`cooked` + `raw`) to extract any placeholder metadata from the\n * text.\n *\n * If the message part has a metadata block this function will extract the `placeholderName` and\n * `associatedMessageId` (if provided) from the block.\n *\n * These metadata properties are serialized in the string delimited by `@@`.\n *\n * For example:\n *\n * ```ts\n * `:placeholder-name@@associated-id:`\n * ```\n *\n * @param cooked The cooked version of the message part to parse.\n * @param raw The raw version of the message part to parse.\n * @returns A object containing the metadata (`placeholderName` and `associatedMessageId`) of the\n *     preceding placeholder, along with the static text that follows.\n */\nfunction parsePlaceholder(cooked, raw) {\n    const { text: messagePart, block } = splitBlock(cooked, raw);\n    if (block === undefined) {\n        return { messagePart };\n    }\n    else {\n        const [placeholderName, associatedMessageId] = block.split(ID_SEPARATOR);\n        return { messagePart, placeholderName, associatedMessageId };\n    }\n}\n/**\n * Split a message part (`cooked` + `raw`) into an optional delimited \"block\" off the front and the\n * rest of the text of the message part.\n *\n * Blocks appear at the start of message parts. They are delimited by a colon `:` character at the\n * start and end of the block.\n *\n * If the block is in the first message part then it will be metadata about the whole message:\n * meaning, description, id.  Otherwise it will be metadata about the immediately preceding\n * substitution: placeholder name.\n *\n * Since blocks are optional, it is possible that the content of a message block actually starts\n * with a block marker. In this case the marker must be escaped `\\:`.\n *\n * @param cooked The cooked version of the message part to parse.\n * @param raw The raw version of the message part to parse.\n * @returns An object containing the `text` of the message part and the text of the `block`, if it\n * exists.\n * @throws an error if the `block` is unterminated\n */\nfunction splitBlock(cooked, raw) {\n    if (raw.charAt(0) !== BLOCK_MARKER$1) {\n        return { text: cooked };\n    }\n    else {\n        const endOfBlock = findEndOfBlock(cooked, raw);\n        return {\n            block: cooked.substring(1, endOfBlock),\n            text: cooked.substring(endOfBlock + 1),\n        };\n    }\n}\nfunction computePlaceholderName(index) {\n    return index === 1 ? 'PH' : `PH_${index - 1}`;\n}\n/**\n * Find the end of a \"marked block\" indicated by the first non-escaped colon.\n *\n * @param cooked The cooked string (where escaped chars have been processed)\n * @param raw The raw string (where escape sequences are still in place)\n *\n * @returns the index of the end of block marker\n * @throws an error if the block is unterminated\n */\nfunction findEndOfBlock(cooked, raw) {\n    for (let cookedIndex = 1, rawIndex = 1; cookedIndex < cooked.length; cookedIndex++, rawIndex++) {\n        if (raw[rawIndex] === '\\\\') {\n            rawIndex++;\n        }\n        else if (cooked[cookedIndex] === BLOCK_MARKER$1) {\n            return cookedIndex;\n        }\n    }\n    throw new Error(`Unterminated $localize metadata block in \"${raw}\".`);\n}\n\nclass MissingTranslationError extends Error {\n    constructor(parsedMessage) {\n        super(`No translation found for ${describeMessage(parsedMessage)}.`);\n        this.parsedMessage = parsedMessage;\n        this.type = 'MissingTranslationError';\n    }\n}\nfunction isMissingTranslationError(e) {\n    return e.type === 'MissingTranslationError';\n}\n/**\n * Translate the text of the `$localize` tagged-string (i.e. `messageParts` and\n * `substitutions`) using the given `translations`.\n *\n * The tagged-string is parsed to extract its `messageId` which is used to find an appropriate\n * `ParsedTranslation`. If this doesn't match and there are legacy ids then try matching a\n * translation using those.\n *\n * If one is found then it is used to translate the message into a new set of `messageParts` and\n * `substitutions`.\n * The translation may reorder (or remove) substitutions as appropriate.\n *\n * If there is no translation with a matching message id then an error is thrown.\n * If a translation contains a placeholder that is not found in the message being translated then an\n * error is thrown.\n */\nfunction translate$1(translations, messageParts, substitutions) {\n    const message = parseMessage(messageParts, substitutions);\n    // Look up the translation using the messageId, and then the legacyId if available.\n    let translation = translations[message.id];\n    // If the messageId did not match a translation, try matching the legacy ids instead\n    if (message.legacyIds !== undefined) {\n        for (let i = 0; i < message.legacyIds.length && translation === undefined; i++) {\n            translation = translations[message.legacyIds[i]];\n        }\n    }\n    if (translation === undefined) {\n        throw new MissingTranslationError(message);\n    }\n    return [\n        translation.messageParts,\n        translation.placeholderNames.map((placeholder) => {\n            if (message.substitutions.hasOwnProperty(placeholder)) {\n                return message.substitutions[placeholder];\n            }\n            else {\n                throw new Error(`There is a placeholder name mismatch with the translation provided for the message ${describeMessage(message)}.\\n` +\n                    `The translation contains a placeholder with name ${placeholder}, which does not exist in the message.`);\n            }\n        }),\n    ];\n}\n/**\n * Parse the `messageParts` and `placeholderNames` out of a target `message`.\n *\n * Used by `loadTranslations()` to convert target message strings into a structure that is more\n * appropriate for doing translation.\n *\n * @param message the message to be parsed.\n */\nfunction parseTranslation(messageString) {\n    const parts = messageString.split(/{\\$([^}]*)}/);\n    const messageParts = [parts[0]];\n    const placeholderNames = [];\n    for (let i = 1; i < parts.length - 1; i += 2) {\n        placeholderNames.push(parts[i]);\n        messageParts.push(`${parts[i + 1]}`);\n    }\n    const rawMessageParts = messageParts.map((part) => part.charAt(0) === BLOCK_MARKER$1 ? '\\\\' + part : part);\n    return {\n        text: messageString,\n        messageParts: makeTemplateObject(messageParts, rawMessageParts),\n        placeholderNames,\n    };\n}\n/**\n * Create a `ParsedTranslation` from a set of `messageParts` and `placeholderNames`.\n *\n * @param messageParts The message parts to appear in the ParsedTranslation.\n * @param placeholderNames The names of the placeholders to intersperse between the `messageParts`.\n */\nfunction makeParsedTranslation(messageParts, placeholderNames = []) {\n    let messageString = messageParts[0];\n    for (let i = 0; i < placeholderNames.length; i++) {\n        messageString += `{$${placeholderNames[i]}}${messageParts[i + 1]}`;\n    }\n    return {\n        text: messageString,\n        messageParts: makeTemplateObject(messageParts, messageParts),\n        placeholderNames,\n    };\n}\n/**\n * Create the specialized array that is passed to tagged-string tag functions.\n *\n * @param cooked The message parts with their escape codes processed.\n * @param raw The message parts with their escaped codes as-is.\n */\nfunction makeTemplateObject(cooked, raw) {\n    Object.defineProperty(cooked, 'raw', { value: raw });\n    return cooked;\n}\nfunction describeMessage(message) {\n    const meaningString = message.meaning && ` - \"${message.meaning}\"`;\n    const legacy = message.legacyIds && message.legacyIds.length > 0\n        ? ` [${message.legacyIds.map((l) => `\"${l}\"`).join(', ')}]`\n        : '';\n    return `\"${message.id}\"${legacy} (\"${message.text}\"${meaningString})`;\n}\n\n/**\n * Load translations for use by `$localize`, if doing runtime translation.\n *\n * If the `$localize` tagged strings are not going to be replaced at compiled time, it is possible\n * to load a set of translations that will be applied to the `$localize` tagged strings at runtime,\n * in the browser.\n *\n * Loading a new translation will overwrite a previous translation if it has the same `MessageId`.\n *\n * Note that `$localize` messages are only processed once, when the tagged string is first\n * encountered, and does not provide dynamic language changing without refreshing the browser.\n * Loading new translations later in the application life-cycle will not change the translated text\n * of messages that have already been translated.\n *\n * The message IDs and translations are in the same format as that rendered to \"simple JSON\"\n * translation files when extracting messages. In particular, placeholders in messages are rendered\n * using the `{$PLACEHOLDER_NAME}` syntax. For example the message from the following template:\n *\n * ```html\n * <div i18n>pre<span>inner-pre<b>bold</b>inner-post</span>post</div>\n * ```\n *\n * would have the following form in the `translations` map:\n *\n * ```ts\n * {\n *   \"2932901491976224757\":\n *      \"pre{$START_TAG_SPAN}inner-pre{$START_BOLD_TEXT}bold{$CLOSE_BOLD_TEXT}inner-post{$CLOSE_TAG_SPAN}post\"\n * }\n * ```\n *\n * @param translations A map from message ID to translated message.\n *\n * These messages are processed and added to a lookup based on their `MessageId`.\n *\n * @see {@link clearTranslations} for removing translations loaded using this function.\n * @see {@link $localize} for tagging messages as needing to be translated.\n * @publicApi\n */\nfunction loadTranslations(translations) {\n    // Ensure the translate function exists\n    if (!$localize.translate) {\n        $localize.translate = translate;\n    }\n    if (!$localize.TRANSLATIONS) {\n        $localize.TRANSLATIONS = {};\n    }\n    Object.keys(translations).forEach((key) => {\n        $localize.TRANSLATIONS[key] = parseTranslation(translations[key]);\n    });\n}\n/**\n * Remove all translations for `$localize`, if doing runtime translation.\n *\n * All translations that had been loading into memory using `loadTranslations()` will be removed.\n *\n * @see {@link loadTranslations} for loading translations at runtime.\n * @see {@link $localize} for tagging messages as needing to be translated.\n *\n * @publicApi\n */\nfunction clearTranslations() {\n    $localize.translate = undefined;\n    $localize.TRANSLATIONS = {};\n}\n/**\n * Translate the text of the given message, using the loaded translations.\n *\n * This function may reorder (or remove) substitutions as indicated in the matching translation.\n */\nfunction translate(messageParts, substitutions) {\n    try {\n        return translate$1($localize.TRANSLATIONS, messageParts, substitutions);\n    }\n    catch (e) {\n        console.warn(e.message);\n        return [messageParts, substitutions];\n    }\n}\n\n/**\n * Tag a template literal string for localization.\n *\n * For example:\n *\n * ```ts\n * $localize `some string to localize`\n * ```\n *\n * **Providing meaning, description and id**\n *\n * You can optionally specify one or more of `meaning`, `description` and `id` for a localized\n * string by pre-pending it with a colon delimited block of the form:\n *\n * ```ts\n * $localize`:meaning|description@@id:source message text`;\n *\n * $localize`:meaning|:source message text`;\n * $localize`:description:source message text`;\n * $localize`:@@id:source message text`;\n * ```\n *\n * This format is the same as that used for `i18n` markers in Angular templates. See the\n * [Angular i18n guide](guide/i18n/prepare#mark-text-in-component-template).\n *\n * **Naming placeholders**\n *\n * If the template literal string contains expressions, then the expressions will be automatically\n * associated with placeholder names for you.\n *\n * For example:\n *\n * ```ts\n * $localize `Hi ${name}! There are ${items.length} items.`;\n * ```\n *\n * will generate a message-source of `Hi {$PH}! There are {$PH_1} items`.\n *\n * The recommended practice is to name the placeholder associated with each expression though.\n *\n * Do this by providing the placeholder name wrapped in `:` characters directly after the\n * expression. These placeholder names are stripped out of the rendered localized string.\n *\n * For example, to name the `items.length` expression placeholder `itemCount` you write:\n *\n * ```ts\n * $localize `There are ${items.length}:itemCount: items`;\n * ```\n *\n * **Escaping colon markers**\n *\n * If you need to use a `:` character directly at the start of a tagged string that has no\n * metadata block, or directly after a substitution expression that has no name you must escape\n * the `:` by preceding it with a backslash:\n *\n * For example:\n *\n * ```ts\n * // message has a metadata block so no need to escape colon\n * $localize `:some description::this message starts with a colon (:)`;\n * // no metadata block so the colon must be escaped\n * $localize `\\:this message starts with a colon (:)`;\n * ```\n *\n * ```ts\n * // named substitution so no need to escape colon\n * $localize `${label}:label:: ${}`\n * // anonymous substitution so colon must be escaped\n * $localize `${label}\\: ${}`\n * ```\n *\n * **Processing localized strings:**\n *\n * There are three scenarios:\n *\n * * **compile-time inlining**: the `$localize` tag is transformed at compile time by a\n * transpiler, removing the tag and replacing the template literal string with a translated\n * literal string from a collection of translations provided to the transpilation tool.\n *\n * * **run-time evaluation**: the `$localize` tag is a run-time function that replaces and\n * reorders the parts (static strings and expressions) of the template literal string with strings\n * from a collection of translations loaded at run-time.\n *\n * * **pass-through evaluation**: the `$localize` tag is a run-time function that simply evaluates\n * the original template literal string without applying any translations to the parts. This\n * version is used during development or where there is no need to translate the localized\n * template literals.\n *\n * @param messageParts a collection of the static parts of the template string.\n * @param expressions a collection of the values of each placeholder in the template string.\n * @returns the translated string, with the `messageParts` and `expressions` interleaved together.\n *\n * @globalApi\n * @publicApi\n */\nconst $localize$1 = function (messageParts, ...expressions) {\n    if ($localize$1.translate) {\n        // Don't use array expansion here to avoid the compiler adding `__read()` helper unnecessarily.\n        const translation = $localize$1.translate(messageParts, expressions);\n        messageParts = translation[0];\n        expressions = translation[1];\n    }\n    let message = stripBlock(messageParts[0], messageParts.raw[0]);\n    for (let i = 1; i < messageParts.length; i++) {\n        message += expressions[i - 1] + stripBlock(messageParts[i], messageParts.raw[i]);\n    }\n    return message;\n};\nconst BLOCK_MARKER = ':';\n/**\n * Strip a delimited \"block\" from the start of the `messagePart`, if it is found.\n *\n * If a marker character (:) actually appears in the content at the start of a tagged string or\n * after a substitution expression, where a block has not been provided the character must be\n * escaped with a backslash, `\\:`. This function checks for this by looking at the `raw`\n * messagePart, which should still contain the backslash.\n *\n * @param messagePart The cooked message part to process.\n * @param rawMessagePart The raw message part to check.\n * @returns the message part with the placeholder name stripped, if found.\n * @throws an error if the block is unterminated\n */\nfunction stripBlock(messagePart, rawMessagePart) {\n    return rawMessagePart.charAt(0) === BLOCK_MARKER\n        ? messagePart.substring(findEndOfBlock(messagePart, rawMessagePart) + 1)\n        : messagePart;\n}\n\n// This file exports all the `utils` as private exports so that other parts of `@angular/localize`\n\n// This file contains the public API of the `@angular/localize` entry-point\n\n// DO NOT ADD public exports to this file.\n\nexport { clearTranslations, loadTranslations, $localize$1 as ɵ$localize, MissingTranslationError as ɵMissingTranslationError, computeMsgId as ɵcomputeMsgId, findEndOfBlock as ɵfindEndOfBlock, isMissingTranslationError as ɵisMissingTranslationError, makeParsedTranslation as ɵmakeParsedTranslation, makeTemplateObject as ɵmakeTemplateObject, parseMessage as ɵparseMessage, parseMetadata as ɵparseMetadata, parseTranslation as ɵparseTranslation, splitBlock as ɵsplitBlock, translate$1 as ɵtranslate };\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMA,cAAc,GAAG,GAAG;AAC1B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,iBAAiB,GAAG,GAAG;AAC7B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,YAAY,GAAG,IAAI;AACzB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,mBAAmB,GAAG,QAAQ;;AAEpC;AACA;AACA;AACA,IAAIC,WAAW;AACf;AACA;AACA;AACA,SAASC,MAAMA,CAACC,OAAO,EAAE;EACrB,OAAOA,OAAO,CAACC,EAAE,IAAIC,aAAa,CAACF,OAAO,CAAC;AAC/C;AACA;AACA;AACA;AACA,SAASE,aAAaA,CAACF,OAAO,EAAE;EAC5B,OAAOG,IAAI,CAACC,cAAc,CAACJ,OAAO,CAACK,KAAK,CAAC,CAACC,IAAI,CAAC,EAAE,CAAC,GAAG,IAAIN,OAAO,CAACO,OAAO,GAAG,CAAC;AAChF;AACA;AACA;AACA;AACA,SAASC,aAAaA,CAACR,OAAO,EAAES,oBAAoB,EAAE;EAClD,OAAOT,OAAO,CAACC,EAAE,IAAIS,oBAAoB,CAACV,OAAO,EAAES,oBAAoB,CAAC;AAC5E;AACA;AACA;AACA;AACA,SAASC,oBAAoBA,CAACV,OAAO,EAAES,oBAAoB,EAAE;EACzD,MAAME,OAAO,GAAG,IAAIC,2BAA2B,CAACH,oBAAoB,CAAC;EACrE,MAAMI,KAAK,GAAGb,OAAO,CAACK,KAAK,CAACS,GAAG,CAAEC,CAAC,IAAKA,CAAC,CAACC,KAAK,CAACL,OAAO,EAAE,IAAI,CAAC,CAAC;EAC9D,OAAOM,YAAY,CAACJ,KAAK,CAACP,IAAI,CAAC,EAAE,CAAC,EAAEN,OAAO,CAACO,OAAO,CAAC;AACxD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMW,kBAAkB,CAAC;EACrBC,SAASA,CAACC,IAAI,EAAEC,OAAO,EAAE;IACrB,OAAOD,IAAI,CAACE,KAAK;EACrB;EACAC,cAAcA,CAACC,SAAS,EAAEH,OAAO,EAAE;IAC/B,OAAO,IAAIG,SAAS,CAACC,QAAQ,CAACX,GAAG,CAAEY,KAAK,IAAKA,KAAK,CAACV,KAAK,CAAC,IAAI,CAAC,CAAC,CAACV,IAAI,CAAC,IAAI,CAAC,GAAG;EACjF;EACAqB,QAAQA,CAACC,GAAG,EAAEP,OAAO,EAAE;IACnB,MAAMQ,QAAQ,GAAGC,MAAM,CAACC,IAAI,CAACH,GAAG,CAACI,KAAK,CAAC,CAAClB,GAAG,CAAEmB,CAAC,IAAK,GAAGA,CAAC,KAAKL,GAAG,CAACI,KAAK,CAACC,CAAC,CAAC,CAACjB,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC;IACxF,OAAO,IAAIY,GAAG,CAACM,UAAU,KAAKN,GAAG,CAACO,IAAI,KAAKN,QAAQ,CAACvB,IAAI,CAAC,IAAI,CAAC,GAAG;EACrE;EACA8B,mBAAmBA,CAACC,EAAE,EAAEhB,OAAO,EAAE;IAC7B,OAAOgB,EAAE,CAACC,MAAM,GACV,iBAAiBD,EAAE,CAACE,SAAS,KAAK,GAClC,iBAAiBF,EAAE,CAACE,SAAS,KAAKF,EAAE,CAACZ,QAAQ,CAC1CX,GAAG,CAAEY,KAAK,IAAKA,KAAK,CAACV,KAAK,CAAC,IAAI,CAAC,CAAC,CACjCV,IAAI,CAAC,IAAI,CAAC,cAAc+B,EAAE,CAACG,SAAS,IAAI;EACrD;EACAC,gBAAgBA,CAACJ,EAAE,EAAEhB,OAAO,EAAE;IAC1B,OAAOgB,EAAE,CAACf,KAAK,GAAG,aAAae,EAAE,CAACK,IAAI,KAAKL,EAAE,CAACf,KAAK,OAAO,GAAG,aAAae,EAAE,CAACK,IAAI,KAAK;EAC1F;EACAC,mBAAmBA,CAACN,EAAE,EAAEhB,OAAO,EAAE;IAC7B,OAAO,iBAAiBgB,EAAE,CAACK,IAAI,KAAKL,EAAE,CAACf,KAAK,CAACN,KAAK,CAAC,IAAI,CAAC,OAAO;EACnE;EACA4B,qBAAqBA,CAACP,EAAE,EAAEhB,OAAO,EAAE;IAC/B,OAAO,mBAAmBgB,EAAE,CAACE,SAAS,KAAKF,EAAE,CAACZ,QAAQ,CACjDX,GAAG,CAAEY,KAAK,IAAKA,KAAK,CAACV,KAAK,CAAC,IAAI,CAAC,CAAC,CACjCV,IAAI,CAAC,IAAI,CAAC,cAAc+B,EAAE,CAACG,SAAS,IAAI;EACjD;AACJ;AACA,MAAMK,iBAAiB,GAAG,IAAI3B,kBAAkB,CAAC,CAAC;AAClD,SAASd,cAAcA,CAACC,KAAK,EAAE;EAC3B,OAAOA,KAAK,CAACS,GAAG,CAAEC,CAAC,IAAKA,CAAC,CAACC,KAAK,CAAC6B,iBAAiB,EAAE,IAAI,CAAC,CAAC;AAC7D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMjC,2BAA2B,SAASM,kBAAkB,CAAC;EACzD4B,WAAWA,CAACrC,oBAAoB,EAAE;IAC9B,KAAK,CAAC,CAAC;IACP,IAAI,CAACA,oBAAoB,GAAGA,oBAAoB;EACpD;EACAgC,gBAAgBA,CAACJ,EAAE,EAAEhB,OAAO,EAAE;IAC1B;IACA,OAAO,IAAI,CAACZ,oBAAoB,GAC1B,KAAK,CAACgC,gBAAgB,CAACJ,EAAE,EAAEhB,OAAO,CAAC,GACnC,aAAagB,EAAE,CAACK,IAAI,KAAK;EACnC;EACAf,QAAQA,CAACC,GAAG,EAAE;IACV,IAAIC,QAAQ,GAAGC,MAAM,CAACC,IAAI,CAACH,GAAG,CAACI,KAAK,CAAC,CAAClB,GAAG,CAAEmB,CAAC,IAAK,GAAGA,CAAC,KAAKL,GAAG,CAACI,KAAK,CAACC,CAAC,CAAC,CAACjB,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC;IACtF;IACA,OAAO,IAAIY,GAAG,CAACO,IAAI,KAAKN,QAAQ,CAACvB,IAAI,CAAC,IAAI,CAAC,GAAG;EAClD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASH,IAAIA,CAAC4C,GAAG,EAAE;EACfjD,WAAW,KAAK,IAAIkD,WAAW,CAAC,CAAC;EACjC,MAAMC,IAAI,GAAG,CAAC,GAAGnD,WAAW,CAACoD,MAAM,CAACH,GAAG,CAAC,CAAC;EACzC,MAAMI,OAAO,GAAGC,cAAc,CAACH,IAAI,EAAEI,MAAM,CAACC,GAAG,CAAC;EAChD,MAAMC,GAAG,GAAGN,IAAI,CAACO,MAAM,GAAG,CAAC;EAC3B,MAAMC,CAAC,GAAG,IAAIC,WAAW,CAAC,EAAE,CAAC;EAC7B,IAAI3C,CAAC,GAAG,UAAU;IAAE4C,CAAC,GAAG,UAAU;IAAEC,CAAC,GAAG,UAAU;IAAEC,CAAC,GAAG,UAAU;IAAEC,CAAC,GAAG,UAAU;EAClFX,OAAO,CAACI,GAAG,IAAI,CAAC,CAAC,IAAI,IAAI,IAAK,EAAE,GAAIA,GAAG,GAAG,EAAI;EAC9CJ,OAAO,CAAC,CAAGI,GAAG,GAAG,EAAE,IAAK,CAAC,IAAK,CAAC,IAAI,EAAE,CAAC,GAAGA,GAAG;EAC5C,KAAK,IAAIQ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGZ,OAAO,CAACK,MAAM,EAAEO,CAAC,IAAI,EAAE,EAAE;IACzC,MAAMC,EAAE,GAAGjD,CAAC;MAAEkD,EAAE,GAAGN,CAAC;MAAEO,EAAE,GAAGN,CAAC;MAAEO,EAAE,GAAGN,CAAC;MAAEO,EAAE,GAAGN,CAAC;IAC5C,KAAK,IAAIO,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAEA,CAAC,EAAE,EAAE;MACzB,IAAIA,CAAC,GAAG,EAAE,EAAE;QACRZ,CAAC,CAACY,CAAC,CAAC,GAAGlB,OAAO,CAACY,CAAC,GAAGM,CAAC,CAAC;MACzB,CAAC,MACI;QACDZ,CAAC,CAACY,CAAC,CAAC,GAAGC,KAAK,CAACb,CAAC,CAACY,CAAC,GAAG,CAAC,CAAC,GAAGZ,CAAC,CAACY,CAAC,GAAG,CAAC,CAAC,GAAGZ,CAAC,CAACY,CAAC,GAAG,EAAE,CAAC,GAAGZ,CAAC,CAACY,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC;MAChE;MACA,MAAME,KAAK,GAAGC,EAAE,CAACH,CAAC,EAAEV,CAAC,EAAEC,CAAC,EAAEC,CAAC,CAAC;MAC5B,MAAMY,CAAC,GAAGF,KAAK,CAAC,CAAC,CAAC;MAClB,MAAMtC,CAAC,GAAGsC,KAAK,CAAC,CAAC,CAAC;MAClB,MAAMG,IAAI,GAAG,CAACJ,KAAK,CAACvD,CAAC,EAAE,CAAC,CAAC,EAAE0D,CAAC,EAAEX,CAAC,EAAE7B,CAAC,EAAEwB,CAAC,CAACY,CAAC,CAAC,CAAC,CAACM,MAAM,CAACC,KAAK,CAAC;MACvDd,CAAC,GAAGD,CAAC;MACLA,CAAC,GAAGD,CAAC;MACLA,CAAC,GAAGU,KAAK,CAACX,CAAC,EAAE,EAAE,CAAC;MAChBA,CAAC,GAAG5C,CAAC;MACLA,CAAC,GAAG2D,IAAI;IACZ;IACA3D,CAAC,GAAG6D,KAAK,CAAC7D,CAAC,EAAEiD,EAAE,CAAC;IAChBL,CAAC,GAAGiB,KAAK,CAACjB,CAAC,EAAEM,EAAE,CAAC;IAChBL,CAAC,GAAGgB,KAAK,CAAChB,CAAC,EAAEM,EAAE,CAAC;IAChBL,CAAC,GAAGe,KAAK,CAACf,CAAC,EAAEM,EAAE,CAAC;IAChBL,CAAC,GAAGc,KAAK,CAACd,CAAC,EAAEM,EAAE,CAAC;EACpB;EACA;EACA,OAAOS,QAAQ,CAAC9D,CAAC,CAAC,GAAG8D,QAAQ,CAAClB,CAAC,CAAC,GAAGkB,QAAQ,CAACjB,CAAC,CAAC,GAAGiB,QAAQ,CAAChB,CAAC,CAAC,GAAGgB,QAAQ,CAACf,CAAC,CAAC;AAC9E;AACA;AACA;AACA;AACA;AACA;AACA,SAASe,QAAQA,CAACvD,KAAK,EAAE;EACrB;EACA,OAAO,CAACA,KAAK,KAAK,CAAC,EAAEwD,QAAQ,CAAC,EAAE,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;AACtD;AACA,SAASP,EAAEA,CAACQ,KAAK,EAAErB,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAE;EACxB,IAAImB,KAAK,GAAG,EAAE,EAAE;IACZ,OAAO,CAAErB,CAAC,GAAGC,CAAC,GAAK,CAACD,CAAC,GAAGE,CAAE,EAAE,UAAU,CAAC;EAC3C;EACA,IAAImB,KAAK,GAAG,EAAE,EAAE;IACZ,OAAO,CAACrB,CAAC,GAAGC,CAAC,GAAGC,CAAC,EAAE,UAAU,CAAC;EAClC;EACA,IAAImB,KAAK,GAAG,EAAE,EAAE;IACZ,OAAO,CAAErB,CAAC,GAAGC,CAAC,GAAKD,CAAC,GAAGE,CAAE,GAAID,CAAC,GAAGC,CAAE,EAAE,UAAU,CAAC;EACpD;EACA,OAAO,CAACF,CAAC,GAAGC,CAAC,GAAGC,CAAC,EAAE,UAAU,CAAC;AAClC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASoB,WAAWA,CAAClC,GAAG,EAAE;EACtBjD,WAAW,KAAK,IAAIkD,WAAW,CAAC,CAAC;EACjC,MAAMC,IAAI,GAAGnD,WAAW,CAACoD,MAAM,CAACH,GAAG,CAAC;EACpC,MAAMmC,IAAI,GAAG,IAAIC,QAAQ,CAAClC,IAAI,CAACmC,MAAM,EAAEnC,IAAI,CAACoC,UAAU,EAAEpC,IAAI,CAACqC,UAAU,CAAC;EACxE,IAAIC,EAAE,GAAGC,MAAM,CAACN,IAAI,EAAEjC,IAAI,CAACO,MAAM,EAAE,CAAC,CAAC;EACrC,IAAIiC,EAAE,GAAGD,MAAM,CAACN,IAAI,EAAEjC,IAAI,CAACO,MAAM,EAAE,MAAM,CAAC;EAC1C,IAAI+B,EAAE,IAAI,CAAC,KAAKE,EAAE,IAAI,CAAC,IAAIA,EAAE,IAAI,CAAC,CAAC,EAAE;IACjCF,EAAE,GAAGA,EAAE,GAAG,UAAU;IACpBE,EAAE,GAAGA,EAAE,GAAG,CAAC,UAAU;EACzB;EACA,OAAQC,MAAM,CAACC,OAAO,CAAC,EAAE,EAAED,MAAM,CAACH,EAAE,CAAC,CAAC,IAAIG,MAAM,CAAC,EAAE,CAAC,GAAIA,MAAM,CAACC,OAAO,CAAC,EAAE,EAAED,MAAM,CAACD,EAAE,CAAC,CAAC;AAC1F;AACA,SAASxE,YAAYA,CAAC2E,GAAG,EAAErF,OAAO,GAAG,EAAE,EAAE;EACrC,IAAIsF,cAAc,GAAGZ,WAAW,CAACW,GAAG,CAAC;EACrC,IAAIrF,OAAO,EAAE;IACT;IACA;IACAsF,cAAc,GACVH,MAAM,CAACC,OAAO,CAAC,EAAE,EAAEE,cAAc,IAAIH,MAAM,CAAC,CAAC,CAAC,CAAC,GACzCG,cAAc,IAAIH,MAAM,CAAC,EAAE,CAAC,GAAIA,MAAM,CAAC,CAAC,CAAE;IACpDG,cAAc,IAAIZ,WAAW,CAAC1E,OAAO,CAAC;EAC1C;EACA,OAAOmF,MAAM,CAACC,OAAO,CAAC,EAAE,EAAEE,cAAc,CAAC,CAACf,QAAQ,CAAC,CAAC;AACxD;AACA,SAASU,MAAMA,CAACN,IAAI,EAAE1B,MAAM,EAAEI,CAAC,EAAE;EAC7B,IAAI7C,CAAC,GAAG,UAAU;IAAE4C,CAAC,GAAG,UAAU;EAClC,IAAIqB,KAAK,GAAG,CAAC;EACb,MAAMc,GAAG,GAAGtC,MAAM,GAAG,EAAE;EACvB,OAAOwB,KAAK,IAAIc,GAAG,EAAEd,KAAK,IAAI,EAAE,EAAE;IAC9BjE,CAAC,IAAImE,IAAI,CAACa,SAAS,CAACf,KAAK,EAAE,IAAI,CAAC;IAChCrB,CAAC,IAAIuB,IAAI,CAACa,SAAS,CAACf,KAAK,GAAG,CAAC,EAAE,IAAI,CAAC;IACpCpB,CAAC,IAAIsB,IAAI,CAACa,SAAS,CAACf,KAAK,GAAG,CAAC,EAAE,IAAI,CAAC;IACpC,MAAMgB,GAAG,GAAGC,GAAG,CAAClF,CAAC,EAAE4C,CAAC,EAAEC,CAAC,CAAC;IACvB7C,CAAC,GAAGiF,GAAG,CAAC,CAAC,CAAC,EAAIrC,CAAC,GAAGqC,GAAG,CAAC,CAAC,CAAC,EAAIpC,CAAC,GAAGoC,GAAG,CAAC,CAAC,CAAE;EAC5C;EACA,MAAME,SAAS,GAAG1C,MAAM,GAAGwB,KAAK;EAChC;EACApB,CAAC,IAAIJ,MAAM;EACX,IAAI0C,SAAS,IAAI,CAAC,EAAE;IAChBnF,CAAC,IAAImE,IAAI,CAACa,SAAS,CAACf,KAAK,EAAE,IAAI,CAAC;IAChCA,KAAK,IAAI,CAAC;IACV,IAAIkB,SAAS,IAAI,CAAC,EAAE;MAChBvC,CAAC,IAAIuB,IAAI,CAACa,SAAS,CAACf,KAAK,EAAE,IAAI,CAAC;MAChCA,KAAK,IAAI,CAAC;MACV;MACA,IAAIkB,SAAS,IAAI,CAAC,EAAE;QAChBtC,CAAC,IAAIsB,IAAI,CAACiB,QAAQ,CAACnB,KAAK,EAAE,CAAC,IAAI,CAAC;MACpC;MACA,IAAIkB,SAAS,IAAI,EAAE,EAAE;QACjBtC,CAAC,IAAIsB,IAAI,CAACiB,QAAQ,CAACnB,KAAK,EAAE,CAAC,IAAI,EAAE;MACrC;MACA,IAAIkB,SAAS,KAAK,EAAE,EAAE;QAClBtC,CAAC,IAAIsB,IAAI,CAACiB,QAAQ,CAACnB,KAAK,EAAE,CAAC,IAAI,EAAE;MACrC;IACJ,CAAC,MACI;MACD;MACA,IAAIkB,SAAS,IAAI,CAAC,EAAE;QAChBvC,CAAC,IAAIuB,IAAI,CAACiB,QAAQ,CAACnB,KAAK,EAAE,CAAC;MAC/B;MACA,IAAIkB,SAAS,IAAI,CAAC,EAAE;QAChBvC,CAAC,IAAIuB,IAAI,CAACiB,QAAQ,CAACnB,KAAK,EAAE,CAAC,IAAI,CAAC;MACpC;MACA,IAAIkB,SAAS,KAAK,CAAC,EAAE;QACjBvC,CAAC,IAAIuB,IAAI,CAACiB,QAAQ,CAACnB,KAAK,EAAE,CAAC,IAAI,EAAE;MACrC;IACJ;EACJ,CAAC,MACI;IACD;IACA,IAAIkB,SAAS,IAAI,CAAC,EAAE;MAChBnF,CAAC,IAAImE,IAAI,CAACiB,QAAQ,CAACnB,KAAK,EAAE,CAAC;IAC/B;IACA,IAAIkB,SAAS,IAAI,CAAC,EAAE;MAChBnF,CAAC,IAAImE,IAAI,CAACiB,QAAQ,CAACnB,KAAK,EAAE,CAAC,IAAI,CAAC;IACpC;IACA,IAAIkB,SAAS,KAAK,CAAC,EAAE;MACjBnF,CAAC,IAAImE,IAAI,CAACiB,QAAQ,CAACnB,KAAK,EAAE,CAAC,IAAI,EAAE;IACrC;EACJ;EACA,OAAOiB,GAAG,CAAClF,CAAC,EAAE4C,CAAC,EAAEC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC1B;AACA,SAASqC,GAAGA,CAAClF,CAAC,EAAE4C,CAAC,EAAEC,CAAC,EAAE;EAClB7C,CAAC,IAAI4C,CAAC;EACN5C,CAAC,IAAI6C,CAAC;EACN7C,CAAC,IAAI6C,CAAC,KAAK,EAAE;EACbD,CAAC,IAAIC,CAAC;EACND,CAAC,IAAI5C,CAAC;EACN4C,CAAC,IAAI5C,CAAC,IAAI,CAAC;EACX6C,CAAC,IAAI7C,CAAC;EACN6C,CAAC,IAAID,CAAC;EACNC,CAAC,IAAID,CAAC,KAAK,EAAE;EACb5C,CAAC,IAAI4C,CAAC;EACN5C,CAAC,IAAI6C,CAAC;EACN7C,CAAC,IAAI6C,CAAC,KAAK,EAAE;EACbD,CAAC,IAAIC,CAAC;EACND,CAAC,IAAI5C,CAAC;EACN4C,CAAC,IAAI5C,CAAC,IAAI,EAAE;EACZ6C,CAAC,IAAI7C,CAAC;EACN6C,CAAC,IAAID,CAAC;EACNC,CAAC,IAAID,CAAC,KAAK,CAAC;EACZ5C,CAAC,IAAI4C,CAAC;EACN5C,CAAC,IAAI6C,CAAC;EACN7C,CAAC,IAAI6C,CAAC,KAAK,CAAC;EACZD,CAAC,IAAIC,CAAC;EACND,CAAC,IAAI5C,CAAC;EACN4C,CAAC,IAAI5C,CAAC,IAAI,EAAE;EACZ6C,CAAC,IAAI7C,CAAC;EACN6C,CAAC,IAAID,CAAC;EACNC,CAAC,IAAID,CAAC,KAAK,EAAE;EACb,OAAO,CAAC5C,CAAC,EAAE4C,CAAC,EAAEC,CAAC,CAAC;AACpB;AACA;AACA,IAAIP,MAAM;AACV,CAAC,UAAUA,MAAM,EAAE;EACfA,MAAM,CAACA,MAAM,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,GAAG,QAAQ;EACvCA,MAAM,CAACA,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,KAAK;AACrC,CAAC,EAAEA,MAAM,KAAKA,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC;AAC3B,SAASuB,KAAKA,CAAC7D,CAAC,EAAE4C,CAAC,EAAE;EACjB,OAAOyC,SAAS,CAACrF,CAAC,EAAE4C,CAAC,CAAC,CAAC,CAAC,CAAC;AAC7B;AACA,SAASyC,SAASA,CAACrF,CAAC,EAAE4C,CAAC,EAAE;EACrB,MAAM0C,GAAG,GAAG,CAACtF,CAAC,GAAG,MAAM,KAAK4C,CAAC,GAAG,MAAM,CAAC;EACvC,MAAM2C,IAAI,GAAG,CAACvF,CAAC,KAAK,EAAE,KAAK4C,CAAC,KAAK,EAAE,CAAC,IAAI0C,GAAG,KAAK,EAAE,CAAC;EACnD,OAAO,CAACC,IAAI,KAAK,EAAE,EAAGA,IAAI,IAAI,EAAE,GAAKD,GAAG,GAAG,MAAO,CAAC;AACvD;AACA;AACA,SAAS/B,KAAKA,CAACvD,CAAC,EAAEwF,KAAK,EAAE;EACrB,OAAQxF,CAAC,IAAIwF,KAAK,GAAKxF,CAAC,KAAM,EAAE,GAAGwF,KAAO;AAC9C;AACA,SAASnD,cAAcA,CAACoD,KAAK,EAAEC,MAAM,EAAE;EACnC,MAAMC,IAAI,GAAIF,KAAK,CAAChD,MAAM,GAAG,CAAC,KAAM,CAAC;EACrC,MAAML,OAAO,GAAG,EAAE;EAClB,KAAK,IAAIY,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG2C,IAAI,EAAE3C,CAAC,EAAE,EAAE;IAC3BZ,OAAO,CAACY,CAAC,CAAC,GAAG4C,MAAM,CAACH,KAAK,EAAEzC,CAAC,GAAG,CAAC,EAAE0C,MAAM,CAAC;EAC7C;EACA,OAAOtD,OAAO;AAClB;AACA,SAASyD,MAAMA,CAACJ,KAAK,EAAExB,KAAK,EAAE;EAC1B,OAAOA,KAAK,IAAIwB,KAAK,CAAChD,MAAM,GAAG,CAAC,GAAGgD,KAAK,CAACxB,KAAK,CAAC;AACnD;AACA,SAAS2B,MAAMA,CAACH,KAAK,EAAExB,KAAK,EAAEyB,MAAM,EAAE;EAClC,IAAII,IAAI,GAAG,CAAC;EACZ,IAAIJ,MAAM,KAAKpD,MAAM,CAACC,GAAG,EAAE;IACvB,KAAK,IAAIS,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAE,EAAE;MACxB8C,IAAI,IAAID,MAAM,CAACJ,KAAK,EAAExB,KAAK,GAAGjB,CAAC,CAAC,IAAK,EAAE,GAAG,CAAC,GAAGA,CAAE;IACpD;EACJ,CAAC,MACI;IACD,KAAK,IAAIA,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAE,EAAE;MACxB8C,IAAI,IAAID,MAAM,CAACJ,KAAK,EAAExB,KAAK,GAAGjB,CAAC,CAAC,IAAK,CAAC,GAAGA,CAAE;IAC/C;EACJ;EACA,OAAO8C,IAAI;AACf;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,YAAYA,CAACC,YAAY,EAAEC,WAAW,EAAEC,QAAQ,EAAEC,oBAAoB,EAAEC,mBAAmB,GAAG,EAAE,EAAE;EACvG,MAAMC,aAAa,GAAG,CAAC,CAAC;EACxB,MAAMC,qBAAqB,GAAG,CAAC,CAAC;EAChC,MAAMC,oBAAoB,GAAG,CAAC,CAAC;EAC/B,MAAMC,QAAQ,GAAGC,aAAa,CAACT,YAAY,CAAC,CAAC,CAAC,EAAEA,YAAY,CAACU,GAAG,CAAC,CAAC,CAAC,CAAC;EACpE,MAAMC,mBAAmB,GAAG,CAACH,QAAQ,CAACnG,IAAI,CAAC;EAC3C,MAAMuG,gBAAgB,GAAG,EAAE;EAC3B,IAAIC,aAAa,GAAGL,QAAQ,CAACnG,IAAI;EACjC,KAAK,IAAI2C,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGgD,YAAY,CAACvD,MAAM,EAAEO,CAAC,EAAE,EAAE;IAC1C,MAAM;MAAE8D,WAAW;MAAEC,eAAe,GAAGC,sBAAsB,CAAChE,CAAC,CAAC;MAAEiE;IAAqB,CAAC,GAAGC,gBAAgB,CAAClB,YAAY,CAAChD,CAAC,CAAC,EAAEgD,YAAY,CAACU,GAAG,CAAC1D,CAAC,CAAC,CAAC;IACjJ6D,aAAa,IAAI,KAAKE,eAAe,IAAID,WAAW,EAAE;IACtD,IAAIb,WAAW,KAAKkB,SAAS,EAAE;MAC3Bd,aAAa,CAACU,eAAe,CAAC,GAAGd,WAAW,CAACjD,CAAC,GAAG,CAAC,CAAC;MACnDsD,qBAAqB,CAACS,eAAe,CAAC,GAAGX,mBAAmB,CAACpD,CAAC,GAAG,CAAC,CAAC;IACvE;IACA4D,gBAAgB,CAACQ,IAAI,CAACL,eAAe,CAAC;IACtC,IAAIE,mBAAmB,KAAKE,SAAS,EAAE;MACnCZ,oBAAoB,CAACQ,eAAe,CAAC,GAAGE,mBAAmB;IAC/D;IACAN,mBAAmB,CAACS,IAAI,CAACN,WAAW,CAAC;EACzC;EACA,MAAMO,SAAS,GAAGb,QAAQ,CAACc,QAAQ,IAAIpH,YAAY,CAAC2G,aAAa,EAAEL,QAAQ,CAAChH,OAAO,IAAI,EAAE,CAAC;EAC1F,MAAM+H,SAAS,GAAGf,QAAQ,CAACe,SAAS,GAAGf,QAAQ,CAACe,SAAS,CAACC,MAAM,CAAEtI,EAAE,IAAKA,EAAE,KAAKmI,SAAS,CAAC,GAAG,EAAE;EAC/F,OAAO;IACHnI,EAAE,EAAEmI,SAAS;IACbE,SAAS;IACTlB,aAAa;IACbC,qBAAqB;IACrBjG,IAAI,EAAEwG,aAAa;IACnBS,QAAQ,EAAEd,QAAQ,CAACc,QAAQ;IAC3B9H,OAAO,EAAEgH,QAAQ,CAAChH,OAAO,IAAI,EAAE;IAC/BiI,WAAW,EAAEjB,QAAQ,CAACiB,WAAW,IAAI,EAAE;IACvCzB,YAAY,EAAEW,mBAAmB;IACjCR,oBAAoB;IACpBS,gBAAgB;IAChBL,oBAAoB;IACpBL;EACJ,CAAC;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASO,aAAaA,CAACiB,MAAM,EAAEhB,GAAG,EAAE;EAChC,MAAM;IAAErG,IAAI,EAAEwG,aAAa;IAAEc;EAAM,CAAC,GAAGC,UAAU,CAACF,MAAM,EAAEhB,GAAG,CAAC;EAC9D,IAAIiB,KAAK,KAAKR,SAAS,EAAE;IACrB,OAAO;MAAE9G,IAAI,EAAEwG;IAAc,CAAC;EAClC,CAAC,MACI;IACD,MAAM,CAACgB,gBAAgB,EAAE,GAAGN,SAAS,CAAC,GAAGI,KAAK,CAACG,KAAK,CAAChJ,mBAAmB,CAAC;IACzE,MAAM,CAACiJ,cAAc,EAAET,QAAQ,CAAC,GAAGO,gBAAgB,CAACC,KAAK,CAACjJ,YAAY,EAAE,CAAC,CAAC;IAC1E,IAAI,CAACW,OAAO,EAAEiI,WAAW,CAAC,GAAGM,cAAc,CAACD,KAAK,CAAClJ,iBAAiB,EAAE,CAAC,CAAC;IACvE,IAAI6I,WAAW,KAAKN,SAAS,EAAE;MAC3BM,WAAW,GAAGjI,OAAO;MACrBA,OAAO,GAAG2H,SAAS;IACvB;IACA,IAAIM,WAAW,KAAK,EAAE,EAAE;MACpBA,WAAW,GAAGN,SAAS;IAC3B;IACA,OAAO;MAAE9G,IAAI,EAAEwG,aAAa;MAAErH,OAAO;MAAEiI,WAAW;MAAEH,QAAQ;MAAEC;IAAU,CAAC;EAC7E;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASL,gBAAgBA,CAACQ,MAAM,EAAEhB,GAAG,EAAE;EACnC,MAAM;IAAErG,IAAI,EAAEyG,WAAW;IAAEa;EAAM,CAAC,GAAGC,UAAU,CAACF,MAAM,EAAEhB,GAAG,CAAC;EAC5D,IAAIiB,KAAK,KAAKR,SAAS,EAAE;IACrB,OAAO;MAAEL;IAAY,CAAC;EAC1B,CAAC,MACI;IACD,MAAM,CAACC,eAAe,EAAEE,mBAAmB,CAAC,GAAGU,KAAK,CAACG,KAAK,CAACjJ,YAAY,CAAC;IACxE,OAAO;MAAEiI,WAAW;MAAEC,eAAe;MAAEE;IAAoB,CAAC;EAChE;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASW,UAAUA,CAACF,MAAM,EAAEhB,GAAG,EAAE;EAC7B,IAAIA,GAAG,CAACsB,MAAM,CAAC,CAAC,CAAC,KAAKrJ,cAAc,EAAE;IAClC,OAAO;MAAE0B,IAAI,EAAEqH;IAAO,CAAC;EAC3B,CAAC,MACI;IACD,MAAMO,UAAU,GAAGC,cAAc,CAACR,MAAM,EAAEhB,GAAG,CAAC;IAC9C,OAAO;MACHiB,KAAK,EAAED,MAAM,CAACS,SAAS,CAAC,CAAC,EAAEF,UAAU,CAAC;MACtC5H,IAAI,EAAEqH,MAAM,CAACS,SAAS,CAACF,UAAU,GAAG,CAAC;IACzC,CAAC;EACL;AACJ;AACA,SAASjB,sBAAsBA,CAAC/C,KAAK,EAAE;EACnC,OAAOA,KAAK,KAAK,CAAC,GAAG,IAAI,GAAG,MAAMA,KAAK,GAAG,CAAC,EAAE;AACjD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASiE,cAAcA,CAACR,MAAM,EAAEhB,GAAG,EAAE;EACjC,KAAK,IAAI0B,WAAW,GAAG,CAAC,EAAEC,QAAQ,GAAG,CAAC,EAAED,WAAW,GAAGV,MAAM,CAACjF,MAAM,EAAE2F,WAAW,EAAE,EAAEC,QAAQ,EAAE,EAAE;IAC5F,IAAI3B,GAAG,CAAC2B,QAAQ,CAAC,KAAK,IAAI,EAAE;MACxBA,QAAQ,EAAE;IACd,CAAC,MACI,IAAIX,MAAM,CAACU,WAAW,CAAC,KAAKzJ,cAAc,EAAE;MAC7C,OAAOyJ,WAAW;IACtB;EACJ;EACA,MAAM,IAAIE,KAAK,CAAC,6CAA6C5B,GAAG,IAAI,CAAC;AACzE;AAEA,MAAM6B,uBAAuB,SAASD,KAAK,CAAC;EACxCvG,WAAWA,CAACyG,aAAa,EAAE;IACvB,KAAK,CAAC,4BAA4BC,eAAe,CAACD,aAAa,CAAC,GAAG,CAAC;IACpE,IAAI,CAACA,aAAa,GAAGA,aAAa;IAClC,IAAI,CAACpH,IAAI,GAAG,yBAAyB;EACzC;AACJ;AACA,SAASsH,yBAAyBA,CAAC3F,CAAC,EAAE;EAClC,OAAOA,CAAC,CAAC3B,IAAI,KAAK,yBAAyB;AAC/C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASuH,WAAWA,CAACC,YAAY,EAAE5C,YAAY,EAAEK,aAAa,EAAE;EAC5D,MAAMpH,OAAO,GAAG8G,YAAY,CAACC,YAAY,EAAEK,aAAa,CAAC;EACzD;EACA,IAAIwC,WAAW,GAAGD,YAAY,CAAC3J,OAAO,CAACC,EAAE,CAAC;EAC1C;EACA,IAAID,OAAO,CAACsI,SAAS,KAAKJ,SAAS,EAAE;IACjC,KAAK,IAAInE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG/D,OAAO,CAACsI,SAAS,CAAC9E,MAAM,IAAIoG,WAAW,KAAK1B,SAAS,EAAEnE,CAAC,EAAE,EAAE;MAC5E6F,WAAW,GAAGD,YAAY,CAAC3J,OAAO,CAACsI,SAAS,CAACvE,CAAC,CAAC,CAAC;IACpD;EACJ;EACA,IAAI6F,WAAW,KAAK1B,SAAS,EAAE;IAC3B,MAAM,IAAIoB,uBAAuB,CAACtJ,OAAO,CAAC;EAC9C;EACA,OAAO,CACH4J,WAAW,CAAC7C,YAAY,EACxB6C,WAAW,CAACjC,gBAAgB,CAAC7G,GAAG,CAAE+I,WAAW,IAAK;IAC9C,IAAI7J,OAAO,CAACoH,aAAa,CAAC0C,cAAc,CAACD,WAAW,CAAC,EAAE;MACnD,OAAO7J,OAAO,CAACoH,aAAa,CAACyC,WAAW,CAAC;IAC7C,CAAC,MACI;MACD,MAAM,IAAIR,KAAK,CAAC,sFAAsFG,eAAe,CAACxJ,OAAO,CAAC,KAAK,GAC/H,oDAAoD6J,WAAW,wCAAwC,CAAC;IAChH;EACJ,CAAC,CAAC,CACL;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASE,gBAAgBA,CAACnC,aAAa,EAAE;EACrC,MAAM/G,KAAK,GAAG+G,aAAa,CAACiB,KAAK,CAAC,aAAa,CAAC;EAChD,MAAM9B,YAAY,GAAG,CAAClG,KAAK,CAAC,CAAC,CAAC,CAAC;EAC/B,MAAM8G,gBAAgB,GAAG,EAAE;EAC3B,KAAK,IAAI5D,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGlD,KAAK,CAAC2C,MAAM,GAAG,CAAC,EAAEO,CAAC,IAAI,CAAC,EAAE;IAC1C4D,gBAAgB,CAACQ,IAAI,CAACtH,KAAK,CAACkD,CAAC,CAAC,CAAC;IAC/BgD,YAAY,CAACoB,IAAI,CAAC,GAAGtH,KAAK,CAACkD,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC;EACxC;EACA,MAAMiG,eAAe,GAAGjD,YAAY,CAACjG,GAAG,CAAEmJ,IAAI,IAAKA,IAAI,CAAClB,MAAM,CAAC,CAAC,CAAC,KAAKrJ,cAAc,GAAG,IAAI,GAAGuK,IAAI,GAAGA,IAAI,CAAC;EAC1G,OAAO;IACH7I,IAAI,EAAEwG,aAAa;IACnBb,YAAY,EAAEmD,kBAAkB,CAACnD,YAAY,EAAEiD,eAAe,CAAC;IAC/DrC;EACJ,CAAC;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASwC,qBAAqBA,CAACpD,YAAY,EAAEY,gBAAgB,GAAG,EAAE,EAAE;EAChE,IAAIC,aAAa,GAAGb,YAAY,CAAC,CAAC,CAAC;EACnC,KAAK,IAAIhD,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG4D,gBAAgB,CAACnE,MAAM,EAAEO,CAAC,EAAE,EAAE;IAC9C6D,aAAa,IAAI,KAAKD,gBAAgB,CAAC5D,CAAC,CAAC,IAAIgD,YAAY,CAAChD,CAAC,GAAG,CAAC,CAAC,EAAE;EACtE;EACA,OAAO;IACH3C,IAAI,EAAEwG,aAAa;IACnBb,YAAY,EAAEmD,kBAAkB,CAACnD,YAAY,EAAEA,YAAY,CAAC;IAC5DY;EACJ,CAAC;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASuC,kBAAkBA,CAACzB,MAAM,EAAEhB,GAAG,EAAE;EACrC3F,MAAM,CAACsI,cAAc,CAAC3B,MAAM,EAAE,KAAK,EAAE;IAAEnH,KAAK,EAAEmG;EAAI,CAAC,CAAC;EACpD,OAAOgB,MAAM;AACjB;AACA,SAASe,eAAeA,CAACxJ,OAAO,EAAE;EAC9B,MAAMqK,aAAa,GAAGrK,OAAO,CAACO,OAAO,IAAI,OAAOP,OAAO,CAACO,OAAO,GAAG;EAClE,MAAM+J,MAAM,GAAGtK,OAAO,CAACsI,SAAS,IAAItI,OAAO,CAACsI,SAAS,CAAC9E,MAAM,GAAG,CAAC,GAC1D,KAAKxD,OAAO,CAACsI,SAAS,CAACxH,GAAG,CAAEyJ,CAAC,IAAK,IAAIA,CAAC,GAAG,CAAC,CAACjK,IAAI,CAAC,IAAI,CAAC,GAAG,GACzD,EAAE;EACR,OAAO,IAAIN,OAAO,CAACC,EAAE,IAAIqK,MAAM,MAAMtK,OAAO,CAACoB,IAAI,IAAIiJ,aAAa,GAAG;AACzE;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASG,gBAAgBA,CAACb,YAAY,EAAE;EACpC;EACA,IAAI,CAACc,SAAS,CAACC,SAAS,EAAE;IACtBD,SAAS,CAACC,SAAS,GAAGA,SAAS;EACnC;EACA,IAAI,CAACD,SAAS,CAACE,YAAY,EAAE;IACzBF,SAAS,CAACE,YAAY,GAAG,CAAC,CAAC;EAC/B;EACA7I,MAAM,CAACC,IAAI,CAAC4H,YAAY,CAAC,CAACiB,OAAO,CAAEC,GAAG,IAAK;IACvCJ,SAAS,CAACE,YAAY,CAACE,GAAG,CAAC,GAAGd,gBAAgB,CAACJ,YAAY,CAACkB,GAAG,CAAC,CAAC;EACrE,CAAC,CAAC;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,iBAAiBA,CAAA,EAAG;EACzBL,SAAS,CAACC,SAAS,GAAGxC,SAAS;EAC/BuC,SAAS,CAACE,YAAY,GAAG,CAAC,CAAC;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA,SAASD,SAASA,CAAC3D,YAAY,EAAEK,aAAa,EAAE;EAC5C,IAAI;IACA,OAAOsC,WAAW,CAACe,SAAS,CAACE,YAAY,EAAE5D,YAAY,EAAEK,aAAa,CAAC;EAC3E,CAAC,CACD,OAAOtD,CAAC,EAAE;IACNiH,OAAO,CAACC,IAAI,CAAClH,CAAC,CAAC9D,OAAO,CAAC;IACvB,OAAO,CAAC+G,YAAY,EAAEK,aAAa,CAAC;EACxC;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM6D,WAAW,GAAG,SAAAA,CAAUlE,YAAY,EAAE,GAAGC,WAAW,EAAE;EACxD,IAAIiE,WAAW,CAACP,SAAS,EAAE;IACvB;IACA,MAAMd,WAAW,GAAGqB,WAAW,CAACP,SAAS,CAAC3D,YAAY,EAAEC,WAAW,CAAC;IACpED,YAAY,GAAG6C,WAAW,CAAC,CAAC,CAAC;IAC7B5C,WAAW,GAAG4C,WAAW,CAAC,CAAC,CAAC;EAChC;EACA,IAAI5J,OAAO,GAAGkL,UAAU,CAACnE,YAAY,CAAC,CAAC,CAAC,EAAEA,YAAY,CAACU,GAAG,CAAC,CAAC,CAAC,CAAC;EAC9D,KAAK,IAAI1D,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGgD,YAAY,CAACvD,MAAM,EAAEO,CAAC,EAAE,EAAE;IAC1C/D,OAAO,IAAIgH,WAAW,CAACjD,CAAC,GAAG,CAAC,CAAC,GAAGmH,UAAU,CAACnE,YAAY,CAAChD,CAAC,CAAC,EAAEgD,YAAY,CAACU,GAAG,CAAC1D,CAAC,CAAC,CAAC;EACpF;EACA,OAAO/D,OAAO;AAClB,CAAC;AACD,MAAMmL,YAAY,GAAG,GAAG;AACxB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASD,UAAUA,CAACrD,WAAW,EAAEuD,cAAc,EAAE;EAC7C,OAAOA,cAAc,CAACrC,MAAM,CAAC,CAAC,CAAC,KAAKoC,YAAY,GAC1CtD,WAAW,CAACqB,SAAS,CAACD,cAAc,CAACpB,WAAW,EAAEuD,cAAc,CAAC,GAAG,CAAC,CAAC,GACtEvD,WAAW;AACrB;;AAEA;;AAEA;;AAEA;;AAEA,SAASiD,iBAAiB,EAAEN,gBAAgB,EAAES,WAAW,IAAII,UAAU,EAAE/B,uBAAuB,IAAIgC,wBAAwB,EAAErK,YAAY,IAAIsK,aAAa,EAAEtC,cAAc,IAAIuC,eAAe,EAAE/B,yBAAyB,IAAIgC,0BAA0B,EAAEtB,qBAAqB,IAAIuB,sBAAsB,EAAExB,kBAAkB,IAAIyB,mBAAmB,EAAE7E,YAAY,IAAI8E,aAAa,EAAEpE,aAAa,IAAIqE,cAAc,EAAE9B,gBAAgB,IAAI+B,iBAAiB,EAAEnD,UAAU,IAAIoD,WAAW,EAAErC,WAAW,IAAIsC,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}