{"ast": null, "code": "export var CQuotationItemType;\n(function (CQuotationItemType) {\n  /** 客變需求 */\n  CQuotationItemType[CQuotationItemType[\"\\u5BA2\\u8B8A\\u9700\\u6C42\"] = 1] = \"\\u5BA2\\u8B8A\\u9700\\u6C42\";\n  /** 自定義 */\n  CQuotationItemType[CQuotationItemType[\"\\u81EA\\u5B9A\\u7FA9\"] = 2] = \"\\u81EA\\u5B9A\\u7FA9\";\n  /** 選樣 */\n  CQuotationItemType[CQuotationItemType[\"\\u9078\\u6A23\"] = 3] = \"\\u9078\\u6A23\";\n})(CQuotationItemType || (CQuotationItemType = {}));", "map": {"version": 3, "names": ["CQuotationItemType"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\models\\quotation.model.ts"], "sourcesContent": ["export enum CQuotationItemType {\r\n  /** 客變需求 */\r\n  客變需求 = 1,\r\n  /** 自定義 */\r\n  自定義 = 2,\r\n  /** 選樣 */\r\n  選樣 = 3\r\n}\r\n\r\nexport interface QuotationItem {\r\n  cQuotationID?: number;\r\n  cHouseID: number;\r\n  cItemName: string;\r\n  cUnit?: string;\r\n  cUnitPrice: number;\r\n  cCount: number;\r\n  cStatus?: number;\r\n  CQuotationItemType: CQuotationItemType;\r\n}\r\n\r\nexport interface QuotationRequest {\r\n  houseId: number;\r\n  items: QuotationItem[];\r\n}\r\n\r\nexport interface QuotationResponse {\r\n  success: boolean;\r\n  message: string;\r\n  data?: QuotationItem[];\r\n}\r\n"], "mappings": "AAAA,WAAYA,kBAOX;AAPD,WAAYA,kBAAkB;EAC5B;EACAA,kBAAA,CAAAA,kBAAA,8DAAQ;EACR;EACAA,kBAAA,CAAAA,kBAAA,kDAAO;EACP;EACAA,kBAAA,CAAAA,kBAAA,sCAAM;AACR,CAAC,EAPWA,kBAAkB,KAAlBA,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}