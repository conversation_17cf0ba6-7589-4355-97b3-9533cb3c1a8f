{"ast": null, "code": "//! moment.js locale configuration\n//! locale : Finnish [fi]\n//! author : <PERSON><PERSON><PERSON> : https://github.com/bleadof\n\n;\n(function (global, factory) {\n  typeof exports === 'object' && typeof module !== 'undefined' && typeof require === 'function' ? factory(require('../moment')) : typeof define === 'function' && define.amd ? define(['../moment'], factory) : factory(global.moment);\n})(this, function (moment) {\n  'use strict';\n\n  //! moment.js locale configuration\n  var numbersPast = 'nolla yksi kaksi kolme neljä viisi kuusi seitsemän kahdeksan yhdeksän'.split(' '),\n    numbersFuture = ['nolla', 'yhden', 'kahden', 'kolmen', 'neljän', 'viiden', 'kuuden', numbersPast[7], numbersPast[8], numbersPast[9]];\n  function translate(number, withoutSuffix, key, isFuture) {\n    var result = '';\n    switch (key) {\n      case 's':\n        return isFuture ? 'muutaman sekunnin' : 'muutama sekunti';\n      case 'ss':\n        result = isFuture ? 'sekunnin' : 'sekuntia';\n        break;\n      case 'm':\n        return isFuture ? 'minuutin' : 'minuutti';\n      case 'mm':\n        result = isFuture ? 'minuutin' : 'minuuttia';\n        break;\n      case 'h':\n        return isFuture ? 'tunnin' : 'tunti';\n      case 'hh':\n        result = isFuture ? 'tunnin' : 'tuntia';\n        break;\n      case 'd':\n        return isFuture ? 'päivän' : 'päivä';\n      case 'dd':\n        result = isFuture ? 'päivän' : 'päivää';\n        break;\n      case 'M':\n        return isFuture ? 'kuukauden' : 'kuukausi';\n      case 'MM':\n        result = isFuture ? 'kuukauden' : 'kuukautta';\n        break;\n      case 'y':\n        return isFuture ? 'vuoden' : 'vuosi';\n      case 'yy':\n        result = isFuture ? 'vuoden' : 'vuotta';\n        break;\n    }\n    result = verbalNumber(number, isFuture) + ' ' + result;\n    return result;\n  }\n  function verbalNumber(number, isFuture) {\n    return number < 10 ? isFuture ? numbersFuture[number] : numbersPast[number] : number;\n  }\n  var fi = moment.defineLocale('fi', {\n    months: 'tammikuu_helmikuu_maaliskuu_huhtikuu_toukokuu_kesäkuu_heinäkuu_elokuu_syyskuu_lokakuu_marraskuu_joulukuu'.split('_'),\n    monthsShort: 'tammi_helmi_maalis_huhti_touko_kesä_heinä_elo_syys_loka_marras_joulu'.split('_'),\n    weekdays: 'sunnuntai_maanantai_tiistai_keskiviikko_torstai_perjantai_lauantai'.split('_'),\n    weekdaysShort: 'su_ma_ti_ke_to_pe_la'.split('_'),\n    weekdaysMin: 'su_ma_ti_ke_to_pe_la'.split('_'),\n    longDateFormat: {\n      LT: 'HH.mm',\n      LTS: 'HH.mm.ss',\n      L: 'DD.MM.YYYY',\n      LL: 'Do MMMM[ta] YYYY',\n      LLL: 'Do MMMM[ta] YYYY, [klo] HH.mm',\n      LLLL: 'dddd, Do MMMM[ta] YYYY, [klo] HH.mm',\n      l: 'D.M.YYYY',\n      ll: 'Do MMM YYYY',\n      lll: 'Do MMM YYYY, [klo] HH.mm',\n      llll: 'ddd, Do MMM YYYY, [klo] HH.mm'\n    },\n    calendar: {\n      sameDay: '[tänään] [klo] LT',\n      nextDay: '[huomenna] [klo] LT',\n      nextWeek: 'dddd [klo] LT',\n      lastDay: '[eilen] [klo] LT',\n      lastWeek: '[viime] dddd[na] [klo] LT',\n      sameElse: 'L'\n    },\n    relativeTime: {\n      future: '%s päästä',\n      past: '%s sitten',\n      s: translate,\n      ss: translate,\n      m: translate,\n      mm: translate,\n      h: translate,\n      hh: translate,\n      d: translate,\n      dd: translate,\n      M: translate,\n      MM: translate,\n      y: translate,\n      yy: translate\n    },\n    dayOfMonthOrdinalParse: /\\d{1,2}\\./,\n    ordinal: '%d.',\n    week: {\n      dow: 1,\n      // Monday is the first day of the week.\n      doy: 4 // The week that contains Jan 4th is the first week of the year.\n    }\n  });\n  return fi;\n});", "map": {"version": 3, "names": ["global", "factory", "exports", "module", "require", "define", "amd", "moment", "numbersPast", "split", "numbersFuture", "translate", "number", "withoutSuffix", "key", "isFuture", "result", "verbalNumber", "fi", "defineLocale", "months", "monthsShort", "weekdays", "weekdaysShort", "weekdaysMin", "longDateFormat", "LT", "LTS", "L", "LL", "LLL", "LLLL", "l", "ll", "lll", "llll", "calendar", "sameDay", "nextDay", "nextWeek", "lastDay", "lastWeek", "same<PERSON><PERSON><PERSON>", "relativeTime", "future", "past", "s", "ss", "m", "mm", "h", "hh", "d", "dd", "M", "MM", "y", "yy", "dayOfMonthOrdinalParse", "ordinal", "week", "dow", "doy"], "sources": ["C:/Users/<USER>/Documents/salechange-product/adminFront/node_modules/moment/locale/fi.js"], "sourcesContent": ["//! moment.js locale configuration\n//! locale : Finnish [fi]\n//! author : <PERSON><PERSON><PERSON> : https://github.com/bleadof\n\n;(function (global, factory) {\n   typeof exports === 'object' && typeof module !== 'undefined'\n       && typeof require === 'function' ? factory(require('../moment')) :\n   typeof define === 'function' && define.amd ? define(['../moment'], factory) :\n   factory(global.moment)\n}(this, (function (moment) { 'use strict';\n\n    //! moment.js locale configuration\n\n    var numbersPast =\n            'nolla yksi kaksi kolme neljä viisi kuusi seitsemän kahdeksan yhdeksän'.split(\n                ' '\n            ),\n        numbersFuture = [\n            'nolla',\n            'yhden',\n            'kahden',\n            'kolmen',\n            'neljän',\n            'viiden',\n            'kuuden',\n            numbersPast[7],\n            numbersPast[8],\n            numbersPast[9],\n        ];\n    function translate(number, withoutSuffix, key, isFuture) {\n        var result = '';\n        switch (key) {\n            case 's':\n                return isFuture ? 'muutaman sekunnin' : 'muutama sekunti';\n            case 'ss':\n                result = isFuture ? 'sekunnin' : 'sekuntia';\n                break;\n            case 'm':\n                return isFuture ? 'minuutin' : 'minuutti';\n            case 'mm':\n                result = isFuture ? 'minuutin' : 'minuuttia';\n                break;\n            case 'h':\n                return isFuture ? 'tunnin' : 'tunti';\n            case 'hh':\n                result = isFuture ? 'tunnin' : 'tuntia';\n                break;\n            case 'd':\n                return isFuture ? 'päivän' : 'päivä';\n            case 'dd':\n                result = isFuture ? 'päivän' : 'päivää';\n                break;\n            case 'M':\n                return isFuture ? 'kuukauden' : 'kuukausi';\n            case 'MM':\n                result = isFuture ? 'kuukauden' : 'kuukautta';\n                break;\n            case 'y':\n                return isFuture ? 'vuoden' : 'vuosi';\n            case 'yy':\n                result = isFuture ? 'vuoden' : 'vuotta';\n                break;\n        }\n        result = verbalNumber(number, isFuture) + ' ' + result;\n        return result;\n    }\n    function verbalNumber(number, isFuture) {\n        return number < 10\n            ? isFuture\n                ? numbersFuture[number]\n                : numbersPast[number]\n            : number;\n    }\n\n    var fi = moment.defineLocale('fi', {\n        months: 'tammikuu_helmikuu_maaliskuu_huhtikuu_toukokuu_kesäkuu_heinäkuu_elokuu_syyskuu_lokakuu_marraskuu_joulukuu'.split(\n            '_'\n        ),\n        monthsShort:\n            'tammi_helmi_maalis_huhti_touko_kesä_heinä_elo_syys_loka_marras_joulu'.split(\n                '_'\n            ),\n        weekdays:\n            'sunnuntai_maanantai_tiistai_keskiviikko_torstai_perjantai_lauantai'.split(\n                '_'\n            ),\n        weekdaysShort: 'su_ma_ti_ke_to_pe_la'.split('_'),\n        weekdaysMin: 'su_ma_ti_ke_to_pe_la'.split('_'),\n        longDateFormat: {\n            LT: 'HH.mm',\n            LTS: 'HH.mm.ss',\n            L: 'DD.MM.YYYY',\n            LL: 'Do MMMM[ta] YYYY',\n            LLL: 'Do MMMM[ta] YYYY, [klo] HH.mm',\n            LLLL: 'dddd, Do MMMM[ta] YYYY, [klo] HH.mm',\n            l: 'D.M.YYYY',\n            ll: 'Do MMM YYYY',\n            lll: 'Do MMM YYYY, [klo] HH.mm',\n            llll: 'ddd, Do MMM YYYY, [klo] HH.mm',\n        },\n        calendar: {\n            sameDay: '[tänään] [klo] LT',\n            nextDay: '[huomenna] [klo] LT',\n            nextWeek: 'dddd [klo] LT',\n            lastDay: '[eilen] [klo] LT',\n            lastWeek: '[viime] dddd[na] [klo] LT',\n            sameElse: 'L',\n        },\n        relativeTime: {\n            future: '%s päästä',\n            past: '%s sitten',\n            s: translate,\n            ss: translate,\n            m: translate,\n            mm: translate,\n            h: translate,\n            hh: translate,\n            d: translate,\n            dd: translate,\n            M: translate,\n            MM: translate,\n            y: translate,\n            yy: translate,\n        },\n        dayOfMonthOrdinalParse: /\\d{1,2}\\./,\n        ordinal: '%d.',\n        week: {\n            dow: 1, // Monday is the first day of the week.\n            doy: 4, // The week that contains Jan 4th is the first week of the year.\n        },\n    });\n\n    return fi;\n\n})));\n"], "mappings": "AAAA;AACA;AACA;;AAEA;AAAE,WAAUA,MAAM,EAAEC,OAAO,EAAE;EAC1B,OAAOC,OAAO,KAAK,QAAQ,IAAI,OAAOC,MAAM,KAAK,WAAW,IACrD,OAAOC,OAAO,KAAK,UAAU,GAAGH,OAAO,CAACG,OAAO,CAAC,WAAW,CAAC,CAAC,GACpE,OAAOC,MAAM,KAAK,UAAU,IAAIA,MAAM,CAACC,GAAG,GAAGD,MAAM,CAAC,CAAC,WAAW,CAAC,EAAEJ,OAAO,CAAC,GAC3EA,OAAO,CAACD,MAAM,CAACO,MAAM,CAAC;AACzB,CAAC,EAAC,IAAI,EAAG,UAAUA,MAAM,EAAE;EAAE,YAAY;;EAErC;EAEA,IAAIC,WAAW,GACP,uEAAuE,CAACC,KAAK,CACzE,GACJ,CAAC;IACLC,aAAa,GAAG,CACZ,OAAO,EACP,OAAO,EACP,QAAQ,EACR,QAAQ,EACR,QAAQ,EACR,QAAQ,EACR,QAAQ,EACRF,WAAW,CAAC,CAAC,CAAC,EACdA,WAAW,CAAC,CAAC,CAAC,EACdA,WAAW,CAAC,CAAC,CAAC,CACjB;EACL,SAASG,SAASA,CAACC,MAAM,EAAEC,aAAa,EAAEC,GAAG,EAAEC,QAAQ,EAAE;IACrD,IAAIC,MAAM,GAAG,EAAE;IACf,QAAQF,GAAG;MACP,KAAK,GAAG;QACJ,OAAOC,QAAQ,GAAG,mBAAmB,GAAG,iBAAiB;MAC7D,KAAK,IAAI;QACLC,MAAM,GAAGD,QAAQ,GAAG,UAAU,GAAG,UAAU;QAC3C;MACJ,KAAK,GAAG;QACJ,OAAOA,QAAQ,GAAG,UAAU,GAAG,UAAU;MAC7C,KAAK,IAAI;QACLC,MAAM,GAAGD,QAAQ,GAAG,UAAU,GAAG,WAAW;QAC5C;MACJ,KAAK,GAAG;QACJ,OAAOA,QAAQ,GAAG,QAAQ,GAAG,OAAO;MACxC,KAAK,IAAI;QACLC,MAAM,GAAGD,QAAQ,GAAG,QAAQ,GAAG,QAAQ;QACvC;MACJ,KAAK,GAAG;QACJ,OAAOA,QAAQ,GAAG,QAAQ,GAAG,OAAO;MACxC,KAAK,IAAI;QACLC,MAAM,GAAGD,QAAQ,GAAG,QAAQ,GAAG,QAAQ;QACvC;MACJ,KAAK,GAAG;QACJ,OAAOA,QAAQ,GAAG,WAAW,GAAG,UAAU;MAC9C,KAAK,IAAI;QACLC,MAAM,GAAGD,QAAQ,GAAG,WAAW,GAAG,WAAW;QAC7C;MACJ,KAAK,GAAG;QACJ,OAAOA,QAAQ,GAAG,QAAQ,GAAG,OAAO;MACxC,KAAK,IAAI;QACLC,MAAM,GAAGD,QAAQ,GAAG,QAAQ,GAAG,QAAQ;QACvC;IACR;IACAC,MAAM,GAAGC,YAAY,CAACL,MAAM,EAAEG,QAAQ,CAAC,GAAG,GAAG,GAAGC,MAAM;IACtD,OAAOA,MAAM;EACjB;EACA,SAASC,YAAYA,CAACL,MAAM,EAAEG,QAAQ,EAAE;IACpC,OAAOH,MAAM,GAAG,EAAE,GACZG,QAAQ,GACJL,aAAa,CAACE,MAAM,CAAC,GACrBJ,WAAW,CAACI,MAAM,CAAC,GACvBA,MAAM;EAChB;EAEA,IAAIM,EAAE,GAAGX,MAAM,CAACY,YAAY,CAAC,IAAI,EAAE;IAC/BC,MAAM,EAAE,0GAA0G,CAACX,KAAK,CACpH,GACJ,CAAC;IACDY,WAAW,EACP,sEAAsE,CAACZ,KAAK,CACxE,GACJ,CAAC;IACLa,QAAQ,EACJ,oEAAoE,CAACb,KAAK,CACtE,GACJ,CAAC;IACLc,aAAa,EAAE,sBAAsB,CAACd,KAAK,CAAC,GAAG,CAAC;IAChDe,WAAW,EAAE,sBAAsB,CAACf,KAAK,CAAC,GAAG,CAAC;IAC9CgB,cAAc,EAAE;MACZC,EAAE,EAAE,OAAO;MACXC,GAAG,EAAE,UAAU;MACfC,CAAC,EAAE,YAAY;MACfC,EAAE,EAAE,kBAAkB;MACtBC,GAAG,EAAE,+BAA+B;MACpCC,IAAI,EAAE,qCAAqC;MAC3CC,CAAC,EAAE,UAAU;MACbC,EAAE,EAAE,aAAa;MACjBC,GAAG,EAAE,0BAA0B;MAC/BC,IAAI,EAAE;IACV,CAAC;IACDC,QAAQ,EAAE;MACNC,OAAO,EAAE,mBAAmB;MAC5BC,OAAO,EAAE,qBAAqB;MAC9BC,QAAQ,EAAE,eAAe;MACzBC,OAAO,EAAE,kBAAkB;MAC3BC,QAAQ,EAAE,2BAA2B;MACrCC,QAAQ,EAAE;IACd,CAAC;IACDC,YAAY,EAAE;MACVC,MAAM,EAAE,WAAW;MACnBC,IAAI,EAAE,WAAW;MACjBC,CAAC,EAAEnC,SAAS;MACZoC,EAAE,EAAEpC,SAAS;MACbqC,CAAC,EAAErC,SAAS;MACZsC,EAAE,EAAEtC,SAAS;MACbuC,CAAC,EAAEvC,SAAS;MACZwC,EAAE,EAAExC,SAAS;MACbyC,CAAC,EAAEzC,SAAS;MACZ0C,EAAE,EAAE1C,SAAS;MACb2C,CAAC,EAAE3C,SAAS;MACZ4C,EAAE,EAAE5C,SAAS;MACb6C,CAAC,EAAE7C,SAAS;MACZ8C,EAAE,EAAE9C;IACR,CAAC;IACD+C,sBAAsB,EAAE,WAAW;IACnCC,OAAO,EAAE,KAAK;IACdC,IAAI,EAAE;MACFC,GAAG,EAAE,CAAC;MAAE;MACRC,GAAG,EAAE,CAAC,CAAE;IACZ;EACJ,CAAC,CAAC;EAEF,OAAO5C,EAAE;AAEb,CAAE,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}