{"ast": null, "code": "import { EnumAllowType } from '../enum/enumAllowType';\nimport { LocalStorageService } from '../services/local-storage.service';\nimport { STORAGE_KEY } from '../constant/constant';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nexport let AllowHelper = /*#__PURE__*/(() => {\n  class AllowHelper {\n    constructor(router) {\n      this.router = router;\n      this.allow = [];\n    }\n    isCreate() {\n      this.getAllow();\n      return this.allow.filter(x => this.router.url.indexOf(x.CPageUrl) !== -1 && x.CCompetenceType === EnumAllowType.Create).length > 0;\n    }\n    isUpdate() {\n      this.getAllow();\n      return this.allow.filter(x => this.router.url.indexOf(x.CPageUrl) !== -1 && x.CCompetenceType === EnumAllowType.Update).length > 0;\n    }\n    isDelete() {\n      this.getAllow();\n      return this.allow.filter(x => this.router.url.indexOf(x.CPageUrl) !== -1 && x.CCompetenceType === EnumAllowType.Delete).length > 0;\n    }\n    isRead() {\n      this.getAllow();\n      return this.allow.filter(x => this.router.url.indexOf(x.CPageUrl) !== -1 && x.CCompetenceType === EnumAllowType.Read).length > 0;\n    }\n    isExcelImport() {\n      this.getAllow();\n      return this.allow.filter(x => this.router.url.indexOf(x.CPageUrl) !== -1 && x.CCompetenceType === EnumAllowType.ExcelImport).length > 0;\n    }\n    isExcelExport() {\n      this.getAllow();\n      return this.allow.filter(x => this.router.url.indexOf(x.CPageUrl) !== -1 && x.CCompetenceType === EnumAllowType.ExcelExport).length > 0;\n    }\n    isReport() {\n      this.getAllow();\n      return this.allow.filter(x => this.router.url.indexOf(x.CPageUrl) !== -1 && x.CCompetenceType === EnumAllowType.Report).length > 0;\n    }\n    isApiImport() {\n      this.getAllow();\n      return this.allow.filter(x => this.router.url.indexOf(x.CPageUrl) !== -1 && x.CCompetenceType === EnumAllowType.ApiImport).length > 0;\n    }\n    isChangePayStatus() {\n      this.getAllow();\n      return this.allow.filter(x => this.router.url.indexOf(x.CPageUrl) !== -1 && x.CCompetenceType === EnumAllowType.ChangePayStatus).length > 0;\n    }\n    isChangeProgress() {\n      this.getAllow();\n      return this.allow.filter(x => this.router.url.indexOf(x.CPageUrl) !== -1 && x.CCompetenceType === EnumAllowType.ChangeProgress).length > 0;\n    }\n    getAllow() {\n      const allowJson = LocalStorageService.GetLocalStorage(STORAGE_KEY.ALLOW);\n      if (allowJson !== null && allowJson !== '') {\n        this.allow = JSON.parse(allowJson);\n      }\n    }\n    static {\n      this.ɵfac = function AllowHelper_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || AllowHelper)(i0.ɵɵinject(i1.Router));\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: AllowHelper,\n        factory: AllowHelper.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return AllowHelper;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}