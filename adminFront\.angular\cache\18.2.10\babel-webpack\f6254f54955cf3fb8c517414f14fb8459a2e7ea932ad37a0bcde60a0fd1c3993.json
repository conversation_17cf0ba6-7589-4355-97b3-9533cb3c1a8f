{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { Component, Input, Output, EventEmitter, forwardRef, ViewChild } from '@angular/core';\nimport { NG_VALUE_ACCESSOR } from '@angular/forms';\nlet HouseholdBindingComponent = class HouseholdBindingComponent {\n  constructor(cdr, houseCustomService, dialogService) {\n    this.cdr = cdr;\n    this.houseCustomService = houseCustomService;\n    this.dialogService = dialogService;\n    this.placeholder = '請選擇戶別';\n    this.maxSelections = null;\n    this.disabled = false;\n    this.buildCaseId = null; // 新增：建案ID\n    this.buildingData = {};\n    this.showSelectedArea = true;\n    this.allowSearch = true;\n    this.allowBatchSelect = true;\n    this.excludedHouseIds = []; // 改為：排除的戶別ID（已被其他元件選擇）\n    this.useHouseNameMode = false; // 新增：使用戶別名稱模式\n    this.selectionChange = new EventEmitter();\n    this.houseIdChange = new EventEmitter(); // 新增：回傳 houseId 陣列\n    this.houseNameChange = new EventEmitter(); // 新增：useHouseNameMode 時回傳戶別名稱陣列\n    this.isOpen = false;\n    this.selectedBuilding = '';\n    this.searchTerm = '';\n    this.selectedFloor = ''; // 新增：選中的樓層\n    this.selectedHouseIds = []; // 改為：使用 houseId 作為選擇的key\n    this.selectedHouseNames = []; // 新增：useHouseNameMode 時使用的戶別名稱陣列\n    this.buildings = [];\n    this.floors = []; // 新增：當前棧別的樓層列表\n    this.filteredHouseholds = []; // 保持為字串陣列用於UI顯示\n    this.selectedByBuilding = {}; // 改為：儲存 houseId\n    this.isLoading = false; // 新增：載入狀態  // ControlValueAccessor implementation\n    this.onChange = value => {};\n    this.onTouched = () => {};\n  }\n  writeValue(value) {\n    console.log('writeValue called with:', value, 'type:', typeof value?.[0], 'useHouseNameMode:', this.useHouseNameMode);\n    if (!value || value.length === 0) {\n      this.selectedHouseIds = [];\n      this.selectedHouseNames = [];\n    } else {\n      const firstItem = value[0];\n      if (this.useHouseNameMode) {\n        // useHouseNameMode: 期望接收戶別名稱陣列\n        if (typeof firstItem === 'string') {\n          this.selectedHouseNames = [...new Set(value)]; // 去除重複的戶別名稱\n          // 將戶別名稱轉換為 houseId（用於內部邏輯），會自動處理重複名稱\n          this.selectedHouseIds = this.convertHouseNamesToIds(this.selectedHouseNames);\n          console.log('useHouseNameMode: 使用傳入的戶別名稱陣列（已去重）');\n        } else {\n          console.error('useHouseNameMode 期望接收 string[] 但收到:', typeof firstItem);\n          this.selectedHouseNames = [];\n          this.selectedHouseIds = [];\n        }\n      } else {\n        // 一般模式: 期望接收 houseId 陣列\n        if (typeof firstItem === 'number') {\n          this.selectedHouseIds = value;\n          // 將 houseId 轉換為戶別名稱（用於顯示）\n          this.selectedHouseNames = this.convertIdsToHouseNames(this.selectedHouseIds);\n          console.log('一般模式: 使用傳入的 houseId 陣列');\n        } else if (typeof firstItem === 'string') {\n          // 向下相容：如果收到字串但不在 useHouseNameMode，發出警告\n          console.error('⚠️ 警告：一般模式下收到戶別名稱陣列而不是 houseId 陣列！');\n          console.error('⚠️ 建議父元件改用 houseId 陣列或啟用 useHouseNameMode');\n          return;\n        } else {\n          console.error('writeValue 收到未知格式的資料:', value);\n          this.selectedHouseIds = [];\n          this.selectedHouseNames = [];\n        }\n      }\n    }\n    console.log('selectedHouseIds set to:', this.selectedHouseIds);\n    console.log('selectedHouseNames set to:', this.selectedHouseNames);\n    this.updateSelectedByBuilding();\n  }\n  registerOnChange(fn) {\n    this.onChange = fn;\n  }\n  registerOnTouched(fn) {\n    this.onTouched = fn;\n  }\n  setDisabledState(isDisabled) {\n    this.disabled = isDisabled;\n  }\n  ngOnInit() {\n    this.initializeData();\n  }\n  ngOnChanges(changes) {\n    if (changes['buildCaseId'] && this.buildCaseId) {\n      // 當建案ID變更時，重新載入資料\n      this.initializeData();\n    }\n    if (changes['buildingData']) {\n      // 當 buildingData 變更時，重新初始化\n      this.buildings = Object.keys(this.buildingData || {});\n      console.log('buildingData updated:', this.buildingData);\n      this.updateFilteredHouseholds();\n      this.updateSelectedByBuilding();\n    }\n    if (changes['excludedHouseIds']) {\n      // 當排除列表變化時，重新更新顯示\n      this.updateFilteredHouseholds();\n      console.log('Excluded house IDs updated:', this.excludedHouseIds);\n    }\n    if (changes['useHouseNameMode']) {\n      // 當 useHouseNameMode 變更時，重新同步選擇狀態\n      console.log('useHouseNameMode changed to:', this.useHouseNameMode);\n      // 在 useHouseNameMode 切換時，確保樓層選擇被重置\n      if (this.useHouseNameMode) {\n        this.selectedFloor = '';\n      }\n    }\n  }\n  initializeData() {\n    // 使用傳入的 buildingData\n    if (this.buildingData && Object.keys(this.buildingData).length > 0) {\n      this.buildings = Object.keys(this.buildingData);\n      console.log('Component initialized with provided buildingData:', this.buildings);\n      this.updateSelectedByBuilding();\n    } else {\n      // 沒有 buildingData，保持空狀態\n      this.buildings = [];\n      console.log('No buildingData provided');\n    }\n  }\n  updateSelectedByBuilding() {\n    const grouped = {};\n    this.selectedHouseIds.forEach(houseId => {\n      for (const building of this.buildings) {\n        const item = this.buildingData[building]?.find(h => h.houseId === houseId);\n        if (item) {\n          if (!grouped[building]) grouped[building] = [];\n          grouped[building].push(houseId);\n          break;\n        }\n      }\n    });\n    this.selectedByBuilding = grouped;\n  }\n  onBuildingSelect(building) {\n    console.log('Building selected:', building);\n    this.selectedBuilding = building;\n    this.selectedFloor = ''; // 重置樓層選擇\n    this.searchTerm = '';\n    this.updateFloorsForBuilding(); // 更新樓層列表\n    this.updateFilteredHouseholds();\n    console.log('Filtered households count:', this.filteredHouseholds.length);\n    // 手動觸發變更偵測\n    this.cdr.detectChanges();\n  }\n  onBuildingClick(building) {\n    console.log('Building clicked (mousedown):', building);\n  }\n  updateFilteredHouseholds() {\n    console.log('Updating filtered households for building:', this.selectedBuilding, 'floor:', this.selectedFloor, 'useHouseNameMode:', this.useHouseNameMode);\n    if (!this.selectedBuilding) {\n      this.filteredHouseholds = [];\n      return;\n    }\n    const households = this.buildingData[this.selectedBuilding] || [];\n    console.log('Available households for building:', households.length);\n    // 提取戶別代碼並進行搜尋和樓層過濾\n    const filteredItems = households.filter(h => {\n      // 樓層篩選：在 useHouseNameMode 時跳過樓層篩選，否則按原邏輯篩選\n      const floorMatch = this.useHouseNameMode || !this.selectedFloor || h.floor === this.selectedFloor;\n      // 搜尋篩選：戶別代碼包含搜尋詞\n      const searchMatch = !this.searchTerm || h.houseName.toLowerCase().includes(this.searchTerm.toLowerCase());\n      return floorMatch && searchMatch;\n    });\n    // 只提取戶別名稱用於UI顯示（但onSelectAllFiltered會直接使用物件）\n    this.filteredHouseholds = filteredItems.map(h => h.houseName);\n    console.log('Filtered households result:', this.filteredHouseholds.length);\n    console.log('Filtered items details:', filteredItems.map(h => `${h.houseName}-${h.floor} (ID:${h.houseId})`));\n  }\n  onSearchChange(event) {\n    this.searchTerm = event.target.value;\n    this.updateFilteredHouseholds();\n    console.log('Search term changed:', this.searchTerm);\n  }\n  resetSearch() {\n    this.searchTerm = '';\n    this.updateFilteredHouseholds();\n    console.log('Search reset');\n  }\n  onHouseholdToggle(houseId) {\n    console.log('onHouseholdToggle called with houseId:', houseId);\n    console.log('Current selectedHouseIds:', this.selectedHouseIds);\n    if (!houseId) {\n      console.log(`無效的 houseId: ${houseId}`);\n      return;\n    }\n    // 防止選擇已排除的戶別\n    if (this.isHouseIdExcluded(houseId)) {\n      console.log(`戶別 ID ${houseId} 已被其他元件選擇，無法重複選擇`);\n      return;\n    }\n    // 取得被點擊戶別的名稱\n    const clickedHousehold = this.getHouseholdByHouseId(houseId);\n    if (!clickedHousehold) {\n      console.log(`找不到 houseId ${houseId} 對應的戶別資訊`);\n      return;\n    }\n    let newSelection;\n    if (this.useHouseNameMode) {\n      // useHouseNameMode: 處理同名戶別的邏輯\n      const houseName = clickedHousehold.houseName;\n      const allMatchingHouseIds = this.getAllHouseIdsByHouseName(houseName);\n      // 檢查是否有任何同名戶別已被選中\n      const hasAnySelected = allMatchingHouseIds.some(id => this.selectedHouseIds.includes(id));\n      if (hasAnySelected) {\n        // 如果有同名戶別被選中，移除所有同名戶別\n        newSelection = this.selectedHouseIds.filter(id => !allMatchingHouseIds.includes(id));\n        console.log(`useHouseNameMode: 移除所有同名戶別 \"${houseName}\":`, allMatchingHouseIds);\n      } else {\n        // 如果沒有同名戶別被選中，只添加第一個（通常是當前點擊的）\n        if (this.maxSelections && this.selectedHouseIds.length >= this.maxSelections) {\n          console.log('已達到最大選擇數量');\n          return;\n        }\n        newSelection = [...this.selectedHouseIds, allMatchingHouseIds[0]];\n        console.log(`useHouseNameMode: 添加戶別 \"${houseName}\" 的第一個項目:`, allMatchingHouseIds[0]);\n      }\n    } else {\n      // 一般模式: 原有邏輯\n      const isSelected = this.isHouseIdSelected(houseId);\n      if (isSelected) {\n        newSelection = this.selectedHouseIds.filter(id => id !== houseId);\n      } else {\n        if (this.maxSelections && this.selectedHouseIds.length >= this.maxSelections) {\n          console.log('已達到最大選擇數量');\n          return;\n        }\n        newSelection = [...this.selectedHouseIds, houseId];\n      }\n    }\n    this.selectedHouseIds = newSelection;\n    this.emitChanges();\n  }\n  onRemoveHousehold(houseId) {\n    this.selectedHouseIds = this.selectedHouseIds.filter(id => id !== houseId);\n    this.emitChanges();\n  }\n  onSelectAllFiltered() {\n    console.log('onSelectAllFiltered called');\n    console.log('selectedBuilding:', this.selectedBuilding);\n    console.log('selectedFloor:', this.selectedFloor);\n    console.log('searchTerm:', this.searchTerm);\n    if (!this.selectedBuilding) {\n      console.log('No building selected');\n      return;\n    }\n    // 使用 getUniqueHouseholdsForDisplay 方法來獲取要處理的戶別列表\n    const filteredHouseholdItems = this.getUniqueHouseholdsForDisplay();\n    console.log('Filtered household items:', filteredHouseholdItems.map(h => `${h.houseName}-${h.floor} (ID:${h.houseId})`));\n    if (filteredHouseholdItems.length === 0) {\n      console.log('No filtered households found');\n      return;\n    }\n    // 計算可以新增的戶別數量\n    const currentCount = this.selectedHouseIds.length;\n    const maxAllowed = this.maxSelections || Infinity;\n    const remainingSlots = maxAllowed - currentCount;\n    // 取得尚未選擇且未被排除的過濾戶別ID\n    const unselectedFilteredIds = [];\n    for (const household of filteredHouseholdItems) {\n      if (household.houseId) {\n        if (this.useHouseNameMode) {\n          // useHouseNameMode: 檢查是否有任何同名戶別已被選擇\n          const allMatchingHouseIds = this.getAllHouseIdsByHouseName(household.houseName);\n          const hasAnySelected = allMatchingHouseIds.some(id => this.selectedHouseIds.includes(id));\n          const hasAnyExcluded = allMatchingHouseIds.some(id => this.isHouseIdExcluded(id));\n          if (!hasAnySelected && !hasAnyExcluded) {\n            unselectedFilteredIds.push(allMatchingHouseIds[0]); // 只選擇第一個\n          }\n        } else {\n          // 一般模式: 原有邏輯\n          if (!this.isHouseIdSelected(household.houseId) && !this.isHouseIdExcluded(household.houseId)) {\n            unselectedFilteredIds.push(household.houseId);\n          }\n        }\n      }\n    }\n    console.log('Unselected filtered IDs:', unselectedFilteredIds);\n    // 根據剩餘空間決定要新增的戶別\n    const toAdd = unselectedFilteredIds.slice(0, remainingSlots);\n    if (toAdd.length > 0) {\n      this.selectedHouseIds = [...this.selectedHouseIds, ...toAdd];\n      this.emitChanges();\n      console.log(`全選當前: 新增了 ${toAdd.length} 個戶別，IDs:`, toAdd);\n    } else {\n      console.log('No households to add');\n    }\n  }\n  onSelectAllBuilding() {\n    if (!this.selectedBuilding) return;\n    // 取得當前棟別的所有戶別\n    const buildingHouseholds = this.buildingData[this.selectedBuilding] || [];\n    // 計算可以新增的戶別數量\n    const currentCount = this.selectedHouseIds.length;\n    const maxAllowed = this.maxSelections || Infinity;\n    const remainingSlots = maxAllowed - currentCount;\n    // 取得尚未選擇且未被排除的棟別戶別 ID\n    const unselectedBuildingIds = [];\n    if (this.useHouseNameMode) {\n      // useHouseNameMode: 只選擇唯一的戶別名稱\n      const processedHouseNames = new Set();\n      for (const household of buildingHouseholds) {\n        if (household.houseId && household.houseName && !processedHouseNames.has(household.houseName)) {\n          processedHouseNames.add(household.houseName);\n          const allMatchingHouseIds = this.getAllHouseIdsByHouseName(household.houseName);\n          const hasAnySelected = allMatchingHouseIds.some(id => this.selectedHouseIds.includes(id));\n          const hasAnyExcluded = allMatchingHouseIds.some(id => this.isHouseholdExcluded(id));\n          if (!hasAnySelected && !hasAnyExcluded) {\n            unselectedBuildingIds.push(allMatchingHouseIds[0]); // 只選擇第一個\n          }\n        }\n      }\n    } else {\n      // 一般模式: 原有邏輯\n      for (const household of buildingHouseholds) {\n        if (household.houseId && !this.selectedHouseIds.includes(household.houseId) && !this.isHouseholdExcluded(household.houseId)) {\n          unselectedBuildingIds.push(household.houseId);\n        }\n      }\n    }\n    // 根據剩餘空間決定要新增的戶別\n    const toAdd = unselectedBuildingIds.slice(0, remainingSlots);\n    if (toAdd.length > 0) {\n      this.selectedHouseIds = [...this.selectedHouseIds, ...toAdd];\n      this.emitChanges();\n      console.log(`全選棟別: 新增了 ${toAdd.length} 個戶別`);\n    }\n  }\n  onUnselectAllBuilding() {\n    if (!this.selectedBuilding) return;\n    const buildingHouseholds = this.buildingData[this.selectedBuilding] || [];\n    const buildingHouseIds = buildingHouseholds.map(h => h.houseId).filter(id => id !== undefined);\n    this.selectedHouseIds = this.selectedHouseIds.filter(id => !buildingHouseIds.includes(id));\n    this.emitChanges();\n  }\n  onClearAll() {\n    this.selectedHouseIds = [];\n    this.emitChanges();\n  }\n  emitChanges() {\n    this.updateSelectedByBuilding();\n    console.log('Emitting changes - selectedHouseIds:', this.selectedHouseIds); // 根據模式決定要回傳的資料格式\n    if (this.useHouseNameMode) {\n      // useHouseNameMode: 回傳戶別名稱陣列（去重複）\n      this.selectedHouseNames = this.convertIdsToHouseNames(this.selectedHouseIds);\n      const uniqueHouseNames = [...new Set(this.selectedHouseNames)]; // 確保去重複\n      console.log('useHouseNameMode - 回傳戶別名稱陣列（已去重）:', uniqueHouseNames);\n      this.onChange([...uniqueHouseNames]);\n      this.houseNameChange.emit([...uniqueHouseNames]);\n    } else {\n      // 一般模式: 回傳 houseId 陣列\n      console.log('一般模式 - 回傳 houseId 陣列:', this.selectedHouseIds);\n      this.onChange([...this.selectedHouseIds]);\n      // 回傳 houseId 陣列\n      const houseIds = this.selectedHouseIds.filter(id => id !== undefined);\n      console.log('House IDs to emit:', houseIds);\n      this.houseIdChange.emit(houseIds);\n    }\n    this.onTouched();\n    // 無論哪種模式都回傳完整的 HouseholdItem 陣列（向下相容）\n    const selectedItems = this.selectedHouseIds.map(houseId => {\n      for (const building of this.buildings) {\n        const item = this.buildingData[building]?.find(h => h.houseId === houseId);\n        if (item) return item;\n      }\n      return null;\n    }).filter(item => item !== null);\n    console.log('Selected items to emit:', selectedItems);\n    this.selectionChange.emit(selectedItems);\n  }\n  toggleDropdown() {\n    if (!this.disabled) {\n      this.openDialog();\n      console.log('Opening household selection dialog');\n      console.log('Buildings available:', this.buildings);\n    }\n  }\n  openDialog() {\n    this.dialogService.open(this.householdDialog, {\n      context: {},\n      closeOnBackdropClick: false,\n      closeOnEsc: true,\n      autoFocus: false\n    });\n  }\n  closeDropdown() {\n    // 這個方法現在用於關閉對話框\n    // 對話框的關閉將由 NbDialogRef 處理\n  }\n  isHouseholdSelected(houseId) {\n    if (!houseId) return false;\n    if (this.useHouseNameMode) {\n      // useHouseNameMode: 檢查是否有任何同名戶別被選中\n      const household = this.getHouseholdByHouseId(houseId);\n      if (household) {\n        const allMatchingHouseIds = this.getAllHouseIdsByHouseName(household.houseName);\n        return allMatchingHouseIds.some(id => this.selectedHouseIds.includes(id));\n      }\n      return false;\n    } else {\n      // 一般模式: 直接檢查 houseId\n      return this.selectedHouseIds.includes(houseId);\n    }\n  }\n  isHouseholdExcluded(houseId) {\n    if (!houseId) return false;\n    return this.excludedHouseIds.includes(houseId);\n  }\n  isHouseholdDisabled(houseId) {\n    if (!houseId) return true;\n    return this.isHouseholdExcluded(houseId) || !this.canSelectMore() && !this.isHouseholdSelected(houseId);\n  }\n  canSelectMore() {\n    return !this.maxSelections || this.selectedHouseIds.length < this.maxSelections;\n  }\n  isAllBuildingSelected() {\n    if (!this.selectedBuilding) return false;\n    const buildingHouseholds = this.buildingData[this.selectedBuilding].filter(h => !h.isDisabled && h.houseId !== undefined);\n    return buildingHouseholds.length > 0 && buildingHouseholds.every(household => household.houseId && this.selectedHouseIds.includes(household.houseId));\n  }\n  isSomeBuildingSelected() {\n    if (!this.selectedBuilding) return false;\n    const buildingHouseholds = this.buildingData[this.selectedBuilding] || [];\n    return buildingHouseholds.some(household => household.houseId && this.selectedHouseIds.includes(household.houseId));\n  }\n  getSelectedByBuilding() {\n    return this.selectedByBuilding;\n  }\n  getBuildingCount(building) {\n    if (this.useHouseNameMode) {\n      // useHouseNameMode: 返回唯一戶別名稱的數量\n      const households = this.buildingData[building] || [];\n      const uniqueHouseNames = new Set(households.map(h => h.houseName));\n      return uniqueHouseNames.size;\n    } else {\n      // 一般模式: 返回總戶別數量\n      return this.buildingData[building]?.length || 0;\n    }\n  }\n  getSelectedCount() {\n    if (this.useHouseNameMode) {\n      // useHouseNameMode: 返回唯一戶別名稱的數量\n      const uniqueHouseNames = this.convertIdsToHouseNames(this.selectedHouseIds);\n      return uniqueHouseNames.length;\n    } else {\n      // 一般模式: 返回實際選中的戶別數量\n      return this.selectedHouseIds.length;\n    }\n  }\n  // 輔助方法：安全地獲取建築物的已選戶別 ID\n  getBuildingSelectedHouseIds(building) {\n    return this.selectedByBuilding[building] || [];\n  }\n  // 輔助方法：檢查建築物是否有已選戶別\n  hasBuildingSelected(building) {\n    return !!(this.selectedByBuilding[building] && this.selectedByBuilding[building].length > 0);\n  }\n  // 新增：更新當前棧別的樓層計數\n  updateFloorsForBuilding() {\n    if (!this.selectedBuilding) {\n      this.floors = [];\n      return;\n    }\n    const households = this.buildingData[this.selectedBuilding] || [];\n    const floorSet = new Set();\n    households.forEach(household => {\n      if (household.floor) {\n        floorSet.add(household.floor);\n      }\n    });\n    // 對樓層進行自然排序\n    this.floors = Array.from(floorSet).sort((a, b) => {\n      const numA = parseInt(a.replace(/[^0-9]/g, ''));\n      const numB = parseInt(b.replace(/[^0-9]/g, ''));\n      return numA - numB;\n    });\n    console.log('Updated floors for building:', this.selectedBuilding, this.floors);\n  }\n  // 新增：樓層選擇處理\n  onFloorSelect(floor) {\n    console.log('Floor selected:', floor);\n    this.selectedFloor = this.selectedFloor === floor ? '' : floor; // 切換選擇\n    this.updateFilteredHouseholds();\n    this.cdr.detectChanges();\n  }\n  // 新增：取得當前棧別的樓層計數\n  getFloorCount(floor) {\n    if (!this.selectedBuilding) return 0;\n    const households = this.buildingData[this.selectedBuilding] || [];\n    return households.filter(h => h.floor === floor).length;\n  }\n  // 新增：取得戶別的樓層資訊\n  getHouseholdFloor(householdCode) {\n    if (!this.selectedBuilding) return '';\n    const households = this.buildingData[this.selectedBuilding] || [];\n    const household = households.find(h => h.houseName === householdCode);\n    return household?.floor || '';\n  }\n  // 新增：取得戶別的完整資訊（包含樓層）\n  getHouseholdInfo(householdCode) {\n    for (const building of this.buildings) {\n      const households = this.buildingData[building] || [];\n      const household = households.find(h => h.houseName === householdCode);\n      if (household) {\n        return {\n          houseName: household.houseName,\n          floor: household.floor || ''\n        };\n      }\n    }\n    return {\n      houseName: householdCode,\n      floor: ''\n    };\n  }\n  // 新增：根據 houseId 取得戶別的完整資訊\n  getHouseholdInfoById(houseId) {\n    for (const building of this.buildings) {\n      const households = this.buildingData[building] || [];\n      const household = households.find(h => h.houseId === houseId);\n      if (household) {\n        return {\n          houseName: household.houseName,\n          floor: household.floor || ''\n        };\n      }\n    }\n    return {\n      houseName: `ID:${houseId}`,\n      floor: ''\n    };\n  }\n  // 新增：檢查搜尋是否有結果\n  hasNoSearchResults() {\n    if (!this.searchTerm || !this.selectedBuilding || !this.buildingData[this.selectedBuilding]) {\n      return false;\n    }\n    const filtered = this.buildingData[this.selectedBuilding].filter(h => {\n      const floorMatch = this.useHouseNameMode || !this.selectedFloor || h.floor === this.selectedFloor;\n      const searchMatch = h.houseName.toLowerCase().includes(this.searchTerm.toLowerCase());\n      return floorMatch && searchMatch;\n    });\n    return filtered.length === 0;\n  }\n  // 新增：取得過濾後的戶別數量\n  getFilteredHouseholdsCount() {\n    return this.getUniqueHouseholdsForDisplay().length;\n  }\n  // 新增：產生戶別的唯一識別符\n  getHouseholdUniqueId(household) {\n    return household.houseId ? household.houseId.toString() : `${household.houseName}_${household.floor}`;\n  }\n  // 新增：輔助方法 - 根據 houseId 查找 HouseholdItem\n  getHouseholdByHouseId(houseId) {\n    for (const building of this.buildings) {\n      const households = this.buildingData[building] || [];\n      const household = households.find(h => h.houseId === houseId);\n      if (household) return household;\n    }\n    return null;\n  } // 新增：輔助方法 - 根據 houseName 查找 houseId\n  getHouseIdByHouseName(houseName) {\n    const matchingHouseholds = [];\n    // 收集所有符合名稱的戶別\n    for (const building of this.buildings) {\n      const households = this.buildingData[building] || [];\n      const matches = households.filter(h => h.houseName === houseName);\n      matches.forEach(household => {\n        matchingHouseholds.push({\n          building,\n          household\n        });\n      });\n    }\n    console.log(`查找 houseName \"${houseName}\" 的結果:`, matchingHouseholds);\n    if (matchingHouseholds.length === 0) {\n      console.warn(`找不到 houseName \"${houseName}\" 對應的戶別`);\n      return null;\n    }\n    if (matchingHouseholds.length > 1) {\n      console.warn(`發現多個同名戶別 \"${houseName}\":`, matchingHouseholds.map(m => `${m.building}-${m.household.floor}`));\n      console.warn(`將返回第一個找到的: ${matchingHouseholds[0].building}-${matchingHouseholds[0].household.floor}`);\n    }\n    const firstMatch = matchingHouseholds[0];\n    return firstMatch.household.houseId || null;\n  }\n  // 新增：輔助方法 - 根據 houseName 查找所有對應的 houseId（處理重複名稱）\n  getAllHouseIdsByHouseName(houseName) {\n    const houseIds = [];\n    // 收集所有符合名稱的戶別\n    for (const building of this.buildings) {\n      const households = this.buildingData[building] || [];\n      const matches = households.filter(h => h.houseName === houseName);\n      matches.forEach(household => {\n        if (household.houseId) {\n          houseIds.push(household.houseId);\n        }\n      });\n    }\n    return houseIds;\n  }\n  // 新增：輔助方法 - 檢查 houseId 是否被選中\n  isHouseIdSelected(houseId) {\n    return this.selectedHouseIds.includes(houseId);\n  }\n  // 新增：輔助方法 - 檢查 houseId 是否被排除\n  isHouseIdExcluded(houseId) {\n    return this.excludedHouseIds.includes(houseId);\n  }\n  // 新增：從唯一識別符獲取戶別物件\n  getHouseholdFromUniqueId(uniqueId) {\n    for (const building of this.buildings) {\n      const households = this.buildingData[building] || [];\n      const household = households.find(h => this.getHouseholdUniqueId(h) === uniqueId);\n      if (household) return household;\n    }\n    return null;\n  } // 新增：將戶別名稱陣列轉換為 houseId 陣列（在 useHouseNameMode 下只選擇第一個匹配項）\n  convertHouseNamesToIds(houseNames) {\n    const houseIds = [];\n    const uniqueHouseNames = [...new Set(houseNames)]; // 去除重複的戶別名稱\n    for (const houseName of uniqueHouseNames) {\n      const matchingHouseIds = this.getAllHouseIdsByHouseName(houseName);\n      if (matchingHouseIds.length > 0) {\n        if (this.useHouseNameMode) {\n          // useHouseNameMode: 只選擇第一個匹配的 houseId，避免多個同名戶別都被選中\n          houseIds.push(matchingHouseIds[0]);\n          if (matchingHouseIds.length > 1) {\n            console.log(`戶別名稱 \"${houseName}\" 有 ${matchingHouseIds.length} 個重複項目，在 useHouseNameMode 下只選擇第一個:`, matchingHouseIds[0]);\n          }\n        } else {\n          // 一般模式: 將所有對應的 houseId 都加入（保持原有邏輯）\n          houseIds.push(...matchingHouseIds);\n          if (matchingHouseIds.length > 1) {\n            console.warn(`戶別名稱 \"${houseName}\" 有 ${matchingHouseIds.length} 個重複項目，已全部加入選擇:`, matchingHouseIds);\n          }\n        }\n      } else {\n        console.warn(`無法找到戶別名稱 \"${houseName}\" 對應的 houseId`);\n      }\n    }\n    // 去除重複的 houseId\n    return [...new Set(houseIds)];\n  }\n  // 新增：將 houseId 陣列轉換為戶別名稱陣列（去重複）\n  convertIdsToHouseNames(houseIds) {\n    const houseNames = [];\n    const uniqueHouseIds = [...new Set(houseIds)]; // 去除重複的 houseId\n    for (const houseId of uniqueHouseIds) {\n      const householdInfo = this.getHouseholdInfoById(houseId);\n      if (householdInfo.houseName && !householdInfo.houseName.startsWith('ID:')) {\n        houseNames.push(householdInfo.houseName);\n      } else {\n        console.warn(`無法找到 houseId ${houseId} 對應的戶別名稱`);\n      }\n    }\n    // 去除重複的戶別名稱\n    return [...new Set(houseNames)];\n  }\n  // 新增：取得去重複的戶別列表（用於 useHouseNameMode 顯示）\n  getUniqueHouseholdsForDisplay() {\n    if (!this.selectedBuilding || !this.buildingData[this.selectedBuilding]) {\n      return [];\n    }\n    const households = this.buildingData[this.selectedBuilding] || [];\n    if (!this.useHouseNameMode) {\n      // 一般模式：返回所有戶別\n      return households.filter(h => {\n        const floorMatch = !this.selectedFloor || h.floor === this.selectedFloor;\n        const searchMatch = !this.searchTerm || h.houseName.toLowerCase().includes(this.searchTerm.toLowerCase());\n        return floorMatch && searchMatch;\n      });\n    }\n    // useHouseNameMode：只返回唯一的戶別名稱\n    const uniqueHouseNames = new Set();\n    const uniqueHouseholds = [];\n    for (const household of households) {\n      // 搜尋篩選\n      const searchMatch = !this.searchTerm || household.houseName.toLowerCase().includes(this.searchTerm.toLowerCase());\n      if (searchMatch && !uniqueHouseNames.has(household.houseName)) {\n        uniqueHouseNames.add(household.houseName);\n        uniqueHouseholds.push(household);\n      }\n    }\n    console.log('Unique households for display:', uniqueHouseholds.map(h => h.houseName));\n    return uniqueHouseholds;\n  }\n};\n__decorate([ViewChild('householdDialog', {\n  static: false\n})], HouseholdBindingComponent.prototype, \"householdDialog\", void 0);\n__decorate([Input()], HouseholdBindingComponent.prototype, \"placeholder\", void 0);\n__decorate([Input()], HouseholdBindingComponent.prototype, \"maxSelections\", void 0);\n__decorate([Input()], HouseholdBindingComponent.prototype, \"disabled\", void 0);\n__decorate([Input()], HouseholdBindingComponent.prototype, \"buildCaseId\", void 0);\n__decorate([Input()], HouseholdBindingComponent.prototype, \"buildingData\", void 0);\n__decorate([Input()], HouseholdBindingComponent.prototype, \"showSelectedArea\", void 0);\n__decorate([Input()], HouseholdBindingComponent.prototype, \"allowSearch\", void 0);\n__decorate([Input()], HouseholdBindingComponent.prototype, \"allowBatchSelect\", void 0);\n__decorate([Input()], HouseholdBindingComponent.prototype, \"excludedHouseIds\", void 0);\n__decorate([Input()], HouseholdBindingComponent.prototype, \"useHouseNameMode\", void 0);\n__decorate([Output()], HouseholdBindingComponent.prototype, \"selectionChange\", void 0);\n__decorate([Output()], HouseholdBindingComponent.prototype, \"houseIdChange\", void 0);\n__decorate([Output()], HouseholdBindingComponent.prototype, \"houseNameChange\", void 0);\nHouseholdBindingComponent = __decorate([Component({\n  selector: 'app-household-binding',\n  templateUrl: './household-binding.component.html',\n  styleUrls: ['./household-binding.component.scss'],\n  providers: [{\n    provide: NG_VALUE_ACCESSOR,\n    useExisting: forwardRef(() => HouseholdBindingComponent),\n    multi: true\n  }]\n})], HouseholdBindingComponent);\nexport { HouseholdBindingComponent };", "map": {"version": 3, "names": ["Component", "Input", "Output", "EventEmitter", "forwardRef", "ViewChild", "NG_VALUE_ACCESSOR", "HouseholdBindingComponent", "constructor", "cdr", "houseCustomService", "dialogService", "placeholder", "maxSelections", "disabled", "buildCaseId", "buildingData", "showSelectedArea", "allowSearch", "allowBatchSelect", "excludedHouseIds", "useHouseNameMode", "selectionChange", "houseIdChange", "houseNameChange", "isOpen", "selectedBuilding", "searchTerm", "selectedF<PERSON>or", "selectedHouseIds", "selectedHouseNames", "buildings", "floors", "filteredHouseholds", "selectedByBuilding", "isLoading", "onChange", "value", "onTouched", "writeValue", "console", "log", "length", "firstItem", "Set", "convertHouseNamesToIds", "error", "convertIdsToHouseNames", "updateSelectedByBuilding", "registerOnChange", "fn", "registerOnTouched", "setDisabledState", "isDisabled", "ngOnInit", "initializeData", "ngOnChanges", "changes", "Object", "keys", "updateFilteredHouseholds", "grouped", "for<PERSON>ach", "houseId", "building", "item", "find", "h", "push", "onBuildingSelect", "updateFloorsForBuilding", "detectChanges", "onBuildingClick", "households", "filteredItems", "filter", "floorMatch", "floor", "searchMatch", "houseName", "toLowerCase", "includes", "map", "onSearchChange", "event", "target", "resetSearch", "onHouseholdToggle", "isHouseIdExcluded", "clickedHousehold", "getHouseholdByHouseId", "newSelection", "allMatchingHouseIds", "getAllHouseIdsByHouseName", "hasAnySelected", "some", "id", "isSelected", "isHouseIdSelected", "emitChanges", "onRemoveHousehold", "onSelectAllFiltered", "filteredHouseholdItems", "getUniqueHouseholdsForDisplay", "currentCount", "maxAllowed", "Infinity", "remainingSlots", "unselectedFilteredIds", "household", "hasAnyExcluded", "toAdd", "slice", "onSelectAllBuilding", "buildingHouseholds", "unselectedBuildingIds", "processedHouseNames", "has", "add", "isHouseholdExcluded", "onUnselectAllBuilding", "buildingHouseIds", "undefined", "onClearAll", "uniqueHouseNames", "emit", "houseIds", "selectedItems", "toggleDropdown", "openDialog", "open", "householdDialog", "context", "closeOnBackdropClick", "closeOnEsc", "autoFocus", "closeDropdown", "isHouseholdSelected", "isHouseholdDisabled", "canSelectMore", "isAllBuildingSelected", "every", "isSomeBuildingSelected", "getSelectedByBuilding", "getBuildingCount", "size", "getSelectedCount", "getBuildingSelectedHouseIds", "hasBuildingSelected", "floorSet", "Array", "from", "sort", "a", "b", "numA", "parseInt", "replace", "numB", "onFloorSelect", "getFloorCount", "getHouseholdFloor", "householdCode", "getHouseholdInfo", "getHouseholdInfoById", "hasNoSearchResults", "filtered", "getFilteredHouseholdsCount", "getHouseholdUniqueId", "toString", "getHouseIdByHouseName", "matchingHouseholds", "matches", "warn", "m", "firstMatch", "getHouseholdFromUniqueId", "uniqueId", "houseNames", "matchingHouseIds", "uniqueHouseIds", "householdInfo", "startsWith", "uniqueHouseholds", "__decorate", "static", "selector", "templateUrl", "styleUrls", "providers", "provide", "useExisting", "multi"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\shared\\components\\household-binding\\household-binding.component.ts"], "sourcesContent": ["import { Component, Input, Output, EventEmitter, OnInit, OnChanges, SimpleChanges, forwardRef, ChangeDetectorRef, TemplateRef, ViewChild } from '@angular/core';\r\nimport { ControlValueAccessor, NG_VALUE_ACCESSOR } from '@angular/forms';\r\nimport { NbDialogService } from '@nebular/theme';\r\nimport { HouseItem } from '../../../../services/api/models/house.model';\r\n\r\nexport interface HouseholdItem {\r\n  houseName: string;\r\n  building: string;\r\n  floor?: string;\r\n  houseId?: number;\r\n  isSelected?: boolean;\r\n  isDisabled?: boolean;\r\n}\r\n\r\nexport interface BuildingData {\r\n  [key: string]: HouseholdItem[];\r\n}\r\n\r\n@Component({\r\n  selector: 'app-household-binding',\r\n  templateUrl: './household-binding.component.html',\r\n  styleUrls: ['./household-binding.component.scss'],\r\n  providers: [\r\n    {\r\n      provide: NG_VALUE_ACCESSOR,\r\n      useExisting: forwardRef(() => HouseholdBindingComponent),\r\n      multi: true,\r\n    },\r\n  ],\r\n})\r\nexport class HouseholdBindingComponent implements OnInit, OnChanges, ControlValueAccessor {\r\n  @ViewChild('householdDialog', { static: false }) householdDialog!: TemplateRef<any>;\r\n  @Input() placeholder: string = '請選擇戶別';\r\n  @Input() maxSelections: number | null = null;\r\n  @Input() disabled: boolean = false;\r\n  @Input() buildCaseId: number | null = null; // 新增：建案ID\r\n  @Input() buildingData: BuildingData = {}; @Input() showSelectedArea: boolean = true;\r\n  @Input() allowSearch: boolean = true;\r\n  @Input() allowBatchSelect: boolean = true;\r\n  @Input() excludedHouseIds: number[] = []; // 改為：排除的戶別ID（已被其他元件選擇）\r\n  @Input() useHouseNameMode: boolean = false; // 新增：使用戶別名稱模式\r\n\r\n  @Output() selectionChange = new EventEmitter<HouseholdItem[]>();\r\n  @Output() houseIdChange = new EventEmitter<number[]>(); // 新增：回傳 houseId 陣列\r\n  @Output() houseNameChange = new EventEmitter<string[]>(); // 新增：useHouseNameMode 時回傳戶別名稱陣列\r\n  isOpen = false;\r\n  selectedBuilding = '';\r\n  searchTerm = ''; selectedFloor = ''; // 新增：選中的樓層\r\n  selectedHouseIds: number[] = []; // 改為：使用 houseId 作為選擇的key\r\n  selectedHouseNames: string[] = []; // 新增：useHouseNameMode 時使用的戶別名稱陣列\r\n  buildings: string[] = [];\r\n  floors: string[] = []; // 新增：當前棧別的樓層列表\r\n  filteredHouseholds: string[] = [];  // 保持為字串陣列用於UI顯示\r\n  selectedByBuilding: { [building: string]: number[] } = {}; // 改為：儲存 houseId\r\n  isLoading: boolean = false; // 新增：載入狀態  // ControlValueAccessor implementation\r\n  private onChange = (value: number[] | string[]) => { };\r\n  private onTouched = () => { };\r\n  constructor(\r\n    private cdr: ChangeDetectorRef,\r\n    private houseCustomService: HouseCustomService,\r\n    private dialogService: NbDialogService\r\n  ) { } writeValue(value: any[]): void {\r\n    console.log('writeValue called with:', value, 'type:', typeof value?.[0], 'useHouseNameMode:', this.useHouseNameMode);\r\n\r\n    if (!value || value.length === 0) {\r\n      this.selectedHouseIds = [];\r\n      this.selectedHouseNames = [];\r\n    } else {\r\n      const firstItem = value[0]; if (this.useHouseNameMode) {\r\n        // useHouseNameMode: 期望接收戶別名稱陣列\r\n        if (typeof firstItem === 'string') {\r\n          this.selectedHouseNames = [...new Set(value as string[])]; // 去除重複的戶別名稱\r\n          // 將戶別名稱轉換為 houseId（用於內部邏輯），會自動處理重複名稱\r\n          this.selectedHouseIds = this.convertHouseNamesToIds(this.selectedHouseNames);\r\n          console.log('useHouseNameMode: 使用傳入的戶別名稱陣列（已去重）');\r\n        } else {\r\n          console.error('useHouseNameMode 期望接收 string[] 但收到:', typeof firstItem);\r\n          this.selectedHouseNames = [];\r\n          this.selectedHouseIds = [];\r\n        }\r\n      } else {\r\n        // 一般模式: 期望接收 houseId 陣列\r\n        if (typeof firstItem === 'number') {\r\n          this.selectedHouseIds = value as number[];\r\n          // 將 houseId 轉換為戶別名稱（用於顯示）\r\n          this.selectedHouseNames = this.convertIdsToHouseNames(this.selectedHouseIds);\r\n          console.log('一般模式: 使用傳入的 houseId 陣列');\r\n        } else if (typeof firstItem === 'string') {\r\n          // 向下相容：如果收到字串但不在 useHouseNameMode，發出警告\r\n          console.error('⚠️ 警告：一般模式下收到戶別名稱陣列而不是 houseId 陣列！');\r\n          console.error('⚠️ 建議父元件改用 houseId 陣列或啟用 useHouseNameMode');\r\n          return;\r\n        } else {\r\n          console.error('writeValue 收到未知格式的資料:', value);\r\n          this.selectedHouseIds = [];\r\n          this.selectedHouseNames = [];\r\n        }\r\n      }\r\n    }\r\n\r\n    console.log('selectedHouseIds set to:', this.selectedHouseIds);\r\n    console.log('selectedHouseNames set to:', this.selectedHouseNames);\r\n    this.updateSelectedByBuilding();\r\n  }\r\n  registerOnChange(fn: (value: number[] | string[]) => void): void {\r\n    this.onChange = fn;\r\n  }\r\n\r\n  registerOnTouched(fn: () => void): void {\r\n    this.onTouched = fn;\r\n  }\r\n  setDisabledState(isDisabled: boolean): void {\r\n    this.disabled = isDisabled;\r\n  } ngOnInit() {\r\n    this.initializeData();\r\n  }\r\n  ngOnChanges(changes: SimpleChanges) {\r\n    if (changes['buildCaseId'] && this.buildCaseId) {\r\n      // 當建案ID變更時，重新載入資料\r\n      this.initializeData();\r\n    }\r\n    if (changes['buildingData']) {\r\n      // 當 buildingData 變更時，重新初始化\r\n      this.buildings = Object.keys(this.buildingData || {});\r\n      console.log('buildingData updated:', this.buildingData);\r\n      this.updateFilteredHouseholds();\r\n      this.updateSelectedByBuilding();\r\n    } if (changes['excludedHouseIds']) {\r\n      // 當排除列表變化時，重新更新顯示\r\n      this.updateFilteredHouseholds();\r\n      console.log('Excluded house IDs updated:', this.excludedHouseIds);\r\n    }\r\n    if (changes['useHouseNameMode']) {\r\n      // 當 useHouseNameMode 變更時，重新同步選擇狀態\r\n      console.log('useHouseNameMode changed to:', this.useHouseNameMode);\r\n      // 在 useHouseNameMode 切換時，確保樓層選擇被重置\r\n      if (this.useHouseNameMode) {\r\n        this.selectedFloor = '';\r\n      }\r\n    }\r\n  }  private initializeData() {\r\n    // 使用傳入的 buildingData\r\n    if (this.buildingData && Object.keys(this.buildingData).length > 0) {\r\n      this.buildings = Object.keys(this.buildingData);\r\n      console.log('Component initialized with provided buildingData:', this.buildings);\r\n      this.updateSelectedByBuilding();\r\n    } else {\r\n      // 沒有 buildingData，保持空狀態\r\n      this.buildings = [];\r\n      console.log('No buildingData provided');\r\n    }\r\n  }  private updateSelectedByBuilding() {\r\n    const grouped: { [building: string]: number[] } = {};\r\n\r\n    this.selectedHouseIds.forEach(houseId => {\r\n      for (const building of this.buildings) {\r\n        const item = this.buildingData[building]?.find(h => h.houseId === houseId);\r\n        if (item) {\r\n          if (!grouped[building]) grouped[building] = [];\r\n          grouped[building].push(houseId);\r\n          break;\r\n        }\r\n      }\r\n    });\r\n\r\n    this.selectedByBuilding = grouped;\r\n  } onBuildingSelect(building: string) {\r\n    console.log('Building selected:', building);\r\n    this.selectedBuilding = building;\r\n    this.selectedFloor = ''; // 重置樓層選擇\r\n    this.searchTerm = '';\r\n    this.updateFloorsForBuilding(); // 更新樓層列表\r\n    this.updateFilteredHouseholds();\r\n    console.log('Filtered households count:', this.filteredHouseholds.length);\r\n    // 手動觸發變更偵測\r\n    this.cdr.detectChanges();\r\n  }\r\n\r\n  onBuildingClick(building: string) {\r\n    console.log('Building clicked (mousedown):', building);\r\n  } updateFilteredHouseholds() {\r\n    console.log('Updating filtered households for building:', this.selectedBuilding, 'floor:', this.selectedFloor, 'useHouseNameMode:', this.useHouseNameMode);\r\n    if (!this.selectedBuilding) {\r\n      this.filteredHouseholds = [];\r\n      return;\r\n    }\r\n\r\n    const households = this.buildingData[this.selectedBuilding] || [];\r\n    console.log('Available households for building:', households.length);\r\n\r\n    // 提取戶別代碼並進行搜尋和樓層過濾\r\n    const filteredItems = households.filter(h => {\r\n      // 樓層篩選：在 useHouseNameMode 時跳過樓層篩選，否則按原邏輯篩選\r\n      const floorMatch = this.useHouseNameMode || !this.selectedFloor || h.floor === this.selectedFloor;\r\n      // 搜尋篩選：戶別代碼包含搜尋詞\r\n      const searchMatch = !this.searchTerm || h.houseName.toLowerCase().includes(this.searchTerm.toLowerCase());\r\n      return floorMatch && searchMatch;\r\n    });\r\n\r\n    // 只提取戶別名稱用於UI顯示（但onSelectAllFiltered會直接使用物件）\r\n    this.filteredHouseholds = filteredItems.map(h => h.houseName);\r\n\r\n    console.log('Filtered households result:', this.filteredHouseholds.length);\r\n    console.log('Filtered items details:', filteredItems.map(h => `${h.houseName}-${h.floor} (ID:${h.houseId})`));\r\n  }\r\n\r\n  onSearchChange(event: any) {\r\n    this.searchTerm = event.target.value;\r\n    this.updateFilteredHouseholds();\r\n    console.log('Search term changed:', this.searchTerm);\r\n  }\r\n  resetSearch() {\r\n    this.searchTerm = '';\r\n    this.updateFilteredHouseholds();\r\n    console.log('Search reset');\r\n  } onHouseholdToggle(houseId: number | undefined) {\r\n    console.log('onHouseholdToggle called with houseId:', houseId);\r\n    console.log('Current selectedHouseIds:', this.selectedHouseIds);\r\n\r\n    if (!houseId) {\r\n      console.log(`無效的 houseId: ${houseId}`);\r\n      return;\r\n    }\r\n\r\n    // 防止選擇已排除的戶別\r\n    if (this.isHouseIdExcluded(houseId)) {\r\n      console.log(`戶別 ID ${houseId} 已被其他元件選擇，無法重複選擇`);\r\n      return;\r\n    }\r\n\r\n    // 取得被點擊戶別的名稱\r\n    const clickedHousehold = this.getHouseholdByHouseId(houseId);\r\n    if (!clickedHousehold) {\r\n      console.log(`找不到 houseId ${houseId} 對應的戶別資訊`);\r\n      return;\r\n    }\r\n\r\n    let newSelection: number[];\r\n\r\n    if (this.useHouseNameMode) {\r\n      // useHouseNameMode: 處理同名戶別的邏輯\r\n      const houseName = clickedHousehold.houseName;\r\n      const allMatchingHouseIds = this.getAllHouseIdsByHouseName(houseName);\r\n\r\n      // 檢查是否有任何同名戶別已被選中\r\n      const hasAnySelected = allMatchingHouseIds.some(id => this.selectedHouseIds.includes(id));\r\n\r\n      if (hasAnySelected) {\r\n        // 如果有同名戶別被選中，移除所有同名戶別\r\n        newSelection = this.selectedHouseIds.filter(id => !allMatchingHouseIds.includes(id));\r\n        console.log(`useHouseNameMode: 移除所有同名戶別 \"${houseName}\":`, allMatchingHouseIds);\r\n      } else {\r\n        // 如果沒有同名戶別被選中，只添加第一個（通常是當前點擊的）\r\n        if (this.maxSelections && this.selectedHouseIds.length >= this.maxSelections) {\r\n          console.log('已達到最大選擇數量');\r\n          return;\r\n        }\r\n        newSelection = [...this.selectedHouseIds, allMatchingHouseIds[0]];\r\n        console.log(`useHouseNameMode: 添加戶別 \"${houseName}\" 的第一個項目:`, allMatchingHouseIds[0]);\r\n      }\r\n    } else {\r\n      // 一般模式: 原有邏輯\r\n      const isSelected = this.isHouseIdSelected(houseId);\r\n\r\n      if (isSelected) {\r\n        newSelection = this.selectedHouseIds.filter(id => id !== houseId);\r\n      } else {\r\n        if (this.maxSelections && this.selectedHouseIds.length >= this.maxSelections) {\r\n          console.log('已達到最大選擇數量');\r\n          return;\r\n        }\r\n        newSelection = [...this.selectedHouseIds, houseId];\r\n      }\r\n    }\r\n\r\n    this.selectedHouseIds = newSelection;\r\n    this.emitChanges();\r\n  }\r\n  onRemoveHousehold(houseId: number) {\r\n    this.selectedHouseIds = this.selectedHouseIds.filter(id => id !== houseId);\r\n    this.emitChanges();\r\n  } onSelectAllFiltered() {\r\n    console.log('onSelectAllFiltered called');\r\n    console.log('selectedBuilding:', this.selectedBuilding);\r\n    console.log('selectedFloor:', this.selectedFloor);\r\n    console.log('searchTerm:', this.searchTerm);\r\n\r\n    if (!this.selectedBuilding) {\r\n      console.log('No building selected');\r\n      return;\r\n    }\r\n\r\n    // 使用 getUniqueHouseholdsForDisplay 方法來獲取要處理的戶別列表\r\n    const filteredHouseholdItems = this.getUniqueHouseholdsForDisplay();\r\n\r\n    console.log('Filtered household items:', filteredHouseholdItems.map(h => `${h.houseName}-${h.floor} (ID:${h.houseId})`));\r\n\r\n    if (filteredHouseholdItems.length === 0) {\r\n      console.log('No filtered households found');\r\n      return;\r\n    }\r\n\r\n    // 計算可以新增的戶別數量\r\n    const currentCount = this.selectedHouseIds.length;\r\n    const maxAllowed = this.maxSelections || Infinity;\r\n    const remainingSlots = maxAllowed - currentCount;\r\n\r\n    // 取得尚未選擇且未被排除的過濾戶別ID\r\n    const unselectedFilteredIds: number[] = [];\r\n    for (const household of filteredHouseholdItems) {\r\n      if (household.houseId) {\r\n        if (this.useHouseNameMode) {\r\n          // useHouseNameMode: 檢查是否有任何同名戶別已被選擇\r\n          const allMatchingHouseIds = this.getAllHouseIdsByHouseName(household.houseName);\r\n          const hasAnySelected = allMatchingHouseIds.some(id => this.selectedHouseIds.includes(id));\r\n          const hasAnyExcluded = allMatchingHouseIds.some(id => this.isHouseIdExcluded(id));\r\n\r\n          if (!hasAnySelected && !hasAnyExcluded) {\r\n            unselectedFilteredIds.push(allMatchingHouseIds[0]); // 只選擇第一個\r\n          }\r\n        } else {\r\n          // 一般模式: 原有邏輯\r\n          if (!this.isHouseIdSelected(household.houseId) && !this.isHouseIdExcluded(household.houseId)) {\r\n            unselectedFilteredIds.push(household.houseId);\r\n          }\r\n        }\r\n      }\r\n    }\r\n\r\n    console.log('Unselected filtered IDs:', unselectedFilteredIds);\r\n\r\n    // 根據剩餘空間決定要新增的戶別\r\n    const toAdd = unselectedFilteredIds.slice(0, remainingSlots);\r\n\r\n    if (toAdd.length > 0) {\r\n      this.selectedHouseIds = [...this.selectedHouseIds, ...toAdd];\r\n      this.emitChanges();\r\n      console.log(`全選當前: 新增了 ${toAdd.length} 個戶別，IDs:`, toAdd);\r\n    } else {\r\n      console.log('No households to add');\r\n    }\r\n  } onSelectAllBuilding() {\r\n    if (!this.selectedBuilding) return;\r\n\r\n    // 取得當前棟別的所有戶別\r\n    const buildingHouseholds = this.buildingData[this.selectedBuilding] || [];\r\n\r\n    // 計算可以新增的戶別數量\r\n    const currentCount = this.selectedHouseIds.length;\r\n    const maxAllowed = this.maxSelections || Infinity;\r\n    const remainingSlots = maxAllowed - currentCount;\r\n\r\n    // 取得尚未選擇且未被排除的棟別戶別 ID\r\n    const unselectedBuildingIds: number[] = [];\r\n\r\n    if (this.useHouseNameMode) {\r\n      // useHouseNameMode: 只選擇唯一的戶別名稱\r\n      const processedHouseNames = new Set<string>();\r\n\r\n      for (const household of buildingHouseholds) {\r\n        if (household.houseId && household.houseName && !processedHouseNames.has(household.houseName)) {\r\n          processedHouseNames.add(household.houseName);\r\n\r\n          const allMatchingHouseIds = this.getAllHouseIdsByHouseName(household.houseName);\r\n          const hasAnySelected = allMatchingHouseIds.some(id => this.selectedHouseIds.includes(id));\r\n          const hasAnyExcluded = allMatchingHouseIds.some(id => this.isHouseholdExcluded(id));\r\n\r\n          if (!hasAnySelected && !hasAnyExcluded) {\r\n            unselectedBuildingIds.push(allMatchingHouseIds[0]); // 只選擇第一個\r\n          }\r\n        }\r\n      }\r\n    } else {\r\n      // 一般模式: 原有邏輯\r\n      for (const household of buildingHouseholds) {\r\n        if (household.houseId &&\r\n          !this.selectedHouseIds.includes(household.houseId) &&\r\n          !this.isHouseholdExcluded(household.houseId)) {\r\n          unselectedBuildingIds.push(household.houseId);\r\n        }\r\n      }\r\n    }\r\n\r\n    // 根據剩餘空間決定要新增的戶別\r\n    const toAdd = unselectedBuildingIds.slice(0, remainingSlots);\r\n\r\n    if (toAdd.length > 0) {\r\n      this.selectedHouseIds = [...this.selectedHouseIds, ...toAdd];\r\n      this.emitChanges();\r\n      console.log(`全選棟別: 新增了 ${toAdd.length} 個戶別`);\r\n    }\r\n  }\r\n\r\n  onUnselectAllBuilding() {\r\n    if (!this.selectedBuilding) return;\r\n\r\n    const buildingHouseholds = this.buildingData[this.selectedBuilding] || [];\r\n    const buildingHouseIds = buildingHouseholds.map(h => h.houseId).filter(id => id !== undefined) as number[];\r\n    this.selectedHouseIds = this.selectedHouseIds.filter(id => !buildingHouseIds.includes(id));\r\n    this.emitChanges();\r\n  }\r\n\r\n  onClearAll() {\r\n    this.selectedHouseIds = [];\r\n    this.emitChanges();\r\n  } private emitChanges() {\r\n    this.updateSelectedByBuilding();\r\n    console.log('Emitting changes - selectedHouseIds:', this.selectedHouseIds);    // 根據模式決定要回傳的資料格式\r\n    if (this.useHouseNameMode) {\r\n      // useHouseNameMode: 回傳戶別名稱陣列（去重複）\r\n      this.selectedHouseNames = this.convertIdsToHouseNames(this.selectedHouseIds);\r\n      const uniqueHouseNames = [...new Set(this.selectedHouseNames)]; // 確保去重複\r\n      console.log('useHouseNameMode - 回傳戶別名稱陣列（已去重）:', uniqueHouseNames);\r\n      this.onChange([...uniqueHouseNames]);\r\n      this.houseNameChange.emit([...uniqueHouseNames]);\r\n    } else {\r\n      // 一般模式: 回傳 houseId 陣列\r\n      console.log('一般模式 - 回傳 houseId 陣列:', this.selectedHouseIds);\r\n      this.onChange([...this.selectedHouseIds]);\r\n\r\n      // 回傳 houseId 陣列\r\n      const houseIds = this.selectedHouseIds.filter(id => id !== undefined);\r\n      console.log('House IDs to emit:', houseIds);\r\n      this.houseIdChange.emit(houseIds);\r\n    }\r\n\r\n    this.onTouched();\r\n\r\n    // 無論哪種模式都回傳完整的 HouseholdItem 陣列（向下相容）\r\n    const selectedItems = this.selectedHouseIds.map(houseId => {\r\n      for (const building of this.buildings) {\r\n        const item = this.buildingData[building]?.find(h => h.houseId === houseId);\r\n        if (item) return item;\r\n      }\r\n      return null;\r\n    }).filter(item => item !== null) as HouseholdItem[];\r\n\r\n    console.log('Selected items to emit:', selectedItems);\r\n    this.selectionChange.emit(selectedItems);\r\n  } toggleDropdown() {\r\n    if (!this.disabled) {\r\n      this.openDialog();\r\n      console.log('Opening household selection dialog');\r\n      console.log('Buildings available:', this.buildings);\r\n    }\r\n  }\r\n\r\n  openDialog() {\r\n    this.dialogService.open(this.householdDialog, {\r\n      context: {},\r\n      closeOnBackdropClick: false,\r\n      closeOnEsc: true,\r\n      autoFocus: false,\r\n    });\r\n  }\r\n\r\n  closeDropdown() {\r\n    // 這個方法現在用於關閉對話框\r\n    // 對話框的關閉將由 NbDialogRef 處理\r\n  } isHouseholdSelected(houseId: number | undefined): boolean {\r\n    if (!houseId) return false;\r\n\r\n    if (this.useHouseNameMode) {\r\n      // useHouseNameMode: 檢查是否有任何同名戶別被選中\r\n      const household = this.getHouseholdByHouseId(houseId);\r\n      if (household) {\r\n        const allMatchingHouseIds = this.getAllHouseIdsByHouseName(household.houseName);\r\n        return allMatchingHouseIds.some(id => this.selectedHouseIds.includes(id));\r\n      }\r\n      return false;\r\n    } else {\r\n      // 一般模式: 直接檢查 houseId\r\n      return this.selectedHouseIds.includes(houseId);\r\n    }\r\n  }\r\n\r\n  isHouseholdExcluded(houseId: number | undefined): boolean {\r\n    if (!houseId) return false;\r\n    return this.excludedHouseIds.includes(houseId);\r\n  }\r\n\r\n  isHouseholdDisabled(houseId: number | undefined): boolean {\r\n    if (!houseId) return true;\r\n    return this.isHouseholdExcluded(houseId) ||\r\n      (!this.canSelectMore() && !this.isHouseholdSelected(houseId));\r\n  }\r\n  canSelectMore(): boolean {\r\n    return !this.maxSelections || this.selectedHouseIds.length < this.maxSelections;\r\n  } isAllBuildingSelected(): boolean {\r\n    if (!this.selectedBuilding) return false;\r\n    const buildingHouseholds = this.buildingData[this.selectedBuilding]\r\n      .filter(h => !h.isDisabled && h.houseId !== undefined);\r\n    return buildingHouseholds.length > 0 &&\r\n      buildingHouseholds.every(household => household.houseId && this.selectedHouseIds.includes(household.houseId));\r\n  }\r\n\r\n  isSomeBuildingSelected(): boolean {\r\n    if (!this.selectedBuilding) return false;\r\n    const buildingHouseholds = this.buildingData[this.selectedBuilding] || [];\r\n    return buildingHouseholds.some(household => household.houseId && this.selectedHouseIds.includes(household.houseId));\r\n  } getSelectedByBuilding(): { [building: string]: number[] } {\r\n    return this.selectedByBuilding;\r\n  }\r\n  getBuildingCount(building: string): number {\r\n    if (this.useHouseNameMode) {\r\n      // useHouseNameMode: 返回唯一戶別名稱的數量\r\n      const households = this.buildingData[building] || [];\r\n      const uniqueHouseNames = new Set(households.map(h => h.houseName));\r\n      return uniqueHouseNames.size;\r\n    } else {\r\n      // 一般模式: 返回總戶別數量\r\n      return this.buildingData[building]?.length || 0;\r\n    }\r\n  }\r\n  getSelectedCount(): number {\r\n    if (this.useHouseNameMode) {\r\n      // useHouseNameMode: 返回唯一戶別名稱的數量\r\n      const uniqueHouseNames = this.convertIdsToHouseNames(this.selectedHouseIds);\r\n      return uniqueHouseNames.length;\r\n    } else {\r\n      // 一般模式: 返回實際選中的戶別數量\r\n      return this.selectedHouseIds.length;\r\n    }\r\n  }\r\n\r\n  // 輔助方法：安全地獲取建築物的已選戶別 ID\r\n  getBuildingSelectedHouseIds(building: string): number[] {\r\n    return this.selectedByBuilding[building] || [];\r\n  }\r\n\r\n  // 輔助方法：檢查建築物是否有已選戶別\r\n  hasBuildingSelected(building: string): boolean {\r\n    return !!(this.selectedByBuilding[building] && this.selectedByBuilding[building].length > 0);\r\n  }\r\n\r\n  // 新增：更新當前棧別的樓層計數\r\n  private updateFloorsForBuilding() {\r\n    if (!this.selectedBuilding) {\r\n      this.floors = [];\r\n      return;\r\n    }\r\n\r\n    const households = this.buildingData[this.selectedBuilding] || [];\r\n    const floorSet = new Set<string>();\r\n\r\n    households.forEach(household => {\r\n      if (household.floor) {\r\n        floorSet.add(household.floor);\r\n      }\r\n    });\r\n\r\n    // 對樓層進行自然排序\r\n    this.floors = Array.from(floorSet).sort((a, b) => {\r\n      const numA = parseInt(a.replace(/[^0-9]/g, ''));\r\n      const numB = parseInt(b.replace(/[^0-9]/g, ''));\r\n      return numA - numB;\r\n    });\r\n\r\n    console.log('Updated floors for building:', this.selectedBuilding, this.floors);\r\n  }\r\n\r\n  // 新增：樓層選擇處理\r\n  onFloorSelect(floor: string) {\r\n    console.log('Floor selected:', floor);\r\n    this.selectedFloor = this.selectedFloor === floor ? '' : floor; // 切換選擇\r\n    this.updateFilteredHouseholds();\r\n    this.cdr.detectChanges();\r\n  }\r\n\r\n  // 新增：取得當前棧別的樓層計數\r\n  getFloorCount(floor: string): number {\r\n    if (!this.selectedBuilding) return 0;\r\n    const households = this.buildingData[this.selectedBuilding] || [];\r\n    return households.filter(h => h.floor === floor).length;\r\n  }\r\n  // 新增：取得戶別的樓層資訊\r\n  getHouseholdFloor(householdCode: string): string {\r\n    if (!this.selectedBuilding) return '';\r\n    const households = this.buildingData[this.selectedBuilding] || [];\r\n    const household = households.find(h => h.houseName === householdCode);\r\n    return household?.floor || '';\r\n  }\r\n  // 新增：取得戶別的完整資訊（包含樓層）\r\n  getHouseholdInfo(householdCode: string): { houseName: string, floor: string } {\r\n    for (const building of this.buildings) {\r\n      const households = this.buildingData[building] || [];\r\n      const household = households.find(h => h.houseName === householdCode);\r\n      if (household) {\r\n        return {\r\n          houseName: household.houseName,\r\n          floor: household.floor || ''\r\n        };\r\n      }\r\n    }\r\n    return { houseName: householdCode, floor: '' };\r\n  }\r\n\r\n  // 新增：根據 houseId 取得戶別的完整資訊\r\n  getHouseholdInfoById(houseId: number): { houseName: string, floor: string } {\r\n    for (const building of this.buildings) {\r\n      const households = this.buildingData[building] || [];\r\n      const household = households.find(h => h.houseId === houseId);\r\n      if (household) {\r\n        return {\r\n          houseName: household.houseName,\r\n          floor: household.floor || ''\r\n        };\r\n      }\r\n    }\r\n    return { houseName: `ID:${houseId}`, floor: '' };\r\n  }\r\n\r\n  // 新增：檢查搜尋是否有結果\r\n  hasNoSearchResults(): boolean {\r\n    if (!this.searchTerm || !this.selectedBuilding || !this.buildingData[this.selectedBuilding]) {\r\n      return false;\r\n    } const filtered = this.buildingData[this.selectedBuilding].filter(h => {\r\n      const floorMatch = this.useHouseNameMode || !this.selectedFloor || h.floor === this.selectedFloor;\r\n      const searchMatch = h.houseName.toLowerCase().includes(this.searchTerm.toLowerCase());\r\n      return floorMatch && searchMatch;\r\n    });\r\n\r\n    return filtered.length === 0;\r\n  }\r\n  // 新增：取得過濾後的戶別數量\r\n  getFilteredHouseholdsCount(): number {\r\n    return this.getUniqueHouseholdsForDisplay().length;\r\n  }\r\n\r\n  // 新增：產生戶別的唯一識別符\r\n  getHouseholdUniqueId(household: HouseholdItem): string {\r\n    return household.houseId ? household.houseId.toString() : `${household.houseName}_${household.floor}`;\r\n  }\r\n  // 新增：輔助方法 - 根據 houseId 查找 HouseholdItem\r\n  private getHouseholdByHouseId(houseId: number): HouseholdItem | null {\r\n    for (const building of this.buildings) {\r\n      const households = this.buildingData[building] || [];\r\n      const household = households.find(h => h.houseId === houseId);\r\n      if (household) return household;\r\n    }\r\n    return null;\r\n  }  // 新增：輔助方法 - 根據 houseName 查找 houseId\r\n  private getHouseIdByHouseName(houseName: string): number | null {\r\n    const matchingHouseholds: { building: string, household: HouseholdItem }[] = [];\r\n\r\n    // 收集所有符合名稱的戶別\r\n    for (const building of this.buildings) {\r\n      const households = this.buildingData[building] || [];\r\n      const matches = households.filter(h => h.houseName === houseName);\r\n      matches.forEach(household => {\r\n        matchingHouseholds.push({ building, household });\r\n      });\r\n    }\r\n\r\n    console.log(`查找 houseName \"${houseName}\" 的結果:`, matchingHouseholds);\r\n\r\n    if (matchingHouseholds.length === 0) {\r\n      console.warn(`找不到 houseName \"${houseName}\" 對應的戶別`);\r\n      return null;\r\n    }\r\n\r\n    if (matchingHouseholds.length > 1) {\r\n      console.warn(`發現多個同名戶別 \"${houseName}\":`, matchingHouseholds.map(m => `${m.building}-${m.household.floor}`));\r\n      console.warn(`將返回第一個找到的: ${matchingHouseholds[0].building}-${matchingHouseholds[0].household.floor}`);\r\n    }\r\n\r\n    const firstMatch = matchingHouseholds[0];\r\n    return firstMatch.household.houseId || null;\r\n  }\r\n\r\n  // 新增：輔助方法 - 根據 houseName 查找所有對應的 houseId（處理重複名稱）\r\n  private getAllHouseIdsByHouseName(houseName: string): number[] {\r\n    const houseIds: number[] = [];\r\n\r\n    // 收集所有符合名稱的戶別\r\n    for (const building of this.buildings) {\r\n      const households = this.buildingData[building] || [];\r\n      const matches = households.filter(h => h.houseName === houseName);\r\n      matches.forEach(household => {\r\n        if (household.houseId) {\r\n          houseIds.push(household.houseId);\r\n        }\r\n      });\r\n    }\r\n\r\n    return houseIds;\r\n  }\r\n\r\n  // 新增：輔助方法 - 檢查 houseId 是否被選中\r\n  private isHouseIdSelected(houseId: number): boolean {\r\n    return this.selectedHouseIds.includes(houseId);\r\n  }\r\n\r\n  // 新增：輔助方法 - 檢查 houseId 是否被排除\r\n  private isHouseIdExcluded(houseId: number): boolean {\r\n    return this.excludedHouseIds.includes(houseId);\r\n  }\r\n  // 新增：從唯一識別符獲取戶別物件\r\n  getHouseholdFromUniqueId(uniqueId: string): HouseholdItem | null {\r\n    for (const building of this.buildings) {\r\n      const households = this.buildingData[building] || [];\r\n      const household = households.find(h => this.getHouseholdUniqueId(h) === uniqueId);\r\n      if (household) return household;\r\n    }\r\n    return null;\r\n  }  // 新增：將戶別名稱陣列轉換為 houseId 陣列（在 useHouseNameMode 下只選擇第一個匹配項）\r\n  private convertHouseNamesToIds(houseNames: string[]): number[] {\r\n    const houseIds: number[] = [];\r\n    const uniqueHouseNames = [...new Set(houseNames)]; // 去除重複的戶別名稱\r\n\r\n    for (const houseName of uniqueHouseNames) {\r\n      const matchingHouseIds = this.getAllHouseIdsByHouseName(houseName);\r\n      if (matchingHouseIds.length > 0) {\r\n        if (this.useHouseNameMode) {\r\n          // useHouseNameMode: 只選擇第一個匹配的 houseId，避免多個同名戶別都被選中\r\n          houseIds.push(matchingHouseIds[0]);\r\n          if (matchingHouseIds.length > 1) {\r\n            console.log(`戶別名稱 \"${houseName}\" 有 ${matchingHouseIds.length} 個重複項目，在 useHouseNameMode 下只選擇第一個:`, matchingHouseIds[0]);\r\n          }\r\n        } else {\r\n          // 一般模式: 將所有對應的 houseId 都加入（保持原有邏輯）\r\n          houseIds.push(...matchingHouseIds);\r\n          if (matchingHouseIds.length > 1) {\r\n            console.warn(`戶別名稱 \"${houseName}\" 有 ${matchingHouseIds.length} 個重複項目，已全部加入選擇:`, matchingHouseIds);\r\n          }\r\n        }\r\n      } else {\r\n        console.warn(`無法找到戶別名稱 \"${houseName}\" 對應的 houseId`);\r\n      }\r\n    }\r\n\r\n    // 去除重複的 houseId\r\n    return [...new Set(houseIds)];\r\n  }\r\n  // 新增：將 houseId 陣列轉換為戶別名稱陣列（去重複）\r\n  private convertIdsToHouseNames(houseIds: number[]): string[] {\r\n    const houseNames: string[] = [];\r\n    const uniqueHouseIds = [...new Set(houseIds)]; // 去除重複的 houseId\r\n\r\n    for (const houseId of uniqueHouseIds) {\r\n      const householdInfo = this.getHouseholdInfoById(houseId);\r\n      if (householdInfo.houseName && !householdInfo.houseName.startsWith('ID:')) {\r\n        houseNames.push(householdInfo.houseName);\r\n      } else {\r\n        console.warn(`無法找到 houseId ${houseId} 對應的戶別名稱`);\r\n      }\r\n    }\r\n\r\n    // 去除重複的戶別名稱\r\n    return [...new Set(houseNames)];\r\n  }\r\n\r\n  // 新增：取得去重複的戶別列表（用於 useHouseNameMode 顯示）\r\n  getUniqueHouseholdsForDisplay(): HouseholdItem[] {\r\n    if (!this.selectedBuilding || !this.buildingData[this.selectedBuilding]) {\r\n      return [];\r\n    }\r\n\r\n    const households = this.buildingData[this.selectedBuilding] || [];\r\n\r\n    if (!this.useHouseNameMode) {\r\n      // 一般模式：返回所有戶別\r\n      return households.filter(h => {\r\n        const floorMatch = !this.selectedFloor || h.floor === this.selectedFloor;\r\n        const searchMatch = !this.searchTerm || h.houseName.toLowerCase().includes(this.searchTerm.toLowerCase());\r\n        return floorMatch && searchMatch;\r\n      });\r\n    }\r\n\r\n    // useHouseNameMode：只返回唯一的戶別名稱\r\n    const uniqueHouseNames = new Set<string>();\r\n    const uniqueHouseholds: HouseholdItem[] = [];\r\n\r\n    for (const household of households) {\r\n      // 搜尋篩選\r\n      const searchMatch = !this.searchTerm || household.houseName.toLowerCase().includes(this.searchTerm.toLowerCase());\r\n\r\n      if (searchMatch && !uniqueHouseNames.has(household.houseName)) {\r\n        uniqueHouseNames.add(household.houseName);\r\n        uniqueHouseholds.push(household);\r\n      }\r\n    }\r\n\r\n    console.log('Unique households for display:', uniqueHouseholds.map(h => h.houseName));\r\n    return uniqueHouseholds;\r\n  }\r\n}\r\n"], "mappings": ";AAAA,SAASA,SAAS,EAAEC,KAAK,EAAEC,MAAM,EAAEC,YAAY,EAAoCC,UAAU,EAAkCC,SAAS,QAAQ,eAAe;AAC/J,SAA+BC,iBAAiB,QAAQ,gBAAgB;AA6BjE,IAAMC,yBAAyB,GAA/B,MAAMA,yBAAyB;EA2BpCC,YACUC,GAAsB,EACtBC,kBAAsC,EACtCC,aAA8B;IAF9B,KAAAF,GAAG,GAAHA,GAAG;IACH,KAAAC,kBAAkB,GAAlBA,kBAAkB;IAClB,KAAAC,aAAa,GAAbA,aAAa;IA5Bd,KAAAC,WAAW,GAAW,OAAO;IAC7B,KAAAC,aAAa,GAAkB,IAAI;IACnC,KAAAC,QAAQ,GAAY,KAAK;IACzB,KAAAC,WAAW,GAAkB,IAAI,CAAC,CAAC;IACnC,KAAAC,YAAY,GAAiB,EAAE;IAAW,KAAAC,gBAAgB,GAAY,IAAI;IAC1E,KAAAC,WAAW,GAAY,IAAI;IAC3B,KAAAC,gBAAgB,GAAY,IAAI;IAChC,KAAAC,gBAAgB,GAAa,EAAE,CAAC,CAAC;IACjC,KAAAC,gBAAgB,GAAY,KAAK,CAAC,CAAC;IAElC,KAAAC,eAAe,GAAG,IAAInB,YAAY,EAAmB;IACrD,KAAAoB,aAAa,GAAG,IAAIpB,YAAY,EAAY,CAAC,CAAC;IAC9C,KAAAqB,eAAe,GAAG,IAAIrB,YAAY,EAAY,CAAC,CAAC;IAC1D,KAAAsB,MAAM,GAAG,KAAK;IACd,KAAAC,gBAAgB,GAAG,EAAE;IACrB,KAAAC,UAAU,GAAG,EAAE;IAAE,KAAAC,aAAa,GAAG,EAAE,CAAC,CAAC;IACrC,KAAAC,gBAAgB,GAAa,EAAE,CAAC,CAAC;IACjC,KAAAC,kBAAkB,GAAa,EAAE,CAAC,CAAC;IACnC,KAAAC,SAAS,GAAa,EAAE;IACxB,KAAAC,MAAM,GAAa,EAAE,CAAC,CAAC;IACvB,KAAAC,kBAAkB,GAAa,EAAE,CAAC,CAAE;IACpC,KAAAC,kBAAkB,GAAqC,EAAE,CAAC,CAAC;IAC3D,KAAAC,SAAS,GAAY,KAAK,CAAC,CAAC;IACpB,KAAAC,QAAQ,GAAIC,KAA0B,IAAI,CAAG,CAAC;IAC9C,KAAAC,SAAS,GAAG,MAAK,CAAG,CAAC;EAKzB;EAAEC,UAAUA,CAACF,KAAY;IAC3BG,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEJ,KAAK,EAAE,OAAO,EAAE,OAAOA,KAAK,GAAG,CAAC,CAAC,EAAE,mBAAmB,EAAE,IAAI,CAAChB,gBAAgB,CAAC;IAErH,IAAI,CAACgB,KAAK,IAAIA,KAAK,CAACK,MAAM,KAAK,CAAC,EAAE;MAChC,IAAI,CAACb,gBAAgB,GAAG,EAAE;MAC1B,IAAI,CAACC,kBAAkB,GAAG,EAAE;IAC9B,CAAC,MAAM;MACL,MAAMa,SAAS,GAAGN,KAAK,CAAC,CAAC,CAAC;MAAE,IAAI,IAAI,CAAChB,gBAAgB,EAAE;QACrD;QACA,IAAI,OAAOsB,SAAS,KAAK,QAAQ,EAAE;UACjC,IAAI,CAACb,kBAAkB,GAAG,CAAC,GAAG,IAAIc,GAAG,CAACP,KAAiB,CAAC,CAAC,CAAC,CAAC;UAC3D;UACA,IAAI,CAACR,gBAAgB,GAAG,IAAI,CAACgB,sBAAsB,CAAC,IAAI,CAACf,kBAAkB,CAAC;UAC5EU,OAAO,CAACC,GAAG,CAAC,oCAAoC,CAAC;QACnD,CAAC,MAAM;UACLD,OAAO,CAACM,KAAK,CAAC,qCAAqC,EAAE,OAAOH,SAAS,CAAC;UACtE,IAAI,CAACb,kBAAkB,GAAG,EAAE;UAC5B,IAAI,CAACD,gBAAgB,GAAG,EAAE;QAC5B;MACF,CAAC,MAAM;QACL;QACA,IAAI,OAAOc,SAAS,KAAK,QAAQ,EAAE;UACjC,IAAI,CAACd,gBAAgB,GAAGQ,KAAiB;UACzC;UACA,IAAI,CAACP,kBAAkB,GAAG,IAAI,CAACiB,sBAAsB,CAAC,IAAI,CAAClB,gBAAgB,CAAC;UAC5EW,OAAO,CAACC,GAAG,CAAC,wBAAwB,CAAC;QACvC,CAAC,MAAM,IAAI,OAAOE,SAAS,KAAK,QAAQ,EAAE;UACxC;UACAH,OAAO,CAACM,KAAK,CAAC,oCAAoC,CAAC;UACnDN,OAAO,CAACM,KAAK,CAAC,2CAA2C,CAAC;UAC1D;QACF,CAAC,MAAM;UACLN,OAAO,CAACM,KAAK,CAAC,uBAAuB,EAAET,KAAK,CAAC;UAC7C,IAAI,CAACR,gBAAgB,GAAG,EAAE;UAC1B,IAAI,CAACC,kBAAkB,GAAG,EAAE;QAC9B;MACF;IACF;IAEAU,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAE,IAAI,CAACZ,gBAAgB,CAAC;IAC9DW,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAE,IAAI,CAACX,kBAAkB,CAAC;IAClE,IAAI,CAACkB,wBAAwB,EAAE;EACjC;EACAC,gBAAgBA,CAACC,EAAwC;IACvD,IAAI,CAACd,QAAQ,GAAGc,EAAE;EACpB;EAEAC,iBAAiBA,CAACD,EAAc;IAC9B,IAAI,CAACZ,SAAS,GAAGY,EAAE;EACrB;EACAE,gBAAgBA,CAACC,UAAmB;IAClC,IAAI,CAACvC,QAAQ,GAAGuC,UAAU;EAC5B;EAAEC,QAAQA,CAAA;IACR,IAAI,CAACC,cAAc,EAAE;EACvB;EACAC,WAAWA,CAACC,OAAsB;IAChC,IAAIA,OAAO,CAAC,aAAa,CAAC,IAAI,IAAI,CAAC1C,WAAW,EAAE;MAC9C;MACA,IAAI,CAACwC,cAAc,EAAE;IACvB;IACA,IAAIE,OAAO,CAAC,cAAc,CAAC,EAAE;MAC3B;MACA,IAAI,CAAC1B,SAAS,GAAG2B,MAAM,CAACC,IAAI,CAAC,IAAI,CAAC3C,YAAY,IAAI,EAAE,CAAC;MACrDwB,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAE,IAAI,CAACzB,YAAY,CAAC;MACvD,IAAI,CAAC4C,wBAAwB,EAAE;MAC/B,IAAI,CAACZ,wBAAwB,EAAE;IACjC;IAAE,IAAIS,OAAO,CAAC,kBAAkB,CAAC,EAAE;MACjC;MACA,IAAI,CAACG,wBAAwB,EAAE;MAC/BpB,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAE,IAAI,CAACrB,gBAAgB,CAAC;IACnE;IACA,IAAIqC,OAAO,CAAC,kBAAkB,CAAC,EAAE;MAC/B;MACAjB,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAE,IAAI,CAACpB,gBAAgB,CAAC;MAClE;MACA,IAAI,IAAI,CAACA,gBAAgB,EAAE;QACzB,IAAI,CAACO,aAAa,GAAG,EAAE;MACzB;IACF;EACF;EAAW2B,cAAcA,CAAA;IACvB;IACA,IAAI,IAAI,CAACvC,YAAY,IAAI0C,MAAM,CAACC,IAAI,CAAC,IAAI,CAAC3C,YAAY,CAAC,CAAC0B,MAAM,GAAG,CAAC,EAAE;MAClE,IAAI,CAACX,SAAS,GAAG2B,MAAM,CAACC,IAAI,CAAC,IAAI,CAAC3C,YAAY,CAAC;MAC/CwB,OAAO,CAACC,GAAG,CAAC,mDAAmD,EAAE,IAAI,CAACV,SAAS,CAAC;MAChF,IAAI,CAACiB,wBAAwB,EAAE;IACjC,CAAC,MAAM;MACL;MACA,IAAI,CAACjB,SAAS,GAAG,EAAE;MACnBS,OAAO,CAACC,GAAG,CAAC,0BAA0B,CAAC;IACzC;EACF;EAAWO,wBAAwBA,CAAA;IACjC,MAAMa,OAAO,GAAqC,EAAE;IAEpD,IAAI,CAAChC,gBAAgB,CAACiC,OAAO,CAACC,OAAO,IAAG;MACtC,KAAK,MAAMC,QAAQ,IAAI,IAAI,CAACjC,SAAS,EAAE;QACrC,MAAMkC,IAAI,GAAG,IAAI,CAACjD,YAAY,CAACgD,QAAQ,CAAC,EAAEE,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACJ,OAAO,KAAKA,OAAO,CAAC;QAC1E,IAAIE,IAAI,EAAE;UACR,IAAI,CAACJ,OAAO,CAACG,QAAQ,CAAC,EAAEH,OAAO,CAACG,QAAQ,CAAC,GAAG,EAAE;UAC9CH,OAAO,CAACG,QAAQ,CAAC,CAACI,IAAI,CAACL,OAAO,CAAC;UAC/B;QACF;MACF;IACF,CAAC,CAAC;IAEF,IAAI,CAAC7B,kBAAkB,GAAG2B,OAAO;EACnC;EAAEQ,gBAAgBA,CAACL,QAAgB;IACjCxB,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEuB,QAAQ,CAAC;IAC3C,IAAI,CAACtC,gBAAgB,GAAGsC,QAAQ;IAChC,IAAI,CAACpC,aAAa,GAAG,EAAE,CAAC,CAAC;IACzB,IAAI,CAACD,UAAU,GAAG,EAAE;IACpB,IAAI,CAAC2C,uBAAuB,EAAE,CAAC,CAAC;IAChC,IAAI,CAACV,wBAAwB,EAAE;IAC/BpB,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAE,IAAI,CAACR,kBAAkB,CAACS,MAAM,CAAC;IACzE;IACA,IAAI,CAACjC,GAAG,CAAC8D,aAAa,EAAE;EAC1B;EAEAC,eAAeA,CAACR,QAAgB;IAC9BxB,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAEuB,QAAQ,CAAC;EACxD;EAAEJ,wBAAwBA,CAAA;IACxBpB,OAAO,CAACC,GAAG,CAAC,4CAA4C,EAAE,IAAI,CAACf,gBAAgB,EAAE,QAAQ,EAAE,IAAI,CAACE,aAAa,EAAE,mBAAmB,EAAE,IAAI,CAACP,gBAAgB,CAAC;IAC1J,IAAI,CAAC,IAAI,CAACK,gBAAgB,EAAE;MAC1B,IAAI,CAACO,kBAAkB,GAAG,EAAE;MAC5B;IACF;IAEA,MAAMwC,UAAU,GAAG,IAAI,CAACzD,YAAY,CAAC,IAAI,CAACU,gBAAgB,CAAC,IAAI,EAAE;IACjEc,OAAO,CAACC,GAAG,CAAC,oCAAoC,EAAEgC,UAAU,CAAC/B,MAAM,CAAC;IAEpE;IACA,MAAMgC,aAAa,GAAGD,UAAU,CAACE,MAAM,CAACR,CAAC,IAAG;MAC1C;MACA,MAAMS,UAAU,GAAG,IAAI,CAACvD,gBAAgB,IAAI,CAAC,IAAI,CAACO,aAAa,IAAIuC,CAAC,CAACU,KAAK,KAAK,IAAI,CAACjD,aAAa;MACjG;MACA,MAAMkD,WAAW,GAAG,CAAC,IAAI,CAACnD,UAAU,IAAIwC,CAAC,CAACY,SAAS,CAACC,WAAW,EAAE,CAACC,QAAQ,CAAC,IAAI,CAACtD,UAAU,CAACqD,WAAW,EAAE,CAAC;MACzG,OAAOJ,UAAU,IAAIE,WAAW;IAClC,CAAC,CAAC;IAEF;IACA,IAAI,CAAC7C,kBAAkB,GAAGyC,aAAa,CAACQ,GAAG,CAACf,CAAC,IAAIA,CAAC,CAACY,SAAS,CAAC;IAE7DvC,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAE,IAAI,CAACR,kBAAkB,CAACS,MAAM,CAAC;IAC1EF,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEiC,aAAa,CAACQ,GAAG,CAACf,CAAC,IAAI,GAAGA,CAAC,CAACY,SAAS,IAAIZ,CAAC,CAACU,KAAK,QAAQV,CAAC,CAACJ,OAAO,GAAG,CAAC,CAAC;EAC/G;EAEAoB,cAAcA,CAACC,KAAU;IACvB,IAAI,CAACzD,UAAU,GAAGyD,KAAK,CAACC,MAAM,CAAChD,KAAK;IACpC,IAAI,CAACuB,wBAAwB,EAAE;IAC/BpB,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAE,IAAI,CAACd,UAAU,CAAC;EACtD;EACA2D,WAAWA,CAAA;IACT,IAAI,CAAC3D,UAAU,GAAG,EAAE;IACpB,IAAI,CAACiC,wBAAwB,EAAE;IAC/BpB,OAAO,CAACC,GAAG,CAAC,cAAc,CAAC;EAC7B;EAAE8C,iBAAiBA,CAACxB,OAA2B;IAC7CvB,OAAO,CAACC,GAAG,CAAC,wCAAwC,EAAEsB,OAAO,CAAC;IAC9DvB,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAE,IAAI,CAACZ,gBAAgB,CAAC;IAE/D,IAAI,CAACkC,OAAO,EAAE;MACZvB,OAAO,CAACC,GAAG,CAAC,gBAAgBsB,OAAO,EAAE,CAAC;MACtC;IACF;IAEA;IACA,IAAI,IAAI,CAACyB,iBAAiB,CAACzB,OAAO,CAAC,EAAE;MACnCvB,OAAO,CAACC,GAAG,CAAC,SAASsB,OAAO,kBAAkB,CAAC;MAC/C;IACF;IAEA;IACA,MAAM0B,gBAAgB,GAAG,IAAI,CAACC,qBAAqB,CAAC3B,OAAO,CAAC;IAC5D,IAAI,CAAC0B,gBAAgB,EAAE;MACrBjD,OAAO,CAACC,GAAG,CAAC,eAAesB,OAAO,UAAU,CAAC;MAC7C;IACF;IAEA,IAAI4B,YAAsB;IAE1B,IAAI,IAAI,CAACtE,gBAAgB,EAAE;MACzB;MACA,MAAM0D,SAAS,GAAGU,gBAAgB,CAACV,SAAS;MAC5C,MAAMa,mBAAmB,GAAG,IAAI,CAACC,yBAAyB,CAACd,SAAS,CAAC;MAErE;MACA,MAAMe,cAAc,GAAGF,mBAAmB,CAACG,IAAI,CAACC,EAAE,IAAI,IAAI,CAACnE,gBAAgB,CAACoD,QAAQ,CAACe,EAAE,CAAC,CAAC;MAEzF,IAAIF,cAAc,EAAE;QAClB;QACAH,YAAY,GAAG,IAAI,CAAC9D,gBAAgB,CAAC8C,MAAM,CAACqB,EAAE,IAAI,CAACJ,mBAAmB,CAACX,QAAQ,CAACe,EAAE,CAAC,CAAC;QACpFxD,OAAO,CAACC,GAAG,CAAC,+BAA+BsC,SAAS,IAAI,EAAEa,mBAAmB,CAAC;MAChF,CAAC,MAAM;QACL;QACA,IAAI,IAAI,CAAC/E,aAAa,IAAI,IAAI,CAACgB,gBAAgB,CAACa,MAAM,IAAI,IAAI,CAAC7B,aAAa,EAAE;UAC5E2B,OAAO,CAACC,GAAG,CAAC,WAAW,CAAC;UACxB;QACF;QACAkD,YAAY,GAAG,CAAC,GAAG,IAAI,CAAC9D,gBAAgB,EAAE+D,mBAAmB,CAAC,CAAC,CAAC,CAAC;QACjEpD,OAAO,CAACC,GAAG,CAAC,2BAA2BsC,SAAS,WAAW,EAAEa,mBAAmB,CAAC,CAAC,CAAC,CAAC;MACtF;IACF,CAAC,MAAM;MACL;MACA,MAAMK,UAAU,GAAG,IAAI,CAACC,iBAAiB,CAACnC,OAAO,CAAC;MAElD,IAAIkC,UAAU,EAAE;QACdN,YAAY,GAAG,IAAI,CAAC9D,gBAAgB,CAAC8C,MAAM,CAACqB,EAAE,IAAIA,EAAE,KAAKjC,OAAO,CAAC;MACnE,CAAC,MAAM;QACL,IAAI,IAAI,CAAClD,aAAa,IAAI,IAAI,CAACgB,gBAAgB,CAACa,MAAM,IAAI,IAAI,CAAC7B,aAAa,EAAE;UAC5E2B,OAAO,CAACC,GAAG,CAAC,WAAW,CAAC;UACxB;QACF;QACAkD,YAAY,GAAG,CAAC,GAAG,IAAI,CAAC9D,gBAAgB,EAAEkC,OAAO,CAAC;MACpD;IACF;IAEA,IAAI,CAAClC,gBAAgB,GAAG8D,YAAY;IACpC,IAAI,CAACQ,WAAW,EAAE;EACpB;EACAC,iBAAiBA,CAACrC,OAAe;IAC/B,IAAI,CAAClC,gBAAgB,GAAG,IAAI,CAACA,gBAAgB,CAAC8C,MAAM,CAACqB,EAAE,IAAIA,EAAE,KAAKjC,OAAO,CAAC;IAC1E,IAAI,CAACoC,WAAW,EAAE;EACpB;EAAEE,mBAAmBA,CAAA;IACnB7D,OAAO,CAACC,GAAG,CAAC,4BAA4B,CAAC;IACzCD,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAE,IAAI,CAACf,gBAAgB,CAAC;IACvDc,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAE,IAAI,CAACb,aAAa,CAAC;IACjDY,OAAO,CAACC,GAAG,CAAC,aAAa,EAAE,IAAI,CAACd,UAAU,CAAC;IAE3C,IAAI,CAAC,IAAI,CAACD,gBAAgB,EAAE;MAC1Bc,OAAO,CAACC,GAAG,CAAC,sBAAsB,CAAC;MACnC;IACF;IAEA;IACA,MAAM6D,sBAAsB,GAAG,IAAI,CAACC,6BAA6B,EAAE;IAEnE/D,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAE6D,sBAAsB,CAACpB,GAAG,CAACf,CAAC,IAAI,GAAGA,CAAC,CAACY,SAAS,IAAIZ,CAAC,CAACU,KAAK,QAAQV,CAAC,CAACJ,OAAO,GAAG,CAAC,CAAC;IAExH,IAAIuC,sBAAsB,CAAC5D,MAAM,KAAK,CAAC,EAAE;MACvCF,OAAO,CAACC,GAAG,CAAC,8BAA8B,CAAC;MAC3C;IACF;IAEA;IACA,MAAM+D,YAAY,GAAG,IAAI,CAAC3E,gBAAgB,CAACa,MAAM;IACjD,MAAM+D,UAAU,GAAG,IAAI,CAAC5F,aAAa,IAAI6F,QAAQ;IACjD,MAAMC,cAAc,GAAGF,UAAU,GAAGD,YAAY;IAEhD;IACA,MAAMI,qBAAqB,GAAa,EAAE;IAC1C,KAAK,MAAMC,SAAS,IAAIP,sBAAsB,EAAE;MAC9C,IAAIO,SAAS,CAAC9C,OAAO,EAAE;QACrB,IAAI,IAAI,CAAC1C,gBAAgB,EAAE;UACzB;UACA,MAAMuE,mBAAmB,GAAG,IAAI,CAACC,yBAAyB,CAACgB,SAAS,CAAC9B,SAAS,CAAC;UAC/E,MAAMe,cAAc,GAAGF,mBAAmB,CAACG,IAAI,CAACC,EAAE,IAAI,IAAI,CAACnE,gBAAgB,CAACoD,QAAQ,CAACe,EAAE,CAAC,CAAC;UACzF,MAAMc,cAAc,GAAGlB,mBAAmB,CAACG,IAAI,CAACC,EAAE,IAAI,IAAI,CAACR,iBAAiB,CAACQ,EAAE,CAAC,CAAC;UAEjF,IAAI,CAACF,cAAc,IAAI,CAACgB,cAAc,EAAE;YACtCF,qBAAqB,CAACxC,IAAI,CAACwB,mBAAmB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACtD;QACF,CAAC,MAAM;UACL;UACA,IAAI,CAAC,IAAI,CAACM,iBAAiB,CAACW,SAAS,CAAC9C,OAAO,CAAC,IAAI,CAAC,IAAI,CAACyB,iBAAiB,CAACqB,SAAS,CAAC9C,OAAO,CAAC,EAAE;YAC5F6C,qBAAqB,CAACxC,IAAI,CAACyC,SAAS,CAAC9C,OAAO,CAAC;UAC/C;QACF;MACF;IACF;IAEAvB,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEmE,qBAAqB,CAAC;IAE9D;IACA,MAAMG,KAAK,GAAGH,qBAAqB,CAACI,KAAK,CAAC,CAAC,EAAEL,cAAc,CAAC;IAE5D,IAAII,KAAK,CAACrE,MAAM,GAAG,CAAC,EAAE;MACpB,IAAI,CAACb,gBAAgB,GAAG,CAAC,GAAG,IAAI,CAACA,gBAAgB,EAAE,GAAGkF,KAAK,CAAC;MAC5D,IAAI,CAACZ,WAAW,EAAE;MAClB3D,OAAO,CAACC,GAAG,CAAC,aAAasE,KAAK,CAACrE,MAAM,WAAW,EAAEqE,KAAK,CAAC;IAC1D,CAAC,MAAM;MACLvE,OAAO,CAACC,GAAG,CAAC,sBAAsB,CAAC;IACrC;EACF;EAAEwE,mBAAmBA,CAAA;IACnB,IAAI,CAAC,IAAI,CAACvF,gBAAgB,EAAE;IAE5B;IACA,MAAMwF,kBAAkB,GAAG,IAAI,CAAClG,YAAY,CAAC,IAAI,CAACU,gBAAgB,CAAC,IAAI,EAAE;IAEzE;IACA,MAAM8E,YAAY,GAAG,IAAI,CAAC3E,gBAAgB,CAACa,MAAM;IACjD,MAAM+D,UAAU,GAAG,IAAI,CAAC5F,aAAa,IAAI6F,QAAQ;IACjD,MAAMC,cAAc,GAAGF,UAAU,GAAGD,YAAY;IAEhD;IACA,MAAMW,qBAAqB,GAAa,EAAE;IAE1C,IAAI,IAAI,CAAC9F,gBAAgB,EAAE;MACzB;MACA,MAAM+F,mBAAmB,GAAG,IAAIxE,GAAG,EAAU;MAE7C,KAAK,MAAMiE,SAAS,IAAIK,kBAAkB,EAAE;QAC1C,IAAIL,SAAS,CAAC9C,OAAO,IAAI8C,SAAS,CAAC9B,SAAS,IAAI,CAACqC,mBAAmB,CAACC,GAAG,CAACR,SAAS,CAAC9B,SAAS,CAAC,EAAE;UAC7FqC,mBAAmB,CAACE,GAAG,CAACT,SAAS,CAAC9B,SAAS,CAAC;UAE5C,MAAMa,mBAAmB,GAAG,IAAI,CAACC,yBAAyB,CAACgB,SAAS,CAAC9B,SAAS,CAAC;UAC/E,MAAMe,cAAc,GAAGF,mBAAmB,CAACG,IAAI,CAACC,EAAE,IAAI,IAAI,CAACnE,gBAAgB,CAACoD,QAAQ,CAACe,EAAE,CAAC,CAAC;UACzF,MAAMc,cAAc,GAAGlB,mBAAmB,CAACG,IAAI,CAACC,EAAE,IAAI,IAAI,CAACuB,mBAAmB,CAACvB,EAAE,CAAC,CAAC;UAEnF,IAAI,CAACF,cAAc,IAAI,CAACgB,cAAc,EAAE;YACtCK,qBAAqB,CAAC/C,IAAI,CAACwB,mBAAmB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACtD;QACF;MACF;IACF,CAAC,MAAM;MACL;MACA,KAAK,MAAMiB,SAAS,IAAIK,kBAAkB,EAAE;QAC1C,IAAIL,SAAS,CAAC9C,OAAO,IACnB,CAAC,IAAI,CAAClC,gBAAgB,CAACoD,QAAQ,CAAC4B,SAAS,CAAC9C,OAAO,CAAC,IAClD,CAAC,IAAI,CAACwD,mBAAmB,CAACV,SAAS,CAAC9C,OAAO,CAAC,EAAE;UAC9CoD,qBAAqB,CAAC/C,IAAI,CAACyC,SAAS,CAAC9C,OAAO,CAAC;QAC/C;MACF;IACF;IAEA;IACA,MAAMgD,KAAK,GAAGI,qBAAqB,CAACH,KAAK,CAAC,CAAC,EAAEL,cAAc,CAAC;IAE5D,IAAII,KAAK,CAACrE,MAAM,GAAG,CAAC,EAAE;MACpB,IAAI,CAACb,gBAAgB,GAAG,CAAC,GAAG,IAAI,CAACA,gBAAgB,EAAE,GAAGkF,KAAK,CAAC;MAC5D,IAAI,CAACZ,WAAW,EAAE;MAClB3D,OAAO,CAACC,GAAG,CAAC,aAAasE,KAAK,CAACrE,MAAM,MAAM,CAAC;IAC9C;EACF;EAEA8E,qBAAqBA,CAAA;IACnB,IAAI,CAAC,IAAI,CAAC9F,gBAAgB,EAAE;IAE5B,MAAMwF,kBAAkB,GAAG,IAAI,CAAClG,YAAY,CAAC,IAAI,CAACU,gBAAgB,CAAC,IAAI,EAAE;IACzE,MAAM+F,gBAAgB,GAAGP,kBAAkB,CAAChC,GAAG,CAACf,CAAC,IAAIA,CAAC,CAACJ,OAAO,CAAC,CAACY,MAAM,CAACqB,EAAE,IAAIA,EAAE,KAAK0B,SAAS,CAAa;IAC1G,IAAI,CAAC7F,gBAAgB,GAAG,IAAI,CAACA,gBAAgB,CAAC8C,MAAM,CAACqB,EAAE,IAAI,CAACyB,gBAAgB,CAACxC,QAAQ,CAACe,EAAE,CAAC,CAAC;IAC1F,IAAI,CAACG,WAAW,EAAE;EACpB;EAEAwB,UAAUA,CAAA;IACR,IAAI,CAAC9F,gBAAgB,GAAG,EAAE;IAC1B,IAAI,CAACsE,WAAW,EAAE;EACpB;EAAUA,WAAWA,CAAA;IACnB,IAAI,CAACnD,wBAAwB,EAAE;IAC/BR,OAAO,CAACC,GAAG,CAAC,sCAAsC,EAAE,IAAI,CAACZ,gBAAgB,CAAC,CAAC,CAAI;IAC/E,IAAI,IAAI,CAACR,gBAAgB,EAAE;MACzB;MACA,IAAI,CAACS,kBAAkB,GAAG,IAAI,CAACiB,sBAAsB,CAAC,IAAI,CAAClB,gBAAgB,CAAC;MAC5E,MAAM+F,gBAAgB,GAAG,CAAC,GAAG,IAAIhF,GAAG,CAAC,IAAI,CAACd,kBAAkB,CAAC,CAAC,CAAC,CAAC;MAChEU,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAEmF,gBAAgB,CAAC;MAClE,IAAI,CAACxF,QAAQ,CAAC,CAAC,GAAGwF,gBAAgB,CAAC,CAAC;MACpC,IAAI,CAACpG,eAAe,CAACqG,IAAI,CAAC,CAAC,GAAGD,gBAAgB,CAAC,CAAC;IAClD,CAAC,MAAM;MACL;MACApF,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAE,IAAI,CAACZ,gBAAgB,CAAC;MAC3D,IAAI,CAACO,QAAQ,CAAC,CAAC,GAAG,IAAI,CAACP,gBAAgB,CAAC,CAAC;MAEzC;MACA,MAAMiG,QAAQ,GAAG,IAAI,CAACjG,gBAAgB,CAAC8C,MAAM,CAACqB,EAAE,IAAIA,EAAE,KAAK0B,SAAS,CAAC;MACrElF,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEqF,QAAQ,CAAC;MAC3C,IAAI,CAACvG,aAAa,CAACsG,IAAI,CAACC,QAAQ,CAAC;IACnC;IAEA,IAAI,CAACxF,SAAS,EAAE;IAEhB;IACA,MAAMyF,aAAa,GAAG,IAAI,CAAClG,gBAAgB,CAACqD,GAAG,CAACnB,OAAO,IAAG;MACxD,KAAK,MAAMC,QAAQ,IAAI,IAAI,CAACjC,SAAS,EAAE;QACrC,MAAMkC,IAAI,GAAG,IAAI,CAACjD,YAAY,CAACgD,QAAQ,CAAC,EAAEE,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACJ,OAAO,KAAKA,OAAO,CAAC;QAC1E,IAAIE,IAAI,EAAE,OAAOA,IAAI;MACvB;MACA,OAAO,IAAI;IACb,CAAC,CAAC,CAACU,MAAM,CAACV,IAAI,IAAIA,IAAI,KAAK,IAAI,CAAoB;IAEnDzB,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEsF,aAAa,CAAC;IACrD,IAAI,CAACzG,eAAe,CAACuG,IAAI,CAACE,aAAa,CAAC;EAC1C;EAAEC,cAAcA,CAAA;IACd,IAAI,CAAC,IAAI,CAAClH,QAAQ,EAAE;MAClB,IAAI,CAACmH,UAAU,EAAE;MACjBzF,OAAO,CAACC,GAAG,CAAC,oCAAoC,CAAC;MACjDD,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAE,IAAI,CAACV,SAAS,CAAC;IACrD;EACF;EAEAkG,UAAUA,CAAA;IACR,IAAI,CAACtH,aAAa,CAACuH,IAAI,CAAC,IAAI,CAACC,eAAe,EAAE;MAC5CC,OAAO,EAAE,EAAE;MACXC,oBAAoB,EAAE,KAAK;MAC3BC,UAAU,EAAE,IAAI;MAChBC,SAAS,EAAE;KACZ,CAAC;EACJ;EAEAC,aAAaA,CAAA;IACX;IACA;EAAA;EACAC,mBAAmBA,CAAC1E,OAA2B;IAC/C,IAAI,CAACA,OAAO,EAAE,OAAO,KAAK;IAE1B,IAAI,IAAI,CAAC1C,gBAAgB,EAAE;MACzB;MACA,MAAMwF,SAAS,GAAG,IAAI,CAACnB,qBAAqB,CAAC3B,OAAO,CAAC;MACrD,IAAI8C,SAAS,EAAE;QACb,MAAMjB,mBAAmB,GAAG,IAAI,CAACC,yBAAyB,CAACgB,SAAS,CAAC9B,SAAS,CAAC;QAC/E,OAAOa,mBAAmB,CAACG,IAAI,CAACC,EAAE,IAAI,IAAI,CAACnE,gBAAgB,CAACoD,QAAQ,CAACe,EAAE,CAAC,CAAC;MAC3E;MACA,OAAO,KAAK;IACd,CAAC,MAAM;MACL;MACA,OAAO,IAAI,CAACnE,gBAAgB,CAACoD,QAAQ,CAAClB,OAAO,CAAC;IAChD;EACF;EAEAwD,mBAAmBA,CAACxD,OAA2B;IAC7C,IAAI,CAACA,OAAO,EAAE,OAAO,KAAK;IAC1B,OAAO,IAAI,CAAC3C,gBAAgB,CAAC6D,QAAQ,CAAClB,OAAO,CAAC;EAChD;EAEA2E,mBAAmBA,CAAC3E,OAA2B;IAC7C,IAAI,CAACA,OAAO,EAAE,OAAO,IAAI;IACzB,OAAO,IAAI,CAACwD,mBAAmB,CAACxD,OAAO,CAAC,IACrC,CAAC,IAAI,CAAC4E,aAAa,EAAE,IAAI,CAAC,IAAI,CAACF,mBAAmB,CAAC1E,OAAO,CAAE;EACjE;EACA4E,aAAaA,CAAA;IACX,OAAO,CAAC,IAAI,CAAC9H,aAAa,IAAI,IAAI,CAACgB,gBAAgB,CAACa,MAAM,GAAG,IAAI,CAAC7B,aAAa;EACjF;EAAE+H,qBAAqBA,CAAA;IACrB,IAAI,CAAC,IAAI,CAAClH,gBAAgB,EAAE,OAAO,KAAK;IACxC,MAAMwF,kBAAkB,GAAG,IAAI,CAAClG,YAAY,CAAC,IAAI,CAACU,gBAAgB,CAAC,CAChEiD,MAAM,CAACR,CAAC,IAAI,CAACA,CAAC,CAACd,UAAU,IAAIc,CAAC,CAACJ,OAAO,KAAK2D,SAAS,CAAC;IACxD,OAAOR,kBAAkB,CAACxE,MAAM,GAAG,CAAC,IAClCwE,kBAAkB,CAAC2B,KAAK,CAAChC,SAAS,IAAIA,SAAS,CAAC9C,OAAO,IAAI,IAAI,CAAClC,gBAAgB,CAACoD,QAAQ,CAAC4B,SAAS,CAAC9C,OAAO,CAAC,CAAC;EACjH;EAEA+E,sBAAsBA,CAAA;IACpB,IAAI,CAAC,IAAI,CAACpH,gBAAgB,EAAE,OAAO,KAAK;IACxC,MAAMwF,kBAAkB,GAAG,IAAI,CAAClG,YAAY,CAAC,IAAI,CAACU,gBAAgB,CAAC,IAAI,EAAE;IACzE,OAAOwF,kBAAkB,CAACnB,IAAI,CAACc,SAAS,IAAIA,SAAS,CAAC9C,OAAO,IAAI,IAAI,CAAClC,gBAAgB,CAACoD,QAAQ,CAAC4B,SAAS,CAAC9C,OAAO,CAAC,CAAC;EACrH;EAAEgF,qBAAqBA,CAAA;IACrB,OAAO,IAAI,CAAC7G,kBAAkB;EAChC;EACA8G,gBAAgBA,CAAChF,QAAgB;IAC/B,IAAI,IAAI,CAAC3C,gBAAgB,EAAE;MACzB;MACA,MAAMoD,UAAU,GAAG,IAAI,CAACzD,YAAY,CAACgD,QAAQ,CAAC,IAAI,EAAE;MACpD,MAAM4D,gBAAgB,GAAG,IAAIhF,GAAG,CAAC6B,UAAU,CAACS,GAAG,CAACf,CAAC,IAAIA,CAAC,CAACY,SAAS,CAAC,CAAC;MAClE,OAAO6C,gBAAgB,CAACqB,IAAI;IAC9B,CAAC,MAAM;MACL;MACA,OAAO,IAAI,CAACjI,YAAY,CAACgD,QAAQ,CAAC,EAAEtB,MAAM,IAAI,CAAC;IACjD;EACF;EACAwG,gBAAgBA,CAAA;IACd,IAAI,IAAI,CAAC7H,gBAAgB,EAAE;MACzB;MACA,MAAMuG,gBAAgB,GAAG,IAAI,CAAC7E,sBAAsB,CAAC,IAAI,CAAClB,gBAAgB,CAAC;MAC3E,OAAO+F,gBAAgB,CAAClF,MAAM;IAChC,CAAC,MAAM;MACL;MACA,OAAO,IAAI,CAACb,gBAAgB,CAACa,MAAM;IACrC;EACF;EAEA;EACAyG,2BAA2BA,CAACnF,QAAgB;IAC1C,OAAO,IAAI,CAAC9B,kBAAkB,CAAC8B,QAAQ,CAAC,IAAI,EAAE;EAChD;EAEA;EACAoF,mBAAmBA,CAACpF,QAAgB;IAClC,OAAO,CAAC,EAAE,IAAI,CAAC9B,kBAAkB,CAAC8B,QAAQ,CAAC,IAAI,IAAI,CAAC9B,kBAAkB,CAAC8B,QAAQ,CAAC,CAACtB,MAAM,GAAG,CAAC,CAAC;EAC9F;EAEA;EACQ4B,uBAAuBA,CAAA;IAC7B,IAAI,CAAC,IAAI,CAAC5C,gBAAgB,EAAE;MAC1B,IAAI,CAACM,MAAM,GAAG,EAAE;MAChB;IACF;IAEA,MAAMyC,UAAU,GAAG,IAAI,CAACzD,YAAY,CAAC,IAAI,CAACU,gBAAgB,CAAC,IAAI,EAAE;IACjE,MAAM2H,QAAQ,GAAG,IAAIzG,GAAG,EAAU;IAElC6B,UAAU,CAACX,OAAO,CAAC+C,SAAS,IAAG;MAC7B,IAAIA,SAAS,CAAChC,KAAK,EAAE;QACnBwE,QAAQ,CAAC/B,GAAG,CAACT,SAAS,CAAChC,KAAK,CAAC;MAC/B;IACF,CAAC,CAAC;IAEF;IACA,IAAI,CAAC7C,MAAM,GAAGsH,KAAK,CAACC,IAAI,CAACF,QAAQ,CAAC,CAACG,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAI;MAC/C,MAAMC,IAAI,GAAGC,QAAQ,CAACH,CAAC,CAACI,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC,CAAC;MAC/C,MAAMC,IAAI,GAAGF,QAAQ,CAACF,CAAC,CAACG,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC,CAAC;MAC/C,OAAOF,IAAI,GAAGG,IAAI;IACpB,CAAC,CAAC;IAEFtH,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAE,IAAI,CAACf,gBAAgB,EAAE,IAAI,CAACM,MAAM,CAAC;EACjF;EAEA;EACA+H,aAAaA,CAAClF,KAAa;IACzBrC,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEoC,KAAK,CAAC;IACrC,IAAI,CAACjD,aAAa,GAAG,IAAI,CAACA,aAAa,KAAKiD,KAAK,GAAG,EAAE,GAAGA,KAAK,CAAC,CAAC;IAChE,IAAI,CAACjB,wBAAwB,EAAE;IAC/B,IAAI,CAACnD,GAAG,CAAC8D,aAAa,EAAE;EAC1B;EAEA;EACAyF,aAAaA,CAACnF,KAAa;IACzB,IAAI,CAAC,IAAI,CAACnD,gBAAgB,EAAE,OAAO,CAAC;IACpC,MAAM+C,UAAU,GAAG,IAAI,CAACzD,YAAY,CAAC,IAAI,CAACU,gBAAgB,CAAC,IAAI,EAAE;IACjE,OAAO+C,UAAU,CAACE,MAAM,CAACR,CAAC,IAAIA,CAAC,CAACU,KAAK,KAAKA,KAAK,CAAC,CAACnC,MAAM;EACzD;EACA;EACAuH,iBAAiBA,CAACC,aAAqB;IACrC,IAAI,CAAC,IAAI,CAACxI,gBAAgB,EAAE,OAAO,EAAE;IACrC,MAAM+C,UAAU,GAAG,IAAI,CAACzD,YAAY,CAAC,IAAI,CAACU,gBAAgB,CAAC,IAAI,EAAE;IACjE,MAAMmF,SAAS,GAAGpC,UAAU,CAACP,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACY,SAAS,KAAKmF,aAAa,CAAC;IACrE,OAAOrD,SAAS,EAAEhC,KAAK,IAAI,EAAE;EAC/B;EACA;EACAsF,gBAAgBA,CAACD,aAAqB;IACpC,KAAK,MAAMlG,QAAQ,IAAI,IAAI,CAACjC,SAAS,EAAE;MACrC,MAAM0C,UAAU,GAAG,IAAI,CAACzD,YAAY,CAACgD,QAAQ,CAAC,IAAI,EAAE;MACpD,MAAM6C,SAAS,GAAGpC,UAAU,CAACP,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACY,SAAS,KAAKmF,aAAa,CAAC;MACrE,IAAIrD,SAAS,EAAE;QACb,OAAO;UACL9B,SAAS,EAAE8B,SAAS,CAAC9B,SAAS;UAC9BF,KAAK,EAAEgC,SAAS,CAAChC,KAAK,IAAI;SAC3B;MACH;IACF;IACA,OAAO;MAAEE,SAAS,EAAEmF,aAAa;MAAErF,KAAK,EAAE;IAAE,CAAE;EAChD;EAEA;EACAuF,oBAAoBA,CAACrG,OAAe;IAClC,KAAK,MAAMC,QAAQ,IAAI,IAAI,CAACjC,SAAS,EAAE;MACrC,MAAM0C,UAAU,GAAG,IAAI,CAACzD,YAAY,CAACgD,QAAQ,CAAC,IAAI,EAAE;MACpD,MAAM6C,SAAS,GAAGpC,UAAU,CAACP,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACJ,OAAO,KAAKA,OAAO,CAAC;MAC7D,IAAI8C,SAAS,EAAE;QACb,OAAO;UACL9B,SAAS,EAAE8B,SAAS,CAAC9B,SAAS;UAC9BF,KAAK,EAAEgC,SAAS,CAAChC,KAAK,IAAI;SAC3B;MACH;IACF;IACA,OAAO;MAAEE,SAAS,EAAE,MAAMhB,OAAO,EAAE;MAAEc,KAAK,EAAE;IAAE,CAAE;EAClD;EAEA;EACAwF,kBAAkBA,CAAA;IAChB,IAAI,CAAC,IAAI,CAAC1I,UAAU,IAAI,CAAC,IAAI,CAACD,gBAAgB,IAAI,CAAC,IAAI,CAACV,YAAY,CAAC,IAAI,CAACU,gBAAgB,CAAC,EAAE;MAC3F,OAAO,KAAK;IACd;IAAE,MAAM4I,QAAQ,GAAG,IAAI,CAACtJ,YAAY,CAAC,IAAI,CAACU,gBAAgB,CAAC,CAACiD,MAAM,CAACR,CAAC,IAAG;MACrE,MAAMS,UAAU,GAAG,IAAI,CAACvD,gBAAgB,IAAI,CAAC,IAAI,CAACO,aAAa,IAAIuC,CAAC,CAACU,KAAK,KAAK,IAAI,CAACjD,aAAa;MACjG,MAAMkD,WAAW,GAAGX,CAAC,CAACY,SAAS,CAACC,WAAW,EAAE,CAACC,QAAQ,CAAC,IAAI,CAACtD,UAAU,CAACqD,WAAW,EAAE,CAAC;MACrF,OAAOJ,UAAU,IAAIE,WAAW;IAClC,CAAC,CAAC;IAEF,OAAOwF,QAAQ,CAAC5H,MAAM,KAAK,CAAC;EAC9B;EACA;EACA6H,0BAA0BA,CAAA;IACxB,OAAO,IAAI,CAAChE,6BAA6B,EAAE,CAAC7D,MAAM;EACpD;EAEA;EACA8H,oBAAoBA,CAAC3D,SAAwB;IAC3C,OAAOA,SAAS,CAAC9C,OAAO,GAAG8C,SAAS,CAAC9C,OAAO,CAAC0G,QAAQ,EAAE,GAAG,GAAG5D,SAAS,CAAC9B,SAAS,IAAI8B,SAAS,CAAChC,KAAK,EAAE;EACvG;EACA;EACQa,qBAAqBA,CAAC3B,OAAe;IAC3C,KAAK,MAAMC,QAAQ,IAAI,IAAI,CAACjC,SAAS,EAAE;MACrC,MAAM0C,UAAU,GAAG,IAAI,CAACzD,YAAY,CAACgD,QAAQ,CAAC,IAAI,EAAE;MACpD,MAAM6C,SAAS,GAAGpC,UAAU,CAACP,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACJ,OAAO,KAAKA,OAAO,CAAC;MAC7D,IAAI8C,SAAS,EAAE,OAAOA,SAAS;IACjC;IACA,OAAO,IAAI;EACb,CAAC,CAAE;EACK6D,qBAAqBA,CAAC3F,SAAiB;IAC7C,MAAM4F,kBAAkB,GAAqD,EAAE;IAE/E;IACA,KAAK,MAAM3G,QAAQ,IAAI,IAAI,CAACjC,SAAS,EAAE;MACrC,MAAM0C,UAAU,GAAG,IAAI,CAACzD,YAAY,CAACgD,QAAQ,CAAC,IAAI,EAAE;MACpD,MAAM4G,OAAO,GAAGnG,UAAU,CAACE,MAAM,CAACR,CAAC,IAAIA,CAAC,CAACY,SAAS,KAAKA,SAAS,CAAC;MACjE6F,OAAO,CAAC9G,OAAO,CAAC+C,SAAS,IAAG;QAC1B8D,kBAAkB,CAACvG,IAAI,CAAC;UAAEJ,QAAQ;UAAE6C;QAAS,CAAE,CAAC;MAClD,CAAC,CAAC;IACJ;IAEArE,OAAO,CAACC,GAAG,CAAC,iBAAiBsC,SAAS,QAAQ,EAAE4F,kBAAkB,CAAC;IAEnE,IAAIA,kBAAkB,CAACjI,MAAM,KAAK,CAAC,EAAE;MACnCF,OAAO,CAACqI,IAAI,CAAC,kBAAkB9F,SAAS,SAAS,CAAC;MAClD,OAAO,IAAI;IACb;IAEA,IAAI4F,kBAAkB,CAACjI,MAAM,GAAG,CAAC,EAAE;MACjCF,OAAO,CAACqI,IAAI,CAAC,aAAa9F,SAAS,IAAI,EAAE4F,kBAAkB,CAACzF,GAAG,CAAC4F,CAAC,IAAI,GAAGA,CAAC,CAAC9G,QAAQ,IAAI8G,CAAC,CAACjE,SAAS,CAAChC,KAAK,EAAE,CAAC,CAAC;MAC3GrC,OAAO,CAACqI,IAAI,CAAC,cAAcF,kBAAkB,CAAC,CAAC,CAAC,CAAC3G,QAAQ,IAAI2G,kBAAkB,CAAC,CAAC,CAAC,CAAC9D,SAAS,CAAChC,KAAK,EAAE,CAAC;IACvG;IAEA,MAAMkG,UAAU,GAAGJ,kBAAkB,CAAC,CAAC,CAAC;IACxC,OAAOI,UAAU,CAAClE,SAAS,CAAC9C,OAAO,IAAI,IAAI;EAC7C;EAEA;EACQ8B,yBAAyBA,CAACd,SAAiB;IACjD,MAAM+C,QAAQ,GAAa,EAAE;IAE7B;IACA,KAAK,MAAM9D,QAAQ,IAAI,IAAI,CAACjC,SAAS,EAAE;MACrC,MAAM0C,UAAU,GAAG,IAAI,CAACzD,YAAY,CAACgD,QAAQ,CAAC,IAAI,EAAE;MACpD,MAAM4G,OAAO,GAAGnG,UAAU,CAACE,MAAM,CAACR,CAAC,IAAIA,CAAC,CAACY,SAAS,KAAKA,SAAS,CAAC;MACjE6F,OAAO,CAAC9G,OAAO,CAAC+C,SAAS,IAAG;QAC1B,IAAIA,SAAS,CAAC9C,OAAO,EAAE;UACrB+D,QAAQ,CAAC1D,IAAI,CAACyC,SAAS,CAAC9C,OAAO,CAAC;QAClC;MACF,CAAC,CAAC;IACJ;IAEA,OAAO+D,QAAQ;EACjB;EAEA;EACQ5B,iBAAiBA,CAACnC,OAAe;IACvC,OAAO,IAAI,CAAClC,gBAAgB,CAACoD,QAAQ,CAAClB,OAAO,CAAC;EAChD;EAEA;EACQyB,iBAAiBA,CAACzB,OAAe;IACvC,OAAO,IAAI,CAAC3C,gBAAgB,CAAC6D,QAAQ,CAAClB,OAAO,CAAC;EAChD;EACA;EACAiH,wBAAwBA,CAACC,QAAgB;IACvC,KAAK,MAAMjH,QAAQ,IAAI,IAAI,CAACjC,SAAS,EAAE;MACrC,MAAM0C,UAAU,GAAG,IAAI,CAACzD,YAAY,CAACgD,QAAQ,CAAC,IAAI,EAAE;MACpD,MAAM6C,SAAS,GAAGpC,UAAU,CAACP,IAAI,CAACC,CAAC,IAAI,IAAI,CAACqG,oBAAoB,CAACrG,CAAC,CAAC,KAAK8G,QAAQ,CAAC;MACjF,IAAIpE,SAAS,EAAE,OAAOA,SAAS;IACjC;IACA,OAAO,IAAI;EACb,CAAC,CAAE;EACKhE,sBAAsBA,CAACqI,UAAoB;IACjD,MAAMpD,QAAQ,GAAa,EAAE;IAC7B,MAAMF,gBAAgB,GAAG,CAAC,GAAG,IAAIhF,GAAG,CAACsI,UAAU,CAAC,CAAC,CAAC,CAAC;IAEnD,KAAK,MAAMnG,SAAS,IAAI6C,gBAAgB,EAAE;MACxC,MAAMuD,gBAAgB,GAAG,IAAI,CAACtF,yBAAyB,CAACd,SAAS,CAAC;MAClE,IAAIoG,gBAAgB,CAACzI,MAAM,GAAG,CAAC,EAAE;QAC/B,IAAI,IAAI,CAACrB,gBAAgB,EAAE;UACzB;UACAyG,QAAQ,CAAC1D,IAAI,CAAC+G,gBAAgB,CAAC,CAAC,CAAC,CAAC;UAClC,IAAIA,gBAAgB,CAACzI,MAAM,GAAG,CAAC,EAAE;YAC/BF,OAAO,CAACC,GAAG,CAAC,SAASsC,SAAS,OAAOoG,gBAAgB,CAACzI,MAAM,oCAAoC,EAAEyI,gBAAgB,CAAC,CAAC,CAAC,CAAC;UACxH;QACF,CAAC,MAAM;UACL;UACArD,QAAQ,CAAC1D,IAAI,CAAC,GAAG+G,gBAAgB,CAAC;UAClC,IAAIA,gBAAgB,CAACzI,MAAM,GAAG,CAAC,EAAE;YAC/BF,OAAO,CAACqI,IAAI,CAAC,SAAS9F,SAAS,OAAOoG,gBAAgB,CAACzI,MAAM,iBAAiB,EAAEyI,gBAAgB,CAAC;UACnG;QACF;MACF,CAAC,MAAM;QACL3I,OAAO,CAACqI,IAAI,CAAC,aAAa9F,SAAS,eAAe,CAAC;MACrD;IACF;IAEA;IACA,OAAO,CAAC,GAAG,IAAInC,GAAG,CAACkF,QAAQ,CAAC,CAAC;EAC/B;EACA;EACQ/E,sBAAsBA,CAAC+E,QAAkB;IAC/C,MAAMoD,UAAU,GAAa,EAAE;IAC/B,MAAME,cAAc,GAAG,CAAC,GAAG,IAAIxI,GAAG,CAACkF,QAAQ,CAAC,CAAC,CAAC,CAAC;IAE/C,KAAK,MAAM/D,OAAO,IAAIqH,cAAc,EAAE;MACpC,MAAMC,aAAa,GAAG,IAAI,CAACjB,oBAAoB,CAACrG,OAAO,CAAC;MACxD,IAAIsH,aAAa,CAACtG,SAAS,IAAI,CAACsG,aAAa,CAACtG,SAAS,CAACuG,UAAU,CAAC,KAAK,CAAC,EAAE;QACzEJ,UAAU,CAAC9G,IAAI,CAACiH,aAAa,CAACtG,SAAS,CAAC;MAC1C,CAAC,MAAM;QACLvC,OAAO,CAACqI,IAAI,CAAC,gBAAgB9G,OAAO,UAAU,CAAC;MACjD;IACF;IAEA;IACA,OAAO,CAAC,GAAG,IAAInB,GAAG,CAACsI,UAAU,CAAC,CAAC;EACjC;EAEA;EACA3E,6BAA6BA,CAAA;IAC3B,IAAI,CAAC,IAAI,CAAC7E,gBAAgB,IAAI,CAAC,IAAI,CAACV,YAAY,CAAC,IAAI,CAACU,gBAAgB,CAAC,EAAE;MACvE,OAAO,EAAE;IACX;IAEA,MAAM+C,UAAU,GAAG,IAAI,CAACzD,YAAY,CAAC,IAAI,CAACU,gBAAgB,CAAC,IAAI,EAAE;IAEjE,IAAI,CAAC,IAAI,CAACL,gBAAgB,EAAE;MAC1B;MACA,OAAOoD,UAAU,CAACE,MAAM,CAACR,CAAC,IAAG;QAC3B,MAAMS,UAAU,GAAG,CAAC,IAAI,CAAChD,aAAa,IAAIuC,CAAC,CAACU,KAAK,KAAK,IAAI,CAACjD,aAAa;QACxE,MAAMkD,WAAW,GAAG,CAAC,IAAI,CAACnD,UAAU,IAAIwC,CAAC,CAACY,SAAS,CAACC,WAAW,EAAE,CAACC,QAAQ,CAAC,IAAI,CAACtD,UAAU,CAACqD,WAAW,EAAE,CAAC;QACzG,OAAOJ,UAAU,IAAIE,WAAW;MAClC,CAAC,CAAC;IACJ;IAEA;IACA,MAAM8C,gBAAgB,GAAG,IAAIhF,GAAG,EAAU;IAC1C,MAAM2I,gBAAgB,GAAoB,EAAE;IAE5C,KAAK,MAAM1E,SAAS,IAAIpC,UAAU,EAAE;MAClC;MACA,MAAMK,WAAW,GAAG,CAAC,IAAI,CAACnD,UAAU,IAAIkF,SAAS,CAAC9B,SAAS,CAACC,WAAW,EAAE,CAACC,QAAQ,CAAC,IAAI,CAACtD,UAAU,CAACqD,WAAW,EAAE,CAAC;MAEjH,IAAIF,WAAW,IAAI,CAAC8C,gBAAgB,CAACP,GAAG,CAACR,SAAS,CAAC9B,SAAS,CAAC,EAAE;QAC7D6C,gBAAgB,CAACN,GAAG,CAACT,SAAS,CAAC9B,SAAS,CAAC;QACzCwG,gBAAgB,CAACnH,IAAI,CAACyC,SAAS,CAAC;MAClC;IACF;IAEArE,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAE8I,gBAAgB,CAACrG,GAAG,CAACf,CAAC,IAAIA,CAAC,CAACY,SAAS,CAAC,CAAC;IACrF,OAAOwG,gBAAgB;EACzB;CACD;AAnvBkDC,UAAA,EAAhDnL,SAAS,CAAC,iBAAiB,EAAE;EAAEoL,MAAM,EAAE;AAAK,CAAE,CAAC,C,iEAAoC;AAC3ED,UAAA,EAARvL,KAAK,EAAE,C,6DAA+B;AAC9BuL,UAAA,EAARvL,KAAK,EAAE,C,+DAAqC;AACpCuL,UAAA,EAARvL,KAAK,EAAE,C,0DAA2B;AAC1BuL,UAAA,EAARvL,KAAK,EAAE,C,6DAAmC;AAClCuL,UAAA,EAARvL,KAAK,EAAE,C,8DAAiC;AAAUuL,UAAA,EAARvL,KAAK,EAAE,C,kEAAkC;AAC3EuL,UAAA,EAARvL,KAAK,EAAE,C,6DAA6B;AAC5BuL,UAAA,EAARvL,KAAK,EAAE,C,kEAAkC;AACjCuL,UAAA,EAARvL,KAAK,EAAE,C,kEAAiC;AAChCuL,UAAA,EAARvL,KAAK,EAAE,C,kEAAmC;AAEjCuL,UAAA,EAATtL,MAAM,EAAE,C,iEAAuD;AACtDsL,UAAA,EAATtL,MAAM,EAAE,C,+DAA8C;AAC7CsL,UAAA,EAATtL,MAAM,EAAE,C,iEAAgD;AAd9CK,yBAAyB,GAAAiL,UAAA,EAZrCxL,SAAS,CAAC;EACT0L,QAAQ,EAAE,uBAAuB;EACjCC,WAAW,EAAE,oCAAoC;EACjDC,SAAS,EAAE,CAAC,oCAAoC,CAAC;EACjDC,SAAS,EAAE,CACT;IACEC,OAAO,EAAExL,iBAAiB;IAC1ByL,WAAW,EAAE3L,UAAU,CAAC,MAAMG,yBAAyB,CAAC;IACxDyL,KAAK,EAAE;GACR;CAEJ,CAAC,C,EACWzL,yBAAyB,CAovBrC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}