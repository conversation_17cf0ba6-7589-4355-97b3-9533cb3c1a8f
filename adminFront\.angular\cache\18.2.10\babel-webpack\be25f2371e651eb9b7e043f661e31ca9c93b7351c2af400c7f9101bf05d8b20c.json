{"ast": null, "code": "//! moment.js locale configuration\n//! locale : Esperanto [eo]\n//! author : <PERSON> : https://github.com/colindean\n//! author : <PERSON> Imperatori : https://github.com/miestasmia\n//! comment : miestasm<PERSON> corrected the translation by colindean\n//! comment : <PERSON><PERSON><PERSON> corrected the translation by colindean and miestasmia\n\n;\n(function (global, factory) {\n  typeof exports === 'object' && typeof module !== 'undefined' && typeof require === 'function' ? factory(require('../moment')) : typeof define === 'function' && define.amd ? define(['../moment'], factory) : factory(global.moment);\n})(this, function (moment) {\n  'use strict';\n\n  //! moment.js locale configuration\n  var eo = moment.defineLocale('eo', {\n    months: 'januaro_februaro_marto_aprilo_majo_junio_julio_aŭgusto_septembro_oktobro_novembro_decembro'.split('_'),\n    monthsShort: 'jan_feb_mart_apr_maj_jun_jul_aŭg_sept_okt_nov_dec'.split('_'),\n    weekdays: 'dimanĉo_lundo_mardo_merkredo_ĵaŭdo_vendredo_sabato'.split('_'),\n    weekdaysShort: 'dim_lun_mard_merk_ĵaŭ_ven_sab'.split('_'),\n    weekdaysMin: 'di_lu_ma_me_ĵa_ve_sa'.split('_'),\n    longDateFormat: {\n      LT: 'HH:mm',\n      LTS: 'HH:mm:ss',\n      L: 'YYYY-MM-DD',\n      LL: '[la] D[-an de] MMMM, YYYY',\n      LLL: '[la] D[-an de] MMMM, YYYY HH:mm',\n      LLLL: 'dddd[n], [la] D[-an de] MMMM, YYYY HH:mm',\n      llll: 'ddd, [la] D[-an de] MMM, YYYY HH:mm'\n    },\n    meridiemParse: /[ap]\\.t\\.m/i,\n    isPM: function (input) {\n      return input.charAt(0).toLowerCase() === 'p';\n    },\n    meridiem: function (hours, minutes, isLower) {\n      if (hours > 11) {\n        return isLower ? 'p.t.m.' : 'P.T.M.';\n      } else {\n        return isLower ? 'a.t.m.' : 'A.T.M.';\n      }\n    },\n    calendar: {\n      sameDay: '[Hodiaŭ je] LT',\n      nextDay: '[Morgaŭ je] LT',\n      nextWeek: 'dddd[n je] LT',\n      lastDay: '[Hieraŭ je] LT',\n      lastWeek: '[pasintan] dddd[n je] LT',\n      sameElse: 'L'\n    },\n    relativeTime: {\n      future: 'post %s',\n      past: 'antaŭ %s',\n      s: 'kelkaj sekundoj',\n      ss: '%d sekundoj',\n      m: 'unu minuto',\n      mm: '%d minutoj',\n      h: 'unu horo',\n      hh: '%d horoj',\n      d: 'unu tago',\n      //ne 'diurno', ĉar estas uzita por proksimumo\n      dd: '%d tagoj',\n      M: 'unu monato',\n      MM: '%d monatoj',\n      y: 'unu jaro',\n      yy: '%d jaroj'\n    },\n    dayOfMonthOrdinalParse: /\\d{1,2}a/,\n    ordinal: '%da',\n    week: {\n      dow: 1,\n      // Monday is the first day of the week.\n      doy: 7 // The week that contains Jan 7th is the first week of the year.\n    }\n  });\n  return eo;\n});", "map": {"version": 3, "names": ["global", "factory", "exports", "module", "require", "define", "amd", "moment", "eo", "defineLocale", "months", "split", "monthsShort", "weekdays", "weekdaysShort", "weekdaysMin", "longDateFormat", "LT", "LTS", "L", "LL", "LLL", "LLLL", "llll", "meridiemParse", "isPM", "input", "char<PERSON>t", "toLowerCase", "meridiem", "hours", "minutes", "isLower", "calendar", "sameDay", "nextDay", "nextWeek", "lastDay", "lastWeek", "same<PERSON><PERSON><PERSON>", "relativeTime", "future", "past", "s", "ss", "m", "mm", "h", "hh", "d", "dd", "M", "MM", "y", "yy", "dayOfMonthOrdinalParse", "ordinal", "week", "dow", "doy"], "sources": ["C:/Users/<USER>/Documents/salechange-product/adminFront/node_modules/moment/locale/eo.js"], "sourcesContent": ["//! moment.js locale configuration\n//! locale : Esperanto [eo]\n//! author : <PERSON> : https://github.com/colindean\n//! author : <PERSON> Imperatori : https://github.com/miestasmia\n//! comment : miestasm<PERSON> corrected the translation by colindean\n//! comment : <PERSON><PERSON><PERSON> corrected the translation by colindean and miestasmia\n\n;(function (global, factory) {\n   typeof exports === 'object' && typeof module !== 'undefined'\n       && typeof require === 'function' ? factory(require('../moment')) :\n   typeof define === 'function' && define.amd ? define(['../moment'], factory) :\n   factory(global.moment)\n}(this, (function (moment) { 'use strict';\n\n    //! moment.js locale configuration\n\n    var eo = moment.defineLocale('eo', {\n        months: 'januaro_februaro_marto_aprilo_majo_junio_julio_aŭgusto_septembro_oktobro_novembro_decembro'.split(\n            '_'\n        ),\n        monthsShort: 'jan_feb_mart_apr_maj_jun_jul_aŭg_sept_okt_nov_dec'.split('_'),\n        weekdays: 'dimanĉo_lundo_mardo_merkredo_ĵaŭdo_vendredo_sabato'.split('_'),\n        weekdaysShort: 'dim_lun_mard_merk_ĵaŭ_ven_sab'.split('_'),\n        weekdaysMin: 'di_lu_ma_me_ĵa_ve_sa'.split('_'),\n        longDateFormat: {\n            LT: 'HH:mm',\n            LTS: 'HH:mm:ss',\n            L: 'YYYY-MM-DD',\n            LL: '[la] D[-an de] MMMM, YYYY',\n            LLL: '[la] D[-an de] MMMM, YYYY HH:mm',\n            LLLL: 'dddd[n], [la] D[-an de] MMMM, YYYY HH:mm',\n            llll: 'ddd, [la] D[-an de] MMM, YYYY HH:mm',\n        },\n        meridiemParse: /[ap]\\.t\\.m/i,\n        isPM: function (input) {\n            return input.charAt(0).toLowerCase() === 'p';\n        },\n        meridiem: function (hours, minutes, isLower) {\n            if (hours > 11) {\n                return isLower ? 'p.t.m.' : 'P.T.M.';\n            } else {\n                return isLower ? 'a.t.m.' : 'A.T.M.';\n            }\n        },\n        calendar: {\n            sameDay: '[Hodiaŭ je] LT',\n            nextDay: '[Morgaŭ je] LT',\n            nextWeek: 'dddd[n je] LT',\n            lastDay: '[Hieraŭ je] LT',\n            lastWeek: '[pasintan] dddd[n je] LT',\n            sameElse: 'L',\n        },\n        relativeTime: {\n            future: 'post %s',\n            past: 'antaŭ %s',\n            s: 'kelkaj sekundoj',\n            ss: '%d sekundoj',\n            m: 'unu minuto',\n            mm: '%d minutoj',\n            h: 'unu horo',\n            hh: '%d horoj',\n            d: 'unu tago', //ne 'diurno', ĉar estas uzita por proksimumo\n            dd: '%d tagoj',\n            M: 'unu monato',\n            MM: '%d monatoj',\n            y: 'unu jaro',\n            yy: '%d jaroj',\n        },\n        dayOfMonthOrdinalParse: /\\d{1,2}a/,\n        ordinal: '%da',\n        week: {\n            dow: 1, // Monday is the first day of the week.\n            doy: 7, // The week that contains Jan 7th is the first week of the year.\n        },\n    });\n\n    return eo;\n\n})));\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;;AAEA;AAAE,WAAUA,MAAM,EAAEC,OAAO,EAAE;EAC1B,OAAOC,OAAO,KAAK,QAAQ,IAAI,OAAOC,MAAM,KAAK,WAAW,IACrD,OAAOC,OAAO,KAAK,UAAU,GAAGH,OAAO,CAACG,OAAO,CAAC,WAAW,CAAC,CAAC,GACpE,OAAOC,MAAM,KAAK,UAAU,IAAIA,MAAM,CAACC,GAAG,GAAGD,MAAM,CAAC,CAAC,WAAW,CAAC,EAAEJ,OAAO,CAAC,GAC3EA,OAAO,CAACD,MAAM,CAACO,MAAM,CAAC;AACzB,CAAC,EAAC,IAAI,EAAG,UAAUA,MAAM,EAAE;EAAE,YAAY;;EAErC;EAEA,IAAIC,EAAE,GAAGD,MAAM,CAACE,YAAY,CAAC,IAAI,EAAE;IAC/BC,MAAM,EAAE,4FAA4F,CAACC,KAAK,CACtG,GACJ,CAAC;IACDC,WAAW,EAAE,mDAAmD,CAACD,KAAK,CAAC,GAAG,CAAC;IAC3EE,QAAQ,EAAE,oDAAoD,CAACF,KAAK,CAAC,GAAG,CAAC;IACzEG,aAAa,EAAE,+BAA+B,CAACH,KAAK,CAAC,GAAG,CAAC;IACzDI,WAAW,EAAE,sBAAsB,CAACJ,KAAK,CAAC,GAAG,CAAC;IAC9CK,cAAc,EAAE;MACZC,EAAE,EAAE,OAAO;MACXC,GAAG,EAAE,UAAU;MACfC,CAAC,EAAE,YAAY;MACfC,EAAE,EAAE,2BAA2B;MAC/BC,GAAG,EAAE,iCAAiC;MACtCC,IAAI,EAAE,0CAA0C;MAChDC,IAAI,EAAE;IACV,CAAC;IACDC,aAAa,EAAE,aAAa;IAC5BC,IAAI,EAAE,SAAAA,CAAUC,KAAK,EAAE;MACnB,OAAOA,KAAK,CAACC,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,KAAK,GAAG;IAChD,CAAC;IACDC,QAAQ,EAAE,SAAAA,CAAUC,KAAK,EAAEC,OAAO,EAAEC,OAAO,EAAE;MACzC,IAAIF,KAAK,GAAG,EAAE,EAAE;QACZ,OAAOE,OAAO,GAAG,QAAQ,GAAG,QAAQ;MACxC,CAAC,MAAM;QACH,OAAOA,OAAO,GAAG,QAAQ,GAAG,QAAQ;MACxC;IACJ,CAAC;IACDC,QAAQ,EAAE;MACNC,OAAO,EAAE,gBAAgB;MACzBC,OAAO,EAAE,gBAAgB;MACzBC,QAAQ,EAAE,eAAe;MACzBC,OAAO,EAAE,gBAAgB;MACzBC,QAAQ,EAAE,0BAA0B;MACpCC,QAAQ,EAAE;IACd,CAAC;IACDC,YAAY,EAAE;MACVC,MAAM,EAAE,SAAS;MACjBC,IAAI,EAAE,UAAU;MAChBC,CAAC,EAAE,iBAAiB;MACpBC,EAAE,EAAE,aAAa;MACjBC,CAAC,EAAE,YAAY;MACfC,EAAE,EAAE,YAAY;MAChBC,CAAC,EAAE,UAAU;MACbC,EAAE,EAAE,UAAU;MACdC,CAAC,EAAE,UAAU;MAAE;MACfC,EAAE,EAAE,UAAU;MACdC,CAAC,EAAE,YAAY;MACfC,EAAE,EAAE,YAAY;MAChBC,CAAC,EAAE,UAAU;MACbC,EAAE,EAAE;IACR,CAAC;IACDC,sBAAsB,EAAE,UAAU;IAClCC,OAAO,EAAE,KAAK;IACdC,IAAI,EAAE;MACFC,GAAG,EAAE,CAAC;MAAE;MACRC,GAAG,EAAE,CAAC,CAAE;IACZ;EACJ,CAAC,CAAC;EAEF,OAAOnD,EAAE;AAEb,CAAE,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}