{"ast": null, "code": "import { map } from 'rxjs/operators';\nimport { BaseService } from '../base-service';\nimport { apiBuildCaseDeleteBuildCasePost$Json } from '../fn/build-case/api-build-case-delete-build-case-post-json';\nimport { apiBuildCaseDeleteBuildCasePost$Plain } from '../fn/build-case/api-build-case-delete-build-case-post-plain';\nimport { apiBuildCaseGetAllBuildCasePost$Json } from '../fn/build-case/api-build-case-get-all-build-case-post-json';\nimport { apiBuildCaseGetAllBuildCasePost$Plain } from '../fn/build-case/api-build-case-get-all-build-case-post-plain';\nimport { apiBuildCaseGetAnnouncementPost$Json } from '../fn/build-case/api-build-case-get-announcement-post-json';\nimport { apiBuildCaseGetAnnouncementPost$Plain } from '../fn/build-case/api-build-case-get-announcement-post-plain';\nimport { apiBuildCaseGetBuildCaseByIdPost$Json } from '../fn/build-case/api-build-case-get-build-case-by-id-post-json';\nimport { apiBuildCaseGetBuildCaseByIdPost$Plain } from '../fn/build-case/api-build-case-get-build-case-by-id-post-plain';\nimport { apiBuildCaseGetBuildCaseFilePost$Json } from '../fn/build-case/api-build-case-get-build-case-file-post-json';\nimport { apiBuildCaseGetBuildCaseFilePost$Plain } from '../fn/build-case/api-build-case-get-build-case-file-post-plain';\nimport { apiBuildCaseGetBuildCaseListPost$Json } from '../fn/build-case/api-build-case-get-build-case-list-post-json';\nimport { apiBuildCaseGetBuildCaseListPost$Plain } from '../fn/build-case/api-build-case-get-build-case-list-post-plain';\nimport { apiBuildCaseGetDisclaimerPost$Json } from '../fn/build-case/api-build-case-get-disclaimer-post-json';\nimport { apiBuildCaseGetDisclaimerPost$Plain } from '../fn/build-case/api-build-case-get-disclaimer-post-plain';\nimport { apiBuildCaseGetHouseAndFloorByBuildCaseIdPost$Json } from '../fn/build-case/api-build-case-get-house-and-floor-by-build-case-id-post-json';\nimport { apiBuildCaseGetHouseAndFloorByBuildCaseIdPost$Plain } from '../fn/build-case/api-build-case-get-house-and-floor-by-build-case-id-post-plain';\nimport { apiBuildCaseGetSystemInstructionPost$Json } from '../fn/build-case/api-build-case-get-system-instruction-post-json';\nimport { apiBuildCaseGetSystemInstructionPost$Plain } from '../fn/build-case/api-build-case-get-system-instruction-post-plain';\nimport { apiBuildCaseGetUserBuildCasePost$Json } from '../fn/build-case/api-build-case-get-user-build-case-post-json';\nimport { apiBuildCaseGetUserBuildCasePost$Plain } from '../fn/build-case/api-build-case-get-user-build-case-post-plain';\nimport { apiBuildCaseSaveBuildCasePost$Json } from '../fn/build-case/api-build-case-save-build-case-post-json';\nimport { apiBuildCaseSaveBuildCasePost$Plain } from '../fn/build-case/api-build-case-save-build-case-post-plain';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../api-configuration\";\nimport * as i2 from \"@angular/common/http\";\nexport class BuildCaseService extends BaseService {\n  constructor(config, http) {\n    super(config, http);\n  }\n  /** Path part for operation `apiBuildCaseGetBuildCaseListPost()` */\n  static {\n    this.ApiBuildCaseGetBuildCaseListPostPath = '/api/BuildCase/GetBuildCaseList';\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiBuildCaseGetBuildCaseListPost$Plain()` instead.\n   *\n   * This method doesn't expect any request body.\n   */\n  apiBuildCaseGetBuildCaseListPost$Plain$Response(params, context) {\n    return apiBuildCaseGetBuildCaseListPost$Plain(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiBuildCaseGetBuildCaseListPost$Plain$Response()` instead.\n   *\n   * This method doesn't expect any request body.\n   */\n  apiBuildCaseGetBuildCaseListPost$Plain(params, context) {\n    return this.apiBuildCaseGetBuildCaseListPost$Plain$Response(params, context).pipe(map(r => r.body));\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiBuildCaseGetBuildCaseListPost$Json()` instead.\n   *\n   * This method doesn't expect any request body.\n   */\n  apiBuildCaseGetBuildCaseListPost$Json$Response(params, context) {\n    return apiBuildCaseGetBuildCaseListPost$Json(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiBuildCaseGetBuildCaseListPost$Json$Response()` instead.\n   *\n   * This method doesn't expect any request body.\n   */\n  apiBuildCaseGetBuildCaseListPost$Json(params, context) {\n    return this.apiBuildCaseGetBuildCaseListPost$Json$Response(params, context).pipe(map(r => r.body));\n  }\n  /** Path part for operation `apiBuildCaseGetAnnouncementPost()` */\n  static {\n    this.ApiBuildCaseGetAnnouncementPostPath = '/api/BuildCase/GetAnnouncement';\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiBuildCaseGetAnnouncementPost$Plain()` instead.\n   *\n   * This method doesn't expect any request body.\n   */\n  apiBuildCaseGetAnnouncementPost$Plain$Response(params, context) {\n    return apiBuildCaseGetAnnouncementPost$Plain(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiBuildCaseGetAnnouncementPost$Plain$Response()` instead.\n   *\n   * This method doesn't expect any request body.\n   */\n  apiBuildCaseGetAnnouncementPost$Plain(params, context) {\n    return this.apiBuildCaseGetAnnouncementPost$Plain$Response(params, context).pipe(map(r => r.body));\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiBuildCaseGetAnnouncementPost$Json()` instead.\n   *\n   * This method doesn't expect any request body.\n   */\n  apiBuildCaseGetAnnouncementPost$Json$Response(params, context) {\n    return apiBuildCaseGetAnnouncementPost$Json(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiBuildCaseGetAnnouncementPost$Json$Response()` instead.\n   *\n   * This method doesn't expect any request body.\n   */\n  apiBuildCaseGetAnnouncementPost$Json(params, context) {\n    return this.apiBuildCaseGetAnnouncementPost$Json$Response(params, context).pipe(map(r => r.body));\n  }\n  /** Path part for operation `apiBuildCaseGetBuildCaseFilePost()` */\n  static {\n    this.ApiBuildCaseGetBuildCaseFilePostPath = '/api/BuildCase/GetBuildCaseFile';\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiBuildCaseGetBuildCaseFilePost$Plain()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiBuildCaseGetBuildCaseFilePost$Plain$Response(params, context) {\n    return apiBuildCaseGetBuildCaseFilePost$Plain(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiBuildCaseGetBuildCaseFilePost$Plain$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiBuildCaseGetBuildCaseFilePost$Plain(params, context) {\n    return this.apiBuildCaseGetBuildCaseFilePost$Plain$Response(params, context).pipe(map(r => r.body));\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiBuildCaseGetBuildCaseFilePost$Json()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiBuildCaseGetBuildCaseFilePost$Json$Response(params, context) {\n    return apiBuildCaseGetBuildCaseFilePost$Json(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiBuildCaseGetBuildCaseFilePost$Json$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiBuildCaseGetBuildCaseFilePost$Json(params, context) {\n    return this.apiBuildCaseGetBuildCaseFilePost$Json$Response(params, context).pipe(map(r => r.body));\n  }\n  /** Path part for operation `apiBuildCaseGetDisclaimerPost()` */\n  static {\n    this.ApiBuildCaseGetDisclaimerPostPath = '/api/BuildCase/GetDisclaimer';\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiBuildCaseGetDisclaimerPost$Plain()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiBuildCaseGetDisclaimerPost$Plain$Response(params, context) {\n    return apiBuildCaseGetDisclaimerPost$Plain(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiBuildCaseGetDisclaimerPost$Plain$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiBuildCaseGetDisclaimerPost$Plain(params, context) {\n    return this.apiBuildCaseGetDisclaimerPost$Plain$Response(params, context).pipe(map(r => r.body));\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiBuildCaseGetDisclaimerPost$Json()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiBuildCaseGetDisclaimerPost$Json$Response(params, context) {\n    return apiBuildCaseGetDisclaimerPost$Json(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiBuildCaseGetDisclaimerPost$Json$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiBuildCaseGetDisclaimerPost$Json(params, context) {\n    return this.apiBuildCaseGetDisclaimerPost$Json$Response(params, context).pipe(map(r => r.body));\n  }\n  /** Path part for operation `apiBuildCaseGetSystemInstructionPost()` */\n  static {\n    this.ApiBuildCaseGetSystemInstructionPostPath = '/api/BuildCase/GetSystemInstruction';\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiBuildCaseGetSystemInstructionPost$Plain()` instead.\n   *\n   * This method doesn't expect any request body.\n   */\n  apiBuildCaseGetSystemInstructionPost$Plain$Response(params, context) {\n    return apiBuildCaseGetSystemInstructionPost$Plain(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiBuildCaseGetSystemInstructionPost$Plain$Response()` instead.\n   *\n   * This method doesn't expect any request body.\n   */\n  apiBuildCaseGetSystemInstructionPost$Plain(params, context) {\n    return this.apiBuildCaseGetSystemInstructionPost$Plain$Response(params, context).pipe(map(r => r.body));\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiBuildCaseGetSystemInstructionPost$Json()` instead.\n   *\n   * This method doesn't expect any request body.\n   */\n  apiBuildCaseGetSystemInstructionPost$Json$Response(params, context) {\n    return apiBuildCaseGetSystemInstructionPost$Json(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiBuildCaseGetSystemInstructionPost$Json$Response()` instead.\n   *\n   * This method doesn't expect any request body.\n   */\n  apiBuildCaseGetSystemInstructionPost$Json(params, context) {\n    return this.apiBuildCaseGetSystemInstructionPost$Json$Response(params, context).pipe(map(r => r.body));\n  }\n  /** Path part for operation `apiBuildCaseGetHouseAndFloorByBuildCaseIdPost()` */\n  static {\n    this.ApiBuildCaseGetHouseAndFloorByBuildCaseIdPostPath = '/api/BuildCase/GetHouseAndFloorByBuildCaseId';\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiBuildCaseGetHouseAndFloorByBuildCaseIdPost$Plain()` instead.\n   *\n   * This method doesn't expect any request body.\n   */\n  apiBuildCaseGetHouseAndFloorByBuildCaseIdPost$Plain$Response(params, context) {\n    return apiBuildCaseGetHouseAndFloorByBuildCaseIdPost$Plain(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiBuildCaseGetHouseAndFloorByBuildCaseIdPost$Plain$Response()` instead.\n   *\n   * This method doesn't expect any request body.\n   */\n  apiBuildCaseGetHouseAndFloorByBuildCaseIdPost$Plain(params, context) {\n    return this.apiBuildCaseGetHouseAndFloorByBuildCaseIdPost$Plain$Response(params, context).pipe(map(r => r.body));\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiBuildCaseGetHouseAndFloorByBuildCaseIdPost$Json()` instead.\n   *\n   * This method doesn't expect any request body.\n   */\n  apiBuildCaseGetHouseAndFloorByBuildCaseIdPost$Json$Response(params, context) {\n    return apiBuildCaseGetHouseAndFloorByBuildCaseIdPost$Json(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiBuildCaseGetHouseAndFloorByBuildCaseIdPost$Json$Response()` instead.\n   *\n   * This method doesn't expect any request body.\n   */\n  apiBuildCaseGetHouseAndFloorByBuildCaseIdPost$Json(params, context) {\n    return this.apiBuildCaseGetHouseAndFloorByBuildCaseIdPost$Json$Response(params, context).pipe(map(r => r.body));\n  }\n  /** Path part for operation `apiBuildCaseGetUserBuildCasePost()` */\n  static {\n    this.ApiBuildCaseGetUserBuildCasePostPath = '/api/BuildCase/GetUserBuildCase';\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiBuildCaseGetUserBuildCasePost$Plain()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiBuildCaseGetUserBuildCasePost$Plain$Response(params, context) {\n    return apiBuildCaseGetUserBuildCasePost$Plain(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiBuildCaseGetUserBuildCasePost$Plain$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiBuildCaseGetUserBuildCasePost$Plain(params, context) {\n    return this.apiBuildCaseGetUserBuildCasePost$Plain$Response(params, context).pipe(map(r => r.body));\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiBuildCaseGetUserBuildCasePost$Json()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiBuildCaseGetUserBuildCasePost$Json$Response(params, context) {\n    return apiBuildCaseGetUserBuildCasePost$Json(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiBuildCaseGetUserBuildCasePost$Json$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiBuildCaseGetUserBuildCasePost$Json(params, context) {\n    return this.apiBuildCaseGetUserBuildCasePost$Json$Response(params, context).pipe(map(r => r.body));\n  }\n  /** Path part for operation `apiBuildCaseGetAllBuildCasePost()` */\n  static {\n    this.ApiBuildCaseGetAllBuildCasePostPath = '/api/BuildCase/GetAllBuildCase';\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiBuildCaseGetAllBuildCasePost$Plain()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiBuildCaseGetAllBuildCasePost$Plain$Response(params, context) {\n    return apiBuildCaseGetAllBuildCasePost$Plain(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiBuildCaseGetAllBuildCasePost$Plain$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiBuildCaseGetAllBuildCasePost$Plain(params, context) {\n    return this.apiBuildCaseGetAllBuildCasePost$Plain$Response(params, context).pipe(map(r => r.body));\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiBuildCaseGetAllBuildCasePost$Json()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiBuildCaseGetAllBuildCasePost$Json$Response(params, context) {\n    return apiBuildCaseGetAllBuildCasePost$Json(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiBuildCaseGetAllBuildCasePost$Json$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiBuildCaseGetAllBuildCasePost$Json(params, context) {\n    return this.apiBuildCaseGetAllBuildCasePost$Json$Response(params, context).pipe(map(r => r.body));\n  }\n  /** Path part for operation `apiBuildCaseDeleteBuildCasePost()` */\n  static {\n    this.ApiBuildCaseDeleteBuildCasePostPath = '/api/BuildCase/DeleteBuildCase';\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiBuildCaseDeleteBuildCasePost$Plain()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiBuildCaseDeleteBuildCasePost$Plain$Response(params, context) {\n    return apiBuildCaseDeleteBuildCasePost$Plain(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiBuildCaseDeleteBuildCasePost$Plain$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiBuildCaseDeleteBuildCasePost$Plain(params, context) {\n    return this.apiBuildCaseDeleteBuildCasePost$Plain$Response(params, context).pipe(map(r => r.body));\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiBuildCaseDeleteBuildCasePost$Json()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiBuildCaseDeleteBuildCasePost$Json$Response(params, context) {\n    return apiBuildCaseDeleteBuildCasePost$Json(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiBuildCaseDeleteBuildCasePost$Json$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiBuildCaseDeleteBuildCasePost$Json(params, context) {\n    return this.apiBuildCaseDeleteBuildCasePost$Json$Response(params, context).pipe(map(r => r.body));\n  }\n  /** Path part for operation `apiBuildCaseGetBuildCaseByIdPost()` */\n  static {\n    this.ApiBuildCaseGetBuildCaseByIdPostPath = '/api/BuildCase/GetBuildCaseByID';\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiBuildCaseGetBuildCaseByIdPost$Plain()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiBuildCaseGetBuildCaseByIdPost$Plain$Response(params, context) {\n    return apiBuildCaseGetBuildCaseByIdPost$Plain(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiBuildCaseGetBuildCaseByIdPost$Plain$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiBuildCaseGetBuildCaseByIdPost$Plain(params, context) {\n    return this.apiBuildCaseGetBuildCaseByIdPost$Plain$Response(params, context).pipe(map(r => r.body));\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiBuildCaseGetBuildCaseByIdPost$Json()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiBuildCaseGetBuildCaseByIdPost$Json$Response(params, context) {\n    return apiBuildCaseGetBuildCaseByIdPost$Json(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiBuildCaseGetBuildCaseByIdPost$Json$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiBuildCaseGetBuildCaseByIdPost$Json(params, context) {\n    return this.apiBuildCaseGetBuildCaseByIdPost$Json$Response(params, context).pipe(map(r => r.body));\n  }\n  /** Path part for operation `apiBuildCaseSaveBuildCasePost()` */\n  static {\n    this.ApiBuildCaseSaveBuildCasePostPath = '/api/BuildCase/SaveBuildCase';\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiBuildCaseSaveBuildCasePost$Plain()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiBuildCaseSaveBuildCasePost$Plain$Response(params, context) {\n    return apiBuildCaseSaveBuildCasePost$Plain(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiBuildCaseSaveBuildCasePost$Plain$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiBuildCaseSaveBuildCasePost$Plain(params, context) {\n    return this.apiBuildCaseSaveBuildCasePost$Plain$Response(params, context).pipe(map(r => r.body));\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiBuildCaseSaveBuildCasePost$Json()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiBuildCaseSaveBuildCasePost$Json$Response(params, context) {\n    return apiBuildCaseSaveBuildCasePost$Json(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiBuildCaseSaveBuildCasePost$Json$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiBuildCaseSaveBuildCasePost$Json(params, context) {\n    return this.apiBuildCaseSaveBuildCasePost$Json$Response(params, context).pipe(map(r => r.body));\n  }\n  apiGetAllBuildCaseForSelectPost$Json(params, context) {\n    return this.apiGetAllBuildCaseForSelectPost$Json$Response(params, context).pipe(map(r => r.body));\n  }\n  apiGetAllBuildCaseForSelectPost$Json$Response(params, context) {\n    return this.apiGetAllBuildCaseForSelectPost$Json(this.http, this.rootUrl, params, context);\n  }\n  static {\n    this.ɵfac = function BuildCaseService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || BuildCaseService)(i0.ɵɵinject(i1.ApiConfiguration), i0.ɵɵinject(i2.HttpClient));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: BuildCaseService,\n      factory: BuildCaseService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["map", "BaseService", "apiBuildCaseDeleteBuildCasePost$Json", "apiBuildCaseDeleteBuildCasePost$Plain", "apiBuildCaseGetAllBuildCasePost$Json", "apiBuildCaseGetAllBuildCasePost$Plain", "apiBuildCaseGetAnnouncementPost$Json", "apiBuildCaseGetAnnouncementPost$Plain", "apiBuildCaseGetBuildCaseByIdPost$Json", "apiBuildCaseGetBuildCaseByIdPost$Plain", "apiBuildCaseGetBuildCaseFilePost$Json", "apiBuildCaseGetBuildCaseFilePost$Plain", "apiBuildCaseGetBuildCaseListPost$Json", "apiBuildCaseGetBuildCaseListPost$Plain", "apiBuildCaseGetDisclaimerPost$Json", "apiBuildCaseGetDisclaimerPost$Plain", "apiBuildCaseGetHouseAndFloorByBuildCaseIdPost$Json", "apiBuildCaseGetHouseAndFloorByBuildCaseIdPost$Plain", "apiBuildCaseGetSystemInstructionPost$Json", "apiBuildCaseGetSystemInstructionPost$Plain", "apiBuildCaseGetUserBuildCasePost$Json", "apiBuildCaseGetUserBuildCasePost$Plain", "apiBuildCaseSaveBuildCasePost$Json", "apiBuildCaseSaveBuildCasePost$Plain", "BuildCaseService", "constructor", "config", "http", "ApiBuildCaseGetBuildCaseListPostPath", "apiBuildCaseGetBuildCaseListPost$Plain$Response", "params", "context", "rootUrl", "pipe", "r", "body", "apiBuildCaseGetBuildCaseListPost$Json$Response", "ApiBuildCaseGetAnnouncementPostPath", "apiBuildCaseGetAnnouncementPost$Plain$Response", "apiBuildCaseGetAnnouncementPost$Json$Response", "ApiBuildCaseGetBuildCaseFilePostPath", "apiBuildCaseGetBuildCaseFilePost$Plain$Response", "apiBuildCaseGetBuildCaseFilePost$Json$Response", "ApiBuildCaseGetDisclaimerPostPath", "apiBuildCaseGetDisclaimerPost$Plain$Response", "apiBuildCaseGetDisclaimerPost$Json$Response", "ApiBuildCaseGetSystemInstructionPostPath", "apiBuildCaseGetSystemInstructionPost$Plain$Response", "apiBuildCaseGetSystemInstructionPost$Json$Response", "ApiBuildCaseGetHouseAndFloorByBuildCaseIdPostPath", "apiBuildCaseGetHouseAndFloorByBuildCaseIdPost$Plain$Response", "apiBuildCaseGetHouseAndFloorByBuildCaseIdPost$Json$Response", "ApiBuildCaseGetUserBuildCasePostPath", "apiBuildCaseGetUserBuildCasePost$Plain$Response", "apiBuildCaseGetUserBuildCasePost$Json$Response", "ApiBuildCaseGetAllBuildCasePostPath", "apiBuildCaseGetAllBuildCasePost$Plain$Response", "apiBuildCaseGetAllBuildCasePost$Json$Response", "ApiBuildCaseDeleteBuildCasePostPath", "apiBuildCaseDeleteBuildCasePost$Plain$Response", "apiBuildCaseDeleteBuildCasePost$Json$Response", "ApiBuildCaseGetBuildCaseByIdPostPath", "apiBuildCaseGetBuildCaseByIdPost$Plain$Response", "apiBuildCaseGetBuildCaseByIdPost$Json$Response", "ApiBuildCaseSaveBuildCasePostPath", "apiBuildCaseSaveBuildCasePost$Plain$Response", "apiBuildCaseSaveBuildCasePost$Json$Response", "apiGetAllBuildCaseForSelectPost$Json", "apiGetAllBuildCaseForSelectPost$Json$Response", "i0", "ɵɵinject", "i1", "ApiConfiguration", "i2", "HttpClient", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\Documents\\jeansalechange\\adminFront\\src\\services\\api\\services\\build-case.service.ts"], "sourcesContent": ["/* tslint:disable */\r\n/* eslint-disable */\r\nimport { HttpClient, HttpContext } from '@angular/common/http';\r\nimport { Injectable } from '@angular/core';\r\nimport { Observable } from 'rxjs';\r\nimport { map } from 'rxjs/operators';\r\n\r\nimport { BaseService } from '../base-service';\r\nimport { ApiConfiguration } from '../api-configuration';\r\nimport { StrictHttpResponse } from '../strict-http-response';\r\n\r\nimport { apiBuildCaseDeleteBuildCasePost$Json } from '../fn/build-case/api-build-case-delete-build-case-post-json';\r\nimport { ApiBuildCaseDeleteBuildCasePost$Json$Params } from '../fn/build-case/api-build-case-delete-build-case-post-json';\r\nimport { apiBuildCaseDeleteBuildCasePost$Plain } from '../fn/build-case/api-build-case-delete-build-case-post-plain';\r\nimport { ApiBuildCaseDeleteBuildCasePost$Plain$Params } from '../fn/build-case/api-build-case-delete-build-case-post-plain';\r\nimport { apiBuildCaseGetAllBuildCasePost$Json } from '../fn/build-case/api-build-case-get-all-build-case-post-json';\r\nimport { ApiBuildCaseGetAllBuildCasePost$Json$Params } from '../fn/build-case/api-build-case-get-all-build-case-post-json';\r\nimport { apiBuildCaseGetAllBuildCasePost$Plain } from '../fn/build-case/api-build-case-get-all-build-case-post-plain';\r\nimport { ApiBuildCaseGetAllBuildCasePost$Plain$Params } from '../fn/build-case/api-build-case-get-all-build-case-post-plain';\r\nimport { apiBuildCaseGetAnnouncementPost$Json } from '../fn/build-case/api-build-case-get-announcement-post-json';\r\nimport { ApiBuildCaseGetAnnouncementPost$Json$Params } from '../fn/build-case/api-build-case-get-announcement-post-json';\r\nimport { apiBuildCaseGetAnnouncementPost$Plain } from '../fn/build-case/api-build-case-get-announcement-post-plain';\r\nimport { ApiBuildCaseGetAnnouncementPost$Plain$Params } from '../fn/build-case/api-build-case-get-announcement-post-plain';\r\nimport { apiBuildCaseGetBuildCaseByIdPost$Json } from '../fn/build-case/api-build-case-get-build-case-by-id-post-json';\r\nimport { ApiBuildCaseGetBuildCaseByIdPost$Json$Params } from '../fn/build-case/api-build-case-get-build-case-by-id-post-json';\r\nimport { apiBuildCaseGetBuildCaseByIdPost$Plain } from '../fn/build-case/api-build-case-get-build-case-by-id-post-plain';\r\nimport { ApiBuildCaseGetBuildCaseByIdPost$Plain$Params } from '../fn/build-case/api-build-case-get-build-case-by-id-post-plain';\r\nimport { apiBuildCaseGetBuildCaseFilePost$Json } from '../fn/build-case/api-build-case-get-build-case-file-post-json';\r\nimport { ApiBuildCaseGetBuildCaseFilePost$Json$Params } from '../fn/build-case/api-build-case-get-build-case-file-post-json';\r\nimport { apiBuildCaseGetBuildCaseFilePost$Plain } from '../fn/build-case/api-build-case-get-build-case-file-post-plain';\r\nimport { ApiBuildCaseGetBuildCaseFilePost$Plain$Params } from '../fn/build-case/api-build-case-get-build-case-file-post-plain';\r\nimport { apiBuildCaseGetBuildCaseListPost$Json } from '../fn/build-case/api-build-case-get-build-case-list-post-json';\r\nimport { ApiBuildCaseGetBuildCaseListPost$Json$Params } from '../fn/build-case/api-build-case-get-build-case-list-post-json';\r\nimport { apiBuildCaseGetBuildCaseListPost$Plain } from '../fn/build-case/api-build-case-get-build-case-list-post-plain';\r\nimport { ApiBuildCaseGetBuildCaseListPost$Plain$Params } from '../fn/build-case/api-build-case-get-build-case-list-post-plain';\r\nimport { apiBuildCaseGetDisclaimerPost$Json } from '../fn/build-case/api-build-case-get-disclaimer-post-json';\r\nimport { ApiBuildCaseGetDisclaimerPost$Json$Params } from '../fn/build-case/api-build-case-get-disclaimer-post-json';\r\nimport { apiBuildCaseGetDisclaimerPost$Plain } from '../fn/build-case/api-build-case-get-disclaimer-post-plain';\r\nimport { ApiBuildCaseGetDisclaimerPost$Plain$Params } from '../fn/build-case/api-build-case-get-disclaimer-post-plain';\r\nimport { apiBuildCaseGetHouseAndFloorByBuildCaseIdPost$Json } from '../fn/build-case/api-build-case-get-house-and-floor-by-build-case-id-post-json';\r\nimport { ApiBuildCaseGetHouseAndFloorByBuildCaseIdPost$Json$Params } from '../fn/build-case/api-build-case-get-house-and-floor-by-build-case-id-post-json';\r\nimport { apiBuildCaseGetHouseAndFloorByBuildCaseIdPost$Plain } from '../fn/build-case/api-build-case-get-house-and-floor-by-build-case-id-post-plain';\r\nimport { ApiBuildCaseGetHouseAndFloorByBuildCaseIdPost$Plain$Params } from '../fn/build-case/api-build-case-get-house-and-floor-by-build-case-id-post-plain';\r\nimport { apiBuildCaseGetSystemInstructionPost$Json } from '../fn/build-case/api-build-case-get-system-instruction-post-json';\r\nimport { ApiBuildCaseGetSystemInstructionPost$Json$Params } from '../fn/build-case/api-build-case-get-system-instruction-post-json';\r\nimport { apiBuildCaseGetSystemInstructionPost$Plain } from '../fn/build-case/api-build-case-get-system-instruction-post-plain';\r\nimport { ApiBuildCaseGetSystemInstructionPost$Plain$Params } from '../fn/build-case/api-build-case-get-system-instruction-post-plain';\r\nimport { apiBuildCaseGetUserBuildCasePost$Json } from '../fn/build-case/api-build-case-get-user-build-case-post-json';\r\nimport { ApiBuildCaseGetUserBuildCasePost$Json$Params } from '../fn/build-case/api-build-case-get-user-build-case-post-json';\r\nimport { apiBuildCaseGetUserBuildCasePost$Plain } from '../fn/build-case/api-build-case-get-user-build-case-post-plain';\r\nimport { ApiBuildCaseGetUserBuildCasePost$Plain$Params } from '../fn/build-case/api-build-case-get-user-build-case-post-plain';\r\nimport { apiBuildCaseSaveBuildCasePost$Json } from '../fn/build-case/api-build-case-save-build-case-post-json';\r\nimport { ApiBuildCaseSaveBuildCasePost$Json$Params } from '../fn/build-case/api-build-case-save-build-case-post-json';\r\nimport { apiBuildCaseSaveBuildCasePost$Plain } from '../fn/build-case/api-build-case-save-build-case-post-plain';\r\nimport { ApiBuildCaseSaveBuildCasePost$Plain$Params } from '../fn/build-case/api-build-case-save-build-case-post-plain';\r\nimport { BuildCaseGetFileResponeListResponseBase } from '../models/build-case-get-file-respone-list-response-base';\r\nimport { BuildCaseGetListReponseListResponseBase } from '../models/build-case-get-list-reponse-list-response-base';\r\nimport { BuildCaseGetListReponseResponseBase } from '../models/build-case-get-list-reponse-response-base';\r\nimport { GetHouseAndFloorByBuildCaseIdResListResponseBase } from '../models/get-house-and-floor-by-build-case-id-res-list-response-base';\r\nimport { StringResponseBase } from '../models/string-response-base';\r\n\r\n@Injectable({ providedIn: 'root' })\r\nexport class BuildCaseService extends BaseService {\r\n  constructor(config: ApiConfiguration, http: HttpClient) {\r\n    super(config, http);\r\n  }\r\n\r\n  /** Path part for operation `apiBuildCaseGetBuildCaseListPost()` */\r\n  static readonly ApiBuildCaseGetBuildCaseListPostPath = '/api/BuildCase/GetBuildCaseList';\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiBuildCaseGetBuildCaseListPost$Plain()` instead.\r\n   *\r\n   * This method doesn't expect any request body.\r\n   */\r\n  apiBuildCaseGetBuildCaseListPost$Plain$Response(params?: ApiBuildCaseGetBuildCaseListPost$Plain$Params, context?: HttpContext): Observable<StrictHttpResponse<BuildCaseGetListReponseListResponseBase>> {\r\n    return apiBuildCaseGetBuildCaseListPost$Plain(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiBuildCaseGetBuildCaseListPost$Plain$Response()` instead.\r\n   *\r\n   * This method doesn't expect any request body.\r\n   */\r\n  apiBuildCaseGetBuildCaseListPost$Plain(params?: ApiBuildCaseGetBuildCaseListPost$Plain$Params, context?: HttpContext): Observable<BuildCaseGetListReponseListResponseBase> {\r\n    return this.apiBuildCaseGetBuildCaseListPost$Plain$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<BuildCaseGetListReponseListResponseBase>): BuildCaseGetListReponseListResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiBuildCaseGetBuildCaseListPost$Json()` instead.\r\n   *\r\n   * This method doesn't expect any request body.\r\n   */\r\n  apiBuildCaseGetBuildCaseListPost$Json$Response(params?: ApiBuildCaseGetBuildCaseListPost$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<BuildCaseGetListReponseListResponseBase>> {\r\n    return apiBuildCaseGetBuildCaseListPost$Json(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiBuildCaseGetBuildCaseListPost$Json$Response()` instead.\r\n   *\r\n   * This method doesn't expect any request body.\r\n   */\r\n  apiBuildCaseGetBuildCaseListPost$Json(params?: ApiBuildCaseGetBuildCaseListPost$Json$Params, context?: HttpContext): Observable<BuildCaseGetListReponseListResponseBase> {\r\n    return this.apiBuildCaseGetBuildCaseListPost$Json$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<BuildCaseGetListReponseListResponseBase>): BuildCaseGetListReponseListResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /** Path part for operation `apiBuildCaseGetAnnouncementPost()` */\r\n  static readonly ApiBuildCaseGetAnnouncementPostPath = '/api/BuildCase/GetAnnouncement';\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiBuildCaseGetAnnouncementPost$Plain()` instead.\r\n   *\r\n   * This method doesn't expect any request body.\r\n   */\r\n  apiBuildCaseGetAnnouncementPost$Plain$Response(params?: ApiBuildCaseGetAnnouncementPost$Plain$Params, context?: HttpContext): Observable<StrictHttpResponse<StringResponseBase>> {\r\n    return apiBuildCaseGetAnnouncementPost$Plain(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiBuildCaseGetAnnouncementPost$Plain$Response()` instead.\r\n   *\r\n   * This method doesn't expect any request body.\r\n   */\r\n  apiBuildCaseGetAnnouncementPost$Plain(params?: ApiBuildCaseGetAnnouncementPost$Plain$Params, context?: HttpContext): Observable<StringResponseBase> {\r\n    return this.apiBuildCaseGetAnnouncementPost$Plain$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<StringResponseBase>): StringResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiBuildCaseGetAnnouncementPost$Json()` instead.\r\n   *\r\n   * This method doesn't expect any request body.\r\n   */\r\n  apiBuildCaseGetAnnouncementPost$Json$Response(params?: ApiBuildCaseGetAnnouncementPost$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<StringResponseBase>> {\r\n    return apiBuildCaseGetAnnouncementPost$Json(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiBuildCaseGetAnnouncementPost$Json$Response()` instead.\r\n   *\r\n   * This method doesn't expect any request body.\r\n   */\r\n  apiBuildCaseGetAnnouncementPost$Json(params?: ApiBuildCaseGetAnnouncementPost$Json$Params, context?: HttpContext): Observable<StringResponseBase> {\r\n    return this.apiBuildCaseGetAnnouncementPost$Json$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<StringResponseBase>): StringResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /** Path part for operation `apiBuildCaseGetBuildCaseFilePost()` */\r\n  static readonly ApiBuildCaseGetBuildCaseFilePostPath = '/api/BuildCase/GetBuildCaseFile';\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiBuildCaseGetBuildCaseFilePost$Plain()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiBuildCaseGetBuildCaseFilePost$Plain$Response(params?: ApiBuildCaseGetBuildCaseFilePost$Plain$Params, context?: HttpContext): Observable<StrictHttpResponse<BuildCaseGetFileResponeListResponseBase>> {\r\n    return apiBuildCaseGetBuildCaseFilePost$Plain(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiBuildCaseGetBuildCaseFilePost$Plain$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiBuildCaseGetBuildCaseFilePost$Plain(params?: ApiBuildCaseGetBuildCaseFilePost$Plain$Params, context?: HttpContext): Observable<BuildCaseGetFileResponeListResponseBase> {\r\n    return this.apiBuildCaseGetBuildCaseFilePost$Plain$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<BuildCaseGetFileResponeListResponseBase>): BuildCaseGetFileResponeListResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiBuildCaseGetBuildCaseFilePost$Json()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiBuildCaseGetBuildCaseFilePost$Json$Response(params?: ApiBuildCaseGetBuildCaseFilePost$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<BuildCaseGetFileResponeListResponseBase>> {\r\n    return apiBuildCaseGetBuildCaseFilePost$Json(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiBuildCaseGetBuildCaseFilePost$Json$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiBuildCaseGetBuildCaseFilePost$Json(params?: ApiBuildCaseGetBuildCaseFilePost$Json$Params, context?: HttpContext): Observable<BuildCaseGetFileResponeListResponseBase> {\r\n    return this.apiBuildCaseGetBuildCaseFilePost$Json$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<BuildCaseGetFileResponeListResponseBase>): BuildCaseGetFileResponeListResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /** Path part for operation `apiBuildCaseGetDisclaimerPost()` */\r\n  static readonly ApiBuildCaseGetDisclaimerPostPath = '/api/BuildCase/GetDisclaimer';\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiBuildCaseGetDisclaimerPost$Plain()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiBuildCaseGetDisclaimerPost$Plain$Response(params?: ApiBuildCaseGetDisclaimerPost$Plain$Params, context?: HttpContext): Observable<StrictHttpResponse<StringResponseBase>> {\r\n    return apiBuildCaseGetDisclaimerPost$Plain(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiBuildCaseGetDisclaimerPost$Plain$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiBuildCaseGetDisclaimerPost$Plain(params?: ApiBuildCaseGetDisclaimerPost$Plain$Params, context?: HttpContext): Observable<StringResponseBase> {\r\n    return this.apiBuildCaseGetDisclaimerPost$Plain$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<StringResponseBase>): StringResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiBuildCaseGetDisclaimerPost$Json()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiBuildCaseGetDisclaimerPost$Json$Response(params?: ApiBuildCaseGetDisclaimerPost$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<StringResponseBase>> {\r\n    return apiBuildCaseGetDisclaimerPost$Json(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiBuildCaseGetDisclaimerPost$Json$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiBuildCaseGetDisclaimerPost$Json(params?: ApiBuildCaseGetDisclaimerPost$Json$Params, context?: HttpContext): Observable<StringResponseBase> {\r\n    return this.apiBuildCaseGetDisclaimerPost$Json$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<StringResponseBase>): StringResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /** Path part for operation `apiBuildCaseGetSystemInstructionPost()` */\r\n  static readonly ApiBuildCaseGetSystemInstructionPostPath = '/api/BuildCase/GetSystemInstruction';\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiBuildCaseGetSystemInstructionPost$Plain()` instead.\r\n   *\r\n   * This method doesn't expect any request body.\r\n   */\r\n  apiBuildCaseGetSystemInstructionPost$Plain$Response(params?: ApiBuildCaseGetSystemInstructionPost$Plain$Params, context?: HttpContext): Observable<StrictHttpResponse<StringResponseBase>> {\r\n    return apiBuildCaseGetSystemInstructionPost$Plain(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiBuildCaseGetSystemInstructionPost$Plain$Response()` instead.\r\n   *\r\n   * This method doesn't expect any request body.\r\n   */\r\n  apiBuildCaseGetSystemInstructionPost$Plain(params?: ApiBuildCaseGetSystemInstructionPost$Plain$Params, context?: HttpContext): Observable<StringResponseBase> {\r\n    return this.apiBuildCaseGetSystemInstructionPost$Plain$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<StringResponseBase>): StringResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiBuildCaseGetSystemInstructionPost$Json()` instead.\r\n   *\r\n   * This method doesn't expect any request body.\r\n   */\r\n  apiBuildCaseGetSystemInstructionPost$Json$Response(params?: ApiBuildCaseGetSystemInstructionPost$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<StringResponseBase>> {\r\n    return apiBuildCaseGetSystemInstructionPost$Json(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiBuildCaseGetSystemInstructionPost$Json$Response()` instead.\r\n   *\r\n   * This method doesn't expect any request body.\r\n   */\r\n  apiBuildCaseGetSystemInstructionPost$Json(params?: ApiBuildCaseGetSystemInstructionPost$Json$Params, context?: HttpContext): Observable<StringResponseBase> {\r\n    return this.apiBuildCaseGetSystemInstructionPost$Json$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<StringResponseBase>): StringResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /** Path part for operation `apiBuildCaseGetHouseAndFloorByBuildCaseIdPost()` */\r\n  static readonly ApiBuildCaseGetHouseAndFloorByBuildCaseIdPostPath = '/api/BuildCase/GetHouseAndFloorByBuildCaseId';\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiBuildCaseGetHouseAndFloorByBuildCaseIdPost$Plain()` instead.\r\n   *\r\n   * This method doesn't expect any request body.\r\n   */\r\n  apiBuildCaseGetHouseAndFloorByBuildCaseIdPost$Plain$Response(params?: ApiBuildCaseGetHouseAndFloorByBuildCaseIdPost$Plain$Params, context?: HttpContext): Observable<StrictHttpResponse<GetHouseAndFloorByBuildCaseIdResListResponseBase>> {\r\n    return apiBuildCaseGetHouseAndFloorByBuildCaseIdPost$Plain(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiBuildCaseGetHouseAndFloorByBuildCaseIdPost$Plain$Response()` instead.\r\n   *\r\n   * This method doesn't expect any request body.\r\n   */\r\n  apiBuildCaseGetHouseAndFloorByBuildCaseIdPost$Plain(params?: ApiBuildCaseGetHouseAndFloorByBuildCaseIdPost$Plain$Params, context?: HttpContext): Observable<GetHouseAndFloorByBuildCaseIdResListResponseBase> {\r\n    return this.apiBuildCaseGetHouseAndFloorByBuildCaseIdPost$Plain$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<GetHouseAndFloorByBuildCaseIdResListResponseBase>): GetHouseAndFloorByBuildCaseIdResListResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiBuildCaseGetHouseAndFloorByBuildCaseIdPost$Json()` instead.\r\n   *\r\n   * This method doesn't expect any request body.\r\n   */\r\n  apiBuildCaseGetHouseAndFloorByBuildCaseIdPost$Json$Response(params?: ApiBuildCaseGetHouseAndFloorByBuildCaseIdPost$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<GetHouseAndFloorByBuildCaseIdResListResponseBase>> {\r\n    return apiBuildCaseGetHouseAndFloorByBuildCaseIdPost$Json(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiBuildCaseGetHouseAndFloorByBuildCaseIdPost$Json$Response()` instead.\r\n   *\r\n   * This method doesn't expect any request body.\r\n   */\r\n  apiBuildCaseGetHouseAndFloorByBuildCaseIdPost$Json(params?: ApiBuildCaseGetHouseAndFloorByBuildCaseIdPost$Json$Params, context?: HttpContext): Observable<GetHouseAndFloorByBuildCaseIdResListResponseBase> {\r\n    return this.apiBuildCaseGetHouseAndFloorByBuildCaseIdPost$Json$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<GetHouseAndFloorByBuildCaseIdResListResponseBase>): GetHouseAndFloorByBuildCaseIdResListResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /** Path part for operation `apiBuildCaseGetUserBuildCasePost()` */\r\n  static readonly ApiBuildCaseGetUserBuildCasePostPath = '/api/BuildCase/GetUserBuildCase';\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiBuildCaseGetUserBuildCasePost$Plain()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiBuildCaseGetUserBuildCasePost$Plain$Response(params?: ApiBuildCaseGetUserBuildCasePost$Plain$Params, context?: HttpContext): Observable<StrictHttpResponse<BuildCaseGetListReponseListResponseBase>> {\r\n    return apiBuildCaseGetUserBuildCasePost$Plain(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiBuildCaseGetUserBuildCasePost$Plain$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiBuildCaseGetUserBuildCasePost$Plain(params?: ApiBuildCaseGetUserBuildCasePost$Plain$Params, context?: HttpContext): Observable<BuildCaseGetListReponseListResponseBase> {\r\n    return this.apiBuildCaseGetUserBuildCasePost$Plain$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<BuildCaseGetListReponseListResponseBase>): BuildCaseGetListReponseListResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiBuildCaseGetUserBuildCasePost$Json()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiBuildCaseGetUserBuildCasePost$Json$Response(params?: ApiBuildCaseGetUserBuildCasePost$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<BuildCaseGetListReponseListResponseBase>> {\r\n    return apiBuildCaseGetUserBuildCasePost$Json(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiBuildCaseGetUserBuildCasePost$Json$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiBuildCaseGetUserBuildCasePost$Json(params?: ApiBuildCaseGetUserBuildCasePost$Json$Params, context?: HttpContext): Observable<BuildCaseGetListReponseListResponseBase> {\r\n    return this.apiBuildCaseGetUserBuildCasePost$Json$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<BuildCaseGetListReponseListResponseBase>): BuildCaseGetListReponseListResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /** Path part for operation `apiBuildCaseGetAllBuildCasePost()` */\r\n  static readonly ApiBuildCaseGetAllBuildCasePostPath = '/api/BuildCase/GetAllBuildCase';\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiBuildCaseGetAllBuildCasePost$Plain()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiBuildCaseGetAllBuildCasePost$Plain$Response(params?: ApiBuildCaseGetAllBuildCasePost$Plain$Params, context?: HttpContext): Observable<StrictHttpResponse<BuildCaseGetListReponseListResponseBase>> {\r\n    return apiBuildCaseGetAllBuildCasePost$Plain(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiBuildCaseGetAllBuildCasePost$Plain$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiBuildCaseGetAllBuildCasePost$Plain(params?: ApiBuildCaseGetAllBuildCasePost$Plain$Params, context?: HttpContext): Observable<BuildCaseGetListReponseListResponseBase> {\r\n    return this.apiBuildCaseGetAllBuildCasePost$Plain$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<BuildCaseGetListReponseListResponseBase>): BuildCaseGetListReponseListResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiBuildCaseGetAllBuildCasePost$Json()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiBuildCaseGetAllBuildCasePost$Json$Response(params?: ApiBuildCaseGetAllBuildCasePost$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<BuildCaseGetListReponseListResponseBase>> {\r\n    return apiBuildCaseGetAllBuildCasePost$Json(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiBuildCaseGetAllBuildCasePost$Json$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiBuildCaseGetAllBuildCasePost$Json(params?: ApiBuildCaseGetAllBuildCasePost$Json$Params, context?: HttpContext): Observable<BuildCaseGetListReponseListResponseBase> {\r\n    return this.apiBuildCaseGetAllBuildCasePost$Json$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<BuildCaseGetListReponseListResponseBase>): BuildCaseGetListReponseListResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /** Path part for operation `apiBuildCaseDeleteBuildCasePost()` */\r\n  static readonly ApiBuildCaseDeleteBuildCasePostPath = '/api/BuildCase/DeleteBuildCase';\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiBuildCaseDeleteBuildCasePost$Plain()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiBuildCaseDeleteBuildCasePost$Plain$Response(params?: ApiBuildCaseDeleteBuildCasePost$Plain$Params, context?: HttpContext): Observable<StrictHttpResponse<StringResponseBase>> {\r\n    return apiBuildCaseDeleteBuildCasePost$Plain(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiBuildCaseDeleteBuildCasePost$Plain$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiBuildCaseDeleteBuildCasePost$Plain(params?: ApiBuildCaseDeleteBuildCasePost$Plain$Params, context?: HttpContext): Observable<StringResponseBase> {\r\n    return this.apiBuildCaseDeleteBuildCasePost$Plain$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<StringResponseBase>): StringResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiBuildCaseDeleteBuildCasePost$Json()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiBuildCaseDeleteBuildCasePost$Json$Response(params?: ApiBuildCaseDeleteBuildCasePost$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<StringResponseBase>> {\r\n    return apiBuildCaseDeleteBuildCasePost$Json(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiBuildCaseDeleteBuildCasePost$Json$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiBuildCaseDeleteBuildCasePost$Json(params?: ApiBuildCaseDeleteBuildCasePost$Json$Params, context?: HttpContext): Observable<StringResponseBase> {\r\n    return this.apiBuildCaseDeleteBuildCasePost$Json$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<StringResponseBase>): StringResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /** Path part for operation `apiBuildCaseGetBuildCaseByIdPost()` */\r\n  static readonly ApiBuildCaseGetBuildCaseByIdPostPath = '/api/BuildCase/GetBuildCaseByID';\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiBuildCaseGetBuildCaseByIdPost$Plain()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiBuildCaseGetBuildCaseByIdPost$Plain$Response(params?: ApiBuildCaseGetBuildCaseByIdPost$Plain$Params, context?: HttpContext): Observable<StrictHttpResponse<BuildCaseGetListReponseResponseBase>> {\r\n    return apiBuildCaseGetBuildCaseByIdPost$Plain(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiBuildCaseGetBuildCaseByIdPost$Plain$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiBuildCaseGetBuildCaseByIdPost$Plain(params?: ApiBuildCaseGetBuildCaseByIdPost$Plain$Params, context?: HttpContext): Observable<BuildCaseGetListReponseResponseBase> {\r\n    return this.apiBuildCaseGetBuildCaseByIdPost$Plain$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<BuildCaseGetListReponseResponseBase>): BuildCaseGetListReponseResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiBuildCaseGetBuildCaseByIdPost$Json()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiBuildCaseGetBuildCaseByIdPost$Json$Response(params?: ApiBuildCaseGetBuildCaseByIdPost$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<BuildCaseGetListReponseResponseBase>> {\r\n    return apiBuildCaseGetBuildCaseByIdPost$Json(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiBuildCaseGetBuildCaseByIdPost$Json$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiBuildCaseGetBuildCaseByIdPost$Json(params?: ApiBuildCaseGetBuildCaseByIdPost$Json$Params, context?: HttpContext): Observable<BuildCaseGetListReponseResponseBase> {\r\n    return this.apiBuildCaseGetBuildCaseByIdPost$Json$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<BuildCaseGetListReponseResponseBase>): BuildCaseGetListReponseResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /** Path part for operation `apiBuildCaseSaveBuildCasePost()` */\r\n  static readonly ApiBuildCaseSaveBuildCasePostPath = '/api/BuildCase/SaveBuildCase';\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiBuildCaseSaveBuildCasePost$Plain()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiBuildCaseSaveBuildCasePost$Plain$Response(params?: ApiBuildCaseSaveBuildCasePost$Plain$Params, context?: HttpContext): Observable<StrictHttpResponse<StringResponseBase>> {\r\n    return apiBuildCaseSaveBuildCasePost$Plain(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiBuildCaseSaveBuildCasePost$Plain$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiBuildCaseSaveBuildCasePost$Plain(params?: ApiBuildCaseSaveBuildCasePost$Plain$Params, context?: HttpContext): Observable<StringResponseBase> {\r\n    return this.apiBuildCaseSaveBuildCasePost$Plain$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<StringResponseBase>): StringResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiBuildCaseSaveBuildCasePost$Json()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiBuildCaseSaveBuildCasePost$Json$Response(params?: ApiBuildCaseSaveBuildCasePost$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<StringResponseBase>> {\r\n    return apiBuildCaseSaveBuildCasePost$Json(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiBuildCaseSaveBuildCasePost$Json$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiBuildCaseSaveBuildCasePost$Json(params?: ApiBuildCaseSaveBuildCasePost$Json$Params, context?: HttpContext): Observable<StringResponseBase> {\r\n    return this.apiBuildCaseSaveBuildCasePost$Json$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<StringResponseBase>): StringResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  apiGetAllBuildCaseForSelectPost$Json(params?: ApiBuildCaseGetAllBuildCasePost$Json$Params, context?: HttpContext): Observable<BuildCaseGetListReponseListResponseBase> {\r\n    return this.apiGetAllBuildCaseForSelectPost$Json$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<BuildCaseGetListReponseListResponseBase>): BuildCaseGetListReponseListResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  apiGetAllBuildCaseForSelectPost$Json$Response(params?: ApiBuildCaseGetAllBuildCasePost$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<BuildCaseGetListReponseListResponseBase>> {\r\n    return this.apiGetAllBuildCaseForSelectPost$Json(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n}\r\n"], "mappings": "AAKA,SAASA,GAAG,QAAQ,gBAAgB;AAEpC,SAASC,WAAW,QAAQ,iBAAiB;AAI7C,SAASC,oCAAoC,QAAQ,6DAA6D;AAElH,SAASC,qCAAqC,QAAQ,8DAA8D;AAEpH,SAASC,oCAAoC,QAAQ,8DAA8D;AAEnH,SAASC,qCAAqC,QAAQ,+DAA+D;AAErH,SAASC,oCAAoC,QAAQ,4DAA4D;AAEjH,SAASC,qCAAqC,QAAQ,6DAA6D;AAEnH,SAASC,qCAAqC,QAAQ,gEAAgE;AAEtH,SAASC,sCAAsC,QAAQ,iEAAiE;AAExH,SAASC,qCAAqC,QAAQ,+DAA+D;AAErH,SAASC,sCAAsC,QAAQ,gEAAgE;AAEvH,SAASC,qCAAqC,QAAQ,+DAA+D;AAErH,SAASC,sCAAsC,QAAQ,gEAAgE;AAEvH,SAASC,kCAAkC,QAAQ,0DAA0D;AAE7G,SAASC,mCAAmC,QAAQ,2DAA2D;AAE/G,SAASC,kDAAkD,QAAQ,gFAAgF;AAEnJ,SAASC,mDAAmD,QAAQ,iFAAiF;AAErJ,SAASC,yCAAyC,QAAQ,kEAAkE;AAE5H,SAASC,0CAA0C,QAAQ,mEAAmE;AAE9H,SAASC,qCAAqC,QAAQ,+DAA+D;AAErH,SAASC,sCAAsC,QAAQ,gEAAgE;AAEvH,SAASC,kCAAkC,QAAQ,2DAA2D;AAE9G,SAASC,mCAAmC,QAAQ,4DAA4D;;;;AAShH,OAAM,MAAOC,gBAAiB,SAAQvB,WAAW;EAC/CwB,YAAYC,MAAwB,EAAEC,IAAgB;IACpD,KAAK,CAACD,MAAM,EAAEC,IAAI,CAAC;EACrB;EAEA;;IACgB,KAAAC,oCAAoC,GAAG,iCAAiC;EAAC;EAEzF;;;;;;EAMAC,+CAA+CA,CAACC,MAAsD,EAAEC,OAAqB;IAC3H,OAAOlB,sCAAsC,CAAC,IAAI,CAACc,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EACzF;EAEA;;;;;;EAMAlB,sCAAsCA,CAACiB,MAAsD,EAAEC,OAAqB;IAClH,OAAO,IAAI,CAACF,+CAA+C,CAACC,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CAC/EjC,GAAG,CAAEkC,CAA8D,IAA8CA,CAAC,CAACC,IAAI,CAAC,CACzH;EACH;EAEA;;;;;;EAMAC,8CAA8CA,CAACN,MAAqD,EAAEC,OAAqB;IACzH,OAAOnB,qCAAqC,CAAC,IAAI,CAACe,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EACxF;EAEA;;;;;;EAMAnB,qCAAqCA,CAACkB,MAAqD,EAAEC,OAAqB;IAChH,OAAO,IAAI,CAACK,8CAA8C,CAACN,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CAC9EjC,GAAG,CAAEkC,CAA8D,IAA8CA,CAAC,CAACC,IAAI,CAAC,CACzH;EACH;EAEA;;IACgB,KAAAE,mCAAmC,GAAG,gCAAgC;EAAC;EAEvF;;;;;;EAMAC,8CAA8CA,CAACR,MAAqD,EAAEC,OAAqB;IACzH,OAAOxB,qCAAqC,CAAC,IAAI,CAACoB,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EACxF;EAEA;;;;;;EAMAxB,qCAAqCA,CAACuB,MAAqD,EAAEC,OAAqB;IAChH,OAAO,IAAI,CAACO,8CAA8C,CAACR,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CAC9EjC,GAAG,CAAEkC,CAAyC,IAAyBA,CAAC,CAACC,IAAI,CAAC,CAC/E;EACH;EAEA;;;;;;EAMAI,6CAA6CA,CAACT,MAAoD,EAAEC,OAAqB;IACvH,OAAOzB,oCAAoC,CAAC,IAAI,CAACqB,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EACvF;EAEA;;;;;;EAMAzB,oCAAoCA,CAACwB,MAAoD,EAAEC,OAAqB;IAC9G,OAAO,IAAI,CAACQ,6CAA6C,CAACT,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CAC7EjC,GAAG,CAAEkC,CAAyC,IAAyBA,CAAC,CAACC,IAAI,CAAC,CAC/E;EACH;EAEA;;IACgB,KAAAK,oCAAoC,GAAG,iCAAiC;EAAC;EAEzF;;;;;;EAMAC,+CAA+CA,CAACX,MAAsD,EAAEC,OAAqB;IAC3H,OAAOpB,sCAAsC,CAAC,IAAI,CAACgB,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EACzF;EAEA;;;;;;EAMApB,sCAAsCA,CAACmB,MAAsD,EAAEC,OAAqB;IAClH,OAAO,IAAI,CAACU,+CAA+C,CAACX,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CAC/EjC,GAAG,CAAEkC,CAA8D,IAA8CA,CAAC,CAACC,IAAI,CAAC,CACzH;EACH;EAEA;;;;;;EAMAO,8CAA8CA,CAACZ,MAAqD,EAAEC,OAAqB;IACzH,OAAOrB,qCAAqC,CAAC,IAAI,CAACiB,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EACxF;EAEA;;;;;;EAMArB,qCAAqCA,CAACoB,MAAqD,EAAEC,OAAqB;IAChH,OAAO,IAAI,CAACW,8CAA8C,CAACZ,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CAC9EjC,GAAG,CAAEkC,CAA8D,IAA8CA,CAAC,CAACC,IAAI,CAAC,CACzH;EACH;EAEA;;IACgB,KAAAQ,iCAAiC,GAAG,8BAA8B;EAAC;EAEnF;;;;;;EAMAC,4CAA4CA,CAACd,MAAmD,EAAEC,OAAqB;IACrH,OAAOhB,mCAAmC,CAAC,IAAI,CAACY,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EACtF;EAEA;;;;;;EAMAhB,mCAAmCA,CAACe,MAAmD,EAAEC,OAAqB;IAC5G,OAAO,IAAI,CAACa,4CAA4C,CAACd,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CAC5EjC,GAAG,CAAEkC,CAAyC,IAAyBA,CAAC,CAACC,IAAI,CAAC,CAC/E;EACH;EAEA;;;;;;EAMAU,2CAA2CA,CAACf,MAAkD,EAAEC,OAAqB;IACnH,OAAOjB,kCAAkC,CAAC,IAAI,CAACa,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EACrF;EAEA;;;;;;EAMAjB,kCAAkCA,CAACgB,MAAkD,EAAEC,OAAqB;IAC1G,OAAO,IAAI,CAACc,2CAA2C,CAACf,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CAC3EjC,GAAG,CAAEkC,CAAyC,IAAyBA,CAAC,CAACC,IAAI,CAAC,CAC/E;EACH;EAEA;;IACgB,KAAAW,wCAAwC,GAAG,qCAAqC;EAAC;EAEjG;;;;;;EAMAC,mDAAmDA,CAACjB,MAA0D,EAAEC,OAAqB;IACnI,OAAOZ,0CAA0C,CAAC,IAAI,CAACQ,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EAC7F;EAEA;;;;;;EAMAZ,0CAA0CA,CAACW,MAA0D,EAAEC,OAAqB;IAC1H,OAAO,IAAI,CAACgB,mDAAmD,CAACjB,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CACnFjC,GAAG,CAAEkC,CAAyC,IAAyBA,CAAC,CAACC,IAAI,CAAC,CAC/E;EACH;EAEA;;;;;;EAMAa,kDAAkDA,CAAClB,MAAyD,EAAEC,OAAqB;IACjI,OAAOb,yCAAyC,CAAC,IAAI,CAACS,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EAC5F;EAEA;;;;;;EAMAb,yCAAyCA,CAACY,MAAyD,EAAEC,OAAqB;IACxH,OAAO,IAAI,CAACiB,kDAAkD,CAAClB,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CAClFjC,GAAG,CAAEkC,CAAyC,IAAyBA,CAAC,CAACC,IAAI,CAAC,CAC/E;EACH;EAEA;;IACgB,KAAAc,iDAAiD,GAAG,8CAA8C;EAAC;EAEnH;;;;;;EAMAC,4DAA4DA,CAACpB,MAAmE,EAAEC,OAAqB;IACrJ,OAAOd,mDAAmD,CAAC,IAAI,CAACU,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EACtG;EAEA;;;;;;EAMAd,mDAAmDA,CAACa,MAAmE,EAAEC,OAAqB;IAC5I,OAAO,IAAI,CAACmB,4DAA4D,CAACpB,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CAC5FjC,GAAG,CAAEkC,CAAuE,IAAuDA,CAAC,CAACC,IAAI,CAAC,CAC3I;EACH;EAEA;;;;;;EAMAgB,2DAA2DA,CAACrB,MAAkE,EAAEC,OAAqB;IACnJ,OAAOf,kDAAkD,CAAC,IAAI,CAACW,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EACrG;EAEA;;;;;;EAMAf,kDAAkDA,CAACc,MAAkE,EAAEC,OAAqB;IAC1I,OAAO,IAAI,CAACoB,2DAA2D,CAACrB,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CAC3FjC,GAAG,CAAEkC,CAAuE,IAAuDA,CAAC,CAACC,IAAI,CAAC,CAC3I;EACH;EAEA;;IACgB,KAAAiB,oCAAoC,GAAG,iCAAiC;EAAC;EAEzF;;;;;;EAMAC,+CAA+CA,CAACvB,MAAsD,EAAEC,OAAqB;IAC3H,OAAOV,sCAAsC,CAAC,IAAI,CAACM,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EACzF;EAEA;;;;;;EAMAV,sCAAsCA,CAACS,MAAsD,EAAEC,OAAqB;IAClH,OAAO,IAAI,CAACsB,+CAA+C,CAACvB,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CAC/EjC,GAAG,CAAEkC,CAA8D,IAA8CA,CAAC,CAACC,IAAI,CAAC,CACzH;EACH;EAEA;;;;;;EAMAmB,8CAA8CA,CAACxB,MAAqD,EAAEC,OAAqB;IACzH,OAAOX,qCAAqC,CAAC,IAAI,CAACO,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EACxF;EAEA;;;;;;EAMAX,qCAAqCA,CAACU,MAAqD,EAAEC,OAAqB;IAChH,OAAO,IAAI,CAACuB,8CAA8C,CAACxB,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CAC9EjC,GAAG,CAAEkC,CAA8D,IAA8CA,CAAC,CAACC,IAAI,CAAC,CACzH;EACH;EAEA;;IACgB,KAAAoB,mCAAmC,GAAG,gCAAgC;EAAC;EAEvF;;;;;;EAMAC,8CAA8CA,CAAC1B,MAAqD,EAAEC,OAAqB;IACzH,OAAO1B,qCAAqC,CAAC,IAAI,CAACsB,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EACxF;EAEA;;;;;;EAMA1B,qCAAqCA,CAACyB,MAAqD,EAAEC,OAAqB;IAChH,OAAO,IAAI,CAACyB,8CAA8C,CAAC1B,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CAC9EjC,GAAG,CAAEkC,CAA8D,IAA8CA,CAAC,CAACC,IAAI,CAAC,CACzH;EACH;EAEA;;;;;;EAMAsB,6CAA6CA,CAAC3B,MAAoD,EAAEC,OAAqB;IACvH,OAAO3B,oCAAoC,CAAC,IAAI,CAACuB,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EACvF;EAEA;;;;;;EAMA3B,oCAAoCA,CAAC0B,MAAoD,EAAEC,OAAqB;IAC9G,OAAO,IAAI,CAAC0B,6CAA6C,CAAC3B,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CAC7EjC,GAAG,CAAEkC,CAA8D,IAA8CA,CAAC,CAACC,IAAI,CAAC,CACzH;EACH;EAEA;;IACgB,KAAAuB,mCAAmC,GAAG,gCAAgC;EAAC;EAEvF;;;;;;EAMAC,8CAA8CA,CAAC7B,MAAqD,EAAEC,OAAqB;IACzH,OAAO5B,qCAAqC,CAAC,IAAI,CAACwB,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EACxF;EAEA;;;;;;EAMA5B,qCAAqCA,CAAC2B,MAAqD,EAAEC,OAAqB;IAChH,OAAO,IAAI,CAAC4B,8CAA8C,CAAC7B,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CAC9EjC,GAAG,CAAEkC,CAAyC,IAAyBA,CAAC,CAACC,IAAI,CAAC,CAC/E;EACH;EAEA;;;;;;EAMAyB,6CAA6CA,CAAC9B,MAAoD,EAAEC,OAAqB;IACvH,OAAO7B,oCAAoC,CAAC,IAAI,CAACyB,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EACvF;EAEA;;;;;;EAMA7B,oCAAoCA,CAAC4B,MAAoD,EAAEC,OAAqB;IAC9G,OAAO,IAAI,CAAC6B,6CAA6C,CAAC9B,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CAC7EjC,GAAG,CAAEkC,CAAyC,IAAyBA,CAAC,CAACC,IAAI,CAAC,CAC/E;EACH;EAEA;;IACgB,KAAA0B,oCAAoC,GAAG,iCAAiC;EAAC;EAEzF;;;;;;EAMAC,+CAA+CA,CAAChC,MAAsD,EAAEC,OAAqB;IAC3H,OAAOtB,sCAAsC,CAAC,IAAI,CAACkB,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EACzF;EAEA;;;;;;EAMAtB,sCAAsCA,CAACqB,MAAsD,EAAEC,OAAqB;IAClH,OAAO,IAAI,CAAC+B,+CAA+C,CAAChC,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CAC/EjC,GAAG,CAAEkC,CAA0D,IAA0CA,CAAC,CAACC,IAAI,CAAC,CACjH;EACH;EAEA;;;;;;EAMA4B,8CAA8CA,CAACjC,MAAqD,EAAEC,OAAqB;IACzH,OAAOvB,qCAAqC,CAAC,IAAI,CAACmB,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EACxF;EAEA;;;;;;EAMAvB,qCAAqCA,CAACsB,MAAqD,EAAEC,OAAqB;IAChH,OAAO,IAAI,CAACgC,8CAA8C,CAACjC,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CAC9EjC,GAAG,CAAEkC,CAA0D,IAA0CA,CAAC,CAACC,IAAI,CAAC,CACjH;EACH;EAEA;;IACgB,KAAA6B,iCAAiC,GAAG,8BAA8B;EAAC;EAEnF;;;;;;EAMAC,4CAA4CA,CAACnC,MAAmD,EAAEC,OAAqB;IACrH,OAAOR,mCAAmC,CAAC,IAAI,CAACI,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EACtF;EAEA;;;;;;EAMAR,mCAAmCA,CAACO,MAAmD,EAAEC,OAAqB;IAC5G,OAAO,IAAI,CAACkC,4CAA4C,CAACnC,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CAC5EjC,GAAG,CAAEkC,CAAyC,IAAyBA,CAAC,CAACC,IAAI,CAAC,CAC/E;EACH;EAEA;;;;;;EAMA+B,2CAA2CA,CAACpC,MAAkD,EAAEC,OAAqB;IACnH,OAAOT,kCAAkC,CAAC,IAAI,CAACK,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EACrF;EAEA;;;;;;EAMAT,kCAAkCA,CAACQ,MAAkD,EAAEC,OAAqB;IAC1G,OAAO,IAAI,CAACmC,2CAA2C,CAACpC,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CAC3EjC,GAAG,CAAEkC,CAAyC,IAAyBA,CAAC,CAACC,IAAI,CAAC,CAC/E;EACH;EAEAgC,oCAAoCA,CAACrC,MAAoD,EAAEC,OAAqB;IAC9G,OAAO,IAAI,CAACqC,6CAA6C,CAACtC,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CAC7EjC,GAAG,CAAEkC,CAA8D,IAA8CA,CAAC,CAACC,IAAI,CAAC,CACzH;EACH;EAEAiC,6CAA6CA,CAACtC,MAAoD,EAAEC,OAAqB;IACvH,OAAO,IAAI,CAACoC,oCAAoC,CAAC,IAAI,CAACxC,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EAC5F;;;uCAlhBWP,gBAAgB,EAAA6C,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,gBAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,UAAA;IAAA;EAAA;;;aAAhBlD,gBAAgB;MAAAmD,OAAA,EAAhBnD,gBAAgB,CAAAoD,IAAA;MAAAC,UAAA,EADH;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}