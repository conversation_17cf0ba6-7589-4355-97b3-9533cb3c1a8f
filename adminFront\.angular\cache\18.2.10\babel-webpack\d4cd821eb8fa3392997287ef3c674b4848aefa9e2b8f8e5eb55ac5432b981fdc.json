{"ast": null, "code": "import { identity } from '../util/identity';\nimport { isScheduler } from '../util/isScheduler';\nimport { defer } from './defer';\nimport { scheduleIterable } from '../scheduled/scheduleIterable';\nexport function generate(initialStateOrOptions, condition, iterate, resultSelectorOrScheduler, scheduler) {\n  let resultSelector;\n  let initialState;\n  if (arguments.length === 1) {\n    ({\n      initialState,\n      condition,\n      iterate,\n      resultSelector = identity,\n      scheduler\n    } = initialStateOrOptions);\n  } else {\n    initialState = initialStateOrOptions;\n    if (!resultSelectorOrScheduler || isScheduler(resultSelectorOrScheduler)) {\n      resultSelector = identity;\n      scheduler = resultSelectorOrScheduler;\n    } else {\n      resultSelector = resultSelectorOrScheduler;\n    }\n  }\n  function* gen() {\n    for (let state = initialState; !condition || condition(state); state = iterate(state)) {\n      yield resultSelector(state);\n    }\n  }\n  return defer(scheduler ? () => scheduleIterable(gen(), scheduler) : gen);\n}", "map": {"version": 3, "names": ["identity", "isScheduler", "defer", "scheduleIterable", "generate", "initialStateOrOptions", "condition", "iterate", "resultSelectorOrScheduler", "scheduler", "resultSelector", "initialState", "arguments", "length", "gen", "state"], "sources": ["C:/Users/<USER>/Documents/salechange-product/adminFront/node_modules/rxjs/dist/esm/internal/observable/generate.js"], "sourcesContent": ["import { identity } from '../util/identity';\nimport { isScheduler } from '../util/isScheduler';\nimport { defer } from './defer';\nimport { scheduleIterable } from '../scheduled/scheduleIterable';\nexport function generate(initialStateOrOptions, condition, iterate, resultSelectorOrScheduler, scheduler) {\n    let resultSelector;\n    let initialState;\n    if (arguments.length === 1) {\n        ({\n            initialState,\n            condition,\n            iterate,\n            resultSelector = identity,\n            scheduler,\n        } = initialStateOrOptions);\n    }\n    else {\n        initialState = initialStateOrOptions;\n        if (!resultSelectorOrScheduler || isScheduler(resultSelectorOrScheduler)) {\n            resultSelector = identity;\n            scheduler = resultSelectorOrScheduler;\n        }\n        else {\n            resultSelector = resultSelectorOrScheduler;\n        }\n    }\n    function* gen() {\n        for (let state = initialState; !condition || condition(state); state = iterate(state)) {\n            yield resultSelector(state);\n        }\n    }\n    return defer((scheduler\n        ?\n            () => scheduleIterable(gen(), scheduler)\n        :\n            gen));\n}\n"], "mappings": "AAAA,SAASA,QAAQ,QAAQ,kBAAkB;AAC3C,SAASC,WAAW,QAAQ,qBAAqB;AACjD,SAASC,KAAK,QAAQ,SAAS;AAC/B,SAASC,gBAAgB,QAAQ,+BAA+B;AAChE,OAAO,SAASC,QAAQA,CAACC,qBAAqB,EAAEC,SAAS,EAAEC,OAAO,EAAEC,yBAAyB,EAAEC,SAAS,EAAE;EACtG,IAAIC,cAAc;EAClB,IAAIC,YAAY;EAChB,IAAIC,SAAS,CAACC,MAAM,KAAK,CAAC,EAAE;IACxB,CAAC;MACGF,YAAY;MACZL,SAAS;MACTC,OAAO;MACPG,cAAc,GAAGV,QAAQ;MACzBS;IACJ,CAAC,GAAGJ,qBAAqB;EAC7B,CAAC,MACI;IACDM,YAAY,GAAGN,qBAAqB;IACpC,IAAI,CAACG,yBAAyB,IAAIP,WAAW,CAACO,yBAAyB,CAAC,EAAE;MACtEE,cAAc,GAAGV,QAAQ;MACzBS,SAAS,GAAGD,yBAAyB;IACzC,CAAC,MACI;MACDE,cAAc,GAAGF,yBAAyB;IAC9C;EACJ;EACA,UAAUM,GAAGA,CAAA,EAAG;IACZ,KAAK,IAAIC,KAAK,GAAGJ,YAAY,EAAE,CAACL,SAAS,IAAIA,SAAS,CAACS,KAAK,CAAC,EAAEA,KAAK,GAAGR,OAAO,CAACQ,KAAK,CAAC,EAAE;MACnF,MAAML,cAAc,CAACK,KAAK,CAAC;IAC/B;EACJ;EACA,OAAOb,KAAK,CAAEO,SAAS,GAEf,MAAMN,gBAAgB,CAACW,GAAG,CAAC,CAAC,EAAEL,SAAS,CAAC,GAExCK,GAAI,CAAC;AACjB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}