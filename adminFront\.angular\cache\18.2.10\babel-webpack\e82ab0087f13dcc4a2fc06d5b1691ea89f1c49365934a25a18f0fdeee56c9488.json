{"ast": null, "code": "import { LocalStorageService } from 'src/app/shared/services/local-storage.service';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nexport let LogoutComponent = /*#__PURE__*/(() => {\n  class LogoutComponent {\n    constructor(router) {\n      this.router = router;\n    }\n    ngOnInit() {\n      LocalStorageService.ClearLocalStorage();\n      this.router.navigateByUrl('login');\n    }\n    static {\n      this.ɵfac = function LogoutComponent_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || LogoutComponent)(i0.ɵɵdirectiveInject(i1.Router));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: LogoutComponent,\n        selectors: [[\"ngx-logout\"]],\n        standalone: true,\n        features: [i0.ɵɵStandaloneFeature],\n        decls: 0,\n        vars: 0,\n        template: function LogoutComponent_Template(rf, ctx) {}\n      });\n    }\n  }\n  return LogoutComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}