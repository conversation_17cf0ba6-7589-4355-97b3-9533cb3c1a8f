{"ast": null, "code": "import { <PERSON><PERSON><PERSON><PERSON> } from 'primeng/dom';\nimport { DOCUMENT, isPlatformBrowser, CommonModule } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { inject, PLATFORM_ID, ElementRef, booleanAttribute, Directive, Input, NgModule } from '@angular/core';\n\n/**\n * Focus Trap keeps focus within a certain DOM element while tabbing.\n * @group Components\n */\nclass FocusTrap {\n  /**\n   * When set as true, focus wouldn't be managed.\n   * @group Props\n   */\n  pFocusTrapDisabled = false;\n  platformId = inject(PLATFORM_ID);\n  host = inject(ElementRef);\n  document = inject(DOCUMENT);\n  firstHiddenFocusableElement;\n  lastHiddenFocusableElement;\n  ngOnInit() {\n    if (isPlatformBrowser(this.platformId) && !this.pFocusTrapDisabled) {\n      !this.firstHiddenFocusableElement && !this.lastHiddenFocusableElement && this.createHiddenFocusableElements();\n    }\n  }\n  ngOnChanges(changes) {\n    if (changes.pFocusTrapDisabled && isPlatformBrowser(this.platformId)) {\n      if (changes.pFocusTrapDisabled.currentValue) {\n        this.removeHiddenFocusableElements();\n      } else {\n        this.createHiddenFocusableElements();\n      }\n    }\n  }\n  removeHiddenFocusableElements() {\n    if (this.firstHiddenFocusableElement && this.firstHiddenFocusableElement.parentNode) {\n      this.firstHiddenFocusableElement.parentNode.removeChild(this.firstHiddenFocusableElement);\n    }\n    if (this.lastHiddenFocusableElement && this.lastHiddenFocusableElement.parentNode) {\n      this.lastHiddenFocusableElement.parentNode.removeChild(this.lastHiddenFocusableElement);\n    }\n  }\n  getComputedSelector(selector) {\n    return `:not(.p-hidden-focusable):not([data-p-hidden-focusable=\"true\"])${selector ?? ''}`;\n  }\n  createHiddenFocusableElements() {\n    const tabindex = '0';\n    const createFocusableElement = onFocus => {\n      return DomHandler.createElement('span', {\n        class: 'p-hidden-accessible p-hidden-focusable',\n        tabindex,\n        role: 'presentation',\n        'data-p-hidden-accessible': true,\n        'data-p-hidden-focusable': true,\n        onFocus: onFocus?.bind(this)\n      });\n    };\n    this.firstHiddenFocusableElement = createFocusableElement(this.onFirstHiddenElementFocus);\n    this.lastHiddenFocusableElement = createFocusableElement(this.onLastHiddenElementFocus);\n    this.firstHiddenFocusableElement.setAttribute('data-pc-section', 'firstfocusableelement');\n    this.lastHiddenFocusableElement.setAttribute('data-pc-section', 'lastfocusableelement');\n    this.host.nativeElement.prepend(this.firstHiddenFocusableElement);\n    this.host.nativeElement.append(this.lastHiddenFocusableElement);\n  }\n  onFirstHiddenElementFocus(event) {\n    const {\n      currentTarget,\n      relatedTarget\n    } = event;\n    const focusableElement = relatedTarget === this.lastHiddenFocusableElement || !this.host.nativeElement?.contains(relatedTarget) ? DomHandler.getFirstFocusableElement(currentTarget.parentElement, ':not(.p-hidden-focusable)') : this.lastHiddenFocusableElement;\n    DomHandler.focus(focusableElement);\n  }\n  onLastHiddenElementFocus(event) {\n    const {\n      currentTarget,\n      relatedTarget\n    } = event;\n    const focusableElement = relatedTarget === this.firstHiddenFocusableElement || !this.host.nativeElement?.contains(relatedTarget) ? DomHandler.getLastFocusableElement(currentTarget.parentElement, ':not(.p-hidden-focusable)') : this.firstHiddenFocusableElement;\n    DomHandler.focus(focusableElement);\n  }\n  static ɵfac = function FocusTrap_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || FocusTrap)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: FocusTrap,\n    selectors: [[\"\", \"pFocusTrap\", \"\"]],\n    hostAttrs: [1, \"p-element\"],\n    inputs: {\n      pFocusTrapDisabled: [2, \"pFocusTrapDisabled\", \"pFocusTrapDisabled\", booleanAttribute]\n    },\n    features: [i0.ɵɵInputTransformsFeature, i0.ɵɵNgOnChangesFeature]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(FocusTrap, [{\n    type: Directive,\n    args: [{\n      selector: '[pFocusTrap]',\n      host: {\n        class: 'p-element'\n      }\n    }]\n  }], null, {\n    pFocusTrapDisabled: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }]\n  });\n})();\nclass FocusTrapModule {\n  static ɵfac = function FocusTrapModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || FocusTrapModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: FocusTrapModule\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    imports: [CommonModule]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(FocusTrapModule, [{\n    type: NgModule,\n    args: [{\n      imports: [CommonModule],\n      exports: [FocusTrap],\n      declarations: [FocusTrap]\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { FocusTrap, FocusTrapModule };", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON>", "DOCUMENT", "isPlatformBrowser", "CommonModule", "i0", "inject", "PLATFORM_ID", "ElementRef", "booleanAttribute", "Directive", "Input", "NgModule", "FocusTrap", "pFocusTrapDisabled", "platformId", "host", "document", "firstHiddenFocusableElement", "lastHiddenFocusableElement", "ngOnInit", "createHiddenFocusableElements", "ngOnChanges", "changes", "currentValue", "removeHiddenFocusableElements", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "getComputedSelector", "selector", "tabindex", "createFocusableElement", "onFocus", "createElement", "class", "role", "bind", "onFirstHiddenElementFocus", "onLastHiddenElementFocus", "setAttribute", "nativeElement", "prepend", "append", "event", "currentTarget", "relatedTarget", "focusableElement", "contains", "getFirstFocusableElement", "parentElement", "focus", "getLastFocusableElement", "ɵfac", "FocusTrap_Factory", "__ngFactoryType__", "ɵdir", "ɵɵdefineDirective", "type", "selectors", "hostAttrs", "inputs", "features", "ɵɵInputTransformsFeature", "ɵɵNgOnChangesFeature", "ngDevMode", "ɵsetClassMetadata", "args", "transform", "FocusTrapModule", "FocusTrapModule_Factory", "ɵmod", "ɵɵdefineNgModule", "ɵinj", "ɵɵdefineInjector", "imports", "exports", "declarations"], "sources": ["C:/Users/<USER>/Documents/jeansalechange/adminFront/node_modules/primeng/fesm2022/primeng-focustrap.mjs"], "sourcesContent": ["import { <PERSON><PERSON><PERSON><PERSON> } from 'primeng/dom';\nimport { DOCUMENT, isPlatformBrowser, CommonModule } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { inject, PLATFORM_ID, ElementRef, booleanAttribute, Directive, Input, NgModule } from '@angular/core';\n\n/**\n * Focus Trap keeps focus within a certain DOM element while tabbing.\n * @group Components\n */\nclass FocusTrap {\n    /**\n     * When set as true, focus wouldn't be managed.\n     * @group Props\n     */\n    pFocusTrapDisabled = false;\n    platformId = inject(PLATFORM_ID);\n    host = inject(ElementRef);\n    document = inject(DOCUMENT);\n    firstHiddenFocusableElement;\n    lastHiddenFocusableElement;\n    ngOnInit() {\n        if (isPlatformBrowser(this.platformId) && !this.pFocusTrapDisabled) {\n            !this.firstHiddenFocusableElement && !this.lastHiddenFocusableElement && this.createHiddenFocusableElements();\n        }\n    }\n    ngOnChanges(changes) {\n        if (changes.pFocusTrapDisabled && isPlatformBrowser(this.platformId)) {\n            if (changes.pFocusTrapDisabled.currentValue) {\n                this.removeHiddenFocusableElements();\n            }\n            else {\n                this.createHiddenFocusableElements();\n            }\n        }\n    }\n    removeHiddenFocusableElements() {\n        if (this.firstHiddenFocusableElement && this.firstHiddenFocusableElement.parentNode) {\n            this.firstHiddenFocusableElement.parentNode.removeChild(this.firstHiddenFocusableElement);\n        }\n        if (this.lastHiddenFocusableElement && this.lastHiddenFocusableElement.parentNode) {\n            this.lastHiddenFocusableElement.parentNode.removeChild(this.lastHiddenFocusableElement);\n        }\n    }\n    getComputedSelector(selector) {\n        return `:not(.p-hidden-focusable):not([data-p-hidden-focusable=\"true\"])${selector ?? ''}`;\n    }\n    createHiddenFocusableElements() {\n        const tabindex = '0';\n        const createFocusableElement = (onFocus) => {\n            return DomHandler.createElement('span', {\n                class: 'p-hidden-accessible p-hidden-focusable',\n                tabindex,\n                role: 'presentation',\n                'data-p-hidden-accessible': true,\n                'data-p-hidden-focusable': true,\n                onFocus: onFocus?.bind(this)\n            });\n        };\n        this.firstHiddenFocusableElement = createFocusableElement(this.onFirstHiddenElementFocus);\n        this.lastHiddenFocusableElement = createFocusableElement(this.onLastHiddenElementFocus);\n        this.firstHiddenFocusableElement.setAttribute('data-pc-section', 'firstfocusableelement');\n        this.lastHiddenFocusableElement.setAttribute('data-pc-section', 'lastfocusableelement');\n        this.host.nativeElement.prepend(this.firstHiddenFocusableElement);\n        this.host.nativeElement.append(this.lastHiddenFocusableElement);\n    }\n    onFirstHiddenElementFocus(event) {\n        const { currentTarget, relatedTarget } = event;\n        const focusableElement = relatedTarget === this.lastHiddenFocusableElement || !this.host.nativeElement?.contains(relatedTarget) ? DomHandler.getFirstFocusableElement(currentTarget.parentElement, ':not(.p-hidden-focusable)') : this.lastHiddenFocusableElement;\n        DomHandler.focus(focusableElement);\n    }\n    onLastHiddenElementFocus(event) {\n        const { currentTarget, relatedTarget } = event;\n        const focusableElement = relatedTarget === this.firstHiddenFocusableElement || !this.host.nativeElement?.contains(relatedTarget) ? DomHandler.getLastFocusableElement(currentTarget.parentElement, ':not(.p-hidden-focusable)') : this.firstHiddenFocusableElement;\n        DomHandler.focus(focusableElement);\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"18.0.1\", ngImport: i0, type: FocusTrap, deps: [], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"16.1.0\", version: \"18.0.1\", type: FocusTrap, selector: \"[pFocusTrap]\", inputs: { pFocusTrapDisabled: [\"pFocusTrapDisabled\", \"pFocusTrapDisabled\", booleanAttribute] }, host: { classAttribute: \"p-element\" }, usesOnChanges: true, ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"18.0.1\", ngImport: i0, type: FocusTrap, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[pFocusTrap]',\n                    host: {\n                        class: 'p-element'\n                    }\n                }]\n        }], propDecorators: { pFocusTrapDisabled: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }] } });\nclass FocusTrapModule {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"18.0.1\", ngImport: i0, type: FocusTrapModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\n    static ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"18.0.1\", ngImport: i0, type: FocusTrapModule, declarations: [FocusTrap], imports: [CommonModule], exports: [FocusTrap] });\n    static ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"18.0.1\", ngImport: i0, type: FocusTrapModule, imports: [CommonModule] });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"18.0.1\", ngImport: i0, type: FocusTrapModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [CommonModule],\n                    exports: [FocusTrap],\n                    declarations: [FocusTrap]\n                }]\n        }] });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { FocusTrap, FocusTrapModule };\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,aAAa;AACxC,SAASC,QAAQ,EAAEC,iBAAiB,EAAEC,YAAY,QAAQ,iBAAiB;AAC3E,OAAO,KAAKC,EAAE,MAAM,eAAe;AACnC,SAASC,MAAM,EAAEC,WAAW,EAAEC,UAAU,EAAEC,gBAAgB,EAAEC,SAAS,EAAEC,KAAK,EAAEC,QAAQ,QAAQ,eAAe;;AAE7G;AACA;AACA;AACA;AACA,MAAMC,SAAS,CAAC;EACZ;AACJ;AACA;AACA;EACIC,kBAAkB,GAAG,KAAK;EAC1BC,UAAU,GAAGT,MAAM,CAACC,WAAW,CAAC;EAChCS,IAAI,GAAGV,MAAM,CAACE,UAAU,CAAC;EACzBS,QAAQ,GAAGX,MAAM,CAACJ,QAAQ,CAAC;EAC3BgB,2BAA2B;EAC3BC,0BAA0B;EAC1BC,QAAQA,CAAA,EAAG;IACP,IAAIjB,iBAAiB,CAAC,IAAI,CAACY,UAAU,CAAC,IAAI,CAAC,IAAI,CAACD,kBAAkB,EAAE;MAChE,CAAC,IAAI,CAACI,2BAA2B,IAAI,CAAC,IAAI,CAACC,0BAA0B,IAAI,IAAI,CAACE,6BAA6B,CAAC,CAAC;IACjH;EACJ;EACAC,WAAWA,CAACC,OAAO,EAAE;IACjB,IAAIA,OAAO,CAACT,kBAAkB,IAAIX,iBAAiB,CAAC,IAAI,CAACY,UAAU,CAAC,EAAE;MAClE,IAAIQ,OAAO,CAACT,kBAAkB,CAACU,YAAY,EAAE;QACzC,IAAI,CAACC,6BAA6B,CAAC,CAAC;MACxC,CAAC,MACI;QACD,IAAI,CAACJ,6BAA6B,CAAC,CAAC;MACxC;IACJ;EACJ;EACAI,6BAA6BA,CAAA,EAAG;IAC5B,IAAI,IAAI,CAACP,2BAA2B,IAAI,IAAI,CAACA,2BAA2B,CAACQ,UAAU,EAAE;MACjF,IAAI,CAACR,2BAA2B,CAACQ,UAAU,CAACC,WAAW,CAAC,IAAI,CAACT,2BAA2B,CAAC;IAC7F;IACA,IAAI,IAAI,CAACC,0BAA0B,IAAI,IAAI,CAACA,0BAA0B,CAACO,UAAU,EAAE;MAC/E,IAAI,CAACP,0BAA0B,CAACO,UAAU,CAACC,WAAW,CAAC,IAAI,CAACR,0BAA0B,CAAC;IAC3F;EACJ;EACAS,mBAAmBA,CAACC,QAAQ,EAAE;IAC1B,OAAO,kEAAkEA,QAAQ,IAAI,EAAE,EAAE;EAC7F;EACAR,6BAA6BA,CAAA,EAAG;IAC5B,MAAMS,QAAQ,GAAG,GAAG;IACpB,MAAMC,sBAAsB,GAAIC,OAAO,IAAK;MACxC,OAAO/B,UAAU,CAACgC,aAAa,CAAC,MAAM,EAAE;QACpCC,KAAK,EAAE,wCAAwC;QAC/CJ,QAAQ;QACRK,IAAI,EAAE,cAAc;QACpB,0BAA0B,EAAE,IAAI;QAChC,yBAAyB,EAAE,IAAI;QAC/BH,OAAO,EAAEA,OAAO,EAAEI,IAAI,CAAC,IAAI;MAC/B,CAAC,CAAC;IACN,CAAC;IACD,IAAI,CAAClB,2BAA2B,GAAGa,sBAAsB,CAAC,IAAI,CAACM,yBAAyB,CAAC;IACzF,IAAI,CAAClB,0BAA0B,GAAGY,sBAAsB,CAAC,IAAI,CAACO,wBAAwB,CAAC;IACvF,IAAI,CAACpB,2BAA2B,CAACqB,YAAY,CAAC,iBAAiB,EAAE,uBAAuB,CAAC;IACzF,IAAI,CAACpB,0BAA0B,CAACoB,YAAY,CAAC,iBAAiB,EAAE,sBAAsB,CAAC;IACvF,IAAI,CAACvB,IAAI,CAACwB,aAAa,CAACC,OAAO,CAAC,IAAI,CAACvB,2BAA2B,CAAC;IACjE,IAAI,CAACF,IAAI,CAACwB,aAAa,CAACE,MAAM,CAAC,IAAI,CAACvB,0BAA0B,CAAC;EACnE;EACAkB,yBAAyBA,CAACM,KAAK,EAAE;IAC7B,MAAM;MAAEC,aAAa;MAAEC;IAAc,CAAC,GAAGF,KAAK;IAC9C,MAAMG,gBAAgB,GAAGD,aAAa,KAAK,IAAI,CAAC1B,0BAA0B,IAAI,CAAC,IAAI,CAACH,IAAI,CAACwB,aAAa,EAAEO,QAAQ,CAACF,aAAa,CAAC,GAAG5C,UAAU,CAAC+C,wBAAwB,CAACJ,aAAa,CAACK,aAAa,EAAE,2BAA2B,CAAC,GAAG,IAAI,CAAC9B,0BAA0B;IACjQlB,UAAU,CAACiD,KAAK,CAACJ,gBAAgB,CAAC;EACtC;EACAR,wBAAwBA,CAACK,KAAK,EAAE;IAC5B,MAAM;MAAEC,aAAa;MAAEC;IAAc,CAAC,GAAGF,KAAK;IAC9C,MAAMG,gBAAgB,GAAGD,aAAa,KAAK,IAAI,CAAC3B,2BAA2B,IAAI,CAAC,IAAI,CAACF,IAAI,CAACwB,aAAa,EAAEO,QAAQ,CAACF,aAAa,CAAC,GAAG5C,UAAU,CAACkD,uBAAuB,CAACP,aAAa,CAACK,aAAa,EAAE,2BAA2B,CAAC,GAAG,IAAI,CAAC/B,2BAA2B;IAClQjB,UAAU,CAACiD,KAAK,CAACJ,gBAAgB,CAAC;EACtC;EACA,OAAOM,IAAI,YAAAC,kBAAAC,iBAAA;IAAA,YAAAA,iBAAA,IAAwFzC,SAAS;EAAA;EAC5G,OAAO0C,IAAI,kBAD8ElD,EAAE,CAAAmD,iBAAA;IAAAC,IAAA,EACJ5C,SAAS;IAAA6C,SAAA;IAAAC,SAAA;IAAAC,MAAA;MAAA9C,kBAAA,kDAAuGL,gBAAgB;IAAA;IAAAoD,QAAA,GAD9HxD,EAAE,CAAAyD,wBAAA,EAAFzD,EAAE,CAAA0D,oBAAA;EAAA;AAE/F;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAH6F3D,EAAE,CAAA4D,iBAAA,CAGJpD,SAAS,EAAc,CAAC;IACvG4C,IAAI,EAAE/C,SAAS;IACfwD,IAAI,EAAE,CAAC;MACCrC,QAAQ,EAAE,cAAc;MACxBb,IAAI,EAAE;QACFkB,KAAK,EAAE;MACX;IACJ,CAAC;EACT,CAAC,CAAC,QAAkB;IAAEpB,kBAAkB,EAAE,CAAC;MACnC2C,IAAI,EAAE9C,KAAK;MACXuD,IAAI,EAAE,CAAC;QAAEC,SAAS,EAAE1D;MAAiB,CAAC;IAC1C,CAAC;EAAE,CAAC;AAAA;AAChB,MAAM2D,eAAe,CAAC;EAClB,OAAOhB,IAAI,YAAAiB,wBAAAf,iBAAA;IAAA,YAAAA,iBAAA,IAAwFc,eAAe;EAAA;EAClH,OAAOE,IAAI,kBAjB8EjE,EAAE,CAAAkE,gBAAA;IAAAd,IAAA,EAiBSW;EAAe;EACnH,OAAOI,IAAI,kBAlB8EnE,EAAE,CAAAoE,gBAAA;IAAAC,OAAA,GAkBoCtE,YAAY;EAAA;AAC/I;AACA;EAAA,QAAA4D,SAAA,oBAAAA,SAAA,KApB6F3D,EAAE,CAAA4D,iBAAA,CAoBJG,eAAe,EAAc,CAAC;IAC7GX,IAAI,EAAE7C,QAAQ;IACdsD,IAAI,EAAE,CAAC;MACCQ,OAAO,EAAE,CAACtE,YAAY,CAAC;MACvBuE,OAAO,EAAE,CAAC9D,SAAS,CAAC;MACpB+D,YAAY,EAAE,CAAC/D,SAAS;IAC5B,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;;AAEA,SAASA,SAAS,EAAEuD,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}