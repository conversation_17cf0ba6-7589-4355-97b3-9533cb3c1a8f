{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport tinymce from 'tinymce';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nexport class TinyMCEComponent {\n  constructor(host, locationStrategy) {\n    this.host = host;\n    this.locationStrategy = locationStrategy;\n    this.editorKeyup = new EventEmitter();\n  }\n  ngAfterViewInit() {\n    tinymce.init({\n      target: this.host.nativeElement,\n      plugins: ['link', 'paste', 'table'],\n      skin_url: `${this.locationStrategy.getBaseHref()}assets/skins/lightgray`,\n      setup: editor => {\n        this.editor = editor;\n        editor.on('keyup', () => {\n          this.editorKeyup.emit(editor.getContent());\n        });\n      },\n      height: '320'\n    });\n  }\n  ngOnDestroy() {\n    tinymce.remove(this.editor);\n  }\n  static {\n    this.ɵfac = function TinyMCEComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || TinyMCEComponent)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i1.LocationStrategy));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: TinyMCEComponent,\n      selectors: [[\"ngx-tiny-mce\"]],\n      outputs: {\n        editorKeyup: \"editorKeyup\"\n      },\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 0,\n      vars: 0,\n      template: function TinyMCEComponent_Template(rf, ctx) {},\n      encapsulation: 2\n    });\n  }\n}", "map": {"version": 3, "names": ["EventEmitter", "<PERSON><PERSON><PERSON>", "TinyMCEComponent", "constructor", "host", "locationStrategy", "editor<PERSON><PERSON><PERSON>", "ngAfterViewInit", "init", "target", "nativeElement", "plugins", "skin_url", "getBaseHref", "setup", "editor", "on", "emit", "get<PERSON>ontent", "height", "ngOnDestroy", "remove", "i0", "ɵɵdirectiveInject", "ElementRef", "i1", "LocationStrategy", "selectors", "outputs", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "template", "TinyMCEComponent_Template", "rf", "ctx", "encapsulation"], "sources": ["C:\\Users\\<USER>\\Documents\\jeansalechange\\adminFront\\src\\app\\@theme\\components\\tiny-mce\\tiny-mce.component.ts"], "sourcesContent": ["import { Component, On<PERSON><PERSON>roy, AfterViewInit, Output, EventEmitter, ElementRef } from '@angular/core';\r\nimport { LocationStrategy } from '@angular/common';\r\nimport tinymce from 'tinymce';\r\n\r\n@Component({\r\n    selector: 'ngx-tiny-mce',\r\n    template: '',\r\n    standalone: true,\r\n})\r\nexport class TinyMCEComponent implements OnDestroy, AfterViewInit {\r\n\r\n  @Output() editorKeyup = new EventEmitter<any>();\r\n\r\n  editor: any;\r\n\r\n  constructor(\r\n    private host: ElementRef,\r\n    private locationStrategy: LocationStrategy,\r\n  ) { }\r\n\r\n  ngAfterViewInit() {\r\n    tinymce.init({\r\n      target: this.host.nativeElement,\r\n      plugins: ['link', 'paste', 'table'],\r\n      skin_url: `${this.locationStrategy.getBaseHref()}assets/skins/lightgray`,\r\n      setup: editor => {\r\n        this.editor = editor;\r\n        editor.on('keyup', () => {\r\n          this.editorKeyup.emit(editor.getContent());\r\n        });\r\n      },\r\n      height: '320',\r\n    });\r\n  }\r\n\r\n  ngOnDestroy() {\r\n    tinymce.remove(this.editor);\r\n  }\r\n}\r\n"], "mappings": "AAAA,SAAsDA,YAAY,QAAoB,eAAe;AAErG,OAAOC,OAAO,MAAM,SAAS;;;AAO7B,OAAM,MAAOC,gBAAgB;EAM3BC,YACUC,IAAgB,EAChBC,gBAAkC;IADlC,KAAAD,IAAI,GAAJA,IAAI;IACJ,KAAAC,gBAAgB,GAAhBA,gBAAgB;IANhB,KAAAC,WAAW,GAAG,IAAIN,YAAY,EAAO;EAO3C;EAEJO,eAAeA,CAAA;IACbN,OAAO,CAACO,IAAI,CAAC;MACXC,MAAM,EAAE,IAAI,CAACL,IAAI,CAACM,aAAa;MAC/BC,OAAO,EAAE,CAAC,MAAM,EAAE,OAAO,EAAE,OAAO,CAAC;MACnCC,QAAQ,EAAE,GAAG,IAAI,CAACP,gBAAgB,CAACQ,WAAW,EAAE,wBAAwB;MACxEC,KAAK,EAAEC,MAAM,IAAG;QACd,IAAI,CAACA,MAAM,GAAGA,MAAM;QACpBA,MAAM,CAACC,EAAE,CAAC,OAAO,EAAE,MAAK;UACtB,IAAI,CAACV,WAAW,CAACW,IAAI,CAACF,MAAM,CAACG,UAAU,EAAE,CAAC;QAC5C,CAAC,CAAC;MACJ,CAAC;MACDC,MAAM,EAAE;KACT,CAAC;EACJ;EAEAC,WAAWA,CAAA;IACTnB,OAAO,CAACoB,MAAM,CAAC,IAAI,CAACN,MAAM,CAAC;EAC7B;;;uCA5BWb,gBAAgB,EAAAoB,EAAA,CAAAC,iBAAA,CAAAD,EAAA,CAAAE,UAAA,GAAAF,EAAA,CAAAC,iBAAA,CAAAE,EAAA,CAAAC,gBAAA;IAAA;EAAA;;;YAAhBxB,gBAAgB;MAAAyB,SAAA;MAAAC,OAAA;QAAAtB,WAAA;MAAA;MAAAuB,UAAA;MAAAC,QAAA,GAAAR,EAAA,CAAAS,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,QAAA,WAAAC,0BAAAC,EAAA,EAAAC,GAAA;MAAAC,aAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}