{"ast": null, "code": "import * as moment from 'moment';\nimport * as i0 from \"@angular/core\";\nexport class MomentPipe {\n  transform(value, dateFormat) {\n    if (value == null || value === undefined) return null;\n    const date = moment(value).format(dateFormat);\n    const stillUtc = moment.utc(date).toDate();\n    return moment(stillUtc).local().format(dateFormat);\n  }\n  static {\n    this.ɵfac = function MomentPipe_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MomentPipe)();\n    };\n  }\n  static {\n    this.ɵpipe = /*@__PURE__*/i0.ɵɵdefinePipe({\n      name: \"localDate\",\n      type: MomentPipe,\n      pure: true,\n      standalone: true\n    });\n  }\n}", "map": {"version": 3, "names": ["moment", "MomentPipe", "transform", "value", "dateFormat", "undefined", "date", "format", "stillUtc", "utc", "toDate", "local", "pure", "standalone"], "sources": ["C:\\Users\\<USER>\\Documents\\jeansalechange\\adminFront\\src\\app\\@theme\\pipes\\moment.pipe.ts"], "sourcesContent": ["import { Pipe, PipeTransform } from '@angular/core';\r\nimport * as moment from 'moment';\r\n\r\n@Pipe({\r\n    name: 'localDate',\r\n    standalone: true\r\n})\r\nexport class MomentPipe implements PipeTransform {\r\n  transform(value: Date | undefined | moment.Moment | string, dateFormat: string): any {\r\n    if (value == null || value === undefined)\r\n      return null;\r\n    const date = moment(value).format(dateFormat);\r\n    const stillUtc = moment.utc(date).toDate();\r\n    return moment(stillUtc).local().format(dateFormat);\r\n  }\r\n}\r\n"], "mappings": "AACA,OAAO,KAAKA,MAAM,MAAM,QAAQ;;AAMhC,OAAM,MAAOC,UAAU;EACrBC,SAASA,CAACC,KAAgD,EAAEC,UAAkB;IAC5E,IAAID,KAAK,IAAI,IAAI,IAAIA,KAAK,KAAKE,SAAS,EACtC,OAAO,IAAI;IACb,MAAMC,IAAI,GAAGN,MAAM,CAACG,KAAK,CAAC,CAACI,MAAM,CAACH,UAAU,CAAC;IAC7C,MAAMI,QAAQ,GAAGR,MAAM,CAACS,GAAG,CAACH,IAAI,CAAC,CAACI,MAAM,EAAE;IAC1C,OAAOV,MAAM,CAACQ,QAAQ,CAAC,CAACG,KAAK,EAAE,CAACJ,MAAM,CAACH,UAAU,CAAC;EACpD;;;uCAPWH,UAAU;IAAA;EAAA;;;;YAAVA,UAAU;MAAAW,IAAA;MAAAC,UAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}