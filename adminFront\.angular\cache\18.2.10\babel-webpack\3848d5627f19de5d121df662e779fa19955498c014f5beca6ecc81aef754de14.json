{"ast": null, "code": "export class SolarData {}", "map": {"version": 3, "names": ["SolarData"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\@core\\data\\solar.ts"], "sourcesContent": ["import { Observable } from 'rxjs';\r\n\r\nexport abstract class SolarData {\r\n  abstract getSolarData(): Observable<number>;\r\n}\r\n"], "mappings": "AAEA,OAAM,MAAgBA,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}