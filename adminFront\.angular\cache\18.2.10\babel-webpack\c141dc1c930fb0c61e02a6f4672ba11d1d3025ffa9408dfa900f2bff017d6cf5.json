{"ast": null, "code": "import { EventEmitter, forwardRef } from '@angular/core';\nimport { NG_VALUE_ACCESSOR } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nimport * as i2 from \"@angular/forms\";\nimport * as i3 from \"@nebular/theme\";\nfunction HouseholdBindingComponent_div_1_div_9_ng_container_1_span_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"span\", 21);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementStart(2, \"button\", 22);\n    i0.ɵɵlistener(\"click\", function HouseholdBindingComponent_div_1_div_9_ng_container_1_span_4_Template_button_click_2_listener() {\n      const householdCode_r4 = i0.ɵɵrestoreView(_r3).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r1.onRemoveHousehold(householdCode_r4));\n    });\n    i0.ɵɵelement(3, \"nb-icon\", 23);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const householdCode_r4 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", householdCode_r4, \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r1.disabled);\n  }\n}\nfunction HouseholdBindingComponent_div_1_div_9_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 18);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 19);\n    i0.ɵɵtemplate(4, HouseholdBindingComponent_div_1_div_9_ng_container_1_span_4_Template, 4, 2, \"span\", 20);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const building_r5 = i0.ɵɵnextContext().$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", building_r5, \":\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.getBuildingSelectedHouseholds(building_r5));\n  }\n}\nfunction HouseholdBindingComponent_div_1_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 16);\n    i0.ɵɵtemplate(1, HouseholdBindingComponent_div_1_div_9_ng_container_1_Template, 5, 2, \"ng-container\", 17);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const building_r5 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.hasBuildingSelected(building_r5));\n  }\n}\nfunction HouseholdBindingComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 8)(1, \"div\", 9)(2, \"div\", 10);\n    i0.ɵɵelement(3, \"nb-icon\", 11);\n    i0.ɵɵelementStart(4, \"span\", 12);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"button\", 13);\n    i0.ɵɵlistener(\"click\", function HouseholdBindingComponent_div_1_Template_button_click_6_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onClearAll());\n    });\n    i0.ɵɵtext(7, \" \\u6E05\\u7A7A\\u5168\\u90E8 \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 14);\n    i0.ɵɵtemplate(9, HouseholdBindingComponent_div_1_div_9_Template, 2, 1, \"div\", 15);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\"\\u5DF2\\u9078\\u64C7\\u6236\\u5225 (\", ctx_r1.getSelectedCount(), \")\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r1.disabled);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.buildings);\n  }\n}\nfunction HouseholdBindingComponent_div_7_button_17_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 48);\n    i0.ɵɵlistener(\"click\", function HouseholdBindingComponent_div_7_button_17_Template_button_click_0_listener() {\n      const building_r8 = i0.ɵɵrestoreView(_r7).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onBuildingSelect(building_r8));\n    });\n    i0.ɵɵelementStart(1, \"span\", 29);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\", 49);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const building_r8 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵstyleProp(\"background-color\", ctx_r1.selectedBuilding === building_r8 ? \"#e3f2fd\" : \"transparent\")(\"border-left\", ctx_r1.selectedBuilding === building_r8 ? \"3px solid #007bff\" : \"3px solid transparent\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(building_r8);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r1.getBuildingCount(building_r8), \"\\u6236\");\n  }\n}\nfunction HouseholdBindingComponent_div_7_span_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 50);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\"(\", ctx_r1.selectedBuilding, \")\");\n  }\n}\nfunction HouseholdBindingComponent_div_7_div_24_button_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 55);\n    i0.ɵɵlistener(\"click\", function HouseholdBindingComponent_div_7_div_24_button_5_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.onUnselectAllBuilding());\n    });\n    i0.ɵɵtext(1, \" \\u6E05\\u9664 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction HouseholdBindingComponent_div_7_div_24_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 51)(1, \"button\", 52);\n    i0.ɵɵlistener(\"click\", function HouseholdBindingComponent_div_7_div_24_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onSelectAllFiltered());\n    });\n    i0.ɵɵtext(2, \" \\u5168\\u9078\\u7576\\u524D \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"button\", 53);\n    i0.ɵɵlistener(\"click\", function HouseholdBindingComponent_div_7_div_24_Template_button_click_3_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onSelectAllBuilding());\n    });\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(5, HouseholdBindingComponent_div_7_div_24_button_5_Template, 2, 0, \"button\", 54);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵstyleProp(\"opacity\", ctx_r1.canSelectMore() ? \"1\" : \"0.5\");\n    i0.ɵɵproperty(\"disabled\", !ctx_r1.canSelectMore());\n    i0.ɵɵadvance(2);\n    i0.ɵɵstyleProp(\"opacity\", ctx_r1.canSelectMore() ? \"1\" : \"0.5\");\n    i0.ɵɵproperty(\"disabled\", !ctx_r1.canSelectMore());\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \\u5168\\u9078\", ctx_r1.selectedBuilding, \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isSomeBuildingSelected());\n  }\n}\nfunction HouseholdBindingComponent_div_7_div_25_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 61);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \\u627E\\u4E0D\\u5230\\u7B26\\u5408 \\\"\", ctx_r1.searchTerm, \"\\\" \\u7684\\u6236\\u5225 \");\n  }\n}\nfunction HouseholdBindingComponent_div_7_div_25_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 56)(1, \"div\", 57)(2, \"input\", 58);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdBindingComponent_div_7_div_25_Template_input_ngModelChange_2_listener($event) {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r1.searchTerm, $event) || (ctx_r1.searchTerm = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"input\", function HouseholdBindingComponent_div_7_div_25_Template_input_input_2_listener($event) {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onSearchChange($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(3, \"nb-icon\", 59);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, HouseholdBindingComponent_div_7_div_25_div_4_Template, 2, 1, \"div\", 60);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.searchTerm);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.searchTerm && ctx_r1.filteredHouseholds.length === 0);\n  }\n}\nfunction HouseholdBindingComponent_div_7_div_27_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 62);\n    i0.ɵɵelement(1, \"nb-icon\", 63);\n    i0.ɵɵelementStart(2, \"p\", 64);\n    i0.ɵɵtext(3, \"\\u8ACB\\u5148\\u9078\\u64C7\\u68DF\\u5225\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction HouseholdBindingComponent_div_7_div_28_button_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 67);\n    i0.ɵɵlistener(\"click\", function HouseholdBindingComponent_div_7_div_28_button_1_Template_button_click_0_listener() {\n      const householdCode_r13 = i0.ɵɵrestoreView(_r12).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.onHouseholdToggle(householdCode_r13));\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const householdCode_r13 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵstyleProp(\"background-color\", ctx_r1.isHouseholdSelected(householdCode_r13) ? \"#007bff\" : \"#fff\")(\"color\", ctx_r1.isHouseholdSelected(householdCode_r13) ? \"#fff\" : \"#495057\")(\"border-color\", ctx_r1.isHouseholdSelected(householdCode_r13) ? \"#007bff\" : \"#ced4da\")(\"opacity\", !ctx_r1.canSelectMore() && !ctx_r1.isHouseholdSelected(householdCode_r13) ? \"0.5\" : \"1\");\n    i0.ɵɵproperty(\"disabled\", !ctx_r1.canSelectMore() && !ctx_r1.isHouseholdSelected(householdCode_r13));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", householdCode_r13, \" \");\n  }\n}\nfunction HouseholdBindingComponent_div_7_div_28_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 65);\n    i0.ɵɵtemplate(1, HouseholdBindingComponent_div_7_div_28_button_1_Template, 2, 10, \"button\", 66);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.filteredHouseholds);\n  }\n}\nfunction HouseholdBindingComponent_div_7_span_31_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\"\\u6700\\u591A\\u53EF\\u9078 \", ctx_r1.maxSelections, \" \\u500B | \");\n  }\n}\nfunction HouseholdBindingComponent_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 24)(1, \"div\", 25)(2, \"div\", 26)(3, \"div\", 27);\n    i0.ɵɵelement(4, \"nb-icon\", 28);\n    i0.ɵɵelementStart(5, \"span\", 29);\n    i0.ɵɵtext(6, \"\\u9078\\u64C7\\u6236\\u5225\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"span\", 30);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"span\", 30);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(11, \"div\", 31)(12, \"div\", 32)(13, \"div\", 33)(14, \"h6\", 34);\n    i0.ɵɵtext(15, \"\\u68DF\\u5225\\u5217\\u8868\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(16, \"div\", 35);\n    i0.ɵɵtemplate(17, HouseholdBindingComponent_div_7_button_17_Template, 5, 6, \"button\", 36);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(18, \"div\", 37)(19, \"div\", 33)(20, \"div\", 38)(21, \"h6\", 34);\n    i0.ɵɵtext(22, \" \\u6236\\u5225\\u9078\\u64C7 \");\n    i0.ɵɵtemplate(23, HouseholdBindingComponent_div_7_span_23_Template, 2, 1, \"span\", 39);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(24, HouseholdBindingComponent_div_7_div_24_Template, 6, 8, \"div\", 40);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(25, HouseholdBindingComponent_div_7_div_25_Template, 5, 2, \"div\", 41);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"div\", 42);\n    i0.ɵɵtemplate(27, HouseholdBindingComponent_div_7_div_27_Template, 4, 0, \"div\", 43)(28, HouseholdBindingComponent_div_7_div_28_Template, 2, 1, \"div\", 44);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(29, \"div\", 45)(30, \"div\", 46);\n    i0.ɵɵtemplate(31, HouseholdBindingComponent_div_7_span_31_Template, 2, 1, \"span\", 17);\n    i0.ɵɵtext(32);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(33, \"button\", 47);\n    i0.ɵɵlistener(\"click\", function HouseholdBindingComponent_div_7_Template_button_click_33_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.closeDropdown());\n    });\n    i0.ɵɵtext(34, \" \\u78BA\\u5B9A \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate1(\"(\", ctx_r1.buildings.length, \" \\u500B\\u68DF\\u5225)\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\\u5DF2\\u9078\\u64C7: \", ctx_r1.getSelectedCount(), \"\");\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.buildings);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.selectedBuilding);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.allowBatchSelect && ctx_r1.selectedBuilding && ctx_r1.filteredHouseholds.length > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.allowSearch && ctx_r1.selectedBuilding);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.selectedBuilding);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.selectedBuilding && ctx_r1.filteredHouseholds.length > 0);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.maxSelections);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \\u5DF2\\u9078\\u64C7: \", ctx_r1.getSelectedCount(), \" \\u500B\\u6236\\u5225 \");\n  }\n}\nfunction HouseholdBindingComponent_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r14 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 68);\n    i0.ɵɵlistener(\"click\", function HouseholdBindingComponent_div_8_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.closeDropdown());\n    });\n    i0.ɵɵelementEnd();\n  }\n}\nexport class HouseholdBindingComponent {\n  constructor(cdr) {\n    this.cdr = cdr;\n    this.placeholder = '請選擇戶別';\n    this.maxSelections = null;\n    this.disabled = false;\n    this.buildingData = {};\n    this.showSelectedArea = true;\n    this.allowSearch = true;\n    this.allowBatchSelect = true;\n    this.selectionChange = new EventEmitter();\n    this.isOpen = false;\n    this.selectedBuilding = '';\n    this.searchTerm = '';\n    this.selectedHouseholds = [];\n    this.buildings = [];\n    this.filteredHouseholds = []; // 簡化為字串陣列\n    this.selectedByBuilding = {};\n    // ControlValueAccessor implementation\n    this.onChange = value => {};\n    this.onTouched = () => {};\n  }\n  writeValue(value) {\n    this.selectedHouseholds = value || [];\n    this.updateSelectedByBuilding();\n  }\n  registerOnChange(fn) {\n    this.onChange = fn;\n  }\n  registerOnTouched(fn) {\n    this.onTouched = fn;\n  }\n  setDisabledState(isDisabled) {\n    this.disabled = isDisabled;\n  }\n  ngOnInit() {\n    // 如果沒有提供 buildingData，使用 mock 資料\n    if (!this.buildingData || Object.keys(this.buildingData).length === 0) {\n      this.buildingData = this.generateMockData();\n    }\n    this.buildings = Object.keys(this.buildingData);\n    console.log('Component initialized with buildings:', this.buildings);\n    console.log('Building data keys:', Object.keys(this.buildingData));\n    this.updateSelectedByBuilding();\n  }\n  ngOnChanges(changes) {\n    if (changes['buildingData']) {\n      this.buildings = Object.keys(this.buildingData);\n      this.updateFilteredHouseholds();\n      this.updateSelectedByBuilding();\n    }\n  }\n  updateSelectedByBuilding() {\n    const grouped = {};\n    this.selectedHouseholds.forEach(code => {\n      for (const building of this.buildings) {\n        const item = this.buildingData[building]?.find(h => h.code === code);\n        if (item) {\n          if (!grouped[building]) grouped[building] = [];\n          grouped[building].push(code);\n          break;\n        }\n      }\n    });\n    this.selectedByBuilding = grouped;\n  }\n  generateMockData() {\n    // 簡化版本 - 直接生成字串陣列\n    const simpleMockData = {\n      'A棟': Array.from({\n        length: 50\n      }, (_, i) => `A${String(i + 1).padStart(3, '0')}`),\n      'B棟': Array.from({\n        length: 40\n      }, (_, i) => `B${String(i + 1).padStart(3, '0')}`),\n      'C棟': Array.from({\n        length: 60\n      }, (_, i) => `C${String(i + 1).padStart(3, '0')}`),\n      'D棟': Array.from({\n        length: 35\n      }, (_, i) => `D${String(i + 1).padStart(3, '0')}`),\n      'E棟': Array.from({\n        length: 45\n      }, (_, i) => `E${String(i + 1).padStart(3, '0')}`)\n    };\n    // 轉換為 BuildingData 格式\n    const buildingData = {};\n    Object.entries(simpleMockData).forEach(([building, codes]) => {\n      buildingData[building] = codes.map(code => ({\n        code,\n        building,\n        floor: `${Math.floor((parseInt(code.slice(1)) - 1) / 4) + 1}F`,\n        isSelected: false,\n        isDisabled: false\n      }));\n    });\n    return buildingData;\n  }\n  onBuildingSelect(building) {\n    console.log('Building selected:', building);\n    this.selectedBuilding = building;\n    this.searchTerm = '';\n    this.updateFilteredHouseholds();\n    console.log('Filtered households count:', this.filteredHouseholds.length);\n    // 手動觸發變更偵測\n    this.cdr.detectChanges();\n  }\n  onBuildingClick(building) {\n    console.log('Building clicked (mousedown):', building);\n  }\n  updateFilteredHouseholds() {\n    console.log('Updating filtered households for building:', this.selectedBuilding);\n    if (!this.selectedBuilding) {\n      this.filteredHouseholds = [];\n      return;\n    }\n    const households = this.buildingData[this.selectedBuilding] || [];\n    console.log('Available households for building:', households.length);\n    // 提取戶別代碼並進行搜尋過濾\n    this.filteredHouseholds = households.map(h => h.code).filter(code => code.toLowerCase().includes(this.searchTerm.toLowerCase()));\n    console.log('Filtered households result:', this.filteredHouseholds.length);\n  }\n  onSearchChange(event) {\n    this.searchTerm = event.target.value;\n    this.updateFilteredHouseholds();\n  }\n  onHouseholdToggle(householdCode) {\n    const isSelected = this.selectedHouseholds.includes(householdCode);\n    let newSelection;\n    if (isSelected) {\n      newSelection = this.selectedHouseholds.filter(h => h !== householdCode);\n    } else {\n      if (this.maxSelections && this.selectedHouseholds.length >= this.maxSelections) {\n        return; // 達到最大選擇數量\n      }\n      newSelection = [...this.selectedHouseholds, householdCode];\n    }\n    this.selectedHouseholds = newSelection;\n    this.emitChanges();\n  }\n  onRemoveHousehold(householdCode) {\n    this.selectedHouseholds = this.selectedHouseholds.filter(h => h !== householdCode);\n    this.emitChanges();\n  }\n  onSelectAllFiltered() {\n    if (!this.selectedBuilding || this.filteredHouseholds.length === 0) return;\n    // 直接使用過濾後的戶別代碼\n    const availableHouseholds = this.filteredHouseholds;\n    const newSelection = [...new Set([...this.selectedHouseholds, ...availableHouseholds])];\n    if (this.maxSelections && newSelection.length > this.maxSelections) {\n      return; // 超過最大選擇數量\n    }\n    this.selectedHouseholds = newSelection;\n    this.emitChanges();\n  }\n  onSelectAllBuilding() {\n    if (!this.selectedBuilding) return;\n    const buildingHouseholds = this.buildingData[this.selectedBuilding]?.map(h => h.code) || [];\n    const newSelection = [...new Set([...this.selectedHouseholds, ...buildingHouseholds])];\n    if (this.maxSelections && newSelection.length > this.maxSelections) {\n      return; // 超過最大選擇數量\n    }\n    this.selectedHouseholds = newSelection;\n    this.emitChanges();\n  }\n  onUnselectAllBuilding() {\n    if (!this.selectedBuilding) return;\n    const buildingHouseholds = this.buildingData[this.selectedBuilding]?.map(h => h.code) || [];\n    this.selectedHouseholds = this.selectedHouseholds.filter(h => !buildingHouseholds.includes(h));\n    this.emitChanges();\n  }\n  onClearAll() {\n    this.selectedHouseholds = [];\n    this.emitChanges();\n  }\n  emitChanges() {\n    this.updateSelectedByBuilding();\n    this.onChange([...this.selectedHouseholds]);\n    this.onTouched();\n    const selectedItems = this.selectedHouseholds.map(code => {\n      for (const building of this.buildings) {\n        const item = this.buildingData[building]?.find(h => h.code === code);\n        if (item) return item;\n      }\n      return null;\n    }).filter(item => item !== null);\n    this.selectionChange.emit(selectedItems);\n  }\n  toggleDropdown() {\n    if (!this.disabled) {\n      this.isOpen = !this.isOpen;\n      console.log('Dropdown toggled. isOpen:', this.isOpen);\n      console.log('Buildings available:', this.buildings);\n    }\n  }\n  closeDropdown() {\n    this.isOpen = false;\n  }\n  isHouseholdSelected(householdCode) {\n    return this.selectedHouseholds.includes(householdCode);\n  }\n  canSelectMore() {\n    return !this.maxSelections || this.selectedHouseholds.length < this.maxSelections;\n  }\n  isAllBuildingSelected() {\n    if (!this.selectedBuilding) return false;\n    const buildingHouseholds = this.buildingData[this.selectedBuilding].filter(h => !h.isDisabled).map(h => h.code);\n    return buildingHouseholds.length > 0 && buildingHouseholds.every(code => this.selectedHouseholds.includes(code));\n  }\n  isSomeBuildingSelected() {\n    if (!this.selectedBuilding) return false;\n    const buildingHouseholds = this.buildingData[this.selectedBuilding]?.map(h => h.code) || [];\n    return buildingHouseholds.some(code => this.selectedHouseholds.includes(code));\n  }\n  getSelectedByBuilding() {\n    return this.selectedByBuilding;\n  }\n  getBuildingCount(building) {\n    return this.buildingData[building]?.length || 0;\n  }\n  getSelectedCount() {\n    return this.selectedHouseholds.length;\n  }\n  // 輔助方法：安全地獲取建築物的已選戶別\n  getBuildingSelectedHouseholds(building) {\n    return this.selectedByBuilding[building] || [];\n  }\n  // 輔助方法：檢查建築物是否有已選戶別\n  hasBuildingSelected(building) {\n    return !!(this.selectedByBuilding[building] && this.selectedByBuilding[building].length > 0);\n  }\n  static {\n    this.ɵfac = function HouseholdBindingComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || HouseholdBindingComponent)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: HouseholdBindingComponent,\n      selectors: [[\"app-household-binding\"]],\n      inputs: {\n        placeholder: \"placeholder\",\n        maxSelections: \"maxSelections\",\n        disabled: \"disabled\",\n        buildingData: \"buildingData\",\n        showSelectedArea: \"showSelectedArea\",\n        allowSearch: \"allowSearch\",\n        allowBatchSelect: \"allowBatchSelect\"\n      },\n      outputs: {\n        selectionChange: \"selectionChange\"\n      },\n      features: [i0.ɵɵProvidersFeature([{\n        provide: NG_VALUE_ACCESSOR,\n        useExisting: forwardRef(() => HouseholdBindingComponent),\n        multi: true\n      }]), i0.ɵɵNgOnChangesFeature],\n      decls: 9,\n      vars: 9,\n      consts: [[1, \"household-binding-container\"], [\"class\", \"selected-households-area\", 4, \"ngIf\"], [1, \"selector-container\", 2, \"position\", \"relative\"], [\"type\", \"button\", 1, \"selector-button\", 2, \"width\", \"100%\", \"display\", \"flex\", \"align-items\", \"center\", \"justify-content\", \"space-between\", \"padding\", \"0.5rem 0.75rem\", \"border\", \"1px solid #ced4da\", \"border-radius\", \"0.375rem\", \"background-color\", \"#fff\", \"cursor\", \"pointer\", 3, \"click\", \"disabled\"], [1, \"selector-text\"], [\"icon\", \"chevron-down-outline\", 1, \"chevron-icon\"], [\"style\", \"position: absolute; top: calc(100% + 4px); left: 0; right: 0; z-index: 99999; background: white; border: 1px solid #ced4da; border-radius: 8px; box-shadow: 0 4px 12px rgba(0,0,0,0.15); padding: 0; margin: 0; max-height: 400px; overflow: hidden;\", 4, \"ngIf\"], [\"class\", \"backdrop\", 3, \"click\", 4, \"ngIf\"], [1, \"selected-households-area\"], [1, \"selected-header\"], [1, \"selected-info\"], [\"icon\", \"people-outline\", 1, \"text-primary\"], [1, \"selected-count\"], [\"type\", \"button\", 1, \"btn\", \"btn-outline-danger\", \"btn-sm\", 3, \"click\", \"disabled\"], [1, \"selected-content\"], [\"class\", \"building-group\", 4, \"ngFor\", \"ngForOf\"], [1, \"building-group\"], [4, \"ngIf\"], [1, \"building-label\"], [1, \"households-tags\"], [\"class\", \"household-tag\", 4, \"ngFor\", \"ngForOf\"], [1, \"household-tag\"], [\"type\", \"button\", 1, \"remove-btn\", 3, \"click\", \"disabled\"], [\"icon\", \"close-outline\"], [2, \"position\", \"absolute\", \"top\", \"calc(100% + 4px)\", \"left\", \"0\", \"right\", \"0\", \"z-index\", \"99999\", \"background\", \"white\", \"border\", \"1px solid #ced4da\", \"border-radius\", \"8px\", \"box-shadow\", \"0 4px 12px rgba(0,0,0,0.15)\", \"padding\", \"0\", \"margin\", \"0\", \"max-height\", \"400px\", \"overflow\", \"hidden\"], [2, \"padding\", \"12px 16px\", \"border-bottom\", \"1px solid #e9ecef\", \"background-color\", \"#f8f9fa\"], [2, \"display\", \"flex\", \"align-items\", \"center\", \"justify-content\", \"space-between\"], [2, \"display\", \"flex\", \"align-items\", \"center\", \"gap\", \"8px\"], [\"icon\", \"home-outline\", 2, \"color\", \"#007bff\"], [2, \"font-weight\", \"500\", \"color\", \"#495057\"], [2, \"font-size\", \"0.875rem\", \"color\", \"#6c757d\"], [2, \"display\", \"flex\", \"height\", \"320px\"], [2, \"width\", \"40%\", \"border-right\", \"1px solid #e9ecef\", \"background-color\", \"#f8f9fa\"], [2, \"padding\", \"12px 16px\", \"border-bottom\", \"1px solid #e9ecef\"], [2, \"margin\", \"0\", \"font-size\", \"0.875rem\", \"font-weight\", \"500\", \"color\", \"#495057\"], [2, \"max-height\", \"268px\", \"overflow-y\", \"auto\"], [\"type\", \"button\", \"style\", \"width: 100%; text-align: left; padding: 12px 16px; border: none; cursor: pointer; transition: all 0.15s ease; display: flex; align-items: center; justify-content: space-between;\", 3, \"background-color\", \"border-left\", \"click\", 4, \"ngFor\", \"ngForOf\"], [2, \"flex\", \"1\", \"display\", \"flex\", \"flex-direction\", \"column\"], [2, \"display\", \"flex\", \"align-items\", \"center\", \"justify-content\", \"space-between\", \"margin-bottom\", \"8px\"], [\"style\", \"color: #007bff;\", 4, \"ngIf\"], [\"style\", \"display: flex; gap: 4px;\", 4, \"ngIf\"], [\"style\", \"margin-top: 8px;\", 4, \"ngIf\"], [2, \"flex\", \"1\", \"padding\", \"16px\", \"overflow-y\", \"auto\"], [\"style\", \"text-align: center; padding: 40px 20px; color: #6c757d;\", 4, \"ngIf\"], [\"style\", \"display: grid; grid-template-columns: repeat(auto-fill, minmax(80px, 1fr)); gap: 8px;\", 4, \"ngIf\"], [2, \"padding\", \"12px 16px\", \"border-top\", \"1px solid #e9ecef\", \"background-color\", \"#f8f9fa\", \"display\", \"flex\", \"align-items\", \"center\", \"justify-content\", \"space-between\"], [2, \"font-size\", \"0.75rem\", \"color\", \"#6c757d\"], [\"type\", \"button\", 2, \"padding\", \"6px 16px\", \"background\", \"#007bff\", \"color\", \"white\", \"border\", \"none\", \"border-radius\", \"4px\", \"cursor\", \"pointer\", \"font-size\", \"0.875rem\", 3, \"click\"], [\"type\", \"button\", 2, \"width\", \"100%\", \"text-align\", \"left\", \"padding\", \"12px 16px\", \"border\", \"none\", \"cursor\", \"pointer\", \"transition\", \"all 0.15s ease\", \"display\", \"flex\", \"align-items\", \"center\", \"justify-content\", \"space-between\", 3, \"click\"], [2, \"font-size\", \"0.75rem\", \"color\", \"#6c757d\", \"background-color\", \"#e9ecef\", \"padding\", \"2px 6px\", \"border-radius\", \"10px\"], [2, \"color\", \"#007bff\"], [2, \"display\", \"flex\", \"gap\", \"4px\"], [\"type\", \"button\", 2, \"padding\", \"4px 8px\", \"font-size\", \"0.75rem\", \"background\", \"#007bff\", \"color\", \"white\", \"border\", \"none\", \"border-radius\", \"4px\", \"cursor\", \"pointer\", 3, \"click\", \"disabled\"], [\"type\", \"button\", 2, \"padding\", \"4px 8px\", \"font-size\", \"0.75rem\", \"background\", \"#28a745\", \"color\", \"white\", \"border\", \"none\", \"border-radius\", \"4px\", \"cursor\", \"pointer\", 3, \"click\", \"disabled\"], [\"type\", \"button\", \"style\", \"padding: 4px 8px; font-size: 0.75rem; background: #6c757d; color: white; border: none; border-radius: 4px; cursor: pointer;\", 3, \"click\", 4, \"ngIf\"], [\"type\", \"button\", 2, \"padding\", \"4px 8px\", \"font-size\", \"0.75rem\", \"background\", \"#6c757d\", \"color\", \"white\", \"border\", \"none\", \"border-radius\", \"4px\", \"cursor\", \"pointer\", 3, \"click\"], [2, \"margin-top\", \"8px\"], [2, \"position\", \"relative\"], [\"type\", \"text\", \"placeholder\", \"\\u641C\\u5C0B\\u6236\\u5225\\u4EE3\\u78BC...\", 2, \"width\", \"100%\", \"padding\", \"6px 32px 6px 12px\", \"border\", \"1px solid #ced4da\", \"border-radius\", \"4px\", \"font-size\", \"0.875rem\", \"outline\", \"none\", 3, \"ngModelChange\", \"input\", \"ngModel\"], [\"icon\", \"search-outline\", 2, \"position\", \"absolute\", \"right\", \"10px\", \"top\", \"50%\", \"transform\", \"translateY(-50%)\", \"color\", \"#6c757d\", \"font-size\", \"0.875rem\"], [\"style\", \"font-size: 0.75rem; color: #dc3545; margin-top: 4px;\", 4, \"ngIf\"], [2, \"font-size\", \"0.75rem\", \"color\", \"#dc3545\", \"margin-top\", \"4px\"], [2, \"text-align\", \"center\", \"padding\", \"40px 20px\", \"color\", \"#6c757d\"], [\"icon\", \"home-outline\", 2, \"font-size\", \"2rem\", \"margin-bottom\", \"8px\", \"opacity\", \"0.5\"], [2, \"margin\", \"0\", \"font-size\", \"0.875rem\"], [2, \"display\", \"grid\", \"grid-template-columns\", \"repeat(auto-fill, minmax(80px, 1fr))\", \"gap\", \"8px\"], [\"type\", \"button\", \"style\", \"padding: 8px 4px; border: 1px solid; border-radius: 4px; cursor: pointer; transition: all 0.15s ease; font-size: 0.75rem; text-align: center; min-height: 32px;\", 3, \"disabled\", \"background-color\", \"color\", \"border-color\", \"opacity\", \"click\", 4, \"ngFor\", \"ngForOf\"], [\"type\", \"button\", 2, \"padding\", \"8px 4px\", \"border\", \"1px solid\", \"border-radius\", \"4px\", \"cursor\", \"pointer\", \"transition\", \"all 0.15s ease\", \"font-size\", \"0.75rem\", \"text-align\", \"center\", \"min-height\", \"32px\", 3, \"click\", \"disabled\"], [1, \"backdrop\", 3, \"click\"]],\n      template: function HouseholdBindingComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵtemplate(1, HouseholdBindingComponent_div_1_Template, 10, 3, \"div\", 1);\n          i0.ɵɵelementStart(2, \"div\", 2)(3, \"button\", 3);\n          i0.ɵɵlistener(\"click\", function HouseholdBindingComponent_Template_button_click_3_listener() {\n            return ctx.toggleDropdown();\n          });\n          i0.ɵɵelementStart(4, \"span\", 4);\n          i0.ɵɵtext(5);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(6, \"nb-icon\", 5);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(7, HouseholdBindingComponent_div_7_Template, 35, 10, \"div\", 6);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(8, HouseholdBindingComponent_div_8_Template, 1, 0, \"div\", 7);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.showSelectedArea && ctx.selectedHouseholds.length > 0);\n          i0.ɵɵadvance(2);\n          i0.ɵɵclassProp(\"disabled\", ctx.disabled);\n          i0.ɵɵproperty(\"disabled\", ctx.disabled);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate1(\" \", ctx.getSelectedCount() > 0 ? \"\\u5DF2\\u9078\\u64C7 \" + ctx.getSelectedCount() + \" \\u500B\\u6236\\u5225\" : ctx.placeholder, \" \");\n          i0.ɵɵadvance();\n          i0.ɵɵclassProp(\"rotated\", ctx.isOpen);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isOpen);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isOpen);\n        }\n      },\n      dependencies: [i1.NgForOf, i1.NgIf, i2.DefaultValueAccessor, i2.NgControlStatus, i2.NgModel, i3.NbIconComponent],\n      styles: [\".household-binding-container[_ngcontent-%COMP%] {\\n  position: relative;\\n  width: 100%;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%] {\\n  margin-bottom: 1rem;\\n  padding: 1rem;\\n  background-color: #f8f9fa;\\n  border: 1px solid #e9ecef;\\n  border-radius: 0.375rem;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%]   .selected-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 0.75rem;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%]   .selected-header[_ngcontent-%COMP%]   .selected-info[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.5rem;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%]   .selected-header[_ngcontent-%COMP%]   .selected-info[_ngcontent-%COMP%]   .selected-count[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  font-weight: 500;\\n  color: #495057;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%]   .selected-content[_ngcontent-%COMP%]   .building-group[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: flex-start;\\n  gap: 0.5rem;\\n  margin-bottom: 0.5rem;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%]   .selected-content[_ngcontent-%COMP%]   .building-group[_ngcontent-%COMP%]:last-child {\\n  margin-bottom: 0;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%]   .selected-content[_ngcontent-%COMP%]   .building-group[_ngcontent-%COMP%]   .building-label[_ngcontent-%COMP%] {\\n  min-width: 3rem;\\n  font-size: 0.75rem;\\n  color: #6c757d;\\n  font-weight: 500;\\n  margin-top: 0.25rem;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%]   .selected-content[_ngcontent-%COMP%]   .building-group[_ngcontent-%COMP%]   .households-tags[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-wrap: wrap;\\n  gap: 0.25rem;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%]   .selected-content[_ngcontent-%COMP%]   .building-group[_ngcontent-%COMP%]   .households-tags[_ngcontent-%COMP%]   .household-tag[_ngcontent-%COMP%] {\\n  display: inline-flex;\\n  align-items: center;\\n  gap: 0.25rem;\\n  padding: 0.25rem 0.5rem;\\n  background-color: #e3f2fd;\\n  color: #1976d2;\\n  font-size: 0.75rem;\\n  border-radius: 1rem;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%]   .selected-content[_ngcontent-%COMP%]   .building-group[_ngcontent-%COMP%]   .households-tags[_ngcontent-%COMP%]   .household-tag[_ngcontent-%COMP%]   .remove-btn[_ngcontent-%COMP%] {\\n  background: none;\\n  border: none;\\n  padding: 0;\\n  margin: 0;\\n  cursor: pointer;\\n  color: #1976d2;\\n  border-radius: 50%;\\n  width: 1rem;\\n  height: 1rem;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%]   .selected-content[_ngcontent-%COMP%]   .building-group[_ngcontent-%COMP%]   .households-tags[_ngcontent-%COMP%]   .household-tag[_ngcontent-%COMP%]   .remove-btn[_ngcontent-%COMP%]:hover {\\n  background-color: #bbdefb;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%]   .selected-content[_ngcontent-%COMP%]   .building-group[_ngcontent-%COMP%]   .households-tags[_ngcontent-%COMP%]   .household-tag[_ngcontent-%COMP%]   .remove-btn[_ngcontent-%COMP%]:disabled {\\n  opacity: 0.5;\\n  cursor: not-allowed;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%]   .selected-content[_ngcontent-%COMP%]   .building-group[_ngcontent-%COMP%]   .households-tags[_ngcontent-%COMP%]   .household-tag[_ngcontent-%COMP%]   .remove-btn[_ngcontent-%COMP%]   nb-icon[_ngcontent-%COMP%] {\\n  font-size: 0.75rem;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%] {\\n  position: relative;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .selector-button[_ngcontent-%COMP%] {\\n  width: 100%;\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between;\\n  padding: 0.5rem 0.75rem;\\n  border: 1px solid #ced4da;\\n  border-radius: 0.375rem;\\n  background-color: #fff;\\n  cursor: pointer;\\n  transition: all 0.15s ease-in-out;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .selector-button[_ngcontent-%COMP%]:hover:not(.disabled) {\\n  background-color: #f8f9fa;\\n  border-color: #adb5bd;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .selector-button[_ngcontent-%COMP%]:focus {\\n  outline: none;\\n  border-color: #80bdff;\\n  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .selector-button.disabled[_ngcontent-%COMP%] {\\n  opacity: 0.65;\\n  cursor: not-allowed;\\n  background-color: #e9ecef;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .selector-button[_ngcontent-%COMP%]   .selector-text[_ngcontent-%COMP%] {\\n  color: #495057;\\n  font-size: 0.875rem;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .selector-button[_ngcontent-%COMP%]   .chevron-icon[_ngcontent-%COMP%] {\\n  color: #6c757d;\\n  transition: transform 0.15s ease-in-out;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .selector-button[_ngcontent-%COMP%]   .chevron-icon.rotated[_ngcontent-%COMP%] {\\n  transform: rotate(180deg);\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: calc(100% + 0.25rem);\\n  left: 0;\\n  right: 0;\\n  z-index: 1050;\\n  background-color: #fff;\\n  border: 1px solid #ced4da;\\n  border-radius: 0.375rem;\\n  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);\\n  max-height: 24rem;\\n  overflow: hidden;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  height: 20rem;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .buildings-sidebar[_ngcontent-%COMP%] {\\n  width: 33.333%;\\n  border-right: 1px solid #e9ecef;\\n  background-color: #f8f9fa;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .buildings-sidebar[_ngcontent-%COMP%]   .sidebar-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.5rem;\\n  padding: 0.75rem;\\n  border-bottom: 1px solid #e9ecef;\\n  background-color: #e9ecef;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .buildings-sidebar[_ngcontent-%COMP%]   .sidebar-header[_ngcontent-%COMP%]   .sidebar-title[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  font-weight: 500;\\n  color: #495057;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .buildings-sidebar[_ngcontent-%COMP%]   .buildings-list[_ngcontent-%COMP%] {\\n  max-height: 17rem;\\n  overflow-y: auto;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .buildings-sidebar[_ngcontent-%COMP%]   .buildings-list[_ngcontent-%COMP%]   .building-item[_ngcontent-%COMP%] {\\n  width: 100%;\\n  text-align: left;\\n  padding: 0.75rem;\\n  border: none;\\n  background: none;\\n  cursor: pointer;\\n  transition: background-color 0.15s ease-in-out;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .buildings-sidebar[_ngcontent-%COMP%]   .buildings-list[_ngcontent-%COMP%]   .building-item[_ngcontent-%COMP%]:hover {\\n  background-color: #e3f2fd;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .buildings-sidebar[_ngcontent-%COMP%]   .buildings-list[_ngcontent-%COMP%]   .building-item.active[_ngcontent-%COMP%] {\\n  background-color: #e3f2fd;\\n  color: #1976d2;\\n  font-weight: 500;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .buildings-sidebar[_ngcontent-%COMP%]   .buildings-list[_ngcontent-%COMP%]   .building-item[_ngcontent-%COMP%]   .building-info[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .buildings-sidebar[_ngcontent-%COMP%]   .buildings-list[_ngcontent-%COMP%]   .building-item[_ngcontent-%COMP%]   .building-info[_ngcontent-%COMP%]   .building-name[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .buildings-sidebar[_ngcontent-%COMP%]   .buildings-list[_ngcontent-%COMP%]   .building-item[_ngcontent-%COMP%]   .building-info[_ngcontent-%COMP%]   .building-count[_ngcontent-%COMP%] {\\n  font-size: 0.75rem;\\n  color: #6c757d;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .households-main[_ngcontent-%COMP%] {\\n  width: 66.667%;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .households-main[_ngcontent-%COMP%]   .main-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  padding: 0.75rem;\\n  border-bottom: 1px solid #e9ecef;\\n  background-color: #e9ecef;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .households-main[_ngcontent-%COMP%]   .main-header[_ngcontent-%COMP%]   .header-left[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.5rem;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .households-main[_ngcontent-%COMP%]   .main-header[_ngcontent-%COMP%]   .header-left[_ngcontent-%COMP%]   .main-title[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  font-weight: 500;\\n  color: #495057;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .households-main[_ngcontent-%COMP%]   .main-header[_ngcontent-%COMP%]   .header-left[_ngcontent-%COMP%]   .main-title[_ngcontent-%COMP%]   .building-indicator[_ngcontent-%COMP%] {\\n  color: #1976d2;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .households-main[_ngcontent-%COMP%]   .main-header[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 0.25rem;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .households-main[_ngcontent-%COMP%]   .search-box[_ngcontent-%COMP%] {\\n  padding: 0.5rem;\\n  border-bottom: 1px solid #e9ecef;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .households-main[_ngcontent-%COMP%]   .households-grid[_ngcontent-%COMP%] {\\n  padding: 0.5rem;\\n  max-height: 14rem;\\n  overflow-y: auto;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .households-main[_ngcontent-%COMP%]   .households-grid[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  padding: 2rem;\\n  color: #6c757d;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .households-main[_ngcontent-%COMP%]   .households-grid[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%]   .empty-icon[_ngcontent-%COMP%] {\\n  font-size: 2rem;\\n  margin-bottom: 0.5rem;\\n  color: #adb5bd;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .households-main[_ngcontent-%COMP%]   .households-grid[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%]   .empty-text[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  margin: 0;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .households-main[_ngcontent-%COMP%]   .households-grid[_ngcontent-%COMP%]   .grid-container[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(3, 1fr);\\n  gap: 0.25rem;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .households-main[_ngcontent-%COMP%]   .households-grid[_ngcontent-%COMP%]   .grid-container[_ngcontent-%COMP%]   .household-button[_ngcontent-%COMP%] {\\n  padding: 0.5rem;\\n  border: 1px solid #ced4da;\\n  border-radius: 0.25rem;\\n  background-color: #fff;\\n  cursor: pointer;\\n  transition: all 0.15s ease-in-out;\\n  text-align: center;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .households-main[_ngcontent-%COMP%]   .households-grid[_ngcontent-%COMP%]   .grid-container[_ngcontent-%COMP%]   .household-button[_ngcontent-%COMP%]:hover:not(.disabled) {\\n  background-color: #f8f9fa;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .households-main[_ngcontent-%COMP%]   .households-grid[_ngcontent-%COMP%]   .grid-container[_ngcontent-%COMP%]   .household-button.selected[_ngcontent-%COMP%] {\\n  background-color: #e3f2fd;\\n  border-color: #1976d2;\\n  color: #1976d2;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .households-main[_ngcontent-%COMP%]   .households-grid[_ngcontent-%COMP%]   .grid-container[_ngcontent-%COMP%]   .household-button.disabled[_ngcontent-%COMP%] {\\n  background-color: #f8f9fa;\\n  color: #6c757d;\\n  cursor: not-allowed;\\n  opacity: 0.65;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .households-main[_ngcontent-%COMP%]   .households-grid[_ngcontent-%COMP%]   .grid-container[_ngcontent-%COMP%]   .household-button[_ngcontent-%COMP%]   .household-code[_ngcontent-%COMP%] {\\n  display: block;\\n  font-size: 0.875rem;\\n  font-weight: 500;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .households-main[_ngcontent-%COMP%]   .households-grid[_ngcontent-%COMP%]   .grid-container[_ngcontent-%COMP%]   .household-button[_ngcontent-%COMP%]   .household-floor[_ngcontent-%COMP%] {\\n  display: block;\\n  font-size: 0.75rem;\\n  color: #6c757d;\\n  margin-top: 0.125rem;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-footer[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  padding: 0.75rem;\\n  border-top: 1px solid #e9ecef;\\n  background-color: #f8f9fa;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-footer[_ngcontent-%COMP%]   .footer-info[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 1rem;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-footer[_ngcontent-%COMP%]   .footer-info[_ngcontent-%COMP%]   .max-selections-text[_ngcontent-%COMP%], \\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-footer[_ngcontent-%COMP%]   .footer-info[_ngcontent-%COMP%]   .current-selections-text[_ngcontent-%COMP%] {\\n  font-size: 0.75rem;\\n  color: #6c757d;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .backdrop[_ngcontent-%COMP%] {\\n  position: fixed;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  z-index: 1040;\\n  background: transparent;\\n}\\n\\n@media (max-width: 768px) {\\n  .household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    height: auto;\\n    max-height: 20rem;\\n  }\\n  .household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .buildings-sidebar[_ngcontent-%COMP%] {\\n    width: 100%;\\n    max-height: 8rem;\\n    border-right: none;\\n    border-bottom: 1px solid #e9ecef;\\n  }\\n  .household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .buildings-sidebar[_ngcontent-%COMP%]   .buildings-list[_ngcontent-%COMP%] {\\n    max-height: 5rem;\\n  }\\n  .household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .households-main[_ngcontent-%COMP%] {\\n    width: 100%;\\n  }\\n  .household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .households-main[_ngcontent-%COMP%]   .households-grid[_ngcontent-%COMP%]   .grid-container[_ngcontent-%COMP%] {\\n    grid-template-columns: repeat(2, 1fr);\\n  }\\n}\\n@media (prefers-color-scheme: dark) {\\n  .household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%] {\\n    background-color: #343a40;\\n    border-color: #495057;\\n    color: #f8f9fa;\\n  }\\n  .household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%]   .selected-header[_ngcontent-%COMP%]   .selected-info[_ngcontent-%COMP%]   .selected-count[_ngcontent-%COMP%] {\\n    color: #f8f9fa;\\n  }\\n  .household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%]   .selected-content[_ngcontent-%COMP%]   .building-group[_ngcontent-%COMP%]   .building-label[_ngcontent-%COMP%] {\\n    color: #adb5bd;\\n  }\\n  .household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .selector-button[_ngcontent-%COMP%] {\\n    background-color: #495057;\\n    border-color: #6c757d;\\n    color: #f8f9fa;\\n  }\\n  .household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .selector-button[_ngcontent-%COMP%]:hover:not(.disabled) {\\n    background-color: #5a6268;\\n  }\\n  .household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .selector-button.disabled[_ngcontent-%COMP%] {\\n    background-color: #6c757d;\\n  }\\n  .household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%] {\\n    background-color: #495057;\\n    border-color: #6c757d;\\n  }\\n  .household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .buildings-sidebar[_ngcontent-%COMP%] {\\n    background-color: #343a40;\\n    border-color: #495057;\\n  }\\n  .household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .buildings-sidebar[_ngcontent-%COMP%]   .sidebar-header[_ngcontent-%COMP%] {\\n    background-color: #495057;\\n    border-color: #6c757d;\\n    color: #f8f9fa;\\n  }\\n  .household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .buildings-sidebar[_ngcontent-%COMP%]   .buildings-list[_ngcontent-%COMP%]   .building-item[_ngcontent-%COMP%] {\\n    color: #f8f9fa;\\n  }\\n  .household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .buildings-sidebar[_ngcontent-%COMP%]   .buildings-list[_ngcontent-%COMP%]   .building-item[_ngcontent-%COMP%]:hover {\\n    background-color: #495057;\\n  }\\n  .household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .buildings-sidebar[_ngcontent-%COMP%]   .buildings-list[_ngcontent-%COMP%]   .building-item.active[_ngcontent-%COMP%] {\\n    background-color: #0d6efd;\\n    color: #fff;\\n  }\\n  .household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .households-main[_ngcontent-%COMP%]   .main-header[_ngcontent-%COMP%] {\\n    background-color: #495057;\\n    border-color: #6c757d;\\n    color: #f8f9fa;\\n  }\\n  .household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .households-main[_ngcontent-%COMP%]   .search-box[_ngcontent-%COMP%] {\\n    border-color: #6c757d;\\n  }\\n  .household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .households-main[_ngcontent-%COMP%]   .households-grid[_ngcontent-%COMP%]   .grid-container[_ngcontent-%COMP%]   .household-button[_ngcontent-%COMP%] {\\n    background-color: #6c757d;\\n    border-color: #adb5bd;\\n    color: #f8f9fa;\\n  }\\n  .household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .households-main[_ngcontent-%COMP%]   .households-grid[_ngcontent-%COMP%]   .grid-container[_ngcontent-%COMP%]   .household-button[_ngcontent-%COMP%]:hover:not(.disabled) {\\n    background-color: #5a6268;\\n  }\\n  .household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .households-main[_ngcontent-%COMP%]   .households-grid[_ngcontent-%COMP%]   .grid-container[_ngcontent-%COMP%]   .household-button.selected[_ngcontent-%COMP%] {\\n    background-color: #0d6efd;\\n    border-color: #0d6efd;\\n    color: #fff;\\n  }\\n  .household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-footer[_ngcontent-%COMP%] {\\n    background-color: #343a40;\\n    border-color: #495057;\\n    color: #f8f9fa;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;base64,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 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["EventEmitter", "forwardRef", "NG_VALUE_ACCESSOR", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵlistener", "HouseholdBindingComponent_div_1_div_9_ng_container_1_span_4_Template_button_click_2_listener", "householdCode_r4", "ɵɵrestoreView", "_r3", "$implicit", "ctx_r1", "ɵɵnextContext", "ɵɵresetView", "onRemoveHousehold", "ɵɵelement", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate1", "ɵɵproperty", "disabled", "ɵɵelementContainerStart", "ɵɵtemplate", "HouseholdBindingComponent_div_1_div_9_ng_container_1_span_4_Template", "building_r5", "getBuildingSelectedHouseholds", "HouseholdBindingComponent_div_1_div_9_ng_container_1_Template", "hasBuildingSelected", "HouseholdBindingComponent_div_1_Template_button_click_6_listener", "_r1", "onClearAll", "HouseholdBindingComponent_div_1_div_9_Template", "getSelectedCount", "buildings", "HouseholdBindingComponent_div_7_button_17_Template_button_click_0_listener", "building_r8", "_r7", "onBuildingSelect", "ɵɵstyleProp", "selectedBuilding", "ɵɵtextInterpolate", "getBuildingCount", "HouseholdBindingComponent_div_7_div_24_button_5_Template_button_click_0_listener", "_r10", "onUnselectAllBuilding", "HouseholdBindingComponent_div_7_div_24_Template_button_click_1_listener", "_r9", "onSelectAllFiltered", "HouseholdBindingComponent_div_7_div_24_Template_button_click_3_listener", "onSelectAllBuilding", "HouseholdBindingComponent_div_7_div_24_button_5_Template", "canSelectMore", "isSomeBuildingSelected", "searchTerm", "ɵɵtwoWayListener", "HouseholdBindingComponent_div_7_div_25_Template_input_ngModelChange_2_listener", "$event", "_r11", "ɵɵtwoWayBindingSet", "HouseholdBindingComponent_div_7_div_25_Template_input_input_2_listener", "onSearchChange", "HouseholdBindingComponent_div_7_div_25_div_4_Template", "ɵɵtwoWayProperty", "filteredHouseholds", "length", "HouseholdBindingComponent_div_7_div_28_button_1_Template_button_click_0_listener", "householdCode_r13", "_r12", "onHouseholdToggle", "isHouseholdSelected", "HouseholdBindingComponent_div_7_div_28_button_1_Template", "maxSelections", "HouseholdBindingComponent_div_7_button_17_Template", "HouseholdBindingComponent_div_7_span_23_Template", "HouseholdBindingComponent_div_7_div_24_Template", "HouseholdBindingComponent_div_7_div_25_Template", "HouseholdBindingComponent_div_7_div_27_Template", "HouseholdBindingComponent_div_7_div_28_Template", "HouseholdBindingComponent_div_7_span_31_Template", "HouseholdBindingComponent_div_7_Template_button_click_33_listener", "_r6", "closeDropdown", "allowBatchSelect", "allowSearch", "HouseholdBindingComponent_div_8_Template_div_click_0_listener", "_r14", "HouseholdBindingComponent", "constructor", "cdr", "placeholder", "buildingData", "showSelectedArea", "selectionChange", "isOpen", "selectedHouseholds", "selectedByBuilding", "onChange", "value", "onTouched", "writeValue", "updateSelectedByBuilding", "registerOnChange", "fn", "registerOnTouched", "setDisabledState", "isDisabled", "ngOnInit", "Object", "keys", "generateMockData", "console", "log", "ngOnChanges", "changes", "updateFilteredHouseholds", "grouped", "for<PERSON>ach", "code", "building", "item", "find", "h", "push", "simpleMockData", "Array", "from", "_", "i", "String", "padStart", "entries", "codes", "map", "floor", "Math", "parseInt", "slice", "isSelected", "detectChanges", "onBuildingClick", "households", "filter", "toLowerCase", "includes", "event", "target", "householdCode", "newSelection", "emitChanges", "availableHouseholds", "Set", "buildingHouseholds", "selectedItems", "emit", "toggleDropdown", "isAllBuildingSelected", "every", "some", "getSelectedByBuilding", "ɵɵdirectiveInject", "ChangeDetectorRef", "selectors", "inputs", "outputs", "features", "ɵɵProvidersFeature", "provide", "useExisting", "multi", "ɵɵNgOnChangesFeature", "decls", "vars", "consts", "template", "HouseholdBindingComponent_Template", "rf", "ctx", "HouseholdBindingComponent_div_1_Template", "HouseholdBindingComponent_Template_button_click_3_listener", "HouseholdBindingComponent_div_7_Template", "HouseholdBindingComponent_div_8_Template", "ɵɵclassProp"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\shared\\components\\household-binding\\household-binding.component.ts", "C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\shared\\components\\household-binding\\household-binding.component.html"], "sourcesContent": ["import { Component, Input, Output, EventEmitter, OnInit, OnChanges, SimpleChanges, forwardRef, ChangeDetectorRef } from '@angular/core';\r\nimport { ControlValueAccessor, NG_VALUE_ACCESSOR } from '@angular/forms';\r\n\r\nexport interface HouseholdItem {\r\n  code: string;\r\n  building: string;\r\n  floor?: string;\r\n  isSelected?: boolean;\r\n  isDisabled?: boolean;\r\n}\r\n\r\nexport interface BuildingData {\r\n  [key: string]: HouseholdItem[];\r\n}\r\n\r\n// 簡化版本 - 使用字串陣列\r\nexport interface SimpleBuildingData {\r\n  [key: string]: string[];\r\n}\r\n\r\n@Component({\r\n  selector: 'app-household-binding',\r\n  templateUrl: './household-binding.component.html',\r\n  styleUrls: ['./household-binding.component.scss'],\r\n  providers: [\r\n    {\r\n      provide: NG_VALUE_ACCESSOR,\r\n      useExisting: forwardRef(() => HouseholdBindingComponent),\r\n      multi: true,\r\n    },\r\n  ],\r\n})\r\nexport class HouseholdBindingComponent implements OnInit, OnChanges, ControlValueAccessor {\r\n  @Input() placeholder: string = '請選擇戶別';\r\n  @Input() maxSelections: number | null = null;\r\n  @Input() disabled: boolean = false;\r\n  @Input() buildingData: BuildingData = {};\r\n  @Input() showSelectedArea: boolean = true;\r\n  @Input() allowSearch: boolean = true;\r\n  @Input() allowBatchSelect: boolean = true;\r\n\r\n  @Output() selectionChange = new EventEmitter<HouseholdItem[]>();\r\n\r\n  isOpen = false;\r\n  selectedBuilding = '';\r\n  searchTerm = '';\r\n  selectedHouseholds: string[] = [];\r\n  buildings: string[] = [];\r\n  filteredHouseholds: string[] = [];  // 簡化為字串陣列\r\n  selectedByBuilding: { [building: string]: string[] } = {};\r\n  // ControlValueAccessor implementation\r\n  private onChange = (value: string[]) => { };\r\n  private onTouched = () => { };\r\n\r\n  constructor(private cdr: ChangeDetectorRef) { }\r\n\r\n  writeValue(value: string[]): void {\r\n    this.selectedHouseholds = value || [];\r\n    this.updateSelectedByBuilding();\r\n  }\r\n\r\n  registerOnChange(fn: (value: string[]) => void): void {\r\n    this.onChange = fn;\r\n  }\r\n\r\n  registerOnTouched(fn: () => void): void {\r\n    this.onTouched = fn;\r\n  }\r\n  setDisabledState(isDisabled: boolean): void {\r\n    this.disabled = isDisabled;\r\n  } ngOnInit() {\r\n    // 如果沒有提供 buildingData，使用 mock 資料\r\n    if (!this.buildingData || Object.keys(this.buildingData).length === 0) {\r\n      this.buildingData = this.generateMockData();\r\n    }\r\n\r\n    this.buildings = Object.keys(this.buildingData);\r\n    console.log('Component initialized with buildings:', this.buildings);\r\n    console.log('Building data keys:', Object.keys(this.buildingData));\r\n    this.updateSelectedByBuilding();\r\n  }\r\n\r\n  ngOnChanges(changes: SimpleChanges) {\r\n    if (changes['buildingData']) {\r\n      this.buildings = Object.keys(this.buildingData);\r\n      this.updateFilteredHouseholds();\r\n      this.updateSelectedByBuilding();\r\n    }\r\n  }\r\n\r\n  private updateSelectedByBuilding() {\r\n    const grouped: { [building: string]: string[] } = {};\r\n\r\n    this.selectedHouseholds.forEach(code => {\r\n      for (const building of this.buildings) {\r\n        const item = this.buildingData[building]?.find(h => h.code === code);\r\n        if (item) {\r\n          if (!grouped[building]) grouped[building] = [];\r\n          grouped[building].push(code);\r\n          break;\r\n        }\r\n      }\r\n    });\r\n\r\n    this.selectedByBuilding = grouped;\r\n  }\r\n  private generateMockData(): BuildingData {\r\n    // 簡化版本 - 直接生成字串陣列\r\n    const simpleMockData = {\r\n      'A棟': Array.from({ length: 50 }, (_, i) => `A${String(i + 1).padStart(3, '0')}`),\r\n      'B棟': Array.from({ length: 40 }, (_, i) => `B${String(i + 1).padStart(3, '0')}`),\r\n      'C棟': Array.from({ length: 60 }, (_, i) => `C${String(i + 1).padStart(3, '0')}`),\r\n      'D棟': Array.from({ length: 35 }, (_, i) => `D${String(i + 1).padStart(3, '0')}`),\r\n      'E棟': Array.from({ length: 45 }, (_, i) => `E${String(i + 1).padStart(3, '0')}`)\r\n    };\r\n\r\n    // 轉換為 BuildingData 格式\r\n    const buildingData: BuildingData = {};\r\n    Object.entries(simpleMockData).forEach(([building, codes]) => {\r\n      buildingData[building] = codes.map(code => ({\r\n        code,\r\n        building,\r\n        floor: `${Math.floor((parseInt(code.slice(1)) - 1) / 4) + 1}F`,\r\n        isSelected: false,\r\n        isDisabled: false\r\n      }));\r\n    });\r\n\r\n    return buildingData;\r\n  } onBuildingSelect(building: string) {\r\n    console.log('Building selected:', building);\r\n    this.selectedBuilding = building;\r\n    this.searchTerm = '';\r\n    this.updateFilteredHouseholds();\r\n    console.log('Filtered households count:', this.filteredHouseholds.length);\r\n    // 手動觸發變更偵測\r\n    this.cdr.detectChanges();\r\n  }\r\n\r\n  onBuildingClick(building: string) {\r\n    console.log('Building clicked (mousedown):', building);\r\n  } updateFilteredHouseholds() {\r\n    console.log('Updating filtered households for building:', this.selectedBuilding);\r\n    if (!this.selectedBuilding) {\r\n      this.filteredHouseholds = [];\r\n      return;\r\n    }\r\n\r\n    const households = this.buildingData[this.selectedBuilding] || [];\r\n    console.log('Available households for building:', households.length);\r\n\r\n    // 提取戶別代碼並進行搜尋過濾\r\n    this.filteredHouseholds = households\r\n      .map(h => h.code)\r\n      .filter(code => code.toLowerCase().includes(this.searchTerm.toLowerCase()));\r\n\r\n    console.log('Filtered households result:', this.filteredHouseholds.length);\r\n  }\r\n\r\n  onSearchChange(event: any) {\r\n    this.searchTerm = event.target.value;\r\n    this.updateFilteredHouseholds();\r\n  }\r\n  onHouseholdToggle(householdCode: string) {\r\n    const isSelected = this.selectedHouseholds.includes(householdCode);\r\n    let newSelection: string[];\r\n\r\n    if (isSelected) {\r\n      newSelection = this.selectedHouseholds.filter(h => h !== householdCode);\r\n    } else {\r\n      if (this.maxSelections && this.selectedHouseholds.length >= this.maxSelections) {\r\n        return; // 達到最大選擇數量\r\n      }\r\n      newSelection = [...this.selectedHouseholds, householdCode];\r\n    }\r\n\r\n    this.selectedHouseholds = newSelection;\r\n    this.emitChanges();\r\n  }\r\n\r\n  onRemoveHousehold(householdCode: string) {\r\n    this.selectedHouseholds = this.selectedHouseholds.filter(h => h !== householdCode);\r\n    this.emitChanges();\r\n  }\r\n  onSelectAllFiltered() {\r\n    if (!this.selectedBuilding || this.filteredHouseholds.length === 0) return;\r\n\r\n    // 直接使用過濾後的戶別代碼\r\n    const availableHouseholds = this.filteredHouseholds;\r\n    const newSelection = [...new Set([...this.selectedHouseholds, ...availableHouseholds])];\r\n\r\n    if (this.maxSelections && newSelection.length > this.maxSelections) {\r\n      return; // 超過最大選擇數量\r\n    }\r\n\r\n    this.selectedHouseholds = newSelection;\r\n    this.emitChanges();\r\n  }\r\n  onSelectAllBuilding() {\r\n    if (!this.selectedBuilding) return;\r\n\r\n    const buildingHouseholds = this.buildingData[this.selectedBuilding]?.map(h => h.code) || [];\r\n    const newSelection = [...new Set([...this.selectedHouseholds, ...buildingHouseholds])];\r\n\r\n    if (this.maxSelections && newSelection.length > this.maxSelections) {\r\n      return; // 超過最大選擇數量\r\n    }\r\n\r\n    this.selectedHouseholds = newSelection;\r\n    this.emitChanges();\r\n  }\r\n  onUnselectAllBuilding() {\r\n    if (!this.selectedBuilding) return;\r\n\r\n    const buildingHouseholds = this.buildingData[this.selectedBuilding]?.map(h => h.code) || [];\r\n    this.selectedHouseholds = this.selectedHouseholds.filter(h => !buildingHouseholds.includes(h));\r\n    this.emitChanges();\r\n  }\r\n  onClearAll() {\r\n    this.selectedHouseholds = [];\r\n    this.emitChanges();\r\n  }\r\n\r\n  private emitChanges() {\r\n    this.updateSelectedByBuilding();\r\n    this.onChange([...this.selectedHouseholds]);\r\n    this.onTouched();\r\n\r\n    const selectedItems = this.selectedHouseholds.map(code => {\r\n      for (const building of this.buildings) {\r\n        const item = this.buildingData[building]?.find(h => h.code === code);\r\n        if (item) return item;\r\n      }\r\n      return null;\r\n    }).filter(item => item !== null) as HouseholdItem[];\r\n\r\n    this.selectionChange.emit(selectedItems);\r\n  }\r\n  toggleDropdown() {\r\n    if (!this.disabled) {\r\n      this.isOpen = !this.isOpen;\r\n      console.log('Dropdown toggled. isOpen:', this.isOpen);\r\n      console.log('Buildings available:', this.buildings);\r\n    }\r\n  }\r\n\r\n  closeDropdown() {\r\n    this.isOpen = false;\r\n  }\r\n\r\n  isHouseholdSelected(householdCode: string): boolean {\r\n    return this.selectedHouseholds.includes(householdCode);\r\n  }\r\n\r\n  canSelectMore(): boolean {\r\n    return !this.maxSelections || this.selectedHouseholds.length < this.maxSelections;\r\n  }\r\n\r\n  isAllBuildingSelected(): boolean {\r\n    if (!this.selectedBuilding) return false;\r\n    const buildingHouseholds = this.buildingData[this.selectedBuilding]\r\n      .filter(h => !h.isDisabled)\r\n      .map(h => h.code);\r\n    return buildingHouseholds.length > 0 &&\r\n      buildingHouseholds.every(code => this.selectedHouseholds.includes(code));\r\n  }\r\n  isSomeBuildingSelected(): boolean {\r\n    if (!this.selectedBuilding) return false;\r\n    const buildingHouseholds = this.buildingData[this.selectedBuilding]?.map(h => h.code) || [];\r\n    return buildingHouseholds.some(code => this.selectedHouseholds.includes(code));\r\n  }\r\n  getSelectedByBuilding(): { [building: string]: string[] } {\r\n    return this.selectedByBuilding;\r\n  }\r\n\r\n  getBuildingCount(building: string): number {\r\n    return this.buildingData[building]?.length || 0;\r\n  }\r\n\r\n  getSelectedCount(): number {\r\n    return this.selectedHouseholds.length;\r\n  }\r\n\r\n  // 輔助方法：安全地獲取建築物的已選戶別\r\n  getBuildingSelectedHouseholds(building: string): string[] {\r\n    return this.selectedByBuilding[building] || [];\r\n  }\r\n\r\n  // 輔助方法：檢查建築物是否有已選戶別\r\n  hasBuildingSelected(building: string): boolean {\r\n    return !!(this.selectedByBuilding[building] && this.selectedByBuilding[building].length > 0);\r\n  }\r\n}\r\n", "<div class=\"household-binding-container\">\r\n  <!-- 已選擇戶別顯示區域 -->\r\n  <div *ngIf=\"showSelectedArea && selectedHouseholds.length > 0\" class=\"selected-households-area\">\r\n    <div class=\"selected-header\">\r\n      <div class=\"selected-info\">\r\n        <nb-icon icon=\"people-outline\" class=\"text-primary\"></nb-icon>\r\n        <span class=\"selected-count\">已選擇戶別 ({{getSelectedCount()}})</span>\r\n      </div>\r\n      <button type=\"button\" class=\"btn btn-outline-danger btn-sm\" [disabled]=\"disabled\" (click)=\"onClearAll()\">\r\n        清空全部\r\n      </button>\r\n    </div>\r\n    <div class=\"selected-content\">\r\n      <div *ngFor=\"let building of buildings\" class=\"building-group\">\r\n        <ng-container *ngIf=\"hasBuildingSelected(building)\">\r\n          <div class=\"building-label\">{{building}}:</div>\r\n          <div class=\"households-tags\">\r\n            <span *ngFor=\"let householdCode of getBuildingSelectedHouseholds(building)\" class=\"household-tag\">\r\n              {{householdCode}}\r\n              <button type=\"button\" class=\"remove-btn\" [disabled]=\"disabled\" (click)=\"onRemoveHousehold(householdCode)\">\r\n                <nb-icon icon=\"close-outline\"></nb-icon>\r\n              </button>\r\n            </span>\r\n          </div>\r\n        </ng-container>\r\n      </div>\r\n    </div>\r\n  </div>\r\n  <!-- 選擇器 -->\r\n  <div class=\"selector-container\" style=\"position: relative;\">\r\n    <button type=\"button\" class=\"selector-button\" [class.disabled]=\"disabled\" [disabled]=\"disabled\"\r\n      (click)=\"toggleDropdown()\"\r\n      style=\"width: 100%; display: flex; align-items: center; justify-content: space-between; padding: 0.5rem 0.75rem; border: 1px solid #ced4da; border-radius: 0.375rem; background-color: #fff; cursor: pointer;\">\r\n      <span class=\"selector-text\">\r\n        {{getSelectedCount() > 0 ? '已選擇 ' + getSelectedCount() + ' 個戶別' : placeholder}}\r\n      </span>\r\n      <nb-icon icon=\"chevron-down-outline\" [class.rotated]=\"isOpen\" class=\"chevron-icon\">\r\n      </nb-icon>\r\n    </button> <!-- 下拉選單 --> <!-- 下拉選單 - 完全簡化版本 --> <!-- 下拉選單 -->\r\n    <div *ngIf=\"isOpen\"\r\n      style=\"position: absolute; top: calc(100% + 4px); left: 0; right: 0; z-index: 99999; background: white; border: 1px solid #ced4da; border-radius: 8px; box-shadow: 0 4px 12px rgba(0,0,0,0.15); padding: 0; margin: 0; max-height: 400px; overflow: hidden;\">\r\n\r\n      <!-- 標題區域 -->\r\n      <div style=\"padding: 12px 16px; border-bottom: 1px solid #e9ecef; background-color: #f8f9fa;\">\r\n        <div style=\"display: flex; align-items: center; justify-content: space-between;\">\r\n          <div style=\"display: flex; align-items: center; gap: 8px;\">\r\n            <nb-icon icon=\"home-outline\" style=\"color: #007bff;\"></nb-icon>\r\n            <span style=\"font-weight: 500; color: #495057;\">選擇戶別</span>\r\n            <span style=\"font-size: 0.875rem; color: #6c757d;\">({{buildings.length}} 個棟別)</span>\r\n          </div>\r\n          <span style=\"font-size: 0.875rem; color: #6c757d;\">已選擇: {{getSelectedCount()}}</span>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 主要內容區域 -->\r\n      <div style=\"display: flex; height: 320px;\">\r\n\r\n        <!-- 棟別選擇側邊欄 -->\r\n        <div style=\"width: 40%; border-right: 1px solid #e9ecef; background-color: #f8f9fa;\">\r\n          <div style=\"padding: 12px 16px; border-bottom: 1px solid #e9ecef;\">\r\n            <h6 style=\"margin: 0; font-size: 0.875rem; font-weight: 500; color: #495057;\">棟別列表</h6>\r\n          </div>\r\n          <div style=\"max-height: 268px; overflow-y: auto;\">\r\n            <button *ngFor=\"let building of buildings\" type=\"button\" (click)=\"onBuildingSelect(building)\"\r\n              [style.background-color]=\"selectedBuilding === building ? '#e3f2fd' : 'transparent'\"\r\n              [style.border-left]=\"selectedBuilding === building ? '3px solid #007bff' : '3px solid transparent'\"\r\n              style=\"width: 100%; text-align: left; padding: 12px 16px; border: none; cursor: pointer; transition: all 0.15s ease; display: flex; align-items: center; justify-content: space-between;\">\r\n              <span style=\"font-weight: 500; color: #495057;\">{{building}}</span>\r\n              <span\r\n                style=\"font-size: 0.75rem; color: #6c757d; background-color: #e9ecef; padding: 2px 6px; border-radius: 10px;\">{{getBuildingCount(building)}}戶</span>\r\n            </button>\r\n          </div>\r\n        </div>        <!-- 戶別選擇主區域 -->\r\n        <div style=\"flex: 1; display: flex; flex-direction: column;\">\r\n          <div style=\"padding: 12px 16px; border-bottom: 1px solid #e9ecef;\">\r\n            <div style=\"display: flex; align-items: center; justify-content: space-between; margin-bottom: 8px;\">\r\n              <h6 style=\"margin: 0; font-size: 0.875rem; font-weight: 500; color: #495057;\">\r\n                戶別選擇\r\n                <span *ngIf=\"selectedBuilding\" style=\"color: #007bff;\">({{selectedBuilding}})</span>\r\n              </h6>              <div *ngIf=\"allowBatchSelect && selectedBuilding && filteredHouseholds.length > 0\"\r\n                style=\"display: flex; gap: 4px;\">\r\n                <button type=\"button\" [disabled]=\"!canSelectMore()\" (click)=\"onSelectAllFiltered()\"\r\n                  [style.opacity]=\"canSelectMore() ? '1' : '0.5'\"\r\n                  style=\"padding: 4px 8px; font-size: 0.75rem; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer;\">\r\n                  全選當前\r\n                </button>\r\n                <button type=\"button\" [disabled]=\"!canSelectMore()\" (click)=\"onSelectAllBuilding()\"\r\n                  [style.opacity]=\"canSelectMore() ? '1' : '0.5'\"\r\n                  style=\"padding: 4px 8px; font-size: 0.75rem; background: #28a745; color: white; border: none; border-radius: 4px; cursor: pointer;\">\r\n                  全選{{selectedBuilding}}\r\n                </button>\r\n                <button type=\"button\" *ngIf=\"isSomeBuildingSelected()\" (click)=\"onUnselectAllBuilding()\"\r\n                  style=\"padding: 4px 8px; font-size: 0.75rem; background: #6c757d; color: white; border: none; border-radius: 4px; cursor: pointer;\">\r\n                  清除\r\n                </button>\r\n              </div>\r\n            </div>\r\n            \r\n            <!-- 搜尋框 -->\r\n            <div *ngIf=\"allowSearch && selectedBuilding\" style=\"margin-top: 8px;\">\r\n              <div style=\"position: relative;\">\r\n                <input type=\"text\" \r\n                  [(ngModel)]=\"searchTerm\"\r\n                  (input)=\"onSearchChange($event)\"\r\n                  placeholder=\"搜尋戶別代碼...\"\r\n                  style=\"width: 100%; padding: 6px 32px 6px 12px; border: 1px solid #ced4da; border-radius: 4px; font-size: 0.875rem; outline: none;\">\r\n                <nb-icon icon=\"search-outline\" \r\n                  style=\"position: absolute; right: 10px; top: 50%; transform: translateY(-50%); color: #6c757d; font-size: 0.875rem;\"></nb-icon>\r\n              </div>\r\n              <div *ngIf=\"searchTerm && filteredHouseholds.length === 0\" style=\"font-size: 0.75rem; color: #dc3545; margin-top: 4px;\">\r\n                找不到符合 \"{{searchTerm}}\" 的戶別\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 戶別網格或空狀態 -->\r\n          <div style=\"flex: 1; padding: 16px; overflow-y: auto;\">\r\n            <div *ngIf=\"!selectedBuilding\" style=\"text-align: center; padding: 40px 20px; color: #6c757d;\">\r\n              <nb-icon icon=\"home-outline\" style=\"font-size: 2rem; margin-bottom: 8px; opacity: 0.5;\"></nb-icon>\r\n              <p style=\"margin: 0; font-size: 0.875rem;\">請先選擇棟別</p>\r\n            </div>\r\n\r\n            <div *ngIf=\"selectedBuilding && filteredHouseholds.length > 0\"\r\n              style=\"display: grid; grid-template-columns: repeat(auto-fill, minmax(80px, 1fr)); gap: 8px;\">\r\n              <button *ngFor=\"let householdCode of filteredHouseholds\" type=\"button\"\r\n                (click)=\"onHouseholdToggle(householdCode)\"\r\n                [disabled]=\"(!canSelectMore() && !isHouseholdSelected(householdCode))\"\r\n                [style.background-color]=\"isHouseholdSelected(householdCode) ? '#007bff' : '#fff'\"\r\n                [style.color]=\"isHouseholdSelected(householdCode) ? '#fff' : '#495057'\"\r\n                [style.border-color]=\"isHouseholdSelected(householdCode) ? '#007bff' : '#ced4da'\"\r\n                [style.opacity]=\"(!canSelectMore() && !isHouseholdSelected(householdCode)) ? '0.5' : '1'\"\r\n                style=\"padding: 8px 4px; border: 1px solid; border-radius: 4px; cursor: pointer; transition: all 0.15s ease; font-size: 0.75rem; text-align: center; min-height: 32px;\">\r\n                {{householdCode}}\r\n              </button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 底部操作區 -->\r\n      <div\r\n        style=\"padding: 12px 16px; border-top: 1px solid #e9ecef; background-color: #f8f9fa; display: flex; align-items: center; justify-content: space-between;\">\r\n        <div style=\"font-size: 0.75rem; color: #6c757d;\">\r\n          <span *ngIf=\"maxSelections\">最多可選 {{maxSelections}} 個 | </span>\r\n          已選擇: {{getSelectedCount()}} 個戶別\r\n        </div>\r\n        <button type=\"button\" (click)=\"closeDropdown()\"\r\n          style=\"padding: 6px 16px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 0.875rem;\">\r\n          確定\r\n        </button>\r\n      </div>\r\n    </div>\r\n  </div>\r\n\r\n  <!-- 點擊外部關閉下拉選單 -->\r\n  <div *ngIf=\"isOpen\" class=\"backdrop\" (click)=\"closeDropdown()\"></div>"], "mappings": "AAAA,SAAmCA,YAAY,EAAoCC,UAAU,QAA2B,eAAe;AACvI,SAA+BC,iBAAiB,QAAQ,gBAAgB;;;;;;;;ICgB5DC,EAAA,CAAAC,cAAA,eAAkG;IAChGD,EAAA,CAAAE,MAAA,GACA;IAAAF,EAAA,CAAAC,cAAA,iBAA0G;IAA3CD,EAAA,CAAAG,UAAA,mBAAAC,6FAAA;MAAA,MAAAC,gBAAA,GAAAL,EAAA,CAAAM,aAAA,CAAAC,GAAA,EAAAC,SAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAG,iBAAA,CAAAP,gBAAA,CAAgC;IAAA,EAAC;IACvGL,EAAA,CAAAa,SAAA,kBAAwC;IAE5Cb,EADE,CAAAc,YAAA,EAAS,EACJ;;;;;IAJLd,EAAA,CAAAe,SAAA,EACA;IADAf,EAAA,CAAAgB,kBAAA,MAAAX,gBAAA,MACA;IAAyCL,EAAA,CAAAe,SAAA,EAAqB;IAArBf,EAAA,CAAAiB,UAAA,aAAAR,MAAA,CAAAS,QAAA,CAAqB;;;;;IALpElB,EAAA,CAAAmB,uBAAA,GAAoD;IAClDnB,EAAA,CAAAC,cAAA,cAA4B;IAAAD,EAAA,CAAAE,MAAA,GAAa;IAAAF,EAAA,CAAAc,YAAA,EAAM;IAC/Cd,EAAA,CAAAC,cAAA,cAA6B;IAC3BD,EAAA,CAAAoB,UAAA,IAAAC,oEAAA,mBAAkG;IAMpGrB,EAAA,CAAAc,YAAA,EAAM;;;;;;IARsBd,EAAA,CAAAe,SAAA,GAAa;IAAbf,EAAA,CAAAgB,kBAAA,KAAAM,WAAA,MAAa;IAEPtB,EAAA,CAAAe,SAAA,GAA0C;IAA1Cf,EAAA,CAAAiB,UAAA,YAAAR,MAAA,CAAAc,6BAAA,CAAAD,WAAA,EAA0C;;;;;IAJhFtB,EAAA,CAAAC,cAAA,cAA+D;IAC7DD,EAAA,CAAAoB,UAAA,IAAAI,6DAAA,2BAAoD;IAWtDxB,EAAA,CAAAc,YAAA,EAAM;;;;;IAXWd,EAAA,CAAAe,SAAA,EAAmC;IAAnCf,EAAA,CAAAiB,UAAA,SAAAR,MAAA,CAAAgB,mBAAA,CAAAH,WAAA,EAAmC;;;;;;IAVpDtB,EAFJ,CAAAC,cAAA,aAAgG,aACjE,cACA;IACzBD,EAAA,CAAAa,SAAA,kBAA8D;IAC9Db,EAAA,CAAAC,cAAA,eAA6B;IAAAD,EAAA,CAAAE,MAAA,GAA8B;IAC7DF,EAD6D,CAAAc,YAAA,EAAO,EAC9D;IACNd,EAAA,CAAAC,cAAA,iBAAyG;IAAvBD,EAAA,CAAAG,UAAA,mBAAAuB,iEAAA;MAAA1B,EAAA,CAAAM,aAAA,CAAAqB,GAAA;MAAA,MAAAlB,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAmB,UAAA,EAAY;IAAA,EAAC;IACtG5B,EAAA,CAAAE,MAAA,iCACF;IACFF,EADE,CAAAc,YAAA,EAAS,EACL;IACNd,EAAA,CAAAC,cAAA,cAA8B;IAC5BD,EAAA,CAAAoB,UAAA,IAAAS,8CAAA,kBAA+D;IAcnE7B,EADE,CAAAc,YAAA,EAAM,EACF;;;;IArB6Bd,EAAA,CAAAe,SAAA,GAA8B;IAA9Bf,EAAA,CAAAgB,kBAAA,qCAAAP,MAAA,CAAAqB,gBAAA,QAA8B;IAED9B,EAAA,CAAAe,SAAA,EAAqB;IAArBf,EAAA,CAAAiB,UAAA,aAAAR,MAAA,CAAAS,QAAA,CAAqB;IAKvDlB,EAAA,CAAAe,SAAA,GAAY;IAAZf,EAAA,CAAAiB,UAAA,YAAAR,MAAA,CAAAsB,SAAA,CAAY;;;;;;IAkDhC/B,EAAA,CAAAC,cAAA,iBAG4L;IAHnID,EAAA,CAAAG,UAAA,mBAAA6B,2EAAA;MAAA,MAAAC,WAAA,GAAAjC,EAAA,CAAAM,aAAA,CAAA4B,GAAA,EAAA1B,SAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAA0B,gBAAA,CAAAF,WAAA,CAA0B;IAAA,EAAC;IAI3FjC,EAAA,CAAAC,cAAA,eAAgD;IAAAD,EAAA,CAAAE,MAAA,GAAY;IAAAF,EAAA,CAAAc,YAAA,EAAO;IACnEd,EAAA,CAAAC,cAAA,eACgH;IAAAD,EAAA,CAAAE,MAAA,GAA+B;IACjJF,EADiJ,CAAAc,YAAA,EAAO,EAC/I;;;;;IALPd,EADA,CAAAoC,WAAA,qBAAA3B,MAAA,CAAA4B,gBAAA,KAAAJ,WAAA,6BAAoF,gBAAAxB,MAAA,CAAA4B,gBAAA,KAAAJ,WAAA,iDACe;IAEnDjC,EAAA,CAAAe,SAAA,GAAY;IAAZf,EAAA,CAAAsC,iBAAA,CAAAL,WAAA,CAAY;IAEoDjC,EAAA,CAAAe,SAAA,GAA+B;IAA/Bf,EAAA,CAAAgB,kBAAA,KAAAP,MAAA,CAAA8B,gBAAA,CAAAN,WAAA,YAA+B;;;;;IAS7IjC,EAAA,CAAAC,cAAA,eAAuD;IAAAD,EAAA,CAAAE,MAAA,GAAsB;IAAAF,EAAA,CAAAc,YAAA,EAAO;;;;IAA7Bd,EAAA,CAAAe,SAAA,EAAsB;IAAtBf,EAAA,CAAAgB,kBAAA,MAAAP,MAAA,CAAA4B,gBAAA,MAAsB;;;;;;IAa7ErC,EAAA,CAAAC,cAAA,iBACsI;IAD/ED,EAAA,CAAAG,UAAA,mBAAAqC,iFAAA;MAAAxC,EAAA,CAAAM,aAAA,CAAAmC,IAAA;MAAA,MAAAhC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAiC,qBAAA,EAAuB;IAAA,EAAC;IAEtF1C,EAAA,CAAAE,MAAA,qBACF;IAAAF,EAAA,CAAAc,YAAA,EAAS;;;;;;IAbTd,EAFiB,CAAAC,cAAA,cACgB,iBAGqG;IAFlFD,EAAA,CAAAG,UAAA,mBAAAwC,wEAAA;MAAA3C,EAAA,CAAAM,aAAA,CAAAsC,GAAA;MAAA,MAAAnC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAoC,mBAAA,EAAqB;IAAA,EAAC;IAGjF7C,EAAA,CAAAE,MAAA,iCACF;IAAAF,EAAA,CAAAc,YAAA,EAAS;IACTd,EAAA,CAAAC,cAAA,iBAEsI;IAFlFD,EAAA,CAAAG,UAAA,mBAAA2C,wEAAA;MAAA9C,EAAA,CAAAM,aAAA,CAAAsC,GAAA;MAAA,MAAAnC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAsC,mBAAA,EAAqB;IAAA,EAAC;IAGjF/C,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAc,YAAA,EAAS;IACTd,EAAA,CAAAoB,UAAA,IAAA4B,wDAAA,qBACsI;IAGxIhD,EAAA,CAAAc,YAAA,EAAM;;;;IAbFd,EAAA,CAAAe,SAAA,EAA+C;IAA/Cf,EAAA,CAAAoC,WAAA,YAAA3B,MAAA,CAAAwC,aAAA,iBAA+C;IAD3BjD,EAAA,CAAAiB,UAAA,cAAAR,MAAA,CAAAwC,aAAA,GAA6B;IAMjDjD,EAAA,CAAAe,SAAA,GAA+C;IAA/Cf,EAAA,CAAAoC,WAAA,YAAA3B,MAAA,CAAAwC,aAAA,iBAA+C;IAD3BjD,EAAA,CAAAiB,UAAA,cAAAR,MAAA,CAAAwC,aAAA,GAA6B;IAGjDjD,EAAA,CAAAe,SAAA,EACF;IADEf,EAAA,CAAAgB,kBAAA,kBAAAP,MAAA,CAAA4B,gBAAA,MACF;IACuBrC,EAAA,CAAAe,SAAA,EAA8B;IAA9Bf,EAAA,CAAAiB,UAAA,SAAAR,MAAA,CAAAyC,sBAAA,GAA8B;;;;;IAkBvDlD,EAAA,CAAAC,cAAA,cAAwH;IACtHD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAc,YAAA,EAAM;;;;IADJd,EAAA,CAAAe,SAAA,EACF;IADEf,EAAA,CAAAgB,kBAAA,uCAAAP,MAAA,CAAA0C,UAAA,2BACF;;;;;;IAVEnD,EAFJ,CAAAC,cAAA,cAAsE,cACnC,gBAKuG;IAHpID,EAAA,CAAAoD,gBAAA,2BAAAC,+EAAAC,MAAA;MAAAtD,EAAA,CAAAM,aAAA,CAAAiD,IAAA;MAAA,MAAA9C,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAAV,EAAA,CAAAwD,kBAAA,CAAA/C,MAAA,CAAA0C,UAAA,EAAAG,MAAA,MAAA7C,MAAA,CAAA0C,UAAA,GAAAG,MAAA;MAAA,OAAAtD,EAAA,CAAAW,WAAA,CAAA2C,MAAA;IAAA,EAAwB;IACxBtD,EAAA,CAAAG,UAAA,mBAAAsD,uEAAAH,MAAA;MAAAtD,EAAA,CAAAM,aAAA,CAAAiD,IAAA;MAAA,MAAA9C,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAiD,cAAA,CAAAJ,MAAA,CAAsB;IAAA,EAAC;IAFlCtD,EAAA,CAAAc,YAAA,EAIsI;IACtId,EAAA,CAAAa,SAAA,kBACiI;IACnIb,EAAA,CAAAc,YAAA,EAAM;IACNd,EAAA,CAAAoB,UAAA,IAAAuC,qDAAA,kBAAwH;IAG1H3D,EAAA,CAAAc,YAAA,EAAM;;;;IAVAd,EAAA,CAAAe,SAAA,GAAwB;IAAxBf,EAAA,CAAA4D,gBAAA,YAAAnD,MAAA,CAAA0C,UAAA,CAAwB;IAOtBnD,EAAA,CAAAe,SAAA,GAAmD;IAAnDf,EAAA,CAAAiB,UAAA,SAAAR,MAAA,CAAA0C,UAAA,IAAA1C,MAAA,CAAAoD,kBAAA,CAAAC,MAAA,OAAmD;;;;;IAQ3D9D,EAAA,CAAAC,cAAA,cAA+F;IAC7FD,EAAA,CAAAa,SAAA,kBAAkG;IAClGb,EAAA,CAAAC,cAAA,YAA2C;IAAAD,EAAA,CAAAE,MAAA,2CAAM;IACnDF,EADmD,CAAAc,YAAA,EAAI,EACjD;;;;;;IAIJd,EAAA,CAAAC,cAAA,iBAO0K;IANxKD,EAAA,CAAAG,UAAA,mBAAA4D,iFAAA;MAAA,MAAAC,iBAAA,GAAAhE,EAAA,CAAAM,aAAA,CAAA2D,IAAA,EAAAzD,SAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAyD,iBAAA,CAAAF,iBAAA,CAAgC;IAAA,EAAC;IAO1ChE,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAc,YAAA,EAAS;;;;;IAHPd,EAHA,CAAAoC,WAAA,qBAAA3B,MAAA,CAAA0D,mBAAA,CAAAH,iBAAA,uBAAkF,UAAAvD,MAAA,CAAA0D,mBAAA,CAAAH,iBAAA,uBACX,iBAAAvD,MAAA,CAAA0D,mBAAA,CAAAH,iBAAA,0BACU,aAAAvD,MAAA,CAAAwC,aAAA,OAAAxC,MAAA,CAAA0D,mBAAA,CAAAH,iBAAA,gBACQ;IAJzFhE,EAAA,CAAAiB,UAAA,cAAAR,MAAA,CAAAwC,aAAA,OAAAxC,MAAA,CAAA0D,mBAAA,CAAAH,iBAAA,EAAsE;IAMtEhE,EAAA,CAAAe,SAAA,EACF;IADEf,EAAA,CAAAgB,kBAAA,MAAAgD,iBAAA,MACF;;;;;IAXFhE,EAAA,CAAAC,cAAA,cACgG;IAC9FD,EAAA,CAAAoB,UAAA,IAAAgD,wDAAA,sBAO0K;IAG5KpE,EAAA,CAAAc,YAAA,EAAM;;;;IAV8Bd,EAAA,CAAAe,SAAA,EAAqB;IAArBf,EAAA,CAAAiB,UAAA,YAAAR,MAAA,CAAAoD,kBAAA,CAAqB;;;;;IAmB3D7D,EAAA,CAAAC,cAAA,WAA4B;IAAAD,EAAA,CAAAE,MAAA,GAA2B;IAAAF,EAAA,CAAAc,YAAA,EAAO;;;;IAAlCd,EAAA,CAAAe,SAAA,EAA2B;IAA3Bf,EAAA,CAAAgB,kBAAA,8BAAAP,MAAA,CAAA4D,aAAA,eAA2B;;;;;;IAlGvDrE,EANN,CAAAC,cAAA,cAC+P,cAG/J,cACX,cACpB;IACzDD,EAAA,CAAAa,SAAA,kBAA+D;IAC/Db,EAAA,CAAAC,cAAA,eAAgD;IAAAD,EAAA,CAAAE,MAAA,+BAAI;IAAAF,EAAA,CAAAc,YAAA,EAAO;IAC3Dd,EAAA,CAAAC,cAAA,eAAmD;IAAAD,EAAA,CAAAE,MAAA,GAA0B;IAC/EF,EAD+E,CAAAc,YAAA,EAAO,EAChF;IACNd,EAAA,CAAAC,cAAA,eAAmD;IAAAD,EAAA,CAAAE,MAAA,IAA2B;IAElFF,EAFkF,CAAAc,YAAA,EAAO,EACjF,EACF;IAQAd,EALN,CAAAC,cAAA,eAA2C,eAG4C,eAChB,cACa;IAAAD,EAAA,CAAAE,MAAA,gCAAI;IACpFF,EADoF,CAAAc,YAAA,EAAK,EACnF;IACNd,EAAA,CAAAC,cAAA,eAAkD;IAChDD,EAAA,CAAAoB,UAAA,KAAAkD,kDAAA,qBAG4L;IAMhMtE,EADE,CAAAc,YAAA,EAAM,EACF;IAIAd,EAHN,CAAAC,cAAA,eAA6D,eACQ,eACoC,cACrB;IAC5ED,EAAA,CAAAE,MAAA,kCACA;IAAAF,EAAA,CAAAoB,UAAA,KAAAmD,gDAAA,mBAAuD;IACzDvE,EAAA,CAAAc,YAAA,EAAK;IAAcd,EAAA,CAAAoB,UAAA,KAAAoD,+CAAA,kBACgB;IAgBrCxE,EAAA,CAAAc,YAAA,EAAM;IAGNd,EAAA,CAAAoB,UAAA,KAAAqD,+CAAA,kBAAsE;IAcxEzE,EAAA,CAAAc,YAAA,EAAM;IAGNd,EAAA,CAAAC,cAAA,eAAuD;IAMrDD,EALA,CAAAoB,UAAA,KAAAsD,+CAAA,kBAA+F,KAAAC,+CAAA,kBAMC;IActG3E,EAFI,CAAAc,YAAA,EAAM,EACF,EACF;IAKJd,EAFF,CAAAC,cAAA,eAC4J,eACzG;IAC/CD,EAAA,CAAAoB,UAAA,KAAAwD,gDAAA,mBAA4B;IAC5B5E,EAAA,CAAAE,MAAA,IACF;IAAAF,EAAA,CAAAc,YAAA,EAAM;IACNd,EAAA,CAAAC,cAAA,kBACwI;IADlHD,EAAA,CAAAG,UAAA,mBAAA0E,kEAAA;MAAA7E,EAAA,CAAAM,aAAA,CAAAwE,GAAA;MAAA,MAAArE,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAsE,aAAA,EAAe;IAAA,EAAC;IAE7C/E,EAAA,CAAAE,MAAA,sBACF;IAEJF,EAFI,CAAAc,YAAA,EAAS,EACL,EACF;;;;IAvGqDd,EAAA,CAAAe,SAAA,GAA0B;IAA1Bf,EAAA,CAAAgB,kBAAA,MAAAP,MAAA,CAAAsB,SAAA,CAAA+B,MAAA,yBAA0B;IAE5B9D,EAAA,CAAAe,SAAA,GAA2B;IAA3Bf,EAAA,CAAAgB,kBAAA,yBAAAP,MAAA,CAAAqB,gBAAA,OAA2B;IAa/C9B,EAAA,CAAAe,SAAA,GAAY;IAAZf,EAAA,CAAAiB,UAAA,YAAAR,MAAA,CAAAsB,SAAA,CAAY;IAe9B/B,EAAA,CAAAe,SAAA,GAAsB;IAAtBf,EAAA,CAAAiB,UAAA,SAAAR,MAAA,CAAA4B,gBAAA,CAAsB;IACNrC,EAAA,CAAAe,SAAA,EAA2E;IAA3Ef,EAAA,CAAAiB,UAAA,SAAAR,MAAA,CAAAuE,gBAAA,IAAAvE,MAAA,CAAA4B,gBAAA,IAAA5B,MAAA,CAAAoD,kBAAA,CAAAC,MAAA,KAA2E;IAoBhG9D,EAAA,CAAAe,SAAA,EAAqC;IAArCf,EAAA,CAAAiB,UAAA,SAAAR,MAAA,CAAAwE,WAAA,IAAAxE,MAAA,CAAA4B,gBAAA,CAAqC;IAkBrCrC,EAAA,CAAAe,SAAA,GAAuB;IAAvBf,EAAA,CAAAiB,UAAA,UAAAR,MAAA,CAAA4B,gBAAA,CAAuB;IAKvBrC,EAAA,CAAAe,SAAA,EAAuD;IAAvDf,EAAA,CAAAiB,UAAA,SAAAR,MAAA,CAAA4B,gBAAA,IAAA5B,MAAA,CAAAoD,kBAAA,CAAAC,MAAA,KAAuD;IAqBxD9D,EAAA,CAAAe,SAAA,GAAmB;IAAnBf,EAAA,CAAAiB,UAAA,SAAAR,MAAA,CAAA4D,aAAA,CAAmB;IAC1BrE,EAAA,CAAAe,SAAA,EACF;IADEf,EAAA,CAAAgB,kBAAA,0BAAAP,MAAA,CAAAqB,gBAAA,2BACF;;;;;;IAUN9B,EAAA,CAAAC,cAAA,cAA+D;IAA1BD,EAAA,CAAAG,UAAA,mBAAA+E,8DAAA;MAAAlF,EAAA,CAAAM,aAAA,CAAA6E,IAAA;MAAA,MAAA1E,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAsE,aAAA,EAAe;IAAA,EAAC;IAAC/E,EAAA,CAAAc,YAAA,EAAM;;;AD3HvE,OAAM,MAAOsE,yBAAyB;EAsBpCC,YAAoBC,GAAsB;IAAtB,KAAAA,GAAG,GAAHA,GAAG;IArBd,KAAAC,WAAW,GAAW,OAAO;IAC7B,KAAAlB,aAAa,GAAkB,IAAI;IACnC,KAAAnD,QAAQ,GAAY,KAAK;IACzB,KAAAsE,YAAY,GAAiB,EAAE;IAC/B,KAAAC,gBAAgB,GAAY,IAAI;IAChC,KAAAR,WAAW,GAAY,IAAI;IAC3B,KAAAD,gBAAgB,GAAY,IAAI;IAE/B,KAAAU,eAAe,GAAG,IAAI7F,YAAY,EAAmB;IAE/D,KAAA8F,MAAM,GAAG,KAAK;IACd,KAAAtD,gBAAgB,GAAG,EAAE;IACrB,KAAAc,UAAU,GAAG,EAAE;IACf,KAAAyC,kBAAkB,GAAa,EAAE;IACjC,KAAA7D,SAAS,GAAa,EAAE;IACxB,KAAA8B,kBAAkB,GAAa,EAAE,CAAC,CAAE;IACpC,KAAAgC,kBAAkB,GAAqC,EAAE;IACzD;IACQ,KAAAC,QAAQ,GAAIC,KAAe,IAAI,CAAG,CAAC;IACnC,KAAAC,SAAS,GAAG,MAAK,CAAG,CAAC;EAEiB;EAE9CC,UAAUA,CAACF,KAAe;IACxB,IAAI,CAACH,kBAAkB,GAAGG,KAAK,IAAI,EAAE;IACrC,IAAI,CAACG,wBAAwB,EAAE;EACjC;EAEAC,gBAAgBA,CAACC,EAA6B;IAC5C,IAAI,CAACN,QAAQ,GAAGM,EAAE;EACpB;EAEAC,iBAAiBA,CAACD,EAAc;IAC9B,IAAI,CAACJ,SAAS,GAAGI,EAAE;EACrB;EACAE,gBAAgBA,CAACC,UAAmB;IAClC,IAAI,CAACrF,QAAQ,GAAGqF,UAAU;EAC5B;EAAEC,QAAQA,CAAA;IACR;IACA,IAAI,CAAC,IAAI,CAAChB,YAAY,IAAIiB,MAAM,CAACC,IAAI,CAAC,IAAI,CAAClB,YAAY,CAAC,CAAC1B,MAAM,KAAK,CAAC,EAAE;MACrE,IAAI,CAAC0B,YAAY,GAAG,IAAI,CAACmB,gBAAgB,EAAE;IAC7C;IAEA,IAAI,CAAC5E,SAAS,GAAG0E,MAAM,CAACC,IAAI,CAAC,IAAI,CAAClB,YAAY,CAAC;IAC/CoB,OAAO,CAACC,GAAG,CAAC,uCAAuC,EAAE,IAAI,CAAC9E,SAAS,CAAC;IACpE6E,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEJ,MAAM,CAACC,IAAI,CAAC,IAAI,CAAClB,YAAY,CAAC,CAAC;IAClE,IAAI,CAACU,wBAAwB,EAAE;EACjC;EAEAY,WAAWA,CAACC,OAAsB;IAChC,IAAIA,OAAO,CAAC,cAAc,CAAC,EAAE;MAC3B,IAAI,CAAChF,SAAS,GAAG0E,MAAM,CAACC,IAAI,CAAC,IAAI,CAAClB,YAAY,CAAC;MAC/C,IAAI,CAACwB,wBAAwB,EAAE;MAC/B,IAAI,CAACd,wBAAwB,EAAE;IACjC;EACF;EAEQA,wBAAwBA,CAAA;IAC9B,MAAMe,OAAO,GAAqC,EAAE;IAEpD,IAAI,CAACrB,kBAAkB,CAACsB,OAAO,CAACC,IAAI,IAAG;MACrC,KAAK,MAAMC,QAAQ,IAAI,IAAI,CAACrF,SAAS,EAAE;QACrC,MAAMsF,IAAI,GAAG,IAAI,CAAC7B,YAAY,CAAC4B,QAAQ,CAAC,EAAEE,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACJ,IAAI,KAAKA,IAAI,CAAC;QACpE,IAAIE,IAAI,EAAE;UACR,IAAI,CAACJ,OAAO,CAACG,QAAQ,CAAC,EAAEH,OAAO,CAACG,QAAQ,CAAC,GAAG,EAAE;UAC9CH,OAAO,CAACG,QAAQ,CAAC,CAACI,IAAI,CAACL,IAAI,CAAC;UAC5B;QACF;MACF;IACF,CAAC,CAAC;IAEF,IAAI,CAACtB,kBAAkB,GAAGoB,OAAO;EACnC;EACQN,gBAAgBA,CAAA;IACtB;IACA,MAAMc,cAAc,GAAG;MACrB,IAAI,EAAEC,KAAK,CAACC,IAAI,CAAC;QAAE7D,MAAM,EAAE;MAAE,CAAE,EAAE,CAAC8D,CAAC,EAAEC,CAAC,KAAK,IAAIC,MAAM,CAACD,CAAC,GAAG,CAAC,CAAC,CAACE,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE,CAAC;MAChF,IAAI,EAAEL,KAAK,CAACC,IAAI,CAAC;QAAE7D,MAAM,EAAE;MAAE,CAAE,EAAE,CAAC8D,CAAC,EAAEC,CAAC,KAAK,IAAIC,MAAM,CAACD,CAAC,GAAG,CAAC,CAAC,CAACE,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE,CAAC;MAChF,IAAI,EAAEL,KAAK,CAACC,IAAI,CAAC;QAAE7D,MAAM,EAAE;MAAE,CAAE,EAAE,CAAC8D,CAAC,EAAEC,CAAC,KAAK,IAAIC,MAAM,CAACD,CAAC,GAAG,CAAC,CAAC,CAACE,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE,CAAC;MAChF,IAAI,EAAEL,KAAK,CAACC,IAAI,CAAC;QAAE7D,MAAM,EAAE;MAAE,CAAE,EAAE,CAAC8D,CAAC,EAAEC,CAAC,KAAK,IAAIC,MAAM,CAACD,CAAC,GAAG,CAAC,CAAC,CAACE,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE,CAAC;MAChF,IAAI,EAAEL,KAAK,CAACC,IAAI,CAAC;QAAE7D,MAAM,EAAE;MAAE,CAAE,EAAE,CAAC8D,CAAC,EAAEC,CAAC,KAAK,IAAIC,MAAM,CAACD,CAAC,GAAG,CAAC,CAAC,CAACE,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;KAChF;IAED;IACA,MAAMvC,YAAY,GAAiB,EAAE;IACrCiB,MAAM,CAACuB,OAAO,CAACP,cAAc,CAAC,CAACP,OAAO,CAAC,CAAC,CAACE,QAAQ,EAAEa,KAAK,CAAC,KAAI;MAC3DzC,YAAY,CAAC4B,QAAQ,CAAC,GAAGa,KAAK,CAACC,GAAG,CAACf,IAAI,KAAK;QAC1CA,IAAI;QACJC,QAAQ;QACRe,KAAK,EAAE,GAAGC,IAAI,CAACD,KAAK,CAAC,CAACE,QAAQ,CAAClB,IAAI,CAACmB,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,GAAG;QAC9DC,UAAU,EAAE,KAAK;QACjBhC,UAAU,EAAE;OACb,CAAC,CAAC;IACL,CAAC,CAAC;IAEF,OAAOf,YAAY;EACrB;EAAErD,gBAAgBA,CAACiF,QAAgB;IACjCR,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEO,QAAQ,CAAC;IAC3C,IAAI,CAAC/E,gBAAgB,GAAG+E,QAAQ;IAChC,IAAI,CAACjE,UAAU,GAAG,EAAE;IACpB,IAAI,CAAC6D,wBAAwB,EAAE;IAC/BJ,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAE,IAAI,CAAChD,kBAAkB,CAACC,MAAM,CAAC;IACzE;IACA,IAAI,CAACwB,GAAG,CAACkD,aAAa,EAAE;EAC1B;EAEAC,eAAeA,CAACrB,QAAgB;IAC9BR,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAEO,QAAQ,CAAC;EACxD;EAAEJ,wBAAwBA,CAAA;IACxBJ,OAAO,CAACC,GAAG,CAAC,4CAA4C,EAAE,IAAI,CAACxE,gBAAgB,CAAC;IAChF,IAAI,CAAC,IAAI,CAACA,gBAAgB,EAAE;MAC1B,IAAI,CAACwB,kBAAkB,GAAG,EAAE;MAC5B;IACF;IAEA,MAAM6E,UAAU,GAAG,IAAI,CAAClD,YAAY,CAAC,IAAI,CAACnD,gBAAgB,CAAC,IAAI,EAAE;IACjEuE,OAAO,CAACC,GAAG,CAAC,oCAAoC,EAAE6B,UAAU,CAAC5E,MAAM,CAAC;IAEpE;IACA,IAAI,CAACD,kBAAkB,GAAG6E,UAAU,CACjCR,GAAG,CAACX,CAAC,IAAIA,CAAC,CAACJ,IAAI,CAAC,CAChBwB,MAAM,CAACxB,IAAI,IAAIA,IAAI,CAACyB,WAAW,EAAE,CAACC,QAAQ,CAAC,IAAI,CAAC1F,UAAU,CAACyF,WAAW,EAAE,CAAC,CAAC;IAE7EhC,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAE,IAAI,CAAChD,kBAAkB,CAACC,MAAM,CAAC;EAC5E;EAEAJ,cAAcA,CAACoF,KAAU;IACvB,IAAI,CAAC3F,UAAU,GAAG2F,KAAK,CAACC,MAAM,CAAChD,KAAK;IACpC,IAAI,CAACiB,wBAAwB,EAAE;EACjC;EACA9C,iBAAiBA,CAAC8E,aAAqB;IACrC,MAAMT,UAAU,GAAG,IAAI,CAAC3C,kBAAkB,CAACiD,QAAQ,CAACG,aAAa,CAAC;IAClE,IAAIC,YAAsB;IAE1B,IAAIV,UAAU,EAAE;MACdU,YAAY,GAAG,IAAI,CAACrD,kBAAkB,CAAC+C,MAAM,CAACpB,CAAC,IAAIA,CAAC,KAAKyB,aAAa,CAAC;IACzE,CAAC,MAAM;MACL,IAAI,IAAI,CAAC3E,aAAa,IAAI,IAAI,CAACuB,kBAAkB,CAAC9B,MAAM,IAAI,IAAI,CAACO,aAAa,EAAE;QAC9E,OAAO,CAAC;MACV;MACA4E,YAAY,GAAG,CAAC,GAAG,IAAI,CAACrD,kBAAkB,EAAEoD,aAAa,CAAC;IAC5D;IAEA,IAAI,CAACpD,kBAAkB,GAAGqD,YAAY;IACtC,IAAI,CAACC,WAAW,EAAE;EACpB;EAEAtI,iBAAiBA,CAACoI,aAAqB;IACrC,IAAI,CAACpD,kBAAkB,GAAG,IAAI,CAACA,kBAAkB,CAAC+C,MAAM,CAACpB,CAAC,IAAIA,CAAC,KAAKyB,aAAa,CAAC;IAClF,IAAI,CAACE,WAAW,EAAE;EACpB;EACArG,mBAAmBA,CAAA;IACjB,IAAI,CAAC,IAAI,CAACR,gBAAgB,IAAI,IAAI,CAACwB,kBAAkB,CAACC,MAAM,KAAK,CAAC,EAAE;IAEpE;IACA,MAAMqF,mBAAmB,GAAG,IAAI,CAACtF,kBAAkB;IACnD,MAAMoF,YAAY,GAAG,CAAC,GAAG,IAAIG,GAAG,CAAC,CAAC,GAAG,IAAI,CAACxD,kBAAkB,EAAE,GAAGuD,mBAAmB,CAAC,CAAC,CAAC;IAEvF,IAAI,IAAI,CAAC9E,aAAa,IAAI4E,YAAY,CAACnF,MAAM,GAAG,IAAI,CAACO,aAAa,EAAE;MAClE,OAAO,CAAC;IACV;IAEA,IAAI,CAACuB,kBAAkB,GAAGqD,YAAY;IACtC,IAAI,CAACC,WAAW,EAAE;EACpB;EACAnG,mBAAmBA,CAAA;IACjB,IAAI,CAAC,IAAI,CAACV,gBAAgB,EAAE;IAE5B,MAAMgH,kBAAkB,GAAG,IAAI,CAAC7D,YAAY,CAAC,IAAI,CAACnD,gBAAgB,CAAC,EAAE6F,GAAG,CAACX,CAAC,IAAIA,CAAC,CAACJ,IAAI,CAAC,IAAI,EAAE;IAC3F,MAAM8B,YAAY,GAAG,CAAC,GAAG,IAAIG,GAAG,CAAC,CAAC,GAAG,IAAI,CAACxD,kBAAkB,EAAE,GAAGyD,kBAAkB,CAAC,CAAC,CAAC;IAEtF,IAAI,IAAI,CAAChF,aAAa,IAAI4E,YAAY,CAACnF,MAAM,GAAG,IAAI,CAACO,aAAa,EAAE;MAClE,OAAO,CAAC;IACV;IAEA,IAAI,CAACuB,kBAAkB,GAAGqD,YAAY;IACtC,IAAI,CAACC,WAAW,EAAE;EACpB;EACAxG,qBAAqBA,CAAA;IACnB,IAAI,CAAC,IAAI,CAACL,gBAAgB,EAAE;IAE5B,MAAMgH,kBAAkB,GAAG,IAAI,CAAC7D,YAAY,CAAC,IAAI,CAACnD,gBAAgB,CAAC,EAAE6F,GAAG,CAACX,CAAC,IAAIA,CAAC,CAACJ,IAAI,CAAC,IAAI,EAAE;IAC3F,IAAI,CAACvB,kBAAkB,GAAG,IAAI,CAACA,kBAAkB,CAAC+C,MAAM,CAACpB,CAAC,IAAI,CAAC8B,kBAAkB,CAACR,QAAQ,CAACtB,CAAC,CAAC,CAAC;IAC9F,IAAI,CAAC2B,WAAW,EAAE;EACpB;EACAtH,UAAUA,CAAA;IACR,IAAI,CAACgE,kBAAkB,GAAG,EAAE;IAC5B,IAAI,CAACsD,WAAW,EAAE;EACpB;EAEQA,WAAWA,CAAA;IACjB,IAAI,CAAChD,wBAAwB,EAAE;IAC/B,IAAI,CAACJ,QAAQ,CAAC,CAAC,GAAG,IAAI,CAACF,kBAAkB,CAAC,CAAC;IAC3C,IAAI,CAACI,SAAS,EAAE;IAEhB,MAAMsD,aAAa,GAAG,IAAI,CAAC1D,kBAAkB,CAACsC,GAAG,CAACf,IAAI,IAAG;MACvD,KAAK,MAAMC,QAAQ,IAAI,IAAI,CAACrF,SAAS,EAAE;QACrC,MAAMsF,IAAI,GAAG,IAAI,CAAC7B,YAAY,CAAC4B,QAAQ,CAAC,EAAEE,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACJ,IAAI,KAAKA,IAAI,CAAC;QACpE,IAAIE,IAAI,EAAE,OAAOA,IAAI;MACvB;MACA,OAAO,IAAI;IACb,CAAC,CAAC,CAACsB,MAAM,CAACtB,IAAI,IAAIA,IAAI,KAAK,IAAI,CAAoB;IAEnD,IAAI,CAAC3B,eAAe,CAAC6D,IAAI,CAACD,aAAa,CAAC;EAC1C;EACAE,cAAcA,CAAA;IACZ,IAAI,CAAC,IAAI,CAACtI,QAAQ,EAAE;MAClB,IAAI,CAACyE,MAAM,GAAG,CAAC,IAAI,CAACA,MAAM;MAC1BiB,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAE,IAAI,CAAClB,MAAM,CAAC;MACrDiB,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAE,IAAI,CAAC9E,SAAS,CAAC;IACrD;EACF;EAEAgD,aAAaA,CAAA;IACX,IAAI,CAACY,MAAM,GAAG,KAAK;EACrB;EAEAxB,mBAAmBA,CAAC6E,aAAqB;IACvC,OAAO,IAAI,CAACpD,kBAAkB,CAACiD,QAAQ,CAACG,aAAa,CAAC;EACxD;EAEA/F,aAAaA,CAAA;IACX,OAAO,CAAC,IAAI,CAACoB,aAAa,IAAI,IAAI,CAACuB,kBAAkB,CAAC9B,MAAM,GAAG,IAAI,CAACO,aAAa;EACnF;EAEAoF,qBAAqBA,CAAA;IACnB,IAAI,CAAC,IAAI,CAACpH,gBAAgB,EAAE,OAAO,KAAK;IACxC,MAAMgH,kBAAkB,GAAG,IAAI,CAAC7D,YAAY,CAAC,IAAI,CAACnD,gBAAgB,CAAC,CAChEsG,MAAM,CAACpB,CAAC,IAAI,CAACA,CAAC,CAAChB,UAAU,CAAC,CAC1B2B,GAAG,CAACX,CAAC,IAAIA,CAAC,CAACJ,IAAI,CAAC;IACnB,OAAOkC,kBAAkB,CAACvF,MAAM,GAAG,CAAC,IAClCuF,kBAAkB,CAACK,KAAK,CAACvC,IAAI,IAAI,IAAI,CAACvB,kBAAkB,CAACiD,QAAQ,CAAC1B,IAAI,CAAC,CAAC;EAC5E;EACAjE,sBAAsBA,CAAA;IACpB,IAAI,CAAC,IAAI,CAACb,gBAAgB,EAAE,OAAO,KAAK;IACxC,MAAMgH,kBAAkB,GAAG,IAAI,CAAC7D,YAAY,CAAC,IAAI,CAACnD,gBAAgB,CAAC,EAAE6F,GAAG,CAACX,CAAC,IAAIA,CAAC,CAACJ,IAAI,CAAC,IAAI,EAAE;IAC3F,OAAOkC,kBAAkB,CAACM,IAAI,CAACxC,IAAI,IAAI,IAAI,CAACvB,kBAAkB,CAACiD,QAAQ,CAAC1B,IAAI,CAAC,CAAC;EAChF;EACAyC,qBAAqBA,CAAA;IACnB,OAAO,IAAI,CAAC/D,kBAAkB;EAChC;EAEAtD,gBAAgBA,CAAC6E,QAAgB;IAC/B,OAAO,IAAI,CAAC5B,YAAY,CAAC4B,QAAQ,CAAC,EAAEtD,MAAM,IAAI,CAAC;EACjD;EAEAhC,gBAAgBA,CAAA;IACd,OAAO,IAAI,CAAC8D,kBAAkB,CAAC9B,MAAM;EACvC;EAEA;EACAvC,6BAA6BA,CAAC6F,QAAgB;IAC5C,OAAO,IAAI,CAACvB,kBAAkB,CAACuB,QAAQ,CAAC,IAAI,EAAE;EAChD;EAEA;EACA3F,mBAAmBA,CAAC2F,QAAgB;IAClC,OAAO,CAAC,EAAE,IAAI,CAACvB,kBAAkB,CAACuB,QAAQ,CAAC,IAAI,IAAI,CAACvB,kBAAkB,CAACuB,QAAQ,CAAC,CAACtD,MAAM,GAAG,CAAC,CAAC;EAC9F;;;uCAnQWsB,yBAAyB,EAAApF,EAAA,CAAA6J,iBAAA,CAAA7J,EAAA,CAAA8J,iBAAA;IAAA;EAAA;;;YAAzB1E,yBAAyB;MAAA2E,SAAA;MAAAC,MAAA;QAAAzE,WAAA;QAAAlB,aAAA;QAAAnD,QAAA;QAAAsE,YAAA;QAAAC,gBAAA;QAAAR,WAAA;QAAAD,gBAAA;MAAA;MAAAiF,OAAA;QAAAvE,eAAA;MAAA;MAAAwE,QAAA,GAAAlK,EAAA,CAAAmK,kBAAA,CARzB,CACT;QACEC,OAAO,EAAErK,iBAAiB;QAC1BsK,WAAW,EAAEvK,UAAU,CAAC,MAAMsF,yBAAyB,CAAC;QACxDkF,KAAK,EAAE;OACR,CACF,GAAAtK,EAAA,CAAAuK,oBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,mCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UC9BH7K,EAAA,CAAAC,cAAA,aAAyC;UAEvCD,EAAA,CAAAoB,UAAA,IAAA2J,wCAAA,kBAAgG;UA4B9F/K,EADF,CAAAC,cAAA,aAA4D,gBAGuJ;UAD/MD,EAAA,CAAAG,UAAA,mBAAA6K,2DAAA;YAAA,OAASF,GAAA,CAAAtB,cAAA,EAAgB;UAAA,EAAC;UAE1BxJ,EAAA,CAAAC,cAAA,cAA4B;UAC1BD,EAAA,CAAAE,MAAA,GACF;UAAAF,EAAA,CAAAc,YAAA,EAAO;UACPd,EAAA,CAAAa,SAAA,iBACU;UACZb,EAAA,CAAAc,YAAA,EAAS;UACTd,EAAA,CAAAoB,UAAA,IAAA6J,wCAAA,mBAC+P;UAgHjQjL,EAAA,CAAAc,YAAA,EAAM;UAGNd,EAAA,CAAAoB,UAAA,IAAA8J,wCAAA,iBAA+D;UA3JjElL,EAAA,CAAAc,YAAA,EAAyC;;;UAEjCd,EAAA,CAAAe,SAAA,EAAuD;UAAvDf,EAAA,CAAAiB,UAAA,SAAA6J,GAAA,CAAArF,gBAAA,IAAAqF,GAAA,CAAAlF,kBAAA,CAAA9B,MAAA,KAAuD;UA4Bb9D,EAAA,CAAAe,SAAA,GAA2B;UAA3Bf,EAAA,CAAAmL,WAAA,aAAAL,GAAA,CAAA5J,QAAA,CAA2B;UAAClB,EAAA,CAAAiB,UAAA,aAAA6J,GAAA,CAAA5J,QAAA,CAAqB;UAI3FlB,EAAA,CAAAe,SAAA,GACF;UADEf,EAAA,CAAAgB,kBAAA,MAAA8J,GAAA,CAAAhJ,gBAAA,iCAAAgJ,GAAA,CAAAhJ,gBAAA,6BAAAgJ,GAAA,CAAAvF,WAAA,MACF;UACqCvF,EAAA,CAAAe,SAAA,EAAwB;UAAxBf,EAAA,CAAAmL,WAAA,YAAAL,GAAA,CAAAnF,MAAA,CAAwB;UAGzD3F,EAAA,CAAAe,SAAA,EAAY;UAAZf,EAAA,CAAAiB,UAAA,SAAA6J,GAAA,CAAAnF,MAAA,CAAY;UAoHd3F,EAAA,CAAAe,SAAA,EAAY;UAAZf,EAAA,CAAAiB,UAAA,SAAA6J,GAAA,CAAAnF,MAAA,CAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}