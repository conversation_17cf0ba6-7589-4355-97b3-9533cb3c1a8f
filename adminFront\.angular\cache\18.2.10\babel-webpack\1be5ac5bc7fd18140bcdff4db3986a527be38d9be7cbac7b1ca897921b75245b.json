{"ast": null, "code": "import { of as observableOf } from 'rxjs';\nimport { ElectricityData } from '../data/electricity';\nimport * as i0 from \"@angular/core\";\nexport let ElectricityService = /*#__PURE__*/(() => {\n  class ElectricityService extends ElectricityData {\n    constructor() {\n      super();\n      this.listData = [{\n        title: '2015',\n        months: [{\n          month: 'Jan',\n          delta: '0.97',\n          down: true,\n          kWatts: '816',\n          cost: '97'\n        }, {\n          month: 'Feb',\n          delta: '1.83',\n          down: true,\n          kWatts: '806',\n          cost: '95'\n        }, {\n          month: 'Mar',\n          delta: '0.64',\n          down: true,\n          kWatts: '803',\n          cost: '94'\n        }, {\n          month: 'Apr',\n          delta: '2.17',\n          down: false,\n          kWatts: '818',\n          cost: '98'\n        }, {\n          month: 'May',\n          delta: '1.32',\n          down: true,\n          kWatts: '809',\n          cost: '96'\n        }, {\n          month: 'Jun',\n          delta: '0.05',\n          down: true,\n          kWatts: '808',\n          cost: '96'\n        }, {\n          month: 'Jul',\n          delta: '1.39',\n          down: false,\n          kWatts: '815',\n          cost: '97'\n        }, {\n          month: 'Aug',\n          delta: '0.73',\n          down: true,\n          kWatts: '807',\n          cost: '95'\n        }, {\n          month: 'Sept',\n          delta: '2.61',\n          down: true,\n          kWatts: '792',\n          cost: '92'\n        }, {\n          month: 'Oct',\n          delta: '0.16',\n          down: true,\n          kWatts: '791',\n          cost: '92'\n        }, {\n          month: 'Nov',\n          delta: '1.71',\n          down: true,\n          kWatts: '786',\n          cost: '89'\n        }, {\n          month: 'Dec',\n          delta: '0.37',\n          down: false,\n          kWatts: '789',\n          cost: '91'\n        }]\n      }, {\n        title: '2016',\n        active: true,\n        months: [{\n          month: 'Jan',\n          delta: '1.56',\n          down: true,\n          kWatts: '789',\n          cost: '91'\n        }, {\n          month: 'Feb',\n          delta: '0.33',\n          down: false,\n          kWatts: '791',\n          cost: '92'\n        }, {\n          month: 'Mar',\n          delta: '0.62',\n          down: true,\n          kWatts: '790',\n          cost: '92'\n        }, {\n          month: 'Apr',\n          delta: '1.93',\n          down: true,\n          kWatts: '783',\n          cost: '87'\n        }, {\n          month: 'May',\n          delta: '2.52',\n          down: true,\n          kWatts: '771',\n          cost: '83'\n        }, {\n          month: 'Jun',\n          delta: '0.39',\n          down: false,\n          kWatts: '774',\n          cost: '85'\n        }, {\n          month: 'Jul',\n          delta: '1.61',\n          down: true,\n          kWatts: '767',\n          cost: '81'\n        }, {\n          month: 'Aug',\n          delta: '1.41',\n          down: true,\n          kWatts: '759',\n          cost: '76'\n        }, {\n          month: 'Sept',\n          delta: '1.03',\n          down: true,\n          kWatts: '752',\n          cost: '74'\n        }, {\n          month: 'Oct',\n          delta: '2.94',\n          down: false,\n          kWatts: '769',\n          cost: '82'\n        }, {\n          month: 'Nov',\n          delta: '0.26',\n          down: true,\n          kWatts: '767',\n          cost: '81'\n        }, {\n          month: 'Dec',\n          delta: '1.62',\n          down: true,\n          kWatts: '760',\n          cost: '76'\n        }]\n      }, {\n        title: '2017',\n        months: [{\n          month: 'Jan',\n          delta: '1.34',\n          down: false,\n          kWatts: '789',\n          cost: '91'\n        }, {\n          month: 'Feb',\n          delta: '0.95',\n          down: false,\n          kWatts: '793',\n          cost: '93'\n        }, {\n          month: 'Mar',\n          delta: '0.25',\n          down: true,\n          kWatts: '791',\n          cost: '92'\n        }, {\n          month: 'Apr',\n          delta: '1.72',\n          down: false,\n          kWatts: '797',\n          cost: '95'\n        }, {\n          month: 'May',\n          delta: '2.62',\n          down: true,\n          kWatts: '786',\n          cost: '90'\n        }, {\n          month: 'Jun',\n          delta: '0.72',\n          down: false,\n          kWatts: '789',\n          cost: '91'\n        }, {\n          month: 'Jul',\n          delta: '0.78',\n          down: true,\n          kWatts: '784',\n          cost: '89'\n        }, {\n          month: 'Aug',\n          delta: '0.36',\n          down: true,\n          kWatts: '782',\n          cost: '88'\n        }, {\n          month: 'Sept',\n          delta: '0.55',\n          down: false,\n          kWatts: '787',\n          cost: '90'\n        }, {\n          month: 'Oct',\n          delta: '1.81',\n          down: true,\n          kWatts: '779',\n          cost: '86'\n        }, {\n          month: 'Nov',\n          delta: '1.12',\n          down: true,\n          kWatts: '774',\n          cost: '84'\n        }, {\n          month: 'Dec',\n          delta: '0.52',\n          down: false,\n          kWatts: '776',\n          cost: '95'\n        }]\n      }];\n      this.chartPoints = [490, 490, 495, 500, 505, 510, 520, 530, 550, 580, 630, 720, 800, 840, 860, 870, 870, 860, 840, 800, 720, 200, 145, 130, 130, 145, 200, 570, 635, 660, 670, 670, 660, 630, 580, 460, 380, 350, 340, 340, 340, 340, 340, 340, 340, 340, 340];\n      this.chartData = this.chartPoints.map((p, index) => ({\n        label: index % 5 === 3 ? `${Math.round(index / 5)}` : '',\n        value: p\n      }));\n    }\n    getListData() {\n      return observableOf(this.listData);\n    }\n    getChartData() {\n      return observableOf(this.chartData);\n    }\n    static {\n      this.ɵfac = function ElectricityService_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || ElectricityService)();\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: ElectricityService,\n        factory: ElectricityService.ɵfac\n      });\n    }\n  }\n  return ElectricityService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}