{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/Documents/salechange-product/adminFront/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { NbCardModule, NbCheckboxModule, NbInputModule, NbOptionModule, NbSelectModule, NbTabsetModule } from '@nebular/theme';\nimport { BaseComponent } from '../components/base/baseComponent';\nimport { takeUntilDestroyed } from '@angular/core/rxjs-interop';\nimport { BreadcrumbComponent } from '../components/breadcrumb/breadcrumb.component';\nimport { FormsModule } from '@angular/forms';\nimport { NgFor, NgIf } from '@angular/common';\nimport { PaginationComponent } from '../components/pagination/pagination.component';\nimport { StatusPipe } from 'src/app/@theme/pipes/mapping.pipe';\nimport { FormGroupComponent } from 'src/app/@theme/components/form-group/form-group.component';\nimport { NumberWithCommasPipe } from 'src/app/@theme/pipes/number-with-commas.pipe';\nimport { EnumHouseType } from 'src/app/shared/enum/enumHouseType';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/shared/helper/allowHelper\";\nimport * as i2 from \"src/app/shared/helper/enumHelper\";\nimport * as i3 from \"@nebular/theme\";\nimport * as i4 from \"src/app/shared/services/message.service\";\nimport * as i5 from \"src/app/shared/helper/validationHelper\";\nimport * as i6 from \"src/services/api/services\";\nimport * as i7 from \"src/app/shared/helper/petternHelper\";\nimport * as i8 from \"@angular/router\";\nimport * as i9 from \"@angular/forms\";\nconst _c0 = () => [];\nfunction RequirementManagementComponent_nb_option_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 9);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const case_r2 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", case_r2.cID);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", case_r2.CBuildCaseName, \" \");\n  }\n}\nfunction RequirementManagementComponent_nb_option_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 9);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const type_r3 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", type_r3.value);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", type_r3.label, \" \");\n  }\n}\nfunction RequirementManagementComponent_button_56_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 39);\n    i0.ɵɵlistener(\"click\", function RequirementManagementComponent_button_56_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r4 = i0.ɵɵnextContext();\n      const dialog_r6 = i0.ɵɵreference(115);\n      return i0.ɵɵresetView(ctx_r4.add(dialog_r6));\n    });\n    i0.ɵɵelement(1, \"i\", 40);\n    i0.ɵɵtext(2, \"\\u65B0\\u589E\\u5EFA\\u6848\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RequirementManagementComponent_button_57_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 39);\n    i0.ɵɵlistener(\"click\", function RequirementManagementComponent_button_57_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r4 = i0.ɵɵnextContext();\n      const templateDialog_r8 = i0.ɵɵreference(117);\n      return i0.ɵɵresetView(ctx_r4.addTemplate(templateDialog_r8));\n    });\n    i0.ɵɵelement(1, \"i\", 40);\n    i0.ɵɵtext(2, \"\\u65B0\\u589E\\u6A21\\u677F\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RequirementManagementComponent_tr_86_button_20_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 46);\n    i0.ɵɵlistener(\"click\", function RequirementManagementComponent_tr_86_button_20_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const data_r10 = i0.ɵɵnextContext().$implicit;\n      const ctx_r4 = i0.ɵɵnextContext();\n      const dialog_r6 = i0.ɵɵreference(115);\n      return i0.ɵɵresetView(ctx_r4.onEdit(data_r10, dialog_r6));\n    });\n    i0.ɵɵelement(1, \"i\", 47);\n    i0.ɵɵtext(2, \"\\u7DE8\\u8F2F\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RequirementManagementComponent_tr_86_button_21_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 48);\n    i0.ɵɵlistener(\"click\", function RequirementManagementComponent_tr_86_button_21_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const data_r10 = i0.ɵɵnextContext().$implicit;\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.onDelete(data_r10));\n    });\n    i0.ɵɵelement(1, \"i\", 49);\n    i0.ɵɵtext(2, \"\\u522A\\u9664\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RequirementManagementComponent_tr_86_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\", 41)(1, \"td\", 42);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\", 42);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\", 43);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"td\", 43);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"td\", 43);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"td\", 43);\n    i0.ɵɵtext(12);\n    i0.ɵɵpipe(13, \"getStatusName\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"td\", 43);\n    i0.ɵɵtext(15);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"td\", 43);\n    i0.ɵɵtext(17);\n    i0.ɵɵpipe(18, \"ngxNumberWithCommas\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"td\", 42);\n    i0.ɵɵtemplate(20, RequirementManagementComponent_tr_86_button_20_Template, 3, 0, \"button\", 44)(21, RequirementManagementComponent_tr_86_button_21_Template, 3, 0, \"button\", 45);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const data_r10 = ctx.$implicit;\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(data_r10.CBuildCaseName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(data_r10.CRequirement);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(data_r10.CGroupName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r4.getHouseType(data_r10.CHouseType || i0.ɵɵpureFunction0(14, _c0)));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(data_r10.CSort);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(13, 10, data_r10.CStatus));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r4.getCIsShowText(data_r10));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(18, 12, data_r10.CUnitPrice || 0));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.isUpdate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.isDelete);\n  }\n}\nfunction RequirementManagementComponent_tr_112_button_18_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 46);\n    i0.ɵɵlistener(\"click\", function RequirementManagementComponent_tr_112_button_18_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r12);\n      const data_r13 = i0.ɵɵnextContext().$implicit;\n      const ctx_r4 = i0.ɵɵnextContext();\n      const templateDialog_r8 = i0.ɵɵreference(117);\n      return i0.ɵɵresetView(ctx_r4.onEditTemplate(data_r13, templateDialog_r8));\n    });\n    i0.ɵɵelement(1, \"i\", 47);\n    i0.ɵɵtext(2, \"\\u7DE8\\u8F2F\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RequirementManagementComponent_tr_112_button_19_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r14 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 48);\n    i0.ɵɵlistener(\"click\", function RequirementManagementComponent_tr_112_button_19_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r14);\n      const data_r13 = i0.ɵɵnextContext().$implicit;\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.onDelete(data_r13));\n    });\n    i0.ɵɵelement(1, \"i\", 49);\n    i0.ɵɵtext(2, \"\\u522A\\u9664\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RequirementManagementComponent_tr_112_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\", 41)(1, \"td\", 42);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\", 42);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\", 43);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"td\", 43);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"td\", 43);\n    i0.ɵɵtext(10);\n    i0.ɵɵpipe(11, \"getStatusName\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"td\", 43);\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"td\", 43);\n    i0.ɵɵtext(15);\n    i0.ɵɵpipe(16, \"ngxNumberWithCommas\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"td\", 50);\n    i0.ɵɵtemplate(18, RequirementManagementComponent_tr_112_button_18_Template, 3, 0, \"button\", 44)(19, RequirementManagementComponent_tr_112_button_19_Template, 3, 0, \"button\", 45);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const data_r13 = ctx.$implicit;\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(data_r13.CRequirement);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(data_r13.CGroupName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r4.getHouseType(data_r13.CHouseType || i0.ɵɵpureFunction0(13, _c0)));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(data_r13.CSort);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(11, 9, data_r13.CStatus));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r4.getCIsShowText(data_r13));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(16, 11, data_r13.CUnitPrice || 0));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.isUpdate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.isDelete);\n  }\n}\nfunction RequirementManagementComponent_ng_template_114_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"\\u65B0\\u589E\\u5EFA\\u6848\\u9700\\u6C42\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RequirementManagementComponent_ng_template_114_span_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"\\u7DE8\\u8F2F\\u5EFA\\u6848\\u9700\\u6C42\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RequirementManagementComponent_ng_template_114_nb_option_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 69);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const b_r16 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", b_r16.cID);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", b_r16.CBuildCaseName, \"\");\n  }\n}\nfunction RequirementManagementComponent_ng_template_114_nb_option_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 69);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const type_r17 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", type_r17.value);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", type_r17.label, \"\");\n  }\n}\nfunction RequirementManagementComponent_ng_template_114_nb_option_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 69);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const status_r18 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", status_r18.value);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", status_r18.label, \"\");\n  }\n}\nfunction RequirementManagementComponent_ng_template_114_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r15 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-card\", 51)(1, \"nb-card-header\");\n    i0.ɵɵtemplate(2, RequirementManagementComponent_ng_template_114_span_2_Template, 2, 0, \"span\", 52)(3, RequirementManagementComponent_ng_template_114_span_3_Template, 2, 0, \"span\", 52);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"nb-card-body\", 53)(5, \"div\", 5)(6, \"div\", 54)(7, \"div\", 5)(8, \"app-form-group\", 55)(9, \"nb-select\", 56);\n    i0.ɵɵtwoWayListener(\"selectedChange\", function RequirementManagementComponent_ng_template_114_Template_nb_select_selectedChange_9_listener($event) {\n      i0.ɵɵrestoreView(_r15);\n      const ctx_r4 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r4.saveRequirement.CBuildCaseID, $event) || (ctx_r4.saveRequirement.CBuildCaseID = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtemplate(10, RequirementManagementComponent_ng_template_114_nb_option_10_Template, 2, 2, \"nb-option\", 57);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"app-form-group\", 55)(12, \"input\", 58);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_ng_template_114_Template_input_ngModelChange_12_listener($event) {\n      i0.ɵɵrestoreView(_r15);\n      const ctx_r4 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r4.saveRequirement.CRequirement, $event) || (ctx_r4.saveRequirement.CRequirement = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"app-form-group\", 55)(14, \"input\", 59);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_ng_template_114_Template_input_ngModelChange_14_listener($event) {\n      i0.ɵɵrestoreView(_r15);\n      const ctx_r4 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r4.saveRequirement.CGroupName, $event) || (ctx_r4.saveRequirement.CGroupName = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(15, \"app-form-group\", 55)(16, \"input\", 60);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_ng_template_114_Template_input_ngModelChange_16_listener($event) {\n      i0.ɵɵrestoreView(_r15);\n      const ctx_r4 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r4.saveRequirement.CSort, $event) || (ctx_r4.saveRequirement.CSort = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(17, \"app-form-group\", 55)(18, \"nb-select\", 61);\n    i0.ɵɵtwoWayListener(\"selectedChange\", function RequirementManagementComponent_ng_template_114_Template_nb_select_selectedChange_18_listener($event) {\n      i0.ɵɵrestoreView(_r15);\n      const ctx_r4 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r4.saveRequirement.CHouseType, $event) || (ctx_r4.saveRequirement.CHouseType = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtemplate(19, RequirementManagementComponent_ng_template_114_nb_option_19_Template, 2, 2, \"nb-option\", 57);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(20, \"app-form-group\", 55)(21, \"nb-select\", 62);\n    i0.ɵɵtwoWayListener(\"selectedChange\", function RequirementManagementComponent_ng_template_114_Template_nb_select_selectedChange_21_listener($event) {\n      i0.ɵɵrestoreView(_r15);\n      const ctx_r4 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r4.saveRequirement.CStatus, $event) || (ctx_r4.saveRequirement.CStatus = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtemplate(22, RequirementManagementComponent_ng_template_114_nb_option_22_Template, 2, 2, \"nb-option\", 57);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(23, \"app-form-group\", 55)(24, \"input\", 63);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_ng_template_114_Template_input_ngModelChange_24_listener($event) {\n      i0.ɵɵrestoreView(_r15);\n      const ctx_r4 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r4.saveRequirement.CUnitPrice, $event) || (ctx_r4.saveRequirement.CUnitPrice = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(25, \"app-form-group\", 55)(26, \"input\", 64);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_ng_template_114_Template_input_ngModelChange_26_listener($event) {\n      i0.ɵɵrestoreView(_r15);\n      const ctx_r4 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r4.saveRequirement.CUnit, $event) || (ctx_r4.saveRequirement.CUnit = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(27, \"app-form-group\", 55)(28, \"nb-checkbox\", 65);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_ng_template_114_Template_nb_checkbox_ngModelChange_28_listener($event) {\n      i0.ɵɵrestoreView(_r15);\n      const ctx_r4 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r4.saveRequirement.CIsShow, $event) || (ctx_r4.saveRequirement.CIsShow = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtext(29, \" \\u986F\\u793A\\u5728\\u5BA2\\u8B8A\\u9700\\u6C42 \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(30, \"app-form-group\", 55)(31, \"textarea\", 66);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_ng_template_114_Template_textarea_ngModelChange_31_listener($event) {\n      i0.ɵɵrestoreView(_r15);\n      const ctx_r4 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r4.saveRequirement.CRemark, $event) || (ctx_r4.saveRequirement.CRemark = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()()()()()();\n    i0.ɵɵelementStart(32, \"nb-card-footer\")(33, \"div\", 5)(34, \"div\", 67)(35, \"button\", 39);\n    i0.ɵɵlistener(\"click\", function RequirementManagementComponent_ng_template_114_Template_button_click_35_listener() {\n      const ref_r19 = i0.ɵɵrestoreView(_r15).dialogRef;\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.save(ref_r19));\n    });\n    i0.ɵɵtext(36, \"\\u78BA\\u5B9A\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(37, \"button\", 68);\n    i0.ɵɵlistener(\"click\", function RequirementManagementComponent_ng_template_114_Template_button_click_37_listener() {\n      const ref_r19 = i0.ɵɵrestoreView(_r15).dialogRef;\n      return i0.ɵɵresetView(ref_r19.close());\n    });\n    i0.ɵɵtext(38, \"\\u53D6\\u6D88\");\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.isNew === true);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.isNew === false);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"label\", \"\\u5EFA\\u6848\\u540D\\u7A31\")(\"labelFor\", \"CBuildCaseID\")(\"isRequired\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"selected\", ctx_r4.saveRequirement.CBuildCaseID);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r4.buildCaseList);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"label\", \"\\u5DE5\\u7A0B\\u9805\\u76EE\")(\"labelFor\", \"CRequirement\")(\"isRequired\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r4.saveRequirement.CRequirement);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"label\", \"\\u7FA4\\u7D44\\u985E\\u5225\")(\"labelFor\", \"CGroupName\")(\"isRequired\", false);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r4.saveRequirement.CGroupName);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"label\", \"\\u6392\\u5E8F\")(\"labelFor\", \"CSort\")(\"isRequired\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r4.saveRequirement.CSort);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"label\", \"\\u985E\\u578B\")(\"labelFor\", \"CHouseType\")(\"isRequired\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"selected\", ctx_r4.saveRequirement.CHouseType);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r4.houseType);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"label\", \"\\u72C0\\u614B\")(\"labelFor\", \"CStatus\")(\"isRequired\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"selected\", ctx_r4.saveRequirement.CStatus);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r4.statusOptions);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"label\", \"\\u55AE\\u50F9\")(\"labelFor\", \"CUnitPrice\")(\"isRequired\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r4.saveRequirement.CUnitPrice);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"label\", \"\\u55AE\\u4F4D\")(\"labelFor\", \"CUnit\")(\"isRequired\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r4.saveRequirement.CUnit);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"label\", \"\\u5BA2\\u8B8A\\u9700\\u6C42\\u986F\\u793A\")(\"labelFor\", \"CIsShow\")(\"isRequired\", false);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r4.saveRequirement.CIsShow);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"label\", \"\\u5099\\u8A3B\\u8AAA\\u660E\")(\"labelFor\", \"CRemark\")(\"isRequired\", false);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r4.saveRequirement.CRemark);\n  }\n}\nfunction RequirementManagementComponent_ng_template_116_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"\\u65B0\\u589E\\u6A21\\u677F\\u9700\\u6C42\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RequirementManagementComponent_ng_template_116_span_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"\\u7DE8\\u8F2F\\u6A21\\u677F\\u9700\\u6C42\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RequirementManagementComponent_ng_template_116_nb_option_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 69);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const type_r21 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", type_r21.value);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", type_r21.label, \"\");\n  }\n}\nfunction RequirementManagementComponent_ng_template_116_nb_option_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 69);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const status_r22 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", status_r22.value);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", status_r22.label, \"\");\n  }\n}\nfunction RequirementManagementComponent_ng_template_116_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r20 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-card\", 51)(1, \"nb-card-header\");\n    i0.ɵɵtemplate(2, RequirementManagementComponent_ng_template_116_span_2_Template, 2, 0, \"span\", 52)(3, RequirementManagementComponent_ng_template_116_span_3_Template, 2, 0, \"span\", 52);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"nb-card-body\", 53)(5, \"div\", 5)(6, \"div\", 54)(7, \"div\", 5)(8, \"app-form-group\", 55)(9, \"input\", 58);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_ng_template_116_Template_input_ngModelChange_9_listener($event) {\n      i0.ɵɵrestoreView(_r20);\n      const ctx_r4 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r4.saveRequirement.CRequirement, $event) || (ctx_r4.saveRequirement.CRequirement = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"app-form-group\", 55)(11, \"input\", 59);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_ng_template_116_Template_input_ngModelChange_11_listener($event) {\n      i0.ɵɵrestoreView(_r20);\n      const ctx_r4 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r4.saveRequirement.CGroupName, $event) || (ctx_r4.saveRequirement.CGroupName = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(12, \"app-form-group\", 55)(13, \"input\", 60);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_ng_template_116_Template_input_ngModelChange_13_listener($event) {\n      i0.ɵɵrestoreView(_r20);\n      const ctx_r4 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r4.saveRequirement.CSort, $event) || (ctx_r4.saveRequirement.CSort = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(14, \"app-form-group\", 55)(15, \"nb-select\", 61);\n    i0.ɵɵtwoWayListener(\"selectedChange\", function RequirementManagementComponent_ng_template_116_Template_nb_select_selectedChange_15_listener($event) {\n      i0.ɵɵrestoreView(_r20);\n      const ctx_r4 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r4.saveRequirement.CHouseType, $event) || (ctx_r4.saveRequirement.CHouseType = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtemplate(16, RequirementManagementComponent_ng_template_116_nb_option_16_Template, 2, 2, \"nb-option\", 57);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(17, \"app-form-group\", 55)(18, \"nb-select\", 62);\n    i0.ɵɵtwoWayListener(\"selectedChange\", function RequirementManagementComponent_ng_template_116_Template_nb_select_selectedChange_18_listener($event) {\n      i0.ɵɵrestoreView(_r20);\n      const ctx_r4 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r4.saveRequirement.CStatus, $event) || (ctx_r4.saveRequirement.CStatus = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtemplate(19, RequirementManagementComponent_ng_template_116_nb_option_19_Template, 2, 2, \"nb-option\", 57);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(20, \"app-form-group\", 55)(21, \"input\", 63);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_ng_template_116_Template_input_ngModelChange_21_listener($event) {\n      i0.ɵɵrestoreView(_r20);\n      const ctx_r4 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r4.saveRequirement.CUnitPrice, $event) || (ctx_r4.saveRequirement.CUnitPrice = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(22, \"app-form-group\", 55)(23, \"input\", 64);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_ng_template_116_Template_input_ngModelChange_23_listener($event) {\n      i0.ɵɵrestoreView(_r20);\n      const ctx_r4 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r4.saveRequirement.CUnit, $event) || (ctx_r4.saveRequirement.CUnit = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(24, \"app-form-group\", 55)(25, \"nb-checkbox\", 65);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_ng_template_116_Template_nb_checkbox_ngModelChange_25_listener($event) {\n      i0.ɵɵrestoreView(_r20);\n      const ctx_r4 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r4.saveRequirement.CIsShow, $event) || (ctx_r4.saveRequirement.CIsShow = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtext(26, \" \\u986F\\u793A\\u5728\\u5BA2\\u8B8A\\u9700\\u6C42 \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(27, \"app-form-group\", 55)(28, \"textarea\", 66);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_ng_template_116_Template_textarea_ngModelChange_28_listener($event) {\n      i0.ɵɵrestoreView(_r20);\n      const ctx_r4 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r4.saveRequirement.CRemark, $event) || (ctx_r4.saveRequirement.CRemark = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()()()()()();\n    i0.ɵɵelementStart(29, \"nb-card-footer\")(30, \"div\", 5)(31, \"div\", 67)(32, \"button\", 39);\n    i0.ɵɵlistener(\"click\", function RequirementManagementComponent_ng_template_116_Template_button_click_32_listener() {\n      const ref_r23 = i0.ɵɵrestoreView(_r20).dialogRef;\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.saveTemplate(ref_r23));\n    });\n    i0.ɵɵtext(33, \"\\u78BA\\u5B9A\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(34, \"button\", 68);\n    i0.ɵɵlistener(\"click\", function RequirementManagementComponent_ng_template_116_Template_button_click_34_listener() {\n      const ref_r23 = i0.ɵɵrestoreView(_r20).dialogRef;\n      return i0.ɵɵresetView(ref_r23.close());\n    });\n    i0.ɵɵtext(35, \"\\u53D6\\u6D88\");\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.isNew === true);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.isNew === false);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"label\", \"\\u5DE5\\u7A0B\\u9805\\u76EE\")(\"labelFor\", \"CRequirement\")(\"isRequired\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r4.saveRequirement.CRequirement);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"label\", \"\\u7FA4\\u7D44\\u985E\\u5225\")(\"labelFor\", \"CGroupName\")(\"isRequired\", false);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r4.saveRequirement.CGroupName);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"label\", \"\\u6392\\u5E8F\")(\"labelFor\", \"CSort\")(\"isRequired\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r4.saveRequirement.CSort);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"label\", \"\\u985E\\u578B\")(\"labelFor\", \"CHouseType\")(\"isRequired\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"selected\", ctx_r4.saveRequirement.CHouseType);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r4.houseType);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"label\", \"\\u72C0\\u614B\")(\"labelFor\", \"CStatus\")(\"isRequired\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"selected\", ctx_r4.saveRequirement.CStatus);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r4.statusOptions);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"label\", \"\\u55AE\\u50F9\")(\"labelFor\", \"CUnitPrice\")(\"isRequired\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r4.saveRequirement.CUnitPrice);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"label\", \"\\u55AE\\u4F4D\")(\"labelFor\", \"CUnit\")(\"isRequired\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r4.saveRequirement.CUnit);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"label\", \"\\u5BA2\\u8B8A\\u9700\\u6C42\\u986F\\u793A\")(\"labelFor\", \"CIsShow\")(\"isRequired\", false);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r4.saveRequirement.CIsShow);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"label\", \"\\u5099\\u8A3B\\u8AAA\\u660E\")(\"labelFor\", \"CRemark\")(\"isRequired\", false);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r4.saveRequirement.CRemark);\n  }\n}\nexport class RequirementManagementComponent extends BaseComponent {\n  constructor(_allow, enumHelper, dialogService, message, valid, buildCaseService, requirementService, pettern, router, destroyref) {\n    super(_allow);\n    this._allow = _allow;\n    this.enumHelper = enumHelper;\n    this.dialogService = dialogService;\n    this.message = message;\n    this.valid = valid;\n    this.buildCaseService = buildCaseService;\n    this.requirementService = requirementService;\n    this.pettern = pettern;\n    this.router = router;\n    this.destroyref = destroyref;\n    // request\n    this.getListRequirementRequest = {};\n    this.getRequirementRequest = {};\n    // response\n    this.buildCaseList = [];\n    this.requirementList = [];\n    this.saveRequirement = {\n      CHouseType: []\n    };\n    this.statusOptions = [{\n      value: 0,\n      label: '停用'\n    }, {\n      value: 1,\n      label: '啟用'\n    }];\n    this.houseType = this.enumHelper.getEnumOptions(EnumHouseType);\n    this.isNew = false;\n    this.currentBuildCase = 0;\n    this.currentTab = 0; // 追蹤當前選中的 tab\n    this.getBuildCaseList();\n    this.initializeSearchForm();\n    this.getList();\n  }\n  ngOnInit() {}\n  // 初始化搜尋表單\n  initializeSearchForm() {\n    this.getListRequirementRequest.CBuildCaseID = -1;\n    this.getListRequirementRequest.CStatus = -1;\n    this.getListRequirementRequest.CIsShow = null;\n    this.getListRequirementRequest.CRequirement = '';\n    this.getListRequirementRequest.CGroupName = '';\n    // 預設全選所有房屋類型\n    this.getListRequirementRequest.CHouseType = this.houseType.map(type => type.value);\n  }\n  // 重置搜尋\n  resetSearch() {\n    this.initializeSearchForm();\n    this.getList();\n  }\n  getHouseType(hTypes) {\n    if (!hTypes) {\n      return '';\n    }\n    if (!Array.isArray(hTypes)) {\n      hTypes = [hTypes];\n    }\n    let labels = [];\n    hTypes.forEach(htype => {\n      let findH = this.houseType.find(x => x.value == htype);\n      if (findH) {\n        labels.push(findH.label);\n      }\n    });\n    return labels.join(', ');\n  }\n  validation() {\n    this.valid.clear();\n    this.valid.required('[建案名稱]', this.saveRequirement.CBuildCaseID);\n    this.valid.required('[需求]', this.saveRequirement.CRequirement);\n    this.valid.required('[地主戶]', this.saveRequirement.CHouseType);\n    this.valid.required('[安排]', this.saveRequirement.CSort);\n    this.valid.required('[狀態]', this.saveRequirement.CStatus);\n    this.valid.required('[單價]', this.saveRequirement.CUnitPrice);\n    this.valid.required('[單位]', this.saveRequirement.CUnit);\n    // 群組名稱長度驗證\n    if (this.saveRequirement.CGroupName && this.saveRequirement.CGroupName.length > 20) {\n      this.valid.errorMessages.push('[群組名稱] 不能超過20個字');\n    }\n    // 備註說明長度驗證\n    if (this.saveRequirement.CRemark && this.saveRequirement.CRemark.length > 100) {\n      this.valid.errorMessages.push('[備註說明] 不能超過100個字');\n    }\n  }\n  add(dialog) {\n    this.isNew = true;\n    this.saveRequirement = {\n      CHouseType: [],\n      CIsShow: false\n    };\n    this.saveRequirement.CStatus = 1;\n    this.saveRequirement.CUnitPrice = 0;\n    if (this.currentBuildCase != 0) {\n      this.saveRequirement.CBuildCaseID = this.currentBuildCase;\n    }\n    this.getBuildCaseList();\n    this.dialogService.open(dialog);\n  }\n  onEdit(data, dialog) {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      _this.getRequirementRequest.CRequirementID = data.CRequirementID;\n      _this.isNew = false;\n      try {\n        yield _this.getData();\n        _this.dialogService.open(dialog);\n      } catch (error) {\n        console.log(\"Failed to get function data\", error);\n      }\n    })();\n  }\n  save(ref) {\n    this.validation();\n    if (this.valid.errorMessages.length > 0) {\n      this.message.showErrorMSGs(this.valid.errorMessages);\n      return;\n    }\n    this.requirementService.apiRequirementSaveDataPost$Json({\n      body: this.saveRequirement\n    }).subscribe(res => {\n      if (res.StatusCode === 0) {\n        this.message.showSucessMSG('執行成功');\n        this.getList();\n      } else {\n        this.message.showErrorMSG(res.Message);\n      }\n    });\n    ref.close();\n  }\n  onDelete(data) {\n    this.saveRequirement.CRequirementID = data.CRequirementID;\n    this.isNew = false;\n    if (window.confirm('是否確定刪除?')) {\n      this.remove();\n    } else {\n      return;\n    }\n  }\n  remove() {\n    this.requirementService.apiRequirementDeleteDataPost$Json({\n      body: {\n        CRequirementID: this.saveRequirement.CRequirementID\n      }\n    }).subscribe(res => {\n      this.message.showSucessMSG('執行成功');\n      this.getList();\n    });\n  }\n  getBuildCaseList() {\n    this.buildCaseService.apiBuildCaseGetUserBuildCasePost$Json({\n      body: {}\n    }).pipe(takeUntilDestroyed(this.destroyref)).subscribe(res => {\n      this.buildCaseList = res.Entries;\n    });\n  }\n  getList() {\n    this.getListRequirementRequest.PageSize = this.pageSize;\n    this.getListRequirementRequest.PageIndex = this.pageIndex;\n    // 當選擇模板頁面時，強制設定 CBuildCaseID = 0 且不可異動\n    if (this.currentTab === 1) {\n      this.getListRequirementRequest.CBuildCaseID = 0;\n    } else {\n      // 建案頁面的邏輯保持不變\n      if (this.getListRequirementRequest.CBuildCaseID && this.getListRequirementRequest.CBuildCaseID != 0) {\n        this.currentBuildCase = this.getListRequirementRequest.CBuildCaseID;\n      }\n    }\n    this.requirementService.apiRequirementGetListPost$Json({\n      body: this.getListRequirementRequest\n    }).pipe().subscribe(res => {\n      if (res.StatusCode == 0) {\n        if (res.Entries) {\n          this.requirementList = res.Entries;\n          this.totalRecords = res.TotalItems;\n        }\n      }\n    });\n  }\n  getData() {\n    this.requirementService.apiRequirementGetDataPost$Json({\n      body: this.getRequirementRequest\n    }).pipe().subscribe(res => {\n      if (res.StatusCode == 0) {\n        if (res.Entries) {\n          this.saveRequirement = {\n            CHouseType: [],\n            CIsShow: false\n          };\n          this.saveRequirement.CBuildCaseID = res.Entries.CBuildCaseID;\n          this.saveRequirement.CGroupName = res.Entries.CGroupName;\n          this.saveRequirement.CHouseType = res.Entries.CHouseType ? Array.isArray(res.Entries.CHouseType) ? res.Entries.CHouseType : [res.Entries.CHouseType] : [];\n          this.saveRequirement.CRemark = res.Entries.CRemark;\n          this.saveRequirement.CRequirement = res.Entries.CRequirement;\n          this.saveRequirement.CRequirementID = res.Entries.CRequirementID;\n          this.saveRequirement.CSort = res.Entries.CSort;\n          this.saveRequirement.CStatus = res.Entries.CStatus;\n          this.saveRequirement.CUnitPrice = res.Entries.CUnitPrice;\n          this.saveRequirement.CUnit = res.Entries.CUnit;\n          // TODO: 等後端API更新後啟用這行\n          this.saveRequirement.CIsShow = res.Entries.CIsShow || false;\n        }\n      }\n    });\n  }\n  onHouseTypeChange(value, checked) {\n    console.log(checked);\n    if (checked) {\n      if (!this.saveRequirement.CHouseType?.includes(value)) {\n        this.saveRequirement.CHouseType?.push(value);\n      }\n      console.log(this.saveRequirement.CHouseType);\n    } else {\n      this.saveRequirement.CHouseType = this.saveRequirement.CHouseType?.filter(v => v !== value);\n    }\n  }\n  getCIsShowText(data) {\n    return data.CIsShow ? '是' : '否';\n  }\n  // Tab 切換事件處理\n  onTabChange(event) {\n    this.currentTab = event.tabIndex || 0;\n    this.getList(); // 切換 tab 時重新載入列表\n  }\n  // 新增模板\n  addTemplate(dialog) {\n    this.isNew = true;\n    this.saveRequirement = {\n      CHouseType: [],\n      CIsShow: false\n    };\n    this.saveRequirement.CStatus = 1;\n    this.saveRequirement.CUnitPrice = 0;\n    // 模板不需要建案ID\n    this.saveRequirement.CBuildCaseID = undefined;\n    this.dialogService.open(dialog);\n  }\n  // 編輯模板\n  onEditTemplate(data, dialog) {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      _this2.getRequirementRequest.CRequirementID = data.CRequirementID;\n      _this2.isNew = false;\n      try {\n        yield _this2.getData();\n        _this2.dialogService.open(dialog);\n      } catch (error) {\n        console.log(\"Failed to get template data\", error);\n      }\n    })();\n  }\n  // 保存模板\n  saveTemplate(ref) {\n    // 模板驗證（不包含建案名稱）\n    this.valid.clear();\n    this.valid.required('[需求]', this.saveRequirement.CRequirement);\n    this.valid.required('[地主戶]', this.saveRequirement.CHouseType);\n    this.valid.required('[安排]', this.saveRequirement.CSort);\n    this.valid.required('[狀態]', this.saveRequirement.CStatus);\n    this.valid.required('[單價]', this.saveRequirement.CUnitPrice);\n    this.valid.required('[單位]', this.saveRequirement.CUnit);\n    // 群組名稱長度驗證\n    if (this.saveRequirement.CGroupName && this.saveRequirement.CGroupName.length > 20) {\n      this.valid.errorMessages.push('[群組名稱] 不能超過20個字');\n    }\n    // 備註說明長度驗證\n    if (this.saveRequirement.CRemark && this.saveRequirement.CRemark.length > 100) {\n      this.valid.errorMessages.push('[備註說明] 不能超過100個字');\n    }\n    if (this.valid.errorMessages.length > 0) {\n      this.message.showErrorMSGs(this.valid.errorMessages);\n      return;\n    }\n    // 確保模板沒有建案ID\n    const templateData = {\n      ...this.saveRequirement\n    };\n    templateData.CBuildCaseID = undefined;\n    this.requirementService.apiRequirementSaveDataPost$Json({\n      body: templateData\n    }).subscribe(res => {\n      if (res.StatusCode === 0) {\n        this.message.showSucessMSG('執行成功');\n        this.getList();\n      } else {\n        this.message.showErrorMSG(res.Message);\n      }\n    });\n    ref.close();\n  }\n  static {\n    this.ɵfac = function RequirementManagementComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || RequirementManagementComponent)(i0.ɵɵdirectiveInject(i1.AllowHelper), i0.ɵɵdirectiveInject(i2.EnumHelper), i0.ɵɵdirectiveInject(i3.NbDialogService), i0.ɵɵdirectiveInject(i4.MessageService), i0.ɵɵdirectiveInject(i5.ValidationHelper), i0.ɵɵdirectiveInject(i6.BuildCaseService), i0.ɵɵdirectiveInject(i6.RequirementService), i0.ɵɵdirectiveInject(i7.PetternHelper), i0.ɵɵdirectiveInject(i8.Router), i0.ɵɵdirectiveInject(i0.DestroyRef));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: RequirementManagementComponent,\n      selectors: [[\"app-requirement-management\"]],\n      standalone: true,\n      features: [i0.ɵɵInheritDefinitionFeature, i0.ɵɵStandaloneFeature],\n      decls: 118,\n      vars: 25,\n      consts: [[\"dialog\", \"\"], [\"templateDialog\", \"\"], [\"accent\", \"success\"], [1, \"bg-white\", \"pb-0\"], [1, \"col-12\"], [1, \"row\"], [1, \"form-group\", \"col-12\", \"col-md-4\"], [\"for\", \"buildCase\", 1, \"label\", \"mr-2\"], [1, \"col-9\", 3, \"ngModelChange\", \"ngModel\"], [3, \"value\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"for\", \"requirement\", 1, \"label\", \"mr-2\"], [\"type\", \"text\", \"nbInput\", \"\", \"id\", \"requirement\", \"name\", \"requirement\", \"placeholder\", \"\\u5DE5\\u7A0B\\u9805\\u76EE\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"groupName\", 1, \"label\", \"mr-2\"], [\"type\", \"text\", \"nbInput\", \"\", \"id\", \"groupName\", \"name\", \"groupName\", \"placeholder\", \"\\u7FA4\\u7D44\\u985E\\u5225\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"houseType\", 1, \"label\", \"mr-2\"], [\"multiple\", \"\", 1, \"col-9\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"status\", 1, \"label\", \"mr-2\"], [\"for\", \"isShow\", 1, \"label\", \"mr-2\"], [1, \"col-md-6\"], [1, \"form-group\", \"col-12\", \"col-md-6\", \"text-right\"], [1, \"btn\", \"btn-secondary\", \"mr-2\", 3, \"click\"], [1, \"fas\", \"fa-undo\", \"mr-1\"], [1, \"btn\", \"btn-info\", \"mr-2\", 3, \"click\"], [1, \"fas\", \"fa-search\", \"mr-1\"], [\"class\", \"btn btn-success mr-2\", 3, \"click\", 4, \"ngIf\"], [3, \"changeTab\"], [\"tabTitle\", \"\\u5EFA\\u6848\"], [1, \"pt-3\"], [1, \"col-12\", \"mt-3\"], [1, \"table-responsive\"], [1, \"table\", \"table-striped\", \"border\", 2, \"min-width\", \"800px\", \"background-color\", \"#f3f3f3\"], [1, \"d-flex\", \"text-white\", 2, \"background-color\", \"#27ae60\"], [\"scope\", \"col\", 1, \"col-2\"], [\"scope\", \"col\", 1, \"col-1\"], [\"class\", \"d-flex\", 4, \"ngFor\", \"ngForOf\"], [3, \"PageChange\", \"CollectionSize\", \"Page\", \"PageSize\"], [\"tabTitle\", \"\\u6A21\\u677F\"], [\"scope\", \"col\", 1, \"col-3\"], [1, \"btn\", \"btn-success\", \"mr-2\", 3, \"click\"], [1, \"fas\", \"fa-plus\", \"mr-1\"], [1, \"d-flex\"], [1, \"col-2\"], [1, \"col-1\"], [\"type\", \"button\", \"class\", \"btn btn-outline-success m-1  btn-sm\", 3, \"click\", 4, \"ngIf\"], [\"type\", \"button\", \"class\", \"btn btn-outline-danger m-1  btn-sm\", 3, \"click\", 4, \"ngIf\"], [\"type\", \"button\", 1, \"btn\", \"btn-outline-success\", \"m-1\", \"btn-sm\", 3, \"click\"], [1, \"fas\", \"fa-edit\", \"mr-1\"], [\"type\", \"button\", 1, \"btn\", \"btn-outline-danger\", \"m-1\", \"btn-sm\", 3, \"click\"], [1, \"far\", \"fa-trash-alt\", \"mr-1\"], [1, \"col-3\"], [1, \"mr-md-5\", \"ml-md-5\", 2, \"height\", \"100%\", \"overflow\", \"auto\", \"max-width\", \"500px\"], [4, \"ngIf\"], [2, \"padding\", \"1rem 2rem\"], [1, \"col-12\", \"col-md-12\"], [3, \"label\", \"labelFor\", \"isRequired\"], [\"placeholder\", \"\\u8ACB\\u9078\\u64C7\", \"id\", \"CBuildCaseID\", \"name\", \"CBuildCaseID\", 1, \"flex-grow-1\", 3, \"selectedChange\", \"selected\"], [\"langg\", \"\", 3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"type\", \"text\", \"nbInput\", \"\", \"id\", \"CRequirement\", \"name\", \"CRequirement\", \"placeholder\", \"\\u5DE5\\u7A0B\\u9805\\u76EE\", \"maxlength\", \"50\", 1, \"flex-grow-1\", 3, \"ngModelChange\", \"ngModel\"], [\"type\", \"text\", \"nbInput\", \"\", \"id\", \"CGroupName\", \"name\", \"CGroupName\", \"placeholder\", \"\\u7FA4\\u7D44\\u985E\\u5225\", \"maxlength\", \"20\", 1, \"flex-grow-1\", 3, \"ngModelChange\", \"ngModel\"], [\"type\", \"number\", \"nbInput\", \"\", \"id\", \"CSort\", \"name\", \"CSort\", \"placeholder\", \"\\u6392\\u5E8F\", 1, \"flex-grow-1\", 3, \"ngModelChange\", \"ngModel\"], [\"placeholder\", \"\\u8ACB\\u9078\\u64C7\", \"id\", \"CHouseType\", \"name\", \"CHouseType\", \"multiple\", \"\", 1, \"flex-grow-1\", 3, \"selectedChange\", \"selected\"], [\"placeholder\", \"\\u8ACB\\u9078\\u64C7\", \"id\", \"CStatus\", \"name\", \"CStatus\", 1, \"flex-grow-1\", 3, \"selectedChange\", \"selected\"], [\"type\", \"number\", \"nbInput\", \"\", \"id\", \"CUnitPrice\", \"name\", \"CUnitPrice\", \"placeholder\", \"\\u55AE\\u50F9\", 1, \"flex-grow-1\", 3, \"ngModelChange\", \"ngModel\"], [\"type\", \"text\", \"nbInput\", \"\", \"id\", \"CUnit\", \"name\", \"CUnit\", \"placeholder\", \"\\u55AE\\u4F4D\", 1, \"flex-grow-1\", 3, \"ngModelChange\", \"ngModel\"], [\"id\", \"CIsShow\", \"name\", \"CIsShow\", 1, \"flex-grow-1\", 3, \"ngModelChange\", \"ngModel\"], [\"nbInput\", \"\", \"id\", \"CRemark\", \"name\", \"CRemark\", \"placeholder\", \"\\u5099\\u8A3B\\u8AAA\\u660E\", \"maxlength\", \"100\", \"rows\", \"3\", 1, \"flex-grow-1\", 3, \"ngModelChange\", \"ngModel\"], [1, \"col-12\", \"text-center\"], [1, \"btn\", \"btn-danger\", \"mr-2\", 3, \"click\"], [\"langg\", \"\", 3, \"value\"]],\n      template: function RequirementManagementComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"nb-card\", 2)(1, \"nb-card-header\");\n          i0.ɵɵelement(2, \"ngx-breadcrumb\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(3, \"nb-card-body\", 3)(4, \"div\", 4)(5, \"div\", 5)(6, \"div\", 6)(7, \"label\", 7);\n          i0.ɵɵtext(8, \"\\u5EFA\\u6848\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(9, \"nb-select\", 8);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_Template_nb_select_ngModelChange_9_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.getListRequirementRequest.CBuildCaseID, $event) || (ctx.getListRequirementRequest.CBuildCaseID = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵelementStart(10, \"nb-option\", 9);\n          i0.ɵɵtext(11, \"\\u5168\\u90E8\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(12, RequirementManagementComponent_nb_option_12_Template, 2, 2, \"nb-option\", 10);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(13, \"div\", 6)(14, \"label\", 11);\n          i0.ɵɵtext(15, \"\\u5DE5\\u7A0B\\u9805\\u76EE\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(16, \"input\", 12);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_Template_input_ngModelChange_16_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.getListRequirementRequest.CRequirement, $event) || (ctx.getListRequirementRequest.CRequirement = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(17, \"div\", 6)(18, \"label\", 13);\n          i0.ɵɵtext(19, \"\\u7FA4\\u7D44\\u985E\\u5225\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(20, \"input\", 14);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_Template_input_ngModelChange_20_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.getListRequirementRequest.CGroupName, $event) || (ctx.getListRequirementRequest.CGroupName = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(21, \"div\", 5)(22, \"div\", 6)(23, \"label\", 15);\n          i0.ɵɵtext(24, \"\\u985E\\u578B\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(25, \"nb-select\", 16);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_Template_nb_select_ngModelChange_25_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.getListRequirementRequest.CHouseType, $event) || (ctx.getListRequirementRequest.CHouseType = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵtemplate(26, RequirementManagementComponent_nb_option_26_Template, 2, 2, \"nb-option\", 10);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(27, \"div\", 6)(28, \"label\", 17);\n          i0.ɵɵtext(29, \"\\u72C0\\u614B\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(30, \"nb-select\", 8);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_Template_nb_select_ngModelChange_30_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.getListRequirementRequest.CStatus, $event) || (ctx.getListRequirementRequest.CStatus = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵelementStart(31, \"nb-option\", 9);\n          i0.ɵɵtext(32, \"\\u5168\\u90E8\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(33, \"nb-option\", 9);\n          i0.ɵɵtext(34, \"\\u555F\\u7528\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(35, \"nb-option\", 9);\n          i0.ɵɵtext(36, \"\\u505C\\u7528\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(37, \"div\", 6)(38, \"label\", 18);\n          i0.ɵɵtext(39, \"\\u5BA2\\u8B8A\\u9700\\u6C42\\u986F\\u793A\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(40, \"nb-select\", 8);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_Template_nb_select_ngModelChange_40_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.getListRequirementRequest.CIsShow, $event) || (ctx.getListRequirementRequest.CIsShow = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵelementStart(41, \"nb-option\", 9);\n          i0.ɵɵtext(42, \"\\u5168\\u90E8\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(43, \"nb-option\", 9);\n          i0.ɵɵtext(44, \"\\u662F\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(45, \"nb-option\", 9);\n          i0.ɵɵtext(46, \"\\u5426\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(47, \"div\", 5);\n          i0.ɵɵelement(48, \"div\", 19);\n          i0.ɵɵelementStart(49, \"div\", 20)(50, \"button\", 21);\n          i0.ɵɵlistener(\"click\", function RequirementManagementComponent_Template_button_click_50_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.resetSearch());\n          });\n          i0.ɵɵelement(51, \"i\", 22);\n          i0.ɵɵtext(52, \"\\u91CD\\u7F6E\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(53, \"button\", 23);\n          i0.ɵɵlistener(\"click\", function RequirementManagementComponent_Template_button_click_53_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.getList());\n          });\n          i0.ɵɵelement(54, \"i\", 24);\n          i0.ɵɵtext(55, \"\\u67E5\\u8A62\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(56, RequirementManagementComponent_button_56_Template, 3, 0, \"button\", 25)(57, RequirementManagementComponent_button_57_Template, 3, 0, \"button\", 25);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(58, \"nb-card-body\", 3)(59, \"nb-tabset\", 26);\n          i0.ɵɵlistener(\"changeTab\", function RequirementManagementComponent_Template_nb_tabset_changeTab_59_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onTabChange($event));\n          });\n          i0.ɵɵelementStart(60, \"nb-tab\", 27)(61, \"div\", 28)(62, \"div\", 29)(63, \"div\", 30)(64, \"table\", 31)(65, \"thead\")(66, \"tr\", 32)(67, \"th\", 33);\n          i0.ɵɵtext(68, \"\\u5EFA\\u6848\\u540D\\u7A31\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(69, \"th\", 33);\n          i0.ɵɵtext(70, \"\\u5DE5\\u7A0B\\u9805\\u76EE\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(71, \"th\", 34);\n          i0.ɵɵtext(72, \"\\u7FA4\\u7D44\\u985E\\u5225\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(73, \"th\", 34);\n          i0.ɵɵtext(74, \"\\u985E\\u578B\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(75, \"th\", 34);\n          i0.ɵɵtext(76, \"\\u6392\\u5E8F\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(77, \"th\", 34);\n          i0.ɵɵtext(78, \"\\u72C0\\u614B\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(79, \"th\", 34);\n          i0.ɵɵtext(80, \"\\u5BA2\\u8B8A\\u9700\\u6C42\\u986F\\u793A\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(81, \"th\", 34);\n          i0.ɵɵtext(82, \"\\u55AE\\u50F9\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(83, \"th\", 33);\n          i0.ɵɵtext(84, \"\\u64CD\\u4F5C\\u529F\\u80FD\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(85, \"tbody\");\n          i0.ɵɵtemplate(86, RequirementManagementComponent_tr_86_Template, 22, 15, \"tr\", 35);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(87, \"ngx-pagination\", 36);\n          i0.ɵɵtwoWayListener(\"PageChange\", function RequirementManagementComponent_Template_ngx_pagination_PageChange_87_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.pageIndex, $event) || (ctx.pageIndex = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵlistener(\"PageChange\", function RequirementManagementComponent_Template_ngx_pagination_PageChange_87_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.getList());\n          });\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(88, \"nb-tab\", 37)(89, \"div\", 28)(90, \"div\", 29)(91, \"div\", 30)(92, \"table\", 31)(93, \"thead\")(94, \"tr\", 32)(95, \"th\", 33);\n          i0.ɵɵtext(96, \"\\u5DE5\\u7A0B\\u9805\\u76EE\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(97, \"th\", 33);\n          i0.ɵɵtext(98, \"\\u7FA4\\u7D44\\u985E\\u5225\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(99, \"th\", 34);\n          i0.ɵɵtext(100, \"\\u985E\\u578B\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(101, \"th\", 34);\n          i0.ɵɵtext(102, \"\\u6392\\u5E8F\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(103, \"th\", 34);\n          i0.ɵɵtext(104, \"\\u72C0\\u614B\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(105, \"th\", 34);\n          i0.ɵɵtext(106, \"\\u5BA2\\u8B8A\\u9700\\u6C42\\u986F\\u793A\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(107, \"th\", 34);\n          i0.ɵɵtext(108, \"\\u55AE\\u50F9\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(109, \"th\", 38);\n          i0.ɵɵtext(110, \"\\u64CD\\u4F5C\\u529F\\u80FD\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(111, \"tbody\");\n          i0.ɵɵtemplate(112, RequirementManagementComponent_tr_112_Template, 20, 14, \"tr\", 35);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(113, \"ngx-pagination\", 36);\n          i0.ɵɵtwoWayListener(\"PageChange\", function RequirementManagementComponent_Template_ngx_pagination_PageChange_113_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.pageIndex, $event) || (ctx.pageIndex = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵlistener(\"PageChange\", function RequirementManagementComponent_Template_ngx_pagination_PageChange_113_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.getList());\n          });\n          i0.ɵɵelementEnd()()()()()()();\n          i0.ɵɵtemplate(114, RequirementManagementComponent_ng_template_114_Template, 39, 45, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor)(116, RequirementManagementComponent_ng_template_116_Template, 36, 40, \"ng-template\", null, 1, i0.ɵɵtemplateRefExtractor);\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(9);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.getListRequirementRequest.CBuildCaseID);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"value\", -1);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngForOf\", ctx.buildCaseList);\n          i0.ɵɵadvance(4);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.getListRequirementRequest.CRequirement);\n          i0.ɵɵadvance(4);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.getListRequirementRequest.CGroupName);\n          i0.ɵɵadvance(5);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.getListRequirementRequest.CHouseType);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngForOf\", ctx.houseType);\n          i0.ɵɵadvance(4);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.getListRequirementRequest.CStatus);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"value\", -1);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"value\", 1);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"value\", 0);\n          i0.ɵɵadvance(5);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.getListRequirementRequest.CIsShow);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"value\", null);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"value\", true);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"value\", false);\n          i0.ɵɵadvance(11);\n          i0.ɵɵproperty(\"ngIf\", ctx.isCreate && ctx.currentTab === 0);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isCreate && ctx.currentTab === 1);\n          i0.ɵɵadvance(29);\n          i0.ɵɵproperty(\"ngForOf\", ctx.requirementList);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"CollectionSize\", ctx.totalRecords);\n          i0.ɵɵtwoWayProperty(\"Page\", ctx.pageIndex);\n          i0.ɵɵproperty(\"PageSize\", ctx.pageSize);\n          i0.ɵɵadvance(25);\n          i0.ɵɵproperty(\"ngForOf\", ctx.requirementList);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"CollectionSize\", ctx.totalRecords);\n          i0.ɵɵtwoWayProperty(\"Page\", ctx.pageIndex);\n          i0.ɵɵproperty(\"PageSize\", ctx.pageSize);\n        }\n      },\n      dependencies: [NbCardModule, i3.NbCardComponent, i3.NbCardBodyComponent, i3.NbCardFooterComponent, i3.NbCardHeaderComponent, BreadcrumbComponent, NbInputModule, i3.NbInputDirective, FormsModule, i9.DefaultValueAccessor, i9.NumberValueAccessor, i9.NgControlStatus, i9.MaxLengthValidator, i9.NgModel, NbSelectModule, i3.NbSelectComponent, i3.NbOptionComponent, NbOptionModule, NgIf, NgFor, PaginationComponent, StatusPipe, NbCheckboxModule, i3.NbCheckboxComponent, FormGroupComponent, NumberWithCommasPipe, NbTabsetModule, i3.NbTabsetComponent, i3.NbTabComponent],\n      styles: [\"/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsImZpbGUiOiJyZXF1aXJlbWVudC1tYW5hZ2VtZW50LmNvbXBvbmVudC5zY3NzIn0= */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvcGFnZXMvcmVxdWlyZW1lbnQtbWFuYWdlbWVudC9yZXF1aXJlbWVudC1tYW5hZ2VtZW50LmNvbXBvbmVudC5zY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiI7QUFDQSx3TEFBd0wiLCJzb3VyY2VSb290IjoiIn0= */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["NbCardModule", "NbCheckboxModule", "NbInputModule", "NbOptionModule", "NbSelectModule", "NbTabsetModule", "BaseComponent", "takeUntilDestroyed", "BreadcrumbComponent", "FormsModule", "<PERSON><PERSON><PERSON>", "NgIf", "PaginationComponent", "StatusPipe", "FormGroupComponent", "NumberWithCommasPipe", "EnumHouseType", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵproperty", "case_r2", "cID", "ɵɵadvance", "ɵɵtextInterpolate1", "CBuildCaseName", "type_r3", "value", "label", "ɵɵlistener", "RequirementManagementComponent_button_56_Template_button_click_0_listener", "ɵɵrestoreView", "_r4", "ctx_r4", "ɵɵnextContext", "dialog_r6", "ɵɵreference", "ɵɵresetView", "add", "ɵɵelement", "RequirementManagementComponent_button_57_Template_button_click_0_listener", "_r7", "templateDialog_r8", "addTemplate", "RequirementManagementComponent_tr_86_button_20_Template_button_click_0_listener", "_r9", "data_r10", "$implicit", "onEdit", "RequirementManagementComponent_tr_86_button_21_Template_button_click_0_listener", "_r11", "onDelete", "ɵɵtemplate", "RequirementManagementComponent_tr_86_button_20_Template", "RequirementManagementComponent_tr_86_button_21_Template", "ɵɵtextInterpolate", "CRequirement", "CGroupName", "getHouseType", "CHouseType", "ɵɵpureFunction0", "_c0", "CSort", "ɵɵpipeBind1", "CStatus", "getCIsShowText", "CUnitPrice", "isUpdate", "isDelete", "RequirementManagementComponent_tr_112_button_18_Template_button_click_0_listener", "_r12", "data_r13", "onEditTemplate", "RequirementManagementComponent_tr_112_button_19_Template_button_click_0_listener", "_r14", "RequirementManagementComponent_tr_112_button_18_Template", "RequirementManagementComponent_tr_112_button_19_Template", "b_r16", "type_r17", "status_r18", "RequirementManagementComponent_ng_template_114_span_2_Template", "RequirementManagementComponent_ng_template_114_span_3_Template", "ɵɵtwoWayListener", "RequirementManagementComponent_ng_template_114_Template_nb_select_selectedChange_9_listener", "$event", "_r15", "ɵɵtwoWayBindingSet", "saveRequirement", "CBuildCaseID", "RequirementManagementComponent_ng_template_114_nb_option_10_Template", "RequirementManagementComponent_ng_template_114_Template_input_ngModelChange_12_listener", "RequirementManagementComponent_ng_template_114_Template_input_ngModelChange_14_listener", "RequirementManagementComponent_ng_template_114_Template_input_ngModelChange_16_listener", "RequirementManagementComponent_ng_template_114_Template_nb_select_selectedChange_18_listener", "RequirementManagementComponent_ng_template_114_nb_option_19_Template", "RequirementManagementComponent_ng_template_114_Template_nb_select_selectedChange_21_listener", "RequirementManagementComponent_ng_template_114_nb_option_22_Template", "RequirementManagementComponent_ng_template_114_Template_input_ngModelChange_24_listener", "RequirementManagementComponent_ng_template_114_Template_input_ngModelChange_26_listener", "CUnit", "RequirementManagementComponent_ng_template_114_Template_nb_checkbox_ngModelChange_28_listener", "CIsShow", "RequirementManagementComponent_ng_template_114_Template_textarea_ngModelChange_31_listener", "CRemark", "RequirementManagementComponent_ng_template_114_Template_button_click_35_listener", "ref_r19", "dialogRef", "save", "RequirementManagementComponent_ng_template_114_Template_button_click_37_listener", "close", "isNew", "ɵɵtwoWayProperty", "buildCaseList", "houseType", "statusOptions", "type_r21", "status_r22", "RequirementManagementComponent_ng_template_116_span_2_Template", "RequirementManagementComponent_ng_template_116_span_3_Template", "RequirementManagementComponent_ng_template_116_Template_input_ngModelChange_9_listener", "_r20", "RequirementManagementComponent_ng_template_116_Template_input_ngModelChange_11_listener", "RequirementManagementComponent_ng_template_116_Template_input_ngModelChange_13_listener", "RequirementManagementComponent_ng_template_116_Template_nb_select_selectedChange_15_listener", "RequirementManagementComponent_ng_template_116_nb_option_16_Template", "RequirementManagementComponent_ng_template_116_Template_nb_select_selectedChange_18_listener", "RequirementManagementComponent_ng_template_116_nb_option_19_Template", "RequirementManagementComponent_ng_template_116_Template_input_ngModelChange_21_listener", "RequirementManagementComponent_ng_template_116_Template_input_ngModelChange_23_listener", "RequirementManagementComponent_ng_template_116_Template_nb_checkbox_ngModelChange_25_listener", "RequirementManagementComponent_ng_template_116_Template_textarea_ngModelChange_28_listener", "RequirementManagementComponent_ng_template_116_Template_button_click_32_listener", "ref_r23", "saveTemplate", "RequirementManagementComponent_ng_template_116_Template_button_click_34_listener", "RequirementManagementComponent", "constructor", "_allow", "enum<PERSON>elper", "dialogService", "message", "valid", "buildCaseService", "requirementService", "pettern", "router", "destroyref", "getListRequirementRequest", "getRequirementRequest", "requirementList", "getEnumOptions", "currentBuildCase", "currentTab", "getBuildCaseList", "initializeSearchForm", "getList", "ngOnInit", "map", "type", "resetSearch", "hTypes", "Array", "isArray", "labels", "for<PERSON>ach", "htype", "findH", "find", "x", "push", "join", "validation", "clear", "required", "length", "errorMessages", "dialog", "open", "data", "_this", "_asyncToGenerator", "CRequirementID", "getData", "error", "console", "log", "ref", "showErrorMSGs", "apiRequirementSaveDataPost$Json", "body", "subscribe", "res", "StatusCode", "showSucessMSG", "showErrorMSG", "Message", "window", "confirm", "remove", "apiRequirementDeleteDataPost$Json", "apiBuildCaseGetUserBuildCasePost$Json", "pipe", "Entries", "PageSize", "pageSize", "PageIndex", "pageIndex", "apiRequirementGetListPost$Json", "totalRecords", "TotalItems", "apiRequirementGetDataPost$Json", "onHouseTypeChange", "checked", "includes", "filter", "v", "onTabChange", "event", "tabIndex", "undefined", "_this2", "templateData", "ɵɵdirectiveInject", "i1", "AllowHelper", "i2", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "i3", "NbDialogService", "i4", "MessageService", "i5", "ValidationHelper", "i6", "BuildCaseService", "RequirementService", "i7", "PetternHelper", "i8", "Router", "DestroyRef", "selectors", "standalone", "features", "ɵɵInheritDefinitionFeature", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "RequirementManagementComponent_Template", "rf", "ctx", "RequirementManagementComponent_Template_nb_select_ngModelChange_9_listener", "_r1", "RequirementManagementComponent_nb_option_12_Template", "RequirementManagementComponent_Template_input_ngModelChange_16_listener", "RequirementManagementComponent_Template_input_ngModelChange_20_listener", "RequirementManagementComponent_Template_nb_select_ngModelChange_25_listener", "RequirementManagementComponent_nb_option_26_Template", "RequirementManagementComponent_Template_nb_select_ngModelChange_30_listener", "RequirementManagementComponent_Template_nb_select_ngModelChange_40_listener", "RequirementManagementComponent_Template_button_click_50_listener", "RequirementManagementComponent_Template_button_click_53_listener", "RequirementManagementComponent_button_56_Template", "RequirementManagementComponent_button_57_Template", "RequirementManagementComponent_Template_nb_tabset_changeTab_59_listener", "RequirementManagementComponent_tr_86_Template", "RequirementManagementComponent_Template_ngx_pagination_PageChange_87_listener", "RequirementManagementComponent_tr_112_Template", "RequirementManagementComponent_Template_ngx_pagination_PageChange_113_listener", "RequirementManagementComponent_ng_template_114_Template", "ɵɵtemplateRefExtractor", "RequirementManagementComponent_ng_template_116_Template", "isCreate", "NbCardComponent", "NbCardBodyComponent", "NbCardFooterComponent", "NbCardHeaderComponent", "NbInputDirective", "i9", "DefaultValueAccessor", "NumberValueAccessor", "NgControlStatus", "MaxLengthValidator", "NgModel", "NbSelectComponent", "NbOptionComponent", "NbCheckboxComponent", "NbTabsetComponent", "NbTabComponent", "styles"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\requirement-management\\requirement-management.component.ts", "C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\requirement-management\\requirement-management.component.html"], "sourcesContent": ["import { Component, DestroyRef, OnInit, TemplateRef } from '@angular/core';\r\nimport { Router } from '@angular/router';\r\nimport { NbCardModule, NbCheckboxModule, NbDialogService, NbInputModule, NbOptionModule, NbSelectModule, NbTabsetModule } from '@nebular/theme';\r\nimport { MessageService } from 'src/app/shared/services/message.service';\r\nimport { AllowHelper } from 'src/app/shared/helper/allowHelper';\r\nimport { EnumHelper } from 'src/app/shared/helper/enumHelper';\r\nimport { PetternHelper } from 'src/app/shared/helper/petternHelper';\r\nimport { ValidationHelper } from 'src/app/shared/helper/validationHelper';\r\nimport { BuildCaseService, RequirementService } from 'src/services/api/services';\r\nimport { BaseComponent } from '../components/base/baseComponent';\r\nimport { takeUntilDestroyed } from '@angular/core/rxjs-interop';\r\nimport { BuildCaseGetListReponse, GetListRequirementRequest, GetRequirement, GetRequirementByIdRequest, SaveDataRequirement } from 'src/services/api/models';\r\nimport { BreadcrumbComponent } from '../components/breadcrumb/breadcrumb.component';\r\nimport { FormsModule } from '@angular/forms';\r\nimport { NgFor, NgIf } from '@angular/common';\r\nimport { PaginationComponent } from '../components/pagination/pagination.component';\r\nimport { StatusPipe } from 'src/app/@theme/pipes/mapping.pipe';\r\nimport { FormGroupComponent } from 'src/app/@theme/components/form-group/form-group.component';\r\nimport { NumberWithCommasPipe } from 'src/app/@theme/pipes/number-with-commas.pipe';\r\nimport { EnumHouseType } from 'src/app/shared/enum/enumHouseType';\r\n\r\n@Component({\r\n  selector: 'app-requirement-management',\r\n  standalone: true, imports: [\r\n    NbCardModule,\r\n    BreadcrumbComponent,\r\n    NbInputModule,\r\n    FormsModule,\r\n    NbSelectModule,\r\n    NbOptionModule,\r\n    NgIf,\r\n    NgFor,\r\n    PaginationComponent,\r\n    StatusPipe,\r\n    NbCheckboxModule,\r\n    FormGroupComponent,\r\n    NumberWithCommasPipe,\r\n    NbTabsetModule\r\n  ],\r\n  templateUrl: './requirement-management.component.html',\r\n  styleUrl: './requirement-management.component.scss'\r\n})\r\nexport class RequirementManagementComponent extends BaseComponent implements OnInit {\r\n  constructor(\r\n    private _allow: AllowHelper,\r\n    private enumHelper: EnumHelper,\r\n    private dialogService: NbDialogService,\r\n    private message: MessageService,\r\n    private valid: ValidationHelper,\r\n    private buildCaseService: BuildCaseService,\r\n    private requirementService: RequirementService,\r\n    private pettern: PetternHelper,\r\n    private router: Router,\r\n    private destroyref: DestroyRef\r\n  ) {\r\n    super(_allow);\r\n    this.getBuildCaseList();\r\n    this.initializeSearchForm();\r\n    this.getList();\r\n  }\r\n  // request\r\n  getListRequirementRequest = {} as GetListRequirementRequest & { CIsShow?: boolean | null };\r\n  getRequirementRequest: GetRequirementByIdRequest = {};\r\n  // response\r\n  buildCaseList: BuildCaseGetListReponse[] = [];\r\n  requirementList: GetRequirement[] = [];\r\n  saveRequirement: SaveDataRequirement & { CIsShow?: boolean } = { CHouseType: [] };\r\n\r\n  statusOptions = [\r\n    { value: 0, label: '停用' },\r\n    { value: 1, label: '啟用' },\r\n  ];\r\n  houseType = this.enumHelper.getEnumOptions(EnumHouseType);\r\n  isNew = false;\r\n  currentBuildCase = 0;\r\n  currentTab = 0; // 追蹤當前選中的 tab\r\n\r\n  override ngOnInit(): void { }\r\n  // 初始化搜尋表單\r\n  initializeSearchForm() {\r\n    this.getListRequirementRequest.CBuildCaseID = -1;\r\n    this.getListRequirementRequest.CStatus = -1;\r\n    this.getListRequirementRequest.CIsShow = null;\r\n    this.getListRequirementRequest.CRequirement = '';\r\n    this.getListRequirementRequest.CGroupName = '';\r\n    // 預設全選所有房屋類型\r\n    this.getListRequirementRequest.CHouseType = this.houseType.map(type => type.value);\r\n  }\r\n\r\n  // 重置搜尋\r\n  resetSearch() {\r\n    this.initializeSearchForm();\r\n    this.getList();\r\n  }\r\n\r\n  getHouseType(hTypes: number[] | number | null | undefined): string {\r\n    if (!hTypes) {\r\n      return '';\r\n    }\r\n\r\n    if (!Array.isArray(hTypes)) {\r\n      hTypes = [hTypes];\r\n    }\r\n\r\n    let labels: string[] = [];\r\n    hTypes.forEach(htype => {\r\n      let findH = this.houseType.find(x => x.value == htype);\r\n      if (findH) {\r\n        labels.push(findH.label);\r\n      }\r\n    });\r\n    return labels.join(', ');\r\n  } validation() {\r\n    this.valid.clear();\r\n    this.valid.required('[建案名稱]', this.saveRequirement.CBuildCaseID);\r\n    this.valid.required('[需求]', this.saveRequirement.CRequirement);\r\n    this.valid.required('[地主戶]', this.saveRequirement.CHouseType);\r\n    this.valid.required('[安排]', this.saveRequirement.CSort);\r\n    this.valid.required('[狀態]', this.saveRequirement.CStatus);\r\n    this.valid.required('[單價]', this.saveRequirement.CUnitPrice);\r\n    this.valid.required('[單位]', this.saveRequirement.CUnit);\r\n    // 群組名稱長度驗證\r\n    if (this.saveRequirement.CGroupName && this.saveRequirement.CGroupName.length > 20) {\r\n      this.valid.errorMessages.push('[群組名稱] 不能超過20個字');\r\n    }\r\n\r\n    // 備註說明長度驗證\r\n    if (this.saveRequirement.CRemark && this.saveRequirement.CRemark.length > 100) {\r\n      this.valid.errorMessages.push('[備註說明] 不能超過100個字');\r\n    }\r\n  }\r\n\r\n  add(dialog: TemplateRef<any>) {\r\n    this.isNew = true;\r\n    this.saveRequirement = { CHouseType: [], CIsShow: false };\r\n    this.saveRequirement.CStatus = 1;\r\n    this.saveRequirement.CUnitPrice = 0;\r\n    if (this.currentBuildCase != 0) {\r\n      this.saveRequirement.CBuildCaseID = this.currentBuildCase;\r\n    }\r\n    this.getBuildCaseList();\r\n    this.dialogService.open(dialog);\r\n  }\r\n\r\n  async onEdit(data: GetRequirement, dialog: TemplateRef<any>) {\r\n    this.getRequirementRequest.CRequirementID = data.CRequirementID!;\r\n    this.isNew = false;\r\n    try {\r\n      await this.getData();\r\n      this.dialogService.open(dialog);\r\n    } catch (error) {\r\n      console.log(\"Failed to get function data\", error)\r\n    }\r\n  }\r\n\r\n  save(ref: any) {\r\n    this.validation();\r\n    if (this.valid.errorMessages.length > 0) {\r\n      this.message.showErrorMSGs(this.valid.errorMessages);\r\n      return;\r\n    }\r\n\r\n    this.requirementService.apiRequirementSaveDataPost$Json({\r\n      body: this.saveRequirement\r\n    }).subscribe(res => {\r\n      if (res.StatusCode === 0) {\r\n        this.message.showSucessMSG('執行成功');\r\n        this.getList();\r\n      } else {\r\n        this.message.showErrorMSG(res.Message!)\r\n      }\r\n    });\r\n    ref.close();\r\n  }\r\n\r\n  onDelete(data: GetRequirement) {\r\n    this.saveRequirement.CRequirementID = data.CRequirementID!;\r\n    this.isNew = false;\r\n    if (window.confirm('是否確定刪除?')) {\r\n      this.remove();\r\n    } else {\r\n      return;\r\n    }\r\n  }\r\n\r\n  remove() {\r\n    this.requirementService.apiRequirementDeleteDataPost$Json({\r\n      body: {\r\n        CRequirementID: this.saveRequirement.CRequirementID!\r\n      }\r\n    }).subscribe(res => {\r\n      this.message.showSucessMSG('執行成功');\r\n      this.getList();\r\n    });\r\n  }\r\n\r\n  getBuildCaseList() {\r\n    this.buildCaseService.apiBuildCaseGetUserBuildCasePost$Json({ body: {} })\r\n      .pipe(takeUntilDestroyed(this.destroyref)).subscribe(res => {\r\n        this.buildCaseList = res.Entries!;\r\n      })\r\n  }  getList() {\r\n    this.getListRequirementRequest.PageSize = this.pageSize;\r\n    this.getListRequirementRequest.PageIndex = this.pageIndex;\r\n    \r\n    // 當選擇模板頁面時，強制設定 CBuildCaseID = 0 且不可異動\r\n    if (this.currentTab === 1) {\r\n      this.getListRequirementRequest.CBuildCaseID = 0;\r\n    } else {\r\n      // 建案頁面的邏輯保持不變\r\n      if (this.getListRequirementRequest.CBuildCaseID && this.getListRequirementRequest.CBuildCaseID != 0) {\r\n        this.currentBuildCase = this.getListRequirementRequest.CBuildCaseID;\r\n      }\r\n    }\r\n\r\n    this.requirementService.apiRequirementGetListPost$Json({ body: this.getListRequirementRequest })\r\n      .pipe()\r\n      .subscribe(res => {\r\n        if (res.StatusCode == 0) {\r\n          if (res.Entries) {\r\n            this.requirementList = res.Entries;\r\n            this.totalRecords = res.TotalItems!;\r\n          }\r\n        }\r\n      })\r\n  }getData() {\r\n    this.requirementService.apiRequirementGetDataPost$Json({ body: this.getRequirementRequest })\r\n      .pipe()\r\n      .subscribe(res => {\r\n        if (res.StatusCode == 0) {\r\n          if (res.Entries) {\r\n            this.saveRequirement = { CHouseType: [], CIsShow: false };\r\n            this.saveRequirement.CBuildCaseID = res.Entries.CBuildCaseID;\r\n            this.saveRequirement.CGroupName = res.Entries.CGroupName;\r\n            this.saveRequirement.CHouseType = res.Entries.CHouseType ? (Array.isArray(res.Entries.CHouseType) ? res.Entries.CHouseType : [res.Entries.CHouseType]) : [];\r\n            this.saveRequirement.CRemark = res.Entries.CRemark;\r\n            this.saveRequirement.CRequirement = res.Entries.CRequirement;\r\n            this.saveRequirement.CRequirementID = res.Entries.CRequirementID;\r\n            this.saveRequirement.CSort = res.Entries.CSort;\r\n            this.saveRequirement.CStatus = res.Entries.CStatus;\r\n            this.saveRequirement.CUnitPrice = res.Entries.CUnitPrice;\r\n            this.saveRequirement.CUnit = res.Entries.CUnit;\r\n            // TODO: 等後端API更新後啟用這行\r\n            this.saveRequirement.CIsShow = (res.Entries as any).CIsShow || false;\r\n          }\r\n        }\r\n      })\r\n  }\r\n\r\n  onHouseTypeChange(value: number, checked: any) {\r\n    console.log(checked);\r\n\r\n    if (checked) {\r\n      if (!this.saveRequirement.CHouseType?.includes(value)) {\r\n        this.saveRequirement.CHouseType?.push(value);\r\n      }\r\n      console.log(this.saveRequirement.CHouseType);\r\n    } else {\r\n      this.saveRequirement.CHouseType = this.saveRequirement.CHouseType?.filter(v => v !== value);\r\n    }\r\n  }\r\n\r\n  getCIsShowText(data: any): string {\r\n    return data.CIsShow ? '是' : '否';\r\n  }\r\n\r\n  // Tab 切換事件處理\r\n  onTabChange(event: any) {\r\n    this.currentTab = event.tabIndex || 0;\r\n    this.getList(); // 切換 tab 時重新載入列表\r\n  }\r\n\r\n  // 新增模板\r\n  addTemplate(dialog: TemplateRef<any>) {\r\n    this.isNew = true;\r\n    this.saveRequirement = { CHouseType: [], CIsShow: false };\r\n    this.saveRequirement.CStatus = 1;\r\n    this.saveRequirement.CUnitPrice = 0;\r\n    // 模板不需要建案ID\r\n    this.saveRequirement.CBuildCaseID = undefined;\r\n    this.dialogService.open(dialog);\r\n  }\r\n\r\n  // 編輯模板\r\n  async onEditTemplate(data: GetRequirement, dialog: TemplateRef<any>) {\r\n    this.getRequirementRequest.CRequirementID = data.CRequirementID!;\r\n    this.isNew = false;\r\n    try {\r\n      await this.getData();\r\n      this.dialogService.open(dialog);\r\n    } catch (error) {\r\n      console.log(\"Failed to get template data\", error);\r\n    }\r\n  }\r\n\r\n  // 保存模板\r\n  saveTemplate(ref: any) {\r\n    // 模板驗證（不包含建案名稱）\r\n    this.valid.clear();\r\n    this.valid.required('[需求]', this.saveRequirement.CRequirement);\r\n    this.valid.required('[地主戶]', this.saveRequirement.CHouseType);\r\n    this.valid.required('[安排]', this.saveRequirement.CSort);\r\n    this.valid.required('[狀態]', this.saveRequirement.CStatus);\r\n    this.valid.required('[單價]', this.saveRequirement.CUnitPrice);\r\n    this.valid.required('[單位]', this.saveRequirement.CUnit);\r\n\r\n    // 群組名稱長度驗證\r\n    if (this.saveRequirement.CGroupName && this.saveRequirement.CGroupName.length > 20) {\r\n      this.valid.errorMessages.push('[群組名稱] 不能超過20個字');\r\n    }\r\n\r\n    // 備註說明長度驗證\r\n    if (this.saveRequirement.CRemark && this.saveRequirement.CRemark.length > 100) {\r\n      this.valid.errorMessages.push('[備註說明] 不能超過100個字');\r\n    }\r\n\r\n    if (this.valid.errorMessages.length > 0) {\r\n      this.message.showErrorMSGs(this.valid.errorMessages);\r\n      return;\r\n    }\r\n\r\n    // 確保模板沒有建案ID\r\n    const templateData = { ...this.saveRequirement };\r\n    templateData.CBuildCaseID = undefined;\r\n\r\n    this.requirementService.apiRequirementSaveDataPost$Json({\r\n      body: templateData\r\n    }).subscribe(res => {\r\n      if (res.StatusCode === 0) {\r\n        this.message.showSucessMSG('執行成功');\r\n        this.getList();\r\n      } else {\r\n        this.message.showErrorMSG(res.Message!)\r\n      }\r\n    });\r\n    ref.close();\r\n  }\r\n}\r\n", "<nb-card accent=\"success\">\r\n  <nb-card-header>\r\n    <ngx-breadcrumb></ngx-breadcrumb>\r\n  </nb-card-header>\r\n\r\n  <!-- 共用搜尋區域 -->\r\n  <nb-card-body class=\"bg-white pb-0\">\r\n    <div class=\"col-12\">\r\n      <div class=\"row\">\r\n        <div class=\"form-group col-12 col-md-4\">\r\n          <label for=\"buildCase\" class=\"label mr-2\">建案</label>\r\n          <nb-select [(ngModel)]=\"getListRequirementRequest.CBuildCaseID\" class=\"col-9\">\r\n            <nb-option [value]=\"-1\">全部</nb-option>\r\n            <nb-option *ngFor=\"let case of buildCaseList\" [value]=\"case.cID\">\r\n              {{ case.CBuildCaseName }}\r\n            </nb-option>\r\n          </nb-select>\r\n        </div>\r\n        <div class=\"form-group col-12 col-md-4\">\r\n          <label for=\"requirement\" class=\"label mr-2\">工程項目</label>\r\n          <input type=\"text\" nbInput id=\"requirement\" name=\"requirement\" placeholder=\"工程項目\"\r\n            [(ngModel)]=\"getListRequirementRequest.CRequirement\">\r\n        </div>\r\n        <div class=\"form-group col-12 col-md-4\">\r\n          <label for=\"groupName\" class=\"label mr-2\">群組類別</label>\r\n          <input type=\"text\" nbInput id=\"groupName\" name=\"groupName\" placeholder=\"群組類別\"\r\n            [(ngModel)]=\"getListRequirementRequest.CGroupName\">\r\n        </div>\r\n      </div>\r\n      <div class=\"row\">\r\n        <div class=\"form-group col-12 col-md-4\">\r\n          <label for=\"houseType\" class=\"label mr-2\">類型</label>\r\n          <nb-select [(ngModel)]=\"getListRequirementRequest.CHouseType\" class=\"col-9\" multiple>\r\n            <nb-option *ngFor=\"let type of houseType\" [value]=\"type.value\">\r\n              {{ type.label }}\r\n            </nb-option>\r\n          </nb-select>\r\n        </div>\r\n        <div class=\"form-group col-12 col-md-4\">\r\n          <label for=\"status\" class=\"label mr-2\">狀態</label>\r\n          <nb-select [(ngModel)]=\"getListRequirementRequest.CStatus\" class=\"col-9\">\r\n            <nb-option [value]=\"-1\">全部</nb-option>\r\n            <nb-option [value]=\"1\">啟用</nb-option>\r\n            <nb-option [value]=\"0\">停用</nb-option>\r\n          </nb-select>\r\n        </div>\r\n        <div class=\"form-group col-12 col-md-4\">\r\n          <label for=\"isShow\" class=\"label mr-2\">客變需求顯示</label>\r\n          <nb-select [(ngModel)]=\"getListRequirementRequest.CIsShow\" class=\"col-9\">\r\n            <nb-option [value]=\"null\">全部</nb-option>\r\n            <nb-option [value]=\"true\">是</nb-option>\r\n            <nb-option [value]=\"false\">否</nb-option>\r\n          </nb-select>\r\n        </div>\r\n      </div>\r\n      <div class=\"row\">\r\n        <div class=\"col-md-6\"></div>\r\n        <div class=\"form-group col-12 col-md-6 text-right\">\r\n          <button class=\"btn btn-secondary mr-2\" (click)=\"resetSearch()\"><i class=\"fas fa-undo mr-1\"></i>重置</button>\r\n          <button class=\"btn btn-info mr-2\" (click)=\"getList()\"><i class=\"fas fa-search mr-1\"></i>查詢</button>\r\n          <button class=\"btn btn-success mr-2\" (click)=\"add(dialog)\" *ngIf=\"isCreate && currentTab === 0\"><i\r\n              class=\"fas fa-plus mr-1\"></i>新增建案</button>\r\n          <button class=\"btn btn-success mr-2\" (click)=\"addTemplate(templateDialog)\"\r\n            *ngIf=\"isCreate && currentTab === 1\"><i class=\"fas fa-plus mr-1\"></i>新增模板</button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </nb-card-body>\r\n\r\n  <!-- Tab 導航 -->\r\n  <nb-card-body class=\"bg-white pb-0\">\r\n    <nb-tabset (changeTab)=\"onTabChange($event)\">\r\n      <nb-tab tabTitle=\"建案\">\r\n        <div class=\"pt-3\">\r\n          <!-- 建案列表 -->\r\n          <div class=\"col-12 mt-3\">\r\n            <div class=\"table-responsive\">\r\n              <table class=\"table table-striped border \" style=\"min-width: 800px; background-color:#f3f3f3;\">\r\n                <thead>\r\n                  <tr style=\"background-color: #27ae60;\" class=\"d-flex text-white\">\r\n                    <th scope=\"col\" class=\"col-2\">建案名稱</th>\r\n                    <th scope=\"col\" class=\"col-2\">工程項目</th>\r\n                    <th scope=\"col\" class=\"col-1\">群組類別</th>\r\n                    <th scope=\"col\" class=\"col-1\">類型</th>\r\n                    <th scope=\"col\" class=\"col-1\">排序</th>\r\n                    <th scope=\"col\" class=\"col-1\">狀態</th>\r\n                    <th scope=\"col\" class=\"col-1\">客變需求顯示</th>\r\n                    <th scope=\"col\" class=\"col-1\">單價</th>\r\n                    <th scope=\"col\" class=\"col-2\">操作功能</th>\r\n                  </tr>\r\n                </thead>\r\n                <tbody>\r\n                  <tr *ngFor=\"let data of requirementList; let i = index\" class=\"d-flex\">\r\n                    <td class=\"col-2\">{{ data.CBuildCaseName }}</td>\r\n                    <td class=\"col-2\">{{ data.CRequirement }}</td>\r\n                    <td class=\"col-1\">{{ data.CGroupName }}</td>\r\n                    <td class=\"col-1\">{{ getHouseType(data.CHouseType || []) }}</td>\r\n                    <td class=\"col-1\">{{ data.CSort }}</td>\r\n                    <td class=\"col-1\">{{ data.CStatus | getStatusName }}</td>\r\n                    <td class=\"col-1\">{{ getCIsShowText(data) }}</td>\r\n                    <td class=\"col-1\">{{ (data.CUnitPrice || 0) | ngxNumberWithCommas }}</td>\r\n                    <td class=\"col-2\">\r\n                      <button *ngIf=\"isUpdate\" type=\"button\" class=\"btn btn-outline-success m-1  btn-sm\"\r\n                        (click)=\"onEdit(data,dialog)\"><i class=\"fas fa-edit mr-1\"></i>編輯</button>\r\n                      <button *ngIf=\"isDelete\" type=\"button\" class=\"btn btn-outline-danger m-1  btn-sm\"\r\n                        (click)=\"onDelete(data)\"><i class=\"far fa-trash-alt mr-1\"></i>刪除</button>\r\n                    </td>\r\n                  </tr>\r\n                </tbody>\r\n              </table>\r\n            </div>\r\n            <ngx-pagination [CollectionSize]=\"totalRecords\" [(Page)]=\"pageIndex\" [PageSize]=\"pageSize\"\r\n              (PageChange)=\"getList()\">\r\n            </ngx-pagination>\r\n          </div>\r\n        </div>\r\n      </nb-tab>\r\n\r\n      <nb-tab tabTitle=\"模板\">\r\n        <div class=\"pt-3\">\r\n          <!-- 模板列表 -->\r\n          <div class=\"col-12 mt-3\">\r\n            <div class=\"table-responsive\">\r\n              <table class=\"table table-striped border \" style=\"min-width: 800px; background-color:#f3f3f3;\">\r\n                <thead>\r\n                  <tr style=\"background-color: #27ae60;\" class=\"d-flex text-white\">\r\n                    <th scope=\"col\" class=\"col-2\">工程項目</th>\r\n                    <th scope=\"col\" class=\"col-2\">群組類別</th>\r\n                    <th scope=\"col\" class=\"col-1\">類型</th>\r\n                    <th scope=\"col\" class=\"col-1\">排序</th>\r\n                    <th scope=\"col\" class=\"col-1\">狀態</th>\r\n                    <th scope=\"col\" class=\"col-1\">客變需求顯示</th>\r\n                    <th scope=\"col\" class=\"col-1\">單價</th>\r\n                    <th scope=\"col\" class=\"col-3\">操作功能</th>\r\n                  </tr>\r\n                </thead>\r\n                <tbody>\r\n                  <tr *ngFor=\"let data of requirementList; let i = index\" class=\"d-flex\">\r\n                    <td class=\"col-2\">{{ data.CRequirement }}</td>\r\n                    <td class=\"col-2\">{{ data.CGroupName }}</td>\r\n                    <td class=\"col-1\">{{ getHouseType(data.CHouseType || []) }}</td>\r\n                    <td class=\"col-1\">{{ data.CSort }}</td>\r\n                    <td class=\"col-1\">{{ data.CStatus | getStatusName }}</td>\r\n                    <td class=\"col-1\">{{ getCIsShowText(data) }}</td>\r\n                    <td class=\"col-1\">{{ (data.CUnitPrice || 0) | ngxNumberWithCommas }}</td>\r\n                    <td class=\"col-3\">\r\n                      <button *ngIf=\"isUpdate\" type=\"button\" class=\"btn btn-outline-success m-1  btn-sm\"\r\n                        (click)=\"onEditTemplate(data,templateDialog)\"><i class=\"fas fa-edit mr-1\"></i>編輯</button>\r\n                      <button *ngIf=\"isDelete\" type=\"button\" class=\"btn btn-outline-danger m-1  btn-sm\"\r\n                        (click)=\"onDelete(data)\"><i class=\"far fa-trash-alt mr-1\"></i>刪除</button>\r\n                    </td>\r\n                  </tr>\r\n                </tbody>\r\n              </table>\r\n            </div>\r\n            <ngx-pagination [CollectionSize]=\"totalRecords\" [(Page)]=\"pageIndex\" [PageSize]=\"pageSize\"\r\n              (PageChange)=\"getList()\">\r\n            </ngx-pagination>\r\n          </div>\r\n        </div>\r\n      </nb-tab>\r\n    </nb-tabset>\r\n  </nb-card-body>\r\n</nb-card>\r\n\r\n<!-- 建案對話框 -->\r\n<ng-template #dialog let-data let-ref=\"dialogRef\">\r\n  <nb-card style=\"height: 100%; overflow: auto; max-width: 500px;\" class=\"mr-md-5 ml-md-5\">\r\n    <nb-card-header>\r\n      <span *ngIf=\"isNew===true\">新增建案需求</span>\r\n      <span *ngIf=\"isNew===false\">編輯建案需求</span>\r\n    </nb-card-header>\r\n    <nb-card-body style=\"padding:1rem 2rem\">\r\n      <div class=\"row\">\r\n        <div class=\"col-12 col-md-12\">\r\n          <div class=\"row\">\r\n            <app-form-group [label]=\"'建案名稱'\" [labelFor]=\"'CBuildCaseID'\" [isRequired]=\"true\">\r\n              <nb-select class=\"flex-grow-1\" placeholder=\"請選擇\" id=\"CBuildCaseID\" name=\"CBuildCaseID\"\r\n                [(selected)]=\"saveRequirement.CBuildCaseID\">\r\n                <nb-option langg *ngFor=\"let b of buildCaseList\" [value]=\"b.cID\"> {{b.CBuildCaseName}}</nb-option>\r\n              </nb-select>\r\n            </app-form-group>\r\n            <app-form-group [label]=\"'工程項目'\" [labelFor]=\"'CRequirement'\" [isRequired]=\"true\">\r\n              <input type=\"text\" nbInput class=\"flex-grow-1\" id=\"CRequirement\" name=\"CRequirement\" placeholder=\"工程項目\"\r\n                [(ngModel)]=\"saveRequirement.CRequirement\" maxlength=\"50\">\r\n            </app-form-group>\r\n            <app-form-group [label]=\"'群組類別'\" [labelFor]=\"'CGroupName'\" [isRequired]=\"false\">\r\n              <input type=\"text\" nbInput class=\"flex-grow-1\" id=\"CGroupName\" name=\"CGroupName\" placeholder=\"群組類別\"\r\n                [(ngModel)]=\"saveRequirement.CGroupName\" maxlength=\"20\">\r\n            </app-form-group>\r\n            <app-form-group [label]=\"'排序'\" [labelFor]=\"'CSort'\" [isRequired]=\"true\">\r\n              <input type=\"number\" nbInput class=\"flex-grow-1\" id=\"CSort\" name=\"CSort\" placeholder=\"排序\"\r\n                [(ngModel)]=\"saveRequirement.CSort\">\r\n            </app-form-group>\r\n            <app-form-group [label]=\"'類型'\" [labelFor]=\"'CHouseType'\" [isRequired]=\"true\">\r\n              <nb-select class=\"flex-grow-1\" placeholder=\"請選擇\" id=\"CHouseType\" name=\"CHouseType\"\r\n                [(selected)]=\"saveRequirement.CHouseType\" multiple>\r\n                <nb-option langg *ngFor=\"let type of houseType\" [value]=\"type.value\"> {{type.label}}</nb-option>\r\n              </nb-select>\r\n            </app-form-group>\r\n            <app-form-group [label]=\"'狀態'\" [labelFor]=\"'CStatus'\" [isRequired]=\"true\">\r\n              <nb-select class=\"flex-grow-1\" placeholder=\"請選擇\" id=\"CStatus\" name=\"CStatus\"\r\n                [(selected)]=\"saveRequirement.CStatus\">\r\n                <nb-option langg *ngFor=\"let status of statusOptions\" [value]=\"status.value\">\r\n                  {{status.label}}</nb-option>\r\n              </nb-select>\r\n            </app-form-group>\r\n            <app-form-group [label]=\"'單價'\" [labelFor]=\"'CUnitPrice'\" [isRequired]=\"true\">\r\n              <input type=\"number\" nbInput class=\"flex-grow-1\" id=\"CUnitPrice\" name=\"CUnitPrice\" placeholder=\"單價\"\r\n                [(ngModel)]=\"saveRequirement.CUnitPrice\">\r\n            </app-form-group>\r\n            <app-form-group [label]=\"'單位'\" [labelFor]=\"'CUnit'\" [isRequired]=\"true\">\r\n              <input type=\"text\" nbInput class=\"flex-grow-1\" id=\"CUnit\" name=\"CUnit\" placeholder=\"單位\"\r\n                [(ngModel)]=\"saveRequirement.CUnit\">\r\n            </app-form-group>\r\n            <app-form-group [label]=\"'客變需求顯示'\" [labelFor]=\"'CIsShow'\" [isRequired]=\"false\">\r\n              <nb-checkbox id=\"CIsShow\" name=\"CIsShow\" [(ngModel)]=\"saveRequirement.CIsShow\" class=\"flex-grow-1\">\r\n                顯示在客變需求\r\n              </nb-checkbox>\r\n            </app-form-group>\r\n            <app-form-group [label]=\"'備註說明'\" [labelFor]=\"'CRemark'\" [isRequired]=\"false\">\r\n              <textarea nbInput class=\"flex-grow-1\" id=\"CRemark\" name=\"CRemark\" placeholder=\"備註說明\"\r\n                [(ngModel)]=\"saveRequirement.CRemark\" maxlength=\"100\" rows=\"3\"></textarea>\r\n            </app-form-group>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </nb-card-body>\r\n    <nb-card-footer>\r\n      <div class=\"row\">\r\n        <div class=\"col-12 text-center\">\r\n          <button class=\"btn btn-success mr-2\" (click)=\"save(ref)\">確定</button>\r\n          <button class=\"btn btn-danger mr-2\" (click)=\"ref.close()\">取消</button>\r\n        </div>\r\n      </div>\r\n    </nb-card-footer>\r\n  </nb-card>\r\n</ng-template>\r\n\r\n<!-- 模板對話框 -->\r\n<ng-template #templateDialog let-data let-ref=\"dialogRef\">\r\n  <nb-card style=\"height: 100%; overflow: auto; max-width: 500px;\" class=\"mr-md-5 ml-md-5\">\r\n    <nb-card-header>\r\n      <span *ngIf=\"isNew===true\">新增模板需求</span>\r\n      <span *ngIf=\"isNew===false\">編輯模板需求</span>\r\n    </nb-card-header>\r\n    <nb-card-body style=\"padding:1rem 2rem\">\r\n      <div class=\"row\">\r\n        <div class=\"col-12 col-md-12\">\r\n          <div class=\"row\">\r\n            <app-form-group [label]=\"'工程項目'\" [labelFor]=\"'CRequirement'\" [isRequired]=\"true\">\r\n              <input type=\"text\" nbInput class=\"flex-grow-1\" id=\"CRequirement\" name=\"CRequirement\" placeholder=\"工程項目\"\r\n                [(ngModel)]=\"saveRequirement.CRequirement\" maxlength=\"50\">\r\n            </app-form-group>\r\n            <app-form-group [label]=\"'群組類別'\" [labelFor]=\"'CGroupName'\" [isRequired]=\"false\">\r\n              <input type=\"text\" nbInput class=\"flex-grow-1\" id=\"CGroupName\" name=\"CGroupName\" placeholder=\"群組類別\"\r\n                [(ngModel)]=\"saveRequirement.CGroupName\" maxlength=\"20\">\r\n            </app-form-group>\r\n            <app-form-group [label]=\"'排序'\" [labelFor]=\"'CSort'\" [isRequired]=\"true\">\r\n              <input type=\"number\" nbInput class=\"flex-grow-1\" id=\"CSort\" name=\"CSort\" placeholder=\"排序\"\r\n                [(ngModel)]=\"saveRequirement.CSort\">\r\n            </app-form-group>\r\n            <app-form-group [label]=\"'類型'\" [labelFor]=\"'CHouseType'\" [isRequired]=\"true\">\r\n              <nb-select class=\"flex-grow-1\" placeholder=\"請選擇\" id=\"CHouseType\" name=\"CHouseType\"\r\n                [(selected)]=\"saveRequirement.CHouseType\" multiple>\r\n                <nb-option langg *ngFor=\"let type of houseType\" [value]=\"type.value\"> {{type.label}}</nb-option>\r\n              </nb-select>\r\n            </app-form-group>\r\n            <app-form-group [label]=\"'狀態'\" [labelFor]=\"'CStatus'\" [isRequired]=\"true\">\r\n              <nb-select class=\"flex-grow-1\" placeholder=\"請選擇\" id=\"CStatus\" name=\"CStatus\"\r\n                [(selected)]=\"saveRequirement.CStatus\">\r\n                <nb-option langg *ngFor=\"let status of statusOptions\" [value]=\"status.value\">\r\n                  {{status.label}}</nb-option>\r\n              </nb-select>\r\n            </app-form-group>\r\n            <app-form-group [label]=\"'單價'\" [labelFor]=\"'CUnitPrice'\" [isRequired]=\"true\">\r\n              <input type=\"number\" nbInput class=\"flex-grow-1\" id=\"CUnitPrice\" name=\"CUnitPrice\" placeholder=\"單價\"\r\n                [(ngModel)]=\"saveRequirement.CUnitPrice\">\r\n            </app-form-group>\r\n            <app-form-group [label]=\"'單位'\" [labelFor]=\"'CUnit'\" [isRequired]=\"true\">\r\n              <input type=\"text\" nbInput class=\"flex-grow-1\" id=\"CUnit\" name=\"CUnit\" placeholder=\"單位\"\r\n                [(ngModel)]=\"saveRequirement.CUnit\">\r\n            </app-form-group>\r\n            <app-form-group [label]=\"'客變需求顯示'\" [labelFor]=\"'CIsShow'\" [isRequired]=\"false\">\r\n              <nb-checkbox id=\"CIsShow\" name=\"CIsShow\" [(ngModel)]=\"saveRequirement.CIsShow\" class=\"flex-grow-1\">\r\n                顯示在客變需求\r\n              </nb-checkbox>\r\n            </app-form-group>\r\n            <app-form-group [label]=\"'備註說明'\" [labelFor]=\"'CRemark'\" [isRequired]=\"false\">\r\n              <textarea nbInput class=\"flex-grow-1\" id=\"CRemark\" name=\"CRemark\" placeholder=\"備註說明\"\r\n                [(ngModel)]=\"saveRequirement.CRemark\" maxlength=\"100\" rows=\"3\"></textarea>\r\n            </app-form-group>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </nb-card-body>\r\n    <nb-card-footer>\r\n      <div class=\"row\">\r\n        <div class=\"col-12 text-center\">\r\n          <button class=\"btn btn-success mr-2\" (click)=\"saveTemplate(ref)\">確定</button>\r\n          <button class=\"btn btn-danger mr-2\" (click)=\"ref.close()\">取消</button>\r\n        </div>\r\n      </div>\r\n    </nb-card-footer>\r\n  </nb-card>\r\n</ng-template>"], "mappings": ";AAEA,SAASA,YAAY,EAAEC,gBAAgB,EAAmBC,aAAa,EAAEC,cAAc,EAAEC,cAAc,EAAEC,cAAc,QAAQ,gBAAgB;AAO/I,SAASC,aAAa,QAAQ,kCAAkC;AAChE,SAASC,kBAAkB,QAAQ,4BAA4B;AAE/D,SAASC,mBAAmB,QAAQ,+CAA+C;AACnF,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,KAAK,EAAEC,IAAI,QAAQ,iBAAiB;AAC7C,SAASC,mBAAmB,QAAQ,+CAA+C;AACnF,SAASC,UAAU,QAAQ,mCAAmC;AAC9D,SAASC,kBAAkB,QAAQ,2DAA2D;AAC9F,SAASC,oBAAoB,QAAQ,8CAA8C;AACnF,SAASC,aAAa,QAAQ,mCAAmC;;;;;;;;;;;;;;ICNrDC,EAAA,CAAAC,cAAA,mBAAiE;IAC/DD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAFkCH,EAAA,CAAAI,UAAA,UAAAC,OAAA,CAAAC,GAAA,CAAkB;IAC9DN,EAAA,CAAAO,SAAA,EACF;IADEP,EAAA,CAAAQ,kBAAA,MAAAH,OAAA,CAAAI,cAAA,MACF;;;;;IAkBAT,EAAA,CAAAC,cAAA,mBAA+D;IAC7DD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAF8BH,EAAA,CAAAI,UAAA,UAAAM,OAAA,CAAAC,KAAA,CAAoB;IAC5DX,EAAA,CAAAO,SAAA,EACF;IADEP,EAAA,CAAAQ,kBAAA,MAAAE,OAAA,CAAAE,KAAA,MACF;;;;;;IAyBFZ,EAAA,CAAAC,cAAA,iBAAgG;IAA3DD,EAAA,CAAAa,UAAA,mBAAAC,0EAAA;MAAAd,EAAA,CAAAe,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAAA,MAAAC,SAAA,GAAAnB,EAAA,CAAAoB,WAAA;MAAA,OAAApB,EAAA,CAAAqB,WAAA,CAASJ,MAAA,CAAAK,GAAA,CAAAH,SAAA,CAAW;IAAA,EAAC;IAAsCnB,EAAA,CAAAuB,SAAA,YAC/D;IAAAvB,EAAA,CAAAE,MAAA,+BAAI;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;IAC9CH,EAAA,CAAAC,cAAA,iBACuC;IADFD,EAAA,CAAAa,UAAA,mBAAAW,0EAAA;MAAAxB,EAAA,CAAAe,aAAA,CAAAU,GAAA;MAAA,MAAAR,MAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAAA,MAAAQ,iBAAA,GAAA1B,EAAA,CAAAoB,WAAA;MAAA,OAAApB,EAAA,CAAAqB,WAAA,CAASJ,MAAA,CAAAU,WAAA,CAAAD,iBAAA,CAA2B;IAAA,EAAC;IACnC1B,EAAA,CAAAuB,SAAA,YAAgC;IAAAvB,EAAA,CAAAE,MAAA,+BAAI;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;IAuCxEH,EAAA,CAAAC,cAAA,iBACgC;IAA9BD,EAAA,CAAAa,UAAA,mBAAAe,gFAAA;MAAA5B,EAAA,CAAAe,aAAA,CAAAc,GAAA;MAAA,MAAAC,QAAA,GAAA9B,EAAA,CAAAkB,aAAA,GAAAa,SAAA;MAAA,MAAAd,MAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAAA,MAAAC,SAAA,GAAAnB,EAAA,CAAAoB,WAAA;MAAA,OAAApB,EAAA,CAAAqB,WAAA,CAASJ,MAAA,CAAAe,MAAA,CAAAF,QAAA,EAAAX,SAAA,CAAmB;IAAA,EAAC;IAACnB,EAAA,CAAAuB,SAAA,YAAgC;IAAAvB,EAAA,CAAAE,MAAA,mBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;IAC3EH,EAAA,CAAAC,cAAA,iBAC2B;IAAzBD,EAAA,CAAAa,UAAA,mBAAAoB,gFAAA;MAAAjC,EAAA,CAAAe,aAAA,CAAAmB,IAAA;MAAA,MAAAJ,QAAA,GAAA9B,EAAA,CAAAkB,aAAA,GAAAa,SAAA;MAAA,MAAAd,MAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAAA,OAAAlB,EAAA,CAAAqB,WAAA,CAASJ,MAAA,CAAAkB,QAAA,CAAAL,QAAA,CAAc;IAAA,EAAC;IAAC9B,EAAA,CAAAuB,SAAA,YAAqC;IAAAvB,EAAA,CAAAE,MAAA,mBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;IAZ7EH,EADF,CAAAC,cAAA,aAAuE,aACnD;IAAAD,EAAA,CAAAE,MAAA,GAAyB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAChDH,EAAA,CAAAC,cAAA,aAAkB;IAAAD,EAAA,CAAAE,MAAA,GAAuB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC9CH,EAAA,CAAAC,cAAA,aAAkB;IAAAD,EAAA,CAAAE,MAAA,GAAqB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC5CH,EAAA,CAAAC,cAAA,aAAkB;IAAAD,EAAA,CAAAE,MAAA,GAAyC;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAChEH,EAAA,CAAAC,cAAA,aAAkB;IAAAD,EAAA,CAAAE,MAAA,IAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACvCH,EAAA,CAAAC,cAAA,cAAkB;IAAAD,EAAA,CAAAE,MAAA,IAAkC;;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACzDH,EAAA,CAAAC,cAAA,cAAkB;IAAAD,EAAA,CAAAE,MAAA,IAA0B;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACjDH,EAAA,CAAAC,cAAA,cAAkB;IAAAD,EAAA,CAAAE,MAAA,IAAkD;;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACzEH,EAAA,CAAAC,cAAA,cAAkB;IAGhBD,EAFA,CAAAoC,UAAA,KAAAC,uDAAA,qBACgC,KAAAC,uDAAA,qBAEL;IAE/BtC,EADE,CAAAG,YAAA,EAAK,EACF;;;;;IAdeH,EAAA,CAAAO,SAAA,GAAyB;IAAzBP,EAAA,CAAAuC,iBAAA,CAAAT,QAAA,CAAArB,cAAA,CAAyB;IACzBT,EAAA,CAAAO,SAAA,GAAuB;IAAvBP,EAAA,CAAAuC,iBAAA,CAAAT,QAAA,CAAAU,YAAA,CAAuB;IACvBxC,EAAA,CAAAO,SAAA,GAAqB;IAArBP,EAAA,CAAAuC,iBAAA,CAAAT,QAAA,CAAAW,UAAA,CAAqB;IACrBzC,EAAA,CAAAO,SAAA,GAAyC;IAAzCP,EAAA,CAAAuC,iBAAA,CAAAtB,MAAA,CAAAyB,YAAA,CAAAZ,QAAA,CAAAa,UAAA,IAAA3C,EAAA,CAAA4C,eAAA,KAAAC,GAAA,GAAyC;IACzC7C,EAAA,CAAAO,SAAA,GAAgB;IAAhBP,EAAA,CAAAuC,iBAAA,CAAAT,QAAA,CAAAgB,KAAA,CAAgB;IAChB9C,EAAA,CAAAO,SAAA,GAAkC;IAAlCP,EAAA,CAAAuC,iBAAA,CAAAvC,EAAA,CAAA+C,WAAA,SAAAjB,QAAA,CAAAkB,OAAA,EAAkC;IAClChD,EAAA,CAAAO,SAAA,GAA0B;IAA1BP,EAAA,CAAAuC,iBAAA,CAAAtB,MAAA,CAAAgC,cAAA,CAAAnB,QAAA,EAA0B;IAC1B9B,EAAA,CAAAO,SAAA,GAAkD;IAAlDP,EAAA,CAAAuC,iBAAA,CAAAvC,EAAA,CAAA+C,WAAA,SAAAjB,QAAA,CAAAoB,UAAA,OAAkD;IAEzDlD,EAAA,CAAAO,SAAA,GAAc;IAAdP,EAAA,CAAAI,UAAA,SAAAa,MAAA,CAAAkC,QAAA,CAAc;IAEdnD,EAAA,CAAAO,SAAA,EAAc;IAAdP,EAAA,CAAAI,UAAA,SAAAa,MAAA,CAAAmC,QAAA,CAAc;;;;;;IA0CvBpD,EAAA,CAAAC,cAAA,iBACgD;IAA9CD,EAAA,CAAAa,UAAA,mBAAAwC,iFAAA;MAAArD,EAAA,CAAAe,aAAA,CAAAuC,IAAA;MAAA,MAAAC,QAAA,GAAAvD,EAAA,CAAAkB,aAAA,GAAAa,SAAA;MAAA,MAAAd,MAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAAA,MAAAQ,iBAAA,GAAA1B,EAAA,CAAAoB,WAAA;MAAA,OAAApB,EAAA,CAAAqB,WAAA,CAASJ,MAAA,CAAAuC,cAAA,CAAAD,QAAA,EAAA7B,iBAAA,CAAmC;IAAA,EAAC;IAAC1B,EAAA,CAAAuB,SAAA,YAAgC;IAAAvB,EAAA,CAAAE,MAAA,mBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;IAC3FH,EAAA,CAAAC,cAAA,iBAC2B;IAAzBD,EAAA,CAAAa,UAAA,mBAAA4C,iFAAA;MAAAzD,EAAA,CAAAe,aAAA,CAAA2C,IAAA;MAAA,MAAAH,QAAA,GAAAvD,EAAA,CAAAkB,aAAA,GAAAa,SAAA;MAAA,MAAAd,MAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAAA,OAAAlB,EAAA,CAAAqB,WAAA,CAASJ,MAAA,CAAAkB,QAAA,CAAAoB,QAAA,CAAc;IAAA,EAAC;IAACvD,EAAA,CAAAuB,SAAA,YAAqC;IAAAvB,EAAA,CAAAE,MAAA,mBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;IAX7EH,EADF,CAAAC,cAAA,aAAuE,aACnD;IAAAD,EAAA,CAAAE,MAAA,GAAuB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC9CH,EAAA,CAAAC,cAAA,aAAkB;IAAAD,EAAA,CAAAE,MAAA,GAAqB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC5CH,EAAA,CAAAC,cAAA,aAAkB;IAAAD,EAAA,CAAAE,MAAA,GAAyC;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAChEH,EAAA,CAAAC,cAAA,aAAkB;IAAAD,EAAA,CAAAE,MAAA,GAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACvCH,EAAA,CAAAC,cAAA,aAAkB;IAAAD,EAAA,CAAAE,MAAA,IAAkC;;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACzDH,EAAA,CAAAC,cAAA,cAAkB;IAAAD,EAAA,CAAAE,MAAA,IAA0B;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACjDH,EAAA,CAAAC,cAAA,cAAkB;IAAAD,EAAA,CAAAE,MAAA,IAAkD;;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACzEH,EAAA,CAAAC,cAAA,cAAkB;IAGhBD,EAFA,CAAAoC,UAAA,KAAAuB,wDAAA,qBACgD,KAAAC,wDAAA,qBAErB;IAE/B5D,EADE,CAAAG,YAAA,EAAK,EACF;;;;;IAbeH,EAAA,CAAAO,SAAA,GAAuB;IAAvBP,EAAA,CAAAuC,iBAAA,CAAAgB,QAAA,CAAAf,YAAA,CAAuB;IACvBxC,EAAA,CAAAO,SAAA,GAAqB;IAArBP,EAAA,CAAAuC,iBAAA,CAAAgB,QAAA,CAAAd,UAAA,CAAqB;IACrBzC,EAAA,CAAAO,SAAA,GAAyC;IAAzCP,EAAA,CAAAuC,iBAAA,CAAAtB,MAAA,CAAAyB,YAAA,CAAAa,QAAA,CAAAZ,UAAA,IAAA3C,EAAA,CAAA4C,eAAA,KAAAC,GAAA,GAAyC;IACzC7C,EAAA,CAAAO,SAAA,GAAgB;IAAhBP,EAAA,CAAAuC,iBAAA,CAAAgB,QAAA,CAAAT,KAAA,CAAgB;IAChB9C,EAAA,CAAAO,SAAA,GAAkC;IAAlCP,EAAA,CAAAuC,iBAAA,CAAAvC,EAAA,CAAA+C,WAAA,QAAAQ,QAAA,CAAAP,OAAA,EAAkC;IAClChD,EAAA,CAAAO,SAAA,GAA0B;IAA1BP,EAAA,CAAAuC,iBAAA,CAAAtB,MAAA,CAAAgC,cAAA,CAAAM,QAAA,EAA0B;IAC1BvD,EAAA,CAAAO,SAAA,GAAkD;IAAlDP,EAAA,CAAAuC,iBAAA,CAAAvC,EAAA,CAAA+C,WAAA,SAAAQ,QAAA,CAAAL,UAAA,OAAkD;IAEzDlD,EAAA,CAAAO,SAAA,GAAc;IAAdP,EAAA,CAAAI,UAAA,SAAAa,MAAA,CAAAkC,QAAA,CAAc;IAEdnD,EAAA,CAAAO,SAAA,EAAc;IAAdP,EAAA,CAAAI,UAAA,SAAAa,MAAA,CAAAmC,QAAA,CAAc;;;;;IAqBvCpD,EAAA,CAAAC,cAAA,WAA2B;IAAAD,EAAA,CAAAE,MAAA,2CAAM;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IACxCH,EAAA,CAAAC,cAAA,WAA4B;IAAAD,EAAA,CAAAE,MAAA,2CAAM;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAS/BH,EAAA,CAAAC,cAAA,oBAAiE;IAACD,EAAA,CAAAE,MAAA,GAAoB;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAAjDH,EAAA,CAAAI,UAAA,UAAAyD,KAAA,CAAAvD,GAAA,CAAe;IAAEN,EAAA,CAAAO,SAAA,EAAoB;IAApBP,EAAA,CAAAQ,kBAAA,MAAAqD,KAAA,CAAApD,cAAA,KAAoB;;;;;IAkBtFT,EAAA,CAAAC,cAAA,oBAAqE;IAACD,EAAA,CAAAE,MAAA,GAAc;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAAhDH,EAAA,CAAAI,UAAA,UAAA0D,QAAA,CAAAnD,KAAA,CAAoB;IAAEX,EAAA,CAAAO,SAAA,EAAc;IAAdP,EAAA,CAAAQ,kBAAA,MAAAsD,QAAA,CAAAlD,KAAA,KAAc;;;;;IAMpFZ,EAAA,CAAAC,cAAA,oBAA6E;IAC3ED,EAAA,CAAAE,MAAA,GAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IADwBH,EAAA,CAAAI,UAAA,UAAA2D,UAAA,CAAApD,KAAA,CAAsB;IAC1EX,EAAA,CAAAO,SAAA,EAAgB;IAAhBP,EAAA,CAAAQ,kBAAA,MAAAuD,UAAA,CAAAnD,KAAA,KAAgB;;;;;;IApC9BZ,EADF,CAAAC,cAAA,kBAAyF,qBACvE;IAEdD,EADA,CAAAoC,UAAA,IAAA4B,8DAAA,mBAA2B,IAAAC,8DAAA,mBACC;IAC9BjE,EAAA,CAAAG,YAAA,EAAiB;IAMPH,EALV,CAAAC,cAAA,uBAAwC,aACrB,cACe,aACX,yBACkE,oBAEjC;IAA5CD,EAAA,CAAAkE,gBAAA,4BAAAC,4FAAAC,MAAA;MAAApE,EAAA,CAAAe,aAAA,CAAAsD,IAAA;MAAA,MAAApD,MAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAAAlB,EAAA,CAAAsE,kBAAA,CAAArD,MAAA,CAAAsD,eAAA,CAAAC,YAAA,EAAAJ,MAAA,MAAAnD,MAAA,CAAAsD,eAAA,CAAAC,YAAA,GAAAJ,MAAA;MAAA,OAAApE,EAAA,CAAAqB,WAAA,CAAA+C,MAAA;IAAA,EAA2C;IAC3CpE,EAAA,CAAAoC,UAAA,KAAAqC,oEAAA,wBAAiE;IAErEzE,EADE,CAAAG,YAAA,EAAY,EACG;IAEfH,EADF,CAAAC,cAAA,0BAAiF,iBAEnB;IAA1DD,EAAA,CAAAkE,gBAAA,2BAAAQ,wFAAAN,MAAA;MAAApE,EAAA,CAAAe,aAAA,CAAAsD,IAAA;MAAA,MAAApD,MAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAAAlB,EAAA,CAAAsE,kBAAA,CAAArD,MAAA,CAAAsD,eAAA,CAAA/B,YAAA,EAAA4B,MAAA,MAAAnD,MAAA,CAAAsD,eAAA,CAAA/B,YAAA,GAAA4B,MAAA;MAAA,OAAApE,EAAA,CAAAqB,WAAA,CAAA+C,MAAA;IAAA,EAA0C;IAC9CpE,EAFE,CAAAG,YAAA,EAC4D,EAC7C;IAEfH,EADF,CAAAC,cAAA,0BAAgF,iBAEpB;IAAxDD,EAAA,CAAAkE,gBAAA,2BAAAS,wFAAAP,MAAA;MAAApE,EAAA,CAAAe,aAAA,CAAAsD,IAAA;MAAA,MAAApD,MAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAAAlB,EAAA,CAAAsE,kBAAA,CAAArD,MAAA,CAAAsD,eAAA,CAAA9B,UAAA,EAAA2B,MAAA,MAAAnD,MAAA,CAAAsD,eAAA,CAAA9B,UAAA,GAAA2B,MAAA;MAAA,OAAApE,EAAA,CAAAqB,WAAA,CAAA+C,MAAA;IAAA,EAAwC;IAC5CpE,EAFE,CAAAG,YAAA,EAC0D,EAC3C;IAEfH,EADF,CAAAC,cAAA,0BAAwE,iBAEhC;IAApCD,EAAA,CAAAkE,gBAAA,2BAAAU,wFAAAR,MAAA;MAAApE,EAAA,CAAAe,aAAA,CAAAsD,IAAA;MAAA,MAAApD,MAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAAAlB,EAAA,CAAAsE,kBAAA,CAAArD,MAAA,CAAAsD,eAAA,CAAAzB,KAAA,EAAAsB,MAAA,MAAAnD,MAAA,CAAAsD,eAAA,CAAAzB,KAAA,GAAAsB,MAAA;MAAA,OAAApE,EAAA,CAAAqB,WAAA,CAAA+C,MAAA;IAAA,EAAmC;IACvCpE,EAFE,CAAAG,YAAA,EACsC,EACvB;IAEfH,EADF,CAAAC,cAAA,0BAA6E,qBAEtB;IAAnDD,EAAA,CAAAkE,gBAAA,4BAAAW,6FAAAT,MAAA;MAAApE,EAAA,CAAAe,aAAA,CAAAsD,IAAA;MAAA,MAAApD,MAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAAAlB,EAAA,CAAAsE,kBAAA,CAAArD,MAAA,CAAAsD,eAAA,CAAA5B,UAAA,EAAAyB,MAAA,MAAAnD,MAAA,CAAAsD,eAAA,CAAA5B,UAAA,GAAAyB,MAAA;MAAA,OAAApE,EAAA,CAAAqB,WAAA,CAAA+C,MAAA;IAAA,EAAyC;IACzCpE,EAAA,CAAAoC,UAAA,KAAA0C,oEAAA,wBAAqE;IAEzE9E,EADE,CAAAG,YAAA,EAAY,EACG;IAEfH,EADF,CAAAC,cAAA,0BAA0E,qBAE/B;IAAvCD,EAAA,CAAAkE,gBAAA,4BAAAa,6FAAAX,MAAA;MAAApE,EAAA,CAAAe,aAAA,CAAAsD,IAAA;MAAA,MAAApD,MAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAAAlB,EAAA,CAAAsE,kBAAA,CAAArD,MAAA,CAAAsD,eAAA,CAAAvB,OAAA,EAAAoB,MAAA,MAAAnD,MAAA,CAAAsD,eAAA,CAAAvB,OAAA,GAAAoB,MAAA;MAAA,OAAApE,EAAA,CAAAqB,WAAA,CAAA+C,MAAA;IAAA,EAAsC;IACtCpE,EAAA,CAAAoC,UAAA,KAAA4C,oEAAA,wBAA6E;IAGjFhF,EADE,CAAAG,YAAA,EAAY,EACG;IAEfH,EADF,CAAAC,cAAA,0BAA6E,iBAEhC;IAAzCD,EAAA,CAAAkE,gBAAA,2BAAAe,wFAAAb,MAAA;MAAApE,EAAA,CAAAe,aAAA,CAAAsD,IAAA;MAAA,MAAApD,MAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAAAlB,EAAA,CAAAsE,kBAAA,CAAArD,MAAA,CAAAsD,eAAA,CAAArB,UAAA,EAAAkB,MAAA,MAAAnD,MAAA,CAAAsD,eAAA,CAAArB,UAAA,GAAAkB,MAAA;MAAA,OAAApE,EAAA,CAAAqB,WAAA,CAAA+C,MAAA;IAAA,EAAwC;IAC5CpE,EAFE,CAAAG,YAAA,EAC2C,EAC5B;IAEfH,EADF,CAAAC,cAAA,0BAAwE,iBAEhC;IAApCD,EAAA,CAAAkE,gBAAA,2BAAAgB,wFAAAd,MAAA;MAAApE,EAAA,CAAAe,aAAA,CAAAsD,IAAA;MAAA,MAAApD,MAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAAAlB,EAAA,CAAAsE,kBAAA,CAAArD,MAAA,CAAAsD,eAAA,CAAAY,KAAA,EAAAf,MAAA,MAAAnD,MAAA,CAAAsD,eAAA,CAAAY,KAAA,GAAAf,MAAA;MAAA,OAAApE,EAAA,CAAAqB,WAAA,CAAA+C,MAAA;IAAA,EAAmC;IACvCpE,EAFE,CAAAG,YAAA,EACsC,EACvB;IAEfH,EADF,CAAAC,cAAA,0BAA+E,uBACsB;IAA1DD,EAAA,CAAAkE,gBAAA,2BAAAkB,8FAAAhB,MAAA;MAAApE,EAAA,CAAAe,aAAA,CAAAsD,IAAA;MAAA,MAAApD,MAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAAAlB,EAAA,CAAAsE,kBAAA,CAAArD,MAAA,CAAAsD,eAAA,CAAAc,OAAA,EAAAjB,MAAA,MAAAnD,MAAA,CAAAsD,eAAA,CAAAc,OAAA,GAAAjB,MAAA;MAAA,OAAApE,EAAA,CAAAqB,WAAA,CAAA+C,MAAA;IAAA,EAAqC;IAC5EpE,EAAA,CAAAE,MAAA,oDACF;IACFF,EADE,CAAAG,YAAA,EAAc,EACC;IAEfH,EADF,CAAAC,cAAA,0BAA6E,oBAEV;IAA/DD,EAAA,CAAAkE,gBAAA,2BAAAoB,2FAAAlB,MAAA;MAAApE,EAAA,CAAAe,aAAA,CAAAsD,IAAA;MAAA,MAAApD,MAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAAAlB,EAAA,CAAAsE,kBAAA,CAAArD,MAAA,CAAAsD,eAAA,CAAAgB,OAAA,EAAAnB,MAAA,MAAAnD,MAAA,CAAAsD,eAAA,CAAAgB,OAAA,GAAAnB,MAAA;MAAA,OAAApE,EAAA,CAAAqB,WAAA,CAAA+C,MAAA;IAAA,EAAqC;IAKjDpE,EAL2E,CAAAG,YAAA,EAAW,EAC7D,EACb,EACF,EACF,EACO;IAITH,EAHN,CAAAC,cAAA,sBAAgB,cACG,eACiB,kBAC2B;IAApBD,EAAA,CAAAa,UAAA,mBAAA2E,iFAAA;MAAA,MAAAC,OAAA,GAAAzF,EAAA,CAAAe,aAAA,CAAAsD,IAAA,EAAAqB,SAAA;MAAA,MAAAzE,MAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAAA,OAAAlB,EAAA,CAAAqB,WAAA,CAASJ,MAAA,CAAA0E,IAAA,CAAAF,OAAA,CAAS;IAAA,EAAC;IAACzF,EAAA,CAAAE,MAAA,oBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACpEH,EAAA,CAAAC,cAAA,kBAA0D;IAAtBD,EAAA,CAAAa,UAAA,mBAAA+E,iFAAA;MAAA,MAAAH,OAAA,GAAAzF,EAAA,CAAAe,aAAA,CAAAsD,IAAA,EAAAqB,SAAA;MAAA,OAAA1F,EAAA,CAAAqB,WAAA,CAASoE,OAAA,CAAAI,KAAA,EAAW;IAAA,EAAC;IAAC7F,EAAA,CAAAE,MAAA,oBAAE;IAIpEF,EAJoE,CAAAG,YAAA,EAAS,EACjE,EACF,EACS,EACT;;;;IAnECH,EAAA,CAAAO,SAAA,GAAkB;IAAlBP,EAAA,CAAAI,UAAA,SAAAa,MAAA,CAAA6E,KAAA,UAAkB;IAClB9F,EAAA,CAAAO,SAAA,EAAmB;IAAnBP,EAAA,CAAAI,UAAA,SAAAa,MAAA,CAAA6E,KAAA,WAAmB;IAMJ9F,EAAA,CAAAO,SAAA,GAAgB;IAA6BP,EAA7C,CAAAI,UAAA,qCAAgB,4BAA4B,oBAAoB;IAE5EJ,EAAA,CAAAO,SAAA,EAA2C;IAA3CP,EAAA,CAAA+F,gBAAA,aAAA9E,MAAA,CAAAsD,eAAA,CAAAC,YAAA,CAA2C;IACZxE,EAAA,CAAAO,SAAA,EAAgB;IAAhBP,EAAA,CAAAI,UAAA,YAAAa,MAAA,CAAA+E,aAAA,CAAgB;IAGnChG,EAAA,CAAAO,SAAA,EAAgB;IAA6BP,EAA7C,CAAAI,UAAA,qCAAgB,4BAA4B,oBAAoB;IAE5EJ,EAAA,CAAAO,SAAA,EAA0C;IAA1CP,EAAA,CAAA+F,gBAAA,YAAA9E,MAAA,CAAAsD,eAAA,CAAA/B,YAAA,CAA0C;IAE9BxC,EAAA,CAAAO,SAAA,EAAgB;IAA2BP,EAA3C,CAAAI,UAAA,qCAAgB,0BAA0B,qBAAqB;IAE3EJ,EAAA,CAAAO,SAAA,EAAwC;IAAxCP,EAAA,CAAA+F,gBAAA,YAAA9E,MAAA,CAAAsD,eAAA,CAAA9B,UAAA,CAAwC;IAE5BzC,EAAA,CAAAO,SAAA,EAAc;IAAsBP,EAApC,CAAAI,UAAA,yBAAc,qBAAqB,oBAAoB;IAEnEJ,EAAA,CAAAO,SAAA,EAAmC;IAAnCP,EAAA,CAAA+F,gBAAA,YAAA9E,MAAA,CAAAsD,eAAA,CAAAzB,KAAA,CAAmC;IAEvB9C,EAAA,CAAAO,SAAA,EAAc;IAA2BP,EAAzC,CAAAI,UAAA,yBAAc,0BAA0B,oBAAoB;IAExEJ,EAAA,CAAAO,SAAA,EAAyC;IAAzCP,EAAA,CAAA+F,gBAAA,aAAA9E,MAAA,CAAAsD,eAAA,CAAA5B,UAAA,CAAyC;IACP3C,EAAA,CAAAO,SAAA,EAAY;IAAZP,EAAA,CAAAI,UAAA,YAAAa,MAAA,CAAAgF,SAAA,CAAY;IAGlCjG,EAAA,CAAAO,SAAA,EAAc;IAAwBP,EAAtC,CAAAI,UAAA,yBAAc,uBAAuB,oBAAoB;IAErEJ,EAAA,CAAAO,SAAA,EAAsC;IAAtCP,EAAA,CAAA+F,gBAAA,aAAA9E,MAAA,CAAAsD,eAAA,CAAAvB,OAAA,CAAsC;IACFhD,EAAA,CAAAO,SAAA,EAAgB;IAAhBP,EAAA,CAAAI,UAAA,YAAAa,MAAA,CAAAiF,aAAA,CAAgB;IAIxClG,EAAA,CAAAO,SAAA,EAAc;IAA2BP,EAAzC,CAAAI,UAAA,yBAAc,0BAA0B,oBAAoB;IAExEJ,EAAA,CAAAO,SAAA,EAAwC;IAAxCP,EAAA,CAAA+F,gBAAA,YAAA9E,MAAA,CAAAsD,eAAA,CAAArB,UAAA,CAAwC;IAE5BlD,EAAA,CAAAO,SAAA,EAAc;IAAsBP,EAApC,CAAAI,UAAA,yBAAc,qBAAqB,oBAAoB;IAEnEJ,EAAA,CAAAO,SAAA,EAAmC;IAAnCP,EAAA,CAAA+F,gBAAA,YAAA9E,MAAA,CAAAsD,eAAA,CAAAY,KAAA,CAAmC;IAEvBnF,EAAA,CAAAO,SAAA,EAAkB;IAAwBP,EAA1C,CAAAI,UAAA,iDAAkB,uBAAuB,qBAAqB;IACnCJ,EAAA,CAAAO,SAAA,EAAqC;IAArCP,EAAA,CAAA+F,gBAAA,YAAA9E,MAAA,CAAAsD,eAAA,CAAAc,OAAA,CAAqC;IAIhErF,EAAA,CAAAO,SAAA,GAAgB;IAAwBP,EAAxC,CAAAI,UAAA,qCAAgB,uBAAuB,qBAAqB;IAExEJ,EAAA,CAAAO,SAAA,EAAqC;IAArCP,EAAA,CAAA+F,gBAAA,YAAA9E,MAAA,CAAAsD,eAAA,CAAAgB,OAAA,CAAqC;;;;;IAqB/CvF,EAAA,CAAAC,cAAA,WAA2B;IAAAD,EAAA,CAAAE,MAAA,2CAAM;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IACxCH,EAAA,CAAAC,cAAA,WAA4B;IAAAD,EAAA,CAAAE,MAAA,2CAAM;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAqB/BH,EAAA,CAAAC,cAAA,oBAAqE;IAACD,EAAA,CAAAE,MAAA,GAAc;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAAhDH,EAAA,CAAAI,UAAA,UAAA+F,QAAA,CAAAxF,KAAA,CAAoB;IAAEX,EAAA,CAAAO,SAAA,EAAc;IAAdP,EAAA,CAAAQ,kBAAA,MAAA2F,QAAA,CAAAvF,KAAA,KAAc;;;;;IAMpFZ,EAAA,CAAAC,cAAA,oBAA6E;IAC3ED,EAAA,CAAAE,MAAA,GAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IADwBH,EAAA,CAAAI,UAAA,UAAAgG,UAAA,CAAAzF,KAAA,CAAsB;IAC1EX,EAAA,CAAAO,SAAA,EAAgB;IAAhBP,EAAA,CAAAQ,kBAAA,MAAA4F,UAAA,CAAAxF,KAAA,KAAgB;;;;;;IA9B9BZ,EADF,CAAAC,cAAA,kBAAyF,qBACvE;IAEdD,EADA,CAAAoC,UAAA,IAAAiE,8DAAA,mBAA2B,IAAAC,8DAAA,mBACC;IAC9BtG,EAAA,CAAAG,YAAA,EAAiB;IAMPH,EALV,CAAAC,cAAA,uBAAwC,aACrB,cACe,aACX,yBACkE,gBAEnB;IAA1DD,EAAA,CAAAkE,gBAAA,2BAAAqC,uFAAAnC,MAAA;MAAApE,EAAA,CAAAe,aAAA,CAAAyF,IAAA;MAAA,MAAAvF,MAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAAAlB,EAAA,CAAAsE,kBAAA,CAAArD,MAAA,CAAAsD,eAAA,CAAA/B,YAAA,EAAA4B,MAAA,MAAAnD,MAAA,CAAAsD,eAAA,CAAA/B,YAAA,GAAA4B,MAAA;MAAA,OAAApE,EAAA,CAAAqB,WAAA,CAAA+C,MAAA;IAAA,EAA0C;IAC9CpE,EAFE,CAAAG,YAAA,EAC4D,EAC7C;IAEfH,EADF,CAAAC,cAAA,0BAAgF,iBAEpB;IAAxDD,EAAA,CAAAkE,gBAAA,2BAAAuC,wFAAArC,MAAA;MAAApE,EAAA,CAAAe,aAAA,CAAAyF,IAAA;MAAA,MAAAvF,MAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAAAlB,EAAA,CAAAsE,kBAAA,CAAArD,MAAA,CAAAsD,eAAA,CAAA9B,UAAA,EAAA2B,MAAA,MAAAnD,MAAA,CAAAsD,eAAA,CAAA9B,UAAA,GAAA2B,MAAA;MAAA,OAAApE,EAAA,CAAAqB,WAAA,CAAA+C,MAAA;IAAA,EAAwC;IAC5CpE,EAFE,CAAAG,YAAA,EAC0D,EAC3C;IAEfH,EADF,CAAAC,cAAA,0BAAwE,iBAEhC;IAApCD,EAAA,CAAAkE,gBAAA,2BAAAwC,wFAAAtC,MAAA;MAAApE,EAAA,CAAAe,aAAA,CAAAyF,IAAA;MAAA,MAAAvF,MAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAAAlB,EAAA,CAAAsE,kBAAA,CAAArD,MAAA,CAAAsD,eAAA,CAAAzB,KAAA,EAAAsB,MAAA,MAAAnD,MAAA,CAAAsD,eAAA,CAAAzB,KAAA,GAAAsB,MAAA;MAAA,OAAApE,EAAA,CAAAqB,WAAA,CAAA+C,MAAA;IAAA,EAAmC;IACvCpE,EAFE,CAAAG,YAAA,EACsC,EACvB;IAEfH,EADF,CAAAC,cAAA,0BAA6E,qBAEtB;IAAnDD,EAAA,CAAAkE,gBAAA,4BAAAyC,6FAAAvC,MAAA;MAAApE,EAAA,CAAAe,aAAA,CAAAyF,IAAA;MAAA,MAAAvF,MAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAAAlB,EAAA,CAAAsE,kBAAA,CAAArD,MAAA,CAAAsD,eAAA,CAAA5B,UAAA,EAAAyB,MAAA,MAAAnD,MAAA,CAAAsD,eAAA,CAAA5B,UAAA,GAAAyB,MAAA;MAAA,OAAApE,EAAA,CAAAqB,WAAA,CAAA+C,MAAA;IAAA,EAAyC;IACzCpE,EAAA,CAAAoC,UAAA,KAAAwE,oEAAA,wBAAqE;IAEzE5G,EADE,CAAAG,YAAA,EAAY,EACG;IAEfH,EADF,CAAAC,cAAA,0BAA0E,qBAE/B;IAAvCD,EAAA,CAAAkE,gBAAA,4BAAA2C,6FAAAzC,MAAA;MAAApE,EAAA,CAAAe,aAAA,CAAAyF,IAAA;MAAA,MAAAvF,MAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAAAlB,EAAA,CAAAsE,kBAAA,CAAArD,MAAA,CAAAsD,eAAA,CAAAvB,OAAA,EAAAoB,MAAA,MAAAnD,MAAA,CAAAsD,eAAA,CAAAvB,OAAA,GAAAoB,MAAA;MAAA,OAAApE,EAAA,CAAAqB,WAAA,CAAA+C,MAAA;IAAA,EAAsC;IACtCpE,EAAA,CAAAoC,UAAA,KAAA0E,oEAAA,wBAA6E;IAGjF9G,EADE,CAAAG,YAAA,EAAY,EACG;IAEfH,EADF,CAAAC,cAAA,0BAA6E,iBAEhC;IAAzCD,EAAA,CAAAkE,gBAAA,2BAAA6C,wFAAA3C,MAAA;MAAApE,EAAA,CAAAe,aAAA,CAAAyF,IAAA;MAAA,MAAAvF,MAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAAAlB,EAAA,CAAAsE,kBAAA,CAAArD,MAAA,CAAAsD,eAAA,CAAArB,UAAA,EAAAkB,MAAA,MAAAnD,MAAA,CAAAsD,eAAA,CAAArB,UAAA,GAAAkB,MAAA;MAAA,OAAApE,EAAA,CAAAqB,WAAA,CAAA+C,MAAA;IAAA,EAAwC;IAC5CpE,EAFE,CAAAG,YAAA,EAC2C,EAC5B;IAEfH,EADF,CAAAC,cAAA,0BAAwE,iBAEhC;IAApCD,EAAA,CAAAkE,gBAAA,2BAAA8C,wFAAA5C,MAAA;MAAApE,EAAA,CAAAe,aAAA,CAAAyF,IAAA;MAAA,MAAAvF,MAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAAAlB,EAAA,CAAAsE,kBAAA,CAAArD,MAAA,CAAAsD,eAAA,CAAAY,KAAA,EAAAf,MAAA,MAAAnD,MAAA,CAAAsD,eAAA,CAAAY,KAAA,GAAAf,MAAA;MAAA,OAAApE,EAAA,CAAAqB,WAAA,CAAA+C,MAAA;IAAA,EAAmC;IACvCpE,EAFE,CAAAG,YAAA,EACsC,EACvB;IAEfH,EADF,CAAAC,cAAA,0BAA+E,uBACsB;IAA1DD,EAAA,CAAAkE,gBAAA,2BAAA+C,8FAAA7C,MAAA;MAAApE,EAAA,CAAAe,aAAA,CAAAyF,IAAA;MAAA,MAAAvF,MAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAAAlB,EAAA,CAAAsE,kBAAA,CAAArD,MAAA,CAAAsD,eAAA,CAAAc,OAAA,EAAAjB,MAAA,MAAAnD,MAAA,CAAAsD,eAAA,CAAAc,OAAA,GAAAjB,MAAA;MAAA,OAAApE,EAAA,CAAAqB,WAAA,CAAA+C,MAAA;IAAA,EAAqC;IAC5EpE,EAAA,CAAAE,MAAA,oDACF;IACFF,EADE,CAAAG,YAAA,EAAc,EACC;IAEfH,EADF,CAAAC,cAAA,0BAA6E,oBAEV;IAA/DD,EAAA,CAAAkE,gBAAA,2BAAAgD,2FAAA9C,MAAA;MAAApE,EAAA,CAAAe,aAAA,CAAAyF,IAAA;MAAA,MAAAvF,MAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAAAlB,EAAA,CAAAsE,kBAAA,CAAArD,MAAA,CAAAsD,eAAA,CAAAgB,OAAA,EAAAnB,MAAA,MAAAnD,MAAA,CAAAsD,eAAA,CAAAgB,OAAA,GAAAnB,MAAA;MAAA,OAAApE,EAAA,CAAAqB,WAAA,CAAA+C,MAAA;IAAA,EAAqC;IAKjDpE,EAL2E,CAAAG,YAAA,EAAW,EAC7D,EACb,EACF,EACF,EACO;IAITH,EAHN,CAAAC,cAAA,sBAAgB,cACG,eACiB,kBACmC;IAA5BD,EAAA,CAAAa,UAAA,mBAAAsG,iFAAA;MAAA,MAAAC,OAAA,GAAApH,EAAA,CAAAe,aAAA,CAAAyF,IAAA,EAAAd,SAAA;MAAA,MAAAzE,MAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAAA,OAAAlB,EAAA,CAAAqB,WAAA,CAASJ,MAAA,CAAAoG,YAAA,CAAAD,OAAA,CAAiB;IAAA,EAAC;IAACpH,EAAA,CAAAE,MAAA,oBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAC5EH,EAAA,CAAAC,cAAA,kBAA0D;IAAtBD,EAAA,CAAAa,UAAA,mBAAAyG,iFAAA;MAAA,MAAAF,OAAA,GAAApH,EAAA,CAAAe,aAAA,CAAAyF,IAAA,EAAAd,SAAA;MAAA,OAAA1F,EAAA,CAAAqB,WAAA,CAAS+F,OAAA,CAAAvB,KAAA,EAAW;IAAA,EAAC;IAAC7F,EAAA,CAAAE,MAAA,oBAAE;IAIpEF,EAJoE,CAAAG,YAAA,EAAS,EACjE,EACF,EACS,EACT;;;;IA7DCH,EAAA,CAAAO,SAAA,GAAkB;IAAlBP,EAAA,CAAAI,UAAA,SAAAa,MAAA,CAAA6E,KAAA,UAAkB;IAClB9F,EAAA,CAAAO,SAAA,EAAmB;IAAnBP,EAAA,CAAAI,UAAA,SAAAa,MAAA,CAAA6E,KAAA,WAAmB;IAMJ9F,EAAA,CAAAO,SAAA,GAAgB;IAA6BP,EAA7C,CAAAI,UAAA,qCAAgB,4BAA4B,oBAAoB;IAE5EJ,EAAA,CAAAO,SAAA,EAA0C;IAA1CP,EAAA,CAAA+F,gBAAA,YAAA9E,MAAA,CAAAsD,eAAA,CAAA/B,YAAA,CAA0C;IAE9BxC,EAAA,CAAAO,SAAA,EAAgB;IAA2BP,EAA3C,CAAAI,UAAA,qCAAgB,0BAA0B,qBAAqB;IAE3EJ,EAAA,CAAAO,SAAA,EAAwC;IAAxCP,EAAA,CAAA+F,gBAAA,YAAA9E,MAAA,CAAAsD,eAAA,CAAA9B,UAAA,CAAwC;IAE5BzC,EAAA,CAAAO,SAAA,EAAc;IAAsBP,EAApC,CAAAI,UAAA,yBAAc,qBAAqB,oBAAoB;IAEnEJ,EAAA,CAAAO,SAAA,EAAmC;IAAnCP,EAAA,CAAA+F,gBAAA,YAAA9E,MAAA,CAAAsD,eAAA,CAAAzB,KAAA,CAAmC;IAEvB9C,EAAA,CAAAO,SAAA,EAAc;IAA2BP,EAAzC,CAAAI,UAAA,yBAAc,0BAA0B,oBAAoB;IAExEJ,EAAA,CAAAO,SAAA,EAAyC;IAAzCP,EAAA,CAAA+F,gBAAA,aAAA9E,MAAA,CAAAsD,eAAA,CAAA5B,UAAA,CAAyC;IACP3C,EAAA,CAAAO,SAAA,EAAY;IAAZP,EAAA,CAAAI,UAAA,YAAAa,MAAA,CAAAgF,SAAA,CAAY;IAGlCjG,EAAA,CAAAO,SAAA,EAAc;IAAwBP,EAAtC,CAAAI,UAAA,yBAAc,uBAAuB,oBAAoB;IAErEJ,EAAA,CAAAO,SAAA,EAAsC;IAAtCP,EAAA,CAAA+F,gBAAA,aAAA9E,MAAA,CAAAsD,eAAA,CAAAvB,OAAA,CAAsC;IACFhD,EAAA,CAAAO,SAAA,EAAgB;IAAhBP,EAAA,CAAAI,UAAA,YAAAa,MAAA,CAAAiF,aAAA,CAAgB;IAIxClG,EAAA,CAAAO,SAAA,EAAc;IAA2BP,EAAzC,CAAAI,UAAA,yBAAc,0BAA0B,oBAAoB;IAExEJ,EAAA,CAAAO,SAAA,EAAwC;IAAxCP,EAAA,CAAA+F,gBAAA,YAAA9E,MAAA,CAAAsD,eAAA,CAAArB,UAAA,CAAwC;IAE5BlD,EAAA,CAAAO,SAAA,EAAc;IAAsBP,EAApC,CAAAI,UAAA,yBAAc,qBAAqB,oBAAoB;IAEnEJ,EAAA,CAAAO,SAAA,EAAmC;IAAnCP,EAAA,CAAA+F,gBAAA,YAAA9E,MAAA,CAAAsD,eAAA,CAAAY,KAAA,CAAmC;IAEvBnF,EAAA,CAAAO,SAAA,EAAkB;IAAwBP,EAA1C,CAAAI,UAAA,iDAAkB,uBAAuB,qBAAqB;IACnCJ,EAAA,CAAAO,SAAA,EAAqC;IAArCP,EAAA,CAAA+F,gBAAA,YAAA9E,MAAA,CAAAsD,eAAA,CAAAc,OAAA,CAAqC;IAIhErF,EAAA,CAAAO,SAAA,GAAgB;IAAwBP,EAAxC,CAAAI,UAAA,qCAAgB,uBAAuB,qBAAqB;IAExEJ,EAAA,CAAAO,SAAA,EAAqC;IAArCP,EAAA,CAAA+F,gBAAA,YAAA9E,MAAA,CAAAsD,eAAA,CAAAgB,OAAA,CAAqC;;;ADxPrD,OAAM,MAAOgC,8BAA+B,SAAQlI,aAAa;EAC/DmI,YACUC,MAAmB,EACnBC,UAAsB,EACtBC,aAA8B,EAC9BC,OAAuB,EACvBC,KAAuB,EACvBC,gBAAkC,EAClCC,kBAAsC,EACtCC,OAAsB,EACtBC,MAAc,EACdC,UAAsB;IAE9B,KAAK,CAACT,MAAM,CAAC;IAXL,KAAAA,MAAM,GAANA,MAAM;IACN,KAAAC,UAAU,GAAVA,UAAU;IACV,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,kBAAkB,GAAlBA,kBAAkB;IAClB,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,UAAU,GAAVA,UAAU;IAOpB;IACA,KAAAC,yBAAyB,GAAG,EAA8D;IAC1F,KAAAC,qBAAqB,GAA8B,EAAE;IACrD;IACA,KAAApC,aAAa,GAA8B,EAAE;IAC7C,KAAAqC,eAAe,GAAqB,EAAE;IACtC,KAAA9D,eAAe,GAAgD;MAAE5B,UAAU,EAAE;IAAE,CAAE;IAEjF,KAAAuD,aAAa,GAAG,CACd;MAAEvF,KAAK,EAAE,CAAC;MAAEC,KAAK,EAAE;IAAI,CAAE,EACzB;MAAED,KAAK,EAAE,CAAC;MAAEC,KAAK,EAAE;IAAI,CAAE,CAC1B;IACD,KAAAqF,SAAS,GAAG,IAAI,CAACyB,UAAU,CAACY,cAAc,CAACvI,aAAa,CAAC;IACzD,KAAA+F,KAAK,GAAG,KAAK;IACb,KAAAyC,gBAAgB,GAAG,CAAC;IACpB,KAAAC,UAAU,GAAG,CAAC,CAAC,CAAC;IAnBd,IAAI,CAACC,gBAAgB,EAAE;IACvB,IAAI,CAACC,oBAAoB,EAAE;IAC3B,IAAI,CAACC,OAAO,EAAE;EAChB;EAkBSC,QAAQA,CAAA,GAAW;EAC5B;EACAF,oBAAoBA,CAAA;IAClB,IAAI,CAACP,yBAAyB,CAAC3D,YAAY,GAAG,CAAC,CAAC;IAChD,IAAI,CAAC2D,yBAAyB,CAACnF,OAAO,GAAG,CAAC,CAAC;IAC3C,IAAI,CAACmF,yBAAyB,CAAC9C,OAAO,GAAG,IAAI;IAC7C,IAAI,CAAC8C,yBAAyB,CAAC3F,YAAY,GAAG,EAAE;IAChD,IAAI,CAAC2F,yBAAyB,CAAC1F,UAAU,GAAG,EAAE;IAC9C;IACA,IAAI,CAAC0F,yBAAyB,CAACxF,UAAU,GAAG,IAAI,CAACsD,SAAS,CAAC4C,GAAG,CAACC,IAAI,IAAIA,IAAI,CAACnI,KAAK,CAAC;EACpF;EAEA;EACAoI,WAAWA,CAAA;IACT,IAAI,CAACL,oBAAoB,EAAE;IAC3B,IAAI,CAACC,OAAO,EAAE;EAChB;EAEAjG,YAAYA,CAACsG,MAA4C;IACvD,IAAI,CAACA,MAAM,EAAE;MACX,OAAO,EAAE;IACX;IAEA,IAAI,CAACC,KAAK,CAACC,OAAO,CAACF,MAAM,CAAC,EAAE;MAC1BA,MAAM,GAAG,CAACA,MAAM,CAAC;IACnB;IAEA,IAAIG,MAAM,GAAa,EAAE;IACzBH,MAAM,CAACI,OAAO,CAACC,KAAK,IAAG;MACrB,IAAIC,KAAK,GAAG,IAAI,CAACrD,SAAS,CAACsD,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC7I,KAAK,IAAI0I,KAAK,CAAC;MACtD,IAAIC,KAAK,EAAE;QACTH,MAAM,CAACM,IAAI,CAACH,KAAK,CAAC1I,KAAK,CAAC;MAC1B;IACF,CAAC,CAAC;IACF,OAAOuI,MAAM,CAACO,IAAI,CAAC,IAAI,CAAC;EAC1B;EAAEC,UAAUA,CAAA;IACV,IAAI,CAAC9B,KAAK,CAAC+B,KAAK,EAAE;IAClB,IAAI,CAAC/B,KAAK,CAACgC,QAAQ,CAAC,QAAQ,EAAE,IAAI,CAACtF,eAAe,CAACC,YAAY,CAAC;IAChE,IAAI,CAACqD,KAAK,CAACgC,QAAQ,CAAC,MAAM,EAAE,IAAI,CAACtF,eAAe,CAAC/B,YAAY,CAAC;IAC9D,IAAI,CAACqF,KAAK,CAACgC,QAAQ,CAAC,OAAO,EAAE,IAAI,CAACtF,eAAe,CAAC5B,UAAU,CAAC;IAC7D,IAAI,CAACkF,KAAK,CAACgC,QAAQ,CAAC,MAAM,EAAE,IAAI,CAACtF,eAAe,CAACzB,KAAK,CAAC;IACvD,IAAI,CAAC+E,KAAK,CAACgC,QAAQ,CAAC,MAAM,EAAE,IAAI,CAACtF,eAAe,CAACvB,OAAO,CAAC;IACzD,IAAI,CAAC6E,KAAK,CAACgC,QAAQ,CAAC,MAAM,EAAE,IAAI,CAACtF,eAAe,CAACrB,UAAU,CAAC;IAC5D,IAAI,CAAC2E,KAAK,CAACgC,QAAQ,CAAC,MAAM,EAAE,IAAI,CAACtF,eAAe,CAACY,KAAK,CAAC;IACvD;IACA,IAAI,IAAI,CAACZ,eAAe,CAAC9B,UAAU,IAAI,IAAI,CAAC8B,eAAe,CAAC9B,UAAU,CAACqH,MAAM,GAAG,EAAE,EAAE;MAClF,IAAI,CAACjC,KAAK,CAACkC,aAAa,CAACN,IAAI,CAAC,iBAAiB,CAAC;IAClD;IAEA;IACA,IAAI,IAAI,CAAClF,eAAe,CAACgB,OAAO,IAAI,IAAI,CAAChB,eAAe,CAACgB,OAAO,CAACuE,MAAM,GAAG,GAAG,EAAE;MAC7E,IAAI,CAACjC,KAAK,CAACkC,aAAa,CAACN,IAAI,CAAC,kBAAkB,CAAC;IACnD;EACF;EAEAnI,GAAGA,CAAC0I,MAAwB;IAC1B,IAAI,CAAClE,KAAK,GAAG,IAAI;IACjB,IAAI,CAACvB,eAAe,GAAG;MAAE5B,UAAU,EAAE,EAAE;MAAE0C,OAAO,EAAE;IAAK,CAAE;IACzD,IAAI,CAACd,eAAe,CAACvB,OAAO,GAAG,CAAC;IAChC,IAAI,CAACuB,eAAe,CAACrB,UAAU,GAAG,CAAC;IACnC,IAAI,IAAI,CAACqF,gBAAgB,IAAI,CAAC,EAAE;MAC9B,IAAI,CAAChE,eAAe,CAACC,YAAY,GAAG,IAAI,CAAC+D,gBAAgB;IAC3D;IACA,IAAI,CAACE,gBAAgB,EAAE;IACvB,IAAI,CAACd,aAAa,CAACsC,IAAI,CAACD,MAAM,CAAC;EACjC;EAEMhI,MAAMA,CAACkI,IAAoB,EAAEF,MAAwB;IAAA,IAAAG,KAAA;IAAA,OAAAC,iBAAA;MACzDD,KAAI,CAAC/B,qBAAqB,CAACiC,cAAc,GAAGH,IAAI,CAACG,cAAe;MAChEF,KAAI,CAACrE,KAAK,GAAG,KAAK;MAClB,IAAI;QACF,MAAMqE,KAAI,CAACG,OAAO,EAAE;QACpBH,KAAI,CAACxC,aAAa,CAACsC,IAAI,CAACD,MAAM,CAAC;MACjC,CAAC,CAAC,OAAOO,KAAK,EAAE;QACdC,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAEF,KAAK,CAAC;MACnD;IAAC;EACH;EAEA5E,IAAIA,CAAC+E,GAAQ;IACX,IAAI,CAACf,UAAU,EAAE;IACjB,IAAI,IAAI,CAAC9B,KAAK,CAACkC,aAAa,CAACD,MAAM,GAAG,CAAC,EAAE;MACvC,IAAI,CAAClC,OAAO,CAAC+C,aAAa,CAAC,IAAI,CAAC9C,KAAK,CAACkC,aAAa,CAAC;MACpD;IACF;IAEA,IAAI,CAAChC,kBAAkB,CAAC6C,+BAA+B,CAAC;MACtDC,IAAI,EAAE,IAAI,CAACtG;KACZ,CAAC,CAACuG,SAAS,CAACC,GAAG,IAAG;MACjB,IAAIA,GAAG,CAACC,UAAU,KAAK,CAAC,EAAE;QACxB,IAAI,CAACpD,OAAO,CAACqD,aAAa,CAAC,MAAM,CAAC;QAClC,IAAI,CAACtC,OAAO,EAAE;MAChB,CAAC,MAAM;QACL,IAAI,CAACf,OAAO,CAACsD,YAAY,CAACH,GAAG,CAACI,OAAQ,CAAC;MACzC;IACF,CAAC,CAAC;IACFT,GAAG,CAAC7E,KAAK,EAAE;EACb;EAEA1D,QAAQA,CAAC+H,IAAoB;IAC3B,IAAI,CAAC3F,eAAe,CAAC8F,cAAc,GAAGH,IAAI,CAACG,cAAe;IAC1D,IAAI,CAACvE,KAAK,GAAG,KAAK;IAClB,IAAIsF,MAAM,CAACC,OAAO,CAAC,SAAS,CAAC,EAAE;MAC7B,IAAI,CAACC,MAAM,EAAE;IACf,CAAC,MAAM;MACL;IACF;EACF;EAEAA,MAAMA,CAAA;IACJ,IAAI,CAACvD,kBAAkB,CAACwD,iCAAiC,CAAC;MACxDV,IAAI,EAAE;QACJR,cAAc,EAAE,IAAI,CAAC9F,eAAe,CAAC8F;;KAExC,CAAC,CAACS,SAAS,CAACC,GAAG,IAAG;MACjB,IAAI,CAACnD,OAAO,CAACqD,aAAa,CAAC,MAAM,CAAC;MAClC,IAAI,CAACtC,OAAO,EAAE;IAChB,CAAC,CAAC;EACJ;EAEAF,gBAAgBA,CAAA;IACd,IAAI,CAACX,gBAAgB,CAAC0D,qCAAqC,CAAC;MAAEX,IAAI,EAAE;IAAE,CAAE,CAAC,CACtEY,IAAI,CAACnM,kBAAkB,CAAC,IAAI,CAAC4I,UAAU,CAAC,CAAC,CAAC4C,SAAS,CAACC,GAAG,IAAG;MACzD,IAAI,CAAC/E,aAAa,GAAG+E,GAAG,CAACW,OAAQ;IACnC,CAAC,CAAC;EACN;EAAG/C,OAAOA,CAAA;IACR,IAAI,CAACR,yBAAyB,CAACwD,QAAQ,GAAG,IAAI,CAACC,QAAQ;IACvD,IAAI,CAACzD,yBAAyB,CAAC0D,SAAS,GAAG,IAAI,CAACC,SAAS;IAEzD;IACA,IAAI,IAAI,CAACtD,UAAU,KAAK,CAAC,EAAE;MACzB,IAAI,CAACL,yBAAyB,CAAC3D,YAAY,GAAG,CAAC;IACjD,CAAC,MAAM;MACL;MACA,IAAI,IAAI,CAAC2D,yBAAyB,CAAC3D,YAAY,IAAI,IAAI,CAAC2D,yBAAyB,CAAC3D,YAAY,IAAI,CAAC,EAAE;QACnG,IAAI,CAAC+D,gBAAgB,GAAG,IAAI,CAACJ,yBAAyB,CAAC3D,YAAY;MACrE;IACF;IAEA,IAAI,CAACuD,kBAAkB,CAACgE,8BAA8B,CAAC;MAAElB,IAAI,EAAE,IAAI,CAAC1C;IAAyB,CAAE,CAAC,CAC7FsD,IAAI,EAAE,CACNX,SAAS,CAACC,GAAG,IAAG;MACf,IAAIA,GAAG,CAACC,UAAU,IAAI,CAAC,EAAE;QACvB,IAAID,GAAG,CAACW,OAAO,EAAE;UACf,IAAI,CAACrD,eAAe,GAAG0C,GAAG,CAACW,OAAO;UAClC,IAAI,CAACM,YAAY,GAAGjB,GAAG,CAACkB,UAAW;QACrC;MACF;IACF,CAAC,CAAC;EACN;EAAC3B,OAAOA,CAAA;IACN,IAAI,CAACvC,kBAAkB,CAACmE,8BAA8B,CAAC;MAAErB,IAAI,EAAE,IAAI,CAACzC;IAAqB,CAAE,CAAC,CACzFqD,IAAI,EAAE,CACNX,SAAS,CAACC,GAAG,IAAG;MACf,IAAIA,GAAG,CAACC,UAAU,IAAI,CAAC,EAAE;QACvB,IAAID,GAAG,CAACW,OAAO,EAAE;UACf,IAAI,CAACnH,eAAe,GAAG;YAAE5B,UAAU,EAAE,EAAE;YAAE0C,OAAO,EAAE;UAAK,CAAE;UACzD,IAAI,CAACd,eAAe,CAACC,YAAY,GAAGuG,GAAG,CAACW,OAAO,CAAClH,YAAY;UAC5D,IAAI,CAACD,eAAe,CAAC9B,UAAU,GAAGsI,GAAG,CAACW,OAAO,CAACjJ,UAAU;UACxD,IAAI,CAAC8B,eAAe,CAAC5B,UAAU,GAAGoI,GAAG,CAACW,OAAO,CAAC/I,UAAU,GAAIsG,KAAK,CAACC,OAAO,CAAC6B,GAAG,CAACW,OAAO,CAAC/I,UAAU,CAAC,GAAGoI,GAAG,CAACW,OAAO,CAAC/I,UAAU,GAAG,CAACoI,GAAG,CAACW,OAAO,CAAC/I,UAAU,CAAC,GAAI,EAAE;UAC3J,IAAI,CAAC4B,eAAe,CAACgB,OAAO,GAAGwF,GAAG,CAACW,OAAO,CAACnG,OAAO;UAClD,IAAI,CAAChB,eAAe,CAAC/B,YAAY,GAAGuI,GAAG,CAACW,OAAO,CAAClJ,YAAY;UAC5D,IAAI,CAAC+B,eAAe,CAAC8F,cAAc,GAAGU,GAAG,CAACW,OAAO,CAACrB,cAAc;UAChE,IAAI,CAAC9F,eAAe,CAACzB,KAAK,GAAGiI,GAAG,CAACW,OAAO,CAAC5I,KAAK;UAC9C,IAAI,CAACyB,eAAe,CAACvB,OAAO,GAAG+H,GAAG,CAACW,OAAO,CAAC1I,OAAO;UAClD,IAAI,CAACuB,eAAe,CAACrB,UAAU,GAAG6H,GAAG,CAACW,OAAO,CAACxI,UAAU;UACxD,IAAI,CAACqB,eAAe,CAACY,KAAK,GAAG4F,GAAG,CAACW,OAAO,CAACvG,KAAK;UAC9C;UACA,IAAI,CAACZ,eAAe,CAACc,OAAO,GAAI0F,GAAG,CAACW,OAAe,CAACrG,OAAO,IAAI,KAAK;QACtE;MACF;IACF,CAAC,CAAC;EACN;EAEA8G,iBAAiBA,CAACxL,KAAa,EAAEyL,OAAY;IAC3C5B,OAAO,CAACC,GAAG,CAAC2B,OAAO,CAAC;IAEpB,IAAIA,OAAO,EAAE;MACX,IAAI,CAAC,IAAI,CAAC7H,eAAe,CAAC5B,UAAU,EAAE0J,QAAQ,CAAC1L,KAAK,CAAC,EAAE;QACrD,IAAI,CAAC4D,eAAe,CAAC5B,UAAU,EAAE8G,IAAI,CAAC9I,KAAK,CAAC;MAC9C;MACA6J,OAAO,CAACC,GAAG,CAAC,IAAI,CAAClG,eAAe,CAAC5B,UAAU,CAAC;IAC9C,CAAC,MAAM;MACL,IAAI,CAAC4B,eAAe,CAAC5B,UAAU,GAAG,IAAI,CAAC4B,eAAe,CAAC5B,UAAU,EAAE2J,MAAM,CAACC,CAAC,IAAIA,CAAC,KAAK5L,KAAK,CAAC;IAC7F;EACF;EAEAsC,cAAcA,CAACiH,IAAS;IACtB,OAAOA,IAAI,CAAC7E,OAAO,GAAG,GAAG,GAAG,GAAG;EACjC;EAEA;EACAmH,WAAWA,CAACC,KAAU;IACpB,IAAI,CAACjE,UAAU,GAAGiE,KAAK,CAACC,QAAQ,IAAI,CAAC;IACrC,IAAI,CAAC/D,OAAO,EAAE,CAAC,CAAC;EAClB;EAEA;EACAhH,WAAWA,CAACqI,MAAwB;IAClC,IAAI,CAAClE,KAAK,GAAG,IAAI;IACjB,IAAI,CAACvB,eAAe,GAAG;MAAE5B,UAAU,EAAE,EAAE;MAAE0C,OAAO,EAAE;IAAK,CAAE;IACzD,IAAI,CAACd,eAAe,CAACvB,OAAO,GAAG,CAAC;IAChC,IAAI,CAACuB,eAAe,CAACrB,UAAU,GAAG,CAAC;IACnC;IACA,IAAI,CAACqB,eAAe,CAACC,YAAY,GAAGmI,SAAS;IAC7C,IAAI,CAAChF,aAAa,CAACsC,IAAI,CAACD,MAAM,CAAC;EACjC;EAEA;EACMxG,cAAcA,CAAC0G,IAAoB,EAAEF,MAAwB;IAAA,IAAA4C,MAAA;IAAA,OAAAxC,iBAAA;MACjEwC,MAAI,CAACxE,qBAAqB,CAACiC,cAAc,GAAGH,IAAI,CAACG,cAAe;MAChEuC,MAAI,CAAC9G,KAAK,GAAG,KAAK;MAClB,IAAI;QACF,MAAM8G,MAAI,CAACtC,OAAO,EAAE;QACpBsC,MAAI,CAACjF,aAAa,CAACsC,IAAI,CAACD,MAAM,CAAC;MACjC,CAAC,CAAC,OAAOO,KAAK,EAAE;QACdC,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAEF,KAAK,CAAC;MACnD;IAAC;EACH;EAEA;EACAlD,YAAYA,CAACqD,GAAQ;IACnB;IACA,IAAI,CAAC7C,KAAK,CAAC+B,KAAK,EAAE;IAClB,IAAI,CAAC/B,KAAK,CAACgC,QAAQ,CAAC,MAAM,EAAE,IAAI,CAACtF,eAAe,CAAC/B,YAAY,CAAC;IAC9D,IAAI,CAACqF,KAAK,CAACgC,QAAQ,CAAC,OAAO,EAAE,IAAI,CAACtF,eAAe,CAAC5B,UAAU,CAAC;IAC7D,IAAI,CAACkF,KAAK,CAACgC,QAAQ,CAAC,MAAM,EAAE,IAAI,CAACtF,eAAe,CAACzB,KAAK,CAAC;IACvD,IAAI,CAAC+E,KAAK,CAACgC,QAAQ,CAAC,MAAM,EAAE,IAAI,CAACtF,eAAe,CAACvB,OAAO,CAAC;IACzD,IAAI,CAAC6E,KAAK,CAACgC,QAAQ,CAAC,MAAM,EAAE,IAAI,CAACtF,eAAe,CAACrB,UAAU,CAAC;IAC5D,IAAI,CAAC2E,KAAK,CAACgC,QAAQ,CAAC,MAAM,EAAE,IAAI,CAACtF,eAAe,CAACY,KAAK,CAAC;IAEvD;IACA,IAAI,IAAI,CAACZ,eAAe,CAAC9B,UAAU,IAAI,IAAI,CAAC8B,eAAe,CAAC9B,UAAU,CAACqH,MAAM,GAAG,EAAE,EAAE;MAClF,IAAI,CAACjC,KAAK,CAACkC,aAAa,CAACN,IAAI,CAAC,iBAAiB,CAAC;IAClD;IAEA;IACA,IAAI,IAAI,CAAClF,eAAe,CAACgB,OAAO,IAAI,IAAI,CAAChB,eAAe,CAACgB,OAAO,CAACuE,MAAM,GAAG,GAAG,EAAE;MAC7E,IAAI,CAACjC,KAAK,CAACkC,aAAa,CAACN,IAAI,CAAC,kBAAkB,CAAC;IACnD;IAEA,IAAI,IAAI,CAAC5B,KAAK,CAACkC,aAAa,CAACD,MAAM,GAAG,CAAC,EAAE;MACvC,IAAI,CAAClC,OAAO,CAAC+C,aAAa,CAAC,IAAI,CAAC9C,KAAK,CAACkC,aAAa,CAAC;MACpD;IACF;IAEA;IACA,MAAM8C,YAAY,GAAG;MAAE,GAAG,IAAI,CAACtI;IAAe,CAAE;IAChDsI,YAAY,CAACrI,YAAY,GAAGmI,SAAS;IAErC,IAAI,CAAC5E,kBAAkB,CAAC6C,+BAA+B,CAAC;MACtDC,IAAI,EAAEgC;KACP,CAAC,CAAC/B,SAAS,CAACC,GAAG,IAAG;MACjB,IAAIA,GAAG,CAACC,UAAU,KAAK,CAAC,EAAE;QACxB,IAAI,CAACpD,OAAO,CAACqD,aAAa,CAAC,MAAM,CAAC;QAClC,IAAI,CAACtC,OAAO,EAAE;MAChB,CAAC,MAAM;QACL,IAAI,CAACf,OAAO,CAACsD,YAAY,CAACH,GAAG,CAACI,OAAQ,CAAC;MACzC;IACF,CAAC,CAAC;IACFT,GAAG,CAAC7E,KAAK,EAAE;EACb;;;uCAtSW0B,8BAA8B,EAAAvH,EAAA,CAAA8M,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAhN,EAAA,CAAA8M,iBAAA,CAAAG,EAAA,CAAAC,UAAA,GAAAlN,EAAA,CAAA8M,iBAAA,CAAAK,EAAA,CAAAC,eAAA,GAAApN,EAAA,CAAA8M,iBAAA,CAAAO,EAAA,CAAAC,cAAA,GAAAtN,EAAA,CAAA8M,iBAAA,CAAAS,EAAA,CAAAC,gBAAA,GAAAxN,EAAA,CAAA8M,iBAAA,CAAAW,EAAA,CAAAC,gBAAA,GAAA1N,EAAA,CAAA8M,iBAAA,CAAAW,EAAA,CAAAE,kBAAA,GAAA3N,EAAA,CAAA8M,iBAAA,CAAAc,EAAA,CAAAC,aAAA,GAAA7N,EAAA,CAAA8M,iBAAA,CAAAgB,EAAA,CAAAC,MAAA,GAAA/N,EAAA,CAAA8M,iBAAA,CAAA9M,EAAA,CAAAgO,UAAA;IAAA;EAAA;;;YAA9BzG,8BAA8B;MAAA0G,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAAnO,EAAA,CAAAoO,0BAAA,EAAApO,EAAA,CAAAqO,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,wCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;UCzCzC3O,EADF,CAAAC,cAAA,iBAA0B,qBACR;UACdD,EAAA,CAAAuB,SAAA,qBAAiC;UACnCvB,EAAA,CAAAG,YAAA,EAAiB;UAOTH,EAJR,CAAAC,cAAA,sBAAoC,aACd,aACD,aACyB,eACI;UAAAD,EAAA,CAAAE,MAAA,mBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACpDH,EAAA,CAAAC,cAAA,mBAA8E;UAAnED,EAAA,CAAAkE,gBAAA,2BAAA2K,2EAAAzK,MAAA;YAAApE,EAAA,CAAAe,aAAA,CAAA+N,GAAA;YAAA9O,EAAA,CAAAsE,kBAAA,CAAAsK,GAAA,CAAAzG,yBAAA,CAAA3D,YAAA,EAAAJ,MAAA,MAAAwK,GAAA,CAAAzG,yBAAA,CAAA3D,YAAA,GAAAJ,MAAA;YAAA,OAAApE,EAAA,CAAAqB,WAAA,CAAA+C,MAAA;UAAA,EAAoD;UAC7DpE,EAAA,CAAAC,cAAA,oBAAwB;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAY;UACtCH,EAAA,CAAAoC,UAAA,KAAA2M,oDAAA,wBAAiE;UAIrE/O,EADE,CAAAG,YAAA,EAAY,EACR;UAEJH,EADF,CAAAC,cAAA,cAAwC,iBACM;UAAAD,EAAA,CAAAE,MAAA,gCAAI;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACxDH,EAAA,CAAAC,cAAA,iBACuD;UAArDD,EAAA,CAAAkE,gBAAA,2BAAA8K,wEAAA5K,MAAA;YAAApE,EAAA,CAAAe,aAAA,CAAA+N,GAAA;YAAA9O,EAAA,CAAAsE,kBAAA,CAAAsK,GAAA,CAAAzG,yBAAA,CAAA3F,YAAA,EAAA4B,MAAA,MAAAwK,GAAA,CAAAzG,yBAAA,CAAA3F,YAAA,GAAA4B,MAAA;YAAA,OAAApE,EAAA,CAAAqB,WAAA,CAAA+C,MAAA;UAAA,EAAoD;UACxDpE,EAFE,CAAAG,YAAA,EACuD,EACnD;UAEJH,EADF,CAAAC,cAAA,cAAwC,iBACI;UAAAD,EAAA,CAAAE,MAAA,gCAAI;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACtDH,EAAA,CAAAC,cAAA,iBACqD;UAAnDD,EAAA,CAAAkE,gBAAA,2BAAA+K,wEAAA7K,MAAA;YAAApE,EAAA,CAAAe,aAAA,CAAA+N,GAAA;YAAA9O,EAAA,CAAAsE,kBAAA,CAAAsK,GAAA,CAAAzG,yBAAA,CAAA1F,UAAA,EAAA2B,MAAA,MAAAwK,GAAA,CAAAzG,yBAAA,CAAA1F,UAAA,GAAA2B,MAAA;YAAA,OAAApE,EAAA,CAAAqB,WAAA,CAAA+C,MAAA;UAAA,EAAkD;UAExDpE,EAHI,CAAAG,YAAA,EACqD,EACjD,EACF;UAGFH,EAFJ,CAAAC,cAAA,cAAiB,cACyB,iBACI;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACpDH,EAAA,CAAAC,cAAA,qBAAqF;UAA1ED,EAAA,CAAAkE,gBAAA,2BAAAgL,4EAAA9K,MAAA;YAAApE,EAAA,CAAAe,aAAA,CAAA+N,GAAA;YAAA9O,EAAA,CAAAsE,kBAAA,CAAAsK,GAAA,CAAAzG,yBAAA,CAAAxF,UAAA,EAAAyB,MAAA,MAAAwK,GAAA,CAAAzG,yBAAA,CAAAxF,UAAA,GAAAyB,MAAA;YAAA,OAAApE,EAAA,CAAAqB,WAAA,CAAA+C,MAAA;UAAA,EAAkD;UAC3DpE,EAAA,CAAAoC,UAAA,KAAA+M,oDAAA,wBAA+D;UAInEnP,EADE,CAAAG,YAAA,EAAY,EACR;UAEJH,EADF,CAAAC,cAAA,cAAwC,iBACC;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACjDH,EAAA,CAAAC,cAAA,oBAAyE;UAA9DD,EAAA,CAAAkE,gBAAA,2BAAAkL,4EAAAhL,MAAA;YAAApE,EAAA,CAAAe,aAAA,CAAA+N,GAAA;YAAA9O,EAAA,CAAAsE,kBAAA,CAAAsK,GAAA,CAAAzG,yBAAA,CAAAnF,OAAA,EAAAoB,MAAA,MAAAwK,GAAA,CAAAzG,yBAAA,CAAAnF,OAAA,GAAAoB,MAAA;YAAA,OAAApE,EAAA,CAAAqB,WAAA,CAAA+C,MAAA;UAAA,EAA+C;UACxDpE,EAAA,CAAAC,cAAA,oBAAwB;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAY;UACtCH,EAAA,CAAAC,cAAA,oBAAuB;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAY;UACrCH,EAAA,CAAAC,cAAA,oBAAuB;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAE7BF,EAF6B,CAAAG,YAAA,EAAY,EAC3B,EACR;UAEJH,EADF,CAAAC,cAAA,cAAwC,iBACC;UAAAD,EAAA,CAAAE,MAAA,4CAAM;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACrDH,EAAA,CAAAC,cAAA,oBAAyE;UAA9DD,EAAA,CAAAkE,gBAAA,2BAAAmL,4EAAAjL,MAAA;YAAApE,EAAA,CAAAe,aAAA,CAAA+N,GAAA;YAAA9O,EAAA,CAAAsE,kBAAA,CAAAsK,GAAA,CAAAzG,yBAAA,CAAA9C,OAAA,EAAAjB,MAAA,MAAAwK,GAAA,CAAAzG,yBAAA,CAAA9C,OAAA,GAAAjB,MAAA;YAAA,OAAApE,EAAA,CAAAqB,WAAA,CAAA+C,MAAA;UAAA,EAA+C;UACxDpE,EAAA,CAAAC,cAAA,oBAA0B;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAY;UACxCH,EAAA,CAAAC,cAAA,oBAA0B;UAAAD,EAAA,CAAAE,MAAA,cAAC;UAAAF,EAAA,CAAAG,YAAA,EAAY;UACvCH,EAAA,CAAAC,cAAA,oBAA2B;UAAAD,EAAA,CAAAE,MAAA,cAAC;UAGlCF,EAHkC,CAAAG,YAAA,EAAY,EAC9B,EACR,EACF;UACNH,EAAA,CAAAC,cAAA,cAAiB;UACfD,EAAA,CAAAuB,SAAA,eAA4B;UAE1BvB,EADF,CAAAC,cAAA,eAAmD,kBACc;UAAxBD,EAAA,CAAAa,UAAA,mBAAAyO,iEAAA;YAAAtP,EAAA,CAAAe,aAAA,CAAA+N,GAAA;YAAA,OAAA9O,EAAA,CAAAqB,WAAA,CAASuN,GAAA,CAAA7F,WAAA,EAAa;UAAA,EAAC;UAAC/I,EAAA,CAAAuB,SAAA,aAAgC;UAAAvB,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAC1GH,EAAA,CAAAC,cAAA,kBAAsD;UAApBD,EAAA,CAAAa,UAAA,mBAAA0O,iEAAA;YAAAvP,EAAA,CAAAe,aAAA,CAAA+N,GAAA;YAAA,OAAA9O,EAAA,CAAAqB,WAAA,CAASuN,GAAA,CAAAjG,OAAA,EAAS;UAAA,EAAC;UAAC3I,EAAA,CAAAuB,SAAA,aAAkC;UAAAvB,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAGnGH,EAFA,CAAAoC,UAAA,KAAAoN,iDAAA,qBAAgG,KAAAC,iDAAA,qBAGzD;UAI/CzP,EAHM,CAAAG,YAAA,EAAM,EACF,EACF,EACO;UAIbH,EADF,CAAAC,cAAA,uBAAoC,qBACW;UAAlCD,EAAA,CAAAa,UAAA,uBAAA6O,wEAAAtL,MAAA;YAAApE,EAAA,CAAAe,aAAA,CAAA+N,GAAA;YAAA,OAAA9O,EAAA,CAAAqB,WAAA,CAAauN,GAAA,CAAApC,WAAA,CAAApI,MAAA,CAAmB;UAAA,EAAC;UAS5BpE,EARd,CAAAC,cAAA,kBAAsB,eACF,eAES,eACO,iBACmE,aACtF,cAC4D,cACjC;UAAAD,EAAA,CAAAE,MAAA,gCAAI;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACvCH,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAE,MAAA,gCAAI;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACvCH,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAE,MAAA,gCAAI;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACvCH,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACrCH,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACrCH,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACrCH,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAE,MAAA,4CAAM;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACzCH,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACrCH,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAE,MAAA,gCAAI;UAEtCF,EAFsC,CAAAG,YAAA,EAAK,EACpC,EACC;UACRH,EAAA,CAAAC,cAAA,aAAO;UACLD,EAAA,CAAAoC,UAAA,KAAAuN,6CAAA,mBAAuE;UAkB7E3P,EAFI,CAAAG,YAAA,EAAQ,EACF,EACJ;UACNH,EAAA,CAAAC,cAAA,0BAC2B;UADqBD,EAAA,CAAAkE,gBAAA,wBAAA0L,8EAAAxL,MAAA;YAAApE,EAAA,CAAAe,aAAA,CAAA+N,GAAA;YAAA9O,EAAA,CAAAsE,kBAAA,CAAAsK,GAAA,CAAA9C,SAAA,EAAA1H,MAAA,MAAAwK,GAAA,CAAA9C,SAAA,GAAA1H,MAAA;YAAA,OAAApE,EAAA,CAAAqB,WAAA,CAAA+C,MAAA;UAAA,EAAoB;UAClEpE,EAAA,CAAAa,UAAA,wBAAA+O,8EAAA;YAAA5P,EAAA,CAAAe,aAAA,CAAA+N,GAAA;YAAA,OAAA9O,EAAA,CAAAqB,WAAA,CAAcuN,GAAA,CAAAjG,OAAA,EAAS;UAAA,EAAC;UAIhC3I,EAHM,CAAAG,YAAA,EAAiB,EACb,EACF,EACC;UAUKH,EARd,CAAAC,cAAA,kBAAsB,eACF,eAES,eACO,iBACmE,aACtF,cAC4D,cACjC;UAAAD,EAAA,CAAAE,MAAA,gCAAI;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACvCH,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAE,MAAA,gCAAI;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACvCH,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAE,MAAA,qBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACrCH,EAAA,CAAAC,cAAA,eAA8B;UAAAD,EAAA,CAAAE,MAAA,qBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACrCH,EAAA,CAAAC,cAAA,eAA8B;UAAAD,EAAA,CAAAE,MAAA,qBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACrCH,EAAA,CAAAC,cAAA,eAA8B;UAAAD,EAAA,CAAAE,MAAA,6CAAM;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACzCH,EAAA,CAAAC,cAAA,eAA8B;UAAAD,EAAA,CAAAE,MAAA,qBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACrCH,EAAA,CAAAC,cAAA,eAA8B;UAAAD,EAAA,CAAAE,MAAA,iCAAI;UAEtCF,EAFsC,CAAAG,YAAA,EAAK,EACpC,EACC;UACRH,EAAA,CAAAC,cAAA,cAAO;UACLD,EAAA,CAAAoC,UAAA,MAAAyN,8CAAA,mBAAuE;UAiB7E7P,EAFI,CAAAG,YAAA,EAAQ,EACF,EACJ;UACNH,EAAA,CAAAC,cAAA,2BAC2B;UADqBD,EAAA,CAAAkE,gBAAA,wBAAA4L,+EAAA1L,MAAA;YAAApE,EAAA,CAAAe,aAAA,CAAA+N,GAAA;YAAA9O,EAAA,CAAAsE,kBAAA,CAAAsK,GAAA,CAAA9C,SAAA,EAAA1H,MAAA,MAAAwK,GAAA,CAAA9C,SAAA,GAAA1H,MAAA;YAAA,OAAApE,EAAA,CAAAqB,WAAA,CAAA+C,MAAA;UAAA,EAAoB;UAClEpE,EAAA,CAAAa,UAAA,wBAAAiP,+EAAA;YAAA9P,EAAA,CAAAe,aAAA,CAAA+N,GAAA;YAAA,OAAA9O,EAAA,CAAAqB,WAAA,CAAcuN,GAAA,CAAAjG,OAAA,EAAS;UAAA,EAAC;UAOtC3I,EANY,CAAAG,YAAA,EAAiB,EACb,EACF,EACC,EACC,EACC,EACP;UA6EVH,EA1EA,CAAAoC,UAAA,MAAA2N,uDAAA,kCAAA/P,EAAA,CAAAgQ,sBAAA,CAAkD,MAAAC,uDAAA,kCAAAjQ,EAAA,CAAAgQ,sBAAA,CA0EQ;;;UArOrChQ,EAAA,CAAAO,SAAA,GAAoD;UAApDP,EAAA,CAAA+F,gBAAA,YAAA6I,GAAA,CAAAzG,yBAAA,CAAA3D,YAAA,CAAoD;UAClDxE,EAAA,CAAAO,SAAA,EAAY;UAAZP,EAAA,CAAAI,UAAA,aAAY;UACKJ,EAAA,CAAAO,SAAA,GAAgB;UAAhBP,EAAA,CAAAI,UAAA,YAAAwO,GAAA,CAAA5I,aAAA,CAAgB;UAQ5ChG,EAAA,CAAAO,SAAA,GAAoD;UAApDP,EAAA,CAAA+F,gBAAA,YAAA6I,GAAA,CAAAzG,yBAAA,CAAA3F,YAAA,CAAoD;UAKpDxC,EAAA,CAAAO,SAAA,GAAkD;UAAlDP,EAAA,CAAA+F,gBAAA,YAAA6I,GAAA,CAAAzG,yBAAA,CAAA1F,UAAA,CAAkD;UAMzCzC,EAAA,CAAAO,SAAA,GAAkD;UAAlDP,EAAA,CAAA+F,gBAAA,YAAA6I,GAAA,CAAAzG,yBAAA,CAAAxF,UAAA,CAAkD;UAC/B3C,EAAA,CAAAO,SAAA,EAAY;UAAZP,EAAA,CAAAI,UAAA,YAAAwO,GAAA,CAAA3I,SAAA,CAAY;UAO/BjG,EAAA,CAAAO,SAAA,GAA+C;UAA/CP,EAAA,CAAA+F,gBAAA,YAAA6I,GAAA,CAAAzG,yBAAA,CAAAnF,OAAA,CAA+C;UAC7ChD,EAAA,CAAAO,SAAA,EAAY;UAAZP,EAAA,CAAAI,UAAA,aAAY;UACZJ,EAAA,CAAAO,SAAA,GAAW;UAAXP,EAAA,CAAAI,UAAA,YAAW;UACXJ,EAAA,CAAAO,SAAA,GAAW;UAAXP,EAAA,CAAAI,UAAA,YAAW;UAKbJ,EAAA,CAAAO,SAAA,GAA+C;UAA/CP,EAAA,CAAA+F,gBAAA,YAAA6I,GAAA,CAAAzG,yBAAA,CAAA9C,OAAA,CAA+C;UAC7CrF,EAAA,CAAAO,SAAA,EAAc;UAAdP,EAAA,CAAAI,UAAA,eAAc;UACdJ,EAAA,CAAAO,SAAA,GAAc;UAAdP,EAAA,CAAAI,UAAA,eAAc;UACdJ,EAAA,CAAAO,SAAA,GAAe;UAAfP,EAAA,CAAAI,UAAA,gBAAe;UASgCJ,EAAA,CAAAO,SAAA,IAAkC;UAAlCP,EAAA,CAAAI,UAAA,SAAAwO,GAAA,CAAAsB,QAAA,IAAAtB,GAAA,CAAApG,UAAA,OAAkC;UAG3FxI,EAAA,CAAAO,SAAA,EAAkC;UAAlCP,EAAA,CAAAI,UAAA,SAAAwO,GAAA,CAAAsB,QAAA,IAAAtB,GAAA,CAAApG,UAAA,OAAkC;UA6BRxI,EAAA,CAAAO,SAAA,IAAoB;UAApBP,EAAA,CAAAI,UAAA,YAAAwO,GAAA,CAAAvG,eAAA,CAAoB;UAmB/BrI,EAAA,CAAAO,SAAA,EAA+B;UAA/BP,EAAA,CAAAI,UAAA,mBAAAwO,GAAA,CAAA5C,YAAA,CAA+B;UAAChM,EAAA,CAAA+F,gBAAA,SAAA6I,GAAA,CAAA9C,SAAA,CAAoB;UAAC9L,EAAA,CAAAI,UAAA,aAAAwO,GAAA,CAAAhD,QAAA,CAAqB;UA0B/D5L,EAAA,CAAAO,SAAA,IAAoB;UAApBP,EAAA,CAAAI,UAAA,YAAAwO,GAAA,CAAAvG,eAAA,CAAoB;UAkB/BrI,EAAA,CAAAO,SAAA,EAA+B;UAA/BP,EAAA,CAAAI,UAAA,mBAAAwO,GAAA,CAAA5C,YAAA,CAA+B;UAAChM,EAAA,CAAA+F,gBAAA,SAAA6I,GAAA,CAAA9C,SAAA,CAAoB;UAAC9L,EAAA,CAAAI,UAAA,aAAAwO,GAAA,CAAAhD,QAAA,CAAqB;;;qBDnIlG7M,YAAY,EAAAoO,EAAA,CAAAgD,eAAA,EAAAhD,EAAA,CAAAiD,mBAAA,EAAAjD,EAAA,CAAAkD,qBAAA,EAAAlD,EAAA,CAAAmD,qBAAA,EACZ/Q,mBAAmB,EACnBN,aAAa,EAAAkO,EAAA,CAAAoD,gBAAA,EACb/Q,WAAW,EAAAgR,EAAA,CAAAC,oBAAA,EAAAD,EAAA,CAAAE,mBAAA,EAAAF,EAAA,CAAAG,eAAA,EAAAH,EAAA,CAAAI,kBAAA,EAAAJ,EAAA,CAAAK,OAAA,EACX1R,cAAc,EAAAgO,EAAA,CAAA2D,iBAAA,EAAA3D,EAAA,CAAA4D,iBAAA,EACd7R,cAAc,EACdQ,IAAI,EACJD,KAAK,EACLE,mBAAmB,EACnBC,UAAU,EACVZ,gBAAgB,EAAAmO,EAAA,CAAA6D,mBAAA,EAChBnR,kBAAkB,EAClBC,oBAAoB,EACpBV,cAAc,EAAA+N,EAAA,CAAA8D,iBAAA,EAAA9D,EAAA,CAAA+D,cAAA;MAAAC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}