{"ast": null, "code": ";\n(function (root, factory, undef) {\n  if (typeof exports === \"object\") {\n    // CommonJS\n    module.exports = exports = factory(require(\"./core\"), require(\"./sha256\"), require(\"./hmac\"));\n  } else if (typeof define === \"function\" && define.amd) {\n    // AMD\n    define([\"./core\", \"./sha256\", \"./hmac\"], factory);\n  } else {\n    // Global (browser)\n    factory(root.CryptoJS);\n  }\n})(this, function (CryptoJS) {\n  (function () {\n    // Shortcuts\n    var C = CryptoJS;\n    var C_lib = C.lib;\n    var Base = C_lib.Base;\n    var WordArray = C_lib.WordArray;\n    var C_algo = C.algo;\n    var SHA256 = C_algo.SHA256;\n    var HMAC = C_algo.HMAC;\n\n    /**\n     * Password-Based Key Derivation Function 2 algorithm.\n     */\n    var PBKDF2 = C_algo.PBKDF2 = Base.extend({\n      /**\n       * Configuration options.\n       *\n       * @property {number} keySize The key size in words to generate. Default: 4 (128 bits)\n       * @property {Hasher} hasher The hasher to use. Default: SHA256\n       * @property {number} iterations The number of iterations to perform. Default: 250000\n       */\n      cfg: Base.extend({\n        keySize: 128 / 32,\n        hasher: SHA256,\n        iterations: 250000\n      }),\n      /**\n       * Initializes a newly created key derivation function.\n       *\n       * @param {Object} cfg (Optional) The configuration options to use for the derivation.\n       *\n       * @example\n       *\n       *     var kdf = CryptoJS.algo.PBKDF2.create();\n       *     var kdf = CryptoJS.algo.PBKDF2.create({ keySize: 8 });\n       *     var kdf = CryptoJS.algo.PBKDF2.create({ keySize: 8, iterations: 1000 });\n       */\n      init: function (cfg) {\n        this.cfg = this.cfg.extend(cfg);\n      },\n      /**\n       * Computes the Password-Based Key Derivation Function 2.\n       *\n       * @param {WordArray|string} password The password.\n       * @param {WordArray|string} salt A salt.\n       *\n       * @return {WordArray} The derived key.\n       *\n       * @example\n       *\n       *     var key = kdf.compute(password, salt);\n       */\n      compute: function (password, salt) {\n        // Shortcut\n        var cfg = this.cfg;\n\n        // Init HMAC\n        var hmac = HMAC.create(cfg.hasher, password);\n\n        // Initial values\n        var derivedKey = WordArray.create();\n        var blockIndex = WordArray.create([0x00000001]);\n\n        // Shortcuts\n        var derivedKeyWords = derivedKey.words;\n        var blockIndexWords = blockIndex.words;\n        var keySize = cfg.keySize;\n        var iterations = cfg.iterations;\n\n        // Generate key\n        while (derivedKeyWords.length < keySize) {\n          var block = hmac.update(salt).finalize(blockIndex);\n          hmac.reset();\n\n          // Shortcuts\n          var blockWords = block.words;\n          var blockWordsLength = blockWords.length;\n\n          // Iterations\n          var intermediate = block;\n          for (var i = 1; i < iterations; i++) {\n            intermediate = hmac.finalize(intermediate);\n            hmac.reset();\n\n            // Shortcut\n            var intermediateWords = intermediate.words;\n\n            // XOR intermediate with block\n            for (var j = 0; j < blockWordsLength; j++) {\n              blockWords[j] ^= intermediateWords[j];\n            }\n          }\n          derivedKey.concat(block);\n          blockIndexWords[0]++;\n        }\n        derivedKey.sigBytes = keySize * 4;\n        return derivedKey;\n      }\n    });\n\n    /**\n     * Computes the Password-Based Key Derivation Function 2.\n     *\n     * @param {WordArray|string} password The password.\n     * @param {WordArray|string} salt A salt.\n     * @param {Object} cfg (Optional) The configuration options to use for this computation.\n     *\n     * @return {WordArray} The derived key.\n     *\n     * @static\n     *\n     * @example\n     *\n     *     var key = CryptoJS.PBKDF2(password, salt);\n     *     var key = CryptoJS.PBKDF2(password, salt, { keySize: 8 });\n     *     var key = CryptoJS.PBKDF2(password, salt, { keySize: 8, iterations: 1000 });\n     */\n    C.PBKDF2 = function (password, salt, cfg) {\n      return PBKDF2.create(cfg).compute(password, salt);\n    };\n  })();\n  return CryptoJS.PBKDF2;\n});", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}