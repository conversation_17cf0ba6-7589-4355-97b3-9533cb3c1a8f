{"ast": null, "code": ";\n(function (root, factory, undef) {\n  if (typeof exports === \"object\") {\n    // CommonJS\n    module.exports = exports = factory(require(\"./core\"), require(\"./enc-base64\"), require(\"./md5\"), require(\"./evpkdf\"), require(\"./cipher-core\"));\n  } else if (typeof define === \"function\" && define.amd) {\n    // AMD\n    define([\"./core\", \"./enc-base64\", \"./md5\", \"./evpkdf\", \"./cipher-core\"], factory);\n  } else {\n    // Global (browser)\n    factory(root.CryptoJS);\n  }\n})(this, function (CryptoJS) {\n  (function () {\n    // Shortcuts\n    var C = CryptoJS;\n    var C_lib = C.lib;\n    var BlockCipher = C_lib.BlockCipher;\n    var C_algo = C.algo;\n\n    // Lookup tables\n    var SBOX = [];\n    var INV_SBOX = [];\n    var SUB_MIX_0 = [];\n    var SUB_MIX_1 = [];\n    var SUB_MIX_2 = [];\n    var SUB_MIX_3 = [];\n    var INV_SUB_MIX_0 = [];\n    var INV_SUB_MIX_1 = [];\n    var INV_SUB_MIX_2 = [];\n    var INV_SUB_MIX_3 = [];\n\n    // Compute lookup tables\n    (function () {\n      // Compute double table\n      var d = [];\n      for (var i = 0; i < 256; i++) {\n        if (i < 128) {\n          d[i] = i << 1;\n        } else {\n          d[i] = i << 1 ^ 0x11b;\n        }\n      }\n\n      // Walk GF(2^8)\n      var x = 0;\n      var xi = 0;\n      for (var i = 0; i < 256; i++) {\n        // Compute sbox\n        var sx = xi ^ xi << 1 ^ xi << 2 ^ xi << 3 ^ xi << 4;\n        sx = sx >>> 8 ^ sx & 0xff ^ 0x63;\n        SBOX[x] = sx;\n        INV_SBOX[sx] = x;\n\n        // Compute multiplication\n        var x2 = d[x];\n        var x4 = d[x2];\n        var x8 = d[x4];\n\n        // Compute sub bytes, mix columns tables\n        var t = d[sx] * 0x101 ^ sx * 0x1010100;\n        SUB_MIX_0[x] = t << 24 | t >>> 8;\n        SUB_MIX_1[x] = t << 16 | t >>> 16;\n        SUB_MIX_2[x] = t << 8 | t >>> 24;\n        SUB_MIX_3[x] = t;\n\n        // Compute inv sub bytes, inv mix columns tables\n        var t = x8 * 0x1010101 ^ x4 * 0x10001 ^ x2 * 0x101 ^ x * 0x1010100;\n        INV_SUB_MIX_0[sx] = t << 24 | t >>> 8;\n        INV_SUB_MIX_1[sx] = t << 16 | t >>> 16;\n        INV_SUB_MIX_2[sx] = t << 8 | t >>> 24;\n        INV_SUB_MIX_3[sx] = t;\n\n        // Compute next counter\n        if (!x) {\n          x = xi = 1;\n        } else {\n          x = x2 ^ d[d[d[x8 ^ x2]]];\n          xi ^= d[d[xi]];\n        }\n      }\n    })();\n\n    // Precomputed Rcon lookup\n    var RCON = [0x00, 0x01, 0x02, 0x04, 0x08, 0x10, 0x20, 0x40, 0x80, 0x1b, 0x36];\n\n    /**\n     * AES block cipher algorithm.\n     */\n    var AES = C_algo.AES = BlockCipher.extend({\n      _doReset: function () {\n        var t;\n\n        // Skip reset of nRounds has been set before and key did not change\n        if (this._nRounds && this._keyPriorReset === this._key) {\n          return;\n        }\n\n        // Shortcuts\n        var key = this._keyPriorReset = this._key;\n        var keyWords = key.words;\n        var keySize = key.sigBytes / 4;\n\n        // Compute number of rounds\n        var nRounds = this._nRounds = keySize + 6;\n\n        // Compute number of key schedule rows\n        var ksRows = (nRounds + 1) * 4;\n\n        // Compute key schedule\n        var keySchedule = this._keySchedule = [];\n        for (var ksRow = 0; ksRow < ksRows; ksRow++) {\n          if (ksRow < keySize) {\n            keySchedule[ksRow] = keyWords[ksRow];\n          } else {\n            t = keySchedule[ksRow - 1];\n            if (!(ksRow % keySize)) {\n              // Rot word\n              t = t << 8 | t >>> 24;\n\n              // Sub word\n              t = SBOX[t >>> 24] << 24 | SBOX[t >>> 16 & 0xff] << 16 | SBOX[t >>> 8 & 0xff] << 8 | SBOX[t & 0xff];\n\n              // Mix Rcon\n              t ^= RCON[ksRow / keySize | 0] << 24;\n            } else if (keySize > 6 && ksRow % keySize == 4) {\n              // Sub word\n              t = SBOX[t >>> 24] << 24 | SBOX[t >>> 16 & 0xff] << 16 | SBOX[t >>> 8 & 0xff] << 8 | SBOX[t & 0xff];\n            }\n            keySchedule[ksRow] = keySchedule[ksRow - keySize] ^ t;\n          }\n        }\n\n        // Compute inv key schedule\n        var invKeySchedule = this._invKeySchedule = [];\n        for (var invKsRow = 0; invKsRow < ksRows; invKsRow++) {\n          var ksRow = ksRows - invKsRow;\n          if (invKsRow % 4) {\n            var t = keySchedule[ksRow];\n          } else {\n            var t = keySchedule[ksRow - 4];\n          }\n          if (invKsRow < 4 || ksRow <= 4) {\n            invKeySchedule[invKsRow] = t;\n          } else {\n            invKeySchedule[invKsRow] = INV_SUB_MIX_0[SBOX[t >>> 24]] ^ INV_SUB_MIX_1[SBOX[t >>> 16 & 0xff]] ^ INV_SUB_MIX_2[SBOX[t >>> 8 & 0xff]] ^ INV_SUB_MIX_3[SBOX[t & 0xff]];\n          }\n        }\n      },\n      encryptBlock: function (M, offset) {\n        this._doCryptBlock(M, offset, this._keySchedule, SUB_MIX_0, SUB_MIX_1, SUB_MIX_2, SUB_MIX_3, SBOX);\n      },\n      decryptBlock: function (M, offset) {\n        // Swap 2nd and 4th rows\n        var t = M[offset + 1];\n        M[offset + 1] = M[offset + 3];\n        M[offset + 3] = t;\n        this._doCryptBlock(M, offset, this._invKeySchedule, INV_SUB_MIX_0, INV_SUB_MIX_1, INV_SUB_MIX_2, INV_SUB_MIX_3, INV_SBOX);\n\n        // Inv swap 2nd and 4th rows\n        var t = M[offset + 1];\n        M[offset + 1] = M[offset + 3];\n        M[offset + 3] = t;\n      },\n      _doCryptBlock: function (M, offset, keySchedule, SUB_MIX_0, SUB_MIX_1, SUB_MIX_2, SUB_MIX_3, SBOX) {\n        // Shortcut\n        var nRounds = this._nRounds;\n\n        // Get input, add round key\n        var s0 = M[offset] ^ keySchedule[0];\n        var s1 = M[offset + 1] ^ keySchedule[1];\n        var s2 = M[offset + 2] ^ keySchedule[2];\n        var s3 = M[offset + 3] ^ keySchedule[3];\n\n        // Key schedule row counter\n        var ksRow = 4;\n\n        // Rounds\n        for (var round = 1; round < nRounds; round++) {\n          // Shift rows, sub bytes, mix columns, add round key\n          var t0 = SUB_MIX_0[s0 >>> 24] ^ SUB_MIX_1[s1 >>> 16 & 0xff] ^ SUB_MIX_2[s2 >>> 8 & 0xff] ^ SUB_MIX_3[s3 & 0xff] ^ keySchedule[ksRow++];\n          var t1 = SUB_MIX_0[s1 >>> 24] ^ SUB_MIX_1[s2 >>> 16 & 0xff] ^ SUB_MIX_2[s3 >>> 8 & 0xff] ^ SUB_MIX_3[s0 & 0xff] ^ keySchedule[ksRow++];\n          var t2 = SUB_MIX_0[s2 >>> 24] ^ SUB_MIX_1[s3 >>> 16 & 0xff] ^ SUB_MIX_2[s0 >>> 8 & 0xff] ^ SUB_MIX_3[s1 & 0xff] ^ keySchedule[ksRow++];\n          var t3 = SUB_MIX_0[s3 >>> 24] ^ SUB_MIX_1[s0 >>> 16 & 0xff] ^ SUB_MIX_2[s1 >>> 8 & 0xff] ^ SUB_MIX_3[s2 & 0xff] ^ keySchedule[ksRow++];\n\n          // Update state\n          s0 = t0;\n          s1 = t1;\n          s2 = t2;\n          s3 = t3;\n        }\n\n        // Shift rows, sub bytes, add round key\n        var t0 = (SBOX[s0 >>> 24] << 24 | SBOX[s1 >>> 16 & 0xff] << 16 | SBOX[s2 >>> 8 & 0xff] << 8 | SBOX[s3 & 0xff]) ^ keySchedule[ksRow++];\n        var t1 = (SBOX[s1 >>> 24] << 24 | SBOX[s2 >>> 16 & 0xff] << 16 | SBOX[s3 >>> 8 & 0xff] << 8 | SBOX[s0 & 0xff]) ^ keySchedule[ksRow++];\n        var t2 = (SBOX[s2 >>> 24] << 24 | SBOX[s3 >>> 16 & 0xff] << 16 | SBOX[s0 >>> 8 & 0xff] << 8 | SBOX[s1 & 0xff]) ^ keySchedule[ksRow++];\n        var t3 = (SBOX[s3 >>> 24] << 24 | SBOX[s0 >>> 16 & 0xff] << 16 | SBOX[s1 >>> 8 & 0xff] << 8 | SBOX[s2 & 0xff]) ^ keySchedule[ksRow++];\n\n        // Set output\n        M[offset] = t0;\n        M[offset + 1] = t1;\n        M[offset + 2] = t2;\n        M[offset + 3] = t3;\n      },\n      keySize: 256 / 32\n    });\n\n    /**\n     * Shortcut functions to the cipher's object interface.\n     *\n     * @example\n     *\n     *     var ciphertext = CryptoJS.AES.encrypt(message, key, cfg);\n     *     var plaintext  = CryptoJS.AES.decrypt(ciphertext, key, cfg);\n     */\n    C.AES = BlockCipher._createHelper(AES);\n  })();\n  return CryptoJS.AES;\n});", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}