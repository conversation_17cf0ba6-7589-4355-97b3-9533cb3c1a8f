{"ast": null, "code": "import { BaseComponent } from '../base/baseComponent';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../shared.observable\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"src/app/shared/helper/allowHelper\";\nexport let BreadcrumbComponent = /*#__PURE__*/(() => {\n  class BreadcrumbComponent extends BaseComponent {\n    constructor(share, router, allow) {\n      super(allow);\n      this.share = share;\n      this.router = router;\n      this.allow = allow;\n    }\n    ngOnInit() {\n      this.share.SharedMenu.subscribe(res => {\n        if (res.Menu !== undefined) {\n          const subMenu = this.getSubMenu(res, this.router.url);\n          if (subMenu !== undefined) {\n            const masterMenu = res.Menu.find(x => x.CId === subMenu.CParentId);\n            this.masterMenu = masterMenu.CName;\n            this.subMenu = subMenu.CName;\n          }\n        }\n      });\n    }\n    static {\n      this.ɵfac = function BreadcrumbComponent_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || BreadcrumbComponent)(i0.ɵɵdirectiveInject(i1.SharedObservable), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i3.AllowHelper));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: BreadcrumbComponent,\n        selectors: [[\"ngx-breadcrumb\"]],\n        standalone: true,\n        features: [i0.ɵɵInheritDefinitionFeature, i0.ɵɵStandaloneFeature],\n        decls: 1,\n        vars: 2,\n        template: function BreadcrumbComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵtext(0);\n          }\n          if (rf & 2) {\n            i0.ɵɵtextInterpolate2(\"\", ctx.masterMenu, \" / \", ctx.subMenu, \"\\n\");\n          }\n        }\n      });\n    }\n  }\n  return BreadcrumbComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}