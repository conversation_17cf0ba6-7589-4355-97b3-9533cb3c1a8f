{"ast": null, "code": "import { ServiceBase } from './service-base';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport class FileService extends ServiceBase {\n  constructor(http) {\n    super(http);\n    this.baseUrl = `${this.apiBaseUrl}/File`;\n  }\n  getFile(relativePath, fileName) {\n    const url = `${this.baseUrl}/GetFile`;\n    const params = {\n      relativePath: relativePath,\n      fileName: fileName\n    };\n    return this.http.get(url, {\n      params: params,\n      responseType: 'blob'\n    });\n  }\n  static {\n    this.ɵfac = function FileService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || FileService)(i0.ɵɵinject(i1.HttpClient));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: FileService,\n      factory: FileService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["ServiceBase", "FileService", "constructor", "http", "baseUrl", "apiBaseUrl", "getFile", "relativePath", "fileName", "url", "params", "get", "responseType", "i0", "ɵɵinject", "i1", "HttpClient", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\services\\api\\services\\File.service.ts"], "sourcesContent": ["import { HttpClient } from '@angular/common/http';\r\nimport { Injectable } from '@angular/core';\r\nimport { Observable } from 'rxjs';\r\nimport { ServiceBase } from './service-base';\r\n\r\n\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class FileService extends ServiceBase {\r\n\r\n  protected baseUrl = `${this.apiBaseUrl}/File`;\r\n  constructor(\r\n    http: HttpClient\r\n  ) {\r\n    super(http);\r\n  }\r\n\r\n  getFile(relativePath: string, fileName: string): Observable<Blob> {\r\n    const url = `${this.baseUrl}/GetFile`;\r\n    const params = {\r\n      relativePath: relativePath,\r\n      fileName: fileName\r\n    };\r\n\r\n    return this.http.get(url, {\r\n      params: params,\r\n      responseType: 'blob'\r\n    });\r\n  }\r\n}\r\n"], "mappings": "AAGA,SAASA,WAAW,QAAQ,gBAAgB;;;AAM5C,OAAM,MAAOC,WAAY,SAAQD,WAAW;EAG1CE,YACEC,IAAgB;IAEhB,KAAK,CAACA,IAAI,CAAC;IAJH,KAAAC,OAAO,GAAG,GAAG,IAAI,CAACC,UAAU,OAAO;EAK7C;EAEAC,OAAOA,CAACC,YAAoB,EAAEC,QAAgB;IAC5C,MAAMC,GAAG,GAAG,GAAG,IAAI,CAACL,OAAO,UAAU;IACrC,MAAMM,MAAM,GAAG;MACbH,YAAY,EAAEA,YAAY;MAC1BC,QAAQ,EAAEA;KACX;IAED,OAAO,IAAI,CAACL,IAAI,CAACQ,GAAG,CAACF,GAAG,EAAE;MACxBC,MAAM,EAAEA,MAAM;MACdE,YAAY,EAAE;KACf,CAAC;EACJ;;;uCApBWX,WAAW,EAAAY,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;IAAA;EAAA;;;aAAXf,WAAW;MAAAgB,OAAA,EAAXhB,WAAW,CAAAiB,IAAA;MAAAC,UAAA,EAFV;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}