{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { NbDatepickerModule } from '@nebular/theme';\nimport { NbDateFnsDateModule } from '@nebular/date-fns';\nimport * as moment from 'moment';\nimport { SharedModule } from 'src/app/pages/components/shared.module';\nimport { BaseComponent } from 'src/app/pages/components/base/baseComponent';\nimport { EEvent } from 'src/app/shared/services/event.service';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/shared/helper/allowHelper\";\nimport * as i2 from \"@nebular/theme\";\nimport * as i3 from \"src/app/shared/services/message.service\";\nimport * as i4 from \"src/app/shared/helper/validationHelper\";\nimport * as i5 from \"src/services/api/services\";\nimport * as i6 from \"@angular/router\";\nimport * as i7 from \"@angular/common\";\nimport * as i8 from \"src/app/shared/services/event.service\";\nimport * as i9 from \"@angular/forms\";\nimport * as i10 from \"../../../components/breadcrumb/breadcrumb.component\";\nfunction EditSettingTimePeriodComponent_ng_container_38_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"th\", 22)(2, \"nb-checkbox\", 23);\n    i0.ɵɵlistener(\"checkedChange\", function EditSettingTimePeriodComponent_ng_container_38_Template_nb_checkbox_checkedChange_2_listener($event) {\n      const idx_r3 = i0.ɵɵrestoreView(_r2).index;\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.enableAllAtIndex($event, idx_r3));\n    });\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const item_r5 = ctx.$implicit;\n    const idx_r3 = ctx.index;\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"checked\", ctx_r3.isCheckAllColumnChecked(idx_r3));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r5.CHouseHold);\n  }\n}\nfunction EditSettingTimePeriodComponent_tr_40_td_5_nb_checkbox_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-checkbox\", 23);\n    i0.ɵɵtwoWayListener(\"checkedChange\", function EditSettingTimePeriodComponent_tr_40_td_5_nb_checkbox_1_Template_nb_checkbox_checkedChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r8);\n      const itm_r9 = i0.ɵɵnextContext().$implicit;\n      i0.ɵɵtwoWayBindingSet(itm_r9.isChecked, $event) || (itm_r9.isChecked = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementStart(1, \"span\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const itm_r9 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵtwoWayProperty(\"checked\", itm_r9.isChecked);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\" \", itm_r9.CHouseHold, \"-\", itm_r9.CFloor, \"F \");\n  }\n}\nfunction EditSettingTimePeriodComponent_tr_40_td_5_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \" \\u672A\\u8A2D\\u5B9A \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction EditSettingTimePeriodComponent_tr_40_td_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\");\n    i0.ɵɵtemplate(1, EditSettingTimePeriodComponent_tr_40_td_5_nb_checkbox_1_Template, 3, 3, \"nb-checkbox\", 24)(2, EditSettingTimePeriodComponent_tr_40_td_5_span_2_Template, 2, 0, \"span\", 25);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const itm_r9 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", itm_r9.CHouseId);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !itm_r9.CHouseId);\n  }\n}\nfunction EditSettingTimePeriodComponent_tr_40_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\")(2, \"nb-checkbox\", 23);\n    i0.ɵɵlistener(\"checkedChange\", function EditSettingTimePeriodComponent_tr_40_Template_nb_checkbox_checkedChange_2_listener($event) {\n      const item_r7 = i0.ɵɵrestoreView(_r6).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.enableAllRow($event, item_r7));\n    });\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(5, EditSettingTimePeriodComponent_tr_40_td_5_Template, 3, 2, \"td\", 18);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r7 = ctx.$implicit;\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"checked\", ctx_r3.isCheckAllRowChecked(item_r7));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", item_r7[0].CFloor, \"F \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", item_r7);\n  }\n}\nexport let EditSettingTimePeriodComponent = /*#__PURE__*/(() => {\n  class EditSettingTimePeriodComponent extends BaseComponent {\n    constructor(_allow, dialogService, message, valid, _houseService, _buildCaseService, route, _location, _eventService) {\n      super(_allow);\n      this._allow = _allow;\n      this.dialogService = dialogService;\n      this.message = message;\n      this.valid = valid;\n      this._houseService = _houseService;\n      this._buildCaseService = _buildCaseService;\n      this.route = route;\n      this._location = _location;\n      this._eventService = _eventService;\n      this.buildCaseOptions = [{\n        label: '全部',\n        value: ''\n      }];\n      this.isStatus = true;\n      this.isHouseList = false;\n      this.buildCaseId = this.route.snapshot.paramMap.get('id');\n      this.selectedHouseChangeDate = {\n        CChangeStartDate: '',\n        CChangeEndDate: '',\n        CFloor: undefined,\n        CHouseHold: '',\n        CHouseId: undefined\n      };\n    }\n    ngOnInit() {\n      this.searchQuery = {\n        CBuildCaseSelected: this.buildCaseId,\n        CChangeStartDate: undefined,\n        CChangeEndDate: undefined\n      };\n      this.getHouseChangeDate();\n    }\n    openModel(ref, item) {\n      if (item.CHouseId) {\n        console.log({\n          item\n        });\n        this.selectedHouseChangeDate = {\n          ...item,\n          CChangeStartDate: item.CChangeStartDate ? new Date(item.CChangeStartDate) : undefined,\n          CChangeEndDate: item.CChangeEndDate ? new Date(item.CChangeEndDate) : undefined\n        };\n        this.dialogService.open(ref);\n      }\n    }\n    formatDate(CChangeDate) {\n      if (CChangeDate) {\n        return moment(CChangeDate).format('YYYY-MM-DDTHH:mm:ss');\n      }\n      return '';\n    }\n    onSubmit() {\n      const bodyReq = this.convertData();\n      this.validation(bodyReq);\n      if (this.valid.errorMessages.length > 0) {\n        this.message.showErrorMSGs(this.valid.errorMessages);\n        return;\n      }\n      this._houseService.apiHouseSaveHouseChangeDatePost$Json({\n        body: bodyReq\n      }).subscribe(res => {\n        if (res.StatusCode == 0) {\n          this.message.showSucessMSG(\"執行成功\");\n          this.goBack();\n        }\n      });\n    }\n    convertData() {\n      return this.convertedHouseArray.flat().filter(item => item.isChecked && item.CHouseId !== null) // Lọc các item theo điều kiện\n      .map(item => ({\n        ...item,\n        CChangeStartDate: this.formatDate(this.searchQuery.CChangeStartDate),\n        CChangeEndDate: this.formatDate(this.searchQuery.CChangeEndDate)\n      }));\n    }\n    convertHouseholdArrayOptimized(arr) {\n      const floorDict = {}; // Khởi tạo dictionary để gom nhóm các phần tử theo CFloor\n      arr.forEach(household => {\n        household.CHouses.forEach(house => {\n          const floor = house.CFloor;\n          if (!floorDict[floor]) {\n            // Nếu CFloor chưa có trong dictionary thì khởi tạo danh sách rỗng\n            floorDict[floor] = [];\n          }\n          floorDict[floor].push({\n            CHouseHold: household.CHouseHold,\n            CHouseId: house.CHouseId,\n            CFloor: house.CFloor,\n            CChangeStartDate: house.CChangeStartDate,\n            CChangeEndDate: house.CChangeEndDate,\n            isChecked: false\n          });\n        });\n      });\n      this.floors.sort((a, b) => b - a);\n      const result = this.floors.map(floor => {\n        return this.households.map(household => {\n          const house = floorDict[floor].find(h => h.CHouseHold === household);\n          return house || {\n            CHouseHold: household,\n            CHouseId: null,\n            CFloor: floor,\n            CChangeStartDate: null,\n            CChangeEndDate: null\n          };\n        });\n      });\n      return result;\n    }\n    getFloorsAndHouseholds(arr) {\n      const floorsSet = new Set();\n      const householdsSet = new Set();\n      arr.forEach(household => {\n        householdsSet.add(household.CHouseHold);\n        household.CHouses.forEach(house => {\n          floorsSet.add(house.CFloor);\n        });\n      });\n      this.floors = Array.from(floorsSet);\n      this.households = Array.from(householdsSet);\n      return {\n        floors: Array.from(floorsSet),\n        households: Array.from(householdsSet)\n      };\n    }\n    getHouseChangeDate() {\n      this._houseService.apiHouseGetHouseChangeDatePost$Json({\n        body: {\n          CBuildCaseId: this.buildCaseId\n        }\n      }).subscribe(res => {\n        if (res.Entries && res.StatusCode == 0) {\n          this.houseChangeDates = res.Entries ? res.Entries : [];\n          if (this.houseChangeDates) {\n            this.getFloorsAndHouseholds(this.houseChangeDates);\n            this.convertedHouseArray = this.convertHouseholdArrayOptimized(this.houseChangeDates);\n            console.log('convertedHouseArray', this.convertedHouseArray);\n          }\n        }\n      });\n    }\n    onClose(ref) {\n      ref.close();\n    }\n    validation(bodyReq) {\n      this.valid.clear();\n      this.valid.required('[客變開始日期]', this.searchQuery.CChangeStartDate);\n      this.valid.required('[客變結束日期]', this.searchQuery.CChangeEndDate);\n      this.valid.checkStartBeforeEnd('[開放日期]', this.searchQuery.CChangeStartDate, this.searchQuery.CChangeEndDate);\n      if (bodyReq && bodyReq.length) {\n        return;\n      } else {\n        this.valid.required('[ 勾選適用戶型 ]', '');\n      }\n    }\n    goBack() {\n      this._eventService.push({\n        action: \"GET_BUILDCASE\" /* EEvent.GET_BUILDCASE */,\n        payload: this.buildCaseId\n      });\n      this._location.back();\n    }\n    isCheckAllRowChecked(row) {\n      return row.every(item => item.isChecked || !item.CHouseId);\n    }\n    isCheckAllColumnChecked(index) {\n      if (index < 0 || index >= this.convertedHouseArray[0].length) {\n        throw new Error(\"Invalid index. Index must be within the bounds of the array.\");\n      }\n      for (const floorData of this.convertedHouseArray) {\n        if (floorData[index].CHouseId) {\n          if (index >= floorData.length || !floorData[index].isChecked) {\n            return false;\n          }\n        }\n      }\n      return true;\n    }\n    enableAllAtIndex(checked, index) {\n      if (index < 0) {\n        throw new Error(\"Invalid index. Index must be a non-negative number.\");\n      }\n      for (const floorData of this.convertedHouseArray) {\n        if (index < floorData.length) {\n          // Check if index is valid for this floor\n          if (floorData[index].CHouseId) {\n            floorData[index].isChecked = checked;\n          }\n        }\n      }\n    }\n    enableAllRow(checked, row) {\n      for (const item of row) {\n        item.isChecked = checked;\n      }\n    }\n    static {\n      this.ɵfac = function EditSettingTimePeriodComponent_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || EditSettingTimePeriodComponent)(i0.ɵɵdirectiveInject(i1.AllowHelper), i0.ɵɵdirectiveInject(i2.NbDialogService), i0.ɵɵdirectiveInject(i3.MessageService), i0.ɵɵdirectiveInject(i4.ValidationHelper), i0.ɵɵdirectiveInject(i5.HouseService), i0.ɵɵdirectiveInject(i5.BuildCaseService), i0.ɵɵdirectiveInject(i6.ActivatedRoute), i0.ɵɵdirectiveInject(i7.Location), i0.ɵɵdirectiveInject(i8.EventService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: EditSettingTimePeriodComponent,\n        selectors: [[\"ngx-edit-setting-time-period\"]],\n        standalone: true,\n        features: [i0.ɵɵProvidersFeature([]), i0.ɵɵInheritDefinitionFeature, i0.ɵɵStandaloneFeature],\n        decls: 47,\n        vars: 6,\n        consts: [[\"StartDate\", \"\"], [\"EndDate\", \"\"], [\"accent\", \"success\"], [1, \"font-bold\", \"text-[#818181]\"], [1, \"d-flex\", \"flex-wrap\"], [1, \"col-md-12\"], [1, \"form-group\", \"d-flex\", \"align-items-center\"], [\"for\", \"StartDate\", 1, \"label\", \"col-3\"], [1, \"text-red-600\"], [1, \"ml-3\"], [\"nbPrefix\", \"\", \"icon\", \"calendar-outline\"], [\"nbInput\", \"\", \"type\", \"text\", \"id\", \"StartDate\", \"placeholder\", \"\\u5E74/\\u6708/\\u65E5\", 1, \"w-full\", \"col-4\", 3, \"ngModelChange\", \"nbDatepicker\", \"ngModel\"], [\"format\", \"yyyy-MM-dd\"], [\"for\", \"EndDate\", 1, \"label\", \"col-1\", \"text-center\"], [\"nbInput\", \"\", \"type\", \"text\", \"id\", \"EndDate\", \"placeholder\", \"\\u5E74/\\u6708/\\u65E5\", 1, \"w-full\", \"col-4\", 3, \"ngModelChange\", \"nbDatepicker\", \"ngModel\"], [\"for\", \"cFloorFrom\", 1, \"label\", \"col-3\"], [1, \"table-responsive\", \"mt-4\"], [1, \"table\", \"table-striped\", \"border\", 2, \"background-color\", \"#f3f3f3\"], [4, \"ngFor\", \"ngForOf\"], [1, \"d-flex\", \"justify-center\", \"w-full\"], [1, \"btn\", \"btn-secondary\", \"btn-sm\", \"mx-2\", 3, \"click\"], [1, \"btn\", \"btn-info\", \"btn-sm\", 3, \"click\"], [1, \"min-w-[100px]\"], [\"status\", \"basic\", 3, \"checkedChange\", \"checked\"], [\"status\", \"basic\", 3, \"checked\", \"checkedChange\", 4, \"ngIf\"], [4, \"ngIf\"]],\n        template: function EditSettingTimePeriodComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            const _r1 = i0.ɵɵgetCurrentView();\n            i0.ɵɵelementStart(0, \"nb-card\", 2)(1, \"nb-card-header\");\n            i0.ɵɵelement(2, \"ngx-breadcrumb\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(3, \"nb-card-body\")(4, \"h1\", 3);\n            i0.ɵɵtext(5, \"\\u60A8\\u53EF\\u8207\\u6B64\\u7BA1\\u7406\\u5404\\u6236\\u5225\\u5167\\u4E4B\\u76F8\\u95DC\\u8CC7\\u8A0A\\uFF0C\\u5305\\u542B\\u57FA\\u672C\\u8CC7\\u6599\\u3001\\u7E73\\u6B3E\\u72C0\\u6CC1\\u3001\\u4E0A\\u50B3\\u5BA2\\u8B8A\\u5716\\u9762\\u3001\\u6AA2\\u8996\\u5BA2\\u8B8A\\u7D50\\u679C\\u7B49\\u7B49\\u3002 \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(6, \"div\", 4)(7, \"div\", 5)(8, \"div\", 6)(9, \"label\", 7);\n            i0.ɵɵtext(10, \"1. \\u958B\\u653E\\u6642\\u9593\\u8D77\\u8A16 \");\n            i0.ɵɵelementStart(11, \"span\", 8);\n            i0.ɵɵtext(12, \"*\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(13, \" : \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(14, \"nb-form-field\", 9);\n            i0.ɵɵelement(15, \"nb-icon\", 10);\n            i0.ɵɵelementStart(16, \"input\", 11);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function EditSettingTimePeriodComponent_Template_input_ngModelChange_16_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.searchQuery.CChangeStartDate, $event) || (ctx.searchQuery.CChangeStartDate = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(17, \"nb-datepicker\", 12, 0);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(19, \"label\", 13);\n            i0.ɵɵtext(20, \" ~ \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(21, \"nb-form-field\");\n            i0.ɵɵelement(22, \"nb-icon\", 10);\n            i0.ɵɵelementStart(23, \"input\", 14);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function EditSettingTimePeriodComponent_Template_input_ngModelChange_23_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.searchQuery.CChangeEndDate, $event) || (ctx.searchQuery.CChangeEndDate = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(24, \"nb-datepicker\", 12, 1);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(26, \"div\", 5)(27, \"div\", 6)(28, \"label\", 15);\n            i0.ɵɵtext(29, \"2. \\u52FE\\u9078\\u9069\\u7528\\u6236\\u578B \");\n            i0.ɵɵelementStart(30, \"span\", 8);\n            i0.ɵɵtext(31, \"*\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(32, \" : \");\n            i0.ɵɵelementEnd()()()();\n            i0.ɵɵelementStart(33, \"div\", 16)(34, \"table\", 17)(35, \"thead\")(36, \"tr\");\n            i0.ɵɵelement(37, \"th\");\n            i0.ɵɵtemplate(38, EditSettingTimePeriodComponent_ng_container_38_Template, 5, 2, \"ng-container\", 18);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(39, \"tbody\");\n            i0.ɵɵtemplate(40, EditSettingTimePeriodComponent_tr_40_Template, 6, 3, \"tr\", 18);\n            i0.ɵɵelementEnd()()()();\n            i0.ɵɵelementStart(41, \"nb-card-footer\")(42, \"div\", 19)(43, \"button\", 20);\n            i0.ɵɵlistener(\"click\", function EditSettingTimePeriodComponent_Template_button_click_43_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.goBack());\n            });\n            i0.ɵɵtext(44, \" \\u8FD4\\u56DE\\u4E0A\\u9801 \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(45, \"button\", 21);\n            i0.ɵɵlistener(\"click\", function EditSettingTimePeriodComponent_Template_button_click_45_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.onSubmit());\n            });\n            i0.ɵɵtext(46, \" \\u5132\\u5B58 \");\n            i0.ɵɵelementEnd()()()();\n          }\n          if (rf & 2) {\n            const StartDate_r10 = i0.ɵɵreference(18);\n            const EndDate_r11 = i0.ɵɵreference(25);\n            i0.ɵɵadvance(16);\n            i0.ɵɵproperty(\"nbDatepicker\", StartDate_r10);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchQuery.CChangeStartDate);\n            i0.ɵɵadvance(7);\n            i0.ɵɵproperty(\"nbDatepicker\", EndDate_r11);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchQuery.CChangeEndDate);\n            i0.ɵɵadvance(15);\n            i0.ɵɵproperty(\"ngForOf\", ctx.houseChangeDates);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"ngForOf\", ctx.convertedHouseArray);\n          }\n        },\n        dependencies: [CommonModule, i7.NgForOf, i7.NgIf, SharedModule, i9.DefaultValueAccessor, i9.NgControlStatus, i9.NgModel, i2.NbCardComponent, i2.NbCardBodyComponent, i2.NbCardFooterComponent, i2.NbCardHeaderComponent, i2.NbCheckboxComponent, i2.NbInputDirective, i2.NbFormFieldComponent, i2.NbPrefixDirective, i2.NbIconComponent, i2.NbDatepickerDirective, i2.NbDatepickerComponent, i10.BreadcrumbComponent, NbDatepickerModule, NbDateFnsDateModule]\n      });\n    }\n  }\n  return EditSettingTimePeriodComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}