{"ast": null, "code": "//! moment.js locale configuration\n//! locale : Irish or Irish Gaelic [ga]\n//! author : <PERSON> : https://github.com/askpt\n\n;\n(function (global, factory) {\n  typeof exports === 'object' && typeof module !== 'undefined' && typeof require === 'function' ? factory(require('../moment')) : typeof define === 'function' && define.amd ? define(['../moment'], factory) : factory(global.moment);\n})(this, function (moment) {\n  'use strict';\n\n  //! moment.js locale configuration\n  var months = ['<PERSON>an<PERSON>ir', '<PERSON><PERSON><PERSON>', 'M<PERSON><PERSON>', 'Aibreán', 'Bealtaine', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>hai<PERSON>', '<PERSON>hain', '<PERSON>lla<PERSON>'],\n    monthsShort = ['Ean', '<PERSON>abh', '<PERSON>árt', 'Aib', 'Beal', 'Meith', '<PERSON><PERSON>il', 'Lún', 'M.F.', '<PERSON><PERSON><PERSON>.', '<PERSON><PERSON>', 'Noll'],\n    weekdays = ['<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON> Sathairn'],\n    weekdaysShort = ['Domh', 'Luan', 'Máirt', 'Céad', 'Déar', 'Aoine', 'Sath'],\n    weekdaysMin = ['Do', 'Lu', 'Má', 'Cé', 'Dé', 'A', 'Sa'];\n  var ga = moment.defineLocale('ga', {\n    months: months,\n    monthsShort: monthsShort,\n    monthsParseExact: true,\n    weekdays: weekdays,\n    weekdaysShort: weekdaysShort,\n    weekdaysMin: weekdaysMin,\n    longDateFormat: {\n      LT: 'HH:mm',\n      LTS: 'HH:mm:ss',\n      L: 'DD/MM/YYYY',\n      LL: 'D MMMM YYYY',\n      LLL: 'D MMMM YYYY HH:mm',\n      LLLL: 'dddd, D MMMM YYYY HH:mm'\n    },\n    calendar: {\n      sameDay: '[Inniu ag] LT',\n      nextDay: '[Amárach ag] LT',\n      nextWeek: 'dddd [ag] LT',\n      lastDay: '[Inné ag] LT',\n      lastWeek: 'dddd [seo caite] [ag] LT',\n      sameElse: 'L'\n    },\n    relativeTime: {\n      future: 'i %s',\n      past: '%s ó shin',\n      s: 'cúpla soicind',\n      ss: '%d soicind',\n      m: 'nóiméad',\n      mm: '%d nóiméad',\n      h: 'uair an chloig',\n      hh: '%d uair an chloig',\n      d: 'lá',\n      dd: '%d lá',\n      M: 'mí',\n      MM: '%d míonna',\n      y: 'bliain',\n      yy: '%d bliain'\n    },\n    dayOfMonthOrdinalParse: /\\d{1,2}(d|na|mh)/,\n    ordinal: function (number) {\n      var output = number === 1 ? 'd' : number % 10 === 2 ? 'na' : 'mh';\n      return number + output;\n    },\n    week: {\n      dow: 1,\n      // Monday is the first day of the week.\n      doy: 4 // The week that contains Jan 4th is the first week of the year.\n    }\n  });\n  return ga;\n});", "map": {"version": 3, "names": ["global", "factory", "exports", "module", "require", "define", "amd", "moment", "months", "monthsShort", "weekdays", "weekdaysShort", "weekdaysMin", "ga", "defineLocale", "monthsParseExact", "longDateFormat", "LT", "LTS", "L", "LL", "LLL", "LLLL", "calendar", "sameDay", "nextDay", "nextWeek", "lastDay", "lastWeek", "same<PERSON><PERSON><PERSON>", "relativeTime", "future", "past", "s", "ss", "m", "mm", "h", "hh", "d", "dd", "M", "MM", "y", "yy", "dayOfMonthOrdinalParse", "ordinal", "number", "output", "week", "dow", "doy"], "sources": ["C:/Users/<USER>/Documents/jeansalechange/adminFront/node_modules/moment/locale/ga.js"], "sourcesContent": ["//! moment.js locale configuration\n//! locale : Irish or Irish Gaelic [ga]\n//! author : <PERSON> : https://github.com/askpt\n\n;(function (global, factory) {\n   typeof exports === 'object' && typeof module !== 'undefined'\n       && typeof require === 'function' ? factory(require('../moment')) :\n   typeof define === 'function' && define.amd ? define(['../moment'], factory) :\n   factory(global.moment)\n}(this, (function (moment) { 'use strict';\n\n    //! moment.js locale configuration\n\n    var months = [\n            '<PERSON>an<PERSON>ir',\n            '<PERSON><PERSON><PERSON>',\n            '<PERSON><PERSON><PERSON>',\n            'Aibreán',\n            '<PERSON>altaine',\n            '<PERSON><PERSON><PERSON><PERSON>',\n            '<PERSON><PERSON><PERSON>',\n            '<PERSON><PERSON><PERSON>',\n            '<PERSON><PERSON>',\n            '<PERSON><PERSON><PERSON><PERSON>hai<PERSON>',\n            '<PERSON>hain',\n            '<PERSON><PERSON><PERSON>',\n        ],\n        monthsShort = [\n            'Ean',\n            '<PERSON>abh',\n            '<PERSON>árt',\n            'Aib',\n            'Beal',\n            'Meith',\n            '<PERSON><PERSON>il',\n            '<PERSON>ún',\n            'M.F.',\n            '<PERSON><PERSON><PERSON>.',\n            '<PERSON><PERSON>',\n            'Noll',\n        ],\n        weekdays = [\n            '<PERSON><PERSON>',\n            '<PERSON><PERSON>',\n            '<PERSON><PERSON>',\n            '<PERSON><PERSON>',\n            '<PERSON><PERSON><PERSON><PERSON><PERSON>',\n            '<PERSON><PERSON>',\n            '<PERSON>é Sathairn',\n        ],\n        weekdaysShort = ['Domh', 'Luan', 'Máirt', 'Céad', 'Déar', 'Aoine', 'Sath'],\n        weekdaysMin = ['Do', 'Lu', 'Má', 'Cé', 'Dé', 'A', 'Sa'];\n\n    var ga = moment.defineLocale('ga', {\n        months: months,\n        monthsShort: monthsShort,\n        monthsParseExact: true,\n        weekdays: weekdays,\n        weekdaysShort: weekdaysShort,\n        weekdaysMin: weekdaysMin,\n        longDateFormat: {\n            LT: 'HH:mm',\n            LTS: 'HH:mm:ss',\n            L: 'DD/MM/YYYY',\n            LL: 'D MMMM YYYY',\n            LLL: 'D MMMM YYYY HH:mm',\n            LLLL: 'dddd, D MMMM YYYY HH:mm',\n        },\n        calendar: {\n            sameDay: '[Inniu ag] LT',\n            nextDay: '[Amárach ag] LT',\n            nextWeek: 'dddd [ag] LT',\n            lastDay: '[Inné ag] LT',\n            lastWeek: 'dddd [seo caite] [ag] LT',\n            sameElse: 'L',\n        },\n        relativeTime: {\n            future: 'i %s',\n            past: '%s ó shin',\n            s: 'cúpla soicind',\n            ss: '%d soicind',\n            m: 'nóiméad',\n            mm: '%d nóiméad',\n            h: 'uair an chloig',\n            hh: '%d uair an chloig',\n            d: 'lá',\n            dd: '%d lá',\n            M: 'mí',\n            MM: '%d míonna',\n            y: 'bliain',\n            yy: '%d bliain',\n        },\n        dayOfMonthOrdinalParse: /\\d{1,2}(d|na|mh)/,\n        ordinal: function (number) {\n            var output = number === 1 ? 'd' : number % 10 === 2 ? 'na' : 'mh';\n            return number + output;\n        },\n        week: {\n            dow: 1, // Monday is the first day of the week.\n            doy: 4, // The week that contains Jan 4th is the first week of the year.\n        },\n    });\n\n    return ga;\n\n})));\n"], "mappings": "AAAA;AACA;AACA;;AAEA;AAAE,WAAUA,MAAM,EAAEC,OAAO,EAAE;EAC1B,OAAOC,OAAO,KAAK,QAAQ,IAAI,OAAOC,MAAM,KAAK,WAAW,IACrD,OAAOC,OAAO,KAAK,UAAU,GAAGH,OAAO,CAACG,OAAO,CAAC,WAAW,CAAC,CAAC,GACpE,OAAOC,MAAM,KAAK,UAAU,IAAIA,MAAM,CAACC,GAAG,GAAGD,MAAM,CAAC,CAAC,WAAW,CAAC,EAAEJ,OAAO,CAAC,GAC3EA,OAAO,CAACD,MAAM,CAACO,MAAM,CAAC;AACzB,CAAC,EAAC,IAAI,EAAG,UAAUA,MAAM,EAAE;EAAE,YAAY;;EAErC;EAEA,IAAIC,MAAM,GAAG,CACL,QAAQ,EACR,SAAS,EACT,OAAO,EACP,SAAS,EACT,WAAW,EACX,WAAW,EACX,MAAM,EACN,QAAQ,EACR,cAAc,EACd,kBAAkB,EAClB,SAAS,EACT,SAAS,CACZ;IACDC,WAAW,GAAG,CACV,KAAK,EACL,OAAO,EACP,MAAM,EACN,KAAK,EACL,MAAM,EACN,OAAO,EACP,MAAM,EACN,KAAK,EACL,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,CACT;IACDC,QAAQ,GAAG,CACP,cAAc,EACd,UAAU,EACV,UAAU,EACV,aAAa,EACb,WAAW,EACX,WAAW,EACX,aAAa,CAChB;IACDC,aAAa,GAAG,CAAC,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,CAAC;IAC1EC,WAAW,GAAG,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,EAAE,IAAI,CAAC;EAE3D,IAAIC,EAAE,GAAGN,MAAM,CAACO,YAAY,CAAC,IAAI,EAAE;IAC/BN,MAAM,EAAEA,MAAM;IACdC,WAAW,EAAEA,WAAW;IACxBM,gBAAgB,EAAE,IAAI;IACtBL,QAAQ,EAAEA,QAAQ;IAClBC,aAAa,EAAEA,aAAa;IAC5BC,WAAW,EAAEA,WAAW;IACxBI,cAAc,EAAE;MACZC,EAAE,EAAE,OAAO;MACXC,GAAG,EAAE,UAAU;MACfC,CAAC,EAAE,YAAY;MACfC,EAAE,EAAE,aAAa;MACjBC,GAAG,EAAE,mBAAmB;MACxBC,IAAI,EAAE;IACV,CAAC;IACDC,QAAQ,EAAE;MACNC,OAAO,EAAE,eAAe;MACxBC,OAAO,EAAE,iBAAiB;MAC1BC,QAAQ,EAAE,cAAc;MACxBC,OAAO,EAAE,cAAc;MACvBC,QAAQ,EAAE,0BAA0B;MACpCC,QAAQ,EAAE;IACd,CAAC;IACDC,YAAY,EAAE;MACVC,MAAM,EAAE,MAAM;MACdC,IAAI,EAAE,WAAW;MACjBC,CAAC,EAAE,eAAe;MAClBC,EAAE,EAAE,YAAY;MAChBC,CAAC,EAAE,SAAS;MACZC,EAAE,EAAE,YAAY;MAChBC,CAAC,EAAE,gBAAgB;MACnBC,EAAE,EAAE,mBAAmB;MACvBC,CAAC,EAAE,IAAI;MACPC,EAAE,EAAE,OAAO;MACXC,CAAC,EAAE,IAAI;MACPC,EAAE,EAAE,WAAW;MACfC,CAAC,EAAE,QAAQ;MACXC,EAAE,EAAE;IACR,CAAC;IACDC,sBAAsB,EAAE,kBAAkB;IAC1CC,OAAO,EAAE,SAAAA,CAAUC,MAAM,EAAE;MACvB,IAAIC,MAAM,GAAGD,MAAM,KAAK,CAAC,GAAG,GAAG,GAAGA,MAAM,GAAG,EAAE,KAAK,CAAC,GAAG,IAAI,GAAG,IAAI;MACjE,OAAOA,MAAM,GAAGC,MAAM;IAC1B,CAAC;IACDC,IAAI,EAAE;MACFC,GAAG,EAAE,CAAC;MAAE;MACRC,GAAG,EAAE,CAAC,CAAE;IACZ;EACJ,CAAC,CAAC;EAEF,OAAOtC,EAAE;AAEb,CAAE,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}