{"ast": null, "code": "import toDate from \"../toDate/index.js\";\nimport differenceInCalendarDays from \"../differenceInCalendarDays/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\"; // Like `compareAsc` but uses local time not UTC, which is needed\n// for accurate equality comparisons of UTC timestamps that end up\n// having the same representation in local time, e.g. one hour before\n// DST ends vs. the instant that DST ends.\nfunction compareLocalAsc(dateLeft, dateRight) {\n  var diff = dateLeft.getFullYear() - dateRight.getFullYear() || dateLeft.getMonth() - dateRight.getMonth() || dateLeft.getDate() - dateRight.getDate() || dateLeft.getHours() - dateRight.getHours() || dateLeft.getMinutes() - dateRight.getMinutes() || dateLeft.getSeconds() - dateRight.getSeconds() || dateLeft.getMilliseconds() - dateRight.getMilliseconds();\n  if (diff < 0) {\n    return -1;\n  } else if (diff > 0) {\n    return 1;\n    // Return 0 if diff is 0; return NaN if diff is NaN\n  } else {\n    return diff;\n  }\n}\n\n/**\n * @name differenceInDays\n * @category Day Helpers\n * @summary Get the number of full days between the given dates.\n *\n * @description\n * Get the number of full day periods between two dates. Fractional days are\n * truncated towards zero.\n *\n * One \"full day\" is the distance between a local time in one day to the same\n * local time on the next or previous day. A full day can sometimes be less than\n * or more than 24 hours if a daylight savings change happens between two dates.\n *\n * To ignore DST and only measure exact 24-hour periods, use this instead:\n * `Math.floor(differenceInHours(dateLeft, dateRight)/24)|0`.\n *\n *\n * @param {Date|Number} dateLeft - the later date\n * @param {Date|Number} dateRight - the earlier date\n * @returns {Number} the number of full days according to the local timezone\n * @throws {TypeError} 2 arguments required\n *\n * @example\n * // How many full days are between\n * // 2 July 2011 23:00:00 and 2 July 2012 00:00:00?\n * const result = differenceInDays(\n *   new Date(2012, 6, 2, 0, 0),\n *   new Date(2011, 6, 2, 23, 0)\n * )\n * //=> 365\n * // How many full days are between\n * // 2 July 2011 23:59:00 and 3 July 2011 00:01:00?\n * const result = differenceInDays(\n *   new Date(2011, 6, 3, 0, 1),\n *   new Date(2011, 6, 2, 23, 59)\n * )\n * //=> 0\n * // How many full days are between\n * // 1 March 2020 0:00 and 1 June 2020 0:00 ?\n * // Note: because local time is used, the\n * // result will always be 92 days, even in\n * // time zones where DST starts and the\n * // period has only 92*24-1 hours.\n * const result = differenceInDays(\n *   new Date(2020, 5, 1),\n *   new Date(2020, 2, 1)\n * )\n//=> 92\n */\nexport default function differenceInDays(dirtyDateLeft, dirtyDateRight) {\n  requiredArgs(2, arguments);\n  var dateLeft = toDate(dirtyDateLeft);\n  var dateRight = toDate(dirtyDateRight);\n  var sign = compareLocalAsc(dateLeft, dateRight);\n  var difference = Math.abs(differenceInCalendarDays(dateLeft, dateRight));\n  dateLeft.setDate(dateLeft.getDate() - sign * difference);\n\n  // Math.abs(diff in full days - diff in calendar days) === 1 if last calendar day is not full\n  // If so, result must be decreased by 1 in absolute value\n  var isLastDayNotFull = Number(compareLocalAsc(dateLeft, dateRight) === -sign);\n  var result = sign * (difference - isLastDayNotFull);\n  // Prevent negative zero\n  return result === 0 ? 0 : result;\n}", "map": {"version": 3, "names": ["toDate", "differenceInCalendarDays", "requiredArgs", "compareLocalAsc", "dateLeft", "dateRight", "diff", "getFullYear", "getMonth", "getDate", "getHours", "getMinutes", "getSeconds", "getMilliseconds", "differenceInDays", "dirtyDateLeft", "dirtyDateRight", "arguments", "sign", "difference", "Math", "abs", "setDate", "isLastDayNotFull", "Number", "result"], "sources": ["C:/Users/<USER>/Documents/jeansalechange/adminFront/node_modules/date-fns/esm/differenceInDays/index.js"], "sourcesContent": ["import toDate from \"../toDate/index.js\";\nimport differenceInCalendarDays from \"../differenceInCalendarDays/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\"; // Like `compareAsc` but uses local time not UTC, which is needed\n// for accurate equality comparisons of UTC timestamps that end up\n// having the same representation in local time, e.g. one hour before\n// DST ends vs. the instant that DST ends.\nfunction compareLocalAsc(dateLeft, dateRight) {\n  var diff = dateLeft.getFullYear() - dateRight.getFullYear() || dateLeft.getMonth() - dateRight.getMonth() || dateLeft.getDate() - dateRight.getDate() || dateLeft.getHours() - dateRight.getHours() || dateLeft.getMinutes() - dateRight.getMinutes() || dateLeft.getSeconds() - dateRight.getSeconds() || dateLeft.getMilliseconds() - dateRight.getMilliseconds();\n  if (diff < 0) {\n    return -1;\n  } else if (diff > 0) {\n    return 1;\n    // Return 0 if diff is 0; return NaN if diff is NaN\n  } else {\n    return diff;\n  }\n}\n\n/**\n * @name differenceInDays\n * @category Day Helpers\n * @summary Get the number of full days between the given dates.\n *\n * @description\n * Get the number of full day periods between two dates. Fractional days are\n * truncated towards zero.\n *\n * One \"full day\" is the distance between a local time in one day to the same\n * local time on the next or previous day. A full day can sometimes be less than\n * or more than 24 hours if a daylight savings change happens between two dates.\n *\n * To ignore DST and only measure exact 24-hour periods, use this instead:\n * `Math.floor(differenceInHours(dateLeft, dateRight)/24)|0`.\n *\n *\n * @param {Date|Number} dateLeft - the later date\n * @param {Date|Number} dateRight - the earlier date\n * @returns {Number} the number of full days according to the local timezone\n * @throws {TypeError} 2 arguments required\n *\n * @example\n * // How many full days are between\n * // 2 July 2011 23:00:00 and 2 July 2012 00:00:00?\n * const result = differenceInDays(\n *   new Date(2012, 6, 2, 0, 0),\n *   new Date(2011, 6, 2, 23, 0)\n * )\n * //=> 365\n * // How many full days are between\n * // 2 July 2011 23:59:00 and 3 July 2011 00:01:00?\n * const result = differenceInDays(\n *   new Date(2011, 6, 3, 0, 1),\n *   new Date(2011, 6, 2, 23, 59)\n * )\n * //=> 0\n * // How many full days are between\n * // 1 March 2020 0:00 and 1 June 2020 0:00 ?\n * // Note: because local time is used, the\n * // result will always be 92 days, even in\n * // time zones where DST starts and the\n * // period has only 92*24-1 hours.\n * const result = differenceInDays(\n *   new Date(2020, 5, 1),\n *   new Date(2020, 2, 1)\n * )\n//=> 92\n */\nexport default function differenceInDays(dirtyDateLeft, dirtyDateRight) {\n  requiredArgs(2, arguments);\n  var dateLeft = toDate(dirtyDateLeft);\n  var dateRight = toDate(dirtyDateRight);\n  var sign = compareLocalAsc(dateLeft, dateRight);\n  var difference = Math.abs(differenceInCalendarDays(dateLeft, dateRight));\n  dateLeft.setDate(dateLeft.getDate() - sign * difference);\n\n  // Math.abs(diff in full days - diff in calendar days) === 1 if last calendar day is not full\n  // If so, result must be decreased by 1 in absolute value\n  var isLastDayNotFull = Number(compareLocalAsc(dateLeft, dateRight) === -sign);\n  var result = sign * (difference - isLastDayNotFull);\n  // Prevent negative zero\n  return result === 0 ? 0 : result;\n}"], "mappings": "AAAA,OAAOA,MAAM,MAAM,oBAAoB;AACvC,OAAOC,wBAAwB,MAAM,sCAAsC;AAC3E,OAAOC,YAAY,MAAM,+BAA+B,CAAC,CAAC;AAC1D;AACA;AACA;AACA,SAASC,eAAeA,CAACC,QAAQ,EAAEC,SAAS,EAAE;EAC5C,IAAIC,IAAI,GAAGF,QAAQ,CAACG,WAAW,CAAC,CAAC,GAAGF,SAAS,CAACE,WAAW,CAAC,CAAC,IAAIH,QAAQ,CAACI,QAAQ,CAAC,CAAC,GAAGH,SAAS,CAACG,QAAQ,CAAC,CAAC,IAAIJ,QAAQ,CAACK,OAAO,CAAC,CAAC,GAAGJ,SAAS,CAACI,OAAO,CAAC,CAAC,IAAIL,QAAQ,CAACM,QAAQ,CAAC,CAAC,GAAGL,SAAS,CAACK,QAAQ,CAAC,CAAC,IAAIN,QAAQ,CAACO,UAAU,CAAC,CAAC,GAAGN,SAAS,CAACM,UAAU,CAAC,CAAC,IAAIP,QAAQ,CAACQ,UAAU,CAAC,CAAC,GAAGP,SAAS,CAACO,UAAU,CAAC,CAAC,IAAIR,QAAQ,CAACS,eAAe,CAAC,CAAC,GAAGR,SAAS,CAACQ,eAAe,CAAC,CAAC;EACnW,IAAIP,IAAI,GAAG,CAAC,EAAE;IACZ,OAAO,CAAC,CAAC;EACX,CAAC,MAAM,IAAIA,IAAI,GAAG,CAAC,EAAE;IACnB,OAAO,CAAC;IACR;EACF,CAAC,MAAM;IACL,OAAOA,IAAI;EACb;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,SAASQ,gBAAgBA,CAACC,aAAa,EAAEC,cAAc,EAAE;EACtEd,YAAY,CAAC,CAAC,EAAEe,SAAS,CAAC;EAC1B,IAAIb,QAAQ,GAAGJ,MAAM,CAACe,aAAa,CAAC;EACpC,IAAIV,SAAS,GAAGL,MAAM,CAACgB,cAAc,CAAC;EACtC,IAAIE,IAAI,GAAGf,eAAe,CAACC,QAAQ,EAAEC,SAAS,CAAC;EAC/C,IAAIc,UAAU,GAAGC,IAAI,CAACC,GAAG,CAACpB,wBAAwB,CAACG,QAAQ,EAAEC,SAAS,CAAC,CAAC;EACxED,QAAQ,CAACkB,OAAO,CAAClB,QAAQ,CAACK,OAAO,CAAC,CAAC,GAAGS,IAAI,GAAGC,UAAU,CAAC;;EAExD;EACA;EACA,IAAII,gBAAgB,GAAGC,MAAM,CAACrB,eAAe,CAACC,QAAQ,EAAEC,SAAS,CAAC,KAAK,CAACa,IAAI,CAAC;EAC7E,IAAIO,MAAM,GAAGP,IAAI,IAAIC,UAAU,GAAGI,gBAAgB,CAAC;EACnD;EACA,OAAOE,MAAM,KAAK,CAAC,GAAG,CAAC,GAAGA,MAAM;AAClC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}