{"ast": null, "code": "import * as moment from 'moment';\nimport { BaseComponent } from '../../components/base/baseComponent';\nimport { EEvent } from 'src/app/shared/services/event.service';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/shared/helper/allowHelper\";\nimport * as i2 from \"@nebular/theme\";\nimport * as i3 from \"src/app/shared/helper/validationHelper\";\nimport * as i4 from \"src/services/api/services\";\nimport * as i5 from \"@angular/router\";\nimport * as i6 from \"src/app/shared/services/message.service\";\nimport * as i7 from \"@angular/common\";\nimport * as i8 from \"src/app/shared/services/event.service\";\nimport * as i9 from \"@angular/forms\";\nimport * as i10 from \"@ng-bootstrap/ng-bootstrap\";\nimport * as i11 from \"../../../@theme/directives/label.directive\";\nimport * as i12 from \"primeng/calendar\";\nconst _c0 = [\"fileInput\"];\nfunction CustomerChangePictureComponent_button_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 17);\n    i0.ɵɵlistener(\"click\", function CustomerChangePictureComponent_button_9_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      const dialogUploadDrawing_r4 = i0.ɵɵreference(33);\n      return i0.ɵɵresetView(ctx_r2.addNew(dialogUploadDrawing_r4));\n    });\n    i0.ɵɵtext(1, \" \\u4E0A\\u50B3\\u5716\\u9762\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CustomerChangePictureComponent_tr_25_button_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 21);\n    i0.ɵɵlistener(\"click\", function CustomerChangePictureComponent_tr_25_button_10_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const item_r6 = i0.ɵɵnextContext().$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      const dialogUploadDrawing_r4 = i0.ɵɵreference(33);\n      return i0.ɵɵresetView(ctx_r2.onEdit(dialogUploadDrawing_r4, item_r6));\n    });\n    i0.ɵɵtext(1, \"\\u6AA2\\u8996\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CustomerChangePictureComponent_tr_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\", 18)(1, \"td\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"td\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"td\", 19);\n    i0.ɵɵtemplate(10, CustomerChangePictureComponent_tr_25_button_10_Template, 2, 0, \"button\", 20);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const item_r6 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r2.formatDate(item_r6.CChangeDate));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r6.CDrawingName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r2.formatDate(item_r6.CCreateDT));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r6.CIsApprove == null ? \"\\u5F85\\u5BE9\\u6838\" : item_r6.CIsApprove ? \"\\u901A\\u904E\" : \"\\u99C1\\u56DE\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isUpdate);\n  }\n}\nfunction CustomerChangePictureComponent_ng_template_32_div_22_div_1_img_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 46);\n  }\n  if (rf & 2) {\n    const file_r10 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵproperty(\"src\", file_r10.data, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction CustomerChangePictureComponent_ng_template_32_div_22_div_1_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 47);\n    i0.ɵɵtext(1, \"PDF\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CustomerChangePictureComponent_ng_template_32_div_22_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 40);\n    i0.ɵɵtemplate(1, CustomerChangePictureComponent_ng_template_32_div_22_div_1_img_1_Template, 1, 1, \"img\", 41)(2, CustomerChangePictureComponent_ng_template_32_div_22_div_1_span_2_Template, 2, 0, \"span\", 42);\n    i0.ɵɵelementStart(3, \"p\", 43);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"span\", 44);\n    i0.ɵɵlistener(\"click\", function CustomerChangePictureComponent_ng_template_32_div_22_div_1_Template_span_click_5_listener() {\n      const i_r11 = i0.ɵɵrestoreView(_r9).index;\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r2.removeFile(i_r11));\n    });\n    i0.ɵɵelement(6, \"i\", 45);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const file_r10 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isImage(file_r10.CFileType));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.isImage(file_r10.CFileType));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(file_r10.CFileName);\n  }\n}\nfunction CustomerChangePictureComponent_ng_template_32_div_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 38);\n    i0.ɵɵtemplate(1, CustomerChangePictureComponent_ng_template_32_div_22_div_1_Template, 7, 3, \"div\", 39);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.imageUrlList);\n  }\n}\nfunction CustomerChangePictureComponent_ng_template_32_div_23_div_1_img_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"img\", 50);\n    i0.ɵɵlistener(\"click\", function CustomerChangePictureComponent_ng_template_32_div_23_div_1_img_1_Template_img_click_0_listener() {\n      i0.ɵɵrestoreView(_r12);\n      const file_r13 = i0.ɵɵnextContext().$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r2.openNewTab(file_r13.CFile));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const file_r13 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵproperty(\"src\", file_r13.CFile, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction CustomerChangePictureComponent_ng_template_32_div_23_div_1_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r14 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"span\", 51);\n    i0.ɵɵlistener(\"click\", function CustomerChangePictureComponent_ng_template_32_div_23_div_1_span_2_Template_span_click_0_listener() {\n      i0.ɵɵrestoreView(_r14);\n      const file_r13 = i0.ɵɵnextContext().$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r2.openNewTab(file_r13.CFile));\n    });\n    i0.ɵɵtext(1, \"PDF\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CustomerChangePictureComponent_ng_template_32_div_23_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 40);\n    i0.ɵɵtemplate(1, CustomerChangePictureComponent_ng_template_32_div_23_div_1_img_1_Template, 1, 1, \"img\", 48)(2, CustomerChangePictureComponent_ng_template_32_div_23_div_1_span_2_Template, 2, 0, \"span\", 49);\n    i0.ɵɵelementStart(3, \"p\", 43);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const file_r13 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.isPDFString(file_r13.CFile));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isPDFString(file_r13.CFile));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(file_r13.CFileName);\n  }\n}\nfunction CustomerChangePictureComponent_ng_template_32_div_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 38);\n    i0.ɵɵtemplate(1, CustomerChangePictureComponent_ng_template_32_div_23_div_1_Template, 5, 3, \"div\", 39);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.SpecialChange.CFileRes);\n  }\n}\nfunction CustomerChangePictureComponent_ng_template_32_button_31_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r16 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 52);\n    i0.ɵɵlistener(\"click\", function CustomerChangePictureComponent_ng_template_32_button_31_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r16);\n      const ref_r15 = i0.ɵɵnextContext().dialogRef;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onSaveSpecialChange(ref_r15));\n    });\n    i0.ɵɵtext(1, \"\\u9001\\u51FA\\u5BE9\\u6838\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CustomerChangePictureComponent_ng_template_32_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-card\", 22)(1, \"nb-card-header\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"nb-card-body\", 23)(4, \"div\", 24)(5, \"label\", 25, 1);\n    i0.ɵɵtext(7, \" \\u8A0E\\u8AD6\\u65E5\\u671F \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"p-calendar\", 26);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function CustomerChangePictureComponent_ng_template_32_Template_p_calendar_ngModelChange_8_listener($event) {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.formSpecialChange.CChangeDate, $event) || (ctx_r2.formSpecialChange.CChangeDate = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"div\", 24)(10, \"label\", 27);\n    i0.ɵɵtext(11, \" \\u5716\\u9762\\u540D\\u7A31 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"input\", 28);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function CustomerChangePictureComponent_ng_template_32_Template_input_ngModelChange_12_listener($event) {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.formSpecialChange.CDrawingName, $event) || (ctx_r2.formSpecialChange.CDrawingName = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"div\", 24)(14, \"label\", 29);\n    i0.ɵɵtext(15, \" \\u9078\\u6A23\\u7D50\\u679C \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"button\", 17);\n    i0.ɵɵlistener(\"click\", function CustomerChangePictureComponent_ng_template_32_Template_button_click_16_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const inputFile_r8 = i0.ɵɵreference(19);\n      return i0.ɵɵresetView(inputFile_r8.click());\n    });\n    i0.ɵɵtext(17, \"\\u9078\\u64C7\\u6A94\\u6848\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"input\", 30, 2);\n    i0.ɵɵlistener(\"change\", function CustomerChangePictureComponent_ng_template_32_Template_input_change_18_listener($event) {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.detectFiles($event));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(20, \"div\", 31);\n    i0.ɵɵelement(21, \"label\", 32);\n    i0.ɵɵtemplate(22, CustomerChangePictureComponent_ng_template_32_div_22_Template, 2, 1, \"div\", 33)(23, CustomerChangePictureComponent_ng_template_32_div_23_Template, 2, 1, \"div\", 33);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"div\", 31)(25, \"label\", 34);\n    i0.ɵɵtext(26, \"\\u5BE9\\u6838\\u8AAA\\u660E\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(27, \"textarea\", 35);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function CustomerChangePictureComponent_ng_template_32_Template_textarea_ngModelChange_27_listener($event) {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.formSpecialChange.CApproveRemark, $event) || (ctx_r2.formSpecialChange.CApproveRemark = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(28, \"div\", 14)(29, \"button\", 36);\n    i0.ɵɵlistener(\"click\", function CustomerChangePictureComponent_ng_template_32_Template_button_click_29_listener() {\n      const ref_r15 = i0.ɵɵrestoreView(_r7).dialogRef;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onClose(ref_r15));\n    });\n    i0.ɵɵtext(30, \"\\u53D6\\u6D88\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(31, CustomerChangePictureComponent_ng_template_32_button_31_Template, 2, 0, \"button\", 37);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate3(\" \\u6236\\u5225\\u7BA1\\u7406 > \", ctx_r2.house.CCustomerName, \" > \", ctx_r2.house.CHousehold, \" \\u00A0 \", ctx_r2.house.CFloor, \"F \");\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"appendTo\", \"CChangeDate\")(\"iconDisplay\", \"input\")(\"showIcon\", true);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.formSpecialChange.CChangeDate);\n    i0.ɵɵproperty(\"disabled\", ctx_r2.isEdit)(\"showButtonBar\", true);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.formSpecialChange.CDrawingName);\n    i0.ɵɵproperty(\"disabled\", ctx_r2.isEdit);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"disabled\", ctx_r2.isEdit);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.isEdit);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isEdit);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"disabled\", ctx_r2.isEdit);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.formSpecialChange.CApproveRemark);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.isEdit);\n  }\n}\nexport class CustomerChangePictureComponent extends BaseComponent {\n  constructor(_allow, dialogService, valid, _specialChangeService, _houseService, route, message, location, _eventService) {\n    super(_allow);\n    this._allow = _allow;\n    this.dialogService = dialogService;\n    this.valid = valid;\n    this._specialChangeService = _specialChangeService;\n    this._houseService = _houseService;\n    this.route = route;\n    this.message = message;\n    this.location = location;\n    this._eventService = _eventService;\n    this.imageUrlList = [];\n    this.isEdit = false;\n    this.statusOptions = [{\n      value: 0,\n      key: 'allow',\n      label: '允許'\n    }, {\n      value: 1,\n      key: 'not allowed',\n      label: '不允許'\n    }];\n    this.pageFirst = 1;\n    this.pageSize = 10;\n    this.pageIndex = 1;\n    this.totalRecords = 0;\n    this.listPictures = [];\n  }\n  ngOnInit() {\n    this.route.paramMap.subscribe(params => {\n      if (params) {\n        const idParam = params.get('id1');\n        const id = idParam ? +idParam : 0;\n        this.buildCaseId = id;\n        const idParam2 = params.get('id2');\n        const id2 = idParam2 ? +idParam2 : 0;\n        this.houseId = id2;\n        this.getListSpecialChange();\n        this.getHouseById();\n      }\n    });\n  }\n  openPdfInNewTab(data) {\n    if (data && data.CFileRes.CFile) window.open(data.CFileRes.CFile, '_blank');\n  }\n  getListSpecialChange() {\n    this._specialChangeService.apiSpecialChangeGetListSpecialChangePost$Json({\n      body: {\n        CHouseId: this.houseId,\n        PageIndex: this.pageIndex,\n        PageSize: this.pageSize\n      }\n    }).subscribe(res => {\n      if (res.TotalItems && res.Entries && res.StatusCode == 0) {\n        this.listSpecialChange = res.Entries ?? [];\n        this.totalRecords = res.TotalItems;\n      }\n    });\n  }\n  getHouseById() {\n    this._houseService.apiHouseGetHouseByIdPost$Json({\n      body: {\n        CHouseID: this.houseId\n      }\n    }).subscribe(res => {\n      if (res.TotalItems && res.Entries && res.StatusCode == 0) {\n        this.house = res.Entries;\n        this.houseTitle = `${this.house.CHousehold} ${this.house.CFloor}F`;\n      }\n    });\n  }\n  getSpecialChangeById(ref, CSpecialChangeID) {\n    this._specialChangeService.apiSpecialChangeGetSpecialChangeByIdPost$Json({\n      body: CSpecialChangeID\n    }).subscribe(res => {\n      if (res.TotalItems && res.Entries && res.StatusCode == 0) {\n        this.SpecialChange = res.Entries;\n        this.formSpecialChange = {\n          CApproveRemark: this.SpecialChange.CApproveRemark,\n          CBuildCaseID: this.buildCaseId,\n          CDrawingName: this.SpecialChange.CDrawingName,\n          CHouseID: this.houseId,\n          SpecialChangeFiles: null\n        };\n        if (this.SpecialChange.CChangeDate) {\n          this.formSpecialChange.CChangeDate = new Date(this.SpecialChange.CChangeDate);\n        }\n        this.dialogService.open(ref);\n      }\n    });\n  }\n  onSaveSpecialChange(ref) {\n    this.validation();\n    if (this.valid.errorMessages.length > 0) {\n      this.message.showErrorMSGs(this.valid.errorMessages);\n      return;\n    }\n    this._specialChangeService.apiSpecialChangeSaveSpecialChangePost$Json({\n      body: this.formatParam()\n    }).subscribe(res => {\n      if (res.StatusCode === 0) {\n        this.message.showSucessMSG(\"執行成功\");\n        this.getListSpecialChange();\n        ref.close();\n      }\n    });\n  }\n  pageChanged(newPage) {\n    this.pageIndex = newPage;\n    this.getListSpecialChange();\n  }\n  addNew(ref) {\n    this.imageUrlList = [];\n    this.isEdit = false;\n    this.formSpecialChange = {\n      CApproveRemark: '',\n      CBuildCaseID: this.buildCaseId,\n      CChangeDate: '',\n      CDrawingName: '',\n      CHouseID: this.houseId,\n      SpecialChangeFiles: null\n    };\n    this.dialogService.open(ref);\n  }\n  onEdit(ref, specialChange) {\n    this.imageUrlList = [];\n    this.isEdit = true;\n    this.getSpecialChangeById(ref, specialChange.CSpecialChangeID);\n  }\n  validation() {\n    this.valid.clear();\n    this.valid.required('[討論日期]', this.formSpecialChange.CChangeDate);\n    this.valid.required('[圖面名稱]', this.formSpecialChange.CDrawingName);\n    this.valid.required('[審核說明]', this.formSpecialChange.CApproveRemark);\n  }\n  formatDate(CChangeDate) {\n    if (CChangeDate) {\n      return moment(CChangeDate).format('YYYY-MM-DD');\n    }\n    return '';\n  }\n  deleteDataFields(array) {\n    for (const item of array) {\n      delete item.data;\n    }\n    return array;\n  }\n  formatParam() {\n    const result = {\n      ...this.formSpecialChange,\n      SpecialChangeFiles: this.imageUrlList\n    };\n    this.deleteDataFields(result.SpecialChangeFiles);\n    if (this.formSpecialChange.CChangeDate) {\n      result.CChangeDate = this.formatDate(this.formSpecialChange.CChangeDate);\n    }\n    return result;\n  }\n  onClose(ref) {\n    ref.close();\n  }\n  removeBase64Prefix(base64String) {\n    const prefixIndex = base64String.indexOf(\",\");\n    if (prefixIndex !== -1) {\n      return base64String.substring(prefixIndex + 1);\n    }\n    return base64String;\n  }\n  detectFiles(event) {\n    const files = event.target.files;\n    if (files && files.length > 0) {\n      const allowedTypes = ['image/jpeg', 'image/jpg', 'application/pdf'];\n      const fileRegex = /pdf|jpg|jpeg|png/i;\n      for (let i = 0; i < files.length; i++) {\n        const file = files[i];\n        if (!fileRegex.test(file.type)) {\n          this.message.showErrorMSG('檔案格式錯誤，僅限pdf、圖片檔');\n        }\n        if (allowedTypes.includes(file.type)) {\n          const reader = new FileReader();\n          reader.onload = e => {\n            const fileType = file.type.startsWith('image/') ? 2 : 1;\n            this.imageUrlList.push({\n              data: e.target.result,\n              CFileBlood: this.removeBase64Prefix(e.target.result),\n              CFileName: file.name,\n              CFileType: fileType\n            });\n            if (this.imageUrlList.length === files.length) {\n              console.log('this.imageUrlList', this.imageUrlList);\n              if (this.fileInput) {\n                this.fileInput.nativeElement.value = null;\n              }\n            }\n          };\n          reader.readAsDataURL(file);\n        }\n      }\n    }\n  }\n  isPDFString(str) {\n    if (str) {\n      return str.toLowerCase().endsWith(\".pdf\");\n    }\n    return false;\n  }\n  isImage(fileType) {\n    return fileType === 2;\n  }\n  isPdf(extension) {\n    return extension.toLowerCase() === 'pdf';\n  }\n  removeFile(index) {\n    this.imageUrlList.splice(index, 1);\n  }\n  removeImage(pictureId) {\n    this.listPictures = this.listPictures.filter(x => x.id != pictureId);\n  }\n  uploadImage(ref) {}\n  renameFile(event, index) {\n    var blob = this.listPictures[index].CFile.slice(0, this.listPictures[index].CFile.size, this.listPictures[index].CFile.type);\n    var newFile = new File([blob], `${event.target.value + '.' + this.listPictures[index].extension}`, {\n      type: this.listPictures[index].CFile.type\n    });\n    this.listPictures[index].CFile = newFile;\n  }\n  goBack() {\n    this._eventService.push({\n      action: \"GET_BUILDCASE\" /* EEvent.GET_BUILDCASE */,\n      payload: this.buildCaseId\n    });\n    this.location.back();\n  }\n  openNewTab(url) {\n    if (url) window.open(url, \"_blank\");\n  }\n  static {\n    this.ɵfac = function CustomerChangePictureComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || CustomerChangePictureComponent)(i0.ɵɵdirectiveInject(i1.AllowHelper), i0.ɵɵdirectiveInject(i2.NbDialogService), i0.ɵɵdirectiveInject(i3.ValidationHelper), i0.ɵɵdirectiveInject(i4.SpecialChangeService), i0.ɵɵdirectiveInject(i4.HouseService), i0.ɵɵdirectiveInject(i5.ActivatedRoute), i0.ɵɵdirectiveInject(i6.MessageService), i0.ɵɵdirectiveInject(i7.Location), i0.ɵɵdirectiveInject(i8.EventService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: CustomerChangePictureComponent,\n      selectors: [[\"ngx-customer-change-picture\"]],\n      viewQuery: function CustomerChangePictureComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.fileInput = _t.first);\n        }\n      },\n      features: [i0.ɵɵInheritDefinitionFeature],\n      decls: 34,\n      vars: 6,\n      consts: [[\"dialogUploadDrawing\", \"\"], [\"CChangeDate\", \"\"], [\"inputFile\", \"\"], [\"accent\", \"success\"], [1, \"font-bold\", \"text-[#818181]\"], [1, \"d-flex\", \"flex-wrap\"], [1, \"col-md-12\"], [1, \"d-flex\", \"justify-content-end\", \"w-full\"], [\"class\", \"btn btn-info\", 3, \"click\", 4, \"ngIf\"], [1, \"table-responsive\", \"mt-4\"], [1, \"table\", \"table-striped\", \"border\", 2, \"min-width\", \"1000px\", \"background-color\", \"#f3f3f3\"], [1, \"text-center\", 2, \"background-color\", \"#27ae60\", \"color\", \"white\"], [\"scope\", \"col\", 1, \"col-1\"], [\"class\", \"text-center\", 4, \"ngFor\", \"ngForOf\"], [1, \"d-flex\", \"justify-content-center\"], [\"aria-label\", \"Pagination\", 3, \"pageChange\", \"page\", \"pageSize\", \"collectionSize\"], [1, \"btn\", \"btn-secondary\", \"btn-sm\", 3, \"click\"], [1, \"btn\", \"btn-info\", 3, \"click\"], [1, \"text-center\"], [1, \"text-center\", \"w-32\"], [\"class\", \"btn btn-outline-primary btn-sm m-1\", 3, \"click\", 4, \"ngIf\"], [1, \"btn\", \"btn-outline-primary\", \"btn-sm\", \"m-1\", 3, \"click\"], [2, \"min-width\", \"600px\", \"max-height\", \"95vh\"], [1, \"px-4\"], [1, \"form-group\"], [\"for\", \"CChangeDate\", \"baseLabel\", \"\", 1, \"required-field\", \"mr-4\", 2, \"min-width\", \"75px\"], [\"placeholder\", \"\\u5E74/\\u6708/\\u65E5\", \"inputId\", \"icondisplay\", \"dateFormat\", \"yy/mm/dd\", 1, \"!w-[400px]\", 3, \"ngModelChange\", \"appendTo\", \"iconDisplay\", \"showIcon\", \"ngModel\", \"disabled\", \"showButtonBar\"], [\"for\", \"cDrawingName\", \"baseLabel\", \"\", 1, \"required-field\", \"mr-4\", 2, \"min-width\", \"75px\"], [\"type\", \"text\", \"nbInput\", \"\", \"placeholder\", \"\\u5716\\u9762\\u540D\\u7A31\", 1, \"w-full\", 3, \"ngModelChange\", \"ngModel\", \"disabled\"], [\"for\", \"cIsEnable\", \"baseLabel\", \"\", 1, \"mr-4\", 2, \"min-width\", \"75px\"], [\"type\", \"file\", \"id\", \"fileInput\", \"accept\", \"image/jpeg, image/jpg, application/pdf\", \"multiple\", \"\", 1, \"hidden\", 3, \"change\", \"disabled\"], [1, \"form-group\", \"d-flex\", \"align-items-center\"], [\"baseLabel\", \"\", 1, \"align-self-start\", \"mr-4\", 2, \"min-width\", \"75px\"], [\"class\", \"flex flex-wrap mt-2\", 4, \"ngIf\"], [\"for\", \"cApproveRemark\", \"baseLabel\", \"\", 1, \"required-field\", \"align-self-start\", \"mr-4\", 2, \"min-width\", \"75px\"], [\"name\", \"remark\", \"id\", \"cApproveRemark\", \"rows\", \"5\", \"nbInput\", \"\", 1, \"w-full\", 2, \"resize\", \"none\", 3, \"ngModelChange\", \"disabled\", \"ngModel\"], [1, \"btn\", \"btn-outline-secondary\", \"m-2\", 3, \"click\"], [\"class\", \"btn btn-success m-2\", 3, \"click\", 4, \"ngIf\"], [1, \"flex\", \"flex-wrap\", \"mt-2\"], [\"class\", \"relative w-24 h-24 mr-2 mb-2 border\", 4, \"ngFor\", \"ngForOf\"], [1, \"relative\", \"w-24\", \"h-24\", \"mr-2\", \"mb-2\", \"border\"], [\"class\", \"w-full h-full object-contain cursor-pointer\", 3, \"src\", 4, \"ngIf\"], [\"class\", \"absolute inset-0 flex items-center justify-center cursor-pointer\", 4, \"ngIf\"], [1, \"absolute\", \"-bottom-4\", \"left-0\", \"w-full\", \"text-xs\", \"truncate\", \"px-1\", \"text-center\"], [1, \"absolute\", \"top-0\", \"right-0\", \"cursor-pointer\", \"bg-white\", \"rounded-full\", 3, \"click\"], [1, \"fa\", \"fa-times\", \"text-red-600\"], [1, \"w-full\", \"h-full\", \"object-contain\", \"cursor-pointer\", 3, \"src\"], [1, \"absolute\", \"inset-0\", \"flex\", \"items-center\", \"justify-center\", \"cursor-pointer\"], [\"class\", \"w-full h-full object-contain cursor-pointer\", 3, \"src\", \"click\", 4, \"ngIf\"], [\"class\", \"absolute inset-0 flex items-center justify-center cursor-pointer\", 3, \"click\", 4, \"ngIf\"], [1, \"w-full\", \"h-full\", \"object-contain\", \"cursor-pointer\", 3, \"click\", \"src\"], [1, \"absolute\", \"inset-0\", \"flex\", \"items-center\", \"justify-center\", \"cursor-pointer\", 3, \"click\"], [1, \"btn\", \"btn-success\", \"m-2\", 3, \"click\"]],\n      template: function CustomerChangePictureComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"nb-card\", 3)(1, \"nb-card-header\");\n          i0.ɵɵtext(2);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(3, \"nb-card-body\")(4, \"h1\", 4);\n          i0.ɵɵtext(5, \"\\u60A8\\u53EF\\u8207\\u6B64\\u4E0A\\u50B3\\u8207\\u8A72\\u6236\\u5225\\u5BA2\\u6236\\u8A0E\\u8AD6\\u7684\\u5BA2\\u6236\\u5716\\u9762\\uFF0C\\u5BE9\\u6838\\u901A\\u904E\\u5F8C\\u5BA2\\u6236\\u5C31\\u53EF\\u4EE5\\u5728\\u524D\\u53F0\\u6AA2\\u8996\\u8A72\\u5716\\u9762\\u3002\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(6, \"div\", 5)(7, \"div\", 6)(8, \"div\", 7);\n          i0.ɵɵtemplate(9, CustomerChangePictureComponent_button_9_Template, 2, 0, \"button\", 8);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(10, \"div\", 9)(11, \"table\", 10)(12, \"thead\")(13, \"tr\", 11)(14, \"th\", 12);\n          i0.ɵɵtext(15, \"\\u8A0E\\u8AD6\\u65E5\\u671F\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(16, \"th\", 12);\n          i0.ɵɵtext(17, \"\\u5716\\u9762\\u540D\\u7A31\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(18, \"th\", 12);\n          i0.ɵɵtext(19, \"\\u4E0A\\u50B3\\u65E5\\u671F\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(20, \"th\", 12);\n          i0.ɵɵtext(21, \"\\u5BE9\\u6838\\u72C0\\u614B\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(22, \"th\", 12);\n          i0.ɵɵtext(23, \"\\u64CD\\u4F5C\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(24, \"tbody\");\n          i0.ɵɵtemplate(25, CustomerChangePictureComponent_tr_25_Template, 11, 5, \"tr\", 13);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(26, \"nb-card-footer\", 14)(27, \"ngb-pagination\", 15);\n          i0.ɵɵtwoWayListener(\"pageChange\", function CustomerChangePictureComponent_Template_ngb_pagination_pageChange_27_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.pageIndex, $event) || (ctx.pageIndex = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵlistener(\"pageChange\", function CustomerChangePictureComponent_Template_ngb_pagination_pageChange_27_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.pageChanged($event));\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(28, \"nb-card-footer\")(29, \"div\", 14)(30, \"button\", 16);\n          i0.ɵɵlistener(\"click\", function CustomerChangePictureComponent_Template_button_click_30_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.goBack());\n          });\n          i0.ɵɵtext(31, \" \\u8FD4\\u56DE\\u4E0A\\u4E00\\u9801 \");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵtemplate(32, CustomerChangePictureComponent_ng_template_32_Template, 32, 17, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor);\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate1(\" \\u6236\\u5225\\u7BA1\\u7406 > \\u5BA2\\u8B8A\\u5716\\u4E0A\\u50B3 > \", ctx.houseTitle, \" \");\n          i0.ɵɵadvance(7);\n          i0.ɵɵproperty(\"ngIf\", ctx.isCreate);\n          i0.ɵɵadvance(16);\n          i0.ɵɵproperty(\"ngForOf\", ctx.listSpecialChange);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtwoWayProperty(\"page\", ctx.pageIndex);\n          i0.ɵɵproperty(\"pageSize\", ctx.pageSize)(\"collectionSize\", ctx.totalRecords);\n        }\n      },\n      dependencies: [i7.NgForOf, i7.NgIf, i9.DefaultValueAccessor, i9.NgControlStatus, i9.NgModel, i2.NbCardComponent, i2.NbCardBodyComponent, i2.NbCardFooterComponent, i2.NbCardHeaderComponent, i2.NbInputDirective, i10.NgbPagination, i11.BaseLabelDirective, i12.Calendar],\n      styles: [\"#icondisplay {\\n  width: 318px;\\n}\\n\\n  [id^=pn_id_] {\\n  z-index: 10;\\n}\\n/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImN1c3RvbWVyLWNoYW5nZS1waWN0dXJlLmNvbXBvbmVudC5zY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUFBO0VBQ0ksWUFBQTtBQUNKOztBQUVBO0VBQ0ksV0FBQTtBQUNKIiwiZmlsZSI6ImN1c3RvbWVyLWNoYW5nZS1waWN0dXJlLmNvbXBvbmVudC5zY3NzIiwic291cmNlc0NvbnRlbnQiOlsiOjpuZy1kZWVwICNpY29uZGlzcGxheSB7XHJcbiAgICB3aWR0aDogMzE4cHg7XHJcbn1cclxuXHJcbjo6bmctZGVlcCBbaWRePVwicG5faWRfXCJdIHtcclxuICAgIHotaW5kZXg6IDEwO1xyXG59XHJcbiJdfQ== */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvcGFnZXMvaG91c2Vob2xkLW1hbmFnZW1lbnQvY3VzdG9tZXItY2hhbmdlLXBpY3R1cmUvY3VzdG9tZXItY2hhbmdlLXBpY3R1cmUuY29tcG9uZW50LnNjc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUE7RUFDSSxZQUFBO0FBQ0o7O0FBRUE7RUFDSSxXQUFBO0FBQ0o7QUFDQSw0ZEFBNGQiLCJzb3VyY2VzQ29udGVudCI6WyI6Om5nLWRlZXAgI2ljb25kaXNwbGF5IHtcclxuICAgIHdpZHRoOiAzMThweDtcclxufVxyXG5cclxuOjpuZy1kZWVwIFtpZF49XCJwbl9pZF9cIl0ge1xyXG4gICAgei1pbmRleDogMTA7XHJcbn1cclxuIl0sInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["moment", "BaseComponent", "EEvent", "i0", "ɵɵelementStart", "ɵɵlistener", "CustomerChangePictureComponent_button_9_Template_button_click_0_listener", "ɵɵrestoreView", "_r2", "ctx_r2", "ɵɵnextContext", "dialogUploadDrawing_r4", "ɵɵreference", "ɵɵresetView", "addNew", "ɵɵtext", "ɵɵelementEnd", "CustomerChangePictureComponent_tr_25_button_10_Template_button_click_0_listener", "_r5", "item_r6", "$implicit", "onEdit", "ɵɵtemplate", "CustomerChangePictureComponent_tr_25_button_10_Template", "ɵɵadvance", "ɵɵtextInterpolate", "formatDate", "CChangeDate", "CDrawingName", "CCreateDT", "CIsApprove", "ɵɵproperty", "isUpdate", "ɵɵelement", "file_r10", "data", "ɵɵsanitizeUrl", "CustomerChangePictureComponent_ng_template_32_div_22_div_1_img_1_Template", "CustomerChangePictureComponent_ng_template_32_div_22_div_1_span_2_Template", "CustomerChangePictureComponent_ng_template_32_div_22_div_1_Template_span_click_5_listener", "i_r11", "_r9", "index", "removeFile", "isImage", "CFileType", "CFileName", "CustomerChangePictureComponent_ng_template_32_div_22_div_1_Template", "imageUrlList", "CustomerChangePictureComponent_ng_template_32_div_23_div_1_img_1_Template_img_click_0_listener", "_r12", "file_r13", "openNewTab", "CFile", "CustomerChangePictureComponent_ng_template_32_div_23_div_1_span_2_Template_span_click_0_listener", "_r14", "CustomerChangePictureComponent_ng_template_32_div_23_div_1_img_1_Template", "CustomerChangePictureComponent_ng_template_32_div_23_div_1_span_2_Template", "isPDFString", "CustomerChangePictureComponent_ng_template_32_div_23_div_1_Template", "SpecialChange", "CFileRes", "CustomerChangePictureComponent_ng_template_32_button_31_Template_button_click_0_listener", "_r16", "ref_r15", "dialogRef", "onSaveSpecialChange", "ɵɵtwoWayListener", "CustomerChangePictureComponent_ng_template_32_Template_p_calendar_ngModelChange_8_listener", "$event", "_r7", "ɵɵtwoWayBindingSet", "formSpecialChange", "CustomerChangePictureComponent_ng_template_32_Template_input_ngModelChange_12_listener", "CustomerChangePictureComponent_ng_template_32_Template_button_click_16_listener", "inputFile_r8", "click", "CustomerChangePictureComponent_ng_template_32_Template_input_change_18_listener", "detectFiles", "CustomerChangePictureComponent_ng_template_32_div_22_Template", "CustomerChangePictureComponent_ng_template_32_div_23_Template", "CustomerChangePictureComponent_ng_template_32_Template_textarea_ngModelChange_27_listener", "CApproveRemark", "CustomerChangePictureComponent_ng_template_32_Template_button_click_29_listener", "onClose", "CustomerChangePictureComponent_ng_template_32_button_31_Template", "ɵɵtextInterpolate3", "house", "CCustomerName", "CHousehold", "CFloor", "ɵɵtwoWayProperty", "isEdit", "CustomerChangePictureComponent", "constructor", "_allow", "dialogService", "valid", "_specialChangeService", "_houseService", "route", "message", "location", "_eventService", "statusOptions", "value", "key", "label", "pageFirst", "pageSize", "pageIndex", "totalRecords", "listPictures", "ngOnInit", "paramMap", "subscribe", "params", "idParam", "get", "id", "buildCaseId", "idParam2", "id2", "houseId", "getListSpecialChange", "getHouseById", "openPdfInNewTab", "window", "open", "apiSpecialChangeGetListSpecialChangePost$Json", "body", "CHouseId", "PageIndex", "PageSize", "res", "TotalItems", "Entries", "StatusCode", "listSpecialChange", "apiHouseGetHouseByIdPost$Json", "CHouseID", "houseTitle", "getSpecialChangeById", "ref", "CSpecialChangeID", "apiSpecialChangeGetSpecialChangeByIdPost$Json", "CBuildCaseID", "SpecialChangeFiles", "Date", "validation", "errorMessages", "length", "showErrorMSGs", "apiSpecialChangeSaveSpecialChangePost$Json", "formatParam", "showSucessMSG", "close", "pageChanged", "newPage", "specialChange", "clear", "required", "format", "deleteDataFields", "array", "item", "result", "removeBase64Prefix", "base64String", "prefixIndex", "indexOf", "substring", "event", "files", "target", "allowedTypes", "fileRegex", "i", "file", "test", "type", "showErrorMSG", "includes", "reader", "FileReader", "onload", "e", "fileType", "startsWith", "push", "CFileBlood", "name", "console", "log", "fileInput", "nativeElement", "readAsDataURL", "str", "toLowerCase", "endsWith", "isPdf", "extension", "splice", "removeImage", "pictureId", "filter", "x", "uploadImage", "renameFile", "blob", "slice", "size", "newFile", "File", "goBack", "action", "payload", "back", "url", "ɵɵdirectiveInject", "i1", "AllowHelper", "i2", "NbDialogService", "i3", "ValidationHelper", "i4", "SpecialChangeService", "HouseService", "i5", "ActivatedRoute", "i6", "MessageService", "i7", "Location", "i8", "EventService", "selectors", "viewQuery", "CustomerChangePictureComponent_Query", "rf", "ctx", "CustomerChangePictureComponent_button_9_Template", "CustomerChangePictureComponent_tr_25_Template", "CustomerChangePictureComponent_Template_ngb_pagination_pageChange_27_listener", "_r1", "CustomerChangePictureComponent_Template_button_click_30_listener", "CustomerChangePictureComponent_ng_template_32_Template", "ɵɵtemplateRefExtractor", "ɵɵtextInterpolate1", "isCreate"], "sources": ["C:\\Users\\<USER>\\Documents\\jeansalechange\\adminFront\\src\\app\\pages\\household-management\\customer-change-picture\\customer-change-picture.component.ts", "C:\\Users\\<USER>\\Documents\\jeansalechange\\adminFront\\src\\app\\pages\\household-management\\customer-change-picture\\customer-change-picture.component.html"], "sourcesContent": ["import { Component, ElementRef, OnInit, ViewChild } from '@angular/core';\r\nimport { Location } from '@angular/common';\r\nimport { NbDialogService } from '@nebular/theme';\r\nimport { ValidationHelper } from 'src/app/shared/helper/validationHelper';\r\nimport { HouseService, SpecialChangeService } from 'src/services/api/services';\r\nimport { TblHouse, SpecialChangeRes } from 'src/services/api/models';\r\nimport { AllowHelper } from 'src/app/shared/helper/allowHelper';\r\nimport { ActivatedRoute } from '@angular/router';\r\nimport { MessageService } from 'src/app/shared/services/message.service';\r\nimport * as moment from 'moment';\r\nimport { BaseComponent } from '../../components/base/baseComponent';\r\nimport { EventService, EEvent } from 'src/app/shared/services/event.service';\r\n\r\nexport interface selectItem {\r\n  label: string,\r\n  value: number,\r\n  key?: string\r\n}\r\n\r\n@Component({\r\n  selector: 'ngx-customer-change-picture',\r\n  templateUrl: './customer-change-picture.component.html',\r\n  styleUrls: ['./customer-change-picture.component.scss'],\r\n})\r\n\r\nexport class CustomerChangePictureComponent extends BaseComponent implements OnInit {\r\n  constructor(\r\n    private _allow: AllowHelper,\r\n    private dialogService: NbDialogService,\r\n    private valid: ValidationHelper,\r\n    private _specialChangeService: SpecialChangeService,\r\n    private _houseService: HouseService,\r\n\r\n    private route: ActivatedRoute,\r\n    private message: MessageService,\r\n    private location: Location,\r\n    private _eventService: EventService\r\n  ) { super(_allow) }\r\n\r\n  @ViewChild('fileInput') fileInput: ElementRef;\r\n  imageUrlList: any[] = [];\r\n  isEdit = false\r\n  statusOptions: selectItem[] = [\r\n    {\r\n      value: 0,\r\n      key: 'allow',\r\n      label: '允許',\r\n    },\r\n    {\r\n      value: 1,\r\n      key: 'not allowed',\r\n      label: '不允許',\r\n    }\r\n  ]\r\n\r\n  override pageFirst = 1;\r\n  override pageSize = 10;\r\n  override pageIndex = 1;\r\n  override totalRecords = 0;\r\n\r\n  selectedBuildCase: selectItem\r\n\r\n  buildCaseId: number\r\n  houseId: number\r\n  house: TblHouse\r\n  houseTitle: string\r\n\r\n  override ngOnInit(): void {\r\n    \r\n    this.route.paramMap.subscribe(params => {\r\n      if (params) {\r\n        const idParam = params.get('id1');\r\n        const id = idParam ? +idParam : 0;\r\n        this.buildCaseId = id\r\n        const idParam2 = params.get('id2');\r\n        const id2 = idParam2 ? +idParam2 : 0;\r\n        this.houseId = id2\r\n        this.getListSpecialChange()\r\n        this.getHouseById()\r\n      }\r\n    });\r\n  }\r\n\r\n\r\n  openPdfInNewTab(data?: any) {\r\n    if (data && data.CFileRes.CFile) window.open(data.CFileRes.CFile, '_blank');\r\n  }\r\n\r\n\r\n  listSpecialChange: any[]\r\n\r\n  getListSpecialChange() {\r\n    this._specialChangeService.apiSpecialChangeGetListSpecialChangePost$Json({ body: {\r\n      CHouseId: this.houseId,\r\n      PageIndex: this.pageIndex,\r\n      PageSize: this.pageSize\r\n    } }).subscribe(res => {\r\n      if (res.TotalItems && res.Entries && res.StatusCode == 0) {\r\n        this.listSpecialChange = res.Entries! ?? []\r\n        this.totalRecords = res.TotalItems;\r\n      }\r\n    })\r\n  }\r\n  \r\n  getHouseById() {\r\n    this._houseService.apiHouseGetHouseByIdPost$Json({ body: {\r\n      CHouseID: this.houseId\r\n    } }).subscribe(res => {\r\n      if (res.TotalItems && res.Entries && res.StatusCode == 0) {\r\n        this.house = res.Entries\r\n        this.houseTitle = `${this.house.CHousehold} ${this.house.CFloor}F`\r\n      }\r\n    })\r\n  }\r\n\r\n  SpecialChange : SpecialChangeRes\r\n  fileUrl : any\r\n  getSpecialChangeById(ref: any, CSpecialChangeID: any) {\r\n    this._specialChangeService.apiSpecialChangeGetSpecialChangeByIdPost$Json({ body: CSpecialChangeID }).subscribe(res => {\r\n      if (res.TotalItems && res.Entries && res.StatusCode == 0) {\r\n        this.SpecialChange = res.Entries\r\n        this.formSpecialChange = {\r\n          CApproveRemark: this.SpecialChange.CApproveRemark,\r\n          CBuildCaseID: this.buildCaseId,\r\n          CDrawingName: this.SpecialChange.CDrawingName,\r\n          CHouseID: this.houseId,\r\n          SpecialChangeFiles: null \r\n        }\r\n        if(this.SpecialChange.CChangeDate) {\r\n          this.formSpecialChange.CChangeDate = new Date(this.SpecialChange.CChangeDate);\r\n        }\r\n        this.dialogService.open(ref)\r\n      }\r\n    })\r\n  }\r\n\r\n  formSpecialChange: any\r\n\r\n  onSaveSpecialChange(ref: any) {\r\n    this.validation()\r\n    if (this.valid.errorMessages.length > 0) {\r\n      this.message.showErrorMSGs(this.valid.errorMessages);\r\n      return\r\n    }\r\n\r\n    this._specialChangeService.apiSpecialChangeSaveSpecialChangePost$Json({ body: this.formatParam() }).subscribe(res => {\r\n      if (res.StatusCode === 0) {\r\n        this.message.showSucessMSG(\"執行成功\");\r\n        this.getListSpecialChange()\r\n        ref.close();\r\n      }\r\n    })\r\n  }\r\n\r\n  pageChanged(newPage: number) {\r\n    this.pageIndex = newPage;\r\n    this.getListSpecialChange();\r\n  }\r\n\r\n\r\n  addNew(ref: any) {\r\n    this.imageUrlList = [];\r\n    this.isEdit = false\r\n    this.formSpecialChange = {\r\n      CApproveRemark: '',\r\n      CBuildCaseID: this.buildCaseId,\r\n      CChangeDate: '',\r\n      CDrawingName: '',\r\n      CHouseID: this.houseId,\r\n      SpecialChangeFiles: null \r\n    }\r\n    this.dialogService.open(ref)\r\n  }\r\n\r\n  onEdit(ref: any, specialChange : any ) {\r\n    this.imageUrlList = [];\r\n    this.isEdit = true\r\n    this.getSpecialChangeById(ref, specialChange.CSpecialChangeID)\r\n  }\r\n\r\n  validation() {\r\n    this.valid.clear();\r\n    this.valid.required('[討論日期]', this.formSpecialChange.CChangeDate)\r\n    this.valid.required('[圖面名稱]', this.formSpecialChange.CDrawingName)\r\n    this.valid.required('[審核說明]', this.formSpecialChange.CApproveRemark)\r\n  }\r\n\r\n  formatDate(CChangeDate: string): string {\r\n    if (CChangeDate) {\r\n      return moment(CChangeDate).format('YYYY-MM-DD');\r\n    }\r\n    return ''\r\n  }\r\n\r\n  deleteDataFields(array: any[]) {\r\n    for (const item of array) {\r\n      delete item.data;\r\n    }\r\n    return array; \r\n  }\r\n\r\n  formatParam() {\r\n    const result = {\r\n      ...this.formSpecialChange,\r\n      SpecialChangeFiles: this.imageUrlList\r\n    }\r\n    this.deleteDataFields(result.SpecialChangeFiles)\r\n\r\n    if (this.formSpecialChange.CChangeDate) {\r\n      result.CChangeDate = this.formatDate(this.formSpecialChange.CChangeDate)\r\n    }\r\n    return result\r\n  }\r\n\r\n  onClose(ref: any) {\r\n    ref.close();\r\n  }\r\n\r\n  removeBase64Prefix(base64String: any) {\r\n    const prefixIndex = base64String.indexOf(\",\");\r\n    if (prefixIndex !== -1) {\r\n      return base64String.substring(prefixIndex + 1);\r\n    }\r\n    return base64String;\r\n  }\r\n\r\n  detectFiles(event: any) {\r\n    const files: FileList = event.target.files;\r\n    if (files && files.length > 0) {   \r\n      const allowedTypes = ['image/jpeg', 'image/jpg', 'application/pdf'];\r\n      const fileRegex = /pdf|jpg|jpeg|png/i;\r\n      for (let i = 0; i < files.length; i++) {\r\n        const file = files[i];\r\n        if (!fileRegex.test(file.type)) {\r\n          this.message.showErrorMSG('檔案格式錯誤，僅限pdf、圖片檔');\r\n        }\r\n        if (allowedTypes.includes(file.type)) {\r\n          const reader = new FileReader();\r\n\r\n          reader.onload = (e: any) => {\r\n            const fileType = file.type.startsWith('image/') ? 2 : 1;\r\n            this.imageUrlList.push({\r\n              data: e.target.result,\r\n              CFileBlood: this.removeBase64Prefix(e.target.result),\r\n              CFileName: file.name,\r\n              CFileType: fileType\r\n            });\r\n\r\n            if (this.imageUrlList.length === files.length) {\r\n              console.log('this.imageUrlList', this.imageUrlList);\r\n              if (this.fileInput) {\r\n                this.fileInput.nativeElement.value = null;\r\n              }\r\n            }\r\n          };\r\n          reader.readAsDataURL(file);\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n\r\n  isPDFString(str: any): boolean {\r\n    if(str) {\r\n      return str.toLowerCase().endsWith(\".pdf\")\r\n    }\r\n    return false\r\n  }\r\n\r\n  isImage(fileType: number): boolean {\r\n    return fileType === 2;\r\n  }\r\n\r\n\r\n  isPdf(extension: string): boolean {\r\n    return extension.toLowerCase() === 'pdf';\r\n  }\r\n\r\n  listPictures: any[] = []\r\n\r\n  removeFile(index: number) {\r\n    this.imageUrlList.splice(index, 1); \r\n  }\r\n\r\n  removeImage(pictureId: number) {\r\n    this.listPictures = this.listPictures.filter(x => x.id != pictureId)\r\n  }\r\n\r\n  uploadImage(ref: any) {\r\n  }\r\n\r\n  renameFile(event: any, index: number) {\r\n    var blob = this.listPictures[index].CFile.slice(0, this.listPictures[index].CFile.size, this.listPictures[index].CFile.type);\r\n    var newFile = new File([blob], `${event.target.value + '.' + this.listPictures[index].extension}`, { type: this.listPictures[index].CFile.type });\r\n    this.listPictures[index].CFile = newFile\r\n  }\r\n\r\n  goBack() {\r\n    this._eventService.push({\r\n      action: EEvent.GET_BUILDCASE,\r\n      payload: this.buildCaseId\r\n    })\r\n    this.location.back()\r\n  }\r\n  openNewTab(url: any) {\r\n    if(url) window.open(url, \"_blank\"); \r\n  }\r\n\r\n}\r\n", "<nb-card accent=\"success\">\r\n  <nb-card-header>\r\n    戶別管理 > 客變圖上傳 > {{houseTitle}}\r\n  </nb-card-header>\r\n  <nb-card-body>\r\n    <h1 class=\"font-bold text-[#818181]\">您可與此上傳與該戶別客戶討論的客戶圖面，審核通過後客戶就可以在前台檢視該圖面。</h1>\r\n\r\n    <div class=\"d-flex flex-wrap\">\r\n\r\n      <div class=\"col-md-12\">\r\n        <div class=\"d-flex justify-content-end w-full\">\r\n          <button class=\"btn btn-info\" (click)=\"addNew(dialogUploadDrawing)\" *ngIf=\"isCreate\">\r\n            上傳圖面</button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <div class=\"table-responsive mt-4\">\r\n      <table class=\"table table-striped border \" style=\"min-width: 1000px; background-color:#f3f3f3;\">\r\n        <thead>\r\n          <tr style=\"background-color: #27ae60; color: white;\" class=\"text-center\">\r\n            <th scope=\"col\" class=\"col-1\">討論日期</th>\r\n            <th scope=\"col\" class=\"col-1\">圖面名稱</th>\r\n            <th scope=\"col\" class=\"col-1\">上傳日期</th>\r\n            <th scope=\"col\" class=\"col-1\">審核狀態</th>\r\n            <th scope=\"col\" class=\"col-1\">操作</th>\r\n          </tr>\r\n        </thead>\r\n        <tbody>\r\n          <tr *ngFor=\"let item of listSpecialChange ; let i = index\" class=\"text-center\">\r\n            <td>{{ formatDate(item.CChangeDate)}}</td>\r\n            <td>{{ item.CDrawingName}}</td>\r\n            <td>{{formatDate(item.CCreateDT)}}</td>\r\n            <td>{{ item.CIsApprove ==  null ? '待審核' : ( item.CIsApprove ? \"通過\" : \"駁回\")}}</td>\r\n            <td class=\"text-center w-32\">\r\n              <button class=\"btn btn-outline-primary btn-sm m-1\" *ngIf=\"isUpdate\"\r\n                (click)=\"onEdit(dialogUploadDrawing, item)\">檢視</button>\r\n            </td>\r\n          </tr>\r\n        </tbody>\r\n      </table>\r\n    </div>\r\n  </nb-card-body>\r\n  <nb-card-footer class=\"d-flex justify-content-center\">\r\n    <ngb-pagination [(page)]=\"pageIndex\" [pageSize]=\"pageSize\" [collectionSize]=\"totalRecords\"\r\n      (pageChange)=\"pageChanged($event)\" aria-label=\"Pagination\">\r\n    </ngb-pagination>\r\n  </nb-card-footer>\r\n  <nb-card-footer>\r\n    <div class=\"d-flex justify-content-center\">\r\n      <button class=\"btn btn-secondary btn-sm\"  (click)=\"goBack()\">\r\n        返回上一頁\r\n      </button>\r\n    </div>\r\n  </nb-card-footer>\r\n</nb-card>\r\n\r\n\r\n<ng-template #dialogUploadDrawing let-dialog let-ref=\"dialogRef\">\r\n  <nb-card style=\"min-width:600px; max-height: 95vh\">\r\n    <nb-card-header>\r\n      戶別管理 > {{house.CCustomerName}} > {{house.CHousehold}} &nbsp; {{house.CFloor}}F\r\n    </nb-card-header>\r\n    <nb-card-body class=\"px-4\">\r\n\r\n      <div class=\"form-group\">\r\n        <label for=\"CChangeDate\" #CChangeDate class=\"required-field mr-4\" style=\"min-width:75px\" baseLabel>\r\n          討論日期\r\n        </label>\r\n        <p-calendar [appendTo]=\"'CChangeDate'\" placeholder=\"年/月/日\" \r\n          [iconDisplay]=\"'input'\" [showIcon]=\"true\" inputId=\"icondisplay\" dateFormat=\"yy/mm/dd\"\r\n          [(ngModel)]=\"formSpecialChange.CChangeDate\" [disabled]=\"isEdit\" [showButtonBar]=\"true\" class=\"!w-[400px]\"></p-calendar>\r\n      </div>\r\n      <div class=\"form-group\">\r\n        <label for=\"cDrawingName\" class=\"required-field mr-4\" style=\"min-width:75px\" baseLabel>\r\n          圖面名稱\r\n        </label>\r\n        <input type=\"text\" class=\"w-full\" nbInput placeholder=\"圖面名稱\" [(ngModel)]=\"formSpecialChange.CDrawingName\"\r\n          [disabled]=\"isEdit\" />\r\n      </div>\r\n      <div class=\"form-group\">\r\n        <label for=\"cIsEnable\" class=\" mr-4\" style=\"min-width:75px\" baseLabel>\r\n          選樣結果\r\n        </label>\r\n        <button class=\"btn btn-info\" (click)=\"inputFile.click()\">選擇檔案</button>\r\n        <input #inputFile type=\"file\" id=\"fileInput\" class=\"hidden\" (change)=\"detectFiles($event)\" [disabled]=\"isEdit\"\r\n          accept=\"image/jpeg, image/jpg, application/pdf\" multiple>\r\n      </div>\r\n\r\n      <div class=\"form-group d-flex align-items-center\">\r\n        <label baseLabel class=\"align-self-start mr-4\" style=\"min-width:75px\">\r\n        </label>\r\n        <div class=\"flex flex-wrap mt-2\" *ngIf=\"!isEdit\">\r\n          <div *ngFor=\"let file of imageUrlList; let i = index\" class=\"relative w-24 h-24 mr-2 mb-2 border\">\r\n            <img *ngIf=\"isImage(file.CFileType)\" class=\"w-full h-full object-contain cursor-pointer\" [src]=\"file.data\" >\r\n            <span *ngIf=\"!isImage(file.CFileType)\" class=\"absolute inset-0 flex items-center justify-center cursor-pointer\">PDF</span>\r\n            <p class=\"absolute -bottom-4 left-0 w-full text-xs truncate px-1 text-center\">{{ file.CFileName }}</p>\r\n            <span class=\"absolute top-0 right-0 cursor-pointer bg-white rounded-full\" (click)=\"removeFile(i)\">\r\n              <i class=\"fa fa-times text-red-600\"></i>\r\n            </span>\r\n          </div>\r\n        </div>\r\n\r\n        <div class=\"flex flex-wrap mt-2\" *ngIf=\"isEdit\">\r\n          <div *ngFor=\"let file of SpecialChange.CFileRes; let i = index\" class=\"relative w-24 h-24 mr-2 mb-2 border\">\r\n            <img *ngIf=\"!isPDFString(file.CFile)\" class=\"w-full h-full object-contain cursor-pointer\" [src]=\"file.CFile\" (click)=\"openNewTab(file.CFile)\">\r\n            <span *ngIf=\"isPDFString(file.CFile)\" class=\"absolute inset-0 flex items-center justify-center cursor-pointer\" (click)=\"openNewTab(file.CFile)\">PDF</span>\r\n            <p class=\"absolute -bottom-4 left-0 w-full text-xs truncate px-1 text-center\">{{ file.CFileName }}</p>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <div class=\"form-group d-flex align-items-center\">\r\n        <label for=\"cApproveRemark\" baseLabel class=\"required-field align-self-start mr-4\"\r\n          style=\"min-width:75px\">審核說明</label>\r\n        <textarea name=\"remark\" id=\"cApproveRemark\" rows=\"5\" nbInput style=\"resize: none;\" class=\"w-full\"\r\n          [disabled]=\"isEdit\" class=\"w-full\" [(ngModel)]=\"formSpecialChange.CApproveRemark\"></textarea>\r\n      </div>\r\n\r\n      <div class=\"d-flex justify-content-center\">\r\n        <button class=\"btn btn-outline-secondary m-2\" (click)=\"onClose(ref)\">取消</button>\r\n        <button class=\"btn btn-success m-2\" *ngIf=\"!isEdit\"\r\n          (click)=\"onSaveSpecialChange(ref)\">送出審核</button>\r\n      </div>\r\n    </nb-card-body>\r\n  </nb-card>\r\n</ng-template>"], "mappings": "AASA,OAAO,KAAKA,MAAM,MAAM,QAAQ;AAChC,SAASC,aAAa,QAAQ,qCAAqC;AACnE,SAAuBC,MAAM,QAAQ,uCAAuC;;;;;;;;;;;;;;;;;;ICAlEC,EAAA,CAAAC,cAAA,iBAAoF;IAAvDD,EAAA,CAAAE,UAAA,mBAAAC,yEAAA;MAAAH,EAAA,CAAAI,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,MAAAC,sBAAA,GAAAR,EAAA,CAAAS,WAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASJ,MAAA,CAAAK,MAAA,CAAAH,sBAAA,CAA2B;IAAA,EAAC;IAChER,EAAA,CAAAY,MAAA,gCAAI;IAAAZ,EAAA,CAAAa,YAAA,EAAS;;;;;;IAuBXb,EAAA,CAAAC,cAAA,iBAC8C;IAA5CD,EAAA,CAAAE,UAAA,mBAAAY,gFAAA;MAAAd,EAAA,CAAAI,aAAA,CAAAW,GAAA;MAAA,MAAAC,OAAA,GAAAhB,EAAA,CAAAO,aAAA,GAAAU,SAAA;MAAA,MAAAX,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,MAAAC,sBAAA,GAAAR,EAAA,CAAAS,WAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASJ,MAAA,CAAAY,MAAA,CAAAV,sBAAA,EAAAQ,OAAA,CAAiC;IAAA,EAAC;IAAChB,EAAA,CAAAY,MAAA,mBAAE;IAAAZ,EAAA,CAAAa,YAAA,EAAS;;;;;IAN3Db,EADF,CAAAC,cAAA,aAA+E,SACzE;IAAAD,EAAA,CAAAY,MAAA,GAAiC;IAAAZ,EAAA,CAAAa,YAAA,EAAK;IAC1Cb,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAY,MAAA,GAAsB;IAAAZ,EAAA,CAAAa,YAAA,EAAK;IAC/Bb,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAY,MAAA,GAA8B;IAAAZ,EAAA,CAAAa,YAAA,EAAK;IACvCb,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAY,MAAA,GAAwE;IAAAZ,EAAA,CAAAa,YAAA,EAAK;IACjFb,EAAA,CAAAC,cAAA,aAA6B;IAC3BD,EAAA,CAAAmB,UAAA,KAAAC,uDAAA,qBAC8C;IAElDpB,EADE,CAAAa,YAAA,EAAK,EACF;;;;;IARCb,EAAA,CAAAqB,SAAA,GAAiC;IAAjCrB,EAAA,CAAAsB,iBAAA,CAAAhB,MAAA,CAAAiB,UAAA,CAAAP,OAAA,CAAAQ,WAAA,EAAiC;IACjCxB,EAAA,CAAAqB,SAAA,GAAsB;IAAtBrB,EAAA,CAAAsB,iBAAA,CAAAN,OAAA,CAAAS,YAAA,CAAsB;IACtBzB,EAAA,CAAAqB,SAAA,GAA8B;IAA9BrB,EAAA,CAAAsB,iBAAA,CAAAhB,MAAA,CAAAiB,UAAA,CAAAP,OAAA,CAAAU,SAAA,EAA8B;IAC9B1B,EAAA,CAAAqB,SAAA,GAAwE;IAAxErB,EAAA,CAAAsB,iBAAA,CAAAN,OAAA,CAAAW,UAAA,kCAAAX,OAAA,CAAAW,UAAA,mCAAwE;IAEtB3B,EAAA,CAAAqB,SAAA,GAAc;IAAdrB,EAAA,CAAA4B,UAAA,SAAAtB,MAAA,CAAAuB,QAAA,CAAc;;;;;IA2DpE7B,EAAA,CAAA8B,SAAA,cAA4G;;;;IAAnB9B,EAAA,CAAA4B,UAAA,QAAAG,QAAA,CAAAC,IAAA,EAAAhC,EAAA,CAAAiC,aAAA,CAAiB;;;;;IAC1GjC,EAAA,CAAAC,cAAA,eAAgH;IAAAD,EAAA,CAAAY,MAAA,UAAG;IAAAZ,EAAA,CAAAa,YAAA,EAAO;;;;;;IAF5Hb,EAAA,CAAAC,cAAA,cAAkG;IAEhGD,EADA,CAAAmB,UAAA,IAAAe,yEAAA,kBAA4G,IAAAC,0EAAA,mBACI;IAChHnC,EAAA,CAAAC,cAAA,YAA8E;IAAAD,EAAA,CAAAY,MAAA,GAAoB;IAAAZ,EAAA,CAAAa,YAAA,EAAI;IACtGb,EAAA,CAAAC,cAAA,eAAkG;IAAxBD,EAAA,CAAAE,UAAA,mBAAAkC,0FAAA;MAAA,MAAAC,KAAA,GAAArC,EAAA,CAAAI,aAAA,CAAAkC,GAAA,EAAAC,KAAA;MAAA,MAAAjC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAU,WAAA,CAASJ,MAAA,CAAAkC,UAAA,CAAAH,KAAA,CAAa;IAAA,EAAC;IAC/FrC,EAAA,CAAA8B,SAAA,YAAwC;IAE5C9B,EADE,CAAAa,YAAA,EAAO,EACH;;;;;IANEb,EAAA,CAAAqB,SAAA,EAA6B;IAA7BrB,EAAA,CAAA4B,UAAA,SAAAtB,MAAA,CAAAmC,OAAA,CAAAV,QAAA,CAAAW,SAAA,EAA6B;IAC5B1C,EAAA,CAAAqB,SAAA,EAA8B;IAA9BrB,EAAA,CAAA4B,UAAA,UAAAtB,MAAA,CAAAmC,OAAA,CAAAV,QAAA,CAAAW,SAAA,EAA8B;IACyC1C,EAAA,CAAAqB,SAAA,GAAoB;IAApBrB,EAAA,CAAAsB,iBAAA,CAAAS,QAAA,CAAAY,SAAA,CAAoB;;;;;IAJtG3C,EAAA,CAAAC,cAAA,cAAiD;IAC/CD,EAAA,CAAAmB,UAAA,IAAAyB,mEAAA,kBAAkG;IAQpG5C,EAAA,CAAAa,YAAA,EAAM;;;;IARkBb,EAAA,CAAAqB,SAAA,EAAiB;IAAjBrB,EAAA,CAAA4B,UAAA,YAAAtB,MAAA,CAAAuC,YAAA,CAAiB;;;;;;IAYrC7C,EAAA,CAAAC,cAAA,cAA8I;IAAjCD,EAAA,CAAAE,UAAA,mBAAA4C,+FAAA;MAAA9C,EAAA,CAAAI,aAAA,CAAA2C,IAAA;MAAA,MAAAC,QAAA,GAAAhD,EAAA,CAAAO,aAAA,GAAAU,SAAA;MAAA,MAAAX,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAU,WAAA,CAASJ,MAAA,CAAA2C,UAAA,CAAAD,QAAA,CAAAE,KAAA,CAAsB;IAAA,EAAC;IAA7IlD,EAAA,CAAAa,YAAA,EAA8I;;;;IAApDb,EAAA,CAAA4B,UAAA,QAAAoB,QAAA,CAAAE,KAAA,EAAAlD,EAAA,CAAAiC,aAAA,CAAkB;;;;;;IAC5GjC,EAAA,CAAAC,cAAA,eAAgJ;IAAjCD,EAAA,CAAAE,UAAA,mBAAAiD,iGAAA;MAAAnD,EAAA,CAAAI,aAAA,CAAAgD,IAAA;MAAA,MAAAJ,QAAA,GAAAhD,EAAA,CAAAO,aAAA,GAAAU,SAAA;MAAA,MAAAX,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAU,WAAA,CAASJ,MAAA,CAAA2C,UAAA,CAAAD,QAAA,CAAAE,KAAA,CAAsB;IAAA,EAAC;IAAClD,EAAA,CAAAY,MAAA,UAAG;IAAAZ,EAAA,CAAAa,YAAA,EAAO;;;;;IAF5Jb,EAAA,CAAAC,cAAA,cAA4G;IAE1GD,EADA,CAAAmB,UAAA,IAAAkC,yEAAA,kBAA8I,IAAAC,0EAAA,mBACE;IAChJtD,EAAA,CAAAC,cAAA,YAA8E;IAAAD,EAAA,CAAAY,MAAA,GAAoB;IACpGZ,EADoG,CAAAa,YAAA,EAAI,EAClG;;;;;IAHEb,EAAA,CAAAqB,SAAA,EAA8B;IAA9BrB,EAAA,CAAA4B,UAAA,UAAAtB,MAAA,CAAAiD,WAAA,CAAAP,QAAA,CAAAE,KAAA,EAA8B;IAC7BlD,EAAA,CAAAqB,SAAA,EAA6B;IAA7BrB,EAAA,CAAA4B,UAAA,SAAAtB,MAAA,CAAAiD,WAAA,CAAAP,QAAA,CAAAE,KAAA,EAA6B;IAC0ClD,EAAA,CAAAqB,SAAA,GAAoB;IAApBrB,EAAA,CAAAsB,iBAAA,CAAA0B,QAAA,CAAAL,SAAA,CAAoB;;;;;IAJtG3C,EAAA,CAAAC,cAAA,cAAgD;IAC9CD,EAAA,CAAAmB,UAAA,IAAAqC,mEAAA,kBAA4G;IAK9GxD,EAAA,CAAAa,YAAA,EAAM;;;;IALkBb,EAAA,CAAAqB,SAAA,EAA2B;IAA3BrB,EAAA,CAAA4B,UAAA,YAAAtB,MAAA,CAAAmD,aAAA,CAAAC,QAAA,CAA2B;;;;;;IAgBnD1D,EAAA,CAAAC,cAAA,iBACqC;IAAnCD,EAAA,CAAAE,UAAA,mBAAAyD,yFAAA;MAAA3D,EAAA,CAAAI,aAAA,CAAAwD,IAAA;MAAA,MAAAC,OAAA,GAAA7D,EAAA,CAAAO,aAAA,GAAAuD,SAAA;MAAA,MAAAxD,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAU,WAAA,CAASJ,MAAA,CAAAyD,mBAAA,CAAAF,OAAA,CAAwB;IAAA,EAAC;IAAC7D,EAAA,CAAAY,MAAA,+BAAI;IAAAZ,EAAA,CAAAa,YAAA,EAAS;;;;;;IA7DtDb,EADF,CAAAC,cAAA,kBAAmD,qBACjC;IACdD,EAAA,CAAAY,MAAA,GACF;IAAAZ,EAAA,CAAAa,YAAA,EAAiB;IAIbb,EAHJ,CAAAC,cAAA,uBAA2B,cAED,mBAC6E;IACjGD,EAAA,CAAAY,MAAA,iCACF;IAAAZ,EAAA,CAAAa,YAAA,EAAQ;IACRb,EAAA,CAAAC,cAAA,qBAE4G;IAA1GD,EAAA,CAAAgE,gBAAA,2BAAAC,2FAAAC,MAAA;MAAAlE,EAAA,CAAAI,aAAA,CAAA+D,GAAA;MAAA,MAAA7D,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAAP,EAAA,CAAAoE,kBAAA,CAAA9D,MAAA,CAAA+D,iBAAA,CAAA7C,WAAA,EAAA0C,MAAA,MAAA5D,MAAA,CAAA+D,iBAAA,CAAA7C,WAAA,GAAA0C,MAAA;MAAA,OAAAlE,EAAA,CAAAU,WAAA,CAAAwD,MAAA;IAAA,EAA2C;IAC/ClE,EAD8G,CAAAa,YAAA,EAAa,EACrH;IAEJb,EADF,CAAAC,cAAA,cAAwB,iBACiE;IACrFD,EAAA,CAAAY,MAAA,kCACF;IAAAZ,EAAA,CAAAa,YAAA,EAAQ;IACRb,EAAA,CAAAC,cAAA,iBACwB;IADqCD,EAAA,CAAAgE,gBAAA,2BAAAM,uFAAAJ,MAAA;MAAAlE,EAAA,CAAAI,aAAA,CAAA+D,GAAA;MAAA,MAAA7D,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAAP,EAAA,CAAAoE,kBAAA,CAAA9D,MAAA,CAAA+D,iBAAA,CAAA5C,YAAA,EAAAyC,MAAA,MAAA5D,MAAA,CAAA+D,iBAAA,CAAA5C,YAAA,GAAAyC,MAAA;MAAA,OAAAlE,EAAA,CAAAU,WAAA,CAAAwD,MAAA;IAAA,EAA4C;IAE3GlE,EAFE,CAAAa,YAAA,EACwB,EACpB;IAEJb,EADF,CAAAC,cAAA,eAAwB,iBACgD;IACpED,EAAA,CAAAY,MAAA,kCACF;IAAAZ,EAAA,CAAAa,YAAA,EAAQ;IACRb,EAAA,CAAAC,cAAA,kBAAyD;IAA5BD,EAAA,CAAAE,UAAA,mBAAAqE,gFAAA;MAAAvE,EAAA,CAAAI,aAAA,CAAA+D,GAAA;MAAA,MAAAK,YAAA,GAAAxE,EAAA,CAAAS,WAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAAS8D,YAAA,CAAAC,KAAA,EAAiB;IAAA,EAAC;IAACzE,EAAA,CAAAY,MAAA,gCAAI;IAAAZ,EAAA,CAAAa,YAAA,EAAS;IACtEb,EAAA,CAAAC,cAAA,oBAC2D;IADCD,EAAA,CAAAE,UAAA,oBAAAwE,gFAAAR,MAAA;MAAAlE,EAAA,CAAAI,aAAA,CAAA+D,GAAA;MAAA,MAAA7D,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAU,WAAA,CAAUJ,MAAA,CAAAqE,WAAA,CAAAT,MAAA,CAAmB;IAAA,EAAC;IAE5FlE,EAFE,CAAAa,YAAA,EAC2D,EACvD;IAENb,EAAA,CAAAC,cAAA,eAAkD;IAChDD,EAAA,CAAA8B,SAAA,iBACQ;IAYR9B,EAXA,CAAAmB,UAAA,KAAAyD,6DAAA,kBAAiD,KAAAC,6DAAA,kBAWD;IAOlD7E,EAAA,CAAAa,YAAA,EAAM;IAEJb,EADF,CAAAC,cAAA,eAAkD,iBAEvB;IAAAD,EAAA,CAAAY,MAAA,gCAAI;IAAAZ,EAAA,CAAAa,YAAA,EAAQ;IACrCb,EAAA,CAAAC,cAAA,oBACoF;IAA/CD,EAAA,CAAAgE,gBAAA,2BAAAc,0FAAAZ,MAAA;MAAAlE,EAAA,CAAAI,aAAA,CAAA+D,GAAA;MAAA,MAAA7D,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAAP,EAAA,CAAAoE,kBAAA,CAAA9D,MAAA,CAAA+D,iBAAA,CAAAU,cAAA,EAAAb,MAAA,MAAA5D,MAAA,CAAA+D,iBAAA,CAAAU,cAAA,GAAAb,MAAA;MAAA,OAAAlE,EAAA,CAAAU,WAAA,CAAAwD,MAAA;IAAA,EAA8C;IACrFlE,EADsF,CAAAa,YAAA,EAAW,EAC3F;IAGJb,EADF,CAAAC,cAAA,eAA2C,kBAC4B;IAAvBD,EAAA,CAAAE,UAAA,mBAAA8E,gFAAA;MAAA,MAAAnB,OAAA,GAAA7D,EAAA,CAAAI,aAAA,CAAA+D,GAAA,EAAAL,SAAA;MAAA,MAAAxD,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAU,WAAA,CAASJ,MAAA,CAAA2E,OAAA,CAAApB,OAAA,CAAY;IAAA,EAAC;IAAC7D,EAAA,CAAAY,MAAA,oBAAE;IAAAZ,EAAA,CAAAa,YAAA,EAAS;IAChFb,EAAA,CAAAmB,UAAA,KAAA+D,gEAAA,qBACqC;IAG3ClF,EAFI,CAAAa,YAAA,EAAM,EACO,EACP;;;;IA/DNb,EAAA,CAAAqB,SAAA,GACF;IADErB,EAAA,CAAAmF,kBAAA,iCAAA7E,MAAA,CAAA8E,KAAA,CAAAC,aAAA,SAAA/E,MAAA,CAAA8E,KAAA,CAAAE,UAAA,cAAAhF,MAAA,CAAA8E,KAAA,CAAAG,MAAA,OACF;IAOgBvF,EAAA,CAAAqB,SAAA,GAA0B;IACZrB,EADd,CAAA4B,UAAA,2BAA0B,wBACb,kBAAkB;IACzC5B,EAAA,CAAAwF,gBAAA,YAAAlF,MAAA,CAAA+D,iBAAA,CAAA7C,WAAA,CAA2C;IAAqBxB,EAApB,CAAA4B,UAAA,aAAAtB,MAAA,CAAAmF,MAAA,CAAmB,uBAAuB;IAM3BzF,EAAA,CAAAqB,SAAA,GAA4C;IAA5CrB,EAAA,CAAAwF,gBAAA,YAAAlF,MAAA,CAAA+D,iBAAA,CAAA5C,YAAA,CAA4C;IACvGzB,EAAA,CAAA4B,UAAA,aAAAtB,MAAA,CAAAmF,MAAA,CAAmB;IAOsEzF,EAAA,CAAAqB,SAAA,GAAmB;IAAnBrB,EAAA,CAAA4B,UAAA,aAAAtB,MAAA,CAAAmF,MAAA,CAAmB;IAO5EzF,EAAA,CAAAqB,SAAA,GAAa;IAAbrB,EAAA,CAAA4B,UAAA,UAAAtB,MAAA,CAAAmF,MAAA,CAAa;IAWbzF,EAAA,CAAAqB,SAAA,EAAY;IAAZrB,EAAA,CAAA4B,UAAA,SAAAtB,MAAA,CAAAmF,MAAA,CAAY;IAY5CzF,EAAA,CAAAqB,SAAA,GAAmB;IAAnBrB,EAAA,CAAA4B,UAAA,aAAAtB,MAAA,CAAAmF,MAAA,CAAmB;IAAgBzF,EAAA,CAAAwF,gBAAA,YAAAlF,MAAA,CAAA+D,iBAAA,CAAAU,cAAA,CAA8C;IAK9C/E,EAAA,CAAAqB,SAAA,GAAa;IAAbrB,EAAA,CAAA4B,UAAA,UAAAtB,MAAA,CAAAmF,MAAA,CAAa;;;AD/F1D,OAAM,MAAOC,8BAA+B,SAAQ5F,aAAa;EAC/D6F,YACUC,MAAmB,EACnBC,aAA8B,EAC9BC,KAAuB,EACvBC,qBAA2C,EAC3CC,aAA2B,EAE3BC,KAAqB,EACrBC,OAAuB,EACvBC,QAAkB,EAClBC,aAA2B;IACjC,KAAK,CAACR,MAAM,CAAC;IAVP,KAAAA,MAAM,GAANA,MAAM;IACN,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,qBAAqB,GAArBA,qBAAqB;IACrB,KAAAC,aAAa,GAAbA,aAAa;IAEb,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAC,QAAQ,GAARA,QAAQ;IACR,KAAAC,aAAa,GAAbA,aAAa;IAIvB,KAAAvD,YAAY,GAAU,EAAE;IACxB,KAAA4C,MAAM,GAAG,KAAK;IACd,KAAAY,aAAa,GAAiB,CAC5B;MACEC,KAAK,EAAE,CAAC;MACRC,GAAG,EAAE,OAAO;MACZC,KAAK,EAAE;KACR,EACD;MACEF,KAAK,EAAE,CAAC;MACRC,GAAG,EAAE,aAAa;MAClBC,KAAK,EAAE;KACR,CACF;IAEQ,KAAAC,SAAS,GAAG,CAAC;IACb,KAAAC,QAAQ,GAAG,EAAE;IACb,KAAAC,SAAS,GAAG,CAAC;IACb,KAAAC,YAAY,GAAG,CAAC;IA4NzB,KAAAC,YAAY,GAAU,EAAE;EAjPN;EA8BTC,QAAQA,CAAA;IAEf,IAAI,CAACb,KAAK,CAACc,QAAQ,CAACC,SAAS,CAACC,MAAM,IAAG;MACrC,IAAIA,MAAM,EAAE;QACV,MAAMC,OAAO,GAAGD,MAAM,CAACE,GAAG,CAAC,KAAK,CAAC;QACjC,MAAMC,EAAE,GAAGF,OAAO,GAAG,CAACA,OAAO,GAAG,CAAC;QACjC,IAAI,CAACG,WAAW,GAAGD,EAAE;QACrB,MAAME,QAAQ,GAAGL,MAAM,CAACE,GAAG,CAAC,KAAK,CAAC;QAClC,MAAMI,GAAG,GAAGD,QAAQ,GAAG,CAACA,QAAQ,GAAG,CAAC;QACpC,IAAI,CAACE,OAAO,GAAGD,GAAG;QAClB,IAAI,CAACE,oBAAoB,EAAE;QAC3B,IAAI,CAACC,YAAY,EAAE;MACrB;IACF,CAAC,CAAC;EACJ;EAGAC,eAAeA,CAAC3F,IAAU;IACxB,IAAIA,IAAI,IAAIA,IAAI,CAAC0B,QAAQ,CAACR,KAAK,EAAE0E,MAAM,CAACC,IAAI,CAAC7F,IAAI,CAAC0B,QAAQ,CAACR,KAAK,EAAE,QAAQ,CAAC;EAC7E;EAKAuE,oBAAoBA,CAAA;IAClB,IAAI,CAAC1B,qBAAqB,CAAC+B,6CAA6C,CAAC;MAAEC,IAAI,EAAE;QAC/EC,QAAQ,EAAE,IAAI,CAACR,OAAO;QACtBS,SAAS,EAAE,IAAI,CAACtB,SAAS;QACzBuB,QAAQ,EAAE,IAAI,CAACxB;;IAChB,CAAE,CAAC,CAACM,SAAS,CAACmB,GAAG,IAAG;MACnB,IAAIA,GAAG,CAACC,UAAU,IAAID,GAAG,CAACE,OAAO,IAAIF,GAAG,CAACG,UAAU,IAAI,CAAC,EAAE;QACxD,IAAI,CAACC,iBAAiB,GAAGJ,GAAG,CAACE,OAAQ,IAAI,EAAE;QAC3C,IAAI,CAACzB,YAAY,GAAGuB,GAAG,CAACC,UAAU;MACpC;IACF,CAAC,CAAC;EACJ;EAEAV,YAAYA,CAAA;IACV,IAAI,CAAC1B,aAAa,CAACwC,6BAA6B,CAAC;MAAET,IAAI,EAAE;QACvDU,QAAQ,EAAE,IAAI,CAACjB;;IAChB,CAAE,CAAC,CAACR,SAAS,CAACmB,GAAG,IAAG;MACnB,IAAIA,GAAG,CAACC,UAAU,IAAID,GAAG,CAACE,OAAO,IAAIF,GAAG,CAACG,UAAU,IAAI,CAAC,EAAE;QACxD,IAAI,CAAClD,KAAK,GAAG+C,GAAG,CAACE,OAAO;QACxB,IAAI,CAACK,UAAU,GAAG,GAAG,IAAI,CAACtD,KAAK,CAACE,UAAU,IAAI,IAAI,CAACF,KAAK,CAACG,MAAM,GAAG;MACpE;IACF,CAAC,CAAC;EACJ;EAIAoD,oBAAoBA,CAACC,GAAQ,EAAEC,gBAAqB;IAClD,IAAI,CAAC9C,qBAAqB,CAAC+C,6CAA6C,CAAC;MAAEf,IAAI,EAAEc;IAAgB,CAAE,CAAC,CAAC7B,SAAS,CAACmB,GAAG,IAAG;MACnH,IAAIA,GAAG,CAACC,UAAU,IAAID,GAAG,CAACE,OAAO,IAAIF,GAAG,CAACG,UAAU,IAAI,CAAC,EAAE;QACxD,IAAI,CAAC7E,aAAa,GAAG0E,GAAG,CAACE,OAAO;QAChC,IAAI,CAAChE,iBAAiB,GAAG;UACvBU,cAAc,EAAE,IAAI,CAACtB,aAAa,CAACsB,cAAc;UACjDgE,YAAY,EAAE,IAAI,CAAC1B,WAAW;UAC9B5F,YAAY,EAAE,IAAI,CAACgC,aAAa,CAAChC,YAAY;UAC7CgH,QAAQ,EAAE,IAAI,CAACjB,OAAO;UACtBwB,kBAAkB,EAAE;SACrB;QACD,IAAG,IAAI,CAACvF,aAAa,CAACjC,WAAW,EAAE;UACjC,IAAI,CAAC6C,iBAAiB,CAAC7C,WAAW,GAAG,IAAIyH,IAAI,CAAC,IAAI,CAACxF,aAAa,CAACjC,WAAW,CAAC;QAC/E;QACA,IAAI,CAACqE,aAAa,CAACgC,IAAI,CAACe,GAAG,CAAC;MAC9B;IACF,CAAC,CAAC;EACJ;EAIA7E,mBAAmBA,CAAC6E,GAAQ;IAC1B,IAAI,CAACM,UAAU,EAAE;IACjB,IAAI,IAAI,CAACpD,KAAK,CAACqD,aAAa,CAACC,MAAM,GAAG,CAAC,EAAE;MACvC,IAAI,CAAClD,OAAO,CAACmD,aAAa,CAAC,IAAI,CAACvD,KAAK,CAACqD,aAAa,CAAC;MACpD;IACF;IAEA,IAAI,CAACpD,qBAAqB,CAACuD,0CAA0C,CAAC;MAAEvB,IAAI,EAAE,IAAI,CAACwB,WAAW;IAAE,CAAE,CAAC,CAACvC,SAAS,CAACmB,GAAG,IAAG;MAClH,IAAIA,GAAG,CAACG,UAAU,KAAK,CAAC,EAAE;QACxB,IAAI,CAACpC,OAAO,CAACsD,aAAa,CAAC,MAAM,CAAC;QAClC,IAAI,CAAC/B,oBAAoB,EAAE;QAC3BmB,GAAG,CAACa,KAAK,EAAE;MACb;IACF,CAAC,CAAC;EACJ;EAEAC,WAAWA,CAACC,OAAe;IACzB,IAAI,CAAChD,SAAS,GAAGgD,OAAO;IACxB,IAAI,CAAClC,oBAAoB,EAAE;EAC7B;EAGA9G,MAAMA,CAACiI,GAAQ;IACb,IAAI,CAAC/F,YAAY,GAAG,EAAE;IACtB,IAAI,CAAC4C,MAAM,GAAG,KAAK;IACnB,IAAI,CAACpB,iBAAiB,GAAG;MACvBU,cAAc,EAAE,EAAE;MAClBgE,YAAY,EAAE,IAAI,CAAC1B,WAAW;MAC9B7F,WAAW,EAAE,EAAE;MACfC,YAAY,EAAE,EAAE;MAChBgH,QAAQ,EAAE,IAAI,CAACjB,OAAO;MACtBwB,kBAAkB,EAAE;KACrB;IACD,IAAI,CAACnD,aAAa,CAACgC,IAAI,CAACe,GAAG,CAAC;EAC9B;EAEA1H,MAAMA,CAAC0H,GAAQ,EAAEgB,aAAmB;IAClC,IAAI,CAAC/G,YAAY,GAAG,EAAE;IACtB,IAAI,CAAC4C,MAAM,GAAG,IAAI;IAClB,IAAI,CAACkD,oBAAoB,CAACC,GAAG,EAAEgB,aAAa,CAACf,gBAAgB,CAAC;EAChE;EAEAK,UAAUA,CAAA;IACR,IAAI,CAACpD,KAAK,CAAC+D,KAAK,EAAE;IAClB,IAAI,CAAC/D,KAAK,CAACgE,QAAQ,CAAC,QAAQ,EAAE,IAAI,CAACzF,iBAAiB,CAAC7C,WAAW,CAAC;IACjE,IAAI,CAACsE,KAAK,CAACgE,QAAQ,CAAC,QAAQ,EAAE,IAAI,CAACzF,iBAAiB,CAAC5C,YAAY,CAAC;IAClE,IAAI,CAACqE,KAAK,CAACgE,QAAQ,CAAC,QAAQ,EAAE,IAAI,CAACzF,iBAAiB,CAACU,cAAc,CAAC;EACtE;EAEAxD,UAAUA,CAACC,WAAmB;IAC5B,IAAIA,WAAW,EAAE;MACf,OAAO3B,MAAM,CAAC2B,WAAW,CAAC,CAACuI,MAAM,CAAC,YAAY,CAAC;IACjD;IACA,OAAO,EAAE;EACX;EAEAC,gBAAgBA,CAACC,KAAY;IAC3B,KAAK,MAAMC,IAAI,IAAID,KAAK,EAAE;MACxB,OAAOC,IAAI,CAAClI,IAAI;IAClB;IACA,OAAOiI,KAAK;EACd;EAEAV,WAAWA,CAAA;IACT,MAAMY,MAAM,GAAG;MACb,GAAG,IAAI,CAAC9F,iBAAiB;MACzB2E,kBAAkB,EAAE,IAAI,CAACnG;KAC1B;IACD,IAAI,CAACmH,gBAAgB,CAACG,MAAM,CAACnB,kBAAkB,CAAC;IAEhD,IAAI,IAAI,CAAC3E,iBAAiB,CAAC7C,WAAW,EAAE;MACtC2I,MAAM,CAAC3I,WAAW,GAAG,IAAI,CAACD,UAAU,CAAC,IAAI,CAAC8C,iBAAiB,CAAC7C,WAAW,CAAC;IAC1E;IACA,OAAO2I,MAAM;EACf;EAEAlF,OAAOA,CAAC2D,GAAQ;IACdA,GAAG,CAACa,KAAK,EAAE;EACb;EAEAW,kBAAkBA,CAACC,YAAiB;IAClC,MAAMC,WAAW,GAAGD,YAAY,CAACE,OAAO,CAAC,GAAG,CAAC;IAC7C,IAAID,WAAW,KAAK,CAAC,CAAC,EAAE;MACtB,OAAOD,YAAY,CAACG,SAAS,CAACF,WAAW,GAAG,CAAC,CAAC;IAChD;IACA,OAAOD,YAAY;EACrB;EAEA1F,WAAWA,CAAC8F,KAAU;IACpB,MAAMC,KAAK,GAAaD,KAAK,CAACE,MAAM,CAACD,KAAK;IAC1C,IAAIA,KAAK,IAAIA,KAAK,CAACtB,MAAM,GAAG,CAAC,EAAE;MAC7B,MAAMwB,YAAY,GAAG,CAAC,YAAY,EAAE,WAAW,EAAE,iBAAiB,CAAC;MACnE,MAAMC,SAAS,GAAG,mBAAmB;MACrC,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,KAAK,CAACtB,MAAM,EAAE0B,CAAC,EAAE,EAAE;QACrC,MAAMC,IAAI,GAAGL,KAAK,CAACI,CAAC,CAAC;QACrB,IAAI,CAACD,SAAS,CAACG,IAAI,CAACD,IAAI,CAACE,IAAI,CAAC,EAAE;UAC9B,IAAI,CAAC/E,OAAO,CAACgF,YAAY,CAAC,kBAAkB,CAAC;QAC/C;QACA,IAAIN,YAAY,CAACO,QAAQ,CAACJ,IAAI,CAACE,IAAI,CAAC,EAAE;UACpC,MAAMG,MAAM,GAAG,IAAIC,UAAU,EAAE;UAE/BD,MAAM,CAACE,MAAM,GAAIC,CAAM,IAAI;YACzB,MAAMC,QAAQ,GAAGT,IAAI,CAACE,IAAI,CAACQ,UAAU,CAAC,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC;YACvD,IAAI,CAAC5I,YAAY,CAAC6I,IAAI,CAAC;cACrB1J,IAAI,EAAEuJ,CAAC,CAACZ,MAAM,CAACR,MAAM;cACrBwB,UAAU,EAAE,IAAI,CAACvB,kBAAkB,CAACmB,CAAC,CAACZ,MAAM,CAACR,MAAM,CAAC;cACpDxH,SAAS,EAAEoI,IAAI,CAACa,IAAI;cACpBlJ,SAAS,EAAE8I;aACZ,CAAC;YAEF,IAAI,IAAI,CAAC3I,YAAY,CAACuG,MAAM,KAAKsB,KAAK,CAACtB,MAAM,EAAE;cAC7CyC,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAE,IAAI,CAACjJ,YAAY,CAAC;cACnD,IAAI,IAAI,CAACkJ,SAAS,EAAE;gBAClB,IAAI,CAACA,SAAS,CAACC,aAAa,CAAC1F,KAAK,GAAG,IAAI;cAC3C;YACF;UACF,CAAC;UACD8E,MAAM,CAACa,aAAa,CAAClB,IAAI,CAAC;QAC5B;MACF;IACF;EACF;EAGAxH,WAAWA,CAAC2I,GAAQ;IAClB,IAAGA,GAAG,EAAE;MACN,OAAOA,GAAG,CAACC,WAAW,EAAE,CAACC,QAAQ,CAAC,MAAM,CAAC;IAC3C;IACA,OAAO,KAAK;EACd;EAEA3J,OAAOA,CAAC+I,QAAgB;IACtB,OAAOA,QAAQ,KAAK,CAAC;EACvB;EAGAa,KAAKA,CAACC,SAAiB;IACrB,OAAOA,SAAS,CAACH,WAAW,EAAE,KAAK,KAAK;EAC1C;EAIA3J,UAAUA,CAACD,KAAa;IACtB,IAAI,CAACM,YAAY,CAAC0J,MAAM,CAAChK,KAAK,EAAE,CAAC,CAAC;EACpC;EAEAiK,WAAWA,CAACC,SAAiB;IAC3B,IAAI,CAAC5F,YAAY,GAAG,IAAI,CAACA,YAAY,CAAC6F,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACvF,EAAE,IAAIqF,SAAS,CAAC;EACtE;EAEAG,WAAWA,CAAChE,GAAQ,GACpB;EAEAiE,UAAUA,CAACpC,KAAU,EAAElI,KAAa;IAClC,IAAIuK,IAAI,GAAG,IAAI,CAACjG,YAAY,CAACtE,KAAK,CAAC,CAACW,KAAK,CAAC6J,KAAK,CAAC,CAAC,EAAE,IAAI,CAAClG,YAAY,CAACtE,KAAK,CAAC,CAACW,KAAK,CAAC8J,IAAI,EAAE,IAAI,CAACnG,YAAY,CAACtE,KAAK,CAAC,CAACW,KAAK,CAAC+H,IAAI,CAAC;IAC5H,IAAIgC,OAAO,GAAG,IAAIC,IAAI,CAAC,CAACJ,IAAI,CAAC,EAAE,GAAGrC,KAAK,CAACE,MAAM,CAACrE,KAAK,GAAG,GAAG,GAAG,IAAI,CAACO,YAAY,CAACtE,KAAK,CAAC,CAAC+J,SAAS,EAAE,EAAE;MAAErB,IAAI,EAAE,IAAI,CAACpE,YAAY,CAACtE,KAAK,CAAC,CAACW,KAAK,CAAC+H;IAAI,CAAE,CAAC;IACjJ,IAAI,CAACpE,YAAY,CAACtE,KAAK,CAAC,CAACW,KAAK,GAAG+J,OAAO;EAC1C;EAEAE,MAAMA,CAAA;IACJ,IAAI,CAAC/G,aAAa,CAACsF,IAAI,CAAC;MACtB0B,MAAM;MACNC,OAAO,EAAE,IAAI,CAAChG;KACf,CAAC;IACF,IAAI,CAAClB,QAAQ,CAACmH,IAAI,EAAE;EACtB;EACArK,UAAUA,CAACsK,GAAQ;IACjB,IAAGA,GAAG,EAAE3F,MAAM,CAACC,IAAI,CAAC0F,GAAG,EAAE,QAAQ,CAAC;EACpC;;;uCAzRW7H,8BAA8B,EAAA1F,EAAA,CAAAwN,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAA1N,EAAA,CAAAwN,iBAAA,CAAAG,EAAA,CAAAC,eAAA,GAAA5N,EAAA,CAAAwN,iBAAA,CAAAK,EAAA,CAAAC,gBAAA,GAAA9N,EAAA,CAAAwN,iBAAA,CAAAO,EAAA,CAAAC,oBAAA,GAAAhO,EAAA,CAAAwN,iBAAA,CAAAO,EAAA,CAAAE,YAAA,GAAAjO,EAAA,CAAAwN,iBAAA,CAAAU,EAAA,CAAAC,cAAA,GAAAnO,EAAA,CAAAwN,iBAAA,CAAAY,EAAA,CAAAC,cAAA,GAAArO,EAAA,CAAAwN,iBAAA,CAAAc,EAAA,CAAAC,QAAA,GAAAvO,EAAA,CAAAwN,iBAAA,CAAAgB,EAAA,CAAAC,YAAA;IAAA;EAAA;;;YAA9B/I,8BAA8B;MAAAgJ,SAAA;MAAAC,SAAA,WAAAC,qCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;;;;;;;;;;;;;;UCxBzC7O,EADF,CAAAC,cAAA,iBAA0B,qBACR;UACdD,EAAA,CAAAY,MAAA,GACF;UAAAZ,EAAA,CAAAa,YAAA,EAAiB;UAEfb,EADF,CAAAC,cAAA,mBAAc,YACyB;UAAAD,EAAA,CAAAY,MAAA,iPAAuC;UAAAZ,EAAA,CAAAa,YAAA,EAAK;UAK7Eb,EAHJ,CAAAC,cAAA,aAA8B,aAEL,aAC0B;UAC7CD,EAAA,CAAAmB,UAAA,IAAA4N,gDAAA,oBAAoF;UAI1F/O,EAFI,CAAAa,YAAA,EAAM,EACF,EACF;UAMEb,EAJR,CAAAC,cAAA,cAAmC,iBAC+D,aACvF,cACoE,cACzC;UAAAD,EAAA,CAAAY,MAAA,gCAAI;UAAAZ,EAAA,CAAAa,YAAA,EAAK;UACvCb,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAY,MAAA,gCAAI;UAAAZ,EAAA,CAAAa,YAAA,EAAK;UACvCb,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAY,MAAA,gCAAI;UAAAZ,EAAA,CAAAa,YAAA,EAAK;UACvCb,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAY,MAAA,gCAAI;UAAAZ,EAAA,CAAAa,YAAA,EAAK;UACvCb,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAY,MAAA,oBAAE;UAEpCZ,EAFoC,CAAAa,YAAA,EAAK,EAClC,EACC;UACRb,EAAA,CAAAC,cAAA,aAAO;UACLD,EAAA,CAAAmB,UAAA,KAAA6N,6CAAA,kBAA+E;UAavFhP,EAHM,CAAAa,YAAA,EAAQ,EACF,EACJ,EACO;UAEbb,EADF,CAAAC,cAAA,0BAAsD,0BAES;UAD7CD,EAAA,CAAAgE,gBAAA,wBAAAiL,8EAAA/K,MAAA;YAAAlE,EAAA,CAAAI,aAAA,CAAA8O,GAAA;YAAAlP,EAAA,CAAAoE,kBAAA,CAAA0K,GAAA,CAAAnI,SAAA,EAAAzC,MAAA,MAAA4K,GAAA,CAAAnI,SAAA,GAAAzC,MAAA;YAAA,OAAAlE,EAAA,CAAAU,WAAA,CAAAwD,MAAA;UAAA,EAAoB;UAClClE,EAAA,CAAAE,UAAA,wBAAA+O,8EAAA/K,MAAA;YAAAlE,EAAA,CAAAI,aAAA,CAAA8O,GAAA;YAAA,OAAAlP,EAAA,CAAAU,WAAA,CAAcoO,GAAA,CAAApF,WAAA,CAAAxF,MAAA,CAAmB;UAAA,EAAC;UAEtClE,EADE,CAAAa,YAAA,EAAiB,EACF;UAGbb,EAFJ,CAAAC,cAAA,sBAAgB,eAC6B,kBACoB;UAAnBD,EAAA,CAAAE,UAAA,mBAAAiP,iEAAA;YAAAnP,EAAA,CAAAI,aAAA,CAAA8O,GAAA;YAAA,OAAAlP,EAAA,CAAAU,WAAA,CAASoO,GAAA,CAAA3B,MAAA,EAAQ;UAAA,EAAC;UAC1DnN,EAAA,CAAAY,MAAA,wCACF;UAGNZ,EAHM,CAAAa,YAAA,EAAS,EACL,EACS,EACT;UAGVb,EAAA,CAAAmB,UAAA,KAAAiO,sDAAA,kCAAApP,EAAA,CAAAqP,sBAAA,CAAiE;;;UAxD7DrP,EAAA,CAAAqB,SAAA,GACF;UADErB,EAAA,CAAAsP,kBAAA,kEAAAR,GAAA,CAAApG,UAAA,MACF;UAQ4E1I,EAAA,CAAAqB,SAAA,GAAc;UAAdrB,EAAA,CAAA4B,UAAA,SAAAkN,GAAA,CAAAS,QAAA,CAAc;UAkB7DvP,EAAA,CAAAqB,SAAA,IAAuB;UAAvBrB,EAAA,CAAA4B,UAAA,YAAAkN,GAAA,CAAAvG,iBAAA,CAAuB;UAelCvI,EAAA,CAAAqB,SAAA,GAAoB;UAApBrB,EAAA,CAAAwF,gBAAA,SAAAsJ,GAAA,CAAAnI,SAAA,CAAoB;UAAuB3G,EAAtB,CAAA4B,UAAA,aAAAkN,GAAA,CAAApI,QAAA,CAAqB,mBAAAoI,GAAA,CAAAlI,YAAA,CAAgC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}