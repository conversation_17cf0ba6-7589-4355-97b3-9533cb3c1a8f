{"ast": null, "code": "import { of as observableOf } from 'rxjs';\nimport { TrafficBarData } from '../data/traffic-bar';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"./periods.service\";\nexport let TrafficBarService = /*#__PURE__*/(() => {\n  class TrafficBarService extends TrafficBarData {\n    constructor(period) {\n      super();\n      this.period = period;\n      this.data = {};\n      this.data = {\n        week: this.getDataForWeekPeriod(),\n        month: this.getDataForMonthPeriod(),\n        year: this.getDataForYearPeriod()\n      };\n    }\n    getDataForWeekPeriod() {\n      return {\n        data: [10, 15, 19, 7, 20, 13, 15],\n        labels: this.period.getWeeks(),\n        formatter: '{c0} MB'\n      };\n    }\n    getDataForMonthPeriod() {\n      return {\n        data: [0.5, 0.3, 0.8, 0.2, 0.3, 0.7, 0.8, 1, 0.7, 0.8, 0.6, 0.7],\n        labels: this.period.getMonths(),\n        formatter: '{c0} GB'\n      };\n    }\n    getDataForYearPeriod() {\n      return {\n        data: [10, 15, 19, 7, 20, 13, 15, 19, 11],\n        labels: this.period.getYears(),\n        formatter: '{c0} GB'\n      };\n    }\n    getTrafficBarData(period) {\n      return observableOf(this.data[period]);\n    }\n    static {\n      this.ɵfac = function TrafficBarService_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || TrafficBarService)(i0.ɵɵinject(i1.PeriodsService));\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: TrafficBarService,\n        factory: TrafficBarService.ɵfac\n      });\n    }\n  }\n  return TrafficBarService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}