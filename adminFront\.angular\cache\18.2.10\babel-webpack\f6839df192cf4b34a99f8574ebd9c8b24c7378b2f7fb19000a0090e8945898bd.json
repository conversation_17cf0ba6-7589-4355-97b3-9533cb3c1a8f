{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { Component, ViewChild } from '@angular/core';\nimport { SharedModule } from '../../../components/shared.module';\nimport { CommonModule } from '@angular/common';\nimport { NbDatepickerModule } from '@nebular/theme';\nimport { LabelInOptionsPipe, TypeMailPipe } from 'src/app/@theme/pipes/mapping.pipe';\nimport { BaseComponent } from '../../../components/base/baseComponent';\nimport { tap } from 'rxjs';\nimport { NbDateFnsDateModule } from '@nebular/date-fns';\nimport * as moment from 'moment';\nimport { EnumFileType } from 'src/app/shared/enum/enumFileType';\nimport { DateFormatHourPipe, DateFormatPipe } from 'src/app/@theme/pipes';\nimport { LocalStorageService } from 'src/app/shared/services/local-storage.service';\nimport { STORAGE_KEY } from 'src/app/shared/constant/constant';\nlet ReviewDocumentManagementComponent = class ReviewDocumentManagementComponent extends BaseComponent {\n  constructor(_allow, dialogService, message, valid, _houseService, _buildCaseService, _reviewService, reviewService, utilityService) {\n    super(_allow);\n    this._allow = _allow;\n    this.dialogService = dialogService;\n    this.message = message;\n    this.valid = valid;\n    this._houseService = _houseService;\n    this._buildCaseService = _buildCaseService;\n    this._reviewService = _reviewService;\n    this.reviewService = reviewService;\n    this.utilityService = utilityService;\n    this.pageFirst = 1;\n    this.pageSize = 10;\n    this.pageIndex = 1;\n    this.totalRecords = 0;\n    this.reviewTypeOptions = [{\n      value: 1,\n      label: '標準圖' //standard drawing\n    }, {\n      value: 2,\n      label: '設備圖' //equipment drawing\n    }];\n    this.reviewTypeOptionsQuery = [{\n      value: -1,\n      label: '全部'\n    }, {\n      value: 1,\n      label: '標準圖' //standard drawing\n    }, {\n      value: 2,\n      label: '設備圖' //equipment drawing\n    }];\n    this.examineStatusOptions = [{\n      value: -1,\n      label: '待審核' //Pending review\n    }, {\n      value: 1,\n      label: '已通過' //passed\n    }, {\n      value: 2,\n      label: '已駁回' //rejected\n    }];\n    this.examineStatusOptionsQuery = [{\n      value: -1,\n      label: '全部'\n    }, {\n      value: 0,\n      label: '待審核' //Pending review\n    }, {\n      value: 1,\n      label: '已通過' //passed\n    }, {\n      value: 2,\n      label: '已駁回' //rejected\n    }];\n    this.statusOptions = [{\n      value: 1,\n      //0停用 1啟用 9刪除\n      label: '啟用' //enable\n    }, {\n      value: 2,\n      label: '停用' //Disable\n    }];\n    this.statusOptionsQuery = [{\n      value: -1,\n      label: '全部'\n    }, {\n      value: 1,\n      //0停用 1啟用 9刪除\n      label: '啟用' //enable\n    }, {\n      value: 2,\n      label: '停用' //Disable\n    }];\n    this.buildCaseOptions = [{\n      label: '全部',\n      value: ''\n    }];\n    this.fileName = null;\n    this.imageUrl = undefined;\n    this.isHouseList = false;\n    this.latestAction = 0;\n    this.isNew = true;\n  }\n  ngOnInit() {\n    if (LocalStorageService.GetSessionStorage(STORAGE_KEY.REVIEW_SEARCH) != null && LocalStorageService.GetSessionStorage(STORAGE_KEY.REVIEW_SEARCH) != undefined && LocalStorageService.GetSessionStorage(STORAGE_KEY.REVIEW_SEARCH) != \"\") {\n      let previous_search = JSON.parse(LocalStorageService.GetSessionStorage(STORAGE_KEY.REVIEW_SEARCH));\n      this.searchQuery = {\n        selectedBuildCase: null,\n        selectedReviewType: this.reviewTypeOptionsQuery.find(x => x.value == previous_search.CReviewType.value) ? this.reviewTypeOptionsQuery.find(x => x.value == previous_search.CReviewType.value) : this.reviewTypeOptionsQuery[0],\n        selectedExamineStatus: this.examineStatusOptionsQuery.find(x => x.value == previous_search.CExamineStatus.value) ? this.examineStatusOptionsQuery.find(x => x.value == previous_search.CExamineStatus.value) : this.examineStatusOptionsQuery[0],\n        seletedStatus: this.statusOptionsQuery.find(x => x.value == previous_search.CSeletedStatus.value) ? this.statusOptionsQuery.find(x => x.value == previous_search.CSeletedStatus.value) : this.statusOptionsQuery[0],\n        CReviewName: previous_search.CReviewName\n      };\n    } else {\n      this.searchQuery = {\n        selectedBuildCase: null,\n        selectedReviewType: this.reviewTypeOptionsQuery[0],\n        selectedExamineStatus: this.examineStatusOptionsQuery[0],\n        seletedStatus: this.statusOptionsQuery[0],\n        CReviewName: ''\n      };\n    }\n    this.getUserBuildCase();\n  }\n  clearImage() {\n    if (this.imageUrl) {\n      this.imageUrl = null;\n      this.fileName = null;\n      if (this.fileInput) {\n        this.fileInput.nativeElement.value = null; // Xóa giá trị input file\n      }\n    }\n  }\n  onFileSelected(event) {\n    const file = event.target.files[0];\n    const fileRegex = /pdf|jpg|jpeg|png|dwg/i;\n    if (!fileRegex.test(file.name)) {\n      this.message.showErrorMSG('檔案格式錯誤，僅限pdf');\n      return;\n    }\n    if (file) {\n      this.fileName = file.name;\n      const reader = new FileReader();\n      reader.onload = e => {\n        this.imageUrl = {\n          CName: file.name,\n          CFile: e.target?.result?.toString().split(',')[1],\n          Cimg: file.name.includes('pdf') ? file : file,\n          CFileUpload: file,\n          CFileType: EnumFileType.PDF\n        };\n        if (this.fileInput) {\n          this.fileInput.nativeElement.value = null;\n        }\n      };\n      reader.readAsDataURL(file);\n    }\n  }\n  isCheckAllRowChecked(row) {\n    return row.every(item => item.CIsSelect);\n  }\n  isCheckAllColumnChecked(index) {\n    if (this.isHouseList) {\n      if (index < 0 || index >= this.houseList2D[0].length) {\n        throw new Error(\"Invalid index. Index must be within the bounds of the array.\");\n      }\n      for (const floorData of this.houseList2D) {\n        if (index >= floorData.length || !floorData[index].CIsSelect) {\n          return false; // Found a customer with CIsEnable not true (or missing)\n        }\n      }\n      return true; // All customers at the given index have CIsEnable as true\n    }\n    return false;\n  }\n  enableAllAtIndex(checked, index) {\n    if (index < 0) {\n      throw new Error(\"Invalid index. Index must be a non-negative number.\");\n    }\n    for (const floorData of this.houseList2D) {\n      if (index < floorData.length) {\n        // Check if index is valid for this floor\n        floorData[index].CIsSelect = checked;\n      }\n    }\n  }\n  enableAllRow(checked, row) {\n    for (const item of row) {\n      item.CIsSelect = checked;\n    }\n  }\n  getItemByValue(value, options) {\n    for (const item of options) {\n      if (item.value === value) {\n        return item;\n      }\n    }\n    return null;\n  }\n  getReviewById(item, ref) {\n    this._reviewService.apiReviewGetReviewByIdPost$Json({\n      body: item.CReviewId\n    }).subscribe(res => {\n      if (res.Entries && res.StatusCode == 0) {\n        const data = res.Entries;\n        this.selectedReview = {\n          CBuildCaseId: data.tblReview?.CBuildCaseId,\n          CReviewId: data.tblReview?.CReviewId,\n          CReviewType: data.tblReview?.CReviewType,\n          CReviewName: data.tblReview?.CReviewName ? data.tblReview?.CReviewName : '',\n          CSort: data.tblReview?.CSort,\n          CStatus: data.tblReview?.CStatus,\n          CFileUrl: data.tblReview?.CFileUrl,\n          CExamineNote: data?.CExamineNote ? data?.CExamineNote : '',\n          seletedStatus: data.tblReview?.CStatus ? this.getItemByValue(data.tblReview?.CStatus, this.statusOptions) : this.statusOptions[0],\n          selectedReviewType: data.tblReview?.CReviewType ? this.getItemByValue(data.tblReview?.CReviewType, this.reviewTypeOptions) : this.reviewTypeOptions[0],\n          tblExamineLogs: data.tblExamineLogs,\n          reviewHouseHolds: data?.reviewHouseHolds?.filter(i => i.CIsSelect),\n          tblReview: data.tblReview\n        };\n        if (data && data?.tblExamineLogs && data?.tblExamineLogs.length) {\n          if (data?.tblExamineLogs.length === 0) return undefined;\n          this.latestAction = data?.tblExamineLogs[0].CAction;\n          let latestDate = data?.tblExamineLogs[0].CCreateDt ? new Date(data?.tblExamineLogs[0].CCreateDt) : '';\n          for (let i = 1; i < data.tblExamineLogs.length; i++) {\n            if (data.tblExamineLogs[i].CCreateDt) {\n              const currentDate = new Date(data.tblExamineLogs[i].CCreateDt);\n              if (currentDate > latestDate) {\n                latestDate = currentDate;\n                this.latestAction = data?.tblExamineLogs[i].CAction;\n              }\n            }\n          }\n        }\n        this.getHouseList();\n        this.dialogService.open(ref);\n      }\n    });\n  }\n  flattenAndFilter(data) {\n    const flattened = [];\n    for (const floorData of data) {\n      for (const house of floorData) {\n        if (house.CIsSelect && house.CIsEnable) {\n          flattened.push({\n            CHouseID: house.CHouseID,\n            CIsSelect: house.CIsSelect,\n            CFloor: house.CFloor,\n            CHouseHold: house.CHouseHold\n          });\n        }\n      }\n    }\n    return flattened;\n  }\n  onSaveReview(ref) {\n    this.validation();\n    if (this.valid.errorMessages.length > 0) {\n      this.message.showErrorMSGs(this.valid.errorMessages);\n      return;\n    }\n    this.saveReviewPostRes = {\n      CBuildCaseId: this.searchQuery.selectedBuildCase.value,\n      CReviewId: this.selectedReview.CReviewId,\n      CReviewType: this.selectedReview.selectedReviewType.value,\n      CReviewName: this.selectedReview.CReviewName,\n      CSort: this.selectedReview?.CSort,\n      CStatus: this.selectedReview.seletedStatus.value,\n      CFile: this.imageUrl ? this.imageUrl.CFileUpload : undefined,\n      CExamineNote: this.selectedReview.CExamineNote,\n      HouseReviews: this.houseList2D != null && this.houseList2D != undefined && this.houseList2D.length > 0 ? this.flattenAndFilter(this.houseList2D) : []\n    };\n    this.reviewService.SaveReview(this.saveReviewPostRes).subscribe(res => {\n      if (res && res.body && res.body.StatusCode === 0) {\n        this.message.showSucessMSG(\"執行成功\");\n        this.clearImage();\n        this.getReviewList();\n        ref.close();\n      } else {\n        this.message.showErrorMSG(res && res.body && res.body.Message);\n      }\n    });\n  }\n  onSearch() {\n    let previous_search = {\n      CReviewName: this.searchQuery.CReviewName,\n      CSelectedBuildCase: this.searchQuery.selectedBuildCase,\n      CSeletedStatus: this.searchQuery.seletedStatus,\n      CReviewType: this.searchQuery.selectedReviewType,\n      CExamineStatus: this.searchQuery.selectedExamineStatus\n    };\n    LocalStorageService.AddSessionStorage(STORAGE_KEY.REVIEW_SEARCH, JSON.stringify(previous_search));\n    this.getReviewList();\n  }\n  pageChanged(newPage) {\n    this.pageIndex = newPage;\n    this.getReviewList();\n  }\n  groupByFloor(customerData, isDefaut) {\n    const groupedData = [];\n    const uniqueFloors = Array.from(new Set(customerData.map(customer => customer.CFloor).filter(floor => floor !== null)));\n    for (const floor of uniqueFloors) {\n      groupedData.push([]);\n    }\n    for (const customer of customerData) {\n      const floorIndex = uniqueFloors.indexOf(customer.CFloor);\n      if (floorIndex !== -1) {\n        groupedData[floorIndex].push({\n          CIsSelect: customer?.CIsSelect || false,\n          CHouseID: customer.CID,\n          CHouseType: customer.CHouseType,\n          CFloor: customer.CFloor,\n          CHouseHold: customer.CHouseHold,\n          CIsEnable: customer.CIsEnable\n        });\n      }\n    }\n    return groupedData;\n  }\n  addCIsSelectToA(A, B) {\n    const mapB = new Map(B.map(item => [`${item.CHouseHold}-${item.CFloor}`, item.CIsSelect]));\n    return A.map(item => {\n      const key = `${item.CHouseHold}-${item.CFloor}`;\n      return {\n        ...item,\n        CIsSelect: mapB.has(key) ? mapB.get(key) : false\n      };\n    });\n  }\n  sortByFloorDescending(arr) {\n    return arr.sort((a, b) => (b.CFloor ?? 0) - (a.CFloor ?? 0));\n  }\n  getHouseList() {\n    return this._houseService.apiHouseGetHouseListPost$Json({\n      body: {\n        CBuildCaseID: this.searchQuery.selectedBuildCase.value,\n        CIsPagi: false\n      }\n    }).pipe(tap(res => {\n      if (res && res.StatusCode === 0 && res.Entries) {\n        const rest = this.sortByFloorDescending(res.Entries);\n        this.houseListEnable = [...rest];\n        if (this.selectedReview.CReviewId) {\n          this.houseList2D = this.groupByFloor(this.addCIsSelectToA([...this.houseListEnable], this.selectedReview.reviewHouseHolds ? [...this.selectedReview.reviewHouseHolds] : []));\n        } else {\n          this.houseList2D = this.groupByFloor([...rest]);\n        }\n        this.isHouseList = true;\n      }\n    })).subscribe();\n  }\n  openPdfInNewTab(CFileUrl) {\n    if (CFileUrl) {\n      this.utilityService.openFileNewTab(CFileUrl);\n    }\n  }\n  getReviewList() {\n    return this._reviewService.apiReviewGetReviewListPost$Json({\n      body: {\n        PageIndex: this.pageIndex,\n        PageSize: this.pageSize,\n        CReviewName: this.searchQuery.CReviewName,\n        CBuildCaseID: this.searchQuery.selectedBuildCase.value,\n        CStatus: this.searchQuery.seletedStatus.value,\n        CReviewType: this.searchQuery.selectedReviewType.value,\n        CExamineStatus: this.searchQuery.selectedExamineStatus.value\n      }\n    }).pipe(tap(res => {\n      if (res.Entries && res.StatusCode == 0) {\n        this.reviewList = res.Entries;\n        this.totalRecords = res.TotalItems;\n      }\n    })).subscribe();\n  }\n  onSelectionChangeBuildCase() {\n    if (this.searchQuery.selectedBuildCase.value) {\n      this.getReviewList();\n    }\n  }\n  getUserBuildCase() {\n    this._buildCaseService.apiBuildCaseGetUserBuildCasePost$Json({\n      body: {}\n    }).pipe(tap(res => {\n      if (res.Entries && res.StatusCode == 0) {\n        this.userBuildCaseOptions = res.Entries.map(res => {\n          return {\n            label: res.CBuildCaseName,\n            value: res.cID\n          };\n        });\n        if (LocalStorageService.GetSessionStorage(STORAGE_KEY.REVIEW_SEARCH) != null && LocalStorageService.GetSessionStorage(STORAGE_KEY.REVIEW_SEARCH) != undefined && LocalStorageService.GetSessionStorage(STORAGE_KEY.REVIEW_SEARCH) != \"\") {\n          let previous_search = JSON.parse(LocalStorageService.GetSessionStorage(STORAGE_KEY.REVIEW_SEARCH));\n          if (previous_search.CSelectedBuildCase != null && previous_search.CSelectedBuildCase != undefined) {\n            let index = this.userBuildCaseOptions.findIndex(x => x.value == previous_search.CSelectedBuildCase.value);\n            this.searchQuery.selectedBuildCase = this.userBuildCaseOptions[index];\n          } else {\n            this.searchQuery.selectedBuildCase = this.userBuildCaseOptions[0];\n          }\n        } else {\n          this.searchQuery.selectedBuildCase = this.userBuildCaseOptions[0];\n        }\n        if (this.searchQuery.selectedBuildCase.value) {\n          this.getReviewList();\n        }\n      }\n    })).subscribe();\n  }\n  openModel(ref, item) {\n    this.latestAction = 0;\n    this.isHouseList = false;\n    this.isNew = true;\n    this.clearImage();\n    this.selectedReview = {\n      selectedReviewType: this.reviewTypeOptions[0],\n      seletedStatus: this.statusOptions[0],\n      selectedExamineStatus: this.examineStatusOptions[0],\n      CReviewName: '',\n      CSort: 0,\n      CFileUrl: '',\n      CExamineNote: '',\n      CIsSelectAll: false\n    };\n    if (item) {\n      this.isNew = false;\n      this.getReviewById(item, ref);\n    } else {\n      this.isNew = true;\n      this.getHouseList();\n      this.dialogService.open(ref);\n    }\n  }\n  formatDate(CChangeDate) {\n    if (CChangeDate) {\n      return moment(CChangeDate).format('YYYY-MM-DDTHH:mm:ss');\n    }\n    return '';\n  }\n  onSubmit(ref) {}\n  onClose(ref) {\n    ref.close();\n  }\n  validation() {\n    this.valid.clear();\n    if (this.isNew && !this.imageUrl) {\n      this.valid.addErrorMessage(`前台圖片`);\n    }\n    this.valid.required('[送審說明]', this.selectedReview.CExamineNote);\n  }\n  getActionName(actionID) {\n    let textR = \"\";\n    if (actionID != undefined) {\n      switch (actionID) {\n        case 1:\n          textR = \"傳送\";\n          break;\n        case 2:\n          textR = \"通過\";\n          break;\n        case 3:\n          textR = \"駁回\";\n          break;\n        default:\n          break;\n      }\n    }\n    return textR;\n  }\n  checkAllHouseIsValid(household, floor) {\n    let count = 0;\n    let total = 0;\n    if (household != null) {\n      for (let i = 1; i < this.houseList2D.length; i++) {\n        this.houseList2D[i].map(val => {\n          if (val.CHouseHold && val.CHouseHold == household) {\n            total++;\n            if (val.CIsEnable == true) {\n              count++;\n            }\n          }\n        });\n      }\n    }\n    if (floor != null) {\n      for (let i = 0; i < this.houseList2D.length; i++) {\n        this.houseList2D[i].map(val => {\n          if (val.CFloor == floor) {\n            total++;\n            if (val.CIsEnable == true) {\n              count++;\n            }\n          }\n        });\n      }\n    }\n    if (count == total) {\n      return false;\n    }\n    return true;\n  }\n};\n__decorate([ViewChild('fileInput')], ReviewDocumentManagementComponent.prototype, \"fileInput\", void 0);\nReviewDocumentManagementComponent = __decorate([Component({\n  selector: 'ngx-review-document-management',\n  templateUrl: './review-document-management.component.html',\n  styleUrls: ['./review-document-management.component.scss'],\n  standalone: true,\n  imports: [CommonModule, SharedModule, TypeMailPipe, NbDatepickerModule, NbDateFnsDateModule, DateFormatPipe, LabelInOptionsPipe, DateFormatHourPipe]\n})], ReviewDocumentManagementComponent);\nexport { ReviewDocumentManagementComponent };", "map": {"version": 3, "names": ["Component", "ViewChild", "SharedModule", "CommonModule", "NbDatepickerModule", "LabelInOptionsPipe", "TypeMailPipe", "BaseComponent", "tap", "NbDateFnsDateModule", "moment", "EnumFileType", "DateFormatHourPipe", "DateFormatPipe", "LocalStorageService", "STORAGE_KEY", "ReviewDocumentManagementComponent", "constructor", "_allow", "dialogService", "message", "valid", "_houseService", "_buildCaseService", "_reviewService", "reviewService", "utilityService", "pageFirst", "pageSize", "pageIndex", "totalRecords", "reviewTypeOptions", "value", "label", "reviewTypeOptionsQuery", "examineStatusOptions", "examineStatusOptionsQuery", "statusOptions", "statusOptionsQuery", "buildCaseOptions", "fileName", "imageUrl", "undefined", "isHouseList", "latestAction", "isNew", "ngOnInit", "GetSessionStorage", "REVIEW_SEARCH", "previous_search", "JSON", "parse", "searchQuery", "selectedBuildCase", "selectedReviewType", "find", "x", "CReviewType", "selectedExamineStatus", "CExamineStatus", "se<PERSON><PERSON><PERSON><PERSON><PERSON>", "CSeletedStatus", "CReviewName", "getUserBuildCase", "clearImage", "fileInput", "nativeElement", "onFileSelected", "event", "file", "target", "files", "fileRegex", "test", "name", "showErrorMSG", "reader", "FileReader", "onload", "e", "CName", "CFile", "result", "toString", "split", "Cimg", "includes", "CFileUpload", "CFileType", "PDF", "readAsDataURL", "isCheckAllRowChecked", "row", "every", "item", "CIsSelect", "isCheckAllColumnChecked", "index", "houseList2D", "length", "Error", "floorData", "enableAllAtIndex", "checked", "enableAllRow", "getItemByValue", "options", "getReviewById", "ref", "apiReviewGetReviewByIdPost$Json", "body", "CReviewId", "subscribe", "res", "Entries", "StatusCode", "data", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "CBuildCaseId", "tblReview", "CSort", "CStatus", "CFileUrl", "CExamineNote", "tblExamineLogs", "reviewHouseHolds", "filter", "i", "CAction", "latestDate", "CCreateDt", "Date", "currentDate", "getHouseList", "open", "flattenAndFilter", "flattened", "house", "CIsEnable", "push", "CHouseID", "CFloor", "CHouseHold", "onSaveReview", "validation", "errorMessages", "showErrorMSGs", "saveReviewPostRes", "HouseReviews", "SaveReview", "showSucessMSG", "getReviewList", "close", "Message", "onSearch", "CSelectedBuildCase", "AddSessionStorage", "stringify", "pageChanged", "newPage", "groupByFloor", "customerData", "isDefaut", "groupedData", "uniqueFloors", "Array", "from", "Set", "map", "customer", "floor", "floorIndex", "indexOf", "CID", "CHouseType", "addCIsSelectToA", "A", "B", "mapB", "Map", "key", "has", "get", "sortByFloorDescending", "arr", "sort", "a", "b", "apiHouseGetHouseListPost$Json", "CBuildCaseID", "CIsPagi", "pipe", "rest", "houseListEnable", "openPdfInNewTab", "openFileNewTab", "apiReviewGetReviewListPost$Json", "PageIndex", "PageSize", "reviewList", "TotalItems", "onSelectionChangeBuildCase", "apiBuildCaseGetUserBuildCasePost$Json", "userBuildCaseOptions", "CBuildCaseName", "cID", "findIndex", "openModel", "CIsSelectAll", "formatDate", "CChangeDate", "format", "onSubmit", "onClose", "clear", "addErrorMessage", "required", "getActionName", "actionID", "textR", "checkAllHouseIsValid", "household", "count", "total", "val", "__decorate", "selector", "templateUrl", "styleUrls", "standalone", "imports"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\construction-project-management\\notice-management\\review-document-management\\review-document-management.component.ts"], "sourcesContent": ["import { Component, ElementRef, OnInit, ViewChild } from '@angular/core';\r\nimport { SharedModule } from '../../../components/shared.module';\r\nimport { CommonModule } from '@angular/common';\r\nimport { NbDatepickerModule, NbDialogService } from '@nebular/theme';\r\nimport { MessageService } from 'src/app/shared/services/message.service';\r\nimport { ValidationHelper } from 'src/app/shared/helper/validationHelper';\r\nimport { BuildCaseService, HouseService, ReviewService } from 'src/services/api/services';\r\nimport { LabelInOptionsPipe, TypeMailPipe } from 'src/app/@theme/pipes/mapping.pipe';\r\nimport { BaseComponent } from '../../../components/base/baseComponent';\r\nimport { AllowHelper } from 'src/app/shared/helper/allowHelper';\r\nimport { GetHouseListArgs, GetHouseListRes, GetReviewListRes, HouseReview, ReviewHouseHold, TblExamineLog, TblReview } from 'src/services/api/models';\r\nimport { tap } from 'rxjs';\r\nimport { NbDateFnsDateModule } from '@nebular/date-fns';\r\nimport * as moment from 'moment';\r\nimport { EnumFileType } from 'src/app/shared/enum/enumFileType';\r\nimport { ReviewServiceCustom } from 'src/app/@core/service/review.service';\r\nimport { DateFormatHourPipe, DateFormatPipe } from 'src/app/@theme/pipes';\r\nimport { UtilityService } from 'src/app/shared/services/utility.service';\r\nimport { LocalStorageService } from 'src/app/shared/services/local-storage.service';\r\nimport { STORAGE_KEY } from 'src/app/shared/constant/constant';\r\nimport { co } from '@fullcalendar/core/internal-common';\r\n\r\nexport interface selectItem {\r\n  label: string,\r\n  value: number | string,\r\n}\r\n\r\nexport interface HouseList {\r\n  CFloor?: number | null;\r\n  CHouseHold?: string | null;\r\n  CHouseID?: number;\r\n  CID?: number;\r\n  CIsSelect?: boolean | null;\r\n  CHouseType?: number | null;\r\n  CIsEnable?: boolean | null;\r\n}\r\n\r\nexport interface SaveReviewPostParam {\r\n  CBuildCaseId?: number;\r\n  CReviewId?: number;\r\n  CReviewType?: number;\r\n  CReviewName?: string;\r\n  CSort?: number;\r\n  CStatus?: number;\r\n  CFile?: Blob;\r\n  CExamineNote?: string;\r\n  HouseReviews?: Array<HouseReview>;\r\n  CIsSelectAll?: boolean;\r\n  selectedCNoticeType?: any\r\n}\r\n\r\nexport interface ReviewType {\r\n  CBuildCaseId?: number;\r\n  CReviewId?: number;\r\n  CReviewType?: number | null;\r\n  CReviewName?: string;\r\n  CSort?: number;\r\n  CStatus?: number;\r\n  CFile?: Blob;\r\n  CExamineNote?: string;\r\n  HouseReviews?: Array<HouseReview>;\r\n  CIsSelectAll?: boolean;\r\n  selectedCNoticeType?: any,\r\n  CFileUrl?: string | null;\r\n  selectedBuildCase?: any | null;\r\n  selectedReviewType?: any | null;\r\n  selectedExamineStatus?: any | null;\r\n  seletedStatus?: any | null;\r\n  reviewHouseHolds?: Array<ReviewHouseHold>;\r\n  tblExamineLogs?: Array<TblExamineLog> | null;\r\n  tblReview?: TblReview;\r\n}\r\nexport interface SearchQuery {\r\n  selectedBuildCase?: any | null;\r\n  selectedReviewType?: any | null;\r\n  selectedExamineStatus?: any | null;\r\n  seletedStatus?: any | null;\r\n  CReviewName?: any | null;\r\n}\r\n@Component({\r\n  selector: 'ngx-review-document-management',\r\n  templateUrl: './review-document-management.component.html',\r\n  styleUrls: ['./review-document-management.component.scss'],\r\n  standalone: true,\r\n  imports: [CommonModule, SharedModule, TypeMailPipe, NbDatepickerModule, NbDateFnsDateModule, DateFormatPipe, LabelInOptionsPipe, DateFormatHourPipe],\r\n})\r\n\r\nexport class ReviewDocumentManagementComponent extends BaseComponent implements OnInit {\r\n  constructor(\r\n    private _allow: AllowHelper,\r\n    private dialogService: NbDialogService,\r\n    private message: MessageService,\r\n    private valid: ValidationHelper,\r\n    private _houseService: HouseService,\r\n    private _buildCaseService: BuildCaseService,\r\n    private _reviewService: ReviewService,\r\n    private reviewService: ReviewServiceCustom,\r\n    private utilityService: UtilityService,\r\n  ) { super(_allow) }\r\n\r\n  override pageFirst = 1;\r\n  override pageSize = 10;\r\n  override pageIndex = 1;\r\n  override totalRecords = 0;\r\n\r\n  reviewTypeOptions = [{\r\n    value: 1,\r\n    label: '標準圖', //standard drawing\r\n  }, {\r\n    value: 2,\r\n    label: '設備圖', //equipment drawing\r\n  }\r\n  ]\r\n\r\n  reviewTypeOptionsQuery = [\r\n    {\r\n      value: -1,\r\n      label: '全部'\r\n    },\r\n    {\r\n      value: 1,\r\n      label: '標準圖', //standard drawing\r\n    }, {\r\n      value: 2,\r\n      label: '設備圖', //equipment drawing\r\n    }\r\n  ]\r\n\r\n  examineStatusOptions = [\r\n    {\r\n      value: -1,\r\n      label: '待審核' //Pending review\r\n\r\n    }, {\r\n      value: 1,\r\n      label: '已通過' //passed\r\n\r\n    },\r\n    {\r\n      value: 2,\r\n      label: '已駁回' //rejected\r\n\r\n    }\r\n  ]\r\n\r\n  examineStatusOptionsQuery = [{\r\n    value: -1,\r\n    label: '全部'\r\n  },\r\n  {\r\n    value: 0,\r\n    label: '待審核' //Pending review\r\n\r\n  }, {\r\n    value: 1,\r\n    label: '已通過' //passed\r\n\r\n  },\r\n  {\r\n    value: 2,\r\n    label: '已駁回' //rejected\r\n  }\r\n  ]\r\n\r\n  statusOptions = [{\r\n    value: 1, //0停用 1啟用 9刪除\r\n    label: '啟用' //enable\r\n  }, {\r\n    value: 2,\r\n    label: '停用' //Disable\r\n  },]\r\n\r\n  statusOptionsQuery = [{\r\n    value: -1,\r\n    label: '全部'\r\n  }, {\r\n    value: 1, //0停用 1啟用 9刪除\r\n    label: '啟用' //enable\r\n  }, {\r\n    value: 2,\r\n    label: '停用' //Disable\r\n  },]\r\n\r\n\r\n  searchQuery: SearchQuery\r\n\r\n  buildCaseOptions: any[] = [{ label: '全部', value: '' }]\r\n\r\n  userBuildCaseOptions: any\r\n\r\n\r\n  override ngOnInit(): void {\r\n    if (LocalStorageService.GetSessionStorage(STORAGE_KEY.REVIEW_SEARCH) != null\r\n      && LocalStorageService.GetSessionStorage(STORAGE_KEY.REVIEW_SEARCH) != undefined\r\n      && LocalStorageService.GetSessionStorage(STORAGE_KEY.REVIEW_SEARCH) != \"\") {\r\n      let previous_search = JSON.parse(LocalStorageService.GetSessionStorage(STORAGE_KEY.REVIEW_SEARCH));\r\n      this.searchQuery = {\r\n        selectedBuildCase: null,\r\n        selectedReviewType: this.reviewTypeOptionsQuery.find(x => x.value == previous_search.CReviewType.value)\r\n          ? this.reviewTypeOptionsQuery.find(x => x.value == previous_search.CReviewType.value)\r\n          : this.reviewTypeOptionsQuery[0],\r\n        selectedExamineStatus: this.examineStatusOptionsQuery.find(x => x.value == previous_search.CExamineStatus.value)\r\n          ? this.examineStatusOptionsQuery.find(x => x.value == previous_search.CExamineStatus.value)\r\n          : this.examineStatusOptionsQuery[0],\r\n        seletedStatus: this.statusOptionsQuery.find(x => x.value == previous_search.CSeletedStatus.value)\r\n          ? this.statusOptionsQuery.find(x => x.value == previous_search.CSeletedStatus.value)\r\n          : this.statusOptionsQuery[0],\r\n        CReviewName: previous_search.CReviewName\r\n      }\r\n    }\r\n    else {\r\n      this.searchQuery = {\r\n        selectedBuildCase: null,\r\n        selectedReviewType: this.reviewTypeOptionsQuery[0],\r\n        selectedExamineStatus: this.examineStatusOptionsQuery[0],\r\n        seletedStatus: this.statusOptionsQuery[0],\r\n        CReviewName: ''\r\n      }\r\n    }\r\n    this.getUserBuildCase()\r\n  }\r\n\r\n  @ViewChild('fileInput') fileInput!: ElementRef;\r\n  fileName: string | null = null;\r\n  imageUrl: any = undefined;\r\n\r\n  clearImage() {\r\n    if (this.imageUrl) {\r\n      this.imageUrl = null;\r\n      this.fileName = null;\r\n      if (this.fileInput) {\r\n        this.fileInput.nativeElement.value = null; // Xóa giá trị input file\r\n      }\r\n    }\r\n  }\r\n\r\n  onFileSelected(event: any) {\r\n    const file: File = event.target.files[0];\r\n    const fileRegex = /pdf|jpg|jpeg|png|dwg/i;\r\n    if (!fileRegex.test(file.name)) {\r\n      this.message.showErrorMSG('檔案格式錯誤，僅限pdf');\r\n      return;\r\n    }\r\n    if (file) {\r\n      this.fileName = file.name;\r\n      const reader = new FileReader();\r\n      reader.onload = (e: any) => {\r\n        this.imageUrl = {\r\n          CName: file.name,\r\n          CFile: e.target?.result?.toString().split(',')[1],\r\n          Cimg: file.name.includes('pdf') ? file : file,\r\n          CFileUpload: file,\r\n          CFileType: EnumFileType.PDF,\r\n        };\r\n        if (this.fileInput) {\r\n          this.fileInput.nativeElement.value = null;\r\n        }\r\n      };\r\n      reader.readAsDataURL(file);\r\n    }\r\n  }\r\n\r\n  isHouseList = false\r\n\r\n  houseListDefault: any\r\n\r\n  isCheckAllRowChecked(row: any[]): boolean {\r\n    return row.every((item: { CIsSelect: any; }) => item.CIsSelect);\r\n  }\r\n\r\n  isCheckAllColumnChecked(index: number): boolean {\r\n    if (this.isHouseList) {\r\n      if (index < 0 || index >= this.houseList2D[0].length) {\r\n        throw new Error(\"Invalid index. Index must be within the bounds of the array.\");\r\n      }\r\n      for (const floorData of this.houseList2D) {\r\n        if (index >= floorData.length || !floorData[index].CIsSelect) {\r\n          return false; // Found a customer with CIsEnable not true (or missing)\r\n        }\r\n      }\r\n      return true; // All customers at the given index have CIsEnable as true\r\n    }\r\n    return false\r\n  }\r\n\r\n  enableAllAtIndex(checked: boolean, index: number): void {\r\n    if (index < 0) {\r\n      throw new Error(\"Invalid index. Index must be a non-negative number.\");\r\n    }\r\n    for (const floorData of this.houseList2D) {\r\n      if (index < floorData.length) { // Check if index is valid for this floor\r\n        floorData[index].CIsSelect = checked;\r\n      }\r\n    }\r\n  }\r\n\r\n  enableAllRow(checked: boolean, row: any[]) {\r\n    for (const item of row) {\r\n      item.CIsSelect = checked;\r\n    }\r\n  }\r\n  selectedReview: ReviewType\r\n\r\n  getItemByValue(value: any, options: any[]) {\r\n    for (const item of options) {\r\n      if (item.value === value) {\r\n        return item;\r\n      }\r\n    }\r\n    return null;\r\n  }\r\n  latestAction: any = 0\r\n\r\n  getReviewById(item: any, ref: any) {\r\n\r\n    this._reviewService.apiReviewGetReviewByIdPost$Json({\r\n      body: item.CReviewId\r\n    }).subscribe(res => {\r\n      if (res.Entries && res.StatusCode == 0) {\r\n        const data = res.Entries\r\n        this.selectedReview = {\r\n          CBuildCaseId: data.tblReview?.CBuildCaseId,\r\n          CReviewId: data.tblReview?.CReviewId,\r\n          CReviewType: data.tblReview?.CReviewType,\r\n          CReviewName: data.tblReview?.CReviewName ? data.tblReview?.CReviewName : '',\r\n          CSort: data.tblReview?.CSort,\r\n          CStatus: data.tblReview?.CStatus,\r\n          CFileUrl: data.tblReview?.CFileUrl,\r\n          CExamineNote: data?.CExamineNote ? data?.CExamineNote : '',\r\n          seletedStatus: data.tblReview?.CStatus ? this.getItemByValue(data.tblReview?.CStatus, this.statusOptions) : this.statusOptions[0],\r\n          selectedReviewType: data.tblReview?.CReviewType ? this.getItemByValue(data.tblReview?.CReviewType, this.reviewTypeOptions) : this.reviewTypeOptions[0],\r\n          tblExamineLogs: data.tblExamineLogs,\r\n          reviewHouseHolds: data?.reviewHouseHolds?.filter((i: any) => i.CIsSelect),\r\n          tblReview: data.tblReview\r\n        }\r\n\r\n        if (data && data?.tblExamineLogs && data?.tblExamineLogs.length) {\r\n          if (data?.tblExamineLogs.length === 0) return undefined;\r\n          this.latestAction = data?.tblExamineLogs[0].CAction;\r\n          let latestDate = data?.tblExamineLogs[0].CCreateDt ? new Date(data?.tblExamineLogs[0].CCreateDt) : ''\r\n          for (let i = 1; i < data.tblExamineLogs.length; i++) {\r\n            if (data.tblExamineLogs[i].CCreateDt) {\r\n              const currentDate = new Date(data.tblExamineLogs[i].CCreateDt!)\r\n              if (currentDate > latestDate) {\r\n                latestDate = currentDate;\r\n                this.latestAction = data?.tblExamineLogs[i].CAction;\r\n              }\r\n            }\r\n          }\r\n        }\r\n        this.getHouseList()\r\n        this.dialogService.open(ref)\r\n      }\r\n    })\r\n  }\r\n\r\n  saveReviewPostRes: SaveReviewPostParam\r\n\r\n  flattenAndFilter(data: any[][]): HouseReview[] {\r\n    const flattened: HouseReview[] = [];\r\n    for (const floorData of data) {\r\n      for (const house of floorData) {\r\n        if (house.CIsSelect && house.CIsEnable) {\r\n          flattened.push({\r\n            CHouseID: house.CHouseID,\r\n            CIsSelect: house.CIsSelect,\r\n            CFloor: house.CFloor,\r\n            CHouseHold: house.CHouseHold,\r\n          });\r\n        }\r\n      }\r\n    }\r\n    return flattened;\r\n  }\r\n\r\n  houseList2D: HouseList[][]\r\n\r\n  onSaveReview(ref: any) {\r\n    this.validation()\r\n    if (this.valid.errorMessages.length > 0) {\r\n      this.message.showErrorMSGs(this.valid.errorMessages);\r\n      return\r\n    }\r\n    this.saveReviewPostRes = {\r\n      CBuildCaseId: this.searchQuery.selectedBuildCase.value,\r\n      CReviewId: this.selectedReview.CReviewId,\r\n      CReviewType: this.selectedReview.selectedReviewType.value,\r\n      CReviewName: this.selectedReview.CReviewName,\r\n      CSort: this.selectedReview?.CSort,\r\n      CStatus: this.selectedReview.seletedStatus.value,\r\n      CFile: this.imageUrl ? this.imageUrl.CFileUpload : undefined,\r\n      CExamineNote: this.selectedReview.CExamineNote,\r\n      HouseReviews: this.houseList2D != null && this.houseList2D != undefined && this.houseList2D.length > 0\r\n        ? this.flattenAndFilter(this.houseList2D)\r\n        : [],\r\n    }\r\n    this.reviewService.SaveReview(this.saveReviewPostRes).subscribe(res => {\r\n      if (res && res.body! && res.body.StatusCode! === 0) {\r\n        this.message.showSucessMSG(\"執行成功\");\r\n        this.clearImage()\r\n        this.getReviewList()\r\n        ref.close();\r\n      } else {\r\n        this.message.showErrorMSG(res && res.body && res.body.Message!);\r\n      }\r\n    })\r\n  }\r\n\r\n  onSearch() {\r\n    let previous_search = {\r\n      CReviewName: this.searchQuery.CReviewName,\r\n      CSelectedBuildCase: this.searchQuery.selectedBuildCase,\r\n      CSeletedStatus: this.searchQuery.seletedStatus,\r\n      CReviewType: this.searchQuery.selectedReviewType,\r\n      CExamineStatus: this.searchQuery.selectedExamineStatus,\r\n    }\r\n    LocalStorageService.AddSessionStorage(STORAGE_KEY.REVIEW_SEARCH, JSON.stringify(previous_search));\r\n    this.getReviewList()\r\n  }\r\n\r\n  pageChanged(newPage: number) {\r\n    this.pageIndex = newPage;\r\n    this.getReviewList()\r\n  }\r\n\r\n  bodyRequest: GetHouseListArgs\r\n\r\n  groupByFloor(customerData: HouseList[], isDefaut?: any): HouseList[][] {\r\n    const groupedData: HouseList[][] = [];\r\n    const uniqueFloors = Array.from(new Set(\r\n      customerData.map(customer => customer.CFloor).filter(floor => floor !== null) as number[]\r\n    ));\r\n    for (const floor of uniqueFloors) {\r\n      groupedData.push([]);\r\n    }\r\n    for (const customer of customerData) {\r\n      const floorIndex = uniqueFloors.indexOf(customer.CFloor as number);\r\n      if (floorIndex !== -1) {\r\n        groupedData[floorIndex].push({\r\n          CIsSelect: customer?.CIsSelect || false,\r\n          CHouseID: customer.CID,\r\n          CHouseType: customer.CHouseType,\r\n          CFloor: customer.CFloor,\r\n          CHouseHold: customer.CHouseHold,\r\n          CIsEnable: customer.CIsEnable,\r\n        });\r\n      }\r\n    }\r\n    return groupedData;\r\n  }\r\n\r\n\r\n  houseListEnable: any[]\r\n\r\n\r\n  addCIsSelectToA(A: any[], B: any[]): any[] {\r\n    const mapB = new Map(B.map(item => [`${item.CHouseHold}-${item.CFloor}`, item.CIsSelect]));\r\n    return A.map(item => {\r\n      const key = `${item.CHouseHold}-${item.CFloor}`;\r\n      return {\r\n        ...item,\r\n        CIsSelect: mapB.has(key) ? mapB.get(key) : false\r\n      };\r\n    });\r\n  }\r\n\r\n  sortByFloorDescending(arr: GetHouseListRes[]): GetHouseListRes[] {\r\n    return arr.sort((a, b) => (b.CFloor ?? 0) - (a.CFloor ?? 0));\r\n  }\r\n\r\n  getHouseList() {\r\n    return this._houseService.apiHouseGetHouseListPost$Json({\r\n      body: { CBuildCaseID: this.searchQuery.selectedBuildCase.value, CIsPagi: false }\r\n    }).pipe(\r\n      tap(res => {\r\n        if (res && res.StatusCode === 0 && res.Entries) {\r\n          const rest = this.sortByFloorDescending(res.Entries)\r\n          this.houseListEnable = [...rest]\r\n          if (this.selectedReview.CReviewId) {\r\n            this.houseList2D = this.groupByFloor(this.addCIsSelectToA([...this.houseListEnable], this.selectedReview.reviewHouseHolds ? [...this.selectedReview.reviewHouseHolds] : []))\r\n          } else {\r\n            this.houseList2D = this.groupByFloor([...rest])\r\n          }\r\n          this.isHouseList = true\r\n        }\r\n      }),\r\n\r\n    ).subscribe();\r\n  }\r\n\r\n\r\n  reviewList: GetReviewListRes[] | undefined\r\n\r\n  isNew = true\r\n\r\n  openPdfInNewTab(CFileUrl?: any) {\r\n    if (CFileUrl) {\r\n      this.utilityService.openFileNewTab(CFileUrl)\r\n    }\r\n  }\r\n\r\n  getReviewList() {\r\n    return this._reviewService.apiReviewGetReviewListPost$Json({\r\n      body: {\r\n        PageIndex: this.pageIndex,\r\n        PageSize: this.pageSize,\r\n        CReviewName: this.searchQuery.CReviewName,\r\n        CBuildCaseID: this.searchQuery.selectedBuildCase.value,\r\n        CStatus: this.searchQuery.seletedStatus.value,\r\n        CReviewType: this.searchQuery.selectedReviewType.value,\r\n        CExamineStatus: this.searchQuery.selectedExamineStatus.value,\r\n      }\r\n    }).pipe(\r\n      tap(res => {\r\n        if (res.Entries && res.StatusCode == 0) {\r\n          this.reviewList = res.Entries\r\n          this.totalRecords = res.TotalItems!\r\n        }\r\n      })\r\n    ).subscribe()\r\n  }\r\n\r\n\r\n  onSelectionChangeBuildCase() {\r\n    if (this.searchQuery.selectedBuildCase.value) {\r\n      this.getReviewList()\r\n    }\r\n  }\r\n\r\n  getUserBuildCase() {\r\n    this._buildCaseService.apiBuildCaseGetUserBuildCasePost$Json({ body: {} }).pipe(\r\n      tap(res => {\r\n        if (res.Entries && res.StatusCode == 0) {\r\n          this.userBuildCaseOptions = res.Entries.map(res => {\r\n            return {\r\n              label: res.CBuildCaseName,\r\n              value: res.cID\r\n            }\r\n          })\r\n          if (LocalStorageService.GetSessionStorage(STORAGE_KEY.REVIEW_SEARCH) != null\r\n            && LocalStorageService.GetSessionStorage(STORAGE_KEY.REVIEW_SEARCH) != undefined\r\n            && LocalStorageService.GetSessionStorage(STORAGE_KEY.REVIEW_SEARCH) != \"\") {\r\n            let previous_search = JSON.parse(LocalStorageService.GetSessionStorage(STORAGE_KEY.REVIEW_SEARCH));\r\n            if (previous_search.CSelectedBuildCase != null && previous_search.CSelectedBuildCase != undefined) {\r\n              let index = this.userBuildCaseOptions.findIndex((x: any) => x.value == previous_search.CSelectedBuildCase.value)\r\n              this.searchQuery.selectedBuildCase = this.userBuildCaseOptions[index]\r\n            } else {\r\n              this.searchQuery.selectedBuildCase = this.userBuildCaseOptions[0]\r\n            }\r\n          }\r\n          else {\r\n            this.searchQuery.selectedBuildCase = this.userBuildCaseOptions[0]\r\n          }\r\n          if (this.searchQuery.selectedBuildCase.value) {\r\n            this.getReviewList()\r\n          }\r\n        }\r\n      }),\r\n    ).subscribe()\r\n  }\r\n\r\n  openModel(ref: any, item?: any) {\r\n    this.latestAction = 0\r\n    this.isHouseList = false\r\n    this.isNew = true\r\n    this.clearImage()\r\n    this.selectedReview = {\r\n      selectedReviewType: this.reviewTypeOptions[0],\r\n      seletedStatus: this.statusOptions[0],\r\n      selectedExamineStatus: this.examineStatusOptions[0],\r\n      CReviewName: '',\r\n      CSort: 0,\r\n      CFileUrl: '',\r\n      CExamineNote: '',\r\n      CIsSelectAll: false\r\n    }\r\n    if (item) {\r\n      this.isNew = false\r\n      this.getReviewById(item, ref)\r\n    } else {\r\n      this.isNew = true\r\n      this.getHouseList()\r\n      this.dialogService.open(ref)\r\n    }\r\n  }\r\n\r\n  formatDate(CChangeDate: string): string {\r\n    if (CChangeDate) {\r\n      return moment(CChangeDate).format('YYYY-MM-DDTHH:mm:ss')\r\n    }\r\n    return ''\r\n  }\r\n\r\n  onSubmit(ref: any) {\r\n\r\n  }\r\n\r\n  onClose(ref: any) {\r\n    ref.close();\r\n  }\r\n\r\n\r\n  validation() {\r\n    this.valid.clear();\r\n    if (this.isNew && !this.imageUrl) {\r\n      this.valid.addErrorMessage(`前台圖片`);\r\n    }\r\n    this.valid.required('[送審說明]', this.selectedReview.CExamineNote)\r\n  }\r\n\r\n  getActionName(actionID: number | undefined) {\r\n    let textR = \"\";\r\n    if (actionID != undefined) {\r\n      switch (actionID) {\r\n        case 1:\r\n          textR = \"傳送\";\r\n          break;\r\n        case 2:\r\n          textR = \"通過\";\r\n          break;\r\n        case 3:\r\n          textR = \"駁回\";\r\n          break;\r\n        default:\r\n          break;\r\n      }\r\n    }\r\n    return textR;\r\n  }\r\n\r\n  checkAllHouseIsValid(household: string | null | undefined, floor: number | null | undefined) {\r\n    let count = 0;\r\n    let total = 0;\r\n    if (household != null) {\r\n      for (let i = 1; i < this.houseList2D.length; i++) {\r\n        this.houseList2D[i].map(val => {\r\n          if (val.CHouseHold && val.CHouseHold == household) {\r\n            total++;\r\n            if (val.CIsEnable == true) {\r\n              count++;\r\n            }\r\n          }\r\n        });\r\n      }\r\n    }\r\n    if (floor != null) {\r\n      for (let i = 0; i < this.houseList2D.length; i++) {\r\n        this.houseList2D[i].map(val => {\r\n          if (val.CFloor == floor) {\r\n            total++;\r\n            if (val.CIsEnable == true) {\r\n              count++;\r\n            }\r\n          }\r\n        });\r\n      }\r\n    }\r\n\r\n    if (count == total) {\r\n      return false;\r\n    }\r\n    return true;\r\n  }\r\n}\r\n"], "mappings": ";AAAA,SAASA,SAAS,EAAsBC,SAAS,QAAQ,eAAe;AACxE,SAASC,YAAY,QAAQ,mCAAmC;AAChE,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,kBAAkB,QAAyB,gBAAgB;AAIpE,SAASC,kBAAkB,EAAEC,YAAY,QAAQ,mCAAmC;AACpF,SAASC,aAAa,QAAQ,wCAAwC;AAGtE,SAASC,GAAG,QAAQ,MAAM;AAC1B,SAASC,mBAAmB,QAAQ,mBAAmB;AACvD,OAAO,KAAKC,MAAM,MAAM,QAAQ;AAChC,SAASC,YAAY,QAAQ,kCAAkC;AAE/D,SAASC,kBAAkB,EAAEC,cAAc,QAAQ,sBAAsB;AAEzE,SAASC,mBAAmB,QAAQ,+CAA+C;AACnF,SAASC,WAAW,QAAQ,kCAAkC;AAoEvD,IAAMC,iCAAiC,GAAvC,MAAMA,iCAAkC,SAAQT,aAAa;EAClEU,YACUC,MAAmB,EACnBC,aAA8B,EAC9BC,OAAuB,EACvBC,KAAuB,EACvBC,aAA2B,EAC3BC,iBAAmC,EACnCC,cAA6B,EAC7BC,aAAkC,EAClCC,cAA8B;IACpC,KAAK,CAACR,MAAM,CAAC;IATP,KAAAA,MAAM,GAANA,MAAM;IACN,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,cAAc,GAAdA,cAAc;IAGf,KAAAC,SAAS,GAAG,CAAC;IACb,KAAAC,QAAQ,GAAG,EAAE;IACb,KAAAC,SAAS,GAAG,CAAC;IACb,KAAAC,YAAY,GAAG,CAAC;IAEzB,KAAAC,iBAAiB,GAAG,CAAC;MACnBC,KAAK,EAAE,CAAC;MACRC,KAAK,EAAE,KAAK,CAAE;KACf,EAAE;MACDD,KAAK,EAAE,CAAC;MACRC,KAAK,EAAE,KAAK,CAAE;KACf,CACA;IAED,KAAAC,sBAAsB,GAAG,CACvB;MACEF,KAAK,EAAE,CAAC,CAAC;MACTC,KAAK,EAAE;KACR,EACD;MACED,KAAK,EAAE,CAAC;MACRC,KAAK,EAAE,KAAK,CAAE;KACf,EAAE;MACDD,KAAK,EAAE,CAAC;MACRC,KAAK,EAAE,KAAK,CAAE;KACf,CACF;IAED,KAAAE,oBAAoB,GAAG,CACrB;MACEH,KAAK,EAAE,CAAC,CAAC;MACTC,KAAK,EAAE,KAAK,CAAC;KAEd,EAAE;MACDD,KAAK,EAAE,CAAC;MACRC,KAAK,EAAE,KAAK,CAAC;KAEd,EACD;MACED,KAAK,EAAE,CAAC;MACRC,KAAK,EAAE,KAAK,CAAC;KAEd,CACF;IAED,KAAAG,yBAAyB,GAAG,CAAC;MAC3BJ,KAAK,EAAE,CAAC,CAAC;MACTC,KAAK,EAAE;KACR,EACD;MACED,KAAK,EAAE,CAAC;MACRC,KAAK,EAAE,KAAK,CAAC;KAEd,EAAE;MACDD,KAAK,EAAE,CAAC;MACRC,KAAK,EAAE,KAAK,CAAC;KAEd,EACD;MACED,KAAK,EAAE,CAAC;MACRC,KAAK,EAAE,KAAK,CAAC;KACd,CACA;IAED,KAAAI,aAAa,GAAG,CAAC;MACfL,KAAK,EAAE,CAAC;MAAE;MACVC,KAAK,EAAE,IAAI,CAAC;KACb,EAAE;MACDD,KAAK,EAAE,CAAC;MACRC,KAAK,EAAE,IAAI,CAAC;KACb,CAAE;IAEH,KAAAK,kBAAkB,GAAG,CAAC;MACpBN,KAAK,EAAE,CAAC,CAAC;MACTC,KAAK,EAAE;KACR,EAAE;MACDD,KAAK,EAAE,CAAC;MAAE;MACVC,KAAK,EAAE,IAAI,CAAC;KACb,EAAE;MACDD,KAAK,EAAE,CAAC;MACRC,KAAK,EAAE,IAAI,CAAC;KACb,CAAE;IAKH,KAAAM,gBAAgB,GAAU,CAAC;MAAEN,KAAK,EAAE,IAAI;MAAED,KAAK,EAAE;IAAE,CAAE,CAAC;IAqCtD,KAAAQ,QAAQ,GAAkB,IAAI;IAC9B,KAAAC,QAAQ,GAAQC,SAAS;IAsCzB,KAAAC,WAAW,GAAG,KAAK;IAiDnB,KAAAC,YAAY,GAAQ,CAAC;IAsLrB,KAAAC,KAAK,GAAG,IAAI;EA3YM;EA6FTC,QAAQA,CAAA;IACf,IAAIhC,mBAAmB,CAACiC,iBAAiB,CAAChC,WAAW,CAACiC,aAAa,CAAC,IAAI,IAAI,IACvElC,mBAAmB,CAACiC,iBAAiB,CAAChC,WAAW,CAACiC,aAAa,CAAC,IAAIN,SAAS,IAC7E5B,mBAAmB,CAACiC,iBAAiB,CAAChC,WAAW,CAACiC,aAAa,CAAC,IAAI,EAAE,EAAE;MAC3E,IAAIC,eAAe,GAAGC,IAAI,CAACC,KAAK,CAACrC,mBAAmB,CAACiC,iBAAiB,CAAChC,WAAW,CAACiC,aAAa,CAAC,CAAC;MAClG,IAAI,CAACI,WAAW,GAAG;QACjBC,iBAAiB,EAAE,IAAI;QACvBC,kBAAkB,EAAE,IAAI,CAACpB,sBAAsB,CAACqB,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACxB,KAAK,IAAIiB,eAAe,CAACQ,WAAW,CAACzB,KAAK,CAAC,GACnG,IAAI,CAACE,sBAAsB,CAACqB,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACxB,KAAK,IAAIiB,eAAe,CAACQ,WAAW,CAACzB,KAAK,CAAC,GACnF,IAAI,CAACE,sBAAsB,CAAC,CAAC,CAAC;QAClCwB,qBAAqB,EAAE,IAAI,CAACtB,yBAAyB,CAACmB,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACxB,KAAK,IAAIiB,eAAe,CAACU,cAAc,CAAC3B,KAAK,CAAC,GAC5G,IAAI,CAACI,yBAAyB,CAACmB,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACxB,KAAK,IAAIiB,eAAe,CAACU,cAAc,CAAC3B,KAAK,CAAC,GACzF,IAAI,CAACI,yBAAyB,CAAC,CAAC,CAAC;QACrCwB,aAAa,EAAE,IAAI,CAACtB,kBAAkB,CAACiB,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACxB,KAAK,IAAIiB,eAAe,CAACY,cAAc,CAAC7B,KAAK,CAAC,GAC7F,IAAI,CAACM,kBAAkB,CAACiB,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACxB,KAAK,IAAIiB,eAAe,CAACY,cAAc,CAAC7B,KAAK,CAAC,GAClF,IAAI,CAACM,kBAAkB,CAAC,CAAC,CAAC;QAC9BwB,WAAW,EAAEb,eAAe,CAACa;OAC9B;IACH,CAAC,MACI;MACH,IAAI,CAACV,WAAW,GAAG;QACjBC,iBAAiB,EAAE,IAAI;QACvBC,kBAAkB,EAAE,IAAI,CAACpB,sBAAsB,CAAC,CAAC,CAAC;QAClDwB,qBAAqB,EAAE,IAAI,CAACtB,yBAAyB,CAAC,CAAC,CAAC;QACxDwB,aAAa,EAAE,IAAI,CAACtB,kBAAkB,CAAC,CAAC,CAAC;QACzCwB,WAAW,EAAE;OACd;IACH;IACA,IAAI,CAACC,gBAAgB,EAAE;EACzB;EAMAC,UAAUA,CAAA;IACR,IAAI,IAAI,CAACvB,QAAQ,EAAE;MACjB,IAAI,CAACA,QAAQ,GAAG,IAAI;MACpB,IAAI,CAACD,QAAQ,GAAG,IAAI;MACpB,IAAI,IAAI,CAACyB,SAAS,EAAE;QAClB,IAAI,CAACA,SAAS,CAACC,aAAa,CAAClC,KAAK,GAAG,IAAI,CAAC,CAAC;MAC7C;IACF;EACF;EAEAmC,cAAcA,CAACC,KAAU;IACvB,MAAMC,IAAI,GAASD,KAAK,CAACE,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC;IACxC,MAAMC,SAAS,GAAG,uBAAuB;IACzC,IAAI,CAACA,SAAS,CAACC,IAAI,CAACJ,IAAI,CAACK,IAAI,CAAC,EAAE;MAC9B,IAAI,CAACtD,OAAO,CAACuD,YAAY,CAAC,cAAc,CAAC;MACzC;IACF;IACA,IAAIN,IAAI,EAAE;MACR,IAAI,CAAC7B,QAAQ,GAAG6B,IAAI,CAACK,IAAI;MACzB,MAAME,MAAM,GAAG,IAAIC,UAAU,EAAE;MAC/BD,MAAM,CAACE,MAAM,GAAIC,CAAM,IAAI;QACzB,IAAI,CAACtC,QAAQ,GAAG;UACduC,KAAK,EAAEX,IAAI,CAACK,IAAI;UAChBO,KAAK,EAAEF,CAAC,CAACT,MAAM,EAAEY,MAAM,EAAEC,QAAQ,EAAE,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;UACjDC,IAAI,EAAEhB,IAAI,CAACK,IAAI,CAACY,QAAQ,CAAC,KAAK,CAAC,GAAGjB,IAAI,GAAGA,IAAI;UAC7CkB,WAAW,EAAElB,IAAI;UACjBmB,SAAS,EAAE7E,YAAY,CAAC8E;SACzB;QACD,IAAI,IAAI,CAACxB,SAAS,EAAE;UAClB,IAAI,CAACA,SAAS,CAACC,aAAa,CAAClC,KAAK,GAAG,IAAI;QAC3C;MACF,CAAC;MACD4C,MAAM,CAACc,aAAa,CAACrB,IAAI,CAAC;IAC5B;EACF;EAMAsB,oBAAoBA,CAACC,GAAU;IAC7B,OAAOA,GAAG,CAACC,KAAK,CAAEC,IAAyB,IAAKA,IAAI,CAACC,SAAS,CAAC;EACjE;EAEAC,uBAAuBA,CAACC,KAAa;IACnC,IAAI,IAAI,CAACtD,WAAW,EAAE;MACpB,IAAIsD,KAAK,GAAG,CAAC,IAAIA,KAAK,IAAI,IAAI,CAACC,WAAW,CAAC,CAAC,CAAC,CAACC,MAAM,EAAE;QACpD,MAAM,IAAIC,KAAK,CAAC,8DAA8D,CAAC;MACjF;MACA,KAAK,MAAMC,SAAS,IAAI,IAAI,CAACH,WAAW,EAAE;QACxC,IAAID,KAAK,IAAII,SAAS,CAACF,MAAM,IAAI,CAACE,SAAS,CAACJ,KAAK,CAAC,CAACF,SAAS,EAAE;UAC5D,OAAO,KAAK,CAAC,CAAC;QAChB;MACF;MACA,OAAO,IAAI,CAAC,CAAC;IACf;IACA,OAAO,KAAK;EACd;EAEAO,gBAAgBA,CAACC,OAAgB,EAAEN,KAAa;IAC9C,IAAIA,KAAK,GAAG,CAAC,EAAE;MACb,MAAM,IAAIG,KAAK,CAAC,qDAAqD,CAAC;IACxE;IACA,KAAK,MAAMC,SAAS,IAAI,IAAI,CAACH,WAAW,EAAE;MACxC,IAAID,KAAK,GAAGI,SAAS,CAACF,MAAM,EAAE;QAAE;QAC9BE,SAAS,CAACJ,KAAK,CAAC,CAACF,SAAS,GAAGQ,OAAO;MACtC;IACF;EACF;EAEAC,YAAYA,CAACD,OAAgB,EAAEX,GAAU;IACvC,KAAK,MAAME,IAAI,IAAIF,GAAG,EAAE;MACtBE,IAAI,CAACC,SAAS,GAAGQ,OAAO;IAC1B;EACF;EAGAE,cAAcA,CAACzE,KAAU,EAAE0E,OAAc;IACvC,KAAK,MAAMZ,IAAI,IAAIY,OAAO,EAAE;MAC1B,IAAIZ,IAAI,CAAC9D,KAAK,KAAKA,KAAK,EAAE;QACxB,OAAO8D,IAAI;MACb;IACF;IACA,OAAO,IAAI;EACb;EAGAa,aAAaA,CAACb,IAAS,EAAEc,GAAQ;IAE/B,IAAI,CAACpF,cAAc,CAACqF,+BAA+B,CAAC;MAClDC,IAAI,EAAEhB,IAAI,CAACiB;KACZ,CAAC,CAACC,SAAS,CAACC,GAAG,IAAG;MACjB,IAAIA,GAAG,CAACC,OAAO,IAAID,GAAG,CAACE,UAAU,IAAI,CAAC,EAAE;QACtC,MAAMC,IAAI,GAAGH,GAAG,CAACC,OAAO;QACxB,IAAI,CAACG,cAAc,GAAG;UACpBC,YAAY,EAAEF,IAAI,CAACG,SAAS,EAAED,YAAY;UAC1CP,SAAS,EAAEK,IAAI,CAACG,SAAS,EAAER,SAAS;UACpCtD,WAAW,EAAE2D,IAAI,CAACG,SAAS,EAAE9D,WAAW;UACxCK,WAAW,EAAEsD,IAAI,CAACG,SAAS,EAAEzD,WAAW,GAAGsD,IAAI,CAACG,SAAS,EAAEzD,WAAW,GAAG,EAAE;UAC3E0D,KAAK,EAAEJ,IAAI,CAACG,SAAS,EAAEC,KAAK;UAC5BC,OAAO,EAAEL,IAAI,CAACG,SAAS,EAAEE,OAAO;UAChCC,QAAQ,EAAEN,IAAI,CAACG,SAAS,EAAEG,QAAQ;UAClCC,YAAY,EAAEP,IAAI,EAAEO,YAAY,GAAGP,IAAI,EAAEO,YAAY,GAAG,EAAE;UAC1D/D,aAAa,EAAEwD,IAAI,CAACG,SAAS,EAAEE,OAAO,GAAG,IAAI,CAAChB,cAAc,CAACW,IAAI,CAACG,SAAS,EAAEE,OAAO,EAAE,IAAI,CAACpF,aAAa,CAAC,GAAG,IAAI,CAACA,aAAa,CAAC,CAAC,CAAC;UACjIiB,kBAAkB,EAAE8D,IAAI,CAACG,SAAS,EAAE9D,WAAW,GAAG,IAAI,CAACgD,cAAc,CAACW,IAAI,CAACG,SAAS,EAAE9D,WAAW,EAAE,IAAI,CAAC1B,iBAAiB,CAAC,GAAG,IAAI,CAACA,iBAAiB,CAAC,CAAC,CAAC;UACtJ6F,cAAc,EAAER,IAAI,CAACQ,cAAc;UACnCC,gBAAgB,EAAET,IAAI,EAAES,gBAAgB,EAAEC,MAAM,CAAEC,CAAM,IAAKA,CAAC,CAAChC,SAAS,CAAC;UACzEwB,SAAS,EAAEH,IAAI,CAACG;SACjB;QAED,IAAIH,IAAI,IAAIA,IAAI,EAAEQ,cAAc,IAAIR,IAAI,EAAEQ,cAAc,CAACzB,MAAM,EAAE;UAC/D,IAAIiB,IAAI,EAAEQ,cAAc,CAACzB,MAAM,KAAK,CAAC,EAAE,OAAOzD,SAAS;UACvD,IAAI,CAACE,YAAY,GAAGwE,IAAI,EAAEQ,cAAc,CAAC,CAAC,CAAC,CAACI,OAAO;UACnD,IAAIC,UAAU,GAAGb,IAAI,EAAEQ,cAAc,CAAC,CAAC,CAAC,CAACM,SAAS,GAAG,IAAIC,IAAI,CAACf,IAAI,EAAEQ,cAAc,CAAC,CAAC,CAAC,CAACM,SAAS,CAAC,GAAG,EAAE;UACrG,KAAK,IAAIH,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGX,IAAI,CAACQ,cAAc,CAACzB,MAAM,EAAE4B,CAAC,EAAE,EAAE;YACnD,IAAIX,IAAI,CAACQ,cAAc,CAACG,CAAC,CAAC,CAACG,SAAS,EAAE;cACpC,MAAME,WAAW,GAAG,IAAID,IAAI,CAACf,IAAI,CAACQ,cAAc,CAACG,CAAC,CAAC,CAACG,SAAU,CAAC;cAC/D,IAAIE,WAAW,GAAGH,UAAU,EAAE;gBAC5BA,UAAU,GAAGG,WAAW;gBACxB,IAAI,CAACxF,YAAY,GAAGwE,IAAI,EAAEQ,cAAc,CAACG,CAAC,CAAC,CAACC,OAAO;cACrD;YACF;UACF;QACF;QACA,IAAI,CAACK,YAAY,EAAE;QACnB,IAAI,CAAClH,aAAa,CAACmH,IAAI,CAAC1B,GAAG,CAAC;MAC9B;IACF,CAAC,CAAC;EACJ;EAIA2B,gBAAgBA,CAACnB,IAAa;IAC5B,MAAMoB,SAAS,GAAkB,EAAE;IACnC,KAAK,MAAMnC,SAAS,IAAIe,IAAI,EAAE;MAC5B,KAAK,MAAMqB,KAAK,IAAIpC,SAAS,EAAE;QAC7B,IAAIoC,KAAK,CAAC1C,SAAS,IAAI0C,KAAK,CAACC,SAAS,EAAE;UACtCF,SAAS,CAACG,IAAI,CAAC;YACbC,QAAQ,EAAEH,KAAK,CAACG,QAAQ;YACxB7C,SAAS,EAAE0C,KAAK,CAAC1C,SAAS;YAC1B8C,MAAM,EAAEJ,KAAK,CAACI,MAAM;YACpBC,UAAU,EAAEL,KAAK,CAACK;WACnB,CAAC;QACJ;MACF;IACF;IACA,OAAON,SAAS;EAClB;EAIAO,YAAYA,CAACnC,GAAQ;IACnB,IAAI,CAACoC,UAAU,EAAE;IACjB,IAAI,IAAI,CAAC3H,KAAK,CAAC4H,aAAa,CAAC9C,MAAM,GAAG,CAAC,EAAE;MACvC,IAAI,CAAC/E,OAAO,CAAC8H,aAAa,CAAC,IAAI,CAAC7H,KAAK,CAAC4H,aAAa,CAAC;MACpD;IACF;IACA,IAAI,CAACE,iBAAiB,GAAG;MACvB7B,YAAY,EAAE,IAAI,CAAClE,WAAW,CAACC,iBAAiB,CAACrB,KAAK;MACtD+E,SAAS,EAAE,IAAI,CAACM,cAAc,CAACN,SAAS;MACxCtD,WAAW,EAAE,IAAI,CAAC4D,cAAc,CAAC/D,kBAAkB,CAACtB,KAAK;MACzD8B,WAAW,EAAE,IAAI,CAACuD,cAAc,CAACvD,WAAW;MAC5C0D,KAAK,EAAE,IAAI,CAACH,cAAc,EAAEG,KAAK;MACjCC,OAAO,EAAE,IAAI,CAACJ,cAAc,CAACzD,aAAa,CAAC5B,KAAK;MAChDiD,KAAK,EAAE,IAAI,CAACxC,QAAQ,GAAG,IAAI,CAACA,QAAQ,CAAC8C,WAAW,GAAG7C,SAAS;MAC5DiF,YAAY,EAAE,IAAI,CAACN,cAAc,CAACM,YAAY;MAC9CyB,YAAY,EAAE,IAAI,CAAClD,WAAW,IAAI,IAAI,IAAI,IAAI,CAACA,WAAW,IAAIxD,SAAS,IAAI,IAAI,CAACwD,WAAW,CAACC,MAAM,GAAG,CAAC,GAClG,IAAI,CAACoC,gBAAgB,CAAC,IAAI,CAACrC,WAAW,CAAC,GACvC;KACL;IACD,IAAI,CAACzE,aAAa,CAAC4H,UAAU,CAAC,IAAI,CAACF,iBAAiB,CAAC,CAACnC,SAAS,CAACC,GAAG,IAAG;MACpE,IAAIA,GAAG,IAAIA,GAAG,CAACH,IAAK,IAAIG,GAAG,CAACH,IAAI,CAACK,UAAW,KAAK,CAAC,EAAE;QAClD,IAAI,CAAC/F,OAAO,CAACkI,aAAa,CAAC,MAAM,CAAC;QAClC,IAAI,CAACtF,UAAU,EAAE;QACjB,IAAI,CAACuF,aAAa,EAAE;QACpB3C,GAAG,CAAC4C,KAAK,EAAE;MACb,CAAC,MAAM;QACL,IAAI,CAACpI,OAAO,CAACuD,YAAY,CAACsC,GAAG,IAAIA,GAAG,CAACH,IAAI,IAAIG,GAAG,CAACH,IAAI,CAAC2C,OAAQ,CAAC;MACjE;IACF,CAAC,CAAC;EACJ;EAEAC,QAAQA,CAAA;IACN,IAAIzG,eAAe,GAAG;MACpBa,WAAW,EAAE,IAAI,CAACV,WAAW,CAACU,WAAW;MACzC6F,kBAAkB,EAAE,IAAI,CAACvG,WAAW,CAACC,iBAAiB;MACtDQ,cAAc,EAAE,IAAI,CAACT,WAAW,CAACQ,aAAa;MAC9CH,WAAW,EAAE,IAAI,CAACL,WAAW,CAACE,kBAAkB;MAChDK,cAAc,EAAE,IAAI,CAACP,WAAW,CAACM;KAClC;IACD5C,mBAAmB,CAAC8I,iBAAiB,CAAC7I,WAAW,CAACiC,aAAa,EAAEE,IAAI,CAAC2G,SAAS,CAAC5G,eAAe,CAAC,CAAC;IACjG,IAAI,CAACsG,aAAa,EAAE;EACtB;EAEAO,WAAWA,CAACC,OAAe;IACzB,IAAI,CAAClI,SAAS,GAAGkI,OAAO;IACxB,IAAI,CAACR,aAAa,EAAE;EACtB;EAIAS,YAAYA,CAACC,YAAyB,EAAEC,QAAc;IACpD,MAAMC,WAAW,GAAkB,EAAE;IACrC,MAAMC,YAAY,GAAGC,KAAK,CAACC,IAAI,CAAC,IAAIC,GAAG,CACrCN,YAAY,CAACO,GAAG,CAACC,QAAQ,IAAIA,QAAQ,CAAC5B,MAAM,CAAC,CAACf,MAAM,CAAC4C,KAAK,IAAIA,KAAK,KAAK,IAAI,CAAa,CAC1F,CAAC;IACF,KAAK,MAAMA,KAAK,IAAIN,YAAY,EAAE;MAChCD,WAAW,CAACxB,IAAI,CAAC,EAAE,CAAC;IACtB;IACA,KAAK,MAAM8B,QAAQ,IAAIR,YAAY,EAAE;MACnC,MAAMU,UAAU,GAAGP,YAAY,CAACQ,OAAO,CAACH,QAAQ,CAAC5B,MAAgB,CAAC;MAClE,IAAI8B,UAAU,KAAK,CAAC,CAAC,EAAE;QACrBR,WAAW,CAACQ,UAAU,CAAC,CAAChC,IAAI,CAAC;UAC3B5C,SAAS,EAAE0E,QAAQ,EAAE1E,SAAS,IAAI,KAAK;UACvC6C,QAAQ,EAAE6B,QAAQ,CAACI,GAAG;UACtBC,UAAU,EAAEL,QAAQ,CAACK,UAAU;UAC/BjC,MAAM,EAAE4B,QAAQ,CAAC5B,MAAM;UACvBC,UAAU,EAAE2B,QAAQ,CAAC3B,UAAU;UAC/BJ,SAAS,EAAE+B,QAAQ,CAAC/B;SACrB,CAAC;MACJ;IACF;IACA,OAAOyB,WAAW;EACpB;EAMAY,eAAeA,CAACC,CAAQ,EAAEC,CAAQ;IAChC,MAAMC,IAAI,GAAG,IAAIC,GAAG,CAACF,CAAC,CAACT,GAAG,CAAC1E,IAAI,IAAI,CAAC,GAAGA,IAAI,CAACgD,UAAU,IAAIhD,IAAI,CAAC+C,MAAM,EAAE,EAAE/C,IAAI,CAACC,SAAS,CAAC,CAAC,CAAC;IAC1F,OAAOiF,CAAC,CAACR,GAAG,CAAC1E,IAAI,IAAG;MAClB,MAAMsF,GAAG,GAAG,GAAGtF,IAAI,CAACgD,UAAU,IAAIhD,IAAI,CAAC+C,MAAM,EAAE;MAC/C,OAAO;QACL,GAAG/C,IAAI;QACPC,SAAS,EAAEmF,IAAI,CAACG,GAAG,CAACD,GAAG,CAAC,GAAGF,IAAI,CAACI,GAAG,CAACF,GAAG,CAAC,GAAG;OAC5C;IACH,CAAC,CAAC;EACJ;EAEAG,qBAAqBA,CAACC,GAAsB;IAC1C,OAAOA,GAAG,CAACC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK,CAACA,CAAC,CAAC9C,MAAM,IAAI,CAAC,KAAK6C,CAAC,CAAC7C,MAAM,IAAI,CAAC,CAAC,CAAC;EAC9D;EAEAR,YAAYA,CAAA;IACV,OAAO,IAAI,CAAC/G,aAAa,CAACsK,6BAA6B,CAAC;MACtD9E,IAAI,EAAE;QAAE+E,YAAY,EAAE,IAAI,CAACzI,WAAW,CAACC,iBAAiB,CAACrB,KAAK;QAAE8J,OAAO,EAAE;MAAK;KAC/E,CAAC,CAACC,IAAI,CACLvL,GAAG,CAACyG,GAAG,IAAG;MACR,IAAIA,GAAG,IAAIA,GAAG,CAACE,UAAU,KAAK,CAAC,IAAIF,GAAG,CAACC,OAAO,EAAE;QAC9C,MAAM8E,IAAI,GAAG,IAAI,CAACT,qBAAqB,CAACtE,GAAG,CAACC,OAAO,CAAC;QACpD,IAAI,CAAC+E,eAAe,GAAG,CAAC,GAAGD,IAAI,CAAC;QAChC,IAAI,IAAI,CAAC3E,cAAc,CAACN,SAAS,EAAE;UACjC,IAAI,CAACb,WAAW,GAAG,IAAI,CAAC8D,YAAY,CAAC,IAAI,CAACe,eAAe,CAAC,CAAC,GAAG,IAAI,CAACkB,eAAe,CAAC,EAAE,IAAI,CAAC5E,cAAc,CAACQ,gBAAgB,GAAG,CAAC,GAAG,IAAI,CAACR,cAAc,CAACQ,gBAAgB,CAAC,GAAG,EAAE,CAAC,CAAC;QAC9K,CAAC,MAAM;UACL,IAAI,CAAC3B,WAAW,GAAG,IAAI,CAAC8D,YAAY,CAAC,CAAC,GAAGgC,IAAI,CAAC,CAAC;QACjD;QACA,IAAI,CAACrJ,WAAW,GAAG,IAAI;MACzB;IACF,CAAC,CAAC,CAEH,CAACqE,SAAS,EAAE;EACf;EAOAkF,eAAeA,CAACxE,QAAc;IAC5B,IAAIA,QAAQ,EAAE;MACZ,IAAI,CAAChG,cAAc,CAACyK,cAAc,CAACzE,QAAQ,CAAC;IAC9C;EACF;EAEA6B,aAAaA,CAAA;IACX,OAAO,IAAI,CAAC/H,cAAc,CAAC4K,+BAA+B,CAAC;MACzDtF,IAAI,EAAE;QACJuF,SAAS,EAAE,IAAI,CAACxK,SAAS;QACzByK,QAAQ,EAAE,IAAI,CAAC1K,QAAQ;QACvBkC,WAAW,EAAE,IAAI,CAACV,WAAW,CAACU,WAAW;QACzC+H,YAAY,EAAE,IAAI,CAACzI,WAAW,CAACC,iBAAiB,CAACrB,KAAK;QACtDyF,OAAO,EAAE,IAAI,CAACrE,WAAW,CAACQ,aAAa,CAAC5B,KAAK;QAC7CyB,WAAW,EAAE,IAAI,CAACL,WAAW,CAACE,kBAAkB,CAACtB,KAAK;QACtD2B,cAAc,EAAE,IAAI,CAACP,WAAW,CAACM,qBAAqB,CAAC1B;;KAE1D,CAAC,CAAC+J,IAAI,CACLvL,GAAG,CAACyG,GAAG,IAAG;MACR,IAAIA,GAAG,CAACC,OAAO,IAAID,GAAG,CAACE,UAAU,IAAI,CAAC,EAAE;QACtC,IAAI,CAACoF,UAAU,GAAGtF,GAAG,CAACC,OAAO;QAC7B,IAAI,CAACpF,YAAY,GAAGmF,GAAG,CAACuF,UAAW;MACrC;IACF,CAAC,CAAC,CACH,CAACxF,SAAS,EAAE;EACf;EAGAyF,0BAA0BA,CAAA;IACxB,IAAI,IAAI,CAACrJ,WAAW,CAACC,iBAAiB,CAACrB,KAAK,EAAE;MAC5C,IAAI,CAACuH,aAAa,EAAE;IACtB;EACF;EAEAxF,gBAAgBA,CAAA;IACd,IAAI,CAACxC,iBAAiB,CAACmL,qCAAqC,CAAC;MAAE5F,IAAI,EAAE;IAAE,CAAE,CAAC,CAACiF,IAAI,CAC7EvL,GAAG,CAACyG,GAAG,IAAG;MACR,IAAIA,GAAG,CAACC,OAAO,IAAID,GAAG,CAACE,UAAU,IAAI,CAAC,EAAE;QACtC,IAAI,CAACwF,oBAAoB,GAAG1F,GAAG,CAACC,OAAO,CAACsD,GAAG,CAACvD,GAAG,IAAG;UAChD,OAAO;YACLhF,KAAK,EAAEgF,GAAG,CAAC2F,cAAc;YACzB5K,KAAK,EAAEiF,GAAG,CAAC4F;WACZ;QACH,CAAC,CAAC;QACF,IAAI/L,mBAAmB,CAACiC,iBAAiB,CAAChC,WAAW,CAACiC,aAAa,CAAC,IAAI,IAAI,IACvElC,mBAAmB,CAACiC,iBAAiB,CAAChC,WAAW,CAACiC,aAAa,CAAC,IAAIN,SAAS,IAC7E5B,mBAAmB,CAACiC,iBAAiB,CAAChC,WAAW,CAACiC,aAAa,CAAC,IAAI,EAAE,EAAE;UAC3E,IAAIC,eAAe,GAAGC,IAAI,CAACC,KAAK,CAACrC,mBAAmB,CAACiC,iBAAiB,CAAChC,WAAW,CAACiC,aAAa,CAAC,CAAC;UAClG,IAAIC,eAAe,CAAC0G,kBAAkB,IAAI,IAAI,IAAI1G,eAAe,CAAC0G,kBAAkB,IAAIjH,SAAS,EAAE;YACjG,IAAIuD,KAAK,GAAG,IAAI,CAAC0G,oBAAoB,CAACG,SAAS,CAAEtJ,CAAM,IAAKA,CAAC,CAACxB,KAAK,IAAIiB,eAAe,CAAC0G,kBAAkB,CAAC3H,KAAK,CAAC;YAChH,IAAI,CAACoB,WAAW,CAACC,iBAAiB,GAAG,IAAI,CAACsJ,oBAAoB,CAAC1G,KAAK,CAAC;UACvE,CAAC,MAAM;YACL,IAAI,CAAC7C,WAAW,CAACC,iBAAiB,GAAG,IAAI,CAACsJ,oBAAoB,CAAC,CAAC,CAAC;UACnE;QACF,CAAC,MACI;UACH,IAAI,CAACvJ,WAAW,CAACC,iBAAiB,GAAG,IAAI,CAACsJ,oBAAoB,CAAC,CAAC,CAAC;QACnE;QACA,IAAI,IAAI,CAACvJ,WAAW,CAACC,iBAAiB,CAACrB,KAAK,EAAE;UAC5C,IAAI,CAACuH,aAAa,EAAE;QACtB;MACF;IACF,CAAC,CAAC,CACH,CAACvC,SAAS,EAAE;EACf;EAEA+F,SAASA,CAACnG,GAAQ,EAAEd,IAAU;IAC5B,IAAI,CAAClD,YAAY,GAAG,CAAC;IACrB,IAAI,CAACD,WAAW,GAAG,KAAK;IACxB,IAAI,CAACE,KAAK,GAAG,IAAI;IACjB,IAAI,CAACmB,UAAU,EAAE;IACjB,IAAI,CAACqD,cAAc,GAAG;MACpB/D,kBAAkB,EAAE,IAAI,CAACvB,iBAAiB,CAAC,CAAC,CAAC;MAC7C6B,aAAa,EAAE,IAAI,CAACvB,aAAa,CAAC,CAAC,CAAC;MACpCqB,qBAAqB,EAAE,IAAI,CAACvB,oBAAoB,CAAC,CAAC,CAAC;MACnD2B,WAAW,EAAE,EAAE;MACf0D,KAAK,EAAE,CAAC;MACRE,QAAQ,EAAE,EAAE;MACZC,YAAY,EAAE,EAAE;MAChBqF,YAAY,EAAE;KACf;IACD,IAAIlH,IAAI,EAAE;MACR,IAAI,CAACjD,KAAK,GAAG,KAAK;MAClB,IAAI,CAAC8D,aAAa,CAACb,IAAI,EAAEc,GAAG,CAAC;IAC/B,CAAC,MAAM;MACL,IAAI,CAAC/D,KAAK,GAAG,IAAI;MACjB,IAAI,CAACwF,YAAY,EAAE;MACnB,IAAI,CAAClH,aAAa,CAACmH,IAAI,CAAC1B,GAAG,CAAC;IAC9B;EACF;EAEAqG,UAAUA,CAACC,WAAmB;IAC5B,IAAIA,WAAW,EAAE;MACf,OAAOxM,MAAM,CAACwM,WAAW,CAAC,CAACC,MAAM,CAAC,qBAAqB,CAAC;IAC1D;IACA,OAAO,EAAE;EACX;EAEAC,QAAQA,CAACxG,GAAQ,GAEjB;EAEAyG,OAAOA,CAACzG,GAAQ;IACdA,GAAG,CAAC4C,KAAK,EAAE;EACb;EAGAR,UAAUA,CAAA;IACR,IAAI,CAAC3H,KAAK,CAACiM,KAAK,EAAE;IAClB,IAAI,IAAI,CAACzK,KAAK,IAAI,CAAC,IAAI,CAACJ,QAAQ,EAAE;MAChC,IAAI,CAACpB,KAAK,CAACkM,eAAe,CAAC,MAAM,CAAC;IACpC;IACA,IAAI,CAAClM,KAAK,CAACmM,QAAQ,CAAC,QAAQ,EAAE,IAAI,CAACnG,cAAc,CAACM,YAAY,CAAC;EACjE;EAEA8F,aAAaA,CAACC,QAA4B;IACxC,IAAIC,KAAK,GAAG,EAAE;IACd,IAAID,QAAQ,IAAIhL,SAAS,EAAE;MACzB,QAAQgL,QAAQ;QACd,KAAK,CAAC;UACJC,KAAK,GAAG,IAAI;UACZ;QACF,KAAK,CAAC;UACJA,KAAK,GAAG,IAAI;UACZ;QACF,KAAK,CAAC;UACJA,KAAK,GAAG,IAAI;UACZ;QACF;UACE;MACJ;IACF;IACA,OAAOA,KAAK;EACd;EAEAC,oBAAoBA,CAACC,SAAoC,EAAEnD,KAAgC;IACzF,IAAIoD,KAAK,GAAG,CAAC;IACb,IAAIC,KAAK,GAAG,CAAC;IACb,IAAIF,SAAS,IAAI,IAAI,EAAE;MACrB,KAAK,IAAI9F,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAAC7B,WAAW,CAACC,MAAM,EAAE4B,CAAC,EAAE,EAAE;QAChD,IAAI,CAAC7B,WAAW,CAAC6B,CAAC,CAAC,CAACyC,GAAG,CAACwD,GAAG,IAAG;UAC5B,IAAIA,GAAG,CAAClF,UAAU,IAAIkF,GAAG,CAAClF,UAAU,IAAI+E,SAAS,EAAE;YACjDE,KAAK,EAAE;YACP,IAAIC,GAAG,CAACtF,SAAS,IAAI,IAAI,EAAE;cACzBoF,KAAK,EAAE;YACT;UACF;QACF,CAAC,CAAC;MACJ;IACF;IACA,IAAIpD,KAAK,IAAI,IAAI,EAAE;MACjB,KAAK,IAAI3C,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAAC7B,WAAW,CAACC,MAAM,EAAE4B,CAAC,EAAE,EAAE;QAChD,IAAI,CAAC7B,WAAW,CAAC6B,CAAC,CAAC,CAACyC,GAAG,CAACwD,GAAG,IAAG;UAC5B,IAAIA,GAAG,CAACnF,MAAM,IAAI6B,KAAK,EAAE;YACvBqD,KAAK,EAAE;YACP,IAAIC,GAAG,CAACtF,SAAS,IAAI,IAAI,EAAE;cACzBoF,KAAK,EAAE;YACT;UACF;QACF,CAAC,CAAC;MACJ;IACF;IAEA,IAAIA,KAAK,IAAIC,KAAK,EAAE;MAClB,OAAO,KAAK;IACd;IACA,OAAO,IAAI;EACb;CACD;AAzbyBE,UAAA,EAAvBhO,SAAS,CAAC,WAAW,CAAC,C,mEAAwB;AAvIpCe,iCAAiC,GAAAiN,UAAA,EAR7CjO,SAAS,CAAC;EACTkO,QAAQ,EAAE,gCAAgC;EAC1CC,WAAW,EAAE,6CAA6C;EAC1DC,SAAS,EAAE,CAAC,6CAA6C,CAAC;EAC1DC,UAAU,EAAE,IAAI;EAChBC,OAAO,EAAE,CAACnO,YAAY,EAAED,YAAY,EAAEI,YAAY,EAAEF,kBAAkB,EAAEK,mBAAmB,EAAEI,cAAc,EAAER,kBAAkB,EAAEO,kBAAkB;CACpJ,CAAC,C,EAEWI,iCAAiC,CAgkB7C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}