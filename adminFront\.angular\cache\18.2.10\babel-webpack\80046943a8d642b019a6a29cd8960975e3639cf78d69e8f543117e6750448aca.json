{"ast": null, "code": "import { map } from 'rxjs/operators';\nimport { BaseService } from '../base-service';\nimport { apiUserAddDataPost$Json } from '../fn/user/api-user-add-data-post-json';\nimport { apiUserAddDataPost$Plain } from '../fn/user/api-user-add-data-post-plain';\nimport { apiUserGetDataPost$Json } from '../fn/user/api-user-get-data-post-json';\nimport { apiUserGetDataPost$Plain } from '../fn/user/api-user-get-data-post-plain';\nimport { apiUserGetListPost$Json } from '../fn/user/api-user-get-list-post-json';\nimport { apiUserGetListPost$Plain } from '../fn/user/api-user-get-list-post-plain';\nimport { apiUserGetMenuPost$Json } from '../fn/user/api-user-get-menu-post-json';\nimport { apiUserGetMenuPost$Plain } from '../fn/user/api-user-get-menu-post-plain';\nimport { apiUserGetUserLogPost$Json } from '../fn/user/api-user-get-user-log-post-json';\nimport { apiUserGetUserLogPost$Plain } from '../fn/user/api-user-get-user-log-post-plain';\nimport { apiUserRemoveUserPost$Json } from '../fn/user/api-user-remove-user-post-json';\nimport { apiUserRemoveUserPost$Plain } from '../fn/user/api-user-remove-user-post-plain';\nimport { apiUserSaveDataPost$Json } from '../fn/user/api-user-save-data-post-json';\nimport { apiUserSaveDataPost$Plain } from '../fn/user/api-user-save-data-post-plain';\nimport { apiUserUserLoginPost$Json } from '../fn/user/api-user-user-login-post-json';\nimport { apiUserUserLoginPost$Plain } from '../fn/user/api-user-user-login-post-plain';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../api-configuration\";\nimport * as i2 from \"@angular/common/http\";\nexport class UserService extends BaseService {\n  constructor(config, http) {\n    super(config, http);\n  }\n  /** Path part for operation `apiUserUserLoginPost()` */\n  static {\n    this.ApiUserUserLoginPostPath = '/api/User/UserLogin';\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiUserUserLoginPost$Plain()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiUserUserLoginPost$Plain$Response(params, context) {\n    return apiUserUserLoginPost$Plain(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiUserUserLoginPost$Plain$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiUserUserLoginPost$Plain(params, context) {\n    return this.apiUserUserLoginPost$Plain$Response(params, context).pipe(map(r => r.body));\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiUserUserLoginPost$Json()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiUserUserLoginPost$Json$Response(params, context) {\n    return apiUserUserLoginPost$Json(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiUserUserLoginPost$Json$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiUserUserLoginPost$Json(params, context) {\n    return this.apiUserUserLoginPost$Json$Response(params, context).pipe(map(r => r.body));\n  }\n  /** Path part for operation `apiUserGetMenuPost()` */\n  static {\n    this.ApiUserGetMenuPostPath = '/api/User/GetMenu';\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiUserGetMenuPost$Plain()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiUserGetMenuPost$Plain$Response(params, context) {\n    return apiUserGetMenuPost$Plain(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiUserGetMenuPost$Plain$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiUserGetMenuPost$Plain(params, context) {\n    return this.apiUserGetMenuPost$Plain$Response(params, context).pipe(map(r => r.body));\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiUserGetMenuPost$Json()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiUserGetMenuPost$Json$Response(params, context) {\n    return apiUserGetMenuPost$Json(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiUserGetMenuPost$Json$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiUserGetMenuPost$Json(params, context) {\n    return this.apiUserGetMenuPost$Json$Response(params, context).pipe(map(r => r.body));\n  }\n  /** Path part for operation `apiUserGetListPost()` */\n  static {\n    this.ApiUserGetListPostPath = '/api/User/GetList';\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiUserGetListPost$Plain()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiUserGetListPost$Plain$Response(params, context) {\n    return apiUserGetListPost$Plain(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiUserGetListPost$Plain$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiUserGetListPost$Plain(params, context) {\n    return this.apiUserGetListPost$Plain$Response(params, context).pipe(map(r => r.body));\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiUserGetListPost$Json()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiUserGetListPost$Json$Response(params, context) {\n    return apiUserGetListPost$Json(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiUserGetListPost$Json$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiUserGetListPost$Json(params, context) {\n    return this.apiUserGetListPost$Json$Response(params, context).pipe(map(r => r.body));\n  }\n  /** Path part for operation `apiUserGetDataPost()` */\n  static {\n    this.ApiUserGetDataPostPath = '/api/User/GetData';\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiUserGetDataPost$Plain()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiUserGetDataPost$Plain$Response(params, context) {\n    return apiUserGetDataPost$Plain(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiUserGetDataPost$Plain$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiUserGetDataPost$Plain(params, context) {\n    return this.apiUserGetDataPost$Plain$Response(params, context).pipe(map(r => r.body));\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiUserGetDataPost$Json()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiUserGetDataPost$Json$Response(params, context) {\n    return apiUserGetDataPost$Json(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiUserGetDataPost$Json$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiUserGetDataPost$Json(params, context) {\n    return this.apiUserGetDataPost$Json$Response(params, context).pipe(map(r => r.body));\n  }\n  /** Path part for operation `apiUserAddDataPost()` */\n  static {\n    this.ApiUserAddDataPostPath = '/api/User/AddData';\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiUserAddDataPost$Plain()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiUserAddDataPost$Plain$Response(params, context) {\n    return apiUserAddDataPost$Plain(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiUserAddDataPost$Plain$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiUserAddDataPost$Plain(params, context) {\n    return this.apiUserAddDataPost$Plain$Response(params, context).pipe(map(r => r.body));\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiUserAddDataPost$Json()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiUserAddDataPost$Json$Response(params, context) {\n    return apiUserAddDataPost$Json(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiUserAddDataPost$Json$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiUserAddDataPost$Json(params, context) {\n    return this.apiUserAddDataPost$Json$Response(params, context).pipe(map(r => r.body));\n  }\n  /** Path part for operation `apiUserSaveDataPost()` */\n  static {\n    this.ApiUserSaveDataPostPath = '/api/User/SaveData';\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiUserSaveDataPost$Plain()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiUserSaveDataPost$Plain$Response(params, context) {\n    return apiUserSaveDataPost$Plain(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiUserSaveDataPost$Plain$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiUserSaveDataPost$Plain(params, context) {\n    return this.apiUserSaveDataPost$Plain$Response(params, context).pipe(map(r => r.body));\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiUserSaveDataPost$Json()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiUserSaveDataPost$Json$Response(params, context) {\n    return apiUserSaveDataPost$Json(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiUserSaveDataPost$Json$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiUserSaveDataPost$Json(params, context) {\n    return this.apiUserSaveDataPost$Json$Response(params, context).pipe(map(r => r.body));\n  }\n  /** Path part for operation `apiUserRemoveUserPost()` */\n  static {\n    this.ApiUserRemoveUserPostPath = '/api/User/RemoveUser';\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiUserRemoveUserPost$Plain()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiUserRemoveUserPost$Plain$Response(params, context) {\n    return apiUserRemoveUserPost$Plain(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiUserRemoveUserPost$Plain$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiUserRemoveUserPost$Plain(params, context) {\n    return this.apiUserRemoveUserPost$Plain$Response(params, context).pipe(map(r => r.body));\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiUserRemoveUserPost$Json()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiUserRemoveUserPost$Json$Response(params, context) {\n    return apiUserRemoveUserPost$Json(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiUserRemoveUserPost$Json$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiUserRemoveUserPost$Json(params, context) {\n    return this.apiUserRemoveUserPost$Json$Response(params, context).pipe(map(r => r.body));\n  }\n  /** Path part for operation `apiUserGetUserLogPost()` */\n  static {\n    this.ApiUserGetUserLogPostPath = '/api/User/GetUserLog';\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiUserGetUserLogPost$Plain()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiUserGetUserLogPost$Plain$Response(params, context) {\n    return apiUserGetUserLogPost$Plain(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiUserGetUserLogPost$Plain$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiUserGetUserLogPost$Plain(params, context) {\n    return this.apiUserGetUserLogPost$Plain$Response(params, context).pipe(map(r => r.body));\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiUserGetUserLogPost$Json()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiUserGetUserLogPost$Json$Response(params, context) {\n    return apiUserGetUserLogPost$Json(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiUserGetUserLogPost$Json$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiUserGetUserLogPost$Json(params, context) {\n    return this.apiUserGetUserLogPost$Json$Response(params, context).pipe(map(r => r.body));\n  }\n  static {\n    this.ɵfac = function UserService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || UserService)(i0.ɵɵinject(i1.ApiConfiguration), i0.ɵɵinject(i2.HttpClient));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: UserService,\n      factory: UserService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["map", "BaseService", "apiUserAddDataPost$Json", "apiUserAddDataPost$Plain", "apiUserGetDataPost$Json", "apiUserGetDataPost$Plain", "apiUserGetListPost$Json", "apiUserGetListPost$Plain", "apiUserGetMenuPost$Json", "apiUserGetMenuPost$Plain", "apiUserGetUserLogPost$Json", "apiUserGetUserLogPost$Plain", "apiUserRemoveUserPost$Json", "apiUserRemoveUserPost$Plain", "apiUserSaveDataPost$Json", "apiUserSaveDataPost$Plain", "apiUserUserLoginPost$Json", "apiUserUserLoginPost$Plain", "UserService", "constructor", "config", "http", "ApiUserUserLoginPostPath", "apiUserUserLoginPost$Plain$Response", "params", "context", "rootUrl", "pipe", "r", "body", "apiUserUserLoginPost$Json$Response", "ApiUserGetMenuPostPath", "apiUserGetMenuPost$Plain$Response", "apiUserGetMenuPost$Json$Response", "ApiUserGetListPostPath", "apiUserGetListPost$Plain$Response", "apiUserGetListPost$Json$Response", "ApiUserGetDataPostPath", "apiUserGetDataPost$Plain$Response", "apiUserGetDataPost$Json$Response", "ApiUserAddDataPostPath", "apiUserAddDataPost$Plain$Response", "apiUserAddDataPost$Json$Response", "ApiUserSaveDataPostPath", "apiUserSaveDataPost$Plain$Response", "apiUserSaveDataPost$Json$Response", "ApiUserRemoveUserPostPath", "apiUserRemoveUserPost$Plain$Response", "apiUserRemoveUserPost$Json$Response", "ApiUserGetUserLogPostPath", "apiUserGetUserLogPost$Plain$Response", "apiUserGetUserLogPost$Json$Response", "i0", "ɵɵinject", "i1", "ApiConfiguration", "i2", "HttpClient", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\services\\api\\services\\user.service.ts"], "sourcesContent": ["/* tslint:disable */\r\n/* eslint-disable */\r\n/* Code generated by ng-openapi-gen DO NOT EDIT. */\r\n\r\nimport { HttpClient, HttpContext } from '@angular/common/http';\r\nimport { Injectable } from '@angular/core';\r\nimport { Observable } from 'rxjs';\r\nimport { map } from 'rxjs/operators';\r\n\r\nimport { BaseService } from '../base-service';\r\nimport { ApiConfiguration } from '../api-configuration';\r\nimport { StrictHttpResponse } from '../strict-http-response';\r\n\r\nimport { apiUserAddDataPost$Json } from '../fn/user/api-user-add-data-post-json';\r\nimport { ApiUserAddDataPost$Json$Params } from '../fn/user/api-user-add-data-post-json';\r\nimport { apiUserAddDataPost$Plain } from '../fn/user/api-user-add-data-post-plain';\r\nimport { ApiUserAddDataPost$Plain$Params } from '../fn/user/api-user-add-data-post-plain';\r\nimport { apiUserGetDataPost$Json } from '../fn/user/api-user-get-data-post-json';\r\nimport { ApiUserGetDataPost$Json$Params } from '../fn/user/api-user-get-data-post-json';\r\nimport { apiUserGetDataPost$Plain } from '../fn/user/api-user-get-data-post-plain';\r\nimport { ApiUserGetDataPost$Plain$Params } from '../fn/user/api-user-get-data-post-plain';\r\nimport { apiUserGetListPost$Json } from '../fn/user/api-user-get-list-post-json';\r\nimport { ApiUserGetListPost$Json$Params } from '../fn/user/api-user-get-list-post-json';\r\nimport { apiUserGetListPost$Plain } from '../fn/user/api-user-get-list-post-plain';\r\nimport { ApiUserGetListPost$Plain$Params } from '../fn/user/api-user-get-list-post-plain';\r\nimport { apiUserGetMenuPost$Json } from '../fn/user/api-user-get-menu-post-json';\r\nimport { ApiUserGetMenuPost$Json$Params } from '../fn/user/api-user-get-menu-post-json';\r\nimport { apiUserGetMenuPost$Plain } from '../fn/user/api-user-get-menu-post-plain';\r\nimport { ApiUserGetMenuPost$Plain$Params } from '../fn/user/api-user-get-menu-post-plain';\r\nimport { apiUserGetUserLogPost$Json } from '../fn/user/api-user-get-user-log-post-json';\r\nimport { ApiUserGetUserLogPost$Json$Params } from '../fn/user/api-user-get-user-log-post-json';\r\nimport { apiUserGetUserLogPost$Plain } from '../fn/user/api-user-get-user-log-post-plain';\r\nimport { ApiUserGetUserLogPost$Plain$Params } from '../fn/user/api-user-get-user-log-post-plain';\r\nimport { apiUserRemoveUserPost$Json } from '../fn/user/api-user-remove-user-post-json';\r\nimport { ApiUserRemoveUserPost$Json$Params } from '../fn/user/api-user-remove-user-post-json';\r\nimport { apiUserRemoveUserPost$Plain } from '../fn/user/api-user-remove-user-post-plain';\r\nimport { ApiUserRemoveUserPost$Plain$Params } from '../fn/user/api-user-remove-user-post-plain';\r\nimport { apiUserSaveDataPost$Json } from '../fn/user/api-user-save-data-post-json';\r\nimport { ApiUserSaveDataPost$Json$Params } from '../fn/user/api-user-save-data-post-json';\r\nimport { apiUserSaveDataPost$Plain } from '../fn/user/api-user-save-data-post-plain';\r\nimport { ApiUserSaveDataPost$Plain$Params } from '../fn/user/api-user-save-data-post-plain';\r\nimport { apiUserUserLoginPost$Json } from '../fn/user/api-user-user-login-post-json';\r\nimport { ApiUserUserLoginPost$Json$Params } from '../fn/user/api-user-user-login-post-json';\r\nimport { apiUserUserLoginPost$Plain } from '../fn/user/api-user-user-login-post-plain';\r\nimport { ApiUserUserLoginPost$Plain$Params } from '../fn/user/api-user-user-login-post-plain';\r\nimport { GetMenuResponseResponseBase } from '../models/get-menu-response-response-base';\r\nimport { StringResponseBase } from '../models/string-response-base';\r\nimport { UserGetDataResponseResponseBase } from '../models/user-get-data-response-response-base';\r\nimport { UserGetListResponseListResponseBase } from '../models/user-get-list-response-list-response-base';\r\nimport { UserGetUserLogResponseListResponseBase } from '../models/user-get-user-log-response-list-response-base';\r\nimport { UserRemoveDataResponseResponseBase } from '../models/user-remove-data-response-response-base';\r\nimport { UserSaveDataResponseResponseBase } from '../models/user-save-data-response-response-base';\r\n\r\n@Injectable({ providedIn: 'root' })\r\nexport class UserService extends BaseService {\r\n  constructor(config: ApiConfiguration, http: HttpClient) {\r\n    super(config, http);\r\n  }\r\n\r\n  /** Path part for operation `apiUserUserLoginPost()` */\r\n  static readonly ApiUserUserLoginPostPath = '/api/User/UserLogin';\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiUserUserLoginPost$Plain()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiUserUserLoginPost$Plain$Response(params?: ApiUserUserLoginPost$Plain$Params, context?: HttpContext): Observable<StrictHttpResponse<StringResponseBase>> {\r\n    return apiUserUserLoginPost$Plain(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiUserUserLoginPost$Plain$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiUserUserLoginPost$Plain(params?: ApiUserUserLoginPost$Plain$Params, context?: HttpContext): Observable<StringResponseBase> {\r\n    return this.apiUserUserLoginPost$Plain$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<StringResponseBase>): StringResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiUserUserLoginPost$Json()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiUserUserLoginPost$Json$Response(params?: ApiUserUserLoginPost$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<StringResponseBase>> {\r\n    return apiUserUserLoginPost$Json(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiUserUserLoginPost$Json$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiUserUserLoginPost$Json(params?: ApiUserUserLoginPost$Json$Params, context?: HttpContext): Observable<StringResponseBase> {\r\n    return this.apiUserUserLoginPost$Json$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<StringResponseBase>): StringResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /** Path part for operation `apiUserGetMenuPost()` */\r\n  static readonly ApiUserGetMenuPostPath = '/api/User/GetMenu';\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiUserGetMenuPost$Plain()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiUserGetMenuPost$Plain$Response(params?: ApiUserGetMenuPost$Plain$Params, context?: HttpContext): Observable<StrictHttpResponse<GetMenuResponseResponseBase>> {\r\n    return apiUserGetMenuPost$Plain(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiUserGetMenuPost$Plain$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiUserGetMenuPost$Plain(params?: ApiUserGetMenuPost$Plain$Params, context?: HttpContext): Observable<GetMenuResponseResponseBase> {\r\n    return this.apiUserGetMenuPost$Plain$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<GetMenuResponseResponseBase>): GetMenuResponseResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiUserGetMenuPost$Json()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiUserGetMenuPost$Json$Response(params?: ApiUserGetMenuPost$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<GetMenuResponseResponseBase>> {\r\n    return apiUserGetMenuPost$Json(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiUserGetMenuPost$Json$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiUserGetMenuPost$Json(params?: ApiUserGetMenuPost$Json$Params, context?: HttpContext): Observable<GetMenuResponseResponseBase> {\r\n    return this.apiUserGetMenuPost$Json$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<GetMenuResponseResponseBase>): GetMenuResponseResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /** Path part for operation `apiUserGetListPost()` */\r\n  static readonly ApiUserGetListPostPath = '/api/User/GetList';\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiUserGetListPost$Plain()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiUserGetListPost$Plain$Response(params?: ApiUserGetListPost$Plain$Params, context?: HttpContext): Observable<StrictHttpResponse<UserGetListResponseListResponseBase>> {\r\n    return apiUserGetListPost$Plain(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiUserGetListPost$Plain$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiUserGetListPost$Plain(params?: ApiUserGetListPost$Plain$Params, context?: HttpContext): Observable<UserGetListResponseListResponseBase> {\r\n    return this.apiUserGetListPost$Plain$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<UserGetListResponseListResponseBase>): UserGetListResponseListResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiUserGetListPost$Json()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiUserGetListPost$Json$Response(params?: ApiUserGetListPost$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<UserGetListResponseListResponseBase>> {\r\n    return apiUserGetListPost$Json(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiUserGetListPost$Json$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiUserGetListPost$Json(params?: ApiUserGetListPost$Json$Params, context?: HttpContext): Observable<UserGetListResponseListResponseBase> {\r\n    return this.apiUserGetListPost$Json$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<UserGetListResponseListResponseBase>): UserGetListResponseListResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /** Path part for operation `apiUserGetDataPost()` */\r\n  static readonly ApiUserGetDataPostPath = '/api/User/GetData';\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiUserGetDataPost$Plain()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiUserGetDataPost$Plain$Response(params?: ApiUserGetDataPost$Plain$Params, context?: HttpContext): Observable<StrictHttpResponse<UserGetDataResponseResponseBase>> {\r\n    return apiUserGetDataPost$Plain(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiUserGetDataPost$Plain$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiUserGetDataPost$Plain(params?: ApiUserGetDataPost$Plain$Params, context?: HttpContext): Observable<UserGetDataResponseResponseBase> {\r\n    return this.apiUserGetDataPost$Plain$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<UserGetDataResponseResponseBase>): UserGetDataResponseResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiUserGetDataPost$Json()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiUserGetDataPost$Json$Response(params?: ApiUserGetDataPost$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<UserGetDataResponseResponseBase>> {\r\n    return apiUserGetDataPost$Json(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiUserGetDataPost$Json$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiUserGetDataPost$Json(params?: ApiUserGetDataPost$Json$Params, context?: HttpContext): Observable<UserGetDataResponseResponseBase> {\r\n    return this.apiUserGetDataPost$Json$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<UserGetDataResponseResponseBase>): UserGetDataResponseResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /** Path part for operation `apiUserAddDataPost()` */\r\n  static readonly ApiUserAddDataPostPath = '/api/User/AddData';\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiUserAddDataPost$Plain()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiUserAddDataPost$Plain$Response(params?: ApiUserAddDataPost$Plain$Params, context?: HttpContext): Observable<StrictHttpResponse<UserSaveDataResponseResponseBase>> {\r\n    return apiUserAddDataPost$Plain(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiUserAddDataPost$Plain$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiUserAddDataPost$Plain(params?: ApiUserAddDataPost$Plain$Params, context?: HttpContext): Observable<UserSaveDataResponseResponseBase> {\r\n    return this.apiUserAddDataPost$Plain$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<UserSaveDataResponseResponseBase>): UserSaveDataResponseResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiUserAddDataPost$Json()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiUserAddDataPost$Json$Response(params?: ApiUserAddDataPost$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<UserSaveDataResponseResponseBase>> {\r\n    return apiUserAddDataPost$Json(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiUserAddDataPost$Json$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiUserAddDataPost$Json(params?: ApiUserAddDataPost$Json$Params, context?: HttpContext): Observable<UserSaveDataResponseResponseBase> {\r\n    return this.apiUserAddDataPost$Json$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<UserSaveDataResponseResponseBase>): UserSaveDataResponseResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /** Path part for operation `apiUserSaveDataPost()` */\r\n  static readonly ApiUserSaveDataPostPath = '/api/User/SaveData';\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiUserSaveDataPost$Plain()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiUserSaveDataPost$Plain$Response(params?: ApiUserSaveDataPost$Plain$Params, context?: HttpContext): Observable<StrictHttpResponse<UserSaveDataResponseResponseBase>> {\r\n    return apiUserSaveDataPost$Plain(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiUserSaveDataPost$Plain$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiUserSaveDataPost$Plain(params?: ApiUserSaveDataPost$Plain$Params, context?: HttpContext): Observable<UserSaveDataResponseResponseBase> {\r\n    return this.apiUserSaveDataPost$Plain$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<UserSaveDataResponseResponseBase>): UserSaveDataResponseResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiUserSaveDataPost$Json()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiUserSaveDataPost$Json$Response(params?: ApiUserSaveDataPost$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<UserSaveDataResponseResponseBase>> {\r\n    return apiUserSaveDataPost$Json(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiUserSaveDataPost$Json$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiUserSaveDataPost$Json(params?: ApiUserSaveDataPost$Json$Params, context?: HttpContext): Observable<UserSaveDataResponseResponseBase> {\r\n    return this.apiUserSaveDataPost$Json$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<UserSaveDataResponseResponseBase>): UserSaveDataResponseResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /** Path part for operation `apiUserRemoveUserPost()` */\r\n  static readonly ApiUserRemoveUserPostPath = '/api/User/RemoveUser';\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiUserRemoveUserPost$Plain()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiUserRemoveUserPost$Plain$Response(params?: ApiUserRemoveUserPost$Plain$Params, context?: HttpContext): Observable<StrictHttpResponse<UserRemoveDataResponseResponseBase>> {\r\n    return apiUserRemoveUserPost$Plain(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiUserRemoveUserPost$Plain$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiUserRemoveUserPost$Plain(params?: ApiUserRemoveUserPost$Plain$Params, context?: HttpContext): Observable<UserRemoveDataResponseResponseBase> {\r\n    return this.apiUserRemoveUserPost$Plain$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<UserRemoveDataResponseResponseBase>): UserRemoveDataResponseResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiUserRemoveUserPost$Json()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiUserRemoveUserPost$Json$Response(params?: ApiUserRemoveUserPost$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<UserRemoveDataResponseResponseBase>> {\r\n    return apiUserRemoveUserPost$Json(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiUserRemoveUserPost$Json$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiUserRemoveUserPost$Json(params?: ApiUserRemoveUserPost$Json$Params, context?: HttpContext): Observable<UserRemoveDataResponseResponseBase> {\r\n    return this.apiUserRemoveUserPost$Json$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<UserRemoveDataResponseResponseBase>): UserRemoveDataResponseResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /** Path part for operation `apiUserGetUserLogPost()` */\r\n  static readonly ApiUserGetUserLogPostPath = '/api/User/GetUserLog';\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiUserGetUserLogPost$Plain()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiUserGetUserLogPost$Plain$Response(params?: ApiUserGetUserLogPost$Plain$Params, context?: HttpContext): Observable<StrictHttpResponse<UserGetUserLogResponseListResponseBase>> {\r\n    return apiUserGetUserLogPost$Plain(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiUserGetUserLogPost$Plain$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiUserGetUserLogPost$Plain(params?: ApiUserGetUserLogPost$Plain$Params, context?: HttpContext): Observable<UserGetUserLogResponseListResponseBase> {\r\n    return this.apiUserGetUserLogPost$Plain$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<UserGetUserLogResponseListResponseBase>): UserGetUserLogResponseListResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiUserGetUserLogPost$Json()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiUserGetUserLogPost$Json$Response(params?: ApiUserGetUserLogPost$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<UserGetUserLogResponseListResponseBase>> {\r\n    return apiUserGetUserLogPost$Json(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiUserGetUserLogPost$Json$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiUserGetUserLogPost$Json(params?: ApiUserGetUserLogPost$Json$Params, context?: HttpContext): Observable<UserGetUserLogResponseListResponseBase> {\r\n    return this.apiUserGetUserLogPost$Json$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<UserGetUserLogResponseListResponseBase>): UserGetUserLogResponseListResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n}\r\n"], "mappings": "AAOA,SAASA,GAAG,QAAQ,gBAAgB;AAEpC,SAASC,WAAW,QAAQ,iBAAiB;AAI7C,SAASC,uBAAuB,QAAQ,wCAAwC;AAEhF,SAASC,wBAAwB,QAAQ,yCAAyC;AAElF,SAASC,uBAAuB,QAAQ,wCAAwC;AAEhF,SAASC,wBAAwB,QAAQ,yCAAyC;AAElF,SAASC,uBAAuB,QAAQ,wCAAwC;AAEhF,SAASC,wBAAwB,QAAQ,yCAAyC;AAElF,SAASC,uBAAuB,QAAQ,wCAAwC;AAEhF,SAASC,wBAAwB,QAAQ,yCAAyC;AAElF,SAASC,0BAA0B,QAAQ,4CAA4C;AAEvF,SAASC,2BAA2B,QAAQ,6CAA6C;AAEzF,SAASC,0BAA0B,QAAQ,2CAA2C;AAEtF,SAASC,2BAA2B,QAAQ,4CAA4C;AAExF,SAASC,wBAAwB,QAAQ,yCAAyC;AAElF,SAASC,yBAAyB,QAAQ,0CAA0C;AAEpF,SAASC,yBAAyB,QAAQ,0CAA0C;AAEpF,SAASC,0BAA0B,QAAQ,2CAA2C;;;;AAWtF,OAAM,MAAOC,WAAY,SAAQjB,WAAW;EAC1CkB,YAAYC,MAAwB,EAAEC,IAAgB;IACpD,KAAK,CAACD,MAAM,EAAEC,IAAI,CAAC;EACrB;EAEA;;IACgB,KAAAC,wBAAwB,GAAG,qBAAqB;EAAC;EAEjE;;;;;;EAMAC,mCAAmCA,CAACC,MAA0C,EAAEC,OAAqB;IACnG,OAAOR,0BAA0B,CAAC,IAAI,CAACI,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EAC7E;EAEA;;;;;;EAMAR,0BAA0BA,CAACO,MAA0C,EAAEC,OAAqB;IAC1F,OAAO,IAAI,CAACF,mCAAmC,CAACC,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CACnE3B,GAAG,CAAE4B,CAAyC,IAAyBA,CAAC,CAACC,IAAI,CAAC,CAC/E;EACH;EAEA;;;;;;EAMAC,kCAAkCA,CAACN,MAAyC,EAAEC,OAAqB;IACjG,OAAOT,yBAAyB,CAAC,IAAI,CAACK,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EAC5E;EAEA;;;;;;EAMAT,yBAAyBA,CAACQ,MAAyC,EAAEC,OAAqB;IACxF,OAAO,IAAI,CAACK,kCAAkC,CAACN,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CAClE3B,GAAG,CAAE4B,CAAyC,IAAyBA,CAAC,CAACC,IAAI,CAAC,CAC/E;EACH;EAEA;;IACgB,KAAAE,sBAAsB,GAAG,mBAAmB;EAAC;EAE7D;;;;;;EAMAC,iCAAiCA,CAACR,MAAwC,EAAEC,OAAqB;IAC/F,OAAOhB,wBAAwB,CAAC,IAAI,CAACY,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EAC3E;EAEA;;;;;;EAMAhB,wBAAwBA,CAACe,MAAwC,EAAEC,OAAqB;IACtF,OAAO,IAAI,CAACO,iCAAiC,CAACR,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CACjE3B,GAAG,CAAE4B,CAAkD,IAAkCA,CAAC,CAACC,IAAI,CAAC,CACjG;EACH;EAEA;;;;;;EAMAI,gCAAgCA,CAACT,MAAuC,EAAEC,OAAqB;IAC7F,OAAOjB,uBAAuB,CAAC,IAAI,CAACa,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EAC1E;EAEA;;;;;;EAMAjB,uBAAuBA,CAACgB,MAAuC,EAAEC,OAAqB;IACpF,OAAO,IAAI,CAACQ,gCAAgC,CAACT,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CAChE3B,GAAG,CAAE4B,CAAkD,IAAkCA,CAAC,CAACC,IAAI,CAAC,CACjG;EACH;EAEA;;IACgB,KAAAK,sBAAsB,GAAG,mBAAmB;EAAC;EAE7D;;;;;;EAMAC,iCAAiCA,CAACX,MAAwC,EAAEC,OAAqB;IAC/F,OAAOlB,wBAAwB,CAAC,IAAI,CAACc,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EAC3E;EAEA;;;;;;EAMAlB,wBAAwBA,CAACiB,MAAwC,EAAEC,OAAqB;IACtF,OAAO,IAAI,CAACU,iCAAiC,CAACX,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CACjE3B,GAAG,CAAE4B,CAA0D,IAA0CA,CAAC,CAACC,IAAI,CAAC,CACjH;EACH;EAEA;;;;;;EAMAO,gCAAgCA,CAACZ,MAAuC,EAAEC,OAAqB;IAC7F,OAAOnB,uBAAuB,CAAC,IAAI,CAACe,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EAC1E;EAEA;;;;;;EAMAnB,uBAAuBA,CAACkB,MAAuC,EAAEC,OAAqB;IACpF,OAAO,IAAI,CAACW,gCAAgC,CAACZ,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CAChE3B,GAAG,CAAE4B,CAA0D,IAA0CA,CAAC,CAACC,IAAI,CAAC,CACjH;EACH;EAEA;;IACgB,KAAAQ,sBAAsB,GAAG,mBAAmB;EAAC;EAE7D;;;;;;EAMAC,iCAAiCA,CAACd,MAAwC,EAAEC,OAAqB;IAC/F,OAAOpB,wBAAwB,CAAC,IAAI,CAACgB,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EAC3E;EAEA;;;;;;EAMApB,wBAAwBA,CAACmB,MAAwC,EAAEC,OAAqB;IACtF,OAAO,IAAI,CAACa,iCAAiC,CAACd,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CACjE3B,GAAG,CAAE4B,CAAsD,IAAsCA,CAAC,CAACC,IAAI,CAAC,CACzG;EACH;EAEA;;;;;;EAMAU,gCAAgCA,CAACf,MAAuC,EAAEC,OAAqB;IAC7F,OAAOrB,uBAAuB,CAAC,IAAI,CAACiB,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EAC1E;EAEA;;;;;;EAMArB,uBAAuBA,CAACoB,MAAuC,EAAEC,OAAqB;IACpF,OAAO,IAAI,CAACc,gCAAgC,CAACf,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CAChE3B,GAAG,CAAE4B,CAAsD,IAAsCA,CAAC,CAACC,IAAI,CAAC,CACzG;EACH;EAEA;;IACgB,KAAAW,sBAAsB,GAAG,mBAAmB;EAAC;EAE7D;;;;;;EAMAC,iCAAiCA,CAACjB,MAAwC,EAAEC,OAAqB;IAC/F,OAAOtB,wBAAwB,CAAC,IAAI,CAACkB,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EAC3E;EAEA;;;;;;EAMAtB,wBAAwBA,CAACqB,MAAwC,EAAEC,OAAqB;IACtF,OAAO,IAAI,CAACgB,iCAAiC,CAACjB,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CACjE3B,GAAG,CAAE4B,CAAuD,IAAuCA,CAAC,CAACC,IAAI,CAAC,CAC3G;EACH;EAEA;;;;;;EAMAa,gCAAgCA,CAAClB,MAAuC,EAAEC,OAAqB;IAC7F,OAAOvB,uBAAuB,CAAC,IAAI,CAACmB,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EAC1E;EAEA;;;;;;EAMAvB,uBAAuBA,CAACsB,MAAuC,EAAEC,OAAqB;IACpF,OAAO,IAAI,CAACiB,gCAAgC,CAAClB,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CAChE3B,GAAG,CAAE4B,CAAuD,IAAuCA,CAAC,CAACC,IAAI,CAAC,CAC3G;EACH;EAEA;;IACgB,KAAAc,uBAAuB,GAAG,oBAAoB;EAAC;EAE/D;;;;;;EAMAC,kCAAkCA,CAACpB,MAAyC,EAAEC,OAAqB;IACjG,OAAOV,yBAAyB,CAAC,IAAI,CAACM,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EAC5E;EAEA;;;;;;EAMAV,yBAAyBA,CAACS,MAAyC,EAAEC,OAAqB;IACxF,OAAO,IAAI,CAACmB,kCAAkC,CAACpB,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CAClE3B,GAAG,CAAE4B,CAAuD,IAAuCA,CAAC,CAACC,IAAI,CAAC,CAC3G;EACH;EAEA;;;;;;EAMAgB,iCAAiCA,CAACrB,MAAwC,EAAEC,OAAqB;IAC/F,OAAOX,wBAAwB,CAAC,IAAI,CAACO,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EAC3E;EAEA;;;;;;EAMAX,wBAAwBA,CAACU,MAAwC,EAAEC,OAAqB;IACtF,OAAO,IAAI,CAACoB,iCAAiC,CAACrB,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CACjE3B,GAAG,CAAE4B,CAAuD,IAAuCA,CAAC,CAACC,IAAI,CAAC,CAC3G;EACH;EAEA;;IACgB,KAAAiB,yBAAyB,GAAG,sBAAsB;EAAC;EAEnE;;;;;;EAMAC,oCAAoCA,CAACvB,MAA2C,EAAEC,OAAqB;IACrG,OAAOZ,2BAA2B,CAAC,IAAI,CAACQ,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EAC9E;EAEA;;;;;;EAMAZ,2BAA2BA,CAACW,MAA2C,EAAEC,OAAqB;IAC5F,OAAO,IAAI,CAACsB,oCAAoC,CAACvB,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CACpE3B,GAAG,CAAE4B,CAAyD,IAAyCA,CAAC,CAACC,IAAI,CAAC,CAC/G;EACH;EAEA;;;;;;EAMAmB,mCAAmCA,CAACxB,MAA0C,EAAEC,OAAqB;IACnG,OAAOb,0BAA0B,CAAC,IAAI,CAACS,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EAC7E;EAEA;;;;;;EAMAb,0BAA0BA,CAACY,MAA0C,EAAEC,OAAqB;IAC1F,OAAO,IAAI,CAACuB,mCAAmC,CAACxB,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CACnE3B,GAAG,CAAE4B,CAAyD,IAAyCA,CAAC,CAACC,IAAI,CAAC,CAC/G;EACH;EAEA;;IACgB,KAAAoB,yBAAyB,GAAG,sBAAsB;EAAC;EAEnE;;;;;;EAMAC,oCAAoCA,CAAC1B,MAA2C,EAAEC,OAAqB;IACrG,OAAOd,2BAA2B,CAAC,IAAI,CAACU,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EAC9E;EAEA;;;;;;EAMAd,2BAA2BA,CAACa,MAA2C,EAAEC,OAAqB;IAC5F,OAAO,IAAI,CAACyB,oCAAoC,CAAC1B,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CACpE3B,GAAG,CAAE4B,CAA6D,IAA6CA,CAAC,CAACC,IAAI,CAAC,CACvH;EACH;EAEA;;;;;;EAMAsB,mCAAmCA,CAAC3B,MAA0C,EAAEC,OAAqB;IACnG,OAAOf,0BAA0B,CAAC,IAAI,CAACW,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EAC7E;EAEA;;;;;;EAMAf,0BAA0BA,CAACc,MAA0C,EAAEC,OAAqB;IAC1F,OAAO,IAAI,CAAC0B,mCAAmC,CAAC3B,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CACnE3B,GAAG,CAAE4B,CAA6D,IAA6CA,CAAC,CAACC,IAAI,CAAC,CACvH;EACH;;;uCA3XWX,WAAW,EAAAkC,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,gBAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,UAAA;IAAA;EAAA;;;aAAXvC,WAAW;MAAAwC,OAAA,EAAXxC,WAAW,CAAAyC,IAAA;MAAAC,UAAA,EADE;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}