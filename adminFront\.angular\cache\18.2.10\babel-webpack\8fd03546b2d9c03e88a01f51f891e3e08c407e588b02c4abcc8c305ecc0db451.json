{"ast": null, "code": ";\n(function (root, factory, undef) {\n  if (typeof exports === \"object\") {\n    // CommonJS\n    module.exports = exports = factory(require(\"./core\"), require(\"./cipher-core\"));\n  } else if (typeof define === \"function\" && define.amd) {\n    // AMD\n    define([\"./core\", \"./cipher-core\"], factory);\n  } else {\n    // Global (browser)\n    factory(root.CryptoJS);\n  }\n})(this, function (CryptoJS) {\n  /**\n   * A noop padding strategy.\n   */\n  CryptoJS.pad.NoPadding = {\n    pad: function () {},\n    unpad: function () {}\n  };\n  return CryptoJS.pad.NoPadding;\n});", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}