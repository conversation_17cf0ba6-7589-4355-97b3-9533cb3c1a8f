{"ast": null, "code": "import { SharedModule } from '../../../components/shared.module';\nimport { CommonModule } from '@angular/common';\nimport { NbCheckboxModule } from '@nebular/theme';\nimport { BaseComponent } from '../../../components/base/baseComponent';\nimport { tap } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/shared/helper/allowHelper\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"src/app/shared/services/message.service\";\nimport * as i4 from \"src/services/api/services\";\nimport * as i5 from \"src/app/shared/services/utility.service\";\nimport * as i6 from \"src/app/shared/helper/validationHelper\";\nimport * as i7 from \"@angular/common\";\nimport * as i8 from \"@angular/forms\";\nimport * as i9 from \"@nebular/theme\";\nimport * as i10 from \"../../../components/breadcrumb/breadcrumb.component\";\nimport * as i11 from \"../../../../@theme/pipes/base-file.pipe\";\nfunction DetailContentManagementLandownerComponent_ng_container_9_input_12_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"input\", 31, 0);\n    i0.ɵɵlistener(\"change\", function DetailContentManagementLandownerComponent_ng_container_9_input_12_Template_input_change_0_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const formItemReq_r2 = i0.ɵɵnextContext().$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.detectFiles($event, formItemReq_r2));\n    });\n    i0.ɵɵelementEnd();\n  }\n}\nfunction DetailContentManagementLandownerComponent_ng_container_9_tr_16_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 32)(2, \"label\");\n    i0.ɵɵtext(3, \"\\u6587\\u4EF6\\u540D \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(4, \" \\u00A0 \");\n    i0.ɵɵelementStart(5, \"input\", 33);\n    i0.ɵɵlistener(\"blur\", function DetailContentManagementLandownerComponent_ng_container_9_tr_16_Template_input_blur_5_listener($event) {\n      const i_r6 = i0.ɵɵrestoreView(_r5).index;\n      const formItemReq_r2 = i0.ɵɵnextContext().$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.renameFile($event, i_r6, formItemReq_r2));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"td\", 34);\n    i0.ɵɵelement(7, \"img\", 35);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"td\", 36)(9, \"button\", 37);\n    i0.ɵɵlistener(\"click\", function DetailContentManagementLandownerComponent_ng_container_9_tr_16_Template_button_click_9_listener() {\n      const picture_r7 = i0.ɵɵrestoreView(_r5).$implicit;\n      const formItemReq_r2 = i0.ɵɵnextContext().$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.removeImage(picture_r7.id, formItemReq_r2));\n    });\n    i0.ɵɵtext(10, \"\\u5220\\u9664\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const picture_r7 = ctx.$implicit;\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"value\", picture_r7.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", picture_r7.data, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction DetailContentManagementLandownerComponent_ng_container_9_div_17_img_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 42);\n    i0.ɵɵpipe(1, \"addBaseFile\");\n  }\n  if (rf & 2) {\n    const formItemReq_r2 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵproperty(\"src\", i0.ɵɵpipeBind1(1, 1, formItemReq_r2.CDesignFileUrl), i0.ɵɵsanitizeUrl);\n  }\n}\nfunction DetailContentManagementLandownerComponent_ng_container_9_div_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 38)(1, \"div\", 19)(2, \"table\", 39)(3, \"tbody\")(4, \"tr\");\n    i0.ɵɵelement(5, \"td\", 32);\n    i0.ɵɵelementStart(6, \"td\", 40);\n    i0.ɵɵtemplate(7, DetailContentManagementLandownerComponent_ng_container_9_div_17_img_7_Template, 2, 3, \"img\", 41);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(8, \"td\", 36);\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const formItemReq_r2 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵproperty(\"ngClass\", formItemReq_r2.listPictures.length ? \"hidden\" : \"\");\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngIf\", formItemReq_r2.CDesignFileUrl);\n  }\n}\nfunction DetailContentManagementLandownerComponent_ng_container_9_nb_option_29_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 43);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const case_r8 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", case_r8);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", case_r8.label, \" \");\n  }\n}\nfunction DetailContentManagementLandownerComponent_ng_container_9_label_35_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"label\", 44)(1, \"nb-checkbox\", 45);\n    i0.ɵɵtwoWayListener(\"checkedChange\", function DetailContentManagementLandownerComponent_ng_container_9_label_35_Template_nb_checkbox_checkedChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r9);\n      const formItemReq_r2 = i0.ɵɵnextContext().$implicit;\n      i0.ɵɵtwoWayBindingSet(formItemReq_r2.allSelected, $event) || (formItemReq_r2.allSelected = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"checkedChange\", function DetailContentManagementLandownerComponent_ng_container_9_label_35_Template_nb_checkbox_checkedChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r9);\n      const formItemReq_r2 = i0.ɵɵnextContext().$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onCheckAllChange($event, formItemReq_r2));\n    });\n    i0.ɵɵtext(2, \" \\u5168\\u9078 \");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const formItemReq_r2 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"checked\", formItemReq_r2.allSelected);\n  }\n}\nfunction DetailContentManagementLandownerComponent_ng_container_9_label_36_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"label\", 44)(1, \"nb-checkbox\", 46);\n    i0.ɵɵtwoWayListener(\"checkedChange\", function DetailContentManagementLandownerComponent_ng_container_9_label_36_Template_nb_checkbox_checkedChange_1_listener($event) {\n      const item_r11 = i0.ɵɵrestoreView(_r10).$implicit;\n      const formItemReq_r2 = i0.ɵɵnextContext().$implicit;\n      i0.ɵɵtwoWayBindingSet(formItemReq_r2.selectedItems[item_r11], $event) || (formItemReq_r2.selectedItems[item_r11] = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"checkedChange\", function DetailContentManagementLandownerComponent_ng_container_9_label_36_Template_nb_checkbox_checkedChange_1_listener($event) {\n      const item_r11 = i0.ɵɵrestoreView(_r10).$implicit;\n      const formItemReq_r2 = i0.ɵɵnextContext().$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onCheckboxHouseHoldListChange($event, item_r11, formItemReq_r2));\n    });\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const item_r11 = ctx.$implicit;\n    const formItemReq_r2 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"checked\", formItemReq_r2.selectedItems[item_r11]);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", item_r11, \" \");\n  }\n}\nfunction DetailContentManagementLandownerComponent_ng_container_9_div_37_label_4_nb_checkbox_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-checkbox\", 46);\n    i0.ɵɵtwoWayListener(\"checkedChange\", function DetailContentManagementLandownerComponent_ng_container_9_div_37_label_4_nb_checkbox_1_Template_nb_checkbox_checkedChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r12);\n      const remark_r13 = i0.ɵɵnextContext().$implicit;\n      const formItemReq_r2 = i0.ɵɵnextContext(2).$implicit;\n      i0.ɵɵtwoWayBindingSet(formItemReq_r2.selectedRemarkType[remark_r13], $event) || (formItemReq_r2.selectedRemarkType[remark_r13] = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"checkedChange\", function DetailContentManagementLandownerComponent_ng_container_9_div_37_label_4_nb_checkbox_1_Template_nb_checkbox_checkedChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r12);\n      const remark_r13 = i0.ɵɵnextContext().$implicit;\n      const formItemReq_r2 = i0.ɵɵnextContext(2).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onCheckboxRemarkChange($event, remark_r13, formItemReq_r2));\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const remark_r13 = i0.ɵɵnextContext().$implicit;\n    const formItemReq_r2 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵtwoWayProperty(\"checked\", formItemReq_r2.selectedRemarkType[remark_r13]);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", remark_r13, \" \");\n  }\n}\nfunction DetailContentManagementLandownerComponent_ng_container_9_div_37_label_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"label\", 44);\n    i0.ɵɵtemplate(1, DetailContentManagementLandownerComponent_ng_container_9_div_37_label_4_nb_checkbox_1_Template, 2, 2, \"nb-checkbox\", 47);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const formItemReq_r2 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", formItemReq_r2.selectedRemarkType);\n  }\n}\nfunction DetailContentManagementLandownerComponent_ng_container_9_div_37_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 4)(1, \"div\", 11)(2, \"label\", 27);\n    i0.ɵɵtext(3, \"\\u5099\\u8A3B\\u9078\\u9805\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, DetailContentManagementLandownerComponent_ng_container_9_div_37_label_4_Template, 2, 1, \"label\", 29);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.CRemarkTypeOptions);\n  }\n}\nfunction DetailContentManagementLandownerComponent_ng_container_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 10)(2, \"div\", 11)(3, \"label\", 12);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 13)(6, \"input\", 14);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function DetailContentManagementLandownerComponent_ng_container_9_Template_input_ngModelChange_6_listener($event) {\n      const formItemReq_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      i0.ɵɵtwoWayBindingSet(formItemReq_r2.CItemName, $event) || (formItemReq_r2.CItemName = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(7, \"div\", 15)(8, \"div\", 16)(9, \"button\", 9);\n    i0.ɵɵlistener(\"click\", function DetailContentManagementLandownerComponent_ng_container_9_Template_button_click_9_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.inputFile.click());\n    });\n    i0.ɵɵtext(10, \"\\u4E0A\\u50B3\\u6982\\u5FF5\\u8A2D\\u8A08\\u5716\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(11, \"div\", 17);\n    i0.ɵɵtemplate(12, DetailContentManagementLandownerComponent_ng_container_9_input_12_Template, 2, 0, \"input\", 18);\n    i0.ɵɵelementStart(13, \"div\", 19)(14, \"table\", 20)(15, \"tbody\");\n    i0.ɵɵtemplate(16, DetailContentManagementLandownerComponent_ng_container_9_tr_16_Template, 11, 2, \"tr\", 6);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵtemplate(17, DetailContentManagementLandownerComponent_ng_container_9_div_17_Template, 9, 2, \"div\", 21);\n    i0.ɵɵelementStart(18, \"div\", 10)(19, \"div\", 11)(20, \"label\", 22);\n    i0.ɵɵtext(21, \"\\u5FC5\\u586B\\u6578\\u91CF\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"input\", 23);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function DetailContentManagementLandownerComponent_ng_container_9_Template_input_ngModelChange_22_listener($event) {\n      const formItemReq_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      i0.ɵɵtwoWayBindingSet(formItemReq_r2.CRequireAnswer, $event) || (formItemReq_r2.CRequireAnswer = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelement(23, \"div\", 15);\n    i0.ɵɵelementStart(24, \"div\", 10)(25, \"div\", 11)(26, \"label\", 24);\n    i0.ɵɵtext(27, \"\\u524D\\u53F0UI\\u985E\\u578B\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(28, \"nb-select\", 25);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function DetailContentManagementLandownerComponent_ng_container_9_Template_nb_select_ngModelChange_28_listener($event) {\n      const formItemReq_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      i0.ɵɵtwoWayBindingSet(formItemReq_r2.selectedCUiType, $event) || (formItemReq_r2.selectedCUiType = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"selectedChange\", function DetailContentManagementLandownerComponent_ng_container_9_Template_nb_select_selectedChange_28_listener() {\n      const formItemReq_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.changeSelectCUiType(formItemReq_r2));\n    });\n    i0.ɵɵtemplate(29, DetailContentManagementLandownerComponent_ng_container_9_nb_option_29_Template, 2, 2, \"nb-option\", 26);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelement(30, \"div\", 15);\n    i0.ɵɵelementStart(31, \"div\", 4)(32, \"div\", 11)(33, \"label\", 27);\n    i0.ɵɵtext(34, \"\\u9069\\u7528\\u6236\\u5225 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(35, DetailContentManagementLandownerComponent_ng_container_9_label_35_Template, 3, 1, \"label\", 28)(36, DetailContentManagementLandownerComponent_ng_container_9_label_36_Template, 3, 2, \"label\", 29);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(37, DetailContentManagementLandownerComponent_ng_container_9_div_37_Template, 5, 1, \"div\", 30);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const formItemReq_r2 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate3(\" \", formItemReq_r2.CName, \"-\", formItemReq_r2.CPart, \"-\", formItemReq_r2.CLocation, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtwoWayProperty(\"ngModel\", formItemReq_r2.CItemName);\n    i0.ɵɵproperty(\"placeholder\", formItemReq_r2.CItemName)(\"disabled\", ctx_r2.listFormItem.CIsLock);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngClass\", formItemReq_r2.listPictures.length ? \"\" : \"hidden\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.listFormItem.CIsLock);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", formItemReq_r2.listPictures);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", formItemReq_r2.CDesignFileUrl);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtwoWayProperty(\"ngModel\", formItemReq_r2.CRequireAnswer);\n    i0.ɵɵproperty(\"disabled\", formItemReq_r2.selectedCUiType.value === 3);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtwoWayProperty(\"ngModel\", formItemReq_r2.selectedCUiType);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.CUiTypeOptions);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.houseHoldList.length);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.houseHoldList);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", formItemReq_r2.selectedCUiType.value === 3);\n  }\n}\nexport class DetailContentManagementLandownerComponent extends BaseComponent {\n  constructor(_allow, route, message, _formItemService, _regularNoticeFileService, _utilityService, valid, location, _materialService) {\n    super(_allow);\n    this._allow = _allow;\n    this.route = route;\n    this.message = message;\n    this._formItemService = _formItemService;\n    this._regularNoticeFileService = _regularNoticeFileService;\n    this._utilityService = _utilityService;\n    this.valid = valid;\n    this.location = location;\n    this._materialService = _materialService;\n    this.typeContentManagementLandowner = {\n      CFormType: 1,\n      CNoticeType: 1\n    };\n    this.CUiTypeOptions = [{\n      value: 1,\n      label: '建材選色'\n    }, {\n      value: 2,\n      label: '群組選樣_選色'\n    }, {\n      value: 3,\n      label: '建材選樣'\n    }];\n    this.CRemarkTypeOptions = [\"正常\", \"留料\"];\n    this.selectedItems = {};\n    this.selectedRemarkType = {};\n    this.isNew = true;\n  }\n  ngOnInit() {\n    this.route.paramMap.subscribe(params => {\n      if (params) {\n        const idParam = params.get('id');\n        const id = idParam ? +idParam : 0;\n        this.buildCaseId = id;\n        if (this.buildCaseId) {\n          this.getListRegularNoticeFileHouseHold();\n        }\n      }\n    });\n  }\n  getItemByValue(value, options) {\n    for (const item of options) {\n      if (item.value === value) {\n        return item;\n      }\n    }\n    return null;\n  }\n  onCheckAllChange(checked, formItemReq_) {\n    formItemReq_.allSelected = checked;\n    this.houseHoldList.forEach(item => {\n      formItemReq_.selectedItems[item] = checked;\n    });\n  }\n  detectFiles(event, formItemReq_) {\n    const file = event.target.files[0];\n    if (file) {\n      let reader = new FileReader();\n      reader.readAsDataURL(file);\n      reader.onload = () => {\n        let base64Str = reader.result;\n        if (!base64Str) {\n          return;\n        }\n        if (formItemReq_.listPictures.length > 0) {\n          formItemReq_.listPictures[0] = {\n            id: new Date().getTime(),\n            name: file.name.split('.')[0],\n            data: base64Str,\n            extension: this._utilityService.getFileExtension(file.name),\n            CFile: file\n          };\n        } else {\n          formItemReq_.listPictures.push({\n            id: new Date().getTime(),\n            name: file.name.split('.')[0],\n            data: base64Str,\n            extension: this._utilityService.getFileExtension(file.name),\n            CFile: file\n          });\n        }\n        event.target.value = null;\n      };\n    }\n  }\n  removeImage(pictureId, formItemReq_) {\n    if (formItemReq_.listPictures.length) {\n      formItemReq_.listPictures = formItemReq_.listPictures.filter(x => x.id != pictureId);\n    }\n  }\n  renameFile(event, index, formItemReq_) {\n    var blob = formItemReq_.listPictures[index].CFile.slice(0, formItemReq_.listPictures[index].CFile.size, formItemReq_.listPictures[index].CFile.type);\n    var newFile = new File([blob], `${event.target.value + '.' + formItemReq_.listPictures[index].extension}`, {\n      type: formItemReq_.listPictures[index].CFile.type\n    });\n    formItemReq_.listPictures[index].CFile = newFile;\n  }\n  onCheckboxHouseHoldListChange(checked, item, formItemReq_) {\n    if (checked) {\n      formItemReq_.selectedItems[item] = checked;\n      formItemReq_.allSelected = this.houseHoldList.every(item => formItemReq_.selectedItems[item] == checked);\n    } else {\n      formItemReq_.allSelected = false;\n    }\n  }\n  onCheckboxRemarkChange(checked, item, formItemReq_) {\n    formItemReq_.selectedRemarkType[item] = checked;\n  }\n  createRemarkObject(CRemarkTypeOptions, CRemarkType) {\n    const remarkObject = {};\n    for (const option of CRemarkTypeOptions) {\n      remarkObject[option] = false;\n    }\n    const remarkTypes = CRemarkType.split('-');\n    for (const type of remarkTypes) {\n      if (CRemarkTypeOptions.includes(type)) {\n        remarkObject[type] = true;\n      }\n    }\n    return remarkObject;\n  }\n  getMaterialList() {\n    this._materialService.apiMaterialGetMaterialListPost$Json({\n      body: {\n        CBuildCaseId: this.buildCaseId,\n        CPagi: false\n      }\n    }).pipe(tap(res => {\n      if (res.Entries && res.StatusCode == 0) {\n        this.houseHoldList.forEach(item => this.selectedItems[item] = false);\n        this.CRemarkTypeOptions.forEach(item => this.selectedRemarkType[item] = false);\n        this.arrListFormItemReq = res.Entries.map(o => {\n          return {\n            CDesignFileUrl: null,\n            CFormItemHouseHold: null,\n            CFormId: null,\n            CLocation: o.CLocation,\n            CName: o.CName,\n            CPart: o.CPart,\n            CItemName: `${o.CName}-${o.CPart}-${o.CLocation}`,\n            CRemarkType: null,\n            CTotalAnswer: 0,\n            CRequireAnswer: undefined,\n            CUiType: 0,\n            selectedItems: {},\n            selectedRemarkType: this.selectedRemarkType,\n            allSelected: false,\n            listPictures: [],\n            selectedCUiType: this.CUiTypeOptions[0]\n          };\n        });\n        this.arrListFormItemReq = [...this.mergeItems(this.arrListFormItemReq)];\n      }\n    })).subscribe();\n  }\n  getListFormItem() {\n    this._formItemService.apiFormItemGetListFormItemPost$Json({\n      body: {\n        CBuildCaseId: this.buildCaseId,\n        CFormType: this.typeContentManagementLandowner.CFormType,\n        CIsPaging: false\n      }\n    }).pipe(tap(res => {\n      if (res.Entries && res.StatusCode == 0) {\n        this.listFormItem = res.Entries;\n        this.isNew = res.Entries.formItems ? false : true;\n        if (res.Entries.formItems) {\n          this.houseHoldList.forEach(item => this.selectedItems[item] = false);\n          this.CRemarkTypeOptions.forEach(item => this.selectedRemarkType[item] = false);\n          this.arrListFormItemReq = res.Entries.formItems.map(o => {\n            return {\n              CFormId: this.listFormItem.CFormId,\n              CDesignFileUrl: o.CDesignFileUrl,\n              CFile: o.CFile,\n              CFormItemHouseHold: o.CFormItemHouseHold,\n              CFormItemId: o.CFormItemId,\n              CLocation: o.CLocation,\n              CName: o.CName,\n              CPart: o.CPart,\n              CItemName: o.CItemName ? o.CItemName : `${o.CName}-${o.CPart}-${o.CLocation}`,\n              CRemarkType: o.CRemarkType,\n              CTotalAnswer: o.CTotalAnswer,\n              CRequireAnswer: o.CUiType === 3 ? 1 : o.CRequireAnswer,\n              CUiType: o.CUiType,\n              selectedItems: o.tblFormItemHouseholds.length ? this.createArrayObjectFromArray(this.houseHoldList, o.tblFormItemHouseholds) : {\n                ...this.selectedItems\n              },\n              selectedRemarkType: o.CRemarkType ? this.createRemarkObject(this.CRemarkTypeOptions, o.CRemarkType) : {\n                ...this.selectedRemarkType\n              },\n              allSelected: o.tblFormItemHouseholds.length == this.houseHoldList.length,\n              listPictures: [],\n              selectedCUiType: o.CUiType ? this.getItemByValue(o.CUiType, this.CUiTypeOptions) : this.CUiTypeOptions[0]\n            };\n          });\n        } else {\n          this.getMaterialList();\n        }\n      }\n    })).subscribe();\n  }\n  changeSelectCUiType(formItemReq) {\n    if (formItemReq.selectedCUiType && formItemReq.selectedCUiType.value === 3) {\n      formItemReq.CRequireAnswer = 1;\n    }\n  }\n  getHouseHoldListByNoticeType(data) {\n    for (let item of data) {\n      if (item.CNoticeType === this.typeContentManagementLandowner.CNoticeType) {\n        return item.CHouseHoldList;\n      }\n    }\n    return [];\n  }\n  getKeysWithTrueValue(obj) {\n    return Object.keys(obj).filter(key => obj[key]);\n  }\n  getKeysWithTrueValueJoined(obj) {\n    return Object.keys(obj).filter(key => obj[key]).join('-');\n  }\n  getCRemarkType(selectedCUiType, selectedRemarkType) {\n    if (selectedCUiType && selectedCUiType.value == 3) {\n      return this.getKeysWithTrueValueJoined(selectedRemarkType);\n    }\n  }\n  getStringAfterComma(inputString) {\n    const parts = inputString.split(',');\n    if (parts.length > 1) {\n      return parts[1];\n    } else return \"\";\n  }\n  formatFile(listPictures) {\n    if (listPictures && listPictures.length > 0) {\n      return {\n        Base64String: this.getStringAfterComma(listPictures[0].data) || null,\n        FileExtension: listPictures[0].extension || null,\n        FileName: listPictures[0].CFile.name || listPictures[0].name || null\n      };\n    } else return undefined;\n  }\n  // formatParam() {\n  //   const result: SaveListFormItemReq[] = \n  //   })\n  //   return result\n  // }\n  mergeItems(items) {\n    const map = new Map();\n    items.forEach(item => {\n      const key = `${item.CLocation}_${item.CName}_${item.CPart}`;\n      if (map.has(key)) {\n        const existing = map.get(key);\n        existing.count += 1;\n      } else {\n        map.set(key, {\n          item: {\n            ...item\n          },\n          count: 1\n        });\n      }\n    });\n    return Array.from(map.values()).map(({\n      item,\n      count\n    }) => ({\n      ...item,\n      CTotalAnswer: count\n    }));\n  }\n  validation() {\n    this.valid.clear();\n    let hasInvalidCUiType = false;\n    let hasInvalidCRequireAnswer = false;\n    let hasInvalidItemName = false;\n    for (const item of this.saveListFormItemReq) {\n      if (!hasInvalidCUiType && !item.CUiType) {\n        hasInvalidCUiType = true;\n      }\n      if (!hasInvalidCRequireAnswer && !item.CRequireAnswer) {\n        hasInvalidCRequireAnswer = true;\n      }\n      if (item.CTotalAnswer && item.CRequireAnswer) {\n        if (item.CRequireAnswer > item.CTotalAnswer) {\n          this.valid.addErrorMessage('[必填數量]' + ' <= ' + item.CTotalAnswer + ` (${item.CItemName}) `);\n        }\n      }\n      if (!hasInvalidItemName && !item.CItemName) {\n        hasInvalidItemName = true;\n      }\n    }\n    if (hasInvalidCUiType) {\n      this.valid.addErrorMessage('[前台UI類型]' + ' 必填');\n    }\n    if (hasInvalidCRequireAnswer) {\n      this.valid.addErrorMessage('[必填數量]' + ' 必填且>0');\n    }\n    if (hasInvalidItemName) {\n      this.valid.addErrorMessage('[廚房-廚具-櫃體]' + ' 必填');\n    }\n  }\n  onSubmit() {\n    this.saveListFormItemReq = this.arrListFormItemReq.map(e => {\n      return {\n        CDesignFileUrl: e.CDesignFileUrl ? e.CDesignFileUrl : null,\n        CFile: e.listPictures ? this.formatFile(e.listPictures) : undefined,\n        CFormItemHouseHold: this.getKeysWithTrueValue(e.selectedItems),\n        CFormItemId: e.CFormItemId ? e.CFormItemId : null,\n        CFormID: this.isNew ? null : this.listFormItem.CFormId,\n        CName: e.CName,\n        CPart: e.CPart,\n        CLocation: e.CLocation,\n        CItemName: e.CItemName,\n        // ? e.CItemName : `${e.CName}-${e.CPart}-${e.CLocation}`,\n        CRemarkType: e.selectedCUiType.value !== 3 ? null : this.getCRemarkType(e.selectedCUiType, e.selectedRemarkType) || null,\n        CTotalAnswer: e.CTotalAnswer,\n        CRequireAnswer: e.CRequireAnswer,\n        CUiType: e.selectedCUiType.value\n      };\n    });\n    this.validation();\n    if (this.valid.errorMessages.length > 0) {\n      this.message.showErrorMSGs(this.valid.errorMessages);\n      return;\n    }\n    if (this.isNew) {\n      this.createListFormItem();\n    } else {\n      this.saveListFormItem();\n    }\n  }\n  saveListFormItem() {\n    this._formItemService.apiFormItemSaveListFormItemPost$Json({\n      body: this.saveListFormItemReq\n    }).subscribe(res => {\n      if (res.StatusCode == 0) {\n        this.message.showSucessMSG(\"執行成功\");\n        // this.getListFormItem()\n        this.goBack();\n      }\n    });\n  }\n  createListFormItem() {\n    this.creatListFormItem = {\n      CBuildCaseId: this.buildCaseId,\n      CFormItem: this.saveListFormItemReq || null,\n      CFormType: this.typeContentManagementLandowner.CFormType\n    };\n    this._formItemService.apiFormItemCreateListFormItemPost$Json({\n      body: this.creatListFormItem\n    }).subscribe(res => {\n      if (res.StatusCode == 0) {\n        this.message.showSucessMSG(\"執行成功\");\n        // this.getListFormItem()\n        this.goBack();\n      }\n    });\n  }\n  createArrayObjectFromArray(a, b) {\n    const c = {};\n    for (const item of a) {\n      const matchingItem = b.find(bItem => bItem.CHousehold === item && bItem.CIsSelect);\n      c[item] = !!matchingItem;\n    }\n    return c;\n  } //[\"House1\", \"House2\", \"House3\"] => [{CHousehold: \"House1\", CIsSelect: true,... }, ... ]\n  getListRegularNoticeFileHouseHold() {\n    this._regularNoticeFileService.apiRegularNoticeFileGetListRegularNoticeFileHouseHoldPost$Json({\n      body: this.buildCaseId\n    }).pipe(tap(res => {\n      if (res.Entries && res.StatusCode == 0) {\n        this.houseHoldList = this.getHouseHoldListByNoticeType(res.Entries);\n        this.getListFormItem();\n      }\n    })).subscribe();\n  }\n  goBack() {\n    this.location.back();\n  }\n  static {\n    this.ɵfac = function DetailContentManagementLandownerComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || DetailContentManagementLandownerComponent)(i0.ɵɵdirectiveInject(i1.AllowHelper), i0.ɵɵdirectiveInject(i2.ActivatedRoute), i0.ɵɵdirectiveInject(i3.MessageService), i0.ɵɵdirectiveInject(i4.FormItemService), i0.ɵɵdirectiveInject(i4.RegularNoticeFileService), i0.ɵɵdirectiveInject(i5.UtilityService), i0.ɵɵdirectiveInject(i6.ValidationHelper), i0.ɵɵdirectiveInject(i7.Location), i0.ɵɵdirectiveInject(i4.MaterialService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: DetailContentManagementLandownerComponent,\n      selectors: [[\"ngx-detail-content-management-landowner\"]],\n      standalone: true,\n      features: [i0.ɵɵInheritDefinitionFeature, i0.ɵɵStandaloneFeature],\n      decls: 15,\n      vars: 1,\n      consts: [[\"inputFile\", \"\"], [\"accent\", \"success\"], [1, \"font-bold\", \"text-[#818181]\"], [1, \"d-flex\", \"flex-wrap\"], [1, \"col-md-12\"], [1, \"font-bold\", \"text-lg\", \"px-3\", \"pb-3\"], [4, \"ngFor\", \"ngForOf\"], [1, \"d-flex\", \"justify-content-center\"], [1, \"btn\", \"btn-secondary\", \"mx-2\", 3, \"click\"], [1, \"btn\", \"btn-info\", 3, \"click\"], [1, \"col-md-9\"], [1, \"form-group\", \"d-flex\", \"align-items-center\", \"w-full\"], [\"for\", \"CItemName\", 1, \"label\", \"col-4\", \"text-base\"], [1, \"input-group\", \"items-center\", \"w-full\", \"col-8\", \"px-0\"], [\"type\", \"text\", \"nbInput\", \"\", 1, \"w-full\", \"col-12\", 3, \"ngModelChange\", \"ngModel\", \"placeholder\", \"disabled\"], [1, \"col-md-3\"], [1, \"d-flex\", \"justify-content-end\", \"w-full\"], [1, \"col-md-12\", 3, \"ngClass\"], [\"type\", \"file\", \"class\", \"hidden\", \"accept\", \"image/png, image/gif, image/jpeg\", 3, \"change\", 4, \"ngIf\"], [1, \"mt-3\", \"w-full\", \"flex\", \"flex-col\"], [1, \"table\", \"table-striped\", \"border\", 2, \"background-color\", \"#f3f3f3\"], [\"class\", \"col-md-12 text-center\", 3, \"ngClass\", 4, \"ngIf\"], [\"for\", \"cRequireAnswer\", 1, \"label\", \"col-4\"], [\"type\", \"number\", \"nbInput\", \"\", \"placeholder\", \"\\u5FC5\\u586B\\u6578\\u91CF\", 1, \"col-8\", 3, \"ngModelChange\", \"ngModel\", \"disabled\"], [\"for\", \"buildingName\", 1, \"label\", \"col-4\"], [\"placeholder\", \"\\u5EFA\\u6848\", 1, \"col-8\", \"px-0\", 3, \"ngModelChange\", \"selectedChange\", \"ngModel\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"for\", \"buildingName\", 1, \"label\", \"col-3\"], [\"class\", \"mr-2\", 4, \"ngIf\"], [\"class\", \"mr-2\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"col-md-12\", 4, \"ngIf\"], [\"type\", \"file\", \"accept\", \"image/png, image/gif, image/jpeg\", 1, \"hidden\", 3, \"change\"], [1, \"align-middle\"], [\"nbInput\", \"\", \"type\", \"text\", 1, \"w-100\", \"p-2\", \"text-[13px]\", 3, \"blur\", \"value\"], [1, \"w-[100px]\", \"h-auto\"], [1, \"fit-size\", 3, \"src\"], [1, \"text-center\", \"w-32\", \"align-middle\"], [1, \"btn\", \"btn-outline-danger\", \"btn-sm\", \"m-1\", 3, \"click\"], [1, \"col-md-12\", \"text-center\", 3, \"ngClass\"], [1, \"table\", \"table-striped\", \"border\"], [1, \"w-[80px]\", \"h-auto\"], [\"class\", \"w-14 h-14\", 3, \"src\", 4, \"ngIf\"], [1, \"w-14\", \"h-14\", 3, \"src\"], [3, \"value\"], [1, \"mr-2\"], [3, \"checkedChange\", \"checked\"], [\"value\", \"item\", 3, \"checkedChange\", \"checked\"], [\"value\", \"item\", 3, \"checked\", \"checkedChange\", 4, \"ngIf\"]],\n      template: function DetailContentManagementLandownerComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"nb-card\", 1)(1, \"nb-card-header\");\n          i0.ɵɵelement(2, \"ngx-breadcrumb\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(3, \"nb-card-body\");\n          i0.ɵɵelement(4, \"h1\", 2);\n          i0.ɵɵelementStart(5, \"div\", 3)(6, \"div\", 4)(7, \"h4\", 5);\n          i0.ɵɵtext(8, \"\\u985E\\u578B-\\u7368\\u7ACB\\u9078\\u6A23\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(9, DetailContentManagementLandownerComponent_ng_container_9_Template, 38, 17, \"ng-container\", 6);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(10, \"nb-card-footer\", 7)(11, \"button\", 8);\n          i0.ɵɵlistener(\"click\", function DetailContentManagementLandownerComponent_Template_button_click_11_listener() {\n            return ctx.goBack();\n          });\n          i0.ɵɵtext(12, \" \\u53D6\\u6D88 \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(13, \"button\", 9);\n          i0.ɵɵlistener(\"click\", function DetailContentManagementLandownerComponent_Template_button_click_13_listener() {\n            return ctx.onSubmit();\n          });\n          i0.ɵɵtext(14, \" \\u5132\\u5B58 \");\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(9);\n          i0.ɵɵproperty(\"ngForOf\", ctx.arrListFormItemReq);\n        }\n      },\n      dependencies: [CommonModule, i7.NgClass, i7.NgForOf, i7.NgIf, SharedModule, i8.DefaultValueAccessor, i8.NumberValueAccessor, i8.NgControlStatus, i8.NgModel, i9.NbCardComponent, i9.NbCardBodyComponent, i9.NbCardFooterComponent, i9.NbCardHeaderComponent, i9.NbCheckboxComponent, i9.NbInputDirective, i9.NbSelectComponent, i9.NbOptionComponent, i10.BreadcrumbComponent, i11.BaseFilePipe, NbCheckboxModule],\n      styles: [\"/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsImZpbGUiOiJkZXRhaWwtY29udGVudC1tYW5hZ2VtZW50LWxhbmRvd25lci5jb21wb25lbnQuc2NzcyJ9 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvcGFnZXMvc2VsZWN0aW9uLW1hbmFnZW1lbnQvY29udGVudC1tYW5hZ2VtZW50LWxhbmRvd25lci9kZXRhaWwtY29udGVudC1tYW5hZ2VtZW50LWxhbmRvd25lci9kZXRhaWwtY29udGVudC1tYW5hZ2VtZW50LWxhbmRvd25lci5jb21wb25lbnQuc2NzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiO0FBQ0Esd01BQXdNIiwic291cmNlUm9vdCI6IiJ9 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["SharedModule", "CommonModule", "NbCheckboxModule", "BaseComponent", "tap", "i0", "ɵɵelementStart", "ɵɵlistener", "DetailContentManagementLandownerComponent_ng_container_9_input_12_Template_input_change_0_listener", "$event", "ɵɵrestoreView", "_r4", "formItemReq_r2", "ɵɵnextContext", "$implicit", "ctx_r2", "ɵɵresetView", "detectFiles", "ɵɵelementEnd", "ɵɵtext", "DetailContentManagementLandownerComponent_ng_container_9_tr_16_Template_input_blur_5_listener", "i_r6", "_r5", "index", "renameFile", "ɵɵelement", "DetailContentManagementLandownerComponent_ng_container_9_tr_16_Template_button_click_9_listener", "picture_r7", "removeImage", "id", "ɵɵadvance", "ɵɵproperty", "name", "data", "ɵɵsanitizeUrl", "ɵɵpipeBind1", "CDesignFileUrl", "ɵɵtemplate", "DetailContentManagementLandownerComponent_ng_container_9_div_17_img_7_Template", "listPictures", "length", "case_r8", "ɵɵtextInterpolate1", "label", "ɵɵtwoWayListener", "DetailContentManagementLandownerComponent_ng_container_9_label_35_Template_nb_checkbox_checkedChange_1_listener", "_r9", "ɵɵtwoWayBindingSet", "allSelected", "onCheckAllChange", "ɵɵtwoWayProperty", "DetailContentManagementLandownerComponent_ng_container_9_label_36_Template_nb_checkbox_checkedChange_1_listener", "item_r11", "_r10", "selectedItems", "onCheckboxHouseHoldListChange", "DetailContentManagementLandownerComponent_ng_container_9_div_37_label_4_nb_checkbox_1_Template_nb_checkbox_checkedChange_0_listener", "_r12", "remark_r13", "selectedRemarkType", "onCheckboxRemarkChange", "DetailContentManagementLandownerComponent_ng_container_9_div_37_label_4_nb_checkbox_1_Template", "DetailContentManagementLandownerComponent_ng_container_9_div_37_label_4_Template", "CRemarkTypeOptions", "ɵɵelementContainerStart", "DetailContentManagementLandownerComponent_ng_container_9_Template_input_ngModelChange_6_listener", "_r1", "CItemName", "DetailContentManagementLandownerComponent_ng_container_9_Template_button_click_9_listener", "inputFile", "click", "DetailContentManagementLandownerComponent_ng_container_9_input_12_Template", "DetailContentManagementLandownerComponent_ng_container_9_tr_16_Template", "DetailContentManagementLandownerComponent_ng_container_9_div_17_Template", "DetailContentManagementLandownerComponent_ng_container_9_Template_input_ngModelChange_22_listener", "CRequireAnswer", "DetailContentManagementLandownerComponent_ng_container_9_Template_nb_select_ngModelChange_28_listener", "selectedCUiType", "DetailContentManagementLandownerComponent_ng_container_9_Template_nb_select_selectedChange_28_listener", "changeSelectCUiType", "DetailContentManagementLandownerComponent_ng_container_9_nb_option_29_Template", "DetailContentManagementLandownerComponent_ng_container_9_label_35_Template", "DetailContentManagementLandownerComponent_ng_container_9_label_36_Template", "DetailContentManagementLandownerComponent_ng_container_9_div_37_Template", "ɵɵtextInterpolate3", "CName", "<PERSON>art", "CLocation", "listFormItem", "CIsLock", "value", "CUiTypeOptions", "houseHoldList", "DetailContentManagementLandownerComponent", "constructor", "_allow", "route", "message", "_formItemService", "_regularNoticeFileService", "_utilityService", "valid", "location", "_materialService", "typeContentManagementLandowner", "CFormType", "CNoticeType", "isNew", "ngOnInit", "paramMap", "subscribe", "params", "idParam", "get", "buildCaseId", "getListRegularNoticeFileHouseHold", "getItemByValue", "options", "item", "checked", "formItemReq_", "for<PERSON>ach", "event", "file", "target", "files", "reader", "FileReader", "readAsDataURL", "onload", "base64Str", "result", "Date", "getTime", "split", "extension", "getFileExtension", "CFile", "push", "pictureId", "filter", "x", "blob", "slice", "size", "type", "newFile", "File", "every", "createRemarkObject", "CRemarkType", "remarkObject", "option", "remarkTypes", "includes", "getMaterialList", "apiMaterialGetMaterialListPost$Json", "body", "CBuildCaseId", "CPagi", "pipe", "res", "Entries", "StatusCode", "arrListFormItemReq", "map", "o", "CFormItemHouseHold", "CFormId", "CTotalAnswer", "undefined", "CUiType", "mergeItems", "getListFormItem", "apiFormItemGetListFormItemPost$Json", "CIsPaging", "formItems", "CFormItemId", "tblFormItemHouseholds", "createArrayObjectFromArray", "formItemReq", "getHouseHoldListByNoticeType", "CHouseHoldList", "getKeysWithTrueValue", "obj", "Object", "keys", "key", "getKeysWithTrueValueJoined", "join", "getCRemarkType", "getStringAfterComma", "inputString", "parts", "formatFile", "Base64String", "FileExtension", "FileName", "items", "Map", "has", "existing", "count", "set", "Array", "from", "values", "validation", "clear", "hasInvalidCUiType", "hasInvalidCRequireAnswer", "hasInvalidItemName", "saveListFormItemReq", "addErrorMessage", "onSubmit", "e", "CFormID", "errorMessages", "showErrorMSGs", "createListFormItem", "saveListFormItem", "apiFormItemSaveListFormItemPost$Json", "showSucessMSG", "goBack", "creatListFormItem", "CFormItem", "apiFormItemCreateListFormItemPost$Json", "a", "b", "c", "matchingItem", "find", "bItem", "CHousehold", "CIsSelect", "apiRegularNoticeFileGetListRegularNoticeFileHouseHoldPost$Json", "back", "ɵɵdirectiveInject", "i1", "AllowHelper", "i2", "ActivatedRoute", "i3", "MessageService", "i4", "FormItemService", "RegularNoticeFileService", "i5", "UtilityService", "i6", "ValidationHelper", "i7", "Location", "MaterialService", "selectors", "standalone", "features", "ɵɵInheritDefinitionFeature", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "DetailContentManagementLandownerComponent_Template", "rf", "ctx", "DetailContentManagementLandownerComponent_ng_container_9_Template", "DetailContentManagementLandownerComponent_Template_button_click_11_listener", "DetailContentManagementLandownerComponent_Template_button_click_13_listener", "Ng<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "i8", "DefaultValueAccessor", "NumberValueAccessor", "NgControlStatus", "NgModel", "i9", "NbCardComponent", "NbCardBodyComponent", "NbCardFooterComponent", "NbCardHeaderComponent", "NbCheckboxComponent", "NbInputDirective", "NbSelectComponent", "NbOptionComponent", "i10", "BreadcrumbComponent", "i11", "BaseFilePipe", "styles"], "sources": ["C:\\Users\\<USER>\\Documents\\jeansalechange\\adminFront\\src\\app\\pages\\selection-management\\content-management-landowner\\detail-content-management-landowner\\detail-content-management-landowner.component.ts", "C:\\Users\\<USER>\\Documents\\jeansalechange\\adminFront\\src\\app\\pages\\selection-management\\content-management-landowner\\detail-content-management-landowner\\detail-content-management-landowner.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { SharedModule } from '../../../components/shared.module';\r\nimport { CommonModule, Location } from '@angular/common';\r\nimport { NbCheckboxModule } from '@nebular/theme';\r\nimport { BaseComponent } from '../../../components/base/baseComponent';\r\nimport { AllowHelper } from 'src/app/shared/helper/allowHelper';\r\nimport { FormItemService, MaterialService, RegularNoticeFileService } from 'src/services/api/services';\r\nimport { ActivatedRoute } from '@angular/router';\r\nimport { MessageService } from 'src/app/shared/services/message.service';\r\n\r\nimport { tap } from 'rxjs';\r\nimport { CreateListFormItem, FileViewModel, GetListFormItemRes, SaveListFormItemReq } from 'src/services/api/models';\r\nimport { BaseFilePipe } from 'src/app/@theme/pipes/base-file.pipe';\r\nimport { UtilityService } from 'src/app/shared/services/utility.service';\r\nimport { ValidationHelper } from 'src/app/shared/helper/validationHelper';\r\n\r\nexport interface ExtendedSaveListFormItemReq {\r\n  CDesignFileUrl?: string | null;\r\n  CFile?: FileViewModel,\r\n  CFormItemHouseHold?: Array<string> | null;\r\n  CFormItemId?: number;\r\n  CItemName?: string;\r\n  CFormID?: number;\r\n  CPart?: string | null;\r\n  CName?: string | null;\r\n  CLocation?: string | null;\r\n  CRemarkType?: string | null;\r\n  CTotalAnswer?: number;\r\n  CRequireAnswer?: number;\r\n  CUiType?: number;\r\n  selectedCUiType: any | null;\r\n  selectedItems: { [key: string]: boolean }\r\n  selectedRemarkType?: { [key: string]: boolean }\r\n  allSelected: boolean,\r\n  listPictures: any[]\r\n}\r\n\r\n@Component({\r\n  selector: 'ngx-detail-content-management-landowner',\r\n  templateUrl: './detail-content-management-landowner.component.html',\r\n  styleUrls: ['./detail-content-management-landowner.component.scss'],\r\n  standalone: true,\r\n  imports: [CommonModule, SharedModule, NbCheckboxModule, BaseFilePipe],\r\n\r\n})\r\n\r\nexport class DetailContentManagementLandownerComponent extends BaseComponent implements OnInit {\r\n  constructor(\r\n    private _allow: AllowHelper,\r\n    private route: ActivatedRoute,\r\n    private message: MessageService,\r\n    private _formItemService: FormItemService,\r\n    private _regularNoticeFileService: RegularNoticeFileService,\r\n    private _utilityService: UtilityService,\r\n    private valid: ValidationHelper,\r\n    private location: Location,\r\n    private _materialService: MaterialService\r\n  ) {\r\n    super(_allow)\r\n  }\r\n\r\n  typeContentManagementLandowner = {\r\n    CFormType: 1,\r\n    CNoticeType: 1\r\n  }\r\n  CUiTypeOptions: any[] = [\r\n    {\r\n      value: 1, label: '建材選色'\r\n    },\r\n    {\r\n      value: 2, label: '群組選樣_選色'\r\n    }, {\r\n      value: 3, label: '建材選樣'\r\n    }\r\n  ]\r\n  CRemarkTypeOptions = [\"正常\", \"留料\"]\r\n  buildCaseId: number\r\n\r\n  override ngOnInit(): void {\r\n    this.route.paramMap.subscribe(params => {\r\n      if (params) {\r\n        const idParam = params.get('id');\r\n        const id = idParam ? +idParam : 0;\r\n        this.buildCaseId = id\r\n        if (this.buildCaseId) {\r\n          this.getListRegularNoticeFileHouseHold()\r\n        }\r\n      }\r\n    })\r\n  }\r\n\r\n\r\n  getItemByValue(value: any, options: any[]) {\r\n    for (const item of options) {\r\n      if (item.value === value) {\r\n        return item;\r\n      }\r\n    }\r\n    return null;\r\n  }\r\n\r\n\r\n  selectedItems: { [key: string]: boolean } = {};\r\n  selectedRemarkType: { [key: string]: boolean } = {};\r\n\r\n  onCheckAllChange(checked: boolean, formItemReq_: any) {\r\n    formItemReq_.allSelected = checked;\r\n    this.houseHoldList.forEach(item => {\r\n      formItemReq_.selectedItems[item] = checked;\r\n    });\r\n  }\r\n\r\n\r\n  detectFiles(event: any, formItemReq_: any) {\r\n    const file = event.target.files[0]\r\n    if (file) {\r\n      let reader = new FileReader();\r\n      reader.readAsDataURL(file);\r\n      reader.onload = () => {\r\n        let base64Str: string = reader.result as string;\r\n        if (!base64Str) {\r\n          return;\r\n        }\r\n        if (formItemReq_.listPictures.length > 0) {\r\n          formItemReq_.listPictures[0] = {\r\n            id: new Date().getTime(),\r\n            name: file.name.split('.')[0],\r\n            data: base64Str,\r\n            extension: this._utilityService.getFileExtension(file.name),\r\n            CFile: file\r\n          };\r\n        } else {\r\n          formItemReq_.listPictures.push({\r\n            id: new Date().getTime(),\r\n            name: file.name.split('.')[0],\r\n            data: base64Str,\r\n            extension: this._utilityService.getFileExtension(file.name),\r\n            CFile: file\r\n          });\r\n        }\r\n        event.target.value = null;\r\n      };\r\n    }\r\n  }\r\n\r\n  removeImage(pictureId: number, formItemReq_: any) {\r\n    if (formItemReq_.listPictures.length) {\r\n      formItemReq_.listPictures = formItemReq_.listPictures.filter((x: any) => x.id != pictureId)\r\n    }\r\n  }\r\n\r\n  renameFile(event: any, index: number, formItemReq_: any) {\r\n    var blob = formItemReq_.listPictures[index].CFile.slice(0, formItemReq_.listPictures[index].CFile.size, formItemReq_.listPictures[index].CFile.type);\r\n    var newFile = new File([blob], `${event.target.value + '.' + formItemReq_.listPictures[index].extension}`, { type: formItemReq_.listPictures[index].CFile.type });\r\n    formItemReq_.listPictures[index].CFile = newFile\r\n  }\r\n\r\n  onCheckboxHouseHoldListChange(checked: boolean, item: string, formItemReq_: any) {\r\n    if (checked) {\r\n      formItemReq_.selectedItems[item] = checked;\r\n      formItemReq_.allSelected = this.houseHoldList.every(item => formItemReq_.selectedItems[item] == checked);\r\n    } else {\r\n      formItemReq_.allSelected = false\r\n    }\r\n  }\r\n\r\n\r\n\r\n  onCheckboxRemarkChange(checked: boolean, item: string, formItemReq_: any) {\r\n    formItemReq_.selectedRemarkType[item] = checked;\r\n  }\r\n\r\n  createRemarkObject(CRemarkTypeOptions: string[], CRemarkType: string): { [key: string]: boolean } {\r\n    const remarkObject: { [key: string]: boolean } = {};\r\n    for (const option of CRemarkTypeOptions) {\r\n      remarkObject[option] = false;\r\n    }\r\n    const remarkTypes = CRemarkType.split('-');\r\n    for (const type of remarkTypes) {\r\n      if (CRemarkTypeOptions.includes(type)) {\r\n        remarkObject[type] = true;\r\n      }\r\n    }\r\n    return remarkObject;\r\n  }\r\n\r\n  getMaterialList() { // call when create\r\n    this._materialService.apiMaterialGetMaterialListPost$Json({\r\n      body: {\r\n        CBuildCaseId: this.buildCaseId,\r\n        CPagi: false\r\n      }\r\n    }).pipe(\r\n      tap(res => {\r\n        if (res.Entries && res.StatusCode == 0) {\r\n\r\n          this.houseHoldList.forEach(item => this.selectedItems[item] = false)\r\n\r\n          this.CRemarkTypeOptions.forEach(item => this.selectedRemarkType[item] = false)\r\n          this.arrListFormItemReq = res.Entries.map((o: any) => {\r\n            return {\r\n              CDesignFileUrl: null,\r\n              CFormItemHouseHold: null,\r\n              CFormId: null,\r\n              CLocation: o.CLocation,\r\n              CName: o.CName,\r\n              CPart: o.CPart,\r\n              CItemName: `${o.CName}-${o.CPart}-${o.CLocation}`,\r\n              CRemarkType: null,\r\n              CTotalAnswer: 0,\r\n              CRequireAnswer: undefined,\r\n              CUiType: 0,\r\n              selectedItems: {},\r\n              selectedRemarkType: this.selectedRemarkType,\r\n              allSelected: false,\r\n              listPictures: [],\r\n              selectedCUiType: this.CUiTypeOptions[0]\r\n            }\r\n          })\r\n          this.arrListFormItemReq = [...this.mergeItems(this.arrListFormItemReq)]\r\n        }\r\n      })\r\n    ).subscribe();\r\n  }\r\n\r\n  listFormItem: GetListFormItemRes\r\n  isNew: boolean = true\r\n\r\n  getListFormItem() {\r\n    this._formItemService.apiFormItemGetListFormItemPost$Json({\r\n      body: {\r\n        CBuildCaseId: this.buildCaseId,\r\n        CFormType: this.typeContentManagementLandowner.CFormType,\r\n        CIsPaging: false\r\n      }\r\n    }).pipe(\r\n      tap(res => {\r\n        if (res.Entries && res.StatusCode == 0) {\r\n          this.listFormItem = res.Entries\r\n          this.isNew = res.Entries.formItems ? false : true\r\n          if (res.Entries.formItems) {\r\n\r\n            this.houseHoldList.forEach(item => this.selectedItems[item] = false)\r\n            this.CRemarkTypeOptions.forEach(item => this.selectedRemarkType[item] = false)\r\n\r\n            this.arrListFormItemReq = res.Entries.formItems.map((o: any) => {\r\n              return {\r\n                CFormId: this.listFormItem.CFormId,\r\n                CDesignFileUrl: o.CDesignFileUrl,\r\n                CFile: o.CFile,\r\n                CFormItemHouseHold: o.CFormItemHouseHold,\r\n                CFormItemId: o.CFormItemId,\r\n                CLocation: o.CLocation,\r\n                CName: o.CName,\r\n                CPart: o.CPart,\r\n                CItemName: o.CItemName ? o.CItemName : `${o.CName}-${o.CPart}-${o.CLocation}`,\r\n                CRemarkType: o.CRemarkType,\r\n                CTotalAnswer: o.CTotalAnswer,\r\n                CRequireAnswer: o.CUiType===3 ? 1: o.CRequireAnswer,\r\n                CUiType: o.CUiType,\r\n                selectedItems: o.tblFormItemHouseholds.length ? this.createArrayObjectFromArray(this.houseHoldList, o.tblFormItemHouseholds) : { ...this.selectedItems },\r\n                selectedRemarkType: o.CRemarkType ? this.createRemarkObject(this.CRemarkTypeOptions, o.CRemarkType) : { ...this.selectedRemarkType },\r\n                allSelected: o.tblFormItemHouseholds.length == this.houseHoldList.length,\r\n                listPictures: [],\r\n                selectedCUiType: o.CUiType ? this.getItemByValue(o.CUiType, this.CUiTypeOptions) : this.CUiTypeOptions[0]\r\n              }\r\n            })\r\n          } else {\r\n            this.getMaterialList()\r\n          }\r\n        }\r\n      })\r\n    ).subscribe()\r\n  }\r\n\r\n  changeSelectCUiType(formItemReq: any) {\r\n    if(formItemReq.selectedCUiType && formItemReq.selectedCUiType.value ===3) {\r\n      formItemReq.CRequireAnswer = 1\r\n    }\r\n  }\r\n\r\n  getHouseHoldListByNoticeType(data: any[]) {\r\n    for (let item of data) {\r\n      if (item.CNoticeType === this.typeContentManagementLandowner.CNoticeType) {\r\n        return item.CHouseHoldList;\r\n      }\r\n    }\r\n    return [];\r\n  }\r\n\r\n  arrListFormItemReq: ExtendedSaveListFormItemReq[]\r\n\r\n  getKeysWithTrueValue(obj: Record<string, boolean>): string[] { // {\"key\": true } => [\"key\"]\r\n    return Object.keys(obj).filter(key => obj[key]);\r\n  }\r\n\r\n  getKeysWithTrueValueJoined(obj: Record<string, boolean>): string {\r\n    return Object.keys(obj)\r\n      .filter(key => obj[key])\r\n      .join('-');\r\n  }\r\n\r\n  getCRemarkType(selectedCUiType: any, selectedRemarkType: any): any {\r\n    if (selectedCUiType && selectedCUiType.value == 3) {\r\n      return this.getKeysWithTrueValueJoined(selectedRemarkType)\r\n    }\r\n  }\r\n\r\n  getStringAfterComma(inputString: string): string {\r\n    const parts = inputString.split(',');\r\n    if (parts.length > 1) {\r\n      return parts[1];\r\n    } else return \"\"\r\n  }\r\n\r\n  formatFile(listPictures: any) {\r\n    if (listPictures && listPictures.length > 0) {\r\n      return {\r\n        Base64String: this.getStringAfterComma(listPictures[0].data) || null,\r\n        FileExtension: listPictures[0].extension || null,\r\n        FileName: listPictures[0].CFile.name || listPictures[0].name || null,\r\n      }\r\n    } else return undefined\r\n\r\n  }\r\n\r\n  // formatParam() {\r\n  //   const result: SaveListFormItemReq[] = \r\n  //   })\r\n  //   return result\r\n  // }\r\n  mergeItems(items: ExtendedSaveListFormItemReq[]): ExtendedSaveListFormItemReq[] {\r\n    const map = new Map<string, { item: ExtendedSaveListFormItemReq, count: number }>();\r\n\r\n    items.forEach(item => {\r\n      const key = `${item.CLocation}_${item.CName}_${item.CPart}`;\r\n      if (map.has(key)) {\r\n        const existing = map.get(key)!;\r\n        existing.count += 1;\r\n      } else {\r\n        map.set(key, { item: { ...item }, count: 1 });\r\n      }\r\n    });\r\n\r\n    return Array.from(map.values()).map(({ item, count }) => ({\r\n      ...item,\r\n      CTotalAnswer: count\r\n    }));\r\n  }\r\n\r\n\r\n  validation() {\r\n    this.valid.clear();\r\n    let hasInvalidCUiType = false\r\n    let hasInvalidCRequireAnswer = false\r\n    let hasInvalidItemName = false;\r\n    for (const item of this.saveListFormItemReq) {\r\n      if (!hasInvalidCUiType && (!item.CUiType)) {\r\n        hasInvalidCUiType = true;\r\n      }\r\n      if (!hasInvalidCRequireAnswer && (!item.CRequireAnswer)) {\r\n        hasInvalidCRequireAnswer = true;\r\n      }\r\n\r\n      if (item.CTotalAnswer && item.CRequireAnswer) {\r\n        if (item.CRequireAnswer > item.CTotalAnswer) {\r\n          this.valid.addErrorMessage('[必填數量]' + ' <= ' + item.CTotalAnswer + ` (${item.CItemName}) `);\r\n        }\r\n      }\r\n      if (!hasInvalidItemName && (!item.CItemName)) {\r\n        hasInvalidItemName = true;\r\n      }\r\n    }\r\n    if (hasInvalidCUiType) {\r\n      this.valid.addErrorMessage('[前台UI類型]' + ' 必填');\r\n    }\r\n    if (hasInvalidCRequireAnswer) {\r\n      this.valid.addErrorMessage('[必填數量]' + ' 必填且>0');\r\n    }\r\n    if (hasInvalidItemName) {\r\n      this.valid.addErrorMessage('[廚房-廚具-櫃體]' + ' 必填');\r\n    }\r\n  }\r\n\r\n  saveListFormItemReq: SaveListFormItemReq[]\r\n\r\n  onSubmit() {\r\n    this.saveListFormItemReq = this.arrListFormItemReq.map((e: any) => {\r\n      return {\r\n        CDesignFileUrl: e.CDesignFileUrl ? e.CDesignFileUrl : null,\r\n        CFile: e.listPictures ? this.formatFile(e.listPictures) : undefined,\r\n        CFormItemHouseHold: this.getKeysWithTrueValue(e.selectedItems),\r\n        CFormItemId: e.CFormItemId ? e.CFormItemId : null,\r\n        CFormID: this.isNew ? null : this.listFormItem.CFormId,\r\n        CName: e.CName,\r\n        CPart: e.CPart,\r\n        CLocation: e.CLocation,\r\n        CItemName: e.CItemName,// ? e.CItemName : `${e.CName}-${e.CPart}-${e.CLocation}`,\r\n        CRemarkType: e.selectedCUiType.value !== 3 ? null : this.getCRemarkType(e.selectedCUiType, e.selectedRemarkType) || null,\r\n        CTotalAnswer: e.CTotalAnswer,\r\n        CRequireAnswer: e.CRequireAnswer,\r\n        CUiType: e.selectedCUiType.value,\r\n      }\r\n    })\r\n    this.validation()\r\n    if (this.valid.errorMessages.length > 0) {\r\n      this.message.showErrorMSGs(this.valid.errorMessages);\r\n      return\r\n    }\r\n    if (this.isNew) {\r\n      this.createListFormItem()\r\n\r\n    } else {\r\n      this.saveListFormItem()\r\n    }\r\n  }\r\n\r\n  saveListFormItem() {\r\n    this._formItemService.apiFormItemSaveListFormItemPost$Json({\r\n      body: this.saveListFormItemReq\r\n    }).subscribe(res => {\r\n      if (res.StatusCode == 0) {\r\n        this.message.showSucessMSG(\"執行成功\");\r\n        // this.getListFormItem()\r\n        this.goBack()\r\n      }\r\n    })\r\n  }\r\n\r\n  creatListFormItem: CreateListFormItem\r\n\r\n  createListFormItem() {\r\n    this.creatListFormItem = {\r\n      CBuildCaseId: this.buildCaseId,\r\n      CFormItem: this.saveListFormItemReq || null,\r\n      CFormType: this.typeContentManagementLandowner.CFormType,\r\n    }\r\n    this._formItemService.apiFormItemCreateListFormItemPost$Json({\r\n      body: this.creatListFormItem\r\n    }).subscribe(res => {\r\n      if (res.StatusCode == 0) {\r\n        this.message.showSucessMSG(\"執行成功\");\r\n        // this.getListFormItem()\r\n        this.goBack()\r\n      }\r\n    })\r\n  }\r\n\r\n  createArrayObjectFromArray(a: string[], b: { CFormItemHouseholdId: number, CFormItemId: number, CHousehold: string, CIsSelect: boolean }[]): { [key: string]: boolean } {\r\n    const c: { [key: string]: boolean } = {};\r\n    for (const item of a) {\r\n      const matchingItem = b.find(bItem => bItem.CHousehold === item && bItem.CIsSelect);\r\n      c[item] = !!matchingItem;\r\n    }\r\n    return c;\r\n  } //[\"House1\", \"House2\", \"House3\"] => [{CHousehold: \"House1\", CIsSelect: true,... }, ... ]\r\n\r\n\r\n  houseHoldList: any[]\r\n\r\n  getListRegularNoticeFileHouseHold() {\r\n    this._regularNoticeFileService.apiRegularNoticeFileGetListRegularNoticeFileHouseHoldPost$Json({\r\n      body: this.buildCaseId\r\n    }).pipe(\r\n      tap(res => {\r\n        if (res.Entries && res.StatusCode == 0) {\r\n          this.houseHoldList = this.getHouseHoldListByNoticeType(res.Entries)\r\n          this.getListFormItem()\r\n        }\r\n      })\r\n    ).subscribe()\r\n  }\r\n  goBack() { this.location.back() }\r\n}\r\n", "<!-- 3.7.1  CNoticeType = 1 -->\r\n<nb-card accent=\"success\">\r\n  <nb-card-header>\r\n    <ngx-breadcrumb></ngx-breadcrumb>\r\n  </nb-card-header>\r\n  <nb-card-body>\r\n    <h1 class=\"font-bold text-[#818181]\"></h1>\r\n    <div class=\"d-flex flex-wrap\">\r\n      <div class=\"col-md-12\">\r\n        <h4 class=\"font-bold text-lg px-3 pb-3\">類型-獨立選樣</h4>\r\n      </div>\r\n      <ng-container *ngFor=\"let formItemReq of arrListFormItemReq; let idx = index\">\r\n        <div class=\"col-md-9\">\r\n          <div class=\"form-group d-flex align-items-center w-full\">\r\n            <label for=\"CItemName\" class=\"label col-4 text-base\">\r\n              {{formItemReq.CName}}-{{formItemReq.CPart}}-{{formItemReq.CLocation}}\r\n            </label>\r\n            <div class=\"input-group items-center w-full col-8 px-0\">\r\n              <input type=\"text\" class=\"w-full col-12\" nbInput [(ngModel)]=\"formItemReq.CItemName\"\r\n                [placeholder]=\"formItemReq.CItemName\" [disabled]=\"listFormItem.CIsLock\" />\r\n            </div>\r\n          </div>\r\n        </div>\r\n        <div class=\"col-md-3\">\r\n          <div class=\"d-flex justify-content-end w-full\">\r\n            <button class=\"btn btn-info\" (click)=\"inputFile.click()\">上傳概念設計圖</button>\r\n          </div>\r\n        </div>\r\n        <div class=\"col-md-12\" [ngClass]=\"formItemReq.listPictures.length ? '':'hidden'\">\r\n          <input #inputFile type=\"file\" class=\"hidden\" (change)=\"detectFiles($event, formItemReq)\"\r\n            accept=\"image/png, image/gif, image/jpeg\" *ngIf=\"!listFormItem.CIsLock\">\r\n          <div class=\"mt-3 w-full flex flex-col\">\r\n            <table class=\"table table-striped border\" style=\"background-color:#f3f3f3;\">\r\n              <tbody>\r\n                <tr *ngFor=\"let picture of formItemReq.listPictures; let i = index\">\r\n                  <td class=\"align-middle\">\r\n                    <label>文件名 </label> &nbsp;\r\n                    <input nbInput class=\"w-100 p-2 text-[13px]\" type=\"text\" [value]=\"picture.name\"\r\n                      (blur)=\"renameFile($event, i, formItemReq)\">\r\n                  </td>\r\n                  <td class=\"w-[100px] h-auto\">\r\n                    <img class=\"fit-size\" [src]=\"picture.data\">\r\n                  </td>\r\n                  <td class=\"text-center w-32 align-middle\">\r\n                    <button class=\"btn btn-outline-danger btn-sm m-1\"\r\n                      (click)=\"removeImage(picture.id, formItemReq)\">删除</button>\r\n                  </td>\r\n                </tr>\r\n              </tbody>\r\n            </table>\r\n          </div>\r\n        </div>\r\n        <div class=\"col-md-12 text-center\" *ngIf=\"formItemReq.CDesignFileUrl\"\r\n          [ngClass]=\"formItemReq.listPictures.length ? 'hidden':''\">\r\n          <div class=\"mt-3 w-full flex flex-col\">\r\n            <table class=\"table table-striped border\">\r\n              <tbody>\r\n                <tr>\r\n                  <td class=\"align-middle\">\r\n                  </td>\r\n                  <td class=\"w-[80px] h-auto\">\r\n                    <img *ngIf=\"formItemReq.CDesignFileUrl\" class=\"w-14 h-14\"\r\n                      [src]=\"formItemReq.CDesignFileUrl | addBaseFile\">\r\n                  </td>\r\n                  <td class=\"text-center w-32 align-middle\">\r\n                  </td>\r\n                </tr>\r\n              </tbody>\r\n            </table>\r\n          </div>\r\n        </div>\r\n        <div class=\"col-md-9\">\r\n          <div class=\"form-group d-flex align-items-center w-full\">\r\n            <label for=\"cRequireAnswer\" class=\"label col-4\">必填數量</label>\r\n            <input type=\"number\" class=\"col-8\" nbInput placeholder=\"必填數量\" [(ngModel)]=\"formItemReq.CRequireAnswer \"\r\n              [disabled]=\"formItemReq.selectedCUiType.value===3\" />\r\n          </div>\r\n        </div>\r\n        <div class=\"col-md-3\">\r\n\r\n        </div>\r\n\r\n        <div class=\"col-md-9\">\r\n          <div class=\"form-group d-flex align-items-center w-full\">\r\n            <label for=\"buildingName\" class=\"label col-4\">前台UI類型</label>\r\n            <nb-select placeholder=\"建案\" [(ngModel)]=\"formItemReq.selectedCUiType\"\r\n              (selectedChange)=\"changeSelectCUiType(formItemReq)\" class=\"col-8 px-0\">\r\n              <nb-option *ngFor=\"let case of CUiTypeOptions\" [value]=\"case\">\r\n                {{ case.label }}\r\n              </nb-option>\r\n            </nb-select>\r\n          </div>\r\n        </div>\r\n        <div class=\"col-md-3\"></div>\r\n        <div class=\"col-md-12\">\r\n          <div class=\"form-group d-flex align-items-center w-full\">\r\n            <label for=\"buildingName\" class=\"label col-3\">適用戶別 </label>\r\n            <label class=\"mr-2\" *ngIf=\"houseHoldList.length\">\r\n              <nb-checkbox [(checked)]=\"formItemReq.allSelected\"\r\n                (checkedChange)=\"onCheckAllChange($event, formItemReq)\">\r\n                全選\r\n              </nb-checkbox>\r\n            </label>\r\n            <label *ngFor=\"let item of houseHoldList\" class=\"mr-2\">\r\n              <nb-checkbox [(checked)]=\"formItemReq.selectedItems[item]\" value=\"item\"\r\n                (checkedChange)=\"onCheckboxHouseHoldListChange($event, item, formItemReq)\">\r\n                {{ item }}\r\n              </nb-checkbox>\r\n            </label>\r\n          </div>\r\n        </div>\r\n        <div class=\"col-md-12\" *ngIf=\"formItemReq.selectedCUiType.value===3\">\r\n          <div class=\"form-group d-flex align-items-center w-full\">\r\n            <label for=\"buildingName\" class=\"label col-3\">備註選項</label>\r\n            <label *ngFor=\"let remark of CRemarkTypeOptions\" class=\"mr-2\">\r\n              <nb-checkbox *ngIf=\"formItemReq.selectedRemarkType\" [(checked)]=\"formItemReq.selectedRemarkType[remark]\"\r\n                value=\"item\" (checkedChange)=\"onCheckboxRemarkChange($event, remark, formItemReq)\">\r\n                {{ remark }}\r\n              </nb-checkbox>\r\n            </label>\r\n          </div>\r\n        </div>\r\n      </ng-container>\r\n    </div>\r\n  </nb-card-body>\r\n  <nb-card-footer class=\"d-flex justify-content-center\">\r\n    <button class=\"btn btn-secondary mx-2\" (click)=\"goBack()\">\r\n      取消\r\n    </button>\r\n    <button class=\"btn btn-info\" (click)=\"onSubmit()\">\r\n      儲存\r\n    </button>\r\n  </nb-card-footer>\r\n</nb-card>"], "mappings": "AACA,SAASA,YAAY,QAAQ,mCAAmC;AAChE,SAASC,YAAY,QAAkB,iBAAiB;AACxD,SAASC,gBAAgB,QAAQ,gBAAgB;AACjD,SAASC,aAAa,QAAQ,wCAAwC;AAMtE,SAASC,GAAG,QAAQ,MAAM;;;;;;;;;;;;;;;;ICmBhBC,EAAA,CAAAC,cAAA,mBAC0E;IAD7BD,EAAA,CAAAE,UAAA,oBAAAC,mGAAAC,MAAA;MAAAJ,EAAA,CAAAK,aAAA,CAAAC,GAAA;MAAA,MAAAC,cAAA,GAAAP,EAAA,CAAAQ,aAAA,GAAAC,SAAA;MAAA,MAAAC,MAAA,GAAAV,EAAA,CAAAQ,aAAA;MAAA,OAAAR,EAAA,CAAAW,WAAA,CAAUD,MAAA,CAAAE,WAAA,CAAAR,MAAA,EAAAG,cAAA,CAAgC;IAAA,EAAC;IAAxFP,EAAA,CAAAa,YAAA,EAC0E;;;;;;IAMhEb,EAFJ,CAAAC,cAAA,SAAoE,aACzC,YAChB;IAAAD,EAAA,CAAAc,MAAA,0BAAI;IAAAd,EAAA,CAAAa,YAAA,EAAQ;IAACb,EAAA,CAAAc,MAAA,eACpB;IAAAd,EAAA,CAAAC,cAAA,gBAC8C;IAA5CD,EAAA,CAAAE,UAAA,kBAAAa,8FAAAX,MAAA;MAAA,MAAAY,IAAA,GAAAhB,EAAA,CAAAK,aAAA,CAAAY,GAAA,EAAAC,KAAA;MAAA,MAAAX,cAAA,GAAAP,EAAA,CAAAQ,aAAA,GAAAC,SAAA;MAAA,MAAAC,MAAA,GAAAV,EAAA,CAAAQ,aAAA;MAAA,OAAAR,EAAA,CAAAW,WAAA,CAAQD,MAAA,CAAAS,UAAA,CAAAf,MAAA,EAAAY,IAAA,EAAAT,cAAA,CAAkC;IAAA,EAAC;IAC/CP,EAFE,CAAAa,YAAA,EAC8C,EAC3C;IACLb,EAAA,CAAAC,cAAA,aAA6B;IAC3BD,EAAA,CAAAoB,SAAA,cAA2C;IAC7CpB,EAAA,CAAAa,YAAA,EAAK;IAEHb,EADF,CAAAC,cAAA,aAA0C,iBAES;IAA/CD,EAAA,CAAAE,UAAA,mBAAAmB,gGAAA;MAAA,MAAAC,UAAA,GAAAtB,EAAA,CAAAK,aAAA,CAAAY,GAAA,EAAAR,SAAA;MAAA,MAAAF,cAAA,GAAAP,EAAA,CAAAQ,aAAA,GAAAC,SAAA;MAAA,MAAAC,MAAA,GAAAV,EAAA,CAAAQ,aAAA;MAAA,OAAAR,EAAA,CAAAW,WAAA,CAASD,MAAA,CAAAa,WAAA,CAAAD,UAAA,CAAAE,EAAA,EAAAjB,cAAA,CAAoC;IAAA,EAAC;IAACP,EAAA,CAAAc,MAAA,oBAAE;IAEvDd,EAFuD,CAAAa,YAAA,EAAS,EACzD,EACF;;;;IAVwDb,EAAA,CAAAyB,SAAA,GAAsB;IAAtBzB,EAAA,CAAA0B,UAAA,UAAAJ,UAAA,CAAAK,IAAA,CAAsB;IAIzD3B,EAAA,CAAAyB,SAAA,GAAoB;IAApBzB,EAAA,CAAA0B,UAAA,QAAAJ,UAAA,CAAAM,IAAA,EAAA5B,EAAA,CAAA6B,aAAA,CAAoB;;;;;IAoB1C7B,EAAA,CAAAoB,SAAA,cACmD;;;;;IAAjDpB,EAAA,CAAA0B,UAAA,QAAA1B,EAAA,CAAA8B,WAAA,OAAAvB,cAAA,CAAAwB,cAAA,GAAA/B,EAAA,CAAA6B,aAAA,CAAgD;;;;;IALtD7B,EALR,CAAAC,cAAA,cAC4D,cACnB,gBACK,YACjC,SACD;IACFD,EAAA,CAAAoB,SAAA,aACK;IACLpB,EAAA,CAAAC,cAAA,aAA4B;IAC1BD,EAAA,CAAAgC,UAAA,IAAAC,8EAAA,kBACmD;IACrDjC,EAAA,CAAAa,YAAA,EAAK;IACLb,EAAA,CAAAoB,SAAA,aACK;IAKfpB,EAJQ,CAAAa,YAAA,EAAK,EACC,EACF,EACJ,EACF;;;;IAjBJb,EAAA,CAAA0B,UAAA,YAAAnB,cAAA,CAAA2B,YAAA,CAAAC,MAAA,iBAAyD;IAQzCnC,EAAA,CAAAyB,SAAA,GAAgC;IAAhCzB,EAAA,CAAA0B,UAAA,SAAAnB,cAAA,CAAAwB,cAAA,CAAgC;;;;;IA0B5C/B,EAAA,CAAAC,cAAA,oBAA8D;IAC5DD,EAAA,CAAAc,MAAA,GACF;IAAAd,EAAA,CAAAa,YAAA,EAAY;;;;IAFmCb,EAAA,CAAA0B,UAAA,UAAAU,OAAA,CAAc;IAC3DpC,EAAA,CAAAyB,SAAA,EACF;IADEzB,EAAA,CAAAqC,kBAAA,MAAAD,OAAA,CAAAE,KAAA,MACF;;;;;;IASAtC,EADF,CAAAC,cAAA,gBAAiD,sBAEW;IAD7CD,EAAA,CAAAuC,gBAAA,2BAAAC,gHAAApC,MAAA;MAAAJ,EAAA,CAAAK,aAAA,CAAAoC,GAAA;MAAA,MAAAlC,cAAA,GAAAP,EAAA,CAAAQ,aAAA,GAAAC,SAAA;MAAAT,EAAA,CAAA0C,kBAAA,CAAAnC,cAAA,CAAAoC,WAAA,EAAAvC,MAAA,MAAAG,cAAA,CAAAoC,WAAA,GAAAvC,MAAA;MAAA,OAAAJ,EAAA,CAAAW,WAAA,CAAAP,MAAA;IAAA,EAAqC;IAChDJ,EAAA,CAAAE,UAAA,2BAAAsC,gHAAApC,MAAA;MAAAJ,EAAA,CAAAK,aAAA,CAAAoC,GAAA;MAAA,MAAAlC,cAAA,GAAAP,EAAA,CAAAQ,aAAA,GAAAC,SAAA;MAAA,MAAAC,MAAA,GAAAV,EAAA,CAAAQ,aAAA;MAAA,OAAAR,EAAA,CAAAW,WAAA,CAAiBD,MAAA,CAAAkC,gBAAA,CAAAxC,MAAA,EAAAG,cAAA,CAAqC;IAAA,EAAC;IACvDP,EAAA,CAAAc,MAAA,qBACF;IACFd,EADE,CAAAa,YAAA,EAAc,EACR;;;;IAJOb,EAAA,CAAAyB,SAAA,EAAqC;IAArCzB,EAAA,CAAA6C,gBAAA,YAAAtC,cAAA,CAAAoC,WAAA,CAAqC;;;;;;IAMlD3C,EADF,CAAAC,cAAA,gBAAuD,sBAEwB;IADhED,EAAA,CAAAuC,gBAAA,2BAAAO,gHAAA1C,MAAA;MAAA,MAAA2C,QAAA,GAAA/C,EAAA,CAAAK,aAAA,CAAA2C,IAAA,EAAAvC,SAAA;MAAA,MAAAF,cAAA,GAAAP,EAAA,CAAAQ,aAAA,GAAAC,SAAA;MAAAT,EAAA,CAAA0C,kBAAA,CAAAnC,cAAA,CAAA0C,aAAA,CAAAF,QAAA,GAAA3C,MAAA,MAAAG,cAAA,CAAA0C,aAAA,CAAAF,QAAA,IAAA3C,MAAA;MAAA,OAAAJ,EAAA,CAAAW,WAAA,CAAAP,MAAA;IAAA,EAA6C;IACxDJ,EAAA,CAAAE,UAAA,2BAAA4C,gHAAA1C,MAAA;MAAA,MAAA2C,QAAA,GAAA/C,EAAA,CAAAK,aAAA,CAAA2C,IAAA,EAAAvC,SAAA;MAAA,MAAAF,cAAA,GAAAP,EAAA,CAAAQ,aAAA,GAAAC,SAAA;MAAA,MAAAC,MAAA,GAAAV,EAAA,CAAAQ,aAAA;MAAA,OAAAR,EAAA,CAAAW,WAAA,CAAiBD,MAAA,CAAAwC,6BAAA,CAAA9C,MAAA,EAAA2C,QAAA,EAAAxC,cAAA,CAAwD;IAAA,EAAC;IAC1EP,EAAA,CAAAc,MAAA,GACF;IACFd,EADE,CAAAa,YAAA,EAAc,EACR;;;;;IAJOb,EAAA,CAAAyB,SAAA,EAA6C;IAA7CzB,EAAA,CAAA6C,gBAAA,YAAAtC,cAAA,CAAA0C,aAAA,CAAAF,QAAA,EAA6C;IAExD/C,EAAA,CAAAyB,SAAA,EACF;IADEzB,EAAA,CAAAqC,kBAAA,MAAAU,QAAA,MACF;;;;;;IAQA/C,EAAA,CAAAC,cAAA,sBACqF;IADjCD,EAAA,CAAAuC,gBAAA,2BAAAY,oIAAA/C,MAAA;MAAAJ,EAAA,CAAAK,aAAA,CAAA+C,IAAA;MAAA,MAAAC,UAAA,GAAArD,EAAA,CAAAQ,aAAA,GAAAC,SAAA;MAAA,MAAAF,cAAA,GAAAP,EAAA,CAAAQ,aAAA,IAAAC,SAAA;MAAAT,EAAA,CAAA0C,kBAAA,CAAAnC,cAAA,CAAA+C,kBAAA,CAAAD,UAAA,GAAAjD,MAAA,MAAAG,cAAA,CAAA+C,kBAAA,CAAAD,UAAA,IAAAjD,MAAA;MAAA,OAAAJ,EAAA,CAAAW,WAAA,CAAAP,MAAA;IAAA,EAAoD;IACzFJ,EAAA,CAAAE,UAAA,2BAAAiD,oIAAA/C,MAAA;MAAAJ,EAAA,CAAAK,aAAA,CAAA+C,IAAA;MAAA,MAAAC,UAAA,GAAArD,EAAA,CAAAQ,aAAA,GAAAC,SAAA;MAAA,MAAAF,cAAA,GAAAP,EAAA,CAAAQ,aAAA,IAAAC,SAAA;MAAA,MAAAC,MAAA,GAAAV,EAAA,CAAAQ,aAAA;MAAA,OAAAR,EAAA,CAAAW,WAAA,CAAiBD,MAAA,CAAA6C,sBAAA,CAAAnD,MAAA,EAAAiD,UAAA,EAAA9C,cAAA,CAAmD;IAAA,EAAC;IAClFP,EAAA,CAAAc,MAAA,GACF;IAAAd,EAAA,CAAAa,YAAA,EAAc;;;;;IAHsCb,EAAA,CAAA6C,gBAAA,YAAAtC,cAAA,CAAA+C,kBAAA,CAAAD,UAAA,EAAoD;IAEtGrD,EAAA,CAAAyB,SAAA,EACF;IADEzB,EAAA,CAAAqC,kBAAA,MAAAgB,UAAA,MACF;;;;;IAJFrD,EAAA,CAAAC,cAAA,gBAA8D;IAC5DD,EAAA,CAAAgC,UAAA,IAAAwB,8FAAA,0BACqF;IAGvFxD,EAAA,CAAAa,YAAA,EAAQ;;;;IAJQb,EAAA,CAAAyB,SAAA,EAAoC;IAApCzB,EAAA,CAAA0B,UAAA,SAAAnB,cAAA,CAAA+C,kBAAA,CAAoC;;;;;IAFpDtD,EAFJ,CAAAC,cAAA,aAAqE,cACV,gBACT;IAAAD,EAAA,CAAAc,MAAA,+BAAI;IAAAd,EAAA,CAAAa,YAAA,EAAQ;IAC1Db,EAAA,CAAAgC,UAAA,IAAAyB,gFAAA,oBAA8D;IAOlEzD,EADE,CAAAa,YAAA,EAAM,EACF;;;;IAPwBb,EAAA,CAAAyB,SAAA,GAAqB;IAArBzB,EAAA,CAAA0B,UAAA,YAAAhB,MAAA,CAAAgD,kBAAA,CAAqB;;;;;;IAvGrD1D,EAAA,CAAA2D,uBAAA,GAA8E;IAGxE3D,EAFJ,CAAAC,cAAA,cAAsB,cACqC,gBACF;IACnDD,EAAA,CAAAc,MAAA,GACF;IAAAd,EAAA,CAAAa,YAAA,EAAQ;IAENb,EADF,CAAAC,cAAA,cAAwD,gBAEsB;IAD3BD,EAAA,CAAAuC,gBAAA,2BAAAqB,iGAAAxD,MAAA;MAAA,MAAAG,cAAA,GAAAP,EAAA,CAAAK,aAAA,CAAAwD,GAAA,EAAApD,SAAA;MAAAT,EAAA,CAAA0C,kBAAA,CAAAnC,cAAA,CAAAuD,SAAA,EAAA1D,MAAA,MAAAG,cAAA,CAAAuD,SAAA,GAAA1D,MAAA;MAAA,OAAAJ,EAAA,CAAAW,WAAA,CAAAP,MAAA;IAAA,EAAmC;IAI1FJ,EAJM,CAAAa,YAAA,EAC4E,EACxE,EACF,EACF;IAGFb,EAFJ,CAAAC,cAAA,cAAsB,cAC2B,gBACY;IAA5BD,EAAA,CAAAE,UAAA,mBAAA6D,0FAAA;MAAA/D,EAAA,CAAAK,aAAA,CAAAwD,GAAA;MAAA,MAAAnD,MAAA,GAAAV,EAAA,CAAAQ,aAAA;MAAA,OAAAR,EAAA,CAAAW,WAAA,CAASD,MAAA,CAAAsD,SAAA,CAAAC,KAAA,EAAiB;IAAA,EAAC;IAACjE,EAAA,CAAAc,MAAA,kDAAO;IAEpEd,EAFoE,CAAAa,YAAA,EAAS,EACrE,EACF;IACNb,EAAA,CAAAC,cAAA,eAAiF;IAC/ED,EAAA,CAAAgC,UAAA,KAAAkC,0EAAA,oBAC0E;IAGtElE,EAFJ,CAAAC,cAAA,eAAuC,iBACuC,aACnE;IACLD,EAAA,CAAAgC,UAAA,KAAAmC,uEAAA,iBAAoE;IAiB5EnE,EAHM,CAAAa,YAAA,EAAQ,EACF,EACJ,EACF;IACNb,EAAA,CAAAgC,UAAA,KAAAoC,wEAAA,kBAC4D;IAoBxDpE,EAFJ,CAAAC,cAAA,eAAsB,eACqC,iBACP;IAAAD,EAAA,CAAAc,MAAA,gCAAI;IAAAd,EAAA,CAAAa,YAAA,EAAQ;IAC5Db,EAAA,CAAAC,cAAA,iBACuD;IADOD,EAAA,CAAAuC,gBAAA,2BAAA8B,kGAAAjE,MAAA;MAAA,MAAAG,cAAA,GAAAP,EAAA,CAAAK,aAAA,CAAAwD,GAAA,EAAApD,SAAA;MAAAT,EAAA,CAAA0C,kBAAA,CAAAnC,cAAA,CAAA+D,cAAA,EAAAlE,MAAA,MAAAG,cAAA,CAAA+D,cAAA,GAAAlE,MAAA;MAAA,OAAAJ,EAAA,CAAAW,WAAA,CAAAP,MAAA;IAAA,EAAyC;IAG3GJ,EAHI,CAAAa,YAAA,EACuD,EACnD,EACF;IACNb,EAAA,CAAAoB,SAAA,eAEM;IAIFpB,EAFJ,CAAAC,cAAA,eAAsB,eACqC,iBACT;IAAAD,EAAA,CAAAc,MAAA,kCAAM;IAAAd,EAAA,CAAAa,YAAA,EAAQ;IAC5Db,EAAA,CAAAC,cAAA,qBACyE;IAD7CD,EAAA,CAAAuC,gBAAA,2BAAAgC,sGAAAnE,MAAA;MAAA,MAAAG,cAAA,GAAAP,EAAA,CAAAK,aAAA,CAAAwD,GAAA,EAAApD,SAAA;MAAAT,EAAA,CAAA0C,kBAAA,CAAAnC,cAAA,CAAAiE,eAAA,EAAApE,MAAA,MAAAG,cAAA,CAAAiE,eAAA,GAAApE,MAAA;MAAA,OAAAJ,EAAA,CAAAW,WAAA,CAAAP,MAAA;IAAA,EAAyC;IACnEJ,EAAA,CAAAE,UAAA,4BAAAuE,uGAAA;MAAA,MAAAlE,cAAA,GAAAP,EAAA,CAAAK,aAAA,CAAAwD,GAAA,EAAApD,SAAA;MAAA,MAAAC,MAAA,GAAAV,EAAA,CAAAQ,aAAA;MAAA,OAAAR,EAAA,CAAAW,WAAA,CAAkBD,MAAA,CAAAgE,mBAAA,CAAAnE,cAAA,CAAgC;IAAA,EAAC;IACnDP,EAAA,CAAAgC,UAAA,KAAA2C,8EAAA,wBAA8D;IAKpE3E,EAFI,CAAAa,YAAA,EAAY,EACR,EACF;IACNb,EAAA,CAAAoB,SAAA,eAA4B;IAGxBpB,EAFJ,CAAAC,cAAA,cAAuB,eACoC,iBACT;IAAAD,EAAA,CAAAc,MAAA,iCAAK;IAAAd,EAAA,CAAAa,YAAA,EAAQ;IAO3Db,EANA,CAAAgC,UAAA,KAAA4C,0EAAA,oBAAiD,KAAAC,0EAAA,oBAMM;IAO3D7E,EADE,CAAAa,YAAA,EAAM,EACF;IACNb,EAAA,CAAAgC,UAAA,KAAA8C,wEAAA,kBAAqE;;;;;;IAhG/D9E,EAAA,CAAAyB,SAAA,GACF;IADEzB,EAAA,CAAA+E,kBAAA,MAAAxE,cAAA,CAAAyE,KAAA,OAAAzE,cAAA,CAAA0E,KAAA,OAAA1E,cAAA,CAAA2E,SAAA,MACF;IAEmDlF,EAAA,CAAAyB,SAAA,GAAmC;IAAnCzB,EAAA,CAAA6C,gBAAA,YAAAtC,cAAA,CAAAuD,SAAA,CAAmC;IAC5C9D,EAAtC,CAAA0B,UAAA,gBAAAnB,cAAA,CAAAuD,SAAA,CAAqC,aAAApD,MAAA,CAAAyE,YAAA,CAAAC,OAAA,CAAkC;IASxDpF,EAAA,CAAAyB,SAAA,GAAyD;IAAzDzB,EAAA,CAAA0B,UAAA,YAAAnB,cAAA,CAAA2B,YAAA,CAAAC,MAAA,iBAAyD;IAEjCnC,EAAA,CAAAyB,SAAA,EAA2B;IAA3BzB,EAAA,CAAA0B,UAAA,UAAAhB,MAAA,CAAAyE,YAAA,CAAAC,OAAA,CAA2B;IAI1CpF,EAAA,CAAAyB,SAAA,GAA6B;IAA7BzB,EAAA,CAAA0B,UAAA,YAAAnB,cAAA,CAAA2B,YAAA,CAA6B;IAkBzBlC,EAAA,CAAAyB,SAAA,EAAgC;IAAhCzB,EAAA,CAAA0B,UAAA,SAAAnB,cAAA,CAAAwB,cAAA,CAAgC;IAsBF/B,EAAA,CAAAyB,SAAA,GAAyC;IAAzCzB,EAAA,CAAA6C,gBAAA,YAAAtC,cAAA,CAAA+D,cAAA,CAAyC;IACrGtE,EAAA,CAAA0B,UAAA,aAAAnB,cAAA,CAAAiE,eAAA,CAAAa,KAAA,OAAkD;IAUxBrF,EAAA,CAAAyB,SAAA,GAAyC;IAAzCzB,EAAA,CAAA6C,gBAAA,YAAAtC,cAAA,CAAAiE,eAAA,CAAyC;IAEvCxE,EAAA,CAAAyB,SAAA,EAAiB;IAAjBzB,EAAA,CAAA0B,UAAA,YAAAhB,MAAA,CAAA4E,cAAA,CAAiB;IAU1BtF,EAAA,CAAAyB,SAAA,GAA0B;IAA1BzB,EAAA,CAAA0B,UAAA,SAAAhB,MAAA,CAAA6E,aAAA,CAAApD,MAAA,CAA0B;IAMvBnC,EAAA,CAAAyB,SAAA,EAAgB;IAAhBzB,EAAA,CAAA0B,UAAA,YAAAhB,MAAA,CAAA6E,aAAA,CAAgB;IAQpBvF,EAAA,CAAAyB,SAAA,EAA2C;IAA3CzB,EAAA,CAAA0B,UAAA,SAAAnB,cAAA,CAAAiE,eAAA,CAAAa,KAAA,OAA2C;;;ADjE3E,OAAM,MAAOG,yCAA0C,SAAQ1F,aAAa;EAC1E2F,YACUC,MAAmB,EACnBC,KAAqB,EACrBC,OAAuB,EACvBC,gBAAiC,EACjCC,yBAAmD,EACnDC,eAA+B,EAC/BC,KAAuB,EACvBC,QAAkB,EAClBC,gBAAiC;IAEzC,KAAK,CAACR,MAAM,CAAC;IAVL,KAAAA,MAAM,GAANA,MAAM;IACN,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,yBAAyB,GAAzBA,yBAAyB;IACzB,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,QAAQ,GAARA,QAAQ;IACR,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAK1B,KAAAC,8BAA8B,GAAG;MAC/BC,SAAS,EAAE,CAAC;MACZC,WAAW,EAAE;KACd;IACD,KAAAf,cAAc,GAAU,CACtB;MACED,KAAK,EAAE,CAAC;MAAE/C,KAAK,EAAE;KAClB,EACD;MACE+C,KAAK,EAAE,CAAC;MAAE/C,KAAK,EAAE;KAClB,EAAE;MACD+C,KAAK,EAAE,CAAC;MAAE/C,KAAK,EAAE;KAClB,CACF;IACD,KAAAoB,kBAAkB,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC;IA2BjC,KAAAT,aAAa,GAA+B,EAAE;IAC9C,KAAAK,kBAAkB,GAA+B,EAAE;IA2HnD,KAAAgD,KAAK,GAAY,IAAI;EAvKrB;EAmBSC,QAAQA,CAAA;IACf,IAAI,CAACZ,KAAK,CAACa,QAAQ,CAACC,SAAS,CAACC,MAAM,IAAG;MACrC,IAAIA,MAAM,EAAE;QACV,MAAMC,OAAO,GAAGD,MAAM,CAACE,GAAG,CAAC,IAAI,CAAC;QAChC,MAAMpF,EAAE,GAAGmF,OAAO,GAAG,CAACA,OAAO,GAAG,CAAC;QACjC,IAAI,CAACE,WAAW,GAAGrF,EAAE;QACrB,IAAI,IAAI,CAACqF,WAAW,EAAE;UACpB,IAAI,CAACC,iCAAiC,EAAE;QAC1C;MACF;IACF,CAAC,CAAC;EACJ;EAGAC,cAAcA,CAAC1B,KAAU,EAAE2B,OAAc;IACvC,KAAK,MAAMC,IAAI,IAAID,OAAO,EAAE;MAC1B,IAAIC,IAAI,CAAC5B,KAAK,KAAKA,KAAK,EAAE;QACxB,OAAO4B,IAAI;MACb;IACF;IACA,OAAO,IAAI;EACb;EAMArE,gBAAgBA,CAACsE,OAAgB,EAAEC,YAAiB;IAClDA,YAAY,CAACxE,WAAW,GAAGuE,OAAO;IAClC,IAAI,CAAC3B,aAAa,CAAC6B,OAAO,CAACH,IAAI,IAAG;MAChCE,YAAY,CAAClE,aAAa,CAACgE,IAAI,CAAC,GAAGC,OAAO;IAC5C,CAAC,CAAC;EACJ;EAGAtG,WAAWA,CAACyG,KAAU,EAAEF,YAAiB;IACvC,MAAMG,IAAI,GAAGD,KAAK,CAACE,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC;IAClC,IAAIF,IAAI,EAAE;MACR,IAAIG,MAAM,GAAG,IAAIC,UAAU,EAAE;MAC7BD,MAAM,CAACE,aAAa,CAACL,IAAI,CAAC;MAC1BG,MAAM,CAACG,MAAM,GAAG,MAAK;QACnB,IAAIC,SAAS,GAAWJ,MAAM,CAACK,MAAgB;QAC/C,IAAI,CAACD,SAAS,EAAE;UACd;QACF;QACA,IAAIV,YAAY,CAACjF,YAAY,CAACC,MAAM,GAAG,CAAC,EAAE;UACxCgF,YAAY,CAACjF,YAAY,CAAC,CAAC,CAAC,GAAG;YAC7BV,EAAE,EAAE,IAAIuG,IAAI,EAAE,CAACC,OAAO,EAAE;YACxBrG,IAAI,EAAE2F,IAAI,CAAC3F,IAAI,CAACsG,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;YAC7BrG,IAAI,EAAEiG,SAAS;YACfK,SAAS,EAAE,IAAI,CAACnC,eAAe,CAACoC,gBAAgB,CAACb,IAAI,CAAC3F,IAAI,CAAC;YAC3DyG,KAAK,EAAEd;WACR;QACH,CAAC,MAAM;UACLH,YAAY,CAACjF,YAAY,CAACmG,IAAI,CAAC;YAC7B7G,EAAE,EAAE,IAAIuG,IAAI,EAAE,CAACC,OAAO,EAAE;YACxBrG,IAAI,EAAE2F,IAAI,CAAC3F,IAAI,CAACsG,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;YAC7BrG,IAAI,EAAEiG,SAAS;YACfK,SAAS,EAAE,IAAI,CAACnC,eAAe,CAACoC,gBAAgB,CAACb,IAAI,CAAC3F,IAAI,CAAC;YAC3DyG,KAAK,EAAEd;WACR,CAAC;QACJ;QACAD,KAAK,CAACE,MAAM,CAAClC,KAAK,GAAG,IAAI;MAC3B,CAAC;IACH;EACF;EAEA9D,WAAWA,CAAC+G,SAAiB,EAAEnB,YAAiB;IAC9C,IAAIA,YAAY,CAACjF,YAAY,CAACC,MAAM,EAAE;MACpCgF,YAAY,CAACjF,YAAY,GAAGiF,YAAY,CAACjF,YAAY,CAACqG,MAAM,CAAEC,CAAM,IAAKA,CAAC,CAAChH,EAAE,IAAI8G,SAAS,CAAC;IAC7F;EACF;EAEAnH,UAAUA,CAACkG,KAAU,EAAEnG,KAAa,EAAEiG,YAAiB;IACrD,IAAIsB,IAAI,GAAGtB,YAAY,CAACjF,YAAY,CAAChB,KAAK,CAAC,CAACkH,KAAK,CAACM,KAAK,CAAC,CAAC,EAAEvB,YAAY,CAACjF,YAAY,CAAChB,KAAK,CAAC,CAACkH,KAAK,CAACO,IAAI,EAAExB,YAAY,CAACjF,YAAY,CAAChB,KAAK,CAAC,CAACkH,KAAK,CAACQ,IAAI,CAAC;IACpJ,IAAIC,OAAO,GAAG,IAAIC,IAAI,CAAC,CAACL,IAAI,CAAC,EAAE,GAAGpB,KAAK,CAACE,MAAM,CAAClC,KAAK,GAAG,GAAG,GAAG8B,YAAY,CAACjF,YAAY,CAAChB,KAAK,CAAC,CAACgH,SAAS,EAAE,EAAE;MAAEU,IAAI,EAAEzB,YAAY,CAACjF,YAAY,CAAChB,KAAK,CAAC,CAACkH,KAAK,CAACQ;IAAI,CAAE,CAAC;IACjKzB,YAAY,CAACjF,YAAY,CAAChB,KAAK,CAAC,CAACkH,KAAK,GAAGS,OAAO;EAClD;EAEA3F,6BAA6BA,CAACgE,OAAgB,EAAED,IAAY,EAAEE,YAAiB;IAC7E,IAAID,OAAO,EAAE;MACXC,YAAY,CAAClE,aAAa,CAACgE,IAAI,CAAC,GAAGC,OAAO;MAC1CC,YAAY,CAACxE,WAAW,GAAG,IAAI,CAAC4C,aAAa,CAACwD,KAAK,CAAC9B,IAAI,IAAIE,YAAY,CAAClE,aAAa,CAACgE,IAAI,CAAC,IAAIC,OAAO,CAAC;IAC1G,CAAC,MAAM;MACLC,YAAY,CAACxE,WAAW,GAAG,KAAK;IAClC;EACF;EAIAY,sBAAsBA,CAAC2D,OAAgB,EAAED,IAAY,EAAEE,YAAiB;IACtEA,YAAY,CAAC7D,kBAAkB,CAAC2D,IAAI,CAAC,GAAGC,OAAO;EACjD;EAEA8B,kBAAkBA,CAACtF,kBAA4B,EAAEuF,WAAmB;IAClE,MAAMC,YAAY,GAA+B,EAAE;IACnD,KAAK,MAAMC,MAAM,IAAIzF,kBAAkB,EAAE;MACvCwF,YAAY,CAACC,MAAM,CAAC,GAAG,KAAK;IAC9B;IACA,MAAMC,WAAW,GAAGH,WAAW,CAAChB,KAAK,CAAC,GAAG,CAAC;IAC1C,KAAK,MAAMW,IAAI,IAAIQ,WAAW,EAAE;MAC9B,IAAI1F,kBAAkB,CAAC2F,QAAQ,CAACT,IAAI,CAAC,EAAE;QACrCM,YAAY,CAACN,IAAI,CAAC,GAAG,IAAI;MAC3B;IACF;IACA,OAAOM,YAAY;EACrB;EAEAI,eAAeA,CAAA;IACb,IAAI,CAACpD,gBAAgB,CAACqD,mCAAmC,CAAC;MACxDC,IAAI,EAAE;QACJC,YAAY,EAAE,IAAI,CAAC5C,WAAW;QAC9B6C,KAAK,EAAE;;KAEV,CAAC,CAACC,IAAI,CACL5J,GAAG,CAAC6J,GAAG,IAAG;MACR,IAAIA,GAAG,CAACC,OAAO,IAAID,GAAG,CAACE,UAAU,IAAI,CAAC,EAAE;QAEtC,IAAI,CAACvE,aAAa,CAAC6B,OAAO,CAACH,IAAI,IAAI,IAAI,CAAChE,aAAa,CAACgE,IAAI,CAAC,GAAG,KAAK,CAAC;QAEpE,IAAI,CAACvD,kBAAkB,CAAC0D,OAAO,CAACH,IAAI,IAAI,IAAI,CAAC3D,kBAAkB,CAAC2D,IAAI,CAAC,GAAG,KAAK,CAAC;QAC9E,IAAI,CAAC8C,kBAAkB,GAAGH,GAAG,CAACC,OAAO,CAACG,GAAG,CAAEC,CAAM,IAAI;UACnD,OAAO;YACLlI,cAAc,EAAE,IAAI;YACpBmI,kBAAkB,EAAE,IAAI;YACxBC,OAAO,EAAE,IAAI;YACbjF,SAAS,EAAE+E,CAAC,CAAC/E,SAAS;YACtBF,KAAK,EAAEiF,CAAC,CAACjF,KAAK;YACdC,KAAK,EAAEgF,CAAC,CAAChF,KAAK;YACdnB,SAAS,EAAE,GAAGmG,CAAC,CAACjF,KAAK,IAAIiF,CAAC,CAAChF,KAAK,IAAIgF,CAAC,CAAC/E,SAAS,EAAE;YACjD+D,WAAW,EAAE,IAAI;YACjBmB,YAAY,EAAE,CAAC;YACf9F,cAAc,EAAE+F,SAAS;YACzBC,OAAO,EAAE,CAAC;YACVrH,aAAa,EAAE,EAAE;YACjBK,kBAAkB,EAAE,IAAI,CAACA,kBAAkB;YAC3CX,WAAW,EAAE,KAAK;YAClBT,YAAY,EAAE,EAAE;YAChBsC,eAAe,EAAE,IAAI,CAACc,cAAc,CAAC,CAAC;WACvC;QACH,CAAC,CAAC;QACF,IAAI,CAACyE,kBAAkB,GAAG,CAAC,GAAG,IAAI,CAACQ,UAAU,CAAC,IAAI,CAACR,kBAAkB,CAAC,CAAC;MACzE;IACF,CAAC,CAAC,CACH,CAACtD,SAAS,EAAE;EACf;EAKA+D,eAAeA,CAAA;IACb,IAAI,CAAC3E,gBAAgB,CAAC4E,mCAAmC,CAAC;MACxDjB,IAAI,EAAE;QACJC,YAAY,EAAE,IAAI,CAAC5C,WAAW;QAC9BT,SAAS,EAAE,IAAI,CAACD,8BAA8B,CAACC,SAAS;QACxDsE,SAAS,EAAE;;KAEd,CAAC,CAACf,IAAI,CACL5J,GAAG,CAAC6J,GAAG,IAAG;MACR,IAAIA,GAAG,CAACC,OAAO,IAAID,GAAG,CAACE,UAAU,IAAI,CAAC,EAAE;QACtC,IAAI,CAAC3E,YAAY,GAAGyE,GAAG,CAACC,OAAO;QAC/B,IAAI,CAACvD,KAAK,GAAGsD,GAAG,CAACC,OAAO,CAACc,SAAS,GAAG,KAAK,GAAG,IAAI;QACjD,IAAIf,GAAG,CAACC,OAAO,CAACc,SAAS,EAAE;UAEzB,IAAI,CAACpF,aAAa,CAAC6B,OAAO,CAACH,IAAI,IAAI,IAAI,CAAChE,aAAa,CAACgE,IAAI,CAAC,GAAG,KAAK,CAAC;UACpE,IAAI,CAACvD,kBAAkB,CAAC0D,OAAO,CAACH,IAAI,IAAI,IAAI,CAAC3D,kBAAkB,CAAC2D,IAAI,CAAC,GAAG,KAAK,CAAC;UAE9E,IAAI,CAAC8C,kBAAkB,GAAGH,GAAG,CAACC,OAAO,CAACc,SAAS,CAACX,GAAG,CAAEC,CAAM,IAAI;YAC7D,OAAO;cACLE,OAAO,EAAE,IAAI,CAAChF,YAAY,CAACgF,OAAO;cAClCpI,cAAc,EAAEkI,CAAC,CAAClI,cAAc;cAChCqG,KAAK,EAAE6B,CAAC,CAAC7B,KAAK;cACd8B,kBAAkB,EAAED,CAAC,CAACC,kBAAkB;cACxCU,WAAW,EAAEX,CAAC,CAACW,WAAW;cAC1B1F,SAAS,EAAE+E,CAAC,CAAC/E,SAAS;cACtBF,KAAK,EAAEiF,CAAC,CAACjF,KAAK;cACdC,KAAK,EAAEgF,CAAC,CAAChF,KAAK;cACdnB,SAAS,EAAEmG,CAAC,CAACnG,SAAS,GAAGmG,CAAC,CAACnG,SAAS,GAAG,GAAGmG,CAAC,CAACjF,KAAK,IAAIiF,CAAC,CAAChF,KAAK,IAAIgF,CAAC,CAAC/E,SAAS,EAAE;cAC7E+D,WAAW,EAAEgB,CAAC,CAAChB,WAAW;cAC1BmB,YAAY,EAAEH,CAAC,CAACG,YAAY;cAC5B9F,cAAc,EAAE2F,CAAC,CAACK,OAAO,KAAG,CAAC,GAAG,CAAC,GAAEL,CAAC,CAAC3F,cAAc;cACnDgG,OAAO,EAAEL,CAAC,CAACK,OAAO;cAClBrH,aAAa,EAAEgH,CAAC,CAACY,qBAAqB,CAAC1I,MAAM,GAAG,IAAI,CAAC2I,0BAA0B,CAAC,IAAI,CAACvF,aAAa,EAAE0E,CAAC,CAACY,qBAAqB,CAAC,GAAG;gBAAE,GAAG,IAAI,CAAC5H;cAAa,CAAE;cACxJK,kBAAkB,EAAE2G,CAAC,CAAChB,WAAW,GAAG,IAAI,CAACD,kBAAkB,CAAC,IAAI,CAACtF,kBAAkB,EAAEuG,CAAC,CAAChB,WAAW,CAAC,GAAG;gBAAE,GAAG,IAAI,CAAC3F;cAAkB,CAAE;cACpIX,WAAW,EAAEsH,CAAC,CAACY,qBAAqB,CAAC1I,MAAM,IAAI,IAAI,CAACoD,aAAa,CAACpD,MAAM;cACxED,YAAY,EAAE,EAAE;cAChBsC,eAAe,EAAEyF,CAAC,CAACK,OAAO,GAAG,IAAI,CAACvD,cAAc,CAACkD,CAAC,CAACK,OAAO,EAAE,IAAI,CAAChF,cAAc,CAAC,GAAG,IAAI,CAACA,cAAc,CAAC,CAAC;aACzG;UACH,CAAC,CAAC;QACJ,CAAC,MAAM;UACL,IAAI,CAACgE,eAAe,EAAE;QACxB;MACF;IACF,CAAC,CAAC,CACH,CAAC7C,SAAS,EAAE;EACf;EAEA/B,mBAAmBA,CAACqG,WAAgB;IAClC,IAAGA,WAAW,CAACvG,eAAe,IAAIuG,WAAW,CAACvG,eAAe,CAACa,KAAK,KAAI,CAAC,EAAE;MACxE0F,WAAW,CAACzG,cAAc,GAAG,CAAC;IAChC;EACF;EAEA0G,4BAA4BA,CAACpJ,IAAW;IACtC,KAAK,IAAIqF,IAAI,IAAIrF,IAAI,EAAE;MACrB,IAAIqF,IAAI,CAACZ,WAAW,KAAK,IAAI,CAACF,8BAA8B,CAACE,WAAW,EAAE;QACxE,OAAOY,IAAI,CAACgE,cAAc;MAC5B;IACF;IACA,OAAO,EAAE;EACX;EAIAC,oBAAoBA,CAACC,GAA4B;IAC/C,OAAOC,MAAM,CAACC,IAAI,CAACF,GAAG,CAAC,CAAC5C,MAAM,CAAC+C,GAAG,IAAIH,GAAG,CAACG,GAAG,CAAC,CAAC;EACjD;EAEAC,0BAA0BA,CAACJ,GAA4B;IACrD,OAAOC,MAAM,CAACC,IAAI,CAACF,GAAG,CAAC,CACpB5C,MAAM,CAAC+C,GAAG,IAAIH,GAAG,CAACG,GAAG,CAAC,CAAC,CACvBE,IAAI,CAAC,GAAG,CAAC;EACd;EAEAC,cAAcA,CAACjH,eAAoB,EAAElB,kBAAuB;IAC1D,IAAIkB,eAAe,IAAIA,eAAe,CAACa,KAAK,IAAI,CAAC,EAAE;MACjD,OAAO,IAAI,CAACkG,0BAA0B,CAACjI,kBAAkB,CAAC;IAC5D;EACF;EAEAoI,mBAAmBA,CAACC,WAAmB;IACrC,MAAMC,KAAK,GAAGD,WAAW,CAAC1D,KAAK,CAAC,GAAG,CAAC;IACpC,IAAI2D,KAAK,CAACzJ,MAAM,GAAG,CAAC,EAAE;MACpB,OAAOyJ,KAAK,CAAC,CAAC,CAAC;IACjB,CAAC,MAAM,OAAO,EAAE;EAClB;EAEAC,UAAUA,CAAC3J,YAAiB;IAC1B,IAAIA,YAAY,IAAIA,YAAY,CAACC,MAAM,GAAG,CAAC,EAAE;MAC3C,OAAO;QACL2J,YAAY,EAAE,IAAI,CAACJ,mBAAmB,CAACxJ,YAAY,CAAC,CAAC,CAAC,CAACN,IAAI,CAAC,IAAI,IAAI;QACpEmK,aAAa,EAAE7J,YAAY,CAAC,CAAC,CAAC,CAACgG,SAAS,IAAI,IAAI;QAChD8D,QAAQ,EAAE9J,YAAY,CAAC,CAAC,CAAC,CAACkG,KAAK,CAACzG,IAAI,IAAIO,YAAY,CAAC,CAAC,CAAC,CAACP,IAAI,IAAI;OACjE;IACH,CAAC,MAAM,OAAO0I,SAAS;EAEzB;EAEA;EACA;EACA;EACA;EACA;EACAE,UAAUA,CAAC0B,KAAoC;IAC7C,MAAMjC,GAAG,GAAG,IAAIkC,GAAG,EAAgE;IAEnFD,KAAK,CAAC7E,OAAO,CAACH,IAAI,IAAG;MACnB,MAAMqE,GAAG,GAAG,GAAGrE,IAAI,CAAC/B,SAAS,IAAI+B,IAAI,CAACjC,KAAK,IAAIiC,IAAI,CAAChC,KAAK,EAAE;MAC3D,IAAI+E,GAAG,CAACmC,GAAG,CAACb,GAAG,CAAC,EAAE;QAChB,MAAMc,QAAQ,GAAGpC,GAAG,CAACpD,GAAG,CAAC0E,GAAG,CAAE;QAC9Bc,QAAQ,CAACC,KAAK,IAAI,CAAC;MACrB,CAAC,MAAM;QACLrC,GAAG,CAACsC,GAAG,CAAChB,GAAG,EAAE;UAAErE,IAAI,EAAE;YAAE,GAAGA;UAAI,CAAE;UAAEoF,KAAK,EAAE;QAAC,CAAE,CAAC;MAC/C;IACF,CAAC,CAAC;IAEF,OAAOE,KAAK,CAACC,IAAI,CAACxC,GAAG,CAACyC,MAAM,EAAE,CAAC,CAACzC,GAAG,CAAC,CAAC;MAAE/C,IAAI;MAAEoF;IAAK,CAAE,MAAM;MACxD,GAAGpF,IAAI;MACPmD,YAAY,EAAEiC;KACf,CAAC,CAAC;EACL;EAGAK,UAAUA,CAAA;IACR,IAAI,CAAC1G,KAAK,CAAC2G,KAAK,EAAE;IAClB,IAAIC,iBAAiB,GAAG,KAAK;IAC7B,IAAIC,wBAAwB,GAAG,KAAK;IACpC,IAAIC,kBAAkB,GAAG,KAAK;IAC9B,KAAK,MAAM7F,IAAI,IAAI,IAAI,CAAC8F,mBAAmB,EAAE;MAC3C,IAAI,CAACH,iBAAiB,IAAK,CAAC3F,IAAI,CAACqD,OAAQ,EAAE;QACzCsC,iBAAiB,GAAG,IAAI;MAC1B;MACA,IAAI,CAACC,wBAAwB,IAAK,CAAC5F,IAAI,CAAC3C,cAAe,EAAE;QACvDuI,wBAAwB,GAAG,IAAI;MACjC;MAEA,IAAI5F,IAAI,CAACmD,YAAY,IAAInD,IAAI,CAAC3C,cAAc,EAAE;QAC5C,IAAI2C,IAAI,CAAC3C,cAAc,GAAG2C,IAAI,CAACmD,YAAY,EAAE;UAC3C,IAAI,CAACpE,KAAK,CAACgH,eAAe,CAAC,QAAQ,GAAG,MAAM,GAAG/F,IAAI,CAACmD,YAAY,GAAG,KAAKnD,IAAI,CAACnD,SAAS,IAAI,CAAC;QAC7F;MACF;MACA,IAAI,CAACgJ,kBAAkB,IAAK,CAAC7F,IAAI,CAACnD,SAAU,EAAE;QAC5CgJ,kBAAkB,GAAG,IAAI;MAC3B;IACF;IACA,IAAIF,iBAAiB,EAAE;MACrB,IAAI,CAAC5G,KAAK,CAACgH,eAAe,CAAC,UAAU,GAAG,KAAK,CAAC;IAChD;IACA,IAAIH,wBAAwB,EAAE;MAC5B,IAAI,CAAC7G,KAAK,CAACgH,eAAe,CAAC,QAAQ,GAAG,QAAQ,CAAC;IACjD;IACA,IAAIF,kBAAkB,EAAE;MACtB,IAAI,CAAC9G,KAAK,CAACgH,eAAe,CAAC,YAAY,GAAG,KAAK,CAAC;IAClD;EACF;EAIAC,QAAQA,CAAA;IACN,IAAI,CAACF,mBAAmB,GAAG,IAAI,CAAChD,kBAAkB,CAACC,GAAG,CAAEkD,CAAM,IAAI;MAChE,OAAO;QACLnL,cAAc,EAAEmL,CAAC,CAACnL,cAAc,GAAGmL,CAAC,CAACnL,cAAc,GAAG,IAAI;QAC1DqG,KAAK,EAAE8E,CAAC,CAAChL,YAAY,GAAG,IAAI,CAAC2J,UAAU,CAACqB,CAAC,CAAChL,YAAY,CAAC,GAAGmI,SAAS;QACnEH,kBAAkB,EAAE,IAAI,CAACgB,oBAAoB,CAACgC,CAAC,CAACjK,aAAa,CAAC;QAC9D2H,WAAW,EAAEsC,CAAC,CAACtC,WAAW,GAAGsC,CAAC,CAACtC,WAAW,GAAG,IAAI;QACjDuC,OAAO,EAAE,IAAI,CAAC7G,KAAK,GAAG,IAAI,GAAG,IAAI,CAACnB,YAAY,CAACgF,OAAO;QACtDnF,KAAK,EAAEkI,CAAC,CAAClI,KAAK;QACdC,KAAK,EAAEiI,CAAC,CAACjI,KAAK;QACdC,SAAS,EAAEgI,CAAC,CAAChI,SAAS;QACtBpB,SAAS,EAAEoJ,CAAC,CAACpJ,SAAS;QAAC;QACvBmF,WAAW,EAAEiE,CAAC,CAAC1I,eAAe,CAACa,KAAK,KAAK,CAAC,GAAG,IAAI,GAAG,IAAI,CAACoG,cAAc,CAACyB,CAAC,CAAC1I,eAAe,EAAE0I,CAAC,CAAC5J,kBAAkB,CAAC,IAAI,IAAI;QACxH8G,YAAY,EAAE8C,CAAC,CAAC9C,YAAY;QAC5B9F,cAAc,EAAE4I,CAAC,CAAC5I,cAAc;QAChCgG,OAAO,EAAE4C,CAAC,CAAC1I,eAAe,CAACa;OAC5B;IACH,CAAC,CAAC;IACF,IAAI,CAACqH,UAAU,EAAE;IACjB,IAAI,IAAI,CAAC1G,KAAK,CAACoH,aAAa,CAACjL,MAAM,GAAG,CAAC,EAAE;MACvC,IAAI,CAACyD,OAAO,CAACyH,aAAa,CAAC,IAAI,CAACrH,KAAK,CAACoH,aAAa,CAAC;MACpD;IACF;IACA,IAAI,IAAI,CAAC9G,KAAK,EAAE;MACd,IAAI,CAACgH,kBAAkB,EAAE;IAE3B,CAAC,MAAM;MACL,IAAI,CAACC,gBAAgB,EAAE;IACzB;EACF;EAEAA,gBAAgBA,CAAA;IACd,IAAI,CAAC1H,gBAAgB,CAAC2H,oCAAoC,CAAC;MACzDhE,IAAI,EAAE,IAAI,CAACuD;KACZ,CAAC,CAACtG,SAAS,CAACmD,GAAG,IAAG;MACjB,IAAIA,GAAG,CAACE,UAAU,IAAI,CAAC,EAAE;QACvB,IAAI,CAAClE,OAAO,CAAC6H,aAAa,CAAC,MAAM,CAAC;QAClC;QACA,IAAI,CAACC,MAAM,EAAE;MACf;IACF,CAAC,CAAC;EACJ;EAIAJ,kBAAkBA,CAAA;IAChB,IAAI,CAACK,iBAAiB,GAAG;MACvBlE,YAAY,EAAE,IAAI,CAAC5C,WAAW;MAC9B+G,SAAS,EAAE,IAAI,CAACb,mBAAmB,IAAI,IAAI;MAC3C3G,SAAS,EAAE,IAAI,CAACD,8BAA8B,CAACC;KAChD;IACD,IAAI,CAACP,gBAAgB,CAACgI,sCAAsC,CAAC;MAC3DrE,IAAI,EAAE,IAAI,CAACmE;KACZ,CAAC,CAAClH,SAAS,CAACmD,GAAG,IAAG;MACjB,IAAIA,GAAG,CAACE,UAAU,IAAI,CAAC,EAAE;QACvB,IAAI,CAAClE,OAAO,CAAC6H,aAAa,CAAC,MAAM,CAAC;QAClC;QACA,IAAI,CAACC,MAAM,EAAE;MACf;IACF,CAAC,CAAC;EACJ;EAEA5C,0BAA0BA,CAACgD,CAAW,EAAEC,CAAkG;IACxI,MAAMC,CAAC,GAA+B,EAAE;IACxC,KAAK,MAAM/G,IAAI,IAAI6G,CAAC,EAAE;MACpB,MAAMG,YAAY,GAAGF,CAAC,CAACG,IAAI,CAACC,KAAK,IAAIA,KAAK,CAACC,UAAU,KAAKnH,IAAI,IAAIkH,KAAK,CAACE,SAAS,CAAC;MAClFL,CAAC,CAAC/G,IAAI,CAAC,GAAG,CAAC,CAACgH,YAAY;IAC1B;IACA,OAAOD,CAAC;EACV,CAAC,CAAC;EAKFlH,iCAAiCA,CAAA;IAC/B,IAAI,CAAChB,yBAAyB,CAACwI,8DAA8D,CAAC;MAC5F9E,IAAI,EAAE,IAAI,CAAC3C;KACZ,CAAC,CAAC8C,IAAI,CACL5J,GAAG,CAAC6J,GAAG,IAAG;MACR,IAAIA,GAAG,CAACC,OAAO,IAAID,GAAG,CAACE,UAAU,IAAI,CAAC,EAAE;QACtC,IAAI,CAACvE,aAAa,GAAG,IAAI,CAACyF,4BAA4B,CAACpB,GAAG,CAACC,OAAO,CAAC;QACnE,IAAI,CAACW,eAAe,EAAE;MACxB;IACF,CAAC,CAAC,CACH,CAAC/D,SAAS,EAAE;EACf;EACAiH,MAAMA,CAAA;IAAK,IAAI,CAACzH,QAAQ,CAACsI,IAAI,EAAE;EAAC;;;uCA1arB/I,yCAAyC,EAAAxF,EAAA,CAAAwO,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAA1O,EAAA,CAAAwO,iBAAA,CAAAG,EAAA,CAAAC,cAAA,GAAA5O,EAAA,CAAAwO,iBAAA,CAAAK,EAAA,CAAAC,cAAA,GAAA9O,EAAA,CAAAwO,iBAAA,CAAAO,EAAA,CAAAC,eAAA,GAAAhP,EAAA,CAAAwO,iBAAA,CAAAO,EAAA,CAAAE,wBAAA,GAAAjP,EAAA,CAAAwO,iBAAA,CAAAU,EAAA,CAAAC,cAAA,GAAAnP,EAAA,CAAAwO,iBAAA,CAAAY,EAAA,CAAAC,gBAAA,GAAArP,EAAA,CAAAwO,iBAAA,CAAAc,EAAA,CAAAC,QAAA,GAAAvP,EAAA,CAAAwO,iBAAA,CAAAO,EAAA,CAAAS,eAAA;IAAA;EAAA;;;YAAzChK,yCAAyC;MAAAiK,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAA3P,EAAA,CAAA4P,0BAAA,EAAA5P,EAAA,CAAA6P,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,mDAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UC5CpDnQ,EADF,CAAAC,cAAA,iBAA0B,qBACR;UACdD,EAAA,CAAAoB,SAAA,qBAAiC;UACnCpB,EAAA,CAAAa,YAAA,EAAiB;UACjBb,EAAA,CAAAC,cAAA,mBAAc;UACZD,EAAA,CAAAoB,SAAA,YAA0C;UAGtCpB,EAFJ,CAAAC,cAAA,aAA8B,aACL,YACmB;UAAAD,EAAA,CAAAc,MAAA,4CAAO;UACjDd,EADiD,CAAAa,YAAA,EAAK,EAChD;UACNb,EAAA,CAAAgC,UAAA,IAAAqO,iEAAA,4BAA8E;UAiHlFrQ,EADE,CAAAa,YAAA,EAAM,EACO;UAEbb,EADF,CAAAC,cAAA,yBAAsD,iBACM;UAAnBD,EAAA,CAAAE,UAAA,mBAAAoQ,4EAAA;YAAA,OAASF,GAAA,CAAA1C,MAAA,EAAQ;UAAA,EAAC;UACvD1N,EAAA,CAAAc,MAAA,sBACF;UAAAd,EAAA,CAAAa,YAAA,EAAS;UACTb,EAAA,CAAAC,cAAA,iBAAkD;UAArBD,EAAA,CAAAE,UAAA,mBAAAqQ,4EAAA;YAAA,OAASH,GAAA,CAAAnD,QAAA,EAAU;UAAA,EAAC;UAC/CjN,EAAA,CAAAc,MAAA,sBACF;UAEJd,EAFI,CAAAa,YAAA,EAAS,EACM,EACT;;;UA1HkCb,EAAA,CAAAyB,SAAA,GAAuB;UAAvBzB,EAAA,CAAA0B,UAAA,YAAA0O,GAAA,CAAArG,kBAAA,CAAuB;;;qBD+BvDnK,YAAY,EAAA0P,EAAA,CAAAkB,OAAA,EAAAlB,EAAA,CAAAmB,OAAA,EAAAnB,EAAA,CAAAoB,IAAA,EAAE/Q,YAAY,EAAAgR,EAAA,CAAAC,oBAAA,EAAAD,EAAA,CAAAE,mBAAA,EAAAF,EAAA,CAAAG,eAAA,EAAAH,EAAA,CAAAI,OAAA,EAAAC,EAAA,CAAAC,eAAA,EAAAD,EAAA,CAAAE,mBAAA,EAAAF,EAAA,CAAAG,qBAAA,EAAAH,EAAA,CAAAI,qBAAA,EAAAJ,EAAA,CAAAK,mBAAA,EAAAL,EAAA,CAAAM,gBAAA,EAAAN,EAAA,CAAAO,iBAAA,EAAAP,EAAA,CAAAQ,iBAAA,EAAAC,GAAA,CAAAC,mBAAA,EAAAC,GAAA,CAAAC,YAAA,EAAE/R,gBAAgB;MAAAgS,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}