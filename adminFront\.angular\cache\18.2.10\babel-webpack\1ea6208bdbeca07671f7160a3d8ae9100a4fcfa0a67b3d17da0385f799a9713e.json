{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { tap } from 'rxjs/operators';\nimport { SharedModule } from '../../components/shared.module';\nimport { BaseComponent } from '../../components/base/baseComponent';\nimport { EEvent } from 'src/app/shared/services/event.service';\nimport { EnumHouseType } from 'src/app/shared/enum/enumHouseType';\n// 新增：導入戶別選擇器相關模組\nimport { SharedModule as AppSharedModule } from 'src/app/shared/shared.module';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/shared/helper/allowHelper\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"src/app/shared/services/message.service\";\nimport * as i4 from \"src/services/api/services\";\nimport * as i5 from \"src/app/shared/services/event.service\";\nimport * as i6 from \"@angular/common\";\nimport * as i7 from \"@angular/forms\";\nimport * as i8 from \"@nebular/theme\";\nimport * as i9 from \"@ng-bootstrap/ng-bootstrap\";\nimport * as i10 from \"../../components/breadcrumb/breadcrumb.component\";\nimport * as i11 from \"../../../shared/components/household-binding/household-binding.component\";\nfunction ContentManagementSalesAccountComponent_nb_option_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 26);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const case_r1 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", case_r1);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", case_r1.CBuildCaseName, \" \");\n  }\n}\nfunction ContentManagementSalesAccountComponent_nb_option_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 26);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const option_r2 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", option_r2.value);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", option_r2.label, \" \");\n  }\n}\nfunction ContentManagementSalesAccountComponent_div_28_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 27)(1, \"app-household-binding\", 28);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function ContentManagementSalesAccountComponent_div_28_Template_app_household_binding_ngModelChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r3 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r3.selectedHouseholds, $event) || (ctx_r3.selectedHouseholds = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"selectionChange\", function ContentManagementSalesAccountComponent_div_28_Template_app_household_binding_selectionChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.onHouseholdSelectionChange($event));\n    })(\"houseIdChange\", function ContentManagementSalesAccountComponent_div_28_Template_app_household_binding_houseIdChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.onHouseIdChange($event));\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"buildCaseId\", ctx_r3.cBuildCaseSelected == null ? null : ctx_r3.cBuildCaseSelected.cID)(\"placeholder\", \"\\u8ACB\\u9078\\u64C7\\u6236\\u5225\")(\"maxSelections\", null)(\"showSelectedArea\", true)(\"allowSearch\", true)(\"allowBatchSelect\", true);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r3.selectedHouseholds);\n  }\n}\nfunction ContentManagementSalesAccountComponent_ng_container_31_button_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 31);\n    i0.ɵɵlistener(\"click\", function ContentManagementSalesAccountComponent_ng_container_31_button_1_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r3.toggleSwitch(ctx_r3.listFormItem.CIsLock));\n    });\n    i0.ɵɵelement(1, \"i\", 32);\n    i0.ɵɵtext(2, \" \\u9396\\u5B9A \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ContentManagementSalesAccountComponent_ng_container_31_button_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 33);\n    i0.ɵɵlistener(\"click\", function ContentManagementSalesAccountComponent_ng_container_31_button_2_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r3.navidateDetai());\n    });\n    i0.ɵɵtext(1, \" \\u7DE8\\u8F2F\\u5167\\u5BB9 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ContentManagementSalesAccountComponent_ng_container_31_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, ContentManagementSalesAccountComponent_ng_container_31_button_1_Template, 3, 0, \"button\", 29)(2, ContentManagementSalesAccountComponent_ng_container_31_button_2_Template, 2, 0, \"button\", 30);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.isUpdate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.listFormItem.formItems && ctx_r3.isUpdate);\n  }\n}\nfunction ContentManagementSalesAccountComponent_ng_container_32_button_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 35);\n    i0.ɵɵlistener(\"click\", function ContentManagementSalesAccountComponent_ng_container_32_button_1_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r3.navidateDetai());\n    });\n    i0.ɵɵtext(1, \" \\u67E5\\u770B\\u5167\\u5BB9 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ContentManagementSalesAccountComponent_ng_container_32_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, ContentManagementSalesAccountComponent_ng_container_32_button_1_Template, 2, 0, \"button\", 34);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.listFormItem.formItems && ctx_r3.isUpdate);\n  }\n}\nfunction ContentManagementSalesAccountComponent_div_34_span_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 40);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const household_r8 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", household_r8, \" \");\n  }\n}\nfunction ContentManagementSalesAccountComponent_div_34_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 36)(1, \"h6\");\n    i0.ɵɵelement(2, \"i\", 37);\n    i0.ɵɵtext(3, \" \\u5DF2\\u9078\\u64C7\\u6236\\u5225\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 2);\n    i0.ɵɵtemplate(5, ContentManagementSalesAccountComponent_div_34_span_5_Template, 2, 1, \"span\", 38);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"small\", 39);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.selectedHouseholds);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\\u5171\\u9078\\u64C7 \", ctx_r3.selectedHouseholds.length, \" \\u6236\");\n  }\n}\nfunction ContentManagementSalesAccountComponent_tr_45_span_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const i_r9 = ctx.$implicit;\n    const ix_r10 = ctx.index;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate2(\" \", ix_r10 > 0 ? \"\\u3001\" : \"\", \" \", i_r9.CHousehold, \" \");\n  }\n}\nfunction ContentManagementSalesAccountComponent_tr_45_ng_container_6_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 44);\n    i0.ɵɵelement(1, \"i\", 45);\n    i0.ɵɵtext(2, \" \\u5339\\u914D \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ContentManagementSalesAccountComponent_tr_45_ng_container_6_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 46);\n    i0.ɵɵelement(1, \"i\", 47);\n    i0.ɵɵtext(2, \" \\u4E0D\\u5339\\u914D \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ContentManagementSalesAccountComponent_tr_45_ng_container_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, ContentManagementSalesAccountComponent_tr_45_ng_container_6_span_1_Template, 3, 0, \"span\", 42)(2, ContentManagementSalesAccountComponent_tr_45_ng_container_6_span_2_Template, 3, 0, \"span\", 43);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const item_r11 = i0.ɵɵnextContext().$implicit;\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.hasMatchingHouseholds(item_r11.tblFormItemHouseholds));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r3.hasMatchingHouseholds(item_r11.tblFormItemHouseholds));\n  }\n}\nfunction ContentManagementSalesAccountComponent_tr_45_span_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 39);\n    i0.ɵɵtext(1, \" \\u8ACB\\u9078\\u64C7\\u6236\\u5225 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ContentManagementSalesAccountComponent_tr_45_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\");\n    i0.ɵɵtemplate(4, ContentManagementSalesAccountComponent_tr_45_span_4_Template, 2, 2, \"span\", 23);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\");\n    i0.ɵɵtemplate(6, ContentManagementSalesAccountComponent_tr_45_ng_container_6_Template, 3, 2, \"ng-container\", 17)(7, ContentManagementSalesAccountComponent_tr_45_span_7_Template, 2, 0, \"span\", 41);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const item_r11 = ctx.$implicit;\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r11.CItemName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", item_r11.tblFormItemHouseholds);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.selectedHouseholds.length > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.selectedHouseholds.length === 0);\n  }\n}\nexport class ContentManagementSalesAccountComponent extends BaseComponent {\n  toggleSwitch(CIsLock) {\n    if (CIsLock) {\n      this.unLock();\n    } else {\n      this.onLock();\n    }\n  }\n  constructor(_allow, router, message, _buildCaseService, _formItemService, _eventService) {\n    super(_allow);\n    this._allow = _allow;\n    this.router = router;\n    this.message = message;\n    this._buildCaseService = _buildCaseService;\n    this._formItemService = _formItemService;\n    this._eventService = _eventService;\n    // 戶型選項\n    this.houseTypeOptions = [{\n      label: '地主戶',\n      value: EnumHouseType.地主戶\n    }, {\n      label: '銷售戶',\n      value: EnumHouseType.銷售戶\n    }];\n    // 選中的戶型\n    this.selectedHouseType = EnumHouseType.銷售戶;\n    // 新增：戶別選擇器相關屬性\n    this.selectedHouseholds = []; // 選中的戶別\n    this.selectedHouseholdItems = []; // 完整的戶別項目資訊\n    this.selectedHouseIds = []; // 選中的房屋ID\n    this.showHouseholdSelector = false; // 是否顯示戶別選擇器\n    this.tempBuildCaseID = -1;\n    this.buildingSelectedOptions = [{\n      value: '',\n      label: '全部'\n    }];\n    this.pageSize = 20;\n    this._eventService.receive().pipe(tap(res => {\n      if (res.action == \"GET_BUILDCASE\" /* EEvent.GET_BUILDCASE */ && !!res.payload) {\n        this.tempBuildCaseID = res.payload;\n      }\n    })).subscribe();\n  }\n  ngOnInit() {\n    this.cBuildCaseSelected = null;\n    this.getUserBuildCase();\n  }\n  getUserBuildCase() {\n    this._buildCaseService.apiBuildCaseGetUserBuildCasePost$Json({\n      body: {\n        CBuildCaseId: this.buildCaseId\n      }\n    }).pipe(tap(res => {\n      if (res.Entries && res.StatusCode == 0) {\n        this.userBuildCaseOptions = res.Entries.map(res => {\n          return {\n            CBuildCaseName: res.CBuildCaseName,\n            cID: res.cID\n          };\n        });\n        if (this.tempBuildCaseID != -1) {\n          let index = this.userBuildCaseOptions.findIndex(x => x.cID == this.tempBuildCaseID);\n          if (index >= 0) {\n            this.cBuildCaseSelected = this.userBuildCaseOptions[index];\n          } else {\n            this.cBuildCaseSelected = this.userBuildCaseOptions[0];\n          }\n        } else {\n          this.cBuildCaseSelected = this.userBuildCaseOptions[0];\n        }\n        if (this.cBuildCaseSelected && this.cBuildCaseSelected.cID && this.cBuildCaseSelected.cID > 0) {\n          this.getListFormItem();\n        }\n      }\n    })).subscribe();\n  }\n  // 移除硬編碼的類型配置，改為動態使用選中的戶型\n  // typeContentManagementSalesAccount = {\n  //   CFormType: 2,\n  // }\n  // 戶型選項變更處理\n  onHouseTypeChange() {\n    // 當戶型改變時，重新載入表單項目\n    if (this.cBuildCaseSelected?.cID) {\n      this.getListFormItem();\n    }\n  }\n  // 新增：戶別選擇器相關方法\n  toggleHouseholdSelector() {\n    this.showHouseholdSelector = !this.showHouseholdSelector;\n  }\n  onHouseholdSelectionChange(selectedItems) {\n    this.selectedHouseholdItems = selectedItems;\n    this.selectedHouseholds = selectedItems.map(item => item.code);\n    console.log('戶別選擇變更:', this.selectedHouseholds);\n  }\n  onHouseIdChange(houseIds) {\n    this.selectedHouseIds = houseIds;\n    console.log('房屋ID變更:', this.selectedHouseIds);\n  }\n  // 清除戶別選擇\n  clearHouseholdSelection() {\n    this.selectedHouseholds = [];\n    this.selectedHouseholdItems = [];\n    this.selectedHouseIds = [];\n  }\n  // 取得選中戶別的顯示文字\n  getSelectedHouseholdsDisplay() {\n    if (this.selectedHouseholds.length === 0) {\n      return '請選擇戶別';\n    }\n    if (this.selectedHouseholds.length <= 3) {\n      return this.selectedHouseholds.join('、');\n    }\n    return `${this.selectedHouseholds.slice(0, 3).join('、')} 等 ${this.selectedHouseholds.length} 戶`;\n  }\n  getListFormItem() {\n    this._formItemService.apiFormItemGetListFormItemPost$Json({\n      body: {\n        CBuildCaseId: this.cBuildCaseSelected.cID,\n        CFormType: this.selectedHouseType,\n        // 使用選中的戶型\n        PageIndex: this.pageIndex,\n        PageSize: this.pageSize,\n        CIsPaging: true\n      }\n    }).pipe(tap(res => {\n      if (res.Entries && res.StatusCode == 0) {\n        this.formItems = res.Entries.formItems;\n        this.listFormItem = res.Entries;\n        this.totalRecords = res.TotalItems ? res.TotalItems : 0;\n      }\n    })).subscribe();\n  }\n  onSelectionChangeBuildCase() {\n    this.getListFormItem();\n  }\n  pageChanged(newPage) {\n    this.pageIndex = newPage;\n    this.getListFormItem();\n  }\n  onLock() {\n    this._formItemService.apiFormItemLockFormItemPost$Json({\n      body: {\n        CBuildCaseId: this.cBuildCaseSelected.cID,\n        CFormId: this.listFormItem.CFormId\n      }\n    }).subscribe(res => {\n      if (res.StatusCode == 0) {\n        this.message.showSucessMSG(\"執行成功\");\n      } else {\n        // this.message.showErrorMSG(res.Message!);\n        this.message.showErrorMSG(\"無資料，不可鎖定\");\n      }\n      this.getListFormItem();\n    });\n  }\n  unLock() {\n    this._formItemService.apiFormItemUnlockFormItemPost$Json({\n      body: {\n        CBuildCaseID: this.cBuildCaseSelected.cID,\n        CFormId: this.listFormItem.CFormId\n      }\n    }).subscribe(res => {\n      if (res.StatusCode == 0) {\n        this.message.showSucessMSG(\"執行成功\");\n      } else {\n        this.message.showErrorMSG(res.Message);\n      }\n      this.getListFormItem();\n    });\n  }\n  navidateDetai() {\n    // 檢查是否有選擇建案\n    if (!this.cBuildCaseSelected || !this.cBuildCaseSelected.cID || this.cBuildCaseSelected.cID <= 0) {\n      this.message.showErrorMSG(\"請先選擇建案\");\n      return;\n    }\n    // 導航到詳細頁面，使用建案ID、戶型和表單ID參數\n    this.router.navigate([`pages/content-management-sales-account/${this.cBuildCaseSelected.cID}`], {\n      queryParams: {\n        houseType: this.selectedHouseType\n      }\n    });\n  }\n  static {\n    this.ɵfac = function ContentManagementSalesAccountComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || ContentManagementSalesAccountComponent)(i0.ɵɵdirectiveInject(i1.AllowHelper), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i3.MessageService), i0.ɵɵdirectiveInject(i4.BuildCaseService), i0.ɵɵdirectiveInject(i4.FormItemService), i0.ɵɵdirectiveInject(i5.EventService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ContentManagementSalesAccountComponent,\n      selectors: [[\"ngx-content-management-sales-account\"]],\n      standalone: true,\n      features: [i0.ɵɵInheritDefinitionFeature, i0.ɵɵStandaloneFeature],\n      decls: 48,\n      vars: 14,\n      consts: [[\"accent\", \"success\"], [1, \"font-bold\", \"text-[#818181]\"], [1, \"d-flex\", \"flex-wrap\"], [1, \"col-md-3\"], [1, \"form-group\", \"d-flex\", \"align-items-center\", \"w-full\"], [\"for\", \"buildingName\", 1, \"label\", \"col-3\"], [\"placeholder\", \"\\u5EFA\\u6848\", 1, \"col-9\", 3, \"ngModelChange\", \"selectedChange\", \"ngModel\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"for\", \"houseType\", 1, \"label\", \"col-3\"], [\"placeholder\", \"\\u9078\\u64C7\\u6236\\u578B\", 1, \"col-9\", 3, \"ngModelChange\", \"selectedChange\", \"ngModel\"], [\"for\", \"households\", 1, \"label\", \"col-3\"], [1, \"col-9\", \"position-relative\"], [1, \"btn\", \"btn-outline-primary\", \"w-100\", \"text-left\", \"d-flex\", \"justify-content-between\", \"align-items-center\", 3, \"click\", \"disabled\"], [1, \"text-truncate\"], [1, \"fas\", \"fa-chevron-down\"], [\"class\", \"position-absolute w-100 mt-1\", \"style\", \"z-index: 1000;\", 4, \"ngIf\"], [1, \"d-flex\", \"justify-content-end\", \"w-full\"], [4, \"ngIf\"], [1, \"table-responsive\", \"mt-4\"], [\"class\", \"alert alert-info mb-3\", 4, \"ngIf\"], [1, \"table\", \"table-striped\", \"border\", 2, \"background-color\", \"#f3f3f3\"], [2, \"background-color\", \"#27ae60\", \"color\", \"white\"], [\"scope\", \"col\", 1, \"col-1\"], [4, \"ngFor\", \"ngForOf\"], [1, \"d-flex\", \"justify-content-center\"], [\"aria-label\", \"Pagination\", 3, \"pageChange\", \"page\", \"pageSize\", \"collectionSize\"], [3, \"value\"], [1, \"position-absolute\", \"w-100\", \"mt-1\", 2, \"z-index\", \"1000\"], [3, \"ngModelChange\", \"selectionChange\", \"houseIdChange\", \"buildCaseId\", \"placeholder\", \"maxSelections\", \"showSelectedArea\", \"allowSearch\", \"allowBatchSelect\", \"ngModel\"], [\"class\", \"btn btn-danger mx-1\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"btn btn-info\", 3, \"click\", 4, \"ngIf\"], [1, \"btn\", \"btn-danger\", \"mx-1\", 3, \"click\"], [1, \"fas\", \"fa-lock\"], [1, \"btn\", \"btn-info\", 3, \"click\"], [\"class\", \"btn btn-secondary\", 3, \"click\", 4, \"ngIf\"], [1, \"btn\", \"btn-secondary\", 3, \"click\"], [1, \"alert\", \"alert-info\", \"mb-3\"], [1, \"fas\", \"fa-info-circle\"], [\"class\", \"badge badge-primary me-2 mb-1\", 4, \"ngFor\", \"ngForOf\"], [1, \"text-muted\"], [1, \"badge\", \"badge-primary\", \"me-2\", \"mb-1\"], [\"class\", \"text-muted\", 4, \"ngIf\"], [\"class\", \"badge badge-success\", 4, \"ngIf\"], [\"class\", \"badge badge-secondary\", 4, \"ngIf\"], [1, \"badge\", \"badge-success\"], [1, \"fas\", \"fa-check\"], [1, \"badge\", \"badge-secondary\"], [1, \"fas\", \"fa-times\"]],\n      template: function ContentManagementSalesAccountComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"nb-card\", 0)(1, \"nb-card-header\");\n          i0.ɵɵelement(2, \"ngx-breadcrumb\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(3, \"nb-card-body\")(4, \"h1\", 1);\n          i0.ɵɵtext(5, \" \\u60A8\\u53EF\\u5C07\\u65BC\\u5EFA\\u6750\\u7BA1\\u7406\\u53CA\\u65B9\\u6848\\u7BA1\\u7406\\u8A2D\\u5B9A\\u597D\\u7684\\u65B9\\u6848\\u53CA\\u6750\\u6599\\uFF0C\\u65BC\\u6B64\\u7D44\\u5408\\u6210\\u9078\\u6A23\\u5167\\u5BB9\\uFF0C\\u4E26\\u53EF\\u8A2D\\u5B9A\\u5404\\u65B9\\u6848\\u3001\\u6750\\u6599\\u53EF\\u9078\\u64C7\\u4E4B\\u6236\\u578B\\u3002 \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(6, \"div\", 2)(7, \"div\", 3)(8, \"div\", 4)(9, \"label\", 5);\n          i0.ɵɵtext(10, \"\\u5EFA\\u6848\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(11, \"nb-select\", 6);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function ContentManagementSalesAccountComponent_Template_nb_select_ngModelChange_11_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.cBuildCaseSelected, $event) || (ctx.cBuildCaseSelected = $event);\n            return $event;\n          });\n          i0.ɵɵlistener(\"selectedChange\", function ContentManagementSalesAccountComponent_Template_nb_select_selectedChange_11_listener() {\n            return ctx.onSelectionChangeBuildCase();\n          });\n          i0.ɵɵtemplate(12, ContentManagementSalesAccountComponent_nb_option_12_Template, 2, 2, \"nb-option\", 7);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(13, \"div\", 3)(14, \"div\", 4)(15, \"label\", 8);\n          i0.ɵɵtext(16, \"\\u985E\\u578B\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(17, \"nb-select\", 9);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function ContentManagementSalesAccountComponent_Template_nb_select_ngModelChange_17_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.selectedHouseType, $event) || (ctx.selectedHouseType = $event);\n            return $event;\n          });\n          i0.ɵɵlistener(\"selectedChange\", function ContentManagementSalesAccountComponent_Template_nb_select_selectedChange_17_listener() {\n            return ctx.onHouseTypeChange();\n          });\n          i0.ɵɵtemplate(18, ContentManagementSalesAccountComponent_nb_option_18_Template, 2, 2, \"nb-option\", 7);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(19, \"div\", 3)(20, \"div\", 4)(21, \"label\", 10);\n          i0.ɵɵtext(22, \"\\u6236\\u5225\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(23, \"div\", 11)(24, \"button\", 12);\n          i0.ɵɵlistener(\"click\", function ContentManagementSalesAccountComponent_Template_button_click_24_listener() {\n            return ctx.toggleHouseholdSelector();\n          });\n          i0.ɵɵelementStart(25, \"span\", 13);\n          i0.ɵɵtext(26);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(27, \"i\", 14);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(28, ContentManagementSalesAccountComponent_div_28_Template, 2, 7, \"div\", 15);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(29, \"div\", 3)(30, \"div\", 16);\n          i0.ɵɵtemplate(31, ContentManagementSalesAccountComponent_ng_container_31_Template, 3, 2, \"ng-container\", 17)(32, ContentManagementSalesAccountComponent_ng_container_32_Template, 2, 1, \"ng-container\", 17);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(33, \"div\", 18);\n          i0.ɵɵtemplate(34, ContentManagementSalesAccountComponent_div_34_Template, 8, 2, \"div\", 19);\n          i0.ɵɵelementStart(35, \"table\", 20)(36, \"thead\")(37, \"tr\", 21)(38, \"th\", 22);\n          i0.ɵɵtext(39, \"\\u65B9\\u6848\\u540D\\u7A31/\\u5EFA\\u6750\\u4F4D\\u7F6E\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(40, \"th\", 22);\n          i0.ɵɵtext(41, \"\\u9069\\u7528\\u6236\\u5225\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(42, \"th\", 22);\n          i0.ɵɵtext(43, \"\\u5DF2\\u9078\\u6236\\u5225\\u5339\\u914D\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(44, \"tbody\");\n          i0.ɵɵtemplate(45, ContentManagementSalesAccountComponent_tr_45_Template, 8, 4, \"tr\", 23);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(46, \"nb-card-footer\", 24)(47, \"ngb-pagination\", 25);\n          i0.ɵɵtwoWayListener(\"pageChange\", function ContentManagementSalesAccountComponent_Template_ngb_pagination_pageChange_47_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.pageIndex, $event) || (ctx.pageIndex = $event);\n            return $event;\n          });\n          i0.ɵɵlistener(\"pageChange\", function ContentManagementSalesAccountComponent_Template_ngb_pagination_pageChange_47_listener($event) {\n            return ctx.pageChanged($event);\n          });\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(11);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.cBuildCaseSelected);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngForOf\", ctx.userBuildCaseOptions);\n          i0.ɵɵadvance(5);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.selectedHouseType);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngForOf\", ctx.houseTypeOptions);\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"disabled\", !(ctx.cBuildCaseSelected == null ? null : ctx.cBuildCaseSelected.cID));\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.getSelectedHouseholdsDisplay());\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.showHouseholdSelector);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", ctx.formItems && ctx.formItems.length > 0 && ctx.listFormItem && !ctx.listFormItem.CIsLock && ctx.cBuildCaseSelected && ctx.cBuildCaseSelected.cID > 0);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.formItems && ctx.formItems.length > 0 && ctx.listFormItem && ctx.listFormItem.CIsLock && ctx.cBuildCaseSelected && ctx.cBuildCaseSelected.cID > 0);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.selectedHouseholds.length > 0);\n          i0.ɵɵadvance(11);\n          i0.ɵɵproperty(\"ngForOf\", ctx.formItems);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtwoWayProperty(\"page\", ctx.pageIndex);\n          i0.ɵɵproperty(\"pageSize\", ctx.pageSize)(\"collectionSize\", ctx.totalRecords);\n        }\n      },\n      dependencies: [CommonModule, i6.NgForOf, i6.NgIf, SharedModule, i7.NgControlStatus, i7.NgModel, i8.NbCardComponent, i8.NbCardBodyComponent, i8.NbCardFooterComponent, i8.NbCardHeaderComponent, i8.NbSelectComponent, i8.NbOptionComponent, i9.NgbPagination, i10.BreadcrumbComponent, AppSharedModule, i11.HouseholdBindingComponent],\n      styles: [\"/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsImZpbGUiOiJjb250ZW50LW1hbmFnZW1lbnQtc2FsZXMtYWNjb3VudC5jb21wb25lbnQuc2NzcyJ9 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvcGFnZXMvc2VsZWN0aW9uLW1hbmFnZW1lbnQvY29udGVudC1tYW5hZ2VtZW50LXNhbGVzLWFjY291bnQvY29udGVudC1tYW5hZ2VtZW50LXNhbGVzLWFjY291bnQuY29tcG9uZW50LnNjc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IjtBQUNBLG9NQUFvTSIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "tap", "SharedModule", "BaseComponent", "EEvent", "EnumHouseType", "AppSharedModule", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵproperty", "case_r1", "ɵɵadvance", "ɵɵtextInterpolate1", "CBuildCaseName", "option_r2", "value", "label", "ɵɵtwoWayListener", "ContentManagementSalesAccountComponent_div_28_Template_app_household_binding_ngModelChange_1_listener", "$event", "ɵɵrestoreView", "_r3", "ctx_r3", "ɵɵnextContext", "ɵɵtwoWayBindingSet", "selectedHouseholds", "ɵɵresetView", "ɵɵlistener", "ContentManagementSalesAccountComponent_div_28_Template_app_household_binding_selectionChange_1_listener", "onHouseholdSelectionChange", "ContentManagementSalesAccountComponent_div_28_Template_app_household_binding_houseIdChange_1_listener", "onHouseIdChange", "cBuildCaseSelected", "cID", "ɵɵtwoWayProperty", "ContentManagementSalesAccountComponent_ng_container_31_button_1_Template_button_click_0_listener", "_r5", "toggleSwitch", "listFormItem", "CIsLock", "ɵɵelement", "ContentManagementSalesAccountComponent_ng_container_31_button_2_Template_button_click_0_listener", "_r6", "navid<PERSON><PERSON><PERSON><PERSON>", "ɵɵelementContainerStart", "ɵɵtemplate", "ContentManagementSalesAccountComponent_ng_container_31_button_1_Template", "ContentManagementSalesAccountComponent_ng_container_31_button_2_Template", "isUpdate", "formItems", "ContentManagementSalesAccountComponent_ng_container_32_button_1_Template_button_click_0_listener", "_r7", "ContentManagementSalesAccountComponent_ng_container_32_button_1_Template", "household_r8", "ContentManagementSalesAccountComponent_div_34_span_5_Template", "length", "ɵɵtextInterpolate2", "ix_r10", "i_r9", "CHousehold", "ContentManagementSalesAccountComponent_tr_45_ng_container_6_span_1_Template", "ContentManagementSalesAccountComponent_tr_45_ng_container_6_span_2_Template", "hasMatchingHouseholds", "item_r11", "tblFormItemHouseholds", "ContentManagementSalesAccountComponent_tr_45_span_4_Template", "ContentManagementSalesAccountComponent_tr_45_ng_container_6_Template", "ContentManagementSalesAccountComponent_tr_45_span_7_Template", "ɵɵtextInterpolate", "CItemName", "ContentManagementSalesAccountComponent", "unLock", "onLock", "constructor", "_allow", "router", "message", "_buildCaseService", "_formItemService", "_eventService", "houseTypeOptions", "地主戶", "銷售戶", "selectedHouseType", "selectedHouseholdItems", "selectedHouseIds", "showHouseholdSelector", "tempBuildCaseID", "buildingSelectedOptions", "pageSize", "receive", "pipe", "res", "action", "payload", "subscribe", "ngOnInit", "getUserBuildCase", "apiBuildCaseGetUserBuildCasePost$Json", "body", "CBuildCaseId", "buildCaseId", "Entries", "StatusCode", "userBuildCaseOptions", "map", "index", "findIndex", "x", "getListFormItem", "onHouseTypeChange", "toggleHouseholdSelector", "selectedItems", "item", "code", "console", "log", "houseIds", "clearHouseholdSelection", "getSelectedHouseholdsDisplay", "join", "slice", "apiFormItemGetListFormItemPost$Json", "CFormType", "PageIndex", "pageIndex", "PageSize", "CIsPaging", "totalRecords", "TotalItems", "onSelectionChangeBuildCase", "pageChanged", "newPage", "apiFormItemLockFormItemPost$Json", "CFormId", "showSucessMSG", "showErrorMSG", "apiFormItemUnlockFormItemPost$Json", "CBuildCaseID", "Message", "navigate", "queryParams", "houseType", "ɵɵdirectiveInject", "i1", "AllowHelper", "i2", "Router", "i3", "MessageService", "i4", "BuildCaseService", "FormItemService", "i5", "EventService", "selectors", "standalone", "features", "ɵɵInheritDefinitionFeature", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "ContentManagementSalesAccountComponent_Template", "rf", "ctx", "ContentManagementSalesAccountComponent_Template_nb_select_ngModelChange_11_listener", "ContentManagementSalesAccountComponent_Template_nb_select_selectedChange_11_listener", "ContentManagementSalesAccountComponent_nb_option_12_Template", "ContentManagementSalesAccountComponent_Template_nb_select_ngModelChange_17_listener", "ContentManagementSalesAccountComponent_Template_nb_select_selectedChange_17_listener", "ContentManagementSalesAccountComponent_nb_option_18_Template", "ContentManagementSalesAccountComponent_Template_button_click_24_listener", "ContentManagementSalesAccountComponent_div_28_Template", "ContentManagementSalesAccountComponent_ng_container_31_Template", "ContentManagementSalesAccountComponent_ng_container_32_Template", "ContentManagementSalesAccountComponent_div_34_Template", "ContentManagementSalesAccountComponent_tr_45_Template", "ContentManagementSalesAccountComponent_Template_ngb_pagination_pageChange_47_listener", "i6", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "i7", "NgControlStatus", "NgModel", "i8", "NbCardComponent", "NbCardBodyComponent", "NbCardFooterComponent", "NbCardHeaderComponent", "NbSelectComponent", "NbOptionComponent", "i9", "NgbPagination", "i10", "BreadcrumbComponent", "i11", "HouseholdBindingComponent", "styles"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\selection-management\\content-management-sales-account\\content-management-sales-account.component.ts", "C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\selection-management\\content-management-sales-account\\content-management-sales-account.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { Router } from '@angular/router';\r\nimport { tap } from 'rxjs/operators';\r\nimport { AllowHelper } from 'src/app/shared/helper/allowHelper';\r\nimport { BuildCaseService, FormItemService } from 'src/services/api/services';\r\nimport { MessageService } from 'src/app/shared/services/message.service';\r\nimport { GetListFormItemRes } from 'src/services/api/models';\r\nimport { SharedModule } from '../../components/shared.module';\r\nimport { BaseComponent } from '../../components/base/baseComponent';\r\nimport { EventService, IEvent, EEvent } from 'src/app/shared/services/event.service';\r\nimport { EnumHouseType } from 'src/app/shared/enum/enumHouseType';\r\n// 新增：導入戶別選擇器相關模組\r\nimport { SharedModule as AppSharedModule } from 'src/app/shared/shared.module';\r\nimport { HouseholdItem } from 'src/app/shared/components/household-binding/household-binding.component';\r\n\r\nexport interface selectItem {\r\n  label: string,\r\n  value: number,\r\n  key?: string\r\n}\r\n\r\n// 戶型選項介面\r\nexport interface HouseTypeOption {\r\n  label: string;\r\n  value: EnumHouseType;\r\n}\r\n\r\n@Component({\r\n  selector: 'ngx-content-management-sales-account',\r\n  templateUrl: './content-management-sales-account.component.html',\r\n  styleUrls: ['./content-management-sales-account.component.scss'],\r\n  standalone: true,\r\n  imports: [CommonModule, SharedModule, AppSharedModule],\r\n})\r\nexport class ContentManagementSalesAccountComponent extends BaseComponent implements OnInit {\r\n\r\n  // 戶型選項\r\n  houseTypeOptions: HouseTypeOption[] = [\r\n    { label: '地主戶', value: EnumHouseType.地主戶 },\r\n    { label: '銷售戶', value: EnumHouseType.銷售戶 }\r\n  ];\r\n\r\n  // 選中的戶型\r\n  selectedHouseType: EnumHouseType = EnumHouseType.銷售戶;\r\n\r\n  // 新增：戶別選擇器相關屬性\r\n  selectedHouseholds: string[] = []; // 選中的戶別\r\n  selectedHouseholdItems: HouseholdItem[] = []; // 完整的戶別項目資訊\r\n  selectedHouseIds: number[] = []; // 選中的房屋ID\r\n  showHouseholdSelector: boolean = false; // 是否顯示戶別選擇器\r\n\r\n  toggleSwitch(CIsLock: any) {\r\n    if (CIsLock) {\r\n      this.unLock()\r\n    } else {\r\n      this.onLock()\r\n    }\r\n  }\r\n\r\n  tempBuildCaseID: number = -1\r\n  selectedBuilding: any;\r\n  buildingSelectedOptions: any[] = [{ value: '', label: '全部' }];\r\n\r\n  formItems: any;\r\n  listFormItem: GetListFormItemRes;\r\n  override pageSize = 20;\r\n\r\n  buildCaseId: number;\r\n  cBuildCaseSelected: any;\r\n  userBuildCaseOptions: any;\r\n\r\n  constructor(\r\n    private _allow: AllowHelper,\r\n    private router: Router,\r\n    private message: MessageService,\r\n    private _buildCaseService: BuildCaseService,\r\n    private _formItemService: FormItemService,\r\n    private _eventService: EventService,\r\n  ) {\r\n    super(_allow);\r\n    this._eventService.receive().pipe(\r\n      tap((res: IEvent) => {\r\n        if (res.action == EEvent.GET_BUILDCASE && !!res.payload) {\r\n          this.tempBuildCaseID = res.payload\r\n        }\r\n      })\r\n    ).subscribe()\r\n  }\r\n\r\n  override ngOnInit(): void {\r\n    this.cBuildCaseSelected = null;\r\n    this.getUserBuildCase()\r\n  }\r\n\r\n  getUserBuildCase() {\r\n    this._buildCaseService.apiBuildCaseGetUserBuildCasePost$Json({\r\n      body: {\r\n        CBuildCaseId: this.buildCaseId\r\n      }\r\n    }).pipe(\r\n      tap(res => {\r\n        if (res.Entries && res.StatusCode == 0) {\r\n          this.userBuildCaseOptions = res.Entries.map(res => {\r\n            return {\r\n              CBuildCaseName: res.CBuildCaseName,\r\n              cID: res.cID\r\n            };\r\n          }); if (this.tempBuildCaseID != -1) {\r\n            let index = this.userBuildCaseOptions.findIndex((x: any) => x.cID == this.tempBuildCaseID)\r\n            if (index >= 0) {\r\n              this.cBuildCaseSelected = this.userBuildCaseOptions[index]\r\n            } else {\r\n              this.cBuildCaseSelected = this.userBuildCaseOptions[0];\r\n            }\r\n          } else {\r\n            this.cBuildCaseSelected = this.userBuildCaseOptions[0];\r\n          }\r\n          if (this.cBuildCaseSelected && this.cBuildCaseSelected.cID && this.cBuildCaseSelected.cID > 0) {\r\n            this.getListFormItem();\r\n          }\r\n        }\r\n      })\r\n    ).subscribe();\r\n  }\r\n  // 移除硬編碼的類型配置，改為動態使用選中的戶型\r\n  // typeContentManagementSalesAccount = {\r\n  //   CFormType: 2,\r\n  // }\r\n\r\n  // 戶型選項變更處理\r\n  onHouseTypeChange(): void {\r\n    // 當戶型改變時，重新載入表單項目\r\n    if (this.cBuildCaseSelected?.cID) {\r\n      this.getListFormItem();\r\n    }\r\n  }\r\n\r\n  // 新增：戶別選擇器相關方法\r\n  toggleHouseholdSelector(): void {\r\n    this.showHouseholdSelector = !this.showHouseholdSelector;\r\n  }\r\n\r\n  onHouseholdSelectionChange(selectedItems: HouseholdItem[]): void {\r\n    this.selectedHouseholdItems = selectedItems;\r\n    this.selectedHouseholds = selectedItems.map(item => item.code);\r\n    console.log('戶別選擇變更:', this.selectedHouseholds);\r\n  }\r\n\r\n  onHouseIdChange(houseIds: number[]): void {\r\n    this.selectedHouseIds = houseIds;\r\n    console.log('房屋ID變更:', this.selectedHouseIds);\r\n  }\r\n\r\n  // 清除戶別選擇\r\n  clearHouseholdSelection(): void {\r\n    this.selectedHouseholds = [];\r\n    this.selectedHouseholdItems = [];\r\n    this.selectedHouseIds = [];\r\n  }\r\n\r\n  // 取得選中戶別的顯示文字\r\n  getSelectedHouseholdsDisplay(): string {\r\n    if (this.selectedHouseholds.length === 0) {\r\n      return '請選擇戶別';\r\n    }\r\n    if (this.selectedHouseholds.length <= 3) {\r\n      return this.selectedHouseholds.join('、');\r\n    }\r\n    return `${this.selectedHouseholds.slice(0, 3).join('、')} 等 ${this.selectedHouseholds.length} 戶`;\r\n  }\r\n\r\n  getListFormItem() {\r\n    this._formItemService.apiFormItemGetListFormItemPost$Json({\r\n      body: {\r\n        CBuildCaseId: this.cBuildCaseSelected.cID,\r\n        CFormType: this.selectedHouseType, // 使用選中的戶型\r\n        PageIndex: this.pageIndex,\r\n        PageSize: this.pageSize,\r\n        CIsPaging: true\r\n      }\r\n    }).pipe(\r\n      tap(res => {\r\n        if (res.Entries && res.StatusCode == 0) {\r\n          this.formItems = res.Entries.formItems;\r\n          this.listFormItem = res.Entries;\r\n          this.totalRecords = res.TotalItems ? res.TotalItems : 0\r\n        }\r\n      })\r\n    ).subscribe();\r\n  }\r\n\r\n  onSelectionChangeBuildCase() {\r\n    this.getListFormItem();\r\n  }\r\n\r\n  pageChanged(newPage: number) {\r\n    this.pageIndex = newPage;\r\n    this.getListFormItem();\r\n  }\r\n\r\n  onLock() {\r\n    this._formItemService.apiFormItemLockFormItemPost$Json({\r\n      body: {\r\n        CBuildCaseId: this.cBuildCaseSelected.cID,\r\n        CFormId: this.listFormItem.CFormId\r\n      }\r\n    }).subscribe(res => {\r\n      if (res.StatusCode == 0) {\r\n        this.message.showSucessMSG(\"執行成功\");\r\n      } else {\r\n        // this.message.showErrorMSG(res.Message!);\r\n        this.message.showErrorMSG(\"無資料，不可鎖定\");\r\n      }\r\n      this.getListFormItem();\r\n    });\r\n  }\r\n\r\n  unLock() {\r\n    this._formItemService.apiFormItemUnlockFormItemPost$Json({\r\n      body: {\r\n        CBuildCaseID: this.cBuildCaseSelected.cID,\r\n        CFormId: this.listFormItem.CFormId\r\n      }\r\n    }).subscribe(res => {\r\n      if (res.StatusCode == 0) {\r\n        this.message.showSucessMSG(\"執行成功\");\r\n      } else {\r\n        this.message.showErrorMSG(res.Message!);\r\n      } this.getListFormItem();\r\n    });\r\n  }\r\n  navidateDetai() {\r\n    // 檢查是否有選擇建案\r\n    if (!this.cBuildCaseSelected || !this.cBuildCaseSelected.cID || this.cBuildCaseSelected.cID <= 0) {\r\n      this.message.showErrorMSG(\"請先選擇建案\");\r\n      return;\r\n    }\r\n\r\n    // 導航到詳細頁面，使用建案ID、戶型和表單ID參數\r\n    this.router.navigate([`pages/content-management-sales-account/${this.cBuildCaseSelected.cID}`], {\r\n      queryParams: {\r\n        houseType: this.selectedHouseType,\r\n      }\r\n    });\r\n  }\r\n}\r\n", "<!-- 3.6  3.7 = 1, 3.6 = 2-->\r\n<nb-card accent=\"success\">\r\n  <nb-card-header>\r\n    <ngx-breadcrumb></ngx-breadcrumb>\r\n  </nb-card-header>\r\n  <nb-card-body>\r\n    <h1 class=\"font-bold text-[#818181]\">\r\n      您可將於建材管理及方案管理設定好的方案及材料，於此組合成選樣內容，並可設定各方案、材料可選擇之戶型。\r\n    </h1>    <div class=\"d-flex flex-wrap\">\r\n      <div class=\"col-md-3\">\r\n        <div class=\"form-group d-flex align-items-center w-full\">\r\n          <label for=\"buildingName\" class=\"label col-3\">建案</label>\r\n          <nb-select placeholder=\"建案\" [(ngModel)]=\"cBuildCaseSelected\" class=\"col-9\"\r\n            (selectedChange)=\"onSelectionChangeBuildCase()\">\r\n            <nb-option *ngFor=\"let case of userBuildCaseOptions\" [value]=\"case\">\r\n              {{ case.CBuildCaseName }}\r\n            </nb-option>\r\n          </nb-select>\r\n        </div>\r\n      </div>\r\n      <div class=\"col-md-3\">\r\n        <div class=\"form-group d-flex align-items-center w-full\">\r\n          <label for=\"houseType\" class=\"label col-3\">類型</label>\r\n          <nb-select placeholder=\"選擇戶型\" [(ngModel)]=\"selectedHouseType\" class=\"col-9\"\r\n            (selectedChange)=\"onHouseTypeChange()\">\r\n            <nb-option *ngFor=\"let option of houseTypeOptions\" [value]=\"option.value\">\r\n              {{ option.label }}\r\n            </nb-option>\r\n          </nb-select>\r\n        </div>\r\n      </div>\r\n      <div class=\"col-md-3\">\r\n        <div class=\"form-group d-flex align-items-center w-full\">\r\n          <label for=\"households\" class=\"label col-3\">戶別</label>\r\n          <div class=\"col-9 position-relative\">\r\n            <button class=\"btn btn-outline-primary w-100 text-left d-flex justify-content-between align-items-center\"\r\n                    (click)=\"toggleHouseholdSelector()\"\r\n                    [disabled]=\"!cBuildCaseSelected?.cID\">\r\n              <span class=\"text-truncate\">{{ getSelectedHouseholdsDisplay() }}</span>\r\n              <i class=\"fas fa-chevron-down\"></i>\r\n            </button>\r\n            <div class=\"position-absolute w-100 mt-1\" style=\"z-index: 1000;\" *ngIf=\"showHouseholdSelector\">\r\n              <app-household-binding\r\n                [buildCaseId]=\"cBuildCaseSelected?.cID\"\r\n                [placeholder]=\"'請選擇戶別'\"\r\n                [maxSelections]=\"null\"\r\n                [showSelectedArea]=\"true\"\r\n                [allowSearch]=\"true\"\r\n                [allowBatchSelect]=\"true\"\r\n                [(ngModel)]=\"selectedHouseholds\"\r\n                (selectionChange)=\"onHouseholdSelectionChange($event)\"\r\n                (houseIdChange)=\"onHouseIdChange($event)\">\r\n              </app-household-binding>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <div class=\"col-md-3\">\r\n        <div class=\"d-flex justify-content-end w-full\">\r\n\r\n          <!-- 當有資料且未鎖定時顯示的按鈕 -->\r\n          <ng-container\r\n            *ngIf=\"formItems && formItems.length > 0 && listFormItem && !listFormItem.CIsLock && cBuildCaseSelected && cBuildCaseSelected.cID > 0\">\r\n            <!-- 鎖定按鈕 -->\r\n            <button class=\"btn btn-danger mx-1\" *ngIf=\"isUpdate\" (click)=\"toggleSwitch(listFormItem.CIsLock)\">\r\n              <i class=\"fas fa-lock\"></i> 鎖定\r\n            </button>\r\n\r\n            <!-- 編輯按鈕 -->\r\n            <button class=\"btn btn-info\" *ngIf=\"listFormItem.formItems && isUpdate\" (click)=\"navidateDetai()\">\r\n              編輯內容\r\n            </button>\r\n          </ng-container>\r\n\r\n          <!-- 當有資料且已鎖定時顯示的按鈕 -->\r\n          <ng-container\r\n            *ngIf=\"formItems && formItems.length > 0 && listFormItem && listFormItem.CIsLock && cBuildCaseSelected && cBuildCaseSelected.cID > 0\">\r\n            <!-- 查看按鈕 -->\r\n            <button class=\"btn btn-secondary\" *ngIf=\"listFormItem.formItems && isUpdate\" (click)=\"navidateDetai()\">\r\n              查看內容\r\n            </button>\r\n          </ng-container>\r\n        </div>\r\n      </div>\r\n    </div>    <div class=\"table-responsive mt-4\">\r\n      <!-- 顯示目前選中的戶別資訊 -->\r\n      <div class=\"alert alert-info mb-3\" *ngIf=\"selectedHouseholds.length > 0\">\r\n        <h6><i class=\"fas fa-info-circle\"></i> 已選擇戶別</h6>\r\n        <div class=\"d-flex flex-wrap\">\r\n          <span class=\"badge badge-primary me-2 mb-1\" *ngFor=\"let household of selectedHouseholds\">\r\n            {{ household }}\r\n          </span>\r\n        </div>\r\n        <small class=\"text-muted\">共選擇 {{ selectedHouseholds.length }} 戶</small>\r\n      </div>\r\n      \r\n      <table class=\"table table-striped border \" style=\"background-color:#f3f3f3;\">\r\n        <thead>\r\n          <tr style=\"background-color: #27ae60; color: white;\">\r\n            <th scope=\"col\" class=\"col-1\">方案名稱/建材位置</th>\r\n            <th scope=\"col\" class=\"col-1\">適用戶別</th>\r\n            <th scope=\"col\" class=\"col-1\">已選戶別匹配</th>\r\n          </tr>\r\n        </thead>\r\n        <tbody>\r\n          <tr *ngFor=\"let item of formItems ; let i = index\">\r\n            <td>{{ item.CItemName}}</td>\r\n            <td>\r\n              <span *ngFor=\"let i of item.tblFormItemHouseholds ; let ix = index\">\r\n                {{ix > 0 ? '、' :''}} {{i.CHousehold}}\r\n              </span>\r\n            </td>\r\n            <td>\r\n              <ng-container *ngIf=\"selectedHouseholds.length > 0\">\r\n                <span class=\"badge badge-success\" \r\n                      *ngIf=\"hasMatchingHouseholds(item.tblFormItemHouseholds)\">\r\n                  <i class=\"fas fa-check\"></i> 匹配\r\n                </span>\r\n                <span class=\"badge badge-secondary\" \r\n                      *ngIf=\"!hasMatchingHouseholds(item.tblFormItemHouseholds)\">\r\n                  <i class=\"fas fa-times\"></i> 不匹配\r\n                </span>\r\n              </ng-container>\r\n              <span class=\"text-muted\" *ngIf=\"selectedHouseholds.length === 0\">\r\n                請選擇戶別\r\n              </span>\r\n            </td>\r\n          </tr>\r\n        </tbody>\r\n      </table>\r\n    </div>\r\n  </nb-card-body>\r\n  <nb-card-footer class=\"d-flex justify-content-center\">\r\n    <ngb-pagination [(page)]=\"pageIndex\" [pageSize]=\"pageSize\" [collectionSize]=\"totalRecords\"\r\n      (pageChange)=\"pageChanged($event)\" aria-label=\"Pagination\">\r\n    </ngb-pagination>\r\n  </nb-card-footer>\r\n</nb-card>"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAE9C,SAASC,GAAG,QAAQ,gBAAgB;AAKpC,SAASC,YAAY,QAAQ,gCAAgC;AAC7D,SAASC,aAAa,QAAQ,qCAAqC;AACnE,SAA+BC,MAAM,QAAQ,uCAAuC;AACpF,SAASC,aAAa,QAAQ,mCAAmC;AACjE;AACA,SAASH,YAAY,IAAII,eAAe,QAAQ,8BAA8B;;;;;;;;;;;;;;;ICClEC,EAAA,CAAAC,cAAA,oBAAoE;IAClED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAFyCH,EAAA,CAAAI,UAAA,UAAAC,OAAA,CAAc;IACjEL,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAAO,kBAAA,MAAAF,OAAA,CAAAG,cAAA,MACF;;;;;IASAR,EAAA,CAAAC,cAAA,oBAA0E;IACxED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAFuCH,EAAA,CAAAI,UAAA,UAAAK,SAAA,CAAAC,KAAA,CAAsB;IACvEV,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAAO,kBAAA,MAAAE,SAAA,CAAAE,KAAA,MACF;;;;;;IAeEX,EADF,CAAAC,cAAA,cAA+F,gCAUjD;IAF1CD,EAAA,CAAAY,gBAAA,2BAAAC,sGAAAC,MAAA;MAAAd,EAAA,CAAAe,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAAAlB,EAAA,CAAAmB,kBAAA,CAAAF,MAAA,CAAAG,kBAAA,EAAAN,MAAA,MAAAG,MAAA,CAAAG,kBAAA,GAAAN,MAAA;MAAA,OAAAd,EAAA,CAAAqB,WAAA,CAAAP,MAAA;IAAA,EAAgC;IAEhCd,EADA,CAAAsB,UAAA,6BAAAC,wGAAAT,MAAA;MAAAd,EAAA,CAAAe,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAAA,OAAAlB,EAAA,CAAAqB,WAAA,CAAmBJ,MAAA,CAAAO,0BAAA,CAAAV,MAAA,CAAkC;IAAA,EAAC,2BAAAW,sGAAAX,MAAA;MAAAd,EAAA,CAAAe,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAAA,OAAAlB,EAAA,CAAAqB,WAAA,CACrCJ,MAAA,CAAAS,eAAA,CAAAZ,MAAA,CAAuB;IAAA,EAAC;IAE7Cd,EADE,CAAAG,YAAA,EAAwB,EACpB;;;;IAVFH,EAAA,CAAAM,SAAA,EAAuC;IAKvCN,EALA,CAAAI,UAAA,gBAAAa,MAAA,CAAAU,kBAAA,kBAAAV,MAAA,CAAAU,kBAAA,CAAAC,GAAA,CAAuC,iDAChB,uBACD,0BACG,qBACL,0BACK;IACzB5B,EAAA,CAAA6B,gBAAA,YAAAZ,MAAA,CAAAG,kBAAA,CAAgC;;;;;;IAepCpB,EAAA,CAAAC,cAAA,iBAAkG;IAA7CD,EAAA,CAAAsB,UAAA,mBAAAQ,iGAAA;MAAA9B,EAAA,CAAAe,aAAA,CAAAgB,GAAA;MAAA,MAAAd,MAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAAA,OAAAlB,EAAA,CAAAqB,WAAA,CAASJ,MAAA,CAAAe,YAAA,CAAAf,MAAA,CAAAgB,YAAA,CAAAC,OAAA,CAAkC;IAAA,EAAC;IAC/FlC,EAAA,CAAAmC,SAAA,YAA2B;IAACnC,EAAA,CAAAE,MAAA,qBAC9B;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;IAGTH,EAAA,CAAAC,cAAA,iBAAkG;IAA1BD,EAAA,CAAAsB,UAAA,mBAAAc,iGAAA;MAAApC,EAAA,CAAAe,aAAA,CAAAsB,GAAA;MAAA,MAAApB,MAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAAA,OAAAlB,EAAA,CAAAqB,WAAA,CAASJ,MAAA,CAAAqB,aAAA,EAAe;IAAA,EAAC;IAC/FtC,EAAA,CAAAE,MAAA,iCACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;IAVXH,EAAA,CAAAuC,uBAAA,GACyI;IAOvIvC,EALA,CAAAwC,UAAA,IAAAC,wEAAA,qBAAkG,IAAAC,wEAAA,qBAKA;;;;;IAL7D1C,EAAA,CAAAM,SAAA,EAAc;IAAdN,EAAA,CAAAI,UAAA,SAAAa,MAAA,CAAA0B,QAAA,CAAc;IAKrB3C,EAAA,CAAAM,SAAA,EAAwC;IAAxCN,EAAA,CAAAI,UAAA,SAAAa,MAAA,CAAAgB,YAAA,CAAAW,SAAA,IAAA3B,MAAA,CAAA0B,QAAA,CAAwC;;;;;;IAStE3C,EAAA,CAAAC,cAAA,iBAAuG;IAA1BD,EAAA,CAAAsB,UAAA,mBAAAuB,iGAAA;MAAA7C,EAAA,CAAAe,aAAA,CAAA+B,GAAA;MAAA,MAAA7B,MAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAAA,OAAAlB,EAAA,CAAAqB,WAAA,CAASJ,MAAA,CAAAqB,aAAA,EAAe;IAAA,EAAC;IACpGtC,EAAA,CAAAE,MAAA,iCACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;IALXH,EAAA,CAAAuC,uBAAA,GACwI;IAEtIvC,EAAA,CAAAwC,UAAA,IAAAO,wEAAA,qBAAuG;;;;;IAApE/C,EAAA,CAAAM,SAAA,EAAwC;IAAxCN,EAAA,CAAAI,UAAA,SAAAa,MAAA,CAAAgB,YAAA,CAAAW,SAAA,IAAA3B,MAAA,CAAA0B,QAAA,CAAwC;;;;;IAW7E3C,EAAA,CAAAC,cAAA,eAAyF;IACvFD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IADLH,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAAO,kBAAA,MAAAyC,YAAA,MACF;;;;;IAJFhD,EADF,CAAAC,cAAA,cAAyE,SACnE;IAAAD,EAAA,CAAAmC,SAAA,YAAkC;IAACnC,EAAA,CAAAE,MAAA,sCAAK;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACjDH,EAAA,CAAAC,cAAA,aAA8B;IAC5BD,EAAA,CAAAwC,UAAA,IAAAS,6DAAA,mBAAyF;IAG3FjD,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,gBAA0B;IAAAD,EAAA,CAAAE,MAAA,GAAqC;IACjEF,EADiE,CAAAG,YAAA,EAAQ,EACnE;;;;IALgEH,EAAA,CAAAM,SAAA,GAAqB;IAArBN,EAAA,CAAAI,UAAA,YAAAa,MAAA,CAAAG,kBAAA,CAAqB;IAI/DpB,EAAA,CAAAM,SAAA,GAAqC;IAArCN,EAAA,CAAAO,kBAAA,wBAAAU,MAAA,CAAAG,kBAAA,CAAA8B,MAAA,YAAqC;;;;;IAezDlD,EAAA,CAAAC,cAAA,WAAoE;IAClED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IADLH,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAAmD,kBAAA,MAAAC,MAAA,2BAAAC,IAAA,CAAAC,UAAA,MACF;;;;;IAIEtD,EAAA,CAAAC,cAAA,eACgE;IAC9DD,EAAA,CAAAmC,SAAA,YAA4B;IAACnC,EAAA,CAAAE,MAAA,qBAC/B;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IACPH,EAAA,CAAAC,cAAA,eACiE;IAC/DD,EAAA,CAAAmC,SAAA,YAA4B;IAACnC,EAAA,CAAAE,MAAA,2BAC/B;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IARTH,EAAA,CAAAuC,uBAAA,GAAoD;IAKlDvC,EAJA,CAAAwC,UAAA,IAAAe,2EAAA,mBACgE,IAAAC,2EAAA,mBAIC;;;;;;IAJ1DxD,EAAA,CAAAM,SAAA,EAAuD;IAAvDN,EAAA,CAAAI,UAAA,SAAAa,MAAA,CAAAwC,qBAAA,CAAAC,QAAA,CAAAC,qBAAA,EAAuD;IAIvD3D,EAAA,CAAAM,SAAA,EAAwD;IAAxDN,EAAA,CAAAI,UAAA,UAAAa,MAAA,CAAAwC,qBAAA,CAAAC,QAAA,CAAAC,qBAAA,EAAwD;;;;;IAIjE3D,EAAA,CAAAC,cAAA,eAAiE;IAC/DD,EAAA,CAAAE,MAAA,uCACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAnBTH,EADF,CAAAC,cAAA,SAAmD,SAC7C;IAAAD,EAAA,CAAAE,MAAA,GAAmB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC5BH,EAAA,CAAAC,cAAA,SAAI;IACFD,EAAA,CAAAwC,UAAA,IAAAoB,4DAAA,mBAAoE;IAGtE5D,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,SAAI;IAWFD,EAVA,CAAAwC,UAAA,IAAAqB,oEAAA,2BAAoD,IAAAC,4DAAA,mBAUa;IAIrE9D,EADE,CAAAG,YAAA,EAAK,EACF;;;;;IArBCH,EAAA,CAAAM,SAAA,GAAmB;IAAnBN,EAAA,CAAA+D,iBAAA,CAAAL,QAAA,CAAAM,SAAA,CAAmB;IAEDhE,EAAA,CAAAM,SAAA,GAAgC;IAAhCN,EAAA,CAAAI,UAAA,YAAAsD,QAAA,CAAAC,qBAAA,CAAgC;IAKrC3D,EAAA,CAAAM,SAAA,GAAmC;IAAnCN,EAAA,CAAAI,UAAA,SAAAa,MAAA,CAAAG,kBAAA,CAAA8B,MAAA,KAAmC;IAUxBlD,EAAA,CAAAM,SAAA,EAAqC;IAArCN,EAAA,CAAAI,UAAA,SAAAa,MAAA,CAAAG,kBAAA,CAAA8B,MAAA,OAAqC;;;ADxF7E,OAAM,MAAOe,sCAAuC,SAAQrE,aAAa;EAiBvEoC,YAAYA,CAACE,OAAY;IACvB,IAAIA,OAAO,EAAE;MACX,IAAI,CAACgC,MAAM,EAAE;IACf,CAAC,MAAM;MACL,IAAI,CAACC,MAAM,EAAE;IACf;EACF;EAcAC,YACUC,MAAmB,EACnBC,MAAc,EACdC,OAAuB,EACvBC,iBAAmC,EACnCC,gBAAiC,EACjCC,aAA2B;IAEnC,KAAK,CAACL,MAAM,CAAC;IAPL,KAAAA,MAAM,GAANA,MAAM;IACN,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAC,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,aAAa,GAAbA,aAAa;IAzCvB;IACA,KAAAC,gBAAgB,GAAsB,CACpC;MAAEhE,KAAK,EAAE,KAAK;MAAED,KAAK,EAAEZ,aAAa,CAAC8E;IAAG,CAAE,EAC1C;MAAEjE,KAAK,EAAE,KAAK;MAAED,KAAK,EAAEZ,aAAa,CAAC+E;IAAG,CAAE,CAC3C;IAED;IACA,KAAAC,iBAAiB,GAAkBhF,aAAa,CAAC+E,GAAG;IAEpD;IACA,KAAAzD,kBAAkB,GAAa,EAAE,CAAC,CAAC;IACnC,KAAA2D,sBAAsB,GAAoB,EAAE,CAAC,CAAC;IAC9C,KAAAC,gBAAgB,GAAa,EAAE,CAAC,CAAC;IACjC,KAAAC,qBAAqB,GAAY,KAAK,CAAC,CAAC;IAUxC,KAAAC,eAAe,GAAW,CAAC,CAAC;IAE5B,KAAAC,uBAAuB,GAAU,CAAC;MAAEzE,KAAK,EAAE,EAAE;MAAEC,KAAK,EAAE;IAAI,CAAE,CAAC;IAIpD,KAAAyE,QAAQ,GAAG,EAAE;IAepB,IAAI,CAACV,aAAa,CAACW,OAAO,EAAE,CAACC,IAAI,CAC/B5F,GAAG,CAAE6F,GAAW,IAAI;MAClB,IAAIA,GAAG,CAACC,MAAM,kDAA4B,CAAC,CAACD,GAAG,CAACE,OAAO,EAAE;QACvD,IAAI,CAACP,eAAe,GAAGK,GAAG,CAACE,OAAO;MACpC;IACF,CAAC,CAAC,CACH,CAACC,SAAS,EAAE;EACf;EAESC,QAAQA,CAAA;IACf,IAAI,CAAChE,kBAAkB,GAAG,IAAI;IAC9B,IAAI,CAACiE,gBAAgB,EAAE;EACzB;EAEAA,gBAAgBA,CAAA;IACd,IAAI,CAACpB,iBAAiB,CAACqB,qCAAqC,CAAC;MAC3DC,IAAI,EAAE;QACJC,YAAY,EAAE,IAAI,CAACC;;KAEtB,CAAC,CAACV,IAAI,CACL5F,GAAG,CAAC6F,GAAG,IAAG;MACR,IAAIA,GAAG,CAACU,OAAO,IAAIV,GAAG,CAACW,UAAU,IAAI,CAAC,EAAE;QACtC,IAAI,CAACC,oBAAoB,GAAGZ,GAAG,CAACU,OAAO,CAACG,GAAG,CAACb,GAAG,IAAG;UAChD,OAAO;YACL/E,cAAc,EAAE+E,GAAG,CAAC/E,cAAc;YAClCoB,GAAG,EAAE2D,GAAG,CAAC3D;WACV;QACH,CAAC,CAAC;QAAE,IAAI,IAAI,CAACsD,eAAe,IAAI,CAAC,CAAC,EAAE;UAClC,IAAImB,KAAK,GAAG,IAAI,CAACF,oBAAoB,CAACG,SAAS,CAAEC,CAAM,IAAKA,CAAC,CAAC3E,GAAG,IAAI,IAAI,CAACsD,eAAe,CAAC;UAC1F,IAAImB,KAAK,IAAI,CAAC,EAAE;YACd,IAAI,CAAC1E,kBAAkB,GAAG,IAAI,CAACwE,oBAAoB,CAACE,KAAK,CAAC;UAC5D,CAAC,MAAM;YACL,IAAI,CAAC1E,kBAAkB,GAAG,IAAI,CAACwE,oBAAoB,CAAC,CAAC,CAAC;UACxD;QACF,CAAC,MAAM;UACL,IAAI,CAACxE,kBAAkB,GAAG,IAAI,CAACwE,oBAAoB,CAAC,CAAC,CAAC;QACxD;QACA,IAAI,IAAI,CAACxE,kBAAkB,IAAI,IAAI,CAACA,kBAAkB,CAACC,GAAG,IAAI,IAAI,CAACD,kBAAkB,CAACC,GAAG,GAAG,CAAC,EAAE;UAC7F,IAAI,CAAC4E,eAAe,EAAE;QACxB;MACF;IACF,CAAC,CAAC,CACH,CAACd,SAAS,EAAE;EACf;EACA;EACA;EACA;EACA;EAEA;EACAe,iBAAiBA,CAAA;IACf;IACA,IAAI,IAAI,CAAC9E,kBAAkB,EAAEC,GAAG,EAAE;MAChC,IAAI,CAAC4E,eAAe,EAAE;IACxB;EACF;EAEA;EACAE,uBAAuBA,CAAA;IACrB,IAAI,CAACzB,qBAAqB,GAAG,CAAC,IAAI,CAACA,qBAAqB;EAC1D;EAEAzD,0BAA0BA,CAACmF,aAA8B;IACvD,IAAI,CAAC5B,sBAAsB,GAAG4B,aAAa;IAC3C,IAAI,CAACvF,kBAAkB,GAAGuF,aAAa,CAACP,GAAG,CAACQ,IAAI,IAAIA,IAAI,CAACC,IAAI,CAAC;IAC9DC,OAAO,CAACC,GAAG,CAAC,SAAS,EAAE,IAAI,CAAC3F,kBAAkB,CAAC;EACjD;EAEAM,eAAeA,CAACsF,QAAkB;IAChC,IAAI,CAAChC,gBAAgB,GAAGgC,QAAQ;IAChCF,OAAO,CAACC,GAAG,CAAC,SAAS,EAAE,IAAI,CAAC/B,gBAAgB,CAAC;EAC/C;EAEA;EACAiC,uBAAuBA,CAAA;IACrB,IAAI,CAAC7F,kBAAkB,GAAG,EAAE;IAC5B,IAAI,CAAC2D,sBAAsB,GAAG,EAAE;IAChC,IAAI,CAACC,gBAAgB,GAAG,EAAE;EAC5B;EAEA;EACAkC,4BAA4BA,CAAA;IAC1B,IAAI,IAAI,CAAC9F,kBAAkB,CAAC8B,MAAM,KAAK,CAAC,EAAE;MACxC,OAAO,OAAO;IAChB;IACA,IAAI,IAAI,CAAC9B,kBAAkB,CAAC8B,MAAM,IAAI,CAAC,EAAE;MACvC,OAAO,IAAI,CAAC9B,kBAAkB,CAAC+F,IAAI,CAAC,GAAG,CAAC;IAC1C;IACA,OAAO,GAAG,IAAI,CAAC/F,kBAAkB,CAACgG,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACD,IAAI,CAAC,GAAG,CAAC,MAAM,IAAI,CAAC/F,kBAAkB,CAAC8B,MAAM,IAAI;EACjG;EAEAsD,eAAeA,CAAA;IACb,IAAI,CAAC/B,gBAAgB,CAAC4C,mCAAmC,CAAC;MACxDvB,IAAI,EAAE;QACJC,YAAY,EAAE,IAAI,CAACpE,kBAAkB,CAACC,GAAG;QACzC0F,SAAS,EAAE,IAAI,CAACxC,iBAAiB;QAAE;QACnCyC,SAAS,EAAE,IAAI,CAACC,SAAS;QACzBC,QAAQ,EAAE,IAAI,CAACrC,QAAQ;QACvBsC,SAAS,EAAE;;KAEd,CAAC,CAACpC,IAAI,CACL5F,GAAG,CAAC6F,GAAG,IAAG;MACR,IAAIA,GAAG,CAACU,OAAO,IAAIV,GAAG,CAACW,UAAU,IAAI,CAAC,EAAE;QACtC,IAAI,CAACtD,SAAS,GAAG2C,GAAG,CAACU,OAAO,CAACrD,SAAS;QACtC,IAAI,CAACX,YAAY,GAAGsD,GAAG,CAACU,OAAO;QAC/B,IAAI,CAAC0B,YAAY,GAAGpC,GAAG,CAACqC,UAAU,GAAGrC,GAAG,CAACqC,UAAU,GAAG,CAAC;MACzD;IACF,CAAC,CAAC,CACH,CAAClC,SAAS,EAAE;EACf;EAEAmC,0BAA0BA,CAAA;IACxB,IAAI,CAACrB,eAAe,EAAE;EACxB;EAEAsB,WAAWA,CAACC,OAAe;IACzB,IAAI,CAACP,SAAS,GAAGO,OAAO;IACxB,IAAI,CAACvB,eAAe,EAAE;EACxB;EAEArC,MAAMA,CAAA;IACJ,IAAI,CAACM,gBAAgB,CAACuD,gCAAgC,CAAC;MACrDlC,IAAI,EAAE;QACJC,YAAY,EAAE,IAAI,CAACpE,kBAAkB,CAACC,GAAG;QACzCqG,OAAO,EAAE,IAAI,CAAChG,YAAY,CAACgG;;KAE9B,CAAC,CAACvC,SAAS,CAACH,GAAG,IAAG;MACjB,IAAIA,GAAG,CAACW,UAAU,IAAI,CAAC,EAAE;QACvB,IAAI,CAAC3B,OAAO,CAAC2D,aAAa,CAAC,MAAM,CAAC;MACpC,CAAC,MAAM;QACL;QACA,IAAI,CAAC3D,OAAO,CAAC4D,YAAY,CAAC,UAAU,CAAC;MACvC;MACA,IAAI,CAAC3B,eAAe,EAAE;IACxB,CAAC,CAAC;EACJ;EAEAtC,MAAMA,CAAA;IACJ,IAAI,CAACO,gBAAgB,CAAC2D,kCAAkC,CAAC;MACvDtC,IAAI,EAAE;QACJuC,YAAY,EAAE,IAAI,CAAC1G,kBAAkB,CAACC,GAAG;QACzCqG,OAAO,EAAE,IAAI,CAAChG,YAAY,CAACgG;;KAE9B,CAAC,CAACvC,SAAS,CAACH,GAAG,IAAG;MACjB,IAAIA,GAAG,CAACW,UAAU,IAAI,CAAC,EAAE;QACvB,IAAI,CAAC3B,OAAO,CAAC2D,aAAa,CAAC,MAAM,CAAC;MACpC,CAAC,MAAM;QACL,IAAI,CAAC3D,OAAO,CAAC4D,YAAY,CAAC5C,GAAG,CAAC+C,OAAQ,CAAC;MACzC;MAAE,IAAI,CAAC9B,eAAe,EAAE;IAC1B,CAAC,CAAC;EACJ;EACAlE,aAAaA,CAAA;IACX;IACA,IAAI,CAAC,IAAI,CAACX,kBAAkB,IAAI,CAAC,IAAI,CAACA,kBAAkB,CAACC,GAAG,IAAI,IAAI,CAACD,kBAAkB,CAACC,GAAG,IAAI,CAAC,EAAE;MAChG,IAAI,CAAC2C,OAAO,CAAC4D,YAAY,CAAC,QAAQ,CAAC;MACnC;IACF;IAEA;IACA,IAAI,CAAC7D,MAAM,CAACiE,QAAQ,CAAC,CAAC,0CAA0C,IAAI,CAAC5G,kBAAkB,CAACC,GAAG,EAAE,CAAC,EAAE;MAC9F4G,WAAW,EAAE;QACXC,SAAS,EAAE,IAAI,CAAC3D;;KAEnB,CAAC;EACJ;;;uCAlNWb,sCAAsC,EAAAjE,EAAA,CAAA0I,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAA5I,EAAA,CAAA0I,iBAAA,CAAAG,EAAA,CAAAC,MAAA,GAAA9I,EAAA,CAAA0I,iBAAA,CAAAK,EAAA,CAAAC,cAAA,GAAAhJ,EAAA,CAAA0I,iBAAA,CAAAO,EAAA,CAAAC,gBAAA,GAAAlJ,EAAA,CAAA0I,iBAAA,CAAAO,EAAA,CAAAE,eAAA,GAAAnJ,EAAA,CAAA0I,iBAAA,CAAAU,EAAA,CAAAC,YAAA;IAAA;EAAA;;;YAAtCpF,sCAAsC;MAAAqF,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAAxJ,EAAA,CAAAyJ,0BAAA,EAAAzJ,EAAA,CAAA0J,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,gDAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCjCjDhK,EADF,CAAAC,cAAA,iBAA0B,qBACR;UACdD,EAAA,CAAAmC,SAAA,qBAAiC;UACnCnC,EAAA,CAAAG,YAAA,EAAiB;UAEfH,EADF,CAAAC,cAAA,mBAAc,YACyB;UACnCD,EAAA,CAAAE,MAAA,qTACF;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAGCH,EAHG,CAAAC,cAAA,aAA8B,aACf,aACqC,eACT;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACxDH,EAAA,CAAAC,cAAA,oBACkD;UADtBD,EAAA,CAAAY,gBAAA,2BAAAsJ,oFAAApJ,MAAA;YAAAd,EAAA,CAAAmB,kBAAA,CAAA8I,GAAA,CAAAtI,kBAAA,EAAAb,MAAA,MAAAmJ,GAAA,CAAAtI,kBAAA,GAAAb,MAAA;YAAA,OAAAA,MAAA;UAAA,EAAgC;UAC1Dd,EAAA,CAAAsB,UAAA,4BAAA6I,qFAAA;YAAA,OAAkBF,GAAA,CAAApC,0BAAA,EAA4B;UAAA,EAAC;UAC/C7H,EAAA,CAAAwC,UAAA,KAAA4H,4DAAA,uBAAoE;UAK1EpK,EAFI,CAAAG,YAAA,EAAY,EACR,EACF;UAGFH,EAFJ,CAAAC,cAAA,cAAsB,cACqC,gBACZ;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACrDH,EAAA,CAAAC,cAAA,oBACyC;UADXD,EAAA,CAAAY,gBAAA,2BAAAyJ,oFAAAvJ,MAAA;YAAAd,EAAA,CAAAmB,kBAAA,CAAA8I,GAAA,CAAAnF,iBAAA,EAAAhE,MAAA,MAAAmJ,GAAA,CAAAnF,iBAAA,GAAAhE,MAAA;YAAA,OAAAA,MAAA;UAAA,EAA+B;UAC3Dd,EAAA,CAAAsB,UAAA,4BAAAgJ,qFAAA;YAAA,OAAkBL,GAAA,CAAAxD,iBAAA,EAAmB;UAAA,EAAC;UACtCzG,EAAA,CAAAwC,UAAA,KAAA+H,4DAAA,uBAA0E;UAKhFvK,EAFI,CAAAG,YAAA,EAAY,EACR,EACF;UAGFH,EAFJ,CAAAC,cAAA,cAAsB,cACqC,iBACX;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAEpDH,EADF,CAAAC,cAAA,eAAqC,kBAGW;UADtCD,EAAA,CAAAsB,UAAA,mBAAAkJ,yEAAA;YAAA,OAASP,GAAA,CAAAvD,uBAAA,EAAyB;UAAA,EAAC;UAEzC1G,EAAA,CAAAC,cAAA,gBAA4B;UAAAD,EAAA,CAAAE,MAAA,IAAoC;UAAAF,EAAA,CAAAG,YAAA,EAAO;UACvEH,EAAA,CAAAmC,SAAA,aAAmC;UACrCnC,EAAA,CAAAG,YAAA,EAAS;UACTH,EAAA,CAAAwC,UAAA,KAAAiI,sDAAA,kBAA+F;UAerGzK,EAFI,CAAAG,YAAA,EAAM,EACF,EACF;UAEJH,EADF,CAAAC,cAAA,cAAsB,eAC2B;UAiB7CD,EAdA,CAAAwC,UAAA,KAAAkI,+DAAA,2BACyI,KAAAC,+DAAA,2BAcD;UAQ9I3K,EAFI,CAAAG,YAAA,EAAM,EACF,EACF;UAAIH,EAAA,CAAAC,cAAA,eAAmC;UAE3CD,EAAA,CAAAwC,UAAA,KAAAoI,sDAAA,kBAAyE;UAanE5K,EAHN,CAAAC,cAAA,iBAA6E,aACpE,cACgD,cACrB;UAAAD,EAAA,CAAAE,MAAA,yDAAS;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAC5CH,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAE,MAAA,gCAAI;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACvCH,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAE,MAAA,4CAAM;UAExCF,EAFwC,CAAAG,YAAA,EAAK,EACtC,EACC;UACRH,EAAA,CAAAC,cAAA,aAAO;UACLD,EAAA,CAAAwC,UAAA,KAAAqI,qDAAA,iBAAmD;UA0B3D7K,EAHM,CAAAG,YAAA,EAAQ,EACF,EACJ,EACO;UAEbH,EADF,CAAAC,cAAA,0BAAsD,0BAES;UAD7CD,EAAA,CAAAY,gBAAA,wBAAAkK,sFAAAhK,MAAA;YAAAd,EAAA,CAAAmB,kBAAA,CAAA8I,GAAA,CAAAzC,SAAA,EAAA1G,MAAA,MAAAmJ,GAAA,CAAAzC,SAAA,GAAA1G,MAAA;YAAA,OAAAA,MAAA;UAAA,EAAoB;UAClCd,EAAA,CAAAsB,UAAA,wBAAAwJ,sFAAAhK,MAAA;YAAA,OAAcmJ,GAAA,CAAAnC,WAAA,CAAAhH,MAAA,CAAmB;UAAA,EAAC;UAGxCd,EAFI,CAAAG,YAAA,EAAiB,EACF,EACT;;;UA7H4BH,EAAA,CAAAM,SAAA,IAAgC;UAAhCN,EAAA,CAAA6B,gBAAA,YAAAoI,GAAA,CAAAtI,kBAAA,CAAgC;UAE9B3B,EAAA,CAAAM,SAAA,EAAuB;UAAvBN,EAAA,CAAAI,UAAA,YAAA6J,GAAA,CAAA9D,oBAAA,CAAuB;UASvBnG,EAAA,CAAAM,SAAA,GAA+B;UAA/BN,EAAA,CAAA6B,gBAAA,YAAAoI,GAAA,CAAAnF,iBAAA,CAA+B;UAE7B9E,EAAA,CAAAM,SAAA,EAAmB;UAAnBN,EAAA,CAAAI,UAAA,YAAA6J,GAAA,CAAAtF,gBAAA,CAAmB;UAYzC3E,EAAA,CAAAM,SAAA,GAAqC;UAArCN,EAAA,CAAAI,UAAA,eAAA6J,GAAA,CAAAtI,kBAAA,kBAAAsI,GAAA,CAAAtI,kBAAA,CAAAC,GAAA,EAAqC;UACf5B,EAAA,CAAAM,SAAA,GAAoC;UAApCN,EAAA,CAAA+D,iBAAA,CAAAkG,GAAA,CAAA/C,4BAAA,GAAoC;UAGAlH,EAAA,CAAAM,SAAA,GAA2B;UAA3BN,EAAA,CAAAI,UAAA,SAAA6J,GAAA,CAAAhF,qBAAA,CAA2B;UAqB5FjF,EAAA,CAAAM,SAAA,GAAoI;UAApIN,EAAA,CAAAI,UAAA,SAAA6J,GAAA,CAAArH,SAAA,IAAAqH,GAAA,CAAArH,SAAA,CAAAM,MAAA,QAAA+G,GAAA,CAAAhI,YAAA,KAAAgI,GAAA,CAAAhI,YAAA,CAAAC,OAAA,IAAA+H,GAAA,CAAAtI,kBAAA,IAAAsI,GAAA,CAAAtI,kBAAA,CAAAC,GAAA,KAAoI;UAcpI5B,EAAA,CAAAM,SAAA,EAAmI;UAAnIN,EAAA,CAAAI,UAAA,SAAA6J,GAAA,CAAArH,SAAA,IAAAqH,GAAA,CAAArH,SAAA,CAAAM,MAAA,QAAA+G,GAAA,CAAAhI,YAAA,IAAAgI,GAAA,CAAAhI,YAAA,CAAAC,OAAA,IAAA+H,GAAA,CAAAtI,kBAAA,IAAAsI,GAAA,CAAAtI,kBAAA,CAAAC,GAAA,KAAmI;UAUtG5B,EAAA,CAAAM,SAAA,GAAmC;UAAnCN,EAAA,CAAAI,UAAA,SAAA6J,GAAA,CAAA7I,kBAAA,CAAA8B,MAAA,KAAmC;UAmB9ClD,EAAA,CAAAM,SAAA,IAAe;UAAfN,EAAA,CAAAI,UAAA,YAAA6J,GAAA,CAAArH,SAAA,CAAe;UA4B1B5C,EAAA,CAAAM,SAAA,GAAoB;UAApBN,EAAA,CAAA6B,gBAAA,SAAAoI,GAAA,CAAAzC,SAAA,CAAoB;UAAuBxH,EAAtB,CAAAI,UAAA,aAAA6J,GAAA,CAAA7E,QAAA,CAAqB,mBAAA6E,GAAA,CAAAtC,YAAA,CAAgC;;;qBDpGlFlI,YAAY,EAAAsL,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EAAEtL,YAAY,EAAAuL,EAAA,CAAAC,eAAA,EAAAD,EAAA,CAAAE,OAAA,EAAAC,EAAA,CAAAC,eAAA,EAAAD,EAAA,CAAAE,mBAAA,EAAAF,EAAA,CAAAG,qBAAA,EAAAH,EAAA,CAAAI,qBAAA,EAAAJ,EAAA,CAAAK,iBAAA,EAAAL,EAAA,CAAAM,iBAAA,EAAAC,EAAA,CAAAC,aAAA,EAAAC,GAAA,CAAAC,mBAAA,EAAEhM,eAAe,EAAAiM,GAAA,CAAAC,yBAAA;MAAAC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}