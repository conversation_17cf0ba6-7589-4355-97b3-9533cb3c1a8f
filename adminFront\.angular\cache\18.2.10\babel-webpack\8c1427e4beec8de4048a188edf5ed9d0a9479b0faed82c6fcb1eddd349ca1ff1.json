{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/Documents/salechange-product/adminFront/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { __decorate } from \"tslib\";\nimport { Component, ViewChild } from '@angular/core';\nimport { SharedModule } from '../components/shared.module';\nimport { CommonModule } from '@angular/common';\nimport { NbDatepickerModule } from '@nebular/theme';\nimport { BaseComponent } from '../components/base/baseComponent';\nimport { concatMap, tap } from 'rxjs';\nimport { NbDateFnsDateModule } from '@nebular/date-fns';\nimport * as moment from 'moment';\nimport { EnumHouseProgress } from 'src/app/shared/enum/enumHouseProgress';\nimport { EnumHouseType } from 'src/app/shared/enum/enumHouseType';\nimport { EnumPayStatus } from 'src/app/shared/enum/enumPayStatus';\nimport { EnumSignStatus } from 'src/app/shared/enum/enumSignStatus';\nimport { LocalStorageService } from 'src/app/shared/services/local-storage.service';\nimport { STORAGE_KEY } from 'src/app/shared/constant/constant';\nlet HouseholdManagementComponent = class HouseholdManagementComponent extends BaseComponent {\n  constructor(_allow, enumHelper, dialogService, message, valid, _houseService, _houseHoldMainService, _buildCaseService, pettern, router, _eventService, _ultilityService, quotationService) {\n    super(_allow);\n    this._allow = _allow;\n    this.enumHelper = enumHelper;\n    this.dialogService = dialogService;\n    this.message = message;\n    this.valid = valid;\n    this._houseService = _houseService;\n    this._houseHoldMainService = _houseHoldMainService;\n    this._buildCaseService = _buildCaseService;\n    this.pettern = pettern;\n    this.router = router;\n    this._eventService = _eventService;\n    this._ultilityService = _ultilityService;\n    this.quotationService = quotationService;\n    this.tempBuildCaseID = -1;\n    this.pageFirst = 1;\n    this.pageSize = 10;\n    this.pageIndex = 1;\n    this.totalRecords = 0;\n    this.statusOptions = [{\n      value: 0,\n      key: 'allow',\n      label: '允許'\n    }, {\n      value: 1,\n      key: 'not allowed',\n      label: '不允許'\n    }];\n    this.cIsEnableOptions = [{\n      value: null,\n      key: 'all',\n      label: '全部'\n    }, {\n      value: true,\n      key: 'enable',\n      label: '啟用'\n    }, {\n      value: false,\n      key: 'deactivate',\n      label: '停用'\n    }];\n    this.buildCaseOptions = [{\n      label: '全部',\n      value: ''\n    }];\n    this.houseHoldOptions = [{\n      label: '全部',\n      value: ''\n    }];\n    this.progressOptions = [{\n      label: '全部',\n      value: -1\n    }];\n    this.houseTypeOptions = [{\n      label: '全部',\n      value: -1\n    }];\n    this.payStatusOptions = [{\n      label: '全部',\n      value: -1\n    }];\n    this.signStatusOptions = [{\n      label: '全部',\n      value: -1\n    }];\n    this.options = {\n      progressOptions: this.enumHelper.getEnumOptions(EnumHouseProgress),\n      payStatusOptions: this.enumHelper.getEnumOptions(EnumPayStatus),\n      houseTypeOptions: this.enumHelper.getEnumOptions(EnumHouseType)\n    };\n    this.initDetail = {\n      CHouseID: 0,\n      CMail: \"\",\n      CIsChange: false,\n      CPayStatus: 0,\n      CIsEnable: false,\n      CCustomerName: \"\",\n      CNationalID: \"\",\n      CProgress: \"\",\n      CHouseType: 0,\n      CHouseHold: \"\",\n      CPhone: \"\"\n    };\n    this.quotationItems = [];\n    this.totalAmount = 0;\n    this.currentHouse = null;\n    this.selectedFile = null;\n    this.buildingSelectedOptions = [{\n      value: '',\n      label: '全部'\n    }];\n    this._eventService.receive().pipe(tap(res => {\n      if (res.action == \"GET_BUILDCASE\" /* EEvent.GET_BUILDCASE */ && !!res.payload) {\n        this.tempBuildCaseID = res.payload;\n      }\n    })).subscribe();\n  }\n  ngOnInit() {\n    this.progressOptions = [...this.progressOptions, ...this.enumHelper.getEnumOptions(EnumHouseProgress)];\n    this.houseTypeOptions = [...this.houseTypeOptions, ...this.enumHelper.getEnumOptions(EnumHouseType)];\n    this.payStatusOptions = [...this.payStatusOptions, ...this.enumHelper.getEnumOptions(EnumPayStatus)];\n    this.signStatusOptions = [...this.signStatusOptions, ...this.enumHelper.getEnumOptions(EnumSignStatus)];\n    if (LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH) != null && LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH) != undefined && LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH) != \"\") {\n      let previous_search = JSON.parse(LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH));\n      this.searchQuery = {\n        CBuildCaseSelected: null,\n        // CBuildingNameSelected: previous_search.CBuildingNameSelected != null && previous_search.CBuildingNameSelected != undefined\n        //   ? this.buildingSelectedOptions.find(x => x.value == previous_search.CBuildingNameSelected.value)\n        //   : this.buildingSelectedOptions[0],\n        CHouseHoldSelected: previous_search.CHouseHoldSelected != null && previous_search.CHouseHoldSelected != undefined ? this.houseHoldOptions.find(x => x.value == previous_search.CHouseHoldSelected.value) : this.houseHoldOptions[0],\n        CHouseTypeSelected: previous_search.CHouseTypeSelected != null && previous_search.CHouseTypeSelected != undefined ? this.houseTypeOptions.find(x => x.value == previous_search.CHouseTypeSelected.value) : this.houseTypeOptions[0],\n        CPayStatusSelected: previous_search.CPayStatusSelected != null && previous_search.CPayStatusSelected != undefined ? this.payStatusOptions.find(x => x.value == previous_search.CPayStatusSelected.value) : this.payStatusOptions[0],\n        CProgressSelected: previous_search.CProgressSelected != null && previous_search.CProgressSelected != undefined ? this.progressOptions.find(x => x.value == previous_search.CProgressSelected.value) : this.progressOptions[0],\n        CSignStatusSelected: previous_search.CSignStatusSelected != null && previous_search.CSignStatusSelected != undefined ? this.signStatusOptions.find(x => x.value == previous_search.CSignStatusSelected.value) : this.signStatusOptions[0],\n        CIsEnableSeleted: previous_search.CIsEnableSeleted != null && previous_search.CIsEnableSeleted != undefined ? this.cIsEnableOptions.find(x => x.value == previous_search.CIsEnableSeleted.value) : this.cIsEnableOptions[0],\n        CFrom: previous_search.CFrom != null && previous_search.CFrom != undefined ? previous_search.CFrom : '',\n        CTo: previous_search.CTo != null && previous_search.CTo != undefined ? previous_search.CTo : ''\n      };\n    } else {\n      this.searchQuery = {\n        CBuildCaseSelected: null,\n        CBuildingNameSelected: this.buildingSelectedOptions[0],\n        CHouseHoldSelected: this.houseHoldOptions[0],\n        CHouseTypeSelected: this.houseTypeOptions[0],\n        CPayStatusSelected: this.payStatusOptions[0],\n        CProgressSelected: this.progressOptions[0],\n        CSignStatusSelected: this.signStatusOptions[0],\n        CIsEnableSeleted: this.cIsEnableOptions[0],\n        CFrom: '',\n        CTo: ''\n      };\n    }\n    this.getListBuildCase();\n  }\n  onSearch() {\n    let sessionSave = {\n      CBuildCaseSelected: this.searchQuery.CBuildCaseSelected,\n      // CBuildingNameSelected: this.searchQuery.CBuildingNameSelected,\n      CFrom: this.searchQuery.CFrom,\n      CTo: this.searchQuery.CTo,\n      CHouseHoldSelected: this.searchQuery.CHouseHoldSelected,\n      CHouseTypeSelected: this.searchQuery.CHouseTypeSelected,\n      CIsEnableSeleted: this.searchQuery.CIsEnableSeleted,\n      CPayStatusSelected: this.searchQuery.CPayStatusSelected,\n      CProgressSelected: this.searchQuery.CProgressSelected,\n      CSignStatusSelected: this.searchQuery.CSignStatusSelected\n    };\n    LocalStorageService.AddSessionStorage(STORAGE_KEY.HOUSE_SEARCH, JSON.stringify(sessionSave));\n    this.getHouseList().subscribe();\n  }\n  pageChanged(newPage) {\n    this.pageIndex = newPage;\n    this.getHouseList().subscribe();\n  }\n  exportHouse() {\n    if (this.searchQuery.CBuildCaseSelected.cID) {\n      this._houseService.apiHouseExportHousePost$Json({\n        CBuildCaseID: this.searchQuery.CBuildCaseSelected.cID\n      }).subscribe(res => {\n        if (res.Entries && res.StatusCode == 0) {\n          this._ultilityService.downloadExcelFile(res.Entries, '戶別資訊範本');\n        } else {\n          this.message.showErrorMSG(res.Message);\n        }\n      });\n    }\n  }\n  triggerFileInput() {\n    this.fileInput.nativeElement.click();\n  }\n  onFileSelected(event) {\n    const input = event.target;\n    if (input.files && input.files.length > 0) {\n      this.selectedFile = input.files[0];\n      this.importExcel();\n    }\n  }\n  importExcel() {\n    if (this.selectedFile) {\n      const formData = new FormData();\n      formData.append('CFile', this.selectedFile);\n      this._houseService.apiHouseImportHousePost$Json({\n        body: {\n          CBuildCaseID: this.searchQuery.CBuildCaseSelected.cID,\n          CFile: this.selectedFile\n        }\n      }).subscribe(res => {\n        if (res.StatusCode === 0) {\n          this.message.showSucessMSG(res.Message);\n          this.getHouseList().subscribe();\n        } else {\n          this.message.showErrorMSG(res.Message);\n        }\n      });\n    }\n  }\n  getListHouseHold() {\n    this._houseService.apiHouseGetListHouseHoldPost$Json({\n      body: {\n        CBuildCaseID: this.searchQuery.CBuildCaseSelected.cID\n      }\n    }).subscribe(res => {\n      if (res.Entries && res.StatusCode == 0) {\n        this.houseHoldOptions = [{\n          value: '',\n          label: '全部'\n        }, ...res.Entries.map(e => {\n          return {\n            value: e,\n            label: e\n          };\n        })];\n        if (LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH) != null && LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH) != undefined && LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH) != \"\") {\n          let previous_search = JSON.parse(LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH));\n          if (previous_search.CHouseHoldSelected != null && previous_search.CHouseHoldSelected != undefined) {\n            let index = this.houseHoldOptions.findIndex(x => x.value == previous_search.CHouseHoldSelected.value);\n            this.searchQuery.CHouseHoldSelected = this.houseHoldOptions[index];\n          } else {\n            this.searchQuery.CHouseHoldSelected = this.houseHoldOptions[0];\n          }\n        }\n      }\n    });\n  }\n  formatQuery() {\n    this.bodyRequest = {\n      CBuildCaseID: this.searchQuery.CBuildCaseSelected.cID,\n      PageIndex: this.pageIndex,\n      PageSize: this.pageSize\n    };\n    // if (this.searchQuery.CBuildingNameSelected) {\n    //   this.bodyRequest['CBuildingName'] = this.searchQuery.CBuildingNameSelected.value\n    // }\n    if (this.searchQuery.CFrom && this.searchQuery.CTo) {\n      this.bodyRequest['CFloor'] = {\n        CFrom: this.searchQuery.CFrom,\n        CTo: this.searchQuery.CTo\n      };\n    }\n    if (this.searchQuery.CHouseHoldSelected) {\n      this.bodyRequest['CHouseHold'] = this.searchQuery.CHouseHoldSelected.value;\n    }\n    if (this.searchQuery.CHouseTypeSelected.value) {\n      this.bodyRequest['CHouseType'] = this.searchQuery.CHouseTypeSelected.value;\n    }\n    if (typeof this.searchQuery.CIsEnableSeleted.value === \"boolean\") {\n      this.bodyRequest['CIsEnable'] = this.searchQuery.CIsEnableSeleted.value;\n    }\n    if (this.searchQuery.CPayStatusSelected.value) {\n      this.bodyRequest['CPayStatus'] = this.searchQuery.CPayStatusSelected.value;\n    }\n    if (this.searchQuery.CProgressSelected.value) {\n      this.bodyRequest['CProgress'] = this.searchQuery.CProgressSelected.value;\n    }\n    if (this.searchQuery.CSignStatusSelected.value) {\n      this.bodyRequest['CSignStatus'] = this.searchQuery.CSignStatusSelected.value;\n    }\n    return this.bodyRequest;\n  }\n  sortByFloorDescending(arr) {\n    return arr.sort((a, b) => (b.CFloor ?? 0) - (a.CFloor ?? 0));\n  }\n  getHouseList() {\n    return this._houseService.apiHouseGetHouseListPost$Json({\n      body: this.formatQuery()\n    }).pipe(tap(res => {\n      if (res.Entries && res.StatusCode == 0) {\n        this.houseList = res.Entries;\n        this.totalRecords = res.TotalItems;\n      }\n    }));\n  }\n  onSelectionChangeBuildCase() {\n    // this.getListBuilding()\n    this.getListHouseHold();\n    this.getHouseList().subscribe();\n  }\n  getListBuildCase() {\n    this._buildCaseService.apiBuildCaseGetAllBuildCaseForSelectPost$Json({\n      body: {\n        CIsPagi: false,\n        CStatus: 1\n      }\n    }).pipe(tap(res => {\n      if (res.Entries && res.StatusCode == 0) {\n        this.userBuildCaseOptions = res.Entries?.length ? res.Entries.map(res => {\n          return {\n            CBuildCaseName: res.CBuildCaseName,\n            cID: res.cID\n          };\n        }) : [];\n        if (LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH) != null && LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH) != undefined && LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH) != \"\") {\n          let previous_search = JSON.parse(LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH));\n          if (previous_search.CBuildCaseSelected != null && previous_search.CBuildCaseSelected != undefined) {\n            let index = this.userBuildCaseOptions.findIndex(x => x.cID == previous_search.CBuildCaseSelected.cID);\n            this.searchQuery.CBuildCaseSelected = this.userBuildCaseOptions[index];\n          } else {\n            this.searchQuery.CBuildCaseSelected = this.userBuildCaseOptions[0];\n          }\n        } else {\n          this.searchQuery.CBuildCaseSelected = this.userBuildCaseOptions[0];\n        }\n      }\n    }), tap(() => {\n      // this.getListBuilding()\n      this.getListHouseHold();\n      setTimeout(() => {\n        this.getHouseList().subscribe();\n      }, 500);\n    })).subscribe();\n  }\n  getHouseById(CID, ref) {\n    this.detailSelected = {};\n    this._houseService.apiHouseGetHouseByIdPost$Json({\n      body: {\n        CHouseID: CID\n      }\n    }).subscribe(res => {\n      if (res.Entries && res.StatusCode == 0) {\n        this.houseDetail = {\n          ...res.Entries,\n          changeStartDate: res.Entries.CChangeStartDate ? new Date(res.Entries.CChangeStartDate) : undefined,\n          changeEndDate: res.Entries.CChangeEndDate ? new Date(res.Entries.CChangeEndDate) : undefined\n        };\n        if (res.Entries.CBuildCaseId) {\n          this.detailSelected.CBuildCaseSelected = this.findItemInArray(this.userBuildCaseOptions, 'cID', res.Entries.CBuildCaseId);\n        }\n        this.detailSelected.CPayStatusSelected = this.findItemInArray(this.options.payStatusOptions, 'value', res.Entries.CPayStatus);\n        if (res.Entries.CHouseType) {\n          this.detailSelected.CHouseTypeSelected = this.findItemInArray(this.options.houseTypeOptions, 'value', res.Entries.CHouseType);\n        } else {\n          this.detailSelected.CHouseTypeSelected = this.options.houseTypeOptions[1];\n        }\n        this.detailSelected.CProgressSelected = this.findItemInArray(this.options.progressOptions, 'value', res.Entries.CProgress);\n        if (res.Entries.CBuildCaseId) {\n          if (this.houseHoldMain) {\n            this.houseHoldMain.CBuildCaseID = res.Entries.CBuildCaseId;\n          }\n        }\n        this.dialogService.open(ref);\n      }\n    });\n  }\n  findItemInArray(array, key, value) {\n    return array.find(item => item[key] === value);\n  }\n  openModelDetail(ref, item) {\n    this.getHouseById(item.CID, ref);\n  }\n  openModel(ref) {\n    this.houseHoldMain = {\n      CBuildingName: '',\n      CFloor: undefined,\n      CHouseHoldCount: undefined\n    };\n    this.dialogService.open(ref);\n  }\n  formatDate(CChangeDate) {\n    if (CChangeDate) {\n      return moment(CChangeDate).format('YYYY-MM-DDTHH:mm:ss');\n    }\n    return '';\n  }\n  onSubmitDetail(ref) {\n    this.houseDetail.CChangeStartDate = this.houseDetail.changeStartDate ? this.formatDate(this.houseDetail.changeStartDate) : '', this.houseDetail.CChangeEndDate = this.houseDetail.changeEndDate ? this.formatDate(this.houseDetail.changeEndDate) : '', this.editHouseArgsParam = {\n      CCustomerName: this.houseDetail.CCustomerName,\n      CHouseHold: this.houseDetail.CHousehold,\n      CHouseID: this.houseDetail.CId,\n      CHouseType: this.detailSelected.CHouseTypeSelected ? this.detailSelected.CHouseTypeSelected.value : null,\n      CIsChange: this.houseDetail.CIsChange,\n      CIsEnable: this.houseDetail.CIsEnable,\n      CMail: this.houseDetail.CMail,\n      CNationalID: this.houseDetail.CNationalId,\n      CPayStatus: this.detailSelected.CPayStatusSelected ? this.detailSelected.CPayStatusSelected.value : null,\n      CPhone: this.houseDetail.CPhone,\n      CProgress: this.detailSelected.CProgressSelected ? this.detailSelected.CProgressSelected.value : null,\n      CChangeStartDate: this.houseDetail.CChangeStartDate,\n      CChangeEndDate: this.houseDetail.CChangeEndDate\n    };\n    this.validation();\n    if (this.valid.errorMessages.length > 0) {\n      this.message.showErrorMSGs(this.valid.errorMessages);\n      return;\n    }\n    this._houseService.apiHouseEditHousePost$Json({\n      body: this.editHouseArgsParam\n    }).pipe(tap(res => {\n      if (res.StatusCode === 0) {\n        this.message.showSucessMSG(\"執行成功\");\n        ref.close();\n      } else {\n        this.message.showErrorMSG(res.Message);\n        ref.close();\n      }\n    }), concatMap(() => this.getHouseList())).subscribe();\n  }\n  onSubmit(ref) {\n    let bodyReq = {\n      CCustomerName: this.houseDetail.CCustomerName,\n      CHouseHold: this.houseDetail.CHousehold,\n      CHouseID: this.houseDetail.CId,\n      CHouseType: this.houseDetail.CHouseType,\n      CIsChange: this.houseDetail.CIsChange,\n      CIsEnable: this.houseDetail.CIsEnable,\n      CMail: this.houseDetail.CMail,\n      CNationalID: this.houseDetail.CNationalId,\n      CPayStatus: this.houseDetail.CPayStatus,\n      CPhone: this.houseDetail.CPhone,\n      CProgress: this.houseDetail.CProgress\n    };\n    this._houseService.apiHouseEditHousePost$Json({\n      body: bodyReq\n    }).subscribe(res => {\n      if (res.StatusCode === 0) {\n        this.message.showSucessMSG(\"執行成功\");\n        ref.close();\n      }\n    });\n  }\n  onClose(ref) {\n    ref.close();\n  }\n  onNavidateId(type, id) {\n    const idURL = id ? id : this.searchQuery.CBuildCaseSelected.cID;\n    this.router.navigate([`/pages/household-management/${type}`, idURL]);\n  }\n  onNavidateBuildCaseIdHouseId(type, buildCaseId, houseId) {\n    this.router.navigate([`/pages/household-management/${type}`, buildCaseId, houseId]);\n  }\n  resetSecureKey(item) {\n    if (confirm(\"您想重設密碼嗎？\")) {\n      this._houseService.apiHouseResetHouseSecureKeyPost$Json({\n        body: item.CID\n      }).subscribe(res => {\n        if (res.StatusCode == 0) {\n          this.message.showSucessMSG(\"執行成功\");\n        }\n      });\n    }\n  }\n  validation() {\n    this.valid.clear();\n    this.valid.required('[建案名稱]', this.houseDetail.CId);\n    this.valid.required('[戶型名稱]', this.editHouseArgsParam.CHouseHold);\n    this.valid.isStringMaxLength('[棟別]', this.editHouseArgsParam.CHouseHold, 50);\n    this.valid.required('[樓層]', this.houseDetail.CFloor);\n    this.valid.isStringMaxLength('[客戶姓名]', this.editHouseArgsParam.CCustomerName, 50);\n    // if (this.editHouseArgsParam.CNationalID) {\n    //   this.valid.CheckTaiwanID(this.editHouseArgsParam.CNationalID)\n    // }\n    this.valid.pattern('[電子郵件]', this.editHouseArgsParam.CMail, this.pettern.MailPettern);\n    this.valid.isPhoneNumber('[聯絡電話]', this.editHouseArgsParam.CPhone);\n    this.valid.required('[進度]', this.editHouseArgsParam.CProgress);\n    this.valid.required('[戶別類型]', this.detailSelected.CHouseTypeSelected.value);\n    this.valid.required('[付款狀態]', this.detailSelected.CPayStatusSelected.value);\n    if (this.houseDetail.CChangeStartDate) {\n      this.valid.required('[客變結束日期]', this.houseDetail.CChangeEndDate);\n    }\n    if (this.houseDetail.CChangeEndDate) {\n      this.valid.required('[客變開始日期]', this.houseDetail.CChangeStartDate);\n    }\n    this.valid.checkStartBeforeEnd('[開放日期]', this.houseDetail.CChangeStartDate ? this.houseDetail.CChangeStartDate : '', this.houseDetail.CChangeEndDate ? this.houseDetail.CChangeEndDate : '');\n  }\n  validationHouseHoldMain() {\n    this.valid.clear();\n    this.valid.required('[建案]', this.houseHoldMain.CBuildCaseID);\n    this.valid.required('[棟別]', this.houseHoldMain.CBuildingName);\n    this.valid.isStringMaxLength('[棟別]', this.houseHoldMain.CBuildingName, 10);\n    this.valid.isNaturalNumberInRange('[當層最多戶數]', this.houseHoldMain.CFloor, 1, 100);\n    this.valid.isNaturalNumberInRange('[本棟總樓層]', this.houseHoldMain.CHouseHoldCount, 1, 100);\n  }\n  addHouseHoldMain(ref) {\n    this.houseHoldMain.CBuildCaseID = this.searchQuery.CBuildCaseSelected.cID, this.validationHouseHoldMain();\n    if (this.valid.errorMessages.length > 0) {\n      this.message.showErrorMSGs(this.valid.errorMessages);\n      return;\n    }\n    this._houseHoldMainService.apiHouseHoldMainAddHouseHoldMainPost$Json({\n      body: this.houseHoldMain\n    }).pipe(tap(res => {\n      if (res.StatusCode === 0) {\n        this.message.showSucessMSG(\"執行成功\");\n        ref.close();\n      }\n    }), concatMap(() => this.getHouseList())).subscribe();\n  }\n  // 開啟報價單對話框\n  openQuotation(dialog, item) {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      _this.currentHouse = item;\n      _this.quotationItems = [];\n      _this.totalAmount = 0;\n      // 載入現有報價資料\n      try {\n        const response = yield _this.quotationService.getQuotationByHouseId(item.CID).toPromise();\n        if (response?.success && response.data) {\n          _this.quotationItems = response.data;\n          _this.calculateTotal();\n        }\n      } catch (error) {\n        console.error('載入報價資料失敗:', error);\n      }\n      _this.dialogService.open(dialog, {\n        context: item,\n        closeOnBackdropClick: false\n      });\n    })();\n  }\n  // 新增自定義報價項目\n  addQuotationItem() {\n    this.quotationItems.push({\n      cHouseID: this.currentHouse?.CID || 0,\n      cItemName: '',\n      cUnitPrice: 0,\n      cCount: 1,\n      cStatus: 1,\n      cVersion: 1,\n      cIsDefault: false\n    });\n  }\n  // 載入預設項目\n  loadDefaultItems() {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      try {\n        const response = yield _this2.quotationService.getDefaultQuotationItems().toPromise();\n        if (response?.success && response.data) {\n          const defaultItems = response.data.map(item => ({\n            ...item,\n            cHouseID: _this2.currentHouse?.CID || 0,\n            cCount: 1\n          }));\n          _this2.quotationItems.push(...defaultItems);\n          _this2.calculateTotal();\n        }\n      } catch (error) {\n        // this.toastrService.error('載入預設項目失敗');\n      }\n    })();\n  }\n  // 移除報價項目\n  removeQuotationItem(index) {\n    const item = this.quotationItems[index];\n    if (item.cIsDefault) {\n      // this.toastrService.warning('預設項目無法刪除');\n      return;\n    }\n    this.quotationItems.splice(index, 1);\n    this.calculateTotal();\n  }\n  // 計算總金額\n  calculateTotal() {\n    this.totalAmount = this.quotationItems.reduce((sum, item) => {\n      return sum + item.cUnitPrice * item.cCount;\n    }, 0);\n  }\n  // 格式化金額\n  formatCurrency(amount) {\n    return new Intl.NumberFormat('zh-TW', {\n      style: 'currency',\n      currency: 'TWD',\n      minimumFractionDigits: 0\n    }).format(amount);\n  }\n  // 儲存報價單\n  saveQuotation(ref) {\n    var _this3 = this;\n    return _asyncToGenerator(function* () {\n      if (_this3.quotationItems.length === 0) {\n        _this3.toastrService.warning('請先新增報價項目');\n        return;\n      }\n      // 驗證必填欄位\n      const invalidItems = _this3.quotationItems.filter(item => !item.cItemName.trim() || item.cUnitPrice < 0 || item.cCount < 1);\n      if (invalidItems.length > 0) {\n        _this3.toastrService.warning('請確認所有項目名稱、單價及數量都已正確填寫');\n        return;\n      }\n      try {\n        const request = {\n          houseId: _this3.currentHouse.CID,\n          items: _this3.quotationItems\n        };\n        const response = yield _this3.quotationService.saveQuotation(request).toPromise();\n        if (response?.success) {\n          _this3.toastrService.success('報價單儲存成功');\n          ref.close();\n        } else {\n          _this3.toastrService.error(response?.message || '儲存失敗');\n        }\n      } catch (error) {\n        _this3.toastrService.error('報價單儲存失敗');\n      }\n    })();\n  }\n  // 匯出報價單\n  exportQuotation() {\n    var _this4 = this;\n    return _asyncToGenerator(function* () {\n      try {\n        const blob = yield _this4.quotationService.exportQuotation(_this4.currentHouse.CID).toPromise();\n        const url = window.URL.createObjectURL(blob);\n        const link = document.createElement('a');\n        link.href = url;\n        link.download = `報價單_${_this4.currentHouse.CHouseHold}_${_this4.currentHouse.CFloor}樓.pdf`;\n        link.click();\n        window.URL.revokeObjectURL(url);\n      } catch (error) {\n        _this4.toastrService.error('匯出報價單失敗');\n      }\n    })();\n  }\n};\n__decorate([ViewChild('fileInput')], HouseholdManagementComponent.prototype, \"fileInput\", void 0);\nHouseholdManagementComponent = __decorate([Component({\n  selector: 'ngx-household-management',\n  templateUrl: './household-management.component.html',\n  styleUrls: ['./household-management.component.scss'],\n  standalone: true,\n  imports: [CommonModule, SharedModule, NbDatepickerModule, NbDateFnsDateModule]\n})], HouseholdManagementComponent);\nexport { HouseholdManagementComponent };", "map": {"version": 3, "names": ["Component", "ViewChild", "SharedModule", "CommonModule", "NbDatepickerModule", "BaseComponent", "concatMap", "tap", "NbDateFnsDateModule", "moment", "EnumHouseProgress", "EnumHouseType", "EnumPayStatus", "EnumSignStatus", "LocalStorageService", "STORAGE_KEY", "HouseholdManagementComponent", "constructor", "_allow", "enum<PERSON>elper", "dialogService", "message", "valid", "_houseService", "_houseHoldMainService", "_buildCaseService", "pettern", "router", "_eventService", "_ultilityService", "quotationService", "tempBuildCaseID", "pageFirst", "pageSize", "pageIndex", "totalRecords", "statusOptions", "value", "key", "label", "cIsEnableOptions", "buildCaseOptions", "houseHoldOptions", "progressOptions", "houseTypeOptions", "payStatusOptions", "signStatusOptions", "options", "getEnumOptions", "initDetail", "CHouseID", "CMail", "CIsChange", "CPayStatus", "CIsEnable", "CCustomerName", "CNationalID", "CProgress", "CHouseType", "CHouseHold", "CPhone", "quotationItems", "totalAmount", "currentHouse", "selectedFile", "buildingSelectedOptions", "receive", "pipe", "res", "action", "payload", "subscribe", "ngOnInit", "GetSessionStorage", "HOUSE_SEARCH", "undefined", "previous_search", "JSON", "parse", "searchQuery", "CBuildCaseSelected", "CHouseHoldSelected", "find", "x", "CHouseTypeSelected", "CPayStatusSelected", "CProgressSelected", "CSignStatusSelected", "CIsEnableSeleted", "CFrom", "CTo", "CBuildingNameSelected", "getListBuildCase", "onSearch", "sessionSave", "AddSessionStorage", "stringify", "getHouseList", "pageChanged", "newPage", "exportHouse", "cID", "apiHouseExportHousePost$Json", "CBuildCaseID", "Entries", "StatusCode", "downloadExcelFile", "showErrorMSG", "Message", "triggerFileInput", "fileInput", "nativeElement", "click", "onFileSelected", "event", "input", "target", "files", "length", "importExcel", "formData", "FormData", "append", "apiHouseImportHousePost$Json", "body", "CFile", "showSucessMSG", "getListHouseHold", "apiHouseGetListHouseHoldPost$Json", "map", "e", "index", "findIndex", "formatQuery", "bodyRequest", "PageIndex", "PageSize", "sortByFloorDescending", "arr", "sort", "a", "b", "CFloor", "apiHouseGetHouseListPost$Json", "houseList", "TotalItems", "onSelectionChangeBuildCase", "apiBuildCaseGetAllBuildCaseForSelectPost$Json", "CIsPagi", "CStatus", "userBuildCaseOptions", "CBuildCaseName", "setTimeout", "getHouseById", "CID", "ref", "detailSelected", "apiHouseGetHouseByIdPost$Json", "houseDetail", "changeStartDate", "CChangeStartDate", "Date", "changeEndDate", "CChangeEndDate", "CBuildCaseId", "findItemInArray", "houseHoldMain", "open", "array", "item", "openModelDetail", "openModel", "CBuildingName", "CHouseHoldCount", "formatDate", "CChangeDate", "format", "onSubmitDetail", "editHouseArgsParam", "CHousehold", "CId", "CNationalId", "validation", "errorMessages", "showErrorMSGs", "apiHouseEditHousePost$Json", "close", "onSubmit", "bodyReq", "onClose", "onNavidateId", "type", "id", "idURL", "navigate", "onNavidateBuildCaseIdHouseId", "buildCaseId", "houseId", "resetSecureKey", "confirm", "apiHouseResetHouseSecureKeyPost$Json", "clear", "required", "isStringMaxLength", "pattern", "MailPettern", "isPhoneNumber", "checkStartBeforeEnd", "validationHouseHoldMain", "isNaturalNumberInRange", "addHouseHoldMain", "apiHouseHoldMainAddHouseHoldMainPost$Json", "openQuotation", "dialog", "_this", "_asyncToGenerator", "response", "getQuotationByHouseId", "to<PERSON>romise", "success", "data", "calculateTotal", "error", "console", "context", "closeOnBackdropClick", "addQuotationItem", "push", "cHouseID", "cItemName", "cUnitPrice", "cCount", "cStatus", "cVersion", "cIsDefault", "loadDefaultItems", "_this2", "getDefaultQuotationItems", "defaultItems", "removeQuotationItem", "splice", "reduce", "sum", "formatCurrency", "amount", "Intl", "NumberFormat", "style", "currency", "minimumFractionDigits", "saveQuotation", "_this3", "toastrService", "warning", "invalidItems", "filter", "trim", "request", "items", "exportQuotation", "_this4", "blob", "url", "window", "URL", "createObjectURL", "link", "document", "createElement", "href", "download", "revokeObjectURL", "__decorate", "selector", "templateUrl", "styleUrls", "standalone", "imports"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\household-management\\household-management.component.ts"], "sourcesContent": ["import { Component, ElementRef, OnInit, ViewChild } from '@angular/core';\r\nimport { SharedModule } from '../components/shared.module';\r\nimport { CommonModule } from '@angular/common';\r\nimport { NbDatepickerModule, NbDialogService } from '@nebular/theme';\r\nimport { MessageService } from 'src/app/shared/services/message.service';\r\nimport { ValidationHelper } from 'src/app/shared/helper/validationHelper';\r\nimport { BuildCaseService, HouseHoldMainService, HouseService } from 'src/services/api/services';\r\nimport { PetternHelper } from 'src/app/shared/helper/petternHelper';\r\n// import { TypeMailPipe } from 'src/app/@theme/pipes/mapping.pipe';\r\nimport { Router } from '@angular/router';\r\nimport { BaseComponent } from '../components/base/baseComponent';\r\nimport { AllowHelper } from 'src/app/shared/helper/allowHelper';\r\nimport { AddHouseHoldMain, EditHouseArgs, GetHouseList<PERSON>rgs, GetHouseListRes, TblHouse } from 'src/services/api/models';\r\nimport { concatMap, tap } from 'rxjs';\r\nimport { NbDateFnsDateModule } from '@nebular/date-fns';\r\nimport * as moment from 'moment';\r\nimport { EEvent, EventService, IEvent } from 'src/app/shared/services/event.service';\r\nimport { UtilityService } from 'src/app/shared/services/utility.service';\r\nimport { EnumHouseProgress } from 'src/app/shared/enum/enumHouseProgress';\r\nimport { EnumHelper } from 'src/app/shared/helper/enumHelper';\r\nimport { EnumHouseType } from 'src/app/shared/enum/enumHouseType';\r\nimport { EnumPayStatus } from 'src/app/shared/enum/enumPayStatus';\r\nimport { EnumSignStatus } from 'src/app/shared/enum/enumSignStatus';\r\nimport { LocalStorageService } from 'src/app/shared/services/local-storage.service';\r\nimport { STORAGE_KEY } from 'src/app/shared/constant/constant';\r\nimport { QuotationService } from '../services/quotation.service';\r\nimport { QuotationItem } from '../models/quotation.model';\r\n\r\nexport interface selectItem {\r\n  label: string,\r\n  value: number | string,\r\n  key?: string\r\n}\r\n\r\n// interface HouseDetailExtension {\r\n//   changeStartDate: string;\r\n//   changeEndDate: string;\r\n// }\r\nexport interface SearchQuery {\r\n  CBuildCaseSelected?: any | null;\r\n  CHouseTypeSelected?: any | null;\r\n  CBuildingNameSelected?: any | null;\r\n  CHouseHoldSelected?: any | null;\r\n  CPayStatusSelected?: any | null;\r\n  CProgressSelected?: any | null;\r\n  CSignStatusSelected?: any | null;\r\n  CIsEnableSeleted?: any | null;\r\n  CFrom?: any | null;\r\n  CTo?: any | null;\r\n}\r\n\r\n@Component({\r\n  selector: 'ngx-household-management',\r\n  templateUrl: './household-management.component.html',\r\n  styleUrls: ['./household-management.component.scss'],\r\n  standalone: true,\r\n  imports: [CommonModule, SharedModule, NbDatepickerModule, NbDateFnsDateModule,],\r\n})\r\n\r\nexport class HouseholdManagementComponent extends BaseComponent implements OnInit {\r\n  tempBuildCaseID: number = -1\r\n  constructor(\r\n    private _allow: AllowHelper,\r\n    private enumHelper: EnumHelper,\r\n    private dialogService: NbDialogService,\r\n    private message: MessageService,\r\n    private valid: ValidationHelper,\r\n    private _houseService: HouseService,\r\n    private _houseHoldMainService: HouseHoldMainService,\r\n    private _buildCaseService: BuildCaseService,\r\n    private pettern: PetternHelper,\r\n    private router: Router,\r\n    private _eventService: EventService,\r\n    private _ultilityService: UtilityService,\r\n    private quotationService: QuotationService\r\n  ) {\r\n    super(_allow)\r\n    this._eventService.receive().pipe(\r\n      tap((res: IEvent) => {\r\n        if (res.action == EEvent.GET_BUILDCASE && !!res.payload) {\r\n          this.tempBuildCaseID = res.payload\r\n        }\r\n      })\r\n    ).subscribe()\r\n  }\r\n\r\n  override pageFirst = 1;\r\n  override pageSize = 10;\r\n  override pageIndex = 1;\r\n  override totalRecords = 0;\r\n\r\n  statusOptions: selectItem[] = [\r\n    {\r\n      value: 0,\r\n      key: 'allow',\r\n      label: '允許',\r\n    },\r\n    {\r\n      value: 1,\r\n      key: 'not allowed',\r\n      label: '不允許',\r\n    }\r\n  ]\r\n\r\n  cIsEnableOptions = [\r\n    {\r\n      value: null,\r\n      key: 'all',\r\n      label: '全部',\r\n    },\r\n    {\r\n      value: true,\r\n      key: 'enable',\r\n      label: '啟用',\r\n    },\r\n    {\r\n      value: false,\r\n      key: 'deactivate',\r\n      label: '停用',\r\n    }\r\n  ]\r\n\r\n  searchQuery: SearchQuery\r\n  detailSelected: SearchQuery\r\n\r\n  buildCaseOptions: any[] = [{ label: '全部', value: '' }]\r\n  houseHoldOptions: any[] = [{ label: '全部', value: '' }]\r\n  progressOptions: any[] = [{ label: '全部', value: -1 }]\r\n  houseTypeOptions: any[] = [{ label: '全部', value: -1 }]\r\n  payStatusOptions: any[] = [{ label: '全部', value: -1 }]\r\n  signStatusOptions: any[] = [{ label: '全部', value: -1 }]\r\n\r\n  options = {\r\n    progressOptions: this.enumHelper.getEnumOptions(EnumHouseProgress),\r\n    payStatusOptions: this.enumHelper.getEnumOptions(EnumPayStatus),\r\n    houseTypeOptions: this.enumHelper.getEnumOptions(EnumHouseType),\r\n  }\r\n\r\n  userBuildCaseOptions: any\r\n  initDetail = {\r\n    CHouseID: 0,\r\n    CMail: \"\",\r\n    CIsChange: false,\r\n    CPayStatus: 0,\r\n    CIsEnable: false,\r\n    CCustomerName: \"\",\r\n    CNationalID: \"\",\r\n    CProgress: \"\",\r\n    CHouseType: 0,\r\n    CHouseHold: \"\",\r\n    CPhone: \"\"\r\n  }\r\n\r\n  quotationItems: QuotationItem[] = [];\r\n  totalAmount: number = 0;\r\n  currentHouse: any = null;\r\n\r\n  override ngOnInit(): void {\r\n    this.progressOptions = [\r\n      ...this.progressOptions,\r\n      ...this.enumHelper.getEnumOptions(EnumHouseProgress)\r\n    ]\r\n    this.houseTypeOptions = [\r\n      ...this.houseTypeOptions,\r\n      ...this.enumHelper.getEnumOptions(EnumHouseType)\r\n    ]\r\n    this.payStatusOptions = [\r\n      ...this.payStatusOptions,\r\n      ...this.enumHelper.getEnumOptions(EnumPayStatus)\r\n    ]\r\n    this.signStatusOptions = [\r\n      ...this.signStatusOptions,\r\n      ...this.enumHelper.getEnumOptions(EnumSignStatus)\r\n    ]\r\n\r\n    if (LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH) != null\r\n      && LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH) != undefined\r\n      && LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH) != \"\") {\r\n      let previous_search = JSON.parse(LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH));\r\n      this.searchQuery = {\r\n        CBuildCaseSelected: null,\r\n        // CBuildingNameSelected: previous_search.CBuildingNameSelected != null && previous_search.CBuildingNameSelected != undefined\r\n        //   ? this.buildingSelectedOptions.find(x => x.value == previous_search.CBuildingNameSelected.value)\r\n        //   : this.buildingSelectedOptions[0],\r\n        CHouseHoldSelected: previous_search.CHouseHoldSelected != null && previous_search.CHouseHoldSelected != undefined\r\n          ? this.houseHoldOptions.find(x => x.value == previous_search.CHouseHoldSelected.value)\r\n          : this.houseHoldOptions[0],\r\n        CHouseTypeSelected: previous_search.CHouseTypeSelected != null && previous_search.CHouseTypeSelected != undefined\r\n          ? this.houseTypeOptions.find(x => x.value == previous_search.CHouseTypeSelected.value)\r\n          : this.houseTypeOptions[0],\r\n        CPayStatusSelected: previous_search.CPayStatusSelected != null && previous_search.CPayStatusSelected != undefined\r\n          ? this.payStatusOptions.find(x => x.value == previous_search.CPayStatusSelected.value)\r\n          : this.payStatusOptions[0],\r\n        CProgressSelected: previous_search.CProgressSelected != null && previous_search.CProgressSelected != undefined\r\n          ? this.progressOptions.find(x => x.value == previous_search.CProgressSelected.value)\r\n          : this.progressOptions[0],\r\n        CSignStatusSelected: previous_search.CSignStatusSelected != null && previous_search.CSignStatusSelected != undefined\r\n          ? this.signStatusOptions.find(x => x.value == previous_search.CSignStatusSelected.value)\r\n          : this.signStatusOptions[0],\r\n        CIsEnableSeleted: previous_search.CIsEnableSeleted != null && previous_search.CIsEnableSeleted != undefined\r\n          ? this.cIsEnableOptions.find(x => x.value == previous_search.CIsEnableSeleted.value)\r\n          : this.cIsEnableOptions[0],\r\n        CFrom: previous_search.CFrom != null && previous_search.CFrom != undefined\r\n          ? previous_search.CFrom\r\n          : '',\r\n        CTo: previous_search.CTo != null && previous_search.CTo != undefined\r\n          ? previous_search.CTo\r\n          : ''\r\n      }\r\n    }\r\n    else {\r\n      this.searchQuery = {\r\n        CBuildCaseSelected: null,\r\n        CBuildingNameSelected: this.buildingSelectedOptions[0],\r\n        CHouseHoldSelected: this.houseHoldOptions[0],\r\n        CHouseTypeSelected: this.houseTypeOptions[0],\r\n        CPayStatusSelected: this.payStatusOptions[0],\r\n        CProgressSelected: this.progressOptions[0],\r\n        CSignStatusSelected: this.signStatusOptions[0],\r\n        CIsEnableSeleted: this.cIsEnableOptions[0],\r\n        CFrom: '',\r\n        CTo: ''\r\n      }\r\n    }\r\n    this.getListBuildCase()\r\n  }\r\n\r\n  onSearch() {\r\n    let sessionSave = {\r\n      CBuildCaseSelected: this.searchQuery.CBuildCaseSelected,\r\n      // CBuildingNameSelected: this.searchQuery.CBuildingNameSelected,\r\n      CFrom: this.searchQuery.CFrom,\r\n      CTo: this.searchQuery.CTo,\r\n      CHouseHoldSelected: this.searchQuery.CHouseHoldSelected,\r\n      CHouseTypeSelected: this.searchQuery.CHouseTypeSelected,\r\n      CIsEnableSeleted: this.searchQuery.CIsEnableSeleted,\r\n      CPayStatusSelected: this.searchQuery.CPayStatusSelected,\r\n      CProgressSelected: this.searchQuery.CProgressSelected,\r\n      CSignStatusSelected: this.searchQuery.CSignStatusSelected\r\n    }\r\n    LocalStorageService.AddSessionStorage(STORAGE_KEY.HOUSE_SEARCH, JSON.stringify(sessionSave));\r\n    this.getHouseList().subscribe()\r\n  }\r\n\r\n  pageChanged(newPage: number) {\r\n    this.pageIndex = newPage;\r\n    this.getHouseList().subscribe()\r\n  }\r\n\r\n  exportHouse() {\r\n    if (this.searchQuery.CBuildCaseSelected.cID) {\r\n      this._houseService.apiHouseExportHousePost$Json({\r\n        CBuildCaseID: this.searchQuery.CBuildCaseSelected.cID\r\n      }).subscribe(res => {\r\n        if (res.Entries && res.StatusCode == 0) {\r\n          this._ultilityService.downloadExcelFile(\r\n            res.Entries, '戶別資訊範本'\r\n          )\r\n        } else {\r\n          this.message.showErrorMSG(res.Message!);\r\n        }\r\n      })\r\n    }\r\n  }\r\n\r\n  selectedFile: File | null = null;\r\n  @ViewChild('fileInput') fileInput: ElementRef<HTMLInputElement>;\r\n\r\n\r\n  triggerFileInput(): void {\r\n    this.fileInput.nativeElement.click();\r\n  }\r\n\r\n  onFileSelected(event: Event): void {\r\n    const input = event.target as HTMLInputElement;\r\n    if (input.files && input.files.length > 0) {\r\n      this.selectedFile = input.files[0];\r\n      this.importExcel();\r\n    }\r\n  }\r\n\r\n  importExcel(): void {\r\n    if (this.selectedFile) {\r\n      const formData = new FormData();\r\n      formData.append('CFile', this.selectedFile);\r\n      this._houseService.apiHouseImportHousePost$Json({\r\n        body: {\r\n          CBuildCaseID: this.searchQuery.CBuildCaseSelected.cID,\r\n          CFile: this.selectedFile\r\n        }\r\n      }).subscribe(res => {\r\n        if (res.StatusCode === 0) {\r\n          this.message.showSucessMSG(res.Message!);\r\n          this.getHouseList().subscribe()\r\n        } else {\r\n          this.message.showErrorMSG(res.Message!);\r\n        }\r\n      });\r\n    }\r\n  }\r\n\r\n\r\n  getListHouseHold() {\r\n    this._houseService.apiHouseGetListHouseHoldPost$Json({\r\n      body: { CBuildCaseID: this.searchQuery.CBuildCaseSelected.cID }\r\n    }).subscribe(res => {\r\n      if (res.Entries && res.StatusCode == 0) {\r\n        this.houseHoldOptions = [{\r\n          value: '', label: '全部'\r\n        }, ...res.Entries.map(e => {\r\n          return { value: e, label: e }\r\n        })]\r\n        if (LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH) != null\r\n          && LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH) != undefined\r\n          && LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH) != \"\") {\r\n          let previous_search = JSON.parse(LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH));\r\n          if (previous_search.CHouseHoldSelected != null && previous_search.CHouseHoldSelected != undefined) {\r\n            let index = this.houseHoldOptions.findIndex((x: any) => x.value == previous_search.CHouseHoldSelected.value)\r\n            this.searchQuery.CHouseHoldSelected = this.houseHoldOptions[index]\r\n          } else {\r\n            this.searchQuery.CHouseHoldSelected = this.houseHoldOptions[0]\r\n          }\r\n        }\r\n      }\r\n    })\r\n  }\r\n\r\n  houseList: any\r\n  bodyRequest: GetHouseListArgs\r\n\r\n  formatQuery() {\r\n    this.bodyRequest = {\r\n      CBuildCaseID: this.searchQuery.CBuildCaseSelected.cID,\r\n      PageIndex: this.pageIndex,\r\n      PageSize: this.pageSize\r\n    }\r\n\r\n    // if (this.searchQuery.CBuildingNameSelected) {\r\n    //   this.bodyRequest['CBuildingName'] = this.searchQuery.CBuildingNameSelected.value\r\n    // }\r\n    if (this.searchQuery.CFrom && this.searchQuery.CTo) {\r\n      this.bodyRequest['CFloor'] = { CFrom: this.searchQuery.CFrom, CTo: this.searchQuery.CTo }\r\n    }\r\n    if (this.searchQuery.CHouseHoldSelected) {\r\n      this.bodyRequest['CHouseHold'] = this.searchQuery.CHouseHoldSelected.value\r\n    }\r\n    if (this.searchQuery.CHouseTypeSelected.value) {\r\n      this.bodyRequest['CHouseType'] = this.searchQuery.CHouseTypeSelected.value\r\n    }\r\n    if (typeof this.searchQuery.CIsEnableSeleted.value === \"boolean\") {\r\n      this.bodyRequest['CIsEnable'] = this.searchQuery.CIsEnableSeleted.value\r\n    }\r\n    if (this.searchQuery.CPayStatusSelected.value) {\r\n      this.bodyRequest['CPayStatus'] = this.searchQuery.CPayStatusSelected.value\r\n    }\r\n    if (this.searchQuery.CProgressSelected.value) {\r\n      this.bodyRequest['CProgress'] = this.searchQuery.CProgressSelected.value\r\n    }\r\n    if (this.searchQuery.CSignStatusSelected.value) {\r\n      this.bodyRequest['CSignStatus'] = this.searchQuery.CSignStatusSelected.value\r\n    }\r\n\r\n    return this.bodyRequest\r\n  }\r\n\r\n  sortByFloorDescending(arr: GetHouseListRes[]): GetHouseListRes[] {\r\n    return arr.sort((a, b) => (b.CFloor ?? 0) - (a.CFloor ?? 0));\r\n  }\r\n\r\n  getHouseList() {\r\n    return this._houseService.apiHouseGetHouseListPost$Json({\r\n      body: this.formatQuery()\r\n    }).pipe(\r\n      tap(res => {\r\n        if (res.Entries && res.StatusCode == 0) {\r\n          this.houseList = res.Entries;\r\n          this.totalRecords = res.TotalItems!;\r\n        }\r\n      })\r\n    )\r\n  }\r\n\r\n\r\n  userBuildCaseSelected: any\r\n  onSelectionChangeBuildCase() {\r\n    // this.getListBuilding()\r\n    this.getListHouseHold()\r\n    this.getHouseList().subscribe()\r\n  }\r\n  getListBuildCase() {\r\n    this._buildCaseService.apiBuildCaseGetAllBuildCaseForSelectPost$Json({\r\n      body: {\r\n        CIsPagi: false,\r\n        CStatus: 1,\r\n      }\r\n    }).pipe(\r\n      tap(res => {\r\n        if (res.Entries && res.StatusCode == 0) {\r\n          this.userBuildCaseOptions = res.Entries?.length ? res.Entries.map(res => {\r\n            return {\r\n              CBuildCaseName: res.CBuildCaseName,\r\n              cID: res.cID\r\n            }\r\n          }) : []\r\n\r\n          if (LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH) != null\r\n            && LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH) != undefined\r\n            && LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH) != \"\") {\r\n            let previous_search = JSON.parse(LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH));\r\n            if (previous_search.CBuildCaseSelected != null && previous_search.CBuildCaseSelected != undefined) {\r\n              let index = this.userBuildCaseOptions.findIndex((x: any) => x.cID == previous_search.CBuildCaseSelected.cID)\r\n              this.searchQuery.CBuildCaseSelected = this.userBuildCaseOptions[index]\r\n            } else {\r\n              this.searchQuery.CBuildCaseSelected = this.userBuildCaseOptions[0]\r\n            }\r\n          }\r\n          else {\r\n            this.searchQuery.CBuildCaseSelected = this.userBuildCaseOptions[0]\r\n          }\r\n        }\r\n      }),\r\n      tap(() => {\r\n        // this.getListBuilding()\r\n        this.getListHouseHold()\r\n        setTimeout(() => {\r\n          this.getHouseList().subscribe();\r\n        }, 500)\r\n      })\r\n    ).subscribe()\r\n  }\r\n\r\n  buildingSelected: any\r\n\r\n  buildingSelectedOptions: any[] = [\r\n    {\r\n      value: '', label: '全部'\r\n    }\r\n  ]\r\n\r\n  // getListBuilding() {\r\n  //   this._houseService.apiHouseGetListBuildingPost$Json({\r\n  //     body: {\r\n  //       CBuildCaseID: this.searchQuery.CBuildCaseSelected.cID\r\n  //     }\r\n  //   }).subscribe(res => {\r\n  //     if (res.Entries && res.StatusCode == 0) {\r\n  //       this.buildingSelectedOptions = [{\r\n  //         value: '', label: '全部'\r\n  //       }, ...res.Entries.map(e => {\r\n  //         return { value: e, label: e }\r\n  //       })]\r\n  //       if (LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH) != null\r\n  //         && LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH) != undefined\r\n  //         && LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH) != \"\") {\r\n  //         let previous_search = JSON.parse(LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH));\r\n  //         if (previous_search.CBuildingNameSelected != null && previous_search.CBuildingNameSelected != undefined) {\r\n  //           let index = this.buildingSelectedOptions.findIndex((x: any) => x.value == previous_search.CBuildingNameSelected.value)\r\n  //           this.searchQuery.CBuildingNameSelected = this.buildingSelectedOptions[index]\r\n  //         } else {\r\n  //           this.searchQuery.CBuildingNameSelected = this.buildingSelectedOptions[0]\r\n  //         }\r\n  //       }\r\n  //       else {\r\n  //         this.searchQuery.CBuildingNameSelected = this.buildingSelectedOptions[0]\r\n  //       }\r\n  //     }\r\n  //   })\r\n  // }\r\n\r\n  houseDetail: TblHouse & {\r\n    changeStartDate?: any;\r\n    changeEndDate?: any\r\n  }\r\n\r\n  getHouseById(CID: any, ref: any) {\r\n    this.detailSelected = {}\r\n    this._houseService.apiHouseGetHouseByIdPost$Json({\r\n      body: { CHouseID: CID }\r\n    }).subscribe(res => {\r\n      if (res.Entries && res.StatusCode == 0) {\r\n        this.houseDetail = {\r\n          ...res.Entries,\r\n          changeStartDate: res.Entries.CChangeStartDate ? new Date(res.Entries.CChangeStartDate) : undefined,\r\n          changeEndDate: res.Entries.CChangeEndDate ? new Date(res.Entries.CChangeEndDate) : undefined,\r\n        }\r\n\r\n        if (res.Entries.CBuildCaseId) {\r\n          this.detailSelected.CBuildCaseSelected = this.findItemInArray(this.userBuildCaseOptions, 'cID', res.Entries.CBuildCaseId)\r\n        }\r\n        this.detailSelected.CPayStatusSelected = this.findItemInArray(this.options.payStatusOptions, 'value', res.Entries.CPayStatus)\r\n        if (res.Entries.CHouseType) {\r\n          this.detailSelected.CHouseTypeSelected = this.findItemInArray(this.options.houseTypeOptions, 'value', res.Entries.CHouseType)\r\n        } else {\r\n          this.detailSelected.CHouseTypeSelected = this.options.houseTypeOptions[1]\r\n        }\r\n        this.detailSelected.CProgressSelected = this.findItemInArray(this.options.progressOptions, 'value', res.Entries.CProgress)\r\n\r\n        if (res.Entries.CBuildCaseId) {\r\n          if (this.houseHoldMain) {\r\n            this.houseHoldMain.CBuildCaseID = res.Entries.CBuildCaseId\r\n          }\r\n        }\r\n        this.dialogService.open(ref)\r\n      }\r\n\r\n    })\r\n  }\r\n\r\n\r\n  findItemInArray(array: any[], key: string, value: any) {\r\n    return array.find(item => item[key] === value);\r\n  }\r\n\r\n\r\n  openModelDetail(ref: any, item: any) {\r\n    this.getHouseById(item.CID, ref)\r\n  }\r\n\r\n  openModel(ref: any) {\r\n    this.houseHoldMain = {\r\n      CBuildingName: '',\r\n      CFloor: undefined,\r\n      CHouseHoldCount: undefined\r\n    }\r\n    this.dialogService.open(ref)\r\n  }\r\n\r\n  editHouseArgsParam: EditHouseArgs\r\n\r\n\r\n  formatDate(CChangeDate: string): string {\r\n    if (CChangeDate) {\r\n      return moment(CChangeDate).format('YYYY-MM-DDTHH:mm:ss')\r\n    }\r\n    return ''\r\n  }\r\n\r\n  onSubmitDetail(ref: any) {\r\n    this.houseDetail.CChangeStartDate = this.houseDetail.changeStartDate ? this.formatDate(this.houseDetail.changeStartDate) : '',\r\n      this.houseDetail.CChangeEndDate = this.houseDetail.changeEndDate ? this.formatDate(this.houseDetail.changeEndDate) : '',\r\n\r\n      this.editHouseArgsParam = {\r\n        CCustomerName: this.houseDetail.CCustomerName,\r\n        CHouseHold: this.houseDetail.CHousehold,\r\n        CHouseID: this.houseDetail.CId,\r\n        CHouseType: this.detailSelected.CHouseTypeSelected ? this.detailSelected.CHouseTypeSelected.value : null,\r\n        CIsChange: this.houseDetail.CIsChange,\r\n        CIsEnable: this.houseDetail.CIsEnable,\r\n        CMail: this.houseDetail.CMail,\r\n        CNationalID: this.houseDetail.CNationalId,\r\n        CPayStatus: this.detailSelected.CPayStatusSelected ? this.detailSelected.CPayStatusSelected.value : null,\r\n        CPhone: this.houseDetail.CPhone,\r\n        CProgress: this.detailSelected.CProgressSelected ? this.detailSelected.CProgressSelected.value : null,\r\n        CChangeStartDate: this.houseDetail.CChangeStartDate,\r\n        CChangeEndDate: this.houseDetail.CChangeEndDate\r\n      }\r\n    this.validation()\r\n    if (this.valid.errorMessages.length > 0) {\r\n      this.message.showErrorMSGs(this.valid.errorMessages);\r\n      return\r\n    }\r\n\r\n    this._houseService.apiHouseEditHousePost$Json({\r\n      body: this.editHouseArgsParam\r\n    }).pipe(\r\n      tap(res => {\r\n        if (res.StatusCode === 0) {\r\n          this.message.showSucessMSG(\"執行成功\");\r\n          ref.close();\r\n        } else {\r\n          this.message.showErrorMSG(res.Message!);\r\n          ref.close();\r\n        }\r\n      }),\r\n      concatMap(() => this.getHouseList())\r\n    ).subscribe();\r\n  }\r\n\r\n\r\n  onSubmit(ref: any) {\r\n    let bodyReq: EditHouseArgs = {\r\n      CCustomerName: this.houseDetail.CCustomerName,\r\n      CHouseHold: this.houseDetail.CHousehold,\r\n      CHouseID: this.houseDetail.CId,\r\n      CHouseType: this.houseDetail.CHouseType,\r\n      CIsChange: this.houseDetail.CIsChange,\r\n      CIsEnable: this.houseDetail.CIsEnable,\r\n      CMail: this.houseDetail.CMail,\r\n      CNationalID: this.houseDetail.CNationalId,\r\n      CPayStatus: this.houseDetail.CPayStatus,\r\n      CPhone: this.houseDetail.CPhone,\r\n      CProgress: this.houseDetail.CProgress,\r\n    }\r\n    this._houseService.apiHouseEditHousePost$Json({\r\n      body: bodyReq\r\n    }).subscribe(res => {\r\n      if (res.StatusCode === 0) {\r\n        this.message.showSucessMSG(\"執行成功\");\r\n        ref.close();\r\n      }\r\n    })\r\n\r\n  }\r\n\r\n  onClose(ref: any) {\r\n    ref.close();\r\n  }\r\n\r\n  onNavidateId(type: any, id?: any) {\r\n    const idURL = id ? id : this.searchQuery.CBuildCaseSelected.cID\r\n    this.router.navigate([`/pages/household-management/${type}`, idURL])\r\n  }\r\n\r\n  onNavidateBuildCaseIdHouseId(type: any, buildCaseId: any, houseId: any) {\r\n    this.router.navigate([`/pages/household-management/${type}`, buildCaseId, houseId])\r\n  }\r\n\r\n  resetSecureKey(item: any) {\r\n    if (confirm(\"您想重設密碼嗎？\")) {\r\n      this._houseService.apiHouseResetHouseSecureKeyPost$Json({\r\n        body: item.CID\r\n      }).subscribe(res => {\r\n        if (res.StatusCode == 0) {\r\n          this.message.showSucessMSG(\"執行成功\");\r\n        }\r\n      })\r\n    }\r\n  }\r\n\r\n  houseHoldMain: AddHouseHoldMain\r\n\r\n  validation() {\r\n    this.valid.clear();\r\n    this.valid.required('[建案名稱]', this.houseDetail.CId)\r\n    this.valid.required('[戶型名稱]', this.editHouseArgsParam.CHouseHold)\r\n    this.valid.isStringMaxLength('[棟別]', this.editHouseArgsParam.CHouseHold, 50)\r\n    this.valid.required('[樓層]', this.houseDetail.CFloor)\r\n    this.valid.isStringMaxLength('[客戶姓名]', this.editHouseArgsParam.CCustomerName, 50)\r\n    // if (this.editHouseArgsParam.CNationalID) {\r\n    //   this.valid.CheckTaiwanID(this.editHouseArgsParam.CNationalID)\r\n    // }\r\n    this.valid.pattern('[電子郵件]', this.editHouseArgsParam.CMail, this.pettern.MailPettern)\r\n    this.valid.isPhoneNumber('[聯絡電話]', this.editHouseArgsParam.CPhone)\r\n    this.valid.required('[進度]', this.editHouseArgsParam.CProgress)\r\n    this.valid.required('[戶別類型]', this.detailSelected.CHouseTypeSelected.value)\r\n    this.valid.required('[付款狀態]', this.detailSelected.CPayStatusSelected.value)\r\n    if (this.houseDetail.CChangeStartDate) {\r\n      this.valid.required('[客變結束日期]', this.houseDetail.CChangeEndDate)\r\n    }\r\n    if (this.houseDetail.CChangeEndDate) {\r\n      this.valid.required('[客變開始日期]', this.houseDetail.CChangeStartDate)\r\n    }\r\n    this.valid.checkStartBeforeEnd('[開放日期]', this.houseDetail.CChangeStartDate ? this.houseDetail.CChangeStartDate : '', this.houseDetail.CChangeEndDate ? this.houseDetail.CChangeEndDate : '')\r\n  }\r\n\r\n  validationHouseHoldMain() {\r\n    this.valid.clear();\r\n    this.valid.required('[建案]', this.houseHoldMain.CBuildCaseID)\r\n    this.valid.required('[棟別]', this.houseHoldMain.CBuildingName)\r\n    this.valid.isStringMaxLength('[棟別]', this.houseHoldMain.CBuildingName, 10)\r\n    this.valid.isNaturalNumberInRange('[當層最多戶數]', this.houseHoldMain.CFloor, 1, 100)\r\n    this.valid.isNaturalNumberInRange('[本棟總樓層]', this.houseHoldMain.CHouseHoldCount, 1, 100)\r\n  }\r\n\r\n\r\n  addHouseHoldMain(ref: any) {\r\n    this.houseHoldMain.CBuildCaseID = this.searchQuery.CBuildCaseSelected.cID,\r\n      this.validationHouseHoldMain()\r\n    if (this.valid.errorMessages.length > 0) {\r\n      this.message.showErrorMSGs(this.valid.errorMessages);\r\n      return\r\n    }\r\n    this._houseHoldMainService.apiHouseHoldMainAddHouseHoldMainPost$Json({\r\n      body: this.houseHoldMain\r\n    }).pipe(\r\n      tap(res => {\r\n        if (res.StatusCode === 0) {\r\n          this.message.showSucessMSG(\"執行成功\");\r\n          ref.close();\r\n        }\r\n      }),\r\n      concatMap(() => this.getHouseList())\r\n    ).subscribe();\r\n  }\r\n\r\n  // 開啟報價單對話框\r\n  async openQuotation(dialog: any, item: any) {\r\n    this.currentHouse = item;\r\n    this.quotationItems = [];\r\n    this.totalAmount = 0;\r\n\r\n    // 載入現有報價資料\r\n    try {\r\n      const response = await this.quotationService.getQuotationByHouseId(item.CID).toPromise();\r\n      if (response?.success && response.data) {\r\n        this.quotationItems = response.data;\r\n        this.calculateTotal();\r\n      }\r\n    } catch (error) {\r\n      console.error('載入報價資料失敗:', error);\r\n    }\r\n\r\n    this.dialogService.open(dialog, {\r\n      context: item,\r\n      closeOnBackdropClick: false\r\n    });\r\n  }\r\n\r\n  // 新增自定義報價項目\r\n  addQuotationItem() {\r\n    this.quotationItems.push({\r\n      cHouseID: this.currentHouse?.CID || 0,\r\n      cItemName: '',\r\n      cUnitPrice: 0,\r\n      cCount: 1,\r\n      cStatus: 1,\r\n      cVersion: 1,\r\n      cIsDefault: false\r\n    });\r\n  }\r\n\r\n  // 載入預設項目\r\n  async loadDefaultItems() {\r\n    try {\r\n      const response = await this.quotationService.getDefaultQuotationItems().toPromise();\r\n      if (response?.success && response.data) {\r\n        const defaultItems = response.data.map(item => ({\r\n          ...item,\r\n          cHouseID: this.currentHouse?.CID || 0,\r\n          cCount: 1\r\n        }));\r\n        this.quotationItems.push(...defaultItems);\r\n        this.calculateTotal();\r\n      }\r\n    } catch (error) {\r\n      // this.toastrService.error('載入預設項目失敗');\r\n    }\r\n  }\r\n\r\n  // 移除報價項目\r\n  removeQuotationItem(index: number) {\r\n    const item = this.quotationItems[index];\r\n    if (item.cIsDefault) {\r\n      // this.toastrService.warning('預設項目無法刪除');\r\n      return;\r\n    }\r\n    this.quotationItems.splice(index, 1);\r\n    this.calculateTotal();\r\n  }\r\n\r\n  // 計算總金額\r\n  calculateTotal() {\r\n    this.totalAmount = this.quotationItems.reduce((sum, item) => {\r\n      return sum + (item.cUnitPrice * item.cCount);\r\n    }, 0);\r\n  }\r\n\r\n  // 格式化金額\r\n  formatCurrency(amount: number): string {\r\n    return new Intl.NumberFormat('zh-TW', {\r\n      style: 'currency',\r\n      currency: 'TWD',\r\n      minimumFractionDigits: 0\r\n    }).format(amount);\r\n  }\r\n\r\n  // 儲存報價單\r\n  async saveQuotation(ref: any) {\r\n    if (this.quotationItems.length === 0) {\r\n      this.toastrService.warning('請先新增報價項目');\r\n      return;\r\n    }\r\n\r\n    // 驗證必填欄位\r\n    const invalidItems = this.quotationItems.filter(item =>\r\n      !item.cItemName.trim() || item.cUnitPrice < 0 || item.cCount < 1\r\n    );\r\n\r\n    if (invalidItems.length > 0) {\r\n      this.toastrService.warning('請確認所有項目名稱、單價及數量都已正確填寫');\r\n      return;\r\n    }\r\n\r\n    try {\r\n      const request = {\r\n        houseId: this.currentHouse.CID,\r\n        items: this.quotationItems\r\n      };\r\n\r\n      const response = await this.quotationService.saveQuotation(request).toPromise();\r\n      if (response?.success) {\r\n        this.toastrService.success('報價單儲存成功');\r\n        ref.close();\r\n      } else {\r\n        this.toastrService.error(response?.message || '儲存失敗');\r\n      }\r\n    } catch (error) {\r\n      this.toastrService.error('報價單儲存失敗');\r\n    }\r\n  }\r\n\r\n  // 匯出報價單\r\n  async exportQuotation() {\r\n    try {\r\n      const blob = await this.quotationService.exportQuotation(this.currentHouse.CID).toPromise();\r\n      const url = window.URL.createObjectURL(blob);\r\n      const link = document.createElement('a');\r\n      link.href = url;\r\n      link.download = `報價單_${this.currentHouse.CHouseHold}_${this.currentHouse.CFloor}樓.pdf`;\r\n      link.click();\r\n      window.URL.revokeObjectURL(url);\r\n    } catch (error) {\r\n      this.toastrService.error('匯出報價單失敗');\r\n    }\r\n  }\r\n}\r\n"], "mappings": ";;AAAA,SAASA,SAAS,EAAsBC,SAAS,QAAQ,eAAe;AACxE,SAASC,YAAY,QAAQ,6BAA6B;AAC1D,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,kBAAkB,QAAyB,gBAAgB;AAOpE,SAASC,aAAa,QAAQ,kCAAkC;AAGhE,SAASC,SAAS,EAAEC,GAAG,QAAQ,MAAM;AACrC,SAASC,mBAAmB,QAAQ,mBAAmB;AACvD,OAAO,KAAKC,MAAM,MAAM,QAAQ;AAGhC,SAASC,iBAAiB,QAAQ,uCAAuC;AAEzE,SAASC,aAAa,QAAQ,mCAAmC;AACjE,SAASC,aAAa,QAAQ,mCAAmC;AACjE,SAASC,cAAc,QAAQ,oCAAoC;AACnE,SAASC,mBAAmB,QAAQ,+CAA+C;AACnF,SAASC,WAAW,QAAQ,kCAAkC;AAmCvD,IAAMC,4BAA4B,GAAlC,MAAMA,4BAA6B,SAAQX,aAAa;EAE7DY,YACUC,MAAmB,EACnBC,UAAsB,EACtBC,aAA8B,EAC9BC,OAAuB,EACvBC,KAAuB,EACvBC,aAA2B,EAC3BC,qBAA2C,EAC3CC,iBAAmC,EACnCC,OAAsB,EACtBC,MAAc,EACdC,aAA2B,EAC3BC,gBAAgC,EAChCC,gBAAkC;IAE1C,KAAK,CAACZ,MAAM,CAAC;IAdL,KAAAA,MAAM,GAANA,MAAM;IACN,KAAAC,UAAU,GAAVA,UAAU;IACV,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,qBAAqB,GAArBA,qBAAqB;IACrB,KAAAC,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAd1B,KAAAC,eAAe,GAAW,CAAC,CAAC;IA0BnB,KAAAC,SAAS,GAAG,CAAC;IACb,KAAAC,QAAQ,GAAG,EAAE;IACb,KAAAC,SAAS,GAAG,CAAC;IACb,KAAAC,YAAY,GAAG,CAAC;IAEzB,KAAAC,aAAa,GAAiB,CAC5B;MACEC,KAAK,EAAE,CAAC;MACRC,GAAG,EAAE,OAAO;MACZC,KAAK,EAAE;KACR,EACD;MACEF,KAAK,EAAE,CAAC;MACRC,GAAG,EAAE,aAAa;MAClBC,KAAK,EAAE;KACR,CACF;IAED,KAAAC,gBAAgB,GAAG,CACjB;MACEH,KAAK,EAAE,IAAI;MACXC,GAAG,EAAE,KAAK;MACVC,KAAK,EAAE;KACR,EACD;MACEF,KAAK,EAAE,IAAI;MACXC,GAAG,EAAE,QAAQ;MACbC,KAAK,EAAE;KACR,EACD;MACEF,KAAK,EAAE,KAAK;MACZC,GAAG,EAAE,YAAY;MACjBC,KAAK,EAAE;KACR,CACF;IAKD,KAAAE,gBAAgB,GAAU,CAAC;MAAEF,KAAK,EAAE,IAAI;MAAEF,KAAK,EAAE;IAAE,CAAE,CAAC;IACtD,KAAAK,gBAAgB,GAAU,CAAC;MAAEH,KAAK,EAAE,IAAI;MAAEF,KAAK,EAAE;IAAE,CAAE,CAAC;IACtD,KAAAM,eAAe,GAAU,CAAC;MAAEJ,KAAK,EAAE,IAAI;MAAEF,KAAK,EAAE,CAAC;IAAC,CAAE,CAAC;IACrD,KAAAO,gBAAgB,GAAU,CAAC;MAAEL,KAAK,EAAE,IAAI;MAAEF,KAAK,EAAE,CAAC;IAAC,CAAE,CAAC;IACtD,KAAAQ,gBAAgB,GAAU,CAAC;MAAEN,KAAK,EAAE,IAAI;MAAEF,KAAK,EAAE,CAAC;IAAC,CAAE,CAAC;IACtD,KAAAS,iBAAiB,GAAU,CAAC;MAAEP,KAAK,EAAE,IAAI;MAAEF,KAAK,EAAE,CAAC;IAAC,CAAE,CAAC;IAEvD,KAAAU,OAAO,GAAG;MACRJ,eAAe,EAAE,IAAI,CAACxB,UAAU,CAAC6B,cAAc,CAACtC,iBAAiB,CAAC;MAClEmC,gBAAgB,EAAE,IAAI,CAAC1B,UAAU,CAAC6B,cAAc,CAACpC,aAAa,CAAC;MAC/DgC,gBAAgB,EAAE,IAAI,CAACzB,UAAU,CAAC6B,cAAc,CAACrC,aAAa;KAC/D;IAGD,KAAAsC,UAAU,GAAG;MACXC,QAAQ,EAAE,CAAC;MACXC,KAAK,EAAE,EAAE;MACTC,SAAS,EAAE,KAAK;MAChBC,UAAU,EAAE,CAAC;MACbC,SAAS,EAAE,KAAK;MAChBC,aAAa,EAAE,EAAE;MACjBC,WAAW,EAAE,EAAE;MACfC,SAAS,EAAE,EAAE;MACbC,UAAU,EAAE,CAAC;MACbC,UAAU,EAAE,EAAE;MACdC,MAAM,EAAE;KACT;IAED,KAAAC,cAAc,GAAoB,EAAE;IACpC,KAAAC,WAAW,GAAW,CAAC;IACvB,KAAAC,YAAY,GAAQ,IAAI;IA8GxB,KAAAC,YAAY,GAAgB,IAAI;IAwKhC,KAAAC,uBAAuB,GAAU,CAC/B;MACE5B,KAAK,EAAE,EAAE;MAAEE,KAAK,EAAE;KACnB,CACF;IAxWC,IAAI,CAACX,aAAa,CAACsC,OAAO,EAAE,CAACC,IAAI,CAC/B5D,GAAG,CAAE6D,GAAW,IAAI;MAClB,IAAIA,GAAG,CAACC,MAAM,kDAA4B,CAAC,CAACD,GAAG,CAACE,OAAO,EAAE;QACvD,IAAI,CAACvC,eAAe,GAAGqC,GAAG,CAACE,OAAO;MACpC;IACF,CAAC,CAAC,CACH,CAACC,SAAS,EAAE;EACf;EAyESC,QAAQA,CAAA;IACf,IAAI,CAAC7B,eAAe,GAAG,CACrB,GAAG,IAAI,CAACA,eAAe,EACvB,GAAG,IAAI,CAACxB,UAAU,CAAC6B,cAAc,CAACtC,iBAAiB,CAAC,CACrD;IACD,IAAI,CAACkC,gBAAgB,GAAG,CACtB,GAAG,IAAI,CAACA,gBAAgB,EACxB,GAAG,IAAI,CAACzB,UAAU,CAAC6B,cAAc,CAACrC,aAAa,CAAC,CACjD;IACD,IAAI,CAACkC,gBAAgB,GAAG,CACtB,GAAG,IAAI,CAACA,gBAAgB,EACxB,GAAG,IAAI,CAAC1B,UAAU,CAAC6B,cAAc,CAACpC,aAAa,CAAC,CACjD;IACD,IAAI,CAACkC,iBAAiB,GAAG,CACvB,GAAG,IAAI,CAACA,iBAAiB,EACzB,GAAG,IAAI,CAAC3B,UAAU,CAAC6B,cAAc,CAACnC,cAAc,CAAC,CAClD;IAED,IAAIC,mBAAmB,CAAC2D,iBAAiB,CAAC1D,WAAW,CAAC2D,YAAY,CAAC,IAAI,IAAI,IACtE5D,mBAAmB,CAAC2D,iBAAiB,CAAC1D,WAAW,CAAC2D,YAAY,CAAC,IAAIC,SAAS,IAC5E7D,mBAAmB,CAAC2D,iBAAiB,CAAC1D,WAAW,CAAC2D,YAAY,CAAC,IAAI,EAAE,EAAE;MAC1E,IAAIE,eAAe,GAAGC,IAAI,CAACC,KAAK,CAAChE,mBAAmB,CAAC2D,iBAAiB,CAAC1D,WAAW,CAAC2D,YAAY,CAAC,CAAC;MACjG,IAAI,CAACK,WAAW,GAAG;QACjBC,kBAAkB,EAAE,IAAI;QACxB;QACA;QACA;QACAC,kBAAkB,EAAEL,eAAe,CAACK,kBAAkB,IAAI,IAAI,IAAIL,eAAe,CAACK,kBAAkB,IAAIN,SAAS,GAC7G,IAAI,CAACjC,gBAAgB,CAACwC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC9C,KAAK,IAAIuC,eAAe,CAACK,kBAAkB,CAAC5C,KAAK,CAAC,GACpF,IAAI,CAACK,gBAAgB,CAAC,CAAC,CAAC;QAC5B0C,kBAAkB,EAAER,eAAe,CAACQ,kBAAkB,IAAI,IAAI,IAAIR,eAAe,CAACQ,kBAAkB,IAAIT,SAAS,GAC7G,IAAI,CAAC/B,gBAAgB,CAACsC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC9C,KAAK,IAAIuC,eAAe,CAACQ,kBAAkB,CAAC/C,KAAK,CAAC,GACpF,IAAI,CAACO,gBAAgB,CAAC,CAAC,CAAC;QAC5ByC,kBAAkB,EAAET,eAAe,CAACS,kBAAkB,IAAI,IAAI,IAAIT,eAAe,CAACS,kBAAkB,IAAIV,SAAS,GAC7G,IAAI,CAAC9B,gBAAgB,CAACqC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC9C,KAAK,IAAIuC,eAAe,CAACS,kBAAkB,CAAChD,KAAK,CAAC,GACpF,IAAI,CAACQ,gBAAgB,CAAC,CAAC,CAAC;QAC5ByC,iBAAiB,EAAEV,eAAe,CAACU,iBAAiB,IAAI,IAAI,IAAIV,eAAe,CAACU,iBAAiB,IAAIX,SAAS,GAC1G,IAAI,CAAChC,eAAe,CAACuC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC9C,KAAK,IAAIuC,eAAe,CAACU,iBAAiB,CAACjD,KAAK,CAAC,GAClF,IAAI,CAACM,eAAe,CAAC,CAAC,CAAC;QAC3B4C,mBAAmB,EAAEX,eAAe,CAACW,mBAAmB,IAAI,IAAI,IAAIX,eAAe,CAACW,mBAAmB,IAAIZ,SAAS,GAChH,IAAI,CAAC7B,iBAAiB,CAACoC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC9C,KAAK,IAAIuC,eAAe,CAACW,mBAAmB,CAAClD,KAAK,CAAC,GACtF,IAAI,CAACS,iBAAiB,CAAC,CAAC,CAAC;QAC7B0C,gBAAgB,EAAEZ,eAAe,CAACY,gBAAgB,IAAI,IAAI,IAAIZ,eAAe,CAACY,gBAAgB,IAAIb,SAAS,GACvG,IAAI,CAACnC,gBAAgB,CAAC0C,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC9C,KAAK,IAAIuC,eAAe,CAACY,gBAAgB,CAACnD,KAAK,CAAC,GAClF,IAAI,CAACG,gBAAgB,CAAC,CAAC,CAAC;QAC5BiD,KAAK,EAAEb,eAAe,CAACa,KAAK,IAAI,IAAI,IAAIb,eAAe,CAACa,KAAK,IAAId,SAAS,GACtEC,eAAe,CAACa,KAAK,GACrB,EAAE;QACNC,GAAG,EAAEd,eAAe,CAACc,GAAG,IAAI,IAAI,IAAId,eAAe,CAACc,GAAG,IAAIf,SAAS,GAChEC,eAAe,CAACc,GAAG,GACnB;OACL;IACH,CAAC,MACI;MACH,IAAI,CAACX,WAAW,GAAG;QACjBC,kBAAkB,EAAE,IAAI;QACxBW,qBAAqB,EAAE,IAAI,CAAC1B,uBAAuB,CAAC,CAAC,CAAC;QACtDgB,kBAAkB,EAAE,IAAI,CAACvC,gBAAgB,CAAC,CAAC,CAAC;QAC5C0C,kBAAkB,EAAE,IAAI,CAACxC,gBAAgB,CAAC,CAAC,CAAC;QAC5CyC,kBAAkB,EAAE,IAAI,CAACxC,gBAAgB,CAAC,CAAC,CAAC;QAC5CyC,iBAAiB,EAAE,IAAI,CAAC3C,eAAe,CAAC,CAAC,CAAC;QAC1C4C,mBAAmB,EAAE,IAAI,CAACzC,iBAAiB,CAAC,CAAC,CAAC;QAC9C0C,gBAAgB,EAAE,IAAI,CAAChD,gBAAgB,CAAC,CAAC,CAAC;QAC1CiD,KAAK,EAAE,EAAE;QACTC,GAAG,EAAE;OACN;IACH;IACA,IAAI,CAACE,gBAAgB,EAAE;EACzB;EAEAC,QAAQA,CAAA;IACN,IAAIC,WAAW,GAAG;MAChBd,kBAAkB,EAAE,IAAI,CAACD,WAAW,CAACC,kBAAkB;MACvD;MACAS,KAAK,EAAE,IAAI,CAACV,WAAW,CAACU,KAAK;MAC7BC,GAAG,EAAE,IAAI,CAACX,WAAW,CAACW,GAAG;MACzBT,kBAAkB,EAAE,IAAI,CAACF,WAAW,CAACE,kBAAkB;MACvDG,kBAAkB,EAAE,IAAI,CAACL,WAAW,CAACK,kBAAkB;MACvDI,gBAAgB,EAAE,IAAI,CAACT,WAAW,CAACS,gBAAgB;MACnDH,kBAAkB,EAAE,IAAI,CAACN,WAAW,CAACM,kBAAkB;MACvDC,iBAAiB,EAAE,IAAI,CAACP,WAAW,CAACO,iBAAiB;MACrDC,mBAAmB,EAAE,IAAI,CAACR,WAAW,CAACQ;KACvC;IACDzE,mBAAmB,CAACiF,iBAAiB,CAAChF,WAAW,CAAC2D,YAAY,EAAEG,IAAI,CAACmB,SAAS,CAACF,WAAW,CAAC,CAAC;IAC5F,IAAI,CAACG,YAAY,EAAE,CAAC1B,SAAS,EAAE;EACjC;EAEA2B,WAAWA,CAACC,OAAe;IACzB,IAAI,CAACjE,SAAS,GAAGiE,OAAO;IACxB,IAAI,CAACF,YAAY,EAAE,CAAC1B,SAAS,EAAE;EACjC;EAEA6B,WAAWA,CAAA;IACT,IAAI,IAAI,CAACrB,WAAW,CAACC,kBAAkB,CAACqB,GAAG,EAAE;MAC3C,IAAI,CAAC9E,aAAa,CAAC+E,4BAA4B,CAAC;QAC9CC,YAAY,EAAE,IAAI,CAACxB,WAAW,CAACC,kBAAkB,CAACqB;OACnD,CAAC,CAAC9B,SAAS,CAACH,GAAG,IAAG;QACjB,IAAIA,GAAG,CAACoC,OAAO,IAAIpC,GAAG,CAACqC,UAAU,IAAI,CAAC,EAAE;UACtC,IAAI,CAAC5E,gBAAgB,CAAC6E,iBAAiB,CACrCtC,GAAG,CAACoC,OAAO,EAAE,QAAQ,CACtB;QACH,CAAC,MAAM;UACL,IAAI,CAACnF,OAAO,CAACsF,YAAY,CAACvC,GAAG,CAACwC,OAAQ,CAAC;QACzC;MACF,CAAC,CAAC;IACJ;EACF;EAMAC,gBAAgBA,CAAA;IACd,IAAI,CAACC,SAAS,CAACC,aAAa,CAACC,KAAK,EAAE;EACtC;EAEAC,cAAcA,CAACC,KAAY;IACzB,MAAMC,KAAK,GAAGD,KAAK,CAACE,MAA0B;IAC9C,IAAID,KAAK,CAACE,KAAK,IAAIF,KAAK,CAACE,KAAK,CAACC,MAAM,GAAG,CAAC,EAAE;MACzC,IAAI,CAACtD,YAAY,GAAGmD,KAAK,CAACE,KAAK,CAAC,CAAC,CAAC;MAClC,IAAI,CAACE,WAAW,EAAE;IACpB;EACF;EAEAA,WAAWA,CAAA;IACT,IAAI,IAAI,CAACvD,YAAY,EAAE;MACrB,MAAMwD,QAAQ,GAAG,IAAIC,QAAQ,EAAE;MAC/BD,QAAQ,CAACE,MAAM,CAAC,OAAO,EAAE,IAAI,CAAC1D,YAAY,CAAC;MAC3C,IAAI,CAACzC,aAAa,CAACoG,4BAA4B,CAAC;QAC9CC,IAAI,EAAE;UACJrB,YAAY,EAAE,IAAI,CAACxB,WAAW,CAACC,kBAAkB,CAACqB,GAAG;UACrDwB,KAAK,EAAE,IAAI,CAAC7D;;OAEf,CAAC,CAACO,SAAS,CAACH,GAAG,IAAG;QACjB,IAAIA,GAAG,CAACqC,UAAU,KAAK,CAAC,EAAE;UACxB,IAAI,CAACpF,OAAO,CAACyG,aAAa,CAAC1D,GAAG,CAACwC,OAAQ,CAAC;UACxC,IAAI,CAACX,YAAY,EAAE,CAAC1B,SAAS,EAAE;QACjC,CAAC,MAAM;UACL,IAAI,CAAClD,OAAO,CAACsF,YAAY,CAACvC,GAAG,CAACwC,OAAQ,CAAC;QACzC;MACF,CAAC,CAAC;IACJ;EACF;EAGAmB,gBAAgBA,CAAA;IACd,IAAI,CAACxG,aAAa,CAACyG,iCAAiC,CAAC;MACnDJ,IAAI,EAAE;QAAErB,YAAY,EAAE,IAAI,CAACxB,WAAW,CAACC,kBAAkB,CAACqB;MAAG;KAC9D,CAAC,CAAC9B,SAAS,CAACH,GAAG,IAAG;MACjB,IAAIA,GAAG,CAACoC,OAAO,IAAIpC,GAAG,CAACqC,UAAU,IAAI,CAAC,EAAE;QACtC,IAAI,CAAC/D,gBAAgB,GAAG,CAAC;UACvBL,KAAK,EAAE,EAAE;UAAEE,KAAK,EAAE;SACnB,EAAE,GAAG6B,GAAG,CAACoC,OAAO,CAACyB,GAAG,CAACC,CAAC,IAAG;UACxB,OAAO;YAAE7F,KAAK,EAAE6F,CAAC;YAAE3F,KAAK,EAAE2F;UAAC,CAAE;QAC/B,CAAC,CAAC,CAAC;QACH,IAAIpH,mBAAmB,CAAC2D,iBAAiB,CAAC1D,WAAW,CAAC2D,YAAY,CAAC,IAAI,IAAI,IACtE5D,mBAAmB,CAAC2D,iBAAiB,CAAC1D,WAAW,CAAC2D,YAAY,CAAC,IAAIC,SAAS,IAC5E7D,mBAAmB,CAAC2D,iBAAiB,CAAC1D,WAAW,CAAC2D,YAAY,CAAC,IAAI,EAAE,EAAE;UAC1E,IAAIE,eAAe,GAAGC,IAAI,CAACC,KAAK,CAAChE,mBAAmB,CAAC2D,iBAAiB,CAAC1D,WAAW,CAAC2D,YAAY,CAAC,CAAC;UACjG,IAAIE,eAAe,CAACK,kBAAkB,IAAI,IAAI,IAAIL,eAAe,CAACK,kBAAkB,IAAIN,SAAS,EAAE;YACjG,IAAIwD,KAAK,GAAG,IAAI,CAACzF,gBAAgB,CAAC0F,SAAS,CAAEjD,CAAM,IAAKA,CAAC,CAAC9C,KAAK,IAAIuC,eAAe,CAACK,kBAAkB,CAAC5C,KAAK,CAAC;YAC5G,IAAI,CAAC0C,WAAW,CAACE,kBAAkB,GAAG,IAAI,CAACvC,gBAAgB,CAACyF,KAAK,CAAC;UACpE,CAAC,MAAM;YACL,IAAI,CAACpD,WAAW,CAACE,kBAAkB,GAAG,IAAI,CAACvC,gBAAgB,CAAC,CAAC,CAAC;UAChE;QACF;MACF;IACF,CAAC,CAAC;EACJ;EAKA2F,WAAWA,CAAA;IACT,IAAI,CAACC,WAAW,GAAG;MACjB/B,YAAY,EAAE,IAAI,CAACxB,WAAW,CAACC,kBAAkB,CAACqB,GAAG;MACrDkC,SAAS,EAAE,IAAI,CAACrG,SAAS;MACzBsG,QAAQ,EAAE,IAAI,CAACvG;KAChB;IAED;IACA;IACA;IACA,IAAI,IAAI,CAAC8C,WAAW,CAACU,KAAK,IAAI,IAAI,CAACV,WAAW,CAACW,GAAG,EAAE;MAClD,IAAI,CAAC4C,WAAW,CAAC,QAAQ,CAAC,GAAG;QAAE7C,KAAK,EAAE,IAAI,CAACV,WAAW,CAACU,KAAK;QAAEC,GAAG,EAAE,IAAI,CAACX,WAAW,CAACW;MAAG,CAAE;IAC3F;IACA,IAAI,IAAI,CAACX,WAAW,CAACE,kBAAkB,EAAE;MACvC,IAAI,CAACqD,WAAW,CAAC,YAAY,CAAC,GAAG,IAAI,CAACvD,WAAW,CAACE,kBAAkB,CAAC5C,KAAK;IAC5E;IACA,IAAI,IAAI,CAAC0C,WAAW,CAACK,kBAAkB,CAAC/C,KAAK,EAAE;MAC7C,IAAI,CAACiG,WAAW,CAAC,YAAY,CAAC,GAAG,IAAI,CAACvD,WAAW,CAACK,kBAAkB,CAAC/C,KAAK;IAC5E;IACA,IAAI,OAAO,IAAI,CAAC0C,WAAW,CAACS,gBAAgB,CAACnD,KAAK,KAAK,SAAS,EAAE;MAChE,IAAI,CAACiG,WAAW,CAAC,WAAW,CAAC,GAAG,IAAI,CAACvD,WAAW,CAACS,gBAAgB,CAACnD,KAAK;IACzE;IACA,IAAI,IAAI,CAAC0C,WAAW,CAACM,kBAAkB,CAAChD,KAAK,EAAE;MAC7C,IAAI,CAACiG,WAAW,CAAC,YAAY,CAAC,GAAG,IAAI,CAACvD,WAAW,CAACM,kBAAkB,CAAChD,KAAK;IAC5E;IACA,IAAI,IAAI,CAAC0C,WAAW,CAACO,iBAAiB,CAACjD,KAAK,EAAE;MAC5C,IAAI,CAACiG,WAAW,CAAC,WAAW,CAAC,GAAG,IAAI,CAACvD,WAAW,CAACO,iBAAiB,CAACjD,KAAK;IAC1E;IACA,IAAI,IAAI,CAAC0C,WAAW,CAACQ,mBAAmB,CAAClD,KAAK,EAAE;MAC9C,IAAI,CAACiG,WAAW,CAAC,aAAa,CAAC,GAAG,IAAI,CAACvD,WAAW,CAACQ,mBAAmB,CAAClD,KAAK;IAC9E;IAEA,OAAO,IAAI,CAACiG,WAAW;EACzB;EAEAG,qBAAqBA,CAACC,GAAsB;IAC1C,OAAOA,GAAG,CAACC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK,CAACA,CAAC,CAACC,MAAM,IAAI,CAAC,KAAKF,CAAC,CAACE,MAAM,IAAI,CAAC,CAAC,CAAC;EAC9D;EAEA7C,YAAYA,CAAA;IACV,OAAO,IAAI,CAAC1E,aAAa,CAACwH,6BAA6B,CAAC;MACtDnB,IAAI,EAAE,IAAI,CAACS,WAAW;KACvB,CAAC,CAAClE,IAAI,CACL5D,GAAG,CAAC6D,GAAG,IAAG;MACR,IAAIA,GAAG,CAACoC,OAAO,IAAIpC,GAAG,CAACqC,UAAU,IAAI,CAAC,EAAE;QACtC,IAAI,CAACuC,SAAS,GAAG5E,GAAG,CAACoC,OAAO;QAC5B,IAAI,CAACrE,YAAY,GAAGiC,GAAG,CAAC6E,UAAW;MACrC;IACF,CAAC,CAAC,CACH;EACH;EAIAC,0BAA0BA,CAAA;IACxB;IACA,IAAI,CAACnB,gBAAgB,EAAE;IACvB,IAAI,CAAC9B,YAAY,EAAE,CAAC1B,SAAS,EAAE;EACjC;EACAqB,gBAAgBA,CAAA;IACd,IAAI,CAACnE,iBAAiB,CAAC0H,6CAA6C,CAAC;MACnEvB,IAAI,EAAE;QACJwB,OAAO,EAAE,KAAK;QACdC,OAAO,EAAE;;KAEZ,CAAC,CAAClF,IAAI,CACL5D,GAAG,CAAC6D,GAAG,IAAG;MACR,IAAIA,GAAG,CAACoC,OAAO,IAAIpC,GAAG,CAACqC,UAAU,IAAI,CAAC,EAAE;QACtC,IAAI,CAAC6C,oBAAoB,GAAGlF,GAAG,CAACoC,OAAO,EAAEc,MAAM,GAAGlD,GAAG,CAACoC,OAAO,CAACyB,GAAG,CAAC7D,GAAG,IAAG;UACtE,OAAO;YACLmF,cAAc,EAAEnF,GAAG,CAACmF,cAAc;YAClClD,GAAG,EAAEjC,GAAG,CAACiC;WACV;QACH,CAAC,CAAC,GAAG,EAAE;QAEP,IAAIvF,mBAAmB,CAAC2D,iBAAiB,CAAC1D,WAAW,CAAC2D,YAAY,CAAC,IAAI,IAAI,IACtE5D,mBAAmB,CAAC2D,iBAAiB,CAAC1D,WAAW,CAAC2D,YAAY,CAAC,IAAIC,SAAS,IAC5E7D,mBAAmB,CAAC2D,iBAAiB,CAAC1D,WAAW,CAAC2D,YAAY,CAAC,IAAI,EAAE,EAAE;UAC1E,IAAIE,eAAe,GAAGC,IAAI,CAACC,KAAK,CAAChE,mBAAmB,CAAC2D,iBAAiB,CAAC1D,WAAW,CAAC2D,YAAY,CAAC,CAAC;UACjG,IAAIE,eAAe,CAACI,kBAAkB,IAAI,IAAI,IAAIJ,eAAe,CAACI,kBAAkB,IAAIL,SAAS,EAAE;YACjG,IAAIwD,KAAK,GAAG,IAAI,CAACmB,oBAAoB,CAAClB,SAAS,CAAEjD,CAAM,IAAKA,CAAC,CAACkB,GAAG,IAAIzB,eAAe,CAACI,kBAAkB,CAACqB,GAAG,CAAC;YAC5G,IAAI,CAACtB,WAAW,CAACC,kBAAkB,GAAG,IAAI,CAACsE,oBAAoB,CAACnB,KAAK,CAAC;UACxE,CAAC,MAAM;YACL,IAAI,CAACpD,WAAW,CAACC,kBAAkB,GAAG,IAAI,CAACsE,oBAAoB,CAAC,CAAC,CAAC;UACpE;QACF,CAAC,MACI;UACH,IAAI,CAACvE,WAAW,CAACC,kBAAkB,GAAG,IAAI,CAACsE,oBAAoB,CAAC,CAAC,CAAC;QACpE;MACF;IACF,CAAC,CAAC,EACF/I,GAAG,CAAC,MAAK;MACP;MACA,IAAI,CAACwH,gBAAgB,EAAE;MACvByB,UAAU,CAAC,MAAK;QACd,IAAI,CAACvD,YAAY,EAAE,CAAC1B,SAAS,EAAE;MACjC,CAAC,EAAE,GAAG,CAAC;IACT,CAAC,CAAC,CACH,CAACA,SAAS,EAAE;EACf;EA6CAkF,YAAYA,CAACC,GAAQ,EAAEC,GAAQ;IAC7B,IAAI,CAACC,cAAc,GAAG,EAAE;IACxB,IAAI,CAACrI,aAAa,CAACsI,6BAA6B,CAAC;MAC/CjC,IAAI,EAAE;QAAE1E,QAAQ,EAAEwG;MAAG;KACtB,CAAC,CAACnF,SAAS,CAACH,GAAG,IAAG;MACjB,IAAIA,GAAG,CAACoC,OAAO,IAAIpC,GAAG,CAACqC,UAAU,IAAI,CAAC,EAAE;QACtC,IAAI,CAACqD,WAAW,GAAG;UACjB,GAAG1F,GAAG,CAACoC,OAAO;UACduD,eAAe,EAAE3F,GAAG,CAACoC,OAAO,CAACwD,gBAAgB,GAAG,IAAIC,IAAI,CAAC7F,GAAG,CAACoC,OAAO,CAACwD,gBAAgB,CAAC,GAAGrF,SAAS;UAClGuF,aAAa,EAAE9F,GAAG,CAACoC,OAAO,CAAC2D,cAAc,GAAG,IAAIF,IAAI,CAAC7F,GAAG,CAACoC,OAAO,CAAC2D,cAAc,CAAC,GAAGxF;SACpF;QAED,IAAIP,GAAG,CAACoC,OAAO,CAAC4D,YAAY,EAAE;UAC5B,IAAI,CAACR,cAAc,CAAC5E,kBAAkB,GAAG,IAAI,CAACqF,eAAe,CAAC,IAAI,CAACf,oBAAoB,EAAE,KAAK,EAAElF,GAAG,CAACoC,OAAO,CAAC4D,YAAY,CAAC;QAC3H;QACA,IAAI,CAACR,cAAc,CAACvE,kBAAkB,GAAG,IAAI,CAACgF,eAAe,CAAC,IAAI,CAACtH,OAAO,CAACF,gBAAgB,EAAE,OAAO,EAAEuB,GAAG,CAACoC,OAAO,CAACnD,UAAU,CAAC;QAC7H,IAAIe,GAAG,CAACoC,OAAO,CAAC9C,UAAU,EAAE;UAC1B,IAAI,CAACkG,cAAc,CAACxE,kBAAkB,GAAG,IAAI,CAACiF,eAAe,CAAC,IAAI,CAACtH,OAAO,CAACH,gBAAgB,EAAE,OAAO,EAAEwB,GAAG,CAACoC,OAAO,CAAC9C,UAAU,CAAC;QAC/H,CAAC,MAAM;UACL,IAAI,CAACkG,cAAc,CAACxE,kBAAkB,GAAG,IAAI,CAACrC,OAAO,CAACH,gBAAgB,CAAC,CAAC,CAAC;QAC3E;QACA,IAAI,CAACgH,cAAc,CAACtE,iBAAiB,GAAG,IAAI,CAAC+E,eAAe,CAAC,IAAI,CAACtH,OAAO,CAACJ,eAAe,EAAE,OAAO,EAAEyB,GAAG,CAACoC,OAAO,CAAC/C,SAAS,CAAC;QAE1H,IAAIW,GAAG,CAACoC,OAAO,CAAC4D,YAAY,EAAE;UAC5B,IAAI,IAAI,CAACE,aAAa,EAAE;YACtB,IAAI,CAACA,aAAa,CAAC/D,YAAY,GAAGnC,GAAG,CAACoC,OAAO,CAAC4D,YAAY;UAC5D;QACF;QACA,IAAI,CAAChJ,aAAa,CAACmJ,IAAI,CAACZ,GAAG,CAAC;MAC9B;IAEF,CAAC,CAAC;EACJ;EAGAU,eAAeA,CAACG,KAAY,EAAElI,GAAW,EAAED,KAAU;IACnD,OAAOmI,KAAK,CAACtF,IAAI,CAACuF,IAAI,IAAIA,IAAI,CAACnI,GAAG,CAAC,KAAKD,KAAK,CAAC;EAChD;EAGAqI,eAAeA,CAACf,GAAQ,EAAEc,IAAS;IACjC,IAAI,CAAChB,YAAY,CAACgB,IAAI,CAACf,GAAG,EAAEC,GAAG,CAAC;EAClC;EAEAgB,SAASA,CAAChB,GAAQ;IAChB,IAAI,CAACW,aAAa,GAAG;MACnBM,aAAa,EAAE,EAAE;MACjB9B,MAAM,EAAEnE,SAAS;MACjBkG,eAAe,EAAElG;KAClB;IACD,IAAI,CAACvD,aAAa,CAACmJ,IAAI,CAACZ,GAAG,CAAC;EAC9B;EAKAmB,UAAUA,CAACC,WAAmB;IAC5B,IAAIA,WAAW,EAAE;MACf,OAAOtK,MAAM,CAACsK,WAAW,CAAC,CAACC,MAAM,CAAC,qBAAqB,CAAC;IAC1D;IACA,OAAO,EAAE;EACX;EAEAC,cAAcA,CAACtB,GAAQ;IACrB,IAAI,CAACG,WAAW,CAACE,gBAAgB,GAAG,IAAI,CAACF,WAAW,CAACC,eAAe,GAAG,IAAI,CAACe,UAAU,CAAC,IAAI,CAAChB,WAAW,CAACC,eAAe,CAAC,GAAG,EAAE,EAC3H,IAAI,CAACD,WAAW,CAACK,cAAc,GAAG,IAAI,CAACL,WAAW,CAACI,aAAa,GAAG,IAAI,CAACY,UAAU,CAAC,IAAI,CAAChB,WAAW,CAACI,aAAa,CAAC,GAAG,EAAE,EAEvH,IAAI,CAACgB,kBAAkB,GAAG;MACxB3H,aAAa,EAAE,IAAI,CAACuG,WAAW,CAACvG,aAAa;MAC7CI,UAAU,EAAE,IAAI,CAACmG,WAAW,CAACqB,UAAU;MACvCjI,QAAQ,EAAE,IAAI,CAAC4G,WAAW,CAACsB,GAAG;MAC9B1H,UAAU,EAAE,IAAI,CAACkG,cAAc,CAACxE,kBAAkB,GAAG,IAAI,CAACwE,cAAc,CAACxE,kBAAkB,CAAC/C,KAAK,GAAG,IAAI;MACxGe,SAAS,EAAE,IAAI,CAAC0G,WAAW,CAAC1G,SAAS;MACrCE,SAAS,EAAE,IAAI,CAACwG,WAAW,CAACxG,SAAS;MACrCH,KAAK,EAAE,IAAI,CAAC2G,WAAW,CAAC3G,KAAK;MAC7BK,WAAW,EAAE,IAAI,CAACsG,WAAW,CAACuB,WAAW;MACzChI,UAAU,EAAE,IAAI,CAACuG,cAAc,CAACvE,kBAAkB,GAAG,IAAI,CAACuE,cAAc,CAACvE,kBAAkB,CAAChD,KAAK,GAAG,IAAI;MACxGuB,MAAM,EAAE,IAAI,CAACkG,WAAW,CAAClG,MAAM;MAC/BH,SAAS,EAAE,IAAI,CAACmG,cAAc,CAACtE,iBAAiB,GAAG,IAAI,CAACsE,cAAc,CAACtE,iBAAiB,CAACjD,KAAK,GAAG,IAAI;MACrG2H,gBAAgB,EAAE,IAAI,CAACF,WAAW,CAACE,gBAAgB;MACnDG,cAAc,EAAE,IAAI,CAACL,WAAW,CAACK;KAClC;IACH,IAAI,CAACmB,UAAU,EAAE;IACjB,IAAI,IAAI,CAAChK,KAAK,CAACiK,aAAa,CAACjE,MAAM,GAAG,CAAC,EAAE;MACvC,IAAI,CAACjG,OAAO,CAACmK,aAAa,CAAC,IAAI,CAAClK,KAAK,CAACiK,aAAa,CAAC;MACpD;IACF;IAEA,IAAI,CAAChK,aAAa,CAACkK,0BAA0B,CAAC;MAC5C7D,IAAI,EAAE,IAAI,CAACsD;KACZ,CAAC,CAAC/G,IAAI,CACL5D,GAAG,CAAC6D,GAAG,IAAG;MACR,IAAIA,GAAG,CAACqC,UAAU,KAAK,CAAC,EAAE;QACxB,IAAI,CAACpF,OAAO,CAACyG,aAAa,CAAC,MAAM,CAAC;QAClC6B,GAAG,CAAC+B,KAAK,EAAE;MACb,CAAC,MAAM;QACL,IAAI,CAACrK,OAAO,CAACsF,YAAY,CAACvC,GAAG,CAACwC,OAAQ,CAAC;QACvC+C,GAAG,CAAC+B,KAAK,EAAE;MACb;IACF,CAAC,CAAC,EACFpL,SAAS,CAAC,MAAM,IAAI,CAAC2F,YAAY,EAAE,CAAC,CACrC,CAAC1B,SAAS,EAAE;EACf;EAGAoH,QAAQA,CAAChC,GAAQ;IACf,IAAIiC,OAAO,GAAkB;MAC3BrI,aAAa,EAAE,IAAI,CAACuG,WAAW,CAACvG,aAAa;MAC7CI,UAAU,EAAE,IAAI,CAACmG,WAAW,CAACqB,UAAU;MACvCjI,QAAQ,EAAE,IAAI,CAAC4G,WAAW,CAACsB,GAAG;MAC9B1H,UAAU,EAAE,IAAI,CAACoG,WAAW,CAACpG,UAAU;MACvCN,SAAS,EAAE,IAAI,CAAC0G,WAAW,CAAC1G,SAAS;MACrCE,SAAS,EAAE,IAAI,CAACwG,WAAW,CAACxG,SAAS;MACrCH,KAAK,EAAE,IAAI,CAAC2G,WAAW,CAAC3G,KAAK;MAC7BK,WAAW,EAAE,IAAI,CAACsG,WAAW,CAACuB,WAAW;MACzChI,UAAU,EAAE,IAAI,CAACyG,WAAW,CAACzG,UAAU;MACvCO,MAAM,EAAE,IAAI,CAACkG,WAAW,CAAClG,MAAM;MAC/BH,SAAS,EAAE,IAAI,CAACqG,WAAW,CAACrG;KAC7B;IACD,IAAI,CAAClC,aAAa,CAACkK,0BAA0B,CAAC;MAC5C7D,IAAI,EAAEgE;KACP,CAAC,CAACrH,SAAS,CAACH,GAAG,IAAG;MACjB,IAAIA,GAAG,CAACqC,UAAU,KAAK,CAAC,EAAE;QACxB,IAAI,CAACpF,OAAO,CAACyG,aAAa,CAAC,MAAM,CAAC;QAClC6B,GAAG,CAAC+B,KAAK,EAAE;MACb;IACF,CAAC,CAAC;EAEJ;EAEAG,OAAOA,CAAClC,GAAQ;IACdA,GAAG,CAAC+B,KAAK,EAAE;EACb;EAEAI,YAAYA,CAACC,IAAS,EAAEC,EAAQ;IAC9B,MAAMC,KAAK,GAAGD,EAAE,GAAGA,EAAE,GAAG,IAAI,CAACjH,WAAW,CAACC,kBAAkB,CAACqB,GAAG;IAC/D,IAAI,CAAC1E,MAAM,CAACuK,QAAQ,CAAC,CAAC,+BAA+BH,IAAI,EAAE,EAAEE,KAAK,CAAC,CAAC;EACtE;EAEAE,4BAA4BA,CAACJ,IAAS,EAAEK,WAAgB,EAAEC,OAAY;IACpE,IAAI,CAAC1K,MAAM,CAACuK,QAAQ,CAAC,CAAC,+BAA+BH,IAAI,EAAE,EAAEK,WAAW,EAAEC,OAAO,CAAC,CAAC;EACrF;EAEAC,cAAcA,CAAC7B,IAAS;IACtB,IAAI8B,OAAO,CAAC,UAAU,CAAC,EAAE;MACvB,IAAI,CAAChL,aAAa,CAACiL,oCAAoC,CAAC;QACtD5E,IAAI,EAAE6C,IAAI,CAACf;OACZ,CAAC,CAACnF,SAAS,CAACH,GAAG,IAAG;QACjB,IAAIA,GAAG,CAACqC,UAAU,IAAI,CAAC,EAAE;UACvB,IAAI,CAACpF,OAAO,CAACyG,aAAa,CAAC,MAAM,CAAC;QACpC;MACF,CAAC,CAAC;IACJ;EACF;EAIAwD,UAAUA,CAAA;IACR,IAAI,CAAChK,KAAK,CAACmL,KAAK,EAAE;IAClB,IAAI,CAACnL,KAAK,CAACoL,QAAQ,CAAC,QAAQ,EAAE,IAAI,CAAC5C,WAAW,CAACsB,GAAG,CAAC;IACnD,IAAI,CAAC9J,KAAK,CAACoL,QAAQ,CAAC,QAAQ,EAAE,IAAI,CAACxB,kBAAkB,CAACvH,UAAU,CAAC;IACjE,IAAI,CAACrC,KAAK,CAACqL,iBAAiB,CAAC,MAAM,EAAE,IAAI,CAACzB,kBAAkB,CAACvH,UAAU,EAAE,EAAE,CAAC;IAC5E,IAAI,CAACrC,KAAK,CAACoL,QAAQ,CAAC,MAAM,EAAE,IAAI,CAAC5C,WAAW,CAAChB,MAAM,CAAC;IACpD,IAAI,CAACxH,KAAK,CAACqL,iBAAiB,CAAC,QAAQ,EAAE,IAAI,CAACzB,kBAAkB,CAAC3H,aAAa,EAAE,EAAE,CAAC;IACjF;IACA;IACA;IACA,IAAI,CAACjC,KAAK,CAACsL,OAAO,CAAC,QAAQ,EAAE,IAAI,CAAC1B,kBAAkB,CAAC/H,KAAK,EAAE,IAAI,CAACzB,OAAO,CAACmL,WAAW,CAAC;IACrF,IAAI,CAACvL,KAAK,CAACwL,aAAa,CAAC,QAAQ,EAAE,IAAI,CAAC5B,kBAAkB,CAACtH,MAAM,CAAC;IAClE,IAAI,CAACtC,KAAK,CAACoL,QAAQ,CAAC,MAAM,EAAE,IAAI,CAACxB,kBAAkB,CAACzH,SAAS,CAAC;IAC9D,IAAI,CAACnC,KAAK,CAACoL,QAAQ,CAAC,QAAQ,EAAE,IAAI,CAAC9C,cAAc,CAACxE,kBAAkB,CAAC/C,KAAK,CAAC;IAC3E,IAAI,CAACf,KAAK,CAACoL,QAAQ,CAAC,QAAQ,EAAE,IAAI,CAAC9C,cAAc,CAACvE,kBAAkB,CAAChD,KAAK,CAAC;IAC3E,IAAI,IAAI,CAACyH,WAAW,CAACE,gBAAgB,EAAE;MACrC,IAAI,CAAC1I,KAAK,CAACoL,QAAQ,CAAC,UAAU,EAAE,IAAI,CAAC5C,WAAW,CAACK,cAAc,CAAC;IAClE;IACA,IAAI,IAAI,CAACL,WAAW,CAACK,cAAc,EAAE;MACnC,IAAI,CAAC7I,KAAK,CAACoL,QAAQ,CAAC,UAAU,EAAE,IAAI,CAAC5C,WAAW,CAACE,gBAAgB,CAAC;IACpE;IACA,IAAI,CAAC1I,KAAK,CAACyL,mBAAmB,CAAC,QAAQ,EAAE,IAAI,CAACjD,WAAW,CAACE,gBAAgB,GAAG,IAAI,CAACF,WAAW,CAACE,gBAAgB,GAAG,EAAE,EAAE,IAAI,CAACF,WAAW,CAACK,cAAc,GAAG,IAAI,CAACL,WAAW,CAACK,cAAc,GAAG,EAAE,CAAC;EAC9L;EAEA6C,uBAAuBA,CAAA;IACrB,IAAI,CAAC1L,KAAK,CAACmL,KAAK,EAAE;IAClB,IAAI,CAACnL,KAAK,CAACoL,QAAQ,CAAC,MAAM,EAAE,IAAI,CAACpC,aAAa,CAAC/D,YAAY,CAAC;IAC5D,IAAI,CAACjF,KAAK,CAACoL,QAAQ,CAAC,MAAM,EAAE,IAAI,CAACpC,aAAa,CAACM,aAAa,CAAC;IAC7D,IAAI,CAACtJ,KAAK,CAACqL,iBAAiB,CAAC,MAAM,EAAE,IAAI,CAACrC,aAAa,CAACM,aAAa,EAAE,EAAE,CAAC;IAC1E,IAAI,CAACtJ,KAAK,CAAC2L,sBAAsB,CAAC,UAAU,EAAE,IAAI,CAAC3C,aAAa,CAACxB,MAAM,EAAE,CAAC,EAAE,GAAG,CAAC;IAChF,IAAI,CAACxH,KAAK,CAAC2L,sBAAsB,CAAC,SAAS,EAAE,IAAI,CAAC3C,aAAa,CAACO,eAAe,EAAE,CAAC,EAAE,GAAG,CAAC;EAC1F;EAGAqC,gBAAgBA,CAACvD,GAAQ;IACvB,IAAI,CAACW,aAAa,CAAC/D,YAAY,GAAG,IAAI,CAACxB,WAAW,CAACC,kBAAkB,CAACqB,GAAG,EACvE,IAAI,CAAC2G,uBAAuB,EAAE;IAChC,IAAI,IAAI,CAAC1L,KAAK,CAACiK,aAAa,CAACjE,MAAM,GAAG,CAAC,EAAE;MACvC,IAAI,CAACjG,OAAO,CAACmK,aAAa,CAAC,IAAI,CAAClK,KAAK,CAACiK,aAAa,CAAC;MACpD;IACF;IACA,IAAI,CAAC/J,qBAAqB,CAAC2L,yCAAyC,CAAC;MACnEvF,IAAI,EAAE,IAAI,CAAC0C;KACZ,CAAC,CAACnG,IAAI,CACL5D,GAAG,CAAC6D,GAAG,IAAG;MACR,IAAIA,GAAG,CAACqC,UAAU,KAAK,CAAC,EAAE;QACxB,IAAI,CAACpF,OAAO,CAACyG,aAAa,CAAC,MAAM,CAAC;QAClC6B,GAAG,CAAC+B,KAAK,EAAE;MACb;IACF,CAAC,CAAC,EACFpL,SAAS,CAAC,MAAM,IAAI,CAAC2F,YAAY,EAAE,CAAC,CACrC,CAAC1B,SAAS,EAAE;EACf;EAEA;EACM6I,aAAaA,CAACC,MAAW,EAAE5C,IAAS;IAAA,IAAA6C,KAAA;IAAA,OAAAC,iBAAA;MACxCD,KAAI,CAACvJ,YAAY,GAAG0G,IAAI;MACxB6C,KAAI,CAACzJ,cAAc,GAAG,EAAE;MACxByJ,KAAI,CAACxJ,WAAW,GAAG,CAAC;MAEpB;MACA,IAAI;QACF,MAAM0J,QAAQ,SAASF,KAAI,CAACxL,gBAAgB,CAAC2L,qBAAqB,CAAChD,IAAI,CAACf,GAAG,CAAC,CAACgE,SAAS,EAAE;QACxF,IAAIF,QAAQ,EAAEG,OAAO,IAAIH,QAAQ,CAACI,IAAI,EAAE;UACtCN,KAAI,CAACzJ,cAAc,GAAG2J,QAAQ,CAACI,IAAI;UACnCN,KAAI,CAACO,cAAc,EAAE;QACvB;MACF,CAAC,CAAC,OAAOC,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;MACnC;MAEAR,KAAI,CAAClM,aAAa,CAACmJ,IAAI,CAAC8C,MAAM,EAAE;QAC9BW,OAAO,EAAEvD,IAAI;QACbwD,oBAAoB,EAAE;OACvB,CAAC;IAAC;EACL;EAEA;EACAC,gBAAgBA,CAAA;IACd,IAAI,CAACrK,cAAc,CAACsK,IAAI,CAAC;MACvBC,QAAQ,EAAE,IAAI,CAACrK,YAAY,EAAE2F,GAAG,IAAI,CAAC;MACrC2E,SAAS,EAAE,EAAE;MACbC,UAAU,EAAE,CAAC;MACbC,MAAM,EAAE,CAAC;MACTC,OAAO,EAAE,CAAC;MACVC,QAAQ,EAAE,CAAC;MACXC,UAAU,EAAE;KACb,CAAC;EACJ;EAEA;EACMC,gBAAgBA,CAAA;IAAA,IAAAC,MAAA;IAAA,OAAArB,iBAAA;MACpB,IAAI;QACF,MAAMC,QAAQ,SAASoB,MAAI,CAAC9M,gBAAgB,CAAC+M,wBAAwB,EAAE,CAACnB,SAAS,EAAE;QACnF,IAAIF,QAAQ,EAAEG,OAAO,IAAIH,QAAQ,CAACI,IAAI,EAAE;UACtC,MAAMkB,YAAY,GAAGtB,QAAQ,CAACI,IAAI,CAAC3F,GAAG,CAACwC,IAAI,KAAK;YAC9C,GAAGA,IAAI;YACP2D,QAAQ,EAAEQ,MAAI,CAAC7K,YAAY,EAAE2F,GAAG,IAAI,CAAC;YACrC6E,MAAM,EAAE;WACT,CAAC,CAAC;UACHK,MAAI,CAAC/K,cAAc,CAACsK,IAAI,CAAC,GAAGW,YAAY,CAAC;UACzCF,MAAI,CAACf,cAAc,EAAE;QACvB;MACF,CAAC,CAAC,OAAOC,KAAK,EAAE;QACd;MAAA;IACD;EACH;EAEA;EACAiB,mBAAmBA,CAAC5G,KAAa;IAC/B,MAAMsC,IAAI,GAAG,IAAI,CAAC5G,cAAc,CAACsE,KAAK,CAAC;IACvC,IAAIsC,IAAI,CAACiE,UAAU,EAAE;MACnB;MACA;IACF;IACA,IAAI,CAAC7K,cAAc,CAACmL,MAAM,CAAC7G,KAAK,EAAE,CAAC,CAAC;IACpC,IAAI,CAAC0F,cAAc,EAAE;EACvB;EAEA;EACAA,cAAcA,CAAA;IACZ,IAAI,CAAC/J,WAAW,GAAG,IAAI,CAACD,cAAc,CAACoL,MAAM,CAAC,CAACC,GAAG,EAAEzE,IAAI,KAAI;MAC1D,OAAOyE,GAAG,GAAIzE,IAAI,CAAC6D,UAAU,GAAG7D,IAAI,CAAC8D,MAAO;IAC9C,CAAC,EAAE,CAAC,CAAC;EACP;EAEA;EACAY,cAAcA,CAACC,MAAc;IAC3B,OAAO,IAAIC,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;MACpCC,KAAK,EAAE,UAAU;MACjBC,QAAQ,EAAE,KAAK;MACfC,qBAAqB,EAAE;KACxB,CAAC,CAACzE,MAAM,CAACoE,MAAM,CAAC;EACnB;EAEA;EACMM,aAAaA,CAAC/F,GAAQ;IAAA,IAAAgG,MAAA;IAAA,OAAApC,iBAAA;MAC1B,IAAIoC,MAAI,CAAC9L,cAAc,CAACyD,MAAM,KAAK,CAAC,EAAE;QACpCqI,MAAI,CAACC,aAAa,CAACC,OAAO,CAAC,UAAU,CAAC;QACtC;MACF;MAEA;MACA,MAAMC,YAAY,GAAGH,MAAI,CAAC9L,cAAc,CAACkM,MAAM,CAACtF,IAAI,IAClD,CAACA,IAAI,CAAC4D,SAAS,CAAC2B,IAAI,EAAE,IAAIvF,IAAI,CAAC6D,UAAU,GAAG,CAAC,IAAI7D,IAAI,CAAC8D,MAAM,GAAG,CAAC,CACjE;MAED,IAAIuB,YAAY,CAACxI,MAAM,GAAG,CAAC,EAAE;QAC3BqI,MAAI,CAACC,aAAa,CAACC,OAAO,CAAC,uBAAuB,CAAC;QACnD;MACF;MAEA,IAAI;QACF,MAAMI,OAAO,GAAG;UACd5D,OAAO,EAAEsD,MAAI,CAAC5L,YAAY,CAAC2F,GAAG;UAC9BwG,KAAK,EAAEP,MAAI,CAAC9L;SACb;QAED,MAAM2J,QAAQ,SAASmC,MAAI,CAAC7N,gBAAgB,CAAC4N,aAAa,CAACO,OAAO,CAAC,CAACvC,SAAS,EAAE;QAC/E,IAAIF,QAAQ,EAAEG,OAAO,EAAE;UACrBgC,MAAI,CAACC,aAAa,CAACjC,OAAO,CAAC,SAAS,CAAC;UACrChE,GAAG,CAAC+B,KAAK,EAAE;QACb,CAAC,MAAM;UACLiE,MAAI,CAACC,aAAa,CAAC9B,KAAK,CAACN,QAAQ,EAAEnM,OAAO,IAAI,MAAM,CAAC;QACvD;MACF,CAAC,CAAC,OAAOyM,KAAK,EAAE;QACd6B,MAAI,CAACC,aAAa,CAAC9B,KAAK,CAAC,SAAS,CAAC;MACrC;IAAC;EACH;EAEA;EACMqC,eAAeA,CAAA;IAAA,IAAAC,MAAA;IAAA,OAAA7C,iBAAA;MACnB,IAAI;QACF,MAAM8C,IAAI,SAASD,MAAI,CAACtO,gBAAgB,CAACqO,eAAe,CAACC,MAAI,CAACrM,YAAY,CAAC2F,GAAG,CAAC,CAACgE,SAAS,EAAE;QAC3F,MAAM4C,GAAG,GAAGC,MAAM,CAACC,GAAG,CAACC,eAAe,CAACJ,IAAI,CAAC;QAC5C,MAAMK,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;QACxCF,IAAI,CAACG,IAAI,GAAGP,GAAG;QACfI,IAAI,CAACI,QAAQ,GAAG,OAAOV,MAAI,CAACrM,YAAY,CAACJ,UAAU,IAAIyM,MAAI,CAACrM,YAAY,CAAC+E,MAAM,OAAO;QACtF4H,IAAI,CAAC1J,KAAK,EAAE;QACZuJ,MAAM,CAACC,GAAG,CAACO,eAAe,CAACT,GAAG,CAAC;MACjC,CAAC,CAAC,OAAOxC,KAAK,EAAE;QACdsC,MAAI,CAACR,aAAa,CAAC9B,KAAK,CAAC,SAAS,CAAC;MACrC;IAAC;EACH;CACD;AAriByBkD,UAAA,EAAvB/Q,SAAS,CAAC,WAAW,CAAC,C,8DAAyC;AA/MrDe,4BAA4B,GAAAgQ,UAAA,EARxChR,SAAS,CAAC;EACTiR,QAAQ,EAAE,0BAA0B;EACpCC,WAAW,EAAE,uCAAuC;EACpDC,SAAS,EAAE,CAAC,uCAAuC,CAAC;EACpDC,UAAU,EAAE,IAAI;EAChBC,OAAO,EAAE,CAAClR,YAAY,EAAED,YAAY,EAAEE,kBAAkB,EAAEI,mBAAmB;CAC9E,CAAC,C,EAEWQ,4BAA4B,CAovBxC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}