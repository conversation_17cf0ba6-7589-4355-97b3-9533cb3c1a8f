{"ast": null, "code": "import { BaseComponent } from '../../components/base/baseComponent';\nimport { EEvent } from 'src/app/shared/services/event.service';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/shared/helper/allowHelper\";\nimport * as i2 from \"@nebular/theme\";\nimport * as i3 from \"src/services/api/services\";\nimport * as i4 from \"@angular/router\";\nimport * as i5 from \"@angular/common\";\nimport * as i6 from \"src/app/shared/services/message.service\";\nimport * as i7 from \"src/app/shared/services/event.service\";\nimport * as i8 from \"@angular/forms\";\nimport * as i9 from \"../../components/breadcrumb/breadcrumb.component\";\nfunction ModifyFloorPlanComponent_nb_option_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 28);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const building_r1 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", building_r1);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", building_r1.label, \" \");\n  }\n}\nfunction ModifyFloorPlanComponent_div_37_tr_3_th_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"th\")(1, \"div\", 33)(2, \"nb-checkbox\", 34);\n    i0.ɵɵlistener(\"checkedChange\", function ModifyFloorPlanComponent_div_37_tr_3_th_2_Template_nb_checkbox_checkedChange_2_listener($event) {\n      const idx_r3 = i0.ɵɵrestoreView(_r2).index;\n      const ctx_r3 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r3.enableAllAtIndex($event, idx_r3));\n    });\n    i0.ɵɵelementStart(3, \"span\", 35);\n    i0.ɵɵtext(4, \"\\u5168\\u9078\\u7121\\u6B64\\u6236\\u578B \");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const idx_r3 = ctx.index;\n    const ctx_r3 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"checked\", ctx_r3.isCheckAllColumnChecked(idx_r3));\n  }\n}\nfunction ModifyFloorPlanComponent_div_37_tr_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\");\n    i0.ɵɵelement(1, \"th\");\n    i0.ɵɵtemplate(2, ModifyFloorPlanComponent_div_37_tr_3_th_2_Template, 5, 1, \"th\", 32);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.houseList[0]);\n  }\n}\nfunction ModifyFloorPlanComponent_div_37_tr_5_td_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"td\")(1, \"div\", 33)(2, \"p\", 36);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"nb-checkbox\", 34);\n    i0.ɵɵtwoWayListener(\"checkedChange\", function ModifyFloorPlanComponent_div_37_tr_5_td_8_Template_nb_checkbox_checkedChange_4_listener($event) {\n      const house_r8 = i0.ɵɵrestoreView(_r7).$implicit;\n      i0.ɵɵtwoWayBindingSet(house_r8.CIsSelected, $event) || (house_r8.CIsSelected = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementStart(5, \"span\", 35);\n    i0.ɵɵtext(6, \"\\u7121\\u6B64\\u6236\\u578B\");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const house_r8 = ctx.$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate2(\"\", house_r8.CHouseHold || \"null\", \" - \", house_r8.CFloor, \"\");\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"checked\", house_r8.CIsSelected);\n  }\n}\nfunction ModifyFloorPlanComponent_div_37_tr_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\")(2, \"div\", 33)(3, \"p\");\n    i0.ɵɵtext(4, \"\\u00A0\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"nb-checkbox\", 34);\n    i0.ɵɵlistener(\"checkedChange\", function ModifyFloorPlanComponent_div_37_tr_5_Template_nb_checkbox_checkedChange_5_listener($event) {\n      const row_r6 = i0.ɵɵrestoreView(_r5).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r3.enableAllRow($event, row_r6));\n    });\n    i0.ɵɵelementStart(6, \"span\", 35);\n    i0.ɵɵtext(7, \"\\u5168\\u9078\\u7121\\u6B64\\u6236\\u578B\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵtemplate(8, ModifyFloorPlanComponent_div_37_tr_5_td_8_Template, 7, 3, \"td\", 32);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const row_r6 = ctx.$implicit;\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"checked\", ctx_r3.isCheckAllRowChecked(row_r6));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", row_r6);\n  }\n}\nfunction ModifyFloorPlanComponent_div_37_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 29)(1, \"table\", 30)(2, \"thead\");\n    i0.ɵɵtemplate(3, ModifyFloorPlanComponent_div_37_tr_3_Template, 3, 1, \"tr\", 31);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"tbody\");\n    i0.ɵɵtemplate(5, ModifyFloorPlanComponent_div_37_tr_5_Template, 9, 2, \"tr\", 32);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.houseList.length);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.houseList);\n  }\n}\nexport class ModifyFloorPlanComponent extends BaseComponent {\n  constructor(_allow, dialogService, _houseService, route, location, message, router, _eventService) {\n    super(_allow);\n    this._allow = _allow;\n    this.dialogService = dialogService;\n    this._houseService = _houseService;\n    this.route = route;\n    this.location = location;\n    this.message = message;\n    this.router = router;\n    this._eventService = _eventService;\n    this.buildingSelectedOptions = [{\n      value: '',\n      label: '全部'\n    }];\n    this.isHouseList = false;\n  }\n  getListBuilding() {\n    this._houseService.apiHouseGetListBuildingPost$Json({\n      body: {\n        CBuildCaseID: this.buildCaseId\n      }\n    }).subscribe(res => {\n      if (res.Entries && res.StatusCode == 0) {\n        this.buildingSelectedOptions = [{\n          value: '',\n          label: '全部'\n        }, ...res.Entries.map(e => {\n          return {\n            value: e,\n            label: e\n          };\n        })];\n      }\n    });\n  }\n  clear() {\n    this.searchQuery = {\n      CFrom: 1,\n      CTo: 100,\n      CBuildingNameSelected: this.buildingSelectedOptions[0]\n    };\n  }\n  groupByFloor(customerData) {\n    const groupedData = [];\n    // Get all unique floor numbers (handling potential nulls)\n    const uniqueFloors = Array.from(new Set(customerData.map(customer => customer.CFloor).filter(floor => floor !== null)));\n    // Create an empty array for each unique floor\n    for (const floor of uniqueFloors) {\n      groupedData.push([]);\n    }\n    // Place each customer in the correct floor array\n    for (const customer of customerData) {\n      const floorIndex = uniqueFloors.indexOf(customer.CFloor); // Find the index of the customer's floor in the uniqueFloors array\n      if (floorIndex !== -1) {\n        groupedData[floorIndex].push({\n          ...customer,\n          CIsSelected: customer.CIsEnable === false ? true : false\n        });\n      } // Add customer to the corresponding array in groupedData\n    }\n    return groupedData;\n  }\n  sortByFloorDescending(arr) {\n    return arr.sort((a, b) => (b.CFloor ?? 0) - (a.CFloor ?? 0));\n  }\n  getHouseList() {\n    this.isHouseList = false;\n    if (this.buildCaseId) {\n      this._houseService.apiHouseGetHouseListPost$Json({\n        body: {\n          CBuildCaseID: this.buildCaseId,\n          CBuildingName: this.searchQuery.CBuildingNameSelected.value || null,\n          CFloor: {\n            CFrom: this.searchQuery.CFrom,\n            CTo: this.searchQuery.CTo\n          },\n          CIsPagi: false\n        }\n      }).subscribe(res => {\n        if (res.StatusCode == 0) {\n          if (res.Entries) {\n            const rest = this.sortByFloorDescending(res.Entries);\n            this.houseList = this.groupByFloor(rest);\n            this.isHouseList = true;\n          }\n        }\n      });\n    }\n  }\n  goBack() {\n    this._eventService.push({\n      action: \"GET_BUILDCASE\" /* EEvent.GET_BUILDCASE */,\n      payload: this.buildCaseId\n    });\n    this.location.back();\n  }\n  onSubmit() {\n    let bodyParam = this.houseList.flat().map(item => {\n      return {\n        CIsEnable: !item.CIsSelected,\n        CHouseID: item.CID\n      };\n    });\n    this._houseService.apiHouseEditListHousePost$Json({\n      body: {\n        Args: bodyParam\n      }\n    }).subscribe(res => {\n      if (res.StatusCode == 0) {\n        this.message.showSucessMSG(\"執行成功\");\n        console.log(res);\n        this.getHouseList();\n      }\n    });\n  }\n  isCheckAllRowChecked(row) {\n    return row.every(item => item.CIsSelected);\n  }\n  isCheckAllColumnChecked(index) {\n    if (this.isHouseList) {\n      if (index < 0 || index >= this.houseList[0].length) {\n        throw new Error(\"Invalid index. Index must be within the bounds of the array.\");\n      }\n      for (const floorData of this.houseList) {\n        if (index >= floorData.length || !floorData[index].CIsSelected) {\n          return false; // Found a customer with CIsSelected not true (or missing)\n        }\n      }\n      return true; // All customers at the given index have CIsSelected as true\n    }\n    return false;\n  }\n  enableAllAtIndex(checked, index) {\n    if (index < 0) {\n      throw new Error(\"Invalid index. Index must be a non-negative number.\");\n    }\n    for (const floorData of this.houseList) {\n      if (index < floorData.length) {\n        // Check if index is valid for this floor\n        floorData[index].CIsSelected = checked;\n      }\n    }\n  }\n  enableAllRow(checked, row) {\n    for (const item of row) {\n      item.CIsSelected = checked;\n    }\n  }\n  ngOnInit() {\n    this.searchQuery = {\n      CBuildingNameSelected: this.buildingSelectedOptions[0],\n      CFrom: 1,\n      CTo: 100\n    };\n    this.route.paramMap.subscribe(params => {\n      if (params) {\n        const idParam = params.get('id');\n        const id = idParam ? +idParam : 0;\n        this.buildCaseId = id;\n        this.getListBuilding();\n        this.getHouseList();\n      }\n    });\n  }\n  pageChanged(newPage) {\n    this.pageIndex = newPage;\n  }\n  onOpen(ref) {\n    this.dialogService.open(ref);\n  }\n  onClose(ref) {\n    ref.close();\n  }\n  onNavigateWithId(type) {\n    this.router.navigate([`/pages/household-management/${type}`, this.buildCaseId]);\n  }\n  static {\n    this.ɵfac = function ModifyFloorPlanComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || ModifyFloorPlanComponent)(i0.ɵɵdirectiveInject(i1.AllowHelper), i0.ɵɵdirectiveInject(i2.NbDialogService), i0.ɵɵdirectiveInject(i3.HouseService), i0.ɵɵdirectiveInject(i4.ActivatedRoute), i0.ɵɵdirectiveInject(i5.Location), i0.ɵɵdirectiveInject(i6.MessageService), i0.ɵɵdirectiveInject(i4.Router), i0.ɵɵdirectiveInject(i7.EventService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ModifyFloorPlanComponent,\n      selectors: [[\"ngx-modify-floor-plan\"]],\n      features: [i0.ɵɵInheritDefinitionFeature],\n      decls: 47,\n      vars: 5,\n      consts: [[\"accent\", \"success\"], [1, \"font-bold\", \"text-[#818181]\"], [1, \"d-flex\", \"flex-wrap\"], [1, \"col-md-4\"], [1, \"form-group\", \"d-flex\", \"align-items-center\", \"w-full\"], [\"for\", \"buildingName\", 1, \"label\", \"col-3\"], [\"placeholder\", \"\\u72C0\\u614B\", 1, \"w-full\", 3, \"ngModelChange\", \"ngModel\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [1, \"col-md-5\"], [1, \"form-group\", \"d-flex\", \"align-items-center\"], [\"for\", \"cFloorFrom\", 1, \"label\", \"col-3\"], [1, \"ml-3\"], [\"type\", \"number\", \"id\", \"search\", \"nbInput\", \"\", 1, \"w-full\", \"col-4\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"cFloorTo\", 1, \"label\", \"col-1\"], [1, \"mr-3\"], [\"type\", \"number\", \"id\", \"search\", \"nbInput\", \"\", 1, \"w-full\", 3, \"ngModelChange\", \"ngModel\"], [1, \"col-md-3\"], [1, \"d-flex\", \"justify-content-end\", \"w-full\"], [1, \"btn\", \"btn-secondary\", \"mx-2\", 3, \"click\"], [1, \"btn\", \"btn-secondary\", 3, \"click\"], [1, \"fas\", \"fa-search\"], [1, \"col-md-12\"], [1, \"btn\", \"btn-primary\", 3, \"click\"], [1, \"btn\", \"btn-primary\", \"mx-2\", 3, \"click\"], [\"class\", \"table-responsive mt-4\", 4, \"ngIf\"], [1, \"d-flex\", \"justify-content-center\"], [1, \"inline\"], [1, \"d-flex\", \"justify-content-center\", \"w-full\"], [3, \"value\"], [1, \"table-responsive\", \"mt-4\"], [1, \"table\", \"table-bordered\", 2, \"min-width\", \"800px\", \"background-color\", \"#f3f3f3\"], [4, \"ngIf\"], [4, \"ngFor\", \"ngForOf\"], [1, \"w-max\"], [\"status\", \"basic\", 3, \"checkedChange\", \"checked\"], [1, \"font-medium\"], [1, \"font-bold\"]],\n      template: function ModifyFloorPlanComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"nb-card\", 0)(1, \"nb-card-header\");\n          i0.ɵɵelement(2, \"ngx-breadcrumb\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(3, \"nb-card-body\");\n          i0.ɵɵelement(4, \"h1\", 1);\n          i0.ɵɵelementStart(5, \"div\", 2)(6, \"div\", 3)(7, \"div\", 4)(8, \"label\", 5);\n          i0.ɵɵtext(9, \"\\u68DF\\u5225\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(10, \"nb-select\", 6);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function ModifyFloorPlanComponent_Template_nb_select_ngModelChange_10_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.searchQuery.CBuildingNameSelected, $event) || (ctx.searchQuery.CBuildingNameSelected = $event);\n            return $event;\n          });\n          i0.ɵɵtemplate(11, ModifyFloorPlanComponent_nb_option_11_Template, 2, 2, \"nb-option\", 7);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(12, \"div\", 8)(13, \"div\", 9)(14, \"label\", 10);\n          i0.ɵɵtext(15, \"\\u6A13 \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(16, \"nb-form-field\", 11)(17, \"input\", 12);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function ModifyFloorPlanComponent_Template_input_ngModelChange_17_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.searchQuery.CFrom, $event) || (ctx.searchQuery.CFrom = $event);\n            return $event;\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(18, \"label\", 13);\n          i0.ɵɵtext(19, \"~ \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(20, \"nb-form-field\", 14)(21, \"input\", 15);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function ModifyFloorPlanComponent_Template_input_ngModelChange_21_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.searchQuery.CTo, $event) || (ctx.searchQuery.CTo = $event);\n            return $event;\n          });\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(22, \"div\", 16)(23, \"div\", 17)(24, \"button\", 18);\n          i0.ɵɵlistener(\"click\", function ModifyFloorPlanComponent_Template_button_click_24_listener() {\n            return ctx.clear();\n          });\n          i0.ɵɵtext(25, \" \\u6E05\\u9664 \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(26, \"button\", 19);\n          i0.ɵɵlistener(\"click\", function ModifyFloorPlanComponent_Template_button_click_26_listener() {\n            return ctx.getHouseList();\n          });\n          i0.ɵɵtext(27, \" \\u67E5\\u8A62 \");\n          i0.ɵɵelement(28, \"i\", 20);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(29, \"div\", 21)(30, \"div\", 17)(31, \"button\", 22);\n          i0.ɵɵlistener(\"click\", function ModifyFloorPlanComponent_Template_button_click_31_listener() {\n            return ctx.onNavigateWithId(\"modify-floor-plan\");\n          });\n          i0.ɵɵtext(32, \" 1.\\u8ABF\\u6574\\u6236\\u578B\\u7D44\\u6210 \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(33, \"button\", 23);\n          i0.ɵɵlistener(\"click\", function ModifyFloorPlanComponent_Template_button_click_33_listener() {\n            return ctx.onNavigateWithId(\"modify-household\");\n          });\n          i0.ɵɵtext(34, \" 2.\\u4FEE\\u6539\\u6236\\u578B\\u540D\\u7A31 \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(35, \"button\", 22);\n          i0.ɵɵlistener(\"click\", function ModifyFloorPlanComponent_Template_button_click_35_listener() {\n            return ctx.onNavigateWithId(\"modify-house-type\");\n          });\n          i0.ɵɵtext(36, \" 3.\\u8A2D\\u5B9A\\u5730\\u4E3B\\u6236 \");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵtemplate(37, ModifyFloorPlanComponent_div_37_Template, 6, 2, \"div\", 24);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(38, \"nb-card-footer\", 25)(39, \"div\", 26)(40, \"div\", 27)(41, \"button\", 22);\n          i0.ɵɵlistener(\"click\", function ModifyFloorPlanComponent_Template_button_click_41_listener() {\n            return ctx.goBack();\n          });\n          i0.ɵɵtext(42, \" \\u8FD4\\u56DE\\u4E0A\\u4E00\\u9801 \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(43, \"button\", 23);\n          i0.ɵɵlistener(\"click\", function ModifyFloorPlanComponent_Template_button_click_43_listener() {\n            return ctx.getHouseList();\n          });\n          i0.ɵɵtext(44, \" \\u53D6\\u6D88 \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(45, \"button\", 22);\n          i0.ɵɵlistener(\"click\", function ModifyFloorPlanComponent_Template_button_click_45_listener() {\n            return ctx.onSubmit();\n          });\n          i0.ɵɵtext(46, \" \\u5132\\u5B58 \");\n          i0.ɵɵelementEnd()()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(10);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchQuery.CBuildingNameSelected);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngForOf\", ctx.buildingSelectedOptions);\n          i0.ɵɵadvance(6);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchQuery.CFrom);\n          i0.ɵɵadvance(4);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchQuery.CTo);\n          i0.ɵɵadvance(16);\n          i0.ɵɵproperty(\"ngIf\", ctx.isHouseList);\n        }\n      },\n      dependencies: [i5.NgForOf, i5.NgIf, i8.DefaultValueAccessor, i8.NumberValueAccessor, i8.NgControlStatus, i8.NgModel, i2.NbCardComponent, i2.NbCardBodyComponent, i2.NbCardFooterComponent, i2.NbCardHeaderComponent, i2.NbCheckboxComponent, i2.NbInputDirective, i2.NbSelectComponent, i2.NbOptionComponent, i2.NbFormFieldComponent, i9.BreadcrumbComponent],\n      styles: [\"/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsImZpbGUiOiJtb2RpZnktZmxvb3ItcGxhbi5jb21wb25lbnQuc2NzcyJ9 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvcGFnZXMvaG91c2Vob2xkLW1hbmFnZW1lbnQvbW9kaWZ5LWZsb29yLXBsYW4vbW9kaWZ5LWZsb29yLXBsYW4uY29tcG9uZW50LnNjc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IjtBQUNBLGdMQUFnTCIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["BaseComponent", "EEvent", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵproperty", "building_r1", "ɵɵadvance", "ɵɵtextInterpolate1", "label", "ɵɵlistener", "ModifyFloorPlanComponent_div_37_tr_3_th_2_Template_nb_checkbox_checkedChange_2_listener", "$event", "idx_r3", "ɵɵrestoreView", "_r2", "index", "ctx_r3", "ɵɵnextContext", "ɵɵresetView", "enableAllAtIndex", "isCheckAllColumnChecked", "ɵɵelement", "ɵɵtemplate", "ModifyFloorPlanComponent_div_37_tr_3_th_2_Template", "houseList", "ɵɵtwoWayListener", "ModifyFloorPlanComponent_div_37_tr_5_td_8_Template_nb_checkbox_checkedChange_4_listener", "house_r8", "_r7", "$implicit", "ɵɵtwoWayBindingSet", "CIsSelected", "ɵɵtextInterpolate2", "CHouseHold", "CFloor", "ɵɵtwoWayProperty", "ModifyFloorPlanComponent_div_37_tr_5_Template_nb_checkbox_checkedChange_5_listener", "row_r6", "_r5", "enableAllRow", "ModifyFloorPlanComponent_div_37_tr_5_td_8_Template", "isCheckAllRowChecked", "ModifyFloorPlanComponent_div_37_tr_3_Template", "ModifyFloorPlanComponent_div_37_tr_5_Template", "length", "ModifyFloorPlanComponent", "constructor", "_allow", "dialogService", "_houseService", "route", "location", "message", "router", "_eventService", "buildingSelectedOptions", "value", "isHouseList", "getListBuilding", "apiHouseGetListBuildingPost$Json", "body", "CBuildCaseID", "buildCaseId", "subscribe", "res", "Entries", "StatusCode", "map", "e", "clear", "searchQuery", "CFrom", "CTo", "CBuildingNameSelected", "groupByFloor", "customerData", "groupedData", "uniqueFloors", "Array", "from", "Set", "customer", "filter", "floor", "push", "floorIndex", "indexOf", "CIsEnable", "sortByFloorDescending", "arr", "sort", "a", "b", "getHouseList", "apiHouseGetHouseListPost$Json", "CBuildingName", "CIsPagi", "rest", "goBack", "action", "payload", "back", "onSubmit", "bodyParam", "flat", "item", "CHouseID", "CID", "apiHouseEditListHousePost$Json", "<PERSON><PERSON><PERSON>", "showSucessMSG", "console", "log", "row", "every", "Error", "floorData", "checked", "ngOnInit", "paramMap", "params", "idParam", "get", "id", "pageChanged", "newPage", "pageIndex", "onOpen", "ref", "open", "onClose", "close", "onNavigateWithId", "type", "navigate", "ɵɵdirectiveInject", "i1", "AllowHelper", "i2", "NbDialogService", "i3", "HouseService", "i4", "ActivatedRoute", "i5", "Location", "i6", "MessageService", "Router", "i7", "EventService", "selectors", "features", "ɵɵInheritDefinitionFeature", "decls", "vars", "consts", "template", "ModifyFloorPlanComponent_Template", "rf", "ctx", "ModifyFloorPlanComponent_Template_nb_select_ngModelChange_10_listener", "ModifyFloorPlanComponent_nb_option_11_Template", "ModifyFloorPlanComponent_Template_input_ngModelChange_17_listener", "ModifyFloorPlanComponent_Template_input_ngModelChange_21_listener", "ModifyFloorPlanComponent_Template_button_click_24_listener", "ModifyFloorPlanComponent_Template_button_click_26_listener", "ModifyFloorPlanComponent_Template_button_click_31_listener", "ModifyFloorPlanComponent_Template_button_click_33_listener", "ModifyFloorPlanComponent_Template_button_click_35_listener", "ModifyFloorPlanComponent_div_37_Template", "ModifyFloorPlanComponent_Template_button_click_41_listener", "ModifyFloorPlanComponent_Template_button_click_43_listener", "ModifyFloorPlanComponent_Template_button_click_45_listener"], "sources": ["C:\\Users\\<USER>\\Documents\\jeansalechange\\adminFront\\src\\app\\pages\\household-management\\modify-floor-plan\\modify-floor-plan.component.ts", "C:\\Users\\<USER>\\Documents\\jeansalechange\\adminFront\\src\\app\\pages\\household-management\\modify-floor-plan\\modify-floor-plan.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { SharedModule } from '../../components/shared.module';\r\nimport { CommonModule, Location } from '@angular/common';\r\nimport { NbDialogService } from '@nebular/theme';\r\nimport { BaseComponent } from '../../components/base/baseComponent';\r\nimport { AllowHelper } from 'src/app/shared/helper/allowHelper';\r\nimport { HouseService } from 'src/services/api/services';\r\nimport { ActivatedRoute, Router } from '@angular/router';\r\n// import { GetHouseListRes } from 'src/services/api/models';\r\nimport { MessageService } from 'src/app/shared/services/message.service';\r\nimport { EEvent, EventService } from 'src/app/shared/services/event.service';\r\n\r\nexport interface selectItem {\r\n  label: string,\r\n  value: number,\r\n  key?: string\r\n}\r\n\r\nexport interface GetHouseListRes {\r\n  CBuildingName?: string | null;\r\n  CFloor?: number | null;\r\n  CHouseHold?: string | null;\r\n  CHouseType?: number | null;\r\n  CID?: number;\r\n  CIsChange?: boolean;\r\n  CIsEnable?: boolean | null;\r\n  CIsSelected?: boolean | null;\r\n  CPayStatus?: number;\r\n  CProgress?: number;\r\n  CSignStatus?: number | null;\r\n  CProgressName?: string | null;\r\n}\r\n\r\n@Component({\r\n  selector: 'ngx-modify-floor-plan',\r\n  templateUrl: './modify-floor-plan.component.html',\r\n  styleUrls: ['./modify-floor-plan.component.scss'],\r\n})\r\n\r\nexport class ModifyFloorPlanComponent extends BaseComponent implements OnInit {\r\n  constructor(\r\n    private _allow: AllowHelper,\r\n    private dialogService: NbDialogService,\r\n    private _houseService: HouseService,\r\n    private route: ActivatedRoute,\r\n    private location: Location,\r\n    private message: MessageService,\r\n    private router: Router,\r\n    private _eventService: EventService\r\n\r\n  ) {\r\n    super(_allow)\r\n  }\r\n\r\n  buildCaseId: number\r\n  buildingSelectedOptions: any[] = [{ value: '', label: '全部' }]\r\n\r\n  getListBuilding() {\r\n    this._houseService.apiHouseGetListBuildingPost$Json({\r\n      body: {\r\n        CBuildCaseID: this.buildCaseId\r\n      }\r\n    }).subscribe(res => {\r\n      if (res.Entries && res.StatusCode == 0) {\r\n        this.buildingSelectedOptions = [{\r\n          value: '', label: '全部'\r\n        }, ...res.Entries.map(e => {\r\n          return { value: e, label: e }\r\n        })]\r\n      }\r\n    })\r\n  }\r\n\r\n\r\n  houseList: any\r\n\r\n\r\n  clear() {\r\n    this.searchQuery = {\r\n      CFrom: 1,\r\n      CTo: 100,\r\n      CBuildingNameSelected: this.buildingSelectedOptions[0]\r\n    }\r\n  }\r\n\r\n  groupByFloor(customerData: GetHouseListRes[]): GetHouseListRes[][] {\r\n    const groupedData: GetHouseListRes[][] = [];\r\n    // Get all unique floor numbers (handling potential nulls)\r\n    const uniqueFloors = Array.from(new Set(\r\n      customerData.map(customer => customer.CFloor).filter(floor => floor !== null) as number[]\r\n    ));\r\n    // Create an empty array for each unique floor\r\n    for (const floor of uniqueFloors) {\r\n      groupedData.push([]);\r\n    }\r\n    // Place each customer in the correct floor array\r\n    for (const customer of customerData) {\r\n      const floorIndex = uniqueFloors.indexOf(customer.CFloor as number); // Find the index of the customer's floor in the uniqueFloors array\r\n      if (floorIndex !== -1) {\r\n        groupedData[floorIndex].push({ ...customer, CIsSelected: customer.CIsEnable === false ? true : false });\r\n      } // Add customer to the corresponding array in groupedData\r\n    }\r\n    return groupedData;\r\n  }\r\n\r\n  isHouseList = false\r\n\r\n  sortByFloorDescending(arr: GetHouseListRes[]): GetHouseListRes[] {\r\n    return arr.sort((a, b) => (b.CFloor ?? 0) - (a.CFloor ?? 0));\r\n  }\r\n\r\n  getHouseList() {\r\n    this.isHouseList = false\r\n    if (this.buildCaseId) {\r\n      this._houseService.apiHouseGetHouseListPost$Json({\r\n        body: {\r\n          CBuildCaseID: this.buildCaseId,\r\n          CBuildingName: this.searchQuery.CBuildingNameSelected.value || null,\r\n          CFloor: {\r\n            CFrom: this.searchQuery.CFrom,\r\n            CTo: this.searchQuery.CTo,\r\n          },\r\n          CIsPagi: false\r\n        }\r\n      }).subscribe(res => {\r\n        if (res.StatusCode == 0) {\r\n          if(res.Entries){\r\n            const rest = this.sortByFloorDescending(res.Entries)\r\n            this.houseList = this.groupByFloor(rest)\r\n            this.isHouseList = true\r\n          }\r\n        }\r\n      })\r\n    }\r\n  }\r\n\r\n\r\n  goBack() {\r\n    this._eventService.push({\r\n      action: EEvent.GET_BUILDCASE,\r\n      payload: this.buildCaseId\r\n    })\r\n    this.location.back()\r\n  }\r\n\r\n  onSubmit() {\r\n    let bodyParam = this.houseList.flat().map((item: any) => {\r\n      return {\r\n        CIsEnable: !item.CIsSelected,\r\n        CHouseID: item.CID,\r\n      };\r\n    });\r\n\r\n    this._houseService.apiHouseEditListHousePost$Json({\r\n      body: {\r\n        Args: bodyParam\r\n      }\r\n    }).subscribe(res => {\r\n      if (res.StatusCode == 0) {\r\n        this.message.showSucessMSG(\"執行成功\");\r\n        console.log(res);\r\n        this.getHouseList()\r\n      }\r\n    })\r\n  }\r\n\r\n\r\n  isCheckAllRowChecked(row: any[]): boolean {\r\n    return row.every((item: { CIsSelected: any; }) => item.CIsSelected);\r\n  }\r\n\r\n  isCheckAllColumnChecked(index: number): boolean {\r\n    if (this.isHouseList) {\r\n\r\n      if (index < 0 || index >= this.houseList[0].length) {\r\n        throw new Error(\"Invalid index. Index must be within the bounds of the array.\");\r\n      }\r\n      for (const floorData of this.houseList) {\r\n        if (index >= floorData.length || !floorData[index].CIsSelected) {\r\n          return false; // Found a customer with CIsSelected not true (or missing)\r\n        }\r\n      }\r\n      return true; // All customers at the given index have CIsSelected as true\r\n    }\r\n    return false\r\n  }\r\n\r\n  enableAllAtIndex(checked: boolean, index: number): void {\r\n    if (index < 0) {\r\n      throw new Error(\"Invalid index. Index must be a non-negative number.\");\r\n    }\r\n    for (const floorData of this.houseList) {\r\n      if (index < floorData.length) { // Check if index is valid for this floor\r\n        floorData[index].CIsSelected = checked;\r\n      }\r\n    }\r\n  }\r\n\r\n  enableAllRow(checked: boolean, row: any[]) {\r\n    for (const item of row) {\r\n      item.CIsSelected = checked;\r\n    }\r\n  }\r\n\r\n\r\n  searchQuery: any\r\n\r\n  override ngOnInit(): void {\r\n    this.searchQuery = {\r\n      CBuildingNameSelected: this.buildingSelectedOptions[0],\r\n      CFrom: 1,\r\n      CTo: 100\r\n    }\r\n    this.route.paramMap.subscribe(params => {\r\n      if (params) {\r\n        const idParam = params.get('id');\r\n        const id = idParam ? +idParam : 0;\r\n        this.buildCaseId = id\r\n        this.getListBuilding()\r\n        this.getHouseList()\r\n      }\r\n    });\r\n\r\n  }\r\n\r\n  pageChanged(newPage: number) {\r\n    this.pageIndex = newPage;\r\n  }\r\n\r\n\r\n  onOpen(ref: any) {\r\n    this.dialogService.open(ref)\r\n  }\r\n\r\n  onClose(ref: any) {\r\n    ref.close();\r\n  }\r\n\r\n  onNavigateWithId(type: any) {\r\n    this.router.navigate([`/pages/household-management/${type}`, this.buildCaseId])\r\n  }\r\n\r\n}\r\n", "<!-- 2.1.3 -->\r\n<nb-card accent=\"success\">\r\n  <nb-card-header>\r\n    <ngx-breadcrumb></ngx-breadcrumb>\r\n  </nb-card-header>\r\n  <nb-card-body>\r\n    <h1 class=\"font-bold text-[#818181]\"></h1>\r\n    <div class=\"d-flex flex-wrap\">\r\n      <div class=\"col-md-4\">\r\n        <div class=\"form-group d-flex align-items-center w-full\">\r\n          <label for=\"buildingName\" class=\"label col-3\">棟別</label>\r\n          <nb-select placeholder=\"狀態\" [(ngModel)]=\"searchQuery.CBuildingNameSelected\" class=\"w-full\">\r\n            <nb-option *ngFor=\"let building of buildingSelectedOptions\" [value]=\"building\">\r\n              {{ building.label }}\r\n            </nb-option>\r\n          </nb-select>\r\n        </div>\r\n\r\n      </div>\r\n      <div class=\"col-md-5\">\r\n        <div class=\"form-group d-flex align-items-center\">\r\n          <label for=\"cFloorFrom\" class=\"label col-3\">樓\r\n          </label>\r\n          <nb-form-field class=\"ml-3\">\r\n            <input type=\"number\" id=\"search\" nbInput class=\"w-full col-4\" [(ngModel)]=\"searchQuery.CFrom\">\r\n          </nb-form-field>\r\n          <label for=\"cFloorTo\" class=\"label col-1\">~\r\n          </label>\r\n          <nb-form-field class=\"mr-3\">\r\n            <input type=\"number\" id=\"search\" nbInput class=\"w-full\" [(ngModel)]=\"searchQuery.CTo\">\r\n          </nb-form-field>\r\n        </div>\r\n      </div>\r\n      <div class=\"col-md-3\">\r\n        <div class=\"d-flex justify-content-end w-full\">\r\n          <button class=\"btn btn-secondary mx-2\" (click)=\"clear()\">\r\n            清除\r\n          </button>\r\n          <button class=\"btn btn-secondary\" (click)=\"getHouseList()\">\r\n            查詢 <i class=\"fas fa-search\"></i>\r\n          </button>\r\n        </div>\r\n      </div>\r\n      <div class=\"col-md-12\">\r\n        <div class=\"d-flex justify-content-end w-full\">\r\n          <button class=\"btn btn-primary\" (click)=\"onNavigateWithId('modify-floor-plan')\">\r\n            1.調整戶型組成\r\n          </button>\r\n          <button class=\"btn btn-primary mx-2\" (click)=\"onNavigateWithId('modify-household')\">\r\n            2.修改戶型名稱\r\n          </button>\r\n          <button class=\"btn btn-primary\" (click)=\"onNavigateWithId('modify-house-type')\">\r\n            3.設定地主戶\r\n          </button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <div class=\"table-responsive mt-4\" *ngIf=\"isHouseList\">\r\n      <table class=\"table table-bordered\" style=\"min-width: 800px; background-color:#f3f3f3;\">\r\n        <thead>\r\n          <tr *ngIf=\"houseList.length\">\r\n            <th></th>\r\n            <th *ngFor=\"let house of houseList[0]; let idx = index;\">\r\n              <div class=\"w-max\">\r\n                <nb-checkbox status=\"basic\" (checkedChange)=\"enableAllAtIndex($event, idx)\"\r\n                  [checked]=\"isCheckAllColumnChecked(idx)\">\r\n                  <span class=\"font-medium\">全選無此戶型 </span>\r\n                </nb-checkbox>\r\n              </div>\r\n            </th>\r\n          </tr>\r\n        </thead>\r\n        <tbody>\r\n          <tr *ngFor=\"let row of houseList\">\r\n            <td>\r\n              <div class=\"w-max\">\r\n                <p>&nbsp;</p>\r\n                <nb-checkbox status=\"basic\" [checked]=\"isCheckAllRowChecked(row)\"\r\n                  (checkedChange)=\"enableAllRow($event,row)\">\r\n                  <span class=\"font-medium\">全選無此戶型</span>\r\n                </nb-checkbox>\r\n              </div>\r\n            </td>\r\n            <td *ngFor=\"let house of row\">\r\n              <div class=\"w-max\">\r\n                <p class=\"font-bold\">{{ house.CHouseHold || 'null' }} - {{ house.CFloor }}</p>\r\n                <nb-checkbox status=\"basic\" [(checked)]=\"house.CIsSelected\">\r\n                  <span class=\"font-medium\">無此戶型</span>\r\n                </nb-checkbox>\r\n              </div>\r\n            </td>\r\n          </tr>\r\n        </tbody>\r\n      </table>\r\n    </div>\r\n  </nb-card-body>\r\n  <nb-card-footer class=\"d-flex justify-content-center\">\r\n    <div class=\"inline\">\r\n      <div class=\"d-flex justify-content-center w-full\">\r\n        <button class=\"btn btn-primary\" (click)=\"goBack()\">\r\n          返回上一頁\r\n        </button>\r\n        <button class=\"btn btn-primary mx-2\" (click)=\"getHouseList()\">\r\n          取消\r\n        </button>\r\n        <button class=\"btn btn-primary\" (click)=\"onSubmit()\">\r\n          儲存\r\n        </button>\r\n      </div>\r\n    </div>\r\n  </nb-card-footer>\r\n</nb-card>"], "mappings": "AAIA,SAASA,aAAa,QAAQ,qCAAqC;AAMnE,SAASC,MAAM,QAAsB,uCAAuC;;;;;;;;;;;;;ICEhEC,EAAA,CAAAC,cAAA,oBAA+E;IAC7ED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAFgDH,EAAA,CAAAI,UAAA,UAAAC,WAAA,CAAkB;IAC5EL,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAAO,kBAAA,MAAAF,WAAA,CAAAG,KAAA,MACF;;;;;;IAkDIR,EAFJ,CAAAC,cAAA,SAAyD,cACpC,sBAE0B;IADfD,EAAA,CAAAS,UAAA,2BAAAC,wFAAAC,MAAA;MAAA,MAAAC,MAAA,GAAAZ,EAAA,CAAAa,aAAA,CAAAC,GAAA,EAAAC,KAAA;MAAA,MAAAC,MAAA,GAAAhB,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAAiBF,MAAA,CAAAG,gBAAA,CAAAR,MAAA,EAAAC,MAAA,CAA6B;IAAA,EAAC;IAEzEZ,EAAA,CAAAC,cAAA,eAA0B;IAAAD,EAAA,CAAAE,MAAA,4CAAO;IAGvCF,EAHuC,CAAAG,YAAA,EAAO,EAC5B,EACV,EACH;;;;;IAJCH,EAAA,CAAAM,SAAA,GAAwC;IAAxCN,EAAA,CAAAI,UAAA,YAAAY,MAAA,CAAAI,uBAAA,CAAAR,MAAA,EAAwC;;;;;IALhDZ,EAAA,CAAAC,cAAA,SAA6B;IAC3BD,EAAA,CAAAqB,SAAA,SAAS;IACTrB,EAAA,CAAAsB,UAAA,IAAAC,kDAAA,iBAAyD;IAQ3DvB,EAAA,CAAAG,YAAA,EAAK;;;;IARmBH,EAAA,CAAAM,SAAA,GAAiB;IAAjBN,EAAA,CAAAI,UAAA,YAAAY,MAAA,CAAAQ,SAAA,IAAiB;;;;;;IAuBnCxB,EAFJ,CAAAC,cAAA,SAA8B,cACT,YACI;IAAAD,EAAA,CAAAE,MAAA,GAAqD;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAC9EH,EAAA,CAAAC,cAAA,sBAA4D;IAAhCD,EAAA,CAAAyB,gBAAA,2BAAAC,wFAAAf,MAAA;MAAA,MAAAgB,QAAA,GAAA3B,EAAA,CAAAa,aAAA,CAAAe,GAAA,EAAAC,SAAA;MAAA7B,EAAA,CAAA8B,kBAAA,CAAAH,QAAA,CAAAI,WAAA,EAAApB,MAAA,MAAAgB,QAAA,CAAAI,WAAA,GAAApB,MAAA;MAAA,OAAAX,EAAA,CAAAkB,WAAA,CAAAP,MAAA;IAAA,EAA+B;IACzDX,EAAA,CAAAC,cAAA,eAA0B;IAAAD,EAAA,CAAAE,MAAA,+BAAI;IAGpCF,EAHoC,CAAAG,YAAA,EAAO,EACzB,EACV,EACH;;;;IALoBH,EAAA,CAAAM,SAAA,GAAqD;IAArDN,EAAA,CAAAgC,kBAAA,KAAAL,QAAA,CAAAM,UAAA,mBAAAN,QAAA,CAAAO,MAAA,KAAqD;IAC9ClC,EAAA,CAAAM,SAAA,EAA+B;IAA/BN,EAAA,CAAAmC,gBAAA,YAAAR,QAAA,CAAAI,WAAA,CAA+B;;;;;;IAV3D/B,EAHN,CAAAC,cAAA,SAAkC,SAC5B,cACiB,QACd;IAAAD,EAAA,CAAAE,MAAA,aAAM;IAAAF,EAAA,CAAAG,YAAA,EAAI;IACbH,EAAA,CAAAC,cAAA,sBAC6C;IAA3CD,EAAA,CAAAS,UAAA,2BAAA2B,mFAAAzB,MAAA;MAAA,MAAA0B,MAAA,GAAArC,EAAA,CAAAa,aAAA,CAAAyB,GAAA,EAAAT,SAAA;MAAA,MAAAb,MAAA,GAAAhB,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAAiBF,MAAA,CAAAuB,YAAA,CAAA5B,MAAA,EAAA0B,MAAA,CAAwB;IAAA,EAAC;IAC1CrC,EAAA,CAAAC,cAAA,eAA0B;IAAAD,EAAA,CAAAE,MAAA,2CAAM;IAGtCF,EAHsC,CAAAG,YAAA,EAAO,EAC3B,EACV,EACH;IACLH,EAAA,CAAAsB,UAAA,IAAAkB,kDAAA,iBAA8B;IAQhCxC,EAAA,CAAAG,YAAA,EAAK;;;;;IAd6BH,EAAA,CAAAM,SAAA,GAAqC;IAArCN,EAAA,CAAAI,UAAA,YAAAY,MAAA,CAAAyB,oBAAA,CAAAJ,MAAA,EAAqC;IAM/CrC,EAAA,CAAAM,SAAA,GAAM;IAANN,EAAA,CAAAI,UAAA,YAAAiC,MAAA,CAAM;;;;;IAxBhCrC,EAFJ,CAAAC,cAAA,cAAuD,gBACmC,YAC/E;IACLD,EAAA,CAAAsB,UAAA,IAAAoB,6CAAA,iBAA6B;IAW/B1C,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,YAAO;IACLD,EAAA,CAAAsB,UAAA,IAAAqB,6CAAA,iBAAkC;IAqBxC3C,EAFI,CAAAG,YAAA,EAAQ,EACF,EACJ;;;;IAlCKH,EAAA,CAAAM,SAAA,GAAsB;IAAtBN,EAAA,CAAAI,UAAA,SAAAY,MAAA,CAAAQ,SAAA,CAAAoB,MAAA,CAAsB;IAaP5C,EAAA,CAAAM,SAAA,GAAY;IAAZN,EAAA,CAAAI,UAAA,YAAAY,MAAA,CAAAQ,SAAA,CAAY;;;ADlC1C,OAAM,MAAOqB,wBAAyB,SAAQ/C,aAAa;EACzDgD,YACUC,MAAmB,EACnBC,aAA8B,EAC9BC,aAA2B,EAC3BC,KAAqB,EACrBC,QAAkB,EAClBC,OAAuB,EACvBC,MAAc,EACdC,aAA2B;IAGnC,KAAK,CAACP,MAAM,CAAC;IAVL,KAAAA,MAAM,GAANA,MAAM;IACN,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,QAAQ,GAARA,QAAQ;IACR,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,aAAa,GAAbA,aAAa;IAOvB,KAAAC,uBAAuB,GAAU,CAAC;MAAEC,KAAK,EAAE,EAAE;MAAEhD,KAAK,EAAE;IAAI,CAAE,CAAC;IAkD7D,KAAAiD,WAAW,GAAG,KAAK;EArDnB;EAKAC,eAAeA,CAAA;IACb,IAAI,CAACT,aAAa,CAACU,gCAAgC,CAAC;MAClDC,IAAI,EAAE;QACJC,YAAY,EAAE,IAAI,CAACC;;KAEtB,CAAC,CAACC,SAAS,CAACC,GAAG,IAAG;MACjB,IAAIA,GAAG,CAACC,OAAO,IAAID,GAAG,CAACE,UAAU,IAAI,CAAC,EAAE;QACtC,IAAI,CAACX,uBAAuB,GAAG,CAAC;UAC9BC,KAAK,EAAE,EAAE;UAAEhD,KAAK,EAAE;SACnB,EAAE,GAAGwD,GAAG,CAACC,OAAO,CAACE,GAAG,CAACC,CAAC,IAAG;UACxB,OAAO;YAAEZ,KAAK,EAAEY,CAAC;YAAE5D,KAAK,EAAE4D;UAAC,CAAE;QAC/B,CAAC,CAAC,CAAC;MACL;IACF,CAAC,CAAC;EACJ;EAMAC,KAAKA,CAAA;IACH,IAAI,CAACC,WAAW,GAAG;MACjBC,KAAK,EAAE,CAAC;MACRC,GAAG,EAAE,GAAG;MACRC,qBAAqB,EAAE,IAAI,CAAClB,uBAAuB,CAAC,CAAC;KACtD;EACH;EAEAmB,YAAYA,CAACC,YAA+B;IAC1C,MAAMC,WAAW,GAAwB,EAAE;IAC3C;IACA,MAAMC,YAAY,GAAGC,KAAK,CAACC,IAAI,CAAC,IAAIC,GAAG,CACrCL,YAAY,CAACR,GAAG,CAACc,QAAQ,IAAIA,QAAQ,CAAC/C,MAAM,CAAC,CAACgD,MAAM,CAACC,KAAK,IAAIA,KAAK,KAAK,IAAI,CAAa,CAC1F,CAAC;IACF;IACA,KAAK,MAAMA,KAAK,IAAIN,YAAY,EAAE;MAChCD,WAAW,CAACQ,IAAI,CAAC,EAAE,CAAC;IACtB;IACA;IACA,KAAK,MAAMH,QAAQ,IAAIN,YAAY,EAAE;MACnC,MAAMU,UAAU,GAAGR,YAAY,CAACS,OAAO,CAACL,QAAQ,CAAC/C,MAAgB,CAAC,CAAC,CAAC;MACpE,IAAImD,UAAU,KAAK,CAAC,CAAC,EAAE;QACrBT,WAAW,CAACS,UAAU,CAAC,CAACD,IAAI,CAAC;UAAE,GAAGH,QAAQ;UAAElD,WAAW,EAAEkD,QAAQ,CAACM,SAAS,KAAK,KAAK,GAAG,IAAI,GAAG;QAAK,CAAE,CAAC;MACzG,CAAC,CAAC;IACJ;IACA,OAAOX,WAAW;EACpB;EAIAY,qBAAqBA,CAACC,GAAsB;IAC1C,OAAOA,GAAG,CAACC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK,CAACA,CAAC,CAAC1D,MAAM,IAAI,CAAC,KAAKyD,CAAC,CAACzD,MAAM,IAAI,CAAC,CAAC,CAAC;EAC9D;EAEA2D,YAAYA,CAAA;IACV,IAAI,CAACpC,WAAW,GAAG,KAAK;IACxB,IAAI,IAAI,CAACK,WAAW,EAAE;MACpB,IAAI,CAACb,aAAa,CAAC6C,6BAA6B,CAAC;QAC/ClC,IAAI,EAAE;UACJC,YAAY,EAAE,IAAI,CAACC,WAAW;UAC9BiC,aAAa,EAAE,IAAI,CAACzB,WAAW,CAACG,qBAAqB,CAACjB,KAAK,IAAI,IAAI;UACnEtB,MAAM,EAAE;YACNqC,KAAK,EAAE,IAAI,CAACD,WAAW,CAACC,KAAK;YAC7BC,GAAG,EAAE,IAAI,CAACF,WAAW,CAACE;WACvB;UACDwB,OAAO,EAAE;;OAEZ,CAAC,CAACjC,SAAS,CAACC,GAAG,IAAG;QACjB,IAAIA,GAAG,CAACE,UAAU,IAAI,CAAC,EAAE;UACvB,IAAGF,GAAG,CAACC,OAAO,EAAC;YACb,MAAMgC,IAAI,GAAG,IAAI,CAACT,qBAAqB,CAACxB,GAAG,CAACC,OAAO,CAAC;YACpD,IAAI,CAACzC,SAAS,GAAG,IAAI,CAACkD,YAAY,CAACuB,IAAI,CAAC;YACxC,IAAI,CAACxC,WAAW,GAAG,IAAI;UACzB;QACF;MACF,CAAC,CAAC;IACJ;EACF;EAGAyC,MAAMA,CAAA;IACJ,IAAI,CAAC5C,aAAa,CAAC8B,IAAI,CAAC;MACtBe,MAAM;MACNC,OAAO,EAAE,IAAI,CAACtC;KACf,CAAC;IACF,IAAI,CAACX,QAAQ,CAACkD,IAAI,EAAE;EACtB;EAEAC,QAAQA,CAAA;IACN,IAAIC,SAAS,GAAG,IAAI,CAAC/E,SAAS,CAACgF,IAAI,EAAE,CAACrC,GAAG,CAAEsC,IAAS,IAAI;MACtD,OAAO;QACLlB,SAAS,EAAE,CAACkB,IAAI,CAAC1E,WAAW;QAC5B2E,QAAQ,EAAED,IAAI,CAACE;OAChB;IACH,CAAC,CAAC;IAEF,IAAI,CAAC1D,aAAa,CAAC2D,8BAA8B,CAAC;MAChDhD,IAAI,EAAE;QACJiD,IAAI,EAAEN;;KAET,CAAC,CAACxC,SAAS,CAACC,GAAG,IAAG;MACjB,IAAIA,GAAG,CAACE,UAAU,IAAI,CAAC,EAAE;QACvB,IAAI,CAACd,OAAO,CAAC0D,aAAa,CAAC,MAAM,CAAC;QAClCC,OAAO,CAACC,GAAG,CAAChD,GAAG,CAAC;QAChB,IAAI,CAAC6B,YAAY,EAAE;MACrB;IACF,CAAC,CAAC;EACJ;EAGApD,oBAAoBA,CAACwE,GAAU;IAC7B,OAAOA,GAAG,CAACC,KAAK,CAAET,IAA2B,IAAKA,IAAI,CAAC1E,WAAW,CAAC;EACrE;EAEAX,uBAAuBA,CAACL,KAAa;IACnC,IAAI,IAAI,CAAC0C,WAAW,EAAE;MAEpB,IAAI1C,KAAK,GAAG,CAAC,IAAIA,KAAK,IAAI,IAAI,CAACS,SAAS,CAAC,CAAC,CAAC,CAACoB,MAAM,EAAE;QAClD,MAAM,IAAIuE,KAAK,CAAC,8DAA8D,CAAC;MACjF;MACA,KAAK,MAAMC,SAAS,IAAI,IAAI,CAAC5F,SAAS,EAAE;QACtC,IAAIT,KAAK,IAAIqG,SAAS,CAACxE,MAAM,IAAI,CAACwE,SAAS,CAACrG,KAAK,CAAC,CAACgB,WAAW,EAAE;UAC9D,OAAO,KAAK,CAAC,CAAC;QAChB;MACF;MACA,OAAO,IAAI,CAAC,CAAC;IACf;IACA,OAAO,KAAK;EACd;EAEAZ,gBAAgBA,CAACkG,OAAgB,EAAEtG,KAAa;IAC9C,IAAIA,KAAK,GAAG,CAAC,EAAE;MACb,MAAM,IAAIoG,KAAK,CAAC,qDAAqD,CAAC;IACxE;IACA,KAAK,MAAMC,SAAS,IAAI,IAAI,CAAC5F,SAAS,EAAE;MACtC,IAAIT,KAAK,GAAGqG,SAAS,CAACxE,MAAM,EAAE;QAAE;QAC9BwE,SAAS,CAACrG,KAAK,CAAC,CAACgB,WAAW,GAAGsF,OAAO;MACxC;IACF;EACF;EAEA9E,YAAYA,CAAC8E,OAAgB,EAAEJ,GAAU;IACvC,KAAK,MAAMR,IAAI,IAAIQ,GAAG,EAAE;MACtBR,IAAI,CAAC1E,WAAW,GAAGsF,OAAO;IAC5B;EACF;EAKSC,QAAQA,CAAA;IACf,IAAI,CAAChD,WAAW,GAAG;MACjBG,qBAAqB,EAAE,IAAI,CAAClB,uBAAuB,CAAC,CAAC,CAAC;MACtDgB,KAAK,EAAE,CAAC;MACRC,GAAG,EAAE;KACN;IACD,IAAI,CAACtB,KAAK,CAACqE,QAAQ,CAACxD,SAAS,CAACyD,MAAM,IAAG;MACrC,IAAIA,MAAM,EAAE;QACV,MAAMC,OAAO,GAAGD,MAAM,CAACE,GAAG,CAAC,IAAI,CAAC;QAChC,MAAMC,EAAE,GAAGF,OAAO,GAAG,CAACA,OAAO,GAAG,CAAC;QACjC,IAAI,CAAC3D,WAAW,GAAG6D,EAAE;QACrB,IAAI,CAACjE,eAAe,EAAE;QACtB,IAAI,CAACmC,YAAY,EAAE;MACrB;IACF,CAAC,CAAC;EAEJ;EAEA+B,WAAWA,CAACC,OAAe;IACzB,IAAI,CAACC,SAAS,GAAGD,OAAO;EAC1B;EAGAE,MAAMA,CAACC,GAAQ;IACb,IAAI,CAAChF,aAAa,CAACiF,IAAI,CAACD,GAAG,CAAC;EAC9B;EAEAE,OAAOA,CAACF,GAAQ;IACdA,GAAG,CAACG,KAAK,EAAE;EACb;EAEAC,gBAAgBA,CAACC,IAAS;IACxB,IAAI,CAAChF,MAAM,CAACiF,QAAQ,CAAC,CAAC,+BAA+BD,IAAI,EAAE,EAAE,IAAI,CAACvE,WAAW,CAAC,CAAC;EACjF;;;uCAzMWjB,wBAAwB,EAAA7C,EAAA,CAAAuI,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAzI,EAAA,CAAAuI,iBAAA,CAAAG,EAAA,CAAAC,eAAA,GAAA3I,EAAA,CAAAuI,iBAAA,CAAAK,EAAA,CAAAC,YAAA,GAAA7I,EAAA,CAAAuI,iBAAA,CAAAO,EAAA,CAAAC,cAAA,GAAA/I,EAAA,CAAAuI,iBAAA,CAAAS,EAAA,CAAAC,QAAA,GAAAjJ,EAAA,CAAAuI,iBAAA,CAAAW,EAAA,CAAAC,cAAA,GAAAnJ,EAAA,CAAAuI,iBAAA,CAAAO,EAAA,CAAAM,MAAA,GAAApJ,EAAA,CAAAuI,iBAAA,CAAAc,EAAA,CAAAC,YAAA;IAAA;EAAA;;;YAAxBzG,wBAAwB;MAAA0G,SAAA;MAAAC,QAAA,GAAAxJ,EAAA,CAAAyJ,0BAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,kCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCrCnC/J,EADF,CAAAC,cAAA,iBAA0B,qBACR;UACdD,EAAA,CAAAqB,SAAA,qBAAiC;UACnCrB,EAAA,CAAAG,YAAA,EAAiB;UACjBH,EAAA,CAAAC,cAAA,mBAAc;UACZD,EAAA,CAAAqB,SAAA,YAA0C;UAIpCrB,EAHN,CAAAC,cAAA,aAA8B,aACN,aACqC,eACT;UAAAD,EAAA,CAAAE,MAAA,mBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACxDH,EAAA,CAAAC,cAAA,oBAA2F;UAA/DD,EAAA,CAAAyB,gBAAA,2BAAAwI,sEAAAtJ,MAAA;YAAAX,EAAA,CAAA8B,kBAAA,CAAAkI,GAAA,CAAA1F,WAAA,CAAAG,qBAAA,EAAA9D,MAAA,MAAAqJ,GAAA,CAAA1F,WAAA,CAAAG,qBAAA,GAAA9D,MAAA;YAAA,OAAAA,MAAA;UAAA,EAA+C;UACzEX,EAAA,CAAAsB,UAAA,KAAA4I,8CAAA,uBAA+E;UAMrFlK,EAHI,CAAAG,YAAA,EAAY,EACR,EAEF;UAGFH,EAFJ,CAAAC,cAAA,cAAsB,cAC8B,iBACJ;UAAAD,EAAA,CAAAE,MAAA,eAC5C;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAENH,EADF,CAAAC,cAAA,yBAA4B,iBACoE;UAAhCD,EAAA,CAAAyB,gBAAA,2BAAA0I,kEAAAxJ,MAAA;YAAAX,EAAA,CAAA8B,kBAAA,CAAAkI,GAAA,CAAA1F,WAAA,CAAAC,KAAA,EAAA5D,MAAA,MAAAqJ,GAAA,CAAA1F,WAAA,CAAAC,KAAA,GAAA5D,MAAA;YAAA,OAAAA,MAAA;UAAA,EAA+B;UAC/FX,EADE,CAAAG,YAAA,EAA8F,EAChF;UAChBH,EAAA,CAAAC,cAAA,iBAA0C;UAAAD,EAAA,CAAAE,MAAA,UAC1C;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAENH,EADF,CAAAC,cAAA,yBAA4B,iBAC4D;UAA9BD,EAAA,CAAAyB,gBAAA,2BAAA2I,kEAAAzJ,MAAA;YAAAX,EAAA,CAAA8B,kBAAA,CAAAkI,GAAA,CAAA1F,WAAA,CAAAE,GAAA,EAAA7D,MAAA,MAAAqJ,GAAA,CAAA1F,WAAA,CAAAE,GAAA,GAAA7D,MAAA;YAAA,OAAAA,MAAA;UAAA,EAA6B;UAG3FX,EAHM,CAAAG,YAAA,EAAsF,EACxE,EACZ,EACF;UAGFH,EAFJ,CAAAC,cAAA,eAAsB,eAC2B,kBACY;UAAlBD,EAAA,CAAAS,UAAA,mBAAA4J,2DAAA;YAAA,OAASL,GAAA,CAAA3F,KAAA,EAAO;UAAA,EAAC;UACtDrE,EAAA,CAAAE,MAAA,sBACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACTH,EAAA,CAAAC,cAAA,kBAA2D;UAAzBD,EAAA,CAAAS,UAAA,mBAAA6J,2DAAA;YAAA,OAASN,GAAA,CAAAnE,YAAA,EAAc;UAAA,EAAC;UACxD7F,EAAA,CAAAE,MAAA,sBAAG;UAAAF,EAAA,CAAAqB,SAAA,aAA6B;UAGtCrB,EAFI,CAAAG,YAAA,EAAS,EACL,EACF;UAGFH,EAFJ,CAAAC,cAAA,eAAuB,eAC0B,kBACmC;UAAhDD,EAAA,CAAAS,UAAA,mBAAA8J,2DAAA;YAAA,OAASP,GAAA,CAAA5B,gBAAA,CAAiB,mBAAmB,CAAC;UAAA,EAAC;UAC7EpI,EAAA,CAAAE,MAAA,gDACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACTH,EAAA,CAAAC,cAAA,kBAAoF;UAA/CD,EAAA,CAAAS,UAAA,mBAAA+J,2DAAA;YAAA,OAASR,GAAA,CAAA5B,gBAAA,CAAiB,kBAAkB,CAAC;UAAA,EAAC;UACjFpI,EAAA,CAAAE,MAAA,gDACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACTH,EAAA,CAAAC,cAAA,kBAAgF;UAAhDD,EAAA,CAAAS,UAAA,mBAAAgK,2DAAA;YAAA,OAAST,GAAA,CAAA5B,gBAAA,CAAiB,mBAAmB,CAAC;UAAA,EAAC;UAC7EpI,EAAA,CAAAE,MAAA,0CACF;UAGNF,EAHM,CAAAG,YAAA,EAAS,EACL,EACF,EACF;UACNH,EAAA,CAAAsB,UAAA,KAAAoJ,wCAAA,kBAAuD;UAsCzD1K,EAAA,CAAAG,YAAA,EAAe;UAITH,EAHN,CAAAC,cAAA,0BAAsD,eAChC,eACgC,kBACG;UAAnBD,EAAA,CAAAS,UAAA,mBAAAkK,2DAAA;YAAA,OAASX,GAAA,CAAA9D,MAAA,EAAQ;UAAA,EAAC;UAChDlG,EAAA,CAAAE,MAAA,wCACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACTH,EAAA,CAAAC,cAAA,kBAA8D;UAAzBD,EAAA,CAAAS,UAAA,mBAAAmK,2DAAA;YAAA,OAASZ,GAAA,CAAAnE,YAAA,EAAc;UAAA,EAAC;UAC3D7F,EAAA,CAAAE,MAAA,sBACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACTH,EAAA,CAAAC,cAAA,kBAAqD;UAArBD,EAAA,CAAAS,UAAA,mBAAAoK,2DAAA;YAAA,OAASb,GAAA,CAAA1D,QAAA,EAAU;UAAA,EAAC;UAClDtG,EAAA,CAAAE,MAAA,sBACF;UAIRF,EAJQ,CAAAG,YAAA,EAAS,EACL,EACF,EACS,EACT;;;UApG4BH,EAAA,CAAAM,SAAA,IAA+C;UAA/CN,EAAA,CAAAmC,gBAAA,YAAA6H,GAAA,CAAA1F,WAAA,CAAAG,qBAAA,CAA+C;UACzCzE,EAAA,CAAAM,SAAA,EAA0B;UAA1BN,EAAA,CAAAI,UAAA,YAAA4J,GAAA,CAAAzG,uBAAA,CAA0B;UAYIvD,EAAA,CAAAM,SAAA,GAA+B;UAA/BN,EAAA,CAAAmC,gBAAA,YAAA6H,GAAA,CAAA1F,WAAA,CAAAC,KAAA,CAA+B;UAKrCvE,EAAA,CAAAM,SAAA,GAA6B;UAA7BN,EAAA,CAAAmC,gBAAA,YAAA6H,GAAA,CAAA1F,WAAA,CAAAE,GAAA,CAA6B;UA4BzDxE,EAAA,CAAAM,SAAA,IAAiB;UAAjBN,EAAA,CAAAI,UAAA,SAAA4J,GAAA,CAAAvG,WAAA,CAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}