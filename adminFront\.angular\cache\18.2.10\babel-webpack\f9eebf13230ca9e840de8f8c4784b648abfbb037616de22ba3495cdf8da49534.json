{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { EventEmitter, PLATFORM_ID, Directive, Inject, Input, Output, Optional, NgModule } from '@angular/core';\nimport { isPlatformBrowser } from '@angular/common';\nimport { Subject, merge, Observable, fromEvent } from 'rxjs';\nimport { tap, share, mergeMap, take, map, pairwise, filter, takeUntil } from 'rxjs/operators';\n\n/**\n * @hidden\n */\nconst IS_TOUCH_DEVICE = (() => {\n  // In case we're in Node.js environment.\n  if (typeof window === 'undefined') {\n    return false;\n  } else {\n    return 'ontouchstart' in window || navigator.maxTouchPoints > 0 || navigator.msMaxTouchPoints > 0;\n  }\n})();\n\n/** Creates a deep clone of an element. */\nfunction deepCloneNode(node) {\n  const clone = node.cloneNode(true);\n  const descendantsWithId = clone.querySelectorAll('[id]');\n  const nodeName = node.nodeName.toLowerCase();\n  // Remove the `id` to avoid having multiple elements with the same id on the page.\n  clone.removeAttribute('id');\n  descendantsWithId.forEach(descendant => {\n    descendant.removeAttribute('id');\n  });\n  if (nodeName === 'canvas') {\n    transferCanvasData(node, clone);\n  } else if (nodeName === 'input' || nodeName === 'select' || nodeName === 'textarea') {\n    transferInputData(node, clone);\n  }\n  transferData('canvas', node, clone, transferCanvasData);\n  transferData('input, textarea, select', node, clone, transferInputData);\n  return clone;\n}\n/** Matches elements between an element and its clone and allows for their data to be cloned. */\nfunction transferData(selector, node, clone, callback) {\n  const descendantElements = node.querySelectorAll(selector);\n  if (descendantElements.length) {\n    const cloneElements = clone.querySelectorAll(selector);\n    for (let i = 0; i < descendantElements.length; i++) {\n      callback(descendantElements[i], cloneElements[i]);\n    }\n  }\n}\n// Counter for unique cloned radio button names.\nlet cloneUniqueId = 0;\n/** Transfers the data of one input element to another. */\nfunction transferInputData(source, clone) {\n  // Browsers throw an error when assigning the value of a file input programmatically.\n  if (clone.type !== 'file') {\n    clone.value = source.value;\n  }\n  // Radio button `name` attributes must be unique for radio button groups\n  // otherwise original radio buttons can lose their checked state\n  // once the clone is inserted in the DOM.\n  if (clone.type === 'radio' && clone.name) {\n    clone.name = `mat-clone-${clone.name}-${cloneUniqueId++}`;\n  }\n}\n/** Transfers the data of one canvas element to another. */\nfunction transferCanvasData(source, clone) {\n  const context = clone.getContext('2d');\n  if (context) {\n    // In some cases `drawImage` can throw (e.g. if the canvas size is 0x0).\n    // We can't do much about it so just ignore the error.\n    try {\n      context.drawImage(source, 0, 0);\n    } catch {}\n  }\n}\nfunction getNewBoundingRectangle(startingRect, edges, clientX, clientY) {\n  const newBoundingRect = {\n    top: startingRect.top,\n    bottom: startingRect.bottom,\n    left: startingRect.left,\n    right: startingRect.right\n  };\n  if (edges.top) {\n    newBoundingRect.top += clientY;\n  }\n  if (edges.bottom) {\n    newBoundingRect.bottom += clientY;\n  }\n  if (edges.left) {\n    newBoundingRect.left += clientX;\n  }\n  if (edges.right) {\n    newBoundingRect.right += clientX;\n  }\n  newBoundingRect.height = newBoundingRect.bottom - newBoundingRect.top;\n  newBoundingRect.width = newBoundingRect.right - newBoundingRect.left;\n  return newBoundingRect;\n}\nfunction getElementRect(element, ghostElementPositioning) {\n  let translateX = 0;\n  let translateY = 0;\n  const style = element.nativeElement.style;\n  const transformProperties = ['transform', '-ms-transform', '-moz-transform', '-o-transform'];\n  const transform = transformProperties.map(property => style[property]).find(value => !!value);\n  if (transform && transform.includes('translate')) {\n    translateX = transform.replace(/.*translate3?d?\\((-?[0-9]*)px, (-?[0-9]*)px.*/, '$1');\n    translateY = transform.replace(/.*translate3?d?\\((-?[0-9]*)px, (-?[0-9]*)px.*/, '$2');\n  }\n  if (ghostElementPositioning === 'absolute') {\n    return {\n      height: element.nativeElement.offsetHeight,\n      width: element.nativeElement.offsetWidth,\n      top: element.nativeElement.offsetTop - translateY,\n      bottom: element.nativeElement.offsetHeight + element.nativeElement.offsetTop - translateY,\n      left: element.nativeElement.offsetLeft - translateX,\n      right: element.nativeElement.offsetWidth + element.nativeElement.offsetLeft - translateX\n    };\n  } else {\n    const boundingRect = element.nativeElement.getBoundingClientRect();\n    return {\n      height: boundingRect.height,\n      width: boundingRect.width,\n      top: boundingRect.top - translateY,\n      bottom: boundingRect.bottom - translateY,\n      left: boundingRect.left - translateX,\n      right: boundingRect.right - translateX,\n      scrollTop: element.nativeElement.scrollTop,\n      scrollLeft: element.nativeElement.scrollLeft\n    };\n  }\n}\nconst DEFAULT_RESIZE_CURSORS = Object.freeze({\n  topLeft: 'nw-resize',\n  topRight: 'ne-resize',\n  bottomLeft: 'sw-resize',\n  bottomRight: 'se-resize',\n  leftOrRight: 'col-resize',\n  topOrBottom: 'row-resize'\n});\nfunction getResizeCursor(edges, cursors) {\n  if (edges.left && edges.top) {\n    return cursors.topLeft;\n  } else if (edges.right && edges.top) {\n    return cursors.topRight;\n  } else if (edges.left && edges.bottom) {\n    return cursors.bottomLeft;\n  } else if (edges.right && edges.bottom) {\n    return cursors.bottomRight;\n  } else if (edges.left || edges.right) {\n    return cursors.leftOrRight;\n  } else if (edges.top || edges.bottom) {\n    return cursors.topOrBottom;\n  } else {\n    return '';\n  }\n}\nfunction getEdgesDiff({\n  edges,\n  initialRectangle,\n  newRectangle\n}) {\n  const edgesDiff = {};\n  Object.keys(edges).forEach(edge => {\n    edgesDiff[edge] = (newRectangle[edge] || 0) - (initialRectangle[edge] || 0);\n  });\n  return edgesDiff;\n}\nconst RESIZE_ACTIVE_CLASS = 'resize-active';\nconst RESIZE_GHOST_ELEMENT_CLASS = 'resize-ghost-element';\nconst MOUSE_MOVE_THROTTLE_MS = 50;\n/**\n * Place this on an element to make it resizable. For example:\n *\n * ```html\n * <div\n *   mwlResizable\n *   [resizeEdges]=\"{bottom: true, right: true, top: true, left: true}\"\n *   [enableGhostResize]=\"true\">\n * </div>\n * ```\n * Or in case they are sibling elements:\n * ```html\n * <div mwlResizable #resizableElement=\"mwlResizable\"></div>\n * <div mwlResizeHandle [resizableContainer]=\"resizableElement\" [resizeEdges]=\"{bottom: true, right: true}\"></div>\n * ```\n */\nlet ResizableDirective = /*#__PURE__*/(() => {\n  class ResizableDirective {\n    /**\n     * @hidden\n     */\n    constructor(platformId, renderer, elm, zone) {\n      this.platformId = platformId;\n      this.renderer = renderer;\n      this.elm = elm;\n      this.zone = zone;\n      /**\n       * Set to `true` to enable a temporary resizing effect of the element in between the `resizeStart` and `resizeEnd` events.\n       */\n      this.enableGhostResize = false;\n      /**\n       * A snap grid that resize events will be locked to.\n       *\n       * e.g. to only allow the element to be resized every 10px set it to `{left: 10, right: 10}`\n       */\n      this.resizeSnapGrid = {};\n      /**\n       * The mouse cursors that will be set on the resize edges\n       */\n      this.resizeCursors = DEFAULT_RESIZE_CURSORS;\n      /**\n       * Define the positioning of the ghost element (can be fixed or absolute)\n       */\n      this.ghostElementPositioning = 'fixed';\n      /**\n       * Allow elements to be resized to negative dimensions\n       */\n      this.allowNegativeResizes = false;\n      /**\n       * The mouse move throttle in milliseconds, default: 50 ms\n       */\n      this.mouseMoveThrottleMS = MOUSE_MOVE_THROTTLE_MS;\n      /**\n       * Called when the mouse is pressed and a resize event is about to begin. `$event` is a `ResizeEvent` object.\n       */\n      this.resizeStart = new EventEmitter();\n      /**\n       * Called as the mouse is dragged after a resize event has begun. `$event` is a `ResizeEvent` object.\n       */\n      this.resizing = new EventEmitter();\n      /**\n       * Called after the mouse is released after a resize event. `$event` is a `ResizeEvent` object.\n       */\n      this.resizeEnd = new EventEmitter();\n      /**\n       * @hidden\n       */\n      this.mouseup = new Subject();\n      /**\n       * @hidden\n       */\n      this.mousedown = new Subject();\n      /**\n       * @hidden\n       */\n      this.mousemove = new Subject();\n      this.destroy$ = new Subject();\n      this.pointerEventListeners = PointerEventListeners.getInstance(renderer, zone);\n    }\n    /**\n     * @hidden\n     */\n    ngOnInit() {\n      const mousedown$ = merge(this.pointerEventListeners.pointerDown, this.mousedown);\n      const mousemove$ = merge(this.pointerEventListeners.pointerMove, this.mousemove).pipe(tap(({\n        event\n      }) => {\n        if (currentResize && event.cancelable) {\n          event.preventDefault();\n        }\n      }), share());\n      const mouseup$ = merge(this.pointerEventListeners.pointerUp, this.mouseup);\n      let currentResize;\n      const removeGhostElement = () => {\n        if (currentResize && currentResize.clonedNode) {\n          this.elm.nativeElement.parentElement.removeChild(currentResize.clonedNode);\n          this.renderer.setStyle(this.elm.nativeElement, 'visibility', 'inherit');\n        }\n      };\n      const getResizeCursors = () => {\n        return {\n          ...DEFAULT_RESIZE_CURSORS,\n          ...this.resizeCursors\n        };\n      };\n      const mousedrag = mousedown$.pipe(mergeMap(startCoords => {\n        function getDiff(moveCoords) {\n          return {\n            clientX: moveCoords.clientX - startCoords.clientX,\n            clientY: moveCoords.clientY - startCoords.clientY\n          };\n        }\n        const getSnapGrid = () => {\n          const snapGrid = {\n            x: 1,\n            y: 1\n          };\n          if (currentResize) {\n            if (this.resizeSnapGrid.left && currentResize.edges.left) {\n              snapGrid.x = +this.resizeSnapGrid.left;\n            } else if (this.resizeSnapGrid.right && currentResize.edges.right) {\n              snapGrid.x = +this.resizeSnapGrid.right;\n            }\n            if (this.resizeSnapGrid.top && currentResize.edges.top) {\n              snapGrid.y = +this.resizeSnapGrid.top;\n            } else if (this.resizeSnapGrid.bottom && currentResize.edges.bottom) {\n              snapGrid.y = +this.resizeSnapGrid.bottom;\n            }\n          }\n          return snapGrid;\n        };\n        function getGrid(coords, snapGrid) {\n          return {\n            x: Math.ceil(coords.clientX / snapGrid.x),\n            y: Math.ceil(coords.clientY / snapGrid.y)\n          };\n        }\n        return merge(mousemove$.pipe(take(1)).pipe(map(coords => [, coords])), mousemove$.pipe(pairwise())).pipe(map(([previousCoords, newCoords]) => {\n          return [previousCoords ? getDiff(previousCoords) : previousCoords, getDiff(newCoords)];\n        })).pipe(filter(([previousCoords, newCoords]) => {\n          if (!previousCoords) {\n            return true;\n          }\n          const snapGrid = getSnapGrid();\n          const previousGrid = getGrid(previousCoords, snapGrid);\n          const newGrid = getGrid(newCoords, snapGrid);\n          return previousGrid.x !== newGrid.x || previousGrid.y !== newGrid.y;\n        })).pipe(map(([, newCoords]) => {\n          const snapGrid = getSnapGrid();\n          return {\n            clientX: Math.round(newCoords.clientX / snapGrid.x) * snapGrid.x,\n            clientY: Math.round(newCoords.clientY / snapGrid.y) * snapGrid.y\n          };\n        })).pipe(takeUntil(merge(mouseup$, mousedown$)));\n      })).pipe(filter(() => !!currentResize));\n      mousedrag.pipe(map(({\n        clientX,\n        clientY\n      }) => {\n        return getNewBoundingRectangle(currentResize.startingRect, currentResize.edges, clientX, clientY);\n      })).pipe(filter(newBoundingRect => {\n        return this.allowNegativeResizes || !!(newBoundingRect.height && newBoundingRect.width && newBoundingRect.height > 0 && newBoundingRect.width > 0);\n      })).pipe(filter(newBoundingRect => {\n        return this.validateResize ? this.validateResize({\n          rectangle: newBoundingRect,\n          edges: getEdgesDiff({\n            edges: currentResize.edges,\n            initialRectangle: currentResize.startingRect,\n            newRectangle: newBoundingRect\n          })\n        }) : true;\n      }), takeUntil(this.destroy$)).subscribe(newBoundingRect => {\n        if (currentResize && currentResize.clonedNode) {\n          this.renderer.setStyle(currentResize.clonedNode, 'height', `${newBoundingRect.height}px`);\n          this.renderer.setStyle(currentResize.clonedNode, 'width', `${newBoundingRect.width}px`);\n          this.renderer.setStyle(currentResize.clonedNode, 'top', `${newBoundingRect.top}px`);\n          this.renderer.setStyle(currentResize.clonedNode, 'left', `${newBoundingRect.left}px`);\n        }\n        if (this.resizing.observers.length > 0) {\n          this.zone.run(() => {\n            this.resizing.emit({\n              edges: getEdgesDiff({\n                edges: currentResize.edges,\n                initialRectangle: currentResize.startingRect,\n                newRectangle: newBoundingRect\n              }),\n              rectangle: newBoundingRect\n            });\n          });\n        }\n        currentResize.currentRect = newBoundingRect;\n      });\n      mousedown$.pipe(map(({\n        edges\n      }) => {\n        return edges || {};\n      }), filter(edges => {\n        return Object.keys(edges).length > 0;\n      }), takeUntil(this.destroy$)).subscribe(edges => {\n        if (currentResize) {\n          removeGhostElement();\n        }\n        const startingRect = getElementRect(this.elm, this.ghostElementPositioning);\n        currentResize = {\n          edges,\n          startingRect,\n          currentRect: startingRect\n        };\n        const resizeCursors = getResizeCursors();\n        const cursor = getResizeCursor(currentResize.edges, resizeCursors);\n        this.renderer.setStyle(document.body, 'cursor', cursor);\n        this.setElementClass(this.elm, RESIZE_ACTIVE_CLASS, true);\n        if (this.enableGhostResize) {\n          currentResize.clonedNode = deepCloneNode(this.elm.nativeElement);\n          this.elm.nativeElement.parentElement.appendChild(currentResize.clonedNode);\n          this.renderer.setStyle(this.elm.nativeElement, 'visibility', 'hidden');\n          this.renderer.setStyle(currentResize.clonedNode, 'position', this.ghostElementPositioning);\n          this.renderer.setStyle(currentResize.clonedNode, 'left', `${currentResize.startingRect.left}px`);\n          this.renderer.setStyle(currentResize.clonedNode, 'top', `${currentResize.startingRect.top}px`);\n          this.renderer.setStyle(currentResize.clonedNode, 'height', `${currentResize.startingRect.height}px`);\n          this.renderer.setStyle(currentResize.clonedNode, 'width', `${currentResize.startingRect.width}px`);\n          this.renderer.setStyle(currentResize.clonedNode, 'cursor', getResizeCursor(currentResize.edges, resizeCursors));\n          this.renderer.addClass(currentResize.clonedNode, RESIZE_GHOST_ELEMENT_CLASS);\n          currentResize.clonedNode.scrollTop = currentResize.startingRect.scrollTop;\n          currentResize.clonedNode.scrollLeft = currentResize.startingRect.scrollLeft;\n        }\n        if (this.resizeStart.observers.length > 0) {\n          this.zone.run(() => {\n            this.resizeStart.emit({\n              edges: getEdgesDiff({\n                edges,\n                initialRectangle: startingRect,\n                newRectangle: startingRect\n              }),\n              rectangle: getNewBoundingRectangle(startingRect, {}, 0, 0)\n            });\n          });\n        }\n      });\n      mouseup$.pipe(takeUntil(this.destroy$)).subscribe(() => {\n        if (currentResize) {\n          this.renderer.removeClass(this.elm.nativeElement, RESIZE_ACTIVE_CLASS);\n          this.renderer.setStyle(document.body, 'cursor', '');\n          this.renderer.setStyle(this.elm.nativeElement, 'cursor', '');\n          if (this.resizeEnd.observers.length > 0) {\n            this.zone.run(() => {\n              this.resizeEnd.emit({\n                edges: getEdgesDiff({\n                  edges: currentResize.edges,\n                  initialRectangle: currentResize.startingRect,\n                  newRectangle: currentResize.currentRect\n                }),\n                rectangle: currentResize.currentRect\n              });\n            });\n          }\n          removeGhostElement();\n          currentResize = null;\n        }\n      });\n    }\n    /**\n     * @hidden\n     */\n    ngOnDestroy() {\n      // browser check for angular universal, because it doesn't know what document is\n      if (isPlatformBrowser(this.platformId)) {\n        this.renderer.setStyle(document.body, 'cursor', '');\n      }\n      this.mousedown.complete();\n      this.mouseup.complete();\n      this.mousemove.complete();\n      this.destroy$.next();\n    }\n    setElementClass(elm, name, add) {\n      if (add) {\n        this.renderer.addClass(elm.nativeElement, name);\n      } else {\n        this.renderer.removeClass(elm.nativeElement, name);\n      }\n    }\n  }\n  ResizableDirective.ɵfac = function ResizableDirective_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || ResizableDirective)(i0.ɵɵdirectiveInject(PLATFORM_ID), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.NgZone));\n  };\n  ResizableDirective.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: ResizableDirective,\n    selectors: [[\"\", \"mwlResizable\", \"\"]],\n    inputs: {\n      validateResize: \"validateResize\",\n      enableGhostResize: \"enableGhostResize\",\n      resizeSnapGrid: \"resizeSnapGrid\",\n      resizeCursors: \"resizeCursors\",\n      ghostElementPositioning: \"ghostElementPositioning\",\n      allowNegativeResizes: \"allowNegativeResizes\",\n      mouseMoveThrottleMS: \"mouseMoveThrottleMS\"\n    },\n    outputs: {\n      resizeStart: \"resizeStart\",\n      resizing: \"resizing\",\n      resizeEnd: \"resizeEnd\"\n    },\n    exportAs: [\"mwlResizable\"]\n  });\n  return ResizableDirective;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nclass PointerEventListeners {\n  constructor(renderer, zone) {\n    this.pointerDown = new Observable(observer => {\n      let unsubscribeMouseDown;\n      let unsubscribeTouchStart;\n      zone.runOutsideAngular(() => {\n        unsubscribeMouseDown = renderer.listen('document', 'mousedown', event => {\n          observer.next({\n            clientX: event.clientX,\n            clientY: event.clientY,\n            event\n          });\n        });\n        if (IS_TOUCH_DEVICE) {\n          unsubscribeTouchStart = renderer.listen('document', 'touchstart', event => {\n            observer.next({\n              clientX: event.touches[0].clientX,\n              clientY: event.touches[0].clientY,\n              event\n            });\n          });\n        }\n      });\n      return () => {\n        unsubscribeMouseDown();\n        if (IS_TOUCH_DEVICE) {\n          unsubscribeTouchStart();\n        }\n      };\n    }).pipe(share());\n    this.pointerMove = new Observable(observer => {\n      let unsubscribeMouseMove;\n      let unsubscribeTouchMove;\n      zone.runOutsideAngular(() => {\n        unsubscribeMouseMove = renderer.listen('document', 'mousemove', event => {\n          observer.next({\n            clientX: event.clientX,\n            clientY: event.clientY,\n            event\n          });\n        });\n        if (IS_TOUCH_DEVICE) {\n          unsubscribeTouchMove = renderer.listen('document', 'touchmove', event => {\n            observer.next({\n              clientX: event.targetTouches[0].clientX,\n              clientY: event.targetTouches[0].clientY,\n              event\n            });\n          });\n        }\n      });\n      return () => {\n        unsubscribeMouseMove();\n        if (IS_TOUCH_DEVICE) {\n          unsubscribeTouchMove();\n        }\n      };\n    }).pipe(share());\n    this.pointerUp = new Observable(observer => {\n      let unsubscribeMouseUp;\n      let unsubscribeTouchEnd;\n      let unsubscribeTouchCancel;\n      zone.runOutsideAngular(() => {\n        unsubscribeMouseUp = renderer.listen('document', 'mouseup', event => {\n          observer.next({\n            clientX: event.clientX,\n            clientY: event.clientY,\n            event\n          });\n        });\n        if (IS_TOUCH_DEVICE) {\n          unsubscribeTouchEnd = renderer.listen('document', 'touchend', event => {\n            observer.next({\n              clientX: event.changedTouches[0].clientX,\n              clientY: event.changedTouches[0].clientY,\n              event\n            });\n          });\n          unsubscribeTouchCancel = renderer.listen('document', 'touchcancel', event => {\n            observer.next({\n              clientX: event.changedTouches[0].clientX,\n              clientY: event.changedTouches[0].clientY,\n              event\n            });\n          });\n        }\n      });\n      return () => {\n        unsubscribeMouseUp();\n        if (IS_TOUCH_DEVICE) {\n          unsubscribeTouchEnd();\n          unsubscribeTouchCancel();\n        }\n      };\n    }).pipe(share());\n  }\n  static getInstance(renderer, zone) {\n    if (!PointerEventListeners.instance) {\n      PointerEventListeners.instance = new PointerEventListeners(renderer, zone);\n    }\n    return PointerEventListeners.instance;\n  }\n}\n\n/**\n * An element placed inside a `mwlResizable` directive to be used as a drag and resize handle\n *\n * For example\n *\n * ```html\n * <div mwlResizable>\n *   <div mwlResizeHandle [resizeEdges]=\"{bottom: true, right: true}\"></div>\n * </div>\n * ```\n * Or in case they are sibling elements:\n * ```html\n * <div mwlResizable #resizableElement=\"mwlResizable\"></div>\n * <div mwlResizeHandle [resizableContainer]=\"resizableElement\" [resizeEdges]=\"{bottom: true, right: true}\"></div>\n * ```\n */\nlet ResizeHandleDirective = /*#__PURE__*/(() => {\n  class ResizeHandleDirective {\n    constructor(renderer, element, zone, resizableDirective) {\n      this.renderer = renderer;\n      this.element = element;\n      this.zone = zone;\n      this.resizableDirective = resizableDirective;\n      /**\n       * The `Edges` object that contains the edges of the parent element that dragging the handle will trigger a resize on\n       */\n      this.resizeEdges = {};\n      this.eventListeners = {};\n      this.destroy$ = new Subject();\n    }\n    ngOnInit() {\n      this.zone.runOutsideAngular(() => {\n        this.listenOnTheHost('mousedown').subscribe(event => {\n          this.onMousedown(event, event.clientX, event.clientY);\n        });\n        this.listenOnTheHost('mouseup').subscribe(event => {\n          this.onMouseup(event.clientX, event.clientY);\n        });\n        if (IS_TOUCH_DEVICE) {\n          this.listenOnTheHost('touchstart').subscribe(event => {\n            this.onMousedown(event, event.touches[0].clientX, event.touches[0].clientY);\n          });\n          merge(this.listenOnTheHost('touchend'), this.listenOnTheHost('touchcancel')).subscribe(event => {\n            this.onMouseup(event.changedTouches[0].clientX, event.changedTouches[0].clientY);\n          });\n        }\n      });\n    }\n    ngOnDestroy() {\n      this.destroy$.next();\n      this.unsubscribeEventListeners();\n    }\n    /**\n     * @hidden\n     */\n    onMousedown(event, clientX, clientY) {\n      if (event.cancelable) {\n        event.preventDefault();\n      }\n      if (!this.eventListeners.touchmove) {\n        this.eventListeners.touchmove = this.renderer.listen(this.element.nativeElement, 'touchmove', touchMoveEvent => {\n          this.onMousemove(touchMoveEvent, touchMoveEvent.targetTouches[0].clientX, touchMoveEvent.targetTouches[0].clientY);\n        });\n      }\n      if (!this.eventListeners.mousemove) {\n        this.eventListeners.mousemove = this.renderer.listen(this.element.nativeElement, 'mousemove', mouseMoveEvent => {\n          this.onMousemove(mouseMoveEvent, mouseMoveEvent.clientX, mouseMoveEvent.clientY);\n        });\n      }\n      this.resizable.mousedown.next({\n        clientX,\n        clientY,\n        edges: this.resizeEdges\n      });\n    }\n    /**\n     * @hidden\n     */\n    onMouseup(clientX, clientY) {\n      this.unsubscribeEventListeners();\n      this.resizable.mouseup.next({\n        clientX,\n        clientY,\n        edges: this.resizeEdges\n      });\n    }\n    // directive might be passed from DI or as an input\n    get resizable() {\n      return this.resizableDirective || this.resizableContainer;\n    }\n    onMousemove(event, clientX, clientY) {\n      this.resizable.mousemove.next({\n        clientX,\n        clientY,\n        edges: this.resizeEdges,\n        event\n      });\n    }\n    unsubscribeEventListeners() {\n      Object.keys(this.eventListeners).forEach(type => {\n        this.eventListeners[type]();\n        delete this.eventListeners[type];\n      });\n    }\n    listenOnTheHost(eventName) {\n      return fromEvent(this.element.nativeElement, eventName).pipe(takeUntil(this.destroy$));\n    }\n  }\n  ResizeHandleDirective.ɵfac = function ResizeHandleDirective_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || ResizeHandleDirective)(i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(ResizableDirective, 8));\n  };\n  ResizeHandleDirective.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: ResizeHandleDirective,\n    selectors: [[\"\", \"mwlResizeHandle\", \"\"]],\n    inputs: {\n      resizeEdges: \"resizeEdges\",\n      resizableContainer: \"resizableContainer\"\n    }\n  });\n  return ResizeHandleDirective;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet ResizableModule = /*#__PURE__*/(() => {\n  class ResizableModule {}\n  ResizableModule.ɵfac = function ResizableModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || ResizableModule)();\n  };\n  ResizableModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: ResizableModule\n  });\n  ResizableModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({});\n  return ResizableModule;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/*\n * Public API Surface of angular-resizable-element\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { ResizableDirective, ResizableModule, ResizeHandleDirective };\n//# sourceMappingURL=angular-resizable-element.mjs.map\n//# sourceMappingURL=angular-resizable-element.mjs.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}