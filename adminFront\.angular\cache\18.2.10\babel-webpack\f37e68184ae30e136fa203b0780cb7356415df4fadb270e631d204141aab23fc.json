{"ast": null, "code": "import { DOCUMENT, NgIf, Ng<PERSON><PERSON>plateOutlet, <PERSON><PERSON><PERSON><PERSON>, NgClass } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { booleanAttribute, Directive, Inject, Input, EventEmitter, numberAttribute, Component, ChangeDetectionStrategy, ViewEncapsulation, Output, ContentChildren, NgModule } from '@angular/core';\nimport { PrimeTemplate, SharedModule } from 'primeng/api';\nimport { AutoFocus } from 'primeng/autofocus';\nimport { <PERSON>Hand<PERSON> } from 'primeng/dom';\nimport { SpinnerIcon } from 'primeng/icons/spinner';\nimport { Ripple } from 'primeng/ripple';\nimport { ObjectUtils } from 'primeng/utils';\nconst _c0 = [\"*\"];\nconst _c1 = a0 => ({\n  class: a0\n});\nfunction Button_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction Button_ng_container_3_ng_container_1_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 8);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"ngClass\", ctx_r0.iconClass());\n    i0.ɵɵattribute(\"aria-hidden\", true)(\"data-pc-section\", \"loadingicon\");\n  }\n}\nfunction Button_ng_container_3_ng_container_1_SpinnerIcon_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"SpinnerIcon\", 9);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"styleClass\", ctx_r0.spinnerIconClass())(\"spin\", true);\n    i0.ɵɵattribute(\"aria-hidden\", true)(\"data-pc-section\", \"loadingicon\");\n  }\n}\nfunction Button_ng_container_3_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, Button_ng_container_3_ng_container_1_span_1_Template, 1, 3, \"span\", 6)(2, Button_ng_container_3_ng_container_1_SpinnerIcon_2_Template, 1, 4, \"SpinnerIcon\", 7);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.loadingIcon);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r0.loadingIcon);\n  }\n}\nfunction Button_ng_container_3_2_ng_template_0_Template(rf, ctx) {}\nfunction Button_ng_container_3_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, Button_ng_container_3_2_ng_template_0_Template, 0, 0, \"ng-template\", 10);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.loadingIconTemplate);\n  }\n}\nfunction Button_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, Button_ng_container_3_ng_container_1_Template, 3, 2, \"ng-container\", 2)(2, Button_ng_container_3_2_Template, 1, 1, null, 5);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r0.loadingIconTemplate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r0.loadingIconTemplate)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(3, _c1, ctx_r0.iconClass()));\n  }\n}\nfunction Button_ng_container_4_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 8);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngClass\", ctx_r0.iconClass());\n    i0.ɵɵattribute(\"data-pc-section\", \"icon\");\n  }\n}\nfunction Button_ng_container_4_2_ng_template_0_Template(rf, ctx) {}\nfunction Button_ng_container_4_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, Button_ng_container_4_2_ng_template_0_Template, 0, 0, \"ng-template\", 10);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r0.icon && ctx_r0.iconTemplate);\n  }\n}\nfunction Button_ng_container_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, Button_ng_container_4_span_1_Template, 1, 2, \"span\", 6)(2, Button_ng_container_4_2_Template, 1, 1, null, 5);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.icon && !ctx_r0.iconTemplate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r0.iconTemplate)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(3, _c1, ctx_r0.iconClass()));\n  }\n}\nfunction Button_span_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 11);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵattribute(\"aria-hidden\", ctx_r0.icon && !ctx_r0.label)(\"data-pc-section\", \"label\");\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r0.label);\n  }\n}\nfunction Button_span_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 8);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵclassMap(ctx_r0.badgeClass);\n    i0.ɵɵproperty(\"ngClass\", ctx_r0.badgeStyleClass());\n    i0.ɵɵattribute(\"data-pc-section\", \"badge\");\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r0.badge);\n  }\n}\nconst INTERNAL_BUTTON_CLASSES = {\n  button: 'p-button',\n  component: 'p-component',\n  iconOnly: 'p-button-icon-only',\n  disabled: 'p-disabled',\n  loading: 'p-button-loading',\n  labelOnly: 'p-button-loading-label-only'\n};\n/**\n * Button directive is an extension to button component.\n * @group Components\n */\nlet ButtonDirective = /*#__PURE__*/(() => {\n  class ButtonDirective {\n    el;\n    document;\n    /**\n     * Position of the icon.\n     * @group Props\n     */\n    iconPos = 'left';\n    /**\n     * Uses to pass attributes to the loading icon's DOM element.\n     * @group Props\n     */\n    loadingIcon;\n    /**\n     * Text of the button.\n     * @group Props\n     */\n    get label() {\n      return this._label;\n    }\n    set label(val) {\n      this._label = val;\n      if (this.initialized) {\n        this.updateLabel();\n        this.updateIcon();\n        this.setStyleClass();\n      }\n    }\n    /**\n     * Name of the icon.\n     * @group Props\n     */\n    get icon() {\n      return this._icon;\n    }\n    set icon(val) {\n      this._icon = val;\n      if (this.initialized) {\n        this.updateIcon();\n        this.setStyleClass();\n      }\n    }\n    /**\n     * Whether the button is in loading state.\n     * @group Props\n     */\n    get loading() {\n      return this._loading;\n    }\n    set loading(val) {\n      this._loading = val;\n      if (this.initialized) {\n        this.updateIcon();\n        this.setStyleClass();\n      }\n    }\n    /**\n     * Defines the style of the button.\n     * @group Props\n     */\n    severity;\n    /**\n     * Add a shadow to indicate elevation.\n     * @group Props\n     */\n    raised = false;\n    /**\n     * Add a circular border radius to the button.\n     * @group Props\n     */\n    rounded = false;\n    /**\n     * Add a textual class to the button without a background initially.\n     * @group Props\n     */\n    text = false;\n    /**\n     * Add a border class without a background initially.\n     * @group Props\n     */\n    outlined = false;\n    /**\n     * Defines the size of the button.\n     * @group Props\n     */\n    size = null;\n    /**\n     * Add a plain textual class to the button without a background initially.\n     * @group Props\n     */\n    plain = false;\n    _label;\n    _icon;\n    _loading = false;\n    initialized;\n    get htmlElement() {\n      return this.el.nativeElement;\n    }\n    _internalClasses = Object.values(INTERNAL_BUTTON_CLASSES);\n    constructor(el, document) {\n      this.el = el;\n      this.document = document;\n    }\n    ngAfterViewInit() {\n      DomHandler.addMultipleClasses(this.htmlElement, this.getStyleClass().join(' '));\n      this.createIcon();\n      this.createLabel();\n      this.initialized = true;\n    }\n    getStyleClass() {\n      const styleClass = [INTERNAL_BUTTON_CLASSES.button, INTERNAL_BUTTON_CLASSES.component];\n      if (this.icon && !this.label && ObjectUtils.isEmpty(this.htmlElement.textContent)) {\n        styleClass.push(INTERNAL_BUTTON_CLASSES.iconOnly);\n      }\n      if (this.loading) {\n        styleClass.push(INTERNAL_BUTTON_CLASSES.disabled, INTERNAL_BUTTON_CLASSES.loading);\n        if (!this.icon && this.label) {\n          styleClass.push(INTERNAL_BUTTON_CLASSES.labelOnly);\n        }\n        if (this.icon && !this.label && !ObjectUtils.isEmpty(this.htmlElement.textContent)) {\n          styleClass.push(INTERNAL_BUTTON_CLASSES.iconOnly);\n        }\n      }\n      if (this.text) {\n        styleClass.push('p-button-text');\n      }\n      if (this.severity) {\n        styleClass.push(`p-button-${this.severity}`);\n      }\n      if (this.plain) {\n        styleClass.push('p-button-plain');\n      }\n      if (this.raised) {\n        styleClass.push('p-button-raised');\n      }\n      if (this.size) {\n        styleClass.push(`p-button-${this.size}`);\n      }\n      if (this.outlined) {\n        styleClass.push('p-button-outlined');\n      }\n      if (this.rounded) {\n        styleClass.push('p-button-rounded');\n      }\n      if (this.size === 'small') {\n        styleClass.push('p-button-sm');\n      }\n      if (this.size === 'large') {\n        styleClass.push('p-button-lg');\n      }\n      return styleClass;\n    }\n    setStyleClass() {\n      const styleClass = this.getStyleClass();\n      this.htmlElement.classList.remove(...this._internalClasses);\n      this.htmlElement.classList.add(...styleClass);\n    }\n    createLabel() {\n      const created = DomHandler.findSingle(this.htmlElement, '.p-button-label');\n      if (!created && this.label) {\n        let labelElement = this.document.createElement('span');\n        if (this.icon && !this.label) {\n          labelElement.setAttribute('aria-hidden', 'true');\n        }\n        labelElement.className = 'p-button-label';\n        labelElement.appendChild(this.document.createTextNode(this.label));\n        this.htmlElement.appendChild(labelElement);\n      }\n    }\n    createIcon() {\n      const created = DomHandler.findSingle(this.htmlElement, '.p-button-icon');\n      if (!created && (this.icon || this.loading)) {\n        let iconElement = this.document.createElement('span');\n        iconElement.className = 'p-button-icon';\n        iconElement.setAttribute('aria-hidden', 'true');\n        let iconPosClass = this.label ? 'p-button-icon-' + this.iconPos : null;\n        if (iconPosClass) {\n          DomHandler.addClass(iconElement, iconPosClass);\n        }\n        let iconClass = this.getIconClass();\n        if (iconClass) {\n          DomHandler.addMultipleClasses(iconElement, iconClass);\n        }\n        this.htmlElement.insertBefore(iconElement, this.htmlElement.firstChild);\n      }\n    }\n    updateLabel() {\n      let labelElement = DomHandler.findSingle(this.htmlElement, '.p-button-label');\n      if (!this.label) {\n        labelElement && this.htmlElement.removeChild(labelElement);\n        return;\n      }\n      labelElement ? labelElement.textContent = this.label : this.createLabel();\n    }\n    updateIcon() {\n      let iconElement = DomHandler.findSingle(this.htmlElement, '.p-button-icon');\n      let labelElement = DomHandler.findSingle(this.htmlElement, '.p-button-label');\n      if (iconElement) {\n        if (this.iconPos) {\n          iconElement.className = 'p-button-icon ' + (labelElement ? 'p-button-icon-' + this.iconPos : '') + ' ' + this.getIconClass();\n        } else {\n          iconElement.className = 'p-button-icon ' + this.getIconClass();\n        }\n      } else {\n        this.createIcon();\n      }\n    }\n    getIconClass() {\n      return this.loading ? 'p-button-loading-icon pi-spin ' + (this.loadingIcon ?? 'pi pi-spinner') : this.icon || 'p-hidden';\n    }\n    ngOnDestroy() {\n      this.initialized = false;\n    }\n    static ɵfac = function ButtonDirective_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || ButtonDirective)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(DOCUMENT));\n    };\n    static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: ButtonDirective,\n      selectors: [[\"\", \"pButton\", \"\"]],\n      hostAttrs: [1, \"p-element\"],\n      inputs: {\n        iconPos: \"iconPos\",\n        loadingIcon: \"loadingIcon\",\n        label: \"label\",\n        icon: \"icon\",\n        loading: \"loading\",\n        severity: \"severity\",\n        raised: [2, \"raised\", \"raised\", booleanAttribute],\n        rounded: [2, \"rounded\", \"rounded\", booleanAttribute],\n        text: [2, \"text\", \"text\", booleanAttribute],\n        outlined: [2, \"outlined\", \"outlined\", booleanAttribute],\n        size: \"size\",\n        plain: [2, \"plain\", \"plain\", booleanAttribute]\n      },\n      standalone: true,\n      features: [i0.ɵɵInputTransformsFeature]\n    });\n  }\n  return ButtonDirective;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * Button is an extension to standard button element with icons and theming.\n * @group Components\n */\nlet Button = /*#__PURE__*/(() => {\n  class Button {\n    el;\n    /**\n     * Type of the button.\n     * @group Props\n     */\n    type = 'button';\n    /**\n     * Position of the icon.\n     * @group Props\n     */\n    iconPos = 'left';\n    /**\n     * Name of the icon.\n     * @group Props\n     */\n    icon;\n    /**\n     * Value of the badge.\n     * @group Props\n     */\n    badge;\n    /**\n     * Uses to pass attributes to the label's DOM element.\n     * @group Props\n     */\n    label;\n    /**\n     * When present, it specifies that the component should be disabled.\n     * @group Props\n     */\n    disabled;\n    /**\n     * Whether the button is in loading state.\n     * @group Props\n     */\n    loading = false;\n    /**\n     * Icon to display in loading state.\n     * @group Props\n     */\n    loadingIcon;\n    /**\n     * Add a shadow to indicate elevation.\n     * @group Props\n     */\n    raised = false;\n    /**\n     * Add a circular border radius to the button.\n     * @group Props\n     */\n    rounded = false;\n    /**\n     * Add a textual class to the button without a background initially.\n     * @group Props\n     */\n    text = false;\n    /**\n     * Add a plain textual class to the button without a background initially.\n     * @group Props\n     */\n    plain = false;\n    /**\n     * Defines the style of the button.\n     * @group Props\n     */\n    severity;\n    /**\n     * Add a border class without a background initially.\n     * @group Props\n     */\n    outlined = false;\n    /**\n     * Add a link style to the button.\n     * @group Props\n     */\n    link = false;\n    /**\n     * Add a tabindex to the button.\n     * @group Props\n     */\n    tabindex;\n    /**\n     * Defines the size of the button.\n     * @group Props\n     */\n    size;\n    /**\n     * Inline style of the element.\n     * @group Props\n     */\n    style;\n    /**\n     * Class of the element.\n     * @group Props\n     */\n    styleClass;\n    /**\n     * Style class of the badge.\n     * @group Props\n     */\n    badgeClass;\n    /**\n     * Used to define a string that autocomplete attribute the current element.\n     * @group Props\n     */\n    ariaLabel;\n    /**\n     * When present, it specifies that the component should automatically get focus on load.\n     * @group Props\n     */\n    autofocus;\n    /**\n     * Callback to execute when button is clicked.\n     * This event is intended to be used with the <p-button> component. Using a regular <button> element, use (click).\n     * @param {MouseEvent} event - Mouse event.\n     * @group Emits\n     */\n    onClick = new EventEmitter();\n    /**\n     * Callback to execute when button is focused.\n     * This event is intended to be used with the <p-button> component. Using a regular <button> element, use (focus).\n     * @param {FocusEvent} event - Focus event.\n     * @group Emits\n     */\n    onFocus = new EventEmitter();\n    /**\n     * Callback to execute when button loses focus.\n     * This event is intended to be used with the <p-button> component. Using a regular <button> element, use (blur).\n     * @param {FocusEvent} event - Focus event.\n     * @group Emits\n     */\n    onBlur = new EventEmitter();\n    contentTemplate;\n    loadingIconTemplate;\n    iconTemplate;\n    templates;\n    constructor(el) {\n      this.el = el;\n    }\n    spinnerIconClass() {\n      return Object.entries(this.iconClass()).filter(([, value]) => !!value).reduce((acc, [key]) => acc + ` ${key}`, 'p-button-loading-icon');\n    }\n    iconClass() {\n      const iconClasses = {\n        'p-button-icon': true,\n        'p-button-icon-left': this.iconPos === 'left' && this.label,\n        'p-button-icon-right': this.iconPos === 'right' && this.label,\n        'p-button-icon-top': this.iconPos === 'top' && this.label,\n        'p-button-icon-bottom': this.iconPos === 'bottom' && this.label\n      };\n      if (this.loading) {\n        iconClasses[`p-button-loading-icon pi-spin ${this.loadingIcon ?? ''}`] = true;\n      } else if (this.icon) {\n        iconClasses[this.icon] = true;\n      }\n      return iconClasses;\n    }\n    get buttonClass() {\n      return {\n        'p-button p-component': true,\n        'p-button-icon-only': (this.icon || this.iconTemplate || this.loadingIcon || this.loadingIconTemplate) && !this.label,\n        'p-button-vertical': (this.iconPos === 'top' || this.iconPos === 'bottom') && this.label,\n        'p-button-loading': this.loading,\n        'p-button-loading-label-only': this.loading && !this.icon && this.label && !this.loadingIcon && this.iconPos === 'left',\n        'p-button-link': this.link,\n        [`p-button-${this.severity}`]: this.severity,\n        'p-button-raised': this.raised,\n        'p-button-rounded': this.rounded,\n        'p-button-text': this.text,\n        'p-button-outlined': this.outlined,\n        'p-button-sm': this.size === 'small',\n        'p-button-lg': this.size === 'large',\n        'p-button-plain': this.plain,\n        [`${this.styleClass}`]: this.styleClass\n      };\n    }\n    ngAfterContentInit() {\n      this.templates?.forEach(item => {\n        switch (item.getType()) {\n          case 'content':\n            this.contentTemplate = item.template;\n            break;\n          case 'icon':\n            this.iconTemplate = item.template;\n            break;\n          case 'loadingicon':\n            this.loadingIconTemplate = item.template;\n            break;\n          default:\n            this.contentTemplate = item.template;\n            break;\n        }\n      });\n    }\n    badgeStyleClass() {\n      return {\n        'p-badge p-component': true,\n        'p-badge-no-gutter': this.badge && String(this.badge).length === 1\n      };\n    }\n    /**\n     * Applies focus.\n     * @group Method\n     */\n    focus() {\n      this.el.nativeElement.firstChild.focus();\n    }\n    static ɵfac = function Button_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || Button)(i0.ɵɵdirectiveInject(i0.ElementRef));\n    };\n    static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: Button,\n      selectors: [[\"p-button\"]],\n      contentQueries: function Button_ContentQueries(rf, ctx, dirIndex) {\n        if (rf & 1) {\n          i0.ɵɵcontentQuery(dirIndex, PrimeTemplate, 4);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.templates = _t);\n        }\n      },\n      hostAttrs: [1, \"p-element\"],\n      hostVars: 2,\n      hostBindings: function Button_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"p-disabled\", ctx.disabled);\n        }\n      },\n      inputs: {\n        type: \"type\",\n        iconPos: \"iconPos\",\n        icon: \"icon\",\n        badge: \"badge\",\n        label: \"label\",\n        disabled: [2, \"disabled\", \"disabled\", booleanAttribute],\n        loading: [2, \"loading\", \"loading\", booleanAttribute],\n        loadingIcon: \"loadingIcon\",\n        raised: [2, \"raised\", \"raised\", booleanAttribute],\n        rounded: [2, \"rounded\", \"rounded\", booleanAttribute],\n        text: [2, \"text\", \"text\", booleanAttribute],\n        plain: [2, \"plain\", \"plain\", booleanAttribute],\n        severity: \"severity\",\n        outlined: [2, \"outlined\", \"outlined\", booleanAttribute],\n        link: [2, \"link\", \"link\", booleanAttribute],\n        tabindex: [2, \"tabindex\", \"tabindex\", numberAttribute],\n        size: \"size\",\n        style: \"style\",\n        styleClass: \"styleClass\",\n        badgeClass: \"badgeClass\",\n        ariaLabel: \"ariaLabel\",\n        autofocus: [2, \"autofocus\", \"autofocus\", booleanAttribute]\n      },\n      outputs: {\n        onClick: \"onClick\",\n        onFocus: \"onFocus\",\n        onBlur: \"onBlur\"\n      },\n      standalone: true,\n      features: [i0.ɵɵInputTransformsFeature, i0.ɵɵStandaloneFeature],\n      ngContentSelectors: _c0,\n      decls: 7,\n      vars: 14,\n      consts: [[\"pRipple\", \"\", \"pAutoFocus\", \"\", 3, \"click\", \"focus\", \"blur\", \"ngStyle\", \"disabled\", \"ngClass\", \"autofocus\"], [4, \"ngTemplateOutlet\"], [4, \"ngIf\"], [\"class\", \"p-button-label\", 4, \"ngIf\"], [3, \"ngClass\", \"class\", 4, \"ngIf\"], [4, \"ngTemplateOutlet\", \"ngTemplateOutletContext\"], [3, \"ngClass\", 4, \"ngIf\"], [3, \"styleClass\", \"spin\", 4, \"ngIf\"], [3, \"ngClass\"], [3, \"styleClass\", \"spin\"], [3, \"ngIf\"], [1, \"p-button-label\"]],\n      template: function Button_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef();\n          i0.ɵɵelementStart(0, \"button\", 0);\n          i0.ɵɵlistener(\"click\", function Button_Template_button_click_0_listener($event) {\n            return ctx.onClick.emit($event);\n          })(\"focus\", function Button_Template_button_focus_0_listener($event) {\n            return ctx.onFocus.emit($event);\n          })(\"blur\", function Button_Template_button_blur_0_listener($event) {\n            return ctx.onBlur.emit($event);\n          });\n          i0.ɵɵprojection(1);\n          i0.ɵɵtemplate(2, Button_ng_container_2_Template, 1, 0, \"ng-container\", 1)(3, Button_ng_container_3_Template, 3, 5, \"ng-container\", 2)(4, Button_ng_container_4_Template, 3, 5, \"ng-container\", 2)(5, Button_span_5_Template, 2, 3, \"span\", 3)(6, Button_span_6_Template, 2, 5, \"span\", 4);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngStyle\", ctx.style)(\"disabled\", ctx.disabled || ctx.loading)(\"ngClass\", ctx.buttonClass)(\"autofocus\", ctx.autofocus);\n          i0.ɵɵattribute(\"type\", ctx.type)(\"aria-label\", ctx.ariaLabel)(\"data-pc-name\", \"button\")(\"data-pc-section\", \"root\")(\"tabindex\", ctx.tabindex);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngTemplateOutlet\", ctx.contentTemplate);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.loading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.loading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.contentTemplate && ctx.label);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.contentTemplate && ctx.badge);\n        }\n      },\n      dependencies: [NgIf, NgTemplateOutlet, NgStyle, NgClass, Ripple, AutoFocus, SpinnerIcon],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n  return Button;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet ButtonModule = /*#__PURE__*/(() => {\n  class ButtonModule {\n    static ɵfac = function ButtonModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || ButtonModule)();\n    };\n    static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: ButtonModule\n    });\n    static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      imports: [Button, SharedModule]\n    });\n  }\n  return ButtonModule;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { Button, ButtonDirective, ButtonModule };\n//# sourceMappingURL=primeng-button.mjs.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}