{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/Documents/jeansalechange/adminFront/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { BaseComponent } from '../../components/base/baseComponent';\nimport { NbCardModule, NbInputModule, NbSelectModule, NbOptionModule, NbIconModule, NbCheckboxModule } from '@nebular/theme';\nimport { StatusPipe } from '../../../@theme/pipes/mapping.pipe';\nimport { MomentPipe } from '../../../@theme/pipes/moment.pipe';\nimport { NgIf, NgFor } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { ShareRequest } from 'src/app/shared/model/requests/shareRequest';\nimport { BreadcrumbComponent } from '../../components/breadcrumb/breadcrumb.component';\nimport { PaginationComponent } from '../../components/pagination/pagination.component';\nimport { lastValueFrom } from 'rxjs';\nimport { EEvent } from 'src/app/shared/services/event.service';\nimport { LocalStorageService } from 'src/app/shared/services/local-storage.service';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@nebular/theme\";\nimport * as i2 from \"src/services/api/services\";\nimport * as i3 from \"src/app/shared/services/message.service\";\nimport * as i4 from \"../../components/shared.observable\";\nimport * as i5 from \"src/app/shared/helper/allowHelper\";\nimport * as i6 from \"src/app/shared/helper/validationHelper\";\nimport * as i7 from \"src/app/shared/services/event.service\";\nimport * as i8 from \"@angular/router\";\nimport * as i9 from \"@angular/forms\";\nfunction RolePermissionsComponent_button_24_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 25);\n    i0.ɵɵlistener(\"click\", function RolePermissionsComponent_button_24_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      const dialog_r4 = i0.ɵɵreference(45);\n      return i0.ɵɵresetView(ctx_r2.add(dialog_r4));\n    });\n    i0.ɵɵelement(1, \"i\", 26);\n    i0.ɵɵtext(2, \"\\u65B0\\u589E\\u89D2\\u8272\\u6B0A\\u9650\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RolePermissionsComponent_tr_42_button_12_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 34);\n    i0.ɵɵlistener(\"click\", function RolePermissionsComponent_tr_42_button_12_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const data_r6 = i0.ɵɵnextContext().$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      const dialog_r4 = i0.ɵɵreference(45);\n      return i0.ɵɵresetView(ctx_r2.onEdit(data_r6, dialog_r4));\n    });\n    i0.ɵɵelement(1, \"i\", 35);\n    i0.ɵɵtext(2, \"\\u7DE8\\u8F2F\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RolePermissionsComponent_tr_42_button_13_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 36);\n    i0.ɵɵlistener(\"click\", function RolePermissionsComponent_tr_42_button_13_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const data_r6 = i0.ɵɵnextContext().$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onDelete(data_r6));\n    });\n    i0.ɵɵelement(1, \"i\", 37);\n    i0.ɵɵtext(2, \"\\u522A\\u9664\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RolePermissionsComponent_tr_42_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\", 27)(1, \"td\", 28);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\", 29);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\", 28);\n    i0.ɵɵtext(6);\n    i0.ɵɵpipe(7, \"getStatusName\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"td\", 30);\n    i0.ɵɵtext(9);\n    i0.ɵɵpipe(10, \"localDate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"td\", 31);\n    i0.ɵɵtemplate(12, RolePermissionsComponent_tr_42_button_12_Template, 3, 0, \"button\", 32)(13, RolePermissionsComponent_tr_42_button_13_Template, 3, 0, \"button\", 33);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const data_r6 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(data_r6.CId);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(data_r6.CName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(7, 6, data_r6.CStatus));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(10, 8, data_r6.CUpdateDt, \"yyyy-MM-DD HH:mm:ss\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isUpdate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isDelete);\n  }\n}\nfunction RolePermissionsComponent_ng_template_44_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"\\u65B0\\u589E\\u89D2\\u8272\\u6B0A\\u9650\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RolePermissionsComponent_ng_template_44_span_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"\\u7DE8\\u8F2F\\u89D2\\u8272\\u6B0A\\u9650\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RolePermissionsComponent_ng_template_44_div_14_div_1_div_4_div_1_label_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"label\", 57)(1, \"nb-checkbox\", 58);\n    i0.ɵɵtwoWayListener(\"checkedChange\", function RolePermissionsComponent_ng_template_44_div_14_div_1_div_4_div_1_label_4_Template_nb_checkbox_checkedChange_1_listener($event) {\n      const auth_r10 = i0.ɵɵrestoreView(_r9).$implicit;\n      i0.ɵɵtwoWayBindingSet(auth_r10.IsChecked, $event) || (auth_r10.IsChecked = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const auth_r10 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"checked\", auth_r10.IsChecked);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\"\", auth_r10.CName, \" \");\n  }\n}\nfunction RolePermissionsComponent_ng_template_44_div_14_div_1_div_4_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 54)(1, \"div\", 55);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 44);\n    i0.ɵɵtemplate(4, RolePermissionsComponent_ng_template_44_div_14_div_1_div_4_div_1_label_4_Template, 3, 2, \"label\", 56);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const v2_r11 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(v2_r11.CName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", v2_r11.Authority);\n  }\n}\nfunction RolePermissionsComponent_ng_template_44_div_14_div_1_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 3);\n    i0.ɵɵtemplate(1, RolePermissionsComponent_ng_template_44_div_14_div_1_div_4_div_1_Template, 5, 2, \"div\", 53);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const v2_r11 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", v2_r11.Authority.length > 0);\n  }\n}\nfunction RolePermissionsComponent_ng_template_44_div_14_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 49)(1, \"div\", 50);\n    i0.ɵɵelement(2, \"nb-icon\", 51);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, RolePermissionsComponent_ng_template_44_div_14_div_1_div_4_Template, 2, 1, \"div\", 52);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const v1_r12 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\", v1_r12.CName, \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", v1_r12.FunctionLv2);\n  }\n}\nfunction RolePermissionsComponent_ng_template_44_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 3);\n    i0.ɵɵtemplate(1, RolePermissionsComponent_ng_template_44_div_14_div_1_Template, 5, 2, \"div\", 48);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const v1_r12 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", v1_r12.FunctionLv2.length > 0);\n  }\n}\nfunction RolePermissionsComponent_ng_template_44_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-card\", 38)(1, \"nb-card-header\");\n    i0.ɵɵtemplate(2, RolePermissionsComponent_ng_template_44_span_2_Template, 2, 0, \"span\", 39)(3, RolePermissionsComponent_ng_template_44_span_3_Template, 2, 0, \"span\", 39);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"nb-card-body\")(5, \"div\", 40)(6, \"div\", 41)(7, \"div\", 4)(8, \"div\", 42)(9, \"label\", 43);\n    i0.ɵɵtext(10, \"\\u89D2\\u8272\\u540D\\u7A31\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"div\", 44)(12, \"input\", 7);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function RolePermissionsComponent_ng_template_44_Template_input_ngModelChange_12_listener($event) {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.userGroupFunction.CName, $event) || (ctx_r2.userGroupFunction.CName = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(13, \"div\", 4);\n    i0.ɵɵtemplate(14, RolePermissionsComponent_ng_template_44_div_14_Template, 2, 1, \"div\", 45);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(15, \"nb-card-footer\")(16, \"div\", 4)(17, \"div\", 46)(18, \"button\", 25);\n    i0.ɵɵlistener(\"click\", function RolePermissionsComponent_ng_template_44_Template_button_click_18_listener() {\n      const ref_r13 = i0.ɵɵrestoreView(_r8).dialogRef;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.save(ref_r13));\n    });\n    i0.ɵɵtext(19, \"\\u78BA\\u5B9A\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"button\", 47);\n    i0.ɵɵlistener(\"click\", function RolePermissionsComponent_ng_template_44_Template_button_click_20_listener() {\n      const ref_r13 = i0.ɵɵrestoreView(_r8).dialogRef;\n      return i0.ɵɵresetView(ref_r13.close());\n    });\n    i0.ɵɵtext(21, \"\\u53D6\\u6D88\");\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isNew === true);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isNew === false);\n    i0.ɵɵadvance(9);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.userGroupFunction.CName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.userGroupFunction.FunctionLv1);\n  }\n}\nexport class RolePermissionsComponent extends BaseComponent {\n  constructor(dialogService, userGroupService, message, share, allow, valid, _eventService, _router) {\n    super(allow);\n    this.dialogService = dialogService;\n    this.userGroupService = userGroupService;\n    this.message = message;\n    this.share = share;\n    this.allow = allow;\n    this.valid = valid;\n    this._eventService = _eventService;\n    this._router = _router;\n    this.userGroups = [];\n    this.userGroupFunction = {};\n    this.request = new ShareRequest();\n    this.isNew = false;\n    this.selectedItem = '';\n    this.share.SharedUserGroup.subscribe(res => {\n      this.userGroups = res;\n    });\n    this.share.SharedUserGroupFunction.subscribe(res => {\n      this.userGroupFunction = res;\n    });\n    this.getList();\n  }\n  ngOnInit() {}\n  getList() {\n    this.request.PageSize = this.pageSize;\n    this.request.PageIndex = this.pageIndex;\n    this.userGroupService.apiUserGroupGetListPost$Json({\n      body: {\n        ...this.request\n      }\n    }).subscribe(res => {\n      this.userGroups = res.Entries;\n      this.totalRecords = res.TotalItems;\n      this.share.SetUserGroup(this.userGroups);\n    });\n  }\n  getFunction() {\n    return lastValueFrom(this.userGroupService.apiUserGroupGetDataPost$Json({\n      body: {\n        CId: this.request.CId\n      }\n    })).then(res => {\n      this.userGroupFunction = res.Entries;\n      this.share.SetUserGroupFunction(this.userGroupFunction);\n    }).catch(error => {\n      console.log(error);\n    });\n  }\n  onDelete(data) {\n    this.request.CId = data.CId;\n    this.isNew = false;\n    if (window.confirm('是否確定刪除?')) {\n      this.remove();\n    } else {\n      return;\n    }\n  }\n  onEdit(data, dialog) {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      _this.request.CId = data.CId;\n      _this.isNew = false;\n      try {\n        yield _this.getFunction();\n        _this.dialogService.open(dialog);\n      } catch (error) {\n        console.log(\"Failed to get function data\", error);\n      }\n    })();\n  }\n  add(dialog) {\n    this.isNew = true;\n    this.request.CId = null;\n    this.getFunction();\n    this.dialogService.open(dialog);\n  }\n  save(ref) {\n    this.validation();\n    if (this.valid.errorMessages.length > 0) {\n      this.message.showErrorMSGs(this.valid.errorMessages);\n      return;\n    }\n    if (this.isNew) {\n      this.userGroupService.apiUserGroupAddDataPost$Json({\n        body: {\n          CId: this.userGroupFunction.CId,\n          CName: this.userGroupFunction.CName,\n          FunctionLv1: this.userGroupFunction.FunctionLv1\n        }\n      }).subscribe(res => {\n        if (res.StatusCode === 0) {\n          this.message.showSucessMSG('執行成功');\n          setTimeout(() => {\n            this._router.navigate([\"logout\"]).then(() => {\n              ref.close();\n              LocalStorageService.ClearLocalStorage();\n            });\n          }, 1500);\n          this.getList();\n          setTimeout(() => {\n            this._eventService.push({\n              action: \"CHANGE_ROLE\" /* EEvent.CHANGE_ROLE */,\n              payload: true\n            });\n          }, 500);\n        } else {\n          this.message.showErrorMSG(res.Message);\n        }\n      });\n    } else {\n      this.userGroupService.apiUserGroupSaveDataPost$Json({\n        body: {\n          CId: this.userGroupFunction.CId,\n          CName: this.userGroupFunction.CName,\n          FunctionLv1: this.userGroupFunction.FunctionLv1\n        }\n      }).subscribe(res => {\n        if (res.StatusCode === 0) {\n          this.message.showSucessMSG('執行成功');\n          // setTimeout(() => {\n          //   this._router.navigate([\"logout\"]).then(() => {\n          //     ref.close();\n          //     LocalStorageService.ClearLocalStorage();\n          //   })\n          // }, 1500);\n          setTimeout(() => {\n            this._eventService.push({\n              action: \"CHANGE_ROLE\" /* EEvent.CHANGE_ROLE */,\n              payload: true\n            });\n          }, 500);\n        } else {\n          this.message.showErrorMSG(res.Message);\n        }\n      });\n    }\n  }\n  remove() {\n    this.userGroupService.apiUserGroupRemoveDataPost$Json({\n      body: {\n        CId: this.request.CId\n      }\n    }).subscribe(res => {\n      this.message.showSucessMSG('執行成功');\n      this.getList();\n    });\n  }\n  validation() {\n    this.valid.clear();\n    this.valid.required('[角色名稱]', this.userGroupFunction.CName);\n  }\n  static {\n    this.ɵfac = function RolePermissionsComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || RolePermissionsComponent)(i0.ɵɵdirectiveInject(i1.NbDialogService), i0.ɵɵdirectiveInject(i2.UserGroupService), i0.ɵɵdirectiveInject(i3.MessageService), i0.ɵɵdirectiveInject(i4.SharedObservable), i0.ɵɵdirectiveInject(i5.AllowHelper), i0.ɵɵdirectiveInject(i6.ValidationHelper), i0.ɵɵdirectiveInject(i7.EventService), i0.ɵɵdirectiveInject(i8.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: RolePermissionsComponent,\n      selectors: [[\"ngx-role-permissions\"]],\n      standalone: true,\n      features: [i0.ɵɵInheritDefinitionFeature, i0.ɵɵStandaloneFeature],\n      decls: 46,\n      vars: 10,\n      consts: [[\"dialog\", \"\"], [\"accent\", \"success\"], [1, \"bg-white\"], [1, \"col-12\"], [1, \"row\"], [1, \"form-group\", \"col-12\", \"col-md-4\"], [\"for\", \"keyWord\", 1, \"label\", \"mr-2\"], [\"type\", \"text\", \"nbInput\", \"\", \"id\", \"l1\", \"placeholder\", \"\\u8ACB\\u8F38\\u5165\\u89D2\\u8272\\u6B0A\\u9650\\u540D\\u7A31\", \"name\", \"Name\", 3, \"ngModelChange\", \"ngModel\"], [1, \"form-group\", \"col-12\", \"col-md-3\"], [\"for\", \"status\", 1, \"label\", \"mr-2\"], [3, \"selectedChange\", \"selected\"], [3, \"value\"], [1, \"form-group\", \"col-12\", \"col-md-5\", \"text-right\"], [1, \"btn\", \"btn-info\", \"mr-2\", 3, \"click\"], [1, \"fas\", \"fa-search\", \"mr-1\"], [\"class\", \"btn btn-success mr-2\", 3, \"click\", 4, \"ngIf\"], [1, \"table-responsive\"], [1, \"table\", \"table-striped\", \"border\", 2, \"min-width\", \"800px\", \"background-color\", \"#f3f3f3\"], [1, \"d-flex\", \"text-white\", 2, \"background-color\", \"#27ae60\"], [\"scope\", \"col\", 1, \"col-1\"], [\"scope\", \"col\", 1, \"col-5\"], [\"scope\", \"col\", 1, \"col-3\"], [\"scope\", \"col\", 1, \"col-2\"], [\"class\", \"d-flex\", 4, \"ngFor\", \"ngForOf\"], [3, \"PageChange\", \"CollectionSize\", \"Page\", \"PageSize\"], [1, \"btn\", \"btn-success\", \"mr-2\", 3, \"click\"], [1, \"fas\", \"fa-plus\", \"mr-1\"], [1, \"d-flex\"], [1, \"col-1\"], [1, \"col-5\"], [1, \"col-3\"], [1, \"col-2\"], [\"type\", \"button\", \"class\", \"btn btn-outline-success m-1  btn-sm\", 3, \"click\", 4, \"ngIf\"], [\"type\", \"button\", \"class\", \"btn btn-outline-danger m-1  btn-sm\", 3, \"click\", 4, \"ngIf\"], [\"type\", \"button\", 1, \"btn\", \"btn-outline-success\", \"m-1\", \"btn-sm\", 3, \"click\"], [1, \"fas\", \"fa-edit\", \"mr-1\"], [\"type\", \"button\", 1, \"btn\", \"btn-outline-danger\", \"m-1\", \"btn-sm\", 3, \"click\"], [1, \"far\", \"fa-trash-alt\", \"mr-1\"], [1, \"mr-md-5\", \"ml-md-5\", 2, \"height\", \"100%\", \"overflow\", \"auto\", \"max-width\", \"800px\"], [4, \"ngIf\"], [1, \"row\", \"p-2\"], [1, \"col-12\", \"col-md-12\"], [1, \"form-group\", \"row\", \"col-12\"], [1, \"col-md-4\", \"col-form-label\", \"required-field\"], [1, \"col-md-8\"], [\"class\", \"col-12 \", 4, \"ngFor\", \"ngForOf\"], [1, \"col-12\", \"text-center\"], [1, \"btn\", \"btn-danger\", \"mr-2\", 3, \"click\"], [\"class\", \"row border\", 4, \"ngIf\"], [1, \"row\", \"border\"], [1, \"col-12\", \"p-2\", \"border\"], [\"icon\", \"arrow-right-outline\", \"status\", \"basic\"], [\"class\", \"col-12\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"row pl-4 pt-3\", 4, \"ngIf\"], [1, \"row\", \"pl-4\", \"pt-3\"], [1, \"col-md-3\"], [\"class\", \"mr-2\", 4, \"ngFor\", \"ngForOf\"], [1, \"mr-2\"], [\"status\", \"basic\", 3, \"checkedChange\", \"checked\"]],\n      template: function RolePermissionsComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"nb-card\", 1)(1, \"nb-card-header\");\n          i0.ɵɵelement(2, \"ngx-breadcrumb\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(3, \"nb-card-body\", 2)(4, \"div\", 3)(5, \"div\", 4)(6, \"div\", 5)(7, \"label\", 6);\n          i0.ɵɵtext(8, \"\\u641C\\u5C0B\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(9, \"input\", 7);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function RolePermissionsComponent_Template_input_ngModelChange_9_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.request.CName, $event) || (ctx.request.CName = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(10, \"div\", 8)(11, \"label\", 9);\n          i0.ɵɵtext(12, \"\\u72C0\\u614B\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(13, \"nb-select\", 10);\n          i0.ɵɵtwoWayListener(\"selectedChange\", function RolePermissionsComponent_Template_nb_select_selectedChange_13_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.request.CStatus, $event) || (ctx.request.CStatus = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵelementStart(14, \"nb-option\", 11);\n          i0.ɵɵtext(15, \"\\u5168\\u90E8\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(16, \"nb-option\", 11);\n          i0.ɵɵtext(17, \"\\u555F\\u7528\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(18, \"nb-option\", 11);\n          i0.ɵɵtext(19, \"\\u505C\\u7528\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(20, \"div\", 12)(21, \"button\", 13);\n          i0.ɵɵlistener(\"click\", function RolePermissionsComponent_Template_button_click_21_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.getList());\n          });\n          i0.ɵɵelement(22, \"i\", 14);\n          i0.ɵɵtext(23, \"\\u67E5\\u8A62\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(24, RolePermissionsComponent_button_24_Template, 3, 0, \"button\", 15);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(25, \"nb-card-body\", 2)(26, \"div\", 3)(27, \"div\", 16)(28, \"table\", 17)(29, \"thead\")(30, \"tr\", 18)(31, \"th\", 19);\n          i0.ɵɵtext(32, \"ID\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(33, \"th\", 20);\n          i0.ɵɵtext(34, \"\\u89D2\\u8272\\u6B0A\\u9650\\u540D\\u7A31\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(35, \"th\", 19);\n          i0.ɵɵtext(36, \"\\u72C0\\u614B\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(37, \"th\", 21);\n          i0.ɵɵtext(38, \"\\u7570\\u52D5\\u6642\\u9593\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(39, \"th\", 22);\n          i0.ɵɵtext(40, \"\\u64CD\\u4F5C\\u529F\\u80FD\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(41, \"tbody\");\n          i0.ɵɵtemplate(42, RolePermissionsComponent_tr_42_Template, 14, 11, \"tr\", 23);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(43, \"ngx-pagination\", 24);\n          i0.ɵɵtwoWayListener(\"PageChange\", function RolePermissionsComponent_Template_ngx_pagination_PageChange_43_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.pageIndex, $event) || (ctx.pageIndex = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵlistener(\"PageChange\", function RolePermissionsComponent_Template_ngx_pagination_PageChange_43_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.getList());\n          });\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵtemplate(44, RolePermissionsComponent_ng_template_44_Template, 22, 4, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor);\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(9);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.request.CName);\n          i0.ɵɵadvance(4);\n          i0.ɵɵtwoWayProperty(\"selected\", ctx.request.CStatus);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"value\", -1);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"value\", 1);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"value\", 0);\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"ngIf\", ctx.isCreate);\n          i0.ɵɵadvance(18);\n          i0.ɵɵproperty(\"ngForOf\", ctx.userGroups);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"CollectionSize\", ctx.totalRecords);\n          i0.ɵɵtwoWayProperty(\"Page\", ctx.pageIndex);\n          i0.ɵɵproperty(\"PageSize\", ctx.pageSize);\n        }\n      },\n      dependencies: [NbCardModule, i1.NbCardComponent, i1.NbCardBodyComponent, i1.NbCardFooterComponent, i1.NbCardHeaderComponent, BreadcrumbComponent, NbInputModule, i1.NbInputDirective, FormsModule, i9.DefaultValueAccessor, i9.NgControlStatus, i9.NgModel, NbSelectModule, i1.NbSelectComponent, i1.NbOptionComponent, NbOptionModule, NgIf, NgFor, PaginationComponent, NbIconModule, i1.NbIconComponent, NbCheckboxModule, i1.NbCheckboxComponent, MomentPipe, StatusPipe],\n      styles: [\"/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsImZpbGUiOiJyb2xlLXBlcm1pc3Npb25zLmNvbXBvbmVudC5zY3NzIn0= */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvcGFnZXMvc3lzdGVtLW1hbmFnZW1lbnQvcm9sZS1wZXJtaXNzaW9ucy9yb2xlLXBlcm1pc3Npb25zLmNvbXBvbmVudC5zY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiI7QUFDQSxnTEFBZ0wiLCJzb3VyY2VSb290IjoiIn0= */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["BaseComponent", "NbCardModule", "NbInputModule", "NbSelectModule", "NbOptionModule", "NbIconModule", "NbCheckboxModule", "StatusPipe", "MomentPipe", "NgIf", "<PERSON><PERSON><PERSON>", "FormsModule", "ShareRequest", "BreadcrumbComponent", "PaginationComponent", "lastValueFrom", "EEvent", "LocalStorageService", "i0", "ɵɵelementStart", "ɵɵlistener", "RolePermissionsComponent_button_24_Template_button_click_0_listener", "ɵɵrestoreView", "_r2", "ctx_r2", "ɵɵnextContext", "dialog_r4", "ɵɵreference", "ɵɵresetView", "add", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd", "RolePermissionsComponent_tr_42_button_12_Template_button_click_0_listener", "_r5", "data_r6", "$implicit", "onEdit", "RolePermissionsComponent_tr_42_button_13_Template_button_click_0_listener", "_r7", "onDelete", "ɵɵtemplate", "RolePermissionsComponent_tr_42_button_12_Template", "RolePermissionsComponent_tr_42_button_13_Template", "ɵɵadvance", "ɵɵtextInterpolate", "CId", "CName", "ɵɵpipeBind1", "CStatus", "ɵɵpipeBind2", "CUpdateDt", "ɵɵproperty", "isUpdate", "isDelete", "ɵɵtwoWayListener", "RolePermissionsComponent_ng_template_44_div_14_div_1_div_4_div_1_label_4_Template_nb_checkbox_checkedChange_1_listener", "$event", "auth_r10", "_r9", "ɵɵtwoWayBindingSet", "IsChecked", "ɵɵtwoWayProperty", "ɵɵtextInterpolate1", "RolePermissionsComponent_ng_template_44_div_14_div_1_div_4_div_1_label_4_Template", "v2_r11", "Authority", "RolePermissionsComponent_ng_template_44_div_14_div_1_div_4_div_1_Template", "length", "RolePermissionsComponent_ng_template_44_div_14_div_1_div_4_Template", "v1_r12", "FunctionLv2", "RolePermissionsComponent_ng_template_44_div_14_div_1_Template", "RolePermissionsComponent_ng_template_44_span_2_Template", "RolePermissionsComponent_ng_template_44_span_3_Template", "RolePermissionsComponent_ng_template_44_Template_input_ngModelChange_12_listener", "_r8", "userGroupFunction", "RolePermissionsComponent_ng_template_44_div_14_Template", "RolePermissionsComponent_ng_template_44_Template_button_click_18_listener", "ref_r13", "dialogRef", "save", "RolePermissionsComponent_ng_template_44_Template_button_click_20_listener", "close", "isNew", "FunctionLv1", "RolePermissionsComponent", "constructor", "dialogService", "userGroupService", "message", "share", "allow", "valid", "_eventService", "_router", "userGroups", "request", "selectedItem", "SharedUserGroup", "subscribe", "res", "SharedUserGroupFunction", "getList", "ngOnInit", "PageSize", "pageSize", "PageIndex", "pageIndex", "apiUserGroupGetListPost$Json", "body", "Entries", "totalRecords", "TotalItems", "SetUserGroup", "getFunction", "apiUserGroupGetDataPost$Json", "then", "SetUserGroupFunction", "catch", "error", "console", "log", "data", "window", "confirm", "remove", "dialog", "_this", "_asyncToGenerator", "open", "ref", "validation", "errorMessages", "showErrorMSGs", "apiUserGroupAddDataPost$Json", "StatusCode", "showSucessMSG", "setTimeout", "navigate", "ClearLocalStorage", "push", "action", "payload", "showErrorMSG", "Message", "apiUserGroupSaveDataPost$Json", "apiUserGroupRemoveDataPost$Json", "clear", "required", "ɵɵdirectiveInject", "i1", "NbDialogService", "i2", "UserGroupService", "i3", "MessageService", "i4", "SharedObservable", "i5", "AllowHelper", "i6", "ValidationHelper", "i7", "EventService", "i8", "Router", "selectors", "standalone", "features", "ɵɵInheritDefinitionFeature", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "RolePermissionsComponent_Template", "rf", "ctx", "RolePermissionsComponent_Template_input_ngModelChange_9_listener", "_r1", "RolePermissionsComponent_Template_nb_select_selected<PERSON><PERSON>e_13_listener", "RolePermissionsComponent_Template_button_click_21_listener", "RolePermissionsComponent_button_24_Template", "RolePermissionsComponent_tr_42_Template", "RolePermissionsComponent_Template_ngx_pagination_PageChange_43_listener", "RolePermissionsComponent_ng_template_44_Template", "ɵɵtemplateRefExtractor", "isCreate", "NbCardComponent", "NbCardBodyComponent", "NbCardFooterComponent", "NbCardHeaderComponent", "NbInputDirective", "i9", "DefaultValueAccessor", "NgControlStatus", "NgModel", "NbSelectComponent", "NbOptionComponent", "NbIconComponent", "NbCheckboxComponent", "styles"], "sources": ["C:\\Users\\<USER>\\Documents\\jeansalechange\\adminFront\\src\\app\\pages\\system-management\\role-permissions\\role-permissions.component.ts", "C:\\Users\\<USER>\\Documents\\jeansalechange\\adminFront\\src\\app\\pages\\system-management\\role-permissions\\role-permissions.component.html"], "sourcesContent": ["import { BaseComponent } from '../../components/base/baseComponent';\r\nimport { Component, OnInit, TemplateRef } from '@angular/core';\r\nimport { NbDialogService, NbCardModule, NbInputModule, NbSelectModule, NbOptionModule, NbIconModule, NbCheckboxModule } from '@nebular/theme';\r\nimport { EnumStatusCode } from '../../../shared/enum/enumStatusCode';\r\nimport { StatusPipe } from '../../../@theme/pipes/mapping.pipe';\r\nimport { MomentPipe } from '../../../@theme/pipes/moment.pipe';\r\nimport { NgIf, NgFor } from '@angular/common';\r\nimport { FormsModule } from '@angular/forms';\r\nimport { UserGroupService } from 'src/services/api/services';\r\nimport { MessageService } from 'src/app/shared/services/message.service';\r\nimport { AllowHelper } from 'src/app/shared/helper/allowHelper';\r\nimport { ValidationHelper } from 'src/app/shared/helper/validationHelper';\r\nimport { UserGroupGetDataResponse, UserGroupGetListResponse } from 'src/services/api/models';\r\nimport { UserGroupFunction } from 'src/app/shared/model/userGroupFunction.model';\r\nimport { UserGroup } from 'src/app/shared/model/userGroup.model';\r\nimport { ShareRequest } from 'src/app/shared/model/requests/shareRequest';\r\nimport { BreadcrumbComponent } from '../../components/breadcrumb/breadcrumb.component';\r\nimport { PaginationComponent } from '../../components/pagination/pagination.component';\r\nimport { SharedObservable } from '../../components/shared.observable';\r\nimport { Observable, lastValueFrom } from 'rxjs';\r\nimport { EEvent, EventService } from 'src/app/shared/services/event.service';\r\nimport { Router } from '@angular/router';\r\nimport { LocalStorageService } from 'src/app/shared/services/local-storage.service';\r\n\r\n@Component({\r\n  selector: 'ngx-role-permissions',\r\n  templateUrl: './role-permissions.component.html',\r\n  styleUrls: ['./role-permissions.component.scss'],\r\n  standalone: true,\r\n  imports: [\r\n    NbCardModule,\r\n    BreadcrumbComponent,\r\n    NbInputModule,\r\n    FormsModule,\r\n    NbSelectModule,\r\n    NbOptionModule,\r\n    NgIf,\r\n    NgFor,\r\n    PaginationComponent,\r\n    NbIconModule,\r\n    NbCheckboxModule,\r\n    MomentPipe,\r\n    StatusPipe,\r\n  ],\r\n})\r\nexport class RolePermissionsComponent extends BaseComponent implements OnInit {\r\n\r\n  userGroups = [] as UserGroupGetListResponse[];\r\n  userGroupFunction = {} as UserGroupGetDataResponse;\r\n\r\n  request = new ShareRequest();\r\n  isNew = false;\r\n\r\n  constructor(\r\n    private dialogService: NbDialogService,\r\n    private userGroupService: UserGroupService,\r\n    private message: MessageService,\r\n    private share: SharedObservable,\r\n    protected override allow: AllowHelper,\r\n    private valid: ValidationHelper,\r\n    private _eventService: EventService,\r\n    private _router: Router\r\n  ) {\r\n    super(allow);\r\n\r\n    this.share.SharedUserGroup.subscribe(res => {\r\n      this.userGroups = res;\r\n    });\r\n    this.share.SharedUserGroupFunction.subscribe(res => {\r\n      this.userGroupFunction = res;\r\n    });\r\n\r\n    this.getList();\r\n  }\r\n\r\n  selectedItem = '';\r\n  override ngOnInit(): void {\r\n\r\n  }\r\n\r\n  getList() {\r\n    this.request.PageSize = this.pageSize;\r\n    this.request.PageIndex = this.pageIndex;\r\n\r\n    this.userGroupService.apiUserGroupGetListPost$Json({\r\n      body: {\r\n        ...this.request\r\n      }\r\n    }).subscribe(res => {\r\n      this.userGroups = res.Entries!;\r\n      this.totalRecords = res.TotalItems!;\r\n      this.share.SetUserGroup(this.userGroups);\r\n    });\r\n  }\r\n\r\n  getFunction(): Promise<void> {\r\n    return lastValueFrom(this.userGroupService.apiUserGroupGetDataPost$Json({\r\n      body: {\r\n        CId: this.request.CId\r\n      }\r\n    })).then(res => {\r\n      this.userGroupFunction = res.Entries!;\r\n      this.share.SetUserGroupFunction(this.userGroupFunction);\r\n    }).catch((error) => {\r\n      console.log(error)\r\n    })\r\n  }\r\n\r\n  onDelete(data: UserGroupGetDataResponse) {\r\n    this.request.CId = data.CId!;\r\n    this.isNew = false;\r\n    if (window.confirm('是否確定刪除?')) {\r\n      this.remove();\r\n    } else {\r\n      return;\r\n    }\r\n  }\r\n\r\n  async onEdit(data: UserGroupGetDataResponse, dialog: TemplateRef<any>) {\r\n    this.request.CId = data.CId!;\r\n    this.isNew = false;\r\n    try {\r\n      await this.getFunction();\r\n      this.dialogService.open(dialog);\r\n    } catch (error) {\r\n      console.log(\"Failed to get function data\", error)\r\n    }\r\n\r\n  }\r\n\r\n  add(dialog: TemplateRef<any>) {\r\n    this.isNew = true;\r\n    this.request.CId = null;\r\n    this.getFunction();\r\n    this.dialogService.open(dialog);\r\n  }\r\n\r\n  save(ref: any) {\r\n    this.validation();\r\n    if (this.valid.errorMessages.length > 0) {\r\n      this.message.showErrorMSGs(this.valid.errorMessages);\r\n      return;\r\n    }\r\n\r\n    if (this.isNew) {\r\n      this.userGroupService.apiUserGroupAddDataPost$Json({\r\n        body: {\r\n          CId: this.userGroupFunction.CId,\r\n          CName: this.userGroupFunction.CName,\r\n          FunctionLv1: this.userGroupFunction.FunctionLv1\r\n        }\r\n      }).subscribe(res => {\r\n        if (res.StatusCode === 0) {\r\n          this.message.showSucessMSG('執行成功');\r\n          setTimeout(() => {\r\n            this._router.navigate([\"logout\"]).then(() => {\r\n          ref.close();\r\n              LocalStorageService.ClearLocalStorage();\r\n            })\r\n          }, 1500);\r\n          this.getList();\r\n          setTimeout(() => {\r\n            this._eventService.push({\r\n              action: EEvent.CHANGE_ROLE,\r\n              payload: true\r\n            })\r\n          }, 500);\r\n        } else {\r\n          this.message.showErrorMSG(res.Message!)\r\n        }\r\n      });\r\n    } else {\r\n      this.userGroupService.apiUserGroupSaveDataPost$Json({\r\n        body: {\r\n          CId: this.userGroupFunction.CId,\r\n          CName: this.userGroupFunction.CName,\r\n          FunctionLv1: this.userGroupFunction.FunctionLv1\r\n        }\r\n      }).subscribe(res => {\r\n        if (res.StatusCode === 0) {\r\n          this.message.showSucessMSG('執行成功');\r\n          // setTimeout(() => {\r\n          //   this._router.navigate([\"logout\"]).then(() => {\r\n          //     ref.close();\r\n          //     LocalStorageService.ClearLocalStorage();\r\n          //   })\r\n          // }, 1500);\r\n          setTimeout(() => {\r\n            this._eventService.push({\r\n              action: EEvent.CHANGE_ROLE,\r\n              payload: true\r\n            })\r\n          }, 500);\r\n        } else {\r\n          this.message.showErrorMSG(res.Message!)\r\n        }\r\n      });\r\n    }\r\n  }\r\n\r\n  remove() {\r\n    this.userGroupService.apiUserGroupRemoveDataPost$Json({\r\n      body: {\r\n        CId: this.request.CId!\r\n      }\r\n    }).subscribe(res => {\r\n      this.message.showSucessMSG('執行成功');\r\n      this.getList();\r\n    });\r\n  }\r\n\r\n  validation() {\r\n    this.valid.clear();\r\n    this.valid.required('[角色名稱]', this.userGroupFunction.CName);\r\n  }\r\n\r\n}\r\n", "<nb-card accent=\"success\">\r\n  <nb-card-header>\r\n    <ngx-breadcrumb></ngx-breadcrumb>\r\n  </nb-card-header>\r\n\r\n  <nb-card-body class=\"bg-white\">\r\n    <div class=\"col-12\">\r\n      <div class=\"row\">\r\n        <div class=\"form-group col-12 col-md-4\">\r\n          <label for=\"keyWord\" class=\"label mr-2\">搜尋</label>\r\n          <input type=\"text\" nbInput id=\"l1\" placeholder=\"請輸入角色權限名稱\" name=\"Name\" [(ngModel)]=\"request.CName\">\r\n        </div>\r\n        <div class=\"form-group col-12 col-md-3\">\r\n          <label for=\"status\" class=\"label mr-2\">狀態</label>\r\n          <nb-select [(selected)]=\"request.CStatus\">\r\n            <nb-option [value]=\"-1\">全部</nb-option>\r\n            <nb-option [value]=\"1\">啟用</nb-option>\r\n            <nb-option [value]=\"0\">停用</nb-option>\r\n          </nb-select>\r\n        </div>\r\n        <div class=\"form-group col-12 col-md-5 text-right\">\r\n          <button class=\"btn btn-info mr-2\" (click)=\"getList()\"><i class=\"fas fa-search mr-1\"></i>查詢</button>\r\n          <button class=\"btn btn-success mr-2\" (click)=\"add(dialog)\" *ngIf=\"isCreate\"><i\r\n              class=\"fas fa-plus mr-1\"></i>新增角色權限</button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </nb-card-body>\r\n\r\n  <nb-card-body class=\"bg-white\">\r\n    <div class=\"col-12\">\r\n\r\n      <div class=\"table-responsive\">\r\n        <table class=\"table table-striped border \" style=\"min-width: 800px; background-color:#f3f3f3;\">\r\n          <thead>\r\n            <tr style=\"background-color: #27ae60;\" class=\"d-flex text-white\">\r\n              <th scope=\"col\" class=\"col-1\">ID</th>\r\n              <th scope=\"col\" class=\"col-5\">角色權限名稱</th>\r\n              <th scope=\"col\" class=\"col-1\">狀態</th>\r\n              <th scope=\"col\" class=\"col-3\">異動時間</th>\r\n              <th scope=\"col\" class=\"col-2\">操作功能</th>\r\n            </tr>\r\n          </thead>\r\n          <tbody>\r\n            <tr *ngFor=\"let data of userGroups\" class=\"d-flex\">\r\n              <td class=\"col-1\">{{ data.CId }}</td>\r\n              <td class=\"col-5\">{{ data.CName }}</td>\r\n              <td class=\"col-1\">{{ data.CStatus! | getStatusName }}</td>\r\n              <td class=\"col-3\">{{ data.CUpdateDt! | localDate: 'yyyy-MM-DD HH:mm:ss' }}</td>\r\n              <td class=\"col-2\">\r\n                <button *ngIf=\"isUpdate\" type=\"button\" class=\"btn btn-outline-success m-1  btn-sm\"\r\n                  (click)=\"onEdit(data,dialog)\"><i class=\"fas fa-edit mr-1\"></i>編輯</button>\r\n                <button *ngIf=\"isDelete\" type=\"button\" class=\"btn btn-outline-danger m-1  btn-sm\"\r\n                  (click)=\"onDelete(data)\"><i class=\"far fa-trash-alt mr-1\"></i>刪除</button>\r\n              </td>\r\n            </tr>\r\n          </tbody>\r\n        </table>\r\n      </div>\r\n\r\n      <ngx-pagination [CollectionSize]=\"totalRecords\" [(Page)]=\"pageIndex\" [PageSize]=\"pageSize\"\r\n        (PageChange)=\"getList()\">\r\n      </ngx-pagination>\r\n\r\n    </div>\r\n\r\n  </nb-card-body>\r\n</nb-card>\r\n\r\n\r\n\r\n<ng-template #dialog let-data let-ref=\"dialogRef\">\r\n  <nb-card style=\"height: 100%; overflow: auto; max-width: 800px; \" class=\"mr-md-5 ml-md-5\">\r\n    <nb-card-header>\r\n      <span *ngIf=\"isNew===true\">新增角色權限</span>\r\n      <span *ngIf=\"isNew===false\">編輯角色權限</span>\r\n    </nb-card-header>\r\n    <nb-card-body>\r\n\r\n      <div class=\"row  p-2\">\r\n        <div class=\"col-12 col-md-12\">\r\n\r\n          <div class=\"row \">\r\n            <div class=\"form-group row col-12\">\r\n              <label class=\"col-md-4 col-form-label required-field\">角色名稱</label>\r\n              <div class=\"col-md-8\">\r\n                <input type=\"text\" nbInput id=\"l1\" placeholder=\"請輸入角色權限名稱\" name=\"Name\"\r\n                  [(ngModel)]=\"userGroupFunction.CName\">\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <div class=\"row\">\r\n            <div class=\"col-12 \" *ngFor=\"let v1 of userGroupFunction.FunctionLv1\">\r\n              <div class=\"row border\" *ngIf=\"v1.FunctionLv2!.length>0\">\r\n                <div class=\"col-12 p-2 border\">\r\n                  <nb-icon icon=\"arrow-right-outline\" status=\"basic\"></nb-icon>{{v1.CName}}\r\n                </div>\r\n                <div class=\"col-12\" *ngFor=\"let v2 of v1.FunctionLv2\">\r\n                  <div class=\"row pl-4 pt-3\" *ngIf=\"v2.Authority!.length>0\">\r\n                    <div class=\" col-md-3\">{{v2.CName}}</div>\r\n                    <div class=\" col-md-8\">\r\n                      <label *ngFor=\"let auth of v2.Authority\" class=\"mr-2\">\r\n                        <nb-checkbox status=\"basic\" [(checked)]=\"auth.IsChecked\">{{auth.CName}}\r\n                        </nb-checkbox>\r\n                      </label>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n          </div>\r\n\r\n        </div>\r\n      </div>\r\n\r\n    </nb-card-body>\r\n    <nb-card-footer>\r\n      <div class=\"row\">\r\n        <div class=\"col-12 text-center\">\r\n          <button class=\"btn btn-success mr-2\" (click)=\"save(ref)\">確定</button>\r\n          <button class=\"btn btn-danger mr-2\" (click)=\"ref.close()\">取消</button>\r\n        </div>\r\n      </div>\r\n    </nb-card-footer>\r\n  </nb-card>\r\n</ng-template>\r\n"], "mappings": ";AAAA,SAASA,aAAa,QAAQ,qCAAqC;AAEnE,SAA0BC,YAAY,EAAEC,aAAa,EAAEC,cAAc,EAAEC,cAAc,EAAEC,YAAY,EAAEC,gBAAgB,QAAQ,gBAAgB;AAE7I,SAASC,UAAU,QAAQ,oCAAoC;AAC/D,SAASC,UAAU,QAAQ,mCAAmC;AAC9D,SAASC,IAAI,EAAEC,KAAK,QAAQ,iBAAiB;AAC7C,SAASC,WAAW,QAAQ,gBAAgB;AAQ5C,SAASC,YAAY,QAAQ,4CAA4C;AACzE,SAASC,mBAAmB,QAAQ,kDAAkD;AACtF,SAASC,mBAAmB,QAAQ,kDAAkD;AAEtF,SAAqBC,aAAa,QAAQ,MAAM;AAChD,SAASC,MAAM,QAAsB,uCAAuC;AAE5E,SAASC,mBAAmB,QAAQ,+CAA+C;;;;;;;;;;;;;;ICAzEC,EAAA,CAAAC,cAAA,iBAA4E;IAAvCD,EAAA,CAAAE,UAAA,mBAAAC,oEAAA;MAAAH,EAAA,CAAAI,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,MAAAC,SAAA,GAAAR,EAAA,CAAAS,WAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASJ,MAAA,CAAAK,GAAA,CAAAH,SAAA,CAAW;IAAA,EAAC;IAAkBR,EAAA,CAAAY,SAAA,YAC3C;IAAAZ,EAAA,CAAAa,MAAA,2CAAM;IAAAb,EAAA,CAAAc,YAAA,EAAS;;;;;;IA2B1Cd,EAAA,CAAAC,cAAA,iBACgC;IAA9BD,EAAA,CAAAE,UAAA,mBAAAa,0EAAA;MAAAf,EAAA,CAAAI,aAAA,CAAAY,GAAA;MAAA,MAAAC,OAAA,GAAAjB,EAAA,CAAAO,aAAA,GAAAW,SAAA;MAAA,MAAAZ,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,MAAAC,SAAA,GAAAR,EAAA,CAAAS,WAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASJ,MAAA,CAAAa,MAAA,CAAAF,OAAA,EAAAT,SAAA,CAAmB;IAAA,EAAC;IAACR,EAAA,CAAAY,SAAA,YAAgC;IAAAZ,EAAA,CAAAa,MAAA,mBAAE;IAAAb,EAAA,CAAAc,YAAA,EAAS;;;;;;IAC3Ed,EAAA,CAAAC,cAAA,iBAC2B;IAAzBD,EAAA,CAAAE,UAAA,mBAAAkB,0EAAA;MAAApB,EAAA,CAAAI,aAAA,CAAAiB,GAAA;MAAA,MAAAJ,OAAA,GAAAjB,EAAA,CAAAO,aAAA,GAAAW,SAAA;MAAA,MAAAZ,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAU,WAAA,CAASJ,MAAA,CAAAgB,QAAA,CAAAL,OAAA,CAAc;IAAA,EAAC;IAACjB,EAAA,CAAAY,SAAA,YAAqC;IAAAZ,EAAA,CAAAa,MAAA,mBAAE;IAAAb,EAAA,CAAAc,YAAA,EAAS;;;;;IAR7Ed,EADF,CAAAC,cAAA,aAAmD,aAC/B;IAAAD,EAAA,CAAAa,MAAA,GAAc;IAAAb,EAAA,CAAAc,YAAA,EAAK;IACrCd,EAAA,CAAAC,cAAA,aAAkB;IAAAD,EAAA,CAAAa,MAAA,GAAgB;IAAAb,EAAA,CAAAc,YAAA,EAAK;IACvCd,EAAA,CAAAC,cAAA,aAAkB;IAAAD,EAAA,CAAAa,MAAA,GAAmC;;IAAAb,EAAA,CAAAc,YAAA,EAAK;IAC1Dd,EAAA,CAAAC,cAAA,aAAkB;IAAAD,EAAA,CAAAa,MAAA,GAAwD;;IAAAb,EAAA,CAAAc,YAAA,EAAK;IAC/Ed,EAAA,CAAAC,cAAA,cAAkB;IAGhBD,EAFA,CAAAuB,UAAA,KAAAC,iDAAA,qBACgC,KAAAC,iDAAA,qBAEL;IAE/BzB,EADE,CAAAc,YAAA,EAAK,EACF;;;;;IAVed,EAAA,CAAA0B,SAAA,GAAc;IAAd1B,EAAA,CAAA2B,iBAAA,CAAAV,OAAA,CAAAW,GAAA,CAAc;IACd5B,EAAA,CAAA0B,SAAA,GAAgB;IAAhB1B,EAAA,CAAA2B,iBAAA,CAAAV,OAAA,CAAAY,KAAA,CAAgB;IAChB7B,EAAA,CAAA0B,SAAA,GAAmC;IAAnC1B,EAAA,CAAA2B,iBAAA,CAAA3B,EAAA,CAAA8B,WAAA,OAAAb,OAAA,CAAAc,OAAA,EAAmC;IACnC/B,EAAA,CAAA0B,SAAA,GAAwD;IAAxD1B,EAAA,CAAA2B,iBAAA,CAAA3B,EAAA,CAAAgC,WAAA,QAAAf,OAAA,CAAAgB,SAAA,yBAAwD;IAE/DjC,EAAA,CAAA0B,SAAA,GAAc;IAAd1B,EAAA,CAAAkC,UAAA,SAAA5B,MAAA,CAAA6B,QAAA,CAAc;IAEdnC,EAAA,CAAA0B,SAAA,EAAc;IAAd1B,EAAA,CAAAkC,UAAA,SAAA5B,MAAA,CAAA8B,QAAA,CAAc;;;;;IAsBjCpC,EAAA,CAAAC,cAAA,WAA2B;IAAAD,EAAA,CAAAa,MAAA,2CAAM;IAAAb,EAAA,CAAAc,YAAA,EAAO;;;;;IACxCd,EAAA,CAAAC,cAAA,WAA4B;IAAAD,EAAA,CAAAa,MAAA,2CAAM;IAAAb,EAAA,CAAAc,YAAA,EAAO;;;;;;IA4BvBd,EADF,CAAAC,cAAA,gBAAsD,sBACK;IAA7BD,EAAA,CAAAqC,gBAAA,2BAAAC,uHAAAC,MAAA;MAAA,MAAAC,QAAA,GAAAxC,EAAA,CAAAI,aAAA,CAAAqC,GAAA,EAAAvB,SAAA;MAAAlB,EAAA,CAAA0C,kBAAA,CAAAF,QAAA,CAAAG,SAAA,EAAAJ,MAAA,MAAAC,QAAA,CAAAG,SAAA,GAAAJ,MAAA;MAAA,OAAAvC,EAAA,CAAAU,WAAA,CAAA6B,MAAA;IAAA,EAA4B;IAACvC,EAAA,CAAAa,MAAA,GACzD;IACFb,EADE,CAAAc,YAAA,EAAc,EACR;;;;IAFsBd,EAAA,CAAA0B,SAAA,EAA4B;IAA5B1B,EAAA,CAAA4C,gBAAA,YAAAJ,QAAA,CAAAG,SAAA,CAA4B;IAAC3C,EAAA,CAAA0B,SAAA,EACzD;IADyD1B,EAAA,CAAA6C,kBAAA,KAAAL,QAAA,CAAAX,KAAA,MACzD;;;;;IAJJ7B,EADF,CAAAC,cAAA,cAA0D,cACjC;IAAAD,EAAA,CAAAa,MAAA,GAAY;IAAAb,EAAA,CAAAc,YAAA,EAAM;IACzCd,EAAA,CAAAC,cAAA,cAAuB;IACrBD,EAAA,CAAAuB,UAAA,IAAAuB,iFAAA,oBAAsD;IAK1D9C,EADE,CAAAc,YAAA,EAAM,EACF;;;;IAPmBd,EAAA,CAAA0B,SAAA,GAAY;IAAZ1B,EAAA,CAAA2B,iBAAA,CAAAoB,MAAA,CAAAlB,KAAA,CAAY;IAET7B,EAAA,CAAA0B,SAAA,GAAe;IAAf1B,EAAA,CAAAkC,UAAA,YAAAa,MAAA,CAAAC,SAAA,CAAe;;;;;IAJ7ChD,EAAA,CAAAC,cAAA,aAAsD;IACpDD,EAAA,CAAAuB,UAAA,IAAA0B,yEAAA,kBAA0D;IAS5DjD,EAAA,CAAAc,YAAA,EAAM;;;;IATwBd,EAAA,CAAA0B,SAAA,EAA4B;IAA5B1B,EAAA,CAAAkC,UAAA,SAAAa,MAAA,CAAAC,SAAA,CAAAE,MAAA,KAA4B;;;;;IAJ1DlD,EADF,CAAAC,cAAA,cAAyD,cACxB;IAC7BD,EAAA,CAAAY,SAAA,kBAA6D;IAAAZ,EAAA,CAAAa,MAAA,GAC/D;IAAAb,EAAA,CAAAc,YAAA,EAAM;IACNd,EAAA,CAAAuB,UAAA,IAAA4B,mEAAA,kBAAsD;IAWxDnD,EAAA,CAAAc,YAAA,EAAM;;;;IAb2Dd,EAAA,CAAA0B,SAAA,GAC/D;IAD+D1B,EAAA,CAAA6C,kBAAA,KAAAO,MAAA,CAAAvB,KAAA,MAC/D;IACmC7B,EAAA,CAAA0B,SAAA,EAAiB;IAAjB1B,EAAA,CAAAkC,UAAA,YAAAkB,MAAA,CAAAC,WAAA,CAAiB;;;;;IALxDrD,EAAA,CAAAC,cAAA,aAAsE;IACpED,EAAA,CAAAuB,UAAA,IAAA+B,6DAAA,kBAAyD;IAgB3DtD,EAAA,CAAAc,YAAA,EAAM;;;;IAhBqBd,EAAA,CAAA0B,SAAA,EAA8B;IAA9B1B,EAAA,CAAAkC,UAAA,SAAAkB,MAAA,CAAAC,WAAA,CAAAH,MAAA,KAA8B;;;;;;IArBjElD,EADF,CAAAC,cAAA,kBAA0F,qBACxE;IAEdD,EADA,CAAAuB,UAAA,IAAAgC,uDAAA,mBAA2B,IAAAC,uDAAA,mBACC;IAC9BxD,EAAA,CAAAc,YAAA,EAAiB;IAQPd,EAPV,CAAAC,cAAA,mBAAc,cAEU,cACU,aAEV,cACmB,gBACqB;IAAAD,EAAA,CAAAa,MAAA,gCAAI;IAAAb,EAAA,CAAAc,YAAA,EAAQ;IAEhEd,EADF,CAAAC,cAAA,eAAsB,gBAEoB;IAAtCD,EAAA,CAAAqC,gBAAA,2BAAAoB,iFAAAlB,MAAA;MAAAvC,EAAA,CAAAI,aAAA,CAAAsD,GAAA;MAAA,MAAApD,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAAP,EAAA,CAAA0C,kBAAA,CAAApC,MAAA,CAAAqD,iBAAA,CAAA9B,KAAA,EAAAU,MAAA,MAAAjC,MAAA,CAAAqD,iBAAA,CAAA9B,KAAA,GAAAU,MAAA;MAAA,OAAAvC,EAAA,CAAAU,WAAA,CAAA6B,MAAA;IAAA,EAAqC;IAG7CvC,EAJM,CAAAc,YAAA,EACwC,EACpC,EACF,EACF;IAENd,EAAA,CAAAC,cAAA,cAAiB;IACfD,EAAA,CAAAuB,UAAA,KAAAqC,uDAAA,kBAAsE;IAwB9E5D,EALM,CAAAc,YAAA,EAAM,EAEF,EACF,EAEO;IAITd,EAHN,CAAAC,cAAA,sBAAgB,cACG,eACiB,kBAC2B;IAApBD,EAAA,CAAAE,UAAA,mBAAA2D,0EAAA;MAAA,MAAAC,OAAA,GAAA9D,EAAA,CAAAI,aAAA,CAAAsD,GAAA,EAAAK,SAAA;MAAA,MAAAzD,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAU,WAAA,CAASJ,MAAA,CAAA0D,IAAA,CAAAF,OAAA,CAAS;IAAA,EAAC;IAAC9D,EAAA,CAAAa,MAAA,oBAAE;IAAAb,EAAA,CAAAc,YAAA,EAAS;IACpEd,EAAA,CAAAC,cAAA,kBAA0D;IAAtBD,EAAA,CAAAE,UAAA,mBAAA+D,0EAAA;MAAA,MAAAH,OAAA,GAAA9D,EAAA,CAAAI,aAAA,CAAAsD,GAAA,EAAAK,SAAA;MAAA,OAAA/D,EAAA,CAAAU,WAAA,CAASoD,OAAA,CAAAI,KAAA,EAAW;IAAA,EAAC;IAAClE,EAAA,CAAAa,MAAA,oBAAE;IAIpEb,EAJoE,CAAAc,YAAA,EAAS,EACjE,EACF,EACS,EACT;;;;IApDCd,EAAA,CAAA0B,SAAA,GAAkB;IAAlB1B,EAAA,CAAAkC,UAAA,SAAA5B,MAAA,CAAA6D,KAAA,UAAkB;IAClBnE,EAAA,CAAA0B,SAAA,EAAmB;IAAnB1B,EAAA,CAAAkC,UAAA,SAAA5B,MAAA,CAAA6D,KAAA,WAAmB;IAYdnE,EAAA,CAAA0B,SAAA,GAAqC;IAArC1B,EAAA,CAAA4C,gBAAA,YAAAtC,MAAA,CAAAqD,iBAAA,CAAA9B,KAAA,CAAqC;IAMP7B,EAAA,CAAA0B,SAAA,GAAgC;IAAhC1B,EAAA,CAAAkC,UAAA,YAAA5B,MAAA,CAAAqD,iBAAA,CAAAS,WAAA,CAAgC;;;ADhDhF,OAAM,MAAOC,wBAAyB,SAAQvF,aAAa;EAQzDwF,YACUC,aAA8B,EAC9BC,gBAAkC,EAClCC,OAAuB,EACvBC,KAAuB,EACZC,KAAkB,EAC7BC,KAAuB,EACvBC,aAA2B,EAC3BC,OAAe;IAEvB,KAAK,CAACH,KAAK,CAAC;IATJ,KAAAJ,aAAa,GAAbA,aAAa;IACb,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAC,KAAK,GAALA,KAAK;IACM,KAAAC,KAAK,GAALA,KAAK;IAChB,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,OAAO,GAAPA,OAAO;IAdjB,KAAAC,UAAU,GAAG,EAAgC;IAC7C,KAAApB,iBAAiB,GAAG,EAA8B;IAElD,KAAAqB,OAAO,GAAG,IAAItF,YAAY,EAAE;IAC5B,KAAAyE,KAAK,GAAG,KAAK;IAwBb,KAAAc,YAAY,GAAG,EAAE;IAVf,IAAI,CAACP,KAAK,CAACQ,eAAe,CAACC,SAAS,CAACC,GAAG,IAAG;MACzC,IAAI,CAACL,UAAU,GAAGK,GAAG;IACvB,CAAC,CAAC;IACF,IAAI,CAACV,KAAK,CAACW,uBAAuB,CAACF,SAAS,CAACC,GAAG,IAAG;MACjD,IAAI,CAACzB,iBAAiB,GAAGyB,GAAG;IAC9B,CAAC,CAAC;IAEF,IAAI,CAACE,OAAO,EAAE;EAChB;EAGSC,QAAQA,CAAA,GAEjB;EAEAD,OAAOA,CAAA;IACL,IAAI,CAACN,OAAO,CAACQ,QAAQ,GAAG,IAAI,CAACC,QAAQ;IACrC,IAAI,CAACT,OAAO,CAACU,SAAS,GAAG,IAAI,CAACC,SAAS;IAEvC,IAAI,CAACnB,gBAAgB,CAACoB,4BAA4B,CAAC;MACjDC,IAAI,EAAE;QACJ,GAAG,IAAI,CAACb;;KAEX,CAAC,CAACG,SAAS,CAACC,GAAG,IAAG;MACjB,IAAI,CAACL,UAAU,GAAGK,GAAG,CAACU,OAAQ;MAC9B,IAAI,CAACC,YAAY,GAAGX,GAAG,CAACY,UAAW;MACnC,IAAI,CAACtB,KAAK,CAACuB,YAAY,CAAC,IAAI,CAAClB,UAAU,CAAC;IAC1C,CAAC,CAAC;EACJ;EAEAmB,WAAWA,CAAA;IACT,OAAOrG,aAAa,CAAC,IAAI,CAAC2E,gBAAgB,CAAC2B,4BAA4B,CAAC;MACtEN,IAAI,EAAE;QACJjE,GAAG,EAAE,IAAI,CAACoD,OAAO,CAACpD;;KAErB,CAAC,CAAC,CAACwE,IAAI,CAAChB,GAAG,IAAG;MACb,IAAI,CAACzB,iBAAiB,GAAGyB,GAAG,CAACU,OAAQ;MACrC,IAAI,CAACpB,KAAK,CAAC2B,oBAAoB,CAAC,IAAI,CAAC1C,iBAAiB,CAAC;IACzD,CAAC,CAAC,CAAC2C,KAAK,CAAEC,KAAK,IAAI;MACjBC,OAAO,CAACC,GAAG,CAACF,KAAK,CAAC;IACpB,CAAC,CAAC;EACJ;EAEAjF,QAAQA,CAACoF,IAA8B;IACrC,IAAI,CAAC1B,OAAO,CAACpD,GAAG,GAAG8E,IAAI,CAAC9E,GAAI;IAC5B,IAAI,CAACuC,KAAK,GAAG,KAAK;IAClB,IAAIwC,MAAM,CAACC,OAAO,CAAC,SAAS,CAAC,EAAE;MAC7B,IAAI,CAACC,MAAM,EAAE;IACf,CAAC,MAAM;MACL;IACF;EACF;EAEM1F,MAAMA,CAACuF,IAA8B,EAAEI,MAAwB;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA;MACnED,KAAI,CAAC/B,OAAO,CAACpD,GAAG,GAAG8E,IAAI,CAAC9E,GAAI;MAC5BmF,KAAI,CAAC5C,KAAK,GAAG,KAAK;MAClB,IAAI;QACF,MAAM4C,KAAI,CAACb,WAAW,EAAE;QACxBa,KAAI,CAACxC,aAAa,CAAC0C,IAAI,CAACH,MAAM,CAAC;MACjC,CAAC,CAAC,OAAOP,KAAK,EAAE;QACdC,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAEF,KAAK,CAAC;MACnD;IAAC;EAEH;EAEA5F,GAAGA,CAACmG,MAAwB;IAC1B,IAAI,CAAC3C,KAAK,GAAG,IAAI;IACjB,IAAI,CAACa,OAAO,CAACpD,GAAG,GAAG,IAAI;IACvB,IAAI,CAACsE,WAAW,EAAE;IAClB,IAAI,CAAC3B,aAAa,CAAC0C,IAAI,CAACH,MAAM,CAAC;EACjC;EAEA9C,IAAIA,CAACkD,GAAQ;IACX,IAAI,CAACC,UAAU,EAAE;IACjB,IAAI,IAAI,CAACvC,KAAK,CAACwC,aAAa,CAAClE,MAAM,GAAG,CAAC,EAAE;MACvC,IAAI,CAACuB,OAAO,CAAC4C,aAAa,CAAC,IAAI,CAACzC,KAAK,CAACwC,aAAa,CAAC;MACpD;IACF;IAEA,IAAI,IAAI,CAACjD,KAAK,EAAE;MACd,IAAI,CAACK,gBAAgB,CAAC8C,4BAA4B,CAAC;QACjDzB,IAAI,EAAE;UACJjE,GAAG,EAAE,IAAI,CAAC+B,iBAAiB,CAAC/B,GAAG;UAC/BC,KAAK,EAAE,IAAI,CAAC8B,iBAAiB,CAAC9B,KAAK;UACnCuC,WAAW,EAAE,IAAI,CAACT,iBAAiB,CAACS;;OAEvC,CAAC,CAACe,SAAS,CAACC,GAAG,IAAG;QACjB,IAAIA,GAAG,CAACmC,UAAU,KAAK,CAAC,EAAE;UACxB,IAAI,CAAC9C,OAAO,CAAC+C,aAAa,CAAC,MAAM,CAAC;UAClCC,UAAU,CAAC,MAAK;YACd,IAAI,CAAC3C,OAAO,CAAC4C,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC,CAACtB,IAAI,CAAC,MAAK;cAC9Cc,GAAG,CAAChD,KAAK,EAAE;cACPnE,mBAAmB,CAAC4H,iBAAiB,EAAE;YACzC,CAAC,CAAC;UACJ,CAAC,EAAE,IAAI,CAAC;UACR,IAAI,CAACrC,OAAO,EAAE;UACdmC,UAAU,CAAC,MAAK;YACd,IAAI,CAAC5C,aAAa,CAAC+C,IAAI,CAAC;cACtBC,MAAM;cACNC,OAAO,EAAE;aACV,CAAC;UACJ,CAAC,EAAE,GAAG,CAAC;QACT,CAAC,MAAM;UACL,IAAI,CAACrD,OAAO,CAACsD,YAAY,CAAC3C,GAAG,CAAC4C,OAAQ,CAAC;QACzC;MACF,CAAC,CAAC;IACJ,CAAC,MAAM;MACL,IAAI,CAACxD,gBAAgB,CAACyD,6BAA6B,CAAC;QAClDpC,IAAI,EAAE;UACJjE,GAAG,EAAE,IAAI,CAAC+B,iBAAiB,CAAC/B,GAAG;UAC/BC,KAAK,EAAE,IAAI,CAAC8B,iBAAiB,CAAC9B,KAAK;UACnCuC,WAAW,EAAE,IAAI,CAACT,iBAAiB,CAACS;;OAEvC,CAAC,CAACe,SAAS,CAACC,GAAG,IAAG;QACjB,IAAIA,GAAG,CAACmC,UAAU,KAAK,CAAC,EAAE;UACxB,IAAI,CAAC9C,OAAO,CAAC+C,aAAa,CAAC,MAAM,CAAC;UAClC;UACA;UACA;UACA;UACA;UACA;UACAC,UAAU,CAAC,MAAK;YACd,IAAI,CAAC5C,aAAa,CAAC+C,IAAI,CAAC;cACtBC,MAAM;cACNC,OAAO,EAAE;aACV,CAAC;UACJ,CAAC,EAAE,GAAG,CAAC;QACT,CAAC,MAAM;UACL,IAAI,CAACrD,OAAO,CAACsD,YAAY,CAAC3C,GAAG,CAAC4C,OAAQ,CAAC;QACzC;MACF,CAAC,CAAC;IACJ;EACF;EAEAnB,MAAMA,CAAA;IACJ,IAAI,CAACrC,gBAAgB,CAAC0D,+BAA+B,CAAC;MACpDrC,IAAI,EAAE;QACJjE,GAAG,EAAE,IAAI,CAACoD,OAAO,CAACpD;;KAErB,CAAC,CAACuD,SAAS,CAACC,GAAG,IAAG;MACjB,IAAI,CAACX,OAAO,CAAC+C,aAAa,CAAC,MAAM,CAAC;MAClC,IAAI,CAAClC,OAAO,EAAE;IAChB,CAAC,CAAC;EACJ;EAEA6B,UAAUA,CAAA;IACR,IAAI,CAACvC,KAAK,CAACuD,KAAK,EAAE;IAClB,IAAI,CAACvD,KAAK,CAACwD,QAAQ,CAAC,QAAQ,EAAE,IAAI,CAACzE,iBAAiB,CAAC9B,KAAK,CAAC;EAC7D;;;uCAzKWwC,wBAAwB,EAAArE,EAAA,CAAAqI,iBAAA,CAAAC,EAAA,CAAAC,eAAA,GAAAvI,EAAA,CAAAqI,iBAAA,CAAAG,EAAA,CAAAC,gBAAA,GAAAzI,EAAA,CAAAqI,iBAAA,CAAAK,EAAA,CAAAC,cAAA,GAAA3I,EAAA,CAAAqI,iBAAA,CAAAO,EAAA,CAAAC,gBAAA,GAAA7I,EAAA,CAAAqI,iBAAA,CAAAS,EAAA,CAAAC,WAAA,GAAA/I,EAAA,CAAAqI,iBAAA,CAAAW,EAAA,CAAAC,gBAAA,GAAAjJ,EAAA,CAAAqI,iBAAA,CAAAa,EAAA,CAAAC,YAAA,GAAAnJ,EAAA,CAAAqI,iBAAA,CAAAe,EAAA,CAAAC,MAAA;IAAA;EAAA;;;YAAxBhF,wBAAwB;MAAAiF,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAAxJ,EAAA,CAAAyJ,0BAAA,EAAAzJ,EAAA,CAAA0J,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,kCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;UC5CnChK,EADF,CAAAC,cAAA,iBAA0B,qBACR;UACdD,EAAA,CAAAY,SAAA,qBAAiC;UACnCZ,EAAA,CAAAc,YAAA,EAAiB;UAMTd,EAJR,CAAAC,cAAA,sBAA+B,aACT,aACD,aACyB,eACE;UAAAD,EAAA,CAAAa,MAAA,mBAAE;UAAAb,EAAA,CAAAc,YAAA,EAAQ;UAClDd,EAAA,CAAAC,cAAA,eAAmG;UAA5BD,EAAA,CAAAqC,gBAAA,2BAAA6H,iEAAA3H,MAAA;YAAAvC,EAAA,CAAAI,aAAA,CAAA+J,GAAA;YAAAnK,EAAA,CAAA0C,kBAAA,CAAAuH,GAAA,CAAAjF,OAAA,CAAAnD,KAAA,EAAAU,MAAA,MAAA0H,GAAA,CAAAjF,OAAA,CAAAnD,KAAA,GAAAU,MAAA;YAAA,OAAAvC,EAAA,CAAAU,WAAA,CAAA6B,MAAA;UAAA,EAA2B;UACpGvC,EADE,CAAAc,YAAA,EAAmG,EAC/F;UAEJd,EADF,CAAAC,cAAA,cAAwC,gBACC;UAAAD,EAAA,CAAAa,MAAA,oBAAE;UAAAb,EAAA,CAAAc,YAAA,EAAQ;UACjDd,EAAA,CAAAC,cAAA,qBAA0C;UAA/BD,EAAA,CAAAqC,gBAAA,4BAAA+H,uEAAA7H,MAAA;YAAAvC,EAAA,CAAAI,aAAA,CAAA+J,GAAA;YAAAnK,EAAA,CAAA0C,kBAAA,CAAAuH,GAAA,CAAAjF,OAAA,CAAAjD,OAAA,EAAAQ,MAAA,MAAA0H,GAAA,CAAAjF,OAAA,CAAAjD,OAAA,GAAAQ,MAAA;YAAA,OAAAvC,EAAA,CAAAU,WAAA,CAAA6B,MAAA;UAAA,EAA8B;UACvCvC,EAAA,CAAAC,cAAA,qBAAwB;UAAAD,EAAA,CAAAa,MAAA,oBAAE;UAAAb,EAAA,CAAAc,YAAA,EAAY;UACtCd,EAAA,CAAAC,cAAA,qBAAuB;UAAAD,EAAA,CAAAa,MAAA,oBAAE;UAAAb,EAAA,CAAAc,YAAA,EAAY;UACrCd,EAAA,CAAAC,cAAA,qBAAuB;UAAAD,EAAA,CAAAa,MAAA,oBAAE;UAE7Bb,EAF6B,CAAAc,YAAA,EAAY,EAC3B,EACR;UAEJd,EADF,CAAAC,cAAA,eAAmD,kBACK;UAApBD,EAAA,CAAAE,UAAA,mBAAAmK,2DAAA;YAAArK,EAAA,CAAAI,aAAA,CAAA+J,GAAA;YAAA,OAAAnK,EAAA,CAAAU,WAAA,CAASuJ,GAAA,CAAA3E,OAAA,EAAS;UAAA,EAAC;UAACtF,EAAA,CAAAY,SAAA,aAAkC;UAAAZ,EAAA,CAAAa,MAAA,oBAAE;UAAAb,EAAA,CAAAc,YAAA,EAAS;UACnGd,EAAA,CAAAuB,UAAA,KAAA+I,2CAAA,qBAA4E;UAKpFtK,EAHM,CAAAc,YAAA,EAAM,EACF,EACF,EACO;UASHd,EAPZ,CAAAC,cAAA,uBAA+B,cACT,eAEY,iBACmE,aACtF,cAC4D,cACjC;UAAAD,EAAA,CAAAa,MAAA,UAAE;UAAAb,EAAA,CAAAc,YAAA,EAAK;UACrCd,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAa,MAAA,4CAAM;UAAAb,EAAA,CAAAc,YAAA,EAAK;UACzCd,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAa,MAAA,oBAAE;UAAAb,EAAA,CAAAc,YAAA,EAAK;UACrCd,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAa,MAAA,gCAAI;UAAAb,EAAA,CAAAc,YAAA,EAAK;UACvCd,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAa,MAAA,gCAAI;UAEtCb,EAFsC,CAAAc,YAAA,EAAK,EACpC,EACC;UACRd,EAAA,CAAAC,cAAA,aAAO;UACLD,EAAA,CAAAuB,UAAA,KAAAgJ,uCAAA,mBAAmD;UAczDvK,EAFI,CAAAc,YAAA,EAAQ,EACF,EACJ;UAENd,EAAA,CAAAC,cAAA,0BAC2B;UADqBD,EAAA,CAAAqC,gBAAA,wBAAAmI,wEAAAjI,MAAA;YAAAvC,EAAA,CAAAI,aAAA,CAAA+J,GAAA;YAAAnK,EAAA,CAAA0C,kBAAA,CAAAuH,GAAA,CAAAtE,SAAA,EAAApD,MAAA,MAAA0H,GAAA,CAAAtE,SAAA,GAAApD,MAAA;YAAA,OAAAvC,EAAA,CAAAU,WAAA,CAAA6B,MAAA;UAAA,EAAoB;UAClEvC,EAAA,CAAAE,UAAA,wBAAAsK,wEAAA;YAAAxK,EAAA,CAAAI,aAAA,CAAA+J,GAAA;YAAA,OAAAnK,EAAA,CAAAU,WAAA,CAAcuJ,GAAA,CAAA3E,OAAA,EAAS;UAAA,EAAC;UAMhCtF,EALM,CAAAc,YAAA,EAAiB,EAEb,EAEO,EACP;UAIVd,EAAA,CAAAuB,UAAA,KAAAkJ,gDAAA,iCAAAzK,EAAA,CAAA0K,sBAAA,CAAkD;;;UA7D+B1K,EAAA,CAAA0B,SAAA,GAA2B;UAA3B1B,EAAA,CAAA4C,gBAAA,YAAAqH,GAAA,CAAAjF,OAAA,CAAAnD,KAAA,CAA2B;UAIvF7B,EAAA,CAAA0B,SAAA,GAA8B;UAA9B1B,EAAA,CAAA4C,gBAAA,aAAAqH,GAAA,CAAAjF,OAAA,CAAAjD,OAAA,CAA8B;UAC5B/B,EAAA,CAAA0B,SAAA,EAAY;UAAZ1B,EAAA,CAAAkC,UAAA,aAAY;UACZlC,EAAA,CAAA0B,SAAA,GAAW;UAAX1B,EAAA,CAAAkC,UAAA,YAAW;UACXlC,EAAA,CAAA0B,SAAA,GAAW;UAAX1B,EAAA,CAAAkC,UAAA,YAAW;UAKoClC,EAAA,CAAA0B,SAAA,GAAc;UAAd1B,EAAA,CAAAkC,UAAA,SAAA+H,GAAA,CAAAU,QAAA,CAAc;UAsBnD3K,EAAA,CAAA0B,SAAA,IAAa;UAAb1B,EAAA,CAAAkC,UAAA,YAAA+H,GAAA,CAAAlF,UAAA,CAAa;UAgBxB/E,EAAA,CAAA0B,SAAA,EAA+B;UAA/B1B,EAAA,CAAAkC,UAAA,mBAAA+H,GAAA,CAAAlE,YAAA,CAA+B;UAAC/F,EAAA,CAAA4C,gBAAA,SAAAqH,GAAA,CAAAtE,SAAA,CAAoB;UAAC3F,EAAA,CAAAkC,UAAA,aAAA+H,GAAA,CAAAxE,QAAA,CAAqB;;;qBD9B5F1G,YAAY,EAAAuJ,EAAA,CAAAsC,eAAA,EAAAtC,EAAA,CAAAuC,mBAAA,EAAAvC,EAAA,CAAAwC,qBAAA,EAAAxC,EAAA,CAAAyC,qBAAA,EACZpL,mBAAmB,EACnBX,aAAa,EAAAsJ,EAAA,CAAA0C,gBAAA,EACbvL,WAAW,EAAAwL,EAAA,CAAAC,oBAAA,EAAAD,EAAA,CAAAE,eAAA,EAAAF,EAAA,CAAAG,OAAA,EACXnM,cAAc,EAAAqJ,EAAA,CAAA+C,iBAAA,EAAA/C,EAAA,CAAAgD,iBAAA,EACdpM,cAAc,EACdK,IAAI,EACJC,KAAK,EACLI,mBAAmB,EACnBT,YAAY,EAAAmJ,EAAA,CAAAiD,eAAA,EACZnM,gBAAgB,EAAAkJ,EAAA,CAAAkD,mBAAA,EAChBlM,UAAU,EACVD,UAAU;MAAAoM,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}