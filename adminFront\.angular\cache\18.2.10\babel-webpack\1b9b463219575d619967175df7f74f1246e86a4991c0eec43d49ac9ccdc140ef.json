{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { Component, Input, Output, EventEmitter, HostListener } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nlet ImageModalComponent = class ImageModalComponent {\n  constructor(cdr, elementRef) {\n    this.cdr = cdr;\n    this.elementRef = elementRef;\n    this.images = [];\n    this.currentIndex = 0;\n    this.isVisible = false;\n    this.close = new EventEmitter();\n    this.indexChange = new EventEmitter();\n  }\n  ngOnInit() {\n    if (this.currentIndex >= this.images.length) {\n      this.currentIndex = 0;\n    }\n    // 監聽 isVisible 變化\n    this.handleModalVisibility();\n  }\n  ngOnDestroy() {\n    // 確保清理\n    if (this.isVisible) {\n      document.body.classList.remove('modal-open');\n    }\n  }\n  ngOnChanges() {\n    this.handleModalVisibility();\n  }\n  handleModalVisibility() {\n    if (this.isVisible) {\n      document.body.classList.add('modal-open');\n    } else {\n      document.body.classList.remove('modal-open');\n    }\n  }\n  handleKeyDown(event) {\n    if (!this.isVisible) return;\n    switch (event.key) {\n      case 'Escape':\n        this.closeModal();\n        break;\n      case 'ArrowLeft':\n        this.prevImage();\n        break;\n      case 'ArrowRight':\n        this.nextImage();\n        break;\n    }\n  }\n  getCurrentImage() {\n    if (this.images && this.images.length > 0 && this.currentIndex < this.images.length) {\n      return this.images[this.currentIndex];\n    }\n    return null;\n  }\n  nextImage() {\n    if (this.images && this.images.length > 1) {\n      this.currentIndex = (this.currentIndex + 1) % this.images.length;\n      this.indexChange.emit(this.currentIndex);\n    }\n  }\n  prevImage() {\n    if (this.images && this.images.length > 1) {\n      this.currentIndex = this.currentIndex === 0 ? this.images.length - 1 : this.currentIndex - 1;\n      this.indexChange.emit(this.currentIndex);\n    }\n  }\n  setCurrentImage(index) {\n    if (index >= 0 && index < this.images.length) {\n      this.currentIndex = index;\n      this.indexChange.emit(this.currentIndex);\n    }\n  }\n  closeModal() {\n    document.body.classList.remove('modal-open');\n    this.close.emit();\n  }\n  onBackdropClick(event) {\n    if (event.target === event.currentTarget) {\n      this.closeModal();\n    }\n  }\n};\n__decorate([Input()], ImageModalComponent.prototype, \"images\", void 0);\n__decorate([Input()], ImageModalComponent.prototype, \"currentIndex\", void 0);\n__decorate([Input()], ImageModalComponent.prototype, \"isVisible\", void 0);\n__decorate([Output()], ImageModalComponent.prototype, \"close\", void 0);\n__decorate([Output()], ImageModalComponent.prototype, \"indexChange\", void 0);\n__decorate([HostListener('document:keydown', ['$event'])], ImageModalComponent.prototype, \"handleKeyDown\", null);\nImageModalComponent = __decorate([Component({\n  selector: 'app-image-modal',\n  templateUrl: './image-modal.component.html',\n  styleUrls: ['./image-modal.component.scss'],\n  standalone: true,\n  imports: [CommonModule],\n  host: {\n    '[class.modal-visible]': 'isVisible'\n  }\n})], ImageModalComponent);\nexport { ImageModalComponent };", "map": {"version": 3, "names": ["Component", "Input", "Output", "EventEmitter", "HostListener", "CommonModule", "ImageModalComponent", "constructor", "cdr", "elementRef", "images", "currentIndex", "isVisible", "close", "indexChange", "ngOnInit", "length", "handleModalVisibility", "ngOnDestroy", "document", "body", "classList", "remove", "ngOnChanges", "add", "handleKeyDown", "event", "key", "closeModal", "prevImage", "nextImage", "getCurrentImage", "emit", "setCurrentImage", "index", "onBackdropClick", "target", "currentTarget", "__decorate", "selector", "templateUrl", "styleUrls", "standalone", "imports", "host"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\components\\image-modal\\image-modal.component.ts"], "sourcesContent": ["import { Component, Input, Output, EventEmitter, OnInit, On<PERSON><PERSON>roy, HostListener } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { ImageData } from '../image-carousel/image-carousel.component';\r\n\r\n@Component({\r\n    selector: 'app-image-modal',\r\n    templateUrl: './image-modal.component.html',\r\n    styleUrls: ['./image-modal.component.scss'],\r\n    standalone: true,\r\n    imports: [CommonModule],\r\n    host: {\r\n        '[class.modal-visible]': 'isVisible'\r\n    }\r\n})\r\nexport class ImageModalComponent implements OnInit, OnDestroy {\r\n    @Input() images: ImageData[] = [];\r\n    @Input() currentIndex: number = 0;\r\n    @Input() isVisible: boolean = false;\r\n\r\n    @Output() close = new EventEmitter<void>();\r\n    @Output() indexChange = new EventEmitter<number>();\r\n\r\n    constructor(\r\n        private cdr: ChangeDetectorRef,\r\n        private elementRef: ElementRef\r\n    ) { }\r\n\r\n    ngOnInit() {\r\n        if (this.currentIndex >= this.images.length) {\r\n            this.currentIndex = 0;\r\n        }\r\n\r\n        // 監聽 isVisible 變化\r\n        this.handleModalVisibility();\r\n    }\r\n\r\n    ngOnDestroy() {\r\n        // 確保清理\r\n        if (this.isVisible) {\r\n            document.body.classList.remove('modal-open');\r\n        }\r\n    }\r\n\r\n    ngOnChanges() {\r\n        this.handleModalVisibility();\r\n    }    private handleModalVisibility() {\r\n        if (this.isVisible) {\r\n            document.body.classList.add('modal-open');\r\n        } else {\r\n            document.body.classList.remove('modal-open');\r\n        }\r\n    }\r\n\r\n    @HostListener('document:keydown', ['$event'])\r\n    handleKeyDown(event: KeyboardEvent) {\r\n        if (!this.isVisible) return;\r\n\r\n        switch (event.key) {\r\n            case 'Escape':\r\n                this.closeModal();\r\n                break;\r\n            case 'ArrowLeft':\r\n                this.prevImage();\r\n                break;\r\n            case 'ArrowRight':\r\n                this.nextImage();\r\n                break;\r\n        }\r\n    }\r\n\r\n    getCurrentImage(): ImageData | null {\r\n        if (this.images && this.images.length > 0 && this.currentIndex < this.images.length) {\r\n            return this.images[this.currentIndex];\r\n        }\r\n        return null;\r\n    }\r\n\r\n    nextImage(): void {\r\n        if (this.images && this.images.length > 1) {\r\n            this.currentIndex = (this.currentIndex + 1) % this.images.length;\r\n            this.indexChange.emit(this.currentIndex);\r\n        }\r\n    }\r\n\r\n    prevImage(): void {\r\n        if (this.images && this.images.length > 1) {\r\n            this.currentIndex = this.currentIndex === 0 ? this.images.length - 1 : this.currentIndex - 1;\r\n            this.indexChange.emit(this.currentIndex);\r\n        }\r\n    }\r\n\r\n    setCurrentImage(index: number): void {\r\n        if (index >= 0 && index < this.images.length) {\r\n            this.currentIndex = index;\r\n            this.indexChange.emit(this.currentIndex);\r\n        }\r\n    } closeModal(): void {\r\n        document.body.classList.remove('modal-open');\r\n        this.close.emit();\r\n    }\r\n\r\n    onBackdropClick(event: Event): void {\r\n        if (event.target === event.currentTarget) {\r\n            this.closeModal();\r\n        }\r\n    }\r\n}\r\n"], "mappings": ";AAAA,SAASA,SAAS,EAAEC,KAAK,EAAEC,MAAM,EAAEC,YAAY,EAAqBC,YAAY,QAAQ,eAAe;AACvG,SAASC,YAAY,QAAQ,iBAAiB;AAavC,IAAMC,mBAAmB,GAAzB,MAAMA,mBAAmB;EAQ5BC,YACYC,GAAsB,EACtBC,UAAsB;IADtB,KAAAD,GAAG,GAAHA,GAAG;IACH,KAAAC,UAAU,GAAVA,UAAU;IATb,KAAAC,MAAM,GAAgB,EAAE;IACxB,KAAAC,YAAY,GAAW,CAAC;IACxB,KAAAC,SAAS,GAAY,KAAK;IAEzB,KAAAC,KAAK,GAAG,IAAIV,YAAY,EAAQ;IAChC,KAAAW,WAAW,GAAG,IAAIX,YAAY,EAAU;EAK9C;EAEJY,QAAQA,CAAA;IACJ,IAAI,IAAI,CAACJ,YAAY,IAAI,IAAI,CAACD,MAAM,CAACM,MAAM,EAAE;MACzC,IAAI,CAACL,YAAY,GAAG,CAAC;IACzB;IAEA;IACA,IAAI,CAACM,qBAAqB,EAAE;EAChC;EAEAC,WAAWA,CAAA;IACP;IACA,IAAI,IAAI,CAACN,SAAS,EAAE;MAChBO,QAAQ,CAACC,IAAI,CAACC,SAAS,CAACC,MAAM,CAAC,YAAY,CAAC;IAChD;EACJ;EAEAC,WAAWA,CAAA;IACP,IAAI,CAACN,qBAAqB,EAAE;EAChC;EAAaA,qBAAqBA,CAAA;IAC9B,IAAI,IAAI,CAACL,SAAS,EAAE;MAChBO,QAAQ,CAACC,IAAI,CAACC,SAAS,CAACG,GAAG,CAAC,YAAY,CAAC;IAC7C,CAAC,MAAM;MACHL,QAAQ,CAACC,IAAI,CAACC,SAAS,CAACC,MAAM,CAAC,YAAY,CAAC;IAChD;EACJ;EAGAG,aAAaA,CAACC,KAAoB;IAC9B,IAAI,CAAC,IAAI,CAACd,SAAS,EAAE;IAErB,QAAQc,KAAK,CAACC,GAAG;MACb,KAAK,QAAQ;QACT,IAAI,CAACC,UAAU,EAAE;QACjB;MACJ,KAAK,WAAW;QACZ,IAAI,CAACC,SAAS,EAAE;QAChB;MACJ,KAAK,YAAY;QACb,IAAI,CAACC,SAAS,EAAE;QAChB;IACR;EACJ;EAEAC,eAAeA,CAAA;IACX,IAAI,IAAI,CAACrB,MAAM,IAAI,IAAI,CAACA,MAAM,CAACM,MAAM,GAAG,CAAC,IAAI,IAAI,CAACL,YAAY,GAAG,IAAI,CAACD,MAAM,CAACM,MAAM,EAAE;MACjF,OAAO,IAAI,CAACN,MAAM,CAAC,IAAI,CAACC,YAAY,CAAC;IACzC;IACA,OAAO,IAAI;EACf;EAEAmB,SAASA,CAAA;IACL,IAAI,IAAI,CAACpB,MAAM,IAAI,IAAI,CAACA,MAAM,CAACM,MAAM,GAAG,CAAC,EAAE;MACvC,IAAI,CAACL,YAAY,GAAG,CAAC,IAAI,CAACA,YAAY,GAAG,CAAC,IAAI,IAAI,CAACD,MAAM,CAACM,MAAM;MAChE,IAAI,CAACF,WAAW,CAACkB,IAAI,CAAC,IAAI,CAACrB,YAAY,CAAC;IAC5C;EACJ;EAEAkB,SAASA,CAAA;IACL,IAAI,IAAI,CAACnB,MAAM,IAAI,IAAI,CAACA,MAAM,CAACM,MAAM,GAAG,CAAC,EAAE;MACvC,IAAI,CAACL,YAAY,GAAG,IAAI,CAACA,YAAY,KAAK,CAAC,GAAG,IAAI,CAACD,MAAM,CAACM,MAAM,GAAG,CAAC,GAAG,IAAI,CAACL,YAAY,GAAG,CAAC;MAC5F,IAAI,CAACG,WAAW,CAACkB,IAAI,CAAC,IAAI,CAACrB,YAAY,CAAC;IAC5C;EACJ;EAEAsB,eAAeA,CAACC,KAAa;IACzB,IAAIA,KAAK,IAAI,CAAC,IAAIA,KAAK,GAAG,IAAI,CAACxB,MAAM,CAACM,MAAM,EAAE;MAC1C,IAAI,CAACL,YAAY,GAAGuB,KAAK;MACzB,IAAI,CAACpB,WAAW,CAACkB,IAAI,CAAC,IAAI,CAACrB,YAAY,CAAC;IAC5C;EACJ;EAAEiB,UAAUA,CAAA;IACRT,QAAQ,CAACC,IAAI,CAACC,SAAS,CAACC,MAAM,CAAC,YAAY,CAAC;IAC5C,IAAI,CAACT,KAAK,CAACmB,IAAI,EAAE;EACrB;EAEAG,eAAeA,CAACT,KAAY;IACxB,IAAIA,KAAK,CAACU,MAAM,KAAKV,KAAK,CAACW,aAAa,EAAE;MACtC,IAAI,CAACT,UAAU,EAAE;IACrB;EACJ;CACH;AA3FYU,UAAA,EAARrC,KAAK,EAAE,C,kDAA0B;AACzBqC,UAAA,EAARrC,KAAK,EAAE,C,wDAA0B;AACzBqC,UAAA,EAARrC,KAAK,EAAE,C,qDAA4B;AAE1BqC,UAAA,EAATpC,MAAM,EAAE,C,iDAAkC;AACjCoC,UAAA,EAATpC,MAAM,EAAE,C,uDAA0C;AAkCnDoC,UAAA,EADClC,YAAY,CAAC,kBAAkB,EAAE,CAAC,QAAQ,CAAC,CAAC,C,uDAe5C;AAtDQE,mBAAmB,GAAAgC,UAAA,EAV/BtC,SAAS,CAAC;EACPuC,QAAQ,EAAE,iBAAiB;EAC3BC,WAAW,EAAE,8BAA8B;EAC3CC,SAAS,EAAE,CAAC,8BAA8B,CAAC;EAC3CC,UAAU,EAAE,IAAI;EAChBC,OAAO,EAAE,CAACtC,YAAY,CAAC;EACvBuC,IAAI,EAAE;IACF,uBAAuB,EAAE;;CAEhC,CAAC,C,EACWtC,mBAAmB,CA4F/B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}