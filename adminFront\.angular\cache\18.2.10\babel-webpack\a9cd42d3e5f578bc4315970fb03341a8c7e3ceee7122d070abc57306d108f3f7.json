{"ast": null, "code": "import addBusinessDays from \"../addBusinessDays/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\nimport toInteger from \"../_lib/toInteger/index.js\";\n/**\n * @name subBusinessDays\n * @category Day Helpers\n * @summary Substract the specified number of business days (mon - fri) to the given date.\n *\n * @description\n * Substract the specified number of business days (mon - fri) to the given date, ignoring weekends.\n *\n * @param {Date|Number} date - the date to be changed\n * @param {Number} amount - the amount of business days to be subtracted. Positive decimals will be rounded using `Math.floor`, decimals less than zero will be rounded using `Math.ceil`.\n * @returns {Date} the new date with the business days subtracted\n * @throws {TypeError} 2 arguments required\n *\n * @example\n * // Substract 10 business days from 1 September 2014:\n * const result = subBusinessDays(new Date(2014, 8, 1), 10)\n * //=> Mon Aug 18 2014 00:00:00 (skipped weekend days)\n */\nexport default function subBusinessDays(dirtyDate, dirtyAmount) {\n  requiredArgs(2, arguments);\n  var amount = toInteger(dirtyAmount);\n  return addBusinessDays(dirtyDate, -amount);\n}", "map": {"version": 3, "names": ["addBusinessDays", "requiredArgs", "toInteger", "subBusinessDays", "dirtyDate", "dirtyAmount", "arguments", "amount"], "sources": ["C:/Users/<USER>/Documents/salechange-product/adminFront/node_modules/date-fns/esm/subBusinessDays/index.js"], "sourcesContent": ["import addBusinessDays from \"../addBusinessDays/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\nimport toInteger from \"../_lib/toInteger/index.js\";\n/**\n * @name subBusinessDays\n * @category Day Helpers\n * @summary Substract the specified number of business days (mon - fri) to the given date.\n *\n * @description\n * Substract the specified number of business days (mon - fri) to the given date, ignoring weekends.\n *\n * @param {Date|Number} date - the date to be changed\n * @param {Number} amount - the amount of business days to be subtracted. Positive decimals will be rounded using `Math.floor`, decimals less than zero will be rounded using `Math.ceil`.\n * @returns {Date} the new date with the business days subtracted\n * @throws {TypeError} 2 arguments required\n *\n * @example\n * // Substract 10 business days from 1 September 2014:\n * const result = subBusinessDays(new Date(2014, 8, 1), 10)\n * //=> Mon Aug 18 2014 00:00:00 (skipped weekend days)\n */\nexport default function subBusinessDays(dirtyDate, dirtyAmount) {\n  requiredArgs(2, arguments);\n  var amount = toInteger(dirtyAmount);\n  return addBusinessDays(dirtyDate, -amount);\n}"], "mappings": "AAAA,OAAOA,eAAe,MAAM,6BAA6B;AACzD,OAAOC,YAAY,MAAM,+BAA+B;AACxD,OAAOC,SAAS,MAAM,4BAA4B;AAClD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,SAASC,eAAeA,CAACC,SAAS,EAAEC,WAAW,EAAE;EAC9DJ,YAAY,CAAC,CAAC,EAAEK,SAAS,CAAC;EAC1B,IAAIC,MAAM,GAAGL,SAAS,CAACG,WAAW,CAAC;EACnC,OAAOL,eAAe,CAACI,SAAS,EAAE,CAACG,MAAM,CAAC;AAC5C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}