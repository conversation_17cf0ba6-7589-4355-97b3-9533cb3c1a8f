{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { concatMap, of, tap } from 'rxjs';\nimport { SharedModule } from '../../components/shared.module';\nimport { BaseComponent } from '../../components/base/baseComponent';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/shared/helper/allowHelper\";\nimport * as i2 from \"@nebular/theme\";\nimport * as i3 from \"src/app/shared/helper/validationHelper\";\nimport * as i4 from \"src/services/api/services\";\nimport * as i5 from \"src/app/shared/services/utility.service\";\nimport * as i6 from \"src/app/shared/services/message.service\";\nimport * as i7 from \"@angular/common\";\nimport * as i8 from \"@angular/forms\";\nimport * as i9 from \"../../components/breadcrumb/breadcrumb.component\";\nimport * as i10 from \"../../components/pagination/pagination.component\";\nimport * as i11 from \"../../../@theme/pipes/base-file.pipe\";\nfunction SchematicPictureComponent_nb_option_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 19);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const buildCase_r2 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", buildCase_r2.cID);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", buildCase_r2.CBuildCaseName, \" \");\n  }\n}\nfunction SchematicPictureComponent_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 20);\n    i0.ɵɵlistener(\"click\", function SchematicPictureComponent_div_14_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r3 = i0.ɵɵnextContext();\n      const dialog_r5 = i0.ɵɵreference(39);\n      return i0.ɵɵresetView(ctx_r3.addNew(dialog_r5));\n    });\n    i0.ɵɵelementStart(1, \"button\", 21);\n    i0.ɵɵtext(2, \" \\u5716\\u7247\\u4E0A\\u50B3\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction SchematicPictureComponent_tr_35_button_17_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 24);\n    i0.ɵɵlistener(\"click\", function SchematicPictureComponent_tr_35_button_17_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const item_r7 = i0.ɵɵnextContext().$implicit;\n      const ctx_r3 = i0.ɵɵnextContext();\n      const dialog_r5 = i0.ɵɵreference(39);\n      return i0.ɵɵresetView(ctx_r3.addNew(dialog_r5, item_r7));\n    });\n    i0.ɵɵtext(1, \"\\u6AA2\\u8996\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SchematicPictureComponent_tr_35_button_18_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 24);\n    i0.ɵɵlistener(\"click\", function SchematicPictureComponent_tr_35_button_18_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const item_r7 = i0.ɵɵnextContext().$implicit;\n      const ctx_r3 = i0.ɵɵnextContext();\n      const dialog_r5 = i0.ɵɵreference(39);\n      return i0.ɵɵresetView(ctx_r3.changePicture(dialog_r5, item_r7));\n    });\n    i0.ɵɵtext(1, \"\\u6539\\u8B8A\\u5716\\u7247\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SchematicPictureComponent_tr_35_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"td\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"td\");\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"td\");\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"td\");\n    i0.ɵɵtext(14);\n    i0.ɵɵpipe(15, \"date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"td\", 22);\n    i0.ɵɵtemplate(17, SchematicPictureComponent_tr_35_button_17_Template, 2, 0, \"button\", 23)(18, SchematicPictureComponent_tr_35_button_18_Template, 2, 0, \"button\", 23);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const item_r7 = ctx.$implicit;\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r7.CId);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r7.CName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r7.CPart);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r7.CLocation);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r7.CSelectName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r7.CPictureCode);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(15, 9, item_r7.CUpdateDT, \"yyyy/MM/dd HH:mm:ss\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.isRead);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.isRead);\n  }\n}\nfunction SchematicPictureComponent_ng_template_38_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 31);\n    i0.ɵɵelement(1, \"img\", 32);\n    i0.ɵɵpipe(2, \"addBaseFile\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"src\", i0.ɵɵpipeBind1(2, 1, ctx_r3.currentImageShowing), i0.ɵɵsanitizeUrl);\n  }\n}\nfunction SchematicPictureComponent_ng_template_38_ng_template_6_tr_14_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\")(2, \"input\", 38);\n    i0.ɵɵlistener(\"blur\", function SchematicPictureComponent_ng_template_38_ng_template_6_tr_14_Template_input_blur_2_listener($event) {\n      const i_r13 = i0.ɵɵrestoreView(_r12).index;\n      const ctx_r3 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r3.renameFile($event, i_r13));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(3, \"td\", 39);\n    i0.ɵɵelement(4, \"img\", 32);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\", 22)(6, \"button\", 40);\n    i0.ɵɵlistener(\"click\", function SchematicPictureComponent_ng_template_38_ng_template_6_tr_14_Template_button_click_6_listener() {\n      const picture_r14 = i0.ɵɵrestoreView(_r12).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r3.removeImage(picture_r14.id));\n    });\n    i0.ɵɵtext(7, \"\\u5220\\u9664\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const picture_r14 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"value\", picture_r14.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", picture_r14.data, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction SchematicPictureComponent_ng_template_38_ng_template_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 33);\n    i0.ɵɵlistener(\"click\", function SchematicPictureComponent_ng_template_38_ng_template_6_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const inputFile_r11 = i0.ɵɵreference(3);\n      return i0.ɵɵresetView(inputFile_r11.click());\n    });\n    i0.ɵɵtext(1, \"\\u5716\\u7247\\u4E0A\\u50B3\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(2, \"input\", 34, 2);\n    i0.ɵɵlistener(\"change\", function SchematicPictureComponent_ng_template_38_ng_template_6_Template_input_change_2_listener($event) {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r3.detectFiles($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 35)(5, \"table\", 36)(6, \"thead\")(7, \"tr\", 14)(8, \"th\", 37);\n    i0.ɵɵtext(9, \"\\u6587\\u4EF6\\u540D\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"th\", 15);\n    i0.ɵɵtext(11, \"\\u6AA2\\u8996\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(12, \"th\", 15);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"tbody\");\n    i0.ɵɵtemplate(14, SchematicPictureComponent_ng_template_38_ng_template_6_tr_14_Template, 8, 2, \"tr\", 16);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassMap(!ctx_r3.isEdit ? \"btn btn-info\" : ctx_r3.listPictures.length < 1 ? \"btn btn-info\" : \"btn btn-info disable\");\n    i0.ɵɵadvance(14);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.listPictures);\n  }\n}\nfunction SchematicPictureComponent_ng_template_38_button_12_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r16 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 41);\n    i0.ɵɵlistener(\"click\", function SchematicPictureComponent_ng_template_38_button_12_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r16);\n      const ref_r15 = i0.ɵɵnextContext().dialogRef;\n      const ctx_r3 = i0.ɵɵnextContext();\n      const dialog_r5 = i0.ɵɵreference(39);\n      ctx_r3.uploadImage(dialog_r5);\n      return i0.ɵɵresetView(ref_r15.close());\n    });\n    i0.ɵɵtext(1, \"\\u5132\\u5B58\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SchematicPictureComponent_ng_template_38_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-card\", 25)(1, \"nb-card-header\")(2, \"span\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"nb-card-body\", 26);\n    i0.ɵɵtemplate(5, SchematicPictureComponent_ng_template_38_div_5_Template, 3, 3, \"div\", 27)(6, SchematicPictureComponent_ng_template_38_ng_template_6_Template, 15, 3, \"ng-template\", null, 1, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"nb-card-footer\")(9, \"div\", 28)(10, \"button\", 29);\n    i0.ɵɵlistener(\"click\", function SchematicPictureComponent_ng_template_38_Template_button_click_10_listener() {\n      const ref_r15 = i0.ɵɵrestoreView(_r9).dialogRef;\n      const ctx_r3 = i0.ɵɵnextContext();\n      ref_r15.close();\n      return i0.ɵɵresetView(ctx_r3.currentImageShowing = \"\");\n    });\n    i0.ɵɵtext(11, \"\\u53D6\\u6D88\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(12, SchematicPictureComponent_ng_template_38_button_12_Template, 2, 0, \"button\", 30);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const upload_r17 = i0.ɵɵreference(7);\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.currentImageShowing ? \"\\u6AA2\\u8996\" : \"\\u5716\\u7247\\u4E0A\\u50B3\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.currentImageShowing)(\"ngIfElse\", upload_r17);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r3.currentImageShowing);\n  }\n}\nexport class SchematicPictureComponent extends BaseComponent {\n  constructor(_allow, dialogService, valid, _infoPictureService, _buildCaseService, _pictureService, _utilityService, message) {\n    super(_allow);\n    this._allow = _allow;\n    this.dialogService = dialogService;\n    this.valid = valid;\n    this._infoPictureService = _infoPictureService;\n    this._buildCaseService = _buildCaseService;\n    this._pictureService = _pictureService;\n    this._utilityService = _utilityService;\n    this.message = message;\n    this.images = [];\n    this.listUserBuildCases = [];\n    this.currentImageShowing = \"\";\n    this.listPictures = [];\n    this.isEdit = false;\n  }\n  ngOnInit() {\n    this.getListBuildCase();\n  }\n  getListBuildCase() {\n    this._buildCaseService.apiBuildCaseGetUserBuildCasePost$Json({}).pipe(tap(res => {\n      if (res.StatusCode === 0) {\n        this.listUserBuildCases = res.Entries ?? [];\n        this.selectedBuildCaseId = this.listUserBuildCases[0].cID;\n      }\n    }), concatMap(() => this.getInfoPicturelList(1))).subscribe();\n  }\n  getInfoPicturelList(pageIndex) {\n    return this._infoPictureService.apiInfoPictureGetInfoPicturelListPost$Json({\n      body: {\n        PageIndex: pageIndex,\n        PageSize: this.pageSize,\n        CBuildCaseId: this.selectedBuildCaseId\n      }\n    }).pipe(tap(res => {\n      if (res.StatusCode == 0) {\n        this.images = res.Entries ?? [];\n        this.totalRecords = res.TotalItems;\n      }\n    }));\n  }\n  pageChanged(newPage) {\n    this.getInfoPicturelList(newPage).subscribe();\n  }\n  selectedChange(buildCaseId) {\n    this.selectedBuildCaseId = buildCaseId;\n    this.getInfoPicturelList(1).subscribe();\n  }\n  addNew(ref, item) {\n    this.dialogService.open(ref);\n    this.isEdit = false;\n    if (!!item) {\n      // 優先使用 CBase64，如果沒有則使用 CFile\n      this.currentImageShowing = item.CBase64 || item.CFile || '';\n    } else {\n      this.listPictures = [];\n    }\n  }\n  changePicture(ref, item) {\n    if (!!item && item.CId) {\n      this.dialogService.open(ref);\n      this.isEdit = true;\n      this.currentEditItem = item.CId;\n      this.listPictures = [];\n    }\n  }\n  validation() {\n    this.valid.clear();\n  }\n  onSubmit(ref) {}\n  detectFiles(event) {\n    for (let index = 0; index < event.target.files.length; index++) {\n      const file = event.target.files[index];\n      if (file) {\n        let reader = new FileReader();\n        reader.readAsDataURL(file);\n        reader.onload = () => {\n          let base64Str = reader.result;\n          if (!base64Str) {\n            return;\n          }\n          // Get name file ( no extension)\n          const fileNameWithoutExtension = file.name.split('.')[0];\n          // Find files with duplicate names\n          const existingFileIndex = this.listPictures.findIndex(picture => picture.name === fileNameWithoutExtension);\n          if (existingFileIndex !== -1) {\n            // If name is duplicate, update file data\n            this.listPictures[existingFileIndex] = {\n              ...this.listPictures[existingFileIndex],\n              data: base64Str,\n              CFile: file,\n              extension: this._utilityService.getFileExtension(file.name)\n            };\n          } else {\n            // If not duplicate, add new file\n            file.id = new Date().getTime();\n            this.listPictures.push({\n              id: new Date().getTime(),\n              name: fileNameWithoutExtension,\n              data: base64Str,\n              extension: this._utilityService.getFileExtension(file.name),\n              CFile: file\n            });\n          }\n          // Reset input file to be able to select the old file again\n          event.target.value = null;\n        };\n      }\n    }\n  }\n  removeImage(pictureId) {\n    this.listPictures = this.listPictures.filter(x => x.id != pictureId);\n  }\n  uploadImage(ref) {\n    if (!this.isEdit) {\n      this._infoPictureService.apiInfoPictureUploadListInfoPicturePost$Json({\n        body: {\n          CBuildCaseId: this.selectedBuildCaseId,\n          CPath: \"infoPicture\",\n          CFile: this.listPictures.map(x => x.CFile)\n        }\n      }).pipe(tap(res => {\n        if (res.StatusCode == 0) {\n          this.message.showSucessMSG('執行成功');\n          this.listPictures = [];\n        } else {\n          this.message.showErrorMSG(res.Message);\n        }\n        ref.close();\n      }), concatMap(res => res.StatusCode == 0 ? this.getInfoPicturelList(1) : of(null))).subscribe();\n    } else {\n      if (this.listPictures.length > 0 && this.listPictures[0].CFile) {\n        this._infoPictureService.apiInfoPictureUpdateInfoPicturePost$Json({\n          body: {\n            CBuildCaseId: this.selectedBuildCaseId,\n            CInfoPictureID: this.currentEditItem,\n            CFile: this.listPictures[0].CFile\n          }\n        }).pipe(tap(res => {\n          if (res.StatusCode == 0) {\n            this.message.showSucessMSG('執行成功');\n            this.listPictures = [];\n          } else {\n            this.message.showErrorMSG(res.Message);\n          }\n          ref.close();\n        }), concatMap(res => res.StatusCode == 0 ? this.getInfoPicturelList(1) : of(null))).subscribe();\n      }\n    }\n  }\n  renameFile(event, index) {\n    var blob = this.listPictures[index].CFile.slice(0, this.listPictures[index].CFile.size, this.listPictures[index].CFile.type);\n    var newFile = new File([blob], `${event.target.value + '.' + this.listPictures[index].extension}`, {\n      type: this.listPictures[index].CFile.type\n    });\n    this.listPictures[index].CFile = newFile;\n  }\n  static {\n    this.ɵfac = function SchematicPictureComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || SchematicPictureComponent)(i0.ɵɵdirectiveInject(i1.AllowHelper), i0.ɵɵdirectiveInject(i2.NbDialogService), i0.ɵɵdirectiveInject(i3.ValidationHelper), i0.ɵɵdirectiveInject(i4.InfoPictureService), i0.ɵɵdirectiveInject(i4.BuildCaseService), i0.ɵɵdirectiveInject(i4.PictureService), i0.ɵɵdirectiveInject(i5.UtilityService), i0.ɵɵdirectiveInject(i6.MessageService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: SchematicPictureComponent,\n      selectors: [[\"ngx-schematic-picture\"]],\n      standalone: true,\n      features: [i0.ɵɵInheritDefinitionFeature, i0.ɵɵStandaloneFeature],\n      decls: 40,\n      vars: 7,\n      consts: [[\"dialog\", \"\"], [\"upload\", \"\"], [\"inputFile\", \"\"], [\"accent\", \"success\"], [1, \"font-bold\", \"text-[#818181]\"], [1, \"d-flex\", \"flex-wrap\"], [1, \"col-md-6\"], [1, \"form-group\", \"d-flex\", \"align-items-center\", \"w-full\"], [\"for\", \"buildingName\", 1, \"label\", \"mr-2\"], [\"placeholder\", \"\\u5EFA\\u6848\", 1, \"w-full\", 3, \"ngModelChange\", \"selectedChange\", \"ngModel\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"d-flex justify-content-end w-full\", 3, \"click\", 4, \"ngIf\"], [1, \"table-responsive\", \"mt-4\"], [1, \"table\", \"table-striped\", \"border\", 2, \"min-width\", \"1000px\", \"background-color\", \"#f3f3f3\"], [2, \"background-color\", \"#27ae60\", \"color\", \"white\"], [\"scope\", \"col\", 1, \"col-1\"], [4, \"ngFor\", \"ngForOf\"], [1, \"d-flex\", \"justify-content-center\"], [\"aria-label\", \"Pagination\", 3, \"PageChange\", \"PageSizeChange\", \"CollectionSizeChange\", \"Page\", \"PageSize\", \"CollectionSize\"], [3, \"value\"], [1, \"d-flex\", \"justify-content-end\", \"w-full\", 3, \"click\"], [1, \"btn\", \"btn-info\"], [1, \"text-center\", \"w-32\"], [\"class\", \"btn btn-outline-primary btn-sm m-1\", 3, \"click\", 4, \"ngIf\"], [1, \"btn\", \"btn-outline-primary\", \"btn-sm\", \"m-1\", 3, \"click\"], [1, \"mr-md-5\", \"ml-md-5\", 2, \"height\", \"100%\", \"overflow\", \"auto\", \"width\", \"700px\"], [2, \"padding\", \"1rem 2rem\"], [\"class\", \"w-full h-auto\", 4, \"ngIf\", \"ngIfElse\"], [1, \"flex\", \"justify-center\", \"items-center\"], [1, \"btn\", \"btn-danger\", \"mr-2\", 3, \"click\"], [\"class\", \"btn btn-success\", 3, \"click\", 4, \"ngIf\"], [1, \"w-full\", \"h-auto\"], [1, \"fit-size\", 3, \"src\"], [3, \"click\"], [\"type\", \"file\", \"accept\", \"image/png, image/gif, image/jpeg\", \"multiple\", \"\", 1, \"hidden\", 3, \"change\"], [1, \"mt-3\", \"w-full\", \"flex\", \"flex-col\"], [1, \"table\", \"table-striped\", \"border\", 2, \"background-color\", \"#f3f3f3\"], [\"scope\", \"col\", 1, \"col-4\"], [\"nbInput\", \"\", \"type\", \"text\", 1, \"w-100\", \"p-2\", \"text-[13px]\", 3, \"blur\", \"value\"], [1, \"w-[100px]\", \"h-auto\"], [1, \"btn\", \"btn-outline-danger\", \"btn-sm\", \"m-1\", 3, \"click\"], [1, \"btn\", \"btn-success\", 3, \"click\"]],\n      template: function SchematicPictureComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"nb-card\", 3)(1, \"nb-card-header\");\n          i0.ɵɵelement(2, \"ngx-breadcrumb\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(3, \"nb-card-body\")(4, \"h1\", 4);\n          i0.ɵɵtext(5, \"\\u53EF\\u8A2D\\u5B9A\\u4E0A\\u50B3\\u5EFA\\u6750\\u793A\\u610F\\u5716\\u7247\\uFF0C\\u4E0A\\u50B3\\u524D\\u8ACB\\u5C07\\u5716\\u7247\\u6A94\\u6848\\u6539\\u70BA\\u793A\\u610F\\u5716\\u7247\\u6A94\\u540D\\u3002\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(6, \"div\", 5)(7, \"div\", 6)(8, \"div\", 7)(9, \"label\", 8);\n          i0.ɵɵtext(10, \"\\u5EFA\\u6848\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(11, \"nb-select\", 9);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function SchematicPictureComponent_Template_nb_select_ngModelChange_11_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.selectedBuildCaseId, $event) || (ctx.selectedBuildCaseId = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵlistener(\"selectedChange\", function SchematicPictureComponent_Template_nb_select_selectedChange_11_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.selectedChange($event));\n          });\n          i0.ɵɵtemplate(12, SchematicPictureComponent_nb_option_12_Template, 2, 2, \"nb-option\", 10);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(13, \"div\", 6);\n          i0.ɵɵtemplate(14, SchematicPictureComponent_div_14_Template, 3, 0, \"div\", 11);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(15, \"div\", 12)(16, \"table\", 13)(17, \"thead\")(18, \"tr\", 14)(19, \"th\", 15);\n          i0.ɵɵtext(20, \"\\u9805\\u6B21\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(21, \"th\", 15);\n          i0.ɵɵtext(22, \"\\u540D\\u7A31\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(23, \"th\", 15);\n          i0.ɵɵtext(24, \"\\u9805\\u76EE\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(25, \"th\", 15);\n          i0.ɵɵtext(26, \"\\u4F4D\\u7F6E\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(27, \"th\", 15);\n          i0.ɵɵtext(28, \"\\u5EFA\\u6750\\u9078\\u9805\\u540D\\u7A31\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(29, \"th\", 15);\n          i0.ɵɵtext(30, \"\\u793A\\u610F\\u5716\\u7247\\u6A94\\u540D\\uFF08\\u76F8\\u540C\\u5EFA\\u6848\\u4E0D\\u53EF\\u91CD\\u8907\\uFF09\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(31, \"th\", 15);\n          i0.ɵɵtext(32, \"\\u6700\\u65B0\\u5716\\u7247\\u4E0A\\u50B3\\u6642\\u9593\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(33, \"th\", 15);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(34, \"tbody\");\n          i0.ɵɵtemplate(35, SchematicPictureComponent_tr_35_Template, 19, 12, \"tr\", 16);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(36, \"nb-card-footer\", 17)(37, \"ngx-pagination\", 18);\n          i0.ɵɵtwoWayListener(\"PageChange\", function SchematicPictureComponent_Template_ngx_pagination_PageChange_37_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.pageIndex, $event) || (ctx.pageIndex = $event);\n            return i0.ɵɵresetView($event);\n          })(\"PageSizeChange\", function SchematicPictureComponent_Template_ngx_pagination_PageSizeChange_37_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.pageSize, $event) || (ctx.pageSize = $event);\n            return i0.ɵɵresetView($event);\n          })(\"CollectionSizeChange\", function SchematicPictureComponent_Template_ngx_pagination_CollectionSizeChange_37_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.totalRecords, $event) || (ctx.totalRecords = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵlistener(\"PageChange\", function SchematicPictureComponent_Template_ngx_pagination_PageChange_37_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.pageChanged($event));\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(38, SchematicPictureComponent_ng_template_38_Template, 13, 4, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor);\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(11);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.selectedBuildCaseId);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngForOf\", ctx.listUserBuildCases);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.isCreate);\n          i0.ɵɵadvance(21);\n          i0.ɵɵproperty(\"ngForOf\", ctx.images);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtwoWayProperty(\"Page\", ctx.pageIndex)(\"PageSize\", ctx.pageSize)(\"CollectionSize\", ctx.totalRecords);\n        }\n      },\n      dependencies: [CommonModule, i7.NgForOf, i7.NgIf, i7.DatePipe, SharedModule, i8.NgControlStatus, i8.NgModel, i2.NbCardComponent, i2.NbCardBodyComponent, i2.NbCardFooterComponent, i2.NbCardHeaderComponent, i2.NbInputDirective, i2.NbSelectComponent, i2.NbOptionComponent, i9.BreadcrumbComponent, i10.PaginationComponent, i11.BaseFilePipe],\n      styles: [\".disable[_ngcontent-%COMP%] {\\n  opacity: 0.5;\\n  pointer-events: none;\\n}\\n/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbInNjaGVtYXRpYy1waWN0dXJlLmNvbXBvbmVudC5zY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUFBO0VBQ0UsWUFBQTtFQUNBLG9CQUFBO0FBQ0YiLCJmaWxlIjoic2NoZW1hdGljLXBpY3R1cmUuY29tcG9uZW50LnNjc3MiLCJzb3VyY2VzQ29udGVudCI6WyIuZGlzYWJsZXtcclxuICBvcGFjaXR5OiAuNTtcclxuICBwb2ludGVyLWV2ZW50czogbm9uZTtcclxufVxyXG4iXX0= */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvcGFnZXMvc2VsZWN0aW9uLW1hbmFnZW1lbnQvc2NoZW1hdGljLXBpY3R1cmUvc2NoZW1hdGljLXBpY3R1cmUuY29tcG9uZW50LnNjc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUE7RUFDRSxZQUFBO0VBQ0Esb0JBQUE7QUFDRjtBQUNBLHdYQUF3WCIsInNvdXJjZXNDb250ZW50IjpbIi5kaXNhYmxle1xyXG4gIG9wYWNpdHk6IC41O1xyXG4gIHBvaW50ZXItZXZlbnRzOiBub25lO1xyXG59XHJcbiJdLCJzb3VyY2VSb290IjoiIn0= */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "concatMap", "of", "tap", "SharedModule", "BaseComponent", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵproperty", "buildCase_r2", "cID", "ɵɵadvance", "ɵɵtextInterpolate1", "CBuildCaseName", "ɵɵlistener", "SchematicPictureComponent_div_14_Template_div_click_0_listener", "ɵɵrestoreView", "_r3", "ctx_r3", "ɵɵnextContext", "dialog_r5", "ɵɵreference", "ɵɵresetView", "addNew", "SchematicPictureComponent_tr_35_button_17_Template_button_click_0_listener", "_r6", "item_r7", "$implicit", "SchematicPictureComponent_tr_35_button_18_Template_button_click_0_listener", "_r8", "changePicture", "ɵɵtemplate", "SchematicPictureComponent_tr_35_button_17_Template", "SchematicPictureComponent_tr_35_button_18_Template", "ɵɵtextInterpolate", "CId", "CName", "<PERSON>art", "CLocation", "CSelectName", "CPictureCode", "ɵɵpipeBind2", "CUpdateDT", "isRead", "ɵɵelement", "ɵɵpipeBind1", "currentImageShowing", "ɵɵsanitizeUrl", "SchematicPictureComponent_ng_template_38_ng_template_6_tr_14_Template_input_blur_2_listener", "$event", "i_r13", "_r12", "index", "renameFile", "SchematicPictureComponent_ng_template_38_ng_template_6_tr_14_Template_button_click_6_listener", "picture_r14", "removeImage", "id", "name", "data", "SchematicPictureComponent_ng_template_38_ng_template_6_Template_button_click_0_listener", "_r10", "inputFile_r11", "click", "SchematicPictureComponent_ng_template_38_ng_template_6_Template_input_change_2_listener", "detectFiles", "SchematicPictureComponent_ng_template_38_ng_template_6_tr_14_Template", "ɵɵclassMap", "isEdit", "listPictures", "length", "SchematicPictureComponent_ng_template_38_button_12_Template_button_click_0_listener", "_r16", "ref_r15", "dialogRef", "uploadImage", "close", "SchematicPictureComponent_ng_template_38_div_5_Template", "SchematicPictureComponent_ng_template_38_ng_template_6_Template", "ɵɵtemplateRefExtractor", "SchematicPictureComponent_ng_template_38_Template_button_click_10_listener", "_r9", "SchematicPictureComponent_ng_template_38_button_12_Template", "upload_r17", "SchematicPictureComponent", "constructor", "_allow", "dialogService", "valid", "_infoPictureService", "_buildCaseService", "_pictureService", "_utilityService", "message", "images", "listUserBuildCases", "ngOnInit", "getListBuildCase", "apiBuildCaseGetUserBuildCasePost$Json", "pipe", "res", "StatusCode", "Entries", "selectedBuildCaseId", "getInfoPicturelList", "subscribe", "pageIndex", "apiInfoPictureGetInfoPicturelListPost$Json", "body", "PageIndex", "PageSize", "pageSize", "CBuildCaseId", "totalRecords", "TotalItems", "pageChanged", "newPage", "<PERSON><PERSON><PERSON><PERSON>", "buildCaseId", "ref", "item", "open", "CBase64", "CFile", "currentEditItem", "validation", "clear", "onSubmit", "event", "target", "files", "file", "reader", "FileReader", "readAsDataURL", "onload", "base64Str", "result", "fileNameWithoutExtension", "split", "existingFileIndex", "findIndex", "picture", "extension", "getFileExtension", "Date", "getTime", "push", "value", "pictureId", "filter", "x", "apiInfoPictureUploadListInfoPicturePost$Json", "CPath", "map", "showSucessMSG", "showErrorMSG", "Message", "apiInfoPictureUpdateInfoPicturePost$Json", "CInfoPictureID", "blob", "slice", "size", "type", "newFile", "File", "ɵɵdirectiveInject", "i1", "AllowHelper", "i2", "NbDialogService", "i3", "ValidationHelper", "i4", "InfoPictureService", "BuildCaseService", "PictureService", "i5", "UtilityService", "i6", "MessageService", "selectors", "standalone", "features", "ɵɵInheritDefinitionFeature", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "SchematicPictureComponent_Template", "rf", "ctx", "ɵɵtwoWayListener", "SchematicPictureComponent_Template_nb_select_ngModelChange_11_listener", "_r1", "ɵɵtwoWayBindingSet", "SchematicPictureComponent_Template_nb_select_selected<PERSON><PERSON>e_11_listener", "SchematicPictureComponent_nb_option_12_Template", "SchematicPictureComponent_div_14_Template", "SchematicPictureComponent_tr_35_Template", "SchematicPictureComponent_Template_ngx_pagination_PageChange_37_listener", "SchematicPictureComponent_Template_ngx_pagination_PageSizeChange_37_listener", "SchematicPictureComponent_Template_ngx_pagination_CollectionSizeChange_37_listener", "SchematicPictureComponent_ng_template_38_Template", "ɵɵtwoWayProperty", "isCreate", "i7", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "DatePipe", "i8", "NgControlStatus", "NgModel", "NbCardComponent", "NbCardBodyComponent", "NbCardFooterComponent", "NbCardHeaderComponent", "NbInputDirective", "NbSelectComponent", "NbOptionComponent", "i9", "BreadcrumbComponent", "i10", "PaginationComponent", "i11", "BaseFilePipe", "styles"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\selection-management\\schematic-picture\\schematic-picture.component.ts", "C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\selection-management\\schematic-picture\\schematic-picture.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { NbDialogService } from '@nebular/theme';\r\nimport { ValidationHelper } from 'src/app/shared/helper/validationHelper';\r\nimport { BuildCaseService, InfoPictureService, PictureService } from 'src/services/api/services';\r\nimport { BuildCaseGetListReponse, GetInfoPictureListResponse } from 'src/services/api/models';\r\nimport { AllowHelper } from 'src/app/shared/helper/allowHelper';\r\nimport { concatMap, finalize, of, tap } from 'rxjs';\r\nimport { BaseFilePipe } from 'src/app/@theme/pipes/base-file.pipe';\r\nimport { Base64ImagePipe } from 'src/app/@theme/pipes/base64-image.pipe';\r\nimport { UtilityService } from 'src/app/shared/services/utility.service';\r\nimport { MessageService } from 'src/app/shared/services/message.service';\r\nimport { SharedModule } from '../../components/shared.module';\r\nimport { BaseComponent } from '../../components/base/baseComponent';\r\n\r\n\r\nexport interface selectItem {\r\n  label: string,\r\n  value: number,\r\n  key?: string\r\n}\r\n\r\n@Component({\r\n  selector: 'ngx-schematic-picture',\r\n  templateUrl: './schematic-picture.component.html',\r\n  styleUrls: ['./schematic-picture.component.scss'],\r\n  standalone: true,\r\n  imports: [\r\n    CommonModule,\r\n    SharedModule,\r\n    BaseFilePipe,\r\n    Base64ImagePipe\r\n  ],\r\n})\r\n\r\nexport class SchematicPictureComponent extends BaseComponent implements OnInit {\r\n\r\n  images: GetInfoPictureListResponse[] = [];\r\n  listUserBuildCases: BuildCaseGetListReponse[] = []\r\n\r\n  selectedBuildCaseId: number\r\n\r\n  currentImageShowing: string = \"\"\r\n\r\n  listPictures: any[] = []\r\n\r\n  isEdit: boolean = false;\r\n  currentEditItem: number;\r\n\r\n  constructor(\r\n    private _allow: AllowHelper,\r\n    private dialogService: NbDialogService,\r\n    private valid: ValidationHelper,\r\n    private _infoPictureService: InfoPictureService,\r\n    private _buildCaseService: BuildCaseService,\r\n    private _pictureService: PictureService,\r\n    private _utilityService: UtilityService,\r\n    private message: MessageService\r\n  ) {\r\n    super(_allow)\r\n  }\r\n\r\n  override ngOnInit(): void {\r\n    this.getListBuildCase();\r\n  }\r\n\r\n  getListBuildCase() {\r\n    this._buildCaseService.apiBuildCaseGetUserBuildCasePost$Json({})\r\n      .pipe(\r\n        tap(res => {\r\n          if (res.StatusCode === 0) {\r\n            this.listUserBuildCases = res.Entries! ?? []\r\n            this.selectedBuildCaseId = this.listUserBuildCases[0].cID!\r\n          }\r\n        }),\r\n        concatMap(() => this.getInfoPicturelList(1))\r\n      ).subscribe()\r\n  }\r\n\r\n  getInfoPicturelList(pageIndex: number) {\r\n    return this._infoPictureService.apiInfoPictureGetInfoPicturelListPost$Json({\r\n      body: {\r\n        PageIndex: pageIndex,\r\n        PageSize: this.pageSize,\r\n        CBuildCaseId: this.selectedBuildCaseId\r\n      }\r\n    }).pipe(\r\n      tap(res => {\r\n        if (res.StatusCode == 0) {\r\n          this.images = res.Entries! ?? []\r\n          this.totalRecords = res.TotalItems!;\r\n        }\r\n      })\r\n    )\r\n  }\r\n\r\n  pageChanged(newPage: number) {\r\n    this.getInfoPicturelList(newPage).subscribe();\r\n  }\r\n\r\n  selectedChange(buildCaseId: number) {\r\n    this.selectedBuildCaseId = buildCaseId;\r\n    this.getInfoPicturelList(1).subscribe();\r\n  }\r\n  addNew(ref: any, item?: GetInfoPictureListResponse) {\r\n    this.dialogService.open(ref)\r\n    this.isEdit = false;\r\n    if (!!item) {\r\n      // 優先使用 CBase64，如果沒有則使用 CFile\r\n      this.currentImageShowing = item.CBase64 || item.CFile || ''\r\n    }\r\n    else{\r\n      this.listPictures = [];\r\n    }\r\n  }\r\n  changePicture(ref: any, item?: GetInfoPictureListResponse){\r\n    if (!!item && item.CId) {\r\n      this.dialogService.open(ref)\r\n      this.isEdit = true;\r\n      this.currentEditItem = item.CId;\r\n      this.listPictures = [];\r\n    }\r\n  }\r\n\r\n  validation() {\r\n    this.valid.clear();\r\n  }\r\n\r\n  onSubmit(ref: any) {\r\n  }\r\n\r\n  detectFiles(event: any) {\r\n    for (let index = 0; index < event.target.files.length; index++) {\r\n      const file = event.target.files[index];\r\n      if (file) {\r\n        let reader = new FileReader();\r\n        reader.readAsDataURL(file);\r\n        reader.onload = () => {\r\n          let base64Str: string = reader.result as string;\r\n          if (!base64Str) {\r\n            return;\r\n          }\r\n          // Get name file ( no extension)\r\n          const fileNameWithoutExtension = file.name.split('.')[0];\r\n          // Find files with duplicate names\r\n          const existingFileIndex = this.listPictures.findIndex(picture => picture.name === fileNameWithoutExtension);\r\n          if (existingFileIndex !== -1) {\r\n            // If name is duplicate, update file data\r\n            this.listPictures[existingFileIndex] = {\r\n              ...this.listPictures[existingFileIndex],\r\n              data: base64Str,\r\n              CFile: file,\r\n              extension: this._utilityService.getFileExtension(file.name)\r\n            };\r\n          } else {\r\n            // If not duplicate, add new file\r\n            file.id = new Date().getTime();\r\n            this.listPictures.push({\r\n              id: new Date().getTime(),\r\n              name: fileNameWithoutExtension,\r\n              data: base64Str,\r\n              extension: this._utilityService.getFileExtension(file.name),\r\n              CFile: file\r\n            });\r\n          }\r\n          // Reset input file to be able to select the old file again\r\n          event.target.value = null;\r\n        };\r\n      }\r\n    }\r\n  }\r\n\r\n  removeImage(pictureId: number) {\r\n    this.listPictures = this.listPictures.filter(x => x.id != pictureId)\r\n  }\r\n\r\n  uploadImage(ref: any) {\r\n    if(!this.isEdit){\r\n      this._infoPictureService.apiInfoPictureUploadListInfoPicturePost$Json({\r\n        body: {\r\n          CBuildCaseId: this.selectedBuildCaseId,\r\n          CPath: \"infoPicture\",\r\n          CFile: this.listPictures.map(x => x.CFile)!\r\n        }\r\n      }).pipe(\r\n        tap(res => {\r\n          if (res.StatusCode == 0) {\r\n            this.message.showSucessMSG('執行成功')\r\n            this.listPictures = []\r\n          } else {\r\n            this.message.showErrorMSG(res.Message!)\r\n          }\r\n          ref.close();\r\n        }),\r\n        concatMap((res) => res.StatusCode! == 0 ? this.getInfoPicturelList(1) : of(null)),\r\n      ).subscribe()\r\n    }\r\n    else{\r\n      if(this.listPictures.length > 0 && this.listPictures[0].CFile){\r\n        this._infoPictureService.apiInfoPictureUpdateInfoPicturePost$Json({\r\n          body: {\r\n            CBuildCaseId: this.selectedBuildCaseId,\r\n            CInfoPictureID: this.currentEditItem,\r\n            CFile: this.listPictures[0].CFile\r\n          }\r\n        }).pipe(\r\n          tap(res => {\r\n            if (res.StatusCode == 0) {\r\n              this.message.showSucessMSG('執行成功')\r\n              this.listPictures = []\r\n            } else {\r\n              this.message.showErrorMSG(res.Message!)\r\n            }\r\n            ref.close();\r\n          }),\r\n          concatMap((res) => res.StatusCode! == 0 ? this.getInfoPicturelList(1) : of(null)),\r\n        ).subscribe()\r\n      }\r\n    }\r\n  }\r\n\r\n  renameFile(event: any, index: number) {\r\n    var blob = this.listPictures[index].CFile.slice(0, this.listPictures[index].CFile.size, this.listPictures[index].CFile.type);\r\n    var newFile = new File([blob], `${event.target.value + '.' + this.listPictures[index].extension}`, { type: this.listPictures[index].CFile.type });\r\n\r\n    this.listPictures[index].CFile = newFile\r\n  }\r\n\r\n\r\n}\r\n", "<nb-card accent=\"success\">\r\n  <nb-card-header>\r\n    <ngx-breadcrumb></ngx-breadcrumb>\r\n  </nb-card-header>\r\n  <nb-card-body>\r\n    <h1 class=\"font-bold text-[#818181]\">可設定上傳建材示意圖片，上傳前請將圖片檔案改為示意圖片檔名。</h1>\r\n\r\n    <div class=\"d-flex flex-wrap\">\r\n      <div class=\"col-md-6\">\r\n        <div class=\"form-group d-flex align-items-center w-full\">\r\n          <label for=\"buildingName\" class=\"label mr-2\">建案</label>\r\n          <nb-select placeholder=\"建案\" [(ngModel)]=\"selectedBuildCaseId\" (selectedChange)=\"selectedChange($event)\"\r\n            class=\"w-full\">\r\n            <nb-option *ngFor=\"let buildCase of listUserBuildCases\" [value]=\"buildCase.cID\">\r\n              {{ buildCase.CBuildCaseName }}\r\n            </nb-option>\r\n          </nb-select>\r\n        </div>\r\n      </div>\r\n      <div class=\"col-md-6\">\r\n        <div class=\"d-flex justify-content-end w-full\" *ngIf=\"isCreate\" (click)=\"addNew(dialog)\">\r\n          <button class=\"btn btn-info\">\r\n            圖片上傳</button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <div class=\"table-responsive mt-4\">\r\n      <table class=\"table table-striped border \" style=\"min-width: 1000px; background-color:#f3f3f3;\">\r\n        <thead>\r\n          <tr style=\"background-color: #27ae60; color: white;\">\r\n            <th scope=\"col\" class=\"col-1\">項次</th>\r\n            <th scope=\"col\" class=\"col-1\">名稱</th>\r\n            <th scope=\"col\" class=\"col-1\">項目</th>\r\n            <th scope=\"col\" class=\"col-1\">位置</th>\r\n            <th scope=\"col\" class=\"col-1\">建材選項名稱</th>\r\n            <th scope=\"col\" class=\"col-1\">示意圖片檔名（相同建案不可重複）</th>\r\n            <th scope=\"col\" class=\"col-1\">最新圖片上傳時間</th>\r\n            <th scope=\"col\" class=\"col-1\"></th>\r\n          </tr>\r\n        </thead>\r\n        <tbody>\r\n          <tr *ngFor=\"let item of images ; let i = index\">\r\n            <td>{{ item.CId}}</td>\r\n            <td>{{ item.CName}}</td>\r\n            <td>{{ item.CPart}}</td>\r\n            <td>{{ item.CLocation}}</td>\r\n            <td>{{ item.CSelectName}}</td>\r\n            <td>{{ item.CPictureCode}}</td>\r\n            <td>{{ item.CUpdateDT | date: \"yyyy/MM/dd HH:mm:ss\"}}</td>\r\n            <td class=\"text-center w-32\">\r\n              <button class=\"btn btn-outline-primary btn-sm m-1\" *ngIf=\"isRead\"\r\n                (click)=\"addNew(dialog, item)\">檢視</button>\r\n              <button class=\"btn btn-outline-primary btn-sm m-1\" *ngIf=\"isRead\"\r\n                (click)=\"changePicture(dialog, item)\">改變圖片</button>\r\n            </td>\r\n          </tr>\r\n        </tbody>\r\n      </table>\r\n    </div>\r\n  </nb-card-body>\r\n  <nb-card-footer class=\"d-flex justify-content-center\">\r\n    <ngx-pagination [(Page)]=\"pageIndex\" [(PageSize)]=\"pageSize\" [(CollectionSize)]=\"totalRecords\"\r\n      (PageChange)=\"pageChanged($event)\" aria-label=\"Pagination\">\r\n    </ngx-pagination>\r\n  </nb-card-footer>\r\n</nb-card>\r\n\r\n<ng-template #dialog let-data let-ref=\"dialogRef\">\r\n  <nb-card style=\"height: 100%; overflow: auto; width: 700px;\" class=\"mr-md-5 ml-md-5\">\r\n    <nb-card-header>\r\n      <span>\r\n        {{currentImageShowing ? '檢視' : '圖片上傳'}}\r\n      </span>\r\n    </nb-card-header>\r\n    <nb-card-body style=\"padding:1rem 2rem\">\r\n      <div class=\"w-full h-auto\" *ngIf=\"currentImageShowing else upload\">\r\n        <img class=\"fit-size\" [src]=\"currentImageShowing | addBaseFile\">\r\n      </div>\r\n      <ng-template #upload>\r\n        <button [class]=\"!isEdit ? 'btn btn-info' : listPictures.length < 1 ? 'btn btn-info' : 'btn btn-info disable'\"\r\n          (click)=\"inputFile.click()\">圖片上傳</button>\r\n        <input #inputFile type=\"file\" class=\"hidden\" (change)=\"detectFiles($event)\"\r\n          accept=\"image/png, image/gif, image/jpeg\" multiple>\r\n        <div class=\"mt-3 w-full flex flex-col\">\r\n          <table class=\"table table-striped border\" style=\"background-color:#f3f3f3;\">\r\n            <thead>\r\n              <tr style=\"background-color: #27ae60; color: white;\">\r\n                <th scope=\"col\" class=\"col-4\">文件名</th>\r\n                <th scope=\"col\" class=\"col-1\">檢視</th>\r\n                <th scope=\"col\" class=\"col-1\"></th>\r\n              </tr>\r\n            </thead>\r\n            <tbody>\r\n              <tr *ngFor=\"let picture of listPictures; let i = index\">\r\n                <td>\r\n                  <input nbInput class=\"w-100 p-2 text-[13px]\" type=\"text\" [value]=\"picture.name\"\r\n                    (blur)=\"renameFile($event, i)\">\r\n                </td>\r\n                <td class=\"w-[100px] h-auto\">\r\n                  <img class=\"fit-size\" [src]=\"picture.data\">\r\n                </td>\r\n                <td class=\"text-center w-32\">\r\n                  <button class=\"btn btn-outline-danger btn-sm m-1\" (click)=\"removeImage(picture.id)\">删除</button>\r\n                </td>\r\n              </tr>\r\n            </tbody>\r\n          </table>\r\n        </div>\r\n      </ng-template>\r\n    </nb-card-body>\r\n    <nb-card-footer>\r\n      <div class=\"flex justify-center items-center\">\r\n        <button class=\"btn btn-danger mr-2\" (click)=\"ref.close(); this.currentImageShowing = ''\">取消</button>\r\n        <button *ngIf=\"!currentImageShowing\" class=\"btn btn-success\"\r\n          (click)=\"uploadImage(dialog); ref.close()\">儲存</button>\r\n      </div>\r\n    </nb-card-footer>\r\n  </nb-card>\r\n</ng-template>"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAM9C,SAASC,SAAS,EAAYC,EAAE,EAAEC,GAAG,QAAQ,MAAM;AAKnD,SAASC,YAAY,QAAQ,gCAAgC;AAC7D,SAASC,aAAa,QAAQ,qCAAqC;;;;;;;;;;;;;;;ICAvDC,EAAA,CAAAC,cAAA,oBAAgF;IAC9ED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAF4CH,EAAA,CAAAI,UAAA,UAAAC,YAAA,CAAAC,GAAA,CAAuB;IAC7EN,EAAA,CAAAO,SAAA,EACF;IADEP,EAAA,CAAAQ,kBAAA,MAAAH,YAAA,CAAAI,cAAA,MACF;;;;;;IAKJT,EAAA,CAAAC,cAAA,cAAyF;IAAzBD,EAAA,CAAAU,UAAA,mBAAAC,+DAAA;MAAAX,EAAA,CAAAY,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,MAAAC,SAAA,GAAAhB,EAAA,CAAAiB,WAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAASJ,MAAA,CAAAK,MAAA,CAAAH,SAAA,CAAc;IAAA,EAAC;IACtFhB,EAAA,CAAAC,cAAA,iBAA6B;IAC3BD,EAAA,CAAAE,MAAA,gCAAI;IACRF,EADQ,CAAAG,YAAA,EAAS,EACX;;;;;;IA4BAH,EAAA,CAAAC,cAAA,iBACiC;IAA/BD,EAAA,CAAAU,UAAA,mBAAAU,2EAAA;MAAApB,EAAA,CAAAY,aAAA,CAAAS,GAAA;MAAA,MAAAC,OAAA,GAAAtB,EAAA,CAAAe,aAAA,GAAAQ,SAAA;MAAA,MAAAT,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,MAAAC,SAAA,GAAAhB,EAAA,CAAAiB,WAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAASJ,MAAA,CAAAK,MAAA,CAAAH,SAAA,EAAAM,OAAA,CAAoB;IAAA,EAAC;IAACtB,EAAA,CAAAE,MAAA,mBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;IAC5CH,EAAA,CAAAC,cAAA,iBACwC;IAAtCD,EAAA,CAAAU,UAAA,mBAAAc,2EAAA;MAAAxB,EAAA,CAAAY,aAAA,CAAAa,GAAA;MAAA,MAAAH,OAAA,GAAAtB,EAAA,CAAAe,aAAA,GAAAQ,SAAA;MAAA,MAAAT,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,MAAAC,SAAA,GAAAhB,EAAA,CAAAiB,WAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAASJ,MAAA,CAAAY,aAAA,CAAAV,SAAA,EAAAM,OAAA,CAA2B;IAAA,EAAC;IAACtB,EAAA,CAAAE,MAAA,+BAAI;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;IAXvDH,EADF,CAAAC,cAAA,SAAgD,SAC1C;IAAAD,EAAA,CAAAE,MAAA,GAAa;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACtBH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,GAAe;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACxBH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,GAAe;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACxBH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,GAAmB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC5BH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,IAAqB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC9BH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAE,MAAA,IAAsB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC/BH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAE,MAAA,IAAiD;;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC1DH,EAAA,CAAAC,cAAA,cAA6B;IAG3BD,EAFA,CAAA2B,UAAA,KAAAC,kDAAA,qBACiC,KAAAC,kDAAA,qBAEO;IAE5C7B,EADE,CAAAG,YAAA,EAAK,EACF;;;;;IAbCH,EAAA,CAAAO,SAAA,GAAa;IAAbP,EAAA,CAAA8B,iBAAA,CAAAR,OAAA,CAAAS,GAAA,CAAa;IACb/B,EAAA,CAAAO,SAAA,GAAe;IAAfP,EAAA,CAAA8B,iBAAA,CAAAR,OAAA,CAAAU,KAAA,CAAe;IACfhC,EAAA,CAAAO,SAAA,GAAe;IAAfP,EAAA,CAAA8B,iBAAA,CAAAR,OAAA,CAAAW,KAAA,CAAe;IACfjC,EAAA,CAAAO,SAAA,GAAmB;IAAnBP,EAAA,CAAA8B,iBAAA,CAAAR,OAAA,CAAAY,SAAA,CAAmB;IACnBlC,EAAA,CAAAO,SAAA,GAAqB;IAArBP,EAAA,CAAA8B,iBAAA,CAAAR,OAAA,CAAAa,WAAA,CAAqB;IACrBnC,EAAA,CAAAO,SAAA,GAAsB;IAAtBP,EAAA,CAAA8B,iBAAA,CAAAR,OAAA,CAAAc,YAAA,CAAsB;IACtBpC,EAAA,CAAAO,SAAA,GAAiD;IAAjDP,EAAA,CAAA8B,iBAAA,CAAA9B,EAAA,CAAAqC,WAAA,QAAAf,OAAA,CAAAgB,SAAA,yBAAiD;IAECtC,EAAA,CAAAO,SAAA,GAAY;IAAZP,EAAA,CAAAI,UAAA,SAAAU,MAAA,CAAAyB,MAAA,CAAY;IAEZvC,EAAA,CAAAO,SAAA,EAAY;IAAZP,EAAA,CAAAI,UAAA,SAAAU,MAAA,CAAAyB,MAAA,CAAY;;;;;IAuBxEvC,EAAA,CAAAC,cAAA,cAAmE;IACjED,EAAA,CAAAwC,SAAA,cAAgE;;IAClExC,EAAA,CAAAG,YAAA,EAAM;;;;IADkBH,EAAA,CAAAO,SAAA,EAAyC;IAAzCP,EAAA,CAAAI,UAAA,QAAAJ,EAAA,CAAAyC,WAAA,OAAA3B,MAAA,CAAA4B,mBAAA,GAAA1C,EAAA,CAAA2C,aAAA,CAAyC;;;;;;IAmBrD3C,EAFJ,CAAAC,cAAA,SAAwD,SAClD,gBAE+B;IAA/BD,EAAA,CAAAU,UAAA,kBAAAkC,4FAAAC,MAAA;MAAA,MAAAC,KAAA,GAAA9C,EAAA,CAAAY,aAAA,CAAAmC,IAAA,EAAAC,KAAA;MAAA,MAAAlC,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAkB,WAAA,CAAQJ,MAAA,CAAAmC,UAAA,CAAAJ,MAAA,EAAAC,KAAA,CAAqB;IAAA,EAAC;IAClC9C,EAFE,CAAAG,YAAA,EACiC,EAC9B;IACLH,EAAA,CAAAC,cAAA,aAA6B;IAC3BD,EAAA,CAAAwC,SAAA,cAA2C;IAC7CxC,EAAA,CAAAG,YAAA,EAAK;IAEHH,EADF,CAAAC,cAAA,aAA6B,iBACyD;IAAlCD,EAAA,CAAAU,UAAA,mBAAAwC,8FAAA;MAAA,MAAAC,WAAA,GAAAnD,EAAA,CAAAY,aAAA,CAAAmC,IAAA,EAAAxB,SAAA;MAAA,MAAAT,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAkB,WAAA,CAASJ,MAAA,CAAAsC,WAAA,CAAAD,WAAA,CAAAE,EAAA,CAAuB;IAAA,EAAC;IAACrD,EAAA,CAAAE,MAAA,mBAAE;IAE1FF,EAF0F,CAAAG,YAAA,EAAS,EAC5F,EACF;;;;IATwDH,EAAA,CAAAO,SAAA,GAAsB;IAAtBP,EAAA,CAAAI,UAAA,UAAA+C,WAAA,CAAAG,IAAA,CAAsB;IAIzDtD,EAAA,CAAAO,SAAA,GAAoB;IAApBP,EAAA,CAAAI,UAAA,QAAA+C,WAAA,CAAAI,IAAA,EAAAvD,EAAA,CAAA2C,aAAA,CAAoB;;;;;;IApBpD3C,EAAA,CAAAC,cAAA,iBAC8B;IAA5BD,EAAA,CAAAU,UAAA,mBAAA8C,wFAAA;MAAAxD,EAAA,CAAAY,aAAA,CAAA6C,IAAA;MAAA,MAAAC,aAAA,GAAA1D,EAAA,CAAAiB,WAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAASwC,aAAA,CAAAC,KAAA,EAAiB;IAAA,EAAC;IAAC3D,EAAA,CAAAE,MAAA,+BAAI;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAC3CH,EAAA,CAAAC,cAAA,mBACqD;IADRD,EAAA,CAAAU,UAAA,oBAAAkD,wFAAAf,MAAA;MAAA7C,EAAA,CAAAY,aAAA,CAAA6C,IAAA;MAAA,MAAA3C,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAkB,WAAA,CAAUJ,MAAA,CAAA+C,WAAA,CAAAhB,MAAA,CAAmB;IAAA,EAAC;IAA3E7C,EAAA,CAAAG,YAAA,EACqD;IAK7CH,EAJR,CAAAC,cAAA,cAAuC,gBACuC,YACnE,aACgD,aACrB;IAAAD,EAAA,CAAAE,MAAA,yBAAG;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACtCH,EAAA,CAAAC,cAAA,cAA8B;IAAAD,EAAA,CAAAE,MAAA,oBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACrCH,EAAA,CAAAwC,SAAA,cAAmC;IAEvCxC,EADE,CAAAG,YAAA,EAAK,EACC;IACRH,EAAA,CAAAC,cAAA,aAAO;IACLD,EAAA,CAAA2B,UAAA,KAAAmC,qEAAA,iBAAwD;IAc9D9D,EAFI,CAAAG,YAAA,EAAQ,EACF,EACJ;;;;IA5BEH,EAAA,CAAA+D,UAAA,EAAAjD,MAAA,CAAAkD,MAAA,oBAAAlD,MAAA,CAAAmD,YAAA,CAAAC,MAAA,+CAAsG;IAchFlE,EAAA,CAAAO,SAAA,IAAiB;IAAjBP,EAAA,CAAAI,UAAA,YAAAU,MAAA,CAAAmD,YAAA,CAAiB;;;;;;IAoB/CjE,EAAA,CAAAC,cAAA,iBAC6C;IAA3CD,EAAA,CAAAU,UAAA,mBAAAyD,oFAAA;MAAAnE,EAAA,CAAAY,aAAA,CAAAwD,IAAA;MAAA,MAAAC,OAAA,GAAArE,EAAA,CAAAe,aAAA,GAAAuD,SAAA;MAAA,MAAAxD,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,MAAAC,SAAA,GAAAhB,EAAA,CAAAiB,WAAA;MAASH,MAAA,CAAAyD,WAAA,CAAAvD,SAAA,CAAmB;MAAA,OAAAhB,EAAA,CAAAkB,WAAA,CAAEmD,OAAA,CAAAG,KAAA,EAAW;IAAA,EAAC;IAACxE,EAAA,CAAAE,MAAA,mBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;IA5C1DH,EAFJ,CAAAC,cAAA,kBAAqF,qBACnE,WACR;IACJD,EAAA,CAAAE,MAAA,GACF;IACFF,EADE,CAAAG,YAAA,EAAO,EACQ;IACjBH,EAAA,CAAAC,cAAA,uBAAwC;IAItCD,EAHA,CAAA2B,UAAA,IAAA8C,uDAAA,kBAAmE,IAAAC,+DAAA,iCAAA1E,EAAA,CAAA2E,sBAAA,CAG9C;IA+BvB3E,EAAA,CAAAG,YAAA,EAAe;IAGXH,EAFJ,CAAAC,cAAA,qBAAgB,cACgC,kBAC6C;IAArDD,EAAA,CAAAU,UAAA,mBAAAkE,2EAAA;MAAA,MAAAP,OAAA,GAAArE,EAAA,CAAAY,aAAA,CAAAiE,GAAA,EAAAP,SAAA;MAAA,MAAAxD,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAASsD,OAAA,CAAAG,KAAA,EAAW;MAAA,OAAAxE,EAAA,CAAAkB,WAAA,CAAAJ,MAAA,CAAA4B,mBAAA,GAA6B,EAAE;IAAA,EAAC;IAAC1C,EAAA,CAAAE,MAAA,oBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACpGH,EAAA,CAAA2B,UAAA,KAAAmD,2DAAA,qBAC6C;IAGnD9E,EAFI,CAAAG,YAAA,EAAM,EACS,EACT;;;;;IA9CJH,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAAQ,kBAAA,MAAAM,MAAA,CAAA4B,mBAAA,oDACF;IAG4B1C,EAAA,CAAAO,SAAA,GAA0B;IAAAP,EAA1B,CAAAI,UAAA,SAAAU,MAAA,CAAA4B,mBAAA,CAA0B,aAAAqC,UAAA,CAAW;IAsCtD/E,EAAA,CAAAO,SAAA,GAA0B;IAA1BP,EAAA,CAAAI,UAAA,UAAAU,MAAA,CAAA4B,mBAAA,CAA0B;;;AD/E3C,OAAM,MAAOsC,yBAA0B,SAAQjF,aAAa;EAc1DkF,YACUC,MAAmB,EACnBC,aAA8B,EAC9BC,KAAuB,EACvBC,mBAAuC,EACvCC,iBAAmC,EACnCC,eAA+B,EAC/BC,eAA+B,EAC/BC,OAAuB;IAE/B,KAAK,CAACP,MAAM,CAAC;IATL,KAAAA,MAAM,GAANA,MAAM;IACN,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,mBAAmB,GAAnBA,mBAAmB;IACnB,KAAAC,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,OAAO,GAAPA,OAAO;IApBjB,KAAAC,MAAM,GAAiC,EAAE;IACzC,KAAAC,kBAAkB,GAA8B,EAAE;IAIlD,KAAAjD,mBAAmB,GAAW,EAAE;IAEhC,KAAAuB,YAAY,GAAU,EAAE;IAExB,KAAAD,MAAM,GAAY,KAAK;EAcvB;EAES4B,QAAQA,CAAA;IACf,IAAI,CAACC,gBAAgB,EAAE;EACzB;EAEAA,gBAAgBA,CAAA;IACd,IAAI,CAACP,iBAAiB,CAACQ,qCAAqC,CAAC,EAAE,CAAC,CAC7DC,IAAI,CACHlG,GAAG,CAACmG,GAAG,IAAG;MACR,IAAIA,GAAG,CAACC,UAAU,KAAK,CAAC,EAAE;QACxB,IAAI,CAACN,kBAAkB,GAAGK,GAAG,CAACE,OAAQ,IAAI,EAAE;QAC5C,IAAI,CAACC,mBAAmB,GAAG,IAAI,CAACR,kBAAkB,CAAC,CAAC,CAAC,CAACrF,GAAI;MAC5D;IACF,CAAC,CAAC,EACFX,SAAS,CAAC,MAAM,IAAI,CAACyG,mBAAmB,CAAC,CAAC,CAAC,CAAC,CAC7C,CAACC,SAAS,EAAE;EACjB;EAEAD,mBAAmBA,CAACE,SAAiB;IACnC,OAAO,IAAI,CAACjB,mBAAmB,CAACkB,0CAA0C,CAAC;MACzEC,IAAI,EAAE;QACJC,SAAS,EAAEH,SAAS;QACpBI,QAAQ,EAAE,IAAI,CAACC,QAAQ;QACvBC,YAAY,EAAE,IAAI,CAACT;;KAEtB,CAAC,CAACJ,IAAI,CACLlG,GAAG,CAACmG,GAAG,IAAG;MACR,IAAIA,GAAG,CAACC,UAAU,IAAI,CAAC,EAAE;QACvB,IAAI,CAACP,MAAM,GAAGM,GAAG,CAACE,OAAQ,IAAI,EAAE;QAChC,IAAI,CAACW,YAAY,GAAGb,GAAG,CAACc,UAAW;MACrC;IACF,CAAC,CAAC,CACH;EACH;EAEAC,WAAWA,CAACC,OAAe;IACzB,IAAI,CAACZ,mBAAmB,CAACY,OAAO,CAAC,CAACX,SAAS,EAAE;EAC/C;EAEAY,cAAcA,CAACC,WAAmB;IAChC,IAAI,CAACf,mBAAmB,GAAGe,WAAW;IACtC,IAAI,CAACd,mBAAmB,CAAC,CAAC,CAAC,CAACC,SAAS,EAAE;EACzC;EACAlF,MAAMA,CAACgG,GAAQ,EAAEC,IAAiC;IAChD,IAAI,CAACjC,aAAa,CAACkC,IAAI,CAACF,GAAG,CAAC;IAC5B,IAAI,CAACnD,MAAM,GAAG,KAAK;IACnB,IAAI,CAAC,CAACoD,IAAI,EAAE;MACV;MACA,IAAI,CAAC1E,mBAAmB,GAAG0E,IAAI,CAACE,OAAO,IAAIF,IAAI,CAACG,KAAK,IAAI,EAAE;IAC7D,CAAC,MACG;MACF,IAAI,CAACtD,YAAY,GAAG,EAAE;IACxB;EACF;EACAvC,aAAaA,CAACyF,GAAQ,EAAEC,IAAiC;IACvD,IAAI,CAAC,CAACA,IAAI,IAAIA,IAAI,CAACrF,GAAG,EAAE;MACtB,IAAI,CAACoD,aAAa,CAACkC,IAAI,CAACF,GAAG,CAAC;MAC5B,IAAI,CAACnD,MAAM,GAAG,IAAI;MAClB,IAAI,CAACwD,eAAe,GAAGJ,IAAI,CAACrF,GAAG;MAC/B,IAAI,CAACkC,YAAY,GAAG,EAAE;IACxB;EACF;EAEAwD,UAAUA,CAAA;IACR,IAAI,CAACrC,KAAK,CAACsC,KAAK,EAAE;EACpB;EAEAC,QAAQA,CAACR,GAAQ,GACjB;EAEAtD,WAAWA,CAAC+D,KAAU;IACpB,KAAK,IAAI5E,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAG4E,KAAK,CAACC,MAAM,CAACC,KAAK,CAAC5D,MAAM,EAAElB,KAAK,EAAE,EAAE;MAC9D,MAAM+E,IAAI,GAAGH,KAAK,CAACC,MAAM,CAACC,KAAK,CAAC9E,KAAK,CAAC;MACtC,IAAI+E,IAAI,EAAE;QACR,IAAIC,MAAM,GAAG,IAAIC,UAAU,EAAE;QAC7BD,MAAM,CAACE,aAAa,CAACH,IAAI,CAAC;QAC1BC,MAAM,CAACG,MAAM,GAAG,MAAK;UACnB,IAAIC,SAAS,GAAWJ,MAAM,CAACK,MAAgB;UAC/C,IAAI,CAACD,SAAS,EAAE;YACd;UACF;UACA;UACA,MAAME,wBAAwB,GAAGP,IAAI,CAACzE,IAAI,CAACiF,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;UACxD;UACA,MAAMC,iBAAiB,GAAG,IAAI,CAACvE,YAAY,CAACwE,SAAS,CAACC,OAAO,IAAIA,OAAO,CAACpF,IAAI,KAAKgF,wBAAwB,CAAC;UAC3G,IAAIE,iBAAiB,KAAK,CAAC,CAAC,EAAE;YAC5B;YACA,IAAI,CAACvE,YAAY,CAACuE,iBAAiB,CAAC,GAAG;cACrC,GAAG,IAAI,CAACvE,YAAY,CAACuE,iBAAiB,CAAC;cACvCjF,IAAI,EAAE6E,SAAS;cACfb,KAAK,EAAEQ,IAAI;cACXY,SAAS,EAAE,IAAI,CAACnD,eAAe,CAACoD,gBAAgB,CAACb,IAAI,CAACzE,IAAI;aAC3D;UACH,CAAC,MAAM;YACL;YACAyE,IAAI,CAAC1E,EAAE,GAAG,IAAIwF,IAAI,EAAE,CAACC,OAAO,EAAE;YAC9B,IAAI,CAAC7E,YAAY,CAAC8E,IAAI,CAAC;cACrB1F,EAAE,EAAE,IAAIwF,IAAI,EAAE,CAACC,OAAO,EAAE;cACxBxF,IAAI,EAAEgF,wBAAwB;cAC9B/E,IAAI,EAAE6E,SAAS;cACfO,SAAS,EAAE,IAAI,CAACnD,eAAe,CAACoD,gBAAgB,CAACb,IAAI,CAACzE,IAAI,CAAC;cAC3DiE,KAAK,EAAEQ;aACR,CAAC;UACJ;UACA;UACAH,KAAK,CAACC,MAAM,CAACmB,KAAK,GAAG,IAAI;QAC3B,CAAC;MACH;IACF;EACF;EAEA5F,WAAWA,CAAC6F,SAAiB;IAC3B,IAAI,CAAChF,YAAY,GAAG,IAAI,CAACA,YAAY,CAACiF,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAC9F,EAAE,IAAI4F,SAAS,CAAC;EACtE;EAEA1E,WAAWA,CAAC4C,GAAQ;IAClB,IAAG,CAAC,IAAI,CAACnD,MAAM,EAAC;MACd,IAAI,CAACqB,mBAAmB,CAAC+D,4CAA4C,CAAC;QACpE5C,IAAI,EAAE;UACJI,YAAY,EAAE,IAAI,CAACT,mBAAmB;UACtCkD,KAAK,EAAE,aAAa;UACpB9B,KAAK,EAAE,IAAI,CAACtD,YAAY,CAACqF,GAAG,CAACH,CAAC,IAAIA,CAAC,CAAC5B,KAAK;;OAE5C,CAAC,CAACxB,IAAI,CACLlG,GAAG,CAACmG,GAAG,IAAG;QACR,IAAIA,GAAG,CAACC,UAAU,IAAI,CAAC,EAAE;UACvB,IAAI,CAACR,OAAO,CAAC8D,aAAa,CAAC,MAAM,CAAC;UAClC,IAAI,CAACtF,YAAY,GAAG,EAAE;QACxB,CAAC,MAAM;UACL,IAAI,CAACwB,OAAO,CAAC+D,YAAY,CAACxD,GAAG,CAACyD,OAAQ,CAAC;QACzC;QACAtC,GAAG,CAAC3C,KAAK,EAAE;MACb,CAAC,CAAC,EACF7E,SAAS,CAAEqG,GAAG,IAAKA,GAAG,CAACC,UAAW,IAAI,CAAC,GAAG,IAAI,CAACG,mBAAmB,CAAC,CAAC,CAAC,GAAGxG,EAAE,CAAC,IAAI,CAAC,CAAC,CAClF,CAACyG,SAAS,EAAE;IACf,CAAC,MACG;MACF,IAAG,IAAI,CAACpC,YAAY,CAACC,MAAM,GAAG,CAAC,IAAI,IAAI,CAACD,YAAY,CAAC,CAAC,CAAC,CAACsD,KAAK,EAAC;QAC5D,IAAI,CAAClC,mBAAmB,CAACqE,wCAAwC,CAAC;UAChElD,IAAI,EAAE;YACJI,YAAY,EAAE,IAAI,CAACT,mBAAmB;YACtCwD,cAAc,EAAE,IAAI,CAACnC,eAAe;YACpCD,KAAK,EAAE,IAAI,CAACtD,YAAY,CAAC,CAAC,CAAC,CAACsD;;SAE/B,CAAC,CAACxB,IAAI,CACLlG,GAAG,CAACmG,GAAG,IAAG;UACR,IAAIA,GAAG,CAACC,UAAU,IAAI,CAAC,EAAE;YACvB,IAAI,CAACR,OAAO,CAAC8D,aAAa,CAAC,MAAM,CAAC;YAClC,IAAI,CAACtF,YAAY,GAAG,EAAE;UACxB,CAAC,MAAM;YACL,IAAI,CAACwB,OAAO,CAAC+D,YAAY,CAACxD,GAAG,CAACyD,OAAQ,CAAC;UACzC;UACAtC,GAAG,CAAC3C,KAAK,EAAE;QACb,CAAC,CAAC,EACF7E,SAAS,CAAEqG,GAAG,IAAKA,GAAG,CAACC,UAAW,IAAI,CAAC,GAAG,IAAI,CAACG,mBAAmB,CAAC,CAAC,CAAC,GAAGxG,EAAE,CAAC,IAAI,CAAC,CAAC,CAClF,CAACyG,SAAS,EAAE;MACf;IACF;EACF;EAEApD,UAAUA,CAAC2E,KAAU,EAAE5E,KAAa;IAClC,IAAI4G,IAAI,GAAG,IAAI,CAAC3F,YAAY,CAACjB,KAAK,CAAC,CAACuE,KAAK,CAACsC,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC5F,YAAY,CAACjB,KAAK,CAAC,CAACuE,KAAK,CAACuC,IAAI,EAAE,IAAI,CAAC7F,YAAY,CAACjB,KAAK,CAAC,CAACuE,KAAK,CAACwC,IAAI,CAAC;IAC5H,IAAIC,OAAO,GAAG,IAAIC,IAAI,CAAC,CAACL,IAAI,CAAC,EAAE,GAAGhC,KAAK,CAACC,MAAM,CAACmB,KAAK,GAAG,GAAG,GAAG,IAAI,CAAC/E,YAAY,CAACjB,KAAK,CAAC,CAAC2F,SAAS,EAAE,EAAE;MAAEoB,IAAI,EAAE,IAAI,CAAC9F,YAAY,CAACjB,KAAK,CAAC,CAACuE,KAAK,CAACwC;IAAI,CAAE,CAAC;IAEjJ,IAAI,CAAC9F,YAAY,CAACjB,KAAK,CAAC,CAACuE,KAAK,GAAGyC,OAAO;EAC1C;;;uCA/LWhF,yBAAyB,EAAAhF,EAAA,CAAAkK,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAApK,EAAA,CAAAkK,iBAAA,CAAAG,EAAA,CAAAC,eAAA,GAAAtK,EAAA,CAAAkK,iBAAA,CAAAK,EAAA,CAAAC,gBAAA,GAAAxK,EAAA,CAAAkK,iBAAA,CAAAO,EAAA,CAAAC,kBAAA,GAAA1K,EAAA,CAAAkK,iBAAA,CAAAO,EAAA,CAAAE,gBAAA,GAAA3K,EAAA,CAAAkK,iBAAA,CAAAO,EAAA,CAAAG,cAAA,GAAA5K,EAAA,CAAAkK,iBAAA,CAAAW,EAAA,CAAAC,cAAA,GAAA9K,EAAA,CAAAkK,iBAAA,CAAAa,EAAA,CAAAC,cAAA;IAAA;EAAA;;;YAAzBhG,yBAAyB;MAAAiG,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAAnL,EAAA,CAAAoL,0BAAA,EAAApL,EAAA,CAAAqL,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,mCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;UClCpC3L,EADF,CAAAC,cAAA,iBAA0B,qBACR;UACdD,EAAA,CAAAwC,SAAA,qBAAiC;UACnCxC,EAAA,CAAAG,YAAA,EAAiB;UAEfH,EADF,CAAAC,cAAA,mBAAc,YACyB;UAAAD,EAAA,CAAAE,MAAA,2LAA8B;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAKlEH,EAHN,CAAAC,cAAA,aAA8B,aACN,aACqC,eACV;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACvDH,EAAA,CAAAC,cAAA,oBACiB;UADWD,EAAA,CAAA6L,gBAAA,2BAAAC,uEAAAjJ,MAAA;YAAA7C,EAAA,CAAAY,aAAA,CAAAmL,GAAA;YAAA/L,EAAA,CAAAgM,kBAAA,CAAAJ,GAAA,CAAAzF,mBAAA,EAAAtD,MAAA,MAAA+I,GAAA,CAAAzF,mBAAA,GAAAtD,MAAA;YAAA,OAAA7C,EAAA,CAAAkB,WAAA,CAAA2B,MAAA;UAAA,EAAiC;UAAC7C,EAAA,CAAAU,UAAA,4BAAAuL,wEAAApJ,MAAA;YAAA7C,EAAA,CAAAY,aAAA,CAAAmL,GAAA;YAAA,OAAA/L,EAAA,CAAAkB,WAAA,CAAkB0K,GAAA,CAAA3E,cAAA,CAAApE,MAAA,CAAsB;UAAA,EAAC;UAErG7C,EAAA,CAAA2B,UAAA,KAAAuK,+CAAA,wBAAgF;UAKtFlM,EAFI,CAAAG,YAAA,EAAY,EACR,EACF;UACNH,EAAA,CAAAC,cAAA,cAAsB;UACpBD,EAAA,CAAA2B,UAAA,KAAAwK,yCAAA,kBAAyF;UAK7FnM,EADE,CAAAG,YAAA,EAAM,EACF;UAMEH,EAJR,CAAAC,cAAA,eAAmC,iBAC+D,aACvF,cACgD,cACrB;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACrCH,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACrCH,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACrCH,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACrCH,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAE,MAAA,4CAAM;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACzCH,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAE,MAAA,wGAAgB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACnDH,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAE,MAAA,wDAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAC3CH,EAAA,CAAAwC,SAAA,cAAmC;UAEvCxC,EADE,CAAAG,YAAA,EAAK,EACC;UACRH,EAAA,CAAAC,cAAA,aAAO;UACLD,EAAA,CAAA2B,UAAA,KAAAyK,wCAAA,mBAAgD;UAkBxDpM,EAHM,CAAAG,YAAA,EAAQ,EACF,EACJ,EACO;UAEbH,EADF,CAAAC,cAAA,0BAAsD,0BAES;UADAD,EAA7C,CAAA6L,gBAAA,wBAAAQ,yEAAAxJ,MAAA;YAAA7C,EAAA,CAAAY,aAAA,CAAAmL,GAAA;YAAA/L,EAAA,CAAAgM,kBAAA,CAAAJ,GAAA,CAAAtF,SAAA,EAAAzD,MAAA,MAAA+I,GAAA,CAAAtF,SAAA,GAAAzD,MAAA;YAAA,OAAA7C,EAAA,CAAAkB,WAAA,CAAA2B,MAAA;UAAA,EAAoB,4BAAAyJ,6EAAAzJ,MAAA;YAAA7C,EAAA,CAAAY,aAAA,CAAAmL,GAAA;YAAA/L,EAAA,CAAAgM,kBAAA,CAAAJ,GAAA,CAAAjF,QAAA,EAAA9D,MAAA,MAAA+I,GAAA,CAAAjF,QAAA,GAAA9D,MAAA;YAAA,OAAA7C,EAAA,CAAAkB,WAAA,CAAA2B,MAAA;UAAA,EAAwB,kCAAA0J,mFAAA1J,MAAA;YAAA7C,EAAA,CAAAY,aAAA,CAAAmL,GAAA;YAAA/L,EAAA,CAAAgM,kBAAA,CAAAJ,GAAA,CAAA/E,YAAA,EAAAhE,MAAA,MAAA+I,GAAA,CAAA/E,YAAA,GAAAhE,MAAA;YAAA,OAAA7C,EAAA,CAAAkB,WAAA,CAAA2B,MAAA;UAAA,EAAkC;UAC5F7C,EAAA,CAAAU,UAAA,wBAAA2L,yEAAAxJ,MAAA;YAAA7C,EAAA,CAAAY,aAAA,CAAAmL,GAAA;YAAA,OAAA/L,EAAA,CAAAkB,WAAA,CAAc0K,GAAA,CAAA7E,WAAA,CAAAlE,MAAA,CAAmB;UAAA,EAAC;UAGxC7C,EAFI,CAAAG,YAAA,EAAiB,EACF,EACT;UAEVH,EAAA,CAAA2B,UAAA,KAAA6K,iDAAA,iCAAAxM,EAAA,CAAA2E,sBAAA,CAAkD;;;UAzDZ3E,EAAA,CAAAO,SAAA,IAAiC;UAAjCP,EAAA,CAAAyM,gBAAA,YAAAb,GAAA,CAAAzF,mBAAA,CAAiC;UAE1BnG,EAAA,CAAAO,SAAA,EAAqB;UAArBP,EAAA,CAAAI,UAAA,YAAAwL,GAAA,CAAAjG,kBAAA,CAAqB;UAOV3F,EAAA,CAAAO,SAAA,GAAc;UAAdP,EAAA,CAAAI,UAAA,SAAAwL,GAAA,CAAAc,QAAA,CAAc;UAsBvC1M,EAAA,CAAAO,SAAA,IAAY;UAAZP,EAAA,CAAAI,UAAA,YAAAwL,GAAA,CAAAlG,MAAA,CAAY;UAoBvB1F,EAAA,CAAAO,SAAA,GAAoB;UAAyBP,EAA7C,CAAAyM,gBAAA,SAAAb,GAAA,CAAAtF,SAAA,CAAoB,aAAAsF,GAAA,CAAAjF,QAAA,CAAwB,mBAAAiF,GAAA,CAAA/E,YAAA,CAAkC;;;qBDlC9FnH,YAAY,EAAAiN,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EAAAF,EAAA,CAAAG,QAAA,EACZhN,YAAY,EAAAiN,EAAA,CAAAC,eAAA,EAAAD,EAAA,CAAAE,OAAA,EAAA5C,EAAA,CAAA6C,eAAA,EAAA7C,EAAA,CAAA8C,mBAAA,EAAA9C,EAAA,CAAA+C,qBAAA,EAAA/C,EAAA,CAAAgD,qBAAA,EAAAhD,EAAA,CAAAiD,gBAAA,EAAAjD,EAAA,CAAAkD,iBAAA,EAAAlD,EAAA,CAAAmD,iBAAA,EAAAC,EAAA,CAAAC,mBAAA,EAAAC,GAAA,CAAAC,mBAAA,EAAAC,GAAA,CAAAC,YAAA;MAAAC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}