{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { NbCheckboxModule } from '@nebular/theme';\nimport { tap } from 'rxjs';\nimport { SharedModule } from 'src/app/pages/components/shared.module';\nimport { BaseComponent } from 'src/app/pages/components/base/baseComponent';\nimport { EEvent } from 'src/app/shared/services/event.service';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/shared/helper/allowHelper\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"src/app/shared/services/message.service\";\nimport * as i4 from \"src/services/api/services\";\nimport * as i5 from \"src/app/shared/services/utility.service\";\nimport * as i6 from \"src/app/shared/helper/validationHelper\";\nimport * as i7 from \"@angular/common\";\nimport * as i8 from \"src/app/shared/services/event.service\";\nimport * as i9 from \"@angular/forms\";\nimport * as i10 from \"@nebular/theme\";\nimport * as i11 from \"../../../components/breadcrumb/breadcrumb.component\";\nimport * as i12 from \"../../../../@theme/pipes/base-file.pipe\";\nfunction DetailContentManagementSalesAccountComponent_ng_container_9_tr_17_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 32)(2, \"label\");\n    i0.ɵɵtext(3, \"\\u6587\\u4EF6\\u540D \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(4, \" \\u00A0 \");\n    i0.ɵɵelementStart(5, \"input\", 33);\n    i0.ɵɵlistener(\"blur\", function DetailContentManagementSalesAccountComponent_ng_container_9_tr_17_Template_input_blur_5_listener($event) {\n      const i_r6 = i0.ɵɵrestoreView(_r5).index;\n      const formItemReq_r2 = i0.ɵɵnextContext().$implicit;\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.renameFile($event, i_r6, formItemReq_r2));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"td\", 34);\n    i0.ɵɵelement(7, \"img\", 35);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"td\", 36)(9, \"button\", 37);\n    i0.ɵɵlistener(\"click\", function DetailContentManagementSalesAccountComponent_ng_container_9_tr_17_Template_button_click_9_listener() {\n      const picture_r7 = i0.ɵɵrestoreView(_r5).$implicit;\n      const formItemReq_r2 = i0.ɵɵnextContext().$implicit;\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.removeImage(picture_r7.id, formItemReq_r2));\n    });\n    i0.ɵɵtext(10, \"\\u5220\\u9664\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const picture_r7 = ctx.$implicit;\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"value\", picture_r7.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", picture_r7.data, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_9_div_18_img_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 42);\n    i0.ɵɵpipe(1, \"addBaseFile\");\n  }\n  if (rf & 2) {\n    const formItemReq_r2 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵproperty(\"src\", i0.ɵɵpipeBind1(1, 1, formItemReq_r2.CDesignFileUrl), i0.ɵɵsanitizeUrl);\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_9_div_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 38)(1, \"div\", 20)(2, \"table\", 39)(3, \"tbody\")(4, \"tr\");\n    i0.ɵɵelement(5, \"td\", 32);\n    i0.ɵɵelementStart(6, \"td\", 40);\n    i0.ɵɵtemplate(7, DetailContentManagementSalesAccountComponent_ng_container_9_div_18_img_7_Template, 2, 3, \"img\", 41);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(8, \"td\", 36);\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const formItemReq_r2 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵproperty(\"ngClass\", formItemReq_r2.listPictures.length ? \"hidden\" : \"\");\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngIf\", formItemReq_r2.CDesignFileUrl);\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_9_nb_option_30_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 43);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const case_r8 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", case_r8);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", case_r8.label, \" \");\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_9_label_36_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"label\", 44)(1, \"nb-checkbox\", 45);\n    i0.ɵɵtwoWayListener(\"checkedChange\", function DetailContentManagementSalesAccountComponent_ng_container_9_label_36_Template_nb_checkbox_checkedChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r9);\n      const formItemReq_r2 = i0.ɵɵnextContext().$implicit;\n      i0.ɵɵtwoWayBindingSet(formItemReq_r2.allSelected, $event) || (formItemReq_r2.allSelected = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"checkedChange\", function DetailContentManagementSalesAccountComponent_ng_container_9_label_36_Template_nb_checkbox_checkedChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r9);\n      const formItemReq_r2 = i0.ɵɵnextContext().$implicit;\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.onCheckAllChange($event, formItemReq_r2));\n    });\n    i0.ɵɵtext(2, \" \\u5168\\u9078 \");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const formItemReq_r2 = i0.ɵɵnextContext().$implicit;\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"checked\", formItemReq_r2.allSelected);\n    i0.ɵɵproperty(\"disabled\", ctx_r3.listFormItem.CIsLock);\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_9_label_37_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"label\", 44)(1, \"nb-checkbox\", 46);\n    i0.ɵɵtwoWayListener(\"checkedChange\", function DetailContentManagementSalesAccountComponent_ng_container_9_label_37_Template_nb_checkbox_checkedChange_1_listener($event) {\n      const item_r11 = i0.ɵɵrestoreView(_r10).$implicit;\n      const formItemReq_r2 = i0.ɵɵnextContext().$implicit;\n      i0.ɵɵtwoWayBindingSet(formItemReq_r2.selectedItems[item_r11], $event) || (formItemReq_r2.selectedItems[item_r11] = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"checkedChange\", function DetailContentManagementSalesAccountComponent_ng_container_9_label_37_Template_nb_checkbox_checkedChange_1_listener($event) {\n      const item_r11 = i0.ɵɵrestoreView(_r10).$implicit;\n      const formItemReq_r2 = i0.ɵɵnextContext().$implicit;\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.onCheckboxHouseHoldListChange($event, item_r11, formItemReq_r2));\n    });\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const item_r11 = ctx.$implicit;\n    const formItemReq_r2 = i0.ɵɵnextContext().$implicit;\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"checked\", formItemReq_r2.selectedItems[item_r11]);\n    i0.ɵɵproperty(\"disabled\", ctx_r3.listFormItem.CIsLock);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", item_r11, \" \");\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_9_div_38_label_4_nb_checkbox_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-checkbox\", 46);\n    i0.ɵɵtwoWayListener(\"checkedChange\", function DetailContentManagementSalesAccountComponent_ng_container_9_div_38_label_4_nb_checkbox_1_Template_nb_checkbox_checkedChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r12);\n      const remark_r13 = i0.ɵɵnextContext().$implicit;\n      const formItemReq_r2 = i0.ɵɵnextContext(2).$implicit;\n      i0.ɵɵtwoWayBindingSet(formItemReq_r2.selectedRemarkType[remark_r13], $event) || (formItemReq_r2.selectedRemarkType[remark_r13] = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"checkedChange\", function DetailContentManagementSalesAccountComponent_ng_container_9_div_38_label_4_nb_checkbox_1_Template_nb_checkbox_checkedChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r12);\n      const remark_r13 = i0.ɵɵnextContext().$implicit;\n      const formItemReq_r2 = i0.ɵɵnextContext(2).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.onCheckboxRemarkChange($event, remark_r13, formItemReq_r2));\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const remark_r13 = i0.ɵɵnextContext().$implicit;\n    const formItemReq_r2 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵtwoWayProperty(\"checked\", formItemReq_r2.selectedRemarkType[remark_r13]);\n    i0.ɵɵproperty(\"disabled\", ctx_r3.listFormItem.CIsLock);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", remark_r13, \" \");\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_9_div_38_label_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"label\", 44);\n    i0.ɵɵtemplate(1, DetailContentManagementSalesAccountComponent_ng_container_9_div_38_label_4_nb_checkbox_1_Template, 2, 3, \"nb-checkbox\", 47);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const formItemReq_r2 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", formItemReq_r2.selectedRemarkType);\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_9_div_38_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 4)(1, \"div\", 11)(2, \"label\", 28);\n    i0.ɵɵtext(3, \"\\u5099\\u8A3B\\u9078\\u9805\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, DetailContentManagementSalesAccountComponent_ng_container_9_div_38_label_4_Template, 2, 1, \"label\", 30);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.CRemarkTypeOptions);\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 10)(2, \"div\", 11)(3, \"label\", 12);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 13)(6, \"input\", 14);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function DetailContentManagementSalesAccountComponent_ng_container_9_Template_input_ngModelChange_6_listener($event) {\n      const formItemReq_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      i0.ɵɵtwoWayBindingSet(formItemReq_r2.CItemName, $event) || (formItemReq_r2.CItemName = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(7, \"div\", 15)(8, \"div\", 16)(9, \"button\", 17);\n    i0.ɵɵlistener(\"click\", function DetailContentManagementSalesAccountComponent_ng_container_9_Template_button_click_9_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const inputFile_r3 = i0.ɵɵreference(13);\n      return i0.ɵɵresetView(inputFile_r3.click());\n    });\n    i0.ɵɵtext(10, \"\\u4E0A\\u50B3\\u6982\\u5FF5\\u8A2D\\u8A08\\u5716\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(11, \"div\", 18)(12, \"input\", 19, 0);\n    i0.ɵɵlistener(\"change\", function DetailContentManagementSalesAccountComponent_ng_container_9_Template_input_change_12_listener($event) {\n      const formItemReq_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.detectFiles($event, formItemReq_r2));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"div\", 20)(15, \"table\", 21)(16, \"tbody\");\n    i0.ɵɵtemplate(17, DetailContentManagementSalesAccountComponent_ng_container_9_tr_17_Template, 11, 2, \"tr\", 6);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵtemplate(18, DetailContentManagementSalesAccountComponent_ng_container_9_div_18_Template, 9, 2, \"div\", 22);\n    i0.ɵɵelementStart(19, \"div\", 10)(20, \"div\", 11)(21, \"label\", 23);\n    i0.ɵɵtext(22, \"\\u5FC5\\u586B\\u6578\\u91CF\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"input\", 24);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function DetailContentManagementSalesAccountComponent_ng_container_9_Template_input_ngModelChange_23_listener($event) {\n      const formItemReq_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      i0.ɵɵtwoWayBindingSet(formItemReq_r2.CRequireAnswer, $event) || (formItemReq_r2.CRequireAnswer = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelement(24, \"div\", 15);\n    i0.ɵɵelementStart(25, \"div\", 10)(26, \"div\", 11)(27, \"label\", 25);\n    i0.ɵɵtext(28, \"\\u524D\\u53F0UI\\u985E\\u578B\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(29, \"nb-select\", 26);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function DetailContentManagementSalesAccountComponent_ng_container_9_Template_nb_select_ngModelChange_29_listener($event) {\n      const formItemReq_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      i0.ɵɵtwoWayBindingSet(formItemReq_r2.selectedCUiType, $event) || (formItemReq_r2.selectedCUiType = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"selectedChange\", function DetailContentManagementSalesAccountComponent_ng_container_9_Template_nb_select_selectedChange_29_listener() {\n      const formItemReq_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.changeSelectCUiType(formItemReq_r2));\n    });\n    i0.ɵɵtemplate(30, DetailContentManagementSalesAccountComponent_ng_container_9_nb_option_30_Template, 2, 2, \"nb-option\", 27);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelement(31, \"div\", 15);\n    i0.ɵɵelementStart(32, \"div\", 4)(33, \"div\", 11)(34, \"label\", 28);\n    i0.ɵɵtext(35, \"\\u9069\\u7528\\u6236\\u5225 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(36, DetailContentManagementSalesAccountComponent_ng_container_9_label_36_Template, 3, 2, \"label\", 29)(37, DetailContentManagementSalesAccountComponent_ng_container_9_label_37_Template, 3, 3, \"label\", 30);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(38, DetailContentManagementSalesAccountComponent_ng_container_9_div_38_Template, 5, 1, \"div\", 31);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const formItemReq_r2 = ctx.$implicit;\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate3(\" \", formItemReq_r2.CName, \"-\", formItemReq_r2.CPart, \"-\", formItemReq_r2.CLocation, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtwoWayProperty(\"ngModel\", formItemReq_r2.CItemName);\n    i0.ɵɵproperty(\"disabled\", ctx_r3.listFormItem.CIsLock);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"disabled\", ctx_r3.listFormItem.CIsLock);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", formItemReq_r2.listPictures.length ? \"\" : \"hidden\");\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngForOf\", formItemReq_r2.listPictures);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", formItemReq_r2.CDesignFileUrl);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtwoWayProperty(\"ngModel\", formItemReq_r2.CRequireAnswer);\n    i0.ɵɵproperty(\"disabled\", formItemReq_r2.selectedCUiType.value === 3 || ctx_r3.listFormItem.CIsLock);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtwoWayProperty(\"ngModel\", formItemReq_r2.selectedCUiType);\n    i0.ɵɵproperty(\"disabled\", ctx_r3.listFormItem.CIsLock);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.CUiTypeOptions);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.houseHoldList.length);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.houseHoldList);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", formItemReq_r2.selectedCUiType.value === 3);\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_button_13_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r14 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 48);\n    i0.ɵɵlistener(\"click\", function DetailContentManagementSalesAccountComponent_button_13_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.onSubmit());\n    });\n    i0.ɵɵtext(1, \" \\u5132\\u5B58 \");\n    i0.ɵɵelementEnd();\n  }\n}\nexport class DetailContentManagementSalesAccountComponent extends BaseComponent {\n  constructor(_allow, route, message, _formItemService, _regularNoticeFileService, _utilityService, valid, location, _materialService, _eventService) {\n    super(_allow);\n    this._allow = _allow;\n    this.route = route;\n    this.message = message;\n    this._formItemService = _formItemService;\n    this._regularNoticeFileService = _regularNoticeFileService;\n    this._utilityService = _utilityService;\n    this.valid = valid;\n    this.location = location;\n    this._materialService = _materialService;\n    this._eventService = _eventService;\n    this.typeContentManagementSalesAccount = {\n      CFormType: 2,\n      CNoticeType: 2\n    };\n    this.CUiTypeOptions = [{\n      value: 1,\n      label: '建材選色'\n    }, {\n      value: 2,\n      label: '群組選樣_選色'\n    }, {\n      value: 3,\n      label: '建材選樣'\n    }];\n    this.CRemarkTypeOptions = [\"正常\", \"留料\"];\n    this.selectedItems = {};\n    this.selectedRemarkType = {};\n    this.isNew = true;\n  }\n  ngOnInit() {\n    this.route.paramMap.subscribe(params => {\n      if (params) {\n        const idParam = params.get('id');\n        const id = idParam ? +idParam : 0;\n        this.buildCaseId = id;\n        if (this.buildCaseId) {\n          this.getListRegularNoticeFileHouseHold();\n        }\n      }\n    });\n  }\n  getItemByValue(value, options) {\n    for (const item of options) {\n      if (item.value === value) {\n        return item;\n      }\n    }\n    return null;\n  }\n  detectFiles(event, formItemReq_) {\n    const file = event.target.files[0];\n    if (file) {\n      let reader = new FileReader();\n      reader.readAsDataURL(file);\n      reader.onload = () => {\n        let base64Str = reader.result;\n        if (!base64Str) {\n          return;\n        }\n        if (formItemReq_.listPictures.length > 0) {\n          formItemReq_.listPictures[0] = {\n            id: new Date().getTime(),\n            name: file.name.split('.')[0],\n            data: base64Str,\n            extension: this._utilityService.getFileExtension(file.name),\n            CFile: file\n          };\n        } else {\n          formItemReq_.listPictures.push({\n            id: new Date().getTime(),\n            name: file.name.split('.')[0],\n            data: base64Str,\n            extension: this._utilityService.getFileExtension(file.name),\n            CFile: file\n          });\n        }\n        event.target.value = null;\n      };\n    }\n  }\n  removeImage(pictureId, formItemReq_) {\n    if (formItemReq_.listPictures.length) {\n      formItemReq_.listPictures = formItemReq_.listPictures.filter(x => x.id != pictureId);\n    }\n  }\n  renameFile(event, index, formItemReq_) {\n    var blob = formItemReq_.listPictures[index].CFile.slice(0, formItemReq_.listPictures[index].CFile.size, formItemReq_.listPictures[index].CFile.type);\n    var newFile = new File([blob], `${event.target.value + '.' + formItemReq_.listPictures[index].extension}`, {\n      type: formItemReq_.listPictures[index].CFile.type\n    });\n    formItemReq_.listPictures[index].CFile = newFile;\n  }\n  onCheckAllChange(checked, formItemReq_) {\n    formItemReq_.allSelected = checked;\n    this.houseHoldList.forEach(item => {\n      formItemReq_.selectedItems[item] = checked;\n    });\n  }\n  onCheckboxHouseHoldListChange(checked, item, formItemReq_) {\n    if (checked) {\n      formItemReq_.selectedItems[item] = checked;\n      formItemReq_.allSelected = this.houseHoldList.every(item => formItemReq_.selectedItems[item] == checked);\n    } else {\n      formItemReq_.allSelected = false;\n    }\n  }\n  onCheckboxRemarkChange(checked, item, formItemReq_) {\n    formItemReq_.selectedRemarkType[item] = checked;\n  }\n  createRemarkObject(CRemarkTypeOptions, CRemarkType) {\n    const remarkObject = {};\n    for (const option of CRemarkTypeOptions) {\n      remarkObject[option] = false;\n    }\n    const remarkTypes = CRemarkType.split('-');\n    for (const type of remarkTypes) {\n      if (CRemarkTypeOptions.includes(type)) {\n        remarkObject[type] = true;\n      }\n    }\n    return remarkObject;\n  }\n  mergeItems(items) {\n    const map = new Map();\n    items.forEach(item => {\n      const key = `${item.CLocation}_${item.CName}_${item.CPart}`;\n      if (map.has(key)) {\n        const existing = map.get(key);\n        existing.count += 1;\n      } else {\n        map.set(key, {\n          item: {\n            ...item\n          },\n          count: 1\n        });\n      }\n    });\n    return Array.from(map.values()).map(({\n      item,\n      count\n    }) => ({\n      ...item,\n      CTotalAnswer: count\n    }));\n  }\n  getMaterialList() {\n    this._materialService.apiMaterialGetMaterialListPost$Json({\n      body: {\n        CBuildCaseId: this.buildCaseId,\n        CPagi: false\n      }\n    }).pipe(tap(res => {\n      if (res.Entries && res.StatusCode == 0) {\n        this.houseHoldList.forEach(item => this.selectedItems[item] = false);\n        this.CRemarkTypeOptions.forEach(item => this.selectedRemarkType[item] = false);\n        this.arrListFormItemReq = res.Entries.map(o => {\n          return {\n            CDesignFileUrl: null,\n            CFormItemHouseHold: null,\n            CFormId: null,\n            CLocation: o.CLocation,\n            CName: o.CName,\n            CPart: o.CPart,\n            CItemName: `${o.CName}-${o.CPart}-${o.CLocation}`,\n            CRemarkType: null,\n            CTotalAnswer: 0,\n            CRequireAnswer: undefined,\n            CUiType: 0,\n            selectedItems: {},\n            selectedRemarkType: this.selectedRemarkType,\n            allSelected: false,\n            listPictures: [],\n            selectedCUiType: this.CUiTypeOptions[0]\n          };\n        });\n        this.arrListFormItemReq = [...this.mergeItems(this.arrListFormItemReq)];\n      }\n    })).subscribe();\n  }\n  getListFormItem() {\n    this._formItemService.apiFormItemGetListFormItemPost$Json({\n      body: {\n        CBuildCaseId: this.buildCaseId,\n        CFormType: this.typeContentManagementSalesAccount.CFormType,\n        CIsPaging: false\n      }\n    }).pipe(tap(res => {\n      if (res.Entries && res.StatusCode == 0) {\n        this.listFormItem = res.Entries;\n        this.isNew = res.Entries.formItems ? false : true;\n        if (res.Entries.formItems) {\n          this.houseHoldList.forEach(item => this.selectedItems[item] = false);\n          this.CRemarkTypeOptions.forEach(item => this.selectedRemarkType[item] = false);\n          this.arrListFormItemReq = res.Entries.formItems.map(o => {\n            return {\n              CFormId: this.listFormItem.CFormId,\n              CDesignFileUrl: o.CDesignFileUrl,\n              CFile: o.CFile,\n              CFormItemHouseHold: o.CFormItemHouseHold,\n              CFormItemId: o.CFormItemId,\n              CLocation: o.CLocation,\n              CName: o.CName,\n              CPart: o.CPart,\n              CItemName: o.CItemName ? o.CItemName : `${o.CName}-${o.CPart}-${o.CLocation}`,\n              CRemarkType: o.CRemarkType,\n              CTotalAnswer: o.CTotalAnswer,\n              CRequireAnswer: o.CUiType === 3 ? 1 : o.CRequireAnswer,\n              CUiType: o.CUiType,\n              selectedItems: o.tblFormItemHouseholds.length ? this.createArrayObjectFromArray(this.houseHoldList, o.tblFormItemHouseholds) : {\n                ...this.selectedItems\n              },\n              selectedRemarkType: o.CRemarkType ? this.createRemarkObject(this.CRemarkTypeOptions, o.CRemarkType) : {\n                ...this.selectedRemarkType\n              },\n              allSelected: o.tblFormItemHouseholds.length === this.houseHoldList.length,\n              listPictures: [],\n              selectedCUiType: o.CUiType ? this.getItemByValue(o.CUiType, this.CUiTypeOptions) : this.CUiTypeOptions[0]\n            };\n          });\n        } else {\n          this.getMaterialList();\n        }\n      }\n    })).subscribe();\n  }\n  changeSelectCUiType(formItemReq) {\n    if (formItemReq.selectedCUiType && formItemReq.selectedCUiType.value === 3) {\n      formItemReq.CRequireAnswer = 1;\n    }\n  }\n  getHouseHoldListByNoticeType(data) {\n    for (let item of data) {\n      if (item.CNoticeType === this.typeContentManagementSalesAccount.CNoticeType) {\n        return item.CHouseHoldList;\n      }\n    }\n    return [];\n  }\n  getKeysWithTrueValue(obj) {\n    return Object.keys(obj).filter(key => obj[key]);\n  }\n  getKeysWithTrueValueJoined(obj) {\n    return Object.keys(obj).filter(key => obj[key]).join('-');\n  }\n  getCRemarkType(selectedCUiType, selectedRemarkType) {\n    if (selectedCUiType && selectedCUiType.value == 3) {\n      return this.getKeysWithTrueValueJoined(selectedRemarkType);\n    }\n  }\n  getStringAfterComma(inputString) {\n    const parts = inputString.split(',');\n    if (parts.length > 1) {\n      return parts[1];\n    } else return \"\";\n  }\n  formatFile(listPictures) {\n    if (listPictures && listPictures.length > 0) {\n      return {\n        Base64String: this.getStringAfterComma(listPictures[0].data) || null,\n        FileExtension: listPictures[0].extension || null,\n        FileName: listPictures[0].CFile.name || listPictures[0].name || null\n      };\n    } else return undefined;\n  }\n  validation() {\n    this.valid.clear();\n    let hasInvalidCUiType = false;\n    let hasInvalidCRequireAnswer = false;\n    let hasInvalidItemName = false;\n    for (const item of this.saveListFormItemReq) {\n      if (!hasInvalidCUiType && !item.CUiType) {\n        hasInvalidCUiType = true;\n      }\n      if (!hasInvalidCRequireAnswer && !item.CRequireAnswer) {\n        hasInvalidCRequireAnswer = true;\n      }\n      if (item.CTotalAnswer && item.CRequireAnswer) {\n        if (item.CRequireAnswer > item.CTotalAnswer || item.CRequireAnswer < 0) {\n          this.valid.addErrorMessage('[必填數量]' + ' <= ' + item.CTotalAnswer + ` (${item.CItemName}) `);\n        }\n      }\n      if (!hasInvalidItemName && !item.CItemName) {\n        hasInvalidItemName = true;\n      }\n    }\n    if (hasInvalidCUiType) {\n      this.valid.addErrorMessage('[前台UI類型]' + ' 必填');\n    }\n    if (hasInvalidCRequireAnswer) {\n      this.valid.addErrorMessage('[必填數量]' + ' 必填且>0');\n    }\n    if (hasInvalidItemName) {\n      this.valid.addErrorMessage('[廚房-廚具-櫃體]' + ' 必填');\n    }\n  }\n  onSubmit() {\n    this.saveListFormItemReq = this.arrListFormItemReq.map(e => {\n      return {\n        CDesignFileUrl: e.CDesignFileUrl ? e.CDesignFileUrl : null,\n        CFile: e.listPictures ? this.formatFile(e.listPictures) : undefined,\n        CFormItemHouseHold: this.getKeysWithTrueValue(e.selectedItems),\n        CFormItemId: e.CFormItemId ? e.CFormItemId : null,\n        CFormID: this.isNew ? null : this.listFormItem.CFormId,\n        CName: e.CName,\n        CPart: e.CPart,\n        CLocation: e.CLocation,\n        CItemName: e.CItemName,\n        //? e.CItemName : `${e.CName}-${e.CPart}-${e.CLocation}`,\n        CRemarkType: e.selectedCUiType.value !== 3 ? null : this.getCRemarkType(e.selectedCUiType, e.selectedRemarkType) || null,\n        CTotalAnswer: e.CTotalAnswer,\n        CRequireAnswer: e.CRequireAnswer,\n        CUiType: e.selectedCUiType.value\n      };\n    });\n    this.validation();\n    if (this.valid.errorMessages.length > 0) {\n      this.message.showErrorMSGs(this.valid.errorMessages);\n      return;\n    }\n    if (this.isNew) {\n      this.createListFormItem();\n    } else {\n      this.saveListFormItem();\n    }\n  }\n  saveListFormItem() {\n    this._formItemService.apiFormItemSaveListFormItemPost$Json({\n      body: this.saveListFormItemReq\n    }).subscribe(res => {\n      if (res.StatusCode == 0) {\n        this.message.showSucessMSG(\"執行成功\");\n        // this.getListFormItem()\n        this.goBack();\n      }\n    });\n  }\n  createListFormItem() {\n    this.creatListFormItem = {\n      CBuildCaseId: this.buildCaseId,\n      CFormItem: this.saveListFormItemReq || null,\n      CFormType: this.typeContentManagementSalesAccount.CFormType\n    };\n    this._formItemService.apiFormItemCreateListFormItemPost$Json({\n      body: this.creatListFormItem\n    }).subscribe(res => {\n      if (res.StatusCode == 0) {\n        this.message.showSucessMSG(\"執行成功\");\n        // this.getListFormItem()\n        this.goBack();\n      }\n    });\n  }\n  createArrayObjectFromArray(a, b) {\n    const c = {};\n    for (const item of a) {\n      const matchingItem = b.find(bItem => bItem.CHousehold === item && bItem.CIsSelect);\n      c[item] = !!matchingItem;\n    }\n    return c;\n  } //[\"House1\", \"House2\", \"House3\"] => [{CHousehold: \"House1\", CIsSelect: true,... }, ... ]\n  getListRegularNoticeFileHouseHold() {\n    this._regularNoticeFileService.apiRegularNoticeFileGetListRegularNoticeFileHouseHoldPost$Json({\n      body: this.buildCaseId\n    }).pipe(tap(res => {\n      if (res.Entries && res.StatusCode == 0) {\n        this.houseHoldList = this.getHouseHoldListByNoticeType(res.Entries);\n        this.getListFormItem();\n      }\n    })).subscribe();\n  }\n  goBack() {\n    this._eventService.push({\n      action: \"GET_BUILDCASE\" /* EEvent.GET_BUILDCASE */,\n      payload: this.buildCaseId\n    });\n    this.location.back();\n  }\n  static {\n    this.ɵfac = function DetailContentManagementSalesAccountComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || DetailContentManagementSalesAccountComponent)(i0.ɵɵdirectiveInject(i1.AllowHelper), i0.ɵɵdirectiveInject(i2.ActivatedRoute), i0.ɵɵdirectiveInject(i3.MessageService), i0.ɵɵdirectiveInject(i4.FormItemService), i0.ɵɵdirectiveInject(i4.RegularNoticeFileService), i0.ɵɵdirectiveInject(i5.UtilityService), i0.ɵɵdirectiveInject(i6.ValidationHelper), i0.ɵɵdirectiveInject(i7.Location), i0.ɵɵdirectiveInject(i4.MaterialService), i0.ɵɵdirectiveInject(i8.EventService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: DetailContentManagementSalesAccountComponent,\n      selectors: [[\"ngx-detail-content-management-sales-account\"]],\n      standalone: true,\n      features: [i0.ɵɵInheritDefinitionFeature, i0.ɵɵStandaloneFeature],\n      decls: 14,\n      vars: 2,\n      consts: [[\"inputFile\", \"\"], [\"accent\", \"success\"], [1, \"font-bold\", \"text-[#818181]\"], [1, \"d-flex\", \"flex-wrap\"], [1, \"col-md-12\"], [1, \"font-bold\", \"text-lg\", \"px-3\", \"pb-3\"], [4, \"ngFor\", \"ngForOf\"], [1, \"d-flex\", \"justify-content-center\"], [1, \"btn\", \"btn-secondary\", \"mx-2\", 3, \"click\"], [\"class\", \"btn btn-info\", 3, \"click\", 4, \"ngIf\"], [1, \"col-md-9\"], [1, \"form-group\", \"d-flex\", \"align-items-center\", \"w-full\"], [\"for\", \"CItemName\", 1, \"label\", \"col-4\", \"text-base\"], [1, \"input-group\", \"items-center\", \"w-full\", \"col-8\", \"px-0\"], [\"type\", \"text\", \"nbInput\", \"\", \"placeholder\", \"\\u5EDA\\u623F\", 1, \"w-full\", \"col-12\", 3, \"ngModelChange\", \"ngModel\", \"disabled\"], [1, \"col-md-3\"], [1, \"d-flex\", \"justify-content-end\", \"w-full\"], [1, \"btn\", \"btn-info\", 3, \"click\", \"disabled\"], [1, \"col-md-12\", 3, \"ngClass\"], [\"type\", \"file\", \"accept\", \"image/png, image/gif, image/jpeg\", 1, \"hidden\", 3, \"change\"], [1, \"mt-3\", \"w-full\", \"flex\", \"flex-col\"], [1, \"table\", \"table-striped\", \"border\", 2, \"background-color\", \"#f3f3f3\"], [\"class\", \"col-md-12 text-center\", 3, \"ngClass\", 4, \"ngIf\"], [\"for\", \"cRequireAnswer\", 1, \"label\", \"col-4\"], [\"type\", \"number\", \"nbInput\", \"\", \"placeholder\", \"\\u5FC5\\u586B\\u6578\\u91CF\", 1, \"col-8\", 3, \"ngModelChange\", \"ngModel\", \"disabled\"], [\"for\", \"buildingName\", 1, \"label\", \"col-4\"], [\"placeholder\", \"\\u5EFA\\u6848\", 1, \"col-8\", \"px-0\", 3, \"ngModelChange\", \"selectedChange\", \"ngModel\", \"disabled\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"for\", \"buildingName\", 1, \"label\", \"col-3\"], [\"class\", \"mr-2\", 4, \"ngIf\"], [\"class\", \"mr-2\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"col-md-12\", 4, \"ngIf\"], [1, \"align-middle\"], [\"nbInput\", \"\", \"type\", \"text\", 1, \"w-100\", \"p-2\", \"text-[13px]\", 3, \"blur\", \"value\"], [1, \"w-[100px]\", \"h-auto\"], [1, \"fit-size\", 3, \"src\"], [1, \"text-center\", \"w-32\", \"align-middle\"], [1, \"btn\", \"btn-outline-danger\", \"btn-sm\", \"m-1\", 3, \"click\"], [1, \"col-md-12\", \"text-center\", 3, \"ngClass\"], [1, \"table\", \"table-striped\", \"border\"], [1, \"w-[80px]\", \"h-auto\"], [\"class\", \"w-14 h-14\", 3, \"src\", 4, \"ngIf\"], [1, \"w-14\", \"h-14\", 3, \"src\"], [3, \"value\"], [1, \"mr-2\"], [3, \"checkedChange\", \"checked\", \"disabled\"], [\"value\", \"item\", 3, \"checkedChange\", \"checked\", \"disabled\"], [\"value\", \"item\", 3, \"checked\", \"disabled\", \"checkedChange\", 4, \"ngIf\"], [1, \"btn\", \"btn-info\", 3, \"click\"]],\n      template: function DetailContentManagementSalesAccountComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"nb-card\", 1)(1, \"nb-card-header\");\n          i0.ɵɵelement(2, \"ngx-breadcrumb\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(3, \"nb-card-body\");\n          i0.ɵɵelement(4, \"h1\", 2);\n          i0.ɵɵelementStart(5, \"div\", 3)(6, \"div\", 4)(7, \"h4\", 5);\n          i0.ɵɵtext(8, \"\\u985E\\u578B-\\u7368\\u7ACB\\u9078\\u6A23\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(9, DetailContentManagementSalesAccountComponent_ng_container_9_Template, 39, 17, \"ng-container\", 6);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(10, \"nb-card-footer\", 7)(11, \"button\", 8);\n          i0.ɵɵlistener(\"click\", function DetailContentManagementSalesAccountComponent_Template_button_click_11_listener() {\n            return ctx.goBack();\n          });\n          i0.ɵɵtext(12, \" \\u53D6\\u6D88 \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(13, DetailContentManagementSalesAccountComponent_button_13_Template, 2, 0, \"button\", 9);\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(9);\n          i0.ɵɵproperty(\"ngForOf\", ctx.arrListFormItemReq);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", !ctx.listFormItem.CIsLock);\n        }\n      },\n      dependencies: [CommonModule, i7.NgClass, i7.NgForOf, i7.NgIf, SharedModule, i9.DefaultValueAccessor, i9.NumberValueAccessor, i9.NgControlStatus, i9.NgModel, i10.NbCardComponent, i10.NbCardBodyComponent, i10.NbCardFooterComponent, i10.NbCardHeaderComponent, i10.NbCheckboxComponent, i10.NbInputDirective, i10.NbSelectComponent, i10.NbOptionComponent, i11.BreadcrumbComponent, i12.BaseFilePipe, NbCheckboxModule],\n      styles: [\"/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsImZpbGUiOiJkZXRhaWwtY29udGVudC1tYW5hZ2VtZW50LXNhbGVzLWFjY291bnQuY29tcG9uZW50LnNjc3MifQ== */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvcGFnZXMvc2VsZWN0aW9uLW1hbmFnZW1lbnQvY29udGVudC1tYW5hZ2VtZW50LXNhbGVzLWFjY291bnQvZGV0YWlsLWNvbnRlbnQtbWFuYWdlbWVudC1zYWxlcy1hY2NvdW50L2RldGFpbC1jb250ZW50LW1hbmFnZW1lbnQtc2FsZXMtYWNjb3VudC5jb21wb25lbnQuc2NzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiO0FBQ0EsZ05BQWdOIiwic291cmNlUm9vdCI6IiJ9 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "NbCheckboxModule", "tap", "SharedModule", "BaseComponent", "EEvent", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵlistener", "DetailContentManagementSalesAccountComponent_ng_container_9_tr_17_Template_input_blur_5_listener", "$event", "i_r6", "ɵɵrestoreView", "_r5", "index", "formItemReq_r2", "ɵɵnextContext", "$implicit", "ctx_r3", "ɵɵresetView", "renameFile", "ɵɵelement", "DetailContentManagementSalesAccountComponent_ng_container_9_tr_17_Template_button_click_9_listener", "picture_r7", "removeImage", "id", "ɵɵadvance", "ɵɵproperty", "name", "data", "ɵɵsanitizeUrl", "ɵɵpipeBind1", "CDesignFileUrl", "ɵɵtemplate", "DetailContentManagementSalesAccountComponent_ng_container_9_div_18_img_7_Template", "listPictures", "length", "case_r8", "ɵɵtextInterpolate1", "label", "ɵɵtwoWayListener", "DetailContentManagementSalesAccountComponent_ng_container_9_label_36_Template_nb_checkbox_checkedChange_1_listener", "_r9", "ɵɵtwoWayBindingSet", "allSelected", "onCheckAllChange", "ɵɵtwoWayProperty", "listFormItem", "CIsLock", "DetailContentManagementSalesAccountComponent_ng_container_9_label_37_Template_nb_checkbox_checkedChange_1_listener", "item_r11", "_r10", "selectedItems", "onCheckboxHouseHoldListChange", "DetailContentManagementSalesAccountComponent_ng_container_9_div_38_label_4_nb_checkbox_1_Template_nb_checkbox_checkedChange_0_listener", "_r12", "remark_r13", "selectedRemarkType", "onCheckboxRemarkChange", "DetailContentManagementSalesAccountComponent_ng_container_9_div_38_label_4_nb_checkbox_1_Template", "DetailContentManagementSalesAccountComponent_ng_container_9_div_38_label_4_Template", "CRemarkTypeOptions", "ɵɵelementContainerStart", "DetailContentManagementSalesAccountComponent_ng_container_9_Template_input_ngModelChange_6_listener", "_r1", "CItemName", "DetailContentManagementSalesAccountComponent_ng_container_9_Template_button_click_9_listener", "inputFile_r3", "ɵɵreference", "click", "DetailContentManagementSalesAccountComponent_ng_container_9_Template_input_change_12_listener", "detectFiles", "DetailContentManagementSalesAccountComponent_ng_container_9_tr_17_Template", "DetailContentManagementSalesAccountComponent_ng_container_9_div_18_Template", "DetailContentManagementSalesAccountComponent_ng_container_9_Template_input_ngModelChange_23_listener", "CRequireAnswer", "DetailContentManagementSalesAccountComponent_ng_container_9_Template_nb_select_ngModelChange_29_listener", "selectedCUiType", "DetailContentManagementSalesAccountComponent_ng_container_9_Template_nb_select_selectedChange_29_listener", "changeSelectCUiType", "DetailContentManagementSalesAccountComponent_ng_container_9_nb_option_30_Template", "DetailContentManagementSalesAccountComponent_ng_container_9_label_36_Template", "DetailContentManagementSalesAccountComponent_ng_container_9_label_37_Template", "DetailContentManagementSalesAccountComponent_ng_container_9_div_38_Template", "ɵɵtextInterpolate3", "CName", "<PERSON>art", "CLocation", "value", "CUiTypeOptions", "houseHoldList", "DetailContentManagementSalesAccountComponent_button_13_Template_button_click_0_listener", "_r14", "onSubmit", "DetailContentManagementSalesAccountComponent", "constructor", "_allow", "route", "message", "_formItemService", "_regularNoticeFileService", "_utilityService", "valid", "location", "_materialService", "_eventService", "typeContentManagementSalesAccount", "CFormType", "CNoticeType", "isNew", "ngOnInit", "paramMap", "subscribe", "params", "idParam", "get", "buildCaseId", "getListRegularNoticeFileHouseHold", "getItemByValue", "options", "item", "event", "formItemReq_", "file", "target", "files", "reader", "FileReader", "readAsDataURL", "onload", "base64Str", "result", "Date", "getTime", "split", "extension", "getFileExtension", "CFile", "push", "pictureId", "filter", "x", "blob", "slice", "size", "type", "newFile", "File", "checked", "for<PERSON>ach", "every", "createRemarkObject", "CRemarkType", "remarkObject", "option", "remarkTypes", "includes", "mergeItems", "items", "map", "Map", "key", "has", "existing", "count", "set", "Array", "from", "values", "CTotalAnswer", "getMaterialList", "apiMaterialGetMaterialListPost$Json", "body", "CBuildCaseId", "CPagi", "pipe", "res", "Entries", "StatusCode", "arrListFormItemReq", "o", "CFormItemHouseHold", "CFormId", "undefined", "CUiType", "getListFormItem", "apiFormItemGetListFormItemPost$Json", "CIsPaging", "formItems", "CFormItemId", "tblFormItemHouseholds", "createArrayObjectFromArray", "formItemReq", "getHouseHoldListByNoticeType", "CHouseHoldList", "getKeysWithTrueValue", "obj", "Object", "keys", "getKeysWithTrueValueJoined", "join", "getCRemarkType", "getStringAfterComma", "inputString", "parts", "formatFile", "Base64String", "FileExtension", "FileName", "validation", "clear", "hasInvalidCUiType", "hasInvalidCRequireAnswer", "hasInvalidItemName", "saveListFormItemReq", "addErrorMessage", "e", "CFormID", "errorMessages", "showErrorMSGs", "createListFormItem", "saveListFormItem", "apiFormItemSaveListFormItemPost$Json", "showSucessMSG", "goBack", "creatListFormItem", "CFormItem", "apiFormItemCreateListFormItemPost$Json", "a", "b", "c", "matchingItem", "find", "bItem", "CHousehold", "CIsSelect", "apiRegularNoticeFileGetListRegularNoticeFileHouseHoldPost$Json", "action", "payload", "back", "ɵɵdirectiveInject", "i1", "AllowHelper", "i2", "ActivatedRoute", "i3", "MessageService", "i4", "FormItemService", "RegularNoticeFileService", "i5", "UtilityService", "i6", "ValidationHelper", "i7", "Location", "MaterialService", "i8", "EventService", "selectors", "standalone", "features", "ɵɵInheritDefinitionFeature", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "DetailContentManagementSalesAccountComponent_Template", "rf", "ctx", "DetailContentManagementSalesAccountComponent_ng_container_9_Template", "DetailContentManagementSalesAccountComponent_Template_button_click_11_listener", "DetailContentManagementSalesAccountComponent_button_13_Template", "Ng<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "i9", "DefaultValueAccessor", "NumberValueAccessor", "NgControlStatus", "NgModel", "i10", "NbCardComponent", "NbCardBodyComponent", "NbCardFooterComponent", "NbCardHeaderComponent", "NbCheckboxComponent", "NbInputDirective", "NbSelectComponent", "NbOptionComponent", "i11", "BreadcrumbComponent", "i12", "BaseFilePipe", "styles"], "sources": ["C:\\Users\\<USER>\\Documents\\jeansalechange\\adminFront\\src\\app\\pages\\selection-management\\content-management-sales-account\\detail-content-management-sales-account\\detail-content-management-sales-account.component.ts", "C:\\Users\\<USER>\\Documents\\jeansalechange\\adminFront\\src\\app\\pages\\selection-management\\content-management-sales-account\\detail-content-management-sales-account\\detail-content-management-sales-account.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { CommonModule, Location } from '@angular/common';\r\nimport { NbCheckboxModule } from '@nebular/theme';\r\nimport { AllowHelper } from 'src/app/shared/helper/allowHelper';\r\nimport { FormItemService, MaterialService, RegularNoticeFileService } from 'src/services/api/services';\r\nimport { ActivatedRoute } from '@angular/router';\r\nimport { MessageService } from 'src/app/shared/services/message.service';\r\nimport { tap } from 'rxjs';\r\nimport { CreateListFormItem, FileViewModel, GetListFormItemRes, SaveListFormItemReq } from 'src/services/api/models';\r\nimport { BaseFilePipe } from 'src/app/@theme/pipes/base-file.pipe';\r\nimport { UtilityService } from 'src/app/shared/services/utility.service';\r\nimport { ValidationHelper } from 'src/app/shared/helper/validationHelper';\r\nimport { SharedModule } from 'src/app/pages/components/shared.module';\r\nimport { BaseComponent } from 'src/app/pages/components/base/baseComponent';\r\nimport { EventService, EEvent } from 'src/app/shared/services/event.service';\r\n\r\n\r\nexport interface ExtendedSaveListFormItemReq {\r\n  CDesignFileUrl?: string | null;\r\n  CFile?: FileViewModel,\r\n  CFormItemHouseHold?: Array<string> | null;\r\n  CFormItemId?: number;\r\n  CItemName?: string;\r\n  CFormID?: number;\r\n  CPart?: string | null;\r\n  CName?: string | null;\r\n  CLocation?: string | null;\r\n  CRemarkType?: string | null;\r\n  CTotalAnswer?: number;\r\n  CRequireAnswer?: number;\r\n  CUiType?: number;\r\n  selectedCUiType: any | null;\r\n  selectedItems: { [key: string]: boolean }\r\n  selectedRemarkType?: { [key: string]: boolean }\r\n  allSelected: boolean,\r\n  listPictures: any[]\r\n}\r\n\r\n@Component({\r\n  selector: 'ngx-detail-content-management-sales-account',\r\n  templateUrl: './detail-content-management-sales-account.component.html',\r\n  styleUrls: ['./detail-content-management-sales-account.component.scss'],\r\n  standalone: true,\r\n  imports: [CommonModule, SharedModule, NbCheckboxModule, BaseFilePipe],\r\n})\r\n\r\nexport class DetailContentManagementSalesAccountComponent extends BaseComponent implements OnInit {\r\n  constructor(\r\n    private _allow: AllowHelper,\r\n    private route: ActivatedRoute,\r\n    private message: MessageService,\r\n    private _formItemService: FormItemService,\r\n    private _regularNoticeFileService: RegularNoticeFileService,\r\n    private _utilityService: UtilityService,\r\n    private valid: ValidationHelper,\r\n    private location: Location,\r\n    private _materialService: MaterialService,\r\n    private _eventService: EventService\r\n  ) {\r\n    super(_allow)\r\n  }\r\n\r\n  typeContentManagementSalesAccount = {\r\n    CFormType: 2,\r\n    CNoticeType: 2\r\n  }\r\n\r\n  CUiTypeOptions: any[] = [\r\n    {\r\n      value: 1, label: '建材選色'\r\n    },\r\n    {\r\n      value: 2, label: '群組選樣_選色'\r\n    }, {\r\n      value: 3, label: '建材選樣'\r\n    }\r\n  ]\r\n  CRemarkTypeOptions = [\"正常\", \"留料\"]\r\n  buildCaseId: number\r\n\r\n  override ngOnInit(): void {\r\n    this.route.paramMap.subscribe(params => {\r\n      if (params) {\r\n        const idParam = params.get('id');\r\n        const id = idParam ? +idParam : 0;\r\n        this.buildCaseId = id\r\n        if (this.buildCaseId) {\r\n          this.getListRegularNoticeFileHouseHold()\r\n        }\r\n      }\r\n    })\r\n  }\r\n\r\n\r\n  getItemByValue(value: any, options: any[]) {\r\n    for (const item of options) {\r\n      if (item.value === value) {\r\n        return item;\r\n      }\r\n    }\r\n    return null;\r\n  }\r\n\r\n\r\n  selectedItems: { [key: string]: boolean } = {};\r\n  selectedRemarkType: { [key: string]: boolean } = {};\r\n\r\n\r\n\r\n  detectFiles(event: any, formItemReq_: any) {\r\n    const file = event.target.files[0]\r\n    if (file) {\r\n      let reader = new FileReader();\r\n      reader.readAsDataURL(file);\r\n      reader.onload = () => {\r\n        let base64Str: string = reader.result as string;\r\n        if (!base64Str) {\r\n          return;\r\n        }\r\n        if (formItemReq_.listPictures.length > 0) {\r\n          formItemReq_.listPictures[0] = {\r\n            id: new Date().getTime(),\r\n            name: file.name.split('.')[0],\r\n            data: base64Str,\r\n            extension: this._utilityService.getFileExtension(file.name),\r\n            CFile: file\r\n          };\r\n        } else {\r\n          formItemReq_.listPictures.push({\r\n            id: new Date().getTime(),\r\n            name: file.name.split('.')[0],\r\n            data: base64Str,\r\n            extension: this._utilityService.getFileExtension(file.name),\r\n            CFile: file\r\n          });\r\n        }\r\n        event.target.value = null;\r\n      };\r\n    }\r\n  }\r\n\r\n  removeImage(pictureId: number, formItemReq_: any) {\r\n    if (formItemReq_.listPictures.length) {\r\n      formItemReq_.listPictures = formItemReq_.listPictures.filter((x: any) => x.id != pictureId)\r\n    }\r\n  }\r\n\r\n  renameFile(event: any, index: number, formItemReq_: any) {\r\n    var blob = formItemReq_.listPictures[index].CFile.slice(0, formItemReq_.listPictures[index].CFile.size, formItemReq_.listPictures[index].CFile.type);\r\n    var newFile = new File([blob], `${event.target.value + '.' + formItemReq_.listPictures[index].extension}`, { type: formItemReq_.listPictures[index].CFile.type });\r\n    formItemReq_.listPictures[index].CFile = newFile\r\n  }\r\n\r\n\r\n  onCheckAllChange(checked: boolean, formItemReq_: any) {\r\n    formItemReq_.allSelected = checked;\r\n    this.houseHoldList.forEach(item => {\r\n      formItemReq_.selectedItems[item] = checked;\r\n    });\r\n  }\r\n\r\n  onCheckboxHouseHoldListChange(checked: boolean, item: string, formItemReq_: any) {\r\n    if (checked) {\r\n      formItemReq_.selectedItems[item] = checked;\r\n      formItemReq_.allSelected = this.houseHoldList.every(item => formItemReq_.selectedItems[item] == checked);\r\n    } else {\r\n      formItemReq_.allSelected = false\r\n    }\r\n  }\r\n\r\n\r\n\r\n  onCheckboxRemarkChange(checked: boolean, item: string, formItemReq_: any) {\r\n    formItemReq_.selectedRemarkType[item] = checked;\r\n  }\r\n\r\n  createRemarkObject(CRemarkTypeOptions: string[], CRemarkType: string): { [key: string]: boolean } {\r\n    const remarkObject: { [key: string]: boolean } = {};\r\n    for (const option of CRemarkTypeOptions) {\r\n      remarkObject[option] = false;\r\n    }\r\n    const remarkTypes = CRemarkType.split('-');\r\n    for (const type of remarkTypes) {\r\n      if (CRemarkTypeOptions.includes(type)) {\r\n        remarkObject[type] = true;\r\n      }\r\n    }\r\n    return remarkObject;\r\n  }\r\n\r\n  mergeItems(items: ExtendedSaveListFormItemReq[]): ExtendedSaveListFormItemReq[] {\r\n    const map = new Map<string, { item: ExtendedSaveListFormItemReq, count: number }>();\r\n\r\n    items.forEach(item => {\r\n      const key = `${item.CLocation}_${item.CName}_${item.CPart}`;\r\n      if (map.has(key)) {\r\n        const existing = map.get(key)!;\r\n        existing.count += 1;\r\n      } else {\r\n        map.set(key, { item: { ...item }, count: 1 });\r\n      }\r\n    });\r\n\r\n    return Array.from(map.values()).map(({ item, count }) => ({\r\n      ...item,\r\n      CTotalAnswer: count\r\n    }));\r\n  }\r\n\r\n\r\n  getMaterialList() { // call when create\r\n    this._materialService.apiMaterialGetMaterialListPost$Json({\r\n      body: {\r\n        CBuildCaseId: this.buildCaseId,\r\n        CPagi: false\r\n      }\r\n    }).pipe(\r\n      tap(res => {\r\n        if (res.Entries && res.StatusCode == 0) {\r\n\r\n          this.houseHoldList.forEach(item => this.selectedItems[item] = false)\r\n\r\n          this.CRemarkTypeOptions.forEach(item => this.selectedRemarkType[item] = false)\r\n\r\n          this.arrListFormItemReq = res.Entries.map((o: any) => {\r\n            return {\r\n              CDesignFileUrl: null,\r\n              CFormItemHouseHold: null,\r\n              CFormId: null,\r\n              CLocation: o.CLocation,\r\n              CName: o.CName,\r\n              CPart: o.CPart,\r\n              CItemName: `${o.CName}-${o.CPart}-${o.CLocation}`,\r\n              CRemarkType: null,\r\n              CTotalAnswer: 0,\r\n              CRequireAnswer: undefined,\r\n              CUiType: 0,\r\n              selectedItems: {},\r\n              selectedRemarkType: this.selectedRemarkType,\r\n              allSelected: false,\r\n              listPictures: [],\r\n              selectedCUiType: this.CUiTypeOptions[0]\r\n            }\r\n          })\r\n          this.arrListFormItemReq = [...this.mergeItems(this.arrListFormItemReq)]\r\n        }\r\n      })\r\n    ).subscribe();\r\n  }\r\n\r\n  listFormItem: GetListFormItemRes\r\n  isNew: boolean = true\r\n\r\n  getListFormItem() {\r\n    this._formItemService.apiFormItemGetListFormItemPost$Json({\r\n      body: {\r\n        CBuildCaseId: this.buildCaseId,\r\n        CFormType: this.typeContentManagementSalesAccount.CFormType,\r\n        CIsPaging: false\r\n      }\r\n    }).pipe(\r\n      tap(res => {\r\n        if (res.Entries && res.StatusCode == 0) {\r\n          this.listFormItem = res.Entries\r\n          this.isNew = res.Entries.formItems ? false : true\r\n          if (res.Entries.formItems) {\r\n\r\n            this.houseHoldList.forEach(item => this.selectedItems[item] = false)\r\n            this.CRemarkTypeOptions.forEach(item => this.selectedRemarkType[item] = false)\r\n\r\n            this.arrListFormItemReq = res.Entries.formItems.map((o: any) => {\r\n              return {\r\n                CFormId: this.listFormItem.CFormId,\r\n                CDesignFileUrl: o.CDesignFileUrl,\r\n                CFile: o.CFile,\r\n                CFormItemHouseHold: o.CFormItemHouseHold,\r\n                CFormItemId: o.CFormItemId,\r\n                CLocation: o.CLocation,\r\n                CName: o.CName,\r\n                CPart: o.CPart,\r\n                CItemName: o.CItemName ? o.CItemName : `${o.CName}-${o.CPart}-${o.CLocation}`,\r\n                CRemarkType: o.CRemarkType,\r\n                CTotalAnswer: o.CTotalAnswer,\r\n                CRequireAnswer: o.CUiType===3 ? 1: o.CRequireAnswer,\r\n                CUiType: o.CUiType,\r\n                selectedItems: o.tblFormItemHouseholds.length ? this.createArrayObjectFromArray(this.houseHoldList, o.tblFormItemHouseholds) : { ...this.selectedItems },\r\n                selectedRemarkType: o.CRemarkType ? this.createRemarkObject(this.CRemarkTypeOptions, o.CRemarkType) : { ...this.selectedRemarkType },\r\n                allSelected: o.tblFormItemHouseholds.length === this.houseHoldList.length,\r\n                listPictures: [],\r\n                selectedCUiType: o.CUiType ? this.getItemByValue(o.CUiType, this.CUiTypeOptions) : this.CUiTypeOptions[0]\r\n              }\r\n            })\r\n          } else {\r\n            this.getMaterialList()\r\n          }\r\n        }\r\n      })\r\n    ).subscribe()\r\n  }\r\n\r\n  changeSelectCUiType(formItemReq: any) {\r\n    if(formItemReq.selectedCUiType && formItemReq.selectedCUiType.value ===3) {\r\n      formItemReq.CRequireAnswer = 1\r\n    }\r\n  }\r\n  getHouseHoldListByNoticeType(data: any[]) {\r\n    for (let item of data) {\r\n      if (item.CNoticeType === this.typeContentManagementSalesAccount.CNoticeType) {\r\n        return item.CHouseHoldList;\r\n      }\r\n    }\r\n    return [];\r\n  }\r\n\r\n  arrListFormItemReq: ExtendedSaveListFormItemReq[]\r\n\r\n  getKeysWithTrueValue(obj: Record<string, boolean>): string[] { // {\"key\": true } => [\"key\"]\r\n    return Object.keys(obj).filter(key => obj[key]);\r\n  }\r\n\r\n  getKeysWithTrueValueJoined(obj: Record<string, boolean>): string {\r\n    return Object.keys(obj)\r\n      .filter(key => obj[key])\r\n      .join('-');\r\n  }\r\n\r\n  getCRemarkType(selectedCUiType: any, selectedRemarkType: any): any {\r\n    if (selectedCUiType && selectedCUiType.value == 3) {\r\n      return this.getKeysWithTrueValueJoined(selectedRemarkType)\r\n    }\r\n  }\r\n\r\n  getStringAfterComma(inputString: string): string {\r\n    const parts = inputString.split(',');\r\n    if (parts.length > 1) {\r\n      return parts[1];\r\n    } else return \"\"\r\n  }\r\n\r\n  formatFile(listPictures: any) {\r\n    if (listPictures && listPictures.length > 0) {\r\n      return {\r\n        Base64String: this.getStringAfterComma(listPictures[0].data) || null,\r\n        FileExtension: listPictures[0].extension || null,\r\n        FileName: listPictures[0].CFile.name || listPictures[0].name || null,\r\n      }\r\n    } else return undefined\r\n\r\n  }\r\n\r\n\r\n  validation() {\r\n    this.valid.clear();\r\n    let hasInvalidCUiType = false;\r\n    let hasInvalidCRequireAnswer = false;\r\n    let hasInvalidItemName = false;\r\n\r\n    for (const item of this.saveListFormItemReq) {\r\n      if (!hasInvalidCUiType && (!item.CUiType)) {\r\n        hasInvalidCUiType = true;\r\n      }\r\n      if (!hasInvalidCRequireAnswer && (!item.CRequireAnswer)) {\r\n        hasInvalidCRequireAnswer = true;\r\n      }\r\n      if (item.CTotalAnswer && item.CRequireAnswer) {\r\n        if (item.CRequireAnswer > item.CTotalAnswer || item.CRequireAnswer < 0) {\r\n          this.valid.addErrorMessage('[必填數量]' + ' <= ' + item.CTotalAnswer + ` (${item.CItemName}) `);\r\n        }\r\n      }\r\n\r\n      if (!hasInvalidItemName && (!item.CItemName)) {\r\n        hasInvalidItemName = true;\r\n      }\r\n    }\r\n    if (hasInvalidCUiType) {\r\n      this.valid.addErrorMessage('[前台UI類型]' + ' 必填');\r\n    }\r\n    if (hasInvalidCRequireAnswer) {\r\n      this.valid.addErrorMessage('[必填數量]' + ' 必填且>0');\r\n    }\r\n    if (hasInvalidItemName) {\r\n      this.valid.addErrorMessage('[廚房-廚具-櫃體]' + ' 必填');\r\n    }\r\n  }\r\n\r\n  saveListFormItemReq: SaveListFormItemReq[]\r\n\r\n  onSubmit() {\r\n    this.saveListFormItemReq = this.arrListFormItemReq.map((e: any) => {\r\n      return {\r\n        CDesignFileUrl: e.CDesignFileUrl ? e.CDesignFileUrl : null,\r\n        CFile: e.listPictures ? this.formatFile(e.listPictures) : undefined,\r\n        CFormItemHouseHold: this.getKeysWithTrueValue(e.selectedItems),\r\n        CFormItemId: e.CFormItemId ? e.CFormItemId : null,\r\n        CFormID: this.isNew ? null : this.listFormItem.CFormId,\r\n        CName: e.CName,\r\n        CPart: e.CPart,\r\n        CLocation: e.CLocation,\r\n        CItemName: e.CItemName, //? e.CItemName : `${e.CName}-${e.CPart}-${e.CLocation}`,\r\n        CRemarkType: e.selectedCUiType.value !== 3 ? null : this.getCRemarkType(e.selectedCUiType, e.selectedRemarkType) || null,\r\n        CTotalAnswer: e.CTotalAnswer,\r\n        CRequireAnswer: e.CRequireAnswer,\r\n        CUiType: e.selectedCUiType.value,\r\n      }\r\n    })\r\n    this.validation()\r\n    if (this.valid.errorMessages.length > 0) {\r\n      this.message.showErrorMSGs(this.valid.errorMessages);\r\n      return\r\n    }\r\n    if (this.isNew) {\r\n      this.createListFormItem()\r\n\r\n    } else {\r\n      this.saveListFormItem()\r\n    }\r\n  }\r\n\r\n  saveListFormItem() {\r\n    this._formItemService.apiFormItemSaveListFormItemPost$Json({\r\n      body: this.saveListFormItemReq\r\n    }).subscribe(res => {\r\n      if (res.StatusCode == 0) {\r\n        this.message.showSucessMSG(\"執行成功\");\r\n        // this.getListFormItem()\r\n        this.goBack()\r\n      }\r\n    })\r\n  }\r\n\r\n  creatListFormItem: CreateListFormItem\r\n\r\n  createListFormItem() {\r\n    this.creatListFormItem = {\r\n      CBuildCaseId: this.buildCaseId,\r\n      CFormItem: this.saveListFormItemReq || null,\r\n      CFormType: this.typeContentManagementSalesAccount.CFormType,\r\n    }\r\n\r\n    this._formItemService.apiFormItemCreateListFormItemPost$Json({\r\n      body: this.creatListFormItem\r\n    }).subscribe(res => {\r\n      if (res.StatusCode == 0) {\r\n        this.message.showSucessMSG(\"執行成功\");\r\n        // this.getListFormItem()\r\n        this.goBack()\r\n      }\r\n    })\r\n  }\r\n\r\n  createArrayObjectFromArray(a: string[], b: { CFormItemHouseholdId: number, CFormItemId: number, CHousehold: string, CIsSelect: boolean }[]): { [key: string]: boolean } {\r\n    const c: { [key: string]: boolean } = {};\r\n    for (const item of a) {\r\n      const matchingItem = b.find(bItem => bItem.CHousehold === item && bItem.CIsSelect);\r\n      c[item] = !!matchingItem;\r\n    }\r\n    return c;\r\n  } //[\"House1\", \"House2\", \"House3\"] => [{CHousehold: \"House1\", CIsSelect: true,... }, ... ]\r\n\r\n\r\n  houseHoldList: any[]\r\n\r\n  getListRegularNoticeFileHouseHold() {\r\n    this._regularNoticeFileService.apiRegularNoticeFileGetListRegularNoticeFileHouseHoldPost$Json({\r\n      body: this.buildCaseId\r\n    }).pipe(\r\n      tap(res => {\r\n        if (res.Entries && res.StatusCode == 0) {\r\n          this.houseHoldList = this.getHouseHoldListByNoticeType(res.Entries)\r\n          this.getListFormItem()\r\n        }\r\n      })\r\n    ).subscribe()\r\n  }\r\n  goBack() { \r\n    this._eventService.push({\r\n      action: EEvent.GET_BUILDCASE,\r\n      payload: this.buildCaseId\r\n    })\r\n    this.location.back() }\r\n}\r\n", "<!-- 3.7.1  CNoticeType = 1 -->\r\n<nb-card accent=\"success\">\r\n  <nb-card-header>\r\n    <ngx-breadcrumb></ngx-breadcrumb>\r\n  </nb-card-header>\r\n  <nb-card-body>\r\n    <h1 class=\"font-bold text-[#818181]\"></h1>\r\n    <div class=\"d-flex flex-wrap\">\r\n      <div class=\"col-md-12\">\r\n        <h4 class=\"font-bold text-lg px-3 pb-3\">類型-獨立選樣</h4>\r\n      </div>\r\n      <ng-container *ngFor=\"let formItemReq of arrListFormItemReq; let idx = index\">\r\n        <div class=\"col-md-9\">\r\n          <div class=\"form-group d-flex align-items-center w-full\">\r\n            <label for=\"CItemName\" class=\"label col-4 text-base\">\r\n              {{formItemReq.CName}}-{{formItemReq.CPart}}-{{formItemReq.CLocation}}\r\n            </label>\r\n            <div class=\"input-group items-center w-full col-8 px-0\">\r\n              <input type=\"text\" class=\"w-full col-12\" nbInput [(ngModel)]=\"formItemReq.CItemName\" placeholder=\"廚房\"\r\n                [disabled]=\"listFormItem.CIsLock\" />\r\n            </div>\r\n          </div>\r\n        </div>\r\n        <div class=\"col-md-3\">\r\n          <div class=\"d-flex justify-content-end w-full\">\r\n            <button class=\"btn btn-info\" (click)=\"inputFile.click()\" [disabled]=\"listFormItem.CIsLock\">上傳概念設計圖</button>\r\n          </div>\r\n        </div>\r\n        <div class=\"col-md-12\" [ngClass]=\"formItemReq.listPictures.length ? '':'hidden'\">\r\n          <input #inputFile type=\"file\" class=\"hidden\" (change)=\"detectFiles($event, formItemReq)\"\r\n            accept=\"image/png, image/gif, image/jpeg\">\r\n          <div class=\"mt-3 w-full flex flex-col\">\r\n            <table class=\"table table-striped border\" style=\"background-color:#f3f3f3;\">\r\n              <tbody>\r\n                <tr *ngFor=\"let picture of formItemReq.listPictures; let i = index\">\r\n                  <td class=\"align-middle\">\r\n                    <label>文件名 </label> &nbsp;\r\n                    <input nbInput class=\"w-100 p-2 text-[13px]\" type=\"text\" [value]=\"picture.name\"\r\n                      (blur)=\"renameFile($event, i, formItemReq)\">\r\n                  </td>\r\n                  <td class=\"w-[100px] h-auto\">\r\n                    <img class=\"fit-size\" [src]=\"picture.data\">\r\n                  </td>\r\n                  <td class=\"text-center w-32 align-middle\">\r\n                    <button class=\"btn btn-outline-danger btn-sm m-1\"\r\n                      (click)=\"removeImage(picture.id, formItemReq)\">删除</button>\r\n                  </td>\r\n                </tr>\r\n              </tbody>\r\n            </table>\r\n          </div>\r\n        </div>\r\n        <div class=\"col-md-12 text-center\" *ngIf=\"formItemReq.CDesignFileUrl\"\r\n          [ngClass]=\"formItemReq.listPictures.length ? 'hidden':''\">\r\n          <div class=\"mt-3 w-full flex flex-col\">\r\n            <table class=\"table table-striped border\">\r\n              <tbody>\r\n                <tr>\r\n                  <td class=\"align-middle\">\r\n                  </td>\r\n                  <td class=\"w-[80px] h-auto\">\r\n                    <img *ngIf=\"formItemReq.CDesignFileUrl\" class=\"w-14 h-14\"\r\n                      [src]=\"formItemReq.CDesignFileUrl | addBaseFile\">\r\n                  </td>\r\n                  <td class=\"text-center w-32 align-middle\">\r\n                  </td>\r\n                </tr>\r\n              </tbody>\r\n            </table>\r\n          </div>\r\n        </div>\r\n        <div class=\"col-md-9\">\r\n          <div class=\"form-group d-flex align-items-center w-full\">\r\n            <label for=\"cRequireAnswer\" class=\"label col-4\">必填數量</label>\r\n            <input type=\"number\" class=\"col-8\" nbInput placeholder=\"必填數量\" [(ngModel)]=\"formItemReq.CRequireAnswer\"\r\n              [disabled]=\"formItemReq.selectedCUiType.value===3||listFormItem.CIsLock\" />\r\n          </div>\r\n        </div>\r\n        <div class=\"col-md-3\">\r\n\r\n        </div>\r\n\r\n        <div class=\"col-md-9\">\r\n          <div class=\"form-group d-flex align-items-center w-full\">\r\n            <label for=\"buildingName\" class=\"label col-4\">前台UI類型</label>\r\n            <nb-select placeholder=\"建案\" [(ngModel)]=\"formItemReq.selectedCUiType\" class=\"col-8 px-0\"\r\n              (selectedChange)=\"changeSelectCUiType(formItemReq)\" [disabled]=\"listFormItem.CIsLock\">\r\n              <nb-option *ngFor=\"let case of CUiTypeOptions\" [value]=\"case\">\r\n                {{ case.label }}\r\n              </nb-option>\r\n            </nb-select>\r\n          </div>\r\n        </div>\r\n        <div class=\"col-md-3\"></div>\r\n        <div class=\"col-md-12\">\r\n          <div class=\"form-group d-flex align-items-center w-full\">\r\n            <label for=\"buildingName\" class=\"label col-3\">適用戶別 </label>\r\n            <label class=\"mr-2\" *ngIf=\"houseHoldList.length\">\r\n              <nb-checkbox [(checked)]=\"formItemReq.allSelected\" [disabled]=\"listFormItem.CIsLock\"\r\n                (checkedChange)=\"onCheckAllChange($event, formItemReq)\">\r\n                全選\r\n              </nb-checkbox>\r\n            </label>\r\n            <label *ngFor=\"let item of houseHoldList\" class=\"mr-2\">\r\n              <nb-checkbox [(checked)]=\"formItemReq.selectedItems[item]\" value=\"item\" [disabled]=\"listFormItem.CIsLock\"\r\n                (checkedChange)=\"onCheckboxHouseHoldListChange($event, item, formItemReq)\">\r\n                {{ item }}\r\n              </nb-checkbox>\r\n            </label>\r\n          </div>\r\n        </div>\r\n        <div class=\"col-md-12\" *ngIf=\"formItemReq.selectedCUiType.value===3\">\r\n          <div class=\"form-group d-flex align-items-center w-full\">\r\n            <label for=\"buildingName\" class=\"label col-3\">備註選項</label>\r\n            <label *ngFor=\"let remark of CRemarkTypeOptions\" class=\"mr-2\">\r\n              <nb-checkbox *ngIf=\"formItemReq.selectedRemarkType\" [(checked)]=\"formItemReq.selectedRemarkType[remark]\"\r\n                [disabled]=\"listFormItem.CIsLock\" value=\"item\"\r\n                (checkedChange)=\"onCheckboxRemarkChange($event, remark, formItemReq)\">\r\n                {{ remark }}\r\n              </nb-checkbox>\r\n            </label>\r\n          </div>\r\n        </div>\r\n      </ng-container>\r\n    </div>\r\n  </nb-card-body>\r\n  <nb-card-footer class=\"d-flex justify-content-center\">\r\n    <button class=\"btn btn-secondary mx-2\" (click)=\"goBack()\">\r\n      取消\r\n    </button>\r\n    <button class=\"btn btn-info\" (click)=\"onSubmit()\" *ngIf=\"!listFormItem.CIsLock\">\r\n      儲存\r\n    </button>\r\n  </nb-card-footer>\r\n</nb-card>"], "mappings": "AACA,SAASA,YAAY,QAAkB,iBAAiB;AACxD,SAASC,gBAAgB,QAAQ,gBAAgB;AAKjD,SAASC,GAAG,QAAQ,MAAM;AAK1B,SAASC,YAAY,QAAQ,wCAAwC;AACrE,SAASC,aAAa,QAAQ,6CAA6C;AAC3E,SAAuBC,MAAM,QAAQ,uCAAuC;;;;;;;;;;;;;;;;;ICsBxDC,EAFJ,CAAAC,cAAA,SAAoE,aACzC,YAChB;IAAAD,EAAA,CAAAE,MAAA,0BAAI;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAACH,EAAA,CAAAE,MAAA,eACpB;IAAAF,EAAA,CAAAC,cAAA,gBAC8C;IAA5CD,EAAA,CAAAI,UAAA,kBAAAC,iGAAAC,MAAA;MAAA,MAAAC,IAAA,GAAAP,EAAA,CAAAQ,aAAA,CAAAC,GAAA,EAAAC,KAAA;MAAA,MAAAC,cAAA,GAAAX,EAAA,CAAAY,aAAA,GAAAC,SAAA;MAAA,MAAAC,MAAA,GAAAd,EAAA,CAAAY,aAAA;MAAA,OAAAZ,EAAA,CAAAe,WAAA,CAAQD,MAAA,CAAAE,UAAA,CAAAV,MAAA,EAAAC,IAAA,EAAAI,cAAA,CAAkC;IAAA,EAAC;IAC/CX,EAFE,CAAAG,YAAA,EAC8C,EAC3C;IACLH,EAAA,CAAAC,cAAA,aAA6B;IAC3BD,EAAA,CAAAiB,SAAA,cAA2C;IAC7CjB,EAAA,CAAAG,YAAA,EAAK;IAEHH,EADF,CAAAC,cAAA,aAA0C,iBAES;IAA/CD,EAAA,CAAAI,UAAA,mBAAAc,mGAAA;MAAA,MAAAC,UAAA,GAAAnB,EAAA,CAAAQ,aAAA,CAAAC,GAAA,EAAAI,SAAA;MAAA,MAAAF,cAAA,GAAAX,EAAA,CAAAY,aAAA,GAAAC,SAAA;MAAA,MAAAC,MAAA,GAAAd,EAAA,CAAAY,aAAA;MAAA,OAAAZ,EAAA,CAAAe,WAAA,CAASD,MAAA,CAAAM,WAAA,CAAAD,UAAA,CAAAE,EAAA,EAAAV,cAAA,CAAoC;IAAA,EAAC;IAACX,EAAA,CAAAE,MAAA,oBAAE;IAEvDF,EAFuD,CAAAG,YAAA,EAAS,EACzD,EACF;;;;IAVwDH,EAAA,CAAAsB,SAAA,GAAsB;IAAtBtB,EAAA,CAAAuB,UAAA,UAAAJ,UAAA,CAAAK,IAAA,CAAsB;IAIzDxB,EAAA,CAAAsB,SAAA,GAAoB;IAApBtB,EAAA,CAAAuB,UAAA,QAAAJ,UAAA,CAAAM,IAAA,EAAAzB,EAAA,CAAA0B,aAAA,CAAoB;;;;;IAoB1C1B,EAAA,CAAAiB,SAAA,cACmD;;;;;IAAjDjB,EAAA,CAAAuB,UAAA,QAAAvB,EAAA,CAAA2B,WAAA,OAAAhB,cAAA,CAAAiB,cAAA,GAAA5B,EAAA,CAAA0B,aAAA,CAAgD;;;;;IALtD1B,EALR,CAAAC,cAAA,cAC4D,cACnB,gBACK,YACjC,SACD;IACFD,EAAA,CAAAiB,SAAA,aACK;IACLjB,EAAA,CAAAC,cAAA,aAA4B;IAC1BD,EAAA,CAAA6B,UAAA,IAAAC,iFAAA,kBACmD;IACrD9B,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAiB,SAAA,aACK;IAKfjB,EAJQ,CAAAG,YAAA,EAAK,EACC,EACF,EACJ,EACF;;;;IAjBJH,EAAA,CAAAuB,UAAA,YAAAZ,cAAA,CAAAoB,YAAA,CAAAC,MAAA,iBAAyD;IAQzChC,EAAA,CAAAsB,SAAA,GAAgC;IAAhCtB,EAAA,CAAAuB,UAAA,SAAAZ,cAAA,CAAAiB,cAAA,CAAgC;;;;;IA0B5C5B,EAAA,CAAAC,cAAA,oBAA8D;IAC5DD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAFmCH,EAAA,CAAAuB,UAAA,UAAAU,OAAA,CAAc;IAC3DjC,EAAA,CAAAsB,SAAA,EACF;IADEtB,EAAA,CAAAkC,kBAAA,MAAAD,OAAA,CAAAE,KAAA,MACF;;;;;;IASAnC,EADF,CAAAC,cAAA,gBAAiD,sBAEW;IAD7CD,EAAA,CAAAoC,gBAAA,2BAAAC,mHAAA/B,MAAA;MAAAN,EAAA,CAAAQ,aAAA,CAAA8B,GAAA;MAAA,MAAA3B,cAAA,GAAAX,EAAA,CAAAY,aAAA,GAAAC,SAAA;MAAAb,EAAA,CAAAuC,kBAAA,CAAA5B,cAAA,CAAA6B,WAAA,EAAAlC,MAAA,MAAAK,cAAA,CAAA6B,WAAA,GAAAlC,MAAA;MAAA,OAAAN,EAAA,CAAAe,WAAA,CAAAT,MAAA;IAAA,EAAqC;IAChDN,EAAA,CAAAI,UAAA,2BAAAiC,mHAAA/B,MAAA;MAAAN,EAAA,CAAAQ,aAAA,CAAA8B,GAAA;MAAA,MAAA3B,cAAA,GAAAX,EAAA,CAAAY,aAAA,GAAAC,SAAA;MAAA,MAAAC,MAAA,GAAAd,EAAA,CAAAY,aAAA;MAAA,OAAAZ,EAAA,CAAAe,WAAA,CAAiBD,MAAA,CAAA2B,gBAAA,CAAAnC,MAAA,EAAAK,cAAA,CAAqC;IAAA,EAAC;IACvDX,EAAA,CAAAE,MAAA,qBACF;IACFF,EADE,CAAAG,YAAA,EAAc,EACR;;;;;IAJOH,EAAA,CAAAsB,SAAA,EAAqC;IAArCtB,EAAA,CAAA0C,gBAAA,YAAA/B,cAAA,CAAA6B,WAAA,CAAqC;IAACxC,EAAA,CAAAuB,UAAA,aAAAT,MAAA,CAAA6B,YAAA,CAAAC,OAAA,CAAiC;;;;;;IAMpF5C,EADF,CAAAC,cAAA,gBAAuD,sBAEwB;IADhED,EAAA,CAAAoC,gBAAA,2BAAAS,mHAAAvC,MAAA;MAAA,MAAAwC,QAAA,GAAA9C,EAAA,CAAAQ,aAAA,CAAAuC,IAAA,EAAAlC,SAAA;MAAA,MAAAF,cAAA,GAAAX,EAAA,CAAAY,aAAA,GAAAC,SAAA;MAAAb,EAAA,CAAAuC,kBAAA,CAAA5B,cAAA,CAAAqC,aAAA,CAAAF,QAAA,GAAAxC,MAAA,MAAAK,cAAA,CAAAqC,aAAA,CAAAF,QAAA,IAAAxC,MAAA;MAAA,OAAAN,EAAA,CAAAe,WAAA,CAAAT,MAAA;IAAA,EAA6C;IACxDN,EAAA,CAAAI,UAAA,2BAAAyC,mHAAAvC,MAAA;MAAA,MAAAwC,QAAA,GAAA9C,EAAA,CAAAQ,aAAA,CAAAuC,IAAA,EAAAlC,SAAA;MAAA,MAAAF,cAAA,GAAAX,EAAA,CAAAY,aAAA,GAAAC,SAAA;MAAA,MAAAC,MAAA,GAAAd,EAAA,CAAAY,aAAA;MAAA,OAAAZ,EAAA,CAAAe,WAAA,CAAiBD,MAAA,CAAAmC,6BAAA,CAAA3C,MAAA,EAAAwC,QAAA,EAAAnC,cAAA,CAAwD;IAAA,EAAC;IAC1EX,EAAA,CAAAE,MAAA,GACF;IACFF,EADE,CAAAG,YAAA,EAAc,EACR;;;;;;IAJOH,EAAA,CAAAsB,SAAA,EAA6C;IAA7CtB,EAAA,CAAA0C,gBAAA,YAAA/B,cAAA,CAAAqC,aAAA,CAAAF,QAAA,EAA6C;IAAc9C,EAAA,CAAAuB,UAAA,aAAAT,MAAA,CAAA6B,YAAA,CAAAC,OAAA,CAAiC;IAEvG5C,EAAA,CAAAsB,SAAA,EACF;IADEtB,EAAA,CAAAkC,kBAAA,MAAAY,QAAA,MACF;;;;;;IAQA9C,EAAA,CAAAC,cAAA,sBAEwE;IAFpBD,EAAA,CAAAoC,gBAAA,2BAAAc,uIAAA5C,MAAA;MAAAN,EAAA,CAAAQ,aAAA,CAAA2C,IAAA;MAAA,MAAAC,UAAA,GAAApD,EAAA,CAAAY,aAAA,GAAAC,SAAA;MAAA,MAAAF,cAAA,GAAAX,EAAA,CAAAY,aAAA,IAAAC,SAAA;MAAAb,EAAA,CAAAuC,kBAAA,CAAA5B,cAAA,CAAA0C,kBAAA,CAAAD,UAAA,GAAA9C,MAAA,MAAAK,cAAA,CAAA0C,kBAAA,CAAAD,UAAA,IAAA9C,MAAA;MAAA,OAAAN,EAAA,CAAAe,WAAA,CAAAT,MAAA;IAAA,EAAoD;IAEtGN,EAAA,CAAAI,UAAA,2BAAA8C,uIAAA5C,MAAA;MAAAN,EAAA,CAAAQ,aAAA,CAAA2C,IAAA;MAAA,MAAAC,UAAA,GAAApD,EAAA,CAAAY,aAAA,GAAAC,SAAA;MAAA,MAAAF,cAAA,GAAAX,EAAA,CAAAY,aAAA,IAAAC,SAAA;MAAA,MAAAC,MAAA,GAAAd,EAAA,CAAAY,aAAA;MAAA,OAAAZ,EAAA,CAAAe,WAAA,CAAiBD,MAAA,CAAAwC,sBAAA,CAAAhD,MAAA,EAAA8C,UAAA,EAAAzC,cAAA,CAAmD;IAAA,EAAC;IACrEX,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAc;;;;;;IAJsCH,EAAA,CAAA0C,gBAAA,YAAA/B,cAAA,CAAA0C,kBAAA,CAAAD,UAAA,EAAoD;IACtGpD,EAAA,CAAAuB,UAAA,aAAAT,MAAA,CAAA6B,YAAA,CAAAC,OAAA,CAAiC;IAEjC5C,EAAA,CAAAsB,SAAA,EACF;IADEtB,EAAA,CAAAkC,kBAAA,MAAAkB,UAAA,MACF;;;;;IALFpD,EAAA,CAAAC,cAAA,gBAA8D;IAC5DD,EAAA,CAAA6B,UAAA,IAAA0B,iGAAA,0BAEwE;IAG1EvD,EAAA,CAAAG,YAAA,EAAQ;;;;IALQH,EAAA,CAAAsB,SAAA,EAAoC;IAApCtB,EAAA,CAAAuB,UAAA,SAAAZ,cAAA,CAAA0C,kBAAA,CAAoC;;;;;IAFpDrD,EAFJ,CAAAC,cAAA,aAAqE,cACV,gBACT;IAAAD,EAAA,CAAAE,MAAA,+BAAI;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAC1DH,EAAA,CAAA6B,UAAA,IAAA2B,mFAAA,oBAA8D;IAQlExD,EADE,CAAAG,YAAA,EAAM,EACF;;;;IARwBH,EAAA,CAAAsB,SAAA,GAAqB;IAArBtB,EAAA,CAAAuB,UAAA,YAAAT,MAAA,CAAA2C,kBAAA,CAAqB;;;;;;IAvGrDzD,EAAA,CAAA0D,uBAAA,GAA8E;IAGxE1D,EAFJ,CAAAC,cAAA,cAAsB,cACqC,gBACF;IACnDD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAENH,EADF,CAAAC,cAAA,cAAwD,gBAEhB;IADWD,EAAA,CAAAoC,gBAAA,2BAAAuB,oGAAArD,MAAA;MAAA,MAAAK,cAAA,GAAAX,EAAA,CAAAQ,aAAA,CAAAoD,GAAA,EAAA/C,SAAA;MAAAb,EAAA,CAAAuC,kBAAA,CAAA5B,cAAA,CAAAkD,SAAA,EAAAvD,MAAA,MAAAK,cAAA,CAAAkD,SAAA,GAAAvD,MAAA;MAAA,OAAAN,EAAA,CAAAe,WAAA,CAAAT,MAAA;IAAA,EAAmC;IAI1FN,EAJM,CAAAG,YAAA,EACsC,EAClC,EACF,EACF;IAGFH,EAFJ,CAAAC,cAAA,cAAsB,cAC2B,iBAC8C;IAA9DD,EAAA,CAAAI,UAAA,mBAAA0D,6FAAA;MAAA9D,EAAA,CAAAQ,aAAA,CAAAoD,GAAA;MAAA,MAAAG,YAAA,GAAA/D,EAAA,CAAAgE,WAAA;MAAA,OAAAhE,EAAA,CAAAe,WAAA,CAASgD,YAAA,CAAAE,KAAA,EAAiB;IAAA,EAAC;IAAmCjE,EAAA,CAAAE,MAAA,kDAAO;IAEtGF,EAFsG,CAAAG,YAAA,EAAS,EACvG,EACF;IAEJH,EADF,CAAAC,cAAA,eAAiF,oBAEnC;IADCD,EAAA,CAAAI,UAAA,oBAAA8D,8FAAA5D,MAAA;MAAA,MAAAK,cAAA,GAAAX,EAAA,CAAAQ,aAAA,CAAAoD,GAAA,EAAA/C,SAAA;MAAA,MAAAC,MAAA,GAAAd,EAAA,CAAAY,aAAA;MAAA,OAAAZ,EAAA,CAAAe,WAAA,CAAUD,MAAA,CAAAqD,WAAA,CAAA7D,MAAA,EAAAK,cAAA,CAAgC;IAAA,EAAC;IAAxFX,EAAA,CAAAG,YAAA,EAC4C;IAGxCH,EAFJ,CAAAC,cAAA,eAAuC,iBACuC,aACnE;IACLD,EAAA,CAAA6B,UAAA,KAAAuC,0EAAA,iBAAoE;IAiB5EpE,EAHM,CAAAG,YAAA,EAAQ,EACF,EACJ,EACF;IACNH,EAAA,CAAA6B,UAAA,KAAAwC,2EAAA,kBAC4D;IAoBxDrE,EAFJ,CAAAC,cAAA,eAAsB,eACqC,iBACP;IAAAD,EAAA,CAAAE,MAAA,gCAAI;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAC5DH,EAAA,CAAAC,cAAA,iBAC6E;IADfD,EAAA,CAAAoC,gBAAA,2BAAAkC,qGAAAhE,MAAA;MAAA,MAAAK,cAAA,GAAAX,EAAA,CAAAQ,aAAA,CAAAoD,GAAA,EAAA/C,SAAA;MAAAb,EAAA,CAAAuC,kBAAA,CAAA5B,cAAA,CAAA4D,cAAA,EAAAjE,MAAA,MAAAK,cAAA,CAAA4D,cAAA,GAAAjE,MAAA;MAAA,OAAAN,EAAA,CAAAe,WAAA,CAAAT,MAAA;IAAA,EAAwC;IAG1GN,EAHI,CAAAG,YAAA,EAC6E,EACzE,EACF;IACNH,EAAA,CAAAiB,SAAA,eAEM;IAIFjB,EAFJ,CAAAC,cAAA,eAAsB,eACqC,iBACT;IAAAD,EAAA,CAAAE,MAAA,kCAAM;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAC5DH,EAAA,CAAAC,cAAA,qBACwF;IAD5DD,EAAA,CAAAoC,gBAAA,2BAAAoC,yGAAAlE,MAAA;MAAA,MAAAK,cAAA,GAAAX,EAAA,CAAAQ,aAAA,CAAAoD,GAAA,EAAA/C,SAAA;MAAAb,EAAA,CAAAuC,kBAAA,CAAA5B,cAAA,CAAA8D,eAAA,EAAAnE,MAAA,MAAAK,cAAA,CAAA8D,eAAA,GAAAnE,MAAA;MAAA,OAAAN,EAAA,CAAAe,WAAA,CAAAT,MAAA;IAAA,EAAyC;IACnEN,EAAA,CAAAI,UAAA,4BAAAsE,0GAAA;MAAA,MAAA/D,cAAA,GAAAX,EAAA,CAAAQ,aAAA,CAAAoD,GAAA,EAAA/C,SAAA;MAAA,MAAAC,MAAA,GAAAd,EAAA,CAAAY,aAAA;MAAA,OAAAZ,EAAA,CAAAe,WAAA,CAAkBD,MAAA,CAAA6D,mBAAA,CAAAhE,cAAA,CAAgC;IAAA,EAAC;IACnDX,EAAA,CAAA6B,UAAA,KAAA+C,iFAAA,wBAA8D;IAKpE5E,EAFI,CAAAG,YAAA,EAAY,EACR,EACF;IACNH,EAAA,CAAAiB,SAAA,eAA4B;IAGxBjB,EAFJ,CAAAC,cAAA,cAAuB,eACoC,iBACT;IAAAD,EAAA,CAAAE,MAAA,iCAAK;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAO3DH,EANA,CAAA6B,UAAA,KAAAgD,6EAAA,oBAAiD,KAAAC,6EAAA,oBAMM;IAO3D9E,EADE,CAAAG,YAAA,EAAM,EACF;IACNH,EAAA,CAAA6B,UAAA,KAAAkD,2EAAA,kBAAqE;;;;;;IAhG/D/E,EAAA,CAAAsB,SAAA,GACF;IADEtB,EAAA,CAAAgF,kBAAA,MAAArE,cAAA,CAAAsE,KAAA,OAAAtE,cAAA,CAAAuE,KAAA,OAAAvE,cAAA,CAAAwE,SAAA,MACF;IAEmDnF,EAAA,CAAAsB,SAAA,GAAmC;IAAnCtB,EAAA,CAAA0C,gBAAA,YAAA/B,cAAA,CAAAkD,SAAA,CAAmC;IAClF7D,EAAA,CAAAuB,UAAA,aAAAT,MAAA,CAAA6B,YAAA,CAAAC,OAAA,CAAiC;IAMoB5C,EAAA,CAAAsB,SAAA,GAAiC;IAAjCtB,EAAA,CAAAuB,UAAA,aAAAT,MAAA,CAAA6B,YAAA,CAAAC,OAAA,CAAiC;IAGvE5C,EAAA,CAAAsB,SAAA,GAAyD;IAAzDtB,EAAA,CAAAuB,UAAA,YAAAZ,cAAA,CAAAoB,YAAA,CAAAC,MAAA,iBAAyD;IAMhDhC,EAAA,CAAAsB,SAAA,GAA6B;IAA7BtB,EAAA,CAAAuB,UAAA,YAAAZ,cAAA,CAAAoB,YAAA,CAA6B;IAkBzB/B,EAAA,CAAAsB,SAAA,EAAgC;IAAhCtB,EAAA,CAAAuB,UAAA,SAAAZ,cAAA,CAAAiB,cAAA,CAAgC;IAsBF5B,EAAA,CAAAsB,SAAA,GAAwC;IAAxCtB,EAAA,CAAA0C,gBAAA,YAAA/B,cAAA,CAAA4D,cAAA,CAAwC;IACpGvE,EAAA,CAAAuB,UAAA,aAAAZ,cAAA,CAAA8D,eAAA,CAAAW,KAAA,UAAAtE,MAAA,CAAA6B,YAAA,CAAAC,OAAA,CAAwE;IAU9C5C,EAAA,CAAAsB,SAAA,GAAyC;IAAzCtB,EAAA,CAAA0C,gBAAA,YAAA/B,cAAA,CAAA8D,eAAA,CAAyC;IACfzE,EAAA,CAAAuB,UAAA,aAAAT,MAAA,CAAA6B,YAAA,CAAAC,OAAA,CAAiC;IACzD5C,EAAA,CAAAsB,SAAA,EAAiB;IAAjBtB,EAAA,CAAAuB,UAAA,YAAAT,MAAA,CAAAuE,cAAA,CAAiB;IAU1BrF,EAAA,CAAAsB,SAAA,GAA0B;IAA1BtB,EAAA,CAAAuB,UAAA,SAAAT,MAAA,CAAAwE,aAAA,CAAAtD,MAAA,CAA0B;IAMvBhC,EAAA,CAAAsB,SAAA,EAAgB;IAAhBtB,EAAA,CAAAuB,UAAA,YAAAT,MAAA,CAAAwE,aAAA,CAAgB;IAQpBtF,EAAA,CAAAsB,SAAA,EAA2C;IAA3CtB,EAAA,CAAAuB,UAAA,SAAAZ,cAAA,CAAA8D,eAAA,CAAAW,KAAA,OAA2C;;;;;;IAmBvEpF,EAAA,CAAAC,cAAA,iBAAgF;IAAnDD,EAAA,CAAAI,UAAA,mBAAAmF,wFAAA;MAAAvF,EAAA,CAAAQ,aAAA,CAAAgF,IAAA;MAAA,MAAA1E,MAAA,GAAAd,EAAA,CAAAY,aAAA;MAAA,OAAAZ,EAAA,CAAAe,WAAA,CAASD,MAAA,CAAA2E,QAAA,EAAU;IAAA,EAAC;IAC/CzF,EAAA,CAAAE,MAAA,qBACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;ADtFb,OAAM,MAAOuF,4CAA6C,SAAQ5F,aAAa;EAC7E6F,YACUC,MAAmB,EACnBC,KAAqB,EACrBC,OAAuB,EACvBC,gBAAiC,EACjCC,yBAAmD,EACnDC,eAA+B,EAC/BC,KAAuB,EACvBC,QAAkB,EAClBC,gBAAiC,EACjCC,aAA2B;IAEnC,KAAK,CAACT,MAAM,CAAC;IAXL,KAAAA,MAAM,GAANA,MAAM;IACN,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,yBAAyB,GAAzBA,yBAAyB;IACzB,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,QAAQ,GAARA,QAAQ;IACR,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,aAAa,GAAbA,aAAa;IAKvB,KAAAC,iCAAiC,GAAG;MAClCC,SAAS,EAAE,CAAC;MACZC,WAAW,EAAE;KACd;IAED,KAAAnB,cAAc,GAAU,CACtB;MACED,KAAK,EAAE,CAAC;MAAEjD,KAAK,EAAE;KAClB,EACD;MACEiD,KAAK,EAAE,CAAC;MAAEjD,KAAK,EAAE;KAClB,EAAE;MACDiD,KAAK,EAAE,CAAC;MAAEjD,KAAK,EAAE;KAClB,CACF;IACD,KAAAsB,kBAAkB,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC;IA2BjC,KAAAT,aAAa,GAA+B,EAAE;IAC9C,KAAAK,kBAAkB,GAA+B,EAAE;IAkJnD,KAAAoD,KAAK,GAAY,IAAI;EA/LrB;EAoBSC,QAAQA,CAAA;IACf,IAAI,CAACb,KAAK,CAACc,QAAQ,CAACC,SAAS,CAACC,MAAM,IAAG;MACrC,IAAIA,MAAM,EAAE;QACV,MAAMC,OAAO,GAAGD,MAAM,CAACE,GAAG,CAAC,IAAI,CAAC;QAChC,MAAM1F,EAAE,GAAGyF,OAAO,GAAG,CAACA,OAAO,GAAG,CAAC;QACjC,IAAI,CAACE,WAAW,GAAG3F,EAAE;QACrB,IAAI,IAAI,CAAC2F,WAAW,EAAE;UACpB,IAAI,CAACC,iCAAiC,EAAE;QAC1C;MACF;IACF,CAAC,CAAC;EACJ;EAGAC,cAAcA,CAAC9B,KAAU,EAAE+B,OAAc;IACvC,KAAK,MAAMC,IAAI,IAAID,OAAO,EAAE;MAC1B,IAAIC,IAAI,CAAChC,KAAK,KAAKA,KAAK,EAAE;QACxB,OAAOgC,IAAI;MACb;IACF;IACA,OAAO,IAAI;EACb;EAQAjD,WAAWA,CAACkD,KAAU,EAAEC,YAAiB;IACvC,MAAMC,IAAI,GAAGF,KAAK,CAACG,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC;IAClC,IAAIF,IAAI,EAAE;MACR,IAAIG,MAAM,GAAG,IAAIC,UAAU,EAAE;MAC7BD,MAAM,CAACE,aAAa,CAACL,IAAI,CAAC;MAC1BG,MAAM,CAACG,MAAM,GAAG,MAAK;QACnB,IAAIC,SAAS,GAAWJ,MAAM,CAACK,MAAgB;QAC/C,IAAI,CAACD,SAAS,EAAE;UACd;QACF;QACA,IAAIR,YAAY,CAACvF,YAAY,CAACC,MAAM,GAAG,CAAC,EAAE;UACxCsF,YAAY,CAACvF,YAAY,CAAC,CAAC,CAAC,GAAG;YAC7BV,EAAE,EAAE,IAAI2G,IAAI,EAAE,CAACC,OAAO,EAAE;YACxBzG,IAAI,EAAE+F,IAAI,CAAC/F,IAAI,CAAC0G,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;YAC7BzG,IAAI,EAAEqG,SAAS;YACfK,SAAS,EAAE,IAAI,CAAClC,eAAe,CAACmC,gBAAgB,CAACb,IAAI,CAAC/F,IAAI,CAAC;YAC3D6G,KAAK,EAAEd;WACR;QACH,CAAC,MAAM;UACLD,YAAY,CAACvF,YAAY,CAACuG,IAAI,CAAC;YAC7BjH,EAAE,EAAE,IAAI2G,IAAI,EAAE,CAACC,OAAO,EAAE;YACxBzG,IAAI,EAAE+F,IAAI,CAAC/F,IAAI,CAAC0G,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;YAC7BzG,IAAI,EAAEqG,SAAS;YACfK,SAAS,EAAE,IAAI,CAAClC,eAAe,CAACmC,gBAAgB,CAACb,IAAI,CAAC/F,IAAI,CAAC;YAC3D6G,KAAK,EAAEd;WACR,CAAC;QACJ;QACAF,KAAK,CAACG,MAAM,CAACpC,KAAK,GAAG,IAAI;MAC3B,CAAC;IACH;EACF;EAEAhE,WAAWA,CAACmH,SAAiB,EAAEjB,YAAiB;IAC9C,IAAIA,YAAY,CAACvF,YAAY,CAACC,MAAM,EAAE;MACpCsF,YAAY,CAACvF,YAAY,GAAGuF,YAAY,CAACvF,YAAY,CAACyG,MAAM,CAAEC,CAAM,IAAKA,CAAC,CAACpH,EAAE,IAAIkH,SAAS,CAAC;IAC7F;EACF;EAEAvH,UAAUA,CAACqG,KAAU,EAAE3G,KAAa,EAAE4G,YAAiB;IACrD,IAAIoB,IAAI,GAAGpB,YAAY,CAACvF,YAAY,CAACrB,KAAK,CAAC,CAAC2H,KAAK,CAACM,KAAK,CAAC,CAAC,EAAErB,YAAY,CAACvF,YAAY,CAACrB,KAAK,CAAC,CAAC2H,KAAK,CAACO,IAAI,EAAEtB,YAAY,CAACvF,YAAY,CAACrB,KAAK,CAAC,CAAC2H,KAAK,CAACQ,IAAI,CAAC;IACpJ,IAAIC,OAAO,GAAG,IAAIC,IAAI,CAAC,CAACL,IAAI,CAAC,EAAE,GAAGrB,KAAK,CAACG,MAAM,CAACpC,KAAK,GAAG,GAAG,GAAGkC,YAAY,CAACvF,YAAY,CAACrB,KAAK,CAAC,CAACyH,SAAS,EAAE,EAAE;MAAEU,IAAI,EAAEvB,YAAY,CAACvF,YAAY,CAACrB,KAAK,CAAC,CAAC2H,KAAK,CAACQ;IAAI,CAAE,CAAC;IACjKvB,YAAY,CAACvF,YAAY,CAACrB,KAAK,CAAC,CAAC2H,KAAK,GAAGS,OAAO;EAClD;EAGArG,gBAAgBA,CAACuG,OAAgB,EAAE1B,YAAiB;IAClDA,YAAY,CAAC9E,WAAW,GAAGwG,OAAO;IAClC,IAAI,CAAC1D,aAAa,CAAC2D,OAAO,CAAC7B,IAAI,IAAG;MAChCE,YAAY,CAACtE,aAAa,CAACoE,IAAI,CAAC,GAAG4B,OAAO;IAC5C,CAAC,CAAC;EACJ;EAEA/F,6BAA6BA,CAAC+F,OAAgB,EAAE5B,IAAY,EAAEE,YAAiB;IAC7E,IAAI0B,OAAO,EAAE;MACX1B,YAAY,CAACtE,aAAa,CAACoE,IAAI,CAAC,GAAG4B,OAAO;MAC1C1B,YAAY,CAAC9E,WAAW,GAAG,IAAI,CAAC8C,aAAa,CAAC4D,KAAK,CAAC9B,IAAI,IAAIE,YAAY,CAACtE,aAAa,CAACoE,IAAI,CAAC,IAAI4B,OAAO,CAAC;IAC1G,CAAC,MAAM;MACL1B,YAAY,CAAC9E,WAAW,GAAG,KAAK;IAClC;EACF;EAIAc,sBAAsBA,CAAC0F,OAAgB,EAAE5B,IAAY,EAAEE,YAAiB;IACtEA,YAAY,CAACjE,kBAAkB,CAAC+D,IAAI,CAAC,GAAG4B,OAAO;EACjD;EAEAG,kBAAkBA,CAAC1F,kBAA4B,EAAE2F,WAAmB;IAClE,MAAMC,YAAY,GAA+B,EAAE;IACnD,KAAK,MAAMC,MAAM,IAAI7F,kBAAkB,EAAE;MACvC4F,YAAY,CAACC,MAAM,CAAC,GAAG,KAAK;IAC9B;IACA,MAAMC,WAAW,GAAGH,WAAW,CAAClB,KAAK,CAAC,GAAG,CAAC;IAC1C,KAAK,MAAMW,IAAI,IAAIU,WAAW,EAAE;MAC9B,IAAI9F,kBAAkB,CAAC+F,QAAQ,CAACX,IAAI,CAAC,EAAE;QACrCQ,YAAY,CAACR,IAAI,CAAC,GAAG,IAAI;MAC3B;IACF;IACA,OAAOQ,YAAY;EACrB;EAEAI,UAAUA,CAACC,KAAoC;IAC7C,MAAMC,GAAG,GAAG,IAAIC,GAAG,EAAgE;IAEnFF,KAAK,CAACT,OAAO,CAAC7B,IAAI,IAAG;MACnB,MAAMyC,GAAG,GAAG,GAAGzC,IAAI,CAACjC,SAAS,IAAIiC,IAAI,CAACnC,KAAK,IAAImC,IAAI,CAAClC,KAAK,EAAE;MAC3D,IAAIyE,GAAG,CAACG,GAAG,CAACD,GAAG,CAAC,EAAE;QAChB,MAAME,QAAQ,GAAGJ,GAAG,CAAC5C,GAAG,CAAC8C,GAAG,CAAE;QAC9BE,QAAQ,CAACC,KAAK,IAAI,CAAC;MACrB,CAAC,MAAM;QACLL,GAAG,CAACM,GAAG,CAACJ,GAAG,EAAE;UAAEzC,IAAI,EAAE;YAAE,GAAGA;UAAI,CAAE;UAAE4C,KAAK,EAAE;QAAC,CAAE,CAAC;MAC/C;IACF,CAAC,CAAC;IAEF,OAAOE,KAAK,CAACC,IAAI,CAACR,GAAG,CAACS,MAAM,EAAE,CAAC,CAACT,GAAG,CAAC,CAAC;MAAEvC,IAAI;MAAE4C;IAAK,CAAE,MAAM;MACxD,GAAG5C,IAAI;MACPiD,YAAY,EAAEL;KACf,CAAC,CAAC;EACL;EAGAM,eAAeA,CAAA;IACb,IAAI,CAAClE,gBAAgB,CAACmE,mCAAmC,CAAC;MACxDC,IAAI,EAAE;QACJC,YAAY,EAAE,IAAI,CAACzD,WAAW;QAC9B0D,KAAK,EAAE;;KAEV,CAAC,CAACC,IAAI,CACL/K,GAAG,CAACgL,GAAG,IAAG;MACR,IAAIA,GAAG,CAACC,OAAO,IAAID,GAAG,CAACE,UAAU,IAAI,CAAC,EAAE;QAEtC,IAAI,CAACxF,aAAa,CAAC2D,OAAO,CAAC7B,IAAI,IAAI,IAAI,CAACpE,aAAa,CAACoE,IAAI,CAAC,GAAG,KAAK,CAAC;QAEpE,IAAI,CAAC3D,kBAAkB,CAACwF,OAAO,CAAC7B,IAAI,IAAI,IAAI,CAAC/D,kBAAkB,CAAC+D,IAAI,CAAC,GAAG,KAAK,CAAC;QAE9E,IAAI,CAAC2D,kBAAkB,GAAGH,GAAG,CAACC,OAAO,CAAClB,GAAG,CAAEqB,CAAM,IAAI;UACnD,OAAO;YACLpJ,cAAc,EAAE,IAAI;YACpBqJ,kBAAkB,EAAE,IAAI;YACxBC,OAAO,EAAE,IAAI;YACb/F,SAAS,EAAE6F,CAAC,CAAC7F,SAAS;YACtBF,KAAK,EAAE+F,CAAC,CAAC/F,KAAK;YACdC,KAAK,EAAE8F,CAAC,CAAC9F,KAAK;YACdrB,SAAS,EAAE,GAAGmH,CAAC,CAAC/F,KAAK,IAAI+F,CAAC,CAAC9F,KAAK,IAAI8F,CAAC,CAAC7F,SAAS,EAAE;YACjDiE,WAAW,EAAE,IAAI;YACjBiB,YAAY,EAAE,CAAC;YACf9F,cAAc,EAAE4G,SAAS;YACzBC,OAAO,EAAE,CAAC;YACVpI,aAAa,EAAE,EAAE;YACjBK,kBAAkB,EAAE,IAAI,CAACA,kBAAkB;YAC3Cb,WAAW,EAAE,KAAK;YAClBT,YAAY,EAAE,EAAE;YAChB0C,eAAe,EAAE,IAAI,CAACY,cAAc,CAAC,CAAC;WACvC;QACH,CAAC,CAAC;QACF,IAAI,CAAC0F,kBAAkB,GAAG,CAAC,GAAG,IAAI,CAACtB,UAAU,CAAC,IAAI,CAACsB,kBAAkB,CAAC,CAAC;MACzE;IACF,CAAC,CAAC,CACH,CAACnE,SAAS,EAAE;EACf;EAKAyE,eAAeA,CAAA;IACb,IAAI,CAACtF,gBAAgB,CAACuF,mCAAmC,CAAC;MACxDd,IAAI,EAAE;QACJC,YAAY,EAAE,IAAI,CAACzD,WAAW;QAC9BT,SAAS,EAAE,IAAI,CAACD,iCAAiC,CAACC,SAAS;QAC3DgF,SAAS,EAAE;;KAEd,CAAC,CAACZ,IAAI,CACL/K,GAAG,CAACgL,GAAG,IAAG;MACR,IAAIA,GAAG,CAACC,OAAO,IAAID,GAAG,CAACE,UAAU,IAAI,CAAC,EAAE;QACtC,IAAI,CAACnI,YAAY,GAAGiI,GAAG,CAACC,OAAO;QAC/B,IAAI,CAACpE,KAAK,GAAGmE,GAAG,CAACC,OAAO,CAACW,SAAS,GAAG,KAAK,GAAG,IAAI;QACjD,IAAIZ,GAAG,CAACC,OAAO,CAACW,SAAS,EAAE;UAEzB,IAAI,CAAClG,aAAa,CAAC2D,OAAO,CAAC7B,IAAI,IAAI,IAAI,CAACpE,aAAa,CAACoE,IAAI,CAAC,GAAG,KAAK,CAAC;UACpE,IAAI,CAAC3D,kBAAkB,CAACwF,OAAO,CAAC7B,IAAI,IAAI,IAAI,CAAC/D,kBAAkB,CAAC+D,IAAI,CAAC,GAAG,KAAK,CAAC;UAE9E,IAAI,CAAC2D,kBAAkB,GAAGH,GAAG,CAACC,OAAO,CAACW,SAAS,CAAC7B,GAAG,CAAEqB,CAAM,IAAI;YAC7D,OAAO;cACLE,OAAO,EAAE,IAAI,CAACvI,YAAY,CAACuI,OAAO;cAClCtJ,cAAc,EAAEoJ,CAAC,CAACpJ,cAAc;cAChCyG,KAAK,EAAE2C,CAAC,CAAC3C,KAAK;cACd4C,kBAAkB,EAAED,CAAC,CAACC,kBAAkB;cACxCQ,WAAW,EAAET,CAAC,CAACS,WAAW;cAC1BtG,SAAS,EAAE6F,CAAC,CAAC7F,SAAS;cACtBF,KAAK,EAAE+F,CAAC,CAAC/F,KAAK;cACdC,KAAK,EAAE8F,CAAC,CAAC9F,KAAK;cACdrB,SAAS,EAAEmH,CAAC,CAACnH,SAAS,GAAGmH,CAAC,CAACnH,SAAS,GAAG,GAAGmH,CAAC,CAAC/F,KAAK,IAAI+F,CAAC,CAAC9F,KAAK,IAAI8F,CAAC,CAAC7F,SAAS,EAAE;cAC7EiE,WAAW,EAAE4B,CAAC,CAAC5B,WAAW;cAC1BiB,YAAY,EAAEW,CAAC,CAACX,YAAY;cAC5B9F,cAAc,EAAEyG,CAAC,CAACI,OAAO,KAAG,CAAC,GAAG,CAAC,GAAEJ,CAAC,CAACzG,cAAc;cACnD6G,OAAO,EAAEJ,CAAC,CAACI,OAAO;cAClBpI,aAAa,EAAEgI,CAAC,CAACU,qBAAqB,CAAC1J,MAAM,GAAG,IAAI,CAAC2J,0BAA0B,CAAC,IAAI,CAACrG,aAAa,EAAE0F,CAAC,CAACU,qBAAqB,CAAC,GAAG;gBAAE,GAAG,IAAI,CAAC1I;cAAa,CAAE;cACxJK,kBAAkB,EAAE2H,CAAC,CAAC5B,WAAW,GAAG,IAAI,CAACD,kBAAkB,CAAC,IAAI,CAAC1F,kBAAkB,EAAEuH,CAAC,CAAC5B,WAAW,CAAC,GAAG;gBAAE,GAAG,IAAI,CAAC/F;cAAkB,CAAE;cACpIb,WAAW,EAAEwI,CAAC,CAACU,qBAAqB,CAAC1J,MAAM,KAAK,IAAI,CAACsD,aAAa,CAACtD,MAAM;cACzED,YAAY,EAAE,EAAE;cAChB0C,eAAe,EAAEuG,CAAC,CAACI,OAAO,GAAG,IAAI,CAAClE,cAAc,CAAC8D,CAAC,CAACI,OAAO,EAAE,IAAI,CAAC/F,cAAc,CAAC,GAAG,IAAI,CAACA,cAAc,CAAC,CAAC;aACzG;UACH,CAAC,CAAC;QACJ,CAAC,MAAM;UACL,IAAI,CAACiF,eAAe,EAAE;QACxB;MACF;IACF,CAAC,CAAC,CACH,CAAC1D,SAAS,EAAE;EACf;EAEAjC,mBAAmBA,CAACiH,WAAgB;IAClC,IAAGA,WAAW,CAACnH,eAAe,IAAImH,WAAW,CAACnH,eAAe,CAACW,KAAK,KAAI,CAAC,EAAE;MACxEwG,WAAW,CAACrH,cAAc,GAAG,CAAC;IAChC;EACF;EACAsH,4BAA4BA,CAACpK,IAAW;IACtC,KAAK,IAAI2F,IAAI,IAAI3F,IAAI,EAAE;MACrB,IAAI2F,IAAI,CAACZ,WAAW,KAAK,IAAI,CAACF,iCAAiC,CAACE,WAAW,EAAE;QAC3E,OAAOY,IAAI,CAAC0E,cAAc;MAC5B;IACF;IACA,OAAO,EAAE;EACX;EAIAC,oBAAoBA,CAACC,GAA4B;IAC/C,OAAOC,MAAM,CAACC,IAAI,CAACF,GAAG,CAAC,CAACxD,MAAM,CAACqB,GAAG,IAAImC,GAAG,CAACnC,GAAG,CAAC,CAAC;EACjD;EAEAsC,0BAA0BA,CAACH,GAA4B;IACrD,OAAOC,MAAM,CAACC,IAAI,CAACF,GAAG,CAAC,CACpBxD,MAAM,CAACqB,GAAG,IAAImC,GAAG,CAACnC,GAAG,CAAC,CAAC,CACvBuC,IAAI,CAAC,GAAG,CAAC;EACd;EAEAC,cAAcA,CAAC5H,eAAoB,EAAEpB,kBAAuB;IAC1D,IAAIoB,eAAe,IAAIA,eAAe,CAACW,KAAK,IAAI,CAAC,EAAE;MACjD,OAAO,IAAI,CAAC+G,0BAA0B,CAAC9I,kBAAkB,CAAC;IAC5D;EACF;EAEAiJ,mBAAmBA,CAACC,WAAmB;IACrC,MAAMC,KAAK,GAAGD,WAAW,CAACrE,KAAK,CAAC,GAAG,CAAC;IACpC,IAAIsE,KAAK,CAACxK,MAAM,GAAG,CAAC,EAAE;MACpB,OAAOwK,KAAK,CAAC,CAAC,CAAC;IACjB,CAAC,MAAM,OAAO,EAAE;EAClB;EAEAC,UAAUA,CAAC1K,YAAiB;IAC1B,IAAIA,YAAY,IAAIA,YAAY,CAACC,MAAM,GAAG,CAAC,EAAE;MAC3C,OAAO;QACL0K,YAAY,EAAE,IAAI,CAACJ,mBAAmB,CAACvK,YAAY,CAAC,CAAC,CAAC,CAACN,IAAI,CAAC,IAAI,IAAI;QACpEkL,aAAa,EAAE5K,YAAY,CAAC,CAAC,CAAC,CAACoG,SAAS,IAAI,IAAI;QAChDyE,QAAQ,EAAE7K,YAAY,CAAC,CAAC,CAAC,CAACsG,KAAK,CAAC7G,IAAI,IAAIO,YAAY,CAAC,CAAC,CAAC,CAACP,IAAI,IAAI;OACjE;IACH,CAAC,MAAM,OAAO2J,SAAS;EAEzB;EAGA0B,UAAUA,CAAA;IACR,IAAI,CAAC3G,KAAK,CAAC4G,KAAK,EAAE;IAClB,IAAIC,iBAAiB,GAAG,KAAK;IAC7B,IAAIC,wBAAwB,GAAG,KAAK;IACpC,IAAIC,kBAAkB,GAAG,KAAK;IAE9B,KAAK,MAAM7F,IAAI,IAAI,IAAI,CAAC8F,mBAAmB,EAAE;MAC3C,IAAI,CAACH,iBAAiB,IAAK,CAAC3F,IAAI,CAACgE,OAAQ,EAAE;QACzC2B,iBAAiB,GAAG,IAAI;MAC1B;MACA,IAAI,CAACC,wBAAwB,IAAK,CAAC5F,IAAI,CAAC7C,cAAe,EAAE;QACvDyI,wBAAwB,GAAG,IAAI;MACjC;MACA,IAAI5F,IAAI,CAACiD,YAAY,IAAIjD,IAAI,CAAC7C,cAAc,EAAE;QAC5C,IAAI6C,IAAI,CAAC7C,cAAc,GAAG6C,IAAI,CAACiD,YAAY,IAAIjD,IAAI,CAAC7C,cAAc,GAAG,CAAC,EAAE;UACtE,IAAI,CAAC2B,KAAK,CAACiH,eAAe,CAAC,QAAQ,GAAG,MAAM,GAAG/F,IAAI,CAACiD,YAAY,GAAG,KAAKjD,IAAI,CAACvD,SAAS,IAAI,CAAC;QAC7F;MACF;MAEA,IAAI,CAACoJ,kBAAkB,IAAK,CAAC7F,IAAI,CAACvD,SAAU,EAAE;QAC5CoJ,kBAAkB,GAAG,IAAI;MAC3B;IACF;IACA,IAAIF,iBAAiB,EAAE;MACrB,IAAI,CAAC7G,KAAK,CAACiH,eAAe,CAAC,UAAU,GAAG,KAAK,CAAC;IAChD;IACA,IAAIH,wBAAwB,EAAE;MAC5B,IAAI,CAAC9G,KAAK,CAACiH,eAAe,CAAC,QAAQ,GAAG,QAAQ,CAAC;IACjD;IACA,IAAIF,kBAAkB,EAAE;MACtB,IAAI,CAAC/G,KAAK,CAACiH,eAAe,CAAC,YAAY,GAAG,KAAK,CAAC;IAClD;EACF;EAIA1H,QAAQA,CAAA;IACN,IAAI,CAACyH,mBAAmB,GAAG,IAAI,CAACnC,kBAAkB,CAACpB,GAAG,CAAEyD,CAAM,IAAI;MAChE,OAAO;QACLxL,cAAc,EAAEwL,CAAC,CAACxL,cAAc,GAAGwL,CAAC,CAACxL,cAAc,GAAG,IAAI;QAC1DyG,KAAK,EAAE+E,CAAC,CAACrL,YAAY,GAAG,IAAI,CAAC0K,UAAU,CAACW,CAAC,CAACrL,YAAY,CAAC,GAAGoJ,SAAS;QACnEF,kBAAkB,EAAE,IAAI,CAACc,oBAAoB,CAACqB,CAAC,CAACpK,aAAa,CAAC;QAC9DyI,WAAW,EAAE2B,CAAC,CAAC3B,WAAW,GAAG2B,CAAC,CAAC3B,WAAW,GAAG,IAAI;QACjD4B,OAAO,EAAE,IAAI,CAAC5G,KAAK,GAAG,IAAI,GAAG,IAAI,CAAC9D,YAAY,CAACuI,OAAO;QACtDjG,KAAK,EAAEmI,CAAC,CAACnI,KAAK;QACdC,KAAK,EAAEkI,CAAC,CAAClI,KAAK;QACdC,SAAS,EAAEiI,CAAC,CAACjI,SAAS;QACtBtB,SAAS,EAAEuJ,CAAC,CAACvJ,SAAS;QAAE;QACxBuF,WAAW,EAAEgE,CAAC,CAAC3I,eAAe,CAACW,KAAK,KAAK,CAAC,GAAG,IAAI,GAAG,IAAI,CAACiH,cAAc,CAACe,CAAC,CAAC3I,eAAe,EAAE2I,CAAC,CAAC/J,kBAAkB,CAAC,IAAI,IAAI;QACxHgH,YAAY,EAAE+C,CAAC,CAAC/C,YAAY;QAC5B9F,cAAc,EAAE6I,CAAC,CAAC7I,cAAc;QAChC6G,OAAO,EAAEgC,CAAC,CAAC3I,eAAe,CAACW;OAC5B;IACH,CAAC,CAAC;IACF,IAAI,CAACyH,UAAU,EAAE;IACjB,IAAI,IAAI,CAAC3G,KAAK,CAACoH,aAAa,CAACtL,MAAM,GAAG,CAAC,EAAE;MACvC,IAAI,CAAC8D,OAAO,CAACyH,aAAa,CAAC,IAAI,CAACrH,KAAK,CAACoH,aAAa,CAAC;MACpD;IACF;IACA,IAAI,IAAI,CAAC7G,KAAK,EAAE;MACd,IAAI,CAAC+G,kBAAkB,EAAE;IAE3B,CAAC,MAAM;MACL,IAAI,CAACC,gBAAgB,EAAE;IACzB;EACF;EAEAA,gBAAgBA,CAAA;IACd,IAAI,CAAC1H,gBAAgB,CAAC2H,oCAAoC,CAAC;MACzDlD,IAAI,EAAE,IAAI,CAAC0C;KACZ,CAAC,CAACtG,SAAS,CAACgE,GAAG,IAAG;MACjB,IAAIA,GAAG,CAACE,UAAU,IAAI,CAAC,EAAE;QACvB,IAAI,CAAChF,OAAO,CAAC6H,aAAa,CAAC,MAAM,CAAC;QAClC;QACA,IAAI,CAACC,MAAM,EAAE;MACf;IACF,CAAC,CAAC;EACJ;EAIAJ,kBAAkBA,CAAA;IAChB,IAAI,CAACK,iBAAiB,GAAG;MACvBpD,YAAY,EAAE,IAAI,CAACzD,WAAW;MAC9B8G,SAAS,EAAE,IAAI,CAACZ,mBAAmB,IAAI,IAAI;MAC3C3G,SAAS,EAAE,IAAI,CAACD,iCAAiC,CAACC;KACnD;IAED,IAAI,CAACR,gBAAgB,CAACgI,sCAAsC,CAAC;MAC3DvD,IAAI,EAAE,IAAI,CAACqD;KACZ,CAAC,CAACjH,SAAS,CAACgE,GAAG,IAAG;MACjB,IAAIA,GAAG,CAACE,UAAU,IAAI,CAAC,EAAE;QACvB,IAAI,CAAChF,OAAO,CAAC6H,aAAa,CAAC,MAAM,CAAC;QAClC;QACA,IAAI,CAACC,MAAM,EAAE;MACf;IACF,CAAC,CAAC;EACJ;EAEAjC,0BAA0BA,CAACqC,CAAW,EAAEC,CAAkG;IACxI,MAAMC,CAAC,GAA+B,EAAE;IACxC,KAAK,MAAM9G,IAAI,IAAI4G,CAAC,EAAE;MACpB,MAAMG,YAAY,GAAGF,CAAC,CAACG,IAAI,CAACC,KAAK,IAAIA,KAAK,CAACC,UAAU,KAAKlH,IAAI,IAAIiH,KAAK,CAACE,SAAS,CAAC;MAClFL,CAAC,CAAC9G,IAAI,CAAC,GAAG,CAAC,CAAC+G,YAAY;IAC1B;IACA,OAAOD,CAAC;EACV,CAAC,CAAC;EAKFjH,iCAAiCA,CAAA;IAC/B,IAAI,CAACjB,yBAAyB,CAACwI,8DAA8D,CAAC;MAC5FhE,IAAI,EAAE,IAAI,CAACxD;KACZ,CAAC,CAAC2D,IAAI,CACL/K,GAAG,CAACgL,GAAG,IAAG;MACR,IAAIA,GAAG,CAACC,OAAO,IAAID,GAAG,CAACE,UAAU,IAAI,CAAC,EAAE;QACtC,IAAI,CAACxF,aAAa,GAAG,IAAI,CAACuG,4BAA4B,CAACjB,GAAG,CAACC,OAAO,CAAC;QACnE,IAAI,CAACQ,eAAe,EAAE;MACxB;IACF,CAAC,CAAC,CACH,CAACzE,SAAS,EAAE;EACf;EACAgH,MAAMA,CAAA;IACJ,IAAI,CAACvH,aAAa,CAACiC,IAAI,CAAC;MACtBmG,MAAM;MACNC,OAAO,EAAE,IAAI,CAAC1H;KACf,CAAC;IACF,IAAI,CAACb,QAAQ,CAACwI,IAAI,EAAE;EAAC;;;uCAjbZjJ,4CAA4C,EAAA1F,EAAA,CAAA4O,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAA9O,EAAA,CAAA4O,iBAAA,CAAAG,EAAA,CAAAC,cAAA,GAAAhP,EAAA,CAAA4O,iBAAA,CAAAK,EAAA,CAAAC,cAAA,GAAAlP,EAAA,CAAA4O,iBAAA,CAAAO,EAAA,CAAAC,eAAA,GAAApP,EAAA,CAAA4O,iBAAA,CAAAO,EAAA,CAAAE,wBAAA,GAAArP,EAAA,CAAA4O,iBAAA,CAAAU,EAAA,CAAAC,cAAA,GAAAvP,EAAA,CAAA4O,iBAAA,CAAAY,EAAA,CAAAC,gBAAA,GAAAzP,EAAA,CAAA4O,iBAAA,CAAAc,EAAA,CAAAC,QAAA,GAAA3P,EAAA,CAAA4O,iBAAA,CAAAO,EAAA,CAAAS,eAAA,GAAA5P,EAAA,CAAA4O,iBAAA,CAAAiB,EAAA,CAAAC,YAAA;IAAA;EAAA;;;YAA5CpK,4CAA4C;MAAAqK,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAAjQ,EAAA,CAAAkQ,0BAAA,EAAAlQ,EAAA,CAAAmQ,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,sDAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UC5CvDzQ,EADF,CAAAC,cAAA,iBAA0B,qBACR;UACdD,EAAA,CAAAiB,SAAA,qBAAiC;UACnCjB,EAAA,CAAAG,YAAA,EAAiB;UACjBH,EAAA,CAAAC,cAAA,mBAAc;UACZD,EAAA,CAAAiB,SAAA,YAA0C;UAGtCjB,EAFJ,CAAAC,cAAA,aAA8B,aACL,YACmB;UAAAD,EAAA,CAAAE,MAAA,4CAAO;UACjDF,EADiD,CAAAG,YAAA,EAAK,EAChD;UACNH,EAAA,CAAA6B,UAAA,IAAA8O,oEAAA,4BAA8E;UAkHlF3Q,EADE,CAAAG,YAAA,EAAM,EACO;UAEbH,EADF,CAAAC,cAAA,yBAAsD,iBACM;UAAnBD,EAAA,CAAAI,UAAA,mBAAAwQ,+EAAA;YAAA,OAASF,GAAA,CAAA9C,MAAA,EAAQ;UAAA,EAAC;UACvD5N,EAAA,CAAAE,MAAA,sBACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACTH,EAAA,CAAA6B,UAAA,KAAAgP,+DAAA,oBAAgF;UAIpF7Q,EADE,CAAAG,YAAA,EAAiB,EACT;;;UA3HkCH,EAAA,CAAAsB,SAAA,GAAuB;UAAvBtB,EAAA,CAAAuB,UAAA,YAAAmP,GAAA,CAAA3F,kBAAA,CAAuB;UAuHZ/K,EAAA,CAAAsB,SAAA,GAA2B;UAA3BtB,EAAA,CAAAuB,UAAA,UAAAmP,GAAA,CAAA/N,YAAA,CAAAC,OAAA,CAA2B;;;qBDvFtElD,YAAY,EAAAgQ,EAAA,CAAAoB,OAAA,EAAApB,EAAA,CAAAqB,OAAA,EAAArB,EAAA,CAAAsB,IAAA,EAAEnR,YAAY,EAAAoR,EAAA,CAAAC,oBAAA,EAAAD,EAAA,CAAAE,mBAAA,EAAAF,EAAA,CAAAG,eAAA,EAAAH,EAAA,CAAAI,OAAA,EAAAC,GAAA,CAAAC,eAAA,EAAAD,GAAA,CAAAE,mBAAA,EAAAF,GAAA,CAAAG,qBAAA,EAAAH,GAAA,CAAAI,qBAAA,EAAAJ,GAAA,CAAAK,mBAAA,EAAAL,GAAA,CAAAM,gBAAA,EAAAN,GAAA,CAAAO,iBAAA,EAAAP,GAAA,CAAAQ,iBAAA,EAAAC,GAAA,CAAAC,mBAAA,EAAAC,GAAA,CAAAC,YAAA,EAAEvS,gBAAgB;MAAAwS,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}