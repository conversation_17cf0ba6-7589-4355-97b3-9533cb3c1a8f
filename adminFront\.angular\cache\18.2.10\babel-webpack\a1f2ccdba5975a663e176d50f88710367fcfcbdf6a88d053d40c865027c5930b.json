{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"./api-configuration\";\nimport * as i2 from \"@angular/common/http\";\n/**\n * Base class for services\n */\nexport class BaseService {\n  constructor(config, http) {\n    this.config = config;\n    this.http = http;\n  }\n  /**\n   * Returns the root url for all operations in this service. If not set directly in this\n   * service, will fallback to `ApiConfiguration.rootUrl`.\n   */\n  get rootUrl() {\n    return this._rootUrl || this.config.rootUrl;\n  }\n  /**\n   * Sets the root URL for API operations in this service.\n   */\n  set rootUrl(rootUrl) {\n    this._rootUrl = rootUrl;\n  }\n  static {\n    this.ɵfac = function BaseService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || BaseService)(i0.ɵɵinject(i1.ApiConfiguration), i0.ɵɵinject(i2.HttpClient));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: BaseService,\n      factory: BaseService.ɵfac\n    });\n  }\n}", "map": {"version": 3, "names": ["BaseService", "constructor", "config", "http", "rootUrl", "_rootUrl", "i0", "ɵɵinject", "i1", "ApiConfiguration", "i2", "HttpClient", "factory", "ɵfac"], "sources": ["C:\\Users\\<USER>\\Documents\\jeansalechange\\adminFront\\src\\services\\api\\base-service.ts"], "sourcesContent": ["/* tslint:disable */\r\n/* eslint-disable */\r\nimport { Injectable } from '@angular/core';\r\nimport { HttpClient } from '@angular/common/http';\r\nimport { ApiConfiguration } from './api-configuration';\r\n\r\n/**\r\n * Base class for services\r\n */\r\n@Injectable()\r\nexport class BaseService {\r\n  constructor(\r\n    protected config: ApiConfiguration,\r\n    protected http: HttpClient\r\n  ) {\r\n  }\r\n\r\n  private _rootUrl?: string;\r\n\r\n  /**\r\n   * Returns the root url for all operations in this service. If not set directly in this\r\n   * service, will fallback to `ApiConfiguration.rootUrl`.\r\n   */\r\n  get rootUrl(): string {\r\n    return this._rootUrl || this.config.rootUrl;\r\n  }\r\n\r\n  /**\r\n   * Sets the root URL for API operations in this service.\r\n   */\r\n  set rootUrl(rootUrl: string) {\r\n    this._rootUrl = rootUrl;\r\n  }\r\n}\r\n"], "mappings": ";;;AAMA;;;AAIA,OAAM,MAAOA,WAAW;EACtBC,YACYC,MAAwB,EACxBC,IAAgB;IADhB,KAAAD,MAAM,GAANA,MAAM;IACN,KAAAC,IAAI,GAAJA,IAAI;EAEhB;EAIA;;;;EAIA,IAAIC,OAAOA,CAAA;IACT,OAAO,IAAI,CAACC,QAAQ,IAAI,IAAI,CAACH,MAAM,CAACE,OAAO;EAC7C;EAEA;;;EAGA,IAAIA,OAAOA,CAACA,OAAe;IACzB,IAAI,CAACC,QAAQ,GAAGD,OAAO;EACzB;;;uCAtBWJ,WAAW,EAAAM,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,gBAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,UAAA;IAAA;EAAA;;;aAAXX,WAAW;MAAAY,OAAA,EAAXZ,WAAW,CAAAa;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}