{"ast": null, "code": "import { map } from 'rxjs/operators';\nimport { CQuotationItemType } from '../models/quotation.model';\nimport { EnumQuotationItemType } from '../../services/api/models/enum-quotation-item-type';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../services/api/services/quotation.service\";\nimport * as i2 from \"@angular/common/http\";\nexport class QuotationService {\n  constructor(apiQuotationService, http) {\n    this.apiQuotationService = apiQuotationService;\n    this.http = http;\n    this.apiUrl = '/api/Quotation';\n  }\n  // 將 EnumQuotationItemType 轉換為 CQuotationItemType\n  mapEnumToCQuotationType(enumType) {\n    switch (enumType) {\n      case EnumQuotationItemType.客變需求:\n        return CQuotationItemType.客變需求;\n      case EnumQuotationItemType.選樣:\n        return CQuotationItemType.選樣;\n      case EnumQuotationItemType.自定義:\n      default:\n        return CQuotationItemType.自定義;\n    }\n  }\n  // 取得報價單列表\n  getQuotationList(request) {\n    return this.apiQuotationService.apiQuotationGetListPost$Json({\n      body: request\n    });\n  }\n  // 取得單筆報價單資料\n  getQuotationData(quotationId) {\n    const request = {\n      CQuotationID: quotationId\n    };\n    return this.apiQuotationService.apiQuotationGetDataPost$Json({\n      body: request\n    });\n  } // 取得戶別的報價項目\n  getQuotationByHouseId(houseId) {\n    const request = {\n      CHouseID: houseId\n    };\n    return this.apiQuotationService.apiQuotationGetListByHouseIdPost$Json({\n      body: request\n    }).pipe(map(response => {\n      // 將 API 響應的小寫屬性名轉換為前端期望的大寫屬性名\n      if (response) {\n        return {\n          StatusCode: response.StatusCode,\n          Message: response.Message,\n          TotalItems: response.TotalItems,\n          Entries: response.Entries // 保持原始結構，因為 entries 是一個物件而不是陣列\n        };\n      }\n      return response;\n    }));\n  }\n  // 儲存報價單 (支援單一項目)\n  saveQuotationItem(quotation) {\n    return this.apiQuotationService.apiQuotationSaveDataPost$Json({\n      body: quotation\n    });\n  } // 儲存報價單 (支援批量保存多個項目) - 使用單一請求\n  saveQuotation(request) {\n    // 將 QuotationItem 轉換為 QuotationItemModel 格式\n    const quotationItems = request.items.map(item => {\n      const quotationType = item.CQuotationItemType && item.CQuotationItemType > 0 ? item.CQuotationItemType : CQuotationItemType.自定義;\n      return {\n        CItemName: item.cItemName,\n        CUnit: item.cUnit || '',\n        CUnitPrice: item.cUnitPrice,\n        CCount: item.cCount,\n        CStatus: item.cStatus || 1,\n        CQuotationItemType: quotationType,\n        CRemark: item.cRemark || ''\n      };\n    });\n    // 建立 SaveDataQuotation 請求，並直接添加額外費用欄位\n    const saveRequest = {\n      CHouseID: request.houseId,\n      CQuotationVersionId: request.quotationId || 0,\n      // 使用傳入的 quotationId，如果沒有則為 0（新建報價單）\n      Items: quotationItems,\n      // 額外費用相關欄位 - 直接添加到請求中\n      CShowOther: request.cShowOther || false,\n      COtherName: request.cOtherName || '',\n      COtherPercent: request.cOtherPercent || 0\n    };\n    // 調試用 - 印出最終送出的請求資料\n    console.log('QuotationService saveRequest:', saveRequest);\n    return this.apiQuotationService.apiQuotationSaveDataPost$Json({\n      body: saveRequest\n    }).pipe(map(response => ({\n      success: response?.StatusCode === 0,\n      message: response?.StatusCode === 0 ? '報價單保存成功' : '報價單保存失敗',\n      data: request.items\n    })));\n  }\n  // 取得預設報價項目 (保持原有方法以兼容現有代碼)\n  getDefaultQuotationItems() {\n    // 使用 GetList 方法獲取預設項目\n    const request = {\n      PageIndex: 0,\n      PageSize: 100\n      // 其他預設參數可能需要根據實際 API 需求調整\n    };\n    return this.apiQuotationService.apiQuotationGetListPost$Json({\n      body: request\n    }).pipe(map(response => {\n      // 將 GetQuotation 轉換為 QuotationItem\n      const quotationItems = (response.Entries || []).map(item => ({\n        cHouseID: item.CHouseID || 0,\n        cItemName: item.CItemName || '',\n        cUnit: item.CUnit || '',\n        cUnitPrice: item.CUnitPrice || 0,\n        cCount: item.CCount || 0,\n        cStatus: item.CStatus || 1,\n        CQuotationItemType: item.CQuotationItemType || CQuotationItemType.自定義,\n        cRemark: item.CRemark || ''\n      }));\n      // 轉換 API 響應格式以保持兼容性\n      return {\n        success: response.StatusCode === 0,\n        // 假設 statusCode 0 表示成功\n        message: response.Message || '',\n        data: quotationItems\n      };\n    }));\n  } // 載入預設報價項目 (LoadDefaultItems API)\n  loadDefaultItems(request) {\n    return this.apiQuotationService.apiQuotationLoadDefaultItemsPost$Json({\n      body: request\n    }).pipe(map(response => {\n      // 將 GetQuotation 轉換為 QuotationItem\n      const quotationItems = (response.Entries || []).map(item => ({\n        cHouseID: item.CHouseID || 0,\n        cItemName: item.CItemName || '',\n        cUnit: item.CUnit || '',\n        cUnitPrice: item.CUnitPrice || 0,\n        cCount: item.CCount || 0,\n        cStatus: item.CStatus || 1,\n        CQuotationItemType: item.CQuotationItemType || CQuotationItemType.自定義,\n        cRemark: item.CRemark || ''\n      }));\n      // 轉換 API 響應格式以保持兼容性\n      return {\n        success: response.StatusCode === 0,\n        // 假設 StatusCode 0 表示成功\n        message: response.Message || '',\n        data: quotationItems\n      };\n    }));\n  } // 載入常規報價項目 (LoadRegularItems API)\n  loadRegularItems(request) {\n    return this.apiQuotationService.apiQuotationLoadRegularItemsPost$Json({\n      body: request\n    }).pipe(map(response => {\n      // 將 GetQuotation 轉換為 QuotationItem\n      const quotationItems = (response.Entries || []).map(item => ({\n        cHouseID: item.CHouseID || 0,\n        cItemName: item.CItemName || '',\n        cUnit: item.CUnit || '',\n        cUnitPrice: item.CUnitPrice || 0,\n        cCount: item.CCount || 0,\n        cStatus: item.CStatus || 1,\n        CQuotationItemType: item.CQuotationItemType || CQuotationItemType.自定義,\n        cRemark: item.CRemark || ''\n      }));\n      // 轉換 API 響應格式以保持兼容性\n      return {\n        success: response.StatusCode === 0,\n        // 假設 StatusCode 0 表示成功\n        message: response.Message || '',\n        data: quotationItems\n      };\n    }));\n  }\n  // 更新報價項目 (使用 SaveData 方法)\n  updateQuotationItem(quotationId, item) {\n    const saveData = {\n      CHouseID: item.cHouseID,\n      CQuotationVersionId: quotationId,\n      Items: [{\n        CItemName: item.cItemName,\n        CUnitPrice: item.cUnitPrice,\n        CCount: item.cCount,\n        CStatus: item.cStatus || 1,\n        CQuotationItemType: item.CQuotationItemType || CQuotationItemType.自定義\n      }]\n    };\n    return this.apiQuotationService.apiQuotationSaveDataPost$Json({\n      body: saveData\n    }).pipe(map(response => {\n      return {\n        success: response.StatusCode === 0,\n        // 假設 StatusCode 0 表示成功\n        message: response.Message || '',\n        data: [item] // 包裝為陣列以符合 QuotationResponse 格式\n      };\n    }));\n  }\n  // 匯出報價單 (需要另外實作，因為 swagger 中沒有此 API)\n  exportQuotation(houseId) {\n    // 這個方法可能需要使用其他 API 或保持原有實作\n    // 暫時拋出錯誤提示需要實作\n    throw new Error('Export quotation functionality needs to be implemented separately');\n  }\n  // 鎖定報價單\n  lockQuotation(quotationId) {\n    return this.apiQuotationService.apiQuotationLockQuotationPost$Json({\n      body: quotationId\n    }).pipe(map(response => {\n      return {\n        success: response.StatusCode === 0,\n        message: response.Message || '',\n        data: response.Entries || null\n      };\n    }));\n  }\n  static {\n    this.ɵfac = function QuotationService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || QuotationService)(i0.ɵɵinject(i1.QuotationService), i0.ɵɵinject(i2.HttpClient));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: QuotationService,\n      factory: QuotationService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["map", "CQuotationItemType", "EnumQuotationItemType", "QuotationService", "constructor", "apiQuotationService", "http", "apiUrl", "mapEnumToCQuotationType", "enumType", "客變需求", "選樣", "自定義", "getQuotationList", "request", "apiQuotationGetListPost$Json", "body", "getQuotationData", "quotationId", "CQuotationID", "apiQuotationGetDataPost$Json", "getQuotationByHouseId", "houseId", "CHouseID", "apiQuotationGetListByHouseIdPost$Json", "pipe", "response", "StatusCode", "Message", "TotalItems", "Entries", "saveQuotationItem", "quotation", "apiQuotationSaveDataPost$Json", "saveQuotation", "quotationItems", "items", "item", "quotationType", "CItemName", "cItemName", "CUnit", "cUnit", "CUnitPrice", "cUnitPrice", "CCount", "cCount", "CStatus", "cStatus", "CRemark", "cRemark", "saveRequest", "CQuotationVersionId", "Items", "CShowOther", "cShowOther", "COtherName", "cOtherName", "COtherPercent", "cOtherPercent", "console", "log", "success", "message", "data", "getDefaultQuotationItems", "PageIndex", "PageSize", "cHouseID", "loadDefaultItems", "apiQuotationLoadDefaultItemsPost$Json", "loadRegularItems", "apiQuotationLoadRegularItemsPost$Json", "updateQuotationItem", "saveData", "exportQuotation", "Error", "lockQuotation", "apiQuotationLockQuotationPost$Json", "i0", "ɵɵinject", "i1", "i2", "HttpClient", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\services\\quotation.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport { Observable } from 'rxjs';\r\nimport { map, switchMap } from 'rxjs/operators';\r\nimport { HttpClient } from '@angular/common/http';\r\nimport { QuotationItem, QuotationRequest, QuotationResponse, CQuotationItemType } from '../models/quotation.model';\r\nimport { QuotationService as ApiQuotationService } from '../../services/api/services/quotation.service';\r\nimport { EnumQuotationItemType } from '../../services/api/models/enum-quotation-item-type';\r\nimport {\r\n  GetListQuotationRequest,\r\n  GetQuotationByIdRequest,\r\n  SaveDataQuotation,\r\n  QuotationItemModel,\r\n  GetListByHouseIdRequest,\r\n  LoadDefaultItemsRequest\r\n} from '../../services/api/models';\r\n\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class QuotationService {\r\n  private readonly apiUrl = '/api/Quotation';\r\n\r\n  constructor(\r\n    private apiQuotationService: ApiQuotationService,\r\n    private http: HttpClient\r\n  ) { }\r\n\r\n  // 將 EnumQuotationItemType 轉換為 CQuotationItemType\r\n  private mapEnumToCQuotationType(enumType?: EnumQuotationItemType): CQuotationItemType {\r\n    switch (enumType) {\r\n      case EnumQuotationItemType.客變需求:\r\n        return CQuotationItemType.客變需求;\r\n      case EnumQuotationItemType.選樣:\r\n        return CQuotationItemType.選樣;\r\n      case EnumQuotationItemType.自定義:\r\n      default:\r\n        return CQuotationItemType.自定義;\r\n    }\r\n  }\r\n  // 取得報價單列表\r\n  getQuotationList(request: GetListQuotationRequest): Observable<any> {\r\n    return this.apiQuotationService.apiQuotationGetListPost$Json({ body: request });\r\n  }\r\n  // 取得單筆報價單資料\r\n  getQuotationData(quotationId: number): Observable<any> {\r\n    const request: GetQuotationByIdRequest = { CQuotationID: quotationId };\r\n    return this.apiQuotationService.apiQuotationGetDataPost$Json({ body: request });\r\n  }  // 取得戶別的報價項目\r\n  getQuotationByHouseId(houseId: number): Observable<any> {\r\n    const request: GetListByHouseIdRequest = { CHouseID: houseId };\r\n    return this.apiQuotationService.apiQuotationGetListByHouseIdPost$Json({ body: request }).pipe(\r\n      map(response => {\r\n        // 將 API 響應的小寫屬性名轉換為前端期望的大寫屬性名\r\n        if (response) {\r\n          return {\r\n            StatusCode: response.StatusCode,\r\n            Message: response.Message,\r\n            TotalItems: response.TotalItems,\r\n            Entries: response.Entries // 保持原始結構，因為 entries 是一個物件而不是陣列\r\n          };\r\n        }\r\n        return response;\r\n      })\r\n    );\r\n  }\r\n  // 儲存報價單 (支援單一項目)\r\n  saveQuotationItem(quotation: SaveDataQuotation): Observable<any> {\r\n    return this.apiQuotationService.apiQuotationSaveDataPost$Json({ body: quotation });\r\n  }  // 儲存報價單 (支援批量保存多個項目) - 使用單一請求\r\n  saveQuotation(request: {\r\n    houseId: number;\r\n    items: QuotationItem[];\r\n    quotationId?: number;\r\n    cShowOther?: boolean;\r\n    cOtherName?: string;\r\n    cOtherPercent?: number;\r\n  }): Observable<QuotationResponse> {\r\n    // 將 QuotationItem 轉換為 QuotationItemModel 格式\r\n    const quotationItems: QuotationItemModel[] = request.items.map(item => {\r\n      const quotationType = item.CQuotationItemType && item.CQuotationItemType > 0 ? item.CQuotationItemType : CQuotationItemType.自定義;\r\n\r\n\r\n      return {\r\n        CItemName: item.cItemName,\r\n        CUnit: item.cUnit || '',\r\n        CUnitPrice: item.cUnitPrice,\r\n        CCount: item.cCount,\r\n        CStatus: item.cStatus || 1,\r\n        CQuotationItemType: quotationType,\r\n        CRemark: item.cRemark || ''\r\n      };\r\n    });\r\n\r\n    // 建立 SaveDataQuotation 請求，並直接添加額外費用欄位\r\n    const saveRequest: any = {\r\n      CHouseID: request.houseId,\r\n      CQuotationVersionId: request.quotationId || 0, // 使用傳入的 quotationId，如果沒有則為 0（新建報價單）\r\n      Items: quotationItems,\r\n      // 額外費用相關欄位 - 直接添加到請求中\r\n      CShowOther: request.cShowOther || false,\r\n      COtherName: request.cOtherName || '',\r\n      COtherPercent: request.cOtherPercent || 0\r\n    };\r\n\r\n    // 調試用 - 印出最終送出的請求資料\r\n    console.log('QuotationService saveRequest:', saveRequest);\r\n\r\n    return this.apiQuotationService.apiQuotationSaveDataPost$Json({ body: saveRequest }).pipe(\r\n      map(response => ({\r\n        success: response?.StatusCode === 0,\r\n        message: response?.StatusCode === 0 ? '報價單保存成功' : '報價單保存失敗',\r\n        data: request.items\r\n      } as QuotationResponse))\r\n    );\r\n  }\r\n\r\n  // 取得預設報價項目 (保持原有方法以兼容現有代碼)\r\n  getDefaultQuotationItems(): Observable<QuotationResponse> {\r\n    // 使用 GetList 方法獲取預設項目\r\n    const request: GetListQuotationRequest = {\r\n      PageIndex: 0,\r\n      PageSize: 100,\r\n      // 其他預設參數可能需要根據實際 API 需求調整\r\n    };\r\n    return this.apiQuotationService.apiQuotationGetListPost$Json({ body: request }).pipe(\r\n      map(response => {\r\n        // 將 GetQuotation 轉換為 QuotationItem\r\n        const quotationItems: QuotationItem[] = (response.Entries || []).map(item => ({\r\n          cHouseID: item.CHouseID || 0,\r\n          cItemName: item.CItemName || '',\r\n          cUnit: item.CUnit || '',\r\n          cUnitPrice: item.CUnitPrice || 0,\r\n          cCount: item.CCount || 0,\r\n          cStatus: item.CStatus || 1,\r\n          CQuotationItemType: item.CQuotationItemType as CQuotationItemType || CQuotationItemType.自定義,\r\n          cRemark: item.CRemark || ''\r\n        }));\r\n        \r\n        // 轉換 API 響應格式以保持兼容性\r\n        return {\r\n          success: response.StatusCode === 0, // 假設 statusCode 0 表示成功\r\n          message: response.Message || '',\r\n          data: quotationItems\r\n        } as QuotationResponse;\r\n      })\r\n    );\r\n  }  // 載入預設報價項目 (LoadDefaultItems API)\r\n  loadDefaultItems(request: LoadDefaultItemsRequest): Observable<QuotationResponse> {\r\n    return this.apiQuotationService.apiQuotationLoadDefaultItemsPost$Json({ body: request }).pipe(\r\n      map(response => {\r\n        // 將 GetQuotation 轉換為 QuotationItem\r\n        const quotationItems: QuotationItem[] = (response.Entries || []).map(item => ({\r\n          cHouseID: item.CHouseID || 0,\r\n          cItemName: item.CItemName || '',\r\n          cUnit: item.CUnit || '',\r\n          cUnitPrice: item.CUnitPrice || 0,\r\n          cCount: item.CCount || 0,\r\n          cStatus: item.CStatus || 1,\r\n          CQuotationItemType: item.CQuotationItemType || CQuotationItemType.自定義,\r\n          cRemark: item.CRemark || ''\r\n        }));\r\n        \r\n        // 轉換 API 響應格式以保持兼容性\r\n        return {\r\n          success: response.StatusCode === 0, // 假設 StatusCode 0 表示成功\r\n          message: response.Message || '',\r\n          data: quotationItems\r\n        } as QuotationResponse;\r\n      })\r\n    );\r\n  }  // 載入常規報價項目 (LoadRegularItems API)\r\n  loadRegularItems(request: LoadDefaultItemsRequest): Observable<QuotationResponse> {\r\n    return this.apiQuotationService.apiQuotationLoadRegularItemsPost$Json({ body: request }).pipe(\r\n      map(response => {\r\n        // 將 GetQuotation 轉換為 QuotationItem\r\n        const quotationItems: QuotationItem[] = (response.Entries || []).map(item => ({\r\n          cHouseID: item.CHouseID || 0,\r\n          cItemName: item.CItemName || '',\r\n          cUnit: item.CUnit || '',\r\n          cUnitPrice: item.CUnitPrice || 0,\r\n          cCount: item.CCount || 0,\r\n          cStatus: item.CStatus || 1,\r\n          CQuotationItemType: item.CQuotationItemType || CQuotationItemType.自定義,\r\n          cRemark: item.CRemark || ''\r\n        }));\r\n        \r\n        // 轉換 API 響應格式以保持兼容性\r\n        return {\r\n          success: response.StatusCode === 0, // 假設 StatusCode 0 表示成功\r\n          message: response.Message || '',\r\n          data: quotationItems\r\n        } as QuotationResponse;\r\n      })\r\n    );\r\n  }\r\n\r\n  // 更新報價項目 (使用 SaveData 方法)\r\n  updateQuotationItem(quotationId: number, item: QuotationItem): Observable<QuotationResponse> {\r\n    const saveData: SaveDataQuotation = {\r\n      CHouseID: item.cHouseID,\r\n      CQuotationVersionId: quotationId,\r\n      Items: [{\r\n        CItemName: item.cItemName,\r\n        CUnitPrice: item.cUnitPrice,\r\n        CCount: item.cCount,\r\n        CStatus: item.cStatus || 1,\r\n        CQuotationItemType: item.CQuotationItemType || CQuotationItemType.自定義\r\n      }]\r\n    };\r\n    return this.apiQuotationService.apiQuotationSaveDataPost$Json({ body: saveData }).pipe(\r\n      map(response => {\r\n        return {\r\n          success: response.StatusCode === 0, // 假設 StatusCode 0 表示成功\r\n          message: response.Message || '',\r\n          data: [item] // 包裝為陣列以符合 QuotationResponse 格式\r\n        } as QuotationResponse;\r\n      })\r\n    );\r\n  }\r\n\r\n  // 匯出報價單 (需要另外實作，因為 swagger 中沒有此 API)\r\n  exportQuotation(houseId: number): Observable<Blob> {\r\n    // 這個方法可能需要使用其他 API 或保持原有實作\r\n    // 暫時拋出錯誤提示需要實作\r\n    throw new Error('Export quotation functionality needs to be implemented separately');\r\n  }\r\n\r\n  // 鎖定報價單\r\n  lockQuotation(quotationId: number): Observable<any> {\r\n    return this.apiQuotationService.apiQuotationLockQuotationPost$Json({ body: quotationId }).pipe(\r\n      map(response => {\r\n        return {\r\n          success: response.StatusCode === 0,\r\n          message: response.Message || '',\r\n          data: response.Entries || null\r\n        };\r\n      })\r\n    );\r\n  }\r\n}\r\n"], "mappings": "AAEA,SAASA,GAAG,QAAmB,gBAAgB;AAE/C,SAA6DC,kBAAkB,QAAQ,2BAA2B;AAElH,SAASC,qBAAqB,QAAQ,oDAAoD;;;;AAa1F,OAAM,MAAOC,gBAAgB;EAG3BC,YACUC,mBAAwC,EACxCC,IAAgB;IADhB,KAAAD,mBAAmB,GAAnBA,mBAAmB;IACnB,KAAAC,IAAI,GAAJA,IAAI;IAJG,KAAAC,MAAM,GAAG,gBAAgB;EAKtC;EAEJ;EACQC,uBAAuBA,CAACC,QAAgC;IAC9D,QAAQA,QAAQ;MACd,KAAKP,qBAAqB,CAACQ,IAAI;QAC7B,OAAOT,kBAAkB,CAACS,IAAI;MAChC,KAAKR,qBAAqB,CAACS,EAAE;QAC3B,OAAOV,kBAAkB,CAACU,EAAE;MAC9B,KAAKT,qBAAqB,CAACU,GAAG;MAC9B;QACE,OAAOX,kBAAkB,CAACW,GAAG;IACjC;EACF;EACA;EACAC,gBAAgBA,CAACC,OAAgC;IAC/C,OAAO,IAAI,CAACT,mBAAmB,CAACU,4BAA4B,CAAC;MAAEC,IAAI,EAAEF;IAAO,CAAE,CAAC;EACjF;EACA;EACAG,gBAAgBA,CAACC,WAAmB;IAClC,MAAMJ,OAAO,GAA4B;MAAEK,YAAY,EAAED;IAAW,CAAE;IACtE,OAAO,IAAI,CAACb,mBAAmB,CAACe,4BAA4B,CAAC;MAAEJ,IAAI,EAAEF;IAAO,CAAE,CAAC;EACjF,CAAC,CAAE;EACHO,qBAAqBA,CAACC,OAAe;IACnC,MAAMR,OAAO,GAA4B;MAAES,QAAQ,EAAED;IAAO,CAAE;IAC9D,OAAO,IAAI,CAACjB,mBAAmB,CAACmB,qCAAqC,CAAC;MAAER,IAAI,EAAEF;IAAO,CAAE,CAAC,CAACW,IAAI,CAC3FzB,GAAG,CAAC0B,QAAQ,IAAG;MACb;MACA,IAAIA,QAAQ,EAAE;QACZ,OAAO;UACLC,UAAU,EAAED,QAAQ,CAACC,UAAU;UAC/BC,OAAO,EAAEF,QAAQ,CAACE,OAAO;UACzBC,UAAU,EAAEH,QAAQ,CAACG,UAAU;UAC/BC,OAAO,EAAEJ,QAAQ,CAACI,OAAO,CAAC;SAC3B;MACH;MACA,OAAOJ,QAAQ;IACjB,CAAC,CAAC,CACH;EACH;EACA;EACAK,iBAAiBA,CAACC,SAA4B;IAC5C,OAAO,IAAI,CAAC3B,mBAAmB,CAAC4B,6BAA6B,CAAC;MAAEjB,IAAI,EAAEgB;IAAS,CAAE,CAAC;EACpF,CAAC,CAAE;EACHE,aAAaA,CAACpB,OAOb;IACC;IACA,MAAMqB,cAAc,GAAyBrB,OAAO,CAACsB,KAAK,CAACpC,GAAG,CAACqC,IAAI,IAAG;MACpE,MAAMC,aAAa,GAAGD,IAAI,CAACpC,kBAAkB,IAAIoC,IAAI,CAACpC,kBAAkB,GAAG,CAAC,GAAGoC,IAAI,CAACpC,kBAAkB,GAAGA,kBAAkB,CAACW,GAAG;MAG/H,OAAO;QACL2B,SAAS,EAAEF,IAAI,CAACG,SAAS;QACzBC,KAAK,EAAEJ,IAAI,CAACK,KAAK,IAAI,EAAE;QACvBC,UAAU,EAAEN,IAAI,CAACO,UAAU;QAC3BC,MAAM,EAAER,IAAI,CAACS,MAAM;QACnBC,OAAO,EAAEV,IAAI,CAACW,OAAO,IAAI,CAAC;QAC1B/C,kBAAkB,EAAEqC,aAAa;QACjCW,OAAO,EAAEZ,IAAI,CAACa,OAAO,IAAI;OAC1B;IACH,CAAC,CAAC;IAEF;IACA,MAAMC,WAAW,GAAQ;MACvB5B,QAAQ,EAAET,OAAO,CAACQ,OAAO;MACzB8B,mBAAmB,EAAEtC,OAAO,CAACI,WAAW,IAAI,CAAC;MAAE;MAC/CmC,KAAK,EAAElB,cAAc;MACrB;MACAmB,UAAU,EAAExC,OAAO,CAACyC,UAAU,IAAI,KAAK;MACvCC,UAAU,EAAE1C,OAAO,CAAC2C,UAAU,IAAI,EAAE;MACpCC,aAAa,EAAE5C,OAAO,CAAC6C,aAAa,IAAI;KACzC;IAED;IACAC,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAEV,WAAW,CAAC;IAEzD,OAAO,IAAI,CAAC9C,mBAAmB,CAAC4B,6BAA6B,CAAC;MAAEjB,IAAI,EAAEmC;IAAW,CAAE,CAAC,CAAC1B,IAAI,CACvFzB,GAAG,CAAC0B,QAAQ,KAAK;MACfoC,OAAO,EAAEpC,QAAQ,EAAEC,UAAU,KAAK,CAAC;MACnCoC,OAAO,EAAErC,QAAQ,EAAEC,UAAU,KAAK,CAAC,GAAG,SAAS,GAAG,SAAS;MAC3DqC,IAAI,EAAElD,OAAO,CAACsB;KACO,EAAC,CACzB;EACH;EAEA;EACA6B,wBAAwBA,CAAA;IACtB;IACA,MAAMnD,OAAO,GAA4B;MACvCoD,SAAS,EAAE,CAAC;MACZC,QAAQ,EAAE;MACV;KACD;IACD,OAAO,IAAI,CAAC9D,mBAAmB,CAACU,4BAA4B,CAAC;MAAEC,IAAI,EAAEF;IAAO,CAAE,CAAC,CAACW,IAAI,CAClFzB,GAAG,CAAC0B,QAAQ,IAAG;MACb;MACA,MAAMS,cAAc,GAAoB,CAACT,QAAQ,CAACI,OAAO,IAAI,EAAE,EAAE9B,GAAG,CAACqC,IAAI,KAAK;QAC5E+B,QAAQ,EAAE/B,IAAI,CAACd,QAAQ,IAAI,CAAC;QAC5BiB,SAAS,EAAEH,IAAI,CAACE,SAAS,IAAI,EAAE;QAC/BG,KAAK,EAAEL,IAAI,CAACI,KAAK,IAAI,EAAE;QACvBG,UAAU,EAAEP,IAAI,CAACM,UAAU,IAAI,CAAC;QAChCG,MAAM,EAAET,IAAI,CAACQ,MAAM,IAAI,CAAC;QACxBG,OAAO,EAAEX,IAAI,CAACU,OAAO,IAAI,CAAC;QAC1B9C,kBAAkB,EAAEoC,IAAI,CAACpC,kBAAwC,IAAIA,kBAAkB,CAACW,GAAG;QAC3FsC,OAAO,EAAEb,IAAI,CAACY,OAAO,IAAI;OAC1B,CAAC,CAAC;MAEH;MACA,OAAO;QACLa,OAAO,EAAEpC,QAAQ,CAACC,UAAU,KAAK,CAAC;QAAE;QACpCoC,OAAO,EAAErC,QAAQ,CAACE,OAAO,IAAI,EAAE;QAC/BoC,IAAI,EAAE7B;OACc;IACxB,CAAC,CAAC,CACH;EACH,CAAC,CAAE;EACHkC,gBAAgBA,CAACvD,OAAgC;IAC/C,OAAO,IAAI,CAACT,mBAAmB,CAACiE,qCAAqC,CAAC;MAAEtD,IAAI,EAAEF;IAAO,CAAE,CAAC,CAACW,IAAI,CAC3FzB,GAAG,CAAC0B,QAAQ,IAAG;MACb;MACA,MAAMS,cAAc,GAAoB,CAACT,QAAQ,CAACI,OAAO,IAAI,EAAE,EAAE9B,GAAG,CAACqC,IAAI,KAAK;QAC5E+B,QAAQ,EAAE/B,IAAI,CAACd,QAAQ,IAAI,CAAC;QAC5BiB,SAAS,EAAEH,IAAI,CAACE,SAAS,IAAI,EAAE;QAC/BG,KAAK,EAAEL,IAAI,CAACI,KAAK,IAAI,EAAE;QACvBG,UAAU,EAAEP,IAAI,CAACM,UAAU,IAAI,CAAC;QAChCG,MAAM,EAAET,IAAI,CAACQ,MAAM,IAAI,CAAC;QACxBG,OAAO,EAAEX,IAAI,CAACU,OAAO,IAAI,CAAC;QAC1B9C,kBAAkB,EAAEoC,IAAI,CAACpC,kBAAkB,IAAIA,kBAAkB,CAACW,GAAG;QACrEsC,OAAO,EAAEb,IAAI,CAACY,OAAO,IAAI;OAC1B,CAAC,CAAC;MAEH;MACA,OAAO;QACLa,OAAO,EAAEpC,QAAQ,CAACC,UAAU,KAAK,CAAC;QAAE;QACpCoC,OAAO,EAAErC,QAAQ,CAACE,OAAO,IAAI,EAAE;QAC/BoC,IAAI,EAAE7B;OACc;IACxB,CAAC,CAAC,CACH;EACH,CAAC,CAAE;EACHoC,gBAAgBA,CAACzD,OAAgC;IAC/C,OAAO,IAAI,CAACT,mBAAmB,CAACmE,qCAAqC,CAAC;MAAExD,IAAI,EAAEF;IAAO,CAAE,CAAC,CAACW,IAAI,CAC3FzB,GAAG,CAAC0B,QAAQ,IAAG;MACb;MACA,MAAMS,cAAc,GAAoB,CAACT,QAAQ,CAACI,OAAO,IAAI,EAAE,EAAE9B,GAAG,CAACqC,IAAI,KAAK;QAC5E+B,QAAQ,EAAE/B,IAAI,CAACd,QAAQ,IAAI,CAAC;QAC5BiB,SAAS,EAAEH,IAAI,CAACE,SAAS,IAAI,EAAE;QAC/BG,KAAK,EAAEL,IAAI,CAACI,KAAK,IAAI,EAAE;QACvBG,UAAU,EAAEP,IAAI,CAACM,UAAU,IAAI,CAAC;QAChCG,MAAM,EAAET,IAAI,CAACQ,MAAM,IAAI,CAAC;QACxBG,OAAO,EAAEX,IAAI,CAACU,OAAO,IAAI,CAAC;QAC1B9C,kBAAkB,EAAEoC,IAAI,CAACpC,kBAAkB,IAAIA,kBAAkB,CAACW,GAAG;QACrEsC,OAAO,EAAEb,IAAI,CAACY,OAAO,IAAI;OAC1B,CAAC,CAAC;MAEH;MACA,OAAO;QACLa,OAAO,EAAEpC,QAAQ,CAACC,UAAU,KAAK,CAAC;QAAE;QACpCoC,OAAO,EAAErC,QAAQ,CAACE,OAAO,IAAI,EAAE;QAC/BoC,IAAI,EAAE7B;OACc;IACxB,CAAC,CAAC,CACH;EACH;EAEA;EACAsC,mBAAmBA,CAACvD,WAAmB,EAAEmB,IAAmB;IAC1D,MAAMqC,QAAQ,GAAsB;MAClCnD,QAAQ,EAAEc,IAAI,CAAC+B,QAAQ;MACvBhB,mBAAmB,EAAElC,WAAW;MAChCmC,KAAK,EAAE,CAAC;QACNd,SAAS,EAAEF,IAAI,CAACG,SAAS;QACzBG,UAAU,EAAEN,IAAI,CAACO,UAAU;QAC3BC,MAAM,EAAER,IAAI,CAACS,MAAM;QACnBC,OAAO,EAAEV,IAAI,CAACW,OAAO,IAAI,CAAC;QAC1B/C,kBAAkB,EAAEoC,IAAI,CAACpC,kBAAkB,IAAIA,kBAAkB,CAACW;OACnE;KACF;IACD,OAAO,IAAI,CAACP,mBAAmB,CAAC4B,6BAA6B,CAAC;MAAEjB,IAAI,EAAE0D;IAAQ,CAAE,CAAC,CAACjD,IAAI,CACpFzB,GAAG,CAAC0B,QAAQ,IAAG;MACb,OAAO;QACLoC,OAAO,EAAEpC,QAAQ,CAACC,UAAU,KAAK,CAAC;QAAE;QACpCoC,OAAO,EAAErC,QAAQ,CAACE,OAAO,IAAI,EAAE;QAC/BoC,IAAI,EAAE,CAAC3B,IAAI,CAAC,CAAC;OACO;IACxB,CAAC,CAAC,CACH;EACH;EAEA;EACAsC,eAAeA,CAACrD,OAAe;IAC7B;IACA;IACA,MAAM,IAAIsD,KAAK,CAAC,mEAAmE,CAAC;EACtF;EAEA;EACAC,aAAaA,CAAC3D,WAAmB;IAC/B,OAAO,IAAI,CAACb,mBAAmB,CAACyE,kCAAkC,CAAC;MAAE9D,IAAI,EAAEE;IAAW,CAAE,CAAC,CAACO,IAAI,CAC5FzB,GAAG,CAAC0B,QAAQ,IAAG;MACb,OAAO;QACLoC,OAAO,EAAEpC,QAAQ,CAACC,UAAU,KAAK,CAAC;QAClCoC,OAAO,EAAErC,QAAQ,CAACE,OAAO,IAAI,EAAE;QAC/BoC,IAAI,EAAEtC,QAAQ,CAACI,OAAO,IAAI;OAC3B;IACH,CAAC,CAAC,CACH;EACH;;;uCA3NW3B,gBAAgB,EAAA4E,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAA9E,gBAAA,GAAA4E,EAAA,CAAAC,QAAA,CAAAE,EAAA,CAAAC,UAAA;IAAA;EAAA;;;aAAhBhF,gBAAgB;MAAAiF,OAAA,EAAhBjF,gBAAgB,CAAAkF,IAAA;MAAAC,UAAA,EAFf;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}