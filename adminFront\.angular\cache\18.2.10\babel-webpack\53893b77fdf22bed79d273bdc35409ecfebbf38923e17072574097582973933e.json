{"ast": null, "code": "import { NbCardModule, NbCheckboxModule, NbInputModule, NbOptionModule, NbSelectModule } from '@nebular/theme';\nimport { FormsModule } from '@angular/forms';\nimport { <PERSON><PERSON><PERSON>, NgIf } from '@angular/common';\nimport { PaginationComponent } from '../../components/pagination/pagination.component';\nimport { BaseComponent } from '../../components/base/baseComponent';\nimport * as moment from 'moment';\nimport { FullCalendarModule } from '@fullcalendar/angular';\nimport interactionPlugin from '@fullcalendar/interaction';\nimport dayGridPlugin from '@fullcalendar/daygrid';\nimport timeGridPlugin from '@fullcalendar/timegrid';\nimport listPlugin from '@fullcalendar/list';\nimport bootstrapPlugin from '@fullcalendar/bootstrap';\nimport { CalendarModule } from 'primeng/calendar';\nimport { EEvent } from 'src/app/shared/services/event.service';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/shared/helper/allowHelper\";\nimport * as i2 from \"src/app/shared/helper/enumHelper\";\nimport * as i3 from \"@nebular/theme\";\nimport * as i4 from \"src/app/shared/services/message.service\";\nimport * as i5 from \"src/app/shared/helper/validationHelper\";\nimport * as i6 from \"src/services/api/services\";\nimport * as i7 from \"src/app/shared/helper/petternHelper\";\nimport * as i8 from \"@angular/router\";\nimport * as i9 from \"src/app/shared/services/event.service\";\nimport * as i10 from \"@angular/common\";\nimport * as i11 from \"src/services/api/services/File.service\";\nimport * as i12 from \"@angular/forms\";\nimport * as i13 from \"primeng/calendar\";\nconst _c0 = [\"calendar\"];\nfunction FinaldochouseManagementComponent_tr_36_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\", 26)(1, \"td\", 27);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\", 28);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\", 29)(6, \"button\", 30);\n    i0.ɵɵlistener(\"click\", function FinaldochouseManagementComponent_tr_36_Template_button_click_6_listener() {\n      const data_r4 = i0.ɵɵrestoreView(_r3).$implicit;\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.openPdfInNewTab(data_r4));\n    });\n    i0.ɵɵtext(7, \"\\u9023\\u7D50\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const data_r4 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(data_r4.CDocumentName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(data_r4.CSignDate);\n  }\n}\nfunction FinaldochouseManagementComponent_ng_template_42_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 52)(1, \"span\", 53);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"button\", 54);\n    i0.ɵɵlistener(\"click\", function FinaldochouseManagementComponent_ng_template_42_div_13_Template_button_click_3_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r4 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r4.clearFile());\n    });\n    i0.ɵɵelement(4, \"i\", 55);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r4.fileName);\n  }\n}\nfunction FinaldochouseManagementComponent_ng_template_42_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-card\", 31)(1, \"nb-card-header\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"nb-card-body\", 32)(4, \"div\", 33)(5, \"div\", 34)(6, \"label\", 35);\n    i0.ɵɵtext(7, \"\\u6587\\u4EF6 \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 36)(9, \"input\", 37);\n    i0.ɵɵlistener(\"change\", function FinaldochouseManagementComponent_ng_template_42_Template_input_change_9_listener($event) {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.onFileSelected($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"label\", 38);\n    i0.ɵɵelement(11, \"i\", 39);\n    i0.ɵɵtext(12, \" \\u4E0A\\u50B3 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(13, FinaldochouseManagementComponent_ng_template_42_div_13_Template, 5, 1, \"div\", 40);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(14, \"div\", 41)(15, \"label\", 42);\n    i0.ɵɵtext(16, \" \\u6587\\u4EF6\\u540D\\u7A31 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"input\", 43);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function FinaldochouseManagementComponent_ng_template_42_Template_input_ngModelChange_17_listener($event) {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r4 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r4.CDocumentName, $event) || (ctx_r4.CDocumentName = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(18, \"div\", 44)(19, \"label\", 45);\n    i0.ɵɵtext(20, \"\\u9001\\u5BE9\\u8CC7\\u8A0A \");\n    i0.ɵɵelementStart(21, \"p\", 46);\n    i0.ɵɵtext(22, \"\\u5167\\u90E8\\u5BE9\\u6838\\u4EBA\\u54E1\\u67E5\\u770B\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(23, \"textarea\", 47);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function FinaldochouseManagementComponent_ng_template_42_Template_textarea_ngModelChange_23_listener($event) {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r4 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r4.CApproveRemark, $event) || (ctx_r4.CApproveRemark = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtext(24, \"        \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(25, \"div\", 44)(26, \"label\", 48);\n    i0.ɵɵtext(27, \"\\u6458\\u8981\\u8A3B\\u8A18 \");\n    i0.ɵɵelementStart(28, \"p\", 46);\n    i0.ɵɵtext(29, \"\\u5BA2\\u6236\\u65BC\\u6587\\u4EF6\\u4E2D\\u67E5\\u770B\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(30, \"textarea\", 49);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function FinaldochouseManagementComponent_ng_template_42_Template_textarea_ngModelChange_30_listener($event) {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r4 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r4.CNote, $event) || (ctx_r4.CNote = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtext(31, \"        \");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(32, \"nb-card-footer\", 24)(33, \"button\", 50);\n    i0.ɵɵlistener(\"click\", function FinaldochouseManagementComponent_ng_template_42_Template_button_click_33_listener() {\n      const ref_r8 = i0.ɵɵrestoreView(_r6).dialogRef;\n      return i0.ɵɵresetView(ref_r8.close());\n    });\n    i0.ɵɵtext(34, \"\\u53D6\\u6D88\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(35, \"button\", 51);\n    i0.ɵɵlistener(\"click\", function FinaldochouseManagementComponent_ng_template_42_Template_button_click_35_listener() {\n      const ref_r8 = i0.ɵɵrestoreView(_r6).dialogRef;\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.onCreateFinalDoc(ref_r8));\n    });\n    i0.ɵɵtext(36, \"\\u78BA\\u8A8D\\u9001\\u51FA\\u5BE9\\u6838\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\" \\u6236\\u5225\\u7BA1\\u7406 > \\u7C3D\\u7F72\\u6587\\u4EF6\\u6B77\\u7A0B > \", ctx_r4.houseByID.CHousehold, \" \", ctx_r4.houseByID.CFloor, \"F \");\n    i0.ɵɵadvance(11);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.fileName);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r4.CDocumentName);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r4.CApproveRemark);\n    i0.ɵɵadvance(7);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r4.CNote);\n  }\n}\nexport class FinaldochouseManagementComponent extends BaseComponent {\n  constructor(_allow, enumHelper, dialogService, message, valid, finalDocumentService, pettern, router, route, destroyref, _eventService, location, _houseService, fileService) {\n    super(_allow);\n    this._allow = _allow;\n    this.enumHelper = enumHelper;\n    this.dialogService = dialogService;\n    this.message = message;\n    this.valid = valid;\n    this.finalDocumentService = finalDocumentService;\n    this.pettern = pettern;\n    this.router = router;\n    this.route = route;\n    this.destroyref = destroyref;\n    this._eventService = _eventService;\n    this.location = location;\n    this._houseService = _houseService;\n    this.fileService = fileService;\n    this.calendarOptions = {\n      plugins: [interactionPlugin, dayGridPlugin, timeGridPlugin, listPlugin, timeGridPlugin, bootstrapPlugin],\n      locale: 'zh-tw',\n      headerToolbar: {\n        left: 'prev',\n        center: 'title',\n        right: 'next'\n      }\n    };\n    this.file = null;\n    // request\n    this.getListFinalDocRequest = {};\n    this.uploadFinaldocRequest = {};\n    // response\n    this.listFinalDoc = [];\n    this.maxDate = new Date();\n  }\n  ngOnInit() {\n    this.route.paramMap.subscribe(params => {\n      if (params) {\n        const idParam = params.get('id1');\n        const id = idParam ? +idParam : 0;\n        this.buildCaseId = id;\n        const idParam2 = params.get('id2');\n        const id2 = idParam2 ? +idParam2 : 0;\n        this.currentHouseID = id2;\n        this.getList();\n        this.getHouseById();\n      }\n    });\n  }\n  addNew(ref) {\n    this.CApproveRemark = null;\n    this.CDocumentName = null;\n    this.CNote = null;\n    this.file = null;\n    this.dialogService.open(ref);\n  }\n  openPdfInNewTab(data) {\n    if (data) {\n      let fileUrl;\n      if (data.CSignDate && data.CSign) {\n        fileUrl = data.CFileAfter;\n      } else {\n        fileUrl = data.CFileBefore;\n      }\n      // 使用 FileService.apiFileGetFileGet 取得檔案 blob\n      this.fileService.apiFileGetFileGet({\n        relativePath: fileUrl,\n        fileName: data.CDocumentName\n      }).subscribe({\n        next: blob => {\n          // 建立 blob URL\n          const url = URL.createObjectURL(blob);\n          // 在新分頁開啟 PDF\n          window.open(url, '_blank');\n          // 延遲清理 URL 以確保檔案能正確載入\n          setTimeout(() => URL.revokeObjectURL(url), 10000);\n        },\n        error: error => {\n          console.error('取得檔案失敗:', error);\n          this.message.showErrorMSG('無法開啟檔案，請稍後再試');\n        }\n      });\n    }\n  }\n  goBack() {\n    this._eventService.push({\n      action: \"GET_BUILDCASE\" /* EEvent.GET_BUILDCASE */,\n      payload: this.buildCaseId\n    });\n    this.location.back();\n  }\n  onFileSelected(event) {\n    const file = event.target.files[0];\n    const fileRegex = /pdf/i;\n    if (!fileRegex.test(file.type)) {\n      this.message.showErrorMSG('文件格式不正確，僅允許 pdf 文件');\n      return;\n    }\n    if (file) {\n      const allowedTypes = ['application/pdf'];\n      if (allowedTypes.includes(file.type)) {\n        this.fileName = file.name;\n        this.file = file;\n      }\n    }\n  }\n  clearFile() {\n    if (this.file) {\n      this.file = null;\n      this.fileName = null;\n    }\n  }\n  getHouseById() {\n    this._houseService.apiHouseGetHouseByIdPost$Json({\n      body: {\n        CHouseID: this.currentHouseID\n      }\n    }).subscribe(res => {\n      if (res.Entries && res.StatusCode == 0) {\n        this.houseByID = res.Entries;\n      }\n    });\n  }\n  getList() {\n    this.getListFinalDocRequest.PageSize = this.pageSize;\n    this.getListFinalDocRequest.PageIndex = this.pageIndex;\n    if (this.currentHouseID != 0) {\n      this.getListFinalDocRequest.CHouseID = this.currentHouseID;\n      this.finalDocumentService.apiFinalDocumentGetListFinalDocByHousePost$Json({\n        body: this.getListFinalDocRequest\n      }).pipe().subscribe(res => {\n        if (res.StatusCode == 0) {\n          if (res.Entries) {\n            this.listFinalDoc = res.Entries;\n            this.totalRecords = res.TotalItems;\n            if (this.listFinalDoc) {\n              for (let i = 0; i < this.listFinalDoc.length; i++) {\n                if (this.listFinalDoc[i].CSignDate) this.listFinalDoc[i].CSignDate = moment(this.listFinalDoc[i].CSignDate).format(\"yyyy/MM/DD H:mm:ss\");\n              }\n            }\n          }\n        }\n      });\n    }\n  }\n  onCreateFinalDoc(ref) {\n    this.validation();\n    if (this.valid.errorMessages.length > 0) {\n      this.message.showErrorMSGs(this.valid.errorMessages);\n      return;\n    }\n    this.finalDocumentService.apiFinalDocumentUploadFinalDocPost$Json({\n      body: {\n        CHouseID: this.currentHouseID,\n        CBuildCaseID: this.buildCaseId,\n        CDocumentName: this.CDocumentName,\n        CApproveRemark: this.CApproveRemark,\n        CNote: this.CNote,\n        CFile: this.file\n      }\n    }).subscribe(res => {\n      if (res.Entries && res.StatusCode == 0) {\n        this.getList();\n        this.message.showSucessMSG(\"執行成功\");\n        ref.close();\n      } else {\n        this.message.showErrorMSG(res.Message);\n      }\n    });\n  }\n  convertToBlob(data, mimeType = 'application/octet-stream') {\n    if (data instanceof ArrayBuffer) {\n      return new Blob([data], {\n        type: mimeType\n      });\n    } else if (typeof data === 'string') {\n      return new Blob([data], {\n        type: mimeType\n      });\n    } else {\n      return undefined;\n    }\n  }\n  validation() {\n    this.valid.clear();\n    this.valid.required('[文件格式不正確]', this.file);\n    this.valid.required('[文件名稱]', this.CDocumentName);\n    this.valid.required('[送審資訊]', this.CApproveRemark);\n    this.valid.required('[系統操作說明]', this.CNote);\n  }\n  static {\n    this.ɵfac = function FinaldochouseManagementComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || FinaldochouseManagementComponent)(i0.ɵɵdirectiveInject(i1.AllowHelper), i0.ɵɵdirectiveInject(i2.EnumHelper), i0.ɵɵdirectiveInject(i3.NbDialogService), i0.ɵɵdirectiveInject(i4.MessageService), i0.ɵɵdirectiveInject(i5.ValidationHelper), i0.ɵɵdirectiveInject(i6.FinalDocumentService), i0.ɵɵdirectiveInject(i7.PetternHelper), i0.ɵɵdirectiveInject(i8.Router), i0.ɵɵdirectiveInject(i8.ActivatedRoute), i0.ɵɵdirectiveInject(i0.DestroyRef), i0.ɵɵdirectiveInject(i9.EventService), i0.ɵɵdirectiveInject(i10.Location), i0.ɵɵdirectiveInject(i6.HouseService), i0.ɵɵdirectiveInject(i11.FileService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: FinaldochouseManagementComponent,\n      selectors: [[\"app-finaldochouse-management\"]],\n      viewQuery: function FinaldochouseManagementComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.calendarComponent = _t.first);\n        }\n      },\n      standalone: true,\n      features: [i0.ɵɵInheritDefinitionFeature, i0.ɵɵStandaloneFeature],\n      decls: 44,\n      vars: 10,\n      consts: [[\"dialogUploadFinaldoc\", \"\"], [\"accent\", \"success\"], [2, \"font-size\", \"32px\"], [1, \"bg-white\"], [1, \"col-12\"], [1, \"row\"], [1, \"flex\", \"form-group\", \"col-12\", \"col-md-9\", \"text-right\"], [\"for\", \"date-select1\", 1, \"mr-3\", \"mt-2\"], [\"placeholder\", \"\\u5E74/\\u6708/\\u65E5\", \"dateFormat\", \"yy/mm/dd\", 3, \"ngModelChange\", \"appendTo\", \"ngModel\", \"maxDate\"], [\"for\", \"date-select1\", 1, \"mr-1\", \"ml-1\", \"mt-2\"], [1, \"form-group\", \"col-12\", \"col-md-3\", \"text-right\"], [1, \"btn\", \"btn-info\", \"mr-2\", 3, \"click\"], [1, \"fas\", \"fa-search\", \"mr-1\"], [1, \"col-md-12\"], [1, \"d-flex\", \"justify-content-end\", \"w-full\"], [1, \"btn\", \"btn-info\", 3, \"click\"], [1, \"table-responsive\"], [1, \"table\", \"table-striped\", \"border\", 2, \"min-width\", \"800px\", \"background-color\", \"#f3f3f3\"], [1, \"d-flex\", \"text-white\", 2, \"background-color\", \"#27ae60\"], [\"scope\", \"col\", 1, \"col-5\"], [\"scope\", \"col\", 1, \"col-4\"], [\"scope\", \"col\", 1, \"col-3\"], [\"class\", \"d-flex\", 4, \"ngFor\", \"ngForOf\"], [3, \"PageChange\", \"CollectionSize\", \"Page\", \"PageSize\"], [1, \"d-flex\", \"justify-content-center\"], [1, \"btn\", \"btn-secondary\", \"btn-sm\", 3, \"click\"], [1, \"d-flex\"], [1, \"col-5\"], [1, \"col-4\"], [1, \"col-3\"], [1, \"btn\", \"btn-outline-primary\", \"btn-sm\", \"m-1\", 3, \"click\"], [2, \"width\", \"1000px\", \"max-height\", \"95vh\"], [1, \"px-4\"], [1, \"form-group\", \"d-flex\", \"align-items-baseline\"], [1, \"d-flex\", \"flex-col\", \"col-3\"], [\"for\", \"file\", \"baseLabel\", \"\", 1, \"required-field\", \"align-self-start\", 2, \"min-width\", \"100px\", \"position\", \"static\"], [1, \"flex\", \"flex-col\", \"items-start\", \"space-y-4\"], [\"type\", \"file\", \"id\", \"fileInput\", \"accept\", \"image/jpeg, image/jpg, application/pdf\", 1, \"hidden\", 2, \"display\", \"none\", 3, \"change\"], [\"for\", \"fileInput\", 1, \"cursor-pointer\", \"bg-blue-500\", \"hover:bg-blue-700\", \"text-white\", \"font-bold\", \"py-2\", \"px-4\", \"rounded\"], [1, \"fa-solid\", \"fa-cloud-arrow-up\", \"mr-2\"], [\"class\", \"flex items-center space-x-2\", 4, \"ngIf\"], [1, \"form-group\"], [\"for\", \"CDocumentName\", \"baseLabel\", \"\", 1, \"required-field\", \"align-self-start\", \"col-3\", 2, \"min-width\", \"75px\"], [\"type\", \"text\", \"nbInput\", \"\", \"placeholder\", \"\\u6587\\u4EF6\\u540D\\u7A31\", 1, \"w-full\", 3, \"ngModelChange\", \"ngModel\"], [1, \"form-group\", \"d-flex\", \"align-items-center\"], [\"for\", \"remark\", \"baseLabel\", \"\", 1, \"required-field\", \"align-self-start\", \"col-3\"], [2, \"color\", \"red\"], [\"name\", \"remark\", \"id\", \"remark\", \"rows\", \"5\", \"nbInput\", \"\", 1, \"w-full\", 2, \"resize\", \"none\", \"max-width\", \"none\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"CNote\", \"baseLabel\", \"\", 1, \"required-field\", \"align-self-start\", \"col-3\"], [\"name\", \"CNote\", \"id\", \"CNote\", \"rows\", \"5\", \"nbInput\", \"\", 1, \"w-full\", 2, \"resize\", \"none\", \"max-width\", \"none\", 3, \"ngModelChange\", \"ngModel\"], [1, \"btn\", \"btn-outline-secondary\", \"m-2\", 3, \"click\"], [1, \"btn\", \"btn-success\", \"m-2\", 3, \"click\"], [1, \"flex\", \"items-center\", \"space-x-2\"], [1, \"text-gray-600\"], [\"type\", \"button\", 1, \"text-red-500\", \"hover:text-red-700\", 3, \"click\"], [1, \"fa-solid\", \"fa-trash\"]],\n      template: function FinaldochouseManagementComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"nb-card\", 1)(1, \"nb-card-header\")(2, \"div\", 2);\n          i0.ɵɵtext(3, \"\\u6236\\u5225\\u7BA1\\u7406 / \\u7C3D\\u7F72\\u6587\\u4EF6\\u6B77\\u7A0B\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(4, \"nb-card-body\", 3)(5, \"div\", 4)(6, \"div\", 5)(7, \"div\", 6)(8, \"span\", 7);\n          i0.ɵɵtext(9, \" \\u5EFA\\u7ACB\\u6642\\u9593 \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(10, \"p-calendar\", 8);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function FinaldochouseManagementComponent_Template_p_calendar_ngModelChange_10_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.getListFinalDocRequest.CDateStart, $event) || (ctx.getListFinalDocRequest.CDateStart = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(11, \"span\", 9);\n          i0.ɵɵtext(12, \"~\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(13, \"p-calendar\", 8);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function FinaldochouseManagementComponent_Template_p_calendar_ngModelChange_13_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.getListFinalDocRequest.CDateEnd, $event) || (ctx.getListFinalDocRequest.CDateEnd = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(14, \"div\", 10)(15, \"button\", 11);\n          i0.ɵɵlistener(\"click\", function FinaldochouseManagementComponent_Template_button_click_15_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.getList());\n          });\n          i0.ɵɵelement(16, \"i\", 12);\n          i0.ɵɵtext(17, \"\\u67E5\\u8A62\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(18, \"div\", 5)(19, \"div\", 13)(20, \"div\", 14)(21, \"button\", 15);\n          i0.ɵɵlistener(\"click\", function FinaldochouseManagementComponent_Template_button_click_21_listener() {\n            i0.ɵɵrestoreView(_r1);\n            const dialogUploadFinaldoc_r2 = i0.ɵɵreference(43);\n            return i0.ɵɵresetView(ctx.addNew(dialogUploadFinaldoc_r2));\n          });\n          i0.ɵɵtext(22, \" \\u65B0\\u589E\\u6587\\u6A94 \");\n          i0.ɵɵelementEnd()()()()()();\n          i0.ɵɵelementStart(23, \"nb-card-body\", 3)(24, \"div\", 4)(25, \"div\", 16)(26, \"table\", 17)(27, \"thead\")(28, \"tr\", 18)(29, \"th\", 19);\n          i0.ɵɵtext(30, \"\\u6587\\u4EF6\\u540D\\u7A31\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(31, \"th\", 20);\n          i0.ɵɵtext(32, \"\\u5BA2\\u6236\\u7C3D\\u540D\\u6642\\u9593\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(33, \"th\", 21);\n          i0.ɵɵtext(34, \"\\u9023\\u7D50\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(35, \"tbody\");\n          i0.ɵɵtemplate(36, FinaldochouseManagementComponent_tr_36_Template, 8, 2, \"tr\", 22);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(37, \"ngx-pagination\", 23);\n          i0.ɵɵtwoWayListener(\"PageChange\", function FinaldochouseManagementComponent_Template_ngx_pagination_PageChange_37_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.pageIndex, $event) || (ctx.pageIndex = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵlistener(\"PageChange\", function FinaldochouseManagementComponent_Template_ngx_pagination_PageChange_37_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.getList());\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(38, \"nb-card-footer\")(39, \"div\", 24)(40, \"button\", 25);\n          i0.ɵɵlistener(\"click\", function FinaldochouseManagementComponent_Template_button_click_40_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.goBack());\n          });\n          i0.ɵɵtext(41, \" \\u8FD4\\u56DE\\u4E0A\\u4E00\\u9801 \");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵtemplate(42, FinaldochouseManagementComponent_ng_template_42_Template, 37, 6, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor);\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(10);\n          i0.ɵɵproperty(\"appendTo\", \"body\");\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.getListFinalDocRequest.CDateStart);\n          i0.ɵɵproperty(\"maxDate\", ctx.maxDate);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"appendTo\", \"body\");\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.getListFinalDocRequest.CDateEnd);\n          i0.ɵɵproperty(\"maxDate\", ctx.maxDate);\n          i0.ɵɵadvance(23);\n          i0.ɵɵproperty(\"ngForOf\", ctx.listFinalDoc);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"CollectionSize\", ctx.totalRecords);\n          i0.ɵɵtwoWayProperty(\"Page\", ctx.pageIndex);\n          i0.ɵɵproperty(\"PageSize\", ctx.pageSize);\n        }\n      },\n      dependencies: [NbCardModule, i3.NbCardComponent, i3.NbCardBodyComponent, i3.NbCardFooterComponent, i3.NbCardHeaderComponent, NbInputModule, i3.NbInputDirective, FormsModule, i12.DefaultValueAccessor, i12.NgControlStatus, i12.NgModel, NbSelectModule, NbOptionModule, NgIf, NgFor, PaginationComponent, NbCheckboxModule, FullCalendarModule, CalendarModule, i13.Calendar],\n      styles: [\"/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsImZpbGUiOiJmaW5hbGRvY2hvdXNlLW1hbmFnZW1lbnQuY29tcG9uZW50LnNjc3MifQ== */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvcGFnZXMvaG91c2Vob2xkLW1hbmFnZW1lbnQvZmluYWxkb2Nob3VzZS1tYW5hZ2VtZW50L2ZpbmFsZG9jaG91c2UtbWFuYWdlbWVudC5jb21wb25lbnQuc2NzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiO0FBQ0EsNExBQTRMIiwic291cmNlUm9vdCI6IiJ9 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["NbCardModule", "NbCheckboxModule", "NbInputModule", "NbOptionModule", "NbSelectModule", "FormsModule", "<PERSON><PERSON><PERSON>", "NgIf", "PaginationComponent", "BaseComponent", "moment", "FullCalendarModule", "interactionPlugin", "dayGridPlugin", "timeGridPlugin", "listPlugin", "bootstrapPlugin", "CalendarModule", "EEvent", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵlistener", "FinaldochouseManagementComponent_tr_36_Template_button_click_6_listener", "data_r4", "ɵɵrestoreView", "_r3", "$implicit", "ctx_r4", "ɵɵnextContext", "ɵɵresetView", "openPdfInNewTab", "ɵɵadvance", "ɵɵtextInterpolate", "CDocumentName", "CSignDate", "FinaldochouseManagementComponent_ng_template_42_div_13_Template_button_click_3_listener", "_r7", "clearFile", "ɵɵelement", "fileName", "FinaldochouseManagementComponent_ng_template_42_Template_input_change_9_listener", "$event", "_r6", "onFileSelected", "ɵɵtemplate", "FinaldochouseManagementComponent_ng_template_42_div_13_Template", "ɵɵtwoWayListener", "FinaldochouseManagementComponent_ng_template_42_Template_input_ngModelChange_17_listener", "ɵɵtwoWayBindingSet", "FinaldochouseManagementComponent_ng_template_42_Template_textarea_ngModelChange_23_listener", "CApproveRemark", "FinaldochouseManagementComponent_ng_template_42_Template_textarea_ngModelChange_30_listener", "CNote", "FinaldochouseManagementComponent_ng_template_42_Template_button_click_33_listener", "ref_r8", "dialogRef", "close", "FinaldochouseManagementComponent_ng_template_42_Template_button_click_35_listener", "onCreateFinalDoc", "ɵɵtextInterpolate2", "houseByID", "CHousehold", "CFloor", "ɵɵproperty", "ɵɵtwoWayProperty", "FinaldochouseManagementComponent", "constructor", "_allow", "enum<PERSON>elper", "dialogService", "message", "valid", "finalDocumentService", "pettern", "router", "route", "destroyref", "_eventService", "location", "_houseService", "fileService", "calendarOptions", "plugins", "locale", "headerToolbar", "left", "center", "right", "file", "getListFinalDocRequest", "uploadFinaldocRequest", "listFinalDoc", "maxDate", "Date", "ngOnInit", "paramMap", "subscribe", "params", "idParam", "get", "id", "buildCaseId", "idParam2", "id2", "currentHouseID", "getList", "getHouseById", "addNew", "ref", "open", "data", "fileUrl", "CSign", "CFileAfter", "CFileBefore", "apiFileGetFileGet", "relativePath", "next", "blob", "url", "URL", "createObjectURL", "window", "setTimeout", "revokeObjectURL", "error", "console", "showErrorMSG", "goBack", "push", "action", "payload", "back", "event", "target", "files", "fileRegex", "test", "type", "allowedTypes", "includes", "name", "apiHouseGetHouseByIdPost$Json", "body", "CHouseID", "res", "Entries", "StatusCode", "PageSize", "pageSize", "PageIndex", "pageIndex", "apiFinalDocumentGetListFinalDocByHousePost$Json", "pipe", "totalRecords", "TotalItems", "i", "length", "format", "validation", "errorMessages", "showErrorMSGs", "apiFinalDocumentUploadFinalDocPost$Json", "CBuildCaseID", "CFile", "showSucessMSG", "Message", "convertToBlob", "mimeType", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Blob", "undefined", "clear", "required", "ɵɵdirectiveInject", "i1", "AllowHelper", "i2", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "i3", "NbDialogService", "i4", "MessageService", "i5", "ValidationHelper", "i6", "FinalDocumentService", "i7", "PetternHelper", "i8", "Router", "ActivatedRoute", "DestroyRef", "i9", "EventService", "i10", "Location", "HouseService", "i11", "FileService", "selectors", "viewQuery", "FinaldochouseManagementComponent_Query", "rf", "ctx", "FinaldochouseManagementComponent_Template_p_calendar_ngModelChange_10_listener", "_r1", "CDateStart", "FinaldochouseManagementComponent_Template_p_calendar_ngModelChange_13_listener", "CDateEnd", "FinaldochouseManagementComponent_Template_button_click_15_listener", "FinaldochouseManagementComponent_Template_button_click_21_listener", "dialogUploadFinaldoc_r2", "ɵɵreference", "FinaldochouseManagementComponent_tr_36_Template", "FinaldochouseManagementComponent_Template_ngx_pagination_PageChange_37_listener", "FinaldochouseManagementComponent_Template_button_click_40_listener", "FinaldochouseManagementComponent_ng_template_42_Template", "ɵɵtemplateRefExtractor", "NbCardComponent", "NbCardBodyComponent", "NbCardFooterComponent", "NbCardHeaderComponent", "NbInputDirective", "i12", "DefaultValueAccessor", "NgControlStatus", "NgModel", "i13", "Calendar", "styles"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\household-management\\finaldochouse-management\\finaldochouse-management.component.ts", "C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\household-management\\finaldochouse-management\\finaldochouse-management.component.html"], "sourcesContent": ["import { Component, DestroyRef, OnInit, ViewChild } from '@angular/core';\r\nimport { NbCardModule, NbCheckboxModule, NbDialogService, NbInputModule, NbOptionModule, NbSelectModule } from '@nebular/theme';\r\nimport { BreadcrumbComponent } from '../../components/breadcrumb/breadcrumb.component';\r\nimport { FormsModule } from '@angular/forms';\r\nimport { NgFor, NgIf, NgTemplateOutlet } from '@angular/common';\r\nimport { PaginationComponent } from '../../components/pagination/pagination.component';\r\nimport { StatusPipe } from 'src/app/@theme/pipes/mapping.pipe';\r\nimport { EnumHelper } from 'src/app/shared/helper/enumHelper';\r\nimport { AllowHelper } from 'src/app/shared/helper/allowHelper';\r\nimport { ValidationHelper } from 'src/app/shared/helper/validationHelper';\r\nimport { FinalDocumentService, HouseService } from 'src/services/api/services';\r\nimport { FileService } from 'src/services/api/services/File.service';\r\nimport { PetternHelper } from 'src/app/shared/helper/petternHelper';\r\nimport { ActivatedRoute, Router } from '@angular/router';\r\nimport { BaseComponent } from '../../components/base/baseComponent';\r\nimport { GetFinalDocListByHouse, TblFinalDocument, TblHouse } from 'src/services/api/models';\r\nimport * as moment from 'moment';\r\nimport { FullCalendarComponent, FullCalendarModule } from '@fullcalendar/angular';\r\nimport { Calendar, CalendarOptions } from 'fullcalendar';\r\nimport interactionPlugin from '@fullcalendar/interaction';\r\nimport dayGridPlugin from '@fullcalendar/daygrid';\r\nimport timeGridPlugin from '@fullcalendar/timegrid';\r\nimport listPlugin from '@fullcalendar/list';\r\nimport bootstrapPlugin from '@fullcalendar/bootstrap';\r\nimport { CalendarModule } from 'primeng/calendar';\r\nimport { MessageService } from 'src/app/shared/services/message.service';\r\nimport { EventService, EEvent } from 'src/app/shared/services/event.service';\r\nimport { Location } from '@angular/common';\r\nimport { ApiFinalDocumentUploadFinalDocPost$Json$Params } from 'src/services/api/fn/final-document/api-final-document-upload-final-doc-post-json';\r\n\r\n@Component({\r\n  selector: 'app-finaldochouse-management',\r\n  standalone: true,\r\n  imports: [\r\n    NbCardModule,\r\n    BreadcrumbComponent,\r\n    NbInputModule,\r\n    FormsModule,\r\n    NbSelectModule,\r\n    NbOptionModule,\r\n    NgIf,\r\n    NgFor,\r\n    PaginationComponent,\r\n    NbCheckboxModule,\r\n    FullCalendarModule,\r\n    CalendarModule\r\n  ],\r\n  templateUrl: './finaldochouse-management.component.html',\r\n  styleUrl: './finaldochouse-management.component.scss'\r\n})\r\nexport class FinaldochouseManagementComponent extends BaseComponent implements OnInit {\r\n  @ViewChild('calendar') calendarComponent: FullCalendarComponent;\r\n  calendarApi: Calendar;\r\n  maxDate!: Date;\r\n\r\n  calendarOptions: CalendarOptions = {\r\n    plugins: [\r\n      interactionPlugin,\r\n      dayGridPlugin,\r\n      timeGridPlugin,\r\n      listPlugin,\r\n      timeGridPlugin,\r\n      bootstrapPlugin\r\n    ],\r\n    locale: 'zh-tw',\r\n    headerToolbar: {\r\n      left: 'prev',\r\n      center: 'title',\r\n      right: 'next'\r\n    },\r\n  };\r\n  constructor(\r\n    private _allow: AllowHelper,\r\n    private enumHelper: EnumHelper,\r\n    private dialogService: NbDialogService,\r\n    private message: MessageService,\r\n    private valid: ValidationHelper,\r\n    private finalDocumentService: FinalDocumentService,\r\n    private pettern: PetternHelper,\r\n    private router: Router,\r\n    private route: ActivatedRoute,\r\n    private destroyref: DestroyRef,\r\n    private _eventService: EventService,\r\n    private location: Location,\r\n    private _houseService: HouseService,\r\n    private fileService: FileService\r\n  ) {\r\n    super(_allow);\r\n    this.maxDate = new Date();\r\n  }\r\n\r\n  currentHouseID: number;\r\n  buildCaseId: number;\r\n  fileName: string | null;\r\n  file: File | null = null;\r\n  CDocumentName: string | null;\r\n  CNote: string | null;\r\n  CApproveRemark: string | null;\r\n  // request\r\n  getListFinalDocRequest: GetFinalDocListByHouse = {};\r\n  uploadFinaldocRequest: ApiFinalDocumentUploadFinalDocPost$Json$Params = {};\r\n\r\n  // response\r\n  listFinalDoc: TblFinalDocument[] = [];\r\n  houseByID: TblHouse;\r\n\r\n  override ngOnInit() {\r\n    this.route.paramMap.subscribe(params => {\r\n      if (params) {\r\n        const idParam = params.get('id1');\r\n        const id = idParam ? +idParam : 0;\r\n        this.buildCaseId = id;\r\n        const idParam2 = params.get('id2');\r\n        const id2 = idParam2 ? +idParam2 : 0;\r\n        this.currentHouseID = id2;\r\n        this.getList();\r\n        this.getHouseById();\r\n      }\r\n    });\r\n  }\r\n\r\n  addNew(ref: any) {\r\n    this.CApproveRemark = null;\r\n    this.CDocumentName = null;\r\n    this.CNote = null;\r\n    this.file = null;\r\n    this.dialogService.open(ref)\r\n  }\r\n  openPdfInNewTab(data: TblFinalDocument) {\r\n    if (data) {\r\n      let fileUrl: string;\r\n\r\n      if (data.CSignDate && data.CSign) {\r\n        fileUrl = data.CFileAfter!;\r\n      } else {\r\n        fileUrl = data.CFileBefore!;\r\n      }\r\n\r\n      // 使用 FileService.apiFileGetFileGet 取得檔案 blob\r\n      this.fileService.apiFileGetFileGet({ relativePath: fileUrl, fileName: data.CDocumentName }).subscribe({\r\n        next: (blob: Blob) => {\r\n          // 建立 blob URL\r\n          const url = URL.createObjectURL(blob);\r\n          // 在新分頁開啟 PDF\r\n          window.open(url, '_blank');\r\n          // 延遲清理 URL 以確保檔案能正確載入\r\n          setTimeout(() => URL.revokeObjectURL(url), 10000);\r\n        },\r\n        error: (error) => {\r\n          console.error('取得檔案失敗:', error);\r\n          this.message.showErrorMSG('無法開啟檔案，請稍後再試');\r\n        }\r\n      });\r\n    }\r\n  }\r\n\r\n  goBack() {\r\n    this._eventService.push({\r\n      action: EEvent.GET_BUILDCASE,\r\n      payload: this.buildCaseId\r\n    })\r\n    this.location.back()\r\n  }\r\n\r\n  onFileSelected(event: any) {\r\n    const file: File = event.target.files[0];\r\n    const fileRegex = /pdf/i;\r\n    if (!fileRegex.test(file.type)) {\r\n      this.message.showErrorMSG('文件格式不正確，僅允許 pdf 文件');\r\n      return;\r\n    }\r\n    if (file) {\r\n      const allowedTypes = ['application/pdf'];\r\n      if (allowedTypes.includes(file.type)) {\r\n        this.fileName = file.name;\r\n        this.file = file;\r\n      }\r\n    }\r\n  }\r\n\r\n  clearFile() {\r\n    if (this.file) {\r\n      this.file = null;\r\n      this.fileName = null;\r\n    }\r\n  }\r\n\r\n  getHouseById() {\r\n    this._houseService.apiHouseGetHouseByIdPost$Json({\r\n      body: { CHouseID: this.currentHouseID }\r\n    }).subscribe(res => {\r\n      if (res.Entries && res.StatusCode == 0) {\r\n        this.houseByID = res.Entries\r\n      }\r\n    })\r\n  }\r\n\r\n  getList() {\r\n    this.getListFinalDocRequest.PageSize = this.pageSize;\r\n    this.getListFinalDocRequest.PageIndex = this.pageIndex;\r\n    if (this.currentHouseID != 0) {\r\n      this.getListFinalDocRequest.CHouseID = this.currentHouseID;\r\n      this.finalDocumentService.apiFinalDocumentGetListFinalDocByHousePost$Json({ body: this.getListFinalDocRequest })\r\n        .pipe()\r\n        .subscribe(res => {\r\n          if (res.StatusCode == 0) {\r\n            if (res.Entries) {\r\n              this.listFinalDoc = res.Entries;\r\n              this.totalRecords = res.TotalItems!;\r\n              if (this.listFinalDoc) {\r\n                for (let i = 0; i < this.listFinalDoc.length; i++) {\r\n                  if (this.listFinalDoc[i].CSignDate)\r\n                    this.listFinalDoc[i].CSignDate = moment(this.listFinalDoc[i].CSignDate).format(\"yyyy/MM/DD H:mm:ss\");\r\n                }\r\n              }\r\n            }\r\n          }\r\n        })\r\n    }\r\n  }\r\n\r\n  onCreateFinalDoc(ref: any) {\r\n    this.validation()\r\n    if (this.valid.errorMessages.length > 0) {\r\n      this.message.showErrorMSGs(this.valid.errorMessages);\r\n      return;\r\n    }\r\n    this.finalDocumentService.apiFinalDocumentUploadFinalDocPost$Json({\r\n      body: {\r\n        CHouseID: this.currentHouseID,\r\n        CBuildCaseID: this.buildCaseId,\r\n        CDocumentName: this.CDocumentName!,\r\n        CApproveRemark: this.CApproveRemark!,\r\n        CNote: this.CNote!,\r\n        CFile: this.file as Blob\r\n      }\r\n    }).subscribe(res => {\r\n      if (res.Entries && res.StatusCode == 0) {\r\n        this.getList();\r\n        this.message.showSucessMSG(\"執行成功\");\r\n        ref.close();\r\n      } else {\r\n        this.message.showErrorMSG(res.Message!)\r\n      }\r\n    })\r\n  }\r\n\r\n  convertToBlob(data: string | ArrayBuffer | null, mimeType: string = 'application/octet-stream'): Blob | undefined {\r\n    if (data instanceof ArrayBuffer) {\r\n      return new Blob([data], { type: mimeType });\r\n    } else if (typeof data === 'string') {\r\n      return new Blob([data], { type: mimeType });\r\n    } else {\r\n      return undefined;\r\n    }\r\n  }\r\n\r\n  validation() {\r\n    this.valid.clear();\r\n    this.valid.required('[文件格式不正確]', this.file)\r\n    this.valid.required('[文件名稱]', this.CDocumentName)\r\n    this.valid.required('[送審資訊]', this.CApproveRemark)\r\n    this.valid.required('[系統操作說明]', this.CNote)\r\n  }\r\n}\r\n", "<nb-card accent=\"success\">\r\n  <nb-card-header>\r\n    <!-- <ngx-breadcrumb></ngx-breadcrumb> -->\r\n    <div style=\"font-size: 32px;\">戶別管理 / 簽署文件歷程</div>\r\n  </nb-card-header>\r\n  <nb-card-body class=\"bg-white\">\r\n    <div class=\"col-12\">\r\n      <div class=\"row\">\r\n        <div class=\"flex form-group col-12 col-md-9 text-right\">\r\n          <span for=\"date-select1\" class=\"mr-3 mt-2\">\r\n            建立時間\r\n          </span>\r\n          <p-calendar [appendTo]=\"'body'\" placeholder='年/月/日' dateFormat=\"yy/mm/dd\"\r\n            [(ngModel)]=\"getListFinalDocRequest.CDateStart\" [maxDate]=\"maxDate\"></p-calendar>\r\n          <span for=\"date-select1\" class=\"mr-1 ml-1 mt-2\">~</span>\r\n          <p-calendar [appendTo]=\"'body'\" placeholder='年/月/日' dateFormat=\"yy/mm/dd\"\r\n            [(ngModel)]=\"getListFinalDocRequest.CDateEnd\" [maxDate]=\"maxDate\"></p-calendar>\r\n        </div>\r\n        <div class=\"form-group col-12 col-md-3 text-right\">\r\n          <button class=\"btn btn-info mr-2\" (click)=\"getList()\"><i class=\"fas fa-search mr-1\"></i>查詢</button>\r\n        </div>\r\n      </div>\r\n      <div class=\"row\">\r\n        <div class=\"col-md-12\">\r\n          <div class=\"d-flex justify-content-end w-full\">\r\n            <button class=\"btn btn-info\" (click)=\"addNew(dialogUploadFinaldoc)\">\r\n              新增文檔\r\n            </button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </nb-card-body>\r\n  <nb-card-body class=\"bg-white\">\r\n    <div class=\"col-12\">\r\n      <div class=\"table-responsive\">\r\n        <table class=\"table table-striped border \" style=\"min-width: 800px; background-color:#f3f3f3;\">\r\n          <thead>\r\n            <tr style=\"background-color: #27ae60;\" class=\"d-flex text-white\">\r\n              <th scope=\"col\" class=\"col-5\">文件名稱</th>\r\n              <th scope=\"col\" class=\"col-4\">客戶簽名時間</th>\r\n              <th scope=\"col\" class=\"col-3\">連結</th>\r\n            </tr>\r\n          </thead>\r\n          <tbody>\r\n            <tr *ngFor=\"let data of listFinalDoc; let i = index\" class=\"d-flex\">\r\n              <td class=\"col-5\">{{ data.CDocumentName }}</td>\r\n              <td class=\"col-4\">{{ data.CSignDate }}</td>\r\n              <td class=\"col-3\">\r\n                <button class=\"btn btn-outline-primary btn-sm m-1\" (click)=\"openPdfInNewTab(data)\">連結</button>\r\n              </td>\r\n            </tr>\r\n          </tbody>\r\n        </table>\r\n      </div>\r\n      <ngx-pagination [CollectionSize]=\"totalRecords\" [(Page)]=\"pageIndex\" [PageSize]=\"pageSize\"\r\n        (PageChange)=\"getList()\">\r\n      </ngx-pagination>\r\n    </div>\r\n  </nb-card-body>\r\n  <nb-card-footer>\r\n    <div class=\"d-flex justify-content-center\">\r\n      <button class=\"btn btn-secondary btn-sm\" (click)=\"goBack()\">\r\n        返回上一頁\r\n      </button>\r\n    </div>\r\n  </nb-card-footer>\r\n</nb-card>\r\n\r\n<ng-template #dialogUploadFinaldoc let-dialog let-ref=\"dialogRef\">\r\n  <nb-card style=\"width:1000px; max-height: 95vh\">\r\n    <nb-card-header>\r\n      戶別管理 > 簽署文件歷程 > {{houseByID.CHousehold}} {{houseByID.CFloor}}F\r\n    </nb-card-header>\r\n    <nb-card-body class=\"px-4\">\r\n      <div class=\"form-group d-flex align-items-baseline\">\r\n        <div class=\"d-flex flex-col col-3\">\r\n          <label for=\"file\" class=\"required-field align-self-start\" style=\"min-width:100px; position: static;\"\r\n            baseLabel>文件\r\n          </label>\r\n        </div>\r\n        <div class=\"flex flex-col items-start space-y-4\">\r\n          <input type=\"file\" id=\"fileInput\" accept=\"image/jpeg, image/jpg, application/pdf\" class=\"hidden\"\r\n            style=\"display: none\" (change)=\"onFileSelected($event)\">\r\n          <label for=\"fileInput\"\r\n            class=\"cursor-pointer bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded\">\r\n            <i class=\"fa-solid fa-cloud-arrow-up mr-2\"></i> 上傳\r\n          </label>\r\n          <div class=\"flex items-center space-x-2\" *ngIf=\"fileName\">\r\n            <span class=\"text-gray-600\">{{ fileName }}</span>\r\n            <button type=\"button\" (click)=\"clearFile()\" class=\"text-red-500 hover:text-red-700\">\r\n              <i class=\"fa-solid fa-trash\"></i>\r\n            </button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label for=\"CDocumentName\" class=\"required-field align-self-start col-3\" style=\"min-width:75px\" baseLabel>\r\n          文件名稱\r\n        </label>\r\n        <input type=\"text\" class=\"w-full\" nbInput placeholder=\"文件名稱\" [(ngModel)]=\"CDocumentName\" />\r\n      </div>\r\n\r\n      <div class=\"form-group d-flex align-items-center\">\r\n        <label for=\"remark\" baseLabel class=\"required-field align-self-start col-3\">送審資訊\r\n          <p style=\"color: red\">內部審核人員查看</p>\r\n        </label>\r\n\r\n        <textarea name=\"remark\" id=\"remark\" rows=\"5\" nbInput style=\"resize: none; max-width: none\" class=\"w-full\"\r\n          [(ngModel)]=\"CApproveRemark\">\r\n        </textarea>\r\n      </div>\r\n\r\n      <div class=\"form-group d-flex align-items-center\">\r\n        <label for=\"CNote\" baseLabel class=\"required-field align-self-start col-3\">摘要註記\r\n          <p style=\"color: red\">客戶於文件中查看</p>\r\n        </label>\r\n        <textarea name=\"CNote\" id=\"CNote\" rows=\"5\" nbInput style=\"resize: none; max-width: none\" class=\"w-full\"\r\n          [(ngModel)]=\"CNote\">\r\n        </textarea>\r\n      </div>\r\n    </nb-card-body>\r\n    <nb-card-footer class=\"d-flex justify-content-center\">\r\n      <button class=\"btn btn-outline-secondary m-2\" (click)=\"ref.close()\">取消</button>\r\n      <button class=\"btn btn-success m-2\" (click)=\"onCreateFinalDoc(ref)\">確認送出審核</button>\r\n    </nb-card-footer>\r\n  </nb-card>\r\n</ng-template>\r\n"], "mappings": "AACA,SAASA,YAAY,EAAEC,gBAAgB,EAAmBC,aAAa,EAAEC,cAAc,EAAEC,cAAc,QAAQ,gBAAgB;AAE/H,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,KAAK,EAAEC,IAAI,QAA0B,iBAAiB;AAC/D,SAASC,mBAAmB,QAAQ,kDAAkD;AAStF,SAASC,aAAa,QAAQ,qCAAqC;AAEnE,OAAO,KAAKC,MAAM,MAAM,QAAQ;AAChC,SAAgCC,kBAAkB,QAAQ,uBAAuB;AAEjF,OAAOC,iBAAiB,MAAM,2BAA2B;AACzD,OAAOC,aAAa,MAAM,uBAAuB;AACjD,OAAOC,cAAc,MAAM,wBAAwB;AACnD,OAAOC,UAAU,MAAM,oBAAoB;AAC3C,OAAOC,eAAe,MAAM,yBAAyB;AACrD,SAASC,cAAc,QAAQ,kBAAkB;AAEjD,SAAuBC,MAAM,QAAQ,uCAAuC;;;;;;;;;;;;;;;;;;;ICoB9DC,EADF,CAAAC,cAAA,aAAoE,aAChD;IAAAD,EAAA,CAAAE,MAAA,GAAwB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC/CH,EAAA,CAAAC,cAAA,aAAkB;IAAAD,EAAA,CAAAE,MAAA,GAAoB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAEzCH,EADF,CAAAC,cAAA,aAAkB,iBACmE;IAAhCD,EAAA,CAAAI,UAAA,mBAAAC,wEAAA;MAAA,MAAAC,OAAA,GAAAN,EAAA,CAAAO,aAAA,CAAAC,GAAA,EAAAC,SAAA;MAAA,MAAAC,MAAA,GAAAV,EAAA,CAAAW,aAAA;MAAA,OAAAX,EAAA,CAAAY,WAAA,CAASF,MAAA,CAAAG,eAAA,CAAAP,OAAA,CAAqB;IAAA,EAAC;IAACN,EAAA,CAAAE,MAAA,mBAAE;IAEzFF,EAFyF,CAAAG,YAAA,EAAS,EAC3F,EACF;;;;IALeH,EAAA,CAAAc,SAAA,GAAwB;IAAxBd,EAAA,CAAAe,iBAAA,CAAAT,OAAA,CAAAU,aAAA,CAAwB;IACxBhB,EAAA,CAAAc,SAAA,GAAoB;IAApBd,EAAA,CAAAe,iBAAA,CAAAT,OAAA,CAAAW,SAAA,CAAoB;;;;;;IA0CxCjB,EADF,CAAAC,cAAA,cAA0D,eAC5B;IAAAD,EAAA,CAAAE,MAAA,GAAc;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACjDH,EAAA,CAAAC,cAAA,iBAAoF;IAA9DD,EAAA,CAAAI,UAAA,mBAAAc,wFAAA;MAAAlB,EAAA,CAAAO,aAAA,CAAAY,GAAA;MAAA,MAAAT,MAAA,GAAAV,EAAA,CAAAW,aAAA;MAAA,OAAAX,EAAA,CAAAY,WAAA,CAASF,MAAA,CAAAU,SAAA,EAAW;IAAA,EAAC;IACzCpB,EAAA,CAAAqB,SAAA,YAAiC;IAErCrB,EADE,CAAAG,YAAA,EAAS,EACL;;;;IAJwBH,EAAA,CAAAc,SAAA,GAAc;IAAdd,EAAA,CAAAe,iBAAA,CAAAL,MAAA,CAAAY,QAAA,CAAc;;;;;;IAlBlDtB,EADF,CAAAC,cAAA,kBAAgD,qBAC9B;IACdD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAiB;IAIXH,EAHN,CAAAC,cAAA,uBAA2B,cAC2B,cACf,gBAErB;IAAAD,EAAA,CAAAE,MAAA,oBACZ;IACFF,EADE,CAAAG,YAAA,EAAQ,EACJ;IAEJH,EADF,CAAAC,cAAA,cAAiD,gBAEW;IAAlCD,EAAA,CAAAI,UAAA,oBAAAmB,iFAAAC,MAAA;MAAAxB,EAAA,CAAAO,aAAA,CAAAkB,GAAA;MAAA,MAAAf,MAAA,GAAAV,EAAA,CAAAW,aAAA;MAAA,OAAAX,EAAA,CAAAY,WAAA,CAAUF,MAAA,CAAAgB,cAAA,CAAAF,MAAA,CAAsB;IAAA,EAAC;IADzDxB,EAAA,CAAAG,YAAA,EAC0D;IAC1DH,EAAA,CAAAC,cAAA,iBAC8F;IAC5FD,EAAA,CAAAqB,SAAA,aAA+C;IAACrB,EAAA,CAAAE,MAAA,sBAClD;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAA2B,UAAA,KAAAC,+DAAA,kBAA0D;IAO9D5B,EADE,CAAAG,YAAA,EAAM,EACF;IAGJH,EADF,CAAAC,cAAA,eAAwB,iBACoF;IACxGD,EAAA,CAAAE,MAAA,kCACF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,iBAA2F;IAA9BD,EAAA,CAAA6B,gBAAA,2BAAAC,yFAAAN,MAAA;MAAAxB,EAAA,CAAAO,aAAA,CAAAkB,GAAA;MAAA,MAAAf,MAAA,GAAAV,EAAA,CAAAW,aAAA;MAAAX,EAAA,CAAA+B,kBAAA,CAAArB,MAAA,CAAAM,aAAA,EAAAQ,MAAA,MAAAd,MAAA,CAAAM,aAAA,GAAAQ,MAAA;MAAA,OAAAxB,EAAA,CAAAY,WAAA,CAAAY,MAAA;IAAA,EAA2B;IAC1FxB,EADE,CAAAG,YAAA,EAA2F,EACvF;IAGJH,EADF,CAAAC,cAAA,eAAkD,iBAC4B;IAAAD,EAAA,CAAAE,MAAA,iCAC1E;IAAAF,EAAA,CAAAC,cAAA,aAAsB;IAAAD,EAAA,CAAAE,MAAA,wDAAQ;IAChCF,EADgC,CAAAG,YAAA,EAAI,EAC5B;IAERH,EAAA,CAAAC,cAAA,oBAC+B;IAA7BD,EAAA,CAAA6B,gBAAA,2BAAAG,4FAAAR,MAAA;MAAAxB,EAAA,CAAAO,aAAA,CAAAkB,GAAA;MAAA,MAAAf,MAAA,GAAAV,EAAA,CAAAW,aAAA;MAAAX,EAAA,CAAA+B,kBAAA,CAAArB,MAAA,CAAAuB,cAAA,EAAAT,MAAA,MAAAd,MAAA,CAAAuB,cAAA,GAAAT,MAAA;MAAA,OAAAxB,EAAA,CAAAY,WAAA,CAAAY,MAAA;IAAA,EAA4B;IAC9BxB,EAAA,CAAAE,MAAA;IACFF,EADE,CAAAG,YAAA,EAAW,EACP;IAGJH,EADF,CAAAC,cAAA,eAAkD,iBAC2B;IAAAD,EAAA,CAAAE,MAAA,iCACzE;IAAAF,EAAA,CAAAC,cAAA,aAAsB;IAAAD,EAAA,CAAAE,MAAA,wDAAQ;IAChCF,EADgC,CAAAG,YAAA,EAAI,EAC5B;IACRH,EAAA,CAAAC,cAAA,oBACsB;IAApBD,EAAA,CAAA6B,gBAAA,2BAAAK,4FAAAV,MAAA;MAAAxB,EAAA,CAAAO,aAAA,CAAAkB,GAAA;MAAA,MAAAf,MAAA,GAAAV,EAAA,CAAAW,aAAA;MAAAX,EAAA,CAAA+B,kBAAA,CAAArB,MAAA,CAAAyB,KAAA,EAAAX,MAAA,MAAAd,MAAA,CAAAyB,KAAA,GAAAX,MAAA;MAAA,OAAAxB,EAAA,CAAAY,WAAA,CAAAY,MAAA;IAAA,EAAmB;IACrBxB,EAAA,CAAAE,MAAA;IAEJF,EAFI,CAAAG,YAAA,EAAW,EACP,EACO;IAEbH,EADF,CAAAC,cAAA,0BAAsD,kBACgB;IAAtBD,EAAA,CAAAI,UAAA,mBAAAgC,kFAAA;MAAA,MAAAC,MAAA,GAAArC,EAAA,CAAAO,aAAA,CAAAkB,GAAA,EAAAa,SAAA;MAAA,OAAAtC,EAAA,CAAAY,WAAA,CAASyB,MAAA,CAAAE,KAAA,EAAW;IAAA,EAAC;IAACvC,EAAA,CAAAE,MAAA,oBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAC/EH,EAAA,CAAAC,cAAA,kBAAoE;IAAhCD,EAAA,CAAAI,UAAA,mBAAAoC,kFAAA;MAAA,MAAAH,MAAA,GAAArC,EAAA,CAAAO,aAAA,CAAAkB,GAAA,EAAAa,SAAA;MAAA,MAAA5B,MAAA,GAAAV,EAAA,CAAAW,aAAA;MAAA,OAAAX,EAAA,CAAAY,WAAA,CAASF,MAAA,CAAA+B,gBAAA,CAAAJ,MAAA,CAAqB;IAAA,EAAC;IAACrC,EAAA,CAAAE,MAAA,4CAAM;IAE9EF,EAF8E,CAAAG,YAAA,EAAS,EACpE,EACT;;;;IAvDNH,EAAA,CAAAc,SAAA,GACF;IADEd,EAAA,CAAA0C,kBAAA,wEAAAhC,MAAA,CAAAiC,SAAA,CAAAC,UAAA,OAAAlC,MAAA,CAAAiC,SAAA,CAAAE,MAAA,OACF;IAegD7C,EAAA,CAAAc,SAAA,IAAc;IAAdd,EAAA,CAAA8C,UAAA,SAAApC,MAAA,CAAAY,QAAA,CAAc;IAaGtB,EAAA,CAAAc,SAAA,GAA2B;IAA3Bd,EAAA,CAAA+C,gBAAA,YAAArC,MAAA,CAAAM,aAAA,CAA2B;IAStFhB,EAAA,CAAAc,SAAA,GAA4B;IAA5Bd,EAAA,CAAA+C,gBAAA,YAAArC,MAAA,CAAAuB,cAAA,CAA4B;IAS5BjC,EAAA,CAAAc,SAAA,GAAmB;IAAnBd,EAAA,CAAA+C,gBAAA,YAAArC,MAAA,CAAAyB,KAAA,CAAmB;;;ADrE7B,OAAM,MAAOa,gCAAiC,SAAQ1D,aAAa;EAqBjE2D,YACUC,MAAmB,EACnBC,UAAsB,EACtBC,aAA8B,EAC9BC,OAAuB,EACvBC,KAAuB,EACvBC,oBAA0C,EAC1CC,OAAsB,EACtBC,MAAc,EACdC,KAAqB,EACrBC,UAAsB,EACtBC,aAA2B,EAC3BC,QAAkB,EAClBC,aAA2B,EAC3BC,WAAwB;IAEhC,KAAK,CAACb,MAAM,CAAC;IAfL,KAAAA,MAAM,GAANA,MAAM;IACN,KAAAC,UAAU,GAAVA,UAAU;IACV,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,oBAAoB,GAApBA,oBAAoB;IACpB,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,UAAU,GAAVA,UAAU;IACV,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,QAAQ,GAARA,QAAQ;IACR,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,WAAW,GAAXA,WAAW;IA9BrB,KAAAC,eAAe,GAAoB;MACjCC,OAAO,EAAE,CACPxE,iBAAiB,EACjBC,aAAa,EACbC,cAAc,EACdC,UAAU,EACVD,cAAc,EACdE,eAAe,CAChB;MACDqE,MAAM,EAAE,OAAO;MACfC,aAAa,EAAE;QACbC,IAAI,EAAE,MAAM;QACZC,MAAM,EAAE,OAAO;QACfC,KAAK,EAAE;;KAEV;IAwBD,KAAAC,IAAI,GAAgB,IAAI;IAIxB;IACA,KAAAC,sBAAsB,GAA2B,EAAE;IACnD,KAAAC,qBAAqB,GAAmD,EAAE;IAE1E;IACA,KAAAC,YAAY,GAAuB,EAAE;IAfnC,IAAI,CAACC,OAAO,GAAG,IAAIC,IAAI,EAAE;EAC3B;EAiBSC,QAAQA,CAAA;IACf,IAAI,CAACnB,KAAK,CAACoB,QAAQ,CAACC,SAAS,CAACC,MAAM,IAAG;MACrC,IAAIA,MAAM,EAAE;QACV,MAAMC,OAAO,GAAGD,MAAM,CAACE,GAAG,CAAC,KAAK,CAAC;QACjC,MAAMC,EAAE,GAAGF,OAAO,GAAG,CAACA,OAAO,GAAG,CAAC;QACjC,IAAI,CAACG,WAAW,GAAGD,EAAE;QACrB,MAAME,QAAQ,GAAGL,MAAM,CAACE,GAAG,CAAC,KAAK,CAAC;QAClC,MAAMI,GAAG,GAAGD,QAAQ,GAAG,CAACA,QAAQ,GAAG,CAAC;QACpC,IAAI,CAACE,cAAc,GAAGD,GAAG;QACzB,IAAI,CAACE,OAAO,EAAE;QACd,IAAI,CAACC,YAAY,EAAE;MACrB;IACF,CAAC,CAAC;EACJ;EAEAC,MAAMA,CAACC,GAAQ;IACb,IAAI,CAAC1D,cAAc,GAAG,IAAI;IAC1B,IAAI,CAACjB,aAAa,GAAG,IAAI;IACzB,IAAI,CAACmB,KAAK,GAAG,IAAI;IACjB,IAAI,CAACoC,IAAI,GAAG,IAAI;IAChB,IAAI,CAACnB,aAAa,CAACwC,IAAI,CAACD,GAAG,CAAC;EAC9B;EACA9E,eAAeA,CAACgF,IAAsB;IACpC,IAAIA,IAAI,EAAE;MACR,IAAIC,OAAe;MAEnB,IAAID,IAAI,CAAC5E,SAAS,IAAI4E,IAAI,CAACE,KAAK,EAAE;QAChCD,OAAO,GAAGD,IAAI,CAACG,UAAW;MAC5B,CAAC,MAAM;QACLF,OAAO,GAAGD,IAAI,CAACI,WAAY;MAC7B;MAEA;MACA,IAAI,CAAClC,WAAW,CAACmC,iBAAiB,CAAC;QAAEC,YAAY,EAAEL,OAAO;QAAExE,QAAQ,EAAEuE,IAAI,CAAC7E;MAAa,CAAE,CAAC,CAAC+D,SAAS,CAAC;QACpGqB,IAAI,EAAGC,IAAU,IAAI;UACnB;UACA,MAAMC,GAAG,GAAGC,GAAG,CAACC,eAAe,CAACH,IAAI,CAAC;UACrC;UACAI,MAAM,CAACb,IAAI,CAACU,GAAG,EAAE,QAAQ,CAAC;UAC1B;UACAI,UAAU,CAAC,MAAMH,GAAG,CAACI,eAAe,CAACL,GAAG,CAAC,EAAE,KAAK,CAAC;QACnD,CAAC;QACDM,KAAK,EAAGA,KAAK,IAAI;UACfC,OAAO,CAACD,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;UAC/B,IAAI,CAACvD,OAAO,CAACyD,YAAY,CAAC,cAAc,CAAC;QAC3C;OACD,CAAC;IACJ;EACF;EAEAC,MAAMA,CAAA;IACJ,IAAI,CAACnD,aAAa,CAACoD,IAAI,CAAC;MACtBC,MAAM;MACNC,OAAO,EAAE,IAAI,CAAC9B;KACf,CAAC;IACF,IAAI,CAACvB,QAAQ,CAACsD,IAAI,EAAE;EACtB;EAEAzF,cAAcA,CAAC0F,KAAU;IACvB,MAAM7C,IAAI,GAAS6C,KAAK,CAACC,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC;IACxC,MAAMC,SAAS,GAAG,MAAM;IACxB,IAAI,CAACA,SAAS,CAACC,IAAI,CAACjD,IAAI,CAACkD,IAAI,CAAC,EAAE;MAC9B,IAAI,CAACpE,OAAO,CAACyD,YAAY,CAAC,oBAAoB,CAAC;MAC/C;IACF;IACA,IAAIvC,IAAI,EAAE;MACR,MAAMmD,YAAY,GAAG,CAAC,iBAAiB,CAAC;MACxC,IAAIA,YAAY,CAACC,QAAQ,CAACpD,IAAI,CAACkD,IAAI,CAAC,EAAE;QACpC,IAAI,CAACnG,QAAQ,GAAGiD,IAAI,CAACqD,IAAI;QACzB,IAAI,CAACrD,IAAI,GAAGA,IAAI;MAClB;IACF;EACF;EAEAnD,SAASA,CAAA;IACP,IAAI,IAAI,CAACmD,IAAI,EAAE;MACb,IAAI,CAACA,IAAI,GAAG,IAAI;MAChB,IAAI,CAACjD,QAAQ,GAAG,IAAI;IACtB;EACF;EAEAmE,YAAYA,CAAA;IACV,IAAI,CAAC3B,aAAa,CAAC+D,6BAA6B,CAAC;MAC/CC,IAAI,EAAE;QAAEC,QAAQ,EAAE,IAAI,CAACxC;MAAc;KACtC,CAAC,CAACR,SAAS,CAACiD,GAAG,IAAG;MACjB,IAAIA,GAAG,CAACC,OAAO,IAAID,GAAG,CAACE,UAAU,IAAI,CAAC,EAAE;QACtC,IAAI,CAACvF,SAAS,GAAGqF,GAAG,CAACC,OAAO;MAC9B;IACF,CAAC,CAAC;EACJ;EAEAzC,OAAOA,CAAA;IACL,IAAI,CAAChB,sBAAsB,CAAC2D,QAAQ,GAAG,IAAI,CAACC,QAAQ;IACpD,IAAI,CAAC5D,sBAAsB,CAAC6D,SAAS,GAAG,IAAI,CAACC,SAAS;IACtD,IAAI,IAAI,CAAC/C,cAAc,IAAI,CAAC,EAAE;MAC5B,IAAI,CAACf,sBAAsB,CAACuD,QAAQ,GAAG,IAAI,CAACxC,cAAc;MAC1D,IAAI,CAAChC,oBAAoB,CAACgF,+CAA+C,CAAC;QAAET,IAAI,EAAE,IAAI,CAACtD;MAAsB,CAAE,CAAC,CAC7GgE,IAAI,EAAE,CACNzD,SAAS,CAACiD,GAAG,IAAG;QACf,IAAIA,GAAG,CAACE,UAAU,IAAI,CAAC,EAAE;UACvB,IAAIF,GAAG,CAACC,OAAO,EAAE;YACf,IAAI,CAACvD,YAAY,GAAGsD,GAAG,CAACC,OAAO;YAC/B,IAAI,CAACQ,YAAY,GAAGT,GAAG,CAACU,UAAW;YACnC,IAAI,IAAI,CAAChE,YAAY,EAAE;cACrB,KAAK,IAAIiE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACjE,YAAY,CAACkE,MAAM,EAAED,CAAC,EAAE,EAAE;gBACjD,IAAI,IAAI,CAACjE,YAAY,CAACiE,CAAC,CAAC,CAAC1H,SAAS,EAChC,IAAI,CAACyD,YAAY,CAACiE,CAAC,CAAC,CAAC1H,SAAS,GAAG1B,MAAM,CAAC,IAAI,CAACmF,YAAY,CAACiE,CAAC,CAAC,CAAC1H,SAAS,CAAC,CAAC4H,MAAM,CAAC,oBAAoB,CAAC;cACxG;YACF;UACF;QACF;MACF,CAAC,CAAC;IACN;EACF;EAEApG,gBAAgBA,CAACkD,GAAQ;IACvB,IAAI,CAACmD,UAAU,EAAE;IACjB,IAAI,IAAI,CAACxF,KAAK,CAACyF,aAAa,CAACH,MAAM,GAAG,CAAC,EAAE;MACvC,IAAI,CAACvF,OAAO,CAAC2F,aAAa,CAAC,IAAI,CAAC1F,KAAK,CAACyF,aAAa,CAAC;MACpD;IACF;IACA,IAAI,CAACxF,oBAAoB,CAAC0F,uCAAuC,CAAC;MAChEnB,IAAI,EAAE;QACJC,QAAQ,EAAE,IAAI,CAACxC,cAAc;QAC7B2D,YAAY,EAAE,IAAI,CAAC9D,WAAW;QAC9BpE,aAAa,EAAE,IAAI,CAACA,aAAc;QAClCiB,cAAc,EAAE,IAAI,CAACA,cAAe;QACpCE,KAAK,EAAE,IAAI,CAACA,KAAM;QAClBgH,KAAK,EAAE,IAAI,CAAC5E;;KAEf,CAAC,CAACQ,SAAS,CAACiD,GAAG,IAAG;MACjB,IAAIA,GAAG,CAACC,OAAO,IAAID,GAAG,CAACE,UAAU,IAAI,CAAC,EAAE;QACtC,IAAI,CAAC1C,OAAO,EAAE;QACd,IAAI,CAACnC,OAAO,CAAC+F,aAAa,CAAC,MAAM,CAAC;QAClCzD,GAAG,CAACpD,KAAK,EAAE;MACb,CAAC,MAAM;QACL,IAAI,CAACc,OAAO,CAACyD,YAAY,CAACkB,GAAG,CAACqB,OAAQ,CAAC;MACzC;IACF,CAAC,CAAC;EACJ;EAEAC,aAAaA,CAACzD,IAAiC,EAAE0D,QAAA,GAAmB,0BAA0B;IAC5F,IAAI1D,IAAI,YAAY2D,WAAW,EAAE;MAC/B,OAAO,IAAIC,IAAI,CAAC,CAAC5D,IAAI,CAAC,EAAE;QAAE4B,IAAI,EAAE8B;MAAQ,CAAE,CAAC;IAC7C,CAAC,MAAM,IAAI,OAAO1D,IAAI,KAAK,QAAQ,EAAE;MACnC,OAAO,IAAI4D,IAAI,CAAC,CAAC5D,IAAI,CAAC,EAAE;QAAE4B,IAAI,EAAE8B;MAAQ,CAAE,CAAC;IAC7C,CAAC,MAAM;MACL,OAAOG,SAAS;IAClB;EACF;EAEAZ,UAAUA,CAAA;IACR,IAAI,CAACxF,KAAK,CAACqG,KAAK,EAAE;IAClB,IAAI,CAACrG,KAAK,CAACsG,QAAQ,CAAC,WAAW,EAAE,IAAI,CAACrF,IAAI,CAAC;IAC3C,IAAI,CAACjB,KAAK,CAACsG,QAAQ,CAAC,QAAQ,EAAE,IAAI,CAAC5I,aAAa,CAAC;IACjD,IAAI,CAACsC,KAAK,CAACsG,QAAQ,CAAC,QAAQ,EAAE,IAAI,CAAC3H,cAAc,CAAC;IAClD,IAAI,CAACqB,KAAK,CAACsG,QAAQ,CAAC,UAAU,EAAE,IAAI,CAACzH,KAAK,CAAC;EAC7C;;;uCArNWa,gCAAgC,EAAAhD,EAAA,CAAA6J,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAA/J,EAAA,CAAA6J,iBAAA,CAAAG,EAAA,CAAAC,UAAA,GAAAjK,EAAA,CAAA6J,iBAAA,CAAAK,EAAA,CAAAC,eAAA,GAAAnK,EAAA,CAAA6J,iBAAA,CAAAO,EAAA,CAAAC,cAAA,GAAArK,EAAA,CAAA6J,iBAAA,CAAAS,EAAA,CAAAC,gBAAA,GAAAvK,EAAA,CAAA6J,iBAAA,CAAAW,EAAA,CAAAC,oBAAA,GAAAzK,EAAA,CAAA6J,iBAAA,CAAAa,EAAA,CAAAC,aAAA,GAAA3K,EAAA,CAAA6J,iBAAA,CAAAe,EAAA,CAAAC,MAAA,GAAA7K,EAAA,CAAA6J,iBAAA,CAAAe,EAAA,CAAAE,cAAA,GAAA9K,EAAA,CAAA6J,iBAAA,CAAA7J,EAAA,CAAA+K,UAAA,GAAA/K,EAAA,CAAA6J,iBAAA,CAAAmB,EAAA,CAAAC,YAAA,GAAAjL,EAAA,CAAA6J,iBAAA,CAAAqB,GAAA,CAAAC,QAAA,GAAAnL,EAAA,CAAA6J,iBAAA,CAAAW,EAAA,CAAAY,YAAA,GAAApL,EAAA,CAAA6J,iBAAA,CAAAwB,GAAA,CAAAC,WAAA;IAAA;EAAA;;;YAAhCtI,gCAAgC;MAAAuI,SAAA;MAAAC,SAAA,WAAAC,uCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;;;;;;;;;;;;;;;UC/CzC1L,EAHJ,CAAAC,cAAA,iBAA0B,qBACR,aAEgB;UAAAD,EAAA,CAAAE,MAAA,sEAAa;UAC7CF,EAD6C,CAAAG,YAAA,EAAM,EAClC;UAKTH,EAJR,CAAAC,cAAA,sBAA+B,aACT,aACD,aACyC,cACX;UACzCD,EAAA,CAAAE,MAAA,iCACF;UAAAF,EAAA,CAAAG,YAAA,EAAO;UACPH,EAAA,CAAAC,cAAA,qBACsE;UAApED,EAAA,CAAA6B,gBAAA,2BAAA+J,+EAAApK,MAAA;YAAAxB,EAAA,CAAAO,aAAA,CAAAsL,GAAA;YAAA7L,EAAA,CAAA+B,kBAAA,CAAA4J,GAAA,CAAAnH,sBAAA,CAAAsH,UAAA,EAAAtK,MAAA,MAAAmK,GAAA,CAAAnH,sBAAA,CAAAsH,UAAA,GAAAtK,MAAA;YAAA,OAAAxB,EAAA,CAAAY,WAAA,CAAAY,MAAA;UAAA,EAA+C;UAAqBxB,EAAA,CAAAG,YAAA,EAAa;UACnFH,EAAA,CAAAC,cAAA,eAAgD;UAAAD,EAAA,CAAAE,MAAA,SAAC;UAAAF,EAAA,CAAAG,YAAA,EAAO;UACxDH,EAAA,CAAAC,cAAA,qBACoE;UAAlED,EAAA,CAAA6B,gBAAA,2BAAAkK,+EAAAvK,MAAA;YAAAxB,EAAA,CAAAO,aAAA,CAAAsL,GAAA;YAAA7L,EAAA,CAAA+B,kBAAA,CAAA4J,GAAA,CAAAnH,sBAAA,CAAAwH,QAAA,EAAAxK,MAAA,MAAAmK,GAAA,CAAAnH,sBAAA,CAAAwH,QAAA,GAAAxK,MAAA;YAAA,OAAAxB,EAAA,CAAAY,WAAA,CAAAY,MAAA;UAAA,EAA6C;UACjDxB,EADsE,CAAAG,YAAA,EAAa,EAC7E;UAEJH,EADF,CAAAC,cAAA,eAAmD,kBACK;UAApBD,EAAA,CAAAI,UAAA,mBAAA6L,mEAAA;YAAAjM,EAAA,CAAAO,aAAA,CAAAsL,GAAA;YAAA,OAAA7L,EAAA,CAAAY,WAAA,CAAS+K,GAAA,CAAAnG,OAAA,EAAS;UAAA,EAAC;UAACxF,EAAA,CAAAqB,SAAA,aAAkC;UAAArB,EAAA,CAAAE,MAAA,oBAAE;UAE9FF,EAF8F,CAAAG,YAAA,EAAS,EAC/F,EACF;UAIAH,EAHN,CAAAC,cAAA,cAAiB,eACQ,eAC0B,kBACuB;UAAvCD,EAAA,CAAAI,UAAA,mBAAA8L,mEAAA;YAAAlM,EAAA,CAAAO,aAAA,CAAAsL,GAAA;YAAA,MAAAM,uBAAA,GAAAnM,EAAA,CAAAoM,WAAA;YAAA,OAAApM,EAAA,CAAAY,WAAA,CAAS+K,GAAA,CAAAjG,MAAA,CAAAyG,uBAAA,CAA4B;UAAA,EAAC;UACjEnM,EAAA,CAAAE,MAAA,kCACF;UAKVF,EALU,CAAAG,YAAA,EAAS,EACL,EACF,EACF,EACF,EACO;UAOHH,EANZ,CAAAC,cAAA,uBAA+B,cACT,eACY,iBACmE,aACtF,cAC4D,cACjC;UAAAD,EAAA,CAAAE,MAAA,gCAAI;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACvCH,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAE,MAAA,4CAAM;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACzCH,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAEpCF,EAFoC,CAAAG,YAAA,EAAK,EAClC,EACC;UACRH,EAAA,CAAAC,cAAA,aAAO;UACLD,EAAA,CAAA2B,UAAA,KAAA0K,+CAAA,iBAAoE;UAS1ErM,EAFI,CAAAG,YAAA,EAAQ,EACF,EACJ;UACNH,EAAA,CAAAC,cAAA,0BAC2B;UADqBD,EAAA,CAAA6B,gBAAA,wBAAAyK,gFAAA9K,MAAA;YAAAxB,EAAA,CAAAO,aAAA,CAAAsL,GAAA;YAAA7L,EAAA,CAAA+B,kBAAA,CAAA4J,GAAA,CAAArD,SAAA,EAAA9G,MAAA,MAAAmK,GAAA,CAAArD,SAAA,GAAA9G,MAAA;YAAA,OAAAxB,EAAA,CAAAY,WAAA,CAAAY,MAAA;UAAA,EAAoB;UAClExB,EAAA,CAAAI,UAAA,wBAAAkM,gFAAA;YAAAtM,EAAA,CAAAO,aAAA,CAAAsL,GAAA;YAAA,OAAA7L,EAAA,CAAAY,WAAA,CAAc+K,GAAA,CAAAnG,OAAA,EAAS;UAAA,EAAC;UAG9BxF,EAFI,CAAAG,YAAA,EAAiB,EACb,EACO;UAGXH,EAFJ,CAAAC,cAAA,sBAAgB,eAC6B,kBACmB;UAAnBD,EAAA,CAAAI,UAAA,mBAAAmM,mEAAA;YAAAvM,EAAA,CAAAO,aAAA,CAAAsL,GAAA;YAAA,OAAA7L,EAAA,CAAAY,WAAA,CAAS+K,GAAA,CAAA5E,MAAA,EAAQ;UAAA,EAAC;UACzD/G,EAAA,CAAAE,MAAA,wCACF;UAGNF,EAHM,CAAAG,YAAA,EAAS,EACL,EACS,EACT;UAEVH,EAAA,CAAA2B,UAAA,KAAA6K,wDAAA,iCAAAxM,EAAA,CAAAyM,sBAAA,CAAkE;;;UAzD5CzM,EAAA,CAAAc,SAAA,IAAmB;UAAnBd,EAAA,CAAA8C,UAAA,oBAAmB;UAC7B9C,EAAA,CAAA+C,gBAAA,YAAA4I,GAAA,CAAAnH,sBAAA,CAAAsH,UAAA,CAA+C;UAAC9L,EAAA,CAAA8C,UAAA,YAAA6I,GAAA,CAAAhH,OAAA,CAAmB;UAEzD3E,EAAA,CAAAc,SAAA,GAAmB;UAAnBd,EAAA,CAAA8C,UAAA,oBAAmB;UAC7B9C,EAAA,CAAA+C,gBAAA,YAAA4I,GAAA,CAAAnH,sBAAA,CAAAwH,QAAA,CAA6C;UAAChM,EAAA,CAAA8C,UAAA,YAAA6I,GAAA,CAAAhH,OAAA,CAAmB;UA6B5C3E,EAAA,CAAAc,SAAA,IAAiB;UAAjBd,EAAA,CAAA8C,UAAA,YAAA6I,GAAA,CAAAjH,YAAA,CAAiB;UAU5B1E,EAAA,CAAAc,SAAA,EAA+B;UAA/Bd,EAAA,CAAA8C,UAAA,mBAAA6I,GAAA,CAAAlD,YAAA,CAA+B;UAACzI,EAAA,CAAA+C,gBAAA,SAAA4I,GAAA,CAAArD,SAAA,CAAoB;UAACtI,EAAA,CAAA8C,UAAA,aAAA6I,GAAA,CAAAvD,QAAA,CAAqB;;;qBDrB5FvJ,YAAY,EAAAqL,EAAA,CAAAwC,eAAA,EAAAxC,EAAA,CAAAyC,mBAAA,EAAAzC,EAAA,CAAA0C,qBAAA,EAAA1C,EAAA,CAAA2C,qBAAA,EAEZ9N,aAAa,EAAAmL,EAAA,CAAA4C,gBAAA,EACb5N,WAAW,EAAA6N,GAAA,CAAAC,oBAAA,EAAAD,GAAA,CAAAE,eAAA,EAAAF,GAAA,CAAAG,OAAA,EACXjO,cAAc,EACdD,cAAc,EACdI,IAAI,EACJD,KAAK,EACLE,mBAAmB,EACnBP,gBAAgB,EAChBU,kBAAkB,EAClBM,cAAc,EAAAqN,GAAA,CAAAC,QAAA;MAAAC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}