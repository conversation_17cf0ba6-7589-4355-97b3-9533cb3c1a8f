{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { InjectionToken, Injectable, Inject, Component, ChangeDetectionStrategy, Injector, NgModule } from '@angular/core';\nimport * as i3 from '@angular/common';\nimport { CommonModule } from '@angular/common';\nimport * as i2 from '@angular/router';\nimport { RouterModule } from '@angular/router';\nimport * as i5 from '@angular/forms';\nimport { FormsModule } from '@angular/forms';\nimport * as i4 from '@nebular/theme';\nimport { NB_WINDOW, NbLayoutModule, NbCardModule, NbCheckboxModule, NbAlertModule, NbInputModule, NbButtonModule, NbIconModule } from '@nebular/theme';\nimport { BehaviorSubject, of, Subject } from 'rxjs';\nimport { filter, share, map, switchMap, delay, catchError, takeUntil } from 'rxjs/operators';\nimport * as i1 from '@angular/common/http';\nimport { HttpResponse, HttpHeaders, HttpErrorResponse } from '@angular/common/http';\nconst _c0 = [\"*\"];\nfunction NbLoginComponent_nb_alert_4_li_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\", 27);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const error_r2 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(error_r2);\n  }\n}\nfunction NbLoginComponent_nb_alert_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-alert\", 23)(1, \"p\", 24)(2, \"b\");\n    i0.ɵɵtext(3, \"Oh snap!\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"ul\", 25);\n    i0.ɵɵtemplate(5, NbLoginComponent_nb_alert_4_li_5_Template, 2, 1, \"li\", 26);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.errors);\n  }\n}\nfunction NbLoginComponent_nb_alert_5_li_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\", 27);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const message_r4 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(message_r4);\n  }\n}\nfunction NbLoginComponent_nb_alert_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-alert\", 28)(1, \"p\", 24)(2, \"b\");\n    i0.ɵɵtext(3, \"Hooray!\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"ul\", 25);\n    i0.ɵɵtemplate(5, NbLoginComponent_nb_alert_5_li_5_Template, 2, 1, \"li\", 26);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.messages);\n  }\n}\nfunction NbLoginComponent_ng_container_13_p_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 30);\n    i0.ɵɵtext(1, \" Email is required! \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction NbLoginComponent_ng_container_13_p_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 30);\n    i0.ɵɵtext(1, \" Email should be the real one! \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction NbLoginComponent_ng_container_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, NbLoginComponent_ng_container_13_p_1_Template, 2, 0, \"p\", 29)(2, NbLoginComponent_ng_container_13_p_2_Template, 2, 0, \"p\", 29);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵnextContext();\n    const email_r5 = i0.ɵɵreference(12);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", email_r5.errors == null ? null : email_r5.errors.required);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", email_r5.errors == null ? null : email_r5.errors.pattern);\n  }\n}\nfunction NbLoginComponent_ng_container_22_p_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 30);\n    i0.ɵɵtext(1, \" Password is required! \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction NbLoginComponent_ng_container_22_p_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 30);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate2(\" Password should contain from \", ctx_r2.getConfigValue(\"forms.validation.password.minLength\"), \" to \", ctx_r2.getConfigValue(\"forms.validation.password.maxLength\"), \" characters \");\n  }\n}\nfunction NbLoginComponent_ng_container_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, NbLoginComponent_ng_container_22_p_1_Template, 2, 0, \"p\", 29)(2, NbLoginComponent_ng_container_22_p_2_Template, 2, 2, \"p\", 29);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵnextContext();\n    const password_r6 = i0.ɵɵreference(21);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", password_r6.errors == null ? null : password_r6.errors.required);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (password_r6.errors == null ? null : password_r6.errors.minlength) || (password_r6.errors == null ? null : password_r6.errors.maxlength));\n  }\n}\nfunction NbLoginComponent_nb_checkbox_24_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-checkbox\", 31);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function NbLoginComponent_nb_checkbox_24_Template_nb_checkbox_ngModelChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.user.rememberMe, $event) || (ctx_r2.user.rememberMe = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtext(1, \"Remember me\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.user.rememberMe);\n  }\n}\nfunction NbLoginComponent_section_27_ng_container_3_a_1_nb_icon_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"nb-icon\", 39);\n  }\n  if (rf & 2) {\n    const socialLink_r8 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵproperty(\"icon\", socialLink_r8.icon);\n  }\n}\nfunction NbLoginComponent_section_27_ng_container_3_a_1_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0);\n  }\n  if (rf & 2) {\n    const socialLink_r8 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵtextInterpolate(socialLink_r8.title);\n  }\n}\nfunction NbLoginComponent_section_27_ng_container_3_a_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 37);\n    i0.ɵɵtemplate(1, NbLoginComponent_section_27_ng_container_3_a_1_nb_icon_1_Template, 1, 1, \"nb-icon\", 38)(2, NbLoginComponent_section_27_ng_container_3_a_1_ng_template_2_Template, 1, 1, \"ng-template\", null, 3, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const title_r9 = i0.ɵɵreference(3);\n    const socialLink_r8 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵclassProp(\"with-icon\", socialLink_r8.icon);\n    i0.ɵɵproperty(\"routerLink\", socialLink_r8.link);\n    i0.ɵɵattribute(\"target\", socialLink_r8.target)(\"class\", socialLink_r8.icon);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", socialLink_r8.icon)(\"ngIfElse\", title_r9);\n  }\n}\nfunction NbLoginComponent_section_27_ng_container_3_a_2_nb_icon_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"nb-icon\", 39);\n  }\n  if (rf & 2) {\n    const socialLink_r8 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵproperty(\"icon\", socialLink_r8.icon);\n  }\n}\nfunction NbLoginComponent_section_27_ng_container_3_a_2_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0);\n  }\n  if (rf & 2) {\n    const socialLink_r8 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵtextInterpolate(socialLink_r8.title);\n  }\n}\nfunction NbLoginComponent_section_27_ng_container_3_a_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\");\n    i0.ɵɵtemplate(1, NbLoginComponent_section_27_ng_container_3_a_2_nb_icon_1_Template, 1, 1, \"nb-icon\", 38)(2, NbLoginComponent_section_27_ng_container_3_a_2_ng_template_2_Template, 1, 1, \"ng-template\", null, 3, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const title_r10 = i0.ɵɵreference(3);\n    const socialLink_r8 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵclassProp(\"with-icon\", socialLink_r8.icon);\n    i0.ɵɵattribute(\"href\", socialLink_r8.url, i0.ɵɵsanitizeUrl)(\"target\", socialLink_r8.target)(\"class\", socialLink_r8.icon);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", socialLink_r8.icon)(\"ngIfElse\", title_r10);\n  }\n}\nfunction NbLoginComponent_section_27_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, NbLoginComponent_section_27_ng_container_3_a_1_Template, 4, 7, \"a\", 35)(2, NbLoginComponent_section_27_ng_container_3_a_2_Template, 4, 7, \"a\", 36);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const socialLink_r8 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", socialLink_r8.link);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", socialLink_r8.url);\n  }\n}\nfunction NbLoginComponent_section_27_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"section\", 32);\n    i0.ɵɵtext(1, \" or enter with: \");\n    i0.ɵɵelementStart(2, \"div\", 33);\n    i0.ɵɵtemplate(3, NbLoginComponent_section_27_ng_container_3_Template, 3, 2, \"ng-container\", 34);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.socialLinks);\n  }\n}\nfunction NbRegisterComponent_nb_alert_2_li_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\", 29);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const error_r2 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(error_r2);\n  }\n}\nfunction NbRegisterComponent_nb_alert_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-alert\", 25)(1, \"p\", 26)(2, \"b\");\n    i0.ɵɵtext(3, \"Oh snap!\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"ul\", 27);\n    i0.ɵɵtemplate(5, NbRegisterComponent_nb_alert_2_li_5_Template, 2, 1, \"li\", 28);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.errors);\n  }\n}\nfunction NbRegisterComponent_nb_alert_3_li_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\", 29);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const message_r4 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(message_r4);\n  }\n}\nfunction NbRegisterComponent_nb_alert_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-alert\", 30)(1, \"p\", 26)(2, \"b\");\n    i0.ɵɵtext(3, \"Hooray!\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"ul\", 27);\n    i0.ɵɵtemplate(5, NbRegisterComponent_nb_alert_3_li_5_Template, 2, 1, \"li\", 28);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.messages);\n  }\n}\nfunction NbRegisterComponent_ng_container_11_p_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 32);\n    i0.ɵɵtext(1, \" Full name is required! \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction NbRegisterComponent_ng_container_11_p_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 32);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate2(\" Full name should contains from \", ctx_r2.getConfigValue(\"forms.validation.fullName.minLength\"), \" to \", ctx_r2.getConfigValue(\"forms.validation.fullName.maxLength\"), \" characters \");\n  }\n}\nfunction NbRegisterComponent_ng_container_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, NbRegisterComponent_ng_container_11_p_1_Template, 2, 0, \"p\", 31)(2, NbRegisterComponent_ng_container_11_p_2_Template, 2, 2, \"p\", 31);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵnextContext();\n    const fullName_r5 = i0.ɵɵreference(10);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", fullName_r5.errors == null ? null : fullName_r5.errors.required);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (fullName_r5.errors == null ? null : fullName_r5.errors.minlength) || (fullName_r5.errors == null ? null : fullName_r5.errors.maxlength));\n  }\n}\nfunction NbRegisterComponent_ng_container_17_p_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 32);\n    i0.ɵɵtext(1, \" Email is required! \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction NbRegisterComponent_ng_container_17_p_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 32);\n    i0.ɵɵtext(1, \" Email should be the real one! \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction NbRegisterComponent_ng_container_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, NbRegisterComponent_ng_container_17_p_1_Template, 2, 0, \"p\", 31)(2, NbRegisterComponent_ng_container_17_p_2_Template, 2, 0, \"p\", 31);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵnextContext();\n    const email_r6 = i0.ɵɵreference(16);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", email_r6.errors == null ? null : email_r6.errors.required);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", email_r6.errors == null ? null : email_r6.errors.pattern);\n  }\n}\nfunction NbRegisterComponent_ng_container_23_p_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 32);\n    i0.ɵɵtext(1, \" Password is required! \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction NbRegisterComponent_ng_container_23_p_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 32);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate2(\" Password should contain from \", ctx_r2.getConfigValue(\"forms.validation.password.minLength\"), \" to \", ctx_r2.getConfigValue(\"forms.validation.password.maxLength\"), \" characters \");\n  }\n}\nfunction NbRegisterComponent_ng_container_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, NbRegisterComponent_ng_container_23_p_1_Template, 2, 0, \"p\", 31)(2, NbRegisterComponent_ng_container_23_p_2_Template, 2, 2, \"p\", 31);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵnextContext();\n    const password_r7 = i0.ɵɵreference(22);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", password_r7.errors == null ? null : password_r7.errors.required);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (password_r7.errors == null ? null : password_r7.errors.minlength) || (password_r7.errors == null ? null : password_r7.errors.maxlength));\n  }\n}\nfunction NbRegisterComponent_ng_container_29_p_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 32);\n    i0.ɵɵtext(1, \" Password confirmation is required! \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction NbRegisterComponent_ng_container_29_p_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 32);\n    i0.ɵɵtext(1, \" Password does not match the confirm password. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction NbRegisterComponent_ng_container_29_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, NbRegisterComponent_ng_container_29_p_1_Template, 2, 0, \"p\", 31)(2, NbRegisterComponent_ng_container_29_p_2_Template, 2, 0, \"p\", 31);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵnextContext();\n    const password_r7 = i0.ɵɵreference(22);\n    const rePass_r8 = i0.ɵɵreference(28);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", rePass_r8.errors == null ? null : rePass_r8.errors.required);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", password_r7.value != rePass_r8.value && !(rePass_r8.errors == null ? null : rePass_r8.errors.required));\n  }\n}\nfunction NbRegisterComponent_div_30_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 33)(1, \"nb-checkbox\", 34);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function NbRegisterComponent_div_30_Template_nb_checkbox_ngModelChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.user.terms, $event) || (ctx_r2.user.terms = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtext(2, \" Agree to \");\n    i0.ɵɵelementStart(3, \"a\", 35)(4, \"strong\");\n    i0.ɵɵtext(5, \"Terms & Conditions\");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.user.terms);\n    i0.ɵɵproperty(\"required\", ctx_r2.getConfigValue(\"forms.register.terms\"));\n  }\n}\nfunction NbRegisterComponent_section_33_ng_container_3_a_1_nb_icon_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"nb-icon\", 43);\n  }\n  if (rf & 2) {\n    const socialLink_r10 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵproperty(\"icon\", socialLink_r10.icon);\n  }\n}\nfunction NbRegisterComponent_section_33_ng_container_3_a_1_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0);\n  }\n  if (rf & 2) {\n    const socialLink_r10 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵtextInterpolate(socialLink_r10.title);\n  }\n}\nfunction NbRegisterComponent_section_33_ng_container_3_a_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 41);\n    i0.ɵɵtemplate(1, NbRegisterComponent_section_33_ng_container_3_a_1_nb_icon_1_Template, 1, 1, \"nb-icon\", 42)(2, NbRegisterComponent_section_33_ng_container_3_a_1_ng_template_2_Template, 1, 1, \"ng-template\", null, 5, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const title_r11 = i0.ɵɵreference(3);\n    const socialLink_r10 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵclassProp(\"with-icon\", socialLink_r10.icon);\n    i0.ɵɵproperty(\"routerLink\", socialLink_r10.link);\n    i0.ɵɵattribute(\"target\", socialLink_r10.target)(\"class\", socialLink_r10.icon);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", socialLink_r10.icon)(\"ngIfElse\", title_r11);\n  }\n}\nfunction NbRegisterComponent_section_33_ng_container_3_a_2_nb_icon_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"nb-icon\", 43);\n  }\n  if (rf & 2) {\n    const socialLink_r10 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵproperty(\"icon\", socialLink_r10.icon);\n  }\n}\nfunction NbRegisterComponent_section_33_ng_container_3_a_2_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0);\n  }\n  if (rf & 2) {\n    const socialLink_r10 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵtextInterpolate(socialLink_r10.title);\n  }\n}\nfunction NbRegisterComponent_section_33_ng_container_3_a_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\");\n    i0.ɵɵtemplate(1, NbRegisterComponent_section_33_ng_container_3_a_2_nb_icon_1_Template, 1, 1, \"nb-icon\", 42)(2, NbRegisterComponent_section_33_ng_container_3_a_2_ng_template_2_Template, 1, 1, \"ng-template\", null, 5, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const title_r12 = i0.ɵɵreference(3);\n    const socialLink_r10 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵclassProp(\"with-icon\", socialLink_r10.icon);\n    i0.ɵɵattribute(\"href\", socialLink_r10.url, i0.ɵɵsanitizeUrl)(\"target\", socialLink_r10.target)(\"class\", socialLink_r10.icon);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", socialLink_r10.icon)(\"ngIfElse\", title_r12);\n  }\n}\nfunction NbRegisterComponent_section_33_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, NbRegisterComponent_section_33_ng_container_3_a_1_Template, 4, 7, \"a\", 39)(2, NbRegisterComponent_section_33_ng_container_3_a_2_Template, 4, 7, \"a\", 40);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const socialLink_r10 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", socialLink_r10.link);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", socialLink_r10.url);\n  }\n}\nfunction NbRegisterComponent_section_33_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"section\", 36);\n    i0.ɵɵtext(1, \" or enter with: \");\n    i0.ɵɵelementStart(2, \"div\", 37);\n    i0.ɵɵtemplate(3, NbRegisterComponent_section_33_ng_container_3_Template, 3, 2, \"ng-container\", 38);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.socialLinks);\n  }\n}\nfunction NbRequestPasswordComponent_nb_alert_4_li_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\", 19);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const error_r2 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(error_r2);\n  }\n}\nfunction NbRequestPasswordComponent_nb_alert_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-alert\", 15)(1, \"p\", 16)(2, \"b\");\n    i0.ɵɵtext(3, \"Oh snap!\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"ul\", 17);\n    i0.ɵɵtemplate(5, NbRequestPasswordComponent_nb_alert_4_li_5_Template, 2, 1, \"li\", 18);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.errors);\n  }\n}\nfunction NbRequestPasswordComponent_nb_alert_5_li_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\", 19);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const message_r4 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(message_r4);\n  }\n}\nfunction NbRequestPasswordComponent_nb_alert_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-alert\", 20)(1, \"p\", 16)(2, \"b\");\n    i0.ɵɵtext(3, \"Hooray!\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"ul\", 17);\n    i0.ɵɵtemplate(5, NbRequestPasswordComponent_nb_alert_5_li_5_Template, 2, 1, \"li\", 18);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.messages);\n  }\n}\nfunction NbRequestPasswordComponent_ng_container_13_p_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 22);\n    i0.ɵɵtext(1, \" Email is required! \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction NbRequestPasswordComponent_ng_container_13_p_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 22);\n    i0.ɵɵtext(1, \" Email should be the real one! \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction NbRequestPasswordComponent_ng_container_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, NbRequestPasswordComponent_ng_container_13_p_1_Template, 2, 0, \"p\", 21)(2, NbRequestPasswordComponent_ng_container_13_p_2_Template, 2, 0, \"p\", 21);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵnextContext();\n    const email_r5 = i0.ɵɵreference(12);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", email_r5.errors == null ? null : email_r5.errors.required);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", email_r5.errors == null ? null : email_r5.errors.pattern);\n  }\n}\nfunction NbResetPasswordComponent_nb_alert_4_li_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\", 23);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const error_r2 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(error_r2);\n  }\n}\nfunction NbResetPasswordComponent_nb_alert_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-alert\", 19)(1, \"p\", 20)(2, \"b\");\n    i0.ɵɵtext(3, \"Oh snap!\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"ul\", 21);\n    i0.ɵɵtemplate(5, NbResetPasswordComponent_nb_alert_4_li_5_Template, 2, 1, \"li\", 22);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.errors);\n  }\n}\nfunction NbResetPasswordComponent_nb_alert_5_li_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\", 23);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const message_r4 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(message_r4);\n  }\n}\nfunction NbResetPasswordComponent_nb_alert_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-alert\", 24)(1, \"p\", 20)(2, \"b\");\n    i0.ɵɵtext(3, \"Hooray!\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"ul\", 21);\n    i0.ɵɵtemplate(5, NbResetPasswordComponent_nb_alert_5_li_5_Template, 2, 1, \"li\", 22);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.messages);\n  }\n}\nfunction NbResetPasswordComponent_ng_container_13_p_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 26);\n    i0.ɵɵtext(1, \" Password is required! \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction NbResetPasswordComponent_ng_container_13_p_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 26);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate2(\" Password should contains from \", ctx_r2.getConfigValue(\"forms.validation.password.minLength\"), \" to \", ctx_r2.getConfigValue(\"forms.validation.password.maxLength\"), \" characters \");\n  }\n}\nfunction NbResetPasswordComponent_ng_container_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, NbResetPasswordComponent_ng_container_13_p_1_Template, 2, 0, \"p\", 25)(2, NbResetPasswordComponent_ng_container_13_p_2_Template, 2, 2, \"p\", 25);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵnextContext();\n    const password_r5 = i0.ɵɵreference(12);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", password_r5.errors == null ? null : password_r5.errors.required);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (password_r5.errors == null ? null : password_r5.errors.minlength) || (password_r5.errors == null ? null : password_r5.errors.maxlength));\n  }\n}\nfunction NbResetPasswordComponent_ng_container_19_p_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 26);\n    i0.ɵɵtext(1, \" Password confirmation is required! \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction NbResetPasswordComponent_ng_container_19_p_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 26);\n    i0.ɵɵtext(1, \" Password does not match the confirm password. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction NbResetPasswordComponent_ng_container_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, NbResetPasswordComponent_ng_container_19_p_1_Template, 2, 0, \"p\", 25)(2, NbResetPasswordComponent_ng_container_19_p_2_Template, 2, 0, \"p\", 25);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵnextContext();\n    const password_r5 = i0.ɵɵreference(12);\n    const rePass_r6 = i0.ɵɵreference(18);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", rePass_r6.invalid && (rePass_r6.errors == null ? null : rePass_r6.errors.required));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", password_r5.value != rePass_r6.value && !(rePass_r6.errors == null ? null : rePass_r6.errors.required));\n  }\n}\nconst _c1 = \"[_nghost-%COMP%]   .form-group[_ngcontent-%COMP%]:last-of-type{margin-bottom:3rem}\\n\\n\\n\\n\\n\\n\";\nconst socialLinks = [];\nconst defaultAuthOptions = {\n  strategies: [],\n  forms: {\n    login: {\n      redirectDelay: 500,\n      // delay before redirect after a successful login, while success message is shown to the user\n      strategy: 'email',\n      // provider id key. If you have multiple strategies, or what to use your own\n      rememberMe: true,\n      // whether to show or not the `rememberMe` checkbox\n      showMessages: {\n        success: true,\n        error: true\n      },\n      socialLinks: socialLinks // social links at the bottom of a page\n    },\n    register: {\n      redirectDelay: 500,\n      strategy: 'email',\n      showMessages: {\n        success: true,\n        error: true\n      },\n      terms: true,\n      socialLinks: socialLinks\n    },\n    requestPassword: {\n      redirectDelay: 500,\n      strategy: 'email',\n      showMessages: {\n        success: true,\n        error: true\n      },\n      socialLinks: socialLinks\n    },\n    resetPassword: {\n      redirectDelay: 500,\n      strategy: 'email',\n      showMessages: {\n        success: true,\n        error: true\n      },\n      socialLinks: socialLinks\n    },\n    logout: {\n      redirectDelay: 500,\n      strategy: 'email'\n    },\n    validation: {\n      password: {\n        required: true,\n        minLength: 4,\n        maxLength: 50\n      },\n      email: {\n        required: true\n      },\n      fullName: {\n        required: false,\n        minLength: 4,\n        maxLength: 50\n      }\n    }\n  }\n};\nconst NB_AUTH_OPTIONS = new InjectionToken('Nebular Auth Options');\nconst NB_AUTH_USER_OPTIONS = new InjectionToken('Nebular User Auth Options');\nconst NB_AUTH_STRATEGIES = new InjectionToken('Nebular Auth Strategies');\nconst NB_AUTH_TOKENS = new InjectionToken('Nebular Auth Tokens');\nconst NB_AUTH_INTERCEPTOR_HEADER = new InjectionToken('Nebular Simple Interceptor Header');\nconst NB_AUTH_TOKEN_INTERCEPTOR_FILTER = new InjectionToken('Nebular Interceptor Filter');\n\n/**\n * Extending object that entered in first argument.\n *\n * Returns extended object or false if have no target object or incorrect type.\n *\n * If you wish to clone source object (without modify it), just use empty new\n * object as first argument, like this:\n *   deepExtend({}, yourObj_1, [yourObj_N]);\n */\nconst deepExtend = function (...objects) {\n  if (arguments.length < 1 || typeof arguments[0] !== 'object') {\n    return false;\n  }\n  if (arguments.length < 2) {\n    return arguments[0];\n  }\n  const target = arguments[0];\n  // convert arguments to array and cut off target object\n  const args = Array.prototype.slice.call(arguments, 1);\n  let val, src;\n  args.forEach(function (obj) {\n    // skip argument if it is array or isn't object\n    if (typeof obj !== 'object' || Array.isArray(obj)) {\n      return;\n    }\n    Object.keys(obj).forEach(function (key) {\n      src = target[key]; // source value\n      val = obj[key]; // new value\n      // recursion prevention\n      if (val === target) {\n        return;\n        /**\n         * if new value isn't object then just overwrite by new value\n         * instead of extending.\n         */\n      } else if (typeof val !== 'object' || val === null) {\n        target[key] = val;\n        return;\n        // just clone arrays (and recursive clone objects inside)\n      } else if (Array.isArray(val)) {\n        target[key] = deepCloneArray(val);\n        return;\n        // custom cloning and overwrite for specific objects\n      } else if (isSpecificValue(val)) {\n        target[key] = cloneSpecificValue(val);\n        return;\n        // overwrite by new value if source isn't object or array\n      } else if (typeof src !== 'object' || src === null || Array.isArray(src)) {\n        target[key] = deepExtend({}, val);\n        return;\n        // source value and new value is objects both, extending...\n      } else {\n        target[key] = deepExtend(src, val);\n        return;\n      }\n    });\n  });\n  return target;\n};\nfunction isSpecificValue(val) {\n  return val instanceof Date || val instanceof RegExp ? true : false;\n}\nfunction cloneSpecificValue(val) {\n  if (val instanceof Date) {\n    return new Date(val.getTime());\n  } else if (val instanceof RegExp) {\n    return new RegExp(val);\n  } else {\n    throw new Error('cloneSpecificValue: Unexpected situation');\n  }\n}\n/**\n * Recursive cloning array.\n */\nfunction deepCloneArray(arr) {\n  const clone = [];\n  arr.forEach(function (item, index) {\n    if (typeof item === 'object' && item !== null) {\n      if (Array.isArray(item)) {\n        clone[index] = deepCloneArray(item);\n      } else if (isSpecificValue(item)) {\n        clone[index] = cloneSpecificValue(item);\n      } else {\n        clone[index] = deepExtend({}, item);\n      }\n    } else {\n      clone[index] = item;\n    }\n  });\n  return clone;\n}\n// getDeepFromObject({result: {data: 1}}, 'result.data', 2); // returns 1\nfunction getDeepFromObject(object = {}, name, defaultValue) {\n  const keys = name.split('.');\n  // clone the object\n  let level = deepExtend({}, object || {});\n  keys.forEach(k => {\n    if (level && typeof level[k] !== 'undefined') {\n      level = level[k];\n    } else {\n      level = undefined;\n    }\n  });\n  return typeof level === 'undefined' ? defaultValue : level;\n}\nfunction urlBase64Decode(str) {\n  let output = str.replace(/-/g, '+').replace(/_/g, '/');\n  switch (output.length % 4) {\n    case 0:\n      {\n        break;\n      }\n    case 2:\n      {\n        output += '==';\n        break;\n      }\n    case 3:\n      {\n        output += '=';\n        break;\n      }\n    default:\n      {\n        throw new Error('Illegal base64url string!');\n      }\n  }\n  return b64DecodeUnicode(output);\n}\nfunction b64decode(str) {\n  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=';\n  let output = '';\n  str = String(str).replace(/=+$/, '');\n  if (str.length % 4 === 1) {\n    throw new Error(`'atob' failed: The string to be decoded is not correctly encoded.`);\n  }\n  for (\n  // initialize result and counters\n  let bc = 0, bs, buffer, idx = 0;\n  // get next character\n  buffer = str.charAt(idx++);\n  // character found in table? initialize bit storage and add its ascii value;\n  ~buffer && (bs = bc % 4 ? bs * 64 + buffer : buffer,\n  // and if not first of each 4 characters,\n  // convert the first 8 bits to one ascii character\n  bc++ % 4) ? output += String.fromCharCode(255 & bs >> (-2 * bc & 6)) : 0) {\n    // try to find character in table (0-63, not found => -1)\n    buffer = chars.indexOf(buffer);\n  }\n  return output;\n}\n// https://developer.mozilla.org/en/docs/Web/API/WindowBase64/Base64_encoding_and_decoding#The_Unicode_Problem\nfunction b64DecodeUnicode(str) {\n  return decodeURIComponent(Array.prototype.map.call(b64decode(str), c => {\n    return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);\n  }).join(''));\n}\nclass NbAuthToken {\n  constructor() {\n    this.payload = null;\n  }\n  getName() {\n    return this.constructor.NAME;\n  }\n  getPayload() {\n    return this.payload;\n  }\n}\nclass NbAuthTokenNotFoundError extends Error {\n  constructor(message) {\n    super(message);\n    Object.setPrototypeOf(this, new.target.prototype);\n  }\n}\nclass NbAuthIllegalTokenError extends Error {\n  constructor(message) {\n    super(message);\n    Object.setPrototypeOf(this, new.target.prototype);\n  }\n}\nclass NbAuthEmptyTokenError extends NbAuthIllegalTokenError {\n  constructor(message) {\n    super(message);\n    Object.setPrototypeOf(this, new.target.prototype);\n  }\n}\nclass NbAuthIllegalJWTTokenError extends NbAuthIllegalTokenError {\n  constructor(message) {\n    super(message);\n    Object.setPrototypeOf(this, new.target.prototype);\n  }\n}\nfunction nbAuthCreateToken(tokenClass, token, ownerStrategyName, createdAt) {\n  return new tokenClass(token, ownerStrategyName, createdAt);\n}\nfunction decodeJwtPayload(payload) {\n  if (payload.length === 0) {\n    throw new NbAuthEmptyTokenError('Cannot extract from an empty payload.');\n  }\n  const parts = payload.split('.');\n  if (parts.length !== 3) {\n    throw new NbAuthIllegalJWTTokenError(`The payload ${payload} is not valid JWT payload and must consist of three parts.`);\n  }\n  let decoded;\n  try {\n    decoded = urlBase64Decode(parts[1]);\n  } catch (e) {\n    throw new NbAuthIllegalJWTTokenError(`The payload ${payload} is not valid JWT payload and cannot be parsed.`);\n  }\n  if (!decoded) {\n    throw new NbAuthIllegalJWTTokenError(`The payload ${payload} is not valid JWT payload and cannot be decoded.`);\n  }\n  return JSON.parse(decoded);\n}\n/**\n * Wrapper for simple (text) token\n */\nlet NbAuthSimpleToken = /*#__PURE__*/(() => {\n  class NbAuthSimpleToken extends NbAuthToken {\n    static {\n      this.NAME = 'nb:auth:simple:token';\n    }\n    constructor(token, ownerStrategyName, createdAt) {\n      super();\n      this.token = token;\n      this.ownerStrategyName = ownerStrategyName;\n      this.createdAt = createdAt;\n      try {\n        this.parsePayload();\n      } catch (err) {\n        if (!(err instanceof NbAuthTokenNotFoundError)) {\n          // token is present but has got a problem, including illegal\n          throw err;\n        }\n      }\n      this.createdAt = this.prepareCreatedAt(createdAt);\n    }\n    parsePayload() {\n      this.payload = null;\n    }\n    prepareCreatedAt(date) {\n      return date ? date : new Date();\n    }\n    /**\n     * Returns the token's creation date\n     * @returns {Date}\n     */\n    getCreatedAt() {\n      return this.createdAt;\n    }\n    /**\n     * Returns the token value\n     * @returns string\n     */\n    getValue() {\n      return this.token;\n    }\n    getOwnerStrategyName() {\n      return this.ownerStrategyName;\n    }\n    /**\n     * Is non empty and valid\n     * @returns {boolean}\n     */\n    isValid() {\n      return !!this.getValue();\n    }\n    /**\n     * Validate value and convert to string, if value is not valid return empty string\n     * @returns {string}\n     */\n    toString() {\n      return !!this.token ? this.token : '';\n    }\n  }\n  return NbAuthSimpleToken;\n})();\n/**\n * Wrapper for JWT token with additional methods.\n */\nlet NbAuthJWTToken = /*#__PURE__*/(() => {\n  class NbAuthJWTToken extends NbAuthSimpleToken {\n    static {\n      this.NAME = 'nb:auth:jwt:token';\n    }\n    /**\n     * for JWT token, the iat (issued at) field of the token payload contains the creation Date\n     */\n    prepareCreatedAt(date) {\n      const decoded = this.getPayload();\n      return decoded && decoded.iat ? new Date(Number(decoded.iat) * 1000) : super.prepareCreatedAt(date);\n    }\n    /**\n     * Returns payload object\n     * @returns any\n     */\n    parsePayload() {\n      if (!this.token) {\n        throw new NbAuthTokenNotFoundError('Token not found. ');\n      }\n      this.payload = decodeJwtPayload(this.token);\n    }\n    /**\n     * Returns expiration date\n     * @returns Date\n     */\n    getTokenExpDate() {\n      const decoded = this.getPayload();\n      if (decoded && !decoded.hasOwnProperty('exp')) {\n        return null;\n      }\n      const date = new Date(0);\n      date.setUTCSeconds(decoded.exp); // 'cause jwt token are set in seconds\n      return date;\n    }\n    /**\n     * Is data expired\n     * @returns {boolean}\n     */\n    isValid() {\n      return super.isValid() && (!this.getTokenExpDate() || new Date() < this.getTokenExpDate());\n    }\n  }\n  return NbAuthJWTToken;\n})();\nconst prepareOAuth2Token = data => {\n  if (typeof data === 'string') {\n    try {\n      return JSON.parse(data);\n    } catch (e) {}\n  }\n  return data;\n};\n/**\n * Wrapper for OAuth2 token whose access_token is a JWT Token\n */\nlet NbAuthOAuth2Token = /*#__PURE__*/(() => {\n  class NbAuthOAuth2Token extends NbAuthSimpleToken {\n    static {\n      this.NAME = 'nb:auth:oauth2:token';\n    }\n    constructor(data = {}, ownerStrategyName, createdAt) {\n      // we may get it as string when retrieving from a storage\n      super(prepareOAuth2Token(data), ownerStrategyName, createdAt);\n    }\n    /**\n     * Returns the token value\n     * @returns string\n     */\n    getValue() {\n      return this.token.access_token;\n    }\n    /**\n     * Returns the refresh token\n     * @returns string\n     */\n    getRefreshToken() {\n      return this.token.refresh_token;\n    }\n    /**\n     *  put refreshToken in the token payload\n      * @param refreshToken\n     */\n    setRefreshToken(refreshToken) {\n      this.token.refresh_token = refreshToken;\n    }\n    /**\n     * Parses token payload\n     * @returns any\n     */\n    parsePayload() {\n      if (!this.token) {\n        throw new NbAuthTokenNotFoundError('Token not found.');\n      } else {\n        if (!Object.keys(this.token).length) {\n          throw new NbAuthEmptyTokenError('Cannot extract payload from an empty token.');\n        }\n      }\n      this.payload = this.token;\n    }\n    /**\n     * Returns the token type\n     * @returns string\n     */\n    getType() {\n      return this.token.token_type;\n    }\n    /**\n     * Is data expired\n     * @returns {boolean}\n     */\n    isValid() {\n      return super.isValid() && (!this.getTokenExpDate() || new Date() < this.getTokenExpDate());\n    }\n    /**\n     * Returns expiration date\n     * @returns Date\n     */\n    getTokenExpDate() {\n      if (!this.token.hasOwnProperty('expires_in')) {\n        return null;\n      }\n      return new Date(this.createdAt.getTime() + Number(this.token.expires_in) * 1000);\n    }\n    /**\n     * Convert to string\n     * @returns {string}\n     */\n    toString() {\n      return JSON.stringify(this.token);\n    }\n  }\n  return NbAuthOAuth2Token;\n})();\n/**\n * Wrapper for OAuth2 token embedding JWT tokens\n */\nlet NbAuthOAuth2JWTToken = /*#__PURE__*/(() => {\n  class NbAuthOAuth2JWTToken extends NbAuthOAuth2Token {\n    static {\n      this.NAME = 'nb:auth:oauth2:jwt:token';\n    }\n    parsePayload() {\n      super.parsePayload();\n      this.parseAccessTokenPayload();\n    }\n    parseAccessTokenPayload() {\n      const accessToken = this.getValue();\n      if (!accessToken) {\n        throw new NbAuthTokenNotFoundError('access_token key not found.');\n      }\n      this.accessTokenPayload = decodeJwtPayload(accessToken);\n    }\n    /**\n     * Returns access token payload\n     * @returns any\n     */\n    getAccessTokenPayload() {\n      return this.accessTokenPayload;\n    }\n    /**\n     * for Oauth2 JWT token, the iat (issued at) field of the access_token payload\n     */\n    prepareCreatedAt(date) {\n      const payload = this.accessTokenPayload;\n      return payload && payload.iat ? new Date(Number(payload.iat) * 1000) : super.prepareCreatedAt(date);\n    }\n    /**\n     * Is token valid\n     * @returns {boolean}\n     */\n    isValid() {\n      return this.accessTokenPayload && super.isValid();\n    }\n    /**\n     * Returns expiration date :\n     * - exp if set,\n     * - super.getExpDate() otherwise\n     * @returns Date\n     */\n    getTokenExpDate() {\n      if (this.accessTokenPayload && this.accessTokenPayload.hasOwnProperty('exp')) {\n        const date = new Date(0);\n        date.setUTCSeconds(this.accessTokenPayload.exp);\n        return date;\n      } else {\n        return super.getTokenExpDate();\n      }\n    }\n  }\n  return NbAuthOAuth2JWTToken;\n})();\nconst NB_AUTH_FALLBACK_TOKEN = new InjectionToken('Nebular Auth Options');\n/**\n * Creates a token parcel which could be stored/restored\n */\nlet NbAuthTokenParceler = /*#__PURE__*/(() => {\n  class NbAuthTokenParceler {\n    constructor(fallbackClass, tokenClasses) {\n      this.fallbackClass = fallbackClass;\n      this.tokenClasses = tokenClasses;\n    }\n    wrap(token) {\n      return JSON.stringify({\n        name: token.getName(),\n        ownerStrategyName: token.getOwnerStrategyName(),\n        createdAt: token.getCreatedAt().getTime(),\n        value: token.toString()\n      });\n    }\n    unwrap(value) {\n      let tokenClass = this.fallbackClass;\n      let tokenValue = '';\n      let tokenOwnerStrategyName = '';\n      let tokenCreatedAt = null;\n      const tokenPack = this.parseTokenPack(value);\n      if (tokenPack) {\n        tokenClass = this.getClassByName(tokenPack.name) || this.fallbackClass;\n        tokenValue = tokenPack.value;\n        tokenOwnerStrategyName = tokenPack.ownerStrategyName;\n        tokenCreatedAt = new Date(Number(tokenPack.createdAt));\n      }\n      return nbAuthCreateToken(tokenClass, tokenValue, tokenOwnerStrategyName, tokenCreatedAt);\n    }\n    // TODO: this could be moved to a separate token registry\n    getClassByName(name) {\n      return this.tokenClasses.find(tokenClass => tokenClass.NAME === name);\n    }\n    parseTokenPack(value) {\n      try {\n        return JSON.parse(value);\n      } catch (e) {}\n      return null;\n    }\n    static {\n      this.ɵfac = function NbAuthTokenParceler_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || NbAuthTokenParceler)(i0.ɵɵinject(NB_AUTH_FALLBACK_TOKEN), i0.ɵɵinject(NB_AUTH_TOKENS));\n      };\n    }\n    static {\n      this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n        token: NbAuthTokenParceler,\n        factory: NbAuthTokenParceler.ɵfac\n      });\n    }\n  }\n  return NbAuthTokenParceler;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nclass NbTokenStorage {}\n/**\n * Service that uses browser localStorage as a storage.\n *\n * The token storage is provided into auth module the following way:\n * ```ts\n * { provide: NbTokenStorage, useClass: NbTokenLocalStorage },\n * ```\n *\n * If you need to change the storage behaviour or provide your own - just extend your class from basic `NbTokenStorage`\n * or `NbTokenLocalStorage` and provide in your `app.module`:\n * ```ts\n * { provide: NbTokenStorage, useClass: NbTokenCustomStorage },\n * ```\n *\n */\nlet NbTokenLocalStorage = /*#__PURE__*/(() => {\n  class NbTokenLocalStorage extends NbTokenStorage {\n    constructor(parceler) {\n      super();\n      this.parceler = parceler;\n      this.key = 'auth_app_token';\n    }\n    /**\n     * Returns token from localStorage\n     * @returns {NbAuthToken}\n     */\n    get() {\n      const raw = localStorage.getItem(this.key);\n      return this.parceler.unwrap(raw);\n    }\n    /**\n     * Sets token to localStorage\n     * @param {NbAuthToken} token\n     */\n    set(token) {\n      const raw = this.parceler.wrap(token);\n      localStorage.setItem(this.key, raw);\n    }\n    /**\n     * Clears token from localStorage\n     */\n    clear() {\n      localStorage.removeItem(this.key);\n    }\n    static {\n      this.ɵfac = function NbTokenLocalStorage_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || NbTokenLocalStorage)(i0.ɵɵinject(NbAuthTokenParceler));\n      };\n    }\n    static {\n      this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n        token: NbTokenLocalStorage,\n        factory: NbTokenLocalStorage.ɵfac\n      });\n    }\n  }\n  return NbTokenLocalStorage;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/**\n * Service that allows you to manage authentication token - get, set, clear and also listen to token changes over time.\n */\nlet NbTokenService = /*#__PURE__*/(() => {\n  class NbTokenService {\n    constructor(tokenStorage) {\n      this.tokenStorage = tokenStorage;\n      this.token$ = new BehaviorSubject(null);\n      this.publishStoredToken();\n    }\n    /**\n     * Publishes token when it changes.\n     * @returns {Observable<NbAuthToken>}\n     */\n    tokenChange() {\n      return this.token$.pipe(filter(value => !!value), share());\n    }\n    /**\n     * Sets a token into the storage. This method is used by the NbAuthService automatically.\n     *\n     * @param {NbAuthToken} token\n     * @returns {Observable<any>}\n     */\n    set(token) {\n      this.tokenStorage.set(token);\n      this.publishStoredToken();\n      return of(null);\n    }\n    /**\n     * Returns observable of current token\n     * @returns {Observable<NbAuthToken>}\n     */\n    get() {\n      const token = this.tokenStorage.get();\n      return of(token);\n    }\n    /**\n     * Removes the token and published token value\n     *\n     * @returns {Observable<any>}\n     */\n    clear() {\n      this.tokenStorage.clear();\n      this.publishStoredToken();\n      return of(null);\n    }\n    publishStoredToken() {\n      this.token$.next(this.tokenStorage.get());\n    }\n    static {\n      this.ɵfac = function NbTokenService_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || NbTokenService)(i0.ɵɵinject(NbTokenStorage));\n      };\n    }\n    static {\n      this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n        token: NbTokenService,\n        factory: NbTokenService.ɵfac\n      });\n    }\n  }\n  return NbTokenService;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/**\n * @license\n * Copyright Akveo. All Rights Reserved.\n * Licensed under the MIT License. See License.txt in the project root for license information.\n */\n/**\n * Common authentication service.\n * Should be used to as an interlayer between UI Components and Auth Strategy.\n */\nlet NbAuthService = /*#__PURE__*/(() => {\n  class NbAuthService {\n    constructor(tokenService, strategies) {\n      this.tokenService = tokenService;\n      this.strategies = strategies;\n    }\n    /**\n     * Retrieves current authenticated token stored\n     * @returns {Observable<any>}\n     */\n    getToken() {\n      return this.tokenService.get();\n    }\n    /**\n     * Returns true if auth token is present in the token storage\n     * @returns {Observable<boolean>}\n     */\n    isAuthenticated() {\n      return this.getToken().pipe(map(token => token.isValid()));\n    }\n    /**\n     * Returns true if valid auth token is present in the token storage.\n     * If not, calls the strategy refreshToken, and returns isAuthenticated() if success, false otherwise\n     * @returns {Observable<boolean>}\n     */\n    isAuthenticatedOrRefresh() {\n      return this.getToken().pipe(switchMap(token => {\n        if (token.getValue() && !token.isValid()) {\n          return this.refreshToken(token.getOwnerStrategyName(), token).pipe(switchMap(res => {\n            if (res.isSuccess()) {\n              return this.isAuthenticated();\n            } else {\n              return of(false);\n            }\n          }));\n        } else {\n          return of(token.isValid());\n        }\n      }));\n    }\n    /**\n     * Returns tokens stream\n     * @returns {Observable<NbAuthSimpleToken>}\n     */\n    onTokenChange() {\n      return this.tokenService.tokenChange();\n    }\n    /**\n     * Returns authentication status stream\n     * @returns {Observable<boolean>}\n     */\n    onAuthenticationChange() {\n      return this.onTokenChange().pipe(map(token => token.isValid()));\n    }\n    /**\n     * Authenticates with the selected strategy\n     * Stores received token in the token storage\n     *\n     * Example:\n     * authenticate('email', {email: '<EMAIL>', password: 'test'})\n     *\n     * @param strategyName\n     * @param data\n     * @returns {Observable<NbAuthResult>}\n     */\n    authenticate(strategyName, data) {\n      return this.getStrategy(strategyName).authenticate(data).pipe(switchMap(result => {\n        return this.processResultToken(result);\n      }));\n    }\n    /**\n     * Registers with the selected strategy\n     * Stores received token in the token storage\n     *\n     * Example:\n     * register('email', {email: '<EMAIL>', name: 'Some Name', password: 'test'})\n     *\n     * @param strategyName\n     * @param data\n     * @returns {Observable<NbAuthResult>}\n     */\n    register(strategyName, data) {\n      return this.getStrategy(strategyName).register(data).pipe(switchMap(result => {\n        return this.processResultToken(result);\n      }));\n    }\n    /**\n     * Sign outs with the selected strategy\n     * Removes token from the token storage\n     *\n     * Example:\n     * logout('email')\n     *\n     * @param strategyName\n     * @returns {Observable<NbAuthResult>}\n     */\n    logout(strategyName) {\n      return this.getStrategy(strategyName).logout().pipe(switchMap(result => {\n        if (result.isSuccess()) {\n          this.tokenService.clear().pipe(map(() => result));\n        }\n        return of(result);\n      }));\n    }\n    /**\n     * Sends forgot password request to the selected strategy\n     *\n     * Example:\n     * requestPassword('email', {email: '<EMAIL>'})\n     *\n     * @param strategyName\n     * @param data\n     * @returns {Observable<NbAuthResult>}\n     */\n    requestPassword(strategyName, data) {\n      return this.getStrategy(strategyName).requestPassword(data);\n    }\n    /**\n     * Tries to reset password with the selected strategy\n     *\n     * Example:\n     * resetPassword('email', {newPassword: 'test'})\n     *\n     * @param strategyName\n     * @param data\n     * @returns {Observable<NbAuthResult>}\n     */\n    resetPassword(strategyName, data) {\n      return this.getStrategy(strategyName).resetPassword(data);\n    }\n    /**\n     * Sends a refresh token request\n     * Stores received token in the token storage\n     *\n     * Example:\n     * refreshToken('email', {token: token})\n     *\n     * @param {string} strategyName\n     * @param data\n     * @returns {Observable<NbAuthResult>}\n     */\n    refreshToken(strategyName, data) {\n      return this.getStrategy(strategyName).refreshToken(data).pipe(switchMap(result => {\n        return this.processResultToken(result);\n      }));\n    }\n    /**\n     * Get registered strategy by name\n     *\n     * Example:\n     * getStrategy('email')\n     *\n     * @param {string} provider\n     * @returns {NbAbstractAuthProvider}\n     */\n    getStrategy(strategyName) {\n      const found = this.strategies.find(strategy => strategy.getName() === strategyName);\n      if (!found) {\n        throw new TypeError(`There is no Auth Strategy registered under '${strategyName}' name`);\n      }\n      return found;\n    }\n    processResultToken(result) {\n      if (result.isSuccess() && result.getToken()) {\n        return this.tokenService.set(result.getToken()).pipe(map(token => {\n          return result;\n        }));\n      }\n      return of(result);\n    }\n    static {\n      this.ɵfac = function NbAuthService_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || NbAuthService)(i0.ɵɵinject(NbTokenService), i0.ɵɵinject(NB_AUTH_STRATEGIES));\n      };\n    }\n    static {\n      this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n        token: NbAuthService,\n        factory: NbAuthService.ɵfac\n      });\n    }\n  }\n  return NbAuthService;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nclass NbAuthStrategy {\n  // we should keep this any and validation should be done in `register` method instead\n  // otherwise it won't be possible to pass an empty object\n  setOptions(options) {\n    this.options = deepExtend({}, this.defaultOptions, options);\n  }\n  getOption(key) {\n    return getDeepFromObject(this.options, key, null);\n  }\n  createToken(value, failWhenInvalidToken) {\n    const token = nbAuthCreateToken(this.getOption('token.class'), value, this.getName());\n    // At this point, nbAuthCreateToken failed with NbAuthIllegalTokenError which MUST be intercepted by strategies\n    // Or token is created. It MAY be created even if backend did not return any token, in this case it is !Valid\n    if (failWhenInvalidToken && !token.isValid()) {\n      // If we require a valid token (i.e. isValid), then we MUST throw NbAuthIllegalTokenError so that the strategies\n      // intercept it\n      throw new NbAuthIllegalTokenError('Token is empty or invalid.');\n    }\n    return token;\n  }\n  getName() {\n    return this.getOption('name');\n  }\n  createFailResponse(data) {\n    return new HttpResponse({\n      body: {},\n      status: 401\n    });\n  }\n  createSuccessResponse(data) {\n    return new HttpResponse({\n      body: {},\n      status: 200\n    });\n  }\n  getActionEndpoint(action) {\n    const actionEndpoint = this.getOption(`${action}.endpoint`);\n    const baseEndpoint = this.getOption('baseEndpoint');\n    return actionEndpoint ? baseEndpoint + actionEndpoint : '';\n  }\n  getHeaders() {\n    const customHeaders = this.getOption('headers') ?? {};\n    if (customHeaders instanceof HttpHeaders) {\n      return customHeaders;\n    }\n    let headers = new HttpHeaders();\n    Object.entries(customHeaders).forEach(([key, value]) => {\n      headers = headers.append(key, value);\n    });\n    return headers;\n  }\n}\nclass NbAuthResult {\n  // TODO: better pass object\n  constructor(success, response, redirect, errors, messages, token = null) {\n    this.success = success;\n    this.response = response;\n    this.redirect = redirect;\n    this.errors = [];\n    this.messages = [];\n    this.errors = this.errors.concat([errors]);\n    if (errors instanceof Array) {\n      this.errors = errors;\n    }\n    this.messages = this.messages.concat([messages]);\n    if (messages instanceof Array) {\n      this.messages = messages;\n    }\n    this.token = token;\n  }\n  getResponse() {\n    return this.response;\n  }\n  getToken() {\n    return this.token;\n  }\n  getRedirect() {\n    return this.redirect;\n  }\n  getErrors() {\n    return this.errors.filter(val => !!val);\n  }\n  getMessages() {\n    return this.messages.filter(val => !!val);\n  }\n  isSuccess() {\n    return this.success;\n  }\n  isFailure() {\n    return !this.success;\n  }\n}\nclass NbAuthStrategyOptions {}\n\n/**\n * @license\n * Copyright Akveo. All Rights Reserved.\n * Licensed under the MIT License. See License.txt in the project root for license information.\n */\nclass NbDummyAuthStrategyOptions extends NbAuthStrategyOptions {\n  constructor() {\n    super(...arguments);\n    this.token = {\n      class: NbAuthSimpleToken\n    };\n    this.delay = 1000;\n    this.alwaysFail = false;\n  }\n}\nconst dummyStrategyOptions = new NbDummyAuthStrategyOptions();\n\n/**\n * Dummy auth strategy. Could be useful for auth setup when backend is not available yet.\n *\n *\n * Strategy settings.\n *\n * ```ts\n * export class NbDummyAuthStrategyOptions extends NbAuthStrategyOptions {\n *   name = 'dummy';\n *   token = {\n *     class: NbAuthSimpleToken,\n *   };\n *   delay? = 1000;\n *   alwaysFail? = false;\n * }\n * ```\n */\nlet NbDummyAuthStrategy = /*#__PURE__*/(() => {\n  class NbDummyAuthStrategy extends NbAuthStrategy {\n    constructor() {\n      super(...arguments);\n      this.defaultOptions = dummyStrategyOptions;\n    }\n    static setup(options) {\n      return [NbDummyAuthStrategy, options];\n    }\n    authenticate(data) {\n      return of(this.createDummyResult(data)).pipe(delay(this.getOption('delay')));\n    }\n    register(data) {\n      return of(this.createDummyResult(data)).pipe(delay(this.getOption('delay')));\n    }\n    requestPassword(data) {\n      return of(this.createDummyResult(data)).pipe(delay(this.getOption('delay')));\n    }\n    resetPassword(data) {\n      return of(this.createDummyResult(data)).pipe(delay(this.getOption('delay')));\n    }\n    logout(data) {\n      return of(this.createDummyResult(data)).pipe(delay(this.getOption('delay')));\n    }\n    refreshToken(data) {\n      return of(this.createDummyResult(data)).pipe(delay(this.getOption('delay')));\n    }\n    createDummyResult(data) {\n      if (this.getOption('alwaysFail')) {\n        return new NbAuthResult(false, this.createFailResponse(data), null, ['Something went wrong.']);\n      }\n      try {\n        const token = this.createToken('test token', true);\n        return new NbAuthResult(true, this.createSuccessResponse(data), '/', [], ['Successfully logged in.'], token);\n      } catch (err) {\n        return new NbAuthResult(false, this.createFailResponse(data), null, [err.message]);\n      }\n    }\n    static {\n      this.ɵfac = /* @__PURE__ */(() => {\n        let ɵNbDummyAuthStrategy_BaseFactory;\n        return function NbDummyAuthStrategy_Factory(__ngFactoryType__) {\n          return (ɵNbDummyAuthStrategy_BaseFactory || (ɵNbDummyAuthStrategy_BaseFactory = i0.ɵɵgetInheritedFactory(NbDummyAuthStrategy)))(__ngFactoryType__ || NbDummyAuthStrategy);\n        };\n      })();\n    }\n    static {\n      this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n        token: NbDummyAuthStrategy,\n        factory: NbDummyAuthStrategy.ɵfac\n      });\n    }\n  }\n  return NbDummyAuthStrategy;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/**\n * @license\n * Copyright Akveo. All Rights Reserved.\n * Licensed under the MIT License. See License.txt in the project root for license information.\n */\nvar NbOAuth2ResponseType = /*#__PURE__*/function (NbOAuth2ResponseType) {\n  NbOAuth2ResponseType[\"CODE\"] = \"code\";\n  NbOAuth2ResponseType[\"TOKEN\"] = \"token\";\n  return NbOAuth2ResponseType;\n}(NbOAuth2ResponseType || {});\n// TODO: client_credentials\nvar NbOAuth2GrantType = /*#__PURE__*/function (NbOAuth2GrantType) {\n  NbOAuth2GrantType[\"AUTHORIZATION_CODE\"] = \"authorization_code\";\n  NbOAuth2GrantType[\"PASSWORD\"] = \"password\";\n  NbOAuth2GrantType[\"REFRESH_TOKEN\"] = \"refresh_token\";\n  return NbOAuth2GrantType;\n}(NbOAuth2GrantType || {});\nvar NbOAuth2ClientAuthMethod = /*#__PURE__*/function (NbOAuth2ClientAuthMethod) {\n  NbOAuth2ClientAuthMethod[\"NONE\"] = \"none\";\n  NbOAuth2ClientAuthMethod[\"BASIC\"] = \"basic\";\n  NbOAuth2ClientAuthMethod[\"REQUEST_BODY\"] = \"request-body\";\n  return NbOAuth2ClientAuthMethod;\n}(NbOAuth2ClientAuthMethod || {});\nclass NbOAuth2AuthStrategyOptions extends NbAuthStrategyOptions {\n  constructor() {\n    super(...arguments);\n    this.baseEndpoint = '';\n    this.clientId = '';\n    this.clientSecret = '';\n    this.clientAuthMethod = NbOAuth2ClientAuthMethod.NONE;\n    this.redirect = {\n      success: '/',\n      failure: null\n    };\n    this.defaultErrors = ['Something went wrong, please try again.'];\n    this.defaultMessages = ['You have been successfully authenticated.'];\n    this.authorize = {\n      endpoint: 'authorize',\n      responseType: NbOAuth2ResponseType.CODE,\n      requireValidToken: true\n    };\n    this.token = {\n      endpoint: 'token',\n      grantType: NbOAuth2GrantType.AUTHORIZATION_CODE,\n      requireValidToken: true,\n      class: NbAuthOAuth2Token\n    };\n    this.refresh = {\n      endpoint: 'token',\n      grantType: NbOAuth2GrantType.REFRESH_TOKEN,\n      requireValidToken: true\n    };\n  }\n}\nconst auth2StrategyOptions = new NbOAuth2AuthStrategyOptions();\n\n/**\n * @license\n * Copyright Akveo. All Rights Reserved.\n * Licensed under the MIT License. See License.txt in the project root for license information.\n */\n/**\n * OAuth2 authentication strategy.\n *\n * Strategy settings:\n *\n * ```ts\n * export enum NbOAuth2ResponseType {\n *   CODE = 'code',\n *   TOKEN = 'token',\n * }\n *\n * export enum NbOAuth2GrantType {\n *   AUTHORIZATION_CODE = 'authorization_code',\n *   PASSWORD = 'password',\n *   REFRESH_TOKEN = 'refresh_token',\n * }\n *\n * export class NbOAuth2AuthStrategyOptions {\n *   name: string;\n *   baseEndpoint?: string = '';\n *   clientId: string = '';\n *   clientSecret: string = '';\n *   clientAuthMethod: string = NbOAuth2ClientAuthMethod.NONE;\n *   redirect?: { success?: string; failure?: string } = {\n *     success: '/',\n *     failure: null,\n *   };\n *   defaultErrors?: any[] = ['Something went wrong, please try again.'];\n *   defaultMessages?: any[] = ['You have been successfully authenticated.'];\n *   authorize?: {\n *     endpoint?: string;\n *     redirectUri?: string;\n *     responseType?: string;\n *     requireValidToken: true,\n *     scope?: string;\n *     state?: string;\n *     params?: { [key: string]: string };\n *   } = {\n *     endpoint: 'authorize',\n *     responseType: NbOAuth2ResponseType.CODE,\n *   };\n *   token?: {\n *     endpoint?: string;\n *     grantType?: string;\n *     requireValidToken: true,\n *     redirectUri?: string;\n *     scope?: string;\n *     class: NbAuthTokenClass,\n *   } = {\n *     endpoint: 'token',\n *     grantType: NbOAuth2GrantType.AUTHORIZATION_CODE,\n *     class: NbAuthOAuth2Token,\n *   };\n *   refresh?: {\n *     endpoint?: string;\n *     grantType?: string;\n *     scope?: string;\n *     requireValidToken: true,\n *   } = {\n *     endpoint: 'token',\n *     grantType: NbOAuth2GrantType.REFRESH_TOKEN,\n *   };\n * }\n * ```\n *\n */\nlet NbOAuth2AuthStrategy = /*#__PURE__*/(() => {\n  class NbOAuth2AuthStrategy extends NbAuthStrategy {\n    static setup(options) {\n      return [NbOAuth2AuthStrategy, options];\n    }\n    get responseType() {\n      return this.getOption('authorize.responseType');\n    }\n    get clientAuthMethod() {\n      return this.getOption('clientAuthMethod');\n    }\n    constructor(http, route, window) {\n      super();\n      this.http = http;\n      this.route = route;\n      this.window = window;\n      this.redirectResultHandlers = {\n        [NbOAuth2ResponseType.CODE]: () => {\n          return of(this.route.snapshot.queryParams).pipe(switchMap(params => {\n            if (params.code) {\n              return this.requestToken(params.code);\n            }\n            return of(new NbAuthResult(false, params, this.getOption('redirect.failure'), this.getOption('defaultErrors'), []));\n          }));\n        },\n        [NbOAuth2ResponseType.TOKEN]: () => {\n          const module = 'authorize';\n          const requireValidToken = this.getOption(`${module}.requireValidToken`);\n          return of(this.route.snapshot.fragment).pipe(map(fragment => this.parseHashAsQueryParams(fragment)), map(params => {\n            if (!params.error) {\n              return new NbAuthResult(true, params, this.getOption('redirect.success'), [], this.getOption('defaultMessages'), this.createToken(params, requireValidToken));\n            }\n            return new NbAuthResult(false, params, this.getOption('redirect.failure'), this.getOption('defaultErrors'), []);\n          }), catchError(err => {\n            const errors = [];\n            if (err instanceof NbAuthIllegalTokenError) {\n              errors.push(err.message);\n            } else {\n              errors.push('Something went wrong.');\n            }\n            return of(new NbAuthResult(false, err, this.getOption('redirect.failure'), errors));\n          }));\n        }\n      };\n      this.redirectResults = {\n        [NbOAuth2ResponseType.CODE]: () => {\n          return of(this.route.snapshot.queryParams).pipe(map(params => !!(params && (params.code || params.error))));\n        },\n        [NbOAuth2ResponseType.TOKEN]: () => {\n          return of(this.route.snapshot.fragment).pipe(map(fragment => this.parseHashAsQueryParams(fragment)), map(params => !!(params && (params.access_token || params.error))));\n        }\n      };\n      this.defaultOptions = auth2StrategyOptions;\n    }\n    authenticate(data) {\n      if (this.getOption('token.grantType') === NbOAuth2GrantType.PASSWORD) {\n        return this.passwordToken(data.email, data.password);\n      } else {\n        return this.isRedirectResult().pipe(switchMap(result => {\n          if (!result) {\n            this.authorizeRedirect();\n            return of(new NbAuthResult(true));\n          }\n          return this.getAuthorizationResult();\n        }));\n      }\n    }\n    getAuthorizationResult() {\n      const redirectResultHandler = this.redirectResultHandlers[this.responseType];\n      if (redirectResultHandler) {\n        return redirectResultHandler.call(this);\n      }\n      throw new Error(`'${this.responseType}' responseType is not supported,\n                      only 'token' and 'code' are supported now`);\n    }\n    refreshToken(token) {\n      const module = 'refresh';\n      const url = this.getActionEndpoint(module);\n      const requireValidToken = this.getOption(`${module}.requireValidToken`);\n      return this.http.post(url, this.buildRefreshRequestData(token), {\n        headers: this.getHeaders()\n      }).pipe(map(res => {\n        return new NbAuthResult(true, res, this.getOption('redirect.success'), [], this.getOption('defaultMessages'), this.createRefreshedToken(res, token, requireValidToken));\n      }), catchError(res => this.handleResponseError(res)));\n    }\n    passwordToken(username, password) {\n      const module = 'token';\n      const url = this.getActionEndpoint(module);\n      const requireValidToken = this.getOption(`${module}.requireValidToken`);\n      return this.http.post(url, this.buildPasswordRequestData(username, password), {\n        headers: this.getHeaders()\n      }).pipe(map(res => {\n        return new NbAuthResult(true, res, this.getOption('redirect.success'), [], this.getOption('defaultMessages'), this.createToken(res, requireValidToken));\n      }), catchError(res => this.handleResponseError(res)));\n    }\n    authorizeRedirect() {\n      this.window.location.href = this.buildRedirectUrl();\n    }\n    isRedirectResult() {\n      return this.redirectResults[this.responseType].call(this);\n    }\n    requestToken(code) {\n      const module = 'token';\n      const url = this.getActionEndpoint(module);\n      const requireValidToken = this.getOption(`${module}.requireValidToken`);\n      return this.http.post(url, this.buildCodeRequestData(code), {\n        headers: this.getHeaders()\n      }).pipe(map(res => {\n        return new NbAuthResult(true, res, this.getOption('redirect.success'), [], this.getOption('defaultMessages'), this.createToken(res, requireValidToken));\n      }), catchError(res => this.handleResponseError(res)));\n    }\n    buildCodeRequestData(code) {\n      const params = {\n        grant_type: this.getOption('token.grantType'),\n        code: code,\n        redirect_uri: this.getOption('token.redirectUri'),\n        client_id: this.getOption('clientId')\n      };\n      return this.urlEncodeParameters(this.cleanParams(this.addCredentialsToParams(params)));\n    }\n    buildRefreshRequestData(token) {\n      const params = {\n        grant_type: this.getOption('refresh.grantType'),\n        refresh_token: token.getRefreshToken(),\n        scope: this.getOption('refresh.scope'),\n        client_id: this.getOption('clientId')\n      };\n      return this.urlEncodeParameters(this.cleanParams(this.addCredentialsToParams(params)));\n    }\n    buildPasswordRequestData(username, password) {\n      const params = {\n        grant_type: this.getOption('token.grantType'),\n        username: username,\n        password: password,\n        scope: this.getOption('token.scope')\n      };\n      return this.urlEncodeParameters(this.cleanParams(this.addCredentialsToParams(params)));\n    }\n    buildAuthHeader() {\n      if (this.clientAuthMethod === NbOAuth2ClientAuthMethod.BASIC) {\n        if (this.getOption('clientId') && this.getOption('clientSecret')) {\n          return new HttpHeaders({\n            Authorization: 'Basic ' + btoa(this.getOption('clientId') + ':' + this.getOption('clientSecret'))\n          });\n        } else {\n          throw Error('For basic client authentication method, please provide both clientId & clientSecret.');\n        }\n      }\n      return undefined;\n    }\n    getHeaders() {\n      let headers = super.getHeaders();\n      headers = headers.append('Content-Type', 'application/x-www-form-urlencoded');\n      const authHeaders = this.buildAuthHeader();\n      if (authHeaders === undefined) {\n        return headers;\n      }\n      for (const headerKey of authHeaders.keys()) {\n        for (const headerValue of authHeaders.getAll(headerKey)) {\n          headers = headers.append(headerKey, headerValue);\n        }\n      }\n      return headers;\n    }\n    cleanParams(params) {\n      Object.entries(params).forEach(([key, val]) => !val && delete params[key]);\n      return params;\n    }\n    addCredentialsToParams(params) {\n      if (this.clientAuthMethod === NbOAuth2ClientAuthMethod.REQUEST_BODY) {\n        if (this.getOption('clientId') && this.getOption('clientSecret')) {\n          return {\n            ...params,\n            client_id: this.getOption('clientId'),\n            client_secret: this.getOption('clientSecret')\n          };\n        } else {\n          throw Error('For request body client authentication method, please provide both clientId & clientSecret.');\n        }\n      }\n      return params;\n    }\n    handleResponseError(res) {\n      let errors = [];\n      if (res instanceof HttpErrorResponse) {\n        if (res.error.error_description) {\n          errors.push(res.error.error_description);\n        } else {\n          errors = this.getOption('defaultErrors');\n        }\n      } else if (res instanceof NbAuthIllegalTokenError) {\n        errors.push(res.message);\n      } else {\n        errors.push('Something went wrong.');\n      }\n      return of(new NbAuthResult(false, res, this.getOption('redirect.failure'), errors, []));\n    }\n    buildRedirectUrl() {\n      const params = {\n        response_type: this.getOption('authorize.responseType'),\n        client_id: this.getOption('clientId'),\n        redirect_uri: this.getOption('authorize.redirectUri'),\n        scope: this.getOption('authorize.scope'),\n        state: this.getOption('authorize.state'),\n        ...this.getOption('authorize.params')\n      };\n      const endpoint = this.getActionEndpoint('authorize');\n      const query = this.urlEncodeParameters(this.cleanParams(params));\n      return `${endpoint}?${query}`;\n    }\n    parseHashAsQueryParams(hash) {\n      return hash ? hash.split('&').reduce((acc, part) => {\n        const item = part.split('=');\n        acc[item[0]] = decodeURIComponent(item[1]);\n        return acc;\n      }, {}) : {};\n    }\n    urlEncodeParameters(params) {\n      return Object.keys(params).map(k => {\n        return `${encodeURIComponent(k)}=${encodeURIComponent(params[k])}`;\n      }).join('&');\n    }\n    createRefreshedToken(res, existingToken, requireValidToken) {\n      const refreshedToken = this.createToken(res, requireValidToken);\n      if (!refreshedToken.getRefreshToken() && existingToken.getRefreshToken()) {\n        refreshedToken.setRefreshToken(existingToken.getRefreshToken());\n      }\n      return refreshedToken;\n    }\n    register(data) {\n      throw new Error('`register` is not supported by `NbOAuth2AuthStrategy`, use `authenticate`.');\n    }\n    requestPassword(data) {\n      throw new Error('`requestPassword` is not supported by `NbOAuth2AuthStrategy`, use `authenticate`.');\n    }\n    resetPassword(data = {}) {\n      throw new Error('`resetPassword` is not supported by `NbOAuth2AuthStrategy`, use `authenticate`.');\n    }\n    logout() {\n      return of(new NbAuthResult(true));\n    }\n    static {\n      this.ɵfac = function NbOAuth2AuthStrategy_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || NbOAuth2AuthStrategy)(i0.ɵɵinject(i1.HttpClient), i0.ɵɵinject(i2.ActivatedRoute), i0.ɵɵinject(NB_WINDOW));\n      };\n    }\n    static {\n      this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n        token: NbOAuth2AuthStrategy,\n        factory: NbOAuth2AuthStrategy.ɵfac\n      });\n    }\n  }\n  return NbOAuth2AuthStrategy;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/**\n * @license\n * Copyright Akveo. All Rights Reserved.\n * Licensed under the MIT License. See License.txt in the project root for license information.\n */\nclass NbPasswordAuthStrategyOptions extends NbAuthStrategyOptions {\n  constructor() {\n    super(...arguments);\n    this.baseEndpoint = '/api/auth/';\n    this.login = {\n      alwaysFail: false,\n      endpoint: 'login',\n      method: 'post',\n      requireValidToken: true,\n      redirect: {\n        success: '/',\n        failure: null\n      },\n      defaultErrors: ['Login/Email combination is not correct, please try again.'],\n      defaultMessages: ['You have been successfully logged in.']\n    };\n    this.register = {\n      alwaysFail: false,\n      endpoint: 'register',\n      method: 'post',\n      requireValidToken: true,\n      redirect: {\n        success: '/',\n        failure: null\n      },\n      defaultErrors: ['Something went wrong, please try again.'],\n      defaultMessages: ['You have been successfully registered.']\n    };\n    this.requestPass = {\n      endpoint: 'request-pass',\n      method: 'post',\n      redirect: {\n        success: '/',\n        failure: null\n      },\n      defaultErrors: ['Something went wrong, please try again.'],\n      defaultMessages: ['Reset password instructions have been sent to your email.']\n    };\n    this.resetPass = {\n      endpoint: 'reset-pass',\n      method: 'put',\n      redirect: {\n        success: '/',\n        failure: null\n      },\n      resetPasswordTokenKey: 'reset_password_token',\n      defaultErrors: ['Something went wrong, please try again.'],\n      defaultMessages: ['Your password has been successfully changed.']\n    };\n    this.logout = {\n      alwaysFail: false,\n      endpoint: 'logout',\n      method: 'delete',\n      redirect: {\n        success: '/',\n        failure: null\n      },\n      defaultErrors: ['Something went wrong, please try again.'],\n      defaultMessages: ['You have been successfully logged out.']\n    };\n    this.refreshToken = {\n      endpoint: 'refresh-token',\n      method: 'post',\n      requireValidToken: true,\n      redirect: {\n        success: null,\n        failure: null\n      },\n      defaultErrors: ['Something went wrong, please try again.'],\n      defaultMessages: ['Your token has been successfully refreshed.']\n    };\n    this.token = {\n      class: NbAuthSimpleToken,\n      key: 'data.token',\n      getter: (module, res, options) => getDeepFromObject(res.body, options.token.key)\n    };\n    this.errors = {\n      key: 'data.errors',\n      getter: (module, res, options) => getDeepFromObject(res.error, options.errors.key, options[module].defaultErrors)\n    };\n    this.messages = {\n      key: 'data.messages',\n      getter: (module, res, options) => getDeepFromObject(res.body, options.messages.key, options[module].defaultMessages)\n    };\n  }\n}\nconst passwordStrategyOptions = new NbPasswordAuthStrategyOptions();\n\n/**\n * @license\n * Copyright Akveo. All Rights Reserved.\n * Licensed under the MIT License. See License.txt in the project root for license information.\n */\n/**\n * The most common authentication provider for email/password strategy.\n *\n * Strategy settings. Note, there is no need to copy over the whole object to change the settings you need.\n * Also, this.getOption call won't work outside of the default options declaration\n * (which is inside of the `NbPasswordAuthStrategy` class), so you have to replace it with a custom helper function\n * if you need it.\n *\n * ```ts\n *export class NbPasswordAuthStrategyOptions extends NbAuthStrategyOptions {\n *  name: string;\n *  baseEndpoint? = '/api/auth/';\n *  login?: boolean | NbPasswordStrategyModule = {\n *    alwaysFail: false,\n *    endpoint: 'login',\n *    method: 'post',\n *    requireValidToken: true,\n *    redirect: {\n *      success: '/',\n *      failure: null,\n *    },\n *    defaultErrors: ['Login/Email combination is not correct, please try again.'],\n *    defaultMessages: ['You have been successfully logged in.'],\n *  };\n *  register?: boolean | NbPasswordStrategyModule = {\n *    alwaysFail: false,\n *    endpoint: 'register',\n *    method: 'post',\n *    requireValidToken: true,\n *    redirect: {\n *      success: '/',\n *      failure: null,\n *    },\n *    defaultErrors: ['Something went wrong, please try again.'],\n *    defaultMessages: ['You have been successfully registered.'],\n *  };\n *  requestPass?: boolean | NbPasswordStrategyModule = {\n *    endpoint: 'request-pass',\n *    method: 'post',\n *    redirect: {\n *      success: '/',\n *      failure: null,\n *    },\n *    defaultErrors: ['Something went wrong, please try again.'],\n *    defaultMessages: ['Reset password instructions have been sent to your email.'],\n *  };\n *  resetPass?: boolean | NbPasswordStrategyReset = {\n *    endpoint: 'reset-pass',\n *    method: 'put',\n *    redirect: {\n *      success: '/',\n *      failure: null,\n *    },\n *    resetPasswordTokenKey: 'reset_password_token',\n *    defaultErrors: ['Something went wrong, please try again.'],\n *    defaultMessages: ['Your password has been successfully changed.'],\n *  };\n *  logout?: boolean | NbPasswordStrategyReset = {\n *    alwaysFail: false,\n *    endpoint: 'logout',\n *    method: 'delete',\n *    redirect: {\n *      success: '/',\n *      failure: null,\n *    },\n *    defaultErrors: ['Something went wrong, please try again.'],\n *    defaultMessages: ['You have been successfully logged out.'],\n *  };\n *  refreshToken?: boolean | NbPasswordStrategyModule = {\n *    endpoint: 'refresh-token',\n *    method: 'post',\n *    requireValidToken: true,\n *    redirect: {\n *      success: null,\n *      failure: null,\n *    },\n *    defaultErrors: ['Something went wrong, please try again.'],\n *    defaultMessages: ['Your token has been successfully refreshed.'],\n *  };\n *  token?: NbPasswordStrategyToken = {\n *    class: NbAuthSimpleToken,\n *    key: 'data.token',\n *    getter: (module: string, res: HttpResponse<Object>, options: NbPasswordAuthStrategyOptions) => getDeepFromObject(\n *      res.body,\n *      options.token.key,\n *    ),\n *  };\n *  errors?: NbPasswordStrategyMessage = {\n *    key: 'data.errors',\n *    getter: (module: string, res: HttpErrorResponse, options: NbPasswordAuthStrategyOptions) => getDeepFromObject(\n *      res.error,\n *      options.errors.key,\n *      options[module].defaultErrors,\n *    ),\n *  };\n *  messages?: NbPasswordStrategyMessage = {\n *    key: 'data.messages',\n *    getter: (module: string, res: HttpResponse<Object>, options: NbPasswordAuthStrategyOptions) => getDeepFromObject(\n *      res.body,\n *      options.messages.key,\n *      options[module].defaultMessages,\n *    ),\n *  };\n *  validation?: {\n *    password?: {\n *      required?: boolean;\n *      minLength?: number | null;\n *      maxLength?: number | null;\n *      regexp?: string | null;\n *    };\n *    email?: {\n *      required?: boolean;\n *      regexp?: string | null;\n *    };\n *    fullName?: {\n *      required?: boolean;\n *      minLength?: number | null;\n *      maxLength?: number | null;\n *      regexp?: string | null;\n *    };\n *  };\n *}\n * ```\n */\nlet NbPasswordAuthStrategy = /*#__PURE__*/(() => {\n  class NbPasswordAuthStrategy extends NbAuthStrategy {\n    static setup(options) {\n      return [NbPasswordAuthStrategy, options];\n    }\n    constructor(http, route) {\n      super();\n      this.http = http;\n      this.route = route;\n      this.defaultOptions = passwordStrategyOptions;\n    }\n    authenticate(data) {\n      const module = 'login';\n      const method = this.getOption(`${module}.method`);\n      const url = this.getActionEndpoint(module);\n      const requireValidToken = this.getOption(`${module}.requireValidToken`);\n      return this.http.request(method, url, {\n        body: data,\n        observe: 'response',\n        headers: this.getHeaders()\n      }).pipe(map(res => {\n        if (this.getOption(`${module}.alwaysFail`)) {\n          throw this.createFailResponse(data);\n        }\n        return res;\n      }), map(res => {\n        return new NbAuthResult(true, res, this.getOption(`${module}.redirect.success`), [], this.getOption('messages.getter')(module, res, this.options), this.createToken(this.getOption('token.getter')(module, res, this.options), requireValidToken));\n      }), catchError(res => {\n        return this.handleResponseError(res, module);\n      }));\n    }\n    register(data) {\n      const module = 'register';\n      const method = this.getOption(`${module}.method`);\n      const url = this.getActionEndpoint(module);\n      const requireValidToken = this.getOption(`${module}.requireValidToken`);\n      return this.http.request(method, url, {\n        body: data,\n        observe: 'response',\n        headers: this.getHeaders()\n      }).pipe(map(res => {\n        if (this.getOption(`${module}.alwaysFail`)) {\n          throw this.createFailResponse(data);\n        }\n        return res;\n      }), map(res => {\n        return new NbAuthResult(true, res, this.getOption(`${module}.redirect.success`), [], this.getOption('messages.getter')(module, res, this.options), this.createToken(this.getOption('token.getter')('login', res, this.options), requireValidToken));\n      }), catchError(res => {\n        return this.handleResponseError(res, module);\n      }));\n    }\n    requestPassword(data) {\n      const module = 'requestPass';\n      const method = this.getOption(`${module}.method`);\n      const url = this.getActionEndpoint(module);\n      return this.http.request(method, url, {\n        body: data,\n        observe: 'response',\n        headers: this.getHeaders()\n      }).pipe(map(res => {\n        if (this.getOption(`${module}.alwaysFail`)) {\n          throw this.createFailResponse();\n        }\n        return res;\n      }), map(res => {\n        return new NbAuthResult(true, res, this.getOption(`${module}.redirect.success`), [], this.getOption('messages.getter')(module, res, this.options));\n      }), catchError(res => {\n        return this.handleResponseError(res, module);\n      }));\n    }\n    resetPassword(data = {}) {\n      const module = 'resetPass';\n      const method = this.getOption(`${module}.method`);\n      const url = this.getActionEndpoint(module);\n      const tokenKey = this.getOption(`${module}.resetPasswordTokenKey`);\n      data[tokenKey] = this.route.snapshot.queryParams[tokenKey];\n      return this.http.request(method, url, {\n        body: data,\n        observe: 'response',\n        headers: this.getHeaders()\n      }).pipe(map(res => {\n        if (this.getOption(`${module}.alwaysFail`)) {\n          throw this.createFailResponse();\n        }\n        return res;\n      }), map(res => {\n        return new NbAuthResult(true, res, this.getOption(`${module}.redirect.success`), [], this.getOption('messages.getter')(module, res, this.options));\n      }), catchError(res => {\n        return this.handleResponseError(res, module);\n      }));\n    }\n    logout() {\n      const module = 'logout';\n      const method = this.getOption(`${module}.method`);\n      const url = this.getActionEndpoint(module);\n      return of({}).pipe(switchMap(res => {\n        if (!url) {\n          return of(res);\n        }\n        return this.http.request(method, url, {\n          observe: 'response',\n          headers: this.getHeaders()\n        });\n      }), map(res => {\n        if (this.getOption(`${module}.alwaysFail`)) {\n          throw this.createFailResponse();\n        }\n        return res;\n      }), map(res => {\n        return new NbAuthResult(true, res, this.getOption(`${module}.redirect.success`), [], this.getOption('messages.getter')(module, res, this.options));\n      }), catchError(res => {\n        return this.handleResponseError(res, module);\n      }));\n    }\n    refreshToken(data) {\n      const module = 'refreshToken';\n      const method = this.getOption(`${module}.method`);\n      const url = this.getActionEndpoint(module);\n      const requireValidToken = this.getOption(`${module}.requireValidToken`);\n      return this.http.request(method, url, {\n        body: data,\n        observe: 'response',\n        headers: this.getHeaders()\n      }).pipe(map(res => {\n        if (this.getOption(`${module}.alwaysFail`)) {\n          throw this.createFailResponse(data);\n        }\n        return res;\n      }), map(res => {\n        return new NbAuthResult(true, res, this.getOption(`${module}.redirect.success`), [], this.getOption('messages.getter')(module, res, this.options), this.createToken(this.getOption('token.getter')(module, res, this.options), requireValidToken));\n      }), catchError(res => {\n        return this.handleResponseError(res, module);\n      }));\n    }\n    handleResponseError(res, module) {\n      let errors = [];\n      if (res instanceof HttpErrorResponse) {\n        errors = this.getOption('errors.getter')(module, res, this.options);\n      } else if (res instanceof NbAuthIllegalTokenError) {\n        errors.push(res.message);\n      } else {\n        errors.push('Something went wrong.');\n      }\n      return of(new NbAuthResult(false, res, this.getOption(`${module}.redirect.failure`), errors));\n    }\n    static {\n      this.ɵfac = function NbPasswordAuthStrategy_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || NbPasswordAuthStrategy)(i0.ɵɵinject(i1.HttpClient), i0.ɵɵinject(i2.ActivatedRoute));\n      };\n    }\n    static {\n      this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n        token: NbPasswordAuthStrategy,\n        factory: NbPasswordAuthStrategy.ɵfac\n      });\n    }\n  }\n  return NbPasswordAuthStrategy;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/**\n * @license\n * Copyright Akveo. All Rights Reserved.\n * Licensed under the MIT License. See License.txt in the project root for license information.\n */\nlet NbAuthBlockComponent = /*#__PURE__*/(() => {\n  class NbAuthBlockComponent {\n    static {\n      this.ɵfac = function NbAuthBlockComponent_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || NbAuthBlockComponent)();\n      };\n    }\n    static {\n      this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n        type: NbAuthBlockComponent,\n        selectors: [[\"nb-auth-block\"]],\n        ngContentSelectors: _c0,\n        decls: 1,\n        vars: 0,\n        template: function NbAuthBlockComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵprojectionDef();\n            i0.ɵɵprojection(0);\n          }\n        },\n        styles: [\"[_nghost-%COMP%]{display:block;width:100%;max-width:35rem}[_nghost-%COMP%]     form{width:100%}[_nghost-%COMP%]     .label{display:block;margin-bottom:.5rem}[_nghost-%COMP%]     .forgot-password{text-decoration:none;margin-bottom:.5rem}[_nghost-%COMP%]     .caption{margin-top:.5rem}[_nghost-%COMP%]     .alert{text-align:center}[_nghost-%COMP%]     .title{margin-top:0;margin-bottom:.75rem;text-align:center}[_nghost-%COMP%]     .sub-title{margin-bottom:2rem;text-align:center}[_nghost-%COMP%]     .form-control-group{margin-bottom:2rem}[_nghost-%COMP%]     .form-control-group.accept-group{display:flex;justify-content:space-between;margin:2rem 0}[_nghost-%COMP%]     .label-with-link{display:flex;justify-content:space-between}[_nghost-%COMP%]     .links{text-align:center;margin-top:1.75rem}[_nghost-%COMP%]     .links .socials{margin-top:1.5rem}[_nghost-%COMP%]     .links .socials a{margin:0 1rem;text-decoration:none;vertical-align:middle}[_nghost-%COMP%]     .links .socials a.with-icon{font-size:2rem}[_nghost-%COMP%]     .another-action{margin-top:2rem;text-align:center}[_nghost-%COMP%]     .sign-in-or-up{margin-top:2rem;display:flex;justify-content:space-between}[_nghost-%COMP%]     nb-alert .alert-title, [_nghost-%COMP%]     nb-alert .alert-message{margin:0 0 .5rem}[_nghost-%COMP%]     nb-alert .alert-message-list{list-style-type:none;padding:0;margin:0}\\n\\n\\n\\n\\n\\n\"]\n      });\n    }\n  }\n  return NbAuthBlockComponent;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/**\n * @license\n * Copyright Akveo. All Rights Reserved.\n * Licensed under the MIT License. See License.txt in the project root for license information.\n */\nlet NbAuthComponent = /*#__PURE__*/(() => {\n  class NbAuthComponent {\n    // showcase of how to use the onAuthenticationChange method\n    constructor(auth, location) {\n      this.auth = auth;\n      this.location = location;\n      this.destroy$ = new Subject();\n      this.authenticated = false;\n      this.token = '';\n      this.subscription = auth.onAuthenticationChange().pipe(takeUntil(this.destroy$)).subscribe(authenticated => {\n        this.authenticated = authenticated;\n      });\n    }\n    back() {\n      this.location.back();\n      return false;\n    }\n    ngOnDestroy() {\n      this.destroy$.next();\n      this.destroy$.complete();\n    }\n    static {\n      this.ɵfac = function NbAuthComponent_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || NbAuthComponent)(i0.ɵɵdirectiveInject(NbAuthService), i0.ɵɵdirectiveInject(i3.Location));\n      };\n    }\n    static {\n      this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n        type: NbAuthComponent,\n        selectors: [[\"nb-auth\"]],\n        decls: 10,\n        vars: 0,\n        consts: [[1, \"navigation\"], [\"href\", \"#\", \"aria-label\", \"Back\", 1, \"link\", \"back-link\", 3, \"click\"], [\"icon\", \"arrow-back\"]],\n        template: function NbAuthComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"nb-layout\")(1, \"nb-layout-column\")(2, \"nb-card\")(3, \"nb-card-header\")(4, \"nav\", 0)(5, \"a\", 1);\n            i0.ɵɵlistener(\"click\", function NbAuthComponent_Template_a_click_5_listener() {\n              return ctx.back();\n            });\n            i0.ɵɵelement(6, \"nb-icon\", 2);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(7, \"nb-card-body\")(8, \"nb-auth-block\");\n            i0.ɵɵelement(9, \"router-outlet\");\n            i0.ɵɵelementEnd()()()()();\n          }\n        },\n        dependencies: [i4.NbLayoutComponent, i4.NbLayoutColumnComponent, i4.NbCardComponent, i4.NbCardBodyComponent, i4.NbCardHeaderComponent, i2.RouterOutlet, i4.NbIconComponent, NbAuthBlockComponent],\n        styles: [\".visually-hidden[_ngcontent-%COMP%]{position:absolute!important;height:1px;width:1px;overflow:hidden;clip:rect(1px 1px 1px 1px);clip:rect(1px,1px,1px,1px)}.cdk-overlay-container[_ngcontent-%COMP%], .cdk-global-overlay-wrapper[_ngcontent-%COMP%]{pointer-events:none;top:0;left:0;height:100%;width:100%}.cdk-overlay-container[_ngcontent-%COMP%]{position:fixed;z-index:1000}.cdk-overlay-container[_ngcontent-%COMP%]:empty{display:none}.cdk-global-overlay-wrapper[_ngcontent-%COMP%]{display:flex;position:absolute;z-index:1000}.cdk-overlay-pane[_ngcontent-%COMP%]{position:absolute;pointer-events:auto;box-sizing:border-box;z-index:1000;display:flex;max-width:100%;max-height:100%}.cdk-overlay-backdrop[_ngcontent-%COMP%]{position:absolute;inset:0;z-index:1000;pointer-events:auto;-webkit-tap-highlight-color:rgba(0,0,0,0);transition:opacity .4s cubic-bezier(.25,.8,.25,1);opacity:0}.cdk-overlay-backdrop.cdk-overlay-backdrop-showing[_ngcontent-%COMP%]{opacity:1}.cdk-high-contrast-active[_ngcontent-%COMP%]   .cdk-overlay-backdrop.cdk-overlay-backdrop-showing[_ngcontent-%COMP%]{opacity:.6}.cdk-overlay-dark-backdrop[_ngcontent-%COMP%]{background:#00000052}.cdk-overlay-transparent-backdrop[_ngcontent-%COMP%]{transition:visibility 1ms linear,opacity 1ms linear;visibility:hidden;opacity:1}.cdk-overlay-transparent-backdrop.cdk-overlay-backdrop-showing[_ngcontent-%COMP%]{opacity:0;visibility:visible}.cdk-overlay-backdrop-noop-animation[_ngcontent-%COMP%]{transition:none}.cdk-overlay-connected-position-bounding-box[_ngcontent-%COMP%]{position:absolute;z-index:1000;display:flex;flex-direction:column;min-width:1px;min-height:1px}.cdk-global-scrollblock[_ngcontent-%COMP%]{position:fixed;width:100%;overflow-y:scroll}.nb-global-scrollblock[_ngcontent-%COMP%]{position:static;width:auto;overflow:hidden}html[_ngcontent-%COMP%]{box-sizing:border-box}*[_ngcontent-%COMP%], *[_ngcontent-%COMP%]:before, *[_ngcontent-%COMP%]:after{box-sizing:inherit}html[_ngcontent-%COMP%], body[_ngcontent-%COMP%]{margin:0;padding:0}html[_ngcontent-%COMP%]{line-height:1.15;-webkit-text-size-adjust:100%}body[_ngcontent-%COMP%]{margin:0}h1[_ngcontent-%COMP%]{font-size:2em;margin:.67em 0}hr[_ngcontent-%COMP%]{box-sizing:content-box;height:0;overflow:visible}pre[_ngcontent-%COMP%]{font-family:monospace,monospace;font-size:1em}a[_ngcontent-%COMP%]{background-color:transparent}abbr[title][_ngcontent-%COMP%]{border-bottom:none;text-decoration:underline;text-decoration:underline dotted}b[_ngcontent-%COMP%], strong[_ngcontent-%COMP%]{font-weight:bolder}code[_ngcontent-%COMP%], kbd[_ngcontent-%COMP%], samp[_ngcontent-%COMP%]{font-family:monospace,monospace;font-size:1em}small[_ngcontent-%COMP%]{font-size:80%}sub[_ngcontent-%COMP%], sup[_ngcontent-%COMP%]{font-size:75%;line-height:0;position:relative;vertical-align:baseline}sub[_ngcontent-%COMP%]{bottom:-.25em}sup[_ngcontent-%COMP%]{top:-.5em}img[_ngcontent-%COMP%]{border-style:none}button[_ngcontent-%COMP%], input[_ngcontent-%COMP%], optgroup[_ngcontent-%COMP%], select[_ngcontent-%COMP%], textarea[_ngcontent-%COMP%]{font-family:inherit;font-size:100%;line-height:1.15;margin:0}button[_ngcontent-%COMP%], input[_ngcontent-%COMP%]{overflow:visible}button[_ngcontent-%COMP%], select[_ngcontent-%COMP%]{text-transform:none}button[_ngcontent-%COMP%], [type=button][_ngcontent-%COMP%], [type=reset][_ngcontent-%COMP%], [type=submit][_ngcontent-%COMP%]{-webkit-appearance:button}button[_ngcontent-%COMP%]::-moz-focus-inner, [type=button][_ngcontent-%COMP%]::-moz-focus-inner, [type=reset][_ngcontent-%COMP%]::-moz-focus-inner, [type=submit][_ngcontent-%COMP%]::-moz-focus-inner{border-style:none;padding:0}button[_ngcontent-%COMP%]:-moz-focusring, [type=button][_ngcontent-%COMP%]:-moz-focusring, [type=reset][_ngcontent-%COMP%]:-moz-focusring, [type=submit][_ngcontent-%COMP%]:-moz-focusring{outline:1px dotted ButtonText}fieldset[_ngcontent-%COMP%]{padding:.35em .75em .625em}legend[_ngcontent-%COMP%]{box-sizing:border-box;color:inherit;display:table;max-width:100%;padding:0;white-space:normal}progress[_ngcontent-%COMP%]{vertical-align:baseline}textarea[_ngcontent-%COMP%]{overflow:auto}[type=checkbox][_ngcontent-%COMP%], [type=radio][_ngcontent-%COMP%]{box-sizing:border-box;padding:0}[type=number][_ngcontent-%COMP%]::-webkit-inner-spin-button, [type=number][_ngcontent-%COMP%]::-webkit-outer-spin-button{height:auto}[type=search][_ngcontent-%COMP%]{-webkit-appearance:textfield;outline-offset:-2px}[type=search][_ngcontent-%COMP%]::-webkit-search-decoration{-webkit-appearance:none}[_ngcontent-%COMP%]::-webkit-file-upload-button{-webkit-appearance:button;font:inherit}details[_ngcontent-%COMP%]{display:block}summary[_ngcontent-%COMP%]{display:list-item}template[_ngcontent-%COMP%]{display:none}[hidden][_ngcontent-%COMP%]{display:none}[_nghost-%COMP%]   nb-card[_ngcontent-%COMP%]{margin:0;height:calc(100vh - 5rem)}[_nghost-%COMP%]   .navigation[_ngcontent-%COMP%]   .link[_ngcontent-%COMP%]{display:inline-block;text-decoration:none}[_nghost-%COMP%]   .navigation[_ngcontent-%COMP%]   .link[_ngcontent-%COMP%]   nb-icon[_ngcontent-%COMP%]{font-size:2rem;vertical-align:middle}[_nghost-%COMP%]   .links[_ngcontent-%COMP%]   nb-icon[_ngcontent-%COMP%]{font-size:2.5rem}[_nghost-%COMP%]   nb-card-body[_ngcontent-%COMP%]{display:flex;width:100%}[_nghost-%COMP%]   nb-auth-block[_ngcontent-%COMP%]{margin:auto}@media (max-width: 767.98px){[_nghost-%COMP%]   nb-card[_ngcontent-%COMP%]{border-radius:0;height:100vh}}[_nghost-%COMP%]     nb-layout .layout .layout-container .content .columns nb-layout-column{padding:2.5rem}@media (max-width: 767.98px){[_nghost-%COMP%]     nb-layout .layout .layout-container .content .columns nb-layout-column{padding:0}}\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\"]\n      });\n    }\n  }\n  return NbAuthComponent;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/**\n * @license\n * Copyright Akveo. All Rights Reserved.\n * Licensed under the MIT License. See License.txt in the project root for license information.\n */\nlet NbLoginComponent = /*#__PURE__*/(() => {\n  class NbLoginComponent {\n    constructor(service, options = {}, cd, router) {\n      this.service = service;\n      this.options = options;\n      this.cd = cd;\n      this.router = router;\n      this.redirectDelay = 0;\n      this.showMessages = {};\n      this.strategy = '';\n      this.errors = [];\n      this.messages = [];\n      this.user = {};\n      this.submitted = false;\n      this.socialLinks = [];\n      this.rememberMe = false;\n      this.redirectDelay = this.getConfigValue('forms.login.redirectDelay');\n      this.showMessages = this.getConfigValue('forms.login.showMessages');\n      this.strategy = this.getConfigValue('forms.login.strategy');\n      this.socialLinks = this.getConfigValue('forms.login.socialLinks');\n      this.rememberMe = this.getConfigValue('forms.login.rememberMe');\n    }\n    login() {\n      this.errors = [];\n      this.messages = [];\n      this.submitted = true;\n      this.service.authenticate(this.strategy, this.user).subscribe(result => {\n        this.submitted = false;\n        if (result.isSuccess()) {\n          this.messages = result.getMessages();\n        } else {\n          this.errors = result.getErrors();\n        }\n        const redirect = result.getRedirect();\n        if (redirect) {\n          setTimeout(() => {\n            return this.router.navigateByUrl(redirect);\n          }, this.redirectDelay);\n        }\n        this.cd.detectChanges();\n      });\n    }\n    getConfigValue(key) {\n      return getDeepFromObject(this.options, key, null);\n    }\n    static {\n      this.ɵfac = function NbLoginComponent_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || NbLoginComponent)(i0.ɵɵdirectiveInject(NbAuthService), i0.ɵɵdirectiveInject(NB_AUTH_OPTIONS), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i2.Router));\n      };\n    }\n    static {\n      this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n        type: NbLoginComponent,\n        selectors: [[\"nb-login\"]],\n        decls: 32,\n        vars: 19,\n        consts: [[\"form\", \"ngForm\"], [\"email\", \"ngModel\"], [\"password\", \"ngModel\"], [\"title\", \"\"], [\"id\", \"title\", 1, \"title\"], [1, \"sub-title\"], [\"outline\", \"danger\", \"role\", \"alert\", 4, \"ngIf\"], [\"outline\", \"success\", \"role\", \"alert\", 4, \"ngIf\"], [\"aria-labelledby\", \"title\", 3, \"ngSubmit\"], [1, \"form-control-group\"], [\"for\", \"input-email\", 1, \"label\"], [\"nbInput\", \"\", \"fullWidth\", \"\", \"name\", \"email\", \"id\", \"input-email\", \"pattern\", \".+@.+\\\\..+\", \"placeholder\", \"Email address\", \"fieldSize\", \"large\", \"autofocus\", \"\", 3, \"ngModelChange\", \"ngModel\", \"status\", \"required\"], [4, \"ngIf\"], [1, \"label-with-link\"], [\"for\", \"input-password\", 1, \"label\"], [\"routerLink\", \"../request-password\", 1, \"forgot-password\", \"caption-2\"], [\"nbInput\", \"\", \"fullWidth\", \"\", \"name\", \"password\", \"type\", \"password\", \"id\", \"input-password\", \"placeholder\", \"Password\", \"fieldSize\", \"large\", 3, \"ngModelChange\", \"ngModel\", \"status\", \"required\", \"minlength\", \"maxlength\"], [1, \"form-control-group\", \"accept-group\"], [\"name\", \"rememberMe\", 3, \"ngModel\", \"ngModelChange\", 4, \"ngIf\"], [\"nbButton\", \"\", \"fullWidth\", \"\", \"status\", \"primary\", \"size\", \"large\", 3, \"disabled\"], [\"class\", \"links\", \"aria-label\", \"Social sign in\", 4, \"ngIf\"], [\"aria-label\", \"Register\", 1, \"another-action\"], [\"routerLink\", \"../register\", 1, \"text-link\"], [\"outline\", \"danger\", \"role\", \"alert\"], [1, \"alert-title\"], [1, \"alert-message-list\"], [\"class\", \"alert-message\", 4, \"ngFor\", \"ngForOf\"], [1, \"alert-message\"], [\"outline\", \"success\", \"role\", \"alert\"], [\"class\", \"caption status-danger\", 4, \"ngIf\"], [1, \"caption\", \"status-danger\"], [\"name\", \"rememberMe\", 3, \"ngModelChange\", \"ngModel\"], [\"aria-label\", \"Social sign in\", 1, \"links\"], [1, \"socials\"], [4, \"ngFor\", \"ngForOf\"], [3, \"routerLink\", \"with-icon\", 4, \"ngIf\"], [3, \"with-icon\", 4, \"ngIf\"], [3, \"routerLink\"], [3, \"icon\", 4, \"ngIf\", \"ngIfElse\"], [3, \"icon\"]],\n        template: function NbLoginComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            const _r1 = i0.ɵɵgetCurrentView();\n            i0.ɵɵelementStart(0, \"h1\", 4);\n            i0.ɵɵtext(1, \"Login\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(2, \"p\", 5);\n            i0.ɵɵtext(3, \"Hello! Log in with your email.\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(4, NbLoginComponent_nb_alert_4_Template, 6, 1, \"nb-alert\", 6)(5, NbLoginComponent_nb_alert_5_Template, 6, 1, \"nb-alert\", 7);\n            i0.ɵɵelementStart(6, \"form\", 8, 0);\n            i0.ɵɵlistener(\"ngSubmit\", function NbLoginComponent_Template_form_ngSubmit_6_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.login());\n            });\n            i0.ɵɵelementStart(8, \"div\", 9)(9, \"label\", 10);\n            i0.ɵɵtext(10, \"Email address:\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(11, \"input\", 11, 1);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function NbLoginComponent_Template_input_ngModelChange_11_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.user.email, $event) || (ctx.user.email = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(13, NbLoginComponent_ng_container_13_Template, 3, 2, \"ng-container\", 12);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(14, \"div\", 9)(15, \"span\", 13)(16, \"label\", 14);\n            i0.ɵɵtext(17, \"Password:\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(18, \"a\", 15);\n            i0.ɵɵtext(19, \"Forgot Password?\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(20, \"input\", 16, 2);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function NbLoginComponent_Template_input_ngModelChange_20_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.user.password, $event) || (ctx.user.password = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(22, NbLoginComponent_ng_container_22_Template, 3, 2, \"ng-container\", 12);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(23, \"div\", 17);\n            i0.ɵɵtemplate(24, NbLoginComponent_nb_checkbox_24_Template, 2, 1, \"nb-checkbox\", 18);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(25, \"button\", 19);\n            i0.ɵɵtext(26, \" Log In \");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵtemplate(27, NbLoginComponent_section_27_Template, 4, 1, \"section\", 20);\n            i0.ɵɵelementStart(28, \"section\", 21);\n            i0.ɵɵtext(29, \" Don't have an account? \");\n            i0.ɵɵelementStart(30, \"a\", 22);\n            i0.ɵɵtext(31, \"Register\");\n            i0.ɵɵelementEnd()();\n          }\n          if (rf & 2) {\n            const form_r11 = i0.ɵɵreference(7);\n            const email_r5 = i0.ɵɵreference(12);\n            const password_r6 = i0.ɵɵreference(21);\n            i0.ɵɵadvance(4);\n            i0.ɵɵproperty(\"ngIf\", ctx.showMessages.error && (ctx.errors == null ? null : ctx.errors.length) && !ctx.submitted);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.showMessages.success && (ctx.messages == null ? null : ctx.messages.length) && !ctx.submitted);\n            i0.ɵɵadvance(6);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.user.email);\n            i0.ɵɵproperty(\"status\", email_r5.dirty ? email_r5.invalid ? \"danger\" : \"success\" : \"basic\")(\"required\", ctx.getConfigValue(\"forms.validation.email.required\"));\n            i0.ɵɵattribute(\"aria-invalid\", email_r5.invalid && email_r5.touched ? true : null);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"ngIf\", email_r5.invalid && email_r5.touched);\n            i0.ɵɵadvance(7);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.user.password);\n            i0.ɵɵproperty(\"status\", password_r6.dirty ? password_r6.invalid ? \"danger\" : \"success\" : \"basic\")(\"required\", ctx.getConfigValue(\"forms.validation.password.required\"))(\"minlength\", ctx.getConfigValue(\"forms.validation.password.minLength\"))(\"maxlength\", ctx.getConfigValue(\"forms.validation.password.maxLength\"));\n            i0.ɵɵattribute(\"aria-invalid\", password_r6.invalid && password_r6.touched ? true : null);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"ngIf\", password_r6.invalid && password_r6.touched);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"ngIf\", ctx.rememberMe);\n            i0.ɵɵadvance();\n            i0.ɵɵclassProp(\"btn-pulse\", ctx.submitted);\n            i0.ɵɵproperty(\"disabled\", ctx.submitted || !form_r11.valid);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"ngIf\", ctx.socialLinks && ctx.socialLinks.length > 0);\n          }\n        },\n        dependencies: [i3.NgForOf, i3.NgIf, i4.NbCheckboxComponent, i4.NbAlertComponent, i4.NbInputDirective, i4.NbButtonComponent, i2.RouterLink, i5.ɵNgNoValidate, i5.DefaultValueAccessor, i5.NgControlStatus, i5.NgControlStatusGroup, i5.RequiredValidator, i5.MinLengthValidator, i5.MaxLengthValidator, i5.PatternValidator, i5.NgModel, i5.NgForm, i4.NbIconComponent],\n        encapsulation: 2,\n        changeDetection: 0\n      });\n    }\n  }\n  return NbLoginComponent;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/**\n * @license\n * Copyright Akveo. All Rights Reserved.\n * Licensed under the MIT License. See License.txt in the project root for license information.\n */\nlet NbRegisterComponent = /*#__PURE__*/(() => {\n  class NbRegisterComponent {\n    constructor(service, options = {}, cd, router) {\n      this.service = service;\n      this.options = options;\n      this.cd = cd;\n      this.router = router;\n      this.redirectDelay = 0;\n      this.showMessages = {};\n      this.strategy = '';\n      this.submitted = false;\n      this.errors = [];\n      this.messages = [];\n      this.user = {};\n      this.socialLinks = [];\n      this.redirectDelay = this.getConfigValue('forms.register.redirectDelay');\n      this.showMessages = this.getConfigValue('forms.register.showMessages');\n      this.strategy = this.getConfigValue('forms.register.strategy');\n      this.socialLinks = this.getConfigValue('forms.login.socialLinks');\n    }\n    register() {\n      this.errors = this.messages = [];\n      this.submitted = true;\n      this.service.register(this.strategy, this.user).subscribe(result => {\n        this.submitted = false;\n        if (result.isSuccess()) {\n          this.messages = result.getMessages();\n        } else {\n          this.errors = result.getErrors();\n        }\n        const redirect = result.getRedirect();\n        if (redirect) {\n          setTimeout(() => {\n            return this.router.navigateByUrl(redirect);\n          }, this.redirectDelay);\n        }\n        this.cd.detectChanges();\n      });\n    }\n    getConfigValue(key) {\n      return getDeepFromObject(this.options, key, null);\n    }\n    static {\n      this.ɵfac = function NbRegisterComponent_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || NbRegisterComponent)(i0.ɵɵdirectiveInject(NbAuthService), i0.ɵɵdirectiveInject(NB_AUTH_OPTIONS), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i2.Router));\n      };\n    }\n    static {\n      this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n        type: NbRegisterComponent,\n        selectors: [[\"nb-register\"]],\n        decls: 38,\n        vars: 31,\n        consts: [[\"form\", \"ngForm\"], [\"fullName\", \"ngModel\"], [\"email\", \"ngModel\"], [\"password\", \"ngModel\"], [\"rePass\", \"ngModel\"], [\"title\", \"\"], [\"id\", \"title\", 1, \"title\"], [\"outline\", \"danger\", \"role\", \"alert\", 4, \"ngIf\"], [\"outline\", \"success\", \"role\", \"alert\", 4, \"ngIf\"], [\"aria-labelledby\", \"title\", 3, \"ngSubmit\"], [1, \"form-control-group\"], [\"for\", \"input-name\", 1, \"label\"], [\"nbInput\", \"\", \"id\", \"input-name\", \"name\", \"fullName\", \"placeholder\", \"Full name\", \"autofocus\", \"\", \"fullWidth\", \"\", \"fieldSize\", \"large\", 3, \"ngModelChange\", \"ngModel\", \"status\", \"required\", \"minlength\", \"maxlength\"], [4, \"ngIf\"], [\"for\", \"input-email\", 1, \"label\"], [\"nbInput\", \"\", \"id\", \"input-email\", \"name\", \"email\", \"pattern\", \".+@.+..+\", \"placeholder\", \"Email address\", \"fullWidth\", \"\", \"fieldSize\", \"large\", 3, \"ngModelChange\", \"ngModel\", \"status\", \"required\"], [\"for\", \"input-password\", 1, \"label\"], [\"nbInput\", \"\", \"type\", \"password\", \"id\", \"input-password\", \"name\", \"password\", \"placeholder\", \"Password\", \"fullWidth\", \"\", \"fieldSize\", \"large\", 3, \"ngModelChange\", \"ngModel\", \"status\", \"required\", \"minlength\", \"maxlength\"], [\"for\", \"input-re-password\", 1, \"label\"], [\"nbInput\", \"\", \"type\", \"password\", \"id\", \"input-re-password\", \"name\", \"rePass\", \"placeholder\", \"Confirm Password\", \"fullWidth\", \"\", \"fieldSize\", \"large\", 3, \"ngModelChange\", \"ngModel\", \"status\", \"required\"], [\"class\", \"form-control-group accept-group\", 4, \"ngIf\"], [\"nbButton\", \"\", \"fullWidth\", \"\", \"status\", \"primary\", \"size\", \"large\", 3, \"disabled\"], [\"class\", \"links\", \"aria-label\", \"Social sign in\", 4, \"ngIf\"], [\"aria-label\", \"Sign in\", 1, \"another-action\"], [\"routerLink\", \"../login\", 1, \"text-link\"], [\"outline\", \"danger\", \"role\", \"alert\"], [1, \"alert-title\"], [1, \"alert-message-list\"], [\"class\", \"alert-message\", 4, \"ngFor\", \"ngForOf\"], [1, \"alert-message\"], [\"outline\", \"success\", \"role\", \"alert\"], [\"class\", \"caption status-danger\", 4, \"ngIf\"], [1, \"caption\", \"status-danger\"], [1, \"form-control-group\", \"accept-group\"], [\"name\", \"terms\", 3, \"ngModelChange\", \"ngModel\", \"required\"], [\"href\", \"#\", \"target\", \"_blank\"], [\"aria-label\", \"Social sign in\", 1, \"links\"], [1, \"socials\"], [4, \"ngFor\", \"ngForOf\"], [3, \"routerLink\", \"with-icon\", 4, \"ngIf\"], [3, \"with-icon\", 4, \"ngIf\"], [3, \"routerLink\"], [3, \"icon\", 4, \"ngIf\", \"ngIfElse\"], [3, \"icon\"]],\n        template: function NbRegisterComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            const _r1 = i0.ɵɵgetCurrentView();\n            i0.ɵɵelementStart(0, \"h1\", 6);\n            i0.ɵɵtext(1, \"Register\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(2, NbRegisterComponent_nb_alert_2_Template, 6, 1, \"nb-alert\", 7)(3, NbRegisterComponent_nb_alert_3_Template, 6, 1, \"nb-alert\", 8);\n            i0.ɵɵelementStart(4, \"form\", 9, 0);\n            i0.ɵɵlistener(\"ngSubmit\", function NbRegisterComponent_Template_form_ngSubmit_4_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.register());\n            });\n            i0.ɵɵelementStart(6, \"div\", 10)(7, \"label\", 11);\n            i0.ɵɵtext(8, \"Full name:\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(9, \"input\", 12, 1);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function NbRegisterComponent_Template_input_ngModelChange_9_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.user.fullName, $event) || (ctx.user.fullName = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(11, NbRegisterComponent_ng_container_11_Template, 3, 2, \"ng-container\", 13);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(12, \"div\", 10)(13, \"label\", 14);\n            i0.ɵɵtext(14, \"Email address:\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(15, \"input\", 15, 2);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function NbRegisterComponent_Template_input_ngModelChange_15_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.user.email, $event) || (ctx.user.email = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(17, NbRegisterComponent_ng_container_17_Template, 3, 2, \"ng-container\", 13);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(18, \"div\", 10)(19, \"label\", 16);\n            i0.ɵɵtext(20, \"Password:\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(21, \"input\", 17, 3);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function NbRegisterComponent_Template_input_ngModelChange_21_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.user.password, $event) || (ctx.user.password = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(23, NbRegisterComponent_ng_container_23_Template, 3, 2, \"ng-container\", 13);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(24, \"div\", 10)(25, \"label\", 18);\n            i0.ɵɵtext(26, \"Repeat password:\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(27, \"input\", 19, 4);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function NbRegisterComponent_Template_input_ngModelChange_27_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.user.confirmPassword, $event) || (ctx.user.confirmPassword = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(29, NbRegisterComponent_ng_container_29_Template, 3, 2, \"ng-container\", 13);\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(30, NbRegisterComponent_div_30_Template, 6, 2, \"div\", 20);\n            i0.ɵɵelementStart(31, \"button\", 21);\n            i0.ɵɵtext(32, \" Register \");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵtemplate(33, NbRegisterComponent_section_33_Template, 4, 1, \"section\", 22);\n            i0.ɵɵelementStart(34, \"section\", 23);\n            i0.ɵɵtext(35, \" Already have an account? \");\n            i0.ɵɵelementStart(36, \"a\", 24);\n            i0.ɵɵtext(37, \"Log in\");\n            i0.ɵɵelementEnd()();\n          }\n          if (rf & 2) {\n            const form_r13 = i0.ɵɵreference(5);\n            const fullName_r5 = i0.ɵɵreference(10);\n            const email_r6 = i0.ɵɵreference(16);\n            const password_r7 = i0.ɵɵreference(22);\n            const rePass_r8 = i0.ɵɵreference(28);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"ngIf\", ctx.showMessages.error && (ctx.errors == null ? null : ctx.errors.length) && !ctx.submitted);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.showMessages.success && (ctx.messages == null ? null : ctx.messages.length) && !ctx.submitted);\n            i0.ɵɵadvance(6);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.user.fullName);\n            i0.ɵɵproperty(\"status\", fullName_r5.dirty ? fullName_r5.invalid ? \"danger\" : \"success\" : \"basic\")(\"required\", ctx.getConfigValue(\"forms.validation.fullName.required\"))(\"minlength\", ctx.getConfigValue(\"forms.validation.fullName.minLength\"))(\"maxlength\", ctx.getConfigValue(\"forms.validation.fullName.maxLength\"));\n            i0.ɵɵattribute(\"aria-invalid\", fullName_r5.invalid && fullName_r5.touched ? true : null);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"ngIf\", fullName_r5.invalid && fullName_r5.touched);\n            i0.ɵɵadvance(4);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.user.email);\n            i0.ɵɵproperty(\"status\", email_r6.dirty ? email_r6.invalid ? \"danger\" : \"success\" : \"basic\")(\"required\", ctx.getConfigValue(\"forms.validation.email.required\"));\n            i0.ɵɵattribute(\"aria-invalid\", email_r6.invalid && email_r6.touched ? true : null);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"ngIf\", email_r6.invalid && email_r6.touched);\n            i0.ɵɵadvance(4);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.user.password);\n            i0.ɵɵproperty(\"status\", password_r7.dirty ? password_r7.invalid ? \"danger\" : \"success\" : \"basic\")(\"required\", ctx.getConfigValue(\"forms.validation.password.required\"))(\"minlength\", ctx.getConfigValue(\"forms.validation.password.minLength\"))(\"maxlength\", ctx.getConfigValue(\"forms.validation.password.maxLength\"));\n            i0.ɵɵattribute(\"aria-invalid\", password_r7.invalid && password_r7.touched ? true : null);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"ngIf\", password_r7.invalid && password_r7.touched);\n            i0.ɵɵadvance(4);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.user.confirmPassword);\n            i0.ɵɵproperty(\"status\", rePass_r8.dirty ? rePass_r8.invalid || password_r7.value != rePass_r8.value ? \"danger\" : \"success\" : \"basic\")(\"required\", ctx.getConfigValue(\"forms.validation.password.required\"));\n            i0.ɵɵattribute(\"aria-invalid\", rePass_r8.invalid && rePass_r8.touched ? true : null);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"ngIf\", rePass_r8.invalid && rePass_r8.touched);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.getConfigValue(\"forms.register.terms\"));\n            i0.ɵɵadvance();\n            i0.ɵɵclassProp(\"btn-pulse\", ctx.submitted);\n            i0.ɵɵproperty(\"disabled\", ctx.submitted || !form_r13.valid);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"ngIf\", ctx.socialLinks && ctx.socialLinks.length > 0);\n          }\n        },\n        dependencies: [i3.NgForOf, i3.NgIf, i4.NbCheckboxComponent, i4.NbAlertComponent, i4.NbInputDirective, i4.NbButtonComponent, i2.RouterLink, i5.ɵNgNoValidate, i5.DefaultValueAccessor, i5.NgControlStatus, i5.NgControlStatusGroup, i5.RequiredValidator, i5.MinLengthValidator, i5.MaxLengthValidator, i5.PatternValidator, i5.NgModel, i5.NgForm, i4.NbIconComponent],\n        styles: [\"[_nghost-%COMP%]   .title[_ngcontent-%COMP%]{margin-bottom:2rem}\\n\\n\\n\\n\\n\\n\"],\n        changeDetection: 0\n      });\n    }\n  }\n  return NbRegisterComponent;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/**\n * @license\n * Copyright Akveo. All Rights Reserved.\n * Licensed under the MIT License. See License.txt in the project root for license information.\n */\nlet NbLogoutComponent = /*#__PURE__*/(() => {\n  class NbLogoutComponent {\n    constructor(service, options = {}, router) {\n      this.service = service;\n      this.options = options;\n      this.router = router;\n      this.redirectDelay = 0;\n      this.strategy = '';\n      this.redirectDelay = this.getConfigValue('forms.logout.redirectDelay');\n      this.strategy = this.getConfigValue('forms.logout.strategy');\n    }\n    ngOnInit() {\n      this.logout(this.strategy);\n    }\n    logout(strategy) {\n      this.service.logout(strategy).subscribe(result => {\n        const redirect = result.getRedirect();\n        if (redirect) {\n          setTimeout(() => {\n            return this.router.navigateByUrl(redirect);\n          }, this.redirectDelay);\n        }\n      });\n    }\n    getConfigValue(key) {\n      return getDeepFromObject(this.options, key, null);\n    }\n    static {\n      this.ɵfac = function NbLogoutComponent_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || NbLogoutComponent)(i0.ɵɵdirectiveInject(NbAuthService), i0.ɵɵdirectiveInject(NB_AUTH_OPTIONS), i0.ɵɵdirectiveInject(i2.Router));\n      };\n    }\n    static {\n      this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n        type: NbLogoutComponent,\n        selectors: [[\"nb-logout\"]],\n        decls: 2,\n        vars: 0,\n        template: function NbLogoutComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\");\n            i0.ɵɵtext(1, \"Logging out, please wait...\");\n            i0.ɵɵelementEnd();\n          }\n        },\n        encapsulation: 2\n      });\n    }\n  }\n  return NbLogoutComponent;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/**\n * @license\n * Copyright Akveo. All Rights Reserved.\n * Licensed under the MIT License. See License.txt in the project root for license information.\n */\nlet NbRequestPasswordComponent = /*#__PURE__*/(() => {\n  class NbRequestPasswordComponent {\n    constructor(service, options = {}, cd, router) {\n      this.service = service;\n      this.options = options;\n      this.cd = cd;\n      this.router = router;\n      this.redirectDelay = 0;\n      this.showMessages = {};\n      this.strategy = '';\n      this.submitted = false;\n      this.errors = [];\n      this.messages = [];\n      this.user = {};\n      this.redirectDelay = this.getConfigValue('forms.requestPassword.redirectDelay');\n      this.showMessages = this.getConfigValue('forms.requestPassword.showMessages');\n      this.strategy = this.getConfigValue('forms.requestPassword.strategy');\n    }\n    requestPass() {\n      this.errors = this.messages = [];\n      this.submitted = true;\n      this.service.requestPassword(this.strategy, this.user).subscribe(result => {\n        this.submitted = false;\n        if (result.isSuccess()) {\n          this.messages = result.getMessages();\n        } else {\n          this.errors = result.getErrors();\n        }\n        const redirect = result.getRedirect();\n        if (redirect) {\n          setTimeout(() => {\n            return this.router.navigateByUrl(redirect);\n          }, this.redirectDelay);\n        }\n        this.cd.detectChanges();\n      });\n    }\n    getConfigValue(key) {\n      return getDeepFromObject(this.options, key, null);\n    }\n    static {\n      this.ɵfac = function NbRequestPasswordComponent_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || NbRequestPasswordComponent)(i0.ɵɵdirectiveInject(NbAuthService), i0.ɵɵdirectiveInject(NB_AUTH_OPTIONS), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i2.Router));\n      };\n    }\n    static {\n      this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n        type: NbRequestPasswordComponent,\n        selectors: [[\"nb-request-password-page\"]],\n        decls: 23,\n        vars: 10,\n        consts: [[\"requestPassForm\", \"ngForm\"], [\"email\", \"ngModel\"], [\"id\", \"title\", 1, \"title\"], [1, \"sub-title\"], [\"outline\", \"danger\", \"role\", \"alert\", 4, \"ngIf\"], [\"outline\", \"success\", \"role\", \"alert\", 4, \"ngIf\"], [\"aria-labelledby\", \"title\", 3, \"ngSubmit\"], [1, \"form-control-group\"], [\"for\", \"input-email\", 1, \"label\"], [\"nbInput\", \"\", \"id\", \"input-email\", \"name\", \"email\", \"pattern\", \".+@.+\\\\..+\", \"placeholder\", \"Email address\", \"autofocus\", \"\", \"fullWidth\", \"\", \"fieldSize\", \"large\", 3, \"ngModelChange\", \"ngModel\", \"status\", \"required\"], [4, \"ngIf\"], [\"nbButton\", \"\", \"fullWidth\", \"\", \"status\", \"primary\", \"size\", \"large\", 3, \"disabled\"], [\"aria-label\", \"Sign in or sign up\", 1, \"sign-in-or-up\"], [\"routerLink\", \"../login\", 1, \"text-link\"], [\"routerLink\", \"../register\", 1, \"text-link\"], [\"outline\", \"danger\", \"role\", \"alert\"], [1, \"alert-title\"], [1, \"alert-message-list\"], [\"class\", \"alert-message\", 4, \"ngFor\", \"ngForOf\"], [1, \"alert-message\"], [\"outline\", \"success\", \"role\", \"alert\"], [\"class\", \"caption status-danger\", 4, \"ngIf\"], [1, \"caption\", \"status-danger\"]],\n        template: function NbRequestPasswordComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            const _r1 = i0.ɵɵgetCurrentView();\n            i0.ɵɵelementStart(0, \"h1\", 2);\n            i0.ɵɵtext(1, \"Forgot Password\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(2, \"p\", 3);\n            i0.ɵɵtext(3, \"Enter your email address and we\\u2019ll send a link to reset your password\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(4, NbRequestPasswordComponent_nb_alert_4_Template, 6, 1, \"nb-alert\", 4)(5, NbRequestPasswordComponent_nb_alert_5_Template, 6, 1, \"nb-alert\", 5);\n            i0.ɵɵelementStart(6, \"form\", 6, 0);\n            i0.ɵɵlistener(\"ngSubmit\", function NbRequestPasswordComponent_Template_form_ngSubmit_6_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.requestPass());\n            });\n            i0.ɵɵelementStart(8, \"div\", 7)(9, \"label\", 8);\n            i0.ɵɵtext(10, \"Enter your email address:\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(11, \"input\", 9, 1);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function NbRequestPasswordComponent_Template_input_ngModelChange_11_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.user.email, $event) || (ctx.user.email = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(13, NbRequestPasswordComponent_ng_container_13_Template, 3, 2, \"ng-container\", 10);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(14, \"button\", 11);\n            i0.ɵɵtext(15, \" Request password \");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(16, \"section\", 12)(17, \"p\")(18, \"a\", 13);\n            i0.ɵɵtext(19, \"Back to Log In\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(20, \"p\")(21, \"a\", 14);\n            i0.ɵɵtext(22, \"Register\");\n            i0.ɵɵelementEnd()()();\n          }\n          if (rf & 2) {\n            const requestPassForm_r6 = i0.ɵɵreference(7);\n            const email_r5 = i0.ɵɵreference(12);\n            i0.ɵɵadvance(4);\n            i0.ɵɵproperty(\"ngIf\", ctx.showMessages.error && (ctx.errors == null ? null : ctx.errors.length) && !ctx.submitted);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.showMessages.success && (ctx.messages == null ? null : ctx.messages.length) && !ctx.submitted);\n            i0.ɵɵadvance(6);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.user.email);\n            i0.ɵɵproperty(\"status\", email_r5.dirty ? email_r5.invalid ? \"danger\" : \"success\" : \"basic\")(\"required\", ctx.getConfigValue(\"forms.validation.email.required\"));\n            i0.ɵɵattribute(\"aria-invalid\", email_r5.invalid && email_r5.touched ? true : null);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"ngIf\", email_r5.invalid && email_r5.touched);\n            i0.ɵɵadvance();\n            i0.ɵɵclassProp(\"btn-pulse\", ctx.submitted);\n            i0.ɵɵproperty(\"disabled\", ctx.submitted || !requestPassForm_r6.valid);\n          }\n        },\n        dependencies: [i3.NgForOf, i3.NgIf, i4.NbAlertComponent, i4.NbInputDirective, i4.NbButtonComponent, i2.RouterLink, i5.ɵNgNoValidate, i5.DefaultValueAccessor, i5.NgControlStatus, i5.NgControlStatusGroup, i5.RequiredValidator, i5.PatternValidator, i5.NgModel, i5.NgForm],\n        styles: [\"[_nghost-%COMP%]   .form-group[_ngcontent-%COMP%]:last-of-type{margin-bottom:3rem}\\n\\n\\n\\n\\n\\n\"],\n        changeDetection: 0\n      });\n    }\n  }\n  return NbRequestPasswordComponent;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/**\n * @license\n * Copyright Akveo. All Rights Reserved.\n * Licensed under the MIT License. See License.txt in the project root for license information.\n */\nlet NbResetPasswordComponent = /*#__PURE__*/(() => {\n  class NbResetPasswordComponent {\n    constructor(service, options = {}, cd, router) {\n      this.service = service;\n      this.options = options;\n      this.cd = cd;\n      this.router = router;\n      this.redirectDelay = 0;\n      this.showMessages = {};\n      this.strategy = '';\n      this.submitted = false;\n      this.errors = [];\n      this.messages = [];\n      this.user = {};\n      this.redirectDelay = this.getConfigValue('forms.resetPassword.redirectDelay');\n      this.showMessages = this.getConfigValue('forms.resetPassword.showMessages');\n      this.strategy = this.getConfigValue('forms.resetPassword.strategy');\n    }\n    resetPass() {\n      this.errors = this.messages = [];\n      this.submitted = true;\n      this.service.resetPassword(this.strategy, this.user).subscribe(result => {\n        this.submitted = false;\n        if (result.isSuccess()) {\n          this.messages = result.getMessages();\n        } else {\n          this.errors = result.getErrors();\n        }\n        const redirect = result.getRedirect();\n        if (redirect) {\n          setTimeout(() => {\n            return this.router.navigateByUrl(redirect);\n          }, this.redirectDelay);\n        }\n        this.cd.detectChanges();\n      });\n    }\n    getConfigValue(key) {\n      return getDeepFromObject(this.options, key, null);\n    }\n    static {\n      this.ɵfac = function NbResetPasswordComponent_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || NbResetPasswordComponent)(i0.ɵɵdirectiveInject(NbAuthService), i0.ɵɵdirectiveInject(NB_AUTH_OPTIONS), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i2.Router));\n      };\n    }\n    static {\n      this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n        type: NbResetPasswordComponent,\n        selectors: [[\"nb-reset-password-page\"]],\n        decls: 29,\n        vars: 17,\n        consts: [[\"resetPassForm\", \"ngForm\"], [\"password\", \"ngModel\"], [\"rePass\", \"ngModel\"], [\"id\", \"title\", 1, \"title\"], [1, \"sub-title\"], [\"outline\", \"danger\", \"role\", \"alert\", 4, \"ngIf\"], [\"outline\", \"success\", \"role\", \"alert\", 4, \"ngIf\"], [\"aria-labelledby\", \"title\", 3, \"ngSubmit\"], [1, \"form-control-group\"], [\"for\", \"input-password\", 1, \"label\"], [\"nbInput\", \"\", \"type\", \"password\", \"id\", \"input-password\", \"name\", \"password\", \"placeholder\", \"New Password\", \"autofocus\", \"\", \"fullWidth\", \"\", \"fieldSize\", \"large\", 1, \"first\", 3, \"ngModelChange\", \"ngModel\", \"status\", \"required\", \"minlength\", \"maxlength\"], [4, \"ngIf\"], [1, \"form-group\"], [\"for\", \"input-re-password\", 1, \"label\"], [\"nbInput\", \"\", \"id\", \"input-re-password\", \"name\", \"rePass\", \"type\", \"password\", \"placeholder\", \"Confirm Password\", \"fullWidth\", \"\", \"fieldSize\", \"large\", 1, \"last\", 3, \"ngModelChange\", \"ngModel\", \"status\", \"required\"], [\"nbButton\", \"\", \"status\", \"primary\", \"fullWidth\", \"\", \"size\", \"large\", 3, \"disabled\"], [\"aria-label\", \"Sign in or sign up\", 1, \"sign-in-or-up\"], [\"routerLink\", \"../login\", 1, \"text-link\"], [\"routerLink\", \"../register\", 1, \"text-link\"], [\"outline\", \"danger\", \"role\", \"alert\"], [1, \"alert-title\"], [1, \"alert-message-list\"], [\"class\", \"alert-message\", 4, \"ngFor\", \"ngForOf\"], [1, \"alert-message\"], [\"outline\", \"success\", \"role\", \"alert\"], [\"class\", \"caption status-danger\", 4, \"ngIf\"], [1, \"caption\", \"status-danger\"]],\n        template: function NbResetPasswordComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            const _r1 = i0.ɵɵgetCurrentView();\n            i0.ɵɵelementStart(0, \"h1\", 3);\n            i0.ɵɵtext(1, \"Change password\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(2, \"p\", 4);\n            i0.ɵɵtext(3, \"Please set a new password\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(4, NbResetPasswordComponent_nb_alert_4_Template, 6, 1, \"nb-alert\", 5)(5, NbResetPasswordComponent_nb_alert_5_Template, 6, 1, \"nb-alert\", 6);\n            i0.ɵɵelementStart(6, \"form\", 7, 0);\n            i0.ɵɵlistener(\"ngSubmit\", function NbResetPasswordComponent_Template_form_ngSubmit_6_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.resetPass());\n            });\n            i0.ɵɵelementStart(8, \"div\", 8)(9, \"label\", 9);\n            i0.ɵɵtext(10, \"New Password:\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(11, \"input\", 10, 1);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function NbResetPasswordComponent_Template_input_ngModelChange_11_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.user.password, $event) || (ctx.user.password = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(13, NbResetPasswordComponent_ng_container_13_Template, 3, 2, \"ng-container\", 11);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(14, \"div\", 12)(15, \"label\", 13);\n            i0.ɵɵtext(16, \"Confirm Password:\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(17, \"input\", 14, 2);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function NbResetPasswordComponent_Template_input_ngModelChange_17_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.user.confirmPassword, $event) || (ctx.user.confirmPassword = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(19, NbResetPasswordComponent_ng_container_19_Template, 3, 2, \"ng-container\", 11);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(20, \"button\", 15);\n            i0.ɵɵtext(21, \" Change password \");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(22, \"section\", 16)(23, \"p\")(24, \"a\", 17);\n            i0.ɵɵtext(25, \"Back to Log In\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(26, \"p\")(27, \"a\", 18);\n            i0.ɵɵtext(28, \"Register\");\n            i0.ɵɵelementEnd()()();\n          }\n          if (rf & 2) {\n            const resetPassForm_r7 = i0.ɵɵreference(7);\n            const password_r5 = i0.ɵɵreference(12);\n            const rePass_r6 = i0.ɵɵreference(18);\n            i0.ɵɵadvance(4);\n            i0.ɵɵproperty(\"ngIf\", ctx.showMessages.error && (ctx.errors == null ? null : ctx.errors.length) && !ctx.submitted);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.showMessages.success && (ctx.messages == null ? null : ctx.messages.length) && !ctx.submitted);\n            i0.ɵɵadvance(6);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.user.password);\n            i0.ɵɵproperty(\"status\", password_r5.dirty ? password_r5.invalid ? \"danger\" : \"success\" : \"basic\")(\"required\", ctx.getConfigValue(\"forms.validation.password.required\"))(\"minlength\", ctx.getConfigValue(\"forms.validation.password.minLength\"))(\"maxlength\", ctx.getConfigValue(\"forms.validation.password.maxLength\"));\n            i0.ɵɵattribute(\"aria-invalid\", password_r5.invalid && password_r5.touched ? true : null);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"ngIf\", password_r5.invalid && password_r5.touched);\n            i0.ɵɵadvance(4);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.user.confirmPassword);\n            i0.ɵɵproperty(\"status\", rePass_r6.touched ? rePass_r6.invalid || password_r5.value != rePass_r6.value ? \"danger\" : \"success\" : \"basic\")(\"required\", ctx.getConfigValue(\"forms.validation.password.required\"));\n            i0.ɵɵattribute(\"aria-invalid\", rePass_r6.invalid && rePass_r6.touched ? true : null);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"ngIf\", rePass_r6.touched);\n            i0.ɵɵadvance();\n            i0.ɵɵclassProp(\"btn-pulse\", ctx.submitted);\n            i0.ɵɵproperty(\"disabled\", ctx.submitted || !resetPassForm_r7.valid);\n          }\n        },\n        dependencies: [i3.NgForOf, i3.NgIf, i4.NbAlertComponent, i4.NbInputDirective, i4.NbButtonComponent, i2.RouterLink, i5.ɵNgNoValidate, i5.DefaultValueAccessor, i5.NgControlStatus, i5.NgControlStatusGroup, i5.RequiredValidator, i5.MinLengthValidator, i5.MaxLengthValidator, i5.NgModel, i5.NgForm],\n        styles: [_c1],\n        changeDetection: 0\n      });\n    }\n  }\n  return NbResetPasswordComponent;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nfunction nbStrategiesFactory(options, injector) {\n  const strategies = [];\n  options.strategies.forEach(([strategyClass, strategyOptions]) => {\n    const strategy = injector.get(strategyClass);\n    strategy.setOptions(strategyOptions);\n    strategies.push(strategy);\n  });\n  return strategies;\n}\nfunction nbTokensFactory(strategies) {\n  const tokens = [];\n  strategies.forEach(strategy => {\n    tokens.push(strategy.getOption('token.class'));\n  });\n  return tokens;\n}\nfunction nbOptionsFactory(options) {\n  return deepExtend(defaultAuthOptions, options);\n}\nfunction nbNoOpInterceptorFilter(req) {\n  return true;\n}\nlet NbAuthModule = /*#__PURE__*/(() => {\n  class NbAuthModule {\n    static forRoot(nbAuthOptions) {\n      return {\n        ngModule: NbAuthModule,\n        providers: [{\n          provide: NB_AUTH_USER_OPTIONS,\n          useValue: nbAuthOptions\n        }, {\n          provide: NB_AUTH_OPTIONS,\n          useFactory: nbOptionsFactory,\n          deps: [NB_AUTH_USER_OPTIONS]\n        }, {\n          provide: NB_AUTH_STRATEGIES,\n          useFactory: nbStrategiesFactory,\n          deps: [NB_AUTH_OPTIONS, Injector]\n        }, {\n          provide: NB_AUTH_TOKENS,\n          useFactory: nbTokensFactory,\n          deps: [NB_AUTH_STRATEGIES]\n        }, {\n          provide: NB_AUTH_FALLBACK_TOKEN,\n          useValue: NbAuthSimpleToken\n        }, {\n          provide: NB_AUTH_INTERCEPTOR_HEADER,\n          useValue: 'Authorization'\n        }, {\n          provide: NB_AUTH_TOKEN_INTERCEPTOR_FILTER,\n          useValue: nbNoOpInterceptorFilter\n        }, {\n          provide: NbTokenStorage,\n          useClass: NbTokenLocalStorage\n        }, NbAuthTokenParceler, NbAuthService, NbTokenService, NbDummyAuthStrategy, NbPasswordAuthStrategy, NbOAuth2AuthStrategy]\n      };\n    }\n    static {\n      this.ɵfac = function NbAuthModule_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || NbAuthModule)();\n      };\n    }\n    static {\n      this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n        type: NbAuthModule\n      });\n    }\n    static {\n      this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n        imports: [CommonModule, NbLayoutModule, NbCardModule, NbCheckboxModule, NbAlertModule, NbInputModule, NbButtonModule, RouterModule, FormsModule, NbIconModule]\n      });\n    }\n  }\n  return NbAuthModule;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nconst routes = [{\n  path: 'auth',\n  component: NbAuthComponent,\n  children: [{\n    path: '',\n    component: NbLoginComponent\n  }, {\n    path: 'login',\n    component: NbLoginComponent\n  }, {\n    path: 'register',\n    component: NbRegisterComponent\n  }, {\n    path: 'logout',\n    component: NbLogoutComponent\n  }, {\n    path: 'request-password',\n    component: NbRequestPasswordComponent\n  }, {\n    path: 'reset-password',\n    component: NbResetPasswordComponent\n  }]\n}];\nlet NbAuthJWTInterceptor = /*#__PURE__*/(() => {\n  class NbAuthJWTInterceptor {\n    constructor(injector, filter) {\n      this.injector = injector;\n      this.filter = filter;\n    }\n    intercept(req, next) {\n      // do not intercept request whose urls are filtered by the injected filter\n      if (!this.filter(req)) {\n        return this.authService.isAuthenticatedOrRefresh().pipe(switchMap(authenticated => {\n          if (authenticated) {\n            return this.authService.getToken().pipe(switchMap(token => {\n              const JWT = `Bearer ${token.getValue()}`;\n              req = req.clone({\n                setHeaders: {\n                  Authorization: JWT\n                }\n              });\n              return next.handle(req);\n            }));\n          } else {\n            // Request is sent to server without authentication so that the client code\n            // receives the 401/403 error and can act as desired ('session expired', redirect to login, aso)\n            return next.handle(req);\n          }\n        }));\n      } else {\n        return next.handle(req);\n      }\n    }\n    get authService() {\n      return this.injector.get(NbAuthService);\n    }\n    static {\n      this.ɵfac = function NbAuthJWTInterceptor_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || NbAuthJWTInterceptor)(i0.ɵɵinject(i0.Injector), i0.ɵɵinject(NB_AUTH_TOKEN_INTERCEPTOR_FILTER));\n      };\n    }\n    static {\n      this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n        token: NbAuthJWTInterceptor,\n        factory: NbAuthJWTInterceptor.ɵfac\n      });\n    }\n  }\n  return NbAuthJWTInterceptor;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet NbAuthSimpleInterceptor = /*#__PURE__*/(() => {\n  class NbAuthSimpleInterceptor {\n    constructor(injector, headerName = 'Authorization') {\n      this.injector = injector;\n      this.headerName = headerName;\n    }\n    intercept(req, next) {\n      return this.authService.getToken().pipe(switchMap(token => {\n        if (token && token.getValue()) {\n          req = req.clone({\n            setHeaders: {\n              [this.headerName]: token.getValue()\n            }\n          });\n        }\n        return next.handle(req);\n      }));\n    }\n    get authService() {\n      return this.injector.get(NbAuthService);\n    }\n    static {\n      this.ɵfac = function NbAuthSimpleInterceptor_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || NbAuthSimpleInterceptor)(i0.ɵɵinject(i0.Injector), i0.ɵɵinject(NB_AUTH_INTERCEPTOR_HEADER));\n      };\n    }\n    static {\n      this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n        token: NbAuthSimpleInterceptor,\n        factory: NbAuthSimpleInterceptor.ɵfac\n      });\n    }\n  }\n  return NbAuthSimpleInterceptor;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nclass NbUser {\n  constructor(id, email, password, rememberMe, terms, confirmPassword, fullName) {\n    this.id = id;\n    this.email = email;\n    this.password = password;\n    this.rememberMe = rememberMe;\n    this.terms = terms;\n    this.confirmPassword = confirmPassword;\n    this.fullName = fullName;\n  }\n}\n\n/**\n * @license\n * Copyright Akveo. All Rights Reserved.\n * Licensed under the MIT License. See License.txt in the project root for license information.\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { NB_AUTH_FALLBACK_TOKEN, NB_AUTH_INTERCEPTOR_HEADER, NB_AUTH_OPTIONS, NB_AUTH_STRATEGIES, NB_AUTH_TOKENS, NB_AUTH_TOKEN_INTERCEPTOR_FILTER, NB_AUTH_USER_OPTIONS, NbAuthBlockComponent, NbAuthComponent, NbAuthEmptyTokenError, NbAuthIllegalJWTTokenError, NbAuthIllegalTokenError, NbAuthJWTInterceptor, NbAuthJWTToken, NbAuthModule, NbAuthOAuth2JWTToken, NbAuthOAuth2Token, NbAuthResult, NbAuthService, NbAuthSimpleInterceptor, NbAuthSimpleToken, NbAuthStrategy, NbAuthStrategyOptions, NbAuthToken, NbAuthTokenNotFoundError, NbAuthTokenParceler, NbDummyAuthStrategy, NbDummyAuthStrategyOptions, NbLoginComponent, NbLogoutComponent, NbOAuth2AuthStrategy, NbOAuth2AuthStrategyOptions, NbOAuth2ClientAuthMethod, NbOAuth2GrantType, NbOAuth2ResponseType, NbPasswordAuthStrategy, NbPasswordAuthStrategyOptions, NbRegisterComponent, NbRequestPasswordComponent, NbResetPasswordComponent, NbTokenLocalStorage, NbTokenService, NbTokenStorage, NbUser, auth2StrategyOptions, b64DecodeUnicode, b64decode, decodeJwtPayload, deepExtend, defaultAuthOptions, dummyStrategyOptions, getDeepFromObject, nbAuthCreateToken, nbNoOpInterceptorFilter, nbOptionsFactory, nbStrategiesFactory, nbTokensFactory, passwordStrategyOptions, routes, urlBase64Decode };\n//# sourceMappingURL=nebular-auth.mjs.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}