{"ast": null, "code": "//! moment.js locale configuration\n//! locale : Faroese [fo]\n//! author : <PERSON><PERSON><PERSON> : https://github.com/ragnar123\n//! author : <PERSON><PERSON> : https://github.com/sa<PERSON><PERSON>\n\n;\n(function (global, factory) {\n  typeof exports === 'object' && typeof module !== 'undefined' && typeof require === 'function' ? factory(require('../moment')) : typeof define === 'function' && define.amd ? define(['../moment'], factory) : factory(global.moment);\n})(this, function (moment) {\n  'use strict';\n\n  //! moment.js locale configuration\n  var fo = moment.defineLocale('fo', {\n    months: 'januar_februar_mars_apríl_mai_juni_juli_august_september_oktober_november_desember'.split('_'),\n    monthsShort: 'jan_feb_mar_apr_mai_jun_jul_aug_sep_okt_nov_des'.split('_'),\n    weekdays: 'sunnudagur_mánadagur_týsdagur_mikudagur_hósdagur_fríggjadagur_leygardagur'.split('_'),\n    weekdaysShort: 'sun_mán_týs_mik_hós_frí_ley'.split('_'),\n    weekdaysMin: 'su_má_tý_mi_hó_fr_le'.split('_'),\n    longDateFormat: {\n      LT: 'HH:mm',\n      LTS: 'HH:mm:ss',\n      L: 'DD/MM/YYYY',\n      LL: 'D MMMM YYYY',\n      LLL: 'D MMMM YYYY HH:mm',\n      LLLL: 'dddd D. MMMM, YYYY HH:mm'\n    },\n    calendar: {\n      sameDay: '[Í dag kl.] LT',\n      nextDay: '[Í morgin kl.] LT',\n      nextWeek: 'dddd [kl.] LT',\n      lastDay: '[Í gjár kl.] LT',\n      lastWeek: '[síðstu] dddd [kl] LT',\n      sameElse: 'L'\n    },\n    relativeTime: {\n      future: 'um %s',\n      past: '%s síðani',\n      s: 'fá sekund',\n      ss: '%d sekundir',\n      m: 'ein minuttur',\n      mm: '%d minuttir',\n      h: 'ein tími',\n      hh: '%d tímar',\n      d: 'ein dagur',\n      dd: '%d dagar',\n      M: 'ein mánaður',\n      MM: '%d mánaðir',\n      y: 'eitt ár',\n      yy: '%d ár'\n    },\n    dayOfMonthOrdinalParse: /\\d{1,2}\\./,\n    ordinal: '%d.',\n    week: {\n      dow: 1,\n      // Monday is the first day of the week.\n      doy: 4 // The week that contains Jan 4th is the first week of the year.\n    }\n  });\n  return fo;\n});", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}