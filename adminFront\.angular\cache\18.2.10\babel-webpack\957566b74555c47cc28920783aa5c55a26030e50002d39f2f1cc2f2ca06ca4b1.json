{"ast": null, "code": ";\n(function (root, factory, undef) {\n  if (typeof exports === \"object\") {\n    // CommonJS\n    module.exports = exports = factory(require(\"./core\"), require(\"./sha1\"), require(\"./hmac\"));\n  } else if (typeof define === \"function\" && define.amd) {\n    // AMD\n    define([\"./core\", \"./sha1\", \"./hmac\"], factory);\n  } else {\n    // Global (browser)\n    factory(root.CryptoJS);\n  }\n})(this, function (CryptoJS) {\n  (function () {\n    // Shortcuts\n    var C = CryptoJS;\n    var C_lib = C.lib;\n    var Base = C_lib.Base;\n    var WordArray = C_lib.WordArray;\n    var C_algo = C.algo;\n    var MD5 = C_algo.MD5;\n\n    /**\n     * This key derivation function is meant to conform with EVP_BytesToKey.\n     * www.openssl.org/docs/crypto/EVP_BytesToKey.html\n     */\n    var EvpKDF = C_algo.EvpKDF = Base.extend({\n      /**\n       * Configuration options.\n       *\n       * @property {number} keySize The key size in words to generate. Default: 4 (128 bits)\n       * @property {Hasher} hasher The hash algorithm to use. Default: MD5\n       * @property {number} iterations The number of iterations to perform. Default: 1\n       */\n      cfg: Base.extend({\n        keySize: 128 / 32,\n        hasher: MD5,\n        iterations: 1\n      }),\n      /**\n       * Initializes a newly created key derivation function.\n       *\n       * @param {Object} cfg (Optional) The configuration options to use for the derivation.\n       *\n       * @example\n       *\n       *     var kdf = CryptoJS.algo.EvpKDF.create();\n       *     var kdf = CryptoJS.algo.EvpKDF.create({ keySize: 8 });\n       *     var kdf = CryptoJS.algo.EvpKDF.create({ keySize: 8, iterations: 1000 });\n       */\n      init: function (cfg) {\n        this.cfg = this.cfg.extend(cfg);\n      },\n      /**\n       * Derives a key from a password.\n       *\n       * @param {WordArray|string} password The password.\n       * @param {WordArray|string} salt A salt.\n       *\n       * @return {WordArray} The derived key.\n       *\n       * @example\n       *\n       *     var key = kdf.compute(password, salt);\n       */\n      compute: function (password, salt) {\n        var block;\n\n        // Shortcut\n        var cfg = this.cfg;\n\n        // Init hasher\n        var hasher = cfg.hasher.create();\n\n        // Initial values\n        var derivedKey = WordArray.create();\n\n        // Shortcuts\n        var derivedKeyWords = derivedKey.words;\n        var keySize = cfg.keySize;\n        var iterations = cfg.iterations;\n\n        // Generate key\n        while (derivedKeyWords.length < keySize) {\n          if (block) {\n            hasher.update(block);\n          }\n          block = hasher.update(password).finalize(salt);\n          hasher.reset();\n\n          // Iterations\n          for (var i = 1; i < iterations; i++) {\n            block = hasher.finalize(block);\n            hasher.reset();\n          }\n          derivedKey.concat(block);\n        }\n        derivedKey.sigBytes = keySize * 4;\n        return derivedKey;\n      }\n    });\n\n    /**\n     * Derives a key from a password.\n     *\n     * @param {WordArray|string} password The password.\n     * @param {WordArray|string} salt A salt.\n     * @param {Object} cfg (Optional) The configuration options to use for this computation.\n     *\n     * @return {WordArray} The derived key.\n     *\n     * @static\n     *\n     * @example\n     *\n     *     var key = CryptoJS.EvpKDF(password, salt);\n     *     var key = CryptoJS.EvpKDF(password, salt, { keySize: 8 });\n     *     var key = CryptoJS.EvpKDF(password, salt, { keySize: 8, iterations: 1000 });\n     */\n    C.EvpKDF = function (password, salt, cfg) {\n      return EvpKDF.create(cfg).compute(password, salt);\n    };\n  })();\n  return CryptoJS.EvpKDF;\n});", "map": {"version": 3, "names": ["root", "factory", "undef", "exports", "module", "require", "define", "amd", "CryptoJS", "C", "C_lib", "lib", "Base", "WordArray", "C_algo", "algo", "MD5", "EvpKDF", "extend", "cfg", "keySize", "hasher", "iterations", "init", "compute", "password", "salt", "block", "create", "<PERSON><PERSON><PERSON>", "derived<PERSON>eyWords", "words", "length", "update", "finalize", "reset", "i", "concat", "sigBytes"], "sources": ["C:/Users/<USER>/Documents/salechange-product/adminFront/node_modules/crypto-js/evpkdf.js"], "sourcesContent": [";(function (root, factory, undef) {\n\tif (typeof exports === \"object\") {\n\t\t// CommonJS\n\t\tmodule.exports = exports = factory(require(\"./core\"), require(\"./sha1\"), require(\"./hmac\"));\n\t}\n\telse if (typeof define === \"function\" && define.amd) {\n\t\t// AMD\n\t\tdefine([\"./core\", \"./sha1\", \"./hmac\"], factory);\n\t}\n\telse {\n\t\t// Global (browser)\n\t\tfactory(root.CryptoJS);\n\t}\n}(this, function (CryptoJS) {\n\n\t(function () {\n\t    // Shortcuts\n\t    var C = CryptoJS;\n\t    var C_lib = C.lib;\n\t    var Base = C_lib.Base;\n\t    var WordArray = C_lib.WordArray;\n\t    var C_algo = C.algo;\n\t    var MD5 = C_algo.MD5;\n\n\t    /**\n\t     * This key derivation function is meant to conform with EVP_BytesToKey.\n\t     * www.openssl.org/docs/crypto/EVP_BytesToKey.html\n\t     */\n\t    var EvpKDF = C_algo.EvpKDF = Base.extend({\n\t        /**\n\t         * Configuration options.\n\t         *\n\t         * @property {number} keySize The key size in words to generate. Default: 4 (128 bits)\n\t         * @property {Hasher} hasher The hash algorithm to use. Default: MD5\n\t         * @property {number} iterations The number of iterations to perform. Default: 1\n\t         */\n\t        cfg: Base.extend({\n\t            keySize: 128/32,\n\t            hasher: MD5,\n\t            iterations: 1\n\t        }),\n\n\t        /**\n\t         * Initializes a newly created key derivation function.\n\t         *\n\t         * @param {Object} cfg (Optional) The configuration options to use for the derivation.\n\t         *\n\t         * @example\n\t         *\n\t         *     var kdf = CryptoJS.algo.EvpKDF.create();\n\t         *     var kdf = CryptoJS.algo.EvpKDF.create({ keySize: 8 });\n\t         *     var kdf = CryptoJS.algo.EvpKDF.create({ keySize: 8, iterations: 1000 });\n\t         */\n\t        init: function (cfg) {\n\t            this.cfg = this.cfg.extend(cfg);\n\t        },\n\n\t        /**\n\t         * Derives a key from a password.\n\t         *\n\t         * @param {WordArray|string} password The password.\n\t         * @param {WordArray|string} salt A salt.\n\t         *\n\t         * @return {WordArray} The derived key.\n\t         *\n\t         * @example\n\t         *\n\t         *     var key = kdf.compute(password, salt);\n\t         */\n\t        compute: function (password, salt) {\n\t            var block;\n\n\t            // Shortcut\n\t            var cfg = this.cfg;\n\n\t            // Init hasher\n\t            var hasher = cfg.hasher.create();\n\n\t            // Initial values\n\t            var derivedKey = WordArray.create();\n\n\t            // Shortcuts\n\t            var derivedKeyWords = derivedKey.words;\n\t            var keySize = cfg.keySize;\n\t            var iterations = cfg.iterations;\n\n\t            // Generate key\n\t            while (derivedKeyWords.length < keySize) {\n\t                if (block) {\n\t                    hasher.update(block);\n\t                }\n\t                block = hasher.update(password).finalize(salt);\n\t                hasher.reset();\n\n\t                // Iterations\n\t                for (var i = 1; i < iterations; i++) {\n\t                    block = hasher.finalize(block);\n\t                    hasher.reset();\n\t                }\n\n\t                derivedKey.concat(block);\n\t            }\n\t            derivedKey.sigBytes = keySize * 4;\n\n\t            return derivedKey;\n\t        }\n\t    });\n\n\t    /**\n\t     * Derives a key from a password.\n\t     *\n\t     * @param {WordArray|string} password The password.\n\t     * @param {WordArray|string} salt A salt.\n\t     * @param {Object} cfg (Optional) The configuration options to use for this computation.\n\t     *\n\t     * @return {WordArray} The derived key.\n\t     *\n\t     * @static\n\t     *\n\t     * @example\n\t     *\n\t     *     var key = CryptoJS.EvpKDF(password, salt);\n\t     *     var key = CryptoJS.EvpKDF(password, salt, { keySize: 8 });\n\t     *     var key = CryptoJS.EvpKDF(password, salt, { keySize: 8, iterations: 1000 });\n\t     */\n\t    C.EvpKDF = function (password, salt, cfg) {\n\t        return EvpKDF.create(cfg).compute(password, salt);\n\t    };\n\t}());\n\n\n\treturn CryptoJS.EvpKDF;\n\n}));"], "mappings": "AAAA;AAAE,WAAUA,IAAI,EAAEC,OAAO,EAAEC,KAAK,EAAE;EACjC,IAAI,OAAOC,OAAO,KAAK,QAAQ,EAAE;IAChC;IACAC,MAAM,CAACD,OAAO,GAAGA,OAAO,GAAGF,OAAO,CAACI,OAAO,CAAC,QAAQ,CAAC,EAAEA,OAAO,CAAC,QAAQ,CAAC,EAAEA,OAAO,CAAC,QAAQ,CAAC,CAAC;EAC5F,CAAC,MACI,IAAI,OAAOC,MAAM,KAAK,UAAU,IAAIA,MAAM,CAACC,GAAG,EAAE;IACpD;IACAD,MAAM,CAAC,CAAC,QAAQ,EAAE,QAAQ,EAAE,QAAQ,CAAC,EAAEL,OAAO,CAAC;EAChD,CAAC,MACI;IACJ;IACAA,OAAO,CAACD,IAAI,CAACQ,QAAQ,CAAC;EACvB;AACD,CAAC,EAAC,IAAI,EAAE,UAAUA,QAAQ,EAAE;EAE1B,aAAY;IACT;IACA,IAAIC,CAAC,GAAGD,QAAQ;IAChB,IAAIE,KAAK,GAAGD,CAAC,CAACE,GAAG;IACjB,IAAIC,IAAI,GAAGF,KAAK,CAACE,IAAI;IACrB,IAAIC,SAAS,GAAGH,KAAK,CAACG,SAAS;IAC/B,IAAIC,MAAM,GAAGL,CAAC,CAACM,IAAI;IACnB,IAAIC,GAAG,GAAGF,MAAM,CAACE,GAAG;;IAEpB;AACL;AACA;AACA;IACK,IAAIC,MAAM,GAAGH,MAAM,CAACG,MAAM,GAAGL,IAAI,CAACM,MAAM,CAAC;MACrC;AACT;AACA;AACA;AACA;AACA;AACA;MACSC,GAAG,EAAEP,IAAI,CAACM,MAAM,CAAC;QACbE,OAAO,EAAE,GAAG,GAAC,EAAE;QACfC,MAAM,EAAEL,GAAG;QACXM,UAAU,EAAE;MAChB,CAAC,CAAC;MAEF;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACSC,IAAI,EAAE,SAAAA,CAAUJ,GAAG,EAAE;QACjB,IAAI,CAACA,GAAG,GAAG,IAAI,CAACA,GAAG,CAACD,MAAM,CAACC,GAAG,CAAC;MACnC,CAAC;MAED;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACSK,OAAO,EAAE,SAAAA,CAAUC,QAAQ,EAAEC,IAAI,EAAE;QAC/B,IAAIC,KAAK;;QAET;QACA,IAAIR,GAAG,GAAG,IAAI,CAACA,GAAG;;QAElB;QACA,IAAIE,MAAM,GAAGF,GAAG,CAACE,MAAM,CAACO,MAAM,CAAC,CAAC;;QAEhC;QACA,IAAIC,UAAU,GAAGhB,SAAS,CAACe,MAAM,CAAC,CAAC;;QAEnC;QACA,IAAIE,eAAe,GAAGD,UAAU,CAACE,KAAK;QACtC,IAAIX,OAAO,GAAGD,GAAG,CAACC,OAAO;QACzB,IAAIE,UAAU,GAAGH,GAAG,CAACG,UAAU;;QAE/B;QACA,OAAOQ,eAAe,CAACE,MAAM,GAAGZ,OAAO,EAAE;UACrC,IAAIO,KAAK,EAAE;YACPN,MAAM,CAACY,MAAM,CAACN,KAAK,CAAC;UACxB;UACAA,KAAK,GAAGN,MAAM,CAACY,MAAM,CAACR,QAAQ,CAAC,CAACS,QAAQ,CAACR,IAAI,CAAC;UAC9CL,MAAM,CAACc,KAAK,CAAC,CAAC;;UAEd;UACA,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGd,UAAU,EAAEc,CAAC,EAAE,EAAE;YACjCT,KAAK,GAAGN,MAAM,CAACa,QAAQ,CAACP,KAAK,CAAC;YAC9BN,MAAM,CAACc,KAAK,CAAC,CAAC;UAClB;UAEAN,UAAU,CAACQ,MAAM,CAACV,KAAK,CAAC;QAC5B;QACAE,UAAU,CAACS,QAAQ,GAAGlB,OAAO,GAAG,CAAC;QAEjC,OAAOS,UAAU;MACrB;IACJ,CAAC,CAAC;;IAEF;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACKpB,CAAC,CAACQ,MAAM,GAAG,UAAUQ,QAAQ,EAAEC,IAAI,EAAEP,GAAG,EAAE;MACtC,OAAOF,MAAM,CAACW,MAAM,CAACT,GAAG,CAAC,CAACK,OAAO,CAACC,QAAQ,EAAEC,IAAI,CAAC;IACrD,CAAC;EACL,CAAC,EAAC,CAAC;EAGH,OAAOlB,QAAQ,CAACS,MAAM;AAEvB,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}