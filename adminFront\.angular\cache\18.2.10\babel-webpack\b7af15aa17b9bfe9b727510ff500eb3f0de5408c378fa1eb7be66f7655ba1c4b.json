{"ast": null, "code": "import { createPlugin } from '@fullcalendar/core/index.js';\nimport { ListView } from './internal.js';\nimport { identity, createFormatter } from '@fullcalendar/core/internal.js';\nimport '@fullcalendar/core/preact.js';\nconst OPTION_REFINERS = {\n  listDayFormat: createFalsableFormatter,\n  listDaySideFormat: createFalsableFormatter,\n  noEventsClassNames: identity,\n  noEventsContent: identity,\n  noEventsDidMount: identity,\n  noEventsWillUnmount: identity\n  // noEventsText is defined in base options\n};\nfunction createFalsableFormatter(input) {\n  return input === false ? null : createFormatter(input);\n}\nvar index = createPlugin({\n  name: '@fullcalendar/list',\n  optionRefiners: OPTION_REFINERS,\n  views: {\n    list: {\n      component: ListView,\n      buttonTextKey: 'list',\n      listDayFormat: {\n        month: 'long',\n        day: 'numeric',\n        year: 'numeric'\n      } // like \"January 1, 2016\"\n    },\n    listDay: {\n      type: 'list',\n      duration: {\n        days: 1\n      },\n      listDayFormat: {\n        weekday: 'long'\n      } // day-of-week is all we need. full date is probably in headerToolbar\n    },\n    listWeek: {\n      type: 'list',\n      duration: {\n        weeks: 1\n      },\n      listDayFormat: {\n        weekday: 'long'\n      },\n      listDaySideFormat: {\n        month: 'long',\n        day: 'numeric',\n        year: 'numeric'\n      }\n    },\n    listMonth: {\n      type: 'list',\n      duration: {\n        month: 1\n      },\n      listDaySideFormat: {\n        weekday: 'long'\n      } // day-of-week is nice-to-have\n    },\n    listYear: {\n      type: 'list',\n      duration: {\n        year: 1\n      },\n      listDaySideFormat: {\n        weekday: 'long'\n      } // day-of-week is nice-to-have\n    }\n  }\n});\nexport { index as default };", "map": {"version": 3, "names": ["createPlugin", "ListView", "identity", "createFormatter", "OPTION_REFINERS", "listDayFormat", "createFalsableFormatter", "listDaySideFormat", "noEventsClassNames", "noEventsContent", "noEventsDidMount", "noEventsWillUnmount", "input", "index", "name", "optionRefiners", "views", "list", "component", "buttonTextKey", "month", "day", "year", "listDay", "type", "duration", "days", "weekday", "listWeek", "weeks", "listMonth", "listYear", "default"], "sources": ["C:/Users/<USER>/Documents/jeansalechange/adminFront/node_modules/@fullcalendar/list/index.js"], "sourcesContent": ["import { createPlugin } from '@fullcalendar/core/index.js';\nimport { ListView } from './internal.js';\nimport { identity, createFormatter } from '@fullcalendar/core/internal.js';\nimport '@fullcalendar/core/preact.js';\n\nconst OPTION_REFINERS = {\n    listDayFormat: createFalsableFormatter,\n    listDaySideFormat: createFalsableFormatter,\n    noEventsClassNames: identity,\n    noEventsContent: identity,\n    noEventsDidMount: identity,\n    noEventsWillUnmount: identity,\n    // noEventsText is defined in base options\n};\nfunction createFalsableFormatter(input) {\n    return input === false ? null : createFormatter(input);\n}\n\nvar index = createPlugin({\n    name: '@fullcalendar/list',\n    optionRefiners: OPTION_REFINERS,\n    views: {\n        list: {\n            component: ListView,\n            buttonTextKey: 'list',\n            listDayFormat: { month: 'long', day: 'numeric', year: 'numeric' }, // like \"January 1, 2016\"\n        },\n        listDay: {\n            type: 'list',\n            duration: { days: 1 },\n            listDayFormat: { weekday: 'long' }, // day-of-week is all we need. full date is probably in headerToolbar\n        },\n        listWeek: {\n            type: 'list',\n            duration: { weeks: 1 },\n            listDayFormat: { weekday: 'long' },\n            listDaySideFormat: { month: 'long', day: 'numeric', year: 'numeric' },\n        },\n        listMonth: {\n            type: 'list',\n            duration: { month: 1 },\n            listDaySideFormat: { weekday: 'long' }, // day-of-week is nice-to-have\n        },\n        listYear: {\n            type: 'list',\n            duration: { year: 1 },\n            listDaySideFormat: { weekday: 'long' }, // day-of-week is nice-to-have\n        },\n    },\n});\n\nexport { index as default };\n"], "mappings": "AAAA,SAASA,YAAY,QAAQ,6BAA6B;AAC1D,SAASC,QAAQ,QAAQ,eAAe;AACxC,SAASC,QAAQ,EAAEC,eAAe,QAAQ,gCAAgC;AAC1E,OAAO,8BAA8B;AAErC,MAAMC,eAAe,GAAG;EACpBC,aAAa,EAAEC,uBAAuB;EACtCC,iBAAiB,EAAED,uBAAuB;EAC1CE,kBAAkB,EAAEN,QAAQ;EAC5BO,eAAe,EAAEP,QAAQ;EACzBQ,gBAAgB,EAAER,QAAQ;EAC1BS,mBAAmB,EAAET;EACrB;AACJ,CAAC;AACD,SAASI,uBAAuBA,CAACM,KAAK,EAAE;EACpC,OAAOA,KAAK,KAAK,KAAK,GAAG,IAAI,GAAGT,eAAe,CAACS,KAAK,CAAC;AAC1D;AAEA,IAAIC,KAAK,GAAGb,YAAY,CAAC;EACrBc,IAAI,EAAE,oBAAoB;EAC1BC,cAAc,EAAEX,eAAe;EAC/BY,KAAK,EAAE;IACHC,IAAI,EAAE;MACFC,SAAS,EAAEjB,QAAQ;MACnBkB,aAAa,EAAE,MAAM;MACrBd,aAAa,EAAE;QAAEe,KAAK,EAAE,MAAM;QAAEC,GAAG,EAAE,SAAS;QAAEC,IAAI,EAAE;MAAU,CAAC,CAAE;IACvE,CAAC;IACDC,OAAO,EAAE;MACLC,IAAI,EAAE,MAAM;MACZC,QAAQ,EAAE;QAAEC,IAAI,EAAE;MAAE,CAAC;MACrBrB,aAAa,EAAE;QAAEsB,OAAO,EAAE;MAAO,CAAC,CAAE;IACxC,CAAC;IACDC,QAAQ,EAAE;MACNJ,IAAI,EAAE,MAAM;MACZC,QAAQ,EAAE;QAAEI,KAAK,EAAE;MAAE,CAAC;MACtBxB,aAAa,EAAE;QAAEsB,OAAO,EAAE;MAAO,CAAC;MAClCpB,iBAAiB,EAAE;QAAEa,KAAK,EAAE,MAAM;QAAEC,GAAG,EAAE,SAAS;QAAEC,IAAI,EAAE;MAAU;IACxE,CAAC;IACDQ,SAAS,EAAE;MACPN,IAAI,EAAE,MAAM;MACZC,QAAQ,EAAE;QAAEL,KAAK,EAAE;MAAE,CAAC;MACtBb,iBAAiB,EAAE;QAAEoB,OAAO,EAAE;MAAO,CAAC,CAAE;IAC5C,CAAC;IACDI,QAAQ,EAAE;MACNP,IAAI,EAAE,MAAM;MACZC,QAAQ,EAAE;QAAEH,IAAI,EAAE;MAAE,CAAC;MACrBf,iBAAiB,EAAE;QAAEoB,OAAO,EAAE;MAAO,CAAC,CAAE;IAC5C;EACJ;AACJ,CAAC,CAAC;AAEF,SAASd,KAAK,IAAImB,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}