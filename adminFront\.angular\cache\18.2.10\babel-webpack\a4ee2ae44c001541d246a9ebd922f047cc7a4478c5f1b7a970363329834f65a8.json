{"ast": null, "code": "//! moment.js locale configuration\n//! locale : Montenegrin [me]\n//! author : <PERSON><PERSON><PERSON> <<EMAIL>> : https://github.com/miodragnikac\n\n;\n(function (global, factory) {\n  typeof exports === 'object' && typeof module !== 'undefined' && typeof require === 'function' ? factory(require('../moment')) : typeof define === 'function' && define.amd ? define(['../moment'], factory) : factory(global.moment);\n})(this, function (moment) {\n  'use strict';\n\n  //! moment.js locale configuration\n  var translator = {\n    words: {\n      //Different grammatical cases\n      ss: ['sekund', 'sekunda', 'sekundi'],\n      m: ['jedan minut', 'jednog minuta'],\n      mm: ['minut', 'minuta', 'minuta'],\n      h: ['jedan sat', 'jednog sata'],\n      hh: ['sat', 'sata', 'sati'],\n      dd: ['dan', 'dana', 'dana'],\n      MM: ['mjesec', 'mjeseca', 'mjeseci'],\n      yy: ['godina', 'godine', 'godina']\n    },\n    correctGrammaticalCase: function (number, wordKey) {\n      return number === 1 ? wordKey[0] : number >= 2 && number <= 4 ? wordKey[1] : wordKey[2];\n    },\n    translate: function (number, withoutSuffix, key) {\n      var wordKey = translator.words[key];\n      if (key.length === 1) {\n        return withoutSuffix ? wordKey[0] : wordKey[1];\n      } else {\n        return number + ' ' + translator.correctGrammaticalCase(number, wordKey);\n      }\n    }\n  };\n  var me = moment.defineLocale('me', {\n    months: 'januar_februar_mart_april_maj_jun_jul_avgust_septembar_oktobar_novembar_decembar'.split('_'),\n    monthsShort: 'jan._feb._mar._apr._maj_jun_jul_avg._sep._okt._nov._dec.'.split('_'),\n    monthsParseExact: true,\n    weekdays: 'nedjelja_ponedjeljak_utorak_srijeda_četvrtak_petak_subota'.split('_'),\n    weekdaysShort: 'ned._pon._uto._sri._čet._pet._sub.'.split('_'),\n    weekdaysMin: 'ne_po_ut_sr_če_pe_su'.split('_'),\n    weekdaysParseExact: true,\n    longDateFormat: {\n      LT: 'H:mm',\n      LTS: 'H:mm:ss',\n      L: 'DD.MM.YYYY',\n      LL: 'D. MMMM YYYY',\n      LLL: 'D. MMMM YYYY H:mm',\n      LLLL: 'dddd, D. MMMM YYYY H:mm'\n    },\n    calendar: {\n      sameDay: '[danas u] LT',\n      nextDay: '[sjutra u] LT',\n      nextWeek: function () {\n        switch (this.day()) {\n          case 0:\n            return '[u] [nedjelju] [u] LT';\n          case 3:\n            return '[u] [srijedu] [u] LT';\n          case 6:\n            return '[u] [subotu] [u] LT';\n          case 1:\n          case 2:\n          case 4:\n          case 5:\n            return '[u] dddd [u] LT';\n        }\n      },\n      lastDay: '[juče u] LT',\n      lastWeek: function () {\n        var lastWeekDays = ['[prošle] [nedjelje] [u] LT', '[prošlog] [ponedjeljka] [u] LT', '[prošlog] [utorka] [u] LT', '[prošle] [srijede] [u] LT', '[prošlog] [četvrtka] [u] LT', '[prošlog] [petka] [u] LT', '[prošle] [subote] [u] LT'];\n        return lastWeekDays[this.day()];\n      },\n      sameElse: 'L'\n    },\n    relativeTime: {\n      future: 'za %s',\n      past: 'prije %s',\n      s: 'nekoliko sekundi',\n      ss: translator.translate,\n      m: translator.translate,\n      mm: translator.translate,\n      h: translator.translate,\n      hh: translator.translate,\n      d: 'dan',\n      dd: translator.translate,\n      M: 'mjesec',\n      MM: translator.translate,\n      y: 'godinu',\n      yy: translator.translate\n    },\n    dayOfMonthOrdinalParse: /\\d{1,2}\\./,\n    ordinal: '%d.',\n    week: {\n      dow: 1,\n      // Monday is the first day of the week.\n      doy: 7 // The week that contains Jan 7th is the first week of the year.\n    }\n  });\n  return me;\n});", "map": {"version": 3, "names": ["global", "factory", "exports", "module", "require", "define", "amd", "moment", "translator", "words", "ss", "m", "mm", "h", "hh", "dd", "MM", "yy", "correctGrammaticalCase", "number", "wordKey", "translate", "withoutSuffix", "key", "length", "me", "defineLocale", "months", "split", "monthsShort", "monthsParseExact", "weekdays", "weekdaysShort", "weekdaysMin", "weekdaysParseExact", "longDateFormat", "LT", "LTS", "L", "LL", "LLL", "LLLL", "calendar", "sameDay", "nextDay", "nextWeek", "day", "lastDay", "lastWeek", "lastWeekDays", "same<PERSON><PERSON><PERSON>", "relativeTime", "future", "past", "s", "d", "M", "y", "dayOfMonthOrdinalParse", "ordinal", "week", "dow", "doy"], "sources": ["C:/Users/<USER>/Documents/jeansalechange/adminFront/node_modules/moment/locale/me.js"], "sourcesContent": ["//! moment.js locale configuration\n//! locale : Montenegrin [me]\n//! author : <PERSON><PERSON><PERSON> <<EMAIL>> : https://github.com/miodragnikac\n\n;(function (global, factory) {\n   typeof exports === 'object' && typeof module !== 'undefined'\n       && typeof require === 'function' ? factory(require('../moment')) :\n   typeof define === 'function' && define.amd ? define(['../moment'], factory) :\n   factory(global.moment)\n}(this, (function (moment) { 'use strict';\n\n    //! moment.js locale configuration\n\n    var translator = {\n        words: {\n            //Different grammatical cases\n            ss: ['sekund', 'sekunda', 'sekundi'],\n            m: ['jedan minut', 'jednog minuta'],\n            mm: ['minut', 'minuta', 'minuta'],\n            h: ['jedan sat', 'jednog sata'],\n            hh: ['sat', 'sata', 'sati'],\n            dd: ['dan', 'dana', 'dana'],\n            MM: ['mjesec', 'mjeseca', 'mjeseci'],\n            yy: ['godina', 'godine', 'godina'],\n        },\n        correctGrammaticalCase: function (number, wordKey) {\n            return number === 1\n                ? wordKey[0]\n                : number >= 2 && number <= 4\n                ? wordKey[1]\n                : wordKey[2];\n        },\n        translate: function (number, withoutSuffix, key) {\n            var wordKey = translator.words[key];\n            if (key.length === 1) {\n                return withoutSuffix ? wordKey[0] : wordKey[1];\n            } else {\n                return (\n                    number +\n                    ' ' +\n                    translator.correctGrammaticalCase(number, wordKey)\n                );\n            }\n        },\n    };\n\n    var me = moment.defineLocale('me', {\n        months: 'januar_februar_mart_april_maj_jun_jul_avgust_septembar_oktobar_novembar_decembar'.split(\n            '_'\n        ),\n        monthsShort:\n            'jan._feb._mar._apr._maj_jun_jul_avg._sep._okt._nov._dec.'.split('_'),\n        monthsParseExact: true,\n        weekdays: 'nedjelja_ponedjeljak_utorak_srijeda_četvrtak_petak_subota'.split(\n            '_'\n        ),\n        weekdaysShort: 'ned._pon._uto._sri._čet._pet._sub.'.split('_'),\n        weekdaysMin: 'ne_po_ut_sr_če_pe_su'.split('_'),\n        weekdaysParseExact: true,\n        longDateFormat: {\n            LT: 'H:mm',\n            LTS: 'H:mm:ss',\n            L: 'DD.MM.YYYY',\n            LL: 'D. MMMM YYYY',\n            LLL: 'D. MMMM YYYY H:mm',\n            LLLL: 'dddd, D. MMMM YYYY H:mm',\n        },\n        calendar: {\n            sameDay: '[danas u] LT',\n            nextDay: '[sjutra u] LT',\n\n            nextWeek: function () {\n                switch (this.day()) {\n                    case 0:\n                        return '[u] [nedjelju] [u] LT';\n                    case 3:\n                        return '[u] [srijedu] [u] LT';\n                    case 6:\n                        return '[u] [subotu] [u] LT';\n                    case 1:\n                    case 2:\n                    case 4:\n                    case 5:\n                        return '[u] dddd [u] LT';\n                }\n            },\n            lastDay: '[juče u] LT',\n            lastWeek: function () {\n                var lastWeekDays = [\n                    '[prošle] [nedjelje] [u] LT',\n                    '[prošlog] [ponedjeljka] [u] LT',\n                    '[prošlog] [utorka] [u] LT',\n                    '[prošle] [srijede] [u] LT',\n                    '[prošlog] [četvrtka] [u] LT',\n                    '[prošlog] [petka] [u] LT',\n                    '[prošle] [subote] [u] LT',\n                ];\n                return lastWeekDays[this.day()];\n            },\n            sameElse: 'L',\n        },\n        relativeTime: {\n            future: 'za %s',\n            past: 'prije %s',\n            s: 'nekoliko sekundi',\n            ss: translator.translate,\n            m: translator.translate,\n            mm: translator.translate,\n            h: translator.translate,\n            hh: translator.translate,\n            d: 'dan',\n            dd: translator.translate,\n            M: 'mjesec',\n            MM: translator.translate,\n            y: 'godinu',\n            yy: translator.translate,\n        },\n        dayOfMonthOrdinalParse: /\\d{1,2}\\./,\n        ordinal: '%d.',\n        week: {\n            dow: 1, // Monday is the first day of the week.\n            doy: 7, // The week that contains Jan 7th is the first week of the year.\n        },\n    });\n\n    return me;\n\n})));\n"], "mappings": "AAAA;AACA;AACA;;AAEA;AAAE,WAAUA,MAAM,EAAEC,OAAO,EAAE;EAC1B,OAAOC,OAAO,KAAK,QAAQ,IAAI,OAAOC,MAAM,KAAK,WAAW,IACrD,OAAOC,OAAO,KAAK,UAAU,GAAGH,OAAO,CAACG,OAAO,CAAC,WAAW,CAAC,CAAC,GACpE,OAAOC,MAAM,KAAK,UAAU,IAAIA,MAAM,CAACC,GAAG,GAAGD,MAAM,CAAC,CAAC,WAAW,CAAC,EAAEJ,OAAO,CAAC,GAC3EA,OAAO,CAACD,MAAM,CAACO,MAAM,CAAC;AACzB,CAAC,EAAC,IAAI,EAAG,UAAUA,MAAM,EAAE;EAAE,YAAY;;EAErC;EAEA,IAAIC,UAAU,GAAG;IACbC,KAAK,EAAE;MACH;MACAC,EAAE,EAAE,CAAC,QAAQ,EAAE,SAAS,EAAE,SAAS,CAAC;MACpCC,CAAC,EAAE,CAAC,aAAa,EAAE,eAAe,CAAC;MACnCC,EAAE,EAAE,CAAC,OAAO,EAAE,QAAQ,EAAE,QAAQ,CAAC;MACjCC,CAAC,EAAE,CAAC,WAAW,EAAE,aAAa,CAAC;MAC/BC,EAAE,EAAE,CAAC,KAAK,EAAE,MAAM,EAAE,MAAM,CAAC;MAC3BC,EAAE,EAAE,CAAC,KAAK,EAAE,MAAM,EAAE,MAAM,CAAC;MAC3BC,EAAE,EAAE,CAAC,QAAQ,EAAE,SAAS,EAAE,SAAS,CAAC;MACpCC,EAAE,EAAE,CAAC,QAAQ,EAAE,QAAQ,EAAE,QAAQ;IACrC,CAAC;IACDC,sBAAsB,EAAE,SAAAA,CAAUC,MAAM,EAAEC,OAAO,EAAE;MAC/C,OAAOD,MAAM,KAAK,CAAC,GACbC,OAAO,CAAC,CAAC,CAAC,GACVD,MAAM,IAAI,CAAC,IAAIA,MAAM,IAAI,CAAC,GAC1BC,OAAO,CAAC,CAAC,CAAC,GACVA,OAAO,CAAC,CAAC,CAAC;IACpB,CAAC;IACDC,SAAS,EAAE,SAAAA,CAAUF,MAAM,EAAEG,aAAa,EAAEC,GAAG,EAAE;MAC7C,IAAIH,OAAO,GAAGZ,UAAU,CAACC,KAAK,CAACc,GAAG,CAAC;MACnC,IAAIA,GAAG,CAACC,MAAM,KAAK,CAAC,EAAE;QAClB,OAAOF,aAAa,GAAGF,OAAO,CAAC,CAAC,CAAC,GAAGA,OAAO,CAAC,CAAC,CAAC;MAClD,CAAC,MAAM;QACH,OACID,MAAM,GACN,GAAG,GACHX,UAAU,CAACU,sBAAsB,CAACC,MAAM,EAAEC,OAAO,CAAC;MAE1D;IACJ;EACJ,CAAC;EAED,IAAIK,EAAE,GAAGlB,MAAM,CAACmB,YAAY,CAAC,IAAI,EAAE;IAC/BC,MAAM,EAAE,kFAAkF,CAACC,KAAK,CAC5F,GACJ,CAAC;IACDC,WAAW,EACP,0DAA0D,CAACD,KAAK,CAAC,GAAG,CAAC;IACzEE,gBAAgB,EAAE,IAAI;IACtBC,QAAQ,EAAE,2DAA2D,CAACH,KAAK,CACvE,GACJ,CAAC;IACDI,aAAa,EAAE,oCAAoC,CAACJ,KAAK,CAAC,GAAG,CAAC;IAC9DK,WAAW,EAAE,sBAAsB,CAACL,KAAK,CAAC,GAAG,CAAC;IAC9CM,kBAAkB,EAAE,IAAI;IACxBC,cAAc,EAAE;MACZC,EAAE,EAAE,MAAM;MACVC,GAAG,EAAE,SAAS;MACdC,CAAC,EAAE,YAAY;MACfC,EAAE,EAAE,cAAc;MAClBC,GAAG,EAAE,mBAAmB;MACxBC,IAAI,EAAE;IACV,CAAC;IACDC,QAAQ,EAAE;MACNC,OAAO,EAAE,cAAc;MACvBC,OAAO,EAAE,eAAe;MAExBC,QAAQ,EAAE,SAAAA,CAAA,EAAY;QAClB,QAAQ,IAAI,CAACC,GAAG,CAAC,CAAC;UACd,KAAK,CAAC;YACF,OAAO,uBAAuB;UAClC,KAAK,CAAC;YACF,OAAO,sBAAsB;UACjC,KAAK,CAAC;YACF,OAAO,qBAAqB;UAChC,KAAK,CAAC;UACN,KAAK,CAAC;UACN,KAAK,CAAC;UACN,KAAK,CAAC;YACF,OAAO,iBAAiB;QAChC;MACJ,CAAC;MACDC,OAAO,EAAE,aAAa;MACtBC,QAAQ,EAAE,SAAAA,CAAA,EAAY;QAClB,IAAIC,YAAY,GAAG,CACf,4BAA4B,EAC5B,gCAAgC,EAChC,2BAA2B,EAC3B,2BAA2B,EAC3B,6BAA6B,EAC7B,0BAA0B,EAC1B,0BAA0B,CAC7B;QACD,OAAOA,YAAY,CAAC,IAAI,CAACH,GAAG,CAAC,CAAC,CAAC;MACnC,CAAC;MACDI,QAAQ,EAAE;IACd,CAAC;IACDC,YAAY,EAAE;MACVC,MAAM,EAAE,OAAO;MACfC,IAAI,EAAE,UAAU;MAChBC,CAAC,EAAE,kBAAkB;MACrB5C,EAAE,EAAEF,UAAU,CAACa,SAAS;MACxBV,CAAC,EAAEH,UAAU,CAACa,SAAS;MACvBT,EAAE,EAAEJ,UAAU,CAACa,SAAS;MACxBR,CAAC,EAAEL,UAAU,CAACa,SAAS;MACvBP,EAAE,EAAEN,UAAU,CAACa,SAAS;MACxBkC,CAAC,EAAE,KAAK;MACRxC,EAAE,EAAEP,UAAU,CAACa,SAAS;MACxBmC,CAAC,EAAE,QAAQ;MACXxC,EAAE,EAAER,UAAU,CAACa,SAAS;MACxBoC,CAAC,EAAE,QAAQ;MACXxC,EAAE,EAAET,UAAU,CAACa;IACnB,CAAC;IACDqC,sBAAsB,EAAE,WAAW;IACnCC,OAAO,EAAE,KAAK;IACdC,IAAI,EAAE;MACFC,GAAG,EAAE,CAAC;MAAE;MACRC,GAAG,EAAE,CAAC,CAAE;IACZ;EACJ,CAAC,CAAC;EAEF,OAAOrC,EAAE;AAEb,CAAE,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}