{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { finalize, mergeMap, tap } from 'rxjs';\nimport * as XLSX from 'xlsx';\nimport { SharedModule } from '../../components/shared.module';\nimport { BaseComponent } from '../../components/base/baseComponent';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/shared/helper/allowHelper\";\nimport * as i2 from \"@nebular/theme\";\nimport * as i3 from \"src/app/shared/services/message.service\";\nimport * as i4 from \"src/app/shared/helper/validationHelper\";\nimport * as i5 from \"src/services/api/services\";\nimport * as i6 from \"src/app/shared/services/utility.service\";\nimport * as i7 from \"@angular/common\";\nimport * as i8 from \"@angular/forms\";\nimport * as i9 from \"../../components/breadcrumb/breadcrumb.component\";\nimport * as i10 from \"../../components/pagination/pagination.component\";\nfunction BuildingMaterialComponent_nb_option_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 35);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const buildCase_r2 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", buildCase_r2.cID);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", buildCase_r2.CBuildCaseName, \" \");\n  }\n}\nfunction BuildingMaterialComponent_button_32_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 36);\n    i0.ɵɵlistener(\"click\", function BuildingMaterialComponent_button_32_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.exportExelMaterialList());\n    });\n    i0.ɵɵtext(1, \"\\u532F\\u51FA \");\n    i0.ɵɵelement(2, \"i\", 24);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction BuildingMaterialComponent_button_33_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 37);\n    i0.ɵɵlistener(\"click\", function BuildingMaterialComponent_button_33_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.search());\n    });\n    i0.ɵɵtext(1, \" \\u67E5\\u8A62 \");\n    i0.ɵɵelement(2, \"i\", 38);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction BuildingMaterialComponent_button_34_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 39);\n    i0.ɵɵlistener(\"click\", function BuildingMaterialComponent_button_34_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r3 = i0.ɵɵnextContext();\n      const dialog_r7 = i0.ɵɵreference(68);\n      return i0.ɵɵresetView(ctx_r3.addNew(dialog_r7));\n    });\n    i0.ɵɵtext(1, \"\\u55AE\\u7B46\\u65B0\\u589E \");\n    i0.ɵɵelement(2, \"i\", 40);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction BuildingMaterialComponent_button_35_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 41);\n    i0.ɵɵlistener(\"click\", function BuildingMaterialComponent_button_35_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r8);\n      i0.ɵɵnextContext();\n      const inputFile_r9 = i0.ɵɵreference(37);\n      return i0.ɵɵresetView(inputFile_r9.click());\n    });\n    i0.ɵɵtext(1, \" \\u6279\\u6B21\\u532F\\u5165 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction BuildingMaterialComponent_th_61_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 28);\n    i0.ɵɵtext(1, \"\\u50F9\\u683C\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction BuildingMaterialComponent_tbody_64_tr_1_span_15_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"span\", 48)(1, \"img\", 49);\n    i0.ɵɵlistener(\"click\", function BuildingMaterialComponent_tbody_64_tr_1_span_15_Template_img_click_1_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const item_r11 = i0.ɵɵnextContext().$implicit;\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r3.showImage(item_r11.CPicture, ctx_r3.dialogImage));\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const item_r11 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"src\", item_r11.CPicture, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction BuildingMaterialComponent_tbody_64_tr_1_span_20_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"span\", 48)(1, \"img\", 49);\n    i0.ɵɵlistener(\"click\", function BuildingMaterialComponent_tbody_64_tr_1_span_20_Template_img_click_1_listener() {\n      i0.ɵɵrestoreView(_r12);\n      const item_r11 = i0.ɵɵnextContext().$implicit;\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r3.showImage(item_r11.CInfoPicture, ctx_r3.dialogImage));\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const item_r11 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"src\", item_r11.CInfoPicture, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction BuildingMaterialComponent_tbody_64_tr_1_td_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r11 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(item_r11.CPrice);\n  }\n}\nfunction BuildingMaterialComponent_tbody_64_tr_1_button_25_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r13 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 50);\n    i0.ɵɵlistener(\"click\", function BuildingMaterialComponent_tbody_64_tr_1_button_25_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r13);\n      const item_r11 = i0.ɵɵnextContext().$implicit;\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      const dialog_r7 = i0.ɵɵreference(68);\n      return i0.ɵɵresetView(ctx_r3.onSelectedMaterial(item_r11, dialog_r7));\n    });\n    i0.ɵɵtext(1, \"\\u7DE8\\u8F2F\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction BuildingMaterialComponent_tbody_64_tr_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"td\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"td\");\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"td\")(12, \"div\", 43)(13, \"span\", 44);\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(15, BuildingMaterialComponent_tbody_64_tr_1_span_15_Template, 2, 1, \"span\", 45);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(16, \"td\")(17, \"div\", 43)(18, \"span\", 44);\n    i0.ɵɵtext(19);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(20, BuildingMaterialComponent_tbody_64_tr_1_span_20_Template, 2, 1, \"span\", 45);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(21, \"td\");\n    i0.ɵɵtext(22);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(23, BuildingMaterialComponent_tbody_64_tr_1_td_23_Template, 2, 1, \"td\", 32);\n    i0.ɵɵelementStart(24, \"td\", 46);\n    i0.ɵɵtemplate(25, BuildingMaterialComponent_tbody_64_tr_1_button_25_Template, 2, 0, \"button\", 47);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const item_r11 = ctx.$implicit;\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r11.CId);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r11.CName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r11.CPart);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r11.CLocation);\n    i0.ɵɵadvance();\n    i0.ɵɵstyleMap(!item_r11.CIsMapping ? \"color: red\" : \"\");\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(item_r11.CSelectName);\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassMap(item_r11.CPicture == null ? \"empty-image\" : \"\");\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", item_r11.CImageCode, \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", item_r11.CPicture);\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassMap(item_r11.CInfoPicture == null ? \"empty-image\" : \"\");\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", item_r11.CInfoImageCode, \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", item_r11.CInfoPicture);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r11.CDescription);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", item_r11.CShowPrice == true);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.isRead);\n  }\n}\nfunction BuildingMaterialComponent_tbody_64_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tbody\");\n    i0.ɵɵtemplate(1, BuildingMaterialComponent_tbody_64_tr_1_Template, 26, 18, \"tr\", 42);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.materialList);\n  }\n}\nfunction BuildingMaterialComponent_ng_template_67_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r14 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-card\", 51)(1, \"nb-card-header\");\n    i0.ɵɵtext(2, \" \\u5EFA\\u6750\\u7BA1\\u7406 > \\u65B0\\u589E\\u5EFA\\u6750 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"nb-card-body\", 52)(4, \"h5\", 53);\n    i0.ɵɵtext(5, \"\\u8ACB\\u8F38\\u5165\\u4E0B\\u65B9\\u5167\\u5BB9\\u65B0\\u589E\\u5EFA\\u6750\\uFF0C\\u8ACB\\u7559\\u610F\\u5EFA\\u6750\\u5716\\u7247\\u7DE8\\u865F\\u4E0D\\u53EF\\u8207\\u8A72\\u5EFA\\u6848\\u5167\\u5176\\u4ED6\\u7DE8\\u865F\\u91CD\\u8907\\u3002\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 54)(7, \"div\", 55)(8, \"label\", 56);\n    i0.ɵɵtext(9, \"\\u540D\\u7A31\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"input\", 57);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function BuildingMaterialComponent_ng_template_67_Template_input_ngModelChange_10_listener($event) {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r3 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r3.selectedMaterial.CName, $event) || (ctx_r3.selectedMaterial.CName = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"div\", 58)(12, \"label\", 56);\n    i0.ɵɵtext(13, \"\\u9805\\u76EE\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"input\", 57);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function BuildingMaterialComponent_ng_template_67_Template_input_ngModelChange_14_listener($event) {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r3 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r3.selectedMaterial.CPart, $event) || (ctx_r3.selectedMaterial.CPart = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(15, \"div\", 58)(16, \"label\", 56);\n    i0.ɵɵtext(17, \"\\u4F4D\\u7F6E\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"input\", 57);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function BuildingMaterialComponent_ng_template_67_Template_input_ngModelChange_18_listener($event) {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r3 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r3.selectedMaterial.CLocation, $event) || (ctx_r3.selectedMaterial.CLocation = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(19, \"div\", 58)(20, \"label\", 56);\n    i0.ɵɵtext(21, \"\\u5EFA\\u6750\\u9078\\u9805\\u540D\\u7A31\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"input\", 57);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function BuildingMaterialComponent_ng_template_67_Template_input_ngModelChange_22_listener($event) {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r3 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r3.selectedMaterial.CSelectName, $event) || (ctx_r3.selectedMaterial.CSelectName = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(23, \"div\", 58)(24, \"label\", 56);\n    i0.ɵɵtext(25, \"\\u5EFA\\u6750\\u5716\\u7247\\u6A94\\u540D\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"div\", 59)(27, \"input\", 60);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function BuildingMaterialComponent_ng_template_67_Template_input_ngModelChange_27_listener($event) {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r3 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r3.selectedMaterial.CImageCode, $event) || (ctx_r3.selectedMaterial.CImageCode = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(28, \"button\", 61);\n    i0.ɵɵlistener(\"click\", function BuildingMaterialComponent_ng_template_67_Template_button_click_28_listener() {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r3 = i0.ɵɵnextContext();\n      const imageBinder_r15 = i0.ɵɵreference(70);\n      return i0.ɵɵresetView(ctx_r3.openImageBinder(imageBinder_r15));\n    });\n    i0.ɵɵtext(29, \" \\u7D81\\u5B9A\\u5716\\u7247 \");\n    i0.ɵɵelement(30, \"i\", 62);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(31, \"div\", 58)(32, \"label\", 63);\n    i0.ɵɵtext(33, \"\\u5EFA\\u6750\\u8AAA\\u660E\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(34, \"textarea\", 64);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function BuildingMaterialComponent_ng_template_67_Template_textarea_ngModelChange_34_listener($event) {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r3 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r3.selectedMaterial.CDescription, $event) || (ctx_r3.selectedMaterial.CDescription = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(35, \"div\", 58)(36, \"label\", 63);\n    i0.ɵɵtext(37, \"\\u50F9\\u683C\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(38, \"input\", 65);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function BuildingMaterialComponent_ng_template_67_Template_input_ngModelChange_38_listener($event) {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r3 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r3.selectedMaterial.CPrice, $event) || (ctx_r3.selectedMaterial.CPrice = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(39, \"nb-card-footer\", 33)(40, \"button\", 66);\n    i0.ɵɵlistener(\"click\", function BuildingMaterialComponent_ng_template_67_Template_button_click_40_listener() {\n      const ref_r16 = i0.ɵɵrestoreView(_r14).dialogRef;\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.onClose(ref_r16));\n    });\n    i0.ɵɵtext(41, \"\\u95DC\\u9589\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(42, \"button\", 67);\n    i0.ɵɵlistener(\"click\", function BuildingMaterialComponent_ng_template_67_Template_button_click_42_listener() {\n      const ref_r16 = i0.ɵɵrestoreView(_r14).dialogRef;\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.onSubmit(ref_r16));\n    });\n    i0.ɵɵtext(43, \"\\u5132\\u5B58\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(10);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r3.selectedMaterial.CName);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r3.selectedMaterial.CPart);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r3.selectedMaterial.CLocation);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r3.selectedMaterial.CSelectName);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r3.selectedMaterial.CImageCode);\n    i0.ɵɵadvance(7);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r3.selectedMaterial.CDescription);\n    i0.ɵɵproperty(\"rows\", 4);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r3.selectedMaterial.CPrice);\n  }\n}\nfunction BuildingMaterialComponent_ng_template_69_div_21_i_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 102);\n  }\n}\nfunction BuildingMaterialComponent_ng_template_69_div_21_img_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 103);\n  }\n  if (rf & 2) {\n    const image_r19 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵproperty(\"src\", image_r19.thumbnailUrl, i0.ɵɵsanitizeUrl)(\"alt\", image_r19.name);\n  }\n}\nfunction BuildingMaterialComponent_ng_template_69_div_21_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 104);\n    i0.ɵɵelement(1, \"i\", 105);\n    i0.ɵɵelementStart(2, \"div\", 106);\n    i0.ɵɵtext(3, \"\\u7121\\u9810\\u89BD\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction BuildingMaterialComponent_ng_template_69_div_21_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r18 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 90);\n    i0.ɵɵlistener(\"click\", function BuildingMaterialComponent_ng_template_69_div_21_Template_div_click_0_listener() {\n      const image_r19 = i0.ɵɵrestoreView(_r18).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r3.toggleImageSelection(image_r19));\n    });\n    i0.ɵɵelementStart(1, \"div\", 91)(2, \"div\", 92);\n    i0.ɵɵtemplate(3, BuildingMaterialComponent_ng_template_69_div_21_i_3_Template, 1, 0, \"i\", 93);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"button\", 94);\n    i0.ɵɵlistener(\"click\", function BuildingMaterialComponent_ng_template_69_div_21_Template_button_click_4_listener($event) {\n      const image_r19 = i0.ɵɵrestoreView(_r18).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      const imagePreview_r20 = i0.ɵɵreference(72);\n      return i0.ɵɵresetView(ctx_r3.previewImage(image_r19, imagePreview_r20, $event));\n    });\n    i0.ɵɵelement(5, \"i\", 95);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 96);\n    i0.ɵɵtemplate(7, BuildingMaterialComponent_ng_template_69_div_21_img_7_Template, 1, 2, \"img\", 97)(8, BuildingMaterialComponent_ng_template_69_div_21_div_8_Template, 4, 0, \"div\", 98);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"div\", 99)(10, \"div\", 100);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"div\", 101);\n    i0.ɵɵtext(13);\n    i0.ɵɵpipe(14, \"number\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const image_r19 = ctx.$implicit;\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassMap(ctx_r3.isImageSelected(image_r19) ? \"border-blue-500 bg-blue-50\" : \"border-gray-200 hover:border-gray-400\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassMap(ctx_r3.isImageSelected(image_r19) ? \"border-blue-500 bg-blue-500\" : \"border-gray-300\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.isImageSelected(image_r19));\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", image_r19.thumbnailUrl);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !image_r19.thumbnailUrl);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"title\", image_r19.name);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(image_r19.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", i0.ɵɵpipeBind1(14, 10, image_r19.size), \" KB\");\n  }\n}\nfunction BuildingMaterialComponent_ng_template_69_div_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 107);\n    i0.ɵɵelement(1, \"i\", 108);\n    i0.ɵɵelementStart(2, \"div\");\n    i0.ɵɵtext(3, \"\\u627E\\u4E0D\\u5230\\u76F8\\u7B26\\u7684\\u5716\\u7247\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction BuildingMaterialComponent_ng_template_69_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r17 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-card\", 68)(1, \"nb-card-header\");\n    i0.ɵɵtext(2, \" \\u5716\\u7247\\u7D81\\u5B9A - \\u9078\\u64C7\\u5EFA\\u6750\\u5716\\u7247 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"nb-card-body\", 69)(4, \"div\", 70)(5, \"div\", 71)(6, \"input\", 72);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function BuildingMaterialComponent_ng_template_69_Template_input_ngModelChange_6_listener($event) {\n      i0.ɵɵrestoreView(_r17);\n      const ctx_r3 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r3.imageSearchTerm, $event) || (ctx_r3.imageSearchTerm = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"input\", function BuildingMaterialComponent_ng_template_69_Template_input_input_6_listener() {\n      i0.ɵɵrestoreView(_r17);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.filterImages());\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"button\", 73);\n    i0.ɵɵlistener(\"click\", function BuildingMaterialComponent_ng_template_69_Template_button_click_7_listener() {\n      i0.ɵɵrestoreView(_r17);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.loadImages());\n    });\n    i0.ɵɵtext(8, \" \\u91CD\\u65B0\\u8F09\\u5165 \");\n    i0.ɵɵelement(9, \"i\", 74);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"div\", 75)(11, \"button\", 76);\n    i0.ɵɵlistener(\"click\", function BuildingMaterialComponent_ng_template_69_Template_button_click_11_listener() {\n      i0.ɵɵrestoreView(_r17);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.selectAllImages());\n    });\n    i0.ɵɵtext(12, \" \\u5168\\u9078 \");\n    i0.ɵɵelement(13, \"i\", 77);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"button\", 78);\n    i0.ɵɵlistener(\"click\", function BuildingMaterialComponent_ng_template_69_Template_button_click_14_listener() {\n      i0.ɵɵrestoreView(_r17);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.clearAllSelection());\n    });\n    i0.ɵɵtext(15, \" \\u6E05\\u9664\\u9078\\u53D6 \");\n    i0.ɵɵelement(16, \"i\", 79);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"div\", 80);\n    i0.ɵɵtext(18);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(19, \"div\", 81)(20, \"div\", 82);\n    i0.ɵɵtemplate(21, BuildingMaterialComponent_ng_template_69_div_21_Template, 15, 12, \"div\", 83);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(22, BuildingMaterialComponent_ng_template_69_div_22_Template, 4, 0, \"div\", 84);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(23, \"nb-card-footer\", 85)(24, \"div\", 86);\n    i0.ɵɵtext(25);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"div\", 87)(27, \"button\", 88);\n    i0.ɵɵlistener(\"click\", function BuildingMaterialComponent_ng_template_69_Template_button_click_27_listener() {\n      const ref_r21 = i0.ɵɵrestoreView(_r17).dialogRef;\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.onCloseImageBinder(ref_r21));\n    });\n    i0.ɵɵtext(28, \"\\u53D6\\u6D88\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(29, \"button\", 89);\n    i0.ɵɵlistener(\"click\", function BuildingMaterialComponent_ng_template_69_Template_button_click_29_listener() {\n      const ref_r21 = i0.ɵɵrestoreView(_r17).dialogRef;\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.onConfirmImageSelection(ref_r21));\n    });\n    i0.ɵɵtext(30);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r3.imageSearchTerm);\n    i0.ɵɵadvance(12);\n    i0.ɵɵtextInterpolate1(\" \\u5DF2\\u9078\\u53D6: \", ctx_r3.selectedImages.length, \" \\u5F35\\u5716\\u7247 \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.filteredImages);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.filteredImages.length === 0);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \\u5171 \", ctx_r3.availableImages.length, \" \\u5F35\\u5716\\u7247 \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"disabled\", ctx_r3.selectedImages.length === 0);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \\u78BA\\u5B9A\\u9078\\u64C7 (\", ctx_r3.selectedImages.length, \") \");\n  }\n}\nfunction BuildingMaterialComponent_ng_template_71_img_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 103);\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"src\", ctx_r3.previewingImage.fullUrl, i0.ɵɵsanitizeUrl)(\"alt\", ctx_r3.previewingImage.name);\n  }\n}\nfunction BuildingMaterialComponent_ng_template_71_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 104);\n    i0.ɵɵelement(1, \"i\", 114);\n    i0.ɵɵelementStart(2, \"div\");\n    i0.ɵɵtext(3, \"\\u5716\\u7247\\u8F09\\u5165\\u5931\\u6557\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction BuildingMaterialComponent_ng_template_71_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r22 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-card\", 109)(1, \"nb-card-header\", 85)(2, \"span\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 87)(5, \"button\", 110);\n    i0.ɵɵlistener(\"click\", function BuildingMaterialComponent_ng_template_71_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r22);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.previousImage());\n    });\n    i0.ɵɵelement(6, \"i\", 111);\n    i0.ɵɵtext(7, \" \\u4E0A\\u4E00\\u5F35 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"button\", 110);\n    i0.ɵɵlistener(\"click\", function BuildingMaterialComponent_ng_template_71_Template_button_click_8_listener() {\n      i0.ɵɵrestoreView(_r22);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.nextImage());\n    });\n    i0.ɵɵtext(9, \" \\u4E0B\\u4E00\\u5F35 \");\n    i0.ɵɵelement(10, \"i\", 112);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(11, \"nb-card-body\", 113);\n    i0.ɵɵtemplate(12, BuildingMaterialComponent_ng_template_71_img_12_Template, 1, 2, \"img\", 97)(13, BuildingMaterialComponent_ng_template_71_div_13_Template, 4, 0, \"div\", 98);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"nb-card-footer\", 85)(15, \"div\", 86);\n    i0.ɵɵtext(16);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"div\", 87)(18, \"button\", 61);\n    i0.ɵɵlistener(\"click\", function BuildingMaterialComponent_ng_template_71_Template_button_click_18_listener() {\n      i0.ɵɵrestoreView(_r22);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.toggleImageSelectionInPreview());\n    });\n    i0.ɵɵtext(19);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"button\", 88);\n    i0.ɵɵlistener(\"click\", function BuildingMaterialComponent_ng_template_71_Template_button_click_20_listener() {\n      const ref_r23 = i0.ɵɵrestoreView(_r22).dialogRef;\n      return i0.ɵɵresetView(ref_r23.close());\n    });\n    i0.ɵɵtext(21, \"\\u95DC\\u9589\");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\\u5716\\u7247\\u9810\\u89BD - \", ctx_r3.previewingImage == null ? null : ctx_r3.previewingImage.name, \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", ctx_r3.currentPreviewIndex <= 0);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"disabled\", ctx_r3.currentPreviewIndex >= ctx_r3.filteredImages.length - 1);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.previewingImage == null ? null : ctx_r3.previewingImage.fullUrl);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !(ctx_r3.previewingImage == null ? null : ctx_r3.previewingImage.fullUrl));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate2(\" \", ctx_r3.currentPreviewIndex + 1, \" / \", ctx_r3.filteredImages.length, \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.isImageSelected(ctx_r3.previewingImage) ? \"\\u53D6\\u6D88\\u9078\\u53D6\" : \"\\u9078\\u53D6\\u6B64\\u5716\\u7247\", \" \");\n  }\n}\nexport class BuildingMaterialComponent extends BaseComponent {\n  constructor(_allow, dialogService, message, valid, _buildCaseService, _materialService, _utilityService) {\n    super(_allow);\n    this._allow = _allow;\n    this.dialogService = dialogService;\n    this.message = message;\n    this.valid = valid;\n    this._buildCaseService = _buildCaseService;\n    this._materialService = _materialService;\n    this._utilityService = _utilityService;\n    this.isNew = true;\n    this.listBuildCases = [];\n    this.materialOptions = [{\n      value: null,\n      label: '全部'\n    }, {\n      value: false,\n      label: '方案'\n    }, {\n      value: true,\n      label: '選樣'\n    }];\n    this.materialOptionsId = null;\n    this.CSelectName = \"\";\n    this.CImageCode = \"\";\n    this.CInfoImageCode = \"\";\n    this.ShowPrice = false;\n    this.currentImageShowing = \"\";\n    this.filterMapping = false;\n    this.CIsMapping = true;\n    // 圖片綁定相關屬性\n    this.availableImages = [];\n    this.filteredImages = [];\n    this.selectedImages = [];\n    this.imageSearchTerm = \"\";\n    this.previewingImage = null;\n    this.currentPreviewIndex = 0;\n  }\n  ngOnInit() {\n    this.getListBuildCase();\n  }\n  getListBuildCase() {\n    this._buildCaseService.apiBuildCaseGetAllBuildCaseForSelectPost$Json({\n      body: {\n        CIsPagi: false,\n        CStatus: 1\n      }\n    }).pipe(tap(res => {\n      if (res.StatusCode == 0) {\n        this.listBuildCases = res.Entries?.length ? res.Entries : [];\n        this.selectedBuildCaseId = this.listBuildCases[0].cID;\n      }\n    }), mergeMap(() => this.getMaterialList())).subscribe();\n  }\n  getMaterialList(pageIndex = 1) {\n    return this._materialService.apiMaterialGetMaterialListPost$Json({\n      body: {\n        CBuildCaseId: this.selectedBuildCaseId,\n        CPlanUse: this.materialOptionsId,\n        CSelectName: this.CSelectName,\n        CImageCode: this.CImageCode,\n        PageSize: this.pageSize,\n        PageIndex: pageIndex,\n        CInfoImageCode: this.CInfoImageCode,\n        CIsMapping: this.CIsMapping\n      }\n    }).pipe(tap(res => {\n      if (res.StatusCode == 0) {\n        this.materialList = res.Entries ?? [];\n        this.totalRecords = res.TotalItems;\n        if (this.materialList.length > 0) {\n          this.ShowPrice = this.materialList[0].CShowPrice;\n        }\n      }\n    }));\n  }\n  search() {\n    this.getMaterialList().subscribe();\n  }\n  pageChanged(pageIndex) {\n    this.getMaterialList(pageIndex).subscribe();\n  }\n  exportExelMaterialList() {\n    this._materialService.apiMaterialExportExcelMaterialListPost$Json({\n      body: this.selectedBuildCaseId\n    }).subscribe(res => {\n      if (res.StatusCode == 0) {\n        if (res.Entries.FileByte) {\n          this._utilityService.downloadExcelFile(res.Entries.FileByte, '建材管理');\n        }\n      }\n    });\n  }\n  exportExelMaterialTemplate() {\n    this._materialService.apiMaterialExportExcelMaterialTemplatePost$Json({\n      body: this.selectedBuildCaseId\n    }).subscribe(res => {\n      if (res.StatusCode == 0) {\n        if (res.Entries.FileByte) {\n          this._utilityService.downloadExcelFile(res.Entries.FileByte, '建材管理');\n        }\n      }\n    });\n  }\n  addNew(ref) {\n    this.isNew = true;\n    this.selectedMaterial = {};\n    this.dialogService.open(ref);\n  }\n  onSelectedMaterial(data, ref) {\n    this.isNew = false;\n    this.selectedMaterial = {\n      ...data\n    };\n    this.dialogService.open(ref);\n  }\n  validation() {\n    this.valid.clear();\n    this.valid.required('[名稱]', this.selectedMaterial.CName);\n    this.valid.required('[項目]', this.selectedMaterial.CPart);\n    this.valid.required('[位置]', this.selectedMaterial.CLocation);\n    this.valid.required('[建材選項名稱]', this.selectedMaterial.CSelectName);\n    this.valid.required('[建材圖片編號]', this.selectedMaterial.CImageCode);\n    this.valid.isStringMaxLength('[名稱]', this.selectedMaterial.CName, 30);\n    this.valid.isStringMaxLength('[項目]', this.selectedMaterial.CPart, 30);\n    this.valid.isStringMaxLength('[位置]', this.selectedMaterial.CLocation, 30);\n    this.valid.isStringMaxLength('[建材選項名稱]', this.selectedMaterial.CSelectName, 30);\n    this.valid.isStringMaxLength('[建材圖片編號]', this.selectedMaterial.CImageCode, 30);\n  }\n  onSubmit(ref) {\n    this.validation();\n    if (this.valid.errorMessages.length > 0) {\n      this.message.showErrorMSGs(this.valid.errorMessages);\n      return;\n    }\n    this._materialService.apiMaterialSaveMaterialAdminPost$Json({\n      body: {\n        CBuildCaseId: this.selectedBuildCaseId,\n        CImageCode: this.selectedMaterial.CImageCode,\n        CName: this.selectedMaterial.CName,\n        CPart: this.selectedMaterial.CPart,\n        CLocation: this.selectedMaterial.CLocation,\n        CSelectName: this.selectedMaterial.CSelectName,\n        CDescription: this.selectedMaterial.CDescription,\n        CMaterialId: this.isNew ? null : this.selectedMaterial.CId,\n        CPrice: this.selectedMaterial.CPrice\n      }\n    }).pipe(tap(res => {\n      if (res.StatusCode === 0) {\n        this.message.showSucessMSG(\"執行成功\");\n      } else {\n        this.message.showErrorMSG(res.Message);\n      }\n    }), mergeMap(() => this.getMaterialList()), finalize(() => ref.close())).subscribe();\n  }\n  onClose(ref) {\n    ref.close();\n  }\n  detectFileExcel(event) {\n    const target = event.target;\n    const reader = new FileReader();\n    reader.readAsBinaryString(target.files[0]);\n    reader.onload = e => {\n      const binarystr = e.target.result;\n      const wb = XLSX.read(binarystr, {\n        type: 'binary'\n      });\n      const wsname = wb.SheetNames[0];\n      const ws = wb.Sheets[wsname];\n      let isValidFile = true;\n      const data = XLSX.utils.sheet_to_json(ws);\n      if (data && data.length > 0) {\n        data.forEach(x => {\n          if (!(x['名稱'] && x['項目'] && x['位置'] && x['建材選項名稱'] && (x['建材圖片檔名'] || x['建材說明'] || x['示意圖片檔名']))) {\n            isValidFile = false;\n          }\n        });\n        if (!isValidFile) {\n          this.message.showErrorMSG(\"导入文件时出现错误\");\n        } else {\n          this._materialService.apiMaterialImportExcelMaterialListPost$Json({\n            body: {\n              CBuildCaseId: this.selectedBuildCaseId,\n              CFile: target.files[0]\n            }\n          }).pipe(tap(res => {\n            if (res.StatusCode == 0) {\n              this.message.showSucessMSG(\"執行成功\");\n            } else {\n              this.message.showErrorMSG(res.Message);\n            }\n          }), mergeMap(() => this.getMaterialList(1))).subscribe();\n        }\n      } else {\n        this.message.showErrorMSG(\"匯入的檔案內容為空，請檢查檔案並重新上傳。\");\n      }\n      event.target.value = null;\n    };\n  }\n  showImage(imageUrl, dialog) {\n    this.currentImageShowing = imageUrl;\n    this.dialogService.open(dialog);\n  }\n  changeFilter() {\n    if (this.filterMapping) {\n      this.CIsMapping = false;\n      this.getMaterialList().subscribe();\n    } else {\n      this.CIsMapping = true;\n      this.getMaterialList().subscribe();\n    }\n  }\n  // 圖片綁定功能方法\n  openImageBinder(ref) {\n    this.loadImages();\n    this.dialogService.open(ref, {\n      closeOnBackdropClick: false\n    });\n  }\n  loadImages() {\n    // 模擬載入圖片資料，實際應該從 API 取得\n    // 這裡先用假資料示範\n    this.availableImages = [{\n      id: '1',\n      name: 'floor_tile_001.jpg',\n      size: 245,\n      thumbnailUrl: 'assets/images/materials/thumbs/floor_tile_001.jpg',\n      fullUrl: 'assets/images/materials/floor_tile_001.jpg',\n      lastModified: new Date()\n    }, {\n      id: '2',\n      name: 'wall_paint_002.jpg',\n      size: 189,\n      thumbnailUrl: 'assets/images/materials/thumbs/wall_paint_002.jpg',\n      fullUrl: 'assets/images/materials/wall_paint_002.jpg',\n      lastModified: new Date()\n    }, {\n      id: '3',\n      name: 'cabinet_wood_003.jpg',\n      size: 312,\n      thumbnailUrl: 'assets/images/materials/thumbs/cabinet_wood_003.jpg',\n      fullUrl: 'assets/images/materials/cabinet_wood_003.jpg',\n      lastModified: new Date()\n    }, {\n      id: '4',\n      name: 'lighting_fixture_004.jpg',\n      size: 156,\n      thumbnailUrl: 'assets/images/materials/thumbs/lighting_fixture_004.jpg',\n      fullUrl: 'assets/images/materials/lighting_fixture_004.jpg',\n      lastModified: new Date()\n    }, {\n      id: '5',\n      name: 'door_handle_005.jpg',\n      size: 78,\n      thumbnailUrl: 'assets/images/materials/thumbs/door_handle_005.jpg',\n      fullUrl: 'assets/images/materials/door_handle_005.jpg',\n      lastModified: new Date()\n    }, {\n      id: '6',\n      name: 'window_frame_006.jpg',\n      size: 267,\n      thumbnailUrl: 'assets/images/materials/thumbs/window_frame_006.jpg',\n      fullUrl: 'assets/images/materials/window_frame_006.jpg',\n      lastModified: new Date()\n    }];\n    this.filteredImages = [...this.availableImages];\n    // TODO: 實際實作時，這裡應該呼叫 API 取得圖片列表\n    // this._materialService.apiMaterialGetImageListPost$Json({\n    //   body: { CBuildCaseId: this.selectedBuildCaseId }\n    // }).subscribe(res => {\n    //   if (res.StatusCode === 0) {\n    //     this.availableImages = res.Entries || [];\n    //     this.filteredImages = [...this.availableImages];\n    //   }\n    // });\n  }\n  filterImages() {\n    if (!this.imageSearchTerm.trim()) {\n      this.filteredImages = [...this.availableImages];\n    } else {\n      const searchTerm = this.imageSearchTerm.toLowerCase();\n      this.filteredImages = this.availableImages.filter(image => image.name.toLowerCase().includes(searchTerm));\n    }\n  }\n  toggleImageSelection(image) {\n    const index = this.selectedImages.findIndex(selected => selected.id === image.id);\n    if (index > -1) {\n      this.selectedImages.splice(index, 1);\n    } else {\n      this.selectedImages.push(image);\n    }\n  }\n  isImageSelected(image) {\n    return this.selectedImages.some(selected => selected.id === image.id);\n  }\n  selectAllImages() {\n    this.selectedImages = [...this.filteredImages];\n  }\n  clearAllSelection() {\n    this.selectedImages = [];\n  }\n  previewImage(image, event) {\n    event.stopPropagation();\n    this.previewingImage = image;\n    this.currentPreviewIndex = this.filteredImages.findIndex(img => img.id === image.id);\n    // 需要在組件中取得預覽對話框的模板引用\n    // 這裡暫時註解掉，需要在 HTML 中正確設定\n    // this.dialogService.open(imagePreviewRef);\n  }\n  previousImage() {\n    if (this.currentPreviewIndex > 0) {\n      this.currentPreviewIndex--;\n      this.previewingImage = this.filteredImages[this.currentPreviewIndex];\n    }\n  }\n  nextImage() {\n    if (this.currentPreviewIndex < this.filteredImages.length - 1) {\n      this.currentPreviewIndex++;\n      this.previewingImage = this.filteredImages[this.currentPreviewIndex];\n    }\n  }\n  toggleImageSelectionInPreview() {\n    if (this.previewingImage) {\n      this.toggleImageSelection(this.previewingImage);\n    }\n  }\n  onConfirmImageSelection(ref) {\n    if (this.selectedImages.length > 0) {\n      // 如果只選取一張圖片，直接設定到建材圖片檔名\n      if (this.selectedImages.length === 1) {\n        this.selectedMaterial.CImageCode = this.selectedImages[0].name;\n      } else {\n        // 多張圖片的話，可以設定第一張，或者讓使用者選擇主要圖片\n        const imageNames = this.selectedImages.map(img => img.name).join(', ');\n        this.message.showSucessMSG(`已選取 ${this.selectedImages.length} 張圖片: ${imageNames}`);\n        this.selectedMaterial.CImageCode = this.selectedImages[0].name;\n      }\n    }\n    this.clearAllSelection();\n    ref.close();\n  }\n  onCloseImageBinder(ref) {\n    this.clearAllSelection();\n    this.imageSearchTerm = \"\";\n    ref.close();\n  }\n  static {\n    this.ɵfac = function BuildingMaterialComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || BuildingMaterialComponent)(i0.ɵɵdirectiveInject(i1.AllowHelper), i0.ɵɵdirectiveInject(i2.NbDialogService), i0.ɵɵdirectiveInject(i3.MessageService), i0.ɵɵdirectiveInject(i4.ValidationHelper), i0.ɵɵdirectiveInject(i5.BuildCaseService), i0.ɵɵdirectiveInject(i5.MaterialService), i0.ɵɵdirectiveInject(i6.UtilityService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: BuildingMaterialComponent,\n      selectors: [[\"ngx-building-material\"]],\n      standalone: true,\n      features: [i0.ɵɵInheritDefinitionFeature, i0.ɵɵStandaloneFeature],\n      decls: 73,\n      vars: 15,\n      consts: [[\"inputFile\", \"\"], [\"dialog\", \"\"], [\"imageBinder\", \"\"], [\"imagePreview\", \"\"], [\"accent\", \"success\"], [1, \"font-bold\", \"text-[#818181]\"], [1, \"d-flex\", \"flex-wrap\"], [1, \"col-md-6\"], [1, \"form-group\", \"d-flex\", \"align-items-center\", \"w-full\"], [\"for\", \"buildingName\", 1, \"label\", \"mr-2\", \"w-[22%]\"], [\"placeholder\", \"\\u5EFA\\u6848\", 1, \"w-full\", 3, \"ngModelChange\", \"ngModel\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"type\", \"text\", \"nbInput\", \"\", \"placeholder\", \"\\u5EFA\\u6750\\u9078\\u9805\\u540D\\u7A31\", 1, \"w-full\", 3, \"ngModelChange\", \"ngModel\"], [\"type\", \"text\", \"nbInput\", \"\", \"placeholder\", \"\\u5EFA\\u6750\\u5716\\u7247\\u6A94\\u540D\", 1, \"w-full\", 3, \"ngModelChange\", \"ngModel\"], [\"type\", \"text\", \"nbInput\", \"\", \"placeholder\", \"\\u793A\\u610F\\u5716\\u7247\\u6A94\\u540D\", 1, \"w-full\", 3, \"ngModelChange\", \"ngModel\"], [1, \"col-md-12\"], [1, \"d-flex\", \"justify-content-end\", \"w-full\"], [\"status\", \"basic\", 1, \"flex\", 2, \"flex\", \"auto\", 3, \"checkedChange\", \"change\", \"checked\"], [\"class\", \"btn btn-success mr-2\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"btn btn-info mr-2 text-white ml-2\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"btn btn-info mx-1 ml-2 mr-2\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"btn btn-info mx-1\", 3, \"click\", 4, \"ngIf\"], [\"type\", \"file\", \"accept\", \".xls, .xlsx\", 1, \"hidden\", 3, \"change\"], [1, \"btn\", \"btn-success\", \"ml-2\", 3, \"click\"], [1, \"fas\", \"fa-file-download\"], [1, \"table-responsive\", \"mt-4\"], [1, \"table\", \"table-striped\", \"border\", 2, \"min-width\", \"1200px\", \"background-color\", \"#f3f3f3\"], [2, \"background-color\", \"#27ae60\", \"color\", \"white\"], [\"scope\", \"col\", 1, \"col-1\"], [\"scope\", \"col\", 1, \"col-3\"], [\"scope\", \"col\", \"class\", \"col-1\", 4, \"ngIf\"], [\"scope\", \"col\", 1, \"col-1\", \"text-center\"], [4, \"ngIf\"], [1, \"d-flex\", \"justify-content-center\"], [3, \"CollectionSizeChange\", \"PageSizeChange\", \"PageChange\", \"CollectionSize\", \"PageSize\", \"Page\"], [3, \"value\"], [1, \"btn\", \"btn-success\", \"mr-2\", 3, \"click\"], [1, \"btn\", \"btn-info\", \"mr-2\", \"text-white\", \"ml-2\", 3, \"click\"], [1, \"fas\", \"fa-search\"], [1, \"btn\", \"btn-info\", \"mx-1\", \"ml-2\", \"mr-2\", 3, \"click\"], [1, \"fas\", \"fa-plus\"], [1, \"btn\", \"btn-info\", \"mx-1\", 3, \"click\"], [4, \"ngFor\", \"ngForOf\"], [1, \"flex\", \"justify-between\"], [2, \"overflow-wrap\", \"break-word\", \"width\", \"80%\"], [\"class\", \"width-[50px]\", 4, \"ngIf\"], [1, \"text-center\", \"w-32\"], [\"class\", \"btn btn-outline-primary btn-sm m-1\", 3, \"click\", 4, \"ngIf\"], [1, \"width-[50px]\"], [1, \"image-table\", 3, \"click\", \"src\"], [1, \"btn\", \"btn-outline-primary\", \"btn-sm\", \"m-1\", 3, \"click\"], [1, \"w-[700px]\"], [1, \"px-4\"], [1, \"text-base\"], [1, \"w-full\", \"mt-3\"], [1, \"flex\", \"items-center\"], [1, \"required-field\", \"w-[150px]\"], [\"type\", \"text\", \"nbInput\", \"\", \"maxlength\", \"30\", 1, \"w-full\", \"!max-w-full\", \"p-2\", \"rounded\", \"text-[13px]\", 3, \"ngModelChange\", \"ngModel\"], [1, \"flex\", \"items-center\", \"mt-3\"], [1, \"flex\", \"w-full\", \"gap-2\"], [\"type\", \"text\", \"nbInput\", \"\", \"maxlength\", \"30\", 1, \"flex-1\", \"!max-w-full\", \"p-2\", \"rounded\", \"text-[13px]\", 3, \"ngModelChange\", \"ngModel\"], [1, \"btn\", \"btn-outline-info\", \"btn-sm\", 3, \"click\"], [1, \"fas\", \"fa-images\"], [1, \"w-[150px]\"], [\"nbInput\", \"\", 1, \"resize-none\", \"w-full\", \"!max-w-full\", \"p-2\", \"rounded\", \"text-[13px]\", 3, \"ngModelChange\", \"ngModel\", \"rows\"], [\"type\", \"number\", \"nbInput\", \"\", \"maxlength\", \"30\", 1, \"w-full\", \"!max-w-full\", \"p-2\", \"rounded\", \"text-[13px]\", 3, \"ngModelChange\", \"ngModel\"], [1, \"btn\", \"btn-danger\", \"btn-sm\", \"mr-4\", 3, \"click\"], [1, \"btn\", \"btn-success\", \"btn-sm\", 3, \"click\"], [1, \"w-[900px]\", \"h-[700px]\"], [1, \"px-4\", \"h-[600px]\", \"overflow-hidden\"], [1, \"flex\", \"gap-3\", \"mb-4\"], [1, \"flex-1\"], [\"type\", \"text\", \"placeholder\", \"\\u641C\\u5C0B\\u5716\\u7247\\u540D\\u7A31...\", 1, \"w-full\", \"p-2\", \"rounded\", \"border\", 3, \"ngModelChange\", \"input\", \"ngModel\"], [1, \"btn\", \"btn-info\", 3, \"click\"], [1, \"fas\", \"fa-refresh\"], [1, \"flex\", \"gap-2\", \"mb-3\"], [1, \"btn\", \"btn-outline-primary\", \"btn-sm\", 3, \"click\"], [1, \"fas\", \"fa-check-square\"], [1, \"btn\", \"btn-outline-secondary\", \"btn-sm\", 3, \"click\"], [1, \"fas\", \"fa-square\"], [1, \"ml-auto\", \"text-sm\", \"text-gray-600\"], [1, \"h-[480px]\", \"overflow-y-auto\", \"border\", \"rounded\", \"p-3\"], [1, \"grid\", \"grid-cols-4\", \"gap-3\"], [\"class\", \"border rounded p-2 cursor-pointer transition-all duration-200\", 3, \"class\", \"click\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"text-center text-gray-500 py-20\", 4, \"ngIf\"], [1, \"d-flex\", \"justify-content-between\", \"align-items-center\"], [1, \"text-sm\", \"text-gray-600\"], [1, \"d-flex\", \"gap-2\"], [1, \"btn\", \"btn-danger\", \"btn-sm\", 3, \"click\"], [1, \"btn\", \"btn-success\", \"btn-sm\", 3, \"click\", \"disabled\"], [1, \"border\", \"rounded\", \"p-2\", \"cursor-pointer\", \"transition-all\", \"duration-200\", 3, \"click\"], [1, \"flex\", \"justify-between\", \"items-center\", \"mb-2\"], [1, \"w-5\", \"h-5\", \"border-2\", \"rounded\", \"flex\", \"items-center\", \"justify-center\"], [\"class\", \"fas fa-check text-white text-xs\", 4, \"ngIf\"], [1, \"btn\", \"btn-outline-info\", \"btn-xs\", 3, \"click\"], [1, \"fas\", \"fa-eye\"], [1, \"w-full\", \"h-32\", \"bg-gray-100\", \"rounded\", \"mb-2\", \"flex\", \"items-center\", \"justify-center\", \"overflow-hidden\"], [\"class\", \"max-w-full max-h-full object-contain\", 3, \"src\", \"alt\", 4, \"ngIf\"], [\"class\", \"text-gray-400 text-center\", 4, \"ngIf\"], [1, \"text-xs\", \"text-gray-600\"], [1, \"font-medium\", \"truncate\", 3, \"title\"], [1, \"text-gray-400\"], [1, \"fas\", \"fa-check\", \"text-white\", \"text-xs\"], [1, \"max-w-full\", \"max-h-full\", \"object-contain\", 3, \"src\", \"alt\"], [1, \"text-gray-400\", \"text-center\"], [1, \"fas\", \"fa-image\", \"text-2xl\", \"mb-1\"], [1, \"text-xs\"], [1, \"text-center\", \"text-gray-500\", \"py-20\"], [1, \"fas\", \"fa-images\", \"text-4xl\", \"mb-3\"], [1, \"w-[800px]\", \"h-[600px]\"], [1, \"btn\", \"btn-outline-primary\", \"btn-sm\", 3, \"click\", \"disabled\"], [1, \"fas\", \"fa-chevron-left\"], [1, \"fas\", \"fa-chevron-right\"], [1, \"p-0\", \"d-flex\", \"justify-content-center\", \"align-items-center\", 2, \"height\", \"500px\"], [1, \"fas\", \"fa-image\", \"text-4xl\", \"mb-3\"]],\n      template: function BuildingMaterialComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"nb-card\", 4)(1, \"nb-card-header\");\n          i0.ɵɵelement(2, \"ngx-breadcrumb\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(3, \"nb-card-body\")(4, \"h1\", 5);\n          i0.ɵɵtext(5, \" \\u53EF\\u8A2D\\u5B9A\\u55AE\\u7B46\\u6216\\u6279\\u6B21\\u532F\\u5165\\u8A2D\\u5B9A\\u5404\\u5340\\u57DF\\u53CA\\u65B9\\u6848\\u5C0D\\u61C9\\u4E4B\\u5EFA\\u6750\\u3002 \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(6, \"div\", 6)(7, \"div\", 7)(8, \"div\", 8)(9, \"label\", 9);\n          i0.ɵɵtext(10, \"\\u5EFA\\u6848\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(11, \"nb-select\", 10);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function BuildingMaterialComponent_Template_nb_select_ngModelChange_11_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.selectedBuildCaseId, $event) || (ctx.selectedBuildCaseId = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵtemplate(12, BuildingMaterialComponent_nb_option_12_Template, 2, 2, \"nb-option\", 11);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(13, \"div\", 7)(14, \"div\", 8)(15, \"label\", 9);\n          i0.ɵɵtext(16, \"\\u5EFA\\u6750\\u9078\\u9805\\u540D\\u7A31 \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(17, \"input\", 12);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function BuildingMaterialComponent_Template_input_ngModelChange_17_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.CSelectName, $event) || (ctx.CSelectName = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(18, \"div\", 7)(19, \"div\", 8)(20, \"label\", 9);\n          i0.ɵɵtext(21, \"\\u5EFA\\u6750\\u5716\\u7247\\u6A94\\u540D \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(22, \"input\", 13);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function BuildingMaterialComponent_Template_input_ngModelChange_22_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.CImageCode, $event) || (ctx.CImageCode = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(23, \"div\", 7)(24, \"div\", 8)(25, \"label\", 9);\n          i0.ɵɵtext(26, \"\\u793A\\u610F\\u5716\\u7247\\u6A94\\u540D \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(27, \"input\", 14);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function BuildingMaterialComponent_Template_input_ngModelChange_27_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.CInfoImageCode, $event) || (ctx.CInfoImageCode = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(28, \"div\", 15)(29, \"div\", 16)(30, \"nb-checkbox\", 17);\n          i0.ɵɵtwoWayListener(\"checkedChange\", function BuildingMaterialComponent_Template_nb_checkbox_checkedChange_30_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.filterMapping, $event) || (ctx.filterMapping = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵlistener(\"change\", function BuildingMaterialComponent_Template_nb_checkbox_change_30_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.changeFilter());\n          });\n          i0.ɵɵtext(31, \" \\u53EA\\u986F\\u793A\\u7F3A\\u5C11\\u5EFA\\u6750\\u5716\\u7247\\u6216\\u793A\\u610F\\u5716\\u7247\\u7684\\u5EFA\\u6750 \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(32, BuildingMaterialComponent_button_32_Template, 3, 0, \"button\", 18)(33, BuildingMaterialComponent_button_33_Template, 3, 0, \"button\", 19)(34, BuildingMaterialComponent_button_34_Template, 3, 0, \"button\", 20)(35, BuildingMaterialComponent_button_35_Template, 2, 0, \"button\", 21);\n          i0.ɵɵelementStart(36, \"input\", 22, 0);\n          i0.ɵɵlistener(\"change\", function BuildingMaterialComponent_Template_input_change_36_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.detectFileExcel($event));\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(38, \"button\", 23);\n          i0.ɵɵlistener(\"click\", function BuildingMaterialComponent_Template_button_click_38_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.exportExelMaterialTemplate());\n          });\n          i0.ɵɵtext(39, \"\\u4E0B\\u8F09\\u7BC4\\u4F8B\\u6A94\\u6848 \");\n          i0.ɵɵelement(40, \"i\", 24);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(41, \"div\", 25)(42, \"table\", 26)(43, \"thead\")(44, \"tr\", 27)(45, \"th\", 28);\n          i0.ɵɵtext(46, \"\\u9805\\u6B21\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(47, \"th\", 28);\n          i0.ɵɵtext(48, \"\\u540D\\u7A31\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(49, \"th\", 28);\n          i0.ɵɵtext(50, \"\\u9805\\u76EE\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(51, \"th\", 28);\n          i0.ɵɵtext(52, \"\\u4F4D\\u7F6E\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(53, \"th\", 28);\n          i0.ɵɵtext(54, \"\\u5EFA\\u6750\\u9078\\u9805\\u540D\\u7A31\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(55, \"th\", 28);\n          i0.ɵɵtext(56, \"\\u5EFA\\u6750\\u5716\\u7247\\u6A94\\u540D\\uFF08\\u76F8\\u540C\\u5EFA\\u6848\\u4E0D\\u53EF\\u91CD\\u8907\\uFF09\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(57, \"th\", 28);\n          i0.ɵɵtext(58, \"\\u793A\\u610F\\u5716\\u7247\\u6A94\\u540D\\uFF08\\u76F8\\u540C\\u5EFA\\u6848\\u4E0D\\u53EF\\u91CD\\u8907\\uFF09\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(59, \"th\", 29);\n          i0.ɵɵtext(60, \"\\u5EFA\\u6750\\u8AAA\\u660E\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(61, BuildingMaterialComponent_th_61_Template, 2, 0, \"th\", 30);\n          i0.ɵɵelementStart(62, \"th\", 31);\n          i0.ɵɵtext(63, \"\\u64CD\\u4F5C\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(64, BuildingMaterialComponent_tbody_64_Template, 2, 1, \"tbody\", 32);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(65, \"nb-card-footer\", 33)(66, \"ngx-pagination\", 34);\n          i0.ɵɵtwoWayListener(\"CollectionSizeChange\", function BuildingMaterialComponent_Template_ngx_pagination_CollectionSizeChange_66_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.totalRecords, $event) || (ctx.totalRecords = $event);\n            return i0.ɵɵresetView($event);\n          })(\"PageSizeChange\", function BuildingMaterialComponent_Template_ngx_pagination_PageSizeChange_66_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.pageSize, $event) || (ctx.pageSize = $event);\n            return i0.ɵɵresetView($event);\n          })(\"PageChange\", function BuildingMaterialComponent_Template_ngx_pagination_PageChange_66_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.pageIndex, $event) || (ctx.pageIndex = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵlistener(\"PageChange\", function BuildingMaterialComponent_Template_ngx_pagination_PageChange_66_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.pageChanged($event));\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(67, BuildingMaterialComponent_ng_template_67_Template, 44, 8, \"ng-template\", null, 1, i0.ɵɵtemplateRefExtractor)(69, BuildingMaterialComponent_ng_template_69_Template, 31, 7, \"ng-template\", null, 2, i0.ɵɵtemplateRefExtractor)(71, BuildingMaterialComponent_ng_template_71_Template, 22, 8, \"ng-template\", null, 3, i0.ɵɵtemplateRefExtractor);\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(11);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.selectedBuildCaseId);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngForOf\", ctx.listBuildCases);\n          i0.ɵɵadvance(5);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.CSelectName);\n          i0.ɵɵadvance(5);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.CImageCode);\n          i0.ɵɵadvance(5);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.CInfoImageCode);\n          i0.ɵɵadvance(3);\n          i0.ɵɵtwoWayProperty(\"checked\", ctx.filterMapping);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.isExcelExport);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isRead);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isCreate);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isExcelImport);\n          i0.ɵɵadvance(26);\n          i0.ɵɵproperty(\"ngIf\", ctx.ShowPrice == true);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", ctx.materialList != null && ctx.materialList.length > 0);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtwoWayProperty(\"CollectionSize\", ctx.totalRecords)(\"PageSize\", ctx.pageSize)(\"Page\", ctx.pageIndex);\n        }\n      },\n      dependencies: [CommonModule, i7.NgForOf, i7.NgIf, i7.DecimalPipe, SharedModule, i8.DefaultValueAccessor, i8.NumberValueAccessor, i8.NgControlStatus, i8.MaxLengthValidator, i8.NgModel, i2.NbCardComponent, i2.NbCardBodyComponent, i2.NbCardFooterComponent, i2.NbCardHeaderComponent, i2.NbCheckboxComponent, i2.NbInputDirective, i2.NbSelectComponent, i2.NbOptionComponent, i9.BreadcrumbComponent, i10.PaginationComponent],\n      styles: [\".image-table[_ngcontent-%COMP%] {\\n  width: 50px;\\n  height: 50px;\\n  cursor: pointer;\\n}\\n\\n.empty-image[_ngcontent-%COMP%] {\\n  color: red;\\n}\\n/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImJ1aWxkaW5nLW1hdGVyaWFsLmNvbXBvbmVudC5zY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUFBO0VBQ0UsV0FBQTtFQUNBLFlBQUE7RUFDQSxlQUFBO0FBQ0Y7O0FBRUE7RUFDRSxVQUFBO0FBQ0YiLCJmaWxlIjoiYnVpbGRpbmctbWF0ZXJpYWwuY29tcG9uZW50LnNjc3MiLCJzb3VyY2VzQ29udGVudCI6WyIuaW1hZ2UtdGFibGV7XHJcbiAgd2lkdGg6IDUwcHg7XHJcbiAgaGVpZ2h0OiA1MHB4O1xyXG4gIGN1cnNvcjogcG9pbnRlcjtcclxufVxyXG5cclxuLmVtcHR5LWltYWdle1xyXG4gIGNvbG9yOiByZWQ7XHJcbn1cclxuIl19 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvcGFnZXMvc2VsZWN0aW9uLW1hbmFnZW1lbnQvYnVpbGRpbmctbWF0ZXJpYWwvYnVpbGRpbmctbWF0ZXJpYWwuY29tcG9uZW50LnNjc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUE7RUFDRSxXQUFBO0VBQ0EsWUFBQTtFQUNBLGVBQUE7QUFDRjs7QUFFQTtFQUNFLFVBQUE7QUFDRjtBQUNBLGdmQUFnZiIsInNvdXJjZXNDb250ZW50IjpbIi5pbWFnZS10YWJsZXtcclxuICB3aWR0aDogNTBweDtcclxuICBoZWlnaHQ6IDUwcHg7XHJcbiAgY3Vyc29yOiBwb2ludGVyO1xyXG59XHJcblxyXG4uZW1wdHktaW1hZ2V7XHJcbiAgY29sb3I6IHJlZDtcclxufVxyXG4iXSwic291cmNlUm9vdCI6IiJ9 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "finalize", "mergeMap", "tap", "XLSX", "SharedModule", "BaseComponent", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵproperty", "buildCase_r2", "cID", "ɵɵadvance", "ɵɵtextInterpolate1", "CBuildCaseName", "ɵɵlistener", "BuildingMaterialComponent_button_32_Template_button_click_0_listener", "ɵɵrestoreView", "_r3", "ctx_r3", "ɵɵnextContext", "ɵɵresetView", "exportExelMaterialList", "ɵɵelement", "BuildingMaterialComponent_button_33_Template_button_click_0_listener", "_r5", "search", "BuildingMaterialComponent_button_34_Template_button_click_0_listener", "_r6", "dialog_r7", "ɵɵreference", "addNew", "BuildingMaterialComponent_button_35_Template_button_click_0_listener", "_r8", "inputFile_r9", "click", "BuildingMaterialComponent_tbody_64_tr_1_span_15_Template_img_click_1_listener", "_r10", "item_r11", "$implicit", "showImage", "CPicture", "dialogImage", "ɵɵsanitizeUrl", "BuildingMaterialComponent_tbody_64_tr_1_span_20_Template_img_click_1_listener", "_r12", "CInfoPicture", "ɵɵtextInterpolate", "CPrice", "BuildingMaterialComponent_tbody_64_tr_1_button_25_Template_button_click_0_listener", "_r13", "onSelectedMaterial", "ɵɵtemplate", "BuildingMaterialComponent_tbody_64_tr_1_span_15_Template", "BuildingMaterialComponent_tbody_64_tr_1_span_20_Template", "BuildingMaterialComponent_tbody_64_tr_1_td_23_Template", "BuildingMaterialComponent_tbody_64_tr_1_button_25_Template", "CId", "CName", "<PERSON>art", "CLocation", "ɵɵstyleMap", "CIsMapping", "CSelectName", "ɵɵclassMap", "CImageCode", "CInfoImageCode", "CDescription", "CShowPrice", "isRead", "BuildingMaterialComponent_tbody_64_tr_1_Template", "materialList", "ɵɵtwoWayListener", "BuildingMaterialComponent_ng_template_67_Template_input_ngModelChange_10_listener", "$event", "_r14", "ɵɵtwoWayBindingSet", "selectedMaterial", "BuildingMaterialComponent_ng_template_67_Template_input_ngModelChange_14_listener", "BuildingMaterialComponent_ng_template_67_Template_input_ngModelChange_18_listener", "BuildingMaterialComponent_ng_template_67_Template_input_ngModelChange_22_listener", "BuildingMaterialComponent_ng_template_67_Template_input_ngModelChange_27_listener", "BuildingMaterialComponent_ng_template_67_Template_button_click_28_listener", "imageBinder_r15", "openImageBinder", "BuildingMaterialComponent_ng_template_67_Template_textarea_ngModelChange_34_listener", "BuildingMaterialComponent_ng_template_67_Template_input_ngModelChange_38_listener", "BuildingMaterialComponent_ng_template_67_Template_button_click_40_listener", "ref_r16", "dialogRef", "onClose", "BuildingMaterialComponent_ng_template_67_Template_button_click_42_listener", "onSubmit", "ɵɵtwoWayProperty", "image_r19", "thumbnailUrl", "name", "BuildingMaterialComponent_ng_template_69_div_21_Template_div_click_0_listener", "_r18", "toggleImageSelection", "BuildingMaterialComponent_ng_template_69_div_21_i_3_Template", "BuildingMaterialComponent_ng_template_69_div_21_Template_button_click_4_listener", "imagePreview_r20", "previewImage", "BuildingMaterialComponent_ng_template_69_div_21_img_7_Template", "BuildingMaterialComponent_ng_template_69_div_21_div_8_Template", "isImageSelected", "ɵɵpipeBind1", "size", "BuildingMaterialComponent_ng_template_69_Template_input_ngModelChange_6_listener", "_r17", "imageSearchTerm", "BuildingMaterialComponent_ng_template_69_Template_input_input_6_listener", "filterImages", "BuildingMaterialComponent_ng_template_69_Template_button_click_7_listener", "loadImages", "BuildingMaterialComponent_ng_template_69_Template_button_click_11_listener", "selectAllImages", "BuildingMaterialComponent_ng_template_69_Template_button_click_14_listener", "clearAllSelection", "BuildingMaterialComponent_ng_template_69_div_21_Template", "BuildingMaterialComponent_ng_template_69_div_22_Template", "BuildingMaterialComponent_ng_template_69_Template_button_click_27_listener", "ref_r21", "onCloseImageBinder", "BuildingMaterialComponent_ng_template_69_Template_button_click_29_listener", "onConfirmImageSelection", "selectedImages", "length", "filteredImages", "availableImages", "previewingImage", "fullUrl", "BuildingMaterialComponent_ng_template_71_Template_button_click_5_listener", "_r22", "previousImage", "BuildingMaterialComponent_ng_template_71_Template_button_click_8_listener", "nextImage", "BuildingMaterialComponent_ng_template_71_img_12_Template", "BuildingMaterialComponent_ng_template_71_div_13_Template", "BuildingMaterialComponent_ng_template_71_Template_button_click_18_listener", "toggleImageSelectionInPreview", "BuildingMaterialComponent_ng_template_71_Template_button_click_20_listener", "ref_r23", "close", "currentPreviewIndex", "ɵɵtextInterpolate2", "BuildingMaterialComponent", "constructor", "_allow", "dialogService", "message", "valid", "_buildCaseService", "_materialService", "_utilityService", "isNew", "listBuildCases", "materialOptions", "value", "label", "materialOptionsId", "ShowPrice", "currentImageShowing", "filterMapping", "ngOnInit", "getListBuildCase", "apiBuildCaseGetAllBuildCaseForSelectPost$Json", "body", "CIsPagi", "CStatus", "pipe", "res", "StatusCode", "Entries", "selectedBuildCaseId", "getMaterialList", "subscribe", "pageIndex", "apiMaterialGetMaterialListPost$Json", "CBuildCaseId", "CPlanUse", "PageSize", "pageSize", "PageIndex", "totalRecords", "TotalItems", "pageChanged", "apiMaterialExportExcelMaterialListPost$Json", "FileByte", "downloadExcelFile", "exportExelMaterialTemplate", "apiMaterialExportExcelMaterialTemplatePost$Json", "ref", "open", "data", "validation", "clear", "required", "isStringMaxLength", "errorMessages", "showErrorMSGs", "apiMaterialSaveMaterialAdminPost$Json", "CMaterialId", "showSucessMSG", "showErrorMSG", "Message", "detectFileExcel", "event", "target", "reader", "FileReader", "readAsBinaryString", "files", "onload", "e", "binarystr", "result", "wb", "read", "type", "wsname", "SheetNames", "ws", "Sheets", "isValidFile", "utils", "sheet_to_json", "for<PERSON>ach", "x", "apiMaterialImportExcelMaterialListPost$Json", "CFile", "imageUrl", "dialog", "changeFilter", "closeOnBackdropClick", "id", "lastModified", "Date", "trim", "searchTerm", "toLowerCase", "filter", "image", "includes", "index", "findIndex", "selected", "splice", "push", "some", "stopPropagation", "img", "imageNames", "map", "join", "ɵɵdirectiveInject", "i1", "AllowHelper", "i2", "NbDialogService", "i3", "MessageService", "i4", "ValidationHelper", "i5", "BuildCaseService", "MaterialService", "i6", "UtilityService", "selectors", "standalone", "features", "ɵɵInheritDefinitionFeature", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "BuildingMaterialComponent_Template", "rf", "ctx", "BuildingMaterialComponent_Template_nb_select_ngModelChange_11_listener", "_r1", "BuildingMaterialComponent_nb_option_12_Template", "BuildingMaterialComponent_Template_input_ngModelChange_17_listener", "BuildingMaterialComponent_Template_input_ngModelChange_22_listener", "BuildingMaterialComponent_Template_input_ngModelChange_27_listener", "BuildingMaterialComponent_Template_nb_checkbox_checkedChange_30_listener", "BuildingMaterialComponent_Template_nb_checkbox_change_30_listener", "BuildingMaterialComponent_button_32_Template", "BuildingMaterialComponent_button_33_Template", "BuildingMaterialComponent_button_34_Template", "BuildingMaterialComponent_button_35_Template", "BuildingMaterialComponent_Template_input_change_36_listener", "BuildingMaterialComponent_Template_button_click_38_listener", "BuildingMaterialComponent_th_61_Template", "BuildingMaterialComponent_tbody_64_Template", "BuildingMaterialComponent_Template_ngx_pagination_CollectionSizeChange_66_listener", "BuildingMaterialComponent_Template_ngx_pagination_PageSizeChange_66_listener", "BuildingMaterialComponent_Template_ngx_pagination_PageChange_66_listener", "BuildingMaterialComponent_ng_template_67_Template", "ɵɵtemplateRefExtractor", "BuildingMaterialComponent_ng_template_69_Template", "BuildingMaterialComponent_ng_template_71_Template", "isExcelExport", "isCreate", "isExcelImport", "i7", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "DecimalPipe", "i8", "DefaultValueAccessor", "NumberValueAccessor", "NgControlStatus", "MaxLengthValidator", "NgModel", "NbCardComponent", "NbCardBodyComponent", "NbCardFooterComponent", "NbCardHeaderComponent", "NbCheckboxComponent", "NbInputDirective", "NbSelectComponent", "NbOptionComponent", "i9", "BreadcrumbComponent", "i10", "PaginationComponent", "styles"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\selection-management\\building-material\\building-material.component.ts", "C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\selection-management\\building-material\\building-material.component.html"], "sourcesContent": ["import { Component, OnInit, TemplateRef, } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { NbDialogService } from '@nebular/theme';\r\nimport { MessageService } from 'src/app/shared/services/message.service';\r\nimport { ValidationHelper } from 'src/app/shared/helper/validationHelper';\r\nimport { BuildCaseService, MaterialService } from 'src/services/api/services';\r\nimport { BuildCaseGetListReponse, SaveMaterialArgs, GetMaterialListResponse } from 'src/services/api/models';\r\nimport { finalize, mergeMap, tap } from 'rxjs';\r\nimport { AllowHelper } from 'src/app/shared/helper/allowHelper';\r\nimport { UtilityService } from 'src/app/shared/services/utility.service';\r\nimport * as XLSX from 'xlsx';\r\nimport { SharedModule } from '../../components/shared.module';\r\nimport { BaseComponent } from '../../components/base/baseComponent';\r\n\r\n// 圖片項目介面\r\ninterface ImageItem {\r\n  id: string;\r\n  name: string;\r\n  size: number;\r\n  thumbnailUrl?: string;\r\n  fullUrl?: string;\r\n  lastModified?: Date;\r\n}\r\n\r\n@Component({\r\n  selector: 'ngx-building-material',\r\n  templateUrl: './building-material.component.html',\r\n  styleUrls: ['./building-material.component.scss'],\r\n  standalone: true,\r\n  imports: [CommonModule, SharedModule],\r\n})\r\n\r\nexport class BuildingMaterialComponent extends BaseComponent implements OnInit {\r\n  isNew = true\r\n\r\n  materialList: GetMaterialListResponse[]\r\n  selectedMaterial: GetMaterialListResponse\r\n\r\n  listBuildCases: BuildCaseGetListReponse[] = []\r\n  selectedBuildCaseId: number\r\n\r\n  materialOptions = [\r\n    {\r\n      value: null,\r\n      label: '全部',\r\n    },\r\n    {\r\n      value: false,\r\n      label: '方案',\r\n    },\r\n    {\r\n      value: true,\r\n      label: '選樣',\r\n    }\r\n  ]\r\n  materialOptionsId = null\r\n  CSelectName: string = \"\"\r\n  CImageCode: string = \"\"\r\n  CInfoImageCode: string = \"\"  ShowPrice: boolean = false\r\n  currentImageShowing: string = \"\"\r\n  filterMapping: boolean = false\r\n  CIsMapping: boolean = true\r\n\r\n  // 圖片綁定相關屬性\r\n  availableImages: ImageItem[] = []\r\n  filteredImages: ImageItem[] = []\r\n  selectedImages: ImageItem[] = []\r\n  imageSearchTerm: string = \"\"\r\n  previewingImage: ImageItem | null = null\r\n  currentPreviewIndex: number = 0\r\n\r\n  constructor(\r\n    private _allow: AllowHelper,\r\n    private dialogService: NbDialogService,\r\n    private message: MessageService,\r\n    private valid: ValidationHelper,\r\n    private _buildCaseService: BuildCaseService,\r\n    private _materialService: MaterialService,\r\n    private _utilityService: UtilityService\r\n  ) {\r\n    super(_allow)\r\n  }\r\n\r\n  override ngOnInit(): void {\r\n    this.getListBuildCase()\r\n  }\r\n\r\n  getListBuildCase() {\r\n    this._buildCaseService.apiBuildCaseGetAllBuildCaseForSelectPost$Json({\r\n      body: {\r\n        CIsPagi: false,\r\n        CStatus: 1,\r\n      }\r\n    })\r\n      .pipe(\r\n        tap(res => {\r\n          if (res.StatusCode == 0) {\r\n            this.listBuildCases = res.Entries?.length ? res.Entries : []\r\n            this.selectedBuildCaseId = this.listBuildCases[0].cID!\r\n          }\r\n        }),\r\n        mergeMap(() => this.getMaterialList())\r\n      ).subscribe()\r\n  }\r\n\r\n  getMaterialList(pageIndex: number = 1) {\r\n    return this._materialService.apiMaterialGetMaterialListPost$Json({\r\n      body: {\r\n        CBuildCaseId: this.selectedBuildCaseId,\r\n        CPlanUse: this.materialOptionsId,\r\n        CSelectName: this.CSelectName,\r\n        CImageCode: this.CImageCode,\r\n        PageSize: this.pageSize,\r\n        PageIndex: pageIndex,\r\n        CInfoImageCode: this.CInfoImageCode,\r\n        CIsMapping: this.CIsMapping\r\n      }\r\n    }).pipe(\r\n      tap(res => {\r\n        if (res.StatusCode == 0) {\r\n          this.materialList = res.Entries! ?? []\r\n          this.totalRecords = res.TotalItems!\r\n\r\n          if(this.materialList.length > 0) {\r\n            this.ShowPrice = this.materialList[0].CShowPrice!;\r\n          }\r\n        }\r\n      })\r\n    )\r\n  }\r\n\r\n  search() {\r\n    this.getMaterialList().subscribe()\r\n  }\r\n\r\n  pageChanged(pageIndex: number) {\r\n    this.getMaterialList(pageIndex).subscribe()\r\n  }\r\n\r\n  exportExelMaterialList() {\r\n    this._materialService.apiMaterialExportExcelMaterialListPost$Json({\r\n      body: this.selectedBuildCaseId\r\n    }).subscribe(res => {\r\n      if (res.StatusCode == 0) {\r\n        if (res.Entries!.FileByte) {\r\n          this._utilityService.downloadExcelFile(res.Entries!.FileByte, '建材管理')\r\n        }\r\n      }\r\n    })\r\n  }\r\n\r\n  exportExelMaterialTemplate() {\r\n    this._materialService.apiMaterialExportExcelMaterialTemplatePost$Json({\r\n      body: this.selectedBuildCaseId\r\n    }).subscribe(res => {\r\n      if (res.StatusCode == 0) {\r\n        if (res.Entries!.FileByte) {\r\n          this._utilityService.downloadExcelFile(res.Entries!.FileByte, '建材管理')\r\n        }\r\n      }\r\n    })\r\n  }\r\n\r\n  addNew(ref: any) {\r\n    this.isNew = true\r\n    this.selectedMaterial = {}\r\n    this.dialogService.open(ref)\r\n  }\r\n\r\n  onSelectedMaterial(data: GetMaterialListResponse, ref: any) {\r\n    this.isNew = false\r\n    this.selectedMaterial = { ...data }\r\n    this.dialogService.open(ref)\r\n  }\r\n\r\n  validation() {\r\n    this.valid.clear();\r\n    this.valid.required('[名稱]', this.selectedMaterial.CName)\r\n    this.valid.required('[項目]', this.selectedMaterial.CPart)\r\n    this.valid.required('[位置]', this.selectedMaterial.CLocation)\r\n    this.valid.required('[建材選項名稱]', this.selectedMaterial.CSelectName)\r\n    this.valid.required('[建材圖片編號]', this.selectedMaterial.CImageCode)\r\n    this.valid.isStringMaxLength('[名稱]', this.selectedMaterial.CName, 30)\r\n    this.valid.isStringMaxLength('[項目]', this.selectedMaterial.CPart, 30)\r\n    this.valid.isStringMaxLength('[位置]', this.selectedMaterial.CLocation, 30)\r\n    this.valid.isStringMaxLength('[建材選項名稱]', this.selectedMaterial.CSelectName, 30)\r\n    this.valid.isStringMaxLength('[建材圖片編號]', this.selectedMaterial.CImageCode, 30)\r\n  }\r\n\r\n  onSubmit(ref: any) {\r\n    this.validation()\r\n    if (this.valid.errorMessages.length > 0) {\r\n      this.message.showErrorMSGs(this.valid.errorMessages);\r\n      return\r\n    }\r\n    this._materialService.apiMaterialSaveMaterialAdminPost$Json({\r\n      body: {\r\n        CBuildCaseId: this.selectedBuildCaseId,\r\n        CImageCode: this.selectedMaterial.CImageCode,\r\n        CName: this.selectedMaterial.CName,\r\n        CPart: this.selectedMaterial.CPart,\r\n        CLocation: this.selectedMaterial.CLocation,\r\n        CSelectName: this.selectedMaterial.CSelectName,\r\n        CDescription: this.selectedMaterial.CDescription,\r\n        CMaterialId: this.isNew ? null : this.selectedMaterial.CId!,\r\n        CPrice: this.selectedMaterial.CPrice\r\n      }\r\n    })\r\n      .pipe(\r\n        tap(res => {\r\n          if (res.StatusCode === 0) {\r\n            this.message.showSucessMSG(\"執行成功\");\r\n          } else {\r\n            this.message.showErrorMSG(res.Message!);\r\n          }\r\n        }),\r\n        mergeMap(() => this.getMaterialList()),\r\n        finalize(() => ref.close())\r\n      ).subscribe()\r\n  }\r\n\r\n  onClose(ref: any) {\r\n    ref.close();\r\n  }\r\n\r\n  detectFileExcel(event: any) {\r\n    const target: DataTransfer = <DataTransfer>(event.target);\r\n    const reader: FileReader = new FileReader();\r\n    reader.readAsBinaryString(target.files[0]);\r\n    reader.onload = (e: any) => {\r\n      const binarystr: string = e.target.result;\r\n      const wb: XLSX.WorkBook = XLSX.read(binarystr, { type: 'binary' });\r\n\r\n      const wsname: string = wb.SheetNames[0];\r\n      const ws: XLSX.WorkSheet = wb.Sheets[wsname];\r\n\r\n      let isValidFile: boolean = true;\r\n      const data = XLSX.utils.sheet_to_json(ws);\r\n      if (data && data.length > 0) {\r\n        data.forEach((x: any) => {\r\n          if (!(x['名稱'] && x['項目'] && x['位置'] && x['建材選項名稱'] &&\r\n            (x['建材圖片檔名'] || x['建材說明'] || x['示意圖片檔名']))) {\r\n            isValidFile = false;\r\n          }\r\n        })\r\n\r\n        if (!isValidFile) {\r\n          this.message.showErrorMSG(\"导入文件时出现错误\")\r\n        } else {\r\n          this._materialService.apiMaterialImportExcelMaterialListPost$Json({\r\n            body: {\r\n              CBuildCaseId: this.selectedBuildCaseId,\r\n              CFile: target.files[0]\r\n            }\r\n          }).pipe(\r\n            tap(res => {\r\n              if (res.StatusCode == 0) {\r\n                this.message.showSucessMSG(\"執行成功\")\r\n              } else {\r\n                this.message.showErrorMSG(res.Message!)\r\n              }\r\n            }),\r\n            mergeMap(() => this.getMaterialList(1))\r\n          ).subscribe();\r\n        }\r\n      } else {\r\n        this.message.showErrorMSG(\"匯入的檔案內容為空，請檢查檔案並重新上傳。\")\r\n      }\r\n      event.target.value = null;\r\n    };\r\n  }\r\n\r\n  showImage(imageUrl: string, dialog: TemplateRef<any>){\r\n    this.currentImageShowing = imageUrl;\r\n    this.dialogService.open(dialog);\r\n  }\r\n  changeFilter(){\r\n    if(this.filterMapping){\r\n      this.CIsMapping = false;\r\n      this.getMaterialList().subscribe();\r\n    }\r\n    else{\r\n      this.CIsMapping = true;\r\n      this.getMaterialList().subscribe();\r\n    }\r\n  }\r\n\r\n  // 圖片綁定功能方法\r\n  openImageBinder(ref: TemplateRef<any>) {\r\n    this.loadImages();\r\n    this.dialogService.open(ref, { closeOnBackdropClick: false });\r\n  }\r\n\r\n  loadImages() {\r\n    // 模擬載入圖片資料，實際應該從 API 取得\r\n    // 這裡先用假資料示範\r\n    this.availableImages = [\r\n      {\r\n        id: '1',\r\n        name: 'floor_tile_001.jpg',\r\n        size: 245,\r\n        thumbnailUrl: 'assets/images/materials/thumbs/floor_tile_001.jpg',\r\n        fullUrl: 'assets/images/materials/floor_tile_001.jpg',\r\n        lastModified: new Date()\r\n      },\r\n      {\r\n        id: '2', \r\n        name: 'wall_paint_002.jpg',\r\n        size: 189,\r\n        thumbnailUrl: 'assets/images/materials/thumbs/wall_paint_002.jpg',\r\n        fullUrl: 'assets/images/materials/wall_paint_002.jpg',\r\n        lastModified: new Date()\r\n      },\r\n      {\r\n        id: '3',\r\n        name: 'cabinet_wood_003.jpg', \r\n        size: 312,\r\n        thumbnailUrl: 'assets/images/materials/thumbs/cabinet_wood_003.jpg',\r\n        fullUrl: 'assets/images/materials/cabinet_wood_003.jpg',\r\n        lastModified: new Date()\r\n      },\r\n      {\r\n        id: '4',\r\n        name: 'lighting_fixture_004.jpg',\r\n        size: 156,\r\n        thumbnailUrl: 'assets/images/materials/thumbs/lighting_fixture_004.jpg', \r\n        fullUrl: 'assets/images/materials/lighting_fixture_004.jpg',\r\n        lastModified: new Date()\r\n      },\r\n      {\r\n        id: '5',\r\n        name: 'door_handle_005.jpg',\r\n        size: 78,\r\n        thumbnailUrl: 'assets/images/materials/thumbs/door_handle_005.jpg',\r\n        fullUrl: 'assets/images/materials/door_handle_005.jpg', \r\n        lastModified: new Date()\r\n      },\r\n      {\r\n        id: '6',\r\n        name: 'window_frame_006.jpg',\r\n        size: 267,\r\n        thumbnailUrl: 'assets/images/materials/thumbs/window_frame_006.jpg',\r\n        fullUrl: 'assets/images/materials/window_frame_006.jpg',\r\n        lastModified: new Date()\r\n      }\r\n    ];\r\n    \r\n    this.filteredImages = [...this.availableImages];\r\n    \r\n    // TODO: 實際實作時，這裡應該呼叫 API 取得圖片列表\r\n    // this._materialService.apiMaterialGetImageListPost$Json({\r\n    //   body: { CBuildCaseId: this.selectedBuildCaseId }\r\n    // }).subscribe(res => {\r\n    //   if (res.StatusCode === 0) {\r\n    //     this.availableImages = res.Entries || [];\r\n    //     this.filteredImages = [...this.availableImages];\r\n    //   }\r\n    // });\r\n  }\r\n\r\n  filterImages() {\r\n    if (!this.imageSearchTerm.trim()) {\r\n      this.filteredImages = [...this.availableImages];\r\n    } else {\r\n      const searchTerm = this.imageSearchTerm.toLowerCase();\r\n      this.filteredImages = this.availableImages.filter(image => \r\n        image.name.toLowerCase().includes(searchTerm)\r\n      );\r\n    }\r\n  }\r\n\r\n  toggleImageSelection(image: ImageItem) {\r\n    const index = this.selectedImages.findIndex(selected => selected.id === image.id);\r\n    if (index > -1) {\r\n      this.selectedImages.splice(index, 1);\r\n    } else {\r\n      this.selectedImages.push(image);\r\n    }\r\n  }\r\n\r\n  isImageSelected(image: ImageItem): boolean {\r\n    return this.selectedImages.some(selected => selected.id === image.id);\r\n  }\r\n\r\n  selectAllImages() {\r\n    this.selectedImages = [...this.filteredImages];\r\n  }\r\n\r\n  clearAllSelection() {\r\n    this.selectedImages = [];\r\n  }\r\n  previewImage(image: ImageItem, event: Event) {\r\n    event.stopPropagation();\r\n    this.previewingImage = image;\r\n    this.currentPreviewIndex = this.filteredImages.findIndex(img => img.id === image.id);\r\n    // 需要在組件中取得預覽對話框的模板引用\r\n    // 這裡暫時註解掉，需要在 HTML 中正確設定\r\n    // this.dialogService.open(imagePreviewRef);\r\n  }\r\n\r\n  previousImage() {\r\n    if (this.currentPreviewIndex > 0) {\r\n      this.currentPreviewIndex--;\r\n      this.previewingImage = this.filteredImages[this.currentPreviewIndex];\r\n    }\r\n  }\r\n\r\n  nextImage() {\r\n    if (this.currentPreviewIndex < this.filteredImages.length - 1) {\r\n      this.currentPreviewIndex++;\r\n      this.previewingImage = this.filteredImages[this.currentPreviewIndex];\r\n    }\r\n  }\r\n\r\n  toggleImageSelectionInPreview() {\r\n    if (this.previewingImage) {\r\n      this.toggleImageSelection(this.previewingImage);\r\n    }\r\n  }\r\n  onConfirmImageSelection(ref: any) {\r\n    if (this.selectedImages.length > 0) {\r\n      // 如果只選取一張圖片，直接設定到建材圖片檔名\r\n      if (this.selectedImages.length === 1) {\r\n        this.selectedMaterial.CImageCode = this.selectedImages[0].name;\r\n      } else {\r\n        // 多張圖片的話，可以設定第一張，或者讓使用者選擇主要圖片\r\n        const imageNames = this.selectedImages.map(img => img.name).join(', ');\r\n        this.message.showSucessMSG(`已選取 ${this.selectedImages.length} 張圖片: ${imageNames}`);\r\n        this.selectedMaterial.CImageCode = this.selectedImages[0].name;\r\n      }\r\n    }\r\n    \r\n    this.clearAllSelection();\r\n    ref.close();\r\n  }\r\n\r\n  onCloseImageBinder(ref: any) {\r\n    this.clearAllSelection();\r\n    this.imageSearchTerm = \"\";\r\n    ref.close();\r\n  }\r\n}\r\n", "<nb-card accent=\"success\">\r\n  <nb-card-header>\r\n    <ngx-breadcrumb></ngx-breadcrumb>\r\n  </nb-card-header>\r\n  <nb-card-body>\r\n    <h1 class=\"font-bold text-[#818181]\"> 可設定單筆或批次匯入設定各區域及方案對應之建材。\r\n    </h1>\r\n    <div class=\"d-flex flex-wrap\">\r\n      <div class=\"col-md-6\">\r\n        <div class=\"form-group d-flex align-items-center w-full\">\r\n          <label for=\"buildingName\" class=\"label mr-2 w-[22%]\">建案</label>\r\n          <nb-select placeholder=\"建案\" [(ngModel)]=\"selectedBuildCaseId\" class=\"w-full\">\r\n            <nb-option *ngFor=\"let buildCase of listBuildCases\" [value]=\"buildCase.cID\">\r\n              {{ buildCase.CBuildCaseName }}\r\n            </nb-option>\r\n          </nb-select>\r\n        </div>\r\n      </div>\r\n      <!-- <div class=\"col-md-6\">\r\n        <div class=\"form-group d-flex align-items-center w-full\">\r\n          <label for=\"buildingName\" class=\"label mr-2  w-[22%]\">建材類別</label>\r\n          <nb-select placeholder=\"建材類別\" [(ngModel)]=\"materialOptionsId\" class=\"w-full\">\r\n            <nb-option *ngFor=\"let option of materialOptions\" [value]=\"option.value\">\r\n              {{ option.label }}\r\n            </nb-option>\r\n          </nb-select>\r\n        </div>\r\n      </div> -->\r\n      <div class=\"col-md-6\">\r\n        <div class=\"form-group d-flex align-items-center w-full\">\r\n          <label for=\"buildingName\" class=\"label mr-2 w-[22%]\">建材選項名稱 </label>\r\n          <input type=\"text\" nbInput placeholder=\"建材選項名稱\" [(ngModel)]=\"CSelectName\" class=\"w-full\">\r\n        </div>\r\n      </div>\r\n      <div class=\"col-md-6\">\r\n        <div class=\"form-group d-flex align-items-center w-full\">\r\n          <label for=\"buildingName\" class=\"label mr-2 w-[22%]\">建材圖片檔名\r\n          </label>\r\n          <input type=\"text\" nbInput placeholder=\"建材圖片檔名\" [(ngModel)]=\"CImageCode\" class=\"w-full\">\r\n        </div>\r\n      </div>\r\n      <div class=\"col-md-6\">\r\n        <div class=\"form-group d-flex align-items-center w-full\">\r\n          <label for=\"buildingName\" class=\"label mr-2 w-[22%]\">示意圖片檔名\r\n          </label>\r\n          <input type=\"text\" nbInput placeholder=\"示意圖片檔名\" [(ngModel)]=\"CInfoImageCode\" class=\"w-full\">\r\n        </div>\r\n      </div>\r\n      <div class=\"col-md-12\">\r\n        <div class=\"d-flex justify-content-end w-full\">\r\n          <nb-checkbox status=\"basic\" class=\"flex\" style=\"flex:auto\" [(checked)]=\"filterMapping\" (change)=\"changeFilter()\">\r\n            只顯示缺少建材圖片或示意圖片的建材\r\n          </nb-checkbox>\r\n          <button *ngIf=\"isExcelExport\" class=\"btn btn-success mr-2\" (click)=\"exportExelMaterialList()\">匯出 <i\r\n            class=\"fas fa-file-download\"></i></button>\r\n          <button *ngIf=\"isRead\" class=\"btn btn-info mr-2 text-white ml-2\" (click)=\"search()\">\r\n            查詢 <i class=\"fas fa-search\"></i></button>\r\n          <button *ngIf=\"isCreate\" class=\"btn btn-info mx-1 ml-2 mr-2\" (click)=\"addNew(dialog)\">單筆新增 <i\r\n              class=\"fas fa-plus\"></i></button>\r\n          <button class=\"btn btn-info mx-1\" *ngIf=\"isExcelImport\" (click)=\"inputFile.click()\"> 批次匯入 </button>\r\n          <input class=\"hidden\" type=\"file\" accept=\".xls, .xlsx\" #inputFile (change)=\"detectFileExcel($event)\">\r\n          <button class=\"btn btn-success ml-2\" (click)=\"exportExelMaterialTemplate()\">下載範例檔案 <i\r\n              class=\"fas fa-file-download\"></i></button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <div class=\"table-responsive mt-4\">\r\n      <table class=\"table table-striped border \" style=\"min-width: 1200px; background-color:#f3f3f3;\">\r\n        <thead>\r\n          <tr style=\"background-color: #27ae60; color: white;\">\r\n            <th scope=\"col\" class=\"col-1\">項次</th>\r\n            <!-- <th scope=\"col\" class=\"col-1\">建材類別</th> -->\r\n            <th scope=\"col\" class=\"col-1\">名稱</th>\r\n            <th scope=\"col\" class=\"col-1\">項目</th>\r\n            <th scope=\"col\" class=\"col-1\">位置</th>\r\n            <th scope=\"col\" class=\"col-1\">建材選項名稱</th>\r\n            <th scope=\"col\" class=\"col-1\">建材圖片檔名（相同建案不可重複）</th>\r\n            <th scope=\"col\" class=\"col-1\">示意圖片檔名（相同建案不可重複）</th>\r\n            <th scope=\"col\" class=\"col-3\">建材說明</th>\r\n            <th scope=\"col\" class=\"col-1\" *ngIf=\"ShowPrice == true\">價格</th>\r\n            <th scope=\"col\" class=\"col-1 text-center\">操作</th>\r\n          </tr>\r\n        </thead>\r\n        <tbody *ngIf=\"materialList != null && materialList.length > 0\">\r\n          <tr *ngFor=\"let item of materialList ; let i = index\">\r\n            <td>{{ item.CId}}</td>\r\n            <!-- <td>{{ item.CPlanUse! | getPlanUse}}</td> -->\r\n            <td>{{ item.CName}}</td>\r\n            <td>{{ item.CPart}}</td>\r\n            <td>{{ item.CLocation}}</td>\r\n            <td [style]=\"!item.CIsMapping ? 'color: red' : ''\">{{ item.CSelectName}}</td>\r\n            <td>\r\n              <div class=\"flex justify-between\">\r\n                <span style=\"overflow-wrap: break-word; width: 80%\"\r\n                  [class]=\"item.CPicture == null ? 'empty-image' : ''\">\r\n                  {{ item.CImageCode }}\r\n                </span>\r\n                <span class=\"width-[50px]\" *ngIf=\"item.CPicture\">\r\n                  <img [src]=\"item.CPicture\" class=\"image-table\" (click)=\"showImage(item.CPicture, dialogImage)\" />\r\n                </span>\r\n              </div>\r\n            </td>\r\n            <td>\r\n              <div class=\"flex justify-between\">\r\n                <span style=\"overflow-wrap: break-word; width: 80%;\"\r\n                  [class]=\"item.CInfoPicture == null ? 'empty-image' : ''\">\r\n                  {{ item.CInfoImageCode }}\r\n                </span>\r\n                <span class=\"width-[50px]\" *ngIf=\"item.CInfoPicture\">\r\n                  <img [src]=\"item.CInfoPicture\" class=\"image-table\" (click)=\"showImage(item.CInfoPicture, dialogImage)\" />\r\n                </span>\r\n              </div>\r\n            </td>\r\n            <td>{{ item.CDescription}}</td>\r\n            <td *ngIf=\"item.CShowPrice == true\">{{ item.CPrice}}</td>\r\n            <td class=\"text-center w-32\">\r\n              <button class=\"btn btn-outline-primary btn-sm m-1\" (click)=\"onSelectedMaterial(item, dialog)\"\r\n                *ngIf=\"isRead\">編輯</button>\r\n            </td>\r\n          </tr>\r\n        </tbody>\r\n      </table>\r\n    </div>\r\n  </nb-card-body>\r\n  <nb-card-footer class=\"d-flex justify-content-center\">\r\n    <ngx-pagination [(CollectionSize)]=\"totalRecords\" [(PageSize)]=\"pageSize\" [(Page)]=\"pageIndex\"\r\n      (PageChange)=\"pageChanged($event)\">\r\n    </ngx-pagination>\r\n  </nb-card-footer>\r\n</nb-card>\r\n\r\n<ng-template #dialog let-dialog let-ref=\"dialogRef\">\r\n  <nb-card class=\"w-[700px]\">\r\n    <nb-card-header>\r\n      建材管理 > 新增建材\r\n    </nb-card-header>\r\n    <nb-card-body class=\"px-4\">\r\n      <h5 class=\"text-base\">請輸入下方內容新增建材，請留意建材圖片編號不可與該建案內其他編號重複。</h5>\r\n      <div class=\"w-full mt-3\">\r\n        <div class=\"flex items-center\">\r\n          <label class=\"required-field w-[150px]\">名稱</label>\r\n          <input type=\"text\" class=\"w-full !max-w-full p-2 rounded text-[13px]\" nbInput maxlength=\"30\"\r\n            [(ngModel)]=\"selectedMaterial.CName\" />\r\n        </div>\r\n\r\n        <div class=\"flex items-center mt-3\">\r\n          <label class=\"required-field w-[150px]\">項目</label>\r\n          <input type=\"text\" class=\"w-full !max-w-full p-2 rounded text-[13px]\" nbInput maxlength=\"30\"\r\n            [(ngModel)]=\"selectedMaterial.CPart\" />\r\n        </div>\r\n        <div class=\"flex items-center mt-3\">\r\n          <label class=\"required-field w-[150px]\">位置</label>\r\n          <input type=\"text\" class=\"w-full !max-w-full p-2 rounded text-[13px]\" nbInput maxlength=\"30\"\r\n            [(ngModel)]=\"selectedMaterial.CLocation\" />\r\n        </div>\r\n\r\n        <div class=\"flex items-center mt-3\">\r\n          <label class=\"required-field w-[150px]\">建材選項名稱</label>\r\n          <input type=\"text\" nbInput class=\"w-full !max-w-full p-2 rounded text-[13px]\" maxlength=\"30\"\r\n            [(ngModel)]=\"selectedMaterial.CSelectName\" />\r\n        </div>        <div class=\"flex items-center mt-3\">\r\n          <label class=\"required-field w-[150px]\">建材圖片檔名</label>\r\n          <div class=\"flex w-full gap-2\">\r\n            <input type=\"text\" nbInput class=\"flex-1 !max-w-full p-2 rounded text-[13px]\" maxlength=\"30\"\r\n              [(ngModel)]=\"selectedMaterial.CImageCode\" />\r\n            <button class=\"btn btn-outline-info btn-sm\" (click)=\"openImageBinder(imageBinder)\">\r\n              綁定圖片 <i class=\"fas fa-images\"></i>\r\n            </button>\r\n          </div>\r\n        </div>\r\n\r\n        <div class=\"flex items-center mt-3\">\r\n          <label class=\"w-[150px]\">建材說明</label>\r\n          <textarea nbInput [(ngModel)]=\"selectedMaterial.CDescription\" [rows]=\"4\"\r\n            class=\"resize-none w-full !max-w-full p-2 rounded text-[13px]\"></textarea>\r\n        </div>\r\n\r\n        <div class=\"flex items-center mt-3\">\r\n          <label class=\"w-[150px]\">價格</label>\r\n          <input type=\"number\" nbInput class=\"w-full !max-w-full p-2 rounded text-[13px]\" maxlength=\"30\"\r\n            [(ngModel)]=\"selectedMaterial.CPrice\" />\r\n        </div>\r\n      </div>\r\n    </nb-card-body>\r\n    <nb-card-footer class=\"d-flex justify-content-center\">\r\n      <button class=\"btn btn-danger btn-sm mr-4\" (click)=\"onClose(ref)\">關閉</button>\r\n      <button class=\"btn btn-success btn-sm\" (click)=\"onSubmit(ref)\">儲存</button>\r\n    </nb-card-footer>\r\n  </nb-card>\r\n</ng-template>\r\n\r\n<ng-template #imageBinder let-dialog let-ref=\"dialogRef\">\r\n  <nb-card class=\"w-[900px] h-[700px]\">\r\n    <nb-card-header>\r\n      圖片綁定 - 選擇建材圖片\r\n    </nb-card-header>\r\n    <nb-card-body class=\"px-4 h-[600px] overflow-hidden\">\r\n      <!-- 搜尋功能 -->\r\n      <div class=\"flex gap-3 mb-4\">\r\n        <div class=\"flex-1\">\r\n          <input type=\"text\" class=\"w-full p-2 rounded border\" placeholder=\"搜尋圖片名稱...\" \r\n                 [(ngModel)]=\"imageSearchTerm\" (input)=\"filterImages()\" />\r\n        </div>\r\n        <button class=\"btn btn-info\" (click)=\"loadImages()\">\r\n          重新載入 <i class=\"fas fa-refresh\"></i>\r\n        </button>\r\n      </div>\r\n\r\n      <!-- 批次操作按鈕 -->\r\n      <div class=\"flex gap-2 mb-3\">\r\n        <button class=\"btn btn-outline-primary btn-sm\" (click)=\"selectAllImages()\">\r\n          全選 <i class=\"fas fa-check-square\"></i>\r\n        </button>\r\n        <button class=\"btn btn-outline-secondary btn-sm\" (click)=\"clearAllSelection()\">\r\n          清除選取 <i class=\"fas fa-square\"></i>\r\n        </button>\r\n        <div class=\"ml-auto text-sm text-gray-600\">\r\n          已選取: {{ selectedImages.length }} 張圖片\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 圖片列表 -->\r\n      <div class=\"h-[480px] overflow-y-auto border rounded p-3\">\r\n        <div class=\"grid grid-cols-4 gap-3\">\r\n          <div *ngFor=\"let image of filteredImages\" \r\n               class=\"border rounded p-2 cursor-pointer transition-all duration-200\"\r\n               [class]=\"isImageSelected(image) ? 'border-blue-500 bg-blue-50' : 'border-gray-200 hover:border-gray-400'\"\r\n               (click)=\"toggleImageSelection(image)\">\r\n            \r\n            <!-- 選取指示器 -->\r\n            <div class=\"flex justify-between items-center mb-2\">\r\n              <div class=\"w-5 h-5 border-2 rounded flex items-center justify-center\"\r\n                   [class]=\"isImageSelected(image) ? 'border-blue-500 bg-blue-500' : 'border-gray-300'\">\r\n                <i *ngIf=\"isImageSelected(image)\" class=\"fas fa-check text-white text-xs\"></i>\r\n              </div>              <button class=\"btn btn-outline-info btn-xs\" (click)=\"previewImage(image, imagePreview, $event)\">\r\n                <i class=\"fas fa-eye\"></i>\r\n              </button>\r\n            </div>\r\n\r\n            <!-- 圖片預覽 -->\r\n            <div class=\"w-full h-32 bg-gray-100 rounded mb-2 flex items-center justify-center overflow-hidden\">\r\n              <img *ngIf=\"image.thumbnailUrl\" \r\n                   [src]=\"image.thumbnailUrl\" \r\n                   [alt]=\"image.name\"\r\n                   class=\"max-w-full max-h-full object-contain\" />\r\n              <div *ngIf=\"!image.thumbnailUrl\" class=\"text-gray-400 text-center\">\r\n                <i class=\"fas fa-image text-2xl mb-1\"></i>\r\n                <div class=\"text-xs\">無預覽</div>\r\n              </div>\r\n            </div>\r\n\r\n            <!-- 圖片資訊 -->\r\n            <div class=\"text-xs text-gray-600\">\r\n              <div class=\"font-medium truncate\" [title]=\"image.name\">{{ image.name }}</div>\r\n              <div class=\"text-gray-400\">{{ image.size | number }} KB</div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 空狀態 -->\r\n        <div *ngIf=\"filteredImages.length === 0\" class=\"text-center text-gray-500 py-20\">\r\n          <i class=\"fas fa-images text-4xl mb-3\"></i>\r\n          <div>找不到相符的圖片</div>\r\n        </div>\r\n      </div>\r\n    </nb-card-body>\r\n    <nb-card-footer class=\"d-flex justify-content-between align-items-center\">\r\n      <div class=\"text-sm text-gray-600\">\r\n        共 {{ availableImages.length }} 張圖片\r\n      </div>\r\n      <div class=\"d-flex gap-2\">\r\n        <button class=\"btn btn-danger btn-sm\" (click)=\"onCloseImageBinder(ref)\">取消</button>\r\n        <button class=\"btn btn-success btn-sm\" \r\n                [disabled]=\"selectedImages.length === 0\"\r\n                (click)=\"onConfirmImageSelection(ref)\">\r\n          確定選擇 ({{ selectedImages.length }})\r\n        </button>\r\n      </div>\r\n    </nb-card-footer>\r\n  </nb-card>\r\n</ng-template>\r\n\r\n<!-- 圖片預覽對話框 -->\r\n<ng-template #imagePreview let-dialog let-ref=\"dialogRef\">\r\n  <nb-card class=\"w-[800px] h-[600px]\">\r\n    <nb-card-header class=\"d-flex justify-content-between align-items-center\">\r\n      <span>圖片預覽 - {{ previewingImage?.name }}</span>\r\n      <div class=\"d-flex gap-2\">\r\n        <button class=\"btn btn-outline-primary btn-sm\" \r\n                [disabled]=\"currentPreviewIndex <= 0\"\r\n                (click)=\"previousImage()\">\r\n          <i class=\"fas fa-chevron-left\"></i> 上一張\r\n        </button>\r\n        <button class=\"btn btn-outline-primary btn-sm\" \r\n                [disabled]=\"currentPreviewIndex >= filteredImages.length - 1\"\r\n                (click)=\"nextImage()\">\r\n          下一張 <i class=\"fas fa-chevron-right\"></i>\r\n        </button>\r\n      </div>\r\n    </nb-card-header>\r\n    <nb-card-body class=\"p-0 d-flex justify-content-center align-items-center\" style=\"height: 500px;\">\r\n      <img *ngIf=\"previewingImage?.fullUrl\" \r\n           [src]=\"previewingImage.fullUrl\" \r\n           [alt]=\"previewingImage.name\"\r\n           class=\"max-w-full max-h-full object-contain\" />\r\n      <div *ngIf=\"!previewingImage?.fullUrl\" class=\"text-gray-400 text-center\">\r\n        <i class=\"fas fa-image text-4xl mb-3\"></i>\r\n        <div>圖片載入失敗</div>\r\n      </div>\r\n    </nb-card-body>\r\n    <nb-card-footer class=\"d-flex justify-content-between align-items-center\">\r\n      <div class=\"text-sm text-gray-600\">\r\n        {{ currentPreviewIndex + 1 }} / {{ filteredImages.length }}\r\n      </div>\r\n      <div class=\"d-flex gap-2\">\r\n        <button class=\"btn btn-outline-info btn-sm\" (click)=\"toggleImageSelectionInPreview()\">\r\n          {{ isImageSelected(previewingImage!) ? '取消選取' : '選取此圖片' }}\r\n        </button>\r\n        <button class=\"btn btn-danger btn-sm\" (click)=\"ref.close()\">關閉</button>\r\n      </div>\r\n    </nb-card-footer>\r\n  </nb-card>\r\n</ng-template>\r\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAM9C,SAASC,QAAQ,EAAEC,QAAQ,EAAEC,GAAG,QAAQ,MAAM;AAG9C,OAAO,KAAKC,IAAI,MAAM,MAAM;AAC5B,SAASC,YAAY,QAAQ,gCAAgC;AAC7D,SAASC,aAAa,QAAQ,qCAAqC;;;;;;;;;;;;;;ICAvDC,EAAA,CAAAC,cAAA,oBAA4E;IAC1ED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAFwCH,EAAA,CAAAI,UAAA,UAAAC,YAAA,CAAAC,GAAA,CAAuB;IACzEN,EAAA,CAAAO,SAAA,EACF;IADEP,EAAA,CAAAQ,kBAAA,MAAAH,YAAA,CAAAI,cAAA,MACF;;;;;;IAuCFT,EAAA,CAAAC,cAAA,iBAA8F;IAAnCD,EAAA,CAAAU,UAAA,mBAAAC,qEAAA;MAAAX,EAAA,CAAAY,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CAASF,MAAA,CAAAG,sBAAA,EAAwB;IAAA,EAAC;IAACjB,EAAA,CAAAE,MAAA,oBAAG;IAAAF,EAAA,CAAAkB,SAAA,YAC9D;IAAAlB,EAAA,CAAAG,YAAA,EAAS;;;;;;IAC5CH,EAAA,CAAAC,cAAA,iBAAoF;IAAnBD,EAAA,CAAAU,UAAA,mBAAAS,qEAAA;MAAAnB,EAAA,CAAAY,aAAA,CAAAQ,GAAA;MAAA,MAAAN,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CAASF,MAAA,CAAAO,MAAA,EAAQ;IAAA,EAAC;IACjFrB,EAAA,CAAAE,MAAA,qBAAG;IAAAF,EAAA,CAAAkB,SAAA,YAA6B;IAAAlB,EAAA,CAAAG,YAAA,EAAS;;;;;;IAC3CH,EAAA,CAAAC,cAAA,iBAAsF;IAAzBD,EAAA,CAAAU,UAAA,mBAAAY,qEAAA;MAAAtB,EAAA,CAAAY,aAAA,CAAAW,GAAA;MAAA,MAAAT,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,MAAAS,SAAA,GAAAxB,EAAA,CAAAyB,WAAA;MAAA,OAAAzB,EAAA,CAAAgB,WAAA,CAASF,MAAA,CAAAY,MAAA,CAAAF,SAAA,CAAc;IAAA,EAAC;IAACxB,EAAA,CAAAE,MAAA,gCAAK;IAAAF,EAAA,CAAAkB,SAAA,YAC/D;IAAAlB,EAAA,CAAAG,YAAA,EAAS;;;;;;IACrCH,EAAA,CAAAC,cAAA,iBAAoF;IAA5BD,EAAA,CAAAU,UAAA,mBAAAiB,qEAAA;MAAA3B,EAAA,CAAAY,aAAA,CAAAgB,GAAA;MAAA5B,EAAA,CAAAe,aAAA;MAAA,MAAAc,YAAA,GAAA7B,EAAA,CAAAyB,WAAA;MAAA,OAAAzB,EAAA,CAAAgB,WAAA,CAASa,YAAA,CAAAC,KAAA,EAAiB;IAAA,EAAC;IAAE9B,EAAA,CAAAE,MAAA,iCAAK;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;IAoBjGH,EAAA,CAAAC,cAAA,aAAwD;IAAAD,EAAA,CAAAE,MAAA,mBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;;;IAmBzDH,EADF,CAAAC,cAAA,eAAiD,cACkD;IAAlDD,EAAA,CAAAU,UAAA,mBAAAqB,8EAAA;MAAA/B,EAAA,CAAAY,aAAA,CAAAoB,IAAA;MAAA,MAAAC,QAAA,GAAAjC,EAAA,CAAAe,aAAA,GAAAmB,SAAA;MAAA,MAAApB,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CAASF,MAAA,CAAAqB,SAAA,CAAAF,QAAA,CAAAG,QAAA,EAAAtB,MAAA,CAAAuB,WAAA,CAAqC;IAAA,EAAC;IAChGrC,EADE,CAAAG,YAAA,EAAiG,EAC5F;;;;IADAH,EAAA,CAAAO,SAAA,EAAqB;IAArBP,EAAA,CAAAI,UAAA,QAAA6B,QAAA,CAAAG,QAAA,EAAApC,EAAA,CAAAsC,aAAA,CAAqB;;;;;;IAW1BtC,EADF,CAAAC,cAAA,eAAqD,cACsD;IAAtDD,EAAA,CAAAU,UAAA,mBAAA6B,8EAAA;MAAAvC,EAAA,CAAAY,aAAA,CAAA4B,IAAA;MAAA,MAAAP,QAAA,GAAAjC,EAAA,CAAAe,aAAA,GAAAmB,SAAA;MAAA,MAAApB,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CAASF,MAAA,CAAAqB,SAAA,CAAAF,QAAA,CAAAQ,YAAA,EAAA3B,MAAA,CAAAuB,WAAA,CAAyC;IAAA,EAAC;IACxGrC,EADE,CAAAG,YAAA,EAAyG,EACpG;;;;IADAH,EAAA,CAAAO,SAAA,EAAyB;IAAzBP,EAAA,CAAAI,UAAA,QAAA6B,QAAA,CAAAQ,YAAA,EAAAzC,EAAA,CAAAsC,aAAA,CAAyB;;;;;IAKpCtC,EAAA,CAAAC,cAAA,SAAoC;IAAAD,EAAA,CAAAE,MAAA,GAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;IAArBH,EAAA,CAAAO,SAAA,EAAgB;IAAhBP,EAAA,CAAA0C,iBAAA,CAAAT,QAAA,CAAAU,MAAA,CAAgB;;;;;;IAElD3C,EAAA,CAAAC,cAAA,iBACiB;IADkCD,EAAA,CAAAU,UAAA,mBAAAkC,mFAAA;MAAA5C,EAAA,CAAAY,aAAA,CAAAiC,IAAA;MAAA,MAAAZ,QAAA,GAAAjC,EAAA,CAAAe,aAAA,GAAAmB,SAAA;MAAA,MAAApB,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,MAAAS,SAAA,GAAAxB,EAAA,CAAAyB,WAAA;MAAA,OAAAzB,EAAA,CAAAgB,WAAA,CAASF,MAAA,CAAAgC,kBAAA,CAAAb,QAAA,EAAAT,SAAA,CAAgC;IAAA,EAAC;IAC5ExB,EAAA,CAAAE,MAAA,mBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;IAhC9BH,EADF,CAAAC,cAAA,SAAsD,SAChD;IAAAD,EAAA,CAAAE,MAAA,GAAa;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAEtBH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,GAAe;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACxBH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,GAAe;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACxBH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,GAAmB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC5BH,EAAA,CAAAC,cAAA,SAAmD;IAAAD,EAAA,CAAAE,MAAA,IAAqB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAGzEH,EAFJ,CAAAC,cAAA,UAAI,eACgC,gBAEuB;IACrDD,EAAA,CAAAE,MAAA,IACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACPH,EAAA,CAAA+C,UAAA,KAAAC,wDAAA,mBAAiD;IAIrDhD,EADE,CAAAG,YAAA,EAAM,EACH;IAGDH,EAFJ,CAAAC,cAAA,UAAI,eACgC,gBAE2B;IACzDD,EAAA,CAAAE,MAAA,IACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACPH,EAAA,CAAA+C,UAAA,KAAAE,wDAAA,mBAAqD;IAIzDjD,EADE,CAAAG,YAAA,EAAM,EACH;IACLH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAE,MAAA,IAAsB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC/BH,EAAA,CAAA+C,UAAA,KAAAG,sDAAA,iBAAoC;IACpClD,EAAA,CAAAC,cAAA,cAA6B;IAC3BD,EAAA,CAAA+C,UAAA,KAAAI,0DAAA,qBACiB;IAErBnD,EADE,CAAAG,YAAA,EAAK,EACF;;;;;IAlCCH,EAAA,CAAAO,SAAA,GAAa;IAAbP,EAAA,CAAA0C,iBAAA,CAAAT,QAAA,CAAAmB,GAAA,CAAa;IAEbpD,EAAA,CAAAO,SAAA,GAAe;IAAfP,EAAA,CAAA0C,iBAAA,CAAAT,QAAA,CAAAoB,KAAA,CAAe;IACfrD,EAAA,CAAAO,SAAA,GAAe;IAAfP,EAAA,CAAA0C,iBAAA,CAAAT,QAAA,CAAAqB,KAAA,CAAe;IACftD,EAAA,CAAAO,SAAA,GAAmB;IAAnBP,EAAA,CAAA0C,iBAAA,CAAAT,QAAA,CAAAsB,SAAA,CAAmB;IACnBvD,EAAA,CAAAO,SAAA,EAA8C;IAA9CP,EAAA,CAAAwD,UAAA,EAAAvB,QAAA,CAAAwB,UAAA,qBAA8C;IAACzD,EAAA,CAAAO,SAAA,EAAqB;IAArBP,EAAA,CAAA0C,iBAAA,CAAAT,QAAA,CAAAyB,WAAA,CAAqB;IAIlE1D,EAAA,CAAAO,SAAA,GAAoD;IAApDP,EAAA,CAAA2D,UAAA,CAAA1B,QAAA,CAAAG,QAAA,8BAAoD;IACpDpC,EAAA,CAAAO,SAAA,EACF;IADEP,EAAA,CAAAQ,kBAAA,MAAAyB,QAAA,CAAA2B,UAAA,MACF;IAC4B5D,EAAA,CAAAO,SAAA,EAAmB;IAAnBP,EAAA,CAAAI,UAAA,SAAA6B,QAAA,CAAAG,QAAA,CAAmB;IAQ7CpC,EAAA,CAAAO,SAAA,GAAwD;IAAxDP,EAAA,CAAA2D,UAAA,CAAA1B,QAAA,CAAAQ,YAAA,8BAAwD;IACxDzC,EAAA,CAAAO,SAAA,EACF;IADEP,EAAA,CAAAQ,kBAAA,MAAAyB,QAAA,CAAA4B,cAAA,MACF;IAC4B7D,EAAA,CAAAO,SAAA,EAAuB;IAAvBP,EAAA,CAAAI,UAAA,SAAA6B,QAAA,CAAAQ,YAAA,CAAuB;IAKnDzC,EAAA,CAAAO,SAAA,GAAsB;IAAtBP,EAAA,CAAA0C,iBAAA,CAAAT,QAAA,CAAA6B,YAAA,CAAsB;IACrB9D,EAAA,CAAAO,SAAA,EAA6B;IAA7BP,EAAA,CAAAI,UAAA,SAAA6B,QAAA,CAAA8B,UAAA,SAA6B;IAG7B/D,EAAA,CAAAO,SAAA,GAAY;IAAZP,EAAA,CAAAI,UAAA,SAAAU,MAAA,CAAAkD,MAAA,CAAY;;;;;IAlCrBhE,EAAA,CAAAC,cAAA,YAA+D;IAC7DD,EAAA,CAAA+C,UAAA,IAAAkB,gDAAA,mBAAsD;IAoCxDjE,EAAA,CAAAG,YAAA,EAAQ;;;;IApCeH,EAAA,CAAAO,SAAA,EAAkB;IAAlBP,EAAA,CAAAI,UAAA,YAAAU,MAAA,CAAAoD,YAAA,CAAkB;;;;;;IAiD7ClE,EADF,CAAAC,cAAA,kBAA2B,qBACT;IACdD,EAAA,CAAAE,MAAA,4DACF;IAAAF,EAAA,CAAAG,YAAA,EAAiB;IAEfH,EADF,CAAAC,cAAA,uBAA2B,aACH;IAAAD,EAAA,CAAAE,MAAA,yNAAmC;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAG1DH,EAFJ,CAAAC,cAAA,cAAyB,cACQ,gBACW;IAAAD,EAAA,CAAAE,MAAA,mBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAClDH,EAAA,CAAAC,cAAA,iBACyC;IAAvCD,EAAA,CAAAmE,gBAAA,2BAAAC,kFAAAC,MAAA;MAAArE,EAAA,CAAAY,aAAA,CAAA0D,IAAA;MAAA,MAAAxD,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAAf,EAAA,CAAAuE,kBAAA,CAAAzD,MAAA,CAAA0D,gBAAA,CAAAnB,KAAA,EAAAgB,MAAA,MAAAvD,MAAA,CAAA0D,gBAAA,CAAAnB,KAAA,GAAAgB,MAAA;MAAA,OAAArE,EAAA,CAAAgB,WAAA,CAAAqD,MAAA;IAAA,EAAoC;IACxCrE,EAFE,CAAAG,YAAA,EACyC,EACrC;IAGJH,EADF,CAAAC,cAAA,eAAoC,iBACM;IAAAD,EAAA,CAAAE,MAAA,oBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAClDH,EAAA,CAAAC,cAAA,iBACyC;IAAvCD,EAAA,CAAAmE,gBAAA,2BAAAM,kFAAAJ,MAAA;MAAArE,EAAA,CAAAY,aAAA,CAAA0D,IAAA;MAAA,MAAAxD,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAAf,EAAA,CAAAuE,kBAAA,CAAAzD,MAAA,CAAA0D,gBAAA,CAAAlB,KAAA,EAAAe,MAAA,MAAAvD,MAAA,CAAA0D,gBAAA,CAAAlB,KAAA,GAAAe,MAAA;MAAA,OAAArE,EAAA,CAAAgB,WAAA,CAAAqD,MAAA;IAAA,EAAoC;IACxCrE,EAFE,CAAAG,YAAA,EACyC,EACrC;IAEJH,EADF,CAAAC,cAAA,eAAoC,iBACM;IAAAD,EAAA,CAAAE,MAAA,oBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAClDH,EAAA,CAAAC,cAAA,iBAC6C;IAA3CD,EAAA,CAAAmE,gBAAA,2BAAAO,kFAAAL,MAAA;MAAArE,EAAA,CAAAY,aAAA,CAAA0D,IAAA;MAAA,MAAAxD,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAAf,EAAA,CAAAuE,kBAAA,CAAAzD,MAAA,CAAA0D,gBAAA,CAAAjB,SAAA,EAAAc,MAAA,MAAAvD,MAAA,CAAA0D,gBAAA,CAAAjB,SAAA,GAAAc,MAAA;MAAA,OAAArE,EAAA,CAAAgB,WAAA,CAAAqD,MAAA;IAAA,EAAwC;IAC5CrE,EAFE,CAAAG,YAAA,EAC6C,EACzC;IAGJH,EADF,CAAAC,cAAA,eAAoC,iBACM;IAAAD,EAAA,CAAAE,MAAA,4CAAM;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACtDH,EAAA,CAAAC,cAAA,iBAC+C;IAA7CD,EAAA,CAAAmE,gBAAA,2BAAAQ,kFAAAN,MAAA;MAAArE,EAAA,CAAAY,aAAA,CAAA0D,IAAA;MAAA,MAAAxD,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAAf,EAAA,CAAAuE,kBAAA,CAAAzD,MAAA,CAAA0D,gBAAA,CAAAd,WAAA,EAAAW,MAAA,MAAAvD,MAAA,CAAA0D,gBAAA,CAAAd,WAAA,GAAAW,MAAA;MAAA,OAAArE,EAAA,CAAAgB,WAAA,CAAAqD,MAAA;IAAA,EAA0C;IAC9CrE,EAFE,CAAAG,YAAA,EAC+C,EAC3C;IACJH,EADY,CAAAC,cAAA,eAAoC,iBACR;IAAAD,EAAA,CAAAE,MAAA,4CAAM;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAEpDH,EADF,CAAAC,cAAA,eAA+B,iBAEiB;IAA5CD,EAAA,CAAAmE,gBAAA,2BAAAS,kFAAAP,MAAA;MAAArE,EAAA,CAAAY,aAAA,CAAA0D,IAAA;MAAA,MAAAxD,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAAf,EAAA,CAAAuE,kBAAA,CAAAzD,MAAA,CAAA0D,gBAAA,CAAAZ,UAAA,EAAAS,MAAA,MAAAvD,MAAA,CAAA0D,gBAAA,CAAAZ,UAAA,GAAAS,MAAA;MAAA,OAAArE,EAAA,CAAAgB,WAAA,CAAAqD,MAAA;IAAA,EAAyC;IAD3CrE,EAAA,CAAAG,YAAA,EAC8C;IAC9CH,EAAA,CAAAC,cAAA,kBAAmF;IAAvCD,EAAA,CAAAU,UAAA,mBAAAmE,2EAAA;MAAA7E,EAAA,CAAAY,aAAA,CAAA0D,IAAA;MAAA,MAAAxD,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,MAAA+D,eAAA,GAAA9E,EAAA,CAAAyB,WAAA;MAAA,OAAAzB,EAAA,CAAAgB,WAAA,CAASF,MAAA,CAAAiE,eAAA,CAAAD,eAAA,CAA4B;IAAA,EAAC;IAChF9E,EAAA,CAAAE,MAAA,kCAAK;IAAAF,EAAA,CAAAkB,SAAA,aAA6B;IAGxClB,EAFI,CAAAG,YAAA,EAAS,EACL,EACF;IAGJH,EADF,CAAAC,cAAA,eAAoC,iBACT;IAAAD,EAAA,CAAAE,MAAA,gCAAI;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACrCH,EAAA,CAAAC,cAAA,oBACiE;IAD/CD,EAAA,CAAAmE,gBAAA,2BAAAa,qFAAAX,MAAA;MAAArE,EAAA,CAAAY,aAAA,CAAA0D,IAAA;MAAA,MAAAxD,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAAf,EAAA,CAAAuE,kBAAA,CAAAzD,MAAA,CAAA0D,gBAAA,CAAAV,YAAA,EAAAO,MAAA,MAAAvD,MAAA,CAAA0D,gBAAA,CAAAV,YAAA,GAAAO,MAAA;MAAA,OAAArE,EAAA,CAAAgB,WAAA,CAAAqD,MAAA;IAAA,EAA2C;IAE/DrE,EADmE,CAAAG,YAAA,EAAW,EACxE;IAGJH,EADF,CAAAC,cAAA,eAAoC,iBACT;IAAAD,EAAA,CAAAE,MAAA,oBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACnCH,EAAA,CAAAC,cAAA,iBAC0C;IAAxCD,EAAA,CAAAmE,gBAAA,2BAAAc,kFAAAZ,MAAA;MAAArE,EAAA,CAAAY,aAAA,CAAA0D,IAAA;MAAA,MAAAxD,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAAf,EAAA,CAAAuE,kBAAA,CAAAzD,MAAA,CAAA0D,gBAAA,CAAA7B,MAAA,EAAA0B,MAAA,MAAAvD,MAAA,CAAA0D,gBAAA,CAAA7B,MAAA,GAAA0B,MAAA;MAAA,OAAArE,EAAA,CAAAgB,WAAA,CAAAqD,MAAA;IAAA,EAAqC;IAG7CrE,EAJM,CAAAG,YAAA,EAC0C,EACtC,EACF,EACO;IAEbH,EADF,CAAAC,cAAA,0BAAsD,kBACc;IAAvBD,EAAA,CAAAU,UAAA,mBAAAwE,2EAAA;MAAA,MAAAC,OAAA,GAAAnF,EAAA,CAAAY,aAAA,CAAA0D,IAAA,EAAAc,SAAA;MAAA,MAAAtE,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CAASF,MAAA,CAAAuE,OAAA,CAAAF,OAAA,CAAY;IAAA,EAAC;IAACnF,EAAA,CAAAE,MAAA,oBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAC7EH,EAAA,CAAAC,cAAA,kBAA+D;IAAxBD,EAAA,CAAAU,UAAA,mBAAA4E,2EAAA;MAAA,MAAAH,OAAA,GAAAnF,EAAA,CAAAY,aAAA,CAAA0D,IAAA,EAAAc,SAAA;MAAA,MAAAtE,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CAASF,MAAA,CAAAyE,QAAA,CAAAJ,OAAA,CAAa;IAAA,EAAC;IAACnF,EAAA,CAAAE,MAAA,oBAAE;IAErEF,EAFqE,CAAAG,YAAA,EAAS,EAC3D,EACT;;;;IA9CAH,EAAA,CAAAO,SAAA,IAAoC;IAApCP,EAAA,CAAAwF,gBAAA,YAAA1E,MAAA,CAAA0D,gBAAA,CAAAnB,KAAA,CAAoC;IAMpCrD,EAAA,CAAAO,SAAA,GAAoC;IAApCP,EAAA,CAAAwF,gBAAA,YAAA1E,MAAA,CAAA0D,gBAAA,CAAAlB,KAAA,CAAoC;IAKpCtD,EAAA,CAAAO,SAAA,GAAwC;IAAxCP,EAAA,CAAAwF,gBAAA,YAAA1E,MAAA,CAAA0D,gBAAA,CAAAjB,SAAA,CAAwC;IAMxCvD,EAAA,CAAAO,SAAA,GAA0C;IAA1CP,EAAA,CAAAwF,gBAAA,YAAA1E,MAAA,CAAA0D,gBAAA,CAAAd,WAAA,CAA0C;IAKxC1D,EAAA,CAAAO,SAAA,GAAyC;IAAzCP,EAAA,CAAAwF,gBAAA,YAAA1E,MAAA,CAAA0D,gBAAA,CAAAZ,UAAA,CAAyC;IAS3B5D,EAAA,CAAAO,SAAA,GAA2C;IAA3CP,EAAA,CAAAwF,gBAAA,YAAA1E,MAAA,CAAA0D,gBAAA,CAAAV,YAAA,CAA2C;IAAC9D,EAAA,CAAAI,UAAA,WAAU;IAOtEJ,EAAA,CAAAO,SAAA,GAAqC;IAArCP,EAAA,CAAAwF,gBAAA,YAAA1E,MAAA,CAAA0D,gBAAA,CAAA7B,MAAA,CAAqC;;;;;IAqDjC3C,EAAA,CAAAkB,SAAA,aAA8E;;;;;IAQhFlB,EAAA,CAAAkB,SAAA,eAGoD;;;;IAD/ClB,EADA,CAAAI,UAAA,QAAAqF,SAAA,CAAAC,YAAA,EAAA1F,EAAA,CAAAsC,aAAA,CAA0B,QAAAmD,SAAA,CAAAE,IAAA,CACR;;;;;IAEvB3F,EAAA,CAAAC,cAAA,eAAmE;IACjED,EAAA,CAAAkB,SAAA,aAA0C;IAC1ClB,EAAA,CAAAC,cAAA,eAAqB;IAAAD,EAAA,CAAAE,MAAA,yBAAG;IAC1BF,EAD0B,CAAAG,YAAA,EAAM,EAC1B;;;;;;IAxBVH,EAAA,CAAAC,cAAA,cAG2C;IAAtCD,EAAA,CAAAU,UAAA,mBAAAkF,8EAAA;MAAA,MAAAH,SAAA,GAAAzF,EAAA,CAAAY,aAAA,CAAAiF,IAAA,EAAA3D,SAAA;MAAA,MAAApB,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CAASF,MAAA,CAAAgF,oBAAA,CAAAL,SAAA,CAA2B;IAAA,EAAC;IAItCzF,EADF,CAAAC,cAAA,cAAoD,cAEwC;IACxFD,EAAA,CAAA+C,UAAA,IAAAgD,4DAAA,gBAA0E;IAC5E/F,EAAA,CAAAG,YAAA,EAAM;IAAcH,EAAA,CAAAC,cAAA,iBAAgG;IAApDD,EAAA,CAAAU,UAAA,mBAAAsF,iFAAA3B,MAAA;MAAA,MAAAoB,SAAA,GAAAzF,EAAA,CAAAY,aAAA,CAAAiF,IAAA,EAAA3D,SAAA;MAAA,MAAApB,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,MAAAkF,gBAAA,GAAAjG,EAAA,CAAAyB,WAAA;MAAA,OAAAzB,EAAA,CAAAgB,WAAA,CAASF,MAAA,CAAAoF,YAAA,CAAAT,SAAA,EAAAQ,gBAAA,EAAA5B,MAAA,CAAyC;IAAA,EAAC;IACjHrE,EAAA,CAAAkB,SAAA,YAA0B;IAE9BlB,EADE,CAAAG,YAAA,EAAS,EACL;IAGNH,EAAA,CAAAC,cAAA,cAAmG;IAKjGD,EAJA,CAAA+C,UAAA,IAAAoD,8DAAA,kBAGoD,IAAAC,8DAAA,kBACe;IAIrEpG,EAAA,CAAAG,YAAA,EAAM;IAIJH,EADF,CAAAC,cAAA,cAAmC,gBACsB;IAAAD,EAAA,CAAAE,MAAA,IAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAC7EH,EAAA,CAAAC,cAAA,gBAA2B;IAAAD,EAAA,CAAAE,MAAA,IAA4B;;IAE3DF,EAF2D,CAAAG,YAAA,EAAM,EACzD,EACF;;;;;IA9BDH,EAAA,CAAA2D,UAAA,CAAA7C,MAAA,CAAAuF,eAAA,CAAAZ,SAAA,2EAAyG;IAMrGzF,EAAA,CAAAO,SAAA,GAAoF;IAApFP,EAAA,CAAA2D,UAAA,CAAA7C,MAAA,CAAAuF,eAAA,CAAAZ,SAAA,sDAAoF;IACnFzF,EAAA,CAAAO,SAAA,EAA4B;IAA5BP,EAAA,CAAAI,UAAA,SAAAU,MAAA,CAAAuF,eAAA,CAAAZ,SAAA,EAA4B;IAQ5BzF,EAAA,CAAAO,SAAA,GAAwB;IAAxBP,EAAA,CAAAI,UAAA,SAAAqF,SAAA,CAAAC,YAAA,CAAwB;IAIxB1F,EAAA,CAAAO,SAAA,EAAyB;IAAzBP,EAAA,CAAAI,UAAA,UAAAqF,SAAA,CAAAC,YAAA,CAAyB;IAQG1F,EAAA,CAAAO,SAAA,GAAoB;IAApBP,EAAA,CAAAI,UAAA,UAAAqF,SAAA,CAAAE,IAAA,CAAoB;IAAC3F,EAAA,CAAAO,SAAA,EAAgB;IAAhBP,EAAA,CAAA0C,iBAAA,CAAA+C,SAAA,CAAAE,IAAA,CAAgB;IAC5C3F,EAAA,CAAAO,SAAA,GAA4B;IAA5BP,EAAA,CAAAQ,kBAAA,KAAAR,EAAA,CAAAsG,WAAA,SAAAb,SAAA,CAAAc,IAAA,SAA4B;;;;;IAM7DvG,EAAA,CAAAC,cAAA,eAAiF;IAC/ED,EAAA,CAAAkB,SAAA,aAA2C;IAC3ClB,EAAA,CAAAC,cAAA,UAAK;IAAAD,EAAA,CAAAE,MAAA,uDAAQ;IACfF,EADe,CAAAG,YAAA,EAAM,EACf;;;;;;IAtEVH,EADF,CAAAC,cAAA,kBAAqC,qBACnB;IACdD,EAAA,CAAAE,MAAA,wEACF;IAAAF,EAAA,CAAAG,YAAA,EAAiB;IAKXH,EAJN,CAAAC,cAAA,uBAAqD,cAEtB,cACP,gBAE8C;IAAzDD,EAAA,CAAAmE,gBAAA,2BAAAqC,iFAAAnC,MAAA;MAAArE,EAAA,CAAAY,aAAA,CAAA6F,IAAA;MAAA,MAAA3F,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAAf,EAAA,CAAAuE,kBAAA,CAAAzD,MAAA,CAAA4F,eAAA,EAAArC,MAAA,MAAAvD,MAAA,CAAA4F,eAAA,GAAArC,MAAA;MAAA,OAAArE,EAAA,CAAAgB,WAAA,CAAAqD,MAAA;IAAA,EAA6B;IAACrE,EAAA,CAAAU,UAAA,mBAAAiG,yEAAA;MAAA3G,EAAA,CAAAY,aAAA,CAAA6F,IAAA;MAAA,MAAA3F,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CAASF,MAAA,CAAA8F,YAAA,EAAc;IAAA,EAAC;IAC/D5G,EAFE,CAAAG,YAAA,EACgE,EAC5D;IACNH,EAAA,CAAAC,cAAA,iBAAoD;IAAvBD,EAAA,CAAAU,UAAA,mBAAAmG,0EAAA;MAAA7G,EAAA,CAAAY,aAAA,CAAA6F,IAAA;MAAA,MAAA3F,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CAASF,MAAA,CAAAgG,UAAA,EAAY;IAAA,EAAC;IACjD9G,EAAA,CAAAE,MAAA,iCAAK;IAAAF,EAAA,CAAAkB,SAAA,YAA8B;IAEvClB,EADE,CAAAG,YAAA,EAAS,EACL;IAIJH,EADF,CAAAC,cAAA,eAA6B,kBACgD;IAA5BD,EAAA,CAAAU,UAAA,mBAAAqG,2EAAA;MAAA/G,EAAA,CAAAY,aAAA,CAAA6F,IAAA;MAAA,MAAA3F,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CAASF,MAAA,CAAAkG,eAAA,EAAiB;IAAA,EAAC;IACxEhH,EAAA,CAAAE,MAAA,sBAAG;IAAAF,EAAA,CAAAkB,SAAA,aAAmC;IACxClB,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,kBAA+E;IAA9BD,EAAA,CAAAU,UAAA,mBAAAuG,2EAAA;MAAAjH,EAAA,CAAAY,aAAA,CAAA6F,IAAA;MAAA,MAAA3F,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CAASF,MAAA,CAAAoG,iBAAA,EAAmB;IAAA,EAAC;IAC5ElH,EAAA,CAAAE,MAAA,kCAAK;IAAAF,EAAA,CAAAkB,SAAA,aAA6B;IACpClB,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,eAA2C;IACzCD,EAAA,CAAAE,MAAA,IACF;IACFF,EADE,CAAAG,YAAA,EAAM,EACF;IAIJH,EADF,CAAAC,cAAA,eAA0D,eACpB;IAClCD,EAAA,CAAA+C,UAAA,KAAAoE,wDAAA,oBAG2C;IA8B7CnH,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAA+C,UAAA,KAAAqE,wDAAA,kBAAiF;IAKrFpH,EADE,CAAAG,YAAA,EAAM,EACO;IAEbH,EADF,CAAAC,cAAA,0BAA0E,eACrC;IACjCD,EAAA,CAAAE,MAAA,IACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAEJH,EADF,CAAAC,cAAA,eAA0B,kBACgD;IAAlCD,EAAA,CAAAU,UAAA,mBAAA2G,2EAAA;MAAA,MAAAC,OAAA,GAAAtH,EAAA,CAAAY,aAAA,CAAA6F,IAAA,EAAArB,SAAA;MAAA,MAAAtE,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CAASF,MAAA,CAAAyG,kBAAA,CAAAD,OAAA,CAAuB;IAAA,EAAC;IAACtH,EAAA,CAAAE,MAAA,oBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACnFH,EAAA,CAAAC,cAAA,kBAE+C;IAAvCD,EAAA,CAAAU,UAAA,mBAAA8G,2EAAA;MAAA,MAAAF,OAAA,GAAAtH,EAAA,CAAAY,aAAA,CAAA6F,IAAA,EAAArB,SAAA;MAAA,MAAAtE,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CAASF,MAAA,CAAA2G,uBAAA,CAAAH,OAAA,CAA4B;IAAA,EAAC;IAC5CtH,EAAA,CAAAE,MAAA,IACF;IAGNF,EAHM,CAAAG,YAAA,EAAS,EACL,EACS,EACT;;;;IA9EKH,EAAA,CAAAO,SAAA,GAA6B;IAA7BP,EAAA,CAAAwF,gBAAA,YAAA1E,MAAA,CAAA4F,eAAA,CAA6B;IAgBpC1G,EAAA,CAAAO,SAAA,IACF;IADEP,EAAA,CAAAQ,kBAAA,0BAAAM,MAAA,CAAA4G,cAAA,CAAAC,MAAA,yBACF;IAMyB3H,EAAA,CAAAO,SAAA,GAAiB;IAAjBP,EAAA,CAAAI,UAAA,YAAAU,MAAA,CAAA8G,cAAA,CAAiB;IAoCpC5H,EAAA,CAAAO,SAAA,EAAiC;IAAjCP,EAAA,CAAAI,UAAA,SAAAU,MAAA,CAAA8G,cAAA,CAAAD,MAAA,OAAiC;IAQvC3H,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAAQ,kBAAA,aAAAM,MAAA,CAAA+G,eAAA,CAAAF,MAAA,yBACF;IAIU3H,EAAA,CAAAO,SAAA,GAAwC;IAAxCP,EAAA,CAAAI,UAAA,aAAAU,MAAA,CAAA4G,cAAA,CAAAC,MAAA,OAAwC;IAE9C3H,EAAA,CAAAO,SAAA,EACF;IADEP,EAAA,CAAAQ,kBAAA,gCAAAM,MAAA,CAAA4G,cAAA,CAAAC,MAAA,OACF;;;;;IAyBF3H,EAAA,CAAAkB,SAAA,eAGoD;;;;IAD/ClB,EADA,CAAAI,UAAA,QAAAU,MAAA,CAAAgH,eAAA,CAAAC,OAAA,EAAA/H,EAAA,CAAAsC,aAAA,CAA+B,QAAAxB,MAAA,CAAAgH,eAAA,CAAAnC,IAAA,CACH;;;;;IAEjC3F,EAAA,CAAAC,cAAA,eAAyE;IACvED,EAAA,CAAAkB,SAAA,aAA0C;IAC1ClB,EAAA,CAAAC,cAAA,UAAK;IAAAD,EAAA,CAAAE,MAAA,2CAAM;IACbF,EADa,CAAAG,YAAA,EAAM,EACb;;;;;;IAtBNH,EAFJ,CAAAC,cAAA,mBAAqC,yBACuC,WAClE;IAAAD,EAAA,CAAAE,MAAA,GAAkC;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAE7CH,EADF,CAAAC,cAAA,cAA0B,kBAGU;IAA1BD,EAAA,CAAAU,UAAA,mBAAAsH,0EAAA;MAAAhI,EAAA,CAAAY,aAAA,CAAAqH,IAAA;MAAA,MAAAnH,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CAASF,MAAA,CAAAoH,aAAA,EAAe;IAAA,EAAC;IAC/BlI,EAAA,CAAAkB,SAAA,aAAmC;IAAClB,EAAA,CAAAE,MAAA,2BACtC;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,kBAE8B;IAAtBD,EAAA,CAAAU,UAAA,mBAAAyH,0EAAA;MAAAnI,EAAA,CAAAY,aAAA,CAAAqH,IAAA;MAAA,MAAAnH,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CAASF,MAAA,CAAAsH,SAAA,EAAW;IAAA,EAAC;IAC3BpI,EAAA,CAAAE,MAAA,2BAAI;IAAAF,EAAA,CAAAkB,SAAA,cAAoC;IAG9ClB,EAFI,CAAAG,YAAA,EAAS,EACL,EACS;IACjBH,EAAA,CAAAC,cAAA,yBAAkG;IAKhGD,EAJA,CAAA+C,UAAA,KAAAsF,wDAAA,kBAGoD,KAAAC,wDAAA,kBACqB;IAI3EtI,EAAA,CAAAG,YAAA,EAAe;IAEbH,EADF,CAAAC,cAAA,0BAA0E,eACrC;IACjCD,EAAA,CAAAE,MAAA,IACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAEJH,EADF,CAAAC,cAAA,eAA0B,kBAC8D;IAA1CD,EAAA,CAAAU,UAAA,mBAAA6H,2EAAA;MAAAvI,EAAA,CAAAY,aAAA,CAAAqH,IAAA;MAAA,MAAAnH,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CAASF,MAAA,CAAA0H,6BAAA,EAA+B;IAAA,EAAC;IACnFxI,EAAA,CAAAE,MAAA,IACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,kBAA4D;IAAtBD,EAAA,CAAAU,UAAA,mBAAA+H,2EAAA;MAAA,MAAAC,OAAA,GAAA1I,EAAA,CAAAY,aAAA,CAAAqH,IAAA,EAAA7C,SAAA;MAAA,OAAApF,EAAA,CAAAgB,WAAA,CAAS0H,OAAA,CAAAC,KAAA,EAAW;IAAA,EAAC;IAAC3I,EAAA,CAAAE,MAAA,oBAAE;IAGpEF,EAHoE,CAAAG,YAAA,EAAS,EACnE,EACS,EACT;;;;IAnCAH,EAAA,CAAAO,SAAA,GAAkC;IAAlCP,EAAA,CAAAQ,kBAAA,gCAAAM,MAAA,CAAAgH,eAAA,kBAAAhH,MAAA,CAAAgH,eAAA,CAAAnC,IAAA,KAAkC;IAG9B3F,EAAA,CAAAO,SAAA,GAAqC;IAArCP,EAAA,CAAAI,UAAA,aAAAU,MAAA,CAAA8H,mBAAA,MAAqC;IAKrC5I,EAAA,CAAAO,SAAA,GAA6D;IAA7DP,EAAA,CAAAI,UAAA,aAAAU,MAAA,CAAA8H,mBAAA,IAAA9H,MAAA,CAAA8G,cAAA,CAAAD,MAAA,KAA6D;IAOjE3H,EAAA,CAAAO,SAAA,GAA8B;IAA9BP,EAAA,CAAAI,UAAA,SAAAU,MAAA,CAAAgH,eAAA,kBAAAhH,MAAA,CAAAgH,eAAA,CAAAC,OAAA,CAA8B;IAI9B/H,EAAA,CAAAO,SAAA,EAA+B;IAA/BP,EAAA,CAAAI,UAAA,WAAAU,MAAA,CAAAgH,eAAA,kBAAAhH,MAAA,CAAAgH,eAAA,CAAAC,OAAA,EAA+B;IAOnC/H,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAA6I,kBAAA,MAAA/H,MAAA,CAAA8H,mBAAA,aAAA9H,MAAA,CAAA8G,cAAA,CAAAD,MAAA,MACF;IAGI3H,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAAQ,kBAAA,MAAAM,MAAA,CAAAuF,eAAA,CAAAvF,MAAA,CAAAgH,eAAA,uEACF;;;AD7RR,OAAM,MAAOgB,yBAA0B,SAAQ/I,aAAa;EAuC1DgJ,YACUC,MAAmB,EACnBC,aAA8B,EAC9BC,OAAuB,EACvBC,KAAuB,EACvBC,iBAAmC,EACnCC,gBAAiC,EACjCC,eAA+B;IAEvC,KAAK,CAACN,MAAM,CAAC;IARL,KAAAA,MAAM,GAANA,MAAM;IACN,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,eAAe,GAAfA,eAAe;IA7CzB,KAAAC,KAAK,GAAG,IAAI;IAKZ,KAAAC,cAAc,GAA8B,EAAE;IAG9C,KAAAC,eAAe,GAAG,CAChB;MACEC,KAAK,EAAE,IAAI;MACXC,KAAK,EAAE;KACR,EACD;MACED,KAAK,EAAE,KAAK;MACZC,KAAK,EAAE;KACR,EACD;MACED,KAAK,EAAE,IAAI;MACXC,KAAK,EAAE;KACR,CACF;IACD,KAAAC,iBAAiB,GAAG,IAAI;IACxB,KAAAlG,WAAW,GAAW,EAAE;IACxB,KAAAE,UAAU,GAAW,EAAE;IACvB,KAAAC,cAAc,GAAW,EAAE;IAAE,KAAAgG,SAAS,GAAY,KAAK;IACvD,KAAAC,mBAAmB,GAAW,EAAE;IAChC,KAAAC,aAAa,GAAY,KAAK;IAC9B,KAAAtG,UAAU,GAAY,IAAI;IAE1B;IACA,KAAAoE,eAAe,GAAgB,EAAE;IACjC,KAAAD,cAAc,GAAgB,EAAE;IAChC,KAAAF,cAAc,GAAgB,EAAE;IAChC,KAAAhB,eAAe,GAAW,EAAE;IAC5B,KAAAoB,eAAe,GAAqB,IAAI;IACxC,KAAAc,mBAAmB,GAAW,CAAC;EAY/B;EAESoB,QAAQA,CAAA;IACf,IAAI,CAACC,gBAAgB,EAAE;EACzB;EAEAA,gBAAgBA,CAAA;IACd,IAAI,CAACb,iBAAiB,CAACc,6CAA6C,CAAC;MACnEC,IAAI,EAAE;QACJC,OAAO,EAAE,KAAK;QACdC,OAAO,EAAE;;KAEZ,CAAC,CACCC,IAAI,CACH1K,GAAG,CAAC2K,GAAG,IAAG;MACR,IAAIA,GAAG,CAACC,UAAU,IAAI,CAAC,EAAE;QACvB,IAAI,CAAChB,cAAc,GAAGe,GAAG,CAACE,OAAO,EAAE9C,MAAM,GAAG4C,GAAG,CAACE,OAAO,GAAG,EAAE;QAC5D,IAAI,CAACC,mBAAmB,GAAG,IAAI,CAAClB,cAAc,CAAC,CAAC,CAAC,CAAClJ,GAAI;MACxD;IACF,CAAC,CAAC,EACFX,QAAQ,CAAC,MAAM,IAAI,CAACgL,eAAe,EAAE,CAAC,CACvC,CAACC,SAAS,EAAE;EACjB;EAEAD,eAAeA,CAACE,SAAA,GAAoB,CAAC;IACnC,OAAO,IAAI,CAACxB,gBAAgB,CAACyB,mCAAmC,CAAC;MAC/DX,IAAI,EAAE;QACJY,YAAY,EAAE,IAAI,CAACL,mBAAmB;QACtCM,QAAQ,EAAE,IAAI,CAACpB,iBAAiB;QAChClG,WAAW,EAAE,IAAI,CAACA,WAAW;QAC7BE,UAAU,EAAE,IAAI,CAACA,UAAU;QAC3BqH,QAAQ,EAAE,IAAI,CAACC,QAAQ;QACvBC,SAAS,EAAEN,SAAS;QACpBhH,cAAc,EAAE,IAAI,CAACA,cAAc;QACnCJ,UAAU,EAAE,IAAI,CAACA;;KAEpB,CAAC,CAAC6G,IAAI,CACL1K,GAAG,CAAC2K,GAAG,IAAG;MACR,IAAIA,GAAG,CAACC,UAAU,IAAI,CAAC,EAAE;QACvB,IAAI,CAACtG,YAAY,GAAGqG,GAAG,CAACE,OAAQ,IAAI,EAAE;QACtC,IAAI,CAACW,YAAY,GAAGb,GAAG,CAACc,UAAW;QAEnC,IAAG,IAAI,CAACnH,YAAY,CAACyD,MAAM,GAAG,CAAC,EAAE;UAC/B,IAAI,CAACkC,SAAS,GAAG,IAAI,CAAC3F,YAAY,CAAC,CAAC,CAAC,CAACH,UAAW;QACnD;MACF;IACF,CAAC,CAAC,CACH;EACH;EAEA1C,MAAMA,CAAA;IACJ,IAAI,CAACsJ,eAAe,EAAE,CAACC,SAAS,EAAE;EACpC;EAEAU,WAAWA,CAACT,SAAiB;IAC3B,IAAI,CAACF,eAAe,CAACE,SAAS,CAAC,CAACD,SAAS,EAAE;EAC7C;EAEA3J,sBAAsBA,CAAA;IACpB,IAAI,CAACoI,gBAAgB,CAACkC,2CAA2C,CAAC;MAChEpB,IAAI,EAAE,IAAI,CAACO;KACZ,CAAC,CAACE,SAAS,CAACL,GAAG,IAAG;MACjB,IAAIA,GAAG,CAACC,UAAU,IAAI,CAAC,EAAE;QACvB,IAAID,GAAG,CAACE,OAAQ,CAACe,QAAQ,EAAE;UACzB,IAAI,CAAClC,eAAe,CAACmC,iBAAiB,CAAClB,GAAG,CAACE,OAAQ,CAACe,QAAQ,EAAE,MAAM,CAAC;QACvE;MACF;IACF,CAAC,CAAC;EACJ;EAEAE,0BAA0BA,CAAA;IACxB,IAAI,CAACrC,gBAAgB,CAACsC,+CAA+C,CAAC;MACpExB,IAAI,EAAE,IAAI,CAACO;KACZ,CAAC,CAACE,SAAS,CAACL,GAAG,IAAG;MACjB,IAAIA,GAAG,CAACC,UAAU,IAAI,CAAC,EAAE;QACvB,IAAID,GAAG,CAACE,OAAQ,CAACe,QAAQ,EAAE;UACzB,IAAI,CAAClC,eAAe,CAACmC,iBAAiB,CAAClB,GAAG,CAACE,OAAQ,CAACe,QAAQ,EAAE,MAAM,CAAC;QACvE;MACF;IACF,CAAC,CAAC;EACJ;EAEA9J,MAAMA,CAACkK,GAAQ;IACb,IAAI,CAACrC,KAAK,GAAG,IAAI;IACjB,IAAI,CAAC/E,gBAAgB,GAAG,EAAE;IAC1B,IAAI,CAACyE,aAAa,CAAC4C,IAAI,CAACD,GAAG,CAAC;EAC9B;EAEA9I,kBAAkBA,CAACgJ,IAA6B,EAAEF,GAAQ;IACxD,IAAI,CAACrC,KAAK,GAAG,KAAK;IAClB,IAAI,CAAC/E,gBAAgB,GAAG;MAAE,GAAGsH;IAAI,CAAE;IACnC,IAAI,CAAC7C,aAAa,CAAC4C,IAAI,CAACD,GAAG,CAAC;EAC9B;EAEAG,UAAUA,CAAA;IACR,IAAI,CAAC5C,KAAK,CAAC6C,KAAK,EAAE;IAClB,IAAI,CAAC7C,KAAK,CAAC8C,QAAQ,CAAC,MAAM,EAAE,IAAI,CAACzH,gBAAgB,CAACnB,KAAK,CAAC;IACxD,IAAI,CAAC8F,KAAK,CAAC8C,QAAQ,CAAC,MAAM,EAAE,IAAI,CAACzH,gBAAgB,CAAClB,KAAK,CAAC;IACxD,IAAI,CAAC6F,KAAK,CAAC8C,QAAQ,CAAC,MAAM,EAAE,IAAI,CAACzH,gBAAgB,CAACjB,SAAS,CAAC;IAC5D,IAAI,CAAC4F,KAAK,CAAC8C,QAAQ,CAAC,UAAU,EAAE,IAAI,CAACzH,gBAAgB,CAACd,WAAW,CAAC;IAClE,IAAI,CAACyF,KAAK,CAAC8C,QAAQ,CAAC,UAAU,EAAE,IAAI,CAACzH,gBAAgB,CAACZ,UAAU,CAAC;IACjE,IAAI,CAACuF,KAAK,CAAC+C,iBAAiB,CAAC,MAAM,EAAE,IAAI,CAAC1H,gBAAgB,CAACnB,KAAK,EAAE,EAAE,CAAC;IACrE,IAAI,CAAC8F,KAAK,CAAC+C,iBAAiB,CAAC,MAAM,EAAE,IAAI,CAAC1H,gBAAgB,CAAClB,KAAK,EAAE,EAAE,CAAC;IACrE,IAAI,CAAC6F,KAAK,CAAC+C,iBAAiB,CAAC,MAAM,EAAE,IAAI,CAAC1H,gBAAgB,CAACjB,SAAS,EAAE,EAAE,CAAC;IACzE,IAAI,CAAC4F,KAAK,CAAC+C,iBAAiB,CAAC,UAAU,EAAE,IAAI,CAAC1H,gBAAgB,CAACd,WAAW,EAAE,EAAE,CAAC;IAC/E,IAAI,CAACyF,KAAK,CAAC+C,iBAAiB,CAAC,UAAU,EAAE,IAAI,CAAC1H,gBAAgB,CAACZ,UAAU,EAAE,EAAE,CAAC;EAChF;EAEA2B,QAAQA,CAACqG,GAAQ;IACf,IAAI,CAACG,UAAU,EAAE;IACjB,IAAI,IAAI,CAAC5C,KAAK,CAACgD,aAAa,CAACxE,MAAM,GAAG,CAAC,EAAE;MACvC,IAAI,CAACuB,OAAO,CAACkD,aAAa,CAAC,IAAI,CAACjD,KAAK,CAACgD,aAAa,CAAC;MACpD;IACF;IACA,IAAI,CAAC9C,gBAAgB,CAACgD,qCAAqC,CAAC;MAC1DlC,IAAI,EAAE;QACJY,YAAY,EAAE,IAAI,CAACL,mBAAmB;QACtC9G,UAAU,EAAE,IAAI,CAACY,gBAAgB,CAACZ,UAAU;QAC5CP,KAAK,EAAE,IAAI,CAACmB,gBAAgB,CAACnB,KAAK;QAClCC,KAAK,EAAE,IAAI,CAACkB,gBAAgB,CAAClB,KAAK;QAClCC,SAAS,EAAE,IAAI,CAACiB,gBAAgB,CAACjB,SAAS;QAC1CG,WAAW,EAAE,IAAI,CAACc,gBAAgB,CAACd,WAAW;QAC9CI,YAAY,EAAE,IAAI,CAACU,gBAAgB,CAACV,YAAY;QAChDwI,WAAW,EAAE,IAAI,CAAC/C,KAAK,GAAG,IAAI,GAAG,IAAI,CAAC/E,gBAAgB,CAACpB,GAAI;QAC3DT,MAAM,EAAE,IAAI,CAAC6B,gBAAgB,CAAC7B;;KAEjC,CAAC,CACC2H,IAAI,CACH1K,GAAG,CAAC2K,GAAG,IAAG;MACR,IAAIA,GAAG,CAACC,UAAU,KAAK,CAAC,EAAE;QACxB,IAAI,CAACtB,OAAO,CAACqD,aAAa,CAAC,MAAM,CAAC;MACpC,CAAC,MAAM;QACL,IAAI,CAACrD,OAAO,CAACsD,YAAY,CAACjC,GAAG,CAACkC,OAAQ,CAAC;MACzC;IACF,CAAC,CAAC,EACF9M,QAAQ,CAAC,MAAM,IAAI,CAACgL,eAAe,EAAE,CAAC,EACtCjL,QAAQ,CAAC,MAAMkM,GAAG,CAACjD,KAAK,EAAE,CAAC,CAC5B,CAACiC,SAAS,EAAE;EACjB;EAEAvF,OAAOA,CAACuG,GAAQ;IACdA,GAAG,CAACjD,KAAK,EAAE;EACb;EAEA+D,eAAeA,CAACC,KAAU;IACxB,MAAMC,MAAM,GAAgCD,KAAK,CAACC,MAAO;IACzD,MAAMC,MAAM,GAAe,IAAIC,UAAU,EAAE;IAC3CD,MAAM,CAACE,kBAAkB,CAACH,MAAM,CAACI,KAAK,CAAC,CAAC,CAAC,CAAC;IAC1CH,MAAM,CAACI,MAAM,GAAIC,CAAM,IAAI;MACzB,MAAMC,SAAS,GAAWD,CAAC,CAACN,MAAM,CAACQ,MAAM;MACzC,MAAMC,EAAE,GAAkBxN,IAAI,CAACyN,IAAI,CAACH,SAAS,EAAE;QAAEI,IAAI,EAAE;MAAQ,CAAE,CAAC;MAElE,MAAMC,MAAM,GAAWH,EAAE,CAACI,UAAU,CAAC,CAAC,CAAC;MACvC,MAAMC,EAAE,GAAmBL,EAAE,CAACM,MAAM,CAACH,MAAM,CAAC;MAE5C,IAAII,WAAW,GAAY,IAAI;MAC/B,MAAM9B,IAAI,GAAGjM,IAAI,CAACgO,KAAK,CAACC,aAAa,CAACJ,EAAE,CAAC;MACzC,IAAI5B,IAAI,IAAIA,IAAI,CAACnE,MAAM,GAAG,CAAC,EAAE;QAC3BmE,IAAI,CAACiC,OAAO,CAAEC,CAAM,IAAI;UACtB,IAAI,EAAEA,CAAC,CAAC,IAAI,CAAC,IAAIA,CAAC,CAAC,IAAI,CAAC,IAAIA,CAAC,CAAC,IAAI,CAAC,IAAIA,CAAC,CAAC,QAAQ,CAAC,KAC/CA,CAAC,CAAC,QAAQ,CAAC,IAAIA,CAAC,CAAC,MAAM,CAAC,IAAIA,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE;YAC5CJ,WAAW,GAAG,KAAK;UACrB;QACF,CAAC,CAAC;QAEF,IAAI,CAACA,WAAW,EAAE;UAChB,IAAI,CAAC1E,OAAO,CAACsD,YAAY,CAAC,WAAW,CAAC;QACxC,CAAC,MAAM;UACL,IAAI,CAACnD,gBAAgB,CAAC4E,2CAA2C,CAAC;YAChE9D,IAAI,EAAE;cACJY,YAAY,EAAE,IAAI,CAACL,mBAAmB;cACtCwD,KAAK,EAAEtB,MAAM,CAACI,KAAK,CAAC,CAAC;;WAExB,CAAC,CAAC1C,IAAI,CACL1K,GAAG,CAAC2K,GAAG,IAAG;YACR,IAAIA,GAAG,CAACC,UAAU,IAAI,CAAC,EAAE;cACvB,IAAI,CAACtB,OAAO,CAACqD,aAAa,CAAC,MAAM,CAAC;YACpC,CAAC,MAAM;cACL,IAAI,CAACrD,OAAO,CAACsD,YAAY,CAACjC,GAAG,CAACkC,OAAQ,CAAC;YACzC;UACF,CAAC,CAAC,EACF9M,QAAQ,CAAC,MAAM,IAAI,CAACgL,eAAe,CAAC,CAAC,CAAC,CAAC,CACxC,CAACC,SAAS,EAAE;QACf;MACF,CAAC,MAAM;QACL,IAAI,CAAC1B,OAAO,CAACsD,YAAY,CAAC,uBAAuB,CAAC;MACpD;MACAG,KAAK,CAACC,MAAM,CAAClD,KAAK,GAAG,IAAI;IAC3B,CAAC;EACH;EAEAvH,SAASA,CAACgM,QAAgB,EAAEC,MAAwB;IAClD,IAAI,CAACtE,mBAAmB,GAAGqE,QAAQ;IACnC,IAAI,CAAClF,aAAa,CAAC4C,IAAI,CAACuC,MAAM,CAAC;EACjC;EACAC,YAAYA,CAAA;IACV,IAAG,IAAI,CAACtE,aAAa,EAAC;MACpB,IAAI,CAACtG,UAAU,GAAG,KAAK;MACvB,IAAI,CAACkH,eAAe,EAAE,CAACC,SAAS,EAAE;IACpC,CAAC,MACG;MACF,IAAI,CAACnH,UAAU,GAAG,IAAI;MACtB,IAAI,CAACkH,eAAe,EAAE,CAACC,SAAS,EAAE;IACpC;EACF;EAEA;EACA7F,eAAeA,CAAC6G,GAAqB;IACnC,IAAI,CAAC9E,UAAU,EAAE;IACjB,IAAI,CAACmC,aAAa,CAAC4C,IAAI,CAACD,GAAG,EAAE;MAAE0C,oBAAoB,EAAE;IAAK,CAAE,CAAC;EAC/D;EAEAxH,UAAUA,CAAA;IACR;IACA;IACA,IAAI,CAACe,eAAe,GAAG,CACrB;MACE0G,EAAE,EAAE,GAAG;MACP5I,IAAI,EAAE,oBAAoB;MAC1BY,IAAI,EAAE,GAAG;MACTb,YAAY,EAAE,mDAAmD;MACjEqC,OAAO,EAAE,4CAA4C;MACrDyG,YAAY,EAAE,IAAIC,IAAI;KACvB,EACD;MACEF,EAAE,EAAE,GAAG;MACP5I,IAAI,EAAE,oBAAoB;MAC1BY,IAAI,EAAE,GAAG;MACTb,YAAY,EAAE,mDAAmD;MACjEqC,OAAO,EAAE,4CAA4C;MACrDyG,YAAY,EAAE,IAAIC,IAAI;KACvB,EACD;MACEF,EAAE,EAAE,GAAG;MACP5I,IAAI,EAAE,sBAAsB;MAC5BY,IAAI,EAAE,GAAG;MACTb,YAAY,EAAE,qDAAqD;MACnEqC,OAAO,EAAE,8CAA8C;MACvDyG,YAAY,EAAE,IAAIC,IAAI;KACvB,EACD;MACEF,EAAE,EAAE,GAAG;MACP5I,IAAI,EAAE,0BAA0B;MAChCY,IAAI,EAAE,GAAG;MACTb,YAAY,EAAE,yDAAyD;MACvEqC,OAAO,EAAE,kDAAkD;MAC3DyG,YAAY,EAAE,IAAIC,IAAI;KACvB,EACD;MACEF,EAAE,EAAE,GAAG;MACP5I,IAAI,EAAE,qBAAqB;MAC3BY,IAAI,EAAE,EAAE;MACRb,YAAY,EAAE,oDAAoD;MAClEqC,OAAO,EAAE,6CAA6C;MACtDyG,YAAY,EAAE,IAAIC,IAAI;KACvB,EACD;MACEF,EAAE,EAAE,GAAG;MACP5I,IAAI,EAAE,sBAAsB;MAC5BY,IAAI,EAAE,GAAG;MACTb,YAAY,EAAE,qDAAqD;MACnEqC,OAAO,EAAE,8CAA8C;MACvDyG,YAAY,EAAE,IAAIC,IAAI;KACvB,CACF;IAED,IAAI,CAAC7G,cAAc,GAAG,CAAC,GAAG,IAAI,CAACC,eAAe,CAAC;IAE/C;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;EACF;EAEAjB,YAAYA,CAAA;IACV,IAAI,CAAC,IAAI,CAACF,eAAe,CAACgI,IAAI,EAAE,EAAE;MAChC,IAAI,CAAC9G,cAAc,GAAG,CAAC,GAAG,IAAI,CAACC,eAAe,CAAC;IACjD,CAAC,MAAM;MACL,MAAM8G,UAAU,GAAG,IAAI,CAACjI,eAAe,CAACkI,WAAW,EAAE;MACrD,IAAI,CAAChH,cAAc,GAAG,IAAI,CAACC,eAAe,CAACgH,MAAM,CAACC,KAAK,IACrDA,KAAK,CAACnJ,IAAI,CAACiJ,WAAW,EAAE,CAACG,QAAQ,CAACJ,UAAU,CAAC,CAC9C;IACH;EACF;EAEA7I,oBAAoBA,CAACgJ,KAAgB;IACnC,MAAME,KAAK,GAAG,IAAI,CAACtH,cAAc,CAACuH,SAAS,CAACC,QAAQ,IAAIA,QAAQ,CAACX,EAAE,KAAKO,KAAK,CAACP,EAAE,CAAC;IACjF,IAAIS,KAAK,GAAG,CAAC,CAAC,EAAE;MACd,IAAI,CAACtH,cAAc,CAACyH,MAAM,CAACH,KAAK,EAAE,CAAC,CAAC;IACtC,CAAC,MAAM;MACL,IAAI,CAACtH,cAAc,CAAC0H,IAAI,CAACN,KAAK,CAAC;IACjC;EACF;EAEAzI,eAAeA,CAACyI,KAAgB;IAC9B,OAAO,IAAI,CAACpH,cAAc,CAAC2H,IAAI,CAACH,QAAQ,IAAIA,QAAQ,CAACX,EAAE,KAAKO,KAAK,CAACP,EAAE,CAAC;EACvE;EAEAvH,eAAeA,CAAA;IACb,IAAI,CAACU,cAAc,GAAG,CAAC,GAAG,IAAI,CAACE,cAAc,CAAC;EAChD;EAEAV,iBAAiBA,CAAA;IACf,IAAI,CAACQ,cAAc,GAAG,EAAE;EAC1B;EACAxB,YAAYA,CAAC4I,KAAgB,EAAEnC,KAAY;IACzCA,KAAK,CAAC2C,eAAe,EAAE;IACvB,IAAI,CAACxH,eAAe,GAAGgH,KAAK;IAC5B,IAAI,CAAClG,mBAAmB,GAAG,IAAI,CAAChB,cAAc,CAACqH,SAAS,CAACM,GAAG,IAAIA,GAAG,CAAChB,EAAE,KAAKO,KAAK,CAACP,EAAE,CAAC;IACpF;IACA;IACA;EACF;EAEArG,aAAaA,CAAA;IACX,IAAI,IAAI,CAACU,mBAAmB,GAAG,CAAC,EAAE;MAChC,IAAI,CAACA,mBAAmB,EAAE;MAC1B,IAAI,CAACd,eAAe,GAAG,IAAI,CAACF,cAAc,CAAC,IAAI,CAACgB,mBAAmB,CAAC;IACtE;EACF;EAEAR,SAASA,CAAA;IACP,IAAI,IAAI,CAACQ,mBAAmB,GAAG,IAAI,CAAChB,cAAc,CAACD,MAAM,GAAG,CAAC,EAAE;MAC7D,IAAI,CAACiB,mBAAmB,EAAE;MAC1B,IAAI,CAACd,eAAe,GAAG,IAAI,CAACF,cAAc,CAAC,IAAI,CAACgB,mBAAmB,CAAC;IACtE;EACF;EAEAJ,6BAA6BA,CAAA;IAC3B,IAAI,IAAI,CAACV,eAAe,EAAE;MACxB,IAAI,CAAChC,oBAAoB,CAAC,IAAI,CAACgC,eAAe,CAAC;IACjD;EACF;EACAL,uBAAuBA,CAACmE,GAAQ;IAC9B,IAAI,IAAI,CAAClE,cAAc,CAACC,MAAM,GAAG,CAAC,EAAE;MAClC;MACA,IAAI,IAAI,CAACD,cAAc,CAACC,MAAM,KAAK,CAAC,EAAE;QACpC,IAAI,CAACnD,gBAAgB,CAACZ,UAAU,GAAG,IAAI,CAAC8D,cAAc,CAAC,CAAC,CAAC,CAAC/B,IAAI;MAChE,CAAC,MAAM;QACL;QACA,MAAM6J,UAAU,GAAG,IAAI,CAAC9H,cAAc,CAAC+H,GAAG,CAACF,GAAG,IAAIA,GAAG,CAAC5J,IAAI,CAAC,CAAC+J,IAAI,CAAC,IAAI,CAAC;QACtE,IAAI,CAACxG,OAAO,CAACqD,aAAa,CAAC,OAAO,IAAI,CAAC7E,cAAc,CAACC,MAAM,SAAS6H,UAAU,EAAE,CAAC;QAClF,IAAI,CAAChL,gBAAgB,CAACZ,UAAU,GAAG,IAAI,CAAC8D,cAAc,CAAC,CAAC,CAAC,CAAC/B,IAAI;MAChE;IACF;IAEA,IAAI,CAACuB,iBAAiB,EAAE;IACxB0E,GAAG,CAACjD,KAAK,EAAE;EACb;EAEApB,kBAAkBA,CAACqE,GAAQ;IACzB,IAAI,CAAC1E,iBAAiB,EAAE;IACxB,IAAI,CAACR,eAAe,GAAG,EAAE;IACzBkF,GAAG,CAACjD,KAAK,EAAE;EACb;;;uCAxZWG,yBAAyB,EAAA9I,EAAA,CAAA2P,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAA7P,EAAA,CAAA2P,iBAAA,CAAAG,EAAA,CAAAC,eAAA,GAAA/P,EAAA,CAAA2P,iBAAA,CAAAK,EAAA,CAAAC,cAAA,GAAAjQ,EAAA,CAAA2P,iBAAA,CAAAO,EAAA,CAAAC,gBAAA,GAAAnQ,EAAA,CAAA2P,iBAAA,CAAAS,EAAA,CAAAC,gBAAA,GAAArQ,EAAA,CAAA2P,iBAAA,CAAAS,EAAA,CAAAE,eAAA,GAAAtQ,EAAA,CAAA2P,iBAAA,CAAAY,EAAA,CAAAC,cAAA;IAAA;EAAA;;;YAAzB1H,yBAAyB;MAAA2H,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAA3Q,EAAA,CAAA4Q,0BAAA,EAAA5Q,EAAA,CAAA6Q,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,mCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;UC/BpCnR,EADF,CAAAC,cAAA,iBAA0B,qBACR;UACdD,EAAA,CAAAkB,SAAA,qBAAiC;UACnClB,EAAA,CAAAG,YAAA,EAAiB;UAEfH,EADF,CAAAC,cAAA,mBAAc,YACyB;UAACD,EAAA,CAAAE,MAAA,yJACtC;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAICH,EAHN,CAAAC,cAAA,aAA8B,aACN,aACqC,eACF;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAC/DH,EAAA,CAAAC,cAAA,qBAA6E;UAAjDD,EAAA,CAAAmE,gBAAA,2BAAAkN,uEAAAhN,MAAA;YAAArE,EAAA,CAAAY,aAAA,CAAA0Q,GAAA;YAAAtR,EAAA,CAAAuE,kBAAA,CAAA6M,GAAA,CAAA1G,mBAAA,EAAArG,MAAA,MAAA+M,GAAA,CAAA1G,mBAAA,GAAArG,MAAA;YAAA,OAAArE,EAAA,CAAAgB,WAAA,CAAAqD,MAAA;UAAA,EAAiC;UAC3DrE,EAAA,CAAA+C,UAAA,KAAAwO,+CAAA,wBAA4E;UAKlFvR,EAFI,CAAAG,YAAA,EAAY,EACR,EACF;UAaFH,EAFJ,CAAAC,cAAA,cAAsB,cACqC,gBACF;UAAAD,EAAA,CAAAE,MAAA,6CAAO;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACpEH,EAAA,CAAAC,cAAA,iBAAyF;UAAzCD,EAAA,CAAAmE,gBAAA,2BAAAqN,mEAAAnN,MAAA;YAAArE,EAAA,CAAAY,aAAA,CAAA0Q,GAAA;YAAAtR,EAAA,CAAAuE,kBAAA,CAAA6M,GAAA,CAAA1N,WAAA,EAAAW,MAAA,MAAA+M,GAAA,CAAA1N,WAAA,GAAAW,MAAA;YAAA,OAAArE,EAAA,CAAAgB,WAAA,CAAAqD,MAAA;UAAA,EAAyB;UAE7ErE,EAFI,CAAAG,YAAA,EAAyF,EACrF,EACF;UAGFH,EAFJ,CAAAC,cAAA,cAAsB,cACqC,gBACF;UAAAD,EAAA,CAAAE,MAAA,6CACrD;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACRH,EAAA,CAAAC,cAAA,iBAAwF;UAAxCD,EAAA,CAAAmE,gBAAA,2BAAAsN,mEAAApN,MAAA;YAAArE,EAAA,CAAAY,aAAA,CAAA0Q,GAAA;YAAAtR,EAAA,CAAAuE,kBAAA,CAAA6M,GAAA,CAAAxN,UAAA,EAAAS,MAAA,MAAA+M,GAAA,CAAAxN,UAAA,GAAAS,MAAA;YAAA,OAAArE,EAAA,CAAAgB,WAAA,CAAAqD,MAAA;UAAA,EAAwB;UAE5ErE,EAFI,CAAAG,YAAA,EAAwF,EACpF,EACF;UAGFH,EAFJ,CAAAC,cAAA,cAAsB,cACqC,gBACF;UAAAD,EAAA,CAAAE,MAAA,6CACrD;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACRH,EAAA,CAAAC,cAAA,iBAA4F;UAA5CD,EAAA,CAAAmE,gBAAA,2BAAAuN,mEAAArN,MAAA;YAAArE,EAAA,CAAAY,aAAA,CAAA0Q,GAAA;YAAAtR,EAAA,CAAAuE,kBAAA,CAAA6M,GAAA,CAAAvN,cAAA,EAAAQ,MAAA,MAAA+M,GAAA,CAAAvN,cAAA,GAAAQ,MAAA;YAAA,OAAArE,EAAA,CAAAgB,WAAA,CAAAqD,MAAA;UAAA,EAA4B;UAEhFrE,EAFI,CAAAG,YAAA,EAA4F,EACxF,EACF;UAGFH,EAFJ,CAAAC,cAAA,eAAuB,eAC0B,uBACoE;UAAtDD,EAAA,CAAAmE,gBAAA,2BAAAwN,yEAAAtN,MAAA;YAAArE,EAAA,CAAAY,aAAA,CAAA0Q,GAAA;YAAAtR,EAAA,CAAAuE,kBAAA,CAAA6M,GAAA,CAAArH,aAAA,EAAA1F,MAAA,MAAA+M,GAAA,CAAArH,aAAA,GAAA1F,MAAA;YAAA,OAAArE,EAAA,CAAAgB,WAAA,CAAAqD,MAAA;UAAA,EAA2B;UAACrE,EAAA,CAAAU,UAAA,oBAAAkR,kEAAA;YAAA5R,EAAA,CAAAY,aAAA,CAAA0Q,GAAA;YAAA,OAAAtR,EAAA,CAAAgB,WAAA,CAAUoQ,GAAA,CAAA/C,YAAA,EAAc;UAAA,EAAC;UAC9GrO,EAAA,CAAAE,MAAA,gHACF;UAAAF,EAAA,CAAAG,YAAA,EAAc;UAOdH,EANA,CAAA+C,UAAA,KAAA8O,4CAAA,qBAA8F,KAAAC,4CAAA,qBAEV,KAAAC,4CAAA,qBAEE,KAAAC,4CAAA,qBAEF;UACpFhS,EAAA,CAAAC,cAAA,oBAAqG;UAAnCD,EAAA,CAAAU,UAAA,oBAAAuR,4DAAA5N,MAAA;YAAArE,EAAA,CAAAY,aAAA,CAAA0Q,GAAA;YAAA,OAAAtR,EAAA,CAAAgB,WAAA,CAAUoQ,GAAA,CAAA1E,eAAA,CAAArI,MAAA,CAAuB;UAAA,EAAC;UAApGrE,EAAA,CAAAG,YAAA,EAAqG;UACrGH,EAAA,CAAAC,cAAA,kBAA4E;UAAvCD,EAAA,CAAAU,UAAA,mBAAAwR,4DAAA;YAAAlS,EAAA,CAAAY,aAAA,CAAA0Q,GAAA;YAAA,OAAAtR,EAAA,CAAAgB,WAAA,CAASoQ,GAAA,CAAA1F,0BAAA,EAA4B;UAAA,EAAC;UAAC1L,EAAA,CAAAE,MAAA,6CAAO;UAAAF,EAAA,CAAAkB,SAAA,aAC9C;UAG3ClB,EAH2C,CAAAG,YAAA,EAAS,EAC1C,EACF,EACF;UAKEH,EAJR,CAAAC,cAAA,eAAmC,iBAC+D,aACvF,cACgD,cACrB;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAErCH,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACrCH,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACrCH,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACrCH,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAE,MAAA,4CAAM;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACzCH,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAE,MAAA,wGAAgB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACnDH,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAE,MAAA,wGAAgB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACnDH,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAE,MAAA,gCAAI;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACvCH,EAAA,CAAA+C,UAAA,KAAAoP,wCAAA,iBAAwD;UACxDnS,EAAA,CAAAC,cAAA,cAA0C;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAEhDF,EAFgD,CAAAG,YAAA,EAAK,EAC9C,EACC;UACRH,EAAA,CAAA+C,UAAA,KAAAqP,2CAAA,oBAA+D;UAwCrEpS,EAFI,CAAAG,YAAA,EAAQ,EACJ,EACO;UAEbH,EADF,CAAAC,cAAA,0BAAsD,0BAEf;UADqCD,EAA1D,CAAAmE,gBAAA,kCAAAkO,mFAAAhO,MAAA;YAAArE,EAAA,CAAAY,aAAA,CAAA0Q,GAAA;YAAAtR,EAAA,CAAAuE,kBAAA,CAAA6M,GAAA,CAAAhG,YAAA,EAAA/G,MAAA,MAAA+M,GAAA,CAAAhG,YAAA,GAAA/G,MAAA;YAAA,OAAArE,EAAA,CAAAgB,WAAA,CAAAqD,MAAA;UAAA,EAAiC,4BAAAiO,6EAAAjO,MAAA;YAAArE,EAAA,CAAAY,aAAA,CAAA0Q,GAAA;YAAAtR,EAAA,CAAAuE,kBAAA,CAAA6M,GAAA,CAAAlG,QAAA,EAAA7G,MAAA,MAAA+M,GAAA,CAAAlG,QAAA,GAAA7G,MAAA;YAAA,OAAArE,EAAA,CAAAgB,WAAA,CAAAqD,MAAA;UAAA,EAAwB,wBAAAkO,yEAAAlO,MAAA;YAAArE,EAAA,CAAAY,aAAA,CAAA0Q,GAAA;YAAAtR,EAAA,CAAAuE,kBAAA,CAAA6M,GAAA,CAAAvG,SAAA,EAAAxG,MAAA,MAAA+M,GAAA,CAAAvG,SAAA,GAAAxG,MAAA;YAAA,OAAArE,EAAA,CAAAgB,WAAA,CAAAqD,MAAA;UAAA,EAAqB;UAC5FrE,EAAA,CAAAU,UAAA,wBAAA6R,yEAAAlO,MAAA;YAAArE,EAAA,CAAAY,aAAA,CAAA0Q,GAAA;YAAA,OAAAtR,EAAA,CAAAgB,WAAA,CAAcoQ,GAAA,CAAA9F,WAAA,CAAAjH,MAAA,CAAmB;UAAA,EAAC;UAGxCrE,EAFI,CAAAG,YAAA,EAAiB,EACF,EACT;UA0JVH,EAxJA,CAAA+C,UAAA,KAAAyP,iDAAA,iCAAAxS,EAAA,CAAAyS,sBAAA,CAAoD,KAAAC,iDAAA,iCAAA1S,EAAA,CAAAyS,sBAAA,CA4DK,KAAAE,iDAAA,iCAAA3S,EAAA,CAAAyS,sBAAA,CA4FC;;;UAhRpBzS,EAAA,CAAAO,SAAA,IAAiC;UAAjCP,EAAA,CAAAwF,gBAAA,YAAA4L,GAAA,CAAA1G,mBAAA,CAAiC;UAC1B1K,EAAA,CAAAO,SAAA,EAAiB;UAAjBP,EAAA,CAAAI,UAAA,YAAAgR,GAAA,CAAA5H,cAAA,CAAiB;UAmBJxJ,EAAA,CAAAO,SAAA,GAAyB;UAAzBP,EAAA,CAAAwF,gBAAA,YAAA4L,GAAA,CAAA1N,WAAA,CAAyB;UAOzB1D,EAAA,CAAAO,SAAA,GAAwB;UAAxBP,EAAA,CAAAwF,gBAAA,YAAA4L,GAAA,CAAAxN,UAAA,CAAwB;UAOxB5D,EAAA,CAAAO,SAAA,GAA4B;UAA5BP,EAAA,CAAAwF,gBAAA,YAAA4L,GAAA,CAAAvN,cAAA,CAA4B;UAKjB7D,EAAA,CAAAO,SAAA,GAA2B;UAA3BP,EAAA,CAAAwF,gBAAA,YAAA4L,GAAA,CAAArH,aAAA,CAA2B;UAG7E/J,EAAA,CAAAO,SAAA,GAAmB;UAAnBP,EAAA,CAAAI,UAAA,SAAAgR,GAAA,CAAAwB,aAAA,CAAmB;UAEnB5S,EAAA,CAAAO,SAAA,EAAY;UAAZP,EAAA,CAAAI,UAAA,SAAAgR,GAAA,CAAApN,MAAA,CAAY;UAEZhE,EAAA,CAAAO,SAAA,EAAc;UAAdP,EAAA,CAAAI,UAAA,SAAAgR,GAAA,CAAAyB,QAAA,CAAc;UAEY7S,EAAA,CAAAO,SAAA,EAAmB;UAAnBP,EAAA,CAAAI,UAAA,SAAAgR,GAAA,CAAA0B,aAAA,CAAmB;UAoBrB9S,EAAA,CAAAO,SAAA,IAAuB;UAAvBP,EAAA,CAAAI,UAAA,SAAAgR,GAAA,CAAAvH,SAAA,SAAuB;UAIlD7J,EAAA,CAAAO,SAAA,GAAqD;UAArDP,EAAA,CAAAI,UAAA,SAAAgR,GAAA,CAAAlN,YAAA,YAAAkN,GAAA,CAAAlN,YAAA,CAAAyD,MAAA,KAAqD;UA0CjD3H,EAAA,CAAAO,SAAA,GAAiC;UAAyBP,EAA1D,CAAAwF,gBAAA,mBAAA4L,GAAA,CAAAhG,YAAA,CAAiC,aAAAgG,GAAA,CAAAlG,QAAA,CAAwB,SAAAkG,GAAA,CAAAvG,SAAA,CAAqB;;;qBDhGtFpL,YAAY,EAAAsT,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EAAAF,EAAA,CAAAG,WAAA,EAAEpT,YAAY,EAAAqT,EAAA,CAAAC,oBAAA,EAAAD,EAAA,CAAAE,mBAAA,EAAAF,EAAA,CAAAG,eAAA,EAAAH,EAAA,CAAAI,kBAAA,EAAAJ,EAAA,CAAAK,OAAA,EAAA1D,EAAA,CAAA2D,eAAA,EAAA3D,EAAA,CAAA4D,mBAAA,EAAA5D,EAAA,CAAA6D,qBAAA,EAAA7D,EAAA,CAAA8D,qBAAA,EAAA9D,EAAA,CAAA+D,mBAAA,EAAA/D,EAAA,CAAAgE,gBAAA,EAAAhE,EAAA,CAAAiE,iBAAA,EAAAjE,EAAA,CAAAkE,iBAAA,EAAAC,EAAA,CAAAC,mBAAA,EAAAC,GAAA,CAAAC,mBAAA;MAAAC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}