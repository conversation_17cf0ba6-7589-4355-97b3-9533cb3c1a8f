{"ast": null, "code": "import { STORAGE_KEY } from \"../../constant/constant\";\nimport { LocalStorageService } from \"../../services/local-storage.service\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nexport class AuthGuard {\n  constructor(router) {\n    this.router = router;\n  }\n  canActivate(route, state) {\n    if (!LocalStorageService.GetLocalStorage(STORAGE_KEY.TOKEN)) {\n      LocalStorageService.ClearLocalStorage();\n      this.router.navigate(['login']);\n      return false;\n    }\n    return true;\n  }\n  static {\n    this.ɵfac = function AuthGuard_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || AuthGuard)(i0.ɵɵinject(i1.Router));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: AuthGuard,\n      factory: AuthGuard.ɵfac,\n      providedIn: \"root\"\n    });\n  }\n}", "map": {"version": 3, "names": ["STORAGE_KEY", "LocalStorageService", "<PERSON><PERSON><PERSON><PERSON>", "constructor", "router", "canActivate", "route", "state", "GetLocalStorage", "TOKEN", "ClearLocalStorage", "navigate", "i0", "ɵɵinject", "i1", "Router", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\Documents\\jeansalechange\\adminFront\\src\\app\\shared\\auth\\guards\\auth.guard.ts"], "sourcesContent": ["import { Injectable } from \"@angular/core\";\r\nimport {\r\n    ActivatedRouteSnapshot,\r\n    CanActivate,\r\n    Router,\r\n    RouterStateSnapshot,\r\n    UrlTree,\r\n} from \"@angular/router\";\r\nimport { Observable } from \"rxjs\";\r\nimport { STORAGE_KEY } from \"../../constant/constant\";\r\nimport { environment } from \"../../../../environments/environment\";\r\nimport { LocalStorageService } from \"../../services/local-storage.service\";\r\n\r\n\r\n@Injectable({\r\n    providedIn: \"root\",\r\n})\r\nexport class AuthGuard implements CanActivate {\r\n    constructor(private router:Router){}\r\n    canActivate(\r\n        route: ActivatedRouteSnapshot,\r\n        state: RouterStateSnapshot\r\n    ):\r\n        | Observable<boolean | UrlTree>\r\n        | Promise<boolean | UrlTree>\r\n        | boolean\r\n        | UrlTree {\r\n        if (\r\n            !LocalStorageService.GetLocalStorage(STORAGE_KEY.TOKEN)\r\n        ) {\r\n            LocalStorageService.ClearLocalStorage();\r\n            this.router.navigate(['login']);            \r\n            return false;\r\n        }\r\n        return true;\r\n    }\r\n}\r\n\r\n"], "mappings": "AASA,SAASA,WAAW,QAAQ,yBAAyB;AAErD,SAASC,mBAAmB,QAAQ,sCAAsC;;;AAM1E,OAAM,MAAOC,SAAS;EAClBC,YAAoBC,MAAa;IAAb,KAAAA,MAAM,GAANA,MAAM;EAAS;EACnCC,WAAWA,CACPC,KAA6B,EAC7BC,KAA0B;IAM1B,IACI,CAACN,mBAAmB,CAACO,eAAe,CAACR,WAAW,CAACS,KAAK,CAAC,EACzD;MACER,mBAAmB,CAACS,iBAAiB,EAAE;MACvC,IAAI,CAACN,MAAM,CAACO,QAAQ,CAAC,CAAC,OAAO,CAAC,CAAC;MAC/B,OAAO,KAAK;IAChB;IACA,OAAO,IAAI;EACf;;;uCAlBST,SAAS,EAAAU,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,MAAA;IAAA;EAAA;;;aAATb,SAAS;MAAAc,OAAA,EAATd,SAAS,CAAAe,IAAA;MAAAC,UAAA,EAFN;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}