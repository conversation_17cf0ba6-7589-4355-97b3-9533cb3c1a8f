{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nfunction ImageCarouselComponent_img_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 14);\n  }\n  if (rf & 2) {\n    let tmp_2_0;\n    let tmp_3_0;\n    let tmp_4_0;\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵclassMap(ctx_r0.imageClass);\n    i0.ɵɵproperty(\"src\", (tmp_2_0 = ctx_r0.getCurrentImage()) == null ? null : tmp_2_0.url, i0.ɵɵsanitizeUrl)(\"alt\", ((tmp_3_0 = ctx_r0.getCurrentImage()) == null ? null : tmp_3_0.alt) || ((tmp_3_0 = ctx_r0.getCurrentImage()) == null ? null : tmp_3_0.name) || \"\\u5716\\u7247\")(\"title\", (tmp_4_0 = ctx_r0.getCurrentImage()) == null ? null : tmp_4_0.description);\n  }\n}\nfunction ImageCarouselComponent_button_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 15);\n    i0.ɵɵlistener(\"click\", function ImageCarouselComponent_button_9_Template_button_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r0 = i0.ɵɵnextContext();\n      ctx_r0.goToPrevious();\n      return i0.ɵɵresetView($event.stopPropagation());\n    });\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(1, \"svg\", 16);\n    i0.ɵɵelement(2, \"path\", 17);\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ImageCarouselComponent_button_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 18);\n    i0.ɵɵlistener(\"click\", function ImageCarouselComponent_button_10_Template_button_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r0 = i0.ɵɵnextContext();\n      ctx_r0.goToNext();\n      return i0.ɵɵresetView($event.stopPropagation());\n    });\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(1, \"svg\", 16);\n    i0.ɵɵelement(2, \"path\", 19);\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ImageCarouselComponent_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 20);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate2(\" \", ctx_r0.currentIndex + 1, \" / \", ctx_r0.images.length, \" \");\n  }\n}\nfunction ImageCarouselComponent_div_12_button_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 23);\n    i0.ɵɵlistener(\"click\", function ImageCarouselComponent_div_12_button_1_Template_button_click_0_listener() {\n      const i_r5 = i0.ɵɵrestoreView(_r4).index;\n      const ctx_r0 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r0.onThumbnailClick(i_r5));\n    });\n    i0.ɵɵelement(1, \"img\", 24);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const image_r6 = ctx.$implicit;\n    const i_r5 = ctx.index;\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassProp(\"border-blue-500\", i_r5 === ctx_r0.currentIndex)(\"border-gray-300\", i_r5 !== ctx_r0.currentIndex)(\"ring-2\", i_r5 === ctx_r0.currentIndex)(\"ring-blue-200\", i_r5 === ctx_r0.currentIndex);\n    i0.ɵɵproperty(\"title\", \"\\u9EDE\\u9078\\u7B2C \" + (i_r5 + 1) + \" \\u5F35\\u5716\\u7247\" + (image_r6.name ? \": \" + image_r6.name : \"\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"src\", image_r6.url, i0.ɵɵsanitizeUrl)(\"alt\", image_r6.alt || image_r6.name || \"\\u7E2E\\u7565\\u5716\");\n  }\n}\nfunction ImageCarouselComponent_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 21);\n    i0.ɵɵtemplate(1, ImageCarouselComponent_div_12_button_1_Template, 2, 11, \"button\", 22);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r0.images);\n  }\n}\nfunction ImageCarouselComponent_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 25);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(1, \"svg\", 26);\n    i0.ɵɵelement(2, \"path\", 27);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(3, \"span\", 28);\n    i0.ɵɵtext(4, \"\\u66AB\\u7121\\u5716\\u7247\");\n    i0.ɵɵelementEnd()();\n  }\n}\nexport class ImageCarouselComponent {\n  constructor() {\n    this.images = [];\n    this.currentIndex = 0;\n    this.showThumbnails = true;\n    this.showCounter = true;\n    this.showNavigation = true;\n    this.aspectRatio = 'aspect-square';\n    this.autoplay = false;\n    this.autoplayInterval = 3000;\n    this.enableKeyboard = true;\n    this.containerClass = '';\n    this.imageClass = '';\n    this.imageClick = new EventEmitter();\n    this.indexChange = new EventEmitter();\n    this.previousImage = new EventEmitter();\n    this.nextImage = new EventEmitter();\n  }\n  ngOnInit() {\n    if (this.currentIndex >= this.images.length) {\n      this.currentIndex = 0;\n    }\n    this.startAutoplay();\n  }\n  ngOnDestroy() {\n    this.stopAutoplay();\n  }\n  handleKeyboardEvent(event) {\n    if (!this.enableKeyboard || this.images.length <= 1) return;\n    switch (event.key) {\n      case 'ArrowLeft':\n        event.preventDefault();\n        this.goToPrevious();\n        break;\n      case 'ArrowRight':\n        event.preventDefault();\n        this.goToNext();\n        break;\n    }\n  }\n  getCurrentImage() {\n    if (this.images.length === 0 || this.currentIndex < 0 || this.currentIndex >= this.images.length) {\n      return null;\n    }\n    return this.images[this.currentIndex];\n  }\n  goToNext() {\n    if (this.images.length <= 1) return;\n    const newIndex = (this.currentIndex + 1) % this.images.length;\n    this.setCurrentIndex(newIndex);\n    this.nextImage.emit(newIndex);\n  }\n  goToPrevious() {\n    if (this.images.length <= 1) return;\n    const newIndex = this.currentIndex === 0 ? this.images.length - 1 : this.currentIndex - 1;\n    this.setCurrentIndex(newIndex);\n    this.previousImage.emit(newIndex);\n  }\n  goToIndex(index) {\n    if (index >= 0 && index < this.images.length && index !== this.currentIndex) {\n      this.setCurrentIndex(index);\n    }\n  }\n  onImageClick() {\n    const currentImage = this.getCurrentImage();\n    if (currentImage) {\n      this.imageClick.emit({\n        index: this.currentIndex,\n        image: currentImage\n      });\n    }\n  }\n  onThumbnailClick(index) {\n    this.goToIndex(index);\n  }\n  setCurrentIndex(index) {\n    this.currentIndex = index;\n    this.indexChange.emit(index);\n    this.restartAutoplay();\n  }\n  startAutoplay() {\n    if (!this.autoplay || this.images.length <= 1) return;\n    this.autoplayTimer = setInterval(() => {\n      this.goToNext();\n    }, this.autoplayInterval);\n  }\n  stopAutoplay() {\n    if (this.autoplayTimer) {\n      clearInterval(this.autoplayTimer);\n      this.autoplayTimer = null;\n    }\n  }\n  restartAutoplay() {\n    this.stopAutoplay();\n    this.startAutoplay();\n  }\n  // 暫停自動播放（滑鼠懸停時使用）\n  pauseAutoplay() {\n    this.stopAutoplay();\n  }\n  // 恢復自動播放（滑鼠離開時使用）\n  resumeAutoplay() {\n    this.startAutoplay();\n  }\n  static {\n    this.ɵfac = function ImageCarouselComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || ImageCarouselComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ImageCarouselComponent,\n      selectors: [[\"ngx-image-carousel\"]],\n      hostBindings: function ImageCarouselComponent_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"keydown\", function ImageCarouselComponent_keydown_HostBindingHandler($event) {\n            return ctx.handleKeyboardEvent($event);\n          }, false, i0.ɵɵresolveDocument);\n        }\n      },\n      inputs: {\n        images: \"images\",\n        currentIndex: \"currentIndex\",\n        showThumbnails: \"showThumbnails\",\n        showCounter: \"showCounter\",\n        showNavigation: \"showNavigation\",\n        aspectRatio: \"aspectRatio\",\n        autoplay: \"autoplay\",\n        autoplayInterval: \"autoplayInterval\",\n        enableKeyboard: \"enableKeyboard\",\n        containerClass: \"containerClass\",\n        imageClass: \"imageClass\"\n      },\n      outputs: {\n        imageClick: \"imageClick\",\n        indexChange: \"indexChange\",\n        previousImage: \"previousImage\",\n        nextImage: \"nextImage\"\n      },\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 14,\n      vars: 10,\n      consts: [[1, \"image-carousel-container\", 3, \"mouseenter\", \"mouseleave\"], [1, \"main-image-container\"], [1, \"relative\", \"w-full\", \"h-full\", \"overflow-hidden\", \"rounded-xl\", \"border-2\", \"border-gray-200\", \"cursor-pointer\", \"group\", \"shadow-md\", \"hover:shadow-lg\", \"transition-all\", \"duration-300\", 3, \"click\"], [\"class\", \"w-full h-full object-cover transition-transform duration-500 group-hover:scale-110\", 3, \"class\", \"src\", \"alt\", \"title\", 4, \"ngIf\"], [1, \"absolute\", \"inset-0\", \"bg-black\", \"bg-opacity-0\", \"group-hover:bg-opacity-30\", \"transition-all\", \"duration-300\", \"flex\", \"items-center\", \"justify-center\"], [1, \"transform\", \"scale-75\", \"group-hover:scale-100\", \"transition-transform\", \"duration-300\"], [1, \"w-12\", \"h-12\", \"bg-white\", \"bg-opacity-90\", \"rounded-full\", \"flex\", \"items-center\", \"justify-center\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-6\", \"h-6\", \"text-gray-800\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0zM10 7v3m0 0v3m0-3h3m-3 0H7\"], [\"class\", \"nav-btn nav-btn-prev absolute left-2 top-1/2 transform -translate-y-1/2 bg-white bg-opacity-95 text-gray-800 rounded-full w-8 h-8 flex items-center justify-center hover:bg-opacity-100 hover:shadow-md transition-all z-10 opacity-0 group-hover:opacity-100\", \"title\", \"\\u4E0A\\u4E00\\u5F35\\u5716\\u7247\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"nav-btn nav-btn-next absolute right-2 top-1/2 transform -translate-y-1/2 bg-white bg-opacity-95 text-gray-800 rounded-full w-8 h-8 flex items-center justify-center hover:bg-opacity-100 hover:shadow-md transition-all z-10 opacity-0 group-hover:opacity-100\", \"title\", \"\\u4E0B\\u4E00\\u5F35\\u5716\\u7247\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"absolute bottom-2 right-2 bg-black bg-opacity-80 text-white text-xs px-2 py-1 rounded-lg backdrop-blur-sm\", 4, \"ngIf\"], [\"class\", \"thumbnail-navigation flex gap-2 mt-3 overflow-x-auto pb-2\", 4, \"ngIf\"], [\"class\", \"empty-state aspect-square w-full flex flex-col items-center justify-center border-2 border-dashed border-gray-300 rounded-xl bg-gray-50 text-gray-400\", 4, \"ngIf\"], [1, \"w-full\", \"h-full\", \"object-cover\", \"transition-transform\", \"duration-500\", \"group-hover:scale-110\", 3, \"src\", \"alt\", \"title\"], [\"title\", \"\\u4E0A\\u4E00\\u5F35\\u5716\\u7247\", 1, \"nav-btn\", \"nav-btn-prev\", \"absolute\", \"left-2\", \"top-1/2\", \"transform\", \"-translate-y-1/2\", \"bg-white\", \"bg-opacity-95\", \"text-gray-800\", \"rounded-full\", \"w-8\", \"h-8\", \"flex\", \"items-center\", \"justify-center\", \"hover:bg-opacity-100\", \"hover:shadow-md\", \"transition-all\", \"z-10\", \"opacity-0\", \"group-hover:opacity-100\", 3, \"click\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-4\", \"h-4\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2.5\", \"d\", \"M15 19l-7-7 7-7\"], [\"title\", \"\\u4E0B\\u4E00\\u5F35\\u5716\\u7247\", 1, \"nav-btn\", \"nav-btn-next\", \"absolute\", \"right-2\", \"top-1/2\", \"transform\", \"-translate-y-1/2\", \"bg-white\", \"bg-opacity-95\", \"text-gray-800\", \"rounded-full\", \"w-8\", \"h-8\", \"flex\", \"items-center\", \"justify-center\", \"hover:bg-opacity-100\", \"hover:shadow-md\", \"transition-all\", \"z-10\", \"opacity-0\", \"group-hover:opacity-100\", 3, \"click\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2.5\", \"d\", \"M9 5l7 7-7 7\"], [1, \"absolute\", \"bottom-2\", \"right-2\", \"bg-black\", \"bg-opacity-80\", \"text-white\", \"text-xs\", \"px-2\", \"py-1\", \"rounded-lg\", \"backdrop-blur-sm\"], [1, \"thumbnail-navigation\", \"flex\", \"gap-2\", \"mt-3\", \"overflow-x-auto\", \"pb-2\"], [\"class\", \"thumbnail-btn flex-shrink-0 w-12 h-12 border-2 rounded-lg overflow-hidden hover:border-blue-400 transition-all duration-200 cursor-pointer hover:scale-105\", 3, \"border-blue-500\", \"border-gray-300\", \"ring-2\", \"ring-blue-200\", \"title\", \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"thumbnail-btn\", \"flex-shrink-0\", \"w-12\", \"h-12\", \"border-2\", \"rounded-lg\", \"overflow-hidden\", \"hover:border-blue-400\", \"transition-all\", \"duration-200\", \"cursor-pointer\", \"hover:scale-105\", 3, \"click\", \"title\"], [1, \"w-full\", \"h-full\", \"object-cover\", \"transition-transform\", \"hover:scale-110\", 3, \"src\", \"alt\"], [1, \"empty-state\", \"aspect-square\", \"w-full\", \"flex\", \"flex-col\", \"items-center\", \"justify-center\", \"border-2\", \"border-dashed\", \"border-gray-300\", \"rounded-xl\", \"bg-gray-50\", \"text-gray-400\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-12\", \"h-12\", \"mb-2\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"1.5\", \"d\", \"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z\"], [1, \"text-sm\"]],\n      template: function ImageCarouselComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵlistener(\"mouseenter\", function ImageCarouselComponent_Template_div_mouseenter_0_listener() {\n            return ctx.pauseAutoplay();\n          })(\"mouseleave\", function ImageCarouselComponent_Template_div_mouseleave_0_listener() {\n            return ctx.resumeAutoplay();\n          });\n          i0.ɵɵelementStart(1, \"div\", 1)(2, \"div\", 2);\n          i0.ɵɵlistener(\"click\", function ImageCarouselComponent_Template_div_click_2_listener() {\n            return ctx.onImageClick();\n          });\n          i0.ɵɵtemplate(3, ImageCarouselComponent_img_3_Template, 1, 5, \"img\", 3);\n          i0.ɵɵelementStart(4, \"div\", 4)(5, \"div\", 5)(6, \"div\", 6);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(7, \"svg\", 7);\n          i0.ɵɵelement(8, \"path\", 8);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵtemplate(9, ImageCarouselComponent_button_9_Template, 3, 0, \"button\", 9)(10, ImageCarouselComponent_button_10_Template, 3, 0, \"button\", 10)(11, ImageCarouselComponent_div_11_Template, 2, 2, \"div\", 11);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(12, ImageCarouselComponent_div_12_Template, 2, 1, \"div\", 12)(13, ImageCarouselComponent_div_13_Template, 5, 0, \"div\", 13);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵclassMap(ctx.containerClass);\n          i0.ɵɵadvance();\n          i0.ɵɵclassMap(ctx.aspectRatio);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.getCurrentImage());\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"ngIf\", ctx.showNavigation && ctx.images.length > 1);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.showNavigation && ctx.images.length > 1);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.showCounter && ctx.images.length > 1);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.showThumbnails && ctx.images.length > 1);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.images.length === 0);\n        }\n      },\n      dependencies: [CommonModule, i1.NgForOf, i1.NgIf],\n      styles: [\"[_nghost-%COMP%] {\\n  display: block;\\n  width: 100%;\\n}\\n\\n.image-carousel-container[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n.image-carousel-container[_ngcontent-%COMP%]   .main-image-container[_ngcontent-%COMP%] {\\n  position: relative;\\n  width: 100%;\\n}\\n.image-carousel-container[_ngcontent-%COMP%]   .main-image-container.aspect-square[_ngcontent-%COMP%] {\\n  aspect-ratio: 1/1;\\n}\\n.image-carousel-container[_ngcontent-%COMP%]   .main-image-container.aspect-video[_ngcontent-%COMP%] {\\n  aspect-ratio: 16/9;\\n}\\n.image-carousel-container[_ngcontent-%COMP%]   .main-image-container.aspect-photo[_ngcontent-%COMP%] {\\n  aspect-ratio: 4/3;\\n}\\n.image-carousel-container[_ngcontent-%COMP%]   .main-image-container.aspect-wide[_ngcontent-%COMP%] {\\n  aspect-ratio: 21/9;\\n}\\n.image-carousel-container[_ngcontent-%COMP%]   .main-image-container[_ngcontent-%COMP%]   .nav-btn[_ngcontent-%COMP%] {\\n  transition: all 0.3s ease;\\n  -webkit-backdrop-filter: blur(4px);\\n          backdrop-filter: blur(4px);\\n}\\n.image-carousel-container[_ngcontent-%COMP%]   .main-image-container[_ngcontent-%COMP%]   .nav-btn[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-50%) scale(1.1);\\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\\n}\\n.image-carousel-container[_ngcontent-%COMP%]   .main-image-container[_ngcontent-%COMP%]   .nav-btn[_ngcontent-%COMP%]:active {\\n  transform: translateY(-50%) scale(0.95);\\n}\\n.image-carousel-container[_ngcontent-%COMP%]   .main-image-container[_ngcontent-%COMP%]   .nav-btn[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%] {\\n  transition: transform 0.2s ease;\\n}\\n.image-carousel-container[_ngcontent-%COMP%]   .main-image-container[_ngcontent-%COMP%]   .nav-btn[_ngcontent-%COMP%]:hover   svg[_ngcontent-%COMP%] {\\n  transform: scale(1.1);\\n}\\n.image-carousel-container[_ngcontent-%COMP%]   .main-image-container[_ngcontent-%COMP%]   .nav-btn-prev[_ngcontent-%COMP%] {\\n  left: 0.5rem;\\n}\\n.image-carousel-container[_ngcontent-%COMP%]   .main-image-container[_ngcontent-%COMP%]   .nav-btn-next[_ngcontent-%COMP%] {\\n  right: 0.5rem;\\n}\\n.image-carousel-container[_ngcontent-%COMP%]   .thumbnail-navigation[_ngcontent-%COMP%] {\\n  scrollbar-width: thin;\\n  scrollbar-color: #cbd5e0 #f7fafc;\\n}\\n.image-carousel-container[_ngcontent-%COMP%]   .thumbnail-navigation[_ngcontent-%COMP%]::-webkit-scrollbar {\\n  height: 4px;\\n}\\n.image-carousel-container[_ngcontent-%COMP%]   .thumbnail-navigation[_ngcontent-%COMP%]::-webkit-scrollbar-track {\\n  background: #f7fafc;\\n  border-radius: 2px;\\n}\\n.image-carousel-container[_ngcontent-%COMP%]   .thumbnail-navigation[_ngcontent-%COMP%]::-webkit-scrollbar-thumb {\\n  background: #cbd5e0;\\n  border-radius: 2px;\\n}\\n.image-carousel-container[_ngcontent-%COMP%]   .thumbnail-navigation[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover {\\n  background: #a0aec0;\\n}\\n.image-carousel-container[_ngcontent-%COMP%]   .thumbnail-navigation[_ngcontent-%COMP%]   .thumbnail-btn[_ngcontent-%COMP%] {\\n  transition: all 0.2s ease;\\n  flex-shrink: 0;\\n}\\n.image-carousel-container[_ngcontent-%COMP%]   .thumbnail-navigation[_ngcontent-%COMP%]   .thumbnail-btn[_ngcontent-%COMP%]:hover {\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\\n}\\n.image-carousel-container[_ngcontent-%COMP%]   .thumbnail-navigation[_ngcontent-%COMP%]   .thumbnail-btn.border-blue-500[_ngcontent-%COMP%] {\\n  box-shadow: 0 0 0 1px rgba(59, 130, 246, 0.5);\\n}\\n.image-carousel-container[_ngcontent-%COMP%]   .thumbnail-navigation[_ngcontent-%COMP%]   .thumbnail-btn[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  transition: transform 0.2s ease;\\n}\\n.image-carousel-container[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%] {\\n  min-height: 200px;\\n  transition: all 0.3s ease;\\n}\\n.image-carousel-container[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%]:hover {\\n  border-color: #a0aec0;\\n  background-color: #edf2f7;\\n}\\n.image-carousel-container[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%] {\\n  transition: transform 0.3s ease;\\n}\\n.image-carousel-container[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%]:hover   svg[_ngcontent-%COMP%] {\\n  transform: scale(1.1);\\n}\\n\\n@media (max-width: 768px) {\\n  .image-carousel-container[_ngcontent-%COMP%]   .main-image-container[_ngcontent-%COMP%]   .nav-btn[_ngcontent-%COMP%] {\\n    width: 2rem;\\n    height: 2rem;\\n  }\\n  .image-carousel-container[_ngcontent-%COMP%]   .main-image-container[_ngcontent-%COMP%]   .nav-btn[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%] {\\n    width: 1rem;\\n    height: 1rem;\\n  }\\n  .image-carousel-container[_ngcontent-%COMP%]   .thumbnail-navigation[_ngcontent-%COMP%]   .thumbnail-btn[_ngcontent-%COMP%] {\\n    width: 2.5rem;\\n    height: 2.5rem;\\n  }\\n}\\n@keyframes _ngcontent-%COMP%_fadeIn {\\n  from {\\n    opacity: 0;\\n  }\\n  to {\\n    opacity: 1;\\n  }\\n}\\n@keyframes _ngcontent-%COMP%_slideInFromLeft {\\n  from {\\n    transform: translateX(-100%);\\n    opacity: 0;\\n  }\\n  to {\\n    transform: translateX(0);\\n    opacity: 1;\\n  }\\n}\\n@keyframes _ngcontent-%COMP%_slideInFromRight {\\n  from {\\n    transform: translateX(100%);\\n    opacity: 0;\\n  }\\n  to {\\n    transform: translateX(0);\\n    opacity: 1;\\n  }\\n}\\n.animate-fade-in[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_fadeIn 0.3s ease-in-out;\\n}\\n\\n.animate-slide-in-left[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_slideInFromLeft 0.4s ease-out;\\n}\\n\\n.animate-slide-in-right[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_slideInFromRight 0.4s ease-out;\\n}\\n/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImltYWdlLWNhcm91c2VsLmNvbXBvbmVudC5zY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUFBO0VBQ0UsY0FBQTtFQUNBLFdBQUE7QUFDRjs7QUFFQTtFQUNFLFdBQUE7QUFDRjtBQUNFO0VBQ0Usa0JBQUE7RUFDQSxXQUFBO0FBQ0o7QUFFSTtFQUNFLGlCQUFBO0FBQU47QUFHSTtFQUNFLGtCQUFBO0FBRE47QUFJSTtFQUNFLGlCQUFBO0FBRk47QUFLSTtFQUNFLGtCQUFBO0FBSE47QUFNSTtFQUNFLHlCQUFBO0VBQ0Esa0NBQUE7VUFBQSwwQkFBQTtBQUpOO0FBTU07RUFDRSxzQ0FBQTtFQUNBLDBDQUFBO0FBSlI7QUFPTTtFQUNFLHVDQUFBO0FBTFI7QUFRTTtFQUNFLCtCQUFBO0FBTlI7QUFTTTtFQUNFLHFCQUFBO0FBUFI7QUFXSTtFQUNFLFlBQUE7QUFUTjtBQVlJO0VBQ0UsYUFBQTtBQVZOO0FBY0U7RUFDRSxxQkFBQTtFQUNBLGdDQUFBO0FBWko7QUFjSTtFQUNFLFdBQUE7QUFaTjtBQWVJO0VBQ0UsbUJBQUE7RUFDQSxrQkFBQTtBQWJOO0FBZ0JJO0VBQ0UsbUJBQUE7RUFDQSxrQkFBQTtBQWROO0FBZ0JNO0VBQ0UsbUJBQUE7QUFkUjtBQWtCSTtFQUNFLHlCQUFBO0VBQ0EsY0FBQTtBQWhCTjtBQWtCTTtFQUNFLHdDQUFBO0FBaEJSO0FBbUJNO0VBQ0UsNkNBQUE7QUFqQlI7QUFvQk07RUFDRSwrQkFBQTtBQWxCUjtBQXVCRTtFQUNFLGlCQUFBO0VBQ0EseUJBQUE7QUFyQko7QUF1Qkk7RUFDRSxxQkFBQTtFQUNBLHlCQUFBO0FBckJOO0FBd0JJO0VBQ0UsK0JBQUE7QUF0Qk47QUF5Qkk7RUFDRSxxQkFBQTtBQXZCTjs7QUE2QkE7RUFHTTtJQUNFLFdBQUE7SUFDQSxZQUFBO0VBNUJOO0VBOEJNO0lBQ0UsV0FBQTtJQUNBLFlBQUE7RUE1QlI7RUFrQ0k7SUFDRSxhQUFBO0lBQ0EsY0FBQTtFQWhDTjtBQUNGO0FBc0NBO0VBQ0U7SUFDRSxVQUFBO0VBcENGO0VBc0NBO0lBQ0UsVUFBQTtFQXBDRjtBQUNGO0FBdUNBO0VBQ0U7SUFDRSw0QkFBQTtJQUNBLFVBQUE7RUFyQ0Y7RUF1Q0E7SUFDRSx3QkFBQTtJQUNBLFVBQUE7RUFyQ0Y7QUFDRjtBQXdDQTtFQUNFO0lBQ0UsMkJBQUE7SUFDQSxVQUFBO0VBdENGO0VBd0NBO0lBQ0Usd0JBQUE7SUFDQSxVQUFBO0VBdENGO0FBQ0Y7QUF5Q0E7RUFDRSxrQ0FBQTtBQXZDRjs7QUEwQ0E7RUFDRSx3Q0FBQTtBQXZDRjs7QUEwQ0E7RUFDRSx5Q0FBQTtBQXZDRiIsImZpbGUiOiJpbWFnZS1jYXJvdXNlbC5jb21wb25lbnQuc2NzcyIsInNvdXJjZXNDb250ZW50IjpbIjpob3N0IHtcclxuICBkaXNwbGF5OiBibG9jaztcclxuICB3aWR0aDogMTAwJTtcclxufVxyXG5cclxuLmltYWdlLWNhcm91c2VsLWNvbnRhaW5lciB7XHJcbiAgd2lkdGg6IDEwMCU7XHJcbiAgXHJcbiAgLm1haW4taW1hZ2UtY29udGFpbmVyIHtcclxuICAgIHBvc2l0aW9uOiByZWxhdGl2ZTtcclxuICAgIHdpZHRoOiAxMDAlO1xyXG4gICAgXHJcbiAgICAvLyDpoJDoqK3lr6zpq5jmr5RcclxuICAgICYuYXNwZWN0LXNxdWFyZSB7XHJcbiAgICAgIGFzcGVjdC1yYXRpbzogMSAvIDE7XHJcbiAgICB9XHJcbiAgICBcclxuICAgICYuYXNwZWN0LXZpZGVvIHtcclxuICAgICAgYXNwZWN0LXJhdGlvOiAxNiAvIDk7XHJcbiAgICB9XHJcbiAgICBcclxuICAgICYuYXNwZWN0LXBob3RvIHtcclxuICAgICAgYXNwZWN0LXJhdGlvOiA0IC8gMztcclxuICAgIH1cclxuICAgIFxyXG4gICAgJi5hc3BlY3Qtd2lkZSB7XHJcbiAgICAgIGFzcGVjdC1yYXRpbzogMjEgLyA5O1xyXG4gICAgfVxyXG4gICAgXHJcbiAgICAubmF2LWJ0biB7XHJcbiAgICAgIHRyYW5zaXRpb246IGFsbCAwLjNzIGVhc2U7XHJcbiAgICAgIGJhY2tkcm9wLWZpbHRlcjogYmx1cig0cHgpO1xyXG4gICAgICBcclxuICAgICAgJjpob3ZlciB7XHJcbiAgICAgICAgdHJhbnNmb3JtOiB0cmFuc2xhdGVZKC01MCUpIHNjYWxlKDEuMSk7XHJcbiAgICAgICAgYm94LXNoYWRvdzogMCA0cHggMTJweCByZ2JhKDAsIDAsIDAsIDAuMTUpO1xyXG4gICAgICB9XHJcbiAgICAgIFxyXG4gICAgICAmOmFjdGl2ZSB7XHJcbiAgICAgICAgdHJhbnNmb3JtOiB0cmFuc2xhdGVZKC01MCUpIHNjYWxlKDAuOTUpO1xyXG4gICAgICB9XHJcbiAgICAgIFxyXG4gICAgICBzdmcge1xyXG4gICAgICAgIHRyYW5zaXRpb246IHRyYW5zZm9ybSAwLjJzIGVhc2U7XHJcbiAgICAgIH1cclxuICAgICAgXHJcbiAgICAgICY6aG92ZXIgc3ZnIHtcclxuICAgICAgICB0cmFuc2Zvcm06IHNjYWxlKDEuMSk7XHJcbiAgICAgIH1cclxuICAgIH1cclxuICAgIFxyXG4gICAgLm5hdi1idG4tcHJldiB7XHJcbiAgICAgIGxlZnQ6IDAuNXJlbTtcclxuICAgIH1cclxuICAgIFxyXG4gICAgLm5hdi1idG4tbmV4dCB7XHJcbiAgICAgIHJpZ2h0OiAwLjVyZW07XHJcbiAgICB9XHJcbiAgfVxyXG4gIFxyXG4gIC50aHVtYm5haWwtbmF2aWdhdGlvbiB7XHJcbiAgICBzY3JvbGxiYXItd2lkdGg6IHRoaW47XHJcbiAgICBzY3JvbGxiYXItY29sb3I6ICNjYmQ1ZTAgI2Y3ZmFmYztcclxuICAgIFxyXG4gICAgJjo6LXdlYmtpdC1zY3JvbGxiYXIge1xyXG4gICAgICBoZWlnaHQ6IDRweDtcclxuICAgIH1cclxuICAgIFxyXG4gICAgJjo6LXdlYmtpdC1zY3JvbGxiYXItdHJhY2sge1xyXG4gICAgICBiYWNrZ3JvdW5kOiAjZjdmYWZjO1xyXG4gICAgICBib3JkZXItcmFkaXVzOiAycHg7XHJcbiAgICB9XHJcbiAgICBcclxuICAgICY6Oi13ZWJraXQtc2Nyb2xsYmFyLXRodW1iIHtcclxuICAgICAgYmFja2dyb3VuZDogI2NiZDVlMDtcclxuICAgICAgYm9yZGVyLXJhZGl1czogMnB4O1xyXG4gICAgICBcclxuICAgICAgJjpob3ZlciB7XHJcbiAgICAgICAgYmFja2dyb3VuZDogI2EwYWVjMDtcclxuICAgICAgfVxyXG4gICAgfVxyXG4gICAgXHJcbiAgICAudGh1bWJuYWlsLWJ0biB7XHJcbiAgICAgIHRyYW5zaXRpb246IGFsbCAwLjJzIGVhc2U7XHJcbiAgICAgIGZsZXgtc2hyaW5rOiAwO1xyXG4gICAgICBcclxuICAgICAgJjpob3ZlciB7XHJcbiAgICAgICAgYm94LXNoYWRvdzogMCAycHggOHB4IHJnYmEoMCwgMCwgMCwgMC4xKTtcclxuICAgICAgfVxyXG4gICAgICBcclxuICAgICAgJi5ib3JkZXItYmx1ZS01MDAge1xyXG4gICAgICAgIGJveC1zaGFkb3c6IDAgMCAwIDFweCByZ2JhKDU5LCAxMzAsIDI0NiwgMC41KTtcclxuICAgICAgfVxyXG4gICAgICBcclxuICAgICAgaW1nIHtcclxuICAgICAgICB0cmFuc2l0aW9uOiB0cmFuc2Zvcm0gMC4ycyBlYXNlO1xyXG4gICAgICB9XHJcbiAgICB9XHJcbiAgfVxyXG4gIFxyXG4gIC5lbXB0eS1zdGF0ZSB7XHJcbiAgICBtaW4taGVpZ2h0OiAyMDBweDtcclxuICAgIHRyYW5zaXRpb246IGFsbCAwLjNzIGVhc2U7XHJcbiAgICBcclxuICAgICY6aG92ZXIge1xyXG4gICAgICBib3JkZXItY29sb3I6ICNhMGFlYzA7XHJcbiAgICAgIGJhY2tncm91bmQtY29sb3I6ICNlZGYyZjc7XHJcbiAgICB9XHJcbiAgICBcclxuICAgIHN2ZyB7XHJcbiAgICAgIHRyYW5zaXRpb246IHRyYW5zZm9ybSAwLjNzIGVhc2U7XHJcbiAgICB9XHJcbiAgICBcclxuICAgICY6aG92ZXIgc3ZnIHtcclxuICAgICAgdHJhbnNmb3JtOiBzY2FsZSgxLjEpO1xyXG4gICAgfVxyXG4gIH1cclxufVxyXG5cclxuLy8g6Z+/5oeJ5byP6Kit6KiIXHJcbkBtZWRpYSAobWF4LXdpZHRoOiA3NjhweCkge1xyXG4gIC5pbWFnZS1jYXJvdXNlbC1jb250YWluZXIge1xyXG4gICAgLm1haW4taW1hZ2UtY29udGFpbmVyIHtcclxuICAgICAgLm5hdi1idG4ge1xyXG4gICAgICAgIHdpZHRoOiAycmVtO1xyXG4gICAgICAgIGhlaWdodDogMnJlbTtcclxuICAgICAgICBcclxuICAgICAgICBzdmcge1xyXG4gICAgICAgICAgd2lkdGg6IDFyZW07XHJcbiAgICAgICAgICBoZWlnaHQ6IDFyZW07XHJcbiAgICAgICAgfVxyXG4gICAgICB9XHJcbiAgICB9XHJcbiAgICBcclxuICAgIC50aHVtYm5haWwtbmF2aWdhdGlvbiB7XHJcbiAgICAgIC50aHVtYm5haWwtYnRuIHtcclxuICAgICAgICB3aWR0aDogMi41cmVtO1xyXG4gICAgICAgIGhlaWdodDogMi41cmVtO1xyXG4gICAgICB9XHJcbiAgICB9XHJcbiAgfVxyXG59XHJcblxyXG4vLyDli5XnlavmlYjmnpxcclxuQGtleWZyYW1lcyBmYWRlSW4ge1xyXG4gIGZyb20ge1xyXG4gICAgb3BhY2l0eTogMDtcclxuICB9XHJcbiAgdG8ge1xyXG4gICAgb3BhY2l0eTogMTtcclxuICB9XHJcbn1cclxuXHJcbkBrZXlmcmFtZXMgc2xpZGVJbkZyb21MZWZ0IHtcclxuICBmcm9tIHtcclxuICAgIHRyYW5zZm9ybTogdHJhbnNsYXRlWCgtMTAwJSk7XHJcbiAgICBvcGFjaXR5OiAwO1xyXG4gIH1cclxuICB0byB7XHJcbiAgICB0cmFuc2Zvcm06IHRyYW5zbGF0ZVgoMCk7XHJcbiAgICBvcGFjaXR5OiAxO1xyXG4gIH1cclxufVxyXG5cclxuQGtleWZyYW1lcyBzbGlkZUluRnJvbVJpZ2h0IHtcclxuICBmcm9tIHtcclxuICAgIHRyYW5zZm9ybTogdHJhbnNsYXRlWCgxMDAlKTtcclxuICAgIG9wYWNpdHk6IDA7XHJcbiAgfVxyXG4gIHRvIHtcclxuICAgIHRyYW5zZm9ybTogdHJhbnNsYXRlWCgwKTtcclxuICAgIG9wYWNpdHk6IDE7XHJcbiAgfVxyXG59XHJcblxyXG4uYW5pbWF0ZS1mYWRlLWluIHtcclxuICBhbmltYXRpb246IGZhZGVJbiAwLjNzIGVhc2UtaW4tb3V0O1xyXG59XHJcblxyXG4uYW5pbWF0ZS1zbGlkZS1pbi1sZWZ0IHtcclxuICBhbmltYXRpb246IHNsaWRlSW5Gcm9tTGVmdCAwLjRzIGVhc2Utb3V0O1xyXG59XHJcblxyXG4uYW5pbWF0ZS1zbGlkZS1pbi1yaWdodCB7XHJcbiAgYW5pbWF0aW9uOiBzbGlkZUluRnJvbVJpZ2h0IDAuNHMgZWFzZS1vdXQ7XHJcbn1cclxuIl19 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["EventEmitter", "CommonModule", "i0", "ɵɵelement", "ɵɵclassMap", "ctx_r0", "imageClass", "ɵɵproperty", "tmp_2_0", "getCurrentImage", "url", "ɵɵsanitizeUrl", "tmp_3_0", "alt", "name", "tmp_4_0", "description", "ɵɵelementStart", "ɵɵlistener", "ImageCarouselComponent_button_9_Template_button_click_0_listener", "$event", "ɵɵrestoreView", "_r2", "ɵɵnextContext", "goToPrevious", "ɵɵresetView", "stopPropagation", "ɵɵelementEnd", "ImageCarouselComponent_button_10_Template_button_click_0_listener", "_r3", "goToNext", "ɵɵtext", "ɵɵadvance", "ɵɵtextInterpolate2", "currentIndex", "images", "length", "ImageCarouselComponent_div_12_button_1_Template_button_click_0_listener", "i_r5", "_r4", "index", "onThumbnailClick", "ɵɵclassProp", "image_r6", "ɵɵtemplate", "ImageCarouselComponent_div_12_button_1_Template", "ImageCarouselComponent", "constructor", "showThumbnails", "showCounter", "showNavigation", "aspectRatio", "autoplay", "autoplayInterval", "enableKeyboard", "containerClass", "imageClick", "indexChange", "previousImage", "nextImage", "ngOnInit", "startAutoplay", "ngOnDestroy", "stopAutoplay", "handleKeyboardEvent", "event", "key", "preventDefault", "newIndex", "setCurrentIndex", "emit", "goToIndex", "onImageClick", "currentImage", "image", "restartAutoplay", "autoplayTimer", "setInterval", "clearInterval", "pauseAutoplay", "resumeAutoplay", "selectors", "hostBindings", "ImageCarouselComponent_HostBindings", "rf", "ctx", "ImageCarouselComponent_keydown_HostBindingHandler", "ɵɵresolveDocument", "ImageCarouselComponent_Template_div_mouseenter_0_listener", "ImageCarouselComponent_Template_div_mouseleave_0_listener", "ImageCarouselComponent_Template_div_click_2_listener", "ImageCarouselComponent_img_3_Template", "ImageCarouselComponent_button_9_Template", "ImageCarouselComponent_button_10_Template", "ImageCarouselComponent_div_11_Template", "ImageCarouselComponent_div_12_Template", "ImageCarouselComponent_div_13_Template", "i1", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "styles"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\@theme\\components\\image-carousel\\image-carousel.component.ts", "C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\@theme\\components\\image-carousel\\image-carousel.component.html"], "sourcesContent": ["import { Component, Input, Output, EventEmitter, OnInit, OnDestroy, HostListener } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\n\r\nexport interface ImageCarouselItem {\r\n  url: string;\r\n  name?: string;\r\n  description?: string;\r\n  alt?: string;\r\n}\r\n\r\nexport interface CarouselClickEvent {\r\n  index: number;\r\n  image: ImageCarouselItem;\r\n}\r\n\r\n@Component({\r\n  selector: 'ngx-image-carousel',\r\n  standalone: true,\r\n  imports: [CommonModule],\r\n  templateUrl: './image-carousel.component.html',\r\n  styleUrls: ['./image-carousel.component.scss']\r\n})\r\nexport class ImageCarouselComponent implements OnInit, OnDestroy {\r\n  @Input() images: ImageCarouselItem[] = [];\r\n  @Input() currentIndex: number = 0;\r\n  @Input() showThumbnails: boolean = true;\r\n  @Input() showCounter: boolean = true;\r\n  @Input() showNavigation: boolean = true;\r\n  @Input() aspectRatio: string = 'aspect-square';\r\n  @Input() autoplay: boolean = false;\r\n  @Input() autoplayInterval: number = 3000;\r\n  @Input() enableKeyboard: boolean = true;\r\n  @Input() containerClass: string = '';\r\n  @Input() imageClass: string = '';\r\n\r\n  @Output() imageClick = new EventEmitter<CarouselClickEvent>();\r\n  @Output() indexChange = new EventEmitter<number>();\r\n  @Output() previousImage = new EventEmitter<number>();\r\n  @Output() nextImage = new EventEmitter<number>();\r\n\r\n  private autoplayTimer: any;\r\n\r\n  ngOnInit(): void {\r\n    if (this.currentIndex >= this.images.length) {\r\n      this.currentIndex = 0;\r\n    }\r\n    this.startAutoplay();\r\n  }\r\n\r\n  ngOnDestroy(): void {\r\n    this.stopAutoplay();\r\n  }\r\n\r\n  @HostListener('document:keydown', ['$event'])\r\n  handleKeyboardEvent(event: KeyboardEvent): void {\r\n    if (!this.enableKeyboard || this.images.length <= 1) return;\r\n    \r\n    switch (event.key) {\r\n      case 'ArrowLeft':\r\n        event.preventDefault();\r\n        this.goToPrevious();\r\n        break;\r\n      case 'ArrowRight':\r\n        event.preventDefault();\r\n        this.goToNext();\r\n        break;\r\n    }\r\n  }\r\n\r\n  getCurrentImage(): ImageCarouselItem | null {\r\n    if (this.images.length === 0 || this.currentIndex < 0 || this.currentIndex >= this.images.length) {\r\n      return null;\r\n    }\r\n    return this.images[this.currentIndex];\r\n  }\r\n\r\n  goToNext(): void {\r\n    if (this.images.length <= 1) return;\r\n    \r\n    const newIndex = (this.currentIndex + 1) % this.images.length;\r\n    this.setCurrentIndex(newIndex);\r\n    this.nextImage.emit(newIndex);\r\n  }\r\n\r\n  goToPrevious(): void {\r\n    if (this.images.length <= 1) return;\r\n    \r\n    const newIndex = this.currentIndex === 0 ? this.images.length - 1 : this.currentIndex - 1;\r\n    this.setCurrentIndex(newIndex);\r\n    this.previousImage.emit(newIndex);\r\n  }\r\n\r\n  goToIndex(index: number): void {\r\n    if (index >= 0 && index < this.images.length && index !== this.currentIndex) {\r\n      this.setCurrentIndex(index);\r\n    }\r\n  }\r\n\r\n  onImageClick(): void {\r\n    const currentImage = this.getCurrentImage();\r\n    if (currentImage) {\r\n      this.imageClick.emit({ \r\n        index: this.currentIndex, \r\n        image: currentImage \r\n      });\r\n    }\r\n  }\r\n\r\n  onThumbnailClick(index: number): void {\r\n    this.goToIndex(index);\r\n  }\r\n\r\n  private setCurrentIndex(index: number): void {\r\n    this.currentIndex = index;\r\n    this.indexChange.emit(index);\r\n    this.restartAutoplay();\r\n  }\r\n\r\n  private startAutoplay(): void {\r\n    if (!this.autoplay || this.images.length <= 1) return;\r\n    \r\n    this.autoplayTimer = setInterval(() => {\r\n      this.goToNext();\r\n    }, this.autoplayInterval);\r\n  }\r\n\r\n  private stopAutoplay(): void {\r\n    if (this.autoplayTimer) {\r\n      clearInterval(this.autoplayTimer);\r\n      this.autoplayTimer = null;\r\n    }\r\n  }\r\n\r\n  private restartAutoplay(): void {\r\n    this.stopAutoplay();\r\n    this.startAutoplay();\r\n  }\r\n\r\n  // 暫停自動播放（滑鼠懸停時使用）\r\n  pauseAutoplay(): void {\r\n    this.stopAutoplay();\r\n  }\r\n\r\n  // 恢復自動播放（滑鼠離開時使用）\r\n  resumeAutoplay(): void {\r\n    this.startAutoplay();\r\n  }\r\n}\r\n", "<!-- 主要圖片顯示區域 -->\r\n<div class=\"image-carousel-container\" [class]=\"containerClass\"\r\n     (mouseenter)=\"pauseAutoplay()\" \r\n     (mouseleave)=\"resumeAutoplay()\">\r\n  \r\n  <!-- 主圖片容器 -->\r\n  <div class=\"main-image-container\" [class]=\"aspectRatio\">\r\n    <div class=\"relative w-full h-full overflow-hidden rounded-xl border-2 border-gray-200 cursor-pointer group shadow-md hover:shadow-lg transition-all duration-300\"\r\n         (click)=\"onImageClick()\">\r\n      \r\n      <!-- 主圖片 -->\r\n      <img *ngIf=\"getCurrentImage()\" \r\n           class=\"w-full h-full object-cover transition-transform duration-500 group-hover:scale-110\"\r\n           [class]=\"imageClass\"\r\n           [src]=\"getCurrentImage()?.url\" \r\n           [alt]=\"getCurrentImage()?.alt || getCurrentImage()?.name || '圖片'\"\r\n           [title]=\"getCurrentImage()?.description\">\r\n\r\n      <!-- 放大圖示覆蓋層 -->\r\n      <div class=\"absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-all duration-300 flex items-center justify-center\">\r\n        <div class=\"transform scale-75 group-hover:scale-100 transition-transform duration-300\">\r\n          <div class=\"w-12 h-12 bg-white bg-opacity-90 rounded-full flex items-center justify-center\">\r\n            <svg class=\"w-6 h-6 text-gray-800\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n              <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\"\r\n                    d=\"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0zM10 7v3m0 0v3m0-3h3m-3 0H7\"></path>\r\n            </svg>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 導航按鈕 -->\r\n      <button *ngIf=\"showNavigation && images.length > 1\"\r\n              class=\"nav-btn nav-btn-prev absolute left-2 top-1/2 transform -translate-y-1/2 bg-white bg-opacity-95 text-gray-800 rounded-full w-8 h-8 flex items-center justify-center hover:bg-opacity-100 hover:shadow-md transition-all z-10 opacity-0 group-hover:opacity-100\"\r\n              (click)=\"goToPrevious(); $event.stopPropagation()\" \r\n              title=\"上一張圖片\">\r\n        <svg class=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n          <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2.5\" d=\"M15 19l-7-7 7-7\"></path>\r\n        </svg>\r\n      </button>\r\n\r\n      <button *ngIf=\"showNavigation && images.length > 1\"\r\n              class=\"nav-btn nav-btn-next absolute right-2 top-1/2 transform -translate-y-1/2 bg-white bg-opacity-95 text-gray-800 rounded-full w-8 h-8 flex items-center justify-center hover:bg-opacity-100 hover:shadow-md transition-all z-10 opacity-0 group-hover:opacity-100\"\r\n              (click)=\"goToNext(); $event.stopPropagation()\" \r\n              title=\"下一張圖片\">\r\n        <svg class=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n          <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2.5\" d=\"M9 5l7 7-7 7\"></path>\r\n        </svg>\r\n      </button>\r\n\r\n      <!-- 圖片計數器 -->\r\n      <div *ngIf=\"showCounter && images.length > 1\"\r\n           class=\"absolute bottom-2 right-2 bg-black bg-opacity-80 text-white text-xs px-2 py-1 rounded-lg backdrop-blur-sm\">\r\n        {{currentIndex + 1}} / {{images.length}}\r\n      </div>\r\n    </div>\r\n  </div>\r\n\r\n  <!-- 縮略圖導航 -->\r\n  <div *ngIf=\"showThumbnails && images.length > 1\" \r\n       class=\"thumbnail-navigation flex gap-2 mt-3 overflow-x-auto pb-2\">\r\n    <button *ngFor=\"let image of images; let i = index\"\r\n            class=\"thumbnail-btn flex-shrink-0 w-12 h-12 border-2 rounded-lg overflow-hidden hover:border-blue-400 transition-all duration-200 cursor-pointer hover:scale-105\"\r\n            [class.border-blue-500]=\"i === currentIndex\"\r\n            [class.border-gray-300]=\"i !== currentIndex\"\r\n            [class.ring-2]=\"i === currentIndex\"\r\n            [class.ring-blue-200]=\"i === currentIndex\"\r\n            (click)=\"onThumbnailClick(i)\"\r\n            [title]=\"'點選第 ' + (i + 1) + ' 張圖片' + (image.name ? ': ' + image.name : '')\">\r\n      <img class=\"w-full h-full object-cover transition-transform hover:scale-110\"\r\n           [src]=\"image.url\"\r\n           [alt]=\"image.alt || image.name || '縮略圖'\">\r\n    </button>\r\n  </div>\r\n\r\n  <!-- 空狀態 -->\r\n  <div *ngIf=\"images.length === 0\" \r\n       class=\"empty-state aspect-square w-full flex flex-col items-center justify-center border-2 border-dashed border-gray-300 rounded-xl bg-gray-50 text-gray-400\">\r\n    <svg class=\"w-12 h-12 mb-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n      <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\"\r\n            d=\"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z\">\r\n      </path>\r\n    </svg>\r\n    <span class=\"text-sm\">暫無圖片</span>\r\n  </div>\r\n</div>\r\n"], "mappings": "AAAA,SAAmCA,YAAY,QAAyC,eAAe;AACvG,SAASC,YAAY,QAAQ,iBAAiB;;;;;ICUxCC,EAAA,CAAAC,SAAA,cAK8C;;;;;;;IAHzCD,EAAA,CAAAE,UAAA,CAAAC,MAAA,CAAAC,UAAA,CAAoB;IAGpBJ,EAFA,CAAAK,UAAA,SAAAC,OAAA,GAAAH,MAAA,CAAAI,eAAA,qBAAAD,OAAA,CAAAE,GAAA,EAAAR,EAAA,CAAAS,aAAA,CAA8B,UAAAC,OAAA,GAAAP,MAAA,CAAAI,eAAA,qBAAAG,OAAA,CAAAC,GAAA,OAAAD,OAAA,GAAAP,MAAA,CAAAI,eAAA,qBAAAG,OAAA,CAAAE,IAAA,oBACmC,WAAAC,OAAA,GAAAV,MAAA,CAAAI,eAAA,qBAAAM,OAAA,CAAAC,WAAA,CACzB;;;;;;IAe7Cd,EAAA,CAAAe,cAAA,iBAGsB;IADdf,EAAA,CAAAgB,UAAA,mBAAAC,iEAAAC,MAAA;MAAAlB,EAAA,CAAAmB,aAAA,CAAAC,GAAA;MAAA,MAAAjB,MAAA,GAAAH,EAAA,CAAAqB,aAAA;MAASlB,MAAA,CAAAmB,YAAA,EAAc;MAAA,OAAAtB,EAAA,CAAAuB,WAAA,CAAEL,MAAA,CAAAM,eAAA,EAAwB;IAAA,EAAC;;IAExDxB,EAAA,CAAAe,cAAA,cAA2E;IACzEf,EAAA,CAAAC,SAAA,eAAmG;IAEvGD,EADE,CAAAyB,YAAA,EAAM,EACC;;;;;;IAETzB,EAAA,CAAAe,cAAA,iBAGsB;IADdf,EAAA,CAAAgB,UAAA,mBAAAU,kEAAAR,MAAA;MAAAlB,EAAA,CAAAmB,aAAA,CAAAQ,GAAA;MAAA,MAAAxB,MAAA,GAAAH,EAAA,CAAAqB,aAAA;MAASlB,MAAA,CAAAyB,QAAA,EAAU;MAAA,OAAA5B,EAAA,CAAAuB,WAAA,CAAEL,MAAA,CAAAM,eAAA,EAAwB;IAAA,EAAC;;IAEpDxB,EAAA,CAAAe,cAAA,cAA2E;IACzEf,EAAA,CAAAC,SAAA,eAAgG;IAEpGD,EADE,CAAAyB,YAAA,EAAM,EACC;;;;;IAGTzB,EAAA,CAAAe,cAAA,cACuH;IACrHf,EAAA,CAAA6B,MAAA,GACF;IAAA7B,EAAA,CAAAyB,YAAA,EAAM;;;;IADJzB,EAAA,CAAA8B,SAAA,EACF;IADE9B,EAAA,CAAA+B,kBAAA,MAAA5B,MAAA,CAAA6B,YAAA,aAAA7B,MAAA,CAAA8B,MAAA,CAAAC,MAAA,MACF;;;;;;IAOFlC,EAAA,CAAAe,cAAA,iBAOoF;IAD5Ef,EAAA,CAAAgB,UAAA,mBAAAmB,wEAAA;MAAA,MAAAC,IAAA,GAAApC,EAAA,CAAAmB,aAAA,CAAAkB,GAAA,EAAAC,KAAA;MAAA,MAAAnC,MAAA,GAAAH,EAAA,CAAAqB,aAAA;MAAA,OAAArB,EAAA,CAAAuB,WAAA,CAASpB,MAAA,CAAAoC,gBAAA,CAAAH,IAAA,CAAmB;IAAA,EAAC;IAEnCpC,EAAA,CAAAC,SAAA,cAE8C;IAChDD,EAAA,CAAAyB,YAAA,EAAS;;;;;;IANDzB,EAHA,CAAAwC,WAAA,oBAAAJ,IAAA,KAAAjC,MAAA,CAAA6B,YAAA,CAA4C,oBAAAI,IAAA,KAAAjC,MAAA,CAAA6B,YAAA,CACA,WAAAI,IAAA,KAAAjC,MAAA,CAAA6B,YAAA,CACT,kBAAAI,IAAA,KAAAjC,MAAA,CAAA6B,YAAA,CACO;IAE1ChC,EAAA,CAAAK,UAAA,mCAAA+B,IAAA,iCAAAK,QAAA,CAAA7B,IAAA,UAAA6B,QAAA,CAAA7B,IAAA,OAA2E;IAE5EZ,EAAA,CAAA8B,SAAA,EAAiB;IACjB9B,EADA,CAAAK,UAAA,QAAAoC,QAAA,CAAAjC,GAAA,EAAAR,EAAA,CAAAS,aAAA,CAAiB,QAAAgC,QAAA,CAAA9B,GAAA,IAAA8B,QAAA,CAAA7B,IAAA,yBACuB;;;;;IAZjDZ,EAAA,CAAAe,cAAA,cACuE;IACrEf,EAAA,CAAA0C,UAAA,IAAAC,+CAAA,sBAOoF;IAKtF3C,EAAA,CAAAyB,YAAA,EAAM;;;;IAZsBzB,EAAA,CAAA8B,SAAA,EAAW;IAAX9B,EAAA,CAAAK,UAAA,YAAAF,MAAA,CAAA8B,MAAA,CAAW;;;;;IAevCjC,EAAA,CAAAe,cAAA,cACmK;;IACjKf,EAAA,CAAAe,cAAA,cAAkF;IAChFf,EAAA,CAAAC,SAAA,eAEO;IACTD,EAAA,CAAAyB,YAAA,EAAM;;IACNzB,EAAA,CAAAe,cAAA,eAAsB;IAAAf,EAAA,CAAA6B,MAAA,+BAAI;IAC5B7B,EAD4B,CAAAyB,YAAA,EAAO,EAC7B;;;AD7DR,OAAM,MAAOmB,sBAAsB;EAPnCC,YAAA;IAQW,KAAAZ,MAAM,GAAwB,EAAE;IAChC,KAAAD,YAAY,GAAW,CAAC;IACxB,KAAAc,cAAc,GAAY,IAAI;IAC9B,KAAAC,WAAW,GAAY,IAAI;IAC3B,KAAAC,cAAc,GAAY,IAAI;IAC9B,KAAAC,WAAW,GAAW,eAAe;IACrC,KAAAC,QAAQ,GAAY,KAAK;IACzB,KAAAC,gBAAgB,GAAW,IAAI;IAC/B,KAAAC,cAAc,GAAY,IAAI;IAC9B,KAAAC,cAAc,GAAW,EAAE;IAC3B,KAAAjD,UAAU,GAAW,EAAE;IAEtB,KAAAkD,UAAU,GAAG,IAAIxD,YAAY,EAAsB;IACnD,KAAAyD,WAAW,GAAG,IAAIzD,YAAY,EAAU;IACxC,KAAA0D,aAAa,GAAG,IAAI1D,YAAY,EAAU;IAC1C,KAAA2D,SAAS,GAAG,IAAI3D,YAAY,EAAU;;EAIhD4D,QAAQA,CAAA;IACN,IAAI,IAAI,CAAC1B,YAAY,IAAI,IAAI,CAACC,MAAM,CAACC,MAAM,EAAE;MAC3C,IAAI,CAACF,YAAY,GAAG,CAAC;IACvB;IACA,IAAI,CAAC2B,aAAa,EAAE;EACtB;EAEAC,WAAWA,CAAA;IACT,IAAI,CAACC,YAAY,EAAE;EACrB;EAGAC,mBAAmBA,CAACC,KAAoB;IACtC,IAAI,CAAC,IAAI,CAACX,cAAc,IAAI,IAAI,CAACnB,MAAM,CAACC,MAAM,IAAI,CAAC,EAAE;IAErD,QAAQ6B,KAAK,CAACC,GAAG;MACf,KAAK,WAAW;QACdD,KAAK,CAACE,cAAc,EAAE;QACtB,IAAI,CAAC3C,YAAY,EAAE;QACnB;MACF,KAAK,YAAY;QACfyC,KAAK,CAACE,cAAc,EAAE;QACtB,IAAI,CAACrC,QAAQ,EAAE;QACf;IACJ;EACF;EAEArB,eAAeA,CAAA;IACb,IAAI,IAAI,CAAC0B,MAAM,CAACC,MAAM,KAAK,CAAC,IAAI,IAAI,CAACF,YAAY,GAAG,CAAC,IAAI,IAAI,CAACA,YAAY,IAAI,IAAI,CAACC,MAAM,CAACC,MAAM,EAAE;MAChG,OAAO,IAAI;IACb;IACA,OAAO,IAAI,CAACD,MAAM,CAAC,IAAI,CAACD,YAAY,CAAC;EACvC;EAEAJ,QAAQA,CAAA;IACN,IAAI,IAAI,CAACK,MAAM,CAACC,MAAM,IAAI,CAAC,EAAE;IAE7B,MAAMgC,QAAQ,GAAG,CAAC,IAAI,CAAClC,YAAY,GAAG,CAAC,IAAI,IAAI,CAACC,MAAM,CAACC,MAAM;IAC7D,IAAI,CAACiC,eAAe,CAACD,QAAQ,CAAC;IAC9B,IAAI,CAACT,SAAS,CAACW,IAAI,CAACF,QAAQ,CAAC;EAC/B;EAEA5C,YAAYA,CAAA;IACV,IAAI,IAAI,CAACW,MAAM,CAACC,MAAM,IAAI,CAAC,EAAE;IAE7B,MAAMgC,QAAQ,GAAG,IAAI,CAAClC,YAAY,KAAK,CAAC,GAAG,IAAI,CAACC,MAAM,CAACC,MAAM,GAAG,CAAC,GAAG,IAAI,CAACF,YAAY,GAAG,CAAC;IACzF,IAAI,CAACmC,eAAe,CAACD,QAAQ,CAAC;IAC9B,IAAI,CAACV,aAAa,CAACY,IAAI,CAACF,QAAQ,CAAC;EACnC;EAEAG,SAASA,CAAC/B,KAAa;IACrB,IAAIA,KAAK,IAAI,CAAC,IAAIA,KAAK,GAAG,IAAI,CAACL,MAAM,CAACC,MAAM,IAAII,KAAK,KAAK,IAAI,CAACN,YAAY,EAAE;MAC3E,IAAI,CAACmC,eAAe,CAAC7B,KAAK,CAAC;IAC7B;EACF;EAEAgC,YAAYA,CAAA;IACV,MAAMC,YAAY,GAAG,IAAI,CAAChE,eAAe,EAAE;IAC3C,IAAIgE,YAAY,EAAE;MAChB,IAAI,CAACjB,UAAU,CAACc,IAAI,CAAC;QACnB9B,KAAK,EAAE,IAAI,CAACN,YAAY;QACxBwC,KAAK,EAAED;OACR,CAAC;IACJ;EACF;EAEAhC,gBAAgBA,CAACD,KAAa;IAC5B,IAAI,CAAC+B,SAAS,CAAC/B,KAAK,CAAC;EACvB;EAEQ6B,eAAeA,CAAC7B,KAAa;IACnC,IAAI,CAACN,YAAY,GAAGM,KAAK;IACzB,IAAI,CAACiB,WAAW,CAACa,IAAI,CAAC9B,KAAK,CAAC;IAC5B,IAAI,CAACmC,eAAe,EAAE;EACxB;EAEQd,aAAaA,CAAA;IACnB,IAAI,CAAC,IAAI,CAACT,QAAQ,IAAI,IAAI,CAACjB,MAAM,CAACC,MAAM,IAAI,CAAC,EAAE;IAE/C,IAAI,CAACwC,aAAa,GAAGC,WAAW,CAAC,MAAK;MACpC,IAAI,CAAC/C,QAAQ,EAAE;IACjB,CAAC,EAAE,IAAI,CAACuB,gBAAgB,CAAC;EAC3B;EAEQU,YAAYA,CAAA;IAClB,IAAI,IAAI,CAACa,aAAa,EAAE;MACtBE,aAAa,CAAC,IAAI,CAACF,aAAa,CAAC;MACjC,IAAI,CAACA,aAAa,GAAG,IAAI;IAC3B;EACF;EAEQD,eAAeA,CAAA;IACrB,IAAI,CAACZ,YAAY,EAAE;IACnB,IAAI,CAACF,aAAa,EAAE;EACtB;EAEA;EACAkB,aAAaA,CAAA;IACX,IAAI,CAAChB,YAAY,EAAE;EACrB;EAEA;EACAiB,cAAcA,CAAA;IACZ,IAAI,CAACnB,aAAa,EAAE;EACtB;;;uCA5HWf,sBAAsB;IAAA;EAAA;;;YAAtBA,sBAAsB;MAAAmC,SAAA;MAAAC,YAAA,WAAAC,oCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAtBlF,EAAA,CAAAgB,UAAA,qBAAAoE,kDAAAlE,MAAA;YAAA,OAAAiE,GAAA,CAAArB,mBAAA,CAAA5C,MAAA,CAA2B;UAAA,UAAAlB,EAAA,CAAAqF,iBAAA,CAAL;;;;;;;;;;;;;;;;;;;;;;;;;;;;;UCrBnCrF,EAAA,CAAAe,cAAA,aAEqC;UAAhCf,EADA,CAAAgB,UAAA,wBAAAsE,0DAAA;YAAA,OAAcH,GAAA,CAAAN,aAAA,EAAe;UAAA,EAAC,wBAAAU,0DAAA;YAAA,OAChBJ,GAAA,CAAAL,cAAA,EAAgB;UAAA,EAAC;UAIhC9E,EADF,CAAAe,cAAA,aAAwD,aAExB;UAAzBf,EAAA,CAAAgB,UAAA,mBAAAwE,qDAAA;YAAA,OAASL,GAAA,CAAAb,YAAA,EAAc;UAAA,EAAC;UAG3BtE,EAAA,CAAA0C,UAAA,IAAA+C,qCAAA,iBAK8C;UAK1CzF,EAFJ,CAAAe,cAAA,aAA2I,aACjD,aACM;;UAC1Ff,EAAA,CAAAe,cAAA,aAAyF;UACvFf,EAAA,CAAAC,SAAA,cACuF;UAI/FD,EAHM,CAAAyB,YAAA,EAAM,EACF,EACF,EACF;UAsBNzB,EAnBA,CAAA0C,UAAA,IAAAgD,wCAAA,oBAGsB,KAAAC,yCAAA,qBASA,KAAAC,sCAAA,kBAQiG;UAI3H5F,EADE,CAAAyB,YAAA,EAAM,EACF;UAoBNzB,EAjBA,CAAA0C,UAAA,KAAAmD,sCAAA,kBACuE,KAAAC,sCAAA,kBAiB4F;UAQrK9F,EAAA,CAAAyB,YAAA,EAAM;;;UAnFgCzB,EAAA,CAAAE,UAAA,CAAAiF,GAAA,CAAA9B,cAAA,CAAwB;UAK1BrD,EAAA,CAAA8B,SAAA,EAAqB;UAArB9B,EAAA,CAAAE,UAAA,CAAAiF,GAAA,CAAAlC,WAAA,CAAqB;UAK7CjD,EAAA,CAAA8B,SAAA,GAAuB;UAAvB9B,EAAA,CAAAK,UAAA,SAAA8E,GAAA,CAAA5E,eAAA,GAAuB;UAoBpBP,EAAA,CAAA8B,SAAA,GAAyC;UAAzC9B,EAAA,CAAAK,UAAA,SAAA8E,GAAA,CAAAnC,cAAA,IAAAmC,GAAA,CAAAlD,MAAA,CAAAC,MAAA,KAAyC;UASzClC,EAAA,CAAA8B,SAAA,EAAyC;UAAzC9B,EAAA,CAAAK,UAAA,SAAA8E,GAAA,CAAAnC,cAAA,IAAAmC,GAAA,CAAAlD,MAAA,CAAAC,MAAA,KAAyC;UAU5ClC,EAAA,CAAA8B,SAAA,EAAsC;UAAtC9B,EAAA,CAAAK,UAAA,SAAA8E,GAAA,CAAApC,WAAA,IAAAoC,GAAA,CAAAlD,MAAA,CAAAC,MAAA,KAAsC;UAQ1ClC,EAAA,CAAA8B,SAAA,EAAyC;UAAzC9B,EAAA,CAAAK,UAAA,SAAA8E,GAAA,CAAArC,cAAA,IAAAqC,GAAA,CAAAlD,MAAA,CAAAC,MAAA,KAAyC;UAiBzClC,EAAA,CAAA8B,SAAA,EAAyB;UAAzB9B,EAAA,CAAAK,UAAA,SAAA8E,GAAA,CAAAlD,MAAA,CAAAC,MAAA,OAAyB;;;qBDzDrBnC,YAAY,EAAAgG,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA;MAAAC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}