{"ast": null, "code": ";\n(function (root, factory, undef) {\n  if (typeof exports === \"object\") {\n    // CommonJS\n    module.exports = exports = factory(require(\"./core\"), require(\"./sha1\"), require(\"./hmac\"));\n  } else if (typeof define === \"function\" && define.amd) {\n    // AMD\n    define([\"./core\", \"./sha1\", \"./hmac\"], factory);\n  } else {\n    // Global (browser)\n    factory(root.CryptoJS);\n  }\n})(this, function (CryptoJS) {\n  (function () {\n    // Shortcuts\n    var C = CryptoJS;\n    var C_lib = C.lib;\n    var Base = C_lib.Base;\n    var WordArray = C_lib.WordArray;\n    var C_algo = C.algo;\n    var MD5 = C_algo.MD5;\n\n    /**\n     * This key derivation function is meant to conform with EVP_BytesToKey.\n     * www.openssl.org/docs/crypto/EVP_BytesToKey.html\n     */\n    var EvpKDF = C_algo.EvpKDF = Base.extend({\n      /**\n       * Configuration options.\n       *\n       * @property {number} keySize The key size in words to generate. Default: 4 (128 bits)\n       * @property {Hasher} hasher The hash algorithm to use. Default: MD5\n       * @property {number} iterations The number of iterations to perform. Default: 1\n       */\n      cfg: Base.extend({\n        keySize: 128 / 32,\n        hasher: MD5,\n        iterations: 1\n      }),\n      /**\n       * Initializes a newly created key derivation function.\n       *\n       * @param {Object} cfg (Optional) The configuration options to use for the derivation.\n       *\n       * @example\n       *\n       *     var kdf = CryptoJS.algo.EvpKDF.create();\n       *     var kdf = CryptoJS.algo.EvpKDF.create({ keySize: 8 });\n       *     var kdf = CryptoJS.algo.EvpKDF.create({ keySize: 8, iterations: 1000 });\n       */\n      init: function (cfg) {\n        this.cfg = this.cfg.extend(cfg);\n      },\n      /**\n       * Derives a key from a password.\n       *\n       * @param {WordArray|string} password The password.\n       * @param {WordArray|string} salt A salt.\n       *\n       * @return {WordArray} The derived key.\n       *\n       * @example\n       *\n       *     var key = kdf.compute(password, salt);\n       */\n      compute: function (password, salt) {\n        var block;\n\n        // Shortcut\n        var cfg = this.cfg;\n\n        // Init hasher\n        var hasher = cfg.hasher.create();\n\n        // Initial values\n        var derivedKey = WordArray.create();\n\n        // Shortcuts\n        var derivedKeyWords = derivedKey.words;\n        var keySize = cfg.keySize;\n        var iterations = cfg.iterations;\n\n        // Generate key\n        while (derivedKeyWords.length < keySize) {\n          if (block) {\n            hasher.update(block);\n          }\n          block = hasher.update(password).finalize(salt);\n          hasher.reset();\n\n          // Iterations\n          for (var i = 1; i < iterations; i++) {\n            block = hasher.finalize(block);\n            hasher.reset();\n          }\n          derivedKey.concat(block);\n        }\n        derivedKey.sigBytes = keySize * 4;\n        return derivedKey;\n      }\n    });\n\n    /**\n     * Derives a key from a password.\n     *\n     * @param {WordArray|string} password The password.\n     * @param {WordArray|string} salt A salt.\n     * @param {Object} cfg (Optional) The configuration options to use for this computation.\n     *\n     * @return {WordArray} The derived key.\n     *\n     * @static\n     *\n     * @example\n     *\n     *     var key = CryptoJS.EvpKDF(password, salt);\n     *     var key = CryptoJS.EvpKDF(password, salt, { keySize: 8 });\n     *     var key = CryptoJS.EvpKDF(password, salt, { keySize: 8, iterations: 1000 });\n     */\n    C.EvpKDF = function (password, salt, cfg) {\n      return EvpKDF.create(cfg).compute(password, salt);\n    };\n  })();\n  return CryptoJS.EvpKDF;\n});", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}