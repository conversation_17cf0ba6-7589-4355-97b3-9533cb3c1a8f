{"ast": null, "code": "import { BaseComponent } from '../../components/base/baseComponent';\nimport { EEvent } from 'src/app/shared/services/event.service';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/shared/helper/allowHelper\";\nimport * as i2 from \"@nebular/theme\";\nimport * as i3 from \"src/app/shared/helper/validationHelper\";\nimport * as i4 from \"src/services/api/services\";\nimport * as i5 from \"src/app/shared/services/message.service\";\nimport * as i6 from \"@angular/router\";\nimport * as i7 from \"@angular/common\";\nimport * as i8 from \"src/app/shared/services/event.service\";\nimport * as i9 from \"@angular/forms\";\nimport * as i10 from \"@ng-bootstrap/ng-bootstrap\";\nimport * as i11 from \"../../components/breadcrumb/breadcrumb.component\";\nimport * as i12 from \"../../../@theme/directives/label.directive\";\nimport * as i13 from \"../../../@theme/pipes/date-format.pipe\";\nimport * as i14 from \"../../../@theme/pipes/mapping.pipe\";\nfunction SampleSelectionResultComponent_tr_26_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\", 15)(1, \"td\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\");\n    i0.ɵɵtext(6);\n    i0.ɵɵpipe(7, \"dateFormat\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"td\");\n    i0.ɵɵtext(9);\n    i0.ɵɵpipe(10, \"getDocumentStatus\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"td\", 16)(12, \"button\", 17);\n    i0.ɵɵlistener(\"click\", function SampleSelectionResultComponent_tr_26_Template_button_click_12_listener() {\n      const item_r4 = i0.ɵɵrestoreView(_r3).$implicit;\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.openPdfInNewTab(item_r4));\n    });\n    i0.ɵɵtext(13, \"\\u6AA2\\u8996\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const item_r4 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r4.CIsChange === true ? \"\\u5BA2\\u8B8A\" : item_r4.CIsChange === false ? \"\\u9078\\u6A23\" : \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r4.CDocumentName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r4.CSignDate ? i0.ɵɵpipeBind1(7, 5, item_r4.CSignDate) : \"\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(10, 7, item_r4.CDocumentStatus));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"disabled\", !item_r4.CFile);\n  }\n}\nfunction SampleSelectionResultComponent_ng_template_33_tr_33_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\")(2, \"nb-checkbox\", 36);\n    i0.ɵɵtwoWayListener(\"checkedChange\", function SampleSelectionResultComponent_ng_template_33_tr_33_Template_nb_checkbox_checkedChange_2_listener($event) {\n      const drawing_r8 = i0.ɵɵrestoreView(_r7).$implicit;\n      i0.ɵɵtwoWayBindingSet(drawing_r8.isChecked, $event) || (drawing_r8.isChecked = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(3, \"td\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"td\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"td\");\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const drawing_r8 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtwoWayProperty(\"checked\", drawing_r8.isChecked);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(drawing_r8.CChangeDate);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(drawing_r8.CDrawingName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(drawing_r8.CCreateDT);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(drawing_r8.CApproveDate);\n  }\n}\nfunction SampleSelectionResultComponent_ng_template_33_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-card\", 18)(1, \"nb-card-header\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"nb-card-body\", 19)(4, \"h4\");\n    i0.ɵɵtext(5, \" \\u8ACB\\u78BA\\u8A8D\\u8981\\u5C07\\u54EA\\u4E9B\\u5716\\u9762\\u6574\\u5408\\u70BA\\u4E00\\u4EFD\\u6587\\u4EF6\\u4F9B\\u5BA2\\u6236\\u7C3D\\u540D\\u78BA\\u8A8D\\u3002 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 20)(7, \"label\", 21);\n    i0.ɵɵtext(8, \" \\u6587\\u4EF6\\u540D\\u7A31 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"input\", 22);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SampleSelectionResultComponent_ng_template_33_Template_input_ngModelChange_9_listener($event) {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r4 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r4.finalDoc.CDocumentName, $event) || (ctx_r4.finalDoc.CDocumentName = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"div\", 20)(11, \"label\", 23);\n    i0.ɵɵtext(12, \" \\u9078\\u6A23\\u7D50\\u679C \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"nb-checkbox\", 24);\n    i0.ɵɵtwoWayListener(\"checkedChange\", function SampleSelectionResultComponent_ng_template_33_Template_nb_checkbox_checkedChange_13_listener($event) {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r4 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r4.isChecked, $event) || (ctx_r4.isChecked = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtext(14, \"\\u9078\\u6A23\\u7D50\\u679C \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(15, \"div\", 20)(16, \"label\", 25);\n    i0.ɵɵtext(17, \" \\u5BA2\\u8B8A\\u5716 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"h4\");\n    i0.ɵɵtext(19, \"\\u50C5\\u80FD\\u52FE\\u9078\\u5DF2\\u901A\\u904E\\u5BE9\\u6838\\u4E4B\\u5716\\u9762\\u3002\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(20, \"table\", 26)(21, \"thead\")(22, \"tr\", 27);\n    i0.ɵɵelement(23, \"th\");\n    i0.ɵɵelementStart(24, \"th\");\n    i0.ɵɵtext(25, \"\\u8A0E\\u8AD6\\u65E5\\u671F\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"th\");\n    i0.ɵɵtext(27, \"\\u5716\\u9762\\u540D\\u7A31\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(28, \"th\");\n    i0.ɵɵtext(29, \"\\u4E0A\\u50B3\\u65E5\\u671F\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(30, \"th\");\n    i0.ɵɵtext(31, \"\\u901A\\u904E\\u65E5\\u671F\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(32, \"tbody\");\n    i0.ɵɵtemplate(33, SampleSelectionResultComponent_ng_template_33_tr_33_Template, 11, 5, \"tr\", 28);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(34, \"div\", 29)(35, \"label\", 30);\n    i0.ɵɵtext(36, \"\\u9001\\u5BE9\\u8CC7\\u8A0A\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(37, \"textarea\", 31);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SampleSelectionResultComponent_ng_template_33_Template_textarea_ngModelChange_37_listener($event) {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r4 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r4.finalDoc.CApproveRemark, $event) || (ctx_r4.finalDoc.CApproveRemark = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtext(38, \"        \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(39, \"div\", 29)(40, \"label\", 32);\n    i0.ɵɵtext(41, \"\\u7CFB\\u7D71\\u64CD\\u4F5C\\u8AAA\\u660E\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(42, \"textarea\", 33);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SampleSelectionResultComponent_ng_template_33_Template_textarea_ngModelChange_42_listener($event) {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r4 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r4.finalDoc.CNote, $event) || (ctx_r4.finalDoc.CNote = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtext(43, \"        \");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(44, \"nb-card-footer\", 12)(45, \"button\", 34);\n    i0.ɵɵlistener(\"click\", function SampleSelectionResultComponent_ng_template_33_Template_button_click_45_listener() {\n      const ref_r9 = i0.ɵɵrestoreView(_r6).dialogRef;\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.onClose(ref_r9));\n    });\n    i0.ɵɵtext(46, \"\\u53D6\\u6D88\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(47, \"button\", 35);\n    i0.ɵɵlistener(\"click\", function SampleSelectionResultComponent_ng_template_33_Template_button_click_47_listener() {\n      const ref_r9 = i0.ɵɵrestoreView(_r6).dialogRef;\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.onCreateFinalDoc(ref_r9));\n    });\n    i0.ɵɵtext(48, \"\\u78BA\\u8A8D\\u9001\\u51FA\\u5BE9\\u6838\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\" \\u6236\\u5225\\u7BA1\\u7406 > \\u9078\\u6A23\\u53CA\\u5BA2\\u8B8A\\u7D50\\u679C > \", ctx_r4.houseByID.CHousehold, \" \", ctx_r4.houseByID.CFloor, \"F \");\n    i0.ɵɵadvance(7);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r4.finalDoc.CDocumentName);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"checked\", ctx_r4.isChecked);\n    i0.ɵɵadvance(20);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r4.listSpecialChangeAvailable);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r4.finalDoc.CApproveRemark);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r4.finalDoc.CNote);\n  }\n}\nexport class SampleSelectionResultComponent extends BaseComponent {\n  constructor(_allow, dialogService, valid, _finalDocumentService, message, route, location, _eventService, _houseService) {\n    super(_allow);\n    this._allow = _allow;\n    this.dialogService = dialogService;\n    this.valid = valid;\n    this._finalDocumentService = _finalDocumentService;\n    this.message = message;\n    this.route = route;\n    this.location = location;\n    this._eventService = _eventService;\n    this._houseService = _houseService;\n    this.documentStatusOptions = ['待審核', '已駁回', '待客戶簽回', '客戶已簽回'];\n    this.isChecked = true;\n  }\n  ngOnInit() {\n    this.route.paramMap.subscribe(params => {\n      if (params) {\n        this.buildCaseId = +(params.get('id1') ?? 0);\n        this.houseID = +(params.get('id2') ?? 0);\n        if (this.houseID) {\n          this.getHouseById();\n        }\n        this.getListFinalDoc();\n      }\n    });\n  }\n  getHouseById() {\n    this._houseService.apiHouseGetHouseByIdPost$Json({\n      body: {\n        CHouseID: this.houseID\n      }\n    }).subscribe(res => {\n      if (res.Entries && res.StatusCode == 0) {\n        this.houseByID = res.Entries;\n      }\n    });\n  }\n  openPdfInNewTab(data) {\n    if (data && data.CFile) window.open(data.CFile, '_blank');\n  }\n  getListFinalDoc() {\n    this._finalDocumentService.apiFinalDocumentGetListFinalDocPost$Json({\n      body: {\n        CHouseID: this.houseID,\n        PageIndex: this.pageIndex,\n        PageSize: this.pageSize\n      }\n    }).subscribe(res => {\n      if (res.TotalItems && res.Entries && res.StatusCode == 0) {\n        this.listFinalDoc = res.Entries ?? [];\n        this.totalRecords = res.TotalItems;\n      }\n    });\n  }\n  getListSpecialChangeAvailable() {\n    this._finalDocumentService.apiFinalDocumentGetListSpecialChangeAvailablePost$Json({\n      body: {\n        CHouseID: this.houseID\n      }\n    }).subscribe(res => {\n      this.listSpecialChangeAvailable = [];\n      if (res.TotalItems && res.Entries && res.StatusCode == 0) {\n        this.listSpecialChangeAvailable = res.Entries ?? [];\n        if (res.Entries.length) {\n          this.listSpecialChangeAvailable = res.Entries.map(e => {\n            return {\n              ...e,\n              isChecked: false\n            };\n          });\n        }\n      }\n    });\n  }\n  validation() {\n    this.valid.clear();\n    this.valid.required('[文件名稱]', this.finalDoc.CDocumentName);\n    this.valid.required('[送審資訊]', this.finalDoc.CApproveRemark);\n    this.valid.required('[系統操作說明]', this.finalDoc.CNote);\n  }\n  goBack() {\n    this._eventService.push({\n      action: \"GET_BUILDCASE\" /* EEvent.GET_BUILDCASE */,\n      payload: this.buildCaseId\n    });\n    this.location.back();\n  }\n  getCheckedCIDs(changeArray) {\n    if (changeArray && changeArray.length) {\n      return changeArray.filter(change => change.isChecked).map(change => change.CID);\n    }\n    return [];\n  }\n  onCreateFinalDoc(ref) {\n    this.validation();\n    if (this.valid.errorMessages.length > 0) {\n      this.message.showErrorMSGs(this.valid.errorMessages);\n      return;\n    }\n    const param = {\n      ...this.finalDoc,\n      CSpecialChange: this.getCheckedCIDs(this.listSpecialChangeAvailable)\n    };\n    this._finalDocumentService.apiFinalDocumentCreateFinalDocPost$Json({\n      body: param\n    }).subscribe(res => {\n      if (res.StatusCode === 0) {\n        this.getListFinalDoc();\n        this.message.showSucessMSG(\"執行成功\");\n        ref.close();\n      } else {\n        this.message.showErrorMSG(res.Message);\n      }\n    });\n  }\n  pageChanged(newPage) {\n    this.pageIndex = newPage;\n    this.getListFinalDoc();\n  }\n  addNew(ref) {\n    this.finalDoc = {\n      CHouseID: this.houseID,\n      CDocumentName: '',\n      CApproveRemark: '',\n      CNote: \"\"\n    };\n    this.getListSpecialChangeAvailable();\n    this.dialogService.open(ref);\n  }\n  onOpenModel(ref) {\n    this.dialogService.open(ref);\n  }\n  onClose(ref) {\n    ref.close();\n  }\n  static {\n    this.ɵfac = function SampleSelectionResultComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || SampleSelectionResultComponent)(i0.ɵɵdirectiveInject(i1.AllowHelper), i0.ɵɵdirectiveInject(i2.NbDialogService), i0.ɵɵdirectiveInject(i3.ValidationHelper), i0.ɵɵdirectiveInject(i4.FinalDocumentService), i0.ɵɵdirectiveInject(i5.MessageService), i0.ɵɵdirectiveInject(i6.ActivatedRoute), i0.ɵɵdirectiveInject(i7.Location), i0.ɵɵdirectiveInject(i8.EventService), i0.ɵɵdirectiveInject(i4.HouseService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: SampleSelectionResultComponent,\n      selectors: [[\"ngx-sample-selection-result\"]],\n      features: [i0.ɵɵInheritDefinitionFeature],\n      decls: 35,\n      vars: 4,\n      consts: [[\"dialogConfirmImage\", \"\"], [\"accent\", \"success\"], [1, \"font-bold\", \"text-[#818181]\"], [1, \"d-flex\", \"flex-wrap\"], [1, \"col-md-12\"], [1, \"d-flex\", \"justify-content-end\", \"w-full\"], [1, \"btn\", \"btn-info\", 3, \"click\"], [1, \"table-responsive\", \"mt-4\"], [1, \"table\", \"table-striped\", \"border\", 2, \"min-width\", \"1000px\", \"background-color\", \"#f3f3f3\"], [1, \"text-center\", 2, \"background-color\", \"#27ae60\", \"color\", \"white\"], [\"scope\", \"col\", 1, \"col-1\"], [\"class\", \"text-center\", 4, \"ngFor\", \"ngForOf\"], [1, \"d-flex\", \"justify-content-center\"], [\"aria-label\", \"Pagination\", 3, \"pageChange\", \"page\", \"pageSize\", \"collectionSize\"], [1, \"btn\", \"btn-secondary\", \"btn-sm\", 3, \"click\"], [1, \"text-center\"], [1, \"text-center\", \"w-32\"], [1, \"btn\", \"btn-outline-primary\", \"btn-sm\", \"m-1\", 3, \"click\", \"disabled\"], [2, \"width\", \"1000px\", \"max-height\", \"95vh\"], [1, \"px-4\"], [1, \"form-group\"], [\"for\", \"CDocumentName\", \"baseLabel\", \"\", 1, \"required-field\", \"mr-4\", 2, \"min-width\", \"75px\"], [\"type\", \"text\", \"nbInput\", \"\", \"placeholder\", \"\\u6587\\u4EF6\\u540D\\u7A31\", 1, \"w-full\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"isChecked\", \"baseLabel\", \"\", 1, \"required-field\", \"mr-4\", 2, \"min-width\", \"75px\"], [\"status\", \"basic\", \"disabled\", \"\", 3, \"checkedChange\", \"checked\"], [\"for\", \"\\u5BA2\\u8B8A\\u5716\", \"baseLabel\", \"\", 1, \"mr-4\", 2, \"min-width\", \"75px\"], [1, \"table\", \"border\", \"table-striped\", 2, \"min-width\", \"600px\"], [2, \"background-color\", \"#27ae60\", \"color\", \"white\"], [4, \"ngFor\", \"ngForOf\"], [1, \"form-group\", \"d-flex\", \"align-items-center\"], [\"for\", \"remark\", \"baseLabel\", \"\", 1, \"required-field\", \"align-self-start\", 2, \"min-width\", \"75px\"], [\"name\", \"remark\", \"id\", \"remark\", \"rows\", \"5\", \"nbInput\", \"\", 1, \"w-full\", 2, \"resize\", \"none\", \"max-width\", \"none\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"CNote\", \"baseLabel\", \"\", 1, \"required-field\", \"align-self-start\", 2, \"min-width\", \"75px\"], [\"name\", \"CNote\", \"id\", \"CNote\", \"rows\", \"5\", \"nbInput\", \"\", 1, \"w-full\", 2, \"resize\", \"none\", \"max-width\", \"none\", 3, \"ngModelChange\", \"ngModel\"], [1, \"btn\", \"btn-outline-secondary\", \"m-2\", 3, \"click\"], [1, \"btn\", \"btn-success\", \"m-2\", 3, \"click\"], [\"status\", \"basic\", 3, \"checkedChange\", \"checked\"]],\n      template: function SampleSelectionResultComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"nb-card\", 1)(1, \"nb-card-header\");\n          i0.ɵɵelement(2, \"ngx-breadcrumb\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(3, \"nb-card-body\")(4, \"h1\", 2);\n          i0.ɵɵtext(5, \"\\u60A8\\u53EF\\u8207\\u6B64\\u6AA2\\u8996\\u8A72\\u6236\\u5225\\u5BA2\\u6236\\u5C0D\\u65BC\\u9078\\u6A23\\u7D50\\u679C\\u4E4B\\u7C3D\\u8A8D\\u6587\\u4EF6\\uFF0C\\u4E26\\u53EF\\u9078\\u64C7\\u8981\\u5C07\\u54EA\\u4E9B\\u5BA2\\u8B8A\\u5716\\u9762\\u6574\\u5408\\u70BA\\u4E00\\u4EFD\\u5716\\u9762\\u8ACB\\u5BA2\\u6236\\u7C3D\\u56DE\\u78BA\\u8A8D\\u3002 \\u5982\\u679C\\u8A72\\u4F4D\\u5BA2\\u6236\\u6709\\u591A\\u4EFD\\u7C3D\\u56DE\\u6A94\\u6848\\uFF0C\\u65BC\\u5BA2\\u6236\\u7AEF\\u50C5\\u6703\\u986F\\u793A\\u6700\\u65B0\\u7684\\u4E00\\u4EFD\\u6587\\u4EF6\\u3002\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(6, \"div\", 3)(7, \"div\", 4)(8, \"div\", 5)(9, \"button\", 6);\n          i0.ɵɵlistener(\"click\", function SampleSelectionResultComponent_Template_button_click_9_listener() {\n            i0.ɵɵrestoreView(_r1);\n            const dialogConfirmImage_r2 = i0.ɵɵreference(34);\n            return i0.ɵɵresetView(ctx.addNew(dialogConfirmImage_r2));\n          });\n          i0.ɵɵtext(10, \" \\u65B0\\u589E\\u78BA\\u8A8D\\u5BA2\\u8B8A\\u5716\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(11, \"div\", 7)(12, \"table\", 8)(13, \"thead\")(14, \"tr\", 9)(15, \"th\", 10);\n          i0.ɵɵtext(16, \"\\u985E\\u578B\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(17, \"th\", 10);\n          i0.ɵɵtext(18, \"\\u6587\\u4EF6\\u540D\\u7A31\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(19, \"th\", 10);\n          i0.ɵɵtext(20, \"\\u7C3D\\u56DE\\u65E5\\u671F \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(21, \"th\", 10);\n          i0.ɵɵtext(22, \"\\u72C0\\u614B\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(23, \"th\", 10);\n          i0.ɵɵtext(24, \"\\u64CD\\u4F5C\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(25, \"tbody\");\n          i0.ɵɵtemplate(26, SampleSelectionResultComponent_tr_26_Template, 14, 9, \"tr\", 11);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(27, \"nb-card-footer\", 12)(28, \"ngb-pagination\", 13);\n          i0.ɵɵtwoWayListener(\"pageChange\", function SampleSelectionResultComponent_Template_ngb_pagination_pageChange_28_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.pageIndex, $event) || (ctx.pageIndex = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵlistener(\"pageChange\", function SampleSelectionResultComponent_Template_ngb_pagination_pageChange_28_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.pageChanged($event));\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(29, \"nb-card-footer\")(30, \"div\", 12)(31, \"button\", 14);\n          i0.ɵɵlistener(\"click\", function SampleSelectionResultComponent_Template_button_click_31_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.goBack());\n          });\n          i0.ɵɵtext(32, \" \\u8FD4\\u56DE\\u4E0A\\u4E00\\u9801 \");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵtemplate(33, SampleSelectionResultComponent_ng_template_33_Template, 49, 7, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor);\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(26);\n          i0.ɵɵproperty(\"ngForOf\", ctx.listFinalDoc);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtwoWayProperty(\"page\", ctx.pageIndex);\n          i0.ɵɵproperty(\"pageSize\", ctx.pageSize)(\"collectionSize\", ctx.totalRecords);\n        }\n      },\n      dependencies: [i7.NgForOf, i9.DefaultValueAccessor, i9.NgControlStatus, i9.NgModel, i2.NbCardComponent, i2.NbCardBodyComponent, i2.NbCardFooterComponent, i2.NbCardHeaderComponent, i2.NbCheckboxComponent, i2.NbInputDirective, i10.NgbPagination, i11.BreadcrumbComponent, i12.BaseLabelDirective, i13.DateFormatPipe, i14.DocumentStatusPipe],\n      styles: [\"/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsImZpbGUiOiJzYW1wbGUtc2VsZWN0aW9uLXJlc3VsdC5jb21wb25lbnQuc2NzcyJ9 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvcGFnZXMvaG91c2Vob2xkLW1hbmFnZW1lbnQvc2FtcGxlLXNlbGVjdGlvbi1yZXN1bHQvc2FtcGxlLXNlbGVjdGlvbi1yZXN1bHQuY29tcG9uZW50LnNjc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IjtBQUNBLHdMQUF3TCIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["BaseComponent", "EEvent", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵlistener", "SampleSelectionResultComponent_tr_26_Template_button_click_12_listener", "item_r4", "ɵɵrestoreView", "_r3", "$implicit", "ctx_r4", "ɵɵnextContext", "ɵɵresetView", "openPdfInNewTab", "ɵɵadvance", "ɵɵtextInterpolate", "CIsChange", "CDocumentName", "CSignDate", "ɵɵpipeBind1", "CDocumentStatus", "ɵɵproperty", "CFile", "ɵɵtwoWayListener", "SampleSelectionResultComponent_ng_template_33_tr_33_Template_nb_checkbox_checkedChange_2_listener", "$event", "drawing_r8", "_r7", "ɵɵtwoWayBindingSet", "isChecked", "ɵɵtwoWayProperty", "CChangeDate", "CDrawingName", "CCreateDT", "CApproveDate", "SampleSelectionResultComponent_ng_template_33_Template_input_ngModelChange_9_listener", "_r6", "finalDoc", "SampleSelectionResultComponent_ng_template_33_Template_nb_checkbox_checkedChange_13_listener", "ɵɵelement", "ɵɵtemplate", "SampleSelectionResultComponent_ng_template_33_tr_33_Template", "SampleSelectionResultComponent_ng_template_33_Template_textarea_ngModelChange_37_listener", "CApproveRemark", "SampleSelectionResultComponent_ng_template_33_Template_textarea_ngModelChange_42_listener", "CNote", "SampleSelectionResultComponent_ng_template_33_Template_button_click_45_listener", "ref_r9", "dialogRef", "onClose", "SampleSelectionResultComponent_ng_template_33_Template_button_click_47_listener", "onCreateFinalDoc", "ɵɵtextInterpolate2", "houseByID", "CHousehold", "CFloor", "listSpecialChangeAvailable", "SampleSelectionResultComponent", "constructor", "_allow", "dialogService", "valid", "_finalDocumentService", "message", "route", "location", "_eventService", "_houseService", "documentStatusOptions", "ngOnInit", "paramMap", "subscribe", "params", "buildCaseId", "get", "houseID", "getHouseById", "getListFinalDoc", "apiHouseGetHouseByIdPost$Json", "body", "CHouseID", "res", "Entries", "StatusCode", "data", "window", "open", "apiFinalDocumentGetListFinalDocPost$Json", "PageIndex", "pageIndex", "PageSize", "pageSize", "TotalItems", "listFinalDoc", "totalRecords", "getListSpecialChangeAvailable", "apiFinalDocumentGetListSpecialChangeAvailablePost$Json", "length", "map", "e", "validation", "clear", "required", "goBack", "push", "action", "payload", "back", "getCheckedCIDs", "changeArray", "filter", "change", "CID", "ref", "errorMessages", "showErrorMSGs", "param", "CSpecialChange", "apiFinalDocumentCreateFinalDocPost$Json", "showSucessMSG", "close", "showErrorMSG", "Message", "pageChanged", "newPage", "addNew", "onOpenModel", "ɵɵdirectiveInject", "i1", "AllowHelper", "i2", "NbDialogService", "i3", "ValidationHelper", "i4", "FinalDocumentService", "i5", "MessageService", "i6", "ActivatedRoute", "i7", "Location", "i8", "EventService", "HouseService", "selectors", "features", "ɵɵInheritDefinitionFeature", "decls", "vars", "consts", "template", "SampleSelectionResultComponent_Template", "rf", "ctx", "SampleSelectionResultComponent_Template_button_click_9_listener", "_r1", "dialogConfirmImage_r2", "ɵɵreference", "SampleSelectionResultComponent_tr_26_Template", "SampleSelectionResultComponent_Template_ngb_pagination_pageChange_28_listener", "SampleSelectionResultComponent_Template_button_click_31_listener", "SampleSelectionResultComponent_ng_template_33_Template", "ɵɵtemplateRefExtractor"], "sources": ["C:\\Users\\<USER>\\Documents\\jeansalechange\\adminFront\\src\\app\\pages\\household-management\\sample-selection-result\\sample-selection-result.component.ts", "C:\\Users\\<USER>\\Documents\\jeansalechange\\adminFront\\src\\app\\pages\\household-management\\sample-selection-result\\sample-selection-result.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { NbDialogService } from '@nebular/theme';\r\nimport { ValidationHelper } from 'src/app/shared/helper/validationHelper';\r\nimport { FinalDocumentService, HouseService } from 'src/services/api/services';\r\nimport { CreateFinalDocArgs, GetListFinalDocRes, TblHouse } from 'src/services/api/models';\r\nimport { AllowHelper } from 'src/app/shared/helper/allowHelper';\r\nimport { ActivatedRoute } from '@angular/router';\r\nimport { MessageService } from 'src/app/shared/services/message.service';\r\nimport { BaseComponent } from '../../components/base/baseComponent';\r\nimport { Location } from '@angular/common';\r\nimport { EventService, EEvent } from 'src/app/shared/services/event.service';\r\n\r\n@Component({\r\n  selector: 'ngx-sample-selection-result',\r\n  templateUrl: './sample-selection-result.component.html',\r\n  styleUrls: ['./sample-selection-result.component.scss'],\r\n})\r\n\r\nexport class SampleSelectionResultComponent extends BaseComponent implements OnInit {\r\n  constructor(\r\n    private _allow: AllowHelper,\r\n    private dialogService: NbDialogService,\r\n    private valid: ValidationHelper,\r\n    private _finalDocumentService: FinalDocumentService,\r\n    private message: MessageService,\r\n    private route: ActivatedRoute,\r\n    private location: Location,\r\n    private _eventService: EventService,\r\n    private _houseService: HouseService\r\n  ) {\r\n    super(_allow)\r\n  }\r\n\r\n  houseID: any\r\n  buildCaseId: number\r\n\r\n  override ngOnInit(): void {\r\n    this.route.paramMap.subscribe(params => {\r\n      if (params) {\r\n        this.buildCaseId = +(params.get('id1') ?? 0);\r\n        this.houseID = +(params.get('id2') ?? 0);\r\n        if(this.houseID) {\r\n          this.getHouseById()\r\n        }\r\n        this.getListFinalDoc()\r\n      }\r\n    });\r\n  }\r\n\r\n  houseByID: TblHouse\r\n\r\n  getHouseById() {\r\n    this._houseService.apiHouseGetHouseByIdPost$Json({\r\n      body: { CHouseID: this.houseID }\r\n    }).subscribe(res => {\r\n      if (res.Entries && res.StatusCode == 0) {\r\n        this.houseByID = res.Entries\r\n      }\r\n    })\r\n  }\r\n\r\n  documentStatusOptions = ['待審核', '已駁回', '待客戶簽回', '客戶已簽回']\r\n\r\n  openPdfInNewTab(data?: any) {\r\n    if (data && data.CFile) window.open(data.CFile, '_blank');\r\n  }\r\n\r\n\r\n  listFinalDoc: GetListFinalDocRes[]\r\n\r\n\r\n  getListFinalDoc() {\r\n    this._finalDocumentService.apiFinalDocumentGetListFinalDocPost$Json({\r\n      body: {\r\n        CHouseID: this.houseID,\r\n        PageIndex: this.pageIndex,\r\n        PageSize: this.pageSize,\r\n      }\r\n    }).subscribe(res => {\r\n      if (res.TotalItems && res.Entries && res.StatusCode == 0) {\r\n        this.listFinalDoc = res.Entries! ?? []\r\n        this.totalRecords = res.TotalItems;\r\n      }\r\n    })\r\n  }\r\n\r\n\r\n  listSpecialChangeAvailable: any[]\r\n  getListSpecialChangeAvailable() {\r\n    this._finalDocumentService.apiFinalDocumentGetListSpecialChangeAvailablePost$Json({\r\n      body: {\r\n        CHouseID: this.houseID,\r\n      }\r\n    }).subscribe(res => {\r\n      this.listSpecialChangeAvailable = []\r\n      if (res.TotalItems && res.Entries && res.StatusCode == 0) {\r\n        this.listSpecialChangeAvailable = res.Entries! ?? []\r\n        if (res.Entries.length) {\r\n          this.listSpecialChangeAvailable = res.Entries.map((e: any) => {\r\n            return { ...e, isChecked: false }\r\n          })\r\n        }\r\n      }\r\n    })\r\n  }\r\n  finalDoc: CreateFinalDocArgs\r\n\r\n  validation() {\r\n    this.valid.clear();\r\n    this.valid.required('[文件名稱]', this.finalDoc.CDocumentName)\r\n    this.valid.required('[送審資訊]', this.finalDoc.CApproveRemark)\r\n    this.valid.required('[系統操作說明]', this.finalDoc.CNote)\r\n  }\r\n\r\n  goBack() {\r\n    this._eventService.push({\r\n      action: EEvent.GET_BUILDCASE,\r\n      payload: this.buildCaseId\r\n    })\r\n    this.location.back()\r\n  }\r\n\r\n  getCheckedCIDs(changeArray: any[]) {\r\n    if (changeArray && changeArray.length) {\r\n      return changeArray.filter(change => change.isChecked).map(change => change.CID);\r\n    } return []\r\n  }\r\n\r\n  onCreateFinalDoc(ref: any) {\r\n    this.validation()\r\n    if (this.valid.errorMessages.length > 0) {\r\n      this.message.showErrorMSGs(this.valid.errorMessages);\r\n      return\r\n    }\r\n    const param = {\r\n      ...this.finalDoc,\r\n      CSpecialChange: this.getCheckedCIDs(this.listSpecialChangeAvailable)\r\n    }\r\n    this._finalDocumentService.apiFinalDocumentCreateFinalDocPost$Json({\r\n      body: param\r\n    }).subscribe(res => {\r\n      if (res.StatusCode === 0) {\r\n        this.getListFinalDoc();\r\n        this.message.showSucessMSG(\"執行成功\");\r\n        ref.close();\r\n      } else {\r\n        this.message.showErrorMSG(res.Message!)\r\n      }\r\n    })\r\n  }\r\n\r\n  isChecked = true\r\n  pageChanged(newPage: number) {\r\n    this.pageIndex = newPage;\r\n    this.getListFinalDoc();\r\n  }\r\n\r\n\r\n  addNew(ref: any) {\r\n    this.finalDoc = {\r\n      CHouseID: this.houseID,\r\n      CDocumentName: '',\r\n      CApproveRemark: '',\r\n      CNote:\"\"\r\n    }\r\n    this.getListSpecialChangeAvailable()\r\n    this.dialogService.open(ref)\r\n  }\r\n\r\n  onOpenModel(ref: any) {\r\n    this.dialogService.open(ref)\r\n  }\r\n\r\n  onClose(ref: any) {\r\n    ref.close();\r\n  }\r\n}\r\n", "<nb-card accent=\"success\">\r\n  <nb-card-header>\r\n    <ngx-breadcrumb></ngx-breadcrumb>\r\n  </nb-card-header>\r\n  <nb-card-body>\r\n    <h1 class=\"font-bold text-[#818181]\">您可與此檢視該戶別客戶對於選樣結果之簽認文件，並可選擇要將哪些客變圖面整合為一份圖面請客戶簽回確認。\r\n\r\n      如果該位客戶有多份簽回檔案，於客戶端僅會顯示最新的一份文件。</h1>\r\n\r\n    <div class=\"d-flex flex-wrap\">\r\n\r\n      <div class=\"col-md-12\">\r\n        <div class=\"d-flex justify-content-end w-full\">\r\n          <button class=\"btn btn-info\" (click)=\"addNew(dialogConfirmImage)\">\r\n            新增確認客變圖</button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <div class=\"table-responsive mt-4\">\r\n      <table class=\"table table-striped border \" style=\"min-width: 1000px; background-color:#f3f3f3;\">\r\n        <thead>\r\n          <tr style=\"background-color: #27ae60; color: white;\" class=\"text-center\">\r\n            <th scope=\"col\" class=\"col-1\">類型</th>\r\n            <th scope=\"col\" class=\"col-1\">文件名稱</th>\r\n            <th scope=\"col\" class=\"col-1\">簽回日期 </th>\r\n            <th scope=\"col\" class=\"col-1\">狀態</th>\r\n            <th scope=\"col\" class=\"col-1\">操作</th>\r\n          </tr>\r\n        </thead>\r\n        <tbody>\r\n          <tr *ngFor=\"let item of listFinalDoc ; let i = index\" class=\"text-center\">\r\n            <td>{{ item.CIsChange === true ? '客變' : (item.CIsChange === false ? '選樣' :'') }}</td>\r\n            <td>{{ item.CDocumentName}}</td>\r\n            <td>{{ item.CSignDate ? (item.CSignDate | dateFormat) : ''}}</td>\r\n            <td>{{ item.CDocumentStatus! | getDocumentStatus }}</td>\r\n            <td class=\"text-center w-32\">\r\n              <button class=\"btn btn-outline-primary btn-sm m-1\" [disabled]=\"!item.CFile\" \r\n              (click)=\"openPdfInNewTab(item)\">檢視</button>\r\n            </td>\r\n          </tr>\r\n        </tbody>\r\n      </table>\r\n    </div>\r\n  </nb-card-body>\r\n  <nb-card-footer class=\"d-flex justify-content-center\">\r\n    <ngb-pagination [(page)]=\"pageIndex\" [pageSize]=\"pageSize\" [collectionSize]=\"totalRecords\"\r\n      (pageChange)=\"pageChanged($event)\" aria-label=\"Pagination\">\r\n    </ngb-pagination>\r\n  </nb-card-footer>\r\n  <nb-card-footer>\r\n    <div class=\"d-flex justify-content-center\">\r\n      <button class=\"btn btn-secondary btn-sm\"  (click)=\"goBack()\">\r\n        返回上一頁\r\n      </button>\r\n    </div>\r\n  </nb-card-footer>\r\n</nb-card>\r\n\r\n\r\n<ng-template #dialogConfirmImage let-dialog let-ref=\"dialogRef\">\r\n  <nb-card style=\"width:1000px; max-height: 95vh\">\r\n    <nb-card-header>\r\n      戶別管理 > 選樣及客變結果 > {{houseByID.CHousehold}} {{houseByID.CFloor}}F\r\n    </nb-card-header>\r\n    <nb-card-body class=\"px-4\">\r\n      <h4>\r\n        請確認要將哪些圖面整合為一份文件供客戶簽名確認。\r\n      </h4>\r\n\r\n      <div class=\"form-group\">\r\n        <label for=\"CDocumentName\" class=\"required-field mr-4\" style=\"min-width:75px\" baseLabel>\r\n          文件名稱\r\n        </label>\r\n        <input type=\"text\" class=\"w-full\" nbInput placeholder=\"文件名稱\" [(ngModel)]=\"finalDoc.CDocumentName\" />\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label for=\"isChecked\" class=\"required-field mr-4\" style=\"min-width:75px\" baseLabel>\r\n          選樣結果\r\n        </label>\r\n        <nb-checkbox status=\"basic\" [(checked)]=\"isChecked\" disabled>選樣結果 \r\n        </nb-checkbox>\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label for=\"客變圖\" class=\"mr-4\" style=\"min-width:75px\" baseLabel>\r\n          客變圖\r\n        </label>\r\n        <h4>僅能勾選已通過審核之圖面。</h4>\r\n\r\n      </div>\r\n\r\n      <table style=\"min-width: 600px;\" class=\"table border table-striped\">\r\n        <thead>\r\n          <tr style=\"background-color: #27ae60; color: white;\">\r\n            <th></th>\r\n            <th>討論日期</th>\r\n            <th>圖面名稱</th>\r\n            <th>上傳日期</th>\r\n            <th>通過日期</th>\r\n          </tr>\r\n        </thead>\r\n        <tbody>\r\n          <tr *ngFor=\"let drawing of listSpecialChangeAvailable\">\r\n            <td>\r\n              <nb-checkbox status=\"basic\" [(checked)]=\"drawing.isChecked\">\r\n              </nb-checkbox>\r\n            </td>\r\n            <td>{{ drawing.CChangeDate }}</td>\r\n            <td>{{ drawing.CDrawingName }}</td>\r\n            <td>{{ drawing.CCreateDT }}</td>\r\n            <td>{{ drawing.CApproveDate }}</td>\r\n          </tr>\r\n        </tbody>\r\n      </table>\r\n\r\n      <div class=\"form-group d-flex align-items-center\">\r\n        <label for=\"remark\" baseLabel class=\"required-field align-self-start\" style=\"min-width:75px\">送審資訊</label>\r\n        <textarea name=\"remark\" id=\"remark\" rows=\"5\" nbInput style=\"resize: none; max-width: none\" class=\"w-full\"\r\n          [(ngModel)]=\"finalDoc.CApproveRemark\">\r\n        </textarea>\r\n      </div>\r\n\r\n      <div class=\"form-group d-flex align-items-center\">\r\n        <label for=\"CNote\" baseLabel class=\"required-field align-self-start\" style=\"min-width:75px\">系統操作說明</label>\r\n        <textarea name=\"CNote\" id=\"CNote\" rows=\"5\" nbInput style=\"resize: none; max-width: none\" class=\"w-full\"\r\n          [(ngModel)]=\"finalDoc.CNote\">\r\n        </textarea>\r\n      </div>\r\n    </nb-card-body>\r\n    <nb-card-footer class=\"d-flex justify-content-center\">\r\n      <button class=\"btn btn-outline-secondary m-2\" (click)=\"onClose(ref)\">取消</button>\r\n      <button class=\"btn btn-success m-2\" (click)=\"onCreateFinalDoc(ref)\">確認送出審核</button>\r\n    </nb-card-footer>\r\n  </nb-card>\r\n</ng-template>\r\n\r\n"], "mappings": "AAQA,SAASA,aAAa,QAAQ,qCAAqC;AAEnE,SAAuBC,MAAM,QAAQ,uCAAuC;;;;;;;;;;;;;;;;;;;ICsBhEC,EADF,CAAAC,cAAA,aAA0E,SACpE;IAAAD,EAAA,CAAAE,MAAA,GAA4E;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACrFH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,GAAuB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAChCH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,GAAwD;;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACjEH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,GAA+C;;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAEtDH,EADF,CAAAC,cAAA,cAA6B,kBAEK;IAAhCD,EAAA,CAAAI,UAAA,mBAAAC,uEAAA;MAAA,MAAAC,OAAA,GAAAN,EAAA,CAAAO,aAAA,CAAAC,GAAA,EAAAC,SAAA;MAAA,MAAAC,MAAA,GAAAV,EAAA,CAAAW,aAAA;MAAA,OAAAX,EAAA,CAAAY,WAAA,CAASF,MAAA,CAAAG,eAAA,CAAAP,OAAA,CAAqB;IAAA,EAAC;IAACN,EAAA,CAAAE,MAAA,oBAAE;IAEtCF,EAFsC,CAAAG,YAAA,EAAS,EACxC,EACF;;;;IARCH,EAAA,CAAAc,SAAA,GAA4E;IAA5Ed,EAAA,CAAAe,iBAAA,CAAAT,OAAA,CAAAU,SAAA,6BAAAV,OAAA,CAAAU,SAAA,iCAA4E;IAC5EhB,EAAA,CAAAc,SAAA,GAAuB;IAAvBd,EAAA,CAAAe,iBAAA,CAAAT,OAAA,CAAAW,aAAA,CAAuB;IACvBjB,EAAA,CAAAc,SAAA,GAAwD;IAAxDd,EAAA,CAAAe,iBAAA,CAAAT,OAAA,CAAAY,SAAA,GAAAlB,EAAA,CAAAmB,WAAA,OAAAb,OAAA,CAAAY,SAAA,OAAwD;IACxDlB,EAAA,CAAAc,SAAA,GAA+C;IAA/Cd,EAAA,CAAAe,iBAAA,CAAAf,EAAA,CAAAmB,WAAA,QAAAb,OAAA,CAAAc,eAAA,EAA+C;IAEEpB,EAAA,CAAAc,SAAA,GAAwB;IAAxBd,EAAA,CAAAqB,UAAA,cAAAf,OAAA,CAAAgB,KAAA,CAAwB;;;;;;IAqE3EtB,EAFJ,CAAAC,cAAA,SAAuD,SACjD,sBAC0D;IAAhCD,EAAA,CAAAuB,gBAAA,2BAAAC,kGAAAC,MAAA;MAAA,MAAAC,UAAA,GAAA1B,EAAA,CAAAO,aAAA,CAAAoB,GAAA,EAAAlB,SAAA;MAAAT,EAAA,CAAA4B,kBAAA,CAAAF,UAAA,CAAAG,SAAA,EAAAJ,MAAA,MAAAC,UAAA,CAAAG,SAAA,GAAAJ,MAAA;MAAA,OAAAzB,EAAA,CAAAY,WAAA,CAAAa,MAAA;IAAA,EAA+B;IAE7DzB,EADE,CAAAG,YAAA,EAAc,EACX;IACLH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,GAAyB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAClCH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,GAA0B;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACnCH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,GAAuB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAChCH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,IAA0B;IAChCF,EADgC,CAAAG,YAAA,EAAK,EAChC;;;;IAP2BH,EAAA,CAAAc,SAAA,GAA+B;IAA/Bd,EAAA,CAAA8B,gBAAA,YAAAJ,UAAA,CAAAG,SAAA,CAA+B;IAGzD7B,EAAA,CAAAc,SAAA,GAAyB;IAAzBd,EAAA,CAAAe,iBAAA,CAAAW,UAAA,CAAAK,WAAA,CAAyB;IACzB/B,EAAA,CAAAc,SAAA,GAA0B;IAA1Bd,EAAA,CAAAe,iBAAA,CAAAW,UAAA,CAAAM,YAAA,CAA0B;IAC1BhC,EAAA,CAAAc,SAAA,GAAuB;IAAvBd,EAAA,CAAAe,iBAAA,CAAAW,UAAA,CAAAO,SAAA,CAAuB;IACvBjC,EAAA,CAAAc,SAAA,GAA0B;IAA1Bd,EAAA,CAAAe,iBAAA,CAAAW,UAAA,CAAAQ,YAAA,CAA0B;;;;;;IAlDtClC,EADF,CAAAC,cAAA,kBAAgD,qBAC9B;IACdD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAiB;IAEfH,EADF,CAAAC,cAAA,uBAA2B,SACrB;IACFD,EAAA,CAAAE,MAAA,yJACF;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAGHH,EADF,CAAAC,cAAA,cAAwB,gBACkE;IACtFD,EAAA,CAAAE,MAAA,iCACF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,gBAAoG;IAAvCD,EAAA,CAAAuB,gBAAA,2BAAAY,sFAAAV,MAAA;MAAAzB,EAAA,CAAAO,aAAA,CAAA6B,GAAA;MAAA,MAAA1B,MAAA,GAAAV,EAAA,CAAAW,aAAA;MAAAX,EAAA,CAAA4B,kBAAA,CAAAlB,MAAA,CAAA2B,QAAA,CAAApB,aAAA,EAAAQ,MAAA,MAAAf,MAAA,CAAA2B,QAAA,CAAApB,aAAA,GAAAQ,MAAA;MAAA,OAAAzB,EAAA,CAAAY,WAAA,CAAAa,MAAA;IAAA,EAAoC;IACnGzB,EADE,CAAAG,YAAA,EAAoG,EAChG;IAGJH,EADF,CAAAC,cAAA,eAAwB,iBAC8D;IAClFD,EAAA,CAAAE,MAAA,kCACF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,uBAA6D;IAAjCD,EAAA,CAAAuB,gBAAA,2BAAAe,6FAAAb,MAAA;MAAAzB,EAAA,CAAAO,aAAA,CAAA6B,GAAA;MAAA,MAAA1B,MAAA,GAAAV,EAAA,CAAAW,aAAA;MAAAX,EAAA,CAAA4B,kBAAA,CAAAlB,MAAA,CAAAmB,SAAA,EAAAJ,MAAA,MAAAf,MAAA,CAAAmB,SAAA,GAAAJ,MAAA;MAAA,OAAAzB,EAAA,CAAAY,WAAA,CAAAa,MAAA;IAAA,EAAuB;IAAUzB,EAAA,CAAAE,MAAA,iCAC7D;IACFF,EADE,CAAAG,YAAA,EAAc,EACV;IAGJH,EADF,CAAAC,cAAA,eAAwB,iBACyC;IAC7DD,EAAA,CAAAE,MAAA,4BACF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAE,MAAA,sFAAa;IAEnBF,EAFmB,CAAAG,YAAA,EAAK,EAElB;IAIFH,EAFJ,CAAAC,cAAA,iBAAoE,aAC3D,cACgD;IACnDD,EAAA,CAAAuC,SAAA,UAAS;IACTvC,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAE,MAAA,gCAAI;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACbH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAE,MAAA,gCAAI;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACbH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAE,MAAA,gCAAI;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACbH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAE,MAAA,gCAAI;IAEZF,EAFY,CAAAG,YAAA,EAAK,EACV,EACC;IACRH,EAAA,CAAAC,cAAA,aAAO;IACLD,EAAA,CAAAwC,UAAA,KAAAC,4DAAA,kBAAuD;IAW3DzC,EADE,CAAAG,YAAA,EAAQ,EACF;IAGNH,EADF,CAAAC,cAAA,eAAkD,iBAC6C;IAAAD,EAAA,CAAAE,MAAA,gCAAI;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACzGH,EAAA,CAAAC,cAAA,oBACwC;IAAtCD,EAAA,CAAAuB,gBAAA,2BAAAmB,0FAAAjB,MAAA;MAAAzB,EAAA,CAAAO,aAAA,CAAA6B,GAAA;MAAA,MAAA1B,MAAA,GAAAV,EAAA,CAAAW,aAAA;MAAAX,EAAA,CAAA4B,kBAAA,CAAAlB,MAAA,CAAA2B,QAAA,CAAAM,cAAA,EAAAlB,MAAA,MAAAf,MAAA,CAAA2B,QAAA,CAAAM,cAAA,GAAAlB,MAAA;MAAA,OAAAzB,EAAA,CAAAY,WAAA,CAAAa,MAAA;IAAA,EAAqC;IACvCzB,EAAA,CAAAE,MAAA;IACFF,EADE,CAAAG,YAAA,EAAW,EACP;IAGJH,EADF,CAAAC,cAAA,eAAkD,iBAC4C;IAAAD,EAAA,CAAAE,MAAA,4CAAM;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAC1GH,EAAA,CAAAC,cAAA,oBAC+B;IAA7BD,EAAA,CAAAuB,gBAAA,2BAAAqB,0FAAAnB,MAAA;MAAAzB,EAAA,CAAAO,aAAA,CAAA6B,GAAA;MAAA,MAAA1B,MAAA,GAAAV,EAAA,CAAAW,aAAA;MAAAX,EAAA,CAAA4B,kBAAA,CAAAlB,MAAA,CAAA2B,QAAA,CAAAQ,KAAA,EAAApB,MAAA,MAAAf,MAAA,CAAA2B,QAAA,CAAAQ,KAAA,GAAApB,MAAA;MAAA,OAAAzB,EAAA,CAAAY,WAAA,CAAAa,MAAA;IAAA,EAA4B;IAC9BzB,EAAA,CAAAE,MAAA;IAEJF,EAFI,CAAAG,YAAA,EAAW,EACP,EACO;IAEbH,EADF,CAAAC,cAAA,0BAAsD,kBACiB;IAAvBD,EAAA,CAAAI,UAAA,mBAAA0C,gFAAA;MAAA,MAAAC,MAAA,GAAA/C,EAAA,CAAAO,aAAA,CAAA6B,GAAA,EAAAY,SAAA;MAAA,MAAAtC,MAAA,GAAAV,EAAA,CAAAW,aAAA;MAAA,OAAAX,EAAA,CAAAY,WAAA,CAASF,MAAA,CAAAuC,OAAA,CAAAF,MAAA,CAAY;IAAA,EAAC;IAAC/C,EAAA,CAAAE,MAAA,oBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAChFH,EAAA,CAAAC,cAAA,kBAAoE;IAAhCD,EAAA,CAAAI,UAAA,mBAAA8C,gFAAA;MAAA,MAAAH,MAAA,GAAA/C,EAAA,CAAAO,aAAA,CAAA6B,GAAA,EAAAY,SAAA;MAAA,MAAAtC,MAAA,GAAAV,EAAA,CAAAW,aAAA;MAAA,OAAAX,EAAA,CAAAY,WAAA,CAASF,MAAA,CAAAyC,gBAAA,CAAAJ,MAAA,CAAqB;IAAA,EAAC;IAAC/C,EAAA,CAAAE,MAAA,4CAAM;IAE9EF,EAF8E,CAAAG,YAAA,EAAS,EACpE,EACT;;;;IAxENH,EAAA,CAAAc,SAAA,GACF;IADEd,EAAA,CAAAoD,kBAAA,8EAAA1C,MAAA,CAAA2C,SAAA,CAAAC,UAAA,OAAA5C,MAAA,CAAA2C,SAAA,CAAAE,MAAA,OACF;IAUiEvD,EAAA,CAAAc,SAAA,GAAoC;IAApCd,EAAA,CAAA8B,gBAAA,YAAApB,MAAA,CAAA2B,QAAA,CAAApB,aAAA,CAAoC;IAOrEjB,EAAA,CAAAc,SAAA,GAAuB;IAAvBd,EAAA,CAAA8B,gBAAA,YAAApB,MAAA,CAAAmB,SAAA,CAAuB;IAuBzB7B,EAAA,CAAAc,SAAA,IAA6B;IAA7Bd,EAAA,CAAAqB,UAAA,YAAAX,MAAA,CAAA8C,0BAAA,CAA6B;IAgBrDxD,EAAA,CAAAc,SAAA,GAAqC;IAArCd,EAAA,CAAA8B,gBAAA,YAAApB,MAAA,CAAA2B,QAAA,CAAAM,cAAA,CAAqC;IAOrC3C,EAAA,CAAAc,SAAA,GAA4B;IAA5Bd,EAAA,CAAA8B,gBAAA,YAAApB,MAAA,CAAA2B,QAAA,CAAAQ,KAAA,CAA4B;;;AD7GtC,OAAM,MAAOY,8BAA+B,SAAQ3D,aAAa;EAC/D4D,YACUC,MAAmB,EACnBC,aAA8B,EAC9BC,KAAuB,EACvBC,qBAA2C,EAC3CC,OAAuB,EACvBC,KAAqB,EACrBC,QAAkB,EAClBC,aAA2B,EAC3BC,aAA2B;IAEnC,KAAK,CAACR,MAAM,CAAC;IAVL,KAAAA,MAAM,GAANA,MAAM;IACN,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,qBAAqB,GAArBA,qBAAqB;IACrB,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,QAAQ,GAARA,QAAQ;IACR,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,aAAa,GAAbA,aAAa;IAiCvB,KAAAC,qBAAqB,GAAG,CAAC,KAAK,EAAE,KAAK,EAAE,OAAO,EAAE,OAAO,CAAC;IA0FxD,KAAAvC,SAAS,GAAG,IAAI;EAxHhB;EAKSwC,QAAQA,CAAA;IACf,IAAI,CAACL,KAAK,CAACM,QAAQ,CAACC,SAAS,CAACC,MAAM,IAAG;MACrC,IAAIA,MAAM,EAAE;QACV,IAAI,CAACC,WAAW,GAAG,EAAED,MAAM,CAACE,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QAC5C,IAAI,CAACC,OAAO,GAAG,EAAEH,MAAM,CAACE,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QACxC,IAAG,IAAI,CAACC,OAAO,EAAE;UACf,IAAI,CAACC,YAAY,EAAE;QACrB;QACA,IAAI,CAACC,eAAe,EAAE;MACxB;IACF,CAAC,CAAC;EACJ;EAIAD,YAAYA,CAAA;IACV,IAAI,CAACT,aAAa,CAACW,6BAA6B,CAAC;MAC/CC,IAAI,EAAE;QAAEC,QAAQ,EAAE,IAAI,CAACL;MAAO;KAC/B,CAAC,CAACJ,SAAS,CAACU,GAAG,IAAG;MACjB,IAAIA,GAAG,CAACC,OAAO,IAAID,GAAG,CAACE,UAAU,IAAI,CAAC,EAAE;QACtC,IAAI,CAAC9B,SAAS,GAAG4B,GAAG,CAACC,OAAO;MAC9B;IACF,CAAC,CAAC;EACJ;EAIArE,eAAeA,CAACuE,IAAU;IACxB,IAAIA,IAAI,IAAIA,IAAI,CAAC9D,KAAK,EAAE+D,MAAM,CAACC,IAAI,CAACF,IAAI,CAAC9D,KAAK,EAAE,QAAQ,CAAC;EAC3D;EAMAuD,eAAeA,CAAA;IACb,IAAI,CAACf,qBAAqB,CAACyB,wCAAwC,CAAC;MAClER,IAAI,EAAE;QACJC,QAAQ,EAAE,IAAI,CAACL,OAAO;QACtBa,SAAS,EAAE,IAAI,CAACC,SAAS;QACzBC,QAAQ,EAAE,IAAI,CAACC;;KAElB,CAAC,CAACpB,SAAS,CAACU,GAAG,IAAG;MACjB,IAAIA,GAAG,CAACW,UAAU,IAAIX,GAAG,CAACC,OAAO,IAAID,GAAG,CAACE,UAAU,IAAI,CAAC,EAAE;QACxD,IAAI,CAACU,YAAY,GAAGZ,GAAG,CAACC,OAAQ,IAAI,EAAE;QACtC,IAAI,CAACY,YAAY,GAAGb,GAAG,CAACW,UAAU;MACpC;IACF,CAAC,CAAC;EACJ;EAIAG,6BAA6BA,CAAA;IAC3B,IAAI,CAACjC,qBAAqB,CAACkC,sDAAsD,CAAC;MAChFjB,IAAI,EAAE;QACJC,QAAQ,EAAE,IAAI,CAACL;;KAElB,CAAC,CAACJ,SAAS,CAACU,GAAG,IAAG;MACjB,IAAI,CAACzB,0BAA0B,GAAG,EAAE;MACpC,IAAIyB,GAAG,CAACW,UAAU,IAAIX,GAAG,CAACC,OAAO,IAAID,GAAG,CAACE,UAAU,IAAI,CAAC,EAAE;QACxD,IAAI,CAAC3B,0BAA0B,GAAGyB,GAAG,CAACC,OAAQ,IAAI,EAAE;QACpD,IAAID,GAAG,CAACC,OAAO,CAACe,MAAM,EAAE;UACtB,IAAI,CAACzC,0BAA0B,GAAGyB,GAAG,CAACC,OAAO,CAACgB,GAAG,CAAEC,CAAM,IAAI;YAC3D,OAAO;cAAE,GAAGA,CAAC;cAAEtE,SAAS,EAAE;YAAK,CAAE;UACnC,CAAC,CAAC;QACJ;MACF;IACF,CAAC,CAAC;EACJ;EAGAuE,UAAUA,CAAA;IACR,IAAI,CAACvC,KAAK,CAACwC,KAAK,EAAE;IAClB,IAAI,CAACxC,KAAK,CAACyC,QAAQ,CAAC,QAAQ,EAAE,IAAI,CAACjE,QAAQ,CAACpB,aAAa,CAAC;IAC1D,IAAI,CAAC4C,KAAK,CAACyC,QAAQ,CAAC,QAAQ,EAAE,IAAI,CAACjE,QAAQ,CAACM,cAAc,CAAC;IAC3D,IAAI,CAACkB,KAAK,CAACyC,QAAQ,CAAC,UAAU,EAAE,IAAI,CAACjE,QAAQ,CAACQ,KAAK,CAAC;EACtD;EAEA0D,MAAMA,CAAA;IACJ,IAAI,CAACrC,aAAa,CAACsC,IAAI,CAAC;MACtBC,MAAM;MACNC,OAAO,EAAE,IAAI,CAACjC;KACf,CAAC;IACF,IAAI,CAACR,QAAQ,CAAC0C,IAAI,EAAE;EACtB;EAEAC,cAAcA,CAACC,WAAkB;IAC/B,IAAIA,WAAW,IAAIA,WAAW,CAACZ,MAAM,EAAE;MACrC,OAAOY,WAAW,CAACC,MAAM,CAACC,MAAM,IAAIA,MAAM,CAAClF,SAAS,CAAC,CAACqE,GAAG,CAACa,MAAM,IAAIA,MAAM,CAACC,GAAG,CAAC;IACjF;IAAE,OAAO,EAAE;EACb;EAEA7D,gBAAgBA,CAAC8D,GAAQ;IACvB,IAAI,CAACb,UAAU,EAAE;IACjB,IAAI,IAAI,CAACvC,KAAK,CAACqD,aAAa,CAACjB,MAAM,GAAG,CAAC,EAAE;MACvC,IAAI,CAAClC,OAAO,CAACoD,aAAa,CAAC,IAAI,CAACtD,KAAK,CAACqD,aAAa,CAAC;MACpD;IACF;IACA,MAAME,KAAK,GAAG;MACZ,GAAG,IAAI,CAAC/E,QAAQ;MAChBgF,cAAc,EAAE,IAAI,CAACT,cAAc,CAAC,IAAI,CAACpD,0BAA0B;KACpE;IACD,IAAI,CAACM,qBAAqB,CAACwD,uCAAuC,CAAC;MACjEvC,IAAI,EAAEqC;KACP,CAAC,CAAC7C,SAAS,CAACU,GAAG,IAAG;MACjB,IAAIA,GAAG,CAACE,UAAU,KAAK,CAAC,EAAE;QACxB,IAAI,CAACN,eAAe,EAAE;QACtB,IAAI,CAACd,OAAO,CAACwD,aAAa,CAAC,MAAM,CAAC;QAClCN,GAAG,CAACO,KAAK,EAAE;MACb,CAAC,MAAM;QACL,IAAI,CAACzD,OAAO,CAAC0D,YAAY,CAACxC,GAAG,CAACyC,OAAQ,CAAC;MACzC;IACF,CAAC,CAAC;EACJ;EAGAC,WAAWA,CAACC,OAAe;IACzB,IAAI,CAACnC,SAAS,GAAGmC,OAAO;IACxB,IAAI,CAAC/C,eAAe,EAAE;EACxB;EAGAgD,MAAMA,CAACZ,GAAQ;IACb,IAAI,CAAC5E,QAAQ,GAAG;MACd2C,QAAQ,EAAE,IAAI,CAACL,OAAO;MACtB1D,aAAa,EAAE,EAAE;MACjB0B,cAAc,EAAE,EAAE;MAClBE,KAAK,EAAC;KACP;IACD,IAAI,CAACkD,6BAA6B,EAAE;IACpC,IAAI,CAACnC,aAAa,CAAC0B,IAAI,CAAC2B,GAAG,CAAC;EAC9B;EAEAa,WAAWA,CAACb,GAAQ;IAClB,IAAI,CAACrD,aAAa,CAAC0B,IAAI,CAAC2B,GAAG,CAAC;EAC9B;EAEAhE,OAAOA,CAACgE,GAAQ;IACdA,GAAG,CAACO,KAAK,EAAE;EACb;;;uCA7JW/D,8BAA8B,EAAAzD,EAAA,CAAA+H,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAjI,EAAA,CAAA+H,iBAAA,CAAAG,EAAA,CAAAC,eAAA,GAAAnI,EAAA,CAAA+H,iBAAA,CAAAK,EAAA,CAAAC,gBAAA,GAAArI,EAAA,CAAA+H,iBAAA,CAAAO,EAAA,CAAAC,oBAAA,GAAAvI,EAAA,CAAA+H,iBAAA,CAAAS,EAAA,CAAAC,cAAA,GAAAzI,EAAA,CAAA+H,iBAAA,CAAAW,EAAA,CAAAC,cAAA,GAAA3I,EAAA,CAAA+H,iBAAA,CAAAa,EAAA,CAAAC,QAAA,GAAA7I,EAAA,CAAA+H,iBAAA,CAAAe,EAAA,CAAAC,YAAA,GAAA/I,EAAA,CAAA+H,iBAAA,CAAAO,EAAA,CAAAU,YAAA;IAAA;EAAA;;;YAA9BvF,8BAA8B;MAAAwF,SAAA;MAAAC,QAAA,GAAAlJ,EAAA,CAAAmJ,0BAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,wCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;UCjBzCzJ,EADF,CAAAC,cAAA,iBAA0B,qBACR;UACdD,EAAA,CAAAuC,SAAA,qBAAiC;UACnCvC,EAAA,CAAAG,YAAA,EAAiB;UAEfH,EADF,CAAAC,cAAA,mBAAc,YACyB;UAAAD,EAAA,CAAAE,MAAA,weAEL;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAM/BH,EAJN,CAAAC,cAAA,aAA8B,aAEL,aAC0B,gBACqB;UAArCD,EAAA,CAAAI,UAAA,mBAAAuJ,gEAAA;YAAA3J,EAAA,CAAAO,aAAA,CAAAqJ,GAAA;YAAA,MAAAC,qBAAA,GAAA7J,EAAA,CAAA8J,WAAA;YAAA,OAAA9J,EAAA,CAAAY,WAAA,CAAS8I,GAAA,CAAA7B,MAAA,CAAAgC,qBAAA,CAA0B;UAAA,EAAC;UAC/D7J,EAAA,CAAAE,MAAA,mDAAO;UAGfF,EAHe,CAAAG,YAAA,EAAS,EACd,EACF,EACF;UAMEH,EAJR,CAAAC,cAAA,cAAmC,gBAC+D,aACvF,aACoE,cACzC;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACrCH,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAE,MAAA,gCAAI;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACvCH,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAE,MAAA,iCAAK;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACxCH,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACrCH,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAEpCF,EAFoC,CAAAG,YAAA,EAAK,EAClC,EACC;UACRH,EAAA,CAAAC,cAAA,aAAO;UACLD,EAAA,CAAAwC,UAAA,KAAAuH,6CAAA,kBAA0E;UAalF/J,EAHM,CAAAG,YAAA,EAAQ,EACF,EACJ,EACO;UAEbH,EADF,CAAAC,cAAA,0BAAsD,0BAES;UAD7CD,EAAA,CAAAuB,gBAAA,wBAAAyI,8EAAAvI,MAAA;YAAAzB,EAAA,CAAAO,aAAA,CAAAqJ,GAAA;YAAA5J,EAAA,CAAA4B,kBAAA,CAAA8H,GAAA,CAAAjE,SAAA,EAAAhE,MAAA,MAAAiI,GAAA,CAAAjE,SAAA,GAAAhE,MAAA;YAAA,OAAAzB,EAAA,CAAAY,WAAA,CAAAa,MAAA;UAAA,EAAoB;UAClCzB,EAAA,CAAAI,UAAA,wBAAA4J,8EAAAvI,MAAA;YAAAzB,EAAA,CAAAO,aAAA,CAAAqJ,GAAA;YAAA,OAAA5J,EAAA,CAAAY,WAAA,CAAc8I,GAAA,CAAA/B,WAAA,CAAAlG,MAAA,CAAmB;UAAA,EAAC;UAEtCzB,EADE,CAAAG,YAAA,EAAiB,EACF;UAGbH,EAFJ,CAAAC,cAAA,sBAAgB,eAC6B,kBACoB;UAAnBD,EAAA,CAAAI,UAAA,mBAAA6J,iEAAA;YAAAjK,EAAA,CAAAO,aAAA,CAAAqJ,GAAA;YAAA,OAAA5J,EAAA,CAAAY,WAAA,CAAS8I,GAAA,CAAAnD,MAAA,EAAQ;UAAA,EAAC;UAC1DvG,EAAA,CAAAE,MAAA,wCACF;UAGNF,EAHM,CAAAG,YAAA,EAAS,EACL,EACS,EACT;UAGVH,EAAA,CAAAwC,UAAA,KAAA0H,sDAAA,iCAAAlK,EAAA,CAAAmK,sBAAA,CAAgE;;;UA7BjCnK,EAAA,CAAAc,SAAA,IAAkB;UAAlBd,EAAA,CAAAqB,UAAA,YAAAqI,GAAA,CAAA7D,YAAA,CAAkB;UAe7B7F,EAAA,CAAAc,SAAA,GAAoB;UAApBd,EAAA,CAAA8B,gBAAA,SAAA4H,GAAA,CAAAjE,SAAA,CAAoB;UAAuBzF,EAAtB,CAAAqB,UAAA,aAAAqI,GAAA,CAAA/D,QAAA,CAAqB,mBAAA+D,GAAA,CAAA5D,YAAA,CAAgC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}