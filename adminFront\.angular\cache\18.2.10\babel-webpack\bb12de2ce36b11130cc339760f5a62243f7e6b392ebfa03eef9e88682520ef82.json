{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { NbThemeModule, NbLayoutModule, NbCardModule, NbButtonModule, NbSelectModule, NbInputModule, NbCheckboxModule, NbIconModule, NbListModule, NbTagModule } from '@nebular/theme';\nimport { HouseholdBindingComponent } from './components/household-binding/household-binding.component';\nimport * as i0 from \"@angular/core\";\nexport class SharedModule {\n  static {\n    this.ɵfac = function SharedModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || SharedModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: SharedModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [CommonModule, FormsModule, ReactiveFormsModule, NbThemeModule, NbLayoutModule, NbCardModule, NbButtonModule, NbSelectModule, NbInputModule, NbCheckboxModule, NbIconModule, NbListModule, NbTagModule,\n      // 也可以導出常用的模組供其他地方使用\n      CommonModule, FormsModule, ReactiveFormsModule, NbThemeModule, NbLayoutModule, NbCardModule, NbButtonModule, NbSelectModule, NbInputModule, NbCheckboxModule, NbIconModule, NbListModule, NbTagModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(SharedModule, {\n    declarations: [HouseholdBindingComponent],\n    imports: [CommonModule, FormsModule, ReactiveFormsModule, NbThemeModule, NbLayoutModule, NbCardModule, NbButtonModule, NbSelectModule, NbInputModule, NbCheckboxModule, NbIconModule, NbListModule, NbTagModule],\n    exports: [HouseholdBindingComponent,\n    // 也可以導出常用的模組供其他地方使用\n    CommonModule, FormsModule, ReactiveFormsModule, NbThemeModule, NbLayoutModule, NbCardModule, NbButtonModule, NbSelectModule, NbInputModule, NbCheckboxModule, NbIconModule, NbListModule, NbTagModule]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "FormsModule", "ReactiveFormsModule", "NbThemeModule", "NbLayoutModule", "NbCardModule", "NbButtonModule", "NbSelectModule", "NbInputModule", "NbCheckboxModule", "NbIconModule", "NbListModule", "NbTagModule", "HouseholdBindingComponent", "SharedModule", "declarations", "imports", "exports"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\shared\\shared.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\r\nimport {\r\n    NbThemeModule,\r\n    NbLayoutModule,\r\n    NbCardModule,\r\n    NbButtonModule,\r\n    NbSelectModule,\r\n    NbInputModule,\r\n    NbCheckboxModule,\r\n    NbIconModule,\r\n    NbListModule,\r\n    NbTagModule\r\n} from '@nebular/theme';\r\n\r\nimport { HouseholdBindingComponent } from './components/household-binding/household-binding.component';\r\n\r\n@NgModule({\r\n    declarations: [\r\n        HouseholdBindingComponent\r\n    ],\r\n    imports: [\r\n        CommonModule,\r\n        FormsModule,\r\n        ReactiveFormsModule,\r\n        NbThemeModule,\r\n        NbLayoutModule,\r\n        NbCardModule,\r\n        NbButtonModule,\r\n        NbSelectModule,\r\n        NbInputModule,\r\n        NbCheckboxModule,\r\n        NbIconModule,\r\n        NbListModule,\r\n        NbTagModule\r\n    ],\r\n    exports: [\r\n        HouseholdBindingComponent,\r\n        // 也可以導出常用的模組供其他地方使用\r\n        CommonModule,\r\n        FormsModule,\r\n        ReactiveFormsModule,\r\n        NbThemeModule,\r\n        NbLayoutModule,\r\n        NbCardModule,\r\n        NbButtonModule,\r\n        NbSelectModule,\r\n        NbInputModule,\r\n        NbCheckboxModule,\r\n        NbIconModule,\r\n        NbListModule,\r\n        NbTagModule\r\n    ]\r\n})\r\nexport class SharedModule { }\r\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,EAAEC,mBAAmB,QAAQ,gBAAgB;AACjE,SACIC,aAAa,EACbC,cAAc,EACdC,YAAY,EACZC,cAAc,EACdC,cAAc,EACdC,aAAa,EACbC,gBAAgB,EAChBC,YAAY,EACZC,YAAY,EACZC,WAAW,QACR,gBAAgB;AAEvB,SAASC,yBAAyB,QAAQ,4DAA4D;;AAuCtG,OAAM,MAAOC,YAAY;;;uCAAZA,YAAY;IAAA;EAAA;;;YAAZA;IAAY;EAAA;;;gBAhCjBd,YAAY,EACZC,WAAW,EACXC,mBAAmB,EACnBC,aAAa,EACbC,cAAc,EACdC,YAAY,EACZC,cAAc,EACdC,cAAc,EACdC,aAAa,EACbC,gBAAgB,EAChBC,YAAY,EACZC,YAAY,EACZC,WAAW;MAIX;MACAZ,YAAY,EACZC,WAAW,EACXC,mBAAmB,EACnBC,aAAa,EACbC,cAAc,EACdC,YAAY,EACZC,cAAc,EACdC,cAAc,EACdC,aAAa,EACbC,gBAAgB,EAChBC,YAAY,EACZC,YAAY,EACZC,WAAW;IAAA;EAAA;;;2EAGNE,YAAY;IAAAC,YAAA,GAnCjBF,yBAAyB;IAAAG,OAAA,GAGzBhB,YAAY,EACZC,WAAW,EACXC,mBAAmB,EACnBC,aAAa,EACbC,cAAc,EACdC,YAAY,EACZC,cAAc,EACdC,cAAc,EACdC,aAAa,EACbC,gBAAgB,EAChBC,YAAY,EACZC,YAAY,EACZC,WAAW;IAAAK,OAAA,GAGXJ,yBAAyB;IACzB;IACAb,YAAY,EACZC,WAAW,EACXC,mBAAmB,EACnBC,aAAa,EACbC,cAAc,EACdC,YAAY,EACZC,cAAc,EACdC,cAAc,EACdC,aAAa,EACbC,gBAAgB,EAChBC,YAAY,EACZC,YAAY,EACZC,WAAW;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}