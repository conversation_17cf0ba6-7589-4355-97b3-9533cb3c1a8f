{"ast": null, "code": "import { ServiceBase } from './service-base';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport class HouseCustomService extends ServiceBase {\n  constructor(http) {\n    super(http);\n    this.baseUrl = `${this.apiBaseUrl}/House`;\n  }\n  getDropDown(buildCaseId) {\n    const url = `${this.baseUrl}/GetDropDown`;\n    const params = {\n      buildCaseId: buildCaseId.toString()\n    };\n    return this.http.post(url, '', {\n      params: params\n    });\n  }\n  static {\n    this.ɵfac = function HouseCustomService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || HouseCustomService)(i0.ɵɵinject(i1.HttpClient));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: HouseCustomService,\n      factory: HouseCustomService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["ServiceBase", "HouseCustomService", "constructor", "http", "baseUrl", "apiBaseUrl", "getDropDown", "buildCaseId", "url", "params", "toString", "post", "i0", "ɵɵinject", "i1", "HttpClient", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\services\\api\\services\\HouseCustom.service.ts"], "sourcesContent": ["import { HttpClient } from '@angular/common/http';\r\nimport { Injectable } from '@angular/core';\r\nimport { Observable } from 'rxjs';\r\nimport { ServiceBase } from './service-base';\r\nimport { HouseDropDownResponse } from '../models/house.model';\r\n\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class HouseCustomService extends ServiceBase {\r\n\r\n  protected baseUrl = `${this.apiBaseUrl}/House`;\r\n\r\n  constructor(\r\n    http: HttpClient\r\n  ) {\r\n    super(http);\r\n  }\r\n\r\n  getDropDown(buildCaseId: number): Observable<HouseDropDownResponse> {\r\n    const url = `${this.baseUrl}/GetDropDown`;\r\n    const params = {\r\n      buildCaseId: buildCaseId.toString()\r\n    };\r\n\r\n    return this.http.post<HouseDropDownResponse>(url, '', {\r\n      params: params\r\n    });\r\n  }\r\n}\r\n"], "mappings": "AAGA,SAASA,WAAW,QAAQ,gBAAgB;;;AAM5C,OAAM,MAAOC,kBAAmB,SAAQD,WAAW;EAIjDE,YACEC,IAAgB;IAEhB,KAAK,CAACA,IAAI,CAAC;IALH,KAAAC,OAAO,GAAG,GAAG,IAAI,CAACC,UAAU,QAAQ;EAM9C;EAEAC,WAAWA,CAACC,WAAmB;IAC7B,MAAMC,GAAG,GAAG,GAAG,IAAI,CAACJ,OAAO,cAAc;IACzC,MAAMK,MAAM,GAAG;MACbF,WAAW,EAAEA,WAAW,CAACG,QAAQ;KAClC;IAED,OAAO,IAAI,CAACP,IAAI,CAACQ,IAAI,CAAwBH,GAAG,EAAE,EAAE,EAAE;MACpDC,MAAM,EAAEA;KACT,CAAC;EACJ;;;uCAnBWR,kBAAkB,EAAAW,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;IAAA;EAAA;;;aAAlBd,kBAAkB;MAAAe,OAAA,EAAlBf,kBAAkB,CAAAgB,IAAA;MAAAC,UAAA,EAFjB;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}