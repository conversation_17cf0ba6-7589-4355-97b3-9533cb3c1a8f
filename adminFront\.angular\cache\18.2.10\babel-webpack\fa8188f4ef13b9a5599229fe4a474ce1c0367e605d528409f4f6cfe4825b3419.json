{"ast": null, "code": "export var EnumHouseType;\n(function (EnumHouseType) {\n  EnumHouseType[EnumHouseType[\"\\u5730\\u4E3B\\u6236\"] = 1] = \"\\u5730\\u4E3B\\u6236\";\n  EnumHouseType[EnumHouseType[\"\\u92B7\\u552E\\u6236\"] = 2] = \"\\u92B7\\u552E\\u6236\"; //sales account\n})(EnumHouseType || (EnumHouseType = {}));", "map": {"version": 3, "names": ["EnumHouseType"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\shared\\enum\\enumHouseType.ts"], "sourcesContent": ["export enum EnumHouseType {\r\n  地主戶 = 1, //landlord household\r\n  銷售戶 = 2  //sales account\r\n}\r\n"], "mappings": "AAAA,WAAYA,aAGX;AAHD,WAAYA,aAAa;EACvBA,aAAA,CAAAA,aAAA,kDAAO;EACPA,aAAA,CAAAA,aAAA,kDAAO,EAAE;AACX,CAAC,EAHWA,aAAa,KAAbA,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}