{"ast": null, "code": "import * as i0 from \"@angular/core\";\nexport let NumberWithCommasPipe = /*#__PURE__*/(() => {\n  class NumberWithCommasPipe {\n    transform(input) {\n      return new Intl.NumberFormat().format(input);\n    }\n    static {\n      this.ɵfac = function NumberWithCommasPipe_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || NumberWithCommasPipe)();\n      };\n    }\n    static {\n      this.ɵpipe = /*@__PURE__*/i0.ɵɵdefinePipe({\n        name: \"ngxNumberWithCommas\",\n        type: NumberWithCommasPipe,\n        pure: true,\n        standalone: true\n      });\n    }\n  }\n  return NumberWithCommasPipe;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}