{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { NbCheckboxModule } from '@nebular/theme';\nimport { tap } from 'rxjs';\nimport { SharedModule } from 'src/app/pages/components/shared.module';\nimport { BaseComponent } from 'src/app/pages/components/base/baseComponent';\nimport { EEvent } from 'src/app/shared/services/event.service';\nimport { Base64ImagePipe } from \"../../../../@theme/pipes/base64-image.pipe\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/shared/helper/allowHelper\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"src/app/shared/services/message.service\";\nimport * as i4 from \"src/services/api/services\";\nimport * as i5 from \"src/app/shared/services/utility.service\";\nimport * as i6 from \"src/app/shared/helper/validationHelper\";\nimport * as i7 from \"@angular/common\";\nimport * as i8 from \"src/app/shared/services/event.service\";\nimport * as i9 from \"@angular/forms\";\nimport * as i10 from \"@nebular/theme\";\nimport * as i11 from \"../../../components/breadcrumb/breadcrumb.component\";\nimport * as i12 from \"../../../../@theme/pipes/base-file.pipe\";\nfunction DetailContentManagementSalesAccountComponent_ng_container_25_div_24_img_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 93);\n    i0.ɵɵpipe(1, \"base64Image\");\n  }\n  if (rf & 2) {\n    const formItemReq_r3 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"src\", i0.ɵɵpipeBind1(1, 1, ctx_r3.getCurrentImage(formItemReq_r3)), i0.ɵɵsanitizeUrl);\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_25_div_24_button_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 94);\n    i0.ɵɵlistener(\"click\", function DetailContentManagementSalesAccountComponent_ng_container_25_div_24_button_8_Template_button_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r5);\n      const formItemReq_r3 = i0.ɵɵnextContext(2).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext();\n      ctx_r3.prevImage(formItemReq_r3);\n      return i0.ɵɵresetView($event.stopPropagation());\n    });\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(1, \"svg\", 24);\n    i0.ɵɵelement(2, \"path\", 95);\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_25_div_24_button_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 96);\n    i0.ɵɵlistener(\"click\", function DetailContentManagementSalesAccountComponent_ng_container_25_div_24_button_9_Template_button_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r6);\n      const formItemReq_r3 = i0.ɵɵnextContext(2).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext();\n      ctx_r3.nextImage(formItemReq_r3);\n      return i0.ɵɵresetView($event.stopPropagation());\n    });\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(1, \"svg\", 24);\n    i0.ɵɵelement(2, \"path\", 97);\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_25_div_24_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 98);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const formItemReq_r3 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate2(\" \", (formItemReq_r3.currentImageIndex || 0) + 1, \" / \", formItemReq_r3.CMatrialUrl.length, \" \");\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_25_div_24_div_11_button_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 101);\n    i0.ɵɵlistener(\"click\", function DetailContentManagementSalesAccountComponent_ng_container_25_div_24_div_11_button_1_Template_button_click_0_listener() {\n      const i_r8 = i0.ɵɵrestoreView(_r7).index;\n      const formItemReq_r3 = i0.ɵɵnextContext(3).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.openImageModal(formItemReq_r3, i_r8));\n    });\n    i0.ɵɵelement(1, \"img\", 102);\n    i0.ɵɵpipe(2, \"base64Image\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const imageUrl_r9 = ctx.$implicit;\n    const i_r8 = ctx.index;\n    const formItemReq_r3 = i0.ɵɵnextContext(3).$implicit;\n    i0.ɵɵclassProp(\"border-blue-500\", i_r8 === (formItemReq_r3.currentImageIndex || 0))(\"border-gray-300\", i_r8 !== (formItemReq_r3.currentImageIndex || 0))(\"ring-2\", i_r8 === (formItemReq_r3.currentImageIndex || 0))(\"ring-blue-200\", i_r8 === (formItemReq_r3.currentImageIndex || 0));\n    i0.ɵɵproperty(\"title\", \"\\u9EDE\\u9078\\u653E\\u5927\\u7B2C \" + (i_r8 + 1) + \" \\u5F35\\u5716\\u7247\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"src\", i0.ɵɵpipeBind1(2, 10, imageUrl_r9), i0.ɵɵsanitizeUrl);\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_25_div_24_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 99);\n    i0.ɵɵtemplate(1, DetailContentManagementSalesAccountComponent_ng_container_25_div_24_div_11_button_1_Template, 3, 12, \"button\", 100);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const formItemReq_r3 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", formItemReq_r3.CMatrialUrl);\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_25_div_24_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 53)(1, \"div\", 82);\n    i0.ɵɵlistener(\"click\", function DetailContentManagementSalesAccountComponent_ng_container_25_div_24_Template_div_click_1_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const formItemReq_r3 = i0.ɵɵnextContext().$implicit;\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.openImageModal(formItemReq_r3));\n    });\n    i0.ɵɵtemplate(2, DetailContentManagementSalesAccountComponent_ng_container_25_div_24_img_2_Template, 2, 3, \"img\", 83);\n    i0.ɵɵelementStart(3, \"div\", 84)(4, \"div\", 85)(5, \"div\", 86);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(6, \"svg\", 87);\n    i0.ɵɵelement(7, \"path\", 88);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵtemplate(8, DetailContentManagementSalesAccountComponent_ng_container_25_div_24_button_8_Template, 3, 0, \"button\", 89)(9, DetailContentManagementSalesAccountComponent_ng_container_25_div_24_button_9_Template, 3, 0, \"button\", 90)(10, DetailContentManagementSalesAccountComponent_ng_container_25_div_24_div_10_Template, 2, 2, \"div\", 91);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(11, DetailContentManagementSalesAccountComponent_ng_container_25_div_24_div_11_Template, 2, 1, \"div\", 92);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const formItemReq_r3 = i0.ɵɵnextContext().$implicit;\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.getCurrentImage(formItemReq_r3));\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngIf\", formItemReq_r3.CMatrialUrl.length > 1);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", formItemReq_r3.CMatrialUrl.length > 1);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", formItemReq_r3.CMatrialUrl.length > 1);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", formItemReq_r3.CMatrialUrl.length > 1);\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_25_div_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 103);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(1, \"svg\", 104);\n    i0.ɵɵelement(2, \"path\", 105);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(3, \"span\", 106);\n    i0.ɵɵtext(4, \"\\u7121\\u4E3B\\u8981\\u6750\\u6599\\u793A\\u610F\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_25_nb_option_53_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 107);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const case_r10 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", case_r10);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", case_r10.label, \" \");\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_25_div_68_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 109)(1, \"div\", 110);\n    i0.ɵɵelement(2, \"img\", 111)(3, \"div\", 112);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"input\", 113);\n    i0.ɵɵlistener(\"blur\", function DetailContentManagementSalesAccountComponent_ng_container_25_div_68_div_1_Template_input_blur_4_listener($event) {\n      const i_r13 = i0.ɵɵrestoreView(_r12).index;\n      const formItemReq_r3 = i0.ɵɵnextContext(2).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.renameFile($event, i_r13, formItemReq_r3));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 114);\n    i0.ɵɵlistener(\"click\", function DetailContentManagementSalesAccountComponent_ng_container_25_div_68_div_1_Template_button_click_5_listener() {\n      const picture_r14 = i0.ɵɵrestoreView(_r12).$implicit;\n      const formItemReq_r3 = i0.ɵɵnextContext(2).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.removeImage(picture_r14.id, formItemReq_r3));\n    });\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(6, \"svg\", 115);\n    i0.ɵɵelement(7, \"path\", 116);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(8, \" \\u522A\\u9664\\u5716\\u7247 \");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    let tmp_11_0;\n    let tmp_12_0;\n    const picture_r14 = ctx.$implicit;\n    const ctx_r3 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", picture_r14.data, i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"value\", picture_r14.name)(\"disabled\", (tmp_11_0 = ctx_r3.listFormItem.CIsLock) !== null && tmp_11_0 !== undefined ? tmp_11_0 : false);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", (tmp_12_0 = ctx_r3.listFormItem.CIsLock) !== null && tmp_12_0 !== undefined ? tmp_12_0 : false);\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_25_div_68_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 78);\n    i0.ɵɵtemplate(1, DetailContentManagementSalesAccountComponent_ng_container_25_div_68_div_1_Template, 9, 4, \"div\", 108);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const formItemReq_r3 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", formItemReq_r3.listPictures);\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_25_div_69_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 117)(1, \"label\", 118);\n    i0.ɵɵtext(2, \"\\u9810\\u8A2D\\u6982\\u5FF5\\u5716\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 110);\n    i0.ɵɵelement(4, \"img\", 119);\n    i0.ɵɵpipe(5, \"addBaseFile\");\n    i0.ɵɵelement(6, \"div\", 112);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const formItemReq_r3 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"src\", i0.ɵɵpipeBind1(5, 1, formItemReq_r3.CDesignFileUrl), i0.ɵɵsanitizeUrl);\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_25_div_70_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 120);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(1, \"svg\", 121);\n    i0.ɵɵelement(2, \"path\", 122);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(3, \"span\", 123);\n    i0.ɵɵtext(4, \"\\u7121\\u6982\\u5FF5\\u8A2D\\u8A08\\u5716\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_25_label_86_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r15 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"label\", 124)(1, \"nb-checkbox\", 125);\n    i0.ɵɵtwoWayListener(\"checkedChange\", function DetailContentManagementSalesAccountComponent_ng_container_25_label_86_Template_nb_checkbox_checkedChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r15);\n      const formItemReq_r3 = i0.ɵɵnextContext().$implicit;\n      i0.ɵɵtwoWayBindingSet(formItemReq_r3.allSelected, $event) || (formItemReq_r3.allSelected = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"checkedChange\", function DetailContentManagementSalesAccountComponent_ng_container_25_label_86_Template_nb_checkbox_checkedChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r15);\n      const formItemReq_r3 = i0.ɵɵnextContext().$implicit;\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.onCheckAllChange($event, formItemReq_r3));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(2, \"span\", 126);\n    i0.ɵɵtext(3, \"\\u5168\\u9078\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const formItemReq_r3 = i0.ɵɵnextContext().$implicit;\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"checked\", formItemReq_r3.allSelected);\n    i0.ɵɵproperty(\"disabled\", ctx_r3.listFormItem.CIsLock);\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_25_div_87_label_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r16 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"label\", 124)(1, \"nb-checkbox\", 129);\n    i0.ɵɵtwoWayListener(\"checkedChange\", function DetailContentManagementSalesAccountComponent_ng_container_25_div_87_label_1_Template_nb_checkbox_checkedChange_1_listener($event) {\n      const item_r17 = i0.ɵɵrestoreView(_r16).$implicit;\n      const formItemReq_r3 = i0.ɵɵnextContext(2).$implicit;\n      i0.ɵɵtwoWayBindingSet(formItemReq_r3.selectedItems[item_r17], $event) || (formItemReq_r3.selectedItems[item_r17] = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"checkedChange\", function DetailContentManagementSalesAccountComponent_ng_container_25_div_87_label_1_Template_nb_checkbox_checkedChange_1_listener($event) {\n      const item_r17 = i0.ɵɵrestoreView(_r16).$implicit;\n      const formItemReq_r3 = i0.ɵɵnextContext(2).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.onCheckboxHouseHoldListChange($event, item_r17, formItemReq_r3));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(2, \"span\", 130);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const item_r17 = ctx.$implicit;\n    const formItemReq_r3 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"checked\", formItemReq_r3.selectedItems[item_r17]);\n    i0.ɵɵproperty(\"disabled\", ctx_r3.listFormItem.CIsLock);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r17);\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_25_div_87_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 127);\n    i0.ɵɵtemplate(1, DetailContentManagementSalesAccountComponent_ng_container_25_div_87_label_1_Template, 4, 3, \"label\", 128);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.houseHoldList);\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_25_ng_template_88_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 131);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(1, \"svg\", 132);\n    i0.ɵɵelement(2, \"path\", 133);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(3, \"span\", 134);\n    i0.ɵɵtext(4, \"\\u5C1A\\u7121\\u6236\\u5225\\u8CC7\\u6599\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_25_div_90_div_6_label_1_nb_checkbox_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r18 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-checkbox\", 129);\n    i0.ɵɵtwoWayListener(\"checkedChange\", function DetailContentManagementSalesAccountComponent_ng_container_25_div_90_div_6_label_1_nb_checkbox_1_Template_nb_checkbox_checkedChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r18);\n      const remark_r19 = i0.ɵɵnextContext().$implicit;\n      const formItemReq_r3 = i0.ɵɵnextContext(3).$implicit;\n      i0.ɵɵtwoWayBindingSet(formItemReq_r3.selectedRemarkType[remark_r19], $event) || (formItemReq_r3.selectedRemarkType[remark_r19] = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"checkedChange\", function DetailContentManagementSalesAccountComponent_ng_container_25_div_90_div_6_label_1_nb_checkbox_1_Template_nb_checkbox_checkedChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r18);\n      const remark_r19 = i0.ɵɵnextContext().$implicit;\n      const formItemReq_r3 = i0.ɵɵnextContext(3).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.onCheckboxRemarkChange($event, remark_r19, formItemReq_r3));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const remark_r19 = i0.ɵɵnextContext().$implicit;\n    const formItemReq_r3 = i0.ɵɵnextContext(3).$implicit;\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵtwoWayProperty(\"checked\", formItemReq_r3.selectedRemarkType[remark_r19]);\n    i0.ɵɵproperty(\"disabled\", ctx_r3.listFormItem.CIsLock);\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_25_div_90_div_6_label_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"label\", 140);\n    i0.ɵɵtemplate(1, DetailContentManagementSalesAccountComponent_ng_container_25_div_90_div_6_label_1_nb_checkbox_1_Template, 1, 2, \"nb-checkbox\", 141);\n    i0.ɵɵelementStart(2, \"span\", 130);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const remark_r19 = ctx.$implicit;\n    const formItemReq_r3 = i0.ɵɵnextContext(3).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", formItemReq_r3.selectedRemarkType);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(remark_r19);\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_25_div_90_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 127);\n    i0.ɵɵtemplate(1, DetailContentManagementSalesAccountComponent_ng_container_25_div_90_div_6_label_1_Template, 4, 2, \"label\", 139);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.CRemarkTypeOptions);\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_25_div_90_ng_template_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 131);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(1, \"svg\", 132);\n    i0.ɵɵelement(2, \"path\", 142);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(3, \"span\", 134);\n    i0.ɵɵtext(4, \"\\u5C1A\\u7121\\u5099\\u8A3B\\u9078\\u9805\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_25_div_90_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 135)(1, \"div\", 47);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(2, \"svg\", 136);\n    i0.ɵɵelement(3, \"path\", 137);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(4, \"h5\", 138);\n    i0.ɵɵtext(5, \"\\u5099\\u8A3B\\u9078\\u9805\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(6, DetailContentManagementSalesAccountComponent_ng_container_25_div_90_div_6_Template, 2, 1, \"div\", 80)(7, DetailContentManagementSalesAccountComponent_ng_container_25_div_90_ng_template_7_Template, 5, 0, \"ng-template\", null, 2, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const noRemarkOptions_r20 = i0.ɵɵreference(8);\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.CRemarkTypeOptions && ctx_r3.CRemarkTypeOptions.length > 0)(\"ngIfElse\", noRemarkOptions_r20);\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_25_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 30)(2, \"div\", 31)(3, \"div\", 6)(4, \"div\", 15)(5, \"div\", 32);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\")(8, \"h4\", 33);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"p\", 34);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(12, \"div\", 10)(13, \"span\", 35);\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(15, \"div\", 36)(16, \"div\", 37)(17, \"div\", 38)(18, \"div\", 39)(19, \"div\", 10);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(20, \"svg\", 40);\n    i0.ɵɵelement(21, \"path\", 41);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(22, \"label\", 42);\n    i0.ɵɵtext(23, \"\\u4E3B\\u8981\\u6750\\u6599\\u793A\\u610F\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(24, DetailContentManagementSalesAccountComponent_ng_container_25_div_24_Template, 12, 5, \"div\", 43)(25, DetailContentManagementSalesAccountComponent_ng_container_25_div_25_Template, 5, 0, \"div\", 44);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(26, \"div\", 45)(27, \"div\", 46)(28, \"div\", 47);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(29, \"svg\", 40);\n    i0.ɵɵelement(30, \"path\", 18);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(31, \"label\", 42);\n    i0.ɵɵtext(32, \"\\u57FA\\u672C\\u8A2D\\u5B9A\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(33, \"div\", 39)(34, \"div\", 48)(35, \"label\", 49);\n    i0.ɵɵtext(36, \"\\u9805\\u76EE\\u540D\\u7A31\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(37, \"div\", 50)(38, \"span\", 51);\n    i0.ɵɵtext(39);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(40, \"input\", 52);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function DetailContentManagementSalesAccountComponent_ng_container_25_Template_input_ngModelChange_40_listener($event) {\n      const formItemReq_r3 = i0.ɵɵrestoreView(_r1).$implicit;\n      i0.ɵɵtwoWayBindingSet(formItemReq_r3.CItemName, $event) || (formItemReq_r3.CItemName = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(41, \"div\", 48)(42, \"label\", 49);\n    i0.ɵɵtext(43, \"\\u5FC5\\u586B\\u6578\\u91CF\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(44, \"div\", 53)(45, \"input\", 54);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function DetailContentManagementSalesAccountComponent_ng_container_25_Template_input_ngModelChange_45_listener($event) {\n      const formItemReq_r3 = i0.ɵɵrestoreView(_r1).$implicit;\n      i0.ɵɵtwoWayBindingSet(formItemReq_r3.CRequireAnswer, $event) || (formItemReq_r3.CRequireAnswer = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(46, \"div\", 55);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(47, \"svg\", 56);\n    i0.ɵɵelement(48, \"path\", 57);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(49, \"div\", 48)(50, \"label\", 49);\n    i0.ɵɵtext(51, \"\\u524D\\u53F0UI\\u985E\\u578B\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(52, \"nb-select\", 58);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function DetailContentManagementSalesAccountComponent_ng_container_25_Template_nb_select_ngModelChange_52_listener($event) {\n      const formItemReq_r3 = i0.ɵɵrestoreView(_r1).$implicit;\n      i0.ɵɵtwoWayBindingSet(formItemReq_r3.selectedCUiType, $event) || (formItemReq_r3.selectedCUiType = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"selectedChange\", function DetailContentManagementSalesAccountComponent_ng_container_25_Template_nb_select_selectedChange_52_listener() {\n      const formItemReq_r3 = i0.ɵɵrestoreView(_r1).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.changeSelectCUiType(formItemReq_r3));\n    });\n    i0.ɵɵtemplate(53, DetailContentManagementSalesAccountComponent_ng_container_25_nb_option_53_Template, 2, 2, \"nb-option\", 59);\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(54, \"div\", 38)(55, \"div\", 39)(56, \"div\", 10);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(57, \"svg\", 40);\n    i0.ɵɵelement(58, \"path\", 60);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(59, \"label\", 42);\n    i0.ɵɵtext(60, \"\\u6982\\u5FF5\\u8A2D\\u8A08\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(61, \"button\", 61);\n    i0.ɵɵlistener(\"click\", function DetailContentManagementSalesAccountComponent_ng_container_25_Template_button_click_61_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const inputFile_r11 = i0.ɵɵreference(67);\n      return i0.ɵɵresetView(inputFile_r11.click());\n    });\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(62, \"svg\", 62);\n    i0.ɵɵelement(63, \"path\", 63);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(64, \"span\");\n    i0.ɵɵtext(65, \"\\u4E0A\\u50B3\\u6982\\u5FF5\\u8A2D\\u8A08\\u5716\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(66, \"input\", 64, 0);\n    i0.ɵɵlistener(\"change\", function DetailContentManagementSalesAccountComponent_ng_container_25_Template_input_change_66_listener($event) {\n      const formItemReq_r3 = i0.ɵɵrestoreView(_r1).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.detectFiles($event, formItemReq_r3));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(68, DetailContentManagementSalesAccountComponent_ng_container_25_div_68_Template, 2, 1, \"div\", 65)(69, DetailContentManagementSalesAccountComponent_ng_container_25_div_69_Template, 7, 3, \"div\", 66)(70, DetailContentManagementSalesAccountComponent_ng_container_25_div_70_Template, 5, 0, \"div\", 67);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(71, \"div\", 68)(72, \"div\", 53)(73, \"div\", 69);\n    i0.ɵɵelement(74, \"div\", 70);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(75, \"div\", 71)(76, \"span\", 72);\n    i0.ɵɵtext(77, \"\\u8A2D\\u5B9A\\u9078\\u9805\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(78, \"div\", 73)(79, \"div\", 74)(80, \"div\", 47);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(81, \"svg\", 75);\n    i0.ɵɵelement(82, \"path\", 76);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(83, \"h5\", 77);\n    i0.ɵɵtext(84, \"\\u9069\\u7528\\u6236\\u5225\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(85, \"div\", 78);\n    i0.ɵɵtemplate(86, DetailContentManagementSalesAccountComponent_ng_container_25_label_86_Template, 4, 2, \"label\", 79)(87, DetailContentManagementSalesAccountComponent_ng_container_25_div_87_Template, 2, 1, \"div\", 80)(88, DetailContentManagementSalesAccountComponent_ng_container_25_ng_template_88_Template, 5, 0, \"ng-template\", null, 1, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(90, DetailContentManagementSalesAccountComponent_ng_container_25_div_90_Template, 9, 2, \"div\", 81);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    let tmp_15_0;\n    let tmp_19_0;\n    let tmp_23_0;\n    const formItemReq_r3 = ctx.$implicit;\n    const idx_r21 = ctx.index;\n    const noHouseholds_r22 = i0.ɵɵreference(89);\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\" \", idx_r21 + 1, \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate3(\" \", formItemReq_r3.CName, \"-\", formItemReq_r3.CPart, \"-\", formItemReq_r3.CLocation, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\\u9805\\u76EE\\u7DE8\\u865F #\", idx_r21 + 1, \"\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", (formItemReq_r3.selectedCUiType == null ? null : formItemReq_r3.selectedCUiType.label) || \"\\u672A\\u8A2D\\u5B9A\", \" \");\n    i0.ɵɵadvance(10);\n    i0.ɵɵproperty(\"ngIf\", formItemReq_r3.CMatrialUrl && formItemReq_r3.CMatrialUrl.length > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !formItemReq_r3.CMatrialUrl || formItemReq_r3.CMatrialUrl.length === 0);\n    i0.ɵɵadvance(10);\n    i0.ɵɵproperty(\"for\", \"CItemName_\" + idx_r21);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate3(\" \", formItemReq_r3.CName, \"-\", formItemReq_r3.CPart, \"-\", formItemReq_r3.CLocation, \": \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"id\", \"CItemName_\" + idx_r21);\n    i0.ɵɵtwoWayProperty(\"ngModel\", formItemReq_r3.CItemName);\n    i0.ɵɵproperty(\"disabled\", (tmp_15_0 = ctx_r3.listFormItem.CIsLock) !== null && tmp_15_0 !== undefined ? tmp_15_0 : false);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"for\", \"cRequireAnswer_\" + idx_r21);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"id\", \"cRequireAnswer_\" + idx_r21);\n    i0.ɵɵtwoWayProperty(\"ngModel\", formItemReq_r3.CRequireAnswer);\n    i0.ɵɵproperty(\"disabled\", formItemReq_r3.selectedCUiType.value === 3 || ((tmp_19_0 = ctx_r3.listFormItem.CIsLock) !== null && tmp_19_0 !== undefined ? tmp_19_0 : false));\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"for\", \"uiType_\" + idx_r21);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"id\", \"uiType_\" + idx_r21);\n    i0.ɵɵtwoWayProperty(\"ngModel\", formItemReq_r3.selectedCUiType);\n    i0.ɵɵproperty(\"disabled\", (tmp_23_0 = ctx_r3.listFormItem.CIsLock) !== null && tmp_23_0 !== undefined ? tmp_23_0 : false);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.CUiTypeOptions);\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"disabled\", ctx_r3.listFormItem.CIsLock);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngIf\", formItemReq_r3.listPictures && formItemReq_r3.listPictures.length > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", formItemReq_r3.CDesignFileUrl && (!formItemReq_r3.listPictures || formItemReq_r3.listPictures.length === 0));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !formItemReq_r3.CDesignFileUrl && (!formItemReq_r3.listPictures || formItemReq_r3.listPictures.length === 0));\n    i0.ɵɵadvance(16);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.houseHoldList.length);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.houseHoldList.length > 0)(\"ngIfElse\", noHouseholds_r22);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", formItemReq_r3.selectedCUiType.value === 3);\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_44_div_1_img_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 157);\n    i0.ɵɵpipe(1, \"base64Image\");\n  }\n  if (rf & 2) {\n    const formItemReq_r24 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"src\", i0.ɵɵpipeBind1(1, 1, ctx_r3.getCurrentImage(formItemReq_r24)), i0.ɵɵsanitizeUrl);\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_44_div_1_button_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r25 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 158);\n    i0.ɵɵlistener(\"click\", function DetailContentManagementSalesAccountComponent_ng_container_44_div_1_button_7_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r25);\n      const formItemReq_r24 = i0.ɵɵnextContext(2).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.prevImageModal(formItemReq_r24));\n    });\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(1, \"svg\", 159);\n    i0.ɵɵelement(2, \"path\", 95);\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_44_div_1_button_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r26 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 160);\n    i0.ɵɵlistener(\"click\", function DetailContentManagementSalesAccountComponent_ng_container_44_div_1_button_8_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r26);\n      const formItemReq_r24 = i0.ɵɵnextContext(2).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.nextImageModal(formItemReq_r24));\n    });\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(1, \"svg\", 161);\n    i0.ɵɵelement(2, \"path\", 97);\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_44_div_1_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 162)(1, \"div\", 15);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(2, \"svg\", 62);\n    i0.ɵɵelement(3, \"path\", 41);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(4, \"span\", 163);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const formItemReq_r24 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate2(\"\", (formItemReq_r24.currentImageIndex || 0) + 1, \" / \", formItemReq_r24.CMatrialUrl.length, \"\");\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_44_div_1_div_16_button_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r27 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 167);\n    i0.ɵɵlistener(\"click\", function DetailContentManagementSalesAccountComponent_ng_container_44_div_1_div_16_button_2_Template_button_click_0_listener() {\n      const i_r28 = i0.ɵɵrestoreView(_r27).index;\n      const formItemReq_r24 = i0.ɵɵnextContext(3).$implicit;\n      return i0.ɵɵresetView(formItemReq_r24.currentImageIndex = i_r28);\n    });\n    i0.ɵɵelement(1, \"img\", 168);\n    i0.ɵɵpipe(2, \"base64Image\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const imageUrl_r29 = ctx.$implicit;\n    const i_r28 = ctx.index;\n    const formItemReq_r24 = i0.ɵɵnextContext(3).$implicit;\n    i0.ɵɵclassProp(\"border-white\", i_r28 === (formItemReq_r24.currentImageIndex || 0))(\"border-gray-400\", i_r28 !== (formItemReq_r24.currentImageIndex || 0))(\"ring-3\", i_r28 === (formItemReq_r24.currentImageIndex || 0))(\"ring-white\", i_r28 === (formItemReq_r24.currentImageIndex || 0))(\"ring-opacity-50\", i_r28 === (formItemReq_r24.currentImageIndex || 0));\n    i0.ɵɵproperty(\"title\", \"\\u8DF3\\u81F3\\u7B2C \" + (i_r28 + 1) + \" \\u5F35\\u5716\\u7247\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"src\", i0.ɵɵpipeBind1(2, 12, imageUrl_r29), i0.ɵɵsanitizeUrl);\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_44_div_1_div_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 164)(1, \"div\", 165);\n    i0.ɵɵtemplate(2, DetailContentManagementSalesAccountComponent_ng_container_44_div_1_div_16_button_2_Template, 3, 14, \"button\", 166);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const formItemReq_r24 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", formItemReq_r24.CMatrialUrl);\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_44_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r23 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 144);\n    i0.ɵɵlistener(\"click\", function DetailContentManagementSalesAccountComponent_ng_container_44_div_1_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r23);\n      const formItemReq_r24 = i0.ɵɵnextContext().$implicit;\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.closeImageModal(formItemReq_r24));\n    })(\"keydown\", function DetailContentManagementSalesAccountComponent_ng_container_44_div_1_Template_div_keydown_0_listener($event) {\n      i0.ɵɵrestoreView(_r23);\n      const formItemReq_r24 = i0.ɵɵnextContext().$implicit;\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.onKeydown($event, formItemReq_r24));\n    });\n    i0.ɵɵelementStart(1, \"button\", 145);\n    i0.ɵɵlistener(\"click\", function DetailContentManagementSalesAccountComponent_ng_container_44_div_1_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r23);\n      const formItemReq_r24 = i0.ɵɵnextContext().$implicit;\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.closeImageModal(formItemReq_r24));\n    });\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(2, \"svg\", 146);\n    i0.ɵɵelement(3, \"path\", 147);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(4, \"div\", 148);\n    i0.ɵɵlistener(\"click\", function DetailContentManagementSalesAccountComponent_ng_container_44_div_1_Template_div_click_4_listener($event) {\n      i0.ɵɵrestoreView(_r23);\n      return i0.ɵɵresetView($event.stopPropagation());\n    });\n    i0.ɵɵelementStart(5, \"div\", 149);\n    i0.ɵɵtemplate(6, DetailContentManagementSalesAccountComponent_ng_container_44_div_1_img_6_Template, 2, 3, \"img\", 150)(7, DetailContentManagementSalesAccountComponent_ng_container_44_div_1_button_7_Template, 3, 0, \"button\", 151)(8, DetailContentManagementSalesAccountComponent_ng_container_44_div_1_button_8_Template, 3, 0, \"button\", 152);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(9, DetailContentManagementSalesAccountComponent_ng_container_44_div_1_div_9_Template, 6, 2, \"div\", 153);\n    i0.ɵɵelementStart(10, \"div\", 154)(11, \"div\", 10);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(12, \"svg\", 24);\n    i0.ɵɵelement(13, \"path\", 25);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(14, \"span\", 155);\n    i0.ɵɵtext(15);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(16, DetailContentManagementSalesAccountComponent_ng_container_44_div_1_div_16_Template, 3, 1, \"div\", 156);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const formItemReq_r24 = i0.ɵɵnextContext().$implicit;\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.getCurrentImage(formItemReq_r24));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", formItemReq_r24.CMatrialUrl && formItemReq_r24.CMatrialUrl.length > 1);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", formItemReq_r24.CMatrialUrl && formItemReq_r24.CMatrialUrl.length > 1);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", formItemReq_r24.CMatrialUrl && formItemReq_r24.CMatrialUrl.length > 1);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate3(\"\", formItemReq_r24.CName, \"-\", formItemReq_r24.CPart, \"-\", formItemReq_r24.CLocation, \"\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", formItemReq_r24.CMatrialUrl && formItemReq_r24.CMatrialUrl.length > 1);\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_44_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, DetailContentManagementSalesAccountComponent_ng_container_44_div_1_Template, 17, 8, \"div\", 143);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const formItemReq_r24 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", formItemReq_r24.isModalOpen);\n  }\n}\nexport class DetailContentManagementSalesAccountComponent extends BaseComponent {\n  constructor(_allow, route, message, _formItemService, _regularNoticeFileService, _utilityService, valid, location, _materialService, _eventService) {\n    super(_allow);\n    this._allow = _allow;\n    this.route = route;\n    this.message = message;\n    this._formItemService = _formItemService;\n    this._regularNoticeFileService = _regularNoticeFileService;\n    this._utilityService = _utilityService;\n    this.valid = valid;\n    this.location = location;\n    this._materialService = _materialService;\n    this._eventService = _eventService;\n    this.typeContentManagementSalesAccount = {\n      CFormType: 2,\n      CNoticeType: 2\n    };\n    this.CUiTypeOptions = [{\n      value: 1,\n      label: '建材選色'\n    }, {\n      value: 2,\n      label: '群組選樣_選色'\n    }, {\n      value: 3,\n      label: '建材選樣'\n    }];\n    this.CRemarkTypeOptions = [\"正常\", \"留料\"];\n    this.selectedItems = {};\n    this.selectedRemarkType = {};\n    this.isNew = true;\n  }\n  ngOnInit() {\n    this.route.paramMap.subscribe(params => {\n      if (params) {\n        const idParam = params.get('id');\n        const id = idParam ? +idParam : 0;\n        this.buildCaseId = id;\n        if (this.buildCaseId > 0) {\n          this.getListRegularNoticeFileHouseHold();\n        } else {\n          // 如果 buildCaseId 為 0 或無效，顯示錯誤訊息並返回\n          console.error('Invalid buildCaseId:', this.buildCaseId);\n          this.message.showErrorMSG(\"無效的建案ID，請重新選擇建案\");\n          this.goBack();\n        }\n      }\n    });\n  }\n  ngOnDestroy() {\n    // 確保在組件銷毀時恢復body的滾動\n    document.body.style.overflow = 'auto';\n  }\n  getItemByValue(value, options) {\n    for (const item of options) {\n      if (item.value === value) {\n        return item;\n      }\n    }\n    return null;\n  }\n  detectFiles(event, formItemReq_) {\n    const file = event.target.files[0];\n    if (file) {\n      let reader = new FileReader();\n      reader.readAsDataURL(file);\n      reader.onload = () => {\n        let base64Str = reader.result;\n        if (!base64Str) {\n          return;\n        }\n        if (formItemReq_.listPictures.length > 0) {\n          formItemReq_.listPictures[0] = {\n            id: new Date().getTime(),\n            name: file.name.split('.')[0],\n            data: base64Str,\n            extension: this._utilityService.getFileExtension(file.name),\n            CFile: file\n          };\n        } else {\n          formItemReq_.listPictures.push({\n            id: new Date().getTime(),\n            name: file.name.split('.')[0],\n            data: base64Str,\n            extension: this._utilityService.getFileExtension(file.name),\n            CFile: file\n          });\n        }\n        event.target.value = null;\n      };\n    }\n  }\n  removeImage(pictureId, formItemReq_) {\n    if (formItemReq_.listPictures.length) {\n      formItemReq_.listPictures = formItemReq_.listPictures.filter(x => x.id != pictureId);\n    }\n  }\n  renameFile(event, index, formItemReq_) {\n    var blob = formItemReq_.listPictures[index].CFile.slice(0, formItemReq_.listPictures[index].CFile.size, formItemReq_.listPictures[index].CFile.type);\n    var newFile = new File([blob], `${event.target.value + '.' + formItemReq_.listPictures[index].extension}`, {\n      type: formItemReq_.listPictures[index].CFile.type\n    });\n    formItemReq_.listPictures[index].CFile = newFile;\n  }\n  // 輪播功能方法\n  nextImage(formItemReq) {\n    if (formItemReq.CMatrialUrl && formItemReq.CMatrialUrl.length > 0) {\n      formItemReq.currentImageIndex = (formItemReq.currentImageIndex + 1) % formItemReq.CMatrialUrl.length;\n    }\n  }\n  prevImage(formItemReq) {\n    if (formItemReq.CMatrialUrl && formItemReq.CMatrialUrl.length > 0) {\n      formItemReq.currentImageIndex = formItemReq.currentImageIndex === 0 ? formItemReq.CMatrialUrl.length - 1 : formItemReq.currentImageIndex - 1;\n    }\n  }\n  getCurrentImage(formItemReq) {\n    if (formItemReq.CMatrialUrl && formItemReq.CMatrialUrl.length > 0 && formItemReq.currentImageIndex !== undefined) {\n      return formItemReq.CMatrialUrl[formItemReq.currentImageIndex];\n    }\n    return null;\n  }\n  // 放大功能方法\n  openImageModal(formItemReq, imageIndex) {\n    if (formItemReq.CMatrialUrl && formItemReq.CMatrialUrl.length > 0) {\n      if (imageIndex !== undefined) {\n        formItemReq.currentImageIndex = imageIndex;\n      }\n      formItemReq.isModalOpen = true;\n      // 防止背景滾動\n      document.body.style.overflow = 'hidden';\n    }\n  }\n  closeImageModal(formItemReq) {\n    formItemReq.isModalOpen = false;\n    // 恢復背景滾動\n    document.body.style.overflow = 'auto';\n  }\n  // 模態窗口中的輪播方法\n  nextImageModal(formItemReq) {\n    this.nextImage(formItemReq);\n  }\n  prevImageModal(formItemReq) {\n    this.prevImage(formItemReq);\n  }\n  // 鍵盤事件處理\n  onKeydown(event, formItemReq) {\n    if (formItemReq.isModalOpen) {\n      switch (event.key) {\n        case 'ArrowLeft':\n          event.preventDefault();\n          this.prevImageModal(formItemReq);\n          break;\n        case 'ArrowRight':\n          event.preventDefault();\n          this.nextImageModal(formItemReq);\n          break;\n        case 'Escape':\n          event.preventDefault();\n          this.closeImageModal(formItemReq);\n          break;\n      }\n    }\n  }\n  onCheckAllChange(checked, formItemReq_) {\n    formItemReq_.allSelected = checked;\n    this.houseHoldList.forEach(item => {\n      formItemReq_.selectedItems[item] = checked;\n    });\n  }\n  onCheckboxHouseHoldListChange(checked, item, formItemReq_) {\n    if (checked) {\n      formItemReq_.selectedItems[item] = checked;\n      formItemReq_.allSelected = this.houseHoldList.every(item => formItemReq_.selectedItems[item] == checked);\n    } else {\n      formItemReq_.allSelected = false;\n    }\n  }\n  onCheckboxRemarkChange(checked, item, formItemReq_) {\n    formItemReq_.selectedRemarkType[item] = checked;\n  }\n  createRemarkObject(CRemarkTypeOptions, CRemarkType) {\n    const remarkObject = {};\n    for (const option of CRemarkTypeOptions) {\n      remarkObject[option] = false;\n    }\n    const remarkTypes = CRemarkType.split('-');\n    for (const type of remarkTypes) {\n      if (CRemarkTypeOptions.includes(type)) {\n        remarkObject[type] = true;\n      }\n    }\n    return remarkObject;\n  }\n  mergeItems(items) {\n    const map = new Map();\n    items.forEach(item => {\n      const key = `${item.CLocation}_${item.CName}_${item.CPart}`;\n      if (map.has(key)) {\n        const existing = map.get(key);\n        existing.count += 1;\n      } else {\n        map.set(key, {\n          item: {\n            ...item\n          },\n          count: 1\n        });\n      }\n    });\n    return Array.from(map.values()).map(({\n      item,\n      count\n    }) => ({\n      ...item,\n      CTotalAnswer: count\n    }));\n  }\n  getMaterialList() {\n    this._materialService.apiMaterialGetMaterialListPost$Json({\n      body: {\n        CBuildCaseId: this.buildCaseId,\n        CPagi: false\n      }\n    }).pipe(tap(res => {\n      if (res.Entries && res.StatusCode == 0) {\n        this.houseHoldList.forEach(item => this.selectedItems[item] = false);\n        this.CRemarkTypeOptions.forEach(item => this.selectedRemarkType[item] = false);\n        this.arrListFormItemReq = res.Entries.map(o => {\n          return {\n            CDesignFileUrl: null,\n            CFormItemHouseHold: null,\n            CFormId: null,\n            CLocation: o.CLocation,\n            CName: o.CName,\n            CPart: o.CPart,\n            CItemName: `${o.CName}-${o.CPart}-${o.CLocation}`,\n            CRemarkType: null,\n            CTotalAnswer: 0,\n            CRequireAnswer: 1,\n            CUiType: 0,\n            selectedItems: {},\n            selectedRemarkType: this.selectedRemarkType,\n            allSelected: false,\n            listPictures: [],\n            selectedCUiType: this.CUiTypeOptions[0],\n            CMatrialUrl: o.CPicture ? [o.CPicture] : [],\n            currentImageIndex: 0,\n            isModalOpen: false\n          };\n        });\n        this.arrListFormItemReq = [...this.mergeItems(this.arrListFormItemReq)];\n      }\n    })).subscribe();\n  }\n  getListFormItem() {\n    this._formItemService.apiFormItemGetListFormItemPost$Json({\n      body: {\n        CBuildCaseId: this.buildCaseId,\n        CFormType: this.typeContentManagementSalesAccount.CFormType,\n        CIsPaging: false\n      }\n    }).pipe(tap(res => {\n      if (res.Entries && res.StatusCode == 0) {\n        this.listFormItem = res.Entries;\n        this.isNew = res.Entries.formItems ? false : true;\n        if (res.Entries.formItems) {\n          this.houseHoldList.forEach(item => this.selectedItems[item] = false);\n          this.CRemarkTypeOptions.forEach(item => this.selectedRemarkType[item] = false);\n          this.arrListFormItemReq = res.Entries.formItems.map(o => {\n            return {\n              CFormId: this.listFormItem.CFormId,\n              CDesignFileUrl: o.CDesignFileUrl,\n              CMatrialUrl: o.CMatrialUrl || (o.CFirstMatrialUrl ? [o.CFirstMatrialUrl] : []),\n              CFile: o.CFile,\n              CFormItemHouseHold: o.CFormItemHouseHold,\n              CFormItemId: o.CFormItemId,\n              CLocation: o.CLocation,\n              CName: o.CName,\n              CPart: o.CPart,\n              CItemName: o.CItemName ? o.CItemName : `${o.CName}-${o.CPart}-${o.CLocation}`,\n              CRemarkType: o.CRemarkType,\n              CTotalAnswer: o.CTotalAnswer,\n              CRequireAnswer: o.CUiType === 3 ? 1 : o.CRequireAnswer,\n              CUiType: o.CUiType,\n              selectedItems: o.tblFormItemHouseholds.length ? this.createArrayObjectFromArray(this.houseHoldList, o.tblFormItemHouseholds) : {\n                ...this.selectedItems\n              },\n              selectedRemarkType: o.CRemarkType ? this.createRemarkObject(this.CRemarkTypeOptions, o.CRemarkType) : {\n                ...this.selectedRemarkType\n              },\n              allSelected: o.tblFormItemHouseholds.length === this.houseHoldList.length,\n              listPictures: [],\n              selectedCUiType: o.CUiType ? this.getItemByValue(o.CUiType, this.CUiTypeOptions) : this.CUiTypeOptions[0],\n              currentImageIndex: 0,\n              isModalOpen: false\n            };\n          });\n        } else {\n          this.getMaterialList();\n        }\n      }\n    })).subscribe();\n  }\n  changeSelectCUiType(formItemReq) {\n    if (formItemReq.selectedCUiType && formItemReq.selectedCUiType.value === 3) {\n      formItemReq.CRequireAnswer = 1;\n    }\n  }\n  getHouseHoldListByNoticeType(data) {\n    for (let item of data) {\n      if (item.CNoticeType === this.typeContentManagementSalesAccount.CNoticeType) {\n        return item.CHouseHoldList;\n      }\n    }\n    return [];\n  }\n  getKeysWithTrueValue(obj) {\n    return Object.keys(obj).filter(key => obj[key]);\n  }\n  getKeysWithTrueValueJoined(obj) {\n    return Object.keys(obj).filter(key => obj[key]).join('-');\n  }\n  getCRemarkType(selectedCUiType, selectedRemarkType) {\n    if (selectedCUiType && selectedCUiType.value == 3) {\n      return this.getKeysWithTrueValueJoined(selectedRemarkType);\n    }\n  }\n  getStringAfterComma(inputString) {\n    const parts = inputString.split(',');\n    if (parts.length > 1) {\n      return parts[1];\n    } else return \"\";\n  }\n  formatFile(listPictures) {\n    if (listPictures && listPictures.length > 0) {\n      return {\n        Base64String: this.getStringAfterComma(listPictures[0].data) || null,\n        FileExtension: listPictures[0].extension || null,\n        FileName: listPictures[0].CFile.name || listPictures[0].name || null\n      };\n    } else return undefined;\n  }\n  validation() {\n    this.valid.clear();\n    let hasInvalidCUiType = false;\n    let hasInvalidCRequireAnswer = false;\n    let hasInvalidItemName = false;\n    for (const item of this.saveListFormItemReq) {\n      if (!hasInvalidCUiType && !item.CUiType) {\n        hasInvalidCUiType = true;\n      }\n      if (!hasInvalidCRequireAnswer && !item.CRequireAnswer) {\n        hasInvalidCRequireAnswer = true;\n      }\n      if (item.CTotalAnswer && item.CRequireAnswer) {\n        if (item.CRequireAnswer > item.CTotalAnswer || item.CRequireAnswer < 0) {\n          this.valid.addErrorMessage('[必填數量]' + ' <= ' + item.CTotalAnswer + ` (${item.CItemName}) `);\n        }\n      }\n      if (!hasInvalidItemName && !item.CItemName) {\n        hasInvalidItemName = true;\n      }\n    }\n    if (hasInvalidCUiType) {\n      this.valid.addErrorMessage('[前台UI類型]' + ' 必填');\n    }\n    if (hasInvalidCRequireAnswer) {\n      this.valid.addErrorMessage('[必填數量]' + ' 必填且>0');\n    }\n    if (hasInvalidItemName) {\n      this.valid.addErrorMessage('[廚房-廚具-櫃體]' + ' 必填');\n    }\n  }\n  onSubmit() {\n    this.saveListFormItemReq = this.arrListFormItemReq.map(e => {\n      return {\n        CDesignFileUrl: e.CDesignFileUrl ? e.CDesignFileUrl : null,\n        CFile: e.listPictures ? this.formatFile(e.listPictures) : undefined,\n        CFormItemHouseHold: this.getKeysWithTrueValue(e.selectedItems),\n        CFormItemId: e.CFormItemId ? e.CFormItemId : null,\n        CFormID: this.isNew ? null : this.listFormItem.CFormId,\n        CName: e.CName,\n        CPart: e.CPart,\n        CLocation: e.CLocation,\n        CItemName: e.CItemName,\n        //? e.CItemName : `${e.CName}-${e.CPart}-${e.CLocation}`,\n        CRemarkType: e.selectedCUiType.value !== 3 ? null : this.getCRemarkType(e.selectedCUiType, e.selectedRemarkType) || null,\n        CTotalAnswer: e.CTotalAnswer,\n        CRequireAnswer: e.CRequireAnswer,\n        CUiType: e.selectedCUiType.value\n      };\n    });\n    this.validation();\n    if (this.valid.errorMessages.length > 0) {\n      this.message.showErrorMSGs(this.valid.errorMessages);\n      return;\n    }\n    if (this.isNew) {\n      this.createListFormItem();\n    } else {\n      this.saveListFormItem();\n    }\n  }\n  saveListFormItem() {\n    this._formItemService.apiFormItemSaveListFormItemPost$Json({\n      body: this.saveListFormItemReq\n    }).subscribe(res => {\n      if (res.StatusCode == 0) {\n        this.message.showSucessMSG(\"執行成功\");\n        // this.getListFormItem()\n        this.goBack();\n      }\n    });\n  }\n  createListFormItem() {\n    this.creatListFormItem = {\n      CBuildCaseId: this.buildCaseId,\n      CFormItem: this.saveListFormItemReq || null,\n      CFormType: this.typeContentManagementSalesAccount.CFormType\n    };\n    this._formItemService.apiFormItemCreateListFormItemPost$Json({\n      body: this.creatListFormItem\n    }).subscribe(res => {\n      if (res.StatusCode == 0) {\n        this.message.showSucessMSG(\"執行成功\");\n        // this.getListFormItem()\n        this.goBack();\n      }\n    });\n  }\n  createArrayObjectFromArray(a, b) {\n    const c = {};\n    for (const item of a) {\n      const matchingItem = b.find(bItem => bItem.CHousehold === item && bItem.CIsSelect);\n      c[item] = !!matchingItem;\n    }\n    return c;\n  } //[\"House1\", \"House2\", \"House3\"] => [{CHousehold: \"House1\", CIsSelect: true,... }, ... ]\n  getListRegularNoticeFileHouseHold() {\n    this._regularNoticeFileService.apiRegularNoticeFileGetListRegularNoticeFileHouseHoldPost$Json({\n      body: this.buildCaseId\n    }).pipe(tap(res => {\n      if (res.Entries && res.StatusCode == 0) {\n        this.houseHoldList = this.getHouseHoldListByNoticeType(res.Entries);\n        this.getListFormItem();\n      }\n    })).subscribe();\n  }\n  goBack() {\n    this._eventService.push({\n      action: \"GET_BUILDCASE\" /* EEvent.GET_BUILDCASE */,\n      payload: this.buildCaseId\n    });\n    this.location.back();\n  }\n  static {\n    this.ɵfac = function DetailContentManagementSalesAccountComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || DetailContentManagementSalesAccountComponent)(i0.ɵɵdirectiveInject(i1.AllowHelper), i0.ɵɵdirectiveInject(i2.ActivatedRoute), i0.ɵɵdirectiveInject(i3.MessageService), i0.ɵɵdirectiveInject(i4.FormItemService), i0.ɵɵdirectiveInject(i4.RegularNoticeFileService), i0.ɵɵdirectiveInject(i5.UtilityService), i0.ɵɵdirectiveInject(i6.ValidationHelper), i0.ɵɵdirectiveInject(i7.Location), i0.ɵɵdirectiveInject(i4.MaterialService), i0.ɵɵdirectiveInject(i8.EventService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: DetailContentManagementSalesAccountComponent,\n      selectors: [[\"ngx-detail-content-management-sales-account\"]],\n      standalone: true,\n      features: [i0.ɵɵInheritDefinitionFeature, i0.ɵɵStandaloneFeature],\n      decls: 45,\n      vars: 5,\n      consts: [[\"inputFile\", \"\"], [\"noHouseholds\", \"\"], [\"noRemarkOptions\", \"\"], [1, \"min-h-screen\", \"bg-gradient-to-br\", \"from-gray-50\", \"to-gray-100\"], [1, \"shadow-xl\", \"border-0\", \"rounded-xl\", \"overflow-hidden\"], [1, \"bg-white\", \"border-b\", \"border-gray-200\", \"p-6\"], [1, \"flex\", \"items-center\", \"justify-between\"], [1, \"flex\", \"items-center\", \"space-x-4\"], [1, \"w-1\", \"h-8\", \"bg-green-500\", \"rounded-full\"], [1, \"text-2xl\", \"font-bold\", \"text-gray-800\", \"mt-2\"], [1, \"flex\", \"items-center\", \"space-x-2\"], [1, \"px-3\", \"py-1\", \"text-sm\", \"bg-green-100\", \"text-green-800\", \"rounded-full\", \"font-medium\"], [1, \"p-6\", \"bg-gray-50\"], [1, \"space-y-8\"], [1, \"bg-white\", \"rounded-xl\", \"p-6\", \"shadow-sm\", \"border\", \"border-gray-200\"], [1, \"flex\", \"items-center\", \"space-x-3\"], [1, \"w-10\", \"h-10\", \"bg-blue-100\", \"rounded-lg\", \"flex\", \"items-center\", \"justify-center\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-6\", \"h-6\", \"text-blue-600\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"], [1, \"text-xl\", \"font-bold\", \"text-gray-800\"], [1, \"text-sm\", \"text-gray-600\", \"mt-1\"], [4, \"ngFor\", \"ngForOf\"], [1, \"bg-white\", \"border-t\", \"border-gray-200\", \"p-6\", \"sticky\", \"bottom-0\", \"shadow-lg\"], [1, \"flex\", \"items-center\", \"space-x-2\", \"text-sm\", \"text-gray-600\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-4\", \"h-4\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"], [1, \"px-6\", \"py-3\", \"bg-gray-200\", \"hover:bg-gray-300\", \"text-gray-700\", \"font-medium\", \"rounded-lg\", \"transition-all\", \"duration-200\", \"flex\", \"items-center\", \"space-x-2\", \"shadow-sm\", \"hover:shadow-md\", 3, \"click\", \"disabled\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M10 19l-7-7m0 0l7-7m-7 7h18\"], [1, \"px-6\", \"py-3\", \"bg-gradient-to-r\", \"from-green-500\", \"to-green-600\", \"hover:from-green-600\", \"hover:to-green-700\", \"text-white\", \"font-medium\", \"rounded-lg\", \"transition-all\", \"duration-200\", \"flex\", \"items-center\", \"space-x-2\", \"shadow-md\", \"hover:shadow-lg\", \"disabled:opacity-50\", \"disabled:cursor-not-allowed\", 3, \"click\", \"disabled\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M5 13l4 4L19 7\"], [1, \"bg-white\", \"rounded-xl\", \"shadow-lg\", \"border\", \"border-gray-200\", \"overflow-hidden\", \"transition-all\", \"duration-300\", \"hover:shadow-xl\"], [1, \"bg-gradient-to-r\", \"from-blue-50\", \"to-indigo-50\", \"px-6\", \"py-4\", \"border-b\", \"border-gray-200\"], [1, \"w-8\", \"h-8\", \"bg-blue-500\", \"text-white\", \"rounded-full\", \"flex\", \"items-center\", \"justify-center\", \"text-sm\", \"font-bold\"], [1, \"text-lg\", \"font-semibold\", \"text-gray-800\"], [1, \"text-sm\", \"text-gray-600\"], [1, \"px-2\", \"py-1\", \"text-xs\", \"bg-blue-100\", \"text-blue-800\", \"rounded-full\"], [1, \"p-6\"], [1, \"grid\", \"grid-cols-1\", \"lg:grid-cols-4\", \"gap-6\"], [1, \"lg:col-span-1\"], [1, \"space-y-4\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-5\", \"h-5\", \"text-gray-600\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z\"], [1, \"text-sm\", \"font-semibold\", \"text-gray-700\"], [\"class\", \"relative\", 4, \"ngIf\"], [\"class\", \"aspect-square w-full flex flex-col items-center justify-center border-2 border-dashed border-gray-300 rounded-xl bg-gray-50 text-gray-400\", 4, \"ngIf\"], [1, \"lg:col-span-2\"], [1, \"space-y-6\"], [1, \"flex\", \"items-center\", \"space-x-2\", \"mb-4\"], [1, \"group\"], [1, \"block\", \"text-sm\", \"font-medium\", \"text-gray-700\", \"mb-2\", 3, \"for\"], [1, \"flex\", \"items-center\", \"space-x-3\", \"bg-gray-50\", \"p-3\", \"rounded-lg\", \"border\", \"border-gray-200\"], [1, \"text-sm\", \"text-gray-600\", \"font-medium\", \"px-2\", \"py-1\", \"bg-blue-100\", \"rounded-md\", \"whitespace-nowrap\"], [\"type\", \"text\", \"nbInput\", \"\", \"placeholder\", \"\\u4F8B\\u5982\\uFF1A\\u5EDA\\u623F\\u6AAF\\u9762\", 1, \"flex-1\", \"border-0\", \"bg-transparent\", \"focus:ring-2\", \"focus:ring-blue-500\", \"focus:border-transparent\", \"rounded-md\", \"p-2\", 3, \"ngModelChange\", \"id\", \"ngModel\", \"disabled\"], [1, \"relative\"], [\"type\", \"number\", \"nbInput\", \"\", \"placeholder\", \"\\u8F38\\u5165\\u6578\\u91CF\", 1, \"w-full\", \"border-2\", \"border-gray-200\", \"focus:border-blue-500\", \"focus:ring-2\", \"focus:ring-blue-200\", \"rounded-lg\", \"p-3\", \"transition-all\", \"duration-200\", 3, \"ngModelChange\", \"id\", \"ngModel\", \"disabled\"], [1, \"absolute\", \"inset-y-0\", \"right-0\", \"flex\", \"items-center\", \"pr-3\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-5\", \"h-5\", \"text-gray-400\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M7 20l4-16m2 16l4-16M6 9h14M4 15h14\"], [\"placeholder\", \"\\u9078\\u64C7UI\\u985E\\u578B\", 1, \"w-full\", \"border-2\", \"border-gray-200\", \"focus:border-blue-500\", \"rounded-lg\", \"transition-all\", \"duration-200\", 3, \"ngModelChange\", \"selectedChange\", \"id\", \"ngModel\", \"disabled\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M7 4V2a1 1 0 011-1h8a1 1 0 011 1v2m0 0V1a1 1 0 011-1h2a1 1 0 011 1v18a1 1 0 01-1 1H4a1 1 0 01-1-1V4a1 1 0 011-1h2a1 1 0 011 1v3z\"], [1, \"w-full\", \"bg-gradient-to-r\", \"from-blue-500\", \"to-blue-600\", \"hover:from-blue-600\", \"hover:to-blue-700\", \"text-white\", \"font-medium\", \"py-3\", \"px-4\", \"rounded-lg\", \"transition-all\", \"duration-200\", \"flex\", \"items-center\", \"justify-center\", \"space-x-2\", \"shadow-md\", \"hover:shadow-lg\", \"disabled:opacity-50\", \"disabled:cursor-not-allowed\", 3, \"click\", \"disabled\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-5\", \"h-5\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12\"], [\"type\", \"file\", \"accept\", \"image/png, image/gif, image/jpeg\", 1, \"hidden\", 3, \"change\"], [\"class\", \"space-y-3\", 4, \"ngIf\"], [\"class\", \"space-y-2\", 4, \"ngIf\"], [\"class\", \"h-32 w-full flex flex-col items-center justify-center border-2 border-dashed border-gray-300 rounded-lg bg-gray-50 text-gray-400\", 4, \"ngIf\"], [1, \"my-8\"], [1, \"absolute\", \"inset-0\", \"flex\", \"items-center\"], [1, \"w-full\", \"border-t\", \"border-gray-300\"], [1, \"relative\", \"flex\", \"justify-center\", \"text-sm\"], [1, \"px-4\", \"bg-white\", \"text-gray-500\", \"font-medium\"], [1, \"grid\", \"grid-cols-1\", \"lg:grid-cols-2\", \"gap-6\"], [1, \"bg-blue-50\", \"p-4\", \"rounded-lg\", \"border\", \"border-blue-200\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-5\", \"h-5\", \"text-blue-600\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z\"], [1, \"font-semibold\", \"text-blue-800\"], [1, \"space-y-3\"], [\"class\", \"flex items-center cursor-pointer hover:bg-blue-100 p-2 rounded-md transition-colors\", 4, \"ngIf\"], [\"class\", \"grid grid-cols-1 gap-2\", 4, \"ngIf\", \"ngIfElse\"], [\"class\", \"bg-orange-50 p-4 rounded-lg border border-orange-200\", 4, \"ngIf\"], [1, \"aspect-square\", \"w-full\", \"relative\", \"overflow-hidden\", \"rounded-xl\", \"border-2\", \"border-gray-200\", \"cursor-pointer\", \"group\", \"shadow-md\", \"hover:shadow-lg\", \"transition-all\", \"duration-300\", 3, \"click\"], [\"class\", \"w-full h-full object-cover transition-transform duration-500 group-hover:scale-110\", 3, \"src\", 4, \"ngIf\"], [1, \"absolute\", \"inset-0\", \"bg-black\", \"bg-opacity-0\", \"group-hover:bg-opacity-30\", \"transition-all\", \"duration-300\", \"flex\", \"items-center\", \"justify-center\"], [1, \"transform\", \"scale-75\", \"group-hover:scale-100\", \"transition-transform\", \"duration-300\"], [1, \"w-12\", \"h-12\", \"bg-white\", \"bg-opacity-90\", \"rounded-full\", \"flex\", \"items-center\", \"justify-center\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-6\", \"h-6\", \"text-gray-800\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0zM10 7v3m0 0v3m0-3h3m-3 0H7\"], [\"class\", \"absolute left-2 top-1/2 transform -translate-y-1/2 bg-white bg-opacity-95 text-gray-800 rounded-full w-8 h-8 flex items-center justify-center hover:bg-opacity-100 hover:shadow-md transition-all z-10 opacity-0 group-hover:opacity-100\", \"title\", \"\\u4E0A\\u4E00\\u5F35\\u5716\\u7247\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"absolute right-2 top-1/2 transform -translate-y-1/2 bg-white bg-opacity-95 text-gray-800 rounded-full w-8 h-8 flex items-center justify-center hover:bg-opacity-100 hover:shadow-md transition-all z-10 opacity-0 group-hover:opacity-100\", \"title\", \"\\u4E0B\\u4E00\\u5F35\\u5716\\u7247\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"absolute bottom-2 right-2 bg-black bg-opacity-80 text-white text-xs px-2 py-1 rounded-lg backdrop-blur-sm\", 4, \"ngIf\"], [\"class\", \"flex gap-2 mt-3 overflow-x-auto pb-2\", 4, \"ngIf\"], [1, \"w-full\", \"h-full\", \"object-cover\", \"transition-transform\", \"duration-500\", \"group-hover:scale-110\", 3, \"src\"], [\"title\", \"\\u4E0A\\u4E00\\u5F35\\u5716\\u7247\", 1, \"absolute\", \"left-2\", \"top-1/2\", \"transform\", \"-translate-y-1/2\", \"bg-white\", \"bg-opacity-95\", \"text-gray-800\", \"rounded-full\", \"w-8\", \"h-8\", \"flex\", \"items-center\", \"justify-center\", \"hover:bg-opacity-100\", \"hover:shadow-md\", \"transition-all\", \"z-10\", \"opacity-0\", \"group-hover:opacity-100\", 3, \"click\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2.5\", \"d\", \"M15 19l-7-7 7-7\"], [\"title\", \"\\u4E0B\\u4E00\\u5F35\\u5716\\u7247\", 1, \"absolute\", \"right-2\", \"top-1/2\", \"transform\", \"-translate-y-1/2\", \"bg-white\", \"bg-opacity-95\", \"text-gray-800\", \"rounded-full\", \"w-8\", \"h-8\", \"flex\", \"items-center\", \"justify-center\", \"hover:bg-opacity-100\", \"hover:shadow-md\", \"transition-all\", \"z-10\", \"opacity-0\", \"group-hover:opacity-100\", 3, \"click\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2.5\", \"d\", \"M9 5l7 7-7 7\"], [1, \"absolute\", \"bottom-2\", \"right-2\", \"bg-black\", \"bg-opacity-80\", \"text-white\", \"text-xs\", \"px-2\", \"py-1\", \"rounded-lg\", \"backdrop-blur-sm\"], [1, \"flex\", \"gap-2\", \"mt-3\", \"overflow-x-auto\", \"pb-2\"], [\"class\", \"flex-shrink-0 w-12 h-12 border-2 rounded-lg overflow-hidden hover:border-blue-400 transition-all duration-200 cursor-pointer hover:scale-105\", 3, \"border-blue-500\", \"border-gray-300\", \"ring-2\", \"ring-blue-200\", \"title\", \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"flex-shrink-0\", \"w-12\", \"h-12\", \"border-2\", \"rounded-lg\", \"overflow-hidden\", \"hover:border-blue-400\", \"transition-all\", \"duration-200\", \"cursor-pointer\", \"hover:scale-105\", 3, \"click\", \"title\"], [1, \"w-full\", \"h-full\", \"object-cover\", \"transition-transform\", \"hover:scale-110\", 3, \"src\"], [1, \"aspect-square\", \"w-full\", \"flex\", \"flex-col\", \"items-center\", \"justify-center\", \"border-2\", \"border-dashed\", \"border-gray-300\", \"rounded-xl\", \"bg-gray-50\", \"text-gray-400\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-12\", \"h-12\", \"mb-2\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"1.5\", \"d\", \"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z\"], [1, \"text-sm\"], [3, \"value\"], [\"class\", \"bg-gray-50 border border-gray-200 p-3 rounded-lg hover:shadow-md transition-all duration-200\", 4, \"ngFor\", \"ngForOf\"], [1, \"bg-gray-50\", \"border\", \"border-gray-200\", \"p-3\", \"rounded-lg\", \"hover:shadow-md\", \"transition-all\", \"duration-200\"], [1, \"relative\", \"group\"], [1, \"w-full\", \"h-32\", \"object-cover\", \"rounded-lg\", \"mb-3\", \"border\", \"border-gray-200\", 3, \"src\"], [1, \"absolute\", \"inset-0\", \"bg-black\", \"bg-opacity-0\", \"group-hover:bg-opacity-20\", \"transition-all\", \"duration-200\", \"rounded-lg\"], [\"nbInput\", \"\", \"type\", \"text\", \"placeholder\", \"\\u5716\\u7247\\u8AAA\\u660E/\\u6A94\\u540D\", 1, \"w-full\", \"p-2\", \"text-sm\", \"mb-2\", \"border\", \"border-gray-200\", \"rounded-md\", \"focus:ring-2\", \"focus:ring-blue-500\", \"focus:border-transparent\", 3, \"blur\", \"value\", \"disabled\"], [1, \"w-full\", \"bg-red-100\", \"hover:bg-red-200\", \"text-red-700\", \"font-medium\", \"py-2\", \"px-3\", \"rounded-md\", \"transition-colors\", \"duration-200\", \"text-sm\", 3, \"click\", \"disabled\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-4\", \"h-4\", \"inline\", \"mr-1\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16\"], [1, \"space-y-2\"], [1, \"block\", \"text-xs\", \"font-medium\", \"text-gray-600\"], [1, \"w-full\", \"h-32\", \"object-cover\", \"rounded-lg\", \"border\", \"border-gray-200\", 3, \"src\"], [1, \"h-32\", \"w-full\", \"flex\", \"flex-col\", \"items-center\", \"justify-center\", \"border-2\", \"border-dashed\", \"border-gray-300\", \"rounded-lg\", \"bg-gray-50\", \"text-gray-400\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-8\", \"h-8\", \"mb-1\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"1.5\", \"d\", \"M7 4V2a1 1 0 011-1h8a1 1 0 011 1v2m0 0V1a1 1 0 011-1h2a1 1 0 011 1v18a1 1 0 01-1 1H4a1 1 0 01-1-1V4a1 1 0 011-1h2a1 1 0 011 1v3z\"], [1, \"text-xs\"], [1, \"flex\", \"items-center\", \"cursor-pointer\", \"hover:bg-blue-100\", \"p-2\", \"rounded-md\", \"transition-colors\"], [1, \"mr-3\", 3, \"checkedChange\", \"checked\", \"disabled\"], [1, \"font-medium\", \"text-blue-700\"], [1, \"grid\", \"grid-cols-1\", \"gap-2\"], [\"class\", \"flex items-center cursor-pointer hover:bg-blue-100 p-2 rounded-md transition-colors\", 4, \"ngFor\", \"ngForOf\"], [\"value\", \"item\", 1, \"mr-3\", 3, \"checkedChange\", \"checked\", \"disabled\"], [1, \"text-gray-700\"], [1, \"text-center\", \"py-4\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-8\", \"h-8\", \"text-gray-400\", \"mx-auto\", \"mb-2\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"1.5\", \"d\", \"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z\"], [1, \"text-gray-500\", \"text-sm\"], [1, \"bg-orange-50\", \"p-4\", \"rounded-lg\", \"border\", \"border-orange-200\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-5\", \"h-5\", \"text-orange-600\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M7 8h10M7 12h4m1 8l-4-4H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-3l-4 4z\"], [1, \"font-semibold\", \"text-orange-800\"], [\"class\", \"flex items-center cursor-pointer hover:bg-orange-100 p-2 rounded-md transition-colors\", 4, \"ngFor\", \"ngForOf\"], [1, \"flex\", \"items-center\", \"cursor-pointer\", \"hover:bg-orange-100\", \"p-2\", \"rounded-md\", \"transition-colors\"], [\"value\", \"item\", \"class\", \"mr-3\", 3, \"checked\", \"disabled\", \"checkedChange\", 4, \"ngIf\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"1.5\", \"d\", \"M7 8h10M7 12h4m1 8l-4-4H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-3l-4 4z\"], [\"class\", \"fixed inset-0 z-[9999] flex items-center justify-center bg-black bg-opacity-80 backdrop-blur-sm p-4 animate-fadeIn\", \"tabindex\", \"0\", 3, \"click\", \"keydown\", 4, \"ngIf\"], [\"tabindex\", \"0\", 1, \"fixed\", \"inset-0\", \"z-[9999]\", \"flex\", \"items-center\", \"justify-center\", \"bg-black\", \"bg-opacity-80\", \"backdrop-blur-sm\", \"p-4\", \"animate-fadeIn\", 3, \"click\", \"keydown\"], [\"title\", \"\\u95DC\\u9589\\u5716\\u7247\\u6AA2\\u8996 (\\u6309 ESC \\u9375)\", 1, \"fixed\", \"top-6\", \"right-6\", \"z-[60]\", \"bg-red-500\", \"bg-opacity-95\", \"hover:bg-red-600\", \"hover:bg-opacity-100\", \"text-white\", \"rounded-full\", \"w-14\", \"h-14\", \"flex\", \"items-center\", \"justify-center\", \"transition-all\", \"duration-200\", \"shadow-2xl\", \"hover:shadow-3xl\", \"hover:scale-110\", \"group\", 3, \"click\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-7\", \"h-7\", \"group-hover:rotate-90\", \"transition-transform\", \"duration-200\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2.5\", \"d\", \"M6 18L18 6M6 6l12 12\"], [1, \"relative\", \"max-w-7xl\", \"max-h-full\", \"w-full\", \"h-full\", \"flex\", \"items-center\", \"justify-center\", \"animate-scaleIn\", 3, \"click\"], [1, \"relative\", \"max-w-full\", \"max-h-full\", \"bg-white\", \"rounded-2xl\", \"p-2\", \"shadow-2xl\"], [\"class\", \"animate-fadeIn\", 3, \"src\", 4, \"ngIf\"], [\"class\", \"absolute left-4 top-1/2 transform -translate-y-1/2 bg-white bg-opacity-95 hover:bg-opacity-100 text-gray-800 rounded-full w-16 h-16 flex items-center justify-center transition-all duration-200 shadow-lg hover:shadow-xl hover:scale-105 z-[55] group\", \"title\", \"\\u4E0A\\u4E00\\u5F35\\u5716\\u7247 (\\u6309 \\u2190 \\u9375)\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"absolute right-4 top-1/2 transform -translate-y-1/2 bg-white bg-opacity-95 hover:bg-opacity-100 text-gray-800 rounded-full w-16 h-16 flex items-center justify-center transition-all duration-200 shadow-lg hover:shadow-xl hover:scale-105 z-[55] group\", \"title\", \"\\u4E0B\\u4E00\\u5F35\\u5716\\u7247 (\\u6309 \\u2192 \\u9375)\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"absolute bottom-24 left-1/2 transform -translate-x-1/2 bg-black bg-opacity-90 text-white px-6 py-3 rounded-full backdrop-blur-sm shadow-lg\", 4, \"ngIf\"], [1, \"absolute\", \"bottom-6\", \"right-6\", \"bg-gradient-to-r\", \"from-blue-600\", \"to-blue-700\", \"text-white\", \"px-4\", \"py-3\", \"rounded-lg\", \"text-sm\", \"backdrop-blur-sm\", \"shadow-lg\"], [1, \"font-medium\"], [\"class\", \"absolute bottom-32 left-1/2 transform -translate-x-1/2 bg-black bg-opacity-80 backdrop-blur-md p-4 rounded-xl shadow-2xl max-w-full\", 4, \"ngIf\"], [1, \"animate-fadeIn\", 3, \"src\"], [\"title\", \"\\u4E0A\\u4E00\\u5F35\\u5716\\u7247 (\\u6309 \\u2190 \\u9375)\", 1, \"absolute\", \"left-4\", \"top-1/2\", \"transform\", \"-translate-y-1/2\", \"bg-white\", \"bg-opacity-95\", \"hover:bg-opacity-100\", \"text-gray-800\", \"rounded-full\", \"w-16\", \"h-16\", \"flex\", \"items-center\", \"justify-center\", \"transition-all\", \"duration-200\", \"shadow-lg\", \"hover:shadow-xl\", \"hover:scale-105\", \"z-[55]\", \"group\", 3, \"click\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-8\", \"h-8\", \"group-hover:-translate-x-1\", \"transition-transform\", \"duration-200\"], [\"title\", \"\\u4E0B\\u4E00\\u5F35\\u5716\\u7247 (\\u6309 \\u2192 \\u9375)\", 1, \"absolute\", \"right-4\", \"top-1/2\", \"transform\", \"-translate-y-1/2\", \"bg-white\", \"bg-opacity-95\", \"hover:bg-opacity-100\", \"text-gray-800\", \"rounded-full\", \"w-16\", \"h-16\", \"flex\", \"items-center\", \"justify-center\", \"transition-all\", \"duration-200\", \"shadow-lg\", \"hover:shadow-xl\", \"hover:scale-105\", \"z-[55]\", \"group\", 3, \"click\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-8\", \"h-8\", \"group-hover:translate-x-1\", \"transition-transform\", \"duration-200\"], [1, \"absolute\", \"bottom-24\", \"left-1/2\", \"transform\", \"-translate-x-1/2\", \"bg-black\", \"bg-opacity-90\", \"text-white\", \"px-6\", \"py-3\", \"rounded-full\", \"backdrop-blur-sm\", \"shadow-lg\"], [1, \"font-medium\", \"text-lg\"], [1, \"absolute\", \"bottom-32\", \"left-1/2\", \"transform\", \"-translate-x-1/2\", \"bg-black\", \"bg-opacity-80\", \"backdrop-blur-md\", \"p-4\", \"rounded-xl\", \"shadow-2xl\", \"max-w-full\"], [1, \"flex\", \"gap-3\", \"overflow-x-auto\", \"max-w-[80vw]\"], [\"class\", \"flex-shrink-0 w-20 h-20 border-3 rounded-xl overflow-hidden hover:border-white transition-all duration-200 hover:scale-105 group\", 3, \"border-white\", \"border-gray-400\", \"ring-3\", \"ring-white\", \"ring-opacity-50\", \"title\", \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"flex-shrink-0\", \"w-20\", \"h-20\", \"border-3\", \"rounded-xl\", \"overflow-hidden\", \"hover:border-white\", \"transition-all\", \"duration-200\", \"hover:scale-105\", \"group\", 3, \"click\", \"title\"], [1, \"w-full\", \"h-full\", \"object-cover\", \"group-hover:scale-110\", \"transition-transform\", \"duration-200\", 3, \"src\"]],\n      template: function DetailContentManagementSalesAccountComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 3)(1, \"nb-card\", 4)(2, \"nb-card-header\", 5)(3, \"div\", 6)(4, \"div\", 7);\n          i0.ɵɵelement(5, \"div\", 8);\n          i0.ɵɵelementStart(6, \"div\");\n          i0.ɵɵelement(7, \"ngx-breadcrumb\");\n          i0.ɵɵelementStart(8, \"h2\", 9);\n          i0.ɵɵtext(9, \"\\u9078\\u6A23\\u5167\\u5BB9\\u7BA1\\u7406 - \\u92B7\\u552E\\u6236\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(10, \"div\", 10)(11, \"span\", 11);\n          i0.ɵɵtext(12, \" \\u7368\\u7ACB\\u9078\\u6A23\\u7BA1\\u7406 \");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(13, \"nb-card-body\", 12)(14, \"div\", 13)(15, \"div\", 14)(16, \"div\", 15)(17, \"div\", 16);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(18, \"svg\", 17);\n          i0.ɵɵelement(19, \"path\", 18);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(20, \"div\")(21, \"h3\", 19);\n          i0.ɵɵtext(22, \"\\u985E\\u578B - \\u7368\\u7ACB\\u9078\\u6A23\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(23, \"p\", 20);\n          i0.ɵɵtext(24, \"\\u7BA1\\u7406\\u9078\\u6A23\\u9805\\u76EE\\u7684\\u8A73\\u7D30\\u8A2D\\u5B9A\\u8207\\u914D\\u7F6E\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵtemplate(25, DetailContentManagementSalesAccountComponent_ng_container_25_Template, 91, 32, \"ng-container\", 21);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(26, \"nb-card-footer\", 22)(27, \"div\", 6)(28, \"div\", 23);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(29, \"svg\", 24);\n          i0.ɵɵelement(30, \"path\", 25);\n          i0.ɵɵelementEnd();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(31, \"span\");\n          i0.ɵɵtext(32);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(33, \"div\", 7)(34, \"button\", 26);\n          i0.ɵɵlistener(\"click\", function DetailContentManagementSalesAccountComponent_Template_button_click_34_listener() {\n            return ctx.goBack();\n          });\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(35, \"svg\", 24);\n          i0.ɵɵelement(36, \"path\", 27);\n          i0.ɵɵelementEnd();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(37, \"span\");\n          i0.ɵɵtext(38, \"\\u53D6\\u6D88\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(39, \"button\", 28);\n          i0.ɵɵlistener(\"click\", function DetailContentManagementSalesAccountComponent_Template_button_click_39_listener() {\n            return ctx.onSubmit();\n          });\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(40, \"svg\", 24);\n          i0.ɵɵelement(41, \"path\", 29);\n          i0.ɵɵelementEnd();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(42, \"span\");\n          i0.ɵɵtext(43, \"\\u5132\\u5B58\\u8B8A\\u66F4\");\n          i0.ɵɵelementEnd()()()()()()();\n          i0.ɵɵtemplate(44, DetailContentManagementSalesAccountComponent_ng_container_44_Template, 2, 1, \"ng-container\", 21);\n        }\n        if (rf & 2) {\n          let tmp_2_0;\n          let tmp_3_0;\n          i0.ɵɵadvance(25);\n          i0.ɵɵproperty(\"ngForOf\", ctx.arrListFormItemReq);\n          i0.ɵɵadvance(7);\n          i0.ɵɵtextInterpolate1(\"\\u5171 \", ctx.arrListFormItemReq.length || 0, \" \\u500B\\u9078\\u6A23\\u9805\\u76EE\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"disabled\", (tmp_2_0 = ctx.listFormItem.CIsLock) !== null && tmp_2_0 !== undefined ? tmp_2_0 : false);\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"disabled\", (tmp_3_0 = ctx.listFormItem.CIsLock) !== null && tmp_3_0 !== undefined ? tmp_3_0 : false);\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"ngForOf\", ctx.arrListFormItemReq);\n        }\n      },\n      dependencies: [CommonModule, i7.NgForOf, i7.NgIf, SharedModule, i9.DefaultValueAccessor, i9.NumberValueAccessor, i9.NgControlStatus, i9.NgModel, i10.NbCardComponent, i10.NbCardBodyComponent, i10.NbCardFooterComponent, i10.NbCardHeaderComponent, i10.NbCheckboxComponent, i10.NbInputDirective, i10.NbSelectComponent, i10.NbOptionComponent, i11.BreadcrumbComponent, i12.BaseFilePipe, NbCheckboxModule, Base64ImagePipe],\n      styles: [\"[_nghost-%COMP%] {\\n  display: block;\\n  min-height: 100vh;\\n}\\n\\n.page-background[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);\\n  animation: _ngcontent-%COMP%_gradientShift 20s ease infinite;\\n  background-size: 400% 400%;\\n}\\n\\n@keyframes _ngcontent-%COMP%_gradientShift {\\n  0% {\\n    background-position: 0% 50%;\\n  }\\n  50% {\\n    background-position: 100% 50%;\\n  }\\n  100% {\\n    background-position: 0% 50%;\\n  }\\n}\\nnb-card[_ngcontent-%COMP%] {\\n  border-radius: 1rem !important;\\n  overflow: hidden;\\n  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04) !important;\\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\n}\\nnb-card[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-2px);\\n  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25) !important;\\n}\\n\\n.item-card[_ngcontent-%COMP%] {\\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\n}\\n.item-card[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-4px);\\n  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.15);\\n}\\n\\ninput[nbInput][_ngcontent-%COMP%], nb-select[_ngcontent-%COMP%] {\\n  transition: all 0.2s ease-in-out !important;\\n}\\ninput[nbInput][_ngcontent-%COMP%]:focus, nb-select[_ngcontent-%COMP%]:focus {\\n  transform: translateY(-1px);\\n  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15) !important;\\n}\\n\\n.btn-enhanced[_ngcontent-%COMP%] {\\n  position: relative;\\n  overflow: hidden;\\n}\\n.btn-enhanced[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 0;\\n  left: -100%;\\n  width: 100%;\\n  height: 100%;\\n  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);\\n  transition: left 0.5s;\\n}\\n.btn-enhanced[_ngcontent-%COMP%]:hover::before {\\n  left: 100%;\\n}\\n\\n.image-container[_ngcontent-%COMP%] {\\n  position: relative;\\n  overflow: hidden;\\n}\\n.image-container[_ngcontent-%COMP%]::after {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  background: linear-gradient(45deg, rgba(59, 130, 246, 0.1), rgba(147, 51, 234, 0.1));\\n  opacity: 0;\\n  transition: opacity 0.3s ease;\\n}\\n.image-container[_ngcontent-%COMP%]:hover::after {\\n  opacity: 1;\\n}\\n\\n.status-badge[_ngcontent-%COMP%] {\\n  position: relative;\\n}\\n.status-badge[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  background: inherit;\\n  border-radius: inherit;\\n  opacity: 0.1;\\n  transform: scale(0);\\n  transition: transform 0.3s ease;\\n}\\n.status-badge[_ngcontent-%COMP%]:hover::before {\\n  transform: scale(1.1);\\n}\\n\\n.responsive-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  gap: 1.5rem;\\n}\\n@media (min-width: 768px) {\\n  .responsive-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\\n  }\\n}\\n@media (min-width: 1024px) {\\n  .responsive-grid[_ngcontent-%COMP%] {\\n    gap: 2rem;\\n  }\\n}\\n\\n.image-carousel[_ngcontent-%COMP%]:hover   .carousel-controls[_ngcontent-%COMP%] {\\n  opacity: 1;\\n}\\n.image-carousel[_ngcontent-%COMP%]   .carousel-controls[_ngcontent-%COMP%] {\\n  opacity: 0;\\n  transition: opacity 0.3s ease;\\n}\\n\\n.carousel-btn[_ngcontent-%COMP%] {\\n  transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);\\n  -webkit-backdrop-filter: blur(8px);\\n          backdrop-filter: blur(8px);\\n  border: 1px solid rgba(255, 255, 255, 0.2);\\n}\\n.carousel-btn[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-50%) scale(1.15);\\n  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);\\n  -webkit-backdrop-filter: blur(12px);\\n          backdrop-filter: blur(12px);\\n}\\n.carousel-btn[_ngcontent-%COMP%]:active {\\n  transform: translateY(-50%) scale(0.95);\\n}\\n@media (max-width: 768px) {\\n  .carousel-btn[_ngcontent-%COMP%] {\\n    opacity: 1 !important;\\n    width: 2.5rem;\\n    height: 2.5rem;\\n  }\\n  .carousel-btn[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%] {\\n    width: 1rem;\\n    height: 1rem;\\n  }\\n}\\n\\n.thumbnail-navigation[_ngcontent-%COMP%] {\\n  scrollbar-width: thin;\\n}\\n.thumbnail-navigation[_ngcontent-%COMP%]::-webkit-scrollbar {\\n  height: 6px;\\n}\\n.thumbnail-navigation[_ngcontent-%COMP%]::-webkit-scrollbar-track {\\n  background: rgba(0, 0, 0, 0.1);\\n  border-radius: 3px;\\n}\\n.thumbnail-navigation[_ngcontent-%COMP%]::-webkit-scrollbar-thumb {\\n  background: linear-gradient(90deg, #3b82f6, #8b5cf6);\\n  border-radius: 3px;\\n}\\n.thumbnail-navigation[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover {\\n  background: linear-gradient(90deg, #2563eb, #7c3aed);\\n}\\n\\n.image-modal[_ngcontent-%COMP%] {\\n  -webkit-backdrop-filter: blur(12px);\\n          backdrop-filter: blur(12px);\\n}\\n.image-modal[_ngcontent-%COMP%]   .modal-content[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_modalFadeIn 0.4s cubic-bezier(0.4, 0, 0.2, 1);\\n}\\n.image-modal[_ngcontent-%COMP%]   .modal-image[_ngcontent-%COMP%] {\\n  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.5);\\n  transition: transform 0.3s ease;\\n  border-radius: 0.75rem;\\n}\\n.image-modal[_ngcontent-%COMP%]   .modal-controls[_ngcontent-%COMP%] {\\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\n}\\n.image-modal[_ngcontent-%COMP%]   .modal-controls[_ngcontent-%COMP%]:hover {\\n  transform: scale(1.1);\\n}\\n.image-modal[_ngcontent-%COMP%]   .modal-thumbnails[_ngcontent-%COMP%] {\\n  max-height: 20vh;\\n}\\n.image-modal[_ngcontent-%COMP%]   .modal-thumbnails[_ngcontent-%COMP%]::-webkit-scrollbar {\\n  height: 8px;\\n}\\n.image-modal[_ngcontent-%COMP%]   .modal-thumbnails[_ngcontent-%COMP%]::-webkit-scrollbar-track {\\n  background: rgba(255, 255, 255, 0.1);\\n  border-radius: 4px;\\n}\\n.image-modal[_ngcontent-%COMP%]   .modal-thumbnails[_ngcontent-%COMP%]::-webkit-scrollbar-thumb {\\n  background: linear-gradient(90deg, rgba(255, 255, 255, 0.3), rgba(255, 255, 255, 0.5));\\n  border-radius: 4px;\\n}\\n.image-modal[_ngcontent-%COMP%]   .modal-thumbnails[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover {\\n  background: linear-gradient(90deg, rgba(255, 255, 255, 0.5), rgba(255, 255, 255, 0.7));\\n}\\n\\n.modal-nav-btn[_ngcontent-%COMP%] {\\n  -webkit-backdrop-filter: blur(8px);\\n          backdrop-filter: blur(8px);\\n  transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);\\n  border: 2px solid rgba(255, 255, 255, 0.2);\\n  z-index: 9995 !important;\\n}\\n.modal-nav-btn[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-50%) scale(1.1);\\n  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);\\n  border-color: rgba(255, 255, 255, 0.4);\\n}\\n.modal-nav-btn[_ngcontent-%COMP%]:active {\\n  transform: translateY(-50%) scale(0.95);\\n}\\n.modal-nav-btn[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%] {\\n  transition: transform 0.2s ease;\\n}\\n.modal-nav-btn[_ngcontent-%COMP%]:hover   svg[_ngcontent-%COMP%] {\\n  transform: scale(1.1);\\n}\\n@media (max-width: 768px) {\\n  .modal-nav-btn[_ngcontent-%COMP%] {\\n    width: 3.5rem;\\n    height: 3.5rem;\\n  }\\n  .modal-nav-btn[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%] {\\n    width: 1.5rem;\\n    height: 1.5rem;\\n  }\\n}\\n\\n.modal-close-btn[_ngcontent-%COMP%] {\\n  z-index: 9999 !important;\\n  pointer-events: auto;\\n  box-shadow: 0 8px 25px rgba(239, 68, 68, 0.4), 0 0 0 2px rgba(255, 255, 255, 0.1) !important;\\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\n}\\n.modal-close-btn[_ngcontent-%COMP%]:hover {\\n  box-shadow: 0 12px 30px rgba(239, 68, 68, 0.5), 0 0 0 3px rgba(255, 255, 255, 0.2) !important;\\n  transform: scale(1.1) rotate(90deg);\\n}\\n.modal-close-btn[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%] {\\n  transition: transform 0.3s ease;\\n}\\n@media (max-width: 768px) {\\n  .modal-close-btn[_ngcontent-%COMP%] {\\n    width: 3.5rem;\\n    height: 3.5rem;\\n    top: 1rem;\\n    right: 1rem;\\n  }\\n  .modal-close-btn[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%] {\\n    width: 1.5rem;\\n    height: 1.5rem;\\n  }\\n}\\n\\n@keyframes _ngcontent-%COMP%_modalFadeIn {\\n  0% {\\n    opacity: 0;\\n    transform: scale(0.9) translateY(20px);\\n  }\\n  100% {\\n    opacity: 1;\\n    transform: scale(1) translateY(0);\\n  }\\n}\\n@keyframes _ngcontent-%COMP%_slideInFromLeft {\\n  0% {\\n    opacity: 0;\\n    transform: translateX(-20px);\\n  }\\n  100% {\\n    opacity: 1;\\n    transform: translateX(0);\\n  }\\n}\\n@keyframes _ngcontent-%COMP%_slideInFromRight {\\n  0% {\\n    opacity: 0;\\n    transform: translateX(20px);\\n  }\\n  100% {\\n    opacity: 1;\\n    transform: translateX(0);\\n  }\\n}\\n@keyframes _ngcontent-%COMP%_fadeInUp {\\n  0% {\\n    opacity: 0;\\n    transform: translateY(20px);\\n  }\\n  100% {\\n    opacity: 1;\\n    transform: translateY(0);\\n  }\\n}\\n.animate-slide-in-left[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_slideInFromLeft 0.5s ease-out;\\n}\\n\\n.animate-slide-in-right[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_slideInFromRight 0.5s ease-out;\\n}\\n\\n.animate-fade-in-up[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_fadeInUp 0.6s ease-out;\\n}\\n\\n@media (max-width: 768px) {\\n  .image-modal[_ngcontent-%COMP%]   .modal-content[_ngcontent-%COMP%] {\\n    padding: 1rem;\\n    margin: 1rem;\\n  }\\n  .image-modal[_ngcontent-%COMP%]   .modal-controls[_ngcontent-%COMP%] {\\n    width: 3rem;\\n    height: 3rem;\\n  }\\n  .image-modal[_ngcontent-%COMP%]   .modal-controls[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%] {\\n    width: 1.25rem;\\n    height: 1.25rem;\\n  }\\n  .image-modal[_ngcontent-%COMP%]   .modal-thumbnails[_ngcontent-%COMP%] {\\n    max-height: 15vh;\\n  }\\n  .image-modal[_ngcontent-%COMP%]   .modal-thumbnails[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n    width: 3.5rem;\\n    height: 3.5rem;\\n  }\\n  .item-card[_ngcontent-%COMP%] {\\n    margin-bottom: 1rem;\\n  }\\n  .responsive-grid[_ngcontent-%COMP%] {\\n    gap: 1rem;\\n  }\\n}\\n@media (prefers-contrast: high) {\\n  .carousel-btn[_ngcontent-%COMP%], \\n   .modal-nav-btn[_ngcontent-%COMP%] {\\n    border: 2px solid currentColor;\\n    background: rgba(255, 255, 255, 0.95);\\n  }\\n  .modal-close-btn[_ngcontent-%COMP%] {\\n    border: 2px solid currentColor;\\n  }\\n}\\n@media (prefers-reduced-motion: reduce) {\\n  *[_ngcontent-%COMP%], \\n   *[_ngcontent-%COMP%]::before, \\n   *[_ngcontent-%COMP%]::after {\\n    animation-duration: 0.01ms !important;\\n    animation-iteration-count: 1 !important;\\n    transition-duration: 0.01ms !important;\\n  }\\n}\\n@media (prefers-color-scheme: dark) {\\n  .page-background[_ngcontent-%COMP%] {\\n    background: linear-gradient(135deg, #1f2937 0%, #111827 100%);\\n  }\\n  .item-card[_ngcontent-%COMP%] {\\n    background: #374151;\\n    border-color: #4b5563;\\n  }\\n  .carousel-btn[_ngcontent-%COMP%], \\n   .modal-nav-btn[_ngcontent-%COMP%] {\\n    background: rgba(31, 41, 55, 0.9);\\n    border-color: rgba(156, 163, 175, 0.3);\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;base64,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 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\", \"@keyframes _ngcontent-%COMP%_fadeIn {\\n    from { opacity: 0; }\\n    to { opacity: 1; }\\n  }\\n  \\n  @keyframes _ngcontent-%COMP%_scaleIn {\\n    from { \\n      opacity: 0;\\n      transform: scale(0.95);\\n    }\\n    to { \\n      opacity: 1;\\n      transform: scale(1);\\n    }\\n  }\\n  \\n  .animate-fadeIn[_ngcontent-%COMP%] {\\n    animation: _ngcontent-%COMP%_fadeIn 0.3s ease-out;\\n  }\\n  \\n  .animate-scaleIn[_ngcontent-%COMP%] {\\n    animation: _ngcontent-%COMP%_scaleIn 0.3s ease-out;\\n  }\\n  \\n  .border-3[_ngcontent-%COMP%] {\\n    border-width: 3px;\\n  }\\n  \\n  .ring-3[_ngcontent-%COMP%] {\\n    --tw-ring-width: 3px;\\n  }\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "NbCheckboxModule", "tap", "SharedModule", "BaseComponent", "EEvent", "Base64ImagePipe", "i0", "ɵɵelement", "ɵɵproperty", "ɵɵpipeBind1", "ctx_r3", "getCurrentImage", "formItemReq_r3", "ɵɵsanitizeUrl", "ɵɵelementStart", "ɵɵlistener", "DetailContentManagementSalesAccountComponent_ng_container_25_div_24_button_8_Template_button_click_0_listener", "$event", "ɵɵrestoreView", "_r5", "ɵɵnextContext", "$implicit", "prevImage", "ɵɵresetView", "stopPropagation", "ɵɵelementEnd", "DetailContentManagementSalesAccountComponent_ng_container_25_div_24_button_9_Template_button_click_0_listener", "_r6", "nextImage", "ɵɵtext", "ɵɵadvance", "ɵɵtextInterpolate2", "currentImageIndex", "CMatrialUrl", "length", "DetailContentManagementSalesAccountComponent_ng_container_25_div_24_div_11_button_1_Template_button_click_0_listener", "i_r8", "_r7", "index", "openImageModal", "ɵɵclassProp", "imageUrl_r9", "ɵɵtemplate", "DetailContentManagementSalesAccountComponent_ng_container_25_div_24_div_11_button_1_Template", "DetailContentManagementSalesAccountComponent_ng_container_25_div_24_Template_div_click_1_listener", "_r2", "DetailContentManagementSalesAccountComponent_ng_container_25_div_24_img_2_Template", "DetailContentManagementSalesAccountComponent_ng_container_25_div_24_button_8_Template", "DetailContentManagementSalesAccountComponent_ng_container_25_div_24_button_9_Template", "DetailContentManagementSalesAccountComponent_ng_container_25_div_24_div_10_Template", "DetailContentManagementSalesAccountComponent_ng_container_25_div_24_div_11_Template", "case_r10", "ɵɵtextInterpolate1", "label", "DetailContentManagementSalesAccountComponent_ng_container_25_div_68_div_1_Template_input_blur_4_listener", "i_r13", "_r12", "renameFile", "DetailContentManagementSalesAccountComponent_ng_container_25_div_68_div_1_Template_button_click_5_listener", "picture_r14", "removeImage", "id", "data", "name", "tmp_11_0", "listFormItem", "CIsLock", "undefined", "tmp_12_0", "DetailContentManagementSalesAccountComponent_ng_container_25_div_68_div_1_Template", "listPictures", "CDesignFileUrl", "ɵɵtwoWayListener", "DetailContentManagementSalesAccountComponent_ng_container_25_label_86_Template_nb_checkbox_checkedChange_1_listener", "_r15", "ɵɵtwoWayBindingSet", "allSelected", "onCheckAllChange", "ɵɵtwoWayProperty", "DetailContentManagementSalesAccountComponent_ng_container_25_div_87_label_1_Template_nb_checkbox_checkedChange_1_listener", "item_r17", "_r16", "selectedItems", "onCheckboxHouseHoldListChange", "ɵɵtextInterpolate", "DetailContentManagementSalesAccountComponent_ng_container_25_div_87_label_1_Template", "houseHoldList", "DetailContentManagementSalesAccountComponent_ng_container_25_div_90_div_6_label_1_nb_checkbox_1_Template_nb_checkbox_checkedChange_0_listener", "_r18", "remark_r19", "selectedRemarkType", "onCheckboxRemarkChange", "DetailContentManagementSalesAccountComponent_ng_container_25_div_90_div_6_label_1_nb_checkbox_1_Template", "DetailContentManagementSalesAccountComponent_ng_container_25_div_90_div_6_label_1_Template", "CRemarkTypeOptions", "DetailContentManagementSalesAccountComponent_ng_container_25_div_90_div_6_Template", "DetailContentManagementSalesAccountComponent_ng_container_25_div_90_ng_template_7_Template", "ɵɵtemplateRefExtractor", "noRemarkOptions_r20", "ɵɵelementContainerStart", "DetailContentManagementSalesAccountComponent_ng_container_25_div_24_Template", "DetailContentManagementSalesAccountComponent_ng_container_25_div_25_Template", "DetailContentManagementSalesAccountComponent_ng_container_25_Template_input_ngModelChange_40_listener", "_r1", "CItemName", "DetailContentManagementSalesAccountComponent_ng_container_25_Template_input_ngModelChange_45_listener", "CRequireAnswer", "DetailContentManagementSalesAccountComponent_ng_container_25_Template_nb_select_ngModelChange_52_listener", "selectedCUiType", "DetailContentManagementSalesAccountComponent_ng_container_25_Template_nb_select_selectedChange_52_listener", "changeSelectCUiType", "DetailContentManagementSalesAccountComponent_ng_container_25_nb_option_53_Template", "DetailContentManagementSalesAccountComponent_ng_container_25_Template_button_click_61_listener", "inputFile_r11", "ɵɵreference", "click", "DetailContentManagementSalesAccountComponent_ng_container_25_Template_input_change_66_listener", "detectFiles", "DetailContentManagementSalesAccountComponent_ng_container_25_div_68_Template", "DetailContentManagementSalesAccountComponent_ng_container_25_div_69_Template", "DetailContentManagementSalesAccountComponent_ng_container_25_div_70_Template", "DetailContentManagementSalesAccountComponent_ng_container_25_label_86_Template", "DetailContentManagementSalesAccountComponent_ng_container_25_div_87_Template", "DetailContentManagementSalesAccountComponent_ng_container_25_ng_template_88_Template", "DetailContentManagementSalesAccountComponent_ng_container_25_div_90_Template", "idx_r21", "ɵɵtextInterpolate3", "CName", "<PERSON>art", "CLocation", "tmp_15_0", "value", "tmp_19_0", "tmp_23_0", "CUiTypeOptions", "noHouseholds_r22", "formItemReq_r24", "DetailContentManagementSalesAccountComponent_ng_container_44_div_1_button_7_Template_button_click_0_listener", "_r25", "prevImageModal", "DetailContentManagementSalesAccountComponent_ng_container_44_div_1_button_8_Template_button_click_0_listener", "_r26", "nextImageModal", "DetailContentManagementSalesAccountComponent_ng_container_44_div_1_div_16_button_2_Template_button_click_0_listener", "i_r28", "_r27", "imageUrl_r29", "DetailContentManagementSalesAccountComponent_ng_container_44_div_1_div_16_button_2_Template", "DetailContentManagementSalesAccountComponent_ng_container_44_div_1_Template_div_click_0_listener", "_r23", "closeImageModal", "DetailContentManagementSalesAccountComponent_ng_container_44_div_1_Template_div_keydown_0_listener", "onKeydown", "DetailContentManagementSalesAccountComponent_ng_container_44_div_1_Template_button_click_1_listener", "DetailContentManagementSalesAccountComponent_ng_container_44_div_1_Template_div_click_4_listener", "DetailContentManagementSalesAccountComponent_ng_container_44_div_1_img_6_Template", "DetailContentManagementSalesAccountComponent_ng_container_44_div_1_button_7_Template", "DetailContentManagementSalesAccountComponent_ng_container_44_div_1_button_8_Template", "DetailContentManagementSalesAccountComponent_ng_container_44_div_1_div_9_Template", "DetailContentManagementSalesAccountComponent_ng_container_44_div_1_div_16_Template", "DetailContentManagementSalesAccountComponent_ng_container_44_div_1_Template", "isModalOpen", "DetailContentManagementSalesAccountComponent", "constructor", "_allow", "route", "message", "_formItemService", "_regularNoticeFileService", "_utilityService", "valid", "location", "_materialService", "_eventService", "typeContentManagementSalesAccount", "CFormType", "CNoticeType", "isNew", "ngOnInit", "paramMap", "subscribe", "params", "idParam", "get", "buildCaseId", "getListRegularNoticeFileHouseHold", "console", "error", "showErrorMSG", "goBack", "ngOnDestroy", "document", "body", "style", "overflow", "getItemByValue", "options", "item", "event", "formItemReq_", "file", "target", "files", "reader", "FileReader", "readAsDataURL", "onload", "base64Str", "result", "Date", "getTime", "split", "extension", "getFileExtension", "CFile", "push", "pictureId", "filter", "x", "blob", "slice", "size", "type", "newFile", "File", "formItemReq", "imageIndex", "key", "preventDefault", "checked", "for<PERSON>ach", "every", "createRemarkObject", "CRemarkType", "remarkObject", "option", "remarkTypes", "includes", "mergeItems", "items", "map", "Map", "has", "existing", "count", "set", "Array", "from", "values", "CTotalAnswer", "getMaterialList", "apiMaterialGetMaterialListPost$Json", "CBuildCaseId", "CPagi", "pipe", "res", "Entries", "StatusCode", "arrListFormItemReq", "o", "CFormItemHouseHold", "CFormId", "CUiType", "CPicture", "getListFormItem", "apiFormItemGetListFormItemPost$Json", "CIsPaging", "formItems", "CFirstMatrialUrl", "CFormItemId", "tblFormItemHouseholds", "createArrayObjectFromArray", "getHouseHoldListByNoticeType", "CHouseHoldList", "getKeysWithTrueValue", "obj", "Object", "keys", "getKeysWithTrueValueJoined", "join", "getCRemarkType", "getStringAfterComma", "inputString", "parts", "formatFile", "Base64String", "FileExtension", "FileName", "validation", "clear", "hasInvalidCUiType", "hasInvalidCRequireAnswer", "hasInvalidItemName", "saveListFormItemReq", "addErrorMessage", "onSubmit", "e", "CFormID", "errorMessages", "showErrorMSGs", "createListFormItem", "saveListFormItem", "apiFormItemSaveListFormItemPost$Json", "showSucessMSG", "creatListFormItem", "CFormItem", "apiFormItemCreateListFormItemPost$Json", "a", "b", "c", "matchingItem", "find", "bItem", "CHousehold", "CIsSelect", "apiRegularNoticeFileGetListRegularNoticeFileHouseHoldPost$Json", "action", "payload", "back", "ɵɵdirectiveInject", "i1", "AllowHelper", "i2", "ActivatedRoute", "i3", "MessageService", "i4", "FormItemService", "RegularNoticeFileService", "i5", "UtilityService", "i6", "ValidationHelper", "i7", "Location", "MaterialService", "i8", "EventService", "selectors", "standalone", "features", "ɵɵInheritDefinitionFeature", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "DetailContentManagementSalesAccountComponent_Template", "rf", "ctx", "DetailContentManagementSalesAccountComponent_ng_container_25_Template", "DetailContentManagementSalesAccountComponent_Template_button_click_34_listener", "DetailContentManagementSalesAccountComponent_Template_button_click_39_listener", "DetailContentManagementSalesAccountComponent_ng_container_44_Template", "tmp_2_0", "tmp_3_0", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "i9", "DefaultValueAccessor", "NumberValueAccessor", "NgControlStatus", "NgModel", "i10", "NbCardComponent", "NbCardBodyComponent", "NbCardFooterComponent", "NbCardHeaderComponent", "NbCheckboxComponent", "NbInputDirective", "NbSelectComponent", "NbOptionComponent", "i11", "BreadcrumbComponent", "i12", "BaseFilePipe", "styles"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\selection-management\\content-management-sales-account\\detail-content-management-sales-account\\detail-content-management-sales-account.component.ts", "C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\selection-management\\content-management-sales-account\\detail-content-management-sales-account\\detail-content-management-sales-account.component.html"], "sourcesContent": ["import { Component, OnIni<PERSON>, On<PERSON><PERSON>roy } from '@angular/core';\r\nimport { CommonModule, Location } from '@angular/common';\r\nimport { NbCheckboxModule } from '@nebular/theme';\r\nimport { AllowHelper } from 'src/app/shared/helper/allowHelper';\r\nimport { FormItemService, MaterialService, RegularNoticeFileService } from 'src/services/api/services';\r\nimport { ActivatedRoute } from '@angular/router';\r\nimport { MessageService } from 'src/app/shared/services/message.service';\r\nimport { tap } from 'rxjs';\r\nimport { CreateListFormItem, FileViewModel, GetListFormItemRes, SaveListFormItemReq } from 'src/services/api/models';\r\nimport { BaseFilePipe } from 'src/app/@theme/pipes/base-file.pipe';\r\nimport { UtilityService } from 'src/app/shared/services/utility.service';\r\nimport { ValidationHelper } from 'src/app/shared/helper/validationHelper';\r\nimport { SharedModule } from 'src/app/pages/components/shared.module';\r\nimport { BaseComponent } from 'src/app/pages/components/base/baseComponent';\r\nimport { EventService, EEvent } from 'src/app/shared/services/event.service';\r\nimport { Base64ImagePipe } from \"../../../../@theme/pipes/base64-image.pipe\";\r\n\r\n\r\nexport interface ExtendedSaveListFormItemReq {\r\n  CDesignFileUrl?: string | null;\r\n  CMatrialUrl?: string[] | null;\r\n  CFile?: FileViewModel,\r\n  CFormItemHouseHold?: Array<string> | null;\r\n  CFormItemId?: number;\r\n  CItemName?: string;\r\n  CFormID?: number;\r\n  CPart?: string | null;\r\n  CName?: string | null;\r\n  CLocation?: string | null;\r\n  CRemarkType?: string | null;\r\n  CTotalAnswer?: number;\r\n  CRequireAnswer?: number;\r\n  CUiType?: number;\r\n  selectedCUiType: any | null;\r\n  selectedItems: { [key: string]: boolean }\r\n  selectedRemarkType?: { [key: string]: boolean }\r\n  allSelected: boolean,\r\n  listPictures: any[],\r\n  currentImageIndex?: number; // 當前顯示的圖片索引\r\n  isModalOpen?: boolean; // 是否打開放大模態窗口\r\n}\r\n\r\n@Component({\r\n  selector: 'ngx-detail-content-management-sales-account',\r\n  templateUrl: './detail-content-management-sales-account.component.html',\r\n  styleUrls: ['./detail-content-management-sales-account.component.scss'],\r\n  standalone: true,\r\n  imports: [CommonModule, SharedModule, NbCheckboxModule, BaseFilePipe, Base64ImagePipe],\r\n})\r\n\r\nexport class DetailContentManagementSalesAccountComponent extends BaseComponent implements OnInit, OnDestroy {\r\n  constructor(\r\n    private _allow: AllowHelper,\r\n    private route: ActivatedRoute,\r\n    private message: MessageService,\r\n    private _formItemService: FormItemService,\r\n    private _regularNoticeFileService: RegularNoticeFileService,\r\n    private _utilityService: UtilityService,\r\n    private valid: ValidationHelper,\r\n    private location: Location,\r\n    private _materialService: MaterialService,\r\n    private _eventService: EventService\r\n  ) {\r\n    super(_allow)\r\n  }\r\n\r\n  typeContentManagementSalesAccount = {\r\n    CFormType: 2,\r\n    CNoticeType: 2\r\n  }\r\n\r\n  CUiTypeOptions: any[] = [\r\n    {\r\n      value: 1, label: '建材選色'\r\n    },\r\n    {\r\n      value: 2, label: '群組選樣_選色'\r\n    }, {\r\n      value: 3, label: '建材選樣'\r\n    }]\r\n  CRemarkTypeOptions = [\"正常\", \"留料\"]\r\n  buildCaseId: number;\r\n\r\n  override ngOnInit(): void {\r\n    this.route.paramMap.subscribe(params => {\r\n      if (params) {\r\n        const idParam = params.get('id');\r\n        const id = idParam ? +idParam : 0;\r\n        this.buildCaseId = id\r\n        if (this.buildCaseId > 0) {\r\n          this.getListRegularNoticeFileHouseHold()\r\n        } else {\r\n          // 如果 buildCaseId 為 0 或無效，顯示錯誤訊息並返回\r\n          console.error('Invalid buildCaseId:', this.buildCaseId);\r\n          this.message.showErrorMSG(\"無效的建案ID，請重新選擇建案\");\r\n          this.goBack();\r\n        }\r\n      }\r\n    })\r\n  }\r\n\r\n  ngOnDestroy(): void {\r\n    // 確保在組件銷毀時恢復body的滾動\r\n    document.body.style.overflow = 'auto';\r\n  }\r\n\r\n  getItemByValue(value: any, options: any[]) {\r\n    for (const item of options) {\r\n      if (item.value === value) {\r\n        return item;\r\n      }\r\n    }\r\n    return null;\r\n  }\r\n\r\n\r\n  selectedItems: { [key: string]: boolean } = {};\r\n  selectedRemarkType: { [key: string]: boolean } = {};\r\n\r\n\r\n\r\n  detectFiles(event: any, formItemReq_: any) {\r\n    const file = event.target.files[0]\r\n    if (file) {\r\n      let reader = new FileReader();\r\n      reader.readAsDataURL(file);\r\n      reader.onload = () => {\r\n        let base64Str: string = reader.result as string;\r\n        if (!base64Str) {\r\n          return;\r\n        }\r\n        if (formItemReq_.listPictures.length > 0) {\r\n          formItemReq_.listPictures[0] = {\r\n            id: new Date().getTime(),\r\n            name: file.name.split('.')[0],\r\n            data: base64Str,\r\n            extension: this._utilityService.getFileExtension(file.name),\r\n            CFile: file\r\n          };\r\n        } else {\r\n          formItemReq_.listPictures.push({\r\n            id: new Date().getTime(),\r\n            name: file.name.split('.')[0],\r\n            data: base64Str,\r\n            extension: this._utilityService.getFileExtension(file.name),\r\n            CFile: file\r\n          });\r\n        }\r\n        event.target.value = null;\r\n      };\r\n    }\r\n  }\r\n\r\n  removeImage(pictureId: number, formItemReq_: any) {\r\n    if (formItemReq_.listPictures.length) {\r\n      formItemReq_.listPictures = formItemReq_.listPictures.filter((x: any) => x.id != pictureId)\r\n    }\r\n  }\r\n  renameFile(event: any, index: number, formItemReq_: any) {\r\n    var blob = formItemReq_.listPictures[index].CFile.slice(0, formItemReq_.listPictures[index].CFile.size, formItemReq_.listPictures[index].CFile.type);\r\n    var newFile = new File([blob], `${event.target.value + '.' + formItemReq_.listPictures[index].extension}`, { type: formItemReq_.listPictures[index].CFile.type });\r\n    formItemReq_.listPictures[index].CFile = newFile\r\n  }\r\n\r\n  // 輪播功能方法\r\n  nextImage(formItemReq: any) {\r\n    if (formItemReq.CMatrialUrl && formItemReq.CMatrialUrl.length > 0) {\r\n      formItemReq.currentImageIndex = (formItemReq.currentImageIndex + 1) % formItemReq.CMatrialUrl.length;\r\n    }\r\n  }\r\n\r\n  prevImage(formItemReq: any) {\r\n    if (formItemReq.CMatrialUrl && formItemReq.CMatrialUrl.length > 0) {\r\n      formItemReq.currentImageIndex = formItemReq.currentImageIndex === 0\r\n        ? formItemReq.CMatrialUrl.length - 1\r\n        : formItemReq.currentImageIndex - 1;\r\n    }\r\n  }\r\n  getCurrentImage(formItemReq: any): string | null {\r\n    if (formItemReq.CMatrialUrl && formItemReq.CMatrialUrl.length > 0 && formItemReq.currentImageIndex !== undefined) {\r\n      return formItemReq.CMatrialUrl[formItemReq.currentImageIndex];\r\n    }\r\n    return null;\r\n  }\r\n\r\n  // 放大功能方法\r\n  openImageModal(formItemReq: any, imageIndex?: number) {\r\n    if (formItemReq.CMatrialUrl && formItemReq.CMatrialUrl.length > 0) {\r\n      if (imageIndex !== undefined) {\r\n        formItemReq.currentImageIndex = imageIndex;\r\n      }\r\n      formItemReq.isModalOpen = true;\r\n      // 防止背景滾動\r\n      document.body.style.overflow = 'hidden';\r\n    }\r\n  }\r\n\r\n  closeImageModal(formItemReq: any) {\r\n    formItemReq.isModalOpen = false;\r\n    // 恢復背景滾動\r\n    document.body.style.overflow = 'auto';\r\n  }\r\n\r\n  // 模態窗口中的輪播方法\r\n  nextImageModal(formItemReq: any) {\r\n    this.nextImage(formItemReq);\r\n  }\r\n\r\n  prevImageModal(formItemReq: any) {\r\n    this.prevImage(formItemReq);\r\n  }\r\n\r\n  // 鍵盤事件處理\r\n  onKeydown(event: KeyboardEvent, formItemReq: any) {\r\n    if (formItemReq.isModalOpen) {\r\n      switch (event.key) {\r\n        case 'ArrowLeft':\r\n          event.preventDefault();\r\n          this.prevImageModal(formItemReq);\r\n          break;\r\n        case 'ArrowRight':\r\n          event.preventDefault();\r\n          this.nextImageModal(formItemReq);\r\n          break;\r\n        case 'Escape':\r\n          event.preventDefault();\r\n          this.closeImageModal(formItemReq);\r\n          break;\r\n      }\r\n    }\r\n  }\r\n\r\n  onCheckAllChange(checked: boolean, formItemReq_: any) {\r\n    formItemReq_.allSelected = checked;\r\n    this.houseHoldList.forEach(item => {\r\n      formItemReq_.selectedItems[item] = checked;\r\n    });\r\n  }\r\n\r\n  onCheckboxHouseHoldListChange(checked: boolean, item: string, formItemReq_: any) {\r\n    if (checked) {\r\n      formItemReq_.selectedItems[item] = checked;\r\n      formItemReq_.allSelected = this.houseHoldList.every(item => formItemReq_.selectedItems[item] == checked);\r\n    } else {\r\n      formItemReq_.allSelected = false\r\n    }\r\n  }\r\n\r\n\r\n\r\n  onCheckboxRemarkChange(checked: boolean, item: string, formItemReq_: any) {\r\n    formItemReq_.selectedRemarkType[item] = checked;\r\n  }\r\n\r\n  createRemarkObject(CRemarkTypeOptions: string[], CRemarkType: string): { [key: string]: boolean } {\r\n    const remarkObject: { [key: string]: boolean } = {};\r\n    for (const option of CRemarkTypeOptions) {\r\n      remarkObject[option] = false;\r\n    }\r\n    const remarkTypes = CRemarkType.split('-');\r\n    for (const type of remarkTypes) {\r\n      if (CRemarkTypeOptions.includes(type)) {\r\n        remarkObject[type] = true;\r\n      }\r\n    }\r\n    return remarkObject;\r\n  }\r\n\r\n  mergeItems(items: ExtendedSaveListFormItemReq[]): ExtendedSaveListFormItemReq[] {\r\n    const map = new Map<string, { item: ExtendedSaveListFormItemReq, count: number }>();\r\n\r\n    items.forEach(item => {\r\n      const key = `${item.CLocation}_${item.CName}_${item.CPart}`;\r\n      if (map.has(key)) {\r\n        const existing = map.get(key)!;\r\n        existing.count += 1;\r\n      } else {\r\n        map.set(key, { item: { ...item }, count: 1 });\r\n      }\r\n    });\r\n\r\n    return Array.from(map.values()).map(({ item, count }) => ({\r\n      ...item,\r\n      CTotalAnswer: count\r\n    }));\r\n  }\r\n\r\n\r\n  getMaterialList() { // call when create\r\n    this._materialService.apiMaterialGetMaterialListPost$Json({\r\n      body: {\r\n        CBuildCaseId: this.buildCaseId,\r\n        CPagi: false\r\n      }\r\n    }).pipe(\r\n      tap(res => {\r\n        if (res.Entries && res.StatusCode == 0) {\r\n\r\n          this.houseHoldList.forEach(item => this.selectedItems[item] = false);\r\n\r\n          this.CRemarkTypeOptions.forEach(item => this.selectedRemarkType[item] = false);\r\n\r\n          this.arrListFormItemReq = res.Entries.map((o: any) => {\r\n            return {\r\n              CDesignFileUrl: null,\r\n              CFormItemHouseHold: null,\r\n              CFormId: null,\r\n              CLocation: o.CLocation,\r\n              CName: o.CName,\r\n              CPart: o.CPart,\r\n              CItemName: `${o.CName}-${o.CPart}-${o.CLocation}`,\r\n              CRemarkType: null,\r\n              CTotalAnswer: 0,\r\n              CRequireAnswer: 1,\r\n              CUiType: 0, selectedItems: {},\r\n              selectedRemarkType: this.selectedRemarkType,\r\n              allSelected: false,\r\n              listPictures: [], selectedCUiType: this.CUiTypeOptions[0],\r\n              CMatrialUrl: o.CPicture ? [o.CPicture] : [],\r\n              currentImageIndex: 0,\r\n              isModalOpen: false,\r\n            }\r\n          })\r\n          this.arrListFormItemReq = [...this.mergeItems(this.arrListFormItemReq)]\r\n        }\r\n      })\r\n    ).subscribe();\r\n  }\r\n\r\n  listFormItem: GetListFormItemRes\r\n  isNew: boolean = true\r\n\r\n  getListFormItem() {\r\n    this._formItemService.apiFormItemGetListFormItemPost$Json({\r\n      body: {\r\n        CBuildCaseId: this.buildCaseId,\r\n        CFormType: this.typeContentManagementSalesAccount.CFormType,\r\n        CIsPaging: false\r\n      }\r\n    }).pipe(\r\n      tap(res => {\r\n        if (res.Entries && res.StatusCode == 0) {\r\n          this.listFormItem = res.Entries\r\n          this.isNew = res.Entries.formItems ? false : true\r\n          if (res.Entries.formItems) {\r\n            this.houseHoldList.forEach(item => this.selectedItems[item] = false);\r\n            this.CRemarkTypeOptions.forEach(item => this.selectedRemarkType[item] = false);\r\n\r\n            this.arrListFormItemReq = res.Entries.formItems.map((o: any) => {\r\n              return {\r\n                CFormId: this.listFormItem.CFormId,\r\n                CDesignFileUrl: o.CDesignFileUrl,\r\n                CMatrialUrl: o.CMatrialUrl || (o.CFirstMatrialUrl ? [o.CFirstMatrialUrl] : []),\r\n                CFile: o.CFile,\r\n                CFormItemHouseHold: o.CFormItemHouseHold,\r\n                CFormItemId: o.CFormItemId,\r\n                CLocation: o.CLocation,\r\n                CName: o.CName,\r\n                CPart: o.CPart,\r\n                CItemName: o.CItemName ? o.CItemName : `${o.CName}-${o.CPart}-${o.CLocation}`,\r\n                CRemarkType: o.CRemarkType,\r\n                CTotalAnswer: o.CTotalAnswer,\r\n                CRequireAnswer: o.CUiType === 3 ? 1 : o.CRequireAnswer,\r\n                CUiType: o.CUiType,\r\n                selectedItems: o.tblFormItemHouseholds.length ? this.createArrayObjectFromArray(this.houseHoldList, o.tblFormItemHouseholds) : { ...this.selectedItems }, selectedRemarkType: o.CRemarkType ? this.createRemarkObject(this.CRemarkTypeOptions, o.CRemarkType) : { ...this.selectedRemarkType },\r\n                allSelected: o.tblFormItemHouseholds.length === this.houseHoldList.length,\r\n                listPictures: [], selectedCUiType: o.CUiType ? this.getItemByValue(o.CUiType, this.CUiTypeOptions) : this.CUiTypeOptions[0],\r\n                currentImageIndex: 0,\r\n                isModalOpen: false\r\n              }\r\n            })\r\n          } else {\r\n            this.getMaterialList()\r\n          }\r\n        }\r\n      })\r\n    ).subscribe()\r\n  }\r\n\r\n  changeSelectCUiType(formItemReq: any) {\r\n    if (formItemReq.selectedCUiType && formItemReq.selectedCUiType.value === 3) {\r\n      formItemReq.CRequireAnswer = 1\r\n    }\r\n  }\r\n  getHouseHoldListByNoticeType(data: any[]) {\r\n    for (let item of data) {\r\n      if (item.CNoticeType === this.typeContentManagementSalesAccount.CNoticeType) {\r\n        return item.CHouseHoldList;\r\n      }\r\n    }\r\n    return [];\r\n  }\r\n\r\n  arrListFormItemReq: ExtendedSaveListFormItemReq[]\r\n\r\n  getKeysWithTrueValue(obj: Record<string, boolean>): string[] { // {\"key\": true } => [\"key\"]\r\n    return Object.keys(obj).filter(key => obj[key]);\r\n  }\r\n\r\n  getKeysWithTrueValueJoined(obj: Record<string, boolean>): string {\r\n    return Object.keys(obj)\r\n      .filter(key => obj[key])\r\n      .join('-');\r\n  }\r\n\r\n  getCRemarkType(selectedCUiType: any, selectedRemarkType: any): any {\r\n    if (selectedCUiType && selectedCUiType.value == 3) {\r\n      return this.getKeysWithTrueValueJoined(selectedRemarkType)\r\n    }\r\n  }\r\n\r\n  getStringAfterComma(inputString: string): string {\r\n    const parts = inputString.split(',');\r\n    if (parts.length > 1) {\r\n      return parts[1];\r\n    } else return \"\"\r\n  }\r\n\r\n  formatFile(listPictures: any) {\r\n    if (listPictures && listPictures.length > 0) {\r\n      return {\r\n        Base64String: this.getStringAfterComma(listPictures[0].data) || null,\r\n        FileExtension: listPictures[0].extension || null,\r\n        FileName: listPictures[0].CFile.name || listPictures[0].name || null,\r\n      }\r\n    } else return undefined\r\n\r\n  }\r\n\r\n\r\n  validation() {\r\n    this.valid.clear();\r\n    let hasInvalidCUiType = false;\r\n    let hasInvalidCRequireAnswer = false;\r\n    let hasInvalidItemName = false;\r\n\r\n    for (const item of this.saveListFormItemReq) {\r\n      if (!hasInvalidCUiType && (!item.CUiType)) {\r\n        hasInvalidCUiType = true;\r\n      }\r\n      if (!hasInvalidCRequireAnswer && (!item.CRequireAnswer)) {\r\n        hasInvalidCRequireAnswer = true;\r\n      }\r\n      if (item.CTotalAnswer && item.CRequireAnswer) {\r\n        if (item.CRequireAnswer > item.CTotalAnswer || item.CRequireAnswer < 0) {\r\n          this.valid.addErrorMessage('[必填數量]' + ' <= ' + item.CTotalAnswer + ` (${item.CItemName}) `);\r\n        }\r\n      }\r\n\r\n      if (!hasInvalidItemName && (!item.CItemName)) {\r\n        hasInvalidItemName = true;\r\n      }\r\n    }\r\n    if (hasInvalidCUiType) {\r\n      this.valid.addErrorMessage('[前台UI類型]' + ' 必填');\r\n    }\r\n    if (hasInvalidCRequireAnswer) {\r\n      this.valid.addErrorMessage('[必填數量]' + ' 必填且>0');\r\n    }\r\n    if (hasInvalidItemName) {\r\n      this.valid.addErrorMessage('[廚房-廚具-櫃體]' + ' 必填');\r\n    }\r\n  }\r\n\r\n  saveListFormItemReq: SaveListFormItemReq[]\r\n\r\n  onSubmit() {\r\n    this.saveListFormItemReq = this.arrListFormItemReq.map((e: any) => {\r\n      return {\r\n        CDesignFileUrl: e.CDesignFileUrl ? e.CDesignFileUrl : null,\r\n        CFile: e.listPictures ? this.formatFile(e.listPictures) : undefined,\r\n        CFormItemHouseHold: this.getKeysWithTrueValue(e.selectedItems),\r\n        CFormItemId: e.CFormItemId ? e.CFormItemId : null,\r\n        CFormID: this.isNew ? null : this.listFormItem.CFormId,\r\n        CName: e.CName,\r\n        CPart: e.CPart,\r\n        CLocation: e.CLocation,\r\n        CItemName: e.CItemName, //? e.CItemName : `${e.CName}-${e.CPart}-${e.CLocation}`,\r\n        CRemarkType: e.selectedCUiType.value !== 3 ? null : this.getCRemarkType(e.selectedCUiType, e.selectedRemarkType) || null,\r\n        CTotalAnswer: e.CTotalAnswer,\r\n        CRequireAnswer: e.CRequireAnswer,\r\n        CUiType: e.selectedCUiType.value,\r\n      }\r\n    })\r\n    this.validation()\r\n    if (this.valid.errorMessages.length > 0) {\r\n      this.message.showErrorMSGs(this.valid.errorMessages);\r\n      return\r\n    }\r\n    if (this.isNew) {\r\n      this.createListFormItem()\r\n\r\n    } else {\r\n      this.saveListFormItem()\r\n    }\r\n  }\r\n\r\n  saveListFormItem() {\r\n    this._formItemService.apiFormItemSaveListFormItemPost$Json({\r\n      body: this.saveListFormItemReq\r\n    }).subscribe(res => {\r\n      if (res.StatusCode == 0) {\r\n        this.message.showSucessMSG(\"執行成功\");\r\n        // this.getListFormItem()\r\n        this.goBack()\r\n      }\r\n    })\r\n  }\r\n\r\n  creatListFormItem: CreateListFormItem\r\n\r\n  createListFormItem() {\r\n    this.creatListFormItem = {\r\n      CBuildCaseId: this.buildCaseId,\r\n      CFormItem: this.saveListFormItemReq || null,\r\n      CFormType: this.typeContentManagementSalesAccount.CFormType,\r\n    }\r\n\r\n    this._formItemService.apiFormItemCreateListFormItemPost$Json({\r\n      body: this.creatListFormItem\r\n    }).subscribe(res => {\r\n      if (res.StatusCode == 0) {\r\n        this.message.showSucessMSG(\"執行成功\");\r\n        // this.getListFormItem()\r\n        this.goBack()\r\n      }\r\n    })\r\n  }\r\n\r\n  createArrayObjectFromArray(a: string[], b: { CFormItemHouseholdId: number, CFormItemId: number, CHousehold: string, CIsSelect: boolean }[]): { [key: string]: boolean } {\r\n    const c: { [key: string]: boolean } = {};\r\n    for (const item of a) {\r\n      const matchingItem = b.find(bItem => bItem.CHousehold === item && bItem.CIsSelect);\r\n      c[item] = !!matchingItem;\r\n    }\r\n    return c;\r\n  } //[\"House1\", \"House2\", \"House3\"] => [{CHousehold: \"House1\", CIsSelect: true,... }, ... ]\r\n\r\n\r\n  houseHoldList: any[]\r\n\r\n  getListRegularNoticeFileHouseHold() {\r\n    this._regularNoticeFileService.apiRegularNoticeFileGetListRegularNoticeFileHouseHoldPost$Json({\r\n      body: this.buildCaseId\r\n    }).pipe(\r\n      tap(res => {\r\n        if (res.Entries && res.StatusCode == 0) {\r\n          this.houseHoldList = this.getHouseHoldListByNoticeType(res.Entries)\r\n          this.getListFormItem()\r\n        }\r\n      })\r\n    ).subscribe()\r\n  }\r\n  goBack() {\r\n    this._eventService.push({\r\n      action: EEvent.GET_BUILDCASE,\r\n      payload: this.buildCaseId\r\n    })\r\n    this.location.back()\r\n  }\r\n}\r\n", "<!-- 3.7.1  CNoticeType = 1 -->\r\n<div class=\"min-h-screen bg-gradient-to-br from-gray-50 to-gray-100\">\r\n  <nb-card class=\"shadow-xl border-0 rounded-xl overflow-hidden\">\r\n    <nb-card-header class=\"bg-white border-b border-gray-200 p-6\">\r\n      <div class=\"flex items-center justify-between\">\r\n        <div class=\"flex items-center space-x-4\">\r\n          <div class=\"w-1 h-8 bg-green-500 rounded-full\"></div>\r\n          <div>\r\n            <ngx-breadcrumb></ngx-breadcrumb>\r\n            <h2 class=\"text-2xl font-bold text-gray-800 mt-2\">選樣內容管理 - 銷售戶</h2>\r\n          </div>\r\n        </div>\r\n        <div class=\"flex items-center space-x-2\">\r\n          <span class=\"px-3 py-1 text-sm bg-green-100 text-green-800 rounded-full font-medium\">\r\n            獨立選樣管理\r\n          </span>\r\n        </div>\r\n      </div>\r\n    </nb-card-header>\r\n    \r\n    <nb-card-body class=\"p-6 bg-gray-50\">\r\n      <div class=\"space-y-8\">\r\n        <!-- Page Title Section -->\r\n        <div class=\"bg-white rounded-xl p-6 shadow-sm border border-gray-200\">\r\n          <div class=\"flex items-center space-x-3\">\r\n            <div class=\"w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center\">\r\n              <svg class=\"w-6 h-6 text-blue-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"></path>\r\n              </svg>\r\n            </div>\r\n            <div>\r\n              <h3 class=\"text-xl font-bold text-gray-800\">類型 - 獨立選樣</h3>\r\n              <p class=\"text-sm text-gray-600 mt-1\">管理選樣項目的詳細設定與配置</p>\r\n            </div>\r\n          </div>\r\n        </div>        <!-- Form Items Section -->\r\n        <ng-container *ngFor=\"let formItemReq of arrListFormItemReq; let idx = index\">\r\n          <div class=\"bg-white rounded-xl shadow-lg border border-gray-200 overflow-hidden transition-all duration-300 hover:shadow-xl\">\r\n            <!-- Item Header -->\r\n            <div class=\"bg-gradient-to-r from-blue-50 to-indigo-50 px-6 py-4 border-b border-gray-200\">\r\n              <div class=\"flex items-center justify-between\">\r\n                <div class=\"flex items-center space-x-3\">\r\n                  <div class=\"w-8 h-8 bg-blue-500 text-white rounded-full flex items-center justify-center text-sm font-bold\">\r\n                    {{idx + 1}}\r\n                  </div>\r\n                  <div>\r\n                    <h4 class=\"text-lg font-semibold text-gray-800\">\r\n                      {{formItemReq.CName}}-{{formItemReq.CPart}}-{{formItemReq.CLocation}}\r\n                    </h4>\r\n                    <p class=\"text-sm text-gray-600\">項目編號 #{{idx + 1}}</p>\r\n                  </div>\r\n                </div>\r\n                <div class=\"flex items-center space-x-2\">\r\n                  <span class=\"px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded-full\">\r\n                    {{formItemReq.selectedCUiType?.label || '未設定'}}\r\n                  </span>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            <!-- Main Content Area -->\r\n            <div class=\"p-6\">\r\n              <div class=\"grid grid-cols-1 lg:grid-cols-4 gap-6\">\r\n                \r\n                <!-- Material Images Section (Enhanced) -->\r\n                <div class=\"lg:col-span-1\">\r\n                  <div class=\"space-y-4\">\r\n                    <div class=\"flex items-center space-x-2\">\r\n                      <svg class=\"w-5 h-5 text-gray-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                        <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z\"></path>\r\n                      </svg>\r\n                      <label class=\"text-sm font-semibold text-gray-700\">主要材料示意</label>\r\n                    </div>\r\n                    \r\n                    <div *ngIf=\"formItemReq.CMatrialUrl && formItemReq.CMatrialUrl.length > 0\" class=\"relative\">\r\n                      <!-- Enhanced Image carousel container -->\r\n                      <div class=\"aspect-square w-full relative overflow-hidden rounded-xl border-2 border-gray-200 cursor-pointer group shadow-md hover:shadow-lg transition-all duration-300\"\r\n                        (click)=\"openImageModal(formItemReq)\">\r\n                        <img class=\"w-full h-full object-cover transition-transform duration-500 group-hover:scale-110\"\r\n                          [src]=\"getCurrentImage(formItemReq) | base64Image\" *ngIf=\"getCurrentImage(formItemReq)\">\r\n\r\n                        <!-- Enhanced Zoom overlay -->\r\n                        <div class=\"absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-all duration-300 flex items-center justify-center\">\r\n                          <div class=\"transform scale-75 group-hover:scale-100 transition-transform duration-300\">\r\n                            <div class=\"w-12 h-12 bg-white bg-opacity-90 rounded-full flex items-center justify-center\">\r\n                              <svg class=\"w-6 h-6 text-gray-800\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\"\r\n                                  d=\"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0zM10 7v3m0 0v3m0-3h3m-3 0H7\"></path>\r\n                              </svg>\r\n                            </div>\r\n                          </div>\r\n                        </div>\r\n\r\n                        <!-- Navigation buttons -->\r\n                        <button *ngIf=\"formItemReq.CMatrialUrl.length > 1\"\r\n                          class=\"absolute left-2 top-1/2 transform -translate-y-1/2 bg-white bg-opacity-95 text-gray-800 rounded-full w-8 h-8 flex items-center justify-center hover:bg-opacity-100 hover:shadow-md transition-all z-10 opacity-0 group-hover:opacity-100\"\r\n                          (click)=\"prevImage(formItemReq); $event.stopPropagation()\" title=\"上一張圖片\">\r\n                          <svg class=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2.5\" d=\"M15 19l-7-7 7-7\"></path>\r\n                          </svg>\r\n                        </button>\r\n\r\n                        <button *ngIf=\"formItemReq.CMatrialUrl.length > 1\"\r\n                          class=\"absolute right-2 top-1/2 transform -translate-y-1/2 bg-white bg-opacity-95 text-gray-800 rounded-full w-8 h-8 flex items-center justify-center hover:bg-opacity-100 hover:shadow-md transition-all z-10 opacity-0 group-hover:opacity-100\"\r\n                          (click)=\"nextImage(formItemReq); $event.stopPropagation()\" title=\"下一張圖片\">\r\n                          <svg class=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2.5\" d=\"M9 5l7 7-7 7\"></path>\r\n                          </svg>\r\n                        </button>\r\n\r\n                        <!-- Enhanced Image counter -->\r\n                        <div *ngIf=\"formItemReq.CMatrialUrl.length > 1\"\r\n                          class=\"absolute bottom-2 right-2 bg-black bg-opacity-80 text-white text-xs px-2 py-1 rounded-lg backdrop-blur-sm\">\r\n                          {{(formItemReq.currentImageIndex || 0) + 1}} / {{formItemReq.CMatrialUrl.length}}\r\n                        </div>\r\n                      </div>\r\n\r\n                      <!-- Enhanced Thumbnail navigation -->\r\n                      <div *ngIf=\"formItemReq.CMatrialUrl.length > 1\" class=\"flex gap-2 mt-3 overflow-x-auto pb-2\">\r\n                        <button *ngFor=\"let imageUrl of formItemReq.CMatrialUrl; let i = index\"\r\n                          class=\"flex-shrink-0 w-12 h-12 border-2 rounded-lg overflow-hidden hover:border-blue-400 transition-all duration-200 cursor-pointer hover:scale-105\"\r\n                          [class.border-blue-500]=\"i === (formItemReq.currentImageIndex || 0)\"\r\n                          [class.border-gray-300]=\"i !== (formItemReq.currentImageIndex || 0)\"\r\n                          [class.ring-2]=\"i === (formItemReq.currentImageIndex || 0)\"\r\n                          [class.ring-blue-200]=\"i === (formItemReq.currentImageIndex || 0)\"\r\n                          (click)=\"openImageModal(formItemReq, i)\" [title]=\"'點選放大第 ' + (i + 1) + ' 張圖片'\">\r\n                          <img class=\"w-full h-full object-cover transition-transform hover:scale-110\"\r\n                            [src]=\"imageUrl | base64Image\">\r\n                        </button>\r\n                      </div>\r\n                    </div>\r\n                    \r\n                    <div *ngIf=\"!formItemReq.CMatrialUrl || formItemReq.CMatrialUrl.length === 0\"\r\n                      class=\"aspect-square w-full flex flex-col items-center justify-center border-2 border-dashed border-gray-300 rounded-xl bg-gray-50 text-gray-400\">\r\n                      <svg class=\"w-12 h-12 mb-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                        <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z\"></path>\r\n                      </svg>\r\n                      <span class=\"text-sm\">無主要材料示意</span>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n\r\n                <!-- Form Fields Section (Enhanced) -->\r\n                <div class=\"lg:col-span-2\">\r\n                  <div class=\"space-y-6\">\r\n                    <div class=\"flex items-center space-x-2 mb-4\">\r\n                      <svg class=\"w-5 h-5 text-gray-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                        <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"></path>\r\n                      </svg>\r\n                      <label class=\"text-sm font-semibold text-gray-700\">基本設定</label>\r\n                    </div>\r\n\r\n                    <!-- Enhanced Form Groups -->\r\n                    <div class=\"space-y-4\">\r\n                      <div class=\"group\">\r\n                        <label [for]=\"'CItemName_' + idx\" class=\"block text-sm font-medium text-gray-700 mb-2\">項目名稱</label>\r\n                        <div class=\"flex items-center space-x-3 bg-gray-50 p-3 rounded-lg border border-gray-200\">\r\n                          <span class=\"text-sm text-gray-600 font-medium px-2 py-1 bg-blue-100 rounded-md whitespace-nowrap\">\r\n                            {{formItemReq.CName}}-{{formItemReq.CPart}}-{{formItemReq.CLocation}}:\r\n                          </span>\r\n                          <input type=\"text\" [id]=\"'CItemName_' + idx\" \r\n                            class=\"flex-1 border-0 bg-transparent focus:ring-2 focus:ring-blue-500 focus:border-transparent rounded-md p-2\" \r\n                            nbInput [(ngModel)]=\"formItemReq.CItemName\" \r\n                            placeholder=\"例如：廚房檯面\"\r\n                            [disabled]=\"listFormItem.CIsLock ?? false\" />\r\n                        </div>\r\n                      </div>\r\n\r\n                      <div class=\"group\">\r\n                        <label [for]=\"'cRequireAnswer_' + idx\" class=\"block text-sm font-medium text-gray-700 mb-2\">必填數量</label>\r\n                        <div class=\"relative\">\r\n                          <input type=\"number\" [id]=\"'cRequireAnswer_' + idx\" \r\n                            class=\"w-full border-2 border-gray-200 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 rounded-lg p-3 transition-all duration-200\" \r\n                            nbInput placeholder=\"輸入數量\"\r\n                            [(ngModel)]=\"formItemReq.CRequireAnswer\"\r\n                            [disabled]=\"formItemReq.selectedCUiType.value === 3 || (listFormItem.CIsLock ?? false)\" />\r\n                          <div class=\"absolute inset-y-0 right-0 flex items-center pr-3\">\r\n                            <svg class=\"w-5 h-5 text-gray-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                              <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M7 20l4-16m2 16l4-16M6 9h14M4 15h14\"></path>\r\n                            </svg>\r\n                          </div>\r\n                        </div>\r\n                      </div>\r\n\r\n                      <div class=\"group\">\r\n                        <label [for]=\"'uiType_' + idx\" class=\"block text-sm font-medium text-gray-700 mb-2\">前台UI類型</label>\r\n                        <nb-select placeholder=\"選擇UI類型\" [id]=\"'uiType_' + idx\" \r\n                          [(ngModel)]=\"formItemReq.selectedCUiType\"\r\n                          class=\"w-full border-2 border-gray-200 focus:border-blue-500 rounded-lg transition-all duration-200\" \r\n                          (selectedChange)=\"changeSelectCUiType(formItemReq)\"\r\n                          [disabled]=\"listFormItem.CIsLock ?? false\">\r\n                          <nb-option *ngFor=\"let case of CUiTypeOptions\" [value]=\"case\">\r\n                            {{ case.label }}\r\n                          </nb-option>\r\n                        </nb-select>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n\r\n                <!-- Concept Design Section (Enhanced) -->\r\n                <div class=\"lg:col-span-1\">\r\n                  <div class=\"space-y-4\">\r\n                    <div class=\"flex items-center space-x-2\">\r\n                      <svg class=\"w-5 h-5 text-gray-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                        <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M7 4V2a1 1 0 011-1h8a1 1 0 011 1v2m0 0V1a1 1 0 011-1h2a1 1 0 011 1v18a1 1 0 01-1 1H4a1 1 0 01-1-1V4a1 1 0 011-1h2a1 1 0 011 1v3z\"></path>\r\n                      </svg>\r\n                      <label class=\"text-sm font-semibold text-gray-700\">概念設計</label>\r\n                    </div>\r\n\r\n                    <!-- Enhanced Upload Button -->\r\n                    <button class=\"w-full bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white font-medium py-3 px-4 rounded-lg transition-all duration-200 flex items-center justify-center space-x-2 shadow-md hover:shadow-lg disabled:opacity-50 disabled:cursor-not-allowed\" \r\n                      [disabled]=\"listFormItem.CIsLock\"\r\n                      (click)=\"inputFile.click()\">\r\n                      <svg class=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                        <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12\"></path>\r\n                      </svg>\r\n                      <span>上傳概念設計圖</span>\r\n                    </button>\r\n                    <input #inputFile type=\"file\" class=\"hidden\" (change)=\"detectFiles($event, formItemReq)\"\r\n                      accept=\"image/png, image/gif, image/jpeg\">\r\n\r\n                    <!-- Enhanced Uploaded Pictures List -->\r\n                    <div *ngIf=\"formItemReq.listPictures && formItemReq.listPictures.length > 0\" class=\"space-y-3\">\r\n                      <div *ngFor=\"let picture of formItemReq.listPictures; let i = index\"\r\n                        class=\"bg-gray-50 border border-gray-200 p-3 rounded-lg hover:shadow-md transition-all duration-200\">\r\n                        <div class=\"relative group\">\r\n                          <img class=\"w-full h-32 object-cover rounded-lg mb-3 border border-gray-200\" [src]=\"picture.data\">\r\n                          <div class=\"absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all duration-200 rounded-lg\"></div>\r\n                        </div>\r\n                        <input nbInput class=\"w-full p-2 text-sm mb-2 border border-gray-200 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent\" \r\n                          type=\"text\" placeholder=\"圖片說明/檔名\"\r\n                          [value]=\"picture.name\" (blur)=\"renameFile($event, i, formItemReq)\"\r\n                          [disabled]=\"listFormItem.CIsLock ?? false\">\r\n                        <button class=\"w-full bg-red-100 hover:bg-red-200 text-red-700 font-medium py-2 px-3 rounded-md transition-colors duration-200 text-sm\" \r\n                          (click)=\"removeImage(picture.id, formItemReq)\"\r\n                          [disabled]=\"listFormItem.CIsLock ?? false\">\r\n                          <svg class=\"w-4 h-4 inline mr-1\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16\"></path>\r\n                          </svg>\r\n                          刪除圖片\r\n                        </button>\r\n                      </div>\r\n                    </div>\r\n\r\n                    <!-- Enhanced Default Concept Design Image -->\r\n                    <div class=\"space-y-2\"\r\n                      *ngIf=\"formItemReq.CDesignFileUrl && (!formItemReq.listPictures || formItemReq.listPictures.length === 0)\">\r\n                      <label class=\"block text-xs font-medium text-gray-600\">預設概念圖</label>\r\n                      <div class=\"relative group\">\r\n                        <img class=\"w-full h-32 object-cover rounded-lg border border-gray-200\" [src]=\"formItemReq.CDesignFileUrl | addBaseFile\">\r\n                        <div class=\"absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all duration-200 rounded-lg\"></div>\r\n                      </div>\r\n                    </div>\r\n\r\n                    <div *ngIf=\"!formItemReq.CDesignFileUrl && (!formItemReq.listPictures || formItemReq.listPictures.length === 0)\"\r\n                      class=\"h-32 w-full flex flex-col items-center justify-center border-2 border-dashed border-gray-300 rounded-lg bg-gray-50 text-gray-400\">\r\n                      <svg class=\"w-8 h-8 mb-1\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                        <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M7 4V2a1 1 0 011-1h8a1 1 0 011 1v2m0 0V1a1 1 0 011-1h2a1 1 0 011 1v18a1 1 0 01-1 1H4a1 1 0 01-1-1V4a1 1 0 011-1h2a1 1 0 011 1v3z\"></path>\r\n                      </svg>\r\n                      <span class=\"text-xs\">無概念設計圖</span>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n\r\n              <!-- Enhanced Separator -->\r\n              <div class=\"my-8\">\r\n                <div class=\"relative\">\r\n                  <div class=\"absolute inset-0 flex items-center\">\r\n                    <div class=\"w-full border-t border-gray-300\"></div>\r\n                  </div>\r\n                  <div class=\"relative flex justify-center text-sm\">\r\n                    <span class=\"px-4 bg-white text-gray-500 font-medium\">設定選項</span>\r\n                  </div>\r\n                </div>\r\n              </div>              <!-- Enhanced Applicable Households Section -->\r\n              <div class=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\r\n                <div class=\"bg-blue-50 p-4 rounded-lg border border-blue-200\">\r\n                  <div class=\"flex items-center space-x-2 mb-4\">\r\n                    <svg class=\"w-5 h-5 text-blue-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                      <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z\"></path>\r\n                    </svg>\r\n                    <h5 class=\"font-semibold text-blue-800\">適用戶別</h5>\r\n                  </div>\r\n                  \r\n                  <div class=\"space-y-3\">\r\n                    <label class=\"flex items-center cursor-pointer hover:bg-blue-100 p-2 rounded-md transition-colors\" *ngIf=\"houseHoldList.length\">\r\n                      <nb-checkbox [(checked)]=\"formItemReq.allSelected\" [disabled]=\"listFormItem.CIsLock\"\r\n                        (checkedChange)=\"onCheckAllChange($event, formItemReq)\" class=\"mr-3\">\r\n                      </nb-checkbox>\r\n                      <span class=\"font-medium text-blue-700\">全選</span>\r\n                    </label>\r\n                    \r\n                    <div class=\"grid grid-cols-1 gap-2\" *ngIf=\"houseHoldList.length > 0; else noHouseholds\">\r\n                      <label *ngFor=\"let item of houseHoldList\" class=\"flex items-center cursor-pointer hover:bg-blue-100 p-2 rounded-md transition-colors\">\r\n                        <nb-checkbox [(checked)]=\"formItemReq.selectedItems[item]\" value=\"item\"\r\n                          [disabled]=\"listFormItem.CIsLock\"\r\n                          (checkedChange)=\"onCheckboxHouseHoldListChange($event, item, formItemReq)\" class=\"mr-3\">\r\n                        </nb-checkbox>\r\n                        <span class=\"text-gray-700\">{{ item }}</span>\r\n                      </label>\r\n                    </div>\r\n                    \r\n                    <ng-template #noHouseholds>\r\n                      <div class=\"text-center py-4\">\r\n                        <svg class=\"w-8 h-8 text-gray-400 mx-auto mb-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                          <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z\"></path>\r\n                        </svg>\r\n                        <span class=\"text-gray-500 text-sm\">尚無戶別資料</span>\r\n                      </div>\r\n                    </ng-template>\r\n                  </div>\r\n                </div>\r\n\r\n                <!-- Enhanced Remark Options Section -->\r\n                <div class=\"bg-orange-50 p-4 rounded-lg border border-orange-200\" *ngIf=\"formItemReq.selectedCUiType.value === 3\">\r\n                  <div class=\"flex items-center space-x-2 mb-4\">\r\n                    <svg class=\"w-5 h-5 text-orange-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                      <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M7 8h10M7 12h4m1 8l-4-4H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-3l-4 4z\"></path>\r\n                    </svg>\r\n                    <h5 class=\"font-semibold text-orange-800\">備註選項</h5>\r\n                  </div>\r\n                  \r\n                  <div class=\"grid grid-cols-1 gap-2\" *ngIf=\"CRemarkTypeOptions && CRemarkTypeOptions.length > 0; else noRemarkOptions\">\r\n                    <label *ngFor=\"let remark of CRemarkTypeOptions\" class=\"flex items-center cursor-pointer hover:bg-orange-100 p-2 rounded-md transition-colors\">\r\n                      <nb-checkbox *ngIf=\"formItemReq.selectedRemarkType\"\r\n                        [(checked)]=\"formItemReq.selectedRemarkType[remark]\" [disabled]=\"listFormItem.CIsLock\" value=\"item\"\r\n                        (checkedChange)=\"onCheckboxRemarkChange($event, remark, formItemReq)\" class=\"mr-3\">\r\n                      </nb-checkbox>\r\n                      <span class=\"text-gray-700\">{{ remark }}</span>\r\n                    </label>\r\n                  </div>\r\n                  \r\n                  <ng-template #noRemarkOptions>\r\n                    <div class=\"text-center py-4\">\r\n                      <svg class=\"w-8 h-8 text-gray-400 mx-auto mb-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                        <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" d=\"M7 8h10M7 12h4m1 8l-4-4H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-3l-4 4z\"></path>\r\n                      </svg>\r\n                      <span class=\"text-gray-500 text-sm\">尚無備註選項</span>\r\n                    </div>\r\n                  </ng-template>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </ng-container>\r\n      </div>\r\n    </nb-card-body>\r\n    \r\n    <!-- Enhanced Footer -->\r\n    <nb-card-footer class=\"bg-white border-t border-gray-200 p-6 sticky bottom-0 shadow-lg\">\r\n      <div class=\"flex items-center justify-between\">\r\n        <div class=\"flex items-center space-x-2 text-sm text-gray-600\">\r\n          <svg class=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"></path>\r\n          </svg>\r\n          <span>共 {{arrListFormItemReq.length || 0}} 個選樣項目</span>\r\n        </div>\r\n        \r\n        <div class=\"flex items-center space-x-4\">\r\n          <button class=\"px-6 py-3 bg-gray-200 hover:bg-gray-300 text-gray-700 font-medium rounded-lg transition-all duration-200 flex items-center space-x-2 shadow-sm hover:shadow-md\" \r\n            (click)=\"goBack()\" [disabled]=\"listFormItem.CIsLock ?? false\">\r\n            <svg class=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n              <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M10 19l-7-7m0 0l7-7m-7 7h18\"></path>\r\n            </svg>\r\n            <span>取消</span>\r\n          </button>\r\n          \r\n          <button class=\"px-6 py-3 bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white font-medium rounded-lg transition-all duration-200 flex items-center space-x-2 shadow-md hover:shadow-lg disabled:opacity-50 disabled:cursor-not-allowed\" \r\n            (click)=\"onSubmit()\" [disabled]=\"listFormItem.CIsLock ?? false\">\r\n            <svg class=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n              <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M5 13l4 4L19 7\"></path>\r\n            </svg>\r\n            <span>儲存變更</span>\r\n          </button>\r\n        </div>\r\n      </div>\r\n    </nb-card-footer>\r\n  </nb-card>\r\n</div>\r\n\r\n<!-- Enhanced Image Modal for each formItemReq -->\r\n<ng-container *ngFor=\"let formItemReq of arrListFormItemReq; let idx = index\">\r\n  <div *ngIf=\"formItemReq.isModalOpen\"\r\n    class=\"fixed inset-0 z-[9999] flex items-center justify-center bg-black bg-opacity-80 backdrop-blur-sm p-4 animate-fadeIn\"\r\n    (click)=\"closeImageModal(formItemReq)\" (keydown)=\"onKeydown($event, formItemReq)\" tabindex=\"0\">\r\n    \r\n    <!-- Enhanced Close Button -->\r\n    <button\r\n      class=\"fixed top-6 right-6 z-[60] bg-red-500 bg-opacity-95 hover:bg-red-600 hover:bg-opacity-100 text-white rounded-full w-14 h-14 flex items-center justify-center transition-all duration-200 shadow-2xl hover:shadow-3xl hover:scale-110 group\"\r\n      (click)=\"closeImageModal(formItemReq)\" title=\"關閉圖片檢視 (按 ESC 鍵)\">\r\n      <svg class=\"w-7 h-7 group-hover:rotate-90 transition-transform duration-200\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n        <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2.5\" d=\"M6 18L18 6M6 6l12 12\"></path>\r\n      </svg>\r\n    </button>\r\n\r\n    <!-- Enhanced Modal Content -->\r\n    <div class=\"relative max-w-7xl max-h-full w-full h-full flex items-center justify-center animate-scaleIn\"\r\n      (click)=\"$event.stopPropagation()\">\r\n\r\n      <!-- Main Image Container -->\r\n      <div class=\"relative max-w-full max-h-full bg-white rounded-2xl p-2 shadow-2xl\">\r\n        <img class=\"max-w-full max-h-[85vh] object-contain rounded-xl\" \r\n          [src]=\"getCurrentImage(formItemReq) | base64Image\" \r\n          *ngIf=\"getCurrentImage(formItemReq)\"\r\n          class=\"animate-fadeIn\">\r\n        \r\n        <!-- Enhanced Navigation Buttons -->\r\n        <button *ngIf=\"formItemReq.CMatrialUrl && formItemReq.CMatrialUrl.length > 1\"\r\n          class=\"absolute left-4 top-1/2 transform -translate-y-1/2 bg-white bg-opacity-95 hover:bg-opacity-100 text-gray-800 rounded-full w-16 h-16 flex items-center justify-center transition-all duration-200 shadow-lg hover:shadow-xl hover:scale-105 z-[55] group\"\r\n          (click)=\"prevImageModal(formItemReq)\" title=\"上一張圖片 (按 ← 鍵)\">\r\n          <svg class=\"w-8 h-8 group-hover:-translate-x-1 transition-transform duration-200\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2.5\" d=\"M15 19l-7-7 7-7\"></path>\r\n          </svg>\r\n        </button>\r\n\r\n        <button *ngIf=\"formItemReq.CMatrialUrl && formItemReq.CMatrialUrl.length > 1\"\r\n          class=\"absolute right-4 top-1/2 transform -translate-y-1/2 bg-white bg-opacity-95 hover:bg-opacity-100 text-gray-800 rounded-full w-16 h-16 flex items-center justify-center transition-all duration-200 shadow-lg hover:shadow-xl hover:scale-105 z-[55] group\"\r\n          (click)=\"nextImageModal(formItemReq)\" title=\"下一張圖片 (按 → 鍵)\">\r\n          <svg class=\"w-8 h-8 group-hover:translate-x-1 transition-transform duration-200\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2.5\" d=\"M9 5l7 7-7 7\"></path>\r\n          </svg>\r\n        </button>\r\n      </div>\r\n\r\n      <!-- Enhanced Image Counter -->\r\n      <div *ngIf=\"formItemReq.CMatrialUrl && formItemReq.CMatrialUrl.length > 1\"\r\n        class=\"absolute bottom-24 left-1/2 transform -translate-x-1/2 bg-black bg-opacity-90 text-white px-6 py-3 rounded-full backdrop-blur-sm shadow-lg\">\r\n        <div class=\"flex items-center space-x-3\">\r\n          <svg class=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z\"></path>\r\n          </svg>\r\n          <span class=\"font-medium text-lg\">{{(formItemReq.currentImageIndex || 0) + 1}} / {{formItemReq.CMatrialUrl.length}}</span>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- Enhanced Image Info -->\r\n      <div class=\"absolute bottom-6 right-6 bg-gradient-to-r from-blue-600 to-blue-700 text-white px-4 py-3 rounded-lg text-sm backdrop-blur-sm shadow-lg\">\r\n        <div class=\"flex items-center space-x-2\">\r\n          <svg class=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"></path>\r\n          </svg>\r\n          <span class=\"font-medium\">{{formItemReq.CName}}-{{formItemReq.CPart}}-{{formItemReq.CLocation}}</span>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- Enhanced Thumbnail Strip for Modal -->\r\n      <div *ngIf=\"formItemReq.CMatrialUrl && formItemReq.CMatrialUrl.length > 1\"\r\n        class=\"absolute bottom-32 left-1/2 transform -translate-x-1/2 bg-black bg-opacity-80 backdrop-blur-md p-4 rounded-xl shadow-2xl max-w-full\">\r\n        <div class=\"flex gap-3 overflow-x-auto max-w-[80vw]\">\r\n          <button *ngFor=\"let imageUrl of formItemReq.CMatrialUrl; let i = index\"\r\n            class=\"flex-shrink-0 w-20 h-20 border-3 rounded-xl overflow-hidden hover:border-white transition-all duration-200 hover:scale-105 group\"\r\n            [class.border-white]=\"i === (formItemReq.currentImageIndex || 0)\"\r\n            [class.border-gray-400]=\"i !== (formItemReq.currentImageIndex || 0)\"\r\n            [class.ring-3]=\"i === (formItemReq.currentImageIndex || 0)\"\r\n            [class.ring-white]=\"i === (formItemReq.currentImageIndex || 0)\"\r\n            [class.ring-opacity-50]=\"i === (formItemReq.currentImageIndex || 0)\"\r\n            (click)=\"formItemReq.currentImageIndex = i\"\r\n            [title]=\"'跳至第 ' + (i + 1) + ' 張圖片'\">\r\n            <img class=\"w-full h-full object-cover group-hover:scale-110 transition-transform duration-200\" \r\n              [src]=\"imageUrl | base64Image\">\r\n          </button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</ng-container>\r\n\r\n<!-- Custom CSS for animations -->\r\n<style>\r\n  @keyframes fadeIn {\r\n    from { opacity: 0; }\r\n    to { opacity: 1; }\r\n  }\r\n  \r\n  @keyframes scaleIn {\r\n    from { \r\n      opacity: 0;\r\n      transform: scale(0.95);\r\n    }\r\n    to { \r\n      opacity: 1;\r\n      transform: scale(1);\r\n    }\r\n  }\r\n  \r\n  .animate-fadeIn {\r\n    animation: fadeIn 0.3s ease-out;\r\n  }\r\n  \r\n  .animate-scaleIn {\r\n    animation: scaleIn 0.3s ease-out;\r\n  }\r\n  \r\n  .border-3 {\r\n    border-width: 3px;\r\n  }\r\n  \r\n  .ring-3 {\r\n    --tw-ring-width: 3px;\r\n  }\r\n</style>"], "mappings": "AACA,SAASA,YAAY,QAAkB,iBAAiB;AACxD,SAASC,gBAAgB,QAAQ,gBAAgB;AAKjD,SAASC,GAAG,QAAQ,MAAM;AAK1B,SAASC,YAAY,QAAQ,wCAAwC;AACrE,SAASC,aAAa,QAAQ,6CAA6C;AAC3E,SAAuBC,MAAM,QAAQ,uCAAuC;AAC5E,SAASC,eAAe,QAAQ,4CAA4C;;;;;;;;;;;;;;;;IC+DpDC,EAAA,CAAAC,SAAA,cAC0F;;;;;;IAAxFD,EAAA,CAAAE,UAAA,QAAAF,EAAA,CAAAG,WAAA,OAAAC,MAAA,CAAAC,eAAA,CAAAC,cAAA,IAAAN,EAAA,CAAAO,aAAA,CAAkD;;;;;;IAepDP,EAAA,CAAAQ,cAAA,iBAE2E;IAAzER,EAAA,CAAAS,UAAA,mBAAAC,8GAAAC,MAAA;MAAAX,EAAA,CAAAY,aAAA,CAAAC,GAAA;MAAA,MAAAP,cAAA,GAAAN,EAAA,CAAAc,aAAA,IAAAC,SAAA;MAAA,MAAAX,MAAA,GAAAJ,EAAA,CAAAc,aAAA;MAASV,MAAA,CAAAY,SAAA,CAAAV,cAAA,CAAsB;MAAA,OAAAN,EAAA,CAAAiB,WAAA,CAAEN,MAAA,CAAAO,eAAA,EAAwB;IAAA,EAAC;;IAC1DlB,EAAA,CAAAQ,cAAA,cAA2E;IACzER,EAAA,CAAAC,SAAA,eAAmG;IAEvGD,EADE,CAAAmB,YAAA,EAAM,EACC;;;;;;IAETnB,EAAA,CAAAQ,cAAA,iBAE2E;IAAzER,EAAA,CAAAS,UAAA,mBAAAW,8GAAAT,MAAA;MAAAX,EAAA,CAAAY,aAAA,CAAAS,GAAA;MAAA,MAAAf,cAAA,GAAAN,EAAA,CAAAc,aAAA,IAAAC,SAAA;MAAA,MAAAX,MAAA,GAAAJ,EAAA,CAAAc,aAAA;MAASV,MAAA,CAAAkB,SAAA,CAAAhB,cAAA,CAAsB;MAAA,OAAAN,EAAA,CAAAiB,WAAA,CAAEN,MAAA,CAAAO,eAAA,EAAwB;IAAA,EAAC;;IAC1DlB,EAAA,CAAAQ,cAAA,cAA2E;IACzER,EAAA,CAAAC,SAAA,eAAgG;IAEpGD,EADE,CAAAmB,YAAA,EAAM,EACC;;;;;IAGTnB,EAAA,CAAAQ,cAAA,cACoH;IAClHR,EAAA,CAAAuB,MAAA,GACF;IAAAvB,EAAA,CAAAmB,YAAA,EAAM;;;;IADJnB,EAAA,CAAAwB,SAAA,EACF;IADExB,EAAA,CAAAyB,kBAAA,OAAAnB,cAAA,CAAAoB,iBAAA,mBAAApB,cAAA,CAAAqB,WAAA,CAAAC,MAAA,MACF;;;;;;IAKA5B,EAAA,CAAAQ,cAAA,kBAMiF;IAA/ER,EAAA,CAAAS,UAAA,mBAAAoB,qHAAA;MAAA,MAAAC,IAAA,GAAA9B,EAAA,CAAAY,aAAA,CAAAmB,GAAA,EAAAC,KAAA;MAAA,MAAA1B,cAAA,GAAAN,EAAA,CAAAc,aAAA,IAAAC,SAAA;MAAA,MAAAX,MAAA,GAAAJ,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAiB,WAAA,CAASb,MAAA,CAAA6B,cAAA,CAAA3B,cAAA,EAAAwB,IAAA,CAA8B;IAAA,EAAC;IACxC9B,EAAA,CAAAC,SAAA,eACiC;;IACnCD,EAAA,CAAAmB,YAAA,EAAS;;;;;;IAJPnB,EAHA,CAAAkC,WAAA,oBAAAJ,IAAA,MAAAxB,cAAA,CAAAoB,iBAAA,OAAoE,oBAAAI,IAAA,MAAAxB,cAAA,CAAAoB,iBAAA,OACA,WAAAI,IAAA,MAAAxB,cAAA,CAAAoB,iBAAA,OACT,kBAAAI,IAAA,MAAAxB,cAAA,CAAAoB,iBAAA,OACO;IACzB1B,EAAA,CAAAE,UAAA,+CAAA4B,IAAA,8BAAqC;IAE5E9B,EAAA,CAAAwB,SAAA,EAA8B;IAA9BxB,EAAA,CAAAE,UAAA,QAAAF,EAAA,CAAAG,WAAA,QAAAgC,WAAA,GAAAnC,EAAA,CAAAO,aAAA,CAA8B;;;;;IATpCP,EAAA,CAAAQ,cAAA,cAA6F;IAC3FR,EAAA,CAAAoC,UAAA,IAAAC,4FAAA,uBAMiF;IAInFrC,EAAA,CAAAmB,YAAA,EAAM;;;;IAVyBnB,EAAA,CAAAwB,SAAA,EAA4B;IAA5BxB,EAAA,CAAAE,UAAA,YAAAI,cAAA,CAAAqB,WAAA,CAA4B;;;;;;IA3C3D3B,EAFF,CAAAQ,cAAA,cAA4F,cAGlD;IAAtCR,EAAA,CAAAS,UAAA,mBAAA6B,kGAAA;MAAAtC,EAAA,CAAAY,aAAA,CAAA2B,GAAA;MAAA,MAAAjC,cAAA,GAAAN,EAAA,CAAAc,aAAA,GAAAC,SAAA;MAAA,MAAAX,MAAA,GAAAJ,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAiB,WAAA,CAASb,MAAA,CAAA6B,cAAA,CAAA3B,cAAA,CAA2B;IAAA,EAAC;IACrCN,EAAA,CAAAoC,UAAA,IAAAI,kFAAA,kBAC0F;IAKtFxC,EAFJ,CAAAQ,cAAA,cAA2I,cACjD,cACM;;IAC1FR,EAAA,CAAAQ,cAAA,cAAyF;IACvFR,EAAA,CAAAC,SAAA,eACmF;IAI3FD,EAHM,CAAAmB,YAAA,EAAM,EACF,EACF,EACF;IAoBNnB,EAjBA,CAAAoC,UAAA,IAAAK,qFAAA,qBAE2E,IAAAC,qFAAA,qBAQA,KAAAC,mFAAA,kBAQyC;IAGtH3C,EAAA,CAAAmB,YAAA,EAAM;IAGNnB,EAAA,CAAAoC,UAAA,KAAAQ,mFAAA,kBAA6F;IAY/F5C,EAAA,CAAAmB,YAAA,EAAM;;;;;IAnDoDnB,EAAA,CAAAwB,SAAA,GAAkC;IAAlCxB,EAAA,CAAAE,UAAA,SAAAE,MAAA,CAAAC,eAAA,CAAAC,cAAA,EAAkC;IAe/EN,EAAA,CAAAwB,SAAA,GAAwC;IAAxCxB,EAAA,CAAAE,UAAA,SAAAI,cAAA,CAAAqB,WAAA,CAAAC,MAAA,KAAwC;IAQxC5B,EAAA,CAAAwB,SAAA,EAAwC;IAAxCxB,EAAA,CAAAE,UAAA,SAAAI,cAAA,CAAAqB,WAAA,CAAAC,MAAA,KAAwC;IAS3C5B,EAAA,CAAAwB,SAAA,EAAwC;IAAxCxB,EAAA,CAAAE,UAAA,SAAAI,cAAA,CAAAqB,WAAA,CAAAC,MAAA,KAAwC;IAO1C5B,EAAA,CAAAwB,SAAA,EAAwC;IAAxCxB,EAAA,CAAAE,UAAA,SAAAI,cAAA,CAAAqB,WAAA,CAAAC,MAAA,KAAwC;;;;;IAchD5B,EAAA,CAAAQ,cAAA,eACoJ;;IAClJR,EAAA,CAAAQ,cAAA,eAAkF;IAChFR,EAAA,CAAAC,SAAA,gBAA6O;IAC/OD,EAAA,CAAAmB,YAAA,EAAM;;IACNnB,EAAA,CAAAQ,cAAA,gBAAsB;IAAAR,EAAA,CAAAuB,MAAA,iDAAO;IAC/BvB,EAD+B,CAAAmB,YAAA,EAAO,EAChC;;;;;IAqDAnB,EAAA,CAAAQ,cAAA,qBAA8D;IAC5DR,EAAA,CAAAuB,MAAA,GACF;IAAAvB,EAAA,CAAAmB,YAAA,EAAY;;;;IAFmCnB,EAAA,CAAAE,UAAA,UAAA2C,QAAA,CAAc;IAC3D7C,EAAA,CAAAwB,SAAA,EACF;IADExB,EAAA,CAAA8C,kBAAA,MAAAD,QAAA,CAAAE,KAAA,MACF;;;;;;IAiCF/C,EAFF,CAAAQ,cAAA,eACuG,eACzE;IAE1BR,EADA,CAAAC,SAAA,eAAkG,eACyB;IAC7HD,EAAA,CAAAmB,YAAA,EAAM;IACNnB,EAAA,CAAAQ,cAAA,iBAG6C;IADpBR,EAAA,CAAAS,UAAA,kBAAAuC,yGAAArC,MAAA;MAAA,MAAAsC,KAAA,GAAAjD,EAAA,CAAAY,aAAA,CAAAsC,IAAA,EAAAlB,KAAA;MAAA,MAAA1B,cAAA,GAAAN,EAAA,CAAAc,aAAA,IAAAC,SAAA;MAAA,MAAAX,MAAA,GAAAJ,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAiB,WAAA,CAAQb,MAAA,CAAA+C,UAAA,CAAAxC,MAAA,EAAAsC,KAAA,EAAA3C,cAAA,CAAkC;IAAA,EAAC;IAFpEN,EAAA,CAAAmB,YAAA,EAG6C;IAC7CnB,EAAA,CAAAQ,cAAA,kBAE6C;IAD3CR,EAAA,CAAAS,UAAA,mBAAA2C,2GAAA;MAAA,MAAAC,WAAA,GAAArD,EAAA,CAAAY,aAAA,CAAAsC,IAAA,EAAAnC,SAAA;MAAA,MAAAT,cAAA,GAAAN,EAAA,CAAAc,aAAA,IAAAC,SAAA;MAAA,MAAAX,MAAA,GAAAJ,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAiB,WAAA,CAASb,MAAA,CAAAkD,WAAA,CAAAD,WAAA,CAAAE,EAAA,EAAAjD,cAAA,CAAoC;IAAA,EAAC;;IAE9CN,EAAA,CAAAQ,cAAA,eAAuF;IACrFR,EAAA,CAAAC,SAAA,gBAA8M;IAChND,EAAA,CAAAmB,YAAA,EAAM;IACNnB,EAAA,CAAAuB,MAAA,iCACF;IACFvB,EADE,CAAAmB,YAAA,EAAS,EACL;;;;;;;IAf2EnB,EAAA,CAAAwB,SAAA,GAAoB;IAApBxB,EAAA,CAAAE,UAAA,QAAAmD,WAAA,CAAAG,IAAA,EAAAxD,EAAA,CAAAO,aAAA,CAAoB;IAKjGP,EAAA,CAAAwB,SAAA,GAAsB;IACtBxB,EADA,CAAAE,UAAA,UAAAmD,WAAA,CAAAI,IAAA,CAAsB,cAAAC,QAAA,GAAAtD,MAAA,CAAAuD,YAAA,CAAAC,OAAA,cAAAF,QAAA,KAAAG,SAAA,GAAAH,QAAA,SACoB;IAG1C1D,EAAA,CAAAwB,SAAA,EAA0C;IAA1CxB,EAAA,CAAAE,UAAA,cAAA4D,QAAA,GAAA1D,MAAA,CAAAuD,YAAA,CAAAC,OAAA,cAAAE,QAAA,KAAAD,SAAA,GAAAC,QAAA,SAA0C;;;;;IAbhD9D,EAAA,CAAAQ,cAAA,cAA+F;IAC7FR,EAAA,CAAAoC,UAAA,IAAA2B,kFAAA,mBACuG;IAkBzG/D,EAAA,CAAAmB,YAAA,EAAM;;;;IAnBqBnB,EAAA,CAAAwB,SAAA,EAA6B;IAA7BxB,EAAA,CAAAE,UAAA,YAAAI,cAAA,CAAA0D,YAAA,CAA6B;;;;;IAwBtDhE,EAFF,CAAAQ,cAAA,eAC6G,iBACpD;IAAAR,EAAA,CAAAuB,MAAA,qCAAK;IAAAvB,EAAA,CAAAmB,YAAA,EAAQ;IACpEnB,EAAA,CAAAQ,cAAA,eAA4B;IAC1BR,EAAA,CAAAC,SAAA,eAAyH;;IACzHD,EAAA,CAAAC,SAAA,eAA2H;IAE/HD,EADE,CAAAmB,YAAA,EAAM,EACF;;;;IAHsEnB,EAAA,CAAAwB,SAAA,GAAgD;IAAhDxB,EAAA,CAAAE,UAAA,QAAAF,EAAA,CAAAG,WAAA,OAAAG,cAAA,CAAA2D,cAAA,GAAAjE,EAAA,CAAAO,aAAA,CAAgD;;;;;IAK5HP,EAAA,CAAAQ,cAAA,eAC2I;;IACzIR,EAAA,CAAAQ,cAAA,eAAgF;IAC9ER,EAAA,CAAAC,SAAA,gBAAoN;IACtND,EAAA,CAAAmB,YAAA,EAAM;;IACNnB,EAAA,CAAAQ,cAAA,gBAAsB;IAAAR,EAAA,CAAAuB,MAAA,2CAAM;IAC9BvB,EAD8B,CAAAmB,YAAA,EAAO,EAC/B;;;;;;IA2BJnB,EADF,CAAAQ,cAAA,iBAAgI,uBAEvD;IAD1DR,EAAA,CAAAkE,gBAAA,2BAAAC,oHAAAxD,MAAA;MAAAX,EAAA,CAAAY,aAAA,CAAAwD,IAAA;MAAA,MAAA9D,cAAA,GAAAN,EAAA,CAAAc,aAAA,GAAAC,SAAA;MAAAf,EAAA,CAAAqE,kBAAA,CAAA/D,cAAA,CAAAgE,WAAA,EAAA3D,MAAA,MAAAL,cAAA,CAAAgE,WAAA,GAAA3D,MAAA;MAAA,OAAAX,EAAA,CAAAiB,WAAA,CAAAN,MAAA;IAAA,EAAqC;IAChDX,EAAA,CAAAS,UAAA,2BAAA0D,oHAAAxD,MAAA;MAAAX,EAAA,CAAAY,aAAA,CAAAwD,IAAA;MAAA,MAAA9D,cAAA,GAAAN,EAAA,CAAAc,aAAA,GAAAC,SAAA;MAAA,MAAAX,MAAA,GAAAJ,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAiB,WAAA,CAAiBb,MAAA,CAAAmE,gBAAA,CAAA5D,MAAA,EAAAL,cAAA,CAAqC;IAAA,EAAC;IACzDN,EAAA,CAAAmB,YAAA,EAAc;IACdnB,EAAA,CAAAQ,cAAA,gBAAwC;IAAAR,EAAA,CAAAuB,MAAA,mBAAE;IAC5CvB,EAD4C,CAAAmB,YAAA,EAAO,EAC3C;;;;;IAJOnB,EAAA,CAAAwB,SAAA,EAAqC;IAArCxB,EAAA,CAAAwE,gBAAA,YAAAlE,cAAA,CAAAgE,WAAA,CAAqC;IAACtE,EAAA,CAAAE,UAAA,aAAAE,MAAA,CAAAuD,YAAA,CAAAC,OAAA,CAAiC;;;;;;IAQlF5D,EADF,CAAAQ,cAAA,iBAAsI,uBAG1C;IAF7ER,EAAA,CAAAkE,gBAAA,2BAAAO,0HAAA9D,MAAA;MAAA,MAAA+D,QAAA,GAAA1E,EAAA,CAAAY,aAAA,CAAA+D,IAAA,EAAA5D,SAAA;MAAA,MAAAT,cAAA,GAAAN,EAAA,CAAAc,aAAA,IAAAC,SAAA;MAAAf,EAAA,CAAAqE,kBAAA,CAAA/D,cAAA,CAAAsE,aAAA,CAAAF,QAAA,GAAA/D,MAAA,MAAAL,cAAA,CAAAsE,aAAA,CAAAF,QAAA,IAAA/D,MAAA;MAAA,OAAAX,EAAA,CAAAiB,WAAA,CAAAN,MAAA;IAAA,EAA6C;IAExDX,EAAA,CAAAS,UAAA,2BAAAgE,0HAAA9D,MAAA;MAAA,MAAA+D,QAAA,GAAA1E,EAAA,CAAAY,aAAA,CAAA+D,IAAA,EAAA5D,SAAA;MAAA,MAAAT,cAAA,GAAAN,EAAA,CAAAc,aAAA,IAAAC,SAAA;MAAA,MAAAX,MAAA,GAAAJ,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAiB,WAAA,CAAiBb,MAAA,CAAAyE,6BAAA,CAAAlE,MAAA,EAAA+D,QAAA,EAAApE,cAAA,CAAwD;IAAA,EAAC;IAC5EN,EAAA,CAAAmB,YAAA,EAAc;IACdnB,EAAA,CAAAQ,cAAA,gBAA4B;IAAAR,EAAA,CAAAuB,MAAA,GAAU;IACxCvB,EADwC,CAAAmB,YAAA,EAAO,EACvC;;;;;;IALOnB,EAAA,CAAAwB,SAAA,EAA6C;IAA7CxB,EAAA,CAAAwE,gBAAA,YAAAlE,cAAA,CAAAsE,aAAA,CAAAF,QAAA,EAA6C;IACxD1E,EAAA,CAAAE,UAAA,aAAAE,MAAA,CAAAuD,YAAA,CAAAC,OAAA,CAAiC;IAGP5D,EAAA,CAAAwB,SAAA,GAAU;IAAVxB,EAAA,CAAA8E,iBAAA,CAAAJ,QAAA,CAAU;;;;;IAN1C1E,EAAA,CAAAQ,cAAA,eAAwF;IACtFR,EAAA,CAAAoC,UAAA,IAAA2C,oFAAA,qBAAsI;IAOxI/E,EAAA,CAAAmB,YAAA,EAAM;;;;IAPoBnB,EAAA,CAAAwB,SAAA,EAAgB;IAAhBxB,EAAA,CAAAE,UAAA,YAAAE,MAAA,CAAA4E,aAAA,CAAgB;;;;;IAUxChF,EAAA,CAAAQ,cAAA,eAA8B;;IAC5BR,EAAA,CAAAQ,cAAA,eAAsG;IACpGR,EAAA,CAAAC,SAAA,gBAA0V;IAC5VD,EAAA,CAAAmB,YAAA,EAAM;;IACNnB,EAAA,CAAAQ,cAAA,gBAAoC;IAAAR,EAAA,CAAAuB,MAAA,2CAAM;IAC5CvB,EAD4C,CAAAmB,YAAA,EAAO,EAC7C;;;;;;IAgBNnB,EAAA,CAAAQ,cAAA,uBAEqF;IADnFR,EAAA,CAAAkE,gBAAA,2BAAAe,8IAAAtE,MAAA;MAAAX,EAAA,CAAAY,aAAA,CAAAsE,IAAA;MAAA,MAAAC,UAAA,GAAAnF,EAAA,CAAAc,aAAA,GAAAC,SAAA;MAAA,MAAAT,cAAA,GAAAN,EAAA,CAAAc,aAAA,IAAAC,SAAA;MAAAf,EAAA,CAAAqE,kBAAA,CAAA/D,cAAA,CAAA8E,kBAAA,CAAAD,UAAA,GAAAxE,MAAA,MAAAL,cAAA,CAAA8E,kBAAA,CAAAD,UAAA,IAAAxE,MAAA;MAAA,OAAAX,EAAA,CAAAiB,WAAA,CAAAN,MAAA;IAAA,EAAoD;IACpDX,EAAA,CAAAS,UAAA,2BAAAwE,8IAAAtE,MAAA;MAAAX,EAAA,CAAAY,aAAA,CAAAsE,IAAA;MAAA,MAAAC,UAAA,GAAAnF,EAAA,CAAAc,aAAA,GAAAC,SAAA;MAAA,MAAAT,cAAA,GAAAN,EAAA,CAAAc,aAAA,IAAAC,SAAA;MAAA,MAAAX,MAAA,GAAAJ,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAiB,WAAA,CAAiBb,MAAA,CAAAiF,sBAAA,CAAA1E,MAAA,EAAAwE,UAAA,EAAA7E,cAAA,CAAmD;IAAA,EAAC;IACvEN,EAAA,CAAAmB,YAAA,EAAc;;;;;;IAFZnB,EAAA,CAAAwE,gBAAA,YAAAlE,cAAA,CAAA8E,kBAAA,CAAAD,UAAA,EAAoD;IAACnF,EAAA,CAAAE,UAAA,aAAAE,MAAA,CAAAuD,YAAA,CAAAC,OAAA,CAAiC;;;;;IAF1F5D,EAAA,CAAAQ,cAAA,iBAA+I;IAC7IR,EAAA,CAAAoC,UAAA,IAAAkD,wGAAA,2BAEqF;IAErFtF,EAAA,CAAAQ,cAAA,gBAA4B;IAAAR,EAAA,CAAAuB,MAAA,GAAY;IAC1CvB,EAD0C,CAAAmB,YAAA,EAAO,EACzC;;;;;IALQnB,EAAA,CAAAwB,SAAA,EAAoC;IAApCxB,EAAA,CAAAE,UAAA,SAAAI,cAAA,CAAA8E,kBAAA,CAAoC;IAItBpF,EAAA,CAAAwB,SAAA,GAAY;IAAZxB,EAAA,CAAA8E,iBAAA,CAAAK,UAAA,CAAY;;;;;IAN5CnF,EAAA,CAAAQ,cAAA,eAAsH;IACpHR,EAAA,CAAAoC,UAAA,IAAAmD,0FAAA,qBAA+I;IAOjJvF,EAAA,CAAAmB,YAAA,EAAM;;;;IAPsBnB,EAAA,CAAAwB,SAAA,EAAqB;IAArBxB,EAAA,CAAAE,UAAA,YAAAE,MAAA,CAAAoF,kBAAA,CAAqB;;;;;IAU/CxF,EAAA,CAAAQ,cAAA,eAA8B;;IAC5BR,EAAA,CAAAQ,cAAA,eAAsG;IACpGR,EAAA,CAAAC,SAAA,gBAA+K;IACjLD,EAAA,CAAAmB,YAAA,EAAM;;IACNnB,EAAA,CAAAQ,cAAA,gBAAoC;IAAAR,EAAA,CAAAuB,MAAA,2CAAM;IAC5CvB,EAD4C,CAAAmB,YAAA,EAAO,EAC7C;;;;;IAvBRnB,EADF,CAAAQ,cAAA,eAAkH,cAClE;;IAC5CR,EAAA,CAAAQ,cAAA,eAA2F;IACzFR,EAAA,CAAAC,SAAA,gBAA6K;IAC/KD,EAAA,CAAAmB,YAAA,EAAM;;IACNnB,EAAA,CAAAQ,cAAA,cAA0C;IAAAR,EAAA,CAAAuB,MAAA,+BAAI;IAChDvB,EADgD,CAAAmB,YAAA,EAAK,EAC/C;IAYNnB,EAVA,CAAAoC,UAAA,IAAAqD,kFAAA,kBAAsH,IAAAC,0FAAA,gCAAA1F,EAAA,CAAA2F,sBAAA,CAUxF;IAQhC3F,EAAA,CAAAmB,YAAA,EAAM;;;;;IAlBiCnB,EAAA,CAAAwB,SAAA,GAA2D;IAAAxB,EAA3D,CAAAE,UAAA,SAAAE,MAAA,CAAAoF,kBAAA,IAAApF,MAAA,CAAAoF,kBAAA,CAAA5D,MAAA,KAA2D,aAAAgE,mBAAA,CAAoB;;;;;;IAhS9H5F,EAAA,CAAA6F,uBAAA,GAA8E;IAMpE7F,EALR,CAAAQ,cAAA,cAA8H,cAEjC,aAC1C,cACJ,cACqE;IAC1GR,EAAA,CAAAuB,MAAA,GACF;IAAAvB,EAAA,CAAAmB,YAAA,EAAM;IAEJnB,EADF,CAAAQ,cAAA,UAAK,aAC6C;IAC9CR,EAAA,CAAAuB,MAAA,GACF;IAAAvB,EAAA,CAAAmB,YAAA,EAAK;IACLnB,EAAA,CAAAQ,cAAA,aAAiC;IAAAR,EAAA,CAAAuB,MAAA,IAAiB;IAEtDvB,EAFsD,CAAAmB,YAAA,EAAI,EAClD,EACF;IAEJnB,EADF,CAAAQ,cAAA,eAAyC,gBACgC;IACrER,EAAA,CAAAuB,MAAA,IACF;IAGNvB,EAHM,CAAAmB,YAAA,EAAO,EACH,EACF,EACF;IASEnB,EANR,CAAAQ,cAAA,eAAiB,eACoC,eAGtB,eACF,eACoB;;IACvCR,EAAA,CAAAQ,cAAA,eAAyF;IACvFR,EAAA,CAAAC,SAAA,gBAA2O;IAC7OD,EAAA,CAAAmB,YAAA,EAAM;;IACNnB,EAAA,CAAAQ,cAAA,iBAAmD;IAAAR,EAAA,CAAAuB,MAAA,4CAAM;IAC3DvB,EAD2D,CAAAmB,YAAA,EAAQ,EAC7D;IA4DNnB,EA1DA,CAAAoC,UAAA,KAAA0D,4EAAA,mBAA4F,KAAAC,4EAAA,kBA2DwD;IAOxJ/F,EADE,CAAAmB,YAAA,EAAM,EACF;IAKFnB,EAFJ,CAAAQ,cAAA,eAA2B,eACF,eACyB;;IAC5CR,EAAA,CAAAQ,cAAA,eAAyF;IACvFR,EAAA,CAAAC,SAAA,gBAAsM;IACxMD,EAAA,CAAAmB,YAAA,EAAM;;IACNnB,EAAA,CAAAQ,cAAA,iBAAmD;IAAAR,EAAA,CAAAuB,MAAA,gCAAI;IACzDvB,EADyD,CAAAmB,YAAA,EAAQ,EAC3D;IAKFnB,EAFJ,CAAAQ,cAAA,eAAuB,eACF,iBACsE;IAAAR,EAAA,CAAAuB,MAAA,gCAAI;IAAAvB,EAAA,CAAAmB,YAAA,EAAQ;IAEjGnB,EADF,CAAAQ,cAAA,eAA0F,gBACW;IACjGR,EAAA,CAAAuB,MAAA,IACF;IAAAvB,EAAA,CAAAmB,YAAA,EAAO;IACPnB,EAAA,CAAAQ,cAAA,iBAI+C;IAFrCR,EAAA,CAAAkE,gBAAA,2BAAA8B,sGAAArF,MAAA;MAAA,MAAAL,cAAA,GAAAN,EAAA,CAAAY,aAAA,CAAAqF,GAAA,EAAAlF,SAAA;MAAAf,EAAA,CAAAqE,kBAAA,CAAA/D,cAAA,CAAA4F,SAAA,EAAAvF,MAAA,MAAAL,cAAA,CAAA4F,SAAA,GAAAvF,MAAA;MAAA,OAAAX,EAAA,CAAAiB,WAAA,CAAAN,MAAA;IAAA,EAAmC;IAIjDX,EANI,CAAAmB,YAAA,EAI+C,EAC3C,EACF;IAGJnB,EADF,CAAAQ,cAAA,eAAmB,iBAC2E;IAAAR,EAAA,CAAAuB,MAAA,gCAAI;IAAAvB,EAAA,CAAAmB,YAAA,EAAQ;IAEtGnB,EADF,CAAAQ,cAAA,eAAsB,iBAKwE;IAD1FR,EAAA,CAAAkE,gBAAA,2BAAAiC,sGAAAxF,MAAA;MAAA,MAAAL,cAAA,GAAAN,EAAA,CAAAY,aAAA,CAAAqF,GAAA,EAAAlF,SAAA;MAAAf,EAAA,CAAAqE,kBAAA,CAAA/D,cAAA,CAAA8F,cAAA,EAAAzF,MAAA,MAAAL,cAAA,CAAA8F,cAAA,GAAAzF,MAAA;MAAA,OAAAX,EAAA,CAAAiB,WAAA,CAAAN,MAAA;IAAA,EAAwC;IAH1CX,EAAA,CAAAmB,YAAA,EAI4F;IAC5FnB,EAAA,CAAAQ,cAAA,eAA+D;;IAC7DR,EAAA,CAAAQ,cAAA,eAAyF;IACvFR,EAAA,CAAAC,SAAA,gBAAqH;IAI7HD,EAHM,CAAAmB,YAAA,EAAM,EACF,EACF,EACF;;IAGJnB,EADF,CAAAQ,cAAA,eAAmB,iBACmE;IAAAR,EAAA,CAAAuB,MAAA,kCAAM;IAAAvB,EAAA,CAAAmB,YAAA,EAAQ;IAClGnB,EAAA,CAAAQ,cAAA,qBAI6C;IAH3CR,EAAA,CAAAkE,gBAAA,2BAAAmC,0GAAA1F,MAAA;MAAA,MAAAL,cAAA,GAAAN,EAAA,CAAAY,aAAA,CAAAqF,GAAA,EAAAlF,SAAA;MAAAf,EAAA,CAAAqE,kBAAA,CAAA/D,cAAA,CAAAgG,eAAA,EAAA3F,MAAA,MAAAL,cAAA,CAAAgG,eAAA,GAAA3F,MAAA;MAAA,OAAAX,EAAA,CAAAiB,WAAA,CAAAN,MAAA;IAAA,EAAyC;IAEzCX,EAAA,CAAAS,UAAA,4BAAA8F,2GAAA;MAAA,MAAAjG,cAAA,GAAAN,EAAA,CAAAY,aAAA,CAAAqF,GAAA,EAAAlF,SAAA;MAAA,MAAAX,MAAA,GAAAJ,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAiB,WAAA,CAAkBb,MAAA,CAAAoG,mBAAA,CAAAlG,cAAA,CAAgC;IAAA,EAAC;IAEnDN,EAAA,CAAAoC,UAAA,KAAAqE,kFAAA,wBAA8D;IAOxEzG,EAJQ,CAAAmB,YAAA,EAAY,EACR,EACF,EACF,EACF;IAKFnB,EAFJ,CAAAQ,cAAA,eAA2B,eACF,eACoB;;IACvCR,EAAA,CAAAQ,cAAA,eAAyF;IACvFR,EAAA,CAAAC,SAAA,gBAAkN;IACpND,EAAA,CAAAmB,YAAA,EAAM;;IACNnB,EAAA,CAAAQ,cAAA,iBAAmD;IAAAR,EAAA,CAAAuB,MAAA,gCAAI;IACzDvB,EADyD,CAAAmB,YAAA,EAAQ,EAC3D;IAGNnB,EAAA,CAAAQ,cAAA,kBAE8B;IAA5BR,EAAA,CAAAS,UAAA,mBAAAiG,+FAAA;MAAA1G,EAAA,CAAAY,aAAA,CAAAqF,GAAA;MAAA,MAAAU,aAAA,GAAA3G,EAAA,CAAA4G,WAAA;MAAA,OAAA5G,EAAA,CAAAiB,WAAA,CAAS0F,aAAA,CAAAE,KAAA,EAAiB;IAAA,EAAC;;IAC3B7G,EAAA,CAAAQ,cAAA,eAA2E;IACzER,EAAA,CAAAC,SAAA,gBAAuK;IACzKD,EAAA,CAAAmB,YAAA,EAAM;;IACNnB,EAAA,CAAAQ,cAAA,YAAM;IAAAR,EAAA,CAAAuB,MAAA,kDAAO;IACfvB,EADe,CAAAmB,YAAA,EAAO,EACb;IACTnB,EAAA,CAAAQ,cAAA,oBAC4C;IADCR,EAAA,CAAAS,UAAA,oBAAAqG,+FAAAnG,MAAA;MAAA,MAAAL,cAAA,GAAAN,EAAA,CAAAY,aAAA,CAAAqF,GAAA,EAAAlF,SAAA;MAAA,MAAAX,MAAA,GAAAJ,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAiB,WAAA,CAAUb,MAAA,CAAA2G,WAAA,CAAApG,MAAA,EAAAL,cAAA,CAAgC;IAAA,EAAC;IAAxFN,EAAA,CAAAmB,YAAA,EAC4C;IAmC5CnB,EAhCA,CAAAoC,UAAA,KAAA4E,4EAAA,kBAA+F,KAAAC,4EAAA,kBAwBc,KAAAC,4EAAA,kBAS8B;IAQjJlH,EAFI,CAAAmB,YAAA,EAAM,EACF,EACF;IAKFnB,EAFJ,CAAAQ,cAAA,eAAkB,eACM,eAC4B;IAC9CR,EAAA,CAAAC,SAAA,eAAmD;IACrDD,EAAA,CAAAmB,YAAA,EAAM;IAEJnB,EADF,CAAAQ,cAAA,eAAkD,gBACM;IAAAR,EAAA,CAAAuB,MAAA,gCAAI;IAGhEvB,EAHgE,CAAAmB,YAAA,EAAO,EAC7D,EACF,EACF;IAGFnB,EAFJ,CAAAQ,cAAA,eAAmD,eACa,eACd;;IAC5CR,EAAA,CAAAQ,cAAA,eAAyF;IACvFR,EAAA,CAAAC,SAAA,gBAAwV;IAC1VD,EAAA,CAAAmB,YAAA,EAAM;;IACNnB,EAAA,CAAAQ,cAAA,cAAwC;IAAAR,EAAA,CAAAuB,MAAA,gCAAI;IAC9CvB,EAD8C,CAAAmB,YAAA,EAAK,EAC7C;IAENnB,EAAA,CAAAQ,cAAA,eAAuB;IAkBrBR,EAjBA,CAAAoC,UAAA,KAAA+E,8EAAA,oBAAgI,KAAAC,4EAAA,kBAOxC,KAAAC,oFAAA,gCAAArH,EAAA,CAAA2F,sBAAA,CAU7D;IAS/B3F,EADE,CAAAmB,YAAA,EAAM,EACF;IAGNnB,EAAA,CAAAoC,UAAA,KAAAkF,4EAAA,kBAAkH;IA6BxHtH,EAFI,CAAAmB,YAAA,EAAM,EACF,EACF;;;;;;;;;;;IA9SInB,EAAA,CAAAwB,SAAA,GACF;IADExB,EAAA,CAAA8C,kBAAA,MAAAyE,OAAA,UACF;IAGIvH,EAAA,CAAAwB,SAAA,GACF;IADExB,EAAA,CAAAwH,kBAAA,MAAAlH,cAAA,CAAAmH,KAAA,OAAAnH,cAAA,CAAAoH,KAAA,OAAApH,cAAA,CAAAqH,SAAA,MACF;IACiC3H,EAAA,CAAAwB,SAAA,GAAiB;IAAjBxB,EAAA,CAAA8C,kBAAA,+BAAAyE,OAAA,SAAiB;IAKlDvH,EAAA,CAAAwB,SAAA,GACF;IADExB,EAAA,CAAA8C,kBAAA,OAAAxC,cAAA,CAAAgG,eAAA,kBAAAhG,cAAA,CAAAgG,eAAA,CAAAvD,KAAA,+BACF;IAmBQ/C,EAAA,CAAAwB,SAAA,IAAmE;IAAnExB,EAAA,CAAAE,UAAA,SAAAI,cAAA,CAAAqB,WAAA,IAAArB,cAAA,CAAAqB,WAAA,CAAAC,MAAA,KAAmE;IA0DnE5B,EAAA,CAAAwB,SAAA,EAAsE;IAAtExB,EAAA,CAAAE,UAAA,UAAAI,cAAA,CAAAqB,WAAA,IAAArB,cAAA,CAAAqB,WAAA,CAAAC,MAAA,OAAsE;IAuBjE5B,EAAA,CAAAwB,SAAA,IAA0B;IAA1BxB,EAAA,CAAAE,UAAA,uBAAAqH,OAAA,CAA0B;IAG7BvH,EAAA,CAAAwB,SAAA,GACF;IADExB,EAAA,CAAAwH,kBAAA,MAAAlH,cAAA,CAAAmH,KAAA,OAAAnH,cAAA,CAAAoH,KAAA,OAAApH,cAAA,CAAAqH,SAAA,OACF;IACmB3H,EAAA,CAAAwB,SAAA,EAAyB;IAAzBxB,EAAA,CAAAE,UAAA,sBAAAqH,OAAA,CAAyB;IAElCvH,EAAA,CAAAwE,gBAAA,YAAAlE,cAAA,CAAA4F,SAAA,CAAmC;IAE3ClG,EAAA,CAAAE,UAAA,cAAA0H,QAAA,GAAAxH,MAAA,CAAAuD,YAAA,CAAAC,OAAA,cAAAgE,QAAA,KAAA/D,SAAA,GAAA+D,QAAA,SAA0C;IAKvC5H,EAAA,CAAAwB,SAAA,GAA+B;IAA/BxB,EAAA,CAAAE,UAAA,4BAAAqH,OAAA,CAA+B;IAEfvH,EAAA,CAAAwB,SAAA,GAA8B;IAA9BxB,EAAA,CAAAE,UAAA,2BAAAqH,OAAA,CAA8B;IAGjDvH,EAAA,CAAAwE,gBAAA,YAAAlE,cAAA,CAAA8F,cAAA,CAAwC;IACxCpG,EAAA,CAAAE,UAAA,aAAAI,cAAA,CAAAgG,eAAA,CAAAuB,KAAA,YAAAC,QAAA,GAAA1H,MAAA,CAAAuD,YAAA,CAAAC,OAAA,cAAAkE,QAAA,KAAAjE,SAAA,GAAAiE,QAAA,UAAuF;IAUpF9H,EAAA,CAAAwB,SAAA,GAAuB;IAAvBxB,EAAA,CAAAE,UAAA,oBAAAqH,OAAA,CAAuB;IACEvH,EAAA,CAAAwB,SAAA,GAAsB;IAAtBxB,EAAA,CAAAE,UAAA,mBAAAqH,OAAA,CAAsB;IACpDvH,EAAA,CAAAwE,gBAAA,YAAAlE,cAAA,CAAAgG,eAAA,CAAyC;IAGzCtG,EAAA,CAAAE,UAAA,cAAA6H,QAAA,GAAA3H,MAAA,CAAAuD,YAAA,CAAAC,OAAA,cAAAmE,QAAA,KAAAlE,SAAA,GAAAkE,QAAA,SAA0C;IACd/H,EAAA,CAAAwB,SAAA,EAAiB;IAAjBxB,EAAA,CAAAE,UAAA,YAAAE,MAAA,CAAA4H,cAAA,CAAiB;IAqBjDhI,EAAA,CAAAwB,SAAA,GAAiC;IAAjCxB,EAAA,CAAAE,UAAA,aAAAE,MAAA,CAAAuD,YAAA,CAAAC,OAAA,CAAiC;IAW7B5D,EAAA,CAAAwB,SAAA,GAAqE;IAArExB,EAAA,CAAAE,UAAA,SAAAI,cAAA,CAAA0D,YAAA,IAAA1D,cAAA,CAAA0D,YAAA,CAAApC,MAAA,KAAqE;IAwBxE5B,EAAA,CAAAwB,SAAA,EAAwG;IAAxGxB,EAAA,CAAAE,UAAA,SAAAI,cAAA,CAAA2D,cAAA,MAAA3D,cAAA,CAAA0D,YAAA,IAAA1D,cAAA,CAAA0D,YAAA,CAAApC,MAAA,QAAwG;IAQrG5B,EAAA,CAAAwB,SAAA,EAAyG;IAAzGxB,EAAA,CAAAE,UAAA,UAAAI,cAAA,CAAA2D,cAAA,MAAA3D,cAAA,CAAA0D,YAAA,IAAA1D,cAAA,CAAA0D,YAAA,CAAApC,MAAA,QAAyG;IAgCX5B,EAAA,CAAAwB,SAAA,IAA0B;IAA1BxB,EAAA,CAAAE,UAAA,SAAAE,MAAA,CAAA4E,aAAA,CAAApD,MAAA,CAA0B;IAOzF5B,EAAA,CAAAwB,SAAA,EAAgC;IAAAxB,EAAhC,CAAAE,UAAA,SAAAE,MAAA,CAAA4E,aAAA,CAAApD,MAAA,KAAgC,aAAAqG,gBAAA,CAAiB;IAsBvBjI,EAAA,CAAAwB,SAAA,GAA6C;IAA7CxB,EAAA,CAAAE,UAAA,SAAAI,cAAA,CAAAgG,eAAA,CAAAuB,KAAA,OAA6C;;;;;IAuFxH7H,EAAA,CAAAC,SAAA,eAGyB;;;;;;IAFvBD,EAAA,CAAAE,UAAA,QAAAF,EAAA,CAAAG,WAAA,OAAAC,MAAA,CAAAC,eAAA,CAAA6H,eAAA,IAAAlI,EAAA,CAAAO,aAAA,CAAkD;;;;;;IAKpDP,EAAA,CAAAQ,cAAA,kBAE8D;IAA5DR,EAAA,CAAAS,UAAA,mBAAA0H,6GAAA;MAAAnI,EAAA,CAAAY,aAAA,CAAAwH,IAAA;MAAA,MAAAF,eAAA,GAAAlI,EAAA,CAAAc,aAAA,IAAAC,SAAA;MAAA,MAAAX,MAAA,GAAAJ,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAiB,WAAA,CAASb,MAAA,CAAAiI,cAAA,CAAAH,eAAA,CAA2B;IAAA,EAAC;;IACrClI,EAAA,CAAAQ,cAAA,eAAwI;IACtIR,EAAA,CAAAC,SAAA,eAAmG;IAEvGD,EADE,CAAAmB,YAAA,EAAM,EACC;;;;;;IAETnB,EAAA,CAAAQ,cAAA,kBAE8D;IAA5DR,EAAA,CAAAS,UAAA,mBAAA6H,6GAAA;MAAAtI,EAAA,CAAAY,aAAA,CAAA2H,IAAA;MAAA,MAAAL,eAAA,GAAAlI,EAAA,CAAAc,aAAA,IAAAC,SAAA;MAAA,MAAAX,MAAA,GAAAJ,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAiB,WAAA,CAASb,MAAA,CAAAoI,cAAA,CAAAN,eAAA,CAA2B;IAAA,EAAC;;IACrClI,EAAA,CAAAQ,cAAA,eAAuI;IACrIR,EAAA,CAAAC,SAAA,eAAgG;IAEpGD,EADE,CAAAmB,YAAA,EAAM,EACC;;;;;IAMTnB,EAFF,CAAAQ,cAAA,eACqJ,cAC1G;;IACvCR,EAAA,CAAAQ,cAAA,cAA2E;IACzER,EAAA,CAAAC,SAAA,eAA2O;IAC7OD,EAAA,CAAAmB,YAAA,EAAM;;IACNnB,EAAA,CAAAQ,cAAA,gBAAkC;IAAAR,EAAA,CAAAuB,MAAA,GAAiF;IAEvHvB,EAFuH,CAAAmB,YAAA,EAAO,EACtH,EACF;;;;IAFgCnB,EAAA,CAAAwB,SAAA,GAAiF;IAAjFxB,EAAA,CAAAyB,kBAAA,MAAAyG,eAAA,CAAAxG,iBAAA,mBAAAwG,eAAA,CAAAvG,WAAA,CAAAC,MAAA,KAAiF;;;;;;IAkBnH5B,EAAA,CAAAQ,cAAA,kBAQsC;IADpCR,EAAA,CAAAS,UAAA,mBAAAgI,oHAAA;MAAA,MAAAC,KAAA,GAAA1I,EAAA,CAAAY,aAAA,CAAA+H,IAAA,EAAA3G,KAAA;MAAA,MAAAkG,eAAA,GAAAlI,EAAA,CAAAc,aAAA,IAAAC,SAAA;MAAA,OAAAf,EAAA,CAAAiB,WAAA,CAAAiH,eAAA,CAAAxG,iBAAA,GAAAgH,KAAA;IAAA,EAA2C;IAE3C1I,EAAA,CAAAC,SAAA,eACiC;;IACnCD,EAAA,CAAAmB,YAAA,EAAS;;;;;;IALPnB,EAJA,CAAAkC,WAAA,iBAAAwG,KAAA,MAAAR,eAAA,CAAAxG,iBAAA,OAAiE,oBAAAgH,KAAA,MAAAR,eAAA,CAAAxG,iBAAA,OACG,WAAAgH,KAAA,MAAAR,eAAA,CAAAxG,iBAAA,OACT,eAAAgH,KAAA,MAAAR,eAAA,CAAAxG,iBAAA,OACI,oBAAAgH,KAAA,MAAAR,eAAA,CAAAxG,iBAAA,OACK;IAEpE1B,EAAA,CAAAE,UAAA,mCAAAwI,KAAA,8BAAmC;IAEjC1I,EAAA,CAAAwB,SAAA,EAA8B;IAA9BxB,EAAA,CAAAE,UAAA,QAAAF,EAAA,CAAAG,WAAA,QAAAyI,YAAA,GAAA5I,EAAA,CAAAO,aAAA,CAA8B;;;;;IAXpCP,EAFF,CAAAQ,cAAA,eAC8I,eACvF;IACnDR,EAAA,CAAAoC,UAAA,IAAAyG,2FAAA,uBAQsC;IAK1C7I,EADE,CAAAmB,YAAA,EAAM,EACF;;;;IAb2BnB,EAAA,CAAAwB,SAAA,GAA4B;IAA5BxB,EAAA,CAAAE,UAAA,YAAAgI,eAAA,CAAAvG,WAAA,CAA4B;;;;;;IAnEjE3B,EAAA,CAAAQ,cAAA,eAEiG;IAAxDR,EAAvC,CAAAS,UAAA,mBAAAqI,iGAAA;MAAA9I,EAAA,CAAAY,aAAA,CAAAmI,IAAA;MAAA,MAAAb,eAAA,GAAAlI,EAAA,CAAAc,aAAA,GAAAC,SAAA;MAAA,MAAAX,MAAA,GAAAJ,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAiB,WAAA,CAASb,MAAA,CAAA4I,eAAA,CAAAd,eAAA,CAA4B;IAAA,EAAC,qBAAAe,mGAAAtI,MAAA;MAAAX,EAAA,CAAAY,aAAA,CAAAmI,IAAA;MAAA,MAAAb,eAAA,GAAAlI,EAAA,CAAAc,aAAA,GAAAC,SAAA;MAAA,MAAAX,MAAA,GAAAJ,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAiB,WAAA,CAAYb,MAAA,CAAA8I,SAAA,CAAAvI,MAAA,EAAAuH,eAAA,CAA8B;IAAA,EAAC;IAGjFlI,EAAA,CAAAQ,cAAA,kBAEkE;IAAhER,EAAA,CAAAS,UAAA,mBAAA0I,oGAAA;MAAAnJ,EAAA,CAAAY,aAAA,CAAAmI,IAAA;MAAA,MAAAb,eAAA,GAAAlI,EAAA,CAAAc,aAAA,GAAAC,SAAA;MAAA,MAAAX,MAAA,GAAAJ,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAiB,WAAA,CAASb,MAAA,CAAA4I,eAAA,CAAAd,eAAA,CAA4B;IAAA,EAAC;;IACtClI,EAAA,CAAAQ,cAAA,eAAmI;IACjIR,EAAA,CAAAC,SAAA,gBAAwG;IAE5GD,EADE,CAAAmB,YAAA,EAAM,EACC;;IAGTnB,EAAA,CAAAQ,cAAA,eACqC;IAAnCR,EAAA,CAAAS,UAAA,mBAAA2I,iGAAAzI,MAAA;MAAAX,EAAA,CAAAY,aAAA,CAAAmI,IAAA;MAAA,OAAA/I,EAAA,CAAAiB,WAAA,CAASN,MAAA,CAAAO,eAAA,EAAwB;IAAA,EAAC;IAGlClB,EAAA,CAAAQ,cAAA,eAAgF;IAe9ER,EAdA,CAAAoC,UAAA,IAAAiH,iFAAA,mBAGyB,IAAAC,oFAAA,sBAKqC,IAAAC,oFAAA,sBAQA;IAKhEvJ,EAAA,CAAAmB,YAAA,EAAM;IAGNnB,EAAA,CAAAoC,UAAA,IAAAoH,iFAAA,mBACqJ;IAWnJxJ,EADF,CAAAQ,cAAA,gBAAqJ,eAC1G;;IACvCR,EAAA,CAAAQ,cAAA,eAA2E;IACzER,EAAA,CAAAC,SAAA,gBAA2I;IAC7ID,EAAA,CAAAmB,YAAA,EAAM;;IACNnB,EAAA,CAAAQ,cAAA,iBAA0B;IAAAR,EAAA,CAAAuB,MAAA,IAAqE;IAEnGvB,EAFmG,CAAAmB,YAAA,EAAO,EAClG,EACF;IAGNnB,EAAA,CAAAoC,UAAA,KAAAqH,kFAAA,mBAC8I;IAiBlJzJ,EADE,CAAAmB,YAAA,EAAM,EACF;;;;;IA7DGnB,EAAA,CAAAwB,SAAA,GAAkC;IAAlCxB,EAAA,CAAAE,UAAA,SAAAE,MAAA,CAAAC,eAAA,CAAA6H,eAAA,EAAkC;IAI5BlI,EAAA,CAAAwB,SAAA,EAAmE;IAAnExB,EAAA,CAAAE,UAAA,SAAAgI,eAAA,CAAAvG,WAAA,IAAAuG,eAAA,CAAAvG,WAAA,CAAAC,MAAA,KAAmE;IAQnE5B,EAAA,CAAAwB,SAAA,EAAmE;IAAnExB,EAAA,CAAAE,UAAA,SAAAgI,eAAA,CAAAvG,WAAA,IAAAuG,eAAA,CAAAvG,WAAA,CAAAC,MAAA,KAAmE;IAUxE5B,EAAA,CAAAwB,SAAA,EAAmE;IAAnExB,EAAA,CAAAE,UAAA,SAAAgI,eAAA,CAAAvG,WAAA,IAAAuG,eAAA,CAAAvG,WAAA,CAAAC,MAAA,KAAmE;IAgB3C5B,EAAA,CAAAwB,SAAA,GAAqE;IAArExB,EAAA,CAAAwH,kBAAA,KAAAU,eAAA,CAAAT,KAAA,OAAAS,eAAA,CAAAR,KAAA,OAAAQ,eAAA,CAAAP,SAAA,KAAqE;IAK7F3H,EAAA,CAAAwB,SAAA,EAAmE;IAAnExB,EAAA,CAAAE,UAAA,SAAAgI,eAAA,CAAAvG,WAAA,IAAAuG,eAAA,CAAAvG,WAAA,CAAAC,MAAA,KAAmE;;;;;IAjE/E5B,EAAA,CAAA6F,uBAAA,GAA8E;IAC5E7F,EAAA,CAAAoC,UAAA,IAAAsH,2EAAA,oBAEiG;;;;;IAF3F1J,EAAA,CAAAwB,SAAA,EAA6B;IAA7BxB,EAAA,CAAAE,UAAA,SAAAgI,eAAA,CAAAyB,WAAA,CAA6B;;;AD9UrC,OAAM,MAAOC,4CAA6C,SAAQ/J,aAAa;EAC7EgK,YACUC,MAAmB,EACnBC,KAAqB,EACrBC,OAAuB,EACvBC,gBAAiC,EACjCC,yBAAmD,EACnDC,eAA+B,EAC/BC,KAAuB,EACvBC,QAAkB,EAClBC,gBAAiC,EACjCC,aAA2B;IAEnC,KAAK,CAACT,MAAM,CAAC;IAXL,KAAAA,MAAM,GAANA,MAAM;IACN,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,yBAAyB,GAAzBA,yBAAyB;IACzB,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,QAAQ,GAARA,QAAQ;IACR,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,aAAa,GAAbA,aAAa;IAKvB,KAAAC,iCAAiC,GAAG;MAClCC,SAAS,EAAE,CAAC;MACZC,WAAW,EAAE;KACd;IAED,KAAA1C,cAAc,GAAU,CACtB;MACEH,KAAK,EAAE,CAAC;MAAE9E,KAAK,EAAE;KAClB,EACD;MACE8E,KAAK,EAAE,CAAC;MAAE9E,KAAK,EAAE;KAClB,EAAE;MACD8E,KAAK,EAAE,CAAC;MAAE9E,KAAK,EAAE;KAClB,CAAC;IACJ,KAAAyC,kBAAkB,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC;IAoCjC,KAAAZ,aAAa,GAA+B,EAAE;IAC9C,KAAAQ,kBAAkB,GAA+B,EAAE;IAqNnD,KAAAuF,KAAK,GAAY,IAAI;EA1QrB;EAmBSC,QAAQA,CAAA;IACf,IAAI,CAACb,KAAK,CAACc,QAAQ,CAACC,SAAS,CAACC,MAAM,IAAG;MACrC,IAAIA,MAAM,EAAE;QACV,MAAMC,OAAO,GAAGD,MAAM,CAACE,GAAG,CAAC,IAAI,CAAC;QAChC,MAAM1H,EAAE,GAAGyH,OAAO,GAAG,CAACA,OAAO,GAAG,CAAC;QACjC,IAAI,CAACE,WAAW,GAAG3H,EAAE;QACrB,IAAI,IAAI,CAAC2H,WAAW,GAAG,CAAC,EAAE;UACxB,IAAI,CAACC,iCAAiC,EAAE;QAC1C,CAAC,MAAM;UACL;UACAC,OAAO,CAACC,KAAK,CAAC,sBAAsB,EAAE,IAAI,CAACH,WAAW,CAAC;UACvD,IAAI,CAAClB,OAAO,CAACsB,YAAY,CAAC,iBAAiB,CAAC;UAC5C,IAAI,CAACC,MAAM,EAAE;QACf;MACF;IACF,CAAC,CAAC;EACJ;EAEAC,WAAWA,CAAA;IACT;IACAC,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,MAAM;EACvC;EAEAC,cAAcA,CAAChE,KAAU,EAAEiE,OAAc;IACvC,KAAK,MAAMC,IAAI,IAAID,OAAO,EAAE;MAC1B,IAAIC,IAAI,CAAClE,KAAK,KAAKA,KAAK,EAAE;QACxB,OAAOkE,IAAI;MACb;IACF;IACA,OAAO,IAAI;EACb;EAQAhF,WAAWA,CAACiF,KAAU,EAAEC,YAAiB;IACvC,MAAMC,IAAI,GAAGF,KAAK,CAACG,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC;IAClC,IAAIF,IAAI,EAAE;MACR,IAAIG,MAAM,GAAG,IAAIC,UAAU,EAAE;MAC7BD,MAAM,CAACE,aAAa,CAACL,IAAI,CAAC;MAC1BG,MAAM,CAACG,MAAM,GAAG,MAAK;QACnB,IAAIC,SAAS,GAAWJ,MAAM,CAACK,MAAgB;QAC/C,IAAI,CAACD,SAAS,EAAE;UACd;QACF;QACA,IAAIR,YAAY,CAACjI,YAAY,CAACpC,MAAM,GAAG,CAAC,EAAE;UACxCqK,YAAY,CAACjI,YAAY,CAAC,CAAC,CAAC,GAAG;YAC7BT,EAAE,EAAE,IAAIoJ,IAAI,EAAE,CAACC,OAAO,EAAE;YACxBnJ,IAAI,EAAEyI,IAAI,CAACzI,IAAI,CAACoJ,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;YAC7BrJ,IAAI,EAAEiJ,SAAS;YACfK,SAAS,EAAE,IAAI,CAAC3C,eAAe,CAAC4C,gBAAgB,CAACb,IAAI,CAACzI,IAAI,CAAC;YAC3DuJ,KAAK,EAAEd;WACR;QACH,CAAC,MAAM;UACLD,YAAY,CAACjI,YAAY,CAACiJ,IAAI,CAAC;YAC7B1J,EAAE,EAAE,IAAIoJ,IAAI,EAAE,CAACC,OAAO,EAAE;YACxBnJ,IAAI,EAAEyI,IAAI,CAACzI,IAAI,CAACoJ,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;YAC7BrJ,IAAI,EAAEiJ,SAAS;YACfK,SAAS,EAAE,IAAI,CAAC3C,eAAe,CAAC4C,gBAAgB,CAACb,IAAI,CAACzI,IAAI,CAAC;YAC3DuJ,KAAK,EAAEd;WACR,CAAC;QACJ;QACAF,KAAK,CAACG,MAAM,CAACtE,KAAK,GAAG,IAAI;MAC3B,CAAC;IACH;EACF;EAEAvE,WAAWA,CAAC4J,SAAiB,EAAEjB,YAAiB;IAC9C,IAAIA,YAAY,CAACjI,YAAY,CAACpC,MAAM,EAAE;MACpCqK,YAAY,CAACjI,YAAY,GAAGiI,YAAY,CAACjI,YAAY,CAACmJ,MAAM,CAAEC,CAAM,IAAKA,CAAC,CAAC7J,EAAE,IAAI2J,SAAS,CAAC;IAC7F;EACF;EACA/J,UAAUA,CAAC6I,KAAU,EAAEhK,KAAa,EAAEiK,YAAiB;IACrD,IAAIoB,IAAI,GAAGpB,YAAY,CAACjI,YAAY,CAAChC,KAAK,CAAC,CAACgL,KAAK,CAACM,KAAK,CAAC,CAAC,EAAErB,YAAY,CAACjI,YAAY,CAAChC,KAAK,CAAC,CAACgL,KAAK,CAACO,IAAI,EAAEtB,YAAY,CAACjI,YAAY,CAAChC,KAAK,CAAC,CAACgL,KAAK,CAACQ,IAAI,CAAC;IACpJ,IAAIC,OAAO,GAAG,IAAIC,IAAI,CAAC,CAACL,IAAI,CAAC,EAAE,GAAGrB,KAAK,CAACG,MAAM,CAACtE,KAAK,GAAG,GAAG,GAAGoE,YAAY,CAACjI,YAAY,CAAChC,KAAK,CAAC,CAAC8K,SAAS,EAAE,EAAE;MAAEU,IAAI,EAAEvB,YAAY,CAACjI,YAAY,CAAChC,KAAK,CAAC,CAACgL,KAAK,CAACQ;IAAI,CAAE,CAAC;IACjKvB,YAAY,CAACjI,YAAY,CAAChC,KAAK,CAAC,CAACgL,KAAK,GAAGS,OAAO;EAClD;EAEA;EACAnM,SAASA,CAACqM,WAAgB;IACxB,IAAIA,WAAW,CAAChM,WAAW,IAAIgM,WAAW,CAAChM,WAAW,CAACC,MAAM,GAAG,CAAC,EAAE;MACjE+L,WAAW,CAACjM,iBAAiB,GAAG,CAACiM,WAAW,CAACjM,iBAAiB,GAAG,CAAC,IAAIiM,WAAW,CAAChM,WAAW,CAACC,MAAM;IACtG;EACF;EAEAZ,SAASA,CAAC2M,WAAgB;IACxB,IAAIA,WAAW,CAAChM,WAAW,IAAIgM,WAAW,CAAChM,WAAW,CAACC,MAAM,GAAG,CAAC,EAAE;MACjE+L,WAAW,CAACjM,iBAAiB,GAAGiM,WAAW,CAACjM,iBAAiB,KAAK,CAAC,GAC/DiM,WAAW,CAAChM,WAAW,CAACC,MAAM,GAAG,CAAC,GAClC+L,WAAW,CAACjM,iBAAiB,GAAG,CAAC;IACvC;EACF;EACArB,eAAeA,CAACsN,WAAgB;IAC9B,IAAIA,WAAW,CAAChM,WAAW,IAAIgM,WAAW,CAAChM,WAAW,CAACC,MAAM,GAAG,CAAC,IAAI+L,WAAW,CAACjM,iBAAiB,KAAKmC,SAAS,EAAE;MAChH,OAAO8J,WAAW,CAAChM,WAAW,CAACgM,WAAW,CAACjM,iBAAiB,CAAC;IAC/D;IACA,OAAO,IAAI;EACb;EAEA;EACAO,cAAcA,CAAC0L,WAAgB,EAAEC,UAAmB;IAClD,IAAID,WAAW,CAAChM,WAAW,IAAIgM,WAAW,CAAChM,WAAW,CAACC,MAAM,GAAG,CAAC,EAAE;MACjE,IAAIgM,UAAU,KAAK/J,SAAS,EAAE;QAC5B8J,WAAW,CAACjM,iBAAiB,GAAGkM,UAAU;MAC5C;MACAD,WAAW,CAAChE,WAAW,GAAG,IAAI;MAC9B;MACA8B,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,QAAQ;IACzC;EACF;EAEA5C,eAAeA,CAAC2E,WAAgB;IAC9BA,WAAW,CAAChE,WAAW,GAAG,KAAK;IAC/B;IACA8B,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,MAAM;EACvC;EAEA;EACApD,cAAcA,CAACmF,WAAgB;IAC7B,IAAI,CAACrM,SAAS,CAACqM,WAAW,CAAC;EAC7B;EAEAtF,cAAcA,CAACsF,WAAgB;IAC7B,IAAI,CAAC3M,SAAS,CAAC2M,WAAW,CAAC;EAC7B;EAEA;EACAzE,SAASA,CAAC8C,KAAoB,EAAE2B,WAAgB;IAC9C,IAAIA,WAAW,CAAChE,WAAW,EAAE;MAC3B,QAAQqC,KAAK,CAAC6B,GAAG;QACf,KAAK,WAAW;UACd7B,KAAK,CAAC8B,cAAc,EAAE;UACtB,IAAI,CAACzF,cAAc,CAACsF,WAAW,CAAC;UAChC;QACF,KAAK,YAAY;UACf3B,KAAK,CAAC8B,cAAc,EAAE;UACtB,IAAI,CAACtF,cAAc,CAACmF,WAAW,CAAC;UAChC;QACF,KAAK,QAAQ;UACX3B,KAAK,CAAC8B,cAAc,EAAE;UACtB,IAAI,CAAC9E,eAAe,CAAC2E,WAAW,CAAC;UACjC;MACJ;IACF;EACF;EAEApJ,gBAAgBA,CAACwJ,OAAgB,EAAE9B,YAAiB;IAClDA,YAAY,CAAC3H,WAAW,GAAGyJ,OAAO;IAClC,IAAI,CAAC/I,aAAa,CAACgJ,OAAO,CAACjC,IAAI,IAAG;MAChCE,YAAY,CAACrH,aAAa,CAACmH,IAAI,CAAC,GAAGgC,OAAO;IAC5C,CAAC,CAAC;EACJ;EAEAlJ,6BAA6BA,CAACkJ,OAAgB,EAAEhC,IAAY,EAAEE,YAAiB;IAC7E,IAAI8B,OAAO,EAAE;MACX9B,YAAY,CAACrH,aAAa,CAACmH,IAAI,CAAC,GAAGgC,OAAO;MAC1C9B,YAAY,CAAC3H,WAAW,GAAG,IAAI,CAACU,aAAa,CAACiJ,KAAK,CAAClC,IAAI,IAAIE,YAAY,CAACrH,aAAa,CAACmH,IAAI,CAAC,IAAIgC,OAAO,CAAC;IAC1G,CAAC,MAAM;MACL9B,YAAY,CAAC3H,WAAW,GAAG,KAAK;IAClC;EACF;EAIAe,sBAAsBA,CAAC0I,OAAgB,EAAEhC,IAAY,EAAEE,YAAiB;IACtEA,YAAY,CAAC7G,kBAAkB,CAAC2G,IAAI,CAAC,GAAGgC,OAAO;EACjD;EAEAG,kBAAkBA,CAAC1I,kBAA4B,EAAE2I,WAAmB;IAClE,MAAMC,YAAY,GAA+B,EAAE;IACnD,KAAK,MAAMC,MAAM,IAAI7I,kBAAkB,EAAE;MACvC4I,YAAY,CAACC,MAAM,CAAC,GAAG,KAAK;IAC9B;IACA,MAAMC,WAAW,GAAGH,WAAW,CAACtB,KAAK,CAAC,GAAG,CAAC;IAC1C,KAAK,MAAMW,IAAI,IAAIc,WAAW,EAAE;MAC9B,IAAI9I,kBAAkB,CAAC+I,QAAQ,CAACf,IAAI,CAAC,EAAE;QACrCY,YAAY,CAACZ,IAAI,CAAC,GAAG,IAAI;MAC3B;IACF;IACA,OAAOY,YAAY;EACrB;EAEAI,UAAUA,CAACC,KAAoC;IAC7C,MAAMC,GAAG,GAAG,IAAIC,GAAG,EAAgE;IAEnFF,KAAK,CAACT,OAAO,CAACjC,IAAI,IAAG;MACnB,MAAM8B,GAAG,GAAG,GAAG9B,IAAI,CAACpE,SAAS,IAAIoE,IAAI,CAACtE,KAAK,IAAIsE,IAAI,CAACrE,KAAK,EAAE;MAC3D,IAAIgH,GAAG,CAACE,GAAG,CAACf,GAAG,CAAC,EAAE;QAChB,MAAMgB,QAAQ,GAAGH,GAAG,CAACzD,GAAG,CAAC4C,GAAG,CAAE;QAC9BgB,QAAQ,CAACC,KAAK,IAAI,CAAC;MACrB,CAAC,MAAM;QACLJ,GAAG,CAACK,GAAG,CAAClB,GAAG,EAAE;UAAE9B,IAAI,EAAE;YAAE,GAAGA;UAAI,CAAE;UAAE+C,KAAK,EAAE;QAAC,CAAE,CAAC;MAC/C;IACF,CAAC,CAAC;IAEF,OAAOE,KAAK,CAACC,IAAI,CAACP,GAAG,CAACQ,MAAM,EAAE,CAAC,CAACR,GAAG,CAAC,CAAC;MAAE3C,IAAI;MAAE+C;IAAK,CAAE,MAAM;MACxD,GAAG/C,IAAI;MACPoD,YAAY,EAAEL;KACf,CAAC,CAAC;EACL;EAGAM,eAAeA,CAAA;IACb,IAAI,CAAC9E,gBAAgB,CAAC+E,mCAAmC,CAAC;MACxD3D,IAAI,EAAE;QACJ4D,YAAY,EAAE,IAAI,CAACpE,WAAW;QAC9BqE,KAAK,EAAE;;KAEV,CAAC,CAACC,IAAI,CACL7P,GAAG,CAAC8P,GAAG,IAAG;MACR,IAAIA,GAAG,CAACC,OAAO,IAAID,GAAG,CAACE,UAAU,IAAI,CAAC,EAAE;QAEtC,IAAI,CAAC3K,aAAa,CAACgJ,OAAO,CAACjC,IAAI,IAAI,IAAI,CAACnH,aAAa,CAACmH,IAAI,CAAC,GAAG,KAAK,CAAC;QAEpE,IAAI,CAACvG,kBAAkB,CAACwI,OAAO,CAACjC,IAAI,IAAI,IAAI,CAAC3G,kBAAkB,CAAC2G,IAAI,CAAC,GAAG,KAAK,CAAC;QAE9E,IAAI,CAAC6D,kBAAkB,GAAGH,GAAG,CAACC,OAAO,CAAChB,GAAG,CAAEmB,CAAM,IAAI;UACnD,OAAO;YACL5L,cAAc,EAAE,IAAI;YACpB6L,kBAAkB,EAAE,IAAI;YACxBC,OAAO,EAAE,IAAI;YACbpI,SAAS,EAAEkI,CAAC,CAAClI,SAAS;YACtBF,KAAK,EAAEoI,CAAC,CAACpI,KAAK;YACdC,KAAK,EAAEmI,CAAC,CAACnI,KAAK;YACdxB,SAAS,EAAE,GAAG2J,CAAC,CAACpI,KAAK,IAAIoI,CAAC,CAACnI,KAAK,IAAImI,CAAC,CAAClI,SAAS,EAAE;YACjDwG,WAAW,EAAE,IAAI;YACjBgB,YAAY,EAAE,CAAC;YACf/I,cAAc,EAAE,CAAC;YACjB4J,OAAO,EAAE,CAAC;YAAEpL,aAAa,EAAE,EAAE;YAC7BQ,kBAAkB,EAAE,IAAI,CAACA,kBAAkB;YAC3Cd,WAAW,EAAE,KAAK;YAClBN,YAAY,EAAE,EAAE;YAAEsC,eAAe,EAAE,IAAI,CAAC0B,cAAc,CAAC,CAAC,CAAC;YACzDrG,WAAW,EAAEkO,CAAC,CAACI,QAAQ,GAAG,CAACJ,CAAC,CAACI,QAAQ,CAAC,GAAG,EAAE;YAC3CvO,iBAAiB,EAAE,CAAC;YACpBiI,WAAW,EAAE;WACd;QACH,CAAC,CAAC;QACF,IAAI,CAACiG,kBAAkB,GAAG,CAAC,GAAG,IAAI,CAACpB,UAAU,CAAC,IAAI,CAACoB,kBAAkB,CAAC,CAAC;MACzE;IACF,CAAC,CAAC,CACH,CAAC9E,SAAS,EAAE;EACf;EAKAoF,eAAeA,CAAA;IACb,IAAI,CAACjG,gBAAgB,CAACkG,mCAAmC,CAAC;MACxDzE,IAAI,EAAE;QACJ4D,YAAY,EAAE,IAAI,CAACpE,WAAW;QAC9BT,SAAS,EAAE,IAAI,CAACD,iCAAiC,CAACC,SAAS;QAC3D2F,SAAS,EAAE;;KAEd,CAAC,CAACZ,IAAI,CACL7P,GAAG,CAAC8P,GAAG,IAAG;MACR,IAAIA,GAAG,CAACC,OAAO,IAAID,GAAG,CAACE,UAAU,IAAI,CAAC,EAAE;QACtC,IAAI,CAAChM,YAAY,GAAG8L,GAAG,CAACC,OAAO;QAC/B,IAAI,CAAC/E,KAAK,GAAG8E,GAAG,CAACC,OAAO,CAACW,SAAS,GAAG,KAAK,GAAG,IAAI;QACjD,IAAIZ,GAAG,CAACC,OAAO,CAACW,SAAS,EAAE;UACzB,IAAI,CAACrL,aAAa,CAACgJ,OAAO,CAACjC,IAAI,IAAI,IAAI,CAACnH,aAAa,CAACmH,IAAI,CAAC,GAAG,KAAK,CAAC;UACpE,IAAI,CAACvG,kBAAkB,CAACwI,OAAO,CAACjC,IAAI,IAAI,IAAI,CAAC3G,kBAAkB,CAAC2G,IAAI,CAAC,GAAG,KAAK,CAAC;UAE9E,IAAI,CAAC6D,kBAAkB,GAAGH,GAAG,CAACC,OAAO,CAACW,SAAS,CAAC3B,GAAG,CAAEmB,CAAM,IAAI;YAC7D,OAAO;cACLE,OAAO,EAAE,IAAI,CAACpM,YAAY,CAACoM,OAAO;cAClC9L,cAAc,EAAE4L,CAAC,CAAC5L,cAAc;cAChCtC,WAAW,EAAEkO,CAAC,CAAClO,WAAW,KAAKkO,CAAC,CAACS,gBAAgB,GAAG,CAACT,CAAC,CAACS,gBAAgB,CAAC,GAAG,EAAE,CAAC;cAC9EtD,KAAK,EAAE6C,CAAC,CAAC7C,KAAK;cACd8C,kBAAkB,EAAED,CAAC,CAACC,kBAAkB;cACxCS,WAAW,EAAEV,CAAC,CAACU,WAAW;cAC1B5I,SAAS,EAAEkI,CAAC,CAAClI,SAAS;cACtBF,KAAK,EAAEoI,CAAC,CAACpI,KAAK;cACdC,KAAK,EAAEmI,CAAC,CAACnI,KAAK;cACdxB,SAAS,EAAE2J,CAAC,CAAC3J,SAAS,GAAG2J,CAAC,CAAC3J,SAAS,GAAG,GAAG2J,CAAC,CAACpI,KAAK,IAAIoI,CAAC,CAACnI,KAAK,IAAImI,CAAC,CAAClI,SAAS,EAAE;cAC7EwG,WAAW,EAAE0B,CAAC,CAAC1B,WAAW;cAC1BgB,YAAY,EAAEU,CAAC,CAACV,YAAY;cAC5B/I,cAAc,EAAEyJ,CAAC,CAACG,OAAO,KAAK,CAAC,GAAG,CAAC,GAAGH,CAAC,CAACzJ,cAAc;cACtD4J,OAAO,EAAEH,CAAC,CAACG,OAAO;cAClBpL,aAAa,EAAEiL,CAAC,CAACW,qBAAqB,CAAC5O,MAAM,GAAG,IAAI,CAAC6O,0BAA0B,CAAC,IAAI,CAACzL,aAAa,EAAE6K,CAAC,CAACW,qBAAqB,CAAC,GAAG;gBAAE,GAAG,IAAI,CAAC5L;cAAa,CAAE;cAAEQ,kBAAkB,EAAEyK,CAAC,CAAC1B,WAAW,GAAG,IAAI,CAACD,kBAAkB,CAAC,IAAI,CAAC1I,kBAAkB,EAAEqK,CAAC,CAAC1B,WAAW,CAAC,GAAG;gBAAE,GAAG,IAAI,CAAC/I;cAAkB,CAAE;cAC9Rd,WAAW,EAAEuL,CAAC,CAACW,qBAAqB,CAAC5O,MAAM,KAAK,IAAI,CAACoD,aAAa,CAACpD,MAAM;cACzEoC,YAAY,EAAE,EAAE;cAAEsC,eAAe,EAAEuJ,CAAC,CAACG,OAAO,GAAG,IAAI,CAACnE,cAAc,CAACgE,CAAC,CAACG,OAAO,EAAE,IAAI,CAAChI,cAAc,CAAC,GAAG,IAAI,CAACA,cAAc,CAAC,CAAC,CAAC;cAC3HtG,iBAAiB,EAAE,CAAC;cACpBiI,WAAW,EAAE;aACd;UACH,CAAC,CAAC;QACJ,CAAC,MAAM;UACL,IAAI,CAACyF,eAAe,EAAE;QACxB;MACF;IACF,CAAC,CAAC,CACH,CAACtE,SAAS,EAAE;EACf;EAEAtE,mBAAmBA,CAACmH,WAAgB;IAClC,IAAIA,WAAW,CAACrH,eAAe,IAAIqH,WAAW,CAACrH,eAAe,CAACuB,KAAK,KAAK,CAAC,EAAE;MAC1E8F,WAAW,CAACvH,cAAc,GAAG,CAAC;IAChC;EACF;EACAsK,4BAA4BA,CAAClN,IAAW;IACtC,KAAK,IAAIuI,IAAI,IAAIvI,IAAI,EAAE;MACrB,IAAIuI,IAAI,CAACrB,WAAW,KAAK,IAAI,CAACF,iCAAiC,CAACE,WAAW,EAAE;QAC3E,OAAOqB,IAAI,CAAC4E,cAAc;MAC5B;IACF;IACA,OAAO,EAAE;EACX;EAIAC,oBAAoBA,CAACC,GAA4B;IAC/C,OAAOC,MAAM,CAACC,IAAI,CAACF,GAAG,CAAC,CAAC1D,MAAM,CAACU,GAAG,IAAIgD,GAAG,CAAChD,GAAG,CAAC,CAAC;EACjD;EAEAmD,0BAA0BA,CAACH,GAA4B;IACrD,OAAOC,MAAM,CAACC,IAAI,CAACF,GAAG,CAAC,CACpB1D,MAAM,CAACU,GAAG,IAAIgD,GAAG,CAAChD,GAAG,CAAC,CAAC,CACvBoD,IAAI,CAAC,GAAG,CAAC;EACd;EAEAC,cAAcA,CAAC5K,eAAoB,EAAElB,kBAAuB;IAC1D,IAAIkB,eAAe,IAAIA,eAAe,CAACuB,KAAK,IAAI,CAAC,EAAE;MACjD,OAAO,IAAI,CAACmJ,0BAA0B,CAAC5L,kBAAkB,CAAC;IAC5D;EACF;EAEA+L,mBAAmBA,CAACC,WAAmB;IACrC,MAAMC,KAAK,GAAGD,WAAW,CAACvE,KAAK,CAAC,GAAG,CAAC;IACpC,IAAIwE,KAAK,CAACzP,MAAM,GAAG,CAAC,EAAE;MACpB,OAAOyP,KAAK,CAAC,CAAC,CAAC;IACjB,CAAC,MAAM,OAAO,EAAE;EAClB;EAEAC,UAAUA,CAACtN,YAAiB;IAC1B,IAAIA,YAAY,IAAIA,YAAY,CAACpC,MAAM,GAAG,CAAC,EAAE;MAC3C,OAAO;QACL2P,YAAY,EAAE,IAAI,CAACJ,mBAAmB,CAACnN,YAAY,CAAC,CAAC,CAAC,CAACR,IAAI,CAAC,IAAI,IAAI;QACpEgO,aAAa,EAAExN,YAAY,CAAC,CAAC,CAAC,CAAC8I,SAAS,IAAI,IAAI;QAChD2E,QAAQ,EAAEzN,YAAY,CAAC,CAAC,CAAC,CAACgJ,KAAK,CAACvJ,IAAI,IAAIO,YAAY,CAAC,CAAC,CAAC,CAACP,IAAI,IAAI;OACjE;IACH,CAAC,MAAM,OAAOI,SAAS;EAEzB;EAGA6N,UAAUA,CAAA;IACR,IAAI,CAACtH,KAAK,CAACuH,KAAK,EAAE;IAClB,IAAIC,iBAAiB,GAAG,KAAK;IAC7B,IAAIC,wBAAwB,GAAG,KAAK;IACpC,IAAIC,kBAAkB,GAAG,KAAK;IAE9B,KAAK,MAAM/F,IAAI,IAAI,IAAI,CAACgG,mBAAmB,EAAE;MAC3C,IAAI,CAACH,iBAAiB,IAAK,CAAC7F,IAAI,CAACiE,OAAQ,EAAE;QACzC4B,iBAAiB,GAAG,IAAI;MAC1B;MACA,IAAI,CAACC,wBAAwB,IAAK,CAAC9F,IAAI,CAAC3F,cAAe,EAAE;QACvDyL,wBAAwB,GAAG,IAAI;MACjC;MACA,IAAI9F,IAAI,CAACoD,YAAY,IAAIpD,IAAI,CAAC3F,cAAc,EAAE;QAC5C,IAAI2F,IAAI,CAAC3F,cAAc,GAAG2F,IAAI,CAACoD,YAAY,IAAIpD,IAAI,CAAC3F,cAAc,GAAG,CAAC,EAAE;UACtE,IAAI,CAACgE,KAAK,CAAC4H,eAAe,CAAC,QAAQ,GAAG,MAAM,GAAGjG,IAAI,CAACoD,YAAY,GAAG,KAAKpD,IAAI,CAAC7F,SAAS,IAAI,CAAC;QAC7F;MACF;MAEA,IAAI,CAAC4L,kBAAkB,IAAK,CAAC/F,IAAI,CAAC7F,SAAU,EAAE;QAC5C4L,kBAAkB,GAAG,IAAI;MAC3B;IACF;IACA,IAAIF,iBAAiB,EAAE;MACrB,IAAI,CAACxH,KAAK,CAAC4H,eAAe,CAAC,UAAU,GAAG,KAAK,CAAC;IAChD;IACA,IAAIH,wBAAwB,EAAE;MAC5B,IAAI,CAACzH,KAAK,CAAC4H,eAAe,CAAC,QAAQ,GAAG,QAAQ,CAAC;IACjD;IACA,IAAIF,kBAAkB,EAAE;MACtB,IAAI,CAAC1H,KAAK,CAAC4H,eAAe,CAAC,YAAY,GAAG,KAAK,CAAC;IAClD;EACF;EAIAC,QAAQA,CAAA;IACN,IAAI,CAACF,mBAAmB,GAAG,IAAI,CAACnC,kBAAkB,CAAClB,GAAG,CAAEwD,CAAM,IAAI;MAChE,OAAO;QACLjO,cAAc,EAAEiO,CAAC,CAACjO,cAAc,GAAGiO,CAAC,CAACjO,cAAc,GAAG,IAAI;QAC1D+I,KAAK,EAAEkF,CAAC,CAAClO,YAAY,GAAG,IAAI,CAACsN,UAAU,CAACY,CAAC,CAAClO,YAAY,CAAC,GAAGH,SAAS;QACnEiM,kBAAkB,EAAE,IAAI,CAACc,oBAAoB,CAACsB,CAAC,CAACtN,aAAa,CAAC;QAC9D2L,WAAW,EAAE2B,CAAC,CAAC3B,WAAW,GAAG2B,CAAC,CAAC3B,WAAW,GAAG,IAAI;QACjD4B,OAAO,EAAE,IAAI,CAACxH,KAAK,GAAG,IAAI,GAAG,IAAI,CAAChH,YAAY,CAACoM,OAAO;QACtDtI,KAAK,EAAEyK,CAAC,CAACzK,KAAK;QACdC,KAAK,EAAEwK,CAAC,CAACxK,KAAK;QACdC,SAAS,EAAEuK,CAAC,CAACvK,SAAS;QACtBzB,SAAS,EAAEgM,CAAC,CAAChM,SAAS;QAAE;QACxBiI,WAAW,EAAE+D,CAAC,CAAC5L,eAAe,CAACuB,KAAK,KAAK,CAAC,GAAG,IAAI,GAAG,IAAI,CAACqJ,cAAc,CAACgB,CAAC,CAAC5L,eAAe,EAAE4L,CAAC,CAAC9M,kBAAkB,CAAC,IAAI,IAAI;QACxH+J,YAAY,EAAE+C,CAAC,CAAC/C,YAAY;QAC5B/I,cAAc,EAAE8L,CAAC,CAAC9L,cAAc;QAChC4J,OAAO,EAAEkC,CAAC,CAAC5L,eAAe,CAACuB;OAC5B;IACH,CAAC,CAAC;IACF,IAAI,CAAC6J,UAAU,EAAE;IACjB,IAAI,IAAI,CAACtH,KAAK,CAACgI,aAAa,CAACxQ,MAAM,GAAG,CAAC,EAAE;MACvC,IAAI,CAACoI,OAAO,CAACqI,aAAa,CAAC,IAAI,CAACjI,KAAK,CAACgI,aAAa,CAAC;MACpD;IACF;IACA,IAAI,IAAI,CAACzH,KAAK,EAAE;MACd,IAAI,CAAC2H,kBAAkB,EAAE;IAE3B,CAAC,MAAM;MACL,IAAI,CAACC,gBAAgB,EAAE;IACzB;EACF;EAEAA,gBAAgBA,CAAA;IACd,IAAI,CAACtI,gBAAgB,CAACuI,oCAAoC,CAAC;MACzD9G,IAAI,EAAE,IAAI,CAACqG;KACZ,CAAC,CAACjH,SAAS,CAAC2E,GAAG,IAAG;MACjB,IAAIA,GAAG,CAACE,UAAU,IAAI,CAAC,EAAE;QACvB,IAAI,CAAC3F,OAAO,CAACyI,aAAa,CAAC,MAAM,CAAC;QAClC;QACA,IAAI,CAAClH,MAAM,EAAE;MACf;IACF,CAAC,CAAC;EACJ;EAIA+G,kBAAkBA,CAAA;IAChB,IAAI,CAACI,iBAAiB,GAAG;MACvBpD,YAAY,EAAE,IAAI,CAACpE,WAAW;MAC9ByH,SAAS,EAAE,IAAI,CAACZ,mBAAmB,IAAI,IAAI;MAC3CtH,SAAS,EAAE,IAAI,CAACD,iCAAiC,CAACC;KACnD;IAED,IAAI,CAACR,gBAAgB,CAAC2I,sCAAsC,CAAC;MAC3DlH,IAAI,EAAE,IAAI,CAACgH;KACZ,CAAC,CAAC5H,SAAS,CAAC2E,GAAG,IAAG;MACjB,IAAIA,GAAG,CAACE,UAAU,IAAI,CAAC,EAAE;QACvB,IAAI,CAAC3F,OAAO,CAACyI,aAAa,CAAC,MAAM,CAAC;QAClC;QACA,IAAI,CAAClH,MAAM,EAAE;MACf;IACF,CAAC,CAAC;EACJ;EAEAkF,0BAA0BA,CAACoC,CAAW,EAAEC,CAAkG;IACxI,MAAMC,CAAC,GAA+B,EAAE;IACxC,KAAK,MAAMhH,IAAI,IAAI8G,CAAC,EAAE;MACpB,MAAMG,YAAY,GAAGF,CAAC,CAACG,IAAI,CAACC,KAAK,IAAIA,KAAK,CAACC,UAAU,KAAKpH,IAAI,IAAImH,KAAK,CAACE,SAAS,CAAC;MAClFL,CAAC,CAAChH,IAAI,CAAC,GAAG,CAAC,CAACiH,YAAY;IAC1B;IACA,OAAOD,CAAC;EACV,CAAC,CAAC;EAKF5H,iCAAiCA,CAAA;IAC/B,IAAI,CAACjB,yBAAyB,CAACmJ,8DAA8D,CAAC;MAC5F3H,IAAI,EAAE,IAAI,CAACR;KACZ,CAAC,CAACsE,IAAI,CACL7P,GAAG,CAAC8P,GAAG,IAAG;MACR,IAAIA,GAAG,CAACC,OAAO,IAAID,GAAG,CAACE,UAAU,IAAI,CAAC,EAAE;QACtC,IAAI,CAAC3K,aAAa,GAAG,IAAI,CAAC0L,4BAA4B,CAACjB,GAAG,CAACC,OAAO,CAAC;QACnE,IAAI,CAACQ,eAAe,EAAE;MACxB;IACF,CAAC,CAAC,CACH,CAACpF,SAAS,EAAE;EACf;EACAS,MAAMA,CAAA;IACJ,IAAI,CAAChB,aAAa,CAAC0C,IAAI,CAAC;MACtBqG,MAAM;MACNC,OAAO,EAAE,IAAI,CAACrI;KACf,CAAC;IACF,IAAI,CAACb,QAAQ,CAACmJ,IAAI,EAAE;EACtB;;;uCA7fW5J,4CAA4C,EAAA5J,EAAA,CAAAyT,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAA3T,EAAA,CAAAyT,iBAAA,CAAAG,EAAA,CAAAC,cAAA,GAAA7T,EAAA,CAAAyT,iBAAA,CAAAK,EAAA,CAAAC,cAAA,GAAA/T,EAAA,CAAAyT,iBAAA,CAAAO,EAAA,CAAAC,eAAA,GAAAjU,EAAA,CAAAyT,iBAAA,CAAAO,EAAA,CAAAE,wBAAA,GAAAlU,EAAA,CAAAyT,iBAAA,CAAAU,EAAA,CAAAC,cAAA,GAAApU,EAAA,CAAAyT,iBAAA,CAAAY,EAAA,CAAAC,gBAAA,GAAAtU,EAAA,CAAAyT,iBAAA,CAAAc,EAAA,CAAAC,QAAA,GAAAxU,EAAA,CAAAyT,iBAAA,CAAAO,EAAA,CAAAS,eAAA,GAAAzU,EAAA,CAAAyT,iBAAA,CAAAiB,EAAA,CAAAC,YAAA;IAAA;EAAA;;;YAA5C/K,4CAA4C;MAAAgL,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAA9U,EAAA,CAAA+U,0BAAA,EAAA/U,EAAA,CAAAgV,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,sDAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UC7CjDtV,EAJR,CAAAQ,cAAA,aAAqE,iBACJ,wBACC,aACb,aACJ;UACvCR,EAAA,CAAAC,SAAA,aAAqD;UACrDD,EAAA,CAAAQ,cAAA,UAAK;UACHR,EAAA,CAAAC,SAAA,qBAAiC;UACjCD,EAAA,CAAAQ,cAAA,YAAkD;UAAAR,EAAA,CAAAuB,MAAA,gEAAY;UAElEvB,EAFkE,CAAAmB,YAAA,EAAK,EAC/D,EACF;UAEJnB,EADF,CAAAQ,cAAA,eAAyC,gBAC8C;UACnFR,EAAA,CAAAuB,MAAA,8CACF;UAGNvB,EAHM,CAAAmB,YAAA,EAAO,EACH,EACF,EACS;UAOTnB,EALR,CAAAQ,cAAA,wBAAqC,eACZ,eAEiD,eAC3B,eACwC;;UAC7ER,EAAA,CAAAQ,cAAA,eAAyF;UACvFR,EAAA,CAAAC,SAAA,gBAAsM;UAE1MD,EADE,CAAAmB,YAAA,EAAM,EACF;;UAEJnB,EADF,CAAAQ,cAAA,WAAK,cACyC;UAAAR,EAAA,CAAAuB,MAAA,+CAAS;UAAAvB,EAAA,CAAAmB,YAAA,EAAK;UAC1DnB,EAAA,CAAAQ,cAAA,aAAsC;UAAAR,EAAA,CAAAuB,MAAA,4FAAc;UAG1DvB,EAH0D,CAAAmB,YAAA,EAAI,EACpD,EACF,EACF;UACNnB,EAAA,CAAAoC,UAAA,KAAAoT,qEAAA,6BAA8E;UAwTlFxV,EADE,CAAAmB,YAAA,EAAM,EACO;UAKXnB,EAFJ,CAAAQ,cAAA,0BAAwF,cACvC,eACkB;;UAC7DR,EAAA,CAAAQ,cAAA,eAA2E;UACzER,EAAA,CAAAC,SAAA,gBAA2I;UAC7ID,EAAA,CAAAmB,YAAA,EAAM;;UACNnB,EAAA,CAAAQ,cAAA,YAAM;UAAAR,EAAA,CAAAuB,MAAA,IAA0C;UAClDvB,EADkD,CAAAmB,YAAA,EAAO,EACnD;UAGJnB,EADF,CAAAQ,cAAA,cAAyC,kBAEyB;UAA9DR,EAAA,CAAAS,UAAA,mBAAAgV,+EAAA;YAAA,OAASF,GAAA,CAAAhK,MAAA,EAAQ;UAAA,EAAC;;UAClBvL,EAAA,CAAAQ,cAAA,eAA2E;UACzER,EAAA,CAAAC,SAAA,gBAA6G;UAC/GD,EAAA,CAAAmB,YAAA,EAAM;;UACNnB,EAAA,CAAAQ,cAAA,YAAM;UAAAR,EAAA,CAAAuB,MAAA,oBAAE;UACVvB,EADU,CAAAmB,YAAA,EAAO,EACR;UAETnB,EAAA,CAAAQ,cAAA,kBACkE;UAAhER,EAAA,CAAAS,UAAA,mBAAAiV,+EAAA;YAAA,OAASH,GAAA,CAAAtD,QAAA,EAAU;UAAA,EAAC;;UACpBjS,EAAA,CAAAQ,cAAA,eAA2E;UACzER,EAAA,CAAAC,SAAA,gBAAgG;UAClGD,EAAA,CAAAmB,YAAA,EAAM;;UACNnB,EAAA,CAAAQ,cAAA,YAAM;UAAAR,EAAA,CAAAuB,MAAA,gCAAI;UAMtBvB,EANsB,CAAAmB,YAAA,EAAO,EACV,EACL,EACF,EACS,EACT,EACN;UAGNnB,EAAA,CAAAoC,UAAA,KAAAuT,qEAAA,2BAA8E;;;;;UA3VhC3V,EAAA,CAAAwB,SAAA,IAAuB;UAAvBxB,EAAA,CAAAE,UAAA,YAAAqV,GAAA,CAAA3F,kBAAA,CAAuB;UAiUrD5P,EAAA,CAAAwB,SAAA,GAA0C;UAA1CxB,EAAA,CAAA8C,kBAAA,YAAAyS,GAAA,CAAA3F,kBAAA,CAAAhO,MAAA,yCAA0C;UAK3B5B,EAAA,CAAAwB,SAAA,GAA0C;UAA1CxB,EAAA,CAAAE,UAAA,cAAA0V,OAAA,GAAAL,GAAA,CAAA5R,YAAA,CAAAC,OAAA,cAAAgS,OAAA,KAAA/R,SAAA,GAAA+R,OAAA,SAA0C;UAQxC5V,EAAA,CAAAwB,SAAA,GAA0C;UAA1CxB,EAAA,CAAAE,UAAA,cAAA2V,OAAA,GAAAN,GAAA,CAAA5R,YAAA,CAAAC,OAAA,cAAAiS,OAAA,KAAAhS,SAAA,GAAAgS,OAAA,SAA0C;UAarC7V,EAAA,CAAAwB,SAAA,GAAuB;UAAvBxB,EAAA,CAAAE,UAAA,YAAAqV,GAAA,CAAA3F,kBAAA,CAAuB;;;qBDhVjDnQ,YAAY,EAAA8U,EAAA,CAAAuB,OAAA,EAAAvB,EAAA,CAAAwB,IAAA,EAAEnW,YAAY,EAAAoW,EAAA,CAAAC,oBAAA,EAAAD,EAAA,CAAAE,mBAAA,EAAAF,EAAA,CAAAG,eAAA,EAAAH,EAAA,CAAAI,OAAA,EAAAC,GAAA,CAAAC,eAAA,EAAAD,GAAA,CAAAE,mBAAA,EAAAF,GAAA,CAAAG,qBAAA,EAAAH,GAAA,CAAAI,qBAAA,EAAAJ,GAAA,CAAAK,mBAAA,EAAAL,GAAA,CAAAM,gBAAA,EAAAN,GAAA,CAAAO,iBAAA,EAAAP,GAAA,CAAAQ,iBAAA,EAAAC,GAAA,CAAAC,mBAAA,EAAAC,GAAA,CAAAC,YAAA,EAAEvX,gBAAgB,EAAgBK,eAAe;MAAAmX,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}