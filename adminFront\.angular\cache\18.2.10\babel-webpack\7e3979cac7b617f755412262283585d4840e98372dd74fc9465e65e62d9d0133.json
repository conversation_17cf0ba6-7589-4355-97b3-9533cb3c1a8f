{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { concatMap, finalize, tap } from 'rxjs';\nimport { BaseComponent } from '../../components/base/baseComponent';\nimport { SharedModule } from '../../components/shared.module';\nimport * as _ from 'lodash';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/shared/helper/allowHelper\";\nimport * as i2 from \"@nebular/theme\";\nimport * as i3 from \"src/app/shared/services/message.service\";\nimport * as i4 from \"src/app/shared/helper/validationHelper\";\nimport * as i5 from \"src/services/api/services\";\nimport * as i6 from \"@angular/router\";\nimport * as i7 from \"src/app/shared/services/utility.service\";\nimport * as i8 from \"@angular/common\";\nimport * as i9 from \"@angular/forms\";\nimport * as i10 from \"@ng-bootstrap/ng-bootstrap\";\nimport * as i11 from \"../../components/breadcrumb/breadcrumb.component\";\nimport * as i12 from \"../../../@theme/directives/label.directive\";\nconst _c0 = [\"fileInput\"];\nfunction ProjectManagementComponent_input_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"input\", 20);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function ProjectManagementComponent_input_10_Template_input_ngModelChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.search, $event) || (ctx_r2.search = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.search);\n  }\n}\nfunction ProjectManagementComponent_button_16_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 21);\n    i0.ɵɵlistener(\"click\", function ProjectManagementComponent_button_16_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r2 = i0.ɵɵnextContext();\n      const dialog_r5 = i0.ɵɵreference(32);\n      return i0.ɵɵresetView(ctx_r2.addNew(dialog_r5));\n    });\n    i0.ɵɵelement(1, \"i\", 22);\n    i0.ɵɵtext(2, \" \\u65B0\\u589E\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProjectManagementComponent_tr_28_button_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 26);\n    i0.ɵɵlistener(\"click\", function ProjectManagementComponent_tr_28_button_6_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const item_r7 = i0.ɵɵnextContext().$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      const dialog_r5 = i0.ɵɵreference(32);\n      return i0.ɵɵresetView(ctx_r2.onSelectedBuildCase(item_r7, dialog_r5));\n    });\n    i0.ɵɵtext(1, \"\\u7DE8\\u8F2F\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProjectManagementComponent_tr_28_button_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 26);\n    i0.ɵɵlistener(\"click\", function ProjectManagementComponent_tr_28_button_7_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const item_r7 = i0.ɵɵnextContext().$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onRelatedDocument(item_r7.cID));\n    });\n    i0.ɵɵtext(1, \"\\u5EFA\\u6848\\u516C\\u4F48\\u6B04\\u6587\\u4EF6\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProjectManagementComponent_tr_28_button_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 27);\n    i0.ɵɵlistener(\"click\", function ProjectManagementComponent_tr_28_button_8_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const item_r7 = i0.ɵɵnextContext().$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      const dialog_r5 = i0.ɵɵreference(32);\n      return i0.ɵɵresetView(ctx_r2.onDelete(item_r7, dialog_r5));\n    });\n    i0.ɵɵtext(1, \"\\u522A\\u9664\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProjectManagementComponent_tr_28_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\", 23);\n    i0.ɵɵtemplate(6, ProjectManagementComponent_tr_28_button_6_Template, 2, 0, \"button\", 24)(7, ProjectManagementComponent_tr_28_button_7_Template, 2, 0, \"button\", 24)(8, ProjectManagementComponent_tr_28_button_8_Template, 2, 0, \"button\", 25);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const item_r7 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r7.cID);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r7.CBuildCaseName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isRead);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isRead);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isDelete);\n  }\n}\nfunction ProjectManagementComponent_ng_template_31_div_25_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 57)(1, \"span\", 58);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"button\", 59);\n    i0.ɵɵlistener(\"click\", function ProjectManagementComponent_ng_template_31_div_25_Template_button_click_3_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.clearImage());\n    });\n    i0.ɵɵelement(4, \"i\", 60);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r2.fileName);\n  }\n}\nfunction ProjectManagementComponent_ng_template_31_img_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 61);\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"src\", ctx_r2.imageUrl, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction ProjectManagementComponent_ng_template_31_img_27_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 61);\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"src\", ctx_r2.selectedBuildCase.CFrontImage, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction ProjectManagementComponent_ng_template_31_nb_option_36_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 62);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const status_r12 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", status_r12);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", status_r12.CTitle, \" \");\n  }\n}\nfunction ProjectManagementComponent_ng_template_31_nb_option_41_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 62);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const status_r13 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", status_r13);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", status_r13.CTitle, \" \");\n  }\n}\nfunction ProjectManagementComponent_ng_template_31_nb_option_46_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 62);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const status_r14 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", status_r14);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", status_r14.CTitle, \" \");\n  }\n}\nfunction ProjectManagementComponent_ng_template_31_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-card\", 28)(1, \"nb-card-header\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"nb-card-body\", 29)(4, \"div\", 30)(5, \"label\", 31);\n    i0.ɵɵtext(6, \"\\u5EFA\\u6848\\u540D\\u7A31 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"input\", 32);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function ProjectManagementComponent_ng_template_31_Template_input_ngModelChange_7_listener($event) {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.selectedBuildCase.CBuildCaseName, $event) || (ctx_r2.selectedBuildCase.CBuildCaseName = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 33)(9, \"div\", 34)(10, \"label\", 35);\n    i0.ɵɵtext(11, \"\\u524D\\u53F0\\u5716\\u7247 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"h3\", 36);\n    i0.ɵɵtext(13, \"\\u53EA\\u63A5\\u53D7pdf, \\u5716\\u7247\\u6A94\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"h3\", 36);\n    i0.ɵɵtext(15, \"\\u5EFA\\u8B70\\u5C3A\\u5BF8:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"h3\", 36);\n    i0.ɵɵtext(17, \"\\u76F4\\u5F0F 1080 x 1440 px\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"h3\", 36);\n    i0.ɵɵtext(19, \"\\u6A6B\\u5F0F 1440 x 1080 px\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(20, \"div\", 37)(21, \"input\", 38);\n    i0.ɵɵlistener(\"change\", function ProjectManagementComponent_ng_template_31_Template_input_change_21_listener($event) {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onFileSelected($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"label\", 39);\n    i0.ɵɵelement(23, \"i\", 40);\n    i0.ɵɵtext(24, \" \\u4E0A\\u50B3 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(25, ProjectManagementComponent_ng_template_31_div_25_Template, 5, 1, \"div\", 41)(26, ProjectManagementComponent_ng_template_31_img_26_Template, 1, 1, \"img\", 42)(27, ProjectManagementComponent_ng_template_31_img_27_Template, 1, 1, \"img\", 42);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(28, \"div\", 43)(29, \"label\", 44);\n    i0.ɵɵtext(30, \"\\u7CFB\\u7D71\\u64CD\\u4F5C\\u8AAA\\u660E \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(31, \"textarea\", 45);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function ProjectManagementComponent_ng_template_31_Template_textarea_ngModelChange_31_listener($event) {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.selectedBuildCase.CSystemInstruction, $event) || (ctx_r2.selectedBuildCase.CSystemInstruction = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(32, \"div\", 46)(33, \"label\", 47);\n    i0.ɵɵtext(34, \"\\u72C0\\u614B\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(35, \"nb-select\", 48);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function ProjectManagementComponent_ng_template_31_Template_nb_select_ngModelChange_35_listener($event) {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.selectedStatus, $event) || (ctx_r2.selectedStatus = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtemplate(36, ProjectManagementComponent_ng_template_31_nb_option_36_Template, 2, 2, \"nb-option\", 49);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(37, \"div\", 43)(38, \"label\", 47);\n    i0.ɵɵtext(39, \"PDF\\u6BCF\\u9801\\u7C3D\\u540D\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(40, \"nb-select\", 50);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function ProjectManagementComponent_ng_template_31_Template_nb_select_ngModelChange_40_listener($event) {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.selectedShowAllSign, $event) || (ctx_r2.selectedShowAllSign = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtemplate(41, ProjectManagementComponent_ng_template_31_nb_option_41_Template, 2, 2, \"nb-option\", 49);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(42, \"div\", 43)(43, \"label\", 47);\n    i0.ɵɵtext(44, \"\\u986F\\u793A\\u50F9\\u683C\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(45, \"nb-select\", 51);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function ProjectManagementComponent_ng_template_31_Template_nb_select_ngModelChange_45_listener($event) {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.selectedShowPrice, $event) || (ctx_r2.selectedShowPrice = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtemplate(46, ProjectManagementComponent_ng_template_31_nb_option_46_Template, 2, 2, \"nb-option\", 49);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(47, \"nb-checkbox\", 52);\n    i0.ɵɵtwoWayListener(\"selectedChange\", function ProjectManagementComponent_ng_template_31_Template_nb_checkbox_selectedChange_47_listener($event) {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.selectedShowPrice, $event) || (ctx_r2.selectedShowPrice = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(48, \"div\", 43)(49, \"label\", 53);\n    i0.ɵɵtext(50, \"\\u7C3D\\u7F72\\u6587\\u4EF6\\u6CE8\\u610F\\u4E8B\\u9805\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(51, \"textarea\", 54);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function ProjectManagementComponent_ng_template_31_Template_textarea_ngModelChange_51_listener($event) {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.selectedBuildCase.CFinalFileNotice, $event) || (ctx_r2.selectedBuildCase.CFinalFileNotice = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(52, \"nb-card-footer\", 18)(53, \"button\", 55);\n    i0.ɵɵlistener(\"click\", function ProjectManagementComponent_ng_template_31_Template_button_click_53_listener() {\n      const ref_r15 = i0.ɵɵrestoreView(_r10).dialogRef;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onClose(ref_r15));\n    });\n    i0.ɵɵtext(54);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(55, \"button\", 56);\n    i0.ɵɵlistener(\"click\", function ProjectManagementComponent_ng_template_31_Template_button_click_55_listener() {\n      const ref_r15 = i0.ɵɵrestoreView(_r10).dialogRef;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onSubmit(ref_r15));\n    });\n    i0.ɵɵtext(56, \"\\u5132\\u5B58\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.isNew ? \"\\u65B0\\u589E\\u5EFA\\u6848\" : \"\\u7DE8\\u8F2F\\u5EFA\\u6848\", \" \");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.selectedBuildCase.CBuildCaseName);\n    i0.ɵɵadvance(18);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.fileName);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.imageUrl);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.selectedBuildCase.CFrontImage && !ctx_r2.imageUrl);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.selectedBuildCase.CSystemInstruction);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.selectedStatus);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.statusOptions);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.selectedShowAllSign);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.statusOptionsShow);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.selectedShowPrice);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.statusOptionsShow);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"selected\", ctx_r2.selectedShowPrice);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.selectedBuildCase.CFinalFileNotice);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r2.isNew ? \"\\u53D6\\u6D88\" : \"\\u95DC\\u9589\");\n  }\n}\nexport class ProjectManagementComponent extends BaseComponent {\n  constructor(_allow, dialogService, message, valid, _buildCaseService, router, _utilityService) {\n    super(_allow);\n    this._allow = _allow;\n    this.dialogService = dialogService;\n    this.message = message;\n    this.valid = valid;\n    this._buildCaseService = _buildCaseService;\n    this.router = router;\n    this._utilityService = _utilityService;\n    this.pageFirst = 1;\n    this.pageSize = 10;\n    this.pageIndex = 1;\n    this.totalRecords = 0;\n    this.statusOptions = [{\n      cID: 0,\n      CValue: 0,\n      CTitle: '停用'\n    }, {\n      cID: 1,\n      CValue: 1,\n      CTitle: '啟用'\n    }];\n    this.isConfirmOptions = [{\n      cID: 0,\n      CValue: 0,\n      CTitle: '未確認'\n    }, {\n      cID: 1,\n      CValue: 1,\n      CTitle: '已確認'\n    }];\n    this.statusOptionsShow = [{\n      cID: 0,\n      CValue: false,\n      CTitle: '停用'\n    }, {\n      cID: 1,\n      CValue: true,\n      CTitle: '啟用'\n    }];\n    // CFinalFileNotice: string = \"\"\n    this.listBuildCase = [];\n    this.search = \"\";\n    this.initBuildCase = {\n      CBuildCaseName: '',\n      CFrontImage: '',\n      CSystemInstruction: '',\n      ImageList: null,\n      cID: undefined,\n      CStatus: undefined,\n      CFinalFileNotice: ''\n    };\n    this.isNew = true;\n    this.imgSrc = null;\n    this.imageUrl = null;\n    this.fileName = null;\n  }\n  onRelatedDocument(id) {\n    this.router.navigateByUrl(`/pages/related-documents/${id}`);\n  }\n  ngOnInit() {\n    this.getListBuildCase().subscribe();\n    this.selectedStatus = this.statusOptions[0];\n    this.selectedShowPrice = this.statusOptionsShow[0];\n    this.selectedShowAllSign = this.statusOptionsShow[0];\n    this.selectedIsConfirm = this.isConfirmOptions[0];\n    this.selectedBuildCase = {\n      ...this.initBuildCase\n    };\n  }\n  getListBuildCase() {\n    return this._buildCaseService.apiBuildCaseGetAllBuildCasePost$Json({\n      body: {\n        PageIndex: this.pageIndex,\n        PageSize: this.pageSize,\n        CName: this.search,\n        CIsPagi: true\n      }\n    }).pipe(tap(res => {\n      if (res.Entries && res.StatusCode == 0) {\n        this.listBuildCase = res.Entries ?? [];\n        this.totalRecords = res.TotalItems;\n      }\n    }));\n  }\n  addNew(ref) {\n    this.isNew = true;\n    this.clearForm();\n    this.selectedStatus = this.statusOptions[0];\n    this.selectedShowPrice = this.statusOptionsShow[0];\n    this.selectedShowAllSign = this.statusOptionsShow[0];\n    this.selectedBuildCase.CStatus = this.statusOptions[0].CValue;\n    this.selectedIsConfirm = this.isConfirmOptions[0];\n    this.selectedBuildCase.CIsConfirm = this.isConfirmOptions[0].CValue;\n    this.dialogService.open(ref);\n  }\n  getBase64Image(file) {\n    return new Promise((resolve, reject) => {\n      const reader = new FileReader();\n      reader.readAsDataURL(file);\n      reader.onload = () => resolve(reader.result);\n      reader.onerror = error => reject(error);\n    });\n  }\n  onFileSelected(event) {\n    const file = event.target.files[0];\n    const fileRegex = /pdf|jpg|jpeg|png/i;\n    if (!fileRegex.test(file.type)) {\n      this.message.showErrorMSG('檔案格式錯誤，僅限pdf、圖片檔');\n      return;\n    }\n    if (file) {\n      const allowedTypes = ['image/jpeg', 'image/jpg', 'application/pdf'];\n      if (allowedTypes.includes(file.type)) {\n        this.fileName = file.name;\n        const reader = new FileReader();\n        reader.onload = e => {\n          this.imageUrl = e.target.result;\n          if (this.fileInput) {\n            this.fileInput.nativeElement.value = null;\n          }\n        };\n        reader.readAsDataURL(file);\n      }\n    }\n  }\n  clearImage() {\n    if (this.imageUrl) {\n      this.imageUrl = null;\n      this.fileName = null;\n      if (this.fileInput) {\n        this.fileInput.nativeElement.value = null; // Xóa giá trị input file\n      }\n    }\n  }\n  clearForm() {\n    this.selectedBuildCase = {\n      ...this.initBuildCase\n    };\n    this.clearImage();\n  }\n  onSelectedBuildCase(data, ref) {\n    console.log(data);\n    this.isNew = false;\n    this.dialogService.open(ref);\n    this.clearImage();\n    this.selectedBuildCase = _.cloneDeep(data);\n    this.selectedBuildCase.CSystemInstruction = this._utilityService.htmltoText(data.CSystemInstruction);\n    this.selectedStatus = this.statusOptions[this.selectedBuildCase.CStatus];\n    this.selectedShowPrice = this.statusOptionsShow[this.selectedBuildCase.CShowPrice == true ? 1 : 0];\n    this.selectedShowAllSign = this.statusOptionsShow[this.selectedBuildCase.CShowSignAll == true ? 1 : 0];\n  }\n  validation() {\n    this.valid.clear();\n    this.valid.required('[建案名稱]', this.selectedBuildCase.CBuildCaseName);\n    this.valid.isStringMaxLength('[建案名稱]', this.selectedBuildCase.CBuildCaseName, 50);\n    if (this.isNew) {\n      this.valid.required('[前台圖片]', this.selectedBuildCase.CFrontImage);\n    }\n    this.valid.required('[系統操作說明]', this.selectedBuildCase.CSystemInstruction);\n    this.valid.required('[狀態]', this.selectedBuildCase.CStatus?.toString());\n  }\n  removeBase64Prefix(base64String) {\n    const prefixIndex = base64String.indexOf(\",\");\n    if (prefixIndex !== -1) {\n      return base64String.substring(prefixIndex + 1);\n    }\n    return base64String;\n  }\n  nl2br(str) {\n    if (typeof str === 'undefined' || str === null) {\n      return '';\n    }\n    return str.replace(/\\n/g, '<br>');\n    ;\n  }\n  onSubmit(ref) {\n    this.selectedBuildCase.CStatus = this.selectedStatus.CValue;\n    this.selectedBuildCase.CShowPrice = this.selectedShowPrice.CValue;\n    this.selectedBuildCase.CShowSignAll = this.selectedShowAllSign.CValue;\n    const requestBody = {\n      CBuildCaseName: this.selectedBuildCase.CBuildCaseName,\n      CStatus: this.selectedStatus.CValue,\n      CSystemInstruction: this.nl2br(this.selectedBuildCase.CSystemInstruction),\n      CIsConfirm: this.selectedIsConfirm.CValue == 0 ? false : true,\n      CFinalFileNotice: this.selectedBuildCase.CFinalFileNotice,\n      CShowPrice: this.selectedShowPrice.CValue,\n      CShowSignAll: this.selectedShowAllSign.CValue\n    };\n    if (this.isNew && this.imageUrl) {\n      // NEW\n      this.selectedBuildCase.CFrontImage = this.removeBase64Prefix(this.imageUrl); // as string\n      requestBody.CFrontImage = this.removeBase64Prefix(this.imageUrl);\n    } else {\n      // EDIT\n      if (this.imageUrl) {\n        requestBody.CFrontImage = this.removeBase64Prefix(this.imageUrl);\n      }\n      requestBody.CBuildCaseID = this.selectedBuildCase.cID;\n    }\n    this.validation();\n    if (this.valid.errorMessages.length > 0) {\n      this.message.showErrorMSGs(this.valid.errorMessages);\n      return;\n    }\n    this._buildCaseService.apiBuildCaseSaveBuildCasePost$Json({\n      body: requestBody\n    }).pipe(tap(res => {\n      if (res.StatusCode === 0) {\n        this.message.showSucessMSG(\"執行成功\");\n      } else {\n        this.message.showSucessMSG(\"建案名稱不可重複\");\n      }\n    }), concatMap(() => this.getListBuildCase()), finalize(() => ref.close())).subscribe();\n  }\n  pageChanged(newPage) {\n    this.pageIndex = newPage;\n    this.getListBuildCase().subscribe();\n  }\n  onDelete(data, ref) {\n    if (window.confirm(`確定要刪除【項目${data.CBuildCaseName}】?`)) {\n      this._buildCaseService.apiBuildCaseDeleteBuildCasePost$Json({\n        body: {\n          \"CBuildCaseID\": data.cID\n        }\n      }).pipe(tap(res => {\n        if (res.StatusCode === 0) {\n          this.message.showSucessMSG(\"執行成功\");\n        } else {\n          this.message.showErrorMSG(res.Message);\n        }\n      }), concatMap(() => this.getListBuildCase()), finalize(() => ref.close())).subscribe();\n    }\n  }\n  onClose(ref) {\n    ref.close();\n  }\n  static {\n    this.ɵfac = function ProjectManagementComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || ProjectManagementComponent)(i0.ɵɵdirectiveInject(i1.AllowHelper), i0.ɵɵdirectiveInject(i2.NbDialogService), i0.ɵɵdirectiveInject(i3.MessageService), i0.ɵɵdirectiveInject(i4.ValidationHelper), i0.ɵɵdirectiveInject(i5.BuildCaseService), i0.ɵɵdirectiveInject(i6.Router), i0.ɵɵdirectiveInject(i7.UtilityService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ProjectManagementComponent,\n      selectors: [[\"ngx-project-management\"]],\n      viewQuery: function ProjectManagementComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.fileInput = _t.first);\n        }\n      },\n      standalone: true,\n      features: [i0.ɵɵInheritDefinitionFeature, i0.ɵɵStandaloneFeature],\n      decls: 33,\n      vars: 6,\n      consts: [[\"dialog\", \"\"], [\"accent\", \"success\"], [1, \"row\"], [1, \"col-1\"], [\"for\", \"project\", 1, \"text-nowrap\", \"mr-1\", \"h-full\", \"mt-2\"], [1, \"col-5\"], [\"type\", \"text\", \"nbInput\", \"\", 3, \"ngModel\", \"ngModelChange\", 4, \"ngIf\"], [1, \"col-6\"], [1, \"d-flex\", \"justify-content-end\"], [1, \"btn\", \"btn-info\", \"mr-2\", 3, \"click\"], [1, \"fas\", \"fa-search\", \"mr-1\"], [\"class\", \"btn btn-info\", 3, \"click\", 4, \"ngIf\"], [1, \"table-responsive\", \"mt-4\"], [1, \"table\", \"table-striped\", \"border\", 2, \"min-width\", \"1000px\", \"background-color\", \"#f3f3f3\"], [2, \"background-color\", \"#27ae60\", \"color\", \"white\"], [\"scope\", \"col\", 1, \"col-1\"], [\"scope\", \"col\", 1, \"col-2\", \"text-center\"], [4, \"ngFor\", \"ngForOf\"], [1, \"d-flex\", \"justify-content-center\"], [\"aria-label\", \"Pagination\", 3, \"pageChange\", \"page\", \"pageSize\", \"collectionSize\"], [\"type\", \"text\", \"nbInput\", \"\", 3, \"ngModelChange\", \"ngModel\"], [1, \"btn\", \"btn-info\", 3, \"click\"], [1, \"fas\", \"fa-plus\", \"mr-1\"], [1, \"text-center\", \"w-32\"], [\"class\", \"btn btn-outline-primary btn-sm m-1\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"btn btn-outline-primary btn-sm m-1 text-red-500 border-red-500\", 3, \"click\", 4, \"ngIf\"], [1, \"btn\", \"btn-outline-primary\", \"btn-sm\", \"m-1\", 3, \"click\"], [1, \"btn\", \"btn-outline-primary\", \"btn-sm\", \"m-1\", \"text-red-500\", \"border-red-500\", 3, \"click\"], [2, \"width\", \"550px\", \"max-height\", \"95vh\"], [1, \"px-4\"], [1, \"form-group\"], [\"for\", \"imageName\", \"baseLabel\", \"\", 1, \"required-field\", \"mr-4\", 2, \"min-width\", \"100px\"], [\"type\", \"text\", \"nbInput\", \"\", \"placeholder\", \"\\u5EFA\\u6848\\u540D\\u7A31\", 1, \"w-full\", 3, \"ngModelChange\", \"ngModel\"], [1, \"form-group\", \"d-flex\", \"align-items-baseline\"], [1, \"d-flex\", \"flex-col\", \"mr-3\"], [\"for\", \"file\", \"baseLabel\", \"\", 1, \"mb-0\", 2, \"min-width\", \"100px\"], [2, \"color\", \"red\"], [1, \"flex\", \"flex-col\", \"items-start\", \"space-y-4\"], [\"type\", \"file\", \"id\", \"fileInput\", \"accept\", \"image/jpeg, image/jpg, application/pdf\", 1, \"hidden\", 2, \"display\", \"none\", 3, \"change\"], [\"for\", \"fileInput\", 1, \"cursor-pointer\", \"bg-blue-500\", \"hover:bg-blue-700\", \"text-white\", \"font-bold\", \"py-2\", \"px-4\", \"rounded\"], [1, \"fa-solid\", \"fa-cloud-arrow-up\", \"mr-2\"], [\"class\", \"flex items-center space-x-2\", 4, \"ngIf\"], [\"alt\", \"Uploaded Image\", \"class\", \"max-w-xs h-auto rounded-md\", 3, \"src\", 4, \"ngIf\"], [1, \"form-group\", \"d-flex\", \"mt-2\"], [\"for\", \"remark\", \"baseLabel\", \"\", 1, \"required-field\", \"mr-4\", 2, \"min-width\", \"100px\"], [\"contenteditable\", \"\", \"name\", \"remark\", \"id\", \"remark\", \"rows\", \"5\", \"nbInput\", \"\", 1, \"w-full\", 2, \"resize\", \"none\", 3, \"ngModelChange\", \"ngModel\"], [1, \"form-group\", \"d-flex\"], [\"for\", \"remark\", \"baseLabel\", \"\", 1, \"mr-4\", 2, \"min-width\", \"100px\"], [\"placeholder\", \"Select Status\", 1, \"w-full\", 3, \"ngModelChange\", \"ngModel\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"placeholder\", \"PDF\\u6BCF\\u9801\\u7C3D\\u540D\", 1, \"w-full\", 3, \"ngModelChange\", \"ngModel\"], [\"placeholder\", \"\\u986F\\u793A\\u50F9\\u683C\", 1, \"w-full\", 3, \"ngModelChange\", \"ngModel\"], [3, \"selectedChange\", \"selected\"], [\"for\", \"finalfilenotice\", \"baseLabel\", \"\", 1, \"mr-4\", 2, \"min-width\", \"100px\"], [\"name\", \"finalfilenotice\", \"id\", \"finalfilenotice\", \"rows\", \"5\", \"nbInput\", \"\", 1, \"w-full\", 2, \"resize\", \"none\", 3, \"ngModelChange\", \"ngModel\"], [1, \"btn\", \"btn-danger\", \"btn-sm\", \"mr-4\", 3, \"click\"], [1, \"btn\", \"btn-success\", \"btn-sm\", 3, \"click\"], [1, \"flex\", \"items-center\", \"space-x-2\"], [1, \"text-gray-600\"], [\"type\", \"button\", 1, \"text-red-500\", \"hover:text-red-700\", 3, \"click\"], [1, \"fa-solid\", \"fa-trash\"], [\"alt\", \"Uploaded Image\", 1, \"max-w-xs\", \"h-auto\", \"rounded-md\", 3, \"src\"], [3, \"value\"]],\n      template: function ProjectManagementComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"nb-card\", 1)(1, \"nb-card-header\");\n          i0.ɵɵelement(2, \"ngx-breadcrumb\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(3, \"nb-card-body\")(4, \"div\", 2)(5, \"div\", 3)(6, \"label\", 4);\n          i0.ɵɵtext(7, \"\\u5EFA\\u6848\\u540D\\u7A31 \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(8, \"div\", 5)(9, \"nb-form-field\");\n          i0.ɵɵtemplate(10, ProjectManagementComponent_input_10_Template, 1, 1, \"input\", 6);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(11, \"div\", 7)(12, \"div\", 8)(13, \"button\", 9);\n          i0.ɵɵlistener(\"click\", function ProjectManagementComponent_Template_button_click_13_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.getListBuildCase().subscribe());\n          });\n          i0.ɵɵelement(14, \"i\", 10);\n          i0.ɵɵtext(15, \" \\u67E5\\u8A62 \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(16, ProjectManagementComponent_button_16_Template, 3, 0, \"button\", 11);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(17, \"div\", 12)(18, \"table\", 13)(19, \"thead\")(20, \"tr\", 14)(21, \"th\", 15);\n          i0.ɵɵtext(22, \"ID\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(23, \"th\", 15);\n          i0.ɵɵtext(24, \"\\u5EFA\\u6848\\u540D\\u7A31\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(25, \"th\", 16);\n          i0.ɵɵtext(26, \"\\u52D5\\u4F5C\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(27, \"tbody\");\n          i0.ɵɵtemplate(28, ProjectManagementComponent_tr_28_Template, 9, 5, \"tr\", 17);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(29, \"nb-card-footer\", 18)(30, \"ngb-pagination\", 19);\n          i0.ɵɵtwoWayListener(\"pageChange\", function ProjectManagementComponent_Template_ngb_pagination_pageChange_30_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.pageIndex, $event) || (ctx.pageIndex = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵlistener(\"pageChange\", function ProjectManagementComponent_Template_ngb_pagination_pageChange_30_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.pageChanged($event));\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(31, ProjectManagementComponent_ng_template_31_Template, 57, 15, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor);\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(10);\n          i0.ɵɵproperty(\"ngIf\", ctx.isRead);\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"ngIf\", ctx.isCreate);\n          i0.ɵɵadvance(12);\n          i0.ɵɵproperty(\"ngForOf\", ctx.listBuildCase);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtwoWayProperty(\"page\", ctx.pageIndex);\n          i0.ɵɵproperty(\"pageSize\", ctx.pageSize)(\"collectionSize\", ctx.totalRecords);\n        }\n      },\n      dependencies: [CommonModule, i8.NgForOf, i8.NgIf, SharedModule, i9.DefaultValueAccessor, i9.NgControlStatus, i9.NgModel, i2.NbCardComponent, i2.NbCardBodyComponent, i2.NbCardFooterComponent, i2.NbCardHeaderComponent, i2.NbCheckboxComponent, i2.NbInputDirective, i2.NbSelectComponent, i2.NbOptionComponent, i2.NbFormFieldComponent, i10.NgbPagination, i11.BreadcrumbComponent, i12.BaseLabelDirective],\n      styles: [\"/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsImZpbGUiOiJwcm9qZWN0LW1hbmFnZW1lbnQuY29tcG9uZW50LnNjc3MifQ== */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvcGFnZXMvY29uc3RydWN0aW9uLXByb2plY3QtbWFuYWdlbWVudC9wcm9qZWN0LW1hbmFnZW1lbnQvcHJvamVjdC1tYW5hZ2VtZW50LmNvbXBvbmVudC5zY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiI7QUFDQSxvTEFBb0wiLCJzb3VyY2VSb290IjoiIn0= */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "concatMap", "finalize", "tap", "BaseComponent", "SharedModule", "_", "i0", "ɵɵelementStart", "ɵɵtwoWayListener", "ProjectManagementComponent_input_10_Template_input_ngModelChange_0_listener", "$event", "ɵɵrestoreView", "_r2", "ctx_r2", "ɵɵnextContext", "ɵɵtwoWayBindingSet", "search", "ɵɵresetView", "ɵɵelementEnd", "ɵɵtwoWayProperty", "ɵɵlistener", "ProjectManagementComponent_button_16_Template_button_click_0_listener", "_r4", "dialog_r5", "ɵɵreference", "addNew", "ɵɵelement", "ɵɵtext", "ProjectManagementComponent_tr_28_button_6_Template_button_click_0_listener", "_r6", "item_r7", "$implicit", "onSelectedBuildCase", "ProjectManagementComponent_tr_28_button_7_Template_button_click_0_listener", "_r8", "onRelatedDocument", "cID", "ProjectManagementComponent_tr_28_button_8_Template_button_click_0_listener", "_r9", "onDelete", "ɵɵtemplate", "ProjectManagementComponent_tr_28_button_6_Template", "ProjectManagementComponent_tr_28_button_7_Template", "ProjectManagementComponent_tr_28_button_8_Template", "ɵɵadvance", "ɵɵtextInterpolate", "CBuildCaseName", "ɵɵproperty", "isRead", "isDelete", "ProjectManagementComponent_ng_template_31_div_25_Template_button_click_3_listener", "_r11", "clearImage", "fileName", "imageUrl", "ɵɵsanitizeUrl", "selectedBuildCase", "CFrontImage", "status_r12", "ɵɵtextInterpolate1", "<PERSON><PERSON><PERSON>", "status_r13", "status_r14", "ProjectManagementComponent_ng_template_31_Template_input_ngModelChange_7_listener", "_r10", "ProjectManagementComponent_ng_template_31_Template_input_change_21_listener", "onFileSelected", "ProjectManagementComponent_ng_template_31_div_25_Template", "ProjectManagementComponent_ng_template_31_img_26_Template", "ProjectManagementComponent_ng_template_31_img_27_Template", "ProjectManagementComponent_ng_template_31_Template_textarea_ngModelChange_31_listener", "CSystemInstruction", "ProjectManagementComponent_ng_template_31_Template_nb_select_ngModelChange_35_listener", "selectedStatus", "ProjectManagementComponent_ng_template_31_nb_option_36_Template", "ProjectManagementComponent_ng_template_31_Template_nb_select_ngModelChange_40_listener", "selectedShowAllSign", "ProjectManagementComponent_ng_template_31_nb_option_41_Template", "ProjectManagementComponent_ng_template_31_Template_nb_select_ngModelChange_45_listener", "selectedShowPrice", "ProjectManagementComponent_ng_template_31_nb_option_46_Template", "ProjectManagementComponent_ng_template_31_Template_nb_checkbox_selectedChange_47_listener", "ProjectManagementComponent_ng_template_31_Template_textarea_ngModelChange_51_listener", "CFinalFileNotice", "ProjectManagementComponent_ng_template_31_Template_button_click_53_listener", "ref_r15", "dialogRef", "onClose", "ProjectManagementComponent_ng_template_31_Template_button_click_55_listener", "onSubmit", "isNew", "statusOptions", "statusOptionsShow", "ProjectManagementComponent", "constructor", "_allow", "dialogService", "message", "valid", "_buildCaseService", "router", "_utilityService", "pageFirst", "pageSize", "pageIndex", "totalRecords", "CValue", "isConfirmOptions", "listBuildCase", "initBuildCase", "ImageList", "undefined", "CStatus", "imgSrc", "id", "navigateByUrl", "ngOnInit", "getListBuildCase", "subscribe", "selectedIsConfirm", "apiBuildCaseGetAllBuildCasePost$Json", "body", "PageIndex", "PageSize", "CName", "CIsPagi", "pipe", "res", "Entries", "StatusCode", "TotalItems", "ref", "clearForm", "CIsConfirm", "open", "getBase64Image", "file", "Promise", "resolve", "reject", "reader", "FileReader", "readAsDataURL", "onload", "result", "onerror", "error", "event", "target", "files", "fileRegex", "test", "type", "showErrorMSG", "allowedTypes", "includes", "name", "e", "fileInput", "nativeElement", "value", "data", "console", "log", "cloneDeep", "htmltoText", "CShowPrice", "CShowSignAll", "validation", "clear", "required", "isStringMaxLength", "toString", "removeBase64Prefix", "base64String", "prefixIndex", "indexOf", "substring", "nl2br", "str", "replace", "requestBody", "CBuildCaseID", "errorMessages", "length", "showErrorMSGs", "apiBuildCaseSaveBuildCasePost$Json", "showSucessMSG", "close", "pageChanged", "newPage", "window", "confirm", "apiBuildCaseDeleteBuildCasePost$Json", "Message", "ɵɵdirectiveInject", "i1", "AllowHelper", "i2", "NbDialogService", "i3", "MessageService", "i4", "ValidationHelper", "i5", "BuildCaseService", "i6", "Router", "i7", "UtilityService", "selectors", "viewQuery", "ProjectManagementComponent_Query", "rf", "ctx", "ProjectManagementComponent_input_10_Template", "ProjectManagementComponent_Template_button_click_13_listener", "_r1", "ProjectManagementComponent_button_16_Template", "ProjectManagementComponent_tr_28_Template", "ProjectManagementComponent_Template_ngb_pagination_pageChange_30_listener", "ProjectManagementComponent_ng_template_31_Template", "ɵɵtemplateRefExtractor", "isCreate", "i8", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "i9", "DefaultValueAccessor", "NgControlStatus", "NgModel", "NbCardComponent", "NbCardBodyComponent", "NbCardFooterComponent", "NbCardHeaderComponent", "NbCheckboxComponent", "NbInputDirective", "NbSelectComponent", "NbOptionComponent", "NbFormFieldComponent", "i10", "NgbPagination", "i11", "BreadcrumbComponent", "i12", "BaseLabelDirective", "styles"], "sources": ["C:\\Users\\<USER>\\Documents\\jeansalechange\\adminFront\\src\\app\\pages\\construction-project-management\\project-management\\project-management.component.ts", "C:\\Users\\<USER>\\Documents\\jeansalechange\\adminFront\\src\\app\\pages\\construction-project-management\\project-management\\project-management.component.html"], "sourcesContent": ["import { Component, ElementRef, OnInit, ViewChild } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { NbDialogService } from '@nebular/theme';\r\nimport { MessageService } from 'src/app/shared/services/message.service';\r\nimport { ValidationHelper } from 'src/app/shared/helper/validationHelper';\r\nimport { BuildCaseService } from 'src/services/api/services';\r\nimport { BuildCaseGetListReponse, SaveBuildCaseArgs } from 'src/services/api/models';\r\nimport { Router } from '@angular/router';\r\nimport { AllowHelper } from 'src/app/shared/helper/allowHelper';\r\nimport { FilterListItemsPipe } from 'src/app/@theme/pipes/searchText.pipe';\r\nimport { concatMap, finalize, tap } from 'rxjs';\r\nimport { BaseComponent } from '../../components/base/baseComponent';\r\nimport { SharedModule } from '../../components/shared.module';\r\nimport { UtilityService } from 'src/app/shared/services/utility.service';\r\nimport { FilterByKeyPipe } from 'src/app/@theme/pipes/filterByKey.pipe';\r\nimport * as _ from 'lodash';\r\n@Component({\r\n  selector: 'ngx-project-management',\r\n  templateUrl: './project-management.component.html',\r\n  styleUrls: ['./project-management.component.scss'],\r\n  standalone: true,\r\n  imports: [\r\n    CommonModule,\r\n    SharedModule,\r\n    FilterListItemsPipe,\r\n    FilterByKeyPipe,\r\n  ],\r\n})\r\n\r\nexport class ProjectManagementComponent extends BaseComponent implements OnInit {\r\n  constructor(\r\n    private _allow: AllowHelper,\r\n    private dialogService: NbDialogService,\r\n    private message: MessageService,\r\n    private valid: ValidationHelper,\r\n    private _buildCaseService: BuildCaseService,\r\n    private router: Router,\r\n    private _utilityService: UtilityService\r\n  ) {\r\n    super(_allow)\r\n  }\r\n\r\n  override pageFirst = 1;\r\n  override pageSize = 10;\r\n  override pageIndex = 1;\r\n  override totalRecords = 0;\r\n  statusOptions: any[] = [\r\n    {\r\n      cID: 0,\r\n      CValue: 0,\r\n      CTitle: '停用',\r\n    },\r\n    {\r\n      cID: 1,\r\n      CValue: 1,\r\n      CTitle: '啟用',\r\n    }\r\n  ]\r\n\r\n  isConfirmOptions: any[] = [\r\n    {\r\n      cID: 0,\r\n      CValue: 0,\r\n      CTitle: '未確認',\r\n    },\r\n    {\r\n      cID: 1,\r\n      CValue: 1,\r\n      CTitle: '已確認',\r\n    }\r\n  ]\r\n\r\n  statusOptionsShow: any[] = [\r\n    {\r\n      cID: 0,\r\n      CValue: false,\r\n      CTitle: '停用',\r\n    },\r\n    {\r\n      cID: 1,\r\n      CValue: true,\r\n      CTitle: '啟用',\r\n    }\r\n  ]\r\n\r\n  selectedStatus: {\r\n    cID: number, CValue: number, CTitle: string\r\n  }\r\n  selectedIsConfirm: {\r\n    cID: number, CValue: number, CTitle: string\r\n  }\r\n  selectedShowPrice: {\r\n    cID: number, CValue: boolean, CTitle: string\r\n  }\r\n  selectedShowAllSign: {\r\n    cID: number, CValue: boolean, CTitle: string\r\n  }\r\n  // CFinalFileNotice: string = \"\"\r\n\r\n  listBuildCase: BuildCaseGetListReponse[] = []\r\n  selectedBuildCase: BuildCaseGetListReponse & {\r\n    CIsConfirm?: number\r\n  }\r\n  search: string = \"\"\r\n\r\n  initBuildCase: BuildCaseGetListReponse = {\r\n    CBuildCaseName: '',\r\n    CFrontImage: '',\r\n    CSystemInstruction: '',\r\n    ImageList: null,\r\n    cID: undefined,\r\n    CStatus: undefined,\r\n    CFinalFileNotice: ''\r\n  }\r\n\r\n  isNew = true\r\n\r\n  imgSrc: string | ArrayBuffer | null = null;\r\n  @ViewChild('fileInput') fileInput!: ElementRef;\r\n\r\n  imageUrl: string | ArrayBuffer | null = null;\r\n  fileName: string | null = null;\r\n\r\n  onRelatedDocument(id: any) {\r\n    this.router.navigateByUrl(`/pages/related-documents/${id}`)\r\n  }\r\n\r\n  override ngOnInit(): void {\r\n    this.getListBuildCase().subscribe();\r\n    this.selectedStatus = this.statusOptions[0]\r\n    this.selectedShowPrice = this.statusOptionsShow[0]\r\n    this.selectedShowAllSign = this.statusOptionsShow[0]\r\n    this.selectedIsConfirm = this.isConfirmOptions[0]\r\n    this.selectedBuildCase = { ...this.initBuildCase }\r\n  }\r\n\r\n  getListBuildCase() {\r\n    return this._buildCaseService.apiBuildCaseGetAllBuildCasePost$Json({ body: {\r\n      PageIndex: this.pageIndex,\r\n      PageSize: this.pageSize,\r\n      CName: this.search,\r\n      CIsPagi: true\r\n    } }).pipe(\r\n      tap(res => {\r\n        if (res.Entries && res.StatusCode == 0) {\r\n          this.listBuildCase = res.Entries! ?? []\r\n          this.totalRecords = res.TotalItems!\r\n        }\r\n      })\r\n    )\r\n  }\r\n\r\n  addNew(ref: any) {\r\n    this.isNew = true;\r\n    this.clearForm()\r\n    this.selectedStatus = this.statusOptions[0]\r\n    this.selectedShowPrice = this.statusOptionsShow[0]\r\n    this.selectedShowAllSign = this.statusOptionsShow[0]\r\n    this.selectedBuildCase.CStatus = this.statusOptions[0].CValue\r\n    this.selectedIsConfirm = this.isConfirmOptions[0]\r\n    this.selectedBuildCase.CIsConfirm = this.isConfirmOptions[0].CValue\r\n    this.dialogService.open(ref)\r\n  }\r\n\r\n\r\n  getBase64Image(file: File): Promise<string | ArrayBuffer | null> {\r\n    return new Promise((resolve, reject) => {\r\n      const reader = new FileReader();\r\n      reader.readAsDataURL(file);\r\n      reader.onload = () => resolve(reader.result);\r\n      reader.onerror = error => reject(error);\r\n    });\r\n  }\r\n\r\n\r\n  onFileSelected(event: any) {\r\n    const file: File = event.target.files[0];\r\n    const fileRegex = /pdf|jpg|jpeg|png/i;\r\n    if (!fileRegex.test(file.type)) {\r\n      this.message.showErrorMSG('檔案格式錯誤，僅限pdf、圖片檔');\r\n      return;\r\n    }\r\n    if (file) {\r\n      const allowedTypes = ['image/jpeg', 'image/jpg', 'application/pdf'];\r\n      if (allowedTypes.includes(file.type)) {\r\n        this.fileName = file.name;\r\n        const reader = new FileReader();\r\n        reader.onload = (e: any) => {\r\n          this.imageUrl = e.target.result;\r\n          if (this.fileInput) {\r\n            this.fileInput.nativeElement.value = null;\r\n          }\r\n        };\r\n        reader.readAsDataURL(file);\r\n      }\r\n    }\r\n  }\r\n\r\n  clearImage() {\r\n    if (this.imageUrl) {\r\n      this.imageUrl = null;\r\n      this.fileName = null;\r\n      if (this.fileInput) {\r\n        this.fileInput.nativeElement.value = null; // Xóa giá trị input file\r\n      }\r\n    }\r\n  }\r\n\r\n  clearForm() {\r\n    this.selectedBuildCase = { ...this.initBuildCase }\r\n    this.clearImage()\r\n  }\r\n\r\n  onSelectedBuildCase(data: any, ref: any) {\r\n    console.log(data);\r\n    this.isNew = false;\r\n    this.dialogService.open(ref);\r\n    this.clearImage()\r\n    this.selectedBuildCase = _.cloneDeep(data);\r\n    this.selectedBuildCase.CSystemInstruction = this._utilityService.htmltoText(data.CSystemInstruction)\r\n    this.selectedStatus = this.statusOptions[this.selectedBuildCase.CStatus!]\r\n    this.selectedShowPrice = this.statusOptionsShow[this.selectedBuildCase.CShowPrice! == true ? 1 : 0]\r\n    this.selectedShowAllSign = this.statusOptionsShow[this.selectedBuildCase.CShowSignAll! == true ? 1 : 0]\r\n  }\r\n\r\n  validation() {\r\n    this.valid.clear();\r\n    this.valid.required('[建案名稱]', this.selectedBuildCase.CBuildCaseName)\r\n    this.valid.isStringMaxLength('[建案名稱]', this.selectedBuildCase.CBuildCaseName, 50)\r\n    if (this.isNew) {\r\n      this.valid.required('[前台圖片]', this.selectedBuildCase.CFrontImage)\r\n    }\r\n    this.valid.required('[系統操作說明]', this.selectedBuildCase.CSystemInstruction)\r\n    this.valid.required('[狀態]', this.selectedBuildCase.CStatus?.toString())\r\n  }\r\n\r\n  removeBase64Prefix(base64String: any) {\r\n    const prefixIndex = base64String.indexOf(\",\");\r\n    if (prefixIndex !== -1) {\r\n      return base64String.substring(prefixIndex + 1);\r\n    }\r\n    return base64String;\r\n  }\r\n\r\n  nl2br(str: string) {\r\n    if (typeof str === 'undefined' || str === null) {\r\n      return '';\r\n    }\r\n    return str.replace(/\\n/g, '<br>');;\r\n  }\r\n\r\n  onSubmit(ref: any) {\r\n    this.selectedBuildCase.CStatus = this.selectedStatus.CValue;\r\n    this.selectedBuildCase.CShowPrice = this.selectedShowPrice.CValue;\r\n    this.selectedBuildCase.CShowSignAll = this.selectedShowAllSign.CValue;\r\n    const requestBody: SaveBuildCaseArgs = {\r\n      CBuildCaseName: this.selectedBuildCase.CBuildCaseName,\r\n      CStatus: this.selectedStatus.CValue,\r\n      CSystemInstruction: this.nl2br(this.selectedBuildCase.CSystemInstruction!),\r\n      CIsConfirm: this.selectedIsConfirm.CValue == 0 ? false : true,\r\n      CFinalFileNotice: this.selectedBuildCase.CFinalFileNotice,\r\n      CShowPrice: this.selectedShowPrice.CValue,\r\n      CShowSignAll: this.selectedShowAllSign.CValue\r\n    }\r\n\r\n    if (this.isNew && this.imageUrl) { // NEW\r\n      this.selectedBuildCase.CFrontImage = this.removeBase64Prefix(this.imageUrl)// as string\r\n      requestBody.CFrontImage = this.removeBase64Prefix(this.imageUrl)\r\n    } else { // EDIT\r\n      if (this.imageUrl) {\r\n        requestBody.CFrontImage = this.removeBase64Prefix(this.imageUrl)\r\n      }\r\n      requestBody.CBuildCaseID = this.selectedBuildCase.cID\r\n    }\r\n    this.validation()\r\n    if (this.valid.errorMessages.length > 0) {\r\n      this.message.showErrorMSGs(this.valid.errorMessages);\r\n      return\r\n    }\r\n\r\n    this._buildCaseService.apiBuildCaseSaveBuildCasePost$Json({ body: requestBody }).pipe(\r\n      tap(res => {\r\n        if (res.StatusCode === 0) {\r\n          this.message.showSucessMSG(\"執行成功\");\r\n        } else {\r\n          this.message.showSucessMSG(\"建案名稱不可重複\")\r\n        }\r\n      }),\r\n      concatMap(() => this.getListBuildCase()),\r\n      finalize(() => ref.close())\r\n    ).subscribe();\r\n  }\r\n\r\n  pageChanged(newPage: number) {\r\n    this.pageIndex = newPage;\r\n    this.getListBuildCase().subscribe()\r\n  }\r\n\r\n  onDelete(data: any, ref: any) {\r\n    if (window.confirm(`確定要刪除【項目${data.CBuildCaseName}】?`)) {\r\n      this._buildCaseService.apiBuildCaseDeleteBuildCasePost$Json({\r\n        body: {\r\n          \"CBuildCaseID\": data.cID\r\n        }\r\n      }).pipe(\r\n        tap(res => {\r\n          if (res.StatusCode === 0) {\r\n            this.message.showSucessMSG(\"執行成功\");\r\n          } else {\r\n            this.message.showErrorMSG(res.Message!)\r\n          }\r\n        }),\r\n        concatMap(() => this.getListBuildCase()),\r\n        finalize(() => ref.close())\r\n      ).subscribe()\r\n    }\r\n  }\r\n\r\n  onClose(ref: any) {\r\n    ref.close();\r\n  }\r\n}\r\n", "<nb-card accent=\"success\">\r\n  <nb-card-header>\r\n    <ngx-breadcrumb></ngx-breadcrumb>\r\n  </nb-card-header>\r\n  <nb-card-body>\r\n    <div class=\"row\">\r\n      <div class=\"col-1\">\r\n        <label for=\"project\" class=\"text-nowrap mr-1 h-full mt-2\">建案名稱\r\n        </label>\r\n      </div>\r\n      <div class=\"col-5\">\r\n        <nb-form-field>\r\n          <input type=\"text\" nbInput [(ngModel)]=\"search\" *ngIf=\"isRead\">\r\n        </nb-form-field>\r\n      </div>\r\n      <div class=\"col-6\">\r\n        <div class=\"d-flex justify-content-end\">\r\n          <button class=\"btn btn-info  mr-2\" (click)=\"getListBuildCase().subscribe()\">\r\n            <i class=\"fas fa-search mr-1\"></i>\r\n            查詢\r\n          </button>\r\n          <button class=\"btn btn-info\" (click)=\"addNew(dialog)\" *ngIf=\"isCreate\">\r\n            <i class=\"fas fa-plus mr-1\"></i> 新增</button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <div class=\"table-responsive mt-4\">\r\n      <table class=\"table table-striped border \" style=\"min-width: 1000px; background-color:#f3f3f3;\">\r\n        <thead>\r\n          <tr style=\"background-color: #27ae60; color: white;\">\r\n            <th scope=\"col\" class=\"col-1 \">ID</th>\r\n            <th scope=\"col\" class=\"col-1\">建案名稱</th>\r\n            <th scope=\"col\" class=\"col-2 text-center\">動作</th>\r\n          </tr>\r\n        </thead>\r\n        <tbody>\r\n          <tr *ngFor=\"let item of listBuildCase; let i = index\">\r\n            <td>{{ item.cID}}</td>\r\n            <td>{{item.CBuildCaseName}}</td>\r\n            <td class=\"text-center w-32\">\r\n              <button class=\"btn btn-outline-primary btn-sm m-1\" (click)=\"onSelectedBuildCase(item, dialog)\"\r\n                *ngIf=\"isRead\">編輯</button>\r\n              <button class=\"btn btn-outline-primary btn-sm m-1\" (click)=\"onRelatedDocument(item.cID)\"\r\n                *ngIf=\"isRead\">建案公佈欄文件</button>\r\n              <button class=\"btn btn-outline-primary btn-sm m-1 text-red-500 border-red-500\"\r\n                (click)=\"onDelete(item, dialog)\" *ngIf=\"isDelete\">刪除</button>\r\n            </td>\r\n          </tr>\r\n        </tbody>\r\n      </table>\r\n    </div>\r\n  </nb-card-body>\r\n  <nb-card-footer class=\"d-flex justify-content-center\">\r\n    <ngb-pagination [(page)]=\"pageIndex\" [pageSize]=\"pageSize\" [collectionSize]=\"totalRecords\"\r\n      (pageChange)=\"pageChanged($event)\" aria-label=\"Pagination\">\r\n    </ngb-pagination>\r\n  </nb-card-footer>\r\n</nb-card>\r\n\r\n<ng-template #dialog let-dialog let-ref=\"dialogRef\">\r\n  <nb-card style=\"width:550px; max-height: 95vh\">\r\n    <nb-card-header>\r\n      {{isNew ? \"新增建案\": \"編輯建案\"}}\r\n    </nb-card-header>\r\n    <nb-card-body class=\"px-4\">\r\n      <div class=\"form-group\">\r\n        <label for=\"imageName\" class=\"required-field mr-4\" style=\"min-width:100px\" baseLabel>建案名稱\r\n        </label>\r\n        <input type=\"text\" class=\"w-full\" nbInput placeholder=\"建案名稱\" [(ngModel)]=\"selectedBuildCase.CBuildCaseName\" />\r\n      </div>\r\n      <div class=\"form-group d-flex align-items-baseline\">\r\n        <div class=\"d-flex flex-col mr-3\">\r\n          <label for=\"file\" class=\"mb-0\" style=\"min-width:100px\" baseLabel>前台圖片\r\n          </label>\r\n          <h3 style=\"color:red;\">只接受pdf, 圖片檔</h3>\r\n          <h3 style=\"color:red;\">建議尺寸:</h3>\r\n          <h3 style=\"color:red;\">直式 1080 x 1440 px</h3>\r\n          <h3 style=\"color:red;\">橫式 1440 x 1080 px</h3>\r\n        </div>\r\n        <div class=\"flex flex-col items-start space-y-4\">\r\n          <input type=\"file\" id=\"fileInput\" accept=\"image/jpeg, image/jpg, application/pdf\" class=\"hidden\"\r\n            style=\"display: none\" (change)=\"onFileSelected($event)\">\r\n          <label for=\"fileInput\"\r\n            class=\"cursor-pointer bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded\">\r\n            <i class=\"fa-solid fa-cloud-arrow-up mr-2\"></i> 上傳\r\n          </label>\r\n          <div class=\"flex items-center space-x-2\" *ngIf=\"fileName\">\r\n            <span class=\"text-gray-600\">{{ fileName }}</span>\r\n            <button type=\"button\" (click)=\"clearImage()\" class=\"text-red-500 hover:text-red-700\">\r\n              <i class=\"fa-solid fa-trash\"></i>\r\n            </button>\r\n          </div>\r\n          <img [src]=\"imageUrl\" *ngIf=\"imageUrl\" alt=\"Uploaded Image\" class=\"max-w-xs h-auto rounded-md\">\r\n          <img [src]=\"selectedBuildCase.CFrontImage\" *ngIf=\"selectedBuildCase.CFrontImage && !imageUrl\"\r\n            alt=\"Uploaded Image\" class=\"max-w-xs h-auto rounded-md\">\r\n        </div>\r\n      </div>\r\n      <div class=\"form-group d-flex mt-2\">\r\n        <label for=\"remark\" style=\"min-width:100px\" class=\"required-field mr-4\" baseLabel>系統操作說明\r\n        </label>\r\n        <textarea contenteditable name=\"remark\" id=\"remark\" rows=\"5\" class=\"w-full\" style=\"resize: none;\" nbInput\r\n          [(ngModel)]=\"selectedBuildCase.CSystemInstruction!\"></textarea>\r\n      </div>\r\n      <div class=\"form-group d-flex\">\r\n        <label for=\"remark\" style=\"min-width:100px\" class=\"mr-4\" baseLabel>狀態</label>\r\n        <nb-select placeholder=\"Select Status\" [(ngModel)]=\"selectedStatus\" class=\"w-full\">\r\n          <nb-option *ngFor=\"let status of statusOptions\" [value]=\"status\">\r\n            {{ status.CTitle }}\r\n          </nb-option>\r\n        </nb-select>\r\n      </div>\r\n      <!-- <div class=\"form-group d-flex mt-2\">\r\n        <label for=\"remark\" style=\"min-width:100px\" class=\"mr-4\" baseLabel>資料確認</label>\r\n        <nb-select placeholder=\"Select Status\" [(ngModel)]=\"selectedIsConfirm\" class=\"w-full\">\r\n          <nb-option *ngFor=\"let status of isConfirmOptions\" [value]=\"status\">\r\n            {{ status.CTitle }}\r\n          </nb-option>\r\n        </nb-select>\r\n      </div> -->\r\n      <div class=\"form-group d-flex mt-2\">\r\n        <label for=\"remark\" style=\"min-width:100px\" class=\"mr-4\" baseLabel>PDF每頁簽名</label>\r\n        <nb-select placeholder=\"PDF每頁簽名\" [(ngModel)]=\"selectedShowAllSign\" class=\"w-full\">\r\n          <nb-option *ngFor=\"let status of statusOptionsShow\" [value]=\"status\">\r\n            {{ status.CTitle }}\r\n          </nb-option>\r\n        </nb-select>\r\n      </div>\r\n      <div class=\"form-group d-flex mt-2\">\r\n        <label for=\"remark\" style=\"min-width:100px\" class=\"mr-4\" baseLabel>顯示價格</label>\r\n        <nb-select placeholder=\"顯示價格\" [(ngModel)]=\"selectedShowPrice\" class=\"w-full\">\r\n          <nb-option *ngFor=\"let status of statusOptionsShow\" [value]=\"status\">\r\n            {{ status.CTitle }}\r\n          </nb-option>\r\n        </nb-select>\r\n        <nb-checkbox [(selected)]=\"this.selectedShowPrice\">\r\n        </nb-checkbox>\r\n      </div>\r\n      <div class=\"form-group d-flex mt-2\">\r\n        <label for=\"finalfilenotice\" style=\"min-width:100px\" class=\"mr-4\" baseLabel>簽署文件注意事項</label>\r\n        <textarea name=\"finalfilenotice\" id=\"finalfilenotice\" rows=\"5\" class=\"w-full\" style=\"resize: none;\" nbInput\r\n          [(ngModel)]=\"selectedBuildCase.CFinalFileNotice\"></textarea>\r\n      </div>\r\n    </nb-card-body>\r\n    <nb-card-footer class=\"d-flex justify-content-center\">\r\n      <button class=\"btn btn-danger btn-sm mr-4\" (click)=\"onClose(ref)\">{{ isNew ? '取消' : '關閉'}}</button>\r\n      <button class=\"btn btn-success btn-sm\" (click)=\"onSubmit(ref)\">儲存</button>\r\n    </nb-card-footer>\r\n  </nb-card>\r\n</ng-template>"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAS9C,SAASC,SAAS,EAAEC,QAAQ,EAAEC,GAAG,QAAQ,MAAM;AAC/C,SAASC,aAAa,QAAQ,qCAAqC;AACnE,SAASC,YAAY,QAAQ,gCAAgC;AAG7D,OAAO,KAAKC,CAAC,MAAM,QAAQ;;;;;;;;;;;;;;;;;;ICHjBC,EAAA,CAAAC,cAAA,gBAA+D;IAApCD,EAAA,CAAAE,gBAAA,2BAAAC,4EAAAC,MAAA;MAAAJ,EAAA,CAAAK,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAP,EAAA,CAAAQ,aAAA;MAAAR,EAAA,CAAAS,kBAAA,CAAAF,MAAA,CAAAG,MAAA,EAAAN,MAAA,MAAAG,MAAA,CAAAG,MAAA,GAAAN,MAAA;MAAA,OAAAJ,EAAA,CAAAW,WAAA,CAAAP,MAAA;IAAA,EAAoB;IAA/CJ,EAAA,CAAAY,YAAA,EAA+D;;;;IAApCZ,EAAA,CAAAa,gBAAA,YAAAN,MAAA,CAAAG,MAAA,CAAoB;;;;;;IAS/CV,EAAA,CAAAC,cAAA,iBAAuE;IAA1CD,EAAA,CAAAc,UAAA,mBAAAC,sEAAA;MAAAf,EAAA,CAAAK,aAAA,CAAAW,GAAA;MAAA,MAAAT,MAAA,GAAAP,EAAA,CAAAQ,aAAA;MAAA,MAAAS,SAAA,GAAAjB,EAAA,CAAAkB,WAAA;MAAA,OAAAlB,EAAA,CAAAW,WAAA,CAASJ,MAAA,CAAAY,MAAA,CAAAF,SAAA,CAAc;IAAA,EAAC;IACnDjB,EAAA,CAAAoB,SAAA,YAAgC;IAACpB,EAAA,CAAAqB,MAAA,oBAAE;IAAArB,EAAA,CAAAY,YAAA,EAAS;;;;;;IAmB1CZ,EAAA,CAAAC,cAAA,iBACiB;IADkCD,EAAA,CAAAc,UAAA,mBAAAQ,2EAAA;MAAAtB,EAAA,CAAAK,aAAA,CAAAkB,GAAA;MAAA,MAAAC,OAAA,GAAAxB,EAAA,CAAAQ,aAAA,GAAAiB,SAAA;MAAA,MAAAlB,MAAA,GAAAP,EAAA,CAAAQ,aAAA;MAAA,MAAAS,SAAA,GAAAjB,EAAA,CAAAkB,WAAA;MAAA,OAAAlB,EAAA,CAAAW,WAAA,CAASJ,MAAA,CAAAmB,mBAAA,CAAAF,OAAA,EAAAP,SAAA,CAAiC;IAAA,EAAC;IAC7EjB,EAAA,CAAAqB,MAAA,mBAAE;IAAArB,EAAA,CAAAY,YAAA,EAAS;;;;;;IAC5BZ,EAAA,CAAAC,cAAA,iBACiB;IADkCD,EAAA,CAAAc,UAAA,mBAAAa,2EAAA;MAAA3B,EAAA,CAAAK,aAAA,CAAAuB,GAAA;MAAA,MAAAJ,OAAA,GAAAxB,EAAA,CAAAQ,aAAA,GAAAiB,SAAA;MAAA,MAAAlB,MAAA,GAAAP,EAAA,CAAAQ,aAAA;MAAA,OAAAR,EAAA,CAAAW,WAAA,CAASJ,MAAA,CAAAsB,iBAAA,CAAAL,OAAA,CAAAM,GAAA,CAA2B;IAAA,EAAC;IACvE9B,EAAA,CAAAqB,MAAA,iDAAO;IAAArB,EAAA,CAAAY,YAAA,EAAS;;;;;;IACjCZ,EAAA,CAAAC,cAAA,iBACoD;IAAlDD,EAAA,CAAAc,UAAA,mBAAAiB,2EAAA;MAAA/B,EAAA,CAAAK,aAAA,CAAA2B,GAAA;MAAA,MAAAR,OAAA,GAAAxB,EAAA,CAAAQ,aAAA,GAAAiB,SAAA;MAAA,MAAAlB,MAAA,GAAAP,EAAA,CAAAQ,aAAA;MAAA,MAAAS,SAAA,GAAAjB,EAAA,CAAAkB,WAAA;MAAA,OAAAlB,EAAA,CAAAW,WAAA,CAASJ,MAAA,CAAA0B,QAAA,CAAAT,OAAA,EAAAP,SAAA,CAAsB;IAAA,EAAC;IAAkBjB,EAAA,CAAAqB,MAAA,mBAAE;IAAArB,EAAA,CAAAY,YAAA,EAAS;;;;;IARjEZ,EADF,CAAAC,cAAA,SAAsD,SAChD;IAAAD,EAAA,CAAAqB,MAAA,GAAa;IAAArB,EAAA,CAAAY,YAAA,EAAK;IACtBZ,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAqB,MAAA,GAAuB;IAAArB,EAAA,CAAAY,YAAA,EAAK;IAChCZ,EAAA,CAAAC,cAAA,aAA6B;IAK3BD,EAJA,CAAAkC,UAAA,IAAAC,kDAAA,qBACiB,IAAAC,kDAAA,qBAEA,IAAAC,kDAAA,qBAEmC;IAExDrC,EADE,CAAAY,YAAA,EAAK,EACF;;;;;IAVCZ,EAAA,CAAAsC,SAAA,GAAa;IAAbtC,EAAA,CAAAuC,iBAAA,CAAAf,OAAA,CAAAM,GAAA,CAAa;IACb9B,EAAA,CAAAsC,SAAA,GAAuB;IAAvBtC,EAAA,CAAAuC,iBAAA,CAAAf,OAAA,CAAAgB,cAAA,CAAuB;IAGtBxC,EAAA,CAAAsC,SAAA,GAAY;IAAZtC,EAAA,CAAAyC,UAAA,SAAAlC,MAAA,CAAAmC,MAAA,CAAY;IAEZ1C,EAAA,CAAAsC,SAAA,EAAY;IAAZtC,EAAA,CAAAyC,UAAA,SAAAlC,MAAA,CAAAmC,MAAA,CAAY;IAEqB1C,EAAA,CAAAsC,SAAA,EAAc;IAAdtC,EAAA,CAAAyC,UAAA,SAAAlC,MAAA,CAAAoC,QAAA,CAAc;;;;;;IA0CpD3C,EADF,CAAAC,cAAA,cAA0D,eAC5B;IAAAD,EAAA,CAAAqB,MAAA,GAAc;IAAArB,EAAA,CAAAY,YAAA,EAAO;IACjDZ,EAAA,CAAAC,cAAA,iBAAqF;IAA/DD,EAAA,CAAAc,UAAA,mBAAA8B,kFAAA;MAAA5C,EAAA,CAAAK,aAAA,CAAAwC,IAAA;MAAA,MAAAtC,MAAA,GAAAP,EAAA,CAAAQ,aAAA;MAAA,OAAAR,EAAA,CAAAW,WAAA,CAASJ,MAAA,CAAAuC,UAAA,EAAY;IAAA,EAAC;IAC1C9C,EAAA,CAAAoB,SAAA,YAAiC;IAErCpB,EADE,CAAAY,YAAA,EAAS,EACL;;;;IAJwBZ,EAAA,CAAAsC,SAAA,GAAc;IAAdtC,EAAA,CAAAuC,iBAAA,CAAAhC,MAAA,CAAAwC,QAAA,CAAc;;;;;IAK5C/C,EAAA,CAAAoB,SAAA,cAA+F;;;;IAA1FpB,EAAA,CAAAyC,UAAA,QAAAlC,MAAA,CAAAyC,QAAA,EAAAhD,EAAA,CAAAiD,aAAA,CAAgB;;;;;IACrBjD,EAAA,CAAAoB,SAAA,cAC0D;;;;IADrDpB,EAAA,CAAAyC,UAAA,QAAAlC,MAAA,CAAA2C,iBAAA,CAAAC,WAAA,EAAAnD,EAAA,CAAAiD,aAAA,CAAqC;;;;;IAa1CjD,EAAA,CAAAC,cAAA,oBAAiE;IAC/DD,EAAA,CAAAqB,MAAA,GACF;IAAArB,EAAA,CAAAY,YAAA,EAAY;;;;IAFoCZ,EAAA,CAAAyC,UAAA,UAAAW,UAAA,CAAgB;IAC9DpD,EAAA,CAAAsC,SAAA,EACF;IADEtC,EAAA,CAAAqD,kBAAA,MAAAD,UAAA,CAAAE,MAAA,MACF;;;;;IAcAtD,EAAA,CAAAC,cAAA,oBAAqE;IACnED,EAAA,CAAAqB,MAAA,GACF;IAAArB,EAAA,CAAAY,YAAA,EAAY;;;;IAFwCZ,EAAA,CAAAyC,UAAA,UAAAc,UAAA,CAAgB;IAClEvD,EAAA,CAAAsC,SAAA,EACF;IADEtC,EAAA,CAAAqD,kBAAA,MAAAE,UAAA,CAAAD,MAAA,MACF;;;;;IAMAtD,EAAA,CAAAC,cAAA,oBAAqE;IACnED,EAAA,CAAAqB,MAAA,GACF;IAAArB,EAAA,CAAAY,YAAA,EAAY;;;;IAFwCZ,EAAA,CAAAyC,UAAA,UAAAe,UAAA,CAAgB;IAClExD,EAAA,CAAAsC,SAAA,EACF;IADEtC,EAAA,CAAAqD,kBAAA,MAAAG,UAAA,CAAAF,MAAA,MACF;;;;;;IAvENtD,EADF,CAAAC,cAAA,kBAA+C,qBAC7B;IACdD,EAAA,CAAAqB,MAAA,GACF;IAAArB,EAAA,CAAAY,YAAA,EAAiB;IAGbZ,EAFJ,CAAAC,cAAA,uBAA2B,cACD,gBAC+D;IAAAD,EAAA,CAAAqB,MAAA,gCACrF;IAAArB,EAAA,CAAAY,YAAA,EAAQ;IACRZ,EAAA,CAAAC,cAAA,gBAA8G;IAAjDD,EAAA,CAAAE,gBAAA,2BAAAuD,kFAAArD,MAAA;MAAAJ,EAAA,CAAAK,aAAA,CAAAqD,IAAA;MAAA,MAAAnD,MAAA,GAAAP,EAAA,CAAAQ,aAAA;MAAAR,EAAA,CAAAS,kBAAA,CAAAF,MAAA,CAAA2C,iBAAA,CAAAV,cAAA,EAAApC,MAAA,MAAAG,MAAA,CAAA2C,iBAAA,CAAAV,cAAA,GAAApC,MAAA;MAAA,OAAAJ,EAAA,CAAAW,WAAA,CAAAP,MAAA;IAAA,EAA8C;IAC7GJ,EADE,CAAAY,YAAA,EAA8G,EAC1G;IAGFZ,EAFJ,CAAAC,cAAA,cAAoD,cAChB,iBACiC;IAAAD,EAAA,CAAAqB,MAAA,iCACjE;IAAArB,EAAA,CAAAY,YAAA,EAAQ;IACRZ,EAAA,CAAAC,cAAA,cAAuB;IAAAD,EAAA,CAAAqB,MAAA,iDAAW;IAAArB,EAAA,CAAAY,YAAA,EAAK;IACvCZ,EAAA,CAAAC,cAAA,cAAuB;IAAAD,EAAA,CAAAqB,MAAA,iCAAK;IAAArB,EAAA,CAAAY,YAAA,EAAK;IACjCZ,EAAA,CAAAC,cAAA,cAAuB;IAAAD,EAAA,CAAAqB,MAAA,mCAAiB;IAAArB,EAAA,CAAAY,YAAA,EAAK;IAC7CZ,EAAA,CAAAC,cAAA,cAAuB;IAAAD,EAAA,CAAAqB,MAAA,mCAAiB;IAC1CrB,EAD0C,CAAAY,YAAA,EAAK,EACzC;IAEJZ,EADF,CAAAC,cAAA,eAAiD,iBAEW;IAAlCD,EAAA,CAAAc,UAAA,oBAAA6C,4EAAAvD,MAAA;MAAAJ,EAAA,CAAAK,aAAA,CAAAqD,IAAA;MAAA,MAAAnD,MAAA,GAAAP,EAAA,CAAAQ,aAAA;MAAA,OAAAR,EAAA,CAAAW,WAAA,CAAUJ,MAAA,CAAAqD,cAAA,CAAAxD,MAAA,CAAsB;IAAA,EAAC;IADzDJ,EAAA,CAAAY,YAAA,EAC0D;IAC1DZ,EAAA,CAAAC,cAAA,iBAC8F;IAC5FD,EAAA,CAAAoB,SAAA,aAA+C;IAACpB,EAAA,CAAAqB,MAAA,sBAClD;IAAArB,EAAA,CAAAY,YAAA,EAAQ;IAQRZ,EAPA,CAAAkC,UAAA,KAAA2B,yDAAA,kBAA0D,KAAAC,yDAAA,kBAMqC,KAAAC,yDAAA,kBAErC;IAE9D/D,EADE,CAAAY,YAAA,EAAM,EACF;IAEJZ,EADF,CAAAC,cAAA,eAAoC,iBACgD;IAAAD,EAAA,CAAAqB,MAAA,6CAClF;IAAArB,EAAA,CAAAY,YAAA,EAAQ;IACRZ,EAAA,CAAAC,cAAA,oBACsD;IAApDD,EAAA,CAAAE,gBAAA,2BAAA8D,sFAAA5D,MAAA;MAAAJ,EAAA,CAAAK,aAAA,CAAAqD,IAAA;MAAA,MAAAnD,MAAA,GAAAP,EAAA,CAAAQ,aAAA;MAAAR,EAAA,CAAAS,kBAAA,CAAAF,MAAA,CAAA2C,iBAAA,CAAAe,kBAAA,EAAA7D,MAAA,MAAAG,MAAA,CAAA2C,iBAAA,CAAAe,kBAAA,GAAA7D,MAAA;MAAA,OAAAJ,EAAA,CAAAW,WAAA,CAAAP,MAAA;IAAA,EAAmD;IACvDJ,EADwD,CAAAY,YAAA,EAAW,EAC7D;IAEJZ,EADF,CAAAC,cAAA,eAA+B,iBACsC;IAAAD,EAAA,CAAAqB,MAAA,oBAAE;IAAArB,EAAA,CAAAY,YAAA,EAAQ;IAC7EZ,EAAA,CAAAC,cAAA,qBAAmF;IAA5CD,EAAA,CAAAE,gBAAA,2BAAAgE,uFAAA9D,MAAA;MAAAJ,EAAA,CAAAK,aAAA,CAAAqD,IAAA;MAAA,MAAAnD,MAAA,GAAAP,EAAA,CAAAQ,aAAA;MAAAR,EAAA,CAAAS,kBAAA,CAAAF,MAAA,CAAA4D,cAAA,EAAA/D,MAAA,MAAAG,MAAA,CAAA4D,cAAA,GAAA/D,MAAA;MAAA,OAAAJ,EAAA,CAAAW,WAAA,CAAAP,MAAA;IAAA,EAA4B;IACjEJ,EAAA,CAAAkC,UAAA,KAAAkC,+DAAA,wBAAiE;IAIrEpE,EADE,CAAAY,YAAA,EAAY,EACR;IAUJZ,EADF,CAAAC,cAAA,eAAoC,iBACiC;IAAAD,EAAA,CAAAqB,MAAA,mCAAO;IAAArB,EAAA,CAAAY,YAAA,EAAQ;IAClFZ,EAAA,CAAAC,cAAA,qBAAkF;IAAjDD,EAAA,CAAAE,gBAAA,2BAAAmE,uFAAAjE,MAAA;MAAAJ,EAAA,CAAAK,aAAA,CAAAqD,IAAA;MAAA,MAAAnD,MAAA,GAAAP,EAAA,CAAAQ,aAAA;MAAAR,EAAA,CAAAS,kBAAA,CAAAF,MAAA,CAAA+D,mBAAA,EAAAlE,MAAA,MAAAG,MAAA,CAAA+D,mBAAA,GAAAlE,MAAA;MAAA,OAAAJ,EAAA,CAAAW,WAAA,CAAAP,MAAA;IAAA,EAAiC;IAChEJ,EAAA,CAAAkC,UAAA,KAAAqC,+DAAA,wBAAqE;IAIzEvE,EADE,CAAAY,YAAA,EAAY,EACR;IAEJZ,EADF,CAAAC,cAAA,eAAoC,iBACiC;IAAAD,EAAA,CAAAqB,MAAA,gCAAI;IAAArB,EAAA,CAAAY,YAAA,EAAQ;IAC/EZ,EAAA,CAAAC,cAAA,qBAA6E;IAA/CD,EAAA,CAAAE,gBAAA,2BAAAsE,uFAAApE,MAAA;MAAAJ,EAAA,CAAAK,aAAA,CAAAqD,IAAA;MAAA,MAAAnD,MAAA,GAAAP,EAAA,CAAAQ,aAAA;MAAAR,EAAA,CAAAS,kBAAA,CAAAF,MAAA,CAAAkE,iBAAA,EAAArE,MAAA,MAAAG,MAAA,CAAAkE,iBAAA,GAAArE,MAAA;MAAA,OAAAJ,EAAA,CAAAW,WAAA,CAAAP,MAAA;IAAA,EAA+B;IAC3DJ,EAAA,CAAAkC,UAAA,KAAAwC,+DAAA,wBAAqE;IAGvE1E,EAAA,CAAAY,YAAA,EAAY;IACZZ,EAAA,CAAAC,cAAA,uBAAmD;IAAtCD,EAAA,CAAAE,gBAAA,4BAAAyE,0FAAAvE,MAAA;MAAAJ,EAAA,CAAAK,aAAA,CAAAqD,IAAA;MAAA,MAAAnD,MAAA,GAAAP,EAAA,CAAAQ,aAAA;MAAAR,EAAA,CAAAS,kBAAA,CAAAF,MAAA,CAAAkE,iBAAA,EAAArE,MAAA,MAAAG,MAAA,CAAAkE,iBAAA,GAAArE,MAAA;MAAA,OAAAJ,EAAA,CAAAW,WAAA,CAAAP,MAAA;IAAA,EAAqC;IAEpDJ,EADE,CAAAY,YAAA,EAAc,EACV;IAEJZ,EADF,CAAAC,cAAA,eAAoC,iBAC0C;IAAAD,EAAA,CAAAqB,MAAA,wDAAQ;IAAArB,EAAA,CAAAY,YAAA,EAAQ;IAC5FZ,EAAA,CAAAC,cAAA,oBACmD;IAAjDD,EAAA,CAAAE,gBAAA,2BAAA0E,sFAAAxE,MAAA;MAAAJ,EAAA,CAAAK,aAAA,CAAAqD,IAAA;MAAA,MAAAnD,MAAA,GAAAP,EAAA,CAAAQ,aAAA;MAAAR,EAAA,CAAAS,kBAAA,CAAAF,MAAA,CAAA2C,iBAAA,CAAA2B,gBAAA,EAAAzE,MAAA,MAAAG,MAAA,CAAA2C,iBAAA,CAAA2B,gBAAA,GAAAzE,MAAA;MAAA,OAAAJ,EAAA,CAAAW,WAAA,CAAAP,MAAA;IAAA,EAAgD;IAEtDJ,EAFuD,CAAAY,YAAA,EAAW,EAC1D,EACO;IAEbZ,EADF,CAAAC,cAAA,0BAAsD,kBACc;IAAvBD,EAAA,CAAAc,UAAA,mBAAAgE,4EAAA;MAAA,MAAAC,OAAA,GAAA/E,EAAA,CAAAK,aAAA,CAAAqD,IAAA,EAAAsB,SAAA;MAAA,MAAAzE,MAAA,GAAAP,EAAA,CAAAQ,aAAA;MAAA,OAAAR,EAAA,CAAAW,WAAA,CAASJ,MAAA,CAAA0E,OAAA,CAAAF,OAAA,CAAY;IAAA,EAAC;IAAC/E,EAAA,CAAAqB,MAAA,IAAwB;IAAArB,EAAA,CAAAY,YAAA,EAAS;IACnGZ,EAAA,CAAAC,cAAA,kBAA+D;IAAxBD,EAAA,CAAAc,UAAA,mBAAAoE,4EAAA;MAAA,MAAAH,OAAA,GAAA/E,EAAA,CAAAK,aAAA,CAAAqD,IAAA,EAAAsB,SAAA;MAAA,MAAAzE,MAAA,GAAAP,EAAA,CAAAQ,aAAA;MAAA,OAAAR,EAAA,CAAAW,WAAA,CAASJ,MAAA,CAAA4E,QAAA,CAAAJ,OAAA,CAAa;IAAA,EAAC;IAAC/E,EAAA,CAAAqB,MAAA,oBAAE;IAErErB,EAFqE,CAAAY,YAAA,EAAS,EAC3D,EACT;;;;IArFNZ,EAAA,CAAAsC,SAAA,GACF;IADEtC,EAAA,CAAAqD,kBAAA,MAAA9C,MAAA,CAAA6E,KAAA,gEACF;IAKiEpF,EAAA,CAAAsC,SAAA,GAA8C;IAA9CtC,EAAA,CAAAa,gBAAA,YAAAN,MAAA,CAAA2C,iBAAA,CAAAV,cAAA,CAA8C;IAkB/DxC,EAAA,CAAAsC,SAAA,IAAc;IAAdtC,EAAA,CAAAyC,UAAA,SAAAlC,MAAA,CAAAwC,QAAA,CAAc;IAMjC/C,EAAA,CAAAsC,SAAA,EAAc;IAAdtC,EAAA,CAAAyC,UAAA,SAAAlC,MAAA,CAAAyC,QAAA,CAAc;IACOhD,EAAA,CAAAsC,SAAA,EAAgD;IAAhDtC,EAAA,CAAAyC,UAAA,SAAAlC,MAAA,CAAA2C,iBAAA,CAAAC,WAAA,KAAA5C,MAAA,CAAAyC,QAAA,CAAgD;IAQ5FhD,EAAA,CAAAsC,SAAA,GAAmD;IAAnDtC,EAAA,CAAAa,gBAAA,YAAAN,MAAA,CAAA2C,iBAAA,CAAAe,kBAAA,CAAmD;IAIdjE,EAAA,CAAAsC,SAAA,GAA4B;IAA5BtC,EAAA,CAAAa,gBAAA,YAAAN,MAAA,CAAA4D,cAAA,CAA4B;IACnCnE,EAAA,CAAAsC,SAAA,EAAgB;IAAhBtC,EAAA,CAAAyC,UAAA,YAAAlC,MAAA,CAAA8E,aAAA,CAAgB;IAefrF,EAAA,CAAAsC,SAAA,GAAiC;IAAjCtC,EAAA,CAAAa,gBAAA,YAAAN,MAAA,CAAA+D,mBAAA,CAAiC;IAClCtE,EAAA,CAAAsC,SAAA,EAAoB;IAApBtC,EAAA,CAAAyC,UAAA,YAAAlC,MAAA,CAAA+E,iBAAA,CAAoB;IAOtBtF,EAAA,CAAAsC,SAAA,GAA+B;IAA/BtC,EAAA,CAAAa,gBAAA,YAAAN,MAAA,CAAAkE,iBAAA,CAA+B;IAC7BzE,EAAA,CAAAsC,SAAA,EAAoB;IAApBtC,EAAA,CAAAyC,UAAA,YAAAlC,MAAA,CAAA+E,iBAAA,CAAoB;IAIvCtF,EAAA,CAAAsC,SAAA,EAAqC;IAArCtC,EAAA,CAAAa,gBAAA,aAAAN,MAAA,CAAAkE,iBAAA,CAAqC;IAMhDzE,EAAA,CAAAsC,SAAA,GAAgD;IAAhDtC,EAAA,CAAAa,gBAAA,YAAAN,MAAA,CAAA2C,iBAAA,CAAA2B,gBAAA,CAAgD;IAIc7E,EAAA,CAAAsC,SAAA,GAAwB;IAAxBtC,EAAA,CAAAuC,iBAAA,CAAAhC,MAAA,CAAA6E,KAAA,mCAAwB;;;ADpHhG,OAAM,MAAOG,0BAA2B,SAAQ1F,aAAa;EAC3D2F,YACUC,MAAmB,EACnBC,aAA8B,EAC9BC,OAAuB,EACvBC,KAAuB,EACvBC,iBAAmC,EACnCC,MAAc,EACdC,eAA+B;IAEvC,KAAK,CAACN,MAAM,CAAC;IARL,KAAAA,MAAM,GAANA,MAAM;IACN,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,eAAe,GAAfA,eAAe;IAKhB,KAAAC,SAAS,GAAG,CAAC;IACb,KAAAC,QAAQ,GAAG,EAAE;IACb,KAAAC,SAAS,GAAG,CAAC;IACb,KAAAC,YAAY,GAAG,CAAC;IACzB,KAAAd,aAAa,GAAU,CACrB;MACEvD,GAAG,EAAE,CAAC;MACNsE,MAAM,EAAE,CAAC;MACT9C,MAAM,EAAE;KACT,EACD;MACExB,GAAG,EAAE,CAAC;MACNsE,MAAM,EAAE,CAAC;MACT9C,MAAM,EAAE;KACT,CACF;IAED,KAAA+C,gBAAgB,GAAU,CACxB;MACEvE,GAAG,EAAE,CAAC;MACNsE,MAAM,EAAE,CAAC;MACT9C,MAAM,EAAE;KACT,EACD;MACExB,GAAG,EAAE,CAAC;MACNsE,MAAM,EAAE,CAAC;MACT9C,MAAM,EAAE;KACT,CACF;IAED,KAAAgC,iBAAiB,GAAU,CACzB;MACExD,GAAG,EAAE,CAAC;MACNsE,MAAM,EAAE,KAAK;MACb9C,MAAM,EAAE;KACT,EACD;MACExB,GAAG,EAAE,CAAC;MACNsE,MAAM,EAAE,IAAI;MACZ9C,MAAM,EAAE;KACT,CACF;IAcD;IAEA,KAAAgD,aAAa,GAA8B,EAAE;IAI7C,KAAA5F,MAAM,GAAW,EAAE;IAEnB,KAAA6F,aAAa,GAA4B;MACvC/D,cAAc,EAAE,EAAE;MAClBW,WAAW,EAAE,EAAE;MACfc,kBAAkB,EAAE,EAAE;MACtBuC,SAAS,EAAE,IAAI;MACf1E,GAAG,EAAE2E,SAAS;MACdC,OAAO,EAAED,SAAS;MAClB5B,gBAAgB,EAAE;KACnB;IAED,KAAAO,KAAK,GAAG,IAAI;IAEZ,KAAAuB,MAAM,GAAgC,IAAI;IAG1C,KAAA3D,QAAQ,GAAgC,IAAI;IAC5C,KAAAD,QAAQ,GAAkB,IAAI;EAjF9B;EAmFAlB,iBAAiBA,CAAC+E,EAAO;IACvB,IAAI,CAACd,MAAM,CAACe,aAAa,CAAC,4BAA4BD,EAAE,EAAE,CAAC;EAC7D;EAESE,QAAQA,CAAA;IACf,IAAI,CAACC,gBAAgB,EAAE,CAACC,SAAS,EAAE;IACnC,IAAI,CAAC7C,cAAc,GAAG,IAAI,CAACkB,aAAa,CAAC,CAAC,CAAC;IAC3C,IAAI,CAACZ,iBAAiB,GAAG,IAAI,CAACa,iBAAiB,CAAC,CAAC,CAAC;IAClD,IAAI,CAAChB,mBAAmB,GAAG,IAAI,CAACgB,iBAAiB,CAAC,CAAC,CAAC;IACpD,IAAI,CAAC2B,iBAAiB,GAAG,IAAI,CAACZ,gBAAgB,CAAC,CAAC,CAAC;IACjD,IAAI,CAACnD,iBAAiB,GAAG;MAAE,GAAG,IAAI,CAACqD;IAAa,CAAE;EACpD;EAEAQ,gBAAgBA,CAAA;IACd,OAAO,IAAI,CAAClB,iBAAiB,CAACqB,oCAAoC,CAAC;MAAEC,IAAI,EAAE;QACzEC,SAAS,EAAE,IAAI,CAAClB,SAAS;QACzBmB,QAAQ,EAAE,IAAI,CAACpB,QAAQ;QACvBqB,KAAK,EAAE,IAAI,CAAC5G,MAAM;QAClB6G,OAAO,EAAE;;IACV,CAAE,CAAC,CAACC,IAAI,CACP5H,GAAG,CAAC6H,GAAG,IAAG;MACR,IAAIA,GAAG,CAACC,OAAO,IAAID,GAAG,CAACE,UAAU,IAAI,CAAC,EAAE;QACtC,IAAI,CAACrB,aAAa,GAAGmB,GAAG,CAACC,OAAQ,IAAI,EAAE;QACvC,IAAI,CAACvB,YAAY,GAAGsB,GAAG,CAACG,UAAW;MACrC;IACF,CAAC,CAAC,CACH;EACH;EAEAzG,MAAMA,CAAC0G,GAAQ;IACb,IAAI,CAACzC,KAAK,GAAG,IAAI;IACjB,IAAI,CAAC0C,SAAS,EAAE;IAChB,IAAI,CAAC3D,cAAc,GAAG,IAAI,CAACkB,aAAa,CAAC,CAAC,CAAC;IAC3C,IAAI,CAACZ,iBAAiB,GAAG,IAAI,CAACa,iBAAiB,CAAC,CAAC,CAAC;IAClD,IAAI,CAAChB,mBAAmB,GAAG,IAAI,CAACgB,iBAAiB,CAAC,CAAC,CAAC;IACpD,IAAI,CAACpC,iBAAiB,CAACwD,OAAO,GAAG,IAAI,CAACrB,aAAa,CAAC,CAAC,CAAC,CAACe,MAAM;IAC7D,IAAI,CAACa,iBAAiB,GAAG,IAAI,CAACZ,gBAAgB,CAAC,CAAC,CAAC;IACjD,IAAI,CAACnD,iBAAiB,CAAC6E,UAAU,GAAG,IAAI,CAAC1B,gBAAgB,CAAC,CAAC,CAAC,CAACD,MAAM;IACnE,IAAI,CAACV,aAAa,CAACsC,IAAI,CAACH,GAAG,CAAC;EAC9B;EAGAI,cAAcA,CAACC,IAAU;IACvB,OAAO,IAAIC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAI;MACrC,MAAMC,MAAM,GAAG,IAAIC,UAAU,EAAE;MAC/BD,MAAM,CAACE,aAAa,CAACN,IAAI,CAAC;MAC1BI,MAAM,CAACG,MAAM,GAAG,MAAML,OAAO,CAACE,MAAM,CAACI,MAAM,CAAC;MAC5CJ,MAAM,CAACK,OAAO,GAAGC,KAAK,IAAIP,MAAM,CAACO,KAAK,CAAC;IACzC,CAAC,CAAC;EACJ;EAGAhF,cAAcA,CAACiF,KAAU;IACvB,MAAMX,IAAI,GAASW,KAAK,CAACC,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC;IACxC,MAAMC,SAAS,GAAG,mBAAmB;IACrC,IAAI,CAACA,SAAS,CAACC,IAAI,CAACf,IAAI,CAACgB,IAAI,CAAC,EAAE;MAC9B,IAAI,CAACvD,OAAO,CAACwD,YAAY,CAAC,kBAAkB,CAAC;MAC7C;IACF;IACA,IAAIjB,IAAI,EAAE;MACR,MAAMkB,YAAY,GAAG,CAAC,YAAY,EAAE,WAAW,EAAE,iBAAiB,CAAC;MACnE,IAAIA,YAAY,CAACC,QAAQ,CAACnB,IAAI,CAACgB,IAAI,CAAC,EAAE;QACpC,IAAI,CAACnG,QAAQ,GAAGmF,IAAI,CAACoB,IAAI;QACzB,MAAMhB,MAAM,GAAG,IAAIC,UAAU,EAAE;QAC/BD,MAAM,CAACG,MAAM,GAAIc,CAAM,IAAI;UACzB,IAAI,CAACvG,QAAQ,GAAGuG,CAAC,CAACT,MAAM,CAACJ,MAAM;UAC/B,IAAI,IAAI,CAACc,SAAS,EAAE;YAClB,IAAI,CAACA,SAAS,CAACC,aAAa,CAACC,KAAK,GAAG,IAAI;UAC3C;QACF,CAAC;QACDpB,MAAM,CAACE,aAAa,CAACN,IAAI,CAAC;MAC5B;IACF;EACF;EAEApF,UAAUA,CAAA;IACR,IAAI,IAAI,CAACE,QAAQ,EAAE;MACjB,IAAI,CAACA,QAAQ,GAAG,IAAI;MACpB,IAAI,CAACD,QAAQ,GAAG,IAAI;MACpB,IAAI,IAAI,CAACyG,SAAS,EAAE;QAClB,IAAI,CAACA,SAAS,CAACC,aAAa,CAACC,KAAK,GAAG,IAAI,CAAC,CAAC;MAC7C;IACF;EACF;EAEA5B,SAASA,CAAA;IACP,IAAI,CAAC5E,iBAAiB,GAAG;MAAE,GAAG,IAAI,CAACqD;IAAa,CAAE;IAClD,IAAI,CAACzD,UAAU,EAAE;EACnB;EAEApB,mBAAmBA,CAACiI,IAAS,EAAE9B,GAAQ;IACrC+B,OAAO,CAACC,GAAG,CAACF,IAAI,CAAC;IACjB,IAAI,CAACvE,KAAK,GAAG,KAAK;IAClB,IAAI,CAACM,aAAa,CAACsC,IAAI,CAACH,GAAG,CAAC;IAC5B,IAAI,CAAC/E,UAAU,EAAE;IACjB,IAAI,CAACI,iBAAiB,GAAGnD,CAAC,CAAC+J,SAAS,CAACH,IAAI,CAAC;IAC1C,IAAI,CAACzG,iBAAiB,CAACe,kBAAkB,GAAG,IAAI,CAAC8B,eAAe,CAACgE,UAAU,CAACJ,IAAI,CAAC1F,kBAAkB,CAAC;IACpG,IAAI,CAACE,cAAc,GAAG,IAAI,CAACkB,aAAa,CAAC,IAAI,CAACnC,iBAAiB,CAACwD,OAAQ,CAAC;IACzE,IAAI,CAACjC,iBAAiB,GAAG,IAAI,CAACa,iBAAiB,CAAC,IAAI,CAACpC,iBAAiB,CAAC8G,UAAW,IAAI,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC;IACnG,IAAI,CAAC1F,mBAAmB,GAAG,IAAI,CAACgB,iBAAiB,CAAC,IAAI,CAACpC,iBAAiB,CAAC+G,YAAa,IAAI,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC;EACzG;EAEAC,UAAUA,CAAA;IACR,IAAI,CAACtE,KAAK,CAACuE,KAAK,EAAE;IAClB,IAAI,CAACvE,KAAK,CAACwE,QAAQ,CAAC,QAAQ,EAAE,IAAI,CAAClH,iBAAiB,CAACV,cAAc,CAAC;IACpE,IAAI,CAACoD,KAAK,CAACyE,iBAAiB,CAAC,QAAQ,EAAE,IAAI,CAACnH,iBAAiB,CAACV,cAAc,EAAE,EAAE,CAAC;IACjF,IAAI,IAAI,CAAC4C,KAAK,EAAE;MACd,IAAI,CAACQ,KAAK,CAACwE,QAAQ,CAAC,QAAQ,EAAE,IAAI,CAAClH,iBAAiB,CAACC,WAAW,CAAC;IACnE;IACA,IAAI,CAACyC,KAAK,CAACwE,QAAQ,CAAC,UAAU,EAAE,IAAI,CAAClH,iBAAiB,CAACe,kBAAkB,CAAC;IAC1E,IAAI,CAAC2B,KAAK,CAACwE,QAAQ,CAAC,MAAM,EAAE,IAAI,CAAClH,iBAAiB,CAACwD,OAAO,EAAE4D,QAAQ,EAAE,CAAC;EACzE;EAEAC,kBAAkBA,CAACC,YAAiB;IAClC,MAAMC,WAAW,GAAGD,YAAY,CAACE,OAAO,CAAC,GAAG,CAAC;IAC7C,IAAID,WAAW,KAAK,CAAC,CAAC,EAAE;MACtB,OAAOD,YAAY,CAACG,SAAS,CAACF,WAAW,GAAG,CAAC,CAAC;IAChD;IACA,OAAOD,YAAY;EACrB;EAEAI,KAAKA,CAACC,GAAW;IACf,IAAI,OAAOA,GAAG,KAAK,WAAW,IAAIA,GAAG,KAAK,IAAI,EAAE;MAC9C,OAAO,EAAE;IACX;IACA,OAAOA,GAAG,CAACC,OAAO,CAAC,KAAK,EAAE,MAAM,CAAC;IAAC;EACpC;EAEA3F,QAAQA,CAAC0C,GAAQ;IACf,IAAI,CAAC3E,iBAAiB,CAACwD,OAAO,GAAG,IAAI,CAACvC,cAAc,CAACiC,MAAM;IAC3D,IAAI,CAAClD,iBAAiB,CAAC8G,UAAU,GAAG,IAAI,CAACvF,iBAAiB,CAAC2B,MAAM;IACjE,IAAI,CAAClD,iBAAiB,CAAC+G,YAAY,GAAG,IAAI,CAAC3F,mBAAmB,CAAC8B,MAAM;IACrE,MAAM2E,WAAW,GAAsB;MACrCvI,cAAc,EAAE,IAAI,CAACU,iBAAiB,CAACV,cAAc;MACrDkE,OAAO,EAAE,IAAI,CAACvC,cAAc,CAACiC,MAAM;MACnCnC,kBAAkB,EAAE,IAAI,CAAC2G,KAAK,CAAC,IAAI,CAAC1H,iBAAiB,CAACe,kBAAmB,CAAC;MAC1E8D,UAAU,EAAE,IAAI,CAACd,iBAAiB,CAACb,MAAM,IAAI,CAAC,GAAG,KAAK,GAAG,IAAI;MAC7DvB,gBAAgB,EAAE,IAAI,CAAC3B,iBAAiB,CAAC2B,gBAAgB;MACzDmF,UAAU,EAAE,IAAI,CAACvF,iBAAiB,CAAC2B,MAAM;MACzC6D,YAAY,EAAE,IAAI,CAAC3F,mBAAmB,CAAC8B;KACxC;IAED,IAAI,IAAI,CAAChB,KAAK,IAAI,IAAI,CAACpC,QAAQ,EAAE;MAAE;MACjC,IAAI,CAACE,iBAAiB,CAACC,WAAW,GAAG,IAAI,CAACoH,kBAAkB,CAAC,IAAI,CAACvH,QAAQ,CAAC;MAC3E+H,WAAW,CAAC5H,WAAW,GAAG,IAAI,CAACoH,kBAAkB,CAAC,IAAI,CAACvH,QAAQ,CAAC;IAClE,CAAC,MAAM;MAAE;MACP,IAAI,IAAI,CAACA,QAAQ,EAAE;QACjB+H,WAAW,CAAC5H,WAAW,GAAG,IAAI,CAACoH,kBAAkB,CAAC,IAAI,CAACvH,QAAQ,CAAC;MAClE;MACA+H,WAAW,CAACC,YAAY,GAAG,IAAI,CAAC9H,iBAAiB,CAACpB,GAAG;IACvD;IACA,IAAI,CAACoI,UAAU,EAAE;IACjB,IAAI,IAAI,CAACtE,KAAK,CAACqF,aAAa,CAACC,MAAM,GAAG,CAAC,EAAE;MACvC,IAAI,CAACvF,OAAO,CAACwF,aAAa,CAAC,IAAI,CAACvF,KAAK,CAACqF,aAAa,CAAC;MACpD;IACF;IAEA,IAAI,CAACpF,iBAAiB,CAACuF,kCAAkC,CAAC;MAAEjE,IAAI,EAAE4D;IAAW,CAAE,CAAC,CAACvD,IAAI,CACnF5H,GAAG,CAAC6H,GAAG,IAAG;MACR,IAAIA,GAAG,CAACE,UAAU,KAAK,CAAC,EAAE;QACxB,IAAI,CAAChC,OAAO,CAAC0F,aAAa,CAAC,MAAM,CAAC;MACpC,CAAC,MAAM;QACL,IAAI,CAAC1F,OAAO,CAAC0F,aAAa,CAAC,UAAU,CAAC;MACxC;IACF,CAAC,CAAC,EACF3L,SAAS,CAAC,MAAM,IAAI,CAACqH,gBAAgB,EAAE,CAAC,EACxCpH,QAAQ,CAAC,MAAMkI,GAAG,CAACyD,KAAK,EAAE,CAAC,CAC5B,CAACtE,SAAS,EAAE;EACf;EAEAuE,WAAWA,CAACC,OAAe;IACzB,IAAI,CAACtF,SAAS,GAAGsF,OAAO;IACxB,IAAI,CAACzE,gBAAgB,EAAE,CAACC,SAAS,EAAE;EACrC;EAEA/E,QAAQA,CAAC0H,IAAS,EAAE9B,GAAQ;IAC1B,IAAI4D,MAAM,CAACC,OAAO,CAAC,WAAW/B,IAAI,CAACnH,cAAc,IAAI,CAAC,EAAE;MACtD,IAAI,CAACqD,iBAAiB,CAAC8F,oCAAoC,CAAC;QAC1DxE,IAAI,EAAE;UACJ,cAAc,EAAEwC,IAAI,CAAC7H;;OAExB,CAAC,CAAC0F,IAAI,CACL5H,GAAG,CAAC6H,GAAG,IAAG;QACR,IAAIA,GAAG,CAACE,UAAU,KAAK,CAAC,EAAE;UACxB,IAAI,CAAChC,OAAO,CAAC0F,aAAa,CAAC,MAAM,CAAC;QACpC,CAAC,MAAM;UACL,IAAI,CAAC1F,OAAO,CAACwD,YAAY,CAAC1B,GAAG,CAACmE,OAAQ,CAAC;QACzC;MACF,CAAC,CAAC,EACFlM,SAAS,CAAC,MAAM,IAAI,CAACqH,gBAAgB,EAAE,CAAC,EACxCpH,QAAQ,CAAC,MAAMkI,GAAG,CAACyD,KAAK,EAAE,CAAC,CAC5B,CAACtE,SAAS,EAAE;IACf;EACF;EAEA/B,OAAOA,CAAC4C,GAAQ;IACdA,GAAG,CAACyD,KAAK,EAAE;EACb;;;uCAnSW/F,0BAA0B,EAAAvF,EAAA,CAAA6L,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAA/L,EAAA,CAAA6L,iBAAA,CAAAG,EAAA,CAAAC,eAAA,GAAAjM,EAAA,CAAA6L,iBAAA,CAAAK,EAAA,CAAAC,cAAA,GAAAnM,EAAA,CAAA6L,iBAAA,CAAAO,EAAA,CAAAC,gBAAA,GAAArM,EAAA,CAAA6L,iBAAA,CAAAS,EAAA,CAAAC,gBAAA,GAAAvM,EAAA,CAAA6L,iBAAA,CAAAW,EAAA,CAAAC,MAAA,GAAAzM,EAAA,CAAA6L,iBAAA,CAAAa,EAAA,CAAAC,cAAA;IAAA;EAAA;;;YAA1BpH,0BAA0B;MAAAqH,SAAA;MAAAC,SAAA,WAAAC,iCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;;;;;;;;;;;;;;;UC5BrC/M,EADF,CAAAC,cAAA,iBAA0B,qBACR;UACdD,EAAA,CAAAoB,SAAA,qBAAiC;UACnCpB,EAAA,CAAAY,YAAA,EAAiB;UAIXZ,EAHN,CAAAC,cAAA,mBAAc,aACK,aACI,eACyC;UAAAD,EAAA,CAAAqB,MAAA,gCAC1D;UACFrB,EADE,CAAAY,YAAA,EAAQ,EACJ;UAEJZ,EADF,CAAAC,cAAA,aAAmB,oBACF;UACbD,EAAA,CAAAkC,UAAA,KAAA+K,4CAAA,mBAA+D;UAEnEjN,EADE,CAAAY,YAAA,EAAgB,EACZ;UAGFZ,EAFJ,CAAAC,cAAA,cAAmB,cACuB,iBACsC;UAAzCD,EAAA,CAAAc,UAAA,mBAAAoM,6DAAA;YAAAlN,EAAA,CAAAK,aAAA,CAAA8M,GAAA;YAAA,OAAAnN,EAAA,CAAAW,WAAA,CAASqM,GAAA,CAAAjG,gBAAA,EAAkB,CAAAC,SAAA,EAAY;UAAA,EAAC;UACzEhH,EAAA,CAAAoB,SAAA,aAAkC;UAClCpB,EAAA,CAAAqB,MAAA,sBACF;UAAArB,EAAA,CAAAY,YAAA,EAAS;UACTZ,EAAA,CAAAkC,UAAA,KAAAkL,6CAAA,qBAAuE;UAI7EpN,EAFI,CAAAY,YAAA,EAAM,EACF,EACF;UAMEZ,EAJR,CAAAC,cAAA,eAAmC,iBAC+D,aACvF,cACgD,cACpB;UAAAD,EAAA,CAAAqB,MAAA,UAAE;UAAArB,EAAA,CAAAY,YAAA,EAAK;UACtCZ,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAqB,MAAA,gCAAI;UAAArB,EAAA,CAAAY,YAAA,EAAK;UACvCZ,EAAA,CAAAC,cAAA,cAA0C;UAAAD,EAAA,CAAAqB,MAAA,oBAAE;UAEhDrB,EAFgD,CAAAY,YAAA,EAAK,EAC9C,EACC;UACRZ,EAAA,CAAAC,cAAA,aAAO;UACLD,EAAA,CAAAkC,UAAA,KAAAmL,yCAAA,iBAAsD;UAe9DrN,EAHM,CAAAY,YAAA,EAAQ,EACF,EACJ,EACO;UAEbZ,EADF,CAAAC,cAAA,0BAAsD,0BAES;UAD7CD,EAAA,CAAAE,gBAAA,wBAAAoN,0EAAAlN,MAAA;YAAAJ,EAAA,CAAAK,aAAA,CAAA8M,GAAA;YAAAnN,EAAA,CAAAS,kBAAA,CAAAuM,GAAA,CAAA9G,SAAA,EAAA9F,MAAA,MAAA4M,GAAA,CAAA9G,SAAA,GAAA9F,MAAA;YAAA,OAAAJ,EAAA,CAAAW,WAAA,CAAAP,MAAA;UAAA,EAAoB;UAClCJ,EAAA,CAAAc,UAAA,wBAAAwM,0EAAAlN,MAAA;YAAAJ,EAAA,CAAAK,aAAA,CAAA8M,GAAA;YAAA,OAAAnN,EAAA,CAAAW,WAAA,CAAcqM,GAAA,CAAAzB,WAAA,CAAAnL,MAAA,CAAmB;UAAA,EAAC;UAGxCJ,EAFI,CAAAY,YAAA,EAAiB,EACF,EACT;UAEVZ,EAAA,CAAAkC,UAAA,KAAAqL,kDAAA,kCAAAvN,EAAA,CAAAwN,sBAAA,CAAoD;;;UAhDOxN,EAAA,CAAAsC,SAAA,IAAY;UAAZtC,EAAA,CAAAyC,UAAA,SAAAuK,GAAA,CAAAtK,MAAA,CAAY;UASN1C,EAAA,CAAAsC,SAAA,GAAc;UAAdtC,EAAA,CAAAyC,UAAA,SAAAuK,GAAA,CAAAS,QAAA,CAAc;UAgBhDzN,EAAA,CAAAsC,SAAA,IAAkB;UAAlBtC,EAAA,CAAAyC,UAAA,YAAAuK,GAAA,CAAA1G,aAAA,CAAkB;UAiB7BtG,EAAA,CAAAsC,SAAA,GAAoB;UAApBtC,EAAA,CAAAa,gBAAA,SAAAmM,GAAA,CAAA9G,SAAA,CAAoB;UAAuBlG,EAAtB,CAAAyC,UAAA,aAAAuK,GAAA,CAAA/G,QAAA,CAAqB,mBAAA+G,GAAA,CAAA7G,YAAA,CAAgC;;;qBDhC1F1G,YAAY,EAAAiO,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EACZ9N,YAAY,EAAA+N,EAAA,CAAAC,oBAAA,EAAAD,EAAA,CAAAE,eAAA,EAAAF,EAAA,CAAAG,OAAA,EAAAhC,EAAA,CAAAiC,eAAA,EAAAjC,EAAA,CAAAkC,mBAAA,EAAAlC,EAAA,CAAAmC,qBAAA,EAAAnC,EAAA,CAAAoC,qBAAA,EAAApC,EAAA,CAAAqC,mBAAA,EAAArC,EAAA,CAAAsC,gBAAA,EAAAtC,EAAA,CAAAuC,iBAAA,EAAAvC,EAAA,CAAAwC,iBAAA,EAAAxC,EAAA,CAAAyC,oBAAA,EAAAC,GAAA,CAAAC,aAAA,EAAAC,GAAA,CAAAC,mBAAA,EAAAC,GAAA,CAAAC,kBAAA;MAAAC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}