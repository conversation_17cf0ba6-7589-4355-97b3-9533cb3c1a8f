{"ast": null, "code": "import toDate from \"../toDate/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name isTuesday\n * @category Weekday Helpers\n * @summary Is the given date Tuesday?\n *\n * @description\n * Is the given date Tuesday?\n *\n * @param {Date|Number} date - the date to check\n * @returns {Boolean} the date is Tuesday\n * @throws {TypeError} 1 argument required\n *\n * @example\n * // Is 23 September 2014 Tuesday?\n * const result = isTuesday(new Date(2014, 8, 23))\n * //=> true\n */\nexport default function isTuesday(dirtyDate) {\n  requiredArgs(1, arguments);\n  return toDate(dirtyDate).getDay() === 2;\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}