{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { Component } from '@angular/core';\nimport { SharedModule } from '../../../components/shared.module';\nimport { CommonModule } from '@angular/common';\nimport { NbCheckboxModule } from '@nebular/theme';\nimport { BaseComponent } from '../../../components/base/baseComponent';\nimport { tap } from 'rxjs';\nimport { BaseFilePipe } from 'src/app/@theme/pipes/base-file.pipe';\nlet DetailContentManagementLandownerComponent = class DetailContentManagementLandownerComponent extends BaseComponent {\n  constructor(_allow, route, message, _formItemService, _regularNoticeFileService, _utilityService, valid, location, _materialService) {\n    super(_allow);\n    this._allow = _allow;\n    this.route = route;\n    this.message = message;\n    this._formItemService = _formItemService;\n    this._regularNoticeFileService = _regularNoticeFileService;\n    this._utilityService = _utilityService;\n    this.valid = valid;\n    this.location = location;\n    this._materialService = _materialService;\n    this.typeContentManagementLandowner = {\n      CFormType: 1,\n      CNoticeType: 1\n    };\n    this.CUiTypeOptions = [{\n      value: 1,\n      label: '建材選色'\n    }, {\n      value: 2,\n      label: '群組選樣_選色'\n    }, {\n      value: 3,\n      label: '建材選樣'\n    }];\n    this.CRemarkTypeOptions = [\"正常\", \"留料\"];\n    this.selectedItems = {};\n    this.selectedRemarkType = {};\n    this.isNew = true;\n  }\n  ngOnInit() {\n    this.route.paramMap.subscribe(params => {\n      if (params) {\n        const idParam = params.get('id');\n        const id = idParam ? +idParam : 0;\n        this.buildCaseId = id;\n        if (this.buildCaseId) {\n          this.getListRegularNoticeFileHouseHold();\n        }\n      }\n    });\n  }\n  getItemByValue(value, options) {\n    for (const item of options) {\n      if (item.value === value) {\n        return item;\n      }\n    }\n    return null;\n  }\n  onCheckAllChange(checked, formItemReq_) {\n    formItemReq_.allSelected = checked;\n    this.houseHoldList.forEach(item => {\n      formItemReq_.selectedItems[item] = checked;\n    });\n  }\n  detectFiles(event, formItemReq_) {\n    const file = event.target.files[0];\n    if (file) {\n      let reader = new FileReader();\n      reader.readAsDataURL(file);\n      reader.onload = () => {\n        let base64Str = reader.result;\n        if (!base64Str) {\n          return;\n        }\n        if (formItemReq_.listPictures.length > 0) {\n          formItemReq_.listPictures[0] = {\n            id: new Date().getTime(),\n            name: file.name.split('.')[0],\n            data: base64Str,\n            extension: this._utilityService.getFileExtension(file.name),\n            CFile: file\n          };\n        } else {\n          formItemReq_.listPictures.push({\n            id: new Date().getTime(),\n            name: file.name.split('.')[0],\n            data: base64Str,\n            extension: this._utilityService.getFileExtension(file.name),\n            CFile: file\n          });\n        }\n        event.target.value = null;\n      };\n    }\n  }\n  removeImage(pictureId, formItemReq_) {\n    if (formItemReq_.listPictures.length) {\n      formItemReq_.listPictures = formItemReq_.listPictures.filter(x => x.id != pictureId);\n    }\n  }\n  renameFile(event, index, formItemReq_) {\n    var blob = formItemReq_.listPictures[index].CFile.slice(0, formItemReq_.listPictures[index].CFile.size, formItemReq_.listPictures[index].CFile.type);\n    var newFile = new File([blob], `${event.target.value + '.' + formItemReq_.listPictures[index].extension}`, {\n      type: formItemReq_.listPictures[index].CFile.type\n    });\n    formItemReq_.listPictures[index].CFile = newFile;\n  }\n  onCheckboxHouseHoldListChange(checked, item, formItemReq_) {\n    if (checked) {\n      formItemReq_.selectedItems[item] = checked;\n      formItemReq_.allSelected = this.houseHoldList.every(item => formItemReq_.selectedItems[item] == checked);\n    } else {\n      formItemReq_.allSelected = false;\n    }\n  }\n  onCheckboxRemarkChange(checked, item, formItemReq_) {\n    formItemReq_.selectedRemarkType[item] = checked;\n  }\n  createRemarkObject(CRemarkTypeOptions, CRemarkType) {\n    const remarkObject = {};\n    for (const option of CRemarkTypeOptions) {\n      remarkObject[option] = false;\n    }\n    const remarkTypes = CRemarkType.split('-');\n    for (const type of remarkTypes) {\n      if (CRemarkTypeOptions.includes(type)) {\n        remarkObject[type] = true;\n      }\n    }\n    return remarkObject;\n  }\n  getMaterialList() {\n    this._materialService.apiMaterialGetMaterialListPost$Json({\n      body: {\n        CBuildCaseId: this.buildCaseId,\n        CPagi: false\n      }\n    }).pipe(tap(res => {\n      if (res.Entries && res.StatusCode == 0) {\n        this.houseHoldList.forEach(item => this.selectedItems[item] = false);\n        this.CRemarkTypeOptions.forEach(item => this.selectedRemarkType[item] = false);\n        this.arrListFormItemReq = res.Entries.map(o => {\n          return {\n            CDesignFileUrl: null,\n            CFormItemHouseHold: null,\n            CFormId: null,\n            CLocation: o.CLocation,\n            CName: o.CName,\n            CPart: o.CPart,\n            CItemName: `${o.CName}-${o.CPart}-${o.CLocation}`,\n            CRemarkType: null,\n            CTotalAnswer: 0,\n            CRequireAnswer: undefined,\n            CUiType: 0,\n            selectedItems: {},\n            selectedRemarkType: this.selectedRemarkType,\n            allSelected: false,\n            listPictures: [],\n            selectedCUiType: this.CUiTypeOptions[0]\n          };\n        });\n        this.arrListFormItemReq = [...this.mergeItems(this.arrListFormItemReq)];\n      }\n    })).subscribe();\n  }\n  getListFormItem() {\n    this._formItemService.apiFormItemGetListFormItemPost$Json({\n      body: {\n        CBuildCaseId: this.buildCaseId,\n        CFormType: this.typeContentManagementLandowner.CFormType,\n        CIsPaging: false\n      }\n    }).pipe(tap(res => {\n      if (res.Entries && res.StatusCode == 0) {\n        this.listFormItem = res.Entries;\n        this.isNew = res.Entries.formItems ? false : true;\n        if (res.Entries.formItems) {\n          this.houseHoldList.forEach(item => this.selectedItems[item] = false);\n          this.CRemarkTypeOptions.forEach(item => this.selectedRemarkType[item] = false);\n          this.arrListFormItemReq = res.Entries.formItems.map(o => {\n            return {\n              CFormId: this.listFormItem.CFormId,\n              CDesignFileUrl: o.CDesignFileUrl,\n              CFile: o.CFile,\n              CFormItemHouseHold: o.CFormItemHouseHold,\n              CFormItemId: o.CFormItemId,\n              CLocation: o.CLocation,\n              CName: o.CName,\n              CPart: o.CPart,\n              CItemName: o.CItemName ? o.CItemName : `${o.CName}-${o.CPart}-${o.CLocation}`,\n              CRemarkType: o.CRemarkType,\n              CTotalAnswer: o.CTotalAnswer,\n              CRequireAnswer: o.CUiType === 3 ? 1 : o.CRequireAnswer,\n              CUiType: o.CUiType,\n              selectedItems: o.tblFormItemHouseholds.length ? this.createArrayObjectFromArray(this.houseHoldList, o.tblFormItemHouseholds) : {\n                ...this.selectedItems\n              },\n              selectedRemarkType: o.CRemarkType ? this.createRemarkObject(this.CRemarkTypeOptions, o.CRemarkType) : {\n                ...this.selectedRemarkType\n              },\n              allSelected: o.tblFormItemHouseholds.length == this.houseHoldList.length,\n              listPictures: [],\n              selectedCUiType: o.CUiType ? this.getItemByValue(o.CUiType, this.CUiTypeOptions) : this.CUiTypeOptions[0]\n            };\n          });\n        } else {\n          this.getMaterialList();\n        }\n      }\n    })).subscribe();\n  }\n  changeSelectCUiType(formItemReq) {\n    if (formItemReq.selectedCUiType && formItemReq.selectedCUiType.value === 3) {\n      formItemReq.CRequireAnswer = 1;\n    }\n  }\n  getHouseHoldListByNoticeType(data) {\n    for (let item of data) {\n      if (item.CNoticeType === this.typeContentManagementLandowner.CNoticeType) {\n        return item.CHouseHoldList;\n      }\n    }\n    return [];\n  }\n  getKeysWithTrueValue(obj) {\n    return Object.keys(obj).filter(key => obj[key]);\n  }\n  getKeysWithTrueValueJoined(obj) {\n    return Object.keys(obj).filter(key => obj[key]).join('-');\n  }\n  getCRemarkType(selectedCUiType, selectedRemarkType) {\n    if (selectedCUiType && selectedCUiType.value == 3) {\n      return this.getKeysWithTrueValueJoined(selectedRemarkType);\n    }\n  }\n  getStringAfterComma(inputString) {\n    const parts = inputString.split(',');\n    if (parts.length > 1) {\n      return parts[1];\n    } else return \"\";\n  }\n  formatFile(listPictures) {\n    if (listPictures && listPictures.length > 0) {\n      return {\n        Base64String: this.getStringAfterComma(listPictures[0].data) || null,\n        FileExtension: listPictures[0].extension || null,\n        FileName: listPictures[0].CFile.name || listPictures[0].name || null\n      };\n    } else return undefined;\n  }\n  // formatParam() {\n  //   const result: SaveListFormItemReq[] = \n  //   })\n  //   return result\n  // }\n  mergeItems(items) {\n    const map = new Map();\n    items.forEach(item => {\n      const key = `${item.CLocation}_${item.CName}_${item.CPart}`;\n      if (map.has(key)) {\n        const existing = map.get(key);\n        existing.count += 1;\n      } else {\n        map.set(key, {\n          item: {\n            ...item\n          },\n          count: 1\n        });\n      }\n    });\n    return Array.from(map.values()).map(({\n      item,\n      count\n    }) => ({\n      ...item,\n      CTotalAnswer: count\n    }));\n  }\n  validation() {\n    this.valid.clear();\n    let hasInvalidCUiType = false;\n    let hasInvalidCRequireAnswer = false;\n    let hasInvalidItemName = false;\n    for (const item of this.saveListFormItemReq) {\n      if (!hasInvalidCUiType && !item.CUiType) {\n        hasInvalidCUiType = true;\n      }\n      if (!hasInvalidCRequireAnswer && !item.CRequireAnswer) {\n        hasInvalidCRequireAnswer = true;\n      }\n      if (item.CTotalAnswer && item.CRequireAnswer) {\n        if (item.CRequireAnswer > item.CTotalAnswer) {\n          this.valid.addErrorMessage('[必填數量]' + ' <= ' + item.CTotalAnswer + ` (${item.CItemName}) `);\n        }\n      }\n      if (!hasInvalidItemName && !item.CItemName) {\n        hasInvalidItemName = true;\n      }\n    }\n    if (hasInvalidCUiType) {\n      this.valid.addErrorMessage('[前台UI類型]' + ' 必填');\n    }\n    if (hasInvalidCRequireAnswer) {\n      this.valid.addErrorMessage('[必填數量]' + ' 必填且>0');\n    }\n    if (hasInvalidItemName) {\n      this.valid.addErrorMessage('[廚房-廚具-櫃體]' + ' 必填');\n    }\n  }\n  onSubmit() {\n    this.saveListFormItemReq = this.arrListFormItemReq.map(e => {\n      return {\n        CDesignFileUrl: e.CDesignFileUrl ? e.CDesignFileUrl : null,\n        CFile: e.listPictures ? this.formatFile(e.listPictures) : undefined,\n        CFormItemHouseHold: this.getKeysWithTrueValue(e.selectedItems),\n        CFormItemId: e.CFormItemId ? e.CFormItemId : null,\n        CFormID: this.isNew ? null : this.listFormItem.CFormId,\n        CName: e.CName,\n        CPart: e.CPart,\n        CLocation: e.CLocation,\n        CItemName: e.CItemName,\n        // ? e.CItemName : `${e.CName}-${e.CPart}-${e.CLocation}`,\n        CRemarkType: e.selectedCUiType.value !== 3 ? null : this.getCRemarkType(e.selectedCUiType, e.selectedRemarkType) || null,\n        CTotalAnswer: e.CTotalAnswer,\n        CRequireAnswer: e.CRequireAnswer,\n        CUiType: e.selectedCUiType.value\n      };\n    });\n    this.validation();\n    if (this.valid.errorMessages.length > 0) {\n      this.message.showErrorMSGs(this.valid.errorMessages);\n      return;\n    }\n    if (this.isNew) {\n      this.createListFormItem();\n    } else {\n      this.saveListFormItem();\n    }\n  }\n  saveListFormItem() {\n    this._formItemService.apiFormItemSaveListFormItemPost$Json({\n      body: this.saveListFormItemReq\n    }).subscribe(res => {\n      if (res.StatusCode == 0) {\n        this.message.showSucessMSG(\"執行成功\");\n        // this.getListFormItem()\n        this.goBack();\n      }\n    });\n  }\n  createListFormItem() {\n    this.creatListFormItem = {\n      CBuildCaseId: this.buildCaseId,\n      CFormItem: this.saveListFormItemReq || null,\n      CFormType: this.typeContentManagementLandowner.CFormType\n    };\n    this._formItemService.apiFormItemCreateListFormItemPost$Json({\n      body: this.creatListFormItem\n    }).subscribe(res => {\n      if (res.StatusCode == 0) {\n        this.message.showSucessMSG(\"執行成功\");\n        // this.getListFormItem()\n        this.goBack();\n      }\n    });\n  }\n  createArrayObjectFromArray(a, b) {\n    const c = {};\n    for (const item of a) {\n      const matchingItem = b.find(bItem => bItem.CHousehold === item && bItem.CIsSelect);\n      c[item] = !!matchingItem;\n    }\n    return c;\n  } //[\"House1\", \"House2\", \"House3\"] => [{CHousehold: \"House1\", CIsSelect: true,... }, ... ]\n  getListRegularNoticeFileHouseHold() {\n    this._regularNoticeFileService.apiRegularNoticeFileGetListRegularNoticeFileHouseHoldPost$Json({\n      body: this.buildCaseId\n    }).pipe(tap(res => {\n      if (res.Entries && res.StatusCode == 0) {\n        this.houseHoldList = this.getHouseHoldListByNoticeType(res.Entries);\n        this.getListFormItem();\n      }\n    })).subscribe();\n  }\n  goBack() {\n    this.location.back();\n  }\n};\nDetailContentManagementLandownerComponent = __decorate([Component({\n  selector: 'ngx-detail-content-management-landowner',\n  templateUrl: './detail-content-management-landowner.component.html',\n  styleUrls: ['./detail-content-management-landowner.component.scss'],\n  standalone: true,\n  imports: [CommonModule, SharedModule, NbCheckboxModule, BaseFilePipe]\n})], DetailContentManagementLandownerComponent);\nexport { DetailContentManagementLandownerComponent };", "map": {"version": 3, "names": ["Component", "SharedModule", "CommonModule", "NbCheckboxModule", "BaseComponent", "tap", "BaseFilePipe", "DetailContentManagementLandownerComponent", "constructor", "_allow", "route", "message", "_formItemService", "_regularNoticeFileService", "_utilityService", "valid", "location", "_materialService", "typeContentManagementLandowner", "CFormType", "CNoticeType", "CUiTypeOptions", "value", "label", "CRemarkTypeOptions", "selectedItems", "selectedRemarkType", "isNew", "ngOnInit", "paramMap", "subscribe", "params", "idParam", "get", "id", "buildCaseId", "getListRegularNoticeFileHouseHold", "getItemByValue", "options", "item", "onCheckAllChange", "checked", "formItemReq_", "allSelected", "houseHoldList", "for<PERSON>ach", "detectFiles", "event", "file", "target", "files", "reader", "FileReader", "readAsDataURL", "onload", "base64Str", "result", "listPictures", "length", "Date", "getTime", "name", "split", "data", "extension", "getFileExtension", "CFile", "push", "removeImage", "pictureId", "filter", "x", "renameFile", "index", "blob", "slice", "size", "type", "newFile", "File", "onCheckboxHouseHoldListChange", "every", "onCheckboxRemarkChange", "createRemarkObject", "CRemarkType", "remarkObject", "option", "remarkTypes", "includes", "getMaterialList", "apiMaterialGetMaterialListPost$Json", "body", "CBuildCaseId", "CPagi", "pipe", "res", "Entries", "StatusCode", "arrListFormItemReq", "map", "o", "CDesignFileUrl", "CFormItemHouseHold", "CFormId", "CLocation", "CName", "<PERSON>art", "CItemName", "CTotalAnswer", "CRequireAnswer", "undefined", "CUiType", "selectedCUiType", "mergeItems", "getListFormItem", "apiFormItemGetListFormItemPost$Json", "CIsPaging", "listFormItem", "formItems", "CFormItemId", "tblFormItemHouseholds", "createArrayObjectFromArray", "changeSelectCUiType", "formItemReq", "getHouseHoldListByNoticeType", "CHouseHoldList", "getKeysWithTrueValue", "obj", "Object", "keys", "key", "getKeysWithTrueValueJoined", "join", "getCRemarkType", "getStringAfterComma", "inputString", "parts", "formatFile", "Base64String", "FileExtension", "FileName", "items", "Map", "has", "existing", "count", "set", "Array", "from", "values", "validation", "clear", "hasInvalidCUiType", "hasInvalidCRequireAnswer", "hasInvalidItemName", "saveListFormItemReq", "addErrorMessage", "onSubmit", "e", "CFormID", "errorMessages", "showErrorMSGs", "createListFormItem", "saveListFormItem", "apiFormItemSaveListFormItemPost$Json", "showSucessMSG", "goBack", "creatListFormItem", "CFormItem", "apiFormItemCreateListFormItemPost$Json", "a", "b", "c", "matchingItem", "find", "bItem", "CHousehold", "CIsSelect", "apiRegularNoticeFileGetListRegularNoticeFileHouseHoldPost$Json", "back", "__decorate", "selector", "templateUrl", "styleUrls", "standalone", "imports"], "sources": ["C:\\Users\\<USER>\\Documents\\jeansalechange\\adminFront\\src\\app\\pages\\selection-management\\content-management-landowner\\detail-content-management-landowner\\detail-content-management-landowner.component.ts"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { SharedModule } from '../../../components/shared.module';\r\nimport { CommonModule, Location } from '@angular/common';\r\nimport { NbCheckboxModule } from '@nebular/theme';\r\nimport { BaseComponent } from '../../../components/base/baseComponent';\r\nimport { AllowHelper } from 'src/app/shared/helper/allowHelper';\r\nimport { FormItemService, MaterialService, RegularNoticeFileService } from 'src/services/api/services';\r\nimport { ActivatedRoute } from '@angular/router';\r\nimport { MessageService } from 'src/app/shared/services/message.service';\r\n\r\nimport { tap } from 'rxjs';\r\nimport { CreateListFormItem, FileViewModel, GetListFormItemRes, SaveListFormItemReq } from 'src/services/api/models';\r\nimport { BaseFilePipe } from 'src/app/@theme/pipes/base-file.pipe';\r\nimport { UtilityService } from 'src/app/shared/services/utility.service';\r\nimport { ValidationHelper } from 'src/app/shared/helper/validationHelper';\r\n\r\nexport interface ExtendedSaveListFormItemReq {\r\n  CDesignFileUrl?: string | null;\r\n  CFile?: FileViewModel,\r\n  CFormItemHouseHold?: Array<string> | null;\r\n  CFormItemId?: number;\r\n  CItemName?: string;\r\n  CFormID?: number;\r\n  CPart?: string | null;\r\n  CName?: string | null;\r\n  CLocation?: string | null;\r\n  CRemarkType?: string | null;\r\n  CTotalAnswer?: number;\r\n  CRequireAnswer?: number;\r\n  CUiType?: number;\r\n  selectedCUiType: any | null;\r\n  selectedItems: { [key: string]: boolean }\r\n  selectedRemarkType?: { [key: string]: boolean }\r\n  allSelected: boolean,\r\n  listPictures: any[]\r\n}\r\n\r\n@Component({\r\n  selector: 'ngx-detail-content-management-landowner',\r\n  templateUrl: './detail-content-management-landowner.component.html',\r\n  styleUrls: ['./detail-content-management-landowner.component.scss'],\r\n  standalone: true,\r\n  imports: [CommonModule, SharedModule, NbCheckboxModule, BaseFilePipe],\r\n\r\n})\r\n\r\nexport class DetailContentManagementLandownerComponent extends BaseComponent implements OnInit {\r\n  constructor(\r\n    private _allow: AllowHelper,\r\n    private route: ActivatedRoute,\r\n    private message: MessageService,\r\n    private _formItemService: FormItemService,\r\n    private _regularNoticeFileService: RegularNoticeFileService,\r\n    private _utilityService: UtilityService,\r\n    private valid: ValidationHelper,\r\n    private location: Location,\r\n    private _materialService: MaterialService\r\n  ) {\r\n    super(_allow)\r\n  }\r\n\r\n  typeContentManagementLandowner = {\r\n    CFormType: 1,\r\n    CNoticeType: 1\r\n  }\r\n  CUiTypeOptions: any[] = [\r\n    {\r\n      value: 1, label: '建材選色'\r\n    },\r\n    {\r\n      value: 2, label: '群組選樣_選色'\r\n    }, {\r\n      value: 3, label: '建材選樣'\r\n    }\r\n  ]\r\n  CRemarkTypeOptions = [\"正常\", \"留料\"]\r\n  buildCaseId: number\r\n\r\n  override ngOnInit(): void {\r\n    this.route.paramMap.subscribe(params => {\r\n      if (params) {\r\n        const idParam = params.get('id');\r\n        const id = idParam ? +idParam : 0;\r\n        this.buildCaseId = id\r\n        if (this.buildCaseId) {\r\n          this.getListRegularNoticeFileHouseHold()\r\n        }\r\n      }\r\n    })\r\n  }\r\n\r\n\r\n  getItemByValue(value: any, options: any[]) {\r\n    for (const item of options) {\r\n      if (item.value === value) {\r\n        return item;\r\n      }\r\n    }\r\n    return null;\r\n  }\r\n\r\n\r\n  selectedItems: { [key: string]: boolean } = {};\r\n  selectedRemarkType: { [key: string]: boolean } = {};\r\n\r\n  onCheckAllChange(checked: boolean, formItemReq_: any) {\r\n    formItemReq_.allSelected = checked;\r\n    this.houseHoldList.forEach(item => {\r\n      formItemReq_.selectedItems[item] = checked;\r\n    });\r\n  }\r\n\r\n\r\n  detectFiles(event: any, formItemReq_: any) {\r\n    const file = event.target.files[0]\r\n    if (file) {\r\n      let reader = new FileReader();\r\n      reader.readAsDataURL(file);\r\n      reader.onload = () => {\r\n        let base64Str: string = reader.result as string;\r\n        if (!base64Str) {\r\n          return;\r\n        }\r\n        if (formItemReq_.listPictures.length > 0) {\r\n          formItemReq_.listPictures[0] = {\r\n            id: new Date().getTime(),\r\n            name: file.name.split('.')[0],\r\n            data: base64Str,\r\n            extension: this._utilityService.getFileExtension(file.name),\r\n            CFile: file\r\n          };\r\n        } else {\r\n          formItemReq_.listPictures.push({\r\n            id: new Date().getTime(),\r\n            name: file.name.split('.')[0],\r\n            data: base64Str,\r\n            extension: this._utilityService.getFileExtension(file.name),\r\n            CFile: file\r\n          });\r\n        }\r\n        event.target.value = null;\r\n      };\r\n    }\r\n  }\r\n\r\n  removeImage(pictureId: number, formItemReq_: any) {\r\n    if (formItemReq_.listPictures.length) {\r\n      formItemReq_.listPictures = formItemReq_.listPictures.filter((x: any) => x.id != pictureId)\r\n    }\r\n  }\r\n\r\n  renameFile(event: any, index: number, formItemReq_: any) {\r\n    var blob = formItemReq_.listPictures[index].CFile.slice(0, formItemReq_.listPictures[index].CFile.size, formItemReq_.listPictures[index].CFile.type);\r\n    var newFile = new File([blob], `${event.target.value + '.' + formItemReq_.listPictures[index].extension}`, { type: formItemReq_.listPictures[index].CFile.type });\r\n    formItemReq_.listPictures[index].CFile = newFile\r\n  }\r\n\r\n  onCheckboxHouseHoldListChange(checked: boolean, item: string, formItemReq_: any) {\r\n    if (checked) {\r\n      formItemReq_.selectedItems[item] = checked;\r\n      formItemReq_.allSelected = this.houseHoldList.every(item => formItemReq_.selectedItems[item] == checked);\r\n    } else {\r\n      formItemReq_.allSelected = false\r\n    }\r\n  }\r\n\r\n\r\n\r\n  onCheckboxRemarkChange(checked: boolean, item: string, formItemReq_: any) {\r\n    formItemReq_.selectedRemarkType[item] = checked;\r\n  }\r\n\r\n  createRemarkObject(CRemarkTypeOptions: string[], CRemarkType: string): { [key: string]: boolean } {\r\n    const remarkObject: { [key: string]: boolean } = {};\r\n    for (const option of CRemarkTypeOptions) {\r\n      remarkObject[option] = false;\r\n    }\r\n    const remarkTypes = CRemarkType.split('-');\r\n    for (const type of remarkTypes) {\r\n      if (CRemarkTypeOptions.includes(type)) {\r\n        remarkObject[type] = true;\r\n      }\r\n    }\r\n    return remarkObject;\r\n  }\r\n\r\n  getMaterialList() { // call when create\r\n    this._materialService.apiMaterialGetMaterialListPost$Json({\r\n      body: {\r\n        CBuildCaseId: this.buildCaseId,\r\n        CPagi: false\r\n      }\r\n    }).pipe(\r\n      tap(res => {\r\n        if (res.Entries && res.StatusCode == 0) {\r\n\r\n          this.houseHoldList.forEach(item => this.selectedItems[item] = false)\r\n\r\n          this.CRemarkTypeOptions.forEach(item => this.selectedRemarkType[item] = false)\r\n          this.arrListFormItemReq = res.Entries.map((o: any) => {\r\n            return {\r\n              CDesignFileUrl: null,\r\n              CFormItemHouseHold: null,\r\n              CFormId: null,\r\n              CLocation: o.CLocation,\r\n              CName: o.CName,\r\n              CPart: o.CPart,\r\n              CItemName: `${o.CName}-${o.CPart}-${o.CLocation}`,\r\n              CRemarkType: null,\r\n              CTotalAnswer: 0,\r\n              CRequireAnswer: undefined,\r\n              CUiType: 0,\r\n              selectedItems: {},\r\n              selectedRemarkType: this.selectedRemarkType,\r\n              allSelected: false,\r\n              listPictures: [],\r\n              selectedCUiType: this.CUiTypeOptions[0]\r\n            }\r\n          })\r\n          this.arrListFormItemReq = [...this.mergeItems(this.arrListFormItemReq)]\r\n        }\r\n      })\r\n    ).subscribe();\r\n  }\r\n\r\n  listFormItem: GetListFormItemRes\r\n  isNew: boolean = true\r\n\r\n  getListFormItem() {\r\n    this._formItemService.apiFormItemGetListFormItemPost$Json({\r\n      body: {\r\n        CBuildCaseId: this.buildCaseId,\r\n        CFormType: this.typeContentManagementLandowner.CFormType,\r\n        CIsPaging: false\r\n      }\r\n    }).pipe(\r\n      tap(res => {\r\n        if (res.Entries && res.StatusCode == 0) {\r\n          this.listFormItem = res.Entries\r\n          this.isNew = res.Entries.formItems ? false : true\r\n          if (res.Entries.formItems) {\r\n\r\n            this.houseHoldList.forEach(item => this.selectedItems[item] = false)\r\n            this.CRemarkTypeOptions.forEach(item => this.selectedRemarkType[item] = false)\r\n\r\n            this.arrListFormItemReq = res.Entries.formItems.map((o: any) => {\r\n              return {\r\n                CFormId: this.listFormItem.CFormId,\r\n                CDesignFileUrl: o.CDesignFileUrl,\r\n                CFile: o.CFile,\r\n                CFormItemHouseHold: o.CFormItemHouseHold,\r\n                CFormItemId: o.CFormItemId,\r\n                CLocation: o.CLocation,\r\n                CName: o.CName,\r\n                CPart: o.CPart,\r\n                CItemName: o.CItemName ? o.CItemName : `${o.CName}-${o.CPart}-${o.CLocation}`,\r\n                CRemarkType: o.CRemarkType,\r\n                CTotalAnswer: o.CTotalAnswer,\r\n                CRequireAnswer: o.CUiType===3 ? 1: o.CRequireAnswer,\r\n                CUiType: o.CUiType,\r\n                selectedItems: o.tblFormItemHouseholds.length ? this.createArrayObjectFromArray(this.houseHoldList, o.tblFormItemHouseholds) : { ...this.selectedItems },\r\n                selectedRemarkType: o.CRemarkType ? this.createRemarkObject(this.CRemarkTypeOptions, o.CRemarkType) : { ...this.selectedRemarkType },\r\n                allSelected: o.tblFormItemHouseholds.length == this.houseHoldList.length,\r\n                listPictures: [],\r\n                selectedCUiType: o.CUiType ? this.getItemByValue(o.CUiType, this.CUiTypeOptions) : this.CUiTypeOptions[0]\r\n              }\r\n            })\r\n          } else {\r\n            this.getMaterialList()\r\n          }\r\n        }\r\n      })\r\n    ).subscribe()\r\n  }\r\n\r\n  changeSelectCUiType(formItemReq: any) {\r\n    if(formItemReq.selectedCUiType && formItemReq.selectedCUiType.value ===3) {\r\n      formItemReq.CRequireAnswer = 1\r\n    }\r\n  }\r\n\r\n  getHouseHoldListByNoticeType(data: any[]) {\r\n    for (let item of data) {\r\n      if (item.CNoticeType === this.typeContentManagementLandowner.CNoticeType) {\r\n        return item.CHouseHoldList;\r\n      }\r\n    }\r\n    return [];\r\n  }\r\n\r\n  arrListFormItemReq: ExtendedSaveListFormItemReq[]\r\n\r\n  getKeysWithTrueValue(obj: Record<string, boolean>): string[] { // {\"key\": true } => [\"key\"]\r\n    return Object.keys(obj).filter(key => obj[key]);\r\n  }\r\n\r\n  getKeysWithTrueValueJoined(obj: Record<string, boolean>): string {\r\n    return Object.keys(obj)\r\n      .filter(key => obj[key])\r\n      .join('-');\r\n  }\r\n\r\n  getCRemarkType(selectedCUiType: any, selectedRemarkType: any): any {\r\n    if (selectedCUiType && selectedCUiType.value == 3) {\r\n      return this.getKeysWithTrueValueJoined(selectedRemarkType)\r\n    }\r\n  }\r\n\r\n  getStringAfterComma(inputString: string): string {\r\n    const parts = inputString.split(',');\r\n    if (parts.length > 1) {\r\n      return parts[1];\r\n    } else return \"\"\r\n  }\r\n\r\n  formatFile(listPictures: any) {\r\n    if (listPictures && listPictures.length > 0) {\r\n      return {\r\n        Base64String: this.getStringAfterComma(listPictures[0].data) || null,\r\n        FileExtension: listPictures[0].extension || null,\r\n        FileName: listPictures[0].CFile.name || listPictures[0].name || null,\r\n      }\r\n    } else return undefined\r\n\r\n  }\r\n\r\n  // formatParam() {\r\n  //   const result: SaveListFormItemReq[] = \r\n  //   })\r\n  //   return result\r\n  // }\r\n  mergeItems(items: ExtendedSaveListFormItemReq[]): ExtendedSaveListFormItemReq[] {\r\n    const map = new Map<string, { item: ExtendedSaveListFormItemReq, count: number }>();\r\n\r\n    items.forEach(item => {\r\n      const key = `${item.CLocation}_${item.CName}_${item.CPart}`;\r\n      if (map.has(key)) {\r\n        const existing = map.get(key)!;\r\n        existing.count += 1;\r\n      } else {\r\n        map.set(key, { item: { ...item }, count: 1 });\r\n      }\r\n    });\r\n\r\n    return Array.from(map.values()).map(({ item, count }) => ({\r\n      ...item,\r\n      CTotalAnswer: count\r\n    }));\r\n  }\r\n\r\n\r\n  validation() {\r\n    this.valid.clear();\r\n    let hasInvalidCUiType = false\r\n    let hasInvalidCRequireAnswer = false\r\n    let hasInvalidItemName = false;\r\n    for (const item of this.saveListFormItemReq) {\r\n      if (!hasInvalidCUiType && (!item.CUiType)) {\r\n        hasInvalidCUiType = true;\r\n      }\r\n      if (!hasInvalidCRequireAnswer && (!item.CRequireAnswer)) {\r\n        hasInvalidCRequireAnswer = true;\r\n      }\r\n\r\n      if (item.CTotalAnswer && item.CRequireAnswer) {\r\n        if (item.CRequireAnswer > item.CTotalAnswer) {\r\n          this.valid.addErrorMessage('[必填數量]' + ' <= ' + item.CTotalAnswer + ` (${item.CItemName}) `);\r\n        }\r\n      }\r\n      if (!hasInvalidItemName && (!item.CItemName)) {\r\n        hasInvalidItemName = true;\r\n      }\r\n    }\r\n    if (hasInvalidCUiType) {\r\n      this.valid.addErrorMessage('[前台UI類型]' + ' 必填');\r\n    }\r\n    if (hasInvalidCRequireAnswer) {\r\n      this.valid.addErrorMessage('[必填數量]' + ' 必填且>0');\r\n    }\r\n    if (hasInvalidItemName) {\r\n      this.valid.addErrorMessage('[廚房-廚具-櫃體]' + ' 必填');\r\n    }\r\n  }\r\n\r\n  saveListFormItemReq: SaveListFormItemReq[]\r\n\r\n  onSubmit() {\r\n    this.saveListFormItemReq = this.arrListFormItemReq.map((e: any) => {\r\n      return {\r\n        CDesignFileUrl: e.CDesignFileUrl ? e.CDesignFileUrl : null,\r\n        CFile: e.listPictures ? this.formatFile(e.listPictures) : undefined,\r\n        CFormItemHouseHold: this.getKeysWithTrueValue(e.selectedItems),\r\n        CFormItemId: e.CFormItemId ? e.CFormItemId : null,\r\n        CFormID: this.isNew ? null : this.listFormItem.CFormId,\r\n        CName: e.CName,\r\n        CPart: e.CPart,\r\n        CLocation: e.CLocation,\r\n        CItemName: e.CItemName,// ? e.CItemName : `${e.CName}-${e.CPart}-${e.CLocation}`,\r\n        CRemarkType: e.selectedCUiType.value !== 3 ? null : this.getCRemarkType(e.selectedCUiType, e.selectedRemarkType) || null,\r\n        CTotalAnswer: e.CTotalAnswer,\r\n        CRequireAnswer: e.CRequireAnswer,\r\n        CUiType: e.selectedCUiType.value,\r\n      }\r\n    })\r\n    this.validation()\r\n    if (this.valid.errorMessages.length > 0) {\r\n      this.message.showErrorMSGs(this.valid.errorMessages);\r\n      return\r\n    }\r\n    if (this.isNew) {\r\n      this.createListFormItem()\r\n\r\n    } else {\r\n      this.saveListFormItem()\r\n    }\r\n  }\r\n\r\n  saveListFormItem() {\r\n    this._formItemService.apiFormItemSaveListFormItemPost$Json({\r\n      body: this.saveListFormItemReq\r\n    }).subscribe(res => {\r\n      if (res.StatusCode == 0) {\r\n        this.message.showSucessMSG(\"執行成功\");\r\n        // this.getListFormItem()\r\n        this.goBack()\r\n      }\r\n    })\r\n  }\r\n\r\n  creatListFormItem: CreateListFormItem\r\n\r\n  createListFormItem() {\r\n    this.creatListFormItem = {\r\n      CBuildCaseId: this.buildCaseId,\r\n      CFormItem: this.saveListFormItemReq || null,\r\n      CFormType: this.typeContentManagementLandowner.CFormType,\r\n    }\r\n    this._formItemService.apiFormItemCreateListFormItemPost$Json({\r\n      body: this.creatListFormItem\r\n    }).subscribe(res => {\r\n      if (res.StatusCode == 0) {\r\n        this.message.showSucessMSG(\"執行成功\");\r\n        // this.getListFormItem()\r\n        this.goBack()\r\n      }\r\n    })\r\n  }\r\n\r\n  createArrayObjectFromArray(a: string[], b: { CFormItemHouseholdId: number, CFormItemId: number, CHousehold: string, CIsSelect: boolean }[]): { [key: string]: boolean } {\r\n    const c: { [key: string]: boolean } = {};\r\n    for (const item of a) {\r\n      const matchingItem = b.find(bItem => bItem.CHousehold === item && bItem.CIsSelect);\r\n      c[item] = !!matchingItem;\r\n    }\r\n    return c;\r\n  } //[\"House1\", \"House2\", \"House3\"] => [{CHousehold: \"House1\", CIsSelect: true,... }, ... ]\r\n\r\n\r\n  houseHoldList: any[]\r\n\r\n  getListRegularNoticeFileHouseHold() {\r\n    this._regularNoticeFileService.apiRegularNoticeFileGetListRegularNoticeFileHouseHoldPost$Json({\r\n      body: this.buildCaseId\r\n    }).pipe(\r\n      tap(res => {\r\n        if (res.Entries && res.StatusCode == 0) {\r\n          this.houseHoldList = this.getHouseHoldListByNoticeType(res.Entries)\r\n          this.getListFormItem()\r\n        }\r\n      })\r\n    ).subscribe()\r\n  }\r\n  goBack() { this.location.back() }\r\n}\r\n"], "mappings": ";AAAA,SAASA,SAAS,QAAgB,eAAe;AACjD,SAASC,YAAY,QAAQ,mCAAmC;AAChE,SAASC,YAAY,QAAkB,iBAAiB;AACxD,SAASC,gBAAgB,QAAQ,gBAAgB;AACjD,SAASC,aAAa,QAAQ,wCAAwC;AAMtE,SAASC,GAAG,QAAQ,MAAM;AAE1B,SAASC,YAAY,QAAQ,qCAAqC;AAkC3D,IAAMC,yCAAyC,GAA/C,MAAMA,yCAA0C,SAAQH,aAAa;EAC1EI,YACUC,MAAmB,EACnBC,KAAqB,EACrBC,OAAuB,EACvBC,gBAAiC,EACjCC,yBAAmD,EACnDC,eAA+B,EAC/BC,KAAuB,EACvBC,QAAkB,EAClBC,gBAAiC;IAEzC,KAAK,CAACR,MAAM,CAAC;IAVL,KAAAA,MAAM,GAANA,MAAM;IACN,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,yBAAyB,GAAzBA,yBAAyB;IACzB,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,QAAQ,GAARA,QAAQ;IACR,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAK1B,KAAAC,8BAA8B,GAAG;MAC/BC,SAAS,EAAE,CAAC;MACZC,WAAW,EAAE;KACd;IACD,KAAAC,cAAc,GAAU,CACtB;MACEC,KAAK,EAAE,CAAC;MAAEC,KAAK,EAAE;KAClB,EACD;MACED,KAAK,EAAE,CAAC;MAAEC,KAAK,EAAE;KAClB,EAAE;MACDD,KAAK,EAAE,CAAC;MAAEC,KAAK,EAAE;KAClB,CACF;IACD,KAAAC,kBAAkB,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC;IA2BjC,KAAAC,aAAa,GAA+B,EAAE;IAC9C,KAAAC,kBAAkB,GAA+B,EAAE;IA2HnD,KAAAC,KAAK,GAAY,IAAI;EAvKrB;EAmBSC,QAAQA,CAAA;IACf,IAAI,CAAClB,KAAK,CAACmB,QAAQ,CAACC,SAAS,CAACC,MAAM,IAAG;MACrC,IAAIA,MAAM,EAAE;QACV,MAAMC,OAAO,GAAGD,MAAM,CAACE,GAAG,CAAC,IAAI,CAAC;QAChC,MAAMC,EAAE,GAAGF,OAAO,GAAG,CAACA,OAAO,GAAG,CAAC;QACjC,IAAI,CAACG,WAAW,GAAGD,EAAE;QACrB,IAAI,IAAI,CAACC,WAAW,EAAE;UACpB,IAAI,CAACC,iCAAiC,EAAE;QAC1C;MACF;IACF,CAAC,CAAC;EACJ;EAGAC,cAAcA,CAACf,KAAU,EAAEgB,OAAc;IACvC,KAAK,MAAMC,IAAI,IAAID,OAAO,EAAE;MAC1B,IAAIC,IAAI,CAACjB,KAAK,KAAKA,KAAK,EAAE;QACxB,OAAOiB,IAAI;MACb;IACF;IACA,OAAO,IAAI;EACb;EAMAC,gBAAgBA,CAACC,OAAgB,EAAEC,YAAiB;IAClDA,YAAY,CAACC,WAAW,GAAGF,OAAO;IAClC,IAAI,CAACG,aAAa,CAACC,OAAO,CAACN,IAAI,IAAG;MAChCG,YAAY,CAACjB,aAAa,CAACc,IAAI,CAAC,GAAGE,OAAO;IAC5C,CAAC,CAAC;EACJ;EAGAK,WAAWA,CAACC,KAAU,EAAEL,YAAiB;IACvC,MAAMM,IAAI,GAAGD,KAAK,CAACE,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC;IAClC,IAAIF,IAAI,EAAE;MACR,IAAIG,MAAM,GAAG,IAAIC,UAAU,EAAE;MAC7BD,MAAM,CAACE,aAAa,CAACL,IAAI,CAAC;MAC1BG,MAAM,CAACG,MAAM,GAAG,MAAK;QACnB,IAAIC,SAAS,GAAWJ,MAAM,CAACK,MAAgB;QAC/C,IAAI,CAACD,SAAS,EAAE;UACd;QACF;QACA,IAAIb,YAAY,CAACe,YAAY,CAACC,MAAM,GAAG,CAAC,EAAE;UACxChB,YAAY,CAACe,YAAY,CAAC,CAAC,CAAC,GAAG;YAC7BvB,EAAE,EAAE,IAAIyB,IAAI,EAAE,CAACC,OAAO,EAAE;YACxBC,IAAI,EAAEb,IAAI,CAACa,IAAI,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;YAC7BC,IAAI,EAAER,SAAS;YACfS,SAAS,EAAE,IAAI,CAAClD,eAAe,CAACmD,gBAAgB,CAACjB,IAAI,CAACa,IAAI,CAAC;YAC3DK,KAAK,EAAElB;WACR;QACH,CAAC,MAAM;UACLN,YAAY,CAACe,YAAY,CAACU,IAAI,CAAC;YAC7BjC,EAAE,EAAE,IAAIyB,IAAI,EAAE,CAACC,OAAO,EAAE;YACxBC,IAAI,EAAEb,IAAI,CAACa,IAAI,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;YAC7BC,IAAI,EAAER,SAAS;YACfS,SAAS,EAAE,IAAI,CAAClD,eAAe,CAACmD,gBAAgB,CAACjB,IAAI,CAACa,IAAI,CAAC;YAC3DK,KAAK,EAAElB;WACR,CAAC;QACJ;QACAD,KAAK,CAACE,MAAM,CAAC3B,KAAK,GAAG,IAAI;MAC3B,CAAC;IACH;EACF;EAEA8C,WAAWA,CAACC,SAAiB,EAAE3B,YAAiB;IAC9C,IAAIA,YAAY,CAACe,YAAY,CAACC,MAAM,EAAE;MACpChB,YAAY,CAACe,YAAY,GAAGf,YAAY,CAACe,YAAY,CAACa,MAAM,CAAEC,CAAM,IAAKA,CAAC,CAACrC,EAAE,IAAImC,SAAS,CAAC;IAC7F;EACF;EAEAG,UAAUA,CAACzB,KAAU,EAAE0B,KAAa,EAAE/B,YAAiB;IACrD,IAAIgC,IAAI,GAAGhC,YAAY,CAACe,YAAY,CAACgB,KAAK,CAAC,CAACP,KAAK,CAACS,KAAK,CAAC,CAAC,EAAEjC,YAAY,CAACe,YAAY,CAACgB,KAAK,CAAC,CAACP,KAAK,CAACU,IAAI,EAAElC,YAAY,CAACe,YAAY,CAACgB,KAAK,CAAC,CAACP,KAAK,CAACW,IAAI,CAAC;IACpJ,IAAIC,OAAO,GAAG,IAAIC,IAAI,CAAC,CAACL,IAAI,CAAC,EAAE,GAAG3B,KAAK,CAACE,MAAM,CAAC3B,KAAK,GAAG,GAAG,GAAGoB,YAAY,CAACe,YAAY,CAACgB,KAAK,CAAC,CAACT,SAAS,EAAE,EAAE;MAAEa,IAAI,EAAEnC,YAAY,CAACe,YAAY,CAACgB,KAAK,CAAC,CAACP,KAAK,CAACW;IAAI,CAAE,CAAC;IACjKnC,YAAY,CAACe,YAAY,CAACgB,KAAK,CAAC,CAACP,KAAK,GAAGY,OAAO;EAClD;EAEAE,6BAA6BA,CAACvC,OAAgB,EAAEF,IAAY,EAAEG,YAAiB;IAC7E,IAAID,OAAO,EAAE;MACXC,YAAY,CAACjB,aAAa,CAACc,IAAI,CAAC,GAAGE,OAAO;MAC1CC,YAAY,CAACC,WAAW,GAAG,IAAI,CAACC,aAAa,CAACqC,KAAK,CAAC1C,IAAI,IAAIG,YAAY,CAACjB,aAAa,CAACc,IAAI,CAAC,IAAIE,OAAO,CAAC;IAC1G,CAAC,MAAM;MACLC,YAAY,CAACC,WAAW,GAAG,KAAK;IAClC;EACF;EAIAuC,sBAAsBA,CAACzC,OAAgB,EAAEF,IAAY,EAAEG,YAAiB;IACtEA,YAAY,CAAChB,kBAAkB,CAACa,IAAI,CAAC,GAAGE,OAAO;EACjD;EAEA0C,kBAAkBA,CAAC3D,kBAA4B,EAAE4D,WAAmB;IAClE,MAAMC,YAAY,GAA+B,EAAE;IACnD,KAAK,MAAMC,MAAM,IAAI9D,kBAAkB,EAAE;MACvC6D,YAAY,CAACC,MAAM,CAAC,GAAG,KAAK;IAC9B;IACA,MAAMC,WAAW,GAAGH,WAAW,CAACtB,KAAK,CAAC,GAAG,CAAC;IAC1C,KAAK,MAAMe,IAAI,IAAIU,WAAW,EAAE;MAC9B,IAAI/D,kBAAkB,CAACgE,QAAQ,CAACX,IAAI,CAAC,EAAE;QACrCQ,YAAY,CAACR,IAAI,CAAC,GAAG,IAAI;MAC3B;IACF;IACA,OAAOQ,YAAY;EACrB;EAEAI,eAAeA,CAAA;IACb,IAAI,CAACxE,gBAAgB,CAACyE,mCAAmC,CAAC;MACxDC,IAAI,EAAE;QACJC,YAAY,EAAE,IAAI,CAACzD,WAAW;QAC9B0D,KAAK,EAAE;;KAEV,CAAC,CAACC,IAAI,CACLzF,GAAG,CAAC0F,GAAG,IAAG;MACR,IAAIA,GAAG,CAACC,OAAO,IAAID,GAAG,CAACE,UAAU,IAAI,CAAC,EAAE;QAEtC,IAAI,CAACrD,aAAa,CAACC,OAAO,CAACN,IAAI,IAAI,IAAI,CAACd,aAAa,CAACc,IAAI,CAAC,GAAG,KAAK,CAAC;QAEpE,IAAI,CAACf,kBAAkB,CAACqB,OAAO,CAACN,IAAI,IAAI,IAAI,CAACb,kBAAkB,CAACa,IAAI,CAAC,GAAG,KAAK,CAAC;QAC9E,IAAI,CAAC2D,kBAAkB,GAAGH,GAAG,CAACC,OAAO,CAACG,GAAG,CAAEC,CAAM,IAAI;UACnD,OAAO;YACLC,cAAc,EAAE,IAAI;YACpBC,kBAAkB,EAAE,IAAI;YACxBC,OAAO,EAAE,IAAI;YACbC,SAAS,EAAEJ,CAAC,CAACI,SAAS;YACtBC,KAAK,EAAEL,CAAC,CAACK,KAAK;YACdC,KAAK,EAAEN,CAAC,CAACM,KAAK;YACdC,SAAS,EAAE,GAAGP,CAAC,CAACK,KAAK,IAAIL,CAAC,CAACM,KAAK,IAAIN,CAAC,CAACI,SAAS,EAAE;YACjDpB,WAAW,EAAE,IAAI;YACjBwB,YAAY,EAAE,CAAC;YACfC,cAAc,EAAEC,SAAS;YACzBC,OAAO,EAAE,CAAC;YACVtF,aAAa,EAAE,EAAE;YACjBC,kBAAkB,EAAE,IAAI,CAACA,kBAAkB;YAC3CiB,WAAW,EAAE,KAAK;YAClBc,YAAY,EAAE,EAAE;YAChBuD,eAAe,EAAE,IAAI,CAAC3F,cAAc,CAAC,CAAC;WACvC;QACH,CAAC,CAAC;QACF,IAAI,CAAC6E,kBAAkB,GAAG,CAAC,GAAG,IAAI,CAACe,UAAU,CAAC,IAAI,CAACf,kBAAkB,CAAC,CAAC;MACzE;IACF,CAAC,CAAC,CACH,CAACpE,SAAS,EAAE;EACf;EAKAoF,eAAeA,CAAA;IACb,IAAI,CAACtG,gBAAgB,CAACuG,mCAAmC,CAAC;MACxDxB,IAAI,EAAE;QACJC,YAAY,EAAE,IAAI,CAACzD,WAAW;QAC9BhB,SAAS,EAAE,IAAI,CAACD,8BAA8B,CAACC,SAAS;QACxDiG,SAAS,EAAE;;KAEd,CAAC,CAACtB,IAAI,CACLzF,GAAG,CAAC0F,GAAG,IAAG;MACR,IAAIA,GAAG,CAACC,OAAO,IAAID,GAAG,CAACE,UAAU,IAAI,CAAC,EAAE;QACtC,IAAI,CAACoB,YAAY,GAAGtB,GAAG,CAACC,OAAO;QAC/B,IAAI,CAACrE,KAAK,GAAGoE,GAAG,CAACC,OAAO,CAACsB,SAAS,GAAG,KAAK,GAAG,IAAI;QACjD,IAAIvB,GAAG,CAACC,OAAO,CAACsB,SAAS,EAAE;UAEzB,IAAI,CAAC1E,aAAa,CAACC,OAAO,CAACN,IAAI,IAAI,IAAI,CAACd,aAAa,CAACc,IAAI,CAAC,GAAG,KAAK,CAAC;UACpE,IAAI,CAACf,kBAAkB,CAACqB,OAAO,CAACN,IAAI,IAAI,IAAI,CAACb,kBAAkB,CAACa,IAAI,CAAC,GAAG,KAAK,CAAC;UAE9E,IAAI,CAAC2D,kBAAkB,GAAGH,GAAG,CAACC,OAAO,CAACsB,SAAS,CAACnB,GAAG,CAAEC,CAAM,IAAI;YAC7D,OAAO;cACLG,OAAO,EAAE,IAAI,CAACc,YAAY,CAACd,OAAO;cAClCF,cAAc,EAAED,CAAC,CAACC,cAAc;cAChCnC,KAAK,EAAEkC,CAAC,CAAClC,KAAK;cACdoC,kBAAkB,EAAEF,CAAC,CAACE,kBAAkB;cACxCiB,WAAW,EAAEnB,CAAC,CAACmB,WAAW;cAC1Bf,SAAS,EAAEJ,CAAC,CAACI,SAAS;cACtBC,KAAK,EAAEL,CAAC,CAACK,KAAK;cACdC,KAAK,EAAEN,CAAC,CAACM,KAAK;cACdC,SAAS,EAAEP,CAAC,CAACO,SAAS,GAAGP,CAAC,CAACO,SAAS,GAAG,GAAGP,CAAC,CAACK,KAAK,IAAIL,CAAC,CAACM,KAAK,IAAIN,CAAC,CAACI,SAAS,EAAE;cAC7EpB,WAAW,EAAEgB,CAAC,CAAChB,WAAW;cAC1BwB,YAAY,EAAER,CAAC,CAACQ,YAAY;cAC5BC,cAAc,EAAET,CAAC,CAACW,OAAO,KAAG,CAAC,GAAG,CAAC,GAAEX,CAAC,CAACS,cAAc;cACnDE,OAAO,EAAEX,CAAC,CAACW,OAAO;cAClBtF,aAAa,EAAE2E,CAAC,CAACoB,qBAAqB,CAAC9D,MAAM,GAAG,IAAI,CAAC+D,0BAA0B,CAAC,IAAI,CAAC7E,aAAa,EAAEwD,CAAC,CAACoB,qBAAqB,CAAC,GAAG;gBAAE,GAAG,IAAI,CAAC/F;cAAa,CAAE;cACxJC,kBAAkB,EAAE0E,CAAC,CAAChB,WAAW,GAAG,IAAI,CAACD,kBAAkB,CAAC,IAAI,CAAC3D,kBAAkB,EAAE4E,CAAC,CAAChB,WAAW,CAAC,GAAG;gBAAE,GAAG,IAAI,CAAC1D;cAAkB,CAAE;cACpIiB,WAAW,EAAEyD,CAAC,CAACoB,qBAAqB,CAAC9D,MAAM,IAAI,IAAI,CAACd,aAAa,CAACc,MAAM;cACxED,YAAY,EAAE,EAAE;cAChBuD,eAAe,EAAEZ,CAAC,CAACW,OAAO,GAAG,IAAI,CAAC1E,cAAc,CAAC+D,CAAC,CAACW,OAAO,EAAE,IAAI,CAAC1F,cAAc,CAAC,GAAG,IAAI,CAACA,cAAc,CAAC,CAAC;aACzG;UACH,CAAC,CAAC;QACJ,CAAC,MAAM;UACL,IAAI,CAACoE,eAAe,EAAE;QACxB;MACF;IACF,CAAC,CAAC,CACH,CAAC3D,SAAS,EAAE;EACf;EAEA4F,mBAAmBA,CAACC,WAAgB;IAClC,IAAGA,WAAW,CAACX,eAAe,IAAIW,WAAW,CAACX,eAAe,CAAC1F,KAAK,KAAI,CAAC,EAAE;MACxEqG,WAAW,CAACd,cAAc,GAAG,CAAC;IAChC;EACF;EAEAe,4BAA4BA,CAAC7D,IAAW;IACtC,KAAK,IAAIxB,IAAI,IAAIwB,IAAI,EAAE;MACrB,IAAIxB,IAAI,CAACnB,WAAW,KAAK,IAAI,CAACF,8BAA8B,CAACE,WAAW,EAAE;QACxE,OAAOmB,IAAI,CAACsF,cAAc;MAC5B;IACF;IACA,OAAO,EAAE;EACX;EAIAC,oBAAoBA,CAACC,GAA4B;IAC/C,OAAOC,MAAM,CAACC,IAAI,CAACF,GAAG,CAAC,CAACzD,MAAM,CAAC4D,GAAG,IAAIH,GAAG,CAACG,GAAG,CAAC,CAAC;EACjD;EAEAC,0BAA0BA,CAACJ,GAA4B;IACrD,OAAOC,MAAM,CAACC,IAAI,CAACF,GAAG,CAAC,CACpBzD,MAAM,CAAC4D,GAAG,IAAIH,GAAG,CAACG,GAAG,CAAC,CAAC,CACvBE,IAAI,CAAC,GAAG,CAAC;EACd;EAEAC,cAAcA,CAACrB,eAAoB,EAAEtF,kBAAuB;IAC1D,IAAIsF,eAAe,IAAIA,eAAe,CAAC1F,KAAK,IAAI,CAAC,EAAE;MACjD,OAAO,IAAI,CAAC6G,0BAA0B,CAACzG,kBAAkB,CAAC;IAC5D;EACF;EAEA4G,mBAAmBA,CAACC,WAAmB;IACrC,MAAMC,KAAK,GAAGD,WAAW,CAACzE,KAAK,CAAC,GAAG,CAAC;IACpC,IAAI0E,KAAK,CAAC9E,MAAM,GAAG,CAAC,EAAE;MACpB,OAAO8E,KAAK,CAAC,CAAC,CAAC;IACjB,CAAC,MAAM,OAAO,EAAE;EAClB;EAEAC,UAAUA,CAAChF,YAAiB;IAC1B,IAAIA,YAAY,IAAIA,YAAY,CAACC,MAAM,GAAG,CAAC,EAAE;MAC3C,OAAO;QACLgF,YAAY,EAAE,IAAI,CAACJ,mBAAmB,CAAC7E,YAAY,CAAC,CAAC,CAAC,CAACM,IAAI,CAAC,IAAI,IAAI;QACpE4E,aAAa,EAAElF,YAAY,CAAC,CAAC,CAAC,CAACO,SAAS,IAAI,IAAI;QAChD4E,QAAQ,EAAEnF,YAAY,CAAC,CAAC,CAAC,CAACS,KAAK,CAACL,IAAI,IAAIJ,YAAY,CAAC,CAAC,CAAC,CAACI,IAAI,IAAI;OACjE;IACH,CAAC,MAAM,OAAOiD,SAAS;EAEzB;EAEA;EACA;EACA;EACA;EACA;EACAG,UAAUA,CAAC4B,KAAoC;IAC7C,MAAM1C,GAAG,GAAG,IAAI2C,GAAG,EAAgE;IAEnFD,KAAK,CAAChG,OAAO,CAACN,IAAI,IAAG;MACnB,MAAM2F,GAAG,GAAG,GAAG3F,IAAI,CAACiE,SAAS,IAAIjE,IAAI,CAACkE,KAAK,IAAIlE,IAAI,CAACmE,KAAK,EAAE;MAC3D,IAAIP,GAAG,CAAC4C,GAAG,CAACb,GAAG,CAAC,EAAE;QAChB,MAAMc,QAAQ,GAAG7C,GAAG,CAAClE,GAAG,CAACiG,GAAG,CAAE;QAC9Bc,QAAQ,CAACC,KAAK,IAAI,CAAC;MACrB,CAAC,MAAM;QACL9C,GAAG,CAAC+C,GAAG,CAAChB,GAAG,EAAE;UAAE3F,IAAI,EAAE;YAAE,GAAGA;UAAI,CAAE;UAAE0G,KAAK,EAAE;QAAC,CAAE,CAAC;MAC/C;IACF,CAAC,CAAC;IAEF,OAAOE,KAAK,CAACC,IAAI,CAACjD,GAAG,CAACkD,MAAM,EAAE,CAAC,CAAClD,GAAG,CAAC,CAAC;MAAE5D,IAAI;MAAE0G;IAAK,CAAE,MAAM;MACxD,GAAG1G,IAAI;MACPqE,YAAY,EAAEqC;KACf,CAAC,CAAC;EACL;EAGAK,UAAUA,CAAA;IACR,IAAI,CAACvI,KAAK,CAACwI,KAAK,EAAE;IAClB,IAAIC,iBAAiB,GAAG,KAAK;IAC7B,IAAIC,wBAAwB,GAAG,KAAK;IACpC,IAAIC,kBAAkB,GAAG,KAAK;IAC9B,KAAK,MAAMnH,IAAI,IAAI,IAAI,CAACoH,mBAAmB,EAAE;MAC3C,IAAI,CAACH,iBAAiB,IAAK,CAACjH,IAAI,CAACwE,OAAQ,EAAE;QACzCyC,iBAAiB,GAAG,IAAI;MAC1B;MACA,IAAI,CAACC,wBAAwB,IAAK,CAAClH,IAAI,CAACsE,cAAe,EAAE;QACvD4C,wBAAwB,GAAG,IAAI;MACjC;MAEA,IAAIlH,IAAI,CAACqE,YAAY,IAAIrE,IAAI,CAACsE,cAAc,EAAE;QAC5C,IAAItE,IAAI,CAACsE,cAAc,GAAGtE,IAAI,CAACqE,YAAY,EAAE;UAC3C,IAAI,CAAC7F,KAAK,CAAC6I,eAAe,CAAC,QAAQ,GAAG,MAAM,GAAGrH,IAAI,CAACqE,YAAY,GAAG,KAAKrE,IAAI,CAACoE,SAAS,IAAI,CAAC;QAC7F;MACF;MACA,IAAI,CAAC+C,kBAAkB,IAAK,CAACnH,IAAI,CAACoE,SAAU,EAAE;QAC5C+C,kBAAkB,GAAG,IAAI;MAC3B;IACF;IACA,IAAIF,iBAAiB,EAAE;MACrB,IAAI,CAACzI,KAAK,CAAC6I,eAAe,CAAC,UAAU,GAAG,KAAK,CAAC;IAChD;IACA,IAAIH,wBAAwB,EAAE;MAC5B,IAAI,CAAC1I,KAAK,CAAC6I,eAAe,CAAC,QAAQ,GAAG,QAAQ,CAAC;IACjD;IACA,IAAIF,kBAAkB,EAAE;MACtB,IAAI,CAAC3I,KAAK,CAAC6I,eAAe,CAAC,YAAY,GAAG,KAAK,CAAC;IAClD;EACF;EAIAC,QAAQA,CAAA;IACN,IAAI,CAACF,mBAAmB,GAAG,IAAI,CAACzD,kBAAkB,CAACC,GAAG,CAAE2D,CAAM,IAAI;MAChE,OAAO;QACLzD,cAAc,EAAEyD,CAAC,CAACzD,cAAc,GAAGyD,CAAC,CAACzD,cAAc,GAAG,IAAI;QAC1DnC,KAAK,EAAE4F,CAAC,CAACrG,YAAY,GAAG,IAAI,CAACgF,UAAU,CAACqB,CAAC,CAACrG,YAAY,CAAC,GAAGqD,SAAS;QACnER,kBAAkB,EAAE,IAAI,CAACwB,oBAAoB,CAACgC,CAAC,CAACrI,aAAa,CAAC;QAC9D8F,WAAW,EAAEuC,CAAC,CAACvC,WAAW,GAAGuC,CAAC,CAACvC,WAAW,GAAG,IAAI;QACjDwC,OAAO,EAAE,IAAI,CAACpI,KAAK,GAAG,IAAI,GAAG,IAAI,CAAC0F,YAAY,CAACd,OAAO;QACtDE,KAAK,EAAEqD,CAAC,CAACrD,KAAK;QACdC,KAAK,EAAEoD,CAAC,CAACpD,KAAK;QACdF,SAAS,EAAEsD,CAAC,CAACtD,SAAS;QACtBG,SAAS,EAAEmD,CAAC,CAACnD,SAAS;QAAC;QACvBvB,WAAW,EAAE0E,CAAC,CAAC9C,eAAe,CAAC1F,KAAK,KAAK,CAAC,GAAG,IAAI,GAAG,IAAI,CAAC+G,cAAc,CAACyB,CAAC,CAAC9C,eAAe,EAAE8C,CAAC,CAACpI,kBAAkB,CAAC,IAAI,IAAI;QACxHkF,YAAY,EAAEkD,CAAC,CAAClD,YAAY;QAC5BC,cAAc,EAAEiD,CAAC,CAACjD,cAAc;QAChCE,OAAO,EAAE+C,CAAC,CAAC9C,eAAe,CAAC1F;OAC5B;IACH,CAAC,CAAC;IACF,IAAI,CAACgI,UAAU,EAAE;IACjB,IAAI,IAAI,CAACvI,KAAK,CAACiJ,aAAa,CAACtG,MAAM,GAAG,CAAC,EAAE;MACvC,IAAI,CAAC/C,OAAO,CAACsJ,aAAa,CAAC,IAAI,CAAClJ,KAAK,CAACiJ,aAAa,CAAC;MACpD;IACF;IACA,IAAI,IAAI,CAACrI,KAAK,EAAE;MACd,IAAI,CAACuI,kBAAkB,EAAE;IAE3B,CAAC,MAAM;MACL,IAAI,CAACC,gBAAgB,EAAE;IACzB;EACF;EAEAA,gBAAgBA,CAAA;IACd,IAAI,CAACvJ,gBAAgB,CAACwJ,oCAAoC,CAAC;MACzDzE,IAAI,EAAE,IAAI,CAACgE;KACZ,CAAC,CAAC7H,SAAS,CAACiE,GAAG,IAAG;MACjB,IAAIA,GAAG,CAACE,UAAU,IAAI,CAAC,EAAE;QACvB,IAAI,CAACtF,OAAO,CAAC0J,aAAa,CAAC,MAAM,CAAC;QAClC;QACA,IAAI,CAACC,MAAM,EAAE;MACf;IACF,CAAC,CAAC;EACJ;EAIAJ,kBAAkBA,CAAA;IAChB,IAAI,CAACK,iBAAiB,GAAG;MACvB3E,YAAY,EAAE,IAAI,CAACzD,WAAW;MAC9BqI,SAAS,EAAE,IAAI,CAACb,mBAAmB,IAAI,IAAI;MAC3CxI,SAAS,EAAE,IAAI,CAACD,8BAA8B,CAACC;KAChD;IACD,IAAI,CAACP,gBAAgB,CAAC6J,sCAAsC,CAAC;MAC3D9E,IAAI,EAAE,IAAI,CAAC4E;KACZ,CAAC,CAACzI,SAAS,CAACiE,GAAG,IAAG;MACjB,IAAIA,GAAG,CAACE,UAAU,IAAI,CAAC,EAAE;QACvB,IAAI,CAACtF,OAAO,CAAC0J,aAAa,CAAC,MAAM,CAAC;QAClC;QACA,IAAI,CAACC,MAAM,EAAE;MACf;IACF,CAAC,CAAC;EACJ;EAEA7C,0BAA0BA,CAACiD,CAAW,EAAEC,CAAkG;IACxI,MAAMC,CAAC,GAA+B,EAAE;IACxC,KAAK,MAAMrI,IAAI,IAAImI,CAAC,EAAE;MACpB,MAAMG,YAAY,GAAGF,CAAC,CAACG,IAAI,CAACC,KAAK,IAAIA,KAAK,CAACC,UAAU,KAAKzI,IAAI,IAAIwI,KAAK,CAACE,SAAS,CAAC;MAClFL,CAAC,CAACrI,IAAI,CAAC,GAAG,CAAC,CAACsI,YAAY;IAC1B;IACA,OAAOD,CAAC;EACV,CAAC,CAAC;EAKFxI,iCAAiCA,CAAA;IAC/B,IAAI,CAACvB,yBAAyB,CAACqK,8DAA8D,CAAC;MAC5FvF,IAAI,EAAE,IAAI,CAACxD;KACZ,CAAC,CAAC2D,IAAI,CACLzF,GAAG,CAAC0F,GAAG,IAAG;MACR,IAAIA,GAAG,CAACC,OAAO,IAAID,GAAG,CAACE,UAAU,IAAI,CAAC,EAAE;QACtC,IAAI,CAACrD,aAAa,GAAG,IAAI,CAACgF,4BAA4B,CAAC7B,GAAG,CAACC,OAAO,CAAC;QACnE,IAAI,CAACkB,eAAe,EAAE;MACxB;IACF,CAAC,CAAC,CACH,CAACpF,SAAS,EAAE;EACf;EACAwI,MAAMA,CAAA;IAAK,IAAI,CAACtJ,QAAQ,CAACmK,IAAI,EAAE;EAAC;CACjC;AA3aY5K,yCAAyC,GAAA6K,UAAA,EATrDpL,SAAS,CAAC;EACTqL,QAAQ,EAAE,yCAAyC;EACnDC,WAAW,EAAE,sDAAsD;EACnEC,SAAS,EAAE,CAAC,sDAAsD,CAAC;EACnEC,UAAU,EAAE,IAAI;EAChBC,OAAO,EAAE,CAACvL,YAAY,EAAED,YAAY,EAAEE,gBAAgB,EAAEG,YAAY;CAErE,CAAC,C,EAEWC,yCAAyC,CA2arD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}