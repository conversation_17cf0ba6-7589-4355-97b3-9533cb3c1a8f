import { Pipe, PipeTransform } from '@angular/core';

@Pipe({
    name: 'specialChangeSource',
    standalone: true
})
export class SpecialChangeSourcePipe implements PipeTransform {
    transform(value: number): string {
        switch (value) {
            case 1:
                return '後台';
            case 2:
                return '前台';
            default:
                return '';
        }
    }
}
