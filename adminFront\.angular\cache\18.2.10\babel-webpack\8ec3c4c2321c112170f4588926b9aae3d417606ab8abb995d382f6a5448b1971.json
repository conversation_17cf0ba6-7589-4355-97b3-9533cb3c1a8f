{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { NbCardModule, NbButtonModule } from '@nebular/theme';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nimport * as i2 from \"@angular/forms\";\nimport * as i3 from \"@nebular/theme\";\nfunction TemplateViewerComponent_button_15_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 11);\n    i0.ɵɵlistener(\"click\", function TemplateViewerComponent_button_15_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.clearSearch());\n    });\n    i0.ɵɵelement(1, \"i\", 31);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction TemplateViewerComponent_div_16_div_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 50);\n    i0.ɵɵelement(1, \"i\", 51);\n    i0.ɵɵelementStart(2, \"span\");\n    i0.ɵɵtext(3, \"\\u66AB\\u7121\\u53EF\\u9078\\u9805\\u76EE\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction TemplateViewerComponent_div_16_div_26_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 52)(1, \"label\", 53)(2, \"input\", 54);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function TemplateViewerComponent_div_16_div_26_Template_input_ngModelChange_2_listener($event) {\n      const item_r5 = i0.ɵɵrestoreView(_r4).$implicit;\n      i0.ɵɵtwoWayBindingSet(item_r5.selected, $event) || (item_r5.selected = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 55)(4, \"div\", 56);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 57);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const item_r5 = ctx.$implicit;\n    const i_r6 = ctx.index;\n    i0.ɵɵadvance(2);\n    i0.ɵɵpropertyInterpolate1(\"name\", \"item_\", i_r6, \"\");\n    i0.ɵɵtwoWayProperty(\"ngModel\", item_r5.selected);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(item_r5.CRequirement || item_r5.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r5.CGroupName || item_r5.description);\n  }\n}\nfunction TemplateViewerComponent_div_16_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 32)(1, \"div\", 33)(2, \"div\", 34)(3, \"div\", 35);\n    i0.ɵɵelement(4, \"i\", 36);\n    i0.ɵɵelementStart(5, \"span\");\n    i0.ɵɵtext(6, \"\\u65B0\\u589E\\u6A21\\u677F\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(7, \"form\", 37);\n    i0.ɵɵlistener(\"ngSubmit\", function TemplateViewerComponent_div_16_Template_form_ngSubmit_7_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.saveNewTemplate());\n    });\n    i0.ɵɵelementStart(8, \"div\", 38)(9, \"div\", 8)(10, \"label\", 39);\n    i0.ɵɵtext(11, \" \\u6A21\\u677F\\u540D\\u7A31 \");\n    i0.ɵɵelementStart(12, \"span\", 40);\n    i0.ɵɵtext(13, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(14, \"input\", 41);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function TemplateViewerComponent_div_16_Template_input_ngModelChange_14_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r1.newTemplate.name, $event) || (ctx_r1.newTemplate.name = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(15, \"div\", 8)(16, \"label\", 39);\n    i0.ɵɵtext(17, \"\\u6A21\\u677F\\u63CF\\u8FF0\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"input\", 42);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function TemplateViewerComponent_div_16_Template_input_ngModelChange_18_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r1.newTemplate.description, $event) || (ctx_r1.newTemplate.description = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(19, \"div\", 43)(20, \"label\", 39);\n    i0.ɵɵtext(21, \" \\u9078\\u64C7\\u8981\\u52A0\\u5165\\u6A21\\u677F\\u7684\\u9805\\u76EE \");\n    i0.ɵɵelementStart(22, \"span\", 40);\n    i0.ɵɵtext(23, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(24, \"div\", 44);\n    i0.ɵɵtemplate(25, TemplateViewerComponent_div_16_div_25_Template, 4, 0, \"div\", 45)(26, TemplateViewerComponent_div_16_div_26_Template, 8, 5, \"div\", 46);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(27, \"div\", 47)(28, \"button\", 48);\n    i0.ɵɵlistener(\"click\", function TemplateViewerComponent_div_16_Template_button_click_28_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.cancelAddTemplate());\n    });\n    i0.ɵɵtext(29, \" \\u53D6\\u6D88 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(30, \"button\", 49);\n    i0.ɵɵtext(31, \" \\u5132\\u5B58\\u6A21\\u677F \");\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(14);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.newTemplate.name);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.newTemplate.description);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.availableData.length === 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.availableData);\n  }\n}\nfunction TemplateViewerComponent_div_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 58)(1, \"small\", 59);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\" \\u627E\\u5230 \", ctx_r1.filteredTemplates.length, \" \\u500B\\u7B26\\u5408\\u300C\", ctx_r1.searchKeyword, \"\\u300D\\u7684\\u6A21\\u677F \");\n  }\n}\nfunction TemplateViewerComponent_div_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 60)(1, \"small\", 59);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate3(\" \\u986F\\u793A\\u7B2C \", (ctx_r1.templatePagination.currentPage - 1) * ctx_r1.templatePagination.pageSize + 1, \" - \", ctx_r1.Math.min(ctx_r1.templatePagination.currentPage * ctx_r1.templatePagination.pageSize, ctx_r1.templatePagination.totalItems), \" \\u9805\\uFF0C \\u5171 \", ctx_r1.templatePagination.totalItems, \" \\u9805\\u6A21\\u677F \");\n  }\n}\nfunction TemplateViewerComponent_tr_31_button_12_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 65);\n    i0.ɵɵlistener(\"click\", function TemplateViewerComponent_tr_31_button_12_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const tpl_r8 = i0.ɵɵnextContext().$implicit;\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(tpl_r8.TemplateID && ctx_r1.onDeleteTemplate(tpl_r8.TemplateID));\n    });\n    i0.ɵɵelement(1, \"i\", 66);\n    i0.ɵɵtext(2, \" \\u522A\\u9664 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction TemplateViewerComponent_tr_31_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\")(2, \"strong\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"td\")(5, \"span\", 59);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"td\", 28)(8, \"div\", 61)(9, \"button\", 62);\n    i0.ɵɵlistener(\"click\", function TemplateViewerComponent_tr_31_Template_button_click_9_listener() {\n      const tpl_r8 = i0.ɵɵrestoreView(_r7).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onSelectTemplate(tpl_r8));\n    });\n    i0.ɵɵelement(10, \"i\", 63);\n    i0.ɵɵtext(11, \" \\u67E5\\u770B \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(12, TemplateViewerComponent_tr_31_button_12_Template, 3, 0, \"button\", 64);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const tpl_r8 = ctx.$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(tpl_r8.TemplateName);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(tpl_r8.Description || \"\\u7121\\u63CF\\u8FF0\");\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngIf\", tpl_r8.TemplateID);\n  }\n}\nfunction TemplateViewerComponent_tr_32_small_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"small\", 59);\n    i0.ɵɵtext(1, \" \\u8ACB\\u5617\\u8A66\\u5176\\u4ED6\\u95DC\\u9375\\u5B57\\u6216 \");\n    i0.ɵɵelementStart(2, \"a\", 72);\n    i0.ɵɵlistener(\"click\", function TemplateViewerComponent_tr_32_small_6_Template_a_click_2_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.clearSearch());\n    });\n    i0.ɵɵtext(3, \"\\u6E05\\u9664\\u641C\\u5C0B\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction TemplateViewerComponent_tr_32_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 67)(2, \"div\", 68);\n    i0.ɵɵelement(3, \"i\", 69);\n    i0.ɵɵelementStart(4, \"p\", 70);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(6, TemplateViewerComponent_tr_32_small_6_Template, 4, 0, \"small\", 71);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.searchKeyword ? \"\\u627E\\u4E0D\\u5230\\u7B26\\u5408\\u689D\\u4EF6\\u7684\\u6A21\\u677F\" : \"\\u66AB\\u7121\\u6A21\\u677F\", \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.searchKeyword);\n  }\n}\nfunction TemplateViewerComponent_div_33_li_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"li\", 76)(1, \"button\", 81);\n    i0.ɵɵlistener(\"click\", function TemplateViewerComponent_div_33_li_6_Template_button_click_1_listener() {\n      const page_r13 = i0.ɵɵrestoreView(_r12).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.goToTemplatePage(page_r13));\n    });\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const page_r13 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassProp(\"active\", page_r13 === ctx_r1.templatePagination.currentPage);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(page_r13);\n  }\n}\nfunction TemplateViewerComponent_div_33_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 73)(1, \"nav\", 74)(2, \"ul\", 75)(3, \"li\", 76)(4, \"button\", 77);\n    i0.ɵɵlistener(\"click\", function TemplateViewerComponent_div_33_Template_button_click_4_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.goToTemplatePage(ctx_r1.templatePagination.currentPage - 1));\n    });\n    i0.ɵɵelement(5, \"i\", 78);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(6, TemplateViewerComponent_div_33_li_6_Template, 3, 3, \"li\", 79);\n    i0.ɵɵelementStart(7, \"li\", 76)(8, \"button\", 77);\n    i0.ɵɵlistener(\"click\", function TemplateViewerComponent_div_33_Template_button_click_8_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.goToTemplatePage(ctx_r1.templatePagination.currentPage + 1));\n    });\n    i0.ɵɵelement(9, \"i\", 80);\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassProp(\"disabled\", ctx_r1.templatePagination.currentPage === 1);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r1.templatePagination.currentPage === 1);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.getTemplatePageNumbers());\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"disabled\", ctx_r1.templatePagination.currentPage === ctx_r1.templatePagination.totalPages);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r1.templatePagination.currentPage === ctx_r1.templatePagination.totalPages);\n  }\n}\nfunction TemplateViewerComponent_div_34_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 93)(1, \"strong\");\n    i0.ɵɵtext(2, \"\\u63CF\\u8FF0\\uFF1A\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\", 59);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r1.selectedTemplate.Description);\n  }\n}\nfunction TemplateViewerComponent_div_34_small_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 59);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate2(\" \\u7B2C \", ctx_r1.detailPagination.currentPage, \" / \", ctx_r1.detailPagination.totalPages, \" \\u9801 \");\n  }\n}\nfunction TemplateViewerComponent_div_34_div_16_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 96)(1, \"div\", 97)(2, \"span\", 98);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"div\", 99)(5, \"div\", 100)(6, \"strong\");\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 101);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const detail_r15 = ctx.$implicit;\n    const i_r16 = ctx.index;\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate((ctx_r1.detailPagination.currentPage - 1) * ctx_r1.detailPagination.pageSize + i_r16 + 1);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\"\", detail_r15.FieldName, \":\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", detail_r15.FieldValue, \" \");\n  }\n}\nfunction TemplateViewerComponent_div_34_div_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 94);\n    i0.ɵɵtemplate(1, TemplateViewerComponent_div_34_div_16_div_1_Template, 10, 3, \"div\", 95);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.paginatedDetails);\n  }\n}\nfunction TemplateViewerComponent_div_34_div_17_li_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r18 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"li\", 76)(1, \"button\", 81);\n    i0.ɵɵlistener(\"click\", function TemplateViewerComponent_div_34_div_17_li_6_Template_button_click_1_listener() {\n      const page_r19 = i0.ɵɵrestoreView(_r18).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.goToDetailPage(page_r19));\n    });\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const page_r19 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵclassProp(\"active\", page_r19 === ctx_r1.detailPagination.currentPage);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(page_r19);\n  }\n}\nfunction TemplateViewerComponent_div_34_div_17_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r17 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 102)(1, \"nav\", 103)(2, \"ul\", 75)(3, \"li\", 76)(4, \"button\", 77);\n    i0.ɵɵlistener(\"click\", function TemplateViewerComponent_div_34_div_17_Template_button_click_4_listener() {\n      i0.ɵɵrestoreView(_r17);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.goToDetailPage(ctx_r1.detailPagination.currentPage - 1));\n    });\n    i0.ɵɵelement(5, \"i\", 78);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(6, TemplateViewerComponent_div_34_div_17_li_6_Template, 3, 3, \"li\", 79);\n    i0.ɵɵelementStart(7, \"li\", 76)(8, \"button\", 77);\n    i0.ɵɵlistener(\"click\", function TemplateViewerComponent_div_34_div_17_Template_button_click_8_listener() {\n      i0.ɵɵrestoreView(_r17);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.goToDetailPage(ctx_r1.detailPagination.currentPage + 1));\n    });\n    i0.ɵɵelement(9, \"i\", 80);\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassProp(\"disabled\", ctx_r1.detailPagination.currentPage === 1);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r1.detailPagination.currentPage === 1);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.getDetailPageNumbers());\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"disabled\", ctx_r1.detailPagination.currentPage === ctx_r1.detailPagination.totalPages);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r1.detailPagination.currentPage === ctx_r1.detailPagination.totalPages);\n  }\n}\nfunction TemplateViewerComponent_div_34_ng_template_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 104);\n    i0.ɵɵelement(1, \"i\", 105);\n    i0.ɵɵelementStart(2, \"p\", 70);\n    i0.ɵɵtext(3, \"\\u6B64\\u6A21\\u677F\\u66AB\\u7121\\u5167\\u5BB9\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction TemplateViewerComponent_div_34_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r14 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 82)(1, \"div\", 83)(2, \"h6\", 3);\n    i0.ɵɵelement(3, \"i\", 84);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 85);\n    i0.ɵɵlistener(\"click\", function TemplateViewerComponent_div_34_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.closeTemplateDetail());\n    });\n    i0.ɵɵelement(6, \"i\", 31);\n    i0.ɵɵtext(7, \" \\u95DC\\u9589 \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 86);\n    i0.ɵɵtemplate(9, TemplateViewerComponent_div_34_div_9_Template, 5, 1, \"div\", 87);\n    i0.ɵɵelementStart(10, \"div\", 88)(11, \"div\", 89)(12, \"h6\", 3);\n    i0.ɵɵelement(13, \"i\", 90);\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(15, TemplateViewerComponent_div_34_small_15_Template, 2, 2, \"small\", 71);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(16, TemplateViewerComponent_div_34_div_16_Template, 2, 1, \"div\", 91)(17, TemplateViewerComponent_div_34_div_17_Template, 10, 7, \"div\", 92)(18, TemplateViewerComponent_div_34_ng_template_18_Template, 4, 0, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const noDetails_r20 = i0.ɵɵreference(19);\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \\u6A21\\u677F\\u7D30\\u7BC0\\uFF1A\", ctx_r1.selectedTemplate.TemplateName, \" \");\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.selectedTemplate.Description);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \\u6A21\\u677F\\u5167\\u5BB9 (\", ctx_r1.currentTemplateDetails.length, \" \\u9805) \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.detailPagination.totalPages > 1);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.currentTemplateDetails.length > 0)(\"ngIfElse\", noDetails_r20);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.detailPagination.totalPages > 1);\n  }\n}\nexport class TemplateViewerComponent {\n  constructor() {\n    this.availableData = []; // 父元件傳入的可選資料 (包含關聯主檔ID和名稱)\n    this.moduleType = 'Requirement'; // 模組類型，用於區分資料來源\n    this.selectTemplate = new EventEmitter();\n    this.close = new EventEmitter(); // 關閉事件\n    // 公開Math對象供模板使用\n    this.Math = Math;\n    // 內部管理的模板資料\n    this.templates = [];\n    this.templateDetails = [];\n    this.selectedTemplate = null;\n    // 查詢功能\n    this.searchKeyword = '';\n    this.filteredTemplates = [];\n    // 分頁功能 - 模板列表\n    this.templatePagination = {\n      currentPage: 1,\n      pageSize: 10,\n      totalItems: 0,\n      totalPages: 0\n    };\n    this.paginatedTemplates = [];\n    // 分頁功能 - 模板詳情\n    this.detailPagination = {\n      currentPage: 1,\n      pageSize: 5,\n      totalItems: 0,\n      totalPages: 0\n    };\n    this.paginatedDetails = [];\n    // 新增模板表單\n    this.showAddForm = false;\n    this.newTemplate = {\n      name: '',\n      description: '',\n      selectedItems: []\n    };\n  }\n  ngOnInit() {\n    this.loadTemplates();\n    this.updateFilteredTemplates();\n  }\n  ngOnChanges() {\n    this.updateFilteredTemplates();\n  }\n  // TODO: 替換為實際的API調用\n  loadTemplates() {\n    // 模擬API調用 - 載入模板列表\n    console.log('載入模板 API 調用:', {\n      moduleType: this.moduleType\n    });\n    this.templates = [{\n      TemplateID: 1,\n      TemplateName: '需求模板A',\n      Description: '包含基本工程項目的模板'\n    }, {\n      TemplateID: 2,\n      TemplateName: '需求模板B',\n      Description: '包含進階工程項目的模板'\n    }];\n    // 載入對應模組類型的模板詳情\n    this.templateDetails = [{\n      TemplateDetailID: 1,\n      TemplateID: 1,\n      RefID: 101,\n      ModuleType: this.moduleType,\n      FieldName: this.getFieldName({}),\n      FieldValue: '工程項目A'\n    }, {\n      TemplateDetailID: 2,\n      TemplateID: 1,\n      RefID: 102,\n      ModuleType: this.moduleType,\n      FieldName: this.getFieldName({}),\n      FieldValue: '工程項目B'\n    }, {\n      TemplateDetailID: 3,\n      TemplateID: 2,\n      RefID: 201,\n      ModuleType: this.moduleType,\n      FieldName: this.getFieldName({}),\n      FieldValue: '工程項目C'\n    }];\n  }\n  // 更新過濾後的模板列表\n  updateFilteredTemplates() {\n    if (!this.searchKeyword.trim()) {\n      this.filteredTemplates = [...this.templates];\n    } else {\n      const keyword = this.searchKeyword.toLowerCase();\n      this.filteredTemplates = this.templates.filter(template => template.TemplateName.toLowerCase().includes(keyword) || template.Description && template.Description.toLowerCase().includes(keyword));\n    }\n    this.updateTemplatePagination();\n  }\n  // 更新模板分頁\n  updateTemplatePagination() {\n    this.templatePagination.totalItems = this.filteredTemplates.length;\n    this.templatePagination.totalPages = Math.ceil(this.templatePagination.totalItems / this.templatePagination.pageSize);\n    // 確保當前頁面不超過總頁數\n    if (this.templatePagination.currentPage > this.templatePagination.totalPages) {\n      this.templatePagination.currentPage = Math.max(1, this.templatePagination.totalPages);\n    }\n    this.updatePaginatedTemplates();\n  }\n  // 更新分頁後的模板列表\n  updatePaginatedTemplates() {\n    const startIndex = (this.templatePagination.currentPage - 1) * this.templatePagination.pageSize;\n    const endIndex = startIndex + this.templatePagination.pageSize;\n    this.paginatedTemplates = this.filteredTemplates.slice(startIndex, endIndex);\n  }\n  // 模板分頁導航\n  goToTemplatePage(page) {\n    if (page >= 1 && page <= this.templatePagination.totalPages) {\n      this.templatePagination.currentPage = page;\n      this.updatePaginatedTemplates();\n    }\n  }\n  // 獲取模板分頁頁碼數組\n  getTemplatePageNumbers() {\n    const pages = [];\n    const totalPages = this.templatePagination.totalPages;\n    const currentPage = this.templatePagination.currentPage;\n    // 顯示當前頁面前後各2頁\n    const startPage = Math.max(1, currentPage - 2);\n    const endPage = Math.min(totalPages, currentPage + 2);\n    for (let i = startPage; i <= endPage; i++) {\n      pages.push(i);\n    }\n    return pages;\n  }\n  // 搜尋模板\n  onSearch() {\n    this.updateFilteredTemplates();\n  }\n  // 清除搜尋\n  clearSearch() {\n    this.searchKeyword = '';\n    this.updateFilteredTemplates();\n  }\n  // 顯示新增模板表單\n  onAddTemplate() {\n    this.showAddForm = true;\n    this.newTemplate = {\n      name: '',\n      description: '',\n      selectedItems: []\n    };\n    // 重置可選資料的選擇狀態\n    this.availableData.forEach(item => item.selected = false);\n  }\n  // 取消新增模板\n  cancelAddTemplate() {\n    this.showAddForm = false;\n    this.newTemplate = {\n      name: '',\n      description: '',\n      selectedItems: []\n    };\n  }\n  // 儲存新模板\n  saveNewTemplate() {\n    if (!this.newTemplate.name.trim()) {\n      alert('請輸入模板名稱');\n      return;\n    }\n    const selectedItems = this.availableData.filter(item => item.selected);\n    if (selectedItems.length === 0) {\n      alert('請至少選擇一個項目');\n      return;\n    }\n    // TODO: 替換為實際的API調用\n    this.createTemplate(this.newTemplate.name, this.newTemplate.description, selectedItems);\n  }\n  // TODO: 替換為實際的API調用\n  createTemplate(name, description, selectedItems) {\n    // 模擬API調用 - 創建模板\n    console.log('創建模板 API 調用:', {\n      templateName: name,\n      description: description,\n      moduleType: this.moduleType,\n      selectedItems: selectedItems.map(item => ({\n        refId: this.getRefId(item),\n        fieldName: this.getFieldName(item),\n        fieldValue: this.getFieldValue(item)\n      }))\n    });\n    // 生成新的模板ID (實際應由後端API返回)\n    const newId = Math.max(...this.templates.map(t => t.TemplateID || 0), 0) + 1;\n    // 創建新模板\n    const newTemplate = {\n      TemplateID: newId,\n      TemplateName: name.trim(),\n      Description: description.trim()\n    };\n    // 添加到模板列表\n    this.templates.push(newTemplate);\n    // 創建模板詳情 - 根據選中的項目創建詳情記錄\n    selectedItems.forEach((item, index) => {\n      const detail = {\n        TemplateDetailID: this.templateDetails.length + index + 1,\n        TemplateID: newId,\n        RefID: this.getRefId(item),\n        // 關聯主檔ID\n        ModuleType: this.moduleType,\n        // 模組類型，用於區分資料來源\n        FieldName: this.getFieldName(item),\n        // 欄位名稱\n        FieldValue: this.getFieldValue(item) // 欄位值\n      };\n      this.templateDetails.push(detail);\n    });\n    // 更新過濾列表\n    this.updateFilteredTemplates();\n    // 關閉表單\n    this.showAddForm = false;\n    alert(`模板 \"${name}\" 已成功創建！包含 ${selectedItems.length} 個項目`);\n  }\n  // 獲取關聯主檔ID的輔助方法\n  getRefId(item) {\n    return item.CRequirementID || item.ID || item.id || 0;\n  }\n  // 獲取欄位名稱的輔助方法\n  getFieldName(_item) {\n    // 根據模組類型決定欄位名稱\n    switch (this.moduleType) {\n      case 'Requirement':\n        return 'CRequirement';\n      default:\n        return 'name';\n    }\n  }\n  // 獲取欄位值的輔助方法\n  getFieldValue(item) {\n    return item.CRequirement || item.name || item.title || '';\n  }\n  // 查看模板\n  onSelectTemplate(template) {\n    this.selectedTemplate = template;\n    this.selectTemplate.emit(template);\n    this.updateDetailPagination();\n  }\n  // 更新詳情分頁\n  updateDetailPagination() {\n    const details = this.currentTemplateDetails;\n    this.detailPagination.totalItems = details.length;\n    this.detailPagination.totalPages = Math.ceil(this.detailPagination.totalItems / this.detailPagination.pageSize);\n    this.detailPagination.currentPage = 1; // 重置到第一頁\n    this.updatePaginatedDetails();\n  }\n  // 更新分頁後的詳情列表\n  updatePaginatedDetails() {\n    const details = this.currentTemplateDetails;\n    const startIndex = (this.detailPagination.currentPage - 1) * this.detailPagination.pageSize;\n    const endIndex = startIndex + this.detailPagination.pageSize;\n    this.paginatedDetails = details.slice(startIndex, endIndex);\n  }\n  // 詳情分頁導航\n  goToDetailPage(page) {\n    if (page >= 1 && page <= this.detailPagination.totalPages) {\n      this.detailPagination.currentPage = page;\n      this.updatePaginatedDetails();\n    }\n  }\n  // 獲取詳情分頁頁碼數組\n  getDetailPageNumbers() {\n    const pages = [];\n    const totalPages = this.detailPagination.totalPages;\n    const currentPage = this.detailPagination.currentPage;\n    // 顯示當前頁面前後各2頁\n    const startPage = Math.max(1, currentPage - 2);\n    const endPage = Math.min(totalPages, currentPage + 2);\n    for (let i = startPage; i <= endPage; i++) {\n      pages.push(i);\n    }\n    return pages;\n  }\n  // 關閉模板查看器\n  onClose() {\n    this.close.emit();\n  }\n  // 刪除模板\n  onDeleteTemplate(templateID) {\n    if (confirm('確定刪除此模板？')) {\n      // TODO: 替換為實際的API調用\n      this.deleteTemplateById(templateID);\n    }\n  }\n  // TODO: 替換為實際的API調用\n  deleteTemplateById(templateID) {\n    // 模擬API調用 - 刪除模板\n    console.log('刪除模板 API 調用:', {\n      templateID: templateID,\n      moduleType: this.moduleType\n    });\n    // 刪除模板和相關詳情\n    this.templates = this.templates.filter(t => t.TemplateID !== templateID);\n    this.templateDetails = this.templateDetails.filter(d => d.TemplateID !== templateID);\n    this.updateFilteredTemplates();\n    // 如果當前查看的模板被刪除，關閉詳情\n    if (this.selectedTemplate?.TemplateID === templateID) {\n      this.selectedTemplate = null;\n    }\n    alert('模板已刪除');\n  }\n  /*\n   * API 設計說明：\n   *\n   * 1. 載入模板列表 API:\n   *    GET /api/templates?moduleType={moduleType}\n   *    返回: { templates: Template[], templateDetails: TemplateDetail[] }\n   *\n   * 2. 創建模板 API:\n   *    POST /api/templates\n   *    請求體: {\n   *      templateName: string,\n   *      description: string,\n   *      moduleType: string,\n   *      details: [{\n   *        refId: number,        // 關聯主檔ID\n   *        fieldName: string,    // 欄位名稱\n   *        fieldValue: string    // 欄位值\n   *      }]\n   *    }\n   *\n   * 3. 刪除模板 API:\n   *    DELETE /api/templates/{templateId}?moduleType={moduleType}\n   *\n   * 資料庫設計重點：\n   * - template 表：存放模板基本資訊 (TemplateID, TemplateName, Description)\n   * - templatedetail 表：存放模板詳情 (TemplateDetailID, TemplateID, RefID, ModuleType, FieldName, FieldValue)\n   * - ModuleType 欄位用於區分不同模組的資料 (如: 'Requirement', 'Product', 'Order' 等)\n   * - RefID 存放關聯主檔的ID，配合 ModuleType 可以找到對應的原始資料\n   */\n  // 關閉模板詳情\n  closeTemplateDetail() {\n    this.selectedTemplate = null;\n  }\n  // 取得當前選中模板的詳情\n  get currentTemplateDetails() {\n    if (!this.selectedTemplate) {\n      return [];\n    }\n    return this.templateDetails.filter(d => d.TemplateID === this.selectedTemplate.TemplateID);\n  }\n  // TrackBy函數用於優化ngFor性能\n  trackByTemplateId(index, template) {\n    return template.TemplateID || index;\n  }\n  static {\n    this.ɵfac = function TemplateViewerComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || TemplateViewerComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: TemplateViewerComponent,\n      selectors: [[\"app-template-viewer\"]],\n      inputs: {\n        availableData: \"availableData\",\n        moduleType: \"moduleType\"\n      },\n      outputs: {\n        selectTemplate: \"selectTemplate\",\n        close: \"close\"\n      },\n      standalone: true,\n      features: [i0.ɵɵNgOnChangesFeature, i0.ɵɵStandaloneFeature],\n      decls: 40,\n      vars: 10,\n      consts: [[\"noDetails\", \"\"], [2, \"width\", \"90vw\", \"max-width\", \"1200px\", \"height\", \"80vh\"], [1, \"d-flex\", \"justify-content-between\", \"align-items-center\"], [1, \"mb-0\"], [1, \"btn\", \"btn-success\", \"btn-sm\", 3, \"click\"], [1, \"fas\", \"fa-plus\", \"mr-1\"], [2, \"overflow\", \"auto\"], [1, \"search-container\", \"mb-3\"], [1, \"input-group\"], [\"type\", \"text\", \"placeholder\", \"\\u641C\\u5C0B\\u6A21\\u677F\\u540D\\u7A31\\u6216\\u63CF\\u8FF0...\", 1, \"form-control\", 3, \"ngModelChange\", \"input\", \"keyup.enter\", \"ngModel\"], [1, \"input-group-append\"], [\"type\", \"button\", 1, \"btn\", \"btn-outline-secondary\", 3, \"click\"], [1, \"fas\", \"fa-search\"], [\"class\", \"btn btn-outline-secondary\", \"type\", \"button\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"add-template-form mb-4\", 4, \"ngIf\"], [1, \"template-list\"], [\"class\", \"search-results-info mb-2\", 4, \"ngIf\"], [\"class\", \"pagination-info mb-2\", 4, \"ngIf\"], [1, \"table-responsive\"], [1, \"table\", \"table-striped\", \"table-hover\"], [1, \"thead-light\"], [\"width\", \"30%\"], [\"width\", \"50%\"], [\"width\", \"20%\", 1, \"text-center\"], [4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [4, \"ngIf\"], [\"class\", \"pagination-container mt-3\", 4, \"ngIf\"], [\"class\", \"template-detail-modal\", 4, \"ngIf\"], [1, \"text-center\"], [1, \"btn\", \"btn-secondary\", 3, \"click\"], [1, \"fas\", \"fa-times\", \"mr-1\"], [1, \"fas\", \"fa-times\"], [1, \"add-template-form\", \"mb-4\"], [1, \"form-container\"], [1, \"form-header\"], [1, \"form-title\"], [1, \"fas\", \"fa-plus\"], [1, \"form-content\", 3, \"ngSubmit\"], [1, \"input-row\"], [1, \"input-label\"], [1, \"required\"], [\"type\", \"text\", \"name\", \"templateName\", \"placeholder\", \"\\u8ACB\\u8F38\\u5165\\u6A21\\u677F\\u540D\\u7A31\", \"required\", \"\", \"maxlength\", \"50\", 1, \"input-field\", 3, \"ngModelChange\", \"ngModel\"], [\"type\", \"text\", \"name\", \"templateDescription\", \"placeholder\", \"\\u8ACB\\u8F38\\u5165\\u6A21\\u677F\\u63CF\\u8FF0\\uFF08\\u53EF\\u9078\\uFF09\", \"maxlength\", \"100\", 1, \"input-field\", 3, \"ngModelChange\", \"ngModel\"], [1, \"input-group\", \"full-width\"], [1, \"items-selector\"], [\"class\", \"empty-items\", 4, \"ngIf\"], [\"class\", \"item-option\", 4, \"ngFor\", \"ngForOf\"], [1, \"form-actions\"], [\"type\", \"button\", 1, \"btn-cancel\", 3, \"click\"], [\"type\", \"submit\", 1, \"btn-save\"], [1, \"empty-items\"], [1, \"fas\", \"fa-info-circle\"], [1, \"item-option\"], [1, \"item-label\"], [\"type\", \"checkbox\", 1, \"item-checkbox\", 3, \"ngModelChange\", \"ngModel\", \"name\"], [1, \"item-content\"], [1, \"item-title\"], [1, \"item-desc\"], [1, \"search-results-info\", \"mb-2\"], [1, \"text-muted\"], [1, \"pagination-info\", \"mb-2\"], [\"role\", \"group\", 1, \"btn-group\", \"btn-group-sm\"], [\"title\", \"\\u67E5\\u770B\\u8A73\\u60C5\", 1, \"btn\", \"btn-info\", 3, \"click\"], [1, \"fas\", \"fa-eye\"], [\"class\", \"btn btn-danger\", \"title\", \"\\u522A\\u9664\\u6A21\\u677F\", 3, \"click\", 4, \"ngIf\"], [\"title\", \"\\u522A\\u9664\\u6A21\\u677F\", 1, \"btn\", \"btn-danger\", 3, \"click\"], [1, \"fas\", \"fa-trash\"], [\"colspan\", \"3\", 1, \"text-center\", \"py-4\"], [1, \"empty-state\"], [1, \"fas\", \"fa-folder-open\", \"fa-2x\", \"text-muted\", \"mb-2\"], [1, \"text-muted\", \"mb-0\"], [\"class\", \"text-muted\", 4, \"ngIf\"], [\"href\", \"javascript:void(0)\", 3, \"click\"], [1, \"pagination-container\", \"mt-3\"], [\"aria-label\", \"\\u6A21\\u677F\\u5217\\u8868\\u5206\\u9801\"], [1, \"pagination\", \"pagination-sm\", \"justify-content-center\", \"mb-0\"], [1, \"page-item\"], [1, \"page-link\", 3, \"click\", \"disabled\"], [1, \"fas\", \"fa-chevron-left\"], [\"class\", \"page-item\", 3, \"active\", 4, \"ngFor\", \"ngForOf\"], [1, \"fas\", \"fa-chevron-right\"], [1, \"page-link\", 3, \"click\"], [1, \"template-detail-modal\"], [1, \"template-detail-header\", \"d-flex\", \"justify-content-between\", \"align-items-center\"], [1, \"fas\", \"fa-file-alt\", \"mr-2\"], [1, \"btn\", \"btn-outline-secondary\", \"btn-sm\", 3, \"click\"], [1, \"template-detail-content\"], [\"class\", \"template-description mb-3\", 4, \"ngIf\"], [1, \"template-items\"], [1, \"d-flex\", \"justify-content-between\", \"align-items-center\", \"mb-2\"], [1, \"fas\", \"fa-list\", \"mr-1\"], [\"class\", \"detail-list\", 4, \"ngIf\", \"ngIfElse\"], [\"class\", \"detail-pagination mt-3\", 4, \"ngIf\"], [1, \"template-description\", \"mb-3\"], [1, \"detail-list\"], [\"class\", \"detail-item d-flex align-items-center py-2 border-bottom\", 4, \"ngFor\", \"ngForOf\"], [1, \"detail-item\", \"d-flex\", \"align-items-center\", \"py-2\", \"border-bottom\"], [1, \"detail-index\"], [1, \"badge\", \"badge-light\"], [1, \"detail-content\", \"flex-grow-1\", \"ml-2\"], [1, \"detail-field\"], [1, \"detail-value\", \"text-muted\"], [1, \"detail-pagination\", \"mt-3\"], [\"aria-label\", \"\\u6A21\\u677F\\u8A73\\u60C5\\u5206\\u9801\"], [1, \"text-center\", \"py-3\"], [1, \"fas\", \"fa-inbox\", \"fa-2x\", \"text-muted\", \"mb-2\"]],\n      template: function TemplateViewerComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"nb-card\", 1)(1, \"nb-card-header\")(2, \"div\", 2)(3, \"h5\", 3);\n          i0.ɵɵtext(4, \"\\u6A21\\u677F\\u7BA1\\u7406\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(5, \"button\", 4);\n          i0.ɵɵlistener(\"click\", function TemplateViewerComponent_Template_button_click_5_listener() {\n            return ctx.onAddTemplate();\n          });\n          i0.ɵɵelement(6, \"i\", 5);\n          i0.ɵɵtext(7, \"\\u65B0\\u589E\\u6A21\\u677F \");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(8, \"nb-card-body\", 6)(9, \"div\", 7)(10, \"div\", 8)(11, \"input\", 9);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function TemplateViewerComponent_Template_input_ngModelChange_11_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.searchKeyword, $event) || (ctx.searchKeyword = $event);\n            return $event;\n          });\n          i0.ɵɵlistener(\"input\", function TemplateViewerComponent_Template_input_input_11_listener() {\n            return ctx.onSearch();\n          })(\"keyup.enter\", function TemplateViewerComponent_Template_input_keyup_enter_11_listener() {\n            return ctx.onSearch();\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(12, \"div\", 10)(13, \"button\", 11);\n          i0.ɵɵlistener(\"click\", function TemplateViewerComponent_Template_button_click_13_listener() {\n            return ctx.onSearch();\n          });\n          i0.ɵɵelement(14, \"i\", 12);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(15, TemplateViewerComponent_button_15_Template, 2, 0, \"button\", 13);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(16, TemplateViewerComponent_div_16_Template, 32, 4, \"div\", 14);\n          i0.ɵɵelementStart(17, \"div\", 15);\n          i0.ɵɵtemplate(18, TemplateViewerComponent_div_18_Template, 3, 2, \"div\", 16)(19, TemplateViewerComponent_div_19_Template, 3, 3, \"div\", 17);\n          i0.ɵɵelementStart(20, \"div\", 18)(21, \"table\", 19)(22, \"thead\", 20)(23, \"tr\")(24, \"th\", 21);\n          i0.ɵɵtext(25, \"\\u6A21\\u677F\\u540D\\u7A31\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(26, \"th\", 22);\n          i0.ɵɵtext(27, \"\\u63CF\\u8FF0\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(28, \"th\", 23);\n          i0.ɵɵtext(29, \"\\u64CD\\u4F5C\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(30, \"tbody\");\n          i0.ɵɵtemplate(31, TemplateViewerComponent_tr_31_Template, 13, 3, \"tr\", 24)(32, TemplateViewerComponent_tr_32_Template, 7, 2, \"tr\", 25);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(33, TemplateViewerComponent_div_33_Template, 10, 7, \"div\", 26);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(34, TemplateViewerComponent_div_34_Template, 20, 7, \"div\", 27);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(35, \"nb-card-footer\")(36, \"div\", 28)(37, \"button\", 29);\n          i0.ɵɵlistener(\"click\", function TemplateViewerComponent_Template_button_click_37_listener() {\n            return ctx.onClose();\n          });\n          i0.ɵɵelement(38, \"i\", 30);\n          i0.ɵɵtext(39, \"\\u95DC\\u9589 \");\n          i0.ɵɵelementEnd()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(11);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchKeyword);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", ctx.searchKeyword);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.showAddForm);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.searchKeyword);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.templatePagination.totalItems > 0);\n          i0.ɵɵadvance(12);\n          i0.ɵɵproperty(\"ngForOf\", ctx.paginatedTemplates)(\"ngForTrackBy\", ctx.trackByTemplateId);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.paginatedTemplates || ctx.paginatedTemplates.length === 0);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.templatePagination.totalPages > 1);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.selectedTemplate);\n        }\n      },\n      dependencies: [CommonModule, i1.NgForOf, i1.NgIf, FormsModule, i2.ɵNgNoValidate, i2.DefaultValueAccessor, i2.CheckboxControlValueAccessor, i2.NgControlStatus, i2.NgControlStatusGroup, i2.RequiredValidator, i2.MaxLengthValidator, i2.NgModel, i2.NgForm, NbCardModule, i3.NbCardComponent, i3.NbCardBodyComponent, i3.NbCardFooterComponent, i3.NbCardHeaderComponent, NbButtonModule],\n      styles: [\"nb-card-header[_ngcontent-%COMP%]   .btn-success[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);\\n  border: none;\\n  font-weight: 500;\\n  transition: all 0.2s ease;\\n}\\nnb-card-header[_ngcontent-%COMP%]   .btn-success[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-1px);\\n  box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);\\n}\\n\\n.search-container[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%] {\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\\n  border-radius: 8px;\\n  overflow: hidden;\\n}\\n.search-container[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .form-control[_ngcontent-%COMP%] {\\n  border: none;\\n  padding: 0.75rem 1rem;\\n  font-size: 0.95rem;\\n}\\n.search-container[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .form-control[_ngcontent-%COMP%]:focus {\\n  box-shadow: none;\\n  border-color: transparent;\\n}\\n.search-container[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .form-control[_ngcontent-%COMP%]::placeholder {\\n  color: #999;\\n  font-style: italic;\\n}\\n.search-container[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .input-group-append[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%] {\\n  border: none;\\n  background: #f8f9fa;\\n  color: #6c757d;\\n  padding: 0.75rem 1rem;\\n  transition: all 0.2s ease;\\n}\\n.search-container[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .input-group-append[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]:hover {\\n  background: #e9ecef;\\n  color: #495057;\\n}\\n.search-container[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .input-group-append[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]:focus {\\n  box-shadow: none;\\n}\\n\\n.search-results-info[_ngcontent-%COMP%] {\\n  padding: 0.5rem 0;\\n  border-left: 3px solid #007bff;\\n  padding-left: 0.75rem;\\n  background: #f8f9ff;\\n  border-radius: 4px;\\n}\\n\\n.pagination-info[_ngcontent-%COMP%] {\\n  padding: 0.5rem 0;\\n  border-left: 3px solid #28a745;\\n  padding-left: 0.75rem;\\n  background: #f8fff8;\\n  border-radius: 4px;\\n}\\n\\n.table-responsive[_ngcontent-%COMP%] {\\n  border-radius: 8px;\\n  overflow: hidden;\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);\\n}\\n\\n.table[_ngcontent-%COMP%] {\\n  margin-bottom: 0;\\n}\\n.table[_ngcontent-%COMP%]   thead.thead-light[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);\\n}\\n.table[_ngcontent-%COMP%]   thead.thead-light[_ngcontent-%COMP%]   th[_ngcontent-%COMP%] {\\n  border: none;\\n  font-weight: 600;\\n  color: #495057;\\n  padding: 1rem 0.75rem;\\n  font-size: 0.9rem;\\n  text-transform: uppercase;\\n  letter-spacing: 0.5px;\\n}\\n.table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%] {\\n  transition: all 0.2s ease;\\n}\\n.table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]:hover {\\n  background-color: #f8f9ff;\\n  transform: translateY(-1px);\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\\n}\\n.table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   td[_ngcontent-%COMP%] {\\n  padding: 1rem 0.75rem;\\n  border-color: #f0f0f0;\\n  vertical-align: middle;\\n}\\n.table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%] {\\n  color: #333;\\n  font-weight: 600;\\n}\\n.table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]   .text-muted[_ngcontent-%COMP%] {\\n  font-size: 0.9rem;\\n}\\n\\n.btn-group-sm[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%] {\\n  padding: 0.375rem 0.75rem;\\n  font-size: 0.875rem;\\n  border-radius: 4px;\\n  font-weight: 500;\\n  transition: all 0.2s ease;\\n}\\n.btn-group-sm[_ngcontent-%COMP%]   .btn.btn-info[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);\\n  border: none;\\n}\\n.btn-group-sm[_ngcontent-%COMP%]   .btn.btn-info[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-1px);\\n  box-shadow: 0 4px 12px rgba(23, 162, 184, 0.3);\\n}\\n.btn-group-sm[_ngcontent-%COMP%]   .btn.btn-danger[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);\\n  border: none;\\n}\\n.btn-group-sm[_ngcontent-%COMP%]   .btn.btn-danger[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-1px);\\n  box-shadow: 0 4px 12px rgba(220, 53, 69, 0.3);\\n}\\n.btn-group-sm[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  margin-right: 0.25rem;\\n}\\n\\n.empty-state[_ngcontent-%COMP%] {\\n  padding: 2rem;\\n}\\n.empty-state[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  display: block;\\n  margin: 0 auto 1rem;\\n  opacity: 0.5;\\n}\\n.empty-state[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  font-size: 1.1rem;\\n  margin-bottom: 0.5rem;\\n}\\n.empty-state[_ngcontent-%COMP%]   a[_ngcontent-%COMP%] {\\n  color: #007bff;\\n  text-decoration: none;\\n}\\n.empty-state[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]:hover {\\n  text-decoration: underline;\\n}\\n\\n.template-detail-modal[_ngcontent-%COMP%] {\\n  margin-top: 1.5rem;\\n  background: #fff;\\n  border: 2px solid #e9ecef;\\n  border-radius: 12px;\\n  overflow: hidden;\\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);\\n  animation: _ngcontent-%COMP%_slideDown 0.3s ease-out;\\n}\\n\\n@keyframes _ngcontent-%COMP%_slideDown {\\n  from {\\n    opacity: 0;\\n    transform: translateY(-20px);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: translateY(0);\\n  }\\n}\\n.template-detail-header[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);\\n  padding: 1rem 1.5rem;\\n  border-bottom: 1px solid #dee2e6;\\n}\\n.template-detail-header[_ngcontent-%COMP%]   h6[_ngcontent-%COMP%] {\\n  color: #495057;\\n  font-weight: 600;\\n}\\n.template-detail-header[_ngcontent-%COMP%]   h6[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  color: #6c757d;\\n}\\n.template-detail-header[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  padding: 0.375rem 0.75rem;\\n}\\n\\n.template-detail-content[_ngcontent-%COMP%] {\\n  padding: 1.5rem;\\n  max-height: 400px;\\n  overflow-y: auto;\\n}\\n.template-detail-content[_ngcontent-%COMP%]   .template-description[_ngcontent-%COMP%] {\\n  background: #f8f9ff;\\n  padding: 1rem;\\n  border-radius: 8px;\\n  border-left: 4px solid #007bff;\\n}\\n.template-detail-content[_ngcontent-%COMP%]   .template-description[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%] {\\n  color: #495057;\\n}\\n.template-detail-content[_ngcontent-%COMP%]   .template-items[_ngcontent-%COMP%]   h6[_ngcontent-%COMP%] {\\n  color: #495057;\\n  font-weight: 600;\\n  padding-bottom: 0.5rem;\\n  border-bottom: 1px solid #e9ecef;\\n}\\n.template-detail-content[_ngcontent-%COMP%]   .template-items[_ngcontent-%COMP%]   h6[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  color: #6c757d;\\n}\\n\\n.detail-list[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%] {\\n  transition: all 0.2s ease;\\n}\\n.detail-list[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]:hover {\\n  background: #f8f9ff;\\n  border-radius: 6px;\\n  margin: 0 -0.5rem;\\n  padding-left: 0.5rem !important;\\n  padding-right: 0.5rem !important;\\n}\\n.detail-list[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]:last-child {\\n  border-bottom: none;\\n}\\n.detail-list[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   .detail-index[_ngcontent-%COMP%]   .badge[_ngcontent-%COMP%] {\\n  background: #e9ecef;\\n  color: #6c757d;\\n  font-weight: 500;\\n  padding: 0.375rem 0.5rem;\\n  border-radius: 50%;\\n  min-width: 2rem;\\n  text-align: center;\\n}\\n.detail-list[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   .detail-content[_ngcontent-%COMP%]   .detail-field[_ngcontent-%COMP%] {\\n  font-size: 0.9rem;\\n  margin-bottom: 0.25rem;\\n}\\n.detail-list[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   .detail-content[_ngcontent-%COMP%]   .detail-field[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%] {\\n  color: #495057;\\n}\\n.detail-list[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   .detail-content[_ngcontent-%COMP%]   .detail-value[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  line-height: 1.4;\\n  word-break: break-word;\\n}\\n\\n.add-template-form[_ngcontent-%COMP%]   .form-container[_ngcontent-%COMP%] {\\n  background: #ffffff;\\n  border: 1px solid #e8ecef;\\n  border-radius: 12px;\\n  overflow: hidden;\\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);\\n  transition: all 0.3s ease;\\n}\\n.add-template-form[_ngcontent-%COMP%]   .form-container[_ngcontent-%COMP%]:hover {\\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.12);\\n}\\n.add-template-form[_ngcontent-%COMP%]   .form-header[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);\\n  padding: 1rem 1.5rem;\\n  border-bottom: 1px solid #e8ecef;\\n}\\n.add-template-form[_ngcontent-%COMP%]   .form-header[_ngcontent-%COMP%]   .form-title[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.5rem;\\n  font-size: 1rem;\\n  font-weight: 600;\\n  color: #495057;\\n}\\n.add-template-form[_ngcontent-%COMP%]   .form-header[_ngcontent-%COMP%]   .form-title[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  color: #28a745;\\n  font-size: 0.9rem;\\n}\\n.add-template-form[_ngcontent-%COMP%]   .form-content[_ngcontent-%COMP%] {\\n  padding: 1.5rem;\\n}\\n.add-template-form[_ngcontent-%COMP%]   .input-row[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: 1fr 1fr;\\n  gap: 1rem;\\n  margin-bottom: 1.5rem;\\n}\\n@media (max-width: 768px) {\\n  .add-template-form[_ngcontent-%COMP%]   .input-row[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n  }\\n}\\n.add-template-form[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n}\\n.add-template-form[_ngcontent-%COMP%]   .input-group.full-width[_ngcontent-%COMP%] {\\n  grid-column: 1/-1;\\n}\\n.add-template-form[_ngcontent-%COMP%]   .input-label[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  font-weight: 500;\\n  color: #374151;\\n  margin-bottom: 0.5rem;\\n}\\n.add-template-form[_ngcontent-%COMP%]   .input-label[_ngcontent-%COMP%]   .required[_ngcontent-%COMP%] {\\n  color: #ef4444;\\n  margin-left: 0.125rem;\\n}\\n.add-template-form[_ngcontent-%COMP%]   .input-field[_ngcontent-%COMP%] {\\n  padding: 0.75rem 1rem;\\n  border: 1px solid #d1d5db;\\n  border-radius: 8px;\\n  font-size: 0.875rem;\\n  transition: all 0.2s ease;\\n  background: #ffffff;\\n}\\n.add-template-form[_ngcontent-%COMP%]   .input-field[_ngcontent-%COMP%]:focus {\\n  outline: none;\\n  border-color: #28a745;\\n  box-shadow: 0 0 0 3px rgba(40, 167, 69, 0.1);\\n}\\n.add-template-form[_ngcontent-%COMP%]   .input-field[_ngcontent-%COMP%]::placeholder {\\n  color: #9ca3af;\\n}\\n.add-template-form[_ngcontent-%COMP%]   .items-selector[_ngcontent-%COMP%] {\\n  border: 1px solid #d1d5db;\\n  border-radius: 8px;\\n  background: #f9fafb;\\n  max-height: 200px;\\n  overflow-y: auto;\\n}\\n.add-template-form[_ngcontent-%COMP%]   .empty-items[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  gap: 0.5rem;\\n  padding: 2rem;\\n  color: #6b7280;\\n  font-size: 0.875rem;\\n}\\n.add-template-form[_ngcontent-%COMP%]   .empty-items[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  color: #9ca3af;\\n}\\n.add-template-form[_ngcontent-%COMP%]   .item-option[_ngcontent-%COMP%] {\\n  border-bottom: 1px solid #e5e7eb;\\n}\\n.add-template-form[_ngcontent-%COMP%]   .item-option[_ngcontent-%COMP%]:last-child {\\n  border-bottom: none;\\n}\\n.add-template-form[_ngcontent-%COMP%]   .item-option[_ngcontent-%COMP%]:hover {\\n  background: #f3f4f6;\\n}\\n.add-template-form[_ngcontent-%COMP%]   .item-label[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: flex-start;\\n  gap: 0.75rem;\\n  padding: 0.875rem 1rem;\\n  cursor: pointer;\\n  margin: 0;\\n  width: 100%;\\n}\\n.add-template-form[_ngcontent-%COMP%]   .item-checkbox[_ngcontent-%COMP%] {\\n  margin: 0;\\n  margin-top: 0.125rem;\\n  width: 1rem;\\n  height: 1rem;\\n  accent-color: #28a745;\\n}\\n.add-template-form[_ngcontent-%COMP%]   .item-content[_ngcontent-%COMP%] {\\n  flex: 1;\\n  min-width: 0;\\n}\\n.add-template-form[_ngcontent-%COMP%]   .item-title[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  font-weight: 500;\\n  color: #374151;\\n  margin-bottom: 0.25rem;\\n  line-height: 1.4;\\n}\\n.add-template-form[_ngcontent-%COMP%]   .item-desc[_ngcontent-%COMP%] {\\n  font-size: 0.8rem;\\n  color: #6b7280;\\n  line-height: 1.3;\\n}\\n.add-template-form[_ngcontent-%COMP%]   .form-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: flex-end;\\n  gap: 0.75rem;\\n  margin-top: 1.5rem;\\n  padding-top: 1.5rem;\\n  border-top: 1px solid #e5e7eb;\\n}\\n.add-template-form[_ngcontent-%COMP%]   .btn-cancel[_ngcontent-%COMP%], \\n.add-template-form[_ngcontent-%COMP%]   .btn-save[_ngcontent-%COMP%] {\\n  padding: 0.625rem 1.25rem;\\n  border-radius: 8px;\\n  font-size: 0.875rem;\\n  font-weight: 500;\\n  border: none;\\n  cursor: pointer;\\n  transition: all 0.2s ease;\\n  min-width: 80px;\\n}\\n.add-template-form[_ngcontent-%COMP%]   .btn-cancel[_ngcontent-%COMP%] {\\n  background: #f3f4f6;\\n  color: #374151;\\n}\\n.add-template-form[_ngcontent-%COMP%]   .btn-cancel[_ngcontent-%COMP%]:hover {\\n  background: #e5e7eb;\\n  transform: translateY(-1px);\\n}\\n.add-template-form[_ngcontent-%COMP%]   .btn-save[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);\\n  color: white;\\n}\\n.add-template-form[_ngcontent-%COMP%]   .btn-save[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-1px);\\n  box-shadow: 0 4px 12px rgba(40, 167, 69, 0.25);\\n}\\n.add-template-form[_ngcontent-%COMP%]   .btn-save[_ngcontent-%COMP%]:disabled {\\n  opacity: 0.6;\\n  cursor: not-allowed;\\n  transform: none;\\n  box-shadow: none;\\n}\\n/*# sourceMappingURL=data:application/json;base64,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 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["EventEmitter", "CommonModule", "FormsModule", "NbCardModule", "NbButtonModule", "i0", "ɵɵelementStart", "ɵɵlistener", "TemplateViewerComponent_button_15_Template_button_click_0_listener", "ɵɵrestoreView", "_r1", "ctx_r1", "ɵɵnextContext", "ɵɵresetView", "clearSearch", "ɵɵelement", "ɵɵelementEnd", "ɵɵtext", "ɵɵtwoWayListener", "TemplateViewerComponent_div_16_div_26_Template_input_ngModelChange_2_listener", "$event", "item_r5", "_r4", "$implicit", "ɵɵtwoWayBindingSet", "selected", "ɵɵadvance", "ɵɵpropertyInterpolate1", "i_r6", "ɵɵtwoWayProperty", "ɵɵtextInterpolate", "CRequirement", "name", "CGroupName", "description", "TemplateViewerComponent_div_16_Template_form_ngSubmit_7_listener", "_r3", "saveNewTemplate", "TemplateViewerComponent_div_16_Template_input_ngModelChange_14_listener", "newTemplate", "TemplateViewerComponent_div_16_Template_input_ngModelChange_18_listener", "ɵɵtemplate", "TemplateViewerComponent_div_16_div_25_Template", "TemplateViewerComponent_div_16_div_26_Template", "TemplateViewerComponent_div_16_Template_button_click_28_listener", "cancelAddTemplate", "ɵɵproperty", "availableData", "length", "ɵɵtextInterpolate2", "filteredTemplates", "searchKeyword", "ɵɵtextInterpolate3", "templatePagination", "currentPage", "pageSize", "Math", "min", "totalItems", "TemplateViewerComponent_tr_31_button_12_Template_button_click_0_listener", "_r9", "tpl_r8", "TemplateID", "onDeleteTemplate", "TemplateViewerComponent_tr_31_Template_button_click_9_listener", "_r7", "onSelectTemplate", "TemplateViewerComponent_tr_31_button_12_Template", "TemplateName", "Description", "TemplateViewerComponent_tr_32_small_6_Template_a_click_2_listener", "_r10", "TemplateViewerComponent_tr_32_small_6_Template", "ɵɵtextInterpolate1", "TemplateViewerComponent_div_33_li_6_Template_button_click_1_listener", "page_r13", "_r12", "goToTemplatePage", "ɵɵclassProp", "TemplateViewerComponent_div_33_Template_button_click_4_listener", "_r11", "TemplateViewerComponent_div_33_li_6_Template", "TemplateViewerComponent_div_33_Template_button_click_8_listener", "getTemplatePageNumbers", "totalPages", "selectedTemplate", "detailPagination", "i_r16", "detail_r15", "FieldName", "FieldValue", "TemplateViewerComponent_div_34_div_16_div_1_Template", "paginatedDetails", "TemplateViewerComponent_div_34_div_17_li_6_Template_button_click_1_listener", "page_r19", "_r18", "goToDetailPage", "TemplateViewerComponent_div_34_div_17_Template_button_click_4_listener", "_r17", "TemplateViewerComponent_div_34_div_17_li_6_Template", "TemplateViewerComponent_div_34_div_17_Template_button_click_8_listener", "getDetailPageNumbers", "TemplateViewerComponent_div_34_Template_button_click_5_listener", "_r14", "closeTemplateDetail", "TemplateViewerComponent_div_34_div_9_Template", "TemplateViewerComponent_div_34_small_15_Template", "TemplateViewerComponent_div_34_div_16_Template", "TemplateViewerComponent_div_34_div_17_Template", "TemplateViewerComponent_div_34_ng_template_18_Template", "ɵɵtemplateRefExtractor", "currentTemplateDetails", "noDetails_r20", "TemplateViewerComponent", "constructor", "moduleType", "selectTemplate", "close", "templates", "templateDetails", "paginatedTemplates", "showAddForm", "selectedItems", "ngOnInit", "loadTemplates", "updateFilteredTemplates", "ngOnChanges", "console", "log", "TemplateDetailID", "RefID", "ModuleType", "getFieldName", "trim", "keyword", "toLowerCase", "filter", "template", "includes", "updateTemplatePagination", "ceil", "max", "updatePaginatedTemplates", "startIndex", "endIndex", "slice", "page", "pages", "startPage", "endPage", "i", "push", "onSearch", "onAddTemplate", "for<PERSON>ach", "item", "alert", "createTemplate", "templateName", "map", "refId", "getRefId", "fieldName", "fieldValue", "getFieldValue", "newId", "t", "index", "detail", "CRequirementID", "ID", "id", "_item", "title", "emit", "updateDetailPagination", "details", "updatePaginatedDetails", "onClose", "templateID", "confirm", "deleteTemplateById", "d", "trackByTemplateId", "selectors", "inputs", "outputs", "standalone", "features", "ɵɵNgOnChangesFeature", "ɵɵStandaloneFeature", "decls", "vars", "consts", "TemplateViewerComponent_Template", "rf", "ctx", "TemplateViewerComponent_Template_button_click_5_listener", "TemplateViewerComponent_Template_input_ngModelChange_11_listener", "TemplateViewerComponent_Template_input_input_11_listener", "TemplateViewerComponent_Template_input_keyup_enter_11_listener", "TemplateViewerComponent_Template_button_click_13_listener", "TemplateViewerComponent_button_15_Template", "TemplateViewerComponent_div_16_Template", "TemplateViewerComponent_div_18_Template", "TemplateViewerComponent_div_19_Template", "TemplateViewerComponent_tr_31_Template", "TemplateViewerComponent_tr_32_Template", "TemplateViewerComponent_div_33_Template", "TemplateViewerComponent_div_34_Template", "TemplateViewerComponent_Template_button_click_37_listener", "i1", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "i2", "ɵNgNoValidate", "DefaultValueAccessor", "CheckboxControlValueAccessor", "NgControlStatus", "NgControlStatusGroup", "RequiredValidator", "MaxLengthValidator", "NgModel", "NgForm", "i3", "NbCardComponent", "NbCardBodyComponent", "NbCardFooterComponent", "NbCardHeaderComponent", "styles"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\shared\\components\\template-viewer\\template-viewer.component.ts", "C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\shared\\components\\template-viewer\\template-viewer.component.html"], "sourcesContent": ["import { Component, EventEmitter, Input, Output, OnInit, OnChanges } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { FormsModule } from '@angular/forms';\r\nimport { NbCardModule, NbButtonModule } from '@nebular/theme';\r\n\r\n@Component({\r\n  selector: 'app-template-viewer',\r\n  templateUrl: './template-viewer.component.html',\r\n  styleUrls: ['./template-viewer.component.scss'],\r\n  standalone: true,\r\n  imports: [CommonModule, FormsModule, NbCardModule, NbButtonModule],\r\n})\r\nexport class TemplateViewerComponent implements OnInit, OnChanges {\r\n  @Input() availableData: any[] = []; // 父元件傳入的可選資料 (包含關聯主檔ID和名稱)\r\n  @Input() moduleType: string = 'Requirement'; // 模組類型，用於區分資料來源\r\n  @Output() selectTemplate = new EventEmitter<Template>();\r\n  @Output() close = new EventEmitter<void>(); // 關閉事件\r\n\r\n  // 公開Math對象供模板使用\r\n  Math = Math;\r\n\r\n  // 內部管理的模板資料\r\n  templates: Template[] = [];\r\n  templateDetails: TemplateDetail[] = [];\r\n  selectedTemplate: Template | null = null;\r\n\r\n  // 查詢功能\r\n  searchKeyword = '';\r\n  filteredTemplates: Template[] = [];\r\n\r\n  // 分頁功能 - 模板列表\r\n  templatePagination = {\r\n    currentPage: 1,\r\n    pageSize: 10,\r\n    totalItems: 0,\r\n    totalPages: 0\r\n  };\r\n  paginatedTemplates: Template[] = [];\r\n\r\n  // 分頁功能 - 模板詳情\r\n  detailPagination = {\r\n    currentPage: 1,\r\n    pageSize: 5,\r\n    totalItems: 0,\r\n    totalPages: 0\r\n  };\r\n  paginatedDetails: TemplateDetail[] = [];\r\n\r\n  // 新增模板表單\r\n  showAddForm = false;\r\n  newTemplate = {\r\n    name: '',\r\n    description: '',\r\n    selectedItems: [] as any[]\r\n  };\r\n\r\n  ngOnInit() {\r\n    this.loadTemplates();\r\n    this.updateFilteredTemplates();\r\n  }\r\n\r\n  ngOnChanges() {\r\n    this.updateFilteredTemplates();\r\n  }\r\n\r\n  // TODO: 替換為實際的API調用\r\n  loadTemplates() {\r\n    // 模擬API調用 - 載入模板列表\r\n    console.log('載入模板 API 調用:', { moduleType: this.moduleType });\r\n\r\n    this.templates = [\r\n      { TemplateID: 1, TemplateName: '需求模板A', Description: '包含基本工程項目的模板' },\r\n      { TemplateID: 2, TemplateName: '需求模板B', Description: '包含進階工程項目的模板' }\r\n    ];\r\n\r\n    // 載入對應模組類型的模板詳情\r\n    this.templateDetails = [\r\n      {\r\n        TemplateDetailID: 1,\r\n        TemplateID: 1,\r\n        RefID: 101,\r\n        ModuleType: this.moduleType,\r\n        FieldName: this.getFieldName({}),\r\n        FieldValue: '工程項目A'\r\n      },\r\n      {\r\n        TemplateDetailID: 2,\r\n        TemplateID: 1,\r\n        RefID: 102,\r\n        ModuleType: this.moduleType,\r\n        FieldName: this.getFieldName({}),\r\n        FieldValue: '工程項目B'\r\n      },\r\n      {\r\n        TemplateDetailID: 3,\r\n        TemplateID: 2,\r\n        RefID: 201,\r\n        ModuleType: this.moduleType,\r\n        FieldName: this.getFieldName({}),\r\n        FieldValue: '工程項目C'\r\n      }\r\n    ];\r\n  }\r\n\r\n  // 更新過濾後的模板列表\r\n  updateFilteredTemplates() {\r\n    if (!this.searchKeyword.trim()) {\r\n      this.filteredTemplates = [...this.templates];\r\n    } else {\r\n      const keyword = this.searchKeyword.toLowerCase();\r\n      this.filteredTemplates = this.templates.filter(template =>\r\n        template.TemplateName.toLowerCase().includes(keyword) ||\r\n        (template.Description && template.Description.toLowerCase().includes(keyword))\r\n      );\r\n    }\r\n    this.updateTemplatePagination();\r\n  }\r\n\r\n  // 更新模板分頁\r\n  updateTemplatePagination() {\r\n    this.templatePagination.totalItems = this.filteredTemplates.length;\r\n    this.templatePagination.totalPages = Math.ceil(this.templatePagination.totalItems / this.templatePagination.pageSize);\r\n\r\n    // 確保當前頁面不超過總頁數\r\n    if (this.templatePagination.currentPage > this.templatePagination.totalPages) {\r\n      this.templatePagination.currentPage = Math.max(1, this.templatePagination.totalPages);\r\n    }\r\n\r\n    this.updatePaginatedTemplates();\r\n  }\r\n\r\n  // 更新分頁後的模板列表\r\n  updatePaginatedTemplates() {\r\n    const startIndex = (this.templatePagination.currentPage - 1) * this.templatePagination.pageSize;\r\n    const endIndex = startIndex + this.templatePagination.pageSize;\r\n    this.paginatedTemplates = this.filteredTemplates.slice(startIndex, endIndex);\r\n  }\r\n\r\n  // 模板分頁導航\r\n  goToTemplatePage(page: number) {\r\n    if (page >= 1 && page <= this.templatePagination.totalPages) {\r\n      this.templatePagination.currentPage = page;\r\n      this.updatePaginatedTemplates();\r\n    }\r\n  }\r\n\r\n  // 獲取模板分頁頁碼數組\r\n  getTemplatePageNumbers(): number[] {\r\n    const pages: number[] = [];\r\n    const totalPages = this.templatePagination.totalPages;\r\n    const currentPage = this.templatePagination.currentPage;\r\n\r\n    // 顯示當前頁面前後各2頁\r\n    const startPage = Math.max(1, currentPage - 2);\r\n    const endPage = Math.min(totalPages, currentPage + 2);\r\n\r\n    for (let i = startPage; i <= endPage; i++) {\r\n      pages.push(i);\r\n    }\r\n\r\n    return pages;\r\n  }\r\n\r\n  // 搜尋模板\r\n  onSearch() {\r\n    this.updateFilteredTemplates();\r\n  }\r\n\r\n  // 清除搜尋\r\n  clearSearch() {\r\n    this.searchKeyword = '';\r\n    this.updateFilteredTemplates();\r\n  }\r\n\r\n\r\n\r\n  // 顯示新增模板表單\r\n  onAddTemplate() {\r\n    this.showAddForm = true;\r\n    this.newTemplate = {\r\n      name: '',\r\n      description: '',\r\n      selectedItems: []\r\n    };\r\n    // 重置可選資料的選擇狀態\r\n    this.availableData.forEach(item => item.selected = false);\r\n  }\r\n\r\n  // 取消新增模板\r\n  cancelAddTemplate() {\r\n    this.showAddForm = false;\r\n    this.newTemplate = {\r\n      name: '',\r\n      description: '',\r\n      selectedItems: []\r\n    };\r\n  }\r\n\r\n  // 儲存新模板\r\n  saveNewTemplate() {\r\n    if (!this.newTemplate.name.trim()) {\r\n      alert('請輸入模板名稱');\r\n      return;\r\n    }\r\n\r\n    const selectedItems = this.availableData.filter(item => item.selected);\r\n    if (selectedItems.length === 0) {\r\n      alert('請至少選擇一個項目');\r\n      return;\r\n    }\r\n\r\n    // TODO: 替換為實際的API調用\r\n    this.createTemplate(this.newTemplate.name, this.newTemplate.description, selectedItems);\r\n  }\r\n\r\n  // TODO: 替換為實際的API調用\r\n  createTemplate(name: string, description: string, selectedItems: any[]) {\r\n    // 模擬API調用 - 創建模板\r\n    console.log('創建模板 API 調用:', {\r\n      templateName: name,\r\n      description: description,\r\n      moduleType: this.moduleType,\r\n      selectedItems: selectedItems.map(item => ({\r\n        refId: this.getRefId(item),\r\n        fieldName: this.getFieldName(item),\r\n        fieldValue: this.getFieldValue(item)\r\n      }))\r\n    });\r\n\r\n    // 生成新的模板ID (實際應由後端API返回)\r\n    const newId = Math.max(...this.templates.map(t => t.TemplateID || 0), 0) + 1;\r\n\r\n    // 創建新模板\r\n    const newTemplate: Template = {\r\n      TemplateID: newId,\r\n      TemplateName: name.trim(),\r\n      Description: description.trim()\r\n    };\r\n\r\n    // 添加到模板列表\r\n    this.templates.push(newTemplate);\r\n\r\n    // 創建模板詳情 - 根據選中的項目創建詳情記錄\r\n    selectedItems.forEach((item, index) => {\r\n      const detail: TemplateDetail = {\r\n        TemplateDetailID: this.templateDetails.length + index + 1,\r\n        TemplateID: newId,\r\n        RefID: this.getRefId(item), // 關聯主檔ID\r\n        ModuleType: this.moduleType, // 模組類型，用於區分資料來源\r\n        FieldName: this.getFieldName(item), // 欄位名稱\r\n        FieldValue: this.getFieldValue(item) // 欄位值\r\n      };\r\n      this.templateDetails.push(detail);\r\n    });\r\n\r\n    // 更新過濾列表\r\n    this.updateFilteredTemplates();\r\n\r\n    // 關閉表單\r\n    this.showAddForm = false;\r\n    alert(`模板 \"${name}\" 已成功創建！包含 ${selectedItems.length} 個項目`);\r\n  }\r\n\r\n  // 獲取關聯主檔ID的輔助方法\r\n  private getRefId(item: any): number {\r\n    return item.CRequirementID || item.ID || item.id || 0;\r\n  }\r\n\r\n  // 獲取欄位名稱的輔助方法\r\n  private getFieldName(_item?: any): string {\r\n    // 根據模組類型決定欄位名稱\r\n    switch (this.moduleType) {\r\n      case 'Requirement':\r\n        return 'CRequirement';\r\n      default:\r\n        return 'name';\r\n    }\r\n  }\r\n\r\n  // 獲取欄位值的輔助方法\r\n  private getFieldValue(item: any): string {\r\n    return item.CRequirement || item.name || item.title || '';\r\n  }\r\n\r\n  // 查看模板\r\n  onSelectTemplate(template: Template) {\r\n    this.selectedTemplate = template;\r\n    this.selectTemplate.emit(template);\r\n    this.updateDetailPagination();\r\n  }\r\n\r\n  // 更新詳情分頁\r\n  updateDetailPagination() {\r\n    const details = this.currentTemplateDetails;\r\n    this.detailPagination.totalItems = details.length;\r\n    this.detailPagination.totalPages = Math.ceil(this.detailPagination.totalItems / this.detailPagination.pageSize);\r\n    this.detailPagination.currentPage = 1; // 重置到第一頁\r\n    this.updatePaginatedDetails();\r\n  }\r\n\r\n  // 更新分頁後的詳情列表\r\n  updatePaginatedDetails() {\r\n    const details = this.currentTemplateDetails;\r\n    const startIndex = (this.detailPagination.currentPage - 1) * this.detailPagination.pageSize;\r\n    const endIndex = startIndex + this.detailPagination.pageSize;\r\n    this.paginatedDetails = details.slice(startIndex, endIndex);\r\n  }\r\n\r\n  // 詳情分頁導航\r\n  goToDetailPage(page: number) {\r\n    if (page >= 1 && page <= this.detailPagination.totalPages) {\r\n      this.detailPagination.currentPage = page;\r\n      this.updatePaginatedDetails();\r\n    }\r\n  }\r\n\r\n  // 獲取詳情分頁頁碼數組\r\n  getDetailPageNumbers(): number[] {\r\n    const pages: number[] = [];\r\n    const totalPages = this.detailPagination.totalPages;\r\n    const currentPage = this.detailPagination.currentPage;\r\n\r\n    // 顯示當前頁面前後各2頁\r\n    const startPage = Math.max(1, currentPage - 2);\r\n    const endPage = Math.min(totalPages, currentPage + 2);\r\n\r\n    for (let i = startPage; i <= endPage; i++) {\r\n      pages.push(i);\r\n    }\r\n\r\n    return pages;\r\n  }\r\n\r\n  // 關閉模板查看器\r\n  onClose() {\r\n    this.close.emit();\r\n  }\r\n\r\n  // 刪除模板\r\n  onDeleteTemplate(templateID: number) {\r\n    if (confirm('確定刪除此模板？')) {\r\n      // TODO: 替換為實際的API調用\r\n      this.deleteTemplateById(templateID);\r\n    }\r\n  }\r\n\r\n  // TODO: 替換為實際的API調用\r\n  deleteTemplateById(templateID: number) {\r\n    // 模擬API調用 - 刪除模板\r\n    console.log('刪除模板 API 調用:', {\r\n      templateID: templateID,\r\n      moduleType: this.moduleType\r\n    });\r\n\r\n    // 刪除模板和相關詳情\r\n    this.templates = this.templates.filter(t => t.TemplateID !== templateID);\r\n    this.templateDetails = this.templateDetails.filter(d => d.TemplateID !== templateID);\r\n    this.updateFilteredTemplates();\r\n\r\n    // 如果當前查看的模板被刪除，關閉詳情\r\n    if (this.selectedTemplate?.TemplateID === templateID) {\r\n      this.selectedTemplate = null;\r\n    }\r\n\r\n    alert('模板已刪除');\r\n  }\r\n\r\n  /*\r\n   * API 設計說明：\r\n   *\r\n   * 1. 載入模板列表 API:\r\n   *    GET /api/templates?moduleType={moduleType}\r\n   *    返回: { templates: Template[], templateDetails: TemplateDetail[] }\r\n   *\r\n   * 2. 創建模板 API:\r\n   *    POST /api/templates\r\n   *    請求體: {\r\n   *      templateName: string,\r\n   *      description: string,\r\n   *      moduleType: string,\r\n   *      details: [{\r\n   *        refId: number,        // 關聯主檔ID\r\n   *        fieldName: string,    // 欄位名稱\r\n   *        fieldValue: string    // 欄位值\r\n   *      }]\r\n   *    }\r\n   *\r\n   * 3. 刪除模板 API:\r\n   *    DELETE /api/templates/{templateId}?moduleType={moduleType}\r\n   *\r\n   * 資料庫設計重點：\r\n   * - template 表：存放模板基本資訊 (TemplateID, TemplateName, Description)\r\n   * - templatedetail 表：存放模板詳情 (TemplateDetailID, TemplateID, RefID, ModuleType, FieldName, FieldValue)\r\n   * - ModuleType 欄位用於區分不同模組的資料 (如: 'Requirement', 'Product', 'Order' 等)\r\n   * - RefID 存放關聯主檔的ID，配合 ModuleType 可以找到對應的原始資料\r\n   */\r\n\r\n  // 關閉模板詳情\r\n  closeTemplateDetail() {\r\n    this.selectedTemplate = null;\r\n  }\r\n\r\n  // 取得當前選中模板的詳情\r\n  get currentTemplateDetails(): TemplateDetail[] {\r\n    if (!this.selectedTemplate) {\r\n      return [];\r\n    }\r\n    return this.templateDetails.filter(d => d.TemplateID === this.selectedTemplate!.TemplateID);\r\n  }\r\n\r\n  // TrackBy函數用於優化ngFor性能\r\n  trackByTemplateId(index: number, template: Template): number {\r\n    return template.TemplateID || index;\r\n  }\r\n}\r\n\r\n// DB 對應型別\r\nexport interface Template {\r\n  TemplateID?: number;\r\n  TemplateName: string;\r\n  Description?: string;\r\n}\r\nexport interface TemplateDetail {\r\n  TemplateDetailID?: number;\r\n  TemplateID: number;\r\n  RefID: number; // 關聯主檔ID\r\n  ModuleType: string;\r\n  FieldName: string;\r\n  FieldValue: string;\r\n}\r\n", "<nb-card style=\"width: 90vw; max-width: 1200px; height: 80vh;\">\r\n  <nb-card-header>\r\n    <div class=\"d-flex justify-content-between align-items-center\">\r\n      <h5 class=\"mb-0\">模板管理</h5>\r\n      <button class=\"btn btn-success btn-sm\" (click)=\"onAddTemplate()\">\r\n        <i class=\"fas fa-plus mr-1\"></i>新增模板\r\n      </button>\r\n    </div>\r\n  </nb-card-header>\r\n  <nb-card-body style=\"overflow: auto;\">\r\n    <!-- 搜尋功能 -->\r\n    <div class=\"search-container mb-3\">\r\n      <div class=\"input-group\">\r\n        <input type=\"text\" class=\"form-control\" placeholder=\"搜尋模板名稱或描述...\" [(ngModel)]=\"searchKeyword\"\r\n          (input)=\"onSearch()\" (keyup.enter)=\"onSearch()\">\r\n        <div class=\"input-group-append\">\r\n          <button class=\"btn btn-outline-secondary\" type=\"button\" (click)=\"onSearch()\">\r\n            <i class=\"fas fa-search\"></i>\r\n          </button>\r\n          <button class=\"btn btn-outline-secondary\" type=\"button\" (click)=\"clearSearch()\" *ngIf=\"searchKeyword\">\r\n            <i class=\"fas fa-times\"></i>\r\n          </button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 新增模板表單 -->\r\n    <div *ngIf=\"showAddForm\" class=\"add-template-form mb-4\">\r\n      <div class=\"form-container\">\r\n        <div class=\"form-header\">\r\n          <div class=\"form-title\">\r\n            <i class=\"fas fa-plus\"></i>\r\n            <span>新增模板</span>\r\n          </div>\r\n        </div>\r\n\r\n        <form (ngSubmit)=\"saveNewTemplate()\" class=\"form-content\">\r\n          <div class=\"input-row\">\r\n            <div class=\"input-group\">\r\n              <label class=\"input-label\">\r\n                模板名稱 <span class=\"required\">*</span>\r\n              </label>\r\n              <input type=\"text\" class=\"input-field\" [(ngModel)]=\"newTemplate.name\" name=\"templateName\"\r\n                placeholder=\"請輸入模板名稱\" required maxlength=\"50\">\r\n            </div>\r\n            <div class=\"input-group\">\r\n              <label class=\"input-label\">模板描述</label>\r\n              <input type=\"text\" class=\"input-field\" [(ngModel)]=\"newTemplate.description\" name=\"templateDescription\"\r\n                placeholder=\"請輸入模板描述（可選）\" maxlength=\"100\">\r\n            </div>\r\n          </div>\r\n\r\n          <div class=\"input-group full-width\">\r\n            <label class=\"input-label\">\r\n              選擇要加入模板的項目 <span class=\"required\">*</span>\r\n            </label>\r\n            <div class=\"items-selector\">\r\n              <div *ngIf=\"availableData.length === 0\" class=\"empty-items\">\r\n                <i class=\"fas fa-info-circle\"></i>\r\n                <span>暫無可選項目</span>\r\n              </div>\r\n              <div *ngFor=\"let item of availableData; let i = index\" class=\"item-option\">\r\n                <label class=\"item-label\">\r\n                  <input type=\"checkbox\" class=\"item-checkbox\" [(ngModel)]=\"item.selected\" name=\"item_{{i}}\">\r\n                  <div class=\"item-content\">\r\n                    <div class=\"item-title\">{{ item.CRequirement || item.name }}</div>\r\n                    <div class=\"item-desc\">{{ item.CGroupName || item.description }}</div>\r\n                  </div>\r\n                </label>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <div class=\"form-actions\">\r\n            <button type=\"button\" class=\"btn-cancel\" (click)=\"cancelAddTemplate()\">\r\n              取消\r\n            </button>\r\n            <button type=\"submit\" class=\"btn-save\">\r\n              儲存模板\r\n            </button>\r\n          </div>\r\n        </form>\r\n      </div>\r\n    </div>\r\n\r\n\r\n\r\n    <div class=\"template-list\">\r\n      <!-- 搜尋結果統計 -->\r\n      <div class=\"search-results-info mb-2\" *ngIf=\"searchKeyword\">\r\n        <small class=\"text-muted\">\r\n          找到 {{ filteredTemplates.length }} 個符合「{{ searchKeyword }}」的模板\r\n        </small>\r\n      </div>\r\n\r\n      <!-- 分頁資訊 -->\r\n      <div class=\"pagination-info mb-2\" *ngIf=\"templatePagination.totalItems > 0\">\r\n        <small class=\"text-muted\">\r\n          顯示第 {{ (templatePagination.currentPage - 1) * templatePagination.pageSize + 1 }} -\r\n          {{ Math.min(templatePagination.currentPage * templatePagination.pageSize, templatePagination.totalItems) }} 項，\r\n          共 {{ templatePagination.totalItems }} 項模板\r\n        </small>\r\n      </div>\r\n\r\n      <div class=\"table-responsive\">\r\n        <table class=\"table table-striped table-hover\">\r\n          <thead class=\"thead-light\">\r\n            <tr>\r\n              <th width=\"30%\">模板名稱</th>\r\n              <th width=\"50%\">描述</th>\r\n              <th width=\"20%\" class=\"text-center\">操作</th>\r\n            </tr>\r\n          </thead>\r\n          <tbody>\r\n            <tr *ngFor=\"let tpl of paginatedTemplates; trackBy: trackByTemplateId\">\r\n              <td>\r\n                <strong>{{ tpl.TemplateName }}</strong>\r\n              </td>\r\n              <td>\r\n                <span class=\"text-muted\">{{ tpl.Description || '無描述' }}</span>\r\n              </td>\r\n              <td class=\"text-center\">\r\n                <div class=\"btn-group btn-group-sm\" role=\"group\">\r\n                  <button class=\"btn btn-info\" (click)=\"onSelectTemplate(tpl)\" title=\"查看詳情\">\r\n                    <i class=\"fas fa-eye\"></i> 查看\r\n                  </button>\r\n                  <button class=\"btn btn-danger\" (click)=\"tpl.TemplateID && onDeleteTemplate(tpl.TemplateID)\"\r\n                    *ngIf=\"tpl.TemplateID\" title=\"刪除模板\">\r\n                    <i class=\"fas fa-trash\"></i> 刪除\r\n                  </button>\r\n                </div>\r\n              </td>\r\n            </tr>\r\n            <tr *ngIf=\"!paginatedTemplates || paginatedTemplates.length === 0\">\r\n              <td colspan=\"3\" class=\"text-center py-4\">\r\n                <div class=\"empty-state\">\r\n                  <i class=\"fas fa-folder-open fa-2x text-muted mb-2\"></i>\r\n                  <p class=\"text-muted mb-0\">\r\n                    {{ searchKeyword ? '找不到符合條件的模板' : '暫無模板' }}\r\n                  </p>\r\n                  <small class=\"text-muted\" *ngIf=\"searchKeyword\">\r\n                    請嘗試其他關鍵字或 <a href=\"javascript:void(0)\" (click)=\"clearSearch()\">清除搜尋</a>\r\n                  </small>\r\n                </div>\r\n              </td>\r\n            </tr>\r\n          </tbody>\r\n        </table>\r\n      </div>\r\n\r\n      <!-- 模板列表分頁控制器 -->\r\n      <div class=\"pagination-container mt-3\" *ngIf=\"templatePagination.totalPages > 1\">\r\n        <nav aria-label=\"模板列表分頁\">\r\n          <ul class=\"pagination pagination-sm justify-content-center mb-0\">\r\n            <!-- 上一頁 -->\r\n            <li class=\"page-item\" [class.disabled]=\"templatePagination.currentPage === 1\">\r\n              <button class=\"page-link\" (click)=\"goToTemplatePage(templatePagination.currentPage - 1)\"\r\n                [disabled]=\"templatePagination.currentPage === 1\">\r\n                <i class=\"fas fa-chevron-left\"></i>\r\n              </button>\r\n            </li>\r\n\r\n            <!-- 頁碼 -->\r\n            <li class=\"page-item\" *ngFor=\"let page of getTemplatePageNumbers()\"\r\n              [class.active]=\"page === templatePagination.currentPage\">\r\n              <button class=\"page-link\" (click)=\"goToTemplatePage(page)\">{{ page }}</button>\r\n            </li>\r\n\r\n            <!-- 下一頁 -->\r\n            <li class=\"page-item\" [class.disabled]=\"templatePagination.currentPage === templatePagination.totalPages\">\r\n              <button class=\"page-link\" (click)=\"goToTemplatePage(templatePagination.currentPage + 1)\"\r\n                [disabled]=\"templatePagination.currentPage === templatePagination.totalPages\">\r\n                <i class=\"fas fa-chevron-right\"></i>\r\n              </button>\r\n            </li>\r\n          </ul>\r\n        </nav>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 查看模板細節 -->\r\n    <div *ngIf=\"selectedTemplate\" class=\"template-detail-modal\">\r\n      <div class=\"template-detail-header d-flex justify-content-between align-items-center\">\r\n        <h6 class=\"mb-0\">\r\n          <i class=\"fas fa-file-alt mr-2\"></i>\r\n          模板細節：{{ selectedTemplate!.TemplateName }}\r\n        </h6>\r\n        <button class=\"btn btn-outline-secondary btn-sm\" (click)=\"closeTemplateDetail()\">\r\n          <i class=\"fas fa-times\"></i> 關閉\r\n        </button>\r\n      </div>\r\n\r\n      <div class=\"template-detail-content\">\r\n        <div *ngIf=\"selectedTemplate.Description\" class=\"template-description mb-3\">\r\n          <strong>描述：</strong>\r\n          <span class=\"text-muted\">{{ selectedTemplate.Description }}</span>\r\n        </div>\r\n\r\n        <div class=\"template-items\">\r\n          <div class=\"d-flex justify-content-between align-items-center mb-2\">\r\n            <h6 class=\"mb-0\">\r\n              <i class=\"fas fa-list mr-1\"></i>\r\n              模板內容 ({{ currentTemplateDetails.length }} 項)\r\n            </h6>\r\n            <small class=\"text-muted\" *ngIf=\"detailPagination.totalPages > 1\">\r\n              第 {{ detailPagination.currentPage }} / {{ detailPagination.totalPages }} 頁\r\n            </small>\r\n          </div>\r\n\r\n          <div *ngIf=\"currentTemplateDetails.length > 0; else noDetails\" class=\"detail-list\">\r\n            <div *ngFor=\"let detail of paginatedDetails; let i = index\"\r\n              class=\"detail-item d-flex align-items-center py-2 border-bottom\">\r\n              <div class=\"detail-index\">\r\n                <span class=\"badge badge-light\">{{ (detailPagination.currentPage - 1) * detailPagination.pageSize + i +\r\n                  1 }}</span>\r\n              </div>\r\n              <div class=\"detail-content flex-grow-1 ml-2\">\r\n                <div class=\"detail-field\">\r\n                  <strong>{{ detail.FieldName }}:</strong>\r\n                </div>\r\n                <div class=\"detail-value text-muted\">\r\n                  {{ detail.FieldValue }}\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 詳情分頁控制器 -->\r\n          <div class=\"detail-pagination mt-3\" *ngIf=\"detailPagination.totalPages > 1\">\r\n            <nav aria-label=\"模板詳情分頁\">\r\n              <ul class=\"pagination pagination-sm justify-content-center mb-0\">\r\n                <!-- 上一頁 -->\r\n                <li class=\"page-item\" [class.disabled]=\"detailPagination.currentPage === 1\">\r\n                  <button class=\"page-link\" (click)=\"goToDetailPage(detailPagination.currentPage - 1)\"\r\n                    [disabled]=\"detailPagination.currentPage === 1\">\r\n                    <i class=\"fas fa-chevron-left\"></i>\r\n                  </button>\r\n                </li>\r\n\r\n                <!-- 頁碼 -->\r\n                <li class=\"page-item\" *ngFor=\"let page of getDetailPageNumbers()\"\r\n                  [class.active]=\"page === detailPagination.currentPage\">\r\n                  <button class=\"page-link\" (click)=\"goToDetailPage(page)\">{{ page }}</button>\r\n                </li>\r\n\r\n                <!-- 下一頁 -->\r\n                <li class=\"page-item\" [class.disabled]=\"detailPagination.currentPage === detailPagination.totalPages\">\r\n                  <button class=\"page-link\" (click)=\"goToDetailPage(detailPagination.currentPage + 1)\"\r\n                    [disabled]=\"detailPagination.currentPage === detailPagination.totalPages\">\r\n                    <i class=\"fas fa-chevron-right\"></i>\r\n                  </button>\r\n                </li>\r\n              </ul>\r\n            </nav>\r\n          </div>\r\n\r\n          <ng-template #noDetails>\r\n            <div class=\"text-center py-3\">\r\n              <i class=\"fas fa-inbox fa-2x text-muted mb-2\"></i>\r\n              <p class=\"text-muted mb-0\">此模板暫無內容</p>\r\n            </div>\r\n          </ng-template>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </nb-card-body>\r\n  <nb-card-footer>\r\n    <div class=\"text-center\">\r\n      <button class=\"btn btn-secondary\" (click)=\"onClose()\">\r\n        <i class=\"fas fa-times mr-1\"></i>關閉\r\n      </button>\r\n    </div>\r\n  </nb-card-footer>\r\n</nb-card>"], "mappings": "AAAA,SAAoBA,YAAY,QAA0C,eAAe;AACzF,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,YAAY,EAAEC,cAAc,QAAQ,gBAAgB;;;;;;;;ICgBnDC,EAAA,CAAAC,cAAA,iBAAsG;IAA9CD,EAAA,CAAAE,UAAA,mBAAAC,mEAAA;MAAAH,EAAA,CAAAI,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAG,WAAA,EAAa;IAAA,EAAC;IAC7ET,EAAA,CAAAU,SAAA,YAA4B;IAC9BV,EAAA,CAAAW,YAAA,EAAS;;;;;IAoCLX,EAAA,CAAAC,cAAA,cAA4D;IAC1DD,EAAA,CAAAU,SAAA,YAAkC;IAClCV,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAY,MAAA,2CAAM;IACdZ,EADc,CAAAW,YAAA,EAAO,EACf;;;;;;IAGFX,EAFJ,CAAAC,cAAA,cAA2E,gBAC/C,gBACmE;IAA9CD,EAAA,CAAAa,gBAAA,2BAAAC,8EAAAC,MAAA;MAAA,MAAAC,OAAA,GAAAhB,EAAA,CAAAI,aAAA,CAAAa,GAAA,EAAAC,SAAA;MAAAlB,EAAA,CAAAmB,kBAAA,CAAAH,OAAA,CAAAI,QAAA,EAAAL,MAAA,MAAAC,OAAA,CAAAI,QAAA,GAAAL,MAAA;MAAA,OAAAf,EAAA,CAAAQ,WAAA,CAAAO,MAAA;IAAA,EAA2B;IAAxEf,EAAA,CAAAW,YAAA,EAA2F;IAEzFX,EADF,CAAAC,cAAA,cAA0B,cACA;IAAAD,EAAA,CAAAY,MAAA,GAAoC;IAAAZ,EAAA,CAAAW,YAAA,EAAM;IAClEX,EAAA,CAAAC,cAAA,cAAuB;IAAAD,EAAA,CAAAY,MAAA,GAAyC;IAGtEZ,EAHsE,CAAAW,YAAA,EAAM,EAClE,EACA,EACJ;;;;;IANuEX,EAAA,CAAAqB,SAAA,GAAiB;IAAjBrB,EAAA,CAAAsB,sBAAA,kBAAAC,IAAA,KAAiB;IAA7CvB,EAAA,CAAAwB,gBAAA,YAAAR,OAAA,CAAAI,QAAA,CAA2B;IAE9CpB,EAAA,CAAAqB,SAAA,GAAoC;IAApCrB,EAAA,CAAAyB,iBAAA,CAAAT,OAAA,CAAAU,YAAA,IAAAV,OAAA,CAAAW,IAAA,CAAoC;IACrC3B,EAAA,CAAAqB,SAAA,GAAyC;IAAzCrB,EAAA,CAAAyB,iBAAA,CAAAT,OAAA,CAAAY,UAAA,IAAAZ,OAAA,CAAAa,WAAA,CAAyC;;;;;;IApC1E7B,EAHN,CAAAC,cAAA,cAAwD,cAC1B,cACD,cACC;IACtBD,EAAA,CAAAU,SAAA,YAA2B;IAC3BV,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAY,MAAA,+BAAI;IAEdZ,EAFc,CAAAW,YAAA,EAAO,EACb,EACF;IAENX,EAAA,CAAAC,cAAA,eAA0D;IAApDD,EAAA,CAAAE,UAAA,sBAAA4B,iEAAA;MAAA9B,EAAA,CAAAI,aAAA,CAAA2B,GAAA;MAAA,MAAAzB,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAYF,MAAA,CAAA0B,eAAA,EAAiB;IAAA,EAAC;IAG9BhC,EAFJ,CAAAC,cAAA,cAAuB,aACI,iBACI;IACzBD,EAAA,CAAAY,MAAA,kCAAK;IAAAZ,EAAA,CAAAC,cAAA,gBAAuB;IAAAD,EAAA,CAAAY,MAAA,SAAC;IAC/BZ,EAD+B,CAAAW,YAAA,EAAO,EAC9B;IACRX,EAAA,CAAAC,cAAA,iBACgD;IADTD,EAAA,CAAAa,gBAAA,2BAAAoB,wEAAAlB,MAAA;MAAAf,EAAA,CAAAI,aAAA,CAAA2B,GAAA;MAAA,MAAAzB,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAAP,EAAA,CAAAmB,kBAAA,CAAAb,MAAA,CAAA4B,WAAA,CAAAP,IAAA,EAAAZ,MAAA,MAAAT,MAAA,CAAA4B,WAAA,CAAAP,IAAA,GAAAZ,MAAA;MAAA,OAAAf,EAAA,CAAAQ,WAAA,CAAAO,MAAA;IAAA,EAA8B;IAEvEf,EAFE,CAAAW,YAAA,EACgD,EAC5C;IAEJX,EADF,CAAAC,cAAA,cAAyB,iBACI;IAAAD,EAAA,CAAAY,MAAA,gCAAI;IAAAZ,EAAA,CAAAW,YAAA,EAAQ;IACvCX,EAAA,CAAAC,cAAA,iBAC4C;IADLD,EAAA,CAAAa,gBAAA,2BAAAsB,wEAAApB,MAAA;MAAAf,EAAA,CAAAI,aAAA,CAAA2B,GAAA;MAAA,MAAAzB,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAAP,EAAA,CAAAmB,kBAAA,CAAAb,MAAA,CAAA4B,WAAA,CAAAL,WAAA,EAAAd,MAAA,MAAAT,MAAA,CAAA4B,WAAA,CAAAL,WAAA,GAAAd,MAAA;MAAA,OAAAf,EAAA,CAAAQ,WAAA,CAAAO,MAAA;IAAA,EAAqC;IAGhFf,EAHI,CAAAW,YAAA,EAC4C,EACxC,EACF;IAGJX,EADF,CAAAC,cAAA,eAAoC,iBACP;IACzBD,EAAA,CAAAY,MAAA,sEAAW;IAAAZ,EAAA,CAAAC,cAAA,gBAAuB;IAAAD,EAAA,CAAAY,MAAA,SAAC;IACrCZ,EADqC,CAAAW,YAAA,EAAO,EACpC;IACRX,EAAA,CAAAC,cAAA,eAA4B;IAK1BD,EAJA,CAAAoC,UAAA,KAAAC,8CAAA,kBAA4D,KAAAC,8CAAA,kBAIe;IAU/EtC,EADE,CAAAW,YAAA,EAAM,EACF;IAGJX,EADF,CAAAC,cAAA,eAA0B,kBAC+C;IAA9BD,EAAA,CAAAE,UAAA,mBAAAqC,iEAAA;MAAAvC,EAAA,CAAAI,aAAA,CAAA2B,GAAA;MAAA,MAAAzB,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAkC,iBAAA,EAAmB;IAAA,EAAC;IACpExC,EAAA,CAAAY,MAAA,sBACF;IAAAZ,EAAA,CAAAW,YAAA,EAAS;IACTX,EAAA,CAAAC,cAAA,kBAAuC;IACrCD,EAAA,CAAAY,MAAA,kCACF;IAIRZ,EAJQ,CAAAW,YAAA,EAAS,EACL,EACD,EACH,EACF;;;;IAzC2CX,EAAA,CAAAqB,SAAA,IAA8B;IAA9BrB,EAAA,CAAAwB,gBAAA,YAAAlB,MAAA,CAAA4B,WAAA,CAAAP,IAAA,CAA8B;IAK9B3B,EAAA,CAAAqB,SAAA,GAAqC;IAArCrB,EAAA,CAAAwB,gBAAA,YAAAlB,MAAA,CAAA4B,WAAA,CAAAL,WAAA,CAAqC;IAUtE7B,EAAA,CAAAqB,SAAA,GAAgC;IAAhCrB,EAAA,CAAAyC,UAAA,SAAAnC,MAAA,CAAAoC,aAAA,CAAAC,MAAA,OAAgC;IAIhB3C,EAAA,CAAAqB,SAAA,EAAkB;IAAlBrB,EAAA,CAAAyC,UAAA,YAAAnC,MAAA,CAAAoC,aAAA,CAAkB;;;;;IA6B9C1C,EADF,CAAAC,cAAA,cAA4D,gBAChC;IACxBD,EAAA,CAAAY,MAAA,GACF;IACFZ,EADE,CAAAW,YAAA,EAAQ,EACJ;;;;IAFFX,EAAA,CAAAqB,SAAA,GACF;IADErB,EAAA,CAAA4C,kBAAA,mBAAAtC,MAAA,CAAAuC,iBAAA,CAAAF,MAAA,+BAAArC,MAAA,CAAAwC,aAAA,8BACF;;;;;IAKA9C,EADF,CAAAC,cAAA,cAA4E,gBAChD;IACxBD,EAAA,CAAAY,MAAA,GAGF;IACFZ,EADE,CAAAW,YAAA,EAAQ,EACJ;;;;IAJFX,EAAA,CAAAqB,SAAA,GAGF;IAHErB,EAAA,CAAA+C,kBAAA,0BAAAzC,MAAA,CAAA0C,kBAAA,CAAAC,WAAA,QAAA3C,MAAA,CAAA0C,kBAAA,CAAAE,QAAA,aAAA5C,MAAA,CAAA6C,IAAA,CAAAC,GAAA,CAAA9C,MAAA,CAAA0C,kBAAA,CAAAC,WAAA,GAAA3C,MAAA,CAAA0C,kBAAA,CAAAE,QAAA,EAAA5C,MAAA,CAAA0C,kBAAA,CAAAK,UAAA,4BAAA/C,MAAA,CAAA0C,kBAAA,CAAAK,UAAA,yBAGF;;;;;;IAyBUrD,EAAA,CAAAC,cAAA,iBACsC;IADPD,EAAA,CAAAE,UAAA,mBAAAoD,yEAAA;MAAAtD,EAAA,CAAAI,aAAA,CAAAmD,GAAA;MAAA,MAAAC,MAAA,GAAAxD,EAAA,CAAAO,aAAA,GAAAW,SAAA;MAAA,MAAAZ,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAAgD,MAAA,CAAAC,UAAA,IAA2BnD,MAAA,CAAAoD,gBAAA,CAAAF,MAAA,CAAAC,UAAA,CAAgC;IAAA,EAAC;IAEzFzD,EAAA,CAAAU,SAAA,YAA4B;IAACV,EAAA,CAAAY,MAAA,qBAC/B;IAAAZ,EAAA,CAAAW,YAAA,EAAS;;;;;;IAbXX,EAFJ,CAAAC,cAAA,SAAuE,SACjE,aACM;IAAAD,EAAA,CAAAY,MAAA,GAAsB;IAChCZ,EADgC,CAAAW,YAAA,EAAS,EACpC;IAEHX,EADF,CAAAC,cAAA,SAAI,eACuB;IAAAD,EAAA,CAAAY,MAAA,GAA8B;IACzDZ,EADyD,CAAAW,YAAA,EAAO,EAC3D;IAGDX,EAFJ,CAAAC,cAAA,aAAwB,cAC2B,iBAC2B;IAA7CD,EAAA,CAAAE,UAAA,mBAAAyD,+DAAA;MAAA,MAAAH,MAAA,GAAAxD,EAAA,CAAAI,aAAA,CAAAwD,GAAA,EAAA1C,SAAA;MAAA,MAAAZ,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAuD,gBAAA,CAAAL,MAAA,CAAqB;IAAA,EAAC;IAC1DxD,EAAA,CAAAU,SAAA,aAA0B;IAACV,EAAA,CAAAY,MAAA,sBAC7B;IAAAZ,EAAA,CAAAW,YAAA,EAAS;IACTX,EAAA,CAAAoC,UAAA,KAAA0B,gDAAA,qBACsC;IAK5C9D,EAFI,CAAAW,YAAA,EAAM,EACH,EACF;;;;IAhBOX,EAAA,CAAAqB,SAAA,GAAsB;IAAtBrB,EAAA,CAAAyB,iBAAA,CAAA+B,MAAA,CAAAO,YAAA,CAAsB;IAGL/D,EAAA,CAAAqB,SAAA,GAA8B;IAA9BrB,EAAA,CAAAyB,iBAAA,CAAA+B,MAAA,CAAAQ,WAAA,yBAA8B;IAQlDhE,EAAA,CAAAqB,SAAA,GAAoB;IAApBrB,EAAA,CAAAyC,UAAA,SAAAe,MAAA,CAAAC,UAAA,CAAoB;;;;;;IAavBzD,EAAA,CAAAC,cAAA,gBAAgD;IAC9CD,EAAA,CAAAY,MAAA,+DAAU;IAAAZ,EAAA,CAAAC,cAAA,YAAqD;IAAxBD,EAAA,CAAAE,UAAA,mBAAA+D,kEAAA;MAAAjE,EAAA,CAAAI,aAAA,CAAA8D,IAAA;MAAA,MAAA5D,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAG,WAAA,EAAa;IAAA,EAAC;IAACT,EAAA,CAAAY,MAAA,+BAAI;IACrEZ,EADqE,CAAAW,YAAA,EAAI,EACjE;;;;;IAPVX,EAFJ,CAAAC,cAAA,SAAmE,aACxB,cACd;IACvBD,EAAA,CAAAU,SAAA,YAAwD;IACxDV,EAAA,CAAAC,cAAA,YAA2B;IACzBD,EAAA,CAAAY,MAAA,GACF;IAAAZ,EAAA,CAAAW,YAAA,EAAI;IACJX,EAAA,CAAAoC,UAAA,IAAA+B,8CAAA,oBAAgD;IAKtDnE,EAFI,CAAAW,YAAA,EAAM,EACH,EACF;;;;IAPGX,EAAA,CAAAqB,SAAA,GACF;IADErB,EAAA,CAAAoE,kBAAA,MAAA9D,MAAA,CAAAwC,aAAA,oGACF;IAC2B9C,EAAA,CAAAqB,SAAA,EAAmB;IAAnBrB,EAAA,CAAAyC,UAAA,SAAAnC,MAAA,CAAAwC,aAAA,CAAmB;;;;;;IAyBlD9C,EAFF,CAAAC,cAAA,aAC2D,iBACE;IAAjCD,EAAA,CAAAE,UAAA,mBAAAmE,qEAAA;MAAA,MAAAC,QAAA,GAAAtE,EAAA,CAAAI,aAAA,CAAAmE,IAAA,EAAArD,SAAA;MAAA,MAAAZ,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAkE,gBAAA,CAAAF,QAAA,CAAsB;IAAA,EAAC;IAACtE,EAAA,CAAAY,MAAA,GAAU;IACvEZ,EADuE,CAAAW,YAAA,EAAS,EAC3E;;;;;IAFHX,EAAA,CAAAyE,WAAA,WAAAH,QAAA,KAAAhE,MAAA,CAAA0C,kBAAA,CAAAC,WAAA,CAAwD;IACGjD,EAAA,CAAAqB,SAAA,GAAU;IAAVrB,EAAA,CAAAyB,iBAAA,CAAA6C,QAAA,CAAU;;;;;;IATrEtE,EALR,CAAAC,cAAA,cAAiF,cACtD,aAC0C,aAEe,iBAExB;IAD1BD,EAAA,CAAAE,UAAA,mBAAAwE,gEAAA;MAAA1E,EAAA,CAAAI,aAAA,CAAAuE,IAAA;MAAA,MAAArE,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAkE,gBAAA,CAAAlE,MAAA,CAAA0C,kBAAA,CAAAC,WAAA,GAAkD,CAAC,CAAC;IAAA,EAAC;IAEtFjD,EAAA,CAAAU,SAAA,YAAmC;IAEvCV,EADE,CAAAW,YAAA,EAAS,EACN;IAGLX,EAAA,CAAAoC,UAAA,IAAAwC,4CAAA,iBAC2D;IAMzD5E,EADF,CAAAC,cAAA,aAA0G,iBAExB;IADtDD,EAAA,CAAAE,UAAA,mBAAA2E,gEAAA;MAAA7E,EAAA,CAAAI,aAAA,CAAAuE,IAAA;MAAA,MAAArE,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAkE,gBAAA,CAAAlE,MAAA,CAAA0C,kBAAA,CAAAC,WAAA,GAAkD,CAAC,CAAC;IAAA,EAAC;IAEtFjD,EAAA,CAAAU,SAAA,YAAoC;IAK9CV,EAJQ,CAAAW,YAAA,EAAS,EACN,EACF,EACD,EACF;;;;IAtBsBX,EAAA,CAAAqB,SAAA,GAAuD;IAAvDrB,EAAA,CAAAyE,WAAA,aAAAnE,MAAA,CAAA0C,kBAAA,CAAAC,WAAA,OAAuD;IAEzEjD,EAAA,CAAAqB,SAAA,EAAiD;IAAjDrB,EAAA,CAAAyC,UAAA,aAAAnC,MAAA,CAAA0C,kBAAA,CAAAC,WAAA,OAAiD;IAMdjD,EAAA,CAAAqB,SAAA,GAA2B;IAA3BrB,EAAA,CAAAyC,UAAA,YAAAnC,MAAA,CAAAwE,sBAAA,GAA2B;IAM5C9E,EAAA,CAAAqB,SAAA,EAAmF;IAAnFrB,EAAA,CAAAyE,WAAA,aAAAnE,MAAA,CAAA0C,kBAAA,CAAAC,WAAA,KAAA3C,MAAA,CAAA0C,kBAAA,CAAA+B,UAAA,CAAmF;IAErG/E,EAAA,CAAAqB,SAAA,EAA6E;IAA7ErB,EAAA,CAAAyC,UAAA,aAAAnC,MAAA,CAAA0C,kBAAA,CAAAC,WAAA,KAAA3C,MAAA,CAAA0C,kBAAA,CAAA+B,UAAA,CAA6E;;;;;IAuBnF/E,EADF,CAAAC,cAAA,cAA4E,aAClE;IAAAD,EAAA,CAAAY,MAAA,yBAAG;IAAAZ,EAAA,CAAAW,YAAA,EAAS;IACpBX,EAAA,CAAAC,cAAA,eAAyB;IAAAD,EAAA,CAAAY,MAAA,GAAkC;IAC7DZ,EAD6D,CAAAW,YAAA,EAAO,EAC9D;;;;IADqBX,EAAA,CAAAqB,SAAA,GAAkC;IAAlCrB,EAAA,CAAAyB,iBAAA,CAAAnB,MAAA,CAAA0E,gBAAA,CAAAhB,WAAA,CAAkC;;;;;IASzDhE,EAAA,CAAAC,cAAA,gBAAkE;IAChED,EAAA,CAAAY,MAAA,GACF;IAAAZ,EAAA,CAAAW,YAAA,EAAQ;;;;IADNX,EAAA,CAAAqB,SAAA,EACF;IADErB,EAAA,CAAA4C,kBAAA,aAAAtC,MAAA,CAAA2E,gBAAA,CAAAhC,WAAA,SAAA3C,MAAA,CAAA2E,gBAAA,CAAAF,UAAA,aACF;;;;;IAOI/E,EAHJ,CAAAC,cAAA,cACmE,cACvC,eACQ;IAAAD,EAAA,CAAAY,MAAA,GAC1B;IACRZ,EADQ,CAAAW,YAAA,EAAO,EACT;IAGFX,EAFJ,CAAAC,cAAA,cAA6C,eACjB,aAChB;IAAAD,EAAA,CAAAY,MAAA,GAAuB;IACjCZ,EADiC,CAAAW,YAAA,EAAS,EACpC;IACNX,EAAA,CAAAC,cAAA,eAAqC;IACnCD,EAAA,CAAAY,MAAA,GACF;IAEJZ,EAFI,CAAAW,YAAA,EAAM,EACF,EACF;;;;;;IAX8BX,EAAA,CAAAqB,SAAA,GAC1B;IAD0BrB,EAAA,CAAAyB,iBAAA,EAAAnB,MAAA,CAAA2E,gBAAA,CAAAhC,WAAA,QAAA3C,MAAA,CAAA2E,gBAAA,CAAA/B,QAAA,GAAAgC,KAAA,KAC1B;IAIIlF,EAAA,CAAAqB,SAAA,GAAuB;IAAvBrB,EAAA,CAAAoE,kBAAA,KAAAe,UAAA,CAAAC,SAAA,MAAuB;IAG/BpF,EAAA,CAAAqB,SAAA,GACF;IADErB,EAAA,CAAAoE,kBAAA,MAAAe,UAAA,CAAAE,UAAA,MACF;;;;;IAbNrF,EAAA,CAAAC,cAAA,cAAmF;IACjFD,EAAA,CAAAoC,UAAA,IAAAkD,oDAAA,mBACmE;IAcrEtF,EAAA,CAAAW,YAAA,EAAM;;;;IAfoBX,EAAA,CAAAqB,SAAA,EAAqB;IAArBrB,EAAA,CAAAyC,UAAA,YAAAnC,MAAA,CAAAiF,gBAAA,CAAqB;;;;;;IAgCvCvF,EAFF,CAAAC,cAAA,aACyD,iBACE;IAA/BD,EAAA,CAAAE,UAAA,mBAAAsF,4EAAA;MAAA,MAAAC,QAAA,GAAAzF,EAAA,CAAAI,aAAA,CAAAsF,IAAA,EAAAxE,SAAA;MAAA,MAAAZ,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAqF,cAAA,CAAAF,QAAA,CAAoB;IAAA,EAAC;IAACzF,EAAA,CAAAY,MAAA,GAAU;IACrEZ,EADqE,CAAAW,YAAA,EAAS,EACzE;;;;;IAFHX,EAAA,CAAAyE,WAAA,WAAAgB,QAAA,KAAAnF,MAAA,CAAA2E,gBAAA,CAAAhC,WAAA,CAAsD;IACGjD,EAAA,CAAAqB,SAAA,GAAU;IAAVrB,EAAA,CAAAyB,iBAAA,CAAAgE,QAAA,CAAU;;;;;;IATnEzF,EALR,CAAAC,cAAA,eAA4E,eACjD,aAC0C,aAEa,iBAExB;IADxBD,EAAA,CAAAE,UAAA,mBAAA0F,uEAAA;MAAA5F,EAAA,CAAAI,aAAA,CAAAyF,IAAA;MAAA,MAAAvF,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAqF,cAAA,CAAArF,MAAA,CAAA2E,gBAAA,CAAAhC,WAAA,GAA8C,CAAC,CAAC;IAAA,EAAC;IAElFjD,EAAA,CAAAU,SAAA,YAAmC;IAEvCV,EADE,CAAAW,YAAA,EAAS,EACN;IAGLX,EAAA,CAAAoC,UAAA,IAAA0D,mDAAA,iBACyD;IAMvD9F,EADF,CAAAC,cAAA,aAAsG,iBAExB;IADlDD,EAAA,CAAAE,UAAA,mBAAA6F,uEAAA;MAAA/F,EAAA,CAAAI,aAAA,CAAAyF,IAAA;MAAA,MAAAvF,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAqF,cAAA,CAAArF,MAAA,CAAA2E,gBAAA,CAAAhC,WAAA,GAA8C,CAAC,CAAC;IAAA,EAAC;IAElFjD,EAAA,CAAAU,SAAA,YAAoC;IAK9CV,EAJQ,CAAAW,YAAA,EAAS,EACN,EACF,EACD,EACF;;;;IAtBsBX,EAAA,CAAAqB,SAAA,GAAqD;IAArDrB,EAAA,CAAAyE,WAAA,aAAAnE,MAAA,CAAA2E,gBAAA,CAAAhC,WAAA,OAAqD;IAEvEjD,EAAA,CAAAqB,SAAA,EAA+C;IAA/CrB,EAAA,CAAAyC,UAAA,aAAAnC,MAAA,CAAA2E,gBAAA,CAAAhC,WAAA,OAA+C;IAMZjD,EAAA,CAAAqB,SAAA,GAAyB;IAAzBrB,EAAA,CAAAyC,UAAA,YAAAnC,MAAA,CAAA0F,oBAAA,GAAyB;IAM1ChG,EAAA,CAAAqB,SAAA,EAA+E;IAA/ErB,EAAA,CAAAyE,WAAA,aAAAnE,MAAA,CAAA2E,gBAAA,CAAAhC,WAAA,KAAA3C,MAAA,CAAA2E,gBAAA,CAAAF,UAAA,CAA+E;IAEjG/E,EAAA,CAAAqB,SAAA,EAAyE;IAAzErB,EAAA,CAAAyC,UAAA,aAAAnC,MAAA,CAAA2E,gBAAA,CAAAhC,WAAA,KAAA3C,MAAA,CAAA2E,gBAAA,CAAAF,UAAA,CAAyE;;;;;IASjF/E,EAAA,CAAAC,cAAA,eAA8B;IAC5BD,EAAA,CAAAU,SAAA,aAAkD;IAClDV,EAAA,CAAAC,cAAA,YAA2B;IAAAD,EAAA,CAAAY,MAAA,iDAAO;IACpCZ,EADoC,CAAAW,YAAA,EAAI,EAClC;;;;;;IA7EVX,EAFJ,CAAAC,cAAA,cAA4D,cAC4B,YACnE;IACfD,EAAA,CAAAU,SAAA,YAAoC;IACpCV,EAAA,CAAAY,MAAA,GACF;IAAAZ,EAAA,CAAAW,YAAA,EAAK;IACLX,EAAA,CAAAC,cAAA,iBAAiF;IAAhCD,EAAA,CAAAE,UAAA,mBAAA+F,gEAAA;MAAAjG,EAAA,CAAAI,aAAA,CAAA8F,IAAA;MAAA,MAAA5F,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAA6F,mBAAA,EAAqB;IAAA,EAAC;IAC9EnG,EAAA,CAAAU,SAAA,YAA4B;IAACV,EAAA,CAAAY,MAAA,qBAC/B;IACFZ,EADE,CAAAW,YAAA,EAAS,EACL;IAENX,EAAA,CAAAC,cAAA,cAAqC;IACnCD,EAAA,CAAAoC,UAAA,IAAAgE,6CAAA,kBAA4E;IAOxEpG,EAFJ,CAAAC,cAAA,eAA4B,eAC0C,aACjD;IACfD,EAAA,CAAAU,SAAA,aAAgC;IAChCV,EAAA,CAAAY,MAAA,IACF;IAAAZ,EAAA,CAAAW,YAAA,EAAK;IACLX,EAAA,CAAAoC,UAAA,KAAAiE,gDAAA,oBAAkE;IAGpErG,EAAA,CAAAW,YAAA,EAAM;IAiDNX,EA/CA,CAAAoC,UAAA,KAAAkE,8CAAA,kBAAmF,KAAAC,8CAAA,mBAmBP,KAAAC,sDAAA,gCAAAxG,EAAA,CAAAyG,sBAAA,CA4BpD;IAQ9BzG,EAFI,CAAAW,YAAA,EAAM,EACF,EACF;;;;;IA/EAX,EAAA,CAAAqB,SAAA,GACF;IADErB,EAAA,CAAAoE,kBAAA,oCAAA9D,MAAA,CAAA0E,gBAAA,CAAAjB,YAAA,MACF;IAOM/D,EAAA,CAAAqB,SAAA,GAAkC;IAAlCrB,EAAA,CAAAyC,UAAA,SAAAnC,MAAA,CAAA0E,gBAAA,CAAAhB,WAAA,CAAkC;IASlChE,EAAA,CAAAqB,SAAA,GACF;IADErB,EAAA,CAAAoE,kBAAA,gCAAA9D,MAAA,CAAAoG,sBAAA,CAAA/D,MAAA,cACF;IAC2B3C,EAAA,CAAAqB,SAAA,EAAqC;IAArCrB,EAAA,CAAAyC,UAAA,SAAAnC,MAAA,CAAA2E,gBAAA,CAAAF,UAAA,KAAqC;IAK5D/E,EAAA,CAAAqB,SAAA,EAAyC;IAAArB,EAAzC,CAAAyC,UAAA,SAAAnC,MAAA,CAAAoG,sBAAA,CAAA/D,MAAA,KAAyC,aAAAgE,aAAA,CAAc;IAmBxB3G,EAAA,CAAAqB,SAAA,EAAqC;IAArCrB,EAAA,CAAAyC,UAAA,SAAAnC,MAAA,CAAA2E,gBAAA,CAAAF,UAAA,KAAqC;;;ADxNpF,OAAM,MAAO6B,uBAAuB;EAPpCC,YAAA;IAQW,KAAAnE,aAAa,GAAU,EAAE,CAAC,CAAC;IAC3B,KAAAoE,UAAU,GAAW,aAAa,CAAC,CAAC;IACnC,KAAAC,cAAc,GAAG,IAAIpH,YAAY,EAAY;IAC7C,KAAAqH,KAAK,GAAG,IAAIrH,YAAY,EAAQ,CAAC,CAAC;IAE5C;IACA,KAAAwD,IAAI,GAAGA,IAAI;IAEX;IACA,KAAA8D,SAAS,GAAe,EAAE;IAC1B,KAAAC,eAAe,GAAqB,EAAE;IACtC,KAAAlC,gBAAgB,GAAoB,IAAI;IAExC;IACA,KAAAlC,aAAa,GAAG,EAAE;IAClB,KAAAD,iBAAiB,GAAe,EAAE;IAElC;IACA,KAAAG,kBAAkB,GAAG;MACnBC,WAAW,EAAE,CAAC;MACdC,QAAQ,EAAE,EAAE;MACZG,UAAU,EAAE,CAAC;MACb0B,UAAU,EAAE;KACb;IACD,KAAAoC,kBAAkB,GAAe,EAAE;IAEnC;IACA,KAAAlC,gBAAgB,GAAG;MACjBhC,WAAW,EAAE,CAAC;MACdC,QAAQ,EAAE,CAAC;MACXG,UAAU,EAAE,CAAC;MACb0B,UAAU,EAAE;KACb;IACD,KAAAQ,gBAAgB,GAAqB,EAAE;IAEvC;IACA,KAAA6B,WAAW,GAAG,KAAK;IACnB,KAAAlF,WAAW,GAAG;MACZP,IAAI,EAAE,EAAE;MACRE,WAAW,EAAE,EAAE;MACfwF,aAAa,EAAE;KAChB;;EAEDC,QAAQA,CAAA;IACN,IAAI,CAACC,aAAa,EAAE;IACpB,IAAI,CAACC,uBAAuB,EAAE;EAChC;EAEAC,WAAWA,CAAA;IACT,IAAI,CAACD,uBAAuB,EAAE;EAChC;EAEA;EACAD,aAAaA,CAAA;IACX;IACAG,OAAO,CAACC,GAAG,CAAC,cAAc,EAAE;MAAEb,UAAU,EAAE,IAAI,CAACA;IAAU,CAAE,CAAC;IAE5D,IAAI,CAACG,SAAS,GAAG,CACf;MAAExD,UAAU,EAAE,CAAC;MAAEM,YAAY,EAAE,OAAO;MAAEC,WAAW,EAAE;IAAa,CAAE,EACpE;MAAEP,UAAU,EAAE,CAAC;MAAEM,YAAY,EAAE,OAAO;MAAEC,WAAW,EAAE;IAAa,CAAE,CACrE;IAED;IACA,IAAI,CAACkD,eAAe,GAAG,CACrB;MACEU,gBAAgB,EAAE,CAAC;MACnBnE,UAAU,EAAE,CAAC;MACboE,KAAK,EAAE,GAAG;MACVC,UAAU,EAAE,IAAI,CAAChB,UAAU;MAC3B1B,SAAS,EAAE,IAAI,CAAC2C,YAAY,CAAC,EAAE,CAAC;MAChC1C,UAAU,EAAE;KACb,EACD;MACEuC,gBAAgB,EAAE,CAAC;MACnBnE,UAAU,EAAE,CAAC;MACboE,KAAK,EAAE,GAAG;MACVC,UAAU,EAAE,IAAI,CAAChB,UAAU;MAC3B1B,SAAS,EAAE,IAAI,CAAC2C,YAAY,CAAC,EAAE,CAAC;MAChC1C,UAAU,EAAE;KACb,EACD;MACEuC,gBAAgB,EAAE,CAAC;MACnBnE,UAAU,EAAE,CAAC;MACboE,KAAK,EAAE,GAAG;MACVC,UAAU,EAAE,IAAI,CAAChB,UAAU;MAC3B1B,SAAS,EAAE,IAAI,CAAC2C,YAAY,CAAC,EAAE,CAAC;MAChC1C,UAAU,EAAE;KACb,CACF;EACH;EAEA;EACAmC,uBAAuBA,CAAA;IACrB,IAAI,CAAC,IAAI,CAAC1E,aAAa,CAACkF,IAAI,EAAE,EAAE;MAC9B,IAAI,CAACnF,iBAAiB,GAAG,CAAC,GAAG,IAAI,CAACoE,SAAS,CAAC;IAC9C,CAAC,MAAM;MACL,MAAMgB,OAAO,GAAG,IAAI,CAACnF,aAAa,CAACoF,WAAW,EAAE;MAChD,IAAI,CAACrF,iBAAiB,GAAG,IAAI,CAACoE,SAAS,CAACkB,MAAM,CAACC,QAAQ,IACrDA,QAAQ,CAACrE,YAAY,CAACmE,WAAW,EAAE,CAACG,QAAQ,CAACJ,OAAO,CAAC,IACpDG,QAAQ,CAACpE,WAAW,IAAIoE,QAAQ,CAACpE,WAAW,CAACkE,WAAW,EAAE,CAACG,QAAQ,CAACJ,OAAO,CAAE,CAC/E;IACH;IACA,IAAI,CAACK,wBAAwB,EAAE;EACjC;EAEA;EACAA,wBAAwBA,CAAA;IACtB,IAAI,CAACtF,kBAAkB,CAACK,UAAU,GAAG,IAAI,CAACR,iBAAiB,CAACF,MAAM;IAClE,IAAI,CAACK,kBAAkB,CAAC+B,UAAU,GAAG5B,IAAI,CAACoF,IAAI,CAAC,IAAI,CAACvF,kBAAkB,CAACK,UAAU,GAAG,IAAI,CAACL,kBAAkB,CAACE,QAAQ,CAAC;IAErH;IACA,IAAI,IAAI,CAACF,kBAAkB,CAACC,WAAW,GAAG,IAAI,CAACD,kBAAkB,CAAC+B,UAAU,EAAE;MAC5E,IAAI,CAAC/B,kBAAkB,CAACC,WAAW,GAAGE,IAAI,CAACqF,GAAG,CAAC,CAAC,EAAE,IAAI,CAACxF,kBAAkB,CAAC+B,UAAU,CAAC;IACvF;IAEA,IAAI,CAAC0D,wBAAwB,EAAE;EACjC;EAEA;EACAA,wBAAwBA,CAAA;IACtB,MAAMC,UAAU,GAAG,CAAC,IAAI,CAAC1F,kBAAkB,CAACC,WAAW,GAAG,CAAC,IAAI,IAAI,CAACD,kBAAkB,CAACE,QAAQ;IAC/F,MAAMyF,QAAQ,GAAGD,UAAU,GAAG,IAAI,CAAC1F,kBAAkB,CAACE,QAAQ;IAC9D,IAAI,CAACiE,kBAAkB,GAAG,IAAI,CAACtE,iBAAiB,CAAC+F,KAAK,CAACF,UAAU,EAAEC,QAAQ,CAAC;EAC9E;EAEA;EACAnE,gBAAgBA,CAACqE,IAAY;IAC3B,IAAIA,IAAI,IAAI,CAAC,IAAIA,IAAI,IAAI,IAAI,CAAC7F,kBAAkB,CAAC+B,UAAU,EAAE;MAC3D,IAAI,CAAC/B,kBAAkB,CAACC,WAAW,GAAG4F,IAAI;MAC1C,IAAI,CAACJ,wBAAwB,EAAE;IACjC;EACF;EAEA;EACA3D,sBAAsBA,CAAA;IACpB,MAAMgE,KAAK,GAAa,EAAE;IAC1B,MAAM/D,UAAU,GAAG,IAAI,CAAC/B,kBAAkB,CAAC+B,UAAU;IACrD,MAAM9B,WAAW,GAAG,IAAI,CAACD,kBAAkB,CAACC,WAAW;IAEvD;IACA,MAAM8F,SAAS,GAAG5F,IAAI,CAACqF,GAAG,CAAC,CAAC,EAAEvF,WAAW,GAAG,CAAC,CAAC;IAC9C,MAAM+F,OAAO,GAAG7F,IAAI,CAACC,GAAG,CAAC2B,UAAU,EAAE9B,WAAW,GAAG,CAAC,CAAC;IAErD,KAAK,IAAIgG,CAAC,GAAGF,SAAS,EAAEE,CAAC,IAAID,OAAO,EAAEC,CAAC,EAAE,EAAE;MACzCH,KAAK,CAACI,IAAI,CAACD,CAAC,CAAC;IACf;IAEA,OAAOH,KAAK;EACd;EAEA;EACAK,QAAQA,CAAA;IACN,IAAI,CAAC3B,uBAAuB,EAAE;EAChC;EAEA;EACA/G,WAAWA,CAAA;IACT,IAAI,CAACqC,aAAa,GAAG,EAAE;IACvB,IAAI,CAAC0E,uBAAuB,EAAE;EAChC;EAIA;EACA4B,aAAaA,CAAA;IACX,IAAI,CAAChC,WAAW,GAAG,IAAI;IACvB,IAAI,CAAClF,WAAW,GAAG;MACjBP,IAAI,EAAE,EAAE;MACRE,WAAW,EAAE,EAAE;MACfwF,aAAa,EAAE;KAChB;IACD;IACA,IAAI,CAAC3E,aAAa,CAAC2G,OAAO,CAACC,IAAI,IAAIA,IAAI,CAAClI,QAAQ,GAAG,KAAK,CAAC;EAC3D;EAEA;EACAoB,iBAAiBA,CAAA;IACf,IAAI,CAAC4E,WAAW,GAAG,KAAK;IACxB,IAAI,CAAClF,WAAW,GAAG;MACjBP,IAAI,EAAE,EAAE;MACRE,WAAW,EAAE,EAAE;MACfwF,aAAa,EAAE;KAChB;EACH;EAEA;EACArF,eAAeA,CAAA;IACb,IAAI,CAAC,IAAI,CAACE,WAAW,CAACP,IAAI,CAACqG,IAAI,EAAE,EAAE;MACjCuB,KAAK,CAAC,SAAS,CAAC;MAChB;IACF;IAEA,MAAMlC,aAAa,GAAG,IAAI,CAAC3E,aAAa,CAACyF,MAAM,CAACmB,IAAI,IAAIA,IAAI,CAAClI,QAAQ,CAAC;IACtE,IAAIiG,aAAa,CAAC1E,MAAM,KAAK,CAAC,EAAE;MAC9B4G,KAAK,CAAC,WAAW,CAAC;MAClB;IACF;IAEA;IACA,IAAI,CAACC,cAAc,CAAC,IAAI,CAACtH,WAAW,CAACP,IAAI,EAAE,IAAI,CAACO,WAAW,CAACL,WAAW,EAAEwF,aAAa,CAAC;EACzF;EAEA;EACAmC,cAAcA,CAAC7H,IAAY,EAAEE,WAAmB,EAAEwF,aAAoB;IACpE;IACAK,OAAO,CAACC,GAAG,CAAC,cAAc,EAAE;MAC1B8B,YAAY,EAAE9H,IAAI;MAClBE,WAAW,EAAEA,WAAW;MACxBiF,UAAU,EAAE,IAAI,CAACA,UAAU;MAC3BO,aAAa,EAAEA,aAAa,CAACqC,GAAG,CAACJ,IAAI,KAAK;QACxCK,KAAK,EAAE,IAAI,CAACC,QAAQ,CAACN,IAAI,CAAC;QAC1BO,SAAS,EAAE,IAAI,CAAC9B,YAAY,CAACuB,IAAI,CAAC;QAClCQ,UAAU,EAAE,IAAI,CAACC,aAAa,CAACT,IAAI;OACpC,CAAC;KACH,CAAC;IAEF;IACA,MAAMU,KAAK,GAAG7G,IAAI,CAACqF,GAAG,CAAC,GAAG,IAAI,CAACvB,SAAS,CAACyC,GAAG,CAACO,CAAC,IAAIA,CAAC,CAACxG,UAAU,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC;IAE5E;IACA,MAAMvB,WAAW,GAAa;MAC5BuB,UAAU,EAAEuG,KAAK;MACjBjG,YAAY,EAAEpC,IAAI,CAACqG,IAAI,EAAE;MACzBhE,WAAW,EAAEnC,WAAW,CAACmG,IAAI;KAC9B;IAED;IACA,IAAI,CAACf,SAAS,CAACiC,IAAI,CAAChH,WAAW,CAAC;IAEhC;IACAmF,aAAa,CAACgC,OAAO,CAAC,CAACC,IAAI,EAAEY,KAAK,KAAI;MACpC,MAAMC,MAAM,GAAmB;QAC7BvC,gBAAgB,EAAE,IAAI,CAACV,eAAe,CAACvE,MAAM,GAAGuH,KAAK,GAAG,CAAC;QACzDzG,UAAU,EAAEuG,KAAK;QACjBnC,KAAK,EAAE,IAAI,CAAC+B,QAAQ,CAACN,IAAI,CAAC;QAAE;QAC5BxB,UAAU,EAAE,IAAI,CAAChB,UAAU;QAAE;QAC7B1B,SAAS,EAAE,IAAI,CAAC2C,YAAY,CAACuB,IAAI,CAAC;QAAE;QACpCjE,UAAU,EAAE,IAAI,CAAC0E,aAAa,CAACT,IAAI,CAAC,CAAC;OACtC;MACD,IAAI,CAACpC,eAAe,CAACgC,IAAI,CAACiB,MAAM,CAAC;IACnC,CAAC,CAAC;IAEF;IACA,IAAI,CAAC3C,uBAAuB,EAAE;IAE9B;IACA,IAAI,CAACJ,WAAW,GAAG,KAAK;IACxBmC,KAAK,CAAC,OAAO5H,IAAI,cAAc0F,aAAa,CAAC1E,MAAM,MAAM,CAAC;EAC5D;EAEA;EACQiH,QAAQA,CAACN,IAAS;IACxB,OAAOA,IAAI,CAACc,cAAc,IAAId,IAAI,CAACe,EAAE,IAAIf,IAAI,CAACgB,EAAE,IAAI,CAAC;EACvD;EAEA;EACQvC,YAAYA,CAACwC,KAAW;IAC9B;IACA,QAAQ,IAAI,CAACzD,UAAU;MACrB,KAAK,aAAa;QAChB,OAAO,cAAc;MACvB;QACE,OAAO,MAAM;IACjB;EACF;EAEA;EACQiD,aAAaA,CAACT,IAAS;IAC7B,OAAOA,IAAI,CAAC5H,YAAY,IAAI4H,IAAI,CAAC3H,IAAI,IAAI2H,IAAI,CAACkB,KAAK,IAAI,EAAE;EAC3D;EAEA;EACA3G,gBAAgBA,CAACuE,QAAkB;IACjC,IAAI,CAACpD,gBAAgB,GAAGoD,QAAQ;IAChC,IAAI,CAACrB,cAAc,CAAC0D,IAAI,CAACrC,QAAQ,CAAC;IAClC,IAAI,CAACsC,sBAAsB,EAAE;EAC/B;EAEA;EACAA,sBAAsBA,CAAA;IACpB,MAAMC,OAAO,GAAG,IAAI,CAACjE,sBAAsB;IAC3C,IAAI,CAACzB,gBAAgB,CAAC5B,UAAU,GAAGsH,OAAO,CAAChI,MAAM;IACjD,IAAI,CAACsC,gBAAgB,CAACF,UAAU,GAAG5B,IAAI,CAACoF,IAAI,CAAC,IAAI,CAACtD,gBAAgB,CAAC5B,UAAU,GAAG,IAAI,CAAC4B,gBAAgB,CAAC/B,QAAQ,CAAC;IAC/G,IAAI,CAAC+B,gBAAgB,CAAChC,WAAW,GAAG,CAAC,CAAC,CAAC;IACvC,IAAI,CAAC2H,sBAAsB,EAAE;EAC/B;EAEA;EACAA,sBAAsBA,CAAA;IACpB,MAAMD,OAAO,GAAG,IAAI,CAACjE,sBAAsB;IAC3C,MAAMgC,UAAU,GAAG,CAAC,IAAI,CAACzD,gBAAgB,CAAChC,WAAW,GAAG,CAAC,IAAI,IAAI,CAACgC,gBAAgB,CAAC/B,QAAQ;IAC3F,MAAMyF,QAAQ,GAAGD,UAAU,GAAG,IAAI,CAACzD,gBAAgB,CAAC/B,QAAQ;IAC5D,IAAI,CAACqC,gBAAgB,GAAGoF,OAAO,CAAC/B,KAAK,CAACF,UAAU,EAAEC,QAAQ,CAAC;EAC7D;EAEA;EACAhD,cAAcA,CAACkD,IAAY;IACzB,IAAIA,IAAI,IAAI,CAAC,IAAIA,IAAI,IAAI,IAAI,CAAC5D,gBAAgB,CAACF,UAAU,EAAE;MACzD,IAAI,CAACE,gBAAgB,CAAChC,WAAW,GAAG4F,IAAI;MACxC,IAAI,CAAC+B,sBAAsB,EAAE;IAC/B;EACF;EAEA;EACA5E,oBAAoBA,CAAA;IAClB,MAAM8C,KAAK,GAAa,EAAE;IAC1B,MAAM/D,UAAU,GAAG,IAAI,CAACE,gBAAgB,CAACF,UAAU;IACnD,MAAM9B,WAAW,GAAG,IAAI,CAACgC,gBAAgB,CAAChC,WAAW;IAErD;IACA,MAAM8F,SAAS,GAAG5F,IAAI,CAACqF,GAAG,CAAC,CAAC,EAAEvF,WAAW,GAAG,CAAC,CAAC;IAC9C,MAAM+F,OAAO,GAAG7F,IAAI,CAACC,GAAG,CAAC2B,UAAU,EAAE9B,WAAW,GAAG,CAAC,CAAC;IAErD,KAAK,IAAIgG,CAAC,GAAGF,SAAS,EAAEE,CAAC,IAAID,OAAO,EAAEC,CAAC,EAAE,EAAE;MACzCH,KAAK,CAACI,IAAI,CAACD,CAAC,CAAC;IACf;IAEA,OAAOH,KAAK;EACd;EAEA;EACA+B,OAAOA,CAAA;IACL,IAAI,CAAC7D,KAAK,CAACyD,IAAI,EAAE;EACnB;EAEA;EACA/G,gBAAgBA,CAACoH,UAAkB;IACjC,IAAIC,OAAO,CAAC,UAAU,CAAC,EAAE;MACvB;MACA,IAAI,CAACC,kBAAkB,CAACF,UAAU,CAAC;IACrC;EACF;EAEA;EACAE,kBAAkBA,CAACF,UAAkB;IACnC;IACApD,OAAO,CAACC,GAAG,CAAC,cAAc,EAAE;MAC1BmD,UAAU,EAAEA,UAAU;MACtBhE,UAAU,EAAE,IAAI,CAACA;KAClB,CAAC;IAEF;IACA,IAAI,CAACG,SAAS,GAAG,IAAI,CAACA,SAAS,CAACkB,MAAM,CAAC8B,CAAC,IAAIA,CAAC,CAACxG,UAAU,KAAKqH,UAAU,CAAC;IACxE,IAAI,CAAC5D,eAAe,GAAG,IAAI,CAACA,eAAe,CAACiB,MAAM,CAAC8C,CAAC,IAAIA,CAAC,CAACxH,UAAU,KAAKqH,UAAU,CAAC;IACpF,IAAI,CAACtD,uBAAuB,EAAE;IAE9B;IACA,IAAI,IAAI,CAACxC,gBAAgB,EAAEvB,UAAU,KAAKqH,UAAU,EAAE;MACpD,IAAI,CAAC9F,gBAAgB,GAAG,IAAI;IAC9B;IAEAuE,KAAK,CAAC,OAAO,CAAC;EAChB;EAEA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EA8BA;EACApD,mBAAmBA,CAAA;IACjB,IAAI,CAACnB,gBAAgB,GAAG,IAAI;EAC9B;EAEA;EACA,IAAI0B,sBAAsBA,CAAA;IACxB,IAAI,CAAC,IAAI,CAAC1B,gBAAgB,EAAE;MAC1B,OAAO,EAAE;IACX;IACA,OAAO,IAAI,CAACkC,eAAe,CAACiB,MAAM,CAAC8C,CAAC,IAAIA,CAAC,CAACxH,UAAU,KAAK,IAAI,CAACuB,gBAAiB,CAACvB,UAAU,CAAC;EAC7F;EAEA;EACAyH,iBAAiBA,CAAChB,KAAa,EAAE9B,QAAkB;IACjD,OAAOA,QAAQ,CAAC3E,UAAU,IAAIyG,KAAK;EACrC;;;uCAjZWtD,uBAAuB;IAAA;EAAA;;;YAAvBA,uBAAuB;MAAAuE,SAAA;MAAAC,MAAA;QAAA1I,aAAA;QAAAoE,UAAA;MAAA;MAAAuE,OAAA;QAAAtE,cAAA;QAAAC,KAAA;MAAA;MAAAsE,UAAA;MAAAC,QAAA,GAAAvL,EAAA,CAAAwL,oBAAA,EAAAxL,EAAA,CAAAyL,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAxD,QAAA,WAAAyD,iCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCT9B9L,EAHN,CAAAC,cAAA,iBAA+D,qBAC7C,aACiD,YAC5C;UAAAD,EAAA,CAAAY,MAAA,+BAAI;UAAAZ,EAAA,CAAAW,YAAA,EAAK;UAC1BX,EAAA,CAAAC,cAAA,gBAAiE;UAA1BD,EAAA,CAAAE,UAAA,mBAAA8L,yDAAA;YAAA,OAASD,GAAA,CAAA3C,aAAA,EAAe;UAAA,EAAC;UAC9DpJ,EAAA,CAAAU,SAAA,WAAgC;UAAAV,EAAA,CAAAY,MAAA,gCAClC;UAEJZ,EAFI,CAAAW,YAAA,EAAS,EACL,EACS;UAKXX,EAJN,CAAAC,cAAA,sBAAsC,aAED,cACR,gBAE2B;UADiBD,EAAA,CAAAa,gBAAA,2BAAAoL,iEAAAlL,MAAA;YAAAf,EAAA,CAAAmB,kBAAA,CAAA4K,GAAA,CAAAjJ,aAAA,EAAA/B,MAAA,MAAAgL,GAAA,CAAAjJ,aAAA,GAAA/B,MAAA;YAAA,OAAAA,MAAA;UAAA,EAA2B;UACvEf,EAArB,CAAAE,UAAA,mBAAAgM,yDAAA;YAAA,OAASH,GAAA,CAAA5C,QAAA,EAAU;UAAA,EAAC,yBAAAgD,+DAAA;YAAA,OAAgBJ,GAAA,CAAA5C,QAAA,EAAU;UAAA,EAAC;UADjDnJ,EAAA,CAAAW,YAAA,EACkD;UAEhDX,EADF,CAAAC,cAAA,eAAgC,kBAC+C;UAArBD,EAAA,CAAAE,UAAA,mBAAAkM,0DAAA;YAAA,OAASL,GAAA,CAAA5C,QAAA,EAAU;UAAA,EAAC;UAC1EnJ,EAAA,CAAAU,SAAA,aAA6B;UAC/BV,EAAA,CAAAW,YAAA,EAAS;UACTX,EAAA,CAAAoC,UAAA,KAAAiK,0CAAA,qBAAsG;UAK5GrM,EAFI,CAAAW,YAAA,EAAM,EACF,EACF;UAGNX,EAAA,CAAAoC,UAAA,KAAAkK,uCAAA,mBAAwD;UA4DxDtM,EAAA,CAAAC,cAAA,eAA2B;UASzBD,EAPA,CAAAoC,UAAA,KAAAmK,uCAAA,kBAA4D,KAAAC,uCAAA,kBAOgB;UAYpExM,EAJR,CAAAC,cAAA,eAA8B,iBACmB,iBAClB,UACrB,cACc;UAAAD,EAAA,CAAAY,MAAA,gCAAI;UAAAZ,EAAA,CAAAW,YAAA,EAAK;UACzBX,EAAA,CAAAC,cAAA,cAAgB;UAAAD,EAAA,CAAAY,MAAA,oBAAE;UAAAZ,EAAA,CAAAW,YAAA,EAAK;UACvBX,EAAA,CAAAC,cAAA,cAAoC;UAAAD,EAAA,CAAAY,MAAA,oBAAE;UAE1CZ,EAF0C,CAAAW,YAAA,EAAK,EACxC,EACC;UACRX,EAAA,CAAAC,cAAA,aAAO;UAoBLD,EAnBA,CAAAoC,UAAA,KAAAqK,sCAAA,kBAAuE,KAAAC,sCAAA,iBAmBJ;UAezE1M,EAFI,CAAAW,YAAA,EAAQ,EACF,EACJ;UAGNX,EAAA,CAAAoC,UAAA,KAAAuK,uCAAA,mBAAiF;UA2BnF3M,EAAA,CAAAW,YAAA,EAAM;UAGNX,EAAA,CAAAoC,UAAA,KAAAwK,uCAAA,mBAA4D;UAoF9D5M,EAAA,CAAAW,YAAA,EAAe;UAGXX,EAFJ,CAAAC,cAAA,sBAAgB,eACW,kBAC+B;UAApBD,EAAA,CAAAE,UAAA,mBAAA2M,0DAAA;YAAA,OAASd,GAAA,CAAAlB,OAAA,EAAS;UAAA,EAAC;UACnD7K,EAAA,CAAAU,SAAA,aAAiC;UAAAV,EAAA,CAAAY,MAAA,qBACnC;UAGNZ,EAHM,CAAAW,YAAA,EAAS,EACL,EACS,EACT;;;UApQiEX,EAAA,CAAAqB,SAAA,IAA2B;UAA3BrB,EAAA,CAAAwB,gBAAA,YAAAuK,GAAA,CAAAjJ,aAAA,CAA2B;UAMX9C,EAAA,CAAAqB,SAAA,GAAmB;UAAnBrB,EAAA,CAAAyC,UAAA,SAAAsJ,GAAA,CAAAjJ,aAAA,CAAmB;UAQpG9C,EAAA,CAAAqB,SAAA,EAAiB;UAAjBrB,EAAA,CAAAyC,UAAA,SAAAsJ,GAAA,CAAA3E,WAAA,CAAiB;UA8DkBpH,EAAA,CAAAqB,SAAA,GAAmB;UAAnBrB,EAAA,CAAAyC,UAAA,SAAAsJ,GAAA,CAAAjJ,aAAA,CAAmB;UAOvB9C,EAAA,CAAAqB,SAAA,EAAuC;UAAvCrB,EAAA,CAAAyC,UAAA,SAAAsJ,GAAA,CAAA/I,kBAAA,CAAAK,UAAA,KAAuC;UAkBhDrD,EAAA,CAAAqB,SAAA,IAAuB;UAAArB,EAAvB,CAAAyC,UAAA,YAAAsJ,GAAA,CAAA5E,kBAAA,CAAuB,iBAAA4E,GAAA,CAAAb,iBAAA,CAA0B;UAmBhElL,EAAA,CAAAqB,SAAA,EAA4D;UAA5DrB,EAAA,CAAAyC,UAAA,UAAAsJ,GAAA,CAAA5E,kBAAA,IAAA4E,GAAA,CAAA5E,kBAAA,CAAAxE,MAAA,OAA4D;UAkB/B3C,EAAA,CAAAqB,SAAA,EAAuC;UAAvCrB,EAAA,CAAAyC,UAAA,SAAAsJ,GAAA,CAAA/I,kBAAA,CAAA+B,UAAA,KAAuC;UA8B3E/E,EAAA,CAAAqB,SAAA,EAAsB;UAAtBrB,EAAA,CAAAyC,UAAA,SAAAsJ,GAAA,CAAA/G,gBAAA,CAAsB;;;qBD3KpBpF,YAAY,EAAAkN,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EAAEnN,WAAW,EAAAoN,EAAA,CAAAC,aAAA,EAAAD,EAAA,CAAAE,oBAAA,EAAAF,EAAA,CAAAG,4BAAA,EAAAH,EAAA,CAAAI,eAAA,EAAAJ,EAAA,CAAAK,oBAAA,EAAAL,EAAA,CAAAM,iBAAA,EAAAN,EAAA,CAAAO,kBAAA,EAAAP,EAAA,CAAAQ,OAAA,EAAAR,EAAA,CAAAS,MAAA,EAAE5N,YAAY,EAAA6N,EAAA,CAAAC,eAAA,EAAAD,EAAA,CAAAE,mBAAA,EAAAF,EAAA,CAAAG,qBAAA,EAAAH,EAAA,CAAAI,qBAAA,EAAEhO,cAAc;MAAAiO,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}