{"ast": null, "code": "import isSameDay from \"../isSameDay/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name isToday\n * @category Day Helpers\n * @summary Is the given date today?\n * @pure false\n *\n * @description\n * Is the given date today?\n *\n * > ⚠️ Please note that this function is not present in the FP submodule as\n * > it uses `Date.now()` internally hence impure and can't be safely curried.\n *\n * @param {Date|Number} date - the date to check\n * @returns {Boolean} the date is today\n * @throws {TypeError} 1 argument required\n *\n * @example\n * // If today is 6 October 2014, is 6 October 14:00:00 today?\n * const result = isToday(new Date(2014, 9, 6, 14, 0))\n * //=> true\n */\nexport default function isToday(dirtyDate) {\n  requiredArgs(1, arguments);\n  return isSameDay(dirtyDate, Date.now());\n}", "map": {"version": 3, "names": ["isSameDay", "requiredArgs", "isToday", "dirtyDate", "arguments", "Date", "now"], "sources": ["C:/Users/<USER>/Documents/salechange-product/adminFront/node_modules/date-fns/esm/isToday/index.js"], "sourcesContent": ["import isSameDay from \"../isSameDay/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name isToday\n * @category Day Helpers\n * @summary Is the given date today?\n * @pure false\n *\n * @description\n * Is the given date today?\n *\n * > ⚠️ Please note that this function is not present in the FP submodule as\n * > it uses `Date.now()` internally hence impure and can't be safely curried.\n *\n * @param {Date|Number} date - the date to check\n * @returns {Boolean} the date is today\n * @throws {TypeError} 1 argument required\n *\n * @example\n * // If today is 6 October 2014, is 6 October 14:00:00 today?\n * const result = isToday(new Date(2014, 9, 6, 14, 0))\n * //=> true\n */\nexport default function isToday(dirtyDate) {\n  requiredArgs(1, arguments);\n  return isSameDay(dirtyDate, Date.now());\n}"], "mappings": "AAAA,OAAOA,SAAS,MAAM,uBAAuB;AAC7C,OAAOC,YAAY,MAAM,+BAA+B;AACxD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,SAASC,OAAOA,CAACC,SAAS,EAAE;EACzCF,YAAY,CAAC,CAAC,EAAEG,SAAS,CAAC;EAC1B,OAAOJ,SAAS,CAACG,SAAS,EAAEE,IAAI,CAACC,GAAG,CAAC,CAAC,CAAC;AACzC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}