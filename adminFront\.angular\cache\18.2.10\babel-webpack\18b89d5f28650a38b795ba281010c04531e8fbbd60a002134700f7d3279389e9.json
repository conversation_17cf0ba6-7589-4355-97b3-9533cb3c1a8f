{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { tap } from 'rxjs';\nimport { SharedModule } from '../../components/shared.module';\nimport { decodeJwtPayload } from '@nebular/auth';\nimport { LocalStorageService } from 'src/app/shared/services/local-storage.service';\nimport { STORAGE_KEY } from 'src/app/shared/constant/constant';\nimport { EEvent } from 'src/app/shared/services/event.service';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/services/api/services\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"src/app/shared/services/utility.service\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"src/app/shared/services/message.service\";\nimport * as i6 from \"src/app/shared/helper/validationHelper\";\nimport * as i7 from \"src/app/shared/services/event.service\";\nimport * as i8 from \"@angular/forms\";\nimport * as i9 from \"@nebular/theme\";\nimport * as i10 from \"../../../@theme/pipes/mapping.pipe\";\nfunction DetailApprovalWaitingComponent_div_2_li_29_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"li\")(1, \"span\", 20);\n    i0.ɵɵlistener(\"click\", function DetailApprovalWaitingComponent_div_2_li_29_Template_span_click_1_listener() {\n      const item_r3 = i0.ɵɵrestoreView(_r2).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r3.downloadFile(item_r3.CFile, item_r3.CFileName));\n    });\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const item_r3 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", item_r3.CFile ? \"text-blue-400 underline cursor-pointer \" : \"\");\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", item_r3.CFileName, \" \");\n  }\n}\nfunction DetailApprovalWaitingComponent_div_2_div_50_tr_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\", 27)(1, \"th\", 28);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"td\", 29);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"td\", 29);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"td\", 29);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const record_r5 = ctx.$implicit;\n    const ctx_r3 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(3, 6, record_r5.CRecordDate ? record_r5.CRecordDate : ctx_r3.approvalWaiting.CCreateDT, \"yyyy/MM/dd HH:mm:ss\"), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate3(\" \", record_r5.CAction === 1 ? \"\\u9001\\u51FA\\u5BE9\\u6838\" : \"\", \" \", record_r5.CAction === 2 ? \"\\u5BE9\\u6838\\u901A\\u904E\" : \"\", \" \", record_r5.CAction === 3 ? \"\\u5BE9\\u6838\\u99C1\\u56DE\" : \"\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", record_r5.CCreator, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", record_r5.CRemark, \" \");\n  }\n}\nfunction DetailApprovalWaitingComponent_div_2_div_50_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 21)(1, \"table\", 22)(2, \"thead\", 23)(3, \"tr\", 24)(4, \"th\", 25);\n    i0.ɵɵtext(5, \" \\u65E5\\u671F \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"th\", 25);\n    i0.ɵɵtext(7, \" \\u52D5\\u4F5C \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"th\", 25);\n    i0.ɵɵtext(9, \" \\u4F7F\\u7528\\u8005 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"th\", 25);\n    i0.ɵɵtext(11, \" \\u5099\\u8A3B \");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(12, \"tbody\");\n    i0.ɵɵtemplate(13, DetailApprovalWaitingComponent_div_2_div_50_tr_13_Template, 10, 9, \"tr\", 26);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(13);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.approvalWaiting.CApproveRecord);\n  }\n}\nfunction DetailApprovalWaitingComponent_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 1)(1, \"div\", 2)(2, \"span\", 3);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 4)(5, \"div\", 5)(6, \"div\", 6)(7, \"span\", 7);\n    i0.ɵɵtext(8, \" \\u5EFA\\u6848 \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"span\", 8);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"div\", 9)(12, \"div\", 6)(13, \"span\", 7);\n    i0.ɵɵtext(14, \" \\u985E\\u5225 \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(15, \"span\", 8);\n    i0.ɵɵtext(16);\n    i0.ɵɵpipe(17, \"getTypeApprovalWaiting\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(18, \"div\", 9)(19, \"div\", 6)(20, \"span\", 7);\n    i0.ɵɵtext(21, \" \\u540D\\u7A31 \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(22, \"span\", 8);\n    i0.ɵɵtext(23);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(24, \"div\", 9)(25, \"div\", 6)(26, \"span\", 7);\n    i0.ɵɵtext(27, \" \\u6A94\\u6848 \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(28, \"ul\", 8);\n    i0.ɵɵtemplate(29, DetailApprovalWaitingComponent_div_2_li_29_Template, 3, 2, \"li\", 10);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(30, \"div\", 9)(31, \"div\", 6)(32, \"span\", 7);\n    i0.ɵɵtext(33, \" \\u5BE9\\u6838\\u8AAA\\u660E \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(34, \"span\", 8);\n    i0.ɵɵtext(35);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(36, \"div\", 11)(37, \"div\", 12)(38, \"div\", 6)(39, \"span\", 7);\n    i0.ɵɵtext(40, \" \\u5099\\u8A3B \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(41, \"div\", 13)(42, \"textarea\", 14);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function DetailApprovalWaitingComponent_div_2_Template_textarea_ngModelChange_42_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r3 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r3.remark, $event) || (ctx_r3.remark = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(43, \"div\", 15)(44, \"button\", 16);\n    i0.ɵɵlistener(\"click\", function DetailApprovalWaitingComponent_div_2_Template_button_click_44_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.goBack());\n    });\n    i0.ɵɵtext(45, \"\\u53D6\\u6D88\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(46, \"button\", 17);\n    i0.ɵɵlistener(\"click\", function DetailApprovalWaitingComponent_div_2_Template_button_click_46_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.handleAction(false));\n    });\n    i0.ɵɵtext(47, \"\\u99C1\\u56DE\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(48, \"button\", 18);\n    i0.ɵɵlistener(\"click\", function DetailApprovalWaitingComponent_div_2_Template_button_click_48_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.handleAction(true));\n    });\n    i0.ɵɵtext(49, \"\\u540C\\u610F\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(50, DetailApprovalWaitingComponent_div_2_div_50_Template, 14, 1, \"div\", 19);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.approvalWaiting.CName, \" \");\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.approvalWaiting.CBuildcaseName, \" \");\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(17, 11, ctx_r3.approvalWaiting.CType), \" \");\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.approvalWaiting.CName, \" \");\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.approvalWaiting.CFileApproves);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.approvalWaiting.CApprovalRemark, \" \");\n    i0.ɵɵadvance(7);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r3.remark);\n    i0.ɵɵproperty(\"rows\", 4);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"disabled\", ctx_r3.approvalWaiting.CIsApprove !== null && ctx_r3.approvalWaiting.CIsApprove);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", ctx_r3.approvalWaiting.CIsApprove !== null && ctx_r3.approvalWaiting.CIsApprove);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", !!ctx_r3.approvalWaiting);\n  }\n}\nexport class DetailApprovalWaitingComponent {\n  constructor(_specialChangeService, _activatedRoute, _ultilityService, _location, message, _validationHelper, _eventService) {\n    this._specialChangeService = _specialChangeService;\n    this._activatedRoute = _activatedRoute;\n    this._ultilityService = _ultilityService;\n    this._location = _location;\n    this.message = message;\n    this._validationHelper = _validationHelper;\n    this._eventService = _eventService;\n    this.CType = 1;\n    this.remark = \"\";\n    this.decodeJWT = decodeJwtPayload(LocalStorageService.GetLocalStorage(STORAGE_KEY.TOKEN));\n    this.CID = parseInt(this._activatedRoute.snapshot.paramMap.get(\"id\"));\n    this.buildCaseID = parseInt(this._activatedRoute.snapshot.paramMap.get(\"buildCaseId\"));\n    this._activatedRoute.queryParams.pipe(tap(p => {\n      this.CType = p[\"type\"];\n    })).subscribe();\n  }\n  ngOnInit() {\n    this.getApprovalWaitingById();\n  }\n  getApprovalWaitingById() {\n    this._specialChangeService.apiSpecialChangeGetApproveWaitingByIdPost$Json({\n      body: {\n        CID: this.CID,\n        CType: this.CType.toString()\n      }\n    }).pipe(tap(res => {\n      if (res.StatusCode === 0) {\n        this.approvalWaiting = res.Entries;\n      }\n    })).subscribe();\n  }\n  downloadFile(CFile, CFileName) {\n    // if (CFile && CFileName) {\n    //   this._ultilityService.downloadFileFullUrl(\n    //     CFile, CFileName\n    //   )\n    // }\n    window.open(CFile, \"_blank\");\n  }\n  handleAction(isApprove) {\n    if (!isApprove) {\n      this.validation();\n      if (this._validationHelper.errorMessages.length > 0) {\n        this.message.showErrorMSGs(this._validationHelper.errorMessages);\n        return;\n      }\n    }\n    this._specialChangeService.apiSpecialChangeUpdateApproveWaitingPost$Json({\n      body: {\n        CID: this.CID,\n        CType: this.CType,\n        CIsApprove: isApprove,\n        CRemark: this.remark\n      }\n    }).pipe(tap(res => {\n      if (res.StatusCode === 0) {\n        this.message.showSucessMSG(\"執行成功\");\n        this.getApprovalWaitingById();\n        if (this.approvalWaiting.CApproveRecord?.length == 0) {\n          this.approvalWaiting.CApproveRecord?.push({\n            CCreator: this.decodeJWT.userName,\n            CRecordDate: new Date().toISOString(),\n            CRemark: this.remark\n          });\n        }\n        this.remark = \"\";\n      }\n      this.goBack();\n    })).subscribe();\n  }\n  goBack() {\n    this._eventService.push({\n      action: \"GET_BUILDCASE\" /* EEvent.GET_BUILDCASE */,\n      payload: this.buildCaseID\n    });\n    this._location.back();\n  }\n  validation() {\n    this._validationHelper.clear();\n    this._validationHelper.required(\"[備註]\", this.remark);\n  }\n  static {\n    this.ɵfac = function DetailApprovalWaitingComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || DetailApprovalWaitingComponent)(i0.ɵɵdirectiveInject(i1.SpecialChangeService), i0.ɵɵdirectiveInject(i2.ActivatedRoute), i0.ɵɵdirectiveInject(i3.UtilityService), i0.ɵɵdirectiveInject(i4.Location), i0.ɵɵdirectiveInject(i5.MessageService), i0.ɵɵdirectiveInject(i6.ValidationHelper), i0.ɵɵdirectiveInject(i7.EventService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: DetailApprovalWaitingComponent,\n      selectors: [[\"app-detail-approval-waiting\"]],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 3,\n      vars: 1,\n      consts: [[\"class\", \"flex flex-col justify-items-center items-center m-auto w-[50%]\", 4, \"ngIf\"], [1, \"flex\", \"flex-col\", \"justify-items-center\", \"items-center\", \"m-auto\", \"w-[50%]\"], [1, \"border-b-2\", \"border-b-black\", \"w-full\"], [1, \"text-xl\", \"font-bold\"], [1, \"px-3\", \"py-4\"], [1, \"flex\", \"items-center\"], [1, \"w-[100px]\"], [1, \"font-bold\"], [1, \"w-[80%]\", \"break-words\"], [1, \"flex\", \"items-center\", \"mt-3\"], [4, \"ngFor\", \"ngForOf\"], [1, \"mt-2\", \"w-full\"], [1, \"flex\", \"px-3\", \"py-4\", \"w-full\"], [1, \"w-full\"], [\"nbInput\", \"\", 1, \"resize-none\", \"!max-w-full\", \"w-full\", 3, \"ngModelChange\", \"ngModel\", \"rows\"], [1, \"mt-3\", \"flex\", \"items-center\"], [\"nbButton\", \"\", 1, \"btn\", \"border-black\", \"border\", \"mr-2\", 3, \"click\"], [\"nbButton\", \"\", \"status\", \"danger\", 1, \"btn\", \"mr-2\", 3, \"click\", \"disabled\"], [\"nbButton\", \"\", \"status\", \"primary\", 1, \"btn\", \"mr-2\", 3, \"click\", \"disabled\"], [\"class\", \"table-responsive relative overflow-x-auto mt-3\", 4, \"ngIf\"], [1, \"w-[80%]\", \"break-words\", 3, \"click\", \"ngClass\"], [1, \"table-responsive\", \"relative\", \"overflow-x-auto\", \"mt-3\"], [1, \"table\", \"table-bordered\", \"w-full\", \"text-sm\", \"text-left\", \"rtl:text-right\", \"text-gray-500\", \"dark:text-gray-400\"], [1, \"text-xs\", \"text-gray-700\", \"uppercase\", \"bg-gray-50\"], [2, \"background-color\", \"#27ae60\", \"color\", \"white\"], [\"scope\", \"col\", 1, \"px-6\", \"py-3\"], [\"class\", \"bg-white text-black\", 4, \"ngFor\", \"ngForOf\"], [1, \"bg-white\", \"text-black\"], [\"scope\", \"row\", 1, \"px-6\", \"py-4\", \"font-medium\", \"whitespace-nowrap\"], [1, \"px-6\", \"py-4\"]],\n      template: function DetailApprovalWaitingComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"nb-card\")(1, \"nb-card-body\");\n          i0.ɵɵtemplate(2, DetailApprovalWaitingComponent_div_2_Template, 51, 13, \"div\", 0);\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", !!ctx.approvalWaiting);\n        }\n      },\n      dependencies: [CommonModule, i4.NgClass, i4.NgForOf, i4.NgIf, i4.DatePipe, SharedModule, i8.DefaultValueAccessor, i8.NgControlStatus, i8.NgModel, i9.NbCardComponent, i9.NbCardBodyComponent, i9.NbInputDirective, i9.NbButtonComponent, i10.ApprovalWaitingPipe],\n      styles: [\"/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsImZpbGUiOiJkZXRhaWwtYXBwcm92YWwtd2FpdGluZy5jb21wb25lbnQuc2NzcyJ9 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvcGFnZXMvYXBwcm92ZS13YWl0aW5nL2RldGFpbC1hcHByb3ZhbC13YWl0aW5nL2RldGFpbC1hcHByb3ZhbC13YWl0aW5nLmNvbXBvbmVudC5zY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiI7QUFDQSx3TEFBd0wiLCJzb3VyY2VSb290IjoiIn0= */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "tap", "SharedModule", "decodeJwtPayload", "LocalStorageService", "STORAGE_KEY", "EEvent", "i0", "ɵɵelementStart", "ɵɵlistener", "DetailApprovalWaitingComponent_div_2_li_29_Template_span_click_1_listener", "item_r3", "ɵɵrestoreView", "_r2", "$implicit", "ctx_r3", "ɵɵnextContext", "ɵɵresetView", "downloadFile", "CFile", "CFileName", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵproperty", "ɵɵtextInterpolate1", "ɵɵpipeBind2", "record_r5", "CRecordDate", "approvalWaiting", "CCreateDT", "ɵɵtextInterpolate3", "CAction", "CCreator", "CRemark", "ɵɵtemplate", "DetailApprovalWaitingComponent_div_2_div_50_tr_13_Template", "CApproveRecord", "DetailApprovalWaitingComponent_div_2_li_29_Template", "ɵɵtwoWayListener", "DetailApprovalWaitingComponent_div_2_Template_textarea_ngModelChange_42_listener", "$event", "_r1", "ɵɵtwoWayBindingSet", "remark", "DetailApprovalWaitingComponent_div_2_Template_button_click_44_listener", "goBack", "DetailApprovalWaitingComponent_div_2_Template_button_click_46_listener", "handleAction", "DetailApprovalWaitingComponent_div_2_Template_button_click_48_listener", "DetailApprovalWaitingComponent_div_2_div_50_Template", "CName", "CBuildcaseName", "ɵɵpipeBind1", "CType", "CFileApproves", "CApprovalRemark", "ɵɵtwoWayProperty", "CIsApprove", "DetailApprovalWaitingComponent", "constructor", "_specialChangeService", "_activatedRoute", "_ultilityService", "_location", "message", "_validationHelper", "_eventService", "decodeJWT", "GetLocalStorage", "TOKEN", "CID", "parseInt", "snapshot", "paramMap", "get", "buildCaseID", "queryParams", "pipe", "p", "subscribe", "ngOnInit", "getApprovalWaitingById", "apiSpecialChangeGetApproveWaitingByIdPost$Json", "body", "toString", "res", "StatusCode", "Entries", "window", "open", "isApprove", "validation", "errorMessages", "length", "showErrorMSGs", "apiSpecialChangeUpdateApproveWaitingPost$Json", "showSucessMSG", "push", "userName", "Date", "toISOString", "action", "payload", "back", "clear", "required", "ɵɵdirectiveInject", "i1", "SpecialChangeService", "i2", "ActivatedRoute", "i3", "UtilityService", "i4", "Location", "i5", "MessageService", "i6", "ValidationHelper", "i7", "EventService", "selectors", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "DetailApprovalWaitingComponent_Template", "rf", "ctx", "DetailApprovalWaitingComponent_div_2_Template", "Ng<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "DatePipe", "i8", "DefaultValueAccessor", "NgControlStatus", "NgModel", "i9", "NbCardComponent", "NbCardBodyComponent", "NbInputDirective", "NbButtonComponent", "i10", "ApprovalWaitingPipe", "styles"], "sources": ["C:\\Users\\<USER>\\Documents\\jeansalechange\\adminFront\\src\\app\\pages\\approve-waiting\\detail-approval-waiting\\detail-approval-waiting.component.ts", "C:\\Users\\<USER>\\Documents\\jeansalechange\\adminFront\\src\\app\\pages\\approve-waiting\\detail-approval-waiting\\detail-approval-waiting.component.html"], "sourcesContent": ["import { CommonModule, Location } from '@angular/common';\r\nimport { Component, OnInit } from '@angular/core';\r\nimport { ActivatedRoute } from '@angular/router';\r\nimport { tap } from 'rxjs';\r\nimport { ApproveWaitingByIdRes } from 'src/services/api/models';\r\nimport { SpecialChangeService } from 'src/services/api/services';\r\nimport { SharedModule } from '../../components/shared.module';\r\nimport { UtilityService } from 'src/app/shared/services/utility.service';\r\nimport { decodeJwtPayload } from '@nebular/auth';\r\nimport { LocalStorageService } from 'src/app/shared/services/local-storage.service';\r\nimport { STORAGE_KEY } from 'src/app/shared/constant/constant';\r\nimport { BaseFilePipe } from 'src/app/@theme/pipes/base-file.pipe';\r\nimport { MessageService } from 'src/app/shared/services/message.service';\r\nimport { ValidationHelper } from 'src/app/shared/helper/validationHelper';\r\nimport { EEvent, EventService } from 'src/app/shared/services/event.service';\r\n\r\n@Component({\r\n  selector: 'app-detail-approval-waiting',\r\n  standalone: true,\r\n  imports: [\r\n    CommonModule,\r\n    SharedModule,\r\n    BaseFilePipe\r\n  ],\r\n  templateUrl: './detail-approval-waiting.component.html',\r\n  styleUrls: ['./detail-approval-waiting.component.scss']\r\n})\r\nexport class DetailApprovalWaitingComponent implements OnInit {\r\n\r\n  CType: number = 1;\r\n  CID: number\r\n  remark: string = \"\"\r\n  buildCaseID: number\r\n\r\n  approvalWaiting: ApproveWaitingByIdRes\r\n\r\n  decodeJWT: any\r\n\r\n  constructor(\r\n    private _specialChangeService: SpecialChangeService,\r\n    private _activatedRoute: ActivatedRoute,\r\n    private _ultilityService: UtilityService,\r\n    private _location: Location,\r\n    private message: MessageService,\r\n    private _validationHelper: ValidationHelper,\r\n    private _eventService: EventService,\r\n  ) {\r\n    this.decodeJWT = decodeJwtPayload(LocalStorageService.GetLocalStorage(STORAGE_KEY.TOKEN))\r\n    this.CID = parseInt(this._activatedRoute.snapshot.paramMap!.get(\"id\")!);\r\n    this.buildCaseID = parseInt(this._activatedRoute.snapshot.paramMap!.get(\"buildCaseId\")!)\r\n    this._activatedRoute.queryParams.pipe(\r\n      tap(p => {\r\n        this.CType = p[\"type\"]\r\n      })\r\n    ).subscribe();\r\n  }\r\n\r\n  ngOnInit(): void {\r\n    this.getApprovalWaitingById()\r\n  }\r\n\r\n  getApprovalWaitingById() {\r\n    this._specialChangeService.apiSpecialChangeGetApproveWaitingByIdPost$Json({\r\n      body: {\r\n        CID: this.CID,\r\n        CType: this.CType.toString()\r\n      }\r\n    }).pipe(\r\n      tap(res => {\r\n        if (res.StatusCode === 0) {\r\n          this.approvalWaiting = res.Entries!\r\n        }\r\n      })\r\n    ).subscribe();\r\n  }\r\n\r\n  downloadFile(CFile: any, CFileName: any) {\r\n    // if (CFile && CFileName) {\r\n    //   this._ultilityService.downloadFileFullUrl(\r\n    //     CFile, CFileName\r\n    //   )\r\n    // }\r\n    window.open(CFile, \"_blank\");\r\n  }\r\n\r\n  handleAction(isApprove: boolean) {\r\n    if (!isApprove) {\r\n      this.validation()\r\n      if (this._validationHelper.errorMessages.length > 0) {\r\n        this.message.showErrorMSGs(this._validationHelper.errorMessages);\r\n        return\r\n      }\r\n    }\r\n    this._specialChangeService.apiSpecialChangeUpdateApproveWaitingPost$Json({\r\n      body: {\r\n        CID: this.CID,\r\n        CType: this.CType,\r\n        CIsApprove: isApprove,\r\n        CRemark: this.remark\r\n      }\r\n    }).pipe(\r\n      tap(res => {\r\n        if (res.StatusCode === 0) {\r\n          this.message.showSucessMSG(\"執行成功\");\r\n          this.getApprovalWaitingById()\r\n          if (this.approvalWaiting.CApproveRecord?.length == 0) {\r\n            this.approvalWaiting.CApproveRecord?.push({\r\n              CCreator: this.decodeJWT.userName,\r\n              CRecordDate: new Date().toISOString(),\r\n              CRemark: this.remark\r\n            })\r\n          }\r\n          this.remark = \"\"\r\n        }\r\n        this.goBack();\r\n      })\r\n    ).subscribe();\r\n  }\r\n\r\n  goBack() {\r\n    this._eventService.push({\r\n      action: EEvent.GET_BUILDCASE,\r\n      payload: this.buildCaseID\r\n    })\r\n    this._location.back()\r\n  }\r\n\r\n  validation() {\r\n    this._validationHelper.clear();\r\n    this._validationHelper.required(\"[備註]\", this.remark)\r\n  }\r\n}\r\n", "<nb-card>\r\n    <!-- <nb-card-header></nb-card-header> -->\r\n    <nb-card-body>\r\n        <div *ngIf=\"!!approvalWaiting\" class=\"flex flex-col justify-items-center items-center m-auto w-[50%]\">\r\n            <div class=\"border-b-2 border-b-black w-full\">\r\n                <span class=\"text-xl font-bold\">\r\n                    {{approvalWaiting.CName}}\r\n                </span>\r\n                <div class=\"px-3 py-4\">                   \r\n                    <div class=\"flex items-center\">\r\n                        <div class=\"w-[100px]\">\r\n                            <span class=\"font-bold\">\r\n                                建案\r\n                            </span>\r\n                        </div>\r\n                        <span class=\"w-[80%] break-words\">\r\n                            {{approvalWaiting.CBuildcaseName}}\r\n                        </span>\r\n                    </div>\r\n                    <div class=\"flex items-center mt-3\">\r\n                        <div class=\"w-[100px]\">\r\n                            <span class=\"font-bold\">\r\n                                類別\r\n                            </span>\r\n                        </div>\r\n                        <span class=\"w-[80%] break-words\">\r\n                            {{approvalWaiting.CType! | getTypeApprovalWaiting}}\r\n                        </span>\r\n                    </div>\r\n                    <div class=\"flex items-center mt-3\">\r\n                        <div class=\"w-[100px]\">\r\n                            <span class=\"font-bold\">\r\n                                名稱\r\n                            </span>\r\n                        </div>\r\n                        <span class=\"w-[80%] break-words\">\r\n                            {{approvalWaiting.CName}}\r\n                        </span>\r\n                    </div>\r\n                    <div class=\"flex items-center mt-3\">\r\n                        <div class=\"w-[100px]\">\r\n                            <span class=\"font-bold\">\r\n                                檔案\r\n                            </span>\r\n                        </div>\r\n                        <ul class=\"w-[80%] break-words\">\r\n                            <li *ngFor=\"let item of approvalWaiting.CFileApproves\">\r\n                                <span class=\"w-[80%] break-words\" (click)=\"downloadFile(item.CFile, item.CFileName)\"\r\n                                    [ngClass]=\"item.CFile ? 'text-blue-400 underline cursor-pointer ' : ''\">\r\n                                    {{item.CFileName}}\r\n                                </span>\r\n                            </li>\r\n                        </ul>\r\n                    </div>\r\n                    <div class=\"flex items-center mt-3\">\r\n                        <div class=\"w-[100px]\">\r\n                            <span class=\"font-bold\">\r\n                                審核說明\r\n                            </span>\r\n                        </div>\r\n                        <span class=\"w-[80%] break-words\">\r\n                            {{approvalWaiting.CApprovalRemark}}\r\n                        </span>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n            <div class=\"mt-2 w-full\">\r\n                <div class=\"flex px-3 py-4 w-full\">\r\n                    <div class=\"w-[100px]\">\r\n                        <span class=\"font-bold\">\r\n                            備註\r\n                        </span>\r\n                    </div>\r\n                    <div class=\"w-full\">\r\n                        <textarea nbInput [(ngModel)]=\"remark\" [rows]=\"4\"\r\n                            class=\"resize-none !max-w-full w-full\"></textarea>\r\n                        <div class=\"mt-3 flex items-center\">\r\n                            <button nbButton class=\"btn border-black border mr-2\" (click)=\"goBack()\">取消</button>\r\n                            <button nbButton\r\n                                [disabled]=\"approvalWaiting.CIsApprove !== null && approvalWaiting.CIsApprove\"\r\n                                status=\"danger\" class=\"btn mr-2\" (click)=\"handleAction(false)\">駁回</button>\r\n                            <button nbButton\r\n                                [disabled]=\"approvalWaiting.CIsApprove !== null && approvalWaiting.CIsApprove\"\r\n                                status=\"primary\" class=\"btn mr-2\" (click)=\"handleAction(true)\">同意</button>\r\n                        </div>\r\n                        <div class=\"table-responsive relative overflow-x-auto mt-3\" *ngIf=\"!!approvalWaiting\">\r\n                            <table\r\n                                class=\"table table-bordered w-full text-sm text-left rtl:text-right text-gray-500 dark:text-gray-400\">\r\n                                <thead class=\"text-xs text-gray-700 uppercase bg-gray-50\">\r\n                                    <tr style=\"background-color: #27ae60; color: white;\">\r\n                                        <th scope=\"col\" class=\"px-6 py-3\">\r\n                                            日期\r\n                                        </th>\r\n                                        <th scope=\"col\" class=\"px-6 py-3\">\r\n                                            動作\r\n                                        </th>\r\n                                        <th scope=\"col\" class=\"px-6 py-3\">\r\n                                            使用者\r\n                                        </th>\r\n                                        <th scope=\"col\" class=\"px-6 py-3\">\r\n                                            備註\r\n                                        </th>\r\n                                    </tr>\r\n                                </thead>\r\n                                <tbody>\r\n                                    <tr class=\"bg-white text-black\"\r\n                                        *ngFor=\"let record of approvalWaiting.CApproveRecord\">\r\n                                        <th scope=\"row\" class=\"px-6 py-4 font-medium whitespace-nowrap\">\r\n                                            {{\r\n                                            (record.CRecordDate ? record.CRecordDate : approvalWaiting.CCreateDT) |\r\n                                            date: \"yyyy/MM/dd HH:mm:ss\"\r\n                                            }}\r\n                                        </th>\r\n                                        <td class=\"px-6 py-4\">\r\n                                            {{record.CAction === 1 ? '送出審核' : ''}}\r\n                                            {{record.CAction === 2 ? '審核通過' : ''}}\r\n                                            {{record.CAction === 3 ? '審核駁回' : ''}}\r\n                                        </td>\r\n                                        <td class=\"px-6 py-4\">\r\n                                            {{record.CCreator}}\r\n                                        </td>\r\n                                        <td class=\"px-6 py-4\">\r\n                                            {{record.CRemark}}\r\n                                        </td>\r\n                                    </tr>\r\n                                </tbody>\r\n                            </table>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        </div>\r\n    </nb-card-body>\r\n</nb-card>"], "mappings": "AAAA,SAASA,YAAY,QAAkB,iBAAiB;AAGxD,SAASC,GAAG,QAAQ,MAAM;AAG1B,SAASC,YAAY,QAAQ,gCAAgC;AAE7D,SAASC,gBAAgB,QAAQ,eAAe;AAChD,SAASC,mBAAmB,QAAQ,+CAA+C;AACnF,SAASC,WAAW,QAAQ,kCAAkC;AAI9D,SAASC,MAAM,QAAsB,uCAAuC;;;;;;;;;;;;;;;ICiC5CC,EADJ,CAAAC,cAAA,SAAuD,eAEyB;IAD1CD,EAAA,CAAAE,UAAA,mBAAAC,0EAAA;MAAA,MAAAC,OAAA,GAAAJ,EAAA,CAAAK,aAAA,CAAAC,GAAA,EAAAC,SAAA;MAAA,MAAAC,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASF,MAAA,CAAAG,YAAA,CAAAP,OAAA,CAAAQ,KAAA,EAAAR,OAAA,CAAAS,SAAA,CAAwC;IAAA,EAAC;IAEhFb,EAAA,CAAAc,MAAA,GACJ;IACJd,EADI,CAAAe,YAAA,EAAO,EACN;;;;IAHGf,EAAA,CAAAgB,SAAA,EAAuE;IAAvEhB,EAAA,CAAAiB,UAAA,YAAAb,OAAA,CAAAQ,KAAA,kDAAuE;IACvEZ,EAAA,CAAAgB,SAAA,EACJ;IADIhB,EAAA,CAAAkB,kBAAA,MAAAd,OAAA,CAAAS,SAAA,MACJ;;;;;IAyDQb,EAFJ,CAAAC,cAAA,aAC0D,aACU;IAC5DD,EAAA,CAAAc,MAAA,GAIJ;;IAAAd,EAAA,CAAAe,YAAA,EAAK;IACLf,EAAA,CAAAC,cAAA,aAAsB;IAClBD,EAAA,CAAAc,MAAA,GAGJ;IAAAd,EAAA,CAAAe,YAAA,EAAK;IACLf,EAAA,CAAAC,cAAA,aAAsB;IAClBD,EAAA,CAAAc,MAAA,GACJ;IAAAd,EAAA,CAAAe,YAAA,EAAK;IACLf,EAAA,CAAAC,cAAA,aAAsB;IAClBD,EAAA,CAAAc,MAAA,GACJ;IACJd,EADI,CAAAe,YAAA,EAAK,EACJ;;;;;IAhBGf,EAAA,CAAAgB,SAAA,GAIJ;IAJIhB,EAAA,CAAAkB,kBAAA,MAAAlB,EAAA,CAAAmB,WAAA,OAAAC,SAAA,CAAAC,WAAA,GAAAD,SAAA,CAAAC,WAAA,GAAAb,MAAA,CAAAc,eAAA,CAAAC,SAAA,8BAIJ;IAEIvB,EAAA,CAAAgB,SAAA,GAGJ;IAHIhB,EAAA,CAAAwB,kBAAA,MAAAJ,SAAA,CAAAK,OAAA,+CAAAL,SAAA,CAAAK,OAAA,+CAAAL,SAAA,CAAAK,OAAA,8CAGJ;IAEIzB,EAAA,CAAAgB,SAAA,GACJ;IADIhB,EAAA,CAAAkB,kBAAA,MAAAE,SAAA,CAAAM,QAAA,MACJ;IAEI1B,EAAA,CAAAgB,SAAA,GACJ;IADIhB,EAAA,CAAAkB,kBAAA,MAAAE,SAAA,CAAAO,OAAA,MACJ;;;;;IAjCA3B,EALhB,CAAAC,cAAA,cAAsF,gBAEwB,gBAC5C,aACD,aACf;IAC9BD,EAAA,CAAAc,MAAA,qBACJ;IAAAd,EAAA,CAAAe,YAAA,EAAK;IACLf,EAAA,CAAAC,cAAA,aAAkC;IAC9BD,EAAA,CAAAc,MAAA,qBACJ;IAAAd,EAAA,CAAAe,YAAA,EAAK;IACLf,EAAA,CAAAC,cAAA,aAAkC;IAC9BD,EAAA,CAAAc,MAAA,2BACJ;IAAAd,EAAA,CAAAe,YAAA,EAAK;IACLf,EAAA,CAAAC,cAAA,cAAkC;IAC9BD,EAAA,CAAAc,MAAA,sBACJ;IAERd,EAFQ,CAAAe,YAAA,EAAK,EACJ,EACD;IACRf,EAAA,CAAAC,cAAA,aAAO;IACHD,EAAA,CAAA4B,UAAA,KAAAC,0DAAA,kBAC0D;IAqBtE7B,EAFQ,CAAAe,YAAA,EAAQ,EACJ,EACN;;;;IArB6Bf,EAAA,CAAAgB,SAAA,IAAiC;IAAjChB,EAAA,CAAAiB,UAAA,YAAAT,MAAA,CAAAc,eAAA,CAAAQ,cAAA,CAAiC;;;;;;IArG5E9B,EAFR,CAAAC,cAAA,aAAsG,aACpD,cACV;IAC5BD,EAAA,CAAAc,MAAA,GACJ;IAAAd,EAAA,CAAAe,YAAA,EAAO;IAIKf,EAHZ,CAAAC,cAAA,aAAuB,aACY,aACJ,cACK;IACpBD,EAAA,CAAAc,MAAA,qBACJ;IACJd,EADI,CAAAe,YAAA,EAAO,EACL;IACNf,EAAA,CAAAC,cAAA,cAAkC;IAC9BD,EAAA,CAAAc,MAAA,IACJ;IACJd,EADI,CAAAe,YAAA,EAAO,EACL;IAGEf,EAFR,CAAAC,cAAA,cAAoC,cACT,eACK;IACpBD,EAAA,CAAAc,MAAA,sBACJ;IACJd,EADI,CAAAe,YAAA,EAAO,EACL;IACNf,EAAA,CAAAC,cAAA,eAAkC;IAC9BD,EAAA,CAAAc,MAAA,IACJ;;IACJd,EADI,CAAAe,YAAA,EAAO,EACL;IAGEf,EAFR,CAAAC,cAAA,cAAoC,cACT,eACK;IACpBD,EAAA,CAAAc,MAAA,sBACJ;IACJd,EADI,CAAAe,YAAA,EAAO,EACL;IACNf,EAAA,CAAAC,cAAA,eAAkC;IAC9BD,EAAA,CAAAc,MAAA,IACJ;IACJd,EADI,CAAAe,YAAA,EAAO,EACL;IAGEf,EAFR,CAAAC,cAAA,cAAoC,cACT,eACK;IACpBD,EAAA,CAAAc,MAAA,sBACJ;IACJd,EADI,CAAAe,YAAA,EAAO,EACL;IACNf,EAAA,CAAAC,cAAA,aAAgC;IAC5BD,EAAA,CAAA4B,UAAA,KAAAG,mDAAA,iBAAuD;IAO/D/B,EADI,CAAAe,YAAA,EAAK,EACH;IAGEf,EAFR,CAAAC,cAAA,cAAoC,cACT,eACK;IACpBD,EAAA,CAAAc,MAAA,kCACJ;IACJd,EADI,CAAAe,YAAA,EAAO,EACL;IACNf,EAAA,CAAAC,cAAA,eAAkC;IAC9BD,EAAA,CAAAc,MAAA,IACJ;IAGZd,EAHY,CAAAe,YAAA,EAAO,EACL,EACJ,EACJ;IAIMf,EAHZ,CAAAC,cAAA,eAAyB,eACc,cACR,eACK;IACpBD,EAAA,CAAAc,MAAA,sBACJ;IACJd,EADI,CAAAe,YAAA,EAAO,EACL;IAEFf,EADJ,CAAAC,cAAA,eAAoB,oBAE2B;IADzBD,EAAA,CAAAgC,gBAAA,2BAAAC,iFAAAC,MAAA;MAAAlC,EAAA,CAAAK,aAAA,CAAA8B,GAAA;MAAA,MAAA3B,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAAT,EAAA,CAAAoC,kBAAA,CAAA5B,MAAA,CAAA6B,MAAA,EAAAH,MAAA,MAAA1B,MAAA,CAAA6B,MAAA,GAAAH,MAAA;MAAA,OAAAlC,EAAA,CAAAU,WAAA,CAAAwB,MAAA;IAAA,EAAoB;IACKlC,EAAA,CAAAe,YAAA,EAAW;IAElDf,EADJ,CAAAC,cAAA,eAAoC,kBACyC;IAAnBD,EAAA,CAAAE,UAAA,mBAAAoC,uEAAA;MAAAtC,EAAA,CAAAK,aAAA,CAAA8B,GAAA;MAAA,MAAA3B,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASF,MAAA,CAAA+B,MAAA,EAAQ;IAAA,EAAC;IAACvC,EAAA,CAAAc,MAAA,oBAAE;IAAAd,EAAA,CAAAe,YAAA,EAAS;IACpFf,EAAA,CAAAC,cAAA,kBAEmE;IAA9BD,EAAA,CAAAE,UAAA,mBAAAsC,uEAAA;MAAAxC,EAAA,CAAAK,aAAA,CAAA8B,GAAA;MAAA,MAAA3B,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASF,MAAA,CAAAiC,YAAA,CAAa,KAAK,CAAC;IAAA,EAAC;IAACzC,EAAA,CAAAc,MAAA,oBAAE;IAAAd,EAAA,CAAAe,YAAA,EAAS;IAC9Ef,EAAA,CAAAC,cAAA,kBAEmE;IAA7BD,EAAA,CAAAE,UAAA,mBAAAwC,uEAAA;MAAA1C,EAAA,CAAAK,aAAA,CAAA8B,GAAA;MAAA,MAAA3B,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASF,MAAA,CAAAiC,YAAA,CAAa,IAAI,CAAC;IAAA,EAAC;IAACzC,EAAA,CAAAc,MAAA,oBAAE;IACzEd,EADyE,CAAAe,YAAA,EAAS,EAC5E;IACNf,EAAA,CAAA4B,UAAA,KAAAe,oDAAA,mBAAsF;IA8CtG3C,EAHY,CAAAe,YAAA,EAAM,EACJ,EACJ,EACJ;;;;IA7HMf,EAAA,CAAAgB,SAAA,GACJ;IADIhB,EAAA,CAAAkB,kBAAA,MAAAV,MAAA,CAAAc,eAAA,CAAAsB,KAAA,MACJ;IASY5C,EAAA,CAAAgB,SAAA,GACJ;IADIhB,EAAA,CAAAkB,kBAAA,MAAAV,MAAA,CAAAc,eAAA,CAAAuB,cAAA,MACJ;IASI7C,EAAA,CAAAgB,SAAA,GACJ;IADIhB,EAAA,CAAAkB,kBAAA,MAAAlB,EAAA,CAAA8C,WAAA,SAAAtC,MAAA,CAAAc,eAAA,CAAAyB,KAAA,OACJ;IASI/C,EAAA,CAAAgB,SAAA,GACJ;IADIhB,EAAA,CAAAkB,kBAAA,MAAAV,MAAA,CAAAc,eAAA,CAAAsB,KAAA,MACJ;IASyB5C,EAAA,CAAAgB,SAAA,GAAgC;IAAhChB,EAAA,CAAAiB,UAAA,YAAAT,MAAA,CAAAc,eAAA,CAAA0B,aAAA,CAAgC;IAerDhD,EAAA,CAAAgB,SAAA,GACJ;IADIhB,EAAA,CAAAkB,kBAAA,MAAAV,MAAA,CAAAc,eAAA,CAAA2B,eAAA,MACJ;IAYkBjD,EAAA,CAAAgB,SAAA,GAAoB;IAApBhB,EAAA,CAAAkD,gBAAA,YAAA1C,MAAA,CAAA6B,MAAA,CAAoB;IAACrC,EAAA,CAAAiB,UAAA,WAAU;IAKzCjB,EAAA,CAAAgB,SAAA,GAA8E;IAA9EhB,EAAA,CAAAiB,UAAA,aAAAT,MAAA,CAAAc,eAAA,CAAA6B,UAAA,aAAA3C,MAAA,CAAAc,eAAA,CAAA6B,UAAA,CAA8E;IAG9EnD,EAAA,CAAAgB,SAAA,GAA8E;IAA9EhB,EAAA,CAAAiB,UAAA,aAAAT,MAAA,CAAAc,eAAA,CAAA6B,UAAA,aAAA3C,MAAA,CAAAc,eAAA,CAAA6B,UAAA,CAA8E;IAGzBnD,EAAA,CAAAgB,SAAA,GAAuB;IAAvBhB,EAAA,CAAAiB,UAAA,WAAAT,MAAA,CAAAc,eAAA,CAAuB;;;AD1D5G,OAAM,MAAO8B,8BAA8B;EAWzCC,YACUC,qBAA2C,EAC3CC,eAA+B,EAC/BC,gBAAgC,EAChCC,SAAmB,EACnBC,OAAuB,EACvBC,iBAAmC,EACnCC,aAA2B;IAN3B,KAAAN,qBAAqB,GAArBA,qBAAqB;IACrB,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,SAAS,GAATA,SAAS;IACT,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAC,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,aAAa,GAAbA,aAAa;IAhBvB,KAAAb,KAAK,GAAW,CAAC;IAEjB,KAAAV,MAAM,GAAW,EAAE;IAgBjB,IAAI,CAACwB,SAAS,GAAGjE,gBAAgB,CAACC,mBAAmB,CAACiE,eAAe,CAAChE,WAAW,CAACiE,KAAK,CAAC,CAAC;IACzF,IAAI,CAACC,GAAG,GAAGC,QAAQ,CAAC,IAAI,CAACV,eAAe,CAACW,QAAQ,CAACC,QAAS,CAACC,GAAG,CAAC,IAAI,CAAE,CAAC;IACvE,IAAI,CAACC,WAAW,GAAGJ,QAAQ,CAAC,IAAI,CAACV,eAAe,CAACW,QAAQ,CAACC,QAAS,CAACC,GAAG,CAAC,aAAa,CAAE,CAAC;IACxF,IAAI,CAACb,eAAe,CAACe,WAAW,CAACC,IAAI,CACnC7E,GAAG,CAAC8E,CAAC,IAAG;MACN,IAAI,CAACzB,KAAK,GAAGyB,CAAC,CAAC,MAAM,CAAC;IACxB,CAAC,CAAC,CACH,CAACC,SAAS,EAAE;EACf;EAEAC,QAAQA,CAAA;IACN,IAAI,CAACC,sBAAsB,EAAE;EAC/B;EAEAA,sBAAsBA,CAAA;IACpB,IAAI,CAACrB,qBAAqB,CAACsB,8CAA8C,CAAC;MACxEC,IAAI,EAAE;QACJb,GAAG,EAAE,IAAI,CAACA,GAAG;QACbjB,KAAK,EAAE,IAAI,CAACA,KAAK,CAAC+B,QAAQ;;KAE7B,CAAC,CAACP,IAAI,CACL7E,GAAG,CAACqF,GAAG,IAAG;MACR,IAAIA,GAAG,CAACC,UAAU,KAAK,CAAC,EAAE;QACxB,IAAI,CAAC1D,eAAe,GAAGyD,GAAG,CAACE,OAAQ;MACrC;IACF,CAAC,CAAC,CACH,CAACR,SAAS,EAAE;EACf;EAEA9D,YAAYA,CAACC,KAAU,EAAEC,SAAc;IACrC;IACA;IACA;IACA;IACA;IACAqE,MAAM,CAACC,IAAI,CAACvE,KAAK,EAAE,QAAQ,CAAC;EAC9B;EAEA6B,YAAYA,CAAC2C,SAAkB;IAC7B,IAAI,CAACA,SAAS,EAAE;MACd,IAAI,CAACC,UAAU,EAAE;MACjB,IAAI,IAAI,CAAC1B,iBAAiB,CAAC2B,aAAa,CAACC,MAAM,GAAG,CAAC,EAAE;QACnD,IAAI,CAAC7B,OAAO,CAAC8B,aAAa,CAAC,IAAI,CAAC7B,iBAAiB,CAAC2B,aAAa,CAAC;QAChE;MACF;IACF;IACA,IAAI,CAAChC,qBAAqB,CAACmC,6CAA6C,CAAC;MACvEZ,IAAI,EAAE;QACJb,GAAG,EAAE,IAAI,CAACA,GAAG;QACbjB,KAAK,EAAE,IAAI,CAACA,KAAK;QACjBI,UAAU,EAAEiC,SAAS;QACrBzD,OAAO,EAAE,IAAI,CAACU;;KAEjB,CAAC,CAACkC,IAAI,CACL7E,GAAG,CAACqF,GAAG,IAAG;MACR,IAAIA,GAAG,CAACC,UAAU,KAAK,CAAC,EAAE;QACxB,IAAI,CAACtB,OAAO,CAACgC,aAAa,CAAC,MAAM,CAAC;QAClC,IAAI,CAACf,sBAAsB,EAAE;QAC7B,IAAI,IAAI,CAACrD,eAAe,CAACQ,cAAc,EAAEyD,MAAM,IAAI,CAAC,EAAE;UACpD,IAAI,CAACjE,eAAe,CAACQ,cAAc,EAAE6D,IAAI,CAAC;YACxCjE,QAAQ,EAAE,IAAI,CAACmC,SAAS,CAAC+B,QAAQ;YACjCvE,WAAW,EAAE,IAAIwE,IAAI,EAAE,CAACC,WAAW,EAAE;YACrCnE,OAAO,EAAE,IAAI,CAACU;WACf,CAAC;QACJ;QACA,IAAI,CAACA,MAAM,GAAG,EAAE;MAClB;MACA,IAAI,CAACE,MAAM,EAAE;IACf,CAAC,CAAC,CACH,CAACkC,SAAS,EAAE;EACf;EAEAlC,MAAMA,CAAA;IACJ,IAAI,CAACqB,aAAa,CAAC+B,IAAI,CAAC;MACtBI,MAAM;MACNC,OAAO,EAAE,IAAI,CAAC3B;KACf,CAAC;IACF,IAAI,CAACZ,SAAS,CAACwC,IAAI,EAAE;EACvB;EAEAZ,UAAUA,CAAA;IACR,IAAI,CAAC1B,iBAAiB,CAACuC,KAAK,EAAE;IAC9B,IAAI,CAACvC,iBAAiB,CAACwC,QAAQ,CAAC,MAAM,EAAE,IAAI,CAAC9D,MAAM,CAAC;EACtD;;;uCAvGWe,8BAA8B,EAAApD,EAAA,CAAAoG,iBAAA,CAAAC,EAAA,CAAAC,oBAAA,GAAAtG,EAAA,CAAAoG,iBAAA,CAAAG,EAAA,CAAAC,cAAA,GAAAxG,EAAA,CAAAoG,iBAAA,CAAAK,EAAA,CAAAC,cAAA,GAAA1G,EAAA,CAAAoG,iBAAA,CAAAO,EAAA,CAAAC,QAAA,GAAA5G,EAAA,CAAAoG,iBAAA,CAAAS,EAAA,CAAAC,cAAA,GAAA9G,EAAA,CAAAoG,iBAAA,CAAAW,EAAA,CAAAC,gBAAA,GAAAhH,EAAA,CAAAoG,iBAAA,CAAAa,EAAA,CAAAC,YAAA;IAAA;EAAA;;;YAA9B9D,8BAA8B;MAAA+D,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAArH,EAAA,CAAAsH,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,wCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCzBvC5H,EAFJ,CAAAC,cAAA,cAAS,mBAES;UACVD,EAAA,CAAA4B,UAAA,IAAAkG,6CAAA,mBAAsG;UAkI9G9H,EADI,CAAAe,YAAA,EAAe,EACT;;;UAlIIf,EAAA,CAAAgB,SAAA,GAAuB;UAAvBhB,EAAA,CAAAiB,UAAA,WAAA4G,GAAA,CAAAvG,eAAA,CAAuB;;;qBDiBjC7B,YAAY,EAAAkH,EAAA,CAAAoB,OAAA,EAAApB,EAAA,CAAAqB,OAAA,EAAArB,EAAA,CAAAsB,IAAA,EAAAtB,EAAA,CAAAuB,QAAA,EACZvI,YAAY,EAAAwI,EAAA,CAAAC,oBAAA,EAAAD,EAAA,CAAAE,eAAA,EAAAF,EAAA,CAAAG,OAAA,EAAAC,EAAA,CAAAC,eAAA,EAAAD,EAAA,CAAAE,mBAAA,EAAAF,EAAA,CAAAG,gBAAA,EAAAH,EAAA,CAAAI,iBAAA,EAAAC,GAAA,CAAAC,mBAAA;MAAAC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}