{"ast": null, "code": "export class Page {\n  constructor() {\n    this.PageIndex = 1;\n    this.PageSize = 10;\n  }\n}", "map": {"version": 3, "names": ["Page", "constructor", "PageIndex", "PageSize"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\shared\\model\\page.model.ts"], "sourcesContent": ["export class Page {\r\n  PageIndex = 1;\r\n  PageSize = 10;\r\n}\r\n"], "mappings": "AAAA,OAAM,MAAOA,IAAI;EAAjBC,YAAA;IACE,KAAAC,SAAS,GAAG,CAAC;IACb,KAAAC,QAAQ,GAAG,EAAE;EACf", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}