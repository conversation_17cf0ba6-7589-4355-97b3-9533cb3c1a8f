{"ast": null, "code": "import { map } from 'rxjs/operators';\nimport { BaseService } from '../base-service';\nimport { apiBuildCaseFileDeleteBuildCaseFilePost$Json } from '../fn/build-case-file/api-build-case-file-delete-build-case-file-post-json';\nimport { apiBuildCaseFileDeleteBuildCaseFilePost$Plain } from '../fn/build-case-file/api-build-case-file-delete-build-case-file-post-plain';\nimport { apiBuildCaseFileGetBuildCaseFileByIdPost$Json } from '../fn/build-case-file/api-build-case-file-get-build-case-file-by-id-post-json';\nimport { apiBuildCaseFileGetBuildCaseFileByIdPost$Plain } from '../fn/build-case-file/api-build-case-file-get-build-case-file-by-id-post-plain';\nimport { apiBuildCaseFileGetListBuildCaseFilePost$Json } from '../fn/build-case-file/api-build-case-file-get-list-build-case-file-post-json';\nimport { apiBuildCaseFileGetListBuildCaseFilePost$Plain } from '../fn/build-case-file/api-build-case-file-get-list-build-case-file-post-plain';\nimport { apiBuildCaseFileSaveBuildCaseFilePost$Json } from '../fn/build-case-file/api-build-case-file-save-build-case-file-post-json';\nimport { apiBuildCaseFileSaveBuildCaseFilePost$Plain } from '../fn/build-case-file/api-build-case-file-save-build-case-file-post-plain';\nimport { apiBuildCaseFileSaveMultipleBuildCaseFilePost$Json } from '../fn/build-case-file/api-build-case-file-save-multiple-build-case-file-post-json';\nimport { apiBuildCaseFileSaveMultipleBuildCaseFilePost$Plain } from '../fn/build-case-file/api-build-case-file-save-multiple-build-case-file-post-plain';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../api-configuration\";\nimport * as i2 from \"@angular/common/http\";\nexport let BuildCaseFileService = /*#__PURE__*/(() => {\n  class BuildCaseFileService extends BaseService {\n    constructor(config, http) {\n      super(config, http);\n    }\n    /** Path part for operation `apiBuildCaseFileGetListBuildCaseFilePost()` */\n    static {\n      this.ApiBuildCaseFileGetListBuildCaseFilePostPath = '/api/BuildCaseFile/GetListBuildCaseFile';\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiBuildCaseFileGetListBuildCaseFilePost$Plain()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiBuildCaseFileGetListBuildCaseFilePost$Plain$Response(params, context) {\n      return apiBuildCaseFileGetListBuildCaseFilePost$Plain(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiBuildCaseFileGetListBuildCaseFilePost$Plain$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiBuildCaseFileGetListBuildCaseFilePost$Plain(params, context) {\n      return this.apiBuildCaseFileGetListBuildCaseFilePost$Plain$Response(params, context).pipe(map(r => r.body));\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiBuildCaseFileGetListBuildCaseFilePost$Json()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiBuildCaseFileGetListBuildCaseFilePost$Json$Response(params, context) {\n      return apiBuildCaseFileGetListBuildCaseFilePost$Json(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiBuildCaseFileGetListBuildCaseFilePost$Json$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiBuildCaseFileGetListBuildCaseFilePost$Json(params, context) {\n      return this.apiBuildCaseFileGetListBuildCaseFilePost$Json$Response(params, context).pipe(map(r => r.body));\n    }\n    /** Path part for operation `apiBuildCaseFileGetBuildCaseFileByIdPost()` */\n    static {\n      this.ApiBuildCaseFileGetBuildCaseFileByIdPostPath = '/api/BuildCaseFile/GetBuildCaseFileByID';\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiBuildCaseFileGetBuildCaseFileByIdPost$Plain()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiBuildCaseFileGetBuildCaseFileByIdPost$Plain$Response(params, context) {\n      return apiBuildCaseFileGetBuildCaseFileByIdPost$Plain(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiBuildCaseFileGetBuildCaseFileByIdPost$Plain$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiBuildCaseFileGetBuildCaseFileByIdPost$Plain(params, context) {\n      return this.apiBuildCaseFileGetBuildCaseFileByIdPost$Plain$Response(params, context).pipe(map(r => r.body));\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiBuildCaseFileGetBuildCaseFileByIdPost$Json()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiBuildCaseFileGetBuildCaseFileByIdPost$Json$Response(params, context) {\n      return apiBuildCaseFileGetBuildCaseFileByIdPost$Json(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiBuildCaseFileGetBuildCaseFileByIdPost$Json$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiBuildCaseFileGetBuildCaseFileByIdPost$Json(params, context) {\n      return this.apiBuildCaseFileGetBuildCaseFileByIdPost$Json$Response(params, context).pipe(map(r => r.body));\n    }\n    /** Path part for operation `apiBuildCaseFileDeleteBuildCaseFilePost()` */\n    static {\n      this.ApiBuildCaseFileDeleteBuildCaseFilePostPath = '/api/BuildCaseFile/DeleteBuildCaseFile';\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiBuildCaseFileDeleteBuildCaseFilePost$Plain()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiBuildCaseFileDeleteBuildCaseFilePost$Plain$Response(params, context) {\n      return apiBuildCaseFileDeleteBuildCaseFilePost$Plain(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiBuildCaseFileDeleteBuildCaseFilePost$Plain$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiBuildCaseFileDeleteBuildCaseFilePost$Plain(params, context) {\n      return this.apiBuildCaseFileDeleteBuildCaseFilePost$Plain$Response(params, context).pipe(map(r => r.body));\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiBuildCaseFileDeleteBuildCaseFilePost$Json()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiBuildCaseFileDeleteBuildCaseFilePost$Json$Response(params, context) {\n      return apiBuildCaseFileDeleteBuildCaseFilePost$Json(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiBuildCaseFileDeleteBuildCaseFilePost$Json$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiBuildCaseFileDeleteBuildCaseFilePost$Json(params, context) {\n      return this.apiBuildCaseFileDeleteBuildCaseFilePost$Json$Response(params, context).pipe(map(r => r.body));\n    }\n    /** Path part for operation `apiBuildCaseFileSaveBuildCaseFilePost()` */\n    static {\n      this.ApiBuildCaseFileSaveBuildCaseFilePostPath = '/api/BuildCaseFile/SaveBuildCaseFile';\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiBuildCaseFileSaveBuildCaseFilePost$Plain()` instead.\n     *\n     * This method sends `multipart/form-data` and handles request body of type `multipart/form-data`.\n     */\n    apiBuildCaseFileSaveBuildCaseFilePost$Plain$Response(params, context) {\n      return apiBuildCaseFileSaveBuildCaseFilePost$Plain(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiBuildCaseFileSaveBuildCaseFilePost$Plain$Response()` instead.\n     *\n     * This method sends `multipart/form-data` and handles request body of type `multipart/form-data`.\n     */\n    apiBuildCaseFileSaveBuildCaseFilePost$Plain(params, context) {\n      return this.apiBuildCaseFileSaveBuildCaseFilePost$Plain$Response(params, context).pipe(map(r => r.body));\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiBuildCaseFileSaveBuildCaseFilePost$Json()` instead.\n     *\n     * This method sends `multipart/form-data` and handles request body of type `multipart/form-data`.\n     */\n    apiBuildCaseFileSaveBuildCaseFilePost$Json$Response(params, context) {\n      return apiBuildCaseFileSaveBuildCaseFilePost$Json(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiBuildCaseFileSaveBuildCaseFilePost$Json$Response()` instead.\n     *\n     * This method sends `multipart/form-data` and handles request body of type `multipart/form-data`.\n     */\n    apiBuildCaseFileSaveBuildCaseFilePost$Json(params, context) {\n      return this.apiBuildCaseFileSaveBuildCaseFilePost$Json$Response(params, context).pipe(map(r => r.body));\n    }\n    /** Path part for operation `apiBuildCaseFileSaveMultipleBuildCaseFilePost()` */\n    static {\n      this.ApiBuildCaseFileSaveMultipleBuildCaseFilePostPath = '/api/BuildCaseFile/SaveMultipleBuildCaseFile';\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiBuildCaseFileSaveMultipleBuildCaseFilePost$Plain()` instead.\n     *\n     * This method sends `multipart/form-data` and handles request body of type `multipart/form-data`.\n     */\n    apiBuildCaseFileSaveMultipleBuildCaseFilePost$Plain$Response(params, context) {\n      return apiBuildCaseFileSaveMultipleBuildCaseFilePost$Plain(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiBuildCaseFileSaveMultipleBuildCaseFilePost$Plain$Response()` instead.\n     *\n     * This method sends `multipart/form-data` and handles request body of type `multipart/form-data`.\n     */\n    apiBuildCaseFileSaveMultipleBuildCaseFilePost$Plain(params, context) {\n      return this.apiBuildCaseFileSaveMultipleBuildCaseFilePost$Plain$Response(params, context).pipe(map(r => r.body));\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiBuildCaseFileSaveMultipleBuildCaseFilePost$Json()` instead.\n     *\n     * This method sends `multipart/form-data` and handles request body of type `multipart/form-data`.\n     */\n    apiBuildCaseFileSaveMultipleBuildCaseFilePost$Json$Response(params, context) {\n      return apiBuildCaseFileSaveMultipleBuildCaseFilePost$Json(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiBuildCaseFileSaveMultipleBuildCaseFilePost$Json$Response()` instead.\n     *\n     * This method sends `multipart/form-data` and handles request body of type `multipart/form-data`.\n     */\n    apiBuildCaseFileSaveMultipleBuildCaseFilePost$Json(params, context) {\n      return this.apiBuildCaseFileSaveMultipleBuildCaseFilePost$Json$Response(params, context).pipe(map(r => r.body));\n    }\n    static {\n      this.ɵfac = function BuildCaseFileService_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || BuildCaseFileService)(i0.ɵɵinject(i1.ApiConfiguration), i0.ɵɵinject(i2.HttpClient));\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: BuildCaseFileService,\n        factory: BuildCaseFileService.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return BuildCaseFileService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}