{"ast": null, "code": "import { ApproveWaitingComponent } from '../approve-waiting/approve-waiting.component';\nimport * as i0 from \"@angular/core\";\nexport class ApproveWaiting3Component {\n  static {\n    this.ɵfac = function ApproveWaiting3Component_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || ApproveWaiting3Component)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ApproveWaiting3Component,\n      selectors: [[\"app-approve-waiting3\"]],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 1,\n      vars: 1,\n      consts: [[3, \"type\"]],\n      template: function ApproveWaiting3Component_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelement(0, \"app-approve-waiting\", 0);\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"type\", 3);\n        }\n      },\n      dependencies: [ApproveWaitingComponent],\n      styles: [\"[_nghost-%COMP%] {\\n  display: block;\\n}\\n\\n/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImFwcHJvdmUtd2FpdGluZzMuY29tcG9uZW50LmNzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFBQTtFQUNFLGNBQWM7QUFDaEIiLCJmaWxlIjoiYXBwcm92ZS13YWl0aW5nMy5jb21wb25lbnQuY3NzIiwic291cmNlc0NvbnRlbnQiOlsiOmhvc3Qge1xyXG4gIGRpc3BsYXk6IGJsb2NrO1xyXG59XHJcbiJdfQ== */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvcGFnZXMvYXBwcm92ZS13YWl0aW5nMy9hcHByb3ZlLXdhaXRpbmczLmNvbXBvbmVudC5jc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUE7RUFDRSxjQUFjO0FBQ2hCOztBQUVBLG9VQUFvVSIsInNvdXJjZXNDb250ZW50IjpbIjpob3N0IHtcclxuICBkaXNwbGF5OiBibG9jaztcclxufVxyXG4iXSwic291cmNlUm9vdCI6IiJ9 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["ApproveWaitingComponent", "ApproveWaiting3Component", "selectors", "standalone", "features", "i0", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "ApproveWaiting3Component_Template", "rf", "ctx", "ɵɵelement", "ɵɵproperty", "styles"], "sources": ["C:\\Users\\<USER>\\Documents\\jeansalechange\\adminFront\\src\\app\\pages\\approve-waiting3\\approve-waiting3.component.ts", "C:\\Users\\<USER>\\Documents\\jeansalechange\\adminFront\\src\\app\\pages\\approve-waiting3\\approve-waiting3.component.html"], "sourcesContent": ["import { ChangeDetectionStrategy, Component } from '@angular/core';\r\nimport { ApproveWaitingComponent } from '../approve-waiting/approve-waiting.component';\r\n\r\n@Component({\r\n  selector: 'app-approve-waiting3',\r\n  standalone: true,\r\n  imports: [ApproveWaitingComponent],\r\n  templateUrl: './approve-waiting3.component.html',\r\n  styleUrl: './approve-waiting3.component.css',\r\n})\r\nexport class ApproveWaiting3Component { }\r\n", "<app-approve-waiting [type]=\"3\"></app-approve-waiting>"], "mappings": "AACA,SAASA,uBAAuB,QAAQ,8CAA8C;;AAStF,OAAM,MAAOC,wBAAwB;;;uCAAxBA,wBAAwB;IAAA;EAAA;;;YAAxBA,wBAAwB;MAAAC,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAAC,EAAA,CAAAC,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,kCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCVrCP,EAAA,CAAAS,SAAA,6BAAsD;;;UAAjCT,EAAA,CAAAU,UAAA,WAAU;;;qBDMnBf,uBAAuB;MAAAgB,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}