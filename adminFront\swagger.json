{"openapi": "3.0.1", "info": {"title": "App.Api", "version": "1.0"}, "paths": {"/api/BaseFunction/GetStatus": {"post": {"tags": ["BaseFunction"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/EnumArgs"}}, "text/json": {"schema": {"$ref": "#/components/schemas/EnumArgs"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/EnumArgs"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/EnumResponseListResponseBase"}}, "application/json": {"schema": {"$ref": "#/components/schemas/EnumResponseListResponseBase"}}, "text/json": {"schema": {"$ref": "#/components/schemas/EnumResponseListResponseBase"}}}}}}}, "/api/BaseFunction/GetFunction": {"post": {"tags": ["BaseFunction"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/EnumArgs"}}, "text/json": {"schema": {"$ref": "#/components/schemas/EnumArgs"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/EnumArgs"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/EnumResponseListResponseBase"}}, "application/json": {"schema": {"$ref": "#/components/schemas/EnumResponseListResponseBase"}}, "text/json": {"schema": {"$ref": "#/components/schemas/EnumResponseListResponseBase"}}}}}}}, "/api/BuildCase/GetBuildCaseList": {"post": {"tags": ["BuildCase"], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/BuildCaseGetListReponseListResponseBase"}}, "application/json": {"schema": {"$ref": "#/components/schemas/BuildCaseGetListReponseListResponseBase"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BuildCaseGetListReponseListResponseBase"}}}}}}}, "/api/BuildCase/GetAnnouncement": {"post": {"tags": ["BuildCase"], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/StringResponseBase"}}, "application/json": {"schema": {"$ref": "#/components/schemas/StringResponseBase"}}, "text/json": {"schema": {"$ref": "#/components/schemas/StringResponseBase"}}}}}}}, "/api/BuildCase/GetBuildCaseFile": {"post": {"tags": ["BuildCase"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BuildCaseGetFileArgs"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BuildCaseGetFileArgs"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/BuildCaseGetFileArgs"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/BuildCaseGetFileResponeListResponseBase"}}, "application/json": {"schema": {"$ref": "#/components/schemas/BuildCaseGetFileResponeListResponseBase"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BuildCaseGetFileResponeListResponseBase"}}}}}}}, "/api/BuildCase/GetDisclaimer": {"post": {"tags": ["BuildCase"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetDisclaimerArgs"}}, "text/json": {"schema": {"$ref": "#/components/schemas/GetDisclaimerArgs"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/GetDisclaimerArgs"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/StringResponseBase"}}, "application/json": {"schema": {"$ref": "#/components/schemas/StringResponseBase"}}, "text/json": {"schema": {"$ref": "#/components/schemas/StringResponseBase"}}}}}}}, "/api/BuildCase/GetSystemInstruction": {"post": {"tags": ["BuildCase"], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/StringResponseBase"}}, "application/json": {"schema": {"$ref": "#/components/schemas/StringResponseBase"}}, "text/json": {"schema": {"$ref": "#/components/schemas/StringResponseBase"}}}}}}}, "/api/BuildCase/GetHouseAndFloorByBuildCaseId": {"post": {"tags": ["BuildCase"], "parameters": [{"name": "buildCaseId", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/GetHouseAndFloorByBuildCaseIdResListResponseBase"}}, "application/json": {"schema": {"$ref": "#/components/schemas/GetHouseAndFloorByBuildCaseIdResListResponseBase"}}, "text/json": {"schema": {"$ref": "#/components/schemas/GetHouseAndFloorByBuildCaseIdResListResponseBase"}}}}}}}, "/api/BuildCase/GetUserBuildCase": {"post": {"tags": ["BuildCase"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetUserBuildCaseArgs"}}, "text/json": {"schema": {"$ref": "#/components/schemas/GetUserBuildCaseArgs"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/GetUserBuildCaseArgs"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/BuildCaseGetListReponseListResponseBase"}}, "application/json": {"schema": {"$ref": "#/components/schemas/BuildCaseGetListReponseListResponseBase"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BuildCaseGetListReponseListResponseBase"}}}}}}}, "/api/BuildCase/GetAllBuildCase": {"post": {"tags": ["BuildCase"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetAllBuildCaseArgs"}}, "text/json": {"schema": {"$ref": "#/components/schemas/GetAllBuildCaseArgs"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/GetAllBuildCaseArgs"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/BuildCaseGetListReponseListResponseBase"}}, "application/json": {"schema": {"$ref": "#/components/schemas/BuildCaseGetListReponseListResponseBase"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BuildCaseGetListReponseListResponseBase"}}}}}}}, "/api/BuildCase/DeleteBuildCase": {"post": {"tags": ["BuildCase"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DeleteBuildCaseArgs"}}, "text/json": {"schema": {"$ref": "#/components/schemas/DeleteBuildCaseArgs"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/DeleteBuildCaseArgs"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/StringResponseBase"}}, "application/json": {"schema": {"$ref": "#/components/schemas/StringResponseBase"}}, "text/json": {"schema": {"$ref": "#/components/schemas/StringResponseBase"}}}}}}}, "/api/BuildCase/GetBuildCaseByID": {"post": {"tags": ["BuildCase"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetBuildCaseByID"}}, "text/json": {"schema": {"$ref": "#/components/schemas/GetBuildCaseByID"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/GetBuildCaseByID"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/BuildCaseGetListReponseResponseBase"}}, "application/json": {"schema": {"$ref": "#/components/schemas/BuildCaseGetListReponseResponseBase"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BuildCaseGetListReponseResponseBase"}}}}}}}, "/api/BuildCase/SaveBuildCase": {"post": {"tags": ["BuildCase"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SaveBuildCaseArgs"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SaveBuildCaseArgs"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/SaveBuildCaseArgs"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/StringResponseBase"}}, "application/json": {"schema": {"$ref": "#/components/schemas/StringResponseBase"}}, "text/json": {"schema": {"$ref": "#/components/schemas/StringResponseBase"}}}}}}}, "/api/BuildCase/GetAllBuildCaseForSelect": {"post": {"tags": ["BuildCase"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetAllBuildCaseArgs"}}, "text/json": {"schema": {"$ref": "#/components/schemas/GetAllBuildCaseArgs"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/GetAllBuildCaseArgs"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/BuildCaseGetListReponseListResponseBase"}}, "application/json": {"schema": {"$ref": "#/components/schemas/BuildCaseGetListReponseListResponseBase"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BuildCaseGetListReponseListResponseBase"}}}}}}}, "/api/BuildCaseFile/GetListBuildCaseFile": {"post": {"tags": ["BuildCaseFile"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetListBuildCaseFileArgs"}}, "text/json": {"schema": {"$ref": "#/components/schemas/GetListBuildCaseFileArgs"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/GetListBuildCaseFileArgs"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/BuildCaseFileResListResponseBase"}}, "application/json": {"schema": {"$ref": "#/components/schemas/BuildCaseFileResListResponseBase"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BuildCaseFileResListResponseBase"}}}}}}}, "/api/BuildCaseFile/GetBuildCaseFileByID": {"post": {"tags": ["BuildCaseFile"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetBuildCaseFileByID"}}, "text/json": {"schema": {"$ref": "#/components/schemas/GetBuildCaseFileByID"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/GetBuildCaseFileByID"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/BuildCaseFileResResponseBase"}}, "application/json": {"schema": {"$ref": "#/components/schemas/BuildCaseFileResResponseBase"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BuildCaseFileResResponseBase"}}}}}}}, "/api/BuildCaseFile/DeleteBuildCaseFile": {"post": {"tags": ["BuildCaseFile"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetBuildCaseFileByID"}}, "text/json": {"schema": {"$ref": "#/components/schemas/GetBuildCaseFileByID"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/GetBuildCaseFileByID"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/StringResponseBase"}}, "application/json": {"schema": {"$ref": "#/components/schemas/StringResponseBase"}}, "text/json": {"schema": {"$ref": "#/components/schemas/StringResponseBase"}}}}}}}, "/api/BuildCaseFile/SaveBuildCaseFile": {"post": {"tags": ["BuildCaseFile"], "requestBody": {"content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"CBuildCaseID": {"type": "integer", "format": "int32"}, "CBuildCaseFileID": {"type": "integer", "format": "int32"}, "CStatus": {"type": "integer", "format": "int32"}, "CFile": {"type": "string", "format": "binary"}, "CCategoryName": {"type": "string"}, "CSubmitRemark": {"type": "string"}}}, "encoding": {"CBuildCaseID": {"style": "form"}, "CBuildCaseFileID": {"style": "form"}, "CStatus": {"style": "form"}, "CFile": {"style": "form"}, "CCategoryName": {"style": "form"}, "CSubmitRemark": {"style": "form"}}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/StringResponseBase"}}, "application/json": {"schema": {"$ref": "#/components/schemas/StringResponseBase"}}, "text/json": {"schema": {"$ref": "#/components/schemas/StringResponseBase"}}}}}}}, "/api/BuildCaseFile/SaveMultipleBuildCaseFile": {"post": {"tags": ["BuildCaseFile"], "requestBody": {"content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"CBuildCaseID": {"type": "integer", "format": "int32"}, "CCategoryName": {"type": "string"}, "CFiles": {"type": "array", "items": {"type": "string", "format": "binary"}}}}, "encoding": {"CBuildCaseID": {"style": "form"}, "CCategoryName": {"style": "form"}, "CFiles": {"style": "form"}}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/StringResponseBase"}}, "application/json": {"schema": {"$ref": "#/components/schemas/StringResponseBase"}}, "text/json": {"schema": {"$ref": "#/components/schemas/StringResponseBase"}}}}}}}, "/api/BuildCaseMail/SaveBuildCaseMail": {"post": {"tags": ["BuildCaseMail"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SaveBuildCaseMailRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SaveBuildCaseMailRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/SaveBuildCaseMailRequest"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/StringResponseBase"}}, "application/json": {"schema": {"$ref": "#/components/schemas/StringResponseBase"}}, "text/json": {"schema": {"$ref": "#/components/schemas/StringResponseBase"}}}}}}}, "/api/BuildCaseMail/GetBuildCaseMailList": {"post": {"tags": ["BuildCaseMail"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetBuildCaseMailListRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/GetBuildCaseMailListRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/GetBuildCaseMailListRequest"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/GetBuildCaseMailListResponseListResponseBase"}}, "application/json": {"schema": {"$ref": "#/components/schemas/GetBuildCaseMailListResponseListResponseBase"}}, "text/json": {"schema": {"$ref": "#/components/schemas/GetBuildCaseMailListResponseListResponseBase"}}}}}}}, "/api/BuildCaseMail/DeleteBuildCaseMail": {"post": {"tags": ["BuildCaseMail"], "requestBody": {"content": {"application/json": {"schema": {"type": "integer", "format": "int32"}}, "text/json": {"schema": {"type": "integer", "format": "int32"}}, "application/*+json": {"schema": {"type": "integer", "format": "int32"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/StringResponseBase"}}, "application/json": {"schema": {"$ref": "#/components/schemas/StringResponseBase"}}, "text/json": {"schema": {"$ref": "#/components/schemas/StringResponseBase"}}}}}}}, "/api/FinalDocument/GetFinalDocBefore": {"post": {"tags": ["FinalDocument"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetFinalDocBefore"}}, "text/json": {"schema": {"$ref": "#/components/schemas/GetFinalDocBefore"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/GetFinalDocBefore"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/GetFinalDocResListResponseBase"}}, "application/json": {"schema": {"$ref": "#/components/schemas/GetFinalDocResListResponseBase"}}, "text/json": {"schema": {"$ref": "#/components/schemas/GetFinalDocResListResponseBase"}}}}}}}, "/api/FinalDocument/UpdateSign": {"post": {"tags": ["FinalDocument"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateSignArgs"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateSignArgs"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateSignArgs"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/StringResponseBase"}}, "application/json": {"schema": {"$ref": "#/components/schemas/StringResponseBase"}}, "text/json": {"schema": {"$ref": "#/components/schemas/StringResponseBase"}}}}}}}, "/api/FinalDocument/GetFinalDocAfter": {"post": {"tags": ["FinalDocument"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetFinalDocAfter"}}, "text/json": {"schema": {"$ref": "#/components/schemas/GetFinalDocAfter"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/GetFinalDocAfter"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/GetFinalDocResListResponseBase"}}, "application/json": {"schema": {"$ref": "#/components/schemas/GetFinalDocResListResponseBase"}}, "text/json": {"schema": {"$ref": "#/components/schemas/GetFinalDocResListResponseBase"}}}}}}}, "/api/FinalDocument/GetListFinalDoc": {"post": {"tags": ["FinalDocument"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetListFinalDocArgs"}}, "text/json": {"schema": {"$ref": "#/components/schemas/GetListFinalDocArgs"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/GetListFinalDocArgs"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/GetListFinalDocResListResponseBase"}}, "application/json": {"schema": {"$ref": "#/components/schemas/GetListFinalDocResListResponseBase"}}, "text/json": {"schema": {"$ref": "#/components/schemas/GetListFinalDocResListResponseBase"}}}}}}}, "/api/FinalDocument/CreateFinalDoc": {"post": {"tags": ["FinalDocument"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateFinalDocArgs"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateFinalDocArgs"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateFinalDocArgs"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/StringResponseBase"}}, "application/json": {"schema": {"$ref": "#/components/schemas/StringResponseBase"}}, "text/json": {"schema": {"$ref": "#/components/schemas/StringResponseBase"}}}}}}}, "/api/FinalDocument/GetListSpecialChangeAvailable": {"post": {"tags": ["FinalDocument"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SpecialChangeAvailableArgs"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SpecialChangeAvailableArgs"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/SpecialChangeAvailableArgs"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/SpecialChangeAvailableResListResponseBase"}}, "application/json": {"schema": {"$ref": "#/components/schemas/SpecialChangeAvailableResListResponseBase"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SpecialChangeAvailableResListResponseBase"}}}}}}}, "/api/FinalDocument/GetListFinalDocByHouse": {"post": {"tags": ["FinalDocument"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetFinalDocListByHouse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/GetFinalDocListByHouse"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/GetFinalDocListByHouse"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/TblFinalDocumentListResponseBase"}}, "application/json": {"schema": {"$ref": "#/components/schemas/TblFinalDocumentListResponseBase"}}, "text/json": {"schema": {"$ref": "#/components/schemas/TblFinalDocumentListResponseBase"}}}}}}}, "/api/FinalDocument/UploadFinalDoc": {"post": {"tags": ["FinalDocument"], "requestBody": {"content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"CHouseID": {"type": "integer", "format": "int32"}, "CBuildCaseID": {"type": "integer", "format": "int32"}, "CDocumentName": {"type": "string"}, "CNote": {"type": "string"}, "CApproveRemark": {"type": "string"}, "CFile": {"type": "string", "format": "binary"}}}, "encoding": {"CHouseID": {"style": "form"}, "CBuildCaseID": {"style": "form"}, "CDocumentName": {"style": "form"}, "CNote": {"style": "form"}, "CApproveRemark": {"style": "form"}, "CFile": {"style": "form"}}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/StringResponseBase"}}, "application/json": {"schema": {"$ref": "#/components/schemas/StringResponseBase"}}, "text/json": {"schema": {"$ref": "#/components/schemas/StringResponseBase"}}}}}}}, "/api/FormItem/GetListFormItem": {"post": {"tags": ["FormItem"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetListFormItemReq"}}, "text/json": {"schema": {"$ref": "#/components/schemas/GetListFormItemReq"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/GetListFormItemReq"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/GetListFormItemResResponseBase"}}, "application/json": {"schema": {"$ref": "#/components/schemas/GetListFormItemResResponseBase"}}, "text/json": {"schema": {"$ref": "#/components/schemas/GetListFormItemResResponseBase"}}}}}}}, "/api/FormItem/SaveListFormItem": {"post": {"tags": ["FormItem"], "requestBody": {"content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/SaveListFormItemReq"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/SaveListFormItemReq"}}}, "application/*+json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/SaveListFormItemReq"}}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/StringResponseBase"}}, "application/json": {"schema": {"$ref": "#/components/schemas/StringResponseBase"}}, "text/json": {"schema": {"$ref": "#/components/schemas/StringResponseBase"}}}}}}}, "/api/FormItem/LockFormItem": {"post": {"tags": ["FormItem"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/LockFormItemReq"}}, "text/json": {"schema": {"$ref": "#/components/schemas/LockFormItemReq"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/LockFormItemReq"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/StringResponseBase"}}, "application/json": {"schema": {"$ref": "#/components/schemas/StringResponseBase"}}, "text/json": {"schema": {"$ref": "#/components/schemas/StringResponseBase"}}}}}}}, "/api/FormItem/CreateListFormItem": {"post": {"tags": ["FormItem"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateListFormItem"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateListFormItem"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateListFormItem"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/StringResponseBase"}}, "application/json": {"schema": {"$ref": "#/components/schemas/StringResponseBase"}}, "text/json": {"schema": {"$ref": "#/components/schemas/StringResponseBase"}}}}}}}, "/api/FormItem/UnlockFormItem": {"post": {"tags": ["FormItem"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UnlockFormItem"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UnlockFormItem"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UnlockFormItem"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/StringResponseBase"}}, "application/json": {"schema": {"$ref": "#/components/schemas/StringResponseBase"}}, "text/json": {"schema": {"$ref": "#/components/schemas/StringResponseBase"}}}}}}}, "/api/House/Login": {"post": {"tags": ["House"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/HouseLoginRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/HouseLoginRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/HouseLoginRequest"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/HouseLoginResponseResponseBase"}}, "application/json": {"schema": {"$ref": "#/components/schemas/HouseLoginResponseResponseBase"}}, "text/json": {"schema": {"$ref": "#/components/schemas/HouseLoginResponseResponseBase"}}}}}}}, "/api/House/GetChangeDate": {"post": {"tags": ["House"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/HouseGetChangeDateArgs"}}, "text/json": {"schema": {"$ref": "#/components/schemas/HouseGetChangeDateArgs"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/HouseGetChangeDateArgs"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/HouseGetChangeDateResponeResponseBase"}}, "application/json": {"schema": {"$ref": "#/components/schemas/HouseGetChangeDateResponeResponseBase"}}, "text/json": {"schema": {"$ref": "#/components/schemas/HouseGetChangeDateResponeResponseBase"}}}}}}}, "/api/House/ChangePreOrder": {"post": {"tags": ["House"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/HouseChangePreOrderArgs"}}, "text/json": {"schema": {"$ref": "#/components/schemas/HouseChangePreOrderArgs"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/HouseChangePreOrderArgs"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/StringResponseBase"}}, "application/json": {"schema": {"$ref": "#/components/schemas/StringResponseBase"}}, "text/json": {"schema": {"$ref": "#/components/schemas/StringResponseBase"}}}}}}}, "/api/House/GetHourList": {"post": {"tags": ["House"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/HouseGetHourListArgs"}}, "text/json": {"schema": {"$ref": "#/components/schemas/HouseGetHourListArgs"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/HouseGetHourListArgs"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/GetHourListResponeListResponseBase"}}, "application/json": {"schema": {"$ref": "#/components/schemas/GetHourListResponeListResponseBase"}}, "text/json": {"schema": {"$ref": "#/components/schemas/GetHourListResponeListResponseBase"}}}}}}}, "/api/House/CancelChangePreOrder": {"post": {"tags": ["House"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CancelChangePreOrder"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CancelChangePreOrder"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CancelChangePreOrder"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/StringResponseBase"}}, "application/json": {"schema": {"$ref": "#/components/schemas/StringResponseBase"}}, "text/json": {"schema": {"$ref": "#/components/schemas/StringResponseBase"}}}}}}}, "/api/House/GetHouseRegularPicture": {"post": {"tags": ["House"], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/HouseRegularPicResponseBase"}}, "application/json": {"schema": {"$ref": "#/components/schemas/HouseRegularPicResponseBase"}}, "text/json": {"schema": {"$ref": "#/components/schemas/HouseRegularPicResponseBase"}}}}}}}, "/api/House/HouseLoginStep2": {"post": {"tags": ["House"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/HouseLoginStep2Request"}}, "text/json": {"schema": {"$ref": "#/components/schemas/HouseLoginStep2Request"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/HouseLoginStep2Request"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/StringResponseBase"}}, "application/json": {"schema": {"$ref": "#/components/schemas/StringResponseBase"}}, "text/json": {"schema": {"$ref": "#/components/schemas/StringResponseBase"}}}}}}}, "/api/House/AgreeDisclaimer": {"post": {"tags": ["House"], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/StringResponseBase"}}, "application/json": {"schema": {"$ref": "#/components/schemas/StringResponseBase"}}, "text/json": {"schema": {"$ref": "#/components/schemas/StringResponseBase"}}}}}}}, "/api/House/GetRegularNoticeFile": {"post": {"tags": ["House"], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/TblRegularNoticeFileResponseBase"}}, "application/json": {"schema": {"$ref": "#/components/schemas/TblRegularNoticeFileResponseBase"}}, "text/json": {"schema": {"$ref": "#/components/schemas/TblRegularNoticeFileResponseBase"}}}}}}}, "/api/House/GetSpecialNoticeFile": {"post": {"tags": ["House"], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/TblSpecialNoticeFileResponseBase"}}, "application/json": {"schema": {"$ref": "#/components/schemas/TblSpecialNoticeFileResponseBase"}}, "text/json": {"schema": {"$ref": "#/components/schemas/TblSpecialNoticeFileResponseBase"}}}}}}}, "/api/House/GetChangePreOrder": {"post": {"tags": ["House"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetChangePreOrderArgs"}}, "text/json": {"schema": {"$ref": "#/components/schemas/GetChangePreOrderArgs"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/GetChangePreOrderArgs"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/GetChangePreOrderResponeResponseBase"}}, "application/json": {"schema": {"$ref": "#/components/schemas/GetChangePreOrderResponeResponseBase"}}, "text/json": {"schema": {"$ref": "#/components/schemas/GetChangePreOrderResponeResponseBase"}}}}}}}, "/api/House/GetHouseReview": {"post": {"tags": ["House"], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/GetHouseReviewListResponseBase"}}, "application/json": {"schema": {"$ref": "#/components/schemas/GetHouseReviewListResponseBase"}}, "text/json": {"schema": {"$ref": "#/components/schemas/GetHouseReviewListResponseBase"}}}}}}}, "/api/House/UpdateHouseReview": {"post": {"tags": ["House"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateHouseReviewArgs"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateHouseReviewArgs"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateHouseReviewArgs"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/StringResponseBase"}}, "application/json": {"schema": {"$ref": "#/components/schemas/StringResponseBase"}}, "text/json": {"schema": {"$ref": "#/components/schemas/StringResponseBase"}}}}}}}, "/api/House/GetHouseRequirement": {"post": {"tags": ["House"], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/HouseRequirementResListResponseBase"}}, "application/json": {"schema": {"$ref": "#/components/schemas/HouseRequirementResListResponseBase"}}, "text/json": {"schema": {"$ref": "#/components/schemas/HouseRequirementResListResponseBase"}}}}}}}, "/api/House/UpdateHouseRequirement": {"post": {"tags": ["House"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateHouseRequirementArgs"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateHouseRequirementArgs"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateHouseRequirementArgs"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/StringResponseBase"}}, "application/json": {"schema": {"$ref": "#/components/schemas/StringResponseBase"}}, "text/json": {"schema": {"$ref": "#/components/schemas/StringResponseBase"}}}}}}}, "/api/House/CheckIsReview": {"post": {"tags": ["House"], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/BooleanResponseBase"}}, "application/json": {"schema": {"$ref": "#/components/schemas/BooleanResponseBase"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BooleanResponseBase"}}}}}}}, "/api/House/GetHouseInfo": {"post": {"tags": ["House"], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/TblHouseResponseBase"}}, "application/json": {"schema": {"$ref": "#/components/schemas/TblHouseResponseBase"}}, "text/json": {"schema": {"$ref": "#/components/schemas/TblHouseResponseBase"}}}}}}}, "/api/House/EditHouseInfo": {"post": {"tags": ["House"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/EditHouseInfo"}}, "text/json": {"schema": {"$ref": "#/components/schemas/EditHouseInfo"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/EditHouseInfo"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/StringResponseBase"}}, "application/json": {"schema": {"$ref": "#/components/schemas/StringResponseBase"}}, "text/json": {"schema": {"$ref": "#/components/schemas/StringResponseBase"}}}}}}}, "/api/House/GetMilestone": {"post": {"tags": ["House"], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/GetMilestoneResResponseBase"}}, "application/json": {"schema": {"$ref": "#/components/schemas/GetMilestoneResResponseBase"}}, "text/json": {"schema": {"$ref": "#/components/schemas/GetMilestoneResResponseBase"}}}}}}}, "/api/House/SaveMilestone": {"post": {"tags": ["House"], "requestBody": {"content": {"application/json": {"schema": {"type": "integer", "format": "int32"}}, "text/json": {"schema": {"type": "integer", "format": "int32"}}, "application/*+json": {"schema": {"type": "integer", "format": "int32"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/StringResponseBase"}}, "application/json": {"schema": {"$ref": "#/components/schemas/StringResponseBase"}}, "text/json": {"schema": {"$ref": "#/components/schemas/StringResponseBase"}}}}}}}, "/api/House/GetHouseProgress": {"post": {"tags": ["House"], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/GetHouseProgressResponseBase"}}, "application/json": {"schema": {"$ref": "#/components/schemas/GetHouseProgressResponseBase"}}, "text/json": {"schema": {"$ref": "#/components/schemas/GetHouseProgressResponseBase"}}}}}}}, "/api/House/UpdateHouseProgress": {"post": {"tags": ["House"], "requestBody": {"content": {"application/json": {"schema": {"type": "integer", "format": "int32"}}, "text/json": {"schema": {"type": "integer", "format": "int32"}}, "application/*+json": {"schema": {"type": "integer", "format": "int32"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/StringResponseBase"}}, "application/json": {"schema": {"$ref": "#/components/schemas/StringResponseBase"}}, "text/json": {"schema": {"$ref": "#/components/schemas/StringResponseBase"}}}}}}}, "/api/House/GetPayStatus": {"post": {"tags": ["House"], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/GetPayStatusResponseBase"}}, "application/json": {"schema": {"$ref": "#/components/schemas/GetPayStatusResponseBase"}}, "text/json": {"schema": {"$ref": "#/components/schemas/GetPayStatusResponseBase"}}}}}}}, "/api/House/SendOTPChangePassword": {"post": {"tags": ["House"], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/StringResponseBase"}}, "application/json": {"schema": {"$ref": "#/components/schemas/StringResponseBase"}}, "text/json": {"schema": {"$ref": "#/components/schemas/StringResponseBase"}}}}}}}, "/api/House/CheckOTPChangePassword": {"post": {"tags": ["House"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CheckOTPRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CheckOTPRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CheckOTPRequest"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/BooleanResponseBase"}}, "application/json": {"schema": {"$ref": "#/components/schemas/BooleanResponseBase"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BooleanResponseBase"}}}}}}}, "/api/House/ChangePassword": {"post": {"tags": ["House"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ChangePasswordRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ChangePasswordRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ChangePasswordRequest"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/StringResponseBase"}}, "application/json": {"schema": {"$ref": "#/components/schemas/StringResponseBase"}}, "text/json": {"schema": {"$ref": "#/components/schemas/StringResponseBase"}}}}}}}, "/api/House/GetListBuilding": {"post": {"tags": ["House"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetListBuildingArgs"}}, "text/json": {"schema": {"$ref": "#/components/schemas/GetListBuildingArgs"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/GetListBuildingArgs"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/StringListResponseBase"}}, "application/json": {"schema": {"$ref": "#/components/schemas/StringListResponseBase"}}, "text/json": {"schema": {"$ref": "#/components/schemas/StringListResponseBase"}}}}}}}, "/api/House/GetListHouseHold": {"post": {"tags": ["House"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetListHouseHoldArgs"}}, "text/json": {"schema": {"$ref": "#/components/schemas/GetListHouseHoldArgs"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/GetListHouseHoldArgs"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/StringListResponseBase"}}, "application/json": {"schema": {"$ref": "#/components/schemas/StringListResponseBase"}}, "text/json": {"schema": {"$ref": "#/components/schemas/StringListResponseBase"}}}}}}}, "/api/House/GetHouseList": {"post": {"tags": ["House"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetHouseListArgs"}}, "text/json": {"schema": {"$ref": "#/components/schemas/GetHouseListArgs"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/GetHouseListArgs"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/GetHouseListResListResponseBase"}}, "application/json": {"schema": {"$ref": "#/components/schemas/GetHouseListResListResponseBase"}}, "text/json": {"schema": {"$ref": "#/components/schemas/GetHouseListResListResponseBase"}}}}}}}, "/api/House/GetHouseByID": {"post": {"tags": ["House"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetHouseByIDArgs"}}, "text/json": {"schema": {"$ref": "#/components/schemas/GetHouseByIDArgs"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/GetHouseByIDArgs"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/TblHouseResponseBase"}}, "application/json": {"schema": {"$ref": "#/components/schemas/TblHouseResponseBase"}}, "text/json": {"schema": {"$ref": "#/components/schemas/TblHouseResponseBase"}}}}}}}, "/api/House/EditHouse": {"post": {"tags": ["House"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/EditHouseArgs"}}, "text/json": {"schema": {"$ref": "#/components/schemas/EditHouseArgs"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/EditHouseArgs"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/StringResponseBase"}}, "application/json": {"schema": {"$ref": "#/components/schemas/StringResponseBase"}}, "text/json": {"schema": {"$ref": "#/components/schemas/StringResponseBase"}}}}}}}, "/api/House/EditListHouse": {"post": {"tags": ["House"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/EditListHouseArgs"}}, "text/json": {"schema": {"$ref": "#/components/schemas/EditListHouseArgs"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/EditListHouseArgs"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/StringResponseBase"}}, "application/json": {"schema": {"$ref": "#/components/schemas/StringResponseBase"}}, "text/json": {"schema": {"$ref": "#/components/schemas/StringResponseBase"}}}}}}}, "/api/House/GetListHouseRegularPic": {"post": {"tags": ["House"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetListHouseRegularPicArgs"}}, "text/json": {"schema": {"$ref": "#/components/schemas/GetListHouseRegularPicArgs"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/GetListHouseRegularPicArgs"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/GetListHouseRegularPicResListResponseBase"}}, "application/json": {"schema": {"$ref": "#/components/schemas/GetListHouseRegularPicResListResponseBase"}}, "text/json": {"schema": {"$ref": "#/components/schemas/GetListHouseRegularPicResListResponseBase"}}}}}}}, "/api/House/EditHouseRegularPic": {"post": {"tags": ["House"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/EditHouseRegularPictureArgs"}}, "text/json": {"schema": {"$ref": "#/components/schemas/EditHouseRegularPictureArgs"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/EditHouseRegularPictureArgs"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/StringResponseBase"}}, "application/json": {"schema": {"$ref": "#/components/schemas/StringResponseBase"}}, "text/json": {"schema": {"$ref": "#/components/schemas/StringResponseBase"}}}}}}}, "/api/House/DeleteRegularPicture": {"post": {"tags": ["House"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DeleteRegularPic"}}, "text/json": {"schema": {"$ref": "#/components/schemas/DeleteRegularPic"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/DeleteRegularPic"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/StringResponseBase"}}, "application/json": {"schema": {"$ref": "#/components/schemas/StringResponseBase"}}, "text/json": {"schema": {"$ref": "#/components/schemas/StringResponseBase"}}}}}}}, "/api/House/UploadRegularPic": {"post": {"tags": ["House"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UploadRegularPic"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UploadRegularPic"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UploadRegularPic"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/StringResponseBase"}}, "application/json": {"schema": {"$ref": "#/components/schemas/StringResponseBase"}}, "text/json": {"schema": {"$ref": "#/components/schemas/StringResponseBase"}}}}}}}, "/api/House/GetListAppointments": {"post": {"tags": ["House"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetAppoinmentArgs"}}, "text/json": {"schema": {"$ref": "#/components/schemas/GetAppoinmentArgs"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/GetAppoinmentArgs"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/GetAppoinmentResListResponseBase"}}, "application/json": {"schema": {"$ref": "#/components/schemas/GetAppoinmentResListResponseBase"}}, "text/json": {"schema": {"$ref": "#/components/schemas/GetAppoinmentResListResponseBase"}}}}}}}, "/api/House/GetAppointmentByID": {"post": {"tags": ["House"], "requestBody": {"content": {"application/json": {"schema": {"type": "integer", "format": "int32"}}, "text/json": {"schema": {"type": "integer", "format": "int32"}}, "application/*+json": {"schema": {"type": "integer", "format": "int32"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/GetAppoinmentResResponseBase"}}, "application/json": {"schema": {"$ref": "#/components/schemas/GetAppoinmentResResponseBase"}}, "text/json": {"schema": {"$ref": "#/components/schemas/GetAppoinmentResResponseBase"}}}}}}}, "/api/House/EditAppointment": {"post": {"tags": ["House"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/EditAppointmentArgs"}}, "text/json": {"schema": {"$ref": "#/components/schemas/EditAppointmentArgs"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/EditAppointmentArgs"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/StringResponseBase"}}, "application/json": {"schema": {"$ref": "#/components/schemas/StringResponseBase"}}, "text/json": {"schema": {"$ref": "#/components/schemas/StringResponseBase"}}}}}}}, "/api/House/DeleteAppointment": {"post": {"tags": ["House"], "requestBody": {"content": {"application/json": {"schema": {"type": "integer", "format": "int32"}}, "text/json": {"schema": {"type": "integer", "format": "int32"}}, "application/*+json": {"schema": {"type": "integer", "format": "int32"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/StringResponseBase"}}, "application/json": {"schema": {"$ref": "#/components/schemas/StringResponseBase"}}, "text/json": {"schema": {"$ref": "#/components/schemas/StringResponseBase"}}}}}}}, "/api/House/CreateAppointment": {"post": {"tags": ["House"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateAppointmentArgs"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateAppointmentArgs"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateAppointmentArgs"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/StringResponseBase"}}, "application/json": {"schema": {"$ref": "#/components/schemas/StringResponseBase"}}, "text/json": {"schema": {"$ref": "#/components/schemas/StringResponseBase"}}}}}}}, "/api/House/GetHourListAppointment": {"post": {"tags": ["House"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetHourListAppointmentReq"}}, "text/json": {"schema": {"$ref": "#/components/schemas/GetHourListAppointmentReq"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/GetHourListAppointmentReq"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/GetHourListResponeListResponseBase"}}, "application/json": {"schema": {"$ref": "#/components/schemas/GetHourListResponeListResponseBase"}}, "text/json": {"schema": {"$ref": "#/components/schemas/GetHourListResponeListResponseBase"}}}}}}}, "/api/House/ExportExcelListAppointments": {"post": {"tags": ["House"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetAppoinmentArgs"}}, "text/json": {"schema": {"$ref": "#/components/schemas/GetAppoinmentArgs"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/GetAppoinmentArgs"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ExportExcelResponseResponseBase"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ExportExcelResponseResponseBase"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ExportExcelResponseResponseBase"}}}}}}}, "/api/House/GetHouseChangeDate": {"post": {"tags": ["House"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetHouseChangeDateReq"}}, "text/json": {"schema": {"$ref": "#/components/schemas/GetHouseChangeDateReq"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/GetHouseChangeDateReq"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/GetHouseChangeDateResListResponseBase"}}, "application/json": {"schema": {"$ref": "#/components/schemas/GetHouseChangeDateResListResponseBase"}}, "text/json": {"schema": {"$ref": "#/components/schemas/GetHouseChangeDateResListResponseBase"}}}}}}}, "/api/House/SaveHouseChangeDate": {"post": {"tags": ["House"], "requestBody": {"content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/SaveHouseChangeDateReq"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/SaveHouseChangeDateReq"}}}, "application/*+json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/SaveHouseChangeDateReq"}}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/StringResponseBase"}}, "application/json": {"schema": {"$ref": "#/components/schemas/StringResponseBase"}}, "text/json": {"schema": {"$ref": "#/components/schemas/StringResponseBase"}}}}}}}, "/api/House/ExportHouse": {"post": {"tags": ["House"], "parameters": [{"name": "CBuildCaseID", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ByteArrayResponseBase"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ByteArrayResponseBase"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ByteArrayResponseBase"}}}}}}}, "/api/House/ImportHouse": {"post": {"tags": ["House"], "requestBody": {"content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"CBuildCaseID": {"type": "integer", "format": "int32"}, "CFile": {"type": "string", "format": "binary"}}}, "encoding": {"CBuildCaseID": {"style": "form"}, "CFile": {"style": "form"}}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/StringResponseBase"}}, "application/json": {"schema": {"$ref": "#/components/schemas/StringResponseBase"}}, "text/json": {"schema": {"$ref": "#/components/schemas/StringResponseBase"}}}}}}}, "/api/House/ResetHouseSecureKey": {"post": {"tags": ["House"], "requestBody": {"content": {"application/json": {"schema": {"type": "integer", "format": "int32"}}, "text/json": {"schema": {"type": "integer", "format": "int32"}}, "application/*+json": {"schema": {"type": "integer", "format": "int32"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/BooleanResponseBase"}}, "application/json": {"schema": {"$ref": "#/components/schemas/BooleanResponseBase"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BooleanResponseBase"}}}}}}}, "/api/HouseHoldMain/AddHouseHoldMain": {"post": {"tags": ["HouseHoldMain"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AddHouseHoldMain"}}, "text/json": {"schema": {"$ref": "#/components/schemas/AddHouseHoldMain"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/AddHouseHoldMain"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/StringResponseBase"}}, "application/json": {"schema": {"$ref": "#/components/schemas/StringResponseBase"}}, "text/json": {"schema": {"$ref": "#/components/schemas/StringResponseBase"}}}}}}}, "/api/InfoPicture/GetInfoPicturelList": {"post": {"tags": ["InfoPicture"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetInfoPictureListRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/GetInfoPictureListRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/GetInfoPictureListRequest"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/GetInfoPictureListResponseListResponseBase"}}, "application/json": {"schema": {"$ref": "#/components/schemas/GetInfoPictureListResponseListResponseBase"}}, "text/json": {"schema": {"$ref": "#/components/schemas/GetInfoPictureListResponseListResponseBase"}}}}}}}, "/api/InfoPicture/UploadListInfoPicture": {"post": {"tags": ["InfoPicture"], "requestBody": {"content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"CBuildCaseId": {"type": "integer", "format": "int32"}, "CPath": {"type": "string"}, "CFile": {"type": "array", "items": {"type": "string", "format": "binary"}}}}, "encoding": {"CBuildCaseId": {"style": "form"}, "CPath": {"style": "form"}, "CFile": {"style": "form"}}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/UploadFileResponseResponseBase"}}, "application/json": {"schema": {"$ref": "#/components/schemas/UploadFileResponseResponseBase"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UploadFileResponseResponseBase"}}}}}}}, "/api/InfoPicture/UpdateInfoPicture": {"post": {"tags": ["InfoPicture"], "requestBody": {"content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"CFile": {"type": "string", "format": "binary"}, "CInfoPictureID": {"type": "integer", "format": "int32"}, "CBuildCaseId": {"type": "integer", "format": "int32"}}}, "encoding": {"CFile": {"style": "form"}, "CInfoPictureID": {"style": "form"}, "CBuildCaseId": {"style": "form"}}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/UploadFileResponseResponseBase"}}, "application/json": {"schema": {"$ref": "#/components/schemas/UploadFileResponseResponseBase"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UploadFileResponseResponseBase"}}}}}}}, "/api/Material/GetMaterialList": {"post": {"tags": ["Material"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetMaterialListRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/GetMaterialListRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/GetMaterialListRequest"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/GetMaterialListResponseListResponseBase"}}, "application/json": {"schema": {"$ref": "#/components/schemas/GetMaterialListResponseListResponseBase"}}, "text/json": {"schema": {"$ref": "#/components/schemas/GetMaterialListResponseListResponseBase"}}}}}}}, "/api/Material/SaveMaterialAdmin": {"post": {"tags": ["Material"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SaveMaterialArgs"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SaveMaterialArgs"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/SaveMaterialArgs"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/StringResponseBase"}}, "application/json": {"schema": {"$ref": "#/components/schemas/StringResponseBase"}}, "text/json": {"schema": {"$ref": "#/components/schemas/StringResponseBase"}}}}}}}, "/api/Material/ExportExcelMaterialTemplate": {"post": {"tags": ["Material"], "requestBody": {"content": {"application/json": {"schema": {"type": "integer", "format": "int32"}}, "text/json": {"schema": {"type": "integer", "format": "int32"}}, "application/*+json": {"schema": {"type": "integer", "format": "int32"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ExportExcelMaterialsResponseBase"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ExportExcelMaterialsResponseBase"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ExportExcelMaterialsResponseBase"}}}}}}}, "/api/Material/ExportExcelMaterialList": {"post": {"tags": ["Material"], "requestBody": {"content": {"application/json": {"schema": {"type": "integer", "format": "int32"}}, "text/json": {"schema": {"type": "integer", "format": "int32"}}, "application/*+json": {"schema": {"type": "integer", "format": "int32"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ExportExcelMaterialsResponseBase"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ExportExcelMaterialsResponseBase"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ExportExcelMaterialsResponseBase"}}}}}}}, "/api/Material/ImportExcelMaterialList": {"post": {"tags": ["Material"], "requestBody": {"content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"CBuildCaseId": {"type": "integer", "format": "int32"}, "CFile": {"type": "string", "format": "binary"}}}, "encoding": {"CBuildCaseId": {"style": "form"}, "CFile": {"style": "form"}}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/StringResponseBase"}}, "application/json": {"schema": {"$ref": "#/components/schemas/StringResponseBase"}}, "text/json": {"schema": {"$ref": "#/components/schemas/StringResponseBase"}}}}}}}, "/api/Picture/GetPicturelList": {"post": {"tags": ["Picture"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetPictureListRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/GetPictureListRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/GetPictureListRequest"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/GetPictureListResponseListResponseBase"}}, "application/json": {"schema": {"$ref": "#/components/schemas/GetPictureListResponseListResponseBase"}}, "text/json": {"schema": {"$ref": "#/components/schemas/GetPictureListResponseListResponseBase"}}}}}}}, "/api/Picture/UploadListPicture": {"post": {"tags": ["Picture"], "requestBody": {"content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"CBuildCaseId": {"type": "integer", "format": "int32"}, "CPath": {"type": "string"}, "CFile": {"type": "array", "items": {"type": "string", "format": "binary"}}}}, "encoding": {"CBuildCaseId": {"style": "form"}, "CPath": {"style": "form"}, "CFile": {"style": "form"}}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/UploadFileResponseResponseBase"}}, "application/json": {"schema": {"$ref": "#/components/schemas/UploadFileResponseResponseBase"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UploadFileResponseResponseBase"}}}}}}}, "/api/Picture/UpdatePicture": {"post": {"tags": ["Picture"], "requestBody": {"content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"CBuildCaseID": {"type": "integer", "format": "int32"}, "CPictureID": {"type": "integer", "format": "int32"}, "CFile": {"type": "string", "format": "binary"}}}, "encoding": {"CBuildCaseID": {"style": "form"}, "CPictureID": {"style": "form"}, "CFile": {"style": "form"}}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/UploadFileResponseResponseBase"}}, "application/json": {"schema": {"$ref": "#/components/schemas/UploadFileResponseResponseBase"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UploadFileResponseResponseBase"}}}}}}}, "/api/PreOrderSetting/GetPreOrderSetting": {"post": {"tags": ["PreOrderSetting"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetPreOrderSettingArgs"}}, "text/json": {"schema": {"$ref": "#/components/schemas/GetPreOrderSettingArgs"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/GetPreOrderSettingArgs"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/GetPreOrderSettingResponseListResponseBase"}}, "application/json": {"schema": {"$ref": "#/components/schemas/GetPreOrderSettingResponseListResponseBase"}}, "text/json": {"schema": {"$ref": "#/components/schemas/GetPreOrderSettingResponseListResponseBase"}}}}}}}, "/api/PreOrderSetting/SavePreOrderSetting": {"post": {"tags": ["PreOrderSetting"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SavePreOrderSetting"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SavePreOrderSetting"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/SavePreOrderSetting"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/StringResponseBase"}}, "application/json": {"schema": {"$ref": "#/components/schemas/StringResponseBase"}}, "text/json": {"schema": {"$ref": "#/components/schemas/StringResponseBase"}}}}}}}, "/api/Quotation/GetList": {"post": {"tags": ["Quotation"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetListQuotationRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/GetListQuotationRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/GetListQuotationRequest"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/GetQuotationListResponseBase"}}, "application/json": {"schema": {"$ref": "#/components/schemas/GetQuotationListResponseBase"}}, "text/json": {"schema": {"$ref": "#/components/schemas/GetQuotationListResponseBase"}}}}}}}, "/api/Quotation/GetData": {"post": {"tags": ["Quotation"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetQuotationByIDRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/GetQuotationByIDRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/GetQuotationByIDRequest"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/GetQuotationResponseBase"}}, "application/json": {"schema": {"$ref": "#/components/schemas/GetQuotationResponseBase"}}, "text/json": {"schema": {"$ref": "#/components/schemas/GetQuotationResponseBase"}}}}}}}, "/api/Quotation/SaveData": {"post": {"tags": ["Quotation"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SaveDataQuotation"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SaveDataQuotation"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/SaveDataQuotation"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/StringResponseBase"}}, "application/json": {"schema": {"$ref": "#/components/schemas/StringResponseBase"}}, "text/json": {"schema": {"$ref": "#/components/schemas/StringResponseBase"}}}}}}}, "/api/Quotation/DeleteData": {"post": {"tags": ["Quotation"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DeleteQuotationRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/DeleteQuotationRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/DeleteQuotationRequest"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/StringResponseBase"}}, "application/json": {"schema": {"$ref": "#/components/schemas/StringResponseBase"}}, "text/json": {"schema": {"$ref": "#/components/schemas/StringResponseBase"}}}}}}}, "/api/Quotation/GetListByHouseID": {"post": {"tags": ["Quotation"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetListByHouseIDRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/GetListByHouseIDRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/GetListByHouseIDRequest"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/GetQuotationListResponseBase"}}, "application/json": {"schema": {"$ref": "#/components/schemas/GetQuotationListResponseBase"}}, "text/json": {"schema": {"$ref": "#/components/schemas/GetQuotationListResponseBase"}}}}}}}, "/api/RegularChangeItem/GetListRegularChangeItem": {"post": {"tags": ["RegularChangeItem"], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/GetListRegularChangeItemResListResponseBase"}}, "application/json": {"schema": {"$ref": "#/components/schemas/GetListRegularChangeItemResListResponseBase"}}, "text/json": {"schema": {"$ref": "#/components/schemas/GetListRegularChangeItemResListResponseBase"}}}}}}}, "/api/RegularChangeItem/GetListRegularChangeDetailByItemId": {"post": {"tags": ["RegularChangeItem"], "requestBody": {"content": {"application/json": {"schema": {"type": "integer", "format": "int32"}}, "text/json": {"schema": {"type": "integer", "format": "int32"}}, "application/*+json": {"schema": {"type": "integer", "format": "int32"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/GetRegularChangeDetailByItemIdResResponseBase"}}, "application/json": {"schema": {"$ref": "#/components/schemas/GetRegularChangeDetailByItemIdResResponseBase"}}, "text/json": {"schema": {"$ref": "#/components/schemas/GetRegularChangeDetailByItemIdResResponseBase"}}}}}}}, "/api/RegularChangeItem/SaveRegularChangeDetail": {"post": {"tags": ["RegularChangeItem"], "requestBody": {"content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/SaveRegularChangeDetailRequest"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/SaveRegularChangeDetailRequest"}}}, "application/*+json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/SaveRegularChangeDetailRequest"}}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/StringResponseBase"}}, "application/json": {"schema": {"$ref": "#/components/schemas/StringResponseBase"}}, "text/json": {"schema": {"$ref": "#/components/schemas/StringResponseBase"}}}}}}}, "/api/RegularChangeItem/GetBuildingSampleSelection": {"post": {"tags": ["RegularChangeItem"], "requestBody": {"content": {"application/json": {"schema": {"type": "integer", "format": "int32"}}, "text/json": {"schema": {"type": "integer", "format": "int32"}}, "application/*+json": {"schema": {"type": "integer", "format": "int32"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/GetBuildingSampleSelectionResResponseBase"}}, "application/json": {"schema": {"$ref": "#/components/schemas/GetBuildingSampleSelectionResResponseBase"}}, "text/json": {"schema": {"$ref": "#/components/schemas/GetBuildingSampleSelectionResResponseBase"}}}}}}}, "/api/RegularChangeItem/CheckRegularChange": {"post": {"tags": ["RegularChangeItem"], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/BooleanResponseBase"}}, "application/json": {"schema": {"$ref": "#/components/schemas/BooleanResponseBase"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BooleanResponseBase"}}}}}}}, "/api/RegularChangeItem/GetSumaryRegularChangeItem": {"post": {"tags": ["RegularChangeItem"], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/GetSumaryRegularChangeItemResListResponseBase"}}, "application/json": {"schema": {"$ref": "#/components/schemas/GetSumaryRegularChangeItemResListResponseBase"}}, "text/json": {"schema": {"$ref": "#/components/schemas/GetSumaryRegularChangeItemResListResponseBase"}}}}}}}, "/api/RegularNoticeFile/DeleteRegularNoticeFile": {"post": {"tags": ["RegularNoticeFile"], "requestBody": {"content": {"application/json": {"schema": {"type": "integer", "format": "int32"}}, "text/json": {"schema": {"type": "integer", "format": "int32"}}, "application/*+json": {"schema": {"type": "integer", "format": "int32"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/StringResponseBase"}}, "application/json": {"schema": {"$ref": "#/components/schemas/StringResponseBase"}}, "text/json": {"schema": {"$ref": "#/components/schemas/StringResponseBase"}}}}}}}, "/api/RegularNoticeFile/GetRegularNoticeFileList": {"post": {"tags": ["RegularNoticeFile"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetRegularNoticeFileListReq"}}, "text/json": {"schema": {"$ref": "#/components/schemas/GetRegularNoticeFileListReq"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/GetRegularNoticeFileListReq"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/GetRegularNoticeFileListResResponseBase"}}, "application/json": {"schema": {"$ref": "#/components/schemas/GetRegularNoticeFileListResResponseBase"}}, "text/json": {"schema": {"$ref": "#/components/schemas/GetRegularNoticeFileListResResponseBase"}}}}}}}, "/api/RegularNoticeFile/GetRegularNoticeFileById": {"post": {"tags": ["RegularNoticeFile"], "requestBody": {"content": {"application/json": {"schema": {"type": "integer", "format": "int32"}}, "text/json": {"schema": {"type": "integer", "format": "int32"}}, "application/*+json": {"schema": {"type": "integer", "format": "int32"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/GetRegularNoticeFileByIdResResponseBase"}}, "application/json": {"schema": {"$ref": "#/components/schemas/GetRegularNoticeFileByIdResResponseBase"}}, "text/json": {"schema": {"$ref": "#/components/schemas/GetRegularNoticeFileByIdResResponseBase"}}}}}}}, "/api/RegularNoticeFile/SaveRegularNoticeFile": {"post": {"tags": ["RegularNoticeFile"], "parameters": [{"name": "BuId", "in": "query", "schema": {"type": "string"}}, {"name": "UserId", "in": "query", "schema": {"type": "string"}}, {"name": "UserCode", "in": "query", "schema": {"type": "string"}}, {"name": "UserName", "in": "query", "schema": {"type": "string"}}, {"name": "CUserType", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "CreateTime", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "ExpTime", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "LoginId", "in": "query", "schema": {"type": "string"}}, {"name": "IsLimit", "in": "query", "schema": {"type": "boolean"}}], "requestBody": {"content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"CNoticeType": {"type": "integer", "format": "int32"}, "CBuildCaseId": {"type": "integer", "format": "int32"}, "CFile": {"type": "string", "format": "binary"}, "CHouseHold": {"type": "array", "items": {"type": "string"}}, "CRegularNoticeFileId": {"type": "integer", "format": "int32"}, "CIsSelectAll": {"type": "boolean"}}}, "encoding": {"CNoticeType": {"style": "form"}, "CBuildCaseId": {"style": "form"}, "CFile": {"style": "form"}, "CHouseHold": {"style": "form"}, "CRegularNoticeFileId": {"style": "form"}, "CIsSelectAll": {"style": "form"}}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/StringResponseBase"}}, "application/json": {"schema": {"$ref": "#/components/schemas/StringResponseBase"}}, "text/json": {"schema": {"$ref": "#/components/schemas/StringResponseBase"}}}}}}}, "/api/RegularNoticeFile/GetListRegularNoticeFileHouseHold": {"post": {"tags": ["RegularNoticeFile"], "requestBody": {"content": {"application/json": {"schema": {"type": "integer", "format": "int32"}}, "text/json": {"schema": {"type": "integer", "format": "int32"}}, "application/*+json": {"schema": {"type": "integer", "format": "int32"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/GetListHouseHoldResListResponseBase"}}, "application/json": {"schema": {"$ref": "#/components/schemas/GetListHouseHoldResListResponseBase"}}, "text/json": {"schema": {"$ref": "#/components/schemas/GetListHouseHoldResListResponseBase"}}}}}}}, "/api/Requirement/GetList": {"post": {"tags": ["Requirement"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetListRequirementRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/GetListRequirementRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/GetListRequirementRequest"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/GetRequirementListResponseBase"}}, "application/json": {"schema": {"$ref": "#/components/schemas/GetRequirementListResponseBase"}}, "text/json": {"schema": {"$ref": "#/components/schemas/GetRequirementListResponseBase"}}}}}}}, "/api/Requirement/GetData": {"post": {"tags": ["Requirement"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetRequirementByIDRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/GetRequirementByIDRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/GetRequirementByIDRequest"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/GetRequirementResponseBase"}}, "application/json": {"schema": {"$ref": "#/components/schemas/GetRequirementResponseBase"}}, "text/json": {"schema": {"$ref": "#/components/schemas/GetRequirementResponseBase"}}}}}}}, "/api/Requirement/SaveData": {"post": {"tags": ["Requirement"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SaveDataRequirement"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SaveDataRequirement"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/SaveDataRequirement"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/StringResponseBase"}}, "application/json": {"schema": {"$ref": "#/components/schemas/StringResponseBase"}}, "text/json": {"schema": {"$ref": "#/components/schemas/StringResponseBase"}}}}}}}, "/api/Requirement/DeleteData": {"post": {"tags": ["Requirement"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DeleteRequirementRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/DeleteRequirementRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/DeleteRequirementRequest"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/StringResponseBase"}}, "application/json": {"schema": {"$ref": "#/components/schemas/StringResponseBase"}}, "text/json": {"schema": {"$ref": "#/components/schemas/StringResponseBase"}}}}}}}, "/api/Review/GetReviewList": {"post": {"tags": ["Review"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetReviewListReq"}}, "text/json": {"schema": {"$ref": "#/components/schemas/GetReviewListReq"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/GetReviewListReq"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/GetReviewListResListResponseBase"}}, "application/json": {"schema": {"$ref": "#/components/schemas/GetReviewListResListResponseBase"}}, "text/json": {"schema": {"$ref": "#/components/schemas/GetReviewListResListResponseBase"}}}}}}}, "/api/Review/DeleteReview": {"post": {"tags": ["Review"], "requestBody": {"content": {"application/json": {"schema": {"type": "integer", "format": "int32"}}, "text/json": {"schema": {"type": "integer", "format": "int32"}}, "application/*+json": {"schema": {"type": "integer", "format": "int32"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/StringResponseBase"}}, "application/json": {"schema": {"$ref": "#/components/schemas/StringResponseBase"}}, "text/json": {"schema": {"$ref": "#/components/schemas/StringResponseBase"}}}}}}}, "/api/Review/SaveReview": {"post": {"tags": ["Review"], "requestBody": {"content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"CBuildCaseId": {"type": "integer", "format": "int32"}, "CReviewId": {"type": "integer", "format": "int32"}, "CReviewType": {"type": "integer", "format": "int32"}, "CReviewName": {"type": "string"}, "CSort": {"type": "integer", "format": "int32"}, "CStatus": {"type": "integer", "format": "int32"}, "CFile": {"type": "string", "format": "binary"}, "CExamineNote": {"type": "string"}, "HouseReviews": {"type": "array", "items": {"$ref": "#/components/schemas/HouseReview"}}}}, "encoding": {"CBuildCaseId": {"style": "form"}, "CReviewId": {"style": "form"}, "CReviewType": {"style": "form"}, "CReviewName": {"style": "form"}, "CSort": {"style": "form"}, "CStatus": {"style": "form"}, "CFile": {"style": "form"}, "CExamineNote": {"style": "form"}, "HouseReviews": {"style": "form"}}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/StringResponseBase"}}, "application/json": {"schema": {"$ref": "#/components/schemas/StringResponseBase"}}, "text/json": {"schema": {"$ref": "#/components/schemas/StringResponseBase"}}}}}}}, "/api/Review/GetReviewById": {"post": {"tags": ["Review"], "requestBody": {"content": {"application/json": {"schema": {"type": "integer", "format": "int32"}}, "text/json": {"schema": {"type": "integer", "format": "int32"}}, "application/*+json": {"schema": {"type": "integer", "format": "int32"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/GetReviewByIdResResponseBase"}}, "application/json": {"schema": {"$ref": "#/components/schemas/GetReviewByIdResResponseBase"}}, "text/json": {"schema": {"$ref": "#/components/schemas/GetReviewByIdResResponseBase"}}}}}}}, "/api/SpecialChange/GetSpecialChangeFile": {"post": {"tags": ["SpecialChange"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetSpecialChangeFileArgs"}}, "text/json": {"schema": {"$ref": "#/components/schemas/GetSpecialChangeFileArgs"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/GetSpecialChangeFileArgs"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/SpecialChangeFileGroupListResponseBase"}}, "application/json": {"schema": {"$ref": "#/components/schemas/SpecialChangeFileGroupListResponseBase"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SpecialChangeFileGroupListResponseBase"}}}}}}}, "/api/SpecialChange/GetListSpecialChange": {"post": {"tags": ["SpecialChange"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetListSpecialChangeRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/GetListSpecialChangeRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/GetListSpecialChangeRequest"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/SpecialChangeResListResponseBase"}}, "application/json": {"schema": {"$ref": "#/components/schemas/SpecialChangeResListResponseBase"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SpecialChangeResListResponseBase"}}}}}}}, "/api/SpecialChange/GetSpecialChangeByID": {"post": {"tags": ["SpecialChange"], "requestBody": {"content": {"application/json": {"schema": {"type": "integer", "format": "int32"}}, "text/json": {"schema": {"type": "integer", "format": "int32"}}, "application/*+json": {"schema": {"type": "integer", "format": "int32"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/SpecialChangeResResponseBase"}}, "application/json": {"schema": {"$ref": "#/components/schemas/SpecialChangeResResponseBase"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SpecialChangeResResponseBase"}}}}}}}, "/api/SpecialChange/SaveSpecialChange": {"post": {"tags": ["SpecialChange"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UploadSpecialChangeFile"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UploadSpecialChangeFile"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UploadSpecialChangeFile"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/StringResponseBase"}}, "application/json": {"schema": {"$ref": "#/components/schemas/StringResponseBase"}}, "text/json": {"schema": {"$ref": "#/components/schemas/StringResponseBase"}}}}}}}, "/api/SpecialChange/GetApproveWaitingList": {"post": {"tags": ["SpecialChange"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApproveWaitingArgs"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ApproveWaitingArgs"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ApproveWaitingArgs"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ApproveWaitingResListResponseBase"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ApproveWaitingResListResponseBase"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ApproveWaitingResListResponseBase"}}}}}}}, "/api/SpecialChange/GetApproveWaitingByID": {"post": {"tags": ["SpecialChange"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApproveWaitingByIDArgs"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ApproveWaitingByIDArgs"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ApproveWaitingByIDArgs"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ApproveWaitingByIDResResponseBase"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ApproveWaitingByIDResResponseBase"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ApproveWaitingByIDResResponseBase"}}}}}}}, "/api/SpecialChange/UpdateApproveWaiting": {"post": {"tags": ["SpecialChange"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateApproveWaiting"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateApproveWaiting"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateApproveWaiting"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/StringResponseBase"}}, "application/json": {"schema": {"$ref": "#/components/schemas/StringResponseBase"}}, "text/json": {"schema": {"$ref": "#/components/schemas/StringResponseBase"}}}}}}}, "/api/SpecialNoticeFile/DeleteSpecialNoticeFile": {"post": {"tags": ["SpecialNoticeFile"], "requestBody": {"content": {"application/json": {"schema": {"type": "integer", "format": "int32"}}, "text/json": {"schema": {"type": "integer", "format": "int32"}}, "application/*+json": {"schema": {"type": "integer", "format": "int32"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/StringResponseBase"}}, "application/json": {"schema": {"$ref": "#/components/schemas/StringResponseBase"}}, "text/json": {"schema": {"$ref": "#/components/schemas/StringResponseBase"}}}}}}}, "/api/SpecialNoticeFile/GetSpecialNoticeFileList": {"post": {"tags": ["SpecialNoticeFile"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetSpecialNoticeFileListReq"}}, "text/json": {"schema": {"$ref": "#/components/schemas/GetSpecialNoticeFileListReq"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/GetSpecialNoticeFileListReq"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/GetSpecialNoticeFileListResResponseBase"}}, "application/json": {"schema": {"$ref": "#/components/schemas/GetSpecialNoticeFileListResResponseBase"}}, "text/json": {"schema": {"$ref": "#/components/schemas/GetSpecialNoticeFileListResResponseBase"}}}}}}}, "/api/SpecialNoticeFile/GetSpecialNoticeFileById": {"post": {"tags": ["SpecialNoticeFile"], "requestBody": {"content": {"application/json": {"schema": {"type": "integer", "format": "int32"}}, "text/json": {"schema": {"type": "integer", "format": "int32"}}, "application/*+json": {"schema": {"type": "integer", "format": "int32"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/GetSpecialNoticeFileByIdResResponseBase"}}, "application/json": {"schema": {"$ref": "#/components/schemas/GetSpecialNoticeFileByIdResResponseBase"}}, "text/json": {"schema": {"$ref": "#/components/schemas/GetSpecialNoticeFileByIdResResponseBase"}}}}}}}, "/api/SpecialNoticeFile/SaveSpecialNoticeFile": {"post": {"tags": ["SpecialNoticeFile"], "parameters": [{"name": "BuId", "in": "query", "schema": {"type": "string"}}, {"name": "UserId", "in": "query", "schema": {"type": "string"}}, {"name": "UserCode", "in": "query", "schema": {"type": "string"}}, {"name": "UserName", "in": "query", "schema": {"type": "string"}}, {"name": "CUserType", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "CreateTime", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "ExpTime", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "LoginId", "in": "query", "schema": {"type": "string"}}, {"name": "IsLimit", "in": "query", "schema": {"type": "boolean"}}], "requestBody": {"content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"CNoticeType": {"type": "integer", "format": "int32"}, "CBuildCaseId": {"type": "integer", "format": "int32"}, "CFile": {"type": "string", "format": "binary"}, "CHouse": {"type": "array", "items": {"$ref": "#/components/schemas/HouseSpecialNoticeFile"}}, "CSpecialNoticeFileId": {"type": "integer", "format": "int32"}, "CIsSelectAll": {"type": "boolean"}, "CExamineNote": {"type": "string"}}}, "encoding": {"CNoticeType": {"style": "form"}, "CBuildCaseId": {"style": "form"}, "CFile": {"style": "form"}, "CHouse": {"style": "form"}, "CSpecialNoticeFileId": {"style": "form"}, "CIsSelectAll": {"style": "form"}, "CExamineNote": {"style": "form"}}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/StringResponseBase"}}, "application/json": {"schema": {"$ref": "#/components/schemas/StringResponseBase"}}, "text/json": {"schema": {"$ref": "#/components/schemas/StringResponseBase"}}}}}}}, "/api/User/UserLogin": {"post": {"tags": ["User"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserLoginRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UserLoginRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UserLoginRequest"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/StringResponseBase"}}, "application/json": {"schema": {"$ref": "#/components/schemas/StringResponseBase"}}, "text/json": {"schema": {"$ref": "#/components/schemas/StringResponseBase"}}}}}}}, "/api/User/GetMenu": {"post": {"tags": ["User"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetMenuArgs"}}, "text/json": {"schema": {"$ref": "#/components/schemas/GetMenuArgs"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/GetMenuArgs"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/GetMenuResponseResponseBase"}}, "application/json": {"schema": {"$ref": "#/components/schemas/GetMenuResponseResponseBase"}}, "text/json": {"schema": {"$ref": "#/components/schemas/GetMenuResponseResponseBase"}}}}}}}, "/api/User/GetList": {"post": {"tags": ["User"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserGetListArgs"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UserGetListArgs"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UserGetListArgs"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/UserGetListResponseListResponseBase"}}, "application/json": {"schema": {"$ref": "#/components/schemas/UserGetListResponseListResponseBase"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UserGetListResponseListResponseBase"}}}}}}}, "/api/User/GetData": {"post": {"tags": ["User"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserGetDataArgs"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UserGetDataArgs"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UserGetDataArgs"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/UserGetDataResponseResponseBase"}}, "application/json": {"schema": {"$ref": "#/components/schemas/UserGetDataResponseResponseBase"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UserGetDataResponseResponseBase"}}}}}}}, "/api/User/AddData": {"post": {"tags": ["User"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserSaveDataArgs"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UserSaveDataArgs"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UserSaveDataArgs"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/UserSaveDataResponseResponseBase"}}, "application/json": {"schema": {"$ref": "#/components/schemas/UserSaveDataResponseResponseBase"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UserSaveDataResponseResponseBase"}}}}}}}, "/api/User/SaveData": {"post": {"tags": ["User"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserSaveDataArgs"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UserSaveDataArgs"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UserSaveDataArgs"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/UserSaveDataResponseResponseBase"}}, "application/json": {"schema": {"$ref": "#/components/schemas/UserSaveDataResponseResponseBase"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UserSaveDataResponseResponseBase"}}}}}}}, "/api/User/RemoveUser": {"post": {"tags": ["User"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserRemoveDataArgs"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UserRemoveDataArgs"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UserRemoveDataArgs"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/UserRemoveDataResponseResponseBase"}}, "application/json": {"schema": {"$ref": "#/components/schemas/UserRemoveDataResponseResponseBase"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UserRemoveDataResponseResponseBase"}}}}}}}, "/api/User/GetUserLog": {"post": {"tags": ["User"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserGetUserLogArgs"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UserGetUserLogArgs"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UserGetUserLogArgs"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/UserGetUserLogResponseListResponseBase"}}, "application/json": {"schema": {"$ref": "#/components/schemas/UserGetUserLogResponseListResponseBase"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UserGetUserLogResponseListResponseBase"}}}}}}}, "/api/UserGroup/GetList": {"post": {"tags": ["UserGroup"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserGroupGetListArgs"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UserGroupGetListArgs"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UserGroupGetListArgs"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/UserGroupGetListResponseListResponseBase"}}, "application/json": {"schema": {"$ref": "#/components/schemas/UserGroupGetListResponseListResponseBase"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UserGroupGetListResponseListResponseBase"}}}}}}}, "/api/UserGroup/GetData": {"post": {"tags": ["UserGroup"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserGroupGetDataArgs"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UserGroupGetDataArgs"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UserGroupGetDataArgs"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/UserGroupGetDataResponseResponseBase"}}, "application/json": {"schema": {"$ref": "#/components/schemas/UserGroupGetDataResponseResponseBase"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UserGroupGetDataResponseResponseBase"}}}}}}}, "/api/UserGroup/AddData": {"post": {"tags": ["UserGroup"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserGroupSaveDataArgs"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UserGroupSaveDataArgs"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UserGroupSaveDataArgs"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/UserGroupSaveDataResponseResponseBase"}}, "application/json": {"schema": {"$ref": "#/components/schemas/UserGroupSaveDataResponseResponseBase"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UserGroupSaveDataResponseResponseBase"}}}}}}}, "/api/UserGroup/SaveData": {"post": {"tags": ["UserGroup"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserGroupSaveDataArgs"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UserGroupSaveDataArgs"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UserGroupSaveDataArgs"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/UserGroupSaveDataResponseResponseBase"}}, "application/json": {"schema": {"$ref": "#/components/schemas/UserGroupSaveDataResponseResponseBase"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UserGroupSaveDataResponseResponseBase"}}}}}}}, "/api/UserGroup/RemoveData": {"post": {"tags": ["UserGroup"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserGroupRemoveDataArgs"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UserGroupRemoveDataArgs"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UserGroupRemoveDataArgs"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/UserGroupRemoveDataResponseResponseBase"}}, "application/json": {"schema": {"$ref": "#/components/schemas/UserGroupRemoveDataResponseResponseBase"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UserGroupRemoveDataResponseResponseBase"}}}}}}}}, "components": {"schemas": {"AddHouseHoldMain": {"type": "object", "properties": {"CBuildCaseID": {"type": "integer", "format": "int32"}, "CBuildingName": {"type": "string", "nullable": true}, "CFloor": {"type": "integer", "format": "int32"}, "CHouseHoldCount": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "ApproveRecord": {"type": "object", "properties": {"CRecordDate": {"type": "string", "format": "date-time", "nullable": true}, "CRemark": {"type": "string", "nullable": true}, "CCreator": {"type": "string", "nullable": true}, "CAction": {"type": "integer", "format": "int32", "nullable": true}, "CExaminNo": {"type": "string", "nullable": true}}, "additionalProperties": false}, "ApproveWaitingArgs": {"type": "object", "properties": {"PageIndex": {"type": "integer", "format": "int32"}, "PageSize": {"type": "integer", "format": "int32"}, "CDateStart": {"type": "string", "format": "date-time", "nullable": true}, "CDateEnd": {"type": "string", "format": "date-time", "nullable": true}, "CType": {"type": "integer", "format": "int32"}, "CBuilCaseID": {"type": "integer", "format": "int32"}, "CName": {"type": "string", "nullable": true}}, "additionalProperties": false}, "ApproveWaitingByIDArgs": {"type": "object", "properties": {"CType": {"type": "string", "nullable": true}, "CID": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "ApproveWaitingByIDRes": {"type": "object", "properties": {"CID": {"type": "integer", "format": "int32"}, "CType": {"type": "integer", "format": "int32"}, "CName": {"type": "string", "nullable": true}, "CApprovalRemark": {"type": "string", "nullable": true}, "CIsApprove": {"type": "boolean", "nullable": true}, "CCreateDT": {"type": "string", "format": "date-time", "nullable": true}, "CFileApproves": {"type": "array", "items": {"$ref": "#/components/schemas/FileApprove"}, "nullable": true}, "CApproveRecord": {"type": "array", "items": {"$ref": "#/components/schemas/ApproveRecord"}, "nullable": true}, "CBuildcaseName": {"type": "string", "nullable": true}}, "additionalProperties": false}, "ApproveWaitingByIDResResponseBase": {"type": "object", "properties": {"TotalItems": {"type": "integer", "format": "int64"}, "Entries": {"$ref": "#/components/schemas/ApproveWaitingByIDRes"}, "Message": {"type": "string", "nullable": true}, "StatusCode": {"$ref": "#/components/schemas/EnumStatusCode"}}, "additionalProperties": false}, "ApproveWaitingRes": {"type": "object", "properties": {"CID": {"type": "integer", "format": "int32"}, "CType": {"type": "integer", "format": "int32"}, "CName": {"type": "string", "nullable": true}, "CCreateDT": {"type": "string", "format": "date-time"}, "CCreator": {"type": "string", "nullable": true}, "CBuildCaseId": {"type": "integer", "format": "int32", "nullable": true}, "CBuildcaseName": {"type": "string", "nullable": true}}, "additionalProperties": false}, "ApproveWaitingResListResponseBase": {"type": "object", "properties": {"TotalItems": {"type": "integer", "format": "int64"}, "Entries": {"type": "array", "items": {"$ref": "#/components/schemas/ApproveWaitingRes"}, "nullable": true}, "Message": {"type": "string", "nullable": true}, "StatusCode": {"$ref": "#/components/schemas/EnumStatusCode"}}, "additionalProperties": false}, "BooleanResponseBase": {"type": "object", "properties": {"TotalItems": {"type": "integer", "format": "int64"}, "Entries": {"type": "boolean"}, "Message": {"type": "string", "nullable": true}, "StatusCode": {"$ref": "#/components/schemas/EnumStatusCode"}}, "additionalProperties": false}, "BuildCaseFileRes": {"type": "object", "properties": {"CID": {"type": "integer", "format": "int32"}, "CCategoryName": {"type": "string", "nullable": true}, "CName": {"type": "string", "nullable": true}, "CFile": {"type": "string", "nullable": true}, "CStatus": {"type": "integer", "format": "int32"}, "CSubmitRemark": {"type": "string", "nullable": true}, "CApproveStatus": {"type": "integer", "format": "int32", "nullable": true}, "CApproveDate": {"type": "string", "format": "date-time", "nullable": true}, "CExamineRejectNote": {"type": "string", "nullable": true}, "CBuildCaseID": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "BuildCaseFileResListResponseBase": {"type": "object", "properties": {"TotalItems": {"type": "integer", "format": "int64"}, "Entries": {"type": "array", "items": {"$ref": "#/components/schemas/BuildCaseFileRes"}, "nullable": true}, "Message": {"type": "string", "nullable": true}, "StatusCode": {"$ref": "#/components/schemas/EnumStatusCode"}}, "additionalProperties": false}, "BuildCaseFileResResponseBase": {"type": "object", "properties": {"TotalItems": {"type": "integer", "format": "int64"}, "Entries": {"$ref": "#/components/schemas/BuildCaseFileRes"}, "Message": {"type": "string", "nullable": true}, "StatusCode": {"$ref": "#/components/schemas/EnumStatusCode"}}, "additionalProperties": false}, "BuildCaseGetFileArgs": {"type": "object", "additionalProperties": false}, "BuildCaseGetFileRespone": {"type": "object", "properties": {"CName": {"type": "string", "nullable": true}, "CFile": {"type": "string", "nullable": true}, "CCategoryName": {"type": "string", "nullable": true}}, "additionalProperties": false}, "BuildCaseGetFileResponeListResponseBase": {"type": "object", "properties": {"TotalItems": {"type": "integer", "format": "int64"}, "Entries": {"type": "array", "items": {"$ref": "#/components/schemas/BuildCaseGetFileRespone"}, "nullable": true}, "Message": {"type": "string", "nullable": true}, "StatusCode": {"$ref": "#/components/schemas/EnumStatusCode"}}, "additionalProperties": false}, "BuildCaseGetListReponse": {"type": "object", "properties": {"cID": {"type": "integer", "format": "int32"}, "CBuildCaseName": {"type": "string", "nullable": true}, "CFrontImage": {"type": "string", "nullable": true}, "ImageList": {"type": "array", "items": {"type": "string"}, "nullable": true}, "CSystemInstruction": {"type": "string", "nullable": true}, "CFinalFileNotice": {"type": "string", "nullable": true}, "CStatus": {"type": "integer", "format": "int32"}, "CShowSignAll": {"type": "boolean", "nullable": true}, "CShowPrice": {"type": "boolean", "nullable": true}, "CCustomerSign": {"type": "boolean", "nullable": true}}, "additionalProperties": false}, "BuildCaseGetListReponseListResponseBase": {"type": "object", "properties": {"TotalItems": {"type": "integer", "format": "int64"}, "Entries": {"type": "array", "items": {"$ref": "#/components/schemas/BuildCaseGetListReponse"}, "nullable": true}, "Message": {"type": "string", "nullable": true}, "StatusCode": {"$ref": "#/components/schemas/EnumStatusCode"}}, "additionalProperties": false}, "BuildCaseGetListReponseResponseBase": {"type": "object", "properties": {"TotalItems": {"type": "integer", "format": "int64"}, "Entries": {"$ref": "#/components/schemas/BuildCaseGetListReponse"}, "Message": {"type": "string", "nullable": true}, "StatusCode": {"$ref": "#/components/schemas/EnumStatusCode"}}, "additionalProperties": false}, "BuildingSample": {"type": "object", "properties": {"CRegularChangeDetailId": {"type": "integer", "format": "int32"}, "CSelectName": {"type": "string", "nullable": true}, "CRemark": {"type": "string", "nullable": true}, "CPictureFile": {"type": "array", "items": {"$ref": "#/components/schemas/PictureInfo"}, "nullable": true}, "CInfoPictureFile": {"type": "array", "items": {"$ref": "#/components/schemas/PictureInfo"}, "nullable": true}, "CDescription": {"type": "string", "nullable": true}, "RegularRemark": {"type": "array", "items": {"$ref": "#/components/schemas/RegularRemark"}, "nullable": true}, "CIsSelect": {"type": "boolean", "nullable": true}}, "additionalProperties": false}, "ByteArrayResponseBase": {"type": "object", "properties": {"TotalItems": {"type": "integer", "format": "int64"}, "Entries": {"type": "string", "format": "byte", "nullable": true}, "Message": {"type": "string", "nullable": true}, "StatusCode": {"$ref": "#/components/schemas/EnumStatusCode"}}, "additionalProperties": false}, "CancelChangePreOrder": {"type": "object", "properties": {"CChangePreOrderID": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "ChangePasswordRequest": {"type": "object", "properties": {"NewSecureKey": {"type": "string", "nullable": true}}, "additionalProperties": false}, "CheckOTPRequest": {"type": "object", "properties": {"OTP": {"type": "string", "nullable": true}}, "additionalProperties": false}, "CHouse": {"type": "object", "properties": {"CHouseId": {"type": "integer", "format": "int32"}, "CFloor": {"type": "integer", "format": "int32", "nullable": true}, "CChangeStartDate": {"type": "string", "format": "date-time", "nullable": true}, "CChangeEndDate": {"type": "string", "format": "date-time", "nullable": true}}, "additionalProperties": false}, "CreateAppointmentArgs": {"type": "object", "properties": {"CPreOrderDate": {"type": "string", "format": "date-time"}, "CPeoples": {"type": "integer", "format": "int32"}, "CHasDesigner": {"type": "boolean"}, "CNeedMail": {"type": "boolean"}, "CRemark": {"type": "string", "nullable": true}, "CHour": {"type": "integer", "format": "int32"}, "CHouseHold": {"type": "string", "nullable": true}, "CFloor": {"type": "integer", "format": "int32"}, "CBuildcaseID": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "CreateFinalDocArgs": {"type": "object", "properties": {"CHouseID": {"type": "integer", "format": "int32"}, "CDocumentName": {"type": "string", "nullable": true}, "CApproveRemark": {"type": "string", "nullable": true}, "CNote": {"type": "string", "nullable": true}, "CSpecialChange": {"type": "array", "items": {"type": "integer", "format": "int32"}, "nullable": true}}, "additionalProperties": false}, "CreateListFormItem": {"type": "object", "properties": {"CBuildCaseId": {"type": "integer", "format": "int32"}, "CFormType": {"type": "integer", "format": "int32"}, "CFormItem": {"type": "array", "items": {"$ref": "#/components/schemas/SaveListFormItemReq"}, "nullable": true}}, "additionalProperties": false}, "DeleteBuildCaseArgs": {"type": "object", "properties": {"CBuildCaseID": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "DeleteQuotationRequest": {"type": "object", "properties": {"CQuotationID": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "DeleteRegularPic": {"type": "object", "properties": {"CRegularPictureID": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "DeleteRequirementRequest": {"type": "object", "properties": {"CRequirementID": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "EditAppointmentArgs": {"type": "object", "properties": {"CID": {"type": "integer", "format": "int32", "nullable": true}, "CPreOrderDate": {"type": "string", "format": "date-time"}, "CPeoples": {"type": "integer", "format": "int32"}, "CHasDesigner": {"type": "boolean", "nullable": true}, "CNeedMail": {"type": "boolean"}, "CStatus": {"type": "integer", "format": "int32"}, "CRemark": {"type": "string", "nullable": true}, "CHour": {"type": "integer", "format": "int32", "nullable": true}}, "additionalProperties": false}, "EditHouseArgs": {"type": "object", "properties": {"CHouseID": {"type": "integer", "format": "int32"}, "CMail": {"type": "string", "nullable": true}, "CIsChange": {"type": "boolean", "nullable": true}, "CPayStatus": {"type": "integer", "format": "int32"}, "CIsEnable": {"type": "boolean"}, "CCustomerName": {"type": "string", "nullable": true}, "CNationalID": {"type": "string", "nullable": true}, "CProgress": {"type": "integer", "format": "int32"}, "CHouseType": {"type": "integer", "format": "int32"}, "CHouseHold": {"type": "string", "nullable": true}, "CPhone": {"type": "string", "nullable": true}, "CChangeStartDate": {"type": "string", "nullable": true}, "CChangeEndDate": {"type": "string", "nullable": true}}, "additionalProperties": false}, "EditHouseInfo": {"type": "object", "properties": {"CPhone": {"type": "string", "nullable": true}, "CMail": {"type": "string", "nullable": true}}, "additionalProperties": false}, "EditHouseRegularPicture": {"type": "object", "properties": {"CHouseID": {"type": "integer", "format": "int32"}, "CRegularPictureID": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "EditHouseRegularPictureArgs": {"type": "object", "properties": {"CHousePic": {"type": "array", "items": {"$ref": "#/components/schemas/EditHouseRegularPicture"}, "nullable": true}}, "additionalProperties": false}, "EditListHouseArgs": {"type": "object", "properties": {"mode": {"type": "integer", "format": "int32"}, "Args": {"type": "array", "items": {"$ref": "#/components/schemas/EditHouseArgs"}, "nullable": true}}, "additionalProperties": false}, "EnumArgs": {"type": "object", "properties": {"TypeID": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "EnumResponse": {"type": "object", "properties": {"Id": {"nullable": true}, "Name": {"type": "string", "nullable": true}}, "additionalProperties": false}, "EnumResponseListResponseBase": {"type": "object", "properties": {"TotalItems": {"type": "integer", "format": "int64"}, "Entries": {"type": "array", "items": {"$ref": "#/components/schemas/EnumResponse"}, "nullable": true}, "Message": {"type": "string", "nullable": true}, "StatusCode": {"$ref": "#/components/schemas/EnumStatusCode"}}, "additionalProperties": false}, "EnumStatusCode": {"enum": [0, 1, 9, 30001], "type": "integer", "format": "int32"}, "ExportExcelMaterials": {"type": "object", "properties": {"FileByte": {"type": "string", "format": "byte", "nullable": true}}, "additionalProperties": false}, "ExportExcelMaterialsResponseBase": {"type": "object", "properties": {"TotalItems": {"type": "integer", "format": "int64"}, "Entries": {"$ref": "#/components/schemas/ExportExcelMaterials"}, "Message": {"type": "string", "nullable": true}, "StatusCode": {"$ref": "#/components/schemas/EnumStatusCode"}}, "additionalProperties": false}, "ExportExcelResponse": {"type": "object", "properties": {"FileByte": {"type": "string", "format": "byte", "nullable": true}}, "additionalProperties": false}, "ExportExcelResponseResponseBase": {"type": "object", "properties": {"TotalItems": {"type": "integer", "format": "int64"}, "Entries": {"$ref": "#/components/schemas/ExportExcelResponse"}, "Message": {"type": "string", "nullable": true}, "StatusCode": {"$ref": "#/components/schemas/EnumStatusCode"}}, "additionalProperties": false}, "FileApprove": {"type": "object", "properties": {"CFileName": {"type": "string", "nullable": true}, "CFile": {"type": "string", "nullable": true}}, "additionalProperties": false}, "FileRes": {"type": "object", "properties": {"CFileName": {"type": "string", "nullable": true}, "CFile": {"type": "string", "nullable": true}}, "additionalProperties": false}, "FileViewModel": {"type": "object", "properties": {"FileName": {"type": "string", "nullable": true}, "Base64String": {"type": "string", "nullable": true}, "FileExtension": {"type": "string", "nullable": true}}, "additionalProperties": false}, "FloorRange": {"type": "object", "properties": {"CFrom": {"type": "integer", "format": "int32"}, "CTo": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "FormItems": {"type": "object", "properties": {"CFormItemId": {"type": "integer", "format": "int32"}, "CItemName": {"type": "string", "nullable": true}, "CPart": {"type": "string", "nullable": true}, "CName": {"type": "string", "nullable": true}, "CLocation": {"type": "string", "nullable": true}, "CDesignFileUrl": {"type": "string", "nullable": true}, "CUiType": {"type": "integer", "format": "int32", "nullable": true}, "CTotalAnswer": {"type": "integer", "format": "int32"}, "CRequireAnswer": {"type": "integer", "format": "int32"}, "CRemarkType": {"type": "string", "nullable": true}, "tblFormItemHouseholds": {"type": "array", "items": {"$ref": "#/components/schemas/TblFormItemHousehold"}, "nullable": true}, "CFirstMatrialUrl": {"type": "string", "nullable": true}}, "additionalProperties": false}, "FunctionDTO": {"type": "object", "properties": {"CId": {"type": "integer", "format": "int32"}, "CName": {"type": "string", "nullable": true}, "CParentId": {"type": "integer", "format": "int32", "nullable": true}, "CPageUrl": {"type": "string", "nullable": true}, "CIsMenu": {"type": "boolean", "nullable": true}, "CMenuIndex": {"type": "integer", "format": "int32", "nullable": true}, "CCssStyle": {"type": "string", "nullable": true}, "CStatus": {"type": "integer", "format": "int32", "nullable": true}, "CIsDelete": {"type": "boolean", "nullable": true}, "CCompetenceType": {"type": "integer", "format": "int32", "nullable": true}, "CFlowId": {"type": "integer", "format": "int32"}, "Child": {"type": "array", "items": {"$ref": "#/components/schemas/FunctionDTO"}, "nullable": true}}, "additionalProperties": false}, "GetAllBuildCaseArgs": {"type": "object", "properties": {"PageIndex": {"type": "integer", "format": "int32"}, "PageSize": {"type": "integer", "format": "int32"}, "CName": {"type": "string", "nullable": true}, "CIsPagi": {"type": "boolean"}, "CStatus": {"type": "integer", "format": "int32", "nullable": true}}, "additionalProperties": false}, "GetAppoinmentArgs": {"type": "object", "properties": {"CBuildCaseID": {"type": "integer", "format": "int32"}, "CCustomerName": {"type": "string", "nullable": true}, "CPhone": {"type": "string", "nullable": true}, "CPreOrderDate": {"type": "string", "nullable": true}}, "additionalProperties": false}, "GetAppoinmentRes": {"type": "object", "properties": {"CChangePreOrderID": {"type": "integer", "format": "int32"}, "CHouseHold": {"type": "string", "nullable": true}, "CFloor": {"type": "integer", "format": "int32", "nullable": true}, "CCustomerName": {"type": "string", "nullable": true}, "CPhone": {"type": "string", "nullable": true}, "CPreOrderDate": {"type": "string", "format": "date-time"}, "CHour": {"type": "integer", "format": "int32"}, "CPeoples": {"type": "integer", "format": "int32"}, "CHasDesigner": {"type": "boolean"}, "CHouseID": {"type": "integer", "format": "int32"}, "CCreateDT": {"type": "string", "format": "date-time"}, "CRemark": {"type": "string", "nullable": true}, "CNeedMail": {"type": "boolean", "nullable": true}, "CStatus": {"type": "integer", "format": "int32", "nullable": true}, "CHouseRequirement": {"type": "string", "nullable": true}}, "additionalProperties": false}, "GetAppoinmentResListResponseBase": {"type": "object", "properties": {"TotalItems": {"type": "integer", "format": "int64"}, "Entries": {"type": "array", "items": {"$ref": "#/components/schemas/GetAppoinmentRes"}, "nullable": true}, "Message": {"type": "string", "nullable": true}, "StatusCode": {"$ref": "#/components/schemas/EnumStatusCode"}}, "additionalProperties": false}, "GetAppoinmentResResponseBase": {"type": "object", "properties": {"TotalItems": {"type": "integer", "format": "int64"}, "Entries": {"$ref": "#/components/schemas/GetAppoinmentRes"}, "Message": {"type": "string", "nullable": true}, "StatusCode": {"$ref": "#/components/schemas/EnumStatusCode"}}, "additionalProperties": false}, "GetBuildCaseByID": {"type": "object", "properties": {"CBuildCaseID": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "GetBuildCaseFileByID": {"type": "object", "properties": {"CBuildCaseFileID": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "GetBuildCaseMailListRequest": {"type": "object", "properties": {"PageIndex": {"type": "integer", "format": "int32"}, "PageSize": {"type": "integer", "format": "int32"}, "CBuildCaseId": {"type": "integer", "format": "int32", "nullable": true}, "CMailType": {"type": "integer", "format": "int32", "nullable": true}, "CMail": {"type": "string", "nullable": true}, "CBuildCaseMailId": {"type": "integer", "format": "int32", "nullable": true}}, "additionalProperties": false}, "GetBuildCaseMailListResponse": {"type": "object", "properties": {"CBuildCaseMailId": {"type": "integer", "format": "int32"}, "CBuildCaseid": {"type": "integer", "format": "int32"}, "CBuildCaseName": {"type": "string", "nullable": true}, "CMailType": {"type": "integer", "format": "int32"}, "CMail": {"type": "string", "nullable": true}, "CStatus": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "GetBuildCaseMailListResponseListResponseBase": {"type": "object", "properties": {"TotalItems": {"type": "integer", "format": "int64"}, "Entries": {"type": "array", "items": {"$ref": "#/components/schemas/GetBuildCaseMailListResponse"}, "nullable": true}, "Message": {"type": "string", "nullable": true}, "StatusCode": {"$ref": "#/components/schemas/EnumStatusCode"}}, "additionalProperties": false}, "GetBuildingSampleSelectionRes": {"type": "object", "properties": {"CRegularChangeItemId": {"type": "integer", "format": "int32"}, "CDesignFileUrl": {"type": "string", "nullable": true}, "CItemName": {"type": "string", "nullable": true}, "buildingSamples": {"type": "array", "items": {"$ref": "#/components/schemas/BuildingSample"}, "nullable": true}}, "additionalProperties": false}, "GetBuildingSampleSelectionResResponseBase": {"type": "object", "properties": {"TotalItems": {"type": "integer", "format": "int64"}, "Entries": {"$ref": "#/components/schemas/GetBuildingSampleSelectionRes"}, "Message": {"type": "string", "nullable": true}, "StatusCode": {"$ref": "#/components/schemas/EnumStatusCode"}}, "additionalProperties": false}, "GetChangePreOrderArgs": {"type": "object", "additionalProperties": false}, "GetChangePreOrderRespone": {"type": "object", "properties": {"CID": {"type": "integer", "format": "int32"}, "CPreOrderDate": {"type": "string", "format": "date-time"}, "CPeoples": {"type": "integer", "format": "int32"}, "CHasDesigner": {"type": "boolean"}, "CRemark": {"type": "string", "nullable": true}, "CNeedMail": {"type": "boolean", "nullable": true}, "CHour": {"type": "integer", "format": "int32", "nullable": true}, "CRequirement": {"type": "string", "nullable": true}, "CRequirementID": {"type": "array", "items": {"type": "integer", "format": "int32"}, "nullable": true}, "CMail": {"type": "string", "nullable": true}}, "additionalProperties": false}, "GetChangePreOrderResponeResponseBase": {"type": "object", "properties": {"TotalItems": {"type": "integer", "format": "int64"}, "Entries": {"$ref": "#/components/schemas/GetChangePreOrderRespone"}, "Message": {"type": "string", "nullable": true}, "StatusCode": {"$ref": "#/components/schemas/EnumStatusCode"}}, "additionalProperties": false}, "GetDisclaimerArgs": {"type": "object", "additionalProperties": false}, "GetFinalDocAfter": {"type": "object", "additionalProperties": false}, "GetFinalDocBefore": {"type": "object", "additionalProperties": false}, "GetFinalDocListByHouse": {"type": "object", "properties": {"PageIndex": {"type": "integer", "format": "int32"}, "PageSize": {"type": "integer", "format": "int32"}, "CHouseID": {"type": "integer", "format": "int32"}, "CDateStart": {"type": "string", "format": "date-time", "nullable": true}, "CDateEnd": {"type": "string", "format": "date-time", "nullable": true}}, "additionalProperties": false}, "GetFinalDocRes": {"type": "object", "properties": {"CID": {"type": "integer", "format": "int32"}, "CDocumentName": {"type": "string", "nullable": true}, "CLink": {"type": "string", "nullable": true}, "CSign": {"type": "boolean"}, "CNote": {"type": "string", "nullable": true}, "CSignDate": {"type": "string", "format": "date-time", "nullable": true}, "CCreateDT": {"type": "string", "format": "date-time", "nullable": true}, "CPayStatus": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "GetFinalDocResListResponseBase": {"type": "object", "properties": {"TotalItems": {"type": "integer", "format": "int64"}, "Entries": {"type": "array", "items": {"$ref": "#/components/schemas/GetFinalDocRes"}, "nullable": true}, "Message": {"type": "string", "nullable": true}, "StatusCode": {"$ref": "#/components/schemas/EnumStatusCode"}}, "additionalProperties": false}, "GetHourListAppointmentReq": {"type": "object", "properties": {"CBuildCaseID": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "GetHourListRespone": {"type": "object", "properties": {"CDate": {"type": "string", "format": "date-time"}, "CHour": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "GetHourListResponeListResponseBase": {"type": "object", "properties": {"TotalItems": {"type": "integer", "format": "int64"}, "Entries": {"type": "array", "items": {"$ref": "#/components/schemas/GetHourListRespone"}, "nullable": true}, "Message": {"type": "string", "nullable": true}, "StatusCode": {"$ref": "#/components/schemas/EnumStatusCode"}}, "additionalProperties": false}, "GetHouseAndFloorByBuildCaseIdRes": {"type": "object", "properties": {"CHouseHold": {"type": "string", "nullable": true}, "Floors": {"type": "array", "items": {"type": "integer", "format": "int32"}, "nullable": true}}, "additionalProperties": false}, "GetHouseAndFloorByBuildCaseIdResListResponseBase": {"type": "object", "properties": {"TotalItems": {"type": "integer", "format": "int64"}, "Entries": {"type": "array", "items": {"$ref": "#/components/schemas/GetHouseAndFloorByBuildCaseIdRes"}, "nullable": true}, "Message": {"type": "string", "nullable": true}, "StatusCode": {"$ref": "#/components/schemas/EnumStatusCode"}}, "additionalProperties": false}, "GetHouseByIDArgs": {"type": "object", "properties": {"CHouseID": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "GetHouseChangeDateReq": {"type": "object", "properties": {"CBuildCaseId": {"type": "integer", "format": "int32"}, "CChangeStartDate": {"type": "string", "format": "date-time", "nullable": true}, "CChangeEndDate": {"type": "string", "format": "date-time", "nullable": true}}, "additionalProperties": false}, "GetHouseChangeDateRes": {"type": "object", "properties": {"CHouseHold": {"type": "string", "nullable": true}, "CHouses": {"type": "array", "items": {"$ref": "#/components/schemas/CHouse"}, "nullable": true}}, "additionalProperties": false}, "GetHouseChangeDateResListResponseBase": {"type": "object", "properties": {"TotalItems": {"type": "integer", "format": "int64"}, "Entries": {"type": "array", "items": {"$ref": "#/components/schemas/GetHouseChangeDateRes"}, "nullable": true}, "Message": {"type": "string", "nullable": true}, "StatusCode": {"$ref": "#/components/schemas/EnumStatusCode"}}, "additionalProperties": false}, "GetHouseListArgs": {"type": "object", "properties": {"PageIndex": {"type": "integer", "format": "int32"}, "PageSize": {"type": "integer", "format": "int32"}, "CBuildCaseID": {"type": "integer", "format": "int32"}, "CPayStatus": {"type": "integer", "format": "int32", "nullable": true}, "CBuildingName": {"type": "string", "nullable": true}, "CHouseHold": {"type": "string", "nullable": true}, "CProgress": {"type": "integer", "format": "int32", "nullable": true}, "CSignStatus": {"type": "integer", "format": "int32", "nullable": true}, "CHouseType": {"type": "integer", "format": "int32", "nullable": true}, "CIsEnable": {"type": "boolean", "nullable": true}, "CFloor": {"$ref": "#/components/schemas/FloorRange"}, "CIsPagi": {"type": "boolean"}}, "additionalProperties": false}, "GetHouseListRes": {"type": "object", "properties": {"CID": {"type": "integer", "format": "int32"}, "CBuildingName": {"type": "string", "nullable": true}, "CHouseHold": {"type": "string", "nullable": true}, "CFloor": {"type": "integer", "format": "int32", "nullable": true}, "CHouseType": {"type": "integer", "format": "int32", "nullable": true}, "CIsChange": {"type": "boolean"}, "CProgress": {"type": "integer", "format": "int32"}, "CPayStatus": {"type": "integer", "format": "int32"}, "CSignStatus": {"type": "integer", "format": "int32", "nullable": true}, "CIsEnable": {"type": "boolean", "nullable": true}, "CProgressName": {"type": "string", "nullable": true}, "CHouseCode": {"type": "string", "nullable": true}}, "additionalProperties": false}, "GetHouseListResListResponseBase": {"type": "object", "properties": {"TotalItems": {"type": "integer", "format": "int64"}, "Entries": {"type": "array", "items": {"$ref": "#/components/schemas/GetHouseListRes"}, "nullable": true}, "Message": {"type": "string", "nullable": true}, "StatusCode": {"$ref": "#/components/schemas/EnumStatusCode"}}, "additionalProperties": false}, "GetHouseProgress": {"type": "object", "properties": {"CProgress": {"type": "integer", "format": "int32", "nullable": true}, "CIsChange": {"type": "boolean"}}, "additionalProperties": false}, "GetHouseProgressResponseBase": {"type": "object", "properties": {"TotalItems": {"type": "integer", "format": "int64"}, "Entries": {"$ref": "#/components/schemas/GetHouseProgress"}, "Message": {"type": "string", "nullable": true}, "StatusCode": {"$ref": "#/components/schemas/EnumStatusCode"}}, "additionalProperties": false}, "GetHouseReview": {"type": "object", "properties": {"CHouseReviewId": {"type": "integer", "format": "int32"}, "CHouseId": {"type": "integer", "format": "int32"}, "CReviewName": {"type": "string", "nullable": true}, "CSort": {"type": "integer", "format": "int32"}, "CFileUrl": {"type": "string", "nullable": true}, "CReviewDt": {"type": "string", "format": "date-time", "nullable": true}, "CReviewId": {"type": "integer", "format": "int32"}, "CIsReview": {"type": "boolean"}, "CCreateDt": {"type": "string", "format": "date-time", "nullable": true}, "CStatus": {"type": "integer", "format": "int32", "nullable": true}, "CIsDisable": {"type": "boolean", "nullable": true}, "CReviewType": {"type": "integer", "format": "int32", "nullable": true}}, "additionalProperties": false}, "GetHouseReviewListResponseBase": {"type": "object", "properties": {"TotalItems": {"type": "integer", "format": "int64"}, "Entries": {"type": "array", "items": {"$ref": "#/components/schemas/GetHouseReview"}, "nullable": true}, "Message": {"type": "string", "nullable": true}, "StatusCode": {"$ref": "#/components/schemas/EnumStatusCode"}}, "additionalProperties": false}, "GetInfoPictureListRequest": {"type": "object", "properties": {"PageIndex": {"type": "integer", "format": "int32"}, "PageSize": {"type": "integer", "format": "int32"}, "CBuildCaseId": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "GetInfoPictureListResponse": {"type": "object", "properties": {"CId": {"type": "integer", "format": "int32"}, "CName": {"type": "string", "nullable": true}, "CPart": {"type": "string", "nullable": true}, "CLocation": {"type": "string", "nullable": true}, "CSelectName": {"type": "string", "nullable": true}, "CPictureCode": {"type": "string", "nullable": true}, "CFile": {"type": "string", "nullable": true}, "CUpdateDT": {"type": "string", "format": "date-time", "nullable": true}}, "additionalProperties": false}, "GetInfoPictureListResponseListResponseBase": {"type": "object", "properties": {"TotalItems": {"type": "integer", "format": "int64"}, "Entries": {"type": "array", "items": {"$ref": "#/components/schemas/GetInfoPictureListResponse"}, "nullable": true}, "Message": {"type": "string", "nullable": true}, "StatusCode": {"$ref": "#/components/schemas/EnumStatusCode"}}, "additionalProperties": false}, "GetListBuildCaseFileArgs": {"type": "object", "properties": {"PageIndex": {"type": "integer", "format": "int32"}, "PageSize": {"type": "integer", "format": "int32"}, "CBuildCaseID": {"type": "integer", "format": "int32"}, "CID": {"type": "integer", "format": "int32"}, "CCategoryName": {"type": "string", "nullable": true}, "CName": {"type": "string", "nullable": true}, "CApproveStatus": {"type": "string", "nullable": true}, "CApproveDate": {"type": "string", "format": "date-time"}}, "additionalProperties": false}, "GetListBuildingArgs": {"type": "object", "properties": {"CBuildCaseID": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "GetListByHouseIDRequest": {"type": "object", "properties": {"CHouseID": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "GetListFinalDocArgs": {"type": "object", "properties": {"PageIndex": {"type": "integer", "format": "int32"}, "PageSize": {"type": "integer", "format": "int32"}, "CHouseID": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "GetListFinalDocRes": {"type": "object", "properties": {"CID": {"type": "integer", "format": "int32"}, "CDocumentName": {"type": "string", "nullable": true}, "CIsChange": {"type": "boolean"}, "CSignDate": {"type": "string", "format": "date-time", "nullable": true}, "CFile": {"type": "string", "nullable": true}, "CNote": {"type": "string", "nullable": true}, "CDocumentStatus": {"type": "integer", "format": "int32", "nullable": true}}, "additionalProperties": false}, "GetListFinalDocResListResponseBase": {"type": "object", "properties": {"TotalItems": {"type": "integer", "format": "int64"}, "Entries": {"type": "array", "items": {"$ref": "#/components/schemas/GetListFinalDocRes"}, "nullable": true}, "Message": {"type": "string", "nullable": true}, "StatusCode": {"$ref": "#/components/schemas/EnumStatusCode"}}, "additionalProperties": false}, "GetListFormItemReq": {"type": "object", "properties": {"PageIndex": {"type": "integer", "format": "int32"}, "PageSize": {"type": "integer", "format": "int32"}, "CBuildCaseId": {"type": "integer", "format": "int32"}, "CFormType": {"type": "integer", "format": "int32"}, "CIsPaging": {"type": "boolean"}}, "additionalProperties": false}, "GetListFormItemRes": {"type": "object", "properties": {"CBuildCaseId": {"type": "integer", "format": "int32"}, "CFormType": {"type": "integer", "format": "int32"}, "CFormId": {"type": "integer", "format": "int32"}, "CIsLock": {"type": "boolean", "nullable": true}, "formItems": {"type": "array", "items": {"$ref": "#/components/schemas/FormItems"}, "nullable": true}}, "additionalProperties": false}, "GetListFormItemResResponseBase": {"type": "object", "properties": {"TotalItems": {"type": "integer", "format": "int64"}, "Entries": {"$ref": "#/components/schemas/GetListFormItemRes"}, "Message": {"type": "string", "nullable": true}, "StatusCode": {"$ref": "#/components/schemas/EnumStatusCode"}}, "additionalProperties": false}, "GetListHouseHoldArgs": {"type": "object", "properties": {"CBuildCaseID": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "GetListHouseHoldRes": {"type": "object", "properties": {"CNoticeType": {"type": "integer", "format": "int32", "nullable": true}, "CHouseHoldList": {"type": "array", "items": {"type": "string"}, "nullable": true}}, "additionalProperties": false}, "GetListHouseHoldResListResponseBase": {"type": "object", "properties": {"TotalItems": {"type": "integer", "format": "int64"}, "Entries": {"type": "array", "items": {"$ref": "#/components/schemas/GetListHouseHoldRes"}, "nullable": true}, "Message": {"type": "string", "nullable": true}, "StatusCode": {"$ref": "#/components/schemas/EnumStatusCode"}}, "additionalProperties": false}, "GetListHouseRegularPicArgs": {"type": "object", "properties": {"PageIndex": {"type": "integer", "format": "int32"}, "PageSize": {"type": "integer", "format": "int32"}, "CBuildCaseID": {"type": "integer", "format": "int32"}, "CBuildingName": {"type": "string", "nullable": true}}, "additionalProperties": false}, "GetListHouseRegularPicRes": {"type": "object", "properties": {"CRegularPictureID": {"type": "integer", "format": "int32"}, "CFileName": {"type": "string", "nullable": true}, "CHouse": {"type": "array", "items": {"$ref": "#/components/schemas/HouseRes"}, "nullable": true}}, "additionalProperties": false}, "GetListHouseRegularPicResListResponseBase": {"type": "object", "properties": {"TotalItems": {"type": "integer", "format": "int64"}, "Entries": {"type": "array", "items": {"$ref": "#/components/schemas/GetListHouseRegularPicRes"}, "nullable": true}, "Message": {"type": "string", "nullable": true}, "StatusCode": {"$ref": "#/components/schemas/EnumStatusCode"}}, "additionalProperties": false}, "GetListQuotationRequest": {"type": "object", "properties": {"PageIndex": {"type": "integer", "format": "int32"}, "PageSize": {"type": "integer", "format": "int32"}, "CHouseID": {"type": "integer", "format": "int32", "nullable": true}, "CItemName": {"type": "string", "nullable": true}, "CStatus": {"type": "integer", "format": "int32", "nullable": true}, "CVersion": {"type": "integer", "format": "int32", "nullable": true}, "CIsDefault": {"type": "boolean", "nullable": true}}, "additionalProperties": false}, "GetListRegularChangeItemRes": {"type": "object", "properties": {"CRegularChangeItemId": {"type": "integer", "format": "int32"}, "CRegularChangeId": {"type": "integer", "format": "int32", "nullable": true}, "CName": {"type": "string", "nullable": true}, "CPart": {"type": "string", "nullable": true}, "CLocation": {"type": "string", "nullable": true}, "CTotalAnswer": {"type": "integer", "format": "int32"}, "CRequireAnswer": {"type": "integer", "format": "int32"}, "CUiType": {"type": "integer", "format": "int32"}, "CItemName": {"type": "string", "nullable": true}, "CIsSelect": {"type": "boolean", "nullable": true}, "CDesignFileUrl": {"type": "string", "nullable": true}, "CSelectDt": {"type": "string", "format": "date-time", "nullable": true}}, "additionalProperties": false}, "GetListRegularChangeItemResListResponseBase": {"type": "object", "properties": {"TotalItems": {"type": "integer", "format": "int64"}, "Entries": {"type": "array", "items": {"$ref": "#/components/schemas/GetListRegularChangeItemRes"}, "nullable": true}, "Message": {"type": "string", "nullable": true}, "StatusCode": {"$ref": "#/components/schemas/EnumStatusCode"}}, "additionalProperties": false}, "GetListRequirementRequest": {"type": "object", "properties": {"PageIndex": {"type": "integer", "format": "int32"}, "PageSize": {"type": "integer", "format": "int32"}, "CRequirement": {"type": "string", "nullable": true}, "CBuildCaseID": {"type": "integer", "format": "int32"}, "CStatus": {"type": "integer", "format": "int32"}, "CGroupName": {"type": "string", "nullable": true}, "CHouseType": {"type": "array", "items": {"type": "integer", "format": "int32"}, "nullable": true}}, "additionalProperties": false}, "GetListSpecialChangeRequest": {"type": "object", "properties": {"PageIndex": {"type": "integer", "format": "int32"}, "PageSize": {"type": "integer", "format": "int32"}, "CHouseId": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "GetMaterialListRequest": {"type": "object", "properties": {"PageIndex": {"type": "integer", "format": "int32"}, "PageSize": {"type": "integer", "format": "int32"}, "CBuildCaseId": {"type": "integer", "format": "int32", "nullable": true}, "CPlanUse": {"type": "boolean", "nullable": true}, "CSelectName": {"type": "string", "nullable": true}, "CImageCode": {"type": "string", "nullable": true}, "CMaterialId": {"type": "integer", "format": "int32", "nullable": true}, "CPagi": {"type": "boolean", "nullable": true}, "CIsMapping": {"type": "boolean", "nullable": true}, "CInfoImageCode": {"type": "string", "nullable": true}}, "additionalProperties": false}, "GetMaterialListResponse": {"type": "object", "properties": {"CId": {"type": "integer", "format": "int32"}, "CPictureId": {"type": "integer", "format": "int32"}, "CCreateDt": {"type": "string", "format": "date-time", "nullable": true}, "CCreator": {"type": "string", "nullable": true}, "CUpdateDt": {"type": "string", "format": "date-time", "nullable": true}, "CUpdator": {"type": "string", "nullable": true}, "CName": {"type": "string", "nullable": true}, "CPart": {"type": "string", "nullable": true}, "CLocation": {"type": "string", "nullable": true}, "CBuildCaseId": {"type": "integer", "format": "int32"}, "CPlanUse": {"type": "boolean", "nullable": true}, "CDescription": {"type": "string", "nullable": true}, "CImageCode": {"type": "string", "nullable": true}, "CSelectName": {"type": "string", "nullable": true}, "CInfoPictureId": {"type": "integer", "format": "int32"}, "CInfoImageCode": {"type": "string", "nullable": true}, "CPrice": {"type": "number", "format": "double", "nullable": true}, "CPicture": {"type": "string", "nullable": true}, "CInfoPicture": {"type": "string", "nullable": true}, "CShowPrice": {"type": "boolean", "nullable": true}, "CIsMapping": {"type": "boolean"}}, "additionalProperties": false}, "GetMaterialListResponseListResponseBase": {"type": "object", "properties": {"TotalItems": {"type": "integer", "format": "int64"}, "Entries": {"type": "array", "items": {"$ref": "#/components/schemas/GetMaterialListResponse"}, "nullable": true}, "Message": {"type": "string", "nullable": true}, "StatusCode": {"$ref": "#/components/schemas/EnumStatusCode"}}, "additionalProperties": false}, "GetMenuArgs": {"type": "object", "additionalProperties": false}, "GetMenuResponse": {"type": "object", "properties": {"Menu": {"type": "array", "items": {"$ref": "#/components/schemas/FunctionDTO"}, "nullable": true}}, "additionalProperties": false}, "GetMenuResponseResponseBase": {"type": "object", "properties": {"TotalItems": {"type": "integer", "format": "int64"}, "Entries": {"$ref": "#/components/schemas/GetMenuResponse"}, "Message": {"type": "string", "nullable": true}, "StatusCode": {"$ref": "#/components/schemas/EnumStatusCode"}}, "additionalProperties": false}, "GetMilestoneRes": {"type": "object", "properties": {"CMilestone": {"type": "integer", "format": "int32", "nullable": true}, "CIsLock": {"type": "boolean", "nullable": true}}, "additionalProperties": false}, "GetMilestoneResResponseBase": {"type": "object", "properties": {"TotalItems": {"type": "integer", "format": "int64"}, "Entries": {"$ref": "#/components/schemas/GetMilestoneRes"}, "Message": {"type": "string", "nullable": true}, "StatusCode": {"$ref": "#/components/schemas/EnumStatusCode"}}, "additionalProperties": false}, "GetPayStatus": {"type": "object", "properties": {"CPayStatus": {"type": "integer", "format": "int32", "nullable": true}, "CIsChange": {"type": "boolean"}}, "additionalProperties": false}, "GetPayStatusResponseBase": {"type": "object", "properties": {"TotalItems": {"type": "integer", "format": "int64"}, "Entries": {"$ref": "#/components/schemas/GetPayStatus"}, "Message": {"type": "string", "nullable": true}, "StatusCode": {"$ref": "#/components/schemas/EnumStatusCode"}}, "additionalProperties": false}, "GetPictureListRequest": {"type": "object", "properties": {"PageIndex": {"type": "integer", "format": "int32"}, "PageSize": {"type": "integer", "format": "int32"}, "CBuildCaseId": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "GetPictureListResponse": {"type": "object", "properties": {"CId": {"type": "integer", "format": "int32"}, "CName": {"type": "string", "nullable": true}, "CPart": {"type": "string", "nullable": true}, "CLocation": {"type": "string", "nullable": true}, "CSelectName": {"type": "string", "nullable": true}, "CPictureCode": {"type": "string", "nullable": true}, "CFile": {"type": "string", "nullable": true}, "CUpdateDT": {"type": "string", "format": "date-time", "nullable": true}}, "additionalProperties": false}, "GetPictureListResponseListResponseBase": {"type": "object", "properties": {"TotalItems": {"type": "integer", "format": "int64"}, "Entries": {"type": "array", "items": {"$ref": "#/components/schemas/GetPictureListResponse"}, "nullable": true}, "Message": {"type": "string", "nullable": true}, "StatusCode": {"$ref": "#/components/schemas/EnumStatusCode"}}, "additionalProperties": false}, "GetPreOrderSettingArgs": {"type": "object", "properties": {"CBuildCaseID": {"type": "integer", "format": "int32"}, "CDate": {"type": "string", "format": "date-time", "nullable": true}, "CStatus": {"type": "boolean", "nullable": true}, "CDateStart": {"type": "string", "format": "date-time", "nullable": true}, "CDateEnd": {"type": "string", "format": "date-time", "nullable": true}}, "additionalProperties": false}, "GetPreOrderSettingResponse": {"type": "object", "properties": {"CDate": {"type": "string", "format": "date-time"}, "CHour": {"type": "integer", "format": "int32"}, "CStatus": {"type": "boolean"}, "CID": {"type": "integer", "format": "int32"}, "CChangePreOrderID": {"type": "integer", "format": "int32", "nullable": true}, "CHouseHoldName": {"type": "string", "nullable": true}, "CHouseHoldDetailID": {"type": "integer", "format": "int32"}, "CPeoples": {"type": "integer", "format": "int32"}, "CCustomerName": {"type": "string", "nullable": true}, "CFloor": {"type": "integer", "format": "int32", "nullable": true}}, "additionalProperties": false}, "GetPreOrderSettingResponseListResponseBase": {"type": "object", "properties": {"TotalItems": {"type": "integer", "format": "int64"}, "Entries": {"type": "array", "items": {"$ref": "#/components/schemas/GetPreOrderSettingResponse"}, "nullable": true}, "Message": {"type": "string", "nullable": true}, "StatusCode": {"$ref": "#/components/schemas/EnumStatusCode"}}, "additionalProperties": false}, "GetQuotation": {"type": "object", "properties": {"CQuotationID": {"type": "integer", "format": "int32"}, "CHouseID": {"type": "integer", "format": "int32"}, "CItemName": {"type": "string", "nullable": true}, "CUnitPrice": {"type": "number", "format": "double"}, "CCount": {"type": "integer", "format": "int32"}, "CStatus": {"type": "integer", "format": "int32"}, "CVersion": {"type": "integer", "format": "int32"}, "CIsDeafult": {"type": "boolean"}}, "additionalProperties": false}, "GetQuotationByIDRequest": {"type": "object", "properties": {"CQuotationID": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "GetQuotationListResponseBase": {"type": "object", "properties": {"TotalItems": {"type": "integer", "format": "int64"}, "Entries": {"type": "array", "items": {"$ref": "#/components/schemas/GetQuotation"}, "nullable": true}, "Message": {"type": "string", "nullable": true}, "StatusCode": {"$ref": "#/components/schemas/EnumStatusCode"}}, "additionalProperties": false}, "GetQuotationResponseBase": {"type": "object", "properties": {"TotalItems": {"type": "integer", "format": "int64"}, "Entries": {"$ref": "#/components/schemas/GetQuotation"}, "Message": {"type": "string", "nullable": true}, "StatusCode": {"$ref": "#/components/schemas/EnumStatusCode"}}, "additionalProperties": false}, "GetRegularChangeDetailByItemIdRes": {"type": "object", "properties": {"CRegularChangeItemId": {"type": "integer", "format": "int32"}, "CDesignFileUrl": {"type": "string", "nullable": true}, "CItemName": {"type": "string", "nullable": true}, "regularChangeDetails": {"type": "array", "items": {"$ref": "#/components/schemas/RegularChangeDetail"}, "nullable": true}, "CTotalAnswer": {"type": "integer", "format": "int32"}, "CRequireAnswer": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "GetRegularChangeDetailByItemIdResResponseBase": {"type": "object", "properties": {"TotalItems": {"type": "integer", "format": "int64"}, "Entries": {"$ref": "#/components/schemas/GetRegularChangeDetailByItemIdRes"}, "Message": {"type": "string", "nullable": true}, "StatusCode": {"$ref": "#/components/schemas/EnumStatusCode"}}, "additionalProperties": false}, "GetRegularNoticeFileByIdRes": {"type": "object", "properties": {"tblRegularNoticeFile": {"$ref": "#/components/schemas/TblRegularNoticeFile"}, "tblRegularNoticeFileHouses": {"type": "array", "items": {"$ref": "#/components/schemas/TblRegularNoticeFileHouse"}, "nullable": true}}, "additionalProperties": false}, "GetRegularNoticeFileByIdResResponseBase": {"type": "object", "properties": {"TotalItems": {"type": "integer", "format": "int64"}, "Entries": {"$ref": "#/components/schemas/GetRegularNoticeFileByIdRes"}, "Message": {"type": "string", "nullable": true}, "StatusCode": {"$ref": "#/components/schemas/EnumStatusCode"}}, "additionalProperties": false}, "GetRegularNoticeFileListReq": {"type": "object", "properties": {"PageIndexLandLord": {"type": "integer", "format": "int32"}, "PageSizeLandLord": {"type": "integer", "format": "int32"}, "PageIndexSales": {"type": "integer", "format": "int32"}, "PageSizeSales": {"type": "integer", "format": "int32"}, "CBuildCaseId": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "GetRegularNoticeFileListRes": {"type": "object", "properties": {"CBuildCaseId": {"type": "integer", "format": "int32"}, "ListLandLords": {"type": "array", "items": {"$ref": "#/components/schemas/RegularNoticeFileList"}, "nullable": true}, "ListSales": {"type": "array", "items": {"$ref": "#/components/schemas/RegularNoticeFileList"}, "nullable": true}, "TotalListLandLords": {"type": "integer", "format": "int32"}, "TotalListSales": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "GetRegularNoticeFileListResResponseBase": {"type": "object", "properties": {"TotalItems": {"type": "integer", "format": "int64"}, "Entries": {"$ref": "#/components/schemas/GetRegularNoticeFileListRes"}, "Message": {"type": "string", "nullable": true}, "StatusCode": {"$ref": "#/components/schemas/EnumStatusCode"}}, "additionalProperties": false}, "GetRequirement": {"type": "object", "properties": {"CRequirementID": {"type": "integer", "format": "int32"}, "CBuildCaseID": {"type": "integer", "format": "int32"}, "CBuildCaseName": {"type": "string", "nullable": true}, "CRequirement": {"type": "string", "nullable": true}, "CStatus": {"type": "integer", "format": "int32"}, "CCreateDT": {"type": "string", "format": "date-time"}, "CCreator": {"type": "string", "nullable": true}, "CSort": {"type": "integer", "format": "int32"}, "CHouseType": {"type": "array", "items": {"type": "integer", "format": "int32"}, "nullable": true}, "CUnitPrice": {"type": "number", "format": "double", "nullable": true}, "CRemark": {"type": "string", "nullable": true}, "CGroupName": {"type": "string", "nullable": true}}, "additionalProperties": false}, "GetRequirementByIDRequest": {"type": "object", "properties": {"CRequirementID": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "GetRequirementListResponseBase": {"type": "object", "properties": {"TotalItems": {"type": "integer", "format": "int64"}, "Entries": {"type": "array", "items": {"$ref": "#/components/schemas/GetRequirement"}, "nullable": true}, "Message": {"type": "string", "nullable": true}, "StatusCode": {"$ref": "#/components/schemas/EnumStatusCode"}}, "additionalProperties": false}, "GetRequirementResponseBase": {"type": "object", "properties": {"TotalItems": {"type": "integer", "format": "int64"}, "Entries": {"$ref": "#/components/schemas/GetRequirement"}, "Message": {"type": "string", "nullable": true}, "StatusCode": {"$ref": "#/components/schemas/EnumStatusCode"}}, "additionalProperties": false}, "GetReviewByIdRes": {"type": "object", "properties": {"tblReview": {"$ref": "#/components/schemas/TblReview"}, "CExamineNote": {"type": "string", "nullable": true}, "reviewHouseHolds": {"type": "array", "items": {"$ref": "#/components/schemas/ReviewHouseHold"}, "nullable": true}, "tblExamineLogs": {"type": "array", "items": {"$ref": "#/components/schemas/TblExamineLog"}, "nullable": true}}, "additionalProperties": false}, "GetReviewByIdResResponseBase": {"type": "object", "properties": {"TotalItems": {"type": "integer", "format": "int64"}, "Entries": {"$ref": "#/components/schemas/GetReviewByIdRes"}, "Message": {"type": "string", "nullable": true}, "StatusCode": {"$ref": "#/components/schemas/EnumStatusCode"}}, "additionalProperties": false}, "GetReviewListReq": {"type": "object", "properties": {"PageIndex": {"type": "integer", "format": "int32"}, "PageSize": {"type": "integer", "format": "int32"}, "CReviewName": {"type": "string", "nullable": true}, "CBuildCaseID": {"type": "integer", "format": "int32"}, "CStatus": {"type": "integer", "format": "int32"}, "CReviewType": {"type": "integer", "format": "int32"}, "CExamineStatus": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "GetReviewListRes": {"type": "object", "properties": {"CReviewId": {"type": "integer", "format": "int32"}, "CReviewName": {"type": "string", "nullable": true}, "CHouse": {"type": "array", "items": {"type": "string"}, "nullable": true}, "CSort": {"type": "integer", "format": "int32"}, "CStatus": {"type": "integer", "format": "int32"}, "CEnableEdit": {"type": "boolean", "nullable": true}, "CReviewType": {"type": "integer", "format": "int32", "nullable": true}, "CExamineStatus": {"type": "integer", "format": "int32"}, "CActionDate": {"type": "string", "format": "date-time", "nullable": true}}, "additionalProperties": false}, "GetReviewListResListResponseBase": {"type": "object", "properties": {"TotalItems": {"type": "integer", "format": "int64"}, "Entries": {"type": "array", "items": {"$ref": "#/components/schemas/GetReviewListRes"}, "nullable": true}, "Message": {"type": "string", "nullable": true}, "StatusCode": {"$ref": "#/components/schemas/EnumStatusCode"}}, "additionalProperties": false}, "GetSpecialChangeFileArgs": {"type": "object", "additionalProperties": false}, "GetSpecialNoticeFileByIdRes": {"type": "object", "properties": {"tblSpecialNoticeFile": {"$ref": "#/components/schemas/TblSpecialNoticeFile"}, "CExamineNote": {"type": "string", "nullable": true}, "tblSpecialNoticeFileHouses": {"type": "array", "items": {"$ref": "#/components/schemas/HouseSpecialNoticeFile"}, "nullable": true}, "tblExamineLogs": {"type": "array", "items": {"$ref": "#/components/schemas/TblExamineLog"}, "nullable": true}, "CExamineStauts": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "GetSpecialNoticeFileByIdResResponseBase": {"type": "object", "properties": {"TotalItems": {"type": "integer", "format": "int64"}, "Entries": {"$ref": "#/components/schemas/GetSpecialNoticeFileByIdRes"}, "Message": {"type": "string", "nullable": true}, "StatusCode": {"$ref": "#/components/schemas/EnumStatusCode"}}, "additionalProperties": false}, "GetSpecialNoticeFileListReq": {"type": "object", "properties": {"PageIndexLandLord": {"type": "integer", "format": "int32"}, "PageSizeLandLord": {"type": "integer", "format": "int32"}, "PageIndexSales": {"type": "integer", "format": "int32"}, "PageSizeSales": {"type": "integer", "format": "int32"}, "CBuildCaseId": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "GetSpecialNoticeFileListRes": {"type": "object", "properties": {"CBuildCaseId": {"type": "integer", "format": "int32"}, "ListLandLords": {"type": "array", "items": {"$ref": "#/components/schemas/SpecialNoticeFileList"}, "nullable": true}, "ListSales": {"type": "array", "items": {"$ref": "#/components/schemas/SpecialNoticeFileList"}, "nullable": true}, "TotalListLandLords": {"type": "integer", "format": "int32"}, "TotalListSales": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "GetSpecialNoticeFileListResResponseBase": {"type": "object", "properties": {"TotalItems": {"type": "integer", "format": "int64"}, "Entries": {"$ref": "#/components/schemas/GetSpecialNoticeFileListRes"}, "Message": {"type": "string", "nullable": true}, "StatusCode": {"$ref": "#/components/schemas/EnumStatusCode"}}, "additionalProperties": false}, "GetSumaryRegularChangeItemRes": {"type": "object", "properties": {"CRegularChangeItemId": {"type": "integer", "format": "int32"}, "CItemName": {"type": "string", "nullable": true}, "RegularDetails": {"type": "array", "items": {"$ref": "#/components/schemas/RegularDetail"}, "nullable": true}, "CTotalPrice": {"type": "number", "format": "double", "nullable": true}, "CShowPrice": {"type": "boolean", "nullable": true}}, "additionalProperties": false}, "GetSumaryRegularChangeItemResListResponseBase": {"type": "object", "properties": {"TotalItems": {"type": "integer", "format": "int64"}, "Entries": {"type": "array", "items": {"$ref": "#/components/schemas/GetSumaryRegularChangeItemRes"}, "nullable": true}, "Message": {"type": "string", "nullable": true}, "StatusCode": {"$ref": "#/components/schemas/EnumStatusCode"}}, "additionalProperties": false}, "GetUserBuildCaseArgs": {"type": "object", "additionalProperties": false}, "HouseChangePreOrderArgs": {"type": "object", "properties": {"CID": {"type": "integer", "format": "int32", "nullable": true}, "CPreOderDate": {"type": "string", "format": "date-time"}, "CPeoples": {"type": "integer", "format": "int32"}, "CHasDesigner": {"type": "boolean"}, "CHour": {"type": "integer", "format": "int32"}, "CNeedMail": {"type": "boolean"}, "CStatus": {"type": "integer", "format": "int32"}, "CRemark": {"type": "string", "nullable": true}, "CHouseRequirement": {"type": "array", "items": {"$ref": "#/components/schemas/HouseRequirement"}, "nullable": true}}, "additionalProperties": false}, "HouseGetChangeDateArgs": {"type": "object", "additionalProperties": false}, "HouseGetChangeDateRespone": {"type": "object", "properties": {"CChangeStartDate": {"type": "string", "format": "date-time", "nullable": true}, "CChangeEndDate": {"type": "string", "format": "date-time", "nullable": true}}, "additionalProperties": false}, "HouseGetChangeDateResponeResponseBase": {"type": "object", "properties": {"TotalItems": {"type": "integer", "format": "int64"}, "Entries": {"$ref": "#/components/schemas/HouseGetChangeDateRespone"}, "Message": {"type": "string", "nullable": true}, "StatusCode": {"$ref": "#/components/schemas/EnumStatusCode"}}, "additionalProperties": false}, "HouseGetHourListArgs": {"type": "object", "properties": {"CChangePreOderID": {"type": "integer", "format": "int32", "nullable": true}}, "additionalProperties": false}, "HouseLoginRequest": {"type": "object", "properties": {"BuildCaseId": {"type": "integer", "format": "int32"}, "HoldDetail": {"type": "string", "nullable": true}, "Floor": {"type": "integer", "format": "int32"}, "SecureKey": {"type": "string", "nullable": true}}, "additionalProperties": false}, "HouseLoginResponse": {"type": "object", "properties": {"jwtToken": {"type": "string", "nullable": true}, "CIsChange": {"type": "boolean"}}, "additionalProperties": false}, "HouseLoginResponseResponseBase": {"type": "object", "properties": {"TotalItems": {"type": "integer", "format": "int64"}, "Entries": {"$ref": "#/components/schemas/HouseLoginResponse"}, "Message": {"type": "string", "nullable": true}, "StatusCode": {"$ref": "#/components/schemas/EnumStatusCode"}}, "additionalProperties": false}, "HouseLoginStep2Request": {"type": "object", "properties": {"CPhone": {"type": "string", "nullable": true}, "CMail": {"type": "string", "nullable": true}}, "additionalProperties": false}, "HouseRegularPic": {"type": "object", "properties": {"CFileName": {"type": "string", "nullable": true}, "CFileURL": {"type": "string", "nullable": true}}, "additionalProperties": false}, "HouseRegularPicResponseBase": {"type": "object", "properties": {"TotalItems": {"type": "integer", "format": "int64"}, "Entries": {"$ref": "#/components/schemas/HouseRegularPic"}, "Message": {"type": "string", "nullable": true}, "StatusCode": {"$ref": "#/components/schemas/EnumStatusCode"}}, "additionalProperties": false}, "HouseRequirement": {"type": "object", "properties": {"CHouseRequirementID": {"type": "integer", "format": "int32"}, "CIsSelect": {"type": "boolean"}}, "additionalProperties": false}, "HouseRequirementRes": {"type": "object", "properties": {"CHouseRequirementID": {"type": "integer", "format": "int32"}, "CIsSelect": {"type": "boolean"}, "CRequirement": {"type": "string", "nullable": true}, "CSort": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "HouseRequirementResListResponseBase": {"type": "object", "properties": {"TotalItems": {"type": "integer", "format": "int64"}, "Entries": {"type": "array", "items": {"$ref": "#/components/schemas/HouseRequirementRes"}, "nullable": true}, "Message": {"type": "string", "nullable": true}, "StatusCode": {"$ref": "#/components/schemas/EnumStatusCode"}}, "additionalProperties": false}, "HouseRes": {"type": "object", "properties": {"CHouseID": {"type": "integer", "format": "int32"}, "CHouseHold": {"type": "string", "nullable": true}, "CIsSelect": {"type": "boolean"}}, "additionalProperties": false}, "HouseReview": {"type": "object", "properties": {"CHouseID": {"type": "integer", "format": "int32"}, "CHouseHold": {"type": "string", "nullable": true}, "CFloor": {"type": "integer", "format": "int32"}, "CIsSelect": {"type": "boolean"}}, "additionalProperties": false}, "HouseSpecialNoticeFile": {"type": "object", "properties": {"CHouseID": {"type": "integer", "format": "int32"}, "CHouseHold": {"type": "string", "nullable": true}, "CFloor": {"type": "integer", "format": "int32", "nullable": true}, "CIsSelect": {"type": "boolean"}}, "additionalProperties": false}, "LockFormItemReq": {"type": "object", "properties": {"CBuildCaseId": {"type": "integer", "format": "int32"}, "CFormId": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "PictureInfo": {"type": "object", "properties": {"CFile": {"type": "string", "nullable": true}, "CDescription": {"type": "string", "nullable": true}}, "additionalProperties": false}, "PreOrderSetting": {"type": "object", "properties": {"CBuildCaseID": {"type": "integer", "format": "int32"}, "CDate": {"type": "string", "format": "date-time"}, "CHour": {"type": "integer", "format": "int32"}, "CIsDelete": {"type": "boolean"}}, "additionalProperties": false}, "RegularChangeDetail": {"type": "object", "properties": {"CRegularChangeDetailId": {"type": "integer", "format": "int32"}, "CSelectName": {"type": "string", "nullable": true}, "CDescription": {"type": "string", "nullable": true}, "CPicture": {"$ref": "#/components/schemas/PictureInfo"}, "CInfoPicture": {"$ref": "#/components/schemas/PictureInfo"}, "CIsSelect": {"type": "boolean", "nullable": true}}, "additionalProperties": false}, "RegularDetail": {"type": "object", "properties": {"CSelectName": {"type": "string", "nullable": true}, "CFile": {"type": "string", "nullable": true}, "CPrice": {"type": "number", "format": "double", "nullable": true}}, "additionalProperties": false}, "RegularNoticeFileList": {"type": "object", "properties": {"CFileName": {"type": "string", "nullable": true}, "CHouseHold": {"type": "array", "items": {"type": "string"}, "nullable": true}, "CRegularNoticeFileId": {"type": "integer", "format": "int32"}, "CNoticeType": {"type": "integer", "format": "int32"}, "CStatus": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "RegularRemark": {"type": "object", "properties": {"CRegularChangeItemRemarkTypeID": {"type": "integer", "format": "int32", "nullable": true}, "CTypeName": {"type": "string", "nullable": true}, "CIsSelect": {"type": "boolean", "nullable": true}}, "additionalProperties": false}, "RegularRemarkArgs": {"type": "object", "properties": {"CRegularChangeItemRemarkTypeID": {"type": "integer", "format": "int32", "nullable": true}}, "additionalProperties": false}, "ReviewHouseHold": {"type": "object", "properties": {"CHouseHold": {"type": "string", "nullable": true}, "CHouseType": {"type": "integer", "format": "int32"}, "CIsSelect": {"type": "boolean"}, "CFloor": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "SaveBuildCaseArgs": {"type": "object", "properties": {"CBuildCaseID": {"type": "integer", "format": "int32", "nullable": true}, "CBuildCaseName": {"type": "string", "nullable": true}, "CStatus": {"type": "integer", "format": "int32"}, "CFrontImage": {"type": "string", "nullable": true}, "CSystemInstruction": {"type": "string", "nullable": true}, "CFinalFileNotice": {"type": "string", "nullable": true}, "CIsConfirm": {"type": "boolean"}, "CShowPrice": {"type": "boolean", "nullable": true}, "CShowSignAll": {"type": "boolean", "nullable": true}, "CCustomerSign": {"type": "boolean"}}, "additionalProperties": false}, "SaveBuildCaseMailRequest": {"type": "object", "properties": {"CBuildCaseMailId": {"type": "integer", "format": "int32", "nullable": true}, "CBuildCaseId": {"type": "integer", "format": "int32"}, "CMail": {"type": "string", "nullable": true}, "CMailType": {"type": "integer", "format": "int32"}, "CStatus": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "SaveDataQuotation": {"type": "object", "properties": {"CQuotationID": {"type": "integer", "format": "int32", "nullable": true}, "CHouseID": {"type": "integer", "format": "int32"}, "CItemName": {"type": "string", "nullable": true}, "CUnitPrice": {"type": "number", "format": "double"}, "CCount": {"type": "integer", "format": "int32"}, "CStatus": {"type": "integer", "format": "int32"}, "CVersion": {"type": "integer", "format": "int32"}, "CIsDeafult": {"type": "boolean"}}, "additionalProperties": false}, "SaveDataRequirement": {"type": "object", "properties": {"CRequirementID": {"type": "integer", "format": "int32", "nullable": true}, "CBuildCaseID": {"type": "integer", "format": "int32"}, "CRequirement": {"type": "string", "nullable": true}, "CStatus": {"type": "integer", "format": "int32"}, "CSort": {"type": "integer", "format": "int32"}, "CHouseType": {"type": "array", "items": {"type": "integer", "format": "int32"}, "nullable": true}, "CUnitPrice": {"type": "number", "format": "double"}, "CRemark": {"type": "string", "nullable": true}, "CGroupName": {"type": "string", "nullable": true}}, "additionalProperties": false}, "SaveHouseChangeDateReq": {"type": "object", "properties": {"CHouseId": {"type": "integer", "format": "int32"}, "CChangeStartDate": {"type": "string", "format": "date-time", "nullable": true}, "CChangeEndDate": {"type": "string", "format": "date-time", "nullable": true}}, "additionalProperties": false}, "SaveListFormItemReq": {"type": "object", "properties": {"CFormItemId": {"type": "integer", "format": "int32", "nullable": true}, "CItemName": {"type": "string", "nullable": true}, "CName": {"type": "string", "nullable": true}, "CPart": {"type": "string", "nullable": true}, "CLocation": {"type": "string", "nullable": true}, "CDesignFileUrl": {"type": "string", "nullable": true}, "CRemarkType": {"type": "string", "nullable": true}, "CTotalAnswer": {"type": "integer", "format": "int32"}, "CRequireAnswer": {"type": "integer", "format": "int32"}, "CUiType": {"type": "integer", "format": "int32"}, "CFile": {"$ref": "#/components/schemas/FileViewModel"}, "CFormItemHouseHold": {"type": "array", "items": {"type": "string"}, "nullable": true}}, "additionalProperties": false}, "SaveMaterialArgs": {"type": "object", "properties": {"CMaterialId": {"type": "integer", "format": "int32", "nullable": true}, "CName": {"type": "string", "nullable": true}, "CPart": {"type": "string", "nullable": true}, "CLocation": {"type": "string", "nullable": true}, "CSelectName": {"type": "string", "nullable": true}, "CImageCode": {"type": "string", "nullable": true}, "CDescription": {"type": "string", "nullable": true}, "CPrice": {"type": "integer", "format": "int32", "nullable": true}, "CBuildCaseId": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "SavePreOrderSetting": {"type": "object", "properties": {"CSavePreOrderSetting": {"type": "array", "items": {"$ref": "#/components/schemas/PreOrderSetting"}, "nullable": true}}, "additionalProperties": false}, "SaveRegularChangeDetailRequest": {"type": "object", "properties": {"CRegularChangeDetailId": {"type": "integer", "format": "int32"}, "CRegularChangeItemlId": {"type": "integer", "format": "int32"}, "CRemark": {"type": "string", "nullable": true}, "CResultHtml": {"type": "string", "nullable": true}, "RegularRemark": {"$ref": "#/components/schemas/RegularRemarkArgs"}}, "additionalProperties": false}, "SpecialChangeAvailableArgs": {"type": "object", "properties": {"PageIndex": {"type": "integer", "format": "int32"}, "PageSize": {"type": "integer", "format": "int32"}, "CHouseID": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "SpecialChangeAvailableRes": {"type": "object", "properties": {"CID": {"type": "integer", "format": "int32"}, "CChangeDate": {"type": "string", "format": "date-time"}, "CDrawingName": {"type": "string", "nullable": true}, "CFileName": {"type": "string", "nullable": true}, "CFile": {"type": "string", "nullable": true}, "CCreateDT": {"type": "string", "format": "date-time", "nullable": true}, "CApproveDate": {"type": "string", "format": "date-time", "nullable": true}}, "additionalProperties": false}, "SpecialChangeAvailableResListResponseBase": {"type": "object", "properties": {"TotalItems": {"type": "integer", "format": "int64"}, "Entries": {"type": "array", "items": {"$ref": "#/components/schemas/SpecialChangeAvailableRes"}, "nullable": true}, "Message": {"type": "string", "nullable": true}, "StatusCode": {"$ref": "#/components/schemas/EnumStatusCode"}}, "additionalProperties": false}, "SpecialChangeFile": {"type": "object", "properties": {"CFileBlood": {"type": "string", "nullable": true}, "CFileName": {"type": "string", "nullable": true}, "CFileType": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "SpecialChangeFileGroup": {"type": "object", "properties": {"CChangeDate": {"type": "string", "format": "date-time"}, "SpecialChangeFiles": {"type": "array", "items": {"$ref": "#/components/schemas/SpecialChangeFileRespone"}, "nullable": true}}, "additionalProperties": false}, "SpecialChangeFileGroupListResponseBase": {"type": "object", "properties": {"TotalItems": {"type": "integer", "format": "int64"}, "Entries": {"type": "array", "items": {"$ref": "#/components/schemas/SpecialChangeFileGroup"}, "nullable": true}, "Message": {"type": "string", "nullable": true}, "StatusCode": {"$ref": "#/components/schemas/EnumStatusCode"}}, "additionalProperties": false}, "SpecialChangeFileRespone": {"type": "object", "properties": {"CFile": {"type": "string", "nullable": true}, "CFileName": {"type": "string", "nullable": true}, "CChangeDate": {"type": "string", "format": "date-time"}}, "additionalProperties": false}, "SpecialChangeRes": {"type": "object", "properties": {"CSpecialChangeID": {"type": "integer", "format": "int32"}, "CDrawingName": {"type": "string", "nullable": true}, "CChangeDate": {"type": "string", "format": "date-time"}, "CCreateDT": {"type": "string", "format": "date-time", "nullable": true}, "CIsApprove": {"type": "boolean", "nullable": true}, "CApproveRemark": {"type": "string", "nullable": true}, "CApproveDate": {"type": "string", "format": "date-time", "nullable": true}, "CFileRes": {"type": "array", "items": {"$ref": "#/components/schemas/FileRes"}, "nullable": true}}, "additionalProperties": false}, "SpecialChangeResListResponseBase": {"type": "object", "properties": {"TotalItems": {"type": "integer", "format": "int64"}, "Entries": {"type": "array", "items": {"$ref": "#/components/schemas/SpecialChangeRes"}, "nullable": true}, "Message": {"type": "string", "nullable": true}, "StatusCode": {"$ref": "#/components/schemas/EnumStatusCode"}}, "additionalProperties": false}, "SpecialChangeResResponseBase": {"type": "object", "properties": {"TotalItems": {"type": "integer", "format": "int64"}, "Entries": {"$ref": "#/components/schemas/SpecialChangeRes"}, "Message": {"type": "string", "nullable": true}, "StatusCode": {"$ref": "#/components/schemas/EnumStatusCode"}}, "additionalProperties": false}, "SpecialNoticeFileList": {"type": "object", "properties": {"CFile": {"type": "string", "nullable": true}, "CFileName": {"type": "string", "nullable": true}, "CHouseHold": {"type": "array", "items": {"type": "string"}, "nullable": true}, "CSpecialNoticeFileId": {"type": "integer", "format": "int32"}, "CNoticeType": {"type": "integer", "format": "int32"}, "CStatus": {"type": "integer", "format": "int32"}, "CApproveDate": {"type": "string", "format": "date-time", "nullable": true}, "CExamineStatus": {"type": "integer", "format": "int32", "nullable": true}, "CHouseld": {"type": "array", "items": {"type": "integer", "format": "int32"}, "nullable": true}}, "additionalProperties": false}, "StringListResponseBase": {"type": "object", "properties": {"TotalItems": {"type": "integer", "format": "int64"}, "Entries": {"type": "array", "items": {"type": "string"}, "nullable": true}, "Message": {"type": "string", "nullable": true}, "StatusCode": {"$ref": "#/components/schemas/EnumStatusCode"}}, "additionalProperties": false}, "StringResponseBase": {"type": "object", "properties": {"TotalItems": {"type": "integer", "format": "int64"}, "Entries": {"type": "string", "nullable": true}, "Message": {"type": "string", "nullable": true}, "StatusCode": {"$ref": "#/components/schemas/EnumStatusCode"}}, "additionalProperties": false}, "TblExamineLog": {"type": "object", "properties": {"CExamineLogId": {"type": "integer", "format": "int32"}, "CExamineId": {"type": "integer", "format": "int32"}, "CLevel": {"type": "integer", "format": "int32"}, "CAction": {"type": "integer", "format": "int32"}, "CExamineNote": {"type": "string", "nullable": true}, "CCreator": {"type": "string", "nullable": true}, "CCreateDt": {"type": "string", "format": "date-time"}}, "additionalProperties": false}, "TblFinalDocument": {"type": "object", "properties": {"CId": {"type": "integer", "format": "int32"}, "CDocumentName": {"type": "string", "nullable": true}, "CHouseId": {"type": "integer", "format": "int32"}, "CApproveDate": {"type": "string", "format": "date-time", "nullable": true}, "CApprover": {"type": "string", "nullable": true}, "CRegularChangeId": {"type": "integer", "format": "int32"}, "CApproveRemark": {"type": "string", "nullable": true}, "CSign": {"type": "string", "nullable": true}, "CFileBefore": {"type": "string", "nullable": true}, "CFileAfter": {"type": "string", "nullable": true}, "CSignDate": {"type": "string", "format": "date-time", "nullable": true}, "CCreateDt": {"type": "string", "format": "date-time"}, "CCreator": {"type": "string", "nullable": true}, "CUpdateDt": {"type": "string", "format": "date-time"}, "CUpdator": {"type": "string", "nullable": true}, "CIsApprove": {"type": "boolean", "nullable": true}, "CDocumentStatus": {"type": "integer", "format": "int32", "nullable": true}, "CIsChange": {"type": "boolean"}, "CNote": {"type": "string", "nullable": true}}, "additionalProperties": false}, "TblFinalDocumentListResponseBase": {"type": "object", "properties": {"TotalItems": {"type": "integer", "format": "int64"}, "Entries": {"type": "array", "items": {"$ref": "#/components/schemas/TblFinalDocument"}, "nullable": true}, "Message": {"type": "string", "nullable": true}, "StatusCode": {"$ref": "#/components/schemas/EnumStatusCode"}}, "additionalProperties": false}, "TblFormItemHousehold": {"type": "object", "properties": {"CFormItemHouseholdId": {"type": "integer", "format": "int32"}, "CFormItemId": {"type": "integer", "format": "int32"}, "CHousehold": {"type": "string", "nullable": true}, "CIsSelect": {"type": "boolean"}}, "additionalProperties": false}, "TblHouse": {"type": "object", "properties": {"CId": {"type": "integer", "format": "int32"}, "CBuildCaseId": {"type": "integer", "format": "int32"}, "CCreateDt": {"type": "string", "format": "date-time", "nullable": true}, "CCreator": {"type": "string", "nullable": true}, "CUpdateDt": {"type": "string", "format": "date-time", "nullable": true}, "CUpdator": {"type": "string", "nullable": true}, "CMail": {"type": "string", "nullable": true}, "CFloor": {"type": "integer", "format": "int32"}, "CRegularChangeId": {"type": "integer", "format": "int32"}, "CChangeStartDate": {"type": "string", "format": "date-time", "nullable": true}, "CChangeEndDate": {"type": "string", "format": "date-time", "nullable": true}, "CIsChange": {"type": "boolean"}, "CSpecialChangeId": {"type": "integer", "format": "int32", "nullable": true}, "CPayStatus": {"type": "integer", "format": "int32"}, "CHouseholdDetailId": {"type": "integer", "format": "int32"}, "CHouseType": {"type": "integer", "format": "int32"}, "CProgress": {"type": "integer", "format": "int32"}, "CCustomerName": {"type": "string", "nullable": true}, "CNationalId": {"type": "string", "nullable": true}, "CDisclaimerSign": {"type": "string", "nullable": true}, "CFinalDocumentId": {"type": "integer", "format": "int32", "nullable": true}, "CPhone": {"type": "string", "nullable": true}, "CHousehold": {"type": "string", "nullable": true}, "CSignStatus": {"type": "integer", "format": "int32"}, "CIsEnable": {"type": "boolean"}, "CBuildingName": {"type": "string", "nullable": true}, "CHouseholdCode": {"type": "string", "nullable": true}, "CRegularPictureId": {"type": "integer", "format": "int32", "nullable": true}, "CRegularNoticeFileId": {"type": "integer", "format": "int32", "nullable": true}, "CSpecialNoticeFileId": {"type": "integer", "format": "int32", "nullable": true}, "CMileStone": {"type": "integer", "format": "int32", "nullable": true}, "CIsLock": {"type": "boolean"}, "CTotalPrice": {"type": "number", "format": "double", "nullable": true}, "CSecureKey": {"type": "string", "nullable": true}, "CQuotationVersion": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "TblHouseResponseBase": {"type": "object", "properties": {"TotalItems": {"type": "integer", "format": "int64"}, "Entries": {"$ref": "#/components/schemas/TblHouse"}, "Message": {"type": "string", "nullable": true}, "StatusCode": {"$ref": "#/components/schemas/EnumStatusCode"}}, "additionalProperties": false}, "TblRegularNoticeFile": {"type": "object", "properties": {"CRegularNoticeFileId": {"type": "integer", "format": "int32"}, "CBuildCaseId": {"type": "integer", "format": "int32"}, "CNoticeType": {"type": "integer", "format": "int32"}, "CFileName": {"type": "string", "nullable": true}, "CFileUrl": {"type": "string", "nullable": true}, "CCreateDt": {"type": "string", "format": "date-time"}, "CCreator": {"type": "string", "nullable": true}, "CUpdateDt": {"type": "string", "format": "date-time"}, "CUpdator": {"type": "string", "nullable": true}, "CStatus": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "TblRegularNoticeFileHouse": {"type": "object", "properties": {"CRegularNoticeFileHouseholdId": {"type": "integer", "format": "int32"}, "CRegularNoticeFileId": {"type": "integer", "format": "int32"}, "CHousehold": {"type": "string", "nullable": true}, "CIsSelected": {"type": "boolean"}}, "additionalProperties": false}, "TblRegularNoticeFileResponseBase": {"type": "object", "properties": {"TotalItems": {"type": "integer", "format": "int64"}, "Entries": {"$ref": "#/components/schemas/TblRegularNoticeFile"}, "Message": {"type": "string", "nullable": true}, "StatusCode": {"$ref": "#/components/schemas/EnumStatusCode"}}, "additionalProperties": false}, "TblReview": {"type": "object", "properties": {"CReviewId": {"type": "integer", "format": "int32"}, "CBuildCaseId": {"type": "integer", "format": "int32"}, "CReviewName": {"type": "string", "nullable": true}, "CSort": {"type": "integer", "format": "int32"}, "CEnableEdit": {"type": "boolean", "nullable": true}, "CStatus": {"type": "integer", "format": "int32"}, "CCreateDt": {"type": "string", "format": "date-time"}, "CCreator": {"type": "string", "nullable": true}, "CUpdateDt": {"type": "string", "format": "date-time"}, "CUpdator": {"type": "string", "nullable": true}, "CApproveDate": {"type": "string", "format": "date-time", "nullable": true}, "CApprover": {"type": "string", "nullable": true}, "CReviewType": {"type": "integer", "format": "int32", "nullable": true}, "CFileUrl": {"type": "string", "nullable": true}}, "additionalProperties": false}, "TblSpecialNoticeFile": {"type": "object", "properties": {"CSpecialNoticeFileId": {"type": "integer", "format": "int32"}, "CBuildCaseId": {"type": "integer", "format": "int32"}, "CNoticeType": {"type": "integer", "format": "int32"}, "CFileName": {"type": "string", "nullable": true}, "CFileUrl": {"type": "string", "nullable": true}, "CCreateDt": {"type": "string", "format": "date-time"}, "CCreator": {"type": "string", "nullable": true}, "CUpdateDt": {"type": "string", "format": "date-time"}, "CUpdator": {"type": "string", "nullable": true}, "CStatus": {"type": "integer", "format": "int32"}, "CApprover": {"type": "string", "nullable": true}, "CApproveDate": {"type": "string", "format": "date-time", "nullable": true}}, "additionalProperties": false}, "TblSpecialNoticeFileResponseBase": {"type": "object", "properties": {"TotalItems": {"type": "integer", "format": "int64"}, "Entries": {"$ref": "#/components/schemas/TblSpecialNoticeFile"}, "Message": {"type": "string", "nullable": true}, "StatusCode": {"$ref": "#/components/schemas/EnumStatusCode"}}, "additionalProperties": false}, "UnlockFormItem": {"type": "object", "properties": {"CBuildCaseID": {"type": "integer", "format": "int32"}, "CFormId": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "UpdateApproveWaiting": {"type": "object", "properties": {"CID": {"type": "integer", "format": "int32"}, "CType": {"type": "integer", "format": "int32"}, "CRemark": {"type": "string", "nullable": true}, "CIsApprove": {"type": "boolean"}}, "additionalProperties": false}, "UpdateHouseRequirementArgs": {"type": "object", "properties": {"CHouseRequirement": {"type": "array", "items": {"$ref": "#/components/schemas/HouseRequirement"}, "nullable": true}}, "additionalProperties": false}, "UpdateHouseReviewArgs": {"type": "object", "properties": {"CHouseReviewID": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "UpdateSignArgs": {"type": "object", "properties": {"CSign": {"type": "string", "nullable": true}, "CFinalDocID": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "UploadFileResponse": {"type": "object", "properties": {"Messages": {"type": "array", "items": {"type": "string"}, "nullable": true}, "Cname": {"type": "string", "nullable": true}}, "additionalProperties": false}, "UploadFileResponseResponseBase": {"type": "object", "properties": {"TotalItems": {"type": "integer", "format": "int64"}, "Entries": {"$ref": "#/components/schemas/UploadFileResponse"}, "Message": {"type": "string", "nullable": true}, "StatusCode": {"$ref": "#/components/schemas/EnumStatusCode"}}, "additionalProperties": false}, "UploadRegularPic": {"type": "object", "properties": {"CBuildCaseID": {"type": "integer", "format": "int32"}, "CFileName": {"type": "string", "nullable": true}, "CFileBlood": {"type": "string", "nullable": true}}, "additionalProperties": false}, "UploadSpecialChangeFile": {"type": "object", "properties": {"SpecialChangeFiles": {"type": "array", "items": {"$ref": "#/components/schemas/SpecialChangeFile"}, "nullable": true}, "CDrawingName": {"type": "string", "nullable": true}, "CChangeDate": {"type": "string", "format": "date-time"}, "CApproveRemark": {"type": "string", "nullable": true}, "CHouseID": {"type": "integer", "format": "int32"}, "CBuildCaseID": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "UserBuildCase": {"type": "object", "properties": {"CBuilCaseId": {"type": "integer", "format": "int32"}, "CBuilCaseName": {"type": "string", "nullable": true}}, "additionalProperties": false}, "UserGetDataArgs": {"type": "object", "properties": {"CUserId": {"type": "string", "nullable": true}}, "additionalProperties": false}, "UserGetDataResponse": {"type": "object", "properties": {"CUserId": {"type": "string", "nullable": true}, "CAccount": {"type": "string", "nullable": true}, "CName": {"type": "string", "nullable": true}, "CStatus": {"type": "integer", "format": "int32"}, "CUserType": {"type": "integer", "format": "int32"}, "CMail": {"type": "string", "nullable": true}, "ListUserGroup": {"type": "array", "items": {"type": "integer", "format": "int32"}, "nullable": true}, "UserBuildCases": {"type": "array", "items": {"$ref": "#/components/schemas/UserBuildCase"}, "nullable": true}}, "additionalProperties": false}, "UserGetDataResponseResponseBase": {"type": "object", "properties": {"TotalItems": {"type": "integer", "format": "int64"}, "Entries": {"$ref": "#/components/schemas/UserGetDataResponse"}, "Message": {"type": "string", "nullable": true}, "StatusCode": {"$ref": "#/components/schemas/EnumStatusCode"}}, "additionalProperties": false}, "UserGetListArgs": {"type": "object", "properties": {"PageIndex": {"type": "integer", "format": "int32"}, "PageSize": {"type": "integer", "format": "int32"}, "CUserName": {"type": "string", "nullable": true}, "CStatus": {"type": "integer", "format": "int32", "nullable": true}}, "additionalProperties": false}, "UserGetListResponse": {"type": "object", "properties": {"CUserId": {"type": "string", "nullable": true}, "CAccount": {"type": "string", "nullable": true}, "CName": {"type": "string", "nullable": true}, "CStatus": {"type": "integer", "format": "int32"}, "CUserGroupName": {"type": "string", "nullable": true}}, "additionalProperties": false}, "UserGetListResponseListResponseBase": {"type": "object", "properties": {"TotalItems": {"type": "integer", "format": "int64"}, "Entries": {"type": "array", "items": {"$ref": "#/components/schemas/UserGetListResponse"}, "nullable": true}, "Message": {"type": "string", "nullable": true}, "StatusCode": {"$ref": "#/components/schemas/EnumStatusCode"}}, "additionalProperties": false}, "UserGetUserLogArgs": {"type": "object", "properties": {"PageIndex": {"type": "integer", "format": "int32"}, "PageSize": {"type": "integer", "format": "int32"}, "DateStart": {"type": "string", "format": "date-time"}, "DateEnd": {"type": "string", "format": "date-time"}, "FunctionId": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "UserGetUserLogResponse": {"type": "object", "properties": {"CId": {"type": "integer", "format": "int32"}, "CAccount": {"type": "string", "nullable": true}, "CName": {"type": "string", "nullable": true}, "DateCreate": {"type": "string", "format": "date-time"}, "FunctionId": {"type": "integer", "format": "int32"}, "FunctionName": {"type": "string", "nullable": true}, "Ip": {"type": "string", "nullable": true}}, "additionalProperties": false}, "UserGetUserLogResponseListResponseBase": {"type": "object", "properties": {"TotalItems": {"type": "integer", "format": "int64"}, "Entries": {"type": "array", "items": {"$ref": "#/components/schemas/UserGetUserLogResponse"}, "nullable": true}, "Message": {"type": "string", "nullable": true}, "StatusCode": {"$ref": "#/components/schemas/EnumStatusCode"}}, "additionalProperties": false}, "UserGroupGetDataArgs": {"type": "object", "properties": {"CId": {"type": "integer", "format": "int32", "nullable": true}}, "additionalProperties": false}, "UserGroupGetDataAuthority": {"type": "object", "properties": {"CId": {"type": "integer", "format": "int32"}, "CName": {"type": "string", "nullable": true}, "IsChecked": {"type": "boolean"}}, "additionalProperties": false}, "UserGroupGetDataFunctionLv1": {"type": "object", "properties": {"CId": {"type": "integer", "format": "int32"}, "CName": {"type": "string", "nullable": true}, "FunctionLv2": {"type": "array", "items": {"$ref": "#/components/schemas/UserGroupGetDataFunctionLv2"}, "nullable": true}}, "additionalProperties": false}, "UserGroupGetDataFunctionLv2": {"type": "object", "properties": {"CId": {"type": "integer", "format": "int32"}, "CName": {"type": "string", "nullable": true}, "Authority": {"type": "array", "items": {"$ref": "#/components/schemas/UserGroupGetDataAuthority"}, "nullable": true}}, "additionalProperties": false}, "UserGroupGetDataResponse": {"type": "object", "properties": {"CId": {"type": "integer", "format": "int32", "nullable": true}, "CName": {"type": "string", "nullable": true}, "FunctionLv1": {"type": "array", "items": {"$ref": "#/components/schemas/UserGroupGetDataFunctionLv1"}, "nullable": true}}, "additionalProperties": false}, "UserGroupGetDataResponseResponseBase": {"type": "object", "properties": {"TotalItems": {"type": "integer", "format": "int64"}, "Entries": {"$ref": "#/components/schemas/UserGroupGetDataResponse"}, "Message": {"type": "string", "nullable": true}, "StatusCode": {"$ref": "#/components/schemas/EnumStatusCode"}}, "additionalProperties": false}, "UserGroupGetListArgs": {"type": "object", "properties": {"PageIndex": {"type": "integer", "format": "int32"}, "PageSize": {"type": "integer", "format": "int32"}, "CName": {"type": "string", "nullable": true}, "CStatus": {"type": "integer", "format": "int32", "nullable": true}}, "additionalProperties": false}, "UserGroupGetListResponse": {"type": "object", "properties": {"CId": {"type": "integer", "format": "int32"}, "CName": {"type": "string", "nullable": true}, "CStatus": {"type": "integer", "format": "int32", "nullable": true}, "CUpdateDt": {"type": "string", "format": "date-time", "nullable": true}}, "additionalProperties": false}, "UserGroupGetListResponseListResponseBase": {"type": "object", "properties": {"TotalItems": {"type": "integer", "format": "int64"}, "Entries": {"type": "array", "items": {"$ref": "#/components/schemas/UserGroupGetListResponse"}, "nullable": true}, "Message": {"type": "string", "nullable": true}, "StatusCode": {"$ref": "#/components/schemas/EnumStatusCode"}}, "additionalProperties": false}, "UserGroupRemoveDataArgs": {"type": "object", "properties": {"CId": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "UserGroupRemoveDataResponse": {"type": "object", "additionalProperties": false}, "UserGroupRemoveDataResponseResponseBase": {"type": "object", "properties": {"TotalItems": {"type": "integer", "format": "int64"}, "Entries": {"$ref": "#/components/schemas/UserGroupRemoveDataResponse"}, "Message": {"type": "string", "nullable": true}, "StatusCode": {"$ref": "#/components/schemas/EnumStatusCode"}}, "additionalProperties": false}, "UserGroupSaveDataArgs": {"type": "object", "properties": {"CId": {"type": "integer", "format": "int32", "nullable": true}, "CName": {"type": "string", "nullable": true}, "FunctionLv1": {"type": "array", "items": {"$ref": "#/components/schemas/UserGroupGetDataFunctionLv1"}, "nullable": true}}, "additionalProperties": false}, "UserGroupSaveDataResponse": {"type": "object", "additionalProperties": false}, "UserGroupSaveDataResponseResponseBase": {"type": "object", "properties": {"TotalItems": {"type": "integer", "format": "int64"}, "Entries": {"$ref": "#/components/schemas/UserGroupSaveDataResponse"}, "Message": {"type": "string", "nullable": true}, "StatusCode": {"$ref": "#/components/schemas/EnumStatusCode"}}, "additionalProperties": false}, "UserLoginRequest": {"type": "object", "properties": {"Account": {"type": "string", "nullable": true}, "Password": {"type": "string", "nullable": true}}, "additionalProperties": false}, "UserRemoveDataArgs": {"type": "object", "properties": {"CUserId": {"type": "string", "nullable": true}}, "additionalProperties": false}, "UserRemoveDataResponse": {"type": "object", "additionalProperties": false}, "UserRemoveDataResponseResponseBase": {"type": "object", "properties": {"TotalItems": {"type": "integer", "format": "int64"}, "Entries": {"$ref": "#/components/schemas/UserRemoveDataResponse"}, "Message": {"type": "string", "nullable": true}, "StatusCode": {"$ref": "#/components/schemas/EnumStatusCode"}}, "additionalProperties": false}, "UserSaveDataArgs": {"type": "object", "properties": {"CUserId": {"type": "string", "nullable": true}, "CAccount": {"type": "string", "nullable": true}, "CName": {"type": "string", "nullable": true}, "CStatus": {"type": "integer", "format": "int32"}, "CUserType": {"type": "integer", "format": "int32"}, "CMail": {"type": "string", "nullable": true}, "ListUserGroup": {"type": "array", "items": {"type": "integer", "format": "int32"}, "nullable": true}, "UserBuildCases": {"type": "array", "items": {"$ref": "#/components/schemas/UserBuildCase"}, "nullable": true}, "Password": {"type": "string", "nullable": true}, "CfmPassword": {"type": "string", "nullable": true}, "OldPassword": {"type": "string", "nullable": true}}, "additionalProperties": false}, "UserSaveDataResponse": {"type": "object", "additionalProperties": false}, "UserSaveDataResponseResponseBase": {"type": "object", "properties": {"TotalItems": {"type": "integer", "format": "int64"}, "Entries": {"$ref": "#/components/schemas/UserSaveDataResponse"}, "Message": {"type": "string", "nullable": true}, "StatusCode": {"$ref": "#/components/schemas/EnumStatusCode"}}, "additionalProperties": false}}, "securitySchemes": {"Bearer": {"type": "<PERSON><PERSON><PERSON><PERSON>", "description": "JWT Authorization header using the <PERSON><PERSON> scheme. \r\n\r\n Enter 'Bearer' [space] and then your token in the text input below.\r\n\r\nExample: \"Bearer 12345abcdef\"", "name": "Authorization", "in": "header"}, "reCAPTCHA": {"type": "<PERSON><PERSON><PERSON><PERSON>", "description": "Google ReCaptcha", "name": "reCAPTCHA", "in": "header"}}}, "security": [{"Bearer": []}, {"reCAPTCHA": []}]}