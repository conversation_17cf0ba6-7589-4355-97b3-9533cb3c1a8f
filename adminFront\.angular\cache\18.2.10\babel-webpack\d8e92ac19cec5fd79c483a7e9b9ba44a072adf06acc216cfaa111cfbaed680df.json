{"ast": null, "code": "import _classCallCheck from \"@babel/runtime/helpers/esm/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/esm/createClass\";\nimport _assertThisInitialized from \"@babel/runtime/helpers/esm/assertThisInitialized\";\nimport _inherits from \"@babel/runtime/helpers/esm/inherits\";\nimport _createSuper from \"@babel/runtime/helpers/esm/createSuper\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport { Parser } from \"../Parser.js\";\nimport { mapValue, parseNDigits } from \"../utils.js\";\nimport setUTCDay from \"../../../_lib/setUTCDay/index.js\"; // Local day of week\nexport var LocalDayParser = /*#__PURE__*/function (_Parser) {\n  _inherits(LocalDayParser, _Parser);\n  var _super = _createSuper(LocalDayParser);\n  function LocalDayParser() {\n    var _this;\n    _classCallCheck(this, LocalDayParser);\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    _this = _super.call.apply(_super, [this].concat(args));\n    _defineProperty(_assertThisInitialized(_this), \"priority\", 90);\n    _defineProperty(_assertThisInitialized(_this), \"incompatibleTokens\", ['y', 'R', 'u', 'q', 'Q', 'M', 'L', 'I', 'd', 'D', 'E', 'i', 'c', 't', 'T']);\n    return _this;\n  }\n  _createClass(LocalDayParser, [{\n    key: \"parse\",\n    value: function parse(dateString, token, match, options) {\n      var valueCallback = function valueCallback(value) {\n        var wholeWeekDays = Math.floor((value - 1) / 7) * 7;\n        return (value + options.weekStartsOn + 6) % 7 + wholeWeekDays;\n      };\n      switch (token) {\n        // 3\n        case 'e':\n        case 'ee':\n          // 03\n          return mapValue(parseNDigits(token.length, dateString), valueCallback);\n        // 3rd\n        case 'eo':\n          return mapValue(match.ordinalNumber(dateString, {\n            unit: 'day'\n          }), valueCallback);\n        // Tue\n        case 'eee':\n          return match.day(dateString, {\n            width: 'abbreviated',\n            context: 'formatting'\n          }) || match.day(dateString, {\n            width: 'short',\n            context: 'formatting'\n          }) || match.day(dateString, {\n            width: 'narrow',\n            context: 'formatting'\n          });\n        // T\n        case 'eeeee':\n          return match.day(dateString, {\n            width: 'narrow',\n            context: 'formatting'\n          });\n        // Tu\n        case 'eeeeee':\n          return match.day(dateString, {\n            width: 'short',\n            context: 'formatting'\n          }) || match.day(dateString, {\n            width: 'narrow',\n            context: 'formatting'\n          });\n        // Tuesday\n        case 'eeee':\n        default:\n          return match.day(dateString, {\n            width: 'wide',\n            context: 'formatting'\n          }) || match.day(dateString, {\n            width: 'abbreviated',\n            context: 'formatting'\n          }) || match.day(dateString, {\n            width: 'short',\n            context: 'formatting'\n          }) || match.day(dateString, {\n            width: 'narrow',\n            context: 'formatting'\n          });\n      }\n    }\n  }, {\n    key: \"validate\",\n    value: function validate(_date, value) {\n      return value >= 0 && value <= 6;\n    }\n  }, {\n    key: \"set\",\n    value: function set(date, _flags, value, options) {\n      date = setUTCDay(date, value, options);\n      date.setUTCHours(0, 0, 0, 0);\n      return date;\n    }\n  }]);\n  return LocalDayParser;\n}(Parser);", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}