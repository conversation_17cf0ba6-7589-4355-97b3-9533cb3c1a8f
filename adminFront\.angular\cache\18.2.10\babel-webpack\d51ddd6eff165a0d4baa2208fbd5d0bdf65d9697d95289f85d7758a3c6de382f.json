{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { tap } from 'rxjs';\nimport { SharedModule } from '../../components/shared.module';\nimport { decodeJwtPayload } from '@nebular/auth';\nimport { LocalStorageService } from 'src/app/shared/services/local-storage.service';\nimport { STORAGE_KEY } from 'src/app/shared/constant/constant';\nimport { EEvent } from 'src/app/shared/services/event.service';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/services/api/services\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"src/app/shared/services/utility.service\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"src/app/shared/services/message.service\";\nimport * as i6 from \"src/app/shared/helper/validationHelper\";\nimport * as i7 from \"src/app/shared/services/event.service\";\nimport * as i8 from \"@angular/forms\";\nimport * as i9 from \"@nebular/theme\";\nimport * as i10 from \"../../../@theme/pipes/mapping.pipe\";\nfunction DetailApprovalWaitingComponent_div_2_li_29_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"li\")(1, \"span\", 20);\n    i0.ɵɵlistener(\"click\", function DetailApprovalWaitingComponent_div_2_li_29_Template_span_click_1_listener() {\n      const item_r3 = i0.ɵɵrestoreView(_r2).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r3.downloadFile(item_r3.CFile, item_r3.CFileName));\n    });\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const item_r3 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", item_r3.CFile ? \"text-blue-400 underline cursor-pointer \" : \"\");\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", item_r3.CFileName, \" \");\n  }\n}\nfunction DetailApprovalWaitingComponent_div_2_div_50_tr_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\", 27)(1, \"th\", 28);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"td\", 29);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"td\", 29);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"td\", 29);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const record_r5 = ctx.$implicit;\n    const ctx_r3 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(3, 6, record_r5.CRecordDate ? record_r5.CRecordDate : ctx_r3.approvalWaiting.CCreateDT, \"yyyy/MM/dd HH:mm:ss\"), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate3(\" \", record_r5.CAction === 1 ? \"\\u9001\\u51FA\\u5BE9\\u6838\" : \"\", \" \", record_r5.CAction === 2 ? \"\\u5BE9\\u6838\\u901A\\u904E\" : \"\", \" \", record_r5.CAction === 3 ? \"\\u5BE9\\u6838\\u99C1\\u56DE\" : \"\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", record_r5.CCreator, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", record_r5.CRemark, \" \");\n  }\n}\nfunction DetailApprovalWaitingComponent_div_2_div_50_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 21)(1, \"table\", 22)(2, \"thead\", 23)(3, \"tr\", 24)(4, \"th\", 25);\n    i0.ɵɵtext(5, \" \\u65E5\\u671F \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"th\", 25);\n    i0.ɵɵtext(7, \" \\u52D5\\u4F5C \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"th\", 25);\n    i0.ɵɵtext(9, \" \\u4F7F\\u7528\\u8005 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"th\", 25);\n    i0.ɵɵtext(11, \" \\u5099\\u8A3B \");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(12, \"tbody\");\n    i0.ɵɵtemplate(13, DetailApprovalWaitingComponent_div_2_div_50_tr_13_Template, 10, 9, \"tr\", 26);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(13);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.approvalWaiting.CApproveRecord);\n  }\n}\nfunction DetailApprovalWaitingComponent_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 1)(1, \"div\", 2)(2, \"span\", 3);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 4)(5, \"div\", 5)(6, \"div\", 6)(7, \"span\", 7);\n    i0.ɵɵtext(8, \" \\u5EFA\\u6848 \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"span\", 8);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"div\", 9)(12, \"div\", 6)(13, \"span\", 7);\n    i0.ɵɵtext(14, \" \\u985E\\u5225 \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(15, \"span\", 8);\n    i0.ɵɵtext(16);\n    i0.ɵɵpipe(17, \"getTypeApprovalWaiting\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(18, \"div\", 9)(19, \"div\", 6)(20, \"span\", 7);\n    i0.ɵɵtext(21, \" \\u540D\\u7A31 \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(22, \"span\", 8);\n    i0.ɵɵtext(23);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(24, \"div\", 9)(25, \"div\", 6)(26, \"span\", 7);\n    i0.ɵɵtext(27, \" \\u6A94\\u6848 \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(28, \"ul\", 8);\n    i0.ɵɵtemplate(29, DetailApprovalWaitingComponent_div_2_li_29_Template, 3, 2, \"li\", 10);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(30, \"div\", 9)(31, \"div\", 6)(32, \"span\", 7);\n    i0.ɵɵtext(33, \" \\u5BE9\\u6838\\u8AAA\\u660E \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(34, \"span\", 8);\n    i0.ɵɵtext(35);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(36, \"div\", 11)(37, \"div\", 12)(38, \"div\", 6)(39, \"span\", 7);\n    i0.ɵɵtext(40, \" \\u5099\\u8A3B \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(41, \"div\", 13)(42, \"textarea\", 14);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function DetailApprovalWaitingComponent_div_2_Template_textarea_ngModelChange_42_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r3 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r3.remark, $event) || (ctx_r3.remark = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(43, \"div\", 15)(44, \"button\", 16);\n    i0.ɵɵlistener(\"click\", function DetailApprovalWaitingComponent_div_2_Template_button_click_44_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.goBack());\n    });\n    i0.ɵɵtext(45, \"\\u53D6\\u6D88\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(46, \"button\", 17);\n    i0.ɵɵlistener(\"click\", function DetailApprovalWaitingComponent_div_2_Template_button_click_46_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.handleAction(false));\n    });\n    i0.ɵɵtext(47, \"\\u99C1\\u56DE\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(48, \"button\", 18);\n    i0.ɵɵlistener(\"click\", function DetailApprovalWaitingComponent_div_2_Template_button_click_48_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.handleAction(true));\n    });\n    i0.ɵɵtext(49, \"\\u540C\\u610F\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(50, DetailApprovalWaitingComponent_div_2_div_50_Template, 14, 1, \"div\", 19);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.approvalWaiting.CName, \" \");\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.approvalWaiting.CBuildcaseName, \" \");\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(17, 11, ctx_r3.approvalWaiting.CType), \" \");\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.approvalWaiting.CName, \" \");\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.approvalWaiting.CFileApproves);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.approvalWaiting.CApprovalRemark, \" \");\n    i0.ɵɵadvance(7);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r3.remark);\n    i0.ɵɵproperty(\"rows\", 4);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"disabled\", ctx_r3.approvalWaiting.CIsApprove !== null && ctx_r3.approvalWaiting.CIsApprove);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", ctx_r3.approvalWaiting.CIsApprove !== null && ctx_r3.approvalWaiting.CIsApprove);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", !!ctx_r3.approvalWaiting);\n  }\n}\nexport let DetailApprovalWaitingComponent = /*#__PURE__*/(() => {\n  class DetailApprovalWaitingComponent {\n    constructor(_specialChangeService, _activatedRoute, _ultilityService, _location, message, _validationHelper, _eventService) {\n      this._specialChangeService = _specialChangeService;\n      this._activatedRoute = _activatedRoute;\n      this._ultilityService = _ultilityService;\n      this._location = _location;\n      this.message = message;\n      this._validationHelper = _validationHelper;\n      this._eventService = _eventService;\n      this.CType = 1;\n      this.remark = \"\";\n      this.decodeJWT = decodeJwtPayload(LocalStorageService.GetLocalStorage(STORAGE_KEY.TOKEN));\n      this.CID = parseInt(this._activatedRoute.snapshot.paramMap.get(\"id\"));\n      this.buildCaseID = parseInt(this._activatedRoute.snapshot.paramMap.get(\"buildCaseId\"));\n      this._activatedRoute.queryParams.pipe(tap(p => {\n        this.CType = p[\"type\"];\n      })).subscribe();\n    }\n    ngOnInit() {\n      this.getApprovalWaitingById();\n    }\n    getApprovalWaitingById() {\n      this._specialChangeService.apiSpecialChangeGetApproveWaitingByIdPost$Json({\n        body: {\n          CID: this.CID,\n          CType: this.CType.toString()\n        }\n      }).pipe(tap(res => {\n        if (res.StatusCode === 0) {\n          this.approvalWaiting = res.Entries;\n        }\n      })).subscribe();\n    }\n    downloadFile(CFile, CFileName) {\n      if (CFile && CFileName) {\n        this._ultilityService.downloadFileFullUrl(CFile, CFileName);\n      }\n    }\n    handleAction(isApprove) {\n      if (!isApprove) {\n        this.validation();\n        if (this._validationHelper.errorMessages.length > 0) {\n          this.message.showErrorMSGs(this._validationHelper.errorMessages);\n          return;\n        }\n      }\n      this._specialChangeService.apiSpecialChangeUpdateApproveWaitingPost$Json({\n        body: {\n          CID: this.CID,\n          CType: this.CType,\n          CIsApprove: isApprove,\n          CRemark: this.remark\n        }\n      }).pipe(tap(res => {\n        if (res.StatusCode === 0) {\n          this.message.showSucessMSG(\"執行成功\");\n          this.getApprovalWaitingById();\n          if (this.approvalWaiting.CApproveRecord?.length == 0) {\n            this.approvalWaiting.CApproveRecord?.push({\n              CCreator: this.decodeJWT.userName,\n              CRecordDate: new Date().toISOString(),\n              CRemark: this.remark\n            });\n          }\n          this.remark = \"\";\n        }\n      })).subscribe();\n    }\n    goBack() {\n      this._eventService.push({\n        action: \"GET_BUILDCASE\" /* EEvent.GET_BUILDCASE */,\n        payload: this.buildCaseID\n      });\n      this._location.back();\n    }\n    validation() {\n      this._validationHelper.clear();\n      this._validationHelper.required(\"[備註]\", this.remark);\n    }\n    static {\n      this.ɵfac = function DetailApprovalWaitingComponent_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || DetailApprovalWaitingComponent)(i0.ɵɵdirectiveInject(i1.SpecialChangeService), i0.ɵɵdirectiveInject(i2.ActivatedRoute), i0.ɵɵdirectiveInject(i3.UtilityService), i0.ɵɵdirectiveInject(i4.Location), i0.ɵɵdirectiveInject(i5.MessageService), i0.ɵɵdirectiveInject(i6.ValidationHelper), i0.ɵɵdirectiveInject(i7.EventService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: DetailApprovalWaitingComponent,\n        selectors: [[\"app-detail-approval-waiting\"]],\n        standalone: true,\n        features: [i0.ɵɵStandaloneFeature],\n        decls: 3,\n        vars: 1,\n        consts: [[\"class\", \"flex flex-col justify-items-center items-center m-auto w-[50%]\", 4, \"ngIf\"], [1, \"flex\", \"flex-col\", \"justify-items-center\", \"items-center\", \"m-auto\", \"w-[50%]\"], [1, \"border-b-2\", \"border-b-black\", \"w-full\"], [1, \"text-xl\", \"font-bold\"], [1, \"px-3\", \"py-4\"], [1, \"flex\", \"items-center\"], [1, \"w-[100px]\"], [1, \"font-bold\"], [1, \"w-[80%]\", \"break-words\"], [1, \"flex\", \"items-center\", \"mt-3\"], [4, \"ngFor\", \"ngForOf\"], [1, \"mt-2\", \"w-full\"], [1, \"flex\", \"px-3\", \"py-4\", \"w-full\"], [1, \"w-full\"], [\"nbInput\", \"\", 1, \"resize-none\", \"!max-w-full\", \"w-full\", 3, \"ngModelChange\", \"ngModel\", \"rows\"], [1, \"mt-3\", \"flex\", \"items-center\"], [\"nbButton\", \"\", 1, \"btn\", \"border-black\", \"border\", \"mr-2\", 3, \"click\"], [\"nbButton\", \"\", \"status\", \"danger\", 1, \"btn\", \"mr-2\", 3, \"click\", \"disabled\"], [\"nbButton\", \"\", \"status\", \"primary\", 1, \"btn\", \"mr-2\", 3, \"click\", \"disabled\"], [\"class\", \"table-responsive relative overflow-x-auto mt-3\", 4, \"ngIf\"], [1, \"w-[80%]\", \"break-words\", 3, \"click\", \"ngClass\"], [1, \"table-responsive\", \"relative\", \"overflow-x-auto\", \"mt-3\"], [1, \"table\", \"table-bordered\", \"w-full\", \"text-sm\", \"text-left\", \"rtl:text-right\", \"text-gray-500\", \"dark:text-gray-400\"], [1, \"text-xs\", \"text-gray-700\", \"uppercase\", \"bg-gray-50\"], [2, \"background-color\", \"#27ae60\", \"color\", \"white\"], [\"scope\", \"col\", 1, \"px-6\", \"py-3\"], [\"class\", \"bg-white text-black\", 4, \"ngFor\", \"ngForOf\"], [1, \"bg-white\", \"text-black\"], [\"scope\", \"row\", 1, \"px-6\", \"py-4\", \"font-medium\", \"whitespace-nowrap\"], [1, \"px-6\", \"py-4\"]],\n        template: function DetailApprovalWaitingComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"nb-card\")(1, \"nb-card-body\");\n            i0.ɵɵtemplate(2, DetailApprovalWaitingComponent_div_2_Template, 51, 13, \"div\", 0);\n            i0.ɵɵelementEnd()();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"ngIf\", !!ctx.approvalWaiting);\n          }\n        },\n        dependencies: [CommonModule, i4.NgClass, i4.NgForOf, i4.NgIf, i4.DatePipe, SharedModule, i8.DefaultValueAccessor, i8.NgControlStatus, i8.NgModel, i9.NbCardComponent, i9.NbCardBodyComponent, i9.NbInputDirective, i9.NbButtonComponent, i10.ApprovalWaitingPipe]\n      });\n    }\n  }\n  return DetailApprovalWaitingComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}