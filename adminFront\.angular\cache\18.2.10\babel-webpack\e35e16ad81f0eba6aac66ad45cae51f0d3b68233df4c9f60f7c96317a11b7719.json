{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nfunction ImageModalComponent_div_0_span_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 17);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate2(\" (\", ctx_r1.currentIndex + 1, \" / \", ctx_r1.images.length, \") \");\n  }\n}\nfunction ImageModalComponent_div_0_img_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 18);\n  }\n  if (rf & 2) {\n    let tmp_2_0;\n    let tmp_3_0;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"src\", \"data:image/jpeg;base64,\" + ((tmp_2_0 = ctx_r1.getCurrentImage()) == null ? null : tmp_2_0.base64), i0.ɵɵsanitizeUrl)(\"alt\", (tmp_3_0 = ctx_r1.getCurrentImage()) == null ? null : tmp_3_0.name);\n  }\n}\nfunction ImageModalComponent_div_0_button_13_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 19);\n    i0.ɵɵlistener(\"click\", function ImageModalComponent_div_0_button_13_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.prevImage());\n    });\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(1, \"svg\", 8);\n    i0.ɵɵelement(2, \"path\", 20);\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ImageModalComponent_div_0_button_14_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 21);\n    i0.ɵɵlistener(\"click\", function ImageModalComponent_div_0_button_14_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.nextImage());\n    });\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(1, \"svg\", 8);\n    i0.ɵɵelement(2, \"path\", 22);\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ImageModalComponent_div_0_div_15_button_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 26);\n    i0.ɵɵlistener(\"click\", function ImageModalComponent_div_0_div_15_button_2_Template_button_click_0_listener() {\n      const i_r6 = i0.ɵɵrestoreView(_r5).index;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.setCurrentImage(i_r6));\n    });\n    i0.ɵɵelement(1, \"img\", 27);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const image_r7 = ctx.$implicit;\n    const i_r6 = ctx.index;\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵclassProp(\"border-blue-500\", i_r6 === ctx_r1.currentIndex)(\"border-gray-300\", i_r6 !== ctx_r1.currentIndex)(\"ring-2\", i_r6 === ctx_r1.currentIndex)(\"ring-blue-200\", i_r6 === ctx_r1.currentIndex);\n    i0.ɵɵproperty(\"title\", image_r7.name || \"\\u5716\\u7247 \" + (i_r6 + 1));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"src\", \"data:image/jpeg;base64,\" + image_r7.base64, i0.ɵɵsanitizeUrl)(\"alt\", image_r7.name);\n  }\n}\nfunction ImageModalComponent_div_0_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 23)(1, \"div\", 24);\n    i0.ɵɵtemplate(2, ImageModalComponent_div_0_div_15_button_2_Template, 2, 11, \"button\", 25);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.images);\n  }\n}\nfunction ImageModalComponent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 1);\n    i0.ɵɵlistener(\"click\", function ImageModalComponent_div_0_Template_div_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onBackdropClick($event));\n    });\n    i0.ɵɵelementStart(1, \"div\", 2);\n    i0.ɵɵlistener(\"click\", function ImageModalComponent_div_0_Template_div_click_1_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      return i0.ɵɵresetView($event.stopPropagation());\n    });\n    i0.ɵɵelementStart(2, \"div\", 3)(3, \"div\", 4)(4, \"h3\", 5);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(6, ImageModalComponent_div_0_span_6_Template, 2, 2, \"span\", 6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"button\", 7);\n    i0.ɵɵlistener(\"click\", function ImageModalComponent_div_0_Template_button_click_7_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.closeModal());\n    });\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(8, \"svg\", 8);\n    i0.ɵɵelement(9, \"path\", 9);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(10, \"div\", 10)(11, \"div\", 11);\n    i0.ɵɵtemplate(12, ImageModalComponent_div_0_img_12_Template, 1, 2, \"img\", 12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(13, ImageModalComponent_div_0_button_13_Template, 3, 0, \"button\", 13)(14, ImageModalComponent_div_0_button_14_Template, 3, 0, \"button\", 14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(15, ImageModalComponent_div_0_div_15_Template, 3, 1, \"div\", 15);\n    i0.ɵɵelementStart(16, \"div\", 16);\n    i0.ɵɵtext(17, \" \\u4F7F\\u7528\\u9375\\u76E4\\u65B9\\u5411\\u9375\\u5207\\u63DB\\u5716\\u7247\\uFF0C\\u6309 ESC \\u95DC\\u9589 \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    let tmp_1_0;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", ((tmp_1_0 = ctx_r1.getCurrentImage()) == null ? null : tmp_1_0.name) || \"\\u5716\\u7247\\u9810\\u89BD\", \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.images.length > 1);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.getCurrentImage());\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.images.length > 1);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.images.length > 1);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.images.length > 1);\n  }\n}\nexport class ImageModalComponent {\n  constructor(cdr, elementRef) {\n    this.cdr = cdr;\n    this.elementRef = elementRef;\n    this.images = [];\n    this.currentIndex = 0;\n    this.isVisible = false;\n    this.close = new EventEmitter();\n    this.indexChange = new EventEmitter();\n  }\n  ngOnInit() {\n    if (this.currentIndex >= this.images.length) {\n      this.currentIndex = 0;\n    }\n    // 監聽 isVisible 變化\n    this.handleModalVisibility();\n  }\n  ngOnDestroy() {\n    // 確保清理\n    if (this.isVisible) {\n      document.body.classList.remove('modal-open');\n    }\n  }\n  ngOnChanges() {\n    this.handleModalVisibility();\n  }\n  handleModalVisibility() {\n    if (this.isVisible) {\n      document.body.classList.add('modal-open');\n      // 確保模態框在最頂層\n      this.elementRef.nativeElement.style.zIndex = '9999';\n    } else {\n      document.body.classList.remove('modal-open');\n    }\n  }\n  handleKeyDown(event) {\n    if (!this.isVisible) return;\n    switch (event.key) {\n      case 'Escape':\n        this.closeModal();\n        break;\n      case 'ArrowLeft':\n        this.prevImage();\n        break;\n      case 'ArrowRight':\n        this.nextImage();\n        break;\n    }\n  }\n  getCurrentImage() {\n    if (this.images && this.images.length > 0 && this.currentIndex < this.images.length) {\n      return this.images[this.currentIndex];\n    }\n    return null;\n  }\n  nextImage() {\n    if (this.images && this.images.length > 1) {\n      this.currentIndex = (this.currentIndex + 1) % this.images.length;\n      this.indexChange.emit(this.currentIndex);\n    }\n  }\n  prevImage() {\n    if (this.images && this.images.length > 1) {\n      this.currentIndex = this.currentIndex === 0 ? this.images.length - 1 : this.currentIndex - 1;\n      this.indexChange.emit(this.currentIndex);\n    }\n  }\n  setCurrentImage(index) {\n    if (index >= 0 && index < this.images.length) {\n      this.currentIndex = index;\n      this.indexChange.emit(this.currentIndex);\n    }\n  }\n  closeModal() {\n    document.body.classList.remove('modal-open');\n    this.close.emit();\n  }\n  onBackdropClick(event) {\n    if (event.target === event.currentTarget) {\n      this.closeModal();\n    }\n  }\n  static {\n    this.ɵfac = function ImageModalComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || ImageModalComponent)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i0.ElementRef));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ImageModalComponent,\n      selectors: [[\"app-image-modal\"]],\n      hostVars: 2,\n      hostBindings: function ImageModalComponent_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"keydown\", function ImageModalComponent_keydown_HostBindingHandler($event) {\n            return ctx.handleKeyDown($event);\n          }, false, i0.ɵɵresolveDocument);\n        }\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"modal-visible\", ctx.isVisible);\n        }\n      },\n      inputs: {\n        images: \"images\",\n        currentIndex: \"currentIndex\",\n        isVisible: \"isVisible\"\n      },\n      outputs: {\n        close: \"close\",\n        indexChange: \"indexChange\"\n      },\n      standalone: true,\n      features: [i0.ɵɵNgOnChangesFeature, i0.ɵɵStandaloneFeature],\n      decls: 1,\n      vars: 1,\n      consts: [[\"class\", \"fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50 p-4\", 3, \"click\", 4, \"ngIf\"], [1, \"fixed\", \"inset-0\", \"bg-black\", \"bg-opacity-75\", \"flex\", \"items-center\", \"justify-center\", \"z-50\", \"p-4\", 3, \"click\"], [1, \"relative\", \"modal-container\", \"max-h-full\", \"w-full\", \"bg-white\", \"rounded-lg\", \"shadow-2xl\", \"overflow-hidden\", 3, \"click\"], [1, \"flex\", \"items-center\", \"justify-between\", \"p-4\", \"bg-gray-50\", \"border-b\", \"border-gray-200\"], [1, \"flex\", \"items-center\", \"space-x-3\"], [1, \"text-lg\", \"font-semibold\", \"text-gray-800\"], [\"class\", \"text-sm text-gray-600\", 4, \"ngIf\"], [\"title\", \"\\u95DC\\u9589 (ESC)\", 1, \"text-gray-400\", \"hover:text-gray-600\", \"transition-colors\", 3, \"click\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-6\", \"h-6\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M6 18L18 6M6 6l12 12\"], [1, \"relative\", \"bg-gray-100\"], [1, \"flex\", \"items-center\", \"justify-center\", \"modal-image\", \"modal-max-height\"], [\"class\", \"max-w-full max-h-full object-contain\", 3, \"src\", \"alt\", 4, \"ngIf\"], [\"class\", \"absolute left-4 top-1/2 transform -translate-y-1/2 bg-white bg-opacity-90 hover:bg-opacity-100 text-gray-800 rounded-full modal-button flex items-center justify-center shadow-lg hover:shadow-xl transition-all\", \"title\", \"\\u4E0A\\u4E00\\u5F35 (\\u2190)\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"absolute right-4 top-1/2 transform -translate-y-1/2 bg-white bg-opacity-90 hover:bg-opacity-100 text-gray-800 rounded-full modal-button flex items-center justify-center shadow-lg hover:shadow-xl transition-all\", \"title\", \"\\u4E0B\\u4E00\\u5F35 (\\u2192)\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"p-4 bg-gray-50 border-t border-gray-200\", 4, \"ngIf\"], [1, \"px-4\", \"py-2\", \"bg-gray-100\", \"border-t\", \"border-gray-200\", \"text-xs\", \"text-gray-500\", \"text-center\"], [1, \"text-sm\", \"text-gray-600\"], [1, \"max-w-full\", \"max-h-full\", \"object-contain\", 3, \"src\", \"alt\"], [\"title\", \"\\u4E0A\\u4E00\\u5F35 (\\u2190)\", 1, \"absolute\", \"left-4\", \"top-1/2\", \"transform\", \"-translate-y-1/2\", \"bg-white\", \"bg-opacity-90\", \"hover:bg-opacity-100\", \"text-gray-800\", \"rounded-full\", \"modal-button\", \"flex\", \"items-center\", \"justify-center\", \"shadow-lg\", \"hover:shadow-xl\", \"transition-all\", 3, \"click\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M15 19l-7-7 7-7\"], [\"title\", \"\\u4E0B\\u4E00\\u5F35 (\\u2192)\", 1, \"absolute\", \"right-4\", \"top-1/2\", \"transform\", \"-translate-y-1/2\", \"bg-white\", \"bg-opacity-90\", \"hover:bg-opacity-100\", \"text-gray-800\", \"rounded-full\", \"modal-button\", \"flex\", \"items-center\", \"justify-center\", \"shadow-lg\", \"hover:shadow-xl\", \"transition-all\", 3, \"click\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M9 5l7 7-7 7\"], [1, \"p-4\", \"bg-gray-50\", \"border-t\", \"border-gray-200\"], [1, \"flex\", \"gap-2\", \"overflow-x-auto\", \"pb-2\"], [\"class\", \"flex-shrink-0 w-16 h-16 border-2 rounded-lg overflow-hidden hover:border-blue-400 transition-all duration-200 cursor-pointer\", 3, \"border-blue-500\", \"border-gray-300\", \"ring-2\", \"ring-blue-200\", \"title\", \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"flex-shrink-0\", \"w-16\", \"h-16\", \"border-2\", \"rounded-lg\", \"overflow-hidden\", \"hover:border-blue-400\", \"transition-all\", \"duration-200\", \"cursor-pointer\", 3, \"click\", \"title\"], [1, \"w-full\", \"h-full\", \"object-cover\", 3, \"src\", \"alt\"]],\n      template: function ImageModalComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, ImageModalComponent_div_0_Template, 18, 6, \"div\", 0);\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngIf\", ctx.isVisible);\n        }\n      },\n      dependencies: [CommonModule, i1.NgForOf, i1.NgIf],\n      styles: [\"[_nghost-%COMP%] {\\n  position: fixed;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  z-index: 9999 !important;\\n  pointer-events: none;\\n}\\n.modal-visible[_nghost-%COMP%] {\\n  pointer-events: auto;\\n}\\n\\n.fixed.inset-0[_ngcontent-%COMP%] {\\n  position: fixed !important;\\n  top: 0 !important;\\n  left: 0 !important;\\n  right: 0 !important;\\n  bottom: 0 !important;\\n  width: 100vw !important;\\n  height: 100vh !important;\\n  z-index: 9999 !important;\\n  background-color: rgba(0, 0, 0, 0.75) !important;\\n  animation: _ngcontent-%COMP%_fadeIn 0.2s ease-out;\\n  transform: none !important;\\n  margin: 0 !important;\\n  padding: 1rem !important;\\n  box-sizing: border-box !important;\\n}\\n\\n.modal-container[_ngcontent-%COMP%] {\\n  position: relative !important;\\n  z-index: 10000 !important;\\n  max-width: 95vw !important;\\n  max-height: 95vh !important;\\n  margin: auto !important;\\n  min-width: 300px !important;\\n  min-height: 200px !important;\\n}\\n\\n.modal-image[_ngcontent-%COMP%] {\\n  min-height: 400px;\\n}\\n\\n.modal-max-height[_ngcontent-%COMP%] {\\n  max-height: 70vh !important;\\n}\\n\\n.modal-button[_ngcontent-%COMP%] {\\n  width: 3.5rem !important;\\n  height: 3.5rem !important;\\n  z-index: 10001 !important;\\n}\\n\\n.overflow-x-auto[_ngcontent-%COMP%] {\\n  scrollbar-width: thin;\\n  scrollbar-color: rgba(156, 163, 175, 0.5) transparent;\\n}\\n.overflow-x-auto[_ngcontent-%COMP%]::-webkit-scrollbar {\\n  height: 6px;\\n}\\n.overflow-x-auto[_ngcontent-%COMP%]::-webkit-scrollbar-track {\\n  background: transparent;\\n}\\n.overflow-x-auto[_ngcontent-%COMP%]::-webkit-scrollbar-thumb {\\n  background-color: rgba(156, 163, 175, 0.5);\\n  border-radius: 3px;\\n}\\n.overflow-x-auto[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover {\\n  background-color: rgba(156, 163, 175, 0.7);\\n}\\n\\n[_ngcontent-%COMP%]:global(body.modal-open) {\\n  overflow: hidden !important;\\n  position: fixed !important;\\n  width: 100% !important;\\n  height: 100% !important;\\n}\\n\\n@keyframes _ngcontent-%COMP%_fadeIn {\\n  from {\\n    opacity: 0;\\n  }\\n  to {\\n    opacity: 1;\\n  }\\n}\\n@media (max-width: 768px) {\\n  .modal-container[_ngcontent-%COMP%] {\\n    max-width: calc(100vw - 2rem);\\n  }\\n  .modal-image[_ngcontent-%COMP%] {\\n    min-height: 300px;\\n  }\\n  .modal-max-height[_ngcontent-%COMP%] {\\n    max-height: 60vh;\\n  }\\n  .modal-button[_ngcontent-%COMP%] {\\n    width: 3rem;\\n    height: 3rem;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;base64,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 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["EventEmitter", "CommonModule", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate2", "ctx_r1", "currentIndex", "images", "length", "ɵɵelement", "ɵɵproperty", "tmp_2_0", "getCurrentImage", "base64", "ɵɵsanitizeUrl", "tmp_3_0", "name", "ɵɵlistener", "ImageModalComponent_div_0_button_13_Template_button_click_0_listener", "ɵɵrestoreView", "_r3", "ɵɵnextContext", "ɵɵresetView", "prevImage", "ImageModalComponent_div_0_button_14_Template_button_click_0_listener", "_r4", "nextImage", "ImageModalComponent_div_0_div_15_button_2_Template_button_click_0_listener", "i_r6", "_r5", "index", "setCurrentImage", "ɵɵclassProp", "image_r7", "ɵɵtemplate", "ImageModalComponent_div_0_div_15_button_2_Template", "ImageModalComponent_div_0_Template_div_click_0_listener", "$event", "_r1", "onBackdropClick", "ImageModalComponent_div_0_Template_div_click_1_listener", "stopPropagation", "ImageModalComponent_div_0_span_6_Template", "ImageModalComponent_div_0_Template_button_click_7_listener", "closeModal", "ImageModalComponent_div_0_img_12_Template", "ImageModalComponent_div_0_button_13_Template", "ImageModalComponent_div_0_button_14_Template", "ImageModalComponent_div_0_div_15_Template", "ɵɵtextInterpolate1", "tmp_1_0", "ImageModalComponent", "constructor", "cdr", "elementRef", "isVisible", "close", "indexChange", "ngOnInit", "handleModalVisibility", "ngOnDestroy", "document", "body", "classList", "remove", "ngOnChanges", "add", "nativeElement", "style", "zIndex", "handleKeyDown", "event", "key", "emit", "target", "currentTarget", "ɵɵdirectiveInject", "ChangeDetectorRef", "ElementRef", "selectors", "hostVars", "hostBindings", "ImageModalComponent_HostBindings", "rf", "ctx", "ImageModalComponent_keydown_HostBindingHandler", "ɵɵresolveDocument", "ImageModalComponent_div_0_Template", "i1", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "styles"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\components\\image-modal\\image-modal.component.ts", "C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\components\\image-modal\\image-modal.component.html"], "sourcesContent": ["import { Component, Input, Output, EventEmitter, OnInit, OnDestroy, HostListener, ChangeDetectorRef, ElementRef } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { ImageData } from '../image-carousel/image-carousel.component';\r\n\r\n@Component({\r\n    selector: 'app-image-modal',\r\n    templateUrl: './image-modal.component.html',\r\n    styleUrls: ['./image-modal.component.scss'],\r\n    standalone: true,\r\n    imports: [CommonModule],\r\n    host: {\r\n        '[class.modal-visible]': 'isVisible'\r\n    }\r\n})\r\nexport class ImageModalComponent implements OnInit, OnDestroy {\r\n    @Input() images: ImageData[] = [];\r\n    @Input() currentIndex: number = 0;\r\n    @Input() isVisible: boolean = false;\r\n\r\n    @Output() close = new EventEmitter<void>();\r\n    @Output() indexChange = new EventEmitter<number>();\r\n\r\n    constructor(\r\n        private cdr: ChangeDetectorRef,\r\n        private elementRef: ElementRef\r\n    ) {}\r\n\r\n    ngOnInit() {\r\n        if (this.currentIndex >= this.images.length) {\r\n            this.currentIndex = 0;\r\n        }\r\n        \r\n        // 監聽 isVisible 變化\r\n        this.handleModalVisibility();\r\n    }\r\n\r\n    ngOnDestroy() {\r\n        // 確保清理\r\n        if (this.isVisible) {\r\n            document.body.classList.remove('modal-open');\r\n        }\r\n    }\r\n\r\n    ngOnChanges() {\r\n        this.handleModalVisibility();\r\n    }\r\n\r\n    private handleModalVisibility() {\r\n        if (this.isVisible) {\r\n            document.body.classList.add('modal-open');\r\n            // 確保模態框在最頂層\r\n            this.elementRef.nativeElement.style.zIndex = '9999';\r\n        } else {\r\n            document.body.classList.remove('modal-open');\r\n        }\r\n    }\r\n\r\n    @HostListener('document:keydown', ['$event'])\r\n    handleKeyDown(event: KeyboardEvent) {\r\n        if (!this.isVisible) return;\r\n\r\n        switch (event.key) {\r\n            case 'Escape':\r\n                this.closeModal();\r\n                break;\r\n            case 'ArrowLeft':\r\n                this.prevImage();\r\n                break;\r\n            case 'ArrowRight':\r\n                this.nextImage();\r\n                break;\r\n        }\r\n    }\r\n\r\n    getCurrentImage(): ImageData | null {\r\n        if (this.images && this.images.length > 0 && this.currentIndex < this.images.length) {\r\n            return this.images[this.currentIndex];\r\n        }\r\n        return null;\r\n    }\r\n\r\n    nextImage(): void {\r\n        if (this.images && this.images.length > 1) {\r\n            this.currentIndex = (this.currentIndex + 1) % this.images.length;\r\n            this.indexChange.emit(this.currentIndex);\r\n        }\r\n    }\r\n\r\n    prevImage(): void {\r\n        if (this.images && this.images.length > 1) {\r\n            this.currentIndex = this.currentIndex === 0 ? this.images.length - 1 : this.currentIndex - 1;\r\n            this.indexChange.emit(this.currentIndex);\r\n        }\r\n    }\r\n\r\n    setCurrentImage(index: number): void {\r\n        if (index >= 0 && index < this.images.length) {\r\n            this.currentIndex = index;\r\n            this.indexChange.emit(this.currentIndex);\r\n        }\r\n    }    closeModal(): void {\r\n        document.body.classList.remove('modal-open');\r\n        this.close.emit();\r\n    }\r\n\r\n    onBackdropClick(event: Event): void {\r\n        if (event.target === event.currentTarget) {\r\n            this.closeModal();\r\n        }\r\n    }\r\n}\r\n", "<!-- 模態背景覆蓋層 -->\r\n<div *ngIf=\"isVisible\" class=\"fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50 p-4\"\r\n  (click)=\"onBackdropClick($event)\"> \r\n  <!-- 模態內容 -->\r\n  <div class=\"relative modal-container max-h-full w-full bg-white rounded-lg shadow-2xl overflow-hidden\" \r\n       (click)=\"$event.stopPropagation()\">\r\n\r\n    <!-- 模態標題列 -->\r\n    <div class=\"flex items-center justify-between p-4 bg-gray-50 border-b border-gray-200\">\r\n      <div class=\"flex items-center space-x-3\">\r\n        <h3 class=\"text-lg font-semibold text-gray-800\">\r\n          {{getCurrentImage()?.name || '圖片預覽'}}\r\n        </h3>\r\n        <span class=\"text-sm text-gray-600\" *ngIf=\"images.length > 1\">\r\n          ({{currentIndex + 1}} / {{images.length}})\r\n        </span>\r\n      </div>\r\n      <button class=\"text-gray-400 hover:text-gray-600 transition-colors\" (click)=\"closeModal()\" title=\"關閉 (ESC)\">\r\n        <svg class=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n          <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M6 18L18 6M6 6l12 12\">\r\n          </path>\r\n        </svg>\r\n      </button>\r\n    </div>\r\n\r\n    <!-- 主要圖片區域 -->\r\n    <div class=\"relative bg-gray-100\">\r\n      <div class=\"flex items-center justify-center modal-image modal-max-height\">\r\n        <img class=\"max-w-full max-h-full object-contain\" [src]=\"'data:image/jpeg;base64,' + getCurrentImage()?.base64\"\r\n          [alt]=\"getCurrentImage()?.name\" *ngIf=\"getCurrentImage()\">\r\n      </div>\r\n\r\n      <!-- 導航按鈕 -->\r\n      <button *ngIf=\"images.length > 1\"\r\n        class=\"absolute left-4 top-1/2 transform -translate-y-1/2 bg-white bg-opacity-90 hover:bg-opacity-100 text-gray-800 rounded-full modal-button flex items-center justify-center shadow-lg hover:shadow-xl transition-all\"\r\n        (click)=\"prevImage()\" title=\"上一張 (←)\">\r\n        <svg class=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n          <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M15 19l-7-7 7-7\"></path>\r\n        </svg>\r\n      </button> <button *ngIf=\"images.length > 1\"\r\n        class=\"absolute right-4 top-1/2 transform -translate-y-1/2 bg-white bg-opacity-90 hover:bg-opacity-100 text-gray-800 rounded-full modal-button flex items-center justify-center shadow-lg hover:shadow-xl transition-all\"\r\n        (click)=\"nextImage()\" title=\"下一張 (→)\">\r\n        <svg class=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n          <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 5l7 7-7 7\"></path>\r\n        </svg>\r\n      </button>\r\n    </div>\r\n\r\n    <!-- 縮略圖導航 -->\r\n    <div *ngIf=\"images.length > 1\" class=\"p-4 bg-gray-50 border-t border-gray-200\">\r\n      <div class=\"flex gap-2 overflow-x-auto pb-2\">\r\n        <button *ngFor=\"let image of images; let i = index\"\r\n          class=\"flex-shrink-0 w-16 h-16 border-2 rounded-lg overflow-hidden hover:border-blue-400 transition-all duration-200 cursor-pointer\"\r\n          [class.border-blue-500]=\"i === currentIndex\" [class.border-gray-300]=\"i !== currentIndex\"\r\n          [class.ring-2]=\"i === currentIndex\" [class.ring-blue-200]=\"i === currentIndex\" (click)=\"setCurrentImage(i)\"\r\n          [title]=\"image.name || '圖片 ' + (i + 1)\">\r\n          <img class=\"w-full h-full object-cover\" [src]=\"'data:image/jpeg;base64,' + image.base64\" [alt]=\"image.name\">\r\n        </button>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 鍵盤提示 -->\r\n    <div class=\"px-4 py-2 bg-gray-100 border-t border-gray-200 text-xs text-gray-500 text-center\">\r\n      使用鍵盤方向鍵切換圖片，按 ESC 關閉\r\n    </div>\r\n  </div>\r\n</div>\r\n"], "mappings": "AAAA,SAAmCA,YAAY,QAAwE,eAAe;AACtI,SAASC,YAAY,QAAQ,iBAAiB;;;;;ICYtCC,EAAA,CAAAC,cAAA,eAA8D;IAC5DD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IADLH,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAK,kBAAA,OAAAC,MAAA,CAAAC,YAAA,aAAAD,MAAA,CAAAE,MAAA,CAAAC,MAAA,OACF;;;;;IAaAT,EAAA,CAAAU,SAAA,cAC4D;;;;;;IAA1DV,EADgD,CAAAW,UAAA,sCAAAC,OAAA,GAAAN,MAAA,CAAAO,eAAA,qBAAAD,OAAA,CAAAE,MAAA,GAAAd,EAAA,CAAAe,aAAA,CAA6D,SAAAC,OAAA,GAAAV,MAAA,CAAAO,eAAA,qBAAAG,OAAA,CAAAC,IAAA,CAC9E;;;;;;IAInCjB,EAAA,CAAAC,cAAA,iBAEwC;IAAtCD,EAAA,CAAAkB,UAAA,mBAAAC,qEAAA;MAAAnB,EAAA,CAAAoB,aAAA,CAAAC,GAAA;MAAA,MAAAf,MAAA,GAAAN,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAuB,WAAA,CAASjB,MAAA,CAAAkB,SAAA,EAAW;IAAA,EAAC;;IACrBxB,EAAA,CAAAC,cAAA,aAA2E;IACzED,EAAA,CAAAU,SAAA,eAAiG;IAErGV,EADE,CAAAG,YAAA,EAAM,EACC;;;;;;IAACH,EAAA,CAAAC,cAAA,iBAE8B;IAAtCD,EAAA,CAAAkB,UAAA,mBAAAO,qEAAA;MAAAzB,EAAA,CAAAoB,aAAA,CAAAM,GAAA;MAAA,MAAApB,MAAA,GAAAN,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAuB,WAAA,CAASjB,MAAA,CAAAqB,SAAA,EAAW;IAAA,EAAC;;IACrB3B,EAAA,CAAAC,cAAA,aAA2E;IACzED,EAAA,CAAAU,SAAA,eAA8F;IAElGV,EADE,CAAAG,YAAA,EAAM,EACC;;;;;;IAMPH,EAAA,CAAAC,cAAA,iBAI0C;IADuCD,EAAA,CAAAkB,UAAA,mBAAAU,2EAAA;MAAA,MAAAC,IAAA,GAAA7B,EAAA,CAAAoB,aAAA,CAAAU,GAAA,EAAAC,KAAA;MAAA,MAAAzB,MAAA,GAAAN,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAuB,WAAA,CAASjB,MAAA,CAAA0B,eAAA,CAAAH,IAAA,CAAkB;IAAA,EAAC;IAE3G7B,EAAA,CAAAU,SAAA,cAA4G;IAC9GV,EAAA,CAAAG,YAAA,EAAS;;;;;;IAH6BH,EADpC,CAAAiC,WAAA,oBAAAJ,IAAA,KAAAvB,MAAA,CAAAC,YAAA,CAA4C,oBAAAsB,IAAA,KAAAvB,MAAA,CAAAC,YAAA,CAA6C,WAAAsB,IAAA,KAAAvB,MAAA,CAAAC,YAAA,CACtD,kBAAAsB,IAAA,KAAAvB,MAAA,CAAAC,YAAA,CAA2C;IAC9EP,EAAA,CAAAW,UAAA,UAAAuB,QAAA,CAAAjB,IAAA,uBAAAY,IAAA,MAAuC;IACC7B,EAAA,CAAAI,SAAA,EAAgD;IAACJ,EAAjD,CAAAW,UAAA,oCAAAuB,QAAA,CAAApB,MAAA,EAAAd,EAAA,CAAAe,aAAA,CAAgD,QAAAmB,QAAA,CAAAjB,IAAA,CAAmB;;;;;IAN/GjB,EADF,CAAAC,cAAA,cAA+E,cAChC;IAC3CD,EAAA,CAAAmC,UAAA,IAAAC,kDAAA,sBAI0C;IAI9CpC,EADE,CAAAG,YAAA,EAAM,EACF;;;;IARwBH,EAAA,CAAAI,SAAA,GAAW;IAAXJ,EAAA,CAAAW,UAAA,YAAAL,MAAA,CAAAE,MAAA,CAAW;;;;;;IAlD7CR,EAAA,CAAAC,cAAA,aACoC;IAAlCD,EAAA,CAAAkB,UAAA,mBAAAmB,wDAAAC,MAAA;MAAAtC,EAAA,CAAAoB,aAAA,CAAAmB,GAAA;MAAA,MAAAjC,MAAA,GAAAN,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAuB,WAAA,CAASjB,MAAA,CAAAkC,eAAA,CAAAF,MAAA,CAAuB;IAAA,EAAC;IAEjCtC,EAAA,CAAAC,cAAA,aACwC;IAAnCD,EAAA,CAAAkB,UAAA,mBAAAuB,wDAAAH,MAAA;MAAAtC,EAAA,CAAAoB,aAAA,CAAAmB,GAAA;MAAA,OAAAvC,EAAA,CAAAuB,WAAA,CAASe,MAAA,CAAAI,eAAA,EAAwB;IAAA,EAAC;IAKjC1C,EAFJ,CAAAC,cAAA,aAAuF,aAC5C,YACS;IAC9CD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAmC,UAAA,IAAAQ,yCAAA,kBAA8D;IAGhE3C,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,gBAA4G;IAAxCD,EAAA,CAAAkB,UAAA,mBAAA0B,2DAAA;MAAA5C,EAAA,CAAAoB,aAAA,CAAAmB,GAAA;MAAA,MAAAjC,MAAA,GAAAN,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAuB,WAAA,CAASjB,MAAA,CAAAuC,UAAA,EAAY;IAAA,EAAC;;IACxF7C,EAAA,CAAAC,cAAA,aAA2E;IACzED,EAAA,CAAAU,SAAA,cACO;IAGbV,EAFI,CAAAG,YAAA,EAAM,EACC,EACL;;IAIJH,EADF,CAAAC,cAAA,eAAkC,eAC2C;IACzED,EAAA,CAAAmC,UAAA,KAAAW,yCAAA,kBAC4D;IAC9D9C,EAAA,CAAAG,YAAA,EAAM;IASIH,EANV,CAAAmC,UAAA,KAAAY,4CAAA,qBAEwC,KAAAC,4CAAA,qBAMA;IAK1ChD,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAAmC,UAAA,KAAAc,yCAAA,kBAA+E;IAa/EjD,EAAA,CAAAC,cAAA,eAA8F;IAC5FD,EAAA,CAAAE,MAAA,yGACF;IAEJF,EAFI,CAAAG,YAAA,EAAM,EACF,EACF;;;;;IAvDIH,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAkD,kBAAA,QAAAC,OAAA,GAAA7C,MAAA,CAAAO,eAAA,qBAAAsC,OAAA,CAAAlC,IAAA,qCACF;IACqCjB,EAAA,CAAAI,SAAA,EAAuB;IAAvBJ,EAAA,CAAAW,UAAA,SAAAL,MAAA,CAAAE,MAAA,CAAAC,MAAA,KAAuB;IAgBzBT,EAAA,CAAAI,SAAA,GAAuB;IAAvBJ,EAAA,CAAAW,UAAA,SAAAL,MAAA,CAAAO,eAAA,GAAuB;IAInDb,EAAA,CAAAI,SAAA,EAAuB;IAAvBJ,EAAA,CAAAW,UAAA,SAAAL,MAAA,CAAAE,MAAA,CAAAC,MAAA,KAAuB;IAMbT,EAAA,CAAAI,SAAA,EAAuB;IAAvBJ,EAAA,CAAAW,UAAA,SAAAL,MAAA,CAAAE,MAAA,CAAAC,MAAA,KAAuB;IAUtCT,EAAA,CAAAI,SAAA,EAAuB;IAAvBJ,EAAA,CAAAW,UAAA,SAAAL,MAAA,CAAAE,MAAA,CAAAC,MAAA,KAAuB;;;ADnCjC,OAAM,MAAO2C,mBAAmB;EAQ5BC,YACYC,GAAsB,EACtBC,UAAsB;IADtB,KAAAD,GAAG,GAAHA,GAAG;IACH,KAAAC,UAAU,GAAVA,UAAU;IATb,KAAA/C,MAAM,GAAgB,EAAE;IACxB,KAAAD,YAAY,GAAW,CAAC;IACxB,KAAAiD,SAAS,GAAY,KAAK;IAEzB,KAAAC,KAAK,GAAG,IAAI3D,YAAY,EAAQ;IAChC,KAAA4D,WAAW,GAAG,IAAI5D,YAAY,EAAU;EAK/C;EAEH6D,QAAQA,CAAA;IACJ,IAAI,IAAI,CAACpD,YAAY,IAAI,IAAI,CAACC,MAAM,CAACC,MAAM,EAAE;MACzC,IAAI,CAACF,YAAY,GAAG,CAAC;IACzB;IAEA;IACA,IAAI,CAACqD,qBAAqB,EAAE;EAChC;EAEAC,WAAWA,CAAA;IACP;IACA,IAAI,IAAI,CAACL,SAAS,EAAE;MAChBM,QAAQ,CAACC,IAAI,CAACC,SAAS,CAACC,MAAM,CAAC,YAAY,CAAC;IAChD;EACJ;EAEAC,WAAWA,CAAA;IACP,IAAI,CAACN,qBAAqB,EAAE;EAChC;EAEQA,qBAAqBA,CAAA;IACzB,IAAI,IAAI,CAACJ,SAAS,EAAE;MAChBM,QAAQ,CAACC,IAAI,CAACC,SAAS,CAACG,GAAG,CAAC,YAAY,CAAC;MACzC;MACA,IAAI,CAACZ,UAAU,CAACa,aAAa,CAACC,KAAK,CAACC,MAAM,GAAG,MAAM;IACvD,CAAC,MAAM;MACHR,QAAQ,CAACC,IAAI,CAACC,SAAS,CAACC,MAAM,CAAC,YAAY,CAAC;IAChD;EACJ;EAGAM,aAAaA,CAACC,KAAoB;IAC9B,IAAI,CAAC,IAAI,CAAChB,SAAS,EAAE;IAErB,QAAQgB,KAAK,CAACC,GAAG;MACb,KAAK,QAAQ;QACT,IAAI,CAAC5B,UAAU,EAAE;QACjB;MACJ,KAAK,WAAW;QACZ,IAAI,CAACrB,SAAS,EAAE;QAChB;MACJ,KAAK,YAAY;QACb,IAAI,CAACG,SAAS,EAAE;QAChB;IACR;EACJ;EAEAd,eAAeA,CAAA;IACX,IAAI,IAAI,CAACL,MAAM,IAAI,IAAI,CAACA,MAAM,CAACC,MAAM,GAAG,CAAC,IAAI,IAAI,CAACF,YAAY,GAAG,IAAI,CAACC,MAAM,CAACC,MAAM,EAAE;MACjF,OAAO,IAAI,CAACD,MAAM,CAAC,IAAI,CAACD,YAAY,CAAC;IACzC;IACA,OAAO,IAAI;EACf;EAEAoB,SAASA,CAAA;IACL,IAAI,IAAI,CAACnB,MAAM,IAAI,IAAI,CAACA,MAAM,CAACC,MAAM,GAAG,CAAC,EAAE;MACvC,IAAI,CAACF,YAAY,GAAG,CAAC,IAAI,CAACA,YAAY,GAAG,CAAC,IAAI,IAAI,CAACC,MAAM,CAACC,MAAM;MAChE,IAAI,CAACiD,WAAW,CAACgB,IAAI,CAAC,IAAI,CAACnE,YAAY,CAAC;IAC5C;EACJ;EAEAiB,SAASA,CAAA;IACL,IAAI,IAAI,CAAChB,MAAM,IAAI,IAAI,CAACA,MAAM,CAACC,MAAM,GAAG,CAAC,EAAE;MACvC,IAAI,CAACF,YAAY,GAAG,IAAI,CAACA,YAAY,KAAK,CAAC,GAAG,IAAI,CAACC,MAAM,CAACC,MAAM,GAAG,CAAC,GAAG,IAAI,CAACF,YAAY,GAAG,CAAC;MAC5F,IAAI,CAACmD,WAAW,CAACgB,IAAI,CAAC,IAAI,CAACnE,YAAY,CAAC;IAC5C;EACJ;EAEAyB,eAAeA,CAACD,KAAa;IACzB,IAAIA,KAAK,IAAI,CAAC,IAAIA,KAAK,GAAG,IAAI,CAACvB,MAAM,CAACC,MAAM,EAAE;MAC1C,IAAI,CAACF,YAAY,GAAGwB,KAAK;MACzB,IAAI,CAAC2B,WAAW,CAACgB,IAAI,CAAC,IAAI,CAACnE,YAAY,CAAC;IAC5C;EACJ;EAAKsC,UAAUA,CAAA;IACXiB,QAAQ,CAACC,IAAI,CAACC,SAAS,CAACC,MAAM,CAAC,YAAY,CAAC;IAC5C,IAAI,CAACR,KAAK,CAACiB,IAAI,EAAE;EACrB;EAEAlC,eAAeA,CAACgC,KAAY;IACxB,IAAIA,KAAK,CAACG,MAAM,KAAKH,KAAK,CAACI,aAAa,EAAE;MACtC,IAAI,CAAC/B,UAAU,EAAE;IACrB;EACJ;;;uCA/FSO,mBAAmB,EAAApD,EAAA,CAAA6E,iBAAA,CAAA7E,EAAA,CAAA8E,iBAAA,GAAA9E,EAAA,CAAA6E,iBAAA,CAAA7E,EAAA,CAAA+E,UAAA;IAAA;EAAA;;;YAAnB3B,mBAAmB;MAAA4B,SAAA;MAAAC,QAAA;MAAAC,YAAA,WAAAC,iCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAnBpF,EAAA,CAAAkB,UAAA,qBAAAoE,+CAAAhD,MAAA;YAAA,OAAA+C,GAAA,CAAAd,aAAA,CAAAjC,MAAA,CAAqB;UAAA,UAAAtC,EAAA,CAAAuF,iBAAA,CAAF;;;UAAnBvF,EAAA,CAAAiC,WAAA,kBAAAoD,GAAA,CAAA7B,SAAA,CAAmB;;;;;;;;;;;;;;;;;;;UCbhCxD,EAAA,CAAAmC,UAAA,IAAAqD,kCAAA,kBACoC;;;UAD9BxF,EAAA,CAAAW,UAAA,SAAA0E,GAAA,CAAA7B,SAAA,CAAe;;;qBDQPzD,YAAY,EAAA0F,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA;MAAAC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}