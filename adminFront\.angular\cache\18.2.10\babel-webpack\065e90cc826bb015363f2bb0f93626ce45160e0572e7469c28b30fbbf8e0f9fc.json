{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/platform-browser\";\nexport class HtmlPipe {\n  constructor(sanitizer) {\n    this.sanitizer = sanitizer;\n  }\n  transform(style) {\n    return this.sanitizer.bypassSecurityTrustHtml(style);\n  }\n  static {\n    this.ɵfac = function HtmlPipe_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || HtmlPipe)(i0.ɵɵdirectiveInject(i1.<PERSON><PERSON><PERSON><PERSON>, 16));\n    };\n  }\n  static {\n    this.ɵpipe = /*@__PURE__*/i0.ɵɵdefinePipe({\n      name: \"html\",\n      type: HtmlPipe,\n      pure: true,\n      standalone: true\n    });\n  }\n}", "map": {"version": 3, "names": ["HtmlPipe", "constructor", "sanitizer", "transform", "style", "bypassSecurityTrustHtml", "i0", "ɵɵdirectiveInject", "i1", "Dom<PERSON><PERSON><PERSON>zer", "pure", "standalone"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\@theme\\pipes\\html.pipes.ts"], "sourcesContent": ["import { Pipe, PipeTransform } from '@angular/core';\r\nimport { DomSanitizer } from '@angular/platform-browser';\r\n\r\n@Pipe({\r\n  name: 'html',\r\n  standalone: true\r\n})\r\nexport class HtmlPipe implements PipeTransform {\r\n  constructor (private sanitizer: DomSanitizer) {\r\n  }\r\n  transform(style:any) {\r\n    return this.sanitizer.bypassSecurityTrustHtml(style);\r\n    }\r\n}\r\n"], "mappings": ";;AAOA,OAAM,MAAOA,QAAQ;EACnBC,YAAqBC,SAAuB;IAAvB,KAAAA,SAAS,GAATA,SAAS;EAC9B;EACAC,SAASA,CAACC,KAAS;IACjB,OAAO,IAAI,CAACF,SAAS,CAACG,uBAAuB,CAACD,KAAK,CAAC;EACpD;;;uCALSJ,QAAQ,EAAAM,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAC,YAAA;IAAA;EAAA;;;;YAART,QAAQ;MAAAU,IAAA;MAAAC,UAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}