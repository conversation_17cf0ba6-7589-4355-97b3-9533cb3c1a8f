{"ast": null, "code": "import { map } from 'rxjs/operators';\nimport { BaseService } from '../base-service';\nimport { apiRegularNoticeFileDeleteRegularNoticeFilePost$Json } from '../fn/regular-notice-file/api-regular-notice-file-delete-regular-notice-file-post-json';\nimport { apiRegularNoticeFileDeleteRegularNoticeFilePost$Plain } from '../fn/regular-notice-file/api-regular-notice-file-delete-regular-notice-file-post-plain';\nimport { apiRegularNoticeFileGetListRegularNoticeFileHouseHoldPost$Json } from '../fn/regular-notice-file/api-regular-notice-file-get-list-regular-notice-file-house-hold-post-json';\nimport { apiRegularNoticeFileGetListRegularNoticeFileHouseHoldPost$Plain } from '../fn/regular-notice-file/api-regular-notice-file-get-list-regular-notice-file-house-hold-post-plain';\nimport { apiRegularNoticeFileGetRegularNoticeFileByIdPost$Json } from '../fn/regular-notice-file/api-regular-notice-file-get-regular-notice-file-by-id-post-json';\nimport { apiRegularNoticeFileGetRegularNoticeFileByIdPost$Plain } from '../fn/regular-notice-file/api-regular-notice-file-get-regular-notice-file-by-id-post-plain';\nimport { apiRegularNoticeFileGetRegularNoticeFileListPost$Json } from '../fn/regular-notice-file/api-regular-notice-file-get-regular-notice-file-list-post-json';\nimport { apiRegularNoticeFileGetRegularNoticeFileListPost$Plain } from '../fn/regular-notice-file/api-regular-notice-file-get-regular-notice-file-list-post-plain';\nimport { apiRegularNoticeFileSaveRegularNoticeFilePost$Json } from '../fn/regular-notice-file/api-regular-notice-file-save-regular-notice-file-post-json';\nimport { apiRegularNoticeFileSaveRegularNoticeFilePost$Plain } from '../fn/regular-notice-file/api-regular-notice-file-save-regular-notice-file-post-plain';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../api-configuration\";\nimport * as i2 from \"@angular/common/http\";\nexport let RegularNoticeFileService = /*#__PURE__*/(() => {\n  class RegularNoticeFileService extends BaseService {\n    constructor(config, http) {\n      super(config, http);\n    }\n    /** Path part for operation `apiRegularNoticeFileDeleteRegularNoticeFilePost()` */\n    static {\n      this.ApiRegularNoticeFileDeleteRegularNoticeFilePostPath = '/api/RegularNoticeFile/DeleteRegularNoticeFile';\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiRegularNoticeFileDeleteRegularNoticeFilePost$Plain()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiRegularNoticeFileDeleteRegularNoticeFilePost$Plain$Response(params, context) {\n      return apiRegularNoticeFileDeleteRegularNoticeFilePost$Plain(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiRegularNoticeFileDeleteRegularNoticeFilePost$Plain$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiRegularNoticeFileDeleteRegularNoticeFilePost$Plain(params, context) {\n      return this.apiRegularNoticeFileDeleteRegularNoticeFilePost$Plain$Response(params, context).pipe(map(r => r.body));\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiRegularNoticeFileDeleteRegularNoticeFilePost$Json()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiRegularNoticeFileDeleteRegularNoticeFilePost$Json$Response(params, context) {\n      return apiRegularNoticeFileDeleteRegularNoticeFilePost$Json(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiRegularNoticeFileDeleteRegularNoticeFilePost$Json$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiRegularNoticeFileDeleteRegularNoticeFilePost$Json(params, context) {\n      return this.apiRegularNoticeFileDeleteRegularNoticeFilePost$Json$Response(params, context).pipe(map(r => r.body));\n    }\n    /** Path part for operation `apiRegularNoticeFileGetRegularNoticeFileListPost()` */\n    static {\n      this.ApiRegularNoticeFileGetRegularNoticeFileListPostPath = '/api/RegularNoticeFile/GetRegularNoticeFileList';\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiRegularNoticeFileGetRegularNoticeFileListPost$Plain()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiRegularNoticeFileGetRegularNoticeFileListPost$Plain$Response(params, context) {\n      return apiRegularNoticeFileGetRegularNoticeFileListPost$Plain(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiRegularNoticeFileGetRegularNoticeFileListPost$Plain$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiRegularNoticeFileGetRegularNoticeFileListPost$Plain(params, context) {\n      return this.apiRegularNoticeFileGetRegularNoticeFileListPost$Plain$Response(params, context).pipe(map(r => r.body));\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiRegularNoticeFileGetRegularNoticeFileListPost$Json()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiRegularNoticeFileGetRegularNoticeFileListPost$Json$Response(params, context) {\n      return apiRegularNoticeFileGetRegularNoticeFileListPost$Json(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiRegularNoticeFileGetRegularNoticeFileListPost$Json$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiRegularNoticeFileGetRegularNoticeFileListPost$Json(params, context) {\n      return this.apiRegularNoticeFileGetRegularNoticeFileListPost$Json$Response(params, context).pipe(map(r => r.body));\n    }\n    /** Path part for operation `apiRegularNoticeFileGetRegularNoticeFileByIdPost()` */\n    static {\n      this.ApiRegularNoticeFileGetRegularNoticeFileByIdPostPath = '/api/RegularNoticeFile/GetRegularNoticeFileById';\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiRegularNoticeFileGetRegularNoticeFileByIdPost$Plain()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiRegularNoticeFileGetRegularNoticeFileByIdPost$Plain$Response(params, context) {\n      return apiRegularNoticeFileGetRegularNoticeFileByIdPost$Plain(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiRegularNoticeFileGetRegularNoticeFileByIdPost$Plain$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiRegularNoticeFileGetRegularNoticeFileByIdPost$Plain(params, context) {\n      return this.apiRegularNoticeFileGetRegularNoticeFileByIdPost$Plain$Response(params, context).pipe(map(r => r.body));\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiRegularNoticeFileGetRegularNoticeFileByIdPost$Json()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiRegularNoticeFileGetRegularNoticeFileByIdPost$Json$Response(params, context) {\n      return apiRegularNoticeFileGetRegularNoticeFileByIdPost$Json(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiRegularNoticeFileGetRegularNoticeFileByIdPost$Json$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiRegularNoticeFileGetRegularNoticeFileByIdPost$Json(params, context) {\n      return this.apiRegularNoticeFileGetRegularNoticeFileByIdPost$Json$Response(params, context).pipe(map(r => r.body));\n    }\n    /** Path part for operation `apiRegularNoticeFileSaveRegularNoticeFilePost()` */\n    static {\n      this.ApiRegularNoticeFileSaveRegularNoticeFilePostPath = '/api/RegularNoticeFile/SaveRegularNoticeFile';\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiRegularNoticeFileSaveRegularNoticeFilePost$Plain()` instead.\n     *\n     * This method sends `multipart/form-data` and handles request body of type `multipart/form-data`.\n     */\n    apiRegularNoticeFileSaveRegularNoticeFilePost$Plain$Response(params, context) {\n      return apiRegularNoticeFileSaveRegularNoticeFilePost$Plain(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiRegularNoticeFileSaveRegularNoticeFilePost$Plain$Response()` instead.\n     *\n     * This method sends `multipart/form-data` and handles request body of type `multipart/form-data`.\n     */\n    apiRegularNoticeFileSaveRegularNoticeFilePost$Plain(params, context) {\n      return this.apiRegularNoticeFileSaveRegularNoticeFilePost$Plain$Response(params, context).pipe(map(r => r.body));\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiRegularNoticeFileSaveRegularNoticeFilePost$Json()` instead.\n     *\n     * This method sends `multipart/form-data` and handles request body of type `multipart/form-data`.\n     */\n    apiRegularNoticeFileSaveRegularNoticeFilePost$Json$Response(params, context) {\n      return apiRegularNoticeFileSaveRegularNoticeFilePost$Json(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiRegularNoticeFileSaveRegularNoticeFilePost$Json$Response()` instead.\n     *\n     * This method sends `multipart/form-data` and handles request body of type `multipart/form-data`.\n     */\n    apiRegularNoticeFileSaveRegularNoticeFilePost$Json(params, context) {\n      return this.apiRegularNoticeFileSaveRegularNoticeFilePost$Json$Response(params, context).pipe(map(r => r.body));\n    }\n    /** Path part for operation `apiRegularNoticeFileGetListRegularNoticeFileHouseHoldPost()` */\n    static {\n      this.ApiRegularNoticeFileGetListRegularNoticeFileHouseHoldPostPath = '/api/RegularNoticeFile/GetListRegularNoticeFileHouseHold';\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiRegularNoticeFileGetListRegularNoticeFileHouseHoldPost$Plain()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiRegularNoticeFileGetListRegularNoticeFileHouseHoldPost$Plain$Response(params, context) {\n      return apiRegularNoticeFileGetListRegularNoticeFileHouseHoldPost$Plain(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiRegularNoticeFileGetListRegularNoticeFileHouseHoldPost$Plain$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiRegularNoticeFileGetListRegularNoticeFileHouseHoldPost$Plain(params, context) {\n      return this.apiRegularNoticeFileGetListRegularNoticeFileHouseHoldPost$Plain$Response(params, context).pipe(map(r => r.body));\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiRegularNoticeFileGetListRegularNoticeFileHouseHoldPost$Json()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiRegularNoticeFileGetListRegularNoticeFileHouseHoldPost$Json$Response(params, context) {\n      return apiRegularNoticeFileGetListRegularNoticeFileHouseHoldPost$Json(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiRegularNoticeFileGetListRegularNoticeFileHouseHoldPost$Json$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiRegularNoticeFileGetListRegularNoticeFileHouseHoldPost$Json(params, context) {\n      return this.apiRegularNoticeFileGetListRegularNoticeFileHouseHoldPost$Json$Response(params, context).pipe(map(r => r.body));\n    }\n    static {\n      this.ɵfac = function RegularNoticeFileService_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || RegularNoticeFileService)(i0.ɵɵinject(i1.ApiConfiguration), i0.ɵɵinject(i2.HttpClient));\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: RegularNoticeFileService,\n        factory: RegularNoticeFileService.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return RegularNoticeFileService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}