{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport * as i0 from \"@angular/core\";\nexport let HomeComponent = /*#__PURE__*/(() => {\n  class HomeComponent {\n    constructor() {}\n    ngOnInit() {}\n    static {\n      this.ɵfac = function HomeComponent_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || HomeComponent)();\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: HomeComponent,\n        selectors: [[\"ngx-home\"]],\n        standalone: true,\n        features: [i0.ɵɵStandaloneFeature],\n        decls: 2,\n        vars: 0,\n        template: function HomeComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"p\");\n            i0.ɵɵtext(1, \"home works!\");\n            i0.ɵɵelementEnd();\n          }\n        },\n        dependencies: [RouterModule]\n      });\n    }\n  }\n  return HomeComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}