{"ast": null, "code": "/* tslint:disable */\n/* eslint-disable */\n/* Code generated by ng-openapi-gen DO NOT EDIT. */\nimport { HttpResponse } from '@angular/common/http';\nimport { filter, map } from 'rxjs/operators';\nimport { RequestBuilder } from '../../request-builder';\nexport function apiBuildCaseGetSystemInstructionPost$Plain(http, rootUrl, params, context) {\n  const rb = new RequestBuilder(rootUrl, apiBuildCaseGetSystemInstructionPost$Plain.PATH, 'post');\n  if (params) {}\n  return http.request(rb.build({\n    responseType: 'text',\n    accept: 'text/plain',\n    context\n  })).pipe(filter(r => r instanceof HttpResponse), map(r => {\n    return r;\n  }));\n}\napiBuildCaseGetSystemInstructionPost$Plain.PATH = '/api/BuildCase/GetSystemInstruction';", "map": {"version": 3, "names": ["HttpResponse", "filter", "map", "RequestBuilder", "apiBuildCaseGetSystemInstructionPost$Plain", "http", "rootUrl", "params", "context", "rb", "PATH", "request", "build", "responseType", "accept", "pipe", "r"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\services\\api\\fn\\build-case\\api-build-case-get-system-instruction-post-plain.ts"], "sourcesContent": ["/* tslint:disable */\r\n/* eslint-disable */\r\n/* Code generated by ng-openapi-gen DO NOT EDIT. */\r\n\r\nimport { HttpClient, HttpContext, HttpResponse } from '@angular/common/http';\r\nimport { Observable } from 'rxjs';\r\nimport { filter, map } from 'rxjs/operators';\r\nimport { StrictHttpResponse } from '../../strict-http-response';\r\nimport { RequestBuilder } from '../../request-builder';\r\n\r\nimport { StringResponseBase } from '../../models/string-response-base';\r\n\r\nexport interface ApiBuildCaseGetSystemInstructionPost$Plain$Params {\r\n}\r\n\r\nexport function apiBuildCaseGetSystemInstructionPost$Plain(http: HttpClient, rootUrl: string, params?: ApiBuildCaseGetSystemInstructionPost$Plain$Params, context?: HttpContext): Observable<StrictHttpResponse<StringResponseBase>> {\r\n  const rb = new RequestBuilder(rootUrl, apiBuildCaseGetSystemInstructionPost$Plain.PATH, 'post');\r\n  if (params) {\r\n  }\r\n\r\n  return http.request(\r\n    rb.build({ responseType: 'text', accept: 'text/plain', context })\r\n  ).pipe(\r\n    filter((r: any): r is HttpResponse<any> => r instanceof HttpResponse),\r\n    map((r: HttpResponse<any>) => {\r\n      return r as StrictHttpResponse<StringResponseBase>;\r\n    })\r\n  );\r\n}\r\n\r\napiBuildCaseGetSystemInstructionPost$Plain.PATH = '/api/BuildCase/GetSystemInstruction';\r\n"], "mappings": "AAAA;AACA;AACA;AAEA,SAAkCA,YAAY,QAAQ,sBAAsB;AAE5E,SAASC,MAAM,EAAEC,GAAG,QAAQ,gBAAgB;AAE5C,SAASC,cAAc,QAAQ,uBAAuB;AAOtD,OAAM,SAAUC,0CAA0CA,CAACC,IAAgB,EAAEC,OAAe,EAAEC,MAA0D,EAAEC,OAAqB;EAC7K,MAAMC,EAAE,GAAG,IAAIN,cAAc,CAACG,OAAO,EAAEF,0CAA0C,CAACM,IAAI,EAAE,MAAM,CAAC;EAC/F,IAAIH,MAAM,EAAE,CACZ;EAEA,OAAOF,IAAI,CAACM,OAAO,CACjBF,EAAE,CAACG,KAAK,CAAC;IAAEC,YAAY,EAAE,MAAM;IAAEC,MAAM,EAAE,YAAY;IAAEN;EAAO,CAAE,CAAC,CAClE,CAACO,IAAI,CACJd,MAAM,CAAEe,CAAM,IAA6BA,CAAC,YAAYhB,YAAY,CAAC,EACrEE,GAAG,CAAEc,CAAoB,IAAI;IAC3B,OAAOA,CAA2C;EACpD,CAAC,CAAC,CACH;AACH;AAEAZ,0CAA0C,CAACM,IAAI,GAAG,qCAAqC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}