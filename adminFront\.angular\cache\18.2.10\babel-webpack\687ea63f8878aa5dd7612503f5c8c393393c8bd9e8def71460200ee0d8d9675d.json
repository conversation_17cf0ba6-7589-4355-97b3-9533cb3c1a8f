{"ast": null, "code": "import * as i0 from \"@angular/core\";\nexport class PeriodsService {\n  getYears() {\n    return ['2010', '2011', '2012', '2013', '2014', '2015', '2016', '2017', '2018'];\n  }\n  getMonths() {\n    return ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];\n  }\n  getWeeks() {\n    return ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];\n  }\n  static {\n    this.ɵfac = function PeriodsService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || PeriodsService)();\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: PeriodsService,\n      factory: PeriodsService.ɵfac\n    });\n  }\n}", "map": {"version": 3, "names": ["PeriodsService", "getYears", "getMonths", "getWeeks", "factory", "ɵfac"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\@core\\mock\\periods.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\n\r\n@Injectable()\r\nexport class PeriodsService {\r\n  getYears() {\r\n    return [\r\n      '2010', '2011', '2012',\r\n      '2013', '2014', '2015',\r\n      '2016', '2017', '2018',\r\n    ];\r\n  }\r\n\r\n  getMonths() {\r\n    return [\r\n      'Jan', 'Feb', 'Mar',\r\n      'Apr', 'May', 'Jun',\r\n      'Jul', 'Aug', 'Sep',\r\n      'Oct', 'Nov', 'Dec',\r\n    ];\r\n  }\r\n\r\n  getWeeks() {\r\n    return [\r\n      'Mon',\r\n      'Tue',\r\n      'Wed',\r\n      'Thu',\r\n      'Fri',\r\n      'Sat',\r\n      'Sun',\r\n    ];\r\n  }\r\n}\r\n"], "mappings": ";AAGA,OAAM,MAAOA,cAAc;EACzBC,QAAQA,CAAA;IACN,OAAO,CACL,MAAM,EAAE,MAAM,EAAE,MAAM,EACtB,MAAM,EAAE,MAAM,EAAE,MAAM,EACtB,MAAM,EAAE,MAAM,EAAE,MAAM,CACvB;EACH;EAEAC,SAASA,CAAA;IACP,OAAO,CACL,KAAK,EAAE,KAAK,EAAE,KAAK,EACnB,KAAK,EAAE,KAAK,EAAE,KAAK,EACnB,KAAK,EAAE,KAAK,EAAE,KAAK,EACnB,KAAK,EAAE,KAAK,EAAE,KAAK,CACpB;EACH;EAEAC,QAAQA,CAAA;IACN,OAAO,CACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,CACN;EACH;;;uCA5BWH,cAAc;IAAA;EAAA;;;aAAdA,cAAc;MAAAI,OAAA,EAAdJ,cAAc,CAAAK;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}