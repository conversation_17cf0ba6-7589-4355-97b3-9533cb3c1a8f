{"ast": null, "code": "/* tslint:disable */\n/* eslint-disable */\n/* Code generated by ng-openapi-gen DO NOT EDIT. */\nimport { HttpResponse } from '@angular/common/http';\nimport { filter, map } from 'rxjs/operators';\nimport { RequestBuilder } from '../../request-builder';\nexport function apiFileGetFileGet(http, rootUrl, params, context) {\n  const rb = new RequestBuilder(rootUrl, apiFileGetFileGet.PATH, 'get');\n  if (params) {\n    rb.query('relativePath', params.relativePath, {});\n    rb.query('fileName', params.fileName, {});\n  }\n  return http.request(rb.build({\n    responseType: 'text',\n    accept: '*/*',\n    context\n  })).pipe(filter(r => r instanceof HttpResponse), map(r => {\n    return r.clone({\n      body: undefined\n    });\n  }));\n}\napiFileGetFileGet.PATH = '/api/File/GetFile';", "map": {"version": 3, "names": ["HttpResponse", "filter", "map", "RequestBuilder", "apiFileGetFileGet", "http", "rootUrl", "params", "context", "rb", "PATH", "query", "relativePath", "fileName", "request", "build", "responseType", "accept", "pipe", "r", "clone", "body", "undefined"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\services\\api\\fn\\file\\api-file-get-file-get.ts"], "sourcesContent": ["/* tslint:disable */\r\n/* eslint-disable */\r\n/* Code generated by ng-openapi-gen DO NOT EDIT. */\r\n\r\nimport { HttpClient, HttpContext, HttpResponse } from '@angular/common/http';\r\nimport { Observable } from 'rxjs';\r\nimport { filter, map } from 'rxjs/operators';\r\nimport { StrictHttpResponse } from '../../strict-http-response';\r\nimport { RequestBuilder } from '../../request-builder';\r\n\r\n\r\nexport interface ApiFileGetFileGet$Params {\r\n  relativePath?: string;\r\n  fileName?: string;\r\n}\r\n\r\nexport function apiFileGetFileGet(http: HttpClient, rootUrl: string, params?: ApiFileGetFileGet$Params, context?: HttpContext): Observable<StrictHttpResponse<void>> {\r\n  const rb = new RequestBuilder(rootUrl, apiFileGetFileGet.PATH, 'get');\r\n  if (params) {\r\n    rb.query('relativePath', params.relativePath, {});\r\n    rb.query('fileName', params.fileName, {});\r\n  }\r\n\r\n  return http.request(\r\n    rb.build({ responseType: 'text', accept: '*/*', context })\r\n  ).pipe(\r\n    filter((r: any): r is HttpResponse<any> => r instanceof HttpResponse),\r\n    map((r: HttpResponse<any>) => {\r\n      return (r as HttpResponse<any>).clone({ body: undefined }) as StrictHttpResponse<void>;\r\n    })\r\n  );\r\n}\r\n\r\napiFileGetFileGet.PATH = '/api/File/GetFile';\r\n"], "mappings": "AAAA;AACA;AACA;AAEA,SAAkCA,YAAY,QAAQ,sBAAsB;AAE5E,SAASC,MAAM,EAAEC,GAAG,QAAQ,gBAAgB;AAE5C,SAASC,cAAc,QAAQ,uBAAuB;AAQtD,OAAM,SAAUC,iBAAiBA,CAACC,IAAgB,EAAEC,OAAe,EAAEC,MAAiC,EAAEC,OAAqB;EAC3H,MAAMC,EAAE,GAAG,IAAIN,cAAc,CAACG,OAAO,EAAEF,iBAAiB,CAACM,IAAI,EAAE,KAAK,CAAC;EACrE,IAAIH,MAAM,EAAE;IACVE,EAAE,CAACE,KAAK,CAAC,cAAc,EAAEJ,MAAM,CAACK,YAAY,EAAE,EAAE,CAAC;IACjDH,EAAE,CAACE,KAAK,CAAC,UAAU,EAAEJ,MAAM,CAACM,QAAQ,EAAE,EAAE,CAAC;EAC3C;EAEA,OAAOR,IAAI,CAACS,OAAO,CACjBL,EAAE,CAACM,KAAK,CAAC;IAAEC,YAAY,EAAE,MAAM;IAAEC,MAAM,EAAE,KAAK;IAAET;EAAO,CAAE,CAAC,CAC3D,CAACU,IAAI,CACJjB,MAAM,CAAEkB,CAAM,IAA6BA,CAAC,YAAYnB,YAAY,CAAC,EACrEE,GAAG,CAAEiB,CAAoB,IAAI;IAC3B,OAAQA,CAAuB,CAACC,KAAK,CAAC;MAAEC,IAAI,EAAEC;IAAS,CAAE,CAA6B;EACxF,CAAC,CAAC,CACH;AACH;AAEAlB,iBAAiB,CAACM,IAAI,GAAG,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}