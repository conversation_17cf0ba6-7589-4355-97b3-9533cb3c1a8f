{"ast": null, "code": "import { HttpHeaders } from '@angular/common/http';\nimport * as moment from 'moment';\nimport { environment } from 'src/environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport class ServiceBase {\n  constructor(http) {\n    this.http = http;\n    this.httpOptions = {\n      headers: new HttpHeaders({\n        'Content-Type': 'application/json'\n      })\n    };\n    this.apiBaseUrl = `${environment.BASE_URL_API}/api`;\n  }\n  convertUtcDate(data) {\n    return data;\n  }\n  getUtcDate() {\n    return moment().utc;\n  }\n  // 實作moment\n  convertLocalDate(data) {\n    return data;\n  }\n  formatDate(data, format) {}\n  static {\n    this.ɵfac = function ServiceBase_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || ServiceBase)(i0.ɵɵinject(i1.HttpClient));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: ServiceBase,\n      factory: ServiceBase.ɵfac\n    });\n  }\n}", "map": {"version": 3, "names": ["HttpHeaders", "moment", "environment", "ServiceBase", "constructor", "http", "httpOptions", "headers", "apiBaseUrl", "BASE_URL_API", "convertUtcDate", "data", "getUtcDate", "utc", "convertLocalDate", "formatDate", "format", "i0", "ɵɵinject", "i1", "HttpClient", "factory", "ɵfac"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\services\\api\\services\\service-base.ts"], "sourcesContent": ["import { HttpClient, HttpHeaders } from '@angular/common/http';\r\nimport { Injectable } from '@angular/core';\r\nimport * as moment from 'moment';\r\nimport { environment } from 'src/environments/environment';\r\n\r\n@Injectable()\r\nexport class ServiceBase {\r\n  protected httpOptions = {\r\n    headers: new HttpHeaders({ 'Content-Type': 'application/json' }),\r\n  };\r\n  protected apiBaseUrl: string = `${environment.BASE_URL_API}/api`;\r\n\r\n  constructor(protected http: HttpClient) { }\r\n\r\n  convertUtcDate(data: Date) {\r\n    return data;\r\n  }\r\n\r\n  getUtcDate() {\r\n    return moment().utc;\r\n  }\r\n\r\n  // 實作moment\r\n  convertLocalDate(data: Date) {\r\n    return data;\r\n  }\r\n\r\n  formatDate(data: Date, format: string) {\r\n\r\n  }\r\n\r\n}\r\n"], "mappings": "AAAA,SAAqBA,WAAW,QAAQ,sBAAsB;AAE9D,OAAO,KAAKC,MAAM,MAAM,QAAQ;AAChC,SAASC,WAAW,QAAQ,8BAA8B;;;AAG1D,OAAM,MAAOC,WAAW;EAMtBC,YAAsBC,IAAgB;IAAhB,KAAAA,IAAI,GAAJA,IAAI;IALhB,KAAAC,WAAW,GAAG;MACtBC,OAAO,EAAE,IAAIP,WAAW,CAAC;QAAE,cAAc,EAAE;MAAkB,CAAE;KAChE;IACS,KAAAQ,UAAU,GAAW,GAAGN,WAAW,CAACO,YAAY,MAAM;EAEtB;EAE1CC,cAAcA,CAACC,IAAU;IACvB,OAAOA,IAAI;EACb;EAEAC,UAAUA,CAAA;IACR,OAAOX,MAAM,EAAE,CAACY,GAAG;EACrB;EAEA;EACAC,gBAAgBA,CAACH,IAAU;IACzB,OAAOA,IAAI;EACb;EAEAI,UAAUA,CAACJ,IAAU,EAAEK,MAAc,GAErC;;;uCAvBWb,WAAW,EAAAc,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;IAAA;EAAA;;;aAAXjB,WAAW;MAAAkB,OAAA,EAAXlB,WAAW,CAAAmB;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}