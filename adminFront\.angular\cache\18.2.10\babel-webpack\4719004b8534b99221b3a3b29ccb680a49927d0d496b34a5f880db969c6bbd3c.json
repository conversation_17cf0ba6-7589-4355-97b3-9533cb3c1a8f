{"ast": null, "code": "import * as i0 from \"@angular/core\";\nexport class DateFormatPipe {\n  transform(value) {\n    if (!value) return value;\n    const date = new Date(value);\n    const year = date.getFullYear();\n    const month = String(date.getMonth() + 1).padStart(2, '0');\n    const day = String(date.getDate()).padStart(2, '0');\n    return `${year}-${month}-${day}`;\n  }\n  static {\n    this.ɵfac = function DateFormatPipe_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || DateFormatPipe)();\n    };\n  }\n  static {\n    this.ɵpipe = /*@__PURE__*/i0.ɵɵdefinePipe({\n      name: \"dateFormat\",\n      type: DateFormatPipe,\n      pure: true,\n      standalone: true\n    });\n  }\n}\nexport class DateFormatHourPipe {\n  transform(value) {\n    if (!value) return value;\n    const date = new Date(value);\n    const year = date.getFullYear();\n    const month = String(date.getMonth() + 1).padStart(2, '0');\n    const day = String(date.getDate()).padStart(2, '0');\n    const hours = String(date.getHours()).padStart(2, '0');\n    const minutes = String(date.getMinutes()).padStart(2, '0');\n    const seconds = String(date.getSeconds()).padStart(2, '0');\n    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;\n  }\n  static {\n    this.ɵfac = function DateFormatHourPipe_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || DateFormatHourPipe)();\n    };\n  }\n  static {\n    this.ɵpipe = /*@__PURE__*/i0.ɵɵdefinePipe({\n      name: \"dateFormatHour\",\n      type: DateFormatHourPipe,\n      pure: true,\n      standalone: true\n    });\n  }\n}", "map": {"version": 3, "names": ["DateFormatPipe", "transform", "value", "date", "Date", "year", "getFullYear", "month", "String", "getMonth", "padStart", "day", "getDate", "pure", "standalone", "DateFormatHourPipe", "hours", "getHours", "minutes", "getMinutes", "seconds", "getSeconds"], "sources": ["C:\\Users\\<USER>\\Documents\\jeansalechange\\adminFront\\src\\app\\@theme\\pipes\\date-format.pipe.ts"], "sourcesContent": ["import { Pipe, PipeTransform } from '@angular/core';\r\n\r\n@Pipe({\r\n  name: 'dateFormat',\r\n  standalone: true\r\n})\r\nexport class DateFormatPipe implements PipeTransform {\r\n\r\n  transform(value: string | undefined ): string | undefined  {\r\n    if (!value) return value;\r\n    const date = new Date(value);\r\n    const year = date.getFullYear();\r\n    const month = String(date.getMonth() + 1).padStart(2, '0');\r\n    const day = String(date.getDate()).padStart(2, '0');\r\n    return `${year}-${month}-${day}`;\r\n  }\r\n}\r\n\r\n@Pipe({\r\n  name: 'dateFormatHour',\r\n  standalone: true\r\n})\r\nexport class DateFormatHourPipe implements PipeTransform {\r\n\r\n  transform(value: string | undefined): string | undefined {\r\n    if (!value) return value;\r\n    const date = new Date(value);\r\n    const year = date.getFullYear();\r\n    const month = String(date.getMonth() + 1).padStart(2, '0');\r\n    const day = String(date.getDate()).padStart(2, '0');\r\n    const hours = String(date.getHours()).padStart(2, '0');\r\n    const minutes = String(date.getMinutes()).padStart(2, '0');\r\n    const seconds = String(date.getSeconds()).padStart(2, '0');\r\n    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;\r\n  }\r\n}"], "mappings": ";AAMA,OAAM,MAAOA,cAAc;EAEzBC,SAASA,CAACC,KAAyB;IACjC,IAAI,CAACA,KAAK,EAAE,OAAOA,KAAK;IACxB,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAACF,KAAK,CAAC;IAC5B,MAAMG,IAAI,GAAGF,IAAI,CAACG,WAAW,EAAE;IAC/B,MAAMC,KAAK,GAAGC,MAAM,CAACL,IAAI,CAACM,QAAQ,EAAE,GAAG,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;IAC1D,MAAMC,GAAG,GAAGH,MAAM,CAACL,IAAI,CAACS,OAAO,EAAE,CAAC,CAACF,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;IACnD,OAAO,GAAGL,IAAI,IAAIE,KAAK,IAAII,GAAG,EAAE;EAClC;;;uCATWX,cAAc;IAAA;EAAA;;;;YAAdA,cAAc;MAAAa,IAAA;MAAAC,UAAA;IAAA;EAAA;;AAgB3B,OAAM,MAAOC,kBAAkB;EAE7Bd,SAASA,CAACC,KAAyB;IACjC,IAAI,CAACA,KAAK,EAAE,OAAOA,KAAK;IACxB,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAACF,KAAK,CAAC;IAC5B,MAAMG,IAAI,GAAGF,IAAI,CAACG,WAAW,EAAE;IAC/B,MAAMC,KAAK,GAAGC,MAAM,CAACL,IAAI,CAACM,QAAQ,EAAE,GAAG,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;IAC1D,MAAMC,GAAG,GAAGH,MAAM,CAACL,IAAI,CAACS,OAAO,EAAE,CAAC,CAACF,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;IACnD,MAAMM,KAAK,GAAGR,MAAM,CAACL,IAAI,CAACc,QAAQ,EAAE,CAAC,CAACP,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;IACtD,MAAMQ,OAAO,GAAGV,MAAM,CAACL,IAAI,CAACgB,UAAU,EAAE,CAAC,CAACT,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;IAC1D,MAAMU,OAAO,GAAGZ,MAAM,CAACL,IAAI,CAACkB,UAAU,EAAE,CAAC,CAACX,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;IAC1D,OAAO,GAAGL,IAAI,IAAIE,KAAK,IAAII,GAAG,IAAIK,KAAK,IAAIE,OAAO,IAAIE,OAAO,EAAE;EACjE;;;uCAZWL,kBAAkB;IAAA;EAAA;;;;YAAlBA,kBAAkB;MAAAF,IAAA;MAAAC,UAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}