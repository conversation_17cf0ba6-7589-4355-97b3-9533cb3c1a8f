{"ast": null, "code": "import * as i0 from \"@angular/core\";\nexport class FilterListItemsPipe {\n  transform(value, searchText) {\n    if (!searchText) {\n      return value;\n    }\n    return value.filter(data => this.matchValue(data, searchText));\n  }\n  matchValue(data, value) {\n    return Object.keys(data).map(key => {\n      return new RegExp(value, 'gi').test(data[key]);\n    }).some(result => result);\n  }\n  static {\n    this.ɵfac = function FilterListItemsPipe_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || FilterListItemsPipe)();\n    };\n  }\n  static {\n    this.ɵpipe = /*@__PURE__*/i0.ɵɵdefinePipe({\n      name: \"filterListItems\",\n      type: FilterListItemsPipe,\n      pure: true,\n      standalone: true\n    });\n  }\n}", "map": {"version": 3, "names": ["FilterListItemsPipe", "transform", "value", "searchText", "filter", "data", "matchValue", "Object", "keys", "map", "key", "RegExp", "test", "some", "result", "pure", "standalone"], "sources": ["C:\\Users\\<USER>\\Documents\\jeansalechange\\adminFront\\src\\app\\@theme\\pipes\\searchText.pipe.ts"], "sourcesContent": ["import { Pipe, PipeTransform } from \"@angular/core\";\r\nimport { BuildCaseGetListReponse } from \"src/services/api/models\";\r\n\r\n@Pipe({ name: \"filterListItems\", standalone: true })\r\nexport class FilterListItemsPipe implements PipeTransform {\r\n    transform(value: BuildCaseGetListReponse[], searchText: string): BuildCaseGetListReponse[] {\r\n        if (!searchText) {\r\n            return value;\r\n        }\r\n        return value.filter((data) => this.matchValue(data, searchText));\r\n    }\r\n\r\n    matchValue(data: any, value: any) {\r\n        return Object.keys(data).map((key) => {\r\n            return new RegExp(value, 'gi').test(data[key]);\r\n        }).some(result => result);\r\n    }\r\n}\r\n"], "mappings": ";AAIA,OAAM,MAAOA,mBAAmB;EAC5BC,SAASA,CAACC,KAAgC,EAAEC,UAAkB;IAC1D,IAAI,CAACA,UAAU,EAAE;MACb,OAAOD,KAAK;IAChB;IACA,OAAOA,KAAK,CAACE,MAAM,CAAEC,IAAI,IAAK,IAAI,CAACC,UAAU,CAACD,IAAI,EAAEF,UAAU,CAAC,CAAC;EACpE;EAEAG,UAAUA,CAACD,IAAS,EAAEH,KAAU;IAC5B,OAAOK,MAAM,CAACC,IAAI,CAACH,IAAI,CAAC,CAACI,GAAG,CAAEC,GAAG,IAAI;MACjC,OAAO,IAAIC,MAAM,CAACT,KAAK,EAAE,IAAI,CAAC,CAACU,IAAI,CAACP,IAAI,CAACK,GAAG,CAAC,CAAC;IAClD,CAAC,CAAC,CAACG,IAAI,CAACC,MAAM,IAAIA,MAAM,CAAC;EAC7B;;;uCAZSd,mBAAmB;IAAA;EAAA;;;;YAAnBA,mBAAmB;MAAAe,IAAA;MAAAC,UAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}