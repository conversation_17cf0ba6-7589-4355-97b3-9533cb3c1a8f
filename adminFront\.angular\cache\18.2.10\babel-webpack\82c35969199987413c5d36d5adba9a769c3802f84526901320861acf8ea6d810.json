{"ast": null, "code": "import { Page } from \"../page.model\";\nexport class ShareRequest extends Page {\n  constructor() {\n    super(...arguments);\n    this.CName = \"\";\n    this.CStatus = -1;\n    this.CUserName = \"\";\n    this.FunctionId = -1;\n    this.RefBaseTaskStatus = -1;\n  }\n}\nexport class ShareMenuButtonRequest {}", "map": {"version": 3, "names": ["Page", "ShareRequest", "constructor", "CName", "CStatus", "CUserName", "FunctionId", "RefBaseTaskStatus", "ShareMenuButtonRequest"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\shared\\model\\requests\\shareRequest.ts"], "sourcesContent": ["import { Page } from \"../page.model\";\r\nexport class ShareRequest extends Page {\r\n  CName = \"\";\r\n  CStatus = -1;\r\n  CId: number | null;\r\n  CUserName = \"\";\r\n  CUserId: number;\r\n  DateStart: Date;\r\n  DateEnd: Date;\r\n  FunctionId = -1;\r\n  CFunctionId: number;\r\n  CMemberId: number;\r\n  RefBaseInfoId: number;\r\n  RefBaseTaskStatus = -1;\r\n  CRefBaseTaskId: number;\r\n  CTagId: number;\r\n  CTagName: string;\r\n  File: any;\r\n  IsDefault: boolean;\r\n  LastUrl: string;\r\n  CPages: number;\r\n  CType: string;\r\n  contentCModelName: string;\r\n  contentCTemplateId: number;\r\n  announcementCNodeMainId: number;\r\n  announcementCTitle: number;\r\n  announcementCOnShelfDate: Date;\r\n  announcementCOffShelfDate: Date;\r\n  announcementCStatus: number;\r\n\r\n  meetingId: number;\r\n  meetingMinutesId: number;\r\n\r\n  CKeyWord: string;\r\n  CNodeMainId: number;\r\n}\r\n\r\nexport class ShareMenuButtonRequest {\r\n  CNodeMainId: number;\r\n}\r\n"], "mappings": "AAAA,SAASA,IAAI,QAAQ,eAAe;AACpC,OAAM,MAAOC,YAAa,SAAQD,IAAI;EAAtCE,YAAA;;IACE,KAAAC,KAAK,GAAG,EAAE;IACV,KAAAC,OAAO,GAAG,CAAC,CAAC;IAEZ,KAAAC,SAAS,GAAG,EAAE;IAId,KAAAC,UAAU,GAAG,CAAC,CAAC;IAIf,KAAAC,iBAAiB,GAAG,CAAC,CAAC;EAsBxB;;AAEA,OAAM,MAAOC,sBAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}