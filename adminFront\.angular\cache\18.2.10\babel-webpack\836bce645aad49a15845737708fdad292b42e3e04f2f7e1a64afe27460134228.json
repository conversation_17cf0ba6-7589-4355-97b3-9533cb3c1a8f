{"ast": null, "code": "import { Observable } from 'rxjs';\nimport { map, switchMap } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../services/api/services/quotation.service\";\nimport * as i2 from \"@angular/common/http\";\nexport class QuotationService {\n  constructor(apiQuotationService, http) {\n    this.apiQuotationService = apiQuotationService;\n    this.http = http;\n    this.apiUrl = '/api/Quotation';\n  }\n  // 取得報價單列表\n  getQuotationList(request) {\n    return this.apiQuotationService.apiQuotationGetListPost$Json({\n      body: request\n    });\n  }\n  // 取得單筆報價單資料\n  getQuotationData(quotationId) {\n    const request = {\n      cQuotationID: quotationId\n    };\n    return this.apiQuotationService.apiQuotationGetDataPost$Json({\n      body: request\n    });\n  }\n  // 取得戶別的報價項目\n  getQuotationByHouseId(houseId) {\n    const request = {\n      cHouseID: houseId\n    };\n    return this.apiQuotationService.apiQuotationGetListByHouseIdPost$Json({\n      body: request\n    });\n  }\n  // 儲存報價單 (支援單一項目)\n  saveQuotationItem(quotation) {\n    return this.apiQuotationService.apiQuotationSaveDataPost$Json({\n      body: quotation\n    });\n  }\n  // 儲存報價單 (支援批量保存多個項目)\n  saveQuotation(request) {\n    // 先取得現有的報價資料來處理版本號\n    return this.getQuotationByHouseId(request.houseId).pipe(map(existingResponse => {\n      const existingItems = existingResponse?.StatusCode === 0 && existingResponse?.Entries ? existingResponse.Entries : [];\n      // 建立現有項目的版本對照表\n      const existingVersionMap = new Map();\n      existingItems.forEach(item => {\n        if (item.CQuotationID || item.cQuotationID) {\n          const id = item.CQuotationID || item.cQuotationID;\n          const version = item.CVersion || item.cVersion || 1;\n          existingVersionMap.set(id, version);\n        }\n      });\n      // 準備儲存的項目，處理版本號邏輯\n      const saveObservables = request.items.map(item => {\n        let version = 1; // 新項目預設版本為1\n        // 如果是現有項目，版本號+1\n        if (item.cQuotationID && existingVersionMap.has(item.cQuotationID)) {\n          version = existingVersionMap.get(item.cQuotationID) + 1;\n        }\n        const saveData = {\n          cQuotationID: item.cQuotationID || null,\n          cHouseID: request.houseId,\n          cItemName: item.cItemName,\n          cUnitPrice: item.cUnitPrice,\n          cCount: item.cCount,\n          cStatus: item.cStatus || 1,\n          cVersion: version,\n          cIsDeafult: item.cIsDefault || false\n        };\n        return this.apiQuotationService.apiQuotationSaveDataPost$Json({\n          body: saveData\n        });\n      });\n      return saveObservables;\n    }), switchMap(saveObservables => {\n      // 等待所有項目保存完成\n      return new Observable(observer => {\n        Promise.all(saveObservables.map(obs => obs.toPromise())).then(responses => {\n          // 檢查是否所有回應都成功\n          const allSuccess = responses.every(response => response?.StatusCode === 0);\n          observer.next({\n            success: allSuccess,\n            message: allSuccess ? '報價單保存成功' : '部分項目保存失敗',\n            data: request.items\n          });\n          observer.complete();\n        }).catch(error => {\n          observer.next({\n            success: false,\n            message: '報價單保存失敗',\n            data: []\n          });\n          observer.complete();\n        });\n      });\n    }));\n  }\n  // 刪除報價單\n  deleteQuotation(quotationId) {\n    const request = {\n      cQuotationID: quotationId\n    };\n    return this.apiQuotationService.apiQuotationDeleteDataPost$Json({\n      body: request\n    });\n  }\n  // 取得預設報價項目 (保持原有方法以兼容現有代碼)\n  getDefaultQuotationItems() {\n    // 使用 GetList 方法獲取預設項目\n    const request = {\n      pageIndex: 0,\n      pageSize: 100\n      // 其他預設參數可能需要根據實際 API 需求調整\n    };\n    return this.apiQuotationService.apiQuotationGetListPost$Json({\n      body: request\n    }).pipe(map(response => {\n      // 轉換 API 響應格式以保持兼容性\n      return {\n        success: response.statusCode === 0,\n        // 假設 statusCode 0 表示成功\n        message: response.message || '',\n        data: response.entries || []\n      };\n    }));\n  } // 載入預設報價項目 (LoadDefaultItems API)\n  loadDefaultItems(request) {\n    // 使用 apiQuotationService 的基礎設施發送請求\n    return this.apiQuotationService.http.post(`${this.apiQuotationService.rootUrl}/api/Quotation/LoadDefaultItems`, request).pipe(map(response => {\n      // 轉換 API 響應格式以保持兼容性\n      return {\n        success: response.StatusCode === 0,\n        // 假設 StatusCode 0 表示成功\n        message: response.Message || '',\n        data: response.Entries || []\n      };\n    }));\n  }\n  // 更新報價項目 (使用 SaveData 方法)\n  updateQuotationItem(quotationId, item) {\n    const saveData = {\n      cQuotationID: quotationId,\n      cHouseID: item.cHouseID,\n      cItemName: item.cItemName,\n      cUnitPrice: item.cUnitPrice,\n      cCount: item.cCount,\n      cStatus: item.cStatus || 1,\n      cVersion: item.cVersion || 1,\n      cIsDeafult: item.cIsDefault\n    };\n    return this.apiQuotationService.apiQuotationSaveDataPost$Json({\n      body: saveData\n    }).pipe(map(response => {\n      return {\n        success: response.StatusCode === 0,\n        // 假設 StatusCode 0 表示成功\n        message: response.Message || '',\n        data: [item] // 包裝為陣列以符合 QuotationResponse 格式\n      };\n    }));\n  }\n  // 刪除報價項目\n  deleteQuotationItem(quotationId) {\n    return this.deleteQuotation(quotationId).pipe(map(response => {\n      return {\n        success: response.StatusCode === 0,\n        // 假設 StatusCode 0 表示成功\n        message: response.Message || '',\n        data: []\n      };\n    }));\n  }\n  // 匯出報價單 (需要另外實作，因為 swagger 中沒有此 API)\n  exportQuotation(houseId) {\n    // 這個方法可能需要使用其他 API 或保持原有實作\n    // 暫時拋出錯誤提示需要實作\n    throw new Error('Export quotation functionality needs to be implemented separately');\n  }\n  static {\n    this.ɵfac = function QuotationService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || QuotationService)(i0.ɵɵinject(i1.QuotationService), i0.ɵɵinject(i2.HttpClient));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: QuotationService,\n      factory: QuotationService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["Observable", "map", "switchMap", "QuotationService", "constructor", "apiQuotationService", "http", "apiUrl", "getQuotationList", "request", "apiQuotationGetListPost$Json", "body", "getQuotationData", "quotationId", "cQuotationID", "apiQuotationGetDataPost$Json", "getQuotationByHouseId", "houseId", "cHouseID", "apiQuotationGetListByHouseIdPost$Json", "saveQuotationItem", "quotation", "apiQuotationSaveDataPost$Json", "saveQuotation", "pipe", "existingResponse", "existingItems", "StatusCode", "Entries", "existingVersionMap", "Map", "for<PERSON>ach", "item", "CQuotationID", "id", "version", "CVersion", "cVersion", "set", "saveObservables", "items", "has", "get", "saveData", "cItemName", "cUnitPrice", "cCount", "cStatus", "cIs<PERSON><PERSON><PERSON>t", "cIsDefault", "observer", "Promise", "all", "obs", "to<PERSON>romise", "then", "responses", "allSuccess", "every", "response", "next", "success", "message", "data", "complete", "catch", "error", "deleteQuotation", "apiQuotationDeleteDataPost$Json", "getDefaultQuotationItems", "pageIndex", "pageSize", "statusCode", "entries", "loadDefaultItems", "post", "rootUrl", "Message", "updateQuotationItem", "deleteQuotationItem", "exportQuotation", "Error", "i0", "ɵɵinject", "i1", "i2", "HttpClient", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\services\\quotation.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport { Observable } from 'rxjs';\r\nimport { map, switchMap } from 'rxjs/operators';\r\nimport { HttpClient } from '@angular/common/http';\r\nimport { QuotationItem, QuotationRequest, QuotationResponse } from '../models/quotation.model';\r\nimport { QuotationService as ApiQuotationService } from '../../services/api/services/quotation.service';\r\nimport {\r\n  GetListQuotationRequest,\r\n  GetQuotationByIdRequest,\r\n  SaveDataQuotation,\r\n  DeleteQuotationRequest,\r\n  GetListByHouseIdRequest,\r\n  LoadDefaultItemsRequest\r\n} from '../../services/api/models';\r\n\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class QuotationService {\r\n  private readonly apiUrl = '/api/Quotation';\r\n\r\n  constructor(\r\n    private apiQuotationService: ApiQuotationService,\r\n    private http: HttpClient\r\n  ) { }\r\n  // 取得報價單列表\r\n  getQuotationList(request: GetListQuotationRequest): Observable<any> {\r\n    return this.apiQuotationService.apiQuotationGetListPost$Json({ body: request });\r\n  }\r\n  // 取得單筆報價單資料\r\n  getQuotationData(quotationId: number): Observable<any> {\r\n    const request: GetQuotationByIdRequest = { cQuotationID: quotationId };\r\n    return this.apiQuotationService.apiQuotationGetDataPost$Json({ body: request });\r\n  }\r\n\r\n  // 取得戶別的報價項目\r\n  getQuotationByHouseId(houseId: number): Observable<any> {\r\n    const request: GetListByHouseIdRequest = { cHouseID: houseId };\r\n    return this.apiQuotationService.apiQuotationGetListByHouseIdPost$Json({ body: request });\r\n  }\r\n  // 儲存報價單 (支援單一項目)\r\n  saveQuotationItem(quotation: SaveDataQuotation): Observable<any> {\r\n    return this.apiQuotationService.apiQuotationSaveDataPost$Json({ body: quotation });\r\n  }\r\n  // 儲存報價單 (支援批量保存多個項目)\r\n  saveQuotation(request: { houseId: number; items: QuotationItem[] }): Observable<QuotationResponse> {\r\n    // 先取得現有的報價資料來處理版本號\r\n    return this.getQuotationByHouseId(request.houseId).pipe(\r\n      map(existingResponse => {\r\n        const existingItems = existingResponse?.StatusCode === 0 && existingResponse?.Entries\r\n          ? existingResponse.Entries : [];\r\n\r\n        // 建立現有項目的版本對照表\r\n        const existingVersionMap = new Map<number, number>();\r\n        existingItems.forEach((item: any) => {\r\n          if (item.CQuotationID || item.cQuotationID) {\r\n            const id = item.CQuotationID || item.cQuotationID;\r\n            const version = item.CVersion || item.cVersion || 1;\r\n            existingVersionMap.set(id, version);\r\n          }\r\n        });\r\n\r\n        // 準備儲存的項目，處理版本號邏輯\r\n        const saveObservables = request.items.map(item => {\r\n          let version = 1; // 新項目預設版本為1\r\n\r\n          // 如果是現有項目，版本號+1\r\n          if (item.cQuotationID && existingVersionMap.has(item.cQuotationID)) {\r\n            version = existingVersionMap.get(item.cQuotationID)! + 1;\r\n          }\r\n\r\n          const saveData: SaveDataQuotation = {\r\n            cQuotationID: item.cQuotationID || null,\r\n            cHouseID: request.houseId,\r\n            cItemName: item.cItemName,\r\n            cUnitPrice: item.cUnitPrice,\r\n            cCount: item.cCount,\r\n            cStatus: item.cStatus || 1,\r\n            cVersion: version,\r\n            cIsDeafult: item.cIsDefault || false,\r\n          };\r\n          return this.apiQuotationService.apiQuotationSaveDataPost$Json({ body: saveData });\r\n        });\r\n\r\n        return saveObservables;\r\n      }),\r\n      switchMap(saveObservables => {\r\n        // 等待所有項目保存完成\r\n        return new Observable<QuotationResponse>(observer => {\r\n          Promise.all(saveObservables.map(obs => obs.toPromise()))\r\n            .then(responses => {\r\n              // 檢查是否所有回應都成功\r\n              const allSuccess = responses.every(response => response?.StatusCode === 0);\r\n              observer.next({\r\n                success: allSuccess,\r\n                message: allSuccess ? '報價單保存成功' : '部分項目保存失敗',\r\n                data: request.items\r\n              } as QuotationResponse);\r\n              observer.complete();\r\n            })\r\n            .catch(error => {\r\n              observer.next({\r\n                success: false,\r\n                message: '報價單保存失敗',\r\n                data: []\r\n              } as QuotationResponse);\r\n              observer.complete();\r\n            });\r\n        });\r\n      })\r\n    );\r\n  }\r\n  // 刪除報價單\r\n  deleteQuotation(quotationId: number): Observable<any> {\r\n    const request: DeleteQuotationRequest = { cQuotationID: quotationId };\r\n    return this.apiQuotationService.apiQuotationDeleteDataPost$Json({ body: request });\r\n  }\r\n  // 取得預設報價項目 (保持原有方法以兼容現有代碼)\r\n  getDefaultQuotationItems(): Observable<QuotationResponse> {\r\n    // 使用 GetList 方法獲取預設項目\r\n    const request: GetListQuotationRequest = {\r\n      pageIndex: 0,\r\n      pageSize: 100,\r\n      // 其他預設參數可能需要根據實際 API 需求調整\r\n    };\r\n    return this.apiQuotationService.apiQuotationGetListPost$Json({ body: request }).pipe(\r\n      map(response => {\r\n        // 轉換 API 響應格式以保持兼容性\r\n        return {\r\n          success: response.statusCode === 0, // 假設 statusCode 0 表示成功\r\n          message: response.message || '',\r\n          data: response.entries || []\r\n        } as QuotationResponse;\r\n      })\r\n    );\r\n  }  // 載入預設報價項目 (LoadDefaultItems API)\r\n  loadDefaultItems(request: LoadDefaultItemsRequest): Observable<QuotationResponse> {\r\n    // 使用 apiQuotationService 的基礎設施發送請求\r\n    return this.apiQuotationService.http.post<any>(\r\n      `${this.apiQuotationService.rootUrl}/api/Quotation/LoadDefaultItems`, \r\n      request\r\n    ).pipe(\r\n      map(response => {\r\n        // 轉換 API 響應格式以保持兼容性\r\n        return {\r\n          success: response.StatusCode === 0, // 假設 StatusCode 0 表示成功\r\n          message: response.Message || '',\r\n          data: response.Entries || []\r\n        } as QuotationResponse;\r\n      })\r\n    );\r\n  }\r\n  // 更新報價項目 (使用 SaveData 方法)\r\n  updateQuotationItem(quotationId: number, item: QuotationItem): Observable<QuotationResponse> {\r\n    const saveData: SaveDataQuotation = {\r\n      cQuotationID: quotationId,\r\n      cHouseID: item.cHouseID,\r\n      cItemName: item.cItemName,\r\n      cUnitPrice: item.cUnitPrice,\r\n      cCount: item.cCount,\r\n      cStatus: item.cStatus || 1,\r\n      cVersion: item.cVersion || 1,\r\n      cIsDeafult: item.cIsDefault,\r\n    };\r\n    return this.apiQuotationService.apiQuotationSaveDataPost$Json({ body: saveData }).pipe(\r\n      map(response => {\r\n        return {\r\n          success: response.StatusCode === 0, // 假設 StatusCode 0 表示成功\r\n          message: response.Message || '',\r\n          data: [item] // 包裝為陣列以符合 QuotationResponse 格式\r\n        } as QuotationResponse;\r\n      })\r\n    );\r\n  }\r\n\r\n  // 刪除報價項目\r\n  deleteQuotationItem(quotationId: number): Observable<QuotationResponse> {\r\n    return this.deleteQuotation(quotationId).pipe(\r\n      map(response => {\r\n        return {\r\n          success: response.StatusCode === 0, // 假設 StatusCode 0 表示成功\r\n          message: response.Message || '',\r\n          data: []\r\n        } as QuotationResponse;\r\n      })\r\n    );\r\n  }\r\n\r\n  // 匯出報價單 (需要另外實作，因為 swagger 中沒有此 API)\r\n  exportQuotation(houseId: number): Observable<Blob> {\r\n    // 這個方法可能需要使用其他 API 或保持原有實作\r\n    // 暫時拋出錯誤提示需要實作\r\n    throw new Error('Export quotation functionality needs to be implemented separately');\r\n  }\r\n}\r\n"], "mappings": "AACA,SAASA,UAAU,QAAQ,MAAM;AACjC,SAASC,GAAG,EAAEC,SAAS,QAAQ,gBAAgB;;;;AAgB/C,OAAM,MAAOC,gBAAgB;EAG3BC,YACUC,mBAAwC,EACxCC,IAAgB;IADhB,KAAAD,mBAAmB,GAAnBA,mBAAmB;IACnB,KAAAC,IAAI,GAAJA,IAAI;IAJG,KAAAC,MAAM,GAAG,gBAAgB;EAKtC;EACJ;EACAC,gBAAgBA,CAACC,OAAgC;IAC/C,OAAO,IAAI,CAACJ,mBAAmB,CAACK,4BAA4B,CAAC;MAAEC,IAAI,EAAEF;IAAO,CAAE,CAAC;EACjF;EACA;EACAG,gBAAgBA,CAACC,WAAmB;IAClC,MAAMJ,OAAO,GAA4B;MAAEK,YAAY,EAAED;IAAW,CAAE;IACtE,OAAO,IAAI,CAACR,mBAAmB,CAACU,4BAA4B,CAAC;MAAEJ,IAAI,EAAEF;IAAO,CAAE,CAAC;EACjF;EAEA;EACAO,qBAAqBA,CAACC,OAAe;IACnC,MAAMR,OAAO,GAA4B;MAAES,QAAQ,EAAED;IAAO,CAAE;IAC9D,OAAO,IAAI,CAACZ,mBAAmB,CAACc,qCAAqC,CAAC;MAAER,IAAI,EAAEF;IAAO,CAAE,CAAC;EAC1F;EACA;EACAW,iBAAiBA,CAACC,SAA4B;IAC5C,OAAO,IAAI,CAAChB,mBAAmB,CAACiB,6BAA6B,CAAC;MAAEX,IAAI,EAAEU;IAAS,CAAE,CAAC;EACpF;EACA;EACAE,aAAaA,CAACd,OAAoD;IAChE;IACA,OAAO,IAAI,CAACO,qBAAqB,CAACP,OAAO,CAACQ,OAAO,CAAC,CAACO,IAAI,CACrDvB,GAAG,CAACwB,gBAAgB,IAAG;MACrB,MAAMC,aAAa,GAAGD,gBAAgB,EAAEE,UAAU,KAAK,CAAC,IAAIF,gBAAgB,EAAEG,OAAO,GACjFH,gBAAgB,CAACG,OAAO,GAAG,EAAE;MAEjC;MACA,MAAMC,kBAAkB,GAAG,IAAIC,GAAG,EAAkB;MACpDJ,aAAa,CAACK,OAAO,CAAEC,IAAS,IAAI;QAClC,IAAIA,IAAI,CAACC,YAAY,IAAID,IAAI,CAAClB,YAAY,EAAE;UAC1C,MAAMoB,EAAE,GAAGF,IAAI,CAACC,YAAY,IAAID,IAAI,CAAClB,YAAY;UACjD,MAAMqB,OAAO,GAAGH,IAAI,CAACI,QAAQ,IAAIJ,IAAI,CAACK,QAAQ,IAAI,CAAC;UACnDR,kBAAkB,CAACS,GAAG,CAACJ,EAAE,EAAEC,OAAO,CAAC;QACrC;MACF,CAAC,CAAC;MAEF;MACA,MAAMI,eAAe,GAAG9B,OAAO,CAAC+B,KAAK,CAACvC,GAAG,CAAC+B,IAAI,IAAG;QAC/C,IAAIG,OAAO,GAAG,CAAC,CAAC,CAAC;QAEjB;QACA,IAAIH,IAAI,CAAClB,YAAY,IAAIe,kBAAkB,CAACY,GAAG,CAACT,IAAI,CAAClB,YAAY,CAAC,EAAE;UAClEqB,OAAO,GAAGN,kBAAkB,CAACa,GAAG,CAACV,IAAI,CAAClB,YAAY,CAAE,GAAG,CAAC;QAC1D;QAEA,MAAM6B,QAAQ,GAAsB;UAClC7B,YAAY,EAAEkB,IAAI,CAAClB,YAAY,IAAI,IAAI;UACvCI,QAAQ,EAAET,OAAO,CAACQ,OAAO;UACzB2B,SAAS,EAAEZ,IAAI,CAACY,SAAS;UACzBC,UAAU,EAAEb,IAAI,CAACa,UAAU;UAC3BC,MAAM,EAAEd,IAAI,CAACc,MAAM;UACnBC,OAAO,EAAEf,IAAI,CAACe,OAAO,IAAI,CAAC;UAC1BV,QAAQ,EAAEF,OAAO;UACjBa,UAAU,EAAEhB,IAAI,CAACiB,UAAU,IAAI;SAChC;QACD,OAAO,IAAI,CAAC5C,mBAAmB,CAACiB,6BAA6B,CAAC;UAAEX,IAAI,EAAEgC;QAAQ,CAAE,CAAC;MACnF,CAAC,CAAC;MAEF,OAAOJ,eAAe;IACxB,CAAC,CAAC,EACFrC,SAAS,CAACqC,eAAe,IAAG;MAC1B;MACA,OAAO,IAAIvC,UAAU,CAAoBkD,QAAQ,IAAG;QAClDC,OAAO,CAACC,GAAG,CAACb,eAAe,CAACtC,GAAG,CAACoD,GAAG,IAAIA,GAAG,CAACC,SAAS,EAAE,CAAC,CAAC,CACrDC,IAAI,CAACC,SAAS,IAAG;UAChB;UACA,MAAMC,UAAU,GAAGD,SAAS,CAACE,KAAK,CAACC,QAAQ,IAAIA,QAAQ,EAAEhC,UAAU,KAAK,CAAC,CAAC;UAC1EuB,QAAQ,CAACU,IAAI,CAAC;YACZC,OAAO,EAAEJ,UAAU;YACnBK,OAAO,EAAEL,UAAU,GAAG,SAAS,GAAG,UAAU;YAC5CM,IAAI,EAAEtD,OAAO,CAAC+B;WACM,CAAC;UACvBU,QAAQ,CAACc,QAAQ,EAAE;QACrB,CAAC,CAAC,CACDC,KAAK,CAACC,KAAK,IAAG;UACbhB,QAAQ,CAACU,IAAI,CAAC;YACZC,OAAO,EAAE,KAAK;YACdC,OAAO,EAAE,SAAS;YAClBC,IAAI,EAAE;WACc,CAAC;UACvBb,QAAQ,CAACc,QAAQ,EAAE;QACrB,CAAC,CAAC;MACN,CAAC,CAAC;IACJ,CAAC,CAAC,CACH;EACH;EACA;EACAG,eAAeA,CAACtD,WAAmB;IACjC,MAAMJ,OAAO,GAA2B;MAAEK,YAAY,EAAED;IAAW,CAAE;IACrE,OAAO,IAAI,CAACR,mBAAmB,CAAC+D,+BAA+B,CAAC;MAAEzD,IAAI,EAAEF;IAAO,CAAE,CAAC;EACpF;EACA;EACA4D,wBAAwBA,CAAA;IACtB;IACA,MAAM5D,OAAO,GAA4B;MACvC6D,SAAS,EAAE,CAAC;MACZC,QAAQ,EAAE;MACV;KACD;IACD,OAAO,IAAI,CAAClE,mBAAmB,CAACK,4BAA4B,CAAC;MAAEC,IAAI,EAAEF;IAAO,CAAE,CAAC,CAACe,IAAI,CAClFvB,GAAG,CAAC0D,QAAQ,IAAG;MACb;MACA,OAAO;QACLE,OAAO,EAAEF,QAAQ,CAACa,UAAU,KAAK,CAAC;QAAE;QACpCV,OAAO,EAAEH,QAAQ,CAACG,OAAO,IAAI,EAAE;QAC/BC,IAAI,EAAEJ,QAAQ,CAACc,OAAO,IAAI;OACN;IACxB,CAAC,CAAC,CACH;EACH,CAAC,CAAE;EACHC,gBAAgBA,CAACjE,OAAgC;IAC/C;IACA,OAAO,IAAI,CAACJ,mBAAmB,CAACC,IAAI,CAACqE,IAAI,CACvC,GAAG,IAAI,CAACtE,mBAAmB,CAACuE,OAAO,iCAAiC,EACpEnE,OAAO,CACR,CAACe,IAAI,CACJvB,GAAG,CAAC0D,QAAQ,IAAG;MACb;MACA,OAAO;QACLE,OAAO,EAAEF,QAAQ,CAAChC,UAAU,KAAK,CAAC;QAAE;QACpCmC,OAAO,EAAEH,QAAQ,CAACkB,OAAO,IAAI,EAAE;QAC/Bd,IAAI,EAAEJ,QAAQ,CAAC/B,OAAO,IAAI;OACN;IACxB,CAAC,CAAC,CACH;EACH;EACA;EACAkD,mBAAmBA,CAACjE,WAAmB,EAAEmB,IAAmB;IAC1D,MAAMW,QAAQ,GAAsB;MAClC7B,YAAY,EAAED,WAAW;MACzBK,QAAQ,EAAEc,IAAI,CAACd,QAAQ;MACvB0B,SAAS,EAAEZ,IAAI,CAACY,SAAS;MACzBC,UAAU,EAAEb,IAAI,CAACa,UAAU;MAC3BC,MAAM,EAAEd,IAAI,CAACc,MAAM;MACnBC,OAAO,EAAEf,IAAI,CAACe,OAAO,IAAI,CAAC;MAC1BV,QAAQ,EAAEL,IAAI,CAACK,QAAQ,IAAI,CAAC;MAC5BW,UAAU,EAAEhB,IAAI,CAACiB;KAClB;IACD,OAAO,IAAI,CAAC5C,mBAAmB,CAACiB,6BAA6B,CAAC;MAAEX,IAAI,EAAEgC;IAAQ,CAAE,CAAC,CAACnB,IAAI,CACpFvB,GAAG,CAAC0D,QAAQ,IAAG;MACb,OAAO;QACLE,OAAO,EAAEF,QAAQ,CAAChC,UAAU,KAAK,CAAC;QAAE;QACpCmC,OAAO,EAAEH,QAAQ,CAACkB,OAAO,IAAI,EAAE;QAC/Bd,IAAI,EAAE,CAAC/B,IAAI,CAAC,CAAC;OACO;IACxB,CAAC,CAAC,CACH;EACH;EAEA;EACA+C,mBAAmBA,CAAClE,WAAmB;IACrC,OAAO,IAAI,CAACsD,eAAe,CAACtD,WAAW,CAAC,CAACW,IAAI,CAC3CvB,GAAG,CAAC0D,QAAQ,IAAG;MACb,OAAO;QACLE,OAAO,EAAEF,QAAQ,CAAChC,UAAU,KAAK,CAAC;QAAE;QACpCmC,OAAO,EAAEH,QAAQ,CAACkB,OAAO,IAAI,EAAE;QAC/Bd,IAAI,EAAE;OACc;IACxB,CAAC,CAAC,CACH;EACH;EAEA;EACAiB,eAAeA,CAAC/D,OAAe;IAC7B;IACA;IACA,MAAM,IAAIgE,KAAK,CAAC,mEAAmE,CAAC;EACtF;;;uCA/KW9E,gBAAgB,EAAA+E,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAjF,gBAAA,GAAA+E,EAAA,CAAAC,QAAA,CAAAE,EAAA,CAAAC,UAAA;IAAA;EAAA;;;aAAhBnF,gBAAgB;MAAAoF,OAAA,EAAhBpF,gBAAgB,CAAAqF,IAAA;MAAAC,UAAA,EAFf;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}