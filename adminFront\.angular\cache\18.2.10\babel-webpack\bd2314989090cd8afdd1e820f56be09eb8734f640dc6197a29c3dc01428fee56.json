{"ast": null, "code": "/* tslint:disable */\n/* eslint-disable */\nimport { HttpResponse } from '@angular/common/http';\nimport { filter, map } from 'rxjs/operators';\nimport { RequestBuilder } from '../../request-builder';\nexport function apiHouseExportHousePost$Plain(http, rootUrl, params, context) {\n  const rb = new RequestBuilder(rootUrl, apiHouseExportHousePost$Plain.PATH, 'post');\n  if (params) {\n    rb.query('CBuildCaseID', params.CBuildCaseID, {});\n  }\n  return http.request(rb.build({\n    responseType: 'text',\n    accept: 'text/plain',\n    context\n  })).pipe(filter(r => r instanceof HttpResponse), map(r => {\n    return r;\n  }));\n}\napiHouseExportHousePost$Plain.PATH = '/api/House/ExportHouse';", "map": {"version": 3, "names": ["HttpResponse", "filter", "map", "RequestBuilder", "apiHouseExportHousePost$Plain", "http", "rootUrl", "params", "context", "rb", "PATH", "query", "CBuildCaseID", "request", "build", "responseType", "accept", "pipe", "r"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\services\\api\\fn\\house\\api-house-export-house-post-plain.ts"], "sourcesContent": ["/* tslint:disable */\r\n/* eslint-disable */\r\nimport { HttpClient, HttpContext, HttpResponse } from '@angular/common/http';\r\nimport { Observable } from 'rxjs';\r\nimport { filter, map } from 'rxjs/operators';\r\nimport { StrictHttpResponse } from '../../strict-http-response';\r\nimport { RequestBuilder } from '../../request-builder';\r\n\r\nimport { ByteArrayResponseBase } from '../../models/byte-array-response-base';\r\n\r\nexport interface ApiHouseExportHousePost$Plain$Params {\r\n  CBuildCaseID?: number;\r\n}\r\n\r\nexport function apiHouseExportHousePost$Plain(http: HttpClient, rootUrl: string, params?: ApiHouseExportHousePost$Plain$Params, context?: HttpContext): Observable<StrictHttpResponse<ByteArrayResponseBase>> {\r\n  const rb = new RequestBuilder(rootUrl, apiHouseExportHousePost$Plain.PATH, 'post');\r\n  if (params) {\r\n    rb.query('CBuildCaseID', params.CBuildCaseID, {});\r\n  }\r\n\r\n  return http.request(\r\n    rb.build({ responseType: 'text', accept: 'text/plain', context })\r\n  ).pipe(\r\n    filter((r: any): r is HttpResponse<any> => r instanceof HttpResponse),\r\n    map((r: HttpResponse<any>) => {\r\n      return r as StrictHttpResponse<ByteArrayResponseBase>;\r\n    })\r\n  );\r\n}\r\n\r\napiHouseExportHousePost$Plain.PATH = '/api/House/ExportHouse';\r\n"], "mappings": "AAAA;AACA;AACA,SAAkCA,YAAY,QAAQ,sBAAsB;AAE5E,SAASC,MAAM,EAAEC,GAAG,QAAQ,gBAAgB;AAE5C,SAASC,cAAc,QAAQ,uBAAuB;AAQtD,OAAM,SAAUC,6BAA6BA,CAACC,IAAgB,EAAEC,OAAe,EAAEC,MAA6C,EAAEC,OAAqB;EACnJ,MAAMC,EAAE,GAAG,IAAIN,cAAc,CAACG,OAAO,EAAEF,6BAA6B,CAACM,IAAI,EAAE,MAAM,CAAC;EAClF,IAAIH,MAAM,EAAE;IACVE,EAAE,CAACE,KAAK,CAAC,cAAc,EAAEJ,MAAM,CAACK,YAAY,EAAE,EAAE,CAAC;EACnD;EAEA,OAAOP,IAAI,CAACQ,OAAO,CACjBJ,EAAE,CAACK,KAAK,CAAC;IAAEC,YAAY,EAAE,MAAM;IAAEC,MAAM,EAAE,YAAY;IAAER;EAAO,CAAE,CAAC,CAClE,CAACS,IAAI,CACJhB,MAAM,CAAEiB,CAAM,IAA6BA,CAAC,YAAYlB,YAAY,CAAC,EACrEE,GAAG,CAAEgB,CAAoB,IAAI;IAC3B,OAAOA,CAA8C;EACvD,CAAC,CAAC,CACH;AACH;AAEAd,6BAA6B,CAACM,IAAI,GAAG,wBAAwB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}