import { Component, ElementRef, OnInit, ViewChild } from '@angular/core';
import { CommonModule, DatePipe } from '@angular/common';
import { NbDialogService } from '@nebular/theme';
import { MessageService } from 'src/app/shared/services/message.service';
import { ValidationHelper } from 'src/app/shared/helper/validationHelper';
import { BuildCaseService, HouseService, RegularNoticeFileService, SpecialNoticeFileService } from 'src/services/api/services';
import { LabelInOptionsPipe, TypeMailPipe } from 'src/app/@theme/pipes/mapping.pipe';
import { AllowHelper } from 'src/app/shared/helper/allowHelper';
import { GetHouseListRes, GetListHouseHoldRes, GetSpecialNoticeFileListRes, HouseSpecialNoticeFile, TblExamineLog } from 'src/services/api/models';
import { tap } from 'rxjs';
import { SharedModule } from '../../components/shared.module';
import { BaseComponent } from '../../components/base/baseComponent';
import { EnumFileType } from 'src/app/shared/enum/enumFileType';
import { NoticeServiceCustom } from 'src/app/@core/service/notice.service';
import { UtilityService } from 'src/app/shared/services/utility.service';
import { environment } from 'src/environments/environment';
import { DateFormatHourPipe } from 'src/app/@theme/pipes';

export interface HouseList {
  CFloor?: number | null;
  CHouseHold?: string | null;
  CHouseID?: number;
  CID?: number;
  CIsSelect?: boolean | null;
  CHouseType?: number | null;
  CIsEnable?: boolean | null;
}
export interface SaveSpecialNoticeFileCus {
  CFileUrl?: string
  CNoticeType?: number
  CBuildCaseId?: number
  CFile?: Blob
  CHouse?: Array<string>
  CSpecialNoticeFileId?: number
  CIsSelectAll?: boolean
  selectedCNoticeType?: any
  CExamineNote?: string | null;
  tblExamineLogs?: TblExamineLog[],
  tblSpecialNoticeFileHouses: any
  CExamineStauts?: number;
}

@Component({
  selector: 'ngx-notice-management',
  templateUrl: './notice-management.component.html',
  styleUrls: ['./notice-management.component.scss'],
  standalone: true,
  imports: [CommonModule, SharedModule, TypeMailPipe, DatePipe, LabelInOptionsPipe, DateFormatHourPipe],
})

export class NoticeManagementComponent extends BaseComponent implements OnInit {
  constructor(
    private _allow: AllowHelper,
    private dialogService: NbDialogService,
    private message: MessageService,
    private valid: ValidationHelper,
    private _utilityService: UtilityService,
    private _buildCaseService: BuildCaseService,
    private _specialNoticeFileService: SpecialNoticeFileService,
    private _regularNoticeFileService: RegularNoticeFileService,
    private _houseService: HouseService,
    private _specialChangeCustomService: NoticeServiceCustom
  ) { super(_allow) }

  override ngOnInit(): void {
    this.getUserBuildCase()
  }

  selectedCBuildCase: any

  override pageFirst = 1;
  override pageSize = 10;
  override pageIndex = 1;
  override totalRecords = 0;
  pageFirstSales = 1;
  pageSizeSales = 10;
  pageIndexSales = 1;
  totalRecordsSales = 0;


  saveSpecialNoticeFile: SaveSpecialNoticeFileCus

  buildCaseOptions: any[] = [{ label: '全部', value: '' }]

  cNoticeTypeOptions: any[] = [
    { label: '地主戶', value: 1 },
    { label: '銷售戶', value: 2 }
  ]
  seletectedNoticeType: any

  typeContentManagementLandowner = {
    CFormType: 1,
    CNoticeType: 1
  }

  houseList2D: HouseList[][]
  userBuildCaseOptions: any

  pageChanged(newPage: number) {
    this.pageIndex = newPage;
  }

  pageChangedSales(newPage: number) {
    this.pageIndexSales = newPage;
  }

  @ViewChild('fileInput') fileInput!: ElementRef;
  fileName: string | null = null;
  imageUrl: any = undefined;


  onFileSelected(event: any) {
    const file: File = event.target.files[0];
    const fileRegex = /pdf|jpg|jpeg|png/i;
    if (!fileRegex.test(file.type)) {
      this.message.showErrorMSG('檔案格式錯誤，僅限pdf');
      return;
    }
    if (file) {
      const allowedTypes = ['application/pdf'];
      if (allowedTypes.includes(file.type)) {
        this.fileName = file.name;
        const reader = new FileReader();
        reader.onload = (e: any) => {
          this.imageUrl = {
            CName: file.name,
            CFile: e.target?.result?.toString().split(',')[1],
            Cimg: file.name.includes('pdf') ? file : file,
            CFileUpload: file,
            CFileType: EnumFileType.PDF,
          };
          if (this.fileInput) {
            this.fileInput.nativeElement.value = null;
          }
        };
        reader.readAsDataURL(file);
      }
    }
  }


  clearImage() {
    if (this.imageUrl) {
      this.imageUrl = null;
      this.fileName = null;
      if (this.fileInput) {
        this.fileInput.nativeElement.value = null; // Xóa giá trị input file
      }
    }
  }
  userBuildCaseSelected: any

  onChangeBuildCase() {
    if (this.selectedCBuildCase.value) {
      this.getSpecialNoticeFileHouseHoldList()
      this.getSpecialNoticeFileList()
    }
  }

  getUserBuildCase() {
    this._buildCaseService.apiBuildCaseGetUserBuildCasePost$Json({ body: {} }).pipe(
      tap(res => {
        if (res.Entries && res.StatusCode == 0) {
          this.userBuildCaseOptions = res.Entries.map(res => {
            return {
              label: res.CBuildCaseName,
              value: res.cID
            }
          })
          this.selectedCBuildCase = this.userBuildCaseOptions[0]
          if (this.selectedCBuildCase.value) {
            this.getSpecialNoticeFileHouseHoldList()
            this.getSpecialNoticeFileList()
          }
        }
      }),
    ).subscribe()
  }

  groupByFloor(customerData: HouseList[]): HouseList[][] {

    const groupedData: HouseList[][] = [];
    const uniqueFloors = Array.from(new Set(
      customerData.map(customer => customer.CFloor).filter(floor => floor !== null) as number[]
    ));
    for (const floor of uniqueFloors) {
      groupedData.push([]);
    }
    for (const customer of customerData) {
      const floorIndex = uniqueFloors.indexOf(customer.CFloor as number);
      if (floorIndex !== -1) {
        groupedData[floorIndex].push({
          CIsSelect: customer?.CIsSelect || false,
          CHouseID: customer.CID,
          CHouseType: customer.CHouseType,
          CFloor: customer.CFloor,
          CHouseHold: customer.CHouseHold,
          CIsEnable: customer.CIsEnable,
        });
      }
    }
    return groupedData;
  }

  isHouseList = false
  houseListEnable: GetHouseListRes[]
  sortByFloorDescending(arr: GetHouseListRes[]): GetHouseListRes[] {
    return arr.sort((a, b) => (b.CFloor ?? 0) - (a.CFloor ?? 0));
  }

  getHouseList() {
    if (this.selectedCBuildCase.value) {
      this._houseService.apiHouseGetHouseListPost$Json({
        body: {
          CBuildCaseID: this.selectedCBuildCase.value, CIsPagi: false
        }
      }).subscribe(res => {
        if (res.Entries && res.StatusCode == 0) {
          const rest = this.sortByFloorDescending(res.Entries)
          this.houseListEnable = [...rest]
          if (this.saveSpecialNoticeFile.CSpecialNoticeFileId) {
            this.houseList2D = this.groupByFloor(this.addCIsSelectToA([...this.houseListEnable], this.saveSpecialNoticeFile.tblSpecialNoticeFileHouses ? [...this.saveSpecialNoticeFile.tblSpecialNoticeFileHouses] : []))
          } else {
            this.houseList2D = this.groupByFloor([...rest])
          }
          this.isHouseList = true;
          if (this.saveSpecialNoticeFile) {
            this.houseList2D = this.updateCIsClick(this.houseList2D, this.saveSpecialNoticeFile.tblSpecialNoticeFileHouses)
          }
        }
      })
    }
  }

  isCheckAllRowChecked(row: any[]): boolean {
    return row.every((item: { CIsSelect: any; }) => item.CIsSelect);
  }

  isCheckAllColumnChecked(index: number): boolean {
    if (this.isHouseList) {
      if (index < 0 || index >= this.houseList2D[0].length) {
        throw new Error("Invalid index. Index must be within the bounds of the array.");
      }
      for (const floorData of this.houseList2D) {
        if (index >= floorData.length || !floorData[index].CIsSelect) {
          return false; // Found a customer with CIsEnable not true (or missing)
        }
      }
      return true; // All customers at the given index have CIsEnable as true
    }
    return false
  }

  enableAllAtIndex(checked: boolean, index: number): void {
    if (index < 0) {
      throw new Error("Invalid index. Index must be a non-negative number.");
    }
    for (const floorData of this.houseList2D) {
      if (index < floorData.length && (this.saveSpecialNoticeFile.selectedCNoticeType.value === floorData[index].CHouseType)) { // Check if index is valid for this floor
        floorData[index].CIsSelect = checked;
      }
    }
  }

  enableAllRow(checked: boolean, row: HouseList[]) {
    for (const item of row) {
      if ((this.saveSpecialNoticeFile.selectedCNoticeType.value === item.CHouseType)) { // Check if index is valid for this floor
        item.CIsSelect = checked;
      }
    }
  }


  listSpecialNoticeFile: GetSpecialNoticeFileListRes

  getSpecialNoticeFileList() {
    this._specialNoticeFileService.apiSpecialNoticeFileGetSpecialNoticeFileListPost$Json({
      body: {
        CBuildCaseId: this.selectedCBuildCase.value,
        PageIndexLandLord: this.pageIndex,
        PageIndexSales: this.pageIndexSales,
        PageSizeLandLord: this.pageSize,
        PageSizeSales: this.pageSizeSales,
      }
    }).subscribe(res => {
      if (res.Entries && res.StatusCode == 0) {
        this.listSpecialNoticeFile = res.Entries
        this.totalRecords = res.Entries.TotalListLandLords || 0
        this.totalRecordsSales = res.Entries.TotalListSales || 0
      }
    })
  }
  listSpecialNoticeFileHouseHold: GetListHouseHoldRes[]

  houseHoldList: string[]

  onStatusChange(newStatus: any) {
  }

  getSpecialNoticeFileHouseHoldList() {
    this._regularNoticeFileService.apiRegularNoticeFileGetListRegularNoticeFileHouseHoldPost$Json({
      body: this.selectedCBuildCase.value
    }).subscribe(res => {
      if (res.Entries && res.StatusCode == 0) {
        this.listSpecialNoticeFileHouseHold = res.Entries
      }
    })
  }

  createArrayObjectFromArray(a: string[], b: { CSpecialNoticeFileHouseholdId: number, CSpecialNoticeFileId: number, CHousehold: string, CIsSelected: boolean }[] | any[]): { [key: string]: boolean } {
    const c: { [key: string]: boolean } = {};
    for (const item of a) {
      const matchingItem = b.find(bItem => bItem.CHousehold === item && bItem.CIsSelected);
      c[item] = !!matchingItem;
    }
    return c;
  }


  getItemByValue(value: any, options: any[]) {
    for (const item of options) {
      if (item.value === value) {
        return item;
      }
    }
    return null;
  }

  cExamineStatusOption = ['待審核', '已通過', '已駁回']

  updateCIsEnable(houseList2D: HouseList[][], houseSpecialNoticeFile: HouseSpecialNoticeFile[]): HouseList[][] {
    const selectedHouses = new Set(houseSpecialNoticeFile.map(item => `${item.CHouseHold}-${item.CFloor}`));
    return houseList2D.map(floorArray => {
      return floorArray.map(item => {
        const key = `${item.CHouseHold}-${item.CFloor}`;
        if (selectedHouses.has(key)) {
          item.CIsSelect = true;
        } else {
          item.CIsSelect = false;
        }
        return item;
      });
    });
  }

  addCIsSelectToA(A: any[], B: any[]): any[] {
    const mapB = new Map(B.map(item => [`${item.CHouseHold}-${item.CFloor}`, item.CIsSelect]));
    return A.map(item => {
      const key = `${item.CHouseHold}-${item.CFloor}`;
      return {
        ...item,
        CIsSelect: mapB.has(key) ? mapB.get(key) : false
      };
    });
  }


  getSpecialNoticeFileById(item: any, ref: any) {
    this._specialNoticeFileService.apiSpecialNoticeFileGetSpecialNoticeFileByIdPost$Json({
      body: item.CSpecialNoticeFileId
    }).subscribe(res => {
      if (res.Entries && res.StatusCode == 0) {
        const data = res.Entries
        this.saveSpecialNoticeFile = {
          CFileUrl: data.tblSpecialNoticeFile?.CFileUrl || undefined,
          CNoticeType: data.tblSpecialNoticeFile?.CNoticeType,
          CBuildCaseId: data.tblSpecialNoticeFile?.CBuildCaseId,
          CSpecialNoticeFileId: data.tblSpecialNoticeFile?.CSpecialNoticeFileId,
          selectedCNoticeType: data.tblSpecialNoticeFile?.CNoticeType ? this.getItemByValue(data.tblSpecialNoticeFile?.CNoticeType, this.cNoticeTypeOptions) : this.cNoticeTypeOptions[0],
          tblExamineLogs: data.tblExamineLogs ? data.tblExamineLogs : undefined,
          CExamineNote: data.CExamineNote,
          tblSpecialNoticeFileHouses: data.tblSpecialNoticeFileHouses?.filter((i: any) => i.CIsSelect),
          CExamineStauts: data.CExamineStauts
        }
        this.getHouseList()
        this.dialogService.open(ref)
      }
    })
  }

  isNew = true

  openPdfInNewTab(CFileUrl?: any) {
    if (CFileUrl) window.open(environment.BASE_WITHOUT_FILEROOT + CFileUrl, '_blank');
  }

  openModel(ref: any, item?: any) {
    this.isHouseList = false
    this.isNew = true
    this.clearImage()
    this.saveSpecialNoticeFile = {
      CNoticeType: 1,
      CBuildCaseId: undefined,
      CFile: undefined,
      CHouse: [],
      CSpecialNoticeFileId: undefined,
      CIsSelectAll: false,
      selectedCNoticeType: this.cNoticeTypeOptions[0],
      CExamineNote: '',
      tblSpecialNoticeFileHouses: undefined
    }

    if (item) {
      this.isNew = false
      this.getSpecialNoticeFileById(item, ref)
    } else {
      this.isNew = true
      this.getHouseList()
      this.dialogService.open(ref)
    }
  }

  removeBase64Prefix(base64String: any) {
    const prefixIndex = base64String.indexOf(",");
    if (prefixIndex !== -1) {
      return base64String.substring(prefixIndex + 1);
    }
    return base64String;
  }

  onDelete(item: any) {
    if (window.confirm(`確定要刪除【項目${item.CSpecialNoticeFileId}】?`)) {
      this._specialNoticeFileService.apiSpecialNoticeFileDeleteSpecialNoticeFilePost$Json({
        body: item.CSpecialNoticeFileId
      }).subscribe(res => {
        if (res.StatusCode === 0) {
          this.message.showSucessMSG("執行成功");
          this.onChangeBuildCase()
        }
      })
    }
    return item
  }

  getTrueKeys(inputDict: { [key: string]: boolean }): string[] {
    const trueKeys: string[] = [];
    for (const key in inputDict) {
      if (inputDict[key]) {
        trueKeys.push(key);
      }
    }
    return trueKeys;
  }

  flattenAndFilter(data: any[][]): HouseSpecialNoticeFile[] {
    const flattened: HouseSpecialNoticeFile[] = [];
    for (const floorData of data) {
      for (const house of floorData) {
        if (house.CIsSelect && house.CIsEnable && house.CHouseType === this.saveSpecialNoticeFile.selectedCNoticeType.value) {
          flattened.push({
            CHouseID: house.CHouseID,
            CIsSelect: house.CIsSelect,
          });
        }
      }
    }
    return flattened;
  }

  onSaveSpecialNoticeFile(ref: any) {
    const param = {
      CNoticeType: this.saveSpecialNoticeFile.selectedCNoticeType.value,
      CBuildCaseId: this.selectedCBuildCase.value,
      CFile: this.imageUrl ? this.imageUrl.CFileUpload : undefined,
      CHouse: this.flattenAndFilter(this.houseList2D),
      CSpecialNoticeFileId: this.saveSpecialNoticeFile.CSpecialNoticeFileId || undefined,
      CIsSelectAll: this.saveSpecialNoticeFile.CIsSelectAll ?? false,
      CExamineNote: this.saveSpecialNoticeFile.CExamineNote
    }

    this.validation(param.CHouse)

    if (this.valid.errorMessages.length > 0) {
      this.message.showErrorMSGs(this.valid.errorMessages);
      return
    }

    this._specialChangeCustomService.SaveSpecialNoticeFile(param).subscribe(res => {
      if (res && res.body! && res.body.StatusCode! === 0) {
        this.message.showSucessMSG("執行成功");
        this.clearImage()
        this.getSpecialNoticeFileList()
        ref.close();
      } else {
        this.message.showErrorMSG(res && res.body && res.body.Message!);
      }
    })
  }


  onClose(ref: any) {
    ref.close();
  }


  validation(CHouse: any[]) {
    this.valid.clear();
    if (this.isNew && !this.imageUrl) {
      this.valid.required('[檔案]', '')
    }
    if (!(CHouse.length > 0)) {
      this.valid.required('[適用戶別]', '')
    }
    this.valid.required('[送審說明]', this.saveSpecialNoticeFile.CExamineNote)
  }

  getActionName(actionID: number | undefined) {
    let textR = "";
    if (actionID != undefined) {
      switch (actionID) {
        case 1:
          textR = "傳送";
          break;
        case 2:
          textR = "通過";
          break;
        case 3:
          textR = "駁回";
          break;
        default:
          break;
      }
    }
    return textR;
  }


  updateCIsClick(houseList2D: HouseList[][], houseSpecialNoticeFile: HouseSpecialNoticeFile[]): HouseList[][] {
    const selectedHouses = houseSpecialNoticeFile.map(item => item.CHouseID);
    return houseList2D.map(floorArray => {
      return floorArray.map(item => {
        if (selectedHouses.includes(item.CHouseID)) {
          item.CIsSelect = true;
        } else {
          item.CIsSelect = false;
        }
        return item;
      });
    });
  }
}

