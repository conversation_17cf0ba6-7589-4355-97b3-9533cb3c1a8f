{"ast": null, "code": "import { OrdersChartData } from '../data/orders-chart';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"./periods.service\";\nexport class OrdersChartService extends OrdersChartData {\n  constructor(period) {\n    super();\n    this.period = period;\n    this.year = ['2012', '2013', '2014', '2015', '2016', '2017', '2018'];\n    this.data = {};\n    this.data = {\n      week: this.getDataForWeekPeriod(),\n      month: this.getDataForMonthPeriod(),\n      year: this.getDataForYearPeriod()\n    };\n  }\n  getDataForWeekPeriod() {\n    return {\n      chartLabel: this.getDataLabels(42, this.period.getWeeks()),\n      linesData: [[184, 267, 326, 366, 389, 399, 392, 371, 340, 304, 265, 227, 191, 158, 130, 108, 95, 91, 97, 109, 125, 144, 166, 189, 212, 236, 259, 280, 300, 316, 329, 338, 342, 339, 329, 312, 288, 258, 221, 178, 128, 71], [158, 178, 193, 205, 212, 213, 204, 190, 180, 173, 168, 164, 162, 160, 159, 158, 159, 166, 179, 195, 215, 236, 257, 276, 292, 301, 304, 303, 300, 293, 284, 273, 262, 251, 241, 234, 232, 232, 232, 232, 232, 232], [58, 137, 202, 251, 288, 312, 323, 324, 311, 288, 257, 222, 187, 154, 124, 100, 81, 68, 61, 58, 61, 69, 80, 96, 115, 137, 161, 186, 210, 233, 254, 271, 284, 293, 297, 297, 297, 297, 297, 297, 297, 297, 297]]\n    };\n  }\n  getDataForMonthPeriod() {\n    return {\n      chartLabel: this.getDataLabels(47, this.period.getMonths()),\n      linesData: [[5, 63, 113, 156, 194, 225, 250, 270, 283, 289, 290, 286, 277, 264, 244, 220, 194, 171, 157, 151, 150, 152, 155, 160, 166, 170, 167, 153, 135, 115, 97, 82, 71, 64, 63, 62, 61, 62, 65, 73, 84, 102, 127, 159, 203, 259, 333], [6, 83, 148, 200, 240, 265, 273, 259, 211, 122, 55, 30, 28, 36, 50, 68, 88, 109, 129, 146, 158, 163, 165, 173, 187, 208, 236, 271, 310, 346, 375, 393, 400, 398, 387, 368, 341, 309, 275, 243, 220, 206, 202, 207, 222, 247, 286, 348], [398, 348, 315, 292, 274, 261, 251, 243, 237, 231, 222, 209, 192, 172, 152, 132, 116, 102, 90, 80, 71, 64, 58, 53, 49, 48, 54, 66, 84, 104, 125, 142, 156, 166, 172, 174, 172, 167, 159, 149, 136, 121, 105, 86, 67, 45, 22]]\n    };\n  }\n  getDataForYearPeriod() {\n    return {\n      chartLabel: this.getDataLabels(42, this.year),\n      linesData: [[190, 269, 327, 366, 389, 398, 396, 387, 375, 359, 343, 327, 312, 298, 286, 276, 270, 268, 265, 258, 247, 234, 220, 204, 188, 172, 157, 142, 128, 116, 106, 99, 95, 94, 92, 89, 84, 77, 69, 60, 49, 36, 22], [265, 307, 337, 359, 375, 386, 393, 397, 399, 397, 390, 379, 365, 347, 326, 305, 282, 261, 241, 223, 208, 197, 190, 187, 185, 181, 172, 160, 145, 126, 105, 82, 60, 40, 26, 19, 22, 43, 82, 141, 220, 321], [9, 165, 236, 258, 244, 206, 186, 189, 209, 239, 273, 307, 339, 365, 385, 396, 398, 385, 351, 300, 255, 221, 197, 181, 170, 164, 162, 161, 159, 154, 146, 135, 122, 108, 96, 87, 83, 82, 82, 82, 82, 82, 82]]\n    };\n  }\n  getDataLabels(nPoints, labelsArray) {\n    const labelsArrayLength = labelsArray.length;\n    const step = Math.round(nPoints / labelsArrayLength);\n    return Array.from(Array(nPoints)).map((item, index) => {\n      const dataIndex = Math.round(index / step);\n      return index % step === 0 ? labelsArray[dataIndex] : '';\n    });\n  }\n  getOrdersChartData(period) {\n    return this.data[period];\n  }\n  static {\n    this.ɵfac = function OrdersChartService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || OrdersChartService)(i0.ɵɵinject(i1.PeriodsService));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: OrdersChartService,\n      factory: OrdersChartService.ɵfac\n    });\n  }\n}", "map": {"version": 3, "names": ["OrdersChartData", "OrdersChartService", "constructor", "period", "year", "data", "week", "getDataForWeekPeriod", "month", "getDataForMonthPeriod", "getDataForYearPeriod", "chartLabel", "getDataLabels", "getWeeks", "linesData", "getMonths", "nPoints", "labelsArray", "labelsArrayLength", "length", "step", "Math", "round", "Array", "from", "map", "item", "index", "dataIndex", "getOrdersChartData", "i0", "ɵɵinject", "i1", "PeriodsService", "factory", "ɵfac"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\@core\\mock\\orders-chart.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport { PeriodsService } from './periods.service';\r\nimport { OrdersChart, OrdersChartData } from '../data/orders-chart';\r\n\r\n@Injectable()\r\nexport class OrdersChartService extends OrdersChartData {\r\n\r\n  private year = [\r\n    '2012',\r\n    '2013',\r\n    '2014',\r\n    '2015',\r\n    '2016',\r\n    '2017',\r\n    '2018',\r\n  ];\r\n\r\n  private data:any = { };\r\n\r\n  constructor(private period: PeriodsService) {\r\n    super();\r\n    this.data = {\r\n      week: this.getDataForWeekPeriod(),\r\n      month: this.getDataForMonthPeriod(),\r\n      year: this.getDataForYearPeriod(),\r\n    };\r\n  }\r\n\r\n  private getDataForWeekPeriod(): OrdersChart {\r\n    return {\r\n      chartLabel: this.getDataLabels(42, this.period.getWeeks()),\r\n      linesData: [\r\n        [\r\n          184, 267, 326, 366, 389, 399,\r\n          392, 371, 340, 304, 265, 227,\r\n          191, 158, 130, 108, 95, 91, 97,\r\n          109, 125, 144, 166, 189, 212,\r\n          236, 259, 280, 300, 316, 329,\r\n          338, 342, 339, 329, 312, 288,\r\n          258, 221, 178, 128, 71,\r\n        ],\r\n        [\r\n          158, 178, 193, 205, 212, 213,\r\n          204, 190, 180, 173, 168, 164,\r\n          162, 160, 159, 158, 159, 166,\r\n          179, 195, 215, 236, 257, 276,\r\n          292, 301, 304, 303, 300, 293,\r\n          284, 273, 262, 251, 241, 234,\r\n          232, 232, 232, 232, 232, 232,\r\n        ],\r\n        [\r\n          58, 137, 202, 251, 288, 312,\r\n          323, 324, 311, 288, 257, 222,\r\n          187, 154, 124, 100, 81, 68, 61,\r\n          58, 61, 69, 80, 96, 115, 137,\r\n          161, 186, 210, 233, 254, 271,\r\n          284, 293, 297, 297, 297, 297,\r\n          297, 297, 297, 297, 297,\r\n        ],\r\n      ],\r\n    };\r\n  }\r\n\r\n  private getDataForMonthPeriod(): OrdersChart {\r\n    return {\r\n      chartLabel: this.getDataLabels(47, this.period.getMonths()),\r\n      linesData: [\r\n        [\r\n          5, 63, 113, 156, 194, 225,\r\n          250, 270, 283, 289, 290,\r\n          286, 277, 264, 244, 220,\r\n          194, 171, 157, 151, 150,\r\n          152, 155, 160, 166, 170,\r\n          167, 153, 135, 115, 97,\r\n          82, 71, 64, 63, 62, 61,\r\n          62, 65, 73, 84, 102,\r\n          127, 159, 203, 259, 333,\r\n        ],\r\n        [\r\n          6, 83, 148, 200, 240,\r\n          265, 273, 259, 211,\r\n          122, 55, 30, 28, 36,\r\n          50, 68, 88, 109, 129,\r\n          146, 158, 163, 165,\r\n          173, 187, 208, 236,\r\n          271, 310, 346, 375,\r\n          393, 400, 398, 387,\r\n          368, 341, 309, 275,\r\n          243, 220, 206, 202,\r\n          207, 222, 247, 286, 348,\r\n        ],\r\n        [\r\n          398, 348, 315, 292, 274,\r\n          261, 251, 243, 237, 231,\r\n          222, 209, 192, 172, 152,\r\n          132, 116, 102, 90, 80, 71,\r\n          64, 58, 53, 49, 48, 54, 66,\r\n          84, 104, 125, 142, 156, 166,\r\n          172, 174, 172, 167, 159, 149,\r\n          136, 121, 105, 86, 67, 45, 22,\r\n        ],\r\n      ],\r\n    };\r\n  }\r\n\r\n  private getDataForYearPeriod(): OrdersChart {\r\n    return {\r\n      chartLabel: this.getDataLabels(42, this.year),\r\n      linesData: [\r\n        [\r\n          190, 269, 327, 366, 389, 398,\r\n          396, 387, 375, 359, 343, 327,\r\n          312, 298, 286, 276, 270, 268,\r\n          265, 258, 247, 234, 220, 204,\r\n          188, 172, 157, 142, 128, 116,\r\n          106, 99, 95, 94, 92, 89, 84,\r\n          77, 69, 60, 49, 36, 22,\r\n        ],\r\n        [\r\n          265, 307, 337, 359, 375, 386,\r\n          393, 397, 399, 397, 390, 379,\r\n          365, 347, 326, 305, 282, 261,\r\n          241, 223, 208, 197, 190, 187,\r\n          185, 181, 172, 160, 145, 126,\r\n          105, 82, 60, 40, 26, 19, 22,\r\n          43, 82, 141, 220, 321,\r\n        ],\r\n        [\r\n          9, 165, 236, 258, 244, 206,\r\n          186, 189, 209, 239, 273, 307,\r\n          339, 365, 385, 396, 398, 385,\r\n          351, 300, 255, 221, 197, 181,\r\n          170, 164, 162, 161, 159, 154,\r\n          146, 135, 122, 108, 96, 87,\r\n          83, 82, 82, 82, 82, 82, 82,\r\n        ],\r\n      ],\r\n    };\r\n  }\r\n\r\n  getDataLabels(nPoints: number, labelsArray: string[]): string[] {\r\n    const labelsArrayLength = labelsArray.length;\r\n    const step = Math.round(nPoints / labelsArrayLength);\r\n\r\n    return Array.from(Array(nPoints)).map((item, index) => {\r\n      const dataIndex = Math.round(index / step);\r\n\r\n      return index % step === 0 ? labelsArray[dataIndex] : '';\r\n    });\r\n  }\r\n\r\n  getOrdersChartData(period: string): OrdersChart {\r\n    return this.data[period];\r\n  }\r\n}\r\n"], "mappings": "AAEA,SAAsBA,eAAe,QAAQ,sBAAsB;;;AAGnE,OAAM,MAAOC,kBAAmB,SAAQD,eAAe;EAcrDE,YAAoBC,MAAsB;IACxC,KAAK,EAAE;IADW,KAAAA,MAAM,GAANA,MAAM;IAZlB,KAAAC,IAAI,GAAG,CACb,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,CACP;IAEO,KAAAC,IAAI,GAAO,EAAG;IAIpB,IAAI,CAACA,IAAI,GAAG;MACVC,IAAI,EAAE,IAAI,CAACC,oBAAoB,EAAE;MACjCC,KAAK,EAAE,IAAI,CAACC,qBAAqB,EAAE;MACnCL,IAAI,EAAE,IAAI,CAACM,oBAAoB;KAChC;EACH;EAEQH,oBAAoBA,CAAA;IAC1B,OAAO;MACLI,UAAU,EAAE,IAAI,CAACC,aAAa,CAAC,EAAE,EAAE,IAAI,CAACT,MAAM,CAACU,QAAQ,EAAE,CAAC;MAC1DC,SAAS,EAAE,CACT,CACE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAC5B,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAC5B,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAC9B,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAC5B,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAC5B,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAC5B,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,CACvB,EACD,CACE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAC5B,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAC5B,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAC5B,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAC5B,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAC5B,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAC5B,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAC7B,EACD,CACE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAC3B,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAC5B,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAC9B,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAC5B,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAC5B,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAC5B,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CACxB;KAEJ;EACH;EAEQL,qBAAqBA,CAAA;IAC3B,OAAO;MACLE,UAAU,EAAE,IAAI,CAACC,aAAa,CAAC,EAAE,EAAE,IAAI,CAACT,MAAM,CAACY,SAAS,EAAE,CAAC;MAC3DD,SAAS,EAAE,CACT,CACE,CAAC,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EACzB,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EACvB,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EACvB,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EACvB,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EACvB,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EACtB,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EACtB,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EACnB,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CACxB,EACD,CACE,CAAC,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EACpB,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAClB,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EACnB,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EACpB,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAClB,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAClB,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAClB,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAClB,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAClB,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAClB,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CACxB,EACD,CACE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EACvB,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EACvB,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EACvB,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EACzB,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAC1B,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAC3B,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAC5B,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAC9B;KAEJ;EACH;EAEQJ,oBAAoBA,CAAA;IAC1B,OAAO;MACLC,UAAU,EAAE,IAAI,CAACC,aAAa,CAAC,EAAE,EAAE,IAAI,CAACR,IAAI,CAAC;MAC7CU,SAAS,EAAE,CACT,CACE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAC5B,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAC5B,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAC5B,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAC5B,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAC5B,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAC3B,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CACvB,EACD,CACE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAC5B,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAC5B,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAC5B,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAC5B,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAC5B,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAC3B,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CACtB,EACD,CACE,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAC1B,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAC5B,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAC5B,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAC5B,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAC5B,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAC1B,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAC3B;KAEJ;EACH;EAEAF,aAAaA,CAACI,OAAe,EAAEC,WAAqB;IAClD,MAAMC,iBAAiB,GAAGD,WAAW,CAACE,MAAM;IAC5C,MAAMC,IAAI,GAAGC,IAAI,CAACC,KAAK,CAACN,OAAO,GAAGE,iBAAiB,CAAC;IAEpD,OAAOK,KAAK,CAACC,IAAI,CAACD,KAAK,CAACP,OAAO,CAAC,CAAC,CAACS,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,KAAI;MACpD,MAAMC,SAAS,GAAGP,IAAI,CAACC,KAAK,CAACK,KAAK,GAAGP,IAAI,CAAC;MAE1C,OAAOO,KAAK,GAAGP,IAAI,KAAK,CAAC,GAAGH,WAAW,CAACW,SAAS,CAAC,GAAG,EAAE;IACzD,CAAC,CAAC;EACJ;EAEAC,kBAAkBA,CAAC1B,MAAc;IAC/B,OAAO,IAAI,CAACE,IAAI,CAACF,MAAM,CAAC;EAC1B;;;uCApJWF,kBAAkB,EAAA6B,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,cAAA;IAAA;EAAA;;;aAAlBhC,kBAAkB;MAAAiC,OAAA,EAAlBjC,kBAAkB,CAAAkC;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}