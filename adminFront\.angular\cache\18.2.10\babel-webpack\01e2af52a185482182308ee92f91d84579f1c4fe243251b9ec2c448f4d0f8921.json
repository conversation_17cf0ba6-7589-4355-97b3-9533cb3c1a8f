{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { BaseComponent } from '../../components/base/baseComponent';\nimport { EnumFileType } from 'src/app/shared/enum/enumFileType';\nimport { environment } from 'src/environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/shared/helper/allowHelper\";\nimport * as i2 from \"src/app/shared/services/message.service\";\nimport * as i3 from \"src/services/api/services/File.service\";\nimport * as i4 from \"@angular/common\";\nconst _c0 = [\"fileInput\"];\nconst _c1 = a0 => ({\n  CFileName: a0\n});\nfunction FileUploadComponent_h3_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"h3\", 11);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r1.config.helpText);\n  }\n}\nfunction FileUploadComponent_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 12)(1, \"span\", 13);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"button\", 14);\n    i0.ɵɵlistener(\"click\", function FileUploadComponent_div_12_Template_button_click_3_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.clearFile());\n    });\n    i0.ɵɵelement(4, \"i\", 15);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.fileName);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r1.config.disabled);\n  }\n}\nfunction FileUploadComponent_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 12)(1, \"span\", 16)(2, \"a\", 17);\n    i0.ɵɵlistener(\"click\", function FileUploadComponent_div_13_Template_a_click_2_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.openFile(ctx_r1.currentFileUrl));\n    });\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.currentFileUrl, \" \");\n  }\n}\nfunction FileUploadComponent_div_14_div_1_div_1_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 29)(1, \"img\", 30);\n    i0.ɵɵlistener(\"click\", function FileUploadComponent_div_14_div_1_div_1_div_2_Template_img_click_1_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const file_r7 = i0.ɵɵnextContext().$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.handleFileClick(file_r7));\n    })(\"error\", function FileUploadComponent_div_14_div_1_div_1_div_2_Template_img_error_1_listener($event) {\n      i0.ɵɵrestoreView(_r6);\n      const file_r7 = i0.ɵɵnextContext().$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.onImageError($event, file_r7));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(2, \"div\", 31);\n    i0.ɵɵelement(3, \"i\", 32);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const file_r7 = i0.ɵɵnextContext().$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"src\", ctx_r1.getImageSrc(file_r7), i0.ɵɵsanitizeUrl)(\"title\", \"\\u9EDE\\u64CA\\u653E\\u5927\\u9810\\u89BD: \" + file_r7.CFileName);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r1.getFileTypeIcon(file_r7).label, \" \");\n  }\n}\nfunction FileUploadComponent_div_14_div_1_div_1_div_3_span_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 35);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const file_r7 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.getFileSize(file_r7), \" \");\n  }\n}\nfunction FileUploadComponent_div_14_div_1_div_1_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 33);\n    i0.ɵɵlistener(\"click\", function FileUploadComponent_div_14_div_1_div_1_div_3_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const file_r7 = i0.ɵɵnextContext().$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.handleFileClick(file_r7));\n    });\n    i0.ɵɵelement(1, \"i\");\n    i0.ɵɵelementStart(2, \"span\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, FileUploadComponent_div_14_div_1_div_1_div_3_span_4_Template, 2, 1, \"span\", 34);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const file_r7 = i0.ɵɵnextContext().$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"ngClass\", ctx_r1.getFileTypeIcon(file_r7).bgColor)(\"title\", \"\\u9EDE\\u64CA\\u958B\\u555F: \" + file_r7.CFileName);\n    i0.ɵɵadvance();\n    i0.ɵɵclassMap(ctx_r1.getFileTypeIcon(file_r7).icon + \" text-4xl mb-2 \" + ctx_r1.getFileTypeIcon(file_r7).color);\n    i0.ɵɵadvance();\n    i0.ɵɵclassMap(\"text-xs font-medium \" + ctx_r1.getFileTypeIcon(file_r7).color);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.getFileTypeIcon(file_r7).label, \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.getFileSize(file_r7));\n  }\n}\nfunction FileUploadComponent_div_14_div_1_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 22)(1, \"div\", 23);\n    i0.ɵɵtemplate(2, FileUploadComponent_div_14_div_1_div_1_div_2_Template, 5, 3, \"div\", 24)(3, FileUploadComponent_div_14_div_1_div_1_div_3_Template, 5, 8, \"div\", 25);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"p\", 26);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"span\", 27);\n    i0.ɵɵlistener(\"click\", function FileUploadComponent_div_14_div_1_div_1_Template_span_click_6_listener() {\n      const i_r9 = i0.ɵɵrestoreView(_r5).index;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.removeFile(i_r9));\n    });\n    i0.ɵɵelement(7, \"i\", 28);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const file_r7 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isImage(file_r7.CFileType) || ctx_r1.isImageFile(file_r7.CFileName) && !ctx_r1.isPdf(file_r7.CFileType) && !ctx_r1.isCad(file_r7.CFileType));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.isImage(file_r7.CFileType) && (!ctx_r1.isImageFile(file_r7.CFileName) || ctx_r1.isPdf(file_r7.CFileType) || ctx_r1.isCad(file_r7.CFileType)));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"title\", file_r7.CFileName);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(file_r7.CFileName);\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"hidden\", ctx_r1.config.disabled);\n  }\n}\nfunction FileUploadComponent_div_14_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 20);\n    i0.ɵɵtemplate(1, FileUploadComponent_div_14_div_1_div_1_Template, 8, 6, \"div\", 21);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.fileList);\n  }\n}\nfunction FileUploadComponent_div_14_div_2_div_1_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 29)(1, \"img\", 30);\n    i0.ɵɵlistener(\"click\", function FileUploadComponent_div_14_div_2_div_1_div_2_Template_img_click_1_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const file_r11 = i0.ɵɵnextContext().$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.handleFileClick({\n        CFileName: file_r11.CFileName,\n        CFile: file_r11.CFile,\n        data: ctx_r1.getImageSrc(file_r11),\n        relativePath: file_r11.relativePath,\n        fileName: file_r11.fileName\n      }));\n    })(\"error\", function FileUploadComponent_div_14_div_2_div_1_div_2_Template_img_error_1_listener($event) {\n      i0.ɵɵrestoreView(_r10);\n      const file_r11 = i0.ɵɵnextContext().$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.onImageError($event, file_r11));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(2, \"div\", 31);\n    i0.ɵɵelement(3, \"i\", 32);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const file_r11 = i0.ɵɵnextContext().$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"src\", ctx_r1.getImageSrc(file_r11), i0.ɵɵsanitizeUrl)(\"title\", \"\\u9EDE\\u64CA\\u653E\\u5927\\u9810\\u89BD: \" + file_r11.CFileName);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r1.getFileTypeIcon(i0.ɵɵpureFunction1(3, _c1, file_r11.CFileName)).label, \" \");\n  }\n}\nfunction FileUploadComponent_div_14_div_2_div_1_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 33);\n    i0.ɵɵlistener(\"click\", function FileUploadComponent_div_14_div_2_div_1_div_3_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r12);\n      const file_r11 = i0.ɵɵnextContext().$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.handleFileClick({\n        CFileName: file_r11.CFileName,\n        CFile: file_r11.CFile,\n        data: file_r11.CFile,\n        relativePath: file_r11.relativePath,\n        fileName: file_r11.fileName\n      }));\n    });\n    i0.ɵɵelement(1, \"i\");\n    i0.ɵɵelementStart(2, \"span\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const file_r11 = i0.ɵɵnextContext().$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"ngClass\", ctx_r1.getFileTypeIcon(i0.ɵɵpureFunction1(7, _c1, file_r11.CFileName)).bgColor)(\"title\", \"\\u9EDE\\u64CA\\u958B\\u555F: \" + file_r11.CFileName);\n    i0.ɵɵadvance();\n    i0.ɵɵclassMap(ctx_r1.getFileTypeIcon(i0.ɵɵpureFunction1(9, _c1, file_r11.CFileName)).icon + \" text-4xl mb-2 \" + ctx_r1.getFileTypeIcon(i0.ɵɵpureFunction1(11, _c1, file_r11.CFileName)).color);\n    i0.ɵɵadvance();\n    i0.ɵɵclassMap(\"text-xs font-medium \" + ctx_r1.getFileTypeIcon(i0.ɵɵpureFunction1(13, _c1, file_r11.CFileName)).color);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.getFileTypeIcon(i0.ɵɵpureFunction1(15, _c1, file_r11.CFileName)).label, \" \");\n  }\n}\nfunction FileUploadComponent_div_14_div_2_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 22)(1, \"div\", 23);\n    i0.ɵɵtemplate(2, FileUploadComponent_div_14_div_2_div_1_div_2_Template, 5, 5, \"div\", 24)(3, FileUploadComponent_div_14_div_2_div_1_div_3_Template, 4, 17, \"div\", 25);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"p\", 26);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const file_r11 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isImageFile(file_r11.CFileName || file_r11.CFile) && !ctx_r1.isPDFString(file_r11.CFileName || file_r11.CFile) && !ctx_r1.isCadString(file_r11.CFileName || file_r11.CFile));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.isImageFile(file_r11.CFileName || file_r11.CFile) || ctx_r1.isPDFString(file_r11.CFileName || file_r11.CFile) || ctx_r1.isCadString(file_r11.CFileName || file_r11.CFile));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"title\", file_r11.CFileName);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(file_r11.CFileName);\n  }\n}\nfunction FileUploadComponent_div_14_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 20);\n    i0.ɵɵtemplate(1, FileUploadComponent_div_14_div_2_div_1_Template, 6, 4, \"div\", 21);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.existingFiles);\n  }\n}\nfunction FileUploadComponent_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 18);\n    i0.ɵɵtemplate(1, FileUploadComponent_div_14_div_1_Template, 2, 1, \"div\", 19)(2, FileUploadComponent_div_14_div_2_Template, 2, 1, \"div\", 19);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.fileList && ctx_r1.fileList.length > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.existingFiles && ctx_r1.existingFiles.length > 0);\n  }\n}\nfunction FileUploadComponent_div_15_div_2_span_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const file_r14 = i0.ɵɵnextContext().$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\"\\u2022 \", ctx_r1.getFileSize(file_r14), \"\");\n  }\n}\nfunction FileUploadComponent_div_15_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r13 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 38)(1, \"div\", 39);\n    i0.ɵɵlistener(\"click\", function FileUploadComponent_div_15_div_2_Template_div_click_1_listener() {\n      const file_r14 = i0.ɵɵrestoreView(_r13).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.handleFileClick(file_r14));\n    });\n    i0.ɵɵelementStart(2, \"div\", 40);\n    i0.ɵɵelement(3, \"i\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 41)(5, \"span\", 42);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 43)(8, \"span\");\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(10, FileUploadComponent_div_15_div_2_span_10_Template, 2, 1, \"span\", 44);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(11, \"button\", 45);\n    i0.ɵɵlistener(\"click\", function FileUploadComponent_div_15_div_2_Template_button_click_11_listener() {\n      const i_r15 = i0.ɵɵrestoreView(_r13).index;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.removeFile(i_r15));\n    });\n    i0.ɵɵelement(12, \"i\", 46);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const file_r14 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassMap(ctx_r1.getFileTypeIcon(file_r14).icon + \" text-xl \" + ctx_r1.getFileTypeIcon(file_r14).color);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(file_r14.CFileName);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.getFileTypeIcon(file_r14).label);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.getFileSize(file_r14));\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"hidden\", ctx_r1.config.disabled);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.config.disabled);\n  }\n}\nfunction FileUploadComponent_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 18)(1, \"div\", 36);\n    i0.ɵɵtemplate(2, FileUploadComponent_div_15_div_2_Template, 13, 8, \"div\", 37);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.fileList);\n  }\n}\nexport class FileUploadComponent extends BaseComponent {\n  constructor(_allow, message, fileService) {\n    super(_allow);\n    this._allow = _allow;\n    this.message = message;\n    this.fileService = fileService;\n    this.config = {\n      acceptedTypes: ['image/jpeg', 'image/jpg', 'application/pdf'],\n      acceptedFileRegex: /pdf|jpg|jpeg|png/i,\n      acceptAttribute: 'image/jpeg, image/jpg, application/pdf',\n      label: '上傳檔案',\n      helpText: '*請上傳PDF格式',\n      required: false,\n      disabled: false,\n      autoFillName: false,\n      buttonText: '上傳',\n      buttonIcon: 'fa-solid fa-cloud-arrow-up',\n      maxFileSize: 10,\n      multiple: false,\n      showPreview: false\n    };\n    this.currentFileName = null;\n    this.currentFileUrl = null;\n    this.labelMinWidth = '172px';\n    this.fileList = []; // 用於多檔案模式的檔案列表\n    this.existingFiles = []; // 已存在的檔案列表（編輯模式用）\n    this.fileSelected = new EventEmitter();\n    this.fileCleared = new EventEmitter();\n    this.nameAutoFilled = new EventEmitter();\n    this.multiFileSelected = new EventEmitter(); // 多檔案選擇事件\n    this.fileRemoved = new EventEmitter(); // 檔案移除事件\n    this.fileName = null;\n  }\n  ngOnInit() {\n    this.fileName = this.currentFileName; // 設置預設配置\n    this.config = {\n      ...{\n        acceptedTypes: ['image/jpeg', 'image/jpg', 'application/pdf'],\n        acceptedFileRegex: /pdf|jpg|jpeg|png/i,\n        acceptAttribute: 'image/jpeg, image/jpg, application/pdf',\n        label: '上傳檔案',\n        helpText: '*請上傳PDF格式',\n        required: false,\n        disabled: false,\n        autoFillName: false,\n        buttonText: '上傳',\n        buttonIcon: 'fa-solid fa-cloud-arrow-up',\n        maxFileSize: 10,\n        multiple: false,\n        showPreview: false\n      },\n      ...this.config\n    };\n  }\n  onFileSelected(event) {\n    const files = event.target.files;\n    if (!files || files.length === 0) {\n      return;\n    }\n    if (this.config.multiple) {\n      this.handleMultipleFiles(files);\n    } else {\n      this.handleSingleFile(files[0]);\n    }\n  }\n  handleSingleFile(file) {\n    // 檔案格式檢查\n    if (!this.config.acceptedFileRegex.test(file.name)) {\n      const acceptedFormats = this.getAcceptedFormatsText();\n      this.message.showErrorMSG(`檔案格式錯誤，${acceptedFormats}`);\n      return;\n    }\n    // 檔案大小檢查\n    const maxSizeInBytes = (this.config.maxFileSize || 10) * 1024 * 1024;\n    if (file.size > maxSizeInBytes) {\n      this.message.showErrorMSG(`檔案大小超過限制（${this.config.maxFileSize}MB）`);\n      return;\n    }\n    this.fileName = file.name;\n    // 自動填入名稱功能\n    if (this.config.autoFillName) {\n      const fileNameWithoutExtension = file.name.substring(0, file.name.lastIndexOf('.')) || file.name;\n      this.nameAutoFilled.emit(fileNameWithoutExtension);\n    }\n    const reader = new FileReader();\n    reader.onload = e => {\n      // 判斷檔案類型\n      let fileType;\n      if (file.type.startsWith('image/')) {\n        fileType = EnumFileType.JPG; // 圖片\n      } else if (file.name.toLowerCase().includes('.dwg') || file.name.toLowerCase().includes('.dxf')) {\n        fileType = 3; // CAD檔案\n      } else {\n        fileType = EnumFileType.PDF; // PDF\n      }\n      const result = {\n        CName: file.name,\n        CFile: e.target?.result?.toString().split(',')[1],\n        Cimg: file.name.includes('pdf') ? file : file,\n        CFileUpload: file,\n        CFileType: fileType,\n        fileName: file.name\n      };\n      this.fileSelected.emit(result);\n      if (this.fileInput) {\n        this.fileInput.nativeElement.value = null;\n      }\n    };\n    reader.readAsDataURL(file);\n  }\n  handleMultipleFiles(files) {\n    const allowedTypes = ['image/jpeg', 'image/jpg', 'application/pdf', 'application/acad', 'application/x-autocad', 'image/vnd.dwg', 'image/x-dwg'];\n    const fileRegex = this.config.acceptedFileRegex || /pdf|jpg|jpeg|png|dwg|dxf/i;\n    const maxSizeInBytes = (this.config.maxFileSize || 10) * 1024 * 1024;\n    // 如果是第一次選擇檔案且支援自動填入名稱\n    if (this.config.autoFillName && files.length > 0) {\n      const firstFile = files[0];\n      const fileName = firstFile.name;\n      const fileNameWithoutExtension = fileName.substring(0, fileName.lastIndexOf('.')) || fileName;\n      this.nameAutoFilled.emit(fileNameWithoutExtension);\n    }\n    const newFiles = [];\n    let processedCount = 0;\n    for (let i = 0; i < files.length; i++) {\n      const file = files[i];\n      // 檔案格式檢查\n      if (!fileRegex.test(file.name)) {\n        this.message.showErrorMSG('檔案格式錯誤，僅限pdf、圖片檔、CAD檔案（dwg, dxf）');\n        processedCount++;\n        continue;\n      }\n      // 檔案大小檢查\n      if (file.size > maxSizeInBytes) {\n        this.message.showErrorMSG(`檔案 ${file.name} 大小超過限制（${this.config.maxFileSize}MB）`);\n        processedCount++;\n        continue;\n      }\n      if (allowedTypes.includes(file.type) || fileRegex.test(file.name)) {\n        const reader = new FileReader();\n        reader.onload = e => {\n          // 判斷檔案類型\n          let fileType = 1; // 預設為其他\n          const fileName = file.name.toLowerCase();\n          if (fileName.match(/\\.(jpg|jpeg|png)$/)) {\n            fileType = 2; // 圖片\n          } else if (fileName.endsWith('.pdf')) {\n            fileType = 1; // PDF\n          } else if (fileName.match(/\\.(dwg|dxf)$/)) {\n            fileType = 3; // CAD\n          }\n          newFiles.push({\n            data: e.target.result,\n            CFileBlood: this.removeBase64Prefix(e.target.result),\n            CFileName: file.name,\n            CFileType: fileType\n          });\n          processedCount++;\n          if (processedCount === files.length) {\n            this.fileList = [...this.fileList, ...newFiles];\n            this.multiFileSelected.emit(this.fileList);\n            if (this.fileInput) {\n              this.fileInput.nativeElement.value = null;\n            }\n          }\n        };\n        reader.readAsDataURL(file);\n      } else {\n        processedCount++;\n      }\n    }\n  }\n  clearFile() {\n    this.fileName = null;\n    this.fileCleared.emit();\n    if (this.fileInput) {\n      this.fileInput.nativeElement.value = null;\n    }\n  }\n  openFile(url) {\n    if (url) {\n      // 如果 URL 不是完整的 http/https 連結，則添加 base URL\n      const fullUrl = url.startsWith('http') ? url : `${environment.BASE_WITHOUT_FILEROOT}${url}`;\n      window.open(fullUrl, '_blank');\n    }\n  }\n  removeFile(index) {\n    this.fileList.splice(index, 1);\n    this.fileRemoved.emit(index);\n    this.multiFileSelected.emit(this.fileList);\n  }\n  removeBase64Prefix(base64String) {\n    const prefixIndex = base64String.indexOf(\",\");\n    if (prefixIndex !== -1) {\n      return base64String.substring(prefixIndex + 1);\n    }\n    return base64String;\n  }\n  isImage(fileType) {\n    return fileType === 2;\n  }\n  isCad(fileType) {\n    return fileType === 3;\n  }\n  isPdf(fileType) {\n    return fileType === 1;\n  }\n  isPDFString(str) {\n    if (str) {\n      return str.toLowerCase().endsWith(\".pdf\");\n    }\n    return false;\n  }\n  isCadString(str) {\n    if (str) {\n      const lowerStr = str.toLowerCase();\n      return lowerStr.endsWith(\".dwg\") || lowerStr.endsWith(\".dxf\");\n    }\n    return false;\n  } // 處理檔案點擊事件\n  handleFileClick(file) {\n    console.log('檔案點擊事件觸發:', file);\n    const fileName = file.CFileName || file.fileName || '';\n    const displayName = file.CFileName || fileName;\n    // 判斷檔案類型\n    const isImageByName = this.isImageFile(displayName);\n    const isPdfByName = this.isPDFString(displayName);\n    const isCadByName = this.isCadString(displayName);\n    console.log('檔案類型判斷:', {\n      isImageByName,\n      isPdfByName,\n      isCadByName,\n      displayName\n    });\n    // 檢查是否為新上傳的檔案（有 data 屬性）\n    if (file.data) {\n      console.log('處理新上傳的檔案（base64）');\n      this.handleLocalFileData(file.data, displayName, isImageByName, isPdfByName, isCadByName);\n      return;\n    }\n    // 統一使用 GetFile API 取得檔案\n    const relativePath = file.relativePath || file.CFile;\n    const serverFileName = file.fileName || file.CFileName;\n    console.log('檔案路徑資訊:', {\n      relativePath,\n      serverFileName\n    });\n    if (relativePath && serverFileName) {\n      console.log('使用 GetFile API 取得檔案');\n      this.getFileFromServer(relativePath, serverFileName, displayName, isImageByName, isPdfByName, isCadByName);\n    } else {\n      // 如果沒有路徑資訊，顯示錯誤訊息\n      console.error('檔案缺少路徑資訊，無法使用 getFile API:', file);\n      this.message.showErrorMSG('檔案路徑資訊不完整，無法開啟檔案');\n    }\n  }\n  // 處理本地檔案的後備方法 - 已廢棄，統一使用 getFile API\n  handleLocalFile(file, isImage, isPdf, isCad) {\n    const fileName = file.CFileName || file.fileName || '';\n    console.error('檔案缺少必要的路徑資訊，無法使用 getFile API:', file);\n    this.message.showErrorMSG('檔案路徑資訊不完整，無法開啟檔案');\n  }\n  // 處理本地檔案資料（新上傳的檔案）\n  handleLocalFileData(data, fileName, isImage, isPdf, isCad) {\n    if (isImage) {\n      // 圖片預覽\n      this.openImagePreview(data, fileName);\n    } else if (isPdf) {\n      // PDF 檔案另開視窗顯示\n      this.openPdfInNewWindow(data, fileName);\n    } else {\n      // 其他檔案（如CAD）轉換為blob並下載\n      this.downloadBase64File(data, fileName);\n    }\n  }\n  // 下載base64檔案\n  downloadBase64File(base64Data, fileName) {\n    try {\n      // 移除data URL前綴（如果存在）\n      const base64String = base64Data.includes(',') ? base64Data.split(',')[1] : base64Data;\n      // 轉換為blob\n      const byteCharacters = atob(base64String);\n      const byteNumbers = new Array(byteCharacters.length);\n      for (let i = 0; i < byteCharacters.length; i++) {\n        byteNumbers[i] = byteCharacters.charCodeAt(i);\n      }\n      const byteArray = new Uint8Array(byteNumbers);\n      const blob = new Blob([byteArray]);\n      // 創建下載連結\n      const url = URL.createObjectURL(blob);\n      const link = document.createElement('a');\n      link.href = url;\n      link.download = fileName;\n      document.body.appendChild(link);\n      link.click();\n      document.body.removeChild(link);\n      // 清理URL\n      setTimeout(() => URL.revokeObjectURL(url), 1000);\n    } catch (error) {\n      console.error('下載檔案失敗:', error);\n      this.message.showErrorMSG('檔案下載失敗');\n    }\n  }\n  // 從後端取得檔案 blob\n  getFileFromServer(relativePath, fileName, displayName, isImage, isPdf, isCad) {\n    this.fileService.getFile(relativePath, fileName).subscribe({\n      next: blob => {\n        const url = URL.createObjectURL(blob);\n        if (isImage) {\n          // 圖片預覽\n          this.openImagePreview(url, displayName);\n        } else if (isPdf) {\n          // PDF 檔案另開視窗顯示\n          this.openPdfInNewWindow(url, displayName);\n        } else {\n          // 其他檔案直接下載\n          this.downloadBlobFile(blob, displayName);\n        }\n        // 清理 URL (延遲清理以確保檔案能正確下載/預覽)\n        setTimeout(() => URL.revokeObjectURL(url), 10000);\n      },\n      error: error => {\n        console.error('取得檔案失敗:', error);\n        this.message.showErrorMSG('取得檔案失敗，請稍後再試');\n      }\n    });\n  }\n  // 在新視窗中開啟 PDF\n  openPdfInNewWindow(blobUrl, fileName) {\n    try {\n      const newWindow = window.open('', '_blank');\n      if (newWindow) {\n        newWindow.document.write(`\n          <html>\n            <head>\n              <title>${fileName}</title>\n              <style>\n                body { margin: 0; padding: 0; }\n                iframe { width: 100vw; height: 100vh; border: none; }\n              </style>\n            </head>\n            <body>\n              <iframe src=\"${blobUrl}\" type=\"application/pdf\"></iframe>\n            </body>\n          </html>\n        `);\n        newWindow.document.close();\n      } else {\n        // 如果彈出視窗被阻擋，直接開啟 URL\n        window.location.href = blobUrl;\n      }\n    } catch (error) {\n      console.error('開啟 PDF 視窗失敗:', error);\n      // 後備方案：直接開啟 URL\n      window.open(blobUrl, '_blank');\n    }\n  }\n  // 下載 blob 檔案\n  downloadBlobFile(blob, fileName) {\n    const url = URL.createObjectURL(blob);\n    const link = document.createElement('a');\n    link.href = url;\n    link.download = fileName;\n    document.body.appendChild(link);\n    link.click();\n    document.body.removeChild(link);\n    // 清理 URL\n    setTimeout(() => URL.revokeObjectURL(url), 1000);\n  }\n  // 判斷檔案是否為圖片（根據檔名）\n  isImageFile(fileName) {\n    const imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'];\n    const extension = fileName.split('.').pop()?.toLowerCase();\n    return imageExtensions.includes(extension || '');\n  }\n  // 取得正確的圖片 src\n  getImageSrc(file) {\n    const fileName = file.CFileName || file.fileName || '';\n    if (!fileName) {\n      return '';\n    }\n    // 如果檔案有 data 屬性（base64格式），直接使用\n    if (file.data) {\n      return file.data;\n    }\n    // 如果有相對路徑和檔案名，構造 API URL\n    if (file.relativePath && file.fileName) {\n      return `${environment.BASE_WITHOUT_FILEROOT}/api/File/GetFile?relativePath=${encodeURIComponent(file.relativePath)}&fileName=${encodeURIComponent(file.fileName)}`;\n    }\n    // 如果是現有檔案且有 CFile 路徑\n    if (file.CFile && !file.CFile.startsWith('data:')) {\n      return `${environment.BASE_WITHOUT_FILEROOT}${file.CFile}`;\n    }\n    // 後備方案：空字串\n    return '';\n  }\n  // HTML 模板中使用的方法\n  isImageByName(fileName) {\n    return this.isImageFile(fileName);\n  }\n  isPDFByName(fileName) {\n    return this.isPDFString(fileName);\n  }\n  isCadByName(fileName) {\n    return this.isCadString(fileName);\n  } // 圖片載入錯誤處理\n  onImageError(event, file) {\n    const fileName = file.CFileName || file.fileName || '檔案';\n    console.warn('圖片載入失敗:', fileName, file);\n    // 可以設置預設圖片或其他處理\n    event.target.style.display = 'none';\n    // 在圖片載入失敗時，顯示檔案類型圖示，但保留點擊事件\n    const container = event.target.parentElement;\n    if (container) {\n      const iconInfo = this.getFileTypeIcon(file);\n      // 創建新的內容並綁定點擊事件\n      const newContent = document.createElement('div');\n      newContent.className = `w-full h-full flex flex-col items-center justify-center cursor-pointer rounded-lg ${iconInfo.bgColor}`;\n      newContent.innerHTML = `\n        <i class=\"${iconInfo.icon} text-4xl mb-2 ${iconInfo.color}\"></i>\n        <span class=\"text-xs font-medium ${iconInfo.color}\">${iconInfo.label}</span>\n      `;\n      // 綁定點擊事件\n      newContent.addEventListener('click', () => {\n        this.handleFileClick(file);\n      });\n      // 替換內容\n      container.innerHTML = '';\n      container.appendChild(newContent);\n    }\n  }\n  getAcceptedFormatsText() {\n    const extensions = this.config.acceptAttribute?.split(',').map(type => {\n      if (type.includes('pdf')) return 'PDF';\n      if (type.includes('jpeg') || type.includes('jpg')) return 'JPG';\n      if (type.includes('png')) return 'PNG';\n      if (type.includes('.dwg')) return 'DWG';\n      if (type.includes('.dxf')) return 'DXF';\n      return type;\n    }).join('、');\n    return `僅限${extensions}格式`;\n  }\n  // 在瀏覽器中打開 PDF（已廢棄，統一使用 getFile API）\n  openPdfInBrowser(fileUrl) {\n    console.warn('openPdfInBrowser 方法已廢棄，請使用 getFileFromServer');\n    this.message.showErrorMSG('PDF 檢視功能已更新，請重新操作');\n  }\n  // 直接下載檔案（已廢棄，統一使用 getFile API）\n  downloadFileDirectly(fileUrl, fileName) {\n    console.warn('downloadFileDirectly 方法已廢棄，請使用 getFileFromServer');\n    this.message.showErrorMSG('檔案下載功能已更新，請重新操作');\n  }\n  // 開啟圖片預覽\n  openImagePreview(imageUrl, fileName) {\n    try {\n      const newWindow = window.open('', '_blank');\n      if (newWindow) {\n        newWindow.document.write(`\n          <html>\n            <head>\n              <title>${fileName}</title>\n              <style>\n                body {\n                  margin: 0;\n                  padding: 20px;\n                  background-color: #f5f5f5;\n                  display: flex;\n                  justify-content: center;\n                  align-items: center;\n                  min-height: 100vh;\n                }\n                img {\n                  max-width: 100%;\n                  max-height: 100vh;\n                  object-fit: contain;\n                  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);\n                  background-color: white;\n                }\n                .title {\n                  position: fixed;\n                  top: 10px;\n                  left: 20px;\n                  background: rgba(0, 0, 0, 0.7);\n                  color: white;\n                  padding: 10px;\n                  border-radius: 4px;\n                  font-family: Arial, sans-serif;\n                }\n              </style>\n            </head>\n            <body>\n              <div class=\"title\">${fileName}</div>\n              <img src=\"${imageUrl}\" alt=\"${fileName}\" />\n            </body>\n          </html>\n        `);\n        newWindow.document.close();\n      }\n    } catch (error) {\n      console.error('開啟圖片預覽失敗:', error);\n      window.open(imageUrl, '_blank');\n    }\n  }\n  openNewTab(url) {\n    if (url) {\n      // 如果 URL 不是完整的 http/https 連結，則添加 base URL\n      const fullUrl = url.startsWith('http') ? url : `${environment.BASE_WITHOUT_FILEROOT}${url}`;\n      window.open(fullUrl, '_blank');\n    }\n  }\n  // 取得檔案類型圖示資訊\n  getFileTypeIcon(file) {\n    const fileName = file.CFileName || file.fileName || '';\n    const fileType = file.CFileType;\n    const extension = fileName.split('.').pop()?.toLowerCase() || '';\n    // 圖片類型\n    if (fileType === 2 || this.isImageFile(fileName)) {\n      if (extension === 'jpg' || extension === 'jpeg') {\n        return {\n          icon: 'fa fa-file-image-o',\n          color: 'text-blue-600',\n          bgColor: 'bg-blue-50',\n          label: 'JPG'\n        };\n      } else if (extension === 'png') {\n        return {\n          icon: 'fa fa-file-image-o',\n          color: 'text-purple-600',\n          bgColor: 'bg-purple-50',\n          label: 'PNG'\n        };\n      } else {\n        return {\n          icon: 'fa fa-file-image-o',\n          color: 'text-blue-600',\n          bgColor: 'bg-blue-50',\n          label: '圖片'\n        };\n      }\n    }\n    // PDF類型\n    if (fileType === 1 || this.isPDFString(fileName)) {\n      return {\n        icon: 'fa fa-file-pdf-o',\n        color: 'text-red-600',\n        bgColor: 'bg-red-50',\n        label: 'PDF'\n      };\n    }\n    // CAD類型\n    if (fileType === 3 || this.isCadString(fileName)) {\n      if (extension === 'dwg') {\n        return {\n          icon: 'fa fa-cube',\n          color: 'text-green-600',\n          bgColor: 'bg-green-50',\n          label: 'DWG'\n        };\n      } else if (extension === 'dxf') {\n        return {\n          icon: 'fa fa-cube',\n          color: 'text-emerald-600',\n          bgColor: 'bg-emerald-50',\n          label: 'DXF'\n        };\n      } else {\n        return {\n          icon: 'fa fa-cube',\n          color: 'text-green-600',\n          bgColor: 'bg-green-50',\n          label: 'CAD'\n        };\n      }\n    }\n    // Office文件類型\n    if (extension === 'doc' || extension === 'docx') {\n      return {\n        icon: 'fa fa-file-word-o',\n        color: 'text-blue-700',\n        bgColor: 'bg-blue-50',\n        label: 'Word'\n      };\n    }\n    if (extension === 'xls' || extension === 'xlsx') {\n      return {\n        icon: 'fa fa-file-excel-o',\n        color: 'text-green-700',\n        bgColor: 'bg-green-50',\n        label: 'Excel'\n      };\n    }\n    if (extension === 'ppt' || extension === 'pptx') {\n      return {\n        icon: 'fa fa-file-powerpoint-o',\n        color: 'text-orange-600',\n        bgColor: 'bg-orange-50',\n        label: 'PPT'\n      };\n    }\n    // 壓縮檔案\n    if (['zip', 'rar', '7z', 'tar', 'gz'].includes(extension)) {\n      return {\n        icon: 'fa fa-file-archive-o',\n        color: 'text-yellow-600',\n        bgColor: 'bg-yellow-50',\n        label: extension.toUpperCase()\n      };\n    }\n    // 文字檔案\n    if (['txt', 'log', 'md'].includes(extension)) {\n      return {\n        icon: 'fa fa-file-text-o',\n        color: 'text-gray-600',\n        bgColor: 'bg-gray-50',\n        label: extension.toUpperCase()\n      };\n    }\n    // 預設檔案類型\n    return {\n      icon: 'fa fa-file-o',\n      color: 'text-gray-500',\n      bgColor: 'bg-gray-50',\n      label: extension ? extension.toUpperCase() : '檔案'\n    };\n  }\n  // 取得檔案大小格式化字串\n  getFileSize(file) {\n    let size = 0;\n    if (file.size) {\n      size = file.size;\n    } else if (file.CFileBlood) {\n      // base64 字串大小估算 (約為實際檔案大小的 4/3)\n      size = Math.floor(file.CFileBlood.length * 0.75);\n    }\n    if (size === 0) return '';\n    const units = ['B', 'KB', 'MB', 'GB'];\n    let unitIndex = 0;\n    while (size >= 1024 && unitIndex < units.length - 1) {\n      size /= 1024;\n      unitIndex++;\n    }\n    return `${size.toFixed(unitIndex === 0 ? 0 : 1)} ${units[unitIndex]}`;\n  }\n  static {\n    this.ɵfac = function FileUploadComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || FileUploadComponent)(i0.ɵɵdirectiveInject(i1.AllowHelper), i0.ɵɵdirectiveInject(i2.MessageService), i0.ɵɵdirectiveInject(i3.FileService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: FileUploadComponent,\n      selectors: [[\"app-file-upload\"]],\n      viewQuery: function FileUploadComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.fileInput = _t.first);\n        }\n      },\n      inputs: {\n        config: \"config\",\n        currentFileName: \"currentFileName\",\n        currentFileUrl: \"currentFileUrl\",\n        labelMinWidth: \"labelMinWidth\",\n        fileList: \"fileList\",\n        existingFiles: \"existingFiles\"\n      },\n      outputs: {\n        fileSelected: \"fileSelected\",\n        fileCleared: \"fileCleared\",\n        nameAutoFilled: \"nameAutoFilled\",\n        multiFileSelected: \"multiFileSelected\",\n        fileRemoved: \"fileRemoved\"\n      },\n      standalone: true,\n      features: [i0.ɵɵInheritDefinitionFeature, i0.ɵɵStandaloneFeature],\n      decls: 16,\n      vars: 20,\n      consts: [[\"fileInput\", \"\"], [1, \"form-group\", \"d-flex\", \"align-items-center\"], [1, \"form-group\", \"d-flex\", \"align-items-center\", \"w-full\"], [1, \"d-flex\", \"flex-col\", \"mr-3\"], [\"for\", \"file\", 1, \"label\", \"col-3\"], [\"style\", \"color:red;\", 4, \"ngIf\"], [1, \"flex\", \"flex-col\", \"col-9\", \"px-0\", \"items-start\"], [\"type\", \"file\", \"id\", \"fileInput\", 1, \"hidden\", 2, \"display\", \"none\", 3, \"change\", \"accept\", \"multiple\", \"disabled\"], [\"for\", \"fileInput\", 1, \"bg-blue-500\", \"hover:bg-blue-700\", \"text-white\", \"font-bold\", \"py-2\", \"px-4\", \"rounded\"], [\"class\", \"flex items-center space-x-2 mt-2\", 4, \"ngIf\"], [\"class\", \"w-full mt-2\", 4, \"ngIf\"], [2, \"color\", \"red\"], [1, \"flex\", \"items-center\", \"space-x-2\", \"mt-2\"], [1, \"text-gray-600\"], [\"type\", \"button\", 1, \"text-red-500\", \"hover:text-red-700\", 3, \"click\", \"disabled\"], [1, \"fa-solid\", \"fa-trash\"], [1, \"text-sm\"], [1, \"cursor-pointer\", \"text-blue-500\", 3, \"click\"], [1, \"w-full\", \"mt-2\"], [\"class\", \"flex flex-wrap mt-2 file-type-container\", 4, \"ngIf\"], [1, \"flex\", \"flex-wrap\", \"mt-2\", \"file-type-container\"], [\"class\", \"relative w-28 h-28 mr-3 mb-6 file-item\", 4, \"ngFor\", \"ngForOf\"], [1, \"relative\", \"w-28\", \"h-28\", \"mr-3\", \"mb-6\", \"file-item\"], [1, \"w-full\", \"h-full\", \"border\", \"border-gray-300\", \"rounded-lg\", \"shadow-sm\", \"bg-white\", \"hover:shadow-md\", \"transition-shadow\", \"duration-200\"], [\"class\", \"w-full h-full relative rounded-lg overflow-hidden\", 4, \"ngIf\"], [\"class\", \"w-full h-full flex flex-col items-center justify-center cursor-pointer rounded-lg relative\", 3, \"ngClass\", \"title\", \"click\", 4, \"ngIf\"], [1, \"absolute\", \"-bottom-6\", \"left-0\", \"w-full\", \"text-xs\", \"truncate\", \"px-1\", \"text-center\", \"text-gray-600\", 3, \"title\"], [\"title\", \"\\u79FB\\u9664\\u6A94\\u6848\", 1, \"absolute\", \"-top-2\", \"-right-2\", \"cursor-pointer\", \"bg-red-500\", \"text-white\", \"rounded-full\", \"w-6\", \"h-6\", \"flex\", \"items-center\", \"justify-center\", \"hover:bg-red-600\", \"transition-colors\", \"duration-200\", \"remove-btn\", 3, \"click\"], [1, \"fa\", \"fa-times\", \"text-xs\"], [1, \"w-full\", \"h-full\", \"relative\", \"rounded-lg\", \"overflow-hidden\"], [1, \"w-full\", \"h-full\", \"object-cover\", \"cursor-pointer\", 3, \"click\", \"error\", \"src\", \"title\"], [1, \"absolute\", \"top-1\", \"left-1\", \"bg-blue-500\", \"text-white\", \"text-xs\", \"px-1\", \"py-0.5\", \"rounded\", \"file-type-label\"], [1, \"fa\", \"fa-image\", \"mr-1\"], [1, \"w-full\", \"h-full\", \"flex\", \"flex-col\", \"items-center\", \"justify-center\", \"cursor-pointer\", \"rounded-lg\", \"relative\", 3, \"click\", \"ngClass\", \"title\"], [\"class\", \"text-xs text-gray-500 mt-1\", 4, \"ngIf\"], [1, \"text-xs\", \"text-gray-500\", \"mt-1\"], [1, \"space-y-2\"], [\"class\", \"flex items-center justify-between bg-gray-50 p-3 rounded-lg hover:bg-gray-100 transition-colors\", 4, \"ngFor\", \"ngForOf\"], [1, \"flex\", \"items-center\", \"justify-between\", \"bg-gray-50\", \"p-3\", \"rounded-lg\", \"hover:bg-gray-100\", \"transition-colors\"], [1, \"flex\", \"items-center\", \"space-x-3\", \"cursor-pointer\", 3, \"click\"], [1, \"flex-shrink-0\"], [1, \"flex-1\", \"min-w-0\"], [1, \"text-sm\", \"font-medium\", \"text-gray-700\", \"truncate\", \"block\"], [1, \"flex\", \"items-center\", \"space-x-2\", \"text-xs\", \"text-gray-500\"], [4, \"ngIf\"], [\"type\", \"button\", \"title\", \"\\u79FB\\u9664\\u6A94\\u6848\", 1, \"text-red-500\", \"hover:text-red-700\", \"p-1\", \"rounded\", \"hover:bg-red-50\", \"transition-colors\", 3, \"click\", \"disabled\"], [1, \"fa\", \"fa-trash\", \"text-sm\"]],\n      template: function FileUploadComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"div\", 1)(1, \"div\", 2)(2, \"div\", 3)(3, \"label\", 4);\n          i0.ɵɵtext(4);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(5, FileUploadComponent_h3_5_Template, 2, 1, \"h3\", 5);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(6, \"div\", 6)(7, \"input\", 7, 0);\n          i0.ɵɵlistener(\"change\", function FileUploadComponent_Template_input_change_7_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onFileSelected($event));\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(9, \"label\", 8);\n          i0.ɵɵelement(10, \"i\");\n          i0.ɵɵtext(11);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(12, FileUploadComponent_div_12_Template, 5, 2, \"div\", 9)(13, FileUploadComponent_div_13_Template, 4, 1, \"div\", 9)(14, FileUploadComponent_div_14_Template, 3, 2, \"div\", 10)(15, FileUploadComponent_div_15_Template, 3, 1, \"div\", 10);\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(3);\n          i0.ɵɵstyleProp(\"min-width\", ctx.labelMinWidth);\n          i0.ɵɵclassProp(\"required-field\", ctx.config.required);\n          i0.ɵɵadvance();\n          i0.ɵɵtextInterpolate1(\" \", ctx.config.label, \" \");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.config.helpText);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"accept\", ctx.config.acceptAttribute)(\"multiple\", ctx.config.multiple)(\"disabled\", ctx.config.disabled);\n          i0.ɵɵadvance(2);\n          i0.ɵɵclassProp(\"opacity-50\", ctx.config.disabled)(\"cursor-pointer\", !ctx.config.disabled);\n          i0.ɵɵadvance();\n          i0.ɵɵclassMap(ctx.config.buttonIcon + \" mr-2\");\n          i0.ɵɵadvance();\n          i0.ɵɵtextInterpolate1(\" \", ctx.config.buttonText, \" \");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.fileName && !ctx.config.multiple);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.currentFileUrl && !ctx.fileName && !ctx.config.multiple);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.config.multiple && ctx.config.showPreview);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.config.multiple && !ctx.config.showPreview && ctx.fileList && ctx.fileList.length > 0);\n        }\n      },\n      dependencies: [CommonModule, i4.NgClass, i4.NgForOf, i4.NgIf],\n      styles: [\".file-upload-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  width: 100%;\\n}\\n\\n.file-upload-wrapper[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  width: 100%;\\n}\\n\\n.file-upload-label-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  margin-right: 12px;\\n}\\n\\n.file-upload-label[_ngcontent-%COMP%] {\\n  min-width: 172px;\\n  color: #333;\\n  font-weight: 500;\\n}\\n\\n.required-field[_ngcontent-%COMP%]::after {\\n  content: \\\" *\\\";\\n  color: red;\\n}\\n\\n.file-upload-help-text[_ngcontent-%COMP%] {\\n  color: red;\\n  font-size: 14px;\\n  margin-top: 4px;\\n}\\n\\n.file-upload-input-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  flex: 1;\\n  padding: 0;\\n  align-items: flex-start;\\n}\\n\\n.file-input-hidden[_ngcontent-%COMP%] {\\n  display: none;\\n}\\n\\n.file-upload-button[_ngcontent-%COMP%] {\\n  background-color: #3b82f6;\\n  color: white;\\n  font-weight: bold;\\n  padding: 8px 16px;\\n  border-radius: 4px;\\n  border: none;\\n  cursor: pointer;\\n  transition: background-color 0.2s;\\n  display: inline-flex;\\n  align-items: center;\\n}\\n\\n.file-upload-button[_ngcontent-%COMP%]:hover:not(.disabled) {\\n  background-color: #1d4ed8;\\n}\\n\\n.file-upload-button.disabled[_ngcontent-%COMP%] {\\n  opacity: 0.5;\\n  cursor: not-allowed;\\n}\\n\\n.file-display-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  margin-top: 8px;\\n}\\n\\n.file-name[_ngcontent-%COMP%] {\\n  color: #6b7280;\\n  font-size: 14px;\\n}\\n\\n.file-delete-button[_ngcontent-%COMP%] {\\n  color: #ef4444;\\n  background: none;\\n  border: none;\\n  cursor: pointer;\\n  padding: 4px;\\n  transition: color 0.2s;\\n}\\n\\n.file-delete-button[_ngcontent-%COMP%]:hover {\\n  color: #dc2626;\\n}\\n\\n.current-file-link[_ngcontent-%COMP%] {\\n  color: #3b82f6;\\n  cursor: pointer;\\n  font-size: 14px;\\n  text-decoration: underline;\\n}\\n\\n.current-file-link[_ngcontent-%COMP%]:hover {\\n  color: #1d4ed8;\\n}\\n\\n.hidden[_ngcontent-%COMP%] {\\n  display: none;\\n}\\n\\n.upload-icon[_ngcontent-%COMP%] {\\n  margin-right: 8px;\\n}\\n\\n.delete-icon[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n}\\n/*# sourceMappingURL=data:application/json;base64,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 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["EventEmitter", "CommonModule", "BaseComponent", "EnumFileType", "environment", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate", "ctx_r1", "config", "helpText", "ɵɵlistener", "FileUploadComponent_div_12_Template_button_click_3_listener", "ɵɵrestoreView", "_r3", "ɵɵnextContext", "ɵɵresetView", "clearFile", "ɵɵelement", "fileName", "ɵɵproperty", "disabled", "FileUploadComponent_div_13_Template_a_click_2_listener", "_r4", "openFile", "currentFileUrl", "ɵɵtextInterpolate1", "FileUploadComponent_div_14_div_1_div_1_div_2_Template_img_click_1_listener", "_r6", "file_r7", "$implicit", "handleFileClick", "FileUploadComponent_div_14_div_1_div_1_div_2_Template_img_error_1_listener", "$event", "onImageError", "getImageSrc", "ɵɵsanitizeUrl", "CFileName", "getFileTypeIcon", "label", "getFileSize", "FileUploadComponent_div_14_div_1_div_1_div_3_Template_div_click_0_listener", "_r8", "ɵɵtemplate", "FileUploadComponent_div_14_div_1_div_1_div_3_span_4_Template", "bgColor", "ɵɵclassMap", "icon", "color", "FileUploadComponent_div_14_div_1_div_1_div_2_Template", "FileUploadComponent_div_14_div_1_div_1_div_3_Template", "FileUploadComponent_div_14_div_1_div_1_Template_span_click_6_listener", "i_r9", "_r5", "index", "removeFile", "isImage", "CFileType", "isImageFile", "isPdf", "isCad", "ɵɵclassProp", "FileUploadComponent_div_14_div_1_div_1_Template", "fileList", "FileUploadComponent_div_14_div_2_div_1_div_2_Template_img_click_1_listener", "_r10", "file_r11", "CFile", "data", "relativePath", "FileUploadComponent_div_14_div_2_div_1_div_2_Template_img_error_1_listener", "ɵɵpureFunction1", "_c1", "FileUploadComponent_div_14_div_2_div_1_div_3_Template_div_click_0_listener", "_r12", "FileUploadComponent_div_14_div_2_div_1_div_2_Template", "FileUploadComponent_div_14_div_2_div_1_div_3_Template", "isPDFString", "isCadString", "FileUploadComponent_div_14_div_2_div_1_Template", "existingFiles", "FileUploadComponent_div_14_div_1_Template", "FileUploadComponent_div_14_div_2_Template", "length", "file_r14", "FileUploadComponent_div_15_div_2_Template_div_click_1_listener", "_r13", "FileUploadComponent_div_15_div_2_span_10_Template", "FileUploadComponent_div_15_div_2_Template_button_click_11_listener", "i_r15", "FileUploadComponent_div_15_div_2_Template", "FileUploadComponent", "constructor", "_allow", "message", "fileService", "acceptedTypes", "acceptedFileRegex", "acceptAttribute", "required", "autoFillName", "buttonText", "buttonIcon", "maxFileSize", "multiple", "showPreview", "currentFileName", "labelMinWidth", "fileSelected", "fileCleared", "nameAutoFilled", "multiFileSelected", "fileRemoved", "ngOnInit", "onFileSelected", "event", "files", "target", "handleMultipleFiles", "handleSingleFile", "file", "test", "name", "acceptedFormats", "getAcceptedFormatsText", "showErrorMSG", "maxSizeInBytes", "size", "fileNameWithoutExtension", "substring", "lastIndexOf", "emit", "reader", "FileReader", "onload", "e", "fileType", "type", "startsWith", "JPG", "toLowerCase", "includes", "PDF", "result", "CName", "toString", "split", "Cimg", "CFileUpload", "fileInput", "nativeElement", "value", "readAsDataURL", "allowedTypes", "fileRegex", "firstFile", "newFiles", "processedCount", "i", "match", "endsWith", "push", "CFileBlood", "removeBase64Prefix", "url", "fullUrl", "BASE_WITHOUT_FILEROOT", "window", "open", "splice", "base64String", "prefixIndex", "indexOf", "str", "lowerStr", "console", "log", "displayName", "isImageByName", "isPdfByName", "isCadByName", "handleLocalFileData", "serverFileName", "getFileFromServer", "error", "handleLocalFile", "openImagePreview", "openPdfInNewWindow", "downloadBase64File", "base64Data", "byteCharacters", "atob", "byteNumbers", "Array", "charCodeAt", "byteArray", "Uint8Array", "blob", "Blob", "URL", "createObjectURL", "link", "document", "createElement", "href", "download", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "setTimeout", "revokeObjectURL", "getFile", "subscribe", "next", "downloadBlobFile", "blobUrl", "newWindow", "write", "close", "location", "imageExtensions", "extension", "pop", "encodeURIComponent", "isPDFByName", "warn", "style", "display", "container", "parentElement", "iconInfo", "newContent", "className", "innerHTML", "addEventListener", "extensions", "map", "join", "openPdfInBrowser", "fileUrl", "downloadFileDirectly", "imageUrl", "openNewTab", "toUpperCase", "Math", "floor", "units", "unitIndex", "toFixed", "ɵɵdirectiveInject", "i1", "AllowHelper", "i2", "MessageService", "i3", "FileService", "selectors", "viewQuery", "FileUploadComponent_Query", "rf", "ctx", "FileUploadComponent_h3_5_Template", "FileUploadComponent_Template_input_change_7_listener", "_r1", "FileUploadComponent_div_12_Template", "FileUploadComponent_div_13_Template", "FileUploadComponent_div_14_Template", "FileUploadComponent_div_15_Template", "ɵɵstyleProp", "i4", "Ng<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "styles"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\components\\file-upload\\file-upload.component.ts", "C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\components\\file-upload\\file-upload.component.html"], "sourcesContent": ["import { Component, ElementRef, EventEmitter, Input, OnInit, Output, ViewChild } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { AllowHelper } from 'src/app/shared/helper/allowHelper';\r\nimport { BaseComponent } from '../../components/base/baseComponent';\r\nimport { MessageService } from 'src/app/shared/services/message.service';\r\nimport { EnumFileType } from 'src/app/shared/enum/enumFileType';\r\nimport { environment } from 'src/environments/environment';\r\nimport { FileService } from 'src/services/api/services/File.service';\r\n\r\nexport interface FileUploadResult {\r\n  CName: string;\r\n  CFile: string;\r\n  Cimg: File;\r\n  CFileUpload: File;\r\n  CFileType: number;\r\n  fileName: string;\r\n}\r\n\r\nexport interface MultiFileUploadResult {\r\n  data: string;\r\n  CFileBlood: string;\r\n  CFileName: string;\r\n  CFileType: number;\r\n}\r\n\r\nexport interface FileUploadConfig {\r\n  acceptedTypes?: string[];\r\n  acceptedFileRegex?: RegExp;\r\n  acceptAttribute?: string;\r\n  label?: string;\r\n  helpText?: string;\r\n  required?: boolean;\r\n  disabled?: boolean;\r\n  autoFillName?: boolean;\r\n  buttonText?: string;\r\n  buttonIcon?: string;\r\n  maxFileSize?: number; // in MB\r\n  multiple?: boolean; // 是否支援多檔案上傳\r\n  showPreview?: boolean; // 是否顯示檔案預覽\r\n}\r\n\r\n@Component({\r\n  selector: 'app-file-upload',\r\n  templateUrl: './file-upload.component.html',\r\n  styleUrls: ['./file-upload.component.css'],\r\n  standalone: true,\r\n  imports: [CommonModule],\r\n})\r\nexport class FileUploadComponent extends BaseComponent implements OnInit {\r\n  @ViewChild('fileInput') fileInput!: ElementRef;\r\n  @Input() config: FileUploadConfig = {\r\n    acceptedTypes: ['image/jpeg', 'image/jpg', 'application/pdf'],\r\n    acceptedFileRegex: /pdf|jpg|jpeg|png/i,\r\n    acceptAttribute: 'image/jpeg, image/jpg, application/pdf',\r\n    label: '上傳檔案',\r\n    helpText: '*請上傳PDF格式',\r\n    required: false,\r\n    disabled: false,\r\n    autoFillName: false,\r\n    buttonText: '上傳',\r\n    buttonIcon: 'fa-solid fa-cloud-arrow-up',\r\n    maxFileSize: 10,\r\n    multiple: false,\r\n    showPreview: false\r\n  };\r\n  @Input() currentFileName: string | null = null;\r\n  @Input() currentFileUrl: string | null = null;\r\n  @Input() labelMinWidth: string = '172px';\r\n  @Input() fileList: MultiFileUploadResult[] = []; // 用於多檔案模式的檔案列表\r\n  @Input() existingFiles: any[] = []; // 已存在的檔案列表（編輯模式用）\r\n\r\n  @Output() fileSelected = new EventEmitter<FileUploadResult>();\r\n  @Output() fileCleared = new EventEmitter<void>();\r\n  @Output() nameAutoFilled = new EventEmitter<string>();\r\n  @Output() multiFileSelected = new EventEmitter<MultiFileUploadResult[]>(); // 多檔案選擇事件\r\n  @Output() fileRemoved = new EventEmitter<number>(); // 檔案移除事件\r\n\r\n  fileName: string | null = null;\r\n  constructor(\r\n    private _allow: AllowHelper,\r\n    private message: MessageService,\r\n    private fileService: FileService\r\n  ) {\r\n    super(_allow);\r\n  }\r\n\r\n  override ngOnInit(): void {\r\n    this.fileName = this.currentFileName;    // 設置預設配置\r\n    this.config = {\r\n      ...{\r\n        acceptedTypes: ['image/jpeg', 'image/jpg', 'application/pdf'],\r\n        acceptedFileRegex: /pdf|jpg|jpeg|png/i,\r\n        acceptAttribute: 'image/jpeg, image/jpg, application/pdf',\r\n        label: '上傳檔案',\r\n        helpText: '*請上傳PDF格式',\r\n        required: false,\r\n        disabled: false,\r\n        autoFillName: false,\r\n        buttonText: '上傳',\r\n        buttonIcon: 'fa-solid fa-cloud-arrow-up',\r\n        maxFileSize: 10,\r\n        multiple: false,\r\n        showPreview: false\r\n      },\r\n      ...this.config\r\n    };\r\n  }\r\n  onFileSelected(event: any) {\r\n    const files: FileList = event.target.files;\r\n\r\n    if (!files || files.length === 0) {\r\n      return;\r\n    }\r\n\r\n    if (this.config.multiple) {\r\n      this.handleMultipleFiles(files);\r\n    } else {\r\n      this.handleSingleFile(files[0]);\r\n    }\r\n  }\r\n\r\n  private handleSingleFile(file: File) {\r\n    // 檔案格式檢查\r\n    if (!this.config.acceptedFileRegex!.test(file.name)) {\r\n      const acceptedFormats = this.getAcceptedFormatsText();\r\n      this.message.showErrorMSG(`檔案格式錯誤，${acceptedFormats}`);\r\n      return;\r\n    }\r\n\r\n    // 檔案大小檢查\r\n    const maxSizeInBytes = (this.config.maxFileSize || 10) * 1024 * 1024;\r\n    if (file.size > maxSizeInBytes) {\r\n      this.message.showErrorMSG(`檔案大小超過限制（${this.config.maxFileSize}MB）`);\r\n      return;\r\n    }\r\n\r\n    this.fileName = file.name;\r\n\r\n    // 自動填入名稱功能\r\n    if (this.config.autoFillName) {\r\n      const fileNameWithoutExtension = file.name.substring(0, file.name.lastIndexOf('.')) || file.name;\r\n      this.nameAutoFilled.emit(fileNameWithoutExtension);\r\n    }\r\n\r\n    const reader = new FileReader();\r\n    reader.onload = (e: any) => {\r\n      // 判斷檔案類型\r\n      let fileType: number;\r\n      if (file.type.startsWith('image/')) {\r\n        fileType = EnumFileType.JPG; // 圖片\r\n      } else if (file.name.toLowerCase().includes('.dwg') || file.name.toLowerCase().includes('.dxf')) {\r\n        fileType = 3; // CAD檔案\r\n      } else {\r\n        fileType = EnumFileType.PDF; // PDF\r\n      }\r\n\r\n      const result: FileUploadResult = {\r\n        CName: file.name,\r\n        CFile: e.target?.result?.toString().split(',')[1],\r\n        Cimg: file.name.includes('pdf') ? file : file,\r\n        CFileUpload: file,\r\n        CFileType: fileType,\r\n        fileName: file.name\r\n      };\r\n\r\n      this.fileSelected.emit(result);\r\n\r\n      if (this.fileInput) {\r\n        this.fileInput.nativeElement.value = null;\r\n      }\r\n    };\r\n    reader.readAsDataURL(file);\r\n  }\r\n\r\n  private handleMultipleFiles(files: FileList) {\r\n    const allowedTypes = ['image/jpeg', 'image/jpg', 'application/pdf', 'application/acad', 'application/x-autocad', 'image/vnd.dwg', 'image/x-dwg'];\r\n    const fileRegex = this.config.acceptedFileRegex || /pdf|jpg|jpeg|png|dwg|dxf/i;\r\n    const maxSizeInBytes = (this.config.maxFileSize || 10) * 1024 * 1024;\r\n\r\n    // 如果是第一次選擇檔案且支援自動填入名稱\r\n    if (this.config.autoFillName && files.length > 0) {\r\n      const firstFile = files[0];\r\n      const fileName = firstFile.name;\r\n      const fileNameWithoutExtension = fileName.substring(0, fileName.lastIndexOf('.')) || fileName;\r\n      this.nameAutoFilled.emit(fileNameWithoutExtension);\r\n    }\r\n\r\n    const newFiles: MultiFileUploadResult[] = [];\r\n    let processedCount = 0;\r\n\r\n    for (let i = 0; i < files.length; i++) {\r\n      const file = files[i];\r\n\r\n      // 檔案格式檢查\r\n      if (!fileRegex.test(file.name)) {\r\n        this.message.showErrorMSG('檔案格式錯誤，僅限pdf、圖片檔、CAD檔案（dwg, dxf）');\r\n        processedCount++;\r\n        continue;\r\n      }\r\n\r\n      // 檔案大小檢查\r\n      if (file.size > maxSizeInBytes) {\r\n        this.message.showErrorMSG(`檔案 ${file.name} 大小超過限制（${this.config.maxFileSize}MB）`);\r\n        processedCount++;\r\n        continue;\r\n      }\r\n\r\n      if (allowedTypes.includes(file.type) || fileRegex.test(file.name)) {\r\n        const reader = new FileReader();\r\n        reader.onload = (e: any) => {\r\n          // 判斷檔案類型\r\n          let fileType = 1; // 預設為其他\r\n          const fileName = file.name.toLowerCase();\r\n\r\n          if (fileName.match(/\\.(jpg|jpeg|png)$/)) {\r\n            fileType = 2; // 圖片\r\n          } else if (fileName.endsWith('.pdf')) {\r\n            fileType = 1; // PDF\r\n          } else if (fileName.match(/\\.(dwg|dxf)$/)) {\r\n            fileType = 3; // CAD\r\n          }\r\n\r\n          newFiles.push({\r\n            data: e.target.result,\r\n            CFileBlood: this.removeBase64Prefix(e.target.result),\r\n            CFileName: file.name,\r\n            CFileType: fileType\r\n          });\r\n\r\n          processedCount++;\r\n          if (processedCount === files.length) {\r\n            this.fileList = [...this.fileList, ...newFiles];\r\n            this.multiFileSelected.emit(this.fileList);\r\n\r\n            if (this.fileInput) {\r\n              this.fileInput.nativeElement.value = null;\r\n            }\r\n          }\r\n        };\r\n        reader.readAsDataURL(file);\r\n      } else {\r\n        processedCount++;\r\n      }\r\n    }\r\n  }\r\n  clearFile() {\r\n    this.fileName = null;\r\n    this.fileCleared.emit();\r\n    if (this.fileInput) {\r\n      this.fileInput.nativeElement.value = null;\r\n    }\r\n  }\r\n  openFile(url: string) {\r\n    if (url) {\r\n      // 如果 URL 不是完整的 http/https 連結，則添加 base URL\r\n      const fullUrl = url.startsWith('http') ? url : `${environment.BASE_WITHOUT_FILEROOT}${url}`;\r\n      window.open(fullUrl, '_blank');\r\n    }\r\n  }\r\n\r\n  removeFile(index: number) {\r\n    this.fileList.splice(index, 1);\r\n    this.fileRemoved.emit(index);\r\n    this.multiFileSelected.emit(this.fileList);\r\n  }\r\n\r\n  private removeBase64Prefix(base64String: string): string {\r\n    const prefixIndex = base64String.indexOf(\",\");\r\n    if (prefixIndex !== -1) {\r\n      return base64String.substring(prefixIndex + 1);\r\n    }\r\n    return base64String;\r\n  }\r\n\r\n  isImage(fileType: number): boolean {\r\n    return fileType === 2;\r\n  }\r\n\r\n  isCad(fileType: number): boolean {\r\n    return fileType === 3;\r\n  }\r\n\r\n  isPdf(fileType: number): boolean {\r\n    return fileType === 1;\r\n  }\r\n\r\n  isPDFString(str: string): boolean {\r\n    if (str) {\r\n      return str.toLowerCase().endsWith(\".pdf\");\r\n    }\r\n    return false;\r\n  }\r\n\r\n  isCadString(str: string): boolean {\r\n    if (str) {\r\n      const lowerStr = str.toLowerCase();\r\n      return lowerStr.endsWith(\".dwg\") || lowerStr.endsWith(\".dxf\");\r\n    }\r\n    return false;\r\n  }  // 處理檔案點擊事件\r\n  handleFileClick(file: any) {\r\n    console.log('檔案點擊事件觸發:', file);\r\n    const fileName = file.CFileName || file.fileName || '';\r\n    const displayName = file.CFileName || fileName;\r\n\r\n    // 判斷檔案類型\r\n    const isImageByName = this.isImageFile(displayName);\r\n    const isPdfByName = this.isPDFString(displayName);\r\n    const isCadByName = this.isCadString(displayName);\r\n\r\n    console.log('檔案類型判斷:', { isImageByName, isPdfByName, isCadByName, displayName });\r\n\r\n    // 檢查是否為新上傳的檔案（有 data 屬性）\r\n    if (file.data) {\r\n      console.log('處理新上傳的檔案（base64）');\r\n      this.handleLocalFileData(file.data, displayName, isImageByName, isPdfByName, isCadByName);\r\n      return;\r\n    }\r\n\r\n    // 統一使用 GetFile API 取得檔案\r\n    const relativePath = file.relativePath || file.CFile;\r\n    const serverFileName = file.fileName || file.CFileName;\r\n\r\n    console.log('檔案路徑資訊:', { relativePath, serverFileName });\r\n\r\n    if (relativePath && serverFileName) {\r\n      console.log('使用 GetFile API 取得檔案');\r\n      this.getFileFromServer(relativePath, serverFileName, displayName, isImageByName, isPdfByName, isCadByName);\r\n    } else {\r\n      // 如果沒有路徑資訊，顯示錯誤訊息\r\n      console.error('檔案缺少路徑資訊，無法使用 getFile API:', file);\r\n      this.message.showErrorMSG('檔案路徑資訊不完整，無法開啟檔案');\r\n    }\r\n  }\r\n\r\n  // 處理本地檔案的後備方法 - 已廢棄，統一使用 getFile API\r\n  private handleLocalFile(file: any, isImage: boolean, isPdf: boolean, isCad: boolean) {\r\n    const fileName = file.CFileName || file.fileName || '';\r\n    console.error('檔案缺少必要的路徑資訊，無法使用 getFile API:', file);\r\n    this.message.showErrorMSG('檔案路徑資訊不完整，無法開啟檔案');\r\n  }\r\n\r\n  // 處理本地檔案資料（新上傳的檔案）\r\n  private handleLocalFileData(data: string, fileName: string, isImage: boolean, isPdf: boolean, isCad: boolean) {\r\n    if (isImage) {\r\n      // 圖片預覽\r\n      this.openImagePreview(data, fileName);\r\n    } else if (isPdf) {\r\n      // PDF 檔案另開視窗顯示\r\n      this.openPdfInNewWindow(data, fileName);\r\n    } else {\r\n      // 其他檔案（如CAD）轉換為blob並下載\r\n      this.downloadBase64File(data, fileName);\r\n    }\r\n  }\r\n\r\n  // 下載base64檔案\r\n  private downloadBase64File(base64Data: string, fileName: string) {\r\n    try {\r\n      // 移除data URL前綴（如果存在）\r\n      const base64String = base64Data.includes(',') ? base64Data.split(',')[1] : base64Data;\r\n\r\n      // 轉換為blob\r\n      const byteCharacters = atob(base64String);\r\n      const byteNumbers = new Array(byteCharacters.length);\r\n      for (let i = 0; i < byteCharacters.length; i++) {\r\n        byteNumbers[i] = byteCharacters.charCodeAt(i);\r\n      }\r\n      const byteArray = new Uint8Array(byteNumbers);\r\n      const blob = new Blob([byteArray]);\r\n\r\n      // 創建下載連結\r\n      const url = URL.createObjectURL(blob);\r\n      const link = document.createElement('a');\r\n      link.href = url;\r\n      link.download = fileName;\r\n      document.body.appendChild(link);\r\n      link.click();\r\n      document.body.removeChild(link);\r\n\r\n      // 清理URL\r\n      setTimeout(() => URL.revokeObjectURL(url), 1000);\r\n    } catch (error) {\r\n      console.error('下載檔案失敗:', error);\r\n      this.message.showErrorMSG('檔案下載失敗');\r\n    }\r\n  }\r\n\r\n  // 從後端取得檔案 blob\r\n  private getFileFromServer(relativePath: string, fileName: string, displayName: string, isImage: boolean, isPdf: boolean, isCad: boolean) {\r\n    this.fileService.getFile(relativePath, fileName).subscribe({\r\n      next: (blob: Blob) => {\r\n        const url = URL.createObjectURL(blob);\r\n\r\n        if (isImage) {\r\n          // 圖片預覽\r\n          this.openImagePreview(url, displayName);\r\n        } else if (isPdf) {\r\n          // PDF 檔案另開視窗顯示\r\n          this.openPdfInNewWindow(url, displayName);\r\n        } else {\r\n          // 其他檔案直接下載\r\n          this.downloadBlobFile(blob, displayName);\r\n        }\r\n\r\n        // 清理 URL (延遲清理以確保檔案能正確下載/預覽)\r\n        setTimeout(() => URL.revokeObjectURL(url), 10000);\r\n      },\r\n      error: (error) => {\r\n        console.error('取得檔案失敗:', error);\r\n        this.message.showErrorMSG('取得檔案失敗，請稍後再試');\r\n      }\r\n    });\r\n  }\r\n\r\n  // 在新視窗中開啟 PDF\r\n  private openPdfInNewWindow(blobUrl: string, fileName: string) {\r\n    try {\r\n      const newWindow = window.open('', '_blank');\r\n      if (newWindow) {\r\n        newWindow.document.write(`\r\n          <html>\r\n            <head>\r\n              <title>${fileName}</title>\r\n              <style>\r\n                body { margin: 0; padding: 0; }\r\n                iframe { width: 100vw; height: 100vh; border: none; }\r\n              </style>\r\n            </head>\r\n            <body>\r\n              <iframe src=\"${blobUrl}\" type=\"application/pdf\"></iframe>\r\n            </body>\r\n          </html>\r\n        `);\r\n        newWindow.document.close();\r\n      } else {\r\n        // 如果彈出視窗被阻擋，直接開啟 URL\r\n        window.location.href = blobUrl;\r\n      }\r\n    } catch (error) {\r\n      console.error('開啟 PDF 視窗失敗:', error);\r\n      // 後備方案：直接開啟 URL\r\n      window.open(blobUrl, '_blank');\r\n    }\r\n  }\r\n\r\n  // 下載 blob 檔案\r\n  private downloadBlobFile(blob: Blob, fileName: string) {\r\n    const url = URL.createObjectURL(blob);\r\n    const link = document.createElement('a');\r\n    link.href = url;\r\n    link.download = fileName;\r\n    document.body.appendChild(link);\r\n    link.click();\r\n    document.body.removeChild(link);\r\n\r\n    // 清理 URL\r\n    setTimeout(() => URL.revokeObjectURL(url), 1000);\r\n  }\r\n  // 判斷檔案是否為圖片（根據檔名）\r\n  isImageFile(fileName: string): boolean {\r\n    const imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'];\r\n    const extension = fileName.split('.').pop()?.toLowerCase();\r\n    return imageExtensions.includes(extension || '');\r\n  }\r\n  // 取得正確的圖片 src\r\n  getImageSrc(file: any): string {\r\n    const fileName = file.CFileName || file.fileName || '';\r\n\r\n    if (!fileName) {\r\n      return '';\r\n    }\r\n\r\n    // 如果檔案有 data 屬性（base64格式），直接使用\r\n    if (file.data) {\r\n      return file.data;\r\n    }\r\n\r\n    // 如果有相對路徑和檔案名，構造 API URL\r\n    if (file.relativePath && file.fileName) {\r\n      return `${environment.BASE_WITHOUT_FILEROOT}/api/File/GetFile?relativePath=${encodeURIComponent(file.relativePath)}&fileName=${encodeURIComponent(file.fileName)}`;\r\n    }\r\n\r\n    // 如果是現有檔案且有 CFile 路徑\r\n    if (file.CFile && !file.CFile.startsWith('data:')) {\r\n      return `${environment.BASE_WITHOUT_FILEROOT}${file.CFile}`;\r\n    }\r\n\r\n    // 後備方案：空字串\r\n    return '';\r\n  }\r\n\r\n  // HTML 模板中使用的方法\r\n  isImageByName(fileName: string): boolean {\r\n    return this.isImageFile(fileName);\r\n  }\r\n\r\n  isPDFByName(fileName: string): boolean {\r\n    return this.isPDFString(fileName);\r\n  }\r\n\r\n  isCadByName(fileName: string): boolean {\r\n    return this.isCadString(fileName);\r\n  }  // 圖片載入錯誤處理\r\n  onImageError(event: any, file: any) {\r\n    const fileName = file.CFileName || file.fileName || '檔案';\r\n    console.warn('圖片載入失敗:', fileName, file);\r\n    // 可以設置預設圖片或其他處理\r\n    event.target.style.display = 'none';\r\n\r\n    // 在圖片載入失敗時，顯示檔案類型圖示，但保留點擊事件\r\n    const container = event.target.parentElement;\r\n    if (container) {\r\n      const iconInfo = this.getFileTypeIcon(file);\r\n      \r\n      // 創建新的內容並綁定點擊事件\r\n      const newContent = document.createElement('div');\r\n      newContent.className = `w-full h-full flex flex-col items-center justify-center cursor-pointer rounded-lg ${iconInfo.bgColor}`;\r\n      newContent.innerHTML = `\r\n        <i class=\"${iconInfo.icon} text-4xl mb-2 ${iconInfo.color}\"></i>\r\n        <span class=\"text-xs font-medium ${iconInfo.color}\">${iconInfo.label}</span>\r\n      `;\r\n      \r\n      // 綁定點擊事件\r\n      newContent.addEventListener('click', () => {\r\n        this.handleFileClick(file);\r\n      });\r\n      \r\n      // 替換內容\r\n      container.innerHTML = '';\r\n      container.appendChild(newContent);\r\n    }\r\n  }\r\n\r\n  private getAcceptedFormatsText(): string {\r\n    const extensions = this.config.acceptAttribute?.split(',').map(type => {\r\n      if (type.includes('pdf')) return 'PDF';\r\n      if (type.includes('jpeg') || type.includes('jpg')) return 'JPG';\r\n      if (type.includes('png')) return 'PNG';\r\n      if (type.includes('.dwg')) return 'DWG';\r\n      if (type.includes('.dxf')) return 'DXF';\r\n      return type;\r\n    }).join('、');\r\n\r\n    return `僅限${extensions}格式`;\r\n  }\r\n  // 在瀏覽器中打開 PDF（已廢棄，統一使用 getFile API）\r\n  openPdfInBrowser(fileUrl: string) {\r\n    console.warn('openPdfInBrowser 方法已廢棄，請使用 getFileFromServer');\r\n    this.message.showErrorMSG('PDF 檢視功能已更新，請重新操作');\r\n  }\r\n  // 直接下載檔案（已廢棄，統一使用 getFile API）\r\n  downloadFileDirectly(fileUrl: string, fileName: string) {\r\n    console.warn('downloadFileDirectly 方法已廢棄，請使用 getFileFromServer');\r\n    this.message.showErrorMSG('檔案下載功能已更新，請重新操作');\r\n  }\r\n\r\n  // 開啟圖片預覽\r\n  private openImagePreview(imageUrl: string, fileName: string) {\r\n    try {\r\n      const newWindow = window.open('', '_blank');\r\n      if (newWindow) {\r\n        newWindow.document.write(`\r\n          <html>\r\n            <head>\r\n              <title>${fileName}</title>\r\n              <style>\r\n                body {\r\n                  margin: 0;\r\n                  padding: 20px;\r\n                  background-color: #f5f5f5;\r\n                  display: flex;\r\n                  justify-content: center;\r\n                  align-items: center;\r\n                  min-height: 100vh;\r\n                }\r\n                img {\r\n                  max-width: 100%;\r\n                  max-height: 100vh;\r\n                  object-fit: contain;\r\n                  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);\r\n                  background-color: white;\r\n                }\r\n                .title {\r\n                  position: fixed;\r\n                  top: 10px;\r\n                  left: 20px;\r\n                  background: rgba(0, 0, 0, 0.7);\r\n                  color: white;\r\n                  padding: 10px;\r\n                  border-radius: 4px;\r\n                  font-family: Arial, sans-serif;\r\n                }\r\n              </style>\r\n            </head>\r\n            <body>\r\n              <div class=\"title\">${fileName}</div>\r\n              <img src=\"${imageUrl}\" alt=\"${fileName}\" />\r\n            </body>\r\n          </html>\r\n        `);\r\n        newWindow.document.close();\r\n      }\r\n    } catch (error) {\r\n      console.error('開啟圖片預覽失敗:', error);\r\n      window.open(imageUrl, '_blank');\r\n    }\r\n  }\r\n\r\n  openNewTab(url: string) {\r\n    if (url) {\r\n      // 如果 URL 不是完整的 http/https 連結，則添加 base URL\r\n      const fullUrl = url.startsWith('http') ? url : `${environment.BASE_WITHOUT_FILEROOT}${url}`;\r\n      window.open(fullUrl, '_blank');\r\n    }\r\n  }\r\n\r\n  // 取得檔案類型圖示資訊\r\n  getFileTypeIcon(file: any): { icon: string, color: string, bgColor: string, label: string } {\r\n    const fileName = file.CFileName || file.fileName || '';\r\n    const fileType = file.CFileType;\r\n    const extension = fileName.split('.').pop()?.toLowerCase() || '';\r\n\r\n    // 圖片類型\r\n    if (fileType === 2 || this.isImageFile(fileName)) {\r\n      if (extension === 'jpg' || extension === 'jpeg') {\r\n        return {\r\n          icon: 'fa fa-file-image-o',\r\n          color: 'text-blue-600',\r\n          bgColor: 'bg-blue-50',\r\n          label: 'JPG'\r\n        };\r\n      } else if (extension === 'png') {\r\n        return {\r\n          icon: 'fa fa-file-image-o',\r\n          color: 'text-purple-600',\r\n          bgColor: 'bg-purple-50',\r\n          label: 'PNG'\r\n        };\r\n      } else {\r\n        return {\r\n          icon: 'fa fa-file-image-o',\r\n          color: 'text-blue-600',\r\n          bgColor: 'bg-blue-50',\r\n          label: '圖片'\r\n        };\r\n      }\r\n    }\r\n\r\n    // PDF類型\r\n    if (fileType === 1 || this.isPDFString(fileName)) {\r\n      return {\r\n        icon: 'fa fa-file-pdf-o',\r\n        color: 'text-red-600',\r\n        bgColor: 'bg-red-50',\r\n        label: 'PDF'\r\n      };\r\n    }\r\n\r\n    // CAD類型\r\n    if (fileType === 3 || this.isCadString(fileName)) {\r\n      if (extension === 'dwg') {\r\n        return {\r\n          icon: 'fa fa-cube',\r\n          color: 'text-green-600',\r\n          bgColor: 'bg-green-50',\r\n          label: 'DWG'\r\n        };\r\n      } else if (extension === 'dxf') {\r\n        return {\r\n          icon: 'fa fa-cube',\r\n          color: 'text-emerald-600',\r\n          bgColor: 'bg-emerald-50',\r\n          label: 'DXF'\r\n        };\r\n      } else {\r\n        return {\r\n          icon: 'fa fa-cube',\r\n          color: 'text-green-600',\r\n          bgColor: 'bg-green-50',\r\n          label: 'CAD'\r\n        };\r\n      }\r\n    }\r\n\r\n    // Office文件類型\r\n    if (extension === 'doc' || extension === 'docx') {\r\n      return {\r\n        icon: 'fa fa-file-word-o',\r\n        color: 'text-blue-700',\r\n        bgColor: 'bg-blue-50',\r\n        label: 'Word'\r\n      };\r\n    }\r\n\r\n    if (extension === 'xls' || extension === 'xlsx') {\r\n      return {\r\n        icon: 'fa fa-file-excel-o',\r\n        color: 'text-green-700',\r\n        bgColor: 'bg-green-50',\r\n        label: 'Excel'\r\n      };\r\n    }\r\n\r\n    if (extension === 'ppt' || extension === 'pptx') {\r\n      return {\r\n        icon: 'fa fa-file-powerpoint-o',\r\n        color: 'text-orange-600',\r\n        bgColor: 'bg-orange-50',\r\n        label: 'PPT'\r\n      };\r\n    }\r\n\r\n    // 壓縮檔案\r\n    if (['zip', 'rar', '7z', 'tar', 'gz'].includes(extension)) {\r\n      return {\r\n        icon: 'fa fa-file-archive-o',\r\n        color: 'text-yellow-600',\r\n        bgColor: 'bg-yellow-50',\r\n        label: extension.toUpperCase()\r\n      };\r\n    }\r\n\r\n    // 文字檔案\r\n    if (['txt', 'log', 'md'].includes(extension)) {\r\n      return {\r\n        icon: 'fa fa-file-text-o',\r\n        color: 'text-gray-600',\r\n        bgColor: 'bg-gray-50',\r\n        label: extension.toUpperCase()\r\n      };\r\n    }\r\n\r\n    // 預設檔案類型\r\n    return {\r\n      icon: 'fa fa-file-o',\r\n      color: 'text-gray-500',\r\n      bgColor: 'bg-gray-50',\r\n      label: extension ? extension.toUpperCase() : '檔案'\r\n    };\r\n  }\r\n\r\n  // 取得檔案大小格式化字串\r\n  getFileSize(file: any): string {\r\n    let size = 0;\r\n    \r\n    if (file.size) {\r\n      size = file.size;\r\n    } else if (file.CFileBlood) {\r\n      // base64 字串大小估算 (約為實際檔案大小的 4/3)\r\n      size = Math.floor(file.CFileBlood.length * 0.75);\r\n    }\r\n\r\n    if (size === 0) return '';\r\n\r\n    const units = ['B', 'KB', 'MB', 'GB'];\r\n    let unitIndex = 0;\r\n    \r\n    while (size >= 1024 && unitIndex < units.length - 1) {\r\n      size /= 1024;\r\n      unitIndex++;\r\n    }\r\n\r\n    return `${size.toFixed(unitIndex === 0 ? 0 : 1)} ${units[unitIndex]}`;\r\n  }\r\n}\r\n", "<div class=\"form-group d-flex align-items-center\">\r\n  <div class=\"form-group d-flex align-items-center w-full\">\r\n    <div class=\"d-flex flex-col mr-3\">\r\n      <label for=\"file\" class=\"label col-3\" [class.required-field]=\"config.required\" [style.min-width]=\"labelMinWidth\">\r\n        {{ config.label }}\r\n      </label>\r\n      <h3 style=\"color:red;\" *ngIf=\"config.helpText\">{{ config.helpText }}</h3>\r\n    </div>\r\n\r\n    <div class=\"flex flex-col col-9 px-0 items-start\">\r\n      <input #fileInput type=\"file\" id=\"fileInput\" [accept]=\"config.acceptAttribute\" [multiple]=\"config.multiple\"\r\n        class=\"hidden\" style=\"display: none\" (change)=\"onFileSelected($event)\" [disabled]=\"config.disabled\">\r\n\r\n      <label for=\"fileInput\" [class.opacity-50]=\"config.disabled\" [class.cursor-pointer]=\"!config.disabled\"\r\n        class=\"bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded\">\r\n        <i [class]=\"config.buttonIcon + ' mr-2'\"></i> {{ config.buttonText }}\r\n      </label>\r\n\r\n      <!-- 單檔案模式：已選擇的檔案顯示 -->\r\n      <div class=\"flex items-center space-x-2 mt-2\" *ngIf=\"fileName && !config.multiple\">\r\n        <span class=\"text-gray-600\">{{ fileName }}</span>\r\n        <button type=\"button\" (click)=\"clearFile()\" class=\"text-red-500 hover:text-red-700\"\r\n          [disabled]=\"config.disabled\">\r\n          <i class=\"fa-solid fa-trash\"></i>\r\n        </button>\r\n      </div>\r\n\r\n      <!-- 單檔案模式：已存在的檔案連結 -->\r\n      <div class=\"flex items-center space-x-2 mt-2\" *ngIf=\"currentFileUrl && !fileName && !config.multiple\">\r\n        <span class=\"text-sm\">\r\n          <a (click)=\"openFile(currentFileUrl)\" class=\"cursor-pointer text-blue-500\">\r\n            {{ currentFileUrl }}\r\n          </a>\r\n        </span>\r\n      </div> <!-- 多檔案模式：檔案預覽和列表 -->\r\n      <div class=\"w-full mt-2\" *ngIf=\"config.multiple && config.showPreview\">\r\n        <!-- 上傳的檔案預覽 -->\r\n        <div class=\"flex flex-wrap mt-2 file-type-container\" *ngIf=\"fileList && fileList.length > 0\">\r\n          <div *ngFor=\"let file of fileList; let i = index\" class=\"relative w-28 h-28 mr-3 mb-6 file-item\">\r\n            <div\r\n              class=\"w-full h-full border border-gray-300 rounded-lg shadow-sm bg-white hover:shadow-md transition-shadow duration-200\">\r\n\r\n              <!-- 圖片類型 - 顯示實際圖片 -->\r\n              <div\r\n                *ngIf=\"isImage(file.CFileType) || (isImageFile(file.CFileName) && !isPdf(file.CFileType) && !isCad(file.CFileType))\"\r\n                class=\"w-full h-full relative rounded-lg overflow-hidden\">\r\n                <img class=\"w-full h-full object-cover cursor-pointer\" [src]=\"getImageSrc(file)\"\r\n                  (click)=\"handleFileClick(file)\" [title]=\"'點擊放大預覽: ' + file.CFileName\"\r\n                  (error)=\"onImageError($event, file)\">\r\n                <div class=\"absolute top-1 left-1 bg-blue-500 text-white text-xs px-1 py-0.5 rounded file-type-label\">\r\n                  <i class=\"fa fa-image mr-1\"></i>{{ getFileTypeIcon(file).label }}\r\n                </div>\r\n              </div>\r\n\r\n              <!-- 非圖片類型 - 顯示檔案類型圖示 -->\r\n              <div\r\n                *ngIf=\"!isImage(file.CFileType) && (!isImageFile(file.CFileName) || isPdf(file.CFileType) || isCad(file.CFileType))\"\r\n                class=\"w-full h-full flex flex-col items-center justify-center cursor-pointer rounded-lg relative\"\r\n                [ngClass]=\"getFileTypeIcon(file).bgColor\" (click)=\"handleFileClick(file)\"\r\n                [title]=\"'點擊開啟: ' + file.CFileName\">\r\n\r\n                <!-- 檔案圖示 -->\r\n                <i [class]=\"getFileTypeIcon(file).icon + ' text-4xl mb-2 ' + getFileTypeIcon(file).color\"></i>\r\n\r\n                <!-- 檔案類型標籤 -->\r\n                <span [class]=\"'text-xs font-medium ' + getFileTypeIcon(file).color\">\r\n                  {{ getFileTypeIcon(file).label }}\r\n                </span>\r\n\r\n                <!-- 檔案大小 -->\r\n                <span class=\"text-xs text-gray-500 mt-1\" *ngIf=\"getFileSize(file)\">\r\n                  {{ getFileSize(file) }}\r\n                </span>\r\n              </div>\r\n            </div>\r\n\r\n            <!-- 檔案名稱 -->\r\n            <p class=\"absolute -bottom-6 left-0 w-full text-xs truncate px-1 text-center text-gray-600\"\r\n              [title]=\"file.CFileName\">{{ file.CFileName }}</p>\r\n\r\n            <!-- 移除按鈕 -->\r\n            <span\r\n              class=\"absolute -top-2 -right-2 cursor-pointer bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center hover:bg-red-600 transition-colors duration-200 remove-btn\"\r\n              (click)=\"removeFile(i)\" title=\"移除檔案\" [class.hidden]=\"config.disabled\">\r\n              <i class=\"fa fa-times text-xs\"></i>\r\n            </span>\r\n          </div>\r\n        </div> <!-- 已存在的檔案預覽（編輯模式） -->\r\n        <div class=\"flex flex-wrap mt-2 file-type-container\" *ngIf=\"existingFiles && existingFiles.length > 0\">\r\n          <div *ngFor=\"let file of existingFiles; let i = index\" class=\"relative w-28 h-28 mr-3 mb-6 file-item\">\r\n            <div\r\n              class=\"w-full h-full border border-gray-300 rounded-lg shadow-sm bg-white hover:shadow-md transition-shadow duration-200\">\r\n\r\n              <!-- 圖片類型 - 顯示實際圖片 -->\r\n              <div\r\n                *ngIf=\"isImageFile(file.CFileName || file.CFile) && !isPDFString(file.CFileName || file.CFile) && !isCadString(file.CFileName || file.CFile)\"\r\n                class=\"w-full h-full relative rounded-lg overflow-hidden\">\r\n                <img class=\"w-full h-full object-cover cursor-pointer\" [src]=\"getImageSrc(file)\"\r\n                  (click)=\"handleFileClick({CFileName: file.CFileName, CFile: file.CFile, data: getImageSrc(file), relativePath: file.relativePath, fileName: file.fileName})\"\r\n                  [title]=\"'點擊放大預覽: ' + file.CFileName\" (error)=\"onImageError($event, file)\">\r\n                <div class=\"absolute top-1 left-1 bg-blue-500 text-white text-xs px-1 py-0.5 rounded file-type-label\">\r\n                  <i class=\"fa fa-image mr-1\"></i>{{ getFileTypeIcon({CFileName: file.CFileName}).label }}\r\n                </div>\r\n              </div>\r\n\r\n              <!-- 非圖片類型 - 顯示檔案類型圖示 -->\r\n              <div\r\n                *ngIf=\"!isImageFile(file.CFileName || file.CFile) || isPDFString(file.CFileName || file.CFile) || isCadString(file.CFileName || file.CFile)\"\r\n                class=\"w-full h-full flex flex-col items-center justify-center cursor-pointer rounded-lg relative\"\r\n                [ngClass]=\"getFileTypeIcon({CFileName: file.CFileName}).bgColor\"\r\n                (click)=\"handleFileClick({CFileName: file.CFileName, CFile: file.CFile, data: file.CFile, relativePath: file.relativePath, fileName: file.fileName})\"\r\n                [title]=\"'點擊開啟: ' + file.CFileName\">\r\n\r\n                <!-- 檔案圖示 -->\r\n                <i\r\n                  [class]=\"getFileTypeIcon({CFileName: file.CFileName}).icon + ' text-4xl mb-2 ' + getFileTypeIcon({CFileName: file.CFileName}).color\"></i>\r\n\r\n                <!-- 檔案類型標籤 -->\r\n                <span [class]=\"'text-xs font-medium ' + getFileTypeIcon({CFileName: file.CFileName}).color\">\r\n                  {{ getFileTypeIcon({CFileName: file.CFileName}).label }}\r\n                </span>\r\n              </div>\r\n            </div>\r\n\r\n            <!-- 檔案名稱 -->\r\n            <p class=\"absolute -bottom-6 left-0 w-full text-xs truncate px-1 text-center text-gray-600\"\r\n              [title]=\"file.CFileName\">{{ file.CFileName }}</p>\r\n          </div>\r\n        </div>\r\n      </div> <!-- 多檔案模式：簡單列表模式 -->\r\n      <div class=\"w-full mt-2\" *ngIf=\"config.multiple && !config.showPreview && fileList && fileList.length > 0\">\r\n        <div class=\"space-y-2\">\r\n          <div *ngFor=\"let file of fileList; let i = index\"\r\n            class=\"flex items-center justify-between bg-gray-50 p-3 rounded-lg hover:bg-gray-100 transition-colors\">\r\n            <div class=\"flex items-center space-x-3 cursor-pointer\" (click)=\"handleFileClick(file)\">\r\n              <!-- 檔案類型圖示 -->\r\n              <div class=\"flex-shrink-0\">\r\n                <i [class]=\"getFileTypeIcon(file).icon + ' text-xl ' + getFileTypeIcon(file).color\"></i>\r\n              </div>\r\n\r\n              <!-- 檔案資訊 -->\r\n              <div class=\"flex-1 min-w-0\">\r\n                <span class=\"text-sm font-medium text-gray-700 truncate block\">{{ file.CFileName }}</span>\r\n                <div class=\"flex items-center space-x-2 text-xs text-gray-500\">\r\n                  <span>{{ getFileTypeIcon(file).label }}</span>\r\n                  <span *ngIf=\"getFileSize(file)\">• {{ getFileSize(file) }}</span>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            <!-- 移除按鈕 -->\r\n            <button type=\"button\" (click)=\"removeFile(i)\"\r\n              class=\"text-red-500 hover:text-red-700 p-1 rounded hover:bg-red-50 transition-colors\"\r\n              [disabled]=\"config.disabled\" [class.hidden]=\"config.disabled\" title=\"移除檔案\">\r\n              <i class=\"fa fa-trash text-sm\"></i>\r\n            </button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</div>"], "mappings": "AAAA,SAAgCA,YAAY,QAA0C,eAAe;AACrG,SAASC,YAAY,QAAQ,iBAAiB;AAE9C,SAASC,aAAa,QAAQ,qCAAqC;AAEnE,SAASC,YAAY,QAAQ,kCAAkC;AAC/D,SAASC,WAAW,QAAQ,8BAA8B;;;;;;;;;;;;ICApDC,EAAA,CAAAC,cAAA,aAA+C;IAAAD,EAAA,CAAAE,MAAA,GAAqB;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;IAA1BH,EAAA,CAAAI,SAAA,EAAqB;IAArBJ,EAAA,CAAAK,iBAAA,CAAAC,MAAA,CAAAC,MAAA,CAAAC,QAAA,CAAqB;;;;;;IAclER,EADF,CAAAC,cAAA,cAAmF,eACrD;IAAAD,EAAA,CAAAE,MAAA,GAAc;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACjDH,EAAA,CAAAC,cAAA,iBAC+B;IADTD,EAAA,CAAAS,UAAA,mBAAAC,4DAAA;MAAAV,EAAA,CAAAW,aAAA,CAAAC,GAAA;MAAA,MAAAN,MAAA,GAAAN,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAc,WAAA,CAASR,MAAA,CAAAS,SAAA,EAAW;IAAA,EAAC;IAEzCf,EAAA,CAAAgB,SAAA,YAAiC;IAErChB,EADE,CAAAG,YAAA,EAAS,EACL;;;;IALwBH,EAAA,CAAAI,SAAA,GAAc;IAAdJ,EAAA,CAAAK,iBAAA,CAAAC,MAAA,CAAAW,QAAA,CAAc;IAExCjB,EAAA,CAAAI,SAAA,EAA4B;IAA5BJ,EAAA,CAAAkB,UAAA,aAAAZ,MAAA,CAAAC,MAAA,CAAAY,QAAA,CAA4B;;;;;;IAQ5BnB,EAFJ,CAAAC,cAAA,cAAsG,eAC9E,YACuD;IAAxED,EAAA,CAAAS,UAAA,mBAAAW,uDAAA;MAAApB,EAAA,CAAAW,aAAA,CAAAU,GAAA;MAAA,MAAAf,MAAA,GAAAN,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAc,WAAA,CAASR,MAAA,CAAAgB,QAAA,CAAAhB,MAAA,CAAAiB,cAAA,CAAwB;IAAA,EAAC;IACnCvB,EAAA,CAAAE,MAAA,GACF;IAEJF,EAFI,CAAAG,YAAA,EAAI,EACC,EACH;;;;IAHAH,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAwB,kBAAA,MAAAlB,MAAA,CAAAiB,cAAA,MACF;;;;;;IAcMvB,EAHF,CAAAC,cAAA,cAE4D,cAGnB;IAArCD,EADA,CAAAS,UAAA,mBAAAgB,2EAAA;MAAAzB,EAAA,CAAAW,aAAA,CAAAe,GAAA;MAAA,MAAAC,OAAA,GAAA3B,EAAA,CAAAa,aAAA,GAAAe,SAAA;MAAA,MAAAtB,MAAA,GAAAN,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAc,WAAA,CAASR,MAAA,CAAAuB,eAAA,CAAAF,OAAA,CAAqB;IAAA,EAAC,mBAAAG,2EAAAC,MAAA;MAAA/B,EAAA,CAAAW,aAAA,CAAAe,GAAA;MAAA,MAAAC,OAAA,GAAA3B,EAAA,CAAAa,aAAA,GAAAe,SAAA;MAAA,MAAAtB,MAAA,GAAAN,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAc,WAAA,CACtBR,MAAA,CAAA0B,YAAA,CAAAD,MAAA,EAAAJ,OAAA,CAA0B;IAAA,EAAC;IAFtC3B,EAAA,CAAAG,YAAA,EAEuC;IACvCH,EAAA,CAAAC,cAAA,cAAsG;IACpGD,EAAA,CAAAgB,SAAA,YAAgC;IAAAhB,EAAA,CAAAE,MAAA,GAClC;IACFF,EADE,CAAAG,YAAA,EAAM,EACF;;;;;IANmDH,EAAA,CAAAI,SAAA,EAAyB;IAC9CJ,EADqB,CAAAkB,UAAA,QAAAZ,MAAA,CAAA2B,WAAA,CAAAN,OAAA,GAAA3B,EAAA,CAAAkC,aAAA,CAAyB,qDAAAP,OAAA,CAAAQ,SAAA,CACT;IAGrCnC,EAAA,CAAAI,SAAA,GAClC;IADkCJ,EAAA,CAAAwB,kBAAA,KAAAlB,MAAA,CAAA8B,eAAA,CAAAT,OAAA,EAAAU,KAAA,MAClC;;;;;IAmBArC,EAAA,CAAAC,cAAA,eAAmE;IACjED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IADLH,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAwB,kBAAA,MAAAlB,MAAA,CAAAgC,WAAA,CAAAX,OAAA,OACF;;;;;;IAjBF3B,EAAA,CAAAC,cAAA,cAIsC;IADMD,EAAA,CAAAS,UAAA,mBAAA8B,2EAAA;MAAAvC,EAAA,CAAAW,aAAA,CAAA6B,GAAA;MAAA,MAAAb,OAAA,GAAA3B,EAAA,CAAAa,aAAA,GAAAe,SAAA;MAAA,MAAAtB,MAAA,GAAAN,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAc,WAAA,CAASR,MAAA,CAAAuB,eAAA,CAAAF,OAAA,CAAqB;IAAA,EAAC;IAIzE3B,EAAA,CAAAgB,SAAA,QAA8F;IAG9FhB,EAAA,CAAAC,cAAA,WAAqE;IACnED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAGPH,EAAA,CAAAyC,UAAA,IAAAC,4DAAA,mBAAmE;IAGrE1C,EAAA,CAAAG,YAAA,EAAM;;;;;IAdJH,EADA,CAAAkB,UAAA,YAAAZ,MAAA,CAAA8B,eAAA,CAAAT,OAAA,EAAAgB,OAAA,CAAyC,yCAAAhB,OAAA,CAAAQ,SAAA,CACN;IAGhCnC,EAAA,CAAAI,SAAA,EAAsF;IAAtFJ,EAAA,CAAA4C,UAAA,CAAAtC,MAAA,CAAA8B,eAAA,CAAAT,OAAA,EAAAkB,IAAA,uBAAAvC,MAAA,CAAA8B,eAAA,CAAAT,OAAA,EAAAmB,KAAA,CAAsF;IAGnF9C,EAAA,CAAAI,SAAA,EAA8D;IAA9DJ,EAAA,CAAA4C,UAAA,0BAAAtC,MAAA,CAAA8B,eAAA,CAAAT,OAAA,EAAAmB,KAAA,CAA8D;IAClE9C,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAwB,kBAAA,MAAAlB,MAAA,CAAA8B,eAAA,CAAAT,OAAA,EAAAU,KAAA,MACF;IAG0CrC,EAAA,CAAAI,SAAA,EAAuB;IAAvBJ,EAAA,CAAAkB,UAAA,SAAAZ,MAAA,CAAAgC,WAAA,CAAAX,OAAA,EAAuB;;;;;;IA/BrE3B,EADF,CAAAC,cAAA,cAAiG,cAE6B;IAe1HD,EAZA,CAAAyC,UAAA,IAAAM,qDAAA,kBAE4D,IAAAC,qDAAA,kBActB;IAexChD,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAAC,cAAA,YAC2B;IAAAD,EAAA,CAAAE,MAAA,GAAoB;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAGnDH,EAAA,CAAAC,cAAA,eAEwE;IAAtED,EAAA,CAAAS,UAAA,mBAAAwC,sEAAA;MAAA,MAAAC,IAAA,GAAAlD,EAAA,CAAAW,aAAA,CAAAwC,GAAA,EAAAC,KAAA;MAAA,MAAA9C,MAAA,GAAAN,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAc,WAAA,CAASR,MAAA,CAAA+C,UAAA,CAAAH,IAAA,CAAa;IAAA,EAAC;IACvBlD,EAAA,CAAAgB,SAAA,YAAmC;IAEvChB,EADE,CAAAG,YAAA,EAAO,EACH;;;;;IA1CCH,EAAA,CAAAI,SAAA,GAAkH;IAAlHJ,EAAA,CAAAkB,UAAA,SAAAZ,MAAA,CAAAgD,OAAA,CAAA3B,OAAA,CAAA4B,SAAA,KAAAjD,MAAA,CAAAkD,WAAA,CAAA7B,OAAA,CAAAQ,SAAA,MAAA7B,MAAA,CAAAmD,KAAA,CAAA9B,OAAA,CAAA4B,SAAA,MAAAjD,MAAA,CAAAoD,KAAA,CAAA/B,OAAA,CAAA4B,SAAA,EAAkH;IAYlHvD,EAAA,CAAAI,SAAA,EAAkH;IAAlHJ,EAAA,CAAAkB,UAAA,UAAAZ,MAAA,CAAAgD,OAAA,CAAA3B,OAAA,CAAA4B,SAAA,OAAAjD,MAAA,CAAAkD,WAAA,CAAA7B,OAAA,CAAAQ,SAAA,KAAA7B,MAAA,CAAAmD,KAAA,CAAA9B,OAAA,CAAA4B,SAAA,KAAAjD,MAAA,CAAAoD,KAAA,CAAA/B,OAAA,CAAA4B,SAAA,GAAkH;IAsBrHvD,EAAA,CAAAI,SAAA,EAAwB;IAAxBJ,EAAA,CAAAkB,UAAA,UAAAS,OAAA,CAAAQ,SAAA,CAAwB;IAACnC,EAAA,CAAAI,SAAA,EAAoB;IAApBJ,EAAA,CAAAK,iBAAA,CAAAsB,OAAA,CAAAQ,SAAA,CAAoB;IAKRnC,EAAA,CAAAI,SAAA,EAAgC;IAAhCJ,EAAA,CAAA2D,WAAA,WAAArD,MAAA,CAAAC,MAAA,CAAAY,QAAA,CAAgC;;;;;IA9C3EnB,EAAA,CAAAC,cAAA,cAA6F;IAC3FD,EAAA,CAAAyC,UAAA,IAAAmB,+CAAA,kBAAiG;IAiDnG5D,EAAA,CAAAG,YAAA,EAAM;;;;IAjDkBH,EAAA,CAAAI,SAAA,EAAa;IAAbJ,EAAA,CAAAkB,UAAA,YAAAZ,MAAA,CAAAuD,QAAA,CAAa;;;;;;IA2D7B7D,EAHF,CAAAC,cAAA,cAE4D,cAGmB;IAArCD,EADtC,CAAAS,UAAA,mBAAAqD,2EAAA;MAAA9D,EAAA,CAAAW,aAAA,CAAAoD,IAAA;MAAA,MAAAC,QAAA,GAAAhE,EAAA,CAAAa,aAAA,GAAAe,SAAA;MAAA,MAAAtB,MAAA,GAAAN,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAc,WAAA,CAASR,MAAA,CAAAuB,eAAA,CAAgB;QAAAM,SAAA,EAAA6B,QAAA,CAAA7B,SAAA;QAAA8B,KAAA,EAAAD,QAAA,CAAAC,KAAA;QAAAC,IAAA,EAAqD5D,MAAA,CAAA2B,WAAA,CAAA+B,QAAA,CAAiB;QAAAG,YAAA,EAAAH,QAAA,CAAAG,YAAA;QAAAlD,QAAA,EAAA+C,QAAA,CAAA/C;MAAA,CAA2D,CAAC;IAAA,EAAC,mBAAAmD,2EAAArC,MAAA;MAAA/B,EAAA,CAAAW,aAAA,CAAAoD,IAAA;MAAA,MAAAC,QAAA,GAAAhE,EAAA,CAAAa,aAAA,GAAAe,SAAA;MAAA,MAAAtB,MAAA,GAAAN,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAc,WAAA,CAC7GR,MAAA,CAAA0B,YAAA,CAAAD,MAAA,EAAAiC,QAAA,CAA0B;IAAA,EAAC;IAF5EhE,EAAA,CAAAG,YAAA,EAE6E;IAC7EH,EAAA,CAAAC,cAAA,cAAsG;IACpGD,EAAA,CAAAgB,SAAA,YAAgC;IAAAhB,EAAA,CAAAE,MAAA,GAClC;IACFF,EADE,CAAAG,YAAA,EAAM,EACF;;;;;IANmDH,EAAA,CAAAI,SAAA,EAAyB;IAE9EJ,EAFqD,CAAAkB,UAAA,QAAAZ,MAAA,CAAA2B,WAAA,CAAA+B,QAAA,GAAAhE,EAAA,CAAAkC,aAAA,CAAyB,qDAAA8B,QAAA,CAAA7B,SAAA,CAEzC;IAELnC,EAAA,CAAAI,SAAA,GAClC;IADkCJ,EAAA,CAAAwB,kBAAA,KAAAlB,MAAA,CAAA8B,eAAA,CAAApC,EAAA,CAAAqE,eAAA,IAAAC,GAAA,EAAAN,QAAA,CAAA7B,SAAA,GAAAE,KAAA,MAClC;;;;;;IAIFrC,EAAA,CAAAC,cAAA,cAKsC;IADpCD,EAAA,CAAAS,UAAA,mBAAA8D,2EAAA;MAAAvE,EAAA,CAAAW,aAAA,CAAA6D,IAAA;MAAA,MAAAR,QAAA,GAAAhE,EAAA,CAAAa,aAAA,GAAAe,SAAA;MAAA,MAAAtB,MAAA,GAAAN,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAc,WAAA,CAASR,MAAA,CAAAuB,eAAA,CAAgB;QAAAM,SAAA,EAAA6B,QAAA,CAAA7B,SAAA;QAAA8B,KAAA,EAAAD,QAAA,CAAAC,KAAA;QAAAC,IAAA,EAAAF,QAAA,CAAAC,KAAA;QAAAE,YAAA,EAAAH,QAAA,CAAAG,YAAA;QAAAlD,QAAA,EAAA+C,QAAA,CAAA/C;MAAA,CAA0H,CAAC;IAAA,EAAC;IAIrJjB,EAAA,CAAAgB,SAAA,QAC2I;IAG3IhB,EAAA,CAAAC,cAAA,WAA4F;IAC1FD,EAAA,CAAAE,MAAA,GACF;IACFF,EADE,CAAAG,YAAA,EAAO,EACH;;;;;IAVJH,EAFA,CAAAkB,UAAA,YAAAZ,MAAA,CAAA8B,eAAA,CAAApC,EAAA,CAAAqE,eAAA,IAAAC,GAAA,EAAAN,QAAA,CAAA7B,SAAA,GAAAQ,OAAA,CAAgE,yCAAAqB,QAAA,CAAA7B,SAAA,CAE7B;IAIjCnC,EAAA,CAAAI,SAAA,EAAoI;IAApIJ,EAAA,CAAA4C,UAAA,CAAAtC,MAAA,CAAA8B,eAAA,CAAApC,EAAA,CAAAqE,eAAA,IAAAC,GAAA,EAAAN,QAAA,CAAA7B,SAAA,GAAAU,IAAA,uBAAAvC,MAAA,CAAA8B,eAAA,CAAApC,EAAA,CAAAqE,eAAA,KAAAC,GAAA,EAAAN,QAAA,CAAA7B,SAAA,GAAAW,KAAA,CAAoI;IAGhI9C,EAAA,CAAAI,SAAA,EAAqF;IAArFJ,EAAA,CAAA4C,UAAA,0BAAAtC,MAAA,CAAA8B,eAAA,CAAApC,EAAA,CAAAqE,eAAA,KAAAC,GAAA,EAAAN,QAAA,CAAA7B,SAAA,GAAAW,KAAA,CAAqF;IACzF9C,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAwB,kBAAA,MAAAlB,MAAA,CAAA8B,eAAA,CAAApC,EAAA,CAAAqE,eAAA,KAAAC,GAAA,EAAAN,QAAA,CAAA7B,SAAA,GAAAE,KAAA,MACF;;;;;IA9BJrC,EADF,CAAAC,cAAA,cAAsG,cAEwB;IAe1HD,EAZA,CAAAyC,UAAA,IAAAgC,qDAAA,kBAE4D,IAAAC,qDAAA,mBAetB;IAWxC1E,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAAC,cAAA,YAC2B;IAAAD,EAAA,CAAAE,MAAA,GAAoB;IACjDF,EADiD,CAAAG,YAAA,EAAI,EAC/C;;;;;IAhCCH,EAAA,CAAAI,SAAA,GAA2I;IAA3IJ,EAAA,CAAAkB,UAAA,SAAAZ,MAAA,CAAAkD,WAAA,CAAAQ,QAAA,CAAA7B,SAAA,IAAA6B,QAAA,CAAAC,KAAA,MAAA3D,MAAA,CAAAqE,WAAA,CAAAX,QAAA,CAAA7B,SAAA,IAAA6B,QAAA,CAAAC,KAAA,MAAA3D,MAAA,CAAAsE,WAAA,CAAAZ,QAAA,CAAA7B,SAAA,IAAA6B,QAAA,CAAAC,KAAA,EAA2I;IAY3IjE,EAAA,CAAAI,SAAA,EAA0I;IAA1IJ,EAAA,CAAAkB,UAAA,UAAAZ,MAAA,CAAAkD,WAAA,CAAAQ,QAAA,CAAA7B,SAAA,IAAA6B,QAAA,CAAAC,KAAA,KAAA3D,MAAA,CAAAqE,WAAA,CAAAX,QAAA,CAAA7B,SAAA,IAAA6B,QAAA,CAAAC,KAAA,KAAA3D,MAAA,CAAAsE,WAAA,CAAAZ,QAAA,CAAA7B,SAAA,IAAA6B,QAAA,CAAAC,KAAA,EAA0I;IAmB7IjE,EAAA,CAAAI,SAAA,EAAwB;IAAxBJ,EAAA,CAAAkB,UAAA,UAAA8C,QAAA,CAAA7B,SAAA,CAAwB;IAACnC,EAAA,CAAAI,SAAA,EAAoB;IAApBJ,EAAA,CAAAK,iBAAA,CAAA2D,QAAA,CAAA7B,SAAA,CAAoB;;;;;IAtCnDnC,EAAA,CAAAC,cAAA,cAAuG;IACrGD,EAAA,CAAAyC,UAAA,IAAAoC,+CAAA,kBAAsG;IAuCxG7E,EAAA,CAAAG,YAAA,EAAM;;;;IAvCkBH,EAAA,CAAAI,SAAA,EAAkB;IAAlBJ,EAAA,CAAAkB,UAAA,YAAAZ,MAAA,CAAAwE,aAAA,CAAkB;;;;;IAtD5C9E,EAAA,CAAAC,cAAA,cAAuE;IAqDrED,EAnDA,CAAAyC,UAAA,IAAAsC,yCAAA,kBAA6F,IAAAC,yCAAA,kBAmDU;IAyCzGhF,EAAA,CAAAG,YAAA,EAAM;;;;IA5FkDH,EAAA,CAAAI,SAAA,EAAqC;IAArCJ,EAAA,CAAAkB,UAAA,SAAAZ,MAAA,CAAAuD,QAAA,IAAAvD,MAAA,CAAAuD,QAAA,CAAAoB,MAAA,KAAqC;IAmDrCjF,EAAA,CAAAI,SAAA,EAA+C;IAA/CJ,EAAA,CAAAkB,UAAA,SAAAZ,MAAA,CAAAwE,aAAA,IAAAxE,MAAA,CAAAwE,aAAA,CAAAG,MAAA,KAA+C;;;;;IAyD3FjF,EAAA,CAAAC,cAAA,WAAgC;IAAAD,EAAA,CAAAE,MAAA,GAAyB;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAAhCH,EAAA,CAAAI,SAAA,EAAyB;IAAzBJ,EAAA,CAAAwB,kBAAA,YAAAlB,MAAA,CAAAgC,WAAA,CAAA4C,QAAA,MAAyB;;;;;;IAX/DlF,EAFF,CAAAC,cAAA,cAC0G,cAChB;IAAhCD,EAAA,CAAAS,UAAA,mBAAA0E,+DAAA;MAAA,MAAAD,QAAA,GAAAlF,EAAA,CAAAW,aAAA,CAAAyE,IAAA,EAAAxD,SAAA;MAAA,MAAAtB,MAAA,GAAAN,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAc,WAAA,CAASR,MAAA,CAAAuB,eAAA,CAAAqD,QAAA,CAAqB;IAAA,EAAC;IAErFlF,EAAA,CAAAC,cAAA,cAA2B;IACzBD,EAAA,CAAAgB,SAAA,QAAwF;IAC1FhB,EAAA,CAAAG,YAAA,EAAM;IAIJH,EADF,CAAAC,cAAA,cAA4B,eACqC;IAAAD,EAAA,CAAAE,MAAA,GAAoB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAExFH,EADF,CAAAC,cAAA,cAA+D,WACvD;IAAAD,EAAA,CAAAE,MAAA,GAAiC;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC9CH,EAAA,CAAAyC,UAAA,KAAA4C,iDAAA,mBAAgC;IAGtCrF,EAFI,CAAAG,YAAA,EAAM,EACF,EACF;IAGNH,EAAA,CAAAC,cAAA,kBAE6E;IAFvDD,EAAA,CAAAS,UAAA,mBAAA6E,mEAAA;MAAA,MAAAC,KAAA,GAAAvF,EAAA,CAAAW,aAAA,CAAAyE,IAAA,EAAAhC,KAAA;MAAA,MAAA9C,MAAA,GAAAN,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAc,WAAA,CAASR,MAAA,CAAA+C,UAAA,CAAAkC,KAAA,CAAa;IAAA,EAAC;IAG3CvF,EAAA,CAAAgB,SAAA,aAAmC;IAEvChB,EADE,CAAAG,YAAA,EAAS,EACL;;;;;IAnBGH,EAAA,CAAAI,SAAA,GAAgF;IAAhFJ,EAAA,CAAA4C,UAAA,CAAAtC,MAAA,CAAA8B,eAAA,CAAA8C,QAAA,EAAArC,IAAA,iBAAAvC,MAAA,CAAA8B,eAAA,CAAA8C,QAAA,EAAApC,KAAA,CAAgF;IAKpB9C,EAAA,CAAAI,SAAA,GAAoB;IAApBJ,EAAA,CAAAK,iBAAA,CAAA6E,QAAA,CAAA/C,SAAA,CAAoB;IAE3EnC,EAAA,CAAAI,SAAA,GAAiC;IAAjCJ,EAAA,CAAAK,iBAAA,CAAAC,MAAA,CAAA8B,eAAA,CAAA8C,QAAA,EAAA7C,KAAA,CAAiC;IAChCrC,EAAA,CAAAI,SAAA,EAAuB;IAAvBJ,EAAA,CAAAkB,UAAA,SAAAZ,MAAA,CAAAgC,WAAA,CAAA4C,QAAA,EAAuB;IAQLlF,EAAA,CAAAI,SAAA,EAAgC;IAAhCJ,EAAA,CAAA2D,WAAA,WAAArD,MAAA,CAAAC,MAAA,CAAAY,QAAA,CAAgC;IAA7DnB,EAAA,CAAAkB,UAAA,aAAAZ,MAAA,CAAAC,MAAA,CAAAY,QAAA,CAA4B;;;;;IAtBlCnB,EADF,CAAAC,cAAA,cAA2G,cAClF;IACrBD,EAAA,CAAAyC,UAAA,IAAA+C,yCAAA,mBAC0G;IAyB9GxF,EADE,CAAAG,YAAA,EAAM,EACF;;;;IA1BoBH,EAAA,CAAAI,SAAA,GAAa;IAAbJ,EAAA,CAAAkB,UAAA,YAAAZ,MAAA,CAAAuD,QAAA,CAAa;;;ADpF7C,OAAM,MAAO4B,mBAAoB,SAAQ5F,aAAa;EA8BpD6F,YACUC,MAAmB,EACnBC,OAAuB,EACvBC,WAAwB;IAEhC,KAAK,CAACF,MAAM,CAAC;IAJL,KAAAA,MAAM,GAANA,MAAM;IACN,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAC,WAAW,GAAXA,WAAW;IA/BZ,KAAAtF,MAAM,GAAqB;MAClCuF,aAAa,EAAE,CAAC,YAAY,EAAE,WAAW,EAAE,iBAAiB,CAAC;MAC7DC,iBAAiB,EAAE,mBAAmB;MACtCC,eAAe,EAAE,wCAAwC;MACzD3D,KAAK,EAAE,MAAM;MACb7B,QAAQ,EAAE,WAAW;MACrByF,QAAQ,EAAE,KAAK;MACf9E,QAAQ,EAAE,KAAK;MACf+E,YAAY,EAAE,KAAK;MACnBC,UAAU,EAAE,IAAI;MAChBC,UAAU,EAAE,4BAA4B;MACxCC,WAAW,EAAE,EAAE;MACfC,QAAQ,EAAE,KAAK;MACfC,WAAW,EAAE;KACd;IACQ,KAAAC,eAAe,GAAkB,IAAI;IACrC,KAAAjF,cAAc,GAAkB,IAAI;IACpC,KAAAkF,aAAa,GAAW,OAAO;IAC/B,KAAA5C,QAAQ,GAA4B,EAAE,CAAC,CAAC;IACxC,KAAAiB,aAAa,GAAU,EAAE,CAAC,CAAC;IAE1B,KAAA4B,YAAY,GAAG,IAAI/G,YAAY,EAAoB;IACnD,KAAAgH,WAAW,GAAG,IAAIhH,YAAY,EAAQ;IACtC,KAAAiH,cAAc,GAAG,IAAIjH,YAAY,EAAU;IAC3C,KAAAkH,iBAAiB,GAAG,IAAIlH,YAAY,EAA2B,CAAC,CAAC;IACjE,KAAAmH,WAAW,GAAG,IAAInH,YAAY,EAAU,CAAC,CAAC;IAEpD,KAAAsB,QAAQ,GAAkB,IAAI;EAO9B;EAES8F,QAAQA,CAAA;IACf,IAAI,CAAC9F,QAAQ,GAAG,IAAI,CAACuF,eAAe,CAAC,CAAI;IACzC,IAAI,CAACjG,MAAM,GAAG;MACZ,GAAG;QACDuF,aAAa,EAAE,CAAC,YAAY,EAAE,WAAW,EAAE,iBAAiB,CAAC;QAC7DC,iBAAiB,EAAE,mBAAmB;QACtCC,eAAe,EAAE,wCAAwC;QACzD3D,KAAK,EAAE,MAAM;QACb7B,QAAQ,EAAE,WAAW;QACrByF,QAAQ,EAAE,KAAK;QACf9E,QAAQ,EAAE,KAAK;QACf+E,YAAY,EAAE,KAAK;QACnBC,UAAU,EAAE,IAAI;QAChBC,UAAU,EAAE,4BAA4B;QACxCC,WAAW,EAAE,EAAE;QACfC,QAAQ,EAAE,KAAK;QACfC,WAAW,EAAE;OACd;MACD,GAAG,IAAI,CAAChG;KACT;EACH;EACAyG,cAAcA,CAACC,KAAU;IACvB,MAAMC,KAAK,GAAaD,KAAK,CAACE,MAAM,CAACD,KAAK;IAE1C,IAAI,CAACA,KAAK,IAAIA,KAAK,CAACjC,MAAM,KAAK,CAAC,EAAE;MAChC;IACF;IAEA,IAAI,IAAI,CAAC1E,MAAM,CAAC+F,QAAQ,EAAE;MACxB,IAAI,CAACc,mBAAmB,CAACF,KAAK,CAAC;IACjC,CAAC,MAAM;MACL,IAAI,CAACG,gBAAgB,CAACH,KAAK,CAAC,CAAC,CAAC,CAAC;IACjC;EACF;EAEQG,gBAAgBA,CAACC,IAAU;IACjC;IACA,IAAI,CAAC,IAAI,CAAC/G,MAAM,CAACwF,iBAAkB,CAACwB,IAAI,CAACD,IAAI,CAACE,IAAI,CAAC,EAAE;MACnD,MAAMC,eAAe,GAAG,IAAI,CAACC,sBAAsB,EAAE;MACrD,IAAI,CAAC9B,OAAO,CAAC+B,YAAY,CAAC,UAAUF,eAAe,EAAE,CAAC;MACtD;IACF;IAEA;IACA,MAAMG,cAAc,GAAG,CAAC,IAAI,CAACrH,MAAM,CAAC8F,WAAW,IAAI,EAAE,IAAI,IAAI,GAAG,IAAI;IACpE,IAAIiB,IAAI,CAACO,IAAI,GAAGD,cAAc,EAAE;MAC9B,IAAI,CAAChC,OAAO,CAAC+B,YAAY,CAAC,YAAY,IAAI,CAACpH,MAAM,CAAC8F,WAAW,KAAK,CAAC;MACnE;IACF;IAEA,IAAI,CAACpF,QAAQ,GAAGqG,IAAI,CAACE,IAAI;IAEzB;IACA,IAAI,IAAI,CAACjH,MAAM,CAAC2F,YAAY,EAAE;MAC5B,MAAM4B,wBAAwB,GAAGR,IAAI,CAACE,IAAI,CAACO,SAAS,CAAC,CAAC,EAAET,IAAI,CAACE,IAAI,CAACQ,WAAW,CAAC,GAAG,CAAC,CAAC,IAAIV,IAAI,CAACE,IAAI;MAChG,IAAI,CAACZ,cAAc,CAACqB,IAAI,CAACH,wBAAwB,CAAC;IACpD;IAEA,MAAMI,MAAM,GAAG,IAAIC,UAAU,EAAE;IAC/BD,MAAM,CAACE,MAAM,GAAIC,CAAM,IAAI;MACzB;MACA,IAAIC,QAAgB;MACpB,IAAIhB,IAAI,CAACiB,IAAI,CAACC,UAAU,CAAC,QAAQ,CAAC,EAAE;QAClCF,QAAQ,GAAGxI,YAAY,CAAC2I,GAAG,CAAC,CAAC;MAC/B,CAAC,MAAM,IAAInB,IAAI,CAACE,IAAI,CAACkB,WAAW,EAAE,CAACC,QAAQ,CAAC,MAAM,CAAC,IAAIrB,IAAI,CAACE,IAAI,CAACkB,WAAW,EAAE,CAACC,QAAQ,CAAC,MAAM,CAAC,EAAE;QAC/FL,QAAQ,GAAG,CAAC,CAAC,CAAC;MAChB,CAAC,MAAM;QACLA,QAAQ,GAAGxI,YAAY,CAAC8I,GAAG,CAAC,CAAC;MAC/B;MAEA,MAAMC,MAAM,GAAqB;QAC/BC,KAAK,EAAExB,IAAI,CAACE,IAAI;QAChBvD,KAAK,EAAEoE,CAAC,CAAClB,MAAM,EAAE0B,MAAM,EAAEE,QAAQ,EAAE,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QACjDC,IAAI,EAAE3B,IAAI,CAACE,IAAI,CAACmB,QAAQ,CAAC,KAAK,CAAC,GAAGrB,IAAI,GAAGA,IAAI;QAC7C4B,WAAW,EAAE5B,IAAI;QACjB/D,SAAS,EAAE+E,QAAQ;QACnBrH,QAAQ,EAAEqG,IAAI,CAACE;OAChB;MAED,IAAI,CAACd,YAAY,CAACuB,IAAI,CAACY,MAAM,CAAC;MAE9B,IAAI,IAAI,CAACM,SAAS,EAAE;QAClB,IAAI,CAACA,SAAS,CAACC,aAAa,CAACC,KAAK,GAAG,IAAI;MAC3C;IACF,CAAC;IACDnB,MAAM,CAACoB,aAAa,CAAChC,IAAI,CAAC;EAC5B;EAEQF,mBAAmBA,CAACF,KAAe;IACzC,MAAMqC,YAAY,GAAG,CAAC,YAAY,EAAE,WAAW,EAAE,iBAAiB,EAAE,kBAAkB,EAAE,uBAAuB,EAAE,eAAe,EAAE,aAAa,CAAC;IAChJ,MAAMC,SAAS,GAAG,IAAI,CAACjJ,MAAM,CAACwF,iBAAiB,IAAI,2BAA2B;IAC9E,MAAM6B,cAAc,GAAG,CAAC,IAAI,CAACrH,MAAM,CAAC8F,WAAW,IAAI,EAAE,IAAI,IAAI,GAAG,IAAI;IAEpE;IACA,IAAI,IAAI,CAAC9F,MAAM,CAAC2F,YAAY,IAAIgB,KAAK,CAACjC,MAAM,GAAG,CAAC,EAAE;MAChD,MAAMwE,SAAS,GAAGvC,KAAK,CAAC,CAAC,CAAC;MAC1B,MAAMjG,QAAQ,GAAGwI,SAAS,CAACjC,IAAI;MAC/B,MAAMM,wBAAwB,GAAG7G,QAAQ,CAAC8G,SAAS,CAAC,CAAC,EAAE9G,QAAQ,CAAC+G,WAAW,CAAC,GAAG,CAAC,CAAC,IAAI/G,QAAQ;MAC7F,IAAI,CAAC2F,cAAc,CAACqB,IAAI,CAACH,wBAAwB,CAAC;IACpD;IAEA,MAAM4B,QAAQ,GAA4B,EAAE;IAC5C,IAAIC,cAAc,GAAG,CAAC;IAEtB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG1C,KAAK,CAACjC,MAAM,EAAE2E,CAAC,EAAE,EAAE;MACrC,MAAMtC,IAAI,GAAGJ,KAAK,CAAC0C,CAAC,CAAC;MAErB;MACA,IAAI,CAACJ,SAAS,CAACjC,IAAI,CAACD,IAAI,CAACE,IAAI,CAAC,EAAE;QAC9B,IAAI,CAAC5B,OAAO,CAAC+B,YAAY,CAAC,kCAAkC,CAAC;QAC7DgC,cAAc,EAAE;QAChB;MACF;MAEA;MACA,IAAIrC,IAAI,CAACO,IAAI,GAAGD,cAAc,EAAE;QAC9B,IAAI,CAAChC,OAAO,CAAC+B,YAAY,CAAC,MAAML,IAAI,CAACE,IAAI,WAAW,IAAI,CAACjH,MAAM,CAAC8F,WAAW,KAAK,CAAC;QACjFsD,cAAc,EAAE;QAChB;MACF;MAEA,IAAIJ,YAAY,CAACZ,QAAQ,CAACrB,IAAI,CAACiB,IAAI,CAAC,IAAIiB,SAAS,CAACjC,IAAI,CAACD,IAAI,CAACE,IAAI,CAAC,EAAE;QACjE,MAAMU,MAAM,GAAG,IAAIC,UAAU,EAAE;QAC/BD,MAAM,CAACE,MAAM,GAAIC,CAAM,IAAI;UACzB;UACA,IAAIC,QAAQ,GAAG,CAAC,CAAC,CAAC;UAClB,MAAMrH,QAAQ,GAAGqG,IAAI,CAACE,IAAI,CAACkB,WAAW,EAAE;UAExC,IAAIzH,QAAQ,CAAC4I,KAAK,CAAC,mBAAmB,CAAC,EAAE;YACvCvB,QAAQ,GAAG,CAAC,CAAC,CAAC;UAChB,CAAC,MAAM,IAAIrH,QAAQ,CAAC6I,QAAQ,CAAC,MAAM,CAAC,EAAE;YACpCxB,QAAQ,GAAG,CAAC,CAAC,CAAC;UAChB,CAAC,MAAM,IAAIrH,QAAQ,CAAC4I,KAAK,CAAC,cAAc,CAAC,EAAE;YACzCvB,QAAQ,GAAG,CAAC,CAAC,CAAC;UAChB;UAEAoB,QAAQ,CAACK,IAAI,CAAC;YACZ7F,IAAI,EAAEmE,CAAC,CAAClB,MAAM,CAAC0B,MAAM;YACrBmB,UAAU,EAAE,IAAI,CAACC,kBAAkB,CAAC5B,CAAC,CAAClB,MAAM,CAAC0B,MAAM,CAAC;YACpD1G,SAAS,EAAEmF,IAAI,CAACE,IAAI;YACpBjE,SAAS,EAAE+E;WACZ,CAAC;UAEFqB,cAAc,EAAE;UAChB,IAAIA,cAAc,KAAKzC,KAAK,CAACjC,MAAM,EAAE;YACnC,IAAI,CAACpB,QAAQ,GAAG,CAAC,GAAG,IAAI,CAACA,QAAQ,EAAE,GAAG6F,QAAQ,CAAC;YAC/C,IAAI,CAAC7C,iBAAiB,CAACoB,IAAI,CAAC,IAAI,CAACpE,QAAQ,CAAC;YAE1C,IAAI,IAAI,CAACsF,SAAS,EAAE;cAClB,IAAI,CAACA,SAAS,CAACC,aAAa,CAACC,KAAK,GAAG,IAAI;YAC3C;UACF;QACF,CAAC;QACDnB,MAAM,CAACoB,aAAa,CAAChC,IAAI,CAAC;MAC5B,CAAC,MAAM;QACLqC,cAAc,EAAE;MAClB;IACF;EACF;EACA5I,SAASA,CAAA;IACP,IAAI,CAACE,QAAQ,GAAG,IAAI;IACpB,IAAI,CAAC0F,WAAW,CAACsB,IAAI,EAAE;IACvB,IAAI,IAAI,CAACkB,SAAS,EAAE;MAClB,IAAI,CAACA,SAAS,CAACC,aAAa,CAACC,KAAK,GAAG,IAAI;IAC3C;EACF;EACA/H,QAAQA,CAAC4I,GAAW;IAClB,IAAIA,GAAG,EAAE;MACP;MACA,MAAMC,OAAO,GAAGD,GAAG,CAAC1B,UAAU,CAAC,MAAM,CAAC,GAAG0B,GAAG,GAAG,GAAGnK,WAAW,CAACqK,qBAAqB,GAAGF,GAAG,EAAE;MAC3FG,MAAM,CAACC,IAAI,CAACH,OAAO,EAAE,QAAQ,CAAC;IAChC;EACF;EAEA9G,UAAUA,CAACD,KAAa;IACtB,IAAI,CAACS,QAAQ,CAAC0G,MAAM,CAACnH,KAAK,EAAE,CAAC,CAAC;IAC9B,IAAI,CAAC0D,WAAW,CAACmB,IAAI,CAAC7E,KAAK,CAAC;IAC5B,IAAI,CAACyD,iBAAiB,CAACoB,IAAI,CAAC,IAAI,CAACpE,QAAQ,CAAC;EAC5C;EAEQoG,kBAAkBA,CAACO,YAAoB;IAC7C,MAAMC,WAAW,GAAGD,YAAY,CAACE,OAAO,CAAC,GAAG,CAAC;IAC7C,IAAID,WAAW,KAAK,CAAC,CAAC,EAAE;MACtB,OAAOD,YAAY,CAACzC,SAAS,CAAC0C,WAAW,GAAG,CAAC,CAAC;IAChD;IACA,OAAOD,YAAY;EACrB;EAEAlH,OAAOA,CAACgF,QAAgB;IACtB,OAAOA,QAAQ,KAAK,CAAC;EACvB;EAEA5E,KAAKA,CAAC4E,QAAgB;IACpB,OAAOA,QAAQ,KAAK,CAAC;EACvB;EAEA7E,KAAKA,CAAC6E,QAAgB;IACpB,OAAOA,QAAQ,KAAK,CAAC;EACvB;EAEA3D,WAAWA,CAACgG,GAAW;IACrB,IAAIA,GAAG,EAAE;MACP,OAAOA,GAAG,CAACjC,WAAW,EAAE,CAACoB,QAAQ,CAAC,MAAM,CAAC;IAC3C;IACA,OAAO,KAAK;EACd;EAEAlF,WAAWA,CAAC+F,GAAW;IACrB,IAAIA,GAAG,EAAE;MACP,MAAMC,QAAQ,GAAGD,GAAG,CAACjC,WAAW,EAAE;MAClC,OAAOkC,QAAQ,CAACd,QAAQ,CAAC,MAAM,CAAC,IAAIc,QAAQ,CAACd,QAAQ,CAAC,MAAM,CAAC;IAC/D;IACA,OAAO,KAAK;EACd,CAAC,CAAE;EACHjI,eAAeA,CAACyF,IAAS;IACvBuD,OAAO,CAACC,GAAG,CAAC,WAAW,EAAExD,IAAI,CAAC;IAC9B,MAAMrG,QAAQ,GAAGqG,IAAI,CAACnF,SAAS,IAAImF,IAAI,CAACrG,QAAQ,IAAI,EAAE;IACtD,MAAM8J,WAAW,GAAGzD,IAAI,CAACnF,SAAS,IAAIlB,QAAQ;IAE9C;IACA,MAAM+J,aAAa,GAAG,IAAI,CAACxH,WAAW,CAACuH,WAAW,CAAC;IACnD,MAAME,WAAW,GAAG,IAAI,CAACtG,WAAW,CAACoG,WAAW,CAAC;IACjD,MAAMG,WAAW,GAAG,IAAI,CAACtG,WAAW,CAACmG,WAAW,CAAC;IAEjDF,OAAO,CAACC,GAAG,CAAC,SAAS,EAAE;MAAEE,aAAa;MAAEC,WAAW;MAAEC,WAAW;MAAEH;IAAW,CAAE,CAAC;IAEhF;IACA,IAAIzD,IAAI,CAACpD,IAAI,EAAE;MACb2G,OAAO,CAACC,GAAG,CAAC,kBAAkB,CAAC;MAC/B,IAAI,CAACK,mBAAmB,CAAC7D,IAAI,CAACpD,IAAI,EAAE6G,WAAW,EAAEC,aAAa,EAAEC,WAAW,EAAEC,WAAW,CAAC;MACzF;IACF;IAEA;IACA,MAAM/G,YAAY,GAAGmD,IAAI,CAACnD,YAAY,IAAImD,IAAI,CAACrD,KAAK;IACpD,MAAMmH,cAAc,GAAG9D,IAAI,CAACrG,QAAQ,IAAIqG,IAAI,CAACnF,SAAS;IAEtD0I,OAAO,CAACC,GAAG,CAAC,SAAS,EAAE;MAAE3G,YAAY;MAAEiH;IAAc,CAAE,CAAC;IAExD,IAAIjH,YAAY,IAAIiH,cAAc,EAAE;MAClCP,OAAO,CAACC,GAAG,CAAC,qBAAqB,CAAC;MAClC,IAAI,CAACO,iBAAiB,CAAClH,YAAY,EAAEiH,cAAc,EAAEL,WAAW,EAAEC,aAAa,EAAEC,WAAW,EAAEC,WAAW,CAAC;IAC5G,CAAC,MAAM;MACL;MACAL,OAAO,CAACS,KAAK,CAAC,4BAA4B,EAAEhE,IAAI,CAAC;MACjD,IAAI,CAAC1B,OAAO,CAAC+B,YAAY,CAAC,kBAAkB,CAAC;IAC/C;EACF;EAEA;EACQ4D,eAAeA,CAACjE,IAAS,EAAEhE,OAAgB,EAAEG,KAAc,EAAEC,KAAc;IACjF,MAAMzC,QAAQ,GAAGqG,IAAI,CAACnF,SAAS,IAAImF,IAAI,CAACrG,QAAQ,IAAI,EAAE;IACtD4J,OAAO,CAACS,KAAK,CAAC,+BAA+B,EAAEhE,IAAI,CAAC;IACpD,IAAI,CAAC1B,OAAO,CAAC+B,YAAY,CAAC,kBAAkB,CAAC;EAC/C;EAEA;EACQwD,mBAAmBA,CAACjH,IAAY,EAAEjD,QAAgB,EAAEqC,OAAgB,EAAEG,KAAc,EAAEC,KAAc;IAC1G,IAAIJ,OAAO,EAAE;MACX;MACA,IAAI,CAACkI,gBAAgB,CAACtH,IAAI,EAAEjD,QAAQ,CAAC;IACvC,CAAC,MAAM,IAAIwC,KAAK,EAAE;MAChB;MACA,IAAI,CAACgI,kBAAkB,CAACvH,IAAI,EAAEjD,QAAQ,CAAC;IACzC,CAAC,MAAM;MACL;MACA,IAAI,CAACyK,kBAAkB,CAACxH,IAAI,EAAEjD,QAAQ,CAAC;IACzC;EACF;EAEA;EACQyK,kBAAkBA,CAACC,UAAkB,EAAE1K,QAAgB;IAC7D,IAAI;MACF;MACA,MAAMuJ,YAAY,GAAGmB,UAAU,CAAChD,QAAQ,CAAC,GAAG,CAAC,GAAGgD,UAAU,CAAC3C,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG2C,UAAU;MAErF;MACA,MAAMC,cAAc,GAAGC,IAAI,CAACrB,YAAY,CAAC;MACzC,MAAMsB,WAAW,GAAG,IAAIC,KAAK,CAACH,cAAc,CAAC3G,MAAM,CAAC;MACpD,KAAK,IAAI2E,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGgC,cAAc,CAAC3G,MAAM,EAAE2E,CAAC,EAAE,EAAE;QAC9CkC,WAAW,CAAClC,CAAC,CAAC,GAAGgC,cAAc,CAACI,UAAU,CAACpC,CAAC,CAAC;MAC/C;MACA,MAAMqC,SAAS,GAAG,IAAIC,UAAU,CAACJ,WAAW,CAAC;MAC7C,MAAMK,IAAI,GAAG,IAAIC,IAAI,CAAC,CAACH,SAAS,CAAC,CAAC;MAElC;MACA,MAAM/B,GAAG,GAAGmC,GAAG,CAACC,eAAe,CAACH,IAAI,CAAC;MACrC,MAAMI,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;MACxCF,IAAI,CAACG,IAAI,GAAGxC,GAAG;MACfqC,IAAI,CAACI,QAAQ,GAAG1L,QAAQ;MACxBuL,QAAQ,CAACI,IAAI,CAACC,WAAW,CAACN,IAAI,CAAC;MAC/BA,IAAI,CAACO,KAAK,EAAE;MACZN,QAAQ,CAACI,IAAI,CAACG,WAAW,CAACR,IAAI,CAAC;MAE/B;MACAS,UAAU,CAAC,MAAMX,GAAG,CAACY,eAAe,CAAC/C,GAAG,CAAC,EAAE,IAAI,CAAC;IAClD,CAAC,CAAC,OAAOoB,KAAK,EAAE;MACdT,OAAO,CAACS,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;MAC/B,IAAI,CAAC1F,OAAO,CAAC+B,YAAY,CAAC,QAAQ,CAAC;IACrC;EACF;EAEA;EACQ0D,iBAAiBA,CAAClH,YAAoB,EAAElD,QAAgB,EAAE8J,WAAmB,EAAEzH,OAAgB,EAAEG,KAAc,EAAEC,KAAc;IACrI,IAAI,CAACmC,WAAW,CAACqH,OAAO,CAAC/I,YAAY,EAAElD,QAAQ,CAAC,CAACkM,SAAS,CAAC;MACzDC,IAAI,EAAGjB,IAAU,IAAI;QACnB,MAAMjC,GAAG,GAAGmC,GAAG,CAACC,eAAe,CAACH,IAAI,CAAC;QAErC,IAAI7I,OAAO,EAAE;UACX;UACA,IAAI,CAACkI,gBAAgB,CAACtB,GAAG,EAAEa,WAAW,CAAC;QACzC,CAAC,MAAM,IAAItH,KAAK,EAAE;UAChB;UACA,IAAI,CAACgI,kBAAkB,CAACvB,GAAG,EAAEa,WAAW,CAAC;QAC3C,CAAC,MAAM;UACL;UACA,IAAI,CAACsC,gBAAgB,CAAClB,IAAI,EAAEpB,WAAW,CAAC;QAC1C;QAEA;QACAiC,UAAU,CAAC,MAAMX,GAAG,CAACY,eAAe,CAAC/C,GAAG,CAAC,EAAE,KAAK,CAAC;MACnD,CAAC;MACDoB,KAAK,EAAGA,KAAK,IAAI;QACfT,OAAO,CAACS,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;QAC/B,IAAI,CAAC1F,OAAO,CAAC+B,YAAY,CAAC,cAAc,CAAC;MAC3C;KACD,CAAC;EACJ;EAEA;EACQ8D,kBAAkBA,CAAC6B,OAAe,EAAErM,QAAgB;IAC1D,IAAI;MACF,MAAMsM,SAAS,GAAGlD,MAAM,CAACC,IAAI,CAAC,EAAE,EAAE,QAAQ,CAAC;MAC3C,IAAIiD,SAAS,EAAE;QACbA,SAAS,CAACf,QAAQ,CAACgB,KAAK,CAAC;;;uBAGVvM,QAAQ;;;;;;;6BAOFqM,OAAO;;;SAG3B,CAAC;QACFC,SAAS,CAACf,QAAQ,CAACiB,KAAK,EAAE;MAC5B,CAAC,MAAM;QACL;QACApD,MAAM,CAACqD,QAAQ,CAAChB,IAAI,GAAGY,OAAO;MAChC;IACF,CAAC,CAAC,OAAOhC,KAAK,EAAE;MACdT,OAAO,CAACS,KAAK,CAAC,cAAc,EAAEA,KAAK,CAAC;MACpC;MACAjB,MAAM,CAACC,IAAI,CAACgD,OAAO,EAAE,QAAQ,CAAC;IAChC;EACF;EAEA;EACQD,gBAAgBA,CAAClB,IAAU,EAAElL,QAAgB;IACnD,MAAMiJ,GAAG,GAAGmC,GAAG,CAACC,eAAe,CAACH,IAAI,CAAC;IACrC,MAAMI,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;IACxCF,IAAI,CAACG,IAAI,GAAGxC,GAAG;IACfqC,IAAI,CAACI,QAAQ,GAAG1L,QAAQ;IACxBuL,QAAQ,CAACI,IAAI,CAACC,WAAW,CAACN,IAAI,CAAC;IAC/BA,IAAI,CAACO,KAAK,EAAE;IACZN,QAAQ,CAACI,IAAI,CAACG,WAAW,CAACR,IAAI,CAAC;IAE/B;IACAS,UAAU,CAAC,MAAMX,GAAG,CAACY,eAAe,CAAC/C,GAAG,CAAC,EAAE,IAAI,CAAC;EAClD;EACA;EACA1G,WAAWA,CAACvC,QAAgB;IAC1B,MAAM0M,eAAe,GAAG,CAAC,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,CAAC;IACpE,MAAMC,SAAS,GAAG3M,QAAQ,CAAC+H,KAAK,CAAC,GAAG,CAAC,CAAC6E,GAAG,EAAE,EAAEnF,WAAW,EAAE;IAC1D,OAAOiF,eAAe,CAAChF,QAAQ,CAACiF,SAAS,IAAI,EAAE,CAAC;EAClD;EACA;EACA3L,WAAWA,CAACqF,IAAS;IACnB,MAAMrG,QAAQ,GAAGqG,IAAI,CAACnF,SAAS,IAAImF,IAAI,CAACrG,QAAQ,IAAI,EAAE;IAEtD,IAAI,CAACA,QAAQ,EAAE;MACb,OAAO,EAAE;IACX;IAEA;IACA,IAAIqG,IAAI,CAACpD,IAAI,EAAE;MACb,OAAOoD,IAAI,CAACpD,IAAI;IAClB;IAEA;IACA,IAAIoD,IAAI,CAACnD,YAAY,IAAImD,IAAI,CAACrG,QAAQ,EAAE;MACtC,OAAO,GAAGlB,WAAW,CAACqK,qBAAqB,kCAAkC0D,kBAAkB,CAACxG,IAAI,CAACnD,YAAY,CAAC,aAAa2J,kBAAkB,CAACxG,IAAI,CAACrG,QAAQ,CAAC,EAAE;IACpK;IAEA;IACA,IAAIqG,IAAI,CAACrD,KAAK,IAAI,CAACqD,IAAI,CAACrD,KAAK,CAACuE,UAAU,CAAC,OAAO,CAAC,EAAE;MACjD,OAAO,GAAGzI,WAAW,CAACqK,qBAAqB,GAAG9C,IAAI,CAACrD,KAAK,EAAE;IAC5D;IAEA;IACA,OAAO,EAAE;EACX;EAEA;EACA+G,aAAaA,CAAC/J,QAAgB;IAC5B,OAAO,IAAI,CAACuC,WAAW,CAACvC,QAAQ,CAAC;EACnC;EAEA8M,WAAWA,CAAC9M,QAAgB;IAC1B,OAAO,IAAI,CAAC0D,WAAW,CAAC1D,QAAQ,CAAC;EACnC;EAEAiK,WAAWA,CAACjK,QAAgB;IAC1B,OAAO,IAAI,CAAC2D,WAAW,CAAC3D,QAAQ,CAAC;EACnC,CAAC,CAAE;EACHe,YAAYA,CAACiF,KAAU,EAAEK,IAAS;IAChC,MAAMrG,QAAQ,GAAGqG,IAAI,CAACnF,SAAS,IAAImF,IAAI,CAACrG,QAAQ,IAAI,IAAI;IACxD4J,OAAO,CAACmD,IAAI,CAAC,SAAS,EAAE/M,QAAQ,EAAEqG,IAAI,CAAC;IACvC;IACAL,KAAK,CAACE,MAAM,CAAC8G,KAAK,CAACC,OAAO,GAAG,MAAM;IAEnC;IACA,MAAMC,SAAS,GAAGlH,KAAK,CAACE,MAAM,CAACiH,aAAa;IAC5C,IAAID,SAAS,EAAE;MACb,MAAME,QAAQ,GAAG,IAAI,CAACjM,eAAe,CAACkF,IAAI,CAAC;MAE3C;MACA,MAAMgH,UAAU,GAAG9B,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;MAChD6B,UAAU,CAACC,SAAS,GAAG,qFAAqFF,QAAQ,CAAC1L,OAAO,EAAE;MAC9H2L,UAAU,CAACE,SAAS,GAAG;oBACTH,QAAQ,CAACxL,IAAI,kBAAkBwL,QAAQ,CAACvL,KAAK;2CACtBuL,QAAQ,CAACvL,KAAK,KAAKuL,QAAQ,CAAChM,KAAK;OACrE;MAED;MACAiM,UAAU,CAACG,gBAAgB,CAAC,OAAO,EAAE,MAAK;QACxC,IAAI,CAAC5M,eAAe,CAACyF,IAAI,CAAC;MAC5B,CAAC,CAAC;MAEF;MACA6G,SAAS,CAACK,SAAS,GAAG,EAAE;MACxBL,SAAS,CAACtB,WAAW,CAACyB,UAAU,CAAC;IACnC;EACF;EAEQ5G,sBAAsBA,CAAA;IAC5B,MAAMgH,UAAU,GAAG,IAAI,CAACnO,MAAM,CAACyF,eAAe,EAAEgD,KAAK,CAAC,GAAG,CAAC,CAAC2F,GAAG,CAACpG,IAAI,IAAG;MACpE,IAAIA,IAAI,CAACI,QAAQ,CAAC,KAAK,CAAC,EAAE,OAAO,KAAK;MACtC,IAAIJ,IAAI,CAACI,QAAQ,CAAC,MAAM,CAAC,IAAIJ,IAAI,CAACI,QAAQ,CAAC,KAAK,CAAC,EAAE,OAAO,KAAK;MAC/D,IAAIJ,IAAI,CAACI,QAAQ,CAAC,KAAK,CAAC,EAAE,OAAO,KAAK;MACtC,IAAIJ,IAAI,CAACI,QAAQ,CAAC,MAAM,CAAC,EAAE,OAAO,KAAK;MACvC,IAAIJ,IAAI,CAACI,QAAQ,CAAC,MAAM,CAAC,EAAE,OAAO,KAAK;MACvC,OAAOJ,IAAI;IACb,CAAC,CAAC,CAACqG,IAAI,CAAC,GAAG,CAAC;IAEZ,OAAO,KAAKF,UAAU,IAAI;EAC5B;EACA;EACAG,gBAAgBA,CAACC,OAAe;IAC9BjE,OAAO,CAACmD,IAAI,CAAC,8CAA8C,CAAC;IAC5D,IAAI,CAACpI,OAAO,CAAC+B,YAAY,CAAC,mBAAmB,CAAC;EAChD;EACA;EACAoH,oBAAoBA,CAACD,OAAe,EAAE7N,QAAgB;IACpD4J,OAAO,CAACmD,IAAI,CAAC,kDAAkD,CAAC;IAChE,IAAI,CAACpI,OAAO,CAAC+B,YAAY,CAAC,iBAAiB,CAAC;EAC9C;EAEA;EACQ6D,gBAAgBA,CAACwD,QAAgB,EAAE/N,QAAgB;IACzD,IAAI;MACF,MAAMsM,SAAS,GAAGlD,MAAM,CAACC,IAAI,CAAC,EAAE,EAAE,QAAQ,CAAC;MAC3C,IAAIiD,SAAS,EAAE;QACbA,SAAS,CAACf,QAAQ,CAACgB,KAAK,CAAC;;;uBAGVvM,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;mCA+BIA,QAAQ;0BACjB+N,QAAQ,UAAU/N,QAAQ;;;SAG3C,CAAC;QACFsM,SAAS,CAACf,QAAQ,CAACiB,KAAK,EAAE;MAC5B;IACF,CAAC,CAAC,OAAOnC,KAAK,EAAE;MACdT,OAAO,CAACS,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;MACjCjB,MAAM,CAACC,IAAI,CAAC0E,QAAQ,EAAE,QAAQ,CAAC;IACjC;EACF;EAEAC,UAAUA,CAAC/E,GAAW;IACpB,IAAIA,GAAG,EAAE;MACP;MACA,MAAMC,OAAO,GAAGD,GAAG,CAAC1B,UAAU,CAAC,MAAM,CAAC,GAAG0B,GAAG,GAAG,GAAGnK,WAAW,CAACqK,qBAAqB,GAAGF,GAAG,EAAE;MAC3FG,MAAM,CAACC,IAAI,CAACH,OAAO,EAAE,QAAQ,CAAC;IAChC;EACF;EAEA;EACA/H,eAAeA,CAACkF,IAAS;IACvB,MAAMrG,QAAQ,GAAGqG,IAAI,CAACnF,SAAS,IAAImF,IAAI,CAACrG,QAAQ,IAAI,EAAE;IACtD,MAAMqH,QAAQ,GAAGhB,IAAI,CAAC/D,SAAS;IAC/B,MAAMqK,SAAS,GAAG3M,QAAQ,CAAC+H,KAAK,CAAC,GAAG,CAAC,CAAC6E,GAAG,EAAE,EAAEnF,WAAW,EAAE,IAAI,EAAE;IAEhE;IACA,IAAIJ,QAAQ,KAAK,CAAC,IAAI,IAAI,CAAC9E,WAAW,CAACvC,QAAQ,CAAC,EAAE;MAChD,IAAI2M,SAAS,KAAK,KAAK,IAAIA,SAAS,KAAK,MAAM,EAAE;QAC/C,OAAO;UACL/K,IAAI,EAAE,oBAAoB;UAC1BC,KAAK,EAAE,eAAe;UACtBH,OAAO,EAAE,YAAY;UACrBN,KAAK,EAAE;SACR;MACH,CAAC,MAAM,IAAIuL,SAAS,KAAK,KAAK,EAAE;QAC9B,OAAO;UACL/K,IAAI,EAAE,oBAAoB;UAC1BC,KAAK,EAAE,iBAAiB;UACxBH,OAAO,EAAE,cAAc;UACvBN,KAAK,EAAE;SACR;MACH,CAAC,MAAM;QACL,OAAO;UACLQ,IAAI,EAAE,oBAAoB;UAC1BC,KAAK,EAAE,eAAe;UACtBH,OAAO,EAAE,YAAY;UACrBN,KAAK,EAAE;SACR;MACH;IACF;IAEA;IACA,IAAIiG,QAAQ,KAAK,CAAC,IAAI,IAAI,CAAC3D,WAAW,CAAC1D,QAAQ,CAAC,EAAE;MAChD,OAAO;QACL4B,IAAI,EAAE,kBAAkB;QACxBC,KAAK,EAAE,cAAc;QACrBH,OAAO,EAAE,WAAW;QACpBN,KAAK,EAAE;OACR;IACH;IAEA;IACA,IAAIiG,QAAQ,KAAK,CAAC,IAAI,IAAI,CAAC1D,WAAW,CAAC3D,QAAQ,CAAC,EAAE;MAChD,IAAI2M,SAAS,KAAK,KAAK,EAAE;QACvB,OAAO;UACL/K,IAAI,EAAE,YAAY;UAClBC,KAAK,EAAE,gBAAgB;UACvBH,OAAO,EAAE,aAAa;UACtBN,KAAK,EAAE;SACR;MACH,CAAC,MAAM,IAAIuL,SAAS,KAAK,KAAK,EAAE;QAC9B,OAAO;UACL/K,IAAI,EAAE,YAAY;UAClBC,KAAK,EAAE,kBAAkB;UACzBH,OAAO,EAAE,eAAe;UACxBN,KAAK,EAAE;SACR;MACH,CAAC,MAAM;QACL,OAAO;UACLQ,IAAI,EAAE,YAAY;UAClBC,KAAK,EAAE,gBAAgB;UACvBH,OAAO,EAAE,aAAa;UACtBN,KAAK,EAAE;SACR;MACH;IACF;IAEA;IACA,IAAIuL,SAAS,KAAK,KAAK,IAAIA,SAAS,KAAK,MAAM,EAAE;MAC/C,OAAO;QACL/K,IAAI,EAAE,mBAAmB;QACzBC,KAAK,EAAE,eAAe;QACtBH,OAAO,EAAE,YAAY;QACrBN,KAAK,EAAE;OACR;IACH;IAEA,IAAIuL,SAAS,KAAK,KAAK,IAAIA,SAAS,KAAK,MAAM,EAAE;MAC/C,OAAO;QACL/K,IAAI,EAAE,oBAAoB;QAC1BC,KAAK,EAAE,gBAAgB;QACvBH,OAAO,EAAE,aAAa;QACtBN,KAAK,EAAE;OACR;IACH;IAEA,IAAIuL,SAAS,KAAK,KAAK,IAAIA,SAAS,KAAK,MAAM,EAAE;MAC/C,OAAO;QACL/K,IAAI,EAAE,yBAAyB;QAC/BC,KAAK,EAAE,iBAAiB;QACxBH,OAAO,EAAE,cAAc;QACvBN,KAAK,EAAE;OACR;IACH;IAEA;IACA,IAAI,CAAC,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,CAAC,CAACsG,QAAQ,CAACiF,SAAS,CAAC,EAAE;MACzD,OAAO;QACL/K,IAAI,EAAE,sBAAsB;QAC5BC,KAAK,EAAE,iBAAiB;QACxBH,OAAO,EAAE,cAAc;QACvBN,KAAK,EAAEuL,SAAS,CAACsB,WAAW;OAC7B;IACH;IAEA;IACA,IAAI,CAAC,KAAK,EAAE,KAAK,EAAE,IAAI,CAAC,CAACvG,QAAQ,CAACiF,SAAS,CAAC,EAAE;MAC5C,OAAO;QACL/K,IAAI,EAAE,mBAAmB;QACzBC,KAAK,EAAE,eAAe;QACtBH,OAAO,EAAE,YAAY;QACrBN,KAAK,EAAEuL,SAAS,CAACsB,WAAW;OAC7B;IACH;IAEA;IACA,OAAO;MACLrM,IAAI,EAAE,cAAc;MACpBC,KAAK,EAAE,eAAe;MACtBH,OAAO,EAAE,YAAY;MACrBN,KAAK,EAAEuL,SAAS,GAAGA,SAAS,CAACsB,WAAW,EAAE,GAAG;KAC9C;EACH;EAEA;EACA5M,WAAWA,CAACgF,IAAS;IACnB,IAAIO,IAAI,GAAG,CAAC;IAEZ,IAAIP,IAAI,CAACO,IAAI,EAAE;MACbA,IAAI,GAAGP,IAAI,CAACO,IAAI;IAClB,CAAC,MAAM,IAAIP,IAAI,CAAC0C,UAAU,EAAE;MAC1B;MACAnC,IAAI,GAAGsH,IAAI,CAACC,KAAK,CAAC9H,IAAI,CAAC0C,UAAU,CAAC/E,MAAM,GAAG,IAAI,CAAC;IAClD;IAEA,IAAI4C,IAAI,KAAK,CAAC,EAAE,OAAO,EAAE;IAEzB,MAAMwH,KAAK,GAAG,CAAC,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;IACrC,IAAIC,SAAS,GAAG,CAAC;IAEjB,OAAOzH,IAAI,IAAI,IAAI,IAAIyH,SAAS,GAAGD,KAAK,CAACpK,MAAM,GAAG,CAAC,EAAE;MACnD4C,IAAI,IAAI,IAAI;MACZyH,SAAS,EAAE;IACb;IAEA,OAAO,GAAGzH,IAAI,CAAC0H,OAAO,CAACD,SAAS,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,IAAID,KAAK,CAACC,SAAS,CAAC,EAAE;EACvE;;;uCA5sBW7J,mBAAmB,EAAAzF,EAAA,CAAAwP,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAA1P,EAAA,CAAAwP,iBAAA,CAAAG,EAAA,CAAAC,cAAA,GAAA5P,EAAA,CAAAwP,iBAAA,CAAAK,EAAA,CAAAC,WAAA;IAAA;EAAA;;;YAAnBrK,mBAAmB;MAAAsK,SAAA;MAAAC,SAAA,WAAAC,0BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;UC7C1BlQ,EAHN,CAAAC,cAAA,aAAkD,aACS,aACrB,eACiF;UAC/GD,EAAA,CAAAE,MAAA,GACF;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACRH,EAAA,CAAAyC,UAAA,IAAA2N,iCAAA,gBAA+C;UACjDpQ,EAAA,CAAAG,YAAA,EAAM;UAGJH,EADF,CAAAC,cAAA,aAAkD,kBAEsD;UAA/DD,EAAA,CAAAS,UAAA,oBAAA4P,qDAAAtO,MAAA;YAAA/B,EAAA,CAAAW,aAAA,CAAA2P,GAAA;YAAA,OAAAtQ,EAAA,CAAAc,WAAA,CAAUqP,GAAA,CAAAnJ,cAAA,CAAAjF,MAAA,CAAsB;UAAA,EAAC;UADxE/B,EAAA,CAAAG,YAAA,EACsG;UAEtGH,EAAA,CAAAC,cAAA,eAC+E;UAC7ED,EAAA,CAAAgB,SAAA,SAA6C;UAAChB,EAAA,CAAAE,MAAA,IAChD;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAkHRH,EA/GA,CAAAyC,UAAA,KAAA8N,mCAAA,iBAAmF,KAAAC,mCAAA,iBASmB,KAAAC,mCAAA,kBAO/B,KAAAC,mCAAA,kBA+FoC;UA+BjH1Q,EAFI,CAAAG,YAAA,EAAM,EACF,EACF;;;UA9J+EH,EAAA,CAAAI,SAAA,GAAiC;UAAjCJ,EAAA,CAAA2Q,WAAA,cAAAR,GAAA,CAAA1J,aAAA,CAAiC;UAA1EzG,EAAA,CAAA2D,WAAA,mBAAAwM,GAAA,CAAA5P,MAAA,CAAA0F,QAAA,CAAwC;UAC5EjG,EAAA,CAAAI,SAAA,EACF;UADEJ,EAAA,CAAAwB,kBAAA,MAAA2O,GAAA,CAAA5P,MAAA,CAAA8B,KAAA,MACF;UACwBrC,EAAA,CAAAI,SAAA,EAAqB;UAArBJ,EAAA,CAAAkB,UAAA,SAAAiP,GAAA,CAAA5P,MAAA,CAAAC,QAAA,CAAqB;UAIAR,EAAA,CAAAI,SAAA,GAAiC;UACLJ,EAD5B,CAAAkB,UAAA,WAAAiP,GAAA,CAAA5P,MAAA,CAAAyF,eAAA,CAAiC,aAAAmK,GAAA,CAAA5P,MAAA,CAAA+F,QAAA,CAA6B,aAAA6J,GAAA,CAAA5P,MAAA,CAAAY,QAAA,CACN;UAE9EnB,EAAA,CAAAI,SAAA,GAAoC;UAACJ,EAArC,CAAA2D,WAAA,eAAAwM,GAAA,CAAA5P,MAAA,CAAAY,QAAA,CAAoC,oBAAAgP,GAAA,CAAA5P,MAAA,CAAAY,QAAA,CAA0C;UAEhGnB,EAAA,CAAAI,SAAA,EAAqC;UAArCJ,EAAA,CAAA4C,UAAA,CAAAuN,GAAA,CAAA5P,MAAA,CAAA6F,UAAA,WAAqC;UAAMpG,EAAA,CAAAI,SAAA,EAChD;UADgDJ,EAAA,CAAAwB,kBAAA,MAAA2O,GAAA,CAAA5P,MAAA,CAAA4F,UAAA,MAChD;UAG+CnG,EAAA,CAAAI,SAAA,EAAkC;UAAlCJ,EAAA,CAAAkB,UAAA,SAAAiP,GAAA,CAAAlP,QAAA,KAAAkP,GAAA,CAAA5P,MAAA,CAAA+F,QAAA,CAAkC;UASlCtG,EAAA,CAAAI,SAAA,EAAqD;UAArDJ,EAAA,CAAAkB,UAAA,SAAAiP,GAAA,CAAA5O,cAAA,KAAA4O,GAAA,CAAAlP,QAAA,KAAAkP,GAAA,CAAA5P,MAAA,CAAA+F,QAAA,CAAqD;UAO1EtG,EAAA,CAAAI,SAAA,EAA2C;UAA3CJ,EAAA,CAAAkB,UAAA,SAAAiP,GAAA,CAAA5P,MAAA,CAAA+F,QAAA,IAAA6J,GAAA,CAAA5P,MAAA,CAAAgG,WAAA,CAA2C;UA+F3CvG,EAAA,CAAAI,SAAA,EAA+E;UAA/EJ,EAAA,CAAAkB,UAAA,SAAAiP,GAAA,CAAA5P,MAAA,CAAA+F,QAAA,KAAA6J,GAAA,CAAA5P,MAAA,CAAAgG,WAAA,IAAA4J,GAAA,CAAAtM,QAAA,IAAAsM,GAAA,CAAAtM,QAAA,CAAAoB,MAAA,KAA+E;;;qBDpFnGrF,YAAY,EAAAgR,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,OAAA,EAAAF,EAAA,CAAAG,IAAA;MAAAC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}