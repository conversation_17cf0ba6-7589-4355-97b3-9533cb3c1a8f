{"ast": null, "code": "export class SecurityCamerasData {}", "map": {"version": 3, "names": ["SecurityCamerasData"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\@core\\data\\security-cameras.ts"], "sourcesContent": ["import { Observable } from 'rxjs';\r\n\r\nexport interface Camera {\r\n  title: string;\r\n  source: string;\r\n}\r\n\r\nexport abstract class SecurityCamerasData {\r\n  abstract getCamerasData(): Observable<Camera[]>;\r\n}\r\n"], "mappings": "AAOA,OAAM,MAAgBA,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}