{"ast": null, "code": "export function isFunction(value) {\n  return typeof value === 'function';\n}", "map": {"version": 3, "names": ["isFunction", "value"], "sources": ["C:/Users/<USER>/Documents/jeansalechange/adminFront/node_modules/rxjs/dist/esm/internal/util/isFunction.js"], "sourcesContent": ["export function isFunction(value) {\n    return typeof value === 'function';\n}\n"], "mappings": "AAAA,OAAO,SAASA,UAAUA,CAACC,KAAK,EAAE;EAC9B,OAAO,OAAOA,KAAK,KAAK,UAAU;AACtC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}