{"ast": null, "code": "import getQuarter from \"../getQuarter/index.js\";\nimport toDate from \"../toDate/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name differenceInCalendarQuarters\n * @category Quarter Helpers\n * @summary Get the number of calendar quarters between the given dates.\n *\n * @description\n * Get the number of calendar quarters between the given dates.\n *\n * @param {Date|Number} dateLeft - the later date\n * @param {Date|Number} dateRight - the earlier date\n * @returns {Number} the number of calendar quarters\n * @throws {TypeError} 2 arguments required\n *\n * @example\n * // How many calendar quarters are between 31 December 2013 and 2 July 2014?\n * const result = differenceInCalendarQuarters(\n *   new Date(2014, 6, 2),\n *   new Date(2013, 11, 31)\n * )\n * //=> 3\n */\nexport default function differenceInCalendarQuarters(dirtyDateLeft, dirtyDateRight) {\n  requiredArgs(2, arguments);\n  var dateLeft = toDate(dirtyDateLeft);\n  var dateRight = toDate(dirtyDateRight);\n  var yearDiff = dateLeft.getFullYear() - dateRight.getFullYear();\n  var quarterDiff = getQuarter(dateLeft) - getQuarter(dateRight);\n  return yearDiff * 4 + quarterDiff;\n}", "map": {"version": 3, "names": ["getQuarter", "toDate", "requiredArgs", "differenceInCalendarQuarters", "dirtyDateLeft", "dirtyDateRight", "arguments", "dateLeft", "dateRight", "yearDiff", "getFullYear", "quarterDiff"], "sources": ["C:/Users/<USER>/Documents/salechange-product/adminFront/node_modules/date-fns/esm/differenceInCalendarQuarters/index.js"], "sourcesContent": ["import getQuarter from \"../getQuarter/index.js\";\nimport toDate from \"../toDate/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name differenceInCalendarQuarters\n * @category Quarter Helpers\n * @summary Get the number of calendar quarters between the given dates.\n *\n * @description\n * Get the number of calendar quarters between the given dates.\n *\n * @param {Date|Number} dateLeft - the later date\n * @param {Date|Number} dateRight - the earlier date\n * @returns {Number} the number of calendar quarters\n * @throws {TypeError} 2 arguments required\n *\n * @example\n * // How many calendar quarters are between 31 December 2013 and 2 July 2014?\n * const result = differenceInCalendarQuarters(\n *   new Date(2014, 6, 2),\n *   new Date(2013, 11, 31)\n * )\n * //=> 3\n */\nexport default function differenceInCalendarQuarters(dirtyDateLeft, dirtyDateRight) {\n  requiredArgs(2, arguments);\n  var dateLeft = toDate(dirtyDateLeft);\n  var dateRight = toDate(dirtyDateRight);\n  var yearDiff = dateLeft.getFullYear() - dateRight.getFullYear();\n  var quarterDiff = getQuarter(dateLeft) - getQuarter(dateRight);\n  return yearDiff * 4 + quarterDiff;\n}"], "mappings": "AAAA,OAAOA,UAAU,MAAM,wBAAwB;AAC/C,OAAOC,MAAM,MAAM,oBAAoB;AACvC,OAAOC,YAAY,MAAM,+BAA+B;AACxD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,SAASC,4BAA4BA,CAACC,aAAa,EAAEC,cAAc,EAAE;EAClFH,YAAY,CAAC,CAAC,EAAEI,SAAS,CAAC;EAC1B,IAAIC,QAAQ,GAAGN,MAAM,CAACG,aAAa,CAAC;EACpC,IAAII,SAAS,GAAGP,MAAM,CAACI,cAAc,CAAC;EACtC,IAAII,QAAQ,GAAGF,QAAQ,CAACG,WAAW,CAAC,CAAC,GAAGF,SAAS,CAACE,WAAW,CAAC,CAAC;EAC/D,IAAIC,WAAW,GAAGX,UAAU,CAACO,QAAQ,CAAC,GAAGP,UAAU,CAACQ,SAAS,CAAC;EAC9D,OAAOC,QAAQ,GAAG,CAAC,GAAGE,WAAW;AACnC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}