{"ast": null, "code": ";\n(function (root, factory, undef) {\n  if (typeof exports === \"object\") {\n    // CommonJS\n    module.exports = exports = factory(require(\"./core\"), require(\"./x64-core\"));\n  } else if (typeof define === \"function\" && define.amd) {\n    // AMD\n    define([\"./core\", \"./x64-core\"], factory);\n  } else {\n    // Global (browser)\n    factory(root.CryptoJS);\n  }\n})(this, function (CryptoJS) {\n  (function () {\n    // Shortcuts\n    var C = CryptoJS;\n    var C_lib = C.lib;\n    var Hasher = C_lib.Hasher;\n    var C_x64 = C.x64;\n    var X64Word = C_x64.Word;\n    var X64WordArray = C_x64.WordArray;\n    var C_algo = C.algo;\n    function X64Word_create() {\n      return X64Word.create.apply(X64Word, arguments);\n    }\n\n    // Constants\n    var K = [X64Word_create(0x428a2f98, 0xd728ae22), X64Word_create(0x71374491, 0x23ef65cd), X64Word_create(0xb5c0fbcf, 0xec4d3b2f), X64Word_create(0xe9b5dba5, 0x8189dbbc), X64Word_create(0x3956c25b, 0xf348b538), X64Word_create(0x59f111f1, 0xb605d019), X64Word_create(0x923f82a4, 0xaf194f9b), X64Word_create(0xab1c5ed5, 0xda6d8118), X64Word_create(0xd807aa98, 0xa3030242), X64Word_create(0x12835b01, 0x45706fbe), X64Word_create(0x243185be, 0x4ee4b28c), X64Word_create(0x550c7dc3, 0xd5ffb4e2), X64Word_create(0x72be5d74, 0xf27b896f), X64Word_create(0x80deb1fe, 0x3b1696b1), X64Word_create(0x9bdc06a7, 0x25c71235), X64Word_create(0xc19bf174, 0xcf692694), X64Word_create(0xe49b69c1, 0x9ef14ad2), X64Word_create(0xefbe4786, 0x384f25e3), X64Word_create(0x0fc19dc6, 0x8b8cd5b5), X64Word_create(0x240ca1cc, 0x77ac9c65), X64Word_create(0x2de92c6f, 0x592b0275), X64Word_create(0x4a7484aa, 0x6ea6e483), X64Word_create(0x5cb0a9dc, 0xbd41fbd4), X64Word_create(0x76f988da, 0x831153b5), X64Word_create(0x983e5152, 0xee66dfab), X64Word_create(0xa831c66d, 0x2db43210), X64Word_create(0xb00327c8, 0x98fb213f), X64Word_create(0xbf597fc7, 0xbeef0ee4), X64Word_create(0xc6e00bf3, 0x3da88fc2), X64Word_create(0xd5a79147, 0x930aa725), X64Word_create(0x06ca6351, 0xe003826f), X64Word_create(0x14292967, 0x0a0e6e70), X64Word_create(0x27b70a85, 0x46d22ffc), X64Word_create(0x2e1b2138, 0x5c26c926), X64Word_create(0x4d2c6dfc, 0x5ac42aed), X64Word_create(0x53380d13, 0x9d95b3df), X64Word_create(0x650a7354, 0x8baf63de), X64Word_create(0x766a0abb, 0x3c77b2a8), X64Word_create(0x81c2c92e, 0x47edaee6), X64Word_create(0x92722c85, 0x1482353b), X64Word_create(0xa2bfe8a1, 0x4cf10364), X64Word_create(0xa81a664b, 0xbc423001), X64Word_create(0xc24b8b70, 0xd0f89791), X64Word_create(0xc76c51a3, 0x0654be30), X64Word_create(0xd192e819, 0xd6ef5218), X64Word_create(0xd6990624, 0x5565a910), X64Word_create(0xf40e3585, 0x5771202a), X64Word_create(0x106aa070, 0x32bbd1b8), X64Word_create(0x19a4c116, 0xb8d2d0c8), X64Word_create(0x1e376c08, 0x5141ab53), X64Word_create(0x2748774c, 0xdf8eeb99), X64Word_create(0x34b0bcb5, 0xe19b48a8), X64Word_create(0x391c0cb3, 0xc5c95a63), X64Word_create(0x4ed8aa4a, 0xe3418acb), X64Word_create(0x5b9cca4f, 0x7763e373), X64Word_create(0x682e6ff3, 0xd6b2b8a3), X64Word_create(0x748f82ee, 0x5defb2fc), X64Word_create(0x78a5636f, 0x43172f60), X64Word_create(0x84c87814, 0xa1f0ab72), X64Word_create(0x8cc70208, 0x1a6439ec), X64Word_create(0x90befffa, 0x23631e28), X64Word_create(0xa4506ceb, 0xde82bde9), X64Word_create(0xbef9a3f7, 0xb2c67915), X64Word_create(0xc67178f2, 0xe372532b), X64Word_create(0xca273ece, 0xea26619c), X64Word_create(0xd186b8c7, 0x21c0c207), X64Word_create(0xeada7dd6, 0xcde0eb1e), X64Word_create(0xf57d4f7f, 0xee6ed178), X64Word_create(0x06f067aa, 0x72176fba), X64Word_create(0x0a637dc5, 0xa2c898a6), X64Word_create(0x113f9804, 0xbef90dae), X64Word_create(0x1b710b35, 0x131c471b), X64Word_create(0x28db77f5, 0x23047d84), X64Word_create(0x32caab7b, 0x40c72493), X64Word_create(0x3c9ebe0a, 0x15c9bebc), X64Word_create(0x431d67c4, 0x9c100d4c), X64Word_create(0x4cc5d4be, 0xcb3e42b6), X64Word_create(0x597f299c, 0xfc657e2a), X64Word_create(0x5fcb6fab, 0x3ad6faec), X64Word_create(0x6c44198c, 0x4a475817)];\n\n    // Reusable objects\n    var W = [];\n    (function () {\n      for (var i = 0; i < 80; i++) {\n        W[i] = X64Word_create();\n      }\n    })();\n\n    /**\n     * SHA-512 hash algorithm.\n     */\n    var SHA512 = C_algo.SHA512 = Hasher.extend({\n      _doReset: function () {\n        this._hash = new X64WordArray.init([new X64Word.init(0x6a09e667, 0xf3bcc908), new X64Word.init(0xbb67ae85, 0x84caa73b), new X64Word.init(0x3c6ef372, 0xfe94f82b), new X64Word.init(0xa54ff53a, 0x5f1d36f1), new X64Word.init(0x510e527f, 0xade682d1), new X64Word.init(0x9b05688c, 0x2b3e6c1f), new X64Word.init(0x1f83d9ab, 0xfb41bd6b), new X64Word.init(0x5be0cd19, 0x137e2179)]);\n      },\n      _doProcessBlock: function (M, offset) {\n        // Shortcuts\n        var H = this._hash.words;\n        var H0 = H[0];\n        var H1 = H[1];\n        var H2 = H[2];\n        var H3 = H[3];\n        var H4 = H[4];\n        var H5 = H[5];\n        var H6 = H[6];\n        var H7 = H[7];\n        var H0h = H0.high;\n        var H0l = H0.low;\n        var H1h = H1.high;\n        var H1l = H1.low;\n        var H2h = H2.high;\n        var H2l = H2.low;\n        var H3h = H3.high;\n        var H3l = H3.low;\n        var H4h = H4.high;\n        var H4l = H4.low;\n        var H5h = H5.high;\n        var H5l = H5.low;\n        var H6h = H6.high;\n        var H6l = H6.low;\n        var H7h = H7.high;\n        var H7l = H7.low;\n\n        // Working variables\n        var ah = H0h;\n        var al = H0l;\n        var bh = H1h;\n        var bl = H1l;\n        var ch = H2h;\n        var cl = H2l;\n        var dh = H3h;\n        var dl = H3l;\n        var eh = H4h;\n        var el = H4l;\n        var fh = H5h;\n        var fl = H5l;\n        var gh = H6h;\n        var gl = H6l;\n        var hh = H7h;\n        var hl = H7l;\n\n        // Rounds\n        for (var i = 0; i < 80; i++) {\n          var Wil;\n          var Wih;\n\n          // Shortcut\n          var Wi = W[i];\n\n          // Extend message\n          if (i < 16) {\n            Wih = Wi.high = M[offset + i * 2] | 0;\n            Wil = Wi.low = M[offset + i * 2 + 1] | 0;\n          } else {\n            // Gamma0\n            var gamma0x = W[i - 15];\n            var gamma0xh = gamma0x.high;\n            var gamma0xl = gamma0x.low;\n            var gamma0h = (gamma0xh >>> 1 | gamma0xl << 31) ^ (gamma0xh >>> 8 | gamma0xl << 24) ^ gamma0xh >>> 7;\n            var gamma0l = (gamma0xl >>> 1 | gamma0xh << 31) ^ (gamma0xl >>> 8 | gamma0xh << 24) ^ (gamma0xl >>> 7 | gamma0xh << 25);\n\n            // Gamma1\n            var gamma1x = W[i - 2];\n            var gamma1xh = gamma1x.high;\n            var gamma1xl = gamma1x.low;\n            var gamma1h = (gamma1xh >>> 19 | gamma1xl << 13) ^ (gamma1xh << 3 | gamma1xl >>> 29) ^ gamma1xh >>> 6;\n            var gamma1l = (gamma1xl >>> 19 | gamma1xh << 13) ^ (gamma1xl << 3 | gamma1xh >>> 29) ^ (gamma1xl >>> 6 | gamma1xh << 26);\n\n            // W[i] = gamma0 + W[i - 7] + gamma1 + W[i - 16]\n            var Wi7 = W[i - 7];\n            var Wi7h = Wi7.high;\n            var Wi7l = Wi7.low;\n            var Wi16 = W[i - 16];\n            var Wi16h = Wi16.high;\n            var Wi16l = Wi16.low;\n            Wil = gamma0l + Wi7l;\n            Wih = gamma0h + Wi7h + (Wil >>> 0 < gamma0l >>> 0 ? 1 : 0);\n            Wil = Wil + gamma1l;\n            Wih = Wih + gamma1h + (Wil >>> 0 < gamma1l >>> 0 ? 1 : 0);\n            Wil = Wil + Wi16l;\n            Wih = Wih + Wi16h + (Wil >>> 0 < Wi16l >>> 0 ? 1 : 0);\n            Wi.high = Wih;\n            Wi.low = Wil;\n          }\n          var chh = eh & fh ^ ~eh & gh;\n          var chl = el & fl ^ ~el & gl;\n          var majh = ah & bh ^ ah & ch ^ bh & ch;\n          var majl = al & bl ^ al & cl ^ bl & cl;\n          var sigma0h = (ah >>> 28 | al << 4) ^ (ah << 30 | al >>> 2) ^ (ah << 25 | al >>> 7);\n          var sigma0l = (al >>> 28 | ah << 4) ^ (al << 30 | ah >>> 2) ^ (al << 25 | ah >>> 7);\n          var sigma1h = (eh >>> 14 | el << 18) ^ (eh >>> 18 | el << 14) ^ (eh << 23 | el >>> 9);\n          var sigma1l = (el >>> 14 | eh << 18) ^ (el >>> 18 | eh << 14) ^ (el << 23 | eh >>> 9);\n\n          // t1 = h + sigma1 + ch + K[i] + W[i]\n          var Ki = K[i];\n          var Kih = Ki.high;\n          var Kil = Ki.low;\n          var t1l = hl + sigma1l;\n          var t1h = hh + sigma1h + (t1l >>> 0 < hl >>> 0 ? 1 : 0);\n          var t1l = t1l + chl;\n          var t1h = t1h + chh + (t1l >>> 0 < chl >>> 0 ? 1 : 0);\n          var t1l = t1l + Kil;\n          var t1h = t1h + Kih + (t1l >>> 0 < Kil >>> 0 ? 1 : 0);\n          var t1l = t1l + Wil;\n          var t1h = t1h + Wih + (t1l >>> 0 < Wil >>> 0 ? 1 : 0);\n\n          // t2 = sigma0 + maj\n          var t2l = sigma0l + majl;\n          var t2h = sigma0h + majh + (t2l >>> 0 < sigma0l >>> 0 ? 1 : 0);\n\n          // Update working variables\n          hh = gh;\n          hl = gl;\n          gh = fh;\n          gl = fl;\n          fh = eh;\n          fl = el;\n          el = dl + t1l | 0;\n          eh = dh + t1h + (el >>> 0 < dl >>> 0 ? 1 : 0) | 0;\n          dh = ch;\n          dl = cl;\n          ch = bh;\n          cl = bl;\n          bh = ah;\n          bl = al;\n          al = t1l + t2l | 0;\n          ah = t1h + t2h + (al >>> 0 < t1l >>> 0 ? 1 : 0) | 0;\n        }\n\n        // Intermediate hash value\n        H0l = H0.low = H0l + al;\n        H0.high = H0h + ah + (H0l >>> 0 < al >>> 0 ? 1 : 0);\n        H1l = H1.low = H1l + bl;\n        H1.high = H1h + bh + (H1l >>> 0 < bl >>> 0 ? 1 : 0);\n        H2l = H2.low = H2l + cl;\n        H2.high = H2h + ch + (H2l >>> 0 < cl >>> 0 ? 1 : 0);\n        H3l = H3.low = H3l + dl;\n        H3.high = H3h + dh + (H3l >>> 0 < dl >>> 0 ? 1 : 0);\n        H4l = H4.low = H4l + el;\n        H4.high = H4h + eh + (H4l >>> 0 < el >>> 0 ? 1 : 0);\n        H5l = H5.low = H5l + fl;\n        H5.high = H5h + fh + (H5l >>> 0 < fl >>> 0 ? 1 : 0);\n        H6l = H6.low = H6l + gl;\n        H6.high = H6h + gh + (H6l >>> 0 < gl >>> 0 ? 1 : 0);\n        H7l = H7.low = H7l + hl;\n        H7.high = H7h + hh + (H7l >>> 0 < hl >>> 0 ? 1 : 0);\n      },\n      _doFinalize: function () {\n        // Shortcuts\n        var data = this._data;\n        var dataWords = data.words;\n        var nBitsTotal = this._nDataBytes * 8;\n        var nBitsLeft = data.sigBytes * 8;\n\n        // Add padding\n        dataWords[nBitsLeft >>> 5] |= 0x80 << 24 - nBitsLeft % 32;\n        dataWords[(nBitsLeft + 128 >>> 10 << 5) + 30] = Math.floor(nBitsTotal / 0x100000000);\n        dataWords[(nBitsLeft + 128 >>> 10 << 5) + 31] = nBitsTotal;\n        data.sigBytes = dataWords.length * 4;\n\n        // Hash final blocks\n        this._process();\n\n        // Convert hash to 32-bit word array before returning\n        var hash = this._hash.toX32();\n\n        // Return final computed hash\n        return hash;\n      },\n      clone: function () {\n        var clone = Hasher.clone.call(this);\n        clone._hash = this._hash.clone();\n        return clone;\n      },\n      blockSize: 1024 / 32\n    });\n\n    /**\n     * Shortcut function to the hasher's object interface.\n     *\n     * @param {WordArray|string} message The message to hash.\n     *\n     * @return {WordArray} The hash.\n     *\n     * @static\n     *\n     * @example\n     *\n     *     var hash = CryptoJS.SHA512('message');\n     *     var hash = CryptoJS.SHA512(wordArray);\n     */\n    C.SHA512 = Hasher._createHelper(SHA512);\n\n    /**\n     * Shortcut function to the HMAC's object interface.\n     *\n     * @param {WordArray|string} message The message to hash.\n     * @param {WordArray|string} key The secret key.\n     *\n     * @return {WordArray} The HMAC.\n     *\n     * @static\n     *\n     * @example\n     *\n     *     var hmac = CryptoJS.HmacSHA512(message, key);\n     */\n    C.HmacSHA512 = Hasher._createHmacHelper(SHA512);\n  })();\n  return CryptoJS.SHA512;\n});", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}