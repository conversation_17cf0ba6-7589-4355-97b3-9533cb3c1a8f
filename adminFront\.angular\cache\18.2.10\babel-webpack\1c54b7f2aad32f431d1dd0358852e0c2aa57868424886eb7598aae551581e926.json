{"ast": null, "code": "import toDate from \"../toDate/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name startOfYear\n * @category Year Helpers\n * @summary Return the start of a year for the given date.\n *\n * @description\n * Return the start of a year for the given date.\n * The result will be in the local timezone.\n *\n * @param {Date|Number} date - the original date\n * @returns {Date} the start of a year\n * @throws {TypeError} 1 argument required\n *\n * @example\n * // The start of a year for 2 September 2014 11:55:00:\n * const result = startOfYear(new Date(2014, 8, 2, 11, 55, 00))\n * //=> Wed Jan 01 2014 00:00:00\n */\nexport default function startOfYear(dirtyDate) {\n  requiredArgs(1, arguments);\n  var cleanDate = toDate(dirtyDate);\n  var date = new Date(0);\n  date.setFullYear(cleanDate.getFullYear(), 0, 1);\n  date.setHours(0, 0, 0, 0);\n  return date;\n}", "map": {"version": 3, "names": ["toDate", "requiredArgs", "startOfYear", "dirtyDate", "arguments", "cleanDate", "date", "Date", "setFullYear", "getFullYear", "setHours"], "sources": ["C:/Users/<USER>/Documents/salechange-product/adminFront/node_modules/date-fns/esm/startOfYear/index.js"], "sourcesContent": ["import toDate from \"../toDate/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name startOfYear\n * @category Year Helpers\n * @summary Return the start of a year for the given date.\n *\n * @description\n * Return the start of a year for the given date.\n * The result will be in the local timezone.\n *\n * @param {Date|Number} date - the original date\n * @returns {Date} the start of a year\n * @throws {TypeError} 1 argument required\n *\n * @example\n * // The start of a year for 2 September 2014 11:55:00:\n * const result = startOfYear(new Date(2014, 8, 2, 11, 55, 00))\n * //=> Wed Jan 01 2014 00:00:00\n */\nexport default function startOfYear(dirtyDate) {\n  requiredArgs(1, arguments);\n  var cleanDate = toDate(dirtyDate);\n  var date = new Date(0);\n  date.setFullYear(cleanDate.getFullYear(), 0, 1);\n  date.setHours(0, 0, 0, 0);\n  return date;\n}"], "mappings": "AAAA,OAAOA,MAAM,MAAM,oBAAoB;AACvC,OAAOC,YAAY,MAAM,+BAA+B;AACxD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,SAASC,WAAWA,CAACC,SAAS,EAAE;EAC7CF,YAAY,CAAC,CAAC,EAAEG,SAAS,CAAC;EAC1B,IAAIC,SAAS,GAAGL,MAAM,CAACG,SAAS,CAAC;EACjC,IAAIG,IAAI,GAAG,IAAIC,IAAI,CAAC,CAAC,CAAC;EACtBD,IAAI,CAACE,WAAW,CAACH,SAAS,CAACI,WAAW,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;EAC/CH,IAAI,CAACI,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;EACzB,OAAOJ,IAAI;AACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}