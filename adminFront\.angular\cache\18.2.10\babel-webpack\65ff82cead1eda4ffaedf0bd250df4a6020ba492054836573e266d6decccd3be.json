{"ast": null, "code": "export var EnumAllowType;\n(function (EnumAllowType) {\n  EnumAllowType[EnumAllowType[\"Read\"] = 1] = \"Read\";\n  EnumAllowType[EnumAllowType[\"Create\"] = 2] = \"Create\";\n  EnumAllowType[EnumAllowType[\"Update\"] = 3] = \"Update\";\n  EnumAllowType[EnumAllowType[\"Delete\"] = 4] = \"Delete\";\n  EnumAllowType[EnumAllowType[\"ExcelImport\"] = 5] = \"ExcelImport\";\n  EnumAllowType[EnumAllowType[\"ExcelExport\"] = 6] = \"ExcelExport\";\n  EnumAllowType[EnumAllowType[\"Report\"] = 7] = \"Report\";\n  EnumAllowType[EnumAllowType[\"ApiImport\"] = 8] = \"ApiImport\";\n  EnumAllowType[EnumAllowType[\"ChangePayStatus\"] = 9] = \"ChangePayStatus\";\n  EnumAllowType[EnumAllowType[\"ChangeProgress\"] = 10] = \"ChangeProgress\";\n})(EnumAllowType || (EnumAllowType = {}));", "map": {"version": 3, "names": ["EnumAllowType"], "sources": ["C:\\Users\\<USER>\\Documents\\jeansalechange\\adminFront\\src\\app\\shared\\enum\\enumAllowType.ts"], "sourcesContent": ["export enum EnumAllowType {\r\n  Read = 1,\r\n  Create = 2,\r\n  Update = 3,\r\n  Delete = 4,\r\n  ExcelImport = 5,\r\n  ExcelExport = 6,\r\n  Report = 7,\r\n  ApiImport = 8,\r\n  ChangePayStatus = 9,\r\n  ChangeProgress = 10\r\n}\r\n"], "mappings": "AAAA,WAAYA,aAWX;AAXD,WAAYA,aAAa;EACvBA,aAAA,CAAAA,aAAA,sBAAQ;EACRA,aAAA,CAAAA,aAAA,0BAAU;EACVA,aAAA,CAAAA,aAAA,0BAAU;EACVA,aAAA,CAAAA,aAAA,0BAAU;EACVA,aAAA,CAAAA,aAAA,oCAAe;EACfA,aAAA,CAAAA,aAAA,oCAAe;EACfA,aAAA,CAAAA,aAAA,0BAAU;EACVA,aAAA,CAAAA,aAAA,gCAAa;EACbA,aAAA,CAAAA,aAAA,4CAAmB;EACnBA,aAAA,CAAAA,aAAA,2CAAmB;AACrB,CAAC,EAXWA,aAAa,KAAbA,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}