{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { finalize, mergeMap, tap } from 'rxjs';\nimport * as XLSX from 'xlsx';\nimport { SharedModule } from '../../components/shared.module';\nimport { BaseComponent } from '../../components/base/baseComponent';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/shared/helper/allowHelper\";\nimport * as i2 from \"@nebular/theme\";\nimport * as i3 from \"src/app/shared/services/message.service\";\nimport * as i4 from \"src/app/shared/helper/validationHelper\";\nimport * as i5 from \"src/services/api/services\";\nimport * as i6 from \"src/app/shared/services/utility.service\";\nimport * as i7 from \"@angular/common\";\nimport * as i8 from \"@angular/forms\";\nimport * as i9 from \"../../components/breadcrumb/breadcrumb.component\";\nimport * as i10 from \"../../components/pagination/pagination.component\";\nfunction BuildingMaterialComponent_nb_option_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 35);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const buildCase_r2 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", buildCase_r2.cID);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", buildCase_r2.CBuildCaseName, \" \");\n  }\n}\nfunction BuildingMaterialComponent_button_22_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 36);\n    i0.ɵɵlistener(\"click\", function BuildingMaterialComponent_button_22_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.exportExelMaterialList());\n    });\n    i0.ɵɵtext(1, \"\\u532F\\u51FA \");\n    i0.ɵɵelement(2, \"i\", 23);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction BuildingMaterialComponent_button_23_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 37);\n    i0.ɵɵlistener(\"click\", function BuildingMaterialComponent_button_23_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.search());\n    });\n    i0.ɵɵtext(1, \" \\u67E5\\u8A62 \");\n    i0.ɵɵelement(2, \"i\", 38);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction BuildingMaterialComponent_button_24_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 39);\n    i0.ɵɵlistener(\"click\", function BuildingMaterialComponent_button_24_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r3 = i0.ɵɵnextContext();\n      const dialog_r7 = i0.ɵɵreference(52);\n      return i0.ɵɵresetView(ctx_r3.addNew(dialog_r7));\n    });\n    i0.ɵɵtext(1, \"\\u55AE\\u7B46\\u65B0\\u589E \");\n    i0.ɵɵelement(2, \"i\", 40);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction BuildingMaterialComponent_button_25_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 41);\n    i0.ɵɵlistener(\"click\", function BuildingMaterialComponent_button_25_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r8);\n      i0.ɵɵnextContext();\n      const inputFile_r9 = i0.ɵɵreference(27);\n      return i0.ɵɵresetView(inputFile_r9.click());\n    });\n    i0.ɵɵtext(1, \" \\u6279\\u6B21\\u532F\\u5165 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction BuildingMaterialComponent_th_45_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 27);\n    i0.ɵɵtext(1, \"\\u50F9\\u683C\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction BuildingMaterialComponent_tbody_48_tr_1_td_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r10 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(item_r10.CPrice);\n  }\n}\nfunction BuildingMaterialComponent_tbody_48_tr_1_button_13_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 46);\n    i0.ɵɵlistener(\"click\", function BuildingMaterialComponent_tbody_48_tr_1_button_13_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const item_r10 = i0.ɵɵnextContext().$implicit;\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      const dialog_r7 = i0.ɵɵreference(52);\n      return i0.ɵɵresetView(ctx_r3.onSelectedMaterial(item_r10, dialog_r7));\n    });\n    i0.ɵɵtext(1, \"\\u7DE8\\u8F2F\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction BuildingMaterialComponent_tbody_48_tr_1_button_14_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 47);\n    i0.ɵɵlistener(\"click\", function BuildingMaterialComponent_tbody_48_tr_1_button_14_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r12);\n      const item_r10 = i0.ɵɵnextContext().$implicit;\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      const imageBinder_r13 = i0.ɵɵreference(54);\n      return i0.ɵɵresetView(ctx_r3.bindImageForMaterial(item_r10, imageBinder_r13));\n    });\n    i0.ɵɵelement(1, \"i\", 48);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r10 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵproperty(\"title\", \"\\u70BA \" + item_r10.CName + \" \\u7D81\\u5B9A\\u5716\\u7247\");\n  }\n}\nfunction BuildingMaterialComponent_tbody_48_tr_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"td\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"td\");\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(11, BuildingMaterialComponent_tbody_48_tr_1_td_11_Template, 2, 1, \"td\", 32);\n    i0.ɵɵelementStart(12, \"td\", 43);\n    i0.ɵɵtemplate(13, BuildingMaterialComponent_tbody_48_tr_1_button_13_Template, 2, 0, \"button\", 44)(14, BuildingMaterialComponent_tbody_48_tr_1_button_14_Template, 2, 1, \"button\", 45);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const item_r10 = ctx.$implicit;\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r10.CId);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r10.CImageCode || \"\\u5F85\\u8A2D\\u5B9A\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate3(\"\", item_r10.CName, \" - \", item_r10.CPart, \" - \", item_r10.CLocation, \"\");\n    i0.ɵɵadvance();\n    i0.ɵɵstyleMap(!item_r10.CIsMapping ? \"color: red\" : \"\");\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(item_r10.CSelectName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r10.CDescription);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", item_r10.CShowPrice == true);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.isRead);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.isRead);\n  }\n}\nfunction BuildingMaterialComponent_tbody_48_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tbody\");\n    i0.ɵɵtemplate(1, BuildingMaterialComponent_tbody_48_tr_1_Template, 15, 12, \"tr\", 42);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.materialList);\n  }\n}\nfunction BuildingMaterialComponent_ng_template_51_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r14 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-card\", 49)(1, \"nb-card-header\");\n    i0.ɵɵtext(2, \" \\u5EFA\\u6750\\u7BA1\\u7406 > \\u65B0\\u589E\\u5EFA\\u6750 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"nb-card-body\", 50)(4, \"h5\", 51);\n    i0.ɵɵtext(5, \"\\u8ACB\\u8F38\\u5165\\u4E0B\\u65B9\\u5167\\u5BB9\\u65B0\\u589E\\u5EFA\\u6750\\u3002\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 52)(7, \"div\", 53)(8, \"label\", 54);\n    i0.ɵɵtext(9, \"\\u9078\\u9805\\u984C\\u76EE(\\u540D\\u7A31)\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"input\", 55);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function BuildingMaterialComponent_ng_template_51_Template_input_ngModelChange_10_listener($event) {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r3 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r3.selectedMaterial.CName, $event) || (ctx_r3.selectedMaterial.CName = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"div\", 53)(12, \"label\", 54);\n    i0.ɵɵtext(13, \"\\u9078\\u9805\\u984C\\u76EE(\\u9805\\u76EE)\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"input\", 55);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function BuildingMaterialComponent_ng_template_51_Template_input_ngModelChange_14_listener($event) {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r3 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r3.selectedMaterial.CPart, $event) || (ctx_r3.selectedMaterial.CPart = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(15, \"div\", 53)(16, \"label\", 54);\n    i0.ɵɵtext(17, \"\\u9078\\u9805\\u984C\\u76EE(\\u4F4D\\u7F6E)\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"input\", 55);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function BuildingMaterialComponent_ng_template_51_Template_input_ngModelChange_18_listener($event) {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r3 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r3.selectedMaterial.CLocation, $event) || (ctx_r3.selectedMaterial.CLocation = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(19, \"div\", 53)(20, \"label\", 54);\n    i0.ɵɵtext(21, \"\\u5EFA\\u6750\\u9078\\u9805\\u540D\\u7A31\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"input\", 55);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function BuildingMaterialComponent_ng_template_51_Template_input_ngModelChange_22_listener($event) {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r3 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r3.selectedMaterial.CSelectName, $event) || (ctx_r3.selectedMaterial.CSelectName = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(23, \"div\", 53)(24, \"label\", 56);\n    i0.ɵɵtext(25, \"\\u5EFA\\u6750\\u8AAA\\u660E\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"textarea\", 57);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function BuildingMaterialComponent_ng_template_51_Template_textarea_ngModelChange_26_listener($event) {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r3 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r3.selectedMaterial.CDescription, $event) || (ctx_r3.selectedMaterial.CDescription = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(27, \"div\", 53)(28, \"label\", 56);\n    i0.ɵɵtext(29, \"\\u50F9\\u683C\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(30, \"input\", 58);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function BuildingMaterialComponent_ng_template_51_Template_input_ngModelChange_30_listener($event) {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r3 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r3.selectedMaterial.CPrice, $event) || (ctx_r3.selectedMaterial.CPrice = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(31, \"nb-card-footer\", 33)(32, \"button\", 59);\n    i0.ɵɵlistener(\"click\", function BuildingMaterialComponent_ng_template_51_Template_button_click_32_listener() {\n      const ref_r15 = i0.ɵɵrestoreView(_r14).dialogRef;\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.onClose(ref_r15));\n    });\n    i0.ɵɵtext(33, \"\\u95DC\\u9589\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(34, \"button\", 60);\n    i0.ɵɵlistener(\"click\", function BuildingMaterialComponent_ng_template_51_Template_button_click_34_listener() {\n      const ref_r15 = i0.ɵɵrestoreView(_r14).dialogRef;\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.onSubmit(ref_r15));\n    });\n    i0.ɵɵtext(35, \"\\u5132\\u5B58\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(10);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r3.selectedMaterial.CName);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r3.selectedMaterial.CPart);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r3.selectedMaterial.CLocation);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r3.selectedMaterial.CSelectName);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r3.selectedMaterial.CDescription);\n    i0.ɵɵproperty(\"rows\", 4);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r3.selectedMaterial.CPrice);\n  }\n}\nfunction BuildingMaterialComponent_ng_template_53_nb_option_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 35);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const option_r17 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", option_r17.value);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", option_r17.label, \" \");\n  }\n}\nfunction BuildingMaterialComponent_ng_template_53_div_36_i_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 103);\n  }\n}\nfunction BuildingMaterialComponent_ng_template_53_div_36_img_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 104);\n  }\n  if (rf & 2) {\n    const image_r19 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵproperty(\"src\", image_r19.thumbnailUrl, i0.ɵɵsanitizeUrl)(\"alt\", image_r19.name);\n  }\n}\nfunction BuildingMaterialComponent_ng_template_53_div_36_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 105);\n    i0.ɵɵelement(1, \"i\", 106);\n    i0.ɵɵelementStart(2, \"div\", 107);\n    i0.ɵɵtext(3, \"\\u7121\\u9810\\u89BD\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction BuildingMaterialComponent_ng_template_53_div_36_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r18 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 91);\n    i0.ɵɵlistener(\"click\", function BuildingMaterialComponent_ng_template_53_div_36_Template_div_click_0_listener() {\n      const image_r19 = i0.ɵɵrestoreView(_r18).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r3.toggleImageSelection(image_r19));\n    });\n    i0.ɵɵelementStart(1, \"div\", 92)(2, \"div\", 93);\n    i0.ɵɵtemplate(3, BuildingMaterialComponent_ng_template_53_div_36_i_3_Template, 1, 0, \"i\", 94);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"button\", 95);\n    i0.ɵɵlistener(\"click\", function BuildingMaterialComponent_ng_template_53_div_36_Template_button_click_4_listener($event) {\n      const image_r19 = i0.ɵɵrestoreView(_r18).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      const imagePreview_r20 = i0.ɵɵreference(56);\n      return i0.ɵɵresetView(ctx_r3.previewImage(image_r19, imagePreview_r20, $event));\n    });\n    i0.ɵɵelement(5, \"i\", 96);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 97);\n    i0.ɵɵtemplate(7, BuildingMaterialComponent_ng_template_53_div_36_img_7_Template, 1, 2, \"img\", 98)(8, BuildingMaterialComponent_ng_template_53_div_36_div_8_Template, 4, 0, \"div\", 99);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"div\", 100)(10, \"div\", 101);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"div\", 102);\n    i0.ɵɵtext(13);\n    i0.ɵɵpipe(14, \"number\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const image_r19 = ctx.$implicit;\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassProp(\"selected\", ctx_r3.isImageSelected(image_r19));\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"checked\", ctx_r3.isImageSelected(image_r19));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.isImageSelected(image_r19));\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", image_r19.thumbnailUrl);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !image_r19.thumbnailUrl);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"title\", image_r19.name);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(image_r19.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", i0.ɵɵpipeBind1(14, 10, image_r19.size), \" KB\");\n  }\n}\nfunction BuildingMaterialComponent_ng_template_53_div_37_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 108);\n    i0.ɵɵelement(1, \"i\", 109);\n    i0.ɵɵelementStart(2, \"div\");\n    i0.ɵɵtext(3, \"\\u627E\\u4E0D\\u5230\\u76F8\\u7B26\\u7684\\u5716\\u7247\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction BuildingMaterialComponent_ng_template_53_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r16 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-card\", 61)(1, \"nb-card-header\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"nb-card-body\", 62)(4, \"div\", 63)(5, \"div\", 64);\n    i0.ɵɵelement(6, \"i\", 65);\n    i0.ɵɵelementStart(7, \"div\", 66)(8, \"div\", 67);\n    i0.ɵɵtext(9, \"\\u667A\\u6167\\u7D81\\u5B9A\\u63D0\\u793A\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"div\");\n    i0.ɵɵtext(11, \"\\u7576\\u5716\\u7247\\u6A94\\u540D\\u5305\\u542B\\u5EFA\\u6750\\u76F8\\u95DC\\u6587\\u5B57\\uFF08\\u5982\\u540D\\u7A31\\u3001\\u9805\\u76EE\\u3001\\u4F4D\\u7F6E\\u7B49\\u95DC\\u9375\\u5B57\\uFF09\\u6642\\uFF0C\\u7CFB\\u7D71\\u6703\\u81EA\\u52D5\\u9032\\u884C\\u667A\\u6167\\u914D\\u5C0D\\u548C\\u7D81\\u5B9A\\u3002\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(12, \"div\", 68)(13, \"div\", 69)(14, \"label\", 70);\n    i0.ɵɵtext(15, \"\\u5716\\u7247\\u985E\\u5225\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"nb-select\", 71);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function BuildingMaterialComponent_ng_template_53_Template_nb_select_ngModelChange_16_listener($event) {\n      i0.ɵɵrestoreView(_r16);\n      const ctx_r3 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r3.selectedCategory, $event) || (ctx_r3.selectedCategory = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"selectedChange\", function BuildingMaterialComponent_ng_template_53_Template_nb_select_selectedChange_16_listener($event) {\n      i0.ɵɵrestoreView(_r16);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.categoryChanged($event));\n    });\n    i0.ɵɵtemplate(17, BuildingMaterialComponent_ng_template_53_nb_option_17_Template, 2, 2, \"nb-option\", 12);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(18, \"div\", 72)(19, \"label\", 70);\n    i0.ɵɵtext(20, \"\\u641C\\u5C0B\\u5716\\u7247\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"input\", 73);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function BuildingMaterialComponent_ng_template_53_Template_input_ngModelChange_21_listener($event) {\n      i0.ɵɵrestoreView(_r16);\n      const ctx_r3 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r3.imageSearchTerm, $event) || (ctx_r3.imageSearchTerm = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"input\", function BuildingMaterialComponent_ng_template_53_Template_input_input_21_listener() {\n      i0.ɵɵrestoreView(_r16);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.filterImages());\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(22, \"div\", 74)(23, \"button\", 75);\n    i0.ɵɵlistener(\"click\", function BuildingMaterialComponent_ng_template_53_Template_button_click_23_listener() {\n      i0.ɵɵrestoreView(_r16);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.loadImages());\n    });\n    i0.ɵɵtext(24, \" \\u91CD\\u65B0\\u8F09\\u5165 \");\n    i0.ɵɵelement(25, \"i\", 76);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(26, \"div\", 77)(27, \"button\", 78);\n    i0.ɵɵlistener(\"click\", function BuildingMaterialComponent_ng_template_53_Template_button_click_27_listener() {\n      i0.ɵɵrestoreView(_r16);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.selectAllImages());\n    });\n    i0.ɵɵtext(28, \" \\u5168\\u9078 \");\n    i0.ɵɵelement(29, \"i\", 79);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(30, \"button\", 80);\n    i0.ɵɵlistener(\"click\", function BuildingMaterialComponent_ng_template_53_Template_button_click_30_listener() {\n      i0.ɵɵrestoreView(_r16);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.clearAllSelection());\n    });\n    i0.ɵɵtext(31, \" \\u6E05\\u9664\\u9078\\u53D6 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(32, \"div\", 81);\n    i0.ɵɵtext(33);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(34, \"div\", 82)(35, \"div\", 83);\n    i0.ɵɵtemplate(36, BuildingMaterialComponent_ng_template_53_div_36_Template, 15, 12, \"div\", 84);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(37, BuildingMaterialComponent_ng_template_53_div_37_Template, 4, 0, \"div\", 85);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(38, \"nb-card-footer\", 86)(39, \"div\", 87);\n    i0.ɵɵtext(40);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(41, \"div\", 88)(42, \"button\", 89);\n    i0.ɵɵlistener(\"click\", function BuildingMaterialComponent_ng_template_53_Template_button_click_42_listener() {\n      const ref_r21 = i0.ɵɵrestoreView(_r16).dialogRef;\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.onCloseImageBinder(ref_r21));\n    });\n    i0.ɵɵtext(43, \"\\u53D6\\u6D88\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(44, \"button\", 90);\n    i0.ɵɵlistener(\"click\", function BuildingMaterialComponent_ng_template_53_Template_button_click_44_listener() {\n      const ref_r21 = i0.ɵɵrestoreView(_r16).dialogRef;\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.onConfirmImageSelection(ref_r21));\n    });\n    i0.ɵɵtext(45);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \\u5716\\u7247\\u7D81\\u5B9A - \", ctx_r3.selectedMaterial.CName ? \"\\u70BA \" + ctx_r3.selectedMaterial.CName + \" \\u9078\\u64C7\\u5EFA\\u6750\\u5716\\u7247\" : \"\\u9078\\u64C7\\u5EFA\\u6750\\u5716\\u7247\", \" \");\n    i0.ɵɵadvance(14);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r3.selectedCategory);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.categoryOptions);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r3.imageSearchTerm);\n    i0.ɵɵadvance(12);\n    i0.ɵɵtextInterpolate1(\" \\u5DF2\\u9078\\u53D6: \", ctx_r3.selectedImages.length, \" \\u5F35\\u5716\\u7247 \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.filteredImages);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.filteredImages.length === 0);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \\u5171 \", ctx_r3.availableImages.length, \" \\u5F35\\u5716\\u7247 \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"disabled\", ctx_r3.selectedImages.length === 0);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \\u78BA\\u5B9A\\u9078\\u64C7 (\", ctx_r3.selectedImages.length, \") \");\n  }\n}\nfunction BuildingMaterialComponent_ng_template_55_img_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 117);\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"src\", ctx_r3.previewingImage.fullUrl, i0.ɵɵsanitizeUrl)(\"alt\", ctx_r3.previewingImage.name);\n  }\n}\nfunction BuildingMaterialComponent_ng_template_55_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 105);\n    i0.ɵɵelement(1, \"i\", 118);\n    i0.ɵɵelementStart(2, \"div\");\n    i0.ɵɵtext(3, \"\\u5716\\u7247\\u8F09\\u5165\\u5931\\u6557\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction BuildingMaterialComponent_ng_template_55_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r22 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-card\", 110)(1, \"nb-card-header\", 86)(2, \"span\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 88)(5, \"button\", 111);\n    i0.ɵɵlistener(\"click\", function BuildingMaterialComponent_ng_template_55_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r22);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.previousImage());\n    });\n    i0.ɵɵelement(6, \"i\", 112);\n    i0.ɵɵtext(7, \" \\u4E0A\\u4E00\\u5F35 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"button\", 111);\n    i0.ɵɵlistener(\"click\", function BuildingMaterialComponent_ng_template_55_Template_button_click_8_listener() {\n      i0.ɵɵrestoreView(_r22);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.nextImage());\n    });\n    i0.ɵɵtext(9, \" \\u4E0B\\u4E00\\u5F35 \");\n    i0.ɵɵelement(10, \"i\", 113);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(11, \"nb-card-body\", 114);\n    i0.ɵɵtemplate(12, BuildingMaterialComponent_ng_template_55_img_12_Template, 1, 2, \"img\", 115)(13, BuildingMaterialComponent_ng_template_55_div_13_Template, 4, 0, \"div\", 99);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"nb-card-footer\", 86)(15, \"div\", 87);\n    i0.ɵɵtext(16);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"div\", 88)(18, \"button\", 116);\n    i0.ɵɵlistener(\"click\", function BuildingMaterialComponent_ng_template_55_Template_button_click_18_listener() {\n      i0.ɵɵrestoreView(_r22);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.toggleImageSelectionInPreview());\n    });\n    i0.ɵɵtext(19);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"button\", 89);\n    i0.ɵɵlistener(\"click\", function BuildingMaterialComponent_ng_template_55_Template_button_click_20_listener() {\n      const ref_r23 = i0.ɵɵrestoreView(_r22).dialogRef;\n      return i0.ɵɵresetView(ref_r23.close());\n    });\n    i0.ɵɵtext(21, \"\\u95DC\\u9589\");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\\u5716\\u7247\\u9810\\u89BD - \", ctx_r3.previewingImage == null ? null : ctx_r3.previewingImage.name, \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", ctx_r3.currentPreviewIndex <= 0);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"disabled\", ctx_r3.currentPreviewIndex >= ctx_r3.filteredImages.length - 1);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.previewingImage && ctx_r3.previewingImage.fullUrl);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r3.previewingImage || !ctx_r3.previewingImage.fullUrl);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate2(\" \", ctx_r3.currentPreviewIndex + 1, \" / \", ctx_r3.filteredImages.length, \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.previewingImage && ctx_r3.isImageSelected(ctx_r3.previewingImage) ? \"\\u53D6\\u6D88\\u9078\\u53D6\" : \"\\u9078\\u53D6\\u6B64\\u5716\\u7247\", \" \");\n  }\n}\nfunction BuildingMaterialComponent_ng_template_57_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r24 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-card\", 119)(1, \"nb-card-header\")(2, \"span\");\n    i0.ɵɵtext(3, \" \\u6AA2\\u8996 \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"nb-card-body\", 120)(5, \"div\", 121);\n    i0.ɵɵelement(6, \"img\", 122);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"nb-card-footer\")(8, \"div\", 123)(9, \"button\", 124);\n    i0.ɵɵlistener(\"click\", function BuildingMaterialComponent_ng_template_57_Template_button_click_9_listener() {\n      const ref_r25 = i0.ɵɵrestoreView(_r24).dialogRef;\n      return i0.ɵɵresetView(ref_r25.close());\n    });\n    i0.ɵɵtext(10, \"\\u53D6\\u6D88\");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"src\", ctx_r3.currentImageShowing, i0.ɵɵsanitizeUrl);\n  }\n}\n// 圖片類別枚舉\nvar PictureCategory;\n(function (PictureCategory) {\n  PictureCategory[PictureCategory[\"NONE\"] = 0] = \"NONE\";\n  PictureCategory[PictureCategory[\"BUILDING_MATERIAL\"] = 1] = \"BUILDING_MATERIAL\";\n  PictureCategory[PictureCategory[\"SCHEMATIC\"] = 2] = \"SCHEMATIC\"; // 示意圖片\n})(PictureCategory || (PictureCategory = {}));\nexport class BuildingMaterialComponent extends BaseComponent {\n  constructor(_allow, dialogService, message, valid, _buildCaseService, _materialService, _utilityService, _pictureService) {\n    super(_allow);\n    this._allow = _allow;\n    this.dialogService = dialogService;\n    this.message = message;\n    this.valid = valid;\n    this._buildCaseService = _buildCaseService;\n    this._materialService = _materialService;\n    this._utilityService = _utilityService;\n    this._pictureService = _pictureService;\n    this.isNew = true;\n    this.listBuildCases = [];\n    this.materialOptions = [{\n      value: null,\n      label: '全部'\n    }, {\n      value: false,\n      label: '方案'\n    }, {\n      value: true,\n      label: '選樣'\n    }];\n    this.materialOptionsId = null;\n    this.CSelectName = \"\";\n    // 移除圖片檔名相關欄位\n    // CImageCode: string = \"\"\n    // CInfoImageCode: string = \"\"\n    // 預留建材代號欄位，等後端API支援後再啟用\n    this.CMaterialCode = \"\";\n    this.ShowPrice = false;\n    this.currentImageShowing = \"\";\n    this.filterMapping = false;\n    this.CIsMapping = true;\n    // 圖片綁定相關屬性\n    this.availableImages = [];\n    this.filteredImages = [];\n    this.selectedImages = [];\n    this.imageSearchTerm = \"\";\n    this.previewingImage = null;\n    this.currentPreviewIndex = 0;\n    // 類別選項\n    this.categoryOptions = [{\n      value: PictureCategory.BUILDING_MATERIAL,\n      label: '建材圖片'\n    }, {\n      value: PictureCategory.SCHEMATIC,\n      label: '示意圖片'\n    }];\n    this.selectedCategory = PictureCategory.BUILDING_MATERIAL;\n    this.isCategorySelected = true; // 預設選擇建材圖片\n    // 讓模板可以使用 enum\n    this.PictureCategory = PictureCategory;\n  }\n  ngOnInit() {\n    this.getListBuildCase();\n  }\n  getListBuildCase() {\n    this._buildCaseService.apiBuildCaseGetAllBuildCaseForSelectPost$Json({\n      body: {\n        CIsPagi: false,\n        CStatus: 1\n      }\n    }).pipe(tap(res => {\n      if (res.StatusCode == 0) {\n        this.listBuildCases = res.Entries?.length ? res.Entries : [];\n        this.selectedBuildCaseId = this.listBuildCases[0].cID;\n      }\n    }), mergeMap(() => this.getMaterialList())).subscribe();\n  }\n  getMaterialList(pageIndex = 1) {\n    return this._materialService.apiMaterialGetMaterialListPost$Json({\n      body: {\n        CBuildCaseId: this.selectedBuildCaseId,\n        CPlanUse: this.materialOptionsId,\n        CSelectName: this.CSelectName,\n        // 移除圖片檔名相關查詢條件\n        // CImageCode: this.CImageCode,\n        // CInfoImageCode: this.CInfoImageCode,\n        // 預留建材代號查詢條件，等後端API支援後再啟用\n        // CMaterialCode: this.CMaterialCode,\n        PageSize: this.pageSize,\n        PageIndex: pageIndex,\n        CIsMapping: this.CIsMapping\n      }\n    }).pipe(tap(res => {\n      if (res.StatusCode == 0) {\n        this.materialList = res.Entries ?? [];\n        this.totalRecords = res.TotalItems;\n        if (this.materialList.length > 0) {\n          this.ShowPrice = this.materialList[0].CShowPrice;\n        }\n      }\n    }));\n  }\n  search() {\n    this.getMaterialList().subscribe();\n  }\n  pageChanged(pageIndex) {\n    this.getMaterialList(pageIndex).subscribe();\n  }\n  exportExelMaterialList() {\n    this._materialService.apiMaterialExportExcelMaterialListPost$Json({\n      body: this.selectedBuildCaseId\n    }).subscribe(res => {\n      if (res.StatusCode == 0) {\n        if (res.Entries.FileByte) {\n          this._utilityService.downloadExcelFile(res.Entries.FileByte, '建材管理');\n        }\n      }\n    });\n  }\n  exportExelMaterialTemplate() {\n    this._materialService.apiMaterialExportExcelMaterialTemplatePost$Json({\n      body: this.selectedBuildCaseId\n    }).subscribe(res => {\n      if (res.StatusCode == 0) {\n        if (res.Entries.FileByte) {\n          this._utilityService.downloadExcelFile(res.Entries.FileByte, '建材管理');\n        }\n      }\n    });\n  }\n  addNew(ref) {\n    this.isNew = true;\n    this.selectedMaterial = {};\n    this.dialogService.open(ref);\n  }\n  onSelectedMaterial(data, ref) {\n    this.isNew = false;\n    this.selectedMaterial = {\n      ...data\n    };\n    this.dialogService.open(ref);\n  }\n  bindImageForMaterial(data, ref) {\n    this.selectedMaterial = {\n      ...data\n    };\n    this.loadImages();\n    this.dialogService.open(ref, {\n      closeOnBackdropClick: false\n    });\n  }\n  validation() {\n    this.valid.clear();\n    this.valid.required('[名稱]', this.selectedMaterial.CName);\n    this.valid.required('[項目]', this.selectedMaterial.CPart);\n    this.valid.required('[位置]', this.selectedMaterial.CLocation);\n    this.valid.required('[建材選項名稱]', this.selectedMaterial.CSelectName);\n    // 移除圖片檔名驗證\n    // this.valid.required('[建材圖片編號]', this.selectedMaterial.CImageCode)\n    // 預留建材代號驗證，等後端API支援後再啟用\n    // this.valid.required('[建材代號]', this.selectedMaterial.CMaterialCode)\n    this.valid.isStringMaxLength('[名稱]', this.selectedMaterial.CName, 30);\n    this.valid.isStringMaxLength('[項目]', this.selectedMaterial.CPart, 30);\n    this.valid.isStringMaxLength('[位置]', this.selectedMaterial.CLocation, 30);\n    this.valid.isStringMaxLength('[建材選項名稱]', this.selectedMaterial.CSelectName, 30);\n    // 移除圖片檔名長度驗證\n    // this.valid.isStringMaxLength('[建材圖片編號]', this.selectedMaterial.CImageCode, 30)\n    // 預留建材代號長度驗證，等後端API支援後再啟用\n    // this.valid.isStringMaxLength('[建材代號]', this.selectedMaterial.CMaterialCode, 30)\n  }\n  onSubmit(ref) {\n    this.validation();\n    if (this.valid.errorMessages.length > 0) {\n      this.message.showErrorMSGs(this.valid.errorMessages);\n      return;\n    }\n    this._materialService.apiMaterialSaveMaterialAdminPost$Json({\n      body: {\n        CBuildCaseId: this.selectedBuildCaseId,\n        // 暫時保留 CImageCode 給圖片綁定功能使用\n        CImageCode: this.selectedMaterial.CImageCode,\n        // 預留建材代號，等後端API支援後再啟用\n        // CMaterialCode: this.selectedMaterial.CMaterialCode,\n        CName: this.selectedMaterial.CName,\n        CPart: this.selectedMaterial.CPart,\n        CLocation: this.selectedMaterial.CLocation,\n        CSelectName: this.selectedMaterial.CSelectName,\n        CDescription: this.selectedMaterial.CDescription,\n        CMaterialId: this.isNew ? null : this.selectedMaterial.CId,\n        CPrice: this.selectedMaterial.CPrice\n      }\n    }).pipe(tap(res => {\n      if (res.StatusCode === 0) {\n        this.message.showSucessMSG(\"執行成功\");\n      } else {\n        this.message.showErrorMSG(res.Message);\n      }\n    }), mergeMap(() => this.getMaterialList()), finalize(() => ref.close())).subscribe();\n  }\n  onClose(ref) {\n    ref.close();\n  }\n  detectFileExcel(event) {\n    const target = event.target;\n    const reader = new FileReader();\n    reader.readAsBinaryString(target.files[0]);\n    reader.onload = e => {\n      const binarystr = e.target.result;\n      const wb = XLSX.read(binarystr, {\n        type: 'binary'\n      });\n      const wsname = wb.SheetNames[0];\n      const ws = wb.Sheets[wsname];\n      let isValidFile = true;\n      const data = XLSX.utils.sheet_to_json(ws);\n      if (data && data.length > 0) {\n        data.forEach(x => {\n          if (!(x['名稱'] && x['項目'] && x['位置'] && x['建材選項名稱'] && (x['建材圖片檔名'] || x['建材說明'] || x['示意圖片檔名']))) {\n            isValidFile = false;\n          }\n        });\n        if (!isValidFile) {\n          this.message.showErrorMSG(\"导入文件时出现错误\");\n        } else {\n          this._materialService.apiMaterialImportExcelMaterialListPost$Json({\n            body: {\n              CBuildCaseId: this.selectedBuildCaseId,\n              CFile: target.files[0]\n            }\n          }).pipe(tap(res => {\n            if (res.StatusCode == 0) {\n              this.message.showSucessMSG(\"執行成功\");\n            } else {\n              this.message.showErrorMSG(res.Message);\n            }\n          }), mergeMap(() => this.getMaterialList(1))).subscribe();\n        }\n      } else {\n        this.message.showErrorMSG(\"匯入的檔案內容為空，請檢查檔案並重新上傳。\");\n      }\n      event.target.value = null;\n    };\n  }\n  showImage(imageUrl, dialog) {\n    this.currentImageShowing = imageUrl;\n    this.dialogService.open(dialog);\n  }\n  changeFilter() {\n    if (this.filterMapping) {\n      this.CIsMapping = false;\n      this.getMaterialList().subscribe();\n    } else {\n      this.CIsMapping = true;\n      this.getMaterialList().subscribe();\n    }\n  }\n  // 圖片綁定功能方法\n  openImageBinder(ref) {\n    this.loadImages();\n    this.dialogService.open(ref, {\n      closeOnBackdropClick: false\n    });\n  }\n  loadImages() {\n    // 使用 PictureService API 載入圖片列表\n    if (this.isCategorySelected && this.selectedBuildCaseId) {\n      this._pictureService.apiPictureGetPicturelListPost$Json({\n        body: {\n          CBuildCaseId: this.selectedBuildCaseId,\n          cPictureType: this.selectedCategory,\n          PageIndex: 1,\n          PageSize: 1000 // 載入所有圖片\n        }\n      }).subscribe(res => {\n        if (res.StatusCode === 0) {\n          // 將 API 回應轉換為 ImageItem 格式\n          this.availableImages = res.Entries?.map(picture => ({\n            id: picture.CId?.toString() || '',\n            name: picture.CPictureCode || picture.CName || '',\n            size: 0,\n            // API 中沒有檔案大小資訊\n            thumbnailUrl: picture.CBase64 ? `data:image/jpeg;base64,${picture.CBase64}` : '',\n            fullUrl: picture.CBase64 ? `data:image/jpeg;base64,${picture.CBase64}` : '',\n            lastModified: picture.CUpdateDT ? new Date(picture.CUpdateDT) : new Date()\n          })) || [];\n          this.filteredImages = [...this.availableImages];\n        } else {\n          this.message.showErrorMSG(res.Message || '載入圖片失敗');\n          this.availableImages = [];\n          this.filteredImages = [];\n        }\n      });\n    } else {\n      // 如果沒有選擇類別或建案，清空圖片列表\n      this.availableImages = [];\n      this.filteredImages = [];\n    }\n  }\n  filterImages() {\n    if (!this.imageSearchTerm.trim()) {\n      this.filteredImages = [...this.availableImages];\n    } else {\n      const searchTerm = this.imageSearchTerm.toLowerCase();\n      this.filteredImages = this.availableImages.filter(image => image.name.toLowerCase().includes(searchTerm));\n    }\n  }\n  toggleImageSelection(image) {\n    const index = this.selectedImages.findIndex(selected => selected.id === image.id);\n    if (index > -1) {\n      this.selectedImages.splice(index, 1);\n    } else {\n      this.selectedImages.push(image);\n    }\n  }\n  isImageSelected(image) {\n    return this.selectedImages.some(selected => selected.id === image.id);\n  }\n  selectAllImages() {\n    this.selectedImages = [...this.filteredImages];\n  }\n  clearAllSelection() {\n    this.selectedImages = [];\n  }\n  previewImage(image, imagePreviewRef, event) {\n    event.stopPropagation();\n    this.previewingImage = image;\n    this.currentPreviewIndex = this.filteredImages.findIndex(img => img.id === image.id);\n    this.dialogService.open(imagePreviewRef);\n  }\n  previousImage() {\n    if (this.currentPreviewIndex > 0) {\n      this.currentPreviewIndex--;\n      this.previewingImage = this.filteredImages[this.currentPreviewIndex];\n    }\n  }\n  nextImage() {\n    if (this.currentPreviewIndex < this.filteredImages.length - 1) {\n      this.currentPreviewIndex++;\n      this.previewingImage = this.filteredImages[this.currentPreviewIndex];\n    }\n  }\n  toggleImageSelectionInPreview() {\n    if (this.previewingImage) {\n      this.toggleImageSelection(this.previewingImage);\n    }\n  }\n  onConfirmImageSelection(ref) {\n    if (this.selectedImages.length > 0) {\n      // 如果只選取一張圖片，直接設定到建材圖片檔名\n      if (this.selectedImages.length === 1) {\n        this.selectedMaterial.CImageCode = this.selectedImages[0].name;\n      } else {\n        // 多張圖片的話，可以設定第一張，或者讓使用者選擇主要圖片\n        const imageNames = this.selectedImages.map(img => img.name).join(', ');\n        this.message.showSucessMSG(`已選取 ${this.selectedImages.length} 張圖片: ${imageNames}`);\n        this.selectedMaterial.CImageCode = this.selectedImages[0].name;\n      }\n      // 如果是從表格直接綁定圖片（有 CId），則直接保存到資料庫\n      if (this.selectedMaterial.CId) {\n        this.saveImageBinding();\n      }\n    }\n    this.clearAllSelection();\n    ref.close();\n  }\n  // 新增方法：保存圖片綁定\n  saveImageBinding() {\n    this._materialService.apiMaterialSaveMaterialAdminPost$Json({\n      body: {\n        CBuildCaseId: this.selectedBuildCaseId,\n        CImageCode: this.selectedMaterial.CImageCode,\n        CName: this.selectedMaterial.CName,\n        CPart: this.selectedMaterial.CPart,\n        CLocation: this.selectedMaterial.CLocation,\n        CSelectName: this.selectedMaterial.CSelectName,\n        CDescription: this.selectedMaterial.CDescription,\n        CMaterialId: this.selectedMaterial.CId,\n        CPrice: this.selectedMaterial.CPrice\n      }\n    }).pipe(tap(res => {\n      if (res.StatusCode === 0) {\n        this.message.showSucessMSG(`圖片綁定成功: ${this.selectedMaterial.CImageCode}`);\n      } else {\n        this.message.showErrorMSG(res.Message);\n      }\n    }), mergeMap(() => this.getMaterialList()), finalize(() => {\n      // 清空選取的建材\n      this.selectedMaterial = {};\n    })).subscribe();\n  }\n  onCloseImageBinder(ref) {\n    this.clearAllSelection();\n    this.imageSearchTerm = \"\";\n    ref.close();\n  } // 類別變更處理方法\n  categoryChanged(category) {\n    this.selectedCategory = category;\n    this.isCategorySelected = true;\n    // 當類別變更時重新載入圖片\n    if (this.selectedBuildCaseId) {\n      this.loadImages();\n    }\n  }\n  // 獲取類別標籤的方法\n  getCategoryLabel(category) {\n    const option = this.categoryOptions.find(opt => opt.value === category);\n    return option ? option.label : '未知類別';\n  }\n  static {\n    this.ɵfac = function BuildingMaterialComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || BuildingMaterialComponent)(i0.ɵɵdirectiveInject(i1.AllowHelper), i0.ɵɵdirectiveInject(i2.NbDialogService), i0.ɵɵdirectiveInject(i3.MessageService), i0.ɵɵdirectiveInject(i4.ValidationHelper), i0.ɵɵdirectiveInject(i5.BuildCaseService), i0.ɵɵdirectiveInject(i5.MaterialService), i0.ɵɵdirectiveInject(i6.UtilityService), i0.ɵɵdirectiveInject(i5.PictureService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: BuildingMaterialComponent,\n      selectors: [[\"ngx-building-material\"]],\n      standalone: true,\n      features: [i0.ɵɵInheritDefinitionFeature, i0.ɵɵStandaloneFeature],\n      decls: 59,\n      vars: 13,\n      consts: [[\"inputFile\", \"\"], [\"dialog\", \"\"], [\"imageBinder\", \"\"], [\"imagePreview\", \"\"], [\"dialogImage\", \"\"], [\"accent\", \"success\"], [1, \"font-bold\", \"text-[#818181]\"], [1, \"d-flex\", \"flex-wrap\"], [1, \"col-md-6\"], [1, \"form-group\", \"d-flex\", \"align-items-center\", \"w-full\"], [\"for\", \"buildingName\", 1, \"label\", \"mr-2\", \"w-[22%]\"], [\"placeholder\", \"\\u5EFA\\u6848\", 1, \"w-full\", 3, \"ngModelChange\", \"ngModel\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"type\", \"text\", \"nbInput\", \"\", \"placeholder\", \"\\u5EFA\\u6750\\u9078\\u9805\\u540D\\u7A31\", 1, \"w-full\", 3, \"ngModelChange\", \"ngModel\"], [1, \"col-md-12\"], [1, \"d-flex\", \"justify-content-end\", \"w-full\"], [\"status\", \"basic\", 1, \"flex\", 2, \"flex\", \"auto\", 3, \"checkedChange\", \"change\", \"checked\"], [\"class\", \"btn btn-success mr-2\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"btn btn-info mr-2 text-white ml-2\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"btn btn-info mx-1 ml-2 mr-2\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"btn btn-info mx-1\", 3, \"click\", 4, \"ngIf\"], [\"type\", \"file\", \"accept\", \".xls, .xlsx\", 1, \"hidden\", 3, \"change\"], [1, \"btn\", \"btn-success\", \"ml-2\", 3, \"click\"], [1, \"fas\", \"fa-file-download\"], [1, \"table-responsive\", \"mt-4\"], [1, \"table\", \"table-striped\", \"border\", 2, \"min-width\", \"1200px\", \"background-color\", \"#f3f3f3\"], [2, \"background-color\", \"#27ae60\", \"color\", \"white\"], [\"scope\", \"col\", 1, \"col-1\"], [\"scope\", \"col\", 1, \"col-2\"], [\"scope\", \"col\", 1, \"col-3\"], [\"scope\", \"col\", \"class\", \"col-1\", 4, \"ngIf\"], [\"scope\", \"col\", 1, \"col-1\", \"text-center\"], [4, \"ngIf\"], [1, \"d-flex\", \"justify-content-center\"], [3, \"CollectionSizeChange\", \"PageSizeChange\", \"PageChange\", \"CollectionSize\", \"PageSize\", \"Page\"], [3, \"value\"], [1, \"btn\", \"btn-success\", \"mr-2\", 3, \"click\"], [1, \"btn\", \"btn-info\", \"mr-2\", \"text-white\", \"ml-2\", 3, \"click\"], [1, \"fas\", \"fa-search\"], [1, \"btn\", \"btn-info\", \"mx-1\", \"ml-2\", \"mr-2\", 3, \"click\"], [1, \"fas\", \"fa-plus\"], [1, \"btn\", \"btn-info\", \"mx-1\", 3, \"click\"], [4, \"ngFor\", \"ngForOf\"], [1, \"text-center\", \"w-32\"], [\"class\", \"btn btn-outline-primary btn-sm m-1\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"btn btn-outline-info btn-sm m-1\", 3, \"title\", \"click\", 4, \"ngIf\"], [1, \"btn\", \"btn-outline-primary\", \"btn-sm\", \"m-1\", 3, \"click\"], [1, \"btn\", \"btn-outline-info\", \"btn-sm\", \"m-1\", 3, \"click\", \"title\"], [1, \"fas\", \"fa-images\"], [1, \"w-[700px]\"], [1, \"px-4\"], [1, \"text-base\"], [1, \"w-full\", \"mt-3\"], [1, \"flex\", \"items-center\", \"mt-3\"], [1, \"required-field\", \"w-[150px]\"], [\"type\", \"text\", \"nbInput\", \"\", \"maxlength\", \"30\", 1, \"w-full\", \"!max-w-full\", \"p-2\", \"rounded\", \"text-[13px]\", 3, \"ngModelChange\", \"ngModel\"], [1, \"w-[150px]\"], [\"nbInput\", \"\", 1, \"resize-none\", \"w-full\", \"!max-w-full\", \"p-2\", \"rounded\", \"text-[13px]\", 3, \"ngModelChange\", \"ngModel\", \"rows\"], [\"type\", \"number\", \"nbInput\", \"\", \"maxlength\", \"30\", 1, \"w-full\", \"!max-w-full\", \"p-2\", \"rounded\", \"text-[13px]\", 3, \"ngModelChange\", \"ngModel\"], [1, \"btn\", \"btn-danger\", \"btn-sm\", \"mr-4\", 3, \"click\"], [1, \"btn\", \"btn-success\", \"btn-sm\", 3, \"click\"], [1, \"w-[900px]\", \"h-[700px]\"], [1, \"px-4\", \"d-flex\", \"flex-column\", 2, \"height\", \"calc(700px - 120px)\", \"overflow\", \"hidden\", \"padding-bottom\", \"0\"], [1, \"bg-blue-50\", \"border\", \"border-blue-200\", \"rounded-lg\", \"p-3\", \"mb-4\", \"flex-shrink-0\"], [1, \"flex\", \"items-start\", \"gap-2\"], [1, \"fas\", \"fa-info-circle\", \"text-blue-500\", \"mt-1\"], [1, \"text-sm\", \"text-blue-700\"], [1, \"font-medium\", \"mb-1\"], [1, \"flex\", \"gap-3\", \"mb-4\", \"flex-shrink-0\"], [1, \"w-48\"], [1, \"text-sm\", \"font-medium\", \"text-gray-700\", \"mb-2\", \"block\"], [1, \"w-full\", 3, \"ngModelChange\", \"selectedChange\", \"ngModel\"], [1, \"flex-1\"], [\"type\", \"text\", \"placeholder\", \"\\u641C\\u5C0B\\u5716\\u7247\\u540D\\u7A31...\", 1, \"w-full\", \"search-input\", 3, \"ngModelChange\", \"input\", \"ngModel\"], [1, \"flex\", \"flex-col\", \"justify-end\"], [1, \"btn\", \"btn-info\", \"btn-image-action\", 3, \"click\"], [1, \"fas\", \"fa-refresh\"], [1, \"flex\", \"gap-2\", \"mb-3\", \"flex-shrink-0\"], [1, \"btn\", \"btn-outline-primary\", \"btn-sm\", 3, \"click\"], [1, \"fas\", \"fa-check-square\"], [1, \"btn\", \"btn-outline-secondary\", \"btn-sm\", 3, \"click\"], [1, \"ml-auto\", \"text-sm\", \"text-gray-600\"], [1, \"image-preview-container\", \"border\", \"rounded\", \"p-3\", \"flex-1\"], [1, \"grid\", \"grid-cols-4\", \"gap-3\"], [\"class\", \"image-grid-item border rounded p-2 cursor-pointer\", 3, \"selected\", \"click\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"text-center text-gray-500 py-20\", 4, \"ngIf\"], [1, \"d-flex\", \"justify-content-between\", \"align-items-center\"], [1, \"text-sm\", \"text-gray-600\"], [1, \"d-flex\", \"gap-2\"], [1, \"btn\", \"btn-danger\", \"btn-sm\", 3, \"click\"], [1, \"btn\", \"btn-success\", \"btn-sm\", 3, \"click\", \"disabled\"], [1, \"image-grid-item\", \"border\", \"rounded\", \"p-2\", \"cursor-pointer\", 3, \"click\"], [1, \"flex\", \"justify-between\", \"items-center\", \"mb-2\"], [1, \"image-checkbox\", \"w-5\", \"h-5\", \"border-2\", \"rounded\", \"flex\", \"items-center\", \"justify-center\"], [\"class\", \"fas fa-check text-white text-xs\", 4, \"ngIf\"], [1, \"btn\", \"btn-outline-info\", \"btn-xs\", \"btn-image-action\", 3, \"click\"], [1, \"fas\", \"fa-eye\"], [1, \"w-full\", \"h-32\", \"bg-gray-100\", \"rounded\", \"mb-2\", \"flex\", \"items-center\", \"justify-center\", \"overflow-hidden\"], [\"class\", \"image-thumbnail max-w-full max-h-full object-contain\", 3, \"src\", \"alt\", 4, \"ngIf\"], [\"class\", \"text-gray-400 text-center\", 4, \"ngIf\"], [1, \"text-xs\", \"text-gray-600\"], [1, \"font-medium\", \"truncate\", 3, \"title\"], [1, \"text-gray-400\"], [1, \"fas\", \"fa-check\", \"text-white\", \"text-xs\"], [1, \"image-thumbnail\", \"max-w-full\", \"max-h-full\", \"object-contain\", 3, \"src\", \"alt\"], [1, \"text-gray-400\", \"text-center\"], [1, \"fas\", \"fa-image\", \"text-2xl\", \"mb-1\"], [1, \"text-xs\"], [1, \"text-center\", \"text-gray-500\", \"py-20\"], [1, \"fas\", \"fa-images\", \"text-4xl\", \"mb-3\"], [1, \"w-[800px]\", \"h-[600px]\"], [1, \"btn\", \"btn-outline-primary\", \"btn-sm\", 3, \"click\", \"disabled\"], [1, \"fas\", \"fa-chevron-left\"], [1, \"fas\", \"fa-chevron-right\"], [1, \"p-0\", \"d-flex\", \"justify-content-center\", \"align-items-center\", 2, \"height\", \"500px\"], [\"class\", \"max-w-full max-h-full object-contain\", 3, \"src\", \"alt\", 4, \"ngIf\"], [1, \"btn\", \"btn-outline-info\", \"btn-sm\", 3, \"click\"], [1, \"max-w-full\", \"max-h-full\", \"object-contain\", 3, \"src\", \"alt\"], [1, \"fas\", \"fa-image\", \"text-4xl\", \"mb-3\"], [1, \"mr-md-5\", \"ml-md-5\", 2, \"height\", \"100%\", \"overflow\", \"auto\", \"width\", \"700px\"], [2, \"padding\", \"1rem 2rem\"], [1, \"w-full\", \"h-auto\"], [1, \"fit-size\", 3, \"src\"], [1, \"flex\", \"justify-center\", \"items-center\"], [1, \"btn\", \"btn-danger\", \"mr-2\", 3, \"click\"]],\n      template: function BuildingMaterialComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"nb-card\", 5)(1, \"nb-card-header\");\n          i0.ɵɵelement(2, \"ngx-breadcrumb\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(3, \"nb-card-body\")(4, \"h1\", 6);\n          i0.ɵɵtext(5, \" \\u53EF\\u8A2D\\u5B9A\\u55AE\\u7B46\\u6216\\u6279\\u6B21\\u532F\\u5165\\u8A2D\\u5B9A\\u5404\\u5340\\u57DF\\u53CA\\u65B9\\u6848\\u5C0D\\u61C9\\u4E4B\\u5EFA\\u6750\\u3002 \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(6, \"div\", 7)(7, \"div\", 8)(8, \"div\", 9)(9, \"label\", 10);\n          i0.ɵɵtext(10, \"\\u5EFA\\u6848\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(11, \"nb-select\", 11);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function BuildingMaterialComponent_Template_nb_select_ngModelChange_11_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.selectedBuildCaseId, $event) || (ctx.selectedBuildCaseId = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵtemplate(12, BuildingMaterialComponent_nb_option_12_Template, 2, 2, \"nb-option\", 12);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(13, \"div\", 8)(14, \"div\", 9)(15, \"label\", 10);\n          i0.ɵɵtext(16, \"\\u5EFA\\u6750\\u9078\\u9805\\u540D\\u7A31 \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(17, \"input\", 13);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function BuildingMaterialComponent_Template_input_ngModelChange_17_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.CSelectName, $event) || (ctx.CSelectName = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(18, \"div\", 14)(19, \"div\", 15)(20, \"nb-checkbox\", 16);\n          i0.ɵɵtwoWayListener(\"checkedChange\", function BuildingMaterialComponent_Template_nb_checkbox_checkedChange_20_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.filterMapping, $event) || (ctx.filterMapping = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵlistener(\"change\", function BuildingMaterialComponent_Template_nb_checkbox_change_20_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.changeFilter());\n          });\n          i0.ɵɵtext(21, \" \\u53EA\\u986F\\u793A\\u7F3A\\u5C11\\u5EFA\\u6750\\u5716\\u7247\\u6216\\u793A\\u610F\\u5716\\u7247\\u7684\\u5EFA\\u6750 \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(22, BuildingMaterialComponent_button_22_Template, 3, 0, \"button\", 17)(23, BuildingMaterialComponent_button_23_Template, 3, 0, \"button\", 18)(24, BuildingMaterialComponent_button_24_Template, 3, 0, \"button\", 19)(25, BuildingMaterialComponent_button_25_Template, 2, 0, \"button\", 20);\n          i0.ɵɵelementStart(26, \"input\", 21, 0);\n          i0.ɵɵlistener(\"change\", function BuildingMaterialComponent_Template_input_change_26_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.detectFileExcel($event));\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(28, \"button\", 22);\n          i0.ɵɵlistener(\"click\", function BuildingMaterialComponent_Template_button_click_28_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.exportExelMaterialTemplate());\n          });\n          i0.ɵɵtext(29, \"\\u4E0B\\u8F09\\u7BC4\\u4F8B\\u6A94\\u6848 \");\n          i0.ɵɵelement(30, \"i\", 23);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(31, \"div\", 24)(32, \"table\", 25)(33, \"thead\")(34, \"tr\", 26)(35, \"th\", 27);\n          i0.ɵɵtext(36, \"\\u9805\\u6B21\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(37, \"th\", 27);\n          i0.ɵɵtext(38, \"\\u5EFA\\u6750\\u4EE3\\u865F\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(39, \"th\", 28);\n          i0.ɵɵtext(40, \"\\u9078\\u9805\\u984C\\u76EE\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(41, \"th\", 27);\n          i0.ɵɵtext(42, \"\\u9078\\u9805\\u540D\\u7A31\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(43, \"th\", 29);\n          i0.ɵɵtext(44, \"\\u5EFA\\u6750\\u8AAA\\u660E\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(45, BuildingMaterialComponent_th_45_Template, 2, 0, \"th\", 30);\n          i0.ɵɵelementStart(46, \"th\", 31);\n          i0.ɵɵtext(47, \"\\u64CD\\u4F5C\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(48, BuildingMaterialComponent_tbody_48_Template, 2, 1, \"tbody\", 32);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(49, \"nb-card-footer\", 33)(50, \"ngx-pagination\", 34);\n          i0.ɵɵtwoWayListener(\"CollectionSizeChange\", function BuildingMaterialComponent_Template_ngx_pagination_CollectionSizeChange_50_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.totalRecords, $event) || (ctx.totalRecords = $event);\n            return i0.ɵɵresetView($event);\n          })(\"PageSizeChange\", function BuildingMaterialComponent_Template_ngx_pagination_PageSizeChange_50_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.pageSize, $event) || (ctx.pageSize = $event);\n            return i0.ɵɵresetView($event);\n          })(\"PageChange\", function BuildingMaterialComponent_Template_ngx_pagination_PageChange_50_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.pageIndex, $event) || (ctx.pageIndex = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵlistener(\"PageChange\", function BuildingMaterialComponent_Template_ngx_pagination_PageChange_50_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.pageChanged($event));\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(51, BuildingMaterialComponent_ng_template_51_Template, 36, 7, \"ng-template\", null, 1, i0.ɵɵtemplateRefExtractor)(53, BuildingMaterialComponent_ng_template_53_Template, 46, 10, \"ng-template\", null, 2, i0.ɵɵtemplateRefExtractor)(55, BuildingMaterialComponent_ng_template_55_Template, 22, 8, \"ng-template\", null, 3, i0.ɵɵtemplateRefExtractor)(57, BuildingMaterialComponent_ng_template_57_Template, 11, 1, \"ng-template\", null, 4, i0.ɵɵtemplateRefExtractor);\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(11);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.selectedBuildCaseId);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngForOf\", ctx.listBuildCases);\n          i0.ɵɵadvance(5);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.CSelectName);\n          i0.ɵɵadvance(3);\n          i0.ɵɵtwoWayProperty(\"checked\", ctx.filterMapping);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.isExcelExport);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isRead);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isCreate);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isExcelImport);\n          i0.ɵɵadvance(20);\n          i0.ɵɵproperty(\"ngIf\", ctx.ShowPrice == true);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", ctx.materialList != null && ctx.materialList.length > 0);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtwoWayProperty(\"CollectionSize\", ctx.totalRecords)(\"PageSize\", ctx.pageSize)(\"Page\", ctx.pageIndex);\n        }\n      },\n      dependencies: [CommonModule, i7.NgForOf, i7.NgIf, i7.DecimalPipe, SharedModule, i8.DefaultValueAccessor, i8.NumberValueAccessor, i8.NgControlStatus, i8.MaxLengthValidator, i8.NgModel, i2.NbCardComponent, i2.NbCardBodyComponent, i2.NbCardFooterComponent, i2.NbCardHeaderComponent, i2.NbCheckboxComponent, i2.NbInputDirective, i2.NbSelectComponent, i2.NbOptionComponent, i9.BreadcrumbComponent, i10.PaginationComponent],\n      styles: [\"@charset \\\"UTF-8\\\";\\n.image-table[_ngcontent-%COMP%] {\\n  width: 50px;\\n  height: 50px;\\n  cursor: pointer;\\n}\\n\\n.empty-image[_ngcontent-%COMP%] {\\n  color: red;\\n}\\n\\n.fit-size[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: auto;\\n  max-width: 100%;\\n  max-height: 500px;\\n  object-fit: contain;\\n}\\n\\n.image-grid-item[_ngcontent-%COMP%] {\\n  transition: all 0.3s ease;\\n}\\n.image-grid-item[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-2px);\\n  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);\\n}\\n.image-grid-item.selected[_ngcontent-%COMP%] {\\n  border-color: #3366ff;\\n  background-color: #f0f7ff;\\n  box-shadow: 0 0 0 2px rgba(51, 102, 255, 0.2);\\n}\\n\\n.image-checkbox[_ngcontent-%COMP%] {\\n  border-color: #ccc;\\n  background-color: white;\\n  transition: all 0.2s ease;\\n}\\n.image-checkbox.checked[_ngcontent-%COMP%] {\\n  background-color: #3366ff;\\n  border-color: #3366ff;\\n}\\n\\n.image-thumbnail[_ngcontent-%COMP%] {\\n  transition: transform 0.2s ease;\\n}\\n.image-thumbnail[_ngcontent-%COMP%]:hover {\\n  transform: scale(1.05);\\n}\\n\\n.image-preview-container[_ngcontent-%COMP%] {\\n  max-height: 480px;\\n  overflow-y: auto !important;\\n  overflow-x: hidden;\\n  \\n\\n  \\n\\n  -webkit-overflow-scrolling: touch;\\n  scrollbar-width: thin;\\n  scrollbar-color: #c1c1c1 #f1f1f1;\\n}\\n.image-preview-container[_ngcontent-%COMP%]::-webkit-scrollbar {\\n  width: 8px;\\n}\\n.image-preview-container[_ngcontent-%COMP%]::-webkit-scrollbar-track {\\n  background: #f1f1f1;\\n  border-radius: 4px;\\n}\\n.image-preview-container[_ngcontent-%COMP%]::-webkit-scrollbar-thumb {\\n  background: #c1c1c1;\\n  border-radius: 4px;\\n}\\n.image-preview-container[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover {\\n  background: #a8a8a8;\\n}\\n\\n.search-input[_ngcontent-%COMP%] {\\n  padding: 8px 12px;\\n  border: 1px solid #ddd;\\n  border-radius: 4px;\\n  font-size: 14px;\\n  transition: border-color 0.2s ease-in-out;\\n}\\n.search-input[_ngcontent-%COMP%]:focus {\\n  outline: none;\\n  border-color: #3366ff;\\n  box-shadow: 0 0 0 2px rgba(51, 102, 255, 0.1);\\n}\\n\\n.btn-image-action[_ngcontent-%COMP%] {\\n  padding: 4px 8px;\\n  font-size: 12px;\\n  border-radius: 4px;\\n  transition: all 0.2s ease-in-out;\\n}\\n.btn-image-action[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-1px);\\n}\\n.btn-image-action.btn-xs[_ngcontent-%COMP%] {\\n  padding: 2px 6px;\\n  font-size: 10px;\\n}\\n\\n\\n\\n.d-flex.flex-column[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n}\\n\\n.flex-1[_ngcontent-%COMP%] {\\n  flex: 1;\\n  min-height: 0;\\n}\\n\\n\\n\\nnb-card.w-\\\\__ph-0__[_ngcontent-%COMP%]   .nb-card-body[_ngcontent-%COMP%] {\\n  height: 580px !important;\\n  overflow: hidden !important;\\n  display: flex !important;\\n  flex-direction: column !important;\\n}\\n\\n\\n\\n.flex-shrink-0[_ngcontent-%COMP%] {\\n  flex-shrink: 0;\\n}\\n\\n\\n\\n.image-preview-container.flex-1[_ngcontent-%COMP%] {\\n  flex: 1 1 auto;\\n  min-height: 0;\\n  height: auto;\\n  overflow-y: scroll !important;\\n  overflow-x: hidden !important;\\n}\\n\\n\\n\\n.grid.grid-cols-4[_ngcontent-%COMP%] {\\n  min-height: min-content;\\n}\\n\\n\\n\\n  nb-card-body .image-preview-container {\\n  max-height: none !important;\\n  height: 100% !important;\\n}\\n/*# sourceMappingURL=data:application/json;base64,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 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "finalize", "mergeMap", "tap", "XLSX", "SharedModule", "BaseComponent", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵproperty", "buildCase_r2", "cID", "ɵɵadvance", "ɵɵtextInterpolate1", "CBuildCaseName", "ɵɵlistener", "BuildingMaterialComponent_button_22_Template_button_click_0_listener", "ɵɵrestoreView", "_r3", "ctx_r3", "ɵɵnextContext", "ɵɵresetView", "exportExelMaterialList", "ɵɵelement", "BuildingMaterialComponent_button_23_Template_button_click_0_listener", "_r5", "search", "BuildingMaterialComponent_button_24_Template_button_click_0_listener", "_r6", "dialog_r7", "ɵɵreference", "addNew", "BuildingMaterialComponent_button_25_Template_button_click_0_listener", "_r8", "inputFile_r9", "click", "ɵɵtextInterpolate", "item_r10", "CPrice", "BuildingMaterialComponent_tbody_48_tr_1_button_13_Template_button_click_0_listener", "_r11", "$implicit", "onSelectedMaterial", "BuildingMaterialComponent_tbody_48_tr_1_button_14_Template_button_click_0_listener", "_r12", "imageBinder_r13", "bindImageForMaterial", "CName", "ɵɵtemplate", "BuildingMaterialComponent_tbody_48_tr_1_td_11_Template", "BuildingMaterialComponent_tbody_48_tr_1_button_13_Template", "BuildingMaterialComponent_tbody_48_tr_1_button_14_Template", "CId", "CImageCode", "ɵɵtextInterpolate3", "<PERSON>art", "CLocation", "ɵɵstyleMap", "CIsMapping", "CSelectName", "CDescription", "CShowPrice", "isRead", "BuildingMaterialComponent_tbody_48_tr_1_Template", "materialList", "ɵɵtwoWayListener", "BuildingMaterialComponent_ng_template_51_Template_input_ngModelChange_10_listener", "$event", "_r14", "ɵɵtwoWayBindingSet", "selectedMaterial", "BuildingMaterialComponent_ng_template_51_Template_input_ngModelChange_14_listener", "BuildingMaterialComponent_ng_template_51_Template_input_ngModelChange_18_listener", "BuildingMaterialComponent_ng_template_51_Template_input_ngModelChange_22_listener", "BuildingMaterialComponent_ng_template_51_Template_textarea_ngModelChange_26_listener", "BuildingMaterialComponent_ng_template_51_Template_input_ngModelChange_30_listener", "BuildingMaterialComponent_ng_template_51_Template_button_click_32_listener", "ref_r15", "dialogRef", "onClose", "BuildingMaterialComponent_ng_template_51_Template_button_click_34_listener", "onSubmit", "ɵɵtwoWayProperty", "option_r17", "value", "label", "image_r19", "thumbnailUrl", "ɵɵsanitizeUrl", "name", "BuildingMaterialComponent_ng_template_53_div_36_Template_div_click_0_listener", "_r18", "toggleImageSelection", "BuildingMaterialComponent_ng_template_53_div_36_i_3_Template", "BuildingMaterialComponent_ng_template_53_div_36_Template_button_click_4_listener", "imagePreview_r20", "previewImage", "BuildingMaterialComponent_ng_template_53_div_36_img_7_Template", "BuildingMaterialComponent_ng_template_53_div_36_div_8_Template", "ɵɵclassProp", "isImageSelected", "ɵɵpipeBind1", "size", "BuildingMaterialComponent_ng_template_53_Template_nb_select_ngModelChange_16_listener", "_r16", "selectedCate<PERSON><PERSON>", "BuildingMaterialComponent_ng_template_53_Template_nb_select_selectedChange_16_listener", "categoryChanged", "BuildingMaterialComponent_ng_template_53_nb_option_17_Template", "BuildingMaterialComponent_ng_template_53_Template_input_ngModelChange_21_listener", "imageSearchTerm", "BuildingMaterialComponent_ng_template_53_Template_input_input_21_listener", "filterImages", "BuildingMaterialComponent_ng_template_53_Template_button_click_23_listener", "loadImages", "BuildingMaterialComponent_ng_template_53_Template_button_click_27_listener", "selectAllImages", "BuildingMaterialComponent_ng_template_53_Template_button_click_30_listener", "clearAllSelection", "BuildingMaterialComponent_ng_template_53_div_36_Template", "BuildingMaterialComponent_ng_template_53_div_37_Template", "BuildingMaterialComponent_ng_template_53_Template_button_click_42_listener", "ref_r21", "onCloseImageBinder", "BuildingMaterialComponent_ng_template_53_Template_button_click_44_listener", "onConfirmImageSelection", "categoryOptions", "selectedImages", "length", "filteredImages", "availableImages", "previewingImage", "fullUrl", "BuildingMaterialComponent_ng_template_55_Template_button_click_5_listener", "_r22", "previousImage", "BuildingMaterialComponent_ng_template_55_Template_button_click_8_listener", "nextImage", "BuildingMaterialComponent_ng_template_55_img_12_Template", "BuildingMaterialComponent_ng_template_55_div_13_Template", "BuildingMaterialComponent_ng_template_55_Template_button_click_18_listener", "toggleImageSelectionInPreview", "BuildingMaterialComponent_ng_template_55_Template_button_click_20_listener", "ref_r23", "close", "currentPreviewIndex", "ɵɵtextInterpolate2", "BuildingMaterialComponent_ng_template_57_Template_button_click_9_listener", "ref_r25", "_r24", "currentImageShowing", "PictureCategory", "BuildingMaterialComponent", "constructor", "_allow", "dialogService", "message", "valid", "_buildCaseService", "_materialService", "_utilityService", "_pictureService", "isNew", "listBuildCases", "materialOptions", "materialOptionsId", "CMaterialCode", "ShowPrice", "filterMapping", "BUILDING_MATERIAL", "SCHEMATIC", "isCategorySelected", "ngOnInit", "getListBuildCase", "apiBuildCaseGetAllBuildCaseForSelectPost$Json", "body", "CIsPagi", "CStatus", "pipe", "res", "StatusCode", "Entries", "selectedBuildCaseId", "getMaterialList", "subscribe", "pageIndex", "apiMaterialGetMaterialListPost$Json", "CBuildCaseId", "CPlanUse", "PageSize", "pageSize", "PageIndex", "totalRecords", "TotalItems", "pageChanged", "apiMaterialExportExcelMaterialListPost$Json", "FileByte", "downloadExcelFile", "exportExelMaterialTemplate", "apiMaterialExportExcelMaterialTemplatePost$Json", "ref", "open", "data", "closeOnBackdropClick", "validation", "clear", "required", "isStringMaxLength", "errorMessages", "showErrorMSGs", "apiMaterialSaveMaterialAdminPost$Json", "CMaterialId", "showSucessMSG", "showErrorMSG", "Message", "detectFileExcel", "event", "target", "reader", "FileReader", "readAsBinaryString", "files", "onload", "e", "binarystr", "result", "wb", "read", "type", "wsname", "SheetNames", "ws", "Sheets", "isValidFile", "utils", "sheet_to_json", "for<PERSON>ach", "x", "apiMaterialImportExcelMaterialListPost$Json", "CFile", "showImage", "imageUrl", "dialog", "changeFilter", "openImageBinder", "apiPictureGetPicturelListPost$Json", "cPictureType", "map", "picture", "id", "toString", "CPictureCode", "CBase64", "lastModified", "CUpdateDT", "Date", "trim", "searchTerm", "toLowerCase", "filter", "image", "includes", "index", "findIndex", "selected", "splice", "push", "some", "imagePreviewRef", "stopPropagation", "img", "imageNames", "join", "saveImageBinding", "category", "getCategoryLabel", "option", "find", "opt", "ɵɵdirectiveInject", "i1", "AllowHelper", "i2", "NbDialogService", "i3", "MessageService", "i4", "ValidationHelper", "i5", "BuildCaseService", "MaterialService", "i6", "UtilityService", "PictureService", "selectors", "standalone", "features", "ɵɵInheritDefinitionFeature", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "BuildingMaterialComponent_Template", "rf", "ctx", "BuildingMaterialComponent_Template_nb_select_ngModelChange_11_listener", "_r1", "BuildingMaterialComponent_nb_option_12_Template", "BuildingMaterialComponent_Template_input_ngModelChange_17_listener", "BuildingMaterialComponent_Template_nb_checkbox_checkedChange_20_listener", "BuildingMaterialComponent_Template_nb_checkbox_change_20_listener", "BuildingMaterialComponent_button_22_Template", "BuildingMaterialComponent_button_23_Template", "BuildingMaterialComponent_button_24_Template", "BuildingMaterialComponent_button_25_Template", "BuildingMaterialComponent_Template_input_change_26_listener", "BuildingMaterialComponent_Template_button_click_28_listener", "BuildingMaterialComponent_th_45_Template", "BuildingMaterialComponent_tbody_48_Template", "BuildingMaterialComponent_Template_ngx_pagination_CollectionSizeChange_50_listener", "BuildingMaterialComponent_Template_ngx_pagination_PageSizeChange_50_listener", "BuildingMaterialComponent_Template_ngx_pagination_PageChange_50_listener", "BuildingMaterialComponent_ng_template_51_Template", "ɵɵtemplateRefExtractor", "BuildingMaterialComponent_ng_template_53_Template", "BuildingMaterialComponent_ng_template_55_Template", "BuildingMaterialComponent_ng_template_57_Template", "isExcelExport", "isCreate", "isExcelImport", "i7", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "DecimalPipe", "i8", "DefaultValueAccessor", "NumberValueAccessor", "NgControlStatus", "MaxLengthValidator", "NgModel", "NbCardComponent", "NbCardBodyComponent", "NbCardFooterComponent", "NbCardHeaderComponent", "NbCheckboxComponent", "NbInputDirective", "NbSelectComponent", "NbOptionComponent", "i9", "BreadcrumbComponent", "i10", "PaginationComponent", "styles"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\selection-management\\building-material\\building-material.component.ts", "C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\selection-management\\building-material\\building-material.component.html"], "sourcesContent": ["import { Component, OnInit, TemplateRef, } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { NbDialogService } from '@nebular/theme';\r\nimport { MessageService } from 'src/app/shared/services/message.service';\r\nimport { ValidationHelper } from 'src/app/shared/helper/validationHelper';\r\nimport { BuildCaseService, MaterialService, PictureService } from 'src/services/api/services';\r\nimport { BuildCaseGetListReponse, SaveMaterialArgs, GetMaterialListResponse } from 'src/services/api/models';\r\nimport { finalize, mergeMap, tap } from 'rxjs';\r\nimport { AllowHelper } from 'src/app/shared/helper/allowHelper';\r\nimport { UtilityService } from 'src/app/shared/services/utility.service';\r\nimport * as XLSX from 'xlsx';\r\nimport { SharedModule } from '../../components/shared.module';\r\nimport { BaseComponent } from '../../components/base/baseComponent';\r\n\r\n// 圖片類別枚舉\r\nenum PictureCategory {\r\n  NONE = 0,           // 未選擇\r\n  BUILDING_MATERIAL = 1,  // 建材圖片\r\n  SCHEMATIC = 2       // 示意圖片\r\n}\r\n\r\n// 圖片項目介面\r\ninterface ImageItem {\r\n  id: string;\r\n  name: string;\r\n  size: number;\r\n  thumbnailUrl?: string;\r\n  fullUrl?: string;\r\n  lastModified?: Date;\r\n}\r\n\r\n@Component({\r\n  selector: 'ngx-building-material',\r\n  templateUrl: './building-material.component.html',\r\n  styleUrls: ['./building-material.component.scss'],\r\n  standalone: true,\r\n  imports: [CommonModule, SharedModule],\r\n})\r\n\r\nexport class BuildingMaterialComponent extends BaseComponent implements OnInit {\r\n  isNew = true\r\n\r\n  materialList: GetMaterialListResponse[]\r\n  selectedMaterial: GetMaterialListResponse\r\n\r\n  listBuildCases: BuildCaseGetListReponse[] = []\r\n  selectedBuildCaseId: number\r\n\r\n  materialOptions = [\r\n    {\r\n      value: null,\r\n      label: '全部',\r\n    },\r\n    {\r\n      value: false,\r\n      label: '方案',\r\n    },\r\n    {\r\n      value: true,\r\n      label: '選樣',\r\n    }];\r\n  materialOptionsId = null;  CSelectName: string = \"\"\r\n  // 移除圖片檔名相關欄位\r\n  // CImageCode: string = \"\"\r\n  // CInfoImageCode: string = \"\"\r\n  // 預留建材代號欄位，等後端API支援後再啟用\r\n  CMaterialCode: string = \"\"\r\n  ShowPrice: boolean = false\r\n  currentImageShowing: string = \"\"\r\n  filterMapping: boolean = false\r\n  CIsMapping: boolean = true\r\n\r\n  // 圖片綁定相關屬性\r\n  availableImages: ImageItem[] = []\r\n  filteredImages: ImageItem[] = []\r\n  selectedImages: ImageItem[] = []\r\n  imageSearchTerm: string = \"\"\r\n  previewingImage: ImageItem | null = null\r\n  currentPreviewIndex: number = 0\r\n\r\n  // 類別選項\r\n  categoryOptions = [\r\n    { value: PictureCategory.BUILDING_MATERIAL, label: '建材圖片' },\r\n    { value: PictureCategory.SCHEMATIC, label: '示意圖片' }\r\n  ]\r\n  selectedCategory: PictureCategory = PictureCategory.BUILDING_MATERIAL\r\n  isCategorySelected: boolean = true // 預設選擇建材圖片\r\n\r\n  // 讓模板可以使用 enum\r\n  PictureCategory = PictureCategory;\r\n\r\n  constructor(\r\n    private _allow: AllowHelper,\r\n    private dialogService: NbDialogService,\r\n    private message: MessageService,\r\n    private valid: ValidationHelper,\r\n    private _buildCaseService: BuildCaseService,\r\n    private _materialService: MaterialService,\r\n    private _utilityService: UtilityService,\r\n    private _pictureService: PictureService\r\n  ) {\r\n    super(_allow)\r\n  }\r\n\r\n  override ngOnInit(): void {\r\n    this.getListBuildCase()\r\n  }\r\n\r\n  getListBuildCase() {\r\n    this._buildCaseService.apiBuildCaseGetAllBuildCaseForSelectPost$Json({\r\n      body: {\r\n        CIsPagi: false,\r\n        CStatus: 1,\r\n      }\r\n    })\r\n      .pipe(\r\n        tap(res => {\r\n          if (res.StatusCode == 0) {\r\n            this.listBuildCases = res.Entries?.length ? res.Entries : []\r\n            this.selectedBuildCaseId = this.listBuildCases[0].cID!\r\n          }\r\n        }),\r\n        mergeMap(() => this.getMaterialList())\r\n      ).subscribe()\r\n  }  getMaterialList(pageIndex: number = 1) {\r\n    return this._materialService.apiMaterialGetMaterialListPost$Json({\r\n      body: {\r\n        CBuildCaseId: this.selectedBuildCaseId,\r\n        CPlanUse: this.materialOptionsId,\r\n        CSelectName: this.CSelectName,\r\n        // 移除圖片檔名相關查詢條件\r\n        // CImageCode: this.CImageCode,\r\n        // CInfoImageCode: this.CInfoImageCode,\r\n        // 預留建材代號查詢條件，等後端API支援後再啟用\r\n        // CMaterialCode: this.CMaterialCode,\r\n        PageSize: this.pageSize,\r\n        PageIndex: pageIndex,\r\n        CIsMapping: this.CIsMapping\r\n      }\r\n    }).pipe(\r\n      tap(res => {\r\n        if (res.StatusCode == 0) {\r\n          this.materialList = res.Entries! ?? []\r\n          this.totalRecords = res.TotalItems!\r\n\r\n          if (this.materialList.length > 0) {\r\n            this.ShowPrice = this.materialList[0].CShowPrice!;\r\n          }\r\n        }\r\n      })\r\n    )\r\n  }\r\n\r\n  search() {\r\n    this.getMaterialList().subscribe()\r\n  }\r\n\r\n  pageChanged(pageIndex: number) {\r\n    this.getMaterialList(pageIndex).subscribe()\r\n  }\r\n\r\n  exportExelMaterialList() {\r\n    this._materialService.apiMaterialExportExcelMaterialListPost$Json({\r\n      body: this.selectedBuildCaseId\r\n    }).subscribe(res => {\r\n      if (res.StatusCode == 0) {\r\n        if (res.Entries!.FileByte) {\r\n          this._utilityService.downloadExcelFile(res.Entries!.FileByte, '建材管理')\r\n        }\r\n      }\r\n    })\r\n  }\r\n\r\n  exportExelMaterialTemplate() {\r\n    this._materialService.apiMaterialExportExcelMaterialTemplatePost$Json({\r\n      body: this.selectedBuildCaseId\r\n    }).subscribe(res => {\r\n      if (res.StatusCode == 0) {\r\n        if (res.Entries!.FileByte) {\r\n          this._utilityService.downloadExcelFile(res.Entries!.FileByte, '建材管理')\r\n        }\r\n      }\r\n    })\r\n  }\r\n\r\n  addNew(ref: any) {\r\n    this.isNew = true\r\n    this.selectedMaterial = {}\r\n    this.dialogService.open(ref)\r\n  }\r\n  onSelectedMaterial(data: GetMaterialListResponse, ref: any) {\r\n    this.isNew = false\r\n    this.selectedMaterial = { ...data }\r\n    this.dialogService.open(ref)\r\n  }\r\n\r\n  bindImageForMaterial(data: GetMaterialListResponse, ref: TemplateRef<any>) {\r\n    this.selectedMaterial = { ...data }\r\n    this.loadImages()\r\n    this.dialogService.open(ref, { closeOnBackdropClick: false })\r\n  }  validation() {\r\n    this.valid.clear();\r\n    this.valid.required('[名稱]', this.selectedMaterial.CName)\r\n    this.valid.required('[項目]', this.selectedMaterial.CPart)\r\n    this.valid.required('[位置]', this.selectedMaterial.CLocation)\r\n    this.valid.required('[建材選項名稱]', this.selectedMaterial.CSelectName)\r\n    // 移除圖片檔名驗證\r\n    // this.valid.required('[建材圖片編號]', this.selectedMaterial.CImageCode)\r\n    // 預留建材代號驗證，等後端API支援後再啟用\r\n    // this.valid.required('[建材代號]', this.selectedMaterial.CMaterialCode)\r\n    this.valid.isStringMaxLength('[名稱]', this.selectedMaterial.CName, 30)\r\n    this.valid.isStringMaxLength('[項目]', this.selectedMaterial.CPart, 30)\r\n    this.valid.isStringMaxLength('[位置]', this.selectedMaterial.CLocation, 30)\r\n    this.valid.isStringMaxLength('[建材選項名稱]', this.selectedMaterial.CSelectName, 30)\r\n    // 移除圖片檔名長度驗證\r\n    // this.valid.isStringMaxLength('[建材圖片編號]', this.selectedMaterial.CImageCode, 30)\r\n    // 預留建材代號長度驗證，等後端API支援後再啟用\r\n    // this.valid.isStringMaxLength('[建材代號]', this.selectedMaterial.CMaterialCode, 30)\r\n  }\r\n\r\n  onSubmit(ref: any) {\r\n    this.validation()\r\n    if (this.valid.errorMessages.length > 0) {\r\n      this.message.showErrorMSGs(this.valid.errorMessages);\r\n      return\r\n    }    this._materialService.apiMaterialSaveMaterialAdminPost$Json({\r\n      body: {\r\n        CBuildCaseId: this.selectedBuildCaseId,\r\n        // 暫時保留 CImageCode 給圖片綁定功能使用\r\n        CImageCode: this.selectedMaterial.CImageCode,\r\n        // 預留建材代號，等後端API支援後再啟用\r\n        // CMaterialCode: this.selectedMaterial.CMaterialCode,\r\n        CName: this.selectedMaterial.CName,\r\n        CPart: this.selectedMaterial.CPart,\r\n        CLocation: this.selectedMaterial.CLocation,\r\n        CSelectName: this.selectedMaterial.CSelectName,\r\n        CDescription: this.selectedMaterial.CDescription,\r\n        CMaterialId: this.isNew ? null : this.selectedMaterial.CId!,\r\n        CPrice: this.selectedMaterial.CPrice\r\n      }\r\n    })\r\n      .pipe(\r\n        tap(res => {\r\n          if (res.StatusCode === 0) {\r\n            this.message.showSucessMSG(\"執行成功\");\r\n          } else {\r\n            this.message.showErrorMSG(res.Message!);\r\n          }\r\n        }),\r\n        mergeMap(() => this.getMaterialList()),\r\n        finalize(() => ref.close())\r\n      ).subscribe()\r\n  }\r\n\r\n  onClose(ref: any) {\r\n    ref.close();\r\n  }\r\n\r\n  detectFileExcel(event: any) {\r\n    const target: DataTransfer = <DataTransfer>(event.target);\r\n    const reader: FileReader = new FileReader();\r\n    reader.readAsBinaryString(target.files[0]);\r\n    reader.onload = (e: any) => {\r\n      const binarystr: string = e.target.result;\r\n      const wb: XLSX.WorkBook = XLSX.read(binarystr, { type: 'binary' });\r\n\r\n      const wsname: string = wb.SheetNames[0];\r\n      const ws: XLSX.WorkSheet = wb.Sheets[wsname];\r\n\r\n      let isValidFile: boolean = true;\r\n      const data = XLSX.utils.sheet_to_json(ws);\r\n      if (data && data.length > 0) {\r\n        data.forEach((x: any) => {\r\n          if (!(x['名稱'] && x['項目'] && x['位置'] && x['建材選項名稱'] &&\r\n            (x['建材圖片檔名'] || x['建材說明'] || x['示意圖片檔名']))) {\r\n            isValidFile = false;\r\n          }\r\n        })\r\n\r\n        if (!isValidFile) {\r\n          this.message.showErrorMSG(\"导入文件时出现错误\")\r\n        } else {\r\n          this._materialService.apiMaterialImportExcelMaterialListPost$Json({\r\n            body: {\r\n              CBuildCaseId: this.selectedBuildCaseId,\r\n              CFile: target.files[0]\r\n            }\r\n          }).pipe(\r\n            tap(res => {\r\n              if (res.StatusCode == 0) {\r\n                this.message.showSucessMSG(\"執行成功\")\r\n              } else {\r\n                this.message.showErrorMSG(res.Message!)\r\n              }\r\n            }),\r\n            mergeMap(() => this.getMaterialList(1))\r\n          ).subscribe();\r\n        }\r\n      } else {\r\n        this.message.showErrorMSG(\"匯入的檔案內容為空，請檢查檔案並重新上傳。\")\r\n      }\r\n      event.target.value = null;\r\n    };\r\n  }\r\n\r\n  showImage(imageUrl: string, dialog: TemplateRef<any>) {\r\n    this.currentImageShowing = imageUrl;\r\n    this.dialogService.open(dialog);\r\n  }\r\n  changeFilter() {\r\n    if (this.filterMapping) {\r\n      this.CIsMapping = false;\r\n      this.getMaterialList().subscribe();\r\n    }\r\n    else {\r\n      this.CIsMapping = true;\r\n      this.getMaterialList().subscribe();\r\n    }\r\n  }\r\n\r\n  // 圖片綁定功能方法\r\n  openImageBinder(ref: TemplateRef<any>) {\r\n    this.loadImages();\r\n    this.dialogService.open(ref, { closeOnBackdropClick: false });\r\n  } loadImages() {\r\n    // 使用 PictureService API 載入圖片列表\r\n    if (this.isCategorySelected && this.selectedBuildCaseId) {\r\n      this._pictureService.apiPictureGetPicturelListPost$Json({\r\n        body: {\r\n          CBuildCaseId: this.selectedBuildCaseId,\r\n          cPictureType: this.selectedCategory,\r\n          PageIndex: 1,\r\n          PageSize: 1000 // 載入所有圖片\r\n        }\r\n      }).subscribe(res => {\r\n        if (res.StatusCode === 0) {\r\n          // 將 API 回應轉換為 ImageItem 格式\r\n          this.availableImages = res.Entries?.map(picture => ({\r\n            id: picture.CId?.toString() || '',\r\n            name: picture.CPictureCode || picture.CName || '',\r\n            size: 0, // API 中沒有檔案大小資訊\r\n            thumbnailUrl: picture.CBase64 ? `data:image/jpeg;base64,${picture.CBase64}` : '',\r\n            fullUrl: picture.CBase64 ? `data:image/jpeg;base64,${picture.CBase64}` : '',\r\n            lastModified: picture.CUpdateDT ? new Date(picture.CUpdateDT) : new Date()\r\n          })) || [];\r\n          this.filteredImages = [...this.availableImages];\r\n        } else {\r\n          this.message.showErrorMSG(res.Message || '載入圖片失敗');\r\n          this.availableImages = [];\r\n          this.filteredImages = [];\r\n        }\r\n      });\r\n    } else {\r\n      // 如果沒有選擇類別或建案，清空圖片列表\r\n      this.availableImages = [];\r\n      this.filteredImages = [];\r\n    }\r\n  }\r\n\r\n  filterImages() {\r\n    if (!this.imageSearchTerm.trim()) {\r\n      this.filteredImages = [...this.availableImages];\r\n    } else {\r\n      const searchTerm = this.imageSearchTerm.toLowerCase();\r\n      this.filteredImages = this.availableImages.filter(image =>\r\n        image.name.toLowerCase().includes(searchTerm)\r\n      );\r\n    }\r\n  }\r\n\r\n  toggleImageSelection(image: ImageItem) {\r\n    const index = this.selectedImages.findIndex(selected => selected.id === image.id);\r\n    if (index > -1) {\r\n      this.selectedImages.splice(index, 1);\r\n    } else {\r\n      this.selectedImages.push(image);\r\n    }\r\n  }\r\n\r\n  isImageSelected(image: ImageItem): boolean {\r\n    return this.selectedImages.some(selected => selected.id === image.id);\r\n  }\r\n\r\n  selectAllImages() {\r\n    this.selectedImages = [...this.filteredImages];\r\n  }\r\n\r\n  clearAllSelection() {\r\n    this.selectedImages = [];\r\n  } previewImage(image: ImageItem, imagePreviewRef: TemplateRef<any>, event: Event) {\r\n    event.stopPropagation();\r\n    this.previewingImage = image;\r\n    this.currentPreviewIndex = this.filteredImages.findIndex(img => img.id === image.id);\r\n    this.dialogService.open(imagePreviewRef);\r\n  }\r\n\r\n  previousImage() {\r\n    if (this.currentPreviewIndex > 0) {\r\n      this.currentPreviewIndex--;\r\n      this.previewingImage = this.filteredImages[this.currentPreviewIndex];\r\n    }\r\n  }\r\n\r\n  nextImage() {\r\n    if (this.currentPreviewIndex < this.filteredImages.length - 1) {\r\n      this.currentPreviewIndex++;\r\n      this.previewingImage = this.filteredImages[this.currentPreviewIndex];\r\n    }\r\n  }\r\n\r\n  toggleImageSelectionInPreview() {\r\n    if (this.previewingImage) {\r\n      this.toggleImageSelection(this.previewingImage);\r\n    }\r\n  } onConfirmImageSelection(ref: any) {\r\n    if (this.selectedImages.length > 0) {\r\n      // 如果只選取一張圖片，直接設定到建材圖片檔名\r\n      if (this.selectedImages.length === 1) {\r\n        this.selectedMaterial.CImageCode = this.selectedImages[0].name;\r\n      } else {\r\n        // 多張圖片的話，可以設定第一張，或者讓使用者選擇主要圖片\r\n        const imageNames = this.selectedImages.map(img => img.name).join(', ');\r\n        this.message.showSucessMSG(`已選取 ${this.selectedImages.length} 張圖片: ${imageNames}`);\r\n        this.selectedMaterial.CImageCode = this.selectedImages[0].name;\r\n      }\r\n\r\n      // 如果是從表格直接綁定圖片（有 CId），則直接保存到資料庫\r\n      if (this.selectedMaterial.CId) {\r\n        this.saveImageBinding();\r\n      }\r\n    }\r\n\r\n    this.clearAllSelection();\r\n    ref.close();\r\n  }\r\n\r\n  // 新增方法：保存圖片綁定\r\n  saveImageBinding() {\r\n    this._materialService.apiMaterialSaveMaterialAdminPost$Json({\r\n      body: {\r\n        CBuildCaseId: this.selectedBuildCaseId,\r\n        CImageCode: this.selectedMaterial.CImageCode,\r\n        CName: this.selectedMaterial.CName,\r\n        CPart: this.selectedMaterial.CPart,\r\n        CLocation: this.selectedMaterial.CLocation,\r\n        CSelectName: this.selectedMaterial.CSelectName,\r\n        CDescription: this.selectedMaterial.CDescription,\r\n        CMaterialId: this.selectedMaterial.CId!,\r\n        CPrice: this.selectedMaterial.CPrice\r\n      }\r\n    })\r\n      .pipe(\r\n        tap(res => {\r\n          if (res.StatusCode === 0) {\r\n            this.message.showSucessMSG(`圖片綁定成功: ${this.selectedMaterial.CImageCode}`);\r\n          } else {\r\n            this.message.showErrorMSG(res.Message!);\r\n          }\r\n        }),\r\n        mergeMap(() => this.getMaterialList()),\r\n        finalize(() => {\r\n          // 清空選取的建材\r\n          this.selectedMaterial = {};\r\n        })\r\n      ).subscribe()\r\n  }\r\n\r\n  onCloseImageBinder(ref: any) {\r\n    this.clearAllSelection();\r\n    this.imageSearchTerm = \"\";\r\n    ref.close();\r\n  }  // 類別變更處理方法\r\n  categoryChanged(category: PictureCategory) {\r\n    this.selectedCategory = category;\r\n    this.isCategorySelected = true;\r\n    // 當類別變更時重新載入圖片\r\n    if (this.selectedBuildCaseId) {\r\n      this.loadImages();\r\n    }\r\n  }\r\n\r\n  // 獲取類別標籤的方法\r\n  getCategoryLabel(category: number): string {\r\n    const option = this.categoryOptions.find(opt => opt.value === category);\r\n    return option ? option.label : '未知類別';\r\n  }\r\n}\r\n", "<nb-card accent=\"success\">\r\n  <nb-card-header>\r\n    <ngx-breadcrumb></ngx-breadcrumb>\r\n  </nb-card-header>\r\n  <nb-card-body>\r\n    <h1 class=\"font-bold text-[#818181]\"> 可設定單筆或批次匯入設定各區域及方案對應之建材。\r\n    </h1>\r\n    <div class=\"d-flex flex-wrap\">\r\n      <div class=\"col-md-6\">\r\n        <div class=\"form-group d-flex align-items-center w-full\">\r\n          <label for=\"buildingName\" class=\"label mr-2 w-[22%]\">建案</label>\r\n          <nb-select placeholder=\"建案\" [(ngModel)]=\"selectedBuildCaseId\" class=\"w-full\">\r\n            <nb-option *ngFor=\"let buildCase of listBuildCases\" [value]=\"buildCase.cID\">\r\n              {{ buildCase.CBuildCaseName }}\r\n            </nb-option>\r\n          </nb-select>\r\n        </div>\r\n      </div>\r\n      <!-- <div class=\"col-md-6\">\r\n        <div class=\"form-group d-flex align-items-center w-full\">\r\n          <label for=\"buildingName\" class=\"label mr-2  w-[22%]\">建材類別</label>\r\n          <nb-select placeholder=\"建材類別\" [(ngModel)]=\"materialOptionsId\" class=\"w-full\">\r\n            <nb-option *ngFor=\"let option of materialOptions\" [value]=\"option.value\">\r\n              {{ option.label }}\r\n            </nb-option>\r\n          </nb-select>\r\n        </div>\r\n      </div> -->      <div class=\"col-md-6\">\r\n        <div class=\"form-group d-flex align-items-center w-full\">\r\n          <label for=\"buildingName\" class=\"label mr-2 w-[22%]\">建材選項名稱 </label>\r\n          <input type=\"text\" nbInput placeholder=\"建材選項名稱\" [(ngModel)]=\"CSelectName\" class=\"w-full\">\r\n        </div>\r\n      </div> \r\n      <!-- 預留建材代號搜尋欄位，等後端API支援後再啟用 -->\r\n      <!--\r\n      <div class=\"col-md-6\">\r\n        <div class=\"form-group d-flex align-items-center w-full\">\r\n          <label for=\"buildingName\" class=\"label mr-2 w-[22%]\">建材代號</label>\r\n          <input type=\"text\" nbInput placeholder=\"建材代號\" [(ngModel)]=\"CMaterialCode\" class=\"w-full\">\r\n        </div>\r\n      </div>\r\n      -->\r\n      <div class=\"col-md-12\">\r\n        <div class=\"d-flex justify-content-end w-full\">\r\n          <nb-checkbox status=\"basic\" class=\"flex\" style=\"flex:auto\" [(checked)]=\"filterMapping\"\r\n            (change)=\"changeFilter()\">\r\n            只顯示缺少建材圖片或示意圖片的建材\r\n          </nb-checkbox>\r\n          <button *ngIf=\"isExcelExport\" class=\"btn btn-success mr-2\" (click)=\"exportExelMaterialList()\">匯出 <i\r\n              class=\"fas fa-file-download\"></i></button>\r\n          <button *ngIf=\"isRead\" class=\"btn btn-info mr-2 text-white ml-2\" (click)=\"search()\">\r\n            查詢 <i class=\"fas fa-search\"></i></button>\r\n          <button *ngIf=\"isCreate\" class=\"btn btn-info mx-1 ml-2 mr-2\" (click)=\"addNew(dialog)\">單筆新增 <i\r\n              class=\"fas fa-plus\"></i></button>\r\n          <button class=\"btn btn-info mx-1\" *ngIf=\"isExcelImport\" (click)=\"inputFile.click()\"> 批次匯入 </button>\r\n          <input class=\"hidden\" type=\"file\" accept=\".xls, .xlsx\" #inputFile (change)=\"detectFileExcel($event)\">\r\n          <button class=\"btn btn-success ml-2\" (click)=\"exportExelMaterialTemplate()\">下載範例檔案 <i\r\n              class=\"fas fa-file-download\"></i></button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <div class=\"table-responsive mt-4\">\r\n      <table class=\"table table-striped border \" style=\"min-width: 1200px; background-color:#f3f3f3;\">        <thead>\r\n          <tr style=\"background-color: #27ae60; color: white;\">\r\n            <th scope=\"col\" class=\"col-1\">項次</th>\r\n            <th scope=\"col\" class=\"col-1\">建材代號</th>\r\n            <th scope=\"col\" class=\"col-2\">選項題目</th>\r\n            <th scope=\"col\" class=\"col-1\">選項名稱</th>\r\n            <th scope=\"col\" class=\"col-3\">建材說明</th>\r\n            <th scope=\"col\" class=\"col-1\" *ngIf=\"ShowPrice == true\">價格</th>\r\n            <th scope=\"col\" class=\"col-1 text-center\">操作</th>\r\n          </tr>\r\n        </thead>        <tbody *ngIf=\"materialList != null && materialList.length > 0\">          <tr *ngFor=\"let item of materialList ; let i = index\">\r\n            <td>{{ item.CId}}</td>\r\n            <td>{{ item.CImageCode || '待設定' }}</td>\r\n            <td>{{ item.CName }} - {{ item.CPart }} - {{ item.CLocation }}</td>\r\n            <td [style]=\"!item.CIsMapping ? 'color: red' : ''\">{{ item.CSelectName}}</td>\r\n            <td>{{ item.CDescription}}</td>\r\n            <td *ngIf=\"item.CShowPrice == true\">{{ item.CPrice}}</td>\r\n            <td class=\"text-center w-32\">\r\n              <button class=\"btn btn-outline-primary btn-sm m-1\" (click)=\"onSelectedMaterial(item, dialog)\"\r\n                *ngIf=\"isRead\">編輯</button>\r\n              <button class=\"btn btn-outline-info btn-sm m-1\" (click)=\"bindImageForMaterial(item, imageBinder)\"\r\n                *ngIf=\"isRead\" [title]=\"'為 ' + item.CName + ' 綁定圖片'\">\r\n                <i class=\"fas fa-images\"></i>\r\n              </button>\r\n            </td>\r\n          </tr>\r\n        </tbody>\r\n      </table>\r\n    </div>\r\n  </nb-card-body>\r\n  <nb-card-footer class=\"d-flex justify-content-center\">\r\n    <ngx-pagination [(CollectionSize)]=\"totalRecords\" [(PageSize)]=\"pageSize\" [(Page)]=\"pageIndex\"\r\n      (PageChange)=\"pageChanged($event)\">\r\n    </ngx-pagination>\r\n  </nb-card-footer>\r\n</nb-card>\r\n\r\n<ng-template #dialog let-dialog let-ref=\"dialogRef\">\r\n  <nb-card class=\"w-[700px]\">\r\n    <nb-card-header>\r\n      建材管理 > 新增建材\r\n    </nb-card-header>\r\n    <nb-card-body class=\"px-4\">      <h5 class=\"text-base\">請輸入下方內容新增建材。</h5>\r\n      <div class=\"w-full mt-3\">\r\n        <!-- 預留建材代號欄位，等後端API支援後再啟用 -->\r\n        <!--\r\n        <div class=\"flex items-center\">\r\n          <label class=\"required-field w-[150px]\">建材代號</label>\r\n          <input type=\"text\" class=\"w-full !max-w-full p-2 rounded text-[13px]\" nbInput maxlength=\"30\"\r\n            [(ngModel)]=\"selectedMaterial.CMaterialCode\" />\r\n        </div>\r\n        -->\r\n        <div class=\"flex items-center mt-3\">\r\n          <label class=\"required-field w-[150px]\">選項題目(名稱)</label>\r\n          <input type=\"text\" class=\"w-full !max-w-full p-2 rounded text-[13px]\" nbInput maxlength=\"30\"\r\n            [(ngModel)]=\"selectedMaterial.CName\" />\r\n        </div>\r\n\r\n        <div class=\"flex items-center mt-3\">\r\n          <label class=\"required-field w-[150px]\">選項題目(項目)</label>\r\n          <input type=\"text\" class=\"w-full !max-w-full p-2 rounded text-[13px]\" nbInput maxlength=\"30\"\r\n            [(ngModel)]=\"selectedMaterial.CPart\" />\r\n        </div>\r\n        <div class=\"flex items-center mt-3\">\r\n          <label class=\"required-field w-[150px]\">選項題目(位置)</label>\r\n          <input type=\"text\" class=\"w-full !max-w-full p-2 rounded text-[13px]\" nbInput maxlength=\"30\"\r\n            [(ngModel)]=\"selectedMaterial.CLocation\" />\r\n        </div>\r\n\r\n        <div class=\"flex items-center mt-3\">\r\n          <label class=\"required-field w-[150px]\">建材選項名稱</label>\r\n          <input type=\"text\" nbInput class=\"w-full !max-w-full p-2 rounded text-[13px]\" maxlength=\"30\"\r\n            [(ngModel)]=\"selectedMaterial.CSelectName\" />\r\n        </div>\r\n\r\n        <div class=\"flex items-center mt-3\">\r\n          <label class=\"w-[150px]\">建材說明</label>\r\n          <textarea nbInput [(ngModel)]=\"selectedMaterial.CDescription\" [rows]=\"4\"\r\n            class=\"resize-none w-full !max-w-full p-2 rounded text-[13px]\"></textarea>\r\n        </div>\r\n\r\n        <div class=\"flex items-center mt-3\">\r\n          <label class=\"w-[150px]\">價格</label>\r\n          <input type=\"number\" nbInput class=\"w-full !max-w-full p-2 rounded text-[13px]\" maxlength=\"30\"\r\n            [(ngModel)]=\"selectedMaterial.CPrice\" />\r\n        </div>\r\n      </div>\r\n    </nb-card-body>\r\n    <nb-card-footer class=\"d-flex justify-content-center\">\r\n      <button class=\"btn btn-danger btn-sm mr-4\" (click)=\"onClose(ref)\">關閉</button>\r\n      <button class=\"btn btn-success btn-sm\" (click)=\"onSubmit(ref)\">儲存</button>\r\n    </nb-card-footer>\r\n  </nb-card>\r\n</ng-template>\r\n\r\n<ng-template #imageBinder let-dialog let-ref=\"dialogRef\">  <nb-card class=\"w-[900px] h-[700px]\">\r\n    <nb-card-header>\r\n      圖片綁定 - {{ selectedMaterial.CName ? '為 ' + selectedMaterial.CName + ' 選擇建材圖片' : '選擇建材圖片' }}\r\n    </nb-card-header>\r\n    <nb-card-body class=\"px-4 d-flex flex-column\"\r\n      style=\"height: calc(700px - 120px); overflow: hidden; padding-bottom: 0;\">\r\n\r\n      <!-- 自動綁定說明文案 -->\r\n      <div class=\"bg-blue-50 border border-blue-200 rounded-lg p-3 mb-4 flex-shrink-0\">\r\n        <div class=\"flex items-start gap-2\">\r\n          <i class=\"fas fa-info-circle text-blue-500 mt-1\"></i>\r\n          <div class=\"text-sm text-blue-700\">\r\n            <div class=\"font-medium mb-1\">智慧綁定提示</div>\r\n            <div>當圖片檔名包含建材相關文字（如名稱、項目、位置等關鍵字）時，系統會自動進行智慧配對和綁定。</div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 類別選擇 -->\r\n      <div class=\"flex gap-3 mb-4 flex-shrink-0\">\r\n        <div class=\"w-48\">\r\n          <label class=\"text-sm font-medium text-gray-700 mb-2 block\">圖片類別</label>\r\n          <nb-select [(ngModel)]=\"selectedCategory\" (selectedChange)=\"categoryChanged($event)\" class=\"w-full\">\r\n            <nb-option *ngFor=\"let option of categoryOptions\" [value]=\"option.value\">\r\n              {{ option.label }}\r\n            </nb-option>\r\n          </nb-select>\r\n        </div>\r\n        <div class=\"flex-1\">\r\n          <label class=\"text-sm font-medium text-gray-700 mb-2 block\">搜尋圖片</label>\r\n          <input type=\"text\" class=\"w-full search-input\" placeholder=\"搜尋圖片名稱...\" [(ngModel)]=\"imageSearchTerm\"\r\n            (input)=\"filterImages()\" />\r\n        </div>\r\n        <div class=\"flex flex-col justify-end\">\r\n          <button class=\"btn btn-info btn-image-action\" (click)=\"loadImages()\">\r\n            重新載入 <i class=\"fas fa-refresh\"></i>\r\n          </button>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 批次操作按鈕 -->\r\n      <div class=\"flex gap-2 mb-3 flex-shrink-0\"> <button class=\"btn btn-outline-primary btn-sm\"\r\n          (click)=\"selectAllImages()\">\r\n          全選 <i class=\"fas fa-check-square\"></i>\r\n        </button>\r\n        <button class=\"btn btn-outline-secondary btn-sm\" (click)=\"clearAllSelection()\">\r\n          清除選取\r\n        </button>\r\n        <div class=\"ml-auto text-sm text-gray-600\">\r\n          已選取: {{ selectedImages.length }} 張圖片\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 圖片列表 -->\r\n      <div class=\"image-preview-container border rounded p-3 flex-1\">\r\n        <div class=\"grid grid-cols-4 gap-3\">\r\n          <div *ngFor=\"let image of filteredImages\" class=\"image-grid-item border rounded p-2 cursor-pointer\"\r\n            [class.selected]=\"isImageSelected(image)\" (click)=\"toggleImageSelection(image)\">\r\n\r\n            <!-- 選取指示器 -->\r\n            <div class=\"flex justify-between items-center mb-2\">\r\n              <div class=\"image-checkbox w-5 h-5 border-2 rounded flex items-center justify-center\"\r\n                [class.checked]=\"isImageSelected(image)\">\r\n                <i *ngIf=\"isImageSelected(image)\" class=\"fas fa-check text-white text-xs\"></i>\r\n              </div>\r\n              <button class=\"btn btn-outline-info btn-xs btn-image-action\"\r\n                (click)=\"previewImage(image, imagePreview, $event)\">\r\n                <i class=\"fas fa-eye\"></i>\r\n              </button>\r\n            </div>\r\n\r\n            <!-- 圖片預覽 -->\r\n            <div class=\"w-full h-32 bg-gray-100 rounded mb-2 flex items-center justify-center overflow-hidden\">\r\n              <img *ngIf=\"image.thumbnailUrl\" [src]=\"image.thumbnailUrl\" [alt]=\"image.name\"\r\n                class=\"image-thumbnail max-w-full max-h-full object-contain\" />\r\n              <div *ngIf=\"!image.thumbnailUrl\" class=\"text-gray-400 text-center\">\r\n                <i class=\"fas fa-image text-2xl mb-1\"></i>\r\n                <div class=\"text-xs\">無預覽</div>\r\n              </div>\r\n            </div>\r\n\r\n            <!-- 圖片資訊 -->\r\n            <div class=\"text-xs text-gray-600\">\r\n              <div class=\"font-medium truncate\" [title]=\"image.name\">{{ image.name }}</div>\r\n              <div class=\"text-gray-400\">{{ image.size | number }} KB</div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 空狀態 -->\r\n        <div *ngIf=\"filteredImages.length === 0\" class=\"text-center text-gray-500 py-20\">\r\n          <i class=\"fas fa-images text-4xl mb-3\"></i>\r\n          <div>找不到相符的圖片</div>\r\n        </div>\r\n      </div>\r\n    </nb-card-body>\r\n    <nb-card-footer class=\"d-flex justify-content-between align-items-center\">\r\n      <div class=\"text-sm text-gray-600\">\r\n        共 {{ availableImages.length }} 張圖片\r\n      </div>\r\n      <div class=\"d-flex gap-2\">\r\n        <button class=\"btn btn-danger btn-sm\" (click)=\"onCloseImageBinder(ref)\">取消</button>\r\n        <button class=\"btn btn-success btn-sm\" [disabled]=\"selectedImages.length === 0\"\r\n          (click)=\"onConfirmImageSelection(ref)\">\r\n          確定選擇 ({{ selectedImages.length }})\r\n        </button>\r\n      </div>\r\n    </nb-card-footer>\r\n  </nb-card>\r\n</ng-template>\r\n\r\n<!-- 圖片預覽對話框 -->\r\n<ng-template #imagePreview let-dialog let-ref=\"dialogRef\">\r\n  <nb-card class=\"w-[800px] h-[600px]\">\r\n    <nb-card-header class=\"d-flex justify-content-between align-items-center\">\r\n      <span>圖片預覽 - {{ previewingImage?.name }}</span>\r\n      <div class=\"d-flex gap-2\">\r\n        <button class=\"btn btn-outline-primary btn-sm\" [disabled]=\"currentPreviewIndex <= 0\" (click)=\"previousImage()\">\r\n          <i class=\"fas fa-chevron-left\"></i> 上一張\r\n        </button>\r\n        <button class=\"btn btn-outline-primary btn-sm\" [disabled]=\"currentPreviewIndex >= filteredImages.length - 1\"\r\n          (click)=\"nextImage()\">\r\n          下一張 <i class=\"fas fa-chevron-right\"></i>\r\n        </button>\r\n      </div>\r\n    </nb-card-header> <nb-card-body class=\"p-0 d-flex justify-content-center align-items-center\" style=\"height: 500px;\">\r\n      <img *ngIf=\"previewingImage && previewingImage.fullUrl\" [src]=\"previewingImage.fullUrl\"\r\n        [alt]=\"previewingImage.name\" class=\"max-w-full max-h-full object-contain\" />\r\n      <div *ngIf=\"!previewingImage || !previewingImage.fullUrl\" class=\"text-gray-400 text-center\">\r\n        <i class=\"fas fa-image text-4xl mb-3\"></i>\r\n        <div>圖片載入失敗</div>\r\n      </div>\r\n    </nb-card-body>\r\n    <nb-card-footer class=\"d-flex justify-content-between align-items-center\">\r\n      <div class=\"text-sm text-gray-600\">\r\n        {{ currentPreviewIndex + 1 }} / {{ filteredImages.length }}\r\n      </div>\r\n      <div class=\"d-flex gap-2\"> <button class=\"btn btn-outline-info btn-sm\" (click)=\"toggleImageSelectionInPreview()\">\r\n          {{ (previewingImage && isImageSelected(previewingImage)) ? '取消選取' : '選取此圖片' }}\r\n        </button>\r\n        <button class=\"btn btn-danger btn-sm\" (click)=\"ref.close()\">關閉</button>\r\n      </div>\r\n    </nb-card-footer>\r\n  </nb-card>\r\n</ng-template>\r\n\r\n<!-- 原有的圖片檢視對話框 -->\r\n<ng-template #dialogImage let-data let-ref=\"dialogRef\">\r\n  <nb-card style=\"height: 100%; overflow: auto; width: 700px;\" class=\"mr-md-5 ml-md-5\">\r\n    <nb-card-header>\r\n      <span>\r\n        檢視\r\n      </span>\r\n    </nb-card-header>\r\n    <nb-card-body style=\"padding:1rem 2rem\">\r\n      <div class=\"w-full h-auto\">\r\n        <img class=\"fit-size\" [src]=\"currentImageShowing\">\r\n      </div>\r\n    </nb-card-body>\r\n    <nb-card-footer>\r\n      <div class=\"flex justify-center items-center\">\r\n        <button class=\"btn btn-danger mr-2\" (click)=\"ref.close()\">取消</button>\r\n      </div>\r\n    </nb-card-footer>\r\n  </nb-card>\r\n</ng-template>\r\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAM9C,SAASC,QAAQ,EAAEC,QAAQ,EAAEC,GAAG,QAAQ,MAAM;AAG9C,OAAO,KAAKC,IAAI,MAAM,MAAM;AAC5B,SAASC,YAAY,QAAQ,gCAAgC;AAC7D,SAASC,aAAa,QAAQ,qCAAqC;;;;;;;;;;;;;;ICAvDC,EAAA,CAAAC,cAAA,oBAA4E;IAC1ED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAFwCH,EAAA,CAAAI,UAAA,UAAAC,YAAA,CAAAC,GAAA,CAAuB;IACzEN,EAAA,CAAAO,SAAA,EACF;IADEP,EAAA,CAAAQ,kBAAA,MAAAH,YAAA,CAAAI,cAAA,MACF;;;;;;IAkCFT,EAAA,CAAAC,cAAA,iBAA8F;IAAnCD,EAAA,CAAAU,UAAA,mBAAAC,qEAAA;MAAAX,EAAA,CAAAY,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CAASF,MAAA,CAAAG,sBAAA,EAAwB;IAAA,EAAC;IAACjB,EAAA,CAAAE,MAAA,oBAAG;IAAAF,EAAA,CAAAkB,SAAA,YAC5D;IAAAlB,EAAA,CAAAG,YAAA,EAAS;;;;;;IAC9CH,EAAA,CAAAC,cAAA,iBAAoF;IAAnBD,EAAA,CAAAU,UAAA,mBAAAS,qEAAA;MAAAnB,EAAA,CAAAY,aAAA,CAAAQ,GAAA;MAAA,MAAAN,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CAASF,MAAA,CAAAO,MAAA,EAAQ;IAAA,EAAC;IACjFrB,EAAA,CAAAE,MAAA,qBAAG;IAAAF,EAAA,CAAAkB,SAAA,YAA6B;IAAAlB,EAAA,CAAAG,YAAA,EAAS;;;;;;IAC3CH,EAAA,CAAAC,cAAA,iBAAsF;IAAzBD,EAAA,CAAAU,UAAA,mBAAAY,qEAAA;MAAAtB,EAAA,CAAAY,aAAA,CAAAW,GAAA;MAAA,MAAAT,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,MAAAS,SAAA,GAAAxB,EAAA,CAAAyB,WAAA;MAAA,OAAAzB,EAAA,CAAAgB,WAAA,CAASF,MAAA,CAAAY,MAAA,CAAAF,SAAA,CAAc;IAAA,EAAC;IAACxB,EAAA,CAAAE,MAAA,gCAAK;IAAAF,EAAA,CAAAkB,SAAA,YAC/D;IAAAlB,EAAA,CAAAG,YAAA,EAAS;;;;;;IACrCH,EAAA,CAAAC,cAAA,iBAAoF;IAA5BD,EAAA,CAAAU,UAAA,mBAAAiB,qEAAA;MAAA3B,EAAA,CAAAY,aAAA,CAAAgB,GAAA;MAAA5B,EAAA,CAAAe,aAAA;MAAA,MAAAc,YAAA,GAAA7B,EAAA,CAAAyB,WAAA;MAAA,OAAAzB,EAAA,CAAAgB,WAAA,CAASa,YAAA,CAAAC,KAAA,EAAiB;IAAA,EAAC;IAAE9B,EAAA,CAAAE,MAAA,iCAAK;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;IAejGH,EAAA,CAAAC,cAAA,aAAwD;IAAAD,EAAA,CAAAE,MAAA,mBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;;IAS/DH,EAAA,CAAAC,cAAA,SAAoC;IAAAD,EAAA,CAAAE,MAAA,GAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;IAArBH,EAAA,CAAAO,SAAA,EAAgB;IAAhBP,EAAA,CAAA+B,iBAAA,CAAAC,QAAA,CAAAC,MAAA,CAAgB;;;;;;IAElDjC,EAAA,CAAAC,cAAA,iBACiB;IADkCD,EAAA,CAAAU,UAAA,mBAAAwB,mFAAA;MAAAlC,EAAA,CAAAY,aAAA,CAAAuB,IAAA;MAAA,MAAAH,QAAA,GAAAhC,EAAA,CAAAe,aAAA,GAAAqB,SAAA;MAAA,MAAAtB,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,MAAAS,SAAA,GAAAxB,EAAA,CAAAyB,WAAA;MAAA,OAAAzB,EAAA,CAAAgB,WAAA,CAASF,MAAA,CAAAuB,kBAAA,CAAAL,QAAA,EAAAR,SAAA,CAAgC;IAAA,EAAC;IAC5ExB,EAAA,CAAAE,MAAA,mBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;IAC5BH,EAAA,CAAAC,cAAA,iBACuD;IADPD,EAAA,CAAAU,UAAA,mBAAA4B,mFAAA;MAAAtC,EAAA,CAAAY,aAAA,CAAA2B,IAAA;MAAA,MAAAP,QAAA,GAAAhC,EAAA,CAAAe,aAAA,GAAAqB,SAAA;MAAA,MAAAtB,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,MAAAyB,eAAA,GAAAxC,EAAA,CAAAyB,WAAA;MAAA,OAAAzB,EAAA,CAAAgB,WAAA,CAASF,MAAA,CAAA2B,oBAAA,CAAAT,QAAA,EAAAQ,eAAA,CAAuC;IAAA,EAAC;IAE/FxC,EAAA,CAAAkB,SAAA,YAA6B;IAC/BlB,EAAA,CAAAG,YAAA,EAAS;;;;IAFQH,EAAA,CAAAI,UAAA,sBAAA4B,QAAA,CAAAU,KAAA,+BAAqC;;;;;IAVxD1C,EADqF,CAAAC,cAAA,SAAsD,SACvI;IAAAD,EAAA,CAAAE,MAAA,GAAa;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACtBH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,GAA8B;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACvCH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,GAA0D;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACnEH,EAAA,CAAAC,cAAA,SAAmD;IAAAD,EAAA,CAAAE,MAAA,GAAqB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC7EH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,IAAsB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC/BH,EAAA,CAAA2C,UAAA,KAAAC,sDAAA,iBAAoC;IACpC5C,EAAA,CAAAC,cAAA,cAA6B;IAG3BD,EAFA,CAAA2C,UAAA,KAAAE,0DAAA,qBACiB,KAAAC,0DAAA,qBAEsC;IAI3D9C,EADE,CAAAG,YAAA,EAAK,EACF;;;;;IAdCH,EAAA,CAAAO,SAAA,GAAa;IAAbP,EAAA,CAAA+B,iBAAA,CAAAC,QAAA,CAAAe,GAAA,CAAa;IACb/C,EAAA,CAAAO,SAAA,GAA8B;IAA9BP,EAAA,CAAA+B,iBAAA,CAAAC,QAAA,CAAAgB,UAAA,yBAA8B;IAC9BhD,EAAA,CAAAO,SAAA,GAA0D;IAA1DP,EAAA,CAAAiD,kBAAA,KAAAjB,QAAA,CAAAU,KAAA,SAAAV,QAAA,CAAAkB,KAAA,SAAAlB,QAAA,CAAAmB,SAAA,KAA0D;IAC1DnD,EAAA,CAAAO,SAAA,EAA8C;IAA9CP,EAAA,CAAAoD,UAAA,EAAApB,QAAA,CAAAqB,UAAA,qBAA8C;IAACrD,EAAA,CAAAO,SAAA,EAAqB;IAArBP,EAAA,CAAA+B,iBAAA,CAAAC,QAAA,CAAAsB,WAAA,CAAqB;IACpEtD,EAAA,CAAAO,SAAA,GAAsB;IAAtBP,EAAA,CAAA+B,iBAAA,CAAAC,QAAA,CAAAuB,YAAA,CAAsB;IACrBvD,EAAA,CAAAO,SAAA,EAA6B;IAA7BP,EAAA,CAAAI,UAAA,SAAA4B,QAAA,CAAAwB,UAAA,SAA6B;IAG7BxD,EAAA,CAAAO,SAAA,GAAY;IAAZP,EAAA,CAAAI,UAAA,SAAAU,MAAA,CAAA2C,MAAA,CAAY;IAEZzD,EAAA,CAAAO,SAAA,EAAY;IAAZP,EAAA,CAAAI,UAAA,SAAAU,MAAA,CAAA2C,MAAA,CAAY;;;;;IAXLzD,EAAA,CAAAC,cAAA,YAA+D;IAAUD,EAAA,CAAA2C,UAAA,IAAAe,gDAAA,mBAAsD;IAgB/I1D,EAAA,CAAAG,YAAA,EAAQ;;;;IAhBsGH,EAAA,CAAAO,SAAA,EAAkB;IAAlBP,EAAA,CAAAI,UAAA,YAAAU,MAAA,CAAA6C,YAAA,CAAkB;;;;;;IA6BpI3D,EADF,CAAAC,cAAA,kBAA2B,qBACT;IACdD,EAAA,CAAAE,MAAA,4DACF;IAAAF,EAAA,CAAAG,YAAA,EAAiB;IACgBH,EAAjC,CAAAC,cAAA,uBAA2B,aAA4B;IAAAD,EAAA,CAAAE,MAAA,+EAAY;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAWlEH,EAVJ,CAAAC,cAAA,cAAyB,cASa,gBACM;IAAAD,EAAA,CAAAE,MAAA,6CAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACxDH,EAAA,CAAAC,cAAA,iBACyC;IAAvCD,EAAA,CAAA4D,gBAAA,2BAAAC,kFAAAC,MAAA;MAAA9D,EAAA,CAAAY,aAAA,CAAAmD,IAAA;MAAA,MAAAjD,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAAf,EAAA,CAAAgE,kBAAA,CAAAlD,MAAA,CAAAmD,gBAAA,CAAAvB,KAAA,EAAAoB,MAAA,MAAAhD,MAAA,CAAAmD,gBAAA,CAAAvB,KAAA,GAAAoB,MAAA;MAAA,OAAA9D,EAAA,CAAAgB,WAAA,CAAA8C,MAAA;IAAA,EAAoC;IACxC9D,EAFE,CAAAG,YAAA,EACyC,EACrC;IAGJH,EADF,CAAAC,cAAA,eAAoC,iBACM;IAAAD,EAAA,CAAAE,MAAA,8CAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACxDH,EAAA,CAAAC,cAAA,iBACyC;IAAvCD,EAAA,CAAA4D,gBAAA,2BAAAM,kFAAAJ,MAAA;MAAA9D,EAAA,CAAAY,aAAA,CAAAmD,IAAA;MAAA,MAAAjD,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAAf,EAAA,CAAAgE,kBAAA,CAAAlD,MAAA,CAAAmD,gBAAA,CAAAf,KAAA,EAAAY,MAAA,MAAAhD,MAAA,CAAAmD,gBAAA,CAAAf,KAAA,GAAAY,MAAA;MAAA,OAAA9D,EAAA,CAAAgB,WAAA,CAAA8C,MAAA;IAAA,EAAoC;IACxC9D,EAFE,CAAAG,YAAA,EACyC,EACrC;IAEJH,EADF,CAAAC,cAAA,eAAoC,iBACM;IAAAD,EAAA,CAAAE,MAAA,8CAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACxDH,EAAA,CAAAC,cAAA,iBAC6C;IAA3CD,EAAA,CAAA4D,gBAAA,2BAAAO,kFAAAL,MAAA;MAAA9D,EAAA,CAAAY,aAAA,CAAAmD,IAAA;MAAA,MAAAjD,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAAf,EAAA,CAAAgE,kBAAA,CAAAlD,MAAA,CAAAmD,gBAAA,CAAAd,SAAA,EAAAW,MAAA,MAAAhD,MAAA,CAAAmD,gBAAA,CAAAd,SAAA,GAAAW,MAAA;MAAA,OAAA9D,EAAA,CAAAgB,WAAA,CAAA8C,MAAA;IAAA,EAAwC;IAC5C9D,EAFE,CAAAG,YAAA,EAC6C,EACzC;IAGJH,EADF,CAAAC,cAAA,eAAoC,iBACM;IAAAD,EAAA,CAAAE,MAAA,4CAAM;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACtDH,EAAA,CAAAC,cAAA,iBAC+C;IAA7CD,EAAA,CAAA4D,gBAAA,2BAAAQ,kFAAAN,MAAA;MAAA9D,EAAA,CAAAY,aAAA,CAAAmD,IAAA;MAAA,MAAAjD,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAAf,EAAA,CAAAgE,kBAAA,CAAAlD,MAAA,CAAAmD,gBAAA,CAAAX,WAAA,EAAAQ,MAAA,MAAAhD,MAAA,CAAAmD,gBAAA,CAAAX,WAAA,GAAAQ,MAAA;MAAA,OAAA9D,EAAA,CAAAgB,WAAA,CAAA8C,MAAA;IAAA,EAA0C;IAC9C9D,EAFE,CAAAG,YAAA,EAC+C,EAC3C;IAGJH,EADF,CAAAC,cAAA,eAAoC,iBACT;IAAAD,EAAA,CAAAE,MAAA,gCAAI;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACrCH,EAAA,CAAAC,cAAA,oBACiE;IAD/CD,EAAA,CAAA4D,gBAAA,2BAAAS,qFAAAP,MAAA;MAAA9D,EAAA,CAAAY,aAAA,CAAAmD,IAAA;MAAA,MAAAjD,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAAf,EAAA,CAAAgE,kBAAA,CAAAlD,MAAA,CAAAmD,gBAAA,CAAAV,YAAA,EAAAO,MAAA,MAAAhD,MAAA,CAAAmD,gBAAA,CAAAV,YAAA,GAAAO,MAAA;MAAA,OAAA9D,EAAA,CAAAgB,WAAA,CAAA8C,MAAA;IAAA,EAA2C;IAE/D9D,EADmE,CAAAG,YAAA,EAAW,EACxE;IAGJH,EADF,CAAAC,cAAA,eAAoC,iBACT;IAAAD,EAAA,CAAAE,MAAA,oBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACnCH,EAAA,CAAAC,cAAA,iBAC0C;IAAxCD,EAAA,CAAA4D,gBAAA,2BAAAU,kFAAAR,MAAA;MAAA9D,EAAA,CAAAY,aAAA,CAAAmD,IAAA;MAAA,MAAAjD,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAAf,EAAA,CAAAgE,kBAAA,CAAAlD,MAAA,CAAAmD,gBAAA,CAAAhC,MAAA,EAAA6B,MAAA,MAAAhD,MAAA,CAAAmD,gBAAA,CAAAhC,MAAA,GAAA6B,MAAA;MAAA,OAAA9D,EAAA,CAAAgB,WAAA,CAAA8C,MAAA;IAAA,EAAqC;IAG7C9D,EAJM,CAAAG,YAAA,EAC0C,EACtC,EACF,EACO;IAEbH,EADF,CAAAC,cAAA,0BAAsD,kBACc;IAAvBD,EAAA,CAAAU,UAAA,mBAAA6D,2EAAA;MAAA,MAAAC,OAAA,GAAAxE,EAAA,CAAAY,aAAA,CAAAmD,IAAA,EAAAU,SAAA;MAAA,MAAA3D,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CAASF,MAAA,CAAA4D,OAAA,CAAAF,OAAA,CAAY;IAAA,EAAC;IAACxE,EAAA,CAAAE,MAAA,oBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAC7EH,EAAA,CAAAC,cAAA,kBAA+D;IAAxBD,EAAA,CAAAU,UAAA,mBAAAiE,2EAAA;MAAA,MAAAH,OAAA,GAAAxE,EAAA,CAAAY,aAAA,CAAAmD,IAAA,EAAAU,SAAA;MAAA,MAAA3D,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CAASF,MAAA,CAAA8D,QAAA,CAAAJ,OAAA,CAAa;IAAA,EAAC;IAACxE,EAAA,CAAAE,MAAA,oBAAE;IAErEF,EAFqE,CAAAG,YAAA,EAAS,EAC3D,EACT;;;;IArCAH,EAAA,CAAAO,SAAA,IAAoC;IAApCP,EAAA,CAAA6E,gBAAA,YAAA/D,MAAA,CAAAmD,gBAAA,CAAAvB,KAAA,CAAoC;IAMpC1C,EAAA,CAAAO,SAAA,GAAoC;IAApCP,EAAA,CAAA6E,gBAAA,YAAA/D,MAAA,CAAAmD,gBAAA,CAAAf,KAAA,CAAoC;IAKpClD,EAAA,CAAAO,SAAA,GAAwC;IAAxCP,EAAA,CAAA6E,gBAAA,YAAA/D,MAAA,CAAAmD,gBAAA,CAAAd,SAAA,CAAwC;IAMxCnD,EAAA,CAAAO,SAAA,GAA0C;IAA1CP,EAAA,CAAA6E,gBAAA,YAAA/D,MAAA,CAAAmD,gBAAA,CAAAX,WAAA,CAA0C;IAK1BtD,EAAA,CAAAO,SAAA,GAA2C;IAA3CP,EAAA,CAAA6E,gBAAA,YAAA/D,MAAA,CAAAmD,gBAAA,CAAAV,YAAA,CAA2C;IAACvD,EAAA,CAAAI,UAAA,WAAU;IAOtEJ,EAAA,CAAAO,SAAA,GAAqC;IAArCP,EAAA,CAAA6E,gBAAA,YAAA/D,MAAA,CAAAmD,gBAAA,CAAAhC,MAAA,CAAqC;;;;;IAkCrCjC,EAAA,CAAAC,cAAA,oBAAyE;IACvED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAFsCH,EAAA,CAAAI,UAAA,UAAA0E,UAAA,CAAAC,KAAA,CAAsB;IACtE/E,EAAA,CAAAO,SAAA,EACF;IADEP,EAAA,CAAAQ,kBAAA,MAAAsE,UAAA,CAAAE,KAAA,MACF;;;;;IAsCIhF,EAAA,CAAAkB,SAAA,aAA8E;;;;;IAUhFlB,EAAA,CAAAkB,SAAA,eACiE;;;;IADNlB,EAA3B,CAAAI,UAAA,QAAA6E,SAAA,CAAAC,YAAA,EAAAlF,EAAA,CAAAmF,aAAA,CAA0B,QAAAF,SAAA,CAAAG,IAAA,CAAmB;;;;;IAE7EpF,EAAA,CAAAC,cAAA,eAAmE;IACjED,EAAA,CAAAkB,SAAA,aAA0C;IAC1ClB,EAAA,CAAAC,cAAA,eAAqB;IAAAD,EAAA,CAAAE,MAAA,yBAAG;IAC1BF,EAD0B,CAAAG,YAAA,EAAM,EAC1B;;;;;;IAtBVH,EAAA,CAAAC,cAAA,cACkF;IAAtCD,EAAA,CAAAU,UAAA,mBAAA2E,8EAAA;MAAA,MAAAJ,SAAA,GAAAjF,EAAA,CAAAY,aAAA,CAAA0E,IAAA,EAAAlD,SAAA;MAAA,MAAAtB,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CAASF,MAAA,CAAAyE,oBAAA,CAAAN,SAAA,CAA2B;IAAA,EAAC;IAI7EjF,EADF,CAAAC,cAAA,cAAoD,cAEP;IACzCD,EAAA,CAAA2C,UAAA,IAAA6C,4DAAA,gBAA0E;IAC5ExF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,iBACsD;IAApDD,EAAA,CAAAU,UAAA,mBAAA+E,iFAAA3B,MAAA;MAAA,MAAAmB,SAAA,GAAAjF,EAAA,CAAAY,aAAA,CAAA0E,IAAA,EAAAlD,SAAA;MAAA,MAAAtB,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,MAAA2E,gBAAA,GAAA1F,EAAA,CAAAyB,WAAA;MAAA,OAAAzB,EAAA,CAAAgB,WAAA,CAASF,MAAA,CAAA6E,YAAA,CAAAV,SAAA,EAAAS,gBAAA,EAAA5B,MAAA,CAAyC;IAAA,EAAC;IACnD9D,EAAA,CAAAkB,SAAA,YAA0B;IAE9BlB,EADE,CAAAG,YAAA,EAAS,EACL;IAGNH,EAAA,CAAAC,cAAA,cAAmG;IAGjGD,EAFA,CAAA2C,UAAA,IAAAiD,8DAAA,kBACiE,IAAAC,8DAAA,kBACE;IAIrE7F,EAAA,CAAAG,YAAA,EAAM;IAIJH,EADF,CAAAC,cAAA,eAAmC,gBACsB;IAAAD,EAAA,CAAAE,MAAA,IAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAC7EH,EAAA,CAAAC,cAAA,gBAA2B;IAAAD,EAAA,CAAAE,MAAA,IAA4B;;IAE3DF,EAF2D,CAAAG,YAAA,EAAM,EACzD,EACF;;;;;IA7BJH,EAAA,CAAA8F,WAAA,aAAAhF,MAAA,CAAAiF,eAAA,CAAAd,SAAA,EAAyC;IAKrCjF,EAAA,CAAAO,SAAA,GAAwC;IAAxCP,EAAA,CAAA8F,WAAA,YAAAhF,MAAA,CAAAiF,eAAA,CAAAd,SAAA,EAAwC;IACpCjF,EAAA,CAAAO,SAAA,EAA4B;IAA5BP,EAAA,CAAAI,UAAA,SAAAU,MAAA,CAAAiF,eAAA,CAAAd,SAAA,EAA4B;IAU5BjF,EAAA,CAAAO,SAAA,GAAwB;IAAxBP,EAAA,CAAAI,UAAA,SAAA6E,SAAA,CAAAC,YAAA,CAAwB;IAExBlF,EAAA,CAAAO,SAAA,EAAyB;IAAzBP,EAAA,CAAAI,UAAA,UAAA6E,SAAA,CAAAC,YAAA,CAAyB;IAQGlF,EAAA,CAAAO,SAAA,GAAoB;IAApBP,EAAA,CAAAI,UAAA,UAAA6E,SAAA,CAAAG,IAAA,CAAoB;IAACpF,EAAA,CAAAO,SAAA,EAAgB;IAAhBP,EAAA,CAAA+B,iBAAA,CAAAkD,SAAA,CAAAG,IAAA,CAAgB;IAC5CpF,EAAA,CAAAO,SAAA,GAA4B;IAA5BP,EAAA,CAAAQ,kBAAA,KAAAR,EAAA,CAAAgG,WAAA,SAAAf,SAAA,CAAAgB,IAAA,SAA4B;;;;;IAM7DjG,EAAA,CAAAC,cAAA,eAAiF;IAC/ED,EAAA,CAAAkB,SAAA,aAA2C;IAC3ClB,EAAA,CAAAC,cAAA,UAAK;IAAAD,EAAA,CAAAE,MAAA,uDAAQ;IACfF,EADe,CAAAG,YAAA,EAAM,EACf;;;;;;IA5FVH,EADuD,CAAAC,cAAA,kBAAqC,qBAC5E;IACdD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAiB;IAMbH,EALJ,CAAAC,cAAA,uBAC4E,cAGO,cAC3C;IAClCD,EAAA,CAAAkB,SAAA,YAAqD;IAEnDlB,EADF,CAAAC,cAAA,cAAmC,cACH;IAAAD,EAAA,CAAAE,MAAA,2CAAM;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAC1CH,EAAA,CAAAC,cAAA,WAAK;IAAAD,EAAA,CAAAE,MAAA,sRAA6C;IAGxDF,EAHwD,CAAAG,YAAA,EAAM,EACpD,EACF,EACF;IAKFH,EAFJ,CAAAC,cAAA,eAA2C,eACvB,iBAC4C;IAAAD,EAAA,CAAAE,MAAA,gCAAI;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACxEH,EAAA,CAAAC,cAAA,qBAAoG;IAAzFD,EAAA,CAAA4D,gBAAA,2BAAAsC,sFAAApC,MAAA;MAAA9D,EAAA,CAAAY,aAAA,CAAAuF,IAAA;MAAA,MAAArF,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAAf,EAAA,CAAAgE,kBAAA,CAAAlD,MAAA,CAAAsF,gBAAA,EAAAtC,MAAA,MAAAhD,MAAA,CAAAsF,gBAAA,GAAAtC,MAAA;MAAA,OAAA9D,EAAA,CAAAgB,WAAA,CAAA8C,MAAA;IAAA,EAA8B;IAAC9D,EAAA,CAAAU,UAAA,4BAAA2F,uFAAAvC,MAAA;MAAA9D,EAAA,CAAAY,aAAA,CAAAuF,IAAA;MAAA,MAAArF,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CAAkBF,MAAA,CAAAwF,eAAA,CAAAxC,MAAA,CAAuB;IAAA,EAAC;IAClF9D,EAAA,CAAA2C,UAAA,KAAA4D,8DAAA,wBAAyE;IAI7EvG,EADE,CAAAG,YAAA,EAAY,EACR;IAEJH,EADF,CAAAC,cAAA,eAAoB,iBAC0C;IAAAD,EAAA,CAAAE,MAAA,gCAAI;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACxEH,EAAA,CAAAC,cAAA,iBAC6B;IAD0CD,EAAA,CAAA4D,gBAAA,2BAAA4C,kFAAA1C,MAAA;MAAA9D,EAAA,CAAAY,aAAA,CAAAuF,IAAA;MAAA,MAAArF,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAAf,EAAA,CAAAgE,kBAAA,CAAAlD,MAAA,CAAA2F,eAAA,EAAA3C,MAAA,MAAAhD,MAAA,CAAA2F,eAAA,GAAA3C,MAAA;MAAA,OAAA9D,EAAA,CAAAgB,WAAA,CAAA8C,MAAA;IAAA,EAA6B;IAClG9D,EAAA,CAAAU,UAAA,mBAAAgG,0EAAA;MAAA1G,EAAA,CAAAY,aAAA,CAAAuF,IAAA;MAAA,MAAArF,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CAASF,MAAA,CAAA6F,YAAA,EAAc;IAAA,EAAC;IAC5B3G,EAFE,CAAAG,YAAA,EAC6B,EACzB;IAEJH,EADF,CAAAC,cAAA,eAAuC,kBACgC;IAAvBD,EAAA,CAAAU,UAAA,mBAAAkG,2EAAA;MAAA5G,EAAA,CAAAY,aAAA,CAAAuF,IAAA;MAAA,MAAArF,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CAASF,MAAA,CAAA+F,UAAA,EAAY;IAAA,EAAC;IAClE7G,EAAA,CAAAE,MAAA,kCAAK;IAAAF,EAAA,CAAAkB,SAAA,aAA8B;IAGzClB,EAFI,CAAAG,YAAA,EAAS,EACL,EACF;IAGsCH,EAA5C,CAAAC,cAAA,eAA2C,kBACX;IAA5BD,EAAA,CAAAU,UAAA,mBAAAoG,2EAAA;MAAA9G,EAAA,CAAAY,aAAA,CAAAuF,IAAA;MAAA,MAAArF,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CAASF,MAAA,CAAAiG,eAAA,EAAiB;IAAA,EAAC;IAC3B/G,EAAA,CAAAE,MAAA,sBAAG;IAAAF,EAAA,CAAAkB,SAAA,aAAmC;IACxClB,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,kBAA+E;IAA9BD,EAAA,CAAAU,UAAA,mBAAAsG,2EAAA;MAAAhH,EAAA,CAAAY,aAAA,CAAAuF,IAAA;MAAA,MAAArF,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CAASF,MAAA,CAAAmG,iBAAA,EAAmB;IAAA,EAAC;IAC5EjH,EAAA,CAAAE,MAAA,kCACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,eAA2C;IACzCD,EAAA,CAAAE,MAAA,IACF;IACFF,EADE,CAAAG,YAAA,EAAM,EACF;IAIJH,EADF,CAAAC,cAAA,eAA+D,eACzB;IAClCD,EAAA,CAAA2C,UAAA,KAAAuE,wDAAA,oBACkF;IA8BpFlH,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAA2C,UAAA,KAAAwE,wDAAA,kBAAiF;IAKrFnH,EADE,CAAAG,YAAA,EAAM,EACO;IAEbH,EADF,CAAAC,cAAA,0BAA0E,eACrC;IACjCD,EAAA,CAAAE,MAAA,IACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAEJH,EADF,CAAAC,cAAA,eAA0B,kBACgD;IAAlCD,EAAA,CAAAU,UAAA,mBAAA0G,2EAAA;MAAA,MAAAC,OAAA,GAAArH,EAAA,CAAAY,aAAA,CAAAuF,IAAA,EAAA1B,SAAA;MAAA,MAAA3D,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CAASF,MAAA,CAAAwG,kBAAA,CAAAD,OAAA,CAAuB;IAAA,EAAC;IAACrH,EAAA,CAAAE,MAAA,oBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACnFH,EAAA,CAAAC,cAAA,kBACyC;IAAvCD,EAAA,CAAAU,UAAA,mBAAA6G,2EAAA;MAAA,MAAAF,OAAA,GAAArH,EAAA,CAAAY,aAAA,CAAAuF,IAAA,EAAA1B,SAAA;MAAA,MAAA3D,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CAASF,MAAA,CAAA0G,uBAAA,CAAAH,OAAA,CAA4B;IAAA,EAAC;IACtCrH,EAAA,CAAAE,MAAA,IACF;IAGNF,EAHM,CAAAG,YAAA,EAAS,EACL,EACS,EACT;;;;IA1GNH,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAAQ,kBAAA,iCAAAM,MAAA,CAAAmD,gBAAA,CAAAvB,KAAA,eAAA5B,MAAA,CAAAmD,gBAAA,CAAAvB,KAAA,yFACF;IAmBiB1C,EAAA,CAAAO,SAAA,IAA8B;IAA9BP,EAAA,CAAA6E,gBAAA,YAAA/D,MAAA,CAAAsF,gBAAA,CAA8B;IACTpG,EAAA,CAAAO,SAAA,EAAkB;IAAlBP,EAAA,CAAAI,UAAA,YAAAU,MAAA,CAAA2G,eAAA,CAAkB;IAOqBzH,EAAA,CAAAO,SAAA,GAA6B;IAA7BP,EAAA,CAAA6E,gBAAA,YAAA/D,MAAA,CAAA2F,eAAA,CAA6B;IAmBpGzG,EAAA,CAAAO,SAAA,IACF;IADEP,EAAA,CAAAQ,kBAAA,0BAAAM,MAAA,CAAA4G,cAAA,CAAAC,MAAA,yBACF;IAMyB3H,EAAA,CAAAO,SAAA,GAAiB;IAAjBP,EAAA,CAAAI,UAAA,YAAAU,MAAA,CAAA8G,cAAA,CAAiB;IAkCpC5H,EAAA,CAAAO,SAAA,EAAiC;IAAjCP,EAAA,CAAAI,UAAA,SAAAU,MAAA,CAAA8G,cAAA,CAAAD,MAAA,OAAiC;IAQvC3H,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAAQ,kBAAA,aAAAM,MAAA,CAAA+G,eAAA,CAAAF,MAAA,yBACF;IAGyC3H,EAAA,CAAAO,SAAA,GAAwC;IAAxCP,EAAA,CAAAI,UAAA,aAAAU,MAAA,CAAA4G,cAAA,CAAAC,MAAA,OAAwC;IAE7E3H,EAAA,CAAAO,SAAA,EACF;IADEP,EAAA,CAAAQ,kBAAA,gCAAAM,MAAA,CAAA4G,cAAA,CAAAC,MAAA,OACF;;;;;IAqBF3H,EAAA,CAAAkB,SAAA,eAC8E;;;;IAA5ElB,EADsD,CAAAI,UAAA,QAAAU,MAAA,CAAAgH,eAAA,CAAAC,OAAA,EAAA/H,EAAA,CAAAmF,aAAA,CAA+B,QAAArE,MAAA,CAAAgH,eAAA,CAAA1C,IAAA,CACzD;;;;;IAC9BpF,EAAA,CAAAC,cAAA,eAA4F;IAC1FD,EAAA,CAAAkB,SAAA,aAA0C;IAC1ClB,EAAA,CAAAC,cAAA,UAAK;IAAAD,EAAA,CAAAE,MAAA,2CAAM;IACbF,EADa,CAAAG,YAAA,EAAM,EACb;;;;;;IAhBNH,EAFJ,CAAAC,cAAA,mBAAqC,yBACuC,WAClE;IAAAD,EAAA,CAAAE,MAAA,GAAkC;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAE7CH,EADF,CAAAC,cAAA,cAA0B,kBACuF;IAA1BD,EAAA,CAAAU,UAAA,mBAAAsH,0EAAA;MAAAhI,EAAA,CAAAY,aAAA,CAAAqH,IAAA;MAAA,MAAAnH,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CAASF,MAAA,CAAAoH,aAAA,EAAe;IAAA,EAAC;IAC5GlI,EAAA,CAAAkB,SAAA,aAAmC;IAAClB,EAAA,CAAAE,MAAA,2BACtC;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,kBACwB;IAAtBD,EAAA,CAAAU,UAAA,mBAAAyH,0EAAA;MAAAnI,EAAA,CAAAY,aAAA,CAAAqH,IAAA;MAAA,MAAAnH,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CAASF,MAAA,CAAAsH,SAAA,EAAW;IAAA,EAAC;IACrBpI,EAAA,CAAAE,MAAA,2BAAI;IAAAF,EAAA,CAAAkB,SAAA,cAAoC;IAG9ClB,EAFI,CAAAG,YAAA,EAAS,EACL,EACS;IAACH,EAAA,CAAAC,cAAA,yBAAkG;IAGlHD,EAFA,CAAA2C,UAAA,KAAA0F,wDAAA,mBAC8E,KAAAC,wDAAA,kBACc;IAI9FtI,EAAA,CAAAG,YAAA,EAAe;IAEbH,EADF,CAAAC,cAAA,0BAA0E,eACrC;IACjCD,EAAA,CAAAE,MAAA,IACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACqBH,EAA3B,CAAAC,cAAA,eAA0B,mBAAuF;IAA1CD,EAAA,CAAAU,UAAA,mBAAA6H,2EAAA;MAAAvI,EAAA,CAAAY,aAAA,CAAAqH,IAAA;MAAA,MAAAnH,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CAASF,MAAA,CAAA0H,6BAAA,EAA+B;IAAA,EAAC;IAC5GxI,EAAA,CAAAE,MAAA,IACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,kBAA4D;IAAtBD,EAAA,CAAAU,UAAA,mBAAA+H,2EAAA;MAAA,MAAAC,OAAA,GAAA1I,EAAA,CAAAY,aAAA,CAAAqH,IAAA,EAAAxD,SAAA;MAAA,OAAAzE,EAAA,CAAAgB,WAAA,CAAS0H,OAAA,CAAAC,KAAA,EAAW;IAAA,EAAC;IAAC3I,EAAA,CAAAE,MAAA,oBAAE;IAGpEF,EAHoE,CAAAG,YAAA,EAAS,EACnE,EACS,EACT;;;;IA5BAH,EAAA,CAAAO,SAAA,GAAkC;IAAlCP,EAAA,CAAAQ,kBAAA,gCAAAM,MAAA,CAAAgH,eAAA,kBAAAhH,MAAA,CAAAgH,eAAA,CAAA1C,IAAA,KAAkC;IAESpF,EAAA,CAAAO,SAAA,GAAqC;IAArCP,EAAA,CAAAI,UAAA,aAAAU,MAAA,CAAA8H,mBAAA,MAAqC;IAGrC5I,EAAA,CAAAO,SAAA,GAA6D;IAA7DP,EAAA,CAAAI,UAAA,aAAAU,MAAA,CAAA8H,mBAAA,IAAA9H,MAAA,CAAA8G,cAAA,CAAAD,MAAA,KAA6D;IAMxG3H,EAAA,CAAAO,SAAA,GAAgD;IAAhDP,EAAA,CAAAI,UAAA,SAAAU,MAAA,CAAAgH,eAAA,IAAAhH,MAAA,CAAAgH,eAAA,CAAAC,OAAA,CAAgD;IAEhD/H,EAAA,CAAAO,SAAA,EAAkD;IAAlDP,EAAA,CAAAI,UAAA,UAAAU,MAAA,CAAAgH,eAAA,KAAAhH,MAAA,CAAAgH,eAAA,CAAAC,OAAA,CAAkD;IAOtD/H,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAA6I,kBAAA,MAAA/H,MAAA,CAAA8H,mBAAA,aAAA9H,MAAA,CAAA8G,cAAA,CAAAD,MAAA,MACF;IAEI3H,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAAQ,kBAAA,MAAAM,MAAA,CAAAgH,eAAA,IAAAhH,MAAA,CAAAiF,eAAA,CAAAjF,MAAA,CAAAgH,eAAA,uEACF;;;;;;IAWF9H,EAFJ,CAAAC,cAAA,mBAAqF,qBACnE,WACR;IACJD,EAAA,CAAAE,MAAA,qBACF;IACFF,EADE,CAAAG,YAAA,EAAO,EACQ;IAEfH,EADF,CAAAC,cAAA,wBAAwC,eACX;IACzBD,EAAA,CAAAkB,SAAA,eAAkD;IAEtDlB,EADE,CAAAG,YAAA,EAAM,EACO;IAGXH,EAFJ,CAAAC,cAAA,qBAAgB,eACgC,kBACc;IAAtBD,EAAA,CAAAU,UAAA,mBAAAoI,0EAAA;MAAA,MAAAC,OAAA,GAAA/I,EAAA,CAAAY,aAAA,CAAAoI,IAAA,EAAAvE,SAAA;MAAA,OAAAzE,EAAA,CAAAgB,WAAA,CAAS+H,OAAA,CAAAJ,KAAA,EAAW;IAAA,EAAC;IAAC3I,EAAA,CAAAE,MAAA,oBAAE;IAGlEF,EAHkE,CAAAG,YAAA,EAAS,EACjE,EACS,EACT;;;;IARkBH,EAAA,CAAAO,SAAA,GAA2B;IAA3BP,EAAA,CAAAI,UAAA,QAAAU,MAAA,CAAAmI,mBAAA,EAAAjJ,EAAA,CAAAmF,aAAA,CAA2B;;;AD3SzD;AACA,IAAK+D,eAIJ;AAJD,WAAKA,eAAe;EAClBA,eAAA,CAAAA,eAAA,sBAAQ;EACRA,eAAA,CAAAA,eAAA,gDAAqB;EACrBA,eAAA,CAAAA,eAAA,gCAAa,EAAO;AACtB,CAAC,EAJIA,eAAe,KAAfA,eAAe;AAwBpB,OAAM,MAAOC,yBAA0B,SAAQpJ,aAAa;EAoD1DqJ,YACUC,MAAmB,EACnBC,aAA8B,EAC9BC,OAAuB,EACvBC,KAAuB,EACvBC,iBAAmC,EACnCC,gBAAiC,EACjCC,eAA+B,EAC/BC,eAA+B;IAEvC,KAAK,CAACP,MAAM,CAAC;IATL,KAAAA,MAAM,GAANA,MAAM;IACN,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,eAAe,GAAfA,eAAe;IA3DzB,KAAAC,KAAK,GAAG,IAAI;IAKZ,KAAAC,cAAc,GAA8B,EAAE;IAG9C,KAAAC,eAAe,GAAG,CAChB;MACEhF,KAAK,EAAE,IAAI;MACXC,KAAK,EAAE;KACR,EACD;MACED,KAAK,EAAE,KAAK;MACZC,KAAK,EAAE;KACR,EACD;MACED,KAAK,EAAE,IAAI;MACXC,KAAK,EAAE;KACR,CAAC;IACJ,KAAAgF,iBAAiB,GAAG,IAAI;IAAG,KAAA1G,WAAW,GAAW,EAAE;IACnD;IACA;IACA;IACA;IACA,KAAA2G,aAAa,GAAW,EAAE;IAC1B,KAAAC,SAAS,GAAY,KAAK;IAC1B,KAAAjB,mBAAmB,GAAW,EAAE;IAChC,KAAAkB,aAAa,GAAY,KAAK;IAC9B,KAAA9G,UAAU,GAAY,IAAI;IAE1B;IACA,KAAAwE,eAAe,GAAgB,EAAE;IACjC,KAAAD,cAAc,GAAgB,EAAE;IAChC,KAAAF,cAAc,GAAgB,EAAE;IAChC,KAAAjB,eAAe,GAAW,EAAE;IAC5B,KAAAqB,eAAe,GAAqB,IAAI;IACxC,KAAAc,mBAAmB,GAAW,CAAC;IAE/B;IACA,KAAAnB,eAAe,GAAG,CAChB;MAAE1C,KAAK,EAAEmE,eAAe,CAACkB,iBAAiB;MAAEpF,KAAK,EAAE;IAAM,CAAE,EAC3D;MAAED,KAAK,EAAEmE,eAAe,CAACmB,SAAS;MAAErF,KAAK,EAAE;IAAM,CAAE,CACpD;IACD,KAAAoB,gBAAgB,GAAoB8C,eAAe,CAACkB,iBAAiB;IACrE,KAAAE,kBAAkB,GAAY,IAAI,EAAC;IAEnC;IACA,KAAApB,eAAe,GAAGA,eAAe;EAajC;EAESqB,QAAQA,CAAA;IACf,IAAI,CAACC,gBAAgB,EAAE;EACzB;EAEAA,gBAAgBA,CAAA;IACd,IAAI,CAACf,iBAAiB,CAACgB,6CAA6C,CAAC;MACnEC,IAAI,EAAE;QACJC,OAAO,EAAE,KAAK;QACdC,OAAO,EAAE;;KAEZ,CAAC,CACCC,IAAI,CACHjL,GAAG,CAACkL,GAAG,IAAG;MACR,IAAIA,GAAG,CAACC,UAAU,IAAI,CAAC,EAAE;QACvB,IAAI,CAACjB,cAAc,GAAGgB,GAAG,CAACE,OAAO,EAAErD,MAAM,GAAGmD,GAAG,CAACE,OAAO,GAAG,EAAE;QAC5D,IAAI,CAACC,mBAAmB,GAAG,IAAI,CAACnB,cAAc,CAAC,CAAC,CAAC,CAACxJ,GAAI;MACxD;IACF,CAAC,CAAC,EACFX,QAAQ,CAAC,MAAM,IAAI,CAACuL,eAAe,EAAE,CAAC,CACvC,CAACC,SAAS,EAAE;EACjB;EAAGD,eAAeA,CAACE,SAAA,GAAoB,CAAC;IACtC,OAAO,IAAI,CAAC1B,gBAAgB,CAAC2B,mCAAmC,CAAC;MAC/DX,IAAI,EAAE;QACJY,YAAY,EAAE,IAAI,CAACL,mBAAmB;QACtCM,QAAQ,EAAE,IAAI,CAACvB,iBAAiB;QAChC1G,WAAW,EAAE,IAAI,CAACA,WAAW;QAC7B;QACA;QACA;QACA;QACA;QACAkI,QAAQ,EAAE,IAAI,CAACC,QAAQ;QACvBC,SAAS,EAAEN,SAAS;QACpB/H,UAAU,EAAE,IAAI,CAACA;;KAEpB,CAAC,CAACwH,IAAI,CACLjL,GAAG,CAACkL,GAAG,IAAG;MACR,IAAIA,GAAG,CAACC,UAAU,IAAI,CAAC,EAAE;QACvB,IAAI,CAACpH,YAAY,GAAGmH,GAAG,CAACE,OAAQ,IAAI,EAAE;QACtC,IAAI,CAACW,YAAY,GAAGb,GAAG,CAACc,UAAW;QAEnC,IAAI,IAAI,CAACjI,YAAY,CAACgE,MAAM,GAAG,CAAC,EAAE;UAChC,IAAI,CAACuC,SAAS,GAAG,IAAI,CAACvG,YAAY,CAAC,CAAC,CAAC,CAACH,UAAW;QACnD;MACF;IACF,CAAC,CAAC,CACH;EACH;EAEAnC,MAAMA,CAAA;IACJ,IAAI,CAAC6J,eAAe,EAAE,CAACC,SAAS,EAAE;EACpC;EAEAU,WAAWA,CAACT,SAAiB;IAC3B,IAAI,CAACF,eAAe,CAACE,SAAS,CAAC,CAACD,SAAS,EAAE;EAC7C;EAEAlK,sBAAsBA,CAAA;IACpB,IAAI,CAACyI,gBAAgB,CAACoC,2CAA2C,CAAC;MAChEpB,IAAI,EAAE,IAAI,CAACO;KACZ,CAAC,CAACE,SAAS,CAACL,GAAG,IAAG;MACjB,IAAIA,GAAG,CAACC,UAAU,IAAI,CAAC,EAAE;QACvB,IAAID,GAAG,CAACE,OAAQ,CAACe,QAAQ,EAAE;UACzB,IAAI,CAACpC,eAAe,CAACqC,iBAAiB,CAAClB,GAAG,CAACE,OAAQ,CAACe,QAAQ,EAAE,MAAM,CAAC;QACvE;MACF;IACF,CAAC,CAAC;EACJ;EAEAE,0BAA0BA,CAAA;IACxB,IAAI,CAACvC,gBAAgB,CAACwC,+CAA+C,CAAC;MACpExB,IAAI,EAAE,IAAI,CAACO;KACZ,CAAC,CAACE,SAAS,CAACL,GAAG,IAAG;MACjB,IAAIA,GAAG,CAACC,UAAU,IAAI,CAAC,EAAE;QACvB,IAAID,GAAG,CAACE,OAAQ,CAACe,QAAQ,EAAE;UACzB,IAAI,CAACpC,eAAe,CAACqC,iBAAiB,CAAClB,GAAG,CAACE,OAAQ,CAACe,QAAQ,EAAE,MAAM,CAAC;QACvE;MACF;IACF,CAAC,CAAC;EACJ;EAEArK,MAAMA,CAACyK,GAAQ;IACb,IAAI,CAACtC,KAAK,GAAG,IAAI;IACjB,IAAI,CAAC5F,gBAAgB,GAAG,EAAE;IAC1B,IAAI,CAACqF,aAAa,CAAC8C,IAAI,CAACD,GAAG,CAAC;EAC9B;EACA9J,kBAAkBA,CAACgK,IAA6B,EAAEF,GAAQ;IACxD,IAAI,CAACtC,KAAK,GAAG,KAAK;IAClB,IAAI,CAAC5F,gBAAgB,GAAG;MAAE,GAAGoI;IAAI,CAAE;IACnC,IAAI,CAAC/C,aAAa,CAAC8C,IAAI,CAACD,GAAG,CAAC;EAC9B;EAEA1J,oBAAoBA,CAAC4J,IAA6B,EAAEF,GAAqB;IACvE,IAAI,CAAClI,gBAAgB,GAAG;MAAE,GAAGoI;IAAI,CAAE;IACnC,IAAI,CAACxF,UAAU,EAAE;IACjB,IAAI,CAACyC,aAAa,CAAC8C,IAAI,CAACD,GAAG,EAAE;MAAEG,oBAAoB,EAAE;IAAK,CAAE,CAAC;EAC/D;EAAGC,UAAUA,CAAA;IACX,IAAI,CAAC/C,KAAK,CAACgD,KAAK,EAAE;IAClB,IAAI,CAAChD,KAAK,CAACiD,QAAQ,CAAC,MAAM,EAAE,IAAI,CAACxI,gBAAgB,CAACvB,KAAK,CAAC;IACxD,IAAI,CAAC8G,KAAK,CAACiD,QAAQ,CAAC,MAAM,EAAE,IAAI,CAACxI,gBAAgB,CAACf,KAAK,CAAC;IACxD,IAAI,CAACsG,KAAK,CAACiD,QAAQ,CAAC,MAAM,EAAE,IAAI,CAACxI,gBAAgB,CAACd,SAAS,CAAC;IAC5D,IAAI,CAACqG,KAAK,CAACiD,QAAQ,CAAC,UAAU,EAAE,IAAI,CAACxI,gBAAgB,CAACX,WAAW,CAAC;IAClE;IACA;IACA;IACA;IACA,IAAI,CAACkG,KAAK,CAACkD,iBAAiB,CAAC,MAAM,EAAE,IAAI,CAACzI,gBAAgB,CAACvB,KAAK,EAAE,EAAE,CAAC;IACrE,IAAI,CAAC8G,KAAK,CAACkD,iBAAiB,CAAC,MAAM,EAAE,IAAI,CAACzI,gBAAgB,CAACf,KAAK,EAAE,EAAE,CAAC;IACrE,IAAI,CAACsG,KAAK,CAACkD,iBAAiB,CAAC,MAAM,EAAE,IAAI,CAACzI,gBAAgB,CAACd,SAAS,EAAE,EAAE,CAAC;IACzE,IAAI,CAACqG,KAAK,CAACkD,iBAAiB,CAAC,UAAU,EAAE,IAAI,CAACzI,gBAAgB,CAACX,WAAW,EAAE,EAAE,CAAC;IAC/E;IACA;IACA;IACA;EACF;EAEAsB,QAAQA,CAACuH,GAAQ;IACf,IAAI,CAACI,UAAU,EAAE;IACjB,IAAI,IAAI,CAAC/C,KAAK,CAACmD,aAAa,CAAChF,MAAM,GAAG,CAAC,EAAE;MACvC,IAAI,CAAC4B,OAAO,CAACqD,aAAa,CAAC,IAAI,CAACpD,KAAK,CAACmD,aAAa,CAAC;MACpD;IACF;IAAK,IAAI,CAACjD,gBAAgB,CAACmD,qCAAqC,CAAC;MAC/DnC,IAAI,EAAE;QACJY,YAAY,EAAE,IAAI,CAACL,mBAAmB;QACtC;QACAjI,UAAU,EAAE,IAAI,CAACiB,gBAAgB,CAACjB,UAAU;QAC5C;QACA;QACAN,KAAK,EAAE,IAAI,CAACuB,gBAAgB,CAACvB,KAAK;QAClCQ,KAAK,EAAE,IAAI,CAACe,gBAAgB,CAACf,KAAK;QAClCC,SAAS,EAAE,IAAI,CAACc,gBAAgB,CAACd,SAAS;QAC1CG,WAAW,EAAE,IAAI,CAACW,gBAAgB,CAACX,WAAW;QAC9CC,YAAY,EAAE,IAAI,CAACU,gBAAgB,CAACV,YAAY;QAChDuJ,WAAW,EAAE,IAAI,CAACjD,KAAK,GAAG,IAAI,GAAG,IAAI,CAAC5F,gBAAgB,CAAClB,GAAI;QAC3Dd,MAAM,EAAE,IAAI,CAACgC,gBAAgB,CAAChC;;KAEjC,CAAC,CACC4I,IAAI,CACHjL,GAAG,CAACkL,GAAG,IAAG;MACR,IAAIA,GAAG,CAACC,UAAU,KAAK,CAAC,EAAE;QACxB,IAAI,CAACxB,OAAO,CAACwD,aAAa,CAAC,MAAM,CAAC;MACpC,CAAC,MAAM;QACL,IAAI,CAACxD,OAAO,CAACyD,YAAY,CAAClC,GAAG,CAACmC,OAAQ,CAAC;MACzC;IACF,CAAC,CAAC,EACFtN,QAAQ,CAAC,MAAM,IAAI,CAACuL,eAAe,EAAE,CAAC,EACtCxL,QAAQ,CAAC,MAAMyM,GAAG,CAACxD,KAAK,EAAE,CAAC,CAC5B,CAACwC,SAAS,EAAE;EACjB;EAEAzG,OAAOA,CAACyH,GAAQ;IACdA,GAAG,CAACxD,KAAK,EAAE;EACb;EAEAuE,eAAeA,CAACC,KAAU;IACxB,MAAMC,MAAM,GAAgCD,KAAK,CAACC,MAAO;IACzD,MAAMC,MAAM,GAAe,IAAIC,UAAU,EAAE;IAC3CD,MAAM,CAACE,kBAAkB,CAACH,MAAM,CAACI,KAAK,CAAC,CAAC,CAAC,CAAC;IAC1CH,MAAM,CAACI,MAAM,GAAIC,CAAM,IAAI;MACzB,MAAMC,SAAS,GAAWD,CAAC,CAACN,MAAM,CAACQ,MAAM;MACzC,MAAMC,EAAE,GAAkBhO,IAAI,CAACiO,IAAI,CAACH,SAAS,EAAE;QAAEI,IAAI,EAAE;MAAQ,CAAE,CAAC;MAElE,MAAMC,MAAM,GAAWH,EAAE,CAACI,UAAU,CAAC,CAAC,CAAC;MACvC,MAAMC,EAAE,GAAmBL,EAAE,CAACM,MAAM,CAACH,MAAM,CAAC;MAE5C,IAAII,WAAW,GAAY,IAAI;MAC/B,MAAM/B,IAAI,GAAGxM,IAAI,CAACwO,KAAK,CAACC,aAAa,CAACJ,EAAE,CAAC;MACzC,IAAI7B,IAAI,IAAIA,IAAI,CAAC1E,MAAM,GAAG,CAAC,EAAE;QAC3B0E,IAAI,CAACkC,OAAO,CAAEC,CAAM,IAAI;UACtB,IAAI,EAAEA,CAAC,CAAC,IAAI,CAAC,IAAIA,CAAC,CAAC,IAAI,CAAC,IAAIA,CAAC,CAAC,IAAI,CAAC,IAAIA,CAAC,CAAC,QAAQ,CAAC,KAC/CA,CAAC,CAAC,QAAQ,CAAC,IAAIA,CAAC,CAAC,MAAM,CAAC,IAAIA,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE;YAC5CJ,WAAW,GAAG,KAAK;UACrB;QACF,CAAC,CAAC;QAEF,IAAI,CAACA,WAAW,EAAE;UAChB,IAAI,CAAC7E,OAAO,CAACyD,YAAY,CAAC,WAAW,CAAC;QACxC,CAAC,MAAM;UACL,IAAI,CAACtD,gBAAgB,CAAC+E,2CAA2C,CAAC;YAChE/D,IAAI,EAAE;cACJY,YAAY,EAAE,IAAI,CAACL,mBAAmB;cACtCyD,KAAK,EAAEtB,MAAM,CAACI,KAAK,CAAC,CAAC;;WAExB,CAAC,CAAC3C,IAAI,CACLjL,GAAG,CAACkL,GAAG,IAAG;YACR,IAAIA,GAAG,CAACC,UAAU,IAAI,CAAC,EAAE;cACvB,IAAI,CAACxB,OAAO,CAACwD,aAAa,CAAC,MAAM,CAAC;YACpC,CAAC,MAAM;cACL,IAAI,CAACxD,OAAO,CAACyD,YAAY,CAAClC,GAAG,CAACmC,OAAQ,CAAC;YACzC;UACF,CAAC,CAAC,EACFtN,QAAQ,CAAC,MAAM,IAAI,CAACuL,eAAe,CAAC,CAAC,CAAC,CAAC,CACxC,CAACC,SAAS,EAAE;QACf;MACF,CAAC,MAAM;QACL,IAAI,CAAC5B,OAAO,CAACyD,YAAY,CAAC,uBAAuB,CAAC;MACpD;MACAG,KAAK,CAACC,MAAM,CAACrI,KAAK,GAAG,IAAI;IAC3B,CAAC;EACH;EAEA4J,SAASA,CAACC,QAAgB,EAAEC,MAAwB;IAClD,IAAI,CAAC5F,mBAAmB,GAAG2F,QAAQ;IACnC,IAAI,CAACtF,aAAa,CAAC8C,IAAI,CAACyC,MAAM,CAAC;EACjC;EACAC,YAAYA,CAAA;IACV,IAAI,IAAI,CAAC3E,aAAa,EAAE;MACtB,IAAI,CAAC9G,UAAU,GAAG,KAAK;MACvB,IAAI,CAAC6H,eAAe,EAAE,CAACC,SAAS,EAAE;IACpC,CAAC,MACI;MACH,IAAI,CAAC9H,UAAU,GAAG,IAAI;MACtB,IAAI,CAAC6H,eAAe,EAAE,CAACC,SAAS,EAAE;IACpC;EACF;EAEA;EACA4D,eAAeA,CAAC5C,GAAqB;IACnC,IAAI,CAACtF,UAAU,EAAE;IACjB,IAAI,CAACyC,aAAa,CAAC8C,IAAI,CAACD,GAAG,EAAE;MAAEG,oBAAoB,EAAE;IAAK,CAAE,CAAC;EAC/D;EAAEzF,UAAUA,CAAA;IACV;IACA,IAAI,IAAI,CAACyD,kBAAkB,IAAI,IAAI,CAACW,mBAAmB,EAAE;MACvD,IAAI,CAACrB,eAAe,CAACoF,kCAAkC,CAAC;QACtDtE,IAAI,EAAE;UACJY,YAAY,EAAE,IAAI,CAACL,mBAAmB;UACtCgE,YAAY,EAAE,IAAI,CAAC7I,gBAAgB;UACnCsF,SAAS,EAAE,CAAC;UACZF,QAAQ,EAAE,IAAI,CAAC;;OAElB,CAAC,CAACL,SAAS,CAACL,GAAG,IAAG;QACjB,IAAIA,GAAG,CAACC,UAAU,KAAK,CAAC,EAAE;UACxB;UACA,IAAI,CAAClD,eAAe,GAAGiD,GAAG,CAACE,OAAO,EAAEkE,GAAG,CAACC,OAAO,KAAK;YAClDC,EAAE,EAAED,OAAO,CAACpM,GAAG,EAAEsM,QAAQ,EAAE,IAAI,EAAE;YACjCjK,IAAI,EAAE+J,OAAO,CAACG,YAAY,IAAIH,OAAO,CAACzM,KAAK,IAAI,EAAE;YACjDuD,IAAI,EAAE,CAAC;YAAE;YACTf,YAAY,EAAEiK,OAAO,CAACI,OAAO,GAAG,0BAA0BJ,OAAO,CAACI,OAAO,EAAE,GAAG,EAAE;YAChFxH,OAAO,EAAEoH,OAAO,CAACI,OAAO,GAAG,0BAA0BJ,OAAO,CAACI,OAAO,EAAE,GAAG,EAAE;YAC3EC,YAAY,EAAEL,OAAO,CAACM,SAAS,GAAG,IAAIC,IAAI,CAACP,OAAO,CAACM,SAAS,CAAC,GAAG,IAAIC,IAAI;WACzE,CAAC,CAAC,IAAI,EAAE;UACT,IAAI,CAAC9H,cAAc,GAAG,CAAC,GAAG,IAAI,CAACC,eAAe,CAAC;QACjD,CAAC,MAAM;UACL,IAAI,CAAC0B,OAAO,CAACyD,YAAY,CAAClC,GAAG,CAACmC,OAAO,IAAI,QAAQ,CAAC;UAClD,IAAI,CAACpF,eAAe,GAAG,EAAE;UACzB,IAAI,CAACD,cAAc,GAAG,EAAE;QAC1B;MACF,CAAC,CAAC;IACJ,CAAC,MAAM;MACL;MACA,IAAI,CAACC,eAAe,GAAG,EAAE;MACzB,IAAI,CAACD,cAAc,GAAG,EAAE;IAC1B;EACF;EAEAjB,YAAYA,CAAA;IACV,IAAI,CAAC,IAAI,CAACF,eAAe,CAACkJ,IAAI,EAAE,EAAE;MAChC,IAAI,CAAC/H,cAAc,GAAG,CAAC,GAAG,IAAI,CAACC,eAAe,CAAC;IACjD,CAAC,MAAM;MACL,MAAM+H,UAAU,GAAG,IAAI,CAACnJ,eAAe,CAACoJ,WAAW,EAAE;MACrD,IAAI,CAACjI,cAAc,GAAG,IAAI,CAACC,eAAe,CAACiI,MAAM,CAACC,KAAK,IACrDA,KAAK,CAAC3K,IAAI,CAACyK,WAAW,EAAE,CAACG,QAAQ,CAACJ,UAAU,CAAC,CAC9C;IACH;EACF;EAEArK,oBAAoBA,CAACwK,KAAgB;IACnC,MAAME,KAAK,GAAG,IAAI,CAACvI,cAAc,CAACwI,SAAS,CAACC,QAAQ,IAAIA,QAAQ,CAACf,EAAE,KAAKW,KAAK,CAACX,EAAE,CAAC;IACjF,IAAIa,KAAK,GAAG,CAAC,CAAC,EAAE;MACd,IAAI,CAACvI,cAAc,CAAC0I,MAAM,CAACH,KAAK,EAAE,CAAC,CAAC;IACtC,CAAC,MAAM;MACL,IAAI,CAACvI,cAAc,CAAC2I,IAAI,CAACN,KAAK,CAAC;IACjC;EACF;EAEAhK,eAAeA,CAACgK,KAAgB;IAC9B,OAAO,IAAI,CAACrI,cAAc,CAAC4I,IAAI,CAACH,QAAQ,IAAIA,QAAQ,CAACf,EAAE,KAAKW,KAAK,CAACX,EAAE,CAAC;EACvE;EAEArI,eAAeA,CAAA;IACb,IAAI,CAACW,cAAc,GAAG,CAAC,GAAG,IAAI,CAACE,cAAc,CAAC;EAChD;EAEAX,iBAAiBA,CAAA;IACf,IAAI,CAACS,cAAc,GAAG,EAAE;EAC1B;EAAE/B,YAAYA,CAACoK,KAAgB,EAAEQ,eAAiC,EAAEpD,KAAY;IAC9EA,KAAK,CAACqD,eAAe,EAAE;IACvB,IAAI,CAAC1I,eAAe,GAAGiI,KAAK;IAC5B,IAAI,CAACnH,mBAAmB,GAAG,IAAI,CAAChB,cAAc,CAACsI,SAAS,CAACO,GAAG,IAAIA,GAAG,CAACrB,EAAE,KAAKW,KAAK,CAACX,EAAE,CAAC;IACpF,IAAI,CAAC9F,aAAa,CAAC8C,IAAI,CAACmE,eAAe,CAAC;EAC1C;EAEArI,aAAaA,CAAA;IACX,IAAI,IAAI,CAACU,mBAAmB,GAAG,CAAC,EAAE;MAChC,IAAI,CAACA,mBAAmB,EAAE;MAC1B,IAAI,CAACd,eAAe,GAAG,IAAI,CAACF,cAAc,CAAC,IAAI,CAACgB,mBAAmB,CAAC;IACtE;EACF;EAEAR,SAASA,CAAA;IACP,IAAI,IAAI,CAACQ,mBAAmB,GAAG,IAAI,CAAChB,cAAc,CAACD,MAAM,GAAG,CAAC,EAAE;MAC7D,IAAI,CAACiB,mBAAmB,EAAE;MAC1B,IAAI,CAACd,eAAe,GAAG,IAAI,CAACF,cAAc,CAAC,IAAI,CAACgB,mBAAmB,CAAC;IACtE;EACF;EAEAJ,6BAA6BA,CAAA;IAC3B,IAAI,IAAI,CAACV,eAAe,EAAE;MACxB,IAAI,CAACvC,oBAAoB,CAAC,IAAI,CAACuC,eAAe,CAAC;IACjD;EACF;EAAEN,uBAAuBA,CAAC2E,GAAQ;IAChC,IAAI,IAAI,CAACzE,cAAc,CAACC,MAAM,GAAG,CAAC,EAAE;MAClC;MACA,IAAI,IAAI,CAACD,cAAc,CAACC,MAAM,KAAK,CAAC,EAAE;QACpC,IAAI,CAAC1D,gBAAgB,CAACjB,UAAU,GAAG,IAAI,CAAC0E,cAAc,CAAC,CAAC,CAAC,CAACtC,IAAI;MAChE,CAAC,MAAM;QACL;QACA,MAAMsL,UAAU,GAAG,IAAI,CAAChJ,cAAc,CAACwH,GAAG,CAACuB,GAAG,IAAIA,GAAG,CAACrL,IAAI,CAAC,CAACuL,IAAI,CAAC,IAAI,CAAC;QACtE,IAAI,CAACpH,OAAO,CAACwD,aAAa,CAAC,OAAO,IAAI,CAACrF,cAAc,CAACC,MAAM,SAAS+I,UAAU,EAAE,CAAC;QAClF,IAAI,CAACzM,gBAAgB,CAACjB,UAAU,GAAG,IAAI,CAAC0E,cAAc,CAAC,CAAC,CAAC,CAACtC,IAAI;MAChE;MAEA;MACA,IAAI,IAAI,CAACnB,gBAAgB,CAAClB,GAAG,EAAE;QAC7B,IAAI,CAAC6N,gBAAgB,EAAE;MACzB;IACF;IAEA,IAAI,CAAC3J,iBAAiB,EAAE;IACxBkF,GAAG,CAACxD,KAAK,EAAE;EACb;EAEA;EACAiI,gBAAgBA,CAAA;IACd,IAAI,CAAClH,gBAAgB,CAACmD,qCAAqC,CAAC;MAC1DnC,IAAI,EAAE;QACJY,YAAY,EAAE,IAAI,CAACL,mBAAmB;QACtCjI,UAAU,EAAE,IAAI,CAACiB,gBAAgB,CAACjB,UAAU;QAC5CN,KAAK,EAAE,IAAI,CAACuB,gBAAgB,CAACvB,KAAK;QAClCQ,KAAK,EAAE,IAAI,CAACe,gBAAgB,CAACf,KAAK;QAClCC,SAAS,EAAE,IAAI,CAACc,gBAAgB,CAACd,SAAS;QAC1CG,WAAW,EAAE,IAAI,CAACW,gBAAgB,CAACX,WAAW;QAC9CC,YAAY,EAAE,IAAI,CAACU,gBAAgB,CAACV,YAAY;QAChDuJ,WAAW,EAAE,IAAI,CAAC7I,gBAAgB,CAAClB,GAAI;QACvCd,MAAM,EAAE,IAAI,CAACgC,gBAAgB,CAAChC;;KAEjC,CAAC,CACC4I,IAAI,CACHjL,GAAG,CAACkL,GAAG,IAAG;MACR,IAAIA,GAAG,CAACC,UAAU,KAAK,CAAC,EAAE;QACxB,IAAI,CAACxB,OAAO,CAACwD,aAAa,CAAC,WAAW,IAAI,CAAC9I,gBAAgB,CAACjB,UAAU,EAAE,CAAC;MAC3E,CAAC,MAAM;QACL,IAAI,CAACuG,OAAO,CAACyD,YAAY,CAAClC,GAAG,CAACmC,OAAQ,CAAC;MACzC;IACF,CAAC,CAAC,EACFtN,QAAQ,CAAC,MAAM,IAAI,CAACuL,eAAe,EAAE,CAAC,EACtCxL,QAAQ,CAAC,MAAK;MACZ;MACA,IAAI,CAACuE,gBAAgB,GAAG,EAAE;IAC5B,CAAC,CAAC,CACH,CAACkH,SAAS,EAAE;EACjB;EAEA7D,kBAAkBA,CAAC6E,GAAQ;IACzB,IAAI,CAAClF,iBAAiB,EAAE;IACxB,IAAI,CAACR,eAAe,GAAG,EAAE;IACzB0F,GAAG,CAACxD,KAAK,EAAE;EACb,CAAC,CAAE;EACHrC,eAAeA,CAACuK,QAAyB;IACvC,IAAI,CAACzK,gBAAgB,GAAGyK,QAAQ;IAChC,IAAI,CAACvG,kBAAkB,GAAG,IAAI;IAC9B;IACA,IAAI,IAAI,CAACW,mBAAmB,EAAE;MAC5B,IAAI,CAACpE,UAAU,EAAE;IACnB;EACF;EAEA;EACAiK,gBAAgBA,CAACD,QAAgB;IAC/B,MAAME,MAAM,GAAG,IAAI,CAACtJ,eAAe,CAACuJ,IAAI,CAACC,GAAG,IAAIA,GAAG,CAAClM,KAAK,KAAK8L,QAAQ,CAAC;IACvE,OAAOE,MAAM,GAAGA,MAAM,CAAC/L,KAAK,GAAG,MAAM;EACvC;;;uCA9bWmE,yBAAyB,EAAAnJ,EAAA,CAAAkR,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAApR,EAAA,CAAAkR,iBAAA,CAAAG,EAAA,CAAAC,eAAA,GAAAtR,EAAA,CAAAkR,iBAAA,CAAAK,EAAA,CAAAC,cAAA,GAAAxR,EAAA,CAAAkR,iBAAA,CAAAO,EAAA,CAAAC,gBAAA,GAAA1R,EAAA,CAAAkR,iBAAA,CAAAS,EAAA,CAAAC,gBAAA,GAAA5R,EAAA,CAAAkR,iBAAA,CAAAS,EAAA,CAAAE,eAAA,GAAA7R,EAAA,CAAAkR,iBAAA,CAAAY,EAAA,CAAAC,cAAA,GAAA/R,EAAA,CAAAkR,iBAAA,CAAAS,EAAA,CAAAK,cAAA;IAAA;EAAA;;;YAAzB7I,yBAAyB;MAAA8I,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAAnS,EAAA,CAAAoS,0BAAA,EAAApS,EAAA,CAAAqS,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,mCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;UCtCpC3S,EADF,CAAAC,cAAA,iBAA0B,qBACR;UACdD,EAAA,CAAAkB,SAAA,qBAAiC;UACnClB,EAAA,CAAAG,YAAA,EAAiB;UAEfH,EADF,CAAAC,cAAA,mBAAc,YACyB;UAACD,EAAA,CAAAE,MAAA,yJACtC;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAICH,EAHN,CAAAC,cAAA,aAA8B,aACN,aACqC,gBACF;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAC/DH,EAAA,CAAAC,cAAA,qBAA6E;UAAjDD,EAAA,CAAA4D,gBAAA,2BAAAiP,uEAAA/O,MAAA;YAAA9D,EAAA,CAAAY,aAAA,CAAAkS,GAAA;YAAA9S,EAAA,CAAAgE,kBAAA,CAAA4O,GAAA,CAAA3H,mBAAA,EAAAnH,MAAA,MAAA8O,GAAA,CAAA3H,mBAAA,GAAAnH,MAAA;YAAA,OAAA9D,EAAA,CAAAgB,WAAA,CAAA8C,MAAA;UAAA,EAAiC;UAC3D9D,EAAA,CAAA2C,UAAA,KAAAoQ,+CAAA,wBAA4E;UAKlF/S,EAFI,CAAAG,YAAA,EAAY,EACR,EACF;UAYFH,EAFY,CAAAC,cAAA,cAAsB,cACqB,iBACF;UAAAD,EAAA,CAAAE,MAAA,6CAAO;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACpEH,EAAA,CAAAC,cAAA,iBAAyF;UAAzCD,EAAA,CAAA4D,gBAAA,2BAAAoP,mEAAAlP,MAAA;YAAA9D,EAAA,CAAAY,aAAA,CAAAkS,GAAA;YAAA9S,EAAA,CAAAgE,kBAAA,CAAA4O,GAAA,CAAAtP,WAAA,EAAAQ,MAAA,MAAA8O,GAAA,CAAAtP,WAAA,GAAAQ,MAAA;YAAA,OAAA9D,EAAA,CAAAgB,WAAA,CAAA8C,MAAA;UAAA,EAAyB;UAE7E9D,EAFI,CAAAG,YAAA,EAAyF,EACrF,EACF;UAYFH,EAFJ,CAAAC,cAAA,eAAuB,eAC0B,uBAEjB;UAD+BD,EAAA,CAAA4D,gBAAA,2BAAAqP,yEAAAnP,MAAA;YAAA9D,EAAA,CAAAY,aAAA,CAAAkS,GAAA;YAAA9S,EAAA,CAAAgE,kBAAA,CAAA4O,GAAA,CAAAzI,aAAA,EAAArG,MAAA,MAAA8O,GAAA,CAAAzI,aAAA,GAAArG,MAAA;YAAA,OAAA9D,EAAA,CAAAgB,WAAA,CAAA8C,MAAA;UAAA,EAA2B;UACpF9D,EAAA,CAAAU,UAAA,oBAAAwS,kEAAA;YAAAlT,EAAA,CAAAY,aAAA,CAAAkS,GAAA;YAAA,OAAA9S,EAAA,CAAAgB,WAAA,CAAU4R,GAAA,CAAA9D,YAAA,EAAc;UAAA,EAAC;UACzB9O,EAAA,CAAAE,MAAA,gHACF;UAAAF,EAAA,CAAAG,YAAA,EAAc;UAOdH,EANA,CAAA2C,UAAA,KAAAwQ,4CAAA,qBAA8F,KAAAC,4CAAA,qBAEV,KAAAC,4CAAA,qBAEE,KAAAC,4CAAA,qBAEF;UACpFtT,EAAA,CAAAC,cAAA,oBAAqG;UAAnCD,EAAA,CAAAU,UAAA,oBAAA6S,4DAAAzP,MAAA;YAAA9D,EAAA,CAAAY,aAAA,CAAAkS,GAAA;YAAA,OAAA9S,EAAA,CAAAgB,WAAA,CAAU4R,GAAA,CAAA1F,eAAA,CAAApJ,MAAA,CAAuB;UAAA,EAAC;UAApG9D,EAAA,CAAAG,YAAA,EAAqG;UACrGH,EAAA,CAAAC,cAAA,kBAA4E;UAAvCD,EAAA,CAAAU,UAAA,mBAAA8S,4DAAA;YAAAxT,EAAA,CAAAY,aAAA,CAAAkS,GAAA;YAAA,OAAA9S,EAAA,CAAAgB,WAAA,CAAS4R,GAAA,CAAA3G,0BAAA,EAA4B;UAAA,EAAC;UAACjM,EAAA,CAAAE,MAAA,6CAAO;UAAAF,EAAA,CAAAkB,SAAA,aAC9C;UAG3ClB,EAH2C,CAAAG,YAAA,EAAS,EAC1C,EACF,EACF;UAIEH,EAHR,CAAAC,cAAA,eAAmC,iBAC+D,aAAe,cACtD,cACrB;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACrCH,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAE,MAAA,gCAAI;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACvCH,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAE,MAAA,gCAAI;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACvCH,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAE,MAAA,gCAAI;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACvCH,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAE,MAAA,gCAAI;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACvCH,EAAA,CAAA2C,UAAA,KAAA8Q,wCAAA,iBAAwD;UACxDzT,EAAA,CAAAC,cAAA,cAA0C;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAEhDF,EAFgD,CAAAG,YAAA,EAAK,EAC9C,EACC;UAAQH,EAAA,CAAA2C,UAAA,KAAA+Q,2CAAA,oBAA+D;UAmBrF1T,EAFI,CAAAG,YAAA,EAAQ,EACJ,EACO;UAEbH,EADF,CAAAC,cAAA,0BAAsD,0BAEf;UADqCD,EAA1D,CAAA4D,gBAAA,kCAAA+P,mFAAA7P,MAAA;YAAA9D,EAAA,CAAAY,aAAA,CAAAkS,GAAA;YAAA9S,EAAA,CAAAgE,kBAAA,CAAA4O,GAAA,CAAAjH,YAAA,EAAA7H,MAAA,MAAA8O,GAAA,CAAAjH,YAAA,GAAA7H,MAAA;YAAA,OAAA9D,EAAA,CAAAgB,WAAA,CAAA8C,MAAA;UAAA,EAAiC,4BAAA8P,6EAAA9P,MAAA;YAAA9D,EAAA,CAAAY,aAAA,CAAAkS,GAAA;YAAA9S,EAAA,CAAAgE,kBAAA,CAAA4O,GAAA,CAAAnH,QAAA,EAAA3H,MAAA,MAAA8O,GAAA,CAAAnH,QAAA,GAAA3H,MAAA;YAAA,OAAA9D,EAAA,CAAAgB,WAAA,CAAA8C,MAAA;UAAA,EAAwB,wBAAA+P,yEAAA/P,MAAA;YAAA9D,EAAA,CAAAY,aAAA,CAAAkS,GAAA;YAAA9S,EAAA,CAAAgE,kBAAA,CAAA4O,GAAA,CAAAxH,SAAA,EAAAtH,MAAA,MAAA8O,GAAA,CAAAxH,SAAA,GAAAtH,MAAA;YAAA,OAAA9D,EAAA,CAAAgB,WAAA,CAAA8C,MAAA;UAAA,EAAqB;UAC5F9D,EAAA,CAAAU,UAAA,wBAAAmT,yEAAA/P,MAAA;YAAA9D,EAAA,CAAAY,aAAA,CAAAkS,GAAA;YAAA,OAAA9S,EAAA,CAAAgB,WAAA,CAAc4R,GAAA,CAAA/G,WAAA,CAAA/H,MAAA,CAAmB;UAAA,EAAC;UAGxC9D,EAFI,CAAAG,YAAA,EAAiB,EACF,EACT;UA+MVH,EA7MA,CAAA2C,UAAA,KAAAmR,iDAAA,iCAAA9T,EAAA,CAAA+T,sBAAA,CAAoD,KAAAC,iDAAA,kCAAAhU,EAAA,CAAA+T,sBAAA,CA0DK,KAAAE,iDAAA,iCAAAjU,EAAA,CAAA+T,sBAAA,CAgHC,KAAAG,iDAAA,iCAAAlU,EAAA,CAAA+T,sBAAA,CAmCH;;;UArSjB/T,EAAA,CAAAO,SAAA,IAAiC;UAAjCP,EAAA,CAAA6E,gBAAA,YAAA+N,GAAA,CAAA3H,mBAAA,CAAiC;UAC1BjL,EAAA,CAAAO,SAAA,EAAiB;UAAjBP,EAAA,CAAAI,UAAA,YAAAwS,GAAA,CAAA9I,cAAA,CAAiB;UAkBJ9J,EAAA,CAAAO,SAAA,GAAyB;UAAzBP,EAAA,CAAA6E,gBAAA,YAAA+N,GAAA,CAAAtP,WAAA,CAAyB;UAcdtD,EAAA,CAAAO,SAAA,GAA2B;UAA3BP,EAAA,CAAA6E,gBAAA,YAAA+N,GAAA,CAAAzI,aAAA,CAA2B;UAI7EnK,EAAA,CAAAO,SAAA,GAAmB;UAAnBP,EAAA,CAAAI,UAAA,SAAAwS,GAAA,CAAAuB,aAAA,CAAmB;UAEnBnU,EAAA,CAAAO,SAAA,EAAY;UAAZP,EAAA,CAAAI,UAAA,SAAAwS,GAAA,CAAAnP,MAAA,CAAY;UAEZzD,EAAA,CAAAO,SAAA,EAAc;UAAdP,EAAA,CAAAI,UAAA,SAAAwS,GAAA,CAAAwB,QAAA,CAAc;UAEYpU,EAAA,CAAAO,SAAA,EAAmB;UAAnBP,EAAA,CAAAI,UAAA,SAAAwS,GAAA,CAAAyB,aAAA,CAAmB;UAerBrU,EAAA,CAAAO,SAAA,IAAuB;UAAvBP,EAAA,CAAAI,UAAA,SAAAwS,GAAA,CAAA1I,SAAA,SAAuB;UAGlClK,EAAA,CAAAO,SAAA,GAAqD;UAArDP,EAAA,CAAAI,UAAA,SAAAwS,GAAA,CAAAjP,YAAA,YAAAiP,GAAA,CAAAjP,YAAA,CAAAgE,MAAA,KAAqD;UAqBjE3H,EAAA,CAAAO,SAAA,GAAiC;UAAyBP,EAA1D,CAAA6E,gBAAA,mBAAA+N,GAAA,CAAAjH,YAAA,CAAiC,aAAAiH,GAAA,CAAAnH,QAAA,CAAwB,SAAAmH,GAAA,CAAAxH,SAAA,CAAqB;;;qBDzDtF3L,YAAY,EAAA6U,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EAAAF,EAAA,CAAAG,WAAA,EAAE3U,YAAY,EAAA4U,EAAA,CAAAC,oBAAA,EAAAD,EAAA,CAAAE,mBAAA,EAAAF,EAAA,CAAAG,eAAA,EAAAH,EAAA,CAAAI,kBAAA,EAAAJ,EAAA,CAAAK,OAAA,EAAA1D,EAAA,CAAA2D,eAAA,EAAA3D,EAAA,CAAA4D,mBAAA,EAAA5D,EAAA,CAAA6D,qBAAA,EAAA7D,EAAA,CAAA8D,qBAAA,EAAA9D,EAAA,CAAA+D,mBAAA,EAAA/D,EAAA,CAAAgE,gBAAA,EAAAhE,EAAA,CAAAiE,iBAAA,EAAAjE,EAAA,CAAAkE,iBAAA,EAAAC,EAAA,CAAAC,mBAAA,EAAAC,GAAA,CAAAC,mBAAA;MAAAC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}