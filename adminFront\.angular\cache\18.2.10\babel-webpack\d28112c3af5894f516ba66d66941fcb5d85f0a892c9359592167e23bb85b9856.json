{"ast": null, "code": ";\n(function (root, factory, undef) {\n  if (typeof exports === \"object\") {\n    // CommonJS\n    module.exports = exports = factory(require(\"./core\"), require(\"./cipher-core\"));\n  } else if (typeof define === \"function\" && define.amd) {\n    // AMD\n    define([\"./core\", \"./cipher-core\"], factory);\n  } else {\n    // Global (browser)\n    factory(root.CryptoJS);\n  }\n})(this, function (CryptoJS) {\n  /**\n   * ISO 10126 padding strategy.\n   */\n  CryptoJS.pad.Iso10126 = {\n    pad: function (data, blockSize) {\n      // Shortcut\n      var blockSizeBytes = blockSize * 4;\n\n      // Count padding bytes\n      var nPaddingBytes = blockSizeBytes - data.sigBytes % blockSizeBytes;\n\n      // Pad\n      data.concat(CryptoJS.lib.WordArray.random(nPaddingBytes - 1)).concat(CryptoJS.lib.WordArray.create([nPaddingBytes << 24], 1));\n    },\n    unpad: function (data) {\n      // Get number of padding bytes from last byte\n      var nPaddingBytes = data.words[data.sigBytes - 1 >>> 2] & 0xff;\n\n      // Remove padding\n      data.sigBytes -= nPaddingBytes;\n    }\n  };\n  return CryptoJS.pad.Iso10126;\n});", "map": {"version": 3, "names": ["root", "factory", "undef", "exports", "module", "require", "define", "amd", "CryptoJS", "pad", "Iso10126", "data", "blockSize", "blockSizeBytes", "nPaddingBytes", "sigBytes", "concat", "lib", "WordArray", "random", "create", "unpad", "words"], "sources": ["C:/Users/<USER>/Documents/jeansalechange/adminFront/node_modules/crypto-js/pad-iso10126.js"], "sourcesContent": [";(function (root, factory, undef) {\n\tif (typeof exports === \"object\") {\n\t\t// CommonJS\n\t\tmodule.exports = exports = factory(require(\"./core\"), require(\"./cipher-core\"));\n\t}\n\telse if (typeof define === \"function\" && define.amd) {\n\t\t// AMD\n\t\tdefine([\"./core\", \"./cipher-core\"], factory);\n\t}\n\telse {\n\t\t// Global (browser)\n\t\tfactory(root.CryptoJS);\n\t}\n}(this, function (CryptoJS) {\n\n\t/**\n\t * ISO 10126 padding strategy.\n\t */\n\tCryptoJS.pad.Iso10126 = {\n\t    pad: function (data, blockSize) {\n\t        // Shortcut\n\t        var blockSizeBytes = blockSize * 4;\n\n\t        // Count padding bytes\n\t        var nPaddingBytes = blockSizeBytes - data.sigBytes % blockSizeBytes;\n\n\t        // Pad\n\t        data.concat(CryptoJS.lib.WordArray.random(nPaddingBytes - 1)).\n\t             concat(CryptoJS.lib.WordArray.create([nPaddingBytes << 24], 1));\n\t    },\n\n\t    unpad: function (data) {\n\t        // Get number of padding bytes from last byte\n\t        var nPaddingBytes = data.words[(data.sigBytes - 1) >>> 2] & 0xff;\n\n\t        // Remove padding\n\t        data.sigBytes -= nPaddingBytes;\n\t    }\n\t};\n\n\n\treturn CryptoJS.pad.Iso10126;\n\n}));"], "mappings": "AAAA;AAAE,WAAUA,IAAI,EAAEC,OAAO,EAAEC,KAAK,EAAE;EACjC,IAAI,OAAOC,OAAO,KAAK,QAAQ,EAAE;IAChC;IACAC,MAAM,CAACD,OAAO,GAAGA,OAAO,GAAGF,OAAO,CAACI,OAAO,CAAC,QAAQ,CAAC,EAAEA,OAAO,CAAC,eAAe,CAAC,CAAC;EAChF,CAAC,MACI,IAAI,OAAOC,MAAM,KAAK,UAAU,IAAIA,MAAM,CAACC,GAAG,EAAE;IACpD;IACAD,MAAM,CAAC,CAAC,QAAQ,EAAE,eAAe,CAAC,EAAEL,OAAO,CAAC;EAC7C,CAAC,MACI;IACJ;IACAA,OAAO,CAACD,IAAI,CAACQ,QAAQ,CAAC;EACvB;AACD,CAAC,EAAC,IAAI,EAAE,UAAUA,QAAQ,EAAE;EAE3B;AACD;AACA;EACCA,QAAQ,CAACC,GAAG,CAACC,QAAQ,GAAG;IACpBD,GAAG,EAAE,SAAAA,CAAUE,IAAI,EAAEC,SAAS,EAAE;MAC5B;MACA,IAAIC,cAAc,GAAGD,SAAS,GAAG,CAAC;;MAElC;MACA,IAAIE,aAAa,GAAGD,cAAc,GAAGF,IAAI,CAACI,QAAQ,GAAGF,cAAc;;MAEnE;MACAF,IAAI,CAACK,MAAM,CAACR,QAAQ,CAACS,GAAG,CAACC,SAAS,CAACC,MAAM,CAACL,aAAa,GAAG,CAAC,CAAC,CAAC,CACxDE,MAAM,CAACR,QAAQ,CAACS,GAAG,CAACC,SAAS,CAACE,MAAM,CAAC,CAACN,aAAa,IAAI,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;IACxE,CAAC;IAEDO,KAAK,EAAE,SAAAA,CAAUV,IAAI,EAAE;MACnB;MACA,IAAIG,aAAa,GAAGH,IAAI,CAACW,KAAK,CAAEX,IAAI,CAACI,QAAQ,GAAG,CAAC,KAAM,CAAC,CAAC,GAAG,IAAI;;MAEhE;MACAJ,IAAI,CAACI,QAAQ,IAAID,aAAa;IAClC;EACJ,CAAC;EAGD,OAAON,QAAQ,CAACC,GAAG,CAACC,QAAQ;AAE7B,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}