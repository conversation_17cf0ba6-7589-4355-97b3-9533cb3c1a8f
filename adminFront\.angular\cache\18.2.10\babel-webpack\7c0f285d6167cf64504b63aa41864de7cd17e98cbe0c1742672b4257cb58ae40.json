{"ast": null, "code": "//! moment.js locale configuration\n//! locale : Malay [ms-my]\n//! note : DEPRECATED, the correct one is [ms]\n//! author : <PERSON><PERSON><PERSON> : https://github.com/weldan\n\n;\n(function (global, factory) {\n  typeof exports === 'object' && typeof module !== 'undefined' && typeof require === 'function' ? factory(require('../moment')) : typeof define === 'function' && define.amd ? define(['../moment'], factory) : factory(global.moment);\n})(this, function (moment) {\n  'use strict';\n\n  //! moment.js locale configuration\n  var msMy = moment.defineLocale('ms-my', {\n    months: 'Januari_Februari_Mac_April_Mei_Jun_Julai_Ogos_September_Oktober_November_Disember'.split('_'),\n    monthsShort: 'Jan_Feb_Mac_Apr_Mei_Jun_Jul_Ogs_Sep_Okt_Nov_Dis'.split('_'),\n    weekdays: 'Ahad_Isnin_<PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON>_Juma<PERSON>_Sabtu'.split('_'),\n    weekdaysShort: 'Ahd_Isn_Sel_Ra<PERSON>_Kha_Jum_Sab'.split('_'),\n    weekdaysMin: 'Ah_Is_Sl_Rb_Km_Jm_Sb'.split('_'),\n    longDateFormat: {\n      LT: 'HH.mm',\n      LTS: 'HH.mm.ss',\n      L: 'DD/MM/YYYY',\n      LL: 'D MMMM YYYY',\n      LLL: 'D MMMM YYYY [pukul] HH.mm',\n      LLLL: 'dddd, D MMMM YYYY [pukul] HH.mm'\n    },\n    meridiemParse: /pagi|tengahari|petang|malam/,\n    meridiemHour: function (hour, meridiem) {\n      if (hour === 12) {\n        hour = 0;\n      }\n      if (meridiem === 'pagi') {\n        return hour;\n      } else if (meridiem === 'tengahari') {\n        return hour >= 11 ? hour : hour + 12;\n      } else if (meridiem === 'petang' || meridiem === 'malam') {\n        return hour + 12;\n      }\n    },\n    meridiem: function (hours, minutes, isLower) {\n      if (hours < 11) {\n        return 'pagi';\n      } else if (hours < 15) {\n        return 'tengahari';\n      } else if (hours < 19) {\n        return 'petang';\n      } else {\n        return 'malam';\n      }\n    },\n    calendar: {\n      sameDay: '[Hari ini pukul] LT',\n      nextDay: '[Esok pukul] LT',\n      nextWeek: 'dddd [pukul] LT',\n      lastDay: '[Kelmarin pukul] LT',\n      lastWeek: 'dddd [lepas pukul] LT',\n      sameElse: 'L'\n    },\n    relativeTime: {\n      future: 'dalam %s',\n      past: '%s yang lepas',\n      s: 'beberapa saat',\n      ss: '%d saat',\n      m: 'seminit',\n      mm: '%d minit',\n      h: 'sejam',\n      hh: '%d jam',\n      d: 'sehari',\n      dd: '%d hari',\n      M: 'sebulan',\n      MM: '%d bulan',\n      y: 'setahun',\n      yy: '%d tahun'\n    },\n    week: {\n      dow: 1,\n      // Monday is the first day of the week.\n      doy: 7 // The week that contains Jan 7th is the first week of the year.\n    }\n  });\n  return msMy;\n});", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}