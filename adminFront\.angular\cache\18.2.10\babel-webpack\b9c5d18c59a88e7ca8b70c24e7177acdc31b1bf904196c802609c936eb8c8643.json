{"ast": null, "code": "//! moment.js locale configuration\n//! locale : Georgian [ka]\n//! author : <PERSON><PERSON><PERSON> : https://github.com/<PERSON><PERSON><PERSON><PERSON><PERSON>\n\n;\n(function (global, factory) {\n  typeof exports === 'object' && typeof module !== 'undefined' && typeof require === 'function' ? factory(require('../moment')) : typeof define === 'function' && define.amd ? define(['../moment'], factory) : factory(global.moment);\n})(this, function (moment) {\n  'use strict';\n\n  //! moment.js locale configuration\n  var ka = moment.defineLocale('ka', {\n    months: 'იანვარი_თებერვალი_მარტი_აპრილი_მაისი_ივნისი_ივლისი_აგვისტო_სექტემბერი_ოქტომბერი_ნოემბერი_დეკემბერი'.split('_'),\n    monthsShort: 'იან_თებ_მარ_აპრ_მაი_ივნ_ივლ_აგვ_სექ_ოქტ_ნოე_დეკ'.split('_'),\n    weekdays: {\n      standalone: 'კვირა_ორშაბათი_სამშაბათი_ოთხშაბათი_ხუთშაბათი_პარასკევი_შაბათი'.split('_'),\n      format: 'კვირას_ორშაბათს_სამშაბათს_ოთხშაბათს_ხუთშაბათს_პარასკევს_შაბათს'.split('_'),\n      isFormat: /(წინა|შემდეგ)/\n    },\n    weekdaysShort: 'კვი_ორშ_სამ_ოთხ_ხუთ_პარ_შაბ'.split('_'),\n    weekdaysMin: 'კვ_ორ_სა_ოთ_ხუ_პა_შა'.split('_'),\n    longDateFormat: {\n      LT: 'HH:mm',\n      LTS: 'HH:mm:ss',\n      L: 'DD/MM/YYYY',\n      LL: 'D MMMM YYYY',\n      LLL: 'D MMMM YYYY HH:mm',\n      LLLL: 'dddd, D MMMM YYYY HH:mm'\n    },\n    calendar: {\n      sameDay: '[დღეს] LT[-ზე]',\n      nextDay: '[ხვალ] LT[-ზე]',\n      lastDay: '[გუშინ] LT[-ზე]',\n      nextWeek: '[შემდეგ] dddd LT[-ზე]',\n      lastWeek: '[წინა] dddd LT-ზე',\n      sameElse: 'L'\n    },\n    relativeTime: {\n      future: function (s) {\n        return s.replace(/(წამ|წუთ|საათ|წელ|დღ|თვ)(ი|ე)/, function ($0, $1, $2) {\n          return $2 === 'ი' ? $1 + 'ში' : $1 + $2 + 'ში';\n        });\n      },\n      past: function (s) {\n        if (/(წამი|წუთი|საათი|დღე|თვე)/.test(s)) {\n          return s.replace(/(ი|ე)$/, 'ის წინ');\n        }\n        if (/წელი/.test(s)) {\n          return s.replace(/წელი$/, 'წლის წინ');\n        }\n        return s;\n      },\n      s: 'რამდენიმე წამი',\n      ss: '%d წამი',\n      m: 'წუთი',\n      mm: '%d წუთი',\n      h: 'საათი',\n      hh: '%d საათი',\n      d: 'დღე',\n      dd: '%d დღე',\n      M: 'თვე',\n      MM: '%d თვე',\n      y: 'წელი',\n      yy: '%d წელი'\n    },\n    dayOfMonthOrdinalParse: /0|1-ლი|მე-\\d{1,2}|\\d{1,2}-ე/,\n    ordinal: function (number) {\n      if (number === 0) {\n        return number;\n      }\n      if (number === 1) {\n        return number + '-ლი';\n      }\n      if (number < 20 || number <= 100 && number % 20 === 0 || number % 100 === 0) {\n        return 'მე-' + number;\n      }\n      return number + '-ე';\n    },\n    week: {\n      dow: 1,\n      doy: 7\n    }\n  });\n  return ka;\n});", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}