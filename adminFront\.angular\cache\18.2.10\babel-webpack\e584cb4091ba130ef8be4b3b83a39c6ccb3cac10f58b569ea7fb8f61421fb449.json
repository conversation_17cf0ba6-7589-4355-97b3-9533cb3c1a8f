{"ast": null, "code": "import { ProfitChartData } from '../data/profit-chart';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"./periods.service\";\nexport class ProfitChartService extends ProfitChartData {\n  constructor(period) {\n    super();\n    this.period = period;\n    this.year = ['2012', '2013', '2014', '2015', '2016', '2017', '2018'];\n    this.data = {};\n    this.data = {\n      week: this.getDataForWeekPeriod(),\n      month: this.getDataForMonthPeriod(),\n      year: this.getDataForYearPeriod()\n    };\n  }\n  getDataForWeekPeriod() {\n    const nPoint = this.period.getWeeks().length;\n    return {\n      chartLabel: this.period.getWeeks(),\n      data: [this.getRandomData(nPoint), this.getRandomData(nPoint), this.getRandomData(nPoint)]\n    };\n  }\n  getDataForMonthPeriod() {\n    const nPoint = this.period.getMonths().length;\n    return {\n      chartLabel: this.period.getMonths(),\n      data: [this.getRandomData(nPoint), this.getRandomData(nPoint), this.getRandomData(nPoint)]\n    };\n  }\n  getDataForYearPeriod() {\n    const nPoint = this.year.length;\n    return {\n      chartLabel: this.year,\n      data: [this.getRandomData(nPoint), this.getRandomData(nPoint), this.getRandomData(nPoint)]\n    };\n  }\n  getRandomData(nPoints) {\n    return Array.from(Array(nPoints)).map(() => {\n      return Math.round(Math.random() * 500);\n    });\n  }\n  getProfitChartData(period) {\n    return this.data[period];\n  }\n  static {\n    this.ɵfac = function ProfitChartService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || ProfitChartService)(i0.ɵɵinject(i1.PeriodsService));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: ProfitChartService,\n      factory: ProfitChartService.ɵfac\n    });\n  }\n}", "map": {"version": 3, "names": ["ProfitChartData", "ProfitChartService", "constructor", "period", "year", "data", "week", "getDataForWeekPeriod", "month", "getDataForMonthPeriod", "getDataForYearPeriod", "nPoint", "getWeeks", "length", "chartLabel", "getRandomData", "getMonths", "nPoints", "Array", "from", "map", "Math", "round", "random", "getProfitChartData", "i0", "ɵɵinject", "i1", "PeriodsService", "factory", "ɵfac"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\@core\\mock\\profit-chart.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport { PeriodsService } from './periods.service';\r\nimport { ProfitChart, ProfitChartData } from '../data/profit-chart';\r\n\r\n@Injectable()\r\nexport class ProfitChartService extends ProfitChartData {\r\n\r\n  private year = [\r\n    '2012',\r\n    '2013',\r\n    '2014',\r\n    '2015',\r\n    '2016',\r\n    '2017',\r\n    '2018',\r\n  ];\r\n\r\n  private data:any = { };\r\n\r\n  constructor(private period: PeriodsService) {\r\n    super();\r\n    this.data = {\r\n      week: this.getDataForWeekPeriod(),\r\n      month: this.getDataForMonthPeriod(),\r\n      year: this.getDataForYearPeriod(),\r\n    };\r\n  }\r\n\r\n  private getDataForWeekPeriod(): ProfitChart {\r\n    const nPoint = this.period.getWeeks().length;\r\n\r\n    return {\r\n      chartLabel: this.period.getWeeks(),\r\n      data: [\r\n        this.getRandomData(nPoint),\r\n        this.getRandomData(nPoint),\r\n        this.getRandomData(nPoint),\r\n      ],\r\n    };\r\n  }\r\n\r\n  private getDataForMonthPeriod(): ProfitChart {\r\n    const nPoint = this.period.getMonths().length;\r\n\r\n    return {\r\n      chartLabel: this.period.getMonths(),\r\n      data: [\r\n        this.getRandomData(nPoint),\r\n        this.getRandomData(nPoint),\r\n        this.getRandomData(nPoint),\r\n      ],\r\n    };\r\n  }\r\n\r\n  private getDataForYearPeriod(): ProfitChart {\r\n    const nPoint = this.year.length;\r\n\r\n    return {\r\n      chartLabel: this.year,\r\n      data: [\r\n        this.getRandomData(nPoint),\r\n        this.getRandomData(nPoint),\r\n        this.getRandomData(nPoint),\r\n      ],\r\n    };\r\n  }\r\n\r\n  private getRandomData(nPoints: number): number[] {\r\n    return Array.from(Array(nPoints)).map(() => {\r\n      return Math.round(Math.random() * 500);\r\n    });\r\n  }\r\n\r\n  getProfitChartData(period: string): ProfitChart {\r\n    return this.data[period];\r\n  }\r\n}\r\n"], "mappings": "AAEA,SAAsBA,eAAe,QAAQ,sBAAsB;;;AAGnE,OAAM,MAAOC,kBAAmB,SAAQD,eAAe;EAcrDE,YAAoBC,MAAsB;IACxC,KAAK,EAAE;IADW,KAAAA,MAAM,GAANA,MAAM;IAZlB,KAAAC,IAAI,GAAG,CACb,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,CACP;IAEO,KAAAC,IAAI,GAAO,EAAG;IAIpB,IAAI,CAACA,IAAI,GAAG;MACVC,IAAI,EAAE,IAAI,CAACC,oBAAoB,EAAE;MACjCC,KAAK,EAAE,IAAI,CAACC,qBAAqB,EAAE;MACnCL,IAAI,EAAE,IAAI,CAACM,oBAAoB;KAChC;EACH;EAEQH,oBAAoBA,CAAA;IAC1B,MAAMI,MAAM,GAAG,IAAI,CAACR,MAAM,CAACS,QAAQ,EAAE,CAACC,MAAM;IAE5C,OAAO;MACLC,UAAU,EAAE,IAAI,CAACX,MAAM,CAACS,QAAQ,EAAE;MAClCP,IAAI,EAAE,CACJ,IAAI,CAACU,aAAa,CAACJ,MAAM,CAAC,EAC1B,IAAI,CAACI,aAAa,CAACJ,MAAM,CAAC,EAC1B,IAAI,CAACI,aAAa,CAACJ,MAAM,CAAC;KAE7B;EACH;EAEQF,qBAAqBA,CAAA;IAC3B,MAAME,MAAM,GAAG,IAAI,CAACR,MAAM,CAACa,SAAS,EAAE,CAACH,MAAM;IAE7C,OAAO;MACLC,UAAU,EAAE,IAAI,CAACX,MAAM,CAACa,SAAS,EAAE;MACnCX,IAAI,EAAE,CACJ,IAAI,CAACU,aAAa,CAACJ,MAAM,CAAC,EAC1B,IAAI,CAACI,aAAa,CAACJ,MAAM,CAAC,EAC1B,IAAI,CAACI,aAAa,CAACJ,MAAM,CAAC;KAE7B;EACH;EAEQD,oBAAoBA,CAAA;IAC1B,MAAMC,MAAM,GAAG,IAAI,CAACP,IAAI,CAACS,MAAM;IAE/B,OAAO;MACLC,UAAU,EAAE,IAAI,CAACV,IAAI;MACrBC,IAAI,EAAE,CACJ,IAAI,CAACU,aAAa,CAACJ,MAAM,CAAC,EAC1B,IAAI,CAACI,aAAa,CAACJ,MAAM,CAAC,EAC1B,IAAI,CAACI,aAAa,CAACJ,MAAM,CAAC;KAE7B;EACH;EAEQI,aAAaA,CAACE,OAAe;IACnC,OAAOC,KAAK,CAACC,IAAI,CAACD,KAAK,CAACD,OAAO,CAAC,CAAC,CAACG,GAAG,CAAC,MAAK;MACzC,OAAOC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,EAAE,GAAG,GAAG,CAAC;IACxC,CAAC,CAAC;EACJ;EAEAC,kBAAkBA,CAACrB,MAAc;IAC/B,OAAO,IAAI,CAACE,IAAI,CAACF,MAAM,CAAC;EAC1B;;;uCAtEWF,kBAAkB,EAAAwB,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,cAAA;IAAA;EAAA;;;aAAlB3B,kBAAkB;MAAA4B,OAAA,EAAlB5B,kBAAkB,CAAA6B;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}