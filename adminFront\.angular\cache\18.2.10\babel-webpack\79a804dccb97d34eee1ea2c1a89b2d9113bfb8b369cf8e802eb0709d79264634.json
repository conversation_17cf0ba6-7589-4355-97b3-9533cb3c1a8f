{"ast": null, "code": "import { createPlugin } from '@fullcalendar/core/index.js';\nimport { DayTimeColsView } from './internal.js';\nimport '@fullcalendar/core/internal.js';\nimport '@fullcalendar/core/preact.js';\nimport '@fullcalendar/daygrid/internal.js';\nconst OPTION_REFINERS = {\n  allDaySlot: Boolean\n};\nvar index = createPlugin({\n  name: '@fullcalendar/timegrid',\n  initialView: 'timeGridWeek',\n  optionRefiners: OPTION_REFINERS,\n  views: {\n    timeGrid: {\n      component: DayTimeColsView,\n      usesMinMaxTime: true,\n      allDaySlot: true,\n      slotDuration: '00:30:00',\n      slotEventOverlap: true // a bad name. confused with overlap/constraint system\n    },\n    timeGridDay: {\n      type: 'timeGrid',\n      duration: {\n        days: 1\n      }\n    },\n    timeGridWeek: {\n      type: 'timeGrid',\n      duration: {\n        weeks: 1\n      }\n    }\n  }\n});\nexport { index as default };", "map": {"version": 3, "names": ["createPlugin", "DayTimeColsView", "OPTION_REFINERS", "allDaySlot", "Boolean", "index", "name", "initialView", "optionRefiners", "views", "timeGrid", "component", "usesMinMaxTime", "slotDuration", "slotEventOverlap", "timeGridDay", "type", "duration", "days", "timeGridWeek", "weeks", "default"], "sources": ["C:/Users/<USER>/Documents/jeansalechange/adminFront/node_modules/@fullcalendar/timegrid/index.js"], "sourcesContent": ["import { createPlugin } from '@fullcalendar/core/index.js';\nimport { DayTimeColsView } from './internal.js';\nimport '@fullcalendar/core/internal.js';\nimport '@fullcalendar/core/preact.js';\nimport '@fullcalendar/daygrid/internal.js';\n\nconst OPTION_REFINERS = {\n    allDaySlot: Boolean,\n};\n\nvar index = createPlugin({\n    name: '@fullcalendar/timegrid',\n    initialView: 'timeGridWeek',\n    optionRefiners: OPTION_REFINERS,\n    views: {\n        timeGrid: {\n            component: DayTimeColsView,\n            usesMinMaxTime: true,\n            allDaySlot: true,\n            slotDuration: '00:30:00',\n            slotEventOverlap: true, // a bad name. confused with overlap/constraint system\n        },\n        timeGridDay: {\n            type: 'timeGrid',\n            duration: { days: 1 },\n        },\n        timeGridWeek: {\n            type: 'timeGrid',\n            duration: { weeks: 1 },\n        },\n    },\n});\n\nexport { index as default };\n"], "mappings": "AAAA,SAASA,YAAY,QAAQ,6BAA6B;AAC1D,SAASC,eAAe,QAAQ,eAAe;AAC/C,OAAO,gCAAgC;AACvC,OAAO,8BAA8B;AACrC,OAAO,mCAAmC;AAE1C,MAAMC,eAAe,GAAG;EACpBC,UAAU,EAAEC;AAChB,CAAC;AAED,IAAIC,KAAK,GAAGL,YAAY,CAAC;EACrBM,IAAI,EAAE,wBAAwB;EAC9BC,WAAW,EAAE,cAAc;EAC3BC,cAAc,EAAEN,eAAe;EAC/BO,KAAK,EAAE;IACHC,QAAQ,EAAE;MACNC,SAAS,EAAEV,eAAe;MAC1BW,cAAc,EAAE,IAAI;MACpBT,UAAU,EAAE,IAAI;MAChBU,YAAY,EAAE,UAAU;MACxBC,gBAAgB,EAAE,IAAI,CAAE;IAC5B,CAAC;IACDC,WAAW,EAAE;MACTC,IAAI,EAAE,UAAU;MAChBC,QAAQ,EAAE;QAAEC,IAAI,EAAE;MAAE;IACxB,CAAC;IACDC,YAAY,EAAE;MACVH,IAAI,EAAE,UAAU;MAChBC,QAAQ,EAAE;QAAEG,KAAK,EAAE;MAAE;IACzB;EACJ;AACJ,CAAC,CAAC;AAEF,SAASf,KAAK,IAAIgB,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}