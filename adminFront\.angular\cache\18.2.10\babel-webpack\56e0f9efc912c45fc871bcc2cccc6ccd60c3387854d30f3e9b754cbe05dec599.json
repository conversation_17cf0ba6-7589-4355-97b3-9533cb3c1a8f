{"ast": null, "code": "import * as i0 from \"@angular/core\";\nexport let StatusPipe = /*#__PURE__*/(() => {\n  class StatusPipe {\n    transform(value) {\n      if (value === 0) {\n        return '停用';\n      } else if (value === 1) {\n        return '啟用';\n      } else if (value === 9) {\n        return '刪除';\n      }\n    }\n    static {\n      this.ɵfac = function StatusPipe_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || StatusPipe)();\n      };\n    }\n    static {\n      this.ɵpipe = /*@__PURE__*/i0.ɵɵdefinePipe({\n        name: \"getStatusName\",\n        type: StatusPipe,\n        pure: true,\n        standalone: true\n      });\n    }\n  }\n  return StatusPipe;\n})();\nexport let DocumentStatusPipe = /*#__PURE__*/(() => {\n  class DocumentStatusPipe {\n    transform(value) {\n      if (value === 1) {\n        return '待審核'; //'To be reviewed'\n      } else if (value === 2) {\n        return '已駁回'; //'Rejected'\n      } else if (value === 3) {\n        return '待客戶簽回'; //'To be signed back by the customer'\n      } else if (value === 4) {\n        return '客戶已簽回'; //'The customer has signed back'\n      } else return '';\n    }\n    static {\n      this.ɵfac = function DocumentStatusPipe_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || DocumentStatusPipe)();\n      };\n    }\n    static {\n      this.ɵpipe = /*@__PURE__*/i0.ɵɵdefinePipe({\n        name: \"getDocumentStatus\",\n        type: DocumentStatusPipe,\n        pure: true,\n        standalone: true\n      });\n    }\n  }\n  return DocumentStatusPipe;\n})();\nexport let StatusMailPipe = /*#__PURE__*/(() => {\n  class StatusMailPipe {\n    transform(value) {\n      if (value === 0) {\n        return '停用';\n      } else if (value === 1) {\n        return '啟用';\n      }\n    }\n    static {\n      this.ɵfac = function StatusMailPipe_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || StatusMailPipe)();\n      };\n    }\n    static {\n      this.ɵpipe = /*@__PURE__*/i0.ɵɵdefinePipe({\n        name: \"getStatusMailName\",\n        type: StatusMailPipe,\n        pure: true,\n        standalone: true\n      });\n    }\n  }\n  return StatusMailPipe;\n})();\nexport let TypeMailPipe = /*#__PURE__*/(() => {\n  class TypeMailPipe {\n    transform(value) {\n      if (value === 1) {\n        return '簽署完成';\n      } else if (value === 2) {\n        return '已預約客變';\n      } else {\n        return '全部';\n      }\n    }\n    static {\n      this.ɵfac = function TypeMailPipe_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || TypeMailPipe)();\n      };\n    }\n    static {\n      this.ɵpipe = /*@__PURE__*/i0.ɵɵdefinePipe({\n        name: \"getTypeMailName\",\n        type: TypeMailPipe,\n        pure: true,\n        standalone: true\n      });\n    }\n  }\n  return TypeMailPipe;\n})();\nexport let DefaultKeyPipe = /*#__PURE__*/(() => {\n  class DefaultKeyPipe {\n    transform(value) {\n      if (value) {\n        return '是';\n      } else {\n        return '否';\n      }\n    }\n    static {\n      this.ɵfac = function DefaultKeyPipe_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || DefaultKeyPipe)();\n      };\n    }\n    static {\n      this.ɵpipe = /*@__PURE__*/i0.ɵɵdefinePipe({\n        name: \"getDefaultKeyName\",\n        type: DefaultKeyPipe,\n        pure: true,\n        standalone: true\n      });\n    }\n  }\n  return DefaultKeyPipe;\n})();\nexport let ApprovalWaitingPipe = /*#__PURE__*/(() => {\n  class ApprovalWaitingPipe {\n    transform(value) {\n      if (value == 1) {\n        return '客變圖上傳';\n      }\n      if (value == 2) {\n        return '確認客變圖';\n      }\n      if (value == 3) {\n        return '相關文件';\n      }\n      if (value == 4) {\n        return '客變原則';\n      }\n      if (value == 5) {\n        return '審閱文件';\n      }\n    }\n    static {\n      this.ɵfac = function ApprovalWaitingPipe_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || ApprovalWaitingPipe)();\n      };\n    }\n    static {\n      this.ɵpipe = /*@__PURE__*/i0.ɵɵdefinePipe({\n        name: \"getTypeApprovalWaiting\",\n        type: ApprovalWaitingPipe,\n        pure: true,\n        standalone: true\n      });\n    }\n  }\n  return ApprovalWaitingPipe;\n})();\nexport let TaskStatusPipe = /*#__PURE__*/(() => {\n  class TaskStatusPipe {\n    transform(value) {\n      if (value === 0) {\n        return '處理完成';\n      } else if (value === 1) {\n        return '等待中';\n      } else if (value === 2) {\n        return '處理中';\n      } else if (value === 3) {\n        return '資料異常';\n      }\n    }\n    static {\n      this.ɵfac = function TaskStatusPipe_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || TaskStatusPipe)();\n      };\n    }\n    static {\n      this.ɵpipe = /*@__PURE__*/i0.ɵɵdefinePipe({\n        name: \"getTaskStatusName\",\n        type: TaskStatusPipe,\n        pure: true,\n        standalone: true\n      });\n    }\n  }\n  return TaskStatusPipe;\n})();\nexport let TaskLogStatusPipe = /*#__PURE__*/(() => {\n  class TaskLogStatusPipe {\n    transform(value) {\n      if (value === 0) {\n        return '未處理';\n      } else if (value === 1) {\n        return '已處理';\n      }\n    }\n    static {\n      this.ɵfac = function TaskLogStatusPipe_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || TaskLogStatusPipe)();\n      };\n    }\n    static {\n      this.ɵpipe = /*@__PURE__*/i0.ɵɵdefinePipe({\n        name: \"getTaskLogStatusName\",\n        type: TaskLogStatusPipe,\n        pure: true,\n        standalone: true\n      });\n    }\n  }\n  return TaskLogStatusPipe;\n})();\nexport let PlanUsePipe = /*#__PURE__*/(() => {\n  class PlanUsePipe {\n    transform(value) {\n      if (value === true) {\n        return '選樣';\n      } else if (value === false) {\n        return '方案';\n      }\n    }\n    static {\n      this.ɵfac = function PlanUsePipe_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || PlanUsePipe)();\n      };\n    }\n    static {\n      this.ɵpipe = /*@__PURE__*/i0.ɵɵdefinePipe({\n        name: \"getPlanUse\",\n        type: PlanUsePipe,\n        pure: true,\n        standalone: true\n      });\n    }\n  }\n  return PlanUsePipe;\n})();\nexport let SettingTimeStatusPipe = /*#__PURE__*/(() => {\n  class SettingTimeStatusPipe {\n    transform(cChangeStartDate, cChangeEndDate, isStatus) {\n      const now = new Date();\n      // Convert datetime string to Date\n      const startDate = cChangeStartDate ? new Date(cChangeStartDate) : null;\n      const endDate = cChangeEndDate ? new Date(cChangeEndDate) : null;\n      if (isStatus && cChangeStartDate && cChangeEndDate) {\n        return `${cChangeStartDate.split(\"T\")[0]} ~ ${cChangeEndDate.split(\"T\")[0]}`;\n      }\n      if (!startDate && !endDate) {\n        return '未設定';\n      }\n      if (startDate && endDate && startDate <= now && now <= endDate) {\n        return '已開放';\n      }\n      if (endDate && endDate < now) {\n        return '已結束';\n      }\n      if (startDate && startDate > now) {\n        return '未開放';\n      }\n      return '__'; // Unknown case\n    }\n    static {\n      this.ɵfac = function SettingTimeStatusPipe_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || SettingTimeStatusPipe)();\n      };\n    }\n    static {\n      this.ɵpipe = /*@__PURE__*/i0.ɵɵdefinePipe({\n        name: \"getSettingTimeStatus\",\n        type: SettingTimeStatusPipe,\n        pure: true,\n        standalone: true\n      });\n    }\n  }\n  return SettingTimeStatusPipe;\n})();\nexport let cApproveStatusPipe = /*#__PURE__*/(() => {\n  class cApproveStatusPipe {\n    transform(value) {\n      if (value === 0) {\n        return '待審核';\n      }\n      if (value === 1) {\n        return '審核通過';\n      }\n      if (value === 2) {\n        return '退回';\n      }\n    }\n    static {\n      this.ɵfac = function cApproveStatusPipe_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || cApproveStatusPipe)();\n      };\n    }\n    static {\n      this.ɵpipe = /*@__PURE__*/i0.ɵɵdefinePipe({\n        name: \"cApproveStatus\",\n        type: cApproveStatusPipe,\n        pure: true,\n        standalone: true\n      });\n    }\n  }\n  return cApproveStatusPipe;\n})();\nexport let LabelInOptionsPipe = /*#__PURE__*/(() => {\n  class LabelInOptionsPipe {\n    transform(status, statusOptions) {\n      if (status === null) {\n        return '';\n      }\n      const matchingOption = statusOptions.find(option => option.value === status);\n      return matchingOption ? matchingOption.label : '';\n    }\n    static {\n      this.ɵfac = function LabelInOptionsPipe_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || LabelInOptionsPipe)();\n      };\n    }\n    static {\n      this.ɵpipe = /*@__PURE__*/i0.ɵɵdefinePipe({\n        name: \"getLabelInOptions\",\n        type: LabelInOptionsPipe,\n        pure: true,\n        standalone: true\n      });\n    }\n  }\n  return LabelInOptionsPipe;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}