{"ast": null, "code": "import { map } from 'rxjs/operators';\nimport { BaseService } from '../base-service';\nimport { apiFileGetFileGet } from '../fn/file/api-file-get-file-get';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../api-configuration\";\nimport * as i2 from \"@angular/common/http\";\nexport class FileService extends BaseService {\n  constructor(config, http) {\n    super(config, http);\n  }\n  /** Path part for operation `apiFileGetFileGet()` */\n  static {\n    this.ApiFileGetFileGetPath = '/api/File/GetFile';\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiFileGetFileGet()` instead.\n   *\n   * This method doesn't expect any request body.\n   */\n  apiFileGetFileGet$Response(params, context) {\n    return apiFileGetFileGet(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiFileGetFileGet$Response()` instead.\n   *\n   * This method doesn't expect any request body.\n   */\n  apiFileGetFileGet(params, context) {\n    return this.apiFileGetFileGet$Response(params, context).pipe(map(r => r.body));\n  }\n  static {\n    this.ɵfac = function FileService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || FileService)(i0.ɵɵinject(i1.ApiConfiguration), i0.ɵɵinject(i2.HttpClient));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: FileService,\n      factory: FileService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["map", "BaseService", "apiFileGetFileGet", "FileService", "constructor", "config", "http", "ApiFileGetFileGetPath", "apiFileGetFileGet$Response", "params", "context", "rootUrl", "pipe", "r", "body", "i0", "ɵɵinject", "i1", "ApiConfiguration", "i2", "HttpClient", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\services\\api\\services\\file.service.ts"], "sourcesContent": ["/* tslint:disable */\r\n/* eslint-disable */\r\n/* Code generated by ng-openapi-gen DO NOT EDIT. */\r\n\r\nimport { HttpClient, HttpContext } from '@angular/common/http';\r\nimport { Injectable } from '@angular/core';\r\nimport { Observable } from 'rxjs';\r\nimport { map } from 'rxjs/operators';\r\n\r\nimport { BaseService } from '../base-service';\r\nimport { ApiConfiguration } from '../api-configuration';\r\nimport { StrictHttpResponse } from '../strict-http-response';\r\n\r\nimport { apiFileGetFileGet } from '../fn/file/api-file-get-file-get';\r\nimport { ApiFileGetFileGet$Params } from '../fn/file/api-file-get-file-get';\r\n\r\n@Injectable({ providedIn: 'root' })\r\nexport class FileService extends BaseService {\r\n  constructor(config: ApiConfiguration, http: HttpClient) {\r\n    super(config, http);\r\n  }\r\n\r\n  /** Path part for operation `apiFileGetFileGet()` */\r\n  static readonly ApiFileGetFileGetPath = '/api/File/GetFile';\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiFileGetFileGet()` instead.\r\n   *\r\n   * This method doesn't expect any request body.\r\n   */\r\n  apiFileGetFileGet$Response(params?: ApiFileGetFileGet$Params, context?: HttpContext): Observable<StrictHttpResponse<void>> {\r\n    return apiFileGetFileGet(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiFileGetFileGet$Response()` instead.\r\n   *\r\n   * This method doesn't expect any request body.\r\n   */\r\n  apiFileGetFileGet(params?: ApiFileGetFileGet$Params, context?: HttpContext): Observable<void> {\r\n    return this.apiFileGetFileGet$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<void>): void => r.body)\r\n    );\r\n  }\r\n\r\n}\r\n"], "mappings": "AAOA,SAASA,GAAG,QAAQ,gBAAgB;AAEpC,SAASC,WAAW,QAAQ,iBAAiB;AAI7C,SAASC,iBAAiB,QAAQ,kCAAkC;;;;AAIpE,OAAM,MAAOC,WAAY,SAAQF,WAAW;EAC1CG,YAAYC,MAAwB,EAAEC,IAAgB;IACpD,KAAK,CAACD,MAAM,EAAEC,IAAI,CAAC;EACrB;EAEA;;IACgB,KAAAC,qBAAqB,GAAG,mBAAmB;EAAC;EAE5D;;;;;;EAMAC,0BAA0BA,CAACC,MAAiC,EAAEC,OAAqB;IACjF,OAAOR,iBAAiB,CAAC,IAAI,CAACI,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EACpE;EAEA;;;;;;EAMAR,iBAAiBA,CAACO,MAAiC,EAAEC,OAAqB;IACxE,OAAO,IAAI,CAACF,0BAA0B,CAACC,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CAC1DZ,GAAG,CAAEa,CAA2B,IAAWA,CAAC,CAACC,IAAI,CAAC,CACnD;EACH;;;uCA5BWX,WAAW,EAAAY,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,gBAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,UAAA;IAAA;EAAA;;;aAAXjB,WAAW;MAAAkB,OAAA,EAAXlB,WAAW,CAAAmB,IAAA;MAAAC,UAAA,EADE;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}