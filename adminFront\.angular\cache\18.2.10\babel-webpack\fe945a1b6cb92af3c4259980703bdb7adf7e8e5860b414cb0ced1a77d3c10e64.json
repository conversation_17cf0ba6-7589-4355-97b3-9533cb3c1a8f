{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { SharedModule } from 'src/app/pages/components/shared.module';\nimport { BaseComponent } from 'src/app/pages/components/base/baseComponent';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/shared/helper/allowHelper\";\nexport class RegularNoticeComponent extends BaseComponent {\n  constructor(_allow) {\n    super(_allow);\n    this._allow = _allow;\n    this.pageFirst = 1;\n    this.pageSize = 10;\n    this.pageIndex = 1;\n    this.totalRecords = 0;\n  }\n  static {\n    this.ɵfac = function RegularNoticeComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || RegularNoticeComponent)(i0.ɵɵdirectiveInject(i1.AllowHelper));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: RegularNoticeComponent,\n      selectors: [[\"ngx-regular-notice\"]],\n      standalone: true,\n      features: [i0.ɵɵInheritDefinitionFeature, i0.ɵɵStandaloneFeature],\n      decls: 2,\n      vars: 0,\n      template: function RegularNoticeComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\");\n          i0.ɵɵtext(1, \"regularNotice\");\n          i0.ɵɵelementEnd();\n        }\n      },\n      dependencies: [CommonModule, SharedModule],\n      styles: [\"/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsImZpbGUiOiJyZWd1bGFyLW5vdGljZS5jb21wb25lbnQuc2NzcyJ9 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvcGFnZXMvY29uc3RydWN0aW9uLXByb2plY3QtbWFuYWdlbWVudC9ub3RpY2UtbWFuYWdlbWVudC9yZWd1bGFyLW5vdGljZS9yZWd1bGFyLW5vdGljZS5jb21wb25lbnQuc2NzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiO0FBQ0EsNEtBQTRLIiwic291cmNlUm9vdCI6IiJ9 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "SharedModule", "BaseComponent", "RegularNoticeComponent", "constructor", "_allow", "pageFirst", "pageSize", "pageIndex", "totalRecords", "i0", "ɵɵdirectiveInject", "i1", "AllowHelper", "selectors", "standalone", "features", "ɵɵInheritDefinitionFeature", "ɵɵStandaloneFeature", "decls", "vars", "template", "RegularNoticeComponent_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "styles"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\construction-project-management\\notice-management\\regular-notice\\regular-notice.component.ts", "C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\construction-project-management\\notice-management\\regular-notice\\regular-notice.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { TypeMailPipe } from 'src/app/@theme/pipes/mapping.pipe';\r\nimport { AllowHelper } from 'src/app/shared/helper/allowHelper';\r\nimport { SharedModule } from 'src/app/pages/components/shared.module';\r\nimport { BaseComponent } from 'src/app/pages/components/base/baseComponent';\r\n\r\n\r\n@Component({\r\n  selector: 'ngx-regular-notice',\r\n  templateUrl: './regular-notice.component.html',\r\n  styleUrls: ['./regular-notice.component.scss'],\r\n  standalone: true,\r\n  imports: [CommonModule, SharedModule, TypeMailPipe],\r\n})\r\n\r\nexport class RegularNoticeComponent extends BaseComponent implements OnInit {\r\n  constructor(\r\n    private _allow: AllowHelper,\r\n  ) { super(_allow) }\r\n\r\n  override pageFirst = 1;\r\n  override pageSize = 10;\r\n  override pageIndex = 1;\r\n  override totalRecords = 0;\r\n\r\n \r\n}\r\n", "<!-- 1.4 -->\r\n<div>regularNotice</div>"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAG9C,SAASC,YAAY,QAAQ,wCAAwC;AACrE,SAASC,aAAa,QAAQ,6CAA6C;;;AAW3E,OAAM,MAAOC,sBAAuB,SAAQD,aAAa;EACvDE,YACUC,MAAmB;IACzB,KAAK,CAACA,MAAM,CAAC;IADP,KAAAA,MAAM,GAANA,MAAM;IAGP,KAAAC,SAAS,GAAG,CAAC;IACb,KAAAC,QAAQ,GAAG,EAAE;IACb,KAAAC,SAAS,GAAG,CAAC;IACb,KAAAC,YAAY,GAAG,CAAC;EALP;;;uCAHPN,sBAAsB,EAAAO,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAC,WAAA;IAAA;EAAA;;;YAAtBV,sBAAsB;MAAAW,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAAN,EAAA,CAAAO,0BAAA,EAAAP,EAAA,CAAAQ,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,QAAA,WAAAC,gCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCfnCb,EAAA,CAAAe,cAAA,UAAK;UAAAf,EAAA,CAAAgB,MAAA,oBAAa;UAAAhB,EAAA,CAAAiB,YAAA,EAAM;;;qBDYZ3B,YAAY,EAAEC,YAAY;MAAA2B,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}