{"ast": null, "code": "/* tslint:disable */\n/* eslint-disable */\nexport var EnumStatusCode;\n(function (EnumStatusCode) {\n  EnumStatusCode[EnumStatusCode[\"$0\"] = 0] = \"$0\";\n  EnumStatusCode[EnumStatusCode[\"$1\"] = 1] = \"$1\";\n  EnumStatusCode[EnumStatusCode[\"$9\"] = 9] = \"$9\";\n  EnumStatusCode[EnumStatusCode[\"$30001\"] = 30001] = \"$30001\";\n})(EnumStatusCode || (EnumStatusCode = {}));", "map": {"version": 3, "names": ["EnumStatusCode"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\services\\api\\models\\enum-status-code.ts"], "sourcesContent": ["/* tslint:disable */\r\n/* eslint-disable */\r\nexport enum EnumStatusCode {\r\n  $0 = 0,\r\n  $1 = 1,\r\n  $9 = 9,\r\n  $30001 = 30001\r\n}\r\n"], "mappings": "AAAA;AACA;AACA,WAAYA,cAKX;AALD,WAAYA,cAAc;EACxBA,cAAA,CAAAA,cAAA,kBAAM;EACNA,cAAA,CAAAA,cAAA,kBAAM;EACNA,cAAA,CAAAA,cAAA,kBAAM;EACNA,cAAA,CAAAA,cAAA,8BAAc;AAChB,CAAC,EALWA,cAAc,KAAdA,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}