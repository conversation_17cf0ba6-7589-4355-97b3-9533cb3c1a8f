{"ast": null, "code": "import getTimezoneOffsetInMilliseconds from \"../_lib/getTimezoneOffsetInMilliseconds/index.js\";\nimport startOfDay from \"../startOfDay/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\nvar MILLISECONDS_IN_DAY = 86400000;\n\n/**\n * @name differenceInCalendarDays\n * @category Day Helpers\n * @summary Get the number of calendar days between the given dates.\n *\n * @description\n * Get the number of calendar days between the given dates. This means that the times are removed\n * from the dates and then the difference in days is calculated.\n *\n * @param {Date|Number} dateLeft - the later date\n * @param {Date|Number} dateRight - the earlier date\n * @returns {Number} the number of calendar days\n * @throws {TypeError} 2 arguments required\n *\n * @example\n * // How many calendar days are between\n * // 2 July 2011 23:00:00 and 2 July 2012 00:00:00?\n * const result = differenceInCalendarDays(\n *   new Date(2012, 6, 2, 0, 0),\n *   new Date(2011, 6, 2, 23, 0)\n * )\n * //=> 366\n * // How many calendar days are between\n * // 2 July 2011 23:59:00 and 3 July 2011 00:01:00?\n * const result = differenceInCalendarDays(\n *   new Date(2011, 6, 3, 0, 1),\n *   new Date(2011, 6, 2, 23, 59)\n * )\n * //=> 1\n */\nexport default function differenceInCalendarDays(dirtyDateLeft, dirtyDateRight) {\n  requiredArgs(2, arguments);\n  var startOfDayLeft = startOfDay(dirtyDateLeft);\n  var startOfDayRight = startOfDay(dirtyDateRight);\n  var timestampLeft = startOfDayLeft.getTime() - getTimezoneOffsetInMilliseconds(startOfDayLeft);\n  var timestampRight = startOfDayRight.getTime() - getTimezoneOffsetInMilliseconds(startOfDayRight);\n\n  // Round the number of days to the nearest integer\n  // because the number of milliseconds in a day is not constant\n  // (e.g. it's different in the day of the daylight saving time clock shift)\n  return Math.round((timestampLeft - timestampRight) / MILLISECONDS_IN_DAY);\n}", "map": {"version": 3, "names": ["getTimezoneOffsetInMilliseconds", "startOfDay", "requiredArgs", "MILLISECONDS_IN_DAY", "differenceInCalendarDays", "dirtyDateLeft", "dirtyDateRight", "arguments", "startOfDayLeft", "startOfDayRight", "timestampLeft", "getTime", "timestampRight", "Math", "round"], "sources": ["C:/Users/<USER>/Documents/jeansalechange/adminFront/node_modules/date-fns/esm/differenceInCalendarDays/index.js"], "sourcesContent": ["import getTimezoneOffsetInMilliseconds from \"../_lib/getTimezoneOffsetInMilliseconds/index.js\";\nimport startOfDay from \"../startOfDay/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\nvar MILLISECONDS_IN_DAY = 86400000;\n\n/**\n * @name differenceInCalendarDays\n * @category Day Helpers\n * @summary Get the number of calendar days between the given dates.\n *\n * @description\n * Get the number of calendar days between the given dates. This means that the times are removed\n * from the dates and then the difference in days is calculated.\n *\n * @param {Date|Number} dateLeft - the later date\n * @param {Date|Number} dateRight - the earlier date\n * @returns {Number} the number of calendar days\n * @throws {TypeError} 2 arguments required\n *\n * @example\n * // How many calendar days are between\n * // 2 July 2011 23:00:00 and 2 July 2012 00:00:00?\n * const result = differenceInCalendarDays(\n *   new Date(2012, 6, 2, 0, 0),\n *   new Date(2011, 6, 2, 23, 0)\n * )\n * //=> 366\n * // How many calendar days are between\n * // 2 July 2011 23:59:00 and 3 July 2011 00:01:00?\n * const result = differenceInCalendarDays(\n *   new Date(2011, 6, 3, 0, 1),\n *   new Date(2011, 6, 2, 23, 59)\n * )\n * //=> 1\n */\nexport default function differenceInCalendarDays(dirtyDateLeft, dirtyDateRight) {\n  requiredArgs(2, arguments);\n  var startOfDayLeft = startOfDay(dirtyDateLeft);\n  var startOfDayRight = startOfDay(dirtyDateRight);\n  var timestampLeft = startOfDayLeft.getTime() - getTimezoneOffsetInMilliseconds(startOfDayLeft);\n  var timestampRight = startOfDayRight.getTime() - getTimezoneOffsetInMilliseconds(startOfDayRight);\n\n  // Round the number of days to the nearest integer\n  // because the number of milliseconds in a day is not constant\n  // (e.g. it's different in the day of the daylight saving time clock shift)\n  return Math.round((timestampLeft - timestampRight) / MILLISECONDS_IN_DAY);\n}"], "mappings": "AAAA,OAAOA,+BAA+B,MAAM,kDAAkD;AAC9F,OAAOC,UAAU,MAAM,wBAAwB;AAC/C,OAAOC,YAAY,MAAM,+BAA+B;AACxD,IAAIC,mBAAmB,GAAG,QAAQ;;AAElC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,SAASC,wBAAwBA,CAACC,aAAa,EAAEC,cAAc,EAAE;EAC9EJ,YAAY,CAAC,CAAC,EAAEK,SAAS,CAAC;EAC1B,IAAIC,cAAc,GAAGP,UAAU,CAACI,aAAa,CAAC;EAC9C,IAAII,eAAe,GAAGR,UAAU,CAACK,cAAc,CAAC;EAChD,IAAII,aAAa,GAAGF,cAAc,CAACG,OAAO,CAAC,CAAC,GAAGX,+BAA+B,CAACQ,cAAc,CAAC;EAC9F,IAAII,cAAc,GAAGH,eAAe,CAACE,OAAO,CAAC,CAAC,GAAGX,+BAA+B,CAACS,eAAe,CAAC;;EAEjG;EACA;EACA;EACA,OAAOI,IAAI,CAACC,KAAK,CAAC,CAACJ,aAAa,GAAGE,cAAc,IAAIT,mBAAmB,CAAC;AAC3E", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}