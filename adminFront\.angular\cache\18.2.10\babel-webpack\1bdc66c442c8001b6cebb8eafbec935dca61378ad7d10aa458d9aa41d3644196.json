{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { EventEmitter, PLATFORM_ID, Directive, Inject, Input, Output, Optional, NgModule } from '@angular/core';\nimport { isPlatformBrowser } from '@angular/common';\nimport { Subject, merge, Observable, fromEvent } from 'rxjs';\nimport { tap, share, mergeMap, take, map, pairwise, filter, takeUntil } from 'rxjs/operators';\n\n/**\n * @hidden\n */\nconst IS_TOUCH_DEVICE = (() => {\n  // In case we're in Node.js environment.\n  if (typeof window === 'undefined') {\n    return false;\n  } else {\n    return 'ontouchstart' in window || navigator.maxTouchPoints > 0 || navigator.msMaxTouchPoints > 0;\n  }\n})();\n\n/** Creates a deep clone of an element. */\nfunction deepCloneNode(node) {\n  const clone = node.cloneNode(true);\n  const descendantsWithId = clone.querySelectorAll('[id]');\n  const nodeName = node.nodeName.toLowerCase();\n  // Remove the `id` to avoid having multiple elements with the same id on the page.\n  clone.removeAttribute('id');\n  descendantsWithId.forEach(descendant => {\n    descendant.removeAttribute('id');\n  });\n  if (nodeName === 'canvas') {\n    transferCanvasData(node, clone);\n  } else if (nodeName === 'input' || nodeName === 'select' || nodeName === 'textarea') {\n    transferInputData(node, clone);\n  }\n  transferData('canvas', node, clone, transferCanvasData);\n  transferData('input, textarea, select', node, clone, transferInputData);\n  return clone;\n}\n/** Matches elements between an element and its clone and allows for their data to be cloned. */\nfunction transferData(selector, node, clone, callback) {\n  const descendantElements = node.querySelectorAll(selector);\n  if (descendantElements.length) {\n    const cloneElements = clone.querySelectorAll(selector);\n    for (let i = 0; i < descendantElements.length; i++) {\n      callback(descendantElements[i], cloneElements[i]);\n    }\n  }\n}\n// Counter for unique cloned radio button names.\nlet cloneUniqueId = 0;\n/** Transfers the data of one input element to another. */\nfunction transferInputData(source, clone) {\n  // Browsers throw an error when assigning the value of a file input programmatically.\n  if (clone.type !== 'file') {\n    clone.value = source.value;\n  }\n  // Radio button `name` attributes must be unique for radio button groups\n  // otherwise original radio buttons can lose their checked state\n  // once the clone is inserted in the DOM.\n  if (clone.type === 'radio' && clone.name) {\n    clone.name = `mat-clone-${clone.name}-${cloneUniqueId++}`;\n  }\n}\n/** Transfers the data of one canvas element to another. */\nfunction transferCanvasData(source, clone) {\n  const context = clone.getContext('2d');\n  if (context) {\n    // In some cases `drawImage` can throw (e.g. if the canvas size is 0x0).\n    // We can't do much about it so just ignore the error.\n    try {\n      context.drawImage(source, 0, 0);\n    } catch {}\n  }\n}\nfunction getNewBoundingRectangle(startingRect, edges, clientX, clientY) {\n  const newBoundingRect = {\n    top: startingRect.top,\n    bottom: startingRect.bottom,\n    left: startingRect.left,\n    right: startingRect.right\n  };\n  if (edges.top) {\n    newBoundingRect.top += clientY;\n  }\n  if (edges.bottom) {\n    newBoundingRect.bottom += clientY;\n  }\n  if (edges.left) {\n    newBoundingRect.left += clientX;\n  }\n  if (edges.right) {\n    newBoundingRect.right += clientX;\n  }\n  newBoundingRect.height = newBoundingRect.bottom - newBoundingRect.top;\n  newBoundingRect.width = newBoundingRect.right - newBoundingRect.left;\n  return newBoundingRect;\n}\nfunction getElementRect(element, ghostElementPositioning) {\n  let translateX = 0;\n  let translateY = 0;\n  const style = element.nativeElement.style;\n  const transformProperties = ['transform', '-ms-transform', '-moz-transform', '-o-transform'];\n  const transform = transformProperties.map(property => style[property]).find(value => !!value);\n  if (transform && transform.includes('translate')) {\n    translateX = transform.replace(/.*translate3?d?\\((-?[0-9]*)px, (-?[0-9]*)px.*/, '$1');\n    translateY = transform.replace(/.*translate3?d?\\((-?[0-9]*)px, (-?[0-9]*)px.*/, '$2');\n  }\n  if (ghostElementPositioning === 'absolute') {\n    return {\n      height: element.nativeElement.offsetHeight,\n      width: element.nativeElement.offsetWidth,\n      top: element.nativeElement.offsetTop - translateY,\n      bottom: element.nativeElement.offsetHeight + element.nativeElement.offsetTop - translateY,\n      left: element.nativeElement.offsetLeft - translateX,\n      right: element.nativeElement.offsetWidth + element.nativeElement.offsetLeft - translateX\n    };\n  } else {\n    const boundingRect = element.nativeElement.getBoundingClientRect();\n    return {\n      height: boundingRect.height,\n      width: boundingRect.width,\n      top: boundingRect.top - translateY,\n      bottom: boundingRect.bottom - translateY,\n      left: boundingRect.left - translateX,\n      right: boundingRect.right - translateX,\n      scrollTop: element.nativeElement.scrollTop,\n      scrollLeft: element.nativeElement.scrollLeft\n    };\n  }\n}\nconst DEFAULT_RESIZE_CURSORS = Object.freeze({\n  topLeft: 'nw-resize',\n  topRight: 'ne-resize',\n  bottomLeft: 'sw-resize',\n  bottomRight: 'se-resize',\n  leftOrRight: 'col-resize',\n  topOrBottom: 'row-resize'\n});\nfunction getResizeCursor(edges, cursors) {\n  if (edges.left && edges.top) {\n    return cursors.topLeft;\n  } else if (edges.right && edges.top) {\n    return cursors.topRight;\n  } else if (edges.left && edges.bottom) {\n    return cursors.bottomLeft;\n  } else if (edges.right && edges.bottom) {\n    return cursors.bottomRight;\n  } else if (edges.left || edges.right) {\n    return cursors.leftOrRight;\n  } else if (edges.top || edges.bottom) {\n    return cursors.topOrBottom;\n  } else {\n    return '';\n  }\n}\nfunction getEdgesDiff({\n  edges,\n  initialRectangle,\n  newRectangle\n}) {\n  const edgesDiff = {};\n  Object.keys(edges).forEach(edge => {\n    edgesDiff[edge] = (newRectangle[edge] || 0) - (initialRectangle[edge] || 0);\n  });\n  return edgesDiff;\n}\nconst RESIZE_ACTIVE_CLASS = 'resize-active';\nconst RESIZE_GHOST_ELEMENT_CLASS = 'resize-ghost-element';\nconst MOUSE_MOVE_THROTTLE_MS = 50;\n/**\n * Place this on an element to make it resizable. For example:\n *\n * ```html\n * <div\n *   mwlResizable\n *   [resizeEdges]=\"{bottom: true, right: true, top: true, left: true}\"\n *   [enableGhostResize]=\"true\">\n * </div>\n * ```\n * Or in case they are sibling elements:\n * ```html\n * <div mwlResizable #resizableElement=\"mwlResizable\"></div>\n * <div mwlResizeHandle [resizableContainer]=\"resizableElement\" [resizeEdges]=\"{bottom: true, right: true}\"></div>\n * ```\n */\nclass ResizableDirective {\n  /**\n   * @hidden\n   */\n  constructor(platformId, renderer, elm, zone) {\n    this.platformId = platformId;\n    this.renderer = renderer;\n    this.elm = elm;\n    this.zone = zone;\n    /**\n     * Set to `true` to enable a temporary resizing effect of the element in between the `resizeStart` and `resizeEnd` events.\n     */\n    this.enableGhostResize = false;\n    /**\n     * A snap grid that resize events will be locked to.\n     *\n     * e.g. to only allow the element to be resized every 10px set it to `{left: 10, right: 10}`\n     */\n    this.resizeSnapGrid = {};\n    /**\n     * The mouse cursors that will be set on the resize edges\n     */\n    this.resizeCursors = DEFAULT_RESIZE_CURSORS;\n    /**\n     * Define the positioning of the ghost element (can be fixed or absolute)\n     */\n    this.ghostElementPositioning = 'fixed';\n    /**\n     * Allow elements to be resized to negative dimensions\n     */\n    this.allowNegativeResizes = false;\n    /**\n     * The mouse move throttle in milliseconds, default: 50 ms\n     */\n    this.mouseMoveThrottleMS = MOUSE_MOVE_THROTTLE_MS;\n    /**\n     * Called when the mouse is pressed and a resize event is about to begin. `$event` is a `ResizeEvent` object.\n     */\n    this.resizeStart = new EventEmitter();\n    /**\n     * Called as the mouse is dragged after a resize event has begun. `$event` is a `ResizeEvent` object.\n     */\n    this.resizing = new EventEmitter();\n    /**\n     * Called after the mouse is released after a resize event. `$event` is a `ResizeEvent` object.\n     */\n    this.resizeEnd = new EventEmitter();\n    /**\n     * @hidden\n     */\n    this.mouseup = new Subject();\n    /**\n     * @hidden\n     */\n    this.mousedown = new Subject();\n    /**\n     * @hidden\n     */\n    this.mousemove = new Subject();\n    this.destroy$ = new Subject();\n    this.pointerEventListeners = PointerEventListeners.getInstance(renderer, zone);\n  }\n  /**\n   * @hidden\n   */\n  ngOnInit() {\n    const mousedown$ = merge(this.pointerEventListeners.pointerDown, this.mousedown);\n    const mousemove$ = merge(this.pointerEventListeners.pointerMove, this.mousemove).pipe(tap(({\n      event\n    }) => {\n      if (currentResize && event.cancelable) {\n        event.preventDefault();\n      }\n    }), share());\n    const mouseup$ = merge(this.pointerEventListeners.pointerUp, this.mouseup);\n    let currentResize;\n    const removeGhostElement = () => {\n      if (currentResize && currentResize.clonedNode) {\n        this.elm.nativeElement.parentElement.removeChild(currentResize.clonedNode);\n        this.renderer.setStyle(this.elm.nativeElement, 'visibility', 'inherit');\n      }\n    };\n    const getResizeCursors = () => {\n      return {\n        ...DEFAULT_RESIZE_CURSORS,\n        ...this.resizeCursors\n      };\n    };\n    const mousedrag = mousedown$.pipe(mergeMap(startCoords => {\n      function getDiff(moveCoords) {\n        return {\n          clientX: moveCoords.clientX - startCoords.clientX,\n          clientY: moveCoords.clientY - startCoords.clientY\n        };\n      }\n      const getSnapGrid = () => {\n        const snapGrid = {\n          x: 1,\n          y: 1\n        };\n        if (currentResize) {\n          if (this.resizeSnapGrid.left && currentResize.edges.left) {\n            snapGrid.x = +this.resizeSnapGrid.left;\n          } else if (this.resizeSnapGrid.right && currentResize.edges.right) {\n            snapGrid.x = +this.resizeSnapGrid.right;\n          }\n          if (this.resizeSnapGrid.top && currentResize.edges.top) {\n            snapGrid.y = +this.resizeSnapGrid.top;\n          } else if (this.resizeSnapGrid.bottom && currentResize.edges.bottom) {\n            snapGrid.y = +this.resizeSnapGrid.bottom;\n          }\n        }\n        return snapGrid;\n      };\n      function getGrid(coords, snapGrid) {\n        return {\n          x: Math.ceil(coords.clientX / snapGrid.x),\n          y: Math.ceil(coords.clientY / snapGrid.y)\n        };\n      }\n      return merge(mousemove$.pipe(take(1)).pipe(map(coords => [, coords])), mousemove$.pipe(pairwise())).pipe(map(([previousCoords, newCoords]) => {\n        return [previousCoords ? getDiff(previousCoords) : previousCoords, getDiff(newCoords)];\n      })).pipe(filter(([previousCoords, newCoords]) => {\n        if (!previousCoords) {\n          return true;\n        }\n        const snapGrid = getSnapGrid();\n        const previousGrid = getGrid(previousCoords, snapGrid);\n        const newGrid = getGrid(newCoords, snapGrid);\n        return previousGrid.x !== newGrid.x || previousGrid.y !== newGrid.y;\n      })).pipe(map(([, newCoords]) => {\n        const snapGrid = getSnapGrid();\n        return {\n          clientX: Math.round(newCoords.clientX / snapGrid.x) * snapGrid.x,\n          clientY: Math.round(newCoords.clientY / snapGrid.y) * snapGrid.y\n        };\n      })).pipe(takeUntil(merge(mouseup$, mousedown$)));\n    })).pipe(filter(() => !!currentResize));\n    mousedrag.pipe(map(({\n      clientX,\n      clientY\n    }) => {\n      return getNewBoundingRectangle(currentResize.startingRect, currentResize.edges, clientX, clientY);\n    })).pipe(filter(newBoundingRect => {\n      return this.allowNegativeResizes || !!(newBoundingRect.height && newBoundingRect.width && newBoundingRect.height > 0 && newBoundingRect.width > 0);\n    })).pipe(filter(newBoundingRect => {\n      return this.validateResize ? this.validateResize({\n        rectangle: newBoundingRect,\n        edges: getEdgesDiff({\n          edges: currentResize.edges,\n          initialRectangle: currentResize.startingRect,\n          newRectangle: newBoundingRect\n        })\n      }) : true;\n    }), takeUntil(this.destroy$)).subscribe(newBoundingRect => {\n      if (currentResize && currentResize.clonedNode) {\n        this.renderer.setStyle(currentResize.clonedNode, 'height', `${newBoundingRect.height}px`);\n        this.renderer.setStyle(currentResize.clonedNode, 'width', `${newBoundingRect.width}px`);\n        this.renderer.setStyle(currentResize.clonedNode, 'top', `${newBoundingRect.top}px`);\n        this.renderer.setStyle(currentResize.clonedNode, 'left', `${newBoundingRect.left}px`);\n      }\n      if (this.resizing.observers.length > 0) {\n        this.zone.run(() => {\n          this.resizing.emit({\n            edges: getEdgesDiff({\n              edges: currentResize.edges,\n              initialRectangle: currentResize.startingRect,\n              newRectangle: newBoundingRect\n            }),\n            rectangle: newBoundingRect\n          });\n        });\n      }\n      currentResize.currentRect = newBoundingRect;\n    });\n    mousedown$.pipe(map(({\n      edges\n    }) => {\n      return edges || {};\n    }), filter(edges => {\n      return Object.keys(edges).length > 0;\n    }), takeUntil(this.destroy$)).subscribe(edges => {\n      if (currentResize) {\n        removeGhostElement();\n      }\n      const startingRect = getElementRect(this.elm, this.ghostElementPositioning);\n      currentResize = {\n        edges,\n        startingRect,\n        currentRect: startingRect\n      };\n      const resizeCursors = getResizeCursors();\n      const cursor = getResizeCursor(currentResize.edges, resizeCursors);\n      this.renderer.setStyle(document.body, 'cursor', cursor);\n      this.setElementClass(this.elm, RESIZE_ACTIVE_CLASS, true);\n      if (this.enableGhostResize) {\n        currentResize.clonedNode = deepCloneNode(this.elm.nativeElement);\n        this.elm.nativeElement.parentElement.appendChild(currentResize.clonedNode);\n        this.renderer.setStyle(this.elm.nativeElement, 'visibility', 'hidden');\n        this.renderer.setStyle(currentResize.clonedNode, 'position', this.ghostElementPositioning);\n        this.renderer.setStyle(currentResize.clonedNode, 'left', `${currentResize.startingRect.left}px`);\n        this.renderer.setStyle(currentResize.clonedNode, 'top', `${currentResize.startingRect.top}px`);\n        this.renderer.setStyle(currentResize.clonedNode, 'height', `${currentResize.startingRect.height}px`);\n        this.renderer.setStyle(currentResize.clonedNode, 'width', `${currentResize.startingRect.width}px`);\n        this.renderer.setStyle(currentResize.clonedNode, 'cursor', getResizeCursor(currentResize.edges, resizeCursors));\n        this.renderer.addClass(currentResize.clonedNode, RESIZE_GHOST_ELEMENT_CLASS);\n        currentResize.clonedNode.scrollTop = currentResize.startingRect.scrollTop;\n        currentResize.clonedNode.scrollLeft = currentResize.startingRect.scrollLeft;\n      }\n      if (this.resizeStart.observers.length > 0) {\n        this.zone.run(() => {\n          this.resizeStart.emit({\n            edges: getEdgesDiff({\n              edges,\n              initialRectangle: startingRect,\n              newRectangle: startingRect\n            }),\n            rectangle: getNewBoundingRectangle(startingRect, {}, 0, 0)\n          });\n        });\n      }\n    });\n    mouseup$.pipe(takeUntil(this.destroy$)).subscribe(() => {\n      if (currentResize) {\n        this.renderer.removeClass(this.elm.nativeElement, RESIZE_ACTIVE_CLASS);\n        this.renderer.setStyle(document.body, 'cursor', '');\n        this.renderer.setStyle(this.elm.nativeElement, 'cursor', '');\n        if (this.resizeEnd.observers.length > 0) {\n          this.zone.run(() => {\n            this.resizeEnd.emit({\n              edges: getEdgesDiff({\n                edges: currentResize.edges,\n                initialRectangle: currentResize.startingRect,\n                newRectangle: currentResize.currentRect\n              }),\n              rectangle: currentResize.currentRect\n            });\n          });\n        }\n        removeGhostElement();\n        currentResize = null;\n      }\n    });\n  }\n  /**\n   * @hidden\n   */\n  ngOnDestroy() {\n    // browser check for angular universal, because it doesn't know what document is\n    if (isPlatformBrowser(this.platformId)) {\n      this.renderer.setStyle(document.body, 'cursor', '');\n    }\n    this.mousedown.complete();\n    this.mouseup.complete();\n    this.mousemove.complete();\n    this.destroy$.next();\n  }\n  setElementClass(elm, name, add) {\n    if (add) {\n      this.renderer.addClass(elm.nativeElement, name);\n    } else {\n      this.renderer.removeClass(elm.nativeElement, name);\n    }\n  }\n}\nResizableDirective.ɵfac = function ResizableDirective_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || ResizableDirective)(i0.ɵɵdirectiveInject(PLATFORM_ID), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.NgZone));\n};\nResizableDirective.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: ResizableDirective,\n  selectors: [[\"\", \"mwlResizable\", \"\"]],\n  inputs: {\n    validateResize: \"validateResize\",\n    enableGhostResize: \"enableGhostResize\",\n    resizeSnapGrid: \"resizeSnapGrid\",\n    resizeCursors: \"resizeCursors\",\n    ghostElementPositioning: \"ghostElementPositioning\",\n    allowNegativeResizes: \"allowNegativeResizes\",\n    mouseMoveThrottleMS: \"mouseMoveThrottleMS\"\n  },\n  outputs: {\n    resizeStart: \"resizeStart\",\n    resizing: \"resizing\",\n    resizeEnd: \"resizeEnd\"\n  },\n  exportAs: [\"mwlResizable\"]\n});\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ResizableDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[mwlResizable]',\n      exportAs: 'mwlResizable'\n    }]\n  }], function () {\n    return [{\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [PLATFORM_ID]\n      }]\n    }, {\n      type: i0.Renderer2\n    }, {\n      type: i0.ElementRef\n    }, {\n      type: i0.NgZone\n    }];\n  }, {\n    validateResize: [{\n      type: Input\n    }],\n    enableGhostResize: [{\n      type: Input\n    }],\n    resizeSnapGrid: [{\n      type: Input\n    }],\n    resizeCursors: [{\n      type: Input\n    }],\n    ghostElementPositioning: [{\n      type: Input\n    }],\n    allowNegativeResizes: [{\n      type: Input\n    }],\n    mouseMoveThrottleMS: [{\n      type: Input\n    }],\n    resizeStart: [{\n      type: Output\n    }],\n    resizing: [{\n      type: Output\n    }],\n    resizeEnd: [{\n      type: Output\n    }]\n  });\n})();\nclass PointerEventListeners {\n  constructor(renderer, zone) {\n    this.pointerDown = new Observable(observer => {\n      let unsubscribeMouseDown;\n      let unsubscribeTouchStart;\n      zone.runOutsideAngular(() => {\n        unsubscribeMouseDown = renderer.listen('document', 'mousedown', event => {\n          observer.next({\n            clientX: event.clientX,\n            clientY: event.clientY,\n            event\n          });\n        });\n        if (IS_TOUCH_DEVICE) {\n          unsubscribeTouchStart = renderer.listen('document', 'touchstart', event => {\n            observer.next({\n              clientX: event.touches[0].clientX,\n              clientY: event.touches[0].clientY,\n              event\n            });\n          });\n        }\n      });\n      return () => {\n        unsubscribeMouseDown();\n        if (IS_TOUCH_DEVICE) {\n          unsubscribeTouchStart();\n        }\n      };\n    }).pipe(share());\n    this.pointerMove = new Observable(observer => {\n      let unsubscribeMouseMove;\n      let unsubscribeTouchMove;\n      zone.runOutsideAngular(() => {\n        unsubscribeMouseMove = renderer.listen('document', 'mousemove', event => {\n          observer.next({\n            clientX: event.clientX,\n            clientY: event.clientY,\n            event\n          });\n        });\n        if (IS_TOUCH_DEVICE) {\n          unsubscribeTouchMove = renderer.listen('document', 'touchmove', event => {\n            observer.next({\n              clientX: event.targetTouches[0].clientX,\n              clientY: event.targetTouches[0].clientY,\n              event\n            });\n          });\n        }\n      });\n      return () => {\n        unsubscribeMouseMove();\n        if (IS_TOUCH_DEVICE) {\n          unsubscribeTouchMove();\n        }\n      };\n    }).pipe(share());\n    this.pointerUp = new Observable(observer => {\n      let unsubscribeMouseUp;\n      let unsubscribeTouchEnd;\n      let unsubscribeTouchCancel;\n      zone.runOutsideAngular(() => {\n        unsubscribeMouseUp = renderer.listen('document', 'mouseup', event => {\n          observer.next({\n            clientX: event.clientX,\n            clientY: event.clientY,\n            event\n          });\n        });\n        if (IS_TOUCH_DEVICE) {\n          unsubscribeTouchEnd = renderer.listen('document', 'touchend', event => {\n            observer.next({\n              clientX: event.changedTouches[0].clientX,\n              clientY: event.changedTouches[0].clientY,\n              event\n            });\n          });\n          unsubscribeTouchCancel = renderer.listen('document', 'touchcancel', event => {\n            observer.next({\n              clientX: event.changedTouches[0].clientX,\n              clientY: event.changedTouches[0].clientY,\n              event\n            });\n          });\n        }\n      });\n      return () => {\n        unsubscribeMouseUp();\n        if (IS_TOUCH_DEVICE) {\n          unsubscribeTouchEnd();\n          unsubscribeTouchCancel();\n        }\n      };\n    }).pipe(share());\n  }\n  static getInstance(renderer, zone) {\n    if (!PointerEventListeners.instance) {\n      PointerEventListeners.instance = new PointerEventListeners(renderer, zone);\n    }\n    return PointerEventListeners.instance;\n  }\n}\n\n/**\n * An element placed inside a `mwlResizable` directive to be used as a drag and resize handle\n *\n * For example\n *\n * ```html\n * <div mwlResizable>\n *   <div mwlResizeHandle [resizeEdges]=\"{bottom: true, right: true}\"></div>\n * </div>\n * ```\n * Or in case they are sibling elements:\n * ```html\n * <div mwlResizable #resizableElement=\"mwlResizable\"></div>\n * <div mwlResizeHandle [resizableContainer]=\"resizableElement\" [resizeEdges]=\"{bottom: true, right: true}\"></div>\n * ```\n */\nclass ResizeHandleDirective {\n  constructor(renderer, element, zone, resizableDirective) {\n    this.renderer = renderer;\n    this.element = element;\n    this.zone = zone;\n    this.resizableDirective = resizableDirective;\n    /**\n     * The `Edges` object that contains the edges of the parent element that dragging the handle will trigger a resize on\n     */\n    this.resizeEdges = {};\n    this.eventListeners = {};\n    this.destroy$ = new Subject();\n  }\n  ngOnInit() {\n    this.zone.runOutsideAngular(() => {\n      this.listenOnTheHost('mousedown').subscribe(event => {\n        this.onMousedown(event, event.clientX, event.clientY);\n      });\n      this.listenOnTheHost('mouseup').subscribe(event => {\n        this.onMouseup(event.clientX, event.clientY);\n      });\n      if (IS_TOUCH_DEVICE) {\n        this.listenOnTheHost('touchstart').subscribe(event => {\n          this.onMousedown(event, event.touches[0].clientX, event.touches[0].clientY);\n        });\n        merge(this.listenOnTheHost('touchend'), this.listenOnTheHost('touchcancel')).subscribe(event => {\n          this.onMouseup(event.changedTouches[0].clientX, event.changedTouches[0].clientY);\n        });\n      }\n    });\n  }\n  ngOnDestroy() {\n    this.destroy$.next();\n    this.unsubscribeEventListeners();\n  }\n  /**\n   * @hidden\n   */\n  onMousedown(event, clientX, clientY) {\n    if (event.cancelable) {\n      event.preventDefault();\n    }\n    if (!this.eventListeners.touchmove) {\n      this.eventListeners.touchmove = this.renderer.listen(this.element.nativeElement, 'touchmove', touchMoveEvent => {\n        this.onMousemove(touchMoveEvent, touchMoveEvent.targetTouches[0].clientX, touchMoveEvent.targetTouches[0].clientY);\n      });\n    }\n    if (!this.eventListeners.mousemove) {\n      this.eventListeners.mousemove = this.renderer.listen(this.element.nativeElement, 'mousemove', mouseMoveEvent => {\n        this.onMousemove(mouseMoveEvent, mouseMoveEvent.clientX, mouseMoveEvent.clientY);\n      });\n    }\n    this.resizable.mousedown.next({\n      clientX,\n      clientY,\n      edges: this.resizeEdges\n    });\n  }\n  /**\n   * @hidden\n   */\n  onMouseup(clientX, clientY) {\n    this.unsubscribeEventListeners();\n    this.resizable.mouseup.next({\n      clientX,\n      clientY,\n      edges: this.resizeEdges\n    });\n  }\n  // directive might be passed from DI or as an input\n  get resizable() {\n    return this.resizableDirective || this.resizableContainer;\n  }\n  onMousemove(event, clientX, clientY) {\n    this.resizable.mousemove.next({\n      clientX,\n      clientY,\n      edges: this.resizeEdges,\n      event\n    });\n  }\n  unsubscribeEventListeners() {\n    Object.keys(this.eventListeners).forEach(type => {\n      this.eventListeners[type]();\n      delete this.eventListeners[type];\n    });\n  }\n  listenOnTheHost(eventName) {\n    return fromEvent(this.element.nativeElement, eventName).pipe(takeUntil(this.destroy$));\n  }\n}\nResizeHandleDirective.ɵfac = function ResizeHandleDirective_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || ResizeHandleDirective)(i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(ResizableDirective, 8));\n};\nResizeHandleDirective.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: ResizeHandleDirective,\n  selectors: [[\"\", \"mwlResizeHandle\", \"\"]],\n  inputs: {\n    resizeEdges: \"resizeEdges\",\n    resizableContainer: \"resizableContainer\"\n  }\n});\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ResizeHandleDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[mwlResizeHandle]'\n    }]\n  }], function () {\n    return [{\n      type: i0.Renderer2\n    }, {\n      type: i0.ElementRef\n    }, {\n      type: i0.NgZone\n    }, {\n      type: ResizableDirective,\n      decorators: [{\n        type: Optional\n      }]\n    }];\n  }, {\n    resizeEdges: [{\n      type: Input\n    }],\n    resizableContainer: [{\n      type: Input\n    }]\n  });\n})();\nclass ResizableModule {}\nResizableModule.ɵfac = function ResizableModule_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || ResizableModule)();\n};\nResizableModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: ResizableModule\n});\nResizableModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({});\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ResizableModule, [{\n    type: NgModule,\n    args: [{\n      declarations: [ResizableDirective, ResizeHandleDirective],\n      exports: [ResizableDirective, ResizeHandleDirective]\n    }]\n  }], null, null);\n})();\n\n/*\n * Public API Surface of angular-resizable-element\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { ResizableDirective, ResizableModule, ResizeHandleDirective };\n//# sourceMappingURL=angular-resizable-element.mjs.map", "map": {"version": 3, "names": ["i0", "EventEmitter", "PLATFORM_ID", "Directive", "Inject", "Input", "Output", "Optional", "NgModule", "isPlatformBrowser", "Subject", "merge", "Observable", "fromEvent", "tap", "share", "mergeMap", "take", "map", "pairwise", "filter", "takeUntil", "IS_TOUCH_DEVICE", "window", "navigator", "maxTouchPoints", "msMaxTouchPoints", "deepCloneNode", "node", "clone", "cloneNode", "descendantsWithId", "querySelectorAll", "nodeName", "toLowerCase", "removeAttribute", "for<PERSON>ach", "descendant", "transferCanvasData", "transferInputData", "transferData", "selector", "callback", "descendantElements", "length", "cloneElements", "i", "cloneUniqueId", "source", "type", "value", "name", "context", "getContext", "drawImage", "getNewBoundingRectangle", "startingRect", "edges", "clientX", "clientY", "newBoundingRect", "top", "bottom", "left", "right", "height", "width", "getElementRect", "element", "ghostElementPositioning", "translateX", "translateY", "style", "nativeElement", "transformProperties", "transform", "property", "find", "includes", "replace", "offsetHeight", "offsetWidth", "offsetTop", "offsetLeft", "boundingRect", "getBoundingClientRect", "scrollTop", "scrollLeft", "DEFAULT_RESIZE_CURSORS", "Object", "freeze", "topLeft", "topRight", "bottomLeft", "bottomRight", "leftOrRight", "topOrBottom", "getResizeCursor", "cursors", "getEdgesDiff", "initialRectangle", "newRectangle", "edgesDiff", "keys", "edge", "RESIZE_ACTIVE_CLASS", "RESIZE_GHOST_ELEMENT_CLASS", "MOUSE_MOVE_THROTTLE_MS", "ResizableDirective", "constructor", "platformId", "renderer", "elm", "zone", "enableGhostResize", "resizeSnapGrid", "resizeCursors", "allowNegativeResizes", "mouseMoveThrottleMS", "resizeStart", "resizing", "resizeEnd", "mouseup", "mousedown", "mousemove", "destroy$", "pointerEventListeners", "PointerEventListeners", "getInstance", "ngOnInit", "mousedown$", "pointerDown", "mousemove$", "pointer<PERSON><PERSON>", "pipe", "event", "currentResize", "cancelable", "preventDefault", "mouseup$", "pointerUp", "removeGhostElement", "clonedNode", "parentElement", "<PERSON><PERSON><PERSON><PERSON>", "setStyle", "getResizeCursors", "mousedrag", "startCoords", "getDiff", "moveCoords", "getSnapGrid", "snapGrid", "x", "y", "get<PERSON><PERSON>", "coords", "Math", "ceil", "previousCoords", "newCoords", "previousGrid", "newGrid", "round", "validateResize", "rectangle", "subscribe", "observers", "run", "emit", "currentRect", "cursor", "document", "body", "setElementClass", "append<PERSON><PERSON><PERSON>", "addClass", "removeClass", "ngOnDestroy", "complete", "next", "add", "ɵfac", "ResizableDirective_Factory", "__ngFactoryType__", "ɵɵdirectiveInject", "Renderer2", "ElementRef", "NgZone", "ɵdir", "ɵɵdefineDirective", "selectors", "inputs", "outputs", "exportAs", "ngDevMode", "ɵsetClassMetadata", "args", "undefined", "decorators", "observer", "unsubscribeMouseDown", "unsubscribeTouchStart", "runOutsideAngular", "listen", "touches", "unsubscribeMouseMove", "unsubscribeTouchMove", "targetTouches", "unsubscribeMouseUp", "unsubscribeTouchEnd", "unsubscribeTouchCancel", "changedTouches", "instance", "ResizeHandleDirective", "resizableDirective", "resizeEdges", "eventListeners", "listenOnTheHost", "onMousedown", "onMouseup", "unsubscribeEventListeners", "touchmove", "touchMoveEvent", "onMousemove", "mouseMoveEvent", "resizable", "resizableContainer", "eventName", "ResizeHandleDirective_Factory", "ResizableModule", "ResizableModule_Factory", "ɵmod", "ɵɵdefineNgModule", "ɵinj", "ɵɵdefineInjector", "declarations", "exports"], "sources": ["C:/Users/<USER>/Documents/jeansalechange/adminFront/node_modules/angular-resizable-element/fesm2020/angular-resizable-element.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { EventEmitter, PLATFORM_ID, Directive, Inject, Input, Output, Optional, NgModule } from '@angular/core';\nimport { isPlatformBrowser } from '@angular/common';\nimport { Subject, merge, Observable, fromEvent } from 'rxjs';\nimport { tap, share, mergeMap, take, map, pairwise, filter, takeUntil } from 'rxjs/operators';\n\n/**\n * @hidden\n */\nconst IS_TOUCH_DEVICE = (() => {\n    // In case we're in Node.js environment.\n    if (typeof window === 'undefined') {\n        return false;\n    }\n    else {\n        return ('ontouchstart' in window ||\n            navigator.maxTouchPoints > 0 ||\n            navigator.msMaxTouchPoints >\n                0);\n    }\n})();\n\n/** Creates a deep clone of an element. */\nfunction deepCloneNode(node) {\n    const clone = node.cloneNode(true);\n    const descendantsWithId = clone.querySelectorAll('[id]');\n    const nodeName = node.nodeName.toLowerCase();\n    // Remove the `id` to avoid having multiple elements with the same id on the page.\n    clone.removeAttribute('id');\n    descendantsWithId.forEach((descendant) => {\n        descendant.removeAttribute('id');\n    });\n    if (nodeName === 'canvas') {\n        transferCanvasData(node, clone);\n    }\n    else if (nodeName === 'input' ||\n        nodeName === 'select' ||\n        nodeName === 'textarea') {\n        transferInputData(node, clone);\n    }\n    transferData('canvas', node, clone, transferCanvasData);\n    transferData('input, textarea, select', node, clone, transferInputData);\n    return clone;\n}\n/** Matches elements between an element and its clone and allows for their data to be cloned. */\nfunction transferData(selector, node, clone, callback) {\n    const descendantElements = node.querySelectorAll(selector);\n    if (descendantElements.length) {\n        const cloneElements = clone.querySelectorAll(selector);\n        for (let i = 0; i < descendantElements.length; i++) {\n            callback(descendantElements[i], cloneElements[i]);\n        }\n    }\n}\n// Counter for unique cloned radio button names.\nlet cloneUniqueId = 0;\n/** Transfers the data of one input element to another. */\nfunction transferInputData(source, clone) {\n    // Browsers throw an error when assigning the value of a file input programmatically.\n    if (clone.type !== 'file') {\n        clone.value = source.value;\n    }\n    // Radio button `name` attributes must be unique for radio button groups\n    // otherwise original radio buttons can lose their checked state\n    // once the clone is inserted in the DOM.\n    if (clone.type === 'radio' && clone.name) {\n        clone.name = `mat-clone-${clone.name}-${cloneUniqueId++}`;\n    }\n}\n/** Transfers the data of one canvas element to another. */\nfunction transferCanvasData(source, clone) {\n    const context = clone.getContext('2d');\n    if (context) {\n        // In some cases `drawImage` can throw (e.g. if the canvas size is 0x0).\n        // We can't do much about it so just ignore the error.\n        try {\n            context.drawImage(source, 0, 0);\n        }\n        catch { }\n    }\n}\n\nfunction getNewBoundingRectangle(startingRect, edges, clientX, clientY) {\n    const newBoundingRect = {\n        top: startingRect.top,\n        bottom: startingRect.bottom,\n        left: startingRect.left,\n        right: startingRect.right,\n    };\n    if (edges.top) {\n        newBoundingRect.top += clientY;\n    }\n    if (edges.bottom) {\n        newBoundingRect.bottom += clientY;\n    }\n    if (edges.left) {\n        newBoundingRect.left += clientX;\n    }\n    if (edges.right) {\n        newBoundingRect.right += clientX;\n    }\n    newBoundingRect.height = newBoundingRect.bottom - newBoundingRect.top;\n    newBoundingRect.width = newBoundingRect.right - newBoundingRect.left;\n    return newBoundingRect;\n}\nfunction getElementRect(element, ghostElementPositioning) {\n    let translateX = 0;\n    let translateY = 0;\n    const style = element.nativeElement.style;\n    const transformProperties = [\n        'transform',\n        '-ms-transform',\n        '-moz-transform',\n        '-o-transform',\n    ];\n    const transform = transformProperties\n        .map((property) => style[property])\n        .find((value) => !!value);\n    if (transform && transform.includes('translate')) {\n        translateX = transform.replace(/.*translate3?d?\\((-?[0-9]*)px, (-?[0-9]*)px.*/, '$1');\n        translateY = transform.replace(/.*translate3?d?\\((-?[0-9]*)px, (-?[0-9]*)px.*/, '$2');\n    }\n    if (ghostElementPositioning === 'absolute') {\n        return {\n            height: element.nativeElement.offsetHeight,\n            width: element.nativeElement.offsetWidth,\n            top: element.nativeElement.offsetTop - translateY,\n            bottom: element.nativeElement.offsetHeight +\n                element.nativeElement.offsetTop -\n                translateY,\n            left: element.nativeElement.offsetLeft - translateX,\n            right: element.nativeElement.offsetWidth +\n                element.nativeElement.offsetLeft -\n                translateX,\n        };\n    }\n    else {\n        const boundingRect = element.nativeElement.getBoundingClientRect();\n        return {\n            height: boundingRect.height,\n            width: boundingRect.width,\n            top: boundingRect.top - translateY,\n            bottom: boundingRect.bottom - translateY,\n            left: boundingRect.left - translateX,\n            right: boundingRect.right - translateX,\n            scrollTop: element.nativeElement.scrollTop,\n            scrollLeft: element.nativeElement.scrollLeft,\n        };\n    }\n}\nconst DEFAULT_RESIZE_CURSORS = Object.freeze({\n    topLeft: 'nw-resize',\n    topRight: 'ne-resize',\n    bottomLeft: 'sw-resize',\n    bottomRight: 'se-resize',\n    leftOrRight: 'col-resize',\n    topOrBottom: 'row-resize',\n});\nfunction getResizeCursor(edges, cursors) {\n    if (edges.left && edges.top) {\n        return cursors.topLeft;\n    }\n    else if (edges.right && edges.top) {\n        return cursors.topRight;\n    }\n    else if (edges.left && edges.bottom) {\n        return cursors.bottomLeft;\n    }\n    else if (edges.right && edges.bottom) {\n        return cursors.bottomRight;\n    }\n    else if (edges.left || edges.right) {\n        return cursors.leftOrRight;\n    }\n    else if (edges.top || edges.bottom) {\n        return cursors.topOrBottom;\n    }\n    else {\n        return '';\n    }\n}\nfunction getEdgesDiff({ edges, initialRectangle, newRectangle, }) {\n    const edgesDiff = {};\n    Object.keys(edges).forEach((edge) => {\n        edgesDiff[edge] = (newRectangle[edge] || 0) - (initialRectangle[edge] || 0);\n    });\n    return edgesDiff;\n}\nconst RESIZE_ACTIVE_CLASS = 'resize-active';\nconst RESIZE_GHOST_ELEMENT_CLASS = 'resize-ghost-element';\nconst MOUSE_MOVE_THROTTLE_MS = 50;\n/**\n * Place this on an element to make it resizable. For example:\n *\n * ```html\n * <div\n *   mwlResizable\n *   [resizeEdges]=\"{bottom: true, right: true, top: true, left: true}\"\n *   [enableGhostResize]=\"true\">\n * </div>\n * ```\n * Or in case they are sibling elements:\n * ```html\n * <div mwlResizable #resizableElement=\"mwlResizable\"></div>\n * <div mwlResizeHandle [resizableContainer]=\"resizableElement\" [resizeEdges]=\"{bottom: true, right: true}\"></div>\n * ```\n */\nclass ResizableDirective {\n    /**\n     * @hidden\n     */\n    constructor(platformId, renderer, elm, zone) {\n        this.platformId = platformId;\n        this.renderer = renderer;\n        this.elm = elm;\n        this.zone = zone;\n        /**\n         * Set to `true` to enable a temporary resizing effect of the element in between the `resizeStart` and `resizeEnd` events.\n         */\n        this.enableGhostResize = false;\n        /**\n         * A snap grid that resize events will be locked to.\n         *\n         * e.g. to only allow the element to be resized every 10px set it to `{left: 10, right: 10}`\n         */\n        this.resizeSnapGrid = {};\n        /**\n         * The mouse cursors that will be set on the resize edges\n         */\n        this.resizeCursors = DEFAULT_RESIZE_CURSORS;\n        /**\n         * Define the positioning of the ghost element (can be fixed or absolute)\n         */\n        this.ghostElementPositioning = 'fixed';\n        /**\n         * Allow elements to be resized to negative dimensions\n         */\n        this.allowNegativeResizes = false;\n        /**\n         * The mouse move throttle in milliseconds, default: 50 ms\n         */\n        this.mouseMoveThrottleMS = MOUSE_MOVE_THROTTLE_MS;\n        /**\n         * Called when the mouse is pressed and a resize event is about to begin. `$event` is a `ResizeEvent` object.\n         */\n        this.resizeStart = new EventEmitter();\n        /**\n         * Called as the mouse is dragged after a resize event has begun. `$event` is a `ResizeEvent` object.\n         */\n        this.resizing = new EventEmitter();\n        /**\n         * Called after the mouse is released after a resize event. `$event` is a `ResizeEvent` object.\n         */\n        this.resizeEnd = new EventEmitter();\n        /**\n         * @hidden\n         */\n        this.mouseup = new Subject();\n        /**\n         * @hidden\n         */\n        this.mousedown = new Subject();\n        /**\n         * @hidden\n         */\n        this.mousemove = new Subject();\n        this.destroy$ = new Subject();\n        this.pointerEventListeners = PointerEventListeners.getInstance(renderer, zone);\n    }\n    /**\n     * @hidden\n     */\n    ngOnInit() {\n        const mousedown$ = merge(this.pointerEventListeners.pointerDown, this.mousedown);\n        const mousemove$ = merge(this.pointerEventListeners.pointerMove, this.mousemove).pipe(tap(({ event }) => {\n            if (currentResize && event.cancelable) {\n                event.preventDefault();\n            }\n        }), share());\n        const mouseup$ = merge(this.pointerEventListeners.pointerUp, this.mouseup);\n        let currentResize;\n        const removeGhostElement = () => {\n            if (currentResize && currentResize.clonedNode) {\n                this.elm.nativeElement.parentElement.removeChild(currentResize.clonedNode);\n                this.renderer.setStyle(this.elm.nativeElement, 'visibility', 'inherit');\n            }\n        };\n        const getResizeCursors = () => {\n            return {\n                ...DEFAULT_RESIZE_CURSORS,\n                ...this.resizeCursors,\n            };\n        };\n        const mousedrag = mousedown$\n            .pipe(mergeMap((startCoords) => {\n            function getDiff(moveCoords) {\n                return {\n                    clientX: moveCoords.clientX - startCoords.clientX,\n                    clientY: moveCoords.clientY - startCoords.clientY,\n                };\n            }\n            const getSnapGrid = () => {\n                const snapGrid = { x: 1, y: 1 };\n                if (currentResize) {\n                    if (this.resizeSnapGrid.left && currentResize.edges.left) {\n                        snapGrid.x = +this.resizeSnapGrid.left;\n                    }\n                    else if (this.resizeSnapGrid.right &&\n                        currentResize.edges.right) {\n                        snapGrid.x = +this.resizeSnapGrid.right;\n                    }\n                    if (this.resizeSnapGrid.top && currentResize.edges.top) {\n                        snapGrid.y = +this.resizeSnapGrid.top;\n                    }\n                    else if (this.resizeSnapGrid.bottom &&\n                        currentResize.edges.bottom) {\n                        snapGrid.y = +this.resizeSnapGrid.bottom;\n                    }\n                }\n                return snapGrid;\n            };\n            function getGrid(coords, snapGrid) {\n                return {\n                    x: Math.ceil(coords.clientX / snapGrid.x),\n                    y: Math.ceil(coords.clientY / snapGrid.y),\n                };\n            }\n            return merge(mousemove$.pipe(take(1)).pipe(map((coords) => [, coords])), mousemove$.pipe(pairwise()))\n                .pipe(map(([previousCoords, newCoords]) => {\n                return [\n                    previousCoords ? getDiff(previousCoords) : previousCoords,\n                    getDiff(newCoords),\n                ];\n            }))\n                .pipe(filter(([previousCoords, newCoords]) => {\n                if (!previousCoords) {\n                    return true;\n                }\n                const snapGrid = getSnapGrid();\n                const previousGrid = getGrid(previousCoords, snapGrid);\n                const newGrid = getGrid(newCoords, snapGrid);\n                return (previousGrid.x !== newGrid.x || previousGrid.y !== newGrid.y);\n            }))\n                .pipe(map(([, newCoords]) => {\n                const snapGrid = getSnapGrid();\n                return {\n                    clientX: Math.round(newCoords.clientX / snapGrid.x) * snapGrid.x,\n                    clientY: Math.round(newCoords.clientY / snapGrid.y) * snapGrid.y,\n                };\n            }))\n                .pipe(takeUntil(merge(mouseup$, mousedown$)));\n        }))\n            .pipe(filter(() => !!currentResize));\n        mousedrag\n            .pipe(map(({ clientX, clientY }) => {\n            return getNewBoundingRectangle(currentResize.startingRect, currentResize.edges, clientX, clientY);\n        }))\n            .pipe(filter((newBoundingRect) => {\n            return (this.allowNegativeResizes ||\n                !!(newBoundingRect.height &&\n                    newBoundingRect.width &&\n                    newBoundingRect.height > 0 &&\n                    newBoundingRect.width > 0));\n        }))\n            .pipe(filter((newBoundingRect) => {\n            return this.validateResize\n                ? this.validateResize({\n                    rectangle: newBoundingRect,\n                    edges: getEdgesDiff({\n                        edges: currentResize.edges,\n                        initialRectangle: currentResize.startingRect,\n                        newRectangle: newBoundingRect,\n                    }),\n                })\n                : true;\n        }), takeUntil(this.destroy$))\n            .subscribe((newBoundingRect) => {\n            if (currentResize && currentResize.clonedNode) {\n                this.renderer.setStyle(currentResize.clonedNode, 'height', `${newBoundingRect.height}px`);\n                this.renderer.setStyle(currentResize.clonedNode, 'width', `${newBoundingRect.width}px`);\n                this.renderer.setStyle(currentResize.clonedNode, 'top', `${newBoundingRect.top}px`);\n                this.renderer.setStyle(currentResize.clonedNode, 'left', `${newBoundingRect.left}px`);\n            }\n            if (this.resizing.observers.length > 0) {\n                this.zone.run(() => {\n                    this.resizing.emit({\n                        edges: getEdgesDiff({\n                            edges: currentResize.edges,\n                            initialRectangle: currentResize.startingRect,\n                            newRectangle: newBoundingRect,\n                        }),\n                        rectangle: newBoundingRect,\n                    });\n                });\n            }\n            currentResize.currentRect = newBoundingRect;\n        });\n        mousedown$\n            .pipe(map(({ edges }) => {\n            return edges || {};\n        }), filter((edges) => {\n            return Object.keys(edges).length > 0;\n        }), takeUntil(this.destroy$))\n            .subscribe((edges) => {\n            if (currentResize) {\n                removeGhostElement();\n            }\n            const startingRect = getElementRect(this.elm, this.ghostElementPositioning);\n            currentResize = {\n                edges,\n                startingRect,\n                currentRect: startingRect,\n            };\n            const resizeCursors = getResizeCursors();\n            const cursor = getResizeCursor(currentResize.edges, resizeCursors);\n            this.renderer.setStyle(document.body, 'cursor', cursor);\n            this.setElementClass(this.elm, RESIZE_ACTIVE_CLASS, true);\n            if (this.enableGhostResize) {\n                currentResize.clonedNode = deepCloneNode(this.elm.nativeElement);\n                this.elm.nativeElement.parentElement.appendChild(currentResize.clonedNode);\n                this.renderer.setStyle(this.elm.nativeElement, 'visibility', 'hidden');\n                this.renderer.setStyle(currentResize.clonedNode, 'position', this.ghostElementPositioning);\n                this.renderer.setStyle(currentResize.clonedNode, 'left', `${currentResize.startingRect.left}px`);\n                this.renderer.setStyle(currentResize.clonedNode, 'top', `${currentResize.startingRect.top}px`);\n                this.renderer.setStyle(currentResize.clonedNode, 'height', `${currentResize.startingRect.height}px`);\n                this.renderer.setStyle(currentResize.clonedNode, 'width', `${currentResize.startingRect.width}px`);\n                this.renderer.setStyle(currentResize.clonedNode, 'cursor', getResizeCursor(currentResize.edges, resizeCursors));\n                this.renderer.addClass(currentResize.clonedNode, RESIZE_GHOST_ELEMENT_CLASS);\n                currentResize.clonedNode.scrollTop = currentResize.startingRect\n                    .scrollTop;\n                currentResize.clonedNode.scrollLeft = currentResize.startingRect\n                    .scrollLeft;\n            }\n            if (this.resizeStart.observers.length > 0) {\n                this.zone.run(() => {\n                    this.resizeStart.emit({\n                        edges: getEdgesDiff({\n                            edges,\n                            initialRectangle: startingRect,\n                            newRectangle: startingRect,\n                        }),\n                        rectangle: getNewBoundingRectangle(startingRect, {}, 0, 0),\n                    });\n                });\n            }\n        });\n        mouseup$.pipe(takeUntil(this.destroy$)).subscribe(() => {\n            if (currentResize) {\n                this.renderer.removeClass(this.elm.nativeElement, RESIZE_ACTIVE_CLASS);\n                this.renderer.setStyle(document.body, 'cursor', '');\n                this.renderer.setStyle(this.elm.nativeElement, 'cursor', '');\n                if (this.resizeEnd.observers.length > 0) {\n                    this.zone.run(() => {\n                        this.resizeEnd.emit({\n                            edges: getEdgesDiff({\n                                edges: currentResize.edges,\n                                initialRectangle: currentResize.startingRect,\n                                newRectangle: currentResize.currentRect,\n                            }),\n                            rectangle: currentResize.currentRect,\n                        });\n                    });\n                }\n                removeGhostElement();\n                currentResize = null;\n            }\n        });\n    }\n    /**\n     * @hidden\n     */\n    ngOnDestroy() {\n        // browser check for angular universal, because it doesn't know what document is\n        if (isPlatformBrowser(this.platformId)) {\n            this.renderer.setStyle(document.body, 'cursor', '');\n        }\n        this.mousedown.complete();\n        this.mouseup.complete();\n        this.mousemove.complete();\n        this.destroy$.next();\n    }\n    setElementClass(elm, name, add) {\n        if (add) {\n            this.renderer.addClass(elm.nativeElement, name);\n        }\n        else {\n            this.renderer.removeClass(elm.nativeElement, name);\n        }\n    }\n}\nResizableDirective.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.0.3\", ngImport: i0, type: ResizableDirective, deps: [{ token: PLATFORM_ID }, { token: i0.Renderer2 }, { token: i0.ElementRef }, { token: i0.NgZone }], target: i0.ɵɵFactoryTarget.Directive });\nResizableDirective.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"15.0.3\", type: ResizableDirective, selector: \"[mwlResizable]\", inputs: { validateResize: \"validateResize\", enableGhostResize: \"enableGhostResize\", resizeSnapGrid: \"resizeSnapGrid\", resizeCursors: \"resizeCursors\", ghostElementPositioning: \"ghostElementPositioning\", allowNegativeResizes: \"allowNegativeResizes\", mouseMoveThrottleMS: \"mouseMoveThrottleMS\" }, outputs: { resizeStart: \"resizeStart\", resizing: \"resizing\", resizeEnd: \"resizeEnd\" }, exportAs: [\"mwlResizable\"], ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.0.3\", ngImport: i0, type: ResizableDirective, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[mwlResizable]',\n                    exportAs: 'mwlResizable',\n                }]\n        }], ctorParameters: function () { return [{ type: undefined, decorators: [{\n                    type: Inject,\n                    args: [PLATFORM_ID]\n                }] }, { type: i0.Renderer2 }, { type: i0.ElementRef }, { type: i0.NgZone }]; }, propDecorators: { validateResize: [{\n                type: Input\n            }], enableGhostResize: [{\n                type: Input\n            }], resizeSnapGrid: [{\n                type: Input\n            }], resizeCursors: [{\n                type: Input\n            }], ghostElementPositioning: [{\n                type: Input\n            }], allowNegativeResizes: [{\n                type: Input\n            }], mouseMoveThrottleMS: [{\n                type: Input\n            }], resizeStart: [{\n                type: Output\n            }], resizing: [{\n                type: Output\n            }], resizeEnd: [{\n                type: Output\n            }] } });\nclass PointerEventListeners {\n    constructor(renderer, zone) {\n        this.pointerDown = new Observable((observer) => {\n            let unsubscribeMouseDown;\n            let unsubscribeTouchStart;\n            zone.runOutsideAngular(() => {\n                unsubscribeMouseDown = renderer.listen('document', 'mousedown', (event) => {\n                    observer.next({\n                        clientX: event.clientX,\n                        clientY: event.clientY,\n                        event,\n                    });\n                });\n                if (IS_TOUCH_DEVICE) {\n                    unsubscribeTouchStart = renderer.listen('document', 'touchstart', (event) => {\n                        observer.next({\n                            clientX: event.touches[0].clientX,\n                            clientY: event.touches[0].clientY,\n                            event,\n                        });\n                    });\n                }\n            });\n            return () => {\n                unsubscribeMouseDown();\n                if (IS_TOUCH_DEVICE) {\n                    unsubscribeTouchStart();\n                }\n            };\n        }).pipe(share());\n        this.pointerMove = new Observable((observer) => {\n            let unsubscribeMouseMove;\n            let unsubscribeTouchMove;\n            zone.runOutsideAngular(() => {\n                unsubscribeMouseMove = renderer.listen('document', 'mousemove', (event) => {\n                    observer.next({\n                        clientX: event.clientX,\n                        clientY: event.clientY,\n                        event,\n                    });\n                });\n                if (IS_TOUCH_DEVICE) {\n                    unsubscribeTouchMove = renderer.listen('document', 'touchmove', (event) => {\n                        observer.next({\n                            clientX: event.targetTouches[0].clientX,\n                            clientY: event.targetTouches[0].clientY,\n                            event,\n                        });\n                    });\n                }\n            });\n            return () => {\n                unsubscribeMouseMove();\n                if (IS_TOUCH_DEVICE) {\n                    unsubscribeTouchMove();\n                }\n            };\n        }).pipe(share());\n        this.pointerUp = new Observable((observer) => {\n            let unsubscribeMouseUp;\n            let unsubscribeTouchEnd;\n            let unsubscribeTouchCancel;\n            zone.runOutsideAngular(() => {\n                unsubscribeMouseUp = renderer.listen('document', 'mouseup', (event) => {\n                    observer.next({\n                        clientX: event.clientX,\n                        clientY: event.clientY,\n                        event,\n                    });\n                });\n                if (IS_TOUCH_DEVICE) {\n                    unsubscribeTouchEnd = renderer.listen('document', 'touchend', (event) => {\n                        observer.next({\n                            clientX: event.changedTouches[0].clientX,\n                            clientY: event.changedTouches[0].clientY,\n                            event,\n                        });\n                    });\n                    unsubscribeTouchCancel = renderer.listen('document', 'touchcancel', (event) => {\n                        observer.next({\n                            clientX: event.changedTouches[0].clientX,\n                            clientY: event.changedTouches[0].clientY,\n                            event,\n                        });\n                    });\n                }\n            });\n            return () => {\n                unsubscribeMouseUp();\n                if (IS_TOUCH_DEVICE) {\n                    unsubscribeTouchEnd();\n                    unsubscribeTouchCancel();\n                }\n            };\n        }).pipe(share());\n    }\n    static getInstance(renderer, zone) {\n        if (!PointerEventListeners.instance) {\n            PointerEventListeners.instance = new PointerEventListeners(renderer, zone);\n        }\n        return PointerEventListeners.instance;\n    }\n}\n\n/**\n * An element placed inside a `mwlResizable` directive to be used as a drag and resize handle\n *\n * For example\n *\n * ```html\n * <div mwlResizable>\n *   <div mwlResizeHandle [resizeEdges]=\"{bottom: true, right: true}\"></div>\n * </div>\n * ```\n * Or in case they are sibling elements:\n * ```html\n * <div mwlResizable #resizableElement=\"mwlResizable\"></div>\n * <div mwlResizeHandle [resizableContainer]=\"resizableElement\" [resizeEdges]=\"{bottom: true, right: true}\"></div>\n * ```\n */\nclass ResizeHandleDirective {\n    constructor(renderer, element, zone, resizableDirective) {\n        this.renderer = renderer;\n        this.element = element;\n        this.zone = zone;\n        this.resizableDirective = resizableDirective;\n        /**\n         * The `Edges` object that contains the edges of the parent element that dragging the handle will trigger a resize on\n         */\n        this.resizeEdges = {};\n        this.eventListeners = {};\n        this.destroy$ = new Subject();\n    }\n    ngOnInit() {\n        this.zone.runOutsideAngular(() => {\n            this.listenOnTheHost('mousedown').subscribe((event) => {\n                this.onMousedown(event, event.clientX, event.clientY);\n            });\n            this.listenOnTheHost('mouseup').subscribe((event) => {\n                this.onMouseup(event.clientX, event.clientY);\n            });\n            if (IS_TOUCH_DEVICE) {\n                this.listenOnTheHost('touchstart').subscribe((event) => {\n                    this.onMousedown(event, event.touches[0].clientX, event.touches[0].clientY);\n                });\n                merge(this.listenOnTheHost('touchend'), this.listenOnTheHost('touchcancel')).subscribe((event) => {\n                    this.onMouseup(event.changedTouches[0].clientX, event.changedTouches[0].clientY);\n                });\n            }\n        });\n    }\n    ngOnDestroy() {\n        this.destroy$.next();\n        this.unsubscribeEventListeners();\n    }\n    /**\n     * @hidden\n     */\n    onMousedown(event, clientX, clientY) {\n        if (event.cancelable) {\n            event.preventDefault();\n        }\n        if (!this.eventListeners.touchmove) {\n            this.eventListeners.touchmove = this.renderer.listen(this.element.nativeElement, 'touchmove', (touchMoveEvent) => {\n                this.onMousemove(touchMoveEvent, touchMoveEvent.targetTouches[0].clientX, touchMoveEvent.targetTouches[0].clientY);\n            });\n        }\n        if (!this.eventListeners.mousemove) {\n            this.eventListeners.mousemove = this.renderer.listen(this.element.nativeElement, 'mousemove', (mouseMoveEvent) => {\n                this.onMousemove(mouseMoveEvent, mouseMoveEvent.clientX, mouseMoveEvent.clientY);\n            });\n        }\n        this.resizable.mousedown.next({\n            clientX,\n            clientY,\n            edges: this.resizeEdges,\n        });\n    }\n    /**\n     * @hidden\n     */\n    onMouseup(clientX, clientY) {\n        this.unsubscribeEventListeners();\n        this.resizable.mouseup.next({\n            clientX,\n            clientY,\n            edges: this.resizeEdges,\n        });\n    }\n    // directive might be passed from DI or as an input\n    get resizable() {\n        return this.resizableDirective || this.resizableContainer;\n    }\n    onMousemove(event, clientX, clientY) {\n        this.resizable.mousemove.next({\n            clientX,\n            clientY,\n            edges: this.resizeEdges,\n            event,\n        });\n    }\n    unsubscribeEventListeners() {\n        Object.keys(this.eventListeners).forEach((type) => {\n            this.eventListeners[type]();\n            delete this.eventListeners[type];\n        });\n    }\n    listenOnTheHost(eventName) {\n        return fromEvent(this.element.nativeElement, eventName).pipe(takeUntil(this.destroy$));\n    }\n}\nResizeHandleDirective.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.0.3\", ngImport: i0, type: ResizeHandleDirective, deps: [{ token: i0.Renderer2 }, { token: i0.ElementRef }, { token: i0.NgZone }, { token: ResizableDirective, optional: true }], target: i0.ɵɵFactoryTarget.Directive });\nResizeHandleDirective.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"15.0.3\", type: ResizeHandleDirective, selector: \"[mwlResizeHandle]\", inputs: { resizeEdges: \"resizeEdges\", resizableContainer: \"resizableContainer\" }, ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.0.3\", ngImport: i0, type: ResizeHandleDirective, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[mwlResizeHandle]',\n                }]\n        }], ctorParameters: function () { return [{ type: i0.Renderer2 }, { type: i0.ElementRef }, { type: i0.NgZone }, { type: ResizableDirective, decorators: [{\n                    type: Optional\n                }] }]; }, propDecorators: { resizeEdges: [{\n                type: Input\n            }], resizableContainer: [{\n                type: Input\n            }] } });\n\nclass ResizableModule {\n}\nResizableModule.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.0.3\", ngImport: i0, type: ResizableModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\nResizableModule.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"15.0.3\", ngImport: i0, type: ResizableModule, declarations: [ResizableDirective, ResizeHandleDirective], exports: [ResizableDirective, ResizeHandleDirective] });\nResizableModule.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"15.0.3\", ngImport: i0, type: ResizableModule });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.0.3\", ngImport: i0, type: ResizableModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    declarations: [ResizableDirective, ResizeHandleDirective],\n                    exports: [ResizableDirective, ResizeHandleDirective],\n                }]\n        }] });\n\n/*\n * Public API Surface of angular-resizable-element\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { ResizableDirective, ResizableModule, ResizeHandleDirective };\n//# sourceMappingURL=angular-resizable-element.mjs.map\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,YAAY,EAAEC,WAAW,EAAEC,SAAS,EAAEC,MAAM,EAAEC,KAAK,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,QAAQ,QAAQ,eAAe;AAC/G,SAASC,iBAAiB,QAAQ,iBAAiB;AACnD,SAASC,OAAO,EAAEC,KAAK,EAAEC,UAAU,EAAEC,SAAS,QAAQ,MAAM;AAC5D,SAASC,GAAG,EAAEC,KAAK,EAAEC,QAAQ,EAAEC,IAAI,EAAEC,GAAG,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,SAAS,QAAQ,gBAAgB;;AAE7F;AACA;AACA;AACA,MAAMC,eAAe,GAAG,CAAC,MAAM;EAC3B;EACA,IAAI,OAAOC,MAAM,KAAK,WAAW,EAAE;IAC/B,OAAO,KAAK;EAChB,CAAC,MACI;IACD,OAAQ,cAAc,IAAIA,MAAM,IAC5BC,SAAS,CAACC,cAAc,GAAG,CAAC,IAC5BD,SAAS,CAACE,gBAAgB,GACtB,CAAC;EACb;AACJ,CAAC,EAAE,CAAC;;AAEJ;AACA,SAASC,aAAaA,CAACC,IAAI,EAAE;EACzB,MAAMC,KAAK,GAAGD,IAAI,CAACE,SAAS,CAAC,IAAI,CAAC;EAClC,MAAMC,iBAAiB,GAAGF,KAAK,CAACG,gBAAgB,CAAC,MAAM,CAAC;EACxD,MAAMC,QAAQ,GAAGL,IAAI,CAACK,QAAQ,CAACC,WAAW,CAAC,CAAC;EAC5C;EACAL,KAAK,CAACM,eAAe,CAAC,IAAI,CAAC;EAC3BJ,iBAAiB,CAACK,OAAO,CAAEC,UAAU,IAAK;IACtCA,UAAU,CAACF,eAAe,CAAC,IAAI,CAAC;EACpC,CAAC,CAAC;EACF,IAAIF,QAAQ,KAAK,QAAQ,EAAE;IACvBK,kBAAkB,CAACV,IAAI,EAAEC,KAAK,CAAC;EACnC,CAAC,MACI,IAAII,QAAQ,KAAK,OAAO,IACzBA,QAAQ,KAAK,QAAQ,IACrBA,QAAQ,KAAK,UAAU,EAAE;IACzBM,iBAAiB,CAACX,IAAI,EAAEC,KAAK,CAAC;EAClC;EACAW,YAAY,CAAC,QAAQ,EAAEZ,IAAI,EAAEC,KAAK,EAAES,kBAAkB,CAAC;EACvDE,YAAY,CAAC,yBAAyB,EAAEZ,IAAI,EAAEC,KAAK,EAAEU,iBAAiB,CAAC;EACvE,OAAOV,KAAK;AAChB;AACA;AACA,SAASW,YAAYA,CAACC,QAAQ,EAAEb,IAAI,EAAEC,KAAK,EAAEa,QAAQ,EAAE;EACnD,MAAMC,kBAAkB,GAAGf,IAAI,CAACI,gBAAgB,CAACS,QAAQ,CAAC;EAC1D,IAAIE,kBAAkB,CAACC,MAAM,EAAE;IAC3B,MAAMC,aAAa,GAAGhB,KAAK,CAACG,gBAAgB,CAACS,QAAQ,CAAC;IACtD,KAAK,IAAIK,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,kBAAkB,CAACC,MAAM,EAAEE,CAAC,EAAE,EAAE;MAChDJ,QAAQ,CAACC,kBAAkB,CAACG,CAAC,CAAC,EAAED,aAAa,CAACC,CAAC,CAAC,CAAC;IACrD;EACJ;AACJ;AACA;AACA,IAAIC,aAAa,GAAG,CAAC;AACrB;AACA,SAASR,iBAAiBA,CAACS,MAAM,EAAEnB,KAAK,EAAE;EACtC;EACA,IAAIA,KAAK,CAACoB,IAAI,KAAK,MAAM,EAAE;IACvBpB,KAAK,CAACqB,KAAK,GAAGF,MAAM,CAACE,KAAK;EAC9B;EACA;EACA;EACA;EACA,IAAIrB,KAAK,CAACoB,IAAI,KAAK,OAAO,IAAIpB,KAAK,CAACsB,IAAI,EAAE;IACtCtB,KAAK,CAACsB,IAAI,GAAG,aAAatB,KAAK,CAACsB,IAAI,IAAIJ,aAAa,EAAE,EAAE;EAC7D;AACJ;AACA;AACA,SAAST,kBAAkBA,CAACU,MAAM,EAAEnB,KAAK,EAAE;EACvC,MAAMuB,OAAO,GAAGvB,KAAK,CAACwB,UAAU,CAAC,IAAI,CAAC;EACtC,IAAID,OAAO,EAAE;IACT;IACA;IACA,IAAI;MACAA,OAAO,CAACE,SAAS,CAACN,MAAM,EAAE,CAAC,EAAE,CAAC,CAAC;IACnC,CAAC,CACD,MAAM,CAAE;EACZ;AACJ;AAEA,SAASO,uBAAuBA,CAACC,YAAY,EAAEC,KAAK,EAAEC,OAAO,EAAEC,OAAO,EAAE;EACpE,MAAMC,eAAe,GAAG;IACpBC,GAAG,EAAEL,YAAY,CAACK,GAAG;IACrBC,MAAM,EAAEN,YAAY,CAACM,MAAM;IAC3BC,IAAI,EAAEP,YAAY,CAACO,IAAI;IACvBC,KAAK,EAAER,YAAY,CAACQ;EACxB,CAAC;EACD,IAAIP,KAAK,CAACI,GAAG,EAAE;IACXD,eAAe,CAACC,GAAG,IAAIF,OAAO;EAClC;EACA,IAAIF,KAAK,CAACK,MAAM,EAAE;IACdF,eAAe,CAACE,MAAM,IAAIH,OAAO;EACrC;EACA,IAAIF,KAAK,CAACM,IAAI,EAAE;IACZH,eAAe,CAACG,IAAI,IAAIL,OAAO;EACnC;EACA,IAAID,KAAK,CAACO,KAAK,EAAE;IACbJ,eAAe,CAACI,KAAK,IAAIN,OAAO;EACpC;EACAE,eAAe,CAACK,MAAM,GAAGL,eAAe,CAACE,MAAM,GAAGF,eAAe,CAACC,GAAG;EACrED,eAAe,CAACM,KAAK,GAAGN,eAAe,CAACI,KAAK,GAAGJ,eAAe,CAACG,IAAI;EACpE,OAAOH,eAAe;AAC1B;AACA,SAASO,cAAcA,CAACC,OAAO,EAAEC,uBAAuB,EAAE;EACtD,IAAIC,UAAU,GAAG,CAAC;EAClB,IAAIC,UAAU,GAAG,CAAC;EAClB,MAAMC,KAAK,GAAGJ,OAAO,CAACK,aAAa,CAACD,KAAK;EACzC,MAAME,mBAAmB,GAAG,CACxB,WAAW,EACX,eAAe,EACf,gBAAgB,EAChB,cAAc,CACjB;EACD,MAAMC,SAAS,GAAGD,mBAAmB,CAChCxD,GAAG,CAAE0D,QAAQ,IAAKJ,KAAK,CAACI,QAAQ,CAAC,CAAC,CAClCC,IAAI,CAAE3B,KAAK,IAAK,CAAC,CAACA,KAAK,CAAC;EAC7B,IAAIyB,SAAS,IAAIA,SAAS,CAACG,QAAQ,CAAC,WAAW,CAAC,EAAE;IAC9CR,UAAU,GAAGK,SAAS,CAACI,OAAO,CAAC,+CAA+C,EAAE,IAAI,CAAC;IACrFR,UAAU,GAAGI,SAAS,CAACI,OAAO,CAAC,+CAA+C,EAAE,IAAI,CAAC;EACzF;EACA,IAAIV,uBAAuB,KAAK,UAAU,EAAE;IACxC,OAAO;MACHJ,MAAM,EAAEG,OAAO,CAACK,aAAa,CAACO,YAAY;MAC1Cd,KAAK,EAAEE,OAAO,CAACK,aAAa,CAACQ,WAAW;MACxCpB,GAAG,EAAEO,OAAO,CAACK,aAAa,CAACS,SAAS,GAAGX,UAAU;MACjDT,MAAM,EAAEM,OAAO,CAACK,aAAa,CAACO,YAAY,GACtCZ,OAAO,CAACK,aAAa,CAACS,SAAS,GAC/BX,UAAU;MACdR,IAAI,EAAEK,OAAO,CAACK,aAAa,CAACU,UAAU,GAAGb,UAAU;MACnDN,KAAK,EAAEI,OAAO,CAACK,aAAa,CAACQ,WAAW,GACpCb,OAAO,CAACK,aAAa,CAACU,UAAU,GAChCb;IACR,CAAC;EACL,CAAC,MACI;IACD,MAAMc,YAAY,GAAGhB,OAAO,CAACK,aAAa,CAACY,qBAAqB,CAAC,CAAC;IAClE,OAAO;MACHpB,MAAM,EAAEmB,YAAY,CAACnB,MAAM;MAC3BC,KAAK,EAAEkB,YAAY,CAAClB,KAAK;MACzBL,GAAG,EAAEuB,YAAY,CAACvB,GAAG,GAAGU,UAAU;MAClCT,MAAM,EAAEsB,YAAY,CAACtB,MAAM,GAAGS,UAAU;MACxCR,IAAI,EAAEqB,YAAY,CAACrB,IAAI,GAAGO,UAAU;MACpCN,KAAK,EAAEoB,YAAY,CAACpB,KAAK,GAAGM,UAAU;MACtCgB,SAAS,EAAElB,OAAO,CAACK,aAAa,CAACa,SAAS;MAC1CC,UAAU,EAAEnB,OAAO,CAACK,aAAa,CAACc;IACtC,CAAC;EACL;AACJ;AACA,MAAMC,sBAAsB,GAAGC,MAAM,CAACC,MAAM,CAAC;EACzCC,OAAO,EAAE,WAAW;EACpBC,QAAQ,EAAE,WAAW;EACrBC,UAAU,EAAE,WAAW;EACvBC,WAAW,EAAE,WAAW;EACxBC,WAAW,EAAE,YAAY;EACzBC,WAAW,EAAE;AACjB,CAAC,CAAC;AACF,SAASC,eAAeA,CAACxC,KAAK,EAAEyC,OAAO,EAAE;EACrC,IAAIzC,KAAK,CAACM,IAAI,IAAIN,KAAK,CAACI,GAAG,EAAE;IACzB,OAAOqC,OAAO,CAACP,OAAO;EAC1B,CAAC,MACI,IAAIlC,KAAK,CAACO,KAAK,IAAIP,KAAK,CAACI,GAAG,EAAE;IAC/B,OAAOqC,OAAO,CAACN,QAAQ;EAC3B,CAAC,MACI,IAAInC,KAAK,CAACM,IAAI,IAAIN,KAAK,CAACK,MAAM,EAAE;IACjC,OAAOoC,OAAO,CAACL,UAAU;EAC7B,CAAC,MACI,IAAIpC,KAAK,CAACO,KAAK,IAAIP,KAAK,CAACK,MAAM,EAAE;IAClC,OAAOoC,OAAO,CAACJ,WAAW;EAC9B,CAAC,MACI,IAAIrC,KAAK,CAACM,IAAI,IAAIN,KAAK,CAACO,KAAK,EAAE;IAChC,OAAOkC,OAAO,CAACH,WAAW;EAC9B,CAAC,MACI,IAAItC,KAAK,CAACI,GAAG,IAAIJ,KAAK,CAACK,MAAM,EAAE;IAChC,OAAOoC,OAAO,CAACF,WAAW;EAC9B,CAAC,MACI;IACD,OAAO,EAAE;EACb;AACJ;AACA,SAASG,YAAYA,CAAC;EAAE1C,KAAK;EAAE2C,gBAAgB;EAAEC;AAAc,CAAC,EAAE;EAC9D,MAAMC,SAAS,GAAG,CAAC,CAAC;EACpBb,MAAM,CAACc,IAAI,CAAC9C,KAAK,CAAC,CAACrB,OAAO,CAAEoE,IAAI,IAAK;IACjCF,SAAS,CAACE,IAAI,CAAC,GAAG,CAACH,YAAY,CAACG,IAAI,CAAC,IAAI,CAAC,KAAKJ,gBAAgB,CAACI,IAAI,CAAC,IAAI,CAAC,CAAC;EAC/E,CAAC,CAAC;EACF,OAAOF,SAAS;AACpB;AACA,MAAMG,mBAAmB,GAAG,eAAe;AAC3C,MAAMC,0BAA0B,GAAG,sBAAsB;AACzD,MAAMC,sBAAsB,GAAG,EAAE;AACjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,kBAAkB,CAAC;EACrB;AACJ;AACA;EACIC,WAAWA,CAACC,UAAU,EAAEC,QAAQ,EAAEC,GAAG,EAAEC,IAAI,EAAE;IACzC,IAAI,CAACH,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACC,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,GAAG,GAAGA,GAAG;IACd,IAAI,CAACC,IAAI,GAAGA,IAAI;IAChB;AACR;AACA;IACQ,IAAI,CAACC,iBAAiB,GAAG,KAAK;IAC9B;AACR;AACA;AACA;AACA;IACQ,IAAI,CAACC,cAAc,GAAG,CAAC,CAAC;IACxB;AACR;AACA;IACQ,IAAI,CAACC,aAAa,GAAG5B,sBAAsB;IAC3C;AACR;AACA;IACQ,IAAI,CAACnB,uBAAuB,GAAG,OAAO;IACtC;AACR;AACA;IACQ,IAAI,CAACgD,oBAAoB,GAAG,KAAK;IACjC;AACR;AACA;IACQ,IAAI,CAACC,mBAAmB,GAAGX,sBAAsB;IACjD;AACR;AACA;IACQ,IAAI,CAACY,WAAW,GAAG,IAAItH,YAAY,CAAC,CAAC;IACrC;AACR;AACA;IACQ,IAAI,CAACuH,QAAQ,GAAG,IAAIvH,YAAY,CAAC,CAAC;IAClC;AACR;AACA;IACQ,IAAI,CAACwH,SAAS,GAAG,IAAIxH,YAAY,CAAC,CAAC;IACnC;AACR;AACA;IACQ,IAAI,CAACyH,OAAO,GAAG,IAAIhH,OAAO,CAAC,CAAC;IAC5B;AACR;AACA;IACQ,IAAI,CAACiH,SAAS,GAAG,IAAIjH,OAAO,CAAC,CAAC;IAC9B;AACR;AACA;IACQ,IAAI,CAACkH,SAAS,GAAG,IAAIlH,OAAO,CAAC,CAAC;IAC9B,IAAI,CAACmH,QAAQ,GAAG,IAAInH,OAAO,CAAC,CAAC;IAC7B,IAAI,CAACoH,qBAAqB,GAAGC,qBAAqB,CAACC,WAAW,CAACjB,QAAQ,EAAEE,IAAI,CAAC;EAClF;EACA;AACJ;AACA;EACIgB,QAAQA,CAAA,EAAG;IACP,MAAMC,UAAU,GAAGvH,KAAK,CAAC,IAAI,CAACmH,qBAAqB,CAACK,WAAW,EAAE,IAAI,CAACR,SAAS,CAAC;IAChF,MAAMS,UAAU,GAAGzH,KAAK,CAAC,IAAI,CAACmH,qBAAqB,CAACO,WAAW,EAAE,IAAI,CAACT,SAAS,CAAC,CAACU,IAAI,CAACxH,GAAG,CAAC,CAAC;MAAEyH;IAAM,CAAC,KAAK;MACrG,IAAIC,aAAa,IAAID,KAAK,CAACE,UAAU,EAAE;QACnCF,KAAK,CAACG,cAAc,CAAC,CAAC;MAC1B;IACJ,CAAC,CAAC,EAAE3H,KAAK,CAAC,CAAC,CAAC;IACZ,MAAM4H,QAAQ,GAAGhI,KAAK,CAAC,IAAI,CAACmH,qBAAqB,CAACc,SAAS,EAAE,IAAI,CAAClB,OAAO,CAAC;IAC1E,IAAIc,aAAa;IACjB,MAAMK,kBAAkB,GAAGA,CAAA,KAAM;MAC7B,IAAIL,aAAa,IAAIA,aAAa,CAACM,UAAU,EAAE;QAC3C,IAAI,CAAC9B,GAAG,CAACvC,aAAa,CAACsE,aAAa,CAACC,WAAW,CAACR,aAAa,CAACM,UAAU,CAAC;QAC1E,IAAI,CAAC/B,QAAQ,CAACkC,QAAQ,CAAC,IAAI,CAACjC,GAAG,CAACvC,aAAa,EAAE,YAAY,EAAE,SAAS,CAAC;MAC3E;IACJ,CAAC;IACD,MAAMyE,gBAAgB,GAAGA,CAAA,KAAM;MAC3B,OAAO;QACH,GAAG1D,sBAAsB;QACzB,GAAG,IAAI,CAAC4B;MACZ,CAAC;IACL,CAAC;IACD,MAAM+B,SAAS,GAAGjB,UAAU,CACvBI,IAAI,CAACtH,QAAQ,CAAEoI,WAAW,IAAK;MAChC,SAASC,OAAOA,CAACC,UAAU,EAAE;QACzB,OAAO;UACH5F,OAAO,EAAE4F,UAAU,CAAC5F,OAAO,GAAG0F,WAAW,CAAC1F,OAAO;UACjDC,OAAO,EAAE2F,UAAU,CAAC3F,OAAO,GAAGyF,WAAW,CAACzF;QAC9C,CAAC;MACL;MACA,MAAM4F,WAAW,GAAGA,CAAA,KAAM;QACtB,MAAMC,QAAQ,GAAG;UAAEC,CAAC,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAC;QAC/B,IAAIlB,aAAa,EAAE;UACf,IAAI,IAAI,CAACrB,cAAc,CAACpD,IAAI,IAAIyE,aAAa,CAAC/E,KAAK,CAACM,IAAI,EAAE;YACtDyF,QAAQ,CAACC,CAAC,GAAG,CAAC,IAAI,CAACtC,cAAc,CAACpD,IAAI;UAC1C,CAAC,MACI,IAAI,IAAI,CAACoD,cAAc,CAACnD,KAAK,IAC9BwE,aAAa,CAAC/E,KAAK,CAACO,KAAK,EAAE;YAC3BwF,QAAQ,CAACC,CAAC,GAAG,CAAC,IAAI,CAACtC,cAAc,CAACnD,KAAK;UAC3C;UACA,IAAI,IAAI,CAACmD,cAAc,CAACtD,GAAG,IAAI2E,aAAa,CAAC/E,KAAK,CAACI,GAAG,EAAE;YACpD2F,QAAQ,CAACE,CAAC,GAAG,CAAC,IAAI,CAACvC,cAAc,CAACtD,GAAG;UACzC,CAAC,MACI,IAAI,IAAI,CAACsD,cAAc,CAACrD,MAAM,IAC/B0E,aAAa,CAAC/E,KAAK,CAACK,MAAM,EAAE;YAC5B0F,QAAQ,CAACE,CAAC,GAAG,CAAC,IAAI,CAACvC,cAAc,CAACrD,MAAM;UAC5C;QACJ;QACA,OAAO0F,QAAQ;MACnB,CAAC;MACD,SAASG,OAAOA,CAACC,MAAM,EAAEJ,QAAQ,EAAE;QAC/B,OAAO;UACHC,CAAC,EAAEI,IAAI,CAACC,IAAI,CAACF,MAAM,CAAClG,OAAO,GAAG8F,QAAQ,CAACC,CAAC,CAAC;UACzCC,CAAC,EAAEG,IAAI,CAACC,IAAI,CAACF,MAAM,CAACjG,OAAO,GAAG6F,QAAQ,CAACE,CAAC;QAC5C,CAAC;MACL;MACA,OAAO/I,KAAK,CAACyH,UAAU,CAACE,IAAI,CAACrH,IAAI,CAAC,CAAC,CAAC,CAAC,CAACqH,IAAI,CAACpH,GAAG,CAAE0I,MAAM,IAAK,GAAGA,MAAM,CAAC,CAAC,CAAC,EAAExB,UAAU,CAACE,IAAI,CAACnH,QAAQ,CAAC,CAAC,CAAC,CAAC,CAChGmH,IAAI,CAACpH,GAAG,CAAC,CAAC,CAAC6I,cAAc,EAAEC,SAAS,CAAC,KAAK;QAC3C,OAAO,CACHD,cAAc,GAAGV,OAAO,CAACU,cAAc,CAAC,GAAGA,cAAc,EACzDV,OAAO,CAACW,SAAS,CAAC,CACrB;MACL,CAAC,CAAC,CAAC,CACE1B,IAAI,CAAClH,MAAM,CAAC,CAAC,CAAC2I,cAAc,EAAEC,SAAS,CAAC,KAAK;QAC9C,IAAI,CAACD,cAAc,EAAE;UACjB,OAAO,IAAI;QACf;QACA,MAAMP,QAAQ,GAAGD,WAAW,CAAC,CAAC;QAC9B,MAAMU,YAAY,GAAGN,OAAO,CAACI,cAAc,EAAEP,QAAQ,CAAC;QACtD,MAAMU,OAAO,GAAGP,OAAO,CAACK,SAAS,EAAER,QAAQ,CAAC;QAC5C,OAAQS,YAAY,CAACR,CAAC,KAAKS,OAAO,CAACT,CAAC,IAAIQ,YAAY,CAACP,CAAC,KAAKQ,OAAO,CAACR,CAAC;MACxE,CAAC,CAAC,CAAC,CACEpB,IAAI,CAACpH,GAAG,CAAC,CAAC,GAAG8I,SAAS,CAAC,KAAK;QAC7B,MAAMR,QAAQ,GAAGD,WAAW,CAAC,CAAC;QAC9B,OAAO;UACH7F,OAAO,EAAEmG,IAAI,CAACM,KAAK,CAACH,SAAS,CAACtG,OAAO,GAAG8F,QAAQ,CAACC,CAAC,CAAC,GAAGD,QAAQ,CAACC,CAAC;UAChE9F,OAAO,EAAEkG,IAAI,CAACM,KAAK,CAACH,SAAS,CAACrG,OAAO,GAAG6F,QAAQ,CAACE,CAAC,CAAC,GAAGF,QAAQ,CAACE;QACnE,CAAC;MACL,CAAC,CAAC,CAAC,CACEpB,IAAI,CAACjH,SAAS,CAACV,KAAK,CAACgI,QAAQ,EAAET,UAAU,CAAC,CAAC,CAAC;IACrD,CAAC,CAAC,CAAC,CACEI,IAAI,CAAClH,MAAM,CAAC,MAAM,CAAC,CAACoH,aAAa,CAAC,CAAC;IACxCW,SAAS,CACJb,IAAI,CAACpH,GAAG,CAAC,CAAC;MAAEwC,OAAO;MAAEC;IAAQ,CAAC,KAAK;MACpC,OAAOJ,uBAAuB,CAACiF,aAAa,CAAChF,YAAY,EAAEgF,aAAa,CAAC/E,KAAK,EAAEC,OAAO,EAAEC,OAAO,CAAC;IACrG,CAAC,CAAC,CAAC,CACE2E,IAAI,CAAClH,MAAM,CAAEwC,eAAe,IAAK;MAClC,OAAQ,IAAI,CAACyD,oBAAoB,IAC7B,CAAC,EAAEzD,eAAe,CAACK,MAAM,IACrBL,eAAe,CAACM,KAAK,IACrBN,eAAe,CAACK,MAAM,GAAG,CAAC,IAC1BL,eAAe,CAACM,KAAK,GAAG,CAAC,CAAC;IACtC,CAAC,CAAC,CAAC,CACEoE,IAAI,CAAClH,MAAM,CAAEwC,eAAe,IAAK;MAClC,OAAO,IAAI,CAACwG,cAAc,GACpB,IAAI,CAACA,cAAc,CAAC;QAClBC,SAAS,EAAEzG,eAAe;QAC1BH,KAAK,EAAE0C,YAAY,CAAC;UAChB1C,KAAK,EAAE+E,aAAa,CAAC/E,KAAK;UAC1B2C,gBAAgB,EAAEoC,aAAa,CAAChF,YAAY;UAC5C6C,YAAY,EAAEzC;QAClB,CAAC;MACL,CAAC,CAAC,GACA,IAAI;IACd,CAAC,CAAC,EAAEvC,SAAS,CAAC,IAAI,CAACwG,QAAQ,CAAC,CAAC,CACxByC,SAAS,CAAE1G,eAAe,IAAK;MAChC,IAAI4E,aAAa,IAAIA,aAAa,CAACM,UAAU,EAAE;QAC3C,IAAI,CAAC/B,QAAQ,CAACkC,QAAQ,CAACT,aAAa,CAACM,UAAU,EAAE,QAAQ,EAAE,GAAGlF,eAAe,CAACK,MAAM,IAAI,CAAC;QACzF,IAAI,CAAC8C,QAAQ,CAACkC,QAAQ,CAACT,aAAa,CAACM,UAAU,EAAE,OAAO,EAAE,GAAGlF,eAAe,CAACM,KAAK,IAAI,CAAC;QACvF,IAAI,CAAC6C,QAAQ,CAACkC,QAAQ,CAACT,aAAa,CAACM,UAAU,EAAE,KAAK,EAAE,GAAGlF,eAAe,CAACC,GAAG,IAAI,CAAC;QACnF,IAAI,CAACkD,QAAQ,CAACkC,QAAQ,CAACT,aAAa,CAACM,UAAU,EAAE,MAAM,EAAE,GAAGlF,eAAe,CAACG,IAAI,IAAI,CAAC;MACzF;MACA,IAAI,IAAI,CAACyD,QAAQ,CAAC+C,SAAS,CAAC3H,MAAM,GAAG,CAAC,EAAE;QACpC,IAAI,CAACqE,IAAI,CAACuD,GAAG,CAAC,MAAM;UAChB,IAAI,CAAChD,QAAQ,CAACiD,IAAI,CAAC;YACfhH,KAAK,EAAE0C,YAAY,CAAC;cAChB1C,KAAK,EAAE+E,aAAa,CAAC/E,KAAK;cAC1B2C,gBAAgB,EAAEoC,aAAa,CAAChF,YAAY;cAC5C6C,YAAY,EAAEzC;YAClB,CAAC,CAAC;YACFyG,SAAS,EAAEzG;UACf,CAAC,CAAC;QACN,CAAC,CAAC;MACN;MACA4E,aAAa,CAACkC,WAAW,GAAG9G,eAAe;IAC/C,CAAC,CAAC;IACFsE,UAAU,CACLI,IAAI,CAACpH,GAAG,CAAC,CAAC;MAAEuC;IAAM,CAAC,KAAK;MACzB,OAAOA,KAAK,IAAI,CAAC,CAAC;IACtB,CAAC,CAAC,EAAErC,MAAM,CAAEqC,KAAK,IAAK;MAClB,OAAOgC,MAAM,CAACc,IAAI,CAAC9C,KAAK,CAAC,CAACb,MAAM,GAAG,CAAC;IACxC,CAAC,CAAC,EAAEvB,SAAS,CAAC,IAAI,CAACwG,QAAQ,CAAC,CAAC,CACxByC,SAAS,CAAE7G,KAAK,IAAK;MACtB,IAAI+E,aAAa,EAAE;QACfK,kBAAkB,CAAC,CAAC;MACxB;MACA,MAAMrF,YAAY,GAAGW,cAAc,CAAC,IAAI,CAAC6C,GAAG,EAAE,IAAI,CAAC3C,uBAAuB,CAAC;MAC3EmE,aAAa,GAAG;QACZ/E,KAAK;QACLD,YAAY;QACZkH,WAAW,EAAElH;MACjB,CAAC;MACD,MAAM4D,aAAa,GAAG8B,gBAAgB,CAAC,CAAC;MACxC,MAAMyB,MAAM,GAAG1E,eAAe,CAACuC,aAAa,CAAC/E,KAAK,EAAE2D,aAAa,CAAC;MAClE,IAAI,CAACL,QAAQ,CAACkC,QAAQ,CAAC2B,QAAQ,CAACC,IAAI,EAAE,QAAQ,EAAEF,MAAM,CAAC;MACvD,IAAI,CAACG,eAAe,CAAC,IAAI,CAAC9D,GAAG,EAAEP,mBAAmB,EAAE,IAAI,CAAC;MACzD,IAAI,IAAI,CAACS,iBAAiB,EAAE;QACxBsB,aAAa,CAACM,UAAU,GAAGnH,aAAa,CAAC,IAAI,CAACqF,GAAG,CAACvC,aAAa,CAAC;QAChE,IAAI,CAACuC,GAAG,CAACvC,aAAa,CAACsE,aAAa,CAACgC,WAAW,CAACvC,aAAa,CAACM,UAAU,CAAC;QAC1E,IAAI,CAAC/B,QAAQ,CAACkC,QAAQ,CAAC,IAAI,CAACjC,GAAG,CAACvC,aAAa,EAAE,YAAY,EAAE,QAAQ,CAAC;QACtE,IAAI,CAACsC,QAAQ,CAACkC,QAAQ,CAACT,aAAa,CAACM,UAAU,EAAE,UAAU,EAAE,IAAI,CAACzE,uBAAuB,CAAC;QAC1F,IAAI,CAAC0C,QAAQ,CAACkC,QAAQ,CAACT,aAAa,CAACM,UAAU,EAAE,MAAM,EAAE,GAAGN,aAAa,CAAChF,YAAY,CAACO,IAAI,IAAI,CAAC;QAChG,IAAI,CAACgD,QAAQ,CAACkC,QAAQ,CAACT,aAAa,CAACM,UAAU,EAAE,KAAK,EAAE,GAAGN,aAAa,CAAChF,YAAY,CAACK,GAAG,IAAI,CAAC;QAC9F,IAAI,CAACkD,QAAQ,CAACkC,QAAQ,CAACT,aAAa,CAACM,UAAU,EAAE,QAAQ,EAAE,GAAGN,aAAa,CAAChF,YAAY,CAACS,MAAM,IAAI,CAAC;QACpG,IAAI,CAAC8C,QAAQ,CAACkC,QAAQ,CAACT,aAAa,CAACM,UAAU,EAAE,OAAO,EAAE,GAAGN,aAAa,CAAChF,YAAY,CAACU,KAAK,IAAI,CAAC;QAClG,IAAI,CAAC6C,QAAQ,CAACkC,QAAQ,CAACT,aAAa,CAACM,UAAU,EAAE,QAAQ,EAAE7C,eAAe,CAACuC,aAAa,CAAC/E,KAAK,EAAE2D,aAAa,CAAC,CAAC;QAC/G,IAAI,CAACL,QAAQ,CAACiE,QAAQ,CAACxC,aAAa,CAACM,UAAU,EAAEpC,0BAA0B,CAAC;QAC5E8B,aAAa,CAACM,UAAU,CAACxD,SAAS,GAAGkD,aAAa,CAAChF,YAAY,CAC1D8B,SAAS;QACdkD,aAAa,CAACM,UAAU,CAACvD,UAAU,GAAGiD,aAAa,CAAChF,YAAY,CAC3D+B,UAAU;MACnB;MACA,IAAI,IAAI,CAACgC,WAAW,CAACgD,SAAS,CAAC3H,MAAM,GAAG,CAAC,EAAE;QACvC,IAAI,CAACqE,IAAI,CAACuD,GAAG,CAAC,MAAM;UAChB,IAAI,CAACjD,WAAW,CAACkD,IAAI,CAAC;YAClBhH,KAAK,EAAE0C,YAAY,CAAC;cAChB1C,KAAK;cACL2C,gBAAgB,EAAE5C,YAAY;cAC9B6C,YAAY,EAAE7C;YAClB,CAAC,CAAC;YACF6G,SAAS,EAAE9G,uBAAuB,CAACC,YAAY,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;UAC7D,CAAC,CAAC;QACN,CAAC,CAAC;MACN;IACJ,CAAC,CAAC;IACFmF,QAAQ,CAACL,IAAI,CAACjH,SAAS,CAAC,IAAI,CAACwG,QAAQ,CAAC,CAAC,CAACyC,SAAS,CAAC,MAAM;MACpD,IAAI9B,aAAa,EAAE;QACf,IAAI,CAACzB,QAAQ,CAACkE,WAAW,CAAC,IAAI,CAACjE,GAAG,CAACvC,aAAa,EAAEgC,mBAAmB,CAAC;QACtE,IAAI,CAACM,QAAQ,CAACkC,QAAQ,CAAC2B,QAAQ,CAACC,IAAI,EAAE,QAAQ,EAAE,EAAE,CAAC;QACnD,IAAI,CAAC9D,QAAQ,CAACkC,QAAQ,CAAC,IAAI,CAACjC,GAAG,CAACvC,aAAa,EAAE,QAAQ,EAAE,EAAE,CAAC;QAC5D,IAAI,IAAI,CAACgD,SAAS,CAAC8C,SAAS,CAAC3H,MAAM,GAAG,CAAC,EAAE;UACrC,IAAI,CAACqE,IAAI,CAACuD,GAAG,CAAC,MAAM;YAChB,IAAI,CAAC/C,SAAS,CAACgD,IAAI,CAAC;cAChBhH,KAAK,EAAE0C,YAAY,CAAC;gBAChB1C,KAAK,EAAE+E,aAAa,CAAC/E,KAAK;gBAC1B2C,gBAAgB,EAAEoC,aAAa,CAAChF,YAAY;gBAC5C6C,YAAY,EAAEmC,aAAa,CAACkC;cAChC,CAAC,CAAC;cACFL,SAAS,EAAE7B,aAAa,CAACkC;YAC7B,CAAC,CAAC;UACN,CAAC,CAAC;QACN;QACA7B,kBAAkB,CAAC,CAAC;QACpBL,aAAa,GAAG,IAAI;MACxB;IACJ,CAAC,CAAC;EACN;EACA;AACJ;AACA;EACI0C,WAAWA,CAAA,EAAG;IACV;IACA,IAAIzK,iBAAiB,CAAC,IAAI,CAACqG,UAAU,CAAC,EAAE;MACpC,IAAI,CAACC,QAAQ,CAACkC,QAAQ,CAAC2B,QAAQ,CAACC,IAAI,EAAE,QAAQ,EAAE,EAAE,CAAC;IACvD;IACA,IAAI,CAAClD,SAAS,CAACwD,QAAQ,CAAC,CAAC;IACzB,IAAI,CAACzD,OAAO,CAACyD,QAAQ,CAAC,CAAC;IACvB,IAAI,CAACvD,SAAS,CAACuD,QAAQ,CAAC,CAAC;IACzB,IAAI,CAACtD,QAAQ,CAACuD,IAAI,CAAC,CAAC;EACxB;EACAN,eAAeA,CAAC9D,GAAG,EAAE7D,IAAI,EAAEkI,GAAG,EAAE;IAC5B,IAAIA,GAAG,EAAE;MACL,IAAI,CAACtE,QAAQ,CAACiE,QAAQ,CAAChE,GAAG,CAACvC,aAAa,EAAEtB,IAAI,CAAC;IACnD,CAAC,MACI;MACD,IAAI,CAAC4D,QAAQ,CAACkE,WAAW,CAACjE,GAAG,CAACvC,aAAa,EAAEtB,IAAI,CAAC;IACtD;EACJ;AACJ;AACAyD,kBAAkB,CAAC0E,IAAI,YAAAC,2BAAAC,iBAAA;EAAA,YAAAA,iBAAA,IAAwF5E,kBAAkB,EAA5B5G,EAAE,CAAAyL,iBAAA,CAA4CvL,WAAW,GAAzDF,EAAE,CAAAyL,iBAAA,CAAoEzL,EAAE,CAAC0L,SAAS,GAAlF1L,EAAE,CAAAyL,iBAAA,CAA6FzL,EAAE,CAAC2L,UAAU,GAA5G3L,EAAE,CAAAyL,iBAAA,CAAuHzL,EAAE,CAAC4L,MAAM;AAAA,CAA4C;AACnRhF,kBAAkB,CAACiF,IAAI,kBAD8E7L,EAAE,CAAA8L,iBAAA;EAAA7I,IAAA,EACJ2D,kBAAkB;EAAAmF,SAAA;EAAAC,MAAA;IAAA5B,cAAA;IAAAlD,iBAAA;IAAAC,cAAA;IAAAC,aAAA;IAAA/C,uBAAA;IAAAgD,oBAAA;IAAAC,mBAAA;EAAA;EAAA2E,OAAA;IAAA1E,WAAA;IAAAC,QAAA;IAAAC,SAAA;EAAA;EAAAyE,QAAA;AAAA,EAAsc;AAC3jB;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAFqGnM,EAAE,CAAAoM,iBAAA,CAEZxF,kBAAkB,EAAc,CAAC;IAChH3D,IAAI,EAAE9C,SAAS;IACfkM,IAAI,EAAE,CAAC;MACC5J,QAAQ,EAAE,gBAAgB;MAC1ByJ,QAAQ,EAAE;IACd,CAAC;EACT,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEjJ,IAAI,EAAEqJ,SAAS;MAAEC,UAAU,EAAE,CAAC;QAC9DtJ,IAAI,EAAE7C,MAAM;QACZiM,IAAI,EAAE,CAACnM,WAAW;MACtB,CAAC;IAAE,CAAC,EAAE;MAAE+C,IAAI,EAAEjD,EAAE,CAAC0L;IAAU,CAAC,EAAE;MAAEzI,IAAI,EAAEjD,EAAE,CAAC2L;IAAW,CAAC,EAAE;MAAE1I,IAAI,EAAEjD,EAAE,CAAC4L;IAAO,CAAC,CAAC;EAAE,CAAC,EAAkB;IAAExB,cAAc,EAAE,CAAC;MACnHnH,IAAI,EAAE5C;IACV,CAAC,CAAC;IAAE6G,iBAAiB,EAAE,CAAC;MACpBjE,IAAI,EAAE5C;IACV,CAAC,CAAC;IAAE8G,cAAc,EAAE,CAAC;MACjBlE,IAAI,EAAE5C;IACV,CAAC,CAAC;IAAE+G,aAAa,EAAE,CAAC;MAChBnE,IAAI,EAAE5C;IACV,CAAC,CAAC;IAAEgE,uBAAuB,EAAE,CAAC;MAC1BpB,IAAI,EAAE5C;IACV,CAAC,CAAC;IAAEgH,oBAAoB,EAAE,CAAC;MACvBpE,IAAI,EAAE5C;IACV,CAAC,CAAC;IAAEiH,mBAAmB,EAAE,CAAC;MACtBrE,IAAI,EAAE5C;IACV,CAAC,CAAC;IAAEkH,WAAW,EAAE,CAAC;MACdtE,IAAI,EAAE3C;IACV,CAAC,CAAC;IAAEkH,QAAQ,EAAE,CAAC;MACXvE,IAAI,EAAE3C;IACV,CAAC,CAAC;IAAEmH,SAAS,EAAE,CAAC;MACZxE,IAAI,EAAE3C;IACV,CAAC;EAAE,CAAC;AAAA;AAChB,MAAMyH,qBAAqB,CAAC;EACxBlB,WAAWA,CAACE,QAAQ,EAAEE,IAAI,EAAE;IACxB,IAAI,CAACkB,WAAW,GAAG,IAAIvH,UAAU,CAAE4L,QAAQ,IAAK;MAC5C,IAAIC,oBAAoB;MACxB,IAAIC,qBAAqB;MACzBzF,IAAI,CAAC0F,iBAAiB,CAAC,MAAM;QACzBF,oBAAoB,GAAG1F,QAAQ,CAAC6F,MAAM,CAAC,UAAU,EAAE,WAAW,EAAGrE,KAAK,IAAK;UACvEiE,QAAQ,CAACpB,IAAI,CAAC;YACV1H,OAAO,EAAE6E,KAAK,CAAC7E,OAAO;YACtBC,OAAO,EAAE4E,KAAK,CAAC5E,OAAO;YACtB4E;UACJ,CAAC,CAAC;QACN,CAAC,CAAC;QACF,IAAIjH,eAAe,EAAE;UACjBoL,qBAAqB,GAAG3F,QAAQ,CAAC6F,MAAM,CAAC,UAAU,EAAE,YAAY,EAAGrE,KAAK,IAAK;YACzEiE,QAAQ,CAACpB,IAAI,CAAC;cACV1H,OAAO,EAAE6E,KAAK,CAACsE,OAAO,CAAC,CAAC,CAAC,CAACnJ,OAAO;cACjCC,OAAO,EAAE4E,KAAK,CAACsE,OAAO,CAAC,CAAC,CAAC,CAAClJ,OAAO;cACjC4E;YACJ,CAAC,CAAC;UACN,CAAC,CAAC;QACN;MACJ,CAAC,CAAC;MACF,OAAO,MAAM;QACTkE,oBAAoB,CAAC,CAAC;QACtB,IAAInL,eAAe,EAAE;UACjBoL,qBAAqB,CAAC,CAAC;QAC3B;MACJ,CAAC;IACL,CAAC,CAAC,CAACpE,IAAI,CAACvH,KAAK,CAAC,CAAC,CAAC;IAChB,IAAI,CAACsH,WAAW,GAAG,IAAIzH,UAAU,CAAE4L,QAAQ,IAAK;MAC5C,IAAIM,oBAAoB;MACxB,IAAIC,oBAAoB;MACxB9F,IAAI,CAAC0F,iBAAiB,CAAC,MAAM;QACzBG,oBAAoB,GAAG/F,QAAQ,CAAC6F,MAAM,CAAC,UAAU,EAAE,WAAW,EAAGrE,KAAK,IAAK;UACvEiE,QAAQ,CAACpB,IAAI,CAAC;YACV1H,OAAO,EAAE6E,KAAK,CAAC7E,OAAO;YACtBC,OAAO,EAAE4E,KAAK,CAAC5E,OAAO;YACtB4E;UACJ,CAAC,CAAC;QACN,CAAC,CAAC;QACF,IAAIjH,eAAe,EAAE;UACjByL,oBAAoB,GAAGhG,QAAQ,CAAC6F,MAAM,CAAC,UAAU,EAAE,WAAW,EAAGrE,KAAK,IAAK;YACvEiE,QAAQ,CAACpB,IAAI,CAAC;cACV1H,OAAO,EAAE6E,KAAK,CAACyE,aAAa,CAAC,CAAC,CAAC,CAACtJ,OAAO;cACvCC,OAAO,EAAE4E,KAAK,CAACyE,aAAa,CAAC,CAAC,CAAC,CAACrJ,OAAO;cACvC4E;YACJ,CAAC,CAAC;UACN,CAAC,CAAC;QACN;MACJ,CAAC,CAAC;MACF,OAAO,MAAM;QACTuE,oBAAoB,CAAC,CAAC;QACtB,IAAIxL,eAAe,EAAE;UACjByL,oBAAoB,CAAC,CAAC;QAC1B;MACJ,CAAC;IACL,CAAC,CAAC,CAACzE,IAAI,CAACvH,KAAK,CAAC,CAAC,CAAC;IAChB,IAAI,CAAC6H,SAAS,GAAG,IAAIhI,UAAU,CAAE4L,QAAQ,IAAK;MAC1C,IAAIS,kBAAkB;MACtB,IAAIC,mBAAmB;MACvB,IAAIC,sBAAsB;MAC1BlG,IAAI,CAAC0F,iBAAiB,CAAC,MAAM;QACzBM,kBAAkB,GAAGlG,QAAQ,CAAC6F,MAAM,CAAC,UAAU,EAAE,SAAS,EAAGrE,KAAK,IAAK;UACnEiE,QAAQ,CAACpB,IAAI,CAAC;YACV1H,OAAO,EAAE6E,KAAK,CAAC7E,OAAO;YACtBC,OAAO,EAAE4E,KAAK,CAAC5E,OAAO;YACtB4E;UACJ,CAAC,CAAC;QACN,CAAC,CAAC;QACF,IAAIjH,eAAe,EAAE;UACjB4L,mBAAmB,GAAGnG,QAAQ,CAAC6F,MAAM,CAAC,UAAU,EAAE,UAAU,EAAGrE,KAAK,IAAK;YACrEiE,QAAQ,CAACpB,IAAI,CAAC;cACV1H,OAAO,EAAE6E,KAAK,CAAC6E,cAAc,CAAC,CAAC,CAAC,CAAC1J,OAAO;cACxCC,OAAO,EAAE4E,KAAK,CAAC6E,cAAc,CAAC,CAAC,CAAC,CAACzJ,OAAO;cACxC4E;YACJ,CAAC,CAAC;UACN,CAAC,CAAC;UACF4E,sBAAsB,GAAGpG,QAAQ,CAAC6F,MAAM,CAAC,UAAU,EAAE,aAAa,EAAGrE,KAAK,IAAK;YAC3EiE,QAAQ,CAACpB,IAAI,CAAC;cACV1H,OAAO,EAAE6E,KAAK,CAAC6E,cAAc,CAAC,CAAC,CAAC,CAAC1J,OAAO;cACxCC,OAAO,EAAE4E,KAAK,CAAC6E,cAAc,CAAC,CAAC,CAAC,CAACzJ,OAAO;cACxC4E;YACJ,CAAC,CAAC;UACN,CAAC,CAAC;QACN;MACJ,CAAC,CAAC;MACF,OAAO,MAAM;QACT0E,kBAAkB,CAAC,CAAC;QACpB,IAAI3L,eAAe,EAAE;UACjB4L,mBAAmB,CAAC,CAAC;UACrBC,sBAAsB,CAAC,CAAC;QAC5B;MACJ,CAAC;IACL,CAAC,CAAC,CAAC7E,IAAI,CAACvH,KAAK,CAAC,CAAC,CAAC;EACpB;EACA,OAAOiH,WAAWA,CAACjB,QAAQ,EAAEE,IAAI,EAAE;IAC/B,IAAI,CAACc,qBAAqB,CAACsF,QAAQ,EAAE;MACjCtF,qBAAqB,CAACsF,QAAQ,GAAG,IAAItF,qBAAqB,CAAChB,QAAQ,EAAEE,IAAI,CAAC;IAC9E;IACA,OAAOc,qBAAqB,CAACsF,QAAQ;EACzC;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,qBAAqB,CAAC;EACxBzG,WAAWA,CAACE,QAAQ,EAAE3C,OAAO,EAAE6C,IAAI,EAAEsG,kBAAkB,EAAE;IACrD,IAAI,CAACxG,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAAC3C,OAAO,GAAGA,OAAO;IACtB,IAAI,CAAC6C,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACsG,kBAAkB,GAAGA,kBAAkB;IAC5C;AACR;AACA;IACQ,IAAI,CAACC,WAAW,GAAG,CAAC,CAAC;IACrB,IAAI,CAACC,cAAc,GAAG,CAAC,CAAC;IACxB,IAAI,CAAC5F,QAAQ,GAAG,IAAInH,OAAO,CAAC,CAAC;EACjC;EACAuH,QAAQA,CAAA,EAAG;IACP,IAAI,CAAChB,IAAI,CAAC0F,iBAAiB,CAAC,MAAM;MAC9B,IAAI,CAACe,eAAe,CAAC,WAAW,CAAC,CAACpD,SAAS,CAAE/B,KAAK,IAAK;QACnD,IAAI,CAACoF,WAAW,CAACpF,KAAK,EAAEA,KAAK,CAAC7E,OAAO,EAAE6E,KAAK,CAAC5E,OAAO,CAAC;MACzD,CAAC,CAAC;MACF,IAAI,CAAC+J,eAAe,CAAC,SAAS,CAAC,CAACpD,SAAS,CAAE/B,KAAK,IAAK;QACjD,IAAI,CAACqF,SAAS,CAACrF,KAAK,CAAC7E,OAAO,EAAE6E,KAAK,CAAC5E,OAAO,CAAC;MAChD,CAAC,CAAC;MACF,IAAIrC,eAAe,EAAE;QACjB,IAAI,CAACoM,eAAe,CAAC,YAAY,CAAC,CAACpD,SAAS,CAAE/B,KAAK,IAAK;UACpD,IAAI,CAACoF,WAAW,CAACpF,KAAK,EAAEA,KAAK,CAACsE,OAAO,CAAC,CAAC,CAAC,CAACnJ,OAAO,EAAE6E,KAAK,CAACsE,OAAO,CAAC,CAAC,CAAC,CAAClJ,OAAO,CAAC;QAC/E,CAAC,CAAC;QACFhD,KAAK,CAAC,IAAI,CAAC+M,eAAe,CAAC,UAAU,CAAC,EAAE,IAAI,CAACA,eAAe,CAAC,aAAa,CAAC,CAAC,CAACpD,SAAS,CAAE/B,KAAK,IAAK;UAC9F,IAAI,CAACqF,SAAS,CAACrF,KAAK,CAAC6E,cAAc,CAAC,CAAC,CAAC,CAAC1J,OAAO,EAAE6E,KAAK,CAAC6E,cAAc,CAAC,CAAC,CAAC,CAACzJ,OAAO,CAAC;QACpF,CAAC,CAAC;MACN;IACJ,CAAC,CAAC;EACN;EACAuH,WAAWA,CAAA,EAAG;IACV,IAAI,CAACrD,QAAQ,CAACuD,IAAI,CAAC,CAAC;IACpB,IAAI,CAACyC,yBAAyB,CAAC,CAAC;EACpC;EACA;AACJ;AACA;EACIF,WAAWA,CAACpF,KAAK,EAAE7E,OAAO,EAAEC,OAAO,EAAE;IACjC,IAAI4E,KAAK,CAACE,UAAU,EAAE;MAClBF,KAAK,CAACG,cAAc,CAAC,CAAC;IAC1B;IACA,IAAI,CAAC,IAAI,CAAC+E,cAAc,CAACK,SAAS,EAAE;MAChC,IAAI,CAACL,cAAc,CAACK,SAAS,GAAG,IAAI,CAAC/G,QAAQ,CAAC6F,MAAM,CAAC,IAAI,CAACxI,OAAO,CAACK,aAAa,EAAE,WAAW,EAAGsJ,cAAc,IAAK;QAC9G,IAAI,CAACC,WAAW,CAACD,cAAc,EAAEA,cAAc,CAACf,aAAa,CAAC,CAAC,CAAC,CAACtJ,OAAO,EAAEqK,cAAc,CAACf,aAAa,CAAC,CAAC,CAAC,CAACrJ,OAAO,CAAC;MACtH,CAAC,CAAC;IACN;IACA,IAAI,CAAC,IAAI,CAAC8J,cAAc,CAAC7F,SAAS,EAAE;MAChC,IAAI,CAAC6F,cAAc,CAAC7F,SAAS,GAAG,IAAI,CAACb,QAAQ,CAAC6F,MAAM,CAAC,IAAI,CAACxI,OAAO,CAACK,aAAa,EAAE,WAAW,EAAGwJ,cAAc,IAAK;QAC9G,IAAI,CAACD,WAAW,CAACC,cAAc,EAAEA,cAAc,CAACvK,OAAO,EAAEuK,cAAc,CAACtK,OAAO,CAAC;MACpF,CAAC,CAAC;IACN;IACA,IAAI,CAACuK,SAAS,CAACvG,SAAS,CAACyD,IAAI,CAAC;MAC1B1H,OAAO;MACPC,OAAO;MACPF,KAAK,EAAE,IAAI,CAAC+J;IAChB,CAAC,CAAC;EACN;EACA;AACJ;AACA;EACII,SAASA,CAAClK,OAAO,EAAEC,OAAO,EAAE;IACxB,IAAI,CAACkK,yBAAyB,CAAC,CAAC;IAChC,IAAI,CAACK,SAAS,CAACxG,OAAO,CAAC0D,IAAI,CAAC;MACxB1H,OAAO;MACPC,OAAO;MACPF,KAAK,EAAE,IAAI,CAAC+J;IAChB,CAAC,CAAC;EACN;EACA;EACA,IAAIU,SAASA,CAAA,EAAG;IACZ,OAAO,IAAI,CAACX,kBAAkB,IAAI,IAAI,CAACY,kBAAkB;EAC7D;EACAH,WAAWA,CAACzF,KAAK,EAAE7E,OAAO,EAAEC,OAAO,EAAE;IACjC,IAAI,CAACuK,SAAS,CAACtG,SAAS,CAACwD,IAAI,CAAC;MAC1B1H,OAAO;MACPC,OAAO;MACPF,KAAK,EAAE,IAAI,CAAC+J,WAAW;MACvBjF;IACJ,CAAC,CAAC;EACN;EACAsF,yBAAyBA,CAAA,EAAG;IACxBpI,MAAM,CAACc,IAAI,CAAC,IAAI,CAACkH,cAAc,CAAC,CAACrL,OAAO,CAAEa,IAAI,IAAK;MAC/C,IAAI,CAACwK,cAAc,CAACxK,IAAI,CAAC,CAAC,CAAC;MAC3B,OAAO,IAAI,CAACwK,cAAc,CAACxK,IAAI,CAAC;IACpC,CAAC,CAAC;EACN;EACAyK,eAAeA,CAACU,SAAS,EAAE;IACvB,OAAOvN,SAAS,CAAC,IAAI,CAACuD,OAAO,CAACK,aAAa,EAAE2J,SAAS,CAAC,CAAC9F,IAAI,CAACjH,SAAS,CAAC,IAAI,CAACwG,QAAQ,CAAC,CAAC;EAC1F;AACJ;AACAyF,qBAAqB,CAAChC,IAAI,YAAA+C,8BAAA7C,iBAAA;EAAA,YAAAA,iBAAA,IAAwF8B,qBAAqB,EAnPlCtN,EAAE,CAAAyL,iBAAA,CAmPkDzL,EAAE,CAAC0L,SAAS,GAnPhE1L,EAAE,CAAAyL,iBAAA,CAmP2EzL,EAAE,CAAC2L,UAAU,GAnP1F3L,EAAE,CAAAyL,iBAAA,CAmPqGzL,EAAE,CAAC4L,MAAM,GAnPhH5L,EAAE,CAAAyL,iBAAA,CAmP2H7E,kBAAkB;AAAA,CAA4D;AAChT0G,qBAAqB,CAACzB,IAAI,kBApP2E7L,EAAE,CAAA8L,iBAAA;EAAA7I,IAAA,EAoPDqK,qBAAqB;EAAAvB,SAAA;EAAAC,MAAA;IAAAwB,WAAA;IAAAW,kBAAA;EAAA;AAAA,EAAkI;AAC7P;EAAA,QAAAhC,SAAA,oBAAAA,SAAA,KArPqGnM,EAAE,CAAAoM,iBAAA,CAqPZkB,qBAAqB,EAAc,CAAC;IACnHrK,IAAI,EAAE9C,SAAS;IACfkM,IAAI,EAAE,CAAC;MACC5J,QAAQ,EAAE;IACd,CAAC;EACT,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEQ,IAAI,EAAEjD,EAAE,CAAC0L;IAAU,CAAC,EAAE;MAAEzI,IAAI,EAAEjD,EAAE,CAAC2L;IAAW,CAAC,EAAE;MAAE1I,IAAI,EAAEjD,EAAE,CAAC4L;IAAO,CAAC,EAAE;MAAE3I,IAAI,EAAE2D,kBAAkB;MAAE2F,UAAU,EAAE,CAAC;QAC7ItJ,IAAI,EAAE1C;MACV,CAAC;IAAE,CAAC,CAAC;EAAE,CAAC,EAAkB;IAAEiN,WAAW,EAAE,CAAC;MAC1CvK,IAAI,EAAE5C;IACV,CAAC,CAAC;IAAE8N,kBAAkB,EAAE,CAAC;MACrBlL,IAAI,EAAE5C;IACV,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAMiO,eAAe,CAAC;AAEtBA,eAAe,CAAChD,IAAI,YAAAiD,wBAAA/C,iBAAA;EAAA,YAAAA,iBAAA,IAAwF8C,eAAe;AAAA,CAAkD;AAC7KA,eAAe,CAACE,IAAI,kBArQiFxO,EAAE,CAAAyO,gBAAA;EAAAxL,IAAA,EAqQMqL;AAAe,EAAoH;AAChPA,eAAe,CAACI,IAAI,kBAtQiF1O,EAAE,CAAA2O,gBAAA,IAsQwB;AAC/H;EAAA,QAAAxC,SAAA,oBAAAA,SAAA,KAvQqGnM,EAAE,CAAAoM,iBAAA,CAuQZkC,eAAe,EAAc,CAAC;IAC7GrL,IAAI,EAAEzC,QAAQ;IACd6L,IAAI,EAAE,CAAC;MACCuC,YAAY,EAAE,CAAChI,kBAAkB,EAAE0G,qBAAqB,CAAC;MACzDuB,OAAO,EAAE,CAACjI,kBAAkB,EAAE0G,qBAAqB;IACvD,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;;AAEA;AACA;AACA;;AAEA,SAAS1G,kBAAkB,EAAE0H,eAAe,EAAEhB,qBAAqB;AACnE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}