{"ast": null, "code": "//! moment.js locale configuration\n//! locale : Swedish [sv]\n//! author : <PERSON><PERSON> : https://github.com/ulmus\n\n;\n(function (global, factory) {\n  typeof exports === 'object' && typeof module !== 'undefined' && typeof require === 'function' ? factory(require('../moment')) : typeof define === 'function' && define.amd ? define(['../moment'], factory) : factory(global.moment);\n})(this, function (moment) {\n  'use strict';\n\n  //! moment.js locale configuration\n  var sv = moment.defineLocale('sv', {\n    months: 'januari_februari_mars_april_maj_juni_juli_augusti_september_oktober_november_december'.split('_'),\n    monthsShort: 'jan_feb_mar_apr_maj_jun_jul_aug_sep_okt_nov_dec'.split('_'),\n    weekdays: 'söndag_måndag_tisdag_onsdag_torsdag_fredag_lördag'.split('_'),\n    weekdaysShort: 'sön_mån_tis_ons_tor_fre_lör'.split('_'),\n    weekdaysMin: 'sö_må_ti_on_to_fr_lö'.split('_'),\n    longDateFormat: {\n      LT: 'HH:mm',\n      LTS: 'HH:mm:ss',\n      L: 'YYYY-MM-DD',\n      LL: 'D MMMM YYYY',\n      LLL: 'D MMMM YYYY [kl.] HH:mm',\n      LLLL: 'dddd D MMMM YYYY [kl.] HH:mm',\n      lll: 'D MMM YYYY HH:mm',\n      llll: 'ddd D MMM YYYY HH:mm'\n    },\n    calendar: {\n      sameDay: '[Idag] LT',\n      nextDay: '[Imorgon] LT',\n      lastDay: '[Igår] LT',\n      nextWeek: '[På] dddd LT',\n      lastWeek: '[I] dddd[s] LT',\n      sameElse: 'L'\n    },\n    relativeTime: {\n      future: 'om %s',\n      past: 'för %s sedan',\n      s: 'några sekunder',\n      ss: '%d sekunder',\n      m: 'en minut',\n      mm: '%d minuter',\n      h: 'en timme',\n      hh: '%d timmar',\n      d: 'en dag',\n      dd: '%d dagar',\n      M: 'en månad',\n      MM: '%d månader',\n      y: 'ett år',\n      yy: '%d år'\n    },\n    dayOfMonthOrdinalParse: /\\d{1,2}(\\:e|\\:a)/,\n    ordinal: function (number) {\n      var b = number % 10,\n        output = ~~(number % 100 / 10) === 1 ? ':e' : b === 1 ? ':a' : b === 2 ? ':a' : b === 3 ? ':e' : ':e';\n      return number + output;\n    },\n    week: {\n      dow: 1,\n      // Monday is the first day of the week.\n      doy: 4 // The week that contains Jan 4th is the first week of the year.\n    }\n  });\n  return sv;\n});", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}