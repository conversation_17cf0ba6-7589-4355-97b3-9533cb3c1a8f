{"ast": null, "code": "import { secondsInDay, secondsInHour, secondsInMinute, secondsInMonth, secondsInQuarter, secondsInWeek, secondsInYear } from \"../constants/index.js\";\nimport differenceInCalendarDays from \"../differenceInCalendarDays/index.js\";\nimport differenceInCalendarMonths from \"../differenceInCalendarMonths/index.js\";\nimport differenceInCalendarQuarters from \"../differenceInCalendarQuarters/index.js\";\nimport differenceInCalendarWeeks from \"../differenceInCalendarWeeks/index.js\";\nimport differenceInCalendarYears from \"../differenceInCalendarYears/index.js\";\nimport differenceInHours from \"../differenceInHours/index.js\";\nimport differenceInMinutes from \"../differenceInMinutes/index.js\";\nimport differenceInSeconds from \"../differenceInSeconds/index.js\";\nimport toDate from \"../toDate/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name intlFormatDistance\n * @category Common Helpers\n * @summary Formats distance between two dates in a human-readable format\n * @description\n * The function calculates the difference between two dates and formats it as a human-readable string.\n *\n * The function will pick the most appropriate unit depending on the distance between dates. For example, if the distance is a few hours, it might return `x hours`. If the distance is a few months, it might return `x months`.\n *\n * You can also specify a unit to force using it regardless of the distance to get a result like `123456 hours`.\n *\n * See the table below for the unit picking logic:\n *\n * | Distance between dates | Result (past)  | Result (future) |\n * | ---------------------- | -------------- | --------------- |\n * | 0 seconds              | now            | now             |\n * | 1-59 seconds           | X seconds ago  | in X seconds    |\n * | 1-59 minutes           | X minutes ago  | in X minutes    |\n * | 1-23 hours             | X hours ago    | in X hours      |\n * | 1 day                  | yesterday      | tomorrow        |\n * | 2-6 days               | X days ago     | in X days       |\n * | 7 days                 | last week      | next week       |\n * | 8 days-1 month         | X weeks ago    | in X weeks      |\n * | 1 month                | last month     | next month      |\n * | 2-3 months             | X months ago   | in X months     |\n * | 1 quarter              | last quarter   | next quarter    |\n * | 2-3 quarters           | X quarters ago | in X quarters   |\n * | 1 year                 | last year      | next year       |\n * | 2+ years               | X years ago    | in X years      |\n *\n * @param {Date|Number} date - the date\n * @param {Date|Number} baseDate - the date to compare with.\n * @param {Object} [options] - an object with options.\n * @param {String} [options.unit] - formats the distance with the given unit ('year', 'quarter', 'month', 'week', 'day', 'hour', 'minute', 'second').\n * @param {String|String[]} [options.locale] - the locale to use.\n * @param {String} [options.localeMatcher='best fit'] - the locale matching algorithm to use. Other value: 'lookup'.\n * See MDN for details [Locale identification and negotiation](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Intl#locale_identification_and_negotiation)\n * @param {String} [options.numeric='auto'] - the output message format. The values are 'auto' (e.g. `yesterday`), 'always'(e.g. `1 day ago`).\n * @param {String} [options.style='long'] - the length of the result. The values are: 'long' (e.g. `1 month`), 'short' (e.g. 'in 1 mo.'), 'narrow' (e.g. 'in 1 mo.').\n * The narrow one could be similar to the short one for some locales.\n * @returns {String} the distance in words according to language-sensitive relative time formatting.\n * @throws {TypeError} 2 arguments required\n * @throws {RangeError} `date` must not be Invalid Date\n * @throws {RangeError} `baseDate` must not be Invalid Date\n * @throws {RangeError} `options.unit` must not be invalid Unit\n * @throws {RangeError} `options.locale` must not be invalid locale\n * @throws {RangeError} `options.localeMatcher` must not be invalid localeMatcher\n * @throws {RangeError} `options.numeric` must not be invalid numeric\n * @throws {RangeError} `options.style` must not be invalid style\n *\n * @example\n * // What is the distance between the dates when the fist date is after the second?\n * intlFormatDistance(\n *   new Date(1986, 3, 4, 11, 30, 0),\n *   new Date(1986, 3, 4, 10, 30, 0)\n * )\n * //=> 'in 1 hour'\n *\n * // What is the distance between the dates when the fist date is before the second?\n * intlFormatDistance(\n *   new Date(1986, 3, 4, 10, 30, 0),\n *   new Date(1986, 3, 4, 11, 30, 0)\n * )\n * //=> '1 hour ago'\n *\n * @example\n * // Use the unit option to force the function to output the result in quarters. Without setting it, the example would return \"next year\"\n * intlFormatDistance(\n *   new Date(1987, 6, 4, 10, 30, 0),\n *   new Date(1986, 3, 4, 10, 30, 0),\n *   { unit: 'quarter' }\n * )\n * //=> 'in 5 quarters'\n *\n * @example\n * // Use the locale option to get the result in Spanish. Without setting it, the example would return \"in 1 hour\".\n * intlFormatDistance(\n *   new Date(1986, 3, 4, 11, 30, 0),\n *   new Date(1986, 3, 4, 10, 30, 0),\n *   { locale: 'es' }\n * )\n * //=> 'dentro de 1 hora'\n *\n * @example\n * // Use the numeric option to force the function to use numeric values. Without setting it, the example would return \"tomorrow\".\n * intlFormatDistance(\n *   new Date(1986, 3, 5, 11, 30, 0),\n *   new Date(1986, 3, 4, 11, 30, 0),\n *   { numeric: 'always' }\n * )\n * //=> 'in 1 day'\n *\n * @example\n * // Use the style option to force the function to use short values. Without setting it, the example would return \"in 2 years\".\n * intlFormatDistance(\n *   new Date(1988, 3, 4, 11, 30, 0),\n *   new Date(1986, 3, 4, 11, 30, 0),\n *   { style: 'short' }\n * )\n * //=> 'in 2 yr'\n */\nexport default function intlFormatDistance(date, baseDate, options) {\n  requiredArgs(2, arguments);\n  var value = 0;\n  var unit;\n  var dateLeft = toDate(date);\n  var dateRight = toDate(baseDate);\n  if (!(options !== null && options !== void 0 && options.unit)) {\n    // Get the unit based on diffInSeconds calculations if no unit is specified\n    var diffInSeconds = differenceInSeconds(dateLeft, dateRight); // The smallest unit\n\n    if (Math.abs(diffInSeconds) < secondsInMinute) {\n      value = differenceInSeconds(dateLeft, dateRight);\n      unit = 'second';\n    } else if (Math.abs(diffInSeconds) < secondsInHour) {\n      value = differenceInMinutes(dateLeft, dateRight);\n      unit = 'minute';\n    } else if (Math.abs(diffInSeconds) < secondsInDay && Math.abs(differenceInCalendarDays(dateLeft, dateRight)) < 1) {\n      value = differenceInHours(dateLeft, dateRight);\n      unit = 'hour';\n    } else if (Math.abs(diffInSeconds) < secondsInWeek && (value = differenceInCalendarDays(dateLeft, dateRight)) && Math.abs(value) < 7) {\n      unit = 'day';\n    } else if (Math.abs(diffInSeconds) < secondsInMonth) {\n      value = differenceInCalendarWeeks(dateLeft, dateRight);\n      unit = 'week';\n    } else if (Math.abs(diffInSeconds) < secondsInQuarter) {\n      value = differenceInCalendarMonths(dateLeft, dateRight);\n      unit = 'month';\n    } else if (Math.abs(diffInSeconds) < secondsInYear) {\n      if (differenceInCalendarQuarters(dateLeft, dateRight) < 4) {\n        // To filter out cases that are less than a year but match 4 quarters\n        value = differenceInCalendarQuarters(dateLeft, dateRight);\n        unit = 'quarter';\n      } else {\n        value = differenceInCalendarYears(dateLeft, dateRight);\n        unit = 'year';\n      }\n    } else {\n      value = differenceInCalendarYears(dateLeft, dateRight);\n      unit = 'year';\n    }\n  } else {\n    // Get the value if unit is specified\n    unit = options === null || options === void 0 ? void 0 : options.unit;\n    if (unit === 'second') {\n      value = differenceInSeconds(dateLeft, dateRight);\n    } else if (unit === 'minute') {\n      value = differenceInMinutes(dateLeft, dateRight);\n    } else if (unit === 'hour') {\n      value = differenceInHours(dateLeft, dateRight);\n    } else if (unit === 'day') {\n      value = differenceInCalendarDays(dateLeft, dateRight);\n    } else if (unit === 'week') {\n      value = differenceInCalendarWeeks(dateLeft, dateRight);\n    } else if (unit === 'month') {\n      value = differenceInCalendarMonths(dateLeft, dateRight);\n    } else if (unit === 'quarter') {\n      value = differenceInCalendarQuarters(dateLeft, dateRight);\n    } else if (unit === 'year') {\n      value = differenceInCalendarYears(dateLeft, dateRight);\n    }\n  }\n  var rtf = new Intl.RelativeTimeFormat(options === null || options === void 0 ? void 0 : options.locale, {\n    localeMatcher: options === null || options === void 0 ? void 0 : options.localeMatcher,\n    numeric: (options === null || options === void 0 ? void 0 : options.numeric) || 'auto',\n    style: options === null || options === void 0 ? void 0 : options.style\n  });\n  return rtf.format(value, unit);\n}", "map": {"version": 3, "names": ["secondsInDay", "secondsInHour", "secondsInMinute", "secondsIn<PERSON><PERSON><PERSON>", "secondsInQuarter", "secondsInWeek", "secondsInYear", "differenceInCalendarDays", "differenceInCalendarMonths", "differenceInCalendarQuarters", "differenceInCalendarWeeks", "differenceInCalendarYears", "differenceInHours", "differenceInMinutes", "differenceInSeconds", "toDate", "requiredArgs", "intlFormatDistance", "date", "baseDate", "options", "arguments", "value", "unit", "dateLeft", "dateRight", "diffInSeconds", "Math", "abs", "rtf", "Intl", "RelativeTimeFormat", "locale", "localeMatcher", "numeric", "style", "format"], "sources": ["C:/Users/<USER>/Documents/jeansalechange/adminFront/node_modules/date-fns/esm/intlFormatDistance/index.js"], "sourcesContent": ["import { secondsInDay, secondsInHour, secondsInMinute, secondsInMonth, secondsInQuarter, secondsInWeek, secondsInYear } from \"../constants/index.js\";\nimport differenceInCalendarDays from \"../differenceInCalendarDays/index.js\";\nimport differenceInCalendarMonths from \"../differenceInCalendarMonths/index.js\";\nimport differenceInCalendarQuarters from \"../differenceInCalendarQuarters/index.js\";\nimport differenceInCalendarWeeks from \"../differenceInCalendarWeeks/index.js\";\nimport differenceInCalendarYears from \"../differenceInCalendarYears/index.js\";\nimport differenceInHours from \"../differenceInHours/index.js\";\nimport differenceInMinutes from \"../differenceInMinutes/index.js\";\nimport differenceInSeconds from \"../differenceInSeconds/index.js\";\nimport toDate from \"../toDate/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name intlFormatDistance\n * @category Common Helpers\n * @summary Formats distance between two dates in a human-readable format\n * @description\n * The function calculates the difference between two dates and formats it as a human-readable string.\n *\n * The function will pick the most appropriate unit depending on the distance between dates. For example, if the distance is a few hours, it might return `x hours`. If the distance is a few months, it might return `x months`.\n *\n * You can also specify a unit to force using it regardless of the distance to get a result like `123456 hours`.\n *\n * See the table below for the unit picking logic:\n *\n * | Distance between dates | Result (past)  | Result (future) |\n * | ---------------------- | -------------- | --------------- |\n * | 0 seconds              | now            | now             |\n * | 1-59 seconds           | X seconds ago  | in X seconds    |\n * | 1-59 minutes           | X minutes ago  | in X minutes    |\n * | 1-23 hours             | X hours ago    | in X hours      |\n * | 1 day                  | yesterday      | tomorrow        |\n * | 2-6 days               | X days ago     | in X days       |\n * | 7 days                 | last week      | next week       |\n * | 8 days-1 month         | X weeks ago    | in X weeks      |\n * | 1 month                | last month     | next month      |\n * | 2-3 months             | X months ago   | in X months     |\n * | 1 quarter              | last quarter   | next quarter    |\n * | 2-3 quarters           | X quarters ago | in X quarters   |\n * | 1 year                 | last year      | next year       |\n * | 2+ years               | X years ago    | in X years      |\n *\n * @param {Date|Number} date - the date\n * @param {Date|Number} baseDate - the date to compare with.\n * @param {Object} [options] - an object with options.\n * @param {String} [options.unit] - formats the distance with the given unit ('year', 'quarter', 'month', 'week', 'day', 'hour', 'minute', 'second').\n * @param {String|String[]} [options.locale] - the locale to use.\n * @param {String} [options.localeMatcher='best fit'] - the locale matching algorithm to use. Other value: 'lookup'.\n * See MDN for details [Locale identification and negotiation](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Intl#locale_identification_and_negotiation)\n * @param {String} [options.numeric='auto'] - the output message format. The values are 'auto' (e.g. `yesterday`), 'always'(e.g. `1 day ago`).\n * @param {String} [options.style='long'] - the length of the result. The values are: 'long' (e.g. `1 month`), 'short' (e.g. 'in 1 mo.'), 'narrow' (e.g. 'in 1 mo.').\n * The narrow one could be similar to the short one for some locales.\n * @returns {String} the distance in words according to language-sensitive relative time formatting.\n * @throws {TypeError} 2 arguments required\n * @throws {RangeError} `date` must not be Invalid Date\n * @throws {RangeError} `baseDate` must not be Invalid Date\n * @throws {RangeError} `options.unit` must not be invalid Unit\n * @throws {RangeError} `options.locale` must not be invalid locale\n * @throws {RangeError} `options.localeMatcher` must not be invalid localeMatcher\n * @throws {RangeError} `options.numeric` must not be invalid numeric\n * @throws {RangeError} `options.style` must not be invalid style\n *\n * @example\n * // What is the distance between the dates when the fist date is after the second?\n * intlFormatDistance(\n *   new Date(1986, 3, 4, 11, 30, 0),\n *   new Date(1986, 3, 4, 10, 30, 0)\n * )\n * //=> 'in 1 hour'\n *\n * // What is the distance between the dates when the fist date is before the second?\n * intlFormatDistance(\n *   new Date(1986, 3, 4, 10, 30, 0),\n *   new Date(1986, 3, 4, 11, 30, 0)\n * )\n * //=> '1 hour ago'\n *\n * @example\n * // Use the unit option to force the function to output the result in quarters. Without setting it, the example would return \"next year\"\n * intlFormatDistance(\n *   new Date(1987, 6, 4, 10, 30, 0),\n *   new Date(1986, 3, 4, 10, 30, 0),\n *   { unit: 'quarter' }\n * )\n * //=> 'in 5 quarters'\n *\n * @example\n * // Use the locale option to get the result in Spanish. Without setting it, the example would return \"in 1 hour\".\n * intlFormatDistance(\n *   new Date(1986, 3, 4, 11, 30, 0),\n *   new Date(1986, 3, 4, 10, 30, 0),\n *   { locale: 'es' }\n * )\n * //=> 'dentro de 1 hora'\n *\n * @example\n * // Use the numeric option to force the function to use numeric values. Without setting it, the example would return \"tomorrow\".\n * intlFormatDistance(\n *   new Date(1986, 3, 5, 11, 30, 0),\n *   new Date(1986, 3, 4, 11, 30, 0),\n *   { numeric: 'always' }\n * )\n * //=> 'in 1 day'\n *\n * @example\n * // Use the style option to force the function to use short values. Without setting it, the example would return \"in 2 years\".\n * intlFormatDistance(\n *   new Date(1988, 3, 4, 11, 30, 0),\n *   new Date(1986, 3, 4, 11, 30, 0),\n *   { style: 'short' }\n * )\n * //=> 'in 2 yr'\n */\nexport default function intlFormatDistance(date, baseDate, options) {\n  requiredArgs(2, arguments);\n  var value = 0;\n  var unit;\n  var dateLeft = toDate(date);\n  var dateRight = toDate(baseDate);\n  if (!(options !== null && options !== void 0 && options.unit)) {\n    // Get the unit based on diffInSeconds calculations if no unit is specified\n    var diffInSeconds = differenceInSeconds(dateLeft, dateRight); // The smallest unit\n\n    if (Math.abs(diffInSeconds) < secondsInMinute) {\n      value = differenceInSeconds(dateLeft, dateRight);\n      unit = 'second';\n    } else if (Math.abs(diffInSeconds) < secondsInHour) {\n      value = differenceInMinutes(dateLeft, dateRight);\n      unit = 'minute';\n    } else if (Math.abs(diffInSeconds) < secondsInDay && Math.abs(differenceInCalendarDays(dateLeft, dateRight)) < 1) {\n      value = differenceInHours(dateLeft, dateRight);\n      unit = 'hour';\n    } else if (Math.abs(diffInSeconds) < secondsInWeek && (value = differenceInCalendarDays(dateLeft, dateRight)) && Math.abs(value) < 7) {\n      unit = 'day';\n    } else if (Math.abs(diffInSeconds) < secondsInMonth) {\n      value = differenceInCalendarWeeks(dateLeft, dateRight);\n      unit = 'week';\n    } else if (Math.abs(diffInSeconds) < secondsInQuarter) {\n      value = differenceInCalendarMonths(dateLeft, dateRight);\n      unit = 'month';\n    } else if (Math.abs(diffInSeconds) < secondsInYear) {\n      if (differenceInCalendarQuarters(dateLeft, dateRight) < 4) {\n        // To filter out cases that are less than a year but match 4 quarters\n        value = differenceInCalendarQuarters(dateLeft, dateRight);\n        unit = 'quarter';\n      } else {\n        value = differenceInCalendarYears(dateLeft, dateRight);\n        unit = 'year';\n      }\n    } else {\n      value = differenceInCalendarYears(dateLeft, dateRight);\n      unit = 'year';\n    }\n  } else {\n    // Get the value if unit is specified\n    unit = options === null || options === void 0 ? void 0 : options.unit;\n    if (unit === 'second') {\n      value = differenceInSeconds(dateLeft, dateRight);\n    } else if (unit === 'minute') {\n      value = differenceInMinutes(dateLeft, dateRight);\n    } else if (unit === 'hour') {\n      value = differenceInHours(dateLeft, dateRight);\n    } else if (unit === 'day') {\n      value = differenceInCalendarDays(dateLeft, dateRight);\n    } else if (unit === 'week') {\n      value = differenceInCalendarWeeks(dateLeft, dateRight);\n    } else if (unit === 'month') {\n      value = differenceInCalendarMonths(dateLeft, dateRight);\n    } else if (unit === 'quarter') {\n      value = differenceInCalendarQuarters(dateLeft, dateRight);\n    } else if (unit === 'year') {\n      value = differenceInCalendarYears(dateLeft, dateRight);\n    }\n  }\n  var rtf = new Intl.RelativeTimeFormat(options === null || options === void 0 ? void 0 : options.locale, {\n    localeMatcher: options === null || options === void 0 ? void 0 : options.localeMatcher,\n    numeric: (options === null || options === void 0 ? void 0 : options.numeric) || 'auto',\n    style: options === null || options === void 0 ? void 0 : options.style\n  });\n  return rtf.format(value, unit);\n}"], "mappings": "AAAA,SAASA,YAAY,EAAEC,aAAa,EAAEC,eAAe,EAAEC,cAAc,EAAEC,gBAAgB,EAAEC,aAAa,EAAEC,aAAa,QAAQ,uBAAuB;AACpJ,OAAOC,wBAAwB,MAAM,sCAAsC;AAC3E,OAAOC,0BAA0B,MAAM,wCAAwC;AAC/E,OAAOC,4BAA4B,MAAM,0CAA0C;AACnF,OAAOC,yBAAyB,MAAM,uCAAuC;AAC7E,OAAOC,yBAAyB,MAAM,uCAAuC;AAC7E,OAAOC,iBAAiB,MAAM,+BAA+B;AAC7D,OAAOC,mBAAmB,MAAM,iCAAiC;AACjE,OAAOC,mBAAmB,MAAM,iCAAiC;AACjE,OAAOC,MAAM,MAAM,oBAAoB;AACvC,OAAOC,YAAY,MAAM,+BAA+B;AACxD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,SAASC,kBAAkBA,CAACC,IAAI,EAAEC,QAAQ,EAAEC,OAAO,EAAE;EAClEJ,YAAY,CAAC,CAAC,EAAEK,SAAS,CAAC;EAC1B,IAAIC,KAAK,GAAG,CAAC;EACb,IAAIC,IAAI;EACR,IAAIC,QAAQ,GAAGT,MAAM,CAACG,IAAI,CAAC;EAC3B,IAAIO,SAAS,GAAGV,MAAM,CAACI,QAAQ,CAAC;EAChC,IAAI,EAAEC,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,IAAIA,OAAO,CAACG,IAAI,CAAC,EAAE;IAC7D;IACA,IAAIG,aAAa,GAAGZ,mBAAmB,CAACU,QAAQ,EAAEC,SAAS,CAAC,CAAC,CAAC;;IAE9D,IAAIE,IAAI,CAACC,GAAG,CAACF,aAAa,CAAC,GAAGxB,eAAe,EAAE;MAC7CoB,KAAK,GAAGR,mBAAmB,CAACU,QAAQ,EAAEC,SAAS,CAAC;MAChDF,IAAI,GAAG,QAAQ;IACjB,CAAC,MAAM,IAAII,IAAI,CAACC,GAAG,CAACF,aAAa,CAAC,GAAGzB,aAAa,EAAE;MAClDqB,KAAK,GAAGT,mBAAmB,CAACW,QAAQ,EAAEC,SAAS,CAAC;MAChDF,IAAI,GAAG,QAAQ;IACjB,CAAC,MAAM,IAAII,IAAI,CAACC,GAAG,CAACF,aAAa,CAAC,GAAG1B,YAAY,IAAI2B,IAAI,CAACC,GAAG,CAACrB,wBAAwB,CAACiB,QAAQ,EAAEC,SAAS,CAAC,CAAC,GAAG,CAAC,EAAE;MAChHH,KAAK,GAAGV,iBAAiB,CAACY,QAAQ,EAAEC,SAAS,CAAC;MAC9CF,IAAI,GAAG,MAAM;IACf,CAAC,MAAM,IAAII,IAAI,CAACC,GAAG,CAACF,aAAa,CAAC,GAAGrB,aAAa,KAAKiB,KAAK,GAAGf,wBAAwB,CAACiB,QAAQ,EAAEC,SAAS,CAAC,CAAC,IAAIE,IAAI,CAACC,GAAG,CAACN,KAAK,CAAC,GAAG,CAAC,EAAE;MACpIC,IAAI,GAAG,KAAK;IACd,CAAC,MAAM,IAAII,IAAI,CAACC,GAAG,CAACF,aAAa,CAAC,GAAGvB,cAAc,EAAE;MACnDmB,KAAK,GAAGZ,yBAAyB,CAACc,QAAQ,EAAEC,SAAS,CAAC;MACtDF,IAAI,GAAG,MAAM;IACf,CAAC,MAAM,IAAII,IAAI,CAACC,GAAG,CAACF,aAAa,CAAC,GAAGtB,gBAAgB,EAAE;MACrDkB,KAAK,GAAGd,0BAA0B,CAACgB,QAAQ,EAAEC,SAAS,CAAC;MACvDF,IAAI,GAAG,OAAO;IAChB,CAAC,MAAM,IAAII,IAAI,CAACC,GAAG,CAACF,aAAa,CAAC,GAAGpB,aAAa,EAAE;MAClD,IAAIG,4BAA4B,CAACe,QAAQ,EAAEC,SAAS,CAAC,GAAG,CAAC,EAAE;QACzD;QACAH,KAAK,GAAGb,4BAA4B,CAACe,QAAQ,EAAEC,SAAS,CAAC;QACzDF,IAAI,GAAG,SAAS;MAClB,CAAC,MAAM;QACLD,KAAK,GAAGX,yBAAyB,CAACa,QAAQ,EAAEC,SAAS,CAAC;QACtDF,IAAI,GAAG,MAAM;MACf;IACF,CAAC,MAAM;MACLD,KAAK,GAAGX,yBAAyB,CAACa,QAAQ,EAAEC,SAAS,CAAC;MACtDF,IAAI,GAAG,MAAM;IACf;EACF,CAAC,MAAM;IACL;IACAA,IAAI,GAAGH,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACG,IAAI;IACrE,IAAIA,IAAI,KAAK,QAAQ,EAAE;MACrBD,KAAK,GAAGR,mBAAmB,CAACU,QAAQ,EAAEC,SAAS,CAAC;IAClD,CAAC,MAAM,IAAIF,IAAI,KAAK,QAAQ,EAAE;MAC5BD,KAAK,GAAGT,mBAAmB,CAACW,QAAQ,EAAEC,SAAS,CAAC;IAClD,CAAC,MAAM,IAAIF,IAAI,KAAK,MAAM,EAAE;MAC1BD,KAAK,GAAGV,iBAAiB,CAACY,QAAQ,EAAEC,SAAS,CAAC;IAChD,CAAC,MAAM,IAAIF,IAAI,KAAK,KAAK,EAAE;MACzBD,KAAK,GAAGf,wBAAwB,CAACiB,QAAQ,EAAEC,SAAS,CAAC;IACvD,CAAC,MAAM,IAAIF,IAAI,KAAK,MAAM,EAAE;MAC1BD,KAAK,GAAGZ,yBAAyB,CAACc,QAAQ,EAAEC,SAAS,CAAC;IACxD,CAAC,MAAM,IAAIF,IAAI,KAAK,OAAO,EAAE;MAC3BD,KAAK,GAAGd,0BAA0B,CAACgB,QAAQ,EAAEC,SAAS,CAAC;IACzD,CAAC,MAAM,IAAIF,IAAI,KAAK,SAAS,EAAE;MAC7BD,KAAK,GAAGb,4BAA4B,CAACe,QAAQ,EAAEC,SAAS,CAAC;IAC3D,CAAC,MAAM,IAAIF,IAAI,KAAK,MAAM,EAAE;MAC1BD,KAAK,GAAGX,yBAAyB,CAACa,QAAQ,EAAEC,SAAS,CAAC;IACxD;EACF;EACA,IAAII,GAAG,GAAG,IAAIC,IAAI,CAACC,kBAAkB,CAACX,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACY,MAAM,EAAE;IACtGC,aAAa,EAAEb,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACa,aAAa;IACtFC,OAAO,EAAE,CAACd,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACc,OAAO,KAAK,MAAM;IACtFC,KAAK,EAAEf,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACe;EACnE,CAAC,CAAC;EACF,OAAON,GAAG,CAACO,MAAM,CAACd,KAAK,EAAEC,IAAI,CAAC;AAChC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}