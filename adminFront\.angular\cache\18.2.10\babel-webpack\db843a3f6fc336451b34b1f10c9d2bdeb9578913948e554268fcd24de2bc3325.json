{"ast": null, "code": "import { of as observableOf } from 'rxjs';\nimport { UserActivityData } from '../data/user-activity';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"./periods.service\";\nexport let UserActivityService = /*#__PURE__*/(() => {\n  class UserActivityService extends UserActivityData {\n    generateUserActivityRandomData(date) {\n      return {\n        date,\n        pagesVisitCount: this.getRandom(1000),\n        deltaUp: this.getRandom(1) % 2 === 0,\n        newVisits: this.getRandom(100)\n      };\n    }\n    constructor(periods) {\n      super();\n      this.periods = periods;\n      this.getRandom = roundTo => Math.round(Math.random() * roundTo);\n      this.data = {};\n      this.data = {\n        week: this.getDataWeek(),\n        month: this.getDataMonth(),\n        year: this.getDataYear()\n      };\n    }\n    getDataWeek() {\n      return this.periods.getWeeks().map(week => {\n        return this.generateUserActivityRandomData(week);\n      });\n    }\n    getDataMonth() {\n      const currentDate = new Date();\n      const days = currentDate.getDate();\n      const month = this.periods.getMonths()[currentDate.getMonth()];\n      return Array.from(Array(days)).map((_, index) => {\n        const date = `${index + 1} ${month}`;\n        return this.generateUserActivityRandomData(date);\n      });\n    }\n    getDataYear() {\n      return this.periods.getYears().map(year => {\n        return this.generateUserActivityRandomData(year);\n      });\n    }\n    getUserActivityData(period) {\n      return observableOf(this.data[period]);\n    }\n    static {\n      this.ɵfac = function UserActivityService_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || UserActivityService)(i0.ɵɵinject(i1.PeriodsService));\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: UserActivityService,\n        factory: UserActivityService.ɵfac\n      });\n    }\n  }\n  return UserActivityService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}