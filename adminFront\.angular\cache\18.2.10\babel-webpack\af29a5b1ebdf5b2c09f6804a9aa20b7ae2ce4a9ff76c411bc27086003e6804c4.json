{"ast": null, "code": "export const environment = {\n  IS_PRODUCTION: false,\n  BASE_URL_API: 'http://localhost:9453/',\n  BASE_URL: 'http://localhost:9453/',\n  BASE_WITHOUT_FILEROOT: 'https://jeanjalechange.shindabu4.com',\n  OWNER_BUID: 'BU00000',\n  BASE_FILE: 'https://jeanjalechange.shindabu4.com/Files'\n};", "map": {"version": 3, "names": ["environment", "IS_PRODUCTION", "BASE_URL_API", "BASE_URL", "BASE_WITHOUT_FILEROOT", "OWNER_BUID", "BASE_FILE"], "sources": ["C:\\Users\\<USER>\\Documents\\jeansalechange\\adminFront\\src\\environments\\environment.dev.ts"], "sourcesContent": ["import { IEnvironment } from \"./environment.interface\";\r\n\r\nexport const environment: IEnvironment = {\r\n  IS_PRODUCTION: false,\r\n  BASE_URL_API: 'http://localhost:9453/',\r\n  BASE_URL: 'http://localhost:9453/',\r\n  BASE_WITHOUT_FILEROOT: 'https://jeanjalechange.shindabu4.com',\r\n  OWNER_BUID: 'BU00000',\r\n  BASE_FILE: 'https://jeanjalechange.shindabu4.com/Files',\r\n};\r\n"], "mappings": "AAEA,OAAO,MAAMA,WAAW,GAAiB;EACvCC,aAAa,EAAE,KAAK;EACpBC,YAAY,EAAE,wBAAwB;EACtCC,QAAQ,EAAE,wBAAwB;EAClCC,qBAAqB,EAAE,sCAAsC;EAC7DC,UAAU,EAAE,SAAS;EACrBC,SAAS,EAAE;CACZ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}