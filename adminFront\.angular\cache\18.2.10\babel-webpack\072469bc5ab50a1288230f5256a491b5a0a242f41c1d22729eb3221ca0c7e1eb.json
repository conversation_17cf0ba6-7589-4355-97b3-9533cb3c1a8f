{"ast": null, "code": "//! moment.js locale configuration\n//! locale : Uzbek [uz]\n//! author : <PERSON><PERSON><PERSON> : https://github.com/muminoff\n\n;\n(function (global, factory) {\n  typeof exports === 'object' && typeof module !== 'undefined' && typeof require === 'function' ? factory(require('../moment')) : typeof define === 'function' && define.amd ? define(['../moment'], factory) : factory(global.moment);\n})(this, function (moment) {\n  'use strict';\n\n  //! moment.js locale configuration\n  var uz = moment.defineLocale('uz', {\n    months: 'январ_феврал_март_апрел_май_июн_июл_август_сентябр_октябр_ноябр_декабр'.split('_'),\n    monthsShort: 'янв_фев_мар_апр_май_июн_июл_авг_сен_окт_ноя_дек'.split('_'),\n    weekdays: 'Якшанба_Душанба_Сешанба_Чоршанба_Пайшанба_Жума_Шанба'.split('_'),\n    weekdaysShort: 'Якш_Душ_Сеш_Чор_Пай_Жум_Шан'.split('_'),\n    weekdaysMin: 'Як_Ду_Се_Чо_Па_Жу_Ша'.split('_'),\n    longDateFormat: {\n      LT: 'HH:mm',\n      LTS: 'HH:mm:ss',\n      L: 'DD/MM/YYYY',\n      LL: 'D MMMM YYYY',\n      LLL: 'D MMMM YYYY HH:mm',\n      LLLL: 'D MMMM YYYY, dddd HH:mm'\n    },\n    calendar: {\n      sameDay: '[Бугун соат] LT [да]',\n      nextDay: '[Эртага] LT [да]',\n      nextWeek: 'dddd [куни соат] LT [да]',\n      lastDay: '[Кеча соат] LT [да]',\n      lastWeek: '[Утган] dddd [куни соат] LT [да]',\n      sameElse: 'L'\n    },\n    relativeTime: {\n      future: 'Якин %s ичида',\n      past: 'Бир неча %s олдин',\n      s: 'фурсат',\n      ss: '%d фурсат',\n      m: 'бир дакика',\n      mm: '%d дакика',\n      h: 'бир соат',\n      hh: '%d соат',\n      d: 'бир кун',\n      dd: '%d кун',\n      M: 'бир ой',\n      MM: '%d ой',\n      y: 'бир йил',\n      yy: '%d йил'\n    },\n    week: {\n      dow: 1,\n      // Monday is the first day of the week.\n      doy: 7 // The week that contains Jan 4th is the first week of the year.\n    }\n  });\n  return uz;\n});", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}