{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { Component, ChangeDetectionStrategy } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { NbCheckboxModule } from '@nebular/theme';\nimport { tap } from 'rxjs';\nimport { SharedModule } from 'src/app/pages/components/shared.module';\nimport { SharedModule as AppSharedModule } from 'src/app/shared/shared.module';\nimport { BaseComponent } from 'src/app/pages/components/base/baseComponent';\nimport { Base64ImagePipe } from \"../../../../@theme/pipes/base64-image.pipe\";\nimport { EnumHouseType } from 'src/app/shared/enum/enumHouseType';\nlet DetailContentManagementSalesAccountComponent = class DetailContentManagementSalesAccountComponent extends BaseComponent {\n  constructor(_allow, route, message, _formItemService, _regularNoticeFileService, _utilityService, valid, location, _materialService, _eventService, _houseCustomService) {\n    super(_allow);\n    this._allow = _allow;\n    this.route = route;\n    this.message = message;\n    this._formItemService = _formItemService;\n    this._regularNoticeFileService = _regularNoticeFileService;\n    this._utilityService = _utilityService;\n    this.valid = valid;\n    this.location = location;\n    this._materialService = _materialService;\n    this._eventService = _eventService;\n    this._houseCustomService = _houseCustomService;\n    // 通知類型選項映射\n    this.cNoticeTypeOptions = [{\n      label: '地主戶',\n      value: EnumHouseType.地主戶\n    }, {\n      label: '銷售戶',\n      value: EnumHouseType.銷售戶\n    }];\n    this.CUiTypeOptions = [{\n      value: 1,\n      label: '建材選色'\n    }, {\n      value: 2,\n      label: '群組選樣_選色'\n    }, {\n      value: 3,\n      label: '建材選樣'\n    }];\n    this.CRemarkTypeOptions = [\"正常\", \"留料\"];\n    this.houseType = 0;\n    this.selectedItems = {};\n    this.selectedRemarkType = {};\n    // 新增：戶別選擇器相關屬性\n    this.buildingData = {}; // 存放建築物戶別資料\n    this.isNew = true;\n    // 戶別清單 - 使用 this.houseType 簡化邏輯，不再需要 API 查詢\n    this.houseHoldList = [];\n  }\n  // 動態獲取標題文字\n  get dynamicTitle() {\n    const option = this.cNoticeTypeOptions.find(option => option.value === this.houseType // 直接使用 this.houseType\n    );\n    return option ? `類型 - ${option.label}` : '類型 - 獨立選樣';\n  }\n  ngOnInit() {\n    this.route.paramMap.subscribe(params => {\n      if (params) {\n        const idParam = params.get('id');\n        const id = idParam ? +idParam : 0;\n        this.buildCaseId = id;\n        if (this.buildCaseId <= 0) {\n          // 如果 buildCaseId 為 0 或無效，顯示錯誤訊息並返回\n          console.error('Invalid buildCaseId:', this.buildCaseId);\n          this.message.showErrorMSG(\"無效的建案ID，請重新選擇建案\");\n          this.goBack();\n          return;\n        }\n      }\n    });\n    // 處理查詢參數中的戶型\n    this.route.queryParams.subscribe(queryParams => {\n      if (queryParams['houseType']) {\n        this.houseType = Number(queryParams['houseType']);\n        // 簡化邏輯：直接初始化資料\n        if (this.buildCaseId > 0) {\n          this.initializeData();\n        }\n      }\n    });\n  }\n  ngOnDestroy() {\n    // 確保在組件銷毀時恢復body的滾動\n    document.body.style.overflow = 'auto';\n  }\n  getItemByValue(value, options) {\n    for (const item of options) {\n      if (item.value === value) {\n        return item;\n      }\n    }\n    return null;\n  }\n  detectFiles(event, formItemReq_) {\n    const file = event.target.files[0];\n    if (file) {\n      let reader = new FileReader();\n      reader.readAsDataURL(file);\n      reader.onload = () => {\n        let base64Str = reader.result;\n        if (!base64Str) {\n          return;\n        }\n        if (formItemReq_.listPictures.length > 0) {\n          formItemReq_.listPictures[0] = {\n            id: new Date().getTime(),\n            name: file.name.split('.')[0],\n            data: base64Str,\n            extension: this._utilityService.getFileExtension(file.name),\n            CFile: file\n          };\n        } else {\n          formItemReq_.listPictures.push({\n            id: new Date().getTime(),\n            name: file.name.split('.')[0],\n            data: base64Str,\n            extension: this._utilityService.getFileExtension(file.name),\n            CFile: file\n          });\n        }\n        event.target.value = null;\n      };\n    }\n  }\n  removeImage(pictureId, formItemReq_) {\n    if (formItemReq_.listPictures.length) {\n      formItemReq_.listPictures = formItemReq_.listPictures.filter(x => x.id != pictureId);\n    }\n  }\n  renameFile(event, index, formItemReq_) {\n    var blob = formItemReq_.listPictures[index].CFile.slice(0, formItemReq_.listPictures[index].CFile.size, formItemReq_.listPictures[index].CFile.type);\n    var newFile = new File([blob], `${event.target.value + '.' + formItemReq_.listPictures[index].extension}`, {\n      type: formItemReq_.listPictures[index].CFile.type\n    });\n    formItemReq_.listPictures[index].CFile = newFile;\n  }\n  // 輪播功能方法\n  nextImage(formItemReq) {\n    if (formItemReq.CMatrialUrl && formItemReq.CMatrialUrl.length > 0) {\n      formItemReq.currentImageIndex = (formItemReq.currentImageIndex + 1) % formItemReq.CMatrialUrl.length;\n    }\n  }\n  prevImage(formItemReq) {\n    if (formItemReq.CMatrialUrl && formItemReq.CMatrialUrl.length > 0) {\n      formItemReq.currentImageIndex = formItemReq.currentImageIndex === 0 ? formItemReq.CMatrialUrl.length - 1 : formItemReq.currentImageIndex - 1;\n    }\n  }\n  getCurrentImage(formItemReq) {\n    if (formItemReq.CMatrialUrl && formItemReq.CMatrialUrl.length > 0 && formItemReq.currentImageIndex !== undefined) {\n      return formItemReq.CMatrialUrl[formItemReq.currentImageIndex];\n    }\n    return null;\n  }\n  // 放大功能方法\n  openImageModal(formItemReq, imageIndex) {\n    if (formItemReq.CMatrialUrl && formItemReq.CMatrialUrl.length > 0) {\n      if (imageIndex !== undefined) {\n        formItemReq.currentImageIndex = imageIndex;\n      }\n      formItemReq.isModalOpen = true;\n      // 防止背景滾動\n      document.body.style.overflow = 'hidden';\n    }\n  }\n  closeImageModal(formItemReq) {\n    formItemReq.isModalOpen = false;\n    // 恢復背景滾動\n    document.body.style.overflow = 'auto';\n  }\n  // 模態窗口中的輪播方法\n  nextImageModal(formItemReq) {\n    this.nextImage(formItemReq);\n  }\n  prevImageModal(formItemReq) {\n    this.prevImage(formItemReq);\n  }\n  // 鍵盤事件處理\n  onKeydown(event, formItemReq) {\n    if (formItemReq.isModalOpen) {\n      switch (event.key) {\n        case 'ArrowLeft':\n          event.preventDefault();\n          this.prevImageModal(formItemReq);\n          break;\n        case 'ArrowRight':\n          event.preventDefault();\n          this.nextImageModal(formItemReq);\n          break;\n        case 'Escape':\n          event.preventDefault();\n          this.closeImageModal(formItemReq);\n          break;\n      }\n    }\n  }\n  // 新增：從 HouseholdItem 陣列中提取戶別代碼\n  extractHouseholdCodes(households) {\n    if (!households || !Array.isArray(households)) {\n      return [];\n    }\n    return households.map(h => h.code || h);\n  }\n  // 新增：處理戶別選擇變更\n  onHouseholdSelectionChange(selectedHouseholds, formItemReq) {\n    // 重置所有戶別選擇狀態\n    Object.keys(formItemReq.selectedItems).forEach(key => {\n      formItemReq.selectedItems[key] = false;\n    });\n    // 設置選中的戶別\n    selectedHouseholds.forEach(household => {\n      formItemReq.selectedItems[household] = true;\n    });\n    // 更新全選狀態\n    formItemReq.allSelected = this.houseHoldList.length > 0 && this.houseHoldList.every(item => formItemReq.selectedItems[item]);\n    // 更新緩存\n    this.updateSelectedHouseholdsCache(formItemReq);\n  }\n  // 新增：取得已選戶別數組\n  getSelectedHouseholds(formItemReq) {\n    return Object.keys(formItemReq.selectedItems).filter(key => formItemReq.selectedItems[key]);\n  }\n  // 新增：更新已選戶別緩存\n  updateSelectedHouseholdsCache(formItemReq) {\n    console.log('formItemReq:', formItemReq);\n    formItemReq.selectedHouseholdsCached = this.getSelectedHouseholds(formItemReq);\n    console.log('Updated selected households cache:', formItemReq.selectedHouseholdsCached);\n  }\n  // 新增：更新所有項目的緩存\n  updateAllSelectedHouseholdsCache() {\n    if (this.arrListFormItemReq) {\n      this.arrListFormItemReq.forEach(formItemReq => {\n        this.updateSelectedHouseholdsCache(formItemReq);\n      });\n    }\n  }\n  onCheckboxRemarkChange(checked, item, formItemReq_) {\n    formItemReq_.selectedRemarkType[item] = checked;\n  }\n  createRemarkObject(CRemarkTypeOptions, CRemarkType) {\n    const remarkObject = {};\n    for (const option of CRemarkTypeOptions) {\n      remarkObject[option] = false;\n    }\n    const remarkTypes = CRemarkType.split('-');\n    for (const type of remarkTypes) {\n      if (CRemarkTypeOptions.includes(type)) {\n        remarkObject[type] = true;\n      }\n    }\n    return remarkObject;\n  }\n  mergeItems(items) {\n    const map = new Map();\n    items.forEach(item => {\n      const key = `${item.CLocation}_${item.CName}_${item.CPart}`;\n      if (map.has(key)) {\n        const existing = map.get(key);\n        existing.count += 1;\n      } else {\n        map.set(key, {\n          item: {\n            ...item\n          },\n          count: 1\n        });\n      }\n    });\n    return Array.from(map.values()).map(({\n      item,\n      count\n    }) => ({\n      ...item,\n      CTotalAnswer: count\n    }));\n  }\n  getListFormItem() {\n    this._formItemService.apiFormItemGetListFormItemPost$Json({\n      body: {\n        CBuildCaseId: this.buildCaseId,\n        CFormType: this.houseType,\n        CIsPaging: false\n      }\n    }).pipe(tap(res => {\n      if (res.Entries && res.StatusCode == 0) {\n        this.listFormItem = res.Entries;\n        this.isNew = res.Entries.formItems ? false : true;\n        if (res.Entries.formItems) {\n          this.houseHoldList.forEach(item => this.selectedItems[item] = false);\n          this.CRemarkTypeOptions.forEach(item => this.selectedRemarkType[item] = false);\n          this.arrListFormItemReq = res.Entries.formItems.map(o => {\n            return {\n              CFormId: this.listFormItem.CFormId,\n              CDesignFileUrl: o.CDesignFileUrl,\n              CMatrialUrl: o.CMatrialUrl || (o.CFirstMatrialUrl ? [o.CFirstMatrialUrl] : []),\n              CFile: o.CFile,\n              CFormItemHouseHold: o.CFormItemHouseHold,\n              CFormItemId: o.CFormItemId,\n              CLocation: o.CLocation,\n              CName: o.CName,\n              CPart: o.CPart,\n              CItemName: o.CItemName ? o.CItemName : `${o.CName}-${o.CPart}-${o.CLocation}`,\n              CRemarkType: o.CRemarkType,\n              CTotalAnswer: o.CTotalAnswer,\n              CRequireAnswer: o.CUiType === 3 ? 1 : o.CRequireAnswer,\n              CUiType: o.CUiType,\n              selectedItems: o.tblFormItemHouseholds.length ? this.createArrayObjectFromArray(this.houseHoldList, o.tblFormItemHouseholds) : {\n                ...this.selectedItems\n              },\n              selectedRemarkType: o.CRemarkType ? this.createRemarkObject(this.CRemarkTypeOptions, o.CRemarkType) : {\n                ...this.selectedRemarkType\n              },\n              allSelected: o.tblFormItemHouseholds.length === this.houseHoldList.length,\n              listPictures: [],\n              selectedCUiType: o.CUiType ? this.getItemByValue(o.CUiType, this.CUiTypeOptions) : this.CUiTypeOptions[0],\n              currentImageIndex: 0,\n              isModalOpen: false,\n              selectedHouseholdsCached: [] // 初始化緩存，稍後會更新\n            };\n          });\n        } else {\n          // 如果沒有現有的表單項目，則不執行任何操作\n        }\n        // 初始化所有項目的緩存\n        this.updateAllSelectedHouseholdsCache();\n      }\n    })).subscribe();\n  }\n  changeSelectCUiType(formItemReq) {\n    if (formItemReq.selectedCUiType && formItemReq.selectedCUiType.value === 3) {\n      formItemReq.CRequireAnswer = 1;\n    }\n  }\n  getKeysWithTrueValue(obj) {\n    return Object.keys(obj).filter(key => obj[key]);\n  }\n  getKeysWithTrueValueJoined(obj) {\n    return Object.keys(obj).filter(key => obj[key]).join('-');\n  }\n  getCRemarkType(selectedCUiType, selectedRemarkType) {\n    if (selectedCUiType && selectedCUiType.value == 3) {\n      return this.getKeysWithTrueValueJoined(selectedRemarkType);\n    }\n  }\n  getStringAfterComma(inputString) {\n    const parts = inputString.split(',');\n    if (parts.length > 1) {\n      return parts[1];\n    } else return \"\";\n  }\n  formatFile(listPictures) {\n    if (listPictures && listPictures.length > 0) {\n      return {\n        Base64String: this.getStringAfterComma(listPictures[0].data) || null,\n        FileExtension: listPictures[0].extension || null,\n        FileName: listPictures[0].CFile.name || listPictures[0].name || null\n      };\n    } else return undefined;\n  }\n  validation() {\n    this.valid.clear();\n    let hasInvalidCUiType = false;\n    let hasInvalidCRequireAnswer = false;\n    let hasInvalidItemName = false;\n    for (const item of this.saveListFormItemReq) {\n      if (!hasInvalidCUiType && !item.CUiType) {\n        hasInvalidCUiType = true;\n      }\n      if (!hasInvalidCRequireAnswer && !item.CRequireAnswer) {\n        hasInvalidCRequireAnswer = true;\n      }\n      if (item.CTotalAnswer && item.CRequireAnswer) {\n        if (item.CRequireAnswer > item.CTotalAnswer || item.CRequireAnswer < 0) {\n          this.valid.addErrorMessage('[必填數量]' + ' <= ' + item.CTotalAnswer + ` (${item.CItemName}) `);\n        }\n      }\n      if (!hasInvalidItemName && !item.CItemName) {\n        hasInvalidItemName = true;\n      }\n    }\n    if (hasInvalidCUiType) {\n      this.valid.addErrorMessage('[前台UI類型]' + ' 必填');\n    }\n    if (hasInvalidCRequireAnswer) {\n      this.valid.addErrorMessage('[必填數量]' + ' 必填且>0');\n    }\n    if (hasInvalidItemName) {\n      this.valid.addErrorMessage('[廚房-廚具-櫃體]' + ' 必填');\n    }\n  }\n  onSubmit() {\n    this.saveListFormItemReq = this.arrListFormItemReq.map(e => {\n      return {\n        CDesignFileUrl: e.CDesignFileUrl ? e.CDesignFileUrl : null,\n        CFile: e.listPictures ? this.formatFile(e.listPictures) : undefined,\n        CFormItemHouseHold: this.getKeysWithTrueValue(e.selectedItems),\n        CFormItemId: e.CFormItemId ? e.CFormItemId : null,\n        CFormID: this.isNew ? null : this.listFormItem.CFormId,\n        CName: e.CName,\n        CPart: e.CPart,\n        CLocation: e.CLocation,\n        CItemName: e.CItemName,\n        //? e.CItemName : `${e.CName}-${e.CPart}-${e.CLocation}`,\n        CRemarkType: e.selectedCUiType.value !== 3 ? null : this.getCRemarkType(e.selectedCUiType, e.selectedRemarkType) || null,\n        CTotalAnswer: e.CTotalAnswer,\n        CRequireAnswer: e.CRequireAnswer,\n        CUiType: e.selectedCUiType.value\n      };\n    });\n    this.validation();\n    if (this.valid.errorMessages.length > 0) {\n      this.message.showErrorMSGs(this.valid.errorMessages);\n      return;\n    }\n    if (this.isNew) {\n      this.createListFormItem();\n    } else {\n      this.saveListFormItem();\n    }\n  }\n  saveListFormItem() {\n    this._formItemService.apiFormItemSaveListFormItemPost$Json({\n      body: this.saveListFormItemReq\n    }).subscribe(res => {\n      if (res.StatusCode == 0) {\n        this.message.showSucessMSG(\"執行成功\");\n        // this.getListFormItem()\n        this.goBack();\n      }\n    });\n  }\n  createListFormItem() {\n    this.creatListFormItem = {\n      CBuildCaseId: this.buildCaseId,\n      CFormItem: this.saveListFormItemReq || null,\n      CFormType: this.houseType // 直接使用 this.houseType\n    };\n    this._formItemService.apiFormItemCreateListFormItemPost$Json({\n      body: this.creatListFormItem\n    }).subscribe(res => {\n      if (res.StatusCode == 0) {\n        this.message.showSucessMSG(\"執行成功\");\n        // this.getListFormItem()\n        this.goBack();\n      }\n    });\n  }\n  createArrayObjectFromArray(a, b) {\n    const c = {};\n    for (const item of a) {\n      const matchingItem = b.find(bItem => bItem.CHousehold === item && bItem.CIsSelect);\n      c[item] = !!matchingItem;\n    }\n    return c;\n  } //[\"House1\", \"House2\", \"House3\"] => [{CHousehold: \"House1\", CIsSelect: true,... }, ... ]\n  /**\n   * 複製當前表單到新表單\n   */\n  copyToNewForm() {\n    // 先取得當前有效的材料清單\n    this._materialService.apiMaterialGetMaterialListPost$Json({\n      body: {\n        CBuildCaseId: this.buildCaseId,\n        CPagi: false\n      }\n    }).pipe(tap(res => {\n      if (res.Entries && res.StatusCode == 0) {\n        // 建立有效材料清單的鍵值對應\n        const validMaterialKeys = new Set();\n        res.Entries.forEach(material => {\n          const key = `${material.CLocation}_${material.CName}_${material.CPart}`;\n          validMaterialKeys.add(key);\n        });\n        // 篩選出仍然有效的表單項目\n        const validFormItems = this.arrListFormItemReq.filter(item => {\n          const itemKey = `${item.CLocation}_${item.CName}_${item.CPart}`;\n          return validMaterialKeys.has(itemKey);\n        });\n        if (validFormItems.length === 0) {\n          this.message.showErrorMSG(\"沒有有效的表單項目可以複製\");\n          return;\n        }\n        // 準備複製的表單項目數據\n        this.saveListFormItemReq = validFormItems.map(e => {\n          return {\n            CDesignFileUrl: e.CDesignFileUrl ? e.CDesignFileUrl : null,\n            CFile: e.listPictures ? this.formatFile(e.listPictures) : undefined,\n            CFormItemHouseHold: this.getKeysWithTrueValue(e.selectedItems),\n            CFormItemId: null,\n            // 設為 null 以建立新項目\n            CFormID: null,\n            // 設為 null 以建立新表單\n            CName: e.CName,\n            CPart: e.CPart,\n            CLocation: e.CLocation,\n            CItemName: e.CItemName,\n            CRemarkType: e.selectedCUiType.value !== 3 ? null : this.getCRemarkType(e.selectedCUiType, e.selectedRemarkType) || null,\n            CTotalAnswer: e.CTotalAnswer,\n            CRequireAnswer: e.CRequireAnswer,\n            CUiType: e.selectedCUiType.value\n          };\n        });\n        // 執行驗證\n        this.validation();\n        if (this.valid.errorMessages.length > 0) {\n          this.message.showErrorMSGs(this.valid.errorMessages);\n          return;\n        }\n        // 建立複製的表單\n        this.creatListFormItem = {\n          CBuildCaseId: this.buildCaseId,\n          CFormItem: this.saveListFormItemReq || null,\n          CFormType: this.houseType // 直接使用 this.houseType\n        };\n        this._formItemService.apiFormItemCreateListFormItemPost$Json({\n          body: this.creatListFormItem\n        }).subscribe(createRes => {\n          if (createRes.StatusCode == 0) {\n            this.message.showSucessMSG(`複製表單成功，已篩選 ${validFormItems.length} 個有效項目`);\n            // 重新載入資料以顯示新的未鎖定表單\n            this.getListFormItem();\n          }\n        });\n      } else {\n        this.message.showErrorMSG(\"無法取得材料清單，複製失敗\");\n      }\n    })).subscribe();\n  }\n  // 新增：載入建築物戶別資料 (只呼叫一次 GetDropDown API)\n  loadBuildingDataFromAPI() {\n    if (!this.buildCaseId) return;\n    this._houseCustomService.getDropDown(this.buildCaseId).subscribe({\n      next: response => {\n        console.log('GetDropDown API response:', response);\n        if (response.Entries) {\n          this.buildingData = this.convertApiResponseToBuildingData(response.Entries);\n          console.log('Converted buildingData:', this.buildingData);\n        }\n      },\n      error: error => {\n        console.error('Error loading building data from API:', error);\n        // 如果API載入失敗，使用 houseHoldList 來產生基本的建築物資料\n        if (this.houseHoldList && this.houseHoldList.length > 0) {\n          this.buildingData = this.convertHouseHoldListToBuildingData(this.houseHoldList);\n        }\n      }\n    });\n  }\n  // 新增：將 API 回應轉換為建築物資料格式\n  convertApiResponseToBuildingData(entries) {\n    const buildingData = {};\n    Object.entries(entries).forEach(([building, houses]) => {\n      // 根據 houseType 篩選戶別\n      const filteredHouses = houses.filter(house => {\n        return this.shouldIncludeHouse(house);\n      });\n      // 只有在有符合條件的戶別時才加入建築物\n      if (filteredHouses.length > 0) {\n        buildingData[building] = filteredHouses.map(house => ({\n          code: house.HouseName,\n          building: house.Building,\n          floor: house.Floor,\n          houseId: house.HouseId,\n          houseName: house.HouseName,\n          isSelected: false,\n          isDisabled: false\n        }));\n      }\n    });\n    return buildingData;\n  }\n  // 新增：判斷是否應該包含此戶別的邏輯\n  shouldIncludeHouse(house) {\n    // 如果 houseType 為 0 或未設定，顯示所有戶別\n    if (!this.houseType || this.houseType === 0) {\n      return true;\n    }\n    // 根據業務邏輯判斷戶別類型\n    // 這裡需要根據實際的 API 資料結構來調整判斷邏輯\n    // 方案1: 如果 API 回傳的 house 物件中有 houseType 欄位\n    if (house.HouseType !== undefined) {\n      return house.HouseType === this.houseType;\n    }\n    // 方案2: 根據戶別名稱的命名規則判斷（需要根據實際規則調整）\n    // 例如：地主戶可能以特定前綴開頭，銷售戶以另一個前綴開頭\n    const houseName = house.HouseName || house.houseName || '';\n    if (this.houseType === EnumHouseType.地主戶) {\n      // 地主戶的判斷邏輯（需要根據實際規則調整）\n      // 例如：戶別名稱包含 \"地主\" 或特定前綴\n      return houseName.includes('地主') || houseName.startsWith('L');\n    } else if (this.houseType === EnumHouseType.銷售戶) {\n      // 銷售戶的判斷邏輯（需要根據實際規則調整）\n      // 例如：戶別名稱包含 \"銷售\" 或不包含 \"地主\"\n      return houseName.includes('銷售') || !houseName.includes('地主');\n    }\n    // 預設情況：顯示所有戶別\n    return true;\n  }\n  // 新增：將戶別清單轉換為建築物資料格式\n  convertHouseHoldListToBuildingData(houseHoldList) {\n    if (!houseHoldList || houseHoldList.length === 0) {\n      return {};\n    }\n    // 如果戶別有明確的建築物前綴（如 A001, B002），我們可以依此分組\n    const buildingData = {};\n    houseHoldList.forEach(household => {\n      // 嘗試從戶別名稱中提取建築物代碼\n      const buildingMatch = household.match(/^([A-Z]+)/);\n      const building = buildingMatch ? `${buildingMatch[1]}棟` : '預設建築';\n      if (!buildingData[building]) {\n        buildingData[building] = [];\n      }\n      // 計算樓層（假設每4戶為一層）\n      const houseNumber = parseInt(household.replace(/[A-Z]/g, ''));\n      const floor = Math.ceil(houseNumber / 4);\n      buildingData[building].push({\n        code: household,\n        building: building,\n        floor: `${floor}F`,\n        isSelected: false,\n        isDisabled: false\n      });\n    });\n    return buildingData;\n  }\n  // 簡化的初始化方法，使用 this.houseType 替換 getListRegularNoticeFileHouseHold\n  initializeData() {\n    // 初始化空的戶別清單（如果需要的話，可以從其他地方取得）\n    this.houseHoldList = [];\n    // 載入建築物資料\n    this.loadBuildingDataFromAPI();\n    // 載入表單項目\n    this.getListFormItem();\n  }\n  goBack() {\n    this._eventService.push({\n      action: \"GET_BUILDCASE\" /* EEvent.GET_BUILDCASE */,\n      payload: this.buildCaseId\n    });\n    this.location.back();\n  }\n};\nDetailContentManagementSalesAccountComponent = __decorate([Component({\n  selector: 'ngx-detail-content-management-sales-account',\n  templateUrl: './detail-content-management-sales-account.component.html',\n  styleUrls: ['./detail-content-management-sales-account.component.scss'],\n  standalone: true,\n  imports: [CommonModule, SharedModule, AppSharedModule, NbCheckboxModule, Base64ImagePipe],\n  changeDetection: ChangeDetectionStrategy.OnPush\n})], DetailContentManagementSalesAccountComponent);\nexport { DetailContentManagementSalesAccountComponent };", "map": {"version": 3, "names": ["Component", "ChangeDetectionStrategy", "CommonModule", "NbCheckboxModule", "tap", "SharedModule", "AppSharedModule", "BaseComponent", "Base64ImagePipe", "EnumHouseType", "DetailContentManagementSalesAccountComponent", "constructor", "_allow", "route", "message", "_formItemService", "_regularNoticeFileService", "_utilityService", "valid", "location", "_materialService", "_eventService", "_houseCustomService", "cNoticeTypeOptions", "label", "value", "地主戶", "銷售戶", "CUiTypeOptions", "CRemarkTypeOptions", "houseType", "selectedItems", "selectedRemarkType", "buildingData", "isNew", "houseHoldList", "dynamicTitle", "option", "find", "ngOnInit", "paramMap", "subscribe", "params", "idParam", "get", "id", "buildCaseId", "console", "error", "showErrorMSG", "goBack", "queryParams", "Number", "initializeData", "ngOnDestroy", "document", "body", "style", "overflow", "getItemByValue", "options", "item", "detectFiles", "event", "formItemReq_", "file", "target", "files", "reader", "FileReader", "readAsDataURL", "onload", "base64Str", "result", "listPictures", "length", "Date", "getTime", "name", "split", "data", "extension", "getFileExtension", "CFile", "push", "removeImage", "pictureId", "filter", "x", "renameFile", "index", "blob", "slice", "size", "type", "newFile", "File", "nextImage", "formItemReq", "CMatrialUrl", "currentImageIndex", "prevImage", "getCurrentImage", "undefined", "openImageModal", "imageIndex", "isModalOpen", "closeImageModal", "nextImageModal", "prevImageModal", "onKeydown", "key", "preventDefault", "extractHouseholdCodes", "households", "Array", "isArray", "map", "h", "code", "onHouseholdSelectionChange", "selectedHouseholds", "Object", "keys", "for<PERSON>ach", "household", "allSelected", "every", "updateSelectedHouseholdsCache", "getSelectedHouseholds", "log", "selectedHouseholdsCached", "updateAllSelectedHouseholdsCache", "arrListFormItemReq", "onCheckboxRemarkChange", "checked", "createRemarkObject", "CRemarkType", "remarkObject", "remarkTypes", "includes", "mergeItems", "items", "Map", "CLocation", "CName", "<PERSON>art", "has", "existing", "count", "set", "from", "values", "CTotalAnswer", "getListFormItem", "apiFormItemGetListFormItemPost$Json", "CBuildCaseId", "CFormType", "CIsPaging", "pipe", "res", "Entries", "StatusCode", "listFormItem", "formItems", "o", "CFormId", "CDesignFileUrl", "CFirstMatrialUrl", "CFormItemHouseHold", "CFormItemId", "CItemName", "CRequireAnswer", "CUiType", "tblFormItemHouseholds", "createArrayObjectFromArray", "selectedCUiType", "changeSelectCUiType", "getKeysWithTrueValue", "obj", "getKeysWithTrueValueJoined", "join", "getCRemarkType", "getStringAfterComma", "inputString", "parts", "formatFile", "Base64String", "FileExtension", "FileName", "validation", "clear", "hasInvalidCUiType", "hasInvalidCRequireAnswer", "hasInvalidItemName", "saveListFormItemReq", "addErrorMessage", "onSubmit", "e", "CFormID", "errorMessages", "showErrorMSGs", "createListFormItem", "saveListFormItem", "apiFormItemSaveListFormItemPost$Json", "showSucessMSG", "creatListFormItem", "CFormItem", "apiFormItemCreateListFormItemPost$Json", "a", "b", "c", "matchingItem", "bItem", "CHousehold", "CIsSelect", "copyToNewForm", "apiMaterialGetMaterialListPost$Json", "CPagi", "validMaterialKeys", "Set", "material", "add", "validFormItems", "itemKey", "createRes", "loadBuildingDataFromAPI", "getDropDown", "next", "response", "convertApiResponseToBuildingData", "convertHouseHoldListToBuildingData", "entries", "building", "houses", "filteredHouses", "house", "shouldIncludeHouse", "HouseName", "Building", "floor", "Floor", "houseId", "HouseId", "houseName", "isSelected", "isDisabled", "HouseType", "startsWith", "buildingMatch", "match", "houseNumber", "parseInt", "replace", "Math", "ceil", "action", "payload", "back", "__decorate", "selector", "templateUrl", "styleUrls", "standalone", "imports", "changeDetection", "OnPush"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\selection-management\\content-management-sales-account\\detail-content-management-sales-account\\detail-content-management-sales-account.component.ts"], "sourcesContent": ["import { Compo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, ChangeDetectionStrategy } from '@angular/core';\r\nimport { CommonModule, Location } from '@angular/common';\r\nimport { NbCheckboxModule } from '@nebular/theme';\r\nimport { AllowHelper } from 'src/app/shared/helper/allowHelper';\r\nimport { FormItemService, MaterialService, RegularNoticeFileService } from 'src/services/api/services';\r\nimport { HouseCustomService } from 'src/services/api/services/HouseCustom.service';\r\nimport { ActivatedRoute } from '@angular/router';\r\nimport { MessageService } from 'src/app/shared/services/message.service';\r\nimport { tap } from 'rxjs';\r\nimport { CreateListFormItem, FileViewModel, GetListFormItemRes, SaveListFormItemReq } from 'src/services/api/models';\r\nimport { UtilityService } from 'src/app/shared/services/utility.service';\r\nimport { ValidationHelper } from 'src/app/shared/helper/validationHelper';\r\nimport { SharedModule } from 'src/app/pages/components/shared.module';\r\nimport { SharedModule as AppSharedModule } from 'src/app/shared/shared.module';\r\nimport { BaseComponent } from 'src/app/pages/components/base/baseComponent';\r\nimport { EventService, EEvent } from 'src/app/shared/services/event.service';\r\nimport { Base64ImagePipe } from \"../../../../@theme/pipes/base64-image.pipe\";\r\nimport { EnumHouseType } from 'src/app/shared/enum/enumHouseType';\r\n\r\n\r\nexport interface ExtendedSaveListFormItemReq {\r\n  CDesignFileUrl?: string | null;\r\n  CMatrialUrl?: string[] | null;\r\n  CFile?: FileViewModel,\r\n  CFormItemHouseHold?: Array<string> | null;\r\n  CFormItemId?: number;\r\n  CItemName?: string;\r\n  CFormID?: number;\r\n  CPart?: string | null;\r\n  CName?: string | null;\r\n  CLocation?: string | null;\r\n  CRemarkType?: string | null;\r\n  CTotalAnswer?: number;\r\n  CRequireAnswer?: number;\r\n  CUiType?: number;\r\n  selectedCUiType: any | null;\r\n  selectedItems: { [key: string]: boolean }\r\n  selectedRemarkType?: { [key: string]: boolean }\r\n  allSelected: boolean,\r\n  listPictures: any[],\r\n  currentImageIndex?: number; // 當前顯示的圖片索引\r\n  isModalOpen?: boolean; // 是否打開放大模態窗口\r\n  selectedHouseholdsCached?: string[]; // 緩存已選戶別，避免重複計算\r\n}\r\n\r\n@Component({\r\n  selector: 'ngx-detail-content-management-sales-account',\r\n  templateUrl: './detail-content-management-sales-account.component.html',\r\n  styleUrls: ['./detail-content-management-sales-account.component.scss'],\r\n  standalone: true,\r\n  imports: [CommonModule, SharedModule, AppSharedModule, NbCheckboxModule, Base64ImagePipe],\r\n  changeDetection: ChangeDetectionStrategy.OnPush\r\n})\r\n\r\nexport class DetailContentManagementSalesAccountComponent extends BaseComponent implements OnInit, OnDestroy {\r\n  constructor(\r\n    private _allow: AllowHelper,\r\n    private route: ActivatedRoute,\r\n    private message: MessageService,\r\n    private _formItemService: FormItemService,\r\n    private _regularNoticeFileService: RegularNoticeFileService,\r\n    private _utilityService: UtilityService,\r\n    private valid: ValidationHelper,\r\n    private location: Location,\r\n    private _materialService: MaterialService,\r\n    private _eventService: EventService,\r\n    private _houseCustomService: HouseCustomService\r\n  ) {\r\n    super(_allow)\r\n  }\r\n\r\n  // 通知類型選項映射\r\n  cNoticeTypeOptions = [\r\n    { label: '地主戶', value: EnumHouseType.地主戶 },\r\n    { label: '銷售戶', value: EnumHouseType.銷售戶 }\r\n  ];\r\n  // 動態獲取標題文字\r\n  get dynamicTitle(): string {\r\n    const option = this.cNoticeTypeOptions.find(option =>\r\n      option.value === this.houseType // 直接使用 this.houseType\r\n    );\r\n    return option ? `類型 - ${option.label}` : '類型 - 獨立選樣';\r\n  }\r\n\r\n\r\n  CUiTypeOptions: any[] = [\r\n    {\r\n      value: 1, label: '建材選色'\r\n    },\r\n    {\r\n      value: 2, label: '群組選樣_選色'\r\n    }, {\r\n      value: 3, label: '建材選樣'\r\n    }];\r\n  CRemarkTypeOptions = [\"正常\", \"留料\"];\r\n  buildCaseId: number;\r\n  houseType = 0;\r\n\r\n  override ngOnInit(): void {\r\n    this.route.paramMap.subscribe(params => {\r\n      if (params) {\r\n        const idParam = params.get('id');\r\n        const id = idParam ? +idParam : 0;\r\n        this.buildCaseId = id\r\n\r\n        if (this.buildCaseId <= 0) {\r\n          // 如果 buildCaseId 為 0 或無效，顯示錯誤訊息並返回\r\n          console.error('Invalid buildCaseId:', this.buildCaseId);\r\n          this.message.showErrorMSG(\"無效的建案ID，請重新選擇建案\");\r\n          this.goBack();\r\n          return;\r\n        }\r\n      }\r\n    });\r\n\r\n    // 處理查詢參數中的戶型\r\n    this.route.queryParams.subscribe(queryParams => {\r\n      if (queryParams['houseType']) {\r\n        this.houseType = Number(queryParams['houseType']);\r\n\r\n        // 簡化邏輯：直接初始化資料\r\n        if (this.buildCaseId > 0) {\r\n          this.initializeData();\r\n        }\r\n      }\r\n    });\r\n  }\r\n\r\n  ngOnDestroy(): void {\r\n    // 確保在組件銷毀時恢復body的滾動\r\n    document.body.style.overflow = 'auto';\r\n  }\r\n\r\n  getItemByValue(value: any, options: any[]) {\r\n    for (const item of options) {\r\n      if (item.value === value) {\r\n        return item;\r\n      }\r\n    }\r\n    return null;\r\n  }\r\n\r\n  selectedItems: { [key: string]: boolean } = {};\r\n  selectedRemarkType: { [key: string]: boolean } = {};\r\n\r\n  // 新增：戶別選擇器相關屬性\r\n  buildingData: any = {}; // 存放建築物戶別資料\r\n\r\n  detectFiles(event: any, formItemReq_: any) {\r\n    const file = event.target.files[0]\r\n    if (file) {\r\n      let reader = new FileReader();\r\n      reader.readAsDataURL(file);\r\n      reader.onload = () => {\r\n        let base64Str: string = reader.result as string;\r\n        if (!base64Str) {\r\n          return;\r\n        }\r\n        if (formItemReq_.listPictures.length > 0) {\r\n          formItemReq_.listPictures[0] = {\r\n            id: new Date().getTime(),\r\n            name: file.name.split('.')[0],\r\n            data: base64Str,\r\n            extension: this._utilityService.getFileExtension(file.name),\r\n            CFile: file\r\n          };\r\n        } else {\r\n          formItemReq_.listPictures.push({\r\n            id: new Date().getTime(),\r\n            name: file.name.split('.')[0],\r\n            data: base64Str,\r\n            extension: this._utilityService.getFileExtension(file.name),\r\n            CFile: file\r\n          });\r\n        }\r\n        event.target.value = null;\r\n      };\r\n    }\r\n  }\r\n\r\n  removeImage(pictureId: number, formItemReq_: any) {\r\n    if (formItemReq_.listPictures.length) {\r\n      formItemReq_.listPictures = formItemReq_.listPictures.filter((x: any) => x.id != pictureId)\r\n    }\r\n  }\r\n  renameFile(event: any, index: number, formItemReq_: any) {\r\n    var blob = formItemReq_.listPictures[index].CFile.slice(0, formItemReq_.listPictures[index].CFile.size, formItemReq_.listPictures[index].CFile.type);\r\n    var newFile = new File([blob], `${event.target.value + '.' + formItemReq_.listPictures[index].extension}`, { type: formItemReq_.listPictures[index].CFile.type });\r\n    formItemReq_.listPictures[index].CFile = newFile\r\n  }\r\n\r\n  // 輪播功能方法\r\n  nextImage(formItemReq: any) {\r\n    if (formItemReq.CMatrialUrl && formItemReq.CMatrialUrl.length > 0) {\r\n      formItemReq.currentImageIndex = (formItemReq.currentImageIndex + 1) % formItemReq.CMatrialUrl.length;\r\n    }\r\n  }\r\n\r\n  prevImage(formItemReq: any) {\r\n    if (formItemReq.CMatrialUrl && formItemReq.CMatrialUrl.length > 0) {\r\n      formItemReq.currentImageIndex = formItemReq.currentImageIndex === 0\r\n        ? formItemReq.CMatrialUrl.length - 1\r\n        : formItemReq.currentImageIndex - 1;\r\n    }\r\n  }\r\n  getCurrentImage(formItemReq: any): string | null {\r\n    if (formItemReq.CMatrialUrl && formItemReq.CMatrialUrl.length > 0 && formItemReq.currentImageIndex !== undefined) {\r\n      return formItemReq.CMatrialUrl[formItemReq.currentImageIndex];\r\n    }\r\n    return null;\r\n  }\r\n\r\n  // 放大功能方法\r\n  openImageModal(formItemReq: any, imageIndex?: number) {\r\n    if (formItemReq.CMatrialUrl && formItemReq.CMatrialUrl.length > 0) {\r\n      if (imageIndex !== undefined) {\r\n        formItemReq.currentImageIndex = imageIndex;\r\n      }\r\n      formItemReq.isModalOpen = true;\r\n      // 防止背景滾動\r\n      document.body.style.overflow = 'hidden';\r\n    }\r\n  }\r\n\r\n  closeImageModal(formItemReq: any) {\r\n    formItemReq.isModalOpen = false;\r\n    // 恢復背景滾動\r\n    document.body.style.overflow = 'auto';\r\n  }\r\n\r\n  // 模態窗口中的輪播方法\r\n  nextImageModal(formItemReq: any) {\r\n    this.nextImage(formItemReq);\r\n  }\r\n\r\n  prevImageModal(formItemReq: any) {\r\n    this.prevImage(formItemReq);\r\n  }\r\n\r\n  // 鍵盤事件處理\r\n  onKeydown(event: KeyboardEvent, formItemReq: any) {\r\n    if (formItemReq.isModalOpen) {\r\n      switch (event.key) {\r\n        case 'ArrowLeft':\r\n          event.preventDefault();\r\n          this.prevImageModal(formItemReq);\r\n          break;\r\n        case 'ArrowRight':\r\n          event.preventDefault();\r\n          this.nextImageModal(formItemReq);\r\n          break;\r\n        case 'Escape':\r\n          event.preventDefault();\r\n          this.closeImageModal(formItemReq);\r\n          break;\r\n      }\r\n    }\r\n  }\r\n\r\n  // 新增：從 HouseholdItem 陣列中提取戶別代碼\r\n  extractHouseholdCodes(households: any[]): string[] {\r\n    if (!households || !Array.isArray(households)) {\r\n      return [];\r\n    }\r\n    return households.map(h => h.code || h);\r\n  }\r\n  // 新增：處理戶別選擇變更\r\n  onHouseholdSelectionChange(selectedHouseholds: string[], formItemReq: any) {\r\n    // 重置所有戶別選擇狀態\r\n    Object.keys(formItemReq.selectedItems).forEach(key => {\r\n      formItemReq.selectedItems[key] = false;\r\n    });\r\n\r\n    // 設置選中的戶別\r\n    selectedHouseholds.forEach(household => {\r\n      formItemReq.selectedItems[household] = true;\r\n    });\r\n\r\n    // 更新全選狀態\r\n    formItemReq.allSelected = this.houseHoldList.length > 0 &&\r\n      this.houseHoldList.every(item => formItemReq.selectedItems[item]);\r\n\r\n    // 更新緩存\r\n    this.updateSelectedHouseholdsCache(formItemReq);\r\n  }\r\n\r\n  // 新增：取得已選戶別數組\r\n  getSelectedHouseholds(formItemReq: any): string[] {\r\n    return Object.keys(formItemReq.selectedItems).filter(key => formItemReq.selectedItems[key]);\r\n  }\r\n\r\n  // 新增：更新已選戶別緩存\r\n  private updateSelectedHouseholdsCache(formItemReq: any): void {\r\n    console.log('formItemReq:', formItemReq);\r\n    formItemReq.selectedHouseholdsCached = this.getSelectedHouseholds(formItemReq);\r\n    console.log('Updated selected households cache:', formItemReq.selectedHouseholdsCached);\r\n\r\n  }\r\n\r\n  // 新增：更新所有項目的緩存\r\n  private updateAllSelectedHouseholdsCache(): void {\r\n    if (this.arrListFormItemReq) {\r\n      this.arrListFormItemReq.forEach(formItemReq => {\r\n        this.updateSelectedHouseholdsCache(formItemReq);\r\n      });\r\n    }\r\n  }\r\n\r\n\r\n\r\n  onCheckboxRemarkChange(checked: boolean, item: string, formItemReq_: any) {\r\n    formItemReq_.selectedRemarkType[item] = checked;\r\n  }\r\n\r\n  createRemarkObject(CRemarkTypeOptions: string[], CRemarkType: string): { [key: string]: boolean } {\r\n    const remarkObject: { [key: string]: boolean } = {};\r\n    for (const option of CRemarkTypeOptions) {\r\n      remarkObject[option] = false;\r\n    }\r\n    const remarkTypes = CRemarkType.split('-');\r\n    for (const type of remarkTypes) {\r\n      if (CRemarkTypeOptions.includes(type)) {\r\n        remarkObject[type] = true;\r\n      }\r\n    }\r\n    return remarkObject;\r\n  }\r\n\r\n  mergeItems(items: ExtendedSaveListFormItemReq[]): ExtendedSaveListFormItemReq[] {\r\n    const map = new Map<string, { item: ExtendedSaveListFormItemReq, count: number }>();\r\n\r\n    items.forEach(item => {\r\n      const key = `${item.CLocation}_${item.CName}_${item.CPart}`;\r\n      if (map.has(key)) {\r\n        const existing = map.get(key)!;\r\n        existing.count += 1;\r\n      } else {\r\n        map.set(key, { item: { ...item }, count: 1 });\r\n      }\r\n    });\r\n\r\n    return Array.from(map.values()).map(({ item, count }) => ({\r\n      ...item,\r\n      CTotalAnswer: count\r\n    }));\r\n  }\r\n\r\n  listFormItem: GetListFormItemRes\r\n  isNew: boolean = true\r\n\r\n  getListFormItem() {\r\n    this._formItemService.apiFormItemGetListFormItemPost$Json({\r\n      body: {\r\n        CBuildCaseId: this.buildCaseId,\r\n        CFormType: this.houseType,\r\n        CIsPaging: false\r\n      }\r\n    }).pipe(\r\n      tap(res => {\r\n        if (res.Entries && res.StatusCode == 0) {\r\n          this.listFormItem = res.Entries\r\n          this.isNew = res.Entries.formItems ? false : true\r\n          if (res.Entries.formItems) {\r\n            this.houseHoldList.forEach(item => this.selectedItems[item] = false);\r\n            this.CRemarkTypeOptions.forEach(item => this.selectedRemarkType[item] = false);\r\n\r\n            this.arrListFormItemReq = res.Entries.formItems.map((o: any) => {\r\n              return {\r\n                CFormId: this.listFormItem.CFormId,\r\n                CDesignFileUrl: o.CDesignFileUrl,\r\n                CMatrialUrl: o.CMatrialUrl || (o.CFirstMatrialUrl ? [o.CFirstMatrialUrl] : []),\r\n                CFile: o.CFile,\r\n                CFormItemHouseHold: o.CFormItemHouseHold,\r\n                CFormItemId: o.CFormItemId,\r\n                CLocation: o.CLocation,\r\n                CName: o.CName,\r\n                CPart: o.CPart,\r\n                CItemName: o.CItemName ? o.CItemName : `${o.CName}-${o.CPart}-${o.CLocation}`,\r\n                CRemarkType: o.CRemarkType,\r\n                CTotalAnswer: o.CTotalAnswer,\r\n                CRequireAnswer: o.CUiType === 3 ? 1 : o.CRequireAnswer,\r\n                CUiType: o.CUiType,\r\n                selectedItems: o.tblFormItemHouseholds.length ? this.createArrayObjectFromArray(this.houseHoldList, o.tblFormItemHouseholds) : { ...this.selectedItems }, selectedRemarkType: o.CRemarkType ? this.createRemarkObject(this.CRemarkTypeOptions, o.CRemarkType) : { ...this.selectedRemarkType },\r\n                allSelected: o.tblFormItemHouseholds.length === this.houseHoldList.length,\r\n                listPictures: [], selectedCUiType: o.CUiType ? this.getItemByValue(o.CUiType, this.CUiTypeOptions) : this.CUiTypeOptions[0],\r\n                currentImageIndex: 0,\r\n                isModalOpen: false,\r\n                selectedHouseholdsCached: [] // 初始化緩存，稍後會更新\r\n              }\r\n            })\r\n          } else {\r\n            // 如果沒有現有的表單項目，則不執行任何操作\r\n          }\r\n\r\n          // 初始化所有項目的緩存\r\n          this.updateAllSelectedHouseholdsCache();\r\n        }\r\n      })\r\n    ).subscribe()\r\n  }\r\n\r\n  changeSelectCUiType(formItemReq: any) {\r\n    if (formItemReq.selectedCUiType && formItemReq.selectedCUiType.value === 3) {\r\n      formItemReq.CRequireAnswer = 1\r\n    }\r\n  }\r\n\r\n  arrListFormItemReq: ExtendedSaveListFormItemReq[]\r\n\r\n  getKeysWithTrueValue(obj: Record<string, boolean>): string[] { // {\"key\": true } => [\"key\"]\r\n    return Object.keys(obj).filter(key => obj[key]);\r\n  }\r\n\r\n  getKeysWithTrueValueJoined(obj: Record<string, boolean>): string {\r\n    return Object.keys(obj)\r\n      .filter(key => obj[key])\r\n      .join('-');\r\n  }\r\n\r\n  getCRemarkType(selectedCUiType: any, selectedRemarkType: any): any {\r\n    if (selectedCUiType && selectedCUiType.value == 3) {\r\n      return this.getKeysWithTrueValueJoined(selectedRemarkType)\r\n    }\r\n  }\r\n\r\n  getStringAfterComma(inputString: string): string {\r\n    const parts = inputString.split(',');\r\n    if (parts.length > 1) {\r\n      return parts[1];\r\n    } else return \"\"\r\n  }\r\n\r\n  formatFile(listPictures: any) {\r\n    if (listPictures && listPictures.length > 0) {\r\n      return {\r\n        Base64String: this.getStringAfterComma(listPictures[0].data) || null,\r\n        FileExtension: listPictures[0].extension || null,\r\n        FileName: listPictures[0].CFile.name || listPictures[0].name || null,\r\n      }\r\n    } else return undefined\r\n\r\n  }\r\n\r\n\r\n  validation() {\r\n    this.valid.clear();\r\n    let hasInvalidCUiType = false;\r\n    let hasInvalidCRequireAnswer = false;\r\n    let hasInvalidItemName = false;\r\n\r\n    for (const item of this.saveListFormItemReq) {\r\n      if (!hasInvalidCUiType && (!item.CUiType)) {\r\n        hasInvalidCUiType = true;\r\n      }\r\n      if (!hasInvalidCRequireAnswer && (!item.CRequireAnswer)) {\r\n        hasInvalidCRequireAnswer = true;\r\n      }\r\n      if (item.CTotalAnswer && item.CRequireAnswer) {\r\n        if (item.CRequireAnswer > item.CTotalAnswer || item.CRequireAnswer < 0) {\r\n          this.valid.addErrorMessage('[必填數量]' + ' <= ' + item.CTotalAnswer + ` (${item.CItemName}) `);\r\n        }\r\n      }\r\n\r\n      if (!hasInvalidItemName && (!item.CItemName)) {\r\n        hasInvalidItemName = true;\r\n      }\r\n    }\r\n    if (hasInvalidCUiType) {\r\n      this.valid.addErrorMessage('[前台UI類型]' + ' 必填');\r\n    }\r\n    if (hasInvalidCRequireAnswer) {\r\n      this.valid.addErrorMessage('[必填數量]' + ' 必填且>0');\r\n    }\r\n    if (hasInvalidItemName) {\r\n      this.valid.addErrorMessage('[廚房-廚具-櫃體]' + ' 必填');\r\n    }\r\n  }\r\n\r\n  saveListFormItemReq: SaveListFormItemReq[]\r\n\r\n  onSubmit() {\r\n    this.saveListFormItemReq = this.arrListFormItemReq.map((e: any) => {\r\n      return {\r\n        CDesignFileUrl: e.CDesignFileUrl ? e.CDesignFileUrl : null,\r\n        CFile: e.listPictures ? this.formatFile(e.listPictures) : undefined,\r\n        CFormItemHouseHold: this.getKeysWithTrueValue(e.selectedItems),\r\n        CFormItemId: e.CFormItemId ? e.CFormItemId : null,\r\n        CFormID: this.isNew ? null : this.listFormItem.CFormId,\r\n        CName: e.CName,\r\n        CPart: e.CPart,\r\n        CLocation: e.CLocation,\r\n        CItemName: e.CItemName, //? e.CItemName : `${e.CName}-${e.CPart}-${e.CLocation}`,\r\n        CRemarkType: e.selectedCUiType.value !== 3 ? null : this.getCRemarkType(e.selectedCUiType, e.selectedRemarkType) || null,\r\n        CTotalAnswer: e.CTotalAnswer,\r\n        CRequireAnswer: e.CRequireAnswer,\r\n        CUiType: e.selectedCUiType.value,\r\n      }\r\n    })\r\n    this.validation()\r\n    if (this.valid.errorMessages.length > 0) {\r\n      this.message.showErrorMSGs(this.valid.errorMessages);\r\n      return\r\n    }\r\n    if (this.isNew) {\r\n      this.createListFormItem()\r\n\r\n    } else {\r\n      this.saveListFormItem()\r\n    }\r\n  }\r\n\r\n  saveListFormItem() {\r\n    this._formItemService.apiFormItemSaveListFormItemPost$Json({\r\n      body: this.saveListFormItemReq\r\n    }).subscribe(res => {\r\n      if (res.StatusCode == 0) {\r\n        this.message.showSucessMSG(\"執行成功\");\r\n        // this.getListFormItem()\r\n        this.goBack()\r\n      }\r\n    })\r\n  }\r\n\r\n  creatListFormItem: CreateListFormItem\r\n\r\n  createListFormItem() {\r\n    this.creatListFormItem = {\r\n      CBuildCaseId: this.buildCaseId,\r\n      CFormItem: this.saveListFormItemReq || null,\r\n      CFormType: this.houseType, // 直接使用 this.houseType\r\n    }\r\n\r\n    this._formItemService.apiFormItemCreateListFormItemPost$Json({\r\n      body: this.creatListFormItem\r\n    }).subscribe(res => {\r\n      if (res.StatusCode == 0) {\r\n        this.message.showSucessMSG(\"執行成功\");\r\n        // this.getListFormItem()\r\n        this.goBack()\r\n      }\r\n    })\r\n  }\r\n\r\n  createArrayObjectFromArray(a: string[], b: { CFormItemHouseholdId: number, CFormItemId: number, CHousehold: string, CIsSelect: boolean }[]): { [key: string]: boolean } {\r\n    const c: { [key: string]: boolean } = {};\r\n    for (const item of a) {\r\n      const matchingItem = b.find(bItem => bItem.CHousehold === item && bItem.CIsSelect);\r\n      c[item] = !!matchingItem;\r\n    }\r\n    return c;\r\n  } //[\"House1\", \"House2\", \"House3\"] => [{CHousehold: \"House1\", CIsSelect: true,... }, ... ]\r\n\r\n  /**\r\n   * 複製當前表單到新表單\r\n   */\r\n  copyToNewForm() {\r\n    // 先取得當前有效的材料清單\r\n    this._materialService.apiMaterialGetMaterialListPost$Json({\r\n      body: {\r\n        CBuildCaseId: this.buildCaseId,\r\n        CPagi: false\r\n      }\r\n    }).pipe(\r\n      tap(res => {\r\n        if (res.Entries && res.StatusCode == 0) {\r\n          // 建立有效材料清單的鍵值對應\r\n          const validMaterialKeys = new Set<string>();\r\n          res.Entries.forEach((material: any) => {\r\n            const key = `${material.CLocation}_${material.CName}_${material.CPart}`;\r\n            validMaterialKeys.add(key);\r\n          });\r\n\r\n          // 篩選出仍然有效的表單項目\r\n          const validFormItems = this.arrListFormItemReq.filter((item: any) => {\r\n            const itemKey = `${item.CLocation}_${item.CName}_${item.CPart}`;\r\n            return validMaterialKeys.has(itemKey);\r\n          });\r\n\r\n          if (validFormItems.length === 0) {\r\n            this.message.showErrorMSG(\"沒有有效的表單項目可以複製\");\r\n            return;\r\n          }\r\n\r\n          // 準備複製的表單項目數據\r\n          this.saveListFormItemReq = validFormItems.map((e: any) => {\r\n            return {\r\n              CDesignFileUrl: e.CDesignFileUrl ? e.CDesignFileUrl : null,\r\n              CFile: e.listPictures ? this.formatFile(e.listPictures) : undefined,\r\n              CFormItemHouseHold: this.getKeysWithTrueValue(e.selectedItems),\r\n              CFormItemId: null, // 設為 null 以建立新項目\r\n              CFormID: null, // 設為 null 以建立新表單\r\n              CName: e.CName,\r\n              CPart: e.CPart,\r\n              CLocation: e.CLocation,\r\n              CItemName: e.CItemName,\r\n              CRemarkType: e.selectedCUiType.value !== 3 ? null : this.getCRemarkType(e.selectedCUiType, e.selectedRemarkType) || null,\r\n              CTotalAnswer: e.CTotalAnswer,\r\n              CRequireAnswer: e.CRequireAnswer,\r\n              CUiType: e.selectedCUiType.value,\r\n            }\r\n          });\r\n\r\n          // 執行驗證\r\n          this.validation()\r\n          if (this.valid.errorMessages.length > 0) {\r\n            this.message.showErrorMSGs(this.valid.errorMessages);\r\n            return\r\n          }\r\n\r\n          // 建立複製的表單\r\n          this.creatListFormItem = {\r\n            CBuildCaseId: this.buildCaseId,\r\n            CFormItem: this.saveListFormItemReq || null,\r\n            CFormType: this.houseType, // 直接使用 this.houseType\r\n          }\r\n\r\n          this._formItemService.apiFormItemCreateListFormItemPost$Json({\r\n            body: this.creatListFormItem\r\n          }).subscribe(createRes => {\r\n            if (createRes.StatusCode == 0) {\r\n              this.message.showSucessMSG(`複製表單成功，已篩選 ${validFormItems.length} 個有效項目`);\r\n              // 重新載入資料以顯示新的未鎖定表單\r\n              this.getListFormItem()\r\n            }\r\n          })\r\n        } else {\r\n          this.message.showErrorMSG(\"無法取得材料清單，複製失敗\");\r\n        }\r\n      })\r\n    ).subscribe();\r\n  }\r\n\r\n  // 新增：載入建築物戶別資料 (只呼叫一次 GetDropDown API)\r\n  private loadBuildingDataFromAPI(): void {\r\n    if (!this.buildCaseId) return;\r\n\r\n    this._houseCustomService.getDropDown(this.buildCaseId).subscribe({\r\n      next: (response) => {\r\n        console.log('GetDropDown API response:', response);\r\n        if (response.Entries) {\r\n          this.buildingData = this.convertApiResponseToBuildingData(response.Entries);\r\n          console.log('Converted buildingData:', this.buildingData);\r\n        }\r\n      },\r\n      error: (error) => {\r\n        console.error('Error loading building data from API:', error);\r\n        // 如果API載入失敗，使用 houseHoldList 來產生基本的建築物資料\r\n        if (this.houseHoldList && this.houseHoldList.length > 0) {\r\n          this.buildingData = this.convertHouseHoldListToBuildingData(this.houseHoldList);\r\n        }\r\n      }\r\n    });\r\n  }\r\n\r\n  // 新增：將 API 回應轉換為建築物資料格式\r\n  private convertApiResponseToBuildingData(entries: any): any {\r\n    const buildingData: any = {};\r\n\r\n    Object.entries(entries).forEach(([building, houses]: [string, any]) => {\r\n      // 根據 houseType 篩選戶別\r\n      const filteredHouses = houses.filter((house: any) => {\r\n        return this.shouldIncludeHouse(house);\r\n      });\r\n\r\n      // 只有在有符合條件的戶別時才加入建築物\r\n      if (filteredHouses.length > 0) {\r\n        buildingData[building] = filteredHouses.map((house: any) => ({\r\n          code: house.HouseName,\r\n          building: house.Building,\r\n          floor: house.Floor,\r\n          houseId: house.HouseId,\r\n          houseName: house.HouseName,\r\n          isSelected: false,\r\n          isDisabled: false\r\n        }));\r\n      }\r\n    });\r\n\r\n    return buildingData;\r\n  }\r\n\r\n  // 新增：判斷是否應該包含此戶別的邏輯\r\n  private shouldIncludeHouse(house: any): boolean {\r\n    // 如果 houseType 為 0 或未設定，顯示所有戶別\r\n    if (!this.houseType || this.houseType === 0) {\r\n      return true;\r\n    }\r\n\r\n    // 根據業務邏輯判斷戶別類型\r\n    // 這裡需要根據實際的 API 資料結構來調整判斷邏輯\r\n\r\n    // 方案1: 如果 API 回傳的 house 物件中有 houseType 欄位\r\n    if (house.HouseType !== undefined) {\r\n      return house.HouseType === this.houseType;\r\n    }\r\n\r\n    // 方案2: 根據戶別名稱的命名規則判斷（需要根據實際規則調整）\r\n    // 例如：地主戶可能以特定前綴開頭，銷售戶以另一個前綴開頭\r\n    const houseName = house.HouseName || house.houseName || '';\r\n\r\n    if (this.houseType === EnumHouseType.地主戶) {\r\n      // 地主戶的判斷邏輯（需要根據實際規則調整）\r\n      // 例如：戶別名稱包含 \"地主\" 或特定前綴\r\n      return houseName.includes('地主') || houseName.startsWith('L');\r\n    } else if (this.houseType === EnumHouseType.銷售戶) {\r\n      // 銷售戶的判斷邏輯（需要根據實際規則調整）\r\n      // 例如：戶別名稱包含 \"銷售\" 或不包含 \"地主\"\r\n      return houseName.includes('銷售') || !houseName.includes('地主');\r\n    }\r\n\r\n    // 預設情況：顯示所有戶別\r\n    return true;\r\n  }\r\n\r\n  // 新增：將戶別清單轉換為建築物資料格式\r\n  convertHouseHoldListToBuildingData(houseHoldList: string[]): any {\r\n    if (!houseHoldList || houseHoldList.length === 0) {\r\n      return {};\r\n    }\r\n\r\n    // 如果戶別有明確的建築物前綴（如 A001, B002），我們可以依此分組\r\n    const buildingData: any = {};\r\n\r\n    houseHoldList.forEach(household => {\r\n      // 嘗試從戶別名稱中提取建築物代碼\r\n      const buildingMatch = household.match(/^([A-Z]+)/);\r\n      const building = buildingMatch ? `${buildingMatch[1]}棟` : '預設建築';\r\n\r\n      if (!buildingData[building]) {\r\n        buildingData[building] = [];\r\n      }\r\n\r\n      // 計算樓層（假設每4戶為一層）\r\n      const houseNumber = parseInt(household.replace(/[A-Z]/g, ''));\r\n      const floor = Math.ceil(houseNumber / 4);\r\n\r\n      buildingData[building].push({\r\n        code: household,\r\n        building: building,\r\n        floor: `${floor}F`,\r\n        isSelected: false,\r\n        isDisabled: false\r\n      });\r\n    }); return buildingData;\r\n  }\r\n\r\n  // 戶別清單 - 使用 this.houseType 簡化邏輯，不再需要 API 查詢\r\n  houseHoldList: any[] = [];\r\n\r\n  // 簡化的初始化方法，使用 this.houseType 替換 getListRegularNoticeFileHouseHold\r\n  private initializeData(): void {\r\n    // 初始化空的戶別清單（如果需要的話，可以從其他地方取得）\r\n    this.houseHoldList = [];\r\n\r\n    // 載入建築物資料\r\n    this.loadBuildingDataFromAPI();\r\n\r\n    // 載入表單項目\r\n    this.getListFormItem();\r\n  }\r\n\r\n  goBack() {\r\n    this._eventService.push({\r\n      action: EEvent.GET_BUILDCASE,\r\n      payload: this.buildCaseId\r\n    })\r\n    this.location.back()\r\n  }\r\n\r\n}\r\n"], "mappings": ";AAAA,SAASA,SAAS,EAAqBC,uBAAuB,QAAQ,eAAe;AACrF,SAASC,YAAY,QAAkB,iBAAiB;AACxD,SAASC,gBAAgB,QAAQ,gBAAgB;AAMjD,SAASC,GAAG,QAAQ,MAAM;AAI1B,SAASC,YAAY,QAAQ,wCAAwC;AACrE,SAASA,YAAY,IAAIC,eAAe,QAAQ,8BAA8B;AAC9E,SAASC,aAAa,QAAQ,6CAA6C;AAE3E,SAASC,eAAe,QAAQ,4CAA4C;AAC5E,SAASC,aAAa,QAAQ,mCAAmC;AAqC1D,IAAMC,4CAA4C,GAAlD,MAAMA,4CAA6C,SAAQH,aAAa;EAC7EI,YACUC,MAAmB,EACnBC,KAAqB,EACrBC,OAAuB,EACvBC,gBAAiC,EACjCC,yBAAmD,EACnDC,eAA+B,EAC/BC,KAAuB,EACvBC,QAAkB,EAClBC,gBAAiC,EACjCC,aAA2B,EAC3BC,mBAAuC;IAE/C,KAAK,CAACV,MAAM,CAAC;IAZL,KAAAA,MAAM,GAANA,MAAM;IACN,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,yBAAyB,GAAzBA,yBAAyB;IACzB,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,QAAQ,GAARA,QAAQ;IACR,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,mBAAmB,GAAnBA,mBAAmB;IAK7B;IACA,KAAAC,kBAAkB,GAAG,CACnB;MAAEC,KAAK,EAAE,KAAK;MAAEC,KAAK,EAAEhB,aAAa,CAACiB;IAAG,CAAE,EAC1C;MAAEF,KAAK,EAAE,KAAK;MAAEC,KAAK,EAAEhB,aAAa,CAACkB;IAAG,CAAE,CAC3C;IAUD,KAAAC,cAAc,GAAU,CACtB;MACEH,KAAK,EAAE,CAAC;MAAED,KAAK,EAAE;KAClB,EACD;MACEC,KAAK,EAAE,CAAC;MAAED,KAAK,EAAE;KAClB,EAAE;MACDC,KAAK,EAAE,CAAC;MAAED,KAAK,EAAE;KAClB,CAAC;IACJ,KAAAK,kBAAkB,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC;IAEjC,KAAAC,SAAS,GAAG,CAAC;IA8Cb,KAAAC,aAAa,GAA+B,EAAE;IAC9C,KAAAC,kBAAkB,GAA+B,EAAE;IAEnD;IACA,KAAAC,YAAY,GAAQ,EAAE,CAAC,CAAC;IA0MxB,KAAAC,KAAK,GAAY,IAAI;IA8YrB;IACA,KAAAC,aAAa,GAAU,EAAE;EAtqBzB;EAOA;EACA,IAAIC,YAAYA,CAAA;IACd,MAAMC,MAAM,GAAG,IAAI,CAACd,kBAAkB,CAACe,IAAI,CAACD,MAAM,IAChDA,MAAM,CAACZ,KAAK,KAAK,IAAI,CAACK,SAAS,CAAC;KACjC;IACD,OAAOO,MAAM,GAAG,QAAQA,MAAM,CAACb,KAAK,EAAE,GAAG,WAAW;EACtD;EAgBSe,QAAQA,CAAA;IACf,IAAI,CAAC1B,KAAK,CAAC2B,QAAQ,CAACC,SAAS,CAACC,MAAM,IAAG;MACrC,IAAIA,MAAM,EAAE;QACV,MAAMC,OAAO,GAAGD,MAAM,CAACE,GAAG,CAAC,IAAI,CAAC;QAChC,MAAMC,EAAE,GAAGF,OAAO,GAAG,CAACA,OAAO,GAAG,CAAC;QACjC,IAAI,CAACG,WAAW,GAAGD,EAAE;QAErB,IAAI,IAAI,CAACC,WAAW,IAAI,CAAC,EAAE;UACzB;UACAC,OAAO,CAACC,KAAK,CAAC,sBAAsB,EAAE,IAAI,CAACF,WAAW,CAAC;UACvD,IAAI,CAAChC,OAAO,CAACmC,YAAY,CAAC,iBAAiB,CAAC;UAC5C,IAAI,CAACC,MAAM,EAAE;UACb;QACF;MACF;IACF,CAAC,CAAC;IAEF;IACA,IAAI,CAACrC,KAAK,CAACsC,WAAW,CAACV,SAAS,CAACU,WAAW,IAAG;MAC7C,IAAIA,WAAW,CAAC,WAAW,CAAC,EAAE;QAC5B,IAAI,CAACrB,SAAS,GAAGsB,MAAM,CAACD,WAAW,CAAC,WAAW,CAAC,CAAC;QAEjD;QACA,IAAI,IAAI,CAACL,WAAW,GAAG,CAAC,EAAE;UACxB,IAAI,CAACO,cAAc,EAAE;QACvB;MACF;IACF,CAAC,CAAC;EACJ;EAEAC,WAAWA,CAAA;IACT;IACAC,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,MAAM;EACvC;EAEAC,cAAcA,CAAClC,KAAU,EAAEmC,OAAc;IACvC,KAAK,MAAMC,IAAI,IAAID,OAAO,EAAE;MAC1B,IAAIC,IAAI,CAACpC,KAAK,KAAKA,KAAK,EAAE;QACxB,OAAOoC,IAAI;MACb;IACF;IACA,OAAO,IAAI;EACb;EAQAC,WAAWA,CAACC,KAAU,EAAEC,YAAiB;IACvC,MAAMC,IAAI,GAAGF,KAAK,CAACG,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC;IAClC,IAAIF,IAAI,EAAE;MACR,IAAIG,MAAM,GAAG,IAAIC,UAAU,EAAE;MAC7BD,MAAM,CAACE,aAAa,CAACL,IAAI,CAAC;MAC1BG,MAAM,CAACG,MAAM,GAAG,MAAK;QACnB,IAAIC,SAAS,GAAWJ,MAAM,CAACK,MAAgB;QAC/C,IAAI,CAACD,SAAS,EAAE;UACd;QACF;QACA,IAAIR,YAAY,CAACU,YAAY,CAACC,MAAM,GAAG,CAAC,EAAE;UACxCX,YAAY,CAACU,YAAY,CAAC,CAAC,CAAC,GAAG;YAC7B7B,EAAE,EAAE,IAAI+B,IAAI,EAAE,CAACC,OAAO,EAAE;YACxBC,IAAI,EAAEb,IAAI,CAACa,IAAI,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;YAC7BC,IAAI,EAAER,SAAS;YACfS,SAAS,EAAE,IAAI,CAAChE,eAAe,CAACiE,gBAAgB,CAACjB,IAAI,CAACa,IAAI,CAAC;YAC3DK,KAAK,EAAElB;WACR;QACH,CAAC,MAAM;UACLD,YAAY,CAACU,YAAY,CAACU,IAAI,CAAC;YAC7BvC,EAAE,EAAE,IAAI+B,IAAI,EAAE,CAACC,OAAO,EAAE;YACxBC,IAAI,EAAEb,IAAI,CAACa,IAAI,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;YAC7BC,IAAI,EAAER,SAAS;YACfS,SAAS,EAAE,IAAI,CAAChE,eAAe,CAACiE,gBAAgB,CAACjB,IAAI,CAACa,IAAI,CAAC;YAC3DK,KAAK,EAAElB;WACR,CAAC;QACJ;QACAF,KAAK,CAACG,MAAM,CAACzC,KAAK,GAAG,IAAI;MAC3B,CAAC;IACH;EACF;EAEA4D,WAAWA,CAACC,SAAiB,EAAEtB,YAAiB;IAC9C,IAAIA,YAAY,CAACU,YAAY,CAACC,MAAM,EAAE;MACpCX,YAAY,CAACU,YAAY,GAAGV,YAAY,CAACU,YAAY,CAACa,MAAM,CAAEC,CAAM,IAAKA,CAAC,CAAC3C,EAAE,IAAIyC,SAAS,CAAC;IAC7F;EACF;EACAG,UAAUA,CAAC1B,KAAU,EAAE2B,KAAa,EAAE1B,YAAiB;IACrD,IAAI2B,IAAI,GAAG3B,YAAY,CAACU,YAAY,CAACgB,KAAK,CAAC,CAACP,KAAK,CAACS,KAAK,CAAC,CAAC,EAAE5B,YAAY,CAACU,YAAY,CAACgB,KAAK,CAAC,CAACP,KAAK,CAACU,IAAI,EAAE7B,YAAY,CAACU,YAAY,CAACgB,KAAK,CAAC,CAACP,KAAK,CAACW,IAAI,CAAC;IACpJ,IAAIC,OAAO,GAAG,IAAIC,IAAI,CAAC,CAACL,IAAI,CAAC,EAAE,GAAG5B,KAAK,CAACG,MAAM,CAACzC,KAAK,GAAG,GAAG,GAAGuC,YAAY,CAACU,YAAY,CAACgB,KAAK,CAAC,CAACT,SAAS,EAAE,EAAE;MAAEa,IAAI,EAAE9B,YAAY,CAACU,YAAY,CAACgB,KAAK,CAAC,CAACP,KAAK,CAACW;IAAI,CAAE,CAAC;IACjK9B,YAAY,CAACU,YAAY,CAACgB,KAAK,CAAC,CAACP,KAAK,GAAGY,OAAO;EAClD;EAEA;EACAE,SAASA,CAACC,WAAgB;IACxB,IAAIA,WAAW,CAACC,WAAW,IAAID,WAAW,CAACC,WAAW,CAACxB,MAAM,GAAG,CAAC,EAAE;MACjEuB,WAAW,CAACE,iBAAiB,GAAG,CAACF,WAAW,CAACE,iBAAiB,GAAG,CAAC,IAAIF,WAAW,CAACC,WAAW,CAACxB,MAAM;IACtG;EACF;EAEA0B,SAASA,CAACH,WAAgB;IACxB,IAAIA,WAAW,CAACC,WAAW,IAAID,WAAW,CAACC,WAAW,CAACxB,MAAM,GAAG,CAAC,EAAE;MACjEuB,WAAW,CAACE,iBAAiB,GAAGF,WAAW,CAACE,iBAAiB,KAAK,CAAC,GAC/DF,WAAW,CAACC,WAAW,CAACxB,MAAM,GAAG,CAAC,GAClCuB,WAAW,CAACE,iBAAiB,GAAG,CAAC;IACvC;EACF;EACAE,eAAeA,CAACJ,WAAgB;IAC9B,IAAIA,WAAW,CAACC,WAAW,IAAID,WAAW,CAACC,WAAW,CAACxB,MAAM,GAAG,CAAC,IAAIuB,WAAW,CAACE,iBAAiB,KAAKG,SAAS,EAAE;MAChH,OAAOL,WAAW,CAACC,WAAW,CAACD,WAAW,CAACE,iBAAiB,CAAC;IAC/D;IACA,OAAO,IAAI;EACb;EAEA;EACAI,cAAcA,CAACN,WAAgB,EAAEO,UAAmB;IAClD,IAAIP,WAAW,CAACC,WAAW,IAAID,WAAW,CAACC,WAAW,CAACxB,MAAM,GAAG,CAAC,EAAE;MACjE,IAAI8B,UAAU,KAAKF,SAAS,EAAE;QAC5BL,WAAW,CAACE,iBAAiB,GAAGK,UAAU;MAC5C;MACAP,WAAW,CAACQ,WAAW,GAAG,IAAI;MAC9B;MACAnD,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,QAAQ;IACzC;EACF;EAEAiD,eAAeA,CAACT,WAAgB;IAC9BA,WAAW,CAACQ,WAAW,GAAG,KAAK;IAC/B;IACAnD,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,MAAM;EACvC;EAEA;EACAkD,cAAcA,CAACV,WAAgB;IAC7B,IAAI,CAACD,SAAS,CAACC,WAAW,CAAC;EAC7B;EAEAW,cAAcA,CAACX,WAAgB;IAC7B,IAAI,CAACG,SAAS,CAACH,WAAW,CAAC;EAC7B;EAEA;EACAY,SAASA,CAAC/C,KAAoB,EAAEmC,WAAgB;IAC9C,IAAIA,WAAW,CAACQ,WAAW,EAAE;MAC3B,QAAQ3C,KAAK,CAACgD,GAAG;QACf,KAAK,WAAW;UACdhD,KAAK,CAACiD,cAAc,EAAE;UACtB,IAAI,CAACH,cAAc,CAACX,WAAW,CAAC;UAChC;QACF,KAAK,YAAY;UACfnC,KAAK,CAACiD,cAAc,EAAE;UACtB,IAAI,CAACJ,cAAc,CAACV,WAAW,CAAC;UAChC;QACF,KAAK,QAAQ;UACXnC,KAAK,CAACiD,cAAc,EAAE;UACtB,IAAI,CAACL,eAAe,CAACT,WAAW,CAAC;UACjC;MACJ;IACF;EACF;EAEA;EACAe,qBAAqBA,CAACC,UAAiB;IACrC,IAAI,CAACA,UAAU,IAAI,CAACC,KAAK,CAACC,OAAO,CAACF,UAAU,CAAC,EAAE;MAC7C,OAAO,EAAE;IACX;IACA,OAAOA,UAAU,CAACG,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACC,IAAI,IAAID,CAAC,CAAC;EACzC;EACA;EACAE,0BAA0BA,CAACC,kBAA4B,EAAEvB,WAAgB;IACvE;IACAwB,MAAM,CAACC,IAAI,CAACzB,WAAW,CAACnE,aAAa,CAAC,CAAC6F,OAAO,CAACb,GAAG,IAAG;MACnDb,WAAW,CAACnE,aAAa,CAACgF,GAAG,CAAC,GAAG,KAAK;IACxC,CAAC,CAAC;IAEF;IACAU,kBAAkB,CAACG,OAAO,CAACC,SAAS,IAAG;MACrC3B,WAAW,CAACnE,aAAa,CAAC8F,SAAS,CAAC,GAAG,IAAI;IAC7C,CAAC,CAAC;IAEF;IACA3B,WAAW,CAAC4B,WAAW,GAAG,IAAI,CAAC3F,aAAa,CAACwC,MAAM,GAAG,CAAC,IACrD,IAAI,CAACxC,aAAa,CAAC4F,KAAK,CAAClE,IAAI,IAAIqC,WAAW,CAACnE,aAAa,CAAC8B,IAAI,CAAC,CAAC;IAEnE;IACA,IAAI,CAACmE,6BAA6B,CAAC9B,WAAW,CAAC;EACjD;EAEA;EACA+B,qBAAqBA,CAAC/B,WAAgB;IACpC,OAAOwB,MAAM,CAACC,IAAI,CAACzB,WAAW,CAACnE,aAAa,CAAC,CAACwD,MAAM,CAACwB,GAAG,IAAIb,WAAW,CAACnE,aAAa,CAACgF,GAAG,CAAC,CAAC;EAC7F;EAEA;EACQiB,6BAA6BA,CAAC9B,WAAgB;IACpDnD,OAAO,CAACmF,GAAG,CAAC,cAAc,EAAEhC,WAAW,CAAC;IACxCA,WAAW,CAACiC,wBAAwB,GAAG,IAAI,CAACF,qBAAqB,CAAC/B,WAAW,CAAC;IAC9EnD,OAAO,CAACmF,GAAG,CAAC,oCAAoC,EAAEhC,WAAW,CAACiC,wBAAwB,CAAC;EAEzF;EAEA;EACQC,gCAAgCA,CAAA;IACtC,IAAI,IAAI,CAACC,kBAAkB,EAAE;MAC3B,IAAI,CAACA,kBAAkB,CAACT,OAAO,CAAC1B,WAAW,IAAG;QAC5C,IAAI,CAAC8B,6BAA6B,CAAC9B,WAAW,CAAC;MACjD,CAAC,CAAC;IACJ;EACF;EAIAoC,sBAAsBA,CAACC,OAAgB,EAAE1E,IAAY,EAAEG,YAAiB;IACtEA,YAAY,CAAChC,kBAAkB,CAAC6B,IAAI,CAAC,GAAG0E,OAAO;EACjD;EAEAC,kBAAkBA,CAAC3G,kBAA4B,EAAE4G,WAAmB;IAClE,MAAMC,YAAY,GAA+B,EAAE;IACnD,KAAK,MAAMrG,MAAM,IAAIR,kBAAkB,EAAE;MACvC6G,YAAY,CAACrG,MAAM,CAAC,GAAG,KAAK;IAC9B;IACA,MAAMsG,WAAW,GAAGF,WAAW,CAAC1D,KAAK,CAAC,GAAG,CAAC;IAC1C,KAAK,MAAMe,IAAI,IAAI6C,WAAW,EAAE;MAC9B,IAAI9G,kBAAkB,CAAC+G,QAAQ,CAAC9C,IAAI,CAAC,EAAE;QACrC4C,YAAY,CAAC5C,IAAI,CAAC,GAAG,IAAI;MAC3B;IACF;IACA,OAAO4C,YAAY;EACrB;EAEAG,UAAUA,CAACC,KAAoC;IAC7C,MAAMzB,GAAG,GAAG,IAAI0B,GAAG,EAAgE;IAEnFD,KAAK,CAAClB,OAAO,CAAC/D,IAAI,IAAG;MACnB,MAAMkD,GAAG,GAAG,GAAGlD,IAAI,CAACmF,SAAS,IAAInF,IAAI,CAACoF,KAAK,IAAIpF,IAAI,CAACqF,KAAK,EAAE;MAC3D,IAAI7B,GAAG,CAAC8B,GAAG,CAACpC,GAAG,CAAC,EAAE;QAChB,MAAMqC,QAAQ,GAAG/B,GAAG,CAACzE,GAAG,CAACmE,GAAG,CAAE;QAC9BqC,QAAQ,CAACC,KAAK,IAAI,CAAC;MACrB,CAAC,MAAM;QACLhC,GAAG,CAACiC,GAAG,CAACvC,GAAG,EAAE;UAAElD,IAAI,EAAE;YAAE,GAAGA;UAAI,CAAE;UAAEwF,KAAK,EAAE;QAAC,CAAE,CAAC;MAC/C;IACF,CAAC,CAAC;IAEF,OAAOlC,KAAK,CAACoC,IAAI,CAAClC,GAAG,CAACmC,MAAM,EAAE,CAAC,CAACnC,GAAG,CAAC,CAAC;MAAExD,IAAI;MAAEwF;IAAK,CAAE,MAAM;MACxD,GAAGxF,IAAI;MACP4F,YAAY,EAAEJ;KACf,CAAC,CAAC;EACL;EAKAK,eAAeA,CAAA;IACb,IAAI,CAAC3I,gBAAgB,CAAC4I,mCAAmC,CAAC;MACxDnG,IAAI,EAAE;QACJoG,YAAY,EAAE,IAAI,CAAC9G,WAAW;QAC9B+G,SAAS,EAAE,IAAI,CAAC/H,SAAS;QACzBgI,SAAS,EAAE;;KAEd,CAAC,CAACC,IAAI,CACL3J,GAAG,CAAC4J,GAAG,IAAG;MACR,IAAIA,GAAG,CAACC,OAAO,IAAID,GAAG,CAACE,UAAU,IAAI,CAAC,EAAE;QACtC,IAAI,CAACC,YAAY,GAAGH,GAAG,CAACC,OAAO;QAC/B,IAAI,CAAC/H,KAAK,GAAG8H,GAAG,CAACC,OAAO,CAACG,SAAS,GAAG,KAAK,GAAG,IAAI;QACjD,IAAIJ,GAAG,CAACC,OAAO,CAACG,SAAS,EAAE;UACzB,IAAI,CAACjI,aAAa,CAACyF,OAAO,CAAC/D,IAAI,IAAI,IAAI,CAAC9B,aAAa,CAAC8B,IAAI,CAAC,GAAG,KAAK,CAAC;UACpE,IAAI,CAAChC,kBAAkB,CAAC+F,OAAO,CAAC/D,IAAI,IAAI,IAAI,CAAC7B,kBAAkB,CAAC6B,IAAI,CAAC,GAAG,KAAK,CAAC;UAE9E,IAAI,CAACwE,kBAAkB,GAAG2B,GAAG,CAACC,OAAO,CAACG,SAAS,CAAC/C,GAAG,CAAEgD,CAAM,IAAI;YAC7D,OAAO;cACLC,OAAO,EAAE,IAAI,CAACH,YAAY,CAACG,OAAO;cAClCC,cAAc,EAAEF,CAAC,CAACE,cAAc;cAChCpE,WAAW,EAAEkE,CAAC,CAAClE,WAAW,KAAKkE,CAAC,CAACG,gBAAgB,GAAG,CAACH,CAAC,CAACG,gBAAgB,CAAC,GAAG,EAAE,CAAC;cAC9ErF,KAAK,EAAEkF,CAAC,CAAClF,KAAK;cACdsF,kBAAkB,EAAEJ,CAAC,CAACI,kBAAkB;cACxCC,WAAW,EAAEL,CAAC,CAACK,WAAW;cAC1B1B,SAAS,EAAEqB,CAAC,CAACrB,SAAS;cACtBC,KAAK,EAAEoB,CAAC,CAACpB,KAAK;cACdC,KAAK,EAAEmB,CAAC,CAACnB,KAAK;cACdyB,SAAS,EAAEN,CAAC,CAACM,SAAS,GAAGN,CAAC,CAACM,SAAS,GAAG,GAAGN,CAAC,CAACpB,KAAK,IAAIoB,CAAC,CAACnB,KAAK,IAAImB,CAAC,CAACrB,SAAS,EAAE;cAC7EP,WAAW,EAAE4B,CAAC,CAAC5B,WAAW;cAC1BgB,YAAY,EAAEY,CAAC,CAACZ,YAAY;cAC5BmB,cAAc,EAAEP,CAAC,CAACQ,OAAO,KAAK,CAAC,GAAG,CAAC,GAAGR,CAAC,CAACO,cAAc;cACtDC,OAAO,EAAER,CAAC,CAACQ,OAAO;cAClB9I,aAAa,EAAEsI,CAAC,CAACS,qBAAqB,CAACnG,MAAM,GAAG,IAAI,CAACoG,0BAA0B,CAAC,IAAI,CAAC5I,aAAa,EAAEkI,CAAC,CAACS,qBAAqB,CAAC,GAAG;gBAAE,GAAG,IAAI,CAAC/I;cAAa,CAAE;cAAEC,kBAAkB,EAAEqI,CAAC,CAAC5B,WAAW,GAAG,IAAI,CAACD,kBAAkB,CAAC,IAAI,CAAC3G,kBAAkB,EAAEwI,CAAC,CAAC5B,WAAW,CAAC,GAAG;gBAAE,GAAG,IAAI,CAACzG;cAAkB,CAAE;cAC9R8F,WAAW,EAAEuC,CAAC,CAACS,qBAAqB,CAACnG,MAAM,KAAK,IAAI,CAACxC,aAAa,CAACwC,MAAM;cACzED,YAAY,EAAE,EAAE;cAAEsG,eAAe,EAAEX,CAAC,CAACQ,OAAO,GAAG,IAAI,CAAClH,cAAc,CAAC0G,CAAC,CAACQ,OAAO,EAAE,IAAI,CAACjJ,cAAc,CAAC,GAAG,IAAI,CAACA,cAAc,CAAC,CAAC,CAAC;cAC3HwE,iBAAiB,EAAE,CAAC;cACpBM,WAAW,EAAE,KAAK;cAClByB,wBAAwB,EAAE,EAAE,CAAC;aAC9B;UACH,CAAC,CAAC;QACJ,CAAC,MAAM;UACL;QAAA;QAGF;QACA,IAAI,CAACC,gCAAgC,EAAE;MACzC;IACF,CAAC,CAAC,CACH,CAAC3F,SAAS,EAAE;EACf;EAEAwI,mBAAmBA,CAAC/E,WAAgB;IAClC,IAAIA,WAAW,CAAC8E,eAAe,IAAI9E,WAAW,CAAC8E,eAAe,CAACvJ,KAAK,KAAK,CAAC,EAAE;MAC1EyE,WAAW,CAAC0E,cAAc,GAAG,CAAC;IAChC;EACF;EAIAM,oBAAoBA,CAACC,GAA4B;IAC/C,OAAOzD,MAAM,CAACC,IAAI,CAACwD,GAAG,CAAC,CAAC5F,MAAM,CAACwB,GAAG,IAAIoE,GAAG,CAACpE,GAAG,CAAC,CAAC;EACjD;EAEAqE,0BAA0BA,CAACD,GAA4B;IACrD,OAAOzD,MAAM,CAACC,IAAI,CAACwD,GAAG,CAAC,CACpB5F,MAAM,CAACwB,GAAG,IAAIoE,GAAG,CAACpE,GAAG,CAAC,CAAC,CACvBsE,IAAI,CAAC,GAAG,CAAC;EACd;EAEAC,cAAcA,CAACN,eAAoB,EAAEhJ,kBAAuB;IAC1D,IAAIgJ,eAAe,IAAIA,eAAe,CAACvJ,KAAK,IAAI,CAAC,EAAE;MACjD,OAAO,IAAI,CAAC2J,0BAA0B,CAACpJ,kBAAkB,CAAC;IAC5D;EACF;EAEAuJ,mBAAmBA,CAACC,WAAmB;IACrC,MAAMC,KAAK,GAAGD,WAAW,CAACzG,KAAK,CAAC,GAAG,CAAC;IACpC,IAAI0G,KAAK,CAAC9G,MAAM,GAAG,CAAC,EAAE;MACpB,OAAO8G,KAAK,CAAC,CAAC,CAAC;IACjB,CAAC,MAAM,OAAO,EAAE;EAClB;EAEAC,UAAUA,CAAChH,YAAiB;IAC1B,IAAIA,YAAY,IAAIA,YAAY,CAACC,MAAM,GAAG,CAAC,EAAE;MAC3C,OAAO;QACLgH,YAAY,EAAE,IAAI,CAACJ,mBAAmB,CAAC7G,YAAY,CAAC,CAAC,CAAC,CAACM,IAAI,CAAC,IAAI,IAAI;QACpE4G,aAAa,EAAElH,YAAY,CAAC,CAAC,CAAC,CAACO,SAAS,IAAI,IAAI;QAChD4G,QAAQ,EAAEnH,YAAY,CAAC,CAAC,CAAC,CAACS,KAAK,CAACL,IAAI,IAAIJ,YAAY,CAAC,CAAC,CAAC,CAACI,IAAI,IAAI;OACjE;IACH,CAAC,MAAM,OAAOyB,SAAS;EAEzB;EAGAuF,UAAUA,CAAA;IACR,IAAI,CAAC5K,KAAK,CAAC6K,KAAK,EAAE;IAClB,IAAIC,iBAAiB,GAAG,KAAK;IAC7B,IAAIC,wBAAwB,GAAG,KAAK;IACpC,IAAIC,kBAAkB,GAAG,KAAK;IAE9B,KAAK,MAAMrI,IAAI,IAAI,IAAI,CAACsI,mBAAmB,EAAE;MAC3C,IAAI,CAACH,iBAAiB,IAAK,CAACnI,IAAI,CAACgH,OAAQ,EAAE;QACzCmB,iBAAiB,GAAG,IAAI;MAC1B;MACA,IAAI,CAACC,wBAAwB,IAAK,CAACpI,IAAI,CAAC+G,cAAe,EAAE;QACvDqB,wBAAwB,GAAG,IAAI;MACjC;MACA,IAAIpI,IAAI,CAAC4F,YAAY,IAAI5F,IAAI,CAAC+G,cAAc,EAAE;QAC5C,IAAI/G,IAAI,CAAC+G,cAAc,GAAG/G,IAAI,CAAC4F,YAAY,IAAI5F,IAAI,CAAC+G,cAAc,GAAG,CAAC,EAAE;UACtE,IAAI,CAAC1J,KAAK,CAACkL,eAAe,CAAC,QAAQ,GAAG,MAAM,GAAGvI,IAAI,CAAC4F,YAAY,GAAG,KAAK5F,IAAI,CAAC8G,SAAS,IAAI,CAAC;QAC7F;MACF;MAEA,IAAI,CAACuB,kBAAkB,IAAK,CAACrI,IAAI,CAAC8G,SAAU,EAAE;QAC5CuB,kBAAkB,GAAG,IAAI;MAC3B;IACF;IACA,IAAIF,iBAAiB,EAAE;MACrB,IAAI,CAAC9K,KAAK,CAACkL,eAAe,CAAC,UAAU,GAAG,KAAK,CAAC;IAChD;IACA,IAAIH,wBAAwB,EAAE;MAC5B,IAAI,CAAC/K,KAAK,CAACkL,eAAe,CAAC,QAAQ,GAAG,QAAQ,CAAC;IACjD;IACA,IAAIF,kBAAkB,EAAE;MACtB,IAAI,CAAChL,KAAK,CAACkL,eAAe,CAAC,YAAY,GAAG,KAAK,CAAC;IAClD;EACF;EAIAC,QAAQA,CAAA;IACN,IAAI,CAACF,mBAAmB,GAAG,IAAI,CAAC9D,kBAAkB,CAAChB,GAAG,CAAEiF,CAAM,IAAI;MAChE,OAAO;QACL/B,cAAc,EAAE+B,CAAC,CAAC/B,cAAc,GAAG+B,CAAC,CAAC/B,cAAc,GAAG,IAAI;QAC1DpF,KAAK,EAAEmH,CAAC,CAAC5H,YAAY,GAAG,IAAI,CAACgH,UAAU,CAACY,CAAC,CAAC5H,YAAY,CAAC,GAAG6B,SAAS;QACnEkE,kBAAkB,EAAE,IAAI,CAACS,oBAAoB,CAACoB,CAAC,CAACvK,aAAa,CAAC;QAC9D2I,WAAW,EAAE4B,CAAC,CAAC5B,WAAW,GAAG4B,CAAC,CAAC5B,WAAW,GAAG,IAAI;QACjD6B,OAAO,EAAE,IAAI,CAACrK,KAAK,GAAG,IAAI,GAAG,IAAI,CAACiI,YAAY,CAACG,OAAO;QACtDrB,KAAK,EAAEqD,CAAC,CAACrD,KAAK;QACdC,KAAK,EAAEoD,CAAC,CAACpD,KAAK;QACdF,SAAS,EAAEsD,CAAC,CAACtD,SAAS;QACtB2B,SAAS,EAAE2B,CAAC,CAAC3B,SAAS;QAAE;QACxBlC,WAAW,EAAE6D,CAAC,CAACtB,eAAe,CAACvJ,KAAK,KAAK,CAAC,GAAG,IAAI,GAAG,IAAI,CAAC6J,cAAc,CAACgB,CAAC,CAACtB,eAAe,EAAEsB,CAAC,CAACtK,kBAAkB,CAAC,IAAI,IAAI;QACxHyH,YAAY,EAAE6C,CAAC,CAAC7C,YAAY;QAC5BmB,cAAc,EAAE0B,CAAC,CAAC1B,cAAc;QAChCC,OAAO,EAAEyB,CAAC,CAACtB,eAAe,CAACvJ;OAC5B;IACH,CAAC,CAAC;IACF,IAAI,CAACqK,UAAU,EAAE;IACjB,IAAI,IAAI,CAAC5K,KAAK,CAACsL,aAAa,CAAC7H,MAAM,GAAG,CAAC,EAAE;MACvC,IAAI,CAAC7D,OAAO,CAAC2L,aAAa,CAAC,IAAI,CAACvL,KAAK,CAACsL,aAAa,CAAC;MACpD;IACF;IACA,IAAI,IAAI,CAACtK,KAAK,EAAE;MACd,IAAI,CAACwK,kBAAkB,EAAE;IAE3B,CAAC,MAAM;MACL,IAAI,CAACC,gBAAgB,EAAE;IACzB;EACF;EAEAA,gBAAgBA,CAAA;IACd,IAAI,CAAC5L,gBAAgB,CAAC6L,oCAAoC,CAAC;MACzDpJ,IAAI,EAAE,IAAI,CAAC2I;KACZ,CAAC,CAAC1J,SAAS,CAACuH,GAAG,IAAG;MACjB,IAAIA,GAAG,CAACE,UAAU,IAAI,CAAC,EAAE;QACvB,IAAI,CAACpJ,OAAO,CAAC+L,aAAa,CAAC,MAAM,CAAC;QAClC;QACA,IAAI,CAAC3J,MAAM,EAAE;MACf;IACF,CAAC,CAAC;EACJ;EAIAwJ,kBAAkBA,CAAA;IAChB,IAAI,CAACI,iBAAiB,GAAG;MACvBlD,YAAY,EAAE,IAAI,CAAC9G,WAAW;MAC9BiK,SAAS,EAAE,IAAI,CAACZ,mBAAmB,IAAI,IAAI;MAC3CtC,SAAS,EAAE,IAAI,CAAC/H,SAAS,CAAE;KAC5B;IAED,IAAI,CAACf,gBAAgB,CAACiM,sCAAsC,CAAC;MAC3DxJ,IAAI,EAAE,IAAI,CAACsJ;KACZ,CAAC,CAACrK,SAAS,CAACuH,GAAG,IAAG;MACjB,IAAIA,GAAG,CAACE,UAAU,IAAI,CAAC,EAAE;QACvB,IAAI,CAACpJ,OAAO,CAAC+L,aAAa,CAAC,MAAM,CAAC;QAClC;QACA,IAAI,CAAC3J,MAAM,EAAE;MACf;IACF,CAAC,CAAC;EACJ;EAEA6H,0BAA0BA,CAACkC,CAAW,EAAEC,CAAkG;IACxI,MAAMC,CAAC,GAA+B,EAAE;IACxC,KAAK,MAAMtJ,IAAI,IAAIoJ,CAAC,EAAE;MACpB,MAAMG,YAAY,GAAGF,CAAC,CAAC5K,IAAI,CAAC+K,KAAK,IAAIA,KAAK,CAACC,UAAU,KAAKzJ,IAAI,IAAIwJ,KAAK,CAACE,SAAS,CAAC;MAClFJ,CAAC,CAACtJ,IAAI,CAAC,GAAG,CAAC,CAACuJ,YAAY;IAC1B;IACA,OAAOD,CAAC;EACV,CAAC,CAAC;EAEF;;;EAGAK,aAAaA,CAAA;IACX;IACA,IAAI,CAACpM,gBAAgB,CAACqM,mCAAmC,CAAC;MACxDjK,IAAI,EAAE;QACJoG,YAAY,EAAE,IAAI,CAAC9G,WAAW;QAC9B4K,KAAK,EAAE;;KAEV,CAAC,CAAC3D,IAAI,CACL3J,GAAG,CAAC4J,GAAG,IAAG;MACR,IAAIA,GAAG,CAACC,OAAO,IAAID,GAAG,CAACE,UAAU,IAAI,CAAC,EAAE;QACtC;QACA,MAAMyD,iBAAiB,GAAG,IAAIC,GAAG,EAAU;QAC3C5D,GAAG,CAACC,OAAO,CAACrC,OAAO,CAAEiG,QAAa,IAAI;UACpC,MAAM9G,GAAG,GAAG,GAAG8G,QAAQ,CAAC7E,SAAS,IAAI6E,QAAQ,CAAC5E,KAAK,IAAI4E,QAAQ,CAAC3E,KAAK,EAAE;UACvEyE,iBAAiB,CAACG,GAAG,CAAC/G,GAAG,CAAC;QAC5B,CAAC,CAAC;QAEF;QACA,MAAMgH,cAAc,GAAG,IAAI,CAAC1F,kBAAkB,CAAC9C,MAAM,CAAE1B,IAAS,IAAI;UAClE,MAAMmK,OAAO,GAAG,GAAGnK,IAAI,CAACmF,SAAS,IAAInF,IAAI,CAACoF,KAAK,IAAIpF,IAAI,CAACqF,KAAK,EAAE;UAC/D,OAAOyE,iBAAiB,CAACxE,GAAG,CAAC6E,OAAO,CAAC;QACvC,CAAC,CAAC;QAEF,IAAID,cAAc,CAACpJ,MAAM,KAAK,CAAC,EAAE;UAC/B,IAAI,CAAC7D,OAAO,CAACmC,YAAY,CAAC,eAAe,CAAC;UAC1C;QACF;QAEA;QACA,IAAI,CAACkJ,mBAAmB,GAAG4B,cAAc,CAAC1G,GAAG,CAAEiF,CAAM,IAAI;UACvD,OAAO;YACL/B,cAAc,EAAE+B,CAAC,CAAC/B,cAAc,GAAG+B,CAAC,CAAC/B,cAAc,GAAG,IAAI;YAC1DpF,KAAK,EAAEmH,CAAC,CAAC5H,YAAY,GAAG,IAAI,CAACgH,UAAU,CAACY,CAAC,CAAC5H,YAAY,CAAC,GAAG6B,SAAS;YACnEkE,kBAAkB,EAAE,IAAI,CAACS,oBAAoB,CAACoB,CAAC,CAACvK,aAAa,CAAC;YAC9D2I,WAAW,EAAE,IAAI;YAAE;YACnB6B,OAAO,EAAE,IAAI;YAAE;YACftD,KAAK,EAAEqD,CAAC,CAACrD,KAAK;YACdC,KAAK,EAAEoD,CAAC,CAACpD,KAAK;YACdF,SAAS,EAAEsD,CAAC,CAACtD,SAAS;YACtB2B,SAAS,EAAE2B,CAAC,CAAC3B,SAAS;YACtBlC,WAAW,EAAE6D,CAAC,CAACtB,eAAe,CAACvJ,KAAK,KAAK,CAAC,GAAG,IAAI,GAAG,IAAI,CAAC6J,cAAc,CAACgB,CAAC,CAACtB,eAAe,EAAEsB,CAAC,CAACtK,kBAAkB,CAAC,IAAI,IAAI;YACxHyH,YAAY,EAAE6C,CAAC,CAAC7C,YAAY;YAC5BmB,cAAc,EAAE0B,CAAC,CAAC1B,cAAc;YAChCC,OAAO,EAAEyB,CAAC,CAACtB,eAAe,CAACvJ;WAC5B;QACH,CAAC,CAAC;QAEF;QACA,IAAI,CAACqK,UAAU,EAAE;QACjB,IAAI,IAAI,CAAC5K,KAAK,CAACsL,aAAa,CAAC7H,MAAM,GAAG,CAAC,EAAE;UACvC,IAAI,CAAC7D,OAAO,CAAC2L,aAAa,CAAC,IAAI,CAACvL,KAAK,CAACsL,aAAa,CAAC;UACpD;QACF;QAEA;QACA,IAAI,CAACM,iBAAiB,GAAG;UACvBlD,YAAY,EAAE,IAAI,CAAC9G,WAAW;UAC9BiK,SAAS,EAAE,IAAI,CAACZ,mBAAmB,IAAI,IAAI;UAC3CtC,SAAS,EAAE,IAAI,CAAC/H,SAAS,CAAE;SAC5B;QAED,IAAI,CAACf,gBAAgB,CAACiM,sCAAsC,CAAC;UAC3DxJ,IAAI,EAAE,IAAI,CAACsJ;SACZ,CAAC,CAACrK,SAAS,CAACwL,SAAS,IAAG;UACvB,IAAIA,SAAS,CAAC/D,UAAU,IAAI,CAAC,EAAE;YAC7B,IAAI,CAACpJ,OAAO,CAAC+L,aAAa,CAAC,cAAckB,cAAc,CAACpJ,MAAM,QAAQ,CAAC;YACvE;YACA,IAAI,CAAC+E,eAAe,EAAE;UACxB;QACF,CAAC,CAAC;MACJ,CAAC,MAAM;QACL,IAAI,CAAC5I,OAAO,CAACmC,YAAY,CAAC,eAAe,CAAC;MAC5C;IACF,CAAC,CAAC,CACH,CAACR,SAAS,EAAE;EACf;EAEA;EACQyL,uBAAuBA,CAAA;IAC7B,IAAI,CAAC,IAAI,CAACpL,WAAW,EAAE;IAEvB,IAAI,CAACxB,mBAAmB,CAAC6M,WAAW,CAAC,IAAI,CAACrL,WAAW,CAAC,CAACL,SAAS,CAAC;MAC/D2L,IAAI,EAAGC,QAAQ,IAAI;QACjBtL,OAAO,CAACmF,GAAG,CAAC,2BAA2B,EAAEmG,QAAQ,CAAC;QAClD,IAAIA,QAAQ,CAACpE,OAAO,EAAE;UACpB,IAAI,CAAChI,YAAY,GAAG,IAAI,CAACqM,gCAAgC,CAACD,QAAQ,CAACpE,OAAO,CAAC;UAC3ElH,OAAO,CAACmF,GAAG,CAAC,yBAAyB,EAAE,IAAI,CAACjG,YAAY,CAAC;QAC3D;MACF,CAAC;MACDe,KAAK,EAAGA,KAAK,IAAI;QACfD,OAAO,CAACC,KAAK,CAAC,uCAAuC,EAAEA,KAAK,CAAC;QAC7D;QACA,IAAI,IAAI,CAACb,aAAa,IAAI,IAAI,CAACA,aAAa,CAACwC,MAAM,GAAG,CAAC,EAAE;UACvD,IAAI,CAAC1C,YAAY,GAAG,IAAI,CAACsM,kCAAkC,CAAC,IAAI,CAACpM,aAAa,CAAC;QACjF;MACF;KACD,CAAC;EACJ;EAEA;EACQmM,gCAAgCA,CAACE,OAAY;IACnD,MAAMvM,YAAY,GAAQ,EAAE;IAE5ByF,MAAM,CAAC8G,OAAO,CAACA,OAAO,CAAC,CAAC5G,OAAO,CAAC,CAAC,CAAC6G,QAAQ,EAAEC,MAAM,CAAgB,KAAI;MACpE;MACA,MAAMC,cAAc,GAAGD,MAAM,CAACnJ,MAAM,CAAEqJ,KAAU,IAAI;QAClD,OAAO,IAAI,CAACC,kBAAkB,CAACD,KAAK,CAAC;MACvC,CAAC,CAAC;MAEF;MACA,IAAID,cAAc,CAAChK,MAAM,GAAG,CAAC,EAAE;QAC7B1C,YAAY,CAACwM,QAAQ,CAAC,GAAGE,cAAc,CAACtH,GAAG,CAAEuH,KAAU,KAAM;UAC3DrH,IAAI,EAAEqH,KAAK,CAACE,SAAS;UACrBL,QAAQ,EAAEG,KAAK,CAACG,QAAQ;UACxBC,KAAK,EAAEJ,KAAK,CAACK,KAAK;UAClBC,OAAO,EAAEN,KAAK,CAACO,OAAO;UACtBC,SAAS,EAAER,KAAK,CAACE,SAAS;UAC1BO,UAAU,EAAE,KAAK;UACjBC,UAAU,EAAE;SACb,CAAC,CAAC;MACL;IACF,CAAC,CAAC;IAEF,OAAOrN,YAAY;EACrB;EAEA;EACQ4M,kBAAkBA,CAACD,KAAU;IACnC;IACA,IAAI,CAAC,IAAI,CAAC9M,SAAS,IAAI,IAAI,CAACA,SAAS,KAAK,CAAC,EAAE;MAC3C,OAAO,IAAI;IACb;IAEA;IACA;IAEA;IACA,IAAI8M,KAAK,CAACW,SAAS,KAAKhJ,SAAS,EAAE;MACjC,OAAOqI,KAAK,CAACW,SAAS,KAAK,IAAI,CAACzN,SAAS;IAC3C;IAEA;IACA;IACA,MAAMsN,SAAS,GAAGR,KAAK,CAACE,SAAS,IAAIF,KAAK,CAACQ,SAAS,IAAI,EAAE;IAE1D,IAAI,IAAI,CAACtN,SAAS,KAAKrB,aAAa,CAACiB,GAAG,EAAE;MACxC;MACA;MACA,OAAO0N,SAAS,CAACxG,QAAQ,CAAC,IAAI,CAAC,IAAIwG,SAAS,CAACI,UAAU,CAAC,GAAG,CAAC;IAC9D,CAAC,MAAM,IAAI,IAAI,CAAC1N,SAAS,KAAKrB,aAAa,CAACkB,GAAG,EAAE;MAC/C;MACA;MACA,OAAOyN,SAAS,CAACxG,QAAQ,CAAC,IAAI,CAAC,IAAI,CAACwG,SAAS,CAACxG,QAAQ,CAAC,IAAI,CAAC;IAC9D;IAEA;IACA,OAAO,IAAI;EACb;EAEA;EACA2F,kCAAkCA,CAACpM,aAAuB;IACxD,IAAI,CAACA,aAAa,IAAIA,aAAa,CAACwC,MAAM,KAAK,CAAC,EAAE;MAChD,OAAO,EAAE;IACX;IAEA;IACA,MAAM1C,YAAY,GAAQ,EAAE;IAE5BE,aAAa,CAACyF,OAAO,CAACC,SAAS,IAAG;MAChC;MACA,MAAM4H,aAAa,GAAG5H,SAAS,CAAC6H,KAAK,CAAC,WAAW,CAAC;MAClD,MAAMjB,QAAQ,GAAGgB,aAAa,GAAG,GAAGA,aAAa,CAAC,CAAC,CAAC,GAAG,GAAG,MAAM;MAEhE,IAAI,CAACxN,YAAY,CAACwM,QAAQ,CAAC,EAAE;QAC3BxM,YAAY,CAACwM,QAAQ,CAAC,GAAG,EAAE;MAC7B;MAEA;MACA,MAAMkB,WAAW,GAAGC,QAAQ,CAAC/H,SAAS,CAACgI,OAAO,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAC;MAC7D,MAAMb,KAAK,GAAGc,IAAI,CAACC,IAAI,CAACJ,WAAW,GAAG,CAAC,CAAC;MAExC1N,YAAY,CAACwM,QAAQ,CAAC,CAACrJ,IAAI,CAAC;QAC1BmC,IAAI,EAAEM,SAAS;QACf4G,QAAQ,EAAEA,QAAQ;QAClBO,KAAK,EAAE,GAAGA,KAAK,GAAG;QAClBK,UAAU,EAAE,KAAK;QACjBC,UAAU,EAAE;OACb,CAAC;IACJ,CAAC,CAAC;IAAE,OAAOrN,YAAY;EACzB;EAKA;EACQoB,cAAcA,CAAA;IACpB;IACA,IAAI,CAAClB,aAAa,GAAG,EAAE;IAEvB;IACA,IAAI,CAAC+L,uBAAuB,EAAE;IAE9B;IACA,IAAI,CAACxE,eAAe,EAAE;EACxB;EAEAxG,MAAMA,CAAA;IACJ,IAAI,CAAC7B,aAAa,CAAC+D,IAAI,CAAC;MACtB4K,MAAM;MACNC,OAAO,EAAE,IAAI,CAACnN;KACf,CAAC;IACF,IAAI,CAAC3B,QAAQ,CAAC+O,IAAI,EAAE;EACtB;CAED;AA3sBYxP,4CAA4C,GAAAyP,UAAA,EATxDnQ,SAAS,CAAC;EACToQ,QAAQ,EAAE,6CAA6C;EACvDC,WAAW,EAAE,0DAA0D;EACvEC,SAAS,EAAE,CAAC,0DAA0D,CAAC;EACvEC,UAAU,EAAE,IAAI;EAChBC,OAAO,EAAE,CAACtQ,YAAY,EAAEG,YAAY,EAAEC,eAAe,EAAEH,gBAAgB,EAAEK,eAAe,CAAC;EACzFiQ,eAAe,EAAExQ,uBAAuB,CAACyQ;CAC1C,CAAC,C,EAEWhQ,4CAA4C,CA2sBxD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}