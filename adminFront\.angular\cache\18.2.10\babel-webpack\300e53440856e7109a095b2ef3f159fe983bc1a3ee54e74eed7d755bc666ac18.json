{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { tap } from 'rxjs';\nimport { SharedModule } from '../../components/shared.module';\nimport { BaseComponent } from '../../components/base/baseComponent';\nimport { environment } from 'src/environments/environment';\nimport { DateFormatHourPipe } from 'src/app/@theme/pipes';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/shared/helper/allowHelper\";\nimport * as i2 from \"@nebular/theme\";\nimport * as i3 from \"src/app/shared/services/message.service\";\nimport * as i4 from \"src/app/shared/helper/validationHelper\";\nimport * as i5 from \"src/app/shared/services/utility.service\";\nimport * as i6 from \"src/services/api/services\";\nimport * as i7 from \"src/app/@core/service/notice.service\";\nimport * as i8 from \"@angular/common\";\nimport * as i9 from \"@angular/forms\";\nimport * as i10 from \"@ng-bootstrap/ng-bootstrap\";\nimport * as i11 from \"../../components/breadcrumb/breadcrumb.component\";\nimport * as i12 from \"../../../@theme/directives/label.directive\";\nconst _c0 = () => [0, 1, 2];\nconst _c1 = a0 => ({\n  \"!text-red\": a0\n});\nfunction NoticeManagementComponent_nb_option_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 13);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const case_r2 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", case_r2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", case_r2.label, \" \");\n  }\n}\nfunction NoticeManagementComponent_button_15_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 14);\n    i0.ɵɵlistener(\"click\", function NoticeManagementComponent_button_15_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r3 = i0.ɵɵnextContext();\n      const dialog_r5 = i0.ɵɵreference(19);\n      return i0.ɵɵresetView(ctx_r3.openModel(dialog_r5));\n    });\n    i0.ɵɵtext(1, \" \\u65B0\\u589E\\u6A94\\u6848 \");\n    i0.ɵɵelement(2, \"i\", 15);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction NoticeManagementComponent_ng_container_16_tr_18_button_12_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 28);\n    i0.ɵɵlistener(\"click\", function NoticeManagementComponent_ng_container_16_tr_18_button_12_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const item_r8 = i0.ɵɵnextContext().$implicit;\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      const dialog_r5 = i0.ɵɵreference(19);\n      return i0.ɵɵresetView(ctx_r3.openModel(dialog_r5, item_r8));\n    });\n    i0.ɵɵtext(1, \" \\u7DE8\\u8F2F \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction NoticeManagementComponent_ng_container_16_tr_18_button_13_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 29);\n    i0.ɵɵlistener(\"click\", function NoticeManagementComponent_ng_container_16_tr_18_button_13_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const item_r8 = i0.ɵɵnextContext().$implicit;\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r3.onDelete(item_r8));\n    });\n    i0.ɵɵtext(1, \" \\u522A\\u9664 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction NoticeManagementComponent_ng_container_16_tr_18_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\", 24)(1, \"td\")(2, \"a\", 25);\n    i0.ɵɵlistener(\"click\", function NoticeManagementComponent_ng_container_16_tr_18_Template_a_click_2_listener() {\n      const item_r8 = i0.ɵɵrestoreView(_r7).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r3.openPdfInNewTab(item_r8 == null ? null : item_r8.CFile));\n    });\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"td\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"td\");\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"td\");\n    i0.ɵɵtext(9);\n    i0.ɵɵpipe(10, \"date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"td\");\n    i0.ɵɵtemplate(12, NoticeManagementComponent_ng_container_16_tr_18_button_12_Template, 2, 0, \"button\", 26)(13, NoticeManagementComponent_ng_container_16_tr_18_button_13_Template, 2, 0, \"button\", 27);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const item_r8 = ctx.$implicit;\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(item_r8 == null ? null : item_r8.CFileName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r8.CHouseHold == null ? null : item_r8.CHouseHold.join(\"\\u3001\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r8.CExamineStatus != null && i0.ɵɵpureFunction0(9, _c0).includes(item_r8.CExamineStatus) ? ctx_r3.cExamineStatusOption[item_r8.CExamineStatus] : \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(10, 6, item_r8 == null ? null : item_r8.CApproveDate, \"yyyy/MM/dd HH:mm:ss\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.isUpdate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.isDelete);\n  }\n}\nfunction NoticeManagementComponent_ng_container_16_tr_38_button_12_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r13 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 28);\n    i0.ɵɵlistener(\"click\", function NoticeManagementComponent_ng_container_16_tr_38_button_12_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r13);\n      const item_r12 = i0.ɵɵnextContext().$implicit;\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      const dialog_r5 = i0.ɵɵreference(19);\n      return i0.ɵɵresetView(ctx_r3.openModel(dialog_r5, item_r12));\n    });\n    i0.ɵɵtext(1, \" \\u7DE8\\u8F2F \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction NoticeManagementComponent_ng_container_16_tr_38_button_13_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r14 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 29);\n    i0.ɵɵlistener(\"click\", function NoticeManagementComponent_ng_container_16_tr_38_button_13_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r14);\n      const item_r12 = i0.ɵɵnextContext().$implicit;\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r3.onDelete(item_r12));\n    });\n    i0.ɵɵtext(1, \" \\u522A\\u9664 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction NoticeManagementComponent_ng_container_16_tr_38_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\", 24)(1, \"td\")(2, \"a\", 25);\n    i0.ɵɵlistener(\"click\", function NoticeManagementComponent_ng_container_16_tr_38_Template_a_click_2_listener() {\n      const item_r12 = i0.ɵɵrestoreView(_r11).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r3.openPdfInNewTab(item_r12 == null ? null : item_r12.CFile));\n    });\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"td\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"td\");\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"td\");\n    i0.ɵɵtext(9);\n    i0.ɵɵpipe(10, \"date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"td\");\n    i0.ɵɵtemplate(12, NoticeManagementComponent_ng_container_16_tr_38_button_12_Template, 2, 0, \"button\", 26)(13, NoticeManagementComponent_ng_container_16_tr_38_button_13_Template, 2, 0, \"button\", 27);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const item_r12 = ctx.$implicit;\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(item_r12 == null ? null : item_r12.CFileName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r12.CHouseHold == null ? null : item_r12.CHouseHold.join(\"\\u3001\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", item_r12.CExamineStatus != null && i0.ɵɵpureFunction0(9, _c0).includes(item_r12.CExamineStatus) ? ctx_r3.cExamineStatusOption[item_r12.CExamineStatus] : \"\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(10, 6, item_r12 == null ? null : item_r12.CApproveDate, \"yyyy-MM-dd HH:mm:ss\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.isUpdate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.isDelete);\n  }\n}\nfunction NoticeManagementComponent_ng_container_16_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 16)(2, \"h4\", 17);\n    i0.ɵɵtext(3, \"\\u5730\\u4E3B\\u6236 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"table\", 18)(5, \"thead\")(6, \"tr\", 19)(7, \"th\", 20);\n    i0.ɵɵtext(8, \"\\u6A94\\u6848\\u540D\\u7A31\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"th\", 20);\n    i0.ɵɵtext(10, \"\\u9069\\u7528\\u6236\\u5225 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"th\", 20);\n    i0.ɵɵtext(12, \"\\u5BE9\\u6838\\u72C0\\u614B\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"th\", 20);\n    i0.ɵɵtext(14, \"\\u5BE9\\u6838\\u65E5\\u671F\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"th\", 20);\n    i0.ɵɵtext(16, \"\\u64CD\\u4F5C\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(17, \"tbody\");\n    i0.ɵɵtemplate(18, NoticeManagementComponent_ng_container_16_tr_18_Template, 14, 10, \"tr\", 21);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(19, \"div\", 22)(20, \"ngb-pagination\", 23);\n    i0.ɵɵtwoWayListener(\"pageChange\", function NoticeManagementComponent_ng_container_16_Template_ngb_pagination_pageChange_20_listener($event) {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r3 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r3.pageIndex, $event) || (ctx_r3.pageIndex = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"pageChange\", function NoticeManagementComponent_ng_container_16_Template_ngb_pagination_pageChange_20_listener($event) {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.pageChanged($event));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(21, \"div\", 16)(22, \"h4\", 17);\n    i0.ɵɵtext(23, \"\\u92B7\\u552E\\u6236\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"table\", 18)(25, \"thead\")(26, \"tr\", 19)(27, \"th\", 20);\n    i0.ɵɵtext(28, \"\\u6A94\\u6848\\u540D\\u7A31\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(29, \"th\", 20);\n    i0.ɵɵtext(30, \"\\u9069\\u7528\\u6236\\u5225 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(31, \"th\", 20);\n    i0.ɵɵtext(32, \"\\u5BE9\\u6838\\u72C0\\u614B\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(33, \"th\", 20);\n    i0.ɵɵtext(34, \"\\u5BE9\\u6838\\u65E5\\u671F\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(35, \"th\", 20);\n    i0.ɵɵtext(36, \"\\u64CD\\u4F5C\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(37, \"tbody\");\n    i0.ɵɵtemplate(38, NoticeManagementComponent_ng_container_16_tr_38_Template, 14, 10, \"tr\", 21);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(39, \"div\", 22)(40, \"ngb-pagination\", 23);\n    i0.ɵɵtwoWayListener(\"pageChange\", function NoticeManagementComponent_ng_container_16_Template_ngb_pagination_pageChange_40_listener($event) {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r3 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r3.pageIndexSales, $event) || (ctx_r3.pageIndexSales = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"pageChange\", function NoticeManagementComponent_ng_container_16_Template_ngb_pagination_pageChange_40_listener($event) {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.pageChangedSales($event));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(18);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.listSpecialNoticeFile == null ? null : ctx_r3.listSpecialNoticeFile.ListLandLords);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtwoWayProperty(\"page\", ctx_r3.pageIndex);\n    i0.ɵɵproperty(\"pageSize\", ctx_r3.pageSize)(\"collectionSize\", ctx_r3.totalRecords);\n    i0.ɵɵadvance(18);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.listSpecialNoticeFile == null ? null : ctx_r3.listSpecialNoticeFile.ListSales);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtwoWayProperty(\"page\", ctx_r3.pageIndexSales);\n    i0.ɵɵproperty(\"pageSize\", ctx_r3.pageSizeSales)(\"collectionSize\", ctx_r3.totalRecordsSales);\n  }\n}\nfunction NoticeManagementComponent_ng_template_18_nb_card_body_3_nb_option_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 13);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const status_r16 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", status_r16);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", status_r16.label, \" \");\n  }\n}\nfunction NoticeManagementComponent_ng_template_18_nb_card_body_3_div_17_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r17 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 56)(1, \"span\", 57);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"button\", 58);\n    i0.ɵɵlistener(\"click\", function NoticeManagementComponent_ng_template_18_nb_card_body_3_div_17_Template_button_click_3_listener() {\n      i0.ɵɵrestoreView(_r17);\n      const ctx_r3 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r3.clearImage());\n    });\n    i0.ɵɵelement(4, \"i\", 59);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r3.fileName);\n  }\n}\nfunction NoticeManagementComponent_ng_template_18_nb_card_body_3_span_18_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r18 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"span\", 60)(1, \"a\", 25);\n    i0.ɵɵlistener(\"click\", function NoticeManagementComponent_ng_template_18_nb_card_body_3_span_18_Template_a_click_1_listener() {\n      i0.ɵɵrestoreView(_r18);\n      const ctx_r3 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r3.openPdfInNewTab(ctx_r3.saveSpecialNoticeFile.CFileUrl));\n    });\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"ngClass\", ctx_r3.imageUrl ? \"hidden\" : \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.saveSpecialNoticeFile.CFileUrl, \" \");\n  }\n}\nfunction NoticeManagementComponent_ng_template_18_nb_card_body_3_div_22_tr_3_th_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r19 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"th\")(1, \"nb-checkbox\", 64);\n    i0.ɵɵlistener(\"checkedChange\", function NoticeManagementComponent_ng_template_18_nb_card_body_3_div_22_tr_3_th_2_Template_nb_checkbox_checkedChange_1_listener($event) {\n      const idx_r20 = i0.ɵɵrestoreView(_r19).index;\n      const ctx_r3 = i0.ɵɵnextContext(5);\n      return i0.ɵɵresetView(ctx_r3.enableAllAtIndex($event, idx_r20));\n    });\n    i0.ɵɵelementStart(2, \"span\", 65);\n    i0.ɵɵtext(3, \"\\u5168\\u9078\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const idx_r20 = ctx.index;\n    const ctx_r3 = i0.ɵɵnextContext(5);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"checked\", ctx_r3.isCheckAllColumnChecked(idx_r20))(\"disabled\", ctx_r3.saveSpecialNoticeFile.selectedCNoticeType.value == 1 ? true : false);\n  }\n}\nfunction NoticeManagementComponent_ng_template_18_nb_card_body_3_div_22_tr_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\");\n    i0.ɵɵelement(1, \"th\");\n    i0.ɵɵtemplate(2, NoticeManagementComponent_ng_template_18_nb_card_body_3_div_22_tr_3_th_2_Template, 4, 2, \"th\", 63);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.houseList2D[0]);\n  }\n}\nfunction NoticeManagementComponent_ng_template_18_nb_card_body_3_div_22_tr_5_td_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r23 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"td\");\n    i0.ɵɵelementContainerStart(1);\n    i0.ɵɵelementStart(2, \"nb-checkbox\", 64);\n    i0.ɵɵtwoWayListener(\"checkedChange\", function NoticeManagementComponent_ng_template_18_nb_card_body_3_div_22_tr_5_td_5_Template_nb_checkbox_checkedChange_2_listener($event) {\n      const house_r24 = i0.ɵɵrestoreView(_r23).$implicit;\n      i0.ɵɵtwoWayBindingSet(house_r24.CIsSelect, $event) || (house_r24.CIsSelect = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementStart(3, \"span\", 66);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const house_r24 = ctx.$implicit;\n    const ctx_r3 = i0.ɵɵnextContext(5);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtwoWayProperty(\"checked\", house_r24.CIsSelect);\n    i0.ɵɵproperty(\"disabled\", !house_r24.CHouseHold || !house_r24.CIsEnable || !(ctx_r3.saveSpecialNoticeFile.selectedCNoticeType.value === house_r24.CHouseType) || ctx_r3.saveSpecialNoticeFile.CExamineStauts == 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(5, _c1, house_r24.CID));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate2(\" \", house_r24.CHouseHold || \"null\", \" - \", house_r24.CFloor, \"\");\n  }\n}\nfunction NoticeManagementComponent_ng_template_18_nb_card_body_3_div_22_tr_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r21 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\")(2, \"nb-checkbox\", 64);\n    i0.ɵɵlistener(\"checkedChange\", function NoticeManagementComponent_ng_template_18_nb_card_body_3_div_22_tr_5_Template_nb_checkbox_checkedChange_2_listener($event) {\n      const row_r22 = i0.ɵɵrestoreView(_r21).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r3.enableAllRow($event, row_r22));\n    });\n    i0.ɵɵelementStart(3, \"span\", 65);\n    i0.ɵɵtext(4, \"\\u5168\\u9078\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(5, NoticeManagementComponent_ng_template_18_nb_card_body_3_div_22_tr_5_td_5_Template, 5, 7, \"td\", 63);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const row_r22 = ctx.$implicit;\n    const ctx_r3 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"checked\", ctx_r3.isCheckAllRowChecked(row_r22))(\"disabled\", ctx_r3.saveSpecialNoticeFile.selectedCNoticeType.value == 1 ? true : false);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", row_r22);\n  }\n}\nfunction NoticeManagementComponent_ng_template_18_nb_card_body_3_div_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 61)(1, \"table\", 62)(2, \"thead\");\n    i0.ɵɵtemplate(3, NoticeManagementComponent_ng_template_18_nb_card_body_3_div_22_tr_3_Template, 3, 1, \"tr\", 11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"tbody\");\n    i0.ɵɵtemplate(5, NoticeManagementComponent_ng_template_18_nb_card_body_3_div_22_tr_5_Template, 6, 3, \"tr\", 63);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.houseList2D.length);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.houseList2D);\n  }\n}\nfunction NoticeManagementComponent_ng_template_18_nb_card_body_3_div_32_tr_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\");\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"dateFormatHour\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"td\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"td\");\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"td\");\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const row_r26 = ctx.$implicit;\n    const ctx_r3 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(3, 4, row_r26.CCreateDt));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(row_r26.CCreator);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r3.getActionName(row_r26.CAction));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(row_r26.CExamineNote);\n  }\n}\nfunction NoticeManagementComponent_ng_template_18_nb_card_body_3_div_32_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 67)(1, \"div\", 49)(2, \"label\", 68);\n    i0.ɵɵtext(3, \"\\u5BE9\\u6838\\u6B77\\u7A0B\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"table\", 62)(5, \"thead\")(6, \"tr\")(7, \"th\");\n    i0.ɵɵtext(8, \"\\u6642\\u9593\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"th\");\n    i0.ɵɵtext(10, \"\\u4F7F\\u7528\\u8005\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"th\");\n    i0.ɵɵtext(12, \"\\u52D5\\u4F5C\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"th\");\n    i0.ɵɵtext(14, \"\\u8AAA\\u660E\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(15, \"tbody\");\n    i0.ɵɵtemplate(16, NoticeManagementComponent_ng_template_18_nb_card_body_3_div_32_tr_16_Template, 10, 6, \"tr\", 63);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(16);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.saveSpecialNoticeFile.tblExamineLogs);\n  }\n}\nfunction NoticeManagementComponent_ng_template_18_nb_card_body_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r15 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-card-body\", 32)(1, \"div\", 33)(2, \"label\", 34);\n    i0.ɵɵtext(3, \"\\u6A94\\u6848\\u985E\\u578B\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"nb-select\", 35);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function NoticeManagementComponent_ng_template_18_nb_card_body_3_Template_nb_select_ngModelChange_4_listener($event) {\n      i0.ɵɵrestoreView(_r15);\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r3.saveSpecialNoticeFile.selectedCNoticeType, $event) || (ctx_r3.saveSpecialNoticeFile.selectedCNoticeType = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"ngModelChange\", function NoticeManagementComponent_ng_template_18_nb_card_body_3_Template_nb_select_ngModelChange_4_listener($event) {\n      i0.ɵɵrestoreView(_r15);\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r3.onStatusChange($event));\n    });\n    i0.ɵɵtemplate(5, NoticeManagementComponent_ng_template_18_nb_card_body_3_nb_option_5_Template, 2, 2, \"nb-option\", 8);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 36)(7, \"div\", 37)(8, \"label\", 38);\n    i0.ɵɵtext(9, \"\\u4E0A\\u50B3\\u6A94\\u6848 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"span\", 39);\n    i0.ɵɵtext(11, \"\\u53EA\\u63A5\\u53D7pdf\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(12, \"div\", 40)(13, \"input\", 41);\n    i0.ɵɵlistener(\"change\", function NoticeManagementComponent_ng_template_18_nb_card_body_3_Template_input_change_13_listener($event) {\n      i0.ɵɵrestoreView(_r15);\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r3.onFileSelected($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"label\", 42);\n    i0.ɵɵelement(15, \"i\", 43);\n    i0.ɵɵtext(16, \" \\u4E0A\\u50B3 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(17, NoticeManagementComponent_ng_template_18_nb_card_body_3_div_17_Template, 5, 1, \"div\", 44);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(18, NoticeManagementComponent_ng_template_18_nb_card_body_3_span_18_Template, 3, 2, \"span\", 45);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"div\", 46)(20, \"label\", 47);\n    i0.ɵɵtext(21, \"\\u9069\\u7528\\u6236\\u5225\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(22, NoticeManagementComponent_ng_template_18_nb_card_body_3_div_22_Template, 6, 2, \"div\", 48);\n    i0.ɵɵelementStart(23, \"div\", 49)(24, \"label\", 50);\n    i0.ɵɵtext(25, \"\\u9001\\u5BE9\\u5099\\u8A3B\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"textarea\", 51);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function NoticeManagementComponent_ng_template_18_nb_card_body_3_Template_textarea_ngModelChange_26_listener($event) {\n      i0.ɵɵrestoreView(_r15);\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r3.saveSpecialNoticeFile.CExamineNote, $event) || (ctx_r3.saveSpecialNoticeFile.CExamineNote = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(27, \"div\", 52)(28, \"button\", 53);\n    i0.ɵɵlistener(\"click\", function NoticeManagementComponent_ng_template_18_nb_card_body_3_Template_button_click_28_listener() {\n      i0.ɵɵrestoreView(_r15);\n      const ref_r25 = i0.ɵɵnextContext().dialogRef;\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.onClose(ref_r25));\n    });\n    i0.ɵɵtext(29, \" \\u53D6\\u6D88 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(30, \"button\", 54);\n    i0.ɵɵlistener(\"click\", function NoticeManagementComponent_ng_template_18_nb_card_body_3_Template_button_click_30_listener() {\n      i0.ɵɵrestoreView(_r15);\n      const ref_r25 = i0.ɵɵnextContext().dialogRef;\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.onSaveSpecialNoticeFile(ref_r25));\n    });\n    i0.ɵɵtext(31, \" \\u5132\\u5B58\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(32, NoticeManagementComponent_ng_template_18_nb_card_body_3_div_32_Template, 17, 1, \"div\", 55);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r3.saveSpecialNoticeFile.selectedCNoticeType);\n    i0.ɵɵproperty(\"disabled\", !ctx_r3.isNew);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.cNoticeTypeOptions);\n    i0.ɵɵadvance(12);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.fileName);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.saveSpecialNoticeFile && (ctx_r3.saveSpecialNoticeFile == null ? null : ctx_r3.saveSpecialNoticeFile.CFileUrl));\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.isHouseList);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r3.saveSpecialNoticeFile.CExamineNote);\n    i0.ɵɵproperty(\"rows\", 4);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r3.isNew);\n  }\n}\nfunction NoticeManagementComponent_ng_template_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-card\", 30)(1, \"nb-card-header\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, NoticeManagementComponent_ng_template_18_nb_card_body_3_Template, 33, 9, \"nb-card-body\", 31);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.isNew ? \"\\u65B0\\u589E\\u6A94\\u6848-\\u5730\\u4E3B\\u6236\" : \"\\u7DE8\\u8F2F\", \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.isHouseList);\n  }\n}\nexport class NoticeManagementComponent extends BaseComponent {\n  constructor(_allow, dialogService, message, valid, _utilityService, _buildCaseService, _specialNoticeFileService, _regularNoticeFileService, _houseService, _specialChangeCustomService) {\n    super(_allow);\n    this._allow = _allow;\n    this.dialogService = dialogService;\n    this.message = message;\n    this.valid = valid;\n    this._utilityService = _utilityService;\n    this._buildCaseService = _buildCaseService;\n    this._specialNoticeFileService = _specialNoticeFileService;\n    this._regularNoticeFileService = _regularNoticeFileService;\n    this._houseService = _houseService;\n    this._specialChangeCustomService = _specialChangeCustomService;\n    this.pageFirst = 1;\n    this.pageSize = 10;\n    this.pageIndex = 1;\n    this.totalRecords = 0;\n    this.pageFirstSales = 1;\n    this.pageSizeSales = 10;\n    this.pageIndexSales = 1;\n    this.totalRecordsSales = 0;\n    this.buildCaseOptions = [{\n      label: '全部',\n      value: ''\n    }];\n    this.cNoticeTypeOptions = [{\n      label: '地主戶',\n      value: 1\n    }, {\n      label: '銷售戶',\n      value: 2\n    }];\n    this.typeContentManagementLandowner = {\n      CFormType: 1,\n      CNoticeType: 1\n    };\n    // 檔案上傳配置\n    this.fileUploadConfig = {\n      acceptedTypes: ['application/pdf'],\n      acceptedFileRegex: /pdf/i,\n      acceptAttribute: 'application/pdf',\n      label: '上傳檔案',\n      helpText: '*請上傳PDF格式',\n      required: false,\n      disabled: false,\n      autoFillName: false,\n      buttonText: '上傳',\n      buttonIcon: 'fa-solid fa-cloud-arrow-up',\n      maxFileSize: 10,\n      multiple: false,\n      showPreview: false\n    };\n    this.selectedFile = null;\n    this.isHouseList = false;\n    this.cExamineStatusOption = ['待審核', '已通過', '已駁回'];\n    this.isNew = true;\n  }\n  ngOnInit() {\n    this.getUserBuildCase();\n  }\n  pageChanged(newPage) {\n    this.pageIndex = newPage;\n  }\n  pageChangedSales(newPage) {\n    this.pageIndexSales = newPage;\n  }\n  // 檔案選擇事件處理\n  onFileUpload(fileResult) {\n    this.selectedFile = fileResult;\n  }\n  // 檔案清除事件處理  \n  onFileClear() {\n    this.selectedFile = null;\n  }\n  onChangeBuildCase() {\n    if (this.selectedCBuildCase.value) {\n      this.getSpecialNoticeFileHouseHoldList();\n      this.getSpecialNoticeFileList();\n    }\n  }\n  getUserBuildCase() {\n    this._buildCaseService.apiBuildCaseGetUserBuildCasePost$Json({\n      body: {}\n    }).pipe(tap(res => {\n      if (res.Entries && res.StatusCode == 0) {\n        this.userBuildCaseOptions = res.Entries.map(res => {\n          return {\n            label: res.CBuildCaseName,\n            value: res.cID\n          };\n        });\n        this.selectedCBuildCase = this.userBuildCaseOptions[0];\n        if (this.selectedCBuildCase.value) {\n          this.getSpecialNoticeFileHouseHoldList();\n          this.getSpecialNoticeFileList();\n        }\n      }\n    })).subscribe();\n  }\n  groupByFloor(customerData) {\n    const groupedData = [];\n    const uniqueFloors = Array.from(new Set(customerData.map(customer => customer.CFloor).filter(floor => floor !== null)));\n    for (const floor of uniqueFloors) {\n      groupedData.push([]);\n    }\n    for (const customer of customerData) {\n      const floorIndex = uniqueFloors.indexOf(customer.CFloor);\n      if (floorIndex !== -1) {\n        groupedData[floorIndex].push({\n          CIsSelect: customer?.CIsSelect || false,\n          CHouseID: customer.CID,\n          CHouseType: customer.CHouseType,\n          CFloor: customer.CFloor,\n          CHouseHold: customer.CHouseHold,\n          CIsEnable: customer.CIsEnable\n        });\n      }\n    }\n    return groupedData;\n  }\n  sortByFloorDescending(arr) {\n    return arr.sort((a, b) => (b.CFloor ?? 0) - (a.CFloor ?? 0));\n  }\n  getHouseList() {\n    if (this.selectedCBuildCase.value) {\n      this._houseService.apiHouseGetHouseListPost$Json({\n        body: {\n          CBuildCaseID: this.selectedCBuildCase.value,\n          CIsPagi: false\n        }\n      }).subscribe(res => {\n        if (res.Entries && res.StatusCode == 0) {\n          const rest = this.sortByFloorDescending(res.Entries);\n          this.houseListEnable = [...rest];\n          if (this.saveSpecialNoticeFile.CSpecialNoticeFileId) {\n            this.houseList2D = this.groupByFloor(this.addCIsSelectToA([...this.houseListEnable], this.saveSpecialNoticeFile.tblSpecialNoticeFileHouses ? [...this.saveSpecialNoticeFile.tblSpecialNoticeFileHouses] : []));\n          } else {\n            this.houseList2D = this.groupByFloor([...rest]);\n          }\n          this.isHouseList = true;\n          if (this.saveSpecialNoticeFile) {\n            this.houseList2D = this.updateCIsClick(this.houseList2D, this.saveSpecialNoticeFile.tblSpecialNoticeFileHouses);\n          }\n        }\n      });\n    }\n  }\n  isCheckAllRowChecked(row) {\n    return row.every(item => item.CIsSelect);\n  }\n  isCheckAllColumnChecked(index) {\n    if (this.isHouseList) {\n      if (index < 0 || index >= this.houseList2D[0].length) {\n        throw new Error(\"Invalid index. Index must be within the bounds of the array.\");\n      }\n      for (const floorData of this.houseList2D) {\n        if (index >= floorData.length || !floorData[index].CIsSelect) {\n          return false; // Found a customer with CIsEnable not true (or missing)\n        }\n      }\n      return true; // All customers at the given index have CIsEnable as true\n    }\n    return false;\n  }\n  enableAllAtIndex(checked, index) {\n    if (index < 0) {\n      throw new Error(\"Invalid index. Index must be a non-negative number.\");\n    }\n    for (const floorData of this.houseList2D) {\n      if (index < floorData.length && this.saveSpecialNoticeFile.selectedCNoticeType.value === floorData[index].CHouseType) {\n        // Check if index is valid for this floor\n        floorData[index].CIsSelect = checked;\n      }\n    }\n  }\n  enableAllRow(checked, row) {\n    for (const item of row) {\n      if (this.saveSpecialNoticeFile.selectedCNoticeType.value === item.CHouseType) {\n        // Check if index is valid for this floor\n        item.CIsSelect = checked;\n      }\n    }\n  }\n  getSpecialNoticeFileList() {\n    this._specialNoticeFileService.apiSpecialNoticeFileGetSpecialNoticeFileListPost$Json({\n      body: {\n        CBuildCaseId: this.selectedCBuildCase.value,\n        PageIndexLandLord: this.pageIndex,\n        PageIndexSales: this.pageIndexSales,\n        PageSizeLandLord: this.pageSize,\n        PageSizeSales: this.pageSizeSales\n      }\n    }).subscribe(res => {\n      if (res.Entries && res.StatusCode == 0) {\n        this.listSpecialNoticeFile = res.Entries;\n        this.totalRecords = res.Entries.TotalListLandLords || 0;\n        this.totalRecordsSales = res.Entries.TotalListSales || 0;\n      }\n    });\n  }\n  onStatusChange(newStatus) {}\n  getSpecialNoticeFileHouseHoldList() {\n    this._regularNoticeFileService.apiRegularNoticeFileGetListRegularNoticeFileHouseHoldPost$Json({\n      body: this.selectedCBuildCase.value\n    }).subscribe(res => {\n      if (res.Entries && res.StatusCode == 0) {\n        this.listSpecialNoticeFileHouseHold = res.Entries;\n      }\n    });\n  }\n  createArrayObjectFromArray(a, b) {\n    const c = {};\n    for (const item of a) {\n      const matchingItem = b.find(bItem => bItem.CHousehold === item && bItem.CIsSelected);\n      c[item] = !!matchingItem;\n    }\n    return c;\n  }\n  getItemByValue(value, options) {\n    for (const item of options) {\n      if (item.value === value) {\n        return item;\n      }\n    }\n    return null;\n  }\n  updateCIsEnable(houseList2D, houseSpecialNoticeFile) {\n    const selectedHouses = new Set(houseSpecialNoticeFile.map(item => `${item.CHouseHold}-${item.CFloor}`));\n    return houseList2D.map(floorArray => {\n      return floorArray.map(item => {\n        const key = `${item.CHouseHold}-${item.CFloor}`;\n        if (selectedHouses.has(key)) {\n          item.CIsSelect = true;\n        } else {\n          item.CIsSelect = false;\n        }\n        return item;\n      });\n    });\n  }\n  addCIsSelectToA(A, B) {\n    const mapB = new Map(B.map(item => [`${item.CHouseHold}-${item.CFloor}`, item.CIsSelect]));\n    return A.map(item => {\n      const key = `${item.CHouseHold}-${item.CFloor}`;\n      return {\n        ...item,\n        CIsSelect: mapB.has(key) ? mapB.get(key) : false\n      };\n    });\n  }\n  getSpecialNoticeFileById(item, ref) {\n    this._specialNoticeFileService.apiSpecialNoticeFileGetSpecialNoticeFileByIdPost$Json({\n      body: item.CSpecialNoticeFileId\n    }).subscribe(res => {\n      if (res.Entries && res.StatusCode == 0) {\n        const data = res.Entries;\n        this.saveSpecialNoticeFile = {\n          CFileUrl: data.tblSpecialNoticeFile?.CFileUrl || undefined,\n          CNoticeType: data.tblSpecialNoticeFile?.CNoticeType,\n          CBuildCaseId: data.tblSpecialNoticeFile?.CBuildCaseId,\n          CSpecialNoticeFileId: data.tblSpecialNoticeFile?.CSpecialNoticeFileId,\n          selectedCNoticeType: data.tblSpecialNoticeFile?.CNoticeType ? this.getItemByValue(data.tblSpecialNoticeFile?.CNoticeType, this.cNoticeTypeOptions) : this.cNoticeTypeOptions[0],\n          tblExamineLogs: data.tblExamineLogs ? data.tblExamineLogs : undefined,\n          CExamineNote: data.CExamineNote,\n          tblSpecialNoticeFileHouses: data.tblSpecialNoticeFileHouses?.filter(i => i.CIsSelect),\n          CExamineStauts: data.CExamineStauts\n        };\n        this.getHouseList();\n        this.dialogService.open(ref);\n      }\n    });\n  }\n  openPdfInNewTab(CFileUrl) {\n    if (CFileUrl) window.open(environment.BASE_WITHOUT_FILEROOT + CFileUrl, '_blank');\n  }\n  openModel(ref, item) {\n    this.isHouseList = false;\n    this.isNew = true;\n    this.onFileClear(); // 替換 clearImage()\n    this.saveSpecialNoticeFile = {\n      CNoticeType: 1,\n      CBuildCaseId: undefined,\n      CFile: undefined,\n      CHouse: [],\n      CSpecialNoticeFileId: undefined,\n      CIsSelectAll: false,\n      selectedCNoticeType: this.cNoticeTypeOptions[0],\n      CExamineNote: '',\n      tblSpecialNoticeFileHouses: undefined\n    };\n    if (item) {\n      this.isNew = false;\n      this.getSpecialNoticeFileById(item, ref);\n    } else {\n      this.isNew = true;\n      this.getHouseList();\n      this.dialogService.open(ref);\n    }\n  }\n  removeBase64Prefix(base64String) {\n    const prefixIndex = base64String.indexOf(\",\");\n    if (prefixIndex !== -1) {\n      return base64String.substring(prefixIndex + 1);\n    }\n    return base64String;\n  }\n  onDelete(item) {\n    if (window.confirm(`確定要刪除【項目${item.CSpecialNoticeFileId}】?`)) {\n      this._specialNoticeFileService.apiSpecialNoticeFileDeleteSpecialNoticeFilePost$Json({\n        body: item.CSpecialNoticeFileId\n      }).subscribe(res => {\n        if (res.StatusCode === 0) {\n          this.message.showSucessMSG(\"執行成功\");\n          this.onChangeBuildCase();\n        }\n      });\n    }\n    return item;\n  }\n  getTrueKeys(inputDict) {\n    const trueKeys = [];\n    for (const key in inputDict) {\n      if (inputDict[key]) {\n        trueKeys.push(key);\n      }\n    }\n    return trueKeys;\n  }\n  flattenAndFilter(data) {\n    const flattened = [];\n    for (const floorData of data) {\n      for (const house of floorData) {\n        if (house.CIsSelect && house.CIsEnable && house.CHouseType === this.saveSpecialNoticeFile.selectedCNoticeType.value) {\n          flattened.push({\n            CHouseID: house.CHouseID,\n            CIsSelect: house.CIsSelect\n          });\n        }\n      }\n    }\n    return flattened;\n  }\n  onSaveSpecialNoticeFile(ref) {\n    const param = {\n      CNoticeType: this.saveSpecialNoticeFile.selectedCNoticeType.value,\n      CBuildCaseId: this.selectedCBuildCase.value,\n      CFile: this.selectedFile ? this.selectedFile.CFileUpload : undefined,\n      CHouse: this.flattenAndFilter(this.houseList2D),\n      CSpecialNoticeFileId: this.saveSpecialNoticeFile.CSpecialNoticeFileId || undefined,\n      CIsSelectAll: this.saveSpecialNoticeFile.CIsSelectAll ?? false,\n      CExamineNote: this.saveSpecialNoticeFile.CExamineNote\n    };\n    this.validation(param.CHouse);\n    if (this.valid.errorMessages.length > 0) {\n      this.message.showErrorMSGs(this.valid.errorMessages);\n      return;\n    }\n    this._specialChangeCustomService.SaveSpecialNoticeFile(param).subscribe(res => {\n      if (res && res.body && res.body.StatusCode === 0) {\n        this.message.showSucessMSG(\"執行成功\");\n        this.onFileClear(); // 替換 clearImage()\n        this.getSpecialNoticeFileList();\n        ref.close();\n      } else {\n        this.message.showErrorMSG(res && res.body && res.body.Message);\n      }\n    });\n  }\n  onClose(ref) {\n    ref.close();\n  }\n  validation(CHouse) {\n    this.valid.clear();\n    if (this.isNew && !this.selectedFile) {\n      this.valid.required('[檔案]', '');\n    }\n    if (!(CHouse.length > 0)) {\n      this.valid.required('[適用戶別]', '');\n    }\n    this.valid.required('[送審說明]', this.saveSpecialNoticeFile.CExamineNote);\n  }\n  getActionName(actionID) {\n    let textR = \"\";\n    if (actionID != undefined) {\n      switch (actionID) {\n        case 1:\n          textR = \"傳送\";\n          break;\n        case 2:\n          textR = \"通過\";\n          break;\n        case 3:\n          textR = \"駁回\";\n          break;\n        default:\n          break;\n      }\n    }\n    return textR;\n  }\n  updateCIsClick(houseList2D, houseSpecialNoticeFile) {\n    const selectedHouses = houseSpecialNoticeFile.map(item => item.CHouseID);\n    return houseList2D.map(floorArray => {\n      return floorArray.map(item => {\n        if (selectedHouses.includes(item.CHouseID)) {\n          item.CIsSelect = true;\n        } else {\n          item.CIsSelect = false;\n        }\n        return item;\n      });\n    });\n  }\n  static {\n    this.ɵfac = function NoticeManagementComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || NoticeManagementComponent)(i0.ɵɵdirectiveInject(i1.AllowHelper), i0.ɵɵdirectiveInject(i2.NbDialogService), i0.ɵɵdirectiveInject(i3.MessageService), i0.ɵɵdirectiveInject(i4.ValidationHelper), i0.ɵɵdirectiveInject(i5.UtilityService), i0.ɵɵdirectiveInject(i6.BuildCaseService), i0.ɵɵdirectiveInject(i6.SpecialNoticeFileService), i0.ɵɵdirectiveInject(i6.RegularNoticeFileService), i0.ɵɵdirectiveInject(i6.HouseService), i0.ɵɵdirectiveInject(i7.NoticeServiceCustom));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: NoticeManagementComponent,\n      selectors: [[\"ngx-notice-management\"]],\n      standalone: true,\n      features: [i0.ɵɵInheritDefinitionFeature, i0.ɵɵStandaloneFeature],\n      decls: 20,\n      vars: 4,\n      consts: [[\"dialog\", \"\"], [\"accent\", \"success\"], [1, \"font-bold\", \"text-[#818181]\"], [1, \"d-flex\", \"flex-wrap\"], [1, \"col-md-6\"], [1, \"form-group\", \"d-flex\", \"align-items-center\", \"w-full\"], [\"for\", \"buildingName\", 1, \"label\", \"col-3\"], [\"placeholder\", \"\\u5EFA\\u6848\", 1, \"col-9\", 3, \"ngModelChange\", \"selectedChange\", \"ngModel\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [1, \"d-flex\", \"justify-content-end\", \"w-full\"], [\"class\", \"btn btn-secondary btn-sm\", 3, \"click\", 4, \"ngIf\"], [4, \"ngIf\"], [1, \"d-flex\", \"justify-content-center\"], [3, \"value\"], [1, \"btn\", \"btn-secondary\", \"btn-sm\", 3, \"click\"], [1, \"fas\", \"fa-plus\"], [1, \"table-responsive\", \"mt-4\"], [1, \"text-xl\", \"font-bold\"], [1, \"table\", \"table-striped\", \"border\", \"mt-3\", 2, \"background-color\", \"#f3f3f3\"], [1, \"text-center\", 2, \"background-color\", \"#27ae60\", \"color\", \"white\"], [\"scope\", \"col\", 1, \"col-1\"], [\"class\", \"text-center\", 4, \"ngFor\", \"ngForOf\"], [1, \"flex\", \"justify-center\", \"my-3\"], [\"aria-label\", \"Pagination\", 3, \"pageChange\", \"page\", \"pageSize\", \"collectionSize\"], [1, \"text-center\"], [1, \"cursor-pointer\", \"text-blue-500\", 3, \"click\"], [\"class\", \"btn btn-outline-success btn-sm text-left m-[2px]\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"btn btn-outline-danger btn-sm m-[2px]\", 3, \"click\", 4, \"ngIf\"], [1, \"btn\", \"btn-outline-success\", \"btn-sm\", \"text-left\", \"m-[2px]\", 3, \"click\"], [1, \"btn\", \"btn-outline-danger\", \"btn-sm\", \"m-[2px]\", 3, \"click\"], [2, \"width\", \"800px\", \"max-height\", \"95vh\"], [\"class\", \"px-4\", 4, \"ngIf\"], [1, \"px-4\"], [1, \"form-group\", \"d-flex\"], [\"for\", \"remark\", \"baseLabel\", \"\", 1, \"required-field\", \"mr-4\", 2, \"min-width\", \"75px\"], [\"placeholder\", \"Select Status\", 1, \"w-full\", 3, \"ngModelChange\", \"ngModel\", \"disabled\"], [1, \"form-group\", \"d-flex\", \"align-items-baseline\"], [1, \"d-flex\", \"flex-col\", \"mr-3\"], [\"for\", \"file\", \"baseLabel\", \"\", 1, \"required-field\", \"mb-0\", 2, \"min-width\", \"75px\"], [2, \"font-size\", \"10px\", \"color\", \"red\"], [1, \"flex\", \"flex-col\", \"items-start\", \"space-y-4\"], [\"type\", \"file\", \"id\", \"fileInput\", \"accept\", \"image/jpeg, image/jpg, application/pdf\", 1, \"hidden\", 2, \"display\", \"none\", 3, \"change\"], [\"for\", \"fileInput\", 1, \"cursor-pointer\", \"bg-blue-500\", \"hover:bg-blue-700\", \"text-white\", \"font-bold\", \"py-2\", \"px-4\", \"rounded\"], [1, \"fa-solid\", \"fa-cloud-arrow-up\", \"mr-2\"], [\"class\", \"flex items-center space-x-2\", 4, \"ngIf\"], [\"class\", \"text-sm ml-4\", 3, \"ngClass\", 4, \"ngIf\"], [1, \"form-group\", \"d-flex\", \"mb-0\"], [\"for\", \"houseList2D\", \"baseLabel\", \"\", 1, \"required-field\", \"mr-3\", 2, \"min-width\", \"75px\"], [\"class\", \"table-responsive mt-1\", 4, \"ngIf\"], [1, \"form-group\", \"d-flex\", \"align-items-center\"], [\"for\", \"status\", \"baseLabel\", \"\", 1, \"required-field\", \"mr-3\", 2, \"min-width\", \"75px\"], [\"nbInput\", \"\", 1, \"resize-none\", \"!max-w-full\", \"w-full\", 3, \"ngModelChange\", \"ngModel\", \"rows\"], [1, \"d-flex\", \"justify-content-center\", \"min-w-[90px]\"], [1, \"btn\", \"btn-danger\", \"btn-sm\", \"mr-4\", 3, \"click\"], [1, \"btn\", \"btn-success\", \"btn-sm\", \"min-w-[90px]\", 3, \"click\"], [\"class\", \"w-full\", 4, \"ngIf\"], [1, \"flex\", \"items-center\", \"space-x-2\"], [1, \"text-gray-600\"], [\"type\", \"button\", 1, \"text-red-500\", \"hover:text-red-700\", 3, \"click\"], [1, \"fa-solid\", \"fa-trash\"], [1, \"text-sm\", \"ml-4\", 3, \"ngClass\"], [1, \"table-responsive\", \"mt-1\"], [1, \"table\", \"table-bordered\", 2, \"background-color\", \"#f3f3f3\"], [4, \"ngFor\", \"ngForOf\"], [\"status\", \"basic\", 3, \"checkedChange\", \"checked\", \"disabled\"], [1, \"font-medium\"], [1, \"font-bold\", 3, \"ngClass\"], [1, \"w-full\"], [\"for\", \"status\", \"baseLabel\", \"\", 1, \"mr-3\", 2, \"min-width\", \"75px\"]],\n      template: function NoticeManagementComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"nb-card\", 1)(1, \"nb-card-header\");\n          i0.ɵɵelement(2, \"ngx-breadcrumb\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(3, \"nb-card-body\")(4, \"h1\", 2);\n          i0.ɵɵtext(5, \"\\u60A8\\u53EF\\u8207\\u6B64\\u7BA1\\u7406\\u5404\\u6236\\u5225\\u5167\\u4E4B\\u76F8\\u95DC\\u8CC7\\u8A0A\\uFF0C\\u5305\\u542B\\u57FA\\u672C\\u8CC7\\u6599\\u3001\\u7E73\\u6B3E\\u72C0\\u6CC1\\u3001\\u4E0A\\u50B3\\u5BA2\\u8B8A\\u5716\\u9762\\u3001\\u6AA2\\u8996\\u5BA2\\u8B8A\\u7D50\\u679C\\u7B49\\u7B49\\u3002 \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(6, \"div\", 3)(7, \"div\", 4)(8, \"div\", 5)(9, \"label\", 6);\n          i0.ɵɵtext(10, \"\\u5EFA\\u6848\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(11, \"nb-select\", 7);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function NoticeManagementComponent_Template_nb_select_ngModelChange_11_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.selectedCBuildCase, $event) || (ctx.selectedCBuildCase = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵlistener(\"selectedChange\", function NoticeManagementComponent_Template_nb_select_selectedChange_11_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onChangeBuildCase());\n          });\n          i0.ɵɵtemplate(12, NoticeManagementComponent_nb_option_12_Template, 2, 2, \"nb-option\", 8);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(13, \"div\", 4)(14, \"div\", 9);\n          i0.ɵɵtemplate(15, NoticeManagementComponent_button_15_Template, 3, 0, \"button\", 10);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(16, NoticeManagementComponent_ng_container_16_Template, 41, 8, \"ng-container\", 11);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(17, \"nb-card-footer\", 12);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(18, NoticeManagementComponent_ng_template_18_Template, 4, 2, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor);\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(11);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.selectedCBuildCase);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngForOf\", ctx.userBuildCaseOptions);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", ctx.isCreate);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.listSpecialNoticeFile);\n        }\n      },\n      dependencies: [CommonModule, i8.NgClass, i8.NgForOf, i8.NgIf, i8.DatePipe, SharedModule, i9.DefaultValueAccessor, i9.NgControlStatus, i9.NgModel, i2.NbCardComponent, i2.NbCardBodyComponent, i2.NbCardFooterComponent, i2.NbCardHeaderComponent, i2.NbCheckboxComponent, i2.NbInputDirective, i2.NbSelectComponent, i2.NbOptionComponent, i10.NgbPagination, i11.BreadcrumbComponent, i12.BaseLabelDirective, DateFormatHourPipe],\n      styles: [\"/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsImZpbGUiOiJub3RpY2UtbWFuYWdlbWVudC5jb21wb25lbnQuc2NzcyJ9 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvcGFnZXMvY29uc3RydWN0aW9uLXByb2plY3QtbWFuYWdlbWVudC9ub3RpY2UtbWFuYWdlbWVudC9ub3RpY2UtbWFuYWdlbWVudC5jb21wb25lbnQuc2NzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiO0FBQ0EsZ0xBQWdMIiwic291cmNlUm9vdCI6IiJ9 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "tap", "SharedModule", "BaseComponent", "environment", "DateFormatHourPipe", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵproperty", "case_r2", "ɵɵadvance", "ɵɵtextInterpolate1", "label", "ɵɵlistener", "NoticeManagementComponent_button_15_Template_button_click_0_listener", "ɵɵrestoreView", "_r3", "ctx_r3", "ɵɵnextContext", "dialog_r5", "ɵɵreference", "ɵɵresetView", "openModel", "ɵɵelement", "NoticeManagementComponent_ng_container_16_tr_18_button_12_Template_button_click_0_listener", "_r9", "item_r8", "$implicit", "NoticeManagementComponent_ng_container_16_tr_18_button_13_Template_button_click_0_listener", "_r10", "onDelete", "NoticeManagementComponent_ng_container_16_tr_18_Template_a_click_2_listener", "_r7", "openPdfInNewTab", "CFile", "ɵɵtemplate", "NoticeManagementComponent_ng_container_16_tr_18_button_12_Template", "NoticeManagementComponent_ng_container_16_tr_18_button_13_Template", "ɵɵtextInterpolate", "CFileName", "CHouseHold", "join", "CExamineStatus", "ɵɵpureFunction0", "_c0", "includes", "cExamineStatusOption", "ɵɵpipeBind2", "CApproveDate", "isUpdate", "isDelete", "NoticeManagementComponent_ng_container_16_tr_38_button_12_Template_button_click_0_listener", "_r13", "item_r12", "NoticeManagementComponent_ng_container_16_tr_38_button_13_Template_button_click_0_listener", "_r14", "NoticeManagementComponent_ng_container_16_tr_38_Template_a_click_2_listener", "_r11", "NoticeManagementComponent_ng_container_16_tr_38_button_12_Template", "NoticeManagementComponent_ng_container_16_tr_38_button_13_Template", "ɵɵelementContainerStart", "NoticeManagementComponent_ng_container_16_tr_18_Template", "ɵɵtwoWayListener", "NoticeManagementComponent_ng_container_16_Template_ngb_pagination_pageChange_20_listener", "$event", "_r6", "ɵɵtwoWayBindingSet", "pageIndex", "pageChanged", "NoticeManagementComponent_ng_container_16_tr_38_Template", "NoticeManagementComponent_ng_container_16_Template_ngb_pagination_pageChange_40_listener", "pageIndexSales", "pageChangedSales", "listSpecialNoticeFile", "ListLandLords", "ɵɵtwoWayProperty", "pageSize", "totalRecords", "ListSales", "pageSizeSales", "totalRecordsSales", "status_r16", "NoticeManagementComponent_ng_template_18_nb_card_body_3_div_17_Template_button_click_3_listener", "_r17", "clearImage", "fileName", "NoticeManagementComponent_ng_template_18_nb_card_body_3_span_18_Template_a_click_1_listener", "_r18", "saveSpecialNoticeFile", "CFileUrl", "imageUrl", "NoticeManagementComponent_ng_template_18_nb_card_body_3_div_22_tr_3_th_2_Template_nb_checkbox_checkedChange_1_listener", "idx_r20", "_r19", "index", "enableAllAtIndex", "isCheckAllColumnChecked", "selectedCNoticeType", "value", "NoticeManagementComponent_ng_template_18_nb_card_body_3_div_22_tr_3_th_2_Template", "houseList2D", "NoticeManagementComponent_ng_template_18_nb_card_body_3_div_22_tr_5_td_5_Template_nb_checkbox_checkedChange_2_listener", "house_r24", "_r23", "CIsSelect", "CIsEnable", "CHouseType", "CExamineStauts", "ɵɵpureFunction1", "_c1", "CID", "ɵɵtextInterpolate2", "CFloor", "NoticeManagementComponent_ng_template_18_nb_card_body_3_div_22_tr_5_Template_nb_checkbox_checkedChange_2_listener", "row_r22", "_r21", "enableAllRow", "NoticeManagementComponent_ng_template_18_nb_card_body_3_div_22_tr_5_td_5_Template", "isCheckAllRowChecked", "NoticeManagementComponent_ng_template_18_nb_card_body_3_div_22_tr_3_Template", "NoticeManagementComponent_ng_template_18_nb_card_body_3_div_22_tr_5_Template", "length", "ɵɵpipeBind1", "row_r26", "CCreateDt", "CCreator", "getActionName", "CAction", "CExamineNote", "NoticeManagementComponent_ng_template_18_nb_card_body_3_div_32_tr_16_Template", "tblExamineLogs", "NoticeManagementComponent_ng_template_18_nb_card_body_3_Template_nb_select_ngModelChange_4_listener", "_r15", "onStatusChange", "NoticeManagementComponent_ng_template_18_nb_card_body_3_nb_option_5_Template", "NoticeManagementComponent_ng_template_18_nb_card_body_3_Template_input_change_13_listener", "onFileSelected", "NoticeManagementComponent_ng_template_18_nb_card_body_3_div_17_Template", "NoticeManagementComponent_ng_template_18_nb_card_body_3_span_18_Template", "NoticeManagementComponent_ng_template_18_nb_card_body_3_div_22_Template", "NoticeManagementComponent_ng_template_18_nb_card_body_3_Template_textarea_ngModelChange_26_listener", "NoticeManagementComponent_ng_template_18_nb_card_body_3_Template_button_click_28_listener", "ref_r25", "dialogRef", "onClose", "NoticeManagementComponent_ng_template_18_nb_card_body_3_Template_button_click_30_listener", "onSaveSpecialNoticeFile", "NoticeManagementComponent_ng_template_18_nb_card_body_3_div_32_Template", "isNew", "cNoticeTypeOptions", "isHouseList", "NoticeManagementComponent_ng_template_18_nb_card_body_3_Template", "NoticeManagementComponent", "constructor", "_allow", "dialogService", "message", "valid", "_utilityService", "_buildCaseService", "_specialNoticeFileService", "_regularNoticeFileService", "_houseService", "_specialChangeCustomService", "pageFirst", "pageFirstSales", "buildCaseOptions", "typeContentManagementLandowner", "CFormType", "CNoticeType", "fileUploadConfig", "acceptedTypes", "acceptedFileRegex", "acceptAttribute", "helpText", "required", "disabled", "autoFillName", "buttonText", "buttonIcon", "maxFileSize", "multiple", "showPreview", "selectedFile", "ngOnInit", "getUserBuildCase", "newPage", "onFileUpload", "fileResult", "onFileClear", "onChangeBuildCase", "selectedCBuildCase", "getSpecialNoticeFileHouseHoldList", "getSpecialNoticeFileList", "apiBuildCaseGetUserBuildCasePost$Json", "body", "pipe", "res", "Entries", "StatusCode", "userBuildCaseOptions", "map", "CBuildCaseName", "cID", "subscribe", "groupByFloor", "customerData", "groupedData", "uniqueFloors", "Array", "from", "Set", "customer", "filter", "floor", "push", "floorIndex", "indexOf", "CHouseID", "sortByFloorDescending", "arr", "sort", "a", "b", "getHouseList", "apiHouseGetHouseListPost$Json", "CBuildCaseID", "CIsPagi", "rest", "houseListEnable", "CSpecialNoticeFileId", "addCIsSelectToA", "tblSpecialNoticeFileHouses", "updateCIsClick", "row", "every", "item", "Error", "floorData", "checked", "apiSpecialNoticeFileGetSpecialNoticeFileListPost$Json", "CBuildCaseId", "PageIndexLandLord", "PageIndexSales", "PageSizeLandLord", "PageSizeSales", "TotalListLandLords", "TotalListSales", "newStatus", "apiRegularNoticeFileGetListRegularNoticeFileHouseHoldPost$Json", "listSpecialNoticeFileHouseHold", "createArrayObjectFromArray", "c", "matchingItem", "find", "bItem", "CHousehold", "CIsSelected", "getItemByValue", "options", "updateCIsEnable", "houseSpecialNoticeFile", "selectedHouses", "floorArray", "key", "has", "A", "B", "mapB", "Map", "get", "getSpecialNoticeFileById", "ref", "apiSpecialNoticeFileGetSpecialNoticeFileByIdPost$Json", "data", "tblSpecialNoticeFile", "undefined", "i", "open", "window", "BASE_WITHOUT_FILEROOT", "CHouse", "CIsSelectAll", "removeBase64Prefix", "base64String", "prefixIndex", "substring", "confirm", "apiSpecialNoticeFileDeleteSpecialNoticeFilePost$Json", "showSucessMSG", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "inputDict", "true<PERSON>eys", "flattenAndFilter", "flattened", "house", "param", "CFileUpload", "validation", "errorMessages", "showErrorMSGs", "SaveSpecialNoticeFile", "close", "showErrorMSG", "Message", "clear", "actionID", "textR", "ɵɵdirectiveInject", "i1", "AllowHelper", "i2", "NbDialogService", "i3", "MessageService", "i4", "ValidationHelper", "i5", "UtilityService", "i6", "BuildCaseService", "SpecialNoticeFileService", "RegularNoticeFileService", "HouseService", "i7", "NoticeServiceCustom", "selectors", "standalone", "features", "ɵɵInheritDefinitionFeature", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "NoticeManagementComponent_Template", "rf", "ctx", "NoticeManagementComponent_Template_nb_select_ngModelChange_11_listener", "_r1", "NoticeManagementComponent_Template_nb_select_selected<PERSON>hange_11_listener", "NoticeManagementComponent_nb_option_12_Template", "NoticeManagementComponent_button_15_Template", "NoticeManagementComponent_ng_container_16_Template", "NoticeManagementComponent_ng_template_18_Template", "ɵɵtemplateRefExtractor", "isCreate", "i8", "Ng<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "DatePipe", "i9", "DefaultValueAccessor", "NgControlStatus", "NgModel", "NbCardComponent", "NbCardBodyComponent", "NbCardFooterComponent", "NbCardHeaderComponent", "NbCheckboxComponent", "NbInputDirective", "NbSelectComponent", "NbOptionComponent", "i10", "NgbPagination", "i11", "BreadcrumbComponent", "i12", "BaseLabelDirective", "styles"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\construction-project-management\\notice-management\\notice-management.component.ts", "C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\construction-project-management\\notice-management\\notice-management.component.html"], "sourcesContent": ["import { Component, ElementRef, OnInit, ViewChild } from '@angular/core';\r\nimport { CommonModule, DatePipe } from '@angular/common';\r\nimport { NbDialogService } from '@nebular/theme';\r\nimport { MessageService } from 'src/app/shared/services/message.service';\r\nimport { ValidationHelper } from 'src/app/shared/helper/validationHelper';\r\nimport { BuildCaseService, HouseService, RegularNoticeFileService, SpecialNoticeFileService } from 'src/services/api/services';\r\nimport { LabelInOptionsPipe, TypeMailPipe } from 'src/app/@theme/pipes/mapping.pipe';\r\nimport { AllowHelper } from 'src/app/shared/helper/allowHelper';\r\nimport { GetHouseListRes, GetListHouseHoldRes, GetSpecialNoticeFileListRes, HouseSpecialNoticeFile, TblExamineLog } from 'src/services/api/models';\r\nimport { tap } from 'rxjs';\r\nimport { SharedModule } from '../../components/shared.module';\r\nimport { BaseComponent } from '../../components/base/baseComponent';\r\nimport { EnumFileType } from 'src/app/shared/enum/enumFileType';\r\nimport { NoticeServiceCustom } from 'src/app/@core/service/notice.service';\r\nimport { UtilityService } from 'src/app/shared/services/utility.service';\r\nimport { environment } from 'src/environments/environment';\r\nimport { DateFormatHourPipe } from 'src/app/@theme/pipes';\r\nimport { FileUploadComponent, FileUploadConfig, FileUploadResult } from '../../components/file-upload/file-upload.component';\r\n\r\nexport interface HouseList {\r\n  CFloor?: number | null;\r\n  CHouseHold?: string | null;\r\n  CHouseID?: number;\r\n  CID?: number;\r\n  CIsSelect?: boolean | null;\r\n  CHouseType?: number | null;\r\n  CIsEnable?: boolean | null;\r\n}\r\nexport interface SaveSpecialNoticeFileCus {\r\n  CFileUrl?: string\r\n  CNoticeType?: number\r\n  CBuildCaseId?: number\r\n  CFile?: Blob\r\n  CHouse?: Array<string>\r\n  CSpecialNoticeFileId?: number\r\n  CIsSelectAll?: boolean\r\n  selectedCNoticeType?: any\r\n  CExamineNote?: string | null;\r\n  tblExamineLogs?: TblExamineLog[],\r\n  tblSpecialNoticeFileHouses: any\r\n  CExamineStauts?: number;\r\n}\r\n\r\n@Component({\r\n  selector: 'ngx-notice-management',\r\n  templateUrl: './notice-management.component.html',\r\n  styleUrls: ['./notice-management.component.scss'],\r\n  standalone: true,\r\n  imports: [CommonModule, SharedModule, TypeMailPipe, DatePipe, LabelInOptionsPipe, DateFormatHourPipe, FileUploadComponent],\r\n})\r\n\r\nexport class NoticeManagementComponent extends BaseComponent implements OnInit {\r\n  constructor(\r\n    private _allow: AllowHelper,\r\n    private dialogService: NbDialogService,\r\n    private message: MessageService,\r\n    private valid: ValidationHelper,\r\n    private _utilityService: UtilityService,\r\n    private _buildCaseService: BuildCaseService,\r\n    private _specialNoticeFileService: SpecialNoticeFileService,\r\n    private _regularNoticeFileService: RegularNoticeFileService,\r\n    private _houseService: HouseService,\r\n    private _specialChangeCustomService: NoticeServiceCustom\r\n  ) { super(_allow) }\r\n\r\n  override ngOnInit(): void {\r\n    this.getUserBuildCase()\r\n  }\r\n\r\n  selectedCBuildCase: any\r\n\r\n  override pageFirst = 1;\r\n  override pageSize = 10;\r\n  override pageIndex = 1;\r\n  override totalRecords = 0;\r\n  pageFirstSales = 1;\r\n  pageSizeSales = 10;\r\n  pageIndexSales = 1;\r\n  totalRecordsSales = 0;\r\n\r\n\r\n  saveSpecialNoticeFile: SaveSpecialNoticeFileCus\r\n\r\n  buildCaseOptions: any[] = [{ label: '全部', value: '' }]\r\n\r\n  cNoticeTypeOptions: any[] = [\r\n    { label: '地主戶', value: 1 },\r\n    { label: '銷售戶', value: 2 }\r\n  ]\r\n  seletectedNoticeType: any\r\n\r\n  typeContentManagementLandowner = {\r\n    CFormType: 1,\r\n    CNoticeType: 1\r\n  }\r\n\r\n  houseList2D: HouseList[][]\r\n  userBuildCaseOptions: any\r\n\r\n  pageChanged(newPage: number) {\r\n    this.pageIndex = newPage;\r\n  }\r\n  pageChangedSales(newPage: number) {\r\n    this.pageIndexSales = newPage;\r\n  }\r\n\r\n  // 檔案上傳配置\r\n  fileUploadConfig: FileUploadConfig = {\r\n    acceptedTypes: ['application/pdf'],\r\n    acceptedFileRegex: /pdf/i,\r\n    acceptAttribute: 'application/pdf',\r\n    label: '上傳檔案',\r\n    helpText: '*請上傳PDF格式',\r\n    required: false,\r\n    disabled: false,\r\n    autoFillName: false,\r\n    buttonText: '上傳',\r\n    buttonIcon: 'fa-solid fa-cloud-arrow-up',\r\n    maxFileSize: 10,\r\n    multiple: false,\r\n    showPreview: false\r\n  };\r\n\r\n  selectedFile: FileUploadResult | null = null;\r\n\r\n  // 檔案選擇事件處理\r\n  onFileUpload(fileResult: FileUploadResult) {\r\n    this.selectedFile = fileResult;\r\n  }\r\n  // 檔案清除事件處理  \r\n  onFileClear() {\r\n    this.selectedFile = null;\r\n  }\r\n\r\n  userBuildCaseSelected: any\r\n\r\n  onChangeBuildCase() {\r\n    if (this.selectedCBuildCase.value) {\r\n      this.getSpecialNoticeFileHouseHoldList()\r\n      this.getSpecialNoticeFileList()\r\n    }\r\n  }\r\n\r\n  getUserBuildCase() {\r\n    this._buildCaseService.apiBuildCaseGetUserBuildCasePost$Json({ body: {} }).pipe(\r\n      tap(res => {\r\n        if (res.Entries && res.StatusCode == 0) {\r\n          this.userBuildCaseOptions = res.Entries.map(res => {\r\n            return {\r\n              label: res.CBuildCaseName,\r\n              value: res.cID\r\n            }\r\n          })\r\n          this.selectedCBuildCase = this.userBuildCaseOptions[0]\r\n          if (this.selectedCBuildCase.value) {\r\n            this.getSpecialNoticeFileHouseHoldList()\r\n            this.getSpecialNoticeFileList()\r\n          }\r\n        }\r\n      }),\r\n    ).subscribe()\r\n  }\r\n\r\n  groupByFloor(customerData: HouseList[]): HouseList[][] {\r\n\r\n    const groupedData: HouseList[][] = [];\r\n    const uniqueFloors = Array.from(new Set(\r\n      customerData.map(customer => customer.CFloor).filter(floor => floor !== null) as number[]\r\n    ));\r\n    for (const floor of uniqueFloors) {\r\n      groupedData.push([]);\r\n    }\r\n    for (const customer of customerData) {\r\n      const floorIndex = uniqueFloors.indexOf(customer.CFloor as number);\r\n      if (floorIndex !== -1) {\r\n        groupedData[floorIndex].push({\r\n          CIsSelect: customer?.CIsSelect || false,\r\n          CHouseID: customer.CID,\r\n          CHouseType: customer.CHouseType,\r\n          CFloor: customer.CFloor,\r\n          CHouseHold: customer.CHouseHold,\r\n          CIsEnable: customer.CIsEnable,\r\n        });\r\n      }\r\n    }\r\n    return groupedData;\r\n  }\r\n\r\n  isHouseList = false\r\n  houseListEnable: GetHouseListRes[]\r\n  sortByFloorDescending(arr: GetHouseListRes[]): GetHouseListRes[] {\r\n    return arr.sort((a, b) => (b.CFloor ?? 0) - (a.CFloor ?? 0));\r\n  }\r\n\r\n  getHouseList() {\r\n    if (this.selectedCBuildCase.value) {\r\n      this._houseService.apiHouseGetHouseListPost$Json({\r\n        body: {\r\n          CBuildCaseID: this.selectedCBuildCase.value, CIsPagi: false\r\n        }\r\n      }).subscribe(res => {\r\n        if (res.Entries && res.StatusCode == 0) {\r\n          const rest = this.sortByFloorDescending(res.Entries)\r\n          this.houseListEnable = [...rest]\r\n          if (this.saveSpecialNoticeFile.CSpecialNoticeFileId) {\r\n            this.houseList2D = this.groupByFloor(this.addCIsSelectToA([...this.houseListEnable], this.saveSpecialNoticeFile.tblSpecialNoticeFileHouses ? [...this.saveSpecialNoticeFile.tblSpecialNoticeFileHouses] : []))\r\n          } else {\r\n            this.houseList2D = this.groupByFloor([...rest])\r\n          }\r\n          this.isHouseList = true;\r\n          if (this.saveSpecialNoticeFile) {\r\n            this.houseList2D = this.updateCIsClick(this.houseList2D, this.saveSpecialNoticeFile.tblSpecialNoticeFileHouses)\r\n          }\r\n        }\r\n      })\r\n    }\r\n  }\r\n\r\n  isCheckAllRowChecked(row: any[]): boolean {\r\n    return row.every((item: { CIsSelect: any; }) => item.CIsSelect);\r\n  }\r\n\r\n  isCheckAllColumnChecked(index: number): boolean {\r\n    if (this.isHouseList) {\r\n      if (index < 0 || index >= this.houseList2D[0].length) {\r\n        throw new Error(\"Invalid index. Index must be within the bounds of the array.\");\r\n      }\r\n      for (const floorData of this.houseList2D) {\r\n        if (index >= floorData.length || !floorData[index].CIsSelect) {\r\n          return false; // Found a customer with CIsEnable not true (or missing)\r\n        }\r\n      }\r\n      return true; // All customers at the given index have CIsEnable as true\r\n    }\r\n    return false\r\n  }\r\n\r\n  enableAllAtIndex(checked: boolean, index: number): void {\r\n    if (index < 0) {\r\n      throw new Error(\"Invalid index. Index must be a non-negative number.\");\r\n    }\r\n    for (const floorData of this.houseList2D) {\r\n      if (index < floorData.length && (this.saveSpecialNoticeFile.selectedCNoticeType.value === floorData[index].CHouseType)) { // Check if index is valid for this floor\r\n        floorData[index].CIsSelect = checked;\r\n      }\r\n    }\r\n  }\r\n\r\n  enableAllRow(checked: boolean, row: HouseList[]) {\r\n    for (const item of row) {\r\n      if ((this.saveSpecialNoticeFile.selectedCNoticeType.value === item.CHouseType)) { // Check if index is valid for this floor\r\n        item.CIsSelect = checked;\r\n      }\r\n    }\r\n  }\r\n\r\n\r\n  listSpecialNoticeFile: GetSpecialNoticeFileListRes\r\n\r\n  getSpecialNoticeFileList() {\r\n    this._specialNoticeFileService.apiSpecialNoticeFileGetSpecialNoticeFileListPost$Json({\r\n      body: {\r\n        CBuildCaseId: this.selectedCBuildCase.value,\r\n        PageIndexLandLord: this.pageIndex,\r\n        PageIndexSales: this.pageIndexSales,\r\n        PageSizeLandLord: this.pageSize,\r\n        PageSizeSales: this.pageSizeSales,\r\n      }\r\n    }).subscribe(res => {\r\n      if (res.Entries && res.StatusCode == 0) {\r\n        this.listSpecialNoticeFile = res.Entries\r\n        this.totalRecords = res.Entries.TotalListLandLords || 0\r\n        this.totalRecordsSales = res.Entries.TotalListSales || 0\r\n      }\r\n    })\r\n  }\r\n  listSpecialNoticeFileHouseHold: GetListHouseHoldRes[]\r\n\r\n  houseHoldList: string[]\r\n\r\n  onStatusChange(newStatus: any) {\r\n  }\r\n\r\n  getSpecialNoticeFileHouseHoldList() {\r\n    this._regularNoticeFileService.apiRegularNoticeFileGetListRegularNoticeFileHouseHoldPost$Json({\r\n      body: this.selectedCBuildCase.value\r\n    }).subscribe(res => {\r\n      if (res.Entries && res.StatusCode == 0) {\r\n        this.listSpecialNoticeFileHouseHold = res.Entries\r\n      }\r\n    })\r\n  }\r\n\r\n  createArrayObjectFromArray(a: string[], b: { CSpecialNoticeFileHouseholdId: number, CSpecialNoticeFileId: number, CHousehold: string, CIsSelected: boolean }[] | any[]): { [key: string]: boolean } {\r\n    const c: { [key: string]: boolean } = {};\r\n    for (const item of a) {\r\n      const matchingItem = b.find(bItem => bItem.CHousehold === item && bItem.CIsSelected);\r\n      c[item] = !!matchingItem;\r\n    }\r\n    return c;\r\n  }\r\n\r\n\r\n  getItemByValue(value: any, options: any[]) {\r\n    for (const item of options) {\r\n      if (item.value === value) {\r\n        return item;\r\n      }\r\n    }\r\n    return null;\r\n  }\r\n\r\n  cExamineStatusOption = ['待審核', '已通過', '已駁回']\r\n\r\n  updateCIsEnable(houseList2D: HouseList[][], houseSpecialNoticeFile: HouseSpecialNoticeFile[]): HouseList[][] {\r\n    const selectedHouses = new Set(houseSpecialNoticeFile.map(item => `${item.CHouseHold}-${item.CFloor}`));\r\n    return houseList2D.map(floorArray => {\r\n      return floorArray.map(item => {\r\n        const key = `${item.CHouseHold}-${item.CFloor}`;\r\n        if (selectedHouses.has(key)) {\r\n          item.CIsSelect = true;\r\n        } else {\r\n          item.CIsSelect = false;\r\n        }\r\n        return item;\r\n      });\r\n    });\r\n  }\r\n\r\n  addCIsSelectToA(A: any[], B: any[]): any[] {\r\n    const mapB = new Map(B.map(item => [`${item.CHouseHold}-${item.CFloor}`, item.CIsSelect]));\r\n    return A.map(item => {\r\n      const key = `${item.CHouseHold}-${item.CFloor}`;\r\n      return {\r\n        ...item,\r\n        CIsSelect: mapB.has(key) ? mapB.get(key) : false\r\n      };\r\n    });\r\n  }\r\n\r\n\r\n  getSpecialNoticeFileById(item: any, ref: any) {\r\n    this._specialNoticeFileService.apiSpecialNoticeFileGetSpecialNoticeFileByIdPost$Json({\r\n      body: item.CSpecialNoticeFileId\r\n    }).subscribe(res => {\r\n      if (res.Entries && res.StatusCode == 0) {\r\n        const data = res.Entries\r\n        this.saveSpecialNoticeFile = {\r\n          CFileUrl: data.tblSpecialNoticeFile?.CFileUrl || undefined,\r\n          CNoticeType: data.tblSpecialNoticeFile?.CNoticeType,\r\n          CBuildCaseId: data.tblSpecialNoticeFile?.CBuildCaseId,\r\n          CSpecialNoticeFileId: data.tblSpecialNoticeFile?.CSpecialNoticeFileId,\r\n          selectedCNoticeType: data.tblSpecialNoticeFile?.CNoticeType ? this.getItemByValue(data.tblSpecialNoticeFile?.CNoticeType, this.cNoticeTypeOptions) : this.cNoticeTypeOptions[0],\r\n          tblExamineLogs: data.tblExamineLogs ? data.tblExamineLogs : undefined,\r\n          CExamineNote: data.CExamineNote,\r\n          tblSpecialNoticeFileHouses: data.tblSpecialNoticeFileHouses?.filter((i: any) => i.CIsSelect),\r\n          CExamineStauts: data.CExamineStauts\r\n        }\r\n        this.getHouseList()\r\n        this.dialogService.open(ref)\r\n      }\r\n    })\r\n  }\r\n\r\n  isNew = true\r\n\r\n  openPdfInNewTab(CFileUrl?: any) {\r\n    if (CFileUrl) window.open(environment.BASE_WITHOUT_FILEROOT + CFileUrl, '_blank');\r\n  }\r\n  openModel(ref: any, item?: any) {\r\n    this.isHouseList = false\r\n    this.isNew = true\r\n    this.onFileClear() // 替換 clearImage()\r\n    this.saveSpecialNoticeFile = {\r\n      CNoticeType: 1,\r\n      CBuildCaseId: undefined,\r\n      CFile: undefined,\r\n      CHouse: [],\r\n      CSpecialNoticeFileId: undefined,\r\n      CIsSelectAll: false,\r\n      selectedCNoticeType: this.cNoticeTypeOptions[0],\r\n      CExamineNote: '',\r\n      tblSpecialNoticeFileHouses: undefined\r\n    }\r\n\r\n    if (item) {\r\n      this.isNew = false\r\n      this.getSpecialNoticeFileById(item, ref)\r\n    } else {\r\n      this.isNew = true\r\n      this.getHouseList()\r\n      this.dialogService.open(ref)\r\n    }\r\n  }\r\n\r\n  removeBase64Prefix(base64String: any) {\r\n    const prefixIndex = base64String.indexOf(\",\");\r\n    if (prefixIndex !== -1) {\r\n      return base64String.substring(prefixIndex + 1);\r\n    }\r\n    return base64String;\r\n  }\r\n\r\n  onDelete(item: any) {\r\n    if (window.confirm(`確定要刪除【項目${item.CSpecialNoticeFileId}】?`)) {\r\n      this._specialNoticeFileService.apiSpecialNoticeFileDeleteSpecialNoticeFilePost$Json({\r\n        body: item.CSpecialNoticeFileId\r\n      }).subscribe(res => {\r\n        if (res.StatusCode === 0) {\r\n          this.message.showSucessMSG(\"執行成功\");\r\n          this.onChangeBuildCase()\r\n        }\r\n      })\r\n    }\r\n    return item\r\n  }\r\n\r\n  getTrueKeys(inputDict: { [key: string]: boolean }): string[] {\r\n    const trueKeys: string[] = [];\r\n    for (const key in inputDict) {\r\n      if (inputDict[key]) {\r\n        trueKeys.push(key);\r\n      }\r\n    }\r\n    return trueKeys;\r\n  }\r\n\r\n  flattenAndFilter(data: any[][]): HouseSpecialNoticeFile[] {\r\n    const flattened: HouseSpecialNoticeFile[] = [];\r\n    for (const floorData of data) {\r\n      for (const house of floorData) {\r\n        if (house.CIsSelect && house.CIsEnable && house.CHouseType === this.saveSpecialNoticeFile.selectedCNoticeType.value) {\r\n          flattened.push({\r\n            CHouseID: house.CHouseID,\r\n            CIsSelect: house.CIsSelect,\r\n          });\r\n        }\r\n      }\r\n    }\r\n    return flattened;\r\n  }\r\n  onSaveSpecialNoticeFile(ref: any) {\r\n    const param = {\r\n      CNoticeType: this.saveSpecialNoticeFile.selectedCNoticeType.value,\r\n      CBuildCaseId: this.selectedCBuildCase.value,\r\n      CFile: this.selectedFile ? this.selectedFile.CFileUpload : undefined,\r\n      CHouse: this.flattenAndFilter(this.houseList2D),\r\n      CSpecialNoticeFileId: this.saveSpecialNoticeFile.CSpecialNoticeFileId || undefined,\r\n      CIsSelectAll: this.saveSpecialNoticeFile.CIsSelectAll ?? false,\r\n      CExamineNote: this.saveSpecialNoticeFile.CExamineNote\r\n    }\r\n\r\n    this.validation(param.CHouse)\r\n\r\n    if (this.valid.errorMessages.length > 0) {\r\n      this.message.showErrorMSGs(this.valid.errorMessages);\r\n      return\r\n    }    this._specialChangeCustomService.SaveSpecialNoticeFile(param).subscribe(res => {\r\n      if (res && res.body! && res.body.StatusCode! === 0) {\r\n        this.message.showSucessMSG(\"執行成功\");\r\n        this.onFileClear() // 替換 clearImage()\r\n        this.getSpecialNoticeFileList()\r\n        ref.close();\r\n      } else {\r\n        this.message.showErrorMSG(res && res.body && res.body.Message!);\r\n      }\r\n    })\r\n  }\r\n\r\n\r\n  onClose(ref: any) {\r\n    ref.close();\r\n  }\r\n\r\n  validation(CHouse: any[]) {\r\n    this.valid.clear();\r\n    if (this.isNew && !this.selectedFile) {\r\n      this.valid.required('[檔案]', '')\r\n    }\r\n    if (!(CHouse.length > 0)) {\r\n      this.valid.required('[適用戶別]', '')\r\n    }\r\n    this.valid.required('[送審說明]', this.saveSpecialNoticeFile.CExamineNote)\r\n  }\r\n\r\n  getActionName(actionID: number | undefined) {\r\n    let textR = \"\";\r\n    if (actionID != undefined) {\r\n      switch (actionID) {\r\n        case 1:\r\n          textR = \"傳送\";\r\n          break;\r\n        case 2:\r\n          textR = \"通過\";\r\n          break;\r\n        case 3:\r\n          textR = \"駁回\";\r\n          break;\r\n        default:\r\n          break;\r\n      }\r\n    }\r\n    return textR;\r\n  }\r\n\r\n\r\n  updateCIsClick(houseList2D: HouseList[][], houseSpecialNoticeFile: HouseSpecialNoticeFile[]): HouseList[][] {\r\n    const selectedHouses = houseSpecialNoticeFile.map(item => item.CHouseID);\r\n    return houseList2D.map(floorArray => {\r\n      return floorArray.map(item => {\r\n        if (selectedHouses.includes(item.CHouseID)) {\r\n          item.CIsSelect = true;\r\n        } else {\r\n          item.CIsSelect = false;\r\n        }\r\n        return item;\r\n      });\r\n    });\r\n  }\r\n}\r\n\r\n", "<nb-card accent=\"success\">\r\n  <nb-card-header>\r\n    <ngx-breadcrumb></ngx-breadcrumb>\r\n  </nb-card-header>\r\n  <nb-card-body>\r\n    <h1 class=\"font-bold text-[#818181]\">您可與此管理各戶別內之相關資訊，包含基本資料、繳款狀況、上傳客變圖面、檢視客變結果等等。 </h1>\r\n    <div class=\"d-flex flex-wrap\">\r\n      <div class=\"col-md-6\">\r\n        <div class=\"form-group d-flex align-items-center w-full\">\r\n          <label for=\"buildingName\" class=\"label col-3\">建案</label>\r\n          <nb-select placeholder=\"建案\" [(ngModel)]=\"selectedCBuildCase\" class=\"col-9\"\r\n            (selectedChange)=\"onChangeBuildCase()\">\r\n            <nb-option *ngFor=\"let case of userBuildCaseOptions\" [value]=\"case\">\r\n              {{ case.label }}\r\n            </nb-option>\r\n          </nb-select>\r\n        </div>\r\n      </div>\r\n      <div class=\"col-md-6\">\r\n        <div class=\"d-flex justify-content-end w-full\">\r\n          <button class=\"btn btn-secondary btn-sm\" (click)=\"openModel(dialog)\" *ngIf=\"isCreate\">\r\n            新增檔案 <i class=\"fas fa-plus\"></i>\r\n          </button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <ng-container *ngIf=\"listSpecialNoticeFile\">\r\n      <div class=\"table-responsive mt-4\">\r\n        <h4 class=\"text-xl font-bold\">地主戶 </h4>\r\n        <table class=\"table table-striped border mt-3\" style=\"background-color:#f3f3f3;\">\r\n          <thead>\r\n            <tr style=\"background-color: #27ae60; color: white;\" class=\"text-center\">\r\n              <th scope=\"col\" class=\"col-1 \">檔案名稱</th>\r\n              <th scope=\"col\" class=\"col-1\">適用戶別 </th>\r\n              <th scope=\"col\" class=\"col-1\">審核狀態</th>\r\n              <th scope=\"col\" class=\"col-1\">審核日期</th>\r\n              <th scope=\"col\" class=\"col-1\">操作</th>\r\n            </tr>\r\n          </thead>\r\n          <tbody>\r\n            <tr *ngFor=\"let item of listSpecialNoticeFile?.ListLandLords ; let i = index\" class=\"text-center\">\r\n              <td>\r\n                <a class=\"cursor-pointer text-blue-500\" (click)=\"openPdfInNewTab(item?.CFile)\">{{ item?.CFileName}}</a>\r\n              </td>\r\n              <td>{{ item.CHouseHold?.join('、')}}</td>\r\n              <td>{{ item.CExamineStatus != null && [0, 1, 2].includes(item.CExamineStatus) ?\r\n                cExamineStatusOption[item.CExamineStatus] : '' }}</td>\r\n              <td>{{ item?.CApproveDate | date:'yyyy/MM/dd HH:mm:ss' }}</td>\r\n              <td>\r\n                <button class=\"btn btn-outline-success btn-sm text-left m-[2px]\" *ngIf=\"isUpdate\"\r\n                  (click)=\"openModel(dialog, item)\">\r\n                  編輯\r\n                </button>\r\n                <button class=\"btn btn-outline-danger btn-sm m-[2px]\" *ngIf=\"isDelete\" (click)=\"onDelete(item)\">\r\n                  刪除\r\n                </button>\r\n              </td>\r\n            </tr>\r\n          </tbody>\r\n        </table>\r\n      </div>\r\n      <div class=\"flex justify-center my-3\">\r\n        <ngb-pagination [(page)]=\"pageIndex\" [pageSize]=\"pageSize\" [collectionSize]=\"totalRecords\"\r\n          (pageChange)=\"pageChanged($event)\" aria-label=\"Pagination\">\r\n        </ngb-pagination>\r\n      </div>\r\n      <div class=\"table-responsive mt-4\">\r\n        <h4 class=\"text-xl font-bold\">銷售戶</h4>\r\n        <table class=\"table table-striped border mt-3\" style=\"background-color:#f3f3f3;\">\r\n          <thead>\r\n            <tr style=\"background-color: #27ae60; color: white;\" class=\"text-center\">\r\n              <th scope=\"col\" class=\"col-1 \">檔案名稱</th>\r\n              <th scope=\"col\" class=\"col-1\">適用戶別 </th>\r\n              <th scope=\"col\" class=\"col-1\">審核狀態</th>\r\n              <th scope=\"col\" class=\"col-1\">審核日期</th>\r\n              <th scope=\"col\" class=\"col-1\">操作</th>\r\n            </tr>\r\n          </thead>\r\n          <tbody>\r\n            <tr *ngFor=\"let item of listSpecialNoticeFile?.ListSales ; let i = index\" class=\"text-center\">\r\n              <td>\r\n                <a class=\"cursor-pointer text-blue-500\" (click)=\"openPdfInNewTab(item?.CFile)\">{{ item?.CFileName}}</a>\r\n              </td>\r\n              <td>{{ item.CHouseHold?.join('、')}}</td>\r\n              <td>\r\n                {{ item.CExamineStatus != null && [0, 1, 2].includes(item.CExamineStatus) ?\r\n                cExamineStatusOption[item.CExamineStatus] : '' }}\r\n              </td>\r\n              <td>{{ item?.CApproveDate | date:'yyyy-MM-dd HH:mm:ss'}}</td>\r\n              <td>\r\n                <button class=\"btn btn-outline-success btn-sm text-left m-[2px]\" (click)=\"openModel(dialog, item)\"\r\n                  *ngIf=\"isUpdate\">\r\n                  編輯\r\n                </button>\r\n                <button class=\"btn btn-outline-danger btn-sm m-[2px]\" *ngIf=\"isDelete\" (click)=\"onDelete(item)\">\r\n                  刪除\r\n                </button>\r\n              </td>\r\n            </tr>\r\n          </tbody>\r\n        </table>\r\n      </div>\r\n      <div class=\"flex justify-center my-3\">\r\n        <ngb-pagination [(page)]=\"pageIndexSales\" [pageSize]=\"pageSizeSales\" [collectionSize]=\"totalRecordsSales\"\r\n          (pageChange)=\"pageChangedSales($event)\" aria-label=\"Pagination\">\r\n        </ngb-pagination>\r\n      </div>\r\n    </ng-container>\r\n  </nb-card-body>\r\n  <nb-card-footer class=\"d-flex justify-content-center\">\r\n\r\n  </nb-card-footer>\r\n</nb-card>\r\n\r\n\r\n\r\n<ng-template #dialog let-dialog let-ref=\"dialogRef\">\r\n  <nb-card style=\"width:800px; max-height: 95vh\">\r\n    <nb-card-header> {{isNew ? '新增檔案-地主戶': '編輯' }}\r\n    </nb-card-header>\r\n    <nb-card-body class=\"px-4\" *ngIf=\"isHouseList\">\r\n      <div class=\"form-group d-flex\">\r\n        <label for=\"remark\" style=\"min-width:75px\" class=\"required-field mr-4\" baseLabel>檔案類型</label>\r\n        <nb-select placeholder=\"Select Status\" [(ngModel)]=\"saveSpecialNoticeFile.selectedCNoticeType\" class=\"w-full\"\r\n          [disabled]=\"!isNew\" (ngModelChange)=\"onStatusChange($event)\">\r\n          <nb-option *ngFor=\"let status of cNoticeTypeOptions\" [value]=\"status\">\r\n            {{ status.label }}\r\n          </nb-option>\r\n        </nb-select>\r\n      </div>\r\n      <div class=\"form-group d-flex align-items-baseline\">\r\n        <div class=\"d-flex flex-col mr-3\">\r\n          <label for=\"file\" class=\"required-field  mb-0\" style=\"min-width:75px\" baseLabel>上傳檔案\r\n          </label>\r\n          <span style=\"font-size: 10px; color:red;\">只接受pdf</span>\r\n        </div>\r\n        <div class=\"flex flex-col items-start space-y-4\">\r\n          <input type=\"file\" id=\"fileInput\" accept=\"image/jpeg, image/jpg, application/pdf\" class=\"hidden\"\r\n            style=\"display: none\" (change)=\"onFileSelected($event)\">\r\n          <label for=\"fileInput\"\r\n            class=\"cursor-pointer bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded\">\r\n            <i class=\"fa-solid fa-cloud-arrow-up mr-2\"></i> 上傳\r\n          </label>\r\n          <div class=\"flex items-center space-x-2\" *ngIf=\"fileName\">\r\n            <span class=\"text-gray-600\">{{ fileName }}</span>\r\n            <button type=\"button\" (click)=\"clearImage()\" class=\"text-red-500 hover:text-red-700\">\r\n              <i class=\"fa-solid fa-trash\"></i>\r\n            </button>\r\n          </div>\r\n        </div>\r\n        <span class=\"text-sm ml-4\" *ngIf=\"saveSpecialNoticeFile && saveSpecialNoticeFile?.CFileUrl\"\r\n          [ngClass]=\"imageUrl ? 'hidden':''\">\r\n          <a (click)=\"openPdfInNewTab(saveSpecialNoticeFile.CFileUrl)\" class=\"cursor-pointer text-blue-500\">\r\n            {{saveSpecialNoticeFile.CFileUrl}}\r\n          </a>\r\n        </span>\r\n      </div>\r\n      <div class=\"form-group d-flex mb-0\">\r\n        <label for=\"houseList2D\" baseLabel class=\"required-field mr-3\" style=\"min-width:75px\">適用戶別</label>\r\n      </div>\r\n      <div class=\"table-responsive mt-1\" *ngIf=\"isHouseList\">\r\n        <table class=\"table table-bordered\" style=\"background-color:#f3f3f3;\">\r\n          <thead>\r\n            <tr *ngIf=\"houseList2D.length\">\r\n              <th></th>\r\n              <th *ngFor=\"let house of houseList2D[0]; let idx = index;\">\r\n                <nb-checkbox status=\"basic\" (checkedChange)=\"enableAllAtIndex($event, idx)\"\r\n                  [checked]=\"isCheckAllColumnChecked(idx)\"\r\n                  [disabled]=\"saveSpecialNoticeFile.selectedCNoticeType.value == 1 ? true : false\">\r\n                  <span class=\"font-medium\">全選</span>\r\n                </nb-checkbox>\r\n              </th>\r\n            </tr>\r\n          </thead>\r\n          <tbody>\r\n            <tr *ngFor=\"let row of houseList2D\">\r\n              <td>\r\n                <nb-checkbox status=\"basic\" [checked]=\"isCheckAllRowChecked(row)\"\r\n                  (checkedChange)=\"enableAllRow($event,row)\"\r\n                  [disabled]=\"saveSpecialNoticeFile.selectedCNoticeType.value== 1 ? true : false\">\r\n                  <span class=\"font-medium\">全選</span>\r\n                </nb-checkbox>\r\n              </td>\r\n              <td *ngFor=\"let house of row\">\r\n                <!-- *ngIf=\"saveSpecialNoticeFile.selectedCNoticeType.value === house.CHouseType\" -->\r\n                <ng-container>\r\n                  <nb-checkbox status=\"basic\" [(checked)]=\"house.CIsSelect\"\r\n                    [disabled]=\"!house.CHouseHold || !house.CIsEnable || !(saveSpecialNoticeFile.selectedCNoticeType.value === house.CHouseType)||saveSpecialNoticeFile.CExamineStauts==0\">\r\n                    <span class=\"font-bold\" [ngClass]=\"{ '!text-red': house.CID }\"> {{ house.CHouseHold || 'null' }} -\r\n                      {{ house.CFloor }}</span>\r\n                  </nb-checkbox>\r\n                </ng-container>\r\n              </td>\r\n            </tr>\r\n          </tbody>\r\n        </table>\r\n      </div>\r\n      <div class=\"form-group d-flex align-items-center\">\r\n        <label for=\"status\" baseLabel class=\"required-field mr-3\" style=\"min-width:75px\">送審備註</label>\r\n        <textarea nbInput [(ngModel)]=\"saveSpecialNoticeFile.CExamineNote\" [rows]=\"4\"\r\n          class=\"resize-none !max-w-full w-full\"></textarea>\r\n      </div>\r\n\r\n      <div class=\"d-flex justify-content-center min-w-[90px]\">\r\n        <button class=\"btn btn-danger btn-sm mr-4\" (click)=\"onClose(ref)\">\r\n          取消\r\n        </button>\r\n        <button class=\"btn btn-success btn-sm min-w-[90px]\" (click)=\"onSaveSpecialNoticeFile(ref)\">\r\n          儲存</button>\r\n      </div>\r\n      <div class=\"w-full\" *ngIf=\"!isNew\">\r\n        <div class=\"form-group d-flex align-items-center\">\r\n          <label for=\"status\" baseLabel class=\"mr-3\" style=\"min-width:75px\">審核歷程</label>\r\n        </div>\r\n        <table class=\"table table-bordered\" style=\"background-color:#f3f3f3;\">\r\n          <thead>\r\n            <tr>\r\n              <th>時間</th>\r\n              <th>使用者</th>\r\n              <th>動作</th>\r\n              <th>說明</th>\r\n            </tr>\r\n          </thead>\r\n          <tbody>\r\n            <tr *ngFor=\"let row of saveSpecialNoticeFile.tblExamineLogs\">\r\n              <td>{{row.CCreateDt | dateFormatHour}}</td>\r\n              <td>{{row.CCreator}}</td>\r\n              <td>{{getActionName(row.CAction)}}</td>\r\n              <td>{{row.CExamineNote}}</td>\r\n            </tr>\r\n          </tbody>\r\n        </table>\r\n      </div>\r\n    </nb-card-body>\r\n  </nb-card>\r\n</ng-template>"], "mappings": "AACA,SAASA,YAAY,QAAkB,iBAAiB;AAQxD,SAASC,GAAG,QAAQ,MAAM;AAC1B,SAASC,YAAY,QAAQ,gCAAgC;AAC7D,SAASC,aAAa,QAAQ,qCAAqC;AAInE,SAASC,WAAW,QAAQ,8BAA8B;AAC1D,SAASC,kBAAkB,QAAQ,sBAAsB;;;;;;;;;;;;;;;;;;;;ICJ7CC,EAAA,CAAAC,cAAA,oBAAoE;IAClED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAFyCH,EAAA,CAAAI,UAAA,UAAAC,OAAA,CAAc;IACjEL,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAAO,kBAAA,MAAAF,OAAA,CAAAG,KAAA,MACF;;;;;;IAMFR,EAAA,CAAAC,cAAA,iBAAsF;IAA7CD,EAAA,CAAAS,UAAA,mBAAAC,qEAAA;MAAAV,EAAA,CAAAW,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAb,EAAA,CAAAc,aAAA;MAAA,MAAAC,SAAA,GAAAf,EAAA,CAAAgB,WAAA;MAAA,OAAAhB,EAAA,CAAAiB,WAAA,CAASJ,MAAA,CAAAK,SAAA,CAAAH,SAAA,CAAiB;IAAA,EAAC;IAClEf,EAAA,CAAAE,MAAA,iCAAK;IAAAF,EAAA,CAAAmB,SAAA,YAA2B;IAClCnB,EAAA,CAAAG,YAAA,EAAS;;;;;;IA4BHH,EAAA,CAAAC,cAAA,iBACoC;IAAlCD,EAAA,CAAAS,UAAA,mBAAAW,2FAAA;MAAApB,EAAA,CAAAW,aAAA,CAAAU,GAAA;MAAA,MAAAC,OAAA,GAAAtB,EAAA,CAAAc,aAAA,GAAAS,SAAA;MAAA,MAAAV,MAAA,GAAAb,EAAA,CAAAc,aAAA;MAAA,MAAAC,SAAA,GAAAf,EAAA,CAAAgB,WAAA;MAAA,OAAAhB,EAAA,CAAAiB,WAAA,CAASJ,MAAA,CAAAK,SAAA,CAAAH,SAAA,EAAAO,OAAA,CAAuB;IAAA,EAAC;IACjCtB,EAAA,CAAAE,MAAA,qBACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;IACTH,EAAA,CAAAC,cAAA,iBAAgG;IAAzBD,EAAA,CAAAS,UAAA,mBAAAe,2FAAA;MAAAxB,EAAA,CAAAW,aAAA,CAAAc,IAAA;MAAA,MAAAH,OAAA,GAAAtB,EAAA,CAAAc,aAAA,GAAAS,SAAA;MAAA,MAAAV,MAAA,GAAAb,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAiB,WAAA,CAASJ,MAAA,CAAAa,QAAA,CAAAJ,OAAA,CAAc;IAAA,EAAC;IAC7FtB,EAAA,CAAAE,MAAA,qBACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;IAbTH,EAFJ,CAAAC,cAAA,aAAkG,SAC5F,YAC6E;IAAvCD,EAAA,CAAAS,UAAA,mBAAAkB,4EAAA;MAAA,MAAAL,OAAA,GAAAtB,EAAA,CAAAW,aAAA,CAAAiB,GAAA,EAAAL,SAAA;MAAA,MAAAV,MAAA,GAAAb,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAiB,WAAA,CAASJ,MAAA,CAAAgB,eAAA,CAAAP,OAAA,kBAAAA,OAAA,CAAAQ,KAAA,CAA4B;IAAA,EAAC;IAAC9B,EAAA,CAAAE,MAAA,GAAoB;IACrGF,EADqG,CAAAG,YAAA,EAAI,EACpG;IACLH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,GAA+B;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACxCH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,GAC+C;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACxDH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,GAAqD;;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC9DH,EAAA,CAAAC,cAAA,UAAI;IAKFD,EAJA,CAAA+B,UAAA,KAAAC,kEAAA,qBACoC,KAAAC,kEAAA,qBAG4D;IAIpGjC,EADE,CAAAG,YAAA,EAAK,EACF;;;;;IAf8EH,EAAA,CAAAM,SAAA,GAAoB;IAApBN,EAAA,CAAAkC,iBAAA,CAAAZ,OAAA,kBAAAA,OAAA,CAAAa,SAAA,CAAoB;IAEjGnC,EAAA,CAAAM,SAAA,GAA+B;IAA/BN,EAAA,CAAAkC,iBAAA,CAAAZ,OAAA,CAAAc,UAAA,kBAAAd,OAAA,CAAAc,UAAA,CAAAC,IAAA,WAA+B;IAC/BrC,EAAA,CAAAM,SAAA,GAC+C;IAD/CN,EAAA,CAAAkC,iBAAA,CAAAZ,OAAA,CAAAgB,cAAA,YAAAtC,EAAA,CAAAuC,eAAA,IAAAC,GAAA,EAAAC,QAAA,CAAAnB,OAAA,CAAAgB,cAAA,IAAAzB,MAAA,CAAA6B,oBAAA,CAAApB,OAAA,CAAAgB,cAAA,OAC+C;IAC/CtC,EAAA,CAAAM,SAAA,GAAqD;IAArDN,EAAA,CAAAkC,iBAAA,CAAAlC,EAAA,CAAA2C,WAAA,QAAArB,OAAA,kBAAAA,OAAA,CAAAsB,YAAA,yBAAqD;IAEW5C,EAAA,CAAAM,SAAA,GAAc;IAAdN,EAAA,CAAAI,UAAA,SAAAS,MAAA,CAAAgC,QAAA,CAAc;IAIzB7C,EAAA,CAAAM,SAAA,EAAc;IAAdN,EAAA,CAAAI,UAAA,SAAAS,MAAA,CAAAiC,QAAA,CAAc;;;;;;IAqCrE9C,EAAA,CAAAC,cAAA,iBACmB;IAD8CD,EAAA,CAAAS,UAAA,mBAAAsC,2FAAA;MAAA/C,EAAA,CAAAW,aAAA,CAAAqC,IAAA;MAAA,MAAAC,QAAA,GAAAjD,EAAA,CAAAc,aAAA,GAAAS,SAAA;MAAA,MAAAV,MAAA,GAAAb,EAAA,CAAAc,aAAA;MAAA,MAAAC,SAAA,GAAAf,EAAA,CAAAgB,WAAA;MAAA,OAAAhB,EAAA,CAAAiB,WAAA,CAASJ,MAAA,CAAAK,SAAA,CAAAH,SAAA,EAAAkC,QAAA,CAAuB;IAAA,EAAC;IAEhGjD,EAAA,CAAAE,MAAA,qBACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;IACTH,EAAA,CAAAC,cAAA,iBAAgG;IAAzBD,EAAA,CAAAS,UAAA,mBAAAyC,2FAAA;MAAAlD,EAAA,CAAAW,aAAA,CAAAwC,IAAA;MAAA,MAAAF,QAAA,GAAAjD,EAAA,CAAAc,aAAA,GAAAS,SAAA;MAAA,MAAAV,MAAA,GAAAb,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAiB,WAAA,CAASJ,MAAA,CAAAa,QAAA,CAAAuB,QAAA,CAAc;IAAA,EAAC;IAC7FjD,EAAA,CAAAE,MAAA,qBACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;IAfTH,EAFJ,CAAAC,cAAA,aAA8F,SACxF,YAC6E;IAAvCD,EAAA,CAAAS,UAAA,mBAAA2C,4EAAA;MAAA,MAAAH,QAAA,GAAAjD,EAAA,CAAAW,aAAA,CAAA0C,IAAA,EAAA9B,SAAA;MAAA,MAAAV,MAAA,GAAAb,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAiB,WAAA,CAASJ,MAAA,CAAAgB,eAAA,CAAAoB,QAAA,kBAAAA,QAAA,CAAAnB,KAAA,CAA4B;IAAA,EAAC;IAAC9B,EAAA,CAAAE,MAAA,GAAoB;IACrGF,EADqG,CAAAG,YAAA,EAAI,EACpG;IACLH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,GAA+B;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACxCH,EAAA,CAAAC,cAAA,SAAI;IACFD,EAAA,CAAAE,MAAA,GAEF;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,GAAoD;;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC7DH,EAAA,CAAAC,cAAA,UAAI;IAKFD,EAJA,CAAA+B,UAAA,KAAAuB,kEAAA,qBACmB,KAAAC,kEAAA,qBAG6E;IAIpGvD,EADE,CAAAG,YAAA,EAAK,EACF;;;;;IAjB8EH,EAAA,CAAAM,SAAA,GAAoB;IAApBN,EAAA,CAAAkC,iBAAA,CAAAe,QAAA,kBAAAA,QAAA,CAAAd,SAAA,CAAoB;IAEjGnC,EAAA,CAAAM,SAAA,GAA+B;IAA/BN,EAAA,CAAAkC,iBAAA,CAAAe,QAAA,CAAAb,UAAA,kBAAAa,QAAA,CAAAb,UAAA,CAAAC,IAAA,WAA+B;IAEjCrC,EAAA,CAAAM,SAAA,GAEF;IAFEN,EAAA,CAAAO,kBAAA,MAAA0C,QAAA,CAAAX,cAAA,YAAAtC,EAAA,CAAAuC,eAAA,IAAAC,GAAA,EAAAC,QAAA,CAAAQ,QAAA,CAAAX,cAAA,IAAAzB,MAAA,CAAA6B,oBAAA,CAAAO,QAAA,CAAAX,cAAA,YAEF;IACItC,EAAA,CAAAM,SAAA,GAAoD;IAApDN,EAAA,CAAAkC,iBAAA,CAAAlC,EAAA,CAAA2C,WAAA,QAAAM,QAAA,kBAAAA,QAAA,CAAAL,YAAA,yBAAoD;IAGnD5C,EAAA,CAAAM,SAAA,GAAc;IAAdN,EAAA,CAAAI,UAAA,SAAAS,MAAA,CAAAgC,QAAA,CAAc;IAGsC7C,EAAA,CAAAM,SAAA,EAAc;IAAdN,EAAA,CAAAI,UAAA,SAAAS,MAAA,CAAAiC,QAAA,CAAc;;;;;;IApEjF9C,EAAA,CAAAwD,uBAAA,GAA4C;IAExCxD,EADF,CAAAC,cAAA,cAAmC,aACH;IAAAD,EAAA,CAAAE,MAAA,0BAAI;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAIjCH,EAHN,CAAAC,cAAA,gBAAiF,YACxE,aACoE,aACxC;IAAAD,EAAA,CAAAE,MAAA,+BAAI;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACxCH,EAAA,CAAAC,cAAA,aAA8B;IAAAD,EAAA,CAAAE,MAAA,iCAAK;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACxCH,EAAA,CAAAC,cAAA,cAA8B;IAAAD,EAAA,CAAAE,MAAA,gCAAI;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACvCH,EAAA,CAAAC,cAAA,cAA8B;IAAAD,EAAA,CAAAE,MAAA,gCAAI;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACvCH,EAAA,CAAAC,cAAA,cAA8B;IAAAD,EAAA,CAAAE,MAAA,oBAAE;IAEpCF,EAFoC,CAAAG,YAAA,EAAK,EAClC,EACC;IACRH,EAAA,CAAAC,cAAA,aAAO;IACLD,EAAA,CAAA+B,UAAA,KAAA0B,wDAAA,mBAAkG;IAoBxGzD,EAFI,CAAAG,YAAA,EAAQ,EACF,EACJ;IAEJH,EADF,CAAAC,cAAA,eAAsC,0BAEyB;IAD7CD,EAAA,CAAA0D,gBAAA,wBAAAC,yFAAAC,MAAA;MAAA5D,EAAA,CAAAW,aAAA,CAAAkD,GAAA;MAAA,MAAAhD,MAAA,GAAAb,EAAA,CAAAc,aAAA;MAAAd,EAAA,CAAA8D,kBAAA,CAAAjD,MAAA,CAAAkD,SAAA,EAAAH,MAAA,MAAA/C,MAAA,CAAAkD,SAAA,GAAAH,MAAA;MAAA,OAAA5D,EAAA,CAAAiB,WAAA,CAAA2C,MAAA;IAAA,EAAoB;IAClC5D,EAAA,CAAAS,UAAA,wBAAAkD,yFAAAC,MAAA;MAAA5D,EAAA,CAAAW,aAAA,CAAAkD,GAAA;MAAA,MAAAhD,MAAA,GAAAb,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAiB,WAAA,CAAcJ,MAAA,CAAAmD,WAAA,CAAAJ,MAAA,CAAmB;IAAA,EAAC;IAEtC5D,EADE,CAAAG,YAAA,EAAiB,EACb;IAEJH,EADF,CAAAC,cAAA,eAAmC,cACH;IAAAD,EAAA,CAAAE,MAAA,0BAAG;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAIhCH,EAHN,CAAAC,cAAA,iBAAiF,aACxE,cACoE,cACxC;IAAAD,EAAA,CAAAE,MAAA,gCAAI;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACxCH,EAAA,CAAAC,cAAA,cAA8B;IAAAD,EAAA,CAAAE,MAAA,iCAAK;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACxCH,EAAA,CAAAC,cAAA,cAA8B;IAAAD,EAAA,CAAAE,MAAA,gCAAI;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACvCH,EAAA,CAAAC,cAAA,cAA8B;IAAAD,EAAA,CAAAE,MAAA,gCAAI;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACvCH,EAAA,CAAAC,cAAA,cAA8B;IAAAD,EAAA,CAAAE,MAAA,oBAAE;IAEpCF,EAFoC,CAAAG,YAAA,EAAK,EAClC,EACC;IACRH,EAAA,CAAAC,cAAA,aAAO;IACLD,EAAA,CAAA+B,UAAA,KAAAkC,wDAAA,mBAA8F;IAsBpGjE,EAFI,CAAAG,YAAA,EAAQ,EACF,EACJ;IAEJH,EADF,CAAAC,cAAA,eAAsC,0BAE8B;IADlDD,EAAA,CAAA0D,gBAAA,wBAAAQ,yFAAAN,MAAA;MAAA5D,EAAA,CAAAW,aAAA,CAAAkD,GAAA;MAAA,MAAAhD,MAAA,GAAAb,EAAA,CAAAc,aAAA;MAAAd,EAAA,CAAA8D,kBAAA,CAAAjD,MAAA,CAAAsD,cAAA,EAAAP,MAAA,MAAA/C,MAAA,CAAAsD,cAAA,GAAAP,MAAA;MAAA,OAAA5D,EAAA,CAAAiB,WAAA,CAAA2C,MAAA;IAAA,EAAyB;IACvC5D,EAAA,CAAAS,UAAA,wBAAAyD,yFAAAN,MAAA;MAAA5D,EAAA,CAAAW,aAAA,CAAAkD,GAAA;MAAA,MAAAhD,MAAA,GAAAb,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAiB,WAAA,CAAcJ,MAAA,CAAAuD,gBAAA,CAAAR,MAAA,CAAwB;IAAA,EAAC;IAE3C5D,EADE,CAAAG,YAAA,EAAiB,EACb;;;;;IAlEqBH,EAAA,CAAAM,SAAA,IAA0C;IAA1CN,EAAA,CAAAI,UAAA,YAAAS,MAAA,CAAAwD,qBAAA,kBAAAxD,MAAA,CAAAwD,qBAAA,CAAAC,aAAA,CAA0C;IAsBnDtE,EAAA,CAAAM,SAAA,GAAoB;IAApBN,EAAA,CAAAuE,gBAAA,SAAA1D,MAAA,CAAAkD,SAAA,CAAoB;IAAuB/D,EAAtB,CAAAI,UAAA,aAAAS,MAAA,CAAA2D,QAAA,CAAqB,mBAAA3D,MAAA,CAAA4D,YAAA,CAAgC;IAiBjEzE,EAAA,CAAAM,SAAA,IAAsC;IAAtCN,EAAA,CAAAI,UAAA,YAAAS,MAAA,CAAAwD,qBAAA,kBAAAxD,MAAA,CAAAwD,qBAAA,CAAAK,SAAA,CAAsC;IAwB/C1E,EAAA,CAAAM,SAAA,GAAyB;IAAzBN,EAAA,CAAAuE,gBAAA,SAAA1D,MAAA,CAAAsD,cAAA,CAAyB;IAA4BnE,EAA3B,CAAAI,UAAA,aAAAS,MAAA,CAAA8D,aAAA,CAA0B,mBAAA9D,MAAA,CAAA+D,iBAAA,CAAqC;;;;;IAsBvG5E,EAAA,CAAAC,cAAA,oBAAsE;IACpED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAFyCH,EAAA,CAAAI,UAAA,UAAAyE,UAAA,CAAgB;IACnE7E,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAAO,kBAAA,MAAAsE,UAAA,CAAArE,KAAA,MACF;;;;;;IAiBER,EADF,CAAAC,cAAA,cAA0D,eAC5B;IAAAD,EAAA,CAAAE,MAAA,GAAc;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACjDH,EAAA,CAAAC,cAAA,iBAAqF;IAA/DD,EAAA,CAAAS,UAAA,mBAAAqE,gGAAA;MAAA9E,EAAA,CAAAW,aAAA,CAAAoE,IAAA;MAAA,MAAAlE,MAAA,GAAAb,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAiB,WAAA,CAASJ,MAAA,CAAAmE,UAAA,EAAY;IAAA,EAAC;IAC1ChF,EAAA,CAAAmB,SAAA,YAAiC;IAErCnB,EADE,CAAAG,YAAA,EAAS,EACL;;;;IAJwBH,EAAA,CAAAM,SAAA,GAAc;IAAdN,EAAA,CAAAkC,iBAAA,CAAArB,MAAA,CAAAoE,QAAA,CAAc;;;;;;IAQ5CjF,EAFF,CAAAC,cAAA,eACqC,YAC+D;IAA/FD,EAAA,CAAAS,UAAA,mBAAAyE,4FAAA;MAAAlF,EAAA,CAAAW,aAAA,CAAAwE,IAAA;MAAA,MAAAtE,MAAA,GAAAb,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAiB,WAAA,CAASJ,MAAA,CAAAgB,eAAA,CAAAhB,MAAA,CAAAuE,qBAAA,CAAAC,QAAA,CAA+C;IAAA,EAAC;IAC1DrF,EAAA,CAAAE,MAAA,GACF;IACFF,EADE,CAAAG,YAAA,EAAI,EACC;;;;IAJLH,EAAA,CAAAI,UAAA,YAAAS,MAAA,CAAAyE,QAAA,iBAAkC;IAEhCtF,EAAA,CAAAM,SAAA,GACF;IADEN,EAAA,CAAAO,kBAAA,MAAAM,MAAA,CAAAuE,qBAAA,CAAAC,QAAA,MACF;;;;;;IAYMrF,EADF,CAAAC,cAAA,SAA2D,sBAG0B;IAFvDD,EAAA,CAAAS,UAAA,2BAAA8E,uHAAA3B,MAAA;MAAA,MAAA4B,OAAA,GAAAxF,EAAA,CAAAW,aAAA,CAAA8E,IAAA,EAAAC,KAAA;MAAA,MAAA7E,MAAA,GAAAb,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAiB,WAAA,CAAiBJ,MAAA,CAAA8E,gBAAA,CAAA/B,MAAA,EAAA4B,OAAA,CAA6B;IAAA,EAAC;IAGzExF,EAAA,CAAAC,cAAA,eAA0B;IAAAD,EAAA,CAAAE,MAAA,mBAAE;IAEhCF,EAFgC,CAAAG,YAAA,EAAO,EACvB,EACX;;;;;IAJDH,EAAA,CAAAM,SAAA,EAAwC;IACxCN,EADA,CAAAI,UAAA,YAAAS,MAAA,CAAA+E,uBAAA,CAAAJ,OAAA,EAAwC,aAAA3E,MAAA,CAAAuE,qBAAA,CAAAS,mBAAA,CAAAC,KAAA,qBACwC;;;;;IALtF9F,EAAA,CAAAC,cAAA,SAA+B;IAC7BD,EAAA,CAAAmB,SAAA,SAAS;IACTnB,EAAA,CAAA+B,UAAA,IAAAgE,iFAAA,iBAA2D;IAO7D/F,EAAA,CAAAG,YAAA,EAAK;;;;IAPmBH,EAAA,CAAAM,SAAA,GAAmB;IAAnBN,EAAA,CAAAI,UAAA,YAAAS,MAAA,CAAAmF,WAAA,IAAmB;;;;;;IAkBzChG,EAAA,CAAAC,cAAA,SAA8B;IAE5BD,EAAA,CAAAwD,uBAAA,GAAc;IACZxD,EAAA,CAAAC,cAAA,sBACyK;IAD7ID,EAAA,CAAA0D,gBAAA,2BAAAuC,uHAAArC,MAAA;MAAA,MAAAsC,SAAA,GAAAlG,EAAA,CAAAW,aAAA,CAAAwF,IAAA,EAAA5E,SAAA;MAAAvB,EAAA,CAAA8D,kBAAA,CAAAoC,SAAA,CAAAE,SAAA,EAAAxC,MAAA,MAAAsC,SAAA,CAAAE,SAAA,GAAAxC,MAAA;MAAA,OAAA5D,EAAA,CAAAiB,WAAA,CAAA2C,MAAA;IAAA,EAA6B;IAEvD5D,EAAA,CAAAC,cAAA,eAA+D;IAACD,EAAA,CAAAE,MAAA,GAC5C;IACtBF,EADsB,CAAAG,YAAA,EAAO,EACf;;IAElBH,EAAA,CAAAG,YAAA,EAAK;;;;;IAN2BH,EAAA,CAAAM,SAAA,GAA6B;IAA7BN,EAAA,CAAAuE,gBAAA,YAAA2B,SAAA,CAAAE,SAAA,CAA6B;IACvDpG,EAAA,CAAAI,UAAA,cAAA8F,SAAA,CAAA9D,UAAA,KAAA8D,SAAA,CAAAG,SAAA,MAAAxF,MAAA,CAAAuE,qBAAA,CAAAS,mBAAA,CAAAC,KAAA,KAAAI,SAAA,CAAAI,UAAA,KAAAzF,MAAA,CAAAuE,qBAAA,CAAAmB,cAAA,MAAsK;IAC9IvG,EAAA,CAAAM,SAAA,EAAsC;IAAtCN,EAAA,CAAAI,UAAA,YAAAJ,EAAA,CAAAwG,eAAA,IAAAC,GAAA,EAAAP,SAAA,CAAAQ,GAAA,EAAsC;IAAE1G,EAAA,CAAAM,SAAA,EAC5C;IAD4CN,EAAA,CAAA2G,kBAAA,MAAAT,SAAA,CAAA9D,UAAA,mBAAA8D,SAAA,CAAAU,MAAA,KAC5C;;;;;;IAZxB5G,EAFJ,CAAAC,cAAA,SAAoC,SAC9B,sBAGgF;IADhFD,EAAA,CAAAS,UAAA,2BAAAoG,kHAAAjD,MAAA;MAAA,MAAAkD,OAAA,GAAA9G,EAAA,CAAAW,aAAA,CAAAoG,IAAA,EAAAxF,SAAA;MAAA,MAAAV,MAAA,GAAAb,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAiB,WAAA,CAAiBJ,MAAA,CAAAmG,YAAA,CAAApD,MAAA,EAAAkD,OAAA,CAAwB;IAAA,EAAC;IAE1C9G,EAAA,CAAAC,cAAA,eAA0B;IAAAD,EAAA,CAAAE,MAAA,mBAAE;IAEhCF,EAFgC,CAAAG,YAAA,EAAO,EACvB,EACX;IACLH,EAAA,CAAA+B,UAAA,IAAAkF,iFAAA,iBAA8B;IAUhCjH,EAAA,CAAAG,YAAA,EAAK;;;;;IAhB2BH,EAAA,CAAAM,SAAA,GAAqC;IAE/DN,EAF0B,CAAAI,UAAA,YAAAS,MAAA,CAAAqG,oBAAA,CAAAJ,OAAA,EAAqC,aAAAjG,MAAA,CAAAuE,qBAAA,CAAAS,mBAAA,CAAAC,KAAA,qBAEgB;IAI7D9F,EAAA,CAAAM,SAAA,GAAM;IAANN,EAAA,CAAAI,UAAA,YAAA0G,OAAA,CAAM;;;;;IArBhC9G,EAFJ,CAAAC,cAAA,cAAuD,gBACiB,YAC7D;IACLD,EAAA,CAAA+B,UAAA,IAAAoF,4EAAA,iBAA+B;IAUjCnH,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,YAAO;IACLD,EAAA,CAAA+B,UAAA,IAAAqF,4EAAA,iBAAoC;IAqB1CpH,EAFI,CAAAG,YAAA,EAAQ,EACF,EACJ;;;;IAjCKH,EAAA,CAAAM,SAAA,GAAwB;IAAxBN,EAAA,CAAAI,UAAA,SAAAS,MAAA,CAAAmF,WAAA,CAAAqB,MAAA,CAAwB;IAYTrH,EAAA,CAAAM,SAAA,GAAc;IAAdN,EAAA,CAAAI,UAAA,YAAAS,MAAA,CAAAmF,WAAA,CAAc;;;;;IAkDhChG,EADF,CAAAC,cAAA,SAA6D,SACvD;IAAAD,EAAA,CAAAE,MAAA,GAAkC;;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC3CH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,GAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACzBH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,GAA8B;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACvCH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,GAAoB;IAC1BF,EAD0B,CAAAG,YAAA,EAAK,EAC1B;;;;;IAJCH,EAAA,CAAAM,SAAA,GAAkC;IAAlCN,EAAA,CAAAkC,iBAAA,CAAAlC,EAAA,CAAAsH,WAAA,OAAAC,OAAA,CAAAC,SAAA,EAAkC;IAClCxH,EAAA,CAAAM,SAAA,GAAgB;IAAhBN,EAAA,CAAAkC,iBAAA,CAAAqF,OAAA,CAAAE,QAAA,CAAgB;IAChBzH,EAAA,CAAAM,SAAA,GAA8B;IAA9BN,EAAA,CAAAkC,iBAAA,CAAArB,MAAA,CAAA6G,aAAA,CAAAH,OAAA,CAAAI,OAAA,EAA8B;IAC9B3H,EAAA,CAAAM,SAAA,GAAoB;IAApBN,EAAA,CAAAkC,iBAAA,CAAAqF,OAAA,CAAAK,YAAA,CAAoB;;;;;IAhB5B5H,EAFJ,CAAAC,cAAA,cAAmC,cACiB,gBACkB;IAAAD,EAAA,CAAAE,MAAA,+BAAI;IACxEF,EADwE,CAAAG,YAAA,EAAQ,EAC1E;IAIAH,EAHN,CAAAC,cAAA,gBAAsE,YAC7D,SACD,SACE;IAAAD,EAAA,CAAAE,MAAA,mBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACXH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,0BAAG;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACZH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAE,MAAA,oBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACXH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAE,MAAA,oBAAE;IAEVF,EAFU,CAAAG,YAAA,EAAK,EACR,EACC;IACRH,EAAA,CAAAC,cAAA,aAAO;IACLD,EAAA,CAAA+B,UAAA,KAAA8F,6EAAA,kBAA6D;IAQnE7H,EAFI,CAAAG,YAAA,EAAQ,EACF,EACJ;;;;IARoBH,EAAA,CAAAM,SAAA,IAAuC;IAAvCN,EAAA,CAAAI,UAAA,YAAAS,MAAA,CAAAuE,qBAAA,CAAA0C,cAAA,CAAuC;;;;;;IAtG/D9H,EAFJ,CAAAC,cAAA,uBAA+C,cACd,gBACoD;IAAAD,EAAA,CAAAE,MAAA,+BAAI;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAC7FH,EAAA,CAAAC,cAAA,oBAC+D;IADxBD,EAAA,CAAA0D,gBAAA,2BAAAqE,oGAAAnE,MAAA;MAAA5D,EAAA,CAAAW,aAAA,CAAAqH,IAAA;MAAA,MAAAnH,MAAA,GAAAb,EAAA,CAAAc,aAAA;MAAAd,EAAA,CAAA8D,kBAAA,CAAAjD,MAAA,CAAAuE,qBAAA,CAAAS,mBAAA,EAAAjC,MAAA,MAAA/C,MAAA,CAAAuE,qBAAA,CAAAS,mBAAA,GAAAjC,MAAA;MAAA,OAAA5D,EAAA,CAAAiB,WAAA,CAAA2C,MAAA;IAAA,EAAuD;IACxE5D,EAAA,CAAAS,UAAA,2BAAAsH,oGAAAnE,MAAA;MAAA5D,EAAA,CAAAW,aAAA,CAAAqH,IAAA;MAAA,MAAAnH,MAAA,GAAAb,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAiB,WAAA,CAAiBJ,MAAA,CAAAoH,cAAA,CAAArE,MAAA,CAAsB;IAAA,EAAC;IAC5D5D,EAAA,CAAA+B,UAAA,IAAAmG,4EAAA,uBAAsE;IAI1ElI,EADE,CAAAG,YAAA,EAAY,EACR;IAGFH,EAFJ,CAAAC,cAAA,cAAoD,cAChB,gBACgD;IAAAD,EAAA,CAAAE,MAAA,gCAChF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,gBAA0C;IAAAD,EAAA,CAAAE,MAAA,6BAAM;IAClDF,EADkD,CAAAG,YAAA,EAAO,EACnD;IAEJH,EADF,CAAAC,cAAA,eAAiD,iBAEW;IAAlCD,EAAA,CAAAS,UAAA,oBAAA0H,0FAAAvE,MAAA;MAAA5D,EAAA,CAAAW,aAAA,CAAAqH,IAAA;MAAA,MAAAnH,MAAA,GAAAb,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAiB,WAAA,CAAUJ,MAAA,CAAAuH,cAAA,CAAAxE,MAAA,CAAsB;IAAA,EAAC;IADzD5D,EAAA,CAAAG,YAAA,EAC0D;IAC1DH,EAAA,CAAAC,cAAA,iBAC8F;IAC5FD,EAAA,CAAAmB,SAAA,aAA+C;IAACnB,EAAA,CAAAE,MAAA,sBAClD;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAA+B,UAAA,KAAAsG,uEAAA,kBAA0D;IAM5DrI,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAA+B,UAAA,KAAAuG,wEAAA,mBACqC;IAKvCtI,EAAA,CAAAG,YAAA,EAAM;IAEJH,EADF,CAAAC,cAAA,eAAoC,iBACoD;IAAAD,EAAA,CAAAE,MAAA,gCAAI;IAC5FF,EAD4F,CAAAG,YAAA,EAAQ,EAC9F;IACNH,EAAA,CAAA+B,UAAA,KAAAwG,uEAAA,kBAAuD;IAsCrDvI,EADF,CAAAC,cAAA,eAAkD,iBACiC;IAAAD,EAAA,CAAAE,MAAA,gCAAI;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAC7FH,EAAA,CAAAC,cAAA,oBACyC;IADvBD,EAAA,CAAA0D,gBAAA,2BAAA8E,oGAAA5E,MAAA;MAAA5D,EAAA,CAAAW,aAAA,CAAAqH,IAAA;MAAA,MAAAnH,MAAA,GAAAb,EAAA,CAAAc,aAAA;MAAAd,EAAA,CAAA8D,kBAAA,CAAAjD,MAAA,CAAAuE,qBAAA,CAAAwC,YAAA,EAAAhE,MAAA,MAAA/C,MAAA,CAAAuE,qBAAA,CAAAwC,YAAA,GAAAhE,MAAA;MAAA,OAAA5D,EAAA,CAAAiB,WAAA,CAAA2C,MAAA;IAAA,EAAgD;IAEpE5D,EAD2C,CAAAG,YAAA,EAAW,EAChD;IAGJH,EADF,CAAAC,cAAA,eAAwD,kBACY;IAAvBD,EAAA,CAAAS,UAAA,mBAAAgI,0FAAA;MAAAzI,EAAA,CAAAW,aAAA,CAAAqH,IAAA;MAAA,MAAAU,OAAA,GAAA1I,EAAA,CAAAc,aAAA,GAAA6H,SAAA;MAAA,MAAA9H,MAAA,GAAAb,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAiB,WAAA,CAASJ,MAAA,CAAA+H,OAAA,CAAAF,OAAA,CAAY;IAAA,EAAC;IAC/D1I,EAAA,CAAAE,MAAA,sBACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,kBAA2F;IAAvCD,EAAA,CAAAS,UAAA,mBAAAoI,0FAAA;MAAA7I,EAAA,CAAAW,aAAA,CAAAqH,IAAA;MAAA,MAAAU,OAAA,GAAA1I,EAAA,CAAAc,aAAA,GAAA6H,SAAA;MAAA,MAAA9H,MAAA,GAAAb,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAiB,WAAA,CAASJ,MAAA,CAAAiI,uBAAA,CAAAJ,OAAA,CAA4B;IAAA,EAAC;IACxF1I,EAAA,CAAAE,MAAA,qBAAE;IACNF,EADM,CAAAG,YAAA,EAAS,EACT;IACNH,EAAA,CAAA+B,UAAA,KAAAgH,uEAAA,mBAAmC;IAuBrC/I,EAAA,CAAAG,YAAA,EAAe;;;;IA9G4BH,EAAA,CAAAM,SAAA,GAAuD;IAAvDN,EAAA,CAAAuE,gBAAA,YAAA1D,MAAA,CAAAuE,qBAAA,CAAAS,mBAAA,CAAuD;IAC5F7F,EAAA,CAAAI,UAAA,cAAAS,MAAA,CAAAmI,KAAA,CAAmB;IACWhJ,EAAA,CAAAM,SAAA,EAAqB;IAArBN,EAAA,CAAAI,UAAA,YAAAS,MAAA,CAAAoI,kBAAA,CAAqB;IAkBTjJ,EAAA,CAAAM,SAAA,IAAc;IAAdN,EAAA,CAAAI,UAAA,SAAAS,MAAA,CAAAoE,QAAA,CAAc;IAO9BjF,EAAA,CAAAM,SAAA,EAA8D;IAA9DN,EAAA,CAAAI,UAAA,SAAAS,MAAA,CAAAuE,qBAAA,KAAAvE,MAAA,CAAAuE,qBAAA,kBAAAvE,MAAA,CAAAuE,qBAAA,CAAAC,QAAA,EAA8D;IAUxDrF,EAAA,CAAAM,SAAA,GAAiB;IAAjBN,EAAA,CAAAI,UAAA,SAAAS,MAAA,CAAAqI,WAAA,CAAiB;IAuCjClJ,EAAA,CAAAM,SAAA,GAAgD;IAAhDN,EAAA,CAAAuE,gBAAA,YAAA1D,MAAA,CAAAuE,qBAAA,CAAAwC,YAAA,CAAgD;IAAC5H,EAAA,CAAAI,UAAA,WAAU;IAW1DJ,EAAA,CAAAM,SAAA,GAAY;IAAZN,EAAA,CAAAI,UAAA,UAAAS,MAAA,CAAAmI,KAAA,CAAY;;;;;IA5FnChJ,EADF,CAAAC,cAAA,kBAA+C,qBAC7B;IAACD,EAAA,CAAAE,MAAA,GACjB;IAAAF,EAAA,CAAAG,YAAA,EAAiB;IACjBH,EAAA,CAAA+B,UAAA,IAAAoH,gEAAA,4BAA+C;IAkHjDnJ,EAAA,CAAAG,YAAA,EAAU;;;;IApHSH,EAAA,CAAAM,SAAA,GACjB;IADiBN,EAAA,CAAAO,kBAAA,MAAAM,MAAA,CAAAmI,KAAA,uEACjB;IAC4BhJ,EAAA,CAAAM,SAAA,EAAiB;IAAjBN,EAAA,CAAAI,UAAA,SAAAS,MAAA,CAAAqI,WAAA,CAAiB;;;ADtEjD,OAAM,MAAOE,yBAA0B,SAAQvJ,aAAa;EAC1DwJ,YACUC,MAAmB,EACnBC,aAA8B,EAC9BC,OAAuB,EACvBC,KAAuB,EACvBC,eAA+B,EAC/BC,iBAAmC,EACnCC,yBAAmD,EACnDC,yBAAmD,EACnDC,aAA2B,EAC3BC,2BAAgD;IACtD,KAAK,CAACT,MAAM,CAAC;IAVP,KAAAA,MAAM,GAANA,MAAM;IACN,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,yBAAyB,GAAzBA,yBAAyB;IACzB,KAAAC,yBAAyB,GAAzBA,yBAAyB;IACzB,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,2BAA2B,GAA3BA,2BAA2B;IAS5B,KAAAC,SAAS,GAAG,CAAC;IACb,KAAAxF,QAAQ,GAAG,EAAE;IACb,KAAAT,SAAS,GAAG,CAAC;IACb,KAAAU,YAAY,GAAG,CAAC;IACzB,KAAAwF,cAAc,GAAG,CAAC;IAClB,KAAAtF,aAAa,GAAG,EAAE;IAClB,KAAAR,cAAc,GAAG,CAAC;IAClB,KAAAS,iBAAiB,GAAG,CAAC;IAKrB,KAAAsF,gBAAgB,GAAU,CAAC;MAAE1J,KAAK,EAAE,IAAI;MAAEsF,KAAK,EAAE;IAAE,CAAE,CAAC;IAEtD,KAAAmD,kBAAkB,GAAU,CAC1B;MAAEzI,KAAK,EAAE,KAAK;MAAEsF,KAAK,EAAE;IAAC,CAAE,EAC1B;MAAEtF,KAAK,EAAE,KAAK;MAAEsF,KAAK,EAAE;IAAC,CAAE,CAC3B;IAGD,KAAAqE,8BAA8B,GAAG;MAC/BC,SAAS,EAAE,CAAC;MACZC,WAAW,EAAE;KACd;IAYD;IACA,KAAAC,gBAAgB,GAAqB;MACnCC,aAAa,EAAE,CAAC,iBAAiB,CAAC;MAClCC,iBAAiB,EAAE,MAAM;MACzBC,eAAe,EAAE,iBAAiB;MAClCjK,KAAK,EAAE,MAAM;MACbkK,QAAQ,EAAE,WAAW;MACrBC,QAAQ,EAAE,KAAK;MACfC,QAAQ,EAAE,KAAK;MACfC,YAAY,EAAE,KAAK;MACnBC,UAAU,EAAE,IAAI;MAChBC,UAAU,EAAE,4BAA4B;MACxCC,WAAW,EAAE,EAAE;MACfC,QAAQ,EAAE,KAAK;MACfC,WAAW,EAAE;KACd;IAED,KAAAC,YAAY,GAA4B,IAAI;IAiE5C,KAAAjC,WAAW,GAAG,KAAK;IA4HnB,KAAAxG,oBAAoB,GAAG,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;IAoD5C,KAAAsG,KAAK,GAAG,IAAI;EA7SM;EAEToC,QAAQA,CAAA;IACf,IAAI,CAACC,gBAAgB,EAAE;EACzB;EAgCArH,WAAWA,CAACsH,OAAe;IACzB,IAAI,CAACvH,SAAS,GAAGuH,OAAO;EAC1B;EACAlH,gBAAgBA,CAACkH,OAAe;IAC9B,IAAI,CAACnH,cAAc,GAAGmH,OAAO;EAC/B;EAqBA;EACAC,YAAYA,CAACC,UAA4B;IACvC,IAAI,CAACL,YAAY,GAAGK,UAAU;EAChC;EACA;EACAC,WAAWA,CAAA;IACT,IAAI,CAACN,YAAY,GAAG,IAAI;EAC1B;EAIAO,iBAAiBA,CAAA;IACf,IAAI,IAAI,CAACC,kBAAkB,CAAC7F,KAAK,EAAE;MACjC,IAAI,CAAC8F,iCAAiC,EAAE;MACxC,IAAI,CAACC,wBAAwB,EAAE;IACjC;EACF;EAEAR,gBAAgBA,CAAA;IACd,IAAI,CAAC1B,iBAAiB,CAACmC,qCAAqC,CAAC;MAAEC,IAAI,EAAE;IAAE,CAAE,CAAC,CAACC,IAAI,CAC7ErM,GAAG,CAACsM,GAAG,IAAG;MACR,IAAIA,GAAG,CAACC,OAAO,IAAID,GAAG,CAACE,UAAU,IAAI,CAAC,EAAE;QACtC,IAAI,CAACC,oBAAoB,GAAGH,GAAG,CAACC,OAAO,CAACG,GAAG,CAACJ,GAAG,IAAG;UAChD,OAAO;YACLzL,KAAK,EAAEyL,GAAG,CAACK,cAAc;YACzBxG,KAAK,EAAEmG,GAAG,CAACM;WACZ;QACH,CAAC,CAAC;QACF,IAAI,CAACZ,kBAAkB,GAAG,IAAI,CAACS,oBAAoB,CAAC,CAAC,CAAC;QACtD,IAAI,IAAI,CAACT,kBAAkB,CAAC7F,KAAK,EAAE;UACjC,IAAI,CAAC8F,iCAAiC,EAAE;UACxC,IAAI,CAACC,wBAAwB,EAAE;QACjC;MACF;IACF,CAAC,CAAC,CACH,CAACW,SAAS,EAAE;EACf;EAEAC,YAAYA,CAACC,YAAyB;IAEpC,MAAMC,WAAW,GAAkB,EAAE;IACrC,MAAMC,YAAY,GAAGC,KAAK,CAACC,IAAI,CAAC,IAAIC,GAAG,CACrCL,YAAY,CAACL,GAAG,CAACW,QAAQ,IAAIA,QAAQ,CAACpG,MAAM,CAAC,CAACqG,MAAM,CAACC,KAAK,IAAIA,KAAK,KAAK,IAAI,CAAa,CAC1F,CAAC;IACF,KAAK,MAAMA,KAAK,IAAIN,YAAY,EAAE;MAChCD,WAAW,CAACQ,IAAI,CAAC,EAAE,CAAC;IACtB;IACA,KAAK,MAAMH,QAAQ,IAAIN,YAAY,EAAE;MACnC,MAAMU,UAAU,GAAGR,YAAY,CAACS,OAAO,CAACL,QAAQ,CAACpG,MAAgB,CAAC;MAClE,IAAIwG,UAAU,KAAK,CAAC,CAAC,EAAE;QACrBT,WAAW,CAACS,UAAU,CAAC,CAACD,IAAI,CAAC;UAC3B/G,SAAS,EAAE4G,QAAQ,EAAE5G,SAAS,IAAI,KAAK;UACvCkH,QAAQ,EAAEN,QAAQ,CAACtG,GAAG;UACtBJ,UAAU,EAAE0G,QAAQ,CAAC1G,UAAU;UAC/BM,MAAM,EAAEoG,QAAQ,CAACpG,MAAM;UACvBxE,UAAU,EAAE4K,QAAQ,CAAC5K,UAAU;UAC/BiE,SAAS,EAAE2G,QAAQ,CAAC3G;SACrB,CAAC;MACJ;IACF;IACA,OAAOsG,WAAW;EACpB;EAIAY,qBAAqBA,CAACC,GAAsB;IAC1C,OAAOA,GAAG,CAACC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK,CAACA,CAAC,CAAC/G,MAAM,IAAI,CAAC,KAAK8G,CAAC,CAAC9G,MAAM,IAAI,CAAC,CAAC,CAAC;EAC9D;EAEAgH,YAAYA,CAAA;IACV,IAAI,IAAI,CAACjC,kBAAkB,CAAC7F,KAAK,EAAE;MACjC,IAAI,CAACgE,aAAa,CAAC+D,6BAA6B,CAAC;QAC/C9B,IAAI,EAAE;UACJ+B,YAAY,EAAE,IAAI,CAACnC,kBAAkB,CAAC7F,KAAK;UAAEiI,OAAO,EAAE;;OAEzD,CAAC,CAACvB,SAAS,CAACP,GAAG,IAAG;QACjB,IAAIA,GAAG,CAACC,OAAO,IAAID,GAAG,CAACE,UAAU,IAAI,CAAC,EAAE;UACtC,MAAM6B,IAAI,GAAG,IAAI,CAACT,qBAAqB,CAACtB,GAAG,CAACC,OAAO,CAAC;UACpD,IAAI,CAAC+B,eAAe,GAAG,CAAC,GAAGD,IAAI,CAAC;UAChC,IAAI,IAAI,CAAC5I,qBAAqB,CAAC8I,oBAAoB,EAAE;YACnD,IAAI,CAAClI,WAAW,GAAG,IAAI,CAACyG,YAAY,CAAC,IAAI,CAAC0B,eAAe,CAAC,CAAC,GAAG,IAAI,CAACF,eAAe,CAAC,EAAE,IAAI,CAAC7I,qBAAqB,CAACgJ,0BAA0B,GAAG,CAAC,GAAG,IAAI,CAAChJ,qBAAqB,CAACgJ,0BAA0B,CAAC,GAAG,EAAE,CAAC,CAAC;UAChN,CAAC,MAAM;YACL,IAAI,CAACpI,WAAW,GAAG,IAAI,CAACyG,YAAY,CAAC,CAAC,GAAGuB,IAAI,CAAC,CAAC;UACjD;UACA,IAAI,CAAC9E,WAAW,GAAG,IAAI;UACvB,IAAI,IAAI,CAAC9D,qBAAqB,EAAE;YAC9B,IAAI,CAACY,WAAW,GAAG,IAAI,CAACqI,cAAc,CAAC,IAAI,CAACrI,WAAW,EAAE,IAAI,CAACZ,qBAAqB,CAACgJ,0BAA0B,CAAC;UACjH;QACF;MACF,CAAC,CAAC;IACJ;EACF;EAEAlH,oBAAoBA,CAACoH,GAAU;IAC7B,OAAOA,GAAG,CAACC,KAAK,CAAEC,IAAyB,IAAKA,IAAI,CAACpI,SAAS,CAAC;EACjE;EAEAR,uBAAuBA,CAACF,KAAa;IACnC,IAAI,IAAI,CAACwD,WAAW,EAAE;MACpB,IAAIxD,KAAK,GAAG,CAAC,IAAIA,KAAK,IAAI,IAAI,CAACM,WAAW,CAAC,CAAC,CAAC,CAACqB,MAAM,EAAE;QACpD,MAAM,IAAIoH,KAAK,CAAC,8DAA8D,CAAC;MACjF;MACA,KAAK,MAAMC,SAAS,IAAI,IAAI,CAAC1I,WAAW,EAAE;QACxC,IAAIN,KAAK,IAAIgJ,SAAS,CAACrH,MAAM,IAAI,CAACqH,SAAS,CAAChJ,KAAK,CAAC,CAACU,SAAS,EAAE;UAC5D,OAAO,KAAK,CAAC,CAAC;QAChB;MACF;MACA,OAAO,IAAI,CAAC,CAAC;IACf;IACA,OAAO,KAAK;EACd;EAEAT,gBAAgBA,CAACgJ,OAAgB,EAAEjJ,KAAa;IAC9C,IAAIA,KAAK,GAAG,CAAC,EAAE;MACb,MAAM,IAAI+I,KAAK,CAAC,qDAAqD,CAAC;IACxE;IACA,KAAK,MAAMC,SAAS,IAAI,IAAI,CAAC1I,WAAW,EAAE;MACxC,IAAIN,KAAK,GAAGgJ,SAAS,CAACrH,MAAM,IAAK,IAAI,CAACjC,qBAAqB,CAACS,mBAAmB,CAACC,KAAK,KAAK4I,SAAS,CAAChJ,KAAK,CAAC,CAACY,UAAW,EAAE;QAAE;QACxHoI,SAAS,CAAChJ,KAAK,CAAC,CAACU,SAAS,GAAGuI,OAAO;MACtC;IACF;EACF;EAEA3H,YAAYA,CAAC2H,OAAgB,EAAEL,GAAgB;IAC7C,KAAK,MAAME,IAAI,IAAIF,GAAG,EAAE;MACtB,IAAK,IAAI,CAAClJ,qBAAqB,CAACS,mBAAmB,CAACC,KAAK,KAAK0I,IAAI,CAAClI,UAAU,EAAG;QAAE;QAChFkI,IAAI,CAACpI,SAAS,GAAGuI,OAAO;MAC1B;IACF;EACF;EAKA9C,wBAAwBA,CAAA;IACtB,IAAI,CAACjC,yBAAyB,CAACgF,qDAAqD,CAAC;MACnF7C,IAAI,EAAE;QACJ8C,YAAY,EAAE,IAAI,CAAClD,kBAAkB,CAAC7F,KAAK;QAC3CgJ,iBAAiB,EAAE,IAAI,CAAC/K,SAAS;QACjCgL,cAAc,EAAE,IAAI,CAAC5K,cAAc;QACnC6K,gBAAgB,EAAE,IAAI,CAACxK,QAAQ;QAC/ByK,aAAa,EAAE,IAAI,CAACtK;;KAEvB,CAAC,CAAC6H,SAAS,CAACP,GAAG,IAAG;MACjB,IAAIA,GAAG,CAACC,OAAO,IAAID,GAAG,CAACE,UAAU,IAAI,CAAC,EAAE;QACtC,IAAI,CAAC9H,qBAAqB,GAAG4H,GAAG,CAACC,OAAO;QACxC,IAAI,CAACzH,YAAY,GAAGwH,GAAG,CAACC,OAAO,CAACgD,kBAAkB,IAAI,CAAC;QACvD,IAAI,CAACtK,iBAAiB,GAAGqH,GAAG,CAACC,OAAO,CAACiD,cAAc,IAAI,CAAC;MAC1D;IACF,CAAC,CAAC;EACJ;EAKAlH,cAAcA,CAACmH,SAAc,GAC7B;EAEAxD,iCAAiCA,CAAA;IAC/B,IAAI,CAAC/B,yBAAyB,CAACwF,8DAA8D,CAAC;MAC5FtD,IAAI,EAAE,IAAI,CAACJ,kBAAkB,CAAC7F;KAC/B,CAAC,CAAC0G,SAAS,CAACP,GAAG,IAAG;MACjB,IAAIA,GAAG,CAACC,OAAO,IAAID,GAAG,CAACE,UAAU,IAAI,CAAC,EAAE;QACtC,IAAI,CAACmD,8BAA8B,GAAGrD,GAAG,CAACC,OAAO;MACnD;IACF,CAAC,CAAC;EACJ;EAEAqD,0BAA0BA,CAAC7B,CAAW,EAAEC,CAA8H;IACpK,MAAM6B,CAAC,GAA+B,EAAE;IACxC,KAAK,MAAMhB,IAAI,IAAId,CAAC,EAAE;MACpB,MAAM+B,YAAY,GAAG9B,CAAC,CAAC+B,IAAI,CAACC,KAAK,IAAIA,KAAK,CAACC,UAAU,KAAKpB,IAAI,IAAImB,KAAK,CAACE,WAAW,CAAC;MACpFL,CAAC,CAAChB,IAAI,CAAC,GAAG,CAAC,CAACiB,YAAY;IAC1B;IACA,OAAOD,CAAC;EACV;EAGAM,cAAcA,CAAChK,KAAU,EAAEiK,OAAc;IACvC,KAAK,MAAMvB,IAAI,IAAIuB,OAAO,EAAE;MAC1B,IAAIvB,IAAI,CAAC1I,KAAK,KAAKA,KAAK,EAAE;QACxB,OAAO0I,IAAI;MACb;IACF;IACA,OAAO,IAAI;EACb;EAIAwB,eAAeA,CAAChK,WAA0B,EAAEiK,sBAAgD;IAC1F,MAAMC,cAAc,GAAG,IAAInD,GAAG,CAACkD,sBAAsB,CAAC5D,GAAG,CAACmC,IAAI,IAAI,GAAGA,IAAI,CAACpM,UAAU,IAAIoM,IAAI,CAAC5H,MAAM,EAAE,CAAC,CAAC;IACvG,OAAOZ,WAAW,CAACqG,GAAG,CAAC8D,UAAU,IAAG;MAClC,OAAOA,UAAU,CAAC9D,GAAG,CAACmC,IAAI,IAAG;QAC3B,MAAM4B,GAAG,GAAG,GAAG5B,IAAI,CAACpM,UAAU,IAAIoM,IAAI,CAAC5H,MAAM,EAAE;QAC/C,IAAIsJ,cAAc,CAACG,GAAG,CAACD,GAAG,CAAC,EAAE;UAC3B5B,IAAI,CAACpI,SAAS,GAAG,IAAI;QACvB,CAAC,MAAM;UACLoI,IAAI,CAACpI,SAAS,GAAG,KAAK;QACxB;QACA,OAAOoI,IAAI;MACb,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ;EAEAL,eAAeA,CAACmC,CAAQ,EAAEC,CAAQ;IAChC,MAAMC,IAAI,GAAG,IAAIC,GAAG,CAACF,CAAC,CAAClE,GAAG,CAACmC,IAAI,IAAI,CAAC,GAAGA,IAAI,CAACpM,UAAU,IAAIoM,IAAI,CAAC5H,MAAM,EAAE,EAAE4H,IAAI,CAACpI,SAAS,CAAC,CAAC,CAAC;IAC1F,OAAOkK,CAAC,CAACjE,GAAG,CAACmC,IAAI,IAAG;MAClB,MAAM4B,GAAG,GAAG,GAAG5B,IAAI,CAACpM,UAAU,IAAIoM,IAAI,CAAC5H,MAAM,EAAE;MAC/C,OAAO;QACL,GAAG4H,IAAI;QACPpI,SAAS,EAAEoK,IAAI,CAACH,GAAG,CAACD,GAAG,CAAC,GAAGI,IAAI,CAACE,GAAG,CAACN,GAAG,CAAC,GAAG;OAC5C;IACH,CAAC,CAAC;EACJ;EAGAO,wBAAwBA,CAACnC,IAAS,EAAEoC,GAAQ;IAC1C,IAAI,CAAChH,yBAAyB,CAACiH,qDAAqD,CAAC;MACnF9E,IAAI,EAAEyC,IAAI,CAACN;KACZ,CAAC,CAAC1B,SAAS,CAACP,GAAG,IAAG;MACjB,IAAIA,GAAG,CAACC,OAAO,IAAID,GAAG,CAACE,UAAU,IAAI,CAAC,EAAE;QACtC,MAAM2E,IAAI,GAAG7E,GAAG,CAACC,OAAO;QACxB,IAAI,CAAC9G,qBAAqB,GAAG;UAC3BC,QAAQ,EAAEyL,IAAI,CAACC,oBAAoB,EAAE1L,QAAQ,IAAI2L,SAAS;UAC1D3G,WAAW,EAAEyG,IAAI,CAACC,oBAAoB,EAAE1G,WAAW;UACnDwE,YAAY,EAAEiC,IAAI,CAACC,oBAAoB,EAAElC,YAAY;UACrDX,oBAAoB,EAAE4C,IAAI,CAACC,oBAAoB,EAAE7C,oBAAoB;UACrErI,mBAAmB,EAAEiL,IAAI,CAACC,oBAAoB,EAAE1G,WAAW,GAAG,IAAI,CAACyF,cAAc,CAACgB,IAAI,CAACC,oBAAoB,EAAE1G,WAAW,EAAE,IAAI,CAACpB,kBAAkB,CAAC,GAAG,IAAI,CAACA,kBAAkB,CAAC,CAAC,CAAC;UAC/KnB,cAAc,EAAEgJ,IAAI,CAAChJ,cAAc,GAAGgJ,IAAI,CAAChJ,cAAc,GAAGkJ,SAAS;UACrEpJ,YAAY,EAAEkJ,IAAI,CAAClJ,YAAY;UAC/BwG,0BAA0B,EAAE0C,IAAI,CAAC1C,0BAA0B,EAAEnB,MAAM,CAAEgE,CAAM,IAAKA,CAAC,CAAC7K,SAAS,CAAC;UAC5FG,cAAc,EAAEuK,IAAI,CAACvK;SACtB;QACD,IAAI,CAACqH,YAAY,EAAE;QACnB,IAAI,CAACrE,aAAa,CAAC2H,IAAI,CAACN,GAAG,CAAC;MAC9B;IACF,CAAC,CAAC;EACJ;EAIA/O,eAAeA,CAACwD,QAAc;IAC5B,IAAIA,QAAQ,EAAE8L,MAAM,CAACD,IAAI,CAACpR,WAAW,CAACsR,qBAAqB,GAAG/L,QAAQ,EAAE,QAAQ,CAAC;EACnF;EACAnE,SAASA,CAAC0P,GAAQ,EAAEpC,IAAU;IAC5B,IAAI,CAACtF,WAAW,GAAG,KAAK;IACxB,IAAI,CAACF,KAAK,GAAG,IAAI;IACjB,IAAI,CAACyC,WAAW,EAAE,EAAC;IACnB,IAAI,CAACrG,qBAAqB,GAAG;MAC3BiF,WAAW,EAAE,CAAC;MACdwE,YAAY,EAAEmC,SAAS;MACvBlP,KAAK,EAAEkP,SAAS;MAChBK,MAAM,EAAE,EAAE;MACVnD,oBAAoB,EAAE8C,SAAS;MAC/BM,YAAY,EAAE,KAAK;MACnBzL,mBAAmB,EAAE,IAAI,CAACoD,kBAAkB,CAAC,CAAC,CAAC;MAC/CrB,YAAY,EAAE,EAAE;MAChBwG,0BAA0B,EAAE4C;KAC7B;IAED,IAAIxC,IAAI,EAAE;MACR,IAAI,CAACxF,KAAK,GAAG,KAAK;MAClB,IAAI,CAAC2H,wBAAwB,CAACnC,IAAI,EAAEoC,GAAG,CAAC;IAC1C,CAAC,MAAM;MACL,IAAI,CAAC5H,KAAK,GAAG,IAAI;MACjB,IAAI,CAAC4E,YAAY,EAAE;MACnB,IAAI,CAACrE,aAAa,CAAC2H,IAAI,CAACN,GAAG,CAAC;IAC9B;EACF;EAEAW,kBAAkBA,CAACC,YAAiB;IAClC,MAAMC,WAAW,GAAGD,YAAY,CAACnE,OAAO,CAAC,GAAG,CAAC;IAC7C,IAAIoE,WAAW,KAAK,CAAC,CAAC,EAAE;MACtB,OAAOD,YAAY,CAACE,SAAS,CAACD,WAAW,GAAG,CAAC,CAAC;IAChD;IACA,OAAOD,YAAY;EACrB;EAEA9P,QAAQA,CAAC8M,IAAS;IAChB,IAAI2C,MAAM,CAACQ,OAAO,CAAC,WAAWnD,IAAI,CAACN,oBAAoB,IAAI,CAAC,EAAE;MAC5D,IAAI,CAACtE,yBAAyB,CAACgI,oDAAoD,CAAC;QAClF7F,IAAI,EAAEyC,IAAI,CAACN;OACZ,CAAC,CAAC1B,SAAS,CAACP,GAAG,IAAG;QACjB,IAAIA,GAAG,CAACE,UAAU,KAAK,CAAC,EAAE;UACxB,IAAI,CAAC3C,OAAO,CAACqI,aAAa,CAAC,MAAM,CAAC;UAClC,IAAI,CAACnG,iBAAiB,EAAE;QAC1B;MACF,CAAC,CAAC;IACJ;IACA,OAAO8C,IAAI;EACb;EAEAsD,WAAWA,CAACC,SAAqC;IAC/C,MAAMC,QAAQ,GAAa,EAAE;IAC7B,KAAK,MAAM5B,GAAG,IAAI2B,SAAS,EAAE;MAC3B,IAAIA,SAAS,CAAC3B,GAAG,CAAC,EAAE;QAClB4B,QAAQ,CAAC7E,IAAI,CAACiD,GAAG,CAAC;MACpB;IACF;IACA,OAAO4B,QAAQ;EACjB;EAEAC,gBAAgBA,CAACnB,IAAa;IAC5B,MAAMoB,SAAS,GAA6B,EAAE;IAC9C,KAAK,MAAMxD,SAAS,IAAIoC,IAAI,EAAE;MAC5B,KAAK,MAAMqB,KAAK,IAAIzD,SAAS,EAAE;QAC7B,IAAIyD,KAAK,CAAC/L,SAAS,IAAI+L,KAAK,CAAC9L,SAAS,IAAI8L,KAAK,CAAC7L,UAAU,KAAK,IAAI,CAAClB,qBAAqB,CAACS,mBAAmB,CAACC,KAAK,EAAE;UACnHoM,SAAS,CAAC/E,IAAI,CAAC;YACbG,QAAQ,EAAE6E,KAAK,CAAC7E,QAAQ;YACxBlH,SAAS,EAAE+L,KAAK,CAAC/L;WAClB,CAAC;QACJ;MACF;IACF;IACA,OAAO8L,SAAS;EAClB;EACApJ,uBAAuBA,CAAC8H,GAAQ;IAC9B,MAAMwB,KAAK,GAAG;MACZ/H,WAAW,EAAE,IAAI,CAACjF,qBAAqB,CAACS,mBAAmB,CAACC,KAAK;MACjE+I,YAAY,EAAE,IAAI,CAAClD,kBAAkB,CAAC7F,KAAK;MAC3ChE,KAAK,EAAE,IAAI,CAACqJ,YAAY,GAAG,IAAI,CAACA,YAAY,CAACkH,WAAW,GAAGrB,SAAS;MACpEK,MAAM,EAAE,IAAI,CAACY,gBAAgB,CAAC,IAAI,CAACjM,WAAW,CAAC;MAC/CkI,oBAAoB,EAAE,IAAI,CAAC9I,qBAAqB,CAAC8I,oBAAoB,IAAI8C,SAAS;MAClFM,YAAY,EAAE,IAAI,CAAClM,qBAAqB,CAACkM,YAAY,IAAI,KAAK;MAC9D1J,YAAY,EAAE,IAAI,CAACxC,qBAAqB,CAACwC;KAC1C;IAED,IAAI,CAAC0K,UAAU,CAACF,KAAK,CAACf,MAAM,CAAC;IAE7B,IAAI,IAAI,CAAC5H,KAAK,CAAC8I,aAAa,CAAClL,MAAM,GAAG,CAAC,EAAE;MACvC,IAAI,CAACmC,OAAO,CAACgJ,aAAa,CAAC,IAAI,CAAC/I,KAAK,CAAC8I,aAAa,CAAC;MACpD;IACF;IAAK,IAAI,CAACxI,2BAA2B,CAAC0I,qBAAqB,CAACL,KAAK,CAAC,CAAC5F,SAAS,CAACP,GAAG,IAAG;MACjF,IAAIA,GAAG,IAAIA,GAAG,CAACF,IAAK,IAAIE,GAAG,CAACF,IAAI,CAACI,UAAW,KAAK,CAAC,EAAE;QAClD,IAAI,CAAC3C,OAAO,CAACqI,aAAa,CAAC,MAAM,CAAC;QAClC,IAAI,CAACpG,WAAW,EAAE,EAAC;QACnB,IAAI,CAACI,wBAAwB,EAAE;QAC/B+E,GAAG,CAAC8B,KAAK,EAAE;MACb,CAAC,MAAM;QACL,IAAI,CAAClJ,OAAO,CAACmJ,YAAY,CAAC1G,GAAG,IAAIA,GAAG,CAACF,IAAI,IAAIE,GAAG,CAACF,IAAI,CAAC6G,OAAQ,CAAC;MACjE;IACF,CAAC,CAAC;EACJ;EAGAhK,OAAOA,CAACgI,GAAQ;IACdA,GAAG,CAAC8B,KAAK,EAAE;EACb;EAEAJ,UAAUA,CAACjB,MAAa;IACtB,IAAI,CAAC5H,KAAK,CAACoJ,KAAK,EAAE;IAClB,IAAI,IAAI,CAAC7J,KAAK,IAAI,CAAC,IAAI,CAACmC,YAAY,EAAE;MACpC,IAAI,CAAC1B,KAAK,CAACkB,QAAQ,CAAC,MAAM,EAAE,EAAE,CAAC;IACjC;IACA,IAAI,EAAE0G,MAAM,CAAChK,MAAM,GAAG,CAAC,CAAC,EAAE;MACxB,IAAI,CAACoC,KAAK,CAACkB,QAAQ,CAAC,QAAQ,EAAE,EAAE,CAAC;IACnC;IACA,IAAI,CAAClB,KAAK,CAACkB,QAAQ,CAAC,QAAQ,EAAE,IAAI,CAACvF,qBAAqB,CAACwC,YAAY,CAAC;EACxE;EAEAF,aAAaA,CAACoL,QAA4B;IACxC,IAAIC,KAAK,GAAG,EAAE;IACd,IAAID,QAAQ,IAAI9B,SAAS,EAAE;MACzB,QAAQ8B,QAAQ;QACd,KAAK,CAAC;UACJC,KAAK,GAAG,IAAI;UACZ;QACF,KAAK,CAAC;UACJA,KAAK,GAAG,IAAI;UACZ;QACF,KAAK,CAAC;UACJA,KAAK,GAAG,IAAI;UACZ;QACF;UACE;MACJ;IACF;IACA,OAAOA,KAAK;EACd;EAGA1E,cAAcA,CAACrI,WAA0B,EAAEiK,sBAAgD;IACzF,MAAMC,cAAc,GAAGD,sBAAsB,CAAC5D,GAAG,CAACmC,IAAI,IAAIA,IAAI,CAAClB,QAAQ,CAAC;IACxE,OAAOtH,WAAW,CAACqG,GAAG,CAAC8D,UAAU,IAAG;MAClC,OAAOA,UAAU,CAAC9D,GAAG,CAACmC,IAAI,IAAG;QAC3B,IAAI0B,cAAc,CAACzN,QAAQ,CAAC+L,IAAI,CAAClB,QAAQ,CAAC,EAAE;UAC1CkB,IAAI,CAACpI,SAAS,GAAG,IAAI;QACvB,CAAC,MAAM;UACLoI,IAAI,CAACpI,SAAS,GAAG,KAAK;QACxB;QACA,OAAOoI,IAAI;MACb,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ;;;uCAndWpF,yBAAyB,EAAApJ,EAAA,CAAAgT,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAlT,EAAA,CAAAgT,iBAAA,CAAAG,EAAA,CAAAC,eAAA,GAAApT,EAAA,CAAAgT,iBAAA,CAAAK,EAAA,CAAAC,cAAA,GAAAtT,EAAA,CAAAgT,iBAAA,CAAAO,EAAA,CAAAC,gBAAA,GAAAxT,EAAA,CAAAgT,iBAAA,CAAAS,EAAA,CAAAC,cAAA,GAAA1T,EAAA,CAAAgT,iBAAA,CAAAW,EAAA,CAAAC,gBAAA,GAAA5T,EAAA,CAAAgT,iBAAA,CAAAW,EAAA,CAAAE,wBAAA,GAAA7T,EAAA,CAAAgT,iBAAA,CAAAW,EAAA,CAAAG,wBAAA,GAAA9T,EAAA,CAAAgT,iBAAA,CAAAW,EAAA,CAAAI,YAAA,GAAA/T,EAAA,CAAAgT,iBAAA,CAAAgB,EAAA,CAAAC,mBAAA;IAAA;EAAA;;;YAAzB7K,yBAAyB;MAAA8K,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAApU,EAAA,CAAAqU,0BAAA,EAAArU,EAAA,CAAAsU,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,mCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;UClDpC5U,EADF,CAAAC,cAAA,iBAA0B,qBACR;UACdD,EAAA,CAAAmB,SAAA,qBAAiC;UACnCnB,EAAA,CAAAG,YAAA,EAAiB;UAEfH,EADF,CAAAC,cAAA,mBAAc,YACyB;UAAAD,EAAA,CAAAE,MAAA,gRAA6C;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAIjFH,EAHN,CAAAC,cAAA,aAA8B,aACN,aACqC,eACT;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACxDH,EAAA,CAAAC,cAAA,oBACyC;UADbD,EAAA,CAAA0D,gBAAA,2BAAAoR,uEAAAlR,MAAA;YAAA5D,EAAA,CAAAW,aAAA,CAAAoU,GAAA;YAAA/U,EAAA,CAAA8D,kBAAA,CAAA+Q,GAAA,CAAAlJ,kBAAA,EAAA/H,MAAA,MAAAiR,GAAA,CAAAlJ,kBAAA,GAAA/H,MAAA;YAAA,OAAA5D,EAAA,CAAAiB,WAAA,CAAA2C,MAAA;UAAA,EAAgC;UAC1D5D,EAAA,CAAAS,UAAA,4BAAAuU,wEAAA;YAAAhV,EAAA,CAAAW,aAAA,CAAAoU,GAAA;YAAA,OAAA/U,EAAA,CAAAiB,WAAA,CAAkB4T,GAAA,CAAAnJ,iBAAA,EAAmB;UAAA,EAAC;UACtC1L,EAAA,CAAA+B,UAAA,KAAAkT,+CAAA,uBAAoE;UAK1EjV,EAFI,CAAAG,YAAA,EAAY,EACR,EACF;UAEJH,EADF,CAAAC,cAAA,cAAsB,cAC2B;UAC7CD,EAAA,CAAA+B,UAAA,KAAAmT,4CAAA,qBAAsF;UAK5FlV,EAFI,CAAAG,YAAA,EAAM,EACF,EACF;UAENH,EAAA,CAAA+B,UAAA,KAAAoT,kDAAA,4BAA4C;UAkF9CnV,EAAA,CAAAG,YAAA,EAAe;UACfH,EAAA,CAAAmB,SAAA,0BAEiB;UACnBnB,EAAA,CAAAG,YAAA,EAAU;UAIVH,EAAA,CAAA+B,UAAA,KAAAqT,iDAAA,gCAAApV,EAAA,CAAAqV,sBAAA,CAAoD;;;UA3GdrV,EAAA,CAAAM,SAAA,IAAgC;UAAhCN,EAAA,CAAAuE,gBAAA,YAAAsQ,GAAA,CAAAlJ,kBAAA,CAAgC;UAE9B3L,EAAA,CAAAM,SAAA,EAAuB;UAAvBN,EAAA,CAAAI,UAAA,YAAAyU,GAAA,CAAAzI,oBAAA,CAAuB;UAQiBpM,EAAA,CAAAM,SAAA,GAAc;UAAdN,EAAA,CAAAI,UAAA,SAAAyU,GAAA,CAAAS,QAAA,CAAc;UAO3EtV,EAAA,CAAAM,SAAA,EAA2B;UAA3BN,EAAA,CAAAI,UAAA,SAAAyU,GAAA,CAAAxQ,qBAAA,CAA2B;;;qBDqBlC3E,YAAY,EAAA6V,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,OAAA,EAAAF,EAAA,CAAAG,IAAA,EAAAH,EAAA,CAAAI,QAAA,EAAE/V,YAAY,EAAAgW,EAAA,CAAAC,oBAAA,EAAAD,EAAA,CAAAE,eAAA,EAAAF,EAAA,CAAAG,OAAA,EAAA5C,EAAA,CAAA6C,eAAA,EAAA7C,EAAA,CAAA8C,mBAAA,EAAA9C,EAAA,CAAA+C,qBAAA,EAAA/C,EAAA,CAAAgD,qBAAA,EAAAhD,EAAA,CAAAiD,mBAAA,EAAAjD,EAAA,CAAAkD,gBAAA,EAAAlD,EAAA,CAAAmD,iBAAA,EAAAnD,EAAA,CAAAoD,iBAAA,EAAAC,GAAA,CAAAC,aAAA,EAAAC,GAAA,CAAAC,mBAAA,EAAAC,GAAA,CAAAC,kBAAA,EAA8C9W,kBAAkB;MAAA+W,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}