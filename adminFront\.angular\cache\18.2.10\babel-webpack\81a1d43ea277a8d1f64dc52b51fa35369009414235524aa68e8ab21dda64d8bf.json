{"ast": null, "code": "export * from 'preact';\nexport { createPortal } from 'preact/compat';\nexport { ah as createContext, aa as flushSync } from './internal-common.js';", "map": {"version": 3, "names": ["createPortal", "ah", "createContext", "aa", "flushSync"], "sources": ["C:/Users/<USER>/Documents/salechange-product/adminFront/node_modules/@fullcalendar/core/preact.js"], "sourcesContent": ["export * from 'preact';\nexport { createPortal } from 'preact/compat';\nexport { ah as createContext, aa as flushSync } from './internal-common.js';\n"], "mappings": "AAAA,cAAc,QAAQ;AACtB,SAASA,YAAY,QAAQ,eAAe;AAC5C,SAASC,EAAE,IAAIC,aAAa,EAAEC,EAAE,IAAIC,SAAS,QAAQ,sBAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}