{"ast": null, "code": "import { Theme, injectStyles } from '@fullcalendar/core/internal.js';\nclass BootstrapTheme extends Theme {}\nBootstrapTheme.prototype.classes = {\n  root: 'fc-theme-bootstrap',\n  table: 'table-bordered',\n  tableCellShaded: 'table-active',\n  buttonGroup: 'btn-group',\n  button: 'btn btn-primary',\n  buttonActive: 'active',\n  popover: 'popover',\n  popoverHeader: 'popover-header',\n  popoverContent: 'popover-body'\n};\nBootstrapTheme.prototype.baseIconClass = 'fa';\nBootstrapTheme.prototype.iconClasses = {\n  close: 'fa-times',\n  prev: 'fa-chevron-left',\n  next: 'fa-chevron-right',\n  prevYear: 'fa-angle-double-left',\n  nextYear: 'fa-angle-double-right'\n};\nBootstrapTheme.prototype.rtlIconClasses = {\n  prev: 'fa-chevron-right',\n  next: 'fa-chevron-left',\n  prevYear: 'fa-angle-double-right',\n  nextYear: 'fa-angle-double-left'\n};\nBootstrapTheme.prototype.iconOverrideOption = 'bootstrapFontAwesome'; // TODO: make TS-friendly. move the option-processing into this plugin\nBootstrapTheme.prototype.iconOverrideCustomButtonOption = 'bootstrapFontAwesome';\nBootstrapTheme.prototype.iconOverridePrefix = 'fa-';\nvar css_248z = \".fc-theme-bootstrap a:not([href]){color:inherit}.fc-theme-bootstrap .fc-more-link:hover{text-decoration:none}\";\ninjectStyles(css_248z);\nexport { BootstrapTheme };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}