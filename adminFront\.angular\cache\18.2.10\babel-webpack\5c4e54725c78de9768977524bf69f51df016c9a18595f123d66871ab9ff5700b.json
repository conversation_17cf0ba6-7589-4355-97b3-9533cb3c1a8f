{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { Component } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { NbCheckboxModule } from '@nebular/theme';\nimport { tap } from 'rxjs';\nimport { BaseFilePipe } from 'src/app/@theme/pipes/base-file.pipe';\nimport { SharedModule } from 'src/app/pages/components/shared.module';\nimport { BaseComponent } from 'src/app/pages/components/base/baseComponent';\nlet DetailContentManagementSalesAccountComponent = class DetailContentManagementSalesAccountComponent extends BaseComponent {\n  constructor(_allow, route, message, _formItemService, _regularNoticeFileService, _utilityService, valid, location, _materialService, _eventService) {\n    super(_allow);\n    this._allow = _allow;\n    this.route = route;\n    this.message = message;\n    this._formItemService = _formItemService;\n    this._regularNoticeFileService = _regularNoticeFileService;\n    this._utilityService = _utilityService;\n    this.valid = valid;\n    this.location = location;\n    this._materialService = _materialService;\n    this._eventService = _eventService;\n    this.typeContentManagementSalesAccount = {\n      CFormType: 2,\n      CNoticeType: 2\n    };\n    this.CUiTypeOptions = [{\n      value: 1,\n      label: '建材選色'\n    }, {\n      value: 2,\n      label: '群組選樣_選色'\n    }, {\n      value: 3,\n      label: '建材選樣'\n    }];\n    this.CRemarkTypeOptions = [\"正常\", \"留料\"];\n    this.selectedItems = {};\n    this.selectedRemarkType = {};\n    this.isNew = true;\n  }\n  ngOnInit() {\n    this.route.paramMap.subscribe(params => {\n      if (params) {\n        const idParam = params.get('id');\n        const id = idParam ? +idParam : 0;\n        this.buildCaseId = id;\n        if (this.buildCaseId) {\n          this.getListRegularNoticeFileHouseHold();\n        }\n      }\n    });\n  }\n  getItemByValue(value, options) {\n    for (const item of options) {\n      if (item.value === value) {\n        return item;\n      }\n    }\n    return null;\n  }\n  detectFiles(event, formItemReq_) {\n    const file = event.target.files[0];\n    if (file) {\n      let reader = new FileReader();\n      reader.readAsDataURL(file);\n      reader.onload = () => {\n        let base64Str = reader.result;\n        if (!base64Str) {\n          return;\n        }\n        if (formItemReq_.listPictures.length > 0) {\n          formItemReq_.listPictures[0] = {\n            id: new Date().getTime(),\n            name: file.name.split('.')[0],\n            data: base64Str,\n            extension: this._utilityService.getFileExtension(file.name),\n            CFile: file\n          };\n        } else {\n          formItemReq_.listPictures.push({\n            id: new Date().getTime(),\n            name: file.name.split('.')[0],\n            data: base64Str,\n            extension: this._utilityService.getFileExtension(file.name),\n            CFile: file\n          });\n        }\n        event.target.value = null;\n      };\n    }\n  }\n  removeImage(pictureId, formItemReq_) {\n    if (formItemReq_.listPictures.length) {\n      formItemReq_.listPictures = formItemReq_.listPictures.filter(x => x.id != pictureId);\n    }\n  }\n  renameFile(event, index, formItemReq_) {\n    var blob = formItemReq_.listPictures[index].CFile.slice(0, formItemReq_.listPictures[index].CFile.size, formItemReq_.listPictures[index].CFile.type);\n    var newFile = new File([blob], `${event.target.value + '.' + formItemReq_.listPictures[index].extension}`, {\n      type: formItemReq_.listPictures[index].CFile.type\n    });\n    formItemReq_.listPictures[index].CFile = newFile;\n  }\n  onCheckAllChange(checked, formItemReq_) {\n    formItemReq_.allSelected = checked;\n    this.houseHoldList.forEach(item => {\n      formItemReq_.selectedItems[item] = checked;\n    });\n  }\n  onCheckboxHouseHoldListChange(checked, item, formItemReq_) {\n    if (checked) {\n      formItemReq_.selectedItems[item] = checked;\n      formItemReq_.allSelected = this.houseHoldList.every(item => formItemReq_.selectedItems[item] == checked);\n    } else {\n      formItemReq_.allSelected = false;\n    }\n  }\n  onCheckboxRemarkChange(checked, item, formItemReq_) {\n    formItemReq_.selectedRemarkType[item] = checked;\n  }\n  createRemarkObject(CRemarkTypeOptions, CRemarkType) {\n    const remarkObject = {};\n    for (const option of CRemarkTypeOptions) {\n      remarkObject[option] = false;\n    }\n    const remarkTypes = CRemarkType.split('-');\n    for (const type of remarkTypes) {\n      if (CRemarkTypeOptions.includes(type)) {\n        remarkObject[type] = true;\n      }\n    }\n    return remarkObject;\n  }\n  mergeItems(items) {\n    const map = new Map();\n    items.forEach(item => {\n      const key = `${item.CLocation}_${item.CName}_${item.CPart}`;\n      if (map.has(key)) {\n        const existing = map.get(key);\n        existing.count += 1;\n      } else {\n        map.set(key, {\n          item: {\n            ...item\n          },\n          count: 1\n        });\n      }\n    });\n    return Array.from(map.values()).map(({\n      item,\n      count\n    }) => ({\n      ...item,\n      CTotalAnswer: count\n    }));\n  }\n  getMaterialList() {\n    this._materialService.apiMaterialGetMaterialListPost$Json({\n      body: {\n        CBuildCaseId: this.buildCaseId,\n        CPagi: false\n      }\n    }).pipe(tap(res => {\n      if (res.Entries && res.StatusCode == 0) {\n        this.houseHoldList.forEach(item => this.selectedItems[item] = false);\n        this.CRemarkTypeOptions.forEach(item => this.selectedRemarkType[item] = false);\n        this.arrListFormItemReq = res.Entries.map(o => {\n          return {\n            CDesignFileUrl: null,\n            CFormItemHouseHold: null,\n            CFormId: null,\n            CLocation: o.CLocation,\n            CName: o.CName,\n            CPart: o.CPart,\n            CItemName: `${o.CName}-${o.CPart}-${o.CLocation}`,\n            CRemarkType: null,\n            CTotalAnswer: 0,\n            CRequireAnswer: 1,\n            CUiType: 0,\n            selectedItems: {},\n            selectedRemarkType: this.selectedRemarkType,\n            allSelected: false,\n            listPictures: [],\n            selectedCUiType: this.CUiTypeOptions[0],\n            CFirstMatrialUrl: o.CPicture\n          };\n        });\n        this.arrListFormItemReq = [...this.mergeItems(this.arrListFormItemReq)];\n      }\n    })).subscribe();\n  }\n  getListFormItem() {\n    this._formItemService.apiFormItemGetListFormItemPost$Json({\n      body: {\n        CBuildCaseId: this.buildCaseId,\n        CFormType: this.typeContentManagementSalesAccount.CFormType,\n        CIsPaging: false\n      }\n    }).pipe(tap(res => {\n      if (res.Entries && res.StatusCode == 0) {\n        this.listFormItem = res.Entries;\n        this.isNew = res.Entries.formItems ? false : true;\n        if (res.Entries.formItems) {\n          this.houseHoldList.forEach(item => this.selectedItems[item] = false);\n          this.CRemarkTypeOptions.forEach(item => this.selectedRemarkType[item] = false);\n          this.arrListFormItemReq = res.Entries.formItems.map(o => {\n            return {\n              CFormId: this.listFormItem.CFormId,\n              CDesignFileUrl: o.CDesignFileUrl,\n              CFirstMatrialUrl: o.CFirstMatrialUrl,\n              CFile: o.CFile,\n              CFormItemHouseHold: o.CFormItemHouseHold,\n              CFormItemId: o.CFormItemId,\n              CLocation: o.CLocation,\n              CName: o.CName,\n              CPart: o.CPart,\n              CItemName: o.CItemName ? o.CItemName : `${o.CName}-${o.CPart}-${o.CLocation}`,\n              CRemarkType: o.CRemarkType,\n              CTotalAnswer: o.CTotalAnswer,\n              CRequireAnswer: o.CUiType === 3 ? 1 : o.CRequireAnswer,\n              CUiType: o.CUiType,\n              selectedItems: o.tblFormItemHouseholds.length ? this.createArrayObjectFromArray(this.houseHoldList, o.tblFormItemHouseholds) : {\n                ...this.selectedItems\n              },\n              selectedRemarkType: o.CRemarkType ? this.createRemarkObject(this.CRemarkTypeOptions, o.CRemarkType) : {\n                ...this.selectedRemarkType\n              },\n              allSelected: o.tblFormItemHouseholds.length === this.houseHoldList.length,\n              listPictures: [],\n              selectedCUiType: o.CUiType ? this.getItemByValue(o.CUiType, this.CUiTypeOptions) : this.CUiTypeOptions[0]\n            };\n          });\n        } else {\n          this.getMaterialList();\n        }\n      }\n    })).subscribe();\n  }\n  changeSelectCUiType(formItemReq) {\n    if (formItemReq.selectedCUiType && formItemReq.selectedCUiType.value === 3) {\n      formItemReq.CRequireAnswer = 1;\n    }\n  }\n  getHouseHoldListByNoticeType(data) {\n    for (let item of data) {\n      if (item.CNoticeType === this.typeContentManagementSalesAccount.CNoticeType) {\n        return item.CHouseHoldList;\n      }\n    }\n    return [];\n  }\n  getKeysWithTrueValue(obj) {\n    return Object.keys(obj).filter(key => obj[key]);\n  }\n  getKeysWithTrueValueJoined(obj) {\n    return Object.keys(obj).filter(key => obj[key]).join('-');\n  }\n  getCRemarkType(selectedCUiType, selectedRemarkType) {\n    if (selectedCUiType && selectedCUiType.value == 3) {\n      return this.getKeysWithTrueValueJoined(selectedRemarkType);\n    }\n  }\n  getStringAfterComma(inputString) {\n    const parts = inputString.split(',');\n    if (parts.length > 1) {\n      return parts[1];\n    } else return \"\";\n  }\n  formatFile(listPictures) {\n    if (listPictures && listPictures.length > 0) {\n      return {\n        Base64String: this.getStringAfterComma(listPictures[0].data) || null,\n        FileExtension: listPictures[0].extension || null,\n        FileName: listPictures[0].CFile.name || listPictures[0].name || null\n      };\n    } else return undefined;\n  }\n  validation() {\n    this.valid.clear();\n    let hasInvalidCUiType = false;\n    let hasInvalidCRequireAnswer = false;\n    let hasInvalidItemName = false;\n    for (const item of this.saveListFormItemReq) {\n      if (!hasInvalidCUiType && !item.CUiType) {\n        hasInvalidCUiType = true;\n      }\n      if (!hasInvalidCRequireAnswer && !item.CRequireAnswer) {\n        hasInvalidCRequireAnswer = true;\n      }\n      if (item.CTotalAnswer && item.CRequireAnswer) {\n        if (item.CRequireAnswer > item.CTotalAnswer || item.CRequireAnswer < 0) {\n          this.valid.addErrorMessage('[必填數量]' + ' <= ' + item.CTotalAnswer + ` (${item.CItemName}) `);\n        }\n      }\n      if (!hasInvalidItemName && !item.CItemName) {\n        hasInvalidItemName = true;\n      }\n    }\n    if (hasInvalidCUiType) {\n      this.valid.addErrorMessage('[前台UI類型]' + ' 必填');\n    }\n    if (hasInvalidCRequireAnswer) {\n      this.valid.addErrorMessage('[必填數量]' + ' 必填且>0');\n    }\n    if (hasInvalidItemName) {\n      this.valid.addErrorMessage('[廚房-廚具-櫃體]' + ' 必填');\n    }\n  }\n  onSubmit() {\n    this.saveListFormItemReq = this.arrListFormItemReq.map(e => {\n      return {\n        CDesignFileUrl: e.CDesignFileUrl ? e.CDesignFileUrl : null,\n        CFile: e.listPictures ? this.formatFile(e.listPictures) : undefined,\n        CFormItemHouseHold: this.getKeysWithTrueValue(e.selectedItems),\n        CFormItemId: e.CFormItemId ? e.CFormItemId : null,\n        CFormID: this.isNew ? null : this.listFormItem.CFormId,\n        CName: e.CName,\n        CPart: e.CPart,\n        CLocation: e.CLocation,\n        CItemName: e.CItemName,\n        //? e.CItemName : `${e.CName}-${e.CPart}-${e.CLocation}`,\n        CRemarkType: e.selectedCUiType.value !== 3 ? null : this.getCRemarkType(e.selectedCUiType, e.selectedRemarkType) || null,\n        CTotalAnswer: e.CTotalAnswer,\n        CRequireAnswer: e.CRequireAnswer,\n        CUiType: e.selectedCUiType.value\n      };\n    });\n    this.validation();\n    if (this.valid.errorMessages.length > 0) {\n      this.message.showErrorMSGs(this.valid.errorMessages);\n      return;\n    }\n    if (this.isNew) {\n      this.createListFormItem();\n    } else {\n      this.saveListFormItem();\n    }\n  }\n  saveListFormItem() {\n    this._formItemService.apiFormItemSaveListFormItemPost$Json({\n      body: this.saveListFormItemReq\n    }).subscribe(res => {\n      if (res.StatusCode == 0) {\n        this.message.showSucessMSG(\"執行成功\");\n        // this.getListFormItem()\n        this.goBack();\n      }\n    });\n  }\n  createListFormItem() {\n    this.creatListFormItem = {\n      CBuildCaseId: this.buildCaseId,\n      CFormItem: this.saveListFormItemReq || null,\n      CFormType: this.typeContentManagementSalesAccount.CFormType\n    };\n    this._formItemService.apiFormItemCreateListFormItemPost$Json({\n      body: this.creatListFormItem\n    }).subscribe(res => {\n      if (res.StatusCode == 0) {\n        this.message.showSucessMSG(\"執行成功\");\n        // this.getListFormItem()\n        this.goBack();\n      }\n    });\n  }\n  createArrayObjectFromArray(a, b) {\n    const c = {};\n    for (const item of a) {\n      const matchingItem = b.find(bItem => bItem.CHousehold === item && bItem.CIsSelect);\n      c[item] = !!matchingItem;\n    }\n    return c;\n  } //[\"House1\", \"House2\", \"House3\"] => [{CHousehold: \"House1\", CIsSelect: true,... }, ... ]\n  getListRegularNoticeFileHouseHold() {\n    this._regularNoticeFileService.apiRegularNoticeFileGetListRegularNoticeFileHouseHoldPost$Json({\n      body: this.buildCaseId\n    }).pipe(tap(res => {\n      if (res.Entries && res.StatusCode == 0) {\n        this.houseHoldList = this.getHouseHoldListByNoticeType(res.Entries);\n        this.getListFormItem();\n      }\n    })).subscribe();\n  }\n  goBack() {\n    this._eventService.push({\n      action: \"GET_BUILDCASE\" /* EEvent.GET_BUILDCASE */,\n      payload: this.buildCaseId\n    });\n    this.location.back();\n  }\n};\nDetailContentManagementSalesAccountComponent = __decorate([Component({\n  selector: 'ngx-detail-content-management-sales-account',\n  templateUrl: './detail-content-management-sales-account.component.html',\n  styleUrls: ['./detail-content-management-sales-account.component.scss'],\n  standalone: true,\n  imports: [CommonModule, SharedModule, NbCheckboxModule, BaseFilePipe]\n})], DetailContentManagementSalesAccountComponent);\nexport { DetailContentManagementSalesAccountComponent };", "map": {"version": 3, "names": ["Component", "CommonModule", "NbCheckboxModule", "tap", "BaseFilePipe", "SharedModule", "BaseComponent", "DetailContentManagementSalesAccountComponent", "constructor", "_allow", "route", "message", "_formItemService", "_regularNoticeFileService", "_utilityService", "valid", "location", "_materialService", "_eventService", "typeContentManagementSalesAccount", "CFormType", "CNoticeType", "CUiTypeOptions", "value", "label", "CRemarkTypeOptions", "selectedItems", "selectedRemarkType", "isNew", "ngOnInit", "paramMap", "subscribe", "params", "idParam", "get", "id", "buildCaseId", "getListRegularNoticeFileHouseHold", "getItemByValue", "options", "item", "detectFiles", "event", "formItemReq_", "file", "target", "files", "reader", "FileReader", "readAsDataURL", "onload", "base64Str", "result", "listPictures", "length", "Date", "getTime", "name", "split", "data", "extension", "getFileExtension", "CFile", "push", "removeImage", "pictureId", "filter", "x", "renameFile", "index", "blob", "slice", "size", "type", "newFile", "File", "onCheckAllChange", "checked", "allSelected", "houseHoldList", "for<PERSON>ach", "onCheckboxHouseHoldListChange", "every", "onCheckboxRemarkChange", "createRemarkObject", "CRemarkType", "remarkObject", "option", "remarkTypes", "includes", "mergeItems", "items", "map", "Map", "key", "CLocation", "CName", "<PERSON>art", "has", "existing", "count", "set", "Array", "from", "values", "CTotalAnswer", "getMaterialList", "apiMaterialGetMaterialListPost$Json", "body", "CBuildCaseId", "CPagi", "pipe", "res", "Entries", "StatusCode", "arrListFormItemReq", "o", "CDesignFileUrl", "CFormItemHouseHold", "CFormId", "CItemName", "CRequireAnswer", "CUiType", "selectedCUiType", "CFirstMatrialUrl", "CPicture", "getListFormItem", "apiFormItemGetListFormItemPost$Json", "CIsPaging", "listFormItem", "formItems", "CFormItemId", "tblFormItemHouseholds", "createArrayObjectFromArray", "changeSelectCUiType", "formItemReq", "getHouseHoldListByNoticeType", "CHouseHoldList", "getKeysWithTrueValue", "obj", "Object", "keys", "getKeysWithTrueValueJoined", "join", "getCRemarkType", "getStringAfterComma", "inputString", "parts", "formatFile", "Base64String", "FileExtension", "FileName", "undefined", "validation", "clear", "hasInvalidCUiType", "hasInvalidCRequireAnswer", "hasInvalidItemName", "saveListFormItemReq", "addErrorMessage", "onSubmit", "e", "CFormID", "errorMessages", "showErrorMSGs", "createListFormItem", "saveListFormItem", "apiFormItemSaveListFormItemPost$Json", "showSucessMSG", "goBack", "creatListFormItem", "CFormItem", "apiFormItemCreateListFormItemPost$Json", "a", "b", "c", "matchingItem", "find", "bItem", "CHousehold", "CIsSelect", "apiRegularNoticeFileGetListRegularNoticeFileHouseHoldPost$Json", "action", "payload", "back", "__decorate", "selector", "templateUrl", "styleUrls", "standalone", "imports"], "sources": ["C:\\Users\\<USER>\\Documents\\jeansalechange\\adminFront\\src\\app\\pages\\selection-management\\content-management-sales-account\\detail-content-management-sales-account\\detail-content-management-sales-account.component.ts"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { CommonModule, Location } from '@angular/common';\r\nimport { NbCheckboxModule } from '@nebular/theme';\r\nimport { AllowHelper } from 'src/app/shared/helper/allowHelper';\r\nimport { FormItemService, MaterialService, RegularNoticeFileService } from 'src/services/api/services';\r\nimport { ActivatedRoute } from '@angular/router';\r\nimport { MessageService } from 'src/app/shared/services/message.service';\r\nimport { tap } from 'rxjs';\r\nimport { CreateListFormItem, FileViewModel, GetListFormItemRes, SaveListFormItemReq } from 'src/services/api/models';\r\nimport { BaseFilePipe } from 'src/app/@theme/pipes/base-file.pipe';\r\nimport { UtilityService } from 'src/app/shared/services/utility.service';\r\nimport { ValidationHelper } from 'src/app/shared/helper/validationHelper';\r\nimport { SharedModule } from 'src/app/pages/components/shared.module';\r\nimport { BaseComponent } from 'src/app/pages/components/base/baseComponent';\r\nimport { EventService, EEvent } from 'src/app/shared/services/event.service';\r\n\r\n\r\nexport interface ExtendedSaveListFormItemReq {\r\n  CDesignFileUrl?: string | null;\r\n  CFirstMatrialUrl?: string | null;\r\n  CFile?: FileViewModel,\r\n  CFormItemHouseHold?: Array<string> | null;\r\n  CFormItemId?: number;\r\n  CItemName?: string;\r\n  CFormID?: number;\r\n  CPart?: string | null;\r\n  CName?: string | null;\r\n  CLocation?: string | null;\r\n  CRemarkType?: string | null;\r\n  CTotalAnswer?: number;\r\n  CRequireAnswer?: number;\r\n  CUiType?: number;\r\n  selectedCUiType: any | null;\r\n  selectedItems: { [key: string]: boolean }\r\n  selectedRemarkType?: { [key: string]: boolean }\r\n  allSelected: boolean,\r\n  listPictures: any[],\r\n}\r\n\r\n@Component({\r\n  selector: 'ngx-detail-content-management-sales-account',\r\n  templateUrl: './detail-content-management-sales-account.component.html',\r\n  styleUrls: ['./detail-content-management-sales-account.component.scss'],\r\n  standalone: true,\r\n  imports: [CommonModule, SharedModule, NbCheckboxModule, BaseFilePipe],\r\n})\r\n\r\nexport class DetailContentManagementSalesAccountComponent extends BaseComponent implements OnInit {\r\n  constructor(\r\n    private _allow: AllowHelper,\r\n    private route: ActivatedRoute,\r\n    private message: MessageService,\r\n    private _formItemService: FormItemService,\r\n    private _regularNoticeFileService: RegularNoticeFileService,\r\n    private _utilityService: UtilityService,\r\n    private valid: ValidationHelper,\r\n    private location: Location,\r\n    private _materialService: MaterialService,\r\n    private _eventService: EventService\r\n  ) {\r\n    super(_allow)\r\n  }\r\n\r\n  typeContentManagementSalesAccount = {\r\n    CFormType: 2,\r\n    CNoticeType: 2\r\n  }\r\n\r\n  CUiTypeOptions: any[] = [\r\n    {\r\n      value: 1, label: '建材選色'\r\n    },\r\n    {\r\n      value: 2, label: '群組選樣_選色'\r\n    }, {\r\n      value: 3, label: '建材選樣'\r\n    }\r\n  ]\r\n  CRemarkTypeOptions = [\"正常\", \"留料\"]\r\n  buildCaseId: number\r\n\r\n  override ngOnInit(): void {\r\n    this.route.paramMap.subscribe(params => {\r\n      if (params) {\r\n        const idParam = params.get('id');\r\n        const id = idParam ? +idParam : 0;\r\n        this.buildCaseId = id\r\n        if (this.buildCaseId) {\r\n          this.getListRegularNoticeFileHouseHold()\r\n        }\r\n      }\r\n    })\r\n  }\r\n\r\n\r\n  getItemByValue(value: any, options: any[]) {\r\n    for (const item of options) {\r\n      if (item.value === value) {\r\n        return item;\r\n      }\r\n    }\r\n    return null;\r\n  }\r\n\r\n\r\n  selectedItems: { [key: string]: boolean } = {};\r\n  selectedRemarkType: { [key: string]: boolean } = {};\r\n\r\n\r\n\r\n  detectFiles(event: any, formItemReq_: any) {\r\n    const file = event.target.files[0]\r\n    if (file) {\r\n      let reader = new FileReader();\r\n      reader.readAsDataURL(file);\r\n      reader.onload = () => {\r\n        let base64Str: string = reader.result as string;\r\n        if (!base64Str) {\r\n          return;\r\n        }\r\n        if (formItemReq_.listPictures.length > 0) {\r\n          formItemReq_.listPictures[0] = {\r\n            id: new Date().getTime(),\r\n            name: file.name.split('.')[0],\r\n            data: base64Str,\r\n            extension: this._utilityService.getFileExtension(file.name),\r\n            CFile: file\r\n          };\r\n        } else {\r\n          formItemReq_.listPictures.push({\r\n            id: new Date().getTime(),\r\n            name: file.name.split('.')[0],\r\n            data: base64Str,\r\n            extension: this._utilityService.getFileExtension(file.name),\r\n            CFile: file\r\n          });\r\n        }\r\n        event.target.value = null;\r\n      };\r\n    }\r\n  }\r\n\r\n  removeImage(pictureId: number, formItemReq_: any) {\r\n    if (formItemReq_.listPictures.length) {\r\n      formItemReq_.listPictures = formItemReq_.listPictures.filter((x: any) => x.id != pictureId)\r\n    }\r\n  }\r\n\r\n  renameFile(event: any, index: number, formItemReq_: any) {\r\n    var blob = formItemReq_.listPictures[index].CFile.slice(0, formItemReq_.listPictures[index].CFile.size, formItemReq_.listPictures[index].CFile.type);\r\n    var newFile = new File([blob], `${event.target.value + '.' + formItemReq_.listPictures[index].extension}`, { type: formItemReq_.listPictures[index].CFile.type });\r\n    formItemReq_.listPictures[index].CFile = newFile\r\n  }\r\n\r\n\r\n  onCheckAllChange(checked: boolean, formItemReq_: any) {\r\n    formItemReq_.allSelected = checked;\r\n    this.houseHoldList.forEach(item => {\r\n      formItemReq_.selectedItems[item] = checked;\r\n    });\r\n  }\r\n\r\n  onCheckboxHouseHoldListChange(checked: boolean, item: string, formItemReq_: any) {\r\n    if (checked) {\r\n      formItemReq_.selectedItems[item] = checked;\r\n      formItemReq_.allSelected = this.houseHoldList.every(item => formItemReq_.selectedItems[item] == checked);\r\n    } else {\r\n      formItemReq_.allSelected = false\r\n    }\r\n  }\r\n\r\n\r\n\r\n  onCheckboxRemarkChange(checked: boolean, item: string, formItemReq_: any) {\r\n    formItemReq_.selectedRemarkType[item] = checked;\r\n  }\r\n\r\n  createRemarkObject(CRemarkTypeOptions: string[], CRemarkType: string): { [key: string]: boolean } {\r\n    const remarkObject: { [key: string]: boolean } = {};\r\n    for (const option of CRemarkTypeOptions) {\r\n      remarkObject[option] = false;\r\n    }\r\n    const remarkTypes = CRemarkType.split('-');\r\n    for (const type of remarkTypes) {\r\n      if (CRemarkTypeOptions.includes(type)) {\r\n        remarkObject[type] = true;\r\n      }\r\n    }\r\n    return remarkObject;\r\n  }\r\n\r\n  mergeItems(items: ExtendedSaveListFormItemReq[]): ExtendedSaveListFormItemReq[] {\r\n    const map = new Map<string, { item: ExtendedSaveListFormItemReq, count: number }>();\r\n\r\n    items.forEach(item => {\r\n      const key = `${item.CLocation}_${item.CName}_${item.CPart}`;\r\n      if (map.has(key)) {\r\n        const existing = map.get(key)!;\r\n        existing.count += 1;\r\n      } else {\r\n        map.set(key, { item: { ...item }, count: 1 });\r\n      }\r\n    });\r\n\r\n    return Array.from(map.values()).map(({ item, count }) => ({\r\n      ...item,\r\n      CTotalAnswer: count\r\n    }));\r\n  }\r\n\r\n\r\n  getMaterialList() { // call when create\r\n    this._materialService.apiMaterialGetMaterialListPost$Json({\r\n      body: {\r\n        CBuildCaseId: this.buildCaseId,\r\n        CPagi: false\r\n      }\r\n    }).pipe(\r\n      tap(res => {\r\n        if (res.Entries && res.StatusCode == 0) {\r\n\r\n          this.houseHoldList.forEach(item => this.selectedItems[item] = false)\r\n\r\n          this.CRemarkTypeOptions.forEach(item => this.selectedRemarkType[item] = false)\r\n\r\n          this.arrListFormItemReq = res.Entries.map((o: any) => {\r\n            return {\r\n              CDesignFileUrl: null,\r\n              CFormItemHouseHold: null,\r\n              CFormId: null,\r\n              CLocation: o.CLocation,\r\n              CName: o.CName,\r\n              CPart: o.CPart,\r\n              CItemName: `${o.CName}-${o.CPart}-${o.CLocation}`,\r\n              CRemarkType: null,\r\n              CTotalAnswer: 0,\r\n              CRequireAnswer: 1,\r\n              CUiType: 0,\r\n              selectedItems: {},\r\n              selectedRemarkType: this.selectedRemarkType,\r\n              allSelected: false,\r\n              listPictures: [],\r\n              selectedCUiType: this.CUiTypeOptions[0],\r\n              CFirstMatrialUrl: o.CPicture,\r\n            }\r\n          })\r\n          this.arrListFormItemReq = [...this.mergeItems(this.arrListFormItemReq)]\r\n        }\r\n      })\r\n    ).subscribe();\r\n  }\r\n\r\n  listFormItem: GetListFormItemRes\r\n  isNew: boolean = true\r\n\r\n  getListFormItem() {\r\n    this._formItemService.apiFormItemGetListFormItemPost$Json({\r\n      body: {\r\n        CBuildCaseId: this.buildCaseId,\r\n        CFormType: this.typeContentManagementSalesAccount.CFormType,\r\n        CIsPaging: false\r\n      }\r\n    }).pipe(\r\n      tap(res => {\r\n        if (res.Entries && res.StatusCode == 0) {\r\n          this.listFormItem = res.Entries\r\n          this.isNew = res.Entries.formItems ? false : true\r\n          if (res.Entries.formItems) {\r\n\r\n            this.houseHoldList.forEach(item => this.selectedItems[item] = false)\r\n            this.CRemarkTypeOptions.forEach(item => this.selectedRemarkType[item] = false)\r\n\r\n            this.arrListFormItemReq = res.Entries.formItems.map((o: any) => {\r\n              return {\r\n                CFormId: this.listFormItem.CFormId,\r\n                CDesignFileUrl: o.CDesignFileUrl,\r\n                CFirstMatrialUrl: o.CFirstMatrialUrl,\r\n                CFile: o.CFile,\r\n                CFormItemHouseHold: o.CFormItemHouseHold,\r\n                CFormItemId: o.CFormItemId,\r\n                CLocation: o.CLocation,\r\n                CName: o.CName,\r\n                CPart: o.CPart,\r\n                CItemName: o.CItemName ? o.CItemName : `${o.CName}-${o.CPart}-${o.CLocation}`,\r\n                CRemarkType: o.CRemarkType,\r\n                CTotalAnswer: o.CTotalAnswer,\r\n                CRequireAnswer: o.CUiType===3 ? 1: o.CRequireAnswer,\r\n                CUiType: o.CUiType,\r\n                selectedItems: o.tblFormItemHouseholds.length ? this.createArrayObjectFromArray(this.houseHoldList, o.tblFormItemHouseholds) : { ...this.selectedItems },\r\n                selectedRemarkType: o.CRemarkType ? this.createRemarkObject(this.CRemarkTypeOptions, o.CRemarkType) : { ...this.selectedRemarkType },\r\n                allSelected: o.tblFormItemHouseholds.length === this.houseHoldList.length,\r\n                listPictures: [],\r\n                selectedCUiType: o.CUiType ? this.getItemByValue(o.CUiType, this.CUiTypeOptions) : this.CUiTypeOptions[0]\r\n              }\r\n            })\r\n          } else {\r\n            this.getMaterialList()\r\n          }\r\n        }\r\n      })\r\n    ).subscribe()\r\n  }\r\n\r\n  changeSelectCUiType(formItemReq: any) {\r\n    if(formItemReq.selectedCUiType && formItemReq.selectedCUiType.value ===3) {\r\n      formItemReq.CRequireAnswer = 1\r\n    }\r\n  }\r\n  getHouseHoldListByNoticeType(data: any[]) {\r\n    for (let item of data) {\r\n      if (item.CNoticeType === this.typeContentManagementSalesAccount.CNoticeType) {\r\n        return item.CHouseHoldList;\r\n      }\r\n    }\r\n    return [];\r\n  }\r\n\r\n  arrListFormItemReq: ExtendedSaveListFormItemReq[]\r\n\r\n  getKeysWithTrueValue(obj: Record<string, boolean>): string[] { // {\"key\": true } => [\"key\"]\r\n    return Object.keys(obj).filter(key => obj[key]);\r\n  }\r\n\r\n  getKeysWithTrueValueJoined(obj: Record<string, boolean>): string {\r\n    return Object.keys(obj)\r\n      .filter(key => obj[key])\r\n      .join('-');\r\n  }\r\n\r\n  getCRemarkType(selectedCUiType: any, selectedRemarkType: any): any {\r\n    if (selectedCUiType && selectedCUiType.value == 3) {\r\n      return this.getKeysWithTrueValueJoined(selectedRemarkType)\r\n    }\r\n  }\r\n\r\n  getStringAfterComma(inputString: string): string {\r\n    const parts = inputString.split(',');\r\n    if (parts.length > 1) {\r\n      return parts[1];\r\n    } else return \"\"\r\n  }\r\n\r\n  formatFile(listPictures: any) {\r\n    if (listPictures && listPictures.length > 0) {\r\n      return {\r\n        Base64String: this.getStringAfterComma(listPictures[0].data) || null,\r\n        FileExtension: listPictures[0].extension || null,\r\n        FileName: listPictures[0].CFile.name || listPictures[0].name || null,\r\n      }\r\n    } else return undefined\r\n\r\n  }\r\n\r\n\r\n  validation() {\r\n    this.valid.clear();\r\n    let hasInvalidCUiType = false;\r\n    let hasInvalidCRequireAnswer = false;\r\n    let hasInvalidItemName = false;\r\n\r\n    for (const item of this.saveListFormItemReq) {\r\n      if (!hasInvalidCUiType && (!item.CUiType)) {\r\n        hasInvalidCUiType = true;\r\n      }\r\n      if (!hasInvalidCRequireAnswer && (!item.CRequireAnswer)) {\r\n        hasInvalidCRequireAnswer = true;\r\n      }\r\n      if (item.CTotalAnswer && item.CRequireAnswer) {\r\n        if (item.CRequireAnswer > item.CTotalAnswer || item.CRequireAnswer < 0) {\r\n          this.valid.addErrorMessage('[必填數量]' + ' <= ' + item.CTotalAnswer + ` (${item.CItemName}) `);\r\n        }\r\n      }\r\n\r\n      if (!hasInvalidItemName && (!item.CItemName)) {\r\n        hasInvalidItemName = true;\r\n      }\r\n    }\r\n    if (hasInvalidCUiType) {\r\n      this.valid.addErrorMessage('[前台UI類型]' + ' 必填');\r\n    }\r\n    if (hasInvalidCRequireAnswer) {\r\n      this.valid.addErrorMessage('[必填數量]' + ' 必填且>0');\r\n    }\r\n    if (hasInvalidItemName) {\r\n      this.valid.addErrorMessage('[廚房-廚具-櫃體]' + ' 必填');\r\n    }\r\n  }\r\n\r\n  saveListFormItemReq: SaveListFormItemReq[]\r\n\r\n  onSubmit() {\r\n    this.saveListFormItemReq = this.arrListFormItemReq.map((e: any) => {\r\n      return {\r\n        CDesignFileUrl: e.CDesignFileUrl ? e.CDesignFileUrl : null,\r\n        CFile: e.listPictures ? this.formatFile(e.listPictures) : undefined,\r\n        CFormItemHouseHold: this.getKeysWithTrueValue(e.selectedItems),\r\n        CFormItemId: e.CFormItemId ? e.CFormItemId : null,\r\n        CFormID: this.isNew ? null : this.listFormItem.CFormId,\r\n        CName: e.CName,\r\n        CPart: e.CPart,\r\n        CLocation: e.CLocation,\r\n        CItemName: e.CItemName, //? e.CItemName : `${e.CName}-${e.CPart}-${e.CLocation}`,\r\n        CRemarkType: e.selectedCUiType.value !== 3 ? null : this.getCRemarkType(e.selectedCUiType, e.selectedRemarkType) || null,\r\n        CTotalAnswer: e.CTotalAnswer,\r\n        CRequireAnswer: e.CRequireAnswer,\r\n        CUiType: e.selectedCUiType.value,\r\n      }\r\n    })\r\n    this.validation()\r\n    if (this.valid.errorMessages.length > 0) {\r\n      this.message.showErrorMSGs(this.valid.errorMessages);\r\n      return\r\n    }\r\n    if (this.isNew) {\r\n      this.createListFormItem()\r\n\r\n    } else {\r\n      this.saveListFormItem()\r\n    }\r\n  }\r\n\r\n  saveListFormItem() {\r\n    this._formItemService.apiFormItemSaveListFormItemPost$Json({\r\n      body: this.saveListFormItemReq\r\n    }).subscribe(res => {\r\n      if (res.StatusCode == 0) {\r\n        this.message.showSucessMSG(\"執行成功\");\r\n        // this.getListFormItem()\r\n        this.goBack()\r\n      }\r\n    })\r\n  }\r\n\r\n  creatListFormItem: CreateListFormItem\r\n\r\n  createListFormItem() {\r\n    this.creatListFormItem = {\r\n      CBuildCaseId: this.buildCaseId,\r\n      CFormItem: this.saveListFormItemReq || null,\r\n      CFormType: this.typeContentManagementSalesAccount.CFormType,\r\n    }\r\n\r\n    this._formItemService.apiFormItemCreateListFormItemPost$Json({\r\n      body: this.creatListFormItem\r\n    }).subscribe(res => {\r\n      if (res.StatusCode == 0) {\r\n        this.message.showSucessMSG(\"執行成功\");\r\n        // this.getListFormItem()\r\n        this.goBack()\r\n      }\r\n    })\r\n  }\r\n\r\n  createArrayObjectFromArray(a: string[], b: { CFormItemHouseholdId: number, CFormItemId: number, CHousehold: string, CIsSelect: boolean }[]): { [key: string]: boolean } {\r\n    const c: { [key: string]: boolean } = {};\r\n    for (const item of a) {\r\n      const matchingItem = b.find(bItem => bItem.CHousehold === item && bItem.CIsSelect);\r\n      c[item] = !!matchingItem;\r\n    }\r\n    return c;\r\n  } //[\"House1\", \"House2\", \"House3\"] => [{CHousehold: \"House1\", CIsSelect: true,... }, ... ]\r\n\r\n\r\n  houseHoldList: any[]\r\n\r\n  getListRegularNoticeFileHouseHold() {\r\n    this._regularNoticeFileService.apiRegularNoticeFileGetListRegularNoticeFileHouseHoldPost$Json({\r\n      body: this.buildCaseId\r\n    }).pipe(\r\n      tap(res => {\r\n        if (res.Entries && res.StatusCode == 0) {\r\n          this.houseHoldList = this.getHouseHoldListByNoticeType(res.Entries)\r\n          this.getListFormItem()\r\n        }\r\n      })\r\n    ).subscribe()\r\n  }\r\n  goBack() {\r\n    this._eventService.push({\r\n      action: EEvent.GET_BUILDCASE,\r\n      payload: this.buildCaseId\r\n    })\r\n    this.location.back()\r\n  }\r\n}\r\n"], "mappings": ";AAAA,SAASA,SAAS,QAAgB,eAAe;AACjD,SAASC,YAAY,QAAkB,iBAAiB;AACxD,SAASC,gBAAgB,QAAQ,gBAAgB;AAKjD,SAASC,GAAG,QAAQ,MAAM;AAE1B,SAASC,YAAY,QAAQ,qCAAqC;AAGlE,SAASC,YAAY,QAAQ,wCAAwC;AACrE,SAASC,aAAa,QAAQ,6CAA6C;AAkCpE,IAAMC,4CAA4C,GAAlD,MAAMA,4CAA6C,SAAQD,aAAa;EAC7EE,YACUC,MAAmB,EACnBC,KAAqB,EACrBC,OAAuB,EACvBC,gBAAiC,EACjCC,yBAAmD,EACnDC,eAA+B,EAC/BC,KAAuB,EACvBC,QAAkB,EAClBC,gBAAiC,EACjCC,aAA2B;IAEnC,KAAK,CAACT,MAAM,CAAC;IAXL,KAAAA,MAAM,GAANA,MAAM;IACN,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,yBAAyB,GAAzBA,yBAAyB;IACzB,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,QAAQ,GAARA,QAAQ;IACR,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,aAAa,GAAbA,aAAa;IAKvB,KAAAC,iCAAiC,GAAG;MAClCC,SAAS,EAAE,CAAC;MACZC,WAAW,EAAE;KACd;IAED,KAAAC,cAAc,GAAU,CACtB;MACEC,KAAK,EAAE,CAAC;MAAEC,KAAK,EAAE;KAClB,EACD;MACED,KAAK,EAAE,CAAC;MAAEC,KAAK,EAAE;KAClB,EAAE;MACDD,KAAK,EAAE,CAAC;MAAEC,KAAK,EAAE;KAClB,CACF;IACD,KAAAC,kBAAkB,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC;IA2BjC,KAAAC,aAAa,GAA+B,EAAE;IAC9C,KAAAC,kBAAkB,GAA+B,EAAE;IAmJnD,KAAAC,KAAK,GAAY,IAAI;EAhMrB;EAoBSC,QAAQA,CAAA;IACf,IAAI,CAACnB,KAAK,CAACoB,QAAQ,CAACC,SAAS,CAACC,MAAM,IAAG;MACrC,IAAIA,MAAM,EAAE;QACV,MAAMC,OAAO,GAAGD,MAAM,CAACE,GAAG,CAAC,IAAI,CAAC;QAChC,MAAMC,EAAE,GAAGF,OAAO,GAAG,CAACA,OAAO,GAAG,CAAC;QACjC,IAAI,CAACG,WAAW,GAAGD,EAAE;QACrB,IAAI,IAAI,CAACC,WAAW,EAAE;UACpB,IAAI,CAACC,iCAAiC,EAAE;QAC1C;MACF;IACF,CAAC,CAAC;EACJ;EAGAC,cAAcA,CAACf,KAAU,EAAEgB,OAAc;IACvC,KAAK,MAAMC,IAAI,IAAID,OAAO,EAAE;MAC1B,IAAIC,IAAI,CAACjB,KAAK,KAAKA,KAAK,EAAE;QACxB,OAAOiB,IAAI;MACb;IACF;IACA,OAAO,IAAI;EACb;EAQAC,WAAWA,CAACC,KAAU,EAAEC,YAAiB;IACvC,MAAMC,IAAI,GAAGF,KAAK,CAACG,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC;IAClC,IAAIF,IAAI,EAAE;MACR,IAAIG,MAAM,GAAG,IAAIC,UAAU,EAAE;MAC7BD,MAAM,CAACE,aAAa,CAACL,IAAI,CAAC;MAC1BG,MAAM,CAACG,MAAM,GAAG,MAAK;QACnB,IAAIC,SAAS,GAAWJ,MAAM,CAACK,MAAgB;QAC/C,IAAI,CAACD,SAAS,EAAE;UACd;QACF;QACA,IAAIR,YAAY,CAACU,YAAY,CAACC,MAAM,GAAG,CAAC,EAAE;UACxCX,YAAY,CAACU,YAAY,CAAC,CAAC,CAAC,GAAG;YAC7BlB,EAAE,EAAE,IAAIoB,IAAI,EAAE,CAACC,OAAO,EAAE;YACxBC,IAAI,EAAEb,IAAI,CAACa,IAAI,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;YAC7BC,IAAI,EAAER,SAAS;YACfS,SAAS,EAAE,IAAI,CAAC9C,eAAe,CAAC+C,gBAAgB,CAACjB,IAAI,CAACa,IAAI,CAAC;YAC3DK,KAAK,EAAElB;WACR;QACH,CAAC,MAAM;UACLD,YAAY,CAACU,YAAY,CAACU,IAAI,CAAC;YAC7B5B,EAAE,EAAE,IAAIoB,IAAI,EAAE,CAACC,OAAO,EAAE;YACxBC,IAAI,EAAEb,IAAI,CAACa,IAAI,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;YAC7BC,IAAI,EAAER,SAAS;YACfS,SAAS,EAAE,IAAI,CAAC9C,eAAe,CAAC+C,gBAAgB,CAACjB,IAAI,CAACa,IAAI,CAAC;YAC3DK,KAAK,EAAElB;WACR,CAAC;QACJ;QACAF,KAAK,CAACG,MAAM,CAACtB,KAAK,GAAG,IAAI;MAC3B,CAAC;IACH;EACF;EAEAyC,WAAWA,CAACC,SAAiB,EAAEtB,YAAiB;IAC9C,IAAIA,YAAY,CAACU,YAAY,CAACC,MAAM,EAAE;MACpCX,YAAY,CAACU,YAAY,GAAGV,YAAY,CAACU,YAAY,CAACa,MAAM,CAAEC,CAAM,IAAKA,CAAC,CAAChC,EAAE,IAAI8B,SAAS,CAAC;IAC7F;EACF;EAEAG,UAAUA,CAAC1B,KAAU,EAAE2B,KAAa,EAAE1B,YAAiB;IACrD,IAAI2B,IAAI,GAAG3B,YAAY,CAACU,YAAY,CAACgB,KAAK,CAAC,CAACP,KAAK,CAACS,KAAK,CAAC,CAAC,EAAE5B,YAAY,CAACU,YAAY,CAACgB,KAAK,CAAC,CAACP,KAAK,CAACU,IAAI,EAAE7B,YAAY,CAACU,YAAY,CAACgB,KAAK,CAAC,CAACP,KAAK,CAACW,IAAI,CAAC;IACpJ,IAAIC,OAAO,GAAG,IAAIC,IAAI,CAAC,CAACL,IAAI,CAAC,EAAE,GAAG5B,KAAK,CAACG,MAAM,CAACtB,KAAK,GAAG,GAAG,GAAGoB,YAAY,CAACU,YAAY,CAACgB,KAAK,CAAC,CAACT,SAAS,EAAE,EAAE;MAAEa,IAAI,EAAE9B,YAAY,CAACU,YAAY,CAACgB,KAAK,CAAC,CAACP,KAAK,CAACW;IAAI,CAAE,CAAC;IACjK9B,YAAY,CAACU,YAAY,CAACgB,KAAK,CAAC,CAACP,KAAK,GAAGY,OAAO;EAClD;EAGAE,gBAAgBA,CAACC,OAAgB,EAAElC,YAAiB;IAClDA,YAAY,CAACmC,WAAW,GAAGD,OAAO;IAClC,IAAI,CAACE,aAAa,CAACC,OAAO,CAACxC,IAAI,IAAG;MAChCG,YAAY,CAACjB,aAAa,CAACc,IAAI,CAAC,GAAGqC,OAAO;IAC5C,CAAC,CAAC;EACJ;EAEAI,6BAA6BA,CAACJ,OAAgB,EAAErC,IAAY,EAAEG,YAAiB;IAC7E,IAAIkC,OAAO,EAAE;MACXlC,YAAY,CAACjB,aAAa,CAACc,IAAI,CAAC,GAAGqC,OAAO;MAC1ClC,YAAY,CAACmC,WAAW,GAAG,IAAI,CAACC,aAAa,CAACG,KAAK,CAAC1C,IAAI,IAAIG,YAAY,CAACjB,aAAa,CAACc,IAAI,CAAC,IAAIqC,OAAO,CAAC;IAC1G,CAAC,MAAM;MACLlC,YAAY,CAACmC,WAAW,GAAG,KAAK;IAClC;EACF;EAIAK,sBAAsBA,CAACN,OAAgB,EAAErC,IAAY,EAAEG,YAAiB;IACtEA,YAAY,CAAChB,kBAAkB,CAACa,IAAI,CAAC,GAAGqC,OAAO;EACjD;EAEAO,kBAAkBA,CAAC3D,kBAA4B,EAAE4D,WAAmB;IAClE,MAAMC,YAAY,GAA+B,EAAE;IACnD,KAAK,MAAMC,MAAM,IAAI9D,kBAAkB,EAAE;MACvC6D,YAAY,CAACC,MAAM,CAAC,GAAG,KAAK;IAC9B;IACA,MAAMC,WAAW,GAAGH,WAAW,CAAC3B,KAAK,CAAC,GAAG,CAAC;IAC1C,KAAK,MAAMe,IAAI,IAAIe,WAAW,EAAE;MAC9B,IAAI/D,kBAAkB,CAACgE,QAAQ,CAAChB,IAAI,CAAC,EAAE;QACrCa,YAAY,CAACb,IAAI,CAAC,GAAG,IAAI;MAC3B;IACF;IACA,OAAOa,YAAY;EACrB;EAEAI,UAAUA,CAACC,KAAoC;IAC7C,MAAMC,GAAG,GAAG,IAAIC,GAAG,EAAgE;IAEnFF,KAAK,CAACX,OAAO,CAACxC,IAAI,IAAG;MACnB,MAAMsD,GAAG,GAAG,GAAGtD,IAAI,CAACuD,SAAS,IAAIvD,IAAI,CAACwD,KAAK,IAAIxD,IAAI,CAACyD,KAAK,EAAE;MAC3D,IAAIL,GAAG,CAACM,GAAG,CAACJ,GAAG,CAAC,EAAE;QAChB,MAAMK,QAAQ,GAAGP,GAAG,CAAC1D,GAAG,CAAC4D,GAAG,CAAE;QAC9BK,QAAQ,CAACC,KAAK,IAAI,CAAC;MACrB,CAAC,MAAM;QACLR,GAAG,CAACS,GAAG,CAACP,GAAG,EAAE;UAAEtD,IAAI,EAAE;YAAE,GAAGA;UAAI,CAAE;UAAE4D,KAAK,EAAE;QAAC,CAAE,CAAC;MAC/C;IACF,CAAC,CAAC;IAEF,OAAOE,KAAK,CAACC,IAAI,CAACX,GAAG,CAACY,MAAM,EAAE,CAAC,CAACZ,GAAG,CAAC,CAAC;MAAEpD,IAAI;MAAE4D;IAAK,CAAE,MAAM;MACxD,GAAG5D,IAAI;MACPiE,YAAY,EAAEL;KACf,CAAC,CAAC;EACL;EAGAM,eAAeA,CAAA;IACb,IAAI,CAACzF,gBAAgB,CAAC0F,mCAAmC,CAAC;MACxDC,IAAI,EAAE;QACJC,YAAY,EAAE,IAAI,CAACzE,WAAW;QAC9B0E,KAAK,EAAE;;KAEV,CAAC,CAACC,IAAI,CACL5G,GAAG,CAAC6G,GAAG,IAAG;MACR,IAAIA,GAAG,CAACC,OAAO,IAAID,GAAG,CAACE,UAAU,IAAI,CAAC,EAAE;QAEtC,IAAI,CAACnC,aAAa,CAACC,OAAO,CAACxC,IAAI,IAAI,IAAI,CAACd,aAAa,CAACc,IAAI,CAAC,GAAG,KAAK,CAAC;QAEpE,IAAI,CAACf,kBAAkB,CAACuD,OAAO,CAACxC,IAAI,IAAI,IAAI,CAACb,kBAAkB,CAACa,IAAI,CAAC,GAAG,KAAK,CAAC;QAE9E,IAAI,CAAC2E,kBAAkB,GAAGH,GAAG,CAACC,OAAO,CAACrB,GAAG,CAAEwB,CAAM,IAAI;UACnD,OAAO;YACLC,cAAc,EAAE,IAAI;YACpBC,kBAAkB,EAAE,IAAI;YACxBC,OAAO,EAAE,IAAI;YACbxB,SAAS,EAAEqB,CAAC,CAACrB,SAAS;YACtBC,KAAK,EAAEoB,CAAC,CAACpB,KAAK;YACdC,KAAK,EAAEmB,CAAC,CAACnB,KAAK;YACduB,SAAS,EAAE,GAAGJ,CAAC,CAACpB,KAAK,IAAIoB,CAAC,CAACnB,KAAK,IAAImB,CAAC,CAACrB,SAAS,EAAE;YACjDV,WAAW,EAAE,IAAI;YACjBoB,YAAY,EAAE,CAAC;YACfgB,cAAc,EAAE,CAAC;YACjBC,OAAO,EAAE,CAAC;YACVhG,aAAa,EAAE,EAAE;YACjBC,kBAAkB,EAAE,IAAI,CAACA,kBAAkB;YAC3CmD,WAAW,EAAE,KAAK;YAClBzB,YAAY,EAAE,EAAE;YAChBsE,eAAe,EAAE,IAAI,CAACrG,cAAc,CAAC,CAAC,CAAC;YACvCsG,gBAAgB,EAAER,CAAC,CAACS;WACrB;QACH,CAAC,CAAC;QACF,IAAI,CAACV,kBAAkB,GAAG,CAAC,GAAG,IAAI,CAACzB,UAAU,CAAC,IAAI,CAACyB,kBAAkB,CAAC,CAAC;MACzE;IACF,CAAC,CAAC,CACH,CAACpF,SAAS,EAAE;EACf;EAKA+F,eAAeA,CAAA;IACb,IAAI,CAAClH,gBAAgB,CAACmH,mCAAmC,CAAC;MACxDnB,IAAI,EAAE;QACJC,YAAY,EAAE,IAAI,CAACzE,WAAW;QAC9BhB,SAAS,EAAE,IAAI,CAACD,iCAAiC,CAACC,SAAS;QAC3D4G,SAAS,EAAE;;KAEd,CAAC,CAACjB,IAAI,CACL5G,GAAG,CAAC6G,GAAG,IAAG;MACR,IAAIA,GAAG,CAACC,OAAO,IAAID,GAAG,CAACE,UAAU,IAAI,CAAC,EAAE;QACtC,IAAI,CAACe,YAAY,GAAGjB,GAAG,CAACC,OAAO;QAC/B,IAAI,CAACrF,KAAK,GAAGoF,GAAG,CAACC,OAAO,CAACiB,SAAS,GAAG,KAAK,GAAG,IAAI;QACjD,IAAIlB,GAAG,CAACC,OAAO,CAACiB,SAAS,EAAE;UAEzB,IAAI,CAACnD,aAAa,CAACC,OAAO,CAACxC,IAAI,IAAI,IAAI,CAACd,aAAa,CAACc,IAAI,CAAC,GAAG,KAAK,CAAC;UACpE,IAAI,CAACf,kBAAkB,CAACuD,OAAO,CAACxC,IAAI,IAAI,IAAI,CAACb,kBAAkB,CAACa,IAAI,CAAC,GAAG,KAAK,CAAC;UAE9E,IAAI,CAAC2E,kBAAkB,GAAGH,GAAG,CAACC,OAAO,CAACiB,SAAS,CAACtC,GAAG,CAAEwB,CAAM,IAAI;YAC7D,OAAO;cACLG,OAAO,EAAE,IAAI,CAACU,YAAY,CAACV,OAAO;cAClCF,cAAc,EAAED,CAAC,CAACC,cAAc;cAChCO,gBAAgB,EAAER,CAAC,CAACQ,gBAAgB;cACpC9D,KAAK,EAAEsD,CAAC,CAACtD,KAAK;cACdwD,kBAAkB,EAAEF,CAAC,CAACE,kBAAkB;cACxCa,WAAW,EAAEf,CAAC,CAACe,WAAW;cAC1BpC,SAAS,EAAEqB,CAAC,CAACrB,SAAS;cACtBC,KAAK,EAAEoB,CAAC,CAACpB,KAAK;cACdC,KAAK,EAAEmB,CAAC,CAACnB,KAAK;cACduB,SAAS,EAAEJ,CAAC,CAACI,SAAS,GAAGJ,CAAC,CAACI,SAAS,GAAG,GAAGJ,CAAC,CAACpB,KAAK,IAAIoB,CAAC,CAACnB,KAAK,IAAImB,CAAC,CAACrB,SAAS,EAAE;cAC7EV,WAAW,EAAE+B,CAAC,CAAC/B,WAAW;cAC1BoB,YAAY,EAAEW,CAAC,CAACX,YAAY;cAC5BgB,cAAc,EAAEL,CAAC,CAACM,OAAO,KAAG,CAAC,GAAG,CAAC,GAAEN,CAAC,CAACK,cAAc;cACnDC,OAAO,EAAEN,CAAC,CAACM,OAAO;cAClBhG,aAAa,EAAE0F,CAAC,CAACgB,qBAAqB,CAAC9E,MAAM,GAAG,IAAI,CAAC+E,0BAA0B,CAAC,IAAI,CAACtD,aAAa,EAAEqC,CAAC,CAACgB,qBAAqB,CAAC,GAAG;gBAAE,GAAG,IAAI,CAAC1G;cAAa,CAAE;cACxJC,kBAAkB,EAAEyF,CAAC,CAAC/B,WAAW,GAAG,IAAI,CAACD,kBAAkB,CAAC,IAAI,CAAC3D,kBAAkB,EAAE2F,CAAC,CAAC/B,WAAW,CAAC,GAAG;gBAAE,GAAG,IAAI,CAAC1D;cAAkB,CAAE;cACpImD,WAAW,EAAEsC,CAAC,CAACgB,qBAAqB,CAAC9E,MAAM,KAAK,IAAI,CAACyB,aAAa,CAACzB,MAAM;cACzED,YAAY,EAAE,EAAE;cAChBsE,eAAe,EAAEP,CAAC,CAACM,OAAO,GAAG,IAAI,CAACpF,cAAc,CAAC8E,CAAC,CAACM,OAAO,EAAE,IAAI,CAACpG,cAAc,CAAC,GAAG,IAAI,CAACA,cAAc,CAAC,CAAC;aACzG;UACH,CAAC,CAAC;QACJ,CAAC,MAAM;UACL,IAAI,CAACoF,eAAe,EAAE;QACxB;MACF;IACF,CAAC,CAAC,CACH,CAAC3E,SAAS,EAAE;EACf;EAEAuG,mBAAmBA,CAACC,WAAgB;IAClC,IAAGA,WAAW,CAACZ,eAAe,IAAIY,WAAW,CAACZ,eAAe,CAACpG,KAAK,KAAI,CAAC,EAAE;MACxEgH,WAAW,CAACd,cAAc,GAAG,CAAC;IAChC;EACF;EACAe,4BAA4BA,CAAC7E,IAAW;IACtC,KAAK,IAAInB,IAAI,IAAImB,IAAI,EAAE;MACrB,IAAInB,IAAI,CAACnB,WAAW,KAAK,IAAI,CAACF,iCAAiC,CAACE,WAAW,EAAE;QAC3E,OAAOmB,IAAI,CAACiG,cAAc;MAC5B;IACF;IACA,OAAO,EAAE;EACX;EAIAC,oBAAoBA,CAACC,GAA4B;IAC/C,OAAOC,MAAM,CAACC,IAAI,CAACF,GAAG,CAAC,CAACzE,MAAM,CAAC4B,GAAG,IAAI6C,GAAG,CAAC7C,GAAG,CAAC,CAAC;EACjD;EAEAgD,0BAA0BA,CAACH,GAA4B;IACrD,OAAOC,MAAM,CAACC,IAAI,CAACF,GAAG,CAAC,CACpBzE,MAAM,CAAC4B,GAAG,IAAI6C,GAAG,CAAC7C,GAAG,CAAC,CAAC,CACvBiD,IAAI,CAAC,GAAG,CAAC;EACd;EAEAC,cAAcA,CAACrB,eAAoB,EAAEhG,kBAAuB;IAC1D,IAAIgG,eAAe,IAAIA,eAAe,CAACpG,KAAK,IAAI,CAAC,EAAE;MACjD,OAAO,IAAI,CAACuH,0BAA0B,CAACnH,kBAAkB,CAAC;IAC5D;EACF;EAEAsH,mBAAmBA,CAACC,WAAmB;IACrC,MAAMC,KAAK,GAAGD,WAAW,CAACxF,KAAK,CAAC,GAAG,CAAC;IACpC,IAAIyF,KAAK,CAAC7F,MAAM,GAAG,CAAC,EAAE;MACpB,OAAO6F,KAAK,CAAC,CAAC,CAAC;IACjB,CAAC,MAAM,OAAO,EAAE;EAClB;EAEAC,UAAUA,CAAC/F,YAAiB;IAC1B,IAAIA,YAAY,IAAIA,YAAY,CAACC,MAAM,GAAG,CAAC,EAAE;MAC3C,OAAO;QACL+F,YAAY,EAAE,IAAI,CAACJ,mBAAmB,CAAC5F,YAAY,CAAC,CAAC,CAAC,CAACM,IAAI,CAAC,IAAI,IAAI;QACpE2F,aAAa,EAAEjG,YAAY,CAAC,CAAC,CAAC,CAACO,SAAS,IAAI,IAAI;QAChD2F,QAAQ,EAAElG,YAAY,CAAC,CAAC,CAAC,CAACS,KAAK,CAACL,IAAI,IAAIJ,YAAY,CAAC,CAAC,CAAC,CAACI,IAAI,IAAI;OACjE;IACH,CAAC,MAAM,OAAO+F,SAAS;EAEzB;EAGAC,UAAUA,CAAA;IACR,IAAI,CAAC1I,KAAK,CAAC2I,KAAK,EAAE;IAClB,IAAIC,iBAAiB,GAAG,KAAK;IAC7B,IAAIC,wBAAwB,GAAG,KAAK;IACpC,IAAIC,kBAAkB,GAAG,KAAK;IAE9B,KAAK,MAAMrH,IAAI,IAAI,IAAI,CAACsH,mBAAmB,EAAE;MAC3C,IAAI,CAACH,iBAAiB,IAAK,CAACnH,IAAI,CAACkF,OAAQ,EAAE;QACzCiC,iBAAiB,GAAG,IAAI;MAC1B;MACA,IAAI,CAACC,wBAAwB,IAAK,CAACpH,IAAI,CAACiF,cAAe,EAAE;QACvDmC,wBAAwB,GAAG,IAAI;MACjC;MACA,IAAIpH,IAAI,CAACiE,YAAY,IAAIjE,IAAI,CAACiF,cAAc,EAAE;QAC5C,IAAIjF,IAAI,CAACiF,cAAc,GAAGjF,IAAI,CAACiE,YAAY,IAAIjE,IAAI,CAACiF,cAAc,GAAG,CAAC,EAAE;UACtE,IAAI,CAAC1G,KAAK,CAACgJ,eAAe,CAAC,QAAQ,GAAG,MAAM,GAAGvH,IAAI,CAACiE,YAAY,GAAG,KAAKjE,IAAI,CAACgF,SAAS,IAAI,CAAC;QAC7F;MACF;MAEA,IAAI,CAACqC,kBAAkB,IAAK,CAACrH,IAAI,CAACgF,SAAU,EAAE;QAC5CqC,kBAAkB,GAAG,IAAI;MAC3B;IACF;IACA,IAAIF,iBAAiB,EAAE;MACrB,IAAI,CAAC5I,KAAK,CAACgJ,eAAe,CAAC,UAAU,GAAG,KAAK,CAAC;IAChD;IACA,IAAIH,wBAAwB,EAAE;MAC5B,IAAI,CAAC7I,KAAK,CAACgJ,eAAe,CAAC,QAAQ,GAAG,QAAQ,CAAC;IACjD;IACA,IAAIF,kBAAkB,EAAE;MACtB,IAAI,CAAC9I,KAAK,CAACgJ,eAAe,CAAC,YAAY,GAAG,KAAK,CAAC;IAClD;EACF;EAIAC,QAAQA,CAAA;IACN,IAAI,CAACF,mBAAmB,GAAG,IAAI,CAAC3C,kBAAkB,CAACvB,GAAG,CAAEqE,CAAM,IAAI;MAChE,OAAO;QACL5C,cAAc,EAAE4C,CAAC,CAAC5C,cAAc,GAAG4C,CAAC,CAAC5C,cAAc,GAAG,IAAI;QAC1DvD,KAAK,EAAEmG,CAAC,CAAC5G,YAAY,GAAG,IAAI,CAAC+F,UAAU,CAACa,CAAC,CAAC5G,YAAY,CAAC,GAAGmG,SAAS;QACnElC,kBAAkB,EAAE,IAAI,CAACoB,oBAAoB,CAACuB,CAAC,CAACvI,aAAa,CAAC;QAC9DyG,WAAW,EAAE8B,CAAC,CAAC9B,WAAW,GAAG8B,CAAC,CAAC9B,WAAW,GAAG,IAAI;QACjD+B,OAAO,EAAE,IAAI,CAACtI,KAAK,GAAG,IAAI,GAAG,IAAI,CAACqG,YAAY,CAACV,OAAO;QACtDvB,KAAK,EAAEiE,CAAC,CAACjE,KAAK;QACdC,KAAK,EAAEgE,CAAC,CAAChE,KAAK;QACdF,SAAS,EAAEkE,CAAC,CAAClE,SAAS;QACtByB,SAAS,EAAEyC,CAAC,CAACzC,SAAS;QAAE;QACxBnC,WAAW,EAAE4E,CAAC,CAACtC,eAAe,CAACpG,KAAK,KAAK,CAAC,GAAG,IAAI,GAAG,IAAI,CAACyH,cAAc,CAACiB,CAAC,CAACtC,eAAe,EAAEsC,CAAC,CAACtI,kBAAkB,CAAC,IAAI,IAAI;QACxH8E,YAAY,EAAEwD,CAAC,CAACxD,YAAY;QAC5BgB,cAAc,EAAEwC,CAAC,CAACxC,cAAc;QAChCC,OAAO,EAAEuC,CAAC,CAACtC,eAAe,CAACpG;OAC5B;IACH,CAAC,CAAC;IACF,IAAI,CAACkI,UAAU,EAAE;IACjB,IAAI,IAAI,CAAC1I,KAAK,CAACoJ,aAAa,CAAC7G,MAAM,GAAG,CAAC,EAAE;MACvC,IAAI,CAAC3C,OAAO,CAACyJ,aAAa,CAAC,IAAI,CAACrJ,KAAK,CAACoJ,aAAa,CAAC;MACpD;IACF;IACA,IAAI,IAAI,CAACvI,KAAK,EAAE;MACd,IAAI,CAACyI,kBAAkB,EAAE;IAE3B,CAAC,MAAM;MACL,IAAI,CAACC,gBAAgB,EAAE;IACzB;EACF;EAEAA,gBAAgBA,CAAA;IACd,IAAI,CAAC1J,gBAAgB,CAAC2J,oCAAoC,CAAC;MACzD3D,IAAI,EAAE,IAAI,CAACkD;KACZ,CAAC,CAAC/H,SAAS,CAACiF,GAAG,IAAG;MACjB,IAAIA,GAAG,CAACE,UAAU,IAAI,CAAC,EAAE;QACvB,IAAI,CAACvG,OAAO,CAAC6J,aAAa,CAAC,MAAM,CAAC;QAClC;QACA,IAAI,CAACC,MAAM,EAAE;MACf;IACF,CAAC,CAAC;EACJ;EAIAJ,kBAAkBA,CAAA;IAChB,IAAI,CAACK,iBAAiB,GAAG;MACvB7D,YAAY,EAAE,IAAI,CAACzE,WAAW;MAC9BuI,SAAS,EAAE,IAAI,CAACb,mBAAmB,IAAI,IAAI;MAC3C1I,SAAS,EAAE,IAAI,CAACD,iCAAiC,CAACC;KACnD;IAED,IAAI,CAACR,gBAAgB,CAACgK,sCAAsC,CAAC;MAC3DhE,IAAI,EAAE,IAAI,CAAC8D;KACZ,CAAC,CAAC3I,SAAS,CAACiF,GAAG,IAAG;MACjB,IAAIA,GAAG,CAACE,UAAU,IAAI,CAAC,EAAE;QACvB,IAAI,CAACvG,OAAO,CAAC6J,aAAa,CAAC,MAAM,CAAC;QAClC;QACA,IAAI,CAACC,MAAM,EAAE;MACf;IACF,CAAC,CAAC;EACJ;EAEApC,0BAA0BA,CAACwC,CAAW,EAAEC,CAAkG;IACxI,MAAMC,CAAC,GAA+B,EAAE;IACxC,KAAK,MAAMvI,IAAI,IAAIqI,CAAC,EAAE;MACpB,MAAMG,YAAY,GAAGF,CAAC,CAACG,IAAI,CAACC,KAAK,IAAIA,KAAK,CAACC,UAAU,KAAK3I,IAAI,IAAI0I,KAAK,CAACE,SAAS,CAAC;MAClFL,CAAC,CAACvI,IAAI,CAAC,GAAG,CAAC,CAACwI,YAAY;IAC1B;IACA,OAAOD,CAAC;EACV,CAAC,CAAC;EAKF1I,iCAAiCA,CAAA;IAC/B,IAAI,CAACxB,yBAAyB,CAACwK,8DAA8D,CAAC;MAC5FzE,IAAI,EAAE,IAAI,CAACxE;KACZ,CAAC,CAAC2E,IAAI,CACL5G,GAAG,CAAC6G,GAAG,IAAG;MACR,IAAIA,GAAG,CAACC,OAAO,IAAID,GAAG,CAACE,UAAU,IAAI,CAAC,EAAE;QACtC,IAAI,CAACnC,aAAa,GAAG,IAAI,CAACyD,4BAA4B,CAACxB,GAAG,CAACC,OAAO,CAAC;QACnE,IAAI,CAACa,eAAe,EAAE;MACxB;IACF,CAAC,CAAC,CACH,CAAC/F,SAAS,EAAE;EACf;EACA0I,MAAMA,CAAA;IACJ,IAAI,CAACvJ,aAAa,CAAC6C,IAAI,CAAC;MACtBuH,MAAM;MACNC,OAAO,EAAE,IAAI,CAACnJ;KACf,CAAC;IACF,IAAI,CAACpB,QAAQ,CAACwK,IAAI,EAAE;EACtB;CACD;AArbYjL,4CAA4C,GAAAkL,UAAA,EARxDzL,SAAS,CAAC;EACT0L,QAAQ,EAAE,6CAA6C;EACvDC,WAAW,EAAE,0DAA0D;EACvEC,SAAS,EAAE,CAAC,0DAA0D,CAAC;EACvEC,UAAU,EAAE,IAAI;EAChBC,OAAO,EAAE,CAAC7L,YAAY,EAAEI,YAAY,EAAEH,gBAAgB,EAAEE,YAAY;CACrE,CAAC,C,EAEWG,4CAA4C,CAqbxD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}