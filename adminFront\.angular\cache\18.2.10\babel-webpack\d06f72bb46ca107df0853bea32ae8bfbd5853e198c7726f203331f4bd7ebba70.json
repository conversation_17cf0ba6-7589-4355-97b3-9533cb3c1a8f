{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { Component } from '@angular/core';\nimport { BaseComponent } from '../../components/base/baseComponent';\nlet StandardHousePlanComponent = class StandardHousePlanComponent extends BaseComponent {\n  constructor(_allow, dialogService, _houseService, route, message) {\n    super(_allow);\n    this._allow = _allow;\n    this.dialogService = dialogService;\n    this._houseService = _houseService;\n    this.route = route;\n    this.message = message;\n    this.buildingSelectedOptions = [{\n      value: '',\n      label: '全部'\n    }];\n  }\n  getListBuilding() {\n    this._houseService.apiHouseGetListBuildingPost$Json({\n      body: {\n        CBuildCaseID: this.buildCaseId\n      }\n    }).subscribe(res => {\n      if (res.Entries && res.StatusCode == 0) {\n        this.buildingSelectedOptions = [{\n          value: '',\n          label: '全部'\n        }, ...res.Entries.map(e => {\n          return {\n            value: e,\n            label: e\n          };\n        })];\n        this.selectedBuilding = this.buildingSelectedOptions[0];\n        this.getListHouseRegularPic();\n      }\n    });\n  }\n  getListHouseRegularPic() {\n    let param = {\n      CBuildCaseID: this.buildCaseId,\n      CBuildingName: this.selectedBuilding.value,\n      PageIndex: this.pageIndex,\n      PageSize: this.pageSize\n    };\n    if (!this.selectedBuilding.value) {\n      delete param.CBuildingName;\n    }\n    this._houseService.apiHouseGetListHouseRegularPicPost$Json({\n      body: param\n    }).subscribe(res => {\n      if (res.Entries && res.StatusCode == 0) {\n        this.listHouseRegularPic = res.Entries ?? [];\n        this.totalRecords = res.TotalItems;\n      }\n    });\n  }\n  checkAll(checked, houseRegularPic) {\n    if (houseRegularPic.CHouse && houseRegularPic.CHouse.length > 0) {\n      houseRegularPic.CHouse.forEach(item => item.CIsSelect = checked);\n    }\n    if (checked) {\n      this.listHouseRegularPic.forEach((element, index) => {\n        if (element.CRegularPictureID !== houseRegularPic.CRegularPictureID) {\n          if (element.CHouse && Array.isArray(element.CHouse)) {\n            element.CHouse.forEach((item, o) => {\n              item.CIsSelect = false;\n            });\n          }\n        }\n      });\n    }\n  }\n  checkItem(checked, idx, i) {\n    if (checked) {\n      this.listHouseRegularPic.forEach((element, index) => {\n        if (index !== idx) {\n          if (element.CHouse && Array.isArray(element.CHouse)) {\n            element.CHouse.forEach((item, o) => {\n              if (item.CHouseID === i.CHouseID) {\n                item.CIsSelect = false;\n              }\n            });\n          }\n        }\n      });\n    }\n  }\n  isAllChecked(houseRegularPic) {\n    return houseRegularPic.CHouse.every(item => item.CIsSelect);\n  }\n  extractSelectedHouses(data) {\n    const result = [];\n    for (const item of data) {\n      for (const house of item.CHouse) {\n        if (house.CIsSelect) {\n          result.push({\n            CHouseID: house.CHouseID,\n            CRegularPictureID: item.CRegularPictureID\n          });\n        }\n      }\n    }\n    return result;\n  }\n  submitEditHouseRegularPic(ref) {\n    let bodyHouseRegularPic = this.extractSelectedHouses(this.listHouseRegularPic);\n    this._houseService.apiHouseEditHouseRegularPicPost$Json({\n      body: {\n        CHousePic: bodyHouseRegularPic\n      }\n    }).subscribe(res => {\n      if (res.StatusCode === 0) {\n        this.message.showSucessMSG(\"執行成功\");\n        ref.close();\n      }\n    });\n  }\n  onDeleteHouseRegularPic(houseRegularPic) {\n    if (window.confirm(`確定要刪除【項目${houseRegularPic.CFileName}】?`)) {\n      this._houseService.apiHouseDeleteRegularPicturePost$Json({\n        body: {\n          CRegularPictureID: houseRegularPic.CRegularPictureID\n        }\n      }).subscribe(res => {\n        if (res.StatusCode === 0) {\n          this.message.showSucessMSG(\"執行成功\");\n          this.getListHouseRegularPic();\n        }\n      });\n    }\n  }\n  clear() {\n    this.selectedBuilding = this.buildingSelectedOptions[0];\n  }\n  ngOnInit() {\n    this.route.paramMap.subscribe(params => {\n      if (params) {\n        const idParam = params.get('id');\n        const id = idParam ? +idParam : 0;\n        this.buildCaseId = id;\n        this.getListBuilding();\n      }\n    });\n  }\n  pageChanged(newPage) {\n    this.pageIndex = newPage;\n    this.getListHouseRegularPic();\n  }\n  onOpen(ref) {\n    this.dialogService.open(ref);\n  }\n  onClose(ref) {\n    ref.close();\n  }\n};\nStandardHousePlanComponent = __decorate([Component({\n  selector: 'ngx-standard-house-plan',\n  templateUrl: './standard-house-plan.component.html',\n  styleUrls: ['./standard-house-plan.component.scss']\n})], StandardHousePlanComponent);\nexport { StandardHousePlanComponent };", "map": {"version": 3, "names": ["Component", "BaseComponent", "StandardHousePlanComponent", "constructor", "_allow", "dialogService", "_houseService", "route", "message", "buildingSelectedOptions", "value", "label", "getListBuilding", "apiHouseGetListBuildingPost$Json", "body", "CBuildCaseID", "buildCaseId", "subscribe", "res", "Entries", "StatusCode", "map", "e", "selectedBuilding", "getListHouseRegularPic", "param", "CBuildingName", "PageIndex", "pageIndex", "PageSize", "pageSize", "apiHouseGetListHouseRegularPicPost$Json", "listHouseRegularPic", "totalRecords", "TotalItems", "checkAll", "checked", "houseRegularPic", "CHouse", "length", "for<PERSON>ach", "item", "CIsSelect", "element", "index", "CRegularPictureID", "Array", "isArray", "o", "checkItem", "idx", "i", "CHouseID", "isAllChecked", "every", "extractSelectedHouses", "data", "result", "house", "push", "submitEditHouseRegularPic", "ref", "bodyHouseRegularPic", "apiHouseEditHouseRegularPicPost$Json", "CHousePic", "showSucessMSG", "close", "onDeleteHouseRegularPic", "window", "confirm", "CFileName", "apiHouseDeleteRegularPicturePost$Json", "clear", "ngOnInit", "paramMap", "params", "idParam", "get", "id", "pageChanged", "newPage", "onOpen", "open", "onClose", "__decorate", "selector", "templateUrl", "styleUrls"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\household-management\\standard-house-plan\\standard-house-plan.component.ts"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { SharedModule } from '../../components/shared.module';\r\nimport { CommonModule } from '@angular/common';\r\nimport { NbDialogService } from '@nebular/theme';\r\nimport { BaseComponent } from '../../components/base/baseComponent';\r\nimport { AllowHelper } from 'src/app/shared/helper/allowHelper';\r\nimport { HouseService } from 'src/services/api/services';\r\nimport { ActivatedRoute } from '@angular/router';\r\nimport { EditHouseRegularPicture, GetListHouseRegularPicRes } from 'src/services/api/models';\r\nimport { MessageService } from 'src/app/shared/services/message.service';\r\n\r\nexport interface selectItem {\r\n  label: string,\r\n  value: number,\r\n  key?: string\r\n}\r\n\r\n@Component({\r\n  selector: 'ngx-standard-house-plan',\r\n  templateUrl: './standard-house-plan.component.html',\r\n  styleUrls: ['./standard-house-plan.component.scss'],\r\n})\r\n\r\nexport class StandardHousePlanComponent extends BaseComponent implements OnInit {\r\n  constructor(\r\n    private _allow: AllowHelper,\r\n    private dialogService: NbDialogService,\r\n    private _houseService: HouseService,\r\n    private route: ActivatedRoute,\r\n    private message: MessageService,\r\n  ) {\r\n    super(_allow)\r\n  }\r\n\r\n\r\n  selectedBuilding: any\r\n  buildingSelectedOptions: any[] = [\r\n    {\r\n      value: '', label: '全部'\r\n    }\r\n  ]\r\n\r\n  getListBuilding() {\r\n    this._houseService.apiHouseGetListBuildingPost$Json({\r\n      body: {\r\n        CBuildCaseID: this.buildCaseId\r\n      }\r\n    }).subscribe(res => {\r\n      if (res.Entries && res.StatusCode == 0) {\r\n        this.buildingSelectedOptions = [{\r\n          value: '', label: '全部'\r\n        }, ...res.Entries.map(e => {\r\n          return { value: e, label: e }\r\n        })]\r\n        this.selectedBuilding = this.buildingSelectedOptions[0]\r\n        this.getListHouseRegularPic()\r\n      }\r\n    })\r\n  }\r\n\r\n  listHouseRegularPic: GetListHouseRegularPicRes[]\r\n\r\n\r\n  getListHouseRegularPic() {\r\n    let param = {\r\n      CBuildCaseID: this.buildCaseId,\r\n      CBuildingName: this.selectedBuilding.value,\r\n      PageIndex: this.pageIndex,\r\n      PageSize: this.pageSize\r\n    }\r\n    if (!this.selectedBuilding.value) {\r\n      delete param.CBuildingName\r\n    }\r\n    this._houseService.apiHouseGetListHouseRegularPicPost$Json({\r\n      body: param\r\n    }).subscribe(res => {\r\n      if (res.Entries && res.StatusCode == 0) {\r\n        this.listHouseRegularPic = res.Entries! ?? []\r\n        this.totalRecords = res.TotalItems!\r\n      }\r\n    })\r\n  }\r\n\r\n  checkAll(checked: boolean, houseRegularPic: any) {\r\n    if (houseRegularPic.CHouse && houseRegularPic.CHouse.length > 0) {\r\n      houseRegularPic.CHouse.forEach((item: { CIsSelect: boolean; }) => (item.CIsSelect = checked));\r\n    }\r\n    if (checked) {\r\n      this.listHouseRegularPic.forEach((element, index) => {\r\n        if (element.CRegularPictureID !== houseRegularPic.CRegularPictureID) {\r\n          if (element.CHouse && Array.isArray(element.CHouse)) {\r\n            element.CHouse.forEach((item, o) => {\r\n              item.CIsSelect = false\r\n            })\r\n          }\r\n        }\r\n      })\r\n    }\r\n  }\r\n\r\n  checkItem(checked: boolean, idx: any, i: any) {\r\n    if (checked) {\r\n      this.listHouseRegularPic.forEach((element, index) => {\r\n        if (index !== idx) {\r\n          if (element.CHouse && Array.isArray(element.CHouse)) {\r\n            element.CHouse.forEach((item, o) => {\r\n              if (item.CHouseID === i.CHouseID) {\r\n                item.CIsSelect = false\r\n              }\r\n            });\r\n          }\r\n        }\r\n      })\r\n    }\r\n  }\r\n\r\n  isAllChecked(houseRegularPic: any): boolean {\r\n    return houseRegularPic.CHouse.every((item: { CIsSelect: any; }) => item.CIsSelect);\r\n  }\r\n\r\n\r\n  extractSelectedHouses(data: any[]): EditHouseRegularPicture[] {\r\n    const result: EditHouseRegularPicture[] = [];\r\n    for (const item of data) {\r\n      for (const house of item.CHouse) {\r\n        if (house.CIsSelect) {\r\n          result.push({\r\n            CHouseID: house.CHouseID,\r\n            CRegularPictureID: item.CRegularPictureID\r\n          });\r\n        }\r\n      }\r\n    }\r\n    return result;\r\n  }\r\n\r\n  submitEditHouseRegularPic(ref: any) {\r\n    let bodyHouseRegularPic = this.extractSelectedHouses(this.listHouseRegularPic)\r\n    this._houseService.apiHouseEditHouseRegularPicPost$Json( { body: {CHousePic : bodyHouseRegularPic}\r\n    }).subscribe(res => {\r\n      if (res.StatusCode === 0) {\r\n        this.message.showSucessMSG(\"執行成功\");\r\n        ref.close();\r\n      }\r\n    })\r\n  }\r\n\r\n  onDeleteHouseRegularPic(houseRegularPic: any) {\r\n    if (window.confirm(`確定要刪除【項目${houseRegularPic.CFileName}】?`)) {\r\n    this._houseService.apiHouseDeleteRegularPicturePost$Json({body : { CRegularPictureID: houseRegularPic.CRegularPictureID}}).subscribe(res => {\r\n      if (res.StatusCode === 0) {\r\n        this.message.showSucessMSG(\"執行成功\");\r\n        this.getListHouseRegularPic()\r\n      }\r\n    })\r\n  }\r\n  }\r\n\r\n  clear() {\r\n    this.selectedBuilding = this.buildingSelectedOptions[0]\r\n  }\r\n\r\n\r\n  buildCaseId: number\r\n\r\n  override ngOnInit(): void {\r\n    this.route.paramMap.subscribe(params => {\r\n      if (params) {\r\n        const idParam = params.get('id');\r\n        const id = idParam ? +idParam : 0;\r\n        this.buildCaseId = id\r\n        this.getListBuilding()\r\n      }\r\n    });\r\n  }\r\n\r\n  pageChanged(newPage: number) {\r\n    this.pageIndex = newPage;\r\n    this.getListHouseRegularPic()\r\n  }\r\n\r\n\r\n  onOpen(ref: any) {\r\n    this.dialogService.open(ref)\r\n  }\r\n\r\n  onClose(ref: any) {\r\n    ref.close();\r\n  }\r\n\r\n}\r\n"], "mappings": ";AAAA,SAASA,SAAS,QAAgB,eAAe;AAIjD,SAASC,aAAa,QAAQ,qCAAqC;AAmB5D,IAAMC,0BAA0B,GAAhC,MAAMA,0BAA2B,SAAQD,aAAa;EAC3DE,YACUC,MAAmB,EACnBC,aAA8B,EAC9BC,aAA2B,EAC3BC,KAAqB,EACrBC,OAAuB;IAE/B,KAAK,CAACJ,MAAM,CAAC;IANL,KAAAA,MAAM,GAANA,MAAM;IACN,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,OAAO,GAAPA,OAAO;IAOjB,KAAAC,uBAAuB,GAAU,CAC/B;MACEC,KAAK,EAAE,EAAE;MAAEC,KAAK,EAAE;KACnB,CACF;EARD;EAUAC,eAAeA,CAAA;IACb,IAAI,CAACN,aAAa,CAACO,gCAAgC,CAAC;MAClDC,IAAI,EAAE;QACJC,YAAY,EAAE,IAAI,CAACC;;KAEtB,CAAC,CAACC,SAAS,CAACC,GAAG,IAAG;MACjB,IAAIA,GAAG,CAACC,OAAO,IAAID,GAAG,CAACE,UAAU,IAAI,CAAC,EAAE;QACtC,IAAI,CAACX,uBAAuB,GAAG,CAAC;UAC9BC,KAAK,EAAE,EAAE;UAAEC,KAAK,EAAE;SACnB,EAAE,GAAGO,GAAG,CAACC,OAAO,CAACE,GAAG,CAACC,CAAC,IAAG;UACxB,OAAO;YAAEZ,KAAK,EAAEY,CAAC;YAAEX,KAAK,EAAEW;UAAC,CAAE;QAC/B,CAAC,CAAC,CAAC;QACH,IAAI,CAACC,gBAAgB,GAAG,IAAI,CAACd,uBAAuB,CAAC,CAAC,CAAC;QACvD,IAAI,CAACe,sBAAsB,EAAE;MAC/B;IACF,CAAC,CAAC;EACJ;EAKAA,sBAAsBA,CAAA;IACpB,IAAIC,KAAK,GAAG;MACVV,YAAY,EAAE,IAAI,CAACC,WAAW;MAC9BU,aAAa,EAAE,IAAI,CAACH,gBAAgB,CAACb,KAAK;MAC1CiB,SAAS,EAAE,IAAI,CAACC,SAAS;MACzBC,QAAQ,EAAE,IAAI,CAACC;KAChB;IACD,IAAI,CAAC,IAAI,CAACP,gBAAgB,CAACb,KAAK,EAAE;MAChC,OAAOe,KAAK,CAACC,aAAa;IAC5B;IACA,IAAI,CAACpB,aAAa,CAACyB,uCAAuC,CAAC;MACzDjB,IAAI,EAAEW;KACP,CAAC,CAACR,SAAS,CAACC,GAAG,IAAG;MACjB,IAAIA,GAAG,CAACC,OAAO,IAAID,GAAG,CAACE,UAAU,IAAI,CAAC,EAAE;QACtC,IAAI,CAACY,mBAAmB,GAAGd,GAAG,CAACC,OAAQ,IAAI,EAAE;QAC7C,IAAI,CAACc,YAAY,GAAGf,GAAG,CAACgB,UAAW;MACrC;IACF,CAAC,CAAC;EACJ;EAEAC,QAAQA,CAACC,OAAgB,EAAEC,eAAoB;IAC7C,IAAIA,eAAe,CAACC,MAAM,IAAID,eAAe,CAACC,MAAM,CAACC,MAAM,GAAG,CAAC,EAAE;MAC/DF,eAAe,CAACC,MAAM,CAACE,OAAO,CAAEC,IAA6B,IAAMA,IAAI,CAACC,SAAS,GAAGN,OAAQ,CAAC;IAC/F;IACA,IAAIA,OAAO,EAAE;MACX,IAAI,CAACJ,mBAAmB,CAACQ,OAAO,CAAC,CAACG,OAAO,EAAEC,KAAK,KAAI;QAClD,IAAID,OAAO,CAACE,iBAAiB,KAAKR,eAAe,CAACQ,iBAAiB,EAAE;UACnE,IAAIF,OAAO,CAACL,MAAM,IAAIQ,KAAK,CAACC,OAAO,CAACJ,OAAO,CAACL,MAAM,CAAC,EAAE;YACnDK,OAAO,CAACL,MAAM,CAACE,OAAO,CAAC,CAACC,IAAI,EAAEO,CAAC,KAAI;cACjCP,IAAI,CAACC,SAAS,GAAG,KAAK;YACxB,CAAC,CAAC;UACJ;QACF;MACF,CAAC,CAAC;IACJ;EACF;EAEAO,SAASA,CAACb,OAAgB,EAAEc,GAAQ,EAAEC,CAAM;IAC1C,IAAIf,OAAO,EAAE;MACX,IAAI,CAACJ,mBAAmB,CAACQ,OAAO,CAAC,CAACG,OAAO,EAAEC,KAAK,KAAI;QAClD,IAAIA,KAAK,KAAKM,GAAG,EAAE;UACjB,IAAIP,OAAO,CAACL,MAAM,IAAIQ,KAAK,CAACC,OAAO,CAACJ,OAAO,CAACL,MAAM,CAAC,EAAE;YACnDK,OAAO,CAACL,MAAM,CAACE,OAAO,CAAC,CAACC,IAAI,EAAEO,CAAC,KAAI;cACjC,IAAIP,IAAI,CAACW,QAAQ,KAAKD,CAAC,CAACC,QAAQ,EAAE;gBAChCX,IAAI,CAACC,SAAS,GAAG,KAAK;cACxB;YACF,CAAC,CAAC;UACJ;QACF;MACF,CAAC,CAAC;IACJ;EACF;EAEAW,YAAYA,CAAChB,eAAoB;IAC/B,OAAOA,eAAe,CAACC,MAAM,CAACgB,KAAK,CAAEb,IAAyB,IAAKA,IAAI,CAACC,SAAS,CAAC;EACpF;EAGAa,qBAAqBA,CAACC,IAAW;IAC/B,MAAMC,MAAM,GAA8B,EAAE;IAC5C,KAAK,MAAMhB,IAAI,IAAIe,IAAI,EAAE;MACvB,KAAK,MAAME,KAAK,IAAIjB,IAAI,CAACH,MAAM,EAAE;QAC/B,IAAIoB,KAAK,CAAChB,SAAS,EAAE;UACnBe,MAAM,CAACE,IAAI,CAAC;YACVP,QAAQ,EAAEM,KAAK,CAACN,QAAQ;YACxBP,iBAAiB,EAAEJ,IAAI,CAACI;WACzB,CAAC;QACJ;MACF;IACF;IACA,OAAOY,MAAM;EACf;EAEAG,yBAAyBA,CAACC,GAAQ;IAChC,IAAIC,mBAAmB,GAAG,IAAI,CAACP,qBAAqB,CAAC,IAAI,CAACvB,mBAAmB,CAAC;IAC9E,IAAI,CAAC1B,aAAa,CAACyD,oCAAoC,CAAE;MAAEjD,IAAI,EAAE;QAACkD,SAAS,EAAGF;MAAmB;KAChG,CAAC,CAAC7C,SAAS,CAACC,GAAG,IAAG;MACjB,IAAIA,GAAG,CAACE,UAAU,KAAK,CAAC,EAAE;QACxB,IAAI,CAACZ,OAAO,CAACyD,aAAa,CAAC,MAAM,CAAC;QAClCJ,GAAG,CAACK,KAAK,EAAE;MACb;IACF,CAAC,CAAC;EACJ;EAEAC,uBAAuBA,CAAC9B,eAAoB;IAC1C,IAAI+B,MAAM,CAACC,OAAO,CAAC,WAAWhC,eAAe,CAACiC,SAAS,IAAI,CAAC,EAAE;MAC9D,IAAI,CAAChE,aAAa,CAACiE,qCAAqC,CAAC;QAACzD,IAAI,EAAG;UAAE+B,iBAAiB,EAAER,eAAe,CAACQ;QAAiB;MAAC,CAAC,CAAC,CAAC5B,SAAS,CAACC,GAAG,IAAG;QACzI,IAAIA,GAAG,CAACE,UAAU,KAAK,CAAC,EAAE;UACxB,IAAI,CAACZ,OAAO,CAACyD,aAAa,CAAC,MAAM,CAAC;UAClC,IAAI,CAACzC,sBAAsB,EAAE;QAC/B;MACF,CAAC,CAAC;IACJ;EACA;EAEAgD,KAAKA,CAAA;IACH,IAAI,CAACjD,gBAAgB,GAAG,IAAI,CAACd,uBAAuB,CAAC,CAAC,CAAC;EACzD;EAKSgE,QAAQA,CAAA;IACf,IAAI,CAAClE,KAAK,CAACmE,QAAQ,CAACzD,SAAS,CAAC0D,MAAM,IAAG;MACrC,IAAIA,MAAM,EAAE;QACV,MAAMC,OAAO,GAAGD,MAAM,CAACE,GAAG,CAAC,IAAI,CAAC;QAChC,MAAMC,EAAE,GAAGF,OAAO,GAAG,CAACA,OAAO,GAAG,CAAC;QACjC,IAAI,CAAC5D,WAAW,GAAG8D,EAAE;QACrB,IAAI,CAAClE,eAAe,EAAE;MACxB;IACF,CAAC,CAAC;EACJ;EAEAmE,WAAWA,CAACC,OAAe;IACzB,IAAI,CAACpD,SAAS,GAAGoD,OAAO;IACxB,IAAI,CAACxD,sBAAsB,EAAE;EAC/B;EAGAyD,MAAMA,CAACpB,GAAQ;IACb,IAAI,CAACxD,aAAa,CAAC6E,IAAI,CAACrB,GAAG,CAAC;EAC9B;EAEAsB,OAAOA,CAACtB,GAAQ;IACdA,GAAG,CAACK,KAAK,EAAE;EACb;CAED;AAvKYhE,0BAA0B,GAAAkF,UAAA,EANtCpF,SAAS,CAAC;EACTqF,QAAQ,EAAE,yBAAyB;EACnCC,WAAW,EAAE,sCAAsC;EACnDC,SAAS,EAAE,CAAC,sCAAsC;CACnD,CAAC,C,EAEWrF,0BAA0B,CAuKtC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}