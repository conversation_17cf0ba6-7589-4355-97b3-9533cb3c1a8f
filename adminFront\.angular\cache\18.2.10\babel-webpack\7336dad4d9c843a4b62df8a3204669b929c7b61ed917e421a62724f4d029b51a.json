{"ast": null, "code": "import * as i0 from \"@angular/core\";\nexport class RoundPipe {\n  transform(input) {\n    return Math.round(input);\n  }\n  static {\n    this.ɵfac = function RoundPipe_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || RoundPipe)();\n    };\n  }\n  static {\n    this.ɵpipe = /*@__PURE__*/i0.ɵɵdefinePipe({\n      name: \"ngxRound\",\n      type: RoundPipe,\n      pure: true,\n      standalone: true\n    });\n  }\n}", "map": {"version": 3, "names": ["RoundPipe", "transform", "input", "Math", "round", "pure", "standalone"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\@theme\\pipes\\round.pipe.ts"], "sourcesContent": ["import { Pipe, PipeTransform } from '@angular/core';\r\n\r\n@Pipe({\r\n    name: 'ngxRound',\r\n    standalone: true\r\n})\r\nexport class RoundPipe implements PipeTransform {\r\n\r\n  transform(input: number): number {\r\n    return Math.round(input);\r\n  }\r\n}\r\n"], "mappings": ";AAMA,OAAM,MAAOA,SAAS;EAEpBC,SAASA,CAACC,KAAa;IACrB,OAAOC,IAAI,CAACC,KAAK,CAACF,KAAK,CAAC;EAC1B;;;uCAJWF,SAAS;IAAA;EAAA;;;;YAATA,SAAS;MAAAK,IAAA;MAAAC,UAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}