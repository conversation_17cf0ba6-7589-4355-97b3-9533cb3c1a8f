{"ast": null, "code": "import differenceInCalendarWeeks from \"../differenceInCalendarWeeks/index.js\";\nimport lastDayOfMonth from \"../lastDayOfMonth/index.js\";\nimport startOfMonth from \"../startOfMonth/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name getWeeksInMonth\n * @category Week Helpers\n * @summary Get the number of calendar weeks a month spans.\n *\n * @description\n * Get the number of calendar weeks the month in the given date spans.\n *\n * @param {Date|Number} date - the given date\n * @param {Object} [options] - an object with options.\n * @param {Locale} [options.locale=defaultLocale] - the locale object. See [Locale]{@link https://date-fns.org/docs/Locale}\n * @param {0|1|2|3|4|5|6} [options.weekStartsOn=0] - the index of the first day of the week (0 - Sunday)\n * @returns {Number} the number of calendar weeks\n * @throws {TypeError} 2 arguments required\n * @throws {RangeError} `options.weekStartsOn` must be between 0 and 6\n *\n * @example\n * // How many calendar weeks does February 2015 span?\n * const result = getWeeksInMonth(new Date(2015, 1, 8))\n * //=> 4\n *\n * @example\n * // If the week starts on Monday,\n * // how many calendar weeks does July 2017 span?\n * const result = getWeeksInMonth(new Date(2017, 6, 5), { weekStartsOn: 1 })\n * //=> 6\n */\nexport default function getWeeksInMonth(date, options) {\n  requiredArgs(1, arguments);\n  return differenceInCalendarWeeks(lastDayOfMonth(date), startOfMonth(date), options) + 1;\n}", "map": {"version": 3, "names": ["differenceInCalendarWeeks", "lastDayOfMonth", "startOfMonth", "requiredArgs", "getWeeksInMonth", "date", "options", "arguments"], "sources": ["C:/Users/<USER>/Documents/jeansalechange/adminFront/node_modules/date-fns/esm/getWeeksInMonth/index.js"], "sourcesContent": ["import differenceInCalendarWeeks from \"../differenceInCalendarWeeks/index.js\";\nimport lastDayOfMonth from \"../lastDayOfMonth/index.js\";\nimport startOfMonth from \"../startOfMonth/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name getWeeksInMonth\n * @category Week Helpers\n * @summary Get the number of calendar weeks a month spans.\n *\n * @description\n * Get the number of calendar weeks the month in the given date spans.\n *\n * @param {Date|Number} date - the given date\n * @param {Object} [options] - an object with options.\n * @param {Locale} [options.locale=defaultLocale] - the locale object. See [Locale]{@link https://date-fns.org/docs/Locale}\n * @param {0|1|2|3|4|5|6} [options.weekStartsOn=0] - the index of the first day of the week (0 - Sunday)\n * @returns {Number} the number of calendar weeks\n * @throws {TypeError} 2 arguments required\n * @throws {RangeError} `options.weekStartsOn` must be between 0 and 6\n *\n * @example\n * // How many calendar weeks does February 2015 span?\n * const result = getWeeksInMonth(new Date(2015, 1, 8))\n * //=> 4\n *\n * @example\n * // If the week starts on Monday,\n * // how many calendar weeks does July 2017 span?\n * const result = getWeeksInMonth(new Date(2017, 6, 5), { weekStartsOn: 1 })\n * //=> 6\n */\nexport default function getWeeksInMonth(date, options) {\n  requiredArgs(1, arguments);\n  return differenceInCalendarWeeks(lastDayOfMonth(date), startOfMonth(date), options) + 1;\n}"], "mappings": "AAAA,OAAOA,yBAAyB,MAAM,uCAAuC;AAC7E,OAAOC,cAAc,MAAM,4BAA4B;AACvD,OAAOC,YAAY,MAAM,0BAA0B;AACnD,OAAOC,YAAY,MAAM,+BAA+B;AACxD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,SAASC,eAAeA,CAACC,IAAI,EAAEC,OAAO,EAAE;EACrDH,YAAY,CAAC,CAAC,EAAEI,SAAS,CAAC;EAC1B,OAAOP,yBAAyB,CAACC,cAAc,CAACI,IAAI,CAAC,EAAEH,YAAY,CAACG,IAAI,CAAC,EAAEC,OAAO,CAAC,GAAG,CAAC;AACzF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}