{"ast": null, "code": "import compareAsc from \"../compareAsc/index.js\";\nimport add from \"../add/index.js\";\nimport differenceInDays from \"../differenceInDays/index.js\";\nimport differenceInHours from \"../differenceInHours/index.js\";\nimport differenceInMinutes from \"../differenceInMinutes/index.js\";\nimport differenceInMonths from \"../differenceInMonths/index.js\";\nimport differenceInSeconds from \"../differenceInSeconds/index.js\";\nimport differenceInYears from \"../differenceInYears/index.js\";\nimport toDate from \"../toDate/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name intervalToDuration\n * @category Common Helpers\n * @summary Convert interval to duration\n *\n * @description\n * Convert a interval object to a duration object.\n *\n * @param {Interval} interval - the interval to convert to duration\n *\n * @returns {Duration} The duration Object\n * @throws {TypeError} Requires 2 arguments\n * @throws {RangeError} `start` must not be Invalid Date\n * @throws {RangeError} `end` must not be Invalid Date\n *\n * @example\n * // Get the duration between January 15, 1929 and April 4, 1968.\n * intervalToDuration({\n *   start: new Date(1929, 0, 15, 12, 0, 0),\n *   end: new Date(1968, 3, 4, 19, 5, 0)\n * })\n * // => { years: 39, months: 2, days: 20, hours: 7, minutes: 5, seconds: 0 }\n */\nexport default function intervalToDuration(interval) {\n  requiredArgs(1, arguments);\n  var start = toDate(interval.start);\n  var end = toDate(interval.end);\n  if (isNaN(start.getTime())) throw new RangeError('Start Date is invalid');\n  if (isNaN(end.getTime())) throw new RangeError('End Date is invalid');\n  var duration = {};\n  duration.years = Math.abs(differenceInYears(end, start));\n  var sign = compareAsc(end, start);\n  var remainingMonths = add(start, {\n    years: sign * duration.years\n  });\n  duration.months = Math.abs(differenceInMonths(end, remainingMonths));\n  var remainingDays = add(remainingMonths, {\n    months: sign * duration.months\n  });\n  duration.days = Math.abs(differenceInDays(end, remainingDays));\n  var remainingHours = add(remainingDays, {\n    days: sign * duration.days\n  });\n  duration.hours = Math.abs(differenceInHours(end, remainingHours));\n  var remainingMinutes = add(remainingHours, {\n    hours: sign * duration.hours\n  });\n  duration.minutes = Math.abs(differenceInMinutes(end, remainingMinutes));\n  var remainingSeconds = add(remainingMinutes, {\n    minutes: sign * duration.minutes\n  });\n  duration.seconds = Math.abs(differenceInSeconds(end, remainingSeconds));\n  return duration;\n}", "map": {"version": 3, "names": ["compareAsc", "add", "differenceInDays", "differenceInHours", "differenceInMinutes", "differenceInMonths", "differenceInSeconds", "differenceInYears", "toDate", "requiredArgs", "intervalToDuration", "interval", "arguments", "start", "end", "isNaN", "getTime", "RangeError", "duration", "years", "Math", "abs", "sign", "remainingMonths", "months", "remainingDays", "days", "remainingHours", "hours", "remainingMinutes", "minutes", "remainingSeconds", "seconds"], "sources": ["C:/Users/<USER>/Documents/salechange-product/adminFront/node_modules/date-fns/esm/intervalToDuration/index.js"], "sourcesContent": ["import compareAsc from \"../compareAsc/index.js\";\nimport add from \"../add/index.js\";\nimport differenceInDays from \"../differenceInDays/index.js\";\nimport differenceInHours from \"../differenceInHours/index.js\";\nimport differenceInMinutes from \"../differenceInMinutes/index.js\";\nimport differenceInMonths from \"../differenceInMonths/index.js\";\nimport differenceInSeconds from \"../differenceInSeconds/index.js\";\nimport differenceInYears from \"../differenceInYears/index.js\";\nimport toDate from \"../toDate/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name intervalToDuration\n * @category Common Helpers\n * @summary Convert interval to duration\n *\n * @description\n * Convert a interval object to a duration object.\n *\n * @param {Interval} interval - the interval to convert to duration\n *\n * @returns {Duration} The duration Object\n * @throws {TypeError} Requires 2 arguments\n * @throws {RangeError} `start` must not be Invalid Date\n * @throws {RangeError} `end` must not be Invalid Date\n *\n * @example\n * // Get the duration between January 15, 1929 and April 4, 1968.\n * intervalToDuration({\n *   start: new Date(1929, 0, 15, 12, 0, 0),\n *   end: new Date(1968, 3, 4, 19, 5, 0)\n * })\n * // => { years: 39, months: 2, days: 20, hours: 7, minutes: 5, seconds: 0 }\n */\nexport default function intervalToDuration(interval) {\n  requiredArgs(1, arguments);\n  var start = toDate(interval.start);\n  var end = toDate(interval.end);\n  if (isNaN(start.getTime())) throw new RangeError('Start Date is invalid');\n  if (isNaN(end.getTime())) throw new RangeError('End Date is invalid');\n  var duration = {};\n  duration.years = Math.abs(differenceInYears(end, start));\n  var sign = compareAsc(end, start);\n  var remainingMonths = add(start, {\n    years: sign * duration.years\n  });\n  duration.months = Math.abs(differenceInMonths(end, remainingMonths));\n  var remainingDays = add(remainingMonths, {\n    months: sign * duration.months\n  });\n  duration.days = Math.abs(differenceInDays(end, remainingDays));\n  var remainingHours = add(remainingDays, {\n    days: sign * duration.days\n  });\n  duration.hours = Math.abs(differenceInHours(end, remainingHours));\n  var remainingMinutes = add(remainingHours, {\n    hours: sign * duration.hours\n  });\n  duration.minutes = Math.abs(differenceInMinutes(end, remainingMinutes));\n  var remainingSeconds = add(remainingMinutes, {\n    minutes: sign * duration.minutes\n  });\n  duration.seconds = Math.abs(differenceInSeconds(end, remainingSeconds));\n  return duration;\n}"], "mappings": "AAAA,OAAOA,UAAU,MAAM,wBAAwB;AAC/C,OAAOC,GAAG,MAAM,iBAAiB;AACjC,OAAOC,gBAAgB,MAAM,8BAA8B;AAC3D,OAAOC,iBAAiB,MAAM,+BAA+B;AAC7D,OAAOC,mBAAmB,MAAM,iCAAiC;AACjE,OAAOC,kBAAkB,MAAM,gCAAgC;AAC/D,OAAOC,mBAAmB,MAAM,iCAAiC;AACjE,OAAOC,iBAAiB,MAAM,+BAA+B;AAC7D,OAAOC,MAAM,MAAM,oBAAoB;AACvC,OAAOC,YAAY,MAAM,+BAA+B;AACxD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,SAASC,kBAAkBA,CAACC,QAAQ,EAAE;EACnDF,YAAY,CAAC,CAAC,EAAEG,SAAS,CAAC;EAC1B,IAAIC,KAAK,GAAGL,MAAM,CAACG,QAAQ,CAACE,KAAK,CAAC;EAClC,IAAIC,GAAG,GAAGN,MAAM,CAACG,QAAQ,CAACG,GAAG,CAAC;EAC9B,IAAIC,KAAK,CAACF,KAAK,CAACG,OAAO,CAAC,CAAC,CAAC,EAAE,MAAM,IAAIC,UAAU,CAAC,uBAAuB,CAAC;EACzE,IAAIF,KAAK,CAACD,GAAG,CAACE,OAAO,CAAC,CAAC,CAAC,EAAE,MAAM,IAAIC,UAAU,CAAC,qBAAqB,CAAC;EACrE,IAAIC,QAAQ,GAAG,CAAC,CAAC;EACjBA,QAAQ,CAACC,KAAK,GAAGC,IAAI,CAACC,GAAG,CAACd,iBAAiB,CAACO,GAAG,EAAED,KAAK,CAAC,CAAC;EACxD,IAAIS,IAAI,GAAGtB,UAAU,CAACc,GAAG,EAAED,KAAK,CAAC;EACjC,IAAIU,eAAe,GAAGtB,GAAG,CAACY,KAAK,EAAE;IAC/BM,KAAK,EAAEG,IAAI,GAAGJ,QAAQ,CAACC;EACzB,CAAC,CAAC;EACFD,QAAQ,CAACM,MAAM,GAAGJ,IAAI,CAACC,GAAG,CAAChB,kBAAkB,CAACS,GAAG,EAAES,eAAe,CAAC,CAAC;EACpE,IAAIE,aAAa,GAAGxB,GAAG,CAACsB,eAAe,EAAE;IACvCC,MAAM,EAAEF,IAAI,GAAGJ,QAAQ,CAACM;EAC1B,CAAC,CAAC;EACFN,QAAQ,CAACQ,IAAI,GAAGN,IAAI,CAACC,GAAG,CAACnB,gBAAgB,CAACY,GAAG,EAAEW,aAAa,CAAC,CAAC;EAC9D,IAAIE,cAAc,GAAG1B,GAAG,CAACwB,aAAa,EAAE;IACtCC,IAAI,EAAEJ,IAAI,GAAGJ,QAAQ,CAACQ;EACxB,CAAC,CAAC;EACFR,QAAQ,CAACU,KAAK,GAAGR,IAAI,CAACC,GAAG,CAAClB,iBAAiB,CAACW,GAAG,EAAEa,cAAc,CAAC,CAAC;EACjE,IAAIE,gBAAgB,GAAG5B,GAAG,CAAC0B,cAAc,EAAE;IACzCC,KAAK,EAAEN,IAAI,GAAGJ,QAAQ,CAACU;EACzB,CAAC,CAAC;EACFV,QAAQ,CAACY,OAAO,GAAGV,IAAI,CAACC,GAAG,CAACjB,mBAAmB,CAACU,GAAG,EAAEe,gBAAgB,CAAC,CAAC;EACvE,IAAIE,gBAAgB,GAAG9B,GAAG,CAAC4B,gBAAgB,EAAE;IAC3CC,OAAO,EAAER,IAAI,GAAGJ,QAAQ,CAACY;EAC3B,CAAC,CAAC;EACFZ,QAAQ,CAACc,OAAO,GAAGZ,IAAI,CAACC,GAAG,CAACf,mBAAmB,CAACQ,GAAG,EAAEiB,gBAAgB,CAAC,CAAC;EACvE,OAAOb,QAAQ;AACjB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}