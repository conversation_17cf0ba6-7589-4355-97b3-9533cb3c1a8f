{"ast": null, "code": "import requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name intlFormat\n * @category Common Helpers\n * @summary  Format the date with Intl.DateTimeFormat (https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Intl/DateTimeFormat).\n *\n * @description\n * Return the formatted date string in the given format.\n * The method uses [`Intl.DateTimeFormat`](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Intl/DateTimeFormat) inside.\n * formatOptions are the same as [`Intl.DateTimeFormat` options](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Intl/DateTimeFormat#using_options)\n *\n * > ⚠️ Please note that before Node version 13.0.0, only the locale data for en-US is available by default.\n *\n * @param {Date|Number} argument - the original date.\n * @param {Object} [formatOptions] - an object with options.\n * @param {'lookup'|'best fit'} [formatOptions.localeMatcher='best fit'] - locale selection algorithm.\n * @param {'narrow'|'short'|'long'} [formatOptions.weekday] - representation the days of the week.\n * @param {'narrow'|'short'|'long'} [formatOptions.era] - representation of eras.\n * @param {'numeric'|'2-digit'} [formatOptions.year] - representation of years.\n * @param {'numeric'|'2-digit'|'narrow'|'short'|'long'} [formatOptions.month='numeric'] - representation of month.\n * @param {'numeric'|'2-digit'} [formatOptions.day='numeric'] - representation of day.\n * @param {'numeric'|'2-digit'} [formatOptions.hour='numeric'] - representation of hours.\n * @param {'numeric'|'2-digit'} [formatOptions.minute] - representation of minutes.\n * @param {'numeric'|'2-digit'} [formatOptions.second] - representation of seconds.\n * @param {'short'|'long'} [formatOptions.timeZoneName] - representation of names of time zones.\n * @param {'basic'|'best fit'} [formatOptions.formatMatcher='best fit'] - format selection algorithm.\n * @param {Boolean} [formatOptions.hour12] - determines whether to use 12-hour time format.\n * @param {String} [formatOptions.timeZone] - the time zone to use.\n * @param {Object} [localeOptions] - an object with locale.\n * @param {String|String[]} [localeOptions.locale] - the locale code\n * @returns {String} the formatted date string.\n * @throws {TypeError} 1 argument required.\n * @throws {RangeError} `date` must not be Invalid Date\n *\n * @example\n * // Represent 10 October 2019 in German.\n * // Convert the date with format's options and locale's options.\n * const result = intlFormat(new Date(2019, 9, 4, 12, 30, 13, 456), {\n *      weekday: 'long',\n *      year: 'numeric',\n *      month: 'long',\n *      day: 'numeric',\n *    }, {\n *      locale: 'de-DE',\n *  })\n * //=> Freitag, 4. Oktober 2019\n *\n * @example\n * // Represent 10 October 2019.\n * // Convert the date with format's options.\n * const result = intlFormat.default(new Date(2019, 9, 4, 12, 30, 13, 456), {\n *      year: 'numeric',\n *      month: 'numeric',\n *      day: 'numeric',\n *      hour: 'numeric',\n *  })\n * //=> 10/4/2019, 12 PM\n *\n * @example\n * // Represent 10 October 2019 in Korean.\n * // Convert the date with locale's options.\n * const result = intlFormat(new Date(2019, 9, 4, 12, 30, 13, 456), {\n *      locale: 'ko-KR',\n *  })\n * //=> 2019. 10. 4.\n *\n * @example\n * // Represent 10 October 2019 in middle-endian format:\n * const result = intlFormat(new Date(2019, 9, 4, 12, 30, 13, 456))\n * //=> 10/4/2019\n */\nexport default function intlFormat(date, formatOrLocale, localeOptions) {\n  var _localeOptions;\n  requiredArgs(1, arguments);\n  var formatOptions;\n  if (isFormatOptions(formatOrLocale)) {\n    formatOptions = formatOrLocale;\n  } else {\n    localeOptions = formatOrLocale;\n  }\n  return new Intl.DateTimeFormat((_localeOptions = localeOptions) === null || _localeOptions === void 0 ? void 0 : _localeOptions.locale, formatOptions).format(date);\n}\nfunction isFormatOptions(opts) {\n  return opts !== undefined && !('locale' in opts);\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}