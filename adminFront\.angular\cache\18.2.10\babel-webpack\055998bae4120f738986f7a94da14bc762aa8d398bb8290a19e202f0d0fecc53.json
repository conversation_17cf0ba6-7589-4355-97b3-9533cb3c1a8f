{"ast": null, "code": "import * as i0 from \"@angular/core\";\nexport class SpecialChangeSourcePipe {\n  transform(value) {\n    switch (value) {\n      case 1:\n        return '後台';\n      case 2:\n        return '前台';\n      default:\n        return '';\n    }\n  }\n  static {\n    this.ɵfac = function SpecialChangeSourcePipe_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || SpecialChangeSourcePipe)();\n    };\n  }\n  static {\n    this.ɵpipe = /*@__PURE__*/i0.ɵɵdefinePipe({\n      name: \"specialChangeSource\",\n      type: SpecialChangeSourcePipe,\n      pure: true,\n      standalone: true\n    });\n  }\n}", "map": {"version": 3, "names": ["SpecialChangeSourcePipe", "transform", "value", "pure", "standalone"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\@theme\\pipes\\specialChangeSource.pipe.ts"], "sourcesContent": ["import { Pipe, PipeTransform } from '@angular/core';\r\n\r\n@Pipe({\r\n    name: 'specialChangeSource',\r\n    standalone: true\r\n})\r\nexport class SpecialChangeSourcePipe implements PipeTransform {\r\n    transform(value: number): string {\r\n        switch (value) {\r\n            case 1:\r\n                return '後台';\r\n            case 2:\r\n                return '前台';\r\n            default:\r\n                return '';\r\n        }\r\n    }\r\n}\r\n"], "mappings": ";AAMA,OAAM,MAAOA,uBAAuB;EAChCC,SAASA,CAACC,KAAa;IACnB,QAAQA,KAAK;MACT,KAAK,CAAC;QACF,OAAO,IAAI;MACf,KAAK,CAAC;QACF,OAAO,IAAI;MACf;QACI,OAAO,EAAE;IACjB;EACJ;;;uCAVSF,uBAAuB;IAAA;EAAA;;;;YAAvBA,uBAAuB;MAAAG,IAAA;MAAAC,UAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}