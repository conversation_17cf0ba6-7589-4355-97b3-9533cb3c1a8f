{"ast": null, "code": "import addQuarters from \"../addQuarters/index.js\";\nimport startOfQuarter from \"../startOfQuarter/index.js\";\nimport toDate from \"../toDate/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name eachQuarterOfInterval\n * @category Interval Helpers\n * @summary Return the array of quarters within the specified time interval.\n *\n * @description\n * Return the array of quarters within the specified time interval.\n *\n * @param {Interval} interval - the interval. See [Interval]{@link https://date-fns.org/docs/Interval}\n * @returns {Date[]} the array with starts of quarters from the quarter of the interval start to the quarter of the interval end\n * @throws {TypeError} 1 argument required\n * @throws {RangeError} The start of an interval cannot be after its end\n * @throws {RangeError} Date in interval cannot be `Invalid Date`\n *\n * @example\n * // Each quarter within interval 6 February 2014 - 10 August 2014:\n * const result = eachQuarterOfInterval({\n *   start: new Date(2014, 1, 6),\n *   end: new Date(2014, 7, 10)\n * })\n * //=> [\n * //   Wed Jan 01 2014 00:00:00,\n * //   Tue Apr 01 2014 00:00:00,\n * //   Tue Jul 01 2014 00:00:00,\n * // ]\n */\nexport default function eachQuarterOfInterval(dirtyInterval) {\n  requiredArgs(1, arguments);\n  var interval = dirtyInterval || {};\n  var startDate = toDate(interval.start);\n  var endDate = toDate(interval.end);\n  var endTime = endDate.getTime();\n\n  // Throw an exception if start date is after end date or if any date is `Invalid Date`\n  if (!(startDate.getTime() <= endTime)) {\n    throw new RangeError('Invalid interval');\n  }\n  var startDateQuarter = startOfQuarter(startDate);\n  var endDateQuarter = startOfQuarter(endDate);\n  endTime = endDateQuarter.getTime();\n  var quarters = [];\n  var currentQuarter = startDateQuarter;\n  while (currentQuarter.getTime() <= endTime) {\n    quarters.push(toDate(currentQuarter));\n    currentQuarter = addQuarters(currentQuarter, 1);\n  }\n  return quarters;\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}