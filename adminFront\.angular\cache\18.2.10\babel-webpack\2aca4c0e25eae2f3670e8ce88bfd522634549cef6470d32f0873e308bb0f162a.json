{"ast": null, "code": "import { map } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../services/api/services/quotation.service\";\nimport * as i2 from \"@angular/common/http\";\nexport class QuotationService {\n  constructor(apiQuotationService, http) {\n    this.apiQuotationService = apiQuotationService;\n    this.http = http;\n    this.apiUrl = '/api/Quotation';\n  }\n  // 取得報價單列表\n  getQuotationList(request) {\n    return this.apiQuotationService.apiQuotationGetListPost$Json({\n      body: request\n    });\n  }\n  // 取得單筆報價單資料\n  getQuotationData(quotationId) {\n    const request = {\n      cQuotationID: quotationId\n    };\n    return this.apiQuotationService.apiQuotationGetDataPost$Json({\n      body: request\n    });\n  }\n  // 取得戶別的報價項目\n  getQuotationByHouseId(houseId) {\n    const request = {\n      cHouseID: houseId\n    };\n    return this.apiQuotationService.apiQuotationGetListByHouseIdPost$Json({\n      body: request\n    });\n  }\n  // 儲存報價單 (支援單一項目)\n  saveQuotationItem(quotation) {\n    return this.apiQuotationService.apiQuotationSaveDataPost$Json({\n      body: quotation\n    });\n  } // 儲存報價單 (支援批量保存多個項目) - 使用單一請求\n  saveQuotation(request) {\n    // 將 QuotationItem 轉換為 QuotationItemModel 格式\n    const quotationItems = request.items.map(item => ({\n      CItemName: item.cItemName,\n      CUnitPrice: item.cUnitPrice,\n      CCount: item.cCount,\n      CStatus: item.cStatus || 1,\n      CIsDefault: item.cIsDefault || false\n    }));\n    // 建立 SaveDataQuotation 請求\n    const saveRequest = {\n      CHouseID: request.houseId,\n      CQuotationID: 0,\n      // 對於新的報價單或更新現有的報價單\n      Items: quotationItems\n    };\n    return this.apiQuotationService.apiQuotationSaveDataPost$Json({\n      body: saveRequest\n    }).pipe(map(response => ({\n      success: response?.StatusCode === 0,\n      message: response?.StatusCode === 0 ? '報價單保存成功' : '報價單保存失敗',\n      data: request.items\n    })));\n  }\n  // 取得預設報價項目 (保持原有方法以兼容現有代碼)\n  getDefaultQuotationItems() {\n    // 使用 GetList 方法獲取預設項目\n    const request = {\n      pageIndex: 0,\n      pageSize: 100\n      // 其他預設參數可能需要根據實際 API 需求調整\n    };\n    return this.apiQuotationService.apiQuotationGetListPost$Json({\n      body: request\n    }).pipe(map(response => {\n      // 轉換 API 響應格式以保持兼容性\n      return {\n        success: response.statusCode === 0,\n        // 假設 statusCode 0 表示成功\n        message: response.message || '',\n        data: response.entries || []\n      };\n    }));\n  } // 載入預設報價項目 (LoadDefaultItems API)\n  loadDefaultItems(request) {\n    // 使用注入的 HttpClient 直接發送請求，但使用 apiQuotationService 的 rootUrl\n    return this.http.post(`${this.apiQuotationService.rootUrl}/api/Quotation/LoadDefaultItems`, request).pipe(map(response => {\n      // 轉換 API 響應格式以保持兼容性\n      return {\n        success: response.StatusCode === 0,\n        // 假設 StatusCode 0 表示成功\n        message: response.Message || '',\n        data: response.Entries || []\n      };\n    }));\n  } // 更新報價項目 (使用 SaveData 方法)\n  updateQuotationItem(quotationId, item) {\n    const saveData = {\n      CHouseID: item.cHouseID,\n      CQuotationID: quotationId,\n      Items: [{\n        CItemName: item.cItemName,\n        CUnitPrice: item.cUnitPrice,\n        CCount: item.cCount,\n        CStatus: item.cStatus || 1,\n        CIsDefault: item.cIsDefault || false\n      }]\n    };\n    return this.apiQuotationService.apiQuotationSaveDataPost$Json({\n      body: saveData\n    }).pipe(map(response => {\n      return {\n        success: response.StatusCode === 0,\n        // 假設 StatusCode 0 表示成功\n        message: response.Message || '',\n        data: [item] // 包裝為陣列以符合 QuotationResponse 格式\n      };\n    }));\n  }\n  // 匯出報價單 (需要另外實作，因為 swagger 中沒有此 API)\n  exportQuotation(houseId) {\n    // 這個方法可能需要使用其他 API 或保持原有實作\n    // 暫時拋出錯誤提示需要實作\n    throw new Error('Export quotation functionality needs to be implemented separately');\n  }\n  static {\n    this.ɵfac = function QuotationService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || QuotationService)(i0.ɵɵinject(i1.QuotationService), i0.ɵɵinject(i2.HttpClient));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: QuotationService,\n      factory: QuotationService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["map", "QuotationService", "constructor", "apiQuotationService", "http", "apiUrl", "getQuotationList", "request", "apiQuotationGetListPost$Json", "body", "getQuotationData", "quotationId", "cQuotationID", "apiQuotationGetDataPost$Json", "getQuotationByHouseId", "houseId", "cHouseID", "apiQuotationGetListByHouseIdPost$Json", "saveQuotationItem", "quotation", "apiQuotationSaveDataPost$Json", "saveQuotation", "quotationItems", "items", "item", "CItemName", "cItemName", "CUnitPrice", "cUnitPrice", "CCount", "cCount", "CStatus", "cStatus", "CIsDefault", "cIsDefault", "saveRequest", "CHouseID", "CQuotationID", "Items", "pipe", "response", "success", "StatusCode", "message", "data", "getDefaultQuotationItems", "pageIndex", "pageSize", "statusCode", "entries", "loadDefaultItems", "post", "rootUrl", "Message", "Entries", "updateQuotationItem", "saveData", "exportQuotation", "Error", "i0", "ɵɵinject", "i1", "i2", "HttpClient", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\services\\quotation.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport { Observable } from 'rxjs';\r\nimport { map, switchMap } from 'rxjs/operators';\r\nimport { HttpClient } from '@angular/common/http';\r\nimport { QuotationItem, QuotationRequest, QuotationResponse } from '../models/quotation.model';\r\nimport { QuotationService as ApiQuotationService } from '../../services/api/services/quotation.service';\r\nimport {\r\n  GetListQuotationRequest,\r\n  GetQuotationByIdRequest,\r\n  SaveDataQuotation,\r\n  QuotationItemModel,\r\n  GetListByHouseIdRequest,\r\n  LoadDefaultItemsRequest\r\n} from '../../services/api/models';\r\n\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class QuotationService {\r\n  private readonly apiUrl = '/api/Quotation';\r\n\r\n  constructor(\r\n    private apiQuotationService: ApiQuotationService,\r\n    private http: HttpClient\r\n  ) { }\r\n  // 取得報價單列表\r\n  getQuotationList(request: GetListQuotationRequest): Observable<any> {\r\n    return this.apiQuotationService.apiQuotationGetListPost$Json({ body: request });\r\n  }\r\n  // 取得單筆報價單資料\r\n  getQuotationData(quotationId: number): Observable<any> {\r\n    const request: GetQuotationByIdRequest = { cQuotationID: quotationId };\r\n    return this.apiQuotationService.apiQuotationGetDataPost$Json({ body: request });\r\n  }\r\n  // 取得戶別的報價項目\r\n  getQuotationByHouseId(houseId: number): Observable<any> {\r\n    const request: GetListByHouseIdRequest = { cHouseID: houseId };\r\n    return this.apiQuotationService.apiQuotationGetListByHouseIdPost$Json({ body: request });\r\n  }\r\n  // 儲存報價單 (支援單一項目)\r\n  saveQuotationItem(quotation: SaveDataQuotation): Observable<any> {\r\n    return this.apiQuotationService.apiQuotationSaveDataPost$Json({ body: quotation });\r\n  }  // 儲存報價單 (支援批量保存多個項目) - 使用單一請求\r\n  saveQuotation(request: { houseId: number; items: QuotationItem[] }): Observable<QuotationResponse> {\r\n    // 將 QuotationItem 轉換為 QuotationItemModel 格式\r\n    const quotationItems: QuotationItemModel[] = request.items.map(item => ({\r\n      CItemName: item.cItemName,\r\n      CUnitPrice: item.cUnitPrice,\r\n      CCount: item.cCount,\r\n      CStatus: item.cStatus || 1,\r\n      CIsDefault: item.cIsDefault || false,\r\n    }));\r\n\r\n    // 建立 SaveDataQuotation 請求\r\n    const saveRequest: SaveDataQuotation = {\r\n      CHouseID: request.houseId,\r\n      CQuotationID: 0, // 對於新的報價單或更新現有的報價單\r\n      Items: quotationItems\r\n    };\r\n\r\n    return this.apiQuotationService.apiQuotationSaveDataPost$Json({ body: saveRequest }).pipe(\r\n      map(response => ({\r\n        success: response?.StatusCode === 0,\r\n        message: response?.StatusCode === 0 ? '報價單保存成功' : '報價單保存失敗',\r\n        data: request.items\r\n      } as QuotationResponse))\r\n    );\r\n  }\r\n\r\n  // 取得預設報價項目 (保持原有方法以兼容現有代碼)\r\n  getDefaultQuotationItems(): Observable<QuotationResponse> {\r\n    // 使用 GetList 方法獲取預設項目\r\n    const request: GetListQuotationRequest = {\r\n      pageIndex: 0,\r\n      pageSize: 100,\r\n      // 其他預設參數可能需要根據實際 API 需求調整\r\n    };\r\n    return this.apiQuotationService.apiQuotationGetListPost$Json({ body: request }).pipe(\r\n      map(response => {\r\n        // 轉換 API 響應格式以保持兼容性\r\n        return {\r\n          success: response.statusCode === 0, // 假設 statusCode 0 表示成功\r\n          message: response.message || '',\r\n          data: response.entries || []\r\n        } as QuotationResponse;\r\n      })\r\n    );\r\n  }  // 載入預設報價項目 (LoadDefaultItems API)\r\n  loadDefaultItems(request: LoadDefaultItemsRequest): Observable<QuotationResponse> {\r\n    // 使用注入的 HttpClient 直接發送請求，但使用 apiQuotationService 的 rootUrl\r\n    return this.http.post<any>(\r\n      `${this.apiQuotationService.rootUrl}/api/Quotation/LoadDefaultItems`,\r\n      request\r\n    ).pipe(\r\n      map(response => {\r\n        // 轉換 API 響應格式以保持兼容性\r\n        return {\r\n          success: response.StatusCode === 0, // 假設 StatusCode 0 表示成功\r\n          message: response.Message || '',\r\n          data: response.Entries || []\r\n        } as QuotationResponse;\r\n      })\r\n    );\r\n  }  // 更新報價項目 (使用 SaveData 方法)\r\n  updateQuotationItem(quotationId: number, item: QuotationItem): Observable<QuotationResponse> {\r\n    const saveData: SaveDataQuotation = {\r\n      CHouseID: item.cHouseID,\r\n      CQuotationID: quotationId,\r\n      Items: [{\r\n        CItemName: item.cItemName,\r\n        CUnitPrice: item.cUnitPrice,\r\n        CCount: item.cCount,\r\n        CStatus: item.cStatus || 1,\r\n        CIsDefault: item.cIsDefault || false\r\n      }]\r\n    };\r\n    return this.apiQuotationService.apiQuotationSaveDataPost$Json({ body: saveData }).pipe(\r\n      map(response => {\r\n        return {\r\n          success: response.StatusCode === 0, // 假設 StatusCode 0 表示成功\r\n          message: response.Message || '',\r\n          data: [item] // 包裝為陣列以符合 QuotationResponse 格式\r\n        } as QuotationResponse;\r\n      })\r\n    );\r\n  }\r\n\r\n  // 匯出報價單 (需要另外實作，因為 swagger 中沒有此 API)\r\n  exportQuotation(houseId: number): Observable<Blob> {\r\n    // 這個方法可能需要使用其他 API 或保持原有實作\r\n    // 暫時拋出錯誤提示需要實作\r\n    throw new Error('Export quotation functionality needs to be implemented separately');\r\n  }\r\n}\r\n"], "mappings": "AAEA,SAASA,GAAG,QAAmB,gBAAgB;;;;AAgB/C,OAAM,MAAOC,gBAAgB;EAG3BC,YACUC,mBAAwC,EACxCC,IAAgB;IADhB,KAAAD,mBAAmB,GAAnBA,mBAAmB;IACnB,KAAAC,IAAI,GAAJA,IAAI;IAJG,KAAAC,MAAM,GAAG,gBAAgB;EAKtC;EACJ;EACAC,gBAAgBA,CAACC,OAAgC;IAC/C,OAAO,IAAI,CAACJ,mBAAmB,CAACK,4BAA4B,CAAC;MAAEC,IAAI,EAAEF;IAAO,CAAE,CAAC;EACjF;EACA;EACAG,gBAAgBA,CAACC,WAAmB;IAClC,MAAMJ,OAAO,GAA4B;MAAEK,YAAY,EAAED;IAAW,CAAE;IACtE,OAAO,IAAI,CAACR,mBAAmB,CAACU,4BAA4B,CAAC;MAAEJ,IAAI,EAAEF;IAAO,CAAE,CAAC;EACjF;EACA;EACAO,qBAAqBA,CAACC,OAAe;IACnC,MAAMR,OAAO,GAA4B;MAAES,QAAQ,EAAED;IAAO,CAAE;IAC9D,OAAO,IAAI,CAACZ,mBAAmB,CAACc,qCAAqC,CAAC;MAAER,IAAI,EAAEF;IAAO,CAAE,CAAC;EAC1F;EACA;EACAW,iBAAiBA,CAACC,SAA4B;IAC5C,OAAO,IAAI,CAAChB,mBAAmB,CAACiB,6BAA6B,CAAC;MAAEX,IAAI,EAAEU;IAAS,CAAE,CAAC;EACpF,CAAC,CAAE;EACHE,aAAaA,CAACd,OAAoD;IAChE;IACA,MAAMe,cAAc,GAAyBf,OAAO,CAACgB,KAAK,CAACvB,GAAG,CAACwB,IAAI,KAAK;MACtEC,SAAS,EAAED,IAAI,CAACE,SAAS;MACzBC,UAAU,EAAEH,IAAI,CAACI,UAAU;MAC3BC,MAAM,EAAEL,IAAI,CAACM,MAAM;MACnBC,OAAO,EAAEP,IAAI,CAACQ,OAAO,IAAI,CAAC;MAC1BC,UAAU,EAAET,IAAI,CAACU,UAAU,IAAI;KAChC,CAAC,CAAC;IAEH;IACA,MAAMC,WAAW,GAAsB;MACrCC,QAAQ,EAAE7B,OAAO,CAACQ,OAAO;MACzBsB,YAAY,EAAE,CAAC;MAAE;MACjBC,KAAK,EAAEhB;KACR;IAED,OAAO,IAAI,CAACnB,mBAAmB,CAACiB,6BAA6B,CAAC;MAAEX,IAAI,EAAE0B;IAAW,CAAE,CAAC,CAACI,IAAI,CACvFvC,GAAG,CAACwC,QAAQ,KAAK;MACfC,OAAO,EAAED,QAAQ,EAAEE,UAAU,KAAK,CAAC;MACnCC,OAAO,EAAEH,QAAQ,EAAEE,UAAU,KAAK,CAAC,GAAG,SAAS,GAAG,SAAS;MAC3DE,IAAI,EAAErC,OAAO,CAACgB;KACO,EAAC,CACzB;EACH;EAEA;EACAsB,wBAAwBA,CAAA;IACtB;IACA,MAAMtC,OAAO,GAA4B;MACvCuC,SAAS,EAAE,CAAC;MACZC,QAAQ,EAAE;MACV;KACD;IACD,OAAO,IAAI,CAAC5C,mBAAmB,CAACK,4BAA4B,CAAC;MAAEC,IAAI,EAAEF;IAAO,CAAE,CAAC,CAACgC,IAAI,CAClFvC,GAAG,CAACwC,QAAQ,IAAG;MACb;MACA,OAAO;QACLC,OAAO,EAAED,QAAQ,CAACQ,UAAU,KAAK,CAAC;QAAE;QACpCL,OAAO,EAAEH,QAAQ,CAACG,OAAO,IAAI,EAAE;QAC/BC,IAAI,EAAEJ,QAAQ,CAACS,OAAO,IAAI;OACN;IACxB,CAAC,CAAC,CACH;EACH,CAAC,CAAE;EACHC,gBAAgBA,CAAC3C,OAAgC;IAC/C;IACA,OAAO,IAAI,CAACH,IAAI,CAAC+C,IAAI,CACnB,GAAG,IAAI,CAAChD,mBAAmB,CAACiD,OAAO,iCAAiC,EACpE7C,OAAO,CACR,CAACgC,IAAI,CACJvC,GAAG,CAACwC,QAAQ,IAAG;MACb;MACA,OAAO;QACLC,OAAO,EAAED,QAAQ,CAACE,UAAU,KAAK,CAAC;QAAE;QACpCC,OAAO,EAAEH,QAAQ,CAACa,OAAO,IAAI,EAAE;QAC/BT,IAAI,EAAEJ,QAAQ,CAACc,OAAO,IAAI;OACN;IACxB,CAAC,CAAC,CACH;EACH,CAAC,CAAE;EACHC,mBAAmBA,CAAC5C,WAAmB,EAAEa,IAAmB;IAC1D,MAAMgC,QAAQ,GAAsB;MAClCpB,QAAQ,EAAEZ,IAAI,CAACR,QAAQ;MACvBqB,YAAY,EAAE1B,WAAW;MACzB2B,KAAK,EAAE,CAAC;QACNb,SAAS,EAAED,IAAI,CAACE,SAAS;QACzBC,UAAU,EAAEH,IAAI,CAACI,UAAU;QAC3BC,MAAM,EAAEL,IAAI,CAACM,MAAM;QACnBC,OAAO,EAAEP,IAAI,CAACQ,OAAO,IAAI,CAAC;QAC1BC,UAAU,EAAET,IAAI,CAACU,UAAU,IAAI;OAChC;KACF;IACD,OAAO,IAAI,CAAC/B,mBAAmB,CAACiB,6BAA6B,CAAC;MAAEX,IAAI,EAAE+C;IAAQ,CAAE,CAAC,CAACjB,IAAI,CACpFvC,GAAG,CAACwC,QAAQ,IAAG;MACb,OAAO;QACLC,OAAO,EAAED,QAAQ,CAACE,UAAU,KAAK,CAAC;QAAE;QACpCC,OAAO,EAAEH,QAAQ,CAACa,OAAO,IAAI,EAAE;QAC/BT,IAAI,EAAE,CAACpB,IAAI,CAAC,CAAC;OACO;IACxB,CAAC,CAAC,CACH;EACH;EAEA;EACAiC,eAAeA,CAAC1C,OAAe;IAC7B;IACA;IACA,MAAM,IAAI2C,KAAK,CAAC,mEAAmE,CAAC;EACtF;;;uCAlHWzD,gBAAgB,EAAA0D,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAA5D,gBAAA,GAAA0D,EAAA,CAAAC,QAAA,CAAAE,EAAA,CAAAC,UAAA;IAAA;EAAA;;;aAAhB9D,gBAAgB;MAAA+D,OAAA,EAAhB/D,gBAAgB,CAAAgE,IAAA;MAAAC,UAAA,EAFf;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}