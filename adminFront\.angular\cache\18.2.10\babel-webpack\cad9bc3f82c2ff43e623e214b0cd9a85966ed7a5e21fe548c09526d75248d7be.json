{"ast": null, "code": "export var EnumQuotationStatus;\n(function (EnumQuotationStatus) {\n  EnumQuotationStatus[EnumQuotationStatus[\"\\u5F85\\u5831\\u50F9\"] = 1] = \"\\u5F85\\u5831\\u50F9\";\n  EnumQuotationStatus[EnumQuotationStatus[\"\\u5DF2\\u5831\\u50F9\"] = 2] = \"\\u5DF2\\u5831\\u50F9\";\n  EnumQuotationStatus[EnumQuotationStatus[\"\\u5DF2\\u7C3D\\u56DE\"] = 3] = \"\\u5DF2\\u7C3D\\u56DE\";\n})(EnumQuotationStatus || (EnumQuotationStatus = {}));", "map": {"version": 3, "names": ["EnumQuotationStatus"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\shared\\enum\\enumQuotationStatus.ts"], "sourcesContent": ["export enum EnumQuotationStatus {\r\n  待報價 = 1,\r\n  已報價 = 2,\r\n  已簽回 = 3,\r\n}\r\n"], "mappings": "AAAA,WAAYA,mBAIX;AAJD,WAAYA,mBAAmB;EAC7BA,mBAAA,CAAAA,mBAAA,kDAAO;EACPA,mBAAA,CAAAA,mBAAA,kDAAO;EACPA,mBAAA,CAAAA,mBAAA,kDAAO;AACT,CAAC,EAJWA,mBAAmB,KAAnBA,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}