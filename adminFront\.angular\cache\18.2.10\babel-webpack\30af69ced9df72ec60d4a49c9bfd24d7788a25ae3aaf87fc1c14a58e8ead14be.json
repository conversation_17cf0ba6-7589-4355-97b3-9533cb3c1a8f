{"ast": null, "code": "import { NbEvaIconsModule } from '@nebular/eva-icons';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { NbButtonModule, NbCardModule, NbCheckboxModule, NbFormFieldModule, NbInputModule, NbLayoutModule, NbSelectModule, NbTableModule, NbTabsetModule, NbTreeGridModule, NbRadioModule, NbIconModule, NbDatepickerModule, NbTagModule, NbAutocompleteModule } from '@nebular/theme';\nimport { NgbModule } from '@ng-bootstrap/ng-bootstrap';\nimport { PaginationComponent } from './pagination/pagination.component';\nimport { ThemeModule } from '../../@theme/theme.module';\nimport { BreadcrumbComponent } from './breadcrumb/breadcrumb.component';\nimport { DefaultTagAutoCompleteComponent } from './default-tag-auto-complete/default-tag-auto-complete.component';\nimport { TagInputDirectiveComponent } from './tag-input-directive/tag-input-directive.component';\nimport { BaseLabelDirective } from 'src/app/@theme/directives/label.directive';\nimport * as i0 from \"@angular/core\";\nexport class SharedModule {\n  static {\n    this.ɵfac = function SharedModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || SharedModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: SharedModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [CommonModule, NbCardModule, NgbModule, FormsModule, NbLayoutModule, NbCheckboxModule, NbInputModule, NbButtonModule, NbSelectModule, NbTableModule, NbTabsetModule, NbFormFieldModule, NbTreeGridModule,\n      //Ng2SmartTableModule,\n      NbRadioModule, NbEvaIconsModule, NbIconModule, NbDatepickerModule, NgbModule, ThemeModule, NbTagModule, NbAutocompleteModule, PaginationComponent, DefaultTagAutoCompleteComponent, TagInputDirectiveComponent, FormsModule, NbLayoutModule, NbCardModule, NbCheckboxModule, NbInputModule, NbButtonModule, NbSelectModule, NbTableModule, NbTabsetModule, NbFormFieldModule, NbTreeGridModule,\n      //Ng2SmartTableModule,\n      NbRadioModule, NbEvaIconsModule, NbIconModule, NbDatepickerModule, NgbModule, ThemeModule, NbTagModule, NbAutocompleteModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(SharedModule, {\n    imports: [CommonModule, NbCardModule, NgbModule, FormsModule, NbLayoutModule, NbCheckboxModule, NbInputModule, NbButtonModule, NbSelectModule, NbTableModule, NbTabsetModule, NbFormFieldModule, NbTreeGridModule,\n    //Ng2SmartTableModule,\n    NbRadioModule, NbEvaIconsModule, NbIconModule, NbDatepickerModule, NgbModule, ThemeModule, NbTagModule, NbAutocompleteModule, PaginationComponent, BreadcrumbComponent, DefaultTagAutoCompleteComponent, TagInputDirectiveComponent, BaseLabelDirective],\n    exports: [FormsModule, NbLayoutModule, NbCardModule, NbCheckboxModule, NbInputModule, NbButtonModule, NbSelectModule, NbTableModule, NbTabsetModule, NbFormFieldModule, NbTreeGridModule,\n    //Ng2SmartTableModule,\n    NbRadioModule, NbEvaIconsModule, NbIconModule, NbDatepickerModule, NgbModule, ThemeModule, NbTagModule, NbAutocompleteModule,\n    // 自製Component\n    BreadcrumbComponent, PaginationComponent, DefaultTagAutoCompleteComponent, TagInputDirectiveComponent, BaseLabelDirective]\n  });\n})();", "map": {"version": 3, "names": ["NbEvaIconsModule", "CommonModule", "FormsModule", "NbButtonModule", "NbCardModule", "NbCheckboxModule", "NbFormFieldModule", "NbInputModule", "NbLayoutModule", "NbSelectModule", "NbTableModule", "NbTabsetModule", "NbTreeGridModule", "NbRadioModule", "NbIconModule", "NbDatepickerModule", "NbTagModule", "NbAutocompleteModule", "NgbModule", "PaginationComponent", "ThemeModule", "BreadcrumbComponent", "DefaultTagAutoCompleteComponent", "TagInputDirectiveComponent", "BaseLabelDirective", "SharedModule", "imports", "exports"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\components\\shared.module.ts"], "sourcesContent": ["import { NbEvaIconsModule } from '@nebular/eva-icons';\r\nimport { NgModule } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { FormsModule } from '@angular/forms';\r\nimport {\r\n  NbButtonModule, NbCardModule, NbCheckboxModule, NbFormFieldModule,\r\n  NbInputModule, NbLayoutModule, NbSelectModule, NbTableModule,\r\n  NbTabsetModule, NbTreeGridModule, NbRadioModule, NbIconModule,\r\n  NbDatepickerModule,\r\n  NbTagModule,\r\n  NbAutocompleteModule\r\n} from '@nebular/theme';\r\nimport { NgbModule } from '@ng-bootstrap/ng-bootstrap';\r\nimport { PaginationComponent } from './pagination/pagination.component';\r\nimport { ThemeModule } from '../../@theme/theme.module';\r\nimport { BreadcrumbComponent } from './breadcrumb/breadcrumb.component';\r\nimport { DefaultTagAutoCompleteComponent } from './default-tag-auto-complete/default-tag-auto-complete.component';\r\nimport { TagInputDirectiveComponent } from './tag-input-directive/tag-input-directive.component';\r\nimport { BaseLabelDirective } from 'src/app/@theme/directives/label.directive';\r\nimport { FileUploadComponent } from './file-upload/file-upload-enhanced.component';\r\n\r\n@NgModule({\r\n    imports: [\r\n        CommonModule,\r\n        NbCardModule,\r\n        NgbModule,\r\n        FormsModule,\r\n        NbLayoutModule,\r\n        NbCheckboxModule,\r\n        NbInputModule,\r\n        NbButtonModule,\r\n        NbSelectModule,\r\n        NbTableModule,\r\n        NbTabsetModule,\r\n        NbFormFieldModule,\r\n        NbTreeGridModule,\r\n        //Ng2SmartTableModule,\r\n        NbRadioModule,\r\n        NbEvaIconsModule,\r\n        NbIconModule,\r\n        NbDatepickerModule,\r\n        NgbModule,\r\n        ThemeModule,\r\n        NbTagModule,\r\n        NbAutocompleteModule,\r\n        PaginationComponent,\r\n        BreadcrumbComponent,\r\n        DefaultTagAutoCompleteComponent,\r\n        TagInputDirectiveComponent,\r\n        BaseLabelDirective,\r\n    ],\r\n    exports: [\r\n        FormsModule,\r\n        NbLayoutModule,\r\n        NbCardModule,\r\n        NbCheckboxModule,\r\n        NbInputModule,\r\n        NbButtonModule,\r\n        NbSelectModule,\r\n        NbTableModule,\r\n        NbTabsetModule,\r\n        NbFormFieldModule,\r\n        NbTreeGridModule,\r\n        //Ng2SmartTableModule,\r\n        NbRadioModule,\r\n        NbEvaIconsModule,\r\n        NbIconModule,\r\n        NbDatepickerModule,\r\n        NgbModule,\r\n        ThemeModule,\r\n        NbTagModule,\r\n        NbAutocompleteModule,\r\n        // 自製Component\r\n        BreadcrumbComponent,\r\n        PaginationComponent,\r\n        DefaultTagAutoCompleteComponent,\r\n        TagInputDirectiveComponent,\r\n        BaseLabelDirective\r\n    ]\r\n})\r\nexport class SharedModule { }\r\n"], "mappings": "AAAA,SAASA,gBAAgB,QAAQ,oBAAoB;AAErD,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SACEC,cAAc,EAAEC,YAAY,EAAEC,gBAAgB,EAAEC,iBAAiB,EACjEC,aAAa,EAAEC,cAAc,EAAEC,cAAc,EAAEC,aAAa,EAC5DC,cAAc,EAAEC,gBAAgB,EAAEC,aAAa,EAAEC,YAAY,EAC7DC,kBAAkB,EAClBC,WAAW,EACXC,oBAAoB,QACf,gBAAgB;AACvB,SAASC,SAAS,QAAQ,4BAA4B;AACtD,SAASC,mBAAmB,QAAQ,mCAAmC;AACvE,SAASC,WAAW,QAAQ,2BAA2B;AACvD,SAASC,mBAAmB,QAAQ,mCAAmC;AACvE,SAASC,+BAA+B,QAAQ,iEAAiE;AACjH,SAASC,0BAA0B,QAAQ,qDAAqD;AAChG,SAASC,kBAAkB,QAAQ,2CAA2C;;AA8D9E,OAAM,MAAOC,YAAY;;;uCAAZA,YAAY;IAAA;EAAA;;;YAAZA;IAAY;EAAA;;;gBAzDjBxB,YAAY,EACZG,YAAY,EACZc,SAAS,EACThB,WAAW,EACXM,cAAc,EACdH,gBAAgB,EAChBE,aAAa,EACbJ,cAAc,EACdM,cAAc,EACdC,aAAa,EACbC,cAAc,EACdL,iBAAiB,EACjBM,gBAAgB;MAChB;MACAC,aAAa,EACbb,gBAAgB,EAChBc,YAAY,EACZC,kBAAkB,EAClBG,SAAS,EACTE,WAAW,EACXJ,WAAW,EACXC,oBAAoB,EACpBE,mBAAmB,EAEnBG,+BAA+B,EAC/BC,0BAA0B,EAI1BrB,WAAW,EACXM,cAAc,EACdJ,YAAY,EACZC,gBAAgB,EAChBE,aAAa,EACbJ,cAAc,EACdM,cAAc,EACdC,aAAa,EACbC,cAAc,EACdL,iBAAiB,EACjBM,gBAAgB;MAChB;MACAC,aAAa,EACbb,gBAAgB,EAChBc,YAAY,EACZC,kBAAkB,EAClBG,SAAS,EACTE,WAAW,EACXJ,WAAW,EACXC,oBAAoB;IAAA;EAAA;;;2EASfQ,YAAY;IAAAC,OAAA,GAzDjBzB,YAAY,EACZG,YAAY,EACZc,SAAS,EACThB,WAAW,EACXM,cAAc,EACdH,gBAAgB,EAChBE,aAAa,EACbJ,cAAc,EACdM,cAAc,EACdC,aAAa,EACbC,cAAc,EACdL,iBAAiB,EACjBM,gBAAgB;IAChB;IACAC,aAAa,EACbb,gBAAgB,EAChBc,YAAY,EACZC,kBAAkB,EAClBG,SAAS,EACTE,WAAW,EACXJ,WAAW,EACXC,oBAAoB,EACpBE,mBAAmB,EACnBE,mBAAmB,EACnBC,+BAA+B,EAC/BC,0BAA0B,EAC1BC,kBAAkB;IAAAG,OAAA,GAGlBzB,WAAW,EACXM,cAAc,EACdJ,YAAY,EACZC,gBAAgB,EAChBE,aAAa,EACbJ,cAAc,EACdM,cAAc,EACdC,aAAa,EACbC,cAAc,EACdL,iBAAiB,EACjBM,gBAAgB;IAChB;IACAC,aAAa,EACbb,gBAAgB,EAChBc,YAAY,EACZC,kBAAkB,EAClBG,SAAS,EACTE,WAAW,EACXJ,WAAW,EACXC,oBAAoB;IACpB;IACAI,mBAAmB,EACnBF,mBAAmB,EACnBG,+BAA+B,EAC/BC,0BAA0B,EAC1BC,kBAAkB;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}