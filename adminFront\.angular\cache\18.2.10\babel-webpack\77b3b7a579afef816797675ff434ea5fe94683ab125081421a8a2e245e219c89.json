{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { Component, ViewChild } from '@angular/core';\nimport { NbCardModule, NbCheckboxModule, NbInputModule, NbOptionModule, NbSelectModule } from '@nebular/theme';\nimport { BreadcrumbComponent } from '../../components/breadcrumb/breadcrumb.component';\nimport { FormsModule } from '@angular/forms';\nimport { NgFor, NgIf } from '@angular/common';\nimport { PaginationComponent } from '../../components/pagination/pagination.component';\nimport { BaseComponent } from '../../components/base/baseComponent';\nimport * as moment from 'moment';\nimport { FullCalendarModule } from '@fullcalendar/angular';\nimport interactionPlugin from '@fullcalendar/interaction';\nimport dayGridPlugin from '@fullcalendar/daygrid';\nimport timeGridPlugin from '@fullcalendar/timegrid';\nimport listPlugin from '@fullcalendar/list';\nimport bootstrapPlugin from '@fullcalendar/bootstrap';\nimport { CalendarModule } from 'primeng/calendar';\nlet FinaldochouseManagementComponent = class FinaldochouseManagementComponent extends BaseComponent {\n  constructor(_allow, enumHelper, dialogService, message, valid, finalDocumentService, pettern, router, route, destroyref, _eventService, location, _houseService, fileService) {\n    super(_allow);\n    this._allow = _allow;\n    this.enumHelper = enumHelper;\n    this.dialogService = dialogService;\n    this.message = message;\n    this.valid = valid;\n    this.finalDocumentService = finalDocumentService;\n    this.pettern = pettern;\n    this.router = router;\n    this.route = route;\n    this.destroyref = destroyref;\n    this._eventService = _eventService;\n    this.location = location;\n    this._houseService = _houseService;\n    this.fileService = fileService;\n    this.calendarOptions = {\n      plugins: [interactionPlugin, dayGridPlugin, timeGridPlugin, listPlugin, timeGridPlugin, bootstrapPlugin],\n      locale: 'zh-tw',\n      headerToolbar: {\n        left: 'prev',\n        center: 'title',\n        right: 'next'\n      }\n    };\n    this.file = null;\n    // request\n    this.getListFinalDocRequest = {};\n    this.uploadFinaldocRequest = {};\n    // response\n    this.listFinalDoc = [];\n    this.maxDate = new Date();\n  }\n  ngOnInit() {\n    this.route.paramMap.subscribe(params => {\n      if (params) {\n        const idParam = params.get('id1');\n        const id = idParam ? +idParam : 0;\n        this.buildCaseId = id;\n        const idParam2 = params.get('id2');\n        const id2 = idParam2 ? +idParam2 : 0;\n        this.currentHouseID = id2;\n        this.getList();\n        this.getHouseById();\n      }\n    });\n  }\n  addNew(ref) {\n    this.CApproveRemark = null;\n    this.CDocumentName = null;\n    this.CNote = null;\n    this.file = null;\n    this.dialogService.open(ref);\n  }\n  openPdfInNewTab(data) {\n    if (data) {\n      let fileUrl;\n      if (data.CSignDate && data.CSign) {\n        fileUrl = data.CFileAfter;\n      } else {\n        fileUrl = data.CFileBefore;\n      }\n      // 使用 FileService.GetFile 取得檔案 blob\n      this.fileService.getFile(fileUrl, data.CDocumentName).subscribe({\n        next: blob => {\n          // 建立 blob URL\n          const url = URL.createObjectURL(blob);\n          // 在新分頁開啟 PDF\n          window.open(url, '_blank');\n          // 延遲清理 URL 以確保檔案能正確載入\n          setTimeout(() => URL.revokeObjectURL(url), 10000);\n        },\n        error: error => {\n          console.error('取得檔案失敗:', error);\n          this.message.showErrorMSG('無法開啟檔案，請稍後再試');\n        }\n      });\n    }\n  }\n  goBack() {\n    this._eventService.push({\n      action: \"GET_BUILDCASE\" /* EEvent.GET_BUILDCASE */,\n      payload: this.buildCaseId\n    });\n    this.location.back();\n  }\n  onFileSelected(event) {\n    const file = event.target.files[0];\n    const fileRegex = /pdf/i;\n    if (!fileRegex.test(file.type)) {\n      this.message.showErrorMSG('文件格式不正確，僅允許 pdf 文件');\n      return;\n    }\n    if (file) {\n      const allowedTypes = ['application/pdf'];\n      if (allowedTypes.includes(file.type)) {\n        this.fileName = file.name;\n        this.file = file;\n      }\n    }\n  }\n  clearFile() {\n    if (this.file) {\n      this.file = null;\n      this.fileName = null;\n    }\n  }\n  getHouseById() {\n    this._houseService.apiHouseGetHouseByIdPost$Json({\n      body: {\n        CHouseID: this.currentHouseID\n      }\n    }).subscribe(res => {\n      if (res.Entries && res.StatusCode == 0) {\n        this.houseByID = res.Entries;\n      }\n    });\n  }\n  getList() {\n    this.getListFinalDocRequest.PageSize = this.pageSize;\n    this.getListFinalDocRequest.PageIndex = this.pageIndex;\n    if (this.currentHouseID != 0) {\n      this.getListFinalDocRequest.CHouseID = this.currentHouseID;\n      this.finalDocumentService.apiFinalDocumentGetListFinalDocByHousePost$Json({\n        body: this.getListFinalDocRequest\n      }).pipe().subscribe(res => {\n        if (res.StatusCode == 0) {\n          if (res.Entries) {\n            this.listFinalDoc = res.Entries;\n            this.totalRecords = res.TotalItems;\n            if (this.listFinalDoc) {\n              for (let i = 0; i < this.listFinalDoc.length; i++) {\n                if (this.listFinalDoc[i].CSignDate) this.listFinalDoc[i].CSignDate = moment(this.listFinalDoc[i].CSignDate).format(\"yyyy/MM/DD H:mm:ss\");\n              }\n            }\n          }\n        }\n      });\n    }\n  }\n  onCreateFinalDoc(ref) {\n    this.validation();\n    if (this.valid.errorMessages.length > 0) {\n      this.message.showErrorMSGs(this.valid.errorMessages);\n      return;\n    }\n    this.finalDocumentService.apiFinalDocumentUploadFinalDocPost$Json({\n      body: {\n        CHouseID: this.currentHouseID,\n        CBuildCaseID: this.buildCaseId,\n        CDocumentName: this.CDocumentName,\n        CApproveRemark: this.CApproveRemark,\n        CNote: this.CNote,\n        CFile: this.file\n      }\n    }).subscribe(res => {\n      if (res.Entries && res.StatusCode == 0) {\n        this.getList();\n        this.message.showSucessMSG(\"執行成功\");\n        ref.close();\n      } else {\n        this.message.showErrorMSG(res.Message);\n      }\n    });\n  }\n  convertToBlob(data, mimeType = 'application/octet-stream') {\n    if (data instanceof ArrayBuffer) {\n      return new Blob([data], {\n        type: mimeType\n      });\n    } else if (typeof data === 'string') {\n      return new Blob([data], {\n        type: mimeType\n      });\n    } else {\n      return undefined;\n    }\n  }\n  validation() {\n    this.valid.clear();\n    this.valid.required('[文件格式不正確]', this.file);\n    this.valid.required('[文件名稱]', this.CDocumentName);\n    this.valid.required('[送審資訊]', this.CApproveRemark);\n    this.valid.required('[系統操作說明]', this.CNote);\n  }\n};\n__decorate([ViewChild('calendar')], FinaldochouseManagementComponent.prototype, \"calendarComponent\", void 0);\nFinaldochouseManagementComponent = __decorate([Component({\n  selector: 'app-finaldochouse-management',\n  standalone: true,\n  imports: [NbCardModule, BreadcrumbComponent, NbInputModule, FormsModule, NbSelectModule, NbOptionModule, NgIf, NgFor, PaginationComponent, NbCheckboxModule, FullCalendarModule, CalendarModule],\n  templateUrl: './finaldochouse-management.component.html',\n  styleUrl: './finaldochouse-management.component.scss'\n})], FinaldochouseManagementComponent);\nexport { FinaldochouseManagementComponent };", "map": {"version": 3, "names": ["Component", "ViewChild", "NbCardModule", "NbCheckboxModule", "NbInputModule", "NbOptionModule", "NbSelectModule", "BreadcrumbComponent", "FormsModule", "<PERSON><PERSON><PERSON>", "NgIf", "PaginationComponent", "BaseComponent", "moment", "FullCalendarModule", "interactionPlugin", "dayGridPlugin", "timeGridPlugin", "listPlugin", "bootstrapPlugin", "CalendarModule", "FinaldochouseManagementComponent", "constructor", "_allow", "enum<PERSON>elper", "dialogService", "message", "valid", "finalDocumentService", "pettern", "router", "route", "destroyref", "_eventService", "location", "_houseService", "fileService", "calendarOptions", "plugins", "locale", "headerToolbar", "left", "center", "right", "file", "getListFinalDocRequest", "uploadFinaldocRequest", "listFinalDoc", "maxDate", "Date", "ngOnInit", "paramMap", "subscribe", "params", "idParam", "get", "id", "buildCaseId", "idParam2", "id2", "currentHouseID", "getList", "getHouseById", "addNew", "ref", "CApproveRemark", "CDocumentName", "CNote", "open", "openPdfInNewTab", "data", "fileUrl", "CSignDate", "CSign", "CFileAfter", "CFileBefore", "getFile", "next", "blob", "url", "URL", "createObjectURL", "window", "setTimeout", "revokeObjectURL", "error", "console", "showErrorMSG", "goBack", "push", "action", "payload", "back", "onFileSelected", "event", "target", "files", "fileRegex", "test", "type", "allowedTypes", "includes", "fileName", "name", "clearFile", "apiHouseGetHouseByIdPost$Json", "body", "CHouseID", "res", "Entries", "StatusCode", "houseByID", "PageSize", "pageSize", "PageIndex", "pageIndex", "apiFinalDocumentGetListFinalDocByHousePost$Json", "pipe", "totalRecords", "TotalItems", "i", "length", "format", "onCreateFinalDoc", "validation", "errorMessages", "showErrorMSGs", "apiFinalDocumentUploadFinalDocPost$Json", "CBuildCaseID", "CFile", "showSucessMSG", "close", "Message", "convertToBlob", "mimeType", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Blob", "undefined", "clear", "required", "__decorate", "selector", "standalone", "imports", "templateUrl", "styleUrl"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\household-management\\finaldochouse-management\\finaldochouse-management.component.ts"], "sourcesContent": ["import { Component, DestroyRef, OnInit, ViewChild } from '@angular/core';\r\nimport { NbCardModule, NbCheckboxModule, NbDialogService, NbInputModule, NbOptionModule, NbSelectModule } from '@nebular/theme';\r\nimport { BreadcrumbComponent } from '../../components/breadcrumb/breadcrumb.component';\r\nimport { FormsModule } from '@angular/forms';\r\nimport { NgFor, NgIf, NgTemplateOutlet } from '@angular/common';\r\nimport { PaginationComponent } from '../../components/pagination/pagination.component';\r\nimport { StatusPipe } from 'src/app/@theme/pipes/mapping.pipe';\r\nimport { EnumHelper } from 'src/app/shared/helper/enumHelper';\r\nimport { AllowHelper } from 'src/app/shared/helper/allowHelper';\r\nimport { ValidationHelper } from 'src/app/shared/helper/validationHelper';\r\nimport { FinalDocumentService, HouseService } from 'src/services/api/services';\r\nimport { FileService } from 'src/services/api/services/File.service';\r\nimport { PetternHelper } from 'src/app/shared/helper/petternHelper';\r\nimport { ActivatedRoute, Router } from '@angular/router';\r\nimport { BaseComponent } from '../../components/base/baseComponent';\r\nimport { GetFinalDocListByHouse, TblFinalDocument, TblHouse } from 'src/services/api/models';\r\nimport * as moment from 'moment';\r\nimport { FullCalendarComponent, FullCalendarModule } from '@fullcalendar/angular';\r\nimport { Calendar, CalendarOptions } from 'fullcalendar';\r\nimport interactionPlugin from '@fullcalendar/interaction';\r\nimport dayGridPlugin from '@fullcalendar/daygrid';\r\nimport timeGridPlugin from '@fullcalendar/timegrid';\r\nimport listPlugin from '@fullcalendar/list';\r\nimport bootstrapPlugin from '@fullcalendar/bootstrap';\r\nimport { CalendarModule } from 'primeng/calendar';\r\nimport { MessageService } from 'src/app/shared/services/message.service';\r\nimport { EventService, EEvent } from 'src/app/shared/services/event.service';\r\nimport { Location } from '@angular/common';\r\nimport { ApiFinalDocumentUploadFinalDocPost$Json$Params } from 'src/services/api/fn/final-document/api-final-document-upload-final-doc-post-json';\r\n\r\n@Component({\r\n  selector: 'app-finaldochouse-management',\r\n  standalone: true,\r\n  imports: [\r\n    NbCardModule,\r\n    BreadcrumbComponent,\r\n    NbInputModule,\r\n    FormsModule,\r\n    NbSelectModule,\r\n    NbOptionModule,\r\n    NgIf,\r\n    NgFor,\r\n    PaginationComponent,\r\n    NbCheckboxModule,\r\n    FullCalendarModule,\r\n    CalendarModule\r\n  ],\r\n  templateUrl: './finaldochouse-management.component.html',\r\n  styleUrl: './finaldochouse-management.component.scss'\r\n})\r\nexport class FinaldochouseManagementComponent extends BaseComponent implements OnInit {\r\n  @ViewChild('calendar') calendarComponent: FullCalendarComponent;\r\n  calendarApi: Calendar;\r\n  maxDate!: Date;\r\n\r\n  calendarOptions: CalendarOptions = {\r\n    plugins: [\r\n      interactionPlugin,\r\n      dayGridPlugin,\r\n      timeGridPlugin,\r\n      listPlugin,\r\n      timeGridPlugin,\r\n      bootstrapPlugin\r\n    ],\r\n    locale: 'zh-tw',\r\n    headerToolbar: {\r\n      left: 'prev',\r\n      center: 'title',\r\n      right: 'next'\r\n    },\r\n  };\r\n  constructor(\r\n    private _allow: AllowHelper,\r\n    private enumHelper: EnumHelper,\r\n    private dialogService: NbDialogService,\r\n    private message: MessageService,\r\n    private valid: ValidationHelper,\r\n    private finalDocumentService: FinalDocumentService,\r\n    private pettern: PetternHelper,\r\n    private router: Router,\r\n    private route: ActivatedRoute,\r\n    private destroyref: DestroyRef,\r\n    private _eventService: EventService,\r\n    private location: Location,\r\n    private _houseService: HouseService,\r\n    private fileService: FileService\r\n  ) {\r\n    super(_allow);\r\n    this.maxDate = new Date();\r\n  }\r\n\r\n  currentHouseID: number;\r\n  buildCaseId: number;\r\n  fileName: string | null;\r\n  file: File | null = null;\r\n  CDocumentName: string | null;\r\n  CNote: string | null;\r\n  CApproveRemark: string | null;\r\n  // request\r\n  getListFinalDocRequest: GetFinalDocListByHouse = {};\r\n  uploadFinaldocRequest: ApiFinalDocumentUploadFinalDocPost$Json$Params = {};\r\n\r\n  // response\r\n  listFinalDoc: TblFinalDocument[] = [];\r\n  houseByID: TblHouse;\r\n\r\n  override ngOnInit() {\r\n    this.route.paramMap.subscribe(params => {\r\n      if (params) {\r\n        const idParam = params.get('id1');\r\n        const id = idParam ? +idParam : 0;\r\n        this.buildCaseId = id;\r\n        const idParam2 = params.get('id2');\r\n        const id2 = idParam2 ? +idParam2 : 0;\r\n        this.currentHouseID = id2;\r\n        this.getList();\r\n        this.getHouseById();\r\n      }\r\n    });\r\n  }\r\n\r\n  addNew(ref: any) {\r\n    this.CApproveRemark = null;\r\n    this.CDocumentName = null;\r\n    this.CNote = null;\r\n    this.file = null;\r\n    this.dialogService.open(ref)\r\n  }\r\n  openPdfInNewTab(data: TblFinalDocument) {\r\n    if (data) {\r\n      let fileUrl: string;\r\n\r\n      if (data.CSignDate && data.CSign) {\r\n        fileUrl = data.CFileAfter!;\r\n      } else {\r\n        fileUrl = data.CFileBefore!;\r\n      }\r\n\r\n      // 使用 FileService.GetFile 取得檔案 blob\r\n      this.fileService.getFile(fileUrl, data.CDocumentName).subscribe({\r\n        next: (blob: Blob) => {\r\n          // 建立 blob URL\r\n          const url = URL.createObjectURL(blob);\r\n          // 在新分頁開啟 PDF\r\n          window.open(url, '_blank');\r\n          // 延遲清理 URL 以確保檔案能正確載入\r\n          setTimeout(() => URL.revokeObjectURL(url), 10000);\r\n        },\r\n        error: (error: any) => {\r\n          console.error('取得檔案失敗:', error);\r\n          this.message.showErrorMSG('無法開啟檔案，請稍後再試');\r\n        }\r\n      });\r\n    }\r\n  }\r\n\r\n  goBack() {\r\n    this._eventService.push({\r\n      action: EEvent.GET_BUILDCASE,\r\n      payload: this.buildCaseId\r\n    })\r\n    this.location.back()\r\n  }\r\n\r\n  onFileSelected(event: any) {\r\n    const file: File = event.target.files[0];\r\n    const fileRegex = /pdf/i;\r\n    if (!fileRegex.test(file.type)) {\r\n      this.message.showErrorMSG('文件格式不正確，僅允許 pdf 文件');\r\n      return;\r\n    }\r\n    if (file) {\r\n      const allowedTypes = ['application/pdf'];\r\n      if (allowedTypes.includes(file.type)) {\r\n        this.fileName = file.name;\r\n        this.file = file;\r\n      }\r\n    }\r\n  }\r\n\r\n  clearFile() {\r\n    if (this.file) {\r\n      this.file = null;\r\n      this.fileName = null;\r\n    }\r\n  }\r\n\r\n  getHouseById() {\r\n    this._houseService.apiHouseGetHouseByIdPost$Json({\r\n      body: { CHouseID: this.currentHouseID }\r\n    }).subscribe(res => {\r\n      if (res.Entries && res.StatusCode == 0) {\r\n        this.houseByID = res.Entries\r\n      }\r\n    })\r\n  }\r\n\r\n  getList() {\r\n    this.getListFinalDocRequest.PageSize = this.pageSize;\r\n    this.getListFinalDocRequest.PageIndex = this.pageIndex;\r\n    if (this.currentHouseID != 0) {\r\n      this.getListFinalDocRequest.CHouseID = this.currentHouseID;\r\n      this.finalDocumentService.apiFinalDocumentGetListFinalDocByHousePost$Json({ body: this.getListFinalDocRequest })\r\n        .pipe()\r\n        .subscribe(res => {\r\n          if (res.StatusCode == 0) {\r\n            if (res.Entries) {\r\n              this.listFinalDoc = res.Entries;\r\n              this.totalRecords = res.TotalItems!;\r\n              if (this.listFinalDoc) {\r\n                for (let i = 0; i < this.listFinalDoc.length; i++) {\r\n                  if (this.listFinalDoc[i].CSignDate)\r\n                    this.listFinalDoc[i].CSignDate = moment(this.listFinalDoc[i].CSignDate).format(\"yyyy/MM/DD H:mm:ss\");\r\n                }\r\n              }\r\n            }\r\n          }\r\n        })\r\n    }\r\n  }\r\n\r\n  onCreateFinalDoc(ref: any) {\r\n    this.validation()\r\n    if (this.valid.errorMessages.length > 0) {\r\n      this.message.showErrorMSGs(this.valid.errorMessages);\r\n      return;\r\n    }\r\n    this.finalDocumentService.apiFinalDocumentUploadFinalDocPost$Json({\r\n      body: {\r\n        CHouseID: this.currentHouseID,\r\n        CBuildCaseID: this.buildCaseId,\r\n        CDocumentName: this.CDocumentName!,\r\n        CApproveRemark: this.CApproveRemark!,\r\n        CNote: this.CNote!,\r\n        CFile: this.file as Blob\r\n      }\r\n    }).subscribe(res => {\r\n      if (res.Entries && res.StatusCode == 0) {\r\n        this.getList();\r\n        this.message.showSucessMSG(\"執行成功\");\r\n        ref.close();\r\n      } else {\r\n        this.message.showErrorMSG(res.Message!)\r\n      }\r\n    })\r\n  }\r\n\r\n  convertToBlob(data: string | ArrayBuffer | null, mimeType: string = 'application/octet-stream'): Blob | undefined {\r\n    if (data instanceof ArrayBuffer) {\r\n      return new Blob([data], { type: mimeType });\r\n    } else if (typeof data === 'string') {\r\n      return new Blob([data], { type: mimeType });\r\n    } else {\r\n      return undefined;\r\n    }\r\n  }\r\n\r\n  validation() {\r\n    this.valid.clear();\r\n    this.valid.required('[文件格式不正確]', this.file)\r\n    this.valid.required('[文件名稱]', this.CDocumentName)\r\n    this.valid.required('[送審資訊]', this.CApproveRemark)\r\n    this.valid.required('[系統操作說明]', this.CNote)\r\n  }\r\n}\r\n"], "mappings": ";AAAA,SAASA,SAAS,EAAsBC,SAAS,QAAQ,eAAe;AACxE,SAASC,YAAY,EAAEC,gBAAgB,EAAmBC,aAAa,EAAEC,cAAc,EAAEC,cAAc,QAAQ,gBAAgB;AAC/H,SAASC,mBAAmB,QAAQ,kDAAkD;AACtF,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,KAAK,EAAEC,IAAI,QAA0B,iBAAiB;AAC/D,SAASC,mBAAmB,QAAQ,kDAAkD;AAStF,SAASC,aAAa,QAAQ,qCAAqC;AAEnE,OAAO,KAAKC,MAAM,MAAM,QAAQ;AAChC,SAAgCC,kBAAkB,QAAQ,uBAAuB;AAEjF,OAAOC,iBAAiB,MAAM,2BAA2B;AACzD,OAAOC,aAAa,MAAM,uBAAuB;AACjD,OAAOC,cAAc,MAAM,wBAAwB;AACnD,OAAOC,UAAU,MAAM,oBAAoB;AAC3C,OAAOC,eAAe,MAAM,yBAAyB;AACrD,SAASC,cAAc,QAAQ,kBAAkB;AA0B1C,IAAMC,gCAAgC,GAAtC,MAAMA,gCAAiC,SAAQT,aAAa;EAqBjEU,YACUC,MAAmB,EACnBC,UAAsB,EACtBC,aAA8B,EAC9BC,OAAuB,EACvBC,KAAuB,EACvBC,oBAA0C,EAC1CC,OAAsB,EACtBC,MAAc,EACdC,KAAqB,EACrBC,UAAsB,EACtBC,aAA2B,EAC3BC,QAAkB,EAClBC,aAA2B,EAC3BC,WAAwB;IAEhC,KAAK,CAACb,MAAM,CAAC;IAfL,KAAAA,MAAM,GAANA,MAAM;IACN,KAAAC,UAAU,GAAVA,UAAU;IACV,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,oBAAoB,GAApBA,oBAAoB;IACpB,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,UAAU,GAAVA,UAAU;IACV,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,QAAQ,GAARA,QAAQ;IACR,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,WAAW,GAAXA,WAAW;IA9BrB,KAAAC,eAAe,GAAoB;MACjCC,OAAO,EAAE,CACPvB,iBAAiB,EACjBC,aAAa,EACbC,cAAc,EACdC,UAAU,EACVD,cAAc,EACdE,eAAe,CAChB;MACDoB,MAAM,EAAE,OAAO;MACfC,aAAa,EAAE;QACbC,IAAI,EAAE,MAAM;QACZC,MAAM,EAAE,OAAO;QACfC,KAAK,EAAE;;KAEV;IAwBD,KAAAC,IAAI,GAAgB,IAAI;IAIxB;IACA,KAAAC,sBAAsB,GAA2B,EAAE;IACnD,KAAAC,qBAAqB,GAAmD,EAAE;IAE1E;IACA,KAAAC,YAAY,GAAuB,EAAE;IAfnC,IAAI,CAACC,OAAO,GAAG,IAAIC,IAAI,EAAE;EAC3B;EAiBSC,QAAQA,CAAA;IACf,IAAI,CAACnB,KAAK,CAACoB,QAAQ,CAACC,SAAS,CAACC,MAAM,IAAG;MACrC,IAAIA,MAAM,EAAE;QACV,MAAMC,OAAO,GAAGD,MAAM,CAACE,GAAG,CAAC,KAAK,CAAC;QACjC,MAAMC,EAAE,GAAGF,OAAO,GAAG,CAACA,OAAO,GAAG,CAAC;QACjC,IAAI,CAACG,WAAW,GAAGD,EAAE;QACrB,MAAME,QAAQ,GAAGL,MAAM,CAACE,GAAG,CAAC,KAAK,CAAC;QAClC,MAAMI,GAAG,GAAGD,QAAQ,GAAG,CAACA,QAAQ,GAAG,CAAC;QACpC,IAAI,CAACE,cAAc,GAAGD,GAAG;QACzB,IAAI,CAACE,OAAO,EAAE;QACd,IAAI,CAACC,YAAY,EAAE;MACrB;IACF,CAAC,CAAC;EACJ;EAEAC,MAAMA,CAACC,GAAQ;IACb,IAAI,CAACC,cAAc,GAAG,IAAI;IAC1B,IAAI,CAACC,aAAa,GAAG,IAAI;IACzB,IAAI,CAACC,KAAK,GAAG,IAAI;IACjB,IAAI,CAACvB,IAAI,GAAG,IAAI;IAChB,IAAI,CAACnB,aAAa,CAAC2C,IAAI,CAACJ,GAAG,CAAC;EAC9B;EACAK,eAAeA,CAACC,IAAsB;IACpC,IAAIA,IAAI,EAAE;MACR,IAAIC,OAAe;MAEnB,IAAID,IAAI,CAACE,SAAS,IAAIF,IAAI,CAACG,KAAK,EAAE;QAChCF,OAAO,GAAGD,IAAI,CAACI,UAAW;MAC5B,CAAC,MAAM;QACLH,OAAO,GAAGD,IAAI,CAACK,WAAY;MAC7B;MAEA;MACA,IAAI,CAACvC,WAAW,CAACwC,OAAO,CAACL,OAAO,EAAED,IAAI,CAACJ,aAAa,CAAC,CAACd,SAAS,CAAC;QAC9DyB,IAAI,EAAGC,IAAU,IAAI;UACnB;UACA,MAAMC,GAAG,GAAGC,GAAG,CAACC,eAAe,CAACH,IAAI,CAAC;UACrC;UACAI,MAAM,CAACd,IAAI,CAACW,GAAG,EAAE,QAAQ,CAAC;UAC1B;UACAI,UAAU,CAAC,MAAMH,GAAG,CAACI,eAAe,CAACL,GAAG,CAAC,EAAE,KAAK,CAAC;QACnD,CAAC;QACDM,KAAK,EAAGA,KAAU,IAAI;UACpBC,OAAO,CAACD,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;UAC/B,IAAI,CAAC3D,OAAO,CAAC6D,YAAY,CAAC,cAAc,CAAC;QAC3C;OACD,CAAC;IACJ;EACF;EAEAC,MAAMA,CAAA;IACJ,IAAI,CAACvD,aAAa,CAACwD,IAAI,CAAC;MACtBC,MAAM;MACNC,OAAO,EAAE,IAAI,CAAClC;KACf,CAAC;IACF,IAAI,CAACvB,QAAQ,CAAC0D,IAAI,EAAE;EACtB;EAEAC,cAAcA,CAACC,KAAU;IACvB,MAAMlD,IAAI,GAASkD,KAAK,CAACC,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC;IACxC,MAAMC,SAAS,GAAG,MAAM;IACxB,IAAI,CAACA,SAAS,CAACC,IAAI,CAACtD,IAAI,CAACuD,IAAI,CAAC,EAAE;MAC9B,IAAI,CAACzE,OAAO,CAAC6D,YAAY,CAAC,oBAAoB,CAAC;MAC/C;IACF;IACA,IAAI3C,IAAI,EAAE;MACR,MAAMwD,YAAY,GAAG,CAAC,iBAAiB,CAAC;MACxC,IAAIA,YAAY,CAACC,QAAQ,CAACzD,IAAI,CAACuD,IAAI,CAAC,EAAE;QACpC,IAAI,CAACG,QAAQ,GAAG1D,IAAI,CAAC2D,IAAI;QACzB,IAAI,CAAC3D,IAAI,GAAGA,IAAI;MAClB;IACF;EACF;EAEA4D,SAASA,CAAA;IACP,IAAI,IAAI,CAAC5D,IAAI,EAAE;MACb,IAAI,CAACA,IAAI,GAAG,IAAI;MAChB,IAAI,CAAC0D,QAAQ,GAAG,IAAI;IACtB;EACF;EAEAxC,YAAYA,CAAA;IACV,IAAI,CAAC3B,aAAa,CAACsE,6BAA6B,CAAC;MAC/CC,IAAI,EAAE;QAAEC,QAAQ,EAAE,IAAI,CAAC/C;MAAc;KACtC,CAAC,CAACR,SAAS,CAACwD,GAAG,IAAG;MACjB,IAAIA,GAAG,CAACC,OAAO,IAAID,GAAG,CAACE,UAAU,IAAI,CAAC,EAAE;QACtC,IAAI,CAACC,SAAS,GAAGH,GAAG,CAACC,OAAO;MAC9B;IACF,CAAC,CAAC;EACJ;EAEAhD,OAAOA,CAAA;IACL,IAAI,CAAChB,sBAAsB,CAACmE,QAAQ,GAAG,IAAI,CAACC,QAAQ;IACpD,IAAI,CAACpE,sBAAsB,CAACqE,SAAS,GAAG,IAAI,CAACC,SAAS;IACtD,IAAI,IAAI,CAACvD,cAAc,IAAI,CAAC,EAAE;MAC5B,IAAI,CAACf,sBAAsB,CAAC8D,QAAQ,GAAG,IAAI,CAAC/C,cAAc;MAC1D,IAAI,CAAChC,oBAAoB,CAACwF,+CAA+C,CAAC;QAAEV,IAAI,EAAE,IAAI,CAAC7D;MAAsB,CAAE,CAAC,CAC7GwE,IAAI,EAAE,CACNjE,SAAS,CAACwD,GAAG,IAAG;QACf,IAAIA,GAAG,CAACE,UAAU,IAAI,CAAC,EAAE;UACvB,IAAIF,GAAG,CAACC,OAAO,EAAE;YACf,IAAI,CAAC9D,YAAY,GAAG6D,GAAG,CAACC,OAAO;YAC/B,IAAI,CAACS,YAAY,GAAGV,GAAG,CAACW,UAAW;YACnC,IAAI,IAAI,CAACxE,YAAY,EAAE;cACrB,KAAK,IAAIyE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACzE,YAAY,CAAC0E,MAAM,EAAED,CAAC,EAAE,EAAE;gBACjD,IAAI,IAAI,CAACzE,YAAY,CAACyE,CAAC,CAAC,CAAChD,SAAS,EAChC,IAAI,CAACzB,YAAY,CAACyE,CAAC,CAAC,CAAChD,SAAS,GAAG3D,MAAM,CAAC,IAAI,CAACkC,YAAY,CAACyE,CAAC,CAAC,CAAChD,SAAS,CAAC,CAACkD,MAAM,CAAC,oBAAoB,CAAC;cACxG;YACF;UACF;QACF;MACF,CAAC,CAAC;IACN;EACF;EAEAC,gBAAgBA,CAAC3D,GAAQ;IACvB,IAAI,CAAC4D,UAAU,EAAE;IACjB,IAAI,IAAI,CAACjG,KAAK,CAACkG,aAAa,CAACJ,MAAM,GAAG,CAAC,EAAE;MACvC,IAAI,CAAC/F,OAAO,CAACoG,aAAa,CAAC,IAAI,CAACnG,KAAK,CAACkG,aAAa,CAAC;MACpD;IACF;IACA,IAAI,CAACjG,oBAAoB,CAACmG,uCAAuC,CAAC;MAChErB,IAAI,EAAE;QACJC,QAAQ,EAAE,IAAI,CAAC/C,cAAc;QAC7BoE,YAAY,EAAE,IAAI,CAACvE,WAAW;QAC9BS,aAAa,EAAE,IAAI,CAACA,aAAc;QAClCD,cAAc,EAAE,IAAI,CAACA,cAAe;QACpCE,KAAK,EAAE,IAAI,CAACA,KAAM;QAClB8D,KAAK,EAAE,IAAI,CAACrF;;KAEf,CAAC,CAACQ,SAAS,CAACwD,GAAG,IAAG;MACjB,IAAIA,GAAG,CAACC,OAAO,IAAID,GAAG,CAACE,UAAU,IAAI,CAAC,EAAE;QACtC,IAAI,CAACjD,OAAO,EAAE;QACd,IAAI,CAACnC,OAAO,CAACwG,aAAa,CAAC,MAAM,CAAC;QAClClE,GAAG,CAACmE,KAAK,EAAE;MACb,CAAC,MAAM;QACL,IAAI,CAACzG,OAAO,CAAC6D,YAAY,CAACqB,GAAG,CAACwB,OAAQ,CAAC;MACzC;IACF,CAAC,CAAC;EACJ;EAEAC,aAAaA,CAAC/D,IAAiC,EAAEgE,QAAA,GAAmB,0BAA0B;IAC5F,IAAIhE,IAAI,YAAYiE,WAAW,EAAE;MAC/B,OAAO,IAAIC,IAAI,CAAC,CAAClE,IAAI,CAAC,EAAE;QAAE6B,IAAI,EAAEmC;MAAQ,CAAE,CAAC;IAC7C,CAAC,MAAM,IAAI,OAAOhE,IAAI,KAAK,QAAQ,EAAE;MACnC,OAAO,IAAIkE,IAAI,CAAC,CAAClE,IAAI,CAAC,EAAE;QAAE6B,IAAI,EAAEmC;MAAQ,CAAE,CAAC;IAC7C,CAAC,MAAM;MACL,OAAOG,SAAS;IAClB;EACF;EAEAb,UAAUA,CAAA;IACR,IAAI,CAACjG,KAAK,CAAC+G,KAAK,EAAE;IAClB,IAAI,CAAC/G,KAAK,CAACgH,QAAQ,CAAC,WAAW,EAAE,IAAI,CAAC/F,IAAI,CAAC;IAC3C,IAAI,CAACjB,KAAK,CAACgH,QAAQ,CAAC,QAAQ,EAAE,IAAI,CAACzE,aAAa,CAAC;IACjD,IAAI,CAACvC,KAAK,CAACgH,QAAQ,CAAC,QAAQ,EAAE,IAAI,CAAC1E,cAAc,CAAC;IAClD,IAAI,CAACtC,KAAK,CAACgH,QAAQ,CAAC,UAAU,EAAE,IAAI,CAACxE,KAAK,CAAC;EAC7C;CACD;AArNwByE,UAAA,EAAtB3I,SAAS,CAAC,UAAU,CAAC,C,0EAA0C;AADrDoB,gCAAgC,GAAAuH,UAAA,EApB5C5I,SAAS,CAAC;EACT6I,QAAQ,EAAE,8BAA8B;EACxCC,UAAU,EAAE,IAAI;EAChBC,OAAO,EAAE,CACP7I,YAAY,EACZK,mBAAmB,EACnBH,aAAa,EACbI,WAAW,EACXF,cAAc,EACdD,cAAc,EACdK,IAAI,EACJD,KAAK,EACLE,mBAAmB,EACnBR,gBAAgB,EAChBW,kBAAkB,EAClBM,cAAc,CACf;EACD4H,WAAW,EAAE,2CAA2C;EACxDC,QAAQ,EAAE;CACX,CAAC,C,EACW5H,gCAAgC,CAsN5C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}