{"ast": null, "code": "import * as i0 from \"@angular/core\";\nexport let ValidationHelper = /*#__PURE__*/(() => {\n  class ValidationHelper {\n    constructor() {\n      this.errorMessages = [];\n    }\n    required(name, value) {\n      if (value == null || value === undefined) {\n        this.addErrorMessage(name + ' 必填');\n      }\n      if (typeof String) {\n        if (value === '') {\n          this.addErrorMessage(name + ' 必填');\n        }\n      }\n    }\n    checkStartBeforeEnd(name, start, end) {\n      if (start && end) {\n        const startDate = new Date(start);\n        const endDate = new Date(end);\n        if (startDate > endDate) {\n          this.addErrorMessage(name + ' 开始日期不能大于结束日期');\n        }\n      }\n    }\n    isPhoneNumber(name, value) {\n      if (value !== null && value !== undefined && value !== '') {\n        const phoneRegex = /^\\+?\\d{1,3}[-.\\s]?\\(?\\d{1,3}\\)?[-.\\s]?\\d{3,4}[-.\\s]?\\d{4}$/;\n        if (!phoneRegex.test(value)) {\n          this.addErrorMessage(name + '電話號碼格式不正確');\n        }\n        if (value.length !== 10 || isNaN(value)) {\n          this.addErrorMessage(name + ' 長度=10');\n        }\n      }\n    }\n    isNaturalNumberInRange(name, value, min, max) {\n      if (value == null || value === undefined || value === '') {\n        this.addErrorMessage(name + ' 必填');\n      } else if (typeof value === 'string' && !/^\\d+$/.test(value)) {\n        this.addErrorMessage(name + ' 必須是數字');\n      } else {\n        const numValue = parseInt(value, 10);\n        if (numValue < min || numValue > max) {\n          this.addErrorMessage(name + ` 必須介於${min}到${max}之間`);\n        }\n      }\n    }\n    isStringMaxLength(name, value, maxLength) {\n      if (typeof value === 'string' && value.length > maxLength) {\n        this.addErrorMessage(name + ` 長度不能超過 ${maxLength} 個字元`);\n      }\n    }\n    pattern(name, value, pattern, errorDetail = '') {\n      if (!value) return;\n      const regex = new RegExp(pattern);\n      if (regex.test(value) === false) {\n        this.addErrorMessage(name + ' 格式錯誤' + errorDetail);\n      }\n    }\n    regExp(name, value, regEx) {\n      if (regEx.test(value) === false) {\n        this.addErrorMessage(name + ' 格式錯誤');\n      }\n    }\n    equal(name1, name2, value1, value2) {\n      if (value1 !== value2) {\n        this.addErrorMessage(name1 + ' 與 ' + name2 + ' 不相同');\n      }\n    }\n    selected(name, value) {\n      if (value.filter(x => x === '' || x === null || x === undefined).length > 0) {\n        this.addErrorMessage(name + ' 尚未全部選擇');\n      }\n    }\n    CheckTaiwanID(userid) {\n      const reg = /^[A-Z]{1}[1-2]{1}[0-9]{8}$/; //身份證的正規表示式\n      if (reg.test(userid)) {\n        const s = \"ABCDEFGHIJKLMNOPQRSTUVWXYZ\"; //把A取代成10,把B取代成11...的作法\n        const ct = [\"10\", \"11\", \"12\", \"13\", \"14\", \"15\", \"16\", \"17\", \"34\", \"18\", \"19\", \"20\", \"21\", \"22\", \"35\", \"23\", \"24\", \"25\", \"26\", \"27\", \"28\", \"29\", \"32\", \"30\", \"31\", \"33\"];\n        let i = s.indexOf(userid.charAt(0));\n        const tempuserid = ct[i] + userid.substr(1, 9); //若此身份證號若是A123456789=>10123456789\n        let num = parseInt(tempuserid.charAt(0)) * 1;\n        for (i = 1; i <= 9; i++) {\n          num = num + parseInt(tempuserid.charAt(i)) * (10 - i);\n        }\n        num += parseInt(tempuserid.charAt(10)) * 1;\n        if (num % 10 == 0) {\n          console.log(\"next step\");\n        } else {\n          this.addErrorMessage('身份證字號輸入錯誤');\n        }\n      } else {\n        this.addErrorMessage('身份證字號輸入錯誤');\n      }\n    }\n    Date(StrDt, EndDt) {\n      if (EndDt != null) {\n        if (EndDt < StrDt) {\n          this.addErrorMessage(\"起始時間不能大於結束時間！\");\n        }\n      }\n    }\n    addErrorMessage(message) {\n      this.errorMessages.push(message);\n    }\n    clear() {\n      this.errorMessages = [];\n    }\n    static {\n      this.ɵfac = function ValidationHelper_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || ValidationHelper)();\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: ValidationHelper,\n        factory: ValidationHelper.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return ValidationHelper;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}