{"ast": null, "code": "import { getDefaultOptions } from \"../_lib/defaultOptions/index.js\";\nimport differenceInCalendarDays from \"../differenceInCalendarDays/index.js\";\nimport format from \"../format/index.js\";\nimport defaultLocale from \"../_lib/defaultLocale/index.js\";\nimport subMilliseconds from \"../subMilliseconds/index.js\";\nimport toDate from \"../toDate/index.js\";\nimport getTimezoneOffsetInMilliseconds from \"../_lib/getTimezoneOffsetInMilliseconds/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\nimport toInteger from \"../_lib/toInteger/index.js\";\n/**\n * @name formatRelative\n * @category Common Helpers\n * @summary Represent the date in words relative to the given base date.\n *\n * @description\n * Represent the date in words relative to the given base date.\n *\n * | Distance to the base date | Result                    |\n * |---------------------------|---------------------------|\n * | Previous 6 days           | last Sunday at 04:30 AM   |\n * | Last day                  | yesterday at 04:30 AM     |\n * | Same day                  | today at 04:30 AM         |\n * | Next day                  | tomorrow at 04:30 AM      |\n * | Next 6 days               | Sunday at 04:30 AM        |\n * | Other                     | 12/31/2017                |\n *\n * @param {Date|Number} date - the date to format\n * @param {Date|Number} baseDate - the date to compare with\n * @param {Object} [options] - an object with options.\n * @param {Locale} [options.locale=defaultLocale] - the locale object. See [Locale]{@link https://date-fns.org/docs/Locale}\n * @param {0|1|2|3|4|5|6} [options.weekStartsOn=0] - the index of the first day of the week (0 - Sunday)\n * @returns {String} the date in words\n * @throws {TypeError} 2 arguments required\n * @throws {RangeError} `date` must not be Invalid Date\n * @throws {RangeError} `baseDate` must not be Invalid Date\n * @throws {RangeError} `options.weekStartsOn` must be between 0 and 6\n * @throws {RangeError} `options.locale` must contain `localize` property\n * @throws {RangeError} `options.locale` must contain `formatLong` property\n * @throws {RangeError} `options.locale` must contain `formatRelative` property\n *\n * @example\n * // Represent the date of 6 days ago in words relative to the given base date. In this example, today is Wednesday\n * const result = formatRelative(addDays(new Date(), -6), new Date())\n * //=> \"last Thursday at 12:45 AM\"\n */\nexport default function formatRelative(dirtyDate, dirtyBaseDate, options) {\n  var _ref, _options$locale, _ref2, _ref3, _ref4, _options$weekStartsOn, _options$locale2, _options$locale2$opti, _defaultOptions$local, _defaultOptions$local2;\n  requiredArgs(2, arguments);\n  var date = toDate(dirtyDate);\n  var baseDate = toDate(dirtyBaseDate);\n  var defaultOptions = getDefaultOptions();\n  var locale = (_ref = (_options$locale = options === null || options === void 0 ? void 0 : options.locale) !== null && _options$locale !== void 0 ? _options$locale : defaultOptions.locale) !== null && _ref !== void 0 ? _ref : defaultLocale;\n  var weekStartsOn = toInteger((_ref2 = (_ref3 = (_ref4 = (_options$weekStartsOn = options === null || options === void 0 ? void 0 : options.weekStartsOn) !== null && _options$weekStartsOn !== void 0 ? _options$weekStartsOn : options === null || options === void 0 ? void 0 : (_options$locale2 = options.locale) === null || _options$locale2 === void 0 ? void 0 : (_options$locale2$opti = _options$locale2.options) === null || _options$locale2$opti === void 0 ? void 0 : _options$locale2$opti.weekStartsOn) !== null && _ref4 !== void 0 ? _ref4 : defaultOptions.weekStartsOn) !== null && _ref3 !== void 0 ? _ref3 : (_defaultOptions$local = defaultOptions.locale) === null || _defaultOptions$local === void 0 ? void 0 : (_defaultOptions$local2 = _defaultOptions$local.options) === null || _defaultOptions$local2 === void 0 ? void 0 : _defaultOptions$local2.weekStartsOn) !== null && _ref2 !== void 0 ? _ref2 : 0);\n  if (!locale.localize) {\n    throw new RangeError('locale must contain localize property');\n  }\n  if (!locale.formatLong) {\n    throw new RangeError('locale must contain formatLong property');\n  }\n  if (!locale.formatRelative) {\n    throw new RangeError('locale must contain formatRelative property');\n  }\n  var diff = differenceInCalendarDays(date, baseDate);\n  if (isNaN(diff)) {\n    throw new RangeError('Invalid time value');\n  }\n  var token;\n  if (diff < -6) {\n    token = 'other';\n  } else if (diff < -1) {\n    token = 'lastWeek';\n  } else if (diff < 0) {\n    token = 'yesterday';\n  } else if (diff < 1) {\n    token = 'today';\n  } else if (diff < 2) {\n    token = 'tomorrow';\n  } else if (diff < 7) {\n    token = 'nextWeek';\n  } else {\n    token = 'other';\n  }\n  var utcDate = subMilliseconds(date, getTimezoneOffsetInMilliseconds(date));\n  var utcBaseDate = subMilliseconds(baseDate, getTimezoneOffsetInMilliseconds(baseDate));\n  var formatStr = locale.formatRelative(token, utcDate, utcBaseDate, {\n    locale: locale,\n    weekStartsOn: weekStartsOn\n  });\n  return format(date, formatStr, {\n    locale: locale,\n    weekStartsOn: weekStartsOn\n  });\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}