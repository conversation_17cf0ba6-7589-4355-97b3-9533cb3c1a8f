{"ast": null, "code": "import { of as observableOf } from 'rxjs';\nimport { TrafficBarData } from '../data/traffic-bar';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"./periods.service\";\nexport class TrafficBarService extends TrafficBarData {\n  constructor(period) {\n    super();\n    this.period = period;\n    this.data = {};\n    this.data = {\n      week: this.getDataForWeekPeriod(),\n      month: this.getDataForMonthPeriod(),\n      year: this.getDataForYearPeriod()\n    };\n  }\n  getDataForWeekPeriod() {\n    return {\n      data: [10, 15, 19, 7, 20, 13, 15],\n      labels: this.period.getWeeks(),\n      formatter: '{c0} MB'\n    };\n  }\n  getDataForMonthPeriod() {\n    return {\n      data: [0.5, 0.3, 0.8, 0.2, 0.3, 0.7, 0.8, 1, 0.7, 0.8, 0.6, 0.7],\n      labels: this.period.getMonths(),\n      formatter: '{c0} GB'\n    };\n  }\n  getDataForYearPeriod() {\n    return {\n      data: [10, 15, 19, 7, 20, 13, 15, 19, 11],\n      labels: this.period.getYears(),\n      formatter: '{c0} GB'\n    };\n  }\n  getTrafficBarData(period) {\n    return observableOf(this.data[period]);\n  }\n  static {\n    this.ɵfac = function TrafficBarService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || TrafficBarService)(i0.ɵɵinject(i1.PeriodsService));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: TrafficBarService,\n      factory: TrafficBarService.ɵfac\n    });\n  }\n}", "map": {"version": 3, "names": ["of", "observableOf", "TrafficBarData", "TrafficBarService", "constructor", "period", "data", "week", "getDataForWeekPeriod", "month", "getDataForMonthPeriod", "year", "getDataForYearPeriod", "labels", "getWeeks", "formatter", "getMonths", "getYears", "getTrafficBarData", "i0", "ɵɵinject", "i1", "PeriodsService", "factory", "ɵfac"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\@core\\mock\\traffic-bar.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport { of as observableOf,  Observable } from 'rxjs';\r\nimport { PeriodsService } from './periods.service';\r\nimport { TrafficBarData, TrafficBar } from '../data/traffic-bar';\r\n\r\n@Injectable()\r\nexport class TrafficBarService extends TrafficBarData {\r\n\r\n  private data:any = { };\r\n\r\n  constructor(private period: PeriodsService) {\r\n    super();\r\n    this.data = {\r\n      week: this.getDataForWeekPeriod(),\r\n      month: this.getDataForMonthPeriod(),\r\n      year: this.getDataForYearPeriod(),\r\n    };\r\n  }\r\n\r\n  getDataForWeekPeriod(): TrafficBar {\r\n    return {\r\n      data: [10, 15, 19, 7, 20, 13, 15],\r\n      labels: this.period.getWeeks(),\r\n      formatter: '{c0} MB',\r\n    };\r\n  }\r\n\r\n  getDataForMonthPeriod(): TrafficBar {\r\n    return {\r\n      data: [0.5, 0.3, 0.8, 0.2, 0.3, 0.7, 0.8, 1, 0.7, 0.8, 0.6, 0.7],\r\n      labels: this.period.getMonths(),\r\n      formatter: '{c0} GB',\r\n    };\r\n  }\r\n\r\n  getDataForYearPeriod(): TrafficBar {\r\n    return {\r\n      data: [10, 15, 19, 7, 20, 13, 15, 19, 11],\r\n      labels: this.period.getYears(),\r\n      formatter: '{c0} GB',\r\n    };\r\n  }\r\n\r\n  getTrafficBarData(period: string): Observable<TrafficBar> {\r\n    return observableOf(this.data[period]);\r\n  }\r\n}\r\n"], "mappings": "AACA,SAASA,EAAE,IAAIC,YAAY,QAAqB,MAAM;AAEtD,SAASC,cAAc,QAAoB,qBAAqB;;;AAGhE,OAAM,MAAOC,iBAAkB,SAAQD,cAAc;EAInDE,YAAoBC,MAAsB;IACxC,KAAK,EAAE;IADW,KAAAA,MAAM,GAANA,MAAM;IAFlB,KAAAC,IAAI,GAAO,EAAG;IAIpB,IAAI,CAACA,IAAI,GAAG;MACVC,IAAI,EAAE,IAAI,CAACC,oBAAoB,EAAE;MACjCC,KAAK,EAAE,IAAI,CAACC,qBAAqB,EAAE;MACnCC,IAAI,EAAE,IAAI,CAACC,oBAAoB;KAChC;EACH;EAEAJ,oBAAoBA,CAAA;IAClB,OAAO;MACLF,IAAI,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;MACjCO,MAAM,EAAE,IAAI,CAACR,MAAM,CAACS,QAAQ,EAAE;MAC9BC,SAAS,EAAE;KACZ;EACH;EAEAL,qBAAqBA,CAAA;IACnB,OAAO;MACLJ,IAAI,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;MAChEO,MAAM,EAAE,IAAI,CAACR,MAAM,CAACW,SAAS,EAAE;MAC/BD,SAAS,EAAE;KACZ;EACH;EAEAH,oBAAoBA,CAAA;IAClB,OAAO;MACLN,IAAI,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;MACzCO,MAAM,EAAE,IAAI,CAACR,MAAM,CAACY,QAAQ,EAAE;MAC9BF,SAAS,EAAE;KACZ;EACH;EAEAG,iBAAiBA,CAACb,MAAc;IAC9B,OAAOJ,YAAY,CAAC,IAAI,CAACK,IAAI,CAACD,MAAM,CAAC,CAAC;EACxC;;;uCAvCWF,iBAAiB,EAAAgB,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,cAAA;IAAA;EAAA;;;aAAjBnB,iBAAiB;MAAAoB,OAAA,EAAjBpB,iBAAiB,CAAAqB;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}