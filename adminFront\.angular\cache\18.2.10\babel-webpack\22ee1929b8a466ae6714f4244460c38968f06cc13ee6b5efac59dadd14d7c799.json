{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { Component } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { concatMap, of, tap } from 'rxjs';\nimport { BaseFilePipe } from 'src/app/@theme/pipes/base-file.pipe';\nimport { SharedModule } from '../../components/shared.module';\nimport { BaseComponent } from '../../components/base/baseComponent';\nlet PictureMaterialComponent = class PictureMaterialComponent extends BaseComponent {\n  constructor(_allow, dialogService, valid, _pictureService, _buildCaseService, message, _utilityService) {\n    super(_allow);\n    this._allow = _allow;\n    this.dialogService = dialogService;\n    this.valid = valid;\n    this._pictureService = _pictureService;\n    this._buildCaseService = _buildCaseService;\n    this.message = message;\n    this._utilityService = _utilityService;\n    this.images = [];\n    this.listUserBuildCases = [];\n    this.currentImageShowing = \"\";\n    this.listPictures = [];\n    this.isEdit = false;\n  }\n  ngOnInit() {\n    this.getListBuildCase();\n  }\n  getListBuildCase() {\n    this._buildCaseService.apiBuildCaseGetUserBuildCasePost$Json({}).pipe(tap(res => {\n      if (res.StatusCode === 0) {\n        this.listUserBuildCases = res.Entries ?? [];\n        this.selectedBuildCaseId = this.listUserBuildCases[0].cID;\n      }\n    }), concatMap(() => this.getPicturelList(1))).subscribe();\n  }\n  openPdfInNewTab(CFileUrl) {\n    if (CFileUrl) {\n      this._utilityService.openFileInNewTab(CFileUrl);\n    }\n  }\n  getPicturelList(pageIndex) {\n    return this._pictureService.apiPictureGetPicturelListPost$Json({\n      body: {\n        PageIndex: pageIndex,\n        PageSize: this.pageSize,\n        CBuildCaseId: this.selectedBuildCaseId\n      }\n    }).pipe(tap(res => {\n      if (res.StatusCode == 0) {\n        this.images = res.Entries ?? [];\n        this.totalRecords = res.TotalItems;\n      }\n    }));\n  }\n  pageChanged(pageIndex) {\n    // this.pageIndex = newPage;\n    this.getPicturelList(pageIndex).subscribe();\n  }\n  selectedChange(buildCaseId) {\n    this.selectedBuildCaseId = buildCaseId;\n    this.getPicturelList(1).subscribe();\n  }\n  addNew(ref, item) {\n    this.listPictures = [];\n    this.dialogService.open(ref);\n    this.isEdit = false;\n    if (!!item) {\n      this.currentImageShowing = item.CFile;\n    } else {\n      this.listPictures = [];\n    }\n  }\n  changePicture(ref, item) {\n    if (!!item && item.CId) {\n      this.dialogService.open(ref);\n      this.isEdit = true;\n      this.currentEditItem = item.CId;\n      this.listPictures = [];\n    }\n  }\n  validation(CFile) {\n    this.valid.clear();\n    const nameSet = new Set();\n    for (const item of CFile) {\n      if (nameSet.has(item.name)) {\n        this.valid.addErrorMessage('檔名不可重複');\n        return;\n      }\n      nameSet.add(item.name);\n    }\n  }\n  onSubmit(ref) {}\n  detectFiles(event) {\n    for (let index = 0; index < event.target.files.length; index++) {\n      const file = event.target.files[index];\n      if (file) {\n        let reader = new FileReader();\n        reader.readAsDataURL(file);\n        reader.onload = () => {\n          let base64Str = reader.result;\n          if (!base64Str) {\n            return;\n          }\n          // Get name file ( no extension)\n          const fileNameWithoutExtension = file.name.split('.')[0];\n          // Find files with duplicate names\n          const existingFileIndex = this.listPictures.findIndex(picture => picture.name === fileNameWithoutExtension);\n          if (existingFileIndex !== -1) {\n            // If name is duplicate, update file data\n            this.listPictures[existingFileIndex] = {\n              ...this.listPictures[existingFileIndex],\n              data: base64Str,\n              CFile: file,\n              extension: this._utilityService.getFileExtension(file.name)\n            };\n          } else {\n            // If not duplicate, add new file\n            file.id = new Date().getTime();\n            this.listPictures.push({\n              id: new Date().getTime(),\n              name: fileNameWithoutExtension,\n              data: base64Str,\n              extension: this._utilityService.getFileExtension(file.name),\n              CFile: file\n            });\n          }\n          // Reset input file to be able to select the old file again\n          event.target.value = null;\n        };\n      }\n    }\n  }\n  removeImage(pictureId) {\n    this.listPictures = this.listPictures.filter(x => x.id != pictureId);\n  }\n  uploadImage(ref) {\n    if (!this.isEdit) {\n      const CFile = this.listPictures.map(x => x.CFile);\n      this.validation(CFile);\n      if (this.valid.errorMessages.length > 0) {\n        this.message.showErrorMSGs(this.valid.errorMessages);\n        return;\n      }\n      this._pictureService.apiPictureUploadListPicturePost$Json({\n        body: {\n          CBuildCaseId: this.selectedBuildCaseId,\n          CPath: \"picture\",\n          CFile: CFile\n        }\n      }).pipe(tap(res => {\n        if (res.StatusCode == 0) {\n          this.message.showSucessMSG('執行成功');\n          this.listPictures = [];\n        } else {\n          this.message.showErrorMSG(res.Message);\n        }\n        ref.close();\n      }), concatMap(res => res.StatusCode == 0 ? this.getPicturelList(1) : of(null))).subscribe();\n    } else {\n      if (this.listPictures.length > 0 && this.listPictures[0].CFile) {\n        this._pictureService.apiPictureUpdatePicturePost$Json({\n          body: {\n            CBuildCaseID: this.selectedBuildCaseId,\n            CPictureID: this.currentEditItem,\n            CFile: this.listPictures[0].CFile\n          }\n        }).pipe(tap(res => {\n          if (res.StatusCode == 0) {\n            this.message.showSucessMSG('執行成功');\n            this.listPictures = [];\n          } else {\n            this.message.showErrorMSG(res.Message);\n          }\n          ref.close();\n        }), concatMap(res => res.StatusCode == 0 ? this.getPicturelList(1) : of(null))).subscribe();\n      }\n    }\n  }\n  renameFile(event, index) {\n    var blob = this.listPictures[index].CFile.slice(0, this.listPictures[index].CFile.size, this.listPictures[index].CFile.type);\n    var newFile = new File([blob], `${event.target.value + '.' + this.listPictures[index].extension}`, {\n      type: this.listPictures[index].CFile.type\n    });\n    this.listPictures[index].CFile = newFile;\n  }\n};\nPictureMaterialComponent = __decorate([Component({\n  selector: 'ngx-picture-material',\n  templateUrl: './picture-material.component.html',\n  styleUrls: ['./picture-material.component.scss'],\n  standalone: true,\n  imports: [CommonModule, SharedModule, BaseFilePipe]\n})], PictureMaterialComponent);\nexport { PictureMaterialComponent };", "map": {"version": 3, "names": ["Component", "CommonModule", "concatMap", "of", "tap", "BaseFilePipe", "SharedModule", "BaseComponent", "PictureMaterialComponent", "constructor", "_allow", "dialogService", "valid", "_pictureService", "_buildCaseService", "message", "_utilityService", "images", "listUserBuildCases", "currentImageShowing", "listPictures", "isEdit", "ngOnInit", "getListBuildCase", "apiBuildCaseGetUserBuildCasePost$Json", "pipe", "res", "StatusCode", "Entries", "selectedBuildCaseId", "cID", "getPicturelList", "subscribe", "openPdfInNewTab", "CFileUrl", "openFileInNewTab", "pageIndex", "apiPictureGetPicturelListPost$Json", "body", "PageIndex", "PageSize", "pageSize", "CBuildCaseId", "totalRecords", "TotalItems", "pageChanged", "<PERSON><PERSON><PERSON><PERSON>", "buildCaseId", "addNew", "ref", "item", "open", "CFile", "changePicture", "CId", "currentEditItem", "validation", "clear", "nameSet", "Set", "has", "name", "addErrorMessage", "add", "onSubmit", "detectFiles", "event", "index", "target", "files", "length", "file", "reader", "FileReader", "readAsDataURL", "onload", "base64Str", "result", "fileNameWithoutExtension", "split", "existingFileIndex", "findIndex", "picture", "data", "extension", "getFileExtension", "id", "Date", "getTime", "push", "value", "removeImage", "pictureId", "filter", "x", "uploadImage", "map", "errorMessages", "showErrorMSGs", "apiPictureUploadListPicturePost$Json", "CPath", "showSucessMSG", "showErrorMSG", "Message", "close", "apiPictureUpdatePicturePost$Json", "CBuildCaseID", "CPictureID", "renameFile", "blob", "slice", "size", "type", "newFile", "File", "__decorate", "selector", "templateUrl", "styleUrls", "standalone", "imports"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\selection-management\\picture-material\\picture-material.component.ts"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { CommonModule, formatDate } from '@angular/common';\r\nimport { NbDialogService } from '@nebular/theme';\r\nimport { ValidationHelper } from 'src/app/shared/helper/validationHelper';\r\nimport { BuildCaseService, PictureService } from 'src/services/api/services';\r\nimport { BuildCaseGetListReponse, GetPictureListResponse } from 'src/services/api/models';\r\nimport { AllowHelper } from 'src/app/shared/helper/allowHelper';\r\nimport { concatMap, finalize, of, tap } from 'rxjs';\r\nimport { BaseFilePipe } from 'src/app/@theme/pipes/base-file.pipe';\r\nimport { MessageService } from 'src/app/shared/services/message.service';\r\nimport { UtilityService } from 'src/app/shared/services/utility.service';\r\nimport { SharedModule } from '../../components/shared.module';\r\nimport { BaseComponent } from '../../components/base/baseComponent';\r\n\r\n@Component({\r\n  selector: 'ngx-picture-material',\r\n  templateUrl: './picture-material.component.html',\r\n  styleUrls: ['./picture-material.component.scss'],\r\n  standalone: true,\r\n  imports: [\r\n    CommonModule,\r\n    SharedModule,\r\n    BaseFilePipe\r\n  ],\r\n})\r\n\r\nexport class PictureMaterialComponent extends BaseComponent implements OnInit {\r\n\r\n  images: GetPictureListResponse[] = [];\r\n  listUserBuildCases: BuildCaseGetListReponse[] = []\r\n\r\n  selectedBuildCaseId: number\r\n\r\n  currentImageShowing: string = \"\"\r\n\r\n  listPictures: any[] = []\r\n\r\n  isEdit: boolean = false;\r\n  currentEditItem: number;\r\n\r\n  constructor(\r\n    private _allow: AllowHelper,\r\n    private dialogService: NbDialogService,\r\n    private valid: ValidationHelper,\r\n    private _pictureService: PictureService,\r\n    private _buildCaseService: BuildCaseService,\r\n    private message: MessageService,\r\n    private _utilityService: UtilityService\r\n  ) {\r\n    super(_allow)\r\n  }\r\n\r\n  override ngOnInit(): void {\r\n    this.getListBuildCase();\r\n  }\r\n\r\n  getListBuildCase() {\r\n    this._buildCaseService.apiBuildCaseGetUserBuildCasePost$Json({})\r\n      .pipe(\r\n        tap(res => {\r\n          if (res.StatusCode === 0) {\r\n            this.listUserBuildCases = res.Entries! ?? []\r\n            this.selectedBuildCaseId = this.listUserBuildCases[0].cID!\r\n          }\r\n        }),\r\n        concatMap(() => this.getPicturelList(1))\r\n      ).subscribe()\r\n  }\r\n\r\n  openPdfInNewTab(CFileUrl?: any) {\r\n    if (CFileUrl) {\r\n      this._utilityService.openFileInNewTab(CFileUrl)\r\n    }\r\n  }\r\n\r\n  getPicturelList(pageIndex: number) {\r\n    return this._pictureService.apiPictureGetPicturelListPost$Json({\r\n      body: {\r\n        PageIndex: pageIndex,\r\n        PageSize: this.pageSize,\r\n        CBuildCaseId: this.selectedBuildCaseId\r\n      }\r\n    }).pipe(\r\n      tap(res => {\r\n        if (res.StatusCode == 0) {\r\n          this.images = res.Entries! ?? []\r\n          this.totalRecords = res.TotalItems!;\r\n        }\r\n      })\r\n    )\r\n  }\r\n\r\n  pageChanged(pageIndex: number) {\r\n    // this.pageIndex = newPage;\r\n    this.getPicturelList(pageIndex).subscribe();\r\n  }\r\n\r\n  selectedChange(buildCaseId: number) {\r\n    this.selectedBuildCaseId = buildCaseId;\r\n    this.getPicturelList(1).subscribe();\r\n  }\r\n\r\n  addNew(ref: any, item?: GetPictureListResponse) {\r\n    this.listPictures = []\r\n    this.dialogService.open(ref)\r\n    this.isEdit = false;\r\n    if (!!item) {\r\n      this.currentImageShowing = item.CFile!\r\n    }\r\n    else{\r\n      this.listPictures = [];\r\n    }\r\n  }\r\n\r\n  changePicture(ref: any, item?: GetPictureListResponse) {\r\n    if (!!item && item.CId) {\r\n      this.dialogService.open(ref)\r\n      this.isEdit = true;\r\n      this.currentEditItem = item.CId;\r\n      this.listPictures = [];\r\n    }\r\n  }\r\n\r\n  validation(CFile: any) {\r\n    this.valid.clear();\r\n    const nameSet = new Set();\r\n    for (const item of CFile) {\r\n      if (nameSet.has(item.name)) {\r\n        this.valid.addErrorMessage('檔名不可重複')\r\n        return;\r\n      }\r\n      nameSet.add(item.name);\r\n    }\r\n  }\r\n\r\n  onSubmit(ref: any) {\r\n  }\r\n\r\n  detectFiles(event: any) {\r\n    for (let index = 0; index < event.target.files.length; index++) {\r\n      const file = event.target.files[index];\r\n      if (file) {\r\n        let reader = new FileReader();\r\n        reader.readAsDataURL(file);\r\n        reader.onload = () => {\r\n          let base64Str: string = reader.result as string;\r\n          if (!base64Str) {\r\n            return;\r\n          }\r\n          // Get name file ( no extension)\r\n          const fileNameWithoutExtension = file.name.split('.')[0];\r\n          // Find files with duplicate names\r\n          const existingFileIndex = this.listPictures.findIndex(picture => picture.name === fileNameWithoutExtension);\r\n          if (existingFileIndex !== -1) {\r\n            // If name is duplicate, update file data\r\n            this.listPictures[existingFileIndex] = {\r\n              ...this.listPictures[existingFileIndex],\r\n              data: base64Str,\r\n              CFile: file,\r\n              extension: this._utilityService.getFileExtension(file.name)\r\n            };\r\n          } else {\r\n            // If not duplicate, add new file\r\n            file.id = new Date().getTime();\r\n            this.listPictures.push({\r\n              id: new Date().getTime(),\r\n              name: fileNameWithoutExtension,\r\n              data: base64Str,\r\n              extension: this._utilityService.getFileExtension(file.name),\r\n              CFile: file\r\n            });\r\n          }\r\n          // Reset input file to be able to select the old file again\r\n          event.target.value = null;\r\n        };\r\n      }\r\n    }\r\n  }\r\n\r\n  removeImage(pictureId: number) {\r\n    this.listPictures = this.listPictures.filter(x => x.id != pictureId)\r\n  }\r\n\r\n  uploadImage(ref: any) {\r\n    if(!this.isEdit){\r\n      const CFile = this.listPictures.map(x => x.CFile)\r\n      this.validation(CFile)\r\n      if (this.valid.errorMessages.length > 0) {\r\n        this.message.showErrorMSGs(this.valid.errorMessages);\r\n        return\r\n      }\r\n      this._pictureService.apiPictureUploadListPicturePost$Json({\r\n        body: {\r\n          CBuildCaseId: this.selectedBuildCaseId,\r\n          CPath: \"picture\",\r\n          CFile: CFile\r\n        }\r\n      }).pipe(\r\n        tap(res => {\r\n          if (res.StatusCode == 0) {\r\n            this.message.showSucessMSG('執行成功')\r\n            this.listPictures = []\r\n          } else {\r\n            this.message.showErrorMSG(res.Message!)\r\n          }\r\n          ref.close();\r\n        }),\r\n        concatMap((res) => res.StatusCode! == 0 ? this.getPicturelList(1) : of(null)),\r\n      ).subscribe()\r\n    }\r\n    else{\r\n      if(this.listPictures.length > 0 && this.listPictures[0].CFile){\r\n        this._pictureService.apiPictureUpdatePicturePost$Json({\r\n          body: {\r\n            CBuildCaseID: this.selectedBuildCaseId,\r\n            CPictureID: this.currentEditItem,\r\n            CFile: this.listPictures[0].CFile\r\n          }\r\n        }).pipe(\r\n          tap(res => {\r\n            if (res.StatusCode == 0) {\r\n              this.message.showSucessMSG('執行成功')\r\n              this.listPictures = []\r\n            } else {\r\n              this.message.showErrorMSG(res.Message!)\r\n            }\r\n            ref.close();\r\n          }),\r\n          concatMap((res) => res.StatusCode! == 0 ? this.getPicturelList(1) : of(null)),\r\n        ).subscribe()\r\n      }\r\n    }\r\n  }\r\n\r\n  renameFile(event: any, index: number) {\r\n    var blob = this.listPictures[index].CFile.slice(0, this.listPictures[index].CFile.size, this.listPictures[index].CFile.type);\r\n    var newFile = new File([blob], `${event.target.value + '.' + this.listPictures[index].extension}`, { type: this.listPictures[index].CFile.type });\r\n\r\n    this.listPictures[index].CFile = newFile\r\n  }\r\n\r\n}\r\n"], "mappings": ";AAAA,SAASA,SAAS,QAAgB,eAAe;AACjD,SAASC,YAAY,QAAoB,iBAAiB;AAM1D,SAASC,SAAS,EAAYC,EAAE,EAAEC,GAAG,QAAQ,MAAM;AACnD,SAASC,YAAY,QAAQ,qCAAqC;AAGlE,SAASC,YAAY,QAAQ,gCAAgC;AAC7D,SAASC,aAAa,QAAQ,qCAAqC;AAc5D,IAAMC,wBAAwB,GAA9B,MAAMA,wBAAyB,SAAQD,aAAa;EAczDE,YACUC,MAAmB,EACnBC,aAA8B,EAC9BC,KAAuB,EACvBC,eAA+B,EAC/BC,iBAAmC,EACnCC,OAAuB,EACvBC,eAA+B;IAEvC,KAAK,CAACN,MAAM,CAAC;IARL,KAAAA,MAAM,GAANA,MAAM;IACN,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAC,eAAe,GAAfA,eAAe;IAnBzB,KAAAC,MAAM,GAA6B,EAAE;IACrC,KAAAC,kBAAkB,GAA8B,EAAE;IAIlD,KAAAC,mBAAmB,GAAW,EAAE;IAEhC,KAAAC,YAAY,GAAU,EAAE;IAExB,KAAAC,MAAM,GAAY,KAAK;EAavB;EAESC,QAAQA,CAAA;IACf,IAAI,CAACC,gBAAgB,EAAE;EACzB;EAEAA,gBAAgBA,CAAA;IACd,IAAI,CAACT,iBAAiB,CAACU,qCAAqC,CAAC,EAAE,CAAC,CAC7DC,IAAI,CACHrB,GAAG,CAACsB,GAAG,IAAG;MACR,IAAIA,GAAG,CAACC,UAAU,KAAK,CAAC,EAAE;QACxB,IAAI,CAACT,kBAAkB,GAAGQ,GAAG,CAACE,OAAQ,IAAI,EAAE;QAC5C,IAAI,CAACC,mBAAmB,GAAG,IAAI,CAACX,kBAAkB,CAAC,CAAC,CAAC,CAACY,GAAI;MAC5D;IACF,CAAC,CAAC,EACF5B,SAAS,CAAC,MAAM,IAAI,CAAC6B,eAAe,CAAC,CAAC,CAAC,CAAC,CACzC,CAACC,SAAS,EAAE;EACjB;EAEAC,eAAeA,CAACC,QAAc;IAC5B,IAAIA,QAAQ,EAAE;MACZ,IAAI,CAAClB,eAAe,CAACmB,gBAAgB,CAACD,QAAQ,CAAC;IACjD;EACF;EAEAH,eAAeA,CAACK,SAAiB;IAC/B,OAAO,IAAI,CAACvB,eAAe,CAACwB,kCAAkC,CAAC;MAC7DC,IAAI,EAAE;QACJC,SAAS,EAAEH,SAAS;QACpBI,QAAQ,EAAE,IAAI,CAACC,QAAQ;QACvBC,YAAY,EAAE,IAAI,CAACb;;KAEtB,CAAC,CAACJ,IAAI,CACLrB,GAAG,CAACsB,GAAG,IAAG;MACR,IAAIA,GAAG,CAACC,UAAU,IAAI,CAAC,EAAE;QACvB,IAAI,CAACV,MAAM,GAAGS,GAAG,CAACE,OAAQ,IAAI,EAAE;QAChC,IAAI,CAACe,YAAY,GAAGjB,GAAG,CAACkB,UAAW;MACrC;IACF,CAAC,CAAC,CACH;EACH;EAEAC,WAAWA,CAACT,SAAiB;IAC3B;IACA,IAAI,CAACL,eAAe,CAACK,SAAS,CAAC,CAACJ,SAAS,EAAE;EAC7C;EAEAc,cAAcA,CAACC,WAAmB;IAChC,IAAI,CAAClB,mBAAmB,GAAGkB,WAAW;IACtC,IAAI,CAAChB,eAAe,CAAC,CAAC,CAAC,CAACC,SAAS,EAAE;EACrC;EAEAgB,MAAMA,CAACC,GAAQ,EAAEC,IAA6B;IAC5C,IAAI,CAAC9B,YAAY,GAAG,EAAE;IACtB,IAAI,CAACT,aAAa,CAACwC,IAAI,CAACF,GAAG,CAAC;IAC5B,IAAI,CAAC5B,MAAM,GAAG,KAAK;IACnB,IAAI,CAAC,CAAC6B,IAAI,EAAE;MACV,IAAI,CAAC/B,mBAAmB,GAAG+B,IAAI,CAACE,KAAM;IACxC,CAAC,MACG;MACF,IAAI,CAAChC,YAAY,GAAG,EAAE;IACxB;EACF;EAEAiC,aAAaA,CAACJ,GAAQ,EAAEC,IAA6B;IACnD,IAAI,CAAC,CAACA,IAAI,IAAIA,IAAI,CAACI,GAAG,EAAE;MACtB,IAAI,CAAC3C,aAAa,CAACwC,IAAI,CAACF,GAAG,CAAC;MAC5B,IAAI,CAAC5B,MAAM,GAAG,IAAI;MAClB,IAAI,CAACkC,eAAe,GAAGL,IAAI,CAACI,GAAG;MAC/B,IAAI,CAAClC,YAAY,GAAG,EAAE;IACxB;EACF;EAEAoC,UAAUA,CAACJ,KAAU;IACnB,IAAI,CAACxC,KAAK,CAAC6C,KAAK,EAAE;IAClB,MAAMC,OAAO,GAAG,IAAIC,GAAG,EAAE;IACzB,KAAK,MAAMT,IAAI,IAAIE,KAAK,EAAE;MACxB,IAAIM,OAAO,CAACE,GAAG,CAACV,IAAI,CAACW,IAAI,CAAC,EAAE;QAC1B,IAAI,CAACjD,KAAK,CAACkD,eAAe,CAAC,QAAQ,CAAC;QACpC;MACF;MACAJ,OAAO,CAACK,GAAG,CAACb,IAAI,CAACW,IAAI,CAAC;IACxB;EACF;EAEAG,QAAQA,CAACf,GAAQ,GACjB;EAEAgB,WAAWA,CAACC,KAAU;IACpB,KAAK,IAAIC,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGD,KAAK,CAACE,MAAM,CAACC,KAAK,CAACC,MAAM,EAAEH,KAAK,EAAE,EAAE;MAC9D,MAAMI,IAAI,GAAGL,KAAK,CAACE,MAAM,CAACC,KAAK,CAACF,KAAK,CAAC;MACtC,IAAII,IAAI,EAAE;QACR,IAAIC,MAAM,GAAG,IAAIC,UAAU,EAAE;QAC7BD,MAAM,CAACE,aAAa,CAACH,IAAI,CAAC;QAC1BC,MAAM,CAACG,MAAM,GAAG,MAAK;UACnB,IAAIC,SAAS,GAAWJ,MAAM,CAACK,MAAgB;UAC/C,IAAI,CAACD,SAAS,EAAE;YACd;UACF;UACA;UACA,MAAME,wBAAwB,GAAGP,IAAI,CAACV,IAAI,CAACkB,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;UACxD;UACA,MAAMC,iBAAiB,GAAG,IAAI,CAAC5D,YAAY,CAAC6D,SAAS,CAACC,OAAO,IAAIA,OAAO,CAACrB,IAAI,KAAKiB,wBAAwB,CAAC;UAC3G,IAAIE,iBAAiB,KAAK,CAAC,CAAC,EAAE;YAC5B;YACA,IAAI,CAAC5D,YAAY,CAAC4D,iBAAiB,CAAC,GAAG;cACrC,GAAG,IAAI,CAAC5D,YAAY,CAAC4D,iBAAiB,CAAC;cACvCG,IAAI,EAAEP,SAAS;cACfxB,KAAK,EAAEmB,IAAI;cACXa,SAAS,EAAE,IAAI,CAACpE,eAAe,CAACqE,gBAAgB,CAACd,IAAI,CAACV,IAAI;aAC3D;UACH,CAAC,MAAM;YACL;YACAU,IAAI,CAACe,EAAE,GAAG,IAAIC,IAAI,EAAE,CAACC,OAAO,EAAE;YAC9B,IAAI,CAACpE,YAAY,CAACqE,IAAI,CAAC;cACrBH,EAAE,EAAE,IAAIC,IAAI,EAAE,CAACC,OAAO,EAAE;cACxB3B,IAAI,EAAEiB,wBAAwB;cAC9BK,IAAI,EAAEP,SAAS;cACfQ,SAAS,EAAE,IAAI,CAACpE,eAAe,CAACqE,gBAAgB,CAACd,IAAI,CAACV,IAAI,CAAC;cAC3DT,KAAK,EAAEmB;aACR,CAAC;UACJ;UACA;UACAL,KAAK,CAACE,MAAM,CAACsB,KAAK,GAAG,IAAI;QAC3B,CAAC;MACH;IACF;EACF;EAEAC,WAAWA,CAACC,SAAiB;IAC3B,IAAI,CAACxE,YAAY,GAAG,IAAI,CAACA,YAAY,CAACyE,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACR,EAAE,IAAIM,SAAS,CAAC;EACtE;EAEAG,WAAWA,CAAC9C,GAAQ;IAClB,IAAG,CAAC,IAAI,CAAC5B,MAAM,EAAC;MACd,MAAM+B,KAAK,GAAG,IAAI,CAAChC,YAAY,CAAC4E,GAAG,CAACF,CAAC,IAAIA,CAAC,CAAC1C,KAAK,CAAC;MACjD,IAAI,CAACI,UAAU,CAACJ,KAAK,CAAC;MACtB,IAAI,IAAI,CAACxC,KAAK,CAACqF,aAAa,CAAC3B,MAAM,GAAG,CAAC,EAAE;QACvC,IAAI,CAACvD,OAAO,CAACmF,aAAa,CAAC,IAAI,CAACtF,KAAK,CAACqF,aAAa,CAAC;QACpD;MACF;MACA,IAAI,CAACpF,eAAe,CAACsF,oCAAoC,CAAC;QACxD7D,IAAI,EAAE;UACJI,YAAY,EAAE,IAAI,CAACb,mBAAmB;UACtCuE,KAAK,EAAE,SAAS;UAChBhD,KAAK,EAAEA;;OAEV,CAAC,CAAC3B,IAAI,CACLrB,GAAG,CAACsB,GAAG,IAAG;QACR,IAAIA,GAAG,CAACC,UAAU,IAAI,CAAC,EAAE;UACvB,IAAI,CAACZ,OAAO,CAACsF,aAAa,CAAC,MAAM,CAAC;UAClC,IAAI,CAACjF,YAAY,GAAG,EAAE;QACxB,CAAC,MAAM;UACL,IAAI,CAACL,OAAO,CAACuF,YAAY,CAAC5E,GAAG,CAAC6E,OAAQ,CAAC;QACzC;QACAtD,GAAG,CAACuD,KAAK,EAAE;MACb,CAAC,CAAC,EACFtG,SAAS,CAAEwB,GAAG,IAAKA,GAAG,CAACC,UAAW,IAAI,CAAC,GAAG,IAAI,CAACI,eAAe,CAAC,CAAC,CAAC,GAAG5B,EAAE,CAAC,IAAI,CAAC,CAAC,CAC9E,CAAC6B,SAAS,EAAE;IACf,CAAC,MACG;MACF,IAAG,IAAI,CAACZ,YAAY,CAACkD,MAAM,GAAG,CAAC,IAAI,IAAI,CAAClD,YAAY,CAAC,CAAC,CAAC,CAACgC,KAAK,EAAC;QAC5D,IAAI,CAACvC,eAAe,CAAC4F,gCAAgC,CAAC;UACpDnE,IAAI,EAAE;YACJoE,YAAY,EAAE,IAAI,CAAC7E,mBAAmB;YACtC8E,UAAU,EAAE,IAAI,CAACpD,eAAe;YAChCH,KAAK,EAAE,IAAI,CAAChC,YAAY,CAAC,CAAC,CAAC,CAACgC;;SAE/B,CAAC,CAAC3B,IAAI,CACLrB,GAAG,CAACsB,GAAG,IAAG;UACR,IAAIA,GAAG,CAACC,UAAU,IAAI,CAAC,EAAE;YACvB,IAAI,CAACZ,OAAO,CAACsF,aAAa,CAAC,MAAM,CAAC;YAClC,IAAI,CAACjF,YAAY,GAAG,EAAE;UACxB,CAAC,MAAM;YACL,IAAI,CAACL,OAAO,CAACuF,YAAY,CAAC5E,GAAG,CAAC6E,OAAQ,CAAC;UACzC;UACAtD,GAAG,CAACuD,KAAK,EAAE;QACb,CAAC,CAAC,EACFtG,SAAS,CAAEwB,GAAG,IAAKA,GAAG,CAACC,UAAW,IAAI,CAAC,GAAG,IAAI,CAACI,eAAe,CAAC,CAAC,CAAC,GAAG5B,EAAE,CAAC,IAAI,CAAC,CAAC,CAC9E,CAAC6B,SAAS,EAAE;MACf;IACF;EACF;EAEA4E,UAAUA,CAAC1C,KAAU,EAAEC,KAAa;IAClC,IAAI0C,IAAI,GAAG,IAAI,CAACzF,YAAY,CAAC+C,KAAK,CAAC,CAACf,KAAK,CAAC0D,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC1F,YAAY,CAAC+C,KAAK,CAAC,CAACf,KAAK,CAAC2D,IAAI,EAAE,IAAI,CAAC3F,YAAY,CAAC+C,KAAK,CAAC,CAACf,KAAK,CAAC4D,IAAI,CAAC;IAC5H,IAAIC,OAAO,GAAG,IAAIC,IAAI,CAAC,CAACL,IAAI,CAAC,EAAE,GAAG3C,KAAK,CAACE,MAAM,CAACsB,KAAK,GAAG,GAAG,GAAG,IAAI,CAACtE,YAAY,CAAC+C,KAAK,CAAC,CAACiB,SAAS,EAAE,EAAE;MAAE4B,IAAI,EAAE,IAAI,CAAC5F,YAAY,CAAC+C,KAAK,CAAC,CAACf,KAAK,CAAC4D;IAAI,CAAE,CAAC;IAEjJ,IAAI,CAAC5F,YAAY,CAAC+C,KAAK,CAAC,CAACf,KAAK,GAAG6D,OAAO;EAC1C;CAED;AAvNYzG,wBAAwB,GAAA2G,UAAA,EAZpCnH,SAAS,CAAC;EACToH,QAAQ,EAAE,sBAAsB;EAChCC,WAAW,EAAE,mCAAmC;EAChDC,SAAS,EAAE,CAAC,mCAAmC,CAAC;EAChDC,UAAU,EAAE,IAAI;EAChBC,OAAO,EAAE,CACPvH,YAAY,EACZK,YAAY,EACZD,YAAY;CAEf,CAAC,C,EAEWG,wBAAwB,CAuNpC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}