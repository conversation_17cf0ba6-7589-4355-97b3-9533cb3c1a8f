{"ast": null, "code": "import { CryptoService } from \"./crypto.service\";\nimport * as i0 from \"@angular/core\";\nexport class LocalStorageService {\n  static AddLocalStorage(key, value) {\n    localStorage.setItem(key, this._Add(value));\n  }\n  static RemoveLocalStorage(key) {\n    localStorage.removeItem(key);\n  }\n  static GetLocalStorage(key) {\n    let resultStr = this._Get(localStorage.getItem(key) || \"\");\n    return resultStr;\n  }\n  static ClearLocalStorage() {\n    localStorage.clear();\n  }\n  static AddSessionStorage(key, value) {\n    sessionStorage.setItem(key, this._Add(value));\n  }\n  static RemoveSessionStorage(key) {\n    sessionStorage.removeItem(key);\n  }\n  static GetSessionStorage(key) {\n    let resultStr = this._Get(sessionStorage.getItem(key) || \"\");\n    return resultStr;\n  }\n  static _Add(value) {\n    let result = CryptoService.EncryptUsingAES256(value);\n    return result;\n  }\n  static _Get(value) {\n    let result = CryptoService.DecryptUsingAES256(value);\n    result.split(\"\\\\\").join(\"\");\n    result.slice(0, 1);\n    result.slice(result.length - 1, 1);\n    return result == \"\" ? \"\" : JSON.parse(result);\n  }\n  static {\n    this.ɵfac = function LocalStorageService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || LocalStorageService)();\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: LocalStorageService,\n      factory: LocalStorageService.ɵfac,\n      providedIn: \"root\"\n    });\n  }\n}", "map": {"version": 3, "names": ["CryptoService", "LocalStorageService", "AddLocalStorage", "key", "value", "localStorage", "setItem", "_Add", "RemoveLocalStorage", "removeItem", "GetLocalStorage", "resultStr", "_Get", "getItem", "ClearLocalStorage", "clear", "AddSessionStorage", "sessionStorage", "RemoveSessionStorage", "GetSessionStorage", "result", "EncryptUsingAES256", "DecryptUsingAES256", "split", "join", "slice", "length", "JSON", "parse", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\Documents\\jeansalechange\\adminFront\\src\\app\\shared\\services\\local-storage.service.ts"], "sourcesContent": ["import { Injectable } from \"@angular/core\";\r\nimport { CryptoService } from \"./crypto.service\";\r\n\r\n@Injectable({\r\n    providedIn: \"root\",\r\n})\r\nexport abstract class LocalStorageService {\r\n    public static AddLocalStorage(key: string, value: any): void {\r\n        localStorage.setItem(key, this._Add(value));\r\n    }\r\n\r\n    public static RemoveLocalStorage(key: string): void {\r\n        localStorage.removeItem(key);\r\n    }\r\n\r\n    public static GetLocalStorage(key: string): any {\r\n        let resultStr = this._Get(localStorage.getItem(key) || \"\");\r\n        return resultStr;\r\n    }\r\n\r\n    public static ClearLocalStorage(): void {\r\n        localStorage.clear();\r\n    }\r\n\r\n    public static AddSessionStorage(key: string, value: string): void {\r\n        sessionStorage.setItem(key, this._Add(value));\r\n    }\r\n\r\n    public static RemoveSessionStorage(key: string): void {\r\n        sessionStorage.removeItem(key);\r\n    }\r\n\r\n    public static GetSessionStorage(key: string): string {\r\n        let resultStr = this._Get(sessionStorage.getItem(key) || \"\");\r\n        return resultStr;\r\n    }\r\n\r\n    private static _Add(value: string): string {\r\n        let result: string = CryptoService.EncryptUsingAES256(value);\r\n        return result;\r\n    }\r\n\r\n    private static _Get(value: string): string {\r\n        let result: string = CryptoService.DecryptUsingAES256(value);\r\n        result.split(\"\\\\\").join(\"\");\r\n        result.slice(0, 1);\r\n        result.slice(result.length - 1, 1);\r\n        return result == \"\" ? \"\" : JSON.parse(result);\r\n    }\r\n}\r\n"], "mappings": "AACA,SAASA,aAAa,QAAQ,kBAAkB;;AAKhD,OAAM,MAAgBC,mBAAmB;EAC9B,OAAOC,eAAeA,CAACC,GAAW,EAAEC,KAAU;IACjDC,YAAY,CAACC,OAAO,CAACH,GAAG,EAAE,IAAI,CAACI,IAAI,CAACH,KAAK,CAAC,CAAC;EAC/C;EAEO,OAAOI,kBAAkBA,CAACL,GAAW;IACxCE,YAAY,CAACI,UAAU,CAACN,GAAG,CAAC;EAChC;EAEO,OAAOO,eAAeA,CAACP,GAAW;IACrC,IAAIQ,SAAS,GAAG,IAAI,CAACC,IAAI,CAACP,YAAY,CAACQ,OAAO,CAACV,GAAG,CAAC,IAAI,EAAE,CAAC;IAC1D,OAAOQ,SAAS;EACpB;EAEO,OAAOG,iBAAiBA,CAAA;IAC3BT,YAAY,CAACU,KAAK,EAAE;EACxB;EAEO,OAAOC,iBAAiBA,CAACb,GAAW,EAAEC,KAAa;IACtDa,cAAc,CAACX,OAAO,CAACH,GAAG,EAAE,IAAI,CAACI,IAAI,CAACH,KAAK,CAAC,CAAC;EACjD;EAEO,OAAOc,oBAAoBA,CAACf,GAAW;IAC1Cc,cAAc,CAACR,UAAU,CAACN,GAAG,CAAC;EAClC;EAEO,OAAOgB,iBAAiBA,CAAChB,GAAW;IACvC,IAAIQ,SAAS,GAAG,IAAI,CAACC,IAAI,CAACK,cAAc,CAACJ,OAAO,CAACV,GAAG,CAAC,IAAI,EAAE,CAAC;IAC5D,OAAOQ,SAAS;EACpB;EAEQ,OAAOJ,IAAIA,CAACH,KAAa;IAC7B,IAAIgB,MAAM,GAAWpB,aAAa,CAACqB,kBAAkB,CAACjB,KAAK,CAAC;IAC5D,OAAOgB,MAAM;EACjB;EAEQ,OAAOR,IAAIA,CAACR,KAAa;IAC7B,IAAIgB,MAAM,GAAWpB,aAAa,CAACsB,kBAAkB,CAAClB,KAAK,CAAC;IAC5DgB,MAAM,CAACG,KAAK,CAAC,IAAI,CAAC,CAACC,IAAI,CAAC,EAAE,CAAC;IAC3BJ,MAAM,CAACK,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;IAClBL,MAAM,CAACK,KAAK,CAACL,MAAM,CAACM,MAAM,GAAG,CAAC,EAAE,CAAC,CAAC;IAClC,OAAON,MAAM,IAAI,EAAE,GAAG,EAAE,GAAGO,IAAI,CAACC,KAAK,CAACR,MAAM,CAAC;EACjD;;;uCA1CkBnB,mBAAmB;IAAA;EAAA;;;aAAnBA,mBAAmB;MAAA4B,OAAA,EAAnB5B,mBAAmB,CAAA6B,IAAA;MAAAC,UAAA,EAFzB;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}