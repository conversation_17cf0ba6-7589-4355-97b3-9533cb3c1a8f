{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { InjectionToken, Injectable, Optional, Inject, Directive, Input, NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { map, takeUntil } from 'rxjs/operators';\nimport { Subject } from 'rxjs';\nconst NB_SECURITY_OPTIONS_TOKEN = new InjectionToken('Nebular Security Options');\n\n/**\n * @license\n * Copyright Akveo. All Rights Reserved.\n * Licensed under the MIT License. See License.txt in the project root for license information.\n */\nconst shallowObjectClone = o => Object.assign({}, o);\nconst shallowArrayClone = a => Object.assign([], a);\nconst popParent = abilities => {\n  const parent = abilities.parent;\n  delete abilities.parent;\n  return parent;\n};\n/**\n * Common acl service.\n */\nclass NbAclService {\n  static {\n    this.ANY_RESOURCE = '*';\n  }\n  constructor(settings = {}) {\n    this.settings = settings;\n    this.state = {};\n    if (settings.accessControl) {\n      this.setAccessControl(settings.accessControl);\n    }\n  }\n  /**\n   * Set/Reset ACL list\n   * @param {NbAccessControl} list\n   */\n  setAccessControl(list) {\n    for (const [role, value] of Object.entries(list)) {\n      const abilities = shallowObjectClone(value);\n      const parent = popParent(abilities);\n      this.register(role, parent, abilities);\n    }\n  }\n  /**\n   * Register a new role with a list of abilities (permission/resources combinations)\n   * @param {string} role\n   * @param {string} parent\n   * @param {[permission: string]: string|string[]} abilities\n   */\n  register(role, parent = null, abilities = {}) {\n    this.validateRole(role);\n    this.state[role] = {\n      parent: parent\n    };\n    for (const [permission, value] of Object.entries(abilities)) {\n      const resources = typeof value === 'string' ? [value] : value;\n      this.allow(role, permission, shallowArrayClone(resources));\n    }\n  }\n  /**\n   * Allow a permission for specific resources to a role\n   * @param {string} role\n   * @param {string} permission\n   * @param {string | string[]} resource\n   */\n  allow(role, permission, resource) {\n    this.validateRole(role);\n    if (!this.getRole(role)) {\n      this.register(role, null, {});\n    }\n    resource = typeof resource === 'string' ? [resource] : resource;\n    let resources = shallowArrayClone(this.getRoleResources(role, permission));\n    resources = resources.concat(resource);\n    this.state[role][permission] = resources.filter((item, pos) => resources.indexOf(item) === pos);\n  }\n  /**\n   * Check whether the role has a permission to a resource\n   * @param {string} role\n   * @param {string} permission\n   * @param {string} resource\n   * @returns {boolean}\n   */\n  can(role, permission, resource) {\n    this.validateResource(resource);\n    const parentRole = this.getRoleParent(role);\n    const parentCan = parentRole && this.can(this.getRoleParent(role), permission, resource);\n    return parentCan || this.exactCan(role, permission, resource);\n  }\n  getRole(role) {\n    return this.state[role];\n  }\n  validateRole(role) {\n    if (!role) {\n      throw new Error('NbAclService: role name cannot be empty');\n    }\n  }\n  validateResource(resource) {\n    if (!resource || [NbAclService.ANY_RESOURCE].includes(resource)) {\n      throw new Error(`NbAclService: cannot use empty or bulk '*' resource placeholder with 'can' method`);\n    }\n  }\n  exactCan(role, permission, resource) {\n    const resources = this.getRoleResources(role, permission);\n    return resources.includes(resource) || resources.includes(NbAclService.ANY_RESOURCE);\n  }\n  getRoleResources(role, permission) {\n    return this.getRoleAbilities(role)[permission] || [];\n  }\n  getRoleAbilities(role) {\n    const abilities = shallowObjectClone(this.state[role] || {});\n    popParent(shallowObjectClone(this.state[role] || {}));\n    return abilities;\n  }\n  getRoleParent(role) {\n    return this.state[role] ? this.state[role].parent : null;\n  }\n  static {\n    this.ɵfac = function NbAclService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || NbAclService)(i0.ɵɵinject(NB_SECURITY_OPTIONS_TOKEN, 8));\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: NbAclService,\n      factory: NbAclService.ɵfac\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NbAclService, [{\n    type: Injectable\n  }], () => [{\n    type: undefined,\n    decorators: [{\n      type: Optional\n    }, {\n      type: Inject,\n      args: [NB_SECURITY_OPTIONS_TOKEN]\n    }]\n  }], null);\n})();\nclass NbRoleProvider {}\n\n/**\n * @license\n * Copyright Akveo. All Rights Reserved.\n * Licensed under the MIT License. See License.txt in the project root for license information.\n */\n/**\n * Access checker service.\n *\n * Injects `NbRoleProvider` to determine current user role, and checks access permissions using `NbAclService`\n */\nclass NbAccessChecker {\n  constructor(roleProvider, acl) {\n    this.roleProvider = roleProvider;\n    this.acl = acl;\n  }\n  /**\n   * Checks whether access is granted or not\n   *\n   * @param {string} permission\n   * @param {string} resource\n   * @returns {Observable<boolean>}\n   */\n  isGranted(permission, resource) {\n    return this.roleProvider.getRole().pipe(map(role => Array.isArray(role) ? role : [role]), map(roles => {\n      return roles.some(role => this.acl.can(role, permission, resource));\n    }));\n  }\n  static {\n    this.ɵfac = function NbAccessChecker_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || NbAccessChecker)(i0.ɵɵinject(NbRoleProvider), i0.ɵɵinject(NbAclService));\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: NbAccessChecker,\n      factory: NbAccessChecker.ɵfac\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NbAccessChecker, [{\n    type: Injectable\n  }], () => [{\n    type: NbRoleProvider\n  }, {\n    type: NbAclService\n  }], null);\n})();\nclass NbIsGrantedDirective {\n  constructor(templateRef, viewContainer, accessChecker) {\n    this.templateRef = templateRef;\n    this.viewContainer = viewContainer;\n    this.accessChecker = accessChecker;\n    this.destroy$ = new Subject();\n    this.hasView = false;\n  }\n  set nbIsGranted([permission, resource]) {\n    this.accessChecker.isGranted(permission, resource).pipe(takeUntil(this.destroy$)).subscribe(can => {\n      if (can && !this.hasView) {\n        this.viewContainer.createEmbeddedView(this.templateRef);\n        this.hasView = true;\n      } else if (!can && this.hasView) {\n        this.viewContainer.clear();\n        this.hasView = false;\n      }\n    });\n  }\n  ngOnDestroy() {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n  static {\n    this.ɵfac = function NbIsGrantedDirective_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || NbIsGrantedDirective)(i0.ɵɵdirectiveInject(i0.TemplateRef), i0.ɵɵdirectiveInject(i0.ViewContainerRef), i0.ɵɵdirectiveInject(NbAccessChecker));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: NbIsGrantedDirective,\n      selectors: [[\"\", \"nbIsGranted\", \"\"]],\n      inputs: {\n        nbIsGranted: \"nbIsGranted\"\n      }\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NbIsGrantedDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[nbIsGranted]'\n    }]\n  }], () => [{\n    type: i0.TemplateRef\n  }, {\n    type: i0.ViewContainerRef\n  }, {\n    type: NbAccessChecker\n  }], {\n    nbIsGranted: [{\n      type: Input\n    }]\n  });\n})();\nclass NbSecurityModule {\n  static forRoot(nbSecurityOptions) {\n    return {\n      ngModule: NbSecurityModule,\n      providers: [{\n        provide: NB_SECURITY_OPTIONS_TOKEN,\n        useValue: nbSecurityOptions\n      }, NbAclService, NbAccessChecker]\n    };\n  }\n  static {\n    this.ɵfac = function NbSecurityModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || NbSecurityModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: NbSecurityModule\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      imports: [CommonModule]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NbSecurityModule, [{\n    type: NgModule,\n    args: [{\n      imports: [CommonModule],\n      declarations: [NbIsGrantedDirective],\n      exports: [NbIsGrantedDirective]\n    }]\n  }], null, null);\n})();\n\n/**\n * @license\n * Copyright Akveo. All Rights Reserved.\n * Licensed under the MIT License. See License.txt in the project root for license information.\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { NB_SECURITY_OPTIONS_TOKEN, NbAccessChecker, NbAclService, NbIsGrantedDirective, NbRoleProvider, NbSecurityModule };", "map": {"version": 3, "names": ["i0", "InjectionToken", "Injectable", "Optional", "Inject", "Directive", "Input", "NgModule", "CommonModule", "map", "takeUntil", "Subject", "NB_SECURITY_OPTIONS_TOKEN", "shallowObjectClone", "o", "Object", "assign", "shallowArrayClone", "a", "popParent", "abilities", "parent", "NbAclService", "ANY_RESOURCE", "constructor", "settings", "state", "accessControl", "setAccessControl", "list", "role", "value", "entries", "register", "validateRole", "permission", "resources", "allow", "resource", "getRole", "getRoleResources", "concat", "filter", "item", "pos", "indexOf", "can", "validateResource", "parentRole", "getRoleParent", "parentCan", "exactCan", "Error", "includes", "getRoleAbilities", "ɵfac", "NbAclService_Factory", "__ngFactoryType__", "ɵɵinject", "ɵprov", "ɵɵdefineInjectable", "token", "factory", "ngDevMode", "ɵsetClassMetadata", "type", "undefined", "decorators", "args", "NbRoleProvider", "NbAccessChecker", "<PERSON><PERSON><PERSON><PERSON>", "acl", "isGranted", "pipe", "Array", "isArray", "roles", "some", "NbAccessChecker_Factory", "NbIsGrantedDirective", "templateRef", "viewContainer", "accessChecker", "destroy$", "<PERSON><PERSON><PERSON><PERSON>", "nbIsGranted", "subscribe", "createEmbeddedView", "clear", "ngOnDestroy", "next", "complete", "NbIsGrantedDirective_Factory", "ɵɵdirectiveInject", "TemplateRef", "ViewContainerRef", "ɵdir", "ɵɵdefineDirective", "selectors", "inputs", "selector", "NbSecurityModule", "forRoot", "nbSecurityOptions", "ngModule", "providers", "provide", "useValue", "NbSecurityModule_Factory", "ɵmod", "ɵɵdefineNgModule", "ɵinj", "ɵɵdefineInjector", "imports", "declarations", "exports"], "sources": ["C:/Users/<USER>/Documents/salechange-product/adminFront/node_modules/@nebular/security/fesm2022/nebular-security.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { InjectionToken, Injectable, Optional, Inject, Directive, Input, NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { map, takeUntil } from 'rxjs/operators';\nimport { Subject } from 'rxjs';\n\nconst NB_SECURITY_OPTIONS_TOKEN = new InjectionToken('Nebular Security Options');\n\n/**\n * @license\n * Copyright Akveo. All Rights Reserved.\n * Licensed under the MIT License. See License.txt in the project root for license information.\n */\nconst shallowObjectClone = (o) => Object.assign({}, o);\nconst shallowArrayClone = (a) => Object.assign([], a);\nconst popParent = (abilities) => {\n    const parent = abilities.parent;\n    delete abilities.parent;\n    return parent;\n};\n/**\n * Common acl service.\n */\nclass NbAclService {\n    static { this.ANY_RESOURCE = '*'; }\n    constructor(settings = {}) {\n        this.settings = settings;\n        this.state = {};\n        if (settings.accessControl) {\n            this.setAccessControl(settings.accessControl);\n        }\n    }\n    /**\n     * Set/Reset ACL list\n     * @param {NbAccessControl} list\n     */\n    setAccessControl(list) {\n        for (const [role, value] of Object.entries(list)) {\n            const abilities = shallowObjectClone(value);\n            const parent = popParent(abilities);\n            this.register(role, parent, abilities);\n        }\n    }\n    /**\n     * Register a new role with a list of abilities (permission/resources combinations)\n     * @param {string} role\n     * @param {string} parent\n     * @param {[permission: string]: string|string[]} abilities\n     */\n    register(role, parent = null, abilities = {}) {\n        this.validateRole(role);\n        this.state[role] = {\n            parent: parent,\n        };\n        for (const [permission, value] of Object.entries(abilities)) {\n            const resources = typeof value === 'string' ? [value] : value;\n            this.allow(role, permission, shallowArrayClone(resources));\n        }\n    }\n    /**\n     * Allow a permission for specific resources to a role\n     * @param {string} role\n     * @param {string} permission\n     * @param {string | string[]} resource\n     */\n    allow(role, permission, resource) {\n        this.validateRole(role);\n        if (!this.getRole(role)) {\n            this.register(role, null, {});\n        }\n        resource = typeof resource === 'string' ? [resource] : resource;\n        let resources = shallowArrayClone(this.getRoleResources(role, permission));\n        resources = resources.concat(resource);\n        this.state[role][permission] = resources.filter((item, pos) => resources.indexOf(item) === pos);\n    }\n    /**\n     * Check whether the role has a permission to a resource\n     * @param {string} role\n     * @param {string} permission\n     * @param {string} resource\n     * @returns {boolean}\n     */\n    can(role, permission, resource) {\n        this.validateResource(resource);\n        const parentRole = this.getRoleParent(role);\n        const parentCan = parentRole && this.can(this.getRoleParent(role), permission, resource);\n        return parentCan || this.exactCan(role, permission, resource);\n    }\n    getRole(role) {\n        return this.state[role];\n    }\n    validateRole(role) {\n        if (!role) {\n            throw new Error('NbAclService: role name cannot be empty');\n        }\n    }\n    validateResource(resource) {\n        if (!resource || [NbAclService.ANY_RESOURCE].includes(resource)) {\n            throw new Error(`NbAclService: cannot use empty or bulk '*' resource placeholder with 'can' method`);\n        }\n    }\n    exactCan(role, permission, resource) {\n        const resources = this.getRoleResources(role, permission);\n        return resources.includes(resource) || resources.includes(NbAclService.ANY_RESOURCE);\n    }\n    getRoleResources(role, permission) {\n        return this.getRoleAbilities(role)[permission] || [];\n    }\n    getRoleAbilities(role) {\n        const abilities = shallowObjectClone(this.state[role] || {});\n        popParent(shallowObjectClone(this.state[role] || {}));\n        return abilities;\n    }\n    getRoleParent(role) {\n        return this.state[role] ? this.state[role].parent : null;\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"18.1.3\", ngImport: i0, type: NbAclService, deps: [{ token: NB_SECURITY_OPTIONS_TOKEN, optional: true }], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"18.1.3\", ngImport: i0, type: NbAclService }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"18.1.3\", ngImport: i0, type: NbAclService, decorators: [{\n            type: Injectable\n        }], ctorParameters: () => [{ type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [NB_SECURITY_OPTIONS_TOKEN]\n                }] }] });\n\nclass NbRoleProvider {\n}\n\n/**\n * @license\n * Copyright Akveo. All Rights Reserved.\n * Licensed under the MIT License. See License.txt in the project root for license information.\n */\n/**\n * Access checker service.\n *\n * Injects `NbRoleProvider` to determine current user role, and checks access permissions using `NbAclService`\n */\nclass NbAccessChecker {\n    constructor(roleProvider, acl) {\n        this.roleProvider = roleProvider;\n        this.acl = acl;\n    }\n    /**\n     * Checks whether access is granted or not\n     *\n     * @param {string} permission\n     * @param {string} resource\n     * @returns {Observable<boolean>}\n     */\n    isGranted(permission, resource) {\n        return this.roleProvider.getRole()\n            .pipe(map((role) => Array.isArray(role) ? role : [role]), map((roles) => {\n            return roles.some(role => this.acl.can(role, permission, resource));\n        }));\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"18.1.3\", ngImport: i0, type: NbAccessChecker, deps: [{ token: NbRoleProvider }, { token: NbAclService }], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"18.1.3\", ngImport: i0, type: NbAccessChecker }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"18.1.3\", ngImport: i0, type: NbAccessChecker, decorators: [{\n            type: Injectable\n        }], ctorParameters: () => [{ type: NbRoleProvider }, { type: NbAclService }] });\n\nclass NbIsGrantedDirective {\n    constructor(templateRef, viewContainer, accessChecker) {\n        this.templateRef = templateRef;\n        this.viewContainer = viewContainer;\n        this.accessChecker = accessChecker;\n        this.destroy$ = new Subject();\n        this.hasView = false;\n    }\n    set nbIsGranted([permission, resource]) {\n        this.accessChecker.isGranted(permission, resource)\n            .pipe(takeUntil(this.destroy$))\n            .subscribe((can) => {\n            if (can && !this.hasView) {\n                this.viewContainer.createEmbeddedView(this.templateRef);\n                this.hasView = true;\n            }\n            else if (!can && this.hasView) {\n                this.viewContainer.clear();\n                this.hasView = false;\n            }\n        });\n    }\n    ngOnDestroy() {\n        this.destroy$.next();\n        this.destroy$.complete();\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"18.1.3\", ngImport: i0, type: NbIsGrantedDirective, deps: [{ token: i0.TemplateRef }, { token: i0.ViewContainerRef }, { token: NbAccessChecker }], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"18.1.3\", type: NbIsGrantedDirective, selector: \"[nbIsGranted]\", inputs: { nbIsGranted: \"nbIsGranted\" }, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"18.1.3\", ngImport: i0, type: NbIsGrantedDirective, decorators: [{\n            type: Directive,\n            args: [{ selector: '[nbIsGranted]' }]\n        }], ctorParameters: () => [{ type: i0.TemplateRef }, { type: i0.ViewContainerRef }, { type: NbAccessChecker }], propDecorators: { nbIsGranted: [{\n                type: Input\n            }] } });\n\nclass NbSecurityModule {\n    static forRoot(nbSecurityOptions) {\n        return {\n            ngModule: NbSecurityModule,\n            providers: [\n                { provide: NB_SECURITY_OPTIONS_TOKEN, useValue: nbSecurityOptions },\n                NbAclService,\n                NbAccessChecker,\n            ],\n        };\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"18.1.3\", ngImport: i0, type: NbSecurityModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule }); }\n    static { this.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"18.1.3\", ngImport: i0, type: NbSecurityModule, declarations: [NbIsGrantedDirective], imports: [CommonModule], exports: [NbIsGrantedDirective] }); }\n    static { this.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"18.1.3\", ngImport: i0, type: NbSecurityModule, imports: [CommonModule] }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"18.1.3\", ngImport: i0, type: NbSecurityModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [\n                        CommonModule,\n                    ],\n                    declarations: [\n                        NbIsGrantedDirective,\n                    ],\n                    exports: [\n                        NbIsGrantedDirective,\n                    ],\n                }]\n        }] });\n\n/**\n * @license\n * Copyright Akveo. All Rights Reserved.\n * Licensed under the MIT License. See License.txt in the project root for license information.\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { NB_SECURITY_OPTIONS_TOKEN, NbAccessChecker, NbAclService, NbIsGrantedDirective, NbRoleProvider, NbSecurityModule };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,cAAc,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,SAAS,EAAEC,KAAK,EAAEC,QAAQ,QAAQ,eAAe;AACxG,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,GAAG,EAAEC,SAAS,QAAQ,gBAAgB;AAC/C,SAASC,OAAO,QAAQ,MAAM;AAE9B,MAAMC,yBAAyB,GAAG,IAAIX,cAAc,CAAC,0BAA0B,CAAC;;AAEhF;AACA;AACA;AACA;AACA;AACA,MAAMY,kBAAkB,GAAIC,CAAC,IAAKC,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEF,CAAC,CAAC;AACtD,MAAMG,iBAAiB,GAAIC,CAAC,IAAKH,MAAM,CAACC,MAAM,CAAC,EAAE,EAAEE,CAAC,CAAC;AACrD,MAAMC,SAAS,GAAIC,SAAS,IAAK;EAC7B,MAAMC,MAAM,GAAGD,SAAS,CAACC,MAAM;EAC/B,OAAOD,SAAS,CAACC,MAAM;EACvB,OAAOA,MAAM;AACjB,CAAC;AACD;AACA;AACA;AACA,MAAMC,YAAY,CAAC;EACf;IAAS,IAAI,CAACC,YAAY,GAAG,GAAG;EAAE;EAClCC,WAAWA,CAACC,QAAQ,GAAG,CAAC,CAAC,EAAE;IACvB,IAAI,CAACA,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,KAAK,GAAG,CAAC,CAAC;IACf,IAAID,QAAQ,CAACE,aAAa,EAAE;MACxB,IAAI,CAACC,gBAAgB,CAACH,QAAQ,CAACE,aAAa,CAAC;IACjD;EACJ;EACA;AACJ;AACA;AACA;EACIC,gBAAgBA,CAACC,IAAI,EAAE;IACnB,KAAK,MAAM,CAACC,IAAI,EAAEC,KAAK,CAAC,IAAIhB,MAAM,CAACiB,OAAO,CAACH,IAAI,CAAC,EAAE;MAC9C,MAAMT,SAAS,GAAGP,kBAAkB,CAACkB,KAAK,CAAC;MAC3C,MAAMV,MAAM,GAAGF,SAAS,CAACC,SAAS,CAAC;MACnC,IAAI,CAACa,QAAQ,CAACH,IAAI,EAAET,MAAM,EAAED,SAAS,CAAC;IAC1C;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;EACIa,QAAQA,CAACH,IAAI,EAAET,MAAM,GAAG,IAAI,EAAED,SAAS,GAAG,CAAC,CAAC,EAAE;IAC1C,IAAI,CAACc,YAAY,CAACJ,IAAI,CAAC;IACvB,IAAI,CAACJ,KAAK,CAACI,IAAI,CAAC,GAAG;MACfT,MAAM,EAAEA;IACZ,CAAC;IACD,KAAK,MAAM,CAACc,UAAU,EAAEJ,KAAK,CAAC,IAAIhB,MAAM,CAACiB,OAAO,CAACZ,SAAS,CAAC,EAAE;MACzD,MAAMgB,SAAS,GAAG,OAAOL,KAAK,KAAK,QAAQ,GAAG,CAACA,KAAK,CAAC,GAAGA,KAAK;MAC7D,IAAI,CAACM,KAAK,CAACP,IAAI,EAAEK,UAAU,EAAElB,iBAAiB,CAACmB,SAAS,CAAC,CAAC;IAC9D;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;EACIC,KAAKA,CAACP,IAAI,EAAEK,UAAU,EAAEG,QAAQ,EAAE;IAC9B,IAAI,CAACJ,YAAY,CAACJ,IAAI,CAAC;IACvB,IAAI,CAAC,IAAI,CAACS,OAAO,CAACT,IAAI,CAAC,EAAE;MACrB,IAAI,CAACG,QAAQ,CAACH,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;IACjC;IACAQ,QAAQ,GAAG,OAAOA,QAAQ,KAAK,QAAQ,GAAG,CAACA,QAAQ,CAAC,GAAGA,QAAQ;IAC/D,IAAIF,SAAS,GAAGnB,iBAAiB,CAAC,IAAI,CAACuB,gBAAgB,CAACV,IAAI,EAAEK,UAAU,CAAC,CAAC;IAC1EC,SAAS,GAAGA,SAAS,CAACK,MAAM,CAACH,QAAQ,CAAC;IACtC,IAAI,CAACZ,KAAK,CAACI,IAAI,CAAC,CAACK,UAAU,CAAC,GAAGC,SAAS,CAACM,MAAM,CAAC,CAACC,IAAI,EAAEC,GAAG,KAAKR,SAAS,CAACS,OAAO,CAACF,IAAI,CAAC,KAAKC,GAAG,CAAC;EACnG;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACIE,GAAGA,CAAChB,IAAI,EAAEK,UAAU,EAAEG,QAAQ,EAAE;IAC5B,IAAI,CAACS,gBAAgB,CAACT,QAAQ,CAAC;IAC/B,MAAMU,UAAU,GAAG,IAAI,CAACC,aAAa,CAACnB,IAAI,CAAC;IAC3C,MAAMoB,SAAS,GAAGF,UAAU,IAAI,IAAI,CAACF,GAAG,CAAC,IAAI,CAACG,aAAa,CAACnB,IAAI,CAAC,EAAEK,UAAU,EAAEG,QAAQ,CAAC;IACxF,OAAOY,SAAS,IAAI,IAAI,CAACC,QAAQ,CAACrB,IAAI,EAAEK,UAAU,EAAEG,QAAQ,CAAC;EACjE;EACAC,OAAOA,CAACT,IAAI,EAAE;IACV,OAAO,IAAI,CAACJ,KAAK,CAACI,IAAI,CAAC;EAC3B;EACAI,YAAYA,CAACJ,IAAI,EAAE;IACf,IAAI,CAACA,IAAI,EAAE;MACP,MAAM,IAAIsB,KAAK,CAAC,yCAAyC,CAAC;IAC9D;EACJ;EACAL,gBAAgBA,CAACT,QAAQ,EAAE;IACvB,IAAI,CAACA,QAAQ,IAAI,CAAChB,YAAY,CAACC,YAAY,CAAC,CAAC8B,QAAQ,CAACf,QAAQ,CAAC,EAAE;MAC7D,MAAM,IAAIc,KAAK,CAAC,mFAAmF,CAAC;IACxG;EACJ;EACAD,QAAQA,CAACrB,IAAI,EAAEK,UAAU,EAAEG,QAAQ,EAAE;IACjC,MAAMF,SAAS,GAAG,IAAI,CAACI,gBAAgB,CAACV,IAAI,EAAEK,UAAU,CAAC;IACzD,OAAOC,SAAS,CAACiB,QAAQ,CAACf,QAAQ,CAAC,IAAIF,SAAS,CAACiB,QAAQ,CAAC/B,YAAY,CAACC,YAAY,CAAC;EACxF;EACAiB,gBAAgBA,CAACV,IAAI,EAAEK,UAAU,EAAE;IAC/B,OAAO,IAAI,CAACmB,gBAAgB,CAACxB,IAAI,CAAC,CAACK,UAAU,CAAC,IAAI,EAAE;EACxD;EACAmB,gBAAgBA,CAACxB,IAAI,EAAE;IACnB,MAAMV,SAAS,GAAGP,kBAAkB,CAAC,IAAI,CAACa,KAAK,CAACI,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;IAC5DX,SAAS,CAACN,kBAAkB,CAAC,IAAI,CAACa,KAAK,CAACI,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IACrD,OAAOV,SAAS;EACpB;EACA6B,aAAaA,CAACnB,IAAI,EAAE;IAChB,OAAO,IAAI,CAACJ,KAAK,CAACI,IAAI,CAAC,GAAG,IAAI,CAACJ,KAAK,CAACI,IAAI,CAAC,CAACT,MAAM,GAAG,IAAI;EAC5D;EACA;IAAS,IAAI,CAACkC,IAAI,YAAAC,qBAAAC,iBAAA;MAAA,YAAAA,iBAAA,IAAwFnC,YAAY,EAAtBtB,EAAE,CAAA0D,QAAA,CAAsC9C,yBAAyB;IAAA,CAA6D;EAAE;EAChO;IAAS,IAAI,CAAC+C,KAAK,kBAD6E3D,EAAE,CAAA4D,kBAAA;MAAAC,KAAA,EACYvC,YAAY;MAAAwC,OAAA,EAAZxC,YAAY,CAAAiC;IAAA,EAAG;EAAE;AACnI;AACA;EAAA,QAAAQ,SAAA,oBAAAA,SAAA,KAHoG/D,EAAE,CAAAgE,iBAAA,CAGX1C,YAAY,EAAc,CAAC;IAC1G2C,IAAI,EAAE/D;EACV,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAE+D,IAAI,EAAEC,SAAS;IAAEC,UAAU,EAAE,CAAC;MAC/CF,IAAI,EAAE9D;IACV,CAAC,EAAE;MACC8D,IAAI,EAAE7D,MAAM;MACZgE,IAAI,EAAE,CAACxD,yBAAyB;IACpC,CAAC;EAAE,CAAC,CAAC;AAAA;AAErB,MAAMyD,cAAc,CAAC;;AAGrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,eAAe,CAAC;EAClB9C,WAAWA,CAAC+C,YAAY,EAAEC,GAAG,EAAE;IAC3B,IAAI,CAACD,YAAY,GAAGA,YAAY;IAChC,IAAI,CAACC,GAAG,GAAGA,GAAG;EAClB;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACIC,SAASA,CAACtC,UAAU,EAAEG,QAAQ,EAAE;IAC5B,OAAO,IAAI,CAACiC,YAAY,CAAChC,OAAO,CAAC,CAAC,CAC7BmC,IAAI,CAACjE,GAAG,CAAEqB,IAAI,IAAK6C,KAAK,CAACC,OAAO,CAAC9C,IAAI,CAAC,GAAGA,IAAI,GAAG,CAACA,IAAI,CAAC,CAAC,EAAErB,GAAG,CAAEoE,KAAK,IAAK;MACzE,OAAOA,KAAK,CAACC,IAAI,CAAChD,IAAI,IAAI,IAAI,CAAC0C,GAAG,CAAC1B,GAAG,CAAChB,IAAI,EAAEK,UAAU,EAAEG,QAAQ,CAAC,CAAC;IACvE,CAAC,CAAC,CAAC;EACP;EACA;IAAS,IAAI,CAACiB,IAAI,YAAAwB,wBAAAtB,iBAAA;MAAA,YAAAA,iBAAA,IAAwFa,eAAe,EA3CzBtE,EAAE,CAAA0D,QAAA,CA2CyCW,cAAc,GA3CzDrE,EAAE,CAAA0D,QAAA,CA2CoEpC,YAAY;IAAA,CAA6C;EAAE;EACjO;IAAS,IAAI,CAACqC,KAAK,kBA5C6E3D,EAAE,CAAA4D,kBAAA;MAAAC,KAAA,EA4CYS,eAAe;MAAAR,OAAA,EAAfQ,eAAe,CAAAf;IAAA,EAAG;EAAE;AACtI;AACA;EAAA,QAAAQ,SAAA,oBAAAA,SAAA,KA9CoG/D,EAAE,CAAAgE,iBAAA,CA8CXM,eAAe,EAAc,CAAC;IAC7GL,IAAI,EAAE/D;EACV,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAE+D,IAAI,EAAEI;EAAe,CAAC,EAAE;IAAEJ,IAAI,EAAE3C;EAAa,CAAC,CAAC;AAAA;AAEpF,MAAM0D,oBAAoB,CAAC;EACvBxD,WAAWA,CAACyD,WAAW,EAAEC,aAAa,EAAEC,aAAa,EAAE;IACnD,IAAI,CAACF,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACC,aAAa,GAAGA,aAAa;IAClC,IAAI,CAACC,aAAa,GAAGA,aAAa;IAClC,IAAI,CAACC,QAAQ,GAAG,IAAIzE,OAAO,CAAC,CAAC;IAC7B,IAAI,CAAC0E,OAAO,GAAG,KAAK;EACxB;EACA,IAAIC,WAAWA,CAAC,CAACnD,UAAU,EAAEG,QAAQ,CAAC,EAAE;IACpC,IAAI,CAAC6C,aAAa,CAACV,SAAS,CAACtC,UAAU,EAAEG,QAAQ,CAAC,CAC7CoC,IAAI,CAAChE,SAAS,CAAC,IAAI,CAAC0E,QAAQ,CAAC,CAAC,CAC9BG,SAAS,CAAEzC,GAAG,IAAK;MACpB,IAAIA,GAAG,IAAI,CAAC,IAAI,CAACuC,OAAO,EAAE;QACtB,IAAI,CAACH,aAAa,CAACM,kBAAkB,CAAC,IAAI,CAACP,WAAW,CAAC;QACvD,IAAI,CAACI,OAAO,GAAG,IAAI;MACvB,CAAC,MACI,IAAI,CAACvC,GAAG,IAAI,IAAI,CAACuC,OAAO,EAAE;QAC3B,IAAI,CAACH,aAAa,CAACO,KAAK,CAAC,CAAC;QAC1B,IAAI,CAACJ,OAAO,GAAG,KAAK;MACxB;IACJ,CAAC,CAAC;EACN;EACAK,WAAWA,CAAA,EAAG;IACV,IAAI,CAACN,QAAQ,CAACO,IAAI,CAAC,CAAC;IACpB,IAAI,CAACP,QAAQ,CAACQ,QAAQ,CAAC,CAAC;EAC5B;EACA;IAAS,IAAI,CAACrC,IAAI,YAAAsC,6BAAApC,iBAAA;MAAA,YAAAA,iBAAA,IAAwFuB,oBAAoB,EA5E9BhF,EAAE,CAAA8F,iBAAA,CA4E8C9F,EAAE,CAAC+F,WAAW,GA5E9D/F,EAAE,CAAA8F,iBAAA,CA4EyE9F,EAAE,CAACgG,gBAAgB,GA5E9FhG,EAAE,CAAA8F,iBAAA,CA4EyGxB,eAAe;IAAA,CAA4C;EAAE;EACxQ;IAAS,IAAI,CAAC2B,IAAI,kBA7E8EjG,EAAE,CAAAkG,iBAAA;MAAAjC,IAAA,EA6EJe,oBAAoB;MAAAmB,SAAA;MAAAC,MAAA;QAAAd,WAAA;MAAA;IAAA,EAAoF;EAAE;AAC5M;AACA;EAAA,QAAAvB,SAAA,oBAAAA,SAAA,KA/EoG/D,EAAE,CAAAgE,iBAAA,CA+EXgB,oBAAoB,EAAc,CAAC;IAClHf,IAAI,EAAE5D,SAAS;IACf+D,IAAI,EAAE,CAAC;MAAEiC,QAAQ,EAAE;IAAgB,CAAC;EACxC,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEpC,IAAI,EAAEjE,EAAE,CAAC+F;EAAY,CAAC,EAAE;IAAE9B,IAAI,EAAEjE,EAAE,CAACgG;EAAiB,CAAC,EAAE;IAAE/B,IAAI,EAAEK;EAAgB,CAAC,CAAC,EAAkB;IAAEgB,WAAW,EAAE,CAAC;MACxIrB,IAAI,EAAE3D;IACV,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAMgG,gBAAgB,CAAC;EACnB,OAAOC,OAAOA,CAACC,iBAAiB,EAAE;IAC9B,OAAO;MACHC,QAAQ,EAAEH,gBAAgB;MAC1BI,SAAS,EAAE,CACP;QAAEC,OAAO,EAAE/F,yBAAyB;QAAEgG,QAAQ,EAAEJ;MAAkB,CAAC,EACnElF,YAAY,EACZgD,eAAe;IAEvB,CAAC;EACL;EACA;IAAS,IAAI,CAACf,IAAI,YAAAsD,yBAAApD,iBAAA;MAAA,YAAAA,iBAAA,IAAwF6C,gBAAgB;IAAA,CAAkD;EAAE;EAC9K;IAAS,IAAI,CAACQ,IAAI,kBAlG8E9G,EAAE,CAAA+G,gBAAA;MAAA9C,IAAA,EAkGSqC;IAAgB,EAAmG;EAAE;EAChO;IAAS,IAAI,CAACU,IAAI,kBAnG8EhH,EAAE,CAAAiH,gBAAA;MAAAC,OAAA,GAmGqC1G,YAAY;IAAA,EAAI;EAAE;AAC7J;AACA;EAAA,QAAAuD,SAAA,oBAAAA,SAAA,KArGoG/D,EAAE,CAAAgE,iBAAA,CAqGXsC,gBAAgB,EAAc,CAAC;IAC9GrC,IAAI,EAAE1D,QAAQ;IACd6D,IAAI,EAAE,CAAC;MACC8C,OAAO,EAAE,CACL1G,YAAY,CACf;MACD2G,YAAY,EAAE,CACVnC,oBAAoB,CACvB;MACDoC,OAAO,EAAE,CACLpC,oBAAoB;IAE5B,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA,SAASpE,yBAAyB,EAAE0D,eAAe,EAAEhD,YAAY,EAAE0D,oBAAoB,EAAEX,cAAc,EAAEiC,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}