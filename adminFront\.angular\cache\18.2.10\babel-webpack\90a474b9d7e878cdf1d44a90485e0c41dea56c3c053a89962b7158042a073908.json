{"ast": null, "code": "//! moment.js locale configuration\n//! locale : Turkmen [tk]\n//! author : <PERSON><PERSON><PERSON><PERSON> : https://github.com/atamyratabdy\n\n;\n(function (global, factory) {\n  typeof exports === 'object' && typeof module !== 'undefined' && typeof require === 'function' ? factory(require('../moment')) : typeof define === 'function' && define.amd ? define(['../moment'], factory) : factory(global.moment);\n})(this, function (moment) {\n  'use strict';\n\n  //! moment.js locale configuration\n  var suffixes = {\n    1: \"'inji\",\n    5: \"'inji\",\n    8: \"'inji\",\n    70: \"'inji\",\n    80: \"'inji\",\n    2: \"'nji\",\n    7: \"'nji\",\n    20: \"'nji\",\n    50: \"'nji\",\n    3: \"'ünji\",\n    4: \"'ünji\",\n    100: \"'ünji\",\n    6: \"'njy\",\n    9: \"'unjy\",\n    10: \"'unjy\",\n    30: \"'unjy\",\n    60: \"'ynjy\",\n    90: \"'ynjy\"\n  };\n  var tk = moment.defineLocale('tk', {\n    months: 'Ýanwar_Fewral_Mart_Aprel_Maý_Iýun_Iýul_Awgust_Sentýabr_Oktýabr_Noýabr_Dekabr'.split('_'),\n    monthsShort: 'Ýan_Few_Mar_Apr_Maý_Iýn_Iýl_Awg_Sen_Okt_Noý_Dek'.split('_'),\n    weekdays: 'Ýekşenbe_Duşenbe_Sişenbe_Çarşenbe_Penşenbe_Anna_Şenbe'.split('_'),\n    weekdaysShort: 'Ýek_Duş_Siş_Çar_Pen_Ann_Şen'.split('_'),\n    weekdaysMin: 'Ýk_Dş_Sş_Çr_Pn_An_Şn'.split('_'),\n    longDateFormat: {\n      LT: 'HH:mm',\n      LTS: 'HH:mm:ss',\n      L: 'DD.MM.YYYY',\n      LL: 'D MMMM YYYY',\n      LLL: 'D MMMM YYYY HH:mm',\n      LLLL: 'dddd, D MMMM YYYY HH:mm'\n    },\n    calendar: {\n      sameDay: '[bugün sagat] LT',\n      nextDay: '[ertir sagat] LT',\n      nextWeek: '[indiki] dddd [sagat] LT',\n      lastDay: '[düýn] LT',\n      lastWeek: '[geçen] dddd [sagat] LT',\n      sameElse: 'L'\n    },\n    relativeTime: {\n      future: '%s soň',\n      past: '%s öň',\n      s: 'birnäçe sekunt',\n      m: 'bir minut',\n      mm: '%d minut',\n      h: 'bir sagat',\n      hh: '%d sagat',\n      d: 'bir gün',\n      dd: '%d gün',\n      M: 'bir aý',\n      MM: '%d aý',\n      y: 'bir ýyl',\n      yy: '%d ýyl'\n    },\n    ordinal: function (number, period) {\n      switch (period) {\n        case 'd':\n        case 'D':\n        case 'Do':\n        case 'DD':\n          return number;\n        default:\n          if (number === 0) {\n            // special case for zero\n            return number + \"'unjy\";\n          }\n          var a = number % 10,\n            b = number % 100 - a,\n            c = number >= 100 ? 100 : null;\n          return number + (suffixes[a] || suffixes[b] || suffixes[c]);\n      }\n    },\n    week: {\n      dow: 1,\n      // Monday is the first day of the week.\n      doy: 7 // The week that contains Jan 7th is the first week of the year.\n    }\n  });\n  return tk;\n});", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}