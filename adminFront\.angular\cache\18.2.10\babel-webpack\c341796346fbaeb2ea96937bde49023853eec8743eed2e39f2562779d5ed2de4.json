{"ast": null, "code": "/* tslint:disable */\n/* eslint-disable */\nimport { HttpResponse } from '@angular/common/http';\nimport { filter, map } from 'rxjs/operators';\nimport { RequestBuilder } from '../../request-builder';\nexport function apiQuotationGetListPost$Json(http, rootUrl, params, context) {\n  const rb = new RequestBuilder(rootUrl, apiQuotationGetListPost$Json.PATH, 'post');\n  if (params) {\n    rb.body(params.body, 'application/*+json');\n  }\n  return http.request(rb.build({\n    responseType: 'json',\n    accept: 'text/json',\n    context\n  })).pipe(filter(r => r instanceof HttpResponse), map(r => {\n    return r;\n  }));\n}\napiQuotationGetListPost$Json.PATH = '/api/Quotation/GetList';", "map": {"version": 3, "names": ["HttpResponse", "filter", "map", "RequestBuilder", "apiQuotationGetListPost$Json", "http", "rootUrl", "params", "context", "rb", "PATH", "body", "request", "build", "responseType", "accept", "pipe", "r"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\services\\api\\fn\\quotation\\api-quotation-get-list-post-json.ts"], "sourcesContent": ["/* tslint:disable */\r\n/* eslint-disable */\r\nimport { HttpClient, HttpContext, HttpResponse } from '@angular/common/http';\r\nimport { Observable } from 'rxjs';\r\nimport { filter, map } from 'rxjs/operators';\r\nimport { StrictHttpResponse } from '../../strict-http-response';\r\nimport { RequestBuilder } from '../../request-builder';\r\n\r\nimport { GetListQuotationRequest } from '../../models/get-list-quotation-request';\r\nimport { GetQuotationListResponseBase } from '../../models/get-quotation-list-response-base';\r\n\r\nexport interface ApiQuotationGetListPost$Json$Params {\r\n    body?: GetListQuotationRequest\r\n}\r\n\r\nexport function apiQuotationGetListPost$Json(http: HttpClient, rootUrl: string, params?: ApiQuotationGetListPost$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<GetQuotationListResponseBase>> {\r\n    const rb = new RequestBuilder(rootUrl, apiQuotationGetListPost$Json.PATH, 'post');\r\n    if (params) {\r\n        rb.body(params.body, 'application/*+json');\r\n    }\r\n\r\n    return http.request(\r\n        rb.build({ responseType: 'json', accept: 'text/json', context })\r\n    ).pipe(\r\n        filter((r: any): r is HttpResponse<any> => r instanceof HttpResponse),\r\n        map((r: HttpResponse<any>) => {\r\n            return r as StrictHttpResponse<GetQuotationListResponseBase>;\r\n        })\r\n    );\r\n}\r\n\r\napiQuotationGetListPost$Json.PATH = '/api/Quotation/GetList';\r\n"], "mappings": "AAAA;AACA;AACA,SAAkCA,YAAY,QAAQ,sBAAsB;AAE5E,SAASC,MAAM,EAAEC,GAAG,QAAQ,gBAAgB;AAE5C,SAASC,cAAc,QAAQ,uBAAuB;AAStD,OAAM,SAAUC,4BAA4BA,CAACC,IAAgB,EAAEC,OAAe,EAAEC,MAA4C,EAAEC,OAAqB;EAC/I,MAAMC,EAAE,GAAG,IAAIN,cAAc,CAACG,OAAO,EAAEF,4BAA4B,CAACM,IAAI,EAAE,MAAM,CAAC;EACjF,IAAIH,MAAM,EAAE;IACRE,EAAE,CAACE,IAAI,CAACJ,MAAM,CAACI,IAAI,EAAE,oBAAoB,CAAC;EAC9C;EAEA,OAAON,IAAI,CAACO,OAAO,CACfH,EAAE,CAACI,KAAK,CAAC;IAAEC,YAAY,EAAE,MAAM;IAAEC,MAAM,EAAE,WAAW;IAAEP;EAAO,CAAE,CAAC,CACnE,CAACQ,IAAI,CACFf,MAAM,CAAEgB,CAAM,IAA6BA,CAAC,YAAYjB,YAAY,CAAC,EACrEE,GAAG,CAAEe,CAAoB,IAAI;IACzB,OAAOA,CAAqD;EAChE,CAAC,CAAC,CACL;AACL;AAEAb,4BAA4B,CAACM,IAAI,GAAG,wBAAwB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}