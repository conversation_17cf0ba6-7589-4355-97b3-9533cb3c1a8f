{"ast": null, "code": "import { map } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../services/api/services/quotation.service\";\nimport * as i2 from \"@angular/common/http\";\nexport class QuotationService {\n  constructor(apiQuotationService, http) {\n    this.apiQuotationService = apiQuotationService;\n    this.http = http;\n    this.apiUrl = '/api/Quotation';\n  }\n  // 取得報價單列表\n  getQuotationList(request) {\n    return this.apiQuotationService.apiQuotationGetListPost$Json({\n      body: request\n    });\n  }\n  // 取得單筆報價單資料\n  getQuotationData(quotationId) {\n    const request = {\n      cQuotationID: quotationId\n    };\n    return this.apiQuotationService.apiQuotationGetDataPost$Json({\n      body: request\n    });\n  } // 取得戶別的報價項目\n  getQuotationByHouseId(houseId) {\n    const request = {\n      cHouseID: houseId\n    };\n    return this.apiQuotationService.apiQuotationGetListByHouseIdPost$Json({\n      body: request\n    }).pipe(map(response => {\n      // 將 API 響應的小寫屬性名轉換為前端期望的大寫屬性名\n      if (response) {\n        return {\n          StatusCode: response.StatusCode,\n          Message: response.Message,\n          TotalItems: response.TotalItems,\n          Entries: response.Entries // 保持原始結構，因為 entries 是一個物件而不是陣列\n        };\n      }\n      return response;\n    }));\n  }\n  // 儲存報價單 (支援單一項目)\n  saveQuotationItem(quotation) {\n    return this.apiQuotationService.apiQuotationSaveDataPost$Json({\n      body: quotation\n    });\n  } // 儲存報價單 (支援批量保存多個項目) - 使用單一請求\n  saveQuotation(request) {\n    // 將 QuotationItem 轉換為 QuotationItemModel 格式\n    const quotationItems = request.items.map(item => ({\n      CItemName: item.cItemName,\n      CUnitPrice: item.cUnitPrice,\n      CCount: item.cCount,\n      CStatus: item.cStatus || 1,\n      CIsDefault: item.cIsDefault || false\n    }));\n    // 建立 SaveDataQuotation 請求\n    const saveRequest = {\n      CHouseID: request.houseId,\n      CQuotationID: request.quotationId || 0,\n      // 使用傳入的 quotationId，如果沒有則為 0（新建報價單）\n      Items: quotationItems\n    };\n    return this.apiQuotationService.apiQuotationSaveDataPost$Json({\n      body: saveRequest\n    }).pipe(map(response => ({\n      success: response?.StatusCode === 0,\n      message: response?.StatusCode === 0 ? '報價單保存成功' : '報價單保存失敗',\n      data: request.items\n    })));\n  }\n  // 取得預設報價項目 (保持原有方法以兼容現有代碼)\n  getDefaultQuotationItems() {\n    // 使用 GetList 方法獲取預設項目\n    const request = {\n      pageIndex: 0,\n      pageSize: 100\n      // 其他預設參數可能需要根據實際 API 需求調整\n    };\n    return this.apiQuotationService.apiQuotationGetListPost$Json({\n      body: request\n    }).pipe(map(response => {\n      // 轉換 API 響應格式以保持兼容性\n      return {\n        success: response.StatusCode === 0,\n        // 假設 statusCode 0 表示成功\n        message: response.Message || '',\n        data: response.Entries || []\n      };\n    }));\n  } // 載入預設報價項目 (LoadDefaultItems API)\n  loadDefaultItems(request) {\n    // 使用注入的 HttpClient 直接發送請求，但使用 apiQuotationService 的 rootUrl\n    return this.http.post(`${this.apiQuotationService.rootUrl}/api/Quotation/LoadDefaultItems`, request).pipe(map(response => {\n      // 轉換 API 響應格式以保持兼容性\n      return {\n        success: response.StatusCode === 0,\n        // 假設 StatusCode 0 表示成功\n        message: response.Message || '',\n        data: response.Entries || []\n      };\n    }));\n  } // 更新報價項目 (使用 SaveData 方法)\n  updateQuotationItem(quotationId, item) {\n    const saveData = {\n      CHouseID: item.cHouseID,\n      CQuotationID: quotationId,\n      Items: [{\n        CItemName: item.cItemName,\n        CUnitPrice: item.cUnitPrice,\n        CCount: item.cCount,\n        CStatus: item.cStatus || 1,\n        CIsDefault: item.cIsDefault || false\n      }]\n    };\n    return this.apiQuotationService.apiQuotationSaveDataPost$Json({\n      body: saveData\n    }).pipe(map(response => {\n      return {\n        success: response.StatusCode === 0,\n        // 假設 StatusCode 0 表示成功\n        message: response.Message || '',\n        data: [item] // 包裝為陣列以符合 QuotationResponse 格式\n      };\n    }));\n  }\n  // 匯出報價單 (需要另外實作，因為 swagger 中沒有此 API)\n  exportQuotation(houseId) {\n    // 這個方法可能需要使用其他 API 或保持原有實作\n    // 暫時拋出錯誤提示需要實作\n    throw new Error('Export quotation functionality needs to be implemented separately');\n  }\n  static {\n    this.ɵfac = function QuotationService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || QuotationService)(i0.ɵɵinject(i1.QuotationService), i0.ɵɵinject(i2.HttpClient));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: QuotationService,\n      factory: QuotationService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["map", "QuotationService", "constructor", "apiQuotationService", "http", "apiUrl", "getQuotationList", "request", "apiQuotationGetListPost$Json", "body", "getQuotationData", "quotationId", "cQuotationID", "apiQuotationGetDataPost$Json", "getQuotationByHouseId", "houseId", "cHouseID", "apiQuotationGetListByHouseIdPost$Json", "pipe", "response", "StatusCode", "Message", "TotalItems", "Entries", "saveQuotationItem", "quotation", "apiQuotationSaveDataPost$Json", "saveQuotation", "quotationItems", "items", "item", "CItemName", "cItemName", "CUnitPrice", "cUnitPrice", "CCount", "cCount", "CStatus", "cStatus", "CIsDefault", "cIsDefault", "saveRequest", "CHouseID", "CQuotationID", "Items", "success", "message", "data", "getDefaultQuotationItems", "pageIndex", "pageSize", "loadDefaultItems", "post", "rootUrl", "updateQuotationItem", "saveData", "exportQuotation", "Error", "i0", "ɵɵinject", "i1", "i2", "HttpClient", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\services\\quotation.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport { Observable } from 'rxjs';\r\nimport { map, switchMap } from 'rxjs/operators';\r\nimport { HttpClient } from '@angular/common/http';\r\nimport { QuotationItem, QuotationRequest, QuotationResponse } from '../models/quotation.model';\r\nimport { QuotationService as ApiQuotationService } from '../../services/api/services/quotation.service';\r\nimport {\r\n  GetListQuotationRequest,\r\n  GetQuotationByIdRequest,\r\n  SaveDataQuotation,\r\n  QuotationItemModel,\r\n  GetListByHouseIdRequest,\r\n  LoadDefaultItemsRequest\r\n} from '../../services/api/models';\r\n\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class QuotationService {\r\n  private readonly apiUrl = '/api/Quotation';\r\n\r\n  constructor(\r\n    private apiQuotationService: ApiQuotationService,\r\n    private http: HttpClient\r\n  ) { }\r\n  // 取得報價單列表\r\n  getQuotationList(request: GetListQuotationRequest): Observable<any> {\r\n    return this.apiQuotationService.apiQuotationGetListPost$Json({ body: request });\r\n  }\r\n  // 取得單筆報價單資料\r\n  getQuotationData(quotationId: number): Observable<any> {\r\n    const request: GetQuotationByIdRequest = { cQuotationID: quotationId };\r\n    return this.apiQuotationService.apiQuotationGetDataPost$Json({ body: request });\r\n  }  // 取得戶別的報價項目\r\n  getQuotationByHouseId(houseId: number): Observable<any> {\r\n    const request: GetListByHouseIdRequest = { cHouseID: houseId };\r\n    return this.apiQuotationService.apiQuotationGetListByHouseIdPost$Json({ body: request }).pipe(\r\n      map(response => {\r\n        // 將 API 響應的小寫屬性名轉換為前端期望的大寫屬性名\r\n        if (response) {\r\n          return {\r\n            StatusCode: response.StatusCode,\r\n            Message: response.Message,\r\n            TotalItems: response.TotalItems,\r\n            Entries: response.Entries // 保持原始結構，因為 entries 是一個物件而不是陣列\r\n          };\r\n        }\r\n        return response;\r\n      })\r\n    );\r\n  }\r\n  // 儲存報價單 (支援單一項目)\r\n  saveQuotationItem(quotation: SaveDataQuotation): Observable<any> {\r\n    return this.apiQuotationService.apiQuotationSaveDataPost$Json({ body: quotation });\r\n  }  // 儲存報價單 (支援批量保存多個項目) - 使用單一請求\r\n  saveQuotation(request: { houseId: number; items: QuotationItem[]; quotationId?: number }): Observable<QuotationResponse> {\r\n    // 將 QuotationItem 轉換為 QuotationItemModel 格式\r\n    const quotationItems: QuotationItemModel[] = request.items.map(item => ({\r\n      CItemName: item.cItemName,\r\n      CUnitPrice: item.cUnitPrice,\r\n      CCount: item.cCount,\r\n      CStatus: item.cStatus || 1,\r\n      CIsDefault: item.cIsDefault || false,\r\n    }));\r\n\r\n    // 建立 SaveDataQuotation 請求\r\n    const saveRequest: SaveDataQuotation = {\r\n      CHouseID: request.houseId,\r\n      CQuotationID: request.quotationId || 0, // 使用傳入的 quotationId，如果沒有則為 0（新建報價單）\r\n      Items: quotationItems\r\n    };\r\n\r\n    return this.apiQuotationService.apiQuotationSaveDataPost$Json({ body: saveRequest }).pipe(\r\n      map(response => ({\r\n        success: response?.StatusCode === 0,\r\n        message: response?.StatusCode === 0 ? '報價單保存成功' : '報價單保存失敗',\r\n        data: request.items\r\n      } as QuotationResponse))\r\n    );\r\n  }\r\n\r\n  // 取得預設報價項目 (保持原有方法以兼容現有代碼)\r\n  getDefaultQuotationItems(): Observable<QuotationResponse> {\r\n    // 使用 GetList 方法獲取預設項目\r\n    const request: GetListQuotationRequest = {\r\n      pageIndex: 0,\r\n      pageSize: 100,\r\n      // 其他預設參數可能需要根據實際 API 需求調整\r\n    };\r\n    return this.apiQuotationService.apiQuotationGetListPost$Json({ body: request }).pipe(\r\n      map(response => {\r\n        // 轉換 API 響應格式以保持兼容性\r\n        return {\r\n          success: response.StatusCode === 0, // 假設 statusCode 0 表示成功\r\n          message: response.Message || '',\r\n          data: response.Entries || []\r\n        } as QuotationResponse;\r\n      })\r\n    );\r\n  }  // 載入預設報價項目 (LoadDefaultItems API)\r\n  loadDefaultItems(request: LoadDefaultItemsRequest): Observable<QuotationResponse> {\r\n    // 使用注入的 HttpClient 直接發送請求，但使用 apiQuotationService 的 rootUrl\r\n    return this.http.post<any>(\r\n      `${this.apiQuotationService.rootUrl}/api/Quotation/LoadDefaultItems`,\r\n      request\r\n    ).pipe(\r\n      map(response => {\r\n        // 轉換 API 響應格式以保持兼容性\r\n        return {\r\n          success: response.StatusCode === 0, // 假設 StatusCode 0 表示成功\r\n          message: response.Message || '',\r\n          data: response.Entries || []\r\n        } as QuotationResponse;\r\n      })\r\n    );\r\n  }  // 更新報價項目 (使用 SaveData 方法)\r\n  updateQuotationItem(quotationId: number, item: QuotationItem): Observable<QuotationResponse> {\r\n    const saveData: SaveDataQuotation = {\r\n      CHouseID: item.cHouseID,\r\n      CQuotationID: quotationId,\r\n      Items: [{\r\n        CItemName: item.cItemName,\r\n        CUnitPrice: item.cUnitPrice,\r\n        CCount: item.cCount,\r\n        CStatus: item.cStatus || 1,\r\n        CIsDefault: item.cIsDefault || false\r\n      }]\r\n    };\r\n    return this.apiQuotationService.apiQuotationSaveDataPost$Json({ body: saveData }).pipe(\r\n      map(response => {\r\n        return {\r\n          success: response.StatusCode === 0, // 假設 StatusCode 0 表示成功\r\n          message: response.Message || '',\r\n          data: [item] // 包裝為陣列以符合 QuotationResponse 格式\r\n        } as QuotationResponse;\r\n      })\r\n    );\r\n  }\r\n\r\n  // 匯出報價單 (需要另外實作，因為 swagger 中沒有此 API)\r\n  exportQuotation(houseId: number): Observable<Blob> {\r\n    // 這個方法可能需要使用其他 API 或保持原有實作\r\n    // 暫時拋出錯誤提示需要實作\r\n    throw new Error('Export quotation functionality needs to be implemented separately');\r\n  }\r\n}\r\n"], "mappings": "AAEA,SAASA,GAAG,QAAmB,gBAAgB;;;;AAgB/C,OAAM,MAAOC,gBAAgB;EAG3BC,YACUC,mBAAwC,EACxCC,IAAgB;IADhB,KAAAD,mBAAmB,GAAnBA,mBAAmB;IACnB,KAAAC,IAAI,GAAJA,IAAI;IAJG,KAAAC,MAAM,GAAG,gBAAgB;EAKtC;EACJ;EACAC,gBAAgBA,CAACC,OAAgC;IAC/C,OAAO,IAAI,CAACJ,mBAAmB,CAACK,4BAA4B,CAAC;MAAEC,IAAI,EAAEF;IAAO,CAAE,CAAC;EACjF;EACA;EACAG,gBAAgBA,CAACC,WAAmB;IAClC,MAAMJ,OAAO,GAA4B;MAAEK,YAAY,EAAED;IAAW,CAAE;IACtE,OAAO,IAAI,CAACR,mBAAmB,CAACU,4BAA4B,CAAC;MAAEJ,IAAI,EAAEF;IAAO,CAAE,CAAC;EACjF,CAAC,CAAE;EACHO,qBAAqBA,CAACC,OAAe;IACnC,MAAMR,OAAO,GAA4B;MAAES,QAAQ,EAAED;IAAO,CAAE;IAC9D,OAAO,IAAI,CAACZ,mBAAmB,CAACc,qCAAqC,CAAC;MAAER,IAAI,EAAEF;IAAO,CAAE,CAAC,CAACW,IAAI,CAC3FlB,GAAG,CAACmB,QAAQ,IAAG;MACb;MACA,IAAIA,QAAQ,EAAE;QACZ,OAAO;UACLC,UAAU,EAAED,QAAQ,CAACC,UAAU;UAC/BC,OAAO,EAAEF,QAAQ,CAACE,OAAO;UACzBC,UAAU,EAAEH,QAAQ,CAACG,UAAU;UAC/BC,OAAO,EAAEJ,QAAQ,CAACI,OAAO,CAAC;SAC3B;MACH;MACA,OAAOJ,QAAQ;IACjB,CAAC,CAAC,CACH;EACH;EACA;EACAK,iBAAiBA,CAACC,SAA4B;IAC5C,OAAO,IAAI,CAACtB,mBAAmB,CAACuB,6BAA6B,CAAC;MAAEjB,IAAI,EAAEgB;IAAS,CAAE,CAAC;EACpF,CAAC,CAAE;EACHE,aAAaA,CAACpB,OAA0E;IACtF;IACA,MAAMqB,cAAc,GAAyBrB,OAAO,CAACsB,KAAK,CAAC7B,GAAG,CAAC8B,IAAI,KAAK;MACtEC,SAAS,EAAED,IAAI,CAACE,SAAS;MACzBC,UAAU,EAAEH,IAAI,CAACI,UAAU;MAC3BC,MAAM,EAAEL,IAAI,CAACM,MAAM;MACnBC,OAAO,EAAEP,IAAI,CAACQ,OAAO,IAAI,CAAC;MAC1BC,UAAU,EAAET,IAAI,CAACU,UAAU,IAAI;KAChC,CAAC,CAAC;IAEH;IACA,MAAMC,WAAW,GAAsB;MACrCC,QAAQ,EAAEnC,OAAO,CAACQ,OAAO;MACzB4B,YAAY,EAAEpC,OAAO,CAACI,WAAW,IAAI,CAAC;MAAE;MACxCiC,KAAK,EAAEhB;KACR;IAED,OAAO,IAAI,CAACzB,mBAAmB,CAACuB,6BAA6B,CAAC;MAAEjB,IAAI,EAAEgC;IAAW,CAAE,CAAC,CAACvB,IAAI,CACvFlB,GAAG,CAACmB,QAAQ,KAAK;MACf0B,OAAO,EAAE1B,QAAQ,EAAEC,UAAU,KAAK,CAAC;MACnC0B,OAAO,EAAE3B,QAAQ,EAAEC,UAAU,KAAK,CAAC,GAAG,SAAS,GAAG,SAAS;MAC3D2B,IAAI,EAAExC,OAAO,CAACsB;KACO,EAAC,CACzB;EACH;EAEA;EACAmB,wBAAwBA,CAAA;IACtB;IACA,MAAMzC,OAAO,GAA4B;MACvC0C,SAAS,EAAE,CAAC;MACZC,QAAQ,EAAE;MACV;KACD;IACD,OAAO,IAAI,CAAC/C,mBAAmB,CAACK,4BAA4B,CAAC;MAAEC,IAAI,EAAEF;IAAO,CAAE,CAAC,CAACW,IAAI,CAClFlB,GAAG,CAACmB,QAAQ,IAAG;MACb;MACA,OAAO;QACL0B,OAAO,EAAE1B,QAAQ,CAACC,UAAU,KAAK,CAAC;QAAE;QACpC0B,OAAO,EAAE3B,QAAQ,CAACE,OAAO,IAAI,EAAE;QAC/B0B,IAAI,EAAE5B,QAAQ,CAACI,OAAO,IAAI;OACN;IACxB,CAAC,CAAC,CACH;EACH,CAAC,CAAE;EACH4B,gBAAgBA,CAAC5C,OAAgC;IAC/C;IACA,OAAO,IAAI,CAACH,IAAI,CAACgD,IAAI,CACnB,GAAG,IAAI,CAACjD,mBAAmB,CAACkD,OAAO,iCAAiC,EACpE9C,OAAO,CACR,CAACW,IAAI,CACJlB,GAAG,CAACmB,QAAQ,IAAG;MACb;MACA,OAAO;QACL0B,OAAO,EAAE1B,QAAQ,CAACC,UAAU,KAAK,CAAC;QAAE;QACpC0B,OAAO,EAAE3B,QAAQ,CAACE,OAAO,IAAI,EAAE;QAC/B0B,IAAI,EAAE5B,QAAQ,CAACI,OAAO,IAAI;OACN;IACxB,CAAC,CAAC,CACH;EACH,CAAC,CAAE;EACH+B,mBAAmBA,CAAC3C,WAAmB,EAAEmB,IAAmB;IAC1D,MAAMyB,QAAQ,GAAsB;MAClCb,QAAQ,EAAEZ,IAAI,CAACd,QAAQ;MACvB2B,YAAY,EAAEhC,WAAW;MACzBiC,KAAK,EAAE,CAAC;QACNb,SAAS,EAAED,IAAI,CAACE,SAAS;QACzBC,UAAU,EAAEH,IAAI,CAACI,UAAU;QAC3BC,MAAM,EAAEL,IAAI,CAACM,MAAM;QACnBC,OAAO,EAAEP,IAAI,CAACQ,OAAO,IAAI,CAAC;QAC1BC,UAAU,EAAET,IAAI,CAACU,UAAU,IAAI;OAChC;KACF;IACD,OAAO,IAAI,CAACrC,mBAAmB,CAACuB,6BAA6B,CAAC;MAAEjB,IAAI,EAAE8C;IAAQ,CAAE,CAAC,CAACrC,IAAI,CACpFlB,GAAG,CAACmB,QAAQ,IAAG;MACb,OAAO;QACL0B,OAAO,EAAE1B,QAAQ,CAACC,UAAU,KAAK,CAAC;QAAE;QACpC0B,OAAO,EAAE3B,QAAQ,CAACE,OAAO,IAAI,EAAE;QAC/B0B,IAAI,EAAE,CAACjB,IAAI,CAAC,CAAC;OACO;IACxB,CAAC,CAAC,CACH;EACH;EAEA;EACA0B,eAAeA,CAACzC,OAAe;IAC7B;IACA;IACA,MAAM,IAAI0C,KAAK,CAAC,mEAAmE,CAAC;EACtF;;;uCA9HWxD,gBAAgB,EAAAyD,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAA3D,gBAAA,GAAAyD,EAAA,CAAAC,QAAA,CAAAE,EAAA,CAAAC,UAAA;IAAA;EAAA;;;aAAhB7D,gBAAgB;MAAA8D,OAAA,EAAhB9D,gBAAgB,CAAA+D,IAAA;MAAAC,UAAA,EAFf;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}