{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { Component } from '@angular/core';\nimport { SharedModule } from '../components/shared.module';\nimport { CommonModule } from '@angular/common';\nimport { EnumFileType } from 'src/app/shared/enum/enumFileType';\nimport { BaseComponent } from '../components/base/baseComponent';\nlet CategoryManagementComponent = class CategoryManagementComponent extends BaseComponent {\n  onCreate() {\n    return;\n  }\n  onEdit(building) {\n    // Xử lý sự kiện khi nhấn nút \"編輯\"\n    // Ví dụ: mở modal để chỉnh sửa thông tin tòa nhà\n    this.step = 'edit-detail';\n  }\n  onDelete(building) {\n    // Xử lý sự kiện khi nhấn nút \"刪除\"\n    // Ví dụ: xác nhận xóa và xóa tòa nhà khỏi danh sách\n  }\n  constructor(_allow, dialogService, message) {\n    super(_allow);\n    this._allow = _allow;\n    this.dialogService = dialogService;\n    this.message = message;\n    this.buildingName = '';\n    this.floor = null;\n    this.units = null;\n    this.step = \"detail\"; // detail, edit, delete\n    this.buildings = [{\n      id: 1,\n      buildingName: 'A棟',\n      floor: 10,\n      units: 50\n    }, {\n      id: 2,\n      buildingName: 'B棟',\n      floor: 15,\n      units: 80\n    }];\n    this.houseTypeName = ''; // Biến để lưu giá trị của trường nhập liệu\n    this.productList = [{\n      cID: 1,\n      cName: \"Product 1\",\n      cFile: \"https://example.com/product1.file\",\n      cBuildCaseld: 123,\n      cCategoryName: \"Electronics\",\n      cStatus: 1,\n      cCreateDT: \"2024-07-02T00:00:00.000Z\",\n      cCreator: \"user123\"\n    }, {\n      cID: 2,\n      cName: \"Product 2\",\n      cFile: \"https://example.com/product2.file\",\n      cCategoryName: \"Clothing\",\n      cStatus: 0,\n      cCreateDT: \"2024-07-02T00:00:00.000Z\",\n      cCreator: \"user456\"\n    }, {\n      cID: 3,\n      cName: \"Product 3\",\n      cFile: \"https://example.com/product3.file\",\n      cBuildCaseld: 456,\n      cCategoryName: \"Furniture\",\n      cStatus: 1,\n      cCreateDT: \"2024-07-02T00:00:00.000Z\",\n      cCreator: \"user789\"\n    }];\n    this.imgSrc = null;\n    this.uploadedFiles = [];\n  }\n  onSubmitDetail() {\n    // Kiểm tra tính hợp lệ (ví dụ: kiểm tra xem trường có rỗng không)\n    if (this.houseTypeName.trim() === '') {\n      // Xử lý trường hợp không hợp lệ (ví dụ: hiển thị thông báo lỗi)\n      return;\n    }\n    // Xử lý logic submit form tại đây\n    console.log(this.houseTypeName);\n  }\n  ngOnInit() {}\n  addNew(ref) {\n    this.isNew = true;\n    this.uploadedFiles = [];\n    this.project = {\n      cName: '',\n      cFile: '',\n      CStatus: '',\n      CSystemInstruction: ''\n    };\n    this.dialogService.open(ref);\n  }\n  onFileSelected(event) {\n    const input = event.target;\n    if (input.files && input.files[0]) {\n      const file = input.files[0];\n      const reader = new FileReader();\n      reader.onload = () => {\n        this.imgSrc = reader.result;\n      };\n      reader.readAsDataURL(file);\n    }\n  }\n  uploadFile(e) {\n    let files = e.target.files;\n    const fileRegex = /pdf|jpg|jpeg|png/i;\n    for (let i = 0; i < files.length; i++) {\n      if (!fileRegex.test(files[i].type)) {\n        this.message.showErrorMSG('檔案格式錯誤，僅限pdf、圖片檔');\n        return;\n      }\n      let file;\n      if (files[i].name.includes('pdf')) {\n        file = files[i];\n        let reader = new FileReader();\n        reader.onload = e => {\n          this.uploadedFiles.push({\n            cID: i,\n            cName: files[i].name,\n            cFile: e.target?.result?.toString().split(',')[1],\n            cImg: null,\n            cFileType: EnumFileType.PDF\n          });\n        };\n        reader.readAsDataURL(files[i]);\n      } else {\n        file = URL.createObjectURL(files[i]);\n        let reader = new FileReader();\n        reader.onload = e => {\n          this.uploadedFiles.push({\n            cID: i,\n            cName: files[i].name,\n            cFile: e.target?.result?.toString().split(',')[1],\n            cImg: file,\n            cFileType: EnumFileType.JPG\n          });\n        };\n        URL.revokeObjectURL(files[i]);\n        reader.readAsDataURL(files[i]);\n      }\n    }\n  }\n  deleteItem(item) {\n    if (window.confirm(`確定要移除【${item.cName}】?`)) {\n      this.uploadedFiles = this.uploadedFiles.filter(i => i.cID !== item.cID);\n    }\n  }\n  onSubmit() {}\n};\nCategoryManagementComponent = __decorate([Component({\n  selector: 'ngx-category-management',\n  templateUrl: './category-management.component.html',\n  styleUrls: ['./category-management.component.scss'],\n  standalone: true,\n  imports: [CommonModule, SharedModule]\n})], CategoryManagementComponent);\nexport { CategoryManagementComponent };", "map": {"version": 3, "names": ["Component", "SharedModule", "CommonModule", "EnumFileType", "BaseComponent", "CategoryManagementComponent", "onCreate", "onEdit", "building", "step", "onDelete", "constructor", "_allow", "dialogService", "message", "buildingName", "floor", "units", "buildings", "id", "houseTypeName", "productList", "cID", "cName", "cFile", "cBuildCaseld", "cCategoryName", "cStatus", "cCreateDT", "cCreator", "imgSrc", "uploadedFiles", "onSubmitDetail", "trim", "console", "log", "ngOnInit", "addNew", "ref", "isNew", "project", "CStatus", "CSystemInstruction", "open", "onFileSelected", "event", "input", "target", "files", "file", "reader", "FileReader", "onload", "result", "readAsDataURL", "uploadFile", "e", "fileRegex", "i", "length", "test", "type", "showErrorMSG", "name", "includes", "push", "toString", "split", "cImg", "cFileType", "PDF", "URL", "createObjectURL", "JPG", "revokeObjectURL", "deleteItem", "item", "window", "confirm", "filter", "onSubmit", "__decorate", "selector", "templateUrl", "styleUrls", "standalone", "imports"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\category-management\\category-management.component.ts"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { SharedModule } from '../components/shared.module';\r\nimport { CommonModule } from '@angular/common';\r\nimport { NbDialogService } from '@nebular/theme';\r\nimport { MessageService } from 'src/app/shared/services/message.service';\r\nimport { EnumFileType } from 'src/app/shared/enum/enumFileType';\r\nimport { BaseComponent } from '../components/base/baseComponent';\r\nimport { AllowHelper } from 'src/app/shared/helper/allowHelper';\r\n\r\n@Component({\r\n  selector: 'ngx-category-management',\r\n  templateUrl: './category-management.component.html',\r\n  styleUrls: ['./category-management.component.scss'],\r\n  standalone: true,\r\n  imports: [CommonModule, SharedModule,],\r\n})\r\n\r\nexport class CategoryManagementComponent extends BaseComponent implements OnInit {\r\n  buildingName = '';\r\n  floor = null;\r\n  units = null;\r\n  step = \"detail\" // detail, edit, delete\r\n\r\n  buildings = [\r\n    { id: 1, buildingName: 'A棟', floor: 10, units: 50 },\r\n    { id: 2, buildingName: 'B棟', floor: 15, units: 80 },\r\n  ];\r\n\r\n  onCreate() {\r\n    return\r\n  }\r\n\r\n  onEdit(building: any) {\r\n    // Xử lý sự kiện khi nhấn nút \"編輯\"\r\n    // Ví dụ: mở modal để chỉnh sửa thông tin tòa nhà\r\n    this.step = 'edit-detail'\r\n  }\r\n\r\n  onDelete(building: any) {\r\n    // Xử lý sự kiện khi nhấn nút \"刪除\"\r\n    // Ví dụ: xác nhận xóa và xóa tòa nhà khỏi danh sách\r\n  }\r\n\r\n  constructor(\r\n    private _allow: AllowHelper,\r\n    private dialogService: NbDialogService,\r\n    private message: MessageService,\r\n  ) {\r\n    super(_allow)\r\n  }\r\n  houseTypeName = ''; // Biến để lưu giá trị của trường nhập liệu\r\n  onSubmitDetail() {\r\n    // Kiểm tra tính hợp lệ (ví dụ: kiểm tra xem trường có rỗng không)\r\n    if (this.houseTypeName.trim() === '') {\r\n      // Xử lý trường hợp không hợp lệ (ví dụ: hiển thị thông báo lỗi)\r\n      return;\r\n    }\r\n\r\n    // Xử lý logic submit form tại đây\r\n    console.log(this.houseTypeName);\r\n  }\r\n\r\n  override ngOnInit(): void {\r\n  }\r\n\r\n  search: string\r\n\r\n  productList: any[] = [\r\n    {\r\n      cID: 1,\r\n      cName: \"Product 1\",\r\n      cFile: \"https://example.com/product1.file\",\r\n      cBuildCaseld: 123,\r\n      cCategoryName: \"Electronics\",\r\n      cStatus: 1,\r\n      cCreateDT: \"2024-07-02T00:00:00.000Z\",\r\n      cCreator: \"user123\"\r\n    },\r\n    {\r\n      cID: 2,\r\n      cName: \"Product 2\",\r\n      cFile: \"https://example.com/product2.file\",\r\n      cCategoryName: \"Clothing\",\r\n      cStatus: 0,\r\n      cCreateDT: \"2024-07-02T00:00:00.000Z\",\r\n      cCreator: \"user456\"\r\n    },\r\n    {\r\n      cID: 3,\r\n      cName: \"Product 3\",\r\n      cFile: \"https://example.com/product3.file\",\r\n      cBuildCaseld: 456,\r\n      cCategoryName: \"Furniture\",\r\n      cStatus: 1,\r\n      cCreateDT: \"2024-07-02T00:00:00.000Z\",\r\n      cCreator: \"user789\"\r\n    }\r\n  ];\r\n\r\n\r\n  project: {\r\n    cName: string\r\n    cFile: string\r\n    CStatus: string\r\n    CSystemInstruction: string\r\n  }\r\n\r\n  status: any\r\n  isNew: true\r\n\r\n  addNew(ref: any) {\r\n    this.isNew = true;\r\n    this.uploadedFiles = [];\r\n    this.project = {\r\n      cName: '',\r\n      cFile: '',\r\n      CStatus: '',\r\n      CSystemInstruction: '',\r\n    }\r\n    this.dialogService.open(ref)\r\n  }\r\n\r\n  imgSrc: string | ArrayBuffer | null = null;\r\n\r\n  onFileSelected(event: Event): void {\r\n    const input = event.target as HTMLInputElement;\r\n    if (input.files && input.files[0]) {\r\n      const file = input.files[0];\r\n      const reader = new FileReader();\r\n      reader.onload = () => {\r\n        this.imgSrc = reader.result;\r\n      };\r\n      reader.readAsDataURL(file);\r\n    }\r\n  }\r\n\r\n  uploadedFiles: any[] = [];\r\n\r\n  uploadFile(e: any) {\r\n    let files = e.target.files;\r\n    const fileRegex = /pdf|jpg|jpeg|png/i;\r\n\r\n    for (let i = 0; i < files.length; i++) {\r\n      if (!fileRegex.test(files[i].type)) {\r\n        this.message.showErrorMSG('檔案格式錯誤，僅限pdf、圖片檔')\r\n        return\r\n      }\r\n      let file: string;\r\n      if (files[i].name.includes('pdf')) {\r\n        file = files[i];\r\n        let reader = new FileReader();\r\n        reader.onload = e => {\r\n          this.uploadedFiles.push({\r\n            cID: i,\r\n            cName: files[i].name,\r\n            cFile: e.target?.result?.toString().split(',')[1],\r\n            cImg: null,\r\n            cFileType: EnumFileType.PDF\r\n          })\r\n        }\r\n        reader.readAsDataURL(files[i]);\r\n      } else {\r\n        file = URL.createObjectURL(files[i]);\r\n        let reader = new FileReader();\r\n        reader.onload = e => {\r\n          this.uploadedFiles.push({\r\n            cID: i,\r\n            cName: files[i].name,\r\n            cFile: e.target?.result?.toString().split(',')[1],\r\n            cImg: file,\r\n            cFileType: EnumFileType.JPG\r\n          })\r\n        }\r\n        URL.revokeObjectURL(files[i])\r\n        reader.readAsDataURL(files[i]);\r\n      }\r\n    }\r\n  }\r\n\r\n  deleteItem(item: any) {\r\n    if (window.confirm(`確定要移除【${item.cName}】?`)) {\r\n      this.uploadedFiles = this.uploadedFiles.filter(i => i.cID !== item.cID);\r\n    }\r\n  }\r\n\r\n  onSubmit() {\r\n  }\r\n\r\n}\r\n"], "mappings": ";AAAA,SAASA,SAAS,QAAgB,eAAe;AACjD,SAASC,YAAY,QAAQ,6BAA6B;AAC1D,SAASC,YAAY,QAAQ,iBAAiB;AAG9C,SAASC,YAAY,QAAQ,kCAAkC;AAC/D,SAASC,aAAa,QAAQ,kCAAkC;AAWzD,IAAMC,2BAA2B,GAAjC,MAAMA,2BAA4B,SAAQD,aAAa;EAW5DE,QAAQA,CAAA;IACN;EACF;EAEAC,MAAMA,CAACC,QAAa;IAClB;IACA;IACA,IAAI,CAACC,IAAI,GAAG,aAAa;EAC3B;EAEAC,QAAQA,CAACF,QAAa;IACpB;IACA;EAAA;EAGFG,YACUC,MAAmB,EACnBC,aAA8B,EAC9BC,OAAuB;IAE/B,KAAK,CAACF,MAAM,CAAC;IAJL,KAAAA,MAAM,GAANA,MAAM;IACN,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,OAAO,GAAPA,OAAO;IA5BjB,KAAAC,YAAY,GAAG,EAAE;IACjB,KAAAC,KAAK,GAAG,IAAI;IACZ,KAAAC,KAAK,GAAG,IAAI;IACZ,KAAAR,IAAI,GAAG,QAAQ,EAAC;IAEhB,KAAAS,SAAS,GAAG,CACV;MAAEC,EAAE,EAAE,CAAC;MAAEJ,YAAY,EAAE,IAAI;MAAEC,KAAK,EAAE,EAAE;MAAEC,KAAK,EAAE;IAAE,CAAE,EACnD;MAAEE,EAAE,EAAE,CAAC;MAAEJ,YAAY,EAAE,IAAI;MAAEC,KAAK,EAAE,EAAE;MAAEC,KAAK,EAAE;IAAE,CAAE,CACpD;IAwBD,KAAAG,aAAa,GAAG,EAAE,CAAC,CAAC;IAiBpB,KAAAC,WAAW,GAAU,CACnB;MACEC,GAAG,EAAE,CAAC;MACNC,KAAK,EAAE,WAAW;MAClBC,KAAK,EAAE,mCAAmC;MAC1CC,YAAY,EAAE,GAAG;MACjBC,aAAa,EAAE,aAAa;MAC5BC,OAAO,EAAE,CAAC;MACVC,SAAS,EAAE,0BAA0B;MACrCC,QAAQ,EAAE;KACX,EACD;MACEP,GAAG,EAAE,CAAC;MACNC,KAAK,EAAE,WAAW;MAClBC,KAAK,EAAE,mCAAmC;MAC1CE,aAAa,EAAE,UAAU;MACzBC,OAAO,EAAE,CAAC;MACVC,SAAS,EAAE,0BAA0B;MACrCC,QAAQ,EAAE;KACX,EACD;MACEP,GAAG,EAAE,CAAC;MACNC,KAAK,EAAE,WAAW;MAClBC,KAAK,EAAE,mCAAmC;MAC1CC,YAAY,EAAE,GAAG;MACjBC,aAAa,EAAE,WAAW;MAC1BC,OAAO,EAAE,CAAC;MACVC,SAAS,EAAE,0BAA0B;MACrCC,QAAQ,EAAE;KACX,CACF;IAyBD,KAAAC,MAAM,GAAgC,IAAI;IAc1C,KAAAC,aAAa,GAAU,EAAE;EAvFzB;EAEAC,cAAcA,CAAA;IACZ;IACA,IAAI,IAAI,CAACZ,aAAa,CAACa,IAAI,EAAE,KAAK,EAAE,EAAE;MACpC;MACA;IACF;IAEA;IACAC,OAAO,CAACC,GAAG,CAAC,IAAI,CAACf,aAAa,CAAC;EACjC;EAESgB,QAAQA,CAAA,GACjB;EA+CAC,MAAMA,CAACC,GAAQ;IACb,IAAI,CAACC,KAAK,GAAG,IAAI;IACjB,IAAI,CAACR,aAAa,GAAG,EAAE;IACvB,IAAI,CAACS,OAAO,GAAG;MACbjB,KAAK,EAAE,EAAE;MACTC,KAAK,EAAE,EAAE;MACTiB,OAAO,EAAE,EAAE;MACXC,kBAAkB,EAAE;KACrB;IACD,IAAI,CAAC7B,aAAa,CAAC8B,IAAI,CAACL,GAAG,CAAC;EAC9B;EAIAM,cAAcA,CAACC,KAAY;IACzB,MAAMC,KAAK,GAAGD,KAAK,CAACE,MAA0B;IAC9C,IAAID,KAAK,CAACE,KAAK,IAAIF,KAAK,CAACE,KAAK,CAAC,CAAC,CAAC,EAAE;MACjC,MAAMC,IAAI,GAAGH,KAAK,CAACE,KAAK,CAAC,CAAC,CAAC;MAC3B,MAAME,MAAM,GAAG,IAAIC,UAAU,EAAE;MAC/BD,MAAM,CAACE,MAAM,GAAG,MAAK;QACnB,IAAI,CAACtB,MAAM,GAAGoB,MAAM,CAACG,MAAM;MAC7B,CAAC;MACDH,MAAM,CAACI,aAAa,CAACL,IAAI,CAAC;IAC5B;EACF;EAIAM,UAAUA,CAACC,CAAM;IACf,IAAIR,KAAK,GAAGQ,CAAC,CAACT,MAAM,CAACC,KAAK;IAC1B,MAAMS,SAAS,GAAG,mBAAmB;IAErC,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGV,KAAK,CAACW,MAAM,EAAED,CAAC,EAAE,EAAE;MACrC,IAAI,CAACD,SAAS,CAACG,IAAI,CAACZ,KAAK,CAACU,CAAC,CAAC,CAACG,IAAI,CAAC,EAAE;QAClC,IAAI,CAAC/C,OAAO,CAACgD,YAAY,CAAC,kBAAkB,CAAC;QAC7C;MACF;MACA,IAAIb,IAAY;MAChB,IAAID,KAAK,CAACU,CAAC,CAAC,CAACK,IAAI,CAACC,QAAQ,CAAC,KAAK,CAAC,EAAE;QACjCf,IAAI,GAAGD,KAAK,CAACU,CAAC,CAAC;QACf,IAAIR,MAAM,GAAG,IAAIC,UAAU,EAAE;QAC7BD,MAAM,CAACE,MAAM,GAAGI,CAAC,IAAG;UAClB,IAAI,CAACzB,aAAa,CAACkC,IAAI,CAAC;YACtB3C,GAAG,EAAEoC,CAAC;YACNnC,KAAK,EAAEyB,KAAK,CAACU,CAAC,CAAC,CAACK,IAAI;YACpBvC,KAAK,EAAEgC,CAAC,CAACT,MAAM,EAAEM,MAAM,EAAEa,QAAQ,EAAE,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;YACjDC,IAAI,EAAE,IAAI;YACVC,SAAS,EAAElE,YAAY,CAACmE;WACzB,CAAC;QACJ,CAAC;QACDpB,MAAM,CAACI,aAAa,CAACN,KAAK,CAACU,CAAC,CAAC,CAAC;MAChC,CAAC,MAAM;QACLT,IAAI,GAAGsB,GAAG,CAACC,eAAe,CAACxB,KAAK,CAACU,CAAC,CAAC,CAAC;QACpC,IAAIR,MAAM,GAAG,IAAIC,UAAU,EAAE;QAC7BD,MAAM,CAACE,MAAM,GAAGI,CAAC,IAAG;UAClB,IAAI,CAACzB,aAAa,CAACkC,IAAI,CAAC;YACtB3C,GAAG,EAAEoC,CAAC;YACNnC,KAAK,EAAEyB,KAAK,CAACU,CAAC,CAAC,CAACK,IAAI;YACpBvC,KAAK,EAAEgC,CAAC,CAACT,MAAM,EAAEM,MAAM,EAAEa,QAAQ,EAAE,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;YACjDC,IAAI,EAAEnB,IAAI;YACVoB,SAAS,EAAElE,YAAY,CAACsE;WACzB,CAAC;QACJ,CAAC;QACDF,GAAG,CAACG,eAAe,CAAC1B,KAAK,CAACU,CAAC,CAAC,CAAC;QAC7BR,MAAM,CAACI,aAAa,CAACN,KAAK,CAACU,CAAC,CAAC,CAAC;MAChC;IACF;EACF;EAEAiB,UAAUA,CAACC,IAAS;IAClB,IAAIC,MAAM,CAACC,OAAO,CAAC,SAASF,IAAI,CAACrD,KAAK,IAAI,CAAC,EAAE;MAC3C,IAAI,CAACQ,aAAa,GAAG,IAAI,CAACA,aAAa,CAACgD,MAAM,CAACrB,CAAC,IAAIA,CAAC,CAACpC,GAAG,KAAKsD,IAAI,CAACtD,GAAG,CAAC;IACzE;EACF;EAEA0D,QAAQA,CAAA,GACR;CAED;AA3KY3E,2BAA2B,GAAA4E,UAAA,EARvCjF,SAAS,CAAC;EACTkF,QAAQ,EAAE,yBAAyB;EACnCC,WAAW,EAAE,sCAAsC;EACnDC,SAAS,EAAE,CAAC,sCAAsC,CAAC;EACnDC,UAAU,EAAE,IAAI;EAChBC,OAAO,EAAE,CAACpF,YAAY,EAAED,YAAY;CACrC,CAAC,C,EAEWI,2BAA2B,CA2KvC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}