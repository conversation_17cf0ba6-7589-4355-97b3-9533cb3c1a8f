{"ast": null, "code": "import * as i0 from \"@angular/core\";\nexport class LocalStorageService {\n  // public static AddLocalStorage(key: string, value: any): void {\n  //   localStorage.setItem(key, this._Add(value));\n  // }\n  static RemoveLocalStorage(key) {\n    localStorage.removeItem(key);\n  }\n  // public static GetLocalStorage(key: string): any {\n  //   let resultStr = this._Get(localStorage.getItem(key) || \"\");\n  //   return resultStr;\n  // }\n  static ClearLocalStorage() {\n    localStorage.clear();\n  }\n  static {\n    this.ɵfac = function LocalStorageService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || LocalStorageService)();\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: LocalStorageService,\n      factory: LocalStorageService.ɵfac,\n      providedIn: \"root\"\n    });\n  }\n}", "map": {"version": 3, "names": ["LocalStorageService", "RemoveLocalStorage", "key", "localStorage", "removeItem", "ClearLocalStorage", "clear", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\Documents\\jeansalechange\\adminFront\\src\\app\\shared\\services\\local-storage.service.ts"], "sourcesContent": ["import { Injectable } from \"@angular/core\";\r\nimport { CryptoService } from \"./crypto.service\";\r\n\r\n@Injectable({\r\n  providedIn: \"root\",\r\n})\r\nexport abstract class LocalStorageService {\r\n  // public static AddLocalStorage(key: string, value: any): void {\r\n  //   localStorage.setItem(key, this._Add(value));\r\n  // }\r\n\r\n  public static RemoveLocalStorage(key: string): void {\r\n    localStorage.removeItem(key);\r\n  }\r\n\r\n  // public static GetLocalStorage(key: string): any {\r\n  //   let resultStr = this._Get(localStorage.getItem(key) || \"\");\r\n  //   return resultStr;\r\n  // }\r\n\r\n  public static ClearLocalStorage(): void {\r\n    localStorage.clear();\r\n  }\r\n\r\n  // public static AddSessionStorage(key: string, value: string): void {\r\n  //   sessionStorage.setItem(key, this._Add(value));\r\n  // }\r\n\r\n  // public static RemoveSessionStorage(key: string): void {\r\n  //   sessionStorage.removeItem(key);\r\n  // }\r\n\r\n  // public static GetSessionStorage(key: string): string {\r\n  //   let resultStr = this._Get(sessionStorage.getItem(key) || \"\");\r\n  //   return resultStr;\r\n  // }\r\n\r\n  // private static _Add(value: string): string {\r\n  //     let result: string = CryptoService.EncryptUsingAES256(value);\r\n  //     return result;\r\n  // }\r\n\r\n  // private static _Get(value: string): string {\r\n  //     let result: string = CryptoService.DecryptUsingAES256(value);\r\n  //     result.split(\"\\\\\").join(\"\");\r\n  //     result.slice(0, 1);\r\n  //     result.slice(result.length - 1, 1);\r\n  //     return result == \"\" ? \"\" : JSON.parse(result);\r\n  // }\r\n}\r\n"], "mappings": ";AAMA,OAAM,MAAgBA,mBAAmB;EACvC;EACA;EACA;EAEO,OAAOC,kBAAkBA,CAACC,GAAW;IAC1CC,YAAY,CAACC,UAAU,CAACF,GAAG,CAAC;EAC9B;EAEA;EACA;EACA;EACA;EAEO,OAAOG,iBAAiBA,CAAA;IAC7BF,YAAY,CAACG,KAAK,EAAE;EACtB;;;uCAhBoBN,mBAAmB;IAAA;EAAA;;;aAAnBA,mBAAmB;MAAAO,OAAA,EAAnBP,mBAAmB,CAAAQ,IAAA;MAAAC,UAAA,EAF3B;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}