{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { Injectable } from '@angular/core';\nlet QuotationService = class QuotationService {\n  constructor(apiQuotationService) {\n    this.apiQuotationService = apiQuotationService;\n  }\n  // 取得戶別的報價項目\n  getQuotationByHouseId(houseId) {\n    return this.http.get(`${this.apiUrl}/house/${houseId}`);\n  }\n  // 取得預設報價項目\n  getDefaultQuotationItems() {\n    return this.http.get(`${this.apiUrl}/default`);\n  }\n  // 儲存報價單\n  saveQuotation(request) {\n    return this.http.post(this.apiUrl, request);\n  }\n  // 更新報價項目\n  updateQuotationItem(quotationId, item) {\n    return this.http.put(`${this.apiUrl}/${quotationId}`, item);\n  }\n  // 刪除報價項目\n  deleteQuotationItem(quotationId) {\n    return this.http.delete(`${this.apiUrl}/${quotationId}`);\n  }\n  // 匯出報價單\n  exportQuotation(houseId) {\n    return this.http.get(`${this.apiUrl}/export/${houseId}`, {\n      responseType: 'blob'\n    });\n  }\n};\nQuotationService = __decorate([Injectable({\n  providedIn: 'root'\n})], QuotationService);\nexport { QuotationService };", "map": {"version": 3, "names": ["Injectable", "QuotationService", "constructor", "apiQuotationService", "getQuotationByHouseId", "houseId", "http", "get", "apiUrl", "getDefaultQuotationItems", "saveQuotation", "request", "post", "updateQuotationItem", "quotationId", "item", "put", "deleteQuotationItem", "delete", "exportQuotation", "responseType", "__decorate", "providedIn"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\services\\quotation.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport { Observable } from 'rxjs';\r\nimport { map } from 'rxjs/operators';\r\nimport { QuotationItem, QuotationRequest, QuotationResponse } from '../models/quotation.model';\r\nimport { QuotationService as ApiQuotationService } from '../../services/api/services/quotation.service';\r\nimport { \r\n  GetListQuotationRequest,\r\n  GetQuotationByIdRequest,\r\n  SaveDataQuotation,\r\n  DeleteQuotationRequest,\r\n  GetListByHouseIdRequest\r\n} from '../../services/api/models';\r\n\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class QuotationService {\r\n\r\n  constructor(private apiQuotationService: ApiQuotationService) { }\r\n\r\n  // 取得戶別的報價項目\r\n  getQuotationByHouseId(houseId: number): Observable<QuotationResponse> {\r\n    return this.http.get<QuotationResponse>(`${this.apiUrl}/house/${houseId}`);\r\n  }\r\n\r\n  // 取得預設報價項目\r\n  getDefaultQuotationItems(): Observable<QuotationResponse> {\r\n    return this.http.get<QuotationResponse>(`${this.apiUrl}/default`);\r\n  }\r\n\r\n  // 儲存報價單\r\n  saveQuotation(request: QuotationRequest): Observable<QuotationResponse> {\r\n    return this.http.post<QuotationResponse>(this.apiUrl, request);\r\n  }\r\n\r\n  // 更新報價項目\r\n  updateQuotationItem(quotationId: number, item: QuotationItem): Observable<QuotationResponse> {\r\n    return this.http.put<QuotationResponse>(`${this.apiUrl}/${quotationId}`, item);\r\n  }\r\n\r\n  // 刪除報價項目\r\n  deleteQuotationItem(quotationId: number): Observable<QuotationResponse> {\r\n    return this.http.delete<QuotationResponse>(`${this.apiUrl}/${quotationId}`);\r\n  }\r\n\r\n  // 匯出報價單\r\n  exportQuotation(houseId: number): Observable<Blob> {\r\n    return this.http.get(`${this.apiUrl}/export/${houseId}`, {\r\n      responseType: 'blob'\r\n    });\r\n  }\r\n}\r\n"], "mappings": ";AAAA,SAASA,UAAU,QAAQ,eAAe;AAgBnC,IAAMC,gBAAgB,GAAtB,MAAMA,gBAAgB;EAE3BC,YAAoBC,mBAAwC;IAAxC,KAAAA,mBAAmB,GAAnBA,mBAAmB;EAAyB;EAEhE;EACAC,qBAAqBA,CAACC,OAAe;IACnC,OAAO,IAAI,CAACC,IAAI,CAACC,GAAG,CAAoB,GAAG,IAAI,CAACC,MAAM,UAAUH,OAAO,EAAE,CAAC;EAC5E;EAEA;EACAI,wBAAwBA,CAAA;IACtB,OAAO,IAAI,CAACH,IAAI,CAACC,GAAG,CAAoB,GAAG,IAAI,CAACC,MAAM,UAAU,CAAC;EACnE;EAEA;EACAE,aAAaA,CAACC,OAAyB;IACrC,OAAO,IAAI,CAACL,IAAI,CAACM,IAAI,CAAoB,IAAI,CAACJ,MAAM,EAAEG,OAAO,CAAC;EAChE;EAEA;EACAE,mBAAmBA,CAACC,WAAmB,EAAEC,IAAmB;IAC1D,OAAO,IAAI,CAACT,IAAI,CAACU,GAAG,CAAoB,GAAG,IAAI,CAACR,MAAM,IAAIM,WAAW,EAAE,EAAEC,IAAI,CAAC;EAChF;EAEA;EACAE,mBAAmBA,CAACH,WAAmB;IACrC,OAAO,IAAI,CAACR,IAAI,CAACY,MAAM,CAAoB,GAAG,IAAI,CAACV,MAAM,IAAIM,WAAW,EAAE,CAAC;EAC7E;EAEA;EACAK,eAAeA,CAACd,OAAe;IAC7B,OAAO,IAAI,CAACC,IAAI,CAACC,GAAG,CAAC,GAAG,IAAI,CAACC,MAAM,WAAWH,OAAO,EAAE,EAAE;MACvDe,YAAY,EAAE;KACf,CAAC;EACJ;CACD;AAnCYnB,gBAAgB,GAAAoB,UAAA,EAH5BrB,UAAU,CAAC;EACVsB,UAAU,EAAE;CACb,CAAC,C,EACWrB,gBAAgB,CAmC5B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}