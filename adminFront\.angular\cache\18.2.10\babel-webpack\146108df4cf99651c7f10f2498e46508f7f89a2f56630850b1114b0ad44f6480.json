{"ast": null, "code": "import { EventEmitter, forwardRef } from '@angular/core';\nimport { NG_VALUE_ACCESSOR } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../../../services/api/services/HouseCustom.service\";\nimport * as i2 from \"@nebular/theme\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"@angular/forms\";\nconst _c0 = [\"householdDialog\"];\nfunction HouseholdBindingComponent_div_1_div_9_ng_container_1_span_4_span_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 26);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const householdCode_r5 = i0.ɵɵnextContext().$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.getHouseholdInfo(householdCode_r5).floor, \" \");\n  }\n}\nfunction HouseholdBindingComponent_div_1_div_9_ng_container_1_span_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"span\", 20)(1, \"div\", 21)(2, \"span\", 22);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, HouseholdBindingComponent_div_1_div_9_ng_container_1_span_4_span_4_Template, 2, 1, \"span\", 23);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 24);\n    i0.ɵɵlistener(\"click\", function HouseholdBindingComponent_div_1_div_9_ng_container_1_span_4_Template_button_click_5_listener() {\n      const householdCode_r5 = i0.ɵɵrestoreView(_r4).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r2.onRemoveHousehold(householdCode_r5));\n    });\n    i0.ɵɵelement(6, \"nb-icon\", 25);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const householdCode_r5 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r2.getHouseholdInfo(householdCode_r5).houseName);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.getHouseholdInfo(householdCode_r5).floor);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r2.disabled);\n  }\n}\nfunction HouseholdBindingComponent_div_1_div_9_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 17);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 18);\n    i0.ɵɵtemplate(4, HouseholdBindingComponent_div_1_div_9_ng_container_1_span_4_Template, 7, 3, \"span\", 19);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const building_r6 = i0.ɵɵnextContext().$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", building_r6, \":\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.getBuildingSelectedHouseholds(building_r6));\n  }\n}\nfunction HouseholdBindingComponent_div_1_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 16);\n    i0.ɵɵtemplate(1, HouseholdBindingComponent_div_1_div_9_ng_container_1_Template, 5, 2, \"ng-container\", 6);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const building_r6 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.hasBuildingSelected(building_r6));\n  }\n}\nfunction HouseholdBindingComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 8)(1, \"div\", 9)(2, \"div\", 10);\n    i0.ɵɵelement(3, \"nb-icon\", 11);\n    i0.ɵɵelementStart(4, \"span\", 12);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"button\", 13);\n    i0.ɵɵlistener(\"click\", function HouseholdBindingComponent_div_1_Template_button_click_6_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onClearAll());\n    });\n    i0.ɵɵtext(7, \" \\u6E05\\u7A7A\\u5168\\u90E8 \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 14);\n    i0.ɵɵtemplate(9, HouseholdBindingComponent_div_1_div_9_Template, 2, 1, \"div\", 15);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\"\\u5DF2\\u9078\\u64C7\\u6236\\u5225 (\", ctx_r2.getSelectedCount(), \")\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r2.disabled);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.buildings);\n  }\n}\nfunction HouseholdBindingComponent_ng_container_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelement(1, \"nb-icon\", 27);\n    i0.ɵɵtext(2, \" \\u8F09\\u5165\\u4E2D... \");\n    i0.ɵɵelementContainerEnd();\n  }\n}\nfunction HouseholdBindingComponent_ng_container_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.getSelectedCount() > 0 ? \"\\u5DF2\\u9078\\u64C7 \" + ctx_r2.getSelectedCount() + \" \\u500B\\u6236\\u5225\" : ctx_r2.placeholder, \" \");\n  }\n}\nfunction HouseholdBindingComponent_ng_template_8_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 51)(1, \"div\", 52);\n    i0.ɵɵelement(2, \"nb-icon\", 53);\n    i0.ɵɵelementStart(3, \"p\", 54);\n    i0.ɵɵtext(4, \"\\u8F09\\u5165\\u6236\\u5225\\u8CC7\\u6599\\u4E2D...\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction HouseholdBindingComponent_ng_template_8_div_13_button_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 71);\n    i0.ɵɵlistener(\"click\", function HouseholdBindingComponent_ng_template_8_div_13_button_6_Template_button_click_0_listener() {\n      const building_r9 = i0.ɵɵrestoreView(_r8).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r2.onBuildingSelect(building_r9));\n    });\n    i0.ɵɵelementStart(1, \"span\", 72);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\", 73);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const building_r9 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵstyleProp(\"background-color\", ctx_r2.selectedBuilding === building_r9 ? \"#e3f2fd\" : \"transparent\")(\"border-left\", ctx_r2.selectedBuilding === building_r9 ? \"3px solid #007bff\" : \"3px solid transparent\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(building_r9);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.getBuildingCount(building_r9), \"\\u6236 \");\n  }\n}\nfunction HouseholdBindingComponent_ng_template_8_div_13_span_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 74);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\"(\", ctx_r2.selectedBuilding, \")\");\n  }\n}\nfunction HouseholdBindingComponent_ng_template_8_div_13_span_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 75);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" - \", ctx_r2.selectedFloor, \"\");\n  }\n}\nfunction HouseholdBindingComponent_ng_template_8_div_13_div_14_button_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 80);\n    i0.ɵɵlistener(\"click\", function HouseholdBindingComponent_ng_template_8_div_13_div_14_button_5_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r2 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r2.onUnselectAllBuilding());\n    });\n    i0.ɵɵtext(1, \" \\u6E05\\u9664 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction HouseholdBindingComponent_ng_template_8_div_13_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 76)(1, \"button\", 77);\n    i0.ɵɵlistener(\"click\", function HouseholdBindingComponent_ng_template_8_div_13_div_14_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r2.onSelectAllFiltered());\n    });\n    i0.ɵɵtext(2, \" \\u5168\\u9078\\u7576\\u524D \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"button\", 78);\n    i0.ɵɵlistener(\"click\", function HouseholdBindingComponent_ng_template_8_div_13_div_14_Template_button_click_3_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r2.onSelectAllBuilding());\n    });\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(5, HouseholdBindingComponent_ng_template_8_div_13_div_14_button_5_Template, 2, 0, \"button\", 79);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵstyleProp(\"opacity\", ctx_r2.canSelectMore() ? \"1\" : \"0.5\");\n    i0.ɵɵproperty(\"disabled\", !ctx_r2.canSelectMore());\n    i0.ɵɵadvance(2);\n    i0.ɵɵstyleProp(\"opacity\", ctx_r2.canSelectMore() ? \"1\" : \"0.5\");\n    i0.ɵɵproperty(\"disabled\", !ctx_r2.canSelectMore());\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \\u5168\\u9078\", ctx_r2.selectedBuilding, \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isSomeBuildingSelected());\n  }\n}\nfunction HouseholdBindingComponent_ng_template_8_div_13_div_15_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 86);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \\u627E\\u4E0D\\u5230\\u7B26\\u5408 \\\"\", ctx_r2.searchTerm, \"\\\" \\u7684\\u6236\\u5225 \");\n  }\n}\nfunction HouseholdBindingComponent_ng_template_8_div_13_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 81)(1, \"div\", 82)(2, \"input\", 83);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdBindingComponent_ng_template_8_div_13_div_15_Template_input_ngModelChange_2_listener($event) {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      i0.ɵɵtwoWayBindingSet(ctx_r2.searchTerm, $event) || (ctx_r2.searchTerm = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"input\", function HouseholdBindingComponent_ng_template_8_div_13_div_15_Template_input_input_2_listener($event) {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r2.onSearchChange($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(3, \"nb-icon\", 84);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, HouseholdBindingComponent_ng_template_8_div_13_div_15_div_4_Template, 2, 1, \"div\", 85);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.searchTerm);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.searchTerm && ctx_r2.hasNoSearchResults());\n  }\n}\nfunction HouseholdBindingComponent_ng_template_8_div_13_div_16_button_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r13 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 94);\n    i0.ɵɵlistener(\"click\", function HouseholdBindingComponent_ng_template_8_div_13_div_16_button_5_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r2 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r2.onFloorSelect(\"\"));\n    });\n    i0.ɵɵtext(1, \" \\u6E05\\u9664\\u7BE9\\u9078 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction HouseholdBindingComponent_ng_template_8_div_13_div_16_button_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r14 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 95);\n    i0.ɵɵlistener(\"click\", function HouseholdBindingComponent_ng_template_8_div_13_div_16_button_7_Template_button_click_0_listener() {\n      const floor_r15 = i0.ɵɵrestoreView(_r14).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r2.onFloorSelect(floor_r15));\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const floor_r15 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(4);\n    i0.ɵɵstyleProp(\"background-color\", ctx_r2.selectedFloor === floor_r15 ? \"#007bff\" : \"#f8f9fa\")(\"color\", ctx_r2.selectedFloor === floor_r15 ? \"#fff\" : \"#495057\")(\"border-color\", ctx_r2.selectedFloor === floor_r15 ? \"#007bff\" : \"#ced4da\");\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate2(\" \", floor_r15, \" (\", ctx_r2.getFloorCount(floor_r15), \") \");\n  }\n}\nfunction HouseholdBindingComponent_ng_template_8_div_13_div_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 87)(1, \"div\", 88);\n    i0.ɵɵelement(2, \"nb-icon\", 89);\n    i0.ɵɵelementStart(3, \"span\", 90);\n    i0.ɵɵtext(4, \"\\u6A13\\u5C64\\u7BE9\\u9078:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(5, HouseholdBindingComponent_ng_template_8_div_13_div_16_button_5_Template, 2, 0, \"button\", 91);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 92);\n    i0.ɵɵtemplate(7, HouseholdBindingComponent_ng_template_8_div_13_div_16_button_7_Template, 2, 8, \"button\", 93);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.selectedFloor);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.floors);\n  }\n}\nfunction HouseholdBindingComponent_ng_template_8_div_13_div_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 96);\n    i0.ɵɵelement(1, \"nb-icon\", 97);\n    i0.ɵɵelementStart(2, \"p\", 54);\n    i0.ɵɵtext(3, \"\\u8ACB\\u5148\\u9078\\u64C7\\u68DF\\u5225\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction HouseholdBindingComponent_ng_template_8_div_13_div_19_ng_container_1_button_1_span_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 105);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const household_r17 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", household_r17.floor, \" \");\n  }\n}\nfunction HouseholdBindingComponent_ng_template_8_div_13_div_19_ng_container_1_button_1_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 106);\n    i0.ɵɵtext(1, \" \\u2715 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction HouseholdBindingComponent_ng_template_8_div_13_div_19_ng_container_1_button_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r16 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 101);\n    i0.ɵɵlistener(\"click\", function HouseholdBindingComponent_ng_template_8_div_13_div_19_ng_container_1_button_1_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r16);\n      const household_r17 = i0.ɵɵnextContext().$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r2.onHouseholdToggle(household_r17.houseName));\n    });\n    i0.ɵɵelementStart(1, \"span\", 102);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, HouseholdBindingComponent_ng_template_8_div_13_div_19_ng_container_1_button_1_span_3_Template, 2, 1, \"span\", 103)(4, HouseholdBindingComponent_ng_template_8_div_13_div_19_ng_container_1_button_1_div_4_Template, 2, 0, \"div\", 104);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const household_r17 = i0.ɵɵnextContext().$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(4);\n    i0.ɵɵstyleProp(\"background-color\", ctx_r2.isHouseholdSelected(household_r17.houseName) ? \"#007bff\" : ctx_r2.isHouseholdExcluded(household_r17.houseName) ? \"#f8f9fa\" : \"#fff\")(\"color\", ctx_r2.isHouseholdSelected(household_r17.houseName) ? \"#fff\" : ctx_r2.isHouseholdExcluded(household_r17.houseName) ? \"#6c757d\" : \"#495057\")(\"border-color\", ctx_r2.isHouseholdSelected(household_r17.houseName) ? \"#007bff\" : ctx_r2.isHouseholdExcluded(household_r17.houseName) ? \"#dee2e6\" : \"#ced4da\")(\"opacity\", ctx_r2.isHouseholdDisabled(household_r17.houseName) ? \"0.6\" : \"1\")(\"cursor\", ctx_r2.isHouseholdDisabled(household_r17.houseName) ? \"not-allowed\" : \"pointer\");\n    i0.ɵɵproperty(\"disabled\", ctx_r2.isHouseholdDisabled(household_r17.houseName));\n    i0.ɵɵadvance();\n    i0.ɵɵstyleProp(\"text-decoration\", ctx_r2.isHouseholdExcluded(household_r17.houseName) ? \"line-through\" : \"none\");\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", household_r17.houseName, \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", household_r17.floor);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isHouseholdExcluded(household_r17.houseName));\n  }\n}\nfunction HouseholdBindingComponent_ng_template_8_div_13_div_19_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, HouseholdBindingComponent_ng_template_8_div_13_div_19_ng_container_1_button_1_Template, 5, 16, \"button\", 100);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const household_r17 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (!ctx_r2.selectedFloor || household_r17.floor === ctx_r2.selectedFloor) && (!ctx_r2.searchTerm || household_r17.houseName.toLowerCase().includes(ctx_r2.searchTerm.toLowerCase())));\n  }\n}\nfunction HouseholdBindingComponent_ng_template_8_div_13_div_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 98);\n    i0.ɵɵtemplate(1, HouseholdBindingComponent_ng_template_8_div_13_div_19_ng_container_1_Template, 2, 1, \"ng-container\", 99);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.buildingData[ctx_r2.selectedBuilding]);\n  }\n}\nfunction HouseholdBindingComponent_ng_template_8_div_13_div_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 96);\n    i0.ɵɵelement(1, \"nb-icon\", 107);\n    i0.ɵɵelementStart(2, \"p\", 54);\n    i0.ɵɵtext(3, \"\\u6B64\\u68DF\\u5225\\u6C92\\u6709\\u53EF\\u7528\\u7684\\u6236\\u5225\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction HouseholdBindingComponent_ng_template_8_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 55)(1, \"div\", 56)(2, \"div\", 57)(3, \"h6\", 58);\n    i0.ɵɵtext(4, \"\\u68DF\\u5225\\u5217\\u8868\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"div\", 59);\n    i0.ɵɵtemplate(6, HouseholdBindingComponent_ng_template_8_div_13_button_6_Template, 5, 6, \"button\", 60);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 61)(8, \"div\", 57)(9, \"div\", 62)(10, \"h6\", 58);\n    i0.ɵɵtext(11, \" \\u6236\\u5225\\u9078\\u64C7 \");\n    i0.ɵɵtemplate(12, HouseholdBindingComponent_ng_template_8_div_13_span_12_Template, 2, 1, \"span\", 63)(13, HouseholdBindingComponent_ng_template_8_div_13_span_13_Template, 2, 1, \"span\", 64);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(14, HouseholdBindingComponent_ng_template_8_div_13_div_14_Template, 6, 8, \"div\", 65);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(15, HouseholdBindingComponent_ng_template_8_div_13_div_15_Template, 5, 2, \"div\", 66)(16, HouseholdBindingComponent_ng_template_8_div_13_div_16_Template, 8, 2, \"div\", 67);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"div\", 68);\n    i0.ɵɵtemplate(18, HouseholdBindingComponent_ng_template_8_div_13_div_18_Template, 4, 0, \"div\", 69)(19, HouseholdBindingComponent_ng_template_8_div_13_div_19_Template, 2, 1, \"div\", 70)(20, HouseholdBindingComponent_ng_template_8_div_13_div_20_Template, 4, 0, \"div\", 69);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.buildings);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.selectedBuilding);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.selectedFloor);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.allowBatchSelect && ctx_r2.selectedBuilding && ctx_r2.buildingData[ctx_r2.selectedBuilding] && ctx_r2.buildingData[ctx_r2.selectedBuilding].length > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.allowSearch && ctx_r2.selectedBuilding);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.selectedBuilding && ctx_r2.floors.length > 1);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.selectedBuilding);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.selectedBuilding && ctx_r2.buildingData[ctx_r2.selectedBuilding] && ctx_r2.buildingData[ctx_r2.selectedBuilding].length > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.selectedBuilding && (!ctx_r2.buildingData[ctx_r2.selectedBuilding] || ctx_r2.buildingData[ctx_r2.selectedBuilding].length === 0) && !ctx_r2.searchTerm);\n  }\n}\nfunction HouseholdBindingComponent_ng_template_8_div_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 40);\n    i0.ɵɵelement(1, \"nb-icon\", 108);\n    i0.ɵɵelementStart(2, \"span\");\n    i0.ɵɵtext(3, \"\\u9650\\u5236: \\u6700\\u591A \");\n    i0.ɵɵelementStart(4, \"strong\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(6, \" \\u500B\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r2.maxSelections);\n  }\n}\nfunction HouseholdBindingComponent_ng_template_8_div_25_span_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 111);\n    i0.ɵɵelement(1, \"nb-icon\", 112);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.selectedFloor, \" \");\n  }\n}\nfunction HouseholdBindingComponent_ng_template_8_div_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 40);\n    i0.ɵɵelement(1, \"nb-icon\", 109);\n    i0.ɵɵelementStart(2, \"span\");\n    i0.ɵɵtext(3, \"\\u7576\\u524D\\u68DF\\u5225: \");\n    i0.ɵɵelementStart(4, \"strong\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(6, HouseholdBindingComponent_ng_template_8_div_25_span_6_Template, 3, 1, \"span\", 110);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r2.selectedBuilding);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.selectedFloor);\n  }\n}\nfunction HouseholdBindingComponent_ng_template_8_div_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 113);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate2(\" \\u641C\\u5C0B: \\\"\", ctx_r2.searchTerm, \"\\\" (\", ctx_r2.getFilteredHouseholdsCount(), \" \\u500B\\u7D50\\u679C) \");\n  }\n}\nfunction HouseholdBindingComponent_ng_template_8_button_29_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r18 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 114);\n    i0.ɵɵlistener(\"click\", function HouseholdBindingComponent_ng_template_8_button_29_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r18);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.onClearAll());\n    });\n    i0.ɵɵelement(1, \"nb-icon\", 115);\n    i0.ɵɵtext(2, \" \\u6E05\\u7A7A\\u5168\\u90E8 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction HouseholdBindingComponent_ng_template_8_button_30_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r19 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 116);\n    i0.ɵɵlistener(\"click\", function HouseholdBindingComponent_ng_template_8_button_30_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.resetSearch());\n    });\n    i0.ɵɵelement(1, \"nb-icon\", 117);\n    i0.ɵɵtext(2, \" \\u91CD\\u7F6E\\u641C\\u5C0B \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction HouseholdBindingComponent_ng_template_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-card\", 28)(1, \"nb-card-header\")(2, \"div\", 29)(3, \"div\", 30);\n    i0.ɵɵelement(4, \"nb-icon\", 31);\n    i0.ɵɵelementStart(5, \"span\", 32);\n    i0.ɵɵtext(6, \"\\u9078\\u64C7\\u6236\\u5225\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"span\", 33);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"span\", 33);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(11, \"nb-card-body\", 34);\n    i0.ɵɵtemplate(12, HouseholdBindingComponent_ng_template_8_div_12_Template, 5, 0, \"div\", 35)(13, HouseholdBindingComponent_ng_template_8_div_13_Template, 21, 9, \"div\", 36);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"nb-card-footer\", 37)(15, \"div\", 38)(16, \"div\", 39)(17, \"div\", 40);\n    i0.ɵɵelement(18, \"nb-icon\", 41);\n    i0.ɵɵelementStart(19, \"span\");\n    i0.ɵɵtext(20, \"\\u5DF2\\u9078\\u64C7: \");\n    i0.ɵɵelementStart(21, \"strong\");\n    i0.ɵɵtext(22);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(23, \" \\u500B\\u6236\\u5225\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(24, HouseholdBindingComponent_ng_template_8_div_24_Template, 7, 1, \"div\", 42)(25, HouseholdBindingComponent_ng_template_8_div_25_Template, 7, 2, \"div\", 42);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(26, HouseholdBindingComponent_ng_template_8_div_26_Template, 2, 2, \"div\", 43);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(27, \"div\", 44)(28, \"div\", 45);\n    i0.ɵɵtemplate(29, HouseholdBindingComponent_ng_template_8_button_29_Template, 3, 0, \"button\", 46)(30, HouseholdBindingComponent_ng_template_8_button_30_Template, 3, 0, \"button\", 47);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(31, \"div\", 45)(32, \"button\", 48);\n    i0.ɵɵlistener(\"click\", function HouseholdBindingComponent_ng_template_8_Template_button_click_32_listener() {\n      const ref_r20 = i0.ɵɵrestoreView(_r7).dialogRef;\n      return i0.ɵɵresetView(ref_r20.close());\n    });\n    i0.ɵɵtext(33, \" \\u53D6\\u6D88 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(34, \"button\", 49);\n    i0.ɵɵlistener(\"click\", function HouseholdBindingComponent_ng_template_8_Template_button_click_34_listener() {\n      const ref_r20 = i0.ɵɵrestoreView(_r7).dialogRef;\n      return i0.ɵɵresetView(ref_r20.close());\n    });\n    i0.ɵɵelement(35, \"nb-icon\", 50);\n    i0.ɵɵtext(36);\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate1(\"(\", ctx_r2.buildings.length, \" \\u500B\\u68DF\\u5225)\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\\u5DF2\\u9078\\u64C7: \", ctx_r2.getSelectedCount(), \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isLoading);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.isLoading);\n    i0.ɵɵadvance(9);\n    i0.ɵɵtextInterpolate(ctx_r2.getSelectedCount());\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.maxSelections);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.selectedBuilding);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.searchTerm);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.selectedHouseholds.length > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.allowSearch && ctx_r2.searchTerm);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\" \\u78BA\\u5B9A\\u9078\\u64C7 (\", ctx_r2.getSelectedCount(), \") \");\n  }\n}\nexport class HouseholdBindingComponent {\n  constructor(cdr, houseCustomService, dialogService) {\n    this.cdr = cdr;\n    this.houseCustomService = houseCustomService;\n    this.dialogService = dialogService;\n    this.placeholder = '請選擇戶別';\n    this.maxSelections = null;\n    this.disabled = false;\n    this.buildCaseId = null; // 新增：建案ID\n    this.buildingData = {};\n    this.showSelectedArea = true;\n    this.allowSearch = true;\n    this.allowBatchSelect = true;\n    this.excludedHouseIds = []; // 改為：排除的戶別ID（已被其他元件選擇）\n    this.selectionChange = new EventEmitter();\n    this.houseIdChange = new EventEmitter(); // 新增：回傳 houseId 陣列\n    this.isOpen = false;\n    this.selectedBuilding = '';\n    this.searchTerm = '';\n    this.selectedFloor = ''; // 新增：選中的樓層\n    this.selectedHouseIds = []; // 改為：使用 houseId 作為選擇的key\n    this.buildings = [];\n    this.floors = []; // 新增：當前棟別的樓層列表\n    this.filteredHouseholds = []; // 保持為字串陣列用於UI顯示\n    this.selectedByBuilding = {}; // 改為：儲存 houseId\n    this.isLoading = false; // 新增：載入狀態\n    // ControlValueAccessor implementation\n    this.onChange = value => {};\n    this.onTouched = () => {};\n  }\n  writeValue(value) {\n    this.selectedHouseholds = value || [];\n    this.updateSelectedByBuilding();\n  }\n  registerOnChange(fn) {\n    this.onChange = fn;\n  }\n  registerOnTouched(fn) {\n    this.onTouched = fn;\n  }\n  setDisabledState(isDisabled) {\n    this.disabled = isDisabled;\n  }\n  ngOnInit() {\n    this.initializeData();\n  }\n  ngOnChanges(changes) {\n    if (changes['buildCaseId'] && this.buildCaseId) {\n      // 當建案ID變更時，重新載入資料\n      this.initializeData();\n    }\n    if (changes['buildingData']) {\n      // 當 buildingData 變更時，重新初始化\n      this.buildings = Object.keys(this.buildingData || {});\n      console.log('buildingData updated:', this.buildingData);\n      this.updateFilteredHouseholds();\n      this.updateSelectedByBuilding();\n    }\n    if (changes['excludedHouseholds']) {\n      // 當排除列表變化時，重新更新顯示\n      this.updateFilteredHouseholds();\n      console.log('Excluded households updated:', this.excludedHouseholds);\n    }\n  }\n  initializeData() {\n    // 優先檢查是否有傳入 buildingData\n    if (this.buildingData && Object.keys(this.buildingData).length > 0) {\n      // 使用傳入的 buildingData\n      this.buildings = Object.keys(this.buildingData);\n      console.log('Component initialized with provided buildingData:', this.buildings);\n      this.updateSelectedByBuilding();\n    } else if (this.buildCaseId) {\n      // 只有在沒有傳入 buildingData 且有建案ID時才呼叫API\n      this.loadBuildingDataFromApi();\n    } else {\n      // 既沒有 buildingData 也沒有 buildCaseId，保持空狀態\n      this.buildings = [];\n      console.log('No buildingData or buildCaseId provided');\n    }\n  }\n  loadBuildingDataFromApi() {\n    if (!this.buildCaseId) return;\n    this.isLoading = true;\n    this.houseCustomService.getDropDown(this.buildCaseId).subscribe({\n      next: response => {\n        console.log('API response:', response);\n        this.buildingData = this.convertApiResponseToBuildingData(response.Entries);\n        this.buildings = Object.keys(this.buildingData);\n        this.updateSelectedByBuilding();\n        this.isLoading = false;\n        this.cdr.detectChanges();\n      },\n      error: error => {\n        console.error('Error loading building data:', error);\n        // API載入失敗時，不使用備援資料，保持空狀態\n        this.buildingData = {};\n        this.buildings = [];\n        this.isLoading = false;\n        this.cdr.detectChanges();\n      }\n    });\n  }\n  convertApiResponseToBuildingData(entries) {\n    const buildingData = {};\n    Object.entries(entries).forEach(([building, houses]) => {\n      buildingData[building] = houses.map(house => ({\n        houseName: house.houseName || house.HouseName || house.code,\n        building: house.building || house.Building || building,\n        floor: house.floor || house.Floor,\n        houseId: house.houseId || house.HouseId,\n        isSelected: false,\n        isDisabled: false\n      }));\n    });\n    return buildingData;\n  }\n  updateSelectedByBuilding() {\n    const grouped = {};\n    this.selectedHouseholds.forEach(houseName => {\n      for (const building of this.buildings) {\n        const item = this.buildingData[building]?.find(h => h.houseName === houseName);\n        if (item) {\n          if (!grouped[building]) grouped[building] = [];\n          grouped[building].push(houseName);\n          break;\n        }\n      }\n    });\n    this.selectedByBuilding = grouped;\n  }\n  onBuildingSelect(building) {\n    console.log('Building selected:', building);\n    this.selectedBuilding = building;\n    this.selectedFloor = ''; // 重置樓層選擇\n    this.searchTerm = '';\n    this.updateFloorsForBuilding(); // 更新樓層列表\n    this.updateFilteredHouseholds();\n    console.log('Filtered households count:', this.filteredHouseholds.length);\n    // 手動觸發變更偵測\n    this.cdr.detectChanges();\n  }\n  onBuildingClick(building) {\n    console.log('Building clicked (mousedown):', building);\n  }\n  updateFilteredHouseholds() {\n    console.log('Updating filtered households for building:', this.selectedBuilding, 'floor:', this.selectedFloor);\n    if (!this.selectedBuilding) {\n      this.filteredHouseholds = [];\n      return;\n    }\n    const households = this.buildingData[this.selectedBuilding] || [];\n    console.log('Available households for building:', households.length);\n    // 提取戶別代碼並進行搜尋和樓層過濾\n    this.filteredHouseholds = households.filter(h => {\n      // 樓層篩選：如果有選擇樓層，只顯示該樓層的戶別\n      const floorMatch = !this.selectedFloor || h.floor === this.selectedFloor;\n      // 搜尋篩選：戶別代碼包含搜尋詞\n      const searchMatch = h.houseName.toLowerCase().includes(this.searchTerm.toLowerCase());\n      return floorMatch && searchMatch;\n    }).map(h => h.houseName);\n    console.log('Filtered households result:', this.filteredHouseholds.length);\n  }\n  onSearchChange(event) {\n    this.searchTerm = event.target.value;\n    this.updateFilteredHouseholds();\n    console.log('Search term changed:', this.searchTerm);\n  }\n  resetSearch() {\n    this.searchTerm = '';\n    this.updateFilteredHouseholds();\n    console.log('Search reset');\n  }\n  onHouseholdToggle(householdCode) {\n    // 防止選擇已排除的戶別\n    if (this.isHouseholdExcluded(householdCode)) {\n      console.log(`戶別 ${householdCode} 已被其他元件選擇，無法重複選擇`);\n      return;\n    }\n    const isSelected = this.selectedHouseholds.includes(householdCode);\n    let newSelection;\n    if (isSelected) {\n      newSelection = this.selectedHouseholds.filter(h => h !== householdCode);\n    } else {\n      if (this.maxSelections && this.selectedHouseholds.length >= this.maxSelections) {\n        console.log('已達到最大選擇數量');\n        return; // 達到最大選擇數量\n      }\n      newSelection = [...this.selectedHouseholds, householdCode];\n    }\n    this.selectedHouseholds = newSelection;\n    this.emitChanges();\n  }\n  onRemoveHousehold(householdCode) {\n    this.selectedHouseholds = this.selectedHouseholds.filter(h => h !== householdCode);\n    this.emitChanges();\n  }\n  onSelectAllFiltered() {\n    if (!this.selectedBuilding || this.filteredHouseholds.length === 0) return;\n    // 計算可以新增的戶別數量\n    const currentCount = this.selectedHouseholds.length;\n    const maxAllowed = this.maxSelections || Infinity;\n    const remainingSlots = maxAllowed - currentCount; // 取得尚未選擇且未被排除的過濾戶別\n    const unselectedFiltered = this.filteredHouseholds.filter(code => !this.selectedHouseholds.includes(code) && !this.isHouseholdExcluded(code));\n    // 根據剩餘空間決定要新增的戶別\n    const toAdd = unselectedFiltered.slice(0, remainingSlots);\n    if (toAdd.length > 0) {\n      this.selectedHouseholds = [...this.selectedHouseholds, ...toAdd];\n      this.emitChanges();\n      console.log(`全選當前: 新增了 ${toAdd.length} 個戶別`);\n    }\n  }\n  onSelectAllBuilding() {\n    if (!this.selectedBuilding) return;\n    // 取得當前棟別的所有戶別\n    const buildingHouseholds = this.buildingData[this.selectedBuilding]?.map(h => h.houseName) || [];\n    // 計算可以新增的戶別數量\n    const currentCount = this.selectedHouseholds.length;\n    const maxAllowed = this.maxSelections || Infinity;\n    const remainingSlots = maxAllowed - currentCount; // 取得尚未選擇且未被排除的棟別戶別\n    const unselectedBuilding = buildingHouseholds.filter(code => !this.selectedHouseholds.includes(code) && !this.isHouseholdExcluded(code));\n    // 根據剩餘空間決定要新增的戶別\n    const toAdd = unselectedBuilding.slice(0, remainingSlots);\n    if (toAdd.length > 0) {\n      this.selectedHouseholds = [...this.selectedHouseholds, ...toAdd];\n      this.emitChanges();\n      console.log(`全選棟別: 新增了 ${toAdd.length} 個戶別`);\n    }\n  }\n  onUnselectAllBuilding() {\n    if (!this.selectedBuilding) return;\n    const buildingHouseholds = this.buildingData[this.selectedBuilding]?.map(h => h.houseName) || [];\n    this.selectedHouseholds = this.selectedHouseholds.filter(h => !buildingHouseholds.includes(h));\n    this.emitChanges();\n  }\n  onClearAll() {\n    this.selectedHouseholds = [];\n    this.emitChanges();\n  }\n  emitChanges() {\n    this.updateSelectedByBuilding();\n    this.onChange([...this.selectedHouseholds]);\n    this.onTouched();\n    const selectedItems = this.selectedHouseholds.map(houseName => {\n      for (const building of this.buildings) {\n        const item = this.buildingData[building]?.find(h => h.houseName === houseName);\n        if (item) return item;\n      }\n      return null;\n    }).filter(item => item !== null);\n    this.selectionChange.emit(selectedItems);\n    // 新增：回傳 houseId 陣列\n    const houseIds = selectedItems.map(item => item.houseId).filter(id => id !== undefined);\n    this.houseIdChange.emit(houseIds);\n  }\n  toggleDropdown() {\n    if (!this.disabled) {\n      this.openDialog();\n      console.log('Opening household selection dialog');\n      console.log('Buildings available:', this.buildings);\n    }\n  }\n  openDialog() {\n    this.dialogService.open(this.householdDialog, {\n      context: {},\n      closeOnBackdropClick: false,\n      closeOnEsc: true,\n      autoFocus: false\n    });\n  }\n  closeDropdown() {\n    // 這個方法現在用於關閉對話框\n    // 對話框的關閉將由 NbDialogRef 處理\n  }\n  isHouseholdSelected(householdCode) {\n    return this.selectedHouseholds.includes(householdCode);\n  }\n  isHouseholdExcluded(householdCode) {\n    return this.excludedHouseholds.includes(householdCode);\n  }\n  isHouseholdDisabled(householdCode) {\n    return this.isHouseholdExcluded(householdCode) || !this.canSelectMore() && !this.isHouseholdSelected(householdCode);\n  }\n  canSelectMore() {\n    return !this.maxSelections || this.selectedHouseholds.length < this.maxSelections;\n  }\n  isAllBuildingSelected() {\n    if (!this.selectedBuilding) return false;\n    const buildingHouseholds = this.buildingData[this.selectedBuilding].filter(h => !h.isDisabled).map(h => h.houseName);\n    return buildingHouseholds.length > 0 && buildingHouseholds.every(houseName => this.selectedHouseholds.includes(houseName));\n  }\n  isSomeBuildingSelected() {\n    if (!this.selectedBuilding) return false;\n    const buildingHouseholds = this.buildingData[this.selectedBuilding]?.map(h => h.houseName) || [];\n    return buildingHouseholds.some(houseName => this.selectedHouseholds.includes(houseName));\n  }\n  getSelectedByBuilding() {\n    return this.selectedByBuilding;\n  }\n  getBuildingCount(building) {\n    return this.buildingData[building]?.length || 0;\n  }\n  getSelectedCount() {\n    return this.selectedHouseholds.length;\n  }\n  // 輔助方法：安全地獲取建築物的已選戶別\n  getBuildingSelectedHouseholds(building) {\n    return this.selectedByBuilding[building] || [];\n  }\n  // 輔助方法：檢查建築物是否有已選戶別\n  hasBuildingSelected(building) {\n    return !!(this.selectedByBuilding[building] && this.selectedByBuilding[building].length > 0);\n  }\n  // 新增：更新當前棟別的樓層列表\n  updateFloorsForBuilding() {\n    if (!this.selectedBuilding) {\n      this.floors = [];\n      return;\n    }\n    const households = this.buildingData[this.selectedBuilding] || [];\n    const floorSet = new Set();\n    households.forEach(household => {\n      if (household.floor) {\n        floorSet.add(household.floor);\n      }\n    });\n    // 對樓層進行自然排序\n    this.floors = Array.from(floorSet).sort((a, b) => {\n      const numA = parseInt(a.replace(/[^0-9]/g, ''));\n      const numB = parseInt(b.replace(/[^0-9]/g, ''));\n      return numA - numB;\n    });\n    console.log('Updated floors for building:', this.selectedBuilding, this.floors);\n  }\n  // 新增：樓層選擇處理\n  onFloorSelect(floor) {\n    console.log('Floor selected:', floor);\n    this.selectedFloor = this.selectedFloor === floor ? '' : floor; // 切換選擇\n    this.updateFilteredHouseholds();\n    this.cdr.detectChanges();\n  }\n  // 新增：取得當前棧別的樓層計數\n  getFloorCount(floor) {\n    if (!this.selectedBuilding) return 0;\n    const households = this.buildingData[this.selectedBuilding] || [];\n    return households.filter(h => h.floor === floor).length;\n  }\n  // 新增：取得戶別的樓層資訊\n  getHouseholdFloor(householdCode) {\n    if (!this.selectedBuilding) return '';\n    const households = this.buildingData[this.selectedBuilding] || [];\n    const household = households.find(h => h.houseName === householdCode);\n    return household?.floor || '';\n  }\n  // 新增：取得戶別的完整資訊（包含樓層）\n  getHouseholdInfo(householdCode) {\n    for (const building of this.buildings) {\n      const households = this.buildingData[building] || [];\n      const household = households.find(h => h.houseName === householdCode);\n      if (household) {\n        return {\n          houseName: household.houseName,\n          floor: household.floor || ''\n        };\n      }\n    }\n    return {\n      houseName: householdCode,\n      floor: ''\n    };\n  }\n  // 新增：檢查搜尋是否有結果\n  hasNoSearchResults() {\n    if (!this.searchTerm || !this.selectedBuilding || !this.buildingData[this.selectedBuilding]) {\n      return false;\n    }\n    const filtered = this.buildingData[this.selectedBuilding].filter(h => {\n      const floorMatch = !this.selectedFloor || h.floor === this.selectedFloor;\n      const searchMatch = h.houseName.toLowerCase().includes(this.searchTerm.toLowerCase());\n      return floorMatch && searchMatch;\n    });\n    return filtered.length === 0;\n  }\n  // 新增：取得過濾後的戶別數量\n  getFilteredHouseholdsCount() {\n    if (!this.selectedBuilding || !this.buildingData[this.selectedBuilding]) {\n      return 0;\n    }\n    const filtered = this.buildingData[this.selectedBuilding].filter(h => {\n      const floorMatch = !this.selectedFloor || h.floor === this.selectedFloor;\n      const searchMatch = !this.searchTerm || h.houseName.toLowerCase().includes(this.searchTerm.toLowerCase());\n      return floorMatch && searchMatch;\n    });\n    return filtered.length;\n  }\n  // 新增：產生戶別的唯一識別符\n  getHouseholdUniqueId(household) {\n    return household.houseId ? household.houseId.toString() : `${household.houseName}_${household.floor}`;\n  }\n  // 新增：從唯一識別符獲取戶別物件\n  getHouseholdFromUniqueId(uniqueId) {\n    for (const building of this.buildings) {\n      const households = this.buildingData[building] || [];\n      const household = households.find(h => this.getHouseholdUniqueId(h) === uniqueId);\n      if (household) return household;\n    }\n    return null;\n  }\n  static {\n    this.ɵfac = function HouseholdBindingComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || HouseholdBindingComponent)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i1.HouseCustomService), i0.ɵɵdirectiveInject(i2.NbDialogService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: HouseholdBindingComponent,\n      selectors: [[\"app-household-binding\"]],\n      viewQuery: function HouseholdBindingComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.householdDialog = _t.first);\n        }\n      },\n      inputs: {\n        placeholder: \"placeholder\",\n        maxSelections: \"maxSelections\",\n        disabled: \"disabled\",\n        buildCaseId: \"buildCaseId\",\n        buildingData: \"buildingData\",\n        showSelectedArea: \"showSelectedArea\",\n        allowSearch: \"allowSearch\",\n        allowBatchSelect: \"allowBatchSelect\",\n        excludedHouseIds: \"excludedHouseIds\"\n      },\n      outputs: {\n        selectionChange: \"selectionChange\",\n        houseIdChange: \"houseIdChange\"\n      },\n      features: [i0.ɵɵProvidersFeature([{\n        provide: NG_VALUE_ACCESSOR,\n        useExisting: forwardRef(() => HouseholdBindingComponent),\n        multi: true\n      }]), i0.ɵɵNgOnChangesFeature],\n      decls: 10,\n      vars: 6,\n      consts: [[\"householdDialog\", \"\"], [1, \"household-binding-container\"], [\"class\", \"selected-households-area\", 4, \"ngIf\"], [1, \"selector-container\"], [\"type\", \"button\", 1, \"selector-button\", 2, \"width\", \"100%\", \"display\", \"flex\", \"align-items\", \"center\", \"justify-content\", \"space-between\", \"padding\", \"0.5rem 0.75rem\", \"border\", \"1px solid #ced4da\", \"border-radius\", \"0.375rem\", \"background-color\", \"#fff\", \"cursor\", \"pointer\", 3, \"click\", \"disabled\"], [1, \"selector-text\"], [4, \"ngIf\"], [\"icon\", \"home-outline\", 1, \"chevron-icon\"], [1, \"selected-households-area\"], [1, \"selected-header\"], [1, \"selected-info\"], [\"icon\", \"people-outline\", 1, \"text-primary\"], [1, \"selected-count\"], [\"type\", \"button\", 1, \"btn\", \"btn-outline-danger\", \"btn-sm\", 3, \"click\", \"disabled\"], [1, \"selected-content\"], [\"class\", \"building-group\", 4, \"ngFor\", \"ngForOf\"], [1, \"building-group\"], [1, \"building-label\"], [1, \"households-tags\"], [\"class\", \"household-tag\", 4, \"ngFor\", \"ngForOf\"], [1, \"household-tag\"], [1, \"household-info\"], [1, \"household-code\"], [\"class\", \"household-floor\", 4, \"ngIf\"], [\"type\", \"button\", 1, \"remove-btn\", 3, \"click\", \"disabled\"], [\"icon\", \"close-outline\"], [1, \"household-floor\"], [\"icon\", \"loader-outline\", 1, \"spin\"], [2, \"width\", \"95vw\", \"max-width\", \"1200px\", \"max-height\", \"90vh\"], [2, \"display\", \"flex\", \"align-items\", \"center\", \"justify-content\", \"space-between\"], [2, \"display\", \"flex\", \"align-items\", \"center\", \"gap\", \"8px\"], [\"icon\", \"home-outline\", 2, \"color\", \"#007bff\", \"font-size\", \"1.5rem\"], [2, \"font-weight\", \"500\", \"color\", \"#495057\", \"font-size\", \"1.25rem\"], [2, \"font-size\", \"0.875rem\", \"color\", \"#6c757d\"], [2, \"padding\", \"0\", \"overflow\", \"hidden\"], [\"style\", \"width: 100%; display: flex; align-items: center; justify-content: center; padding: 40px;\", 4, \"ngIf\"], [\"style\", \"display: flex; height: 60vh; min-height: 400px;\", 4, \"ngIf\"], [2, \"padding\", \"16px\", \"border-top\", \"1px solid #e9ecef\", \"background-color\", \"#f8f9fa\"], [2, \"display\", \"flex\", \"align-items\", \"center\", \"justify-content\", \"space-between\", \"margin-bottom\", \"12px\"], [2, \"display\", \"flex\", \"align-items\", \"center\", \"gap\", \"16px\", \"font-size\", \"0.875rem\", \"color\", \"#495057\"], [2, \"display\", \"flex\", \"align-items\", \"center\", \"gap\", \"4px\"], [\"icon\", \"checkmark-circle-outline\", 2, \"color\", \"#28a745\"], [\"style\", \"display: flex; align-items: center; gap: 4px;\", 4, \"ngIf\"], [\"style\", \"font-size: 0.75rem; color: #6c757d; background-color: #e9ecef; padding: 4px 8px; border-radius: 12px;\", 4, \"ngIf\"], [2, \"display\", \"flex\", \"align-items\", \"center\", \"justify-content\", \"space-between\", \"gap\", \"8px\"], [2, \"display\", \"flex\", \"gap\", \"8px\"], [\"type\", \"button\", \"style\", \"padding: 8px 12px; background: #dc3545; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 0.875rem; display: flex; align-items: center; gap: 4px;\", 3, \"click\", 4, \"ngIf\"], [\"type\", \"button\", \"style\", \"padding: 8px 12px; background: #6c757d; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 0.875rem; display: flex; align-items: center; gap: 4px;\", 3, \"click\", 4, \"ngIf\"], [\"type\", \"button\", 2, \"padding\", \"8px 16px\", \"background\", \"#6c757d\", \"color\", \"white\", \"border\", \"none\", \"border-radius\", \"4px\", \"cursor\", \"pointer\", \"font-size\", \"0.875rem\", 3, \"click\"], [\"type\", \"button\", 2, \"padding\", \"8px 20px\", \"background\", \"#007bff\", \"color\", \"white\", \"border\", \"none\", \"border-radius\", \"4px\", \"cursor\", \"pointer\", \"font-size\", \"0.875rem\", \"font-weight\", \"500\", \"display\", \"flex\", \"align-items\", \"center\", \"gap\", \"4px\", 3, \"click\"], [\"icon\", \"checkmark-outline\"], [2, \"width\", \"100%\", \"display\", \"flex\", \"align-items\", \"center\", \"justify-content\", \"center\", \"padding\", \"40px\"], [2, \"text-align\", \"center\", \"color\", \"#6c757d\"], [\"icon\", \"loader-outline\", 1, \"spin\", 2, \"font-size\", \"2rem\", \"margin-bottom\", \"8px\"], [2, \"margin\", \"0\", \"font-size\", \"0.875rem\"], [2, \"display\", \"flex\", \"height\", \"60vh\", \"min-height\", \"400px\"], [2, \"width\", \"300px\", \"border-right\", \"1px solid #e9ecef\", \"background-color\", \"#f8f9fa\", \"display\", \"flex\", \"flex-direction\", \"column\"], [2, \"padding\", \"12px 16px\", \"border-bottom\", \"1px solid #e9ecef\"], [2, \"margin\", \"0\", \"font-size\", \"0.875rem\", \"font-weight\", \"500\", \"color\", \"#495057\"], [2, \"flex\", \"1\", \"overflow-y\", \"auto\"], [\"type\", \"button\", \"style\", \"width: 100%; text-align: left; padding: 12px 16px; border: none; cursor: pointer; transition: all 0.15s ease; display: flex; align-items: center; justify-content: space-between;\", 3, \"background-color\", \"border-left\", \"click\", 4, \"ngFor\", \"ngForOf\"], [2, \"flex\", \"1\", \"display\", \"flex\", \"flex-direction\", \"column\"], [2, \"display\", \"flex\", \"align-items\", \"center\", \"justify-content\", \"space-between\", \"margin-bottom\", \"8px\"], [\"style\", \"color: #007bff;\", 4, \"ngIf\"], [\"style\", \"color: #28a745; font-size: 0.75rem;\", 4, \"ngIf\"], [\"style\", \"display: flex; gap: 4px;\", 4, \"ngIf\"], [\"style\", \"margin-top: 8px;\", 4, \"ngIf\"], [\"style\", \"margin-top: 12px;\", 4, \"ngIf\"], [2, \"flex\", \"1\", \"padding\", \"16px\", \"overflow-y\", \"auto\"], [\"style\", \"text-align: center; padding: 40px 20px; color: #6c757d;\", 4, \"ngIf\"], [\"style\", \"display: grid; grid-template-columns: repeat(auto-fill, minmax(90px, 1fr)); gap: 8px;\", 4, \"ngIf\"], [\"type\", \"button\", 2, \"width\", \"100%\", \"text-align\", \"left\", \"padding\", \"12px 16px\", \"border\", \"none\", \"cursor\", \"pointer\", \"transition\", \"all 0.15s ease\", \"display\", \"flex\", \"align-items\", \"center\", \"justify-content\", \"space-between\", 3, \"click\"], [2, \"font-weight\", \"500\", \"color\", \"#495057\"], [2, \"font-size\", \"0.75rem\", \"color\", \"#6c757d\", \"background-color\", \"#e9ecef\", \"padding\", \"2px 6px\", \"border-radius\", \"10px\"], [2, \"color\", \"#007bff\"], [2, \"color\", \"#28a745\", \"font-size\", \"0.75rem\"], [2, \"display\", \"flex\", \"gap\", \"4px\"], [\"type\", \"button\", 2, \"padding\", \"4px 8px\", \"font-size\", \"0.75rem\", \"background\", \"#007bff\", \"color\", \"white\", \"border\", \"none\", \"border-radius\", \"4px\", \"cursor\", \"pointer\", 3, \"click\", \"disabled\"], [\"type\", \"button\", 2, \"padding\", \"4px 8px\", \"font-size\", \"0.75rem\", \"background\", \"#28a745\", \"color\", \"white\", \"border\", \"none\", \"border-radius\", \"4px\", \"cursor\", \"pointer\", 3, \"click\", \"disabled\"], [\"type\", \"button\", \"style\", \"padding: 4px 8px; font-size: 0.75rem; background: #6c757d; color: white; border: none; border-radius: 4px; cursor: pointer;\", 3, \"click\", 4, \"ngIf\"], [\"type\", \"button\", 2, \"padding\", \"4px 8px\", \"font-size\", \"0.75rem\", \"background\", \"#6c757d\", \"color\", \"white\", \"border\", \"none\", \"border-radius\", \"4px\", \"cursor\", \"pointer\", 3, \"click\"], [2, \"margin-top\", \"8px\"], [2, \"position\", \"relative\"], [\"type\", \"text\", \"placeholder\", \"\\u641C\\u5C0B\\u6236\\u5225\\u4EE3\\u78BC...\", 2, \"width\", \"100%\", \"padding\", \"6px 32px 6px 12px\", \"border\", \"1px solid #ced4da\", \"border-radius\", \"4px\", \"font-size\", \"0.875rem\", \"outline\", \"none\", 3, \"ngModelChange\", \"input\", \"ngModel\"], [\"icon\", \"search-outline\", 2, \"position\", \"absolute\", \"right\", \"10px\", \"top\", \"50%\", \"transform\", \"translateY(-50%)\", \"color\", \"#6c757d\", \"font-size\", \"0.875rem\"], [\"style\", \"font-size: 0.75rem; color: #dc3545; margin-top: 4px;\", 4, \"ngIf\"], [2, \"font-size\", \"0.75rem\", \"color\", \"#dc3545\", \"margin-top\", \"4px\"], [2, \"margin-top\", \"12px\"], [2, \"display\", \"flex\", \"align-items\", \"center\", \"gap\", \"8px\", \"margin-bottom\", \"8px\"], [\"icon\", \"layers-outline\", 2, \"color\", \"#6c757d\", \"font-size\", \"0.875rem\"], [2, \"font-size\", \"0.875rem\", \"font-weight\", \"500\", \"color\", \"#495057\"], [\"type\", \"button\", \"style\", \"font-size: 0.75rem; color: #007bff; background: none; border: none; text-decoration: underline; cursor: pointer;\", 3, \"click\", 4, \"ngIf\"], [2, \"display\", \"flex\", \"flex-wrap\", \"wrap\", \"gap\", \"4px\", \"max-height\", \"100px\", \"overflow-y\", \"auto\"], [\"type\", \"button\", \"style\", \"padding: 4px 8px; border: 1px solid; border-radius: 4px; font-size: 0.75rem; cursor: pointer; transition: all 0.15s ease; white-space: nowrap;\", 3, \"background-color\", \"color\", \"border-color\", \"click\", 4, \"ngFor\", \"ngForOf\"], [\"type\", \"button\", 2, \"font-size\", \"0.75rem\", \"color\", \"#007bff\", \"background\", \"none\", \"border\", \"none\", \"text-decoration\", \"underline\", \"cursor\", \"pointer\", 3, \"click\"], [\"type\", \"button\", 2, \"padding\", \"4px 8px\", \"border\", \"1px solid\", \"border-radius\", \"4px\", \"font-size\", \"0.75rem\", \"cursor\", \"pointer\", \"transition\", \"all 0.15s ease\", \"white-space\", \"nowrap\", 3, \"click\"], [2, \"text-align\", \"center\", \"padding\", \"40px 20px\", \"color\", \"#6c757d\"], [\"icon\", \"home-outline\", 2, \"font-size\", \"2rem\", \"margin-bottom\", \"8px\", \"opacity\", \"0.5\"], [2, \"display\", \"grid\", \"grid-template-columns\", \"repeat(auto-fill, minmax(90px, 1fr))\", \"gap\", \"8px\"], [4, \"ngFor\", \"ngForOf\"], [\"type\", \"button\", \"style\", \"padding: 6px 4px; border: 1px solid; border-radius: 4px; transition: all 0.15s ease; font-size: 0.75rem; text-align: center; min-height: 40px; position: relative; display: flex; flex-direction: column; justify-content: center;\", 3, \"disabled\", \"background-color\", \"color\", \"border-color\", \"opacity\", \"cursor\", \"click\", 4, \"ngIf\"], [\"type\", \"button\", 2, \"padding\", \"6px 4px\", \"border\", \"1px solid\", \"border-radius\", \"4px\", \"transition\", \"all 0.15s ease\", \"font-size\", \"0.75rem\", \"text-align\", \"center\", \"min-height\", \"40px\", \"position\", \"relative\", \"display\", \"flex\", \"flex-direction\", \"column\", \"justify-content\", \"center\", 3, \"click\", \"disabled\"], [2, \"font-weight\", \"500\", \"line-height\", \"1.2\"], [\"style\", \"font-size: 0.6rem; opacity: 0.8; margin-top: 2px;\", 4, \"ngIf\"], [\"style\", \"position: absolute; top: -8px; right: -8px; background: #dc3545; color: white; border-radius: 50%; width: 16px; height: 16px; font-size: 10px; display: flex; align-items: center; justify-content: center;\", 4, \"ngIf\"], [2, \"font-size\", \"0.6rem\", \"opacity\", \"0.8\", \"margin-top\", \"2px\"], [2, \"position\", \"absolute\", \"top\", \"-8px\", \"right\", \"-8px\", \"background\", \"#dc3545\", \"color\", \"white\", \"border-radius\", \"50%\", \"width\", \"16px\", \"height\", \"16px\", \"font-size\", \"10px\", \"display\", \"flex\", \"align-items\", \"center\", \"justify-content\", \"center\"], [\"icon\", \"alert-circle-outline\", 2, \"font-size\", \"2rem\", \"margin-bottom\", \"8px\", \"opacity\", \"0.5\"], [\"icon\", \"alert-circle-outline\", 2, \"color\", \"#ffc107\"], [\"icon\", \"home-outline\", 2, \"color\", \"#007bff\"], [\"style\", \"color: #28a745; margin-left: 8px;\", 4, \"ngIf\"], [2, \"color\", \"#28a745\", \"margin-left\", \"8px\"], [\"icon\", \"layers-outline\", 2, \"color\", \"#28a745\"], [2, \"font-size\", \"0.75rem\", \"color\", \"#6c757d\", \"background-color\", \"#e9ecef\", \"padding\", \"4px 8px\", \"border-radius\", \"12px\"], [\"type\", \"button\", 2, \"padding\", \"8px 12px\", \"background\", \"#dc3545\", \"color\", \"white\", \"border\", \"none\", \"border-radius\", \"4px\", \"cursor\", \"pointer\", \"font-size\", \"0.875rem\", \"display\", \"flex\", \"align-items\", \"center\", \"gap\", \"4px\", 3, \"click\"], [\"icon\", \"trash-2-outline\"], [\"type\", \"button\", 2, \"padding\", \"8px 12px\", \"background\", \"#6c757d\", \"color\", \"white\", \"border\", \"none\", \"border-radius\", \"4px\", \"cursor\", \"pointer\", \"font-size\", \"0.875rem\", \"display\", \"flex\", \"align-items\", \"center\", \"gap\", \"4px\", 3, \"click\"], [\"icon\", \"refresh-outline\"]],\n      template: function HouseholdBindingComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"div\", 1);\n          i0.ɵɵtemplate(1, HouseholdBindingComponent_div_1_Template, 10, 3, \"div\", 2);\n          i0.ɵɵelementStart(2, \"div\", 3)(3, \"button\", 4);\n          i0.ɵɵlistener(\"click\", function HouseholdBindingComponent_Template_button_click_3_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.toggleDropdown());\n          });\n          i0.ɵɵelementStart(4, \"span\", 5);\n          i0.ɵɵtemplate(5, HouseholdBindingComponent_ng_container_5_Template, 3, 0, \"ng-container\", 6)(6, HouseholdBindingComponent_ng_container_6_Template, 2, 1, \"ng-container\", 6);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(7, \"nb-icon\", 7);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(8, HouseholdBindingComponent_ng_template_8_Template, 37, 11, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor);\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.showSelectedArea && ctx.selectedHouseholds.length > 0);\n          i0.ɵɵadvance(2);\n          i0.ɵɵclassProp(\"disabled\", ctx.disabled || ctx.isLoading);\n          i0.ɵɵproperty(\"disabled\", ctx.disabled || ctx.isLoading);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading);\n        }\n      },\n      dependencies: [i3.NgForOf, i3.NgIf, i4.DefaultValueAccessor, i4.NgControlStatus, i4.NgModel, i2.NbCardComponent, i2.NbCardBodyComponent, i2.NbCardFooterComponent, i2.NbCardHeaderComponent, i2.NbIconComponent],\n      styles: [\".household-binding-container[_ngcontent-%COMP%] {\\n  position: relative;\\n  width: 100%;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .spin[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_spin 1s linear infinite;\\n}\\n@keyframes _ngcontent-%COMP%_spin {\\n  from {\\n    transform: rotate(0deg);\\n  }\\n  to {\\n    transform: rotate(360deg);\\n  }\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%] {\\n  margin-bottom: 1rem;\\n  padding: 1rem;\\n  background-color: #f8f9fa;\\n  border: 1px solid #e9ecef;\\n  border-radius: 0.375rem;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%]   .selected-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 0.75rem;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%]   .selected-header[_ngcontent-%COMP%]   .selected-info[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.5rem;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%]   .selected-header[_ngcontent-%COMP%]   .selected-info[_ngcontent-%COMP%]   .selected-count[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  font-weight: 500;\\n  color: #495057;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%]   .selected-content[_ngcontent-%COMP%]   .building-group[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: flex-start;\\n  gap: 0.5rem;\\n  margin-bottom: 0.5rem;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%]   .selected-content[_ngcontent-%COMP%]   .building-group[_ngcontent-%COMP%]:last-child {\\n  margin-bottom: 0;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%]   .selected-content[_ngcontent-%COMP%]   .building-group[_ngcontent-%COMP%]   .building-label[_ngcontent-%COMP%] {\\n  min-width: 80px;\\n  font-weight: 500;\\n  color: #495057;\\n  font-size: 0.875rem;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%]   .selected-content[_ngcontent-%COMP%]   .building-group[_ngcontent-%COMP%]   .households-tags[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-wrap: wrap;\\n  gap: 0.25rem;\\n  flex: 1;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%]   .selected-content[_ngcontent-%COMP%]   .building-group[_ngcontent-%COMP%]   .households-tags[_ngcontent-%COMP%]   .household-tag[_ngcontent-%COMP%] {\\n  display: inline-flex;\\n  align-items: center;\\n  gap: 0.375rem;\\n  padding: 0.375rem 0.75rem;\\n  background-color: #e3f2fd;\\n  color: #1976d2;\\n  font-size: 0.75rem;\\n  border-radius: 1.25rem;\\n  border: 1px solid #bbdefb;\\n  transition: all 0.2s ease;\\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%]   .selected-content[_ngcontent-%COMP%]   .building-group[_ngcontent-%COMP%]   .households-tags[_ngcontent-%COMP%]   .household-tag[_ngcontent-%COMP%]:hover {\\n  background-color: #bbdefb;\\n  transform: translateY(-1px);\\n  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%]   .selected-content[_ngcontent-%COMP%]   .building-group[_ngcontent-%COMP%]   .households-tags[_ngcontent-%COMP%]   .household-tag[_ngcontent-%COMP%]   .household-info[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: flex-start;\\n  gap: 0.1rem;\\n  line-height: 1.2;\\n  min-width: 0;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%]   .selected-content[_ngcontent-%COMP%]   .building-group[_ngcontent-%COMP%]   .households-tags[_ngcontent-%COMP%]   .household-tag[_ngcontent-%COMP%]   .household-info[_ngcontent-%COMP%]   .household-code[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  color: #0d47a1;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%]   .selected-content[_ngcontent-%COMP%]   .building-group[_ngcontent-%COMP%]   .households-tags[_ngcontent-%COMP%]   .household-tag[_ngcontent-%COMP%]   .household-info[_ngcontent-%COMP%]   .household-floor[_ngcontent-%COMP%] {\\n  font-size: 0.65rem;\\n  opacity: 0.8;\\n  font-weight: 400;\\n  color: #1565c0;\\n  background-color: rgba(255, 255, 255, 0.3);\\n  padding: 0.1rem 0.25rem;\\n  border-radius: 0.25rem;\\n  min-width: fit-content;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%]   .selected-content[_ngcontent-%COMP%]   .building-group[_ngcontent-%COMP%]   .households-tags[_ngcontent-%COMP%]   .household-tag[_ngcontent-%COMP%]   .remove-btn[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.8);\\n  border: 1px solid #90caf9;\\n  padding: 0.1rem;\\n  margin: 0;\\n  cursor: pointer;\\n  color: #0d47a1;\\n  border-radius: 50%;\\n  width: 1.2rem;\\n  height: 1.2rem;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  transition: all 0.2s ease;\\n  flex-shrink: 0;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%]   .selected-content[_ngcontent-%COMP%]   .building-group[_ngcontent-%COMP%]   .households-tags[_ngcontent-%COMP%]   .household-tag[_ngcontent-%COMP%]   .remove-btn[_ngcontent-%COMP%]:hover {\\n  background-color: #f44336;\\n  border-color: #f44336;\\n  color: white;\\n  transform: scale(1.1);\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%]   .selected-content[_ngcontent-%COMP%]   .building-group[_ngcontent-%COMP%]   .households-tags[_ngcontent-%COMP%]   .household-tag[_ngcontent-%COMP%]   .remove-btn[_ngcontent-%COMP%]:disabled {\\n  opacity: 0.5;\\n  cursor: not-allowed;\\n  transform: none;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%]   .selected-content[_ngcontent-%COMP%]   .building-group[_ngcontent-%COMP%]   .households-tags[_ngcontent-%COMP%]   .household-tag[_ngcontent-%COMP%]   .remove-btn[_ngcontent-%COMP%]:disabled:hover {\\n  background-color: rgba(255, 255, 255, 0.8);\\n  border-color: #90caf9;\\n  color: #0d47a1;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%]   .selected-content[_ngcontent-%COMP%]   .building-group[_ngcontent-%COMP%]   .households-tags[_ngcontent-%COMP%]   .household-tag[_ngcontent-%COMP%]   .remove-btn[_ngcontent-%COMP%]   nb-icon[_ngcontent-%COMP%] {\\n  font-size: 0.75rem;\\n  line-height: 1;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%] {\\n  position: relative;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .selector-button[_ngcontent-%COMP%] {\\n  width: 100%;\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between;\\n  padding: 0.5rem 0.75rem;\\n  border: 1px solid #ced4da;\\n  border-radius: 0.375rem;\\n  background-color: #fff;\\n  cursor: pointer;\\n  transition: all 0.15s ease;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .selector-button[_ngcontent-%COMP%]:hover:not(.disabled) {\\n  border-color: #007bff;\\n  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .selector-button[_ngcontent-%COMP%]:focus {\\n  outline: none;\\n  border-color: #80bdff;\\n  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .selector-button.disabled[_ngcontent-%COMP%] {\\n  opacity: 0.6;\\n  cursor: not-allowed !important;\\n  background-color: #e9ecef;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .selector-button[_ngcontent-%COMP%]   .selector-text[_ngcontent-%COMP%] {\\n  color: #495057;\\n  font-size: 0.875rem;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .selector-button[_ngcontent-%COMP%]   .chevron-icon[_ngcontent-%COMP%] {\\n  transition: transform 0.15s ease;\\n  color: #6c757d;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .selector-button[_ngcontent-%COMP%]   .chevron-icon.rotated[_ngcontent-%COMP%] {\\n  transform: rotate(180deg);\\n}\\n\\n@media (max-width: 768px) {\\n  .household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%]   .selected-content[_ngcontent-%COMP%]   .building-group[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    align-items: flex-start;\\n    gap: 0.25rem;\\n  }\\n  .household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%]   .selected-content[_ngcontent-%COMP%]   .building-group[_ngcontent-%COMP%]   .building-label[_ngcontent-%COMP%] {\\n    min-width: auto;\\n  }\\n  .household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%]   .selected-content[_ngcontent-%COMP%]   .building-group[_ngcontent-%COMP%]   .households-tags[_ngcontent-%COMP%] {\\n    width: 100%;\\n  }\\n  .household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%]   .selected-content[_ngcontent-%COMP%]   .building-group[_ngcontent-%COMP%]   .households-tags[_ngcontent-%COMP%]   .household-tag[_ngcontent-%COMP%]   .household-info[_ngcontent-%COMP%]   .household-floor[_ngcontent-%COMP%] {\\n    font-size: 0.6rem;\\n  }\\n  .household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .selector-button[_ngcontent-%COMP%] {\\n    padding: 0.75rem;\\n  }\\n  .household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .selector-button[_ngcontent-%COMP%]   .selector-text[_ngcontent-%COMP%] {\\n    font-size: 1rem;\\n  }\\n}\\n@media (prefers-color-scheme: dark) {\\n  .household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%] {\\n    background-color: #343a40;\\n    border-color: #495057;\\n    color: #f8f9fa;\\n  }\\n  .household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%]   .selected-header[_ngcontent-%COMP%]   .selected-info[_ngcontent-%COMP%]   .selected-count[_ngcontent-%COMP%] {\\n    color: #f8f9fa;\\n  }\\n  .household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%]   .selected-content[_ngcontent-%COMP%]   .building-group[_ngcontent-%COMP%]   .building-label[_ngcontent-%COMP%] {\\n    color: #adb5bd;\\n  }\\n  .household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%]   .selected-content[_ngcontent-%COMP%]   .building-group[_ngcontent-%COMP%]   .households-tags[_ngcontent-%COMP%]   .household-tag[_ngcontent-%COMP%] {\\n    background-color: #495057;\\n    color: #bbdefb;\\n    border-color: #6c757d;\\n  }\\n  .household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%]   .selected-content[_ngcontent-%COMP%]   .building-group[_ngcontent-%COMP%]   .households-tags[_ngcontent-%COMP%]   .household-tag[_ngcontent-%COMP%]:hover {\\n    background-color: #5a6268;\\n  }\\n  .household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%]   .selected-content[_ngcontent-%COMP%]   .building-group[_ngcontent-%COMP%]   .households-tags[_ngcontent-%COMP%]   .household-tag[_ngcontent-%COMP%]   .household-info[_ngcontent-%COMP%]   .household-code[_ngcontent-%COMP%] {\\n    color: #bbdefb;\\n  }\\n  .household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%]   .selected-content[_ngcontent-%COMP%]   .building-group[_ngcontent-%COMP%]   .households-tags[_ngcontent-%COMP%]   .household-tag[_ngcontent-%COMP%]   .household-info[_ngcontent-%COMP%]   .household-floor[_ngcontent-%COMP%] {\\n    color: #90caf9;\\n    background-color: rgba(0, 0, 0, 0.2);\\n  }\\n  .household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%]   .selected-content[_ngcontent-%COMP%]   .building-group[_ngcontent-%COMP%]   .households-tags[_ngcontent-%COMP%]   .household-tag[_ngcontent-%COMP%]   .remove-btn[_ngcontent-%COMP%] {\\n    background: rgba(0, 0, 0, 0.3);\\n    border-color: #6c757d;\\n    color: #bbdefb;\\n  }\\n  .household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%]   .selected-content[_ngcontent-%COMP%]   .building-group[_ngcontent-%COMP%]   .households-tags[_ngcontent-%COMP%]   .household-tag[_ngcontent-%COMP%]   .remove-btn[_ngcontent-%COMP%]:hover {\\n    background-color: #f44336;\\n    border-color: #f44336;\\n    color: white;\\n  }\\n  .household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .selector-button[_ngcontent-%COMP%] {\\n    background-color: #495057;\\n    border-color: #6c757d;\\n    color: #f8f9fa;\\n  }\\n  .household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .selector-button[_ngcontent-%COMP%]:hover:not(.disabled) {\\n    background-color: #5a6268;\\n  }\\n  .household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .selector-button.disabled[_ngcontent-%COMP%] {\\n    background-color: #6c757d;\\n  }\\n  .household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .selector-button[_ngcontent-%COMP%]   .selector-text[_ngcontent-%COMP%] {\\n    color: #f8f9fa;\\n  }\\n  .household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .selector-button[_ngcontent-%COMP%]   .chevron-icon[_ngcontent-%COMP%] {\\n    color: #adb5bd;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;base64,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 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["EventEmitter", "forwardRef", "NG_VALUE_ACCESSOR", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate1", "ctx_r2", "getHouseholdInfo", "householdCode_r5", "floor", "ɵɵtemplate", "HouseholdBindingComponent_div_1_div_9_ng_container_1_span_4_span_4_Template", "ɵɵlistener", "HouseholdBindingComponent_div_1_div_9_ng_container_1_span_4_Template_button_click_5_listener", "ɵɵrestoreView", "_r4", "$implicit", "ɵɵnextContext", "ɵɵresetView", "onRemoveHousehold", "ɵɵelement", "ɵɵtextInterpolate", "houseName", "ɵɵproperty", "disabled", "ɵɵelementContainerStart", "HouseholdBindingComponent_div_1_div_9_ng_container_1_span_4_Template", "building_r6", "getBuildingSelectedHouseholds", "HouseholdBindingComponent_div_1_div_9_ng_container_1_Template", "hasBuildingSelected", "HouseholdBindingComponent_div_1_Template_button_click_6_listener", "_r2", "onClearAll", "HouseholdBindingComponent_div_1_div_9_Template", "getSelectedCount", "buildings", "placeholder", "HouseholdBindingComponent_ng_template_8_div_13_button_6_Template_button_click_0_listener", "building_r9", "_r8", "onBuildingSelect", "ɵɵstyleProp", "selectedBuilding", "getBuildingCount", "selectedF<PERSON>or", "HouseholdBindingComponent_ng_template_8_div_13_div_14_button_5_Template_button_click_0_listener", "_r11", "onUnselectAllBuilding", "HouseholdBindingComponent_ng_template_8_div_13_div_14_Template_button_click_1_listener", "_r10", "onSelectAllFiltered", "HouseholdBindingComponent_ng_template_8_div_13_div_14_Template_button_click_3_listener", "onSelectAllBuilding", "HouseholdBindingComponent_ng_template_8_div_13_div_14_button_5_Template", "canSelectMore", "isSomeBuildingSelected", "searchTerm", "ɵɵtwoWayListener", "HouseholdBindingComponent_ng_template_8_div_13_div_15_Template_input_ngModelChange_2_listener", "$event", "_r12", "ɵɵtwoWayBindingSet", "HouseholdBindingComponent_ng_template_8_div_13_div_15_Template_input_input_2_listener", "onSearchChange", "HouseholdBindingComponent_ng_template_8_div_13_div_15_div_4_Template", "ɵɵtwoWayProperty", "hasNoSearchResults", "HouseholdBindingComponent_ng_template_8_div_13_div_16_button_5_Template_button_click_0_listener", "_r13", "onFloorSelect", "HouseholdBindingComponent_ng_template_8_div_13_div_16_button_7_Template_button_click_0_listener", "floor_r15", "_r14", "ɵɵtextInterpolate2", "getFloorCount", "HouseholdBindingComponent_ng_template_8_div_13_div_16_button_5_Template", "HouseholdBindingComponent_ng_template_8_div_13_div_16_button_7_Template", "floors", "household_r17", "HouseholdBindingComponent_ng_template_8_div_13_div_19_ng_container_1_button_1_Template_button_click_0_listener", "_r16", "onHouseholdToggle", "HouseholdBindingComponent_ng_template_8_div_13_div_19_ng_container_1_button_1_span_3_Template", "HouseholdBindingComponent_ng_template_8_div_13_div_19_ng_container_1_button_1_div_4_Template", "isHouseholdSelected", "isHouseholdExcluded", "isHouseholdDisabled", "HouseholdBindingComponent_ng_template_8_div_13_div_19_ng_container_1_button_1_Template", "toLowerCase", "includes", "HouseholdBindingComponent_ng_template_8_div_13_div_19_ng_container_1_Template", "buildingData", "HouseholdBindingComponent_ng_template_8_div_13_button_6_Template", "HouseholdBindingComponent_ng_template_8_div_13_span_12_Template", "HouseholdBindingComponent_ng_template_8_div_13_span_13_Template", "HouseholdBindingComponent_ng_template_8_div_13_div_14_Template", "HouseholdBindingComponent_ng_template_8_div_13_div_15_Template", "HouseholdBindingComponent_ng_template_8_div_13_div_16_Template", "HouseholdBindingComponent_ng_template_8_div_13_div_18_Template", "HouseholdBindingComponent_ng_template_8_div_13_div_19_Template", "HouseholdBindingComponent_ng_template_8_div_13_div_20_Template", "allowBatchSelect", "length", "allowSearch", "maxSelections", "HouseholdBindingComponent_ng_template_8_div_25_span_6_Template", "getFilteredHouseholdsCount", "HouseholdBindingComponent_ng_template_8_button_29_Template_button_click_0_listener", "_r18", "HouseholdBindingComponent_ng_template_8_button_30_Template_button_click_0_listener", "_r19", "resetSearch", "HouseholdBindingComponent_ng_template_8_div_12_Template", "HouseholdBindingComponent_ng_template_8_div_13_Template", "HouseholdBindingComponent_ng_template_8_div_24_Template", "HouseholdBindingComponent_ng_template_8_div_25_Template", "HouseholdBindingComponent_ng_template_8_div_26_Template", "HouseholdBindingComponent_ng_template_8_button_29_Template", "HouseholdBindingComponent_ng_template_8_button_30_Template", "HouseholdBindingComponent_ng_template_8_Template_button_click_32_listener", "ref_r20", "_r7", "dialogRef", "close", "HouseholdBindingComponent_ng_template_8_Template_button_click_34_listener", "isLoading", "selectedHouseholds", "HouseholdBindingComponent", "constructor", "cdr", "houseCustomService", "dialogService", "buildCaseId", "showSelectedArea", "excludedHouseIds", "selectionChange", "houseIdChange", "isOpen", "selectedHouseIds", "filteredHouseholds", "selectedByBuilding", "onChange", "value", "onTouched", "writeValue", "updateSelectedByBuilding", "registerOnChange", "fn", "registerOnTouched", "setDisabledState", "isDisabled", "ngOnInit", "initializeData", "ngOnChanges", "changes", "Object", "keys", "console", "log", "updateFilteredHouseholds", "excludedHouseholds", "loadBuildingDataFromApi", "getDropDown", "subscribe", "next", "response", "convertApiResponseToBuildingData", "Entries", "detectChanges", "error", "entries", "for<PERSON>ach", "building", "houses", "map", "house", "HouseName", "code", "Building", "Floor", "houseId", "HouseId", "isSelected", "grouped", "item", "find", "h", "push", "updateFloorsForBuilding", "onBuildingClick", "households", "filter", "floorMatch", "searchMatch", "event", "target", "householdCode", "newSelection", "emitChanges", "currentCount", "maxAllowed", "Infinity", "remainingSlots", "unselectedFiltered", "toAdd", "slice", "buildingHouseholds", "unselectedBuilding", "selectedItems", "emit", "houseIds", "id", "undefined", "toggleDropdown", "openDialog", "open", "householdDialog", "context", "closeOnBackdropClick", "closeOnEsc", "autoFocus", "closeDropdown", "isAllBuildingSelected", "every", "some", "getSelectedByBuilding", "floorSet", "Set", "household", "add", "Array", "from", "sort", "a", "b", "numA", "parseInt", "replace", "numB", "getHouseholdFloor", "filtered", "getHouseholdUniqueId", "toString", "getHouseholdFromUniqueId", "uniqueId", "ɵɵdirectiveInject", "ChangeDetectorRef", "i1", "HouseCustomService", "i2", "NbDialogService", "selectors", "viewQuery", "HouseholdBindingComponent_Query", "rf", "ctx", "provide", "useExisting", "multi", "ɵɵNgOnChangesFeature", "decls", "vars", "consts", "template", "HouseholdBindingComponent_Template", "HouseholdBindingComponent_div_1_Template", "HouseholdBindingComponent_Template_button_click_3_listener", "_r1", "HouseholdBindingComponent_ng_container_5_Template", "HouseholdBindingComponent_ng_container_6_Template", "HouseholdBindingComponent_ng_template_8_Template", "ɵɵtemplateRefExtractor", "ɵɵclassProp"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\shared\\components\\household-binding\\household-binding.component.ts", "C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\shared\\components\\household-binding\\household-binding.component.html"], "sourcesContent": ["import { Component, Input, Output, EventEmitter, OnInit, OnChanges, SimpleChanges, forwardRef, ChangeDetectorRef, TemplateRef, ViewChild } from '@angular/core';\r\nimport { ControlValueAccessor, NG_VALUE_ACCESSOR } from '@angular/forms';\r\nimport { NbDialogService } from '@nebular/theme';\r\nimport { HouseCustomService } from '../../../../services/api/services/HouseCustom.service';\r\nimport { HouseItem } from '../../../../services/api/models/house.model';\r\n\r\nexport interface HouseholdItem {\r\n  houseName: string;\r\n  building: string;\r\n  floor?: string;\r\n  houseId?: number;\r\n  isSelected?: boolean;\r\n  isDisabled?: boolean;\r\n}\r\n\r\nexport interface BuildingData {\r\n  [key: string]: HouseholdItem[];\r\n}\r\n\r\n@Component({\r\n  selector: 'app-household-binding',\r\n  templateUrl: './household-binding.component.html',\r\n  styleUrls: ['./household-binding.component.scss'],\r\n  providers: [\r\n    {\r\n      provide: NG_VALUE_ACCESSOR,\r\n      useExisting: forwardRef(() => HouseholdBindingComponent),\r\n      multi: true,\r\n    },\r\n  ],\r\n})\r\nexport class HouseholdBindingComponent implements OnInit, OnChanges, ControlValueAccessor {\r\n  @ViewChild('householdDialog', { static: false }) householdDialog!: TemplateRef<any>;\r\n\r\n  @Input() placeholder: string = '請選擇戶別';\r\n  @Input() maxSelections: number | null = null;\r\n  @Input() disabled: boolean = false;\r\n  @Input() buildCaseId: number | null = null; // 新增：建案ID\r\n  @Input() buildingData: BuildingData = {};\r\n  @Input() showSelectedArea: boolean = true;\r\n  @Input() allowSearch: boolean = true;  @Input() allowBatchSelect: boolean = true;\r\n  @Input() excludedHouseIds: number[] = []; // 改為：排除的戶別ID（已被其他元件選擇）\r\n\r\n  @Output() selectionChange = new EventEmitter<HouseholdItem[]>();\r\n  @Output() houseIdChange = new EventEmitter<number[]>(); // 新增：回傳 houseId 陣列\r\n  isOpen = false;\r\n  selectedBuilding = '';\r\n  searchTerm = '';\r\n  selectedFloor = ''; // 新增：選中的樓層\r\n  selectedHouseIds: number[] = []; // 改為：使用 houseId 作為選擇的key\r\n  buildings: string[] = [];\r\n  floors: string[] = []; // 新增：當前棟別的樓層列表\r\n  filteredHouseholds: string[] = [];  // 保持為字串陣列用於UI顯示\r\n  selectedByBuilding: { [building: string]: number[] } = {}; // 改為：儲存 houseId\r\n  isLoading: boolean = false; // 新增：載入狀態\r\n\r\n  // ControlValueAccessor implementation\r\n  private onChange = (value: string[]) => { };\r\n  private onTouched = () => { };\r\n  constructor(\r\n    private cdr: ChangeDetectorRef,\r\n    private houseCustomService: HouseCustomService,\r\n    private dialogService: NbDialogService\r\n  ) { }\r\n\r\n  writeValue(value: string[]): void {\r\n    this.selectedHouseholds = value || [];\r\n    this.updateSelectedByBuilding();\r\n  }\r\n\r\n  registerOnChange(fn: (value: string[]) => void): void {\r\n    this.onChange = fn;\r\n  }\r\n\r\n  registerOnTouched(fn: () => void): void {\r\n    this.onTouched = fn;\r\n  }\r\n  setDisabledState(isDisabled: boolean): void {\r\n    this.disabled = isDisabled;\r\n  } ngOnInit() {\r\n    this.initializeData();\r\n  }\r\n  ngOnChanges(changes: SimpleChanges) {\r\n    if (changes['buildCaseId'] && this.buildCaseId) {\r\n      // 當建案ID變更時，重新載入資料\r\n      this.initializeData();\r\n    }\r\n    if (changes['buildingData']) {\r\n      // 當 buildingData 變更時，重新初始化\r\n      this.buildings = Object.keys(this.buildingData || {});\r\n      console.log('buildingData updated:', this.buildingData);\r\n      this.updateFilteredHouseholds();\r\n      this.updateSelectedByBuilding();\r\n    }\r\n    if (changes['excludedHouseholds']) {\r\n      // 當排除列表變化時，重新更新顯示\r\n      this.updateFilteredHouseholds();\r\n      console.log('Excluded households updated:', this.excludedHouseholds);\r\n    }\r\n  } private initializeData() {\r\n    // 優先檢查是否有傳入 buildingData\r\n    if (this.buildingData && Object.keys(this.buildingData).length > 0) {\r\n      // 使用傳入的 buildingData\r\n      this.buildings = Object.keys(this.buildingData);\r\n      console.log('Component initialized with provided buildingData:', this.buildings);\r\n      this.updateSelectedByBuilding();\r\n    } else if (this.buildCaseId) {\r\n      // 只有在沒有傳入 buildingData 且有建案ID時才呼叫API\r\n      this.loadBuildingDataFromApi();\r\n    } else {\r\n      // 既沒有 buildingData 也沒有 buildCaseId，保持空狀態\r\n      this.buildings = [];\r\n      console.log('No buildingData or buildCaseId provided');\r\n    }\r\n  }\r\n\r\n  private loadBuildingDataFromApi() {\r\n    if (!this.buildCaseId) return;\r\n\r\n    this.isLoading = true;\r\n    this.houseCustomService.getDropDown(this.buildCaseId).subscribe({\r\n      next: (response) => {\r\n        console.log('API response:', response);\r\n        this.buildingData = this.convertApiResponseToBuildingData(response.Entries);\r\n        this.buildings = Object.keys(this.buildingData);\r\n        this.updateSelectedByBuilding();\r\n        this.isLoading = false;\r\n        this.cdr.detectChanges();\r\n      }, error: (error) => {\r\n        console.error('Error loading building data:', error);\r\n        // API載入失敗時，不使用備援資料，保持空狀態\r\n        this.buildingData = {};\r\n        this.buildings = [];\r\n        this.isLoading = false;\r\n        this.cdr.detectChanges();\r\n      }\r\n    });\r\n  }\r\n  private convertApiResponseToBuildingData(entries: { [key: string]: any[] }): BuildingData {\r\n    const buildingData: BuildingData = {};\r\n\r\n    Object.entries(entries).forEach(([building, houses]) => {\r\n      buildingData[building] = houses.map(house => ({\r\n        houseName: house.houseName || house.HouseName || house.code,\r\n        building: house.building || house.Building || building,\r\n        floor: house.floor || house.Floor,\r\n        houseId: house.houseId || house.HouseId,\r\n        isSelected: false,\r\n        isDisabled: false\r\n      }));\r\n    });\r\n\r\n    return buildingData;\r\n  } private updateSelectedByBuilding() {\r\n    const grouped: { [building: string]: string[] } = {};\r\n\r\n    this.selectedHouseholds.forEach(houseName => {\r\n      for (const building of this.buildings) {\r\n        const item = this.buildingData[building]?.find(h => h.houseName === houseName);\r\n        if (item) {\r\n          if (!grouped[building]) grouped[building] = [];\r\n          grouped[building].push(houseName);\r\n          break;\r\n        }\r\n      }\r\n    });\r\n\r\n    this.selectedByBuilding = grouped;\r\n  } onBuildingSelect(building: string) {\r\n    console.log('Building selected:', building);\r\n    this.selectedBuilding = building;\r\n    this.selectedFloor = ''; // 重置樓層選擇\r\n    this.searchTerm = '';\r\n    this.updateFloorsForBuilding(); // 更新樓層列表\r\n    this.updateFilteredHouseholds();\r\n    console.log('Filtered households count:', this.filteredHouseholds.length);\r\n    // 手動觸發變更偵測\r\n    this.cdr.detectChanges();\r\n  }\r\n\r\n  onBuildingClick(building: string) {\r\n    console.log('Building clicked (mousedown):', building);\r\n  } updateFilteredHouseholds() {\r\n    console.log('Updating filtered households for building:', this.selectedBuilding, 'floor:', this.selectedFloor);\r\n    if (!this.selectedBuilding) {\r\n      this.filteredHouseholds = [];\r\n      return;\r\n    }\r\n\r\n    const households = this.buildingData[this.selectedBuilding] || [];\r\n    console.log('Available households for building:', households.length);\r\n\r\n    // 提取戶別代碼並進行搜尋和樓層過濾\r\n    this.filteredHouseholds = households\r\n      .filter(h => {\r\n        // 樓層篩選：如果有選擇樓層，只顯示該樓層的戶別\r\n        const floorMatch = !this.selectedFloor || h.floor === this.selectedFloor;\r\n        // 搜尋篩選：戶別代碼包含搜尋詞\r\n        const searchMatch = h.houseName.toLowerCase().includes(this.searchTerm.toLowerCase());\r\n        return floorMatch && searchMatch;\r\n      })\r\n      .map(h => h.houseName);\r\n\r\n    console.log('Filtered households result:', this.filteredHouseholds.length);\r\n  }\r\n\r\n  onSearchChange(event: any) {\r\n    this.searchTerm = event.target.value;\r\n    this.updateFilteredHouseholds();\r\n    console.log('Search term changed:', this.searchTerm);\r\n  }\r\n\r\n  resetSearch() {\r\n    this.searchTerm = '';\r\n    this.updateFilteredHouseholds();\r\n    console.log('Search reset');\r\n  }\r\n  onHouseholdToggle(householdCode: string) {\r\n    // 防止選擇已排除的戶別\r\n    if (this.isHouseholdExcluded(householdCode)) {\r\n      console.log(`戶別 ${householdCode} 已被其他元件選擇，無法重複選擇`);\r\n      return;\r\n    }\r\n\r\n    const isSelected = this.selectedHouseholds.includes(householdCode);\r\n    let newSelection: string[];\r\n\r\n    if (isSelected) {\r\n      newSelection = this.selectedHouseholds.filter(h => h !== householdCode);\r\n    } else {\r\n      if (this.maxSelections && this.selectedHouseholds.length >= this.maxSelections) {\r\n        console.log('已達到最大選擇數量');\r\n        return; // 達到最大選擇數量\r\n      }\r\n      newSelection = [...this.selectedHouseholds, householdCode];\r\n    }\r\n\r\n    this.selectedHouseholds = newSelection;\r\n    this.emitChanges();\r\n  }\r\n\r\n  onRemoveHousehold(householdCode: string) {\r\n    this.selectedHouseholds = this.selectedHouseholds.filter(h => h !== householdCode);\r\n    this.emitChanges();\r\n  } onSelectAllFiltered() {\r\n    if (!this.selectedBuilding || this.filteredHouseholds.length === 0) return;\r\n\r\n    // 計算可以新增的戶別數量\r\n    const currentCount = this.selectedHouseholds.length;\r\n    const maxAllowed = this.maxSelections || Infinity;\r\n    const remainingSlots = maxAllowed - currentCount;    // 取得尚未選擇且未被排除的過濾戶別\r\n    const unselectedFiltered = this.filteredHouseholds.filter(code =>\r\n      !this.selectedHouseholds.includes(code) && !this.isHouseholdExcluded(code)\r\n    );\r\n\r\n    // 根據剩餘空間決定要新增的戶別\r\n    const toAdd = unselectedFiltered.slice(0, remainingSlots);\r\n\r\n    if (toAdd.length > 0) {\r\n      this.selectedHouseholds = [...this.selectedHouseholds, ...toAdd];\r\n      this.emitChanges();\r\n      console.log(`全選當前: 新增了 ${toAdd.length} 個戶別`);\r\n    }\r\n  }\r\n  onSelectAllBuilding() {\r\n    if (!this.selectedBuilding) return;\r\n\r\n    // 取得當前棟別的所有戶別\r\n    const buildingHouseholds = this.buildingData[this.selectedBuilding]?.map(h => h.houseName) || [];\r\n\r\n    // 計算可以新增的戶別數量\r\n    const currentCount = this.selectedHouseholds.length;\r\n    const maxAllowed = this.maxSelections || Infinity;\r\n    const remainingSlots = maxAllowed - currentCount;    // 取得尚未選擇且未被排除的棟別戶別\r\n    const unselectedBuilding = buildingHouseholds.filter(code =>\r\n      !this.selectedHouseholds.includes(code) && !this.isHouseholdExcluded(code)\r\n    );\r\n\r\n    // 根據剩餘空間決定要新增的戶別\r\n    const toAdd = unselectedBuilding.slice(0, remainingSlots);\r\n\r\n    if (toAdd.length > 0) {\r\n      this.selectedHouseholds = [...this.selectedHouseholds, ...toAdd];\r\n      this.emitChanges();\r\n      console.log(`全選棟別: 新增了 ${toAdd.length} 個戶別`);\r\n    }\r\n  } onUnselectAllBuilding() {\r\n    if (!this.selectedBuilding) return;\r\n\r\n    const buildingHouseholds = this.buildingData[this.selectedBuilding]?.map(h => h.houseName) || [];\r\n    this.selectedHouseholds = this.selectedHouseholds.filter(h => !buildingHouseholds.includes(h));\r\n    this.emitChanges();\r\n  }\r\n  onClearAll() {\r\n    this.selectedHouseholds = [];\r\n    this.emitChanges();\r\n  }\r\n  private emitChanges() {\r\n    this.updateSelectedByBuilding();\r\n    this.onChange([...this.selectedHouseholds]);\r\n    this.onTouched();\r\n\r\n    const selectedItems = this.selectedHouseholds.map(houseName => {\r\n      for (const building of this.buildings) {\r\n        const item = this.buildingData[building]?.find(h => h.houseName === houseName);\r\n        if (item) return item;\r\n      }\r\n      return null;\r\n    }).filter(item => item !== null) as HouseholdItem[];\r\n\r\n    this.selectionChange.emit(selectedItems);\r\n\r\n    // 新增：回傳 houseId 陣列\r\n    const houseIds = selectedItems.map(item => item.houseId!).filter(id => id !== undefined);\r\n    this.houseIdChange.emit(houseIds);\r\n  } toggleDropdown() {\r\n    if (!this.disabled) {\r\n      this.openDialog();\r\n      console.log('Opening household selection dialog');\r\n      console.log('Buildings available:', this.buildings);\r\n    }\r\n  }\r\n\r\n  openDialog() {\r\n    this.dialogService.open(this.householdDialog, {\r\n      context: {},\r\n      closeOnBackdropClick: false,\r\n      closeOnEsc: true,\r\n      autoFocus: false,\r\n    });\r\n  }\r\n\r\n  closeDropdown() {\r\n    // 這個方法現在用於關閉對話框\r\n    // 對話框的關閉將由 NbDialogRef 處理\r\n  }\r\n\r\n  isHouseholdSelected(householdCode: string): boolean {\r\n    return this.selectedHouseholds.includes(householdCode);\r\n  }\r\n\r\n  isHouseholdExcluded(householdCode: string): boolean {\r\n    return this.excludedHouseholds.includes(householdCode);\r\n  }\r\n\r\n  isHouseholdDisabled(householdCode: string): boolean {\r\n    return this.isHouseholdExcluded(householdCode) ||\r\n      (!this.canSelectMore() && !this.isHouseholdSelected(householdCode));\r\n  }\r\n\r\n  canSelectMore(): boolean {\r\n    return !this.maxSelections || this.selectedHouseholds.length < this.maxSelections;\r\n  }\r\n  isAllBuildingSelected(): boolean {\r\n    if (!this.selectedBuilding) return false;\r\n    const buildingHouseholds = this.buildingData[this.selectedBuilding]\r\n      .filter(h => !h.isDisabled)\r\n      .map(h => h.houseName);\r\n    return buildingHouseholds.length > 0 &&\r\n      buildingHouseholds.every(houseName => this.selectedHouseholds.includes(houseName));\r\n  } isSomeBuildingSelected(): boolean {\r\n    if (!this.selectedBuilding) return false;\r\n    const buildingHouseholds = this.buildingData[this.selectedBuilding]?.map(h => h.houseName) || [];\r\n    return buildingHouseholds.some(houseName => this.selectedHouseholds.includes(houseName));\r\n  }\r\n  getSelectedByBuilding(): { [building: string]: string[] } {\r\n    return this.selectedByBuilding;\r\n  }\r\n\r\n  getBuildingCount(building: string): number {\r\n    return this.buildingData[building]?.length || 0;\r\n  }\r\n\r\n  getSelectedCount(): number {\r\n    return this.selectedHouseholds.length;\r\n  }\r\n\r\n  // 輔助方法：安全地獲取建築物的已選戶別\r\n  getBuildingSelectedHouseholds(building: string): string[] {\r\n    return this.selectedByBuilding[building] || [];\r\n  }\r\n\r\n  // 輔助方法：檢查建築物是否有已選戶別\r\n  hasBuildingSelected(building: string): boolean {\r\n    return !!(this.selectedByBuilding[building] && this.selectedByBuilding[building].length > 0);\r\n  }\r\n\r\n  // 新增：更新當前棟別的樓層列表\r\n  private updateFloorsForBuilding() {\r\n    if (!this.selectedBuilding) {\r\n      this.floors = [];\r\n      return;\r\n    }\r\n\r\n    const households = this.buildingData[this.selectedBuilding] || [];\r\n    const floorSet = new Set<string>();\r\n\r\n    households.forEach(household => {\r\n      if (household.floor) {\r\n        floorSet.add(household.floor);\r\n      }\r\n    });\r\n\r\n    // 對樓層進行自然排序\r\n    this.floors = Array.from(floorSet).sort((a, b) => {\r\n      const numA = parseInt(a.replace(/[^0-9]/g, ''));\r\n      const numB = parseInt(b.replace(/[^0-9]/g, ''));\r\n      return numA - numB;\r\n    });\r\n\r\n    console.log('Updated floors for building:', this.selectedBuilding, this.floors);\r\n  }\r\n\r\n  // 新增：樓層選擇處理\r\n  onFloorSelect(floor: string) {\r\n    console.log('Floor selected:', floor);\r\n    this.selectedFloor = this.selectedFloor === floor ? '' : floor; // 切換選擇\r\n    this.updateFilteredHouseholds();\r\n    this.cdr.detectChanges();\r\n  }\r\n\r\n  // 新增：取得當前棧別的樓層計數\r\n  getFloorCount(floor: string): number {\r\n    if (!this.selectedBuilding) return 0;\r\n    const households = this.buildingData[this.selectedBuilding] || [];\r\n    return households.filter(h => h.floor === floor).length;\r\n  }\r\n  // 新增：取得戶別的樓層資訊\r\n  getHouseholdFloor(householdCode: string): string {\r\n    if (!this.selectedBuilding) return '';\r\n    const households = this.buildingData[this.selectedBuilding] || [];\r\n    const household = households.find(h => h.houseName === householdCode);\r\n    return household?.floor || '';\r\n  }\r\n  // 新增：取得戶別的完整資訊（包含樓層）\r\n  getHouseholdInfo(householdCode: string): { houseName: string, floor: string } {\r\n    for (const building of this.buildings) {\r\n      const households = this.buildingData[building] || [];\r\n      const household = households.find(h => h.houseName === householdCode);\r\n      if (household) {\r\n        return {\r\n          houseName: household.houseName,\r\n          floor: household.floor || ''\r\n        };\r\n      }\r\n    }\r\n    return { houseName: householdCode, floor: '' };\r\n  }\r\n\r\n  // 新增：檢查搜尋是否有結果\r\n  hasNoSearchResults(): boolean {\r\n    if (!this.searchTerm || !this.selectedBuilding || !this.buildingData[this.selectedBuilding]) {\r\n      return false;\r\n    }\r\n\r\n    const filtered = this.buildingData[this.selectedBuilding].filter(h => {\r\n      const floorMatch = !this.selectedFloor || h.floor === this.selectedFloor;\r\n      const searchMatch = h.houseName.toLowerCase().includes(this.searchTerm.toLowerCase());\r\n      return floorMatch && searchMatch;\r\n    });\r\n\r\n    return filtered.length === 0;\r\n  }\r\n\r\n  // 新增：取得過濾後的戶別數量\r\n  getFilteredHouseholdsCount(): number {\r\n    if (!this.selectedBuilding || !this.buildingData[this.selectedBuilding]) {\r\n      return 0;\r\n    }\r\n\r\n    const filtered = this.buildingData[this.selectedBuilding].filter(h => {\r\n      const floorMatch = !this.selectedFloor || h.floor === this.selectedFloor;\r\n      const searchMatch = !this.searchTerm || h.houseName.toLowerCase().includes(this.searchTerm.toLowerCase());\r\n      return floorMatch && searchMatch;\r\n    });\r\n\r\n    return filtered.length;\r\n  }\r\n\r\n  // 新增：產生戶別的唯一識別符\r\n  getHouseholdUniqueId(household: HouseholdItem): string {\r\n    return household.houseId ? household.houseId.toString() : `${household.houseName}_${household.floor}`;\r\n  }\r\n\r\n  // 新增：從唯一識別符獲取戶別物件\r\n  getHouseholdFromUniqueId(uniqueId: string): HouseholdItem | null {\r\n    for (const building of this.buildings) {\r\n      const households = this.buildingData[building] || [];\r\n      const household = households.find(h => this.getHouseholdUniqueId(h) === uniqueId);\r\n      if (household) return household;\r\n    }\r\n    return null;\r\n  }\r\n}\r\n", "<div class=\"household-binding-container\">\r\n  <!-- 已選擇戶別顯示區域 -->\r\n  <div *ngIf=\"showSelectedArea && selectedHouseholds.length > 0\" class=\"selected-households-area\">\r\n    <div class=\"selected-header\">\r\n      <div class=\"selected-info\">\r\n        <nb-icon icon=\"people-outline\" class=\"text-primary\"></nb-icon>\r\n        <span class=\"selected-count\">已選擇戶別 ({{getSelectedCount()}})</span>\r\n      </div>\r\n      <button type=\"button\" class=\"btn btn-outline-danger btn-sm\" [disabled]=\"disabled\" (click)=\"onClearAll()\">\r\n        清空全部\r\n      </button>\r\n    </div>\r\n    <div class=\"selected-content\">\r\n      <div *ngFor=\"let building of buildings\" class=\"building-group\">\r\n        <ng-container *ngIf=\"hasBuildingSelected(building)\">\r\n          <div class=\"building-label\">{{building}}:</div>\r\n          <div class=\"households-tags\">\r\n            <span *ngFor=\"let householdCode of getBuildingSelectedHouseholds(building)\" class=\"household-tag\">\r\n              <div class=\"household-info\">\r\n                <span class=\"household-code\">{{getHouseholdInfo(householdCode).houseName}}</span>\r\n                <span *ngIf=\"getHouseholdInfo(householdCode).floor\" class=\"household-floor\">\r\n                  {{getHouseholdInfo(householdCode).floor}}\r\n                </span>\r\n              </div>\r\n              <button type=\"button\" class=\"remove-btn\" [disabled]=\"disabled\" (click)=\"onRemoveHousehold(householdCode)\">\r\n                <nb-icon icon=\"close-outline\"></nb-icon>\r\n              </button>\r\n            </span>\r\n          </div>\r\n        </ng-container>\r\n      </div>\r\n    </div>\r\n  </div> <!-- 選擇器 -->\r\n  <div class=\"selector-container\">\r\n    <button type=\"button\" class=\"selector-button\" [class.disabled]=\"disabled || isLoading\"\r\n      [disabled]=\"disabled || isLoading\" (click)=\"toggleDropdown()\"\r\n      style=\"width: 100%; display: flex; align-items: center; justify-content: space-between; padding: 0.5rem 0.75rem; border: 1px solid #ced4da; border-radius: 0.375rem; background-color: #fff; cursor: pointer;\">\r\n      <span class=\"selector-text\">\r\n        <ng-container *ngIf=\"isLoading\">\r\n          <nb-icon icon=\"loader-outline\" class=\"spin\"></nb-icon>\r\n          載入中...\r\n        </ng-container>\r\n        <ng-container *ngIf=\"!isLoading\">\r\n          {{getSelectedCount() > 0 ? '已選擇 ' + getSelectedCount() + ' 個戶別' : placeholder}}\r\n        </ng-container>\r\n      </span>\r\n      <nb-icon icon=\"home-outline\" class=\"chevron-icon\"></nb-icon>\r\n    </button>\r\n  </div>\r\n</div>\r\n\r\n<!-- 戶別選擇對話框 -->\r\n<ng-template #householdDialog let-dialog let-ref=\"dialogRef\">\r\n  <nb-card style=\"width: 95vw; max-width: 1200px; max-height: 90vh;\">\r\n    <nb-card-header>\r\n      <div style=\"display: flex; align-items: center; justify-content: space-between;\">\r\n        <div style=\"display: flex; align-items: center; gap: 8px;\">\r\n          <nb-icon icon=\"home-outline\" style=\"color: #007bff; font-size: 1.5rem;\"></nb-icon>\r\n          <span style=\"font-weight: 500; color: #495057; font-size: 1.25rem;\">選擇戶別</span>\r\n          <span style=\"font-size: 0.875rem; color: #6c757d;\">({{buildings.length}} 個棟別)</span>\r\n        </div>\r\n        <span style=\"font-size: 0.875rem; color: #6c757d;\">已選擇: {{getSelectedCount()}}</span>\r\n      </div>\r\n    </nb-card-header>\r\n\r\n    <nb-card-body style=\"padding: 0; overflow: hidden;\">\r\n      <!-- 載入狀態 -->\r\n      <div *ngIf=\"isLoading\"\r\n        style=\"width: 100%; display: flex; align-items: center; justify-content: center; padding: 40px;\">\r\n        <div style=\"text-align: center; color: #6c757d;\">\r\n          <nb-icon icon=\"loader-outline\" class=\"spin\" style=\"font-size: 2rem; margin-bottom: 8px;\"></nb-icon>\r\n          <p style=\"margin: 0; font-size: 0.875rem;\">載入戶別資料中...</p>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 主要內容區域 -->\r\n      <div *ngIf=\"!isLoading\" style=\"display: flex; height: 60vh; min-height: 400px;\">\r\n        <!-- 棟別選擇側邊欄 -->\r\n        <div\r\n          style=\"width: 300px; border-right: 1px solid #e9ecef; background-color: #f8f9fa; display: flex; flex-direction: column;\">\r\n          <div style=\"padding: 12px 16px; border-bottom: 1px solid #e9ecef;\">\r\n            <h6 style=\"margin: 0; font-size: 0.875rem; font-weight: 500; color: #495057;\">棟別列表</h6>\r\n          </div>\r\n          <div style=\"flex: 1; overflow-y: auto;\">\r\n            <button *ngFor=\"let building of buildings\" type=\"button\" (click)=\"onBuildingSelect(building)\"\r\n              [style.background-color]=\"selectedBuilding === building ? '#e3f2fd' : 'transparent'\"\r\n              [style.border-left]=\"selectedBuilding === building ? '3px solid #007bff' : '3px solid transparent'\"\r\n              style=\"width: 100%; text-align: left; padding: 12px 16px; border: none; cursor: pointer; transition: all 0.15s ease; display: flex; align-items: center; justify-content: space-between;\">\r\n              <span style=\"font-weight: 500; color: #495057;\">{{building}}</span>\r\n              <span\r\n                style=\"font-size: 0.75rem; color: #6c757d; background-color: #e9ecef; padding: 2px 6px; border-radius: 10px;\">\r\n                {{getBuildingCount(building)}}戶\r\n              </span>\r\n            </button>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 戶別選擇主區域 -->\r\n        <div style=\"flex: 1; display: flex; flex-direction: column;\">\r\n          <div style=\"padding: 12px 16px; border-bottom: 1px solid #e9ecef;\">\r\n            <div style=\"display: flex; align-items: center; justify-content: space-between; margin-bottom: 8px;\">\r\n              <h6 style=\"margin: 0; font-size: 0.875rem; font-weight: 500; color: #495057;\">\r\n                戶別選擇\r\n                <span *ngIf=\"selectedBuilding\" style=\"color: #007bff;\">({{selectedBuilding}})</span>\r\n                <span *ngIf=\"selectedFloor\" style=\"color: #28a745; font-size: 0.75rem;\"> - {{selectedFloor}}</span>\r\n              </h6>\r\n              <div\r\n                *ngIf=\"allowBatchSelect && selectedBuilding && buildingData[selectedBuilding] && buildingData[selectedBuilding].length > 0\"\r\n                style=\"display: flex; gap: 4px;\">\r\n                <button type=\"button\" [disabled]=\"!canSelectMore()\" (click)=\"onSelectAllFiltered()\"\r\n                  [style.opacity]=\"canSelectMore() ? '1' : '0.5'\"\r\n                  style=\"padding: 4px 8px; font-size: 0.75rem; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer;\">\r\n                  全選當前\r\n                </button>\r\n                <button type=\"button\" [disabled]=\"!canSelectMore()\" (click)=\"onSelectAllBuilding()\"\r\n                  [style.opacity]=\"canSelectMore() ? '1' : '0.5'\"\r\n                  style=\"padding: 4px 8px; font-size: 0.75rem; background: #28a745; color: white; border: none; border-radius: 4px; cursor: pointer;\">\r\n                  全選{{selectedBuilding}}\r\n                </button>\r\n                <button type=\"button\" *ngIf=\"isSomeBuildingSelected()\" (click)=\"onUnselectAllBuilding()\"\r\n                  style=\"padding: 4px 8px; font-size: 0.75rem; background: #6c757d; color: white; border: none; border-radius: 4px; cursor: pointer;\">\r\n                  清除\r\n                </button>\r\n              </div>\r\n            </div>\r\n\r\n            <!-- 搜尋框 -->\r\n            <div *ngIf=\"allowSearch && selectedBuilding\" style=\"margin-top: 8px;\">\r\n              <div style=\"position: relative;\">\r\n                <input type=\"text\" [(ngModel)]=\"searchTerm\" (input)=\"onSearchChange($event)\" placeholder=\"搜尋戶別代碼...\"\r\n                  style=\"width: 100%; padding: 6px 32px 6px 12px; border: 1px solid #ced4da; border-radius: 4px; font-size: 0.875rem; outline: none;\">\r\n                <nb-icon icon=\"search-outline\"\r\n                  style=\"position: absolute; right: 10px; top: 50%; transform: translateY(-50%); color: #6c757d; font-size: 0.875rem;\"></nb-icon>\r\n              </div>\r\n              <div *ngIf=\"searchTerm && hasNoSearchResults()\"\r\n                style=\"font-size: 0.75rem; color: #dc3545; margin-top: 4px;\">\r\n                找不到符合 \"{{searchTerm}}\" 的戶別\r\n              </div>\r\n            </div>\r\n\r\n            <!-- 樓層篩選器 -->\r\n            <div *ngIf=\"selectedBuilding && floors.length > 1\" style=\"margin-top: 12px;\">\r\n              <div style=\"display: flex; align-items: center; gap: 8px; margin-bottom: 8px;\">\r\n                <nb-icon icon=\"layers-outline\" style=\"color: #6c757d; font-size: 0.875rem;\"></nb-icon>\r\n                <span style=\"font-size: 0.875rem; font-weight: 500; color: #495057;\">樓層篩選:</span>\r\n                <button type=\"button\" *ngIf=\"selectedFloor\" (click)=\"onFloorSelect('')\"\r\n                  style=\"font-size: 0.75rem; color: #007bff; background: none; border: none; text-decoration: underline; cursor: pointer;\">\r\n                  清除篩選\r\n                </button>\r\n              </div>\r\n              <div style=\"display: flex; flex-wrap: wrap; gap: 4px; max-height: 100px; overflow-y: auto;\">\r\n                <button type=\"button\" *ngFor=\"let floor of floors\" (click)=\"onFloorSelect(floor)\"\r\n                  [style.background-color]=\"selectedFloor === floor ? '#007bff' : '#f8f9fa'\"\r\n                  [style.color]=\"selectedFloor === floor ? '#fff' : '#495057'\"\r\n                  [style.border-color]=\"selectedFloor === floor ? '#007bff' : '#ced4da'\"\r\n                  style=\"padding: 4px 8px; border: 1px solid; border-radius: 4px; font-size: 0.75rem; cursor: pointer; transition: all 0.15s ease; white-space: nowrap;\">\r\n                  {{floor}} ({{getFloorCount(floor)}})\r\n                </button>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 戶別網格或空狀態 -->\r\n          <div style=\"flex: 1; padding: 16px; overflow-y: auto;\">\r\n            <div *ngIf=\"!selectedBuilding\" style=\"text-align: center; padding: 40px 20px; color: #6c757d;\">\r\n              <nb-icon icon=\"home-outline\" style=\"font-size: 2rem; margin-bottom: 8px; opacity: 0.5;\"></nb-icon>\r\n              <p style=\"margin: 0; font-size: 0.875rem;\">請先選擇棟別</p>\r\n            </div>\r\n            <div *ngIf=\"selectedBuilding && buildingData[selectedBuilding] && buildingData[selectedBuilding].length > 0\"\r\n              style=\"display: grid; grid-template-columns: repeat(auto-fill, minmax(90px, 1fr)); gap: 8px;\">\r\n              <ng-container *ngFor=\"let household of buildingData[selectedBuilding]\">\r\n                <button *ngIf=\"(!selectedFloor || household.floor === selectedFloor) &&\r\n                              (!searchTerm || household.houseName.toLowerCase().includes(searchTerm.toLowerCase()))\"\r\n                  type=\"button\" (click)=\"onHouseholdToggle(household.houseName)\"\r\n                  [disabled]=\"isHouseholdDisabled(household.houseName)\"\r\n                  [style.background-color]=\"isHouseholdSelected(household.houseName) ? '#007bff' : (isHouseholdExcluded(household.houseName) ? '#f8f9fa' : '#fff')\"\r\n                  [style.color]=\"isHouseholdSelected(household.houseName) ? '#fff' : (isHouseholdExcluded(household.houseName) ? '#6c757d' : '#495057')\"\r\n                  [style.border-color]=\"isHouseholdSelected(household.houseName) ? '#007bff' : (isHouseholdExcluded(household.houseName) ? '#dee2e6' : '#ced4da')\"\r\n                  [style.opacity]=\"isHouseholdDisabled(household.houseName) ? '0.6' : '1'\"\r\n                  [style.cursor]=\"isHouseholdDisabled(household.houseName) ? 'not-allowed' : 'pointer'\"\r\n                  style=\"padding: 6px 4px; border: 1px solid; border-radius: 4px; transition: all 0.15s ease; font-size: 0.75rem; text-align: center; min-height: 40px; position: relative; display: flex; flex-direction: column; justify-content: center;\">\r\n                  <span [style.text-decoration]=\"isHouseholdExcluded(household.houseName) ? 'line-through' : 'none'\"\r\n                    style=\"font-weight: 500; line-height: 1.2;\">\r\n                    {{household.houseName}}\r\n                  </span>\r\n                  <span *ngIf=\"household.floor\" style=\"font-size: 0.6rem; opacity: 0.8; margin-top: 2px;\">\r\n                    {{household.floor}}\r\n                  </span>\r\n                  <div *ngIf=\"isHouseholdExcluded(household.houseName)\"\r\n                    style=\"position: absolute; top: -8px; right: -8px; background: #dc3545; color: white; border-radius: 50%; width: 16px; height: 16px; font-size: 10px; display: flex; align-items: center; justify-content: center;\">\r\n                    ✕\r\n                  </div>\r\n                </button>\r\n              </ng-container>\r\n            </div>\r\n            <div\r\n              *ngIf=\"selectedBuilding && (!buildingData[selectedBuilding] || buildingData[selectedBuilding].length === 0) && !searchTerm\"\r\n              style=\"text-align: center; padding: 40px 20px; color: #6c757d;\">\r\n              <nb-icon icon=\"alert-circle-outline\" style=\"font-size: 2rem; margin-bottom: 8px; opacity: 0.5;\"></nb-icon>\r\n              <p style=\"margin: 0; font-size: 0.875rem;\">此棟別沒有可用的戶別</p>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </nb-card-body>\r\n\r\n    <nb-card-footer style=\"padding: 16px; border-top: 1px solid #e9ecef; background-color: #f8f9fa;\">\r\n      <!-- 統計資訊行 -->\r\n      <div style=\"display: flex; align-items: center; justify-content: space-between; margin-bottom: 12px;\">\r\n        <div style=\"display: flex; align-items: center; gap: 16px; font-size: 0.875rem; color: #495057;\">\r\n          <div style=\"display: flex; align-items: center; gap: 4px;\">\r\n            <nb-icon icon=\"checkmark-circle-outline\" style=\"color: #28a745;\"></nb-icon>\r\n            <span>已選擇: <strong>{{getSelectedCount()}}</strong> 個戶別</span>\r\n          </div>\r\n          <div *ngIf=\"maxSelections\" style=\"display: flex; align-items: center; gap: 4px;\">\r\n            <nb-icon icon=\"alert-circle-outline\" style=\"color: #ffc107;\"></nb-icon>\r\n            <span>限制: 最多 <strong>{{maxSelections}}</strong> 個</span>\r\n          </div>\r\n          <div *ngIf=\"selectedBuilding\" style=\"display: flex; align-items: center; gap: 4px;\">\r\n            <nb-icon icon=\"home-outline\" style=\"color: #007bff;\"></nb-icon>\r\n            <span>當前棟別: <strong>{{selectedBuilding}}</strong></span>\r\n            <span *ngIf=\"selectedFloor\" style=\"color: #28a745; margin-left: 8px;\">\r\n              <nb-icon icon=\"layers-outline\" style=\"color: #28a745;\"></nb-icon>\r\n              {{selectedFloor}}\r\n            </span>\r\n          </div>\r\n        </div>\r\n        <div *ngIf=\"searchTerm\"\r\n          style=\"font-size: 0.75rem; color: #6c757d; background-color: #e9ecef; padding: 4px 8px; border-radius: 12px;\">\r\n          搜尋: \"{{searchTerm}}\" ({{getFilteredHouseholdsCount()}} 個結果)\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 操作按鈕行 -->\r\n      <div style=\"display: flex; align-items: center; justify-content: space-between; gap: 8px;\">\r\n        <div style=\"display: flex; gap: 8px;\">\r\n          <button type=\"button\" *ngIf=\"selectedHouseholds.length > 0\" (click)=\"onClearAll()\"\r\n            style=\"padding: 8px 12px; background: #dc3545; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 0.875rem; display: flex; align-items: center; gap: 4px;\">\r\n            <nb-icon icon=\"trash-2-outline\"></nb-icon>\r\n            清空全部\r\n          </button>\r\n          <button type=\"button\" *ngIf=\"allowSearch && searchTerm\" (click)=\"resetSearch()\"\r\n            style=\"padding: 8px 12px; background: #6c757d; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 0.875rem; display: flex; align-items: center; gap: 4px;\">\r\n            <nb-icon icon=\"refresh-outline\"></nb-icon>\r\n            重置搜尋\r\n          </button>\r\n        </div>\r\n\r\n        <div style=\"display: flex; gap: 8px;\">\r\n          <button type=\"button\" (click)=\"ref.close()\"\r\n            style=\"padding: 8px 16px; background: #6c757d; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 0.875rem;\">\r\n            取消\r\n          </button>\r\n          <button type=\"button\" (click)=\"ref.close()\"\r\n            style=\"padding: 8px 20px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 0.875rem; font-weight: 500; display: flex; align-items: center; gap: 4px;\">\r\n            <nb-icon icon=\"checkmark-outline\"></nb-icon>\r\n            確定選擇 ({{getSelectedCount()}})\r\n          </button>\r\n        </div>\r\n      </div>\r\n    </nb-card-footer>\r\n  </nb-card>\r\n</ng-template>"], "mappings": "AAAA,SAAmCA,YAAY,EAAoCC,UAAU,QAAmD,eAAe;AAC/J,SAA+BC,iBAAiB,QAAQ,gBAAgB;;;;;;;;;ICmBxDC,EAAA,CAAAC,cAAA,eAA4E;IAC1ED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IADLH,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAC,MAAA,CAAAC,gBAAA,CAAAC,gBAAA,EAAAC,KAAA,MACF;;;;;;IAHAT,EAFJ,CAAAC,cAAA,eAAkG,cACpE,eACG;IAAAD,EAAA,CAAAE,MAAA,GAA6C;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACjFH,EAAA,CAAAU,UAAA,IAAAC,2EAAA,mBAA4E;IAG9EX,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,iBAA0G;IAA3CD,EAAA,CAAAY,UAAA,mBAAAC,6FAAA;MAAA,MAAAL,gBAAA,GAAAR,EAAA,CAAAc,aAAA,CAAAC,GAAA,EAAAC,SAAA;MAAA,MAAAV,MAAA,GAAAN,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAASZ,MAAA,CAAAa,iBAAA,CAAAX,gBAAA,CAAgC;IAAA,EAAC;IACvGR,EAAA,CAAAoB,SAAA,kBAAwC;IAE5CpB,EADE,CAAAG,YAAA,EAAS,EACJ;;;;;IAR0BH,EAAA,CAAAI,SAAA,GAA6C;IAA7CJ,EAAA,CAAAqB,iBAAA,CAAAf,MAAA,CAAAC,gBAAA,CAAAC,gBAAA,EAAAc,SAAA,CAA6C;IACnEtB,EAAA,CAAAI,SAAA,EAA2C;IAA3CJ,EAAA,CAAAuB,UAAA,SAAAjB,MAAA,CAAAC,gBAAA,CAAAC,gBAAA,EAAAC,KAAA,CAA2C;IAIXT,EAAA,CAAAI,SAAA,EAAqB;IAArBJ,EAAA,CAAAuB,UAAA,aAAAjB,MAAA,CAAAkB,QAAA,CAAqB;;;;;IAVpExB,EAAA,CAAAyB,uBAAA,GAAoD;IAClDzB,EAAA,CAAAC,cAAA,cAA4B;IAAAD,EAAA,CAAAE,MAAA,GAAa;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAC/CH,EAAA,CAAAC,cAAA,cAA6B;IAC3BD,EAAA,CAAAU,UAAA,IAAAgB,oEAAA,mBAAkG;IAWpG1B,EAAA,CAAAG,YAAA,EAAM;;;;;;IAbsBH,EAAA,CAAAI,SAAA,GAAa;IAAbJ,EAAA,CAAAK,kBAAA,KAAAsB,WAAA,MAAa;IAEP3B,EAAA,CAAAI,SAAA,GAA0C;IAA1CJ,EAAA,CAAAuB,UAAA,YAAAjB,MAAA,CAAAsB,6BAAA,CAAAD,WAAA,EAA0C;;;;;IAJhF3B,EAAA,CAAAC,cAAA,cAA+D;IAC7DD,EAAA,CAAAU,UAAA,IAAAmB,6DAAA,0BAAoD;IAgBtD7B,EAAA,CAAAG,YAAA,EAAM;;;;;IAhBWH,EAAA,CAAAI,SAAA,EAAmC;IAAnCJ,EAAA,CAAAuB,UAAA,SAAAjB,MAAA,CAAAwB,mBAAA,CAAAH,WAAA,EAAmC;;;;;;IAVpD3B,EAFJ,CAAAC,cAAA,aAAgG,aACjE,cACA;IACzBD,EAAA,CAAAoB,SAAA,kBAA8D;IAC9DpB,EAAA,CAAAC,cAAA,eAA6B;IAAAD,EAAA,CAAAE,MAAA,GAA8B;IAC7DF,EAD6D,CAAAG,YAAA,EAAO,EAC9D;IACNH,EAAA,CAAAC,cAAA,iBAAyG;IAAvBD,EAAA,CAAAY,UAAA,mBAAAmB,iEAAA;MAAA/B,EAAA,CAAAc,aAAA,CAAAkB,GAAA;MAAA,MAAA1B,MAAA,GAAAN,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAASZ,MAAA,CAAA2B,UAAA,EAAY;IAAA,EAAC;IACtGjC,EAAA,CAAAE,MAAA,iCACF;IACFF,EADE,CAAAG,YAAA,EAAS,EACL;IACNH,EAAA,CAAAC,cAAA,cAA8B;IAC5BD,EAAA,CAAAU,UAAA,IAAAwB,8CAAA,kBAA+D;IAmBnElC,EADE,CAAAG,YAAA,EAAM,EACF;;;;IA1B6BH,EAAA,CAAAI,SAAA,GAA8B;IAA9BJ,EAAA,CAAAK,kBAAA,qCAAAC,MAAA,CAAA6B,gBAAA,QAA8B;IAEDnC,EAAA,CAAAI,SAAA,EAAqB;IAArBJ,EAAA,CAAAuB,UAAA,aAAAjB,MAAA,CAAAkB,QAAA,CAAqB;IAKvDxB,EAAA,CAAAI,SAAA,GAAY;IAAZJ,EAAA,CAAAuB,UAAA,YAAAjB,MAAA,CAAA8B,SAAA,CAAY;;;;;IAyBpCpC,EAAA,CAAAyB,uBAAA,GAAgC;IAC9BzB,EAAA,CAAAoB,SAAA,kBAAsD;IACtDpB,EAAA,CAAAE,MAAA,8BACF;;;;;;IACAF,EAAA,CAAAyB,uBAAA,GAAiC;IAC/BzB,EAAA,CAAAE,MAAA,GACF;;;;;IADEF,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAC,MAAA,CAAA6B,gBAAA,iCAAA7B,MAAA,CAAA6B,gBAAA,6BAAA7B,MAAA,CAAA+B,WAAA,MACF;;;;;IAyBArC,EAFF,CAAAC,cAAA,cACmG,cAChD;IAC/CD,EAAA,CAAAoB,SAAA,kBAAmG;IACnGpB,EAAA,CAAAC,cAAA,YAA2C;IAAAD,EAAA,CAAAE,MAAA,oDAAU;IAEzDF,EAFyD,CAAAG,YAAA,EAAI,EACrD,EACF;;;;;;IAWAH,EAAA,CAAAC,cAAA,iBAG4L;IAHnID,EAAA,CAAAY,UAAA,mBAAA0B,yFAAA;MAAA,MAAAC,WAAA,GAAAvC,EAAA,CAAAc,aAAA,CAAA0B,GAAA,EAAAxB,SAAA;MAAA,MAAAV,MAAA,GAAAN,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAASZ,MAAA,CAAAmC,gBAAA,CAAAF,WAAA,CAA0B;IAAA,EAAC;IAI3FvC,EAAA,CAAAC,cAAA,eAAgD;IAAAD,EAAA,CAAAE,MAAA,GAAY;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACnEH,EAAA,CAAAC,cAAA,eACgH;IAC9GD,EAAA,CAAAE,MAAA,GACF;IACFF,EADE,CAAAG,YAAA,EAAO,EACA;;;;;IAPPH,EADA,CAAA0C,WAAA,qBAAApC,MAAA,CAAAqC,gBAAA,KAAAJ,WAAA,6BAAoF,gBAAAjC,MAAA,CAAAqC,gBAAA,KAAAJ,WAAA,iDACe;IAEnDvC,EAAA,CAAAI,SAAA,GAAY;IAAZJ,EAAA,CAAAqB,iBAAA,CAAAkB,WAAA,CAAY;IAG1DvC,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAC,MAAA,CAAAsC,gBAAA,CAAAL,WAAA,aACF;;;;;IAWEvC,EAAA,CAAAC,cAAA,eAAuD;IAAAD,EAAA,CAAAE,MAAA,GAAsB;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAA7BH,EAAA,CAAAI,SAAA,EAAsB;IAAtBJ,EAAA,CAAAK,kBAAA,MAAAC,MAAA,CAAAqC,gBAAA,MAAsB;;;;;IAC7E3C,EAAA,CAAAC,cAAA,eAAwE;IAACD,EAAA,CAAAE,MAAA,GAAmB;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAA1BH,EAAA,CAAAI,SAAA,EAAmB;IAAnBJ,EAAA,CAAAK,kBAAA,QAAAC,MAAA,CAAAuC,aAAA,KAAmB;;;;;;IAe5F7C,EAAA,CAAAC,cAAA,iBACsI;IAD/ED,EAAA,CAAAY,UAAA,mBAAAkC,gGAAA;MAAA9C,EAAA,CAAAc,aAAA,CAAAiC,IAAA;MAAA,MAAAzC,MAAA,GAAAN,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAASZ,MAAA,CAAA0C,qBAAA,EAAuB;IAAA,EAAC;IAEtFhD,EAAA,CAAAE,MAAA,qBACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;IAbTH,EAHF,CAAAC,cAAA,cAEmC,iBAGqG;IAFlFD,EAAA,CAAAY,UAAA,mBAAAqC,uFAAA;MAAAjD,EAAA,CAAAc,aAAA,CAAAoC,IAAA;MAAA,MAAA5C,MAAA,GAAAN,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAASZ,MAAA,CAAA6C,mBAAA,EAAqB;IAAA,EAAC;IAGjFnD,EAAA,CAAAE,MAAA,iCACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,iBAEsI;IAFlFD,EAAA,CAAAY,UAAA,mBAAAwC,uFAAA;MAAApD,EAAA,CAAAc,aAAA,CAAAoC,IAAA;MAAA,MAAA5C,MAAA,GAAAN,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAASZ,MAAA,CAAA+C,mBAAA,EAAqB;IAAA,EAAC;IAGjFrD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAU,UAAA,IAAA4C,uEAAA,qBACsI;IAGxItD,EAAA,CAAAG,YAAA,EAAM;;;;IAbFH,EAAA,CAAAI,SAAA,EAA+C;IAA/CJ,EAAA,CAAA0C,WAAA,YAAApC,MAAA,CAAAiD,aAAA,iBAA+C;IAD3BvD,EAAA,CAAAuB,UAAA,cAAAjB,MAAA,CAAAiD,aAAA,GAA6B;IAMjDvD,EAAA,CAAAI,SAAA,GAA+C;IAA/CJ,EAAA,CAAA0C,WAAA,YAAApC,MAAA,CAAAiD,aAAA,iBAA+C;IAD3BvD,EAAA,CAAAuB,UAAA,cAAAjB,MAAA,CAAAiD,aAAA,GAA6B;IAGjDvD,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAK,kBAAA,kBAAAC,MAAA,CAAAqC,gBAAA,MACF;IACuB3C,EAAA,CAAAI,SAAA,EAA8B;IAA9BJ,EAAA,CAAAuB,UAAA,SAAAjB,MAAA,CAAAkD,sBAAA,GAA8B;;;;;IAevDxD,EAAA,CAAAC,cAAA,cAC+D;IAC7DD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADJH,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAK,kBAAA,uCAAAC,MAAA,CAAAmD,UAAA,2BACF;;;;;;IAREzD,EAFJ,CAAAC,cAAA,cAAsE,cACnC,gBAEuG;IADnHD,EAAA,CAAA0D,gBAAA,2BAAAC,8FAAAC,MAAA;MAAA5D,EAAA,CAAAc,aAAA,CAAA+C,IAAA;MAAA,MAAAvD,MAAA,GAAAN,EAAA,CAAAiB,aAAA;MAAAjB,EAAA,CAAA8D,kBAAA,CAAAxD,MAAA,CAAAmD,UAAA,EAAAG,MAAA,MAAAtD,MAAA,CAAAmD,UAAA,GAAAG,MAAA;MAAA,OAAA5D,EAAA,CAAAkB,WAAA,CAAA0C,MAAA;IAAA,EAAwB;IAAC5D,EAAA,CAAAY,UAAA,mBAAAmD,sFAAAH,MAAA;MAAA5D,EAAA,CAAAc,aAAA,CAAA+C,IAAA;MAAA,MAAAvD,MAAA,GAAAN,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAASZ,MAAA,CAAA0D,cAAA,CAAAJ,MAAA,CAAsB;IAAA,EAAC;IAA5E5D,EAAA,CAAAG,YAAA,EACsI;IACtIH,EAAA,CAAAoB,SAAA,kBACiI;IACnIpB,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAU,UAAA,IAAAuD,oEAAA,kBAC+D;IAGjEjE,EAAA,CAAAG,YAAA,EAAM;;;;IATiBH,EAAA,CAAAI,SAAA,GAAwB;IAAxBJ,EAAA,CAAAkE,gBAAA,YAAA5D,MAAA,CAAAmD,UAAA,CAAwB;IAKvCzD,EAAA,CAAAI,SAAA,GAAwC;IAAxCJ,EAAA,CAAAuB,UAAA,SAAAjB,MAAA,CAAAmD,UAAA,IAAAnD,MAAA,CAAA6D,kBAAA,GAAwC;;;;;;IAW5CnE,EAAA,CAAAC,cAAA,iBAC2H;IAD/ED,EAAA,CAAAY,UAAA,mBAAAwD,gGAAA;MAAApE,EAAA,CAAAc,aAAA,CAAAuD,IAAA;MAAA,MAAA/D,MAAA,GAAAN,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAASZ,MAAA,CAAAgE,aAAA,CAAc,EAAE,CAAC;IAAA,EAAC;IAErEtE,EAAA,CAAAE,MAAA,iCACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;IAGTH,EAAA,CAAAC,cAAA,iBAIyJ;IAJtGD,EAAA,CAAAY,UAAA,mBAAA2D,gGAAA;MAAA,MAAAC,SAAA,GAAAxE,EAAA,CAAAc,aAAA,CAAA2D,IAAA,EAAAzD,SAAA;MAAA,MAAAV,MAAA,GAAAN,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAASZ,MAAA,CAAAgE,aAAA,CAAAE,SAAA,CAAoB;IAAA,EAAC;IAK/ExE,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;IAHPH,EAFA,CAAA0C,WAAA,qBAAApC,MAAA,CAAAuC,aAAA,KAAA2B,SAAA,yBAA0E,UAAAlE,MAAA,CAAAuC,aAAA,KAAA2B,SAAA,sBACd,iBAAAlE,MAAA,CAAAuC,aAAA,KAAA2B,SAAA,yBACU;IAEtExE,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAA0E,kBAAA,MAAAF,SAAA,QAAAlE,MAAA,CAAAqE,aAAA,CAAAH,SAAA,QACF;;;;;IAfFxE,EADF,CAAAC,cAAA,cAA6E,cACI;IAC7ED,EAAA,CAAAoB,SAAA,kBAAsF;IACtFpB,EAAA,CAAAC,cAAA,eAAqE;IAAAD,EAAA,CAAAE,MAAA,gCAAK;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACjFH,EAAA,CAAAU,UAAA,IAAAkE,uEAAA,qBAC2H;IAG7H5E,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,cAA4F;IAC1FD,EAAA,CAAAU,UAAA,IAAAmE,uEAAA,qBAIyJ;IAI7J7E,EADE,CAAAG,YAAA,EAAM,EACF;;;;IAdqBH,EAAA,CAAAI,SAAA,GAAmB;IAAnBJ,EAAA,CAAAuB,UAAA,SAAAjB,MAAA,CAAAuC,aAAA,CAAmB;IAMF7C,EAAA,CAAAI,SAAA,GAAS;IAATJ,EAAA,CAAAuB,UAAA,YAAAjB,MAAA,CAAAwE,MAAA,CAAS;;;;;IAarD9E,EAAA,CAAAC,cAAA,cAA+F;IAC7FD,EAAA,CAAAoB,SAAA,kBAAkG;IAClGpB,EAAA,CAAAC,cAAA,YAA2C;IAAAD,EAAA,CAAAE,MAAA,2CAAM;IACnDF,EADmD,CAAAG,YAAA,EAAI,EACjD;;;;;IAkBAH,EAAA,CAAAC,cAAA,gBAAwF;IACtFD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IADLH,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAK,kBAAA,MAAA0E,aAAA,CAAAtE,KAAA,MACF;;;;;IACAT,EAAA,CAAAC,cAAA,eACsN;IACpND,EAAA,CAAAE,MAAA,eACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;;IApBRH,EAAA,CAAAC,cAAA,kBAS6O;IAP7ND,EAAA,CAAAY,UAAA,mBAAAoE,+GAAA;MAAAhF,EAAA,CAAAc,aAAA,CAAAmE,IAAA;MAAA,MAAAF,aAAA,GAAA/E,EAAA,CAAAiB,aAAA,GAAAD,SAAA;MAAA,MAAAV,MAAA,GAAAN,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAASZ,MAAA,CAAA4E,iBAAA,CAAAH,aAAA,CAAAzD,SAAA,CAAsC;IAAA,EAAC;IAQ9DtB,EAAA,CAAAC,cAAA,gBAC8C;IAC5CD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAIPH,EAHA,CAAAU,UAAA,IAAAyE,6FAAA,oBAAwF,IAAAC,4FAAA,mBAI8H;IAGxNpF,EAAA,CAAAG,YAAA,EAAS;;;;;IAbPH,EAJA,CAAA0C,WAAA,qBAAApC,MAAA,CAAA+E,mBAAA,CAAAN,aAAA,CAAAzD,SAAA,gBAAAhB,MAAA,CAAAgF,mBAAA,CAAAP,aAAA,CAAAzD,SAAA,uBAAiJ,UAAAhB,MAAA,CAAA+E,mBAAA,CAAAN,aAAA,CAAAzD,SAAA,aAAAhB,MAAA,CAAAgF,mBAAA,CAAAP,aAAA,CAAAzD,SAAA,0BACX,iBAAAhB,MAAA,CAAA+E,mBAAA,CAAAN,aAAA,CAAAzD,SAAA,gBAAAhB,MAAA,CAAAgF,mBAAA,CAAAP,aAAA,CAAAzD,SAAA,0BACU,YAAAhB,MAAA,CAAAiF,mBAAA,CAAAR,aAAA,CAAAzD,SAAA,gBACxE,WAAAhB,MAAA,CAAAiF,mBAAA,CAAAR,aAAA,CAAAzD,SAAA,8BACa;IALrFtB,EAAA,CAAAuB,UAAA,aAAAjB,MAAA,CAAAiF,mBAAA,CAAAR,aAAA,CAAAzD,SAAA,EAAqD;IAO/CtB,EAAA,CAAAI,SAAA,EAA4F;IAA5FJ,EAAA,CAAA0C,WAAA,oBAAApC,MAAA,CAAAgF,mBAAA,CAAAP,aAAA,CAAAzD,SAAA,4BAA4F;IAEhGtB,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAK,kBAAA,MAAA0E,aAAA,CAAAzD,SAAA,MACF;IACOtB,EAAA,CAAAI,SAAA,EAAqB;IAArBJ,EAAA,CAAAuB,UAAA,SAAAwD,aAAA,CAAAtE,KAAA,CAAqB;IAGtBT,EAAA,CAAAI,SAAA,EAA8C;IAA9CJ,EAAA,CAAAuB,UAAA,SAAAjB,MAAA,CAAAgF,mBAAA,CAAAP,aAAA,CAAAzD,SAAA,EAA8C;;;;;IAlBxDtB,EAAA,CAAAyB,uBAAA,GAAuE;IACrEzB,EAAA,CAAAU,UAAA,IAAA8E,sFAAA,uBAS6O;;;;;;IATpOxF,EAAA,CAAAI,SAAA,EACyF;IADzFJ,EAAA,CAAAuB,UAAA,WAAAjB,MAAA,CAAAuC,aAAA,IAAAkC,aAAA,CAAAtE,KAAA,KAAAH,MAAA,CAAAuC,aAAA,OAAAvC,MAAA,CAAAmD,UAAA,IAAAsB,aAAA,CAAAzD,SAAA,CAAAmE,WAAA,GAAAC,QAAA,CAAApF,MAAA,CAAAmD,UAAA,CAAAgC,WAAA,KACyF;;;;;IAJtGzF,EAAA,CAAAC,cAAA,cACgG;IAC9FD,EAAA,CAAAU,UAAA,IAAAiF,6EAAA,2BAAuE;IAwBzE3F,EAAA,CAAAG,YAAA,EAAM;;;;IAxBgCH,EAAA,CAAAI,SAAA,EAAiC;IAAjCJ,EAAA,CAAAuB,UAAA,YAAAjB,MAAA,CAAAsF,YAAA,CAAAtF,MAAA,CAAAqC,gBAAA,EAAiC;;;;;IAyBvE3C,EAAA,CAAAC,cAAA,cAEkE;IAChED,EAAA,CAAAoB,SAAA,mBAA0G;IAC1GpB,EAAA,CAAAC,cAAA,YAA2C;IAAAD,EAAA,CAAAE,MAAA,mEAAU;IACvDF,EADuD,CAAAG,YAAA,EAAI,EACrD;;;;;IAvHNH,EALN,CAAAC,cAAA,cAAgF,cAG6C,cACtD,aACa;IAAAD,EAAA,CAAAE,MAAA,+BAAI;IACpFF,EADoF,CAAAG,YAAA,EAAK,EACnF;IACNH,EAAA,CAAAC,cAAA,cAAwC;IACtCD,EAAA,CAAAU,UAAA,IAAAmF,gEAAA,qBAG4L;IAQhM7F,EADE,CAAAG,YAAA,EAAM,EACF;IAMAH,EAHN,CAAAC,cAAA,cAA6D,cACQ,cACoC,cACrB;IAC5ED,EAAA,CAAAE,MAAA,kCACA;IACAF,EADA,CAAAU,UAAA,KAAAoF,+DAAA,mBAAuD,KAAAC,+DAAA,mBACiB;IAC1E/F,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAU,UAAA,KAAAsF,8DAAA,kBAEmC;IAgBrChG,EAAA,CAAAG,YAAA,EAAM;IAiBNH,EAdA,CAAAU,UAAA,KAAAuF,8DAAA,kBAAsE,KAAAC,8DAAA,kBAcO;IAmB/ElG,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAAC,cAAA,eAAuD;IAgCrDD,EA/BA,CAAAU,UAAA,KAAAyF,8DAAA,kBAA+F,KAAAC,8DAAA,kBAKC,KAAAC,8DAAA,kBA4B9B;IAMxErG,EAFI,CAAAG,YAAA,EAAM,EACF,EACF;;;;IAvH6BH,EAAA,CAAAI,SAAA,GAAY;IAAZJ,EAAA,CAAAuB,UAAA,YAAAjB,MAAA,CAAA8B,SAAA,CAAY;IAmB9BpC,EAAA,CAAAI,SAAA,GAAsB;IAAtBJ,EAAA,CAAAuB,UAAA,SAAAjB,MAAA,CAAAqC,gBAAA,CAAsB;IACtB3C,EAAA,CAAAI,SAAA,EAAmB;IAAnBJ,EAAA,CAAAuB,UAAA,SAAAjB,MAAA,CAAAuC,aAAA,CAAmB;IAGzB7C,EAAA,CAAAI,SAAA,EAAyH;IAAzHJ,EAAA,CAAAuB,UAAA,SAAAjB,MAAA,CAAAgG,gBAAA,IAAAhG,MAAA,CAAAqC,gBAAA,IAAArC,MAAA,CAAAsF,YAAA,CAAAtF,MAAA,CAAAqC,gBAAA,KAAArC,MAAA,CAAAsF,YAAA,CAAAtF,MAAA,CAAAqC,gBAAA,EAAA4D,MAAA,KAAyH;IAoBxHvG,EAAA,CAAAI,SAAA,EAAqC;IAArCJ,EAAA,CAAAuB,UAAA,SAAAjB,MAAA,CAAAkG,WAAA,IAAAlG,MAAA,CAAAqC,gBAAA,CAAqC;IAcrC3C,EAAA,CAAAI,SAAA,EAA2C;IAA3CJ,EAAA,CAAAuB,UAAA,SAAAjB,MAAA,CAAAqC,gBAAA,IAAArC,MAAA,CAAAwE,MAAA,CAAAyB,MAAA,KAA2C;IAuB3CvG,EAAA,CAAAI,SAAA,GAAuB;IAAvBJ,EAAA,CAAAuB,UAAA,UAAAjB,MAAA,CAAAqC,gBAAA,CAAuB;IAIvB3C,EAAA,CAAAI,SAAA,EAAqG;IAArGJ,EAAA,CAAAuB,UAAA,SAAAjB,MAAA,CAAAqC,gBAAA,IAAArC,MAAA,CAAAsF,YAAA,CAAAtF,MAAA,CAAAqC,gBAAA,KAAArC,MAAA,CAAAsF,YAAA,CAAAtF,MAAA,CAAAqC,gBAAA,EAAA4D,MAAA,KAAqG;IA4BxGvG,EAAA,CAAAI,SAAA,EAAyH;IAAzHJ,EAAA,CAAAuB,UAAA,SAAAjB,MAAA,CAAAqC,gBAAA,MAAArC,MAAA,CAAAsF,YAAA,CAAAtF,MAAA,CAAAqC,gBAAA,KAAArC,MAAA,CAAAsF,YAAA,CAAAtF,MAAA,CAAAqC,gBAAA,EAAA4D,MAAA,YAAAjG,MAAA,CAAAmD,UAAA,CAAyH;;;;;IAkB9HzD,EAAA,CAAAC,cAAA,cAAiF;IAC/ED,EAAA,CAAAoB,SAAA,mBAAuE;IACvEpB,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,kCAAO;IAAAF,EAAA,CAAAC,cAAA,aAAQ;IAAAD,EAAA,CAAAE,MAAA,GAAiB;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAACH,EAAA,CAAAE,MAAA,cAAC;IACnDF,EADmD,CAAAG,YAAA,EAAO,EACpD;;;;IADiBH,EAAA,CAAAI,SAAA,GAAiB;IAAjBJ,EAAA,CAAAqB,iBAAA,CAAAf,MAAA,CAAAmG,aAAA,CAAiB;;;;;IAKtCzG,EAAA,CAAAC,cAAA,gBAAsE;IACpED,EAAA,CAAAoB,SAAA,mBAAiE;IACjEpB,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IADLH,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAC,MAAA,CAAAuC,aAAA,MACF;;;;;IANF7C,EAAA,CAAAC,cAAA,cAAoF;IAClFD,EAAA,CAAAoB,SAAA,mBAA+D;IAC/DpB,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,iCAAM;IAAAF,EAAA,CAAAC,cAAA,aAAQ;IAAAD,EAAA,CAAAE,MAAA,GAAoB;IAASF,EAAT,CAAAG,YAAA,EAAS,EAAO;IACxDH,EAAA,CAAAU,UAAA,IAAAgG,8DAAA,oBAAsE;IAIxE1G,EAAA,CAAAG,YAAA,EAAM;;;;IALgBH,EAAA,CAAAI,SAAA,GAAoB;IAApBJ,EAAA,CAAAqB,iBAAA,CAAAf,MAAA,CAAAqC,gBAAA,CAAoB;IACjC3C,EAAA,CAAAI,SAAA,EAAmB;IAAnBJ,EAAA,CAAAuB,UAAA,SAAAjB,MAAA,CAAAuC,aAAA,CAAmB;;;;;IAM9B7C,EAAA,CAAAC,cAAA,eACgH;IAC9GD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADJH,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAA0E,kBAAA,sBAAApE,MAAA,CAAAmD,UAAA,UAAAnD,MAAA,CAAAqG,0BAAA,4BACF;;;;;;IAME3G,EAAA,CAAAC,cAAA,kBACsL;IAD1HD,EAAA,CAAAY,UAAA,mBAAAgG,mFAAA;MAAA5G,EAAA,CAAAc,aAAA,CAAA+F,IAAA;MAAA,MAAAvG,MAAA,GAAAN,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAASZ,MAAA,CAAA2B,UAAA,EAAY;IAAA,EAAC;IAEhFjC,EAAA,CAAAoB,SAAA,mBAA0C;IAC1CpB,EAAA,CAAAE,MAAA,iCACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;IACTH,EAAA,CAAAC,cAAA,kBACsL;IAD9HD,EAAA,CAAAY,UAAA,mBAAAkG,mFAAA;MAAA9G,EAAA,CAAAc,aAAA,CAAAiG,IAAA;MAAA,MAAAzG,MAAA,GAAAN,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAASZ,MAAA,CAAA0G,WAAA,EAAa;IAAA,EAAC;IAE7EhH,EAAA,CAAAoB,SAAA,mBAA0C;IAC1CpB,EAAA,CAAAE,MAAA,iCACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;IA7LXH,EAHN,CAAAC,cAAA,kBAAmE,qBACjD,cACmE,cACpB;IACzDD,EAAA,CAAAoB,SAAA,kBAAkF;IAClFpB,EAAA,CAAAC,cAAA,eAAoE;IAAAD,EAAA,CAAAE,MAAA,+BAAI;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC/EH,EAAA,CAAAC,cAAA,eAAmD;IAAAD,EAAA,CAAAE,MAAA,GAA0B;IAC/EF,EAD+E,CAAAG,YAAA,EAAO,EAChF;IACNH,EAAA,CAAAC,cAAA,eAAmD;IAAAD,EAAA,CAAAE,MAAA,IAA2B;IAElFF,EAFkF,CAAAG,YAAA,EAAO,EACjF,EACS;IAEjBH,EAAA,CAAAC,cAAA,wBAAoD;IAWlDD,EATA,CAAAU,UAAA,KAAAuG,uDAAA,kBACmG,KAAAC,uDAAA,mBAQnB;IAgIlFlH,EAAA,CAAAG,YAAA,EAAe;IAMTH,EAJN,CAAAC,cAAA,0BAAiG,eAEO,eACH,eACpC;IACzDD,EAAA,CAAAoB,SAAA,mBAA2E;IAC3EpB,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAE,MAAA,4BAAK;IAAAF,EAAA,CAAAC,cAAA,cAAQ;IAAAD,EAAA,CAAAE,MAAA,IAAsB;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAACH,EAAA,CAAAE,MAAA,2BAAG;IACxDF,EADwD,CAAAG,YAAA,EAAO,EACzD;IAKNH,EAJA,CAAAU,UAAA,KAAAyG,uDAAA,kBAAiF,KAAAC,uDAAA,kBAIG;IAQtFpH,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAU,UAAA,KAAA2G,uDAAA,kBACgH;IAGlHrH,EAAA,CAAAG,YAAA,EAAM;IAIJH,EADF,CAAAC,cAAA,eAA2F,eACnD;IAMpCD,EALA,CAAAU,UAAA,KAAA4G,0DAAA,qBACsL,KAAAC,0DAAA,qBAKA;IAIxLvH,EAAA,CAAAG,YAAA,EAAM;IAGJH,EADF,CAAAC,cAAA,eAAsC,kBAEoG;IADlHD,EAAA,CAAAY,UAAA,mBAAA4G,0EAAA;MAAA,MAAAC,OAAA,GAAAzH,EAAA,CAAAc,aAAA,CAAA4G,GAAA,EAAAC,SAAA;MAAA,OAAA3H,EAAA,CAAAkB,WAAA,CAASuG,OAAA,CAAAG,KAAA,EAAW;IAAA,EAAC;IAEzC5H,EAAA,CAAAE,MAAA,sBACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,kBACwM;IADlLD,EAAA,CAAAY,UAAA,mBAAAiH,0EAAA;MAAA,MAAAJ,OAAA,GAAAzH,EAAA,CAAAc,aAAA,CAAA4G,GAAA,EAAAC,SAAA;MAAA,OAAA3H,EAAA,CAAAkB,WAAA,CAASuG,OAAA,CAAAG,KAAA,EAAW;IAAA,EAAC;IAEzC5H,EAAA,CAAAoB,SAAA,mBAA4C;IAC5CpB,EAAA,CAAAE,MAAA,IACF;IAIRF,EAJQ,CAAAG,YAAA,EAAS,EACL,EACF,EACS,EACT;;;;IA1MiDH,EAAA,CAAAI,SAAA,GAA0B;IAA1BJ,EAAA,CAAAK,kBAAA,MAAAC,MAAA,CAAA8B,SAAA,CAAAmE,MAAA,yBAA0B;IAE5BvG,EAAA,CAAAI,SAAA,GAA2B;IAA3BJ,EAAA,CAAAK,kBAAA,yBAAAC,MAAA,CAAA6B,gBAAA,OAA2B;IAM1EnC,EAAA,CAAAI,SAAA,GAAe;IAAfJ,EAAA,CAAAuB,UAAA,SAAAjB,MAAA,CAAAwH,SAAA,CAAe;IASf9H,EAAA,CAAAI,SAAA,EAAgB;IAAhBJ,EAAA,CAAAuB,UAAA,UAAAjB,MAAA,CAAAwH,SAAA,CAAgB;IAwIG9H,EAAA,CAAAI,SAAA,GAAsB;IAAtBJ,EAAA,CAAAqB,iBAAA,CAAAf,MAAA,CAAA6B,gBAAA,GAAsB;IAErCnC,EAAA,CAAAI,SAAA,GAAmB;IAAnBJ,EAAA,CAAAuB,UAAA,SAAAjB,MAAA,CAAAmG,aAAA,CAAmB;IAInBzG,EAAA,CAAAI,SAAA,EAAsB;IAAtBJ,EAAA,CAAAuB,UAAA,SAAAjB,MAAA,CAAAqC,gBAAA,CAAsB;IASxB3C,EAAA,CAAAI,SAAA,EAAgB;IAAhBJ,EAAA,CAAAuB,UAAA,SAAAjB,MAAA,CAAAmD,UAAA,CAAgB;IASGzD,EAAA,CAAAI,SAAA,GAAmC;IAAnCJ,EAAA,CAAAuB,UAAA,SAAAjB,MAAA,CAAAyH,kBAAA,CAAAxB,MAAA,KAAmC;IAKnCvG,EAAA,CAAAI,SAAA,EAA+B;IAA/BJ,EAAA,CAAAuB,UAAA,SAAAjB,MAAA,CAAAkG,WAAA,IAAAlG,MAAA,CAAAmD,UAAA,CAA+B;IAepDzD,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAK,kBAAA,gCAAAC,MAAA,CAAA6B,gBAAA,SACF;;;ADlOV,OAAM,MAAO6F,yBAAyB;EA4BpCC,YACUC,GAAsB,EACtBC,kBAAsC,EACtCC,aAA8B;IAF9B,KAAAF,GAAG,GAAHA,GAAG;IACH,KAAAC,kBAAkB,GAAlBA,kBAAkB;IAClB,KAAAC,aAAa,GAAbA,aAAa;IA5Bd,KAAA/F,WAAW,GAAW,OAAO;IAC7B,KAAAoE,aAAa,GAAkB,IAAI;IACnC,KAAAjF,QAAQ,GAAY,KAAK;IACzB,KAAA6G,WAAW,GAAkB,IAAI,CAAC,CAAC;IACnC,KAAAzC,YAAY,GAAiB,EAAE;IAC/B,KAAA0C,gBAAgB,GAAY,IAAI;IAChC,KAAA9B,WAAW,GAAY,IAAI;IAAY,KAAAF,gBAAgB,GAAY,IAAI;IACvE,KAAAiC,gBAAgB,GAAa,EAAE,CAAC,CAAC;IAEhC,KAAAC,eAAe,GAAG,IAAI3I,YAAY,EAAmB;IACrD,KAAA4I,aAAa,GAAG,IAAI5I,YAAY,EAAY,CAAC,CAAC;IACxD,KAAA6I,MAAM,GAAG,KAAK;IACd,KAAA/F,gBAAgB,GAAG,EAAE;IACrB,KAAAc,UAAU,GAAG,EAAE;IACf,KAAAZ,aAAa,GAAG,EAAE,CAAC,CAAC;IACpB,KAAA8F,gBAAgB,GAAa,EAAE,CAAC,CAAC;IACjC,KAAAvG,SAAS,GAAa,EAAE;IACxB,KAAA0C,MAAM,GAAa,EAAE,CAAC,CAAC;IACvB,KAAA8D,kBAAkB,GAAa,EAAE,CAAC,CAAE;IACpC,KAAAC,kBAAkB,GAAqC,EAAE,CAAC,CAAC;IAC3D,KAAAf,SAAS,GAAY,KAAK,CAAC,CAAC;IAE5B;IACQ,KAAAgB,QAAQ,GAAIC,KAAe,IAAI,CAAG,CAAC;IACnC,KAAAC,SAAS,GAAG,MAAK,CAAG,CAAC;EAKzB;EAEJC,UAAUA,CAACF,KAAe;IACxB,IAAI,CAAChB,kBAAkB,GAAGgB,KAAK,IAAI,EAAE;IACrC,IAAI,CAACG,wBAAwB,EAAE;EACjC;EAEAC,gBAAgBA,CAACC,EAA6B;IAC5C,IAAI,CAACN,QAAQ,GAAGM,EAAE;EACpB;EAEAC,iBAAiBA,CAACD,EAAc;IAC9B,IAAI,CAACJ,SAAS,GAAGI,EAAE;EACrB;EACAE,gBAAgBA,CAACC,UAAmB;IAClC,IAAI,CAAC/H,QAAQ,GAAG+H,UAAU;EAC5B;EAAEC,QAAQA,CAAA;IACR,IAAI,CAACC,cAAc,EAAE;EACvB;EACAC,WAAWA,CAACC,OAAsB;IAChC,IAAIA,OAAO,CAAC,aAAa,CAAC,IAAI,IAAI,CAACtB,WAAW,EAAE;MAC9C;MACA,IAAI,CAACoB,cAAc,EAAE;IACvB;IACA,IAAIE,OAAO,CAAC,cAAc,CAAC,EAAE;MAC3B;MACA,IAAI,CAACvH,SAAS,GAAGwH,MAAM,CAACC,IAAI,CAAC,IAAI,CAACjE,YAAY,IAAI,EAAE,CAAC;MACrDkE,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAE,IAAI,CAACnE,YAAY,CAAC;MACvD,IAAI,CAACoE,wBAAwB,EAAE;MAC/B,IAAI,CAACd,wBAAwB,EAAE;IACjC;IACA,IAAIS,OAAO,CAAC,oBAAoB,CAAC,EAAE;MACjC;MACA,IAAI,CAACK,wBAAwB,EAAE;MAC/BF,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAE,IAAI,CAACE,kBAAkB,CAAC;IACtE;EACF;EAAUR,cAAcA,CAAA;IACtB;IACA,IAAI,IAAI,CAAC7D,YAAY,IAAIgE,MAAM,CAACC,IAAI,CAAC,IAAI,CAACjE,YAAY,CAAC,CAACW,MAAM,GAAG,CAAC,EAAE;MAClE;MACA,IAAI,CAACnE,SAAS,GAAGwH,MAAM,CAACC,IAAI,CAAC,IAAI,CAACjE,YAAY,CAAC;MAC/CkE,OAAO,CAACC,GAAG,CAAC,mDAAmD,EAAE,IAAI,CAAC3H,SAAS,CAAC;MAChF,IAAI,CAAC8G,wBAAwB,EAAE;IACjC,CAAC,MAAM,IAAI,IAAI,CAACb,WAAW,EAAE;MAC3B;MACA,IAAI,CAAC6B,uBAAuB,EAAE;IAChC,CAAC,MAAM;MACL;MACA,IAAI,CAAC9H,SAAS,GAAG,EAAE;MACnB0H,OAAO,CAACC,GAAG,CAAC,yCAAyC,CAAC;IACxD;EACF;EAEQG,uBAAuBA,CAAA;IAC7B,IAAI,CAAC,IAAI,CAAC7B,WAAW,EAAE;IAEvB,IAAI,CAACP,SAAS,GAAG,IAAI;IACrB,IAAI,CAACK,kBAAkB,CAACgC,WAAW,CAAC,IAAI,CAAC9B,WAAW,CAAC,CAAC+B,SAAS,CAAC;MAC9DC,IAAI,EAAGC,QAAQ,IAAI;QACjBR,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEO,QAAQ,CAAC;QACtC,IAAI,CAAC1E,YAAY,GAAG,IAAI,CAAC2E,gCAAgC,CAACD,QAAQ,CAACE,OAAO,CAAC;QAC3E,IAAI,CAACpI,SAAS,GAAGwH,MAAM,CAACC,IAAI,CAAC,IAAI,CAACjE,YAAY,CAAC;QAC/C,IAAI,CAACsD,wBAAwB,EAAE;QAC/B,IAAI,CAACpB,SAAS,GAAG,KAAK;QACtB,IAAI,CAACI,GAAG,CAACuC,aAAa,EAAE;MAC1B,CAAC;MAAEC,KAAK,EAAGA,KAAK,IAAI;QAClBZ,OAAO,CAACY,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;QACpD;QACA,IAAI,CAAC9E,YAAY,GAAG,EAAE;QACtB,IAAI,CAACxD,SAAS,GAAG,EAAE;QACnB,IAAI,CAAC0F,SAAS,GAAG,KAAK;QACtB,IAAI,CAACI,GAAG,CAACuC,aAAa,EAAE;MAC1B;KACD,CAAC;EACJ;EACQF,gCAAgCA,CAACI,OAAiC;IACxE,MAAM/E,YAAY,GAAiB,EAAE;IAErCgE,MAAM,CAACe,OAAO,CAACA,OAAO,CAAC,CAACC,OAAO,CAAC,CAAC,CAACC,QAAQ,EAAEC,MAAM,CAAC,KAAI;MACrDlF,YAAY,CAACiF,QAAQ,CAAC,GAAGC,MAAM,CAACC,GAAG,CAACC,KAAK,KAAK;QAC5C1J,SAAS,EAAE0J,KAAK,CAAC1J,SAAS,IAAI0J,KAAK,CAACC,SAAS,IAAID,KAAK,CAACE,IAAI;QAC3DL,QAAQ,EAAEG,KAAK,CAACH,QAAQ,IAAIG,KAAK,CAACG,QAAQ,IAAIN,QAAQ;QACtDpK,KAAK,EAAEuK,KAAK,CAACvK,KAAK,IAAIuK,KAAK,CAACI,KAAK;QACjCC,OAAO,EAAEL,KAAK,CAACK,OAAO,IAAIL,KAAK,CAACM,OAAO;QACvCC,UAAU,EAAE,KAAK;QACjBhC,UAAU,EAAE;OACb,CAAC,CAAC;IACL,CAAC,CAAC;IAEF,OAAO3D,YAAY;EACrB;EAAUsD,wBAAwBA,CAAA;IAChC,MAAMsC,OAAO,GAAqC,EAAE;IAEpD,IAAI,CAACzD,kBAAkB,CAAC6C,OAAO,CAACtJ,SAAS,IAAG;MAC1C,KAAK,MAAMuJ,QAAQ,IAAI,IAAI,CAACzI,SAAS,EAAE;QACrC,MAAMqJ,IAAI,GAAG,IAAI,CAAC7F,YAAY,CAACiF,QAAQ,CAAC,EAAEa,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACrK,SAAS,KAAKA,SAAS,CAAC;QAC9E,IAAImK,IAAI,EAAE;UACR,IAAI,CAACD,OAAO,CAACX,QAAQ,CAAC,EAAEW,OAAO,CAACX,QAAQ,CAAC,GAAG,EAAE;UAC9CW,OAAO,CAACX,QAAQ,CAAC,CAACe,IAAI,CAACtK,SAAS,CAAC;UACjC;QACF;MACF;IACF,CAAC,CAAC;IAEF,IAAI,CAACuH,kBAAkB,GAAG2C,OAAO;EACnC;EAAE/I,gBAAgBA,CAACoI,QAAgB;IACjCf,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEc,QAAQ,CAAC;IAC3C,IAAI,CAAClI,gBAAgB,GAAGkI,QAAQ;IAChC,IAAI,CAAChI,aAAa,GAAG,EAAE,CAAC,CAAC;IACzB,IAAI,CAACY,UAAU,GAAG,EAAE;IACpB,IAAI,CAACoI,uBAAuB,EAAE,CAAC,CAAC;IAChC,IAAI,CAAC7B,wBAAwB,EAAE;IAC/BF,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAE,IAAI,CAACnB,kBAAkB,CAACrC,MAAM,CAAC;IACzE;IACA,IAAI,CAAC2B,GAAG,CAACuC,aAAa,EAAE;EAC1B;EAEAqB,eAAeA,CAACjB,QAAgB;IAC9Bf,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAEc,QAAQ,CAAC;EACxD;EAAEb,wBAAwBA,CAAA;IACxBF,OAAO,CAACC,GAAG,CAAC,4CAA4C,EAAE,IAAI,CAACpH,gBAAgB,EAAE,QAAQ,EAAE,IAAI,CAACE,aAAa,CAAC;IAC9G,IAAI,CAAC,IAAI,CAACF,gBAAgB,EAAE;MAC1B,IAAI,CAACiG,kBAAkB,GAAG,EAAE;MAC5B;IACF;IAEA,MAAMmD,UAAU,GAAG,IAAI,CAACnG,YAAY,CAAC,IAAI,CAACjD,gBAAgB,CAAC,IAAI,EAAE;IACjEmH,OAAO,CAACC,GAAG,CAAC,oCAAoC,EAAEgC,UAAU,CAACxF,MAAM,CAAC;IAEpE;IACA,IAAI,CAACqC,kBAAkB,GAAGmD,UAAU,CACjCC,MAAM,CAACL,CAAC,IAAG;MACV;MACA,MAAMM,UAAU,GAAG,CAAC,IAAI,CAACpJ,aAAa,IAAI8I,CAAC,CAAClL,KAAK,KAAK,IAAI,CAACoC,aAAa;MACxE;MACA,MAAMqJ,WAAW,GAAGP,CAAC,CAACrK,SAAS,CAACmE,WAAW,EAAE,CAACC,QAAQ,CAAC,IAAI,CAACjC,UAAU,CAACgC,WAAW,EAAE,CAAC;MACrF,OAAOwG,UAAU,IAAIC,WAAW;IAClC,CAAC,CAAC,CACDnB,GAAG,CAACY,CAAC,IAAIA,CAAC,CAACrK,SAAS,CAAC;IAExBwI,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAE,IAAI,CAACnB,kBAAkB,CAACrC,MAAM,CAAC;EAC5E;EAEAvC,cAAcA,CAACmI,KAAU;IACvB,IAAI,CAAC1I,UAAU,GAAG0I,KAAK,CAACC,MAAM,CAACrD,KAAK;IACpC,IAAI,CAACiB,wBAAwB,EAAE;IAC/BF,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAE,IAAI,CAACtG,UAAU,CAAC;EACtD;EAEAuD,WAAWA,CAAA;IACT,IAAI,CAACvD,UAAU,GAAG,EAAE;IACpB,IAAI,CAACuG,wBAAwB,EAAE;IAC/BF,OAAO,CAACC,GAAG,CAAC,cAAc,CAAC;EAC7B;EACA7E,iBAAiBA,CAACmH,aAAqB;IACrC;IACA,IAAI,IAAI,CAAC/G,mBAAmB,CAAC+G,aAAa,CAAC,EAAE;MAC3CvC,OAAO,CAACC,GAAG,CAAC,MAAMsC,aAAa,kBAAkB,CAAC;MAClD;IACF;IAEA,MAAMd,UAAU,GAAG,IAAI,CAACxD,kBAAkB,CAACrC,QAAQ,CAAC2G,aAAa,CAAC;IAClE,IAAIC,YAAsB;IAE1B,IAAIf,UAAU,EAAE;MACde,YAAY,GAAG,IAAI,CAACvE,kBAAkB,CAACiE,MAAM,CAACL,CAAC,IAAIA,CAAC,KAAKU,aAAa,CAAC;IACzE,CAAC,MAAM;MACL,IAAI,IAAI,CAAC5F,aAAa,IAAI,IAAI,CAACsB,kBAAkB,CAACxB,MAAM,IAAI,IAAI,CAACE,aAAa,EAAE;QAC9EqD,OAAO,CAACC,GAAG,CAAC,WAAW,CAAC;QACxB,OAAO,CAAC;MACV;MACAuC,YAAY,GAAG,CAAC,GAAG,IAAI,CAACvE,kBAAkB,EAAEsE,aAAa,CAAC;IAC5D;IAEA,IAAI,CAACtE,kBAAkB,GAAGuE,YAAY;IACtC,IAAI,CAACC,WAAW,EAAE;EACpB;EAEApL,iBAAiBA,CAACkL,aAAqB;IACrC,IAAI,CAACtE,kBAAkB,GAAG,IAAI,CAACA,kBAAkB,CAACiE,MAAM,CAACL,CAAC,IAAIA,CAAC,KAAKU,aAAa,CAAC;IAClF,IAAI,CAACE,WAAW,EAAE;EACpB;EAAEpJ,mBAAmBA,CAAA;IACnB,IAAI,CAAC,IAAI,CAACR,gBAAgB,IAAI,IAAI,CAACiG,kBAAkB,CAACrC,MAAM,KAAK,CAAC,EAAE;IAEpE;IACA,MAAMiG,YAAY,GAAG,IAAI,CAACzE,kBAAkB,CAACxB,MAAM;IACnD,MAAMkG,UAAU,GAAG,IAAI,CAAChG,aAAa,IAAIiG,QAAQ;IACjD,MAAMC,cAAc,GAAGF,UAAU,GAAGD,YAAY,CAAC,CAAI;IACrD,MAAMI,kBAAkB,GAAG,IAAI,CAAChE,kBAAkB,CAACoD,MAAM,CAACd,IAAI,IAC5D,CAAC,IAAI,CAACnD,kBAAkB,CAACrC,QAAQ,CAACwF,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC5F,mBAAmB,CAAC4F,IAAI,CAAC,CAC3E;IAED;IACA,MAAM2B,KAAK,GAAGD,kBAAkB,CAACE,KAAK,CAAC,CAAC,EAAEH,cAAc,CAAC;IAEzD,IAAIE,KAAK,CAACtG,MAAM,GAAG,CAAC,EAAE;MACpB,IAAI,CAACwB,kBAAkB,GAAG,CAAC,GAAG,IAAI,CAACA,kBAAkB,EAAE,GAAG8E,KAAK,CAAC;MAChE,IAAI,CAACN,WAAW,EAAE;MAClBzC,OAAO,CAACC,GAAG,CAAC,aAAa8C,KAAK,CAACtG,MAAM,MAAM,CAAC;IAC9C;EACF;EACAlD,mBAAmBA,CAAA;IACjB,IAAI,CAAC,IAAI,CAACV,gBAAgB,EAAE;IAE5B;IACA,MAAMoK,kBAAkB,GAAG,IAAI,CAACnH,YAAY,CAAC,IAAI,CAACjD,gBAAgB,CAAC,EAAEoI,GAAG,CAACY,CAAC,IAAIA,CAAC,CAACrK,SAAS,CAAC,IAAI,EAAE;IAEhG;IACA,MAAMkL,YAAY,GAAG,IAAI,CAACzE,kBAAkB,CAACxB,MAAM;IACnD,MAAMkG,UAAU,GAAG,IAAI,CAAChG,aAAa,IAAIiG,QAAQ;IACjD,MAAMC,cAAc,GAAGF,UAAU,GAAGD,YAAY,CAAC,CAAI;IACrD,MAAMQ,kBAAkB,GAAGD,kBAAkB,CAACf,MAAM,CAACd,IAAI,IACvD,CAAC,IAAI,CAACnD,kBAAkB,CAACrC,QAAQ,CAACwF,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC5F,mBAAmB,CAAC4F,IAAI,CAAC,CAC3E;IAED;IACA,MAAM2B,KAAK,GAAGG,kBAAkB,CAACF,KAAK,CAAC,CAAC,EAAEH,cAAc,CAAC;IAEzD,IAAIE,KAAK,CAACtG,MAAM,GAAG,CAAC,EAAE;MACpB,IAAI,CAACwB,kBAAkB,GAAG,CAAC,GAAG,IAAI,CAACA,kBAAkB,EAAE,GAAG8E,KAAK,CAAC;MAChE,IAAI,CAACN,WAAW,EAAE;MAClBzC,OAAO,CAACC,GAAG,CAAC,aAAa8C,KAAK,CAACtG,MAAM,MAAM,CAAC;IAC9C;EACF;EAAEvD,qBAAqBA,CAAA;IACrB,IAAI,CAAC,IAAI,CAACL,gBAAgB,EAAE;IAE5B,MAAMoK,kBAAkB,GAAG,IAAI,CAACnH,YAAY,CAAC,IAAI,CAACjD,gBAAgB,CAAC,EAAEoI,GAAG,CAACY,CAAC,IAAIA,CAAC,CAACrK,SAAS,CAAC,IAAI,EAAE;IAChG,IAAI,CAACyG,kBAAkB,GAAG,IAAI,CAACA,kBAAkB,CAACiE,MAAM,CAACL,CAAC,IAAI,CAACoB,kBAAkB,CAACrH,QAAQ,CAACiG,CAAC,CAAC,CAAC;IAC9F,IAAI,CAACY,WAAW,EAAE;EACpB;EACAtK,UAAUA,CAAA;IACR,IAAI,CAAC8F,kBAAkB,GAAG,EAAE;IAC5B,IAAI,CAACwE,WAAW,EAAE;EACpB;EACQA,WAAWA,CAAA;IACjB,IAAI,CAACrD,wBAAwB,EAAE;IAC/B,IAAI,CAACJ,QAAQ,CAAC,CAAC,GAAG,IAAI,CAACf,kBAAkB,CAAC,CAAC;IAC3C,IAAI,CAACiB,SAAS,EAAE;IAEhB,MAAMiE,aAAa,GAAG,IAAI,CAAClF,kBAAkB,CAACgD,GAAG,CAACzJ,SAAS,IAAG;MAC5D,KAAK,MAAMuJ,QAAQ,IAAI,IAAI,CAACzI,SAAS,EAAE;QACrC,MAAMqJ,IAAI,GAAG,IAAI,CAAC7F,YAAY,CAACiF,QAAQ,CAAC,EAAEa,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACrK,SAAS,KAAKA,SAAS,CAAC;QAC9E,IAAImK,IAAI,EAAE,OAAOA,IAAI;MACvB;MACA,OAAO,IAAI;IACb,CAAC,CAAC,CAACO,MAAM,CAACP,IAAI,IAAIA,IAAI,KAAK,IAAI,CAAoB;IAEnD,IAAI,CAACjD,eAAe,CAAC0E,IAAI,CAACD,aAAa,CAAC;IAExC;IACA,MAAME,QAAQ,GAAGF,aAAa,CAAClC,GAAG,CAACU,IAAI,IAAIA,IAAI,CAACJ,OAAQ,CAAC,CAACW,MAAM,CAACoB,EAAE,IAAIA,EAAE,KAAKC,SAAS,CAAC;IACxF,IAAI,CAAC5E,aAAa,CAACyE,IAAI,CAACC,QAAQ,CAAC;EACnC;EAAEG,cAAcA,CAAA;IACd,IAAI,CAAC,IAAI,CAAC9L,QAAQ,EAAE;MAClB,IAAI,CAAC+L,UAAU,EAAE;MACjBzD,OAAO,CAACC,GAAG,CAAC,oCAAoC,CAAC;MACjDD,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAE,IAAI,CAAC3H,SAAS,CAAC;IACrD;EACF;EAEAmL,UAAUA,CAAA;IACR,IAAI,CAACnF,aAAa,CAACoF,IAAI,CAAC,IAAI,CAACC,eAAe,EAAE;MAC5CC,OAAO,EAAE,EAAE;MACXC,oBAAoB,EAAE,KAAK;MAC3BC,UAAU,EAAE,IAAI;MAChBC,SAAS,EAAE;KACZ,CAAC;EACJ;EAEAC,aAAaA,CAAA;IACX;IACA;EAAA;EAGFzI,mBAAmBA,CAACgH,aAAqB;IACvC,OAAO,IAAI,CAACtE,kBAAkB,CAACrC,QAAQ,CAAC2G,aAAa,CAAC;EACxD;EAEA/G,mBAAmBA,CAAC+G,aAAqB;IACvC,OAAO,IAAI,CAACpC,kBAAkB,CAACvE,QAAQ,CAAC2G,aAAa,CAAC;EACxD;EAEA9G,mBAAmBA,CAAC8G,aAAqB;IACvC,OAAO,IAAI,CAAC/G,mBAAmB,CAAC+G,aAAa,CAAC,IAC3C,CAAC,IAAI,CAAC9I,aAAa,EAAE,IAAI,CAAC,IAAI,CAAC8B,mBAAmB,CAACgH,aAAa,CAAE;EACvE;EAEA9I,aAAaA,CAAA;IACX,OAAO,CAAC,IAAI,CAACkD,aAAa,IAAI,IAAI,CAACsB,kBAAkB,CAACxB,MAAM,GAAG,IAAI,CAACE,aAAa;EACnF;EACAsH,qBAAqBA,CAAA;IACnB,IAAI,CAAC,IAAI,CAACpL,gBAAgB,EAAE,OAAO,KAAK;IACxC,MAAMoK,kBAAkB,GAAG,IAAI,CAACnH,YAAY,CAAC,IAAI,CAACjD,gBAAgB,CAAC,CAChEqJ,MAAM,CAACL,CAAC,IAAI,CAACA,CAAC,CAACpC,UAAU,CAAC,CAC1BwB,GAAG,CAACY,CAAC,IAAIA,CAAC,CAACrK,SAAS,CAAC;IACxB,OAAOyL,kBAAkB,CAACxG,MAAM,GAAG,CAAC,IAClCwG,kBAAkB,CAACiB,KAAK,CAAC1M,SAAS,IAAI,IAAI,CAACyG,kBAAkB,CAACrC,QAAQ,CAACpE,SAAS,CAAC,CAAC;EACtF;EAAEkC,sBAAsBA,CAAA;IACtB,IAAI,CAAC,IAAI,CAACb,gBAAgB,EAAE,OAAO,KAAK;IACxC,MAAMoK,kBAAkB,GAAG,IAAI,CAACnH,YAAY,CAAC,IAAI,CAACjD,gBAAgB,CAAC,EAAEoI,GAAG,CAACY,CAAC,IAAIA,CAAC,CAACrK,SAAS,CAAC,IAAI,EAAE;IAChG,OAAOyL,kBAAkB,CAACkB,IAAI,CAAC3M,SAAS,IAAI,IAAI,CAACyG,kBAAkB,CAACrC,QAAQ,CAACpE,SAAS,CAAC,CAAC;EAC1F;EACA4M,qBAAqBA,CAAA;IACnB,OAAO,IAAI,CAACrF,kBAAkB;EAChC;EAEAjG,gBAAgBA,CAACiI,QAAgB;IAC/B,OAAO,IAAI,CAACjF,YAAY,CAACiF,QAAQ,CAAC,EAAEtE,MAAM,IAAI,CAAC;EACjD;EAEApE,gBAAgBA,CAAA;IACd,OAAO,IAAI,CAAC4F,kBAAkB,CAACxB,MAAM;EACvC;EAEA;EACA3E,6BAA6BA,CAACiJ,QAAgB;IAC5C,OAAO,IAAI,CAAChC,kBAAkB,CAACgC,QAAQ,CAAC,IAAI,EAAE;EAChD;EAEA;EACA/I,mBAAmBA,CAAC+I,QAAgB;IAClC,OAAO,CAAC,EAAE,IAAI,CAAChC,kBAAkB,CAACgC,QAAQ,CAAC,IAAI,IAAI,CAAChC,kBAAkB,CAACgC,QAAQ,CAAC,CAACtE,MAAM,GAAG,CAAC,CAAC;EAC9F;EAEA;EACQsF,uBAAuBA,CAAA;IAC7B,IAAI,CAAC,IAAI,CAAClJ,gBAAgB,EAAE;MAC1B,IAAI,CAACmC,MAAM,GAAG,EAAE;MAChB;IACF;IAEA,MAAMiH,UAAU,GAAG,IAAI,CAACnG,YAAY,CAAC,IAAI,CAACjD,gBAAgB,CAAC,IAAI,EAAE;IACjE,MAAMwL,QAAQ,GAAG,IAAIC,GAAG,EAAU;IAElCrC,UAAU,CAACnB,OAAO,CAACyD,SAAS,IAAG;MAC7B,IAAIA,SAAS,CAAC5N,KAAK,EAAE;QACnB0N,QAAQ,CAACG,GAAG,CAACD,SAAS,CAAC5N,KAAK,CAAC;MAC/B;IACF,CAAC,CAAC;IAEF;IACA,IAAI,CAACqE,MAAM,GAAGyJ,KAAK,CAACC,IAAI,CAACL,QAAQ,CAAC,CAACM,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAI;MAC/C,MAAMC,IAAI,GAAGC,QAAQ,CAACH,CAAC,CAACI,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC,CAAC;MAC/C,MAAMC,IAAI,GAAGF,QAAQ,CAACF,CAAC,CAACG,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC,CAAC;MAC/C,OAAOF,IAAI,GAAGG,IAAI;IACpB,CAAC,CAAC;IAEFjF,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAE,IAAI,CAACpH,gBAAgB,EAAE,IAAI,CAACmC,MAAM,CAAC;EACjF;EAEA;EACAR,aAAaA,CAAC7D,KAAa;IACzBqJ,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEtJ,KAAK,CAAC;IACrC,IAAI,CAACoC,aAAa,GAAG,IAAI,CAACA,aAAa,KAAKpC,KAAK,GAAG,EAAE,GAAGA,KAAK,CAAC,CAAC;IAChE,IAAI,CAACuJ,wBAAwB,EAAE;IAC/B,IAAI,CAAC9B,GAAG,CAACuC,aAAa,EAAE;EAC1B;EAEA;EACA9F,aAAaA,CAAClE,KAAa;IACzB,IAAI,CAAC,IAAI,CAACkC,gBAAgB,EAAE,OAAO,CAAC;IACpC,MAAMoJ,UAAU,GAAG,IAAI,CAACnG,YAAY,CAAC,IAAI,CAACjD,gBAAgB,CAAC,IAAI,EAAE;IACjE,OAAOoJ,UAAU,CAACC,MAAM,CAACL,CAAC,IAAIA,CAAC,CAAClL,KAAK,KAAKA,KAAK,CAAC,CAAC8F,MAAM;EACzD;EACA;EACAyI,iBAAiBA,CAAC3C,aAAqB;IACrC,IAAI,CAAC,IAAI,CAAC1J,gBAAgB,EAAE,OAAO,EAAE;IACrC,MAAMoJ,UAAU,GAAG,IAAI,CAACnG,YAAY,CAAC,IAAI,CAACjD,gBAAgB,CAAC,IAAI,EAAE;IACjE,MAAM0L,SAAS,GAAGtC,UAAU,CAACL,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACrK,SAAS,KAAK+K,aAAa,CAAC;IACrE,OAAOgC,SAAS,EAAE5N,KAAK,IAAI,EAAE;EAC/B;EACA;EACAF,gBAAgBA,CAAC8L,aAAqB;IACpC,KAAK,MAAMxB,QAAQ,IAAI,IAAI,CAACzI,SAAS,EAAE;MACrC,MAAM2J,UAAU,GAAG,IAAI,CAACnG,YAAY,CAACiF,QAAQ,CAAC,IAAI,EAAE;MACpD,MAAMwD,SAAS,GAAGtC,UAAU,CAACL,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACrK,SAAS,KAAK+K,aAAa,CAAC;MACrE,IAAIgC,SAAS,EAAE;QACb,OAAO;UACL/M,SAAS,EAAE+M,SAAS,CAAC/M,SAAS;UAC9Bb,KAAK,EAAE4N,SAAS,CAAC5N,KAAK,IAAI;SAC3B;MACH;IACF;IACA,OAAO;MAAEa,SAAS,EAAE+K,aAAa;MAAE5L,KAAK,EAAE;IAAE,CAAE;EAChD;EAEA;EACA0D,kBAAkBA,CAAA;IAChB,IAAI,CAAC,IAAI,CAACV,UAAU,IAAI,CAAC,IAAI,CAACd,gBAAgB,IAAI,CAAC,IAAI,CAACiD,YAAY,CAAC,IAAI,CAACjD,gBAAgB,CAAC,EAAE;MAC3F,OAAO,KAAK;IACd;IAEA,MAAMsM,QAAQ,GAAG,IAAI,CAACrJ,YAAY,CAAC,IAAI,CAACjD,gBAAgB,CAAC,CAACqJ,MAAM,CAACL,CAAC,IAAG;MACnE,MAAMM,UAAU,GAAG,CAAC,IAAI,CAACpJ,aAAa,IAAI8I,CAAC,CAAClL,KAAK,KAAK,IAAI,CAACoC,aAAa;MACxE,MAAMqJ,WAAW,GAAGP,CAAC,CAACrK,SAAS,CAACmE,WAAW,EAAE,CAACC,QAAQ,CAAC,IAAI,CAACjC,UAAU,CAACgC,WAAW,EAAE,CAAC;MACrF,OAAOwG,UAAU,IAAIC,WAAW;IAClC,CAAC,CAAC;IAEF,OAAO+C,QAAQ,CAAC1I,MAAM,KAAK,CAAC;EAC9B;EAEA;EACAI,0BAA0BA,CAAA;IACxB,IAAI,CAAC,IAAI,CAAChE,gBAAgB,IAAI,CAAC,IAAI,CAACiD,YAAY,CAAC,IAAI,CAACjD,gBAAgB,CAAC,EAAE;MACvE,OAAO,CAAC;IACV;IAEA,MAAMsM,QAAQ,GAAG,IAAI,CAACrJ,YAAY,CAAC,IAAI,CAACjD,gBAAgB,CAAC,CAACqJ,MAAM,CAACL,CAAC,IAAG;MACnE,MAAMM,UAAU,GAAG,CAAC,IAAI,CAACpJ,aAAa,IAAI8I,CAAC,CAAClL,KAAK,KAAK,IAAI,CAACoC,aAAa;MACxE,MAAMqJ,WAAW,GAAG,CAAC,IAAI,CAACzI,UAAU,IAAIkI,CAAC,CAACrK,SAAS,CAACmE,WAAW,EAAE,CAACC,QAAQ,CAAC,IAAI,CAACjC,UAAU,CAACgC,WAAW,EAAE,CAAC;MACzG,OAAOwG,UAAU,IAAIC,WAAW;IAClC,CAAC,CAAC;IAEF,OAAO+C,QAAQ,CAAC1I,MAAM;EACxB;EAEA;EACA2I,oBAAoBA,CAACb,SAAwB;IAC3C,OAAOA,SAAS,CAAChD,OAAO,GAAGgD,SAAS,CAAChD,OAAO,CAAC8D,QAAQ,EAAE,GAAG,GAAGd,SAAS,CAAC/M,SAAS,IAAI+M,SAAS,CAAC5N,KAAK,EAAE;EACvG;EAEA;EACA2O,wBAAwBA,CAACC,QAAgB;IACvC,KAAK,MAAMxE,QAAQ,IAAI,IAAI,CAACzI,SAAS,EAAE;MACrC,MAAM2J,UAAU,GAAG,IAAI,CAACnG,YAAY,CAACiF,QAAQ,CAAC,IAAI,EAAE;MACpD,MAAMwD,SAAS,GAAGtC,UAAU,CAACL,IAAI,CAACC,CAAC,IAAI,IAAI,CAACuD,oBAAoB,CAACvD,CAAC,CAAC,KAAK0D,QAAQ,CAAC;MACjF,IAAIhB,SAAS,EAAE,OAAOA,SAAS;IACjC;IACA,OAAO,IAAI;EACb;;;uCA7cWrG,yBAAyB,EAAAhI,EAAA,CAAAsP,iBAAA,CAAAtP,EAAA,CAAAuP,iBAAA,GAAAvP,EAAA,CAAAsP,iBAAA,CAAAE,EAAA,CAAAC,kBAAA,GAAAzP,EAAA,CAAAsP,iBAAA,CAAAI,EAAA,CAAAC,eAAA;IAAA;EAAA;;;YAAzB3H,yBAAyB;MAAA4H,SAAA;MAAAC,SAAA,WAAAC,gCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;;;;;;;;;;;;;;;;;;;;;;uCARzB,CACT;QACEE,OAAO,EAAElQ,iBAAiB;QAC1BmQ,WAAW,EAAEpQ,UAAU,CAAC,MAAMkI,yBAAyB,CAAC;QACxDmI,KAAK,EAAE;OACR,CACF,GAAAnQ,EAAA,CAAAoQ,oBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,mCAAAV,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;UC7BH/P,EAAA,CAAAC,cAAA,aAAyC;UAEvCD,EAAA,CAAAU,UAAA,IAAAgQ,wCAAA,kBAAgG;UAgC9F1Q,EADF,CAAAC,cAAA,aAAgC,gBAGmL;UAD5KD,EAAA,CAAAY,UAAA,mBAAA+P,2DAAA;YAAA3Q,EAAA,CAAAc,aAAA,CAAA8P,GAAA;YAAA,OAAA5Q,EAAA,CAAAkB,WAAA,CAAS8O,GAAA,CAAA1C,cAAA,EAAgB;UAAA,EAAC;UAE7DtN,EAAA,CAAAC,cAAA,cAA4B;UAK1BD,EAJA,CAAAU,UAAA,IAAAmQ,iDAAA,0BAAgC,IAAAC,iDAAA,0BAIC;UAGnC9Q,EAAA,CAAAG,YAAA,EAAO;UACPH,EAAA,CAAAoB,SAAA,iBAA4D;UAGlEpB,EAFI,CAAAG,YAAA,EAAS,EACL,EACF;UAGNH,EAAA,CAAAU,UAAA,IAAAqQ,gDAAA,kCAAA/Q,EAAA,CAAAgR,sBAAA,CAA6D;;;UAlDrDhR,EAAA,CAAAI,SAAA,EAAuD;UAAvDJ,EAAA,CAAAuB,UAAA,SAAAyO,GAAA,CAAA1H,gBAAA,IAAA0H,GAAA,CAAAjI,kBAAA,CAAAxB,MAAA,KAAuD;UAgCbvG,EAAA,CAAAI,SAAA,GAAwC;UAAxCJ,EAAA,CAAAiR,WAAA,aAAAjB,GAAA,CAAAxO,QAAA,IAAAwO,GAAA,CAAAlI,SAAA,CAAwC;UACpF9H,EAAA,CAAAuB,UAAA,aAAAyO,GAAA,CAAAxO,QAAA,IAAAwO,GAAA,CAAAlI,SAAA,CAAkC;UAGjB9H,EAAA,CAAAI,SAAA,GAAe;UAAfJ,EAAA,CAAAuB,UAAA,SAAAyO,GAAA,CAAAlI,SAAA,CAAe;UAIf9H,EAAA,CAAAI,SAAA,EAAgB;UAAhBJ,EAAA,CAAAuB,UAAA,UAAAyO,GAAA,CAAAlI,SAAA,CAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}