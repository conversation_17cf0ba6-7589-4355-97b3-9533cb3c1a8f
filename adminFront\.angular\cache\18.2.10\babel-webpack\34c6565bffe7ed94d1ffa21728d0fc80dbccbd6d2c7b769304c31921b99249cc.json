{"ast": null, "code": "import { SharedModule } from '../components/shared.module';\nimport { CommonModule } from '@angular/common';\nimport { NbDatepickerModule } from '@nebular/theme';\nimport { BaseComponent } from '../components/base/baseComponent';\nimport { concatMap, tap } from 'rxjs';\nimport { NbDateFnsDateModule } from '@nebular/date-fns';\nimport * as moment from 'moment';\nimport { EEvent } from 'src/app/shared/services/event.service';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/shared/helper/allowHelper\";\nimport * as i2 from \"@nebular/theme\";\nimport * as i3 from \"src/app/shared/services/message.service\";\nimport * as i4 from \"src/app/shared/helper/validationHelper\";\nimport * as i5 from \"src/services/api/services\";\nimport * as i6 from \"src/app/shared/helper/petternHelper\";\nimport * as i7 from \"@angular/router\";\nimport * as i8 from \"src/app/shared/services/event.service\";\nimport * as i9 from \"src/app/shared/services/utility.service\";\nimport * as i10 from \"@angular/common\";\nimport * as i11 from \"@angular/forms\";\nimport * as i12 from \"@ng-bootstrap/ng-bootstrap\";\nimport * as i13 from \"../components/breadcrumb/breadcrumb.component\";\nimport * as i14 from \"../../@theme/directives/label.directive\";\nconst _c0 = [\"fileInput\"];\nfunction HouseholdManagementComponent_nb_option_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 48);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const case_r2 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", case_r2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", case_r2.CBuildCaseName, \" \");\n  }\n}\nfunction HouseholdManagementComponent_nb_option_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 48);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const case_r3 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", case_r3);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", case_r3.label, \" \");\n  }\n}\nfunction HouseholdManagementComponent_nb_option_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 48);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const case_r4 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", case_r4);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", case_r4.label, \" \");\n  }\n}\nfunction HouseholdManagementComponent_nb_option_40_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 48);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const case_r5 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", case_r5);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", case_r5.label, \" \");\n  }\n}\nfunction HouseholdManagementComponent_nb_option_46_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 48);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const case_r6 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", case_r6);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", case_r6.label, \" \");\n  }\n}\nfunction HouseholdManagementComponent_nb_option_52_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 48);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const case_r7 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", case_r7);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", case_r7.label, \" \");\n  }\n}\nfunction HouseholdManagementComponent_nb_option_58_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 48);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const case_r8 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", case_r8);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", case_r8.label, \" \");\n  }\n}\nfunction HouseholdManagementComponent_nb_option_64_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 48);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const case_r9 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", case_r9);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", case_r9.label, \" \");\n  }\n}\nfunction HouseholdManagementComponent_button_72_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 37);\n    i0.ɵɵlistener(\"click\", function HouseholdManagementComponent_button_72_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r10 = i0.ɵɵnextContext();\n      const dialogHouseholdMain_r12 = i0.ɵɵreference(112);\n      return i0.ɵɵresetView(ctx_r10.openModel(dialogHouseholdMain_r12));\n    });\n    i0.ɵɵtext(1, \" \\u6279\\u6B21\\u65B0\\u589E\\u6236\\u5225\\u8CC7\\u6599 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction HouseholdManagementComponent_tr_106_button_20_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r14 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 53);\n    i0.ɵɵlistener(\"click\", function HouseholdManagementComponent_tr_106_button_20_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r14);\n      const item_r15 = i0.ɵɵnextContext().$implicit;\n      const ctx_r10 = i0.ɵɵnextContext();\n      const dialogUpdateHousehold_r16 = i0.ɵɵreference(110);\n      return i0.ɵɵresetView(ctx_r10.openModelDetail(dialogUpdateHousehold_r16, item_r15));\n    });\n    i0.ɵɵtext(1, \" \\u7DE8\\u8F2F \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction HouseholdManagementComponent_tr_106_button_21_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r17 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 52);\n    i0.ɵɵlistener(\"click\", function HouseholdManagementComponent_tr_106_button_21_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r17);\n      const item_r15 = i0.ɵɵnextContext().$implicit;\n      const ctx_r10 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r10.onNavidateBuildCaseIdHouseId(\"customer-change-picture\", ctx_r10.searchQuery.CBuildCaseSelected.cID, item_r15.CID));\n    });\n    i0.ɵɵtext(1, \" \\u5BA2\\u8B8A\\u5716\\u4E0A\\u50B3 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction HouseholdManagementComponent_tr_106_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r13 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"td\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"td\");\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"td\");\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"td\");\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"td\");\n    i0.ɵɵtext(16);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"td\");\n    i0.ɵɵtext(18);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"td\", 49);\n    i0.ɵɵtemplate(20, HouseholdManagementComponent_tr_106_button_20_Template, 2, 0, \"button\", 50)(21, HouseholdManagementComponent_tr_106_button_21_Template, 2, 0, \"button\", 51);\n    i0.ɵɵelementStart(22, \"button\", 52);\n    i0.ɵɵlistener(\"click\", function HouseholdManagementComponent_tr_106_Template_button_click_22_listener() {\n      const item_r15 = i0.ɵɵrestoreView(_r13).$implicit;\n      const ctx_r10 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r10.onNavidateBuildCaseIdHouseId(\"sample-selection-result\", ctx_r10.searchQuery.CBuildCaseSelected.cID, item_r15.CID));\n    });\n    i0.ɵɵtext(23, \"\\u9078\\u6A23\\u53CA\\u5BA2\\u8B8A\\u7D50\\u679C \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const item_r15 = ctx.$implicit;\n    const ctx_r10 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r15.CBuildingName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r15.CHouseHold);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r15.CFloor);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\" \", item_r15.CHouseType === 2 ? \"\\u92B7\\u552E\\u6236\" : \"\", \" \", item_r15.CHouseType === 1 ? \"\\u5730\\u4E3B\\u6236\" : \"\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r15.CIsChange === true ? \"\\u5BA2\\u8B8A\" : item_r15.CIsChange === false ? \"\\u6A19\\u6E96\" : \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r15.CProgress);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate3(\" \", item_r15.CPayStatus === 0 ? \"\\u672A\\u4ED8\\u6B3E\" : \"\", \" \", item_r15.CPayStatus === 1 ? \"\\u5DF2\\u4ED8\\u6B3E\" : \"\", \" \", item_r15.CPayStatus === 2 ? \"\\u7121\\u9808\\u4ED8\\u6B3E\" : \"\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r15.CSignStatus === 0 || item_r15.CSignStatus == null ? \"\\u672A\\u7C3D\\u56DE\" : \"\\u5DF2\\u7C3D\\u56DE\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r15.CIsEnable ? \"\\u555F\\u7528\" : \"\\u505C\\u7528\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r10.isUpdate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", item_r15.CPayStatus !== 2);\n  }\n}\nfunction HouseholdManagementComponent_ng_template_109_nb_card_body_1_nb_option_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 48);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const status_r20 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", status_r20);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", status_r20.CBuildCaseName, \" \");\n  }\n}\nfunction HouseholdManagementComponent_ng_template_109_nb_card_body_1_nb_option_34_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 48);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const status_r21 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", status_r21);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", status_r21.label, \" \");\n  }\n}\nfunction HouseholdManagementComponent_ng_template_109_nb_card_body_1_nb_option_39_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 48);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const status_r22 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", status_r22);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", status_r22.label, \" \");\n  }\n}\nfunction HouseholdManagementComponent_ng_template_109_nb_card_body_1_nb_option_44_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 48);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const status_r23 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", status_r23);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", status_r23.label, \" \");\n  }\n}\nfunction HouseholdManagementComponent_ng_template_109_nb_card_body_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r19 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-card-body\", 58)(1, \"div\", 59)(2, \"label\", 60);\n    i0.ɵɵtext(3, \" \\u5EFA\\u6848\\u540D\\u7A31 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"nb-select\", 61);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_ng_template_109_nb_card_body_1_Template_nb_select_ngModelChange_4_listener($event) {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r10 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r10.detailSelected.CBuildCaseSelected, $event) || (ctx_r10.detailSelected.CBuildCaseSelected = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtemplate(5, HouseholdManagementComponent_ng_template_109_nb_card_body_1_nb_option_5_Template, 2, 2, \"nb-option\", 12);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 59)(7, \"label\", 62);\n    i0.ɵɵtext(8, \" \\u6236\\u578B\\u540D\\u7A31 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"input\", 63);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_ng_template_109_nb_card_body_1_Template_input_ngModelChange_9_listener($event) {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r10 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r10.houseDetail.CHousehold, $event) || (ctx_r10.houseDetail.CHousehold = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"div\", 59)(11, \"label\", 64);\n    i0.ɵɵtext(12, \" \\u6A13\\u5C64 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"input\", 65);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_ng_template_109_nb_card_body_1_Template_input_ngModelChange_13_listener($event) {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r10 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r10.houseDetail.CFloor, $event) || (ctx_r10.houseDetail.CFloor = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(14, \"div\", 59)(15, \"label\", 66);\n    i0.ɵɵtext(16, \" \\u5BA2\\u6236\\u59D3\\u540D \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"input\", 67);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_ng_template_109_nb_card_body_1_Template_input_ngModelChange_17_listener($event) {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r10 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r10.houseDetail.CCustomerName, $event) || (ctx_r10.houseDetail.CCustomerName = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(18, \"div\", 59)(19, \"label\", 68);\n    i0.ɵɵtext(20, \" \\u8EAB\\u5206\\u8B49\\u5B57\\u865F \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"input\", 69);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_ng_template_109_nb_card_body_1_Template_input_ngModelChange_21_listener($event) {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r10 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r10.houseDetail.CNationalId, $event) || (ctx_r10.houseDetail.CNationalId = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(22, \"div\", 59)(23, \"label\", 70);\n    i0.ɵɵtext(24, \" \\u96FB\\u5B50\\u90F5\\u4EF6 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(25, \"input\", 71);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_ng_template_109_nb_card_body_1_Template_input_ngModelChange_25_listener($event) {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r10 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r10.houseDetail.CMail, $event) || (ctx_r10.houseDetail.CMail = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(26, \"div\", 59)(27, \"label\", 72);\n    i0.ɵɵtext(28, \" \\u806F\\u7D61\\u96FB\\u8A71 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(29, \"input\", 73);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_ng_template_109_nb_card_body_1_Template_input_ngModelChange_29_listener($event) {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r10 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r10.houseDetail.CPhone, $event) || (ctx_r10.houseDetail.CPhone = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(30, \"div\", 59)(31, \"label\", 74);\n    i0.ɵɵtext(32, \" \\u6236\\u5225\\u985E\\u578B \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(33, \"nb-select\", 75);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_ng_template_109_nb_card_body_1_Template_nb_select_ngModelChange_33_listener($event) {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r10 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r10.detailSelected.CHouseTypeSelected, $event) || (ctx_r10.detailSelected.CHouseTypeSelected = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtemplate(34, HouseholdManagementComponent_ng_template_109_nb_card_body_1_nb_option_34_Template, 2, 2, \"nb-option\", 12);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(35, \"div\", 59)(36, \"label\", 76);\n    i0.ɵɵtext(37, \" \\u4ED8\\u6B3E\\u72C0\\u614B \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(38, \"nb-select\", 77);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_ng_template_109_nb_card_body_1_Template_nb_select_ngModelChange_38_listener($event) {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r10 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r10.detailSelected.CPayStatusSelected, $event) || (ctx_r10.detailSelected.CPayStatusSelected = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtemplate(39, HouseholdManagementComponent_ng_template_109_nb_card_body_1_nb_option_39_Template, 2, 2, \"nb-option\", 12);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(40, \"div\", 59)(41, \"label\", 78);\n    i0.ɵɵtext(42, \" \\u9032\\u5EA6 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(43, \"nb-select\", 79);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_ng_template_109_nb_card_body_1_Template_nb_select_ngModelChange_43_listener($event) {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r10 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r10.detailSelected.CProgressSelected, $event) || (ctx_r10.detailSelected.CProgressSelected = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtemplate(44, HouseholdManagementComponent_ng_template_109_nb_card_body_1_nb_option_44_Template, 2, 2, \"nb-option\", 12);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(45, \"div\", 59)(46, \"label\", 80);\n    i0.ɵɵtext(47, \" \\u662F\\u5426\\u5BA2\\u8B8A \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(48, \"nb-checkbox\", 81);\n    i0.ɵɵtwoWayListener(\"checkedChange\", function HouseholdManagementComponent_ng_template_109_nb_card_body_1_Template_nb_checkbox_checkedChange_48_listener($event) {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r10 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r10.houseDetail.CIsChange, $event) || (ctx_r10.houseDetail.CIsChange = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtext(49, \"\\u662F \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(50, \"div\", 59)(51, \"label\", 82);\n    i0.ɵɵtext(52, \" \\u662F\\u5426\\u555F\\u7528 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(53, \"nb-checkbox\", 81);\n    i0.ɵɵtwoWayListener(\"checkedChange\", function HouseholdManagementComponent_ng_template_109_nb_card_body_1_Template_nb_checkbox_checkedChange_53_listener($event) {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r10 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r10.houseDetail.CIsEnable, $event) || (ctx_r10.houseDetail.CIsEnable = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtext(54, \"\\u662F \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(55, \"div\", 83)(56, \"label\", 84);\n    i0.ɵɵtext(57, \" \\u5BA2\\u8B8A\\u6642\\u6BB5 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(58, \"div\", 85)(59, \"nb-form-field\", 86);\n    i0.ɵɵelement(60, \"nb-icon\", 87);\n    i0.ɵɵelementStart(61, \"input\", 88);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_ng_template_109_nb_card_body_1_Template_input_ngModelChange_61_listener($event) {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r10 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r10.houseDetail.changeStartDate, $event) || (ctx_r10.houseDetail.changeStartDate = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(62, \"nb-datepicker\", 89, 3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(64, \"nb-form-field\", 86);\n    i0.ɵɵelement(65, \"nb-icon\", 87);\n    i0.ɵɵelementStart(66, \"input\", 90);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_ng_template_109_nb_card_body_1_Template_input_ngModelChange_66_listener($event) {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r10 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r10.houseDetail.changeEndDate, $event) || (ctx_r10.houseDetail.changeEndDate = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(67, \"nb-datepicker\", 89, 4);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const StartDate_r24 = i0.ɵɵreference(63);\n    const EndDate_r25 = i0.ɵɵreference(68);\n    const ctx_r10 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r10.detailSelected.CBuildCaseSelected);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r10.userBuildCaseOptions);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r10.houseDetail.CHousehold);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r10.houseDetail.CFloor);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r10.houseDetail.CCustomerName);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r10.houseDetail.CNationalId);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r10.houseDetail.CMail);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r10.houseDetail.CPhone);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r10.detailSelected.CHouseTypeSelected);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r10.options.houseTypeOptions);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r10.detailSelected.CPayStatusSelected);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r10.options.payStatusOptions);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r10.detailSelected.CProgressSelected);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r10.options.progressOptions);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"checked\", ctx_r10.houseDetail.CIsChange);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtwoWayProperty(\"checked\", ctx_r10.houseDetail.CIsEnable);\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"nbDatepicker\", StartDate_r24);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r10.houseDetail.changeStartDate);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"nbDatepicker\", EndDate_r25);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r10.houseDetail.changeEndDate);\n  }\n}\nfunction HouseholdManagementComponent_ng_template_109_button_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r27 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 91);\n    i0.ɵɵlistener(\"click\", function HouseholdManagementComponent_ng_template_109_button_5_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r27);\n      const ref_r26 = i0.ɵɵnextContext().dialogRef;\n      const ctx_r10 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r10.onSubmitDetail(ref_r26));\n    });\n    i0.ɵɵtext(1, \"\\u9001\\u51FA\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction HouseholdManagementComponent_ng_template_109_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r18 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-card\", 54);\n    i0.ɵɵtemplate(1, HouseholdManagementComponent_ng_template_109_nb_card_body_1_Template, 69, 20, \"nb-card-body\", 55);\n    i0.ɵɵelementStart(2, \"nb-card-footer\", 46)(3, \"button\", 56);\n    i0.ɵɵlistener(\"click\", function HouseholdManagementComponent_ng_template_109_Template_button_click_3_listener() {\n      const ref_r26 = i0.ɵɵrestoreView(_r18).dialogRef;\n      const ctx_r10 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r10.onClose(ref_r26));\n    });\n    i0.ɵɵtext(4, \"\\u53D6\\u6D88\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(5, HouseholdManagementComponent_ng_template_109_button_5_Template, 2, 0, \"button\", 57);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r10 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r10.houseDetail);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r10.isCreate);\n  }\n}\nfunction HouseholdManagementComponent_ng_template_111_button_19_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r30 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 100);\n    i0.ɵɵlistener(\"click\", function HouseholdManagementComponent_ng_template_111_button_19_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r30);\n      const ref_r29 = i0.ɵɵnextContext().dialogRef;\n      const ctx_r10 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r10.addHouseHoldMain(ref_r29));\n    });\n    i0.ɵɵtext(1, \"\\u5132\\u5B58\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction HouseholdManagementComponent_ng_template_111_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r28 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-card\", 54)(1, \"nb-card-header\");\n    i0.ɵɵtext(2, \" \\u6236\\u5225\\u7BA1\\u7406 \\u300B\\u6279\\u6B21\\u65B0\\u589E\\u6236\\u5225\\u8CC7\\u6599 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"nb-card-body\", 58)(4, \"div\", 59)(5, \"label\", 92);\n    i0.ɵɵtext(6, \" \\u68DF\\u5225 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"input\", 93);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_ng_template_111_Template_input_ngModelChange_7_listener($event) {\n      i0.ɵɵrestoreView(_r28);\n      const ctx_r10 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r10.houseHoldMain.CBuildingName, $event) || (ctx_r10.houseHoldMain.CBuildingName = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 59)(9, \"label\", 94);\n    i0.ɵɵtext(10, \"\\u7576\\u5C64\\u6700\\u591A\\u6236\\u6578 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"input\", 95);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_ng_template_111_Template_input_ngModelChange_11_listener($event) {\n      i0.ɵɵrestoreView(_r28);\n      const ctx_r10 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r10.houseHoldMain.CHouseHoldCount, $event) || (ctx_r10.houseHoldMain.CHouseHoldCount = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(12, \"div\", 59)(13, \"label\", 96);\n    i0.ɵɵtext(14, \"\\u672C\\u68DF\\u7E3D\\u6A13\\u5C64 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"input\", 97);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_ng_template_111_Template_input_ngModelChange_15_listener($event) {\n      i0.ɵɵrestoreView(_r28);\n      const ctx_r10 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r10.houseHoldMain.CFloor, $event) || (ctx_r10.houseHoldMain.CFloor = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(16, \"nb-card-footer\", 46)(17, \"button\", 98);\n    i0.ɵɵlistener(\"click\", function HouseholdManagementComponent_ng_template_111_Template_button_click_17_listener() {\n      const ref_r29 = i0.ɵɵrestoreView(_r28).dialogRef;\n      const ctx_r10 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r10.onClose(ref_r29));\n    });\n    i0.ɵɵtext(18);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(19, HouseholdManagementComponent_ng_template_111_button_19_Template, 2, 0, \"button\", 99);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r10 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(7);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r10.houseHoldMain.CBuildingName);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r10.houseHoldMain.CHouseHoldCount);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r10.houseHoldMain.CFloor);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(\"\\u95DC\\u9589\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r10.isCreate);\n  }\n}\nexport class HouseholdManagementComponent extends BaseComponent {\n  constructor(_allow, dialogService, message, valid, _houseService, _houseHoldMainService, _buildCaseService, pettern, router, _eventService, _ultilityService) {\n    super(_allow);\n    this._allow = _allow;\n    this.dialogService = dialogService;\n    this.message = message;\n    this.valid = valid;\n    this._houseService = _houseService;\n    this._houseHoldMainService = _houseHoldMainService;\n    this._buildCaseService = _buildCaseService;\n    this.pettern = pettern;\n    this.router = router;\n    this._eventService = _eventService;\n    this._ultilityService = _ultilityService;\n    this.tempBuildCaseID = -1;\n    this.pageFirst = 1;\n    this.pageSize = 10;\n    this.pageIndex = 1;\n    this.totalRecords = 0;\n    this.options = {\n      payStatusOptions: [{\n        value: 0,\n        key: 'unpaid',\n        label: '未付款'\n      }, {\n        value: 1,\n        key: 'paid',\n        label: '已付款'\n      }, {\n        value: 2,\n        key: 'No payment required',\n        label: '無須付款'\n      }],\n      progressOptions: [{\n        \"value\": \"20%\",\n        \"label\": \"20%\"\n      }, {\n        \"value\": \"40%\",\n        \"label\": \"40%\"\n      }, {\n        \"value\": \"60%\",\n        \"label\": \"60%\"\n      }, {\n        \"value\": \"80%\",\n        \"label\": \"80%\"\n      }, {\n        \"value\": \"100%\",\n        \"label\": \"100%\"\n      }],\n      houseTypeOptions: [{\n        value: 2,\n        key: 'sales account',\n        label: '銷售戶'\n      }, {\n        value: 1,\n        key: 'landlord household',\n        label: '地主戶'\n      }]\n    };\n    this.payStatusOptions = [{\n      value: '',\n      key: 'all',\n      label: '全部'\n    }, {\n      value: 0,\n      key: 'unpaid',\n      label: '未付款'\n    }, {\n      value: 1,\n      key: 'paid',\n      label: '已付款'\n    }, {\n      value: 2,\n      key: 'No payment required',\n      label: '無須付款'\n    }];\n    this.statusOptions = [{\n      value: 0,\n      key: 'allow',\n      label: '允許'\n    }, {\n      value: 1,\n      key: 'not allowed',\n      label: '不允許'\n    }];\n    this.cIsEnableOptions = [{\n      value: null,\n      key: 'all',\n      label: '全部'\n    }, {\n      value: true,\n      key: 'enable',\n      label: '啟用'\n    }, {\n      value: false,\n      key: 'deactivate',\n      label: '停用'\n    }];\n    this.signStatusOptions = [{\n      value: 0,\n      key: 'all',\n      label: '全部'\n    }, {\n      value: 1,\n      key: 'signed back',\n      label: '已簽回'\n    }, {\n      value: 2,\n      key: 'not signed back',\n      label: '未簽回'\n    }];\n    this.houseTypeOptions = [{\n      value: '',\n      key: 'all',\n      label: '全部'\n    }, {\n      value: 1,\n      key: 'sales account',\n      label: '地主戶'\n    }, {\n      value: 2,\n      key: 'landlord household',\n      label: '銷售戶'\n    }];\n    this.buildCaseOptions = [{\n      label: '全部',\n      value: ''\n    }];\n    this.houseHoldOptions = [{\n      label: '全部',\n      value: ''\n    }];\n    this.progressOptions = [{\n      \"value\": \"\",\n      \"label\": \"全部\"\n    }, {\n      \"value\": \"20%\",\n      \"label\": \"20%\"\n    }, {\n      \"value\": \"40%\",\n      \"label\": \"40%\"\n    }, {\n      \"value\": \"60%\",\n      \"label\": \"60%\"\n    }, {\n      \"value\": \"80%\",\n      \"label\": \"80%\"\n    }, {\n      \"value\": \"100%\",\n      \"label\": \"100%\"\n    }];\n    this.initDetail = {\n      CHouseID: 0,\n      CMail: \"\",\n      CIsChange: false,\n      CPayStatus: 0,\n      CIsEnable: false,\n      CCustomerName: \"\",\n      CNationalID: \"\",\n      CProgress: \"\",\n      CHouseType: 0,\n      CHouseHold: \"\",\n      CPhone: \"\"\n    };\n    this.selectedFile = null;\n    this.buildingSelectedOptions = [{\n      value: '',\n      label: '全部'\n    }];\n    this.searchQuery = {\n      CBuildCaseSelected: null,\n      CHouseTypeSelected: this.houseTypeOptions[0],\n      CBuildingNameSelected: this.buildingSelectedOptions[0],\n      CHouseHoldSelected: this.houseHoldOptions[0],\n      CPayStatusSelected: this.payStatusOptions[0],\n      CProgressSelected: this.progressOptions[0],\n      CSignStatusSelected: this.signStatusOptions[0],\n      CIsEnableSeleted: this.cIsEnableOptions[0],\n      CFrom: '',\n      CTo: ''\n    };\n    this._eventService.receive().pipe(tap(res => {\n      if (res.action == \"GET_BUILDCASE\" /* EEvent.GET_BUILDCASE */ && !!res.payload) {\n        this.tempBuildCaseID = res.payload;\n      }\n    })).subscribe();\n  }\n  ngOnInit() {\n    this.getListBuildCase();\n  }\n  onSearch() {\n    this.getHouseList().subscribe();\n  }\n  pageChanged(newPage) {\n    this.pageIndex = newPage;\n    this.getHouseList().subscribe();\n  }\n  exportHouse() {\n    if (this.searchQuery.CBuildCaseSelected.cID) {\n      this._houseService.apiHouseExportHousePost$Json({\n        CBuildCaseID: this.searchQuery.CBuildCaseSelected.cID\n      }).subscribe(res => {\n        if (res.Entries && res.StatusCode == 0) {\n          this._ultilityService.downloadExcelFile(res.Entries, '戶別資訊範本');\n        } else {\n          this.message.showErrorMSG(res.Message);\n        }\n      });\n    }\n  }\n  triggerFileInput() {\n    this.fileInput.nativeElement.click();\n  }\n  onFileSelected(event) {\n    const input = event.target;\n    if (input.files && input.files.length > 0) {\n      this.selectedFile = input.files[0];\n      this.importExcel();\n    }\n  }\n  importExcel() {\n    if (this.selectedFile) {\n      const formData = new FormData();\n      formData.append('CFile', this.selectedFile);\n      this._houseService.apiHouseImportHousePost$Json({\n        body: {\n          CBuildCaseID: this.searchQuery.CBuildCaseSelected.cID,\n          CFile: this.selectedFile\n        }\n      }).subscribe(res => {\n        if (res.StatusCode === 0) {\n          this.message.showSucessMSG(res.Message);\n          this.getHouseList().subscribe();\n        } else {\n          this.message.showErrorMSG(res.Message);\n        }\n      });\n    }\n  }\n  getListHouseHold() {\n    this._houseService.apiHouseGetListHouseHoldPost$Json({\n      body: {\n        CBuildCaseID: this.searchQuery.CBuildCaseSelected.cID\n      }\n    }).subscribe(res => {\n      if (res.Entries && res.StatusCode == 0) {\n        this.houseHoldOptions = [{\n          value: '',\n          label: '全部'\n        }, ...res.Entries.map(e => {\n          return {\n            value: e,\n            label: e\n          };\n        })];\n        this.searchQuery.CHouseHoldSelected = this.houseHoldOptions[0];\n      }\n    });\n  }\n  formatQuery() {\n    this.bodyRequest = {\n      CBuildCaseID: this.searchQuery.CBuildCaseSelected.cID,\n      PageIndex: this.pageIndex,\n      PageSize: this.pageSize\n    };\n    if (this.searchQuery.CBuildingNameSelected.value) {\n      this.bodyRequest['CBuildingName'] = this.searchQuery.CBuildingNameSelected.value;\n    }\n    if (this.searchQuery.CBuildingNameSelected.value) {\n      this.bodyRequest['CBuildingName'] = this.searchQuery.CBuildingNameSelected.value;\n    }\n    if (this.searchQuery.CFrom && this.searchQuery.CTo) {\n      this.bodyRequest['CFloor'] = {\n        CFrom: this.searchQuery.CFrom,\n        CTo: this.searchQuery.CTo\n      };\n    }\n    if (this.searchQuery.CHouseHoldSelected.value) {\n      this.bodyRequest['CHouseHold'] = this.searchQuery.CHouseHoldSelected.value;\n    }\n    if (this.searchQuery.CHouseTypeSelected.value) {\n      this.bodyRequest['CHouseType'] = this.searchQuery.CHouseTypeSelected.value;\n    }\n    if (typeof this.searchQuery.CIsEnableSeleted.value === \"boolean\") {\n      this.bodyRequest['CIsEnable'] = this.searchQuery.CIsEnableSeleted.value;\n    }\n    if ([0, 1, 2].includes(this.searchQuery.CPayStatusSelected.value)) {\n      this.bodyRequest['CPayStatus'] = this.searchQuery.CPayStatusSelected.value;\n    }\n    if (this.searchQuery.CProgressSelected.value) {\n      this.bodyRequest['CProgress'] = this.searchQuery.CProgressSelected.value;\n    }\n    if ([1, 2].includes(this.searchQuery.CSignStatusSelected.value)) {\n      this.bodyRequest['CSignStatus'] = this.searchQuery.CSignStatusSelected.value;\n    }\n    return this.bodyRequest;\n  }\n  sortByFloorDescending(arr) {\n    return arr.sort((a, b) => (b.CFloor ?? 0) - (a.CFloor ?? 0));\n  }\n  getHouseList() {\n    return this._houseService.apiHouseGetHouseListPost$Json({\n      body: this.formatQuery()\n    }).pipe(tap(res => {\n      if (res.Entries && res.StatusCode == 0) {\n        this.houseList = this.sortByFloorDescending(res.Entries);\n        this.totalRecords = res.TotalItems;\n      }\n    }));\n  }\n  onSelectionChangeBuildCase() {\n    this.getListBuilding();\n    this.getListHouseHold();\n    this.getHouseList().subscribe();\n  }\n  getListBuildCase() {\n    this._buildCaseService.apiBuildCaseGetAllBuildCasePost$Json({\n      body: {}\n    }).pipe(tap(res => {\n      if (res.Entries && res.StatusCode == 0) {\n        this.userBuildCaseOptions = res.Entries?.length ? res.Entries.filter(a => a.CStatus === 1).map(res => {\n          return {\n            CBuildCaseName: res.CBuildCaseName,\n            cID: res.cID\n          };\n        }) : [];\n        if (this.tempBuildCaseID != -1) {\n          let index = this.userBuildCaseOptions.findIndex(x => x.cID == this.tempBuildCaseID);\n          this.searchQuery.CBuildCaseSelected = this.userBuildCaseOptions[index];\n        } else {\n          this.searchQuery.CBuildCaseSelected = this.userBuildCaseOptions[0];\n        }\n      }\n    }), tap(() => {\n      this.getListBuilding();\n      this.getListHouseHold();\n    }), concatMap(() => this.getHouseList())).subscribe();\n  }\n  getListBuilding() {\n    this._houseService.apiHouseGetListBuildingPost$Json({\n      body: {\n        CBuildCaseID: this.searchQuery.CBuildCaseSelected.cID\n      }\n    }).subscribe(res => {\n      if (res.Entries && res.StatusCode == 0) {\n        this.buildingSelectedOptions = [{\n          value: '',\n          label: '全部'\n        }, ...res.Entries.map(e => {\n          return {\n            value: e,\n            label: e\n          };\n        })];\n        this.searchQuery.CBuildingNameSelected = this.buildingSelectedOptions[0];\n      }\n    });\n  }\n  getHouseById(CID, ref) {\n    this.detailSelected = {};\n    this._houseService.apiHouseGetHouseByIdPost$Json({\n      body: {\n        CHouseID: CID\n      }\n    }).subscribe(res => {\n      if (res.Entries && res.StatusCode == 0) {\n        this.houseDetail = {\n          ...res.Entries,\n          changeStartDate: res.Entries.CChangeStartDate ? new Date(res.Entries.CChangeStartDate) : undefined,\n          changeEndDate: res.Entries.CChangeEndDate ? new Date(res.Entries.CChangeEndDate) : undefined\n        };\n        if (res.Entries.CBuildCaseId) {\n          this.detailSelected.CBuildCaseSelected = this.findItemInArray(this.userBuildCaseOptions, 'cID', res.Entries.CBuildCaseId);\n        }\n        this.detailSelected.CPayStatusSelected = this.findItemInArray(this.options.payStatusOptions, 'value', res.Entries.CPayStatus);\n        if (res.Entries.CHouseType) {\n          this.detailSelected.CHouseTypeSelected = this.findItemInArray(this.options.houseTypeOptions, 'value', res.Entries.CHouseType);\n        } else {\n          this.detailSelected.CHouseTypeSelected = this.options.houseTypeOptions[1];\n        }\n        if (res.Entries.CProgress) {\n          this.detailSelected.CProgressSelected = this.findItemInArray(this.options.progressOptions, 'value', res.Entries.CProgress);\n        }\n        if (res.Entries.CBuildCaseId) {\n          if (this.houseHoldMain) {\n            this.houseHoldMain.CBuildCaseID = res.Entries.CBuildCaseId;\n          }\n        }\n        this.dialogService.open(ref);\n      }\n    });\n  }\n  findItemInArray(array, key, value) {\n    return array.find(item => item[key] === value);\n  }\n  openModelDetail(ref, item) {\n    this.getHouseById(item.CID, ref);\n  }\n  openModel(ref) {\n    this.houseHoldMain = {\n      CBuildingName: '',\n      CFloor: undefined,\n      CHouseHoldCount: undefined\n    };\n    this.dialogService.open(ref);\n  }\n  formatDate(CChangeDate) {\n    if (CChangeDate) {\n      return moment(CChangeDate).format('YYYY-MM-DDTHH:mm:ss');\n    }\n    return '';\n  }\n  onSubmitDetail(ref) {\n    this.houseDetail.CChangeStartDate = this.houseDetail.changeStartDate ? this.formatDate(this.houseDetail.changeStartDate) : '', this.houseDetail.CChangeEndDate = this.houseDetail.changeEndDate ? this.formatDate(this.houseDetail.changeEndDate) : '', this.editHouseArgsParam = {\n      CCustomerName: this.houseDetail.CCustomerName,\n      CHouseHold: this.houseDetail.CHousehold,\n      CHouseID: this.houseDetail.CId,\n      CHouseType: this.detailSelected.CHouseTypeSelected ? this.detailSelected.CHouseTypeSelected.value : null,\n      CIsChange: this.houseDetail.CIsChange,\n      CIsEnable: this.houseDetail.CIsEnable,\n      CMail: this.houseDetail.CMail,\n      CNationalID: this.houseDetail.CNationalId,\n      CPayStatus: this.detailSelected.CPayStatusSelected ? this.detailSelected.CPayStatusSelected.value : null,\n      CPhone: this.houseDetail.CPhone,\n      CProgress: this.detailSelected.CProgressSelected ? this.detailSelected.CProgressSelected.value : null,\n      CChangeStartDate: this.houseDetail.CChangeStartDate,\n      CChangeEndDate: this.houseDetail.CChangeEndDate\n    };\n    this.validation();\n    if (this.valid.errorMessages.length > 0) {\n      this.message.showErrorMSGs(this.valid.errorMessages);\n      return;\n    }\n    this._houseService.apiHouseEditHousePost$Json({\n      body: this.editHouseArgsParam\n    }).pipe(tap(res => {\n      if (res.StatusCode === 0) {\n        this.message.showSucessMSG(\"執行成功\");\n        ref.close();\n      } else {\n        this.message.showErrorMSG(res.Message);\n        ref.close();\n      }\n    }), concatMap(() => this.getHouseList())).subscribe();\n  }\n  onSubmit(ref) {\n    let bodyReq = {\n      CCustomerName: this.houseDetail.CCustomerName,\n      CHouseHold: this.houseDetail.CHousehold,\n      CHouseID: this.houseDetail.CId,\n      CHouseType: this.houseDetail.CHouseType,\n      CIsChange: this.houseDetail.CIsChange,\n      CIsEnable: this.houseDetail.CIsEnable,\n      CMail: this.houseDetail.CMail,\n      CNationalID: this.houseDetail.CNationalId,\n      CPayStatus: this.houseDetail.CPayStatus,\n      CPhone: this.houseDetail.CPhone,\n      CProgress: this.houseDetail.CProgress\n    };\n    this._houseService.apiHouseEditHousePost$Json({\n      body: bodyReq\n    }).subscribe(res => {\n      if (res.StatusCode === 0) {\n        this.message.showSucessMSG(\"執行成功\");\n        ref.close();\n      }\n    });\n  }\n  onClose(ref) {\n    ref.close();\n  }\n  onNavidateId(type, id) {\n    const idURL = id ? id : this.searchQuery.CBuildCaseSelected.cID;\n    this.router.navigate([`/pages/household-management/${type}`, idURL]);\n  }\n  onNavidateBuildCaseIdHouseId(type, buildCaseId, houseId) {\n    this.router.navigate([`/pages/household-management/${type}`, buildCaseId, houseId]);\n  }\n  validation() {\n    this.valid.clear();\n    this.valid.required('[建案名稱]', this.houseDetail.CId);\n    this.valid.required('[戶型名稱]', this.editHouseArgsParam.CHouseHold);\n    this.valid.isStringMaxLength('[棟別]', this.editHouseArgsParam.CHouseHold, 50);\n    this.valid.required('[樓層]', this.houseDetail.CFloor);\n    this.valid.isStringMaxLength('[客戶姓名]', this.editHouseArgsParam.CCustomerName, 50);\n    if (this.editHouseArgsParam.CNationalID) {\n      this.valid.CheckTaiwanID(this.editHouseArgsParam.CNationalID);\n    }\n    this.valid.pattern('[電子郵件]', this.editHouseArgsParam.CMail, this.pettern.MailPettern);\n    this.valid.isPhoneNumber('[聯絡電話]', this.editHouseArgsParam.CPhone);\n    this.valid.required('[進度]', this.editHouseArgsParam.CProgress);\n    this.valid.required('[戶別類型]', this.detailSelected.CHouseTypeSelected.value);\n    this.valid.required('[付款狀態]', this.detailSelected.CPayStatusSelected.value);\n    if (this.houseDetail.CChangeStartDate) {\n      this.valid.required('[客變結束日期]', this.houseDetail.CChangeEndDate);\n    }\n    if (this.houseDetail.CChangeEndDate) {\n      this.valid.required('[客變開始日期]', this.houseDetail.CChangeStartDate);\n    }\n    this.valid.checkStartBeforeEnd('[開放日期]', this.houseDetail.CChangeStartDate ? this.houseDetail.CChangeStartDate : '', this.houseDetail.CChangeEndDate ? this.houseDetail.CChangeEndDate : '');\n  }\n  validationHouseHoldMain() {\n    this.valid.clear();\n    this.valid.required('[建案]', this.houseHoldMain.CBuildCaseID);\n    this.valid.required('[棟別]', this.houseHoldMain.CBuildingName);\n    this.valid.isStringMaxLength('[棟別]', this.houseHoldMain.CBuildingName, 10);\n    this.valid.isNaturalNumberInRange('[當層最多戶數]', this.houseHoldMain.CFloor, 1, 100);\n    this.valid.isNaturalNumberInRange('[本棟總樓層]', this.houseHoldMain.CHouseHoldCount, 1, 100);\n  }\n  addHouseHoldMain(ref) {\n    this.houseHoldMain.CBuildCaseID = this.searchQuery.CBuildCaseSelected.cID, this.validationHouseHoldMain();\n    if (this.valid.errorMessages.length > 0) {\n      this.message.showErrorMSGs(this.valid.errorMessages);\n      return;\n    }\n    this._houseHoldMainService.apiHouseHoldMainAddHouseHoldMainPost$Json({\n      body: this.houseHoldMain\n    }).pipe(tap(res => {\n      if (res.StatusCode === 0) {\n        this.message.showSucessMSG(\"執行成功\");\n        ref.close();\n      }\n    }), concatMap(() => this.getHouseList())).subscribe();\n  }\n  static {\n    this.ɵfac = function HouseholdManagementComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || HouseholdManagementComponent)(i0.ɵɵdirectiveInject(i1.AllowHelper), i0.ɵɵdirectiveInject(i2.NbDialogService), i0.ɵɵdirectiveInject(i3.MessageService), i0.ɵɵdirectiveInject(i4.ValidationHelper), i0.ɵɵdirectiveInject(i5.HouseService), i0.ɵɵdirectiveInject(i5.HouseHoldMainService), i0.ɵɵdirectiveInject(i5.BuildCaseService), i0.ɵɵdirectiveInject(i6.PetternHelper), i0.ɵɵdirectiveInject(i7.Router), i0.ɵɵdirectiveInject(i8.EventService), i0.ɵɵdirectiveInject(i9.UtilityService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: HouseholdManagementComponent,\n      selectors: [[\"ngx-household-management\"]],\n      viewQuery: function HouseholdManagementComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.fileInput = _t.first);\n        }\n      },\n      standalone: true,\n      features: [i0.ɵɵInheritDefinitionFeature, i0.ɵɵStandaloneFeature],\n      decls: 113,\n      vars: 23,\n      consts: [[\"fileInput\", \"\"], [\"dialogUpdateHousehold\", \"\"], [\"dialogHouseholdMain\", \"\"], [\"StartDate\", \"\"], [\"EndDate\", \"\"], [\"accent\", \"success\"], [1, \"font-bold\", \"text-[#818181]\"], [1, \"d-flex\", \"flex-wrap\"], [1, \"col-md-6\"], [1, \"form-group\", \"d-flex\", \"align-items-center\", \"w-full\"], [\"for\", \"buildingName\", 1, \"label\", \"col-3\"], [\"placeholder\", \"\\u5EFA\\u6848\", 1, \"col-9\", 3, \"ngModelChange\", \"selectedChange\", \"ngModel\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"for\", \"cHouseType\", 1, \"label\", \"col-3\"], [1, \"col-9\", 3, \"ngModelChange\", \"ngModel\"], [1, \"form-group\", \"d-flex\", \"align-items-center\"], [\"for\", \"cFloorFrom\", 1, \"label\", \"col-3\"], [1, \"ml-3\"], [\"type\", \"text\", \"id\", \"CFrom\", \"nbInput\", \"\", 1, \"w-full\", \"col-4\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"cFloorTo\", 1, \"label\", \"col-1\"], [1, \"mr-3\"], [\"type\", \"text\", \"id\", \"CTo\", \"nbInput\", \"\", 1, \"w-full\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"cHousehold\", 1, \"label\", \"col-3\"], [\"placeholder\", \"\\u6236\\u578B\", 1, \"col-9\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"cPayStatus\", 1, \"label\", \"col-3\"], [\"placeholder\", \"\\u7E73\\u6B3E\\u72C0\\u614B\", 1, \"col-9\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"cProgress\", 1, \"label\", \"col-3\"], [\"placeholder\", \"\\u9032\\u5EA6\", 1, \"col-9\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"cStatus\", 1, \"label\", \"col-3\"], [\"placeholder\", \"\\u72C0\\u614B\", 1, \"col-9\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"cSignStatus\", 1, \"label\", \"col-3\"], [\"placeholder\", \"\\u7C3D\\u56DE\\u72C0\\u614B\", 1, \"col-9\", 3, \"ngModelChange\", \"ngModel\"], [1, \"d-flex\", \"justify-content-end\", \"w-full\"], [1, \"btn\", \"btn-secondary\", \"btn-sm\", 3, \"click\"], [1, \"fas\", \"fa-search\"], [1, \"col-md-12\"], [\"class\", \"btn btn-info mx-1 btn-sm\", 3, \"click\", 4, \"ngIf\"], [1, \"btn\", \"btn-info\", \"mx-1\", \"btn-sm\", 3, \"click\"], [\"type\", \"file\", \"accept\", \".xlsx, .xls\", 2, \"display\", \"none\", 3, \"change\"], [1, \"btn\", \"btn-info\", \"btn-sm\", 3, \"click\"], [1, \"table-responsive\", \"mt-4\"], [1, \"table\", \"table-striped\", \"border\", 2, \"min-width\", \"1000px\", \"background-color\", \"#f3f3f3\"], [2, \"background-color\", \"#27ae60\", \"color\", \"white\"], [\"scope\", \"col\", 1, \"col-1\"], [\"scope\", \"col\", 1, \"col-4\"], [4, \"ngFor\", \"ngForOf\"], [1, \"d-flex\", \"justify-content-center\"], [\"aria-label\", \"Pagination\", 3, \"pageChange\", \"page\", \"pageSize\", \"collectionSize\"], [3, \"value\"], [1, \"text-center\", \"w-32\", \"px-0\"], [\"class\", \"btn btn-outline-success btn-sm text-left m-[2px]\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"btn btn-outline-success btn-sm m-[2px]\", 3, \"click\", 4, \"ngIf\"], [1, \"btn\", \"btn-outline-success\", \"btn-sm\", \"m-[2px]\", 3, \"click\"], [1, \"btn\", \"btn-outline-success\", \"btn-sm\", \"text-left\", \"m-[2px]\", 3, \"click\"], [2, \"width\", \"500px\", \"max-height\", \"95vh\"], [\"class\", \"px-4\", 4, \"ngIf\"], [1, \"btn\", \"btn-outline-secondary\", \"m-2\", \"px-8\", 3, \"click\"], [\"class\", \"btn btn-primary m-2 bg-[#169BD5] px-8\", 3, \"click\", 4, \"ngIf\"], [1, \"px-4\"], [1, \"form-group\"], [\"for\", \"cBuildCaseId\", \"baseLabel\", \"\", 1, \"required-field\", \"mr-4\", 2, \"min-width\", \"75px\"], [\"placeholder\", \"\\u5EFA\\u6848\\u540D\\u7A31\", \"disabled\", \"true\", 1, \"w-full\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"cHousehold\", \"baseLabel\", \"\", 1, \"required-field\", \"mr-4\", 2, \"min-width\", \"75px\"], [\"type\", \"text\", \"nbInput\", \"\", \"placeholder\", \"\\u6236\\u578B\\u540D\\u7A31\", 1, \"w-full\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"cFloor\", \"baseLabel\", \"\", 1, \"required-field\", \"mr-4\", 2, \"min-width\", \"75px\"], [\"type\", \"number\", \"nbInput\", \"\", \"placeholder\", \"\\u6A13\\u5C64\", \"min\", \"1\", \"max\", \"100\", \"disabled\", \"true\", 1, \"w-full\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"cCustomerName\", \"baseLabel\", \"\", 1, \"mr-4\", 2, \"min-width\", \"75px\"], [\"type\", \"text\", \"nbInput\", \"\", \"placeholder\", \"\\u5BA2\\u6236\\u59D3\\u540D\", 1, \"w-full\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"cNationalId\", \"baseLabel\", \"\", 1, \"mr-4\", 2, \"min-width\", \"75px\"], [\"type\", \"text\", \"nbInput\", \"\", \"placeholder\", \"\\u8EAB\\u5206\\u8B49\\u5B57\\u865F\", 1, \"w-full\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"cMail\", \"baseLabel\", \"\", 1, \"mr-4\", 2, \"min-width\", \"75px\"], [\"type\", \"text\", \"nbInput\", \"\", \"placeholder\", \"\\u96FB\\u5B50\\u90F5\\u4EF6\", 1, \"w-full\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"cPhone\", \"baseLabel\", \"\", 1, \"mr-4\", 2, \"min-width\", \"75px\"], [\"type\", \"text\", \"nbInput\", \"\", \"placeholder\", \"\\u806F\\u7D61\\u96FB\\u8A71\", 1, \"w-full\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"cHouseType\", \"baseLabel\", \"\", 1, \"required-field\", \"mr-4\", 2, \"min-width\", \"75px\"], [\"placeholder\", \"\\u6236\\u5225\\u985E\\u578B\", 1, \"w-full\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"cPayStatus\", \"baseLabel\", \"\", 1, \"required-field\", \"mr-4\", 2, \"min-width\", \"75px\"], [\"placeholder\", \"\\u4ED8\\u6B3E\\u72C0\\u614B\", 1, \"w-full\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"cProgress\", \"baseLabel\", \"\", 1, \"required-field\", \"mr-4\", 2, \"min-width\", \"75px\"], [\"placeholder\", \"\\u9032\\u5EA6\", 1, \"w-full\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"cIsChange\", \"baseLabel\", \"\", 1, \"mr-4\", 2, \"min-width\", \"75px\"], [\"status\", \"basic\", 3, \"checkedChange\", \"checked\"], [\"for\", \"cIsEnable\", \"baseLabel\", \"\", 1, \"mr-4\", 2, \"min-width\", \"75px\"], [1, \"form-group\", \"flex\", \"flex-row\"], [\"for\", \"cIsEnable\", \"baseLabel\", \"\", 1, \"mr-4\", \"content-center\", 2, \"min-width\", \"75px\"], [1, \"max-w-xs\", \"flex\", \"flex-row\"], [1, \"w-1/2\"], [\"nbPrefix\", \"\", \"icon\", \"calendar-outline\"], [\"nbInput\", \"\", \"type\", \"text\", \"id\", \"StartDate\", \"placeholder\", \"yyyy-mm-dd\", 1, \"w-[42%]\", \"mr-2\", 3, \"ngModelChange\", \"nbDatepicker\", \"ngModel\"], [\"format\", \"yyyy-MM-dd\"], [\"nbInput\", \"\", \"type\", \"text\", \"id\", \"EndDate\", \"placeholder\", \"yyyy-mm-dd\", 1, \"w-[42%]\", \"ml-2\", 3, \"ngModelChange\", \"nbDatepicker\", \"ngModel\"], [1, \"btn\", \"btn-primary\", \"m-2\", \"bg-[#169BD5]\", \"px-8\", 3, \"click\"], [\"for\", \"cBuildingName\", \"baseLabel\", \"\", 1, \"required-field\", \"mr-4\", 2, \"min-width\", \"75px\"], [\"type\", \"text\", \"nbInput\", \"\", \"placeholder\", \"\\u68DF\\u5225\", 1, \"w-full\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"CHouseHoldCount\", \"baseLabel\", \"\", 1, \"required-field\", \"mr-4\", 2, \"min-width\", \"75px\"], [\"type\", \"number\", \"nbInput\", \"\", \"placeholder\", \"\\u7576\\u5C64\\u6700\\u591A\\u6236\\u6578\", 1, \"w-full\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"CFloor\", \"baseLabel\", \"\", 1, \"required-field\", \"mr-4\", 2, \"min-width\", \"75px\"], [\"type\", \"number\", \"nbInput\", \"\", \"placeholder\", \"\\u672C\\u68DF\\u7E3D\\u6A13\\u5C64\", 1, \"w-full\", 3, \"ngModelChange\", \"ngModel\"], [1, \"btn\", \"btn-primary\", \"mr-4\", 3, \"click\"], [\"class\", \"btn btn-primary\", 3, \"click\", 4, \"ngIf\"], [1, \"btn\", \"btn-primary\", 3, \"click\"]],\n      template: function HouseholdManagementComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"nb-card\", 5)(1, \"nb-card-header\");\n          i0.ɵɵelement(2, \"ngx-breadcrumb\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(3, \"nb-card-body\")(4, \"h1\", 6);\n          i0.ɵɵtext(5, \"\\u60A8\\u53EF\\u8207\\u6B64\\u7BA1\\u7406\\u5404\\u6236\\u5225\\u5167\\u4E4B\\u76F8\\u95DC\\u8CC7\\u8A0A\\uFF0C\\u5305\\u542B\\u57FA\\u672C\\u8CC7\\u6599\\u3001\\u7E73\\u6B3E\\u72C0\\u6CC1\\u3001\\u4E0A\\u50B3\\u5BA2\\u8B8A\\u5716\\u9762\\u3001\\u6AA2\\u8996\\u5BA2\\u8B8A\\u7D50\\u679C\\u7B49\\u7B49\\u3002 \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(6, \"div\", 7)(7, \"div\", 8)(8, \"div\", 9)(9, \"label\", 10);\n          i0.ɵɵtext(10, \"\\u5EFA\\u6848\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(11, \"nb-select\", 11);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_Template_nb_select_ngModelChange_11_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.searchQuery.CBuildCaseSelected, $event) || (ctx.searchQuery.CBuildCaseSelected = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵlistener(\"selectedChange\", function HouseholdManagementComponent_Template_nb_select_selectedChange_11_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onSelectionChangeBuildCase());\n          });\n          i0.ɵɵtemplate(12, HouseholdManagementComponent_nb_option_12_Template, 2, 2, \"nb-option\", 12);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(13, \"div\", 8)(14, \"div\", 9)(15, \"label\", 13);\n          i0.ɵɵtext(16, \"\\u985E\\u578B\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(17, \"nb-select\", 14);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_Template_nb_select_ngModelChange_17_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.searchQuery.CHouseTypeSelected, $event) || (ctx.searchQuery.CHouseTypeSelected = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵtemplate(18, HouseholdManagementComponent_nb_option_18_Template, 2, 2, \"nb-option\", 12);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(19, \"div\", 8)(20, \"div\", 9)(21, \"label\", 10);\n          i0.ɵɵtext(22, \" \\u68DF\\u5225 \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(23, \"nb-select\", 14);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_Template_nb_select_ngModelChange_23_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.searchQuery.CBuildingNameSelected, $event) || (ctx.searchQuery.CBuildingNameSelected = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵtemplate(24, HouseholdManagementComponent_nb_option_24_Template, 2, 2, \"nb-option\", 12);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(25, \"div\", 8)(26, \"div\", 15)(27, \"label\", 16);\n          i0.ɵɵtext(28, \"\\u6A13 \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(29, \"nb-form-field\", 17)(30, \"input\", 18);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_Template_input_ngModelChange_30_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.searchQuery.CFrom, $event) || (ctx.searchQuery.CFrom = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(31, \"label\", 19);\n          i0.ɵɵtext(32, \"~ \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(33, \"nb-form-field\", 20)(34, \"input\", 21);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_Template_input_ngModelChange_34_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.searchQuery.CTo, $event) || (ctx.searchQuery.CTo = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(35, \"div\", 8)(36, \"div\", 9)(37, \"label\", 22);\n          i0.ɵɵtext(38, \" \\u6236\\u578B \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(39, \"nb-select\", 23);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_Template_nb_select_ngModelChange_39_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.searchQuery.CHouseHoldSelected, $event) || (ctx.searchQuery.CHouseHoldSelected = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵtemplate(40, HouseholdManagementComponent_nb_option_40_Template, 2, 2, \"nb-option\", 12);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(41, \"div\", 8)(42, \"div\", 9)(43, \"label\", 24);\n          i0.ɵɵtext(44, \" \\u7E73\\u6B3E\\u72C0\\u614B \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(45, \"nb-select\", 25);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_Template_nb_select_ngModelChange_45_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.searchQuery.CPayStatusSelected, $event) || (ctx.searchQuery.CPayStatusSelected = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵtemplate(46, HouseholdManagementComponent_nb_option_46_Template, 2, 2, \"nb-option\", 12);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(47, \"div\", 8)(48, \"div\", 9)(49, \"label\", 26);\n          i0.ɵɵtext(50, \" \\u9032\\u5EA6 \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(51, \"nb-select\", 27);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_Template_nb_select_ngModelChange_51_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.searchQuery.CProgressSelected, $event) || (ctx.searchQuery.CProgressSelected = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵtemplate(52, HouseholdManagementComponent_nb_option_52_Template, 2, 2, \"nb-option\", 12);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(53, \"div\", 8)(54, \"div\", 9)(55, \"label\", 28);\n          i0.ɵɵtext(56, \" \\u72C0\\u614B \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(57, \"nb-select\", 29);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_Template_nb_select_ngModelChange_57_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.searchQuery.CIsEnableSeleted, $event) || (ctx.searchQuery.CIsEnableSeleted = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵtemplate(58, HouseholdManagementComponent_nb_option_58_Template, 2, 2, \"nb-option\", 12);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(59, \"div\", 8)(60, \"div\", 9)(61, \"label\", 30);\n          i0.ɵɵtext(62, \" \\u7C3D\\u56DE\\u72C0\\u614B \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(63, \"nb-select\", 31);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_Template_nb_select_ngModelChange_63_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.searchQuery.CSignStatusSelected, $event) || (ctx.searchQuery.CSignStatusSelected = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵtemplate(64, HouseholdManagementComponent_nb_option_64_Template, 2, 2, \"nb-option\", 12);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(65, \"div\", 8)(66, \"div\", 32)(67, \"button\", 33);\n          i0.ɵɵlistener(\"click\", function HouseholdManagementComponent_Template_button_click_67_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onSearch());\n          });\n          i0.ɵɵtext(68, \" \\u67E5\\u8A62 \");\n          i0.ɵɵelement(69, \"i\", 34);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(70, \"div\", 35)(71, \"div\", 32);\n          i0.ɵɵtemplate(72, HouseholdManagementComponent_button_72_Template, 2, 0, \"button\", 36);\n          i0.ɵɵelementStart(73, \"button\", 37);\n          i0.ɵɵlistener(\"click\", function HouseholdManagementComponent_Template_button_click_73_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onNavidateId(\"modify-floor-plan\"));\n          });\n          i0.ɵɵtext(74, \" \\u4FEE\\u6539\\u6A13\\u5C64\\u6236\\u578B \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(75, \"button\", 37);\n          i0.ɵɵlistener(\"click\", function HouseholdManagementComponent_Template_button_click_75_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.exportHouse());\n          });\n          i0.ɵɵtext(76, \" \\u532F\\u51FA\\u6236\\u5225\\u660E\\u7D30\\u6A94 \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(77, \"input\", 38, 0);\n          i0.ɵɵlistener(\"change\", function HouseholdManagementComponent_Template_input_change_77_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onFileSelected($event));\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(79, \"button\", 39);\n          i0.ɵɵlistener(\"click\", function HouseholdManagementComponent_Template_button_click_79_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.triggerFileInput());\n          });\n          i0.ɵɵtext(80, \" \\u532F\\u5165\\u6236\\u5225\\u660E\\u7D30\\u6A94 \");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(81, \"div\", 40)(82, \"table\", 41)(83, \"thead\")(84, \"tr\", 42)(85, \"th\", 43);\n          i0.ɵɵtext(86, \"\\u68DF\\u5225\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(87, \"th\", 43);\n          i0.ɵɵtext(88, \"\\u6236\\u578B\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(89, \"th\", 43);\n          i0.ɵɵtext(90, \"\\u6A13\\u5C64\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(91, \"th\", 43);\n          i0.ɵɵtext(92, \"\\u5730\\u4E3B\\u6236\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(93, \"th\", 43);\n          i0.ɵɵtext(94, \"\\u985E\\u578B\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(95, \"th\", 43);\n          i0.ɵɵtext(96, \"\\u9032\\u5EA6\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(97, \"th\", 43);\n          i0.ɵɵtext(98, \"\\u7E73\\u6B3E\\u72C0\\u614B\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(99, \"th\", 43);\n          i0.ɵɵtext(100, \"\\u7C3D\\u56DE\\u72C0\\u614B\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(101, \"th\", 43);\n          i0.ɵɵtext(102, \"\\u72C0\\u614B\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(103, \"th\", 44);\n          i0.ɵɵtext(104, \"\\u64CD\\u4F5C\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(105, \"tbody\");\n          i0.ɵɵtemplate(106, HouseholdManagementComponent_tr_106_Template, 24, 14, \"tr\", 45);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(107, \"nb-card-footer\", 46)(108, \"ngb-pagination\", 47);\n          i0.ɵɵtwoWayListener(\"pageChange\", function HouseholdManagementComponent_Template_ngb_pagination_pageChange_108_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.pageIndex, $event) || (ctx.pageIndex = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵlistener(\"pageChange\", function HouseholdManagementComponent_Template_ngb_pagination_pageChange_108_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.pageChanged($event));\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(109, HouseholdManagementComponent_ng_template_109_Template, 6, 2, \"ng-template\", null, 1, i0.ɵɵtemplateRefExtractor)(111, HouseholdManagementComponent_ng_template_111_Template, 20, 5, \"ng-template\", null, 2, i0.ɵɵtemplateRefExtractor);\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(11);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchQuery.CBuildCaseSelected);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngForOf\", ctx.userBuildCaseOptions);\n          i0.ɵɵadvance(5);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchQuery.CHouseTypeSelected);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngForOf\", ctx.houseTypeOptions);\n          i0.ɵɵadvance(5);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchQuery.CBuildingNameSelected);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngForOf\", ctx.buildingSelectedOptions);\n          i0.ɵɵadvance(6);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchQuery.CFrom);\n          i0.ɵɵadvance(4);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchQuery.CTo);\n          i0.ɵɵadvance(5);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchQuery.CHouseHoldSelected);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngForOf\", ctx.houseHoldOptions);\n          i0.ɵɵadvance(5);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchQuery.CPayStatusSelected);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngForOf\", ctx.payStatusOptions);\n          i0.ɵɵadvance(5);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchQuery.CProgressSelected);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngForOf\", ctx.progressOptions);\n          i0.ɵɵadvance(5);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchQuery.CIsEnableSeleted);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngForOf\", ctx.cIsEnableOptions);\n          i0.ɵɵadvance(5);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchQuery.CSignStatusSelected);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngForOf\", ctx.signStatusOptions);\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"ngIf\", ctx.isCreate);\n          i0.ɵɵadvance(34);\n          i0.ɵɵproperty(\"ngForOf\", ctx.houseList);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtwoWayProperty(\"page\", ctx.pageIndex);\n          i0.ɵɵproperty(\"pageSize\", ctx.pageSize)(\"collectionSize\", ctx.totalRecords);\n        }\n      },\n      dependencies: [CommonModule, i10.NgForOf, i10.NgIf, SharedModule, i11.DefaultValueAccessor, i11.NumberValueAccessor, i11.NgControlStatus, i11.MinValidator, i11.MaxValidator, i11.NgModel, i2.NbCardComponent, i2.NbCardBodyComponent, i2.NbCardFooterComponent, i2.NbCardHeaderComponent, i2.NbCheckboxComponent, i2.NbInputDirective, i2.NbSelectComponent, i2.NbOptionComponent, i2.NbFormFieldComponent, i2.NbPrefixDirective, i2.NbIconComponent, i2.NbDatepickerDirective, i2.NbDatepickerComponent, i12.NgbPagination, i13.BreadcrumbComponent, i14.BaseLabelDirective, NbDatepickerModule, NbDateFnsDateModule],\n      styles: [\"/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsImZpbGUiOiJob3VzZWhvbGQtbWFuYWdlbWVudC5jb21wb25lbnQuc2NzcyJ9 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvcGFnZXMvaG91c2Vob2xkLW1hbmFnZW1lbnQvaG91c2Vob2xkLW1hbmFnZW1lbnQuY29tcG9uZW50LnNjc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IjtBQUNBLG9MQUFvTCIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["SharedModule", "CommonModule", "NbDatepickerModule", "BaseComponent", "concatMap", "tap", "NbDateFnsDateModule", "moment", "EEvent", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵproperty", "case_r2", "ɵɵadvance", "ɵɵtextInterpolate1", "CBuildCaseName", "case_r3", "label", "case_r4", "case_r5", "case_r6", "case_r7", "case_r8", "case_r9", "ɵɵlistener", "HouseholdManagementComponent_button_72_Template_button_click_0_listener", "ɵɵrestoreView", "_r10", "ctx_r10", "ɵɵnextContext", "dialogHouseholdMain_r12", "ɵɵreference", "ɵɵresetView", "openModel", "HouseholdManagementComponent_tr_106_button_20_Template_button_click_0_listener", "_r14", "item_r15", "$implicit", "dialogUpdateHousehold_r16", "openModelDetail", "HouseholdManagementComponent_tr_106_button_21_Template_button_click_0_listener", "_r17", "onNavidateBuildCaseIdHouseId", "searchQuery", "CBuildCaseSelected", "cID", "CID", "ɵɵtemplate", "HouseholdManagementComponent_tr_106_button_20_Template", "HouseholdManagementComponent_tr_106_button_21_Template", "HouseholdManagementComponent_tr_106_Template_button_click_22_listener", "_r13", "ɵɵtextInterpolate", "CBuildingName", "CHouseHold", "CFloor", "ɵɵtextInterpolate2", "CHouseType", "CIsChange", "CProgress", "ɵɵtextInterpolate3", "CPayStatus", "CSignStatus", "CIsEnable", "isUpdate", "status_r20", "status_r21", "status_r22", "status_r23", "ɵɵtwoWayListener", "HouseholdManagementComponent_ng_template_109_nb_card_body_1_Template_nb_select_ngModelChange_4_listener", "$event", "_r19", "ɵɵtwoWayBindingSet", "detailSelected", "HouseholdManagementComponent_ng_template_109_nb_card_body_1_nb_option_5_Template", "HouseholdManagementComponent_ng_template_109_nb_card_body_1_Template_input_ngModelChange_9_listener", "houseDetail", "CHousehold", "HouseholdManagementComponent_ng_template_109_nb_card_body_1_Template_input_ngModelChange_13_listener", "HouseholdManagementComponent_ng_template_109_nb_card_body_1_Template_input_ngModelChange_17_listener", "CCustomerName", "HouseholdManagementComponent_ng_template_109_nb_card_body_1_Template_input_ngModelChange_21_listener", "CNationalId", "HouseholdManagementComponent_ng_template_109_nb_card_body_1_Template_input_ngModelChange_25_listener", "CMail", "HouseholdManagementComponent_ng_template_109_nb_card_body_1_Template_input_ngModelChange_29_listener", "CPhone", "HouseholdManagementComponent_ng_template_109_nb_card_body_1_Template_nb_select_ngModelChange_33_listener", "CHouseTypeSelected", "HouseholdManagementComponent_ng_template_109_nb_card_body_1_nb_option_34_Template", "HouseholdManagementComponent_ng_template_109_nb_card_body_1_Template_nb_select_ngModelChange_38_listener", "CPayStatusSelected", "HouseholdManagementComponent_ng_template_109_nb_card_body_1_nb_option_39_Template", "HouseholdManagementComponent_ng_template_109_nb_card_body_1_Template_nb_select_ngModelChange_43_listener", "CProgressSelected", "HouseholdManagementComponent_ng_template_109_nb_card_body_1_nb_option_44_Template", "HouseholdManagementComponent_ng_template_109_nb_card_body_1_Template_nb_checkbox_checkedChange_48_listener", "HouseholdManagementComponent_ng_template_109_nb_card_body_1_Template_nb_checkbox_checkedChange_53_listener", "ɵɵelement", "HouseholdManagementComponent_ng_template_109_nb_card_body_1_Template_input_ngModelChange_61_listener", "changeStartDate", "HouseholdManagementComponent_ng_template_109_nb_card_body_1_Template_input_ngModelChange_66_listener", "changeEndDate", "ɵɵtwoWayProperty", "userBuildCaseOptions", "options", "houseTypeOptions", "payStatusOptions", "progressOptions", "StartDate_r24", "EndDate_r25", "HouseholdManagementComponent_ng_template_109_button_5_Template_button_click_0_listener", "_r27", "ref_r26", "dialogRef", "onSubmitDetail", "HouseholdManagementComponent_ng_template_109_nb_card_body_1_Template", "HouseholdManagementComponent_ng_template_109_Template_button_click_3_listener", "_r18", "onClose", "HouseholdManagementComponent_ng_template_109_button_5_Template", "isCreate", "HouseholdManagementComponent_ng_template_111_button_19_Template_button_click_0_listener", "_r30", "ref_r29", "addHouseHoldMain", "HouseholdManagementComponent_ng_template_111_Template_input_ngModelChange_7_listener", "_r28", "houseHoldMain", "HouseholdManagementComponent_ng_template_111_Template_input_ngModelChange_11_listener", "CHouseHoldCount", "HouseholdManagementComponent_ng_template_111_Template_input_ngModelChange_15_listener", "HouseholdManagementComponent_ng_template_111_Template_button_click_17_listener", "HouseholdManagementComponent_ng_template_111_button_19_Template", "HouseholdManagementComponent", "constructor", "_allow", "dialogService", "message", "valid", "_houseService", "_houseHoldMainService", "_buildCaseService", "pettern", "router", "_eventService", "_ultilityService", "tempBuildCaseID", "pageFirst", "pageSize", "pageIndex", "totalRecords", "value", "key", "statusOptions", "cIsEnableOptions", "signStatusOptions", "buildCaseOptions", "houseHoldOptions", "initDetail", "CHouseID", "CNationalID", "selectedFile", "buildingSelectedOptions", "CBuildingNameSelected", "CHouseHoldSelected", "CSignStatusSelected", "CIsEnableSeleted", "CFrom", "CTo", "receive", "pipe", "res", "action", "payload", "subscribe", "ngOnInit", "getListBuildCase", "onSearch", "getHouseList", "pageChanged", "newPage", "exportHouse", "apiHouseExportHousePost$Json", "CBuildCaseID", "Entries", "StatusCode", "downloadExcelFile", "showErrorMSG", "Message", "triggerFileInput", "fileInput", "nativeElement", "click", "onFileSelected", "event", "input", "target", "files", "length", "importExcel", "formData", "FormData", "append", "apiHouseImportHousePost$Json", "body", "CFile", "showSucessMSG", "getListHouseHold", "apiHouseGetListHouseHoldPost$Json", "map", "e", "formatQuery", "bodyRequest", "PageIndex", "PageSize", "includes", "sortByFloorDescending", "arr", "sort", "a", "b", "apiHouseGetHouseListPost$Json", "houseList", "TotalItems", "onSelectionChangeBuildCase", "getListBuilding", "apiBuildCaseGetAllBuildCasePost$Json", "filter", "CStatus", "index", "findIndex", "x", "apiHouseGetListBuildingPost$Json", "getHouseById", "ref", "apiHouseGetHouseByIdPost$Json", "CChangeStartDate", "Date", "undefined", "CChangeEndDate", "CBuildCaseId", "findItemInArray", "open", "array", "find", "item", "formatDate", "CChangeDate", "format", "editHouseArgsParam", "CId", "validation", "errorMessages", "showErrorMSGs", "apiHouseEditHousePost$Json", "close", "onSubmit", "bodyReq", "onNavidateId", "type", "id", "idURL", "navigate", "buildCaseId", "houseId", "clear", "required", "isStringMaxLength", "CheckTaiwanID", "pattern", "MailPettern", "isPhoneNumber", "checkStartBeforeEnd", "validationHouseHoldMain", "isNaturalNumberInRange", "apiHouseHoldMainAddHouseHoldMainPost$Json", "ɵɵdirectiveInject", "i1", "AllowHelper", "i2", "NbDialogService", "i3", "MessageService", "i4", "ValidationHelper", "i5", "HouseService", "HouseHoldMainService", "BuildCaseService", "i6", "PetternHelper", "i7", "Router", "i8", "EventService", "i9", "UtilityService", "selectors", "viewQuery", "HouseholdManagementComponent_Query", "rf", "ctx", "HouseholdManagementComponent_Template_nb_select_ngModelChange_11_listener", "_r1", "HouseholdManagementComponent_Template_nb_select_selected<PERSON><PERSON>e_11_listener", "HouseholdManagementComponent_nb_option_12_Template", "HouseholdManagementComponent_Template_nb_select_ngModelChange_17_listener", "HouseholdManagementComponent_nb_option_18_Template", "HouseholdManagementComponent_Template_nb_select_ngModelChange_23_listener", "HouseholdManagementComponent_nb_option_24_Template", "HouseholdManagementComponent_Template_input_ngModelChange_30_listener", "HouseholdManagementComponent_Template_input_ngModelChange_34_listener", "HouseholdManagementComponent_Template_nb_select_ngModelChange_39_listener", "HouseholdManagementComponent_nb_option_40_Template", "HouseholdManagementComponent_Template_nb_select_ngModelChange_45_listener", "HouseholdManagementComponent_nb_option_46_Template", "HouseholdManagementComponent_Template_nb_select_ngModelChange_51_listener", "HouseholdManagementComponent_nb_option_52_Template", "HouseholdManagementComponent_Template_nb_select_ngModelChange_57_listener", "HouseholdManagementComponent_nb_option_58_Template", "HouseholdManagementComponent_Template_nb_select_ngModelChange_63_listener", "HouseholdManagementComponent_nb_option_64_Template", "HouseholdManagementComponent_Template_button_click_67_listener", "HouseholdManagementComponent_button_72_Template", "HouseholdManagementComponent_Template_button_click_73_listener", "HouseholdManagementComponent_Template_button_click_75_listener", "HouseholdManagementComponent_Template_input_change_77_listener", "HouseholdManagementComponent_Template_button_click_79_listener", "HouseholdManagementComponent_tr_106_Template", "HouseholdManagementComponent_Template_ngb_pagination_pageChange_108_listener", "HouseholdManagementComponent_ng_template_109_Template", "ɵɵtemplateRefExtractor", "HouseholdManagementComponent_ng_template_111_Template", "i10", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "i11", "DefaultValueAccessor", "NumberValueAccessor", "NgControlStatus", "MinValidator", "MaxValidator", "NgModel", "NbCardComponent", "NbCardBodyComponent", "NbCardFooterComponent", "NbCardHeaderComponent", "NbCheckboxComponent", "NbInputDirective", "NbSelectComponent", "NbOptionComponent", "NbFormFieldComponent", "NbPrefixDirective", "NbIconComponent", "NbDatepickerDirective", "NbDatepickerComponent", "i12", "NgbPagination", "i13", "BreadcrumbComponent", "i14", "BaseLabelDirective", "styles"], "sources": ["C:\\Users\\<USER>\\Documents\\jeansalechange\\adminFront\\src\\app\\pages\\household-management\\household-management.component.ts", "C:\\Users\\<USER>\\Documents\\jeansalechange\\adminFront\\src\\app\\pages\\household-management\\household-management.component.html"], "sourcesContent": ["import { Component, ElementRef, OnInit, ViewChild } from '@angular/core';\r\nimport { SharedModule } from '../components/shared.module';\r\nimport { CommonModule } from '@angular/common';\r\nimport { NbDatepickerModule, NbDialogService } from '@nebular/theme';\r\nimport { MessageService } from 'src/app/shared/services/message.service';\r\nimport { ValidationHelper } from 'src/app/shared/helper/validationHelper';\r\nimport { BuildCaseService, HouseHoldMainService, HouseService } from 'src/services/api/services';\r\nimport { PetternHelper } from 'src/app/shared/helper/petternHelper';\r\nimport { TypeMailPipe } from 'src/app/@theme/pipes/mapping.pipe';\r\nimport { Router } from '@angular/router';\r\nimport { BaseComponent } from '../components/base/baseComponent';\r\nimport { AllowHelper } from 'src/app/shared/helper/allowHelper';\r\nimport { AddHouseHoldMain, EditHouseArgs, GetHouseList<PERSON>rgs, GetHouseListRes, TblHouse } from 'src/services/api/models';\r\nimport { concatMap, tap } from 'rxjs';\r\nimport { NbDateFnsDateModule } from '@nebular/date-fns';\r\nimport * as moment from 'moment';\r\nimport { EEvent, EventService, IEvent } from 'src/app/shared/services/event.service';\r\nimport { UtilityService } from 'src/app/shared/services/utility.service';\r\n\r\nexport interface selectItem {\r\n  label: string,\r\n  value: number | string,\r\n  key?: string\r\n}\r\n\r\ninterface HouseDetailExtension {\r\n  changeStartDate: string;\r\n  changeEndDate: string;\r\n}\r\nexport interface SearchQuery {\r\n  CBuildCaseSelected?: any | null;\r\n  CHouseTypeSelected?: any | null;\r\n  CBuildingNameSelected?: any | null;\r\n  CHouseHoldSelected?: any | null;\r\n  CPayStatusSelected?: any | null;\r\n  CProgressSelected?: any | null;\r\n  CSignStatusSelected?: any | null;\r\n  CIsEnableSeleted?: any | null;\r\n  CFrom?: any | null;\r\n  CTo?: any | null;\r\n}\r\n\r\n@Component({\r\n  selector: 'ngx-household-management',\r\n  templateUrl: './household-management.component.html',\r\n  styleUrls: ['./household-management.component.scss'],\r\n  standalone: true,\r\n  imports: [CommonModule, SharedModule, TypeMailPipe, NbDatepickerModule, NbDateFnsDateModule,],\r\n})\r\n\r\nexport class HouseholdManagementComponent extends BaseComponent implements OnInit {\r\n  tempBuildCaseID: number = -1\r\n  constructor(\r\n    private _allow: AllowHelper,\r\n    private dialogService: NbDialogService,\r\n    private message: MessageService,\r\n    private valid: ValidationHelper,\r\n    private _houseService: HouseService,\r\n    private _houseHoldMainService: HouseHoldMainService,\r\n    private _buildCaseService: BuildCaseService,\r\n    private pettern: PetternHelper,\r\n    private router: Router,\r\n    private _eventService: EventService,\r\n    private _ultilityService: UtilityService,\r\n  ) {\r\n    super(_allow)\r\n    this.searchQuery = {\r\n      CBuildCaseSelected: null,\r\n      CHouseTypeSelected: this.houseTypeOptions[0],\r\n      CBuildingNameSelected: this.buildingSelectedOptions[0],\r\n      CHouseHoldSelected: this.houseHoldOptions[0],\r\n      CPayStatusSelected: this.payStatusOptions[0],\r\n      CProgressSelected: this.progressOptions[0],\r\n      CSignStatusSelected: this.signStatusOptions[0],\r\n      CIsEnableSeleted: this.cIsEnableOptions[0],\r\n      CFrom: '',\r\n      CTo: ''\r\n    }\r\n    this._eventService.receive().pipe(\r\n      tap((res: IEvent) => {\r\n        if (res.action == EEvent.GET_BUILDCASE && !!res.payload) {\r\n          this.tempBuildCaseID = res.payload\r\n        }\r\n      })\r\n    ).subscribe()\r\n  }\r\n\r\n  override pageFirst = 1;\r\n  override pageSize = 10;\r\n  override pageIndex = 1;\r\n  override totalRecords = 0;\r\n\r\n  options = {\r\n    payStatusOptions: [\r\n      {\r\n        value: 0,\r\n        key: 'unpaid',\r\n        label: '未付款',\r\n      },\r\n      {\r\n        value: 1,\r\n        key: 'paid',\r\n        label: '已付款',\r\n      },\r\n      {\r\n        value: 2,\r\n        key: 'No payment required',\r\n        label: '無須付款',\r\n      }\r\n    ],\r\n    progressOptions: [\r\n      {\r\n        \"value\": \"20%\",\r\n        \"label\": \"20%\"\r\n      },\r\n      {\r\n        \"value\": \"40%\",\r\n        \"label\": \"40%\"\r\n      },\r\n      {\r\n        \"value\": \"60%\",\r\n        \"label\": \"60%\"\r\n      },\r\n      {\r\n        \"value\": \"80%\",\r\n        \"label\": \"80%\"\r\n      },\r\n      {\r\n        \"value\": \"100%\",\r\n        \"label\": \"100%\"\r\n      },\r\n    ],\r\n    houseTypeOptions: [\r\n      {\r\n        value: 2,\r\n        key: 'sales account',\r\n        label: '銷售戶',\r\n      }, {\r\n        value: 1,\r\n        key: 'landlord household',\r\n        label: '地主戶',\r\n      }\r\n    ]\r\n  }\r\n\r\n  payStatusOptions: selectItem[] = [\r\n    {\r\n      value: '',\r\n      key: 'all',\r\n      label: '全部',\r\n    },\r\n    {\r\n      value: 0,\r\n      key: 'unpaid',\r\n      label: '未付款',\r\n    },\r\n    {\r\n      value: 1,\r\n      key: 'paid',\r\n      label: '已付款',\r\n    },\r\n    {\r\n      value: 2,\r\n      key: 'No payment required',\r\n      label: '無須付款',\r\n    }\r\n  ]\r\n\r\n  statusOptions: selectItem[] = [\r\n    {\r\n      value: 0,\r\n      key: 'allow',\r\n      label: '允許',\r\n    },\r\n    {\r\n      value: 1,\r\n      key: 'not allowed',\r\n      label: '不允許',\r\n    }\r\n  ]\r\n\r\n  cIsEnableOptions = [\r\n    {\r\n      value: null,\r\n      key: 'all',\r\n      label: '全部',\r\n    },\r\n    {\r\n      value: true,\r\n      key: 'enable',\r\n      label: '啟用',\r\n    },\r\n    {\r\n      value: false,\r\n      key: 'deactivate',\r\n      label: '停用',\r\n    }\r\n  ]\r\n\r\n  signStatusOptions = [\r\n    {\r\n      value: 0,\r\n      key: 'all',\r\n      label: '全部',\r\n    },\r\n    {\r\n      value: 1,\r\n      key: 'signed back',\r\n      label: '已簽回',\r\n    },\r\n    {\r\n      value: 2,\r\n      key: 'not signed back',\r\n      label: '未簽回',\r\n    }\r\n  ]\r\n\r\n  houseTypeOptions = [\r\n    {\r\n      value: '',\r\n      key: 'all',\r\n      label: '全部',\r\n    },\r\n    {\r\n      value: 1,\r\n      key: 'sales account',\r\n      label: '地主戶',\r\n    },\r\n    {\r\n      value: 2,\r\n      key: 'landlord household',\r\n      label: '銷售戶',\r\n    }\r\n  ]\r\n\r\n\r\n  searchQuery: SearchQuery\r\n  detailSelected: SearchQuery\r\n\r\n  buildCaseOptions: any[] = [{ label: '全部', value: '' }]\r\n  houseHoldOptions: any[] = [{ label: '全部', value: '' }]\r\n  progressOptions: any[] = [\r\n    {\r\n      \"value\": \"\",\r\n      \"label\": \"全部\"\r\n    },\r\n    {\r\n      \"value\": \"20%\",\r\n      \"label\": \"20%\"\r\n    },\r\n    {\r\n      \"value\": \"40%\",\r\n      \"label\": \"40%\"\r\n    },\r\n    {\r\n      \"value\": \"60%\",\r\n      \"label\": \"60%\"\r\n    },\r\n    {\r\n      \"value\": \"80%\",\r\n      \"label\": \"80%\"\r\n    },\r\n    {\r\n      \"value\": \"100%\",\r\n      \"label\": \"100%\"\r\n    }\r\n  ]\r\n\r\n\r\n  userBuildCaseOptions: any\r\n  initDetail = {\r\n    CHouseID: 0,\r\n    CMail: \"\",\r\n    CIsChange: false,\r\n    CPayStatus: 0,\r\n    CIsEnable: false,\r\n    CCustomerName: \"\",\r\n    CNationalID: \"\",\r\n    CProgress: \"\",\r\n    CHouseType: 0,\r\n    CHouseHold: \"\",\r\n    CPhone: \"\"\r\n  }\r\n\r\n  override ngOnInit(): void {\r\n    this.getListBuildCase()\r\n\r\n  }\r\n\r\n\r\n  onSearch() {\r\n    this.getHouseList().subscribe()\r\n  }\r\n\r\n  pageChanged(newPage: number) {\r\n    this.pageIndex = newPage;\r\n    this.getHouseList().subscribe()\r\n  }\r\n\r\n  exportHouse() {\r\n    if (this.searchQuery.CBuildCaseSelected.cID) {\r\n      this._houseService.apiHouseExportHousePost$Json({\r\n        CBuildCaseID: this.searchQuery.CBuildCaseSelected.cID\r\n      }).subscribe(res => {\r\n        if (res.Entries && res.StatusCode == 0) {\r\n          this._ultilityService.downloadExcelFile(\r\n            res.Entries, '戶別資訊範本'\r\n          )\r\n        } else {\r\n          this.message.showErrorMSG(res.Message!);\r\n        }\r\n      })\r\n    }\r\n  }\r\n\r\n  selectedFile: File | null = null;\r\n  @ViewChild('fileInput') fileInput: ElementRef<HTMLInputElement>;\r\n\r\n\r\n  triggerFileInput(): void {\r\n    this.fileInput.nativeElement.click();\r\n  }\r\n\r\n  onFileSelected(event: Event): void {\r\n    const input = event.target as HTMLInputElement;\r\n    if (input.files && input.files.length > 0) {\r\n      this.selectedFile = input.files[0];\r\n      this.importExcel();\r\n    }\r\n  }\r\n\r\n  importExcel(): void {\r\n    if (this.selectedFile) {\r\n      const formData = new FormData();\r\n      formData.append('CFile', this.selectedFile);\r\n      this._houseService.apiHouseImportHousePost$Json({\r\n        body: {\r\n          CBuildCaseID: this.searchQuery.CBuildCaseSelected.cID,\r\n          CFile: this.selectedFile\r\n        }\r\n      }).subscribe(res => {\r\n        if (res.StatusCode === 0) {\r\n          this.message.showSucessMSG(res.Message!);\r\n          this.getHouseList().subscribe()\r\n        } else {\r\n          this.message.showErrorMSG(res.Message!);\r\n        }\r\n      });\r\n    }\r\n  }\r\n\r\n\r\n  getListHouseHold() {\r\n    this._houseService.apiHouseGetListHouseHoldPost$Json({\r\n      body: { CBuildCaseID: this.searchQuery.CBuildCaseSelected.cID }\r\n    }).subscribe(res => {\r\n      if (res.Entries && res.StatusCode == 0) {\r\n        this.houseHoldOptions = [{\r\n          value: '', label: '全部'\r\n        }, ...res.Entries.map(e => {\r\n          return { value: e, label: e }\r\n        })]\r\n        this.searchQuery.CHouseHoldSelected = this.houseHoldOptions[0]\r\n      }\r\n    })\r\n  }\r\n\r\n  houseList: any\r\n  bodyRequest: GetHouseListArgs\r\n\r\n  formatQuery() {\r\n    this.bodyRequest = {\r\n      CBuildCaseID: this.searchQuery.CBuildCaseSelected.cID,\r\n      PageIndex: this.pageIndex,\r\n      PageSize: this.pageSize\r\n    }\r\n\r\n    if (this.searchQuery.CBuildingNameSelected.value) {\r\n      this.bodyRequest['CBuildingName'] = this.searchQuery.CBuildingNameSelected.value\r\n    }\r\n\r\n    if (this.searchQuery.CBuildingNameSelected.value) {\r\n      this.bodyRequest['CBuildingName'] = this.searchQuery.CBuildingNameSelected.value\r\n    }\r\n    if (this.searchQuery.CFrom && this.searchQuery.CTo) {\r\n      this.bodyRequest['CFloor'] = { CFrom: this.searchQuery.CFrom, CTo: this.searchQuery.CTo }\r\n    }\r\n    if (this.searchQuery.CHouseHoldSelected.value) {\r\n      this.bodyRequest['CHouseHold'] = this.searchQuery.CHouseHoldSelected.value\r\n    }\r\n\r\n    if (this.searchQuery.CHouseTypeSelected.value) {\r\n      this.bodyRequest['CHouseType'] = this.searchQuery.CHouseTypeSelected.value\r\n    }\r\n    if (typeof this.searchQuery.CIsEnableSeleted.value === \"boolean\") {\r\n      this.bodyRequest['CIsEnable'] = this.searchQuery.CIsEnableSeleted.value\r\n    }\r\n    if ([0, 1, 2].includes(this.searchQuery.CPayStatusSelected.value)) {\r\n      this.bodyRequest['CPayStatus'] = this.searchQuery.CPayStatusSelected.value\r\n    }\r\n    if (this.searchQuery.CProgressSelected.value) {\r\n      this.bodyRequest['CProgress'] = this.searchQuery.CProgressSelected.value\r\n    }\r\n    if ([1, 2].includes(this.searchQuery.CSignStatusSelected.value)) {\r\n      this.bodyRequest['CSignStatus'] = this.searchQuery.CSignStatusSelected.value\r\n    }\r\n\r\n    return this.bodyRequest\r\n\r\n  }\r\n\r\n\r\n  sortByFloorDescending(arr: GetHouseListRes[]): GetHouseListRes[] {\r\n    return arr.sort((a, b) => (b.CFloor ?? 0) - (a.CFloor ?? 0));\r\n  }\r\n  getHouseList() {\r\n    return this._houseService.apiHouseGetHouseListPost$Json({\r\n      body: this.formatQuery()\r\n    }).pipe(\r\n      tap(res => {\r\n        if (res.Entries && res.StatusCode == 0) {\r\n          this.houseList = this.sortByFloorDescending(res.Entries)\r\n          this.totalRecords = res.TotalItems!\r\n        }\r\n      })\r\n    )\r\n  }\r\n\r\n\r\n  userBuildCaseSelected: any\r\n  onSelectionChangeBuildCase() {\r\n    this.getListBuilding()\r\n    this.getListHouseHold()\r\n    this.getHouseList().subscribe()\r\n  }\r\n  getListBuildCase() {\r\n    this._buildCaseService.apiBuildCaseGetAllBuildCasePost$Json({ body: {} }).pipe(\r\n      tap(res => {\r\n        if (res.Entries && res.StatusCode == 0) {\r\n          this.userBuildCaseOptions = res.Entries?.length ?  res.Entries.filter((a: any) => a.CStatus === 1).map(res => {\r\n            return {\r\n              CBuildCaseName: res.CBuildCaseName,\r\n              cID: res.cID\r\n            }\r\n          }) : []\r\n\r\n          if (this.tempBuildCaseID != -1) {\r\n            let index = this.userBuildCaseOptions.findIndex((x: any) => x.cID == this.tempBuildCaseID)\r\n            this.searchQuery.CBuildCaseSelected = this.userBuildCaseOptions[index]\r\n          } else {\r\n            this.searchQuery.CBuildCaseSelected = this.userBuildCaseOptions[0]\r\n          }\r\n        }\r\n      }),\r\n      tap(() => {\r\n        this.getListBuilding()\r\n        this.getListHouseHold()\r\n      }),\r\n      concatMap(() => this.getHouseList())\r\n    ).subscribe()\r\n  }\r\n\r\n  buildingSelected: any\r\n\r\n  buildingSelectedOptions: any[] = [\r\n    {\r\n      value: '', label: '全部'\r\n    }\r\n  ]\r\n\r\n  getListBuilding() {\r\n    this._houseService.apiHouseGetListBuildingPost$Json({\r\n      body: {\r\n        CBuildCaseID: this.searchQuery.CBuildCaseSelected.cID\r\n      }\r\n    }).subscribe(res => {\r\n      if (res.Entries && res.StatusCode == 0) {\r\n        this.buildingSelectedOptions = [{\r\n          value: '', label: '全部'\r\n        }, ...res.Entries.map(e => {\r\n          return { value: e, label: e }\r\n        })]\r\n        this.searchQuery.CBuildingNameSelected = this.buildingSelectedOptions[0]\r\n      }\r\n    })\r\n  }\r\n\r\n\r\n  houseDetail: TblHouse & {\r\n    changeStartDate?: any;\r\n    changeEndDate?: any\r\n  }\r\n\r\n  getHouseById(CID: any, ref: any) {\r\n    this.detailSelected = {}\r\n    this._houseService.apiHouseGetHouseByIdPost$Json({\r\n      body: { CHouseID: CID }\r\n    }).subscribe(res => {\r\n      if (res.Entries && res.StatusCode == 0) {\r\n        this.houseDetail = {\r\n          ...res.Entries,\r\n          changeStartDate: res.Entries.CChangeStartDate ? new Date(res.Entries.CChangeStartDate) : undefined,\r\n          changeEndDate: res.Entries.CChangeEndDate ? new Date(res.Entries.CChangeEndDate) : undefined,\r\n        }\r\n\r\n        if (res.Entries.CBuildCaseId) {\r\n          this.detailSelected.CBuildCaseSelected = this.findItemInArray(this.userBuildCaseOptions, 'cID', res.Entries.CBuildCaseId)\r\n\r\n        }\r\n        this.detailSelected.CPayStatusSelected = this.findItemInArray(this.options.payStatusOptions, 'value', res.Entries.CPayStatus)\r\n        if (res.Entries.CHouseType) {\r\n          this.detailSelected.CHouseTypeSelected = this.findItemInArray(this.options.houseTypeOptions, 'value', res.Entries.CHouseType)\r\n        } else {\r\n          this.detailSelected.CHouseTypeSelected = this.options.houseTypeOptions[1]\r\n        }\r\n        if (res.Entries.CProgress) {\r\n          this.detailSelected.CProgressSelected = this.findItemInArray(this.options.progressOptions, 'value', res.Entries.CProgress)\r\n        }\r\n\r\n        if (res.Entries.CBuildCaseId) {\r\n          if (this.houseHoldMain) {\r\n            this.houseHoldMain.CBuildCaseID = res.Entries.CBuildCaseId\r\n          }\r\n        }\r\n        this.dialogService.open(ref)\r\n      }\r\n\r\n    })\r\n  }\r\n\r\n\r\n  findItemInArray(array: any[], key: string, value: any) {\r\n    return array.find(item => item[key] === value);\r\n  }\r\n\r\n\r\n  openModelDetail(ref: any, item: any) {\r\n    this.getHouseById(item.CID, ref)\r\n  }\r\n\r\n  openModel(ref: any) {\r\n    this.houseHoldMain = {\r\n      CBuildingName: '',\r\n      CFloor: undefined,\r\n      CHouseHoldCount: undefined\r\n    }\r\n    this.dialogService.open(ref)\r\n  }\r\n\r\n  editHouseArgsParam: EditHouseArgs\r\n\r\n\r\n  formatDate(CChangeDate: string): string {\r\n    if (CChangeDate) {\r\n      return moment(CChangeDate).format('YYYY-MM-DDTHH:mm:ss')\r\n    }\r\n    return ''\r\n  }\r\n\r\n  onSubmitDetail(ref: any) {\r\n    this.houseDetail.CChangeStartDate = this.houseDetail.changeStartDate ? this.formatDate(this.houseDetail.changeStartDate) : '',\r\n      this.houseDetail.CChangeEndDate = this.houseDetail.changeEndDate ? this.formatDate(this.houseDetail.changeEndDate) : '',\r\n\r\n      this.editHouseArgsParam = {\r\n        CCustomerName: this.houseDetail.CCustomerName,\r\n        CHouseHold: this.houseDetail.CHousehold,\r\n        CHouseID: this.houseDetail.CId,\r\n        CHouseType: this.detailSelected.CHouseTypeSelected ? this.detailSelected.CHouseTypeSelected.value : null,\r\n        CIsChange: this.houseDetail.CIsChange,\r\n        CIsEnable: this.houseDetail.CIsEnable,\r\n        CMail: this.houseDetail.CMail,\r\n        CNationalID: this.houseDetail.CNationalId,\r\n        CPayStatus: this.detailSelected.CPayStatusSelected ? this.detailSelected.CPayStatusSelected.value : null,\r\n        CPhone: this.houseDetail.CPhone,\r\n        CProgress: this.detailSelected.CProgressSelected ? this.detailSelected.CProgressSelected.value : null,\r\n        CChangeStartDate: this.houseDetail.CChangeStartDate,\r\n        CChangeEndDate: this.houseDetail.CChangeEndDate\r\n      }\r\n    this.validation()\r\n    if (this.valid.errorMessages.length > 0) {\r\n      this.message.showErrorMSGs(this.valid.errorMessages);\r\n      return\r\n    }\r\n\r\n    this._houseService.apiHouseEditHousePost$Json({\r\n      body: this.editHouseArgsParam\r\n    }).pipe(\r\n      tap(res => {\r\n        if (res.StatusCode === 0) {\r\n          this.message.showSucessMSG(\"執行成功\");\r\n          ref.close();\r\n        } else {\r\n          this.message.showErrorMSG(res.Message!);\r\n          ref.close();\r\n        }\r\n      }),\r\n      concatMap(() => this.getHouseList())\r\n    ).subscribe();\r\n  }\r\n\r\n\r\n  onSubmit(ref: any) {\r\n    let bodyReq: EditHouseArgs = {\r\n      CCustomerName: this.houseDetail.CCustomerName,\r\n      CHouseHold: this.houseDetail.CHousehold,\r\n      CHouseID: this.houseDetail.CId,\r\n      CHouseType: this.houseDetail.CHouseType,\r\n      CIsChange: this.houseDetail.CIsChange,\r\n      CIsEnable: this.houseDetail.CIsEnable,\r\n      CMail: this.houseDetail.CMail,\r\n      CNationalID: this.houseDetail.CNationalId,\r\n      CPayStatus: this.houseDetail.CPayStatus,\r\n      CPhone: this.houseDetail.CPhone,\r\n      CProgress: this.houseDetail.CProgress,\r\n    }\r\n    this._houseService.apiHouseEditHousePost$Json({\r\n      body: bodyReq\r\n    }).subscribe(res => {\r\n      if (res.StatusCode === 0) {\r\n        this.message.showSucessMSG(\"執行成功\");\r\n        ref.close();\r\n      }\r\n    })\r\n\r\n  }\r\n\r\n  onClose(ref: any) {\r\n    ref.close();\r\n  }\r\n\r\n  onNavidateId(type: any, id?: any) {\r\n    const idURL = id ? id : this.searchQuery.CBuildCaseSelected.cID\r\n    this.router.navigate([`/pages/household-management/${type}`, idURL])\r\n  }\r\n\r\n  onNavidateBuildCaseIdHouseId(type: any, buildCaseId: any, houseId: any) {\r\n    this.router.navigate([`/pages/household-management/${type}`, buildCaseId, houseId])\r\n  }\r\n\r\n  houseHoldMain: AddHouseHoldMain\r\n\r\n  validation() {\r\n    this.valid.clear();\r\n    this.valid.required('[建案名稱]', this.houseDetail.CId)\r\n    this.valid.required('[戶型名稱]', this.editHouseArgsParam.CHouseHold)\r\n    this.valid.isStringMaxLength('[棟別]', this.editHouseArgsParam.CHouseHold, 50)\r\n    this.valid.required('[樓層]', this.houseDetail.CFloor)\r\n    this.valid.isStringMaxLength('[客戶姓名]', this.editHouseArgsParam.CCustomerName, 50)\r\n    if (this.editHouseArgsParam.CNationalID) {\r\n      this.valid.CheckTaiwanID(this.editHouseArgsParam.CNationalID)\r\n    }\r\n    this.valid.pattern('[電子郵件]', this.editHouseArgsParam.CMail, this.pettern.MailPettern)\r\n    this.valid.isPhoneNumber('[聯絡電話]', this.editHouseArgsParam.CPhone)\r\n    this.valid.required('[進度]', this.editHouseArgsParam.CProgress)\r\n    this.valid.required('[戶別類型]', this.detailSelected.CHouseTypeSelected.value)\r\n    this.valid.required('[付款狀態]', this.detailSelected.CPayStatusSelected.value)\r\n    if (this.houseDetail.CChangeStartDate) {\r\n      this.valid.required('[客變結束日期]', this.houseDetail.CChangeEndDate)\r\n    }\r\n    if (this.houseDetail.CChangeEndDate) {\r\n      this.valid.required('[客變開始日期]', this.houseDetail.CChangeStartDate)\r\n    }\r\n    this.valid.checkStartBeforeEnd('[開放日期]', this.houseDetail.CChangeStartDate ? this.houseDetail.CChangeStartDate : '', this.houseDetail.CChangeEndDate ? this.houseDetail.CChangeEndDate : '')\r\n  }\r\n\r\n  validationHouseHoldMain() {\r\n    this.valid.clear();\r\n    this.valid.required('[建案]', this.houseHoldMain.CBuildCaseID)\r\n    this.valid.required('[棟別]', this.houseHoldMain.CBuildingName)\r\n    this.valid.isStringMaxLength('[棟別]', this.houseHoldMain.CBuildingName, 10)\r\n    this.valid.isNaturalNumberInRange('[當層最多戶數]', this.houseHoldMain.CFloor, 1, 100)\r\n    this.valid.isNaturalNumberInRange('[本棟總樓層]', this.houseHoldMain.CHouseHoldCount, 1, 100)\r\n  }\r\n\r\n\r\n  addHouseHoldMain(ref: any) {\r\n    this.houseHoldMain.CBuildCaseID = this.searchQuery.CBuildCaseSelected.cID,\r\n      this.validationHouseHoldMain()\r\n    if (this.valid.errorMessages.length > 0) {\r\n      this.message.showErrorMSGs(this.valid.errorMessages);\r\n      return\r\n    }\r\n    this._houseHoldMainService.apiHouseHoldMainAddHouseHoldMainPost$Json({\r\n      body: this.houseHoldMain\r\n    }).pipe(\r\n      tap(res => {\r\n        if (res.StatusCode === 0) {\r\n          this.message.showSucessMSG(\"執行成功\");\r\n          ref.close();\r\n        }\r\n      }),\r\n      concatMap(() => this.getHouseList())\r\n    ).subscribe();\r\n  }\r\n}\r\n", "<nb-card accent=\"success\">\r\n  <nb-card-header>\r\n    <ngx-breadcrumb></ngx-breadcrumb>\r\n  </nb-card-header>\r\n  <nb-card-body>\r\n    <h1 class=\"font-bold text-[#818181]\">您可與此管理各戶別內之相關資訊，包含基本資料、繳款狀況、上傳客變圖面、檢視客變結果等等。 </h1>\r\n    <div class=\"d-flex flex-wrap\">\r\n      <div class=\"col-md-6\">\r\n        <div class=\"form-group d-flex align-items-center w-full\">\r\n          <label for=\"buildingName\" class=\"label col-3\">建案</label>\r\n          <nb-select placeholder=\"建案\" [(ngModel)]=\"searchQuery.CBuildCaseSelected\" class=\"col-9\"\r\n            (selectedChange)=\"onSelectionChangeBuildCase()\">\r\n            <nb-option *ngFor=\"let case of userBuildCaseOptions\" [value]=\"case\">\r\n              {{ case.CBuildCaseName }}\r\n            </nb-option>\r\n          </nb-select>\r\n        </div>\r\n      </div>\r\n      <div class=\"col-md-6\">\r\n        <div class=\"form-group d-flex align-items-center w-full\">\r\n          <label for=\"cHouseType\" class=\"label col-3\">類型</label>\r\n          <nb-select [(ngModel)]=\"searchQuery.CHouseTypeSelected\" class=\"col-9\">\r\n            <nb-option *ngFor=\"let case of houseTypeOptions\" [value]=\"case\">\r\n              {{ case.label }}\r\n            </nb-option>\r\n          </nb-select>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"col-md-6\">\r\n        <div class=\"form-group d-flex align-items-center w-full\">\r\n          <label for=\"buildingName\" class=\"label col-3\">\r\n            棟別\r\n          </label>\r\n          <nb-select [(ngModel)]=\"searchQuery.CBuildingNameSelected\" class=\"col-9\">\r\n            <nb-option *ngFor=\"let case of buildingSelectedOptions\" [value]=\"case\">\r\n              {{ case.label }}\r\n            </nb-option>\r\n          </nb-select>\r\n        </div>\r\n      </div>\r\n      <div class=\"col-md-6\">\r\n        <div class=\"form-group d-flex align-items-center\">\r\n          <label for=\"cFloorFrom\" class=\"label col-3\">樓\r\n          </label>\r\n          <nb-form-field class=\"ml-3\">\r\n            <input type=\"text\" id=\"CFrom\" nbInput class=\"w-full col-4\" [(ngModel)]=\"searchQuery.CFrom\">\r\n          </nb-form-field>\r\n          <label for=\"cFloorTo\" class=\"label col-1\">~\r\n          </label>\r\n          <nb-form-field class=\"mr-3\">\r\n            <input type=\"text\" id=\"CTo\" nbInput class=\"w-full\" [(ngModel)]=\"searchQuery.CTo\">\r\n          </nb-form-field>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"col-md-6\">\r\n        <div class=\"form-group d-flex align-items-center w-full\">\r\n          <label for=\"cHousehold\" class=\"label col-3\">\r\n            戶型\r\n          </label>\r\n          <nb-select placeholder=\"戶型\" [(ngModel)]=\"searchQuery.CHouseHoldSelected\" class=\"col-9\">\r\n            <nb-option *ngFor=\"let case of houseHoldOptions\" [value]=\"case\">\r\n              {{ case.label }}\r\n            </nb-option>\r\n          </nb-select>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"col-md-6\">\r\n        <div class=\"form-group d-flex align-items-center w-full\">\r\n          <label for=\"cPayStatus\" class=\"label col-3\">\r\n            繳款狀態\r\n          </label>\r\n          <nb-select placeholder=\"繳款狀態\" [(ngModel)]=\"searchQuery.CPayStatusSelected\" class=\"col-9\">\r\n            <nb-option *ngFor=\"let case of payStatusOptions\" [value]=\"case\">\r\n              {{ case.label }}\r\n            </nb-option>\r\n          </nb-select>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"col-md-6\">\r\n        <div class=\"form-group d-flex align-items-center w-full\">\r\n          <label for=\"cProgress\" class=\"label col-3\">\r\n            進度\r\n          </label>\r\n          <nb-select placeholder=\"進度\" [(ngModel)]=\"searchQuery.CProgressSelected\" class=\"col-9\">\r\n            <nb-option *ngFor=\"let case of progressOptions\" [value]=\"case\">\r\n              {{ case.label }}\r\n            </nb-option>\r\n          </nb-select>\r\n        </div>\r\n      </div>\r\n      <div class=\"col-md-6\">\r\n        <div class=\"form-group d-flex align-items-center w-full\">\r\n          <label for=\"cStatus\" class=\"label col-3\">\r\n            狀態\r\n          </label>\r\n          <nb-select placeholder=\"狀態\" [(ngModel)]=\"searchQuery.CIsEnableSeleted\" class=\"col-9\">\r\n            <nb-option *ngFor=\"let case of cIsEnableOptions\" [value]=\"case\">\r\n              {{ case.label }}\r\n            </nb-option>\r\n          </nb-select>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"col-md-6\">\r\n        <div class=\"form-group d-flex align-items-center w-full\">\r\n          <label for=\"cSignStatus\" class=\"label col-3\">\r\n            簽回狀態\r\n          </label>\r\n          <nb-select placeholder=\"簽回狀態\" [(ngModel)]=\"searchQuery.CSignStatusSelected\" class=\"col-9\">\r\n            <nb-option *ngFor=\"let case of signStatusOptions\" [value]=\"case\">\r\n              {{ case.label }}\r\n            </nb-option>\r\n          </nb-select>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"col-md-6\">\r\n        <div class=\"d-flex justify-content-end w-full\">\r\n\r\n          <button class=\"btn btn-secondary btn-sm\" (click)=\"onSearch()\">\r\n            查詢 <i class=\"fas fa-search\"></i>\r\n          </button>\r\n        </div>\r\n      </div>\r\n      <div class=\"col-md-12\">\r\n        <div class=\"d-flex justify-content-end w-full\">\r\n          <button class=\"btn btn-info mx-1 btn-sm\" *ngIf=\"isCreate\" (click)=\"openModel(dialogHouseholdMain)\">\r\n            批次新增戶別資料\r\n          </button>\r\n          <button class=\"btn btn-info mx-1 btn-sm\" (click)=\"onNavidateId('modify-floor-plan')\">\r\n            修改樓層戶型\r\n          </button>\r\n          <!-- <button class=\"btn btn-info mx-1 btn-sm\" (click)=\"onNavidateId('standard-house-plan')\">\r\n            3.設定戶型標準圖\r\n          </button> -->\r\n          <button class=\"btn btn-info mx-1 btn-sm\" (click)=\"exportHouse()\">\r\n            匯出戶別明細檔\r\n          </button>\r\n          <input type=\"file\" #fileInput style=\"display:none\" (change)=\"onFileSelected($event)\" accept=\".xlsx, .xls\" />\r\n          <button class=\"btn btn-info btn-sm\" (click)=\"triggerFileInput()\">\r\n            匯入戶別明細檔\r\n          </button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <div class=\"table-responsive mt-4\">\r\n      <table class=\"table table-striped border \" style=\"min-width: 1000px; background-color:#f3f3f3;\">\r\n        <thead>\r\n          <tr style=\"background-color: #27ae60; color: white;\">\r\n            <th scope=\"col\" class=\"col-1 \">棟別</th>\r\n            <th scope=\"col\" class=\"col-1\">戶型</th>\r\n            <th scope=\"col\" class=\"col-1\">樓層</th>\r\n            <th scope=\"col\" class=\"col-1\">地主戶</th>\r\n            <th scope=\"col\" class=\"col-1\">類型</th>\r\n            <th scope=\"col\" class=\"col-1\">進度</th>\r\n            <th scope=\"col\" class=\"col-1\">繳款狀態</th>\r\n            <th scope=\"col\" class=\"col-1\">簽回狀態</th>\r\n            <th scope=\"col\" class=\"col-1\">狀態</th>\r\n            <th scope=\"col\" class=\"col-4\">操作</th>\r\n          </tr>\r\n        </thead>\r\n        <tbody>\r\n          <tr *ngFor=\"let item of houseList ; let i = index\">\r\n            <td>{{ item.CBuildingName}}</td>\r\n            <td>{{ item.CHouseHold}}</td>\r\n            <td>{{ item.CFloor}}</td>\r\n            <td>\r\n              {{ item.CHouseType === 2 ? '銷售戶' : ''}}\r\n              {{item.CHouseType === 1 ? '地主戶' : ''}}\r\n            </td>\r\n            <td>{{ item.CIsChange === true ? '客變' : (item.CIsChange === false ? '標準' :'') }}</td>\r\n            <td>{{ item.CProgress}}</td>\r\n            <td>\r\n              {{item.CPayStatus === 0 ? '未付款': ''}}\r\n              {{item.CPayStatus === 1 ? '已付款': ''}}\r\n              {{item.CPayStatus === 2 ? '無須付款': ''}}\r\n            </td>\r\n            <td>{{ (item.CSignStatus === 0 || item.CSignStatus == null) ? '未簽回' : '已簽回' }}</td>\r\n            <td>{{ item.CIsEnable ? '啟用' : '停用'}}</td>\r\n            <td class=\"text-center w-32 px-0\">\r\n              <button *ngIf=\"isUpdate\" class=\"btn btn-outline-success btn-sm text-left m-[2px]\"\r\n                (click)=\"openModelDetail(dialogUpdateHousehold, item)\">\r\n                編輯\r\n              </button>\r\n              <button class=\"btn btn-outline-success btn-sm m-[2px]\" *ngIf=\"item.CPayStatus!==2\"\r\n                (click)=\"onNavidateBuildCaseIdHouseId('customer-change-picture', searchQuery.CBuildCaseSelected.cID, item.CID )\">\r\n                客變圖上傳\r\n              </button>\r\n              <button class=\"btn btn-outline-success btn-sm m-[2px]\"\r\n                (click)=\"onNavidateBuildCaseIdHouseId('sample-selection-result', searchQuery.CBuildCaseSelected.cID, item.CID )\">選樣及客變結果\r\n              </button>\r\n            </td>\r\n          </tr>\r\n        </tbody>\r\n      </table>\r\n    </div>\r\n  </nb-card-body>\r\n  <nb-card-footer class=\"d-flex justify-content-center\">\r\n    <ngb-pagination [(page)]=\"pageIndex\" [pageSize]=\"pageSize\" [collectionSize]=\"totalRecords\"\r\n      (pageChange)=\"pageChanged($event)\" aria-label=\"Pagination\">\r\n    </ngb-pagination>\r\n  </nb-card-footer>\r\n</nb-card>\r\n\r\n<ng-template #dialogUpdateHousehold let-dialog let-ref=\"dialogRef\">\r\n  <nb-card style=\"width:500px; max-height: 95vh\">\r\n    <!-- <nb-card-header>\r\n    </nb-card-header> -->\r\n    <nb-card-body class=\"px-4\" *ngIf=\"houseDetail\">\r\n\r\n      <div class=\"form-group\">\r\n        <label for=\"cBuildCaseId\" class=\"required-field mr-4\" style=\"min-width:75px\" baseLabel>\r\n          建案名稱\r\n        </label>\r\n        <nb-select placeholder=\"建案名稱\" [(ngModel)]=\"detailSelected.CBuildCaseSelected\" class=\"w-full\" disabled=\"true\">\r\n          <nb-option *ngFor=\"let status of userBuildCaseOptions\" [value]=\"status\">\r\n            {{ status.CBuildCaseName }}\r\n          </nb-option>\r\n        </nb-select>\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label for=\"cHousehold\" class=\"required-field mr-4\" style=\"min-width:75px\" baseLabel>\r\n          戶型名稱\r\n        </label>\r\n        <input type=\"text\" class=\"w-full\" nbInput placeholder=\"戶型名稱\" [(ngModel)]=\"houseDetail.CHousehold\" />\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label for=\"cFloor\" class=\"required-field mr-4\" style=\"min-width:75px\" baseLabel>\r\n          樓層\r\n        </label>\r\n        <input type=\"number\" class=\"w-full\" nbInput placeholder=\"樓層\" [(ngModel)]=\"houseDetail.CFloor\" min=\"1\" max=\"100\"\r\n          disabled=\"true\" />\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label for=\"cCustomerName\" class=\"mr-4\" style=\"min-width:75px\" baseLabel>\r\n          客戶姓名\r\n        </label>\r\n        <input type=\"text\" class=\"w-full\" nbInput placeholder=\"客戶姓名\" [(ngModel)]=\"houseDetail.CCustomerName\" />\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label for=\"cNationalId\" class=\"mr-4\" style=\"min-width:75px\" baseLabel>\r\n          身分證字號\r\n        </label>\r\n        <input type=\"text\" class=\"w-full\" nbInput placeholder=\"身分證字號\" [(ngModel)]=\"houseDetail.CNationalId\" />\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label for=\"cMail\" class=\"mr-4\" style=\"min-width:75px\" baseLabel>\r\n          電子郵件\r\n        </label>\r\n        <input type=\"text\" class=\"w-full\" nbInput placeholder=\"電子郵件\" [(ngModel)]=\"houseDetail.CMail\" />\r\n      </div>\r\n      <div class=\"form-group\">\r\n        <label for=\"cPhone\" class=\"mr-4\" style=\"min-width:75px\" baseLabel>\r\n          聯絡電話\r\n        </label>\r\n        <input type=\"text\" class=\"w-full\" nbInput placeholder=\"聯絡電話\" [(ngModel)]=\"houseDetail.CPhone\" />\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label for=\"cHouseType\" class=\"required-field mr-4\" style=\"min-width:75px\" baseLabel>\r\n          戶別類型\r\n        </label>\r\n        <nb-select placeholder=\"戶別類型\" [(ngModel)]=\"detailSelected.CHouseTypeSelected\" class=\"w-full\">\r\n          <nb-option *ngFor=\"let status of options.houseTypeOptions\" [value]=\"status\">\r\n            {{ status.label }}\r\n          </nb-option>\r\n        </nb-select>\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label for=\"cPayStatus\" class=\"required-field mr-4\" style=\"min-width:75px\" baseLabel>\r\n          付款狀態\r\n        </label>\r\n        <nb-select placeholder=\"付款狀態\" [(ngModel)]=\"detailSelected.CPayStatusSelected\" class=\"w-full\">\r\n          <nb-option *ngFor=\"let status of options.payStatusOptions\" [value]=\"status\">\r\n            {{ status.label }}\r\n          </nb-option>\r\n        </nb-select>\r\n      </div>\r\n      <div class=\"form-group\">\r\n        <label for=\"cProgress\" class=\"required-field mr-4\" style=\"min-width:75px\" baseLabel>\r\n          進度\r\n        </label>\r\n        <nb-select placeholder=\"進度\" [(ngModel)]=\"detailSelected.CProgressSelected\" class=\"w-full\">\r\n          <nb-option *ngFor=\"let status of options.progressOptions\" [value]=\"status\">\r\n            {{ status.label }}\r\n          </nb-option>\r\n        </nb-select>\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label for=\"cIsChange\" class=\"mr-4\" style=\"min-width:75px\" baseLabel>\r\n          是否客變\r\n        </label>\r\n        <nb-checkbox status=\"basic\" [(checked)]=\"houseDetail.CIsChange\">是\r\n        </nb-checkbox>\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label for=\"cIsEnable\" class=\"mr-4\" style=\"min-width:75px\" baseLabel>\r\n          是否啟用\r\n        </label>\r\n        <nb-checkbox status=\"basic\" [(checked)]=\"houseDetail.CIsEnable\">是\r\n        </nb-checkbox>\r\n      </div>\r\n\r\n      <div class=\"form-group flex flex-row\">\r\n        <label for=\"cIsEnable\" class=\"mr-4 content-center\" style=\"min-width:75px\" baseLabel>\r\n          客變時段\r\n        </label>\r\n        <div class=\"max-w-xs flex flex-row\">\r\n          <nb-form-field class=\"w-1/2\">\r\n            <nb-icon nbPrefix icon=\"calendar-outline\"></nb-icon>\r\n            <input nbInput type=\"text\" id=\"StartDate\" placeholder=\"yyyy-mm-dd\" [nbDatepicker]=\"StartDate\"\r\n              class=\"w-[42%] mr-2\" [(ngModel)]=\"houseDetail.changeStartDate\">\r\n            <nb-datepicker #StartDate format=\"yyyy-MM-dd\"></nb-datepicker>\r\n          </nb-form-field>\r\n          <nb-form-field class=\"w-1/2\">\r\n            <nb-icon nbPrefix icon=\"calendar-outline\"></nb-icon>\r\n            <input nbInput type=\"text\" id=\"EndDate\" placeholder=\"yyyy-mm-dd\" [nbDatepicker]=\"EndDate\"\r\n              class=\"w-[42%] ml-2\" [(ngModel)]=\"houseDetail.changeEndDate\">\r\n            <nb-datepicker #EndDate format=\"yyyy-MM-dd\"></nb-datepicker>\r\n          </nb-form-field>\r\n        </div>\r\n\r\n      </div>\r\n    </nb-card-body>\r\n    <nb-card-footer class=\"d-flex justify-content-center\">\r\n      <button class=\"btn btn-outline-secondary m-2 px-8\" (click)=\"onClose(ref)\">取消</button>\r\n      <button class=\"btn btn-primary m-2 bg-[#169BD5] px-8\" *ngIf=\"isCreate\" (click)=\"onSubmitDetail(ref)\">送出</button>\r\n    </nb-card-footer>\r\n  </nb-card>\r\n</ng-template>\r\n\r\n<ng-template #dialogHouseholdMain let-dialog let-ref=\"dialogRef\">\r\n  <nb-card style=\"width:500px; max-height: 95vh\">\r\n    <nb-card-header>\r\n      戶別管理 》批次新增戶別資料\r\n    </nb-card-header>\r\n    <nb-card-body class=\"px-4\">\r\n      <div class=\"form-group\">\r\n        <label for=\"cBuildingName\" class=\"required-field mr-4\" style=\"min-width:75px\" baseLabel>\r\n          棟別\r\n        </label>\r\n        <input type=\"text\" class=\"w-full\" nbInput placeholder=\"棟別\" [(ngModel)]=\"houseHoldMain.CBuildingName\" />\r\n      </div>\r\n      <div class=\"form-group\">\r\n        <label for=\"CHouseHoldCount\" class=\"required-field mr-4\" style=\"min-width:75px\" baseLabel>當層最多戶數\r\n        </label>\r\n        <input type=\"number\" class=\"w-full\" nbInput placeholder=\"當層最多戶數\" [(ngModel)]=\"houseHoldMain.CHouseHoldCount\" />\r\n      </div>\r\n      <div class=\"form-group\">\r\n        <label for=\"CFloor\" class=\"required-field mr-4\" style=\"min-width:75px\" baseLabel>本棟總樓層\r\n        </label>\r\n        <input type=\"number\" class=\"w-full\" nbInput placeholder=\"本棟總樓層\" [(ngModel)]=\"houseHoldMain.CFloor\" />\r\n      </div>\r\n    </nb-card-body>\r\n    <nb-card-footer class=\"d-flex justify-content-center\">\r\n      <button class=\"btn btn-primary mr-4\" (click)=\"onClose(ref)\">{{ '關閉'}}</button>\r\n      <button class=\"btn btn-primary\" *ngIf=\"isCreate\" (click)=\"addHouseHoldMain(ref)\">儲存</button>\r\n    </nb-card-footer>\r\n  </nb-card>\r\n</ng-template>"], "mappings": "AACA,SAASA,YAAY,QAAQ,6BAA6B;AAC1D,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,kBAAkB,QAAyB,gBAAgB;AAOpE,SAASC,aAAa,QAAQ,kCAAkC;AAGhE,SAASC,SAAS,EAAEC,GAAG,QAAQ,MAAM;AACrC,SAASC,mBAAmB,QAAQ,mBAAmB;AACvD,OAAO,KAAKC,MAAM,MAAM,QAAQ;AAChC,SAASC,MAAM,QAA8B,uCAAuC;;;;;;;;;;;;;;;;;;;ICJxEC,EAAA,CAAAC,cAAA,oBAAoE;IAClED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAFyCH,EAAA,CAAAI,UAAA,UAAAC,OAAA,CAAc;IACjEL,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAAO,kBAAA,MAAAF,OAAA,CAAAG,cAAA,MACF;;;;;IAQAR,EAAA,CAAAC,cAAA,oBAAgE;IAC9DD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAFqCH,EAAA,CAAAI,UAAA,UAAAK,OAAA,CAAc;IAC7DT,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAAO,kBAAA,MAAAE,OAAA,CAAAC,KAAA,MACF;;;;;IAWAV,EAAA,CAAAC,cAAA,oBAAuE;IACrED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAF4CH,EAAA,CAAAI,UAAA,UAAAO,OAAA,CAAc;IACpEX,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAAO,kBAAA,MAAAI,OAAA,CAAAD,KAAA,MACF;;;;;IAyBAV,EAAA,CAAAC,cAAA,oBAAgE;IAC9DD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAFqCH,EAAA,CAAAI,UAAA,UAAAQ,OAAA,CAAc;IAC7DZ,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAAO,kBAAA,MAAAK,OAAA,CAAAF,KAAA,MACF;;;;;IAWAV,EAAA,CAAAC,cAAA,oBAAgE;IAC9DD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAFqCH,EAAA,CAAAI,UAAA,UAAAS,OAAA,CAAc;IAC7Db,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAAO,kBAAA,MAAAM,OAAA,CAAAH,KAAA,MACF;;;;;IAWAV,EAAA,CAAAC,cAAA,oBAA+D;IAC7DD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAFoCH,EAAA,CAAAI,UAAA,UAAAU,OAAA,CAAc;IAC5Dd,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAAO,kBAAA,MAAAO,OAAA,CAAAJ,KAAA,MACF;;;;;IAUAV,EAAA,CAAAC,cAAA,oBAAgE;IAC9DD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAFqCH,EAAA,CAAAI,UAAA,UAAAW,OAAA,CAAc;IAC7Df,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAAO,kBAAA,MAAAQ,OAAA,CAAAL,KAAA,MACF;;;;;IAWAV,EAAA,CAAAC,cAAA,oBAAiE;IAC/DD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAFsCH,EAAA,CAAAI,UAAA,UAAAY,OAAA,CAAc;IAC9DhB,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAAO,kBAAA,MAAAS,OAAA,CAAAN,KAAA,MACF;;;;;;IAeFV,EAAA,CAAAC,cAAA,iBAAmG;IAAzCD,EAAA,CAAAiB,UAAA,mBAAAC,wEAAA;MAAAlB,EAAA,CAAAmB,aAAA,CAAAC,IAAA;MAAA,MAAAC,OAAA,GAAArB,EAAA,CAAAsB,aAAA;MAAA,MAAAC,uBAAA,GAAAvB,EAAA,CAAAwB,WAAA;MAAA,OAAAxB,EAAA,CAAAyB,WAAA,CAASJ,OAAA,CAAAK,SAAA,CAAAH,uBAAA,CAA8B;IAAA,EAAC;IAChGvB,EAAA,CAAAE,MAAA,yDACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;IAqDLH,EAAA,CAAAC,cAAA,iBACyD;IAAvDD,EAAA,CAAAiB,UAAA,mBAAAU,+EAAA;MAAA3B,EAAA,CAAAmB,aAAA,CAAAS,IAAA;MAAA,MAAAC,QAAA,GAAA7B,EAAA,CAAAsB,aAAA,GAAAQ,SAAA;MAAA,MAAAT,OAAA,GAAArB,EAAA,CAAAsB,aAAA;MAAA,MAAAS,yBAAA,GAAA/B,EAAA,CAAAwB,WAAA;MAAA,OAAAxB,EAAA,CAAAyB,WAAA,CAASJ,OAAA,CAAAW,eAAA,CAAAD,yBAAA,EAAAF,QAAA,CAA4C;IAAA,EAAC;IACtD7B,EAAA,CAAAE,MAAA,qBACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;IACTH,EAAA,CAAAC,cAAA,iBACmH;IAAjHD,EAAA,CAAAiB,UAAA,mBAAAgB,+EAAA;MAAAjC,EAAA,CAAAmB,aAAA,CAAAe,IAAA;MAAA,MAAAL,QAAA,GAAA7B,EAAA,CAAAsB,aAAA,GAAAQ,SAAA;MAAA,MAAAT,OAAA,GAAArB,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAyB,WAAA,CAASJ,OAAA,CAAAc,4BAAA,CAA6B,yBAAyB,EAAAd,OAAA,CAAAe,WAAA,CAAAC,kBAAA,CAAAC,GAAA,EAAAT,QAAA,CAAAU,GAAA,CAAgD;IAAA,EAAC;IAChHvC,EAAA,CAAAE,MAAA,uCACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;IAxBXH,EADF,CAAAC,cAAA,SAAmD,SAC7C;IAAAD,EAAA,CAAAE,MAAA,GAAuB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAChCH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,GAAoB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC7BH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,GAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACzBH,EAAA,CAAAC,cAAA,SAAI;IACFD,EAAA,CAAAE,MAAA,GAEF;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,IAA4E;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACrFH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAE,MAAA,IAAmB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC5BH,EAAA,CAAAC,cAAA,UAAI;IACFD,EAAA,CAAAE,MAAA,IAGF;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAE,MAAA,IAA0E;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACnFH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAE,MAAA,IAAiC;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC1CH,EAAA,CAAAC,cAAA,cAAkC;IAKhCD,EAJA,CAAAwC,UAAA,KAAAC,sDAAA,qBACyD,KAAAC,sDAAA,qBAI0D;IAGnH1C,EAAA,CAAAC,cAAA,kBACmH;IAAjHD,EAAA,CAAAiB,UAAA,mBAAA0B,sEAAA;MAAA,MAAAd,QAAA,GAAA7B,EAAA,CAAAmB,aAAA,CAAAyB,IAAA,EAAAd,SAAA;MAAA,MAAAT,OAAA,GAAArB,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAyB,WAAA,CAASJ,OAAA,CAAAc,4BAAA,CAA6B,yBAAyB,EAAAd,OAAA,CAAAe,WAAA,CAAAC,kBAAA,CAAAC,GAAA,EAAAT,QAAA,CAAAU,GAAA,CAAgD;IAAA,EAAC;IAACvC,EAAA,CAAAE,MAAA,mDACnH;IAEJF,EAFI,CAAAG,YAAA,EAAS,EACN,EACF;;;;;IA7BCH,EAAA,CAAAM,SAAA,GAAuB;IAAvBN,EAAA,CAAA6C,iBAAA,CAAAhB,QAAA,CAAAiB,aAAA,CAAuB;IACvB9C,EAAA,CAAAM,SAAA,GAAoB;IAApBN,EAAA,CAAA6C,iBAAA,CAAAhB,QAAA,CAAAkB,UAAA,CAAoB;IACpB/C,EAAA,CAAAM,SAAA,GAAgB;IAAhBN,EAAA,CAAA6C,iBAAA,CAAAhB,QAAA,CAAAmB,MAAA,CAAgB;IAElBhD,EAAA,CAAAM,SAAA,GAEF;IAFEN,EAAA,CAAAiD,kBAAA,MAAApB,QAAA,CAAAqB,UAAA,yCAAArB,QAAA,CAAAqB,UAAA,wCAEF;IACIlD,EAAA,CAAAM,SAAA,GAA4E;IAA5EN,EAAA,CAAA6C,iBAAA,CAAAhB,QAAA,CAAAsB,SAAA,6BAAAtB,QAAA,CAAAsB,SAAA,iCAA4E;IAC5EnD,EAAA,CAAAM,SAAA,GAAmB;IAAnBN,EAAA,CAAA6C,iBAAA,CAAAhB,QAAA,CAAAuB,SAAA,CAAmB;IAErBpD,EAAA,CAAAM,SAAA,GAGF;IAHEN,EAAA,CAAAqD,kBAAA,MAAAxB,QAAA,CAAAyB,UAAA,yCAAAzB,QAAA,CAAAyB,UAAA,yCAAAzB,QAAA,CAAAyB,UAAA,8CAGF;IACItD,EAAA,CAAAM,SAAA,GAA0E;IAA1EN,EAAA,CAAA6C,iBAAA,CAAAhB,QAAA,CAAA0B,WAAA,UAAA1B,QAAA,CAAA0B,WAAA,uDAA0E;IAC1EvD,EAAA,CAAAM,SAAA,GAAiC;IAAjCN,EAAA,CAAA6C,iBAAA,CAAAhB,QAAA,CAAA2B,SAAA,mCAAiC;IAE1BxD,EAAA,CAAAM,SAAA,GAAc;IAAdN,EAAA,CAAAI,UAAA,SAAAiB,OAAA,CAAAoC,QAAA,CAAc;IAIiCzD,EAAA,CAAAM,SAAA,EAAyB;IAAzBN,EAAA,CAAAI,UAAA,SAAAyB,QAAA,CAAAyB,UAAA,OAAyB;;;;;IA+BrFtD,EAAA,CAAAC,cAAA,oBAAwE;IACtED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAF2CH,EAAA,CAAAI,UAAA,UAAAsD,UAAA,CAAgB;IACrE1D,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAAO,kBAAA,MAAAmD,UAAA,CAAAlD,cAAA,MACF;;;;;IAmDAR,EAAA,CAAAC,cAAA,oBAA4E;IAC1ED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAF+CH,EAAA,CAAAI,UAAA,UAAAuD,UAAA,CAAgB;IACzE3D,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAAO,kBAAA,MAAAoD,UAAA,CAAAjD,KAAA,MACF;;;;;IASAV,EAAA,CAAAC,cAAA,oBAA4E;IAC1ED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAF+CH,EAAA,CAAAI,UAAA,UAAAwD,UAAA,CAAgB;IACzE5D,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAAO,kBAAA,MAAAqD,UAAA,CAAAlD,KAAA,MACF;;;;;IAQAV,EAAA,CAAAC,cAAA,oBAA2E;IACzED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAF8CH,EAAA,CAAAI,UAAA,UAAAyD,UAAA,CAAgB;IACxE7D,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAAO,kBAAA,MAAAsD,UAAA,CAAAnD,KAAA,MACF;;;;;;IAhFFV,EAHJ,CAAAC,cAAA,uBAA+C,cAErB,gBACiE;IACrFD,EAAA,CAAAE,MAAA,iCACF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,oBAA6G;IAA/ED,EAAA,CAAA8D,gBAAA,2BAAAC,wGAAAC,MAAA;MAAAhE,EAAA,CAAAmB,aAAA,CAAA8C,IAAA;MAAA,MAAA5C,OAAA,GAAArB,EAAA,CAAAsB,aAAA;MAAAtB,EAAA,CAAAkE,kBAAA,CAAA7C,OAAA,CAAA8C,cAAA,CAAA9B,kBAAA,EAAA2B,MAAA,MAAA3C,OAAA,CAAA8C,cAAA,CAAA9B,kBAAA,GAAA2B,MAAA;MAAA,OAAAhE,EAAA,CAAAyB,WAAA,CAAAuC,MAAA;IAAA,EAA+C;IAC3EhE,EAAA,CAAAwC,UAAA,IAAA4B,gFAAA,wBAAwE;IAI5EpE,EADE,CAAAG,YAAA,EAAY,EACR;IAGJH,EADF,CAAAC,cAAA,cAAwB,gBAC+D;IACnFD,EAAA,CAAAE,MAAA,iCACF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,gBAAoG;IAAvCD,EAAA,CAAA8D,gBAAA,2BAAAO,oGAAAL,MAAA;MAAAhE,EAAA,CAAAmB,aAAA,CAAA8C,IAAA;MAAA,MAAA5C,OAAA,GAAArB,EAAA,CAAAsB,aAAA;MAAAtB,EAAA,CAAAkE,kBAAA,CAAA7C,OAAA,CAAAiD,WAAA,CAAAC,UAAA,EAAAP,MAAA,MAAA3C,OAAA,CAAAiD,WAAA,CAAAC,UAAA,GAAAP,MAAA;MAAA,OAAAhE,EAAA,CAAAyB,WAAA,CAAAuC,MAAA;IAAA,EAAoC;IACnGhE,EADE,CAAAG,YAAA,EAAoG,EAChG;IAGJH,EADF,CAAAC,cAAA,eAAwB,iBAC2D;IAC/ED,EAAA,CAAAE,MAAA,sBACF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,iBACoB;IADyCD,EAAA,CAAA8D,gBAAA,2BAAAU,qGAAAR,MAAA;MAAAhE,EAAA,CAAAmB,aAAA,CAAA8C,IAAA;MAAA,MAAA5C,OAAA,GAAArB,EAAA,CAAAsB,aAAA;MAAAtB,EAAA,CAAAkE,kBAAA,CAAA7C,OAAA,CAAAiD,WAAA,CAAAtB,MAAA,EAAAgB,MAAA,MAAA3C,OAAA,CAAAiD,WAAA,CAAAtB,MAAA,GAAAgB,MAAA;MAAA,OAAAhE,EAAA,CAAAyB,WAAA,CAAAuC,MAAA;IAAA,EAAgC;IAE/FhE,EAFE,CAAAG,YAAA,EACoB,EAChB;IAGJH,EADF,CAAAC,cAAA,eAAwB,iBACmD;IACvED,EAAA,CAAAE,MAAA,kCACF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,iBAAuG;IAA1CD,EAAA,CAAA8D,gBAAA,2BAAAW,qGAAAT,MAAA;MAAAhE,EAAA,CAAAmB,aAAA,CAAA8C,IAAA;MAAA,MAAA5C,OAAA,GAAArB,EAAA,CAAAsB,aAAA;MAAAtB,EAAA,CAAAkE,kBAAA,CAAA7C,OAAA,CAAAiD,WAAA,CAAAI,aAAA,EAAAV,MAAA,MAAA3C,OAAA,CAAAiD,WAAA,CAAAI,aAAA,GAAAV,MAAA;MAAA,OAAAhE,EAAA,CAAAyB,WAAA,CAAAuC,MAAA;IAAA,EAAuC;IACtGhE,EADE,CAAAG,YAAA,EAAuG,EACnG;IAGJH,EADF,CAAAC,cAAA,eAAwB,iBACiD;IACrED,EAAA,CAAAE,MAAA,wCACF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,iBAAsG;IAAxCD,EAAA,CAAA8D,gBAAA,2BAAAa,qGAAAX,MAAA;MAAAhE,EAAA,CAAAmB,aAAA,CAAA8C,IAAA;MAAA,MAAA5C,OAAA,GAAArB,EAAA,CAAAsB,aAAA;MAAAtB,EAAA,CAAAkE,kBAAA,CAAA7C,OAAA,CAAAiD,WAAA,CAAAM,WAAA,EAAAZ,MAAA,MAAA3C,OAAA,CAAAiD,WAAA,CAAAM,WAAA,GAAAZ,MAAA;MAAA,OAAAhE,EAAA,CAAAyB,WAAA,CAAAuC,MAAA;IAAA,EAAqC;IACrGhE,EADE,CAAAG,YAAA,EAAsG,EAClG;IAGJH,EADF,CAAAC,cAAA,eAAwB,iBAC2C;IAC/DD,EAAA,CAAAE,MAAA,kCACF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,iBAA+F;IAAlCD,EAAA,CAAA8D,gBAAA,2BAAAe,qGAAAb,MAAA;MAAAhE,EAAA,CAAAmB,aAAA,CAAA8C,IAAA;MAAA,MAAA5C,OAAA,GAAArB,EAAA,CAAAsB,aAAA;MAAAtB,EAAA,CAAAkE,kBAAA,CAAA7C,OAAA,CAAAiD,WAAA,CAAAQ,KAAA,EAAAd,MAAA,MAAA3C,OAAA,CAAAiD,WAAA,CAAAQ,KAAA,GAAAd,MAAA;MAAA,OAAAhE,EAAA,CAAAyB,WAAA,CAAAuC,MAAA;IAAA,EAA+B;IAC9FhE,EADE,CAAAG,YAAA,EAA+F,EAC3F;IAEJH,EADF,CAAAC,cAAA,eAAwB,iBAC4C;IAChED,EAAA,CAAAE,MAAA,kCACF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,iBAAgG;IAAnCD,EAAA,CAAA8D,gBAAA,2BAAAiB,qGAAAf,MAAA;MAAAhE,EAAA,CAAAmB,aAAA,CAAA8C,IAAA;MAAA,MAAA5C,OAAA,GAAArB,EAAA,CAAAsB,aAAA;MAAAtB,EAAA,CAAAkE,kBAAA,CAAA7C,OAAA,CAAAiD,WAAA,CAAAU,MAAA,EAAAhB,MAAA,MAAA3C,OAAA,CAAAiD,WAAA,CAAAU,MAAA,GAAAhB,MAAA;MAAA,OAAAhE,EAAA,CAAAyB,WAAA,CAAAuC,MAAA;IAAA,EAAgC;IAC/FhE,EADE,CAAAG,YAAA,EAAgG,EAC5F;IAGJH,EADF,CAAAC,cAAA,eAAwB,iBAC+D;IACnFD,EAAA,CAAAE,MAAA,kCACF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,qBAA6F;IAA/DD,EAAA,CAAA8D,gBAAA,2BAAAmB,yGAAAjB,MAAA;MAAAhE,EAAA,CAAAmB,aAAA,CAAA8C,IAAA;MAAA,MAAA5C,OAAA,GAAArB,EAAA,CAAAsB,aAAA;MAAAtB,EAAA,CAAAkE,kBAAA,CAAA7C,OAAA,CAAA8C,cAAA,CAAAe,kBAAA,EAAAlB,MAAA,MAAA3C,OAAA,CAAA8C,cAAA,CAAAe,kBAAA,GAAAlB,MAAA;MAAA,OAAAhE,EAAA,CAAAyB,WAAA,CAAAuC,MAAA;IAAA,EAA+C;IAC3EhE,EAAA,CAAAwC,UAAA,KAAA2C,iFAAA,wBAA4E;IAIhFnF,EADE,CAAAG,YAAA,EAAY,EACR;IAGJH,EADF,CAAAC,cAAA,eAAwB,iBAC+D;IACnFD,EAAA,CAAAE,MAAA,kCACF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,qBAA6F;IAA/DD,EAAA,CAAA8D,gBAAA,2BAAAsB,yGAAApB,MAAA;MAAAhE,EAAA,CAAAmB,aAAA,CAAA8C,IAAA;MAAA,MAAA5C,OAAA,GAAArB,EAAA,CAAAsB,aAAA;MAAAtB,EAAA,CAAAkE,kBAAA,CAAA7C,OAAA,CAAA8C,cAAA,CAAAkB,kBAAA,EAAArB,MAAA,MAAA3C,OAAA,CAAA8C,cAAA,CAAAkB,kBAAA,GAAArB,MAAA;MAAA,OAAAhE,EAAA,CAAAyB,WAAA,CAAAuC,MAAA;IAAA,EAA+C;IAC3EhE,EAAA,CAAAwC,UAAA,KAAA8C,iFAAA,wBAA4E;IAIhFtF,EADE,CAAAG,YAAA,EAAY,EACR;IAEJH,EADF,CAAAC,cAAA,eAAwB,iBAC8D;IAClFD,EAAA,CAAAE,MAAA,sBACF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,qBAA0F;IAA9DD,EAAA,CAAA8D,gBAAA,2BAAAyB,yGAAAvB,MAAA;MAAAhE,EAAA,CAAAmB,aAAA,CAAA8C,IAAA;MAAA,MAAA5C,OAAA,GAAArB,EAAA,CAAAsB,aAAA;MAAAtB,EAAA,CAAAkE,kBAAA,CAAA7C,OAAA,CAAA8C,cAAA,CAAAqB,iBAAA,EAAAxB,MAAA,MAAA3C,OAAA,CAAA8C,cAAA,CAAAqB,iBAAA,GAAAxB,MAAA;MAAA,OAAAhE,EAAA,CAAAyB,WAAA,CAAAuC,MAAA;IAAA,EAA8C;IACxEhE,EAAA,CAAAwC,UAAA,KAAAiD,iFAAA,wBAA2E;IAI/EzF,EADE,CAAAG,YAAA,EAAY,EACR;IAGJH,EADF,CAAAC,cAAA,eAAwB,iBAC+C;IACnED,EAAA,CAAAE,MAAA,kCACF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,uBAAgE;IAApCD,EAAA,CAAA8D,gBAAA,2BAAA4B,2GAAA1B,MAAA;MAAAhE,EAAA,CAAAmB,aAAA,CAAA8C,IAAA;MAAA,MAAA5C,OAAA,GAAArB,EAAA,CAAAsB,aAAA;MAAAtB,EAAA,CAAAkE,kBAAA,CAAA7C,OAAA,CAAAiD,WAAA,CAAAnB,SAAA,EAAAa,MAAA,MAAA3C,OAAA,CAAAiD,WAAA,CAAAnB,SAAA,GAAAa,MAAA;MAAA,OAAAhE,EAAA,CAAAyB,WAAA,CAAAuC,MAAA;IAAA,EAAmC;IAAChE,EAAA,CAAAE,MAAA,eAChE;IACFF,EADE,CAAAG,YAAA,EAAc,EACV;IAGJH,EADF,CAAAC,cAAA,eAAwB,iBAC+C;IACnED,EAAA,CAAAE,MAAA,kCACF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,uBAAgE;IAApCD,EAAA,CAAA8D,gBAAA,2BAAA6B,2GAAA3B,MAAA;MAAAhE,EAAA,CAAAmB,aAAA,CAAA8C,IAAA;MAAA,MAAA5C,OAAA,GAAArB,EAAA,CAAAsB,aAAA;MAAAtB,EAAA,CAAAkE,kBAAA,CAAA7C,OAAA,CAAAiD,WAAA,CAAAd,SAAA,EAAAQ,MAAA,MAAA3C,OAAA,CAAAiD,WAAA,CAAAd,SAAA,GAAAQ,MAAA;MAAA,OAAAhE,EAAA,CAAAyB,WAAA,CAAAuC,MAAA;IAAA,EAAmC;IAAChE,EAAA,CAAAE,MAAA,eAChE;IACFF,EADE,CAAAG,YAAA,EAAc,EACV;IAGJH,EADF,CAAAC,cAAA,eAAsC,iBACgD;IAClFD,EAAA,CAAAE,MAAA,kCACF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAENH,EADF,CAAAC,cAAA,eAAoC,yBACL;IAC3BD,EAAA,CAAA4F,SAAA,mBAAoD;IACpD5F,EAAA,CAAAC,cAAA,iBACiE;IAA1CD,EAAA,CAAA8D,gBAAA,2BAAA+B,qGAAA7B,MAAA;MAAAhE,EAAA,CAAAmB,aAAA,CAAA8C,IAAA;MAAA,MAAA5C,OAAA,GAAArB,EAAA,CAAAsB,aAAA;MAAAtB,EAAA,CAAAkE,kBAAA,CAAA7C,OAAA,CAAAiD,WAAA,CAAAwB,eAAA,EAAA9B,MAAA,MAAA3C,OAAA,CAAAiD,WAAA,CAAAwB,eAAA,GAAA9B,MAAA;MAAA,OAAAhE,EAAA,CAAAyB,WAAA,CAAAuC,MAAA;IAAA,EAAyC;IADhEhE,EAAA,CAAAG,YAAA,EACiE;IACjEH,EAAA,CAAA4F,SAAA,4BAA8D;IAChE5F,EAAA,CAAAG,YAAA,EAAgB;IAChBH,EAAA,CAAAC,cAAA,yBAA6B;IAC3BD,EAAA,CAAA4F,SAAA,mBAAoD;IACpD5F,EAAA,CAAAC,cAAA,iBAC+D;IAAxCD,EAAA,CAAA8D,gBAAA,2BAAAiC,qGAAA/B,MAAA;MAAAhE,EAAA,CAAAmB,aAAA,CAAA8C,IAAA;MAAA,MAAA5C,OAAA,GAAArB,EAAA,CAAAsB,aAAA;MAAAtB,EAAA,CAAAkE,kBAAA,CAAA7C,OAAA,CAAAiD,WAAA,CAAA0B,aAAA,EAAAhC,MAAA,MAAA3C,OAAA,CAAAiD,WAAA,CAAA0B,aAAA,GAAAhC,MAAA;MAAA,OAAAhE,EAAA,CAAAyB,WAAA,CAAAuC,MAAA;IAAA,EAAuC;IAD9DhE,EAAA,CAAAG,YAAA,EAC+D;IAC/DH,EAAA,CAAA4F,SAAA,4BAA4D;IAKpE5F,EAJM,CAAAG,YAAA,EAAgB,EACZ,EAEF,EACO;;;;;;IArHmBH,EAAA,CAAAM,SAAA,GAA+C;IAA/CN,EAAA,CAAAiG,gBAAA,YAAA5E,OAAA,CAAA8C,cAAA,CAAA9B,kBAAA,CAA+C;IAC7CrC,EAAA,CAAAM,SAAA,EAAuB;IAAvBN,EAAA,CAAAI,UAAA,YAAAiB,OAAA,CAAA6E,oBAAA,CAAuB;IAUMlG,EAAA,CAAAM,SAAA,GAAoC;IAApCN,EAAA,CAAAiG,gBAAA,YAAA5E,OAAA,CAAAiD,WAAA,CAAAC,UAAA,CAAoC;IAOpCvE,EAAA,CAAAM,SAAA,GAAgC;IAAhCN,EAAA,CAAAiG,gBAAA,YAAA5E,OAAA,CAAAiD,WAAA,CAAAtB,MAAA,CAAgC;IAQhChD,EAAA,CAAAM,SAAA,GAAuC;IAAvCN,EAAA,CAAAiG,gBAAA,YAAA5E,OAAA,CAAAiD,WAAA,CAAAI,aAAA,CAAuC;IAOtC1E,EAAA,CAAAM,SAAA,GAAqC;IAArCN,EAAA,CAAAiG,gBAAA,YAAA5E,OAAA,CAAAiD,WAAA,CAAAM,WAAA,CAAqC;IAOtC5E,EAAA,CAAAM,SAAA,GAA+B;IAA/BN,EAAA,CAAAiG,gBAAA,YAAA5E,OAAA,CAAAiD,WAAA,CAAAQ,KAAA,CAA+B;IAM/B9E,EAAA,CAAAM,SAAA,GAAgC;IAAhCN,EAAA,CAAAiG,gBAAA,YAAA5E,OAAA,CAAAiD,WAAA,CAAAU,MAAA,CAAgC;IAO/DhF,EAAA,CAAAM,SAAA,GAA+C;IAA/CN,EAAA,CAAAiG,gBAAA,YAAA5E,OAAA,CAAA8C,cAAA,CAAAe,kBAAA,CAA+C;IAC7ClF,EAAA,CAAAM,SAAA,EAA2B;IAA3BN,EAAA,CAAAI,UAAA,YAAAiB,OAAA,CAAA8E,OAAA,CAAAC,gBAAA,CAA2B;IAU7BpG,EAAA,CAAAM,SAAA,GAA+C;IAA/CN,EAAA,CAAAiG,gBAAA,YAAA5E,OAAA,CAAA8C,cAAA,CAAAkB,kBAAA,CAA+C;IAC7CrF,EAAA,CAAAM,SAAA,EAA2B;IAA3BN,EAAA,CAAAI,UAAA,YAAAiB,OAAA,CAAA8E,OAAA,CAAAE,gBAAA,CAA2B;IAS/BrG,EAAA,CAAAM,SAAA,GAA8C;IAA9CN,EAAA,CAAAiG,gBAAA,YAAA5E,OAAA,CAAA8C,cAAA,CAAAqB,iBAAA,CAA8C;IAC1CxF,EAAA,CAAAM,SAAA,EAA0B;IAA1BN,EAAA,CAAAI,UAAA,YAAAiB,OAAA,CAAA8E,OAAA,CAAAG,eAAA,CAA0B;IAU9BtG,EAAA,CAAAM,SAAA,GAAmC;IAAnCN,EAAA,CAAAiG,gBAAA,YAAA5E,OAAA,CAAAiD,WAAA,CAAAnB,SAAA,CAAmC;IAQnCnD,EAAA,CAAAM,SAAA,GAAmC;IAAnCN,EAAA,CAAAiG,gBAAA,YAAA5E,OAAA,CAAAiD,WAAA,CAAAd,SAAA,CAAmC;IAWQxD,EAAA,CAAAM,SAAA,GAA0B;IAA1BN,EAAA,CAAAI,UAAA,iBAAAmG,aAAA,CAA0B;IACtEvG,EAAA,CAAAiG,gBAAA,YAAA5E,OAAA,CAAAiD,WAAA,CAAAwB,eAAA,CAAyC;IAKC9F,EAAA,CAAAM,SAAA,GAAwB;IAAxBN,EAAA,CAAAI,UAAA,iBAAAoG,WAAA,CAAwB;IAClExG,EAAA,CAAAiG,gBAAA,YAAA5E,OAAA,CAAAiD,WAAA,CAAA0B,aAAA,CAAuC;;;;;;IASpEhG,EAAA,CAAAC,cAAA,iBAAqG;IAA9BD,EAAA,CAAAiB,UAAA,mBAAAwF,uFAAA;MAAAzG,EAAA,CAAAmB,aAAA,CAAAuF,IAAA;MAAA,MAAAC,OAAA,GAAA3G,EAAA,CAAAsB,aAAA,GAAAsF,SAAA;MAAA,MAAAvF,OAAA,GAAArB,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAyB,WAAA,CAASJ,OAAA,CAAAwF,cAAA,CAAAF,OAAA,CAAmB;IAAA,EAAC;IAAC3G,EAAA,CAAAE,MAAA,mBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;IAjIpHH,EAAA,CAAAC,cAAA,kBAA+C;IAG7CD,EAAA,CAAAwC,UAAA,IAAAsE,oEAAA,6BAA+C;IA6H7C9G,EADF,CAAAC,cAAA,yBAAsD,iBACsB;IAAvBD,EAAA,CAAAiB,UAAA,mBAAA8F,8EAAA;MAAA,MAAAJ,OAAA,GAAA3G,EAAA,CAAAmB,aAAA,CAAA6F,IAAA,EAAAJ,SAAA;MAAA,MAAAvF,OAAA,GAAArB,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAyB,WAAA,CAASJ,OAAA,CAAA4F,OAAA,CAAAN,OAAA,CAAY;IAAA,EAAC;IAAC3G,EAAA,CAAAE,MAAA,mBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACrFH,EAAA,CAAAwC,UAAA,IAAA0E,8DAAA,qBAAqG;IAEzGlH,EADE,CAAAG,YAAA,EAAiB,EACT;;;;IAhIoBH,EAAA,CAAAM,SAAA,EAAiB;IAAjBN,EAAA,CAAAI,UAAA,SAAAiB,OAAA,CAAAiD,WAAA,CAAiB;IA8HYtE,EAAA,CAAAM,SAAA,GAAc;IAAdN,EAAA,CAAAI,UAAA,SAAAiB,OAAA,CAAA8F,QAAA,CAAc;;;;;;IA8BrEnH,EAAA,CAAAC,cAAA,kBAAiF;IAAhCD,EAAA,CAAAiB,UAAA,mBAAAmG,wFAAA;MAAApH,EAAA,CAAAmB,aAAA,CAAAkG,IAAA;MAAA,MAAAC,OAAA,GAAAtH,EAAA,CAAAsB,aAAA,GAAAsF,SAAA;MAAA,MAAAvF,OAAA,GAAArB,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAyB,WAAA,CAASJ,OAAA,CAAAkG,gBAAA,CAAAD,OAAA,CAAqB;IAAA,EAAC;IAACtH,EAAA,CAAAE,MAAA,mBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;IAvB9FH,EADF,CAAAC,cAAA,kBAA+C,qBAC7B;IACdD,EAAA,CAAAE,MAAA,wFACF;IAAAF,EAAA,CAAAG,YAAA,EAAiB;IAGbH,EAFJ,CAAAC,cAAA,uBAA2B,cACD,gBACkE;IACtFD,EAAA,CAAAE,MAAA,qBACF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,gBAAuG;IAA5CD,EAAA,CAAA8D,gBAAA,2BAAA0D,qFAAAxD,MAAA;MAAAhE,EAAA,CAAAmB,aAAA,CAAAsG,IAAA;MAAA,MAAApG,OAAA,GAAArB,EAAA,CAAAsB,aAAA;MAAAtB,EAAA,CAAAkE,kBAAA,CAAA7C,OAAA,CAAAqG,aAAA,CAAA5E,aAAA,EAAAkB,MAAA,MAAA3C,OAAA,CAAAqG,aAAA,CAAA5E,aAAA,GAAAkB,MAAA;MAAA,OAAAhE,EAAA,CAAAyB,WAAA,CAAAuC,MAAA;IAAA,EAAyC;IACtGhE,EADE,CAAAG,YAAA,EAAuG,EACnG;IAEJH,EADF,CAAAC,cAAA,cAAwB,gBACoE;IAAAD,EAAA,CAAAE,MAAA,6CAC1F;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,iBAA+G;IAA9CD,EAAA,CAAA8D,gBAAA,2BAAA6D,sFAAA3D,MAAA;MAAAhE,EAAA,CAAAmB,aAAA,CAAAsG,IAAA;MAAA,MAAApG,OAAA,GAAArB,EAAA,CAAAsB,aAAA;MAAAtB,EAAA,CAAAkE,kBAAA,CAAA7C,OAAA,CAAAqG,aAAA,CAAAE,eAAA,EAAA5D,MAAA,MAAA3C,OAAA,CAAAqG,aAAA,CAAAE,eAAA,GAAA5D,MAAA;MAAA,OAAAhE,EAAA,CAAAyB,WAAA,CAAAuC,MAAA;IAAA,EAA2C;IAC9GhE,EADE,CAAAG,YAAA,EAA+G,EAC3G;IAEJH,EADF,CAAAC,cAAA,eAAwB,iBAC2D;IAAAD,EAAA,CAAAE,MAAA,uCACjF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,iBAAqG;IAArCD,EAAA,CAAA8D,gBAAA,2BAAA+D,sFAAA7D,MAAA;MAAAhE,EAAA,CAAAmB,aAAA,CAAAsG,IAAA;MAAA,MAAApG,OAAA,GAAArB,EAAA,CAAAsB,aAAA;MAAAtB,EAAA,CAAAkE,kBAAA,CAAA7C,OAAA,CAAAqG,aAAA,CAAA1E,MAAA,EAAAgB,MAAA,MAAA3C,OAAA,CAAAqG,aAAA,CAAA1E,MAAA,GAAAgB,MAAA;MAAA,OAAAhE,EAAA,CAAAyB,WAAA,CAAAuC,MAAA;IAAA,EAAkC;IAEtGhE,EAFI,CAAAG,YAAA,EAAqG,EACjG,EACO;IAEbH,EADF,CAAAC,cAAA,0BAAsD,kBACQ;IAAvBD,EAAA,CAAAiB,UAAA,mBAAA6G,+EAAA;MAAA,MAAAR,OAAA,GAAAtH,EAAA,CAAAmB,aAAA,CAAAsG,IAAA,EAAAb,SAAA;MAAA,MAAAvF,OAAA,GAAArB,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAyB,WAAA,CAASJ,OAAA,CAAA4F,OAAA,CAAAK,OAAA,CAAY;IAAA,EAAC;IAACtH,EAAA,CAAAE,MAAA,IAAS;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAC9EH,EAAA,CAAAwC,UAAA,KAAAuF,+DAAA,qBAAiF;IAErF/H,EADE,CAAAG,YAAA,EAAiB,EACT;;;;IAjBuDH,EAAA,CAAAM,SAAA,GAAyC;IAAzCN,EAAA,CAAAiG,gBAAA,YAAA5E,OAAA,CAAAqG,aAAA,CAAA5E,aAAA,CAAyC;IAKnC9C,EAAA,CAAAM,SAAA,GAA2C;IAA3CN,EAAA,CAAAiG,gBAAA,YAAA5E,OAAA,CAAAqG,aAAA,CAAAE,eAAA,CAA2C;IAK5C5H,EAAA,CAAAM,SAAA,GAAkC;IAAlCN,EAAA,CAAAiG,gBAAA,YAAA5E,OAAA,CAAAqG,aAAA,CAAA1E,MAAA,CAAkC;IAIxChD,EAAA,CAAAM,SAAA,GAAS;IAATN,EAAA,CAAA6C,iBAAA,gBAAS;IACpC7C,EAAA,CAAAM,SAAA,EAAc;IAAdN,EAAA,CAAAI,UAAA,SAAAiB,OAAA,CAAA8F,QAAA,CAAc;;;AD/TrD,OAAM,MAAOa,4BAA6B,SAAQtI,aAAa;EAE7DuI,YACUC,MAAmB,EACnBC,aAA8B,EAC9BC,OAAuB,EACvBC,KAAuB,EACvBC,aAA2B,EAC3BC,qBAA2C,EAC3CC,iBAAmC,EACnCC,OAAsB,EACtBC,MAAc,EACdC,aAA2B,EAC3BC,gBAAgC;IAExC,KAAK,CAACV,MAAM,CAAC;IAZL,KAAAA,MAAM,GAANA,MAAM;IACN,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,qBAAqB,GAArBA,qBAAqB;IACrB,KAAAC,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAZ1B,KAAAC,eAAe,GAAW,CAAC,CAAC;IAoCnB,KAAAC,SAAS,GAAG,CAAC;IACb,KAAAC,QAAQ,GAAG,EAAE;IACb,KAAAC,SAAS,GAAG,CAAC;IACb,KAAAC,YAAY,GAAG,CAAC;IAEzB,KAAA9C,OAAO,GAAG;MACRE,gBAAgB,EAAE,CAChB;QACE6C,KAAK,EAAE,CAAC;QACRC,GAAG,EAAE,QAAQ;QACbzI,KAAK,EAAE;OACR,EACD;QACEwI,KAAK,EAAE,CAAC;QACRC,GAAG,EAAE,MAAM;QACXzI,KAAK,EAAE;OACR,EACD;QACEwI,KAAK,EAAE,CAAC;QACRC,GAAG,EAAE,qBAAqB;QAC1BzI,KAAK,EAAE;OACR,CACF;MACD4F,eAAe,EAAE,CACf;QACE,OAAO,EAAE,KAAK;QACd,OAAO,EAAE;OACV,EACD;QACE,OAAO,EAAE,KAAK;QACd,OAAO,EAAE;OACV,EACD;QACE,OAAO,EAAE,KAAK;QACd,OAAO,EAAE;OACV,EACD;QACE,OAAO,EAAE,KAAK;QACd,OAAO,EAAE;OACV,EACD;QACE,OAAO,EAAE,MAAM;QACf,OAAO,EAAE;OACV,CACF;MACDF,gBAAgB,EAAE,CAChB;QACE8C,KAAK,EAAE,CAAC;QACRC,GAAG,EAAE,eAAe;QACpBzI,KAAK,EAAE;OACR,EAAE;QACDwI,KAAK,EAAE,CAAC;QACRC,GAAG,EAAE,oBAAoB;QACzBzI,KAAK,EAAE;OACR;KAEJ;IAED,KAAA2F,gBAAgB,GAAiB,CAC/B;MACE6C,KAAK,EAAE,EAAE;MACTC,GAAG,EAAE,KAAK;MACVzI,KAAK,EAAE;KACR,EACD;MACEwI,KAAK,EAAE,CAAC;MACRC,GAAG,EAAE,QAAQ;MACbzI,KAAK,EAAE;KACR,EACD;MACEwI,KAAK,EAAE,CAAC;MACRC,GAAG,EAAE,MAAM;MACXzI,KAAK,EAAE;KACR,EACD;MACEwI,KAAK,EAAE,CAAC;MACRC,GAAG,EAAE,qBAAqB;MAC1BzI,KAAK,EAAE;KACR,CACF;IAED,KAAA0I,aAAa,GAAiB,CAC5B;MACEF,KAAK,EAAE,CAAC;MACRC,GAAG,EAAE,OAAO;MACZzI,KAAK,EAAE;KACR,EACD;MACEwI,KAAK,EAAE,CAAC;MACRC,GAAG,EAAE,aAAa;MAClBzI,KAAK,EAAE;KACR,CACF;IAED,KAAA2I,gBAAgB,GAAG,CACjB;MACEH,KAAK,EAAE,IAAI;MACXC,GAAG,EAAE,KAAK;MACVzI,KAAK,EAAE;KACR,EACD;MACEwI,KAAK,EAAE,IAAI;MACXC,GAAG,EAAE,QAAQ;MACbzI,KAAK,EAAE;KACR,EACD;MACEwI,KAAK,EAAE,KAAK;MACZC,GAAG,EAAE,YAAY;MACjBzI,KAAK,EAAE;KACR,CACF;IAED,KAAA4I,iBAAiB,GAAG,CAClB;MACEJ,KAAK,EAAE,CAAC;MACRC,GAAG,EAAE,KAAK;MACVzI,KAAK,EAAE;KACR,EACD;MACEwI,KAAK,EAAE,CAAC;MACRC,GAAG,EAAE,aAAa;MAClBzI,KAAK,EAAE;KACR,EACD;MACEwI,KAAK,EAAE,CAAC;MACRC,GAAG,EAAE,iBAAiB;MACtBzI,KAAK,EAAE;KACR,CACF;IAED,KAAA0F,gBAAgB,GAAG,CACjB;MACE8C,KAAK,EAAE,EAAE;MACTC,GAAG,EAAE,KAAK;MACVzI,KAAK,EAAE;KACR,EACD;MACEwI,KAAK,EAAE,CAAC;MACRC,GAAG,EAAE,eAAe;MACpBzI,KAAK,EAAE;KACR,EACD;MACEwI,KAAK,EAAE,CAAC;MACRC,GAAG,EAAE,oBAAoB;MACzBzI,KAAK,EAAE;KACR,CACF;IAMD,KAAA6I,gBAAgB,GAAU,CAAC;MAAE7I,KAAK,EAAE,IAAI;MAAEwI,KAAK,EAAE;IAAE,CAAE,CAAC;IACtD,KAAAM,gBAAgB,GAAU,CAAC;MAAE9I,KAAK,EAAE,IAAI;MAAEwI,KAAK,EAAE;IAAE,CAAE,CAAC;IACtD,KAAA5C,eAAe,GAAU,CACvB;MACE,OAAO,EAAE,EAAE;MACX,OAAO,EAAE;KACV,EACD;MACE,OAAO,EAAE,KAAK;MACd,OAAO,EAAE;KACV,EACD;MACE,OAAO,EAAE,KAAK;MACd,OAAO,EAAE;KACV,EACD;MACE,OAAO,EAAE,KAAK;MACd,OAAO,EAAE;KACV,EACD;MACE,OAAO,EAAE,KAAK;MACd,OAAO,EAAE;KACV,EACD;MACE,OAAO,EAAE,MAAM;MACf,OAAO,EAAE;KACV,CACF;IAID,KAAAmD,UAAU,GAAG;MACXC,QAAQ,EAAE,CAAC;MACX5E,KAAK,EAAE,EAAE;MACT3B,SAAS,EAAE,KAAK;MAChBG,UAAU,EAAE,CAAC;MACbE,SAAS,EAAE,KAAK;MAChBkB,aAAa,EAAE,EAAE;MACjBiF,WAAW,EAAE,EAAE;MACfvG,SAAS,EAAE,EAAE;MACbF,UAAU,EAAE,CAAC;MACbH,UAAU,EAAE,EAAE;MACdiC,MAAM,EAAE;KACT;IAiCD,KAAA4E,YAAY,GAAgB,IAAI;IAqJhC,KAAAC,uBAAuB,GAAU,CAC/B;MACEX,KAAK,EAAE,EAAE;MAAExI,KAAK,EAAE;KACnB,CACF;IAlZC,IAAI,CAAC0B,WAAW,GAAG;MACjBC,kBAAkB,EAAE,IAAI;MACxB6C,kBAAkB,EAAE,IAAI,CAACkB,gBAAgB,CAAC,CAAC,CAAC;MAC5C0D,qBAAqB,EAAE,IAAI,CAACD,uBAAuB,CAAC,CAAC,CAAC;MACtDE,kBAAkB,EAAE,IAAI,CAACP,gBAAgB,CAAC,CAAC,CAAC;MAC5CnE,kBAAkB,EAAE,IAAI,CAACgB,gBAAgB,CAAC,CAAC,CAAC;MAC5Cb,iBAAiB,EAAE,IAAI,CAACc,eAAe,CAAC,CAAC,CAAC;MAC1C0D,mBAAmB,EAAE,IAAI,CAACV,iBAAiB,CAAC,CAAC,CAAC;MAC9CW,gBAAgB,EAAE,IAAI,CAACZ,gBAAgB,CAAC,CAAC,CAAC;MAC1Ca,KAAK,EAAE,EAAE;MACTC,GAAG,EAAE;KACN;IACD,IAAI,CAACxB,aAAa,CAACyB,OAAO,EAAE,CAACC,IAAI,CAC/BzK,GAAG,CAAE0K,GAAW,IAAI;MAClB,IAAIA,GAAG,CAACC,MAAM,kDAA4B,CAAC,CAACD,GAAG,CAACE,OAAO,EAAE;QACvD,IAAI,CAAC3B,eAAe,GAAGyB,GAAG,CAACE,OAAO;MACpC;IACF,CAAC,CAAC,CACH,CAACC,SAAS,EAAE;EACf;EAuMSC,QAAQA,CAAA;IACf,IAAI,CAACC,gBAAgB,EAAE;EAEzB;EAGAC,QAAQA,CAAA;IACN,IAAI,CAACC,YAAY,EAAE,CAACJ,SAAS,EAAE;EACjC;EAEAK,WAAWA,CAACC,OAAe;IACzB,IAAI,CAAC/B,SAAS,GAAG+B,OAAO;IACxB,IAAI,CAACF,YAAY,EAAE,CAACJ,SAAS,EAAE;EACjC;EAEAO,WAAWA,CAAA;IACT,IAAI,IAAI,CAAC5I,WAAW,CAACC,kBAAkB,CAACC,GAAG,EAAE;MAC3C,IAAI,CAACgG,aAAa,CAAC2C,4BAA4B,CAAC;QAC9CC,YAAY,EAAE,IAAI,CAAC9I,WAAW,CAACC,kBAAkB,CAACC;OACnD,CAAC,CAACmI,SAAS,CAACH,GAAG,IAAG;QACjB,IAAIA,GAAG,CAACa,OAAO,IAAIb,GAAG,CAACc,UAAU,IAAI,CAAC,EAAE;UACtC,IAAI,CAACxC,gBAAgB,CAACyC,iBAAiB,CACrCf,GAAG,CAACa,OAAO,EAAE,QAAQ,CACtB;QACH,CAAC,MAAM;UACL,IAAI,CAAC/C,OAAO,CAACkD,YAAY,CAAChB,GAAG,CAACiB,OAAQ,CAAC;QACzC;MACF,CAAC,CAAC;IACJ;EACF;EAMAC,gBAAgBA,CAAA;IACd,IAAI,CAACC,SAAS,CAACC,aAAa,CAACC,KAAK,EAAE;EACtC;EAEAC,cAAcA,CAACC,KAAY;IACzB,MAAMC,KAAK,GAAGD,KAAK,CAACE,MAA0B;IAC9C,IAAID,KAAK,CAACE,KAAK,IAAIF,KAAK,CAACE,KAAK,CAACC,MAAM,GAAG,CAAC,EAAE;MACzC,IAAI,CAACrC,YAAY,GAAGkC,KAAK,CAACE,KAAK,CAAC,CAAC,CAAC;MAClC,IAAI,CAACE,WAAW,EAAE;IACpB;EACF;EAEAA,WAAWA,CAAA;IACT,IAAI,IAAI,CAACtC,YAAY,EAAE;MACrB,MAAMuC,QAAQ,GAAG,IAAIC,QAAQ,EAAE;MAC/BD,QAAQ,CAACE,MAAM,CAAC,OAAO,EAAE,IAAI,CAACzC,YAAY,CAAC;MAC3C,IAAI,CAACtB,aAAa,CAACgE,4BAA4B,CAAC;QAC9CC,IAAI,EAAE;UACJrB,YAAY,EAAE,IAAI,CAAC9I,WAAW,CAACC,kBAAkB,CAACC,GAAG;UACrDkK,KAAK,EAAE,IAAI,CAAC5C;;OAEf,CAAC,CAACa,SAAS,CAACH,GAAG,IAAG;QACjB,IAAIA,GAAG,CAACc,UAAU,KAAK,CAAC,EAAE;UACxB,IAAI,CAAChD,OAAO,CAACqE,aAAa,CAACnC,GAAG,CAACiB,OAAQ,CAAC;UACxC,IAAI,CAACV,YAAY,EAAE,CAACJ,SAAS,EAAE;QACjC,CAAC,MAAM;UACL,IAAI,CAACrC,OAAO,CAACkD,YAAY,CAAChB,GAAG,CAACiB,OAAQ,CAAC;QACzC;MACF,CAAC,CAAC;IACJ;EACF;EAGAmB,gBAAgBA,CAAA;IACd,IAAI,CAACpE,aAAa,CAACqE,iCAAiC,CAAC;MACnDJ,IAAI,EAAE;QAAErB,YAAY,EAAE,IAAI,CAAC9I,WAAW,CAACC,kBAAkB,CAACC;MAAG;KAC9D,CAAC,CAACmI,SAAS,CAACH,GAAG,IAAG;MACjB,IAAIA,GAAG,CAACa,OAAO,IAAIb,GAAG,CAACc,UAAU,IAAI,CAAC,EAAE;QACtC,IAAI,CAAC5B,gBAAgB,GAAG,CAAC;UACvBN,KAAK,EAAE,EAAE;UAAExI,KAAK,EAAE;SACnB,EAAE,GAAG4J,GAAG,CAACa,OAAO,CAACyB,GAAG,CAACC,CAAC,IAAG;UACxB,OAAO;YAAE3D,KAAK,EAAE2D,CAAC;YAAEnM,KAAK,EAAEmM;UAAC,CAAE;QAC/B,CAAC,CAAC,CAAC;QACH,IAAI,CAACzK,WAAW,CAAC2H,kBAAkB,GAAG,IAAI,CAACP,gBAAgB,CAAC,CAAC,CAAC;MAChE;IACF,CAAC,CAAC;EACJ;EAKAsD,WAAWA,CAAA;IACT,IAAI,CAACC,WAAW,GAAG;MACjB7B,YAAY,EAAE,IAAI,CAAC9I,WAAW,CAACC,kBAAkB,CAACC,GAAG;MACrD0K,SAAS,EAAE,IAAI,CAAChE,SAAS;MACzBiE,QAAQ,EAAE,IAAI,CAAClE;KAChB;IAED,IAAI,IAAI,CAAC3G,WAAW,CAAC0H,qBAAqB,CAACZ,KAAK,EAAE;MAChD,IAAI,CAAC6D,WAAW,CAAC,eAAe,CAAC,GAAG,IAAI,CAAC3K,WAAW,CAAC0H,qBAAqB,CAACZ,KAAK;IAClF;IAEA,IAAI,IAAI,CAAC9G,WAAW,CAAC0H,qBAAqB,CAACZ,KAAK,EAAE;MAChD,IAAI,CAAC6D,WAAW,CAAC,eAAe,CAAC,GAAG,IAAI,CAAC3K,WAAW,CAAC0H,qBAAqB,CAACZ,KAAK;IAClF;IACA,IAAI,IAAI,CAAC9G,WAAW,CAAC8H,KAAK,IAAI,IAAI,CAAC9H,WAAW,CAAC+H,GAAG,EAAE;MAClD,IAAI,CAAC4C,WAAW,CAAC,QAAQ,CAAC,GAAG;QAAE7C,KAAK,EAAE,IAAI,CAAC9H,WAAW,CAAC8H,KAAK;QAAEC,GAAG,EAAE,IAAI,CAAC/H,WAAW,CAAC+H;MAAG,CAAE;IAC3F;IACA,IAAI,IAAI,CAAC/H,WAAW,CAAC2H,kBAAkB,CAACb,KAAK,EAAE;MAC7C,IAAI,CAAC6D,WAAW,CAAC,YAAY,CAAC,GAAG,IAAI,CAAC3K,WAAW,CAAC2H,kBAAkB,CAACb,KAAK;IAC5E;IAEA,IAAI,IAAI,CAAC9G,WAAW,CAAC8C,kBAAkB,CAACgE,KAAK,EAAE;MAC7C,IAAI,CAAC6D,WAAW,CAAC,YAAY,CAAC,GAAG,IAAI,CAAC3K,WAAW,CAAC8C,kBAAkB,CAACgE,KAAK;IAC5E;IACA,IAAI,OAAO,IAAI,CAAC9G,WAAW,CAAC6H,gBAAgB,CAACf,KAAK,KAAK,SAAS,EAAE;MAChE,IAAI,CAAC6D,WAAW,CAAC,WAAW,CAAC,GAAG,IAAI,CAAC3K,WAAW,CAAC6H,gBAAgB,CAACf,KAAK;IACzE;IACA,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAACgE,QAAQ,CAAC,IAAI,CAAC9K,WAAW,CAACiD,kBAAkB,CAAC6D,KAAK,CAAC,EAAE;MACjE,IAAI,CAAC6D,WAAW,CAAC,YAAY,CAAC,GAAG,IAAI,CAAC3K,WAAW,CAACiD,kBAAkB,CAAC6D,KAAK;IAC5E;IACA,IAAI,IAAI,CAAC9G,WAAW,CAACoD,iBAAiB,CAAC0D,KAAK,EAAE;MAC5C,IAAI,CAAC6D,WAAW,CAAC,WAAW,CAAC,GAAG,IAAI,CAAC3K,WAAW,CAACoD,iBAAiB,CAAC0D,KAAK;IAC1E;IACA,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,CAACgE,QAAQ,CAAC,IAAI,CAAC9K,WAAW,CAAC4H,mBAAmB,CAACd,KAAK,CAAC,EAAE;MAC/D,IAAI,CAAC6D,WAAW,CAAC,aAAa,CAAC,GAAG,IAAI,CAAC3K,WAAW,CAAC4H,mBAAmB,CAACd,KAAK;IAC9E;IAEA,OAAO,IAAI,CAAC6D,WAAW;EAEzB;EAGAI,qBAAqBA,CAACC,GAAsB;IAC1C,OAAOA,GAAG,CAACC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK,CAACA,CAAC,CAACvK,MAAM,IAAI,CAAC,KAAKsK,CAAC,CAACtK,MAAM,IAAI,CAAC,CAAC,CAAC;EAC9D;EACA6H,YAAYA,CAAA;IACV,OAAO,IAAI,CAACvC,aAAa,CAACkF,6BAA6B,CAAC;MACtDjB,IAAI,EAAE,IAAI,CAACO,WAAW;KACvB,CAAC,CAACzC,IAAI,CACLzK,GAAG,CAAC0K,GAAG,IAAG;MACR,IAAIA,GAAG,CAACa,OAAO,IAAIb,GAAG,CAACc,UAAU,IAAI,CAAC,EAAE;QACtC,IAAI,CAACqC,SAAS,GAAG,IAAI,CAACN,qBAAqB,CAAC7C,GAAG,CAACa,OAAO,CAAC;QACxD,IAAI,CAAClC,YAAY,GAAGqB,GAAG,CAACoD,UAAW;MACrC;IACF,CAAC,CAAC,CACH;EACH;EAIAC,0BAA0BA,CAAA;IACxB,IAAI,CAACC,eAAe,EAAE;IACtB,IAAI,CAAClB,gBAAgB,EAAE;IACvB,IAAI,CAAC7B,YAAY,EAAE,CAACJ,SAAS,EAAE;EACjC;EACAE,gBAAgBA,CAAA;IACd,IAAI,CAACnC,iBAAiB,CAACqF,oCAAoC,CAAC;MAAEtB,IAAI,EAAE;IAAE,CAAE,CAAC,CAAClC,IAAI,CAC5EzK,GAAG,CAAC0K,GAAG,IAAG;MACR,IAAIA,GAAG,CAACa,OAAO,IAAIb,GAAG,CAACc,UAAU,IAAI,CAAC,EAAE;QACtC,IAAI,CAAClF,oBAAoB,GAAGoE,GAAG,CAACa,OAAO,EAAEc,MAAM,GAAI3B,GAAG,CAACa,OAAO,CAAC2C,MAAM,CAAER,CAAM,IAAKA,CAAC,CAACS,OAAO,KAAK,CAAC,CAAC,CAACnB,GAAG,CAACtC,GAAG,IAAG;UAC3G,OAAO;YACL9J,cAAc,EAAE8J,GAAG,CAAC9J,cAAc;YAClC8B,GAAG,EAAEgI,GAAG,CAAChI;WACV;QACH,CAAC,CAAC,GAAG,EAAE;QAEP,IAAI,IAAI,CAACuG,eAAe,IAAI,CAAC,CAAC,EAAE;UAC9B,IAAImF,KAAK,GAAG,IAAI,CAAC9H,oBAAoB,CAAC+H,SAAS,CAAEC,CAAM,IAAKA,CAAC,CAAC5L,GAAG,IAAI,IAAI,CAACuG,eAAe,CAAC;UAC1F,IAAI,CAACzG,WAAW,CAACC,kBAAkB,GAAG,IAAI,CAAC6D,oBAAoB,CAAC8H,KAAK,CAAC;QACxE,CAAC,MAAM;UACL,IAAI,CAAC5L,WAAW,CAACC,kBAAkB,GAAG,IAAI,CAAC6D,oBAAoB,CAAC,CAAC,CAAC;QACpE;MACF;IACF,CAAC,CAAC,EACFtG,GAAG,CAAC,MAAK;MACP,IAAI,CAACgO,eAAe,EAAE;MACtB,IAAI,CAAClB,gBAAgB,EAAE;IACzB,CAAC,CAAC,EACF/M,SAAS,CAAC,MAAM,IAAI,CAACkL,YAAY,EAAE,CAAC,CACrC,CAACJ,SAAS,EAAE;EACf;EAUAmD,eAAeA,CAAA;IACb,IAAI,CAACtF,aAAa,CAAC6F,gCAAgC,CAAC;MAClD5B,IAAI,EAAE;QACJrB,YAAY,EAAE,IAAI,CAAC9I,WAAW,CAACC,kBAAkB,CAACC;;KAErD,CAAC,CAACmI,SAAS,CAACH,GAAG,IAAG;MACjB,IAAIA,GAAG,CAACa,OAAO,IAAIb,GAAG,CAACc,UAAU,IAAI,CAAC,EAAE;QACtC,IAAI,CAACvB,uBAAuB,GAAG,CAAC;UAC9BX,KAAK,EAAE,EAAE;UAAExI,KAAK,EAAE;SACnB,EAAE,GAAG4J,GAAG,CAACa,OAAO,CAACyB,GAAG,CAACC,CAAC,IAAG;UACxB,OAAO;YAAE3D,KAAK,EAAE2D,CAAC;YAAEnM,KAAK,EAAEmM;UAAC,CAAE;QAC/B,CAAC,CAAC,CAAC;QACH,IAAI,CAACzK,WAAW,CAAC0H,qBAAqB,GAAG,IAAI,CAACD,uBAAuB,CAAC,CAAC,CAAC;MAC1E;IACF,CAAC,CAAC;EACJ;EAQAuE,YAAYA,CAAC7L,GAAQ,EAAE8L,GAAQ;IAC7B,IAAI,CAAClK,cAAc,GAAG,EAAE;IACxB,IAAI,CAACmE,aAAa,CAACgG,6BAA6B,CAAC;MAC/C/B,IAAI,EAAE;QAAE7C,QAAQ,EAAEnH;MAAG;KACtB,CAAC,CAACkI,SAAS,CAACH,GAAG,IAAG;MACjB,IAAIA,GAAG,CAACa,OAAO,IAAIb,GAAG,CAACc,UAAU,IAAI,CAAC,EAAE;QACtC,IAAI,CAAC9G,WAAW,GAAG;UACjB,GAAGgG,GAAG,CAACa,OAAO;UACdrF,eAAe,EAAEwE,GAAG,CAACa,OAAO,CAACoD,gBAAgB,GAAG,IAAIC,IAAI,CAAClE,GAAG,CAACa,OAAO,CAACoD,gBAAgB,CAAC,GAAGE,SAAS;UAClGzI,aAAa,EAAEsE,GAAG,CAACa,OAAO,CAACuD,cAAc,GAAG,IAAIF,IAAI,CAAClE,GAAG,CAACa,OAAO,CAACuD,cAAc,CAAC,GAAGD;SACpF;QAED,IAAInE,GAAG,CAACa,OAAO,CAACwD,YAAY,EAAE;UAC5B,IAAI,CAACxK,cAAc,CAAC9B,kBAAkB,GAAG,IAAI,CAACuM,eAAe,CAAC,IAAI,CAAC1I,oBAAoB,EAAE,KAAK,EAAEoE,GAAG,CAACa,OAAO,CAACwD,YAAY,CAAC;QAE3H;QACA,IAAI,CAACxK,cAAc,CAACkB,kBAAkB,GAAG,IAAI,CAACuJ,eAAe,CAAC,IAAI,CAACzI,OAAO,CAACE,gBAAgB,EAAE,OAAO,EAAEiE,GAAG,CAACa,OAAO,CAAC7H,UAAU,CAAC;QAC7H,IAAIgH,GAAG,CAACa,OAAO,CAACjI,UAAU,EAAE;UAC1B,IAAI,CAACiB,cAAc,CAACe,kBAAkB,GAAG,IAAI,CAAC0J,eAAe,CAAC,IAAI,CAACzI,OAAO,CAACC,gBAAgB,EAAE,OAAO,EAAEkE,GAAG,CAACa,OAAO,CAACjI,UAAU,CAAC;QAC/H,CAAC,MAAM;UACL,IAAI,CAACiB,cAAc,CAACe,kBAAkB,GAAG,IAAI,CAACiB,OAAO,CAACC,gBAAgB,CAAC,CAAC,CAAC;QAC3E;QACA,IAAIkE,GAAG,CAACa,OAAO,CAAC/H,SAAS,EAAE;UACzB,IAAI,CAACe,cAAc,CAACqB,iBAAiB,GAAG,IAAI,CAACoJ,eAAe,CAAC,IAAI,CAACzI,OAAO,CAACG,eAAe,EAAE,OAAO,EAAEgE,GAAG,CAACa,OAAO,CAAC/H,SAAS,CAAC;QAC5H;QAEA,IAAIkH,GAAG,CAACa,OAAO,CAACwD,YAAY,EAAE;UAC5B,IAAI,IAAI,CAACjH,aAAa,EAAE;YACtB,IAAI,CAACA,aAAa,CAACwD,YAAY,GAAGZ,GAAG,CAACa,OAAO,CAACwD,YAAY;UAC5D;QACF;QACA,IAAI,CAACxG,aAAa,CAAC0G,IAAI,CAACR,GAAG,CAAC;MAC9B;IAEF,CAAC,CAAC;EACJ;EAGAO,eAAeA,CAACE,KAAY,EAAE3F,GAAW,EAAED,KAAU;IACnD,OAAO4F,KAAK,CAACC,IAAI,CAACC,IAAI,IAAIA,IAAI,CAAC7F,GAAG,CAAC,KAAKD,KAAK,CAAC;EAChD;EAGAlH,eAAeA,CAACqM,GAAQ,EAAEW,IAAS;IACjC,IAAI,CAACZ,YAAY,CAACY,IAAI,CAACzM,GAAG,EAAE8L,GAAG,CAAC;EAClC;EAEA3M,SAASA,CAAC2M,GAAQ;IAChB,IAAI,CAAC3G,aAAa,GAAG;MACnB5E,aAAa,EAAE,EAAE;MACjBE,MAAM,EAAEyL,SAAS;MACjB7G,eAAe,EAAE6G;KAClB;IACD,IAAI,CAACtG,aAAa,CAAC0G,IAAI,CAACR,GAAG,CAAC;EAC9B;EAKAY,UAAUA,CAACC,WAAmB;IAC5B,IAAIA,WAAW,EAAE;MACf,OAAOpP,MAAM,CAACoP,WAAW,CAAC,CAACC,MAAM,CAAC,qBAAqB,CAAC;IAC1D;IACA,OAAO,EAAE;EACX;EAEAtI,cAAcA,CAACwH,GAAQ;IACrB,IAAI,CAAC/J,WAAW,CAACiK,gBAAgB,GAAG,IAAI,CAACjK,WAAW,CAACwB,eAAe,GAAG,IAAI,CAACmJ,UAAU,CAAC,IAAI,CAAC3K,WAAW,CAACwB,eAAe,CAAC,GAAG,EAAE,EAC3H,IAAI,CAACxB,WAAW,CAACoK,cAAc,GAAG,IAAI,CAACpK,WAAW,CAAC0B,aAAa,GAAG,IAAI,CAACiJ,UAAU,CAAC,IAAI,CAAC3K,WAAW,CAAC0B,aAAa,CAAC,GAAG,EAAE,EAEvH,IAAI,CAACoJ,kBAAkB,GAAG;MACxB1K,aAAa,EAAE,IAAI,CAACJ,WAAW,CAACI,aAAa;MAC7C3B,UAAU,EAAE,IAAI,CAACuB,WAAW,CAACC,UAAU;MACvCmF,QAAQ,EAAE,IAAI,CAACpF,WAAW,CAAC+K,GAAG;MAC9BnM,UAAU,EAAE,IAAI,CAACiB,cAAc,CAACe,kBAAkB,GAAG,IAAI,CAACf,cAAc,CAACe,kBAAkB,CAACgE,KAAK,GAAG,IAAI;MACxG/F,SAAS,EAAE,IAAI,CAACmB,WAAW,CAACnB,SAAS;MACrCK,SAAS,EAAE,IAAI,CAACc,WAAW,CAACd,SAAS;MACrCsB,KAAK,EAAE,IAAI,CAACR,WAAW,CAACQ,KAAK;MAC7B6E,WAAW,EAAE,IAAI,CAACrF,WAAW,CAACM,WAAW;MACzCtB,UAAU,EAAE,IAAI,CAACa,cAAc,CAACkB,kBAAkB,GAAG,IAAI,CAAClB,cAAc,CAACkB,kBAAkB,CAAC6D,KAAK,GAAG,IAAI;MACxGlE,MAAM,EAAE,IAAI,CAACV,WAAW,CAACU,MAAM;MAC/B5B,SAAS,EAAE,IAAI,CAACe,cAAc,CAACqB,iBAAiB,GAAG,IAAI,CAACrB,cAAc,CAACqB,iBAAiB,CAAC0D,KAAK,GAAG,IAAI;MACrGqF,gBAAgB,EAAE,IAAI,CAACjK,WAAW,CAACiK,gBAAgB;MACnDG,cAAc,EAAE,IAAI,CAACpK,WAAW,CAACoK;KAClC;IACH,IAAI,CAACY,UAAU,EAAE;IACjB,IAAI,IAAI,CAACjH,KAAK,CAACkH,aAAa,CAACtD,MAAM,GAAG,CAAC,EAAE;MACvC,IAAI,CAAC7D,OAAO,CAACoH,aAAa,CAAC,IAAI,CAACnH,KAAK,CAACkH,aAAa,CAAC;MACpD;IACF;IAEA,IAAI,CAACjH,aAAa,CAACmH,0BAA0B,CAAC;MAC5ClD,IAAI,EAAE,IAAI,CAAC6C;KACZ,CAAC,CAAC/E,IAAI,CACLzK,GAAG,CAAC0K,GAAG,IAAG;MACR,IAAIA,GAAG,CAACc,UAAU,KAAK,CAAC,EAAE;QACxB,IAAI,CAAChD,OAAO,CAACqE,aAAa,CAAC,MAAM,CAAC;QAClC4B,GAAG,CAACqB,KAAK,EAAE;MACb,CAAC,MAAM;QACL,IAAI,CAACtH,OAAO,CAACkD,YAAY,CAAChB,GAAG,CAACiB,OAAQ,CAAC;QACvC8C,GAAG,CAACqB,KAAK,EAAE;MACb;IACF,CAAC,CAAC,EACF/P,SAAS,CAAC,MAAM,IAAI,CAACkL,YAAY,EAAE,CAAC,CACrC,CAACJ,SAAS,EAAE;EACf;EAGAkF,QAAQA,CAACtB,GAAQ;IACf,IAAIuB,OAAO,GAAkB;MAC3BlL,aAAa,EAAE,IAAI,CAACJ,WAAW,CAACI,aAAa;MAC7C3B,UAAU,EAAE,IAAI,CAACuB,WAAW,CAACC,UAAU;MACvCmF,QAAQ,EAAE,IAAI,CAACpF,WAAW,CAAC+K,GAAG;MAC9BnM,UAAU,EAAE,IAAI,CAACoB,WAAW,CAACpB,UAAU;MACvCC,SAAS,EAAE,IAAI,CAACmB,WAAW,CAACnB,SAAS;MACrCK,SAAS,EAAE,IAAI,CAACc,WAAW,CAACd,SAAS;MACrCsB,KAAK,EAAE,IAAI,CAACR,WAAW,CAACQ,KAAK;MAC7B6E,WAAW,EAAE,IAAI,CAACrF,WAAW,CAACM,WAAW;MACzCtB,UAAU,EAAE,IAAI,CAACgB,WAAW,CAAChB,UAAU;MACvC0B,MAAM,EAAE,IAAI,CAACV,WAAW,CAACU,MAAM;MAC/B5B,SAAS,EAAE,IAAI,CAACkB,WAAW,CAAClB;KAC7B;IACD,IAAI,CAACkF,aAAa,CAACmH,0BAA0B,CAAC;MAC5ClD,IAAI,EAAEqD;KACP,CAAC,CAACnF,SAAS,CAACH,GAAG,IAAG;MACjB,IAAIA,GAAG,CAACc,UAAU,KAAK,CAAC,EAAE;QACxB,IAAI,CAAChD,OAAO,CAACqE,aAAa,CAAC,MAAM,CAAC;QAClC4B,GAAG,CAACqB,KAAK,EAAE;MACb;IACF,CAAC,CAAC;EAEJ;EAEAzI,OAAOA,CAACoH,GAAQ;IACdA,GAAG,CAACqB,KAAK,EAAE;EACb;EAEAG,YAAYA,CAACC,IAAS,EAAEC,EAAQ;IAC9B,MAAMC,KAAK,GAAGD,EAAE,GAAGA,EAAE,GAAG,IAAI,CAAC3N,WAAW,CAACC,kBAAkB,CAACC,GAAG;IAC/D,IAAI,CAACoG,MAAM,CAACuH,QAAQ,CAAC,CAAC,+BAA+BH,IAAI,EAAE,EAAEE,KAAK,CAAC,CAAC;EACtE;EAEA7N,4BAA4BA,CAAC2N,IAAS,EAAEI,WAAgB,EAAEC,OAAY;IACpE,IAAI,CAACzH,MAAM,CAACuH,QAAQ,CAAC,CAAC,+BAA+BH,IAAI,EAAE,EAAEI,WAAW,EAAEC,OAAO,CAAC,CAAC;EACrF;EAIAb,UAAUA,CAAA;IACR,IAAI,CAACjH,KAAK,CAAC+H,KAAK,EAAE;IAClB,IAAI,CAAC/H,KAAK,CAACgI,QAAQ,CAAC,QAAQ,EAAE,IAAI,CAAC/L,WAAW,CAAC+K,GAAG,CAAC;IACnD,IAAI,CAAChH,KAAK,CAACgI,QAAQ,CAAC,QAAQ,EAAE,IAAI,CAACjB,kBAAkB,CAACrM,UAAU,CAAC;IACjE,IAAI,CAACsF,KAAK,CAACiI,iBAAiB,CAAC,MAAM,EAAE,IAAI,CAAClB,kBAAkB,CAACrM,UAAU,EAAE,EAAE,CAAC;IAC5E,IAAI,CAACsF,KAAK,CAACgI,QAAQ,CAAC,MAAM,EAAE,IAAI,CAAC/L,WAAW,CAACtB,MAAM,CAAC;IACpD,IAAI,CAACqF,KAAK,CAACiI,iBAAiB,CAAC,QAAQ,EAAE,IAAI,CAAClB,kBAAkB,CAAC1K,aAAa,EAAE,EAAE,CAAC;IACjF,IAAI,IAAI,CAAC0K,kBAAkB,CAACzF,WAAW,EAAE;MACvC,IAAI,CAACtB,KAAK,CAACkI,aAAa,CAAC,IAAI,CAACnB,kBAAkB,CAACzF,WAAW,CAAC;IAC/D;IACA,IAAI,CAACtB,KAAK,CAACmI,OAAO,CAAC,QAAQ,EAAE,IAAI,CAACpB,kBAAkB,CAACtK,KAAK,EAAE,IAAI,CAAC2D,OAAO,CAACgI,WAAW,CAAC;IACrF,IAAI,CAACpI,KAAK,CAACqI,aAAa,CAAC,QAAQ,EAAE,IAAI,CAACtB,kBAAkB,CAACpK,MAAM,CAAC;IAClE,IAAI,CAACqD,KAAK,CAACgI,QAAQ,CAAC,MAAM,EAAE,IAAI,CAACjB,kBAAkB,CAAChM,SAAS,CAAC;IAC9D,IAAI,CAACiF,KAAK,CAACgI,QAAQ,CAAC,QAAQ,EAAE,IAAI,CAAClM,cAAc,CAACe,kBAAkB,CAACgE,KAAK,CAAC;IAC3E,IAAI,CAACb,KAAK,CAACgI,QAAQ,CAAC,QAAQ,EAAE,IAAI,CAAClM,cAAc,CAACkB,kBAAkB,CAAC6D,KAAK,CAAC;IAC3E,IAAI,IAAI,CAAC5E,WAAW,CAACiK,gBAAgB,EAAE;MACrC,IAAI,CAAClG,KAAK,CAACgI,QAAQ,CAAC,UAAU,EAAE,IAAI,CAAC/L,WAAW,CAACoK,cAAc,CAAC;IAClE;IACA,IAAI,IAAI,CAACpK,WAAW,CAACoK,cAAc,EAAE;MACnC,IAAI,CAACrG,KAAK,CAACgI,QAAQ,CAAC,UAAU,EAAE,IAAI,CAAC/L,WAAW,CAACiK,gBAAgB,CAAC;IACpE;IACA,IAAI,CAAClG,KAAK,CAACsI,mBAAmB,CAAC,QAAQ,EAAE,IAAI,CAACrM,WAAW,CAACiK,gBAAgB,GAAG,IAAI,CAACjK,WAAW,CAACiK,gBAAgB,GAAG,EAAE,EAAE,IAAI,CAACjK,WAAW,CAACoK,cAAc,GAAG,IAAI,CAACpK,WAAW,CAACoK,cAAc,GAAG,EAAE,CAAC;EAC9L;EAEAkC,uBAAuBA,CAAA;IACrB,IAAI,CAACvI,KAAK,CAAC+H,KAAK,EAAE;IAClB,IAAI,CAAC/H,KAAK,CAACgI,QAAQ,CAAC,MAAM,EAAE,IAAI,CAAC3I,aAAa,CAACwD,YAAY,CAAC;IAC5D,IAAI,CAAC7C,KAAK,CAACgI,QAAQ,CAAC,MAAM,EAAE,IAAI,CAAC3I,aAAa,CAAC5E,aAAa,CAAC;IAC7D,IAAI,CAACuF,KAAK,CAACiI,iBAAiB,CAAC,MAAM,EAAE,IAAI,CAAC5I,aAAa,CAAC5E,aAAa,EAAE,EAAE,CAAC;IAC1E,IAAI,CAACuF,KAAK,CAACwI,sBAAsB,CAAC,UAAU,EAAE,IAAI,CAACnJ,aAAa,CAAC1E,MAAM,EAAE,CAAC,EAAE,GAAG,CAAC;IAChF,IAAI,CAACqF,KAAK,CAACwI,sBAAsB,CAAC,SAAS,EAAE,IAAI,CAACnJ,aAAa,CAACE,eAAe,EAAE,CAAC,EAAE,GAAG,CAAC;EAC1F;EAGAL,gBAAgBA,CAAC8G,GAAQ;IACvB,IAAI,CAAC3G,aAAa,CAACwD,YAAY,GAAG,IAAI,CAAC9I,WAAW,CAACC,kBAAkB,CAACC,GAAG,EACvE,IAAI,CAACsO,uBAAuB,EAAE;IAChC,IAAI,IAAI,CAACvI,KAAK,CAACkH,aAAa,CAACtD,MAAM,GAAG,CAAC,EAAE;MACvC,IAAI,CAAC7D,OAAO,CAACoH,aAAa,CAAC,IAAI,CAACnH,KAAK,CAACkH,aAAa,CAAC;MACpD;IACF;IACA,IAAI,CAAChH,qBAAqB,CAACuI,yCAAyC,CAAC;MACnEvE,IAAI,EAAE,IAAI,CAAC7E;KACZ,CAAC,CAAC2C,IAAI,CACLzK,GAAG,CAAC0K,GAAG,IAAG;MACR,IAAIA,GAAG,CAACc,UAAU,KAAK,CAAC,EAAE;QACxB,IAAI,CAAChD,OAAO,CAACqE,aAAa,CAAC,MAAM,CAAC;QAClC4B,GAAG,CAACqB,KAAK,EAAE;MACb;IACF,CAAC,CAAC,EACF/P,SAAS,CAAC,MAAM,IAAI,CAACkL,YAAY,EAAE,CAAC,CACrC,CAACJ,SAAS,EAAE;EACf;;;uCAnoBWzC,4BAA4B,EAAAhI,EAAA,CAAA+Q,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAjR,EAAA,CAAA+Q,iBAAA,CAAAG,EAAA,CAAAC,eAAA,GAAAnR,EAAA,CAAA+Q,iBAAA,CAAAK,EAAA,CAAAC,cAAA,GAAArR,EAAA,CAAA+Q,iBAAA,CAAAO,EAAA,CAAAC,gBAAA,GAAAvR,EAAA,CAAA+Q,iBAAA,CAAAS,EAAA,CAAAC,YAAA,GAAAzR,EAAA,CAAA+Q,iBAAA,CAAAS,EAAA,CAAAE,oBAAA,GAAA1R,EAAA,CAAA+Q,iBAAA,CAAAS,EAAA,CAAAG,gBAAA,GAAA3R,EAAA,CAAA+Q,iBAAA,CAAAa,EAAA,CAAAC,aAAA,GAAA7R,EAAA,CAAA+Q,iBAAA,CAAAe,EAAA,CAAAC,MAAA,GAAA/R,EAAA,CAAA+Q,iBAAA,CAAAiB,EAAA,CAAAC,YAAA,GAAAjS,EAAA,CAAA+Q,iBAAA,CAAAmB,EAAA,CAAAC,cAAA;IAAA;EAAA;;;YAA5BnK,4BAA4B;MAAAoK,SAAA;MAAAC,SAAA,WAAAC,mCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;;;;;;;;;;;;;;;UCjDvCvS,EADF,CAAAC,cAAA,iBAA0B,qBACR;UACdD,EAAA,CAAA4F,SAAA,qBAAiC;UACnC5F,EAAA,CAAAG,YAAA,EAAiB;UAEfH,EADF,CAAAC,cAAA,mBAAc,YACyB;UAAAD,EAAA,CAAAE,MAAA,gRAA6C;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAIjFH,EAHN,CAAAC,cAAA,aAA8B,aACN,aACqC,gBACT;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACxDH,EAAA,CAAAC,cAAA,qBACkD;UADtBD,EAAA,CAAA8D,gBAAA,2BAAA2O,0EAAAzO,MAAA;YAAAhE,EAAA,CAAAmB,aAAA,CAAAuR,GAAA;YAAA1S,EAAA,CAAAkE,kBAAA,CAAAsO,GAAA,CAAApQ,WAAA,CAAAC,kBAAA,EAAA2B,MAAA,MAAAwO,GAAA,CAAApQ,WAAA,CAAAC,kBAAA,GAAA2B,MAAA;YAAA,OAAAhE,EAAA,CAAAyB,WAAA,CAAAuC,MAAA;UAAA,EAA4C;UACtEhE,EAAA,CAAAiB,UAAA,4BAAA0R,2EAAA;YAAA3S,EAAA,CAAAmB,aAAA,CAAAuR,GAAA;YAAA,OAAA1S,EAAA,CAAAyB,WAAA,CAAkB+Q,GAAA,CAAA7E,0BAAA,EAA4B;UAAA,EAAC;UAC/C3N,EAAA,CAAAwC,UAAA,KAAAoQ,kDAAA,wBAAoE;UAK1E5S,EAFI,CAAAG,YAAA,EAAY,EACR,EACF;UAGFH,EAFJ,CAAAC,cAAA,cAAsB,cACqC,iBACX;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACtDH,EAAA,CAAAC,cAAA,qBAAsE;UAA3DD,EAAA,CAAA8D,gBAAA,2BAAA+O,0EAAA7O,MAAA;YAAAhE,EAAA,CAAAmB,aAAA,CAAAuR,GAAA;YAAA1S,EAAA,CAAAkE,kBAAA,CAAAsO,GAAA,CAAApQ,WAAA,CAAA8C,kBAAA,EAAAlB,MAAA,MAAAwO,GAAA,CAAApQ,WAAA,CAAA8C,kBAAA,GAAAlB,MAAA;YAAA,OAAAhE,EAAA,CAAAyB,WAAA,CAAAuC,MAAA;UAAA,EAA4C;UACrDhE,EAAA,CAAAwC,UAAA,KAAAsQ,kDAAA,wBAAgE;UAKtE9S,EAFI,CAAAG,YAAA,EAAY,EACR,EACF;UAIFH,EAFJ,CAAAC,cAAA,cAAsB,cACqC,iBACT;UAC5CD,EAAA,CAAAE,MAAA,sBACF;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACRH,EAAA,CAAAC,cAAA,qBAAyE;UAA9DD,EAAA,CAAA8D,gBAAA,2BAAAiP,0EAAA/O,MAAA;YAAAhE,EAAA,CAAAmB,aAAA,CAAAuR,GAAA;YAAA1S,EAAA,CAAAkE,kBAAA,CAAAsO,GAAA,CAAApQ,WAAA,CAAA0H,qBAAA,EAAA9F,MAAA,MAAAwO,GAAA,CAAApQ,WAAA,CAAA0H,qBAAA,GAAA9F,MAAA;YAAA,OAAAhE,EAAA,CAAAyB,WAAA,CAAAuC,MAAA;UAAA,EAA+C;UACxDhE,EAAA,CAAAwC,UAAA,KAAAwQ,kDAAA,wBAAuE;UAK7EhT,EAFI,CAAAG,YAAA,EAAY,EACR,EACF;UAGFH,EAFJ,CAAAC,cAAA,cAAsB,eAC8B,iBACJ;UAAAD,EAAA,CAAAE,MAAA,eAC5C;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAENH,EADF,CAAAC,cAAA,yBAA4B,iBACiE;UAAhCD,EAAA,CAAA8D,gBAAA,2BAAAmP,sEAAAjP,MAAA;YAAAhE,EAAA,CAAAmB,aAAA,CAAAuR,GAAA;YAAA1S,EAAA,CAAAkE,kBAAA,CAAAsO,GAAA,CAAApQ,WAAA,CAAA8H,KAAA,EAAAlG,MAAA,MAAAwO,GAAA,CAAApQ,WAAA,CAAA8H,KAAA,GAAAlG,MAAA;YAAA,OAAAhE,EAAA,CAAAyB,WAAA,CAAAuC,MAAA;UAAA,EAA+B;UAC5FhE,EADE,CAAAG,YAAA,EAA2F,EAC7E;UAChBH,EAAA,CAAAC,cAAA,iBAA0C;UAAAD,EAAA,CAAAE,MAAA,UAC1C;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAENH,EADF,CAAAC,cAAA,yBAA4B,iBACuD;UAA9BD,EAAA,CAAA8D,gBAAA,2BAAAoP,sEAAAlP,MAAA;YAAAhE,EAAA,CAAAmB,aAAA,CAAAuR,GAAA;YAAA1S,EAAA,CAAAkE,kBAAA,CAAAsO,GAAA,CAAApQ,WAAA,CAAA+H,GAAA,EAAAnG,MAAA,MAAAwO,GAAA,CAAApQ,WAAA,CAAA+H,GAAA,GAAAnG,MAAA;YAAA,OAAAhE,EAAA,CAAAyB,WAAA,CAAAuC,MAAA;UAAA,EAA6B;UAGtFhE,EAHM,CAAAG,YAAA,EAAiF,EACnE,EACZ,EACF;UAIFH,EAFJ,CAAAC,cAAA,cAAsB,cACqC,iBACX;UAC1CD,EAAA,CAAAE,MAAA,sBACF;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACRH,EAAA,CAAAC,cAAA,qBAAuF;UAA3DD,EAAA,CAAA8D,gBAAA,2BAAAqP,0EAAAnP,MAAA;YAAAhE,EAAA,CAAAmB,aAAA,CAAAuR,GAAA;YAAA1S,EAAA,CAAAkE,kBAAA,CAAAsO,GAAA,CAAApQ,WAAA,CAAA2H,kBAAA,EAAA/F,MAAA,MAAAwO,GAAA,CAAApQ,WAAA,CAAA2H,kBAAA,GAAA/F,MAAA;YAAA,OAAAhE,EAAA,CAAAyB,WAAA,CAAAuC,MAAA;UAAA,EAA4C;UACtEhE,EAAA,CAAAwC,UAAA,KAAA4Q,kDAAA,wBAAgE;UAKtEpT,EAFI,CAAAG,YAAA,EAAY,EACR,EACF;UAIFH,EAFJ,CAAAC,cAAA,cAAsB,cACqC,iBACX;UAC1CD,EAAA,CAAAE,MAAA,kCACF;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACRH,EAAA,CAAAC,cAAA,qBAAyF;UAA3DD,EAAA,CAAA8D,gBAAA,2BAAAuP,0EAAArP,MAAA;YAAAhE,EAAA,CAAAmB,aAAA,CAAAuR,GAAA;YAAA1S,EAAA,CAAAkE,kBAAA,CAAAsO,GAAA,CAAApQ,WAAA,CAAAiD,kBAAA,EAAArB,MAAA,MAAAwO,GAAA,CAAApQ,WAAA,CAAAiD,kBAAA,GAAArB,MAAA;YAAA,OAAAhE,EAAA,CAAAyB,WAAA,CAAAuC,MAAA;UAAA,EAA4C;UACxEhE,EAAA,CAAAwC,UAAA,KAAA8Q,kDAAA,wBAAgE;UAKtEtT,EAFI,CAAAG,YAAA,EAAY,EACR,EACF;UAIFH,EAFJ,CAAAC,cAAA,cAAsB,cACqC,iBACZ;UACzCD,EAAA,CAAAE,MAAA,sBACF;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACRH,EAAA,CAAAC,cAAA,qBAAsF;UAA1DD,EAAA,CAAA8D,gBAAA,2BAAAyP,0EAAAvP,MAAA;YAAAhE,EAAA,CAAAmB,aAAA,CAAAuR,GAAA;YAAA1S,EAAA,CAAAkE,kBAAA,CAAAsO,GAAA,CAAApQ,WAAA,CAAAoD,iBAAA,EAAAxB,MAAA,MAAAwO,GAAA,CAAApQ,WAAA,CAAAoD,iBAAA,GAAAxB,MAAA;YAAA,OAAAhE,EAAA,CAAAyB,WAAA,CAAAuC,MAAA;UAAA,EAA2C;UACrEhE,EAAA,CAAAwC,UAAA,KAAAgR,kDAAA,wBAA+D;UAKrExT,EAFI,CAAAG,YAAA,EAAY,EACR,EACF;UAGFH,EAFJ,CAAAC,cAAA,cAAsB,cACqC,iBACd;UACvCD,EAAA,CAAAE,MAAA,sBACF;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACRH,EAAA,CAAAC,cAAA,qBAAqF;UAAzDD,EAAA,CAAA8D,gBAAA,2BAAA2P,0EAAAzP,MAAA;YAAAhE,EAAA,CAAAmB,aAAA,CAAAuR,GAAA;YAAA1S,EAAA,CAAAkE,kBAAA,CAAAsO,GAAA,CAAApQ,WAAA,CAAA6H,gBAAA,EAAAjG,MAAA,MAAAwO,GAAA,CAAApQ,WAAA,CAAA6H,gBAAA,GAAAjG,MAAA;YAAA,OAAAhE,EAAA,CAAAyB,WAAA,CAAAuC,MAAA;UAAA,EAA0C;UACpEhE,EAAA,CAAAwC,UAAA,KAAAkR,kDAAA,wBAAgE;UAKtE1T,EAFI,CAAAG,YAAA,EAAY,EACR,EACF;UAIFH,EAFJ,CAAAC,cAAA,cAAsB,cACqC,iBACV;UAC3CD,EAAA,CAAAE,MAAA,kCACF;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACRH,EAAA,CAAAC,cAAA,qBAA0F;UAA5DD,EAAA,CAAA8D,gBAAA,2BAAA6P,0EAAA3P,MAAA;YAAAhE,EAAA,CAAAmB,aAAA,CAAAuR,GAAA;YAAA1S,EAAA,CAAAkE,kBAAA,CAAAsO,GAAA,CAAApQ,WAAA,CAAA4H,mBAAA,EAAAhG,MAAA,MAAAwO,GAAA,CAAApQ,WAAA,CAAA4H,mBAAA,GAAAhG,MAAA;YAAA,OAAAhE,EAAA,CAAAyB,WAAA,CAAAuC,MAAA;UAAA,EAA6C;UACzEhE,EAAA,CAAAwC,UAAA,KAAAoR,kDAAA,wBAAiE;UAKvE5T,EAFI,CAAAG,YAAA,EAAY,EACR,EACF;UAKFH,EAHJ,CAAAC,cAAA,cAAsB,eAC2B,kBAEiB;UAArBD,EAAA,CAAAiB,UAAA,mBAAA4S,+DAAA;YAAA7T,EAAA,CAAAmB,aAAA,CAAAuR,GAAA;YAAA,OAAA1S,EAAA,CAAAyB,WAAA,CAAS+Q,GAAA,CAAA5H,QAAA,EAAU;UAAA,EAAC;UAC3D5K,EAAA,CAAAE,MAAA,sBAAG;UAAAF,EAAA,CAAA4F,SAAA,aAA6B;UAGtC5F,EAFI,CAAAG,YAAA,EAAS,EACL,EACF;UAEJH,EADF,CAAAC,cAAA,eAAuB,eAC0B;UAC7CD,EAAA,CAAAwC,UAAA,KAAAsR,+CAAA,qBAAmG;UAGnG9T,EAAA,CAAAC,cAAA,kBAAqF;UAA5CD,EAAA,CAAAiB,UAAA,mBAAA8S,+DAAA;YAAA/T,EAAA,CAAAmB,aAAA,CAAAuR,GAAA;YAAA,OAAA1S,EAAA,CAAAyB,WAAA,CAAS+Q,GAAA,CAAA3C,YAAA,CAAa,mBAAmB,CAAC;UAAA,EAAC;UAClF7P,EAAA,CAAAE,MAAA,8CACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAITH,EAAA,CAAAC,cAAA,kBAAiE;UAAxBD,EAAA,CAAAiB,UAAA,mBAAA+S,+DAAA;YAAAhU,EAAA,CAAAmB,aAAA,CAAAuR,GAAA;YAAA,OAAA1S,EAAA,CAAAyB,WAAA,CAAS+Q,GAAA,CAAAxH,WAAA,EAAa;UAAA,EAAC;UAC9DhL,EAAA,CAAAE,MAAA,oDACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACTH,EAAA,CAAAC,cAAA,oBAA4G;UAAzDD,EAAA,CAAAiB,UAAA,oBAAAgT,+DAAAjQ,MAAA;YAAAhE,EAAA,CAAAmB,aAAA,CAAAuR,GAAA;YAAA,OAAA1S,EAAA,CAAAyB,WAAA,CAAU+Q,GAAA,CAAA5G,cAAA,CAAA5H,MAAA,CAAsB;UAAA,EAAC;UAApFhE,EAAA,CAAAG,YAAA,EAA4G;UAC5GH,EAAA,CAAAC,cAAA,kBAAiE;UAA7BD,EAAA,CAAAiB,UAAA,mBAAAiT,+DAAA;YAAAlU,EAAA,CAAAmB,aAAA,CAAAuR,GAAA;YAAA,OAAA1S,EAAA,CAAAyB,WAAA,CAAS+Q,GAAA,CAAAhH,gBAAA,EAAkB;UAAA,EAAC;UAC9DxL,EAAA,CAAAE,MAAA,oDACF;UAGNF,EAHM,CAAAG,YAAA,EAAS,EACL,EACF,EACF;UAMEH,EAJR,CAAAC,cAAA,eAAmC,iBAC+D,aACvF,cACgD,cACpB;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACtCH,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACrCH,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACrCH,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAE,MAAA,0BAAG;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACtCH,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACrCH,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACrCH,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAE,MAAA,gCAAI;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACvCH,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAE,MAAA,iCAAI;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACvCH,EAAA,CAAAC,cAAA,eAA8B;UAAAD,EAAA,CAAAE,MAAA,qBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACrCH,EAAA,CAAAC,cAAA,eAA8B;UAAAD,EAAA,CAAAE,MAAA,qBAAE;UAEpCF,EAFoC,CAAAG,YAAA,EAAK,EAClC,EACC;UACRH,EAAA,CAAAC,cAAA,cAAO;UACLD,EAAA,CAAAwC,UAAA,MAAA2R,4CAAA,mBAAmD;UAkC3DnU,EAHM,CAAAG,YAAA,EAAQ,EACF,EACJ,EACO;UAEbH,EADF,CAAAC,cAAA,2BAAsD,2BAES;UAD7CD,EAAA,CAAA8D,gBAAA,wBAAAsQ,6EAAApQ,MAAA;YAAAhE,EAAA,CAAAmB,aAAA,CAAAuR,GAAA;YAAA1S,EAAA,CAAAkE,kBAAA,CAAAsO,GAAA,CAAAxJ,SAAA,EAAAhF,MAAA,MAAAwO,GAAA,CAAAxJ,SAAA,GAAAhF,MAAA;YAAA,OAAAhE,EAAA,CAAAyB,WAAA,CAAAuC,MAAA;UAAA,EAAoB;UAClChE,EAAA,CAAAiB,UAAA,wBAAAmT,6EAAApQ,MAAA;YAAAhE,EAAA,CAAAmB,aAAA,CAAAuR,GAAA;YAAA,OAAA1S,EAAA,CAAAyB,WAAA,CAAc+Q,GAAA,CAAA1H,WAAA,CAAA9G,MAAA,CAAmB;UAAA,EAAC;UAGxChE,EAFI,CAAAG,YAAA,EAAiB,EACF,EACT;UAyIVH,EAvIA,CAAAwC,UAAA,MAAA6R,qDAAA,gCAAArU,EAAA,CAAAsU,sBAAA,CAAmE,MAAAC,qDAAA,iCAAAvU,EAAA,CAAAsU,sBAAA,CAuIF;;;UA9U3BtU,EAAA,CAAAM,SAAA,IAA4C;UAA5CN,EAAA,CAAAiG,gBAAA,YAAAuM,GAAA,CAAApQ,WAAA,CAAAC,kBAAA,CAA4C;UAE1CrC,EAAA,CAAAM,SAAA,EAAuB;UAAvBN,EAAA,CAAAI,UAAA,YAAAoS,GAAA,CAAAtM,oBAAA,CAAuB;UAS1ClG,EAAA,CAAAM,SAAA,GAA4C;UAA5CN,EAAA,CAAAiG,gBAAA,YAAAuM,GAAA,CAAApQ,WAAA,CAAA8C,kBAAA,CAA4C;UACzBlF,EAAA,CAAAM,SAAA,EAAmB;UAAnBN,EAAA,CAAAI,UAAA,YAAAoS,GAAA,CAAApM,gBAAA,CAAmB;UAYtCpG,EAAA,CAAAM,SAAA,GAA+C;UAA/CN,EAAA,CAAAiG,gBAAA,YAAAuM,GAAA,CAAApQ,WAAA,CAAA0H,qBAAA,CAA+C;UAC5B9J,EAAA,CAAAM,SAAA,EAA0B;UAA1BN,EAAA,CAAAI,UAAA,YAAAoS,GAAA,CAAA3I,uBAAA,CAA0B;UAWK7J,EAAA,CAAAM,SAAA,GAA+B;UAA/BN,EAAA,CAAAiG,gBAAA,YAAAuM,GAAA,CAAApQ,WAAA,CAAA8H,KAAA,CAA+B;UAKvClK,EAAA,CAAAM,SAAA,GAA6B;UAA7BN,EAAA,CAAAiG,gBAAA,YAAAuM,GAAA,CAAApQ,WAAA,CAAA+H,GAAA,CAA6B;UAUtDnK,EAAA,CAAAM,SAAA,GAA4C;UAA5CN,EAAA,CAAAiG,gBAAA,YAAAuM,GAAA,CAAApQ,WAAA,CAAA2H,kBAAA,CAA4C;UAC1C/J,EAAA,CAAAM,SAAA,EAAmB;UAAnBN,EAAA,CAAAI,UAAA,YAAAoS,GAAA,CAAAhJ,gBAAA,CAAmB;UAYnBxJ,EAAA,CAAAM,SAAA,GAA4C;UAA5CN,EAAA,CAAAiG,gBAAA,YAAAuM,GAAA,CAAApQ,WAAA,CAAAiD,kBAAA,CAA4C;UAC5CrF,EAAA,CAAAM,SAAA,EAAmB;UAAnBN,EAAA,CAAAI,UAAA,YAAAoS,GAAA,CAAAnM,gBAAA,CAAmB;UAYrBrG,EAAA,CAAAM,SAAA,GAA2C;UAA3CN,EAAA,CAAAiG,gBAAA,YAAAuM,GAAA,CAAApQ,WAAA,CAAAoD,iBAAA,CAA2C;UACzCxF,EAAA,CAAAM,SAAA,EAAkB;UAAlBN,EAAA,CAAAI,UAAA,YAAAoS,GAAA,CAAAlM,eAAA,CAAkB;UAWpBtG,EAAA,CAAAM,SAAA,GAA0C;UAA1CN,EAAA,CAAAiG,gBAAA,YAAAuM,GAAA,CAAApQ,WAAA,CAAA6H,gBAAA,CAA0C;UACxCjK,EAAA,CAAAM,SAAA,EAAmB;UAAnBN,EAAA,CAAAI,UAAA,YAAAoS,GAAA,CAAAnJ,gBAAA,CAAmB;UAYnBrJ,EAAA,CAAAM,SAAA,GAA6C;UAA7CN,EAAA,CAAAiG,gBAAA,YAAAuM,GAAA,CAAApQ,WAAA,CAAA4H,mBAAA,CAA6C;UAC7ChK,EAAA,CAAAM,SAAA,EAAoB;UAApBN,EAAA,CAAAI,UAAA,YAAAoS,GAAA,CAAAlJ,iBAAA,CAAoB;UAiBRtJ,EAAA,CAAAM,SAAA,GAAc;UAAdN,EAAA,CAAAI,UAAA,SAAAoS,GAAA,CAAArL,QAAA,CAAc;UAqCnCnH,EAAA,CAAAM,SAAA,IAAe;UAAfN,EAAA,CAAAI,UAAA,YAAAoS,GAAA,CAAA/E,SAAA,CAAe;UAoC1BzN,EAAA,CAAAM,SAAA,GAAoB;UAApBN,EAAA,CAAAiG,gBAAA,SAAAuM,GAAA,CAAAxJ,SAAA,CAAoB;UAAuBhJ,EAAtB,CAAAI,UAAA,aAAAoS,GAAA,CAAAzJ,QAAA,CAAqB,mBAAAyJ,GAAA,CAAAvJ,YAAA,CAAgC;;;qBD5JlFzJ,YAAY,EAAAgV,GAAA,CAAAC,OAAA,EAAAD,GAAA,CAAAE,IAAA,EAAEnV,YAAY,EAAAoV,GAAA,CAAAC,oBAAA,EAAAD,GAAA,CAAAE,mBAAA,EAAAF,GAAA,CAAAG,eAAA,EAAAH,GAAA,CAAAI,YAAA,EAAAJ,GAAA,CAAAK,YAAA,EAAAL,GAAA,CAAAM,OAAA,EAAA/D,EAAA,CAAAgE,eAAA,EAAAhE,EAAA,CAAAiE,mBAAA,EAAAjE,EAAA,CAAAkE,qBAAA,EAAAlE,EAAA,CAAAmE,qBAAA,EAAAnE,EAAA,CAAAoE,mBAAA,EAAApE,EAAA,CAAAqE,gBAAA,EAAArE,EAAA,CAAAsE,iBAAA,EAAAtE,EAAA,CAAAuE,iBAAA,EAAAvE,EAAA,CAAAwE,oBAAA,EAAAxE,EAAA,CAAAyE,iBAAA,EAAAzE,EAAA,CAAA0E,eAAA,EAAA1E,EAAA,CAAA2E,qBAAA,EAAA3E,EAAA,CAAA4E,qBAAA,EAAAC,GAAA,CAAAC,aAAA,EAAAC,GAAA,CAAAC,mBAAA,EAAAC,GAAA,CAAAC,kBAAA,EAAgB3W,kBAAkB,EAAEI,mBAAmB;MAAAwW,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}