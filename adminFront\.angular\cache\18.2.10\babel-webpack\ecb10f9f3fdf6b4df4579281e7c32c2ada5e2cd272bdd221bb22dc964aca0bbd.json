{"ast": null, "code": "import { BaseComponent } from '../../components/base/baseComponent';\nimport { FormsModule } from '@angular/forms';\nimport { MomentPipe } from '../../../@theme/pipes/moment.pipe';\nimport { Ng<PERSON><PERSON>, formatDate } from '@angular/common';\nimport { NbCardModule, NbInputModule, NbDatepickerModule, NbSelectModule, NbOptionModule } from '@nebular/theme';\nimport { PaginationComponent } from '../../components/pagination/pagination.component';\nimport { BreadcrumbComponent } from '../../components/breadcrumb/breadcrumb.component';\nimport { takeUntilDestroyed } from '@angular/core/rxjs-interop';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/services/api/services\";\nimport * as i2 from \"../../components/shared.observable\";\nimport * as i3 from \"src/app/shared/helper/allowHelper\";\nimport * as i4 from \"src/app/shared/helper/validationHelper\";\nimport * as i5 from \"src/app/shared/services/message.service\";\nimport * as i6 from \"@nebular/theme\";\nimport * as i7 from \"@angular/forms\";\nfunction LogsManagementComponent_nb_option_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 13);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const fun_r2 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", fun_r2.Id);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", fun_r2.Name, \"\");\n  }\n}\nfunction LogsManagementComponent_tr_47_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\", 26)(1, \"td\", 27);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\", 28);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\", 29);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"td\", 29);\n    i0.ɵɵtext(8);\n    i0.ɵɵpipe(9, \"localDate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"td\", 29);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"td\", 29);\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const data_r3 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(data_r3.CId);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(data_r3.CAccount);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(data_r3.CName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(9, 6, data_r3.DateCreate, \"yyyy-MM-DD HH:mm:ss\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(data_r3.FunctionName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(data_r3.Ip);\n  }\n}\nexport class LogsManagementComponent extends BaseComponent {\n  constructor(userService, share, allow, baseFuncService, valid, message, drestoyedRef) {\n    super(allow);\n    this.userService = userService;\n    this.share = share;\n    this.allow = allow;\n    this.baseFuncService = baseFuncService;\n    this.valid = valid;\n    this.message = message;\n    this.drestoyedRef = drestoyedRef;\n    this.userLogs = [];\n    this.functions = [];\n    this.now = new Date();\n    this.request = {\n      DateEnd: '',\n      DateStart: '',\n      PageIndex: this.pageIndex,\n      PageSize: this.pageSize,\n      FunctionId: -1\n    };\n    this.start = this.now;\n    this.end = this.now;\n    this.share.SharedUserLog.subscribe(res => {\n      this.userLogs = res;\n    });\n    this.share.SharedFunctionModel.subscribe(res => {\n      this.functions = res;\n      if (res.length === 0) {\n        this.getFunction();\n      } else {\n        this.functions = res;\n      }\n    });\n  }\n  ngOnInit() {\n    this.getUserLog();\n  }\n  validationDate() {\n    this.valid.clear();\n    this.valid.Date(new Date(this.start), new Date(this.end));\n  }\n  getUserLog() {\n    this.validationDate();\n    if (this.valid.errorMessages.length > 0) {\n      this.message.showErrorMSGs(this.valid.errorMessages);\n      return;\n    }\n    this.request.PageIndex = this.pageIndex;\n    this.request.PageSize = this.pageSize;\n    this.request.DateStart = formatDate(this.start, 'yyyy-MM-ddT00:00:00', 'en');\n    this.request.DateEnd = formatDate(this.end, 'yyyy-MM-ddT23:59:59', 'en');\n    let req = {\n      body: {\n        ...this.request\n      }\n    };\n    this.userService.apiUserGetUserLogPost$Json(req).pipe(takeUntilDestroyed(this.drestoyedRef)).subscribe(res => {\n      this.userLogs = res.Entries;\n      this.totalRecords = res.TotalItems;\n      this.share.SetUserLog(this.userLogs);\n    });\n  }\n  getFunction() {\n    this.baseFuncService.apiBaseFunctionGetFunctionPost$Json({\n      body: {}\n    }).subscribe(res => {\n      this.functions = res.Entries;\n      this.share.SetFunctionModel(this.functions);\n    });\n  }\n  static {\n    this.ɵfac = function LogsManagementComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || LogsManagementComponent)(i0.ɵɵdirectiveInject(i1.UserService), i0.ɵɵdirectiveInject(i2.SharedObservable), i0.ɵɵdirectiveInject(i3.AllowHelper), i0.ɵɵdirectiveInject(i1.BaseFunctionService), i0.ɵɵdirectiveInject(i4.ValidationHelper), i0.ɵɵdirectiveInject(i5.MessageService), i0.ɵɵdirectiveInject(i0.DestroyRef));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: LogsManagementComponent,\n      selectors: [[\"ngx-logs-management\"]],\n      standalone: true,\n      features: [i0.ɵɵInheritDefinitionFeature, i0.ɵɵStandaloneFeature],\n      decls: 49,\n      vars: 11,\n      consts: [[\"formcontrol\", \"\"], [\"ngmodel\", \"\"], [\"accent\", \"success\"], [1, \"bg-white\"], [1, \"col-12\"], [1, \"row\"], [1, \"form-group\", \"col-12\", \"col-md-6\"], [\"for\", \"keyWord\", 1, \"label\", \"mr-2\"], [\"nbInput\", \"\", \"placeholder\", \"\\u8D77\\u65E5\", 1, \"mr-2\", 3, \"ngModelChange\", \"nbDatepicker\", \"ngModel\"], [1, \"label\", \"mr-2\"], [\"nbInput\", \"\", \"placeholder\", \"\\u8FC4\\u65E5\", 3, \"ngModelChange\", \"nbDatepicker\", \"ngModel\"], [1, \"form-group\", \"col-12\", \"col-md-4\"], [\"id\", \"FunctionId\", \"name\", \"FunctionId\", 3, \"selectedChange\", \"selected\"], [3, \"value\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [1, \"form-group\", \"col-12\", \"col-md-2\", \"text-right\"], [1, \"btn\", \"btn-info\", \"mr-2\", 3, \"click\"], [1, \"fas\", \"fa-search\", \"mr-1\"], [1, \"table-responsive\"], [1, \"table\", \"table-striped\", \"border\", 2, \"min-width\", \"800px\", \"background-color\", \"#f3f3f3\"], [1, \"d-flex\", \"text-white\", 2, \"background-color\", \"#27ae60\"], [\"scope\", \"col\", 1, \"col-1\"], [\"scope\", \"col\", 1, \"col-3\"], [\"scope\", \"col\", 1, \"col-2\"], [\"class\", \"d-flex\", 4, \"ngFor\", \"ngForOf\"], [3, \"PageChange\", \"CollectionSize\", \"Page\", \"PageSize\"], [1, \"d-flex\"], [1, \"col-1\"], [1, \"col-3\"], [1, \"col-2\"]],\n      template: function LogsManagementComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"nb-card\", 2)(1, \"nb-card-header\");\n          i0.ɵɵelement(2, \"ngx-breadcrumb\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(3, \"nb-card-body\", 3)(4, \"div\", 4)(5, \"div\", 5)(6, \"div\", 6)(7, \"label\", 7);\n          i0.ɵɵtext(8, \"\\u65E5\\u671F\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(9, \"input\", 8);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function LogsManagementComponent_Template_input_ngModelChange_9_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.start, $event) || (ctx.start = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(10, \"nb-datepicker\", null, 0);\n          i0.ɵɵelementStart(12, \"label\", 9);\n          i0.ɵɵtext(13, \"~\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(14, \"input\", 10);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function LogsManagementComponent_Template_input_ngModelChange_14_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.end, $event) || (ctx.end = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(15, \"nb-datepicker\", null, 1);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(17, \"div\", 11)(18, \"label\", 7);\n          i0.ɵɵtext(19, \"\\u64CD\\u4F5C\\u529F\\u80FD\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(20, \"nb-select\", 12);\n          i0.ɵɵtwoWayListener(\"selectedChange\", function LogsManagementComponent_Template_nb_select_selectedChange_20_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.request.FunctionId, $event) || (ctx.request.FunctionId = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵelementStart(21, \"nb-option\", 13);\n          i0.ɵɵtext(22, \"\\u5168\\u90E8\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(23, LogsManagementComponent_nb_option_23_Template, 2, 2, \"nb-option\", 14);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(24, \"div\", 15)(25, \"button\", 16);\n          i0.ɵɵlistener(\"click\", function LogsManagementComponent_Template_button_click_25_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.getUserLog());\n          });\n          i0.ɵɵelement(26, \"i\", 17);\n          i0.ɵɵtext(27, \"\\u67E5\\u8A62\");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(28, \"nb-card-body\", 3)(29, \"div\", 4)(30, \"div\", 18)(31, \"table\", 19)(32, \"thead\")(33, \"tr\", 20)(34, \"th\", 21);\n          i0.ɵɵtext(35, \"\\u5E8F\\u865F\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(36, \"th\", 22);\n          i0.ɵɵtext(37, \"\\u4F7F\\u7528\\u8005\\u5E33\\u865F\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(38, \"th\", 23);\n          i0.ɵɵtext(39, \"\\u4F7F\\u7528\\u8005\\u59D3\\u540D\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(40, \"th\", 23);\n          i0.ɵɵtext(41, \"\\u7D00\\u9304\\u6642\\u9593\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(42, \"th\", 23);\n          i0.ɵɵtext(43, \"\\u64CD\\u4F5C\\u529F\\u80FD\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(44, \"th\", 23);\n          i0.ɵɵtext(45, \"\\u767B\\u5165IP\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(46, \"tbody\");\n          i0.ɵɵtemplate(47, LogsManagementComponent_tr_47_Template, 14, 9, \"tr\", 24);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(48, \"ngx-pagination\", 25);\n          i0.ɵɵtwoWayListener(\"PageChange\", function LogsManagementComponent_Template_ngx_pagination_PageChange_48_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.pageIndex, $event) || (ctx.pageIndex = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵlistener(\"PageChange\", function LogsManagementComponent_Template_ngx_pagination_PageChange_48_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.getUserLog());\n          });\n          i0.ɵɵelementEnd()()()();\n        }\n        if (rf & 2) {\n          const formcontrol_r4 = i0.ɵɵreference(11);\n          const ngmodel_r5 = i0.ɵɵreference(16);\n          i0.ɵɵadvance(9);\n          i0.ɵɵproperty(\"nbDatepicker\", formcontrol_r4);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.start);\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"nbDatepicker\", ngmodel_r5);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.end);\n          i0.ɵɵadvance(6);\n          i0.ɵɵtwoWayProperty(\"selected\", ctx.request.FunctionId);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"value\", -1);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngForOf\", ctx.functions);\n          i0.ɵɵadvance(24);\n          i0.ɵɵproperty(\"ngForOf\", ctx.userLogs);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"CollectionSize\", ctx.totalRecords);\n          i0.ɵɵtwoWayProperty(\"Page\", ctx.pageIndex);\n          i0.ɵɵproperty(\"PageSize\", ctx.pageSize);\n        }\n      },\n      dependencies: [NbCardModule, i6.NbCardComponent, i6.NbCardBodyComponent, i6.NbCardHeaderComponent, BreadcrumbComponent, NbInputModule, i6.NbInputDirective, NbDatepickerModule, i6.NbDatepickerDirective, i6.NbDatepickerComponent, FormsModule, i7.DefaultValueAccessor, i7.NgControlStatus, i7.NgModel, NbSelectModule, i6.NbSelectComponent, i6.NbOptionComponent, NbOptionModule, NgFor, PaginationComponent, MomentPipe],\n      styles: [\"/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsImZpbGUiOiJsb2dzLW1hbmFnZW1lbnQuY29tcG9uZW50LnNjc3MifQ== */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvcGFnZXMvc3lzdGVtLW1hbmFnZW1lbnQvbG9ncy1tYW5hZ2VtZW50L2xvZ3MtbWFuYWdlbWVudC5jb21wb25lbnQuc2NzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiO0FBQ0EsZ0xBQWdMIiwic291cmNlUm9vdCI6IiJ9 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["BaseComponent", "FormsModule", "MomentPipe", "<PERSON><PERSON><PERSON>", "formatDate", "NbCardModule", "NbInputModule", "NbDatepickerModule", "NbSelectModule", "NbOptionModule", "PaginationComponent", "BreadcrumbComponent", "takeUntilDestroyed", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵproperty", "fun_r2", "Id", "ɵɵadvance", "ɵɵtextInterpolate1", "Name", "ɵɵtextInterpolate", "data_r3", "CId", "CAccount", "CName", "ɵɵpipeBind2", "DateCreate", "FunctionName", "Ip", "LogsManagementComponent", "constructor", "userService", "share", "allow", "baseFuncService", "valid", "message", "drestoyedRef", "userLogs", "functions", "now", "Date", "request", "DateEnd", "DateStart", "PageIndex", "pageIndex", "PageSize", "pageSize", "FunctionId", "start", "end", "SharedUserLog", "subscribe", "res", "SharedFunctionModel", "length", "getFunction", "ngOnInit", "getUserLog", "validationDate", "clear", "errorMessages", "showErrorMSGs", "req", "body", "apiUserGetUserLogPost$Json", "pipe", "Entries", "totalRecords", "TotalItems", "SetUserLog", "apiBaseFunctionGetFunctionPost$Json", "SetFunctionModel", "ɵɵdirectiveInject", "i1", "UserService", "i2", "SharedObservable", "i3", "AllowHelper", "BaseFunctionService", "i4", "ValidationHelper", "i5", "MessageService", "DestroyRef", "selectors", "standalone", "features", "ɵɵInheritDefinitionFeature", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "LogsManagementComponent_Template", "rf", "ctx", "ɵɵelement", "ɵɵtwoWayListener", "LogsManagementComponent_Template_input_ngModelChange_9_listener", "$event", "ɵɵrestoreView", "_r1", "ɵɵtwoWayBindingSet", "ɵɵresetView", "LogsManagementComponent_Template_input_ngModelChange_14_listener", "LogsManagementComponent_Template_nb_select_selected<PERSON><PERSON>e_20_listener", "ɵɵtemplate", "LogsManagementComponent_nb_option_23_Template", "ɵɵlistener", "LogsManagementComponent_Template_button_click_25_listener", "LogsManagementComponent_tr_47_Template", "LogsManagementComponent_Template_ngx_pagination_PageChange_48_listener", "formcontrol_r4", "ɵɵtwoWayProperty", "ngmodel_r5", "i6", "NbCardComponent", "NbCardBodyComponent", "NbCardHeaderComponent", "NbInputDirective", "NbDatepickerDirective", "NbDatepickerComponent", "i7", "DefaultValueAccessor", "NgControlStatus", "NgModel", "NbSelectComponent", "NbOptionComponent", "styles"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\system-management\\logs-management\\logs-management.component.ts", "C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\system-management\\logs-management\\logs-management.component.html"], "sourcesContent": ["import { BaseComponent } from '../../components/base/baseComponent';\r\nimport { Component, DestroyRef, OnInit } from '@angular/core';\r\nimport { FormControl, FormsModule } from '@angular/forms';\r\nimport { MomentPipe } from '../../../@theme/pipes/moment.pipe';\r\nimport { NgFor, formatDate } from '@angular/common';\r\nimport { NbCardModule, NbInputModule, NbDatepickerModule, NbSelectModule, NbOptionModule } from '@nebular/theme';\r\nimport { BaseFunctionService, UserService } from 'src/services/api/services';\r\nimport { AllowHelper } from 'src/app/shared/helper/allowHelper';\r\nimport { ShareRequest } from 'src/app/shared/model/requests/shareRequest';\r\nimport { PaginationComponent } from '../../components/pagination/pagination.component';\r\nimport { BreadcrumbComponent } from '../../components/breadcrumb/breadcrumb.component';\r\nimport { SharedObservable } from '../../components/shared.observable';\r\nimport { FunctionModel } from 'src/app/shared/model/function.model';\r\nimport { UserGetUserLogResponse } from 'src/services/api/models/user-get-user-log-response';\r\nimport { ValidationHelper } from 'src/app/shared/helper/validationHelper';\r\nimport { MessageService } from 'src/app/shared/services/message.service';\r\nimport { takeUntilDestroyed } from '@angular/core/rxjs-interop';\r\nimport { UserGetUserLogArgs } from 'src/services/api/models/user-get-user-log-args';\r\nimport { EnumResponse } from 'src/services/api/models/enum-response';\r\n\r\n@Component({\r\n  selector: 'ngx-logs-management',\r\n  templateUrl: './logs-management.component.html',\r\n  styleUrls: ['./logs-management.component.scss'],\r\n  standalone: true,\r\n  imports: [\r\n    NbCardModule,\r\n    BreadcrumbComponent,\r\n    NbInputModule,\r\n    NbDatepickerModule,\r\n    FormsModule,\r\n    NbSelectModule,\r\n    NbOptionModule,\r\n    NgFor,\r\n    PaginationComponent,\r\n    MomentPipe,\r\n  ],\r\n})\r\nexport class LogsManagementComponent extends BaseComponent implements OnInit {\r\n\r\n  userLogs: UserGetUserLogResponse[] = []\r\n  functions = [] as EnumResponse[];\r\n\r\n  now = new Date();\r\n  request: UserGetUserLogArgs = {\r\n    DateEnd: '',\r\n    DateStart: '',\r\n    PageIndex: this.pageIndex,\r\n    PageSize: this.pageSize,\r\n    FunctionId: -1\r\n  };\r\n  start = this.now;\r\n  end = this.now;\r\n  ;\r\n\r\n  constructor(\r\n    private userService: UserService,\r\n    private share: SharedObservable,\r\n    protected override allow: AllowHelper,\r\n    private baseFuncService: BaseFunctionService,\r\n    private valid: ValidationHelper,\r\n    private message: MessageService,\r\n    private drestoyedRef: DestroyRef\r\n  ) {\r\n    super(allow);\r\n\r\n    this.share.SharedUserLog.subscribe(res => {\r\n      this.userLogs = res;\r\n    });\r\n\r\n    this.share.SharedFunctionModel.subscribe(res => {\r\n      this.functions = res;\r\n      if (res.length === 0) {\r\n        this.getFunction();\r\n      } else {\r\n        this.functions = res;\r\n      }\r\n    });\r\n  }\r\n\r\n  override ngOnInit(): void {\r\n    this.getUserLog();\r\n  }\r\n\r\n  validationDate() {\r\n    this.valid.clear();\r\n    this.valid.Date(\r\n      new Date(this.start),\r\n      new Date(this.end)\r\n    );\r\n  }\r\n\r\n  getUserLog() {\r\n    this.validationDate();\r\n    if (this.valid.errorMessages.length > 0) {\r\n      this.message.showErrorMSGs(this.valid.errorMessages);\r\n      return\r\n    }\r\n\r\n    this.request.PageIndex = this.pageIndex;\r\n    this.request.PageSize = this.pageSize;\r\n\r\n    this.request.DateStart = formatDate(this.start, 'yyyy-MM-ddT00:00:00', 'en');\r\n    this.request.DateEnd = formatDate(this.end, 'yyyy-MM-ddT23:59:59', 'en');\r\n\r\n    let req = {\r\n      body: {\r\n        ...this.request\r\n      }\r\n    }\r\n\r\n    this.userService.apiUserGetUserLogPost$Json(req).pipe(takeUntilDestroyed(this.drestoyedRef)).subscribe(res => {\r\n      this.userLogs = res.Entries!;\r\n      this.totalRecords = res.TotalItems!;\r\n      this.share.SetUserLog(this.userLogs);\r\n    })\r\n  }\r\n\r\n  getFunction() {\r\n    this.baseFuncService.apiBaseFunctionGetFunctionPost$Json({ body: {} }).subscribe(res => {\r\n      this.functions = res.Entries!;\r\n      this.share.SetFunctionModel(this.functions);\r\n    })\r\n  }\r\n\r\n}\r\n", "<nb-card accent=\"success\">\r\n  <nb-card-header>\r\n    <ngx-breadcrumb></ngx-breadcrumb>\r\n  </nb-card-header>\r\n  <nb-card-body class=\"bg-white\">\r\n    <div class=\"col-12\">\r\n      <div class=\"row\">\r\n        <div class=\"form-group col-12 col-md-6\">\r\n          <label for=\"keyWord\" class=\"label mr-2\">日期</label>\r\n\r\n          <input nbInput placeholder=\"起日\" [nbDatepicker]=\"formcontrol\" class=\"mr-2\" [(ngModel)]=\"start\">\r\n          <nb-datepicker #formcontrol></nb-datepicker>\r\n          <label class=\"label mr-2\">~</label>\r\n          <input nbInput placeholder=\"迄日\" [nbDatepicker]=\"ngmodel\" [(ngModel)]=\"end\">\r\n          <nb-datepicker #ngmodel></nb-datepicker>\r\n\r\n        </div>\r\n        <div class=\"form-group col-12 col-md-4\">\r\n          <label for=\"keyWord\" class=\"label mr-2\">操作功能</label>\r\n          <nb-select id=\"FunctionId\" name=\"FunctionId\" [(selected)]=\"request.FunctionId\">\r\n            <nb-option [value]=\"-1\">全部</nb-option>\r\n            <nb-option *ngFor=\"let fun of functions\" [value]=\"fun.Id\"> {{fun.Name}}</nb-option>\r\n          </nb-select>\r\n        </div>\r\n        <div class=\"form-group col-12 col-md-2 text-right\">\r\n          <button class=\"btn btn-info mr-2\" (click)=\"getUserLog()\"><i class=\"fas fa-search mr-1\"></i>查詢</button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </nb-card-body>\r\n  <nb-card-body class=\"bg-white\">\r\n    <div class=\"col-12\">\r\n\r\n      <div class=\"table-responsive\">\r\n        <table class=\"table table-striped border \" style=\"min-width: 800px; background-color:#f3f3f3;\">\r\n          <thead>\r\n            <tr style=\"background-color: #27ae60;\" class=\"d-flex text-white\">\r\n              <th scope=\"col\" class=\"col-1\">序號</th>\r\n              <th scope=\"col\" class=\"col-3\">使用者帳號</th>\r\n              <th scope=\"col\" class=\"col-2\">使用者姓名</th>\r\n              <th scope=\"col\" class=\"col-2\">紀錄時間</th>\r\n              <th scope=\"col\" class=\"col-2\">操作功能</th>\r\n              <th scope=\"col\" class=\"col-2\">登入IP</th>\r\n            </tr>\r\n          </thead>\r\n          <tbody>\r\n            <tr *ngFor=\"let data of userLogs\" class=\"d-flex\">\r\n              <td class=\"col-1\">{{ data.CId }}</td>\r\n              <td class=\"col-3\">{{ data.CAccount }}</td>\r\n              <td class=\"col-2\">{{ data.CName }}</td>\r\n              <td class=\"col-2\">{{ data.DateCreate | localDate: 'yyyy-MM-DD HH:mm:ss' }}</td>\r\n              <td class=\"col-2\">{{ data.FunctionName }}</td>\r\n              <td class=\"col-2\">{{ data.Ip }}</td>\r\n            </tr>\r\n          </tbody>\r\n        </table>\r\n\r\n      </div>\r\n      <ngx-pagination [CollectionSize]=\"totalRecords\" [(Page)]=\"pageIndex\" [PageSize]=\"pageSize\"\r\n        (PageChange)=\"getUserLog()\">\r\n      </ngx-pagination>\r\n\r\n    </div>\r\n  </nb-card-body>\r\n</nb-card>\r\n"], "mappings": "AAAA,SAASA,aAAa,QAAQ,qCAAqC;AAEnE,SAAsBC,WAAW,QAAQ,gBAAgB;AACzD,SAASC,UAAU,QAAQ,mCAAmC;AAC9D,SAASC,KAAK,EAAEC,UAAU,QAAQ,iBAAiB;AACnD,SAASC,YAAY,EAAEC,aAAa,EAAEC,kBAAkB,EAAEC,cAAc,EAAEC,cAAc,QAAQ,gBAAgB;AAIhH,SAASC,mBAAmB,QAAQ,kDAAkD;AACtF,SAASC,mBAAmB,QAAQ,kDAAkD;AAMtF,SAASC,kBAAkB,QAAQ,4BAA4B;;;;;;;;;;;ICKnDC,EAAA,CAAAC,cAAA,oBAA0D;IAACD,EAAA,CAAAE,MAAA,GAAY;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAA1CH,EAAA,CAAAI,UAAA,UAAAC,MAAA,CAAAC,EAAA,CAAgB;IAAEN,EAAA,CAAAO,SAAA,EAAY;IAAZP,EAAA,CAAAQ,kBAAA,MAAAH,MAAA,CAAAI,IAAA,KAAY;;;;;IA0BrET,EADF,CAAAC,cAAA,aAAiD,aAC7B;IAAAD,EAAA,CAAAE,MAAA,GAAc;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACrCH,EAAA,CAAAC,cAAA,aAAkB;IAAAD,EAAA,CAAAE,MAAA,GAAmB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC1CH,EAAA,CAAAC,cAAA,aAAkB;IAAAD,EAAA,CAAAE,MAAA,GAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACvCH,EAAA,CAAAC,cAAA,aAAkB;IAAAD,EAAA,CAAAE,MAAA,GAAwD;;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC/EH,EAAA,CAAAC,cAAA,cAAkB;IAAAD,EAAA,CAAAE,MAAA,IAAuB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC9CH,EAAA,CAAAC,cAAA,cAAkB;IAAAD,EAAA,CAAAE,MAAA,IAAa;IACjCF,EADiC,CAAAG,YAAA,EAAK,EACjC;;;;IANeH,EAAA,CAAAO,SAAA,GAAc;IAAdP,EAAA,CAAAU,iBAAA,CAAAC,OAAA,CAAAC,GAAA,CAAc;IACdZ,EAAA,CAAAO,SAAA,GAAmB;IAAnBP,EAAA,CAAAU,iBAAA,CAAAC,OAAA,CAAAE,QAAA,CAAmB;IACnBb,EAAA,CAAAO,SAAA,GAAgB;IAAhBP,EAAA,CAAAU,iBAAA,CAAAC,OAAA,CAAAG,KAAA,CAAgB;IAChBd,EAAA,CAAAO,SAAA,GAAwD;IAAxDP,EAAA,CAAAU,iBAAA,CAAAV,EAAA,CAAAe,WAAA,OAAAJ,OAAA,CAAAK,UAAA,yBAAwD;IACxDhB,EAAA,CAAAO,SAAA,GAAuB;IAAvBP,EAAA,CAAAU,iBAAA,CAAAC,OAAA,CAAAM,YAAA,CAAuB;IACvBjB,EAAA,CAAAO,SAAA,GAAa;IAAbP,EAAA,CAAAU,iBAAA,CAAAC,OAAA,CAAAO,EAAA,CAAa;;;ADd7C,OAAM,MAAOC,uBAAwB,SAAQhC,aAAa;EAiBxDiC,YACUC,WAAwB,EACxBC,KAAuB,EACZC,KAAkB,EAC7BC,eAAoC,EACpCC,KAAuB,EACvBC,OAAuB,EACvBC,YAAwB;IAEhC,KAAK,CAACJ,KAAK,CAAC;IARJ,KAAAF,WAAW,GAAXA,WAAW;IACX,KAAAC,KAAK,GAALA,KAAK;IACM,KAAAC,KAAK,GAALA,KAAK;IAChB,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAC,YAAY,GAAZA,YAAY;IAtBtB,KAAAC,QAAQ,GAA6B,EAAE;IACvC,KAAAC,SAAS,GAAG,EAAoB;IAEhC,KAAAC,GAAG,GAAG,IAAIC,IAAI,EAAE;IAChB,KAAAC,OAAO,GAAuB;MAC5BC,OAAO,EAAE,EAAE;MACXC,SAAS,EAAE,EAAE;MACbC,SAAS,EAAE,IAAI,CAACC,SAAS;MACzBC,QAAQ,EAAE,IAAI,CAACC,QAAQ;MACvBC,UAAU,EAAE,CAAC;KACd;IACD,KAAAC,KAAK,GAAG,IAAI,CAACV,GAAG;IAChB,KAAAW,GAAG,GAAG,IAAI,CAACX,GAAG;IAcZ,IAAI,CAACR,KAAK,CAACoB,aAAa,CAACC,SAAS,CAACC,GAAG,IAAG;MACvC,IAAI,CAAChB,QAAQ,GAAGgB,GAAG;IACrB,CAAC,CAAC;IAEF,IAAI,CAACtB,KAAK,CAACuB,mBAAmB,CAACF,SAAS,CAACC,GAAG,IAAG;MAC7C,IAAI,CAACf,SAAS,GAAGe,GAAG;MACpB,IAAIA,GAAG,CAACE,MAAM,KAAK,CAAC,EAAE;QACpB,IAAI,CAACC,WAAW,EAAE;MACpB,CAAC,MAAM;QACL,IAAI,CAAClB,SAAS,GAAGe,GAAG;MACtB;IACF,CAAC,CAAC;EACJ;EAESI,QAAQA,CAAA;IACf,IAAI,CAACC,UAAU,EAAE;EACnB;EAEAC,cAAcA,CAAA;IACZ,IAAI,CAACzB,KAAK,CAAC0B,KAAK,EAAE;IAClB,IAAI,CAAC1B,KAAK,CAACM,IAAI,CACb,IAAIA,IAAI,CAAC,IAAI,CAACS,KAAK,CAAC,EACpB,IAAIT,IAAI,CAAC,IAAI,CAACU,GAAG,CAAC,CACnB;EACH;EAEAQ,UAAUA,CAAA;IACR,IAAI,CAACC,cAAc,EAAE;IACrB,IAAI,IAAI,CAACzB,KAAK,CAAC2B,aAAa,CAACN,MAAM,GAAG,CAAC,EAAE;MACvC,IAAI,CAACpB,OAAO,CAAC2B,aAAa,CAAC,IAAI,CAAC5B,KAAK,CAAC2B,aAAa,CAAC;MACpD;IACF;IAEA,IAAI,CAACpB,OAAO,CAACG,SAAS,GAAG,IAAI,CAACC,SAAS;IACvC,IAAI,CAACJ,OAAO,CAACK,QAAQ,GAAG,IAAI,CAACC,QAAQ;IAErC,IAAI,CAACN,OAAO,CAACE,SAAS,GAAG3C,UAAU,CAAC,IAAI,CAACiD,KAAK,EAAE,qBAAqB,EAAE,IAAI,CAAC;IAC5E,IAAI,CAACR,OAAO,CAACC,OAAO,GAAG1C,UAAU,CAAC,IAAI,CAACkD,GAAG,EAAE,qBAAqB,EAAE,IAAI,CAAC;IAExE,IAAIa,GAAG,GAAG;MACRC,IAAI,EAAE;QACJ,GAAG,IAAI,CAACvB;;KAEX;IAED,IAAI,CAACX,WAAW,CAACmC,0BAA0B,CAACF,GAAG,CAAC,CAACG,IAAI,CAAC1D,kBAAkB,CAAC,IAAI,CAAC4B,YAAY,CAAC,CAAC,CAACgB,SAAS,CAACC,GAAG,IAAG;MAC3G,IAAI,CAAChB,QAAQ,GAAGgB,GAAG,CAACc,OAAQ;MAC5B,IAAI,CAACC,YAAY,GAAGf,GAAG,CAACgB,UAAW;MACnC,IAAI,CAACtC,KAAK,CAACuC,UAAU,CAAC,IAAI,CAACjC,QAAQ,CAAC;IACtC,CAAC,CAAC;EACJ;EAEAmB,WAAWA,CAAA;IACT,IAAI,CAACvB,eAAe,CAACsC,mCAAmC,CAAC;MAAEP,IAAI,EAAE;IAAE,CAAE,CAAC,CAACZ,SAAS,CAACC,GAAG,IAAG;MACrF,IAAI,CAACf,SAAS,GAAGe,GAAG,CAACc,OAAQ;MAC7B,IAAI,CAACpC,KAAK,CAACyC,gBAAgB,CAAC,IAAI,CAAClC,SAAS,CAAC;IAC7C,CAAC,CAAC;EACJ;;;uCArFWV,uBAAuB,EAAAnB,EAAA,CAAAgE,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAlE,EAAA,CAAAgE,iBAAA,CAAAG,EAAA,CAAAC,gBAAA,GAAApE,EAAA,CAAAgE,iBAAA,CAAAK,EAAA,CAAAC,WAAA,GAAAtE,EAAA,CAAAgE,iBAAA,CAAAC,EAAA,CAAAM,mBAAA,GAAAvE,EAAA,CAAAgE,iBAAA,CAAAQ,EAAA,CAAAC,gBAAA,GAAAzE,EAAA,CAAAgE,iBAAA,CAAAU,EAAA,CAAAC,cAAA,GAAA3E,EAAA,CAAAgE,iBAAA,CAAAhE,EAAA,CAAA4E,UAAA;IAAA;EAAA;;;YAAvBzD,uBAAuB;MAAA0D,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAA/E,EAAA,CAAAgF,0BAAA,EAAAhF,EAAA,CAAAiF,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,iCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;UCrClCvF,EADF,CAAAC,cAAA,iBAA0B,qBACR;UACdD,EAAA,CAAAyF,SAAA,qBAAiC;UACnCzF,EAAA,CAAAG,YAAA,EAAiB;UAKTH,EAJR,CAAAC,cAAA,sBAA+B,aACT,aACD,aACyB,eACE;UAAAD,EAAA,CAAAE,MAAA,mBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAElDH,EAAA,CAAAC,cAAA,eAA8F;UAApBD,EAAA,CAAA0F,gBAAA,2BAAAC,gEAAAC,MAAA;YAAA5F,EAAA,CAAA6F,aAAA,CAAAC,GAAA;YAAA9F,EAAA,CAAA+F,kBAAA,CAAAP,GAAA,CAAAhD,KAAA,EAAAoD,MAAA,MAAAJ,GAAA,CAAAhD,KAAA,GAAAoD,MAAA;YAAA,OAAA5F,EAAA,CAAAgG,WAAA,CAAAJ,MAAA;UAAA,EAAmB;UAA7F5F,EAAA,CAAAG,YAAA,EAA8F;UAC9FH,EAAA,CAAAyF,SAAA,8BAA4C;UAC5CzF,EAAA,CAAAC,cAAA,gBAA0B;UAAAD,EAAA,CAAAE,MAAA,SAAC;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACnCH,EAAA,CAAAC,cAAA,iBAA2E;UAAlBD,EAAA,CAAA0F,gBAAA,2BAAAO,iEAAAL,MAAA;YAAA5F,EAAA,CAAA6F,aAAA,CAAAC,GAAA;YAAA9F,EAAA,CAAA+F,kBAAA,CAAAP,GAAA,CAAA/C,GAAA,EAAAmD,MAAA,MAAAJ,GAAA,CAAA/C,GAAA,GAAAmD,MAAA;YAAA,OAAA5F,EAAA,CAAAgG,WAAA,CAAAJ,MAAA;UAAA,EAAiB;UAA1E5F,EAAA,CAAAG,YAAA,EAA2E;UAC3EH,EAAA,CAAAyF,SAAA,8BAAwC;UAE1CzF,EAAA,CAAAG,YAAA,EAAM;UAEJH,EADF,CAAAC,cAAA,eAAwC,gBACE;UAAAD,EAAA,CAAAE,MAAA,gCAAI;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACpDH,EAAA,CAAAC,cAAA,qBAA+E;UAAlCD,EAAA,CAAA0F,gBAAA,4BAAAQ,sEAAAN,MAAA;YAAA5F,EAAA,CAAA6F,aAAA,CAAAC,GAAA;YAAA9F,EAAA,CAAA+F,kBAAA,CAAAP,GAAA,CAAAxD,OAAA,CAAAO,UAAA,EAAAqD,MAAA,MAAAJ,GAAA,CAAAxD,OAAA,CAAAO,UAAA,GAAAqD,MAAA;YAAA,OAAA5F,EAAA,CAAAgG,WAAA,CAAAJ,MAAA;UAAA,EAAiC;UAC5E5F,EAAA,CAAAC,cAAA,qBAAwB;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAY;UACtCH,EAAA,CAAAmG,UAAA,KAAAC,6CAAA,wBAA0D;UAE9DpG,EADE,CAAAG,YAAA,EAAY,EACR;UAEJH,EADF,CAAAC,cAAA,eAAmD,kBACQ;UAAvBD,EAAA,CAAAqG,UAAA,mBAAAC,0DAAA;YAAAtG,EAAA,CAAA6F,aAAA,CAAAC,GAAA;YAAA,OAAA9F,EAAA,CAAAgG,WAAA,CAASR,GAAA,CAAAvC,UAAA,EAAY;UAAA,EAAC;UAACjD,EAAA,CAAAyF,SAAA,aAAkC;UAAAzF,EAAA,CAAAE,MAAA,oBAAE;UAIrGF,EAJqG,CAAAG,YAAA,EAAS,EAClG,EACF,EACF,EACO;UAQHH,EAPZ,CAAAC,cAAA,uBAA+B,cACT,eAEY,iBACmE,aACtF,cAC4D,cACjC;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACrCH,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAE,MAAA,sCAAK;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACxCH,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAE,MAAA,sCAAK;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACxCH,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAE,MAAA,gCAAI;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACvCH,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAE,MAAA,gCAAI;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACvCH,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAE,MAAA,sBAAI;UAEtCF,EAFsC,CAAAG,YAAA,EAAK,EACpC,EACC;UACRH,EAAA,CAAAC,cAAA,aAAO;UACLD,EAAA,CAAAmG,UAAA,KAAAI,sCAAA,kBAAiD;UAWvDvG,EAHI,CAAAG,YAAA,EAAQ,EACF,EAEJ;UACNH,EAAA,CAAAC,cAAA,0BAC8B;UADkBD,EAAA,CAAA0F,gBAAA,wBAAAc,uEAAAZ,MAAA;YAAA5F,EAAA,CAAA6F,aAAA,CAAAC,GAAA;YAAA9F,EAAA,CAAA+F,kBAAA,CAAAP,GAAA,CAAApD,SAAA,EAAAwD,MAAA,MAAAJ,GAAA,CAAApD,SAAA,GAAAwD,MAAA;YAAA,OAAA5F,EAAA,CAAAgG,WAAA,CAAAJ,MAAA;UAAA,EAAoB;UAClE5F,EAAA,CAAAqG,UAAA,wBAAAG,uEAAA;YAAAxG,EAAA,CAAA6F,aAAA,CAAAC,GAAA;YAAA,OAAA9F,EAAA,CAAAgG,WAAA,CAAcR,GAAA,CAAAvC,UAAA,EAAY;UAAA,EAAC;UAKnCjD,EAJM,CAAAG,YAAA,EAAiB,EAEb,EACO,EACP;;;;;UAtDgCH,EAAA,CAAAO,SAAA,GAA4B;UAA5BP,EAAA,CAAAI,UAAA,iBAAAqG,cAAA,CAA4B;UAAczG,EAAA,CAAA0G,gBAAA,YAAAlB,GAAA,CAAAhD,KAAA,CAAmB;UAG7DxC,EAAA,CAAAO,SAAA,GAAwB;UAAxBP,EAAA,CAAAI,UAAA,iBAAAuG,UAAA,CAAwB;UAAC3G,EAAA,CAAA0G,gBAAA,YAAAlB,GAAA,CAAA/C,GAAA,CAAiB;UAM7BzC,EAAA,CAAAO,SAAA,GAAiC;UAAjCP,EAAA,CAAA0G,gBAAA,aAAAlB,GAAA,CAAAxD,OAAA,CAAAO,UAAA,CAAiC;UACjEvC,EAAA,CAAAO,SAAA,EAAY;UAAZP,EAAA,CAAAI,UAAA,aAAY;UACIJ,EAAA,CAAAO,SAAA,GAAY;UAAZP,EAAA,CAAAI,UAAA,YAAAoF,GAAA,CAAA3D,SAAA,CAAY;UAyBlB7B,EAAA,CAAAO,SAAA,IAAW;UAAXP,EAAA,CAAAI,UAAA,YAAAoF,GAAA,CAAA5D,QAAA,CAAW;UAYtB5B,EAAA,CAAAO,SAAA,EAA+B;UAA/BP,EAAA,CAAAI,UAAA,mBAAAoF,GAAA,CAAA7B,YAAA,CAA+B;UAAC3D,EAAA,CAAA0G,gBAAA,SAAAlB,GAAA,CAAApD,SAAA,CAAoB;UAACpC,EAAA,CAAAI,UAAA,aAAAoF,GAAA,CAAAlD,QAAA,CAAqB;;;qBDhC5F9C,YAAY,EAAAoH,EAAA,CAAAC,eAAA,EAAAD,EAAA,CAAAE,mBAAA,EAAAF,EAAA,CAAAG,qBAAA,EACZjH,mBAAmB,EACnBL,aAAa,EAAAmH,EAAA,CAAAI,gBAAA,EACbtH,kBAAkB,EAAAkH,EAAA,CAAAK,qBAAA,EAAAL,EAAA,CAAAM,qBAAA,EAClB9H,WAAW,EAAA+H,EAAA,CAAAC,oBAAA,EAAAD,EAAA,CAAAE,eAAA,EAAAF,EAAA,CAAAG,OAAA,EACX3H,cAAc,EAAAiH,EAAA,CAAAW,iBAAA,EAAAX,EAAA,CAAAY,iBAAA,EACd5H,cAAc,EACdN,KAAK,EACLO,mBAAmB,EACnBR,UAAU;MAAAoI,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}