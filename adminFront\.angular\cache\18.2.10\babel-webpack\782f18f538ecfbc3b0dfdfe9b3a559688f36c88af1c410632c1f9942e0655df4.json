{"ast": null, "code": "import { of as observableOf } from 'rxjs';\nimport { StatsBarData } from '../data/stats-bar';\nimport * as i0 from \"@angular/core\";\nexport let StatsBarService = /*#__PURE__*/(() => {\n  class StatsBarService extends StatsBarData {\n    constructor() {\n      super(...arguments);\n      this.statsBarData = [300, 520, 435, 530, 730, 620, 660, 860];\n    }\n    getStatsBarData() {\n      return observableOf(this.statsBarData);\n    }\n    static {\n      this.ɵfac = /*@__PURE__*/(() => {\n        let ɵStatsBarService_BaseFactory;\n        return function StatsBarService_Factory(__ngFactoryType__) {\n          return (ɵStatsBarService_BaseFactory || (ɵStatsBarService_BaseFactory = i0.ɵɵgetInheritedFactory(StatsBarService)))(__ngFactoryType__ || StatsBarService);\n        };\n      })();\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: StatsBarService,\n        factory: StatsBarService.ɵfac\n      });\n    }\n  }\n  return StatsBarService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}