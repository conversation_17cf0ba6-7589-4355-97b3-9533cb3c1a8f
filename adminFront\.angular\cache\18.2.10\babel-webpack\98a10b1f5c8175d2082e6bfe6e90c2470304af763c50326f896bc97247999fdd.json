{"ast": null, "code": "import { of as observableOf } from 'rxjs';\nimport { TrafficListData } from '../data/traffic-list';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"./periods.service\";\nexport let TrafficListService = /*#__PURE__*/(() => {\n  class TrafficListService extends TrafficListData {\n    constructor(period) {\n      super();\n      this.period = period;\n      this.getRandom = roundTo => Math.round(Math.random() * roundTo);\n      this.data = {};\n      this.data = {\n        week: this.getDataWeek(),\n        month: this.getDataMonth(),\n        year: this.getDataYear()\n      };\n    }\n    getDataWeek() {\n      const getFirstDateInPeriod = () => {\n        const weeks = this.period.getWeeks();\n        return weeks[weeks.length - 1];\n      };\n      return this.reduceData(this.period.getWeeks(), getFirstDateInPeriod);\n    }\n    getDataMonth() {\n      const getFirstDateInPeriod = () => {\n        const months = this.period.getMonths();\n        return months[months.length - 1];\n      };\n      return this.reduceData(this.period.getMonths(), getFirstDateInPeriod);\n    }\n    getDataYear() {\n      const getFirstDateInPeriod = () => {\n        const years = this.period.getYears();\n        return `${parseInt(years[0], 10) - 1}`;\n      };\n      return this.reduceData(this.period.getYears(), getFirstDateInPeriod);\n    }\n    reduceData(timePeriods, getFirstDateInPeriod) {\n      return timePeriods.reduce((result, timePeriod, index) => {\n        const hasResult = result[index - 1];\n        const prevDate = hasResult ? result[index - 1].comparison.nextDate : getFirstDateInPeriod();\n        const prevValue = hasResult ? result[index - 1].comparison.nextValue : this.getRandom(100);\n        const nextValue = this.getRandom(100);\n        const deltaValue = prevValue - nextValue;\n        const item = {\n          date: timePeriod,\n          value: this.getRandom(1000),\n          delta: {\n            up: deltaValue <= 0,\n            value: Math.abs(deltaValue)\n          },\n          comparison: {\n            prevDate,\n            prevValue,\n            nextDate: timePeriod,\n            nextValue\n          }\n        };\n        return [...result, item];\n      }, []);\n    }\n    getTrafficListData(period) {\n      return observableOf(this.data[period]);\n    }\n    static {\n      this.ɵfac = function TrafficListService_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || TrafficListService)(i0.ɵɵinject(i1.PeriodsService));\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: TrafficListService,\n        factory: TrafficListService.ɵfac\n      });\n    }\n  }\n  return TrafficListService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}