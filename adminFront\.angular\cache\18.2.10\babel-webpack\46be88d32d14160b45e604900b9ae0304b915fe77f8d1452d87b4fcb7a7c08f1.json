{"ast": null, "code": "import { combineLatestInit } from '../observable/combineLatest';\nimport { operate } from '../util/lift';\nimport { argsOrArgArray } from '../util/argsOrArgArray';\nimport { mapOneOrManyArgs } from '../util/mapOneOrManyArgs';\nimport { pipe } from '../util/pipe';\nimport { popResultSelector } from '../util/args';\nexport function combineLatest(...args) {\n  const resultSelector = popResultSelector(args);\n  return resultSelector ? pipe(combineLatest(...args), mapOneOrManyArgs(resultSelector)) : operate((source, subscriber) => {\n    combineLatestInit([source, ...argsOrArgArray(args)])(subscriber);\n  });\n}\n//# sourceMappingURL=combineLatest.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}