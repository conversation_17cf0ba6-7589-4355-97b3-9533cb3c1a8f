{"ast": null, "code": "import { Observable } from 'rxjs';\nimport { map, switchMap } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../services/api/services/quotation.service\";\nimport * as i2 from \"@angular/common/http\";\nexport class QuotationService {\n  constructor(apiQuotationService, http) {\n    this.apiQuotationService = apiQuotationService;\n    this.http = http;\n    this.apiUrl = '/api/Quotation';\n  }\n  // 取得報價單列表\n  getQuotationList(request) {\n    return this.apiQuotationService.apiQuotationGetListPost$Json({\n      body: request\n    });\n  }\n  // 取得單筆報價單資料\n  getQuotationData(quotationId) {\n    const request = {\n      cQuotationID: quotationId\n    };\n    return this.apiQuotationService.apiQuotationGetDataPost$Json({\n      body: request\n    });\n  }\n  // 取得戶別的報價項目\n  getQuotationByHouseId(houseId) {\n    const request = {\n      cHouseID: houseId\n    };\n    return this.apiQuotationService.apiQuotationGetListByHouseIdPost$Json({\n      body: request\n    });\n  }\n  // 儲存報價單 (支援單一項目)\n  saveQuotationItem(quotation) {\n    return this.apiQuotationService.apiQuotationSaveDataPost$Json({\n      body: quotation\n    });\n  } // 儲存報價單 (支援批量保存多個項目) - 使用單一請求\n  saveQuotation(request) {\n    // 先取得現有的報價資料來處理版本號\n    return this.getQuotationByHouseId(request.houseId).pipe(switchMap(existingResponse => {\n      const existingItems = existingResponse?.StatusCode === 0 && existingResponse?.Entries ? existingResponse.Entries : [];\n      // 建立現有項目的版本對照表\n      const existingVersionMap = new Map();\n      existingItems.forEach(item => {\n        if (item.CQuotationID || item.cQuotationID) {\n          const id = item.CQuotationID || item.cQuotationID;\n          const version = item.CVersion || item.cVersion || 1;\n          existingVersionMap.set(id, version);\n        }\n      });\n      // 準備批次儲存的項目，處理版本號邏輯\n      const saveDataItems = request.items.map(item => {\n        let version = 1; // 新項目預設版本為1\n        // 如果是現有項目，版本號+1\n        if (item.cQuotationID && existingVersionMap.has(item.cQuotationID)) {\n          version = existingVersionMap.get(item.cQuotationID) + 1;\n        }\n        return {\n          cQuotationID: item.cQuotationID || null,\n          cHouseID: request.houseId,\n          cItemName: item.cItemName,\n          cUnitPrice: item.cUnitPrice,\n          cCount: item.cCount,\n          cStatus: item.cStatus || 1,\n          cVersion: version,\n          cIsDeafult: item.cIsDefault || false\n        };\n      });\n      // 使用批次 API 進行單一請求\n      const batchRequest = {\n        items: saveDataItems\n      };\n      // 檢查是否有批次 API，如果沒有則回退到原有方式\n      if (this.apiQuotationService.apiQuotationBatchSaveDataPost$Json) {\n        return this.apiQuotationService.apiQuotationBatchSaveDataPost$Json({\n          body: batchRequest\n        }).pipe(map(response => ({\n          success: response?.StatusCode === 0,\n          message: response?.StatusCode === 0 ? '報價單批次保存成功' : '報價單批次保存失敗',\n          data: request.items\n        })));\n      } else {\n        // 回退到多個單獨請求的方式\n        const saveObservables = saveDataItems.map(saveData => this.apiQuotationService.apiQuotationSaveDataPost$Json({\n          body: saveData\n        }));\n        return new Observable(observer => {\n          Promise.all(saveObservables.map(obs => obs.toPromise())).then(responses => {\n            const allSuccess = responses.every(response => response?.StatusCode === 0);\n            observer.next({\n              success: allSuccess,\n              message: allSuccess ? '報價單保存成功' : '部分項目保存失敗',\n              data: request.items\n            });\n            observer.complete();\n          }).catch(error => {\n            observer.next({\n              success: false,\n              message: '報價單保存失敗',\n              data: []\n            });\n            observer.complete();\n          });\n        });\n      }\n    }));\n  }\n  // 批次儲存報價單 (新方法，使用單一請求)\n  saveBatchQuotation(request) {\n    // 準備批次儲存的項目\n    const saveDataItems = request.items.map(item => ({\n      cQuotationID: item.cQuotationID || null,\n      cHouseID: request.houseId,\n      cItemName: item.cItemName,\n      cUnitPrice: item.cUnitPrice,\n      cCount: item.cCount,\n      cStatus: item.cStatus || 1,\n      cVersion: item.cVersion || 1,\n      cIsDeafult: item.cIsDefault || false\n    }));\n    const batchRequest = {\n      items: saveDataItems\n    };\n    // 使用 HttpClient 直接發送請求，因為可能後端還沒有實作批次 API\n    return this.http.post(`${this.apiQuotationService.rootUrl}/api/Quotation/BatchSaveData`, batchRequest).pipe(map(response => ({\n      success: response?.StatusCode === 0,\n      message: response?.StatusCode === 0 ? '報價單批次保存成功' : '報價單批次保存失敗',\n      data: request.items\n    })));\n  }\n  // 刪除報價單\n  deleteQuotation(quotationId) {\n    const request = {\n      cQuotationID: quotationId\n    };\n    return this.apiQuotationService.apiQuotationDeleteDataPost$Json({\n      body: request\n    });\n  }\n  // 取得預設報價項目 (保持原有方法以兼容現有代碼)\n  getDefaultQuotationItems() {\n    // 使用 GetList 方法獲取預設項目\n    const request = {\n      pageIndex: 0,\n      pageSize: 100\n      // 其他預設參數可能需要根據實際 API 需求調整\n    };\n    return this.apiQuotationService.apiQuotationGetListPost$Json({\n      body: request\n    }).pipe(map(response => {\n      // 轉換 API 響應格式以保持兼容性\n      return {\n        success: response.statusCode === 0,\n        // 假設 statusCode 0 表示成功\n        message: response.message || '',\n        data: response.entries || []\n      };\n    }));\n  } // 載入預設報價項目 (LoadDefaultItems API)\n  loadDefaultItems(request) {\n    // 使用注入的 HttpClient 直接發送請求，但使用 apiQuotationService 的 rootUrl\n    return this.http.post(`${this.apiQuotationService.rootUrl}/api/Quotation/LoadDefaultItems`, request).pipe(map(response => {\n      // 轉換 API 響應格式以保持兼容性\n      return {\n        success: response.StatusCode === 0,\n        // 假設 StatusCode 0 表示成功\n        message: response.Message || '',\n        data: response.Entries || []\n      };\n    }));\n  }\n  // 更新報價項目 (使用 SaveData 方法)\n  updateQuotationItem(quotationId, item) {\n    const saveData = {\n      cQuotationID: quotationId,\n      cHouseID: item.cHouseID,\n      cItemName: item.cItemName,\n      cUnitPrice: item.cUnitPrice,\n      cCount: item.cCount,\n      cStatus: item.cStatus || 1,\n      cVersion: item.cVersion || 1,\n      cIsDeafult: item.cIsDefault\n    };\n    return this.apiQuotationService.apiQuotationSaveDataPost$Json({\n      body: saveData\n    }).pipe(map(response => {\n      return {\n        success: response.StatusCode === 0,\n        // 假設 StatusCode 0 表示成功\n        message: response.Message || '',\n        data: [item] // 包裝為陣列以符合 QuotationResponse 格式\n      };\n    }));\n  }\n  // 刪除報價項目\n  deleteQuotationItem(quotationId) {\n    return this.deleteQuotation(quotationId).pipe(map(response => {\n      return {\n        success: response.StatusCode === 0,\n        // 假設 StatusCode 0 表示成功\n        message: response.Message || '',\n        data: []\n      };\n    }));\n  }\n  // 匯出報價單 (需要另外實作，因為 swagger 中沒有此 API)\n  exportQuotation(houseId) {\n    // 這個方法可能需要使用其他 API 或保持原有實作\n    // 暫時拋出錯誤提示需要實作\n    throw new Error('Export quotation functionality needs to be implemented separately');\n  }\n  static {\n    this.ɵfac = function QuotationService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || QuotationService)(i0.ɵɵinject(i1.QuotationService), i0.ɵɵinject(i2.HttpClient));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: QuotationService,\n      factory: QuotationService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["Observable", "map", "switchMap", "QuotationService", "constructor", "apiQuotationService", "http", "apiUrl", "getQuotationList", "request", "apiQuotationGetListPost$Json", "body", "getQuotationData", "quotationId", "cQuotationID", "apiQuotationGetDataPost$Json", "getQuotationByHouseId", "houseId", "cHouseID", "apiQuotationGetListByHouseIdPost$Json", "saveQuotationItem", "quotation", "apiQuotationSaveDataPost$Json", "saveQuotation", "pipe", "existingResponse", "existingItems", "StatusCode", "Entries", "existingVersionMap", "Map", "for<PERSON>ach", "item", "CQuotationID", "id", "version", "CVersion", "cVersion", "set", "saveDataItems", "items", "has", "get", "cItemName", "cUnitPrice", "cCount", "cStatus", "cIs<PERSON><PERSON><PERSON>t", "cIsDefault", "batchRequest", "apiQuotationBatchSaveDataPost$Json", "response", "success", "message", "data", "saveObservables", "saveData", "observer", "Promise", "all", "obs", "to<PERSON>romise", "then", "responses", "allSuccess", "every", "next", "complete", "catch", "error", "saveBatchQuotation", "post", "rootUrl", "deleteQuotation", "apiQuotationDeleteDataPost$Json", "getDefaultQuotationItems", "pageIndex", "pageSize", "statusCode", "entries", "loadDefaultItems", "Message", "updateQuotationItem", "deleteQuotationItem", "exportQuotation", "Error", "i0", "ɵɵinject", "i1", "i2", "HttpClient", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\services\\quotation.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport { Observable } from 'rxjs';\r\nimport { map, switchMap } from 'rxjs/operators';\r\nimport { HttpClient } from '@angular/common/http';\r\nimport { QuotationItem, QuotationRequest, QuotationResponse } from '../models/quotation.model';\r\nimport { QuotationService as ApiQuotationService } from '../../services/api/services/quotation.service';\r\nimport {\r\n  GetListQuotationRequest,\r\n  GetQuotationByIdRequest,\r\n  SaveDataQuotation,\r\n  QuotationItemModel,\r\n  BatchSaveDataQuotation,\r\n  DeleteQuotationRequest,\r\n  GetListByHouseIdRequest,\r\n  LoadDefaultItemsRequest\r\n} from '../../services/api/models';\r\n\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class QuotationService {\r\n  private readonly apiUrl = '/api/Quotation';\r\n\r\n  constructor(\r\n    private apiQuotationService: ApiQuotationService,\r\n    private http: HttpClient\r\n  ) { }\r\n  // 取得報價單列表\r\n  getQuotationList(request: GetListQuotationRequest): Observable<any> {\r\n    return this.apiQuotationService.apiQuotationGetListPost$Json({ body: request });\r\n  }\r\n  // 取得單筆報價單資料\r\n  getQuotationData(quotationId: number): Observable<any> {\r\n    const request: GetQuotationByIdRequest = { cQuotationID: quotationId };\r\n    return this.apiQuotationService.apiQuotationGetDataPost$Json({ body: request });\r\n  }\r\n\r\n  // 取得戶別的報價項目\r\n  getQuotationByHouseId(houseId: number): Observable<any> {\r\n    const request: GetListByHouseIdRequest = { cHouseID: houseId };\r\n    return this.apiQuotationService.apiQuotationGetListByHouseIdPost$Json({ body: request });\r\n  }\r\n  // 儲存報價單 (支援單一項目)\r\n  saveQuotationItem(quotation: SaveDataQuotation): Observable<any> {\r\n    return this.apiQuotationService.apiQuotationSaveDataPost$Json({ body: quotation });\r\n  }  // 儲存報價單 (支援批量保存多個項目) - 使用單一請求\r\n  saveQuotation(request: { houseId: number; items: QuotationItem[] }): Observable<QuotationResponse> {\r\n    // 先取得現有的報價資料來處理版本號\r\n    return this.getQuotationByHouseId(request.houseId).pipe(\r\n      switchMap(existingResponse => {\r\n        const existingItems = existingResponse?.StatusCode === 0 && existingResponse?.Entries\r\n          ? existingResponse.Entries : [];\r\n\r\n        // 建立現有項目的版本對照表\r\n        const existingVersionMap = new Map<number, number>();\r\n        existingItems.forEach((item: any) => {\r\n          if (item.CQuotationID || item.cQuotationID) {\r\n            const id = item.CQuotationID || item.cQuotationID;\r\n            const version = item.CVersion || item.cVersion || 1;\r\n            existingVersionMap.set(id, version);\r\n          }\r\n        });\r\n\r\n        // 準備批次儲存的項目，處理版本號邏輯\r\n        const saveDataItems: SaveDataQuotation[] = request.items.map(item => {\r\n          let version = 1; // 新項目預設版本為1\r\n\r\n          // 如果是現有項目，版本號+1\r\n          if (item.cQuotationID && existingVersionMap.has(item.cQuotationID)) {\r\n            version = existingVersionMap.get(item.cQuotationID)! + 1;\r\n          }\r\n\r\n          return {\r\n            cQuotationID: item.cQuotationID || null,\r\n            cHouseID: request.houseId,\r\n            cItemName: item.cItemName,\r\n            cUnitPrice: item.cUnitPrice,\r\n            cCount: item.cCount,\r\n            cStatus: item.cStatus || 1,\r\n            cVersion: version,\r\n            cIsDeafult: item.cIsDefault || false,\r\n          };\r\n        });\r\n\r\n        // 使用批次 API 進行單一請求\r\n        const batchRequest: BatchSaveDataQuotation = {\r\n          items: saveDataItems\r\n        };\r\n\r\n        // 檢查是否有批次 API，如果沒有則回退到原有方式\r\n        if (this.apiQuotationService.apiQuotationBatchSaveDataPost$Json) {\r\n          return this.apiQuotationService.apiQuotationBatchSaveDataPost$Json({ body: batchRequest }).pipe(\r\n            map(response => ({\r\n              success: response?.StatusCode === 0,\r\n              message: response?.StatusCode === 0 ? '報價單批次保存成功' : '報價單批次保存失敗',\r\n              data: request.items\r\n            } as QuotationResponse))\r\n          );\r\n        } else {\r\n          // 回退到多個單獨請求的方式\r\n          const saveObservables = saveDataItems.map(saveData =>\r\n            this.apiQuotationService.apiQuotationSaveDataPost$Json({ body: saveData })\r\n          );\r\n\r\n          return new Observable<QuotationResponse>(observer => {\r\n            Promise.all(saveObservables.map(obs => obs.toPromise()))\r\n              .then(responses => {\r\n                const allSuccess = responses.every(response => response?.StatusCode === 0);\r\n                observer.next({\r\n                  success: allSuccess,\r\n                  message: allSuccess ? '報價單保存成功' : '部分項目保存失敗',\r\n                  data: request.items\r\n                } as QuotationResponse);\r\n                observer.complete();\r\n              })\r\n              .catch(error => {\r\n                observer.next({\r\n                  success: false,\r\n                  message: '報價單保存失敗',\r\n                  data: []\r\n                } as QuotationResponse);\r\n                observer.complete();\r\n              });\r\n          });\r\n        }\r\n      })\r\n    );\r\n  }\r\n\r\n  // 批次儲存報價單 (新方法，使用單一請求)\r\n  saveBatchQuotation(request: { houseId: number; items: QuotationItem[] }): Observable<QuotationResponse> {\r\n    // 準備批次儲存的項目\r\n    const saveDataItems: SaveDataQuotation[] = request.items.map(item => ({\r\n      cQuotationID: item.cQuotationID || null,\r\n      cHouseID: request.houseId,\r\n      cItemName: item.cItemName,\r\n      cUnitPrice: item.cUnitPrice,\r\n      cCount: item.cCount,\r\n      cStatus: item.cStatus || 1,\r\n      cVersion: item.cVersion || 1,\r\n      cIsDeafult: item.cIsDefault || false,\r\n    }));\r\n\r\n    const batchRequest: BatchSaveDataQuotation = {\r\n      items: saveDataItems\r\n    };\r\n\r\n    // 使用 HttpClient 直接發送請求，因為可能後端還沒有實作批次 API\r\n    return this.http.post<any>(\r\n      `${this.apiQuotationService.rootUrl}/api/Quotation/BatchSaveData`,\r\n      batchRequest\r\n    ).pipe(\r\n      map(response => ({\r\n        success: response?.StatusCode === 0,\r\n        message: response?.StatusCode === 0 ? '報價單批次保存成功' : '報價單批次保存失敗',\r\n        data: request.items\r\n      } as QuotationResponse))\r\n    );\r\n  }\r\n\r\n  // 刪除報價單\r\n  deleteQuotation(quotationId: number): Observable<any> {\r\n    const request: DeleteQuotationRequest = { cQuotationID: quotationId };\r\n    return this.apiQuotationService.apiQuotationDeleteDataPost$Json({ body: request });\r\n  }\r\n  // 取得預設報價項目 (保持原有方法以兼容現有代碼)\r\n  getDefaultQuotationItems(): Observable<QuotationResponse> {\r\n    // 使用 GetList 方法獲取預設項目\r\n    const request: GetListQuotationRequest = {\r\n      pageIndex: 0,\r\n      pageSize: 100,\r\n      // 其他預設參數可能需要根據實際 API 需求調整\r\n    };\r\n    return this.apiQuotationService.apiQuotationGetListPost$Json({ body: request }).pipe(\r\n      map(response => {\r\n        // 轉換 API 響應格式以保持兼容性\r\n        return {\r\n          success: response.statusCode === 0, // 假設 statusCode 0 表示成功\r\n          message: response.message || '',\r\n          data: response.entries || []\r\n        } as QuotationResponse;\r\n      })\r\n    );\r\n  }  // 載入預設報價項目 (LoadDefaultItems API)\r\n  loadDefaultItems(request: LoadDefaultItemsRequest): Observable<QuotationResponse> {\r\n    // 使用注入的 HttpClient 直接發送請求，但使用 apiQuotationService 的 rootUrl\r\n    return this.http.post<any>(\r\n      `${this.apiQuotationService.rootUrl}/api/Quotation/LoadDefaultItems`,\r\n      request\r\n    ).pipe(\r\n      map(response => {\r\n        // 轉換 API 響應格式以保持兼容性\r\n        return {\r\n          success: response.StatusCode === 0, // 假設 StatusCode 0 表示成功\r\n          message: response.Message || '',\r\n          data: response.Entries || []\r\n        } as QuotationResponse;\r\n      })\r\n    );\r\n  }\r\n  // 更新報價項目 (使用 SaveData 方法)\r\n  updateQuotationItem(quotationId: number, item: QuotationItem): Observable<QuotationResponse> {\r\n    const saveData: SaveDataQuotation = {\r\n      cQuotationID: quotationId,\r\n      cHouseID: item.cHouseID,\r\n      cItemName: item.cItemName,\r\n      cUnitPrice: item.cUnitPrice,\r\n      cCount: item.cCount,\r\n      cStatus: item.cStatus || 1,\r\n      cVersion: item.cVersion || 1,\r\n      cIsDeafult: item.cIsDefault,\r\n    };\r\n    return this.apiQuotationService.apiQuotationSaveDataPost$Json({ body: saveData }).pipe(\r\n      map(response => {\r\n        return {\r\n          success: response.StatusCode === 0, // 假設 StatusCode 0 表示成功\r\n          message: response.Message || '',\r\n          data: [item] // 包裝為陣列以符合 QuotationResponse 格式\r\n        } as QuotationResponse;\r\n      })\r\n    );\r\n  }\r\n\r\n  // 刪除報價項目\r\n  deleteQuotationItem(quotationId: number): Observable<QuotationResponse> {\r\n    return this.deleteQuotation(quotationId).pipe(\r\n      map(response => {\r\n        return {\r\n          success: response.StatusCode === 0, // 假設 StatusCode 0 表示成功\r\n          message: response.Message || '',\r\n          data: []\r\n        } as QuotationResponse;\r\n      })\r\n    );\r\n  }\r\n\r\n  // 匯出報價單 (需要另外實作，因為 swagger 中沒有此 API)\r\n  exportQuotation(houseId: number): Observable<Blob> {\r\n    // 這個方法可能需要使用其他 API 或保持原有實作\r\n    // 暫時拋出錯誤提示需要實作\r\n    throw new Error('Export quotation functionality needs to be implemented separately');\r\n  }\r\n}\r\n"], "mappings": "AACA,SAASA,UAAU,QAAQ,MAAM;AACjC,SAASC,GAAG,EAAEC,SAAS,QAAQ,gBAAgB;;;;AAkB/C,OAAM,MAAOC,gBAAgB;EAG3BC,YACUC,mBAAwC,EACxCC,IAAgB;IADhB,KAAAD,mBAAmB,GAAnBA,mBAAmB;IACnB,KAAAC,IAAI,GAAJA,IAAI;IAJG,KAAAC,MAAM,GAAG,gBAAgB;EAKtC;EACJ;EACAC,gBAAgBA,CAACC,OAAgC;IAC/C,OAAO,IAAI,CAACJ,mBAAmB,CAACK,4BAA4B,CAAC;MAAEC,IAAI,EAAEF;IAAO,CAAE,CAAC;EACjF;EACA;EACAG,gBAAgBA,CAACC,WAAmB;IAClC,MAAMJ,OAAO,GAA4B;MAAEK,YAAY,EAAED;IAAW,CAAE;IACtE,OAAO,IAAI,CAACR,mBAAmB,CAACU,4BAA4B,CAAC;MAAEJ,IAAI,EAAEF;IAAO,CAAE,CAAC;EACjF;EAEA;EACAO,qBAAqBA,CAACC,OAAe;IACnC,MAAMR,OAAO,GAA4B;MAAES,QAAQ,EAAED;IAAO,CAAE;IAC9D,OAAO,IAAI,CAACZ,mBAAmB,CAACc,qCAAqC,CAAC;MAAER,IAAI,EAAEF;IAAO,CAAE,CAAC;EAC1F;EACA;EACAW,iBAAiBA,CAACC,SAA4B;IAC5C,OAAO,IAAI,CAAChB,mBAAmB,CAACiB,6BAA6B,CAAC;MAAEX,IAAI,EAAEU;IAAS,CAAE,CAAC;EACpF,CAAC,CAAE;EACHE,aAAaA,CAACd,OAAoD;IAChE;IACA,OAAO,IAAI,CAACO,qBAAqB,CAACP,OAAO,CAACQ,OAAO,CAAC,CAACO,IAAI,CACrDtB,SAAS,CAACuB,gBAAgB,IAAG;MAC3B,MAAMC,aAAa,GAAGD,gBAAgB,EAAEE,UAAU,KAAK,CAAC,IAAIF,gBAAgB,EAAEG,OAAO,GACjFH,gBAAgB,CAACG,OAAO,GAAG,EAAE;MAEjC;MACA,MAAMC,kBAAkB,GAAG,IAAIC,GAAG,EAAkB;MACpDJ,aAAa,CAACK,OAAO,CAAEC,IAAS,IAAI;QAClC,IAAIA,IAAI,CAACC,YAAY,IAAID,IAAI,CAAClB,YAAY,EAAE;UAC1C,MAAMoB,EAAE,GAAGF,IAAI,CAACC,YAAY,IAAID,IAAI,CAAClB,YAAY;UACjD,MAAMqB,OAAO,GAAGH,IAAI,CAACI,QAAQ,IAAIJ,IAAI,CAACK,QAAQ,IAAI,CAAC;UACnDR,kBAAkB,CAACS,GAAG,CAACJ,EAAE,EAAEC,OAAO,CAAC;QACrC;MACF,CAAC,CAAC;MAEF;MACA,MAAMI,aAAa,GAAwB9B,OAAO,CAAC+B,KAAK,CAACvC,GAAG,CAAC+B,IAAI,IAAG;QAClE,IAAIG,OAAO,GAAG,CAAC,CAAC,CAAC;QAEjB;QACA,IAAIH,IAAI,CAAClB,YAAY,IAAIe,kBAAkB,CAACY,GAAG,CAACT,IAAI,CAAClB,YAAY,CAAC,EAAE;UAClEqB,OAAO,GAAGN,kBAAkB,CAACa,GAAG,CAACV,IAAI,CAAClB,YAAY,CAAE,GAAG,CAAC;QAC1D;QAEA,OAAO;UACLA,YAAY,EAAEkB,IAAI,CAAClB,YAAY,IAAI,IAAI;UACvCI,QAAQ,EAAET,OAAO,CAACQ,OAAO;UACzB0B,SAAS,EAAEX,IAAI,CAACW,SAAS;UACzBC,UAAU,EAAEZ,IAAI,CAACY,UAAU;UAC3BC,MAAM,EAAEb,IAAI,CAACa,MAAM;UACnBC,OAAO,EAAEd,IAAI,CAACc,OAAO,IAAI,CAAC;UAC1BT,QAAQ,EAAEF,OAAO;UACjBY,UAAU,EAAEf,IAAI,CAACgB,UAAU,IAAI;SAChC;MACH,CAAC,CAAC;MAEF;MACA,MAAMC,YAAY,GAA2B;QAC3CT,KAAK,EAAED;OACR;MAED;MACA,IAAI,IAAI,CAAClC,mBAAmB,CAAC6C,kCAAkC,EAAE;QAC/D,OAAO,IAAI,CAAC7C,mBAAmB,CAAC6C,kCAAkC,CAAC;UAAEvC,IAAI,EAAEsC;QAAY,CAAE,CAAC,CAACzB,IAAI,CAC7FvB,GAAG,CAACkD,QAAQ,KAAK;UACfC,OAAO,EAAED,QAAQ,EAAExB,UAAU,KAAK,CAAC;UACnC0B,OAAO,EAAEF,QAAQ,EAAExB,UAAU,KAAK,CAAC,GAAG,WAAW,GAAG,WAAW;UAC/D2B,IAAI,EAAE7C,OAAO,CAAC+B;SACO,EAAC,CACzB;MACH,CAAC,MAAM;QACL;QACA,MAAMe,eAAe,GAAGhB,aAAa,CAACtC,GAAG,CAACuD,QAAQ,IAChD,IAAI,CAACnD,mBAAmB,CAACiB,6BAA6B,CAAC;UAAEX,IAAI,EAAE6C;QAAQ,CAAE,CAAC,CAC3E;QAED,OAAO,IAAIxD,UAAU,CAAoByD,QAAQ,IAAG;UAClDC,OAAO,CAACC,GAAG,CAACJ,eAAe,CAACtD,GAAG,CAAC2D,GAAG,IAAIA,GAAG,CAACC,SAAS,EAAE,CAAC,CAAC,CACrDC,IAAI,CAACC,SAAS,IAAG;YAChB,MAAMC,UAAU,GAAGD,SAAS,CAACE,KAAK,CAACd,QAAQ,IAAIA,QAAQ,EAAExB,UAAU,KAAK,CAAC,CAAC;YAC1E8B,QAAQ,CAACS,IAAI,CAAC;cACZd,OAAO,EAAEY,UAAU;cACnBX,OAAO,EAAEW,UAAU,GAAG,SAAS,GAAG,UAAU;cAC5CV,IAAI,EAAE7C,OAAO,CAAC+B;aACM,CAAC;YACvBiB,QAAQ,CAACU,QAAQ,EAAE;UACrB,CAAC,CAAC,CACDC,KAAK,CAACC,KAAK,IAAG;YACbZ,QAAQ,CAACS,IAAI,CAAC;cACZd,OAAO,EAAE,KAAK;cACdC,OAAO,EAAE,SAAS;cAClBC,IAAI,EAAE;aACc,CAAC;YACvBG,QAAQ,CAACU,QAAQ,EAAE;UACrB,CAAC,CAAC;QACN,CAAC,CAAC;MACJ;IACF,CAAC,CAAC,CACH;EACH;EAEA;EACAG,kBAAkBA,CAAC7D,OAAoD;IACrE;IACA,MAAM8B,aAAa,GAAwB9B,OAAO,CAAC+B,KAAK,CAACvC,GAAG,CAAC+B,IAAI,KAAK;MACpElB,YAAY,EAAEkB,IAAI,CAAClB,YAAY,IAAI,IAAI;MACvCI,QAAQ,EAAET,OAAO,CAACQ,OAAO;MACzB0B,SAAS,EAAEX,IAAI,CAACW,SAAS;MACzBC,UAAU,EAAEZ,IAAI,CAACY,UAAU;MAC3BC,MAAM,EAAEb,IAAI,CAACa,MAAM;MACnBC,OAAO,EAAEd,IAAI,CAACc,OAAO,IAAI,CAAC;MAC1BT,QAAQ,EAAEL,IAAI,CAACK,QAAQ,IAAI,CAAC;MAC5BU,UAAU,EAAEf,IAAI,CAACgB,UAAU,IAAI;KAChC,CAAC,CAAC;IAEH,MAAMC,YAAY,GAA2B;MAC3CT,KAAK,EAAED;KACR;IAED;IACA,OAAO,IAAI,CAACjC,IAAI,CAACiE,IAAI,CACnB,GAAG,IAAI,CAAClE,mBAAmB,CAACmE,OAAO,8BAA8B,EACjEvB,YAAY,CACb,CAACzB,IAAI,CACJvB,GAAG,CAACkD,QAAQ,KAAK;MACfC,OAAO,EAAED,QAAQ,EAAExB,UAAU,KAAK,CAAC;MACnC0B,OAAO,EAAEF,QAAQ,EAAExB,UAAU,KAAK,CAAC,GAAG,WAAW,GAAG,WAAW;MAC/D2B,IAAI,EAAE7C,OAAO,CAAC+B;KACO,EAAC,CACzB;EACH;EAEA;EACAiC,eAAeA,CAAC5D,WAAmB;IACjC,MAAMJ,OAAO,GAA2B;MAAEK,YAAY,EAAED;IAAW,CAAE;IACrE,OAAO,IAAI,CAACR,mBAAmB,CAACqE,+BAA+B,CAAC;MAAE/D,IAAI,EAAEF;IAAO,CAAE,CAAC;EACpF;EACA;EACAkE,wBAAwBA,CAAA;IACtB;IACA,MAAMlE,OAAO,GAA4B;MACvCmE,SAAS,EAAE,CAAC;MACZC,QAAQ,EAAE;MACV;KACD;IACD,OAAO,IAAI,CAACxE,mBAAmB,CAACK,4BAA4B,CAAC;MAAEC,IAAI,EAAEF;IAAO,CAAE,CAAC,CAACe,IAAI,CAClFvB,GAAG,CAACkD,QAAQ,IAAG;MACb;MACA,OAAO;QACLC,OAAO,EAAED,QAAQ,CAAC2B,UAAU,KAAK,CAAC;QAAE;QACpCzB,OAAO,EAAEF,QAAQ,CAACE,OAAO,IAAI,EAAE;QAC/BC,IAAI,EAAEH,QAAQ,CAAC4B,OAAO,IAAI;OACN;IACxB,CAAC,CAAC,CACH;EACH,CAAC,CAAE;EACHC,gBAAgBA,CAACvE,OAAgC;IAC/C;IACA,OAAO,IAAI,CAACH,IAAI,CAACiE,IAAI,CACnB,GAAG,IAAI,CAAClE,mBAAmB,CAACmE,OAAO,iCAAiC,EACpE/D,OAAO,CACR,CAACe,IAAI,CACJvB,GAAG,CAACkD,QAAQ,IAAG;MACb;MACA,OAAO;QACLC,OAAO,EAAED,QAAQ,CAACxB,UAAU,KAAK,CAAC;QAAE;QACpC0B,OAAO,EAAEF,QAAQ,CAAC8B,OAAO,IAAI,EAAE;QAC/B3B,IAAI,EAAEH,QAAQ,CAACvB,OAAO,IAAI;OACN;IACxB,CAAC,CAAC,CACH;EACH;EACA;EACAsD,mBAAmBA,CAACrE,WAAmB,EAAEmB,IAAmB;IAC1D,MAAMwB,QAAQ,GAAsB;MAClC1C,YAAY,EAAED,WAAW;MACzBK,QAAQ,EAAEc,IAAI,CAACd,QAAQ;MACvByB,SAAS,EAAEX,IAAI,CAACW,SAAS;MACzBC,UAAU,EAAEZ,IAAI,CAACY,UAAU;MAC3BC,MAAM,EAAEb,IAAI,CAACa,MAAM;MACnBC,OAAO,EAAEd,IAAI,CAACc,OAAO,IAAI,CAAC;MAC1BT,QAAQ,EAAEL,IAAI,CAACK,QAAQ,IAAI,CAAC;MAC5BU,UAAU,EAAEf,IAAI,CAACgB;KAClB;IACD,OAAO,IAAI,CAAC3C,mBAAmB,CAACiB,6BAA6B,CAAC;MAAEX,IAAI,EAAE6C;IAAQ,CAAE,CAAC,CAAChC,IAAI,CACpFvB,GAAG,CAACkD,QAAQ,IAAG;MACb,OAAO;QACLC,OAAO,EAAED,QAAQ,CAACxB,UAAU,KAAK,CAAC;QAAE;QACpC0B,OAAO,EAAEF,QAAQ,CAAC8B,OAAO,IAAI,EAAE;QAC/B3B,IAAI,EAAE,CAACtB,IAAI,CAAC,CAAC;OACO;IACxB,CAAC,CAAC,CACH;EACH;EAEA;EACAmD,mBAAmBA,CAACtE,WAAmB;IACrC,OAAO,IAAI,CAAC4D,eAAe,CAAC5D,WAAW,CAAC,CAACW,IAAI,CAC3CvB,GAAG,CAACkD,QAAQ,IAAG;MACb,OAAO;QACLC,OAAO,EAAED,QAAQ,CAACxB,UAAU,KAAK,CAAC;QAAE;QACpC0B,OAAO,EAAEF,QAAQ,CAAC8B,OAAO,IAAI,EAAE;QAC/B3B,IAAI,EAAE;OACc;IACxB,CAAC,CAAC,CACH;EACH;EAEA;EACA8B,eAAeA,CAACnE,OAAe;IAC7B;IACA;IACA,MAAM,IAAIoE,KAAK,CAAC,mEAAmE,CAAC;EACtF;;;uCA7NWlF,gBAAgB,EAAAmF,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAArF,gBAAA,GAAAmF,EAAA,CAAAC,QAAA,CAAAE,EAAA,CAAAC,UAAA;IAAA;EAAA;;;aAAhBvF,gBAAgB;MAAAwF,OAAA,EAAhBxF,gBAAgB,CAAAyF,IAAA;MAAAC,UAAA,EAFf;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}