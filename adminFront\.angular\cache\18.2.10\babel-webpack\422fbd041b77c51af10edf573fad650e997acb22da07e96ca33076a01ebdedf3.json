{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/Documents/salechange-product/adminFront/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { __decorate } from \"tslib\";\nimport { Component } from '@angular/core';\nimport { NbCardModule, NbCheckboxModule, NbInputModule, NbOptionModule, NbSelectModule, NbTabsetModule } from '@nebular/theme';\nimport { BaseComponent } from '../components/base/baseComponent';\nimport { takeUntilDestroyed } from '@angular/core/rxjs-interop';\nimport { BreadcrumbComponent } from '../components/breadcrumb/breadcrumb.component';\nimport { FormsModule } from '@angular/forms';\nimport { NgFor, NgIf } from '@angular/common';\nimport { PaginationComponent } from '../components/pagination/pagination.component';\nimport { StatusPipe } from 'src/app/@theme/pipes/mapping.pipe';\nimport { FormGroupComponent } from 'src/app/@theme/components/form-group/form-group.component';\nimport { NumberWithCommasPipe } from 'src/app/@theme/pipes/number-with-commas.pipe';\nimport { EnumHouseType } from 'src/app/shared/enum/enumHouseType';\nimport { TemplateViewerComponent } from 'src/app/shared/components/template-viewer/template-viewer.component';\nlet RequirementManagementComponent = class RequirementManagementComponent extends BaseComponent {\n  constructor(_allow, enumHelper, dialogService, message, valid, buildCaseService, requirementService, pettern, router, destroyref) {\n    super(_allow);\n    this._allow = _allow;\n    this.enumHelper = enumHelper;\n    this.dialogService = dialogService;\n    this.message = message;\n    this.valid = valid;\n    this.buildCaseService = buildCaseService;\n    this.requirementService = requirementService;\n    this.pettern = pettern;\n    this.router = router;\n    this.destroyref = destroyref;\n    // request\n    this.getListRequirementRequest = {};\n    this.getRequirementRequest = {};\n    // response\n    this.buildCaseList = [];\n    this.requirementList = [];\n    this.saveRequirement = {\n      CHouseType: []\n    };\n    this.statusOptions = [{\n      value: 0,\n      label: '停用'\n    }, {\n      value: 1,\n      label: '啟用'\n    }];\n    this.houseType = this.enumHelper.getEnumOptions(EnumHouseType);\n    this.isNew = false;\n    this.currentBuildCase = 0;\n    this.currentTab = 0; // 追蹤當前選中的 tab\n    // 模板資料\n    this.templateList = [{\n      name: '模板A',\n      description: '範例模板A'\n    }, {\n      name: '模板B',\n      description: '範例模板B'\n    }];\n    this.showTemplateViewer = false;\n    // Tab 切換事件處理\n    this.isFirstTabChange = true;\n    this.initializeSearchForm();\n    this.getBuildCaseList();\n  }\n  ngOnInit() {}\n  // 初始化搜尋表單\n  initializeSearchForm() {\n    // this.getListRequirementRequest.CBuildCaseID = -1; // <-- 移除此行，避免預設為 -1\n    this.getListRequirementRequest.CStatus = -1;\n    this.getListRequirementRequest.CIsShow = null;\n    this.getListRequirementRequest.CRequirement = '';\n    this.getListRequirementRequest.CGroupName = '';\n    // 預設全選所有房屋類型\n    this.getListRequirementRequest.CHouseType = this.houseType.map(type => type.value);\n  }\n  // 重置搜尋\n  resetSearch() {\n    this.initializeSearchForm();\n    // 重置後如果有建案資料，重新設定預設選擇第一個建案\n    if (this.buildCaseList && this.buildCaseList.length > 0) {\n      setTimeout(() => {\n        if (this.currentTab === 0) {\n          this.getListRequirementRequest.CBuildCaseID = this.buildCaseList[0].cID;\n        } else {\n          this.getListRequirementRequest.CBuildCaseID = 0;\n        }\n        this.getList();\n      }, 0);\n    } else {\n      this.getList();\n    }\n  }\n  getHouseType(hTypes) {\n    if (!hTypes) {\n      return '';\n    }\n    if (!Array.isArray(hTypes)) {\n      hTypes = [hTypes];\n    }\n    let labels = [];\n    hTypes.forEach(htype => {\n      let findH = this.houseType.find(x => x.value == htype);\n      if (findH) {\n        labels.push(findH.label);\n      }\n    });\n    return labels.join(', ');\n  }\n  validation() {\n    this.valid.clear();\n    // 根據當前 tab 決定是否需要驗證建案名稱\n    if (this.currentTab === 0) {\n      // 建案頁面需要驗證建案名稱\n      this.valid.required('[建案名稱]', this.saveRequirement.CBuildCaseID);\n    }\n    this.valid.required('[需求]', this.saveRequirement.CRequirement);\n    this.valid.required('[地主戶]', this.saveRequirement.CHouseType);\n    this.valid.required('[安排]', this.saveRequirement.CSort);\n    this.valid.required('[狀態]', this.saveRequirement.CStatus);\n    this.valid.required('[單價]', this.saveRequirement.CUnitPrice);\n    this.valid.required('[單位]', this.saveRequirement.CUnit);\n    // 群組名稱長度驗證\n    if (this.saveRequirement.CGroupName && this.saveRequirement.CGroupName.length > 20) {\n      this.valid.errorMessages.push('[群組名稱] 不能超過20個字');\n    }\n    // 備註說明長度驗證\n    if (this.saveRequirement.CRemark && this.saveRequirement.CRemark.length > 100) {\n      this.valid.errorMessages.push('[備註說明] 不能超過100個字');\n    }\n  }\n  add(dialog) {\n    this.isNew = true;\n    this.saveRequirement = {\n      CHouseType: [],\n      CIsShow: false\n    };\n    this.saveRequirement.CStatus = 1;\n    this.saveRequirement.CUnitPrice = 0;\n    // 根據當前 tab 決定是否需要建案ID\n    if (this.currentTab === 0) {\n      // 建案頁面 - 使用當前選擇的建案或第一個建案\n      if (this.currentBuildCase != 0) {\n        this.saveRequirement.CBuildCaseID = this.currentBuildCase;\n      } else if (this.buildCaseList && this.buildCaseList.length > 0) {\n        this.saveRequirement.CBuildCaseID = this.buildCaseList[0].cID;\n      }\n    } else {\n      // 模板頁面 - 設定建案ID為0\n      this.saveRequirement.CBuildCaseID = 0;\n    }\n    this.dialogService.open(dialog);\n  }\n  onEdit(data, dialog) {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      _this.getRequirementRequest.CRequirementID = data.CRequirementID;\n      _this.isNew = false;\n      try {\n        yield _this.getData();\n        _this.dialogService.open(dialog);\n      } catch (error) {\n        console.log(\"Failed to get function data\", error);\n      }\n    })();\n  }\n  save(ref) {\n    this.validation();\n    if (this.valid.errorMessages.length > 0) {\n      this.message.showErrorMSGs(this.valid.errorMessages);\n      return;\n    }\n    // 如果是模板頁面，確保 CBuildCaseID 設定為 0\n    if (this.currentTab === 1) {\n      this.saveRequirement.CBuildCaseID = 0;\n    }\n    this.requirementService.apiRequirementSaveDataPost$Json({\n      body: this.saveRequirement\n    }).subscribe(res => {\n      if (res.StatusCode === 0) {\n        this.message.showSucessMSG('執行成功');\n        this.getList();\n      } else {\n        this.message.showErrorMSG(res.Message);\n      }\n    });\n    ref.close();\n  }\n  onDelete(data) {\n    this.saveRequirement.CRequirementID = data.CRequirementID;\n    this.isNew = false;\n    if (window.confirm('是否確定刪除?')) {\n      this.remove();\n    } else {\n      return;\n    }\n  }\n  remove() {\n    this.requirementService.apiRequirementDeleteDataPost$Json({\n      body: {\n        CRequirementID: this.saveRequirement.CRequirementID\n      }\n    }).subscribe(res => {\n      this.message.showSucessMSG('執行成功');\n      this.getList();\n    });\n  }\n  getBuildCaseList() {\n    this.buildCaseService.apiBuildCaseGetUserBuildCasePost$Json({\n      body: {}\n    }).pipe(takeUntilDestroyed(this.destroyref)).subscribe(res => {\n      this.buildCaseList = res.Entries;\n      // 只在建案 tab 下且有建案時才查詢\n      if (this.currentTab === 0 && this.buildCaseList.length > 0) {\n        this.getListRequirementRequest.CBuildCaseID = this.buildCaseList[0].cID;\n        this.getList();\n      } else if (this.currentTab === 1) {\n        this.getListRequirementRequest.CBuildCaseID = 0;\n        this.getList();\n      }\n    });\n  }\n  getList() {\n    this.getListRequirementRequest.PageSize = this.pageSize;\n    this.getListRequirementRequest.PageIndex = this.pageIndex;\n    this.requirementList = [];\n    this.totalRecords = 0;\n    // 當選擇模板頁面時，強制設定 CBuildCaseID = 0 且不可異動\n    if (this.currentTab === 1) {\n      this.getListRequirementRequest.CBuildCaseID = 0;\n    } else {\n      // 建案頁面的邏輯保持不變\n      if (this.getListRequirementRequest.CBuildCaseID && this.getListRequirementRequest.CBuildCaseID != 0) {\n        this.currentBuildCase = this.getListRequirementRequest.CBuildCaseID;\n      }\n    }\n    this.requirementService.apiRequirementGetListPost$Json({\n      body: this.getListRequirementRequest\n    }).pipe().subscribe(res => {\n      if (res.StatusCode == 0) {\n        if (res.Entries) {\n          this.requirementList = res.Entries;\n          this.totalRecords = res.TotalItems;\n        }\n      }\n    });\n  }\n  getData() {\n    this.requirementService.apiRequirementGetDataPost$Json({\n      body: this.getRequirementRequest\n    }).pipe().subscribe(res => {\n      if (res.StatusCode == 0) {\n        if (res.Entries) {\n          this.saveRequirement = {\n            CHouseType: [],\n            CIsShow: false\n          };\n          this.saveRequirement.CBuildCaseID = res.Entries.CBuildCaseID;\n          this.saveRequirement.CGroupName = res.Entries.CGroupName;\n          this.saveRequirement.CHouseType = res.Entries.CHouseType ? Array.isArray(res.Entries.CHouseType) ? res.Entries.CHouseType : [res.Entries.CHouseType] : [];\n          this.saveRequirement.CRemark = res.Entries.CRemark;\n          this.saveRequirement.CRequirement = res.Entries.CRequirement;\n          this.saveRequirement.CRequirementID = res.Entries.CRequirementID;\n          this.saveRequirement.CSort = res.Entries.CSort;\n          this.saveRequirement.CStatus = res.Entries.CStatus;\n          this.saveRequirement.CUnitPrice = res.Entries.CUnitPrice;\n          this.saveRequirement.CUnit = res.Entries.CUnit;\n          // TODO: 等後端API更新後啟用這行\n          this.saveRequirement.CIsShow = res.Entries.CIsShow || false;\n        }\n      }\n    });\n  }\n  onHouseTypeChange(value, checked) {\n    console.log(checked);\n    if (checked) {\n      if (!this.saveRequirement.CHouseType?.includes(value)) {\n        this.saveRequirement.CHouseType?.push(value);\n      }\n      console.log(this.saveRequirement.CHouseType);\n    } else {\n      this.saveRequirement.CHouseType = this.saveRequirement.CHouseType?.filter(v => v !== value);\n    }\n  }\n  getCIsShowText(data) {\n    return data.CIsShow ? '是' : '否';\n  }\n  onTabChange(event) {\n    // 避免頁面初始化時自動觸發重複查詢\n    if (this.isFirstTabChange) {\n      this.isFirstTabChange = false;\n      return;\n    }\n    // 根據 tabTitle 來判斷當前頁面\n    if (event.tabTitle === '共用') {\n      this.currentTab = 1;\n      this.getListRequirementRequest.CBuildCaseID = 0;\n    } else {\n      this.currentTab = 0;\n      // 切換回建案頁面時，如果有建案資料，預設選擇第一個\n      if (this.buildCaseList && this.buildCaseList.length > 0) {\n        this.getListRequirementRequest.CBuildCaseID = this.buildCaseList[0].cID;\n      }\n    }\n    this.getList();\n  }\n  // 新增模板\n  addTemplate(dialog) {\n    this.isNew = true;\n    this.saveRequirement = {\n      CHouseType: [],\n      CIsShow: false\n    };\n    this.saveRequirement.CStatus = 1;\n    this.saveRequirement.CUnitPrice = 0;\n    // 模板設定建案ID為0\n    this.saveRequirement.CBuildCaseID = 0;\n    this.dialogService.open(dialog);\n  }\n  // 編輯模板\n  onEditTemplate(data, dialog) {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      _this2.getRequirementRequest.CRequirementID = data.CRequirementID;\n      _this2.isNew = false;\n      try {\n        yield _this2.getData();\n        _this2.dialogService.open(dialog);\n      } catch (error) {\n        console.log(\"Failed to get template data\", error);\n      }\n    })();\n  }\n  // 保存模板\n  saveTemplate(ref) {\n    // 模板驗證（不包含建案名稱）\n    this.valid.clear();\n    this.valid.required('[需求]', this.saveRequirement.CRequirement);\n    this.valid.required('[地主戶]', this.saveRequirement.CHouseType);\n    this.valid.required('[安排]', this.saveRequirement.CSort);\n    this.valid.required('[狀態]', this.saveRequirement.CStatus);\n    this.valid.required('[單價]', this.saveRequirement.CUnitPrice);\n    this.valid.required('[單位]', this.saveRequirement.CUnit);\n    // 群組名稱長度驗證\n    if (this.saveRequirement.CGroupName && this.saveRequirement.CGroupName.length > 20) {\n      this.valid.errorMessages.push('[群組名稱] 不能超過20個字');\n    }\n    // 備註說明長度驗證\n    if (this.saveRequirement.CRemark && this.saveRequirement.CRemark.length > 100) {\n      this.valid.errorMessages.push('[備註說明] 不能超過100個字');\n    }\n    if (this.valid.errorMessages.length > 0) {\n      this.message.showErrorMSGs(this.valid.errorMessages);\n      return;\n    }\n    // 確保模板建案ID為0\n    const templateData = {\n      ...this.saveRequirement\n    };\n    templateData.CBuildCaseID = 0;\n    this.requirementService.apiRequirementSaveDataPost$Json({\n      body: templateData\n    }).subscribe(res => {\n      if (res.StatusCode === 0) {\n        this.message.showSucessMSG('執行成功');\n        this.getList();\n      } else {\n        this.message.showErrorMSG(res.Message);\n      }\n    });\n    ref.close();\n  }\n  openTemplateViewer() {\n    this.showTemplateViewer = true;\n  }\n  closeTemplateViewer() {\n    this.showTemplateViewer = false;\n  }\n  onAddTemplate() {\n    // 新增模板邏輯\n    alert('新增模板功能');\n  }\n  onSelectTemplate(tpl) {\n    // 查看模板邏輯\n    alert('查看模板: ' + tpl.name);\n  }\n  onSaveTemplate(tpl) {\n    this.templateList.push(tpl);\n    // 這裡可串接API儲存模板\n    this.message.success('模板已儲存');\n  }\n};\nRequirementManagementComponent = __decorate([Component({\n  selector: 'app-requirement-management',\n  standalone: true,\n  imports: [NbCardModule, BreadcrumbComponent, NbInputModule, FormsModule, NbSelectModule, NbOptionModule, NgIf, NgFor, PaginationComponent, StatusPipe, NbCheckboxModule, FormGroupComponent, NumberWithCommasPipe, NbTabsetModule, TemplateViewerComponent],\n  templateUrl: './requirement-management.component.html',\n  styleUrl: './requirement-management.component.scss'\n})], RequirementManagementComponent);\nexport { RequirementManagementComponent };", "map": {"version": 3, "names": ["Component", "NbCardModule", "NbCheckboxModule", "NbInputModule", "NbOptionModule", "NbSelectModule", "NbTabsetModule", "BaseComponent", "takeUntilDestroyed", "BreadcrumbComponent", "FormsModule", "<PERSON><PERSON><PERSON>", "NgIf", "PaginationComponent", "StatusPipe", "FormGroupComponent", "NumberWithCommasPipe", "EnumHouseType", "TemplateViewerComponent", "RequirementManagementComponent", "constructor", "_allow", "enum<PERSON>elper", "dialogService", "message", "valid", "buildCaseService", "requirementService", "pettern", "router", "destroyref", "getListRequirementRequest", "getRequirementRequest", "buildCaseList", "requirementList", "saveRequirement", "CHouseType", "statusOptions", "value", "label", "houseType", "getEnumOptions", "isNew", "currentBuildCase", "currentTab", "templateList", "name", "description", "showTemplateViewer", "isFirstTabChange", "initializeSearchForm", "getBuildCaseList", "ngOnInit", "CStatus", "CIsShow", "CRequirement", "CGroupName", "map", "type", "resetSearch", "length", "setTimeout", "CBuildCaseID", "cID", "getList", "getHouseType", "hTypes", "Array", "isArray", "labels", "for<PERSON>ach", "htype", "findH", "find", "x", "push", "join", "validation", "clear", "required", "CSort", "CUnitPrice", "CUnit", "errorMessages", "CRemark", "add", "dialog", "open", "onEdit", "data", "_this", "_asyncToGenerator", "CRequirementID", "getData", "error", "console", "log", "save", "ref", "showErrorMSGs", "apiRequirementSaveDataPost$Json", "body", "subscribe", "res", "StatusCode", "showSucessMSG", "showErrorMSG", "Message", "close", "onDelete", "window", "confirm", "remove", "apiRequirementDeleteDataPost$Json", "apiBuildCaseGetUserBuildCasePost$Json", "pipe", "Entries", "PageSize", "pageSize", "PageIndex", "pageIndex", "totalRecords", "apiRequirementGetListPost$Json", "TotalItems", "apiRequirementGetDataPost$Json", "onHouseTypeChange", "checked", "includes", "filter", "v", "getCIsShowText", "onTabChange", "event", "tabTitle", "addTemplate", "onEditTemplate", "_this2", "saveTemplate", "templateData", "openTemplateViewer", "closeTemplateViewer", "onAddTemplate", "alert", "onSelectTemplate", "tpl", "onSaveTemplate", "success", "__decorate", "selector", "standalone", "imports", "templateUrl", "styleUrl"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\requirement-management\\requirement-management.component.ts"], "sourcesContent": ["import { Component, DestroyRef, OnInit, TemplateRef } from '@angular/core';\r\nimport { Router } from '@angular/router';\r\nimport { NbCardModule, NbCheckboxModule, NbDialogService, NbInputModule, NbOptionModule, NbSelectModule, NbTabsetModule } from '@nebular/theme';\r\nimport { MessageService } from 'src/app/shared/services/message.service';\r\nimport { AllowHelper } from 'src/app/shared/helper/allowHelper';\r\nimport { EnumHelper } from 'src/app/shared/helper/enumHelper';\r\nimport { PetternHelper } from 'src/app/shared/helper/petternHelper';\r\nimport { ValidationHelper } from 'src/app/shared/helper/validationHelper';\r\nimport { BuildCaseService, RequirementService } from 'src/services/api/services';\r\nimport { BaseComponent } from '../components/base/baseComponent';\r\nimport { takeUntilDestroyed } from '@angular/core/rxjs-interop';\r\nimport { BuildCaseGetListReponse, GetListRequirementRequest, GetRequirement, GetRequirementByIdRequest, SaveDataRequirement } from 'src/services/api/models';\r\nimport { BreadcrumbComponent } from '../components/breadcrumb/breadcrumb.component';\r\nimport { FormsModule } from '@angular/forms';\r\nimport { NgFor, NgIf } from '@angular/common';\r\nimport { PaginationComponent } from '../components/pagination/pagination.component';\r\nimport { StatusPipe } from 'src/app/@theme/pipes/mapping.pipe';\r\nimport { FormGroupComponent } from 'src/app/@theme/components/form-group/form-group.component';\r\nimport { NumberWithCommasPipe } from 'src/app/@theme/pipes/number-with-commas.pipe';\r\nimport { EnumHouseType } from 'src/app/shared/enum/enumHouseType';\r\nimport { TemplateViewerComponent } from 'src/app/shared/components/template-viewer/template-viewer.component';\r\n\r\n@Component({\r\n  selector: 'app-requirement-management',\r\n  standalone: true, imports: [\r\n    NbCardModule,\r\n    BreadcrumbComponent,\r\n    NbInputModule,\r\n    FormsModule,\r\n    NbSelectModule,\r\n    NbOptionModule,\r\n    NgIf,\r\n    NgFor,\r\n    PaginationComponent,\r\n    StatusPipe,\r\n    NbCheckboxModule,\r\n    FormGroupComponent,\r\n    NumberWithCommasPipe,\r\n    NbTabsetModule,\r\n    TemplateViewerComponent\r\n  ],\r\n  templateUrl: './requirement-management.component.html',\r\n  styleUrl: './requirement-management.component.scss'\r\n})\r\nexport class RequirementManagementComponent extends BaseComponent implements OnInit {\r\n  constructor(\r\n    private _allow: AllowHelper,\r\n    private enumHelper: EnumHelper,\r\n    private dialogService: NbDialogService,\r\n    private message: MessageService,\r\n    private valid: ValidationHelper,\r\n    private buildCaseService: BuildCaseService,\r\n    private requirementService: RequirementService,\r\n    private pettern: PetternHelper,\r\n    private router: Router,\r\n    private destroyref: DestroyRef\r\n  ) {\r\n    super(_allow);\r\n    this.initializeSearchForm();\r\n    this.getBuildCaseList();\r\n  }\r\n  // request\r\n  getListRequirementRequest = {} as GetListRequirementRequest & { CIsShow?: boolean | null };\r\n  getRequirementRequest: GetRequirementByIdRequest = {};\r\n  // response\r\n  buildCaseList: BuildCaseGetListReponse[] = [];\r\n  requirementList: GetRequirement[] = [];\r\n  saveRequirement: SaveDataRequirement & { CIsShow?: boolean } = { CHouseType: [] };\r\n\r\n  statusOptions = [\r\n    { value: 0, label: '停用' },\r\n    { value: 1, label: '啟用' },\r\n  ];\r\n  houseType = this.enumHelper.getEnumOptions(EnumHouseType);\r\n  isNew = false;\r\n  currentBuildCase = 0;\r\n  currentTab = 0; // 追蹤當前選中的 tab\r\n\r\n  // 模板資料\r\n  templateList: any[] = [\r\n    { name: '模板A', description: '範例模板A' },\r\n    { name: '模板B', description: '範例模板B' }\r\n  ];\r\n  showTemplateViewer = false;\r\n\r\n  override ngOnInit(): void { }\r\n  // 初始化搜尋表單\r\n  initializeSearchForm() {\r\n    // this.getListRequirementRequest.CBuildCaseID = -1; // <-- 移除此行，避免預設為 -1\r\n    this.getListRequirementRequest.CStatus = -1;\r\n    this.getListRequirementRequest.CIsShow = null;\r\n    this.getListRequirementRequest.CRequirement = '';\r\n    this.getListRequirementRequest.CGroupName = '';\r\n    // 預設全選所有房屋類型\r\n    this.getListRequirementRequest.CHouseType = this.houseType.map(type => type.value);\r\n  }\r\n\r\n  // 重置搜尋\r\n  resetSearch() {\r\n    this.initializeSearchForm();\r\n    // 重置後如果有建案資料，重新設定預設選擇第一個建案\r\n    if (this.buildCaseList && this.buildCaseList.length > 0) {\r\n      setTimeout(() => {\r\n        if (this.currentTab === 0) {\r\n          this.getListRequirementRequest.CBuildCaseID = this.buildCaseList[0].cID;\r\n        } else {\r\n          this.getListRequirementRequest.CBuildCaseID = 0;\r\n        }\r\n        this.getList();\r\n      }, 0);\r\n    } else {\r\n      this.getList();\r\n    }\r\n  }\r\n\r\n  getHouseType(hTypes: number[] | number | null | undefined): string {\r\n    if (!hTypes) {\r\n      return '';\r\n    }\r\n\r\n    if (!Array.isArray(hTypes)) {\r\n      hTypes = [hTypes];\r\n    }\r\n\r\n    let labels: string[] = [];\r\n    hTypes.forEach(htype => {\r\n      let findH = this.houseType.find(x => x.value == htype);\r\n      if (findH) {\r\n        labels.push(findH.label);\r\n      }\r\n    });\r\n    return labels.join(', ');\r\n  } validation() {\r\n    this.valid.clear();\r\n\r\n    // 根據當前 tab 決定是否需要驗證建案名稱\r\n    if (this.currentTab === 0) {\r\n      // 建案頁面需要驗證建案名稱\r\n      this.valid.required('[建案名稱]', this.saveRequirement.CBuildCaseID);\r\n    }\r\n\r\n    this.valid.required('[需求]', this.saveRequirement.CRequirement);\r\n    this.valid.required('[地主戶]', this.saveRequirement.CHouseType);\r\n    this.valid.required('[安排]', this.saveRequirement.CSort);\r\n    this.valid.required('[狀態]', this.saveRequirement.CStatus);\r\n    this.valid.required('[單價]', this.saveRequirement.CUnitPrice);\r\n    this.valid.required('[單位]', this.saveRequirement.CUnit);\r\n\r\n    // 群組名稱長度驗證\r\n    if (this.saveRequirement.CGroupName && this.saveRequirement.CGroupName.length > 20) {\r\n      this.valid.errorMessages.push('[群組名稱] 不能超過20個字');\r\n    }\r\n\r\n    // 備註說明長度驗證\r\n    if (this.saveRequirement.CRemark && this.saveRequirement.CRemark.length > 100) {\r\n      this.valid.errorMessages.push('[備註說明] 不能超過100個字');\r\n    }\r\n  }\r\n\r\n  add(dialog: TemplateRef<any>) {\r\n    this.isNew = true;\r\n    this.saveRequirement = { CHouseType: [], CIsShow: false };\r\n    this.saveRequirement.CStatus = 1;\r\n    this.saveRequirement.CUnitPrice = 0;\r\n\r\n    // 根據當前 tab 決定是否需要建案ID\r\n    if (this.currentTab === 0) {\r\n      // 建案頁面 - 使用當前選擇的建案或第一個建案\r\n      if (this.currentBuildCase != 0) {\r\n        this.saveRequirement.CBuildCaseID = this.currentBuildCase;\r\n      } else if (this.buildCaseList && this.buildCaseList.length > 0) {\r\n        this.saveRequirement.CBuildCaseID = this.buildCaseList[0].cID;\r\n      }\r\n    } else {\r\n      // 模板頁面 - 設定建案ID為0\r\n      this.saveRequirement.CBuildCaseID = 0;\r\n    }\r\n\r\n    this.dialogService.open(dialog);\r\n  }\r\n\r\n  async onEdit(data: GetRequirement, dialog: TemplateRef<any>) {\r\n    this.getRequirementRequest.CRequirementID = data.CRequirementID!;\r\n    this.isNew = false;\r\n    try {\r\n      await this.getData();\r\n      this.dialogService.open(dialog);\r\n    } catch (error) {\r\n      console.log(\"Failed to get function data\", error)\r\n    }\r\n  }\r\n\r\n  save(ref: any) {\r\n    this.validation();\r\n    if (this.valid.errorMessages.length > 0) {\r\n      this.message.showErrorMSGs(this.valid.errorMessages);\r\n      return;\r\n    }\r\n\r\n    // 如果是模板頁面，確保 CBuildCaseID 設定為 0\r\n    if (this.currentTab === 1) {\r\n      this.saveRequirement.CBuildCaseID = 0;\r\n    }\r\n\r\n    this.requirementService.apiRequirementSaveDataPost$Json({\r\n      body: this.saveRequirement\r\n    }).subscribe(res => {\r\n      if (res.StatusCode === 0) {\r\n        this.message.showSucessMSG('執行成功');\r\n        this.getList();\r\n      } else {\r\n        this.message.showErrorMSG(res.Message!)\r\n      }\r\n    });\r\n    ref.close();\r\n  }\r\n\r\n  onDelete(data: GetRequirement) {\r\n    this.saveRequirement.CRequirementID = data.CRequirementID!;\r\n    this.isNew = false;\r\n    if (window.confirm('是否確定刪除?')) {\r\n      this.remove();\r\n    } else {\r\n      return;\r\n    }\r\n  }\r\n\r\n  remove() {\r\n    this.requirementService.apiRequirementDeleteDataPost$Json({\r\n      body: {\r\n        CRequirementID: this.saveRequirement.CRequirementID!\r\n      }\r\n    }).subscribe(res => {\r\n      this.message.showSucessMSG('執行成功');\r\n      this.getList();\r\n    });\r\n  }\r\n\r\n  getBuildCaseList() {\r\n    this.buildCaseService.apiBuildCaseGetUserBuildCasePost$Json({ body: {} })\r\n      .pipe(takeUntilDestroyed(this.destroyref)).subscribe(res => {\r\n        this.buildCaseList = res.Entries!;\r\n        // 只在建案 tab 下且有建案時才查詢\r\n        if (this.currentTab === 0 && this.buildCaseList.length > 0) {\r\n          this.getListRequirementRequest.CBuildCaseID = this.buildCaseList[0].cID;\r\n          this.getList();\r\n        } else if (this.currentTab === 1) {\r\n          this.getListRequirementRequest.CBuildCaseID = 0;\r\n          this.getList();\r\n        }\r\n      })\r\n  }\r\n\r\n  getList() {\r\n    this.getListRequirementRequest.PageSize = this.pageSize;\r\n    this.getListRequirementRequest.PageIndex = this.pageIndex;\r\n    this.requirementList = [] as GetRequirement[];\r\n    this.totalRecords = 0;\r\n    // 當選擇模板頁面時，強制設定 CBuildCaseID = 0 且不可異動\r\n    if (this.currentTab === 1) {\r\n      this.getListRequirementRequest.CBuildCaseID = 0;\r\n    } else {\r\n      // 建案頁面的邏輯保持不變\r\n      if (this.getListRequirementRequest.CBuildCaseID && this.getListRequirementRequest.CBuildCaseID != 0) {\r\n        this.currentBuildCase = this.getListRequirementRequest.CBuildCaseID;\r\n      }\r\n    }\r\n\r\n    this.requirementService.apiRequirementGetListPost$Json({ body: this.getListRequirementRequest })\r\n      .pipe()\r\n      .subscribe(res => {\r\n        if (res.StatusCode == 0) {\r\n          if (res.Entries) {\r\n            this.requirementList = res.Entries;\r\n            this.totalRecords = res.TotalItems!;\r\n          }\r\n        }\r\n      })\r\n  } getData() {\r\n    this.requirementService.apiRequirementGetDataPost$Json({ body: this.getRequirementRequest })\r\n      .pipe()\r\n      .subscribe(res => {\r\n        if (res.StatusCode == 0) {\r\n          if (res.Entries) {\r\n            this.saveRequirement = { CHouseType: [], CIsShow: false };\r\n            this.saveRequirement.CBuildCaseID = res.Entries.CBuildCaseID;\r\n            this.saveRequirement.CGroupName = res.Entries.CGroupName;\r\n            this.saveRequirement.CHouseType = res.Entries.CHouseType ? (Array.isArray(res.Entries.CHouseType) ? res.Entries.CHouseType : [res.Entries.CHouseType]) : [];\r\n            this.saveRequirement.CRemark = res.Entries.CRemark;\r\n            this.saveRequirement.CRequirement = res.Entries.CRequirement;\r\n            this.saveRequirement.CRequirementID = res.Entries.CRequirementID;\r\n            this.saveRequirement.CSort = res.Entries.CSort;\r\n            this.saveRequirement.CStatus = res.Entries.CStatus;\r\n            this.saveRequirement.CUnitPrice = res.Entries.CUnitPrice;\r\n            this.saveRequirement.CUnit = res.Entries.CUnit;\r\n            // TODO: 等後端API更新後啟用這行\r\n            this.saveRequirement.CIsShow = (res.Entries as any).CIsShow || false;\r\n          }\r\n        }\r\n      })\r\n  }\r\n\r\n  onHouseTypeChange(value: number, checked: any) {\r\n    console.log(checked);\r\n\r\n    if (checked) {\r\n      if (!this.saveRequirement.CHouseType?.includes(value)) {\r\n        this.saveRequirement.CHouseType?.push(value);\r\n      }\r\n      console.log(this.saveRequirement.CHouseType);\r\n    } else {\r\n      this.saveRequirement.CHouseType = this.saveRequirement.CHouseType?.filter(v => v !== value);\r\n    }\r\n  }\r\n\r\n  getCIsShowText(data: any): string {\r\n    return data.CIsShow ? '是' : '否';\r\n  }\r\n\r\n  // Tab 切換事件處理\r\n  private isFirstTabChange = true;\r\n  onTabChange(event: any) {\r\n    // 避免頁面初始化時自動觸發重複查詢\r\n    if (this.isFirstTabChange) {\r\n      this.isFirstTabChange = false;\r\n      return;\r\n    }\r\n    // 根據 tabTitle 來判斷當前頁面\r\n    if (event.tabTitle === '共用') {\r\n      this.currentTab = 1;\r\n      this.getListRequirementRequest.CBuildCaseID = 0;\r\n    } else {\r\n      this.currentTab = 0;\r\n      // 切換回建案頁面時，如果有建案資料，預設選擇第一個\r\n      if (this.buildCaseList && this.buildCaseList.length > 0) {\r\n        this.getListRequirementRequest.CBuildCaseID = this.buildCaseList[0].cID;\r\n      }\r\n    }\r\n    this.getList();\r\n  }\r\n\r\n  // 新增模板\r\n  addTemplate(dialog: TemplateRef<any>) {\r\n    this.isNew = true;\r\n    this.saveRequirement = { CHouseType: [], CIsShow: false };\r\n    this.saveRequirement.CStatus = 1;\r\n    this.saveRequirement.CUnitPrice = 0;\r\n    // 模板設定建案ID為0\r\n    this.saveRequirement.CBuildCaseID = 0;\r\n    this.dialogService.open(dialog);\r\n  }\r\n\r\n  // 編輯模板\r\n  async onEditTemplate(data: GetRequirement, dialog: TemplateRef<any>) {\r\n    this.getRequirementRequest.CRequirementID = data.CRequirementID!;\r\n    this.isNew = false;\r\n    try {\r\n      await this.getData();\r\n      this.dialogService.open(dialog);\r\n    } catch (error) {\r\n      console.log(\"Failed to get template data\", error);\r\n    }\r\n  }\r\n\r\n  // 保存模板\r\n  saveTemplate(ref: any) {\r\n    // 模板驗證（不包含建案名稱）\r\n    this.valid.clear();\r\n    this.valid.required('[需求]', this.saveRequirement.CRequirement);\r\n    this.valid.required('[地主戶]', this.saveRequirement.CHouseType);\r\n    this.valid.required('[安排]', this.saveRequirement.CSort);\r\n    this.valid.required('[狀態]', this.saveRequirement.CStatus);\r\n    this.valid.required('[單價]', this.saveRequirement.CUnitPrice);\r\n    this.valid.required('[單位]', this.saveRequirement.CUnit);\r\n\r\n    // 群組名稱長度驗證\r\n    if (this.saveRequirement.CGroupName && this.saveRequirement.CGroupName.length > 20) {\r\n      this.valid.errorMessages.push('[群組名稱] 不能超過20個字');\r\n    }\r\n\r\n    // 備註說明長度驗證\r\n    if (this.saveRequirement.CRemark && this.saveRequirement.CRemark.length > 100) {\r\n      this.valid.errorMessages.push('[備註說明] 不能超過100個字');\r\n    }\r\n\r\n    if (this.valid.errorMessages.length > 0) {\r\n      this.message.showErrorMSGs(this.valid.errorMessages);\r\n      return;\r\n    }\r\n\r\n    // 確保模板建案ID為0\r\n    const templateData = { ...this.saveRequirement };\r\n    templateData.CBuildCaseID = 0;\r\n\r\n    this.requirementService.apiRequirementSaveDataPost$Json({\r\n      body: templateData\r\n    }).subscribe(res => {\r\n      if (res.StatusCode === 0) {\r\n        this.message.showSucessMSG('執行成功');\r\n        this.getList();\r\n      } else {\r\n        this.message.showErrorMSG(res.Message!)\r\n      }\r\n    });\r\n    ref.close();\r\n  }\r\n\r\n  openTemplateViewer() {\r\n    this.showTemplateViewer = true;\r\n  }\r\n  closeTemplateViewer() {\r\n    this.showTemplateViewer = false;\r\n  }\r\n  onAddTemplate() {\r\n    // 新增模板邏輯\r\n    alert('新增模板功能');\r\n  }\r\n  onSelectTemplate(tpl: any) {\r\n    // 查看模板邏輯\r\n    alert('查看模板: ' + tpl.name);\r\n  }\r\n  onSaveTemplate(tpl: any) {\r\n    this.templateList.push(tpl);\r\n    // 這裡可串接API儲存模板\r\n    this.message.success('模板已儲存');\r\n  }\r\n}\r\n"], "mappings": ";;AAAA,SAASA,SAAS,QAAyC,eAAe;AAE1E,SAASC,YAAY,EAAEC,gBAAgB,EAAmBC,aAAa,EAAEC,cAAc,EAAEC,cAAc,EAAEC,cAAc,QAAQ,gBAAgB;AAO/I,SAASC,aAAa,QAAQ,kCAAkC;AAChE,SAASC,kBAAkB,QAAQ,4BAA4B;AAE/D,SAASC,mBAAmB,QAAQ,+CAA+C;AACnF,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,KAAK,EAAEC,IAAI,QAAQ,iBAAiB;AAC7C,SAASC,mBAAmB,QAAQ,+CAA+C;AACnF,SAASC,UAAU,QAAQ,mCAAmC;AAC9D,SAASC,kBAAkB,QAAQ,2DAA2D;AAC9F,SAASC,oBAAoB,QAAQ,8CAA8C;AACnF,SAASC,aAAa,QAAQ,mCAAmC;AACjE,SAASC,uBAAuB,QAAQ,qEAAqE;AAwBtG,IAAMC,8BAA8B,GAApC,MAAMA,8BAA+B,SAAQZ,aAAa;EAC/Da,YACUC,MAAmB,EACnBC,UAAsB,EACtBC,aAA8B,EAC9BC,OAAuB,EACvBC,KAAuB,EACvBC,gBAAkC,EAClCC,kBAAsC,EACtCC,OAAsB,EACtBC,MAAc,EACdC,UAAsB;IAE9B,KAAK,CAACT,MAAM,CAAC;IAXL,KAAAA,MAAM,GAANA,MAAM;IACN,KAAAC,UAAU,GAAVA,UAAU;IACV,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,kBAAkB,GAAlBA,kBAAkB;IAClB,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,UAAU,GAAVA,UAAU;IAMpB;IACA,KAAAC,yBAAyB,GAAG,EAA8D;IAC1F,KAAAC,qBAAqB,GAA8B,EAAE;IACrD;IACA,KAAAC,aAAa,GAA8B,EAAE;IAC7C,KAAAC,eAAe,GAAqB,EAAE;IACtC,KAAAC,eAAe,GAAgD;MAAEC,UAAU,EAAE;IAAE,CAAE;IAEjF,KAAAC,aAAa,GAAG,CACd;MAAEC,KAAK,EAAE,CAAC;MAAEC,KAAK,EAAE;IAAI,CAAE,EACzB;MAAED,KAAK,EAAE,CAAC;MAAEC,KAAK,EAAE;IAAI,CAAE,CAC1B;IACD,KAAAC,SAAS,GAAG,IAAI,CAAClB,UAAU,CAACmB,cAAc,CAACxB,aAAa,CAAC;IACzD,KAAAyB,KAAK,GAAG,KAAK;IACb,KAAAC,gBAAgB,GAAG,CAAC;IACpB,KAAAC,UAAU,GAAG,CAAC,CAAC,CAAC;IAEhB;IACA,KAAAC,YAAY,GAAU,CACpB;MAAEC,IAAI,EAAE,KAAK;MAAEC,WAAW,EAAE;IAAO,CAAE,EACrC;MAAED,IAAI,EAAE,KAAK;MAAEC,WAAW,EAAE;IAAO,CAAE,CACtC;IACD,KAAAC,kBAAkB,GAAG,KAAK;IA4O1B;IACQ,KAAAC,gBAAgB,GAAG,IAAI;IAtQ7B,IAAI,CAACC,oBAAoB,EAAE;IAC3B,IAAI,CAACC,gBAAgB,EAAE;EACzB;EAyBSC,QAAQA,CAAA,GAAW;EAC5B;EACAF,oBAAoBA,CAAA;IAClB;IACA,IAAI,CAACnB,yBAAyB,CAACsB,OAAO,GAAG,CAAC,CAAC;IAC3C,IAAI,CAACtB,yBAAyB,CAACuB,OAAO,GAAG,IAAI;IAC7C,IAAI,CAACvB,yBAAyB,CAACwB,YAAY,GAAG,EAAE;IAChD,IAAI,CAACxB,yBAAyB,CAACyB,UAAU,GAAG,EAAE;IAC9C;IACA,IAAI,CAACzB,yBAAyB,CAACK,UAAU,GAAG,IAAI,CAACI,SAAS,CAACiB,GAAG,CAACC,IAAI,IAAIA,IAAI,CAACpB,KAAK,CAAC;EACpF;EAEA;EACAqB,WAAWA,CAAA;IACT,IAAI,CAACT,oBAAoB,EAAE;IAC3B;IACA,IAAI,IAAI,CAACjB,aAAa,IAAI,IAAI,CAACA,aAAa,CAAC2B,MAAM,GAAG,CAAC,EAAE;MACvDC,UAAU,CAAC,MAAK;QACd,IAAI,IAAI,CAACjB,UAAU,KAAK,CAAC,EAAE;UACzB,IAAI,CAACb,yBAAyB,CAAC+B,YAAY,GAAG,IAAI,CAAC7B,aAAa,CAAC,CAAC,CAAC,CAAC8B,GAAG;QACzE,CAAC,MAAM;UACL,IAAI,CAAChC,yBAAyB,CAAC+B,YAAY,GAAG,CAAC;QACjD;QACA,IAAI,CAACE,OAAO,EAAE;MAChB,CAAC,EAAE,CAAC,CAAC;IACP,CAAC,MAAM;MACL,IAAI,CAACA,OAAO,EAAE;IAChB;EACF;EAEAC,YAAYA,CAACC,MAA4C;IACvD,IAAI,CAACA,MAAM,EAAE;MACX,OAAO,EAAE;IACX;IAEA,IAAI,CAACC,KAAK,CAACC,OAAO,CAACF,MAAM,CAAC,EAAE;MAC1BA,MAAM,GAAG,CAACA,MAAM,CAAC;IACnB;IAEA,IAAIG,MAAM,GAAa,EAAE;IACzBH,MAAM,CAACI,OAAO,CAACC,KAAK,IAAG;MACrB,IAAIC,KAAK,GAAG,IAAI,CAAChC,SAAS,CAACiC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACpC,KAAK,IAAIiC,KAAK,CAAC;MACtD,IAAIC,KAAK,EAAE;QACTH,MAAM,CAACM,IAAI,CAACH,KAAK,CAACjC,KAAK,CAAC;MAC1B;IACF,CAAC,CAAC;IACF,OAAO8B,MAAM,CAACO,IAAI,CAAC,IAAI,CAAC;EAC1B;EAAEC,UAAUA,CAAA;IACV,IAAI,CAACpD,KAAK,CAACqD,KAAK,EAAE;IAElB;IACA,IAAI,IAAI,CAAClC,UAAU,KAAK,CAAC,EAAE;MACzB;MACA,IAAI,CAACnB,KAAK,CAACsD,QAAQ,CAAC,QAAQ,EAAE,IAAI,CAAC5C,eAAe,CAAC2B,YAAY,CAAC;IAClE;IAEA,IAAI,CAACrC,KAAK,CAACsD,QAAQ,CAAC,MAAM,EAAE,IAAI,CAAC5C,eAAe,CAACoB,YAAY,CAAC;IAC9D,IAAI,CAAC9B,KAAK,CAACsD,QAAQ,CAAC,OAAO,EAAE,IAAI,CAAC5C,eAAe,CAACC,UAAU,CAAC;IAC7D,IAAI,CAACX,KAAK,CAACsD,QAAQ,CAAC,MAAM,EAAE,IAAI,CAAC5C,eAAe,CAAC6C,KAAK,CAAC;IACvD,IAAI,CAACvD,KAAK,CAACsD,QAAQ,CAAC,MAAM,EAAE,IAAI,CAAC5C,eAAe,CAACkB,OAAO,CAAC;IACzD,IAAI,CAAC5B,KAAK,CAACsD,QAAQ,CAAC,MAAM,EAAE,IAAI,CAAC5C,eAAe,CAAC8C,UAAU,CAAC;IAC5D,IAAI,CAACxD,KAAK,CAACsD,QAAQ,CAAC,MAAM,EAAE,IAAI,CAAC5C,eAAe,CAAC+C,KAAK,CAAC;IAEvD;IACA,IAAI,IAAI,CAAC/C,eAAe,CAACqB,UAAU,IAAI,IAAI,CAACrB,eAAe,CAACqB,UAAU,CAACI,MAAM,GAAG,EAAE,EAAE;MAClF,IAAI,CAACnC,KAAK,CAAC0D,aAAa,CAACR,IAAI,CAAC,iBAAiB,CAAC;IAClD;IAEA;IACA,IAAI,IAAI,CAACxC,eAAe,CAACiD,OAAO,IAAI,IAAI,CAACjD,eAAe,CAACiD,OAAO,CAACxB,MAAM,GAAG,GAAG,EAAE;MAC7E,IAAI,CAACnC,KAAK,CAAC0D,aAAa,CAACR,IAAI,CAAC,kBAAkB,CAAC;IACnD;EACF;EAEAU,GAAGA,CAACC,MAAwB;IAC1B,IAAI,CAAC5C,KAAK,GAAG,IAAI;IACjB,IAAI,CAACP,eAAe,GAAG;MAAEC,UAAU,EAAE,EAAE;MAAEkB,OAAO,EAAE;IAAK,CAAE;IACzD,IAAI,CAACnB,eAAe,CAACkB,OAAO,GAAG,CAAC;IAChC,IAAI,CAAClB,eAAe,CAAC8C,UAAU,GAAG,CAAC;IAEnC;IACA,IAAI,IAAI,CAACrC,UAAU,KAAK,CAAC,EAAE;MACzB;MACA,IAAI,IAAI,CAACD,gBAAgB,IAAI,CAAC,EAAE;QAC9B,IAAI,CAACR,eAAe,CAAC2B,YAAY,GAAG,IAAI,CAACnB,gBAAgB;MAC3D,CAAC,MAAM,IAAI,IAAI,CAACV,aAAa,IAAI,IAAI,CAACA,aAAa,CAAC2B,MAAM,GAAG,CAAC,EAAE;QAC9D,IAAI,CAACzB,eAAe,CAAC2B,YAAY,GAAG,IAAI,CAAC7B,aAAa,CAAC,CAAC,CAAC,CAAC8B,GAAG;MAC/D;IACF,CAAC,MAAM;MACL;MACA,IAAI,CAAC5B,eAAe,CAAC2B,YAAY,GAAG,CAAC;IACvC;IAEA,IAAI,CAACvC,aAAa,CAACgE,IAAI,CAACD,MAAM,CAAC;EACjC;EAEME,MAAMA,CAACC,IAAoB,EAAEH,MAAwB;IAAA,IAAAI,KAAA;IAAA,OAAAC,iBAAA;MACzDD,KAAI,CAAC1D,qBAAqB,CAAC4D,cAAc,GAAGH,IAAI,CAACG,cAAe;MAChEF,KAAI,CAAChD,KAAK,GAAG,KAAK;MAClB,IAAI;QACF,MAAMgD,KAAI,CAACG,OAAO,EAAE;QACpBH,KAAI,CAACnE,aAAa,CAACgE,IAAI,CAACD,MAAM,CAAC;MACjC,CAAC,CAAC,OAAOQ,KAAK,EAAE;QACdC,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAEF,KAAK,CAAC;MACnD;IAAC;EACH;EAEAG,IAAIA,CAACC,GAAQ;IACX,IAAI,CAACrB,UAAU,EAAE;IACjB,IAAI,IAAI,CAACpD,KAAK,CAAC0D,aAAa,CAACvB,MAAM,GAAG,CAAC,EAAE;MACvC,IAAI,CAACpC,OAAO,CAAC2E,aAAa,CAAC,IAAI,CAAC1E,KAAK,CAAC0D,aAAa,CAAC;MACpD;IACF;IAEA;IACA,IAAI,IAAI,CAACvC,UAAU,KAAK,CAAC,EAAE;MACzB,IAAI,CAACT,eAAe,CAAC2B,YAAY,GAAG,CAAC;IACvC;IAEA,IAAI,CAACnC,kBAAkB,CAACyE,+BAA+B,CAAC;MACtDC,IAAI,EAAE,IAAI,CAAClE;KACZ,CAAC,CAACmE,SAAS,CAACC,GAAG,IAAG;MACjB,IAAIA,GAAG,CAACC,UAAU,KAAK,CAAC,EAAE;QACxB,IAAI,CAAChF,OAAO,CAACiF,aAAa,CAAC,MAAM,CAAC;QAClC,IAAI,CAACzC,OAAO,EAAE;MAChB,CAAC,MAAM;QACL,IAAI,CAACxC,OAAO,CAACkF,YAAY,CAACH,GAAG,CAACI,OAAQ,CAAC;MACzC;IACF,CAAC,CAAC;IACFT,GAAG,CAACU,KAAK,EAAE;EACb;EAEAC,QAAQA,CAACpB,IAAoB;IAC3B,IAAI,CAACtD,eAAe,CAACyD,cAAc,GAAGH,IAAI,CAACG,cAAe;IAC1D,IAAI,CAAClD,KAAK,GAAG,KAAK;IAClB,IAAIoE,MAAM,CAACC,OAAO,CAAC,SAAS,CAAC,EAAE;MAC7B,IAAI,CAACC,MAAM,EAAE;IACf,CAAC,MAAM;MACL;IACF;EACF;EAEAA,MAAMA,CAAA;IACJ,IAAI,CAACrF,kBAAkB,CAACsF,iCAAiC,CAAC;MACxDZ,IAAI,EAAE;QACJT,cAAc,EAAE,IAAI,CAACzD,eAAe,CAACyD;;KAExC,CAAC,CAACU,SAAS,CAACC,GAAG,IAAG;MACjB,IAAI,CAAC/E,OAAO,CAACiF,aAAa,CAAC,MAAM,CAAC;MAClC,IAAI,CAACzC,OAAO,EAAE;IAChB,CAAC,CAAC;EACJ;EAEAb,gBAAgBA,CAAA;IACd,IAAI,CAACzB,gBAAgB,CAACwF,qCAAqC,CAAC;MAAEb,IAAI,EAAE;IAAE,CAAE,CAAC,CACtEc,IAAI,CAAC3G,kBAAkB,CAAC,IAAI,CAACsB,UAAU,CAAC,CAAC,CAACwE,SAAS,CAACC,GAAG,IAAG;MACzD,IAAI,CAACtE,aAAa,GAAGsE,GAAG,CAACa,OAAQ;MACjC;MACA,IAAI,IAAI,CAACxE,UAAU,KAAK,CAAC,IAAI,IAAI,CAACX,aAAa,CAAC2B,MAAM,GAAG,CAAC,EAAE;QAC1D,IAAI,CAAC7B,yBAAyB,CAAC+B,YAAY,GAAG,IAAI,CAAC7B,aAAa,CAAC,CAAC,CAAC,CAAC8B,GAAG;QACvE,IAAI,CAACC,OAAO,EAAE;MAChB,CAAC,MAAM,IAAI,IAAI,CAACpB,UAAU,KAAK,CAAC,EAAE;QAChC,IAAI,CAACb,yBAAyB,CAAC+B,YAAY,GAAG,CAAC;QAC/C,IAAI,CAACE,OAAO,EAAE;MAChB;IACF,CAAC,CAAC;EACN;EAEAA,OAAOA,CAAA;IACL,IAAI,CAACjC,yBAAyB,CAACsF,QAAQ,GAAG,IAAI,CAACC,QAAQ;IACvD,IAAI,CAACvF,yBAAyB,CAACwF,SAAS,GAAG,IAAI,CAACC,SAAS;IACzD,IAAI,CAACtF,eAAe,GAAG,EAAsB;IAC7C,IAAI,CAACuF,YAAY,GAAG,CAAC;IACrB;IACA,IAAI,IAAI,CAAC7E,UAAU,KAAK,CAAC,EAAE;MACzB,IAAI,CAACb,yBAAyB,CAAC+B,YAAY,GAAG,CAAC;IACjD,CAAC,MAAM;MACL;MACA,IAAI,IAAI,CAAC/B,yBAAyB,CAAC+B,YAAY,IAAI,IAAI,CAAC/B,yBAAyB,CAAC+B,YAAY,IAAI,CAAC,EAAE;QACnG,IAAI,CAACnB,gBAAgB,GAAG,IAAI,CAACZ,yBAAyB,CAAC+B,YAAY;MACrE;IACF;IAEA,IAAI,CAACnC,kBAAkB,CAAC+F,8BAA8B,CAAC;MAAErB,IAAI,EAAE,IAAI,CAACtE;IAAyB,CAAE,CAAC,CAC7FoF,IAAI,EAAE,CACNb,SAAS,CAACC,GAAG,IAAG;MACf,IAAIA,GAAG,CAACC,UAAU,IAAI,CAAC,EAAE;QACvB,IAAID,GAAG,CAACa,OAAO,EAAE;UACf,IAAI,CAAClF,eAAe,GAAGqE,GAAG,CAACa,OAAO;UAClC,IAAI,CAACK,YAAY,GAAGlB,GAAG,CAACoB,UAAW;QACrC;MACF;IACF,CAAC,CAAC;EACN;EAAE9B,OAAOA,CAAA;IACP,IAAI,CAAClE,kBAAkB,CAACiG,8BAA8B,CAAC;MAAEvB,IAAI,EAAE,IAAI,CAACrE;IAAqB,CAAE,CAAC,CACzFmF,IAAI,EAAE,CACNb,SAAS,CAACC,GAAG,IAAG;MACf,IAAIA,GAAG,CAACC,UAAU,IAAI,CAAC,EAAE;QACvB,IAAID,GAAG,CAACa,OAAO,EAAE;UACf,IAAI,CAACjF,eAAe,GAAG;YAAEC,UAAU,EAAE,EAAE;YAAEkB,OAAO,EAAE;UAAK,CAAE;UACzD,IAAI,CAACnB,eAAe,CAAC2B,YAAY,GAAGyC,GAAG,CAACa,OAAO,CAACtD,YAAY;UAC5D,IAAI,CAAC3B,eAAe,CAACqB,UAAU,GAAG+C,GAAG,CAACa,OAAO,CAAC5D,UAAU;UACxD,IAAI,CAACrB,eAAe,CAACC,UAAU,GAAGmE,GAAG,CAACa,OAAO,CAAChF,UAAU,GAAI+B,KAAK,CAACC,OAAO,CAACmC,GAAG,CAACa,OAAO,CAAChF,UAAU,CAAC,GAAGmE,GAAG,CAACa,OAAO,CAAChF,UAAU,GAAG,CAACmE,GAAG,CAACa,OAAO,CAAChF,UAAU,CAAC,GAAI,EAAE;UAC3J,IAAI,CAACD,eAAe,CAACiD,OAAO,GAAGmB,GAAG,CAACa,OAAO,CAAChC,OAAO;UAClD,IAAI,CAACjD,eAAe,CAACoB,YAAY,GAAGgD,GAAG,CAACa,OAAO,CAAC7D,YAAY;UAC5D,IAAI,CAACpB,eAAe,CAACyD,cAAc,GAAGW,GAAG,CAACa,OAAO,CAACxB,cAAc;UAChE,IAAI,CAACzD,eAAe,CAAC6C,KAAK,GAAGuB,GAAG,CAACa,OAAO,CAACpC,KAAK;UAC9C,IAAI,CAAC7C,eAAe,CAACkB,OAAO,GAAGkD,GAAG,CAACa,OAAO,CAAC/D,OAAO;UAClD,IAAI,CAAClB,eAAe,CAAC8C,UAAU,GAAGsB,GAAG,CAACa,OAAO,CAACnC,UAAU;UACxD,IAAI,CAAC9C,eAAe,CAAC+C,KAAK,GAAGqB,GAAG,CAACa,OAAO,CAAClC,KAAK;UAC9C;UACA,IAAI,CAAC/C,eAAe,CAACmB,OAAO,GAAIiD,GAAG,CAACa,OAAe,CAAC9D,OAAO,IAAI,KAAK;QACtE;MACF;IACF,CAAC,CAAC;EACN;EAEAuE,iBAAiBA,CAACvF,KAAa,EAAEwF,OAAY;IAC3C/B,OAAO,CAACC,GAAG,CAAC8B,OAAO,CAAC;IAEpB,IAAIA,OAAO,EAAE;MACX,IAAI,CAAC,IAAI,CAAC3F,eAAe,CAACC,UAAU,EAAE2F,QAAQ,CAACzF,KAAK,CAAC,EAAE;QACrD,IAAI,CAACH,eAAe,CAACC,UAAU,EAAEuC,IAAI,CAACrC,KAAK,CAAC;MAC9C;MACAyD,OAAO,CAACC,GAAG,CAAC,IAAI,CAAC7D,eAAe,CAACC,UAAU,CAAC;IAC9C,CAAC,MAAM;MACL,IAAI,CAACD,eAAe,CAACC,UAAU,GAAG,IAAI,CAACD,eAAe,CAACC,UAAU,EAAE4F,MAAM,CAACC,CAAC,IAAIA,CAAC,KAAK3F,KAAK,CAAC;IAC7F;EACF;EAEA4F,cAAcA,CAACzC,IAAS;IACtB,OAAOA,IAAI,CAACnC,OAAO,GAAG,GAAG,GAAG,GAAG;EACjC;EAIA6E,WAAWA,CAACC,KAAU;IACpB;IACA,IAAI,IAAI,CAACnF,gBAAgB,EAAE;MACzB,IAAI,CAACA,gBAAgB,GAAG,KAAK;MAC7B;IACF;IACA;IACA,IAAImF,KAAK,CAACC,QAAQ,KAAK,IAAI,EAAE;MAC3B,IAAI,CAACzF,UAAU,GAAG,CAAC;MACnB,IAAI,CAACb,yBAAyB,CAAC+B,YAAY,GAAG,CAAC;IACjD,CAAC,MAAM;MACL,IAAI,CAAClB,UAAU,GAAG,CAAC;MACnB;MACA,IAAI,IAAI,CAACX,aAAa,IAAI,IAAI,CAACA,aAAa,CAAC2B,MAAM,GAAG,CAAC,EAAE;QACvD,IAAI,CAAC7B,yBAAyB,CAAC+B,YAAY,GAAG,IAAI,CAAC7B,aAAa,CAAC,CAAC,CAAC,CAAC8B,GAAG;MACzE;IACF;IACA,IAAI,CAACC,OAAO,EAAE;EAChB;EAEA;EACAsE,WAAWA,CAAChD,MAAwB;IAClC,IAAI,CAAC5C,KAAK,GAAG,IAAI;IACjB,IAAI,CAACP,eAAe,GAAG;MAAEC,UAAU,EAAE,EAAE;MAAEkB,OAAO,EAAE;IAAK,CAAE;IACzD,IAAI,CAACnB,eAAe,CAACkB,OAAO,GAAG,CAAC;IAChC,IAAI,CAAClB,eAAe,CAAC8C,UAAU,GAAG,CAAC;IACnC;IACA,IAAI,CAAC9C,eAAe,CAAC2B,YAAY,GAAG,CAAC;IACrC,IAAI,CAACvC,aAAa,CAACgE,IAAI,CAACD,MAAM,CAAC;EACjC;EAEA;EACMiD,cAAcA,CAAC9C,IAAoB,EAAEH,MAAwB;IAAA,IAAAkD,MAAA;IAAA,OAAA7C,iBAAA;MACjE6C,MAAI,CAACxG,qBAAqB,CAAC4D,cAAc,GAAGH,IAAI,CAACG,cAAe;MAChE4C,MAAI,CAAC9F,KAAK,GAAG,KAAK;MAClB,IAAI;QACF,MAAM8F,MAAI,CAAC3C,OAAO,EAAE;QACpB2C,MAAI,CAACjH,aAAa,CAACgE,IAAI,CAACD,MAAM,CAAC;MACjC,CAAC,CAAC,OAAOQ,KAAK,EAAE;QACdC,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAEF,KAAK,CAAC;MACnD;IAAC;EACH;EAEA;EACA2C,YAAYA,CAACvC,GAAQ;IACnB;IACA,IAAI,CAACzE,KAAK,CAACqD,KAAK,EAAE;IAClB,IAAI,CAACrD,KAAK,CAACsD,QAAQ,CAAC,MAAM,EAAE,IAAI,CAAC5C,eAAe,CAACoB,YAAY,CAAC;IAC9D,IAAI,CAAC9B,KAAK,CAACsD,QAAQ,CAAC,OAAO,EAAE,IAAI,CAAC5C,eAAe,CAACC,UAAU,CAAC;IAC7D,IAAI,CAACX,KAAK,CAACsD,QAAQ,CAAC,MAAM,EAAE,IAAI,CAAC5C,eAAe,CAAC6C,KAAK,CAAC;IACvD,IAAI,CAACvD,KAAK,CAACsD,QAAQ,CAAC,MAAM,EAAE,IAAI,CAAC5C,eAAe,CAACkB,OAAO,CAAC;IACzD,IAAI,CAAC5B,KAAK,CAACsD,QAAQ,CAAC,MAAM,EAAE,IAAI,CAAC5C,eAAe,CAAC8C,UAAU,CAAC;IAC5D,IAAI,CAACxD,KAAK,CAACsD,QAAQ,CAAC,MAAM,EAAE,IAAI,CAAC5C,eAAe,CAAC+C,KAAK,CAAC;IAEvD;IACA,IAAI,IAAI,CAAC/C,eAAe,CAACqB,UAAU,IAAI,IAAI,CAACrB,eAAe,CAACqB,UAAU,CAACI,MAAM,GAAG,EAAE,EAAE;MAClF,IAAI,CAACnC,KAAK,CAAC0D,aAAa,CAACR,IAAI,CAAC,iBAAiB,CAAC;IAClD;IAEA;IACA,IAAI,IAAI,CAACxC,eAAe,CAACiD,OAAO,IAAI,IAAI,CAACjD,eAAe,CAACiD,OAAO,CAACxB,MAAM,GAAG,GAAG,EAAE;MAC7E,IAAI,CAACnC,KAAK,CAAC0D,aAAa,CAACR,IAAI,CAAC,kBAAkB,CAAC;IACnD;IAEA,IAAI,IAAI,CAAClD,KAAK,CAAC0D,aAAa,CAACvB,MAAM,GAAG,CAAC,EAAE;MACvC,IAAI,CAACpC,OAAO,CAAC2E,aAAa,CAAC,IAAI,CAAC1E,KAAK,CAAC0D,aAAa,CAAC;MACpD;IACF;IAEA;IACA,MAAMuD,YAAY,GAAG;MAAE,GAAG,IAAI,CAACvG;IAAe,CAAE;IAChDuG,YAAY,CAAC5E,YAAY,GAAG,CAAC;IAE7B,IAAI,CAACnC,kBAAkB,CAACyE,+BAA+B,CAAC;MACtDC,IAAI,EAAEqC;KACP,CAAC,CAACpC,SAAS,CAACC,GAAG,IAAG;MACjB,IAAIA,GAAG,CAACC,UAAU,KAAK,CAAC,EAAE;QACxB,IAAI,CAAChF,OAAO,CAACiF,aAAa,CAAC,MAAM,CAAC;QAClC,IAAI,CAACzC,OAAO,EAAE;MAChB,CAAC,MAAM;QACL,IAAI,CAACxC,OAAO,CAACkF,YAAY,CAACH,GAAG,CAACI,OAAQ,CAAC;MACzC;IACF,CAAC,CAAC;IACFT,GAAG,CAACU,KAAK,EAAE;EACb;EAEA+B,kBAAkBA,CAAA;IAChB,IAAI,CAAC3F,kBAAkB,GAAG,IAAI;EAChC;EACA4F,mBAAmBA,CAAA;IACjB,IAAI,CAAC5F,kBAAkB,GAAG,KAAK;EACjC;EACA6F,aAAaA,CAAA;IACX;IACAC,KAAK,CAAC,QAAQ,CAAC;EACjB;EACAC,gBAAgBA,CAACC,GAAQ;IACvB;IACAF,KAAK,CAAC,QAAQ,GAAGE,GAAG,CAAClG,IAAI,CAAC;EAC5B;EACAmG,cAAcA,CAACD,GAAQ;IACrB,IAAI,CAACnG,YAAY,CAAC8B,IAAI,CAACqE,GAAG,CAAC;IAC3B;IACA,IAAI,CAACxH,OAAO,CAAC0H,OAAO,CAAC,OAAO,CAAC;EAC/B;CACD;AA9XY/H,8BAA8B,GAAAgI,UAAA,EAtB1CnJ,SAAS,CAAC;EACToJ,QAAQ,EAAE,4BAA4B;EACtCC,UAAU,EAAE,IAAI;EAAEC,OAAO,EAAE,CACzBrJ,YAAY,EACZQ,mBAAmB,EACnBN,aAAa,EACbO,WAAW,EACXL,cAAc,EACdD,cAAc,EACdQ,IAAI,EACJD,KAAK,EACLE,mBAAmB,EACnBC,UAAU,EACVZ,gBAAgB,EAChBa,kBAAkB,EAClBC,oBAAoB,EACpBV,cAAc,EACdY,uBAAuB,CACxB;EACDqI,WAAW,EAAE,yCAAyC;EACtDC,QAAQ,EAAE;CACX,CAAC,C,EACWrI,8BAA8B,CA8X1C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}