{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { concatMap, finalize, tap } from 'rxjs';\nimport { BaseComponent } from '../../components/base/baseComponent';\nimport { SharedModule } from '../../components/shared.module';\nimport * as _ from 'lodash';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/shared/helper/allowHelper\";\nimport * as i2 from \"@nebular/theme\";\nimport * as i3 from \"src/app/shared/services/message.service\";\nimport * as i4 from \"src/app/shared/helper/validationHelper\";\nimport * as i5 from \"src/services/api/services\";\nimport * as i6 from \"@angular/router\";\nimport * as i7 from \"src/app/shared/services/utility.service\";\nimport * as i8 from \"@angular/common\";\nimport * as i9 from \"@angular/forms\";\nimport * as i10 from \"@ng-bootstrap/ng-bootstrap\";\nimport * as i11 from \"../../components/breadcrumb/breadcrumb.component\";\nimport * as i12 from \"../../../@theme/directives/label.directive\";\nconst _c0 = [\"fileInput\"];\nfunction ProjectManagementComponent_input_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"input\", 20);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function ProjectManagementComponent_input_10_Template_input_ngModelChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.search, $event) || (ctx_r2.search = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.search);\n  }\n}\nfunction ProjectManagementComponent_button_16_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 21);\n    i0.ɵɵlistener(\"click\", function ProjectManagementComponent_button_16_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r2 = i0.ɵɵnextContext();\n      const dialog_r5 = i0.ɵɵreference(32);\n      return i0.ɵɵresetView(ctx_r2.addNew(dialog_r5));\n    });\n    i0.ɵɵelement(1, \"i\", 22);\n    i0.ɵɵtext(2, \" \\u65B0\\u589E\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProjectManagementComponent_tr_28_button_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 26);\n    i0.ɵɵlistener(\"click\", function ProjectManagementComponent_tr_28_button_6_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const item_r7 = i0.ɵɵnextContext().$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      const dialog_r5 = i0.ɵɵreference(32);\n      return i0.ɵɵresetView(ctx_r2.onSelectedBuildCase(item_r7, dialog_r5));\n    });\n    i0.ɵɵtext(1, \"\\u7DE8\\u8F2F\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProjectManagementComponent_tr_28_button_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 26);\n    i0.ɵɵlistener(\"click\", function ProjectManagementComponent_tr_28_button_7_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const item_r7 = i0.ɵɵnextContext().$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onRelatedDocument(item_r7.cID));\n    });\n    i0.ɵɵtext(1, \"\\u76F8\\u95DC\\u6587\\u4EF6\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProjectManagementComponent_tr_28_button_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 27);\n    i0.ɵɵlistener(\"click\", function ProjectManagementComponent_tr_28_button_8_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const item_r7 = i0.ɵɵnextContext().$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      const dialog_r5 = i0.ɵɵreference(32);\n      return i0.ɵɵresetView(ctx_r2.onDelete(item_r7, dialog_r5));\n    });\n    i0.ɵɵtext(1, \"\\u522A\\u9664\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProjectManagementComponent_tr_28_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\", 23);\n    i0.ɵɵtemplate(6, ProjectManagementComponent_tr_28_button_6_Template, 2, 0, \"button\", 24)(7, ProjectManagementComponent_tr_28_button_7_Template, 2, 0, \"button\", 24)(8, ProjectManagementComponent_tr_28_button_8_Template, 2, 0, \"button\", 25);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const item_r7 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r7.cID);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r7.CBuildCaseName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isRead);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isRead);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isDelete);\n  }\n}\nfunction ProjectManagementComponent_ng_template_31_div_23_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 54)(1, \"span\", 55);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"button\", 56);\n    i0.ɵɵlistener(\"click\", function ProjectManagementComponent_ng_template_31_div_23_Template_button_click_3_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.clearImage());\n    });\n    i0.ɵɵelement(4, \"i\", 57);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r2.fileName);\n  }\n}\nfunction ProjectManagementComponent_ng_template_31_img_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 58);\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"src\", ctx_r2.imageUrl, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction ProjectManagementComponent_ng_template_31_img_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 58);\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"src\", ctx_r2.selectedBuildCase.CFrontImage, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction ProjectManagementComponent_ng_template_31_nb_option_34_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 59);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const status_r12 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", status_r12);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", status_r12.CTitle, \" \");\n  }\n}\nfunction ProjectManagementComponent_ng_template_31_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-card\", 28)(1, \"nb-card-header\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"nb-card-body\", 29)(4, \"div\", 30)(5, \"label\", 31);\n    i0.ɵɵtext(6, \"\\u5EFA\\u6848\\u540D\\u7A31 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"input\", 32);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function ProjectManagementComponent_ng_template_31_Template_input_ngModelChange_7_listener($event) {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.selectedBuildCase.CBuildCaseName, $event) || (ctx_r2.selectedBuildCase.CBuildCaseName = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 33)(9, \"div\", 34)(10, \"label\", 35);\n    i0.ɵɵtext(11, \"\\u524D\\u53F0\\u5716\\u7247 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"span\", 36);\n    i0.ɵɵtext(13, \"\\u53EA\\u63A5\\u53D7pdf, \\u5716\\u7247\\u6A94\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"span\", 36);\n    i0.ɵɵtext(15, \"\\u5EFA\\u8B70\\u5C3A\\u5BF8:\\u76F4\\u5F0F 360 x 480 px, \");\n    i0.ɵɵelementStart(16, \"p\", 36);\n    i0.ɵɵtext(17, \"\\u6A6B\\u5F0F 480 x 360 px\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(18, \"div\", 37)(19, \"input\", 38);\n    i0.ɵɵlistener(\"change\", function ProjectManagementComponent_ng_template_31_Template_input_change_19_listener($event) {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onFileSelected($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"label\", 39);\n    i0.ɵɵelement(21, \"i\", 40);\n    i0.ɵɵtext(22, \" \\u4E0A\\u50B3 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(23, ProjectManagementComponent_ng_template_31_div_23_Template, 5, 1, \"div\", 41)(24, ProjectManagementComponent_ng_template_31_img_24_Template, 1, 1, \"img\", 42)(25, ProjectManagementComponent_ng_template_31_img_25_Template, 1, 1, \"img\", 42);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(26, \"div\", 43)(27, \"label\", 44);\n    i0.ɵɵtext(28, \"\\u7CFB\\u7D71\\u64CD\\u4F5C\\u8AAA\\u660E \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(29, \"textarea\", 45);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function ProjectManagementComponent_ng_template_31_Template_textarea_ngModelChange_29_listener($event) {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.selectedBuildCase.CSystemInstruction, $event) || (ctx_r2.selectedBuildCase.CSystemInstruction = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(30, \"div\", 46)(31, \"label\", 47);\n    i0.ɵɵtext(32, \"\\u72C0\\u614B\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(33, \"nb-select\", 48);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function ProjectManagementComponent_ng_template_31_Template_nb_select_ngModelChange_33_listener($event) {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.selectedStatus, $event) || (ctx_r2.selectedStatus = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtemplate(34, ProjectManagementComponent_ng_template_31_nb_option_34_Template, 2, 2, \"nb-option\", 49);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(35, \"div\", 43)(36, \"label\", 50);\n    i0.ɵɵtext(37, \"\\u7C3D\\u7F72\\u6587\\u4EF6\\u6CE8\\u610F\\u4E8B\\u9805\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(38, \"textarea\", 51);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function ProjectManagementComponent_ng_template_31_Template_textarea_ngModelChange_38_listener($event) {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.selectedBuildCase.CFinalFileNotice, $event) || (ctx_r2.selectedBuildCase.CFinalFileNotice = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(39, \"nb-card-footer\", 18)(40, \"button\", 52);\n    i0.ɵɵlistener(\"click\", function ProjectManagementComponent_ng_template_31_Template_button_click_40_listener() {\n      const ref_r13 = i0.ɵɵrestoreView(_r10).dialogRef;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onClose(ref_r13));\n    });\n    i0.ɵɵtext(41);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(42, \"button\", 53);\n    i0.ɵɵlistener(\"click\", function ProjectManagementComponent_ng_template_31_Template_button_click_42_listener() {\n      const ref_r13 = i0.ɵɵrestoreView(_r10).dialogRef;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onSubmit(ref_r13));\n    });\n    i0.ɵɵtext(43, \"\\u5132\\u5B58\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.isNew ? \"\\u65B0\\u589E\\u5EFA\\u6848\" : \"\\u7DE8\\u8F2F\\u5EFA\\u6848\", \" \");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.selectedBuildCase.CBuildCaseName);\n    i0.ɵɵadvance(16);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.fileName);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.imageUrl);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.selectedBuildCase.CFrontImage && !ctx_r2.imageUrl);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.selectedBuildCase.CSystemInstruction);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.selectedStatus);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.statusOptions);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.selectedBuildCase.CFinalFileNotice);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r2.isNew ? \"\\u53D6\\u6D88\" : \"\\u95DC\\u9589\");\n  }\n}\nexport class ProjectManagementComponent extends BaseComponent {\n  constructor(_allow, dialogService, message, valid, _buildCaseService, router, _utilityService) {\n    super(_allow);\n    this._allow = _allow;\n    this.dialogService = dialogService;\n    this.message = message;\n    this.valid = valid;\n    this._buildCaseService = _buildCaseService;\n    this.router = router;\n    this._utilityService = _utilityService;\n    this.pageFirst = 1;\n    this.pageSize = 10;\n    this.pageIndex = 1;\n    this.totalRecords = 0;\n    this.statusOptions = [{\n      cID: 0,\n      CValue: 0,\n      CTitle: '停用'\n    }, {\n      cID: 1,\n      CValue: 1,\n      CTitle: '啟用'\n    }];\n    this.isConfirmOptions = [{\n      cID: 0,\n      CValue: 0,\n      CTitle: '未確認'\n    }, {\n      cID: 1,\n      CValue: 1,\n      CTitle: '已確認'\n    }];\n    // CFinalFileNotice: string = \"\"\n    this.listBuildCase = [];\n    this.search = \"\";\n    this.initBuildCase = {\n      CBuildCaseName: '',\n      CFrontImage: '',\n      CSystemInstruction: '',\n      ImageList: null,\n      cID: undefined,\n      CStatus: undefined,\n      CFinalFileNotice: ''\n    };\n    this.isNew = true;\n    this.imgSrc = null;\n    this.imageUrl = null;\n    this.fileName = null;\n  }\n  onRelatedDocument(id) {\n    this.router.navigateByUrl(`/pages/related-documents/${id}`);\n  }\n  ngOnInit() {\n    this.getListBuildCase().subscribe();\n    this.selectedStatus = this.statusOptions[0];\n    this.selectedIsConfirm = this.isConfirmOptions[0];\n    this.selectedBuildCase = {\n      ...this.initBuildCase\n    };\n  }\n  getListBuildCase() {\n    return this._buildCaseService.apiBuildCaseGetAllBuildCasePost$Json({\n      body: {\n        PageIndex: this.pageIndex,\n        PageSize: this.pageSize,\n        CName: this.search,\n        CIsPagi: true\n      }\n    }).pipe(tap(res => {\n      if (res.Entries && res.StatusCode == 0) {\n        this.listBuildCase = res.Entries ?? [];\n        this.totalRecords = res.TotalItems;\n      }\n    }));\n  }\n  addNew(ref) {\n    this.isNew = true;\n    this.clearForm();\n    this.selectedStatus = this.statusOptions[0];\n    this.selectedBuildCase.CStatus = this.statusOptions[0].CValue;\n    this.selectedIsConfirm = this.isConfirmOptions[0];\n    this.selectedBuildCase.CIsConfirm = this.isConfirmOptions[0].CValue;\n    this.dialogService.open(ref);\n  }\n  getBase64Image(file) {\n    return new Promise((resolve, reject) => {\n      const reader = new FileReader();\n      reader.readAsDataURL(file);\n      reader.onload = () => resolve(reader.result);\n      reader.onerror = error => reject(error);\n    });\n  }\n  onFileSelected(event) {\n    const file = event.target.files[0];\n    const fileRegex = /pdf|jpg|jpeg|png/i;\n    if (!fileRegex.test(file.type)) {\n      this.message.showErrorMSG('檔案格式錯誤，僅限pdf、圖片檔');\n      return;\n    }\n    if (file) {\n      const allowedTypes = ['image/jpeg', 'image/jpg', 'application/pdf'];\n      if (allowedTypes.includes(file.type)) {\n        this.fileName = file.name;\n        const reader = new FileReader();\n        reader.onload = e => {\n          this.imageUrl = e.target.result;\n          if (this.fileInput) {\n            this.fileInput.nativeElement.value = null;\n          }\n        };\n        reader.readAsDataURL(file);\n      }\n    }\n  }\n  clearImage() {\n    if (this.imageUrl) {\n      this.imageUrl = null;\n      this.fileName = null;\n      if (this.fileInput) {\n        this.fileInput.nativeElement.value = null; // Xóa giá trị input file\n      }\n    }\n  }\n  clearForm() {\n    this.selectedBuildCase = {\n      ...this.initBuildCase\n    };\n    this.clearImage();\n  }\n  onSelectedBuildCase(data, ref) {\n    this.isNew = false;\n    this.dialogService.open(ref);\n    this.clearImage();\n    this.selectedBuildCase = _.cloneDeep(data);\n    this.selectedBuildCase.CSystemInstruction = this._utilityService.htmltoText(data.CSystemInstruction);\n    this.selectedStatus = this.statusOptions[this.selectedBuildCase.CStatus];\n  }\n  validation() {\n    this.valid.clear();\n    this.valid.required('[建案名稱]', this.selectedBuildCase.CBuildCaseName);\n    this.valid.isStringMaxLength('[建案名稱]', this.selectedBuildCase.CBuildCaseName, 50);\n    if (this.isNew) {\n      this.valid.required('[前台圖片]', this.selectedBuildCase.CFrontImage);\n    }\n    this.valid.required('[系統操作說明]', this.selectedBuildCase.CSystemInstruction);\n    this.valid.required('[狀態]', this.selectedBuildCase.CStatus?.toString());\n  }\n  removeBase64Prefix(base64String) {\n    const prefixIndex = base64String.indexOf(\",\");\n    if (prefixIndex !== -1) {\n      return base64String.substring(prefixIndex + 1);\n    }\n    return base64String;\n  }\n  nl2br(str) {\n    if (typeof str === 'undefined' || str === null) {\n      return '';\n    }\n    return str.replace(/\\n/g, '<br>');\n    ;\n  }\n  onSubmit(ref) {\n    this.selectedBuildCase.CStatus = this.selectedStatus.CValue;\n    const requestBody = {\n      CBuildCaseName: this.selectedBuildCase.CBuildCaseName,\n      CStatus: this.selectedStatus.CValue,\n      CSystemInstruction: this.nl2br(this.selectedBuildCase.CSystemInstruction),\n      CIsConfirm: this.selectedIsConfirm.CValue == 0 ? false : true,\n      CFinalFileNotice: this.selectedBuildCase.CFinalFileNotice\n    };\n    if (this.isNew && this.imageUrl) {\n      // NEW\n      this.selectedBuildCase.CFrontImage = this.removeBase64Prefix(this.imageUrl); // as string\n      requestBody.CFrontImage = this.removeBase64Prefix(this.imageUrl);\n    } else {\n      // EDIT\n      if (this.imageUrl) {\n        requestBody.CFrontImage = this.removeBase64Prefix(this.imageUrl);\n      }\n      requestBody.CBuildCaseID = this.selectedBuildCase.cID;\n    }\n    this.validation();\n    if (this.valid.errorMessages.length > 0) {\n      this.message.showErrorMSGs(this.valid.errorMessages);\n      return;\n    }\n    this._buildCaseService.apiBuildCaseSaveBuildCasePost$Json({\n      body: requestBody\n    }).pipe(tap(res => {\n      if (res.StatusCode === 0) {\n        this.message.showSucessMSG(\"執行成功\");\n      } else {\n        this.message.showSucessMSG(\"建案名稱不可重複\");\n      }\n    }), concatMap(() => this.getListBuildCase()), finalize(() => ref.close())).subscribe();\n  }\n  pageChanged(newPage) {\n    this.pageIndex = newPage;\n    this.getListBuildCase().subscribe();\n  }\n  onDelete(data, ref) {\n    if (window.confirm(`確定要刪除【項目${data.CBuildCaseName}】?`)) {\n      this._buildCaseService.apiBuildCaseDeleteBuildCasePost$Json({\n        body: {\n          \"CBuildCaseID\": data.cID\n        }\n      }).pipe(tap(res => {\n        if (res.StatusCode === 0) {\n          this.message.showSucessMSG(\"執行成功\");\n        } else {\n          this.message.showErrorMSG(res.Message);\n        }\n      }), concatMap(() => this.getListBuildCase()), finalize(() => ref.close())).subscribe();\n    }\n  }\n  onClose(ref) {\n    ref.close();\n  }\n  static {\n    this.ɵfac = function ProjectManagementComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || ProjectManagementComponent)(i0.ɵɵdirectiveInject(i1.AllowHelper), i0.ɵɵdirectiveInject(i2.NbDialogService), i0.ɵɵdirectiveInject(i3.MessageService), i0.ɵɵdirectiveInject(i4.ValidationHelper), i0.ɵɵdirectiveInject(i5.BuildCaseService), i0.ɵɵdirectiveInject(i6.Router), i0.ɵɵdirectiveInject(i7.UtilityService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ProjectManagementComponent,\n      selectors: [[\"ngx-project-management\"]],\n      viewQuery: function ProjectManagementComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.fileInput = _t.first);\n        }\n      },\n      standalone: true,\n      features: [i0.ɵɵInheritDefinitionFeature, i0.ɵɵStandaloneFeature],\n      decls: 33,\n      vars: 6,\n      consts: [[\"dialog\", \"\"], [\"accent\", \"success\"], [1, \"row\"], [1, \"col-1\"], [\"for\", \"project\", 1, \"text-nowrap\", \"mr-1\", \"h-full\", \"mt-2\"], [1, \"col-5\"], [\"type\", \"text\", \"nbInput\", \"\", 3, \"ngModel\", \"ngModelChange\", 4, \"ngIf\"], [1, \"col-6\"], [1, \"d-flex\", \"justify-content-end\"], [1, \"btn\", \"btn-info\", \"mr-2\", 3, \"click\"], [1, \"fas\", \"fa-search\", \"mr-1\"], [\"class\", \"btn btn-info\", 3, \"click\", 4, \"ngIf\"], [1, \"table-responsive\", \"mt-4\"], [1, \"table\", \"table-striped\", \"border\", 2, \"min-width\", \"1000px\", \"background-color\", \"#f3f3f3\"], [2, \"background-color\", \"#27ae60\", \"color\", \"white\"], [\"scope\", \"col\", 1, \"col-1\"], [\"scope\", \"col\", 1, \"col-2\", \"text-center\"], [4, \"ngFor\", \"ngForOf\"], [1, \"d-flex\", \"justify-content-center\"], [\"aria-label\", \"Pagination\", 3, \"pageChange\", \"page\", \"pageSize\", \"collectionSize\"], [\"type\", \"text\", \"nbInput\", \"\", 3, \"ngModelChange\", \"ngModel\"], [1, \"btn\", \"btn-info\", 3, \"click\"], [1, \"fas\", \"fa-plus\", \"mr-1\"], [1, \"text-center\", \"w-32\"], [\"class\", \"btn btn-outline-primary btn-sm m-1\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"btn btn-outline-primary btn-sm m-1 text-red-500 border-red-500\", 3, \"click\", 4, \"ngIf\"], [1, \"btn\", \"btn-outline-primary\", \"btn-sm\", \"m-1\", 3, \"click\"], [1, \"btn\", \"btn-outline-primary\", \"btn-sm\", \"m-1\", \"text-red-500\", \"border-red-500\", 3, \"click\"], [2, \"width\", \"550px\", \"max-height\", \"95vh\"], [1, \"px-4\"], [1, \"form-group\"], [\"for\", \"imageName\", \"baseLabel\", \"\", 1, \"required-field\", \"mr-4\", 2, \"min-width\", \"100px\"], [\"type\", \"text\", \"nbInput\", \"\", \"placeholder\", \"\\u5EFA\\u6848\\u540D\\u7A31\", 1, \"w-full\", 3, \"ngModelChange\", \"ngModel\"], [1, \"form-group\", \"d-flex\", \"align-items-baseline\"], [1, \"d-flex\", \"flex-col\", \"mr-3\"], [\"for\", \"file\", \"baseLabel\", \"\", 1, \"mb-0\", 2, \"min-width\", \"100px\"], [2, \"font-size\", \"10px\", \"color\", \"red\"], [1, \"flex\", \"flex-col\", \"items-start\", \"space-y-4\"], [\"type\", \"file\", \"id\", \"fileInput\", \"accept\", \"image/jpeg, image/jpg, application/pdf\", 1, \"hidden\", 2, \"display\", \"none\", 3, \"change\"], [\"for\", \"fileInput\", 1, \"cursor-pointer\", \"bg-blue-500\", \"hover:bg-blue-700\", \"text-white\", \"font-bold\", \"py-2\", \"px-4\", \"rounded\"], [1, \"fa-solid\", \"fa-cloud-arrow-up\", \"mr-2\"], [\"class\", \"flex items-center space-x-2\", 4, \"ngIf\"], [\"alt\", \"Uploaded Image\", \"class\", \"max-w-xs h-auto rounded-md\", 3, \"src\", 4, \"ngIf\"], [1, \"form-group\", \"d-flex\", \"mt-2\"], [\"for\", \"remark\", \"baseLabel\", \"\", 1, \"required-field\", \"mr-4\", 2, \"min-width\", \"100px\"], [\"contenteditable\", \"\", \"name\", \"remark\", \"id\", \"remark\", \"rows\", \"5\", \"nbInput\", \"\", 1, \"w-full\", 2, \"resize\", \"none\", 3, \"ngModelChange\", \"ngModel\"], [1, \"form-group\", \"d-flex\"], [\"for\", \"remark\", \"baseLabel\", \"\", 1, \"mr-4\", 2, \"min-width\", \"100px\"], [\"placeholder\", \"Select Status\", 1, \"w-full\", 3, \"ngModelChange\", \"ngModel\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"for\", \"finalfilenotice\", \"baseLabel\", \"\", 1, \"mr-4\", 2, \"min-width\", \"100px\"], [\"name\", \"finalfilenotice\", \"id\", \"finalfilenotice\", \"rows\", \"5\", \"nbInput\", \"\", 1, \"w-full\", 2, \"resize\", \"none\", 3, \"ngModelChange\", \"ngModel\"], [1, \"btn\", \"btn-danger\", \"btn-sm\", \"mr-4\", 3, \"click\"], [1, \"btn\", \"btn-success\", \"btn-sm\", 3, \"click\"], [1, \"flex\", \"items-center\", \"space-x-2\"], [1, \"text-gray-600\"], [\"type\", \"button\", 1, \"text-red-500\", \"hover:text-red-700\", 3, \"click\"], [1, \"fa-solid\", \"fa-trash\"], [\"alt\", \"Uploaded Image\", 1, \"max-w-xs\", \"h-auto\", \"rounded-md\", 3, \"src\"], [3, \"value\"]],\n      template: function ProjectManagementComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"nb-card\", 1)(1, \"nb-card-header\");\n          i0.ɵɵelement(2, \"ngx-breadcrumb\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(3, \"nb-card-body\")(4, \"div\", 2)(5, \"div\", 3)(6, \"label\", 4);\n          i0.ɵɵtext(7, \"\\u5EFA\\u6848\\u540D\\u7A31 \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(8, \"div\", 5)(9, \"nb-form-field\");\n          i0.ɵɵtemplate(10, ProjectManagementComponent_input_10_Template, 1, 1, \"input\", 6);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(11, \"div\", 7)(12, \"div\", 8)(13, \"button\", 9);\n          i0.ɵɵlistener(\"click\", function ProjectManagementComponent_Template_button_click_13_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.getListBuildCase().subscribe());\n          });\n          i0.ɵɵelement(14, \"i\", 10);\n          i0.ɵɵtext(15, \" \\u67E5\\u8A62 \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(16, ProjectManagementComponent_button_16_Template, 3, 0, \"button\", 11);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(17, \"div\", 12)(18, \"table\", 13)(19, \"thead\")(20, \"tr\", 14)(21, \"th\", 15);\n          i0.ɵɵtext(22, \"ID\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(23, \"th\", 15);\n          i0.ɵɵtext(24, \"\\u5EFA\\u6848\\u540D\\u7A31\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(25, \"th\", 16);\n          i0.ɵɵtext(26, \"\\u52D5\\u4F5C\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(27, \"tbody\");\n          i0.ɵɵtemplate(28, ProjectManagementComponent_tr_28_Template, 9, 5, \"tr\", 17);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(29, \"nb-card-footer\", 18)(30, \"ngb-pagination\", 19);\n          i0.ɵɵtwoWayListener(\"pageChange\", function ProjectManagementComponent_Template_ngb_pagination_pageChange_30_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.pageIndex, $event) || (ctx.pageIndex = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵlistener(\"pageChange\", function ProjectManagementComponent_Template_ngb_pagination_pageChange_30_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.pageChanged($event));\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(31, ProjectManagementComponent_ng_template_31_Template, 44, 10, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor);\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(10);\n          i0.ɵɵproperty(\"ngIf\", ctx.isRead);\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"ngIf\", ctx.isCreate);\n          i0.ɵɵadvance(12);\n          i0.ɵɵproperty(\"ngForOf\", ctx.listBuildCase);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtwoWayProperty(\"page\", ctx.pageIndex);\n          i0.ɵɵproperty(\"pageSize\", ctx.pageSize)(\"collectionSize\", ctx.totalRecords);\n        }\n      },\n      dependencies: [CommonModule, i8.NgForOf, i8.NgIf, SharedModule, i9.DefaultValueAccessor, i9.NgControlStatus, i9.NgModel, i2.NbCardComponent, i2.NbCardBodyComponent, i2.NbCardFooterComponent, i2.NbCardHeaderComponent, i2.NbInputDirective, i2.NbSelectComponent, i2.NbOptionComponent, i2.NbFormFieldComponent, i10.NgbPagination, i11.BreadcrumbComponent, i12.BaseLabelDirective],\n      styles: [\"/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsImZpbGUiOiJwcm9qZWN0LW1hbmFnZW1lbnQuY29tcG9uZW50LnNjc3MifQ== */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvcGFnZXMvY29uc3RydWN0aW9uLXByb2plY3QtbWFuYWdlbWVudC9wcm9qZWN0LW1hbmFnZW1lbnQvcHJvamVjdC1tYW5hZ2VtZW50LmNvbXBvbmVudC5zY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiI7QUFDQSxvTEFBb0wiLCJzb3VyY2VSb290IjoiIn0= */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "concatMap", "finalize", "tap", "BaseComponent", "SharedModule", "_", "i0", "ɵɵelementStart", "ɵɵtwoWayListener", "ProjectManagementComponent_input_10_Template_input_ngModelChange_0_listener", "$event", "ɵɵrestoreView", "_r2", "ctx_r2", "ɵɵnextContext", "ɵɵtwoWayBindingSet", "search", "ɵɵresetView", "ɵɵelementEnd", "ɵɵtwoWayProperty", "ɵɵlistener", "ProjectManagementComponent_button_16_Template_button_click_0_listener", "_r4", "dialog_r5", "ɵɵreference", "addNew", "ɵɵelement", "ɵɵtext", "ProjectManagementComponent_tr_28_button_6_Template_button_click_0_listener", "_r6", "item_r7", "$implicit", "onSelectedBuildCase", "ProjectManagementComponent_tr_28_button_7_Template_button_click_0_listener", "_r8", "onRelatedDocument", "cID", "ProjectManagementComponent_tr_28_button_8_Template_button_click_0_listener", "_r9", "onDelete", "ɵɵtemplate", "ProjectManagementComponent_tr_28_button_6_Template", "ProjectManagementComponent_tr_28_button_7_Template", "ProjectManagementComponent_tr_28_button_8_Template", "ɵɵadvance", "ɵɵtextInterpolate", "CBuildCaseName", "ɵɵproperty", "isRead", "isDelete", "ProjectManagementComponent_ng_template_31_div_23_Template_button_click_3_listener", "_r11", "clearImage", "fileName", "imageUrl", "ɵɵsanitizeUrl", "selectedBuildCase", "CFrontImage", "status_r12", "ɵɵtextInterpolate1", "<PERSON><PERSON><PERSON>", "ProjectManagementComponent_ng_template_31_Template_input_ngModelChange_7_listener", "_r10", "ProjectManagementComponent_ng_template_31_Template_input_change_19_listener", "onFileSelected", "ProjectManagementComponent_ng_template_31_div_23_Template", "ProjectManagementComponent_ng_template_31_img_24_Template", "ProjectManagementComponent_ng_template_31_img_25_Template", "ProjectManagementComponent_ng_template_31_Template_textarea_ngModelChange_29_listener", "CSystemInstruction", "ProjectManagementComponent_ng_template_31_Template_nb_select_ngModelChange_33_listener", "selectedStatus", "ProjectManagementComponent_ng_template_31_nb_option_34_Template", "ProjectManagementComponent_ng_template_31_Template_textarea_ngModelChange_38_listener", "CFinalFileNotice", "ProjectManagementComponent_ng_template_31_Template_button_click_40_listener", "ref_r13", "dialogRef", "onClose", "ProjectManagementComponent_ng_template_31_Template_button_click_42_listener", "onSubmit", "isNew", "statusOptions", "ProjectManagementComponent", "constructor", "_allow", "dialogService", "message", "valid", "_buildCaseService", "router", "_utilityService", "pageFirst", "pageSize", "pageIndex", "totalRecords", "CValue", "isConfirmOptions", "listBuildCase", "initBuildCase", "ImageList", "undefined", "CStatus", "imgSrc", "id", "navigateByUrl", "ngOnInit", "getListBuildCase", "subscribe", "selectedIsConfirm", "apiBuildCaseGetAllBuildCasePost$Json", "body", "PageIndex", "PageSize", "CName", "CIsPagi", "pipe", "res", "Entries", "StatusCode", "TotalItems", "ref", "clearForm", "CIsConfirm", "open", "getBase64Image", "file", "Promise", "resolve", "reject", "reader", "FileReader", "readAsDataURL", "onload", "result", "onerror", "error", "event", "target", "files", "fileRegex", "test", "type", "showErrorMSG", "allowedTypes", "includes", "name", "e", "fileInput", "nativeElement", "value", "data", "cloneDeep", "htmltoText", "validation", "clear", "required", "isStringMaxLength", "toString", "removeBase64Prefix", "base64String", "prefixIndex", "indexOf", "substring", "nl2br", "str", "replace", "requestBody", "CBuildCaseID", "errorMessages", "length", "showErrorMSGs", "apiBuildCaseSaveBuildCasePost$Json", "showSucessMSG", "close", "pageChanged", "newPage", "window", "confirm", "apiBuildCaseDeleteBuildCasePost$Json", "Message", "ɵɵdirectiveInject", "i1", "AllowHelper", "i2", "NbDialogService", "i3", "MessageService", "i4", "ValidationHelper", "i5", "BuildCaseService", "i6", "Router", "i7", "UtilityService", "selectors", "viewQuery", "ProjectManagementComponent_Query", "rf", "ctx", "ProjectManagementComponent_input_10_Template", "ProjectManagementComponent_Template_button_click_13_listener", "_r1", "ProjectManagementComponent_button_16_Template", "ProjectManagementComponent_tr_28_Template", "ProjectManagementComponent_Template_ngb_pagination_pageChange_30_listener", "ProjectManagementComponent_ng_template_31_Template", "ɵɵtemplateRefExtractor", "isCreate", "i8", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "i9", "DefaultValueAccessor", "NgControlStatus", "NgModel", "NbCardComponent", "NbCardBodyComponent", "NbCardFooterComponent", "NbCardHeaderComponent", "NbInputDirective", "NbSelectComponent", "NbOptionComponent", "NbFormFieldComponent", "i10", "NgbPagination", "i11", "BreadcrumbComponent", "i12", "BaseLabelDirective", "styles"], "sources": ["C:\\Users\\<USER>\\Documents\\jeansalechange\\adminFront\\src\\app\\pages\\construction-project-management\\project-management\\project-management.component.ts", "C:\\Users\\<USER>\\Documents\\jeansalechange\\adminFront\\src\\app\\pages\\construction-project-management\\project-management\\project-management.component.html"], "sourcesContent": ["import { Component, ElementRef, OnInit, ViewChild } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { NbDialogService } from '@nebular/theme';\r\nimport { MessageService } from 'src/app/shared/services/message.service';\r\nimport { ValidationHelper } from 'src/app/shared/helper/validationHelper';\r\nimport { BuildCaseService } from 'src/services/api/services';\r\nimport { BuildCaseGetListReponse, SaveBuildCaseArgs } from 'src/services/api/models';\r\nimport { Router } from '@angular/router';\r\nimport { AllowHelper } from 'src/app/shared/helper/allowHelper';\r\nimport { FilterListItemsPipe } from 'src/app/@theme/pipes/searchText.pipe';\r\nimport { concatMap, finalize, tap } from 'rxjs';\r\nimport { BaseComponent } from '../../components/base/baseComponent';\r\nimport { SharedModule } from '../../components/shared.module';\r\nimport { UtilityService } from 'src/app/shared/services/utility.service';\r\nimport { FilterByKeyPipe } from 'src/app/@theme/pipes/filterByKey.pipe';\r\nimport * as _ from 'lodash';\r\n@Component({\r\n  selector: 'ngx-project-management',\r\n  templateUrl: './project-management.component.html',\r\n  styleUrls: ['./project-management.component.scss'],\r\n  standalone: true,\r\n  imports: [\r\n    CommonModule,\r\n    SharedModule,\r\n    FilterListItemsPipe,\r\n    FilterByKeyPipe,\r\n  ],\r\n})\r\n\r\nexport class ProjectManagementComponent extends BaseComponent implements OnInit {\r\n  constructor(\r\n    private _allow: AllowHelper,\r\n    private dialogService: NbDialogService,\r\n    private message: MessageService,\r\n    private valid: ValidationHelper,\r\n    private _buildCaseService: BuildCaseService,\r\n    private router: Router,\r\n    private _utilityService: UtilityService\r\n  ) {\r\n    super(_allow)\r\n  }\r\n\r\n  override pageFirst = 1;\r\n  override pageSize = 10;\r\n  override pageIndex = 1;\r\n  override totalRecords = 0;\r\n  statusOptions: any[] = [\r\n    {\r\n      cID: 0,\r\n      CValue: 0,\r\n      CTitle: '停用',\r\n    },\r\n    {\r\n      cID: 1,\r\n      CValue: 1,\r\n      CTitle: '啟用',\r\n    }\r\n  ]\r\n\r\n  isConfirmOptions: any[] = [\r\n    {\r\n      cID: 0,\r\n      CValue: 0,\r\n      CTitle: '未確認',\r\n    },\r\n    {\r\n      cID: 1,\r\n      CValue: 1,\r\n      CTitle: '已確認',\r\n    }\r\n  ]\r\n\r\n  selectedStatus: {\r\n    cID: number, CValue: number, CTitle: string\r\n  }\r\n  selectedIsConfirm: {\r\n    cID: number, CValue: number, CTitle: string\r\n  }\r\n  // CFinalFileNotice: string = \"\"\r\n\r\n  listBuildCase: BuildCaseGetListReponse[] = []\r\n  selectedBuildCase: BuildCaseGetListReponse & {\r\n    CIsConfirm?: number\r\n  }\r\n  search: string = \"\"\r\n\r\n  initBuildCase: BuildCaseGetListReponse = {\r\n    CBuildCaseName: '',\r\n    CFrontImage: '',\r\n    CSystemInstruction: '',\r\n    ImageList: null,\r\n    cID: undefined,\r\n    CStatus: undefined,\r\n    CFinalFileNotice: ''\r\n  }\r\n\r\n  isNew = true\r\n\r\n  imgSrc: string | ArrayBuffer | null = null;\r\n  @ViewChild('fileInput') fileInput!: ElementRef;\r\n\r\n  imageUrl: string | ArrayBuffer | null = null;\r\n  fileName: string | null = null;\r\n\r\n  onRelatedDocument(id: any) {\r\n    this.router.navigateByUrl(`/pages/related-documents/${id}`)\r\n  }\r\n\r\n  override ngOnInit(): void {\r\n    this.getListBuildCase().subscribe();\r\n    this.selectedStatus = this.statusOptions[0]\r\n    this.selectedIsConfirm = this.isConfirmOptions[0]\r\n    this.selectedBuildCase = { ...this.initBuildCase }\r\n  }\r\n\r\n  getListBuildCase() {\r\n    return this._buildCaseService.apiBuildCaseGetAllBuildCasePost$Json({ body: {\r\n      PageIndex: this.pageIndex,\r\n      PageSize: this.pageSize,\r\n      CName: this.search,\r\n      CIsPagi: true\r\n    } }).pipe(\r\n      tap(res => {\r\n        if (res.Entries && res.StatusCode == 0) {\r\n          this.listBuildCase = res.Entries! ?? []\r\n          this.totalRecords = res.TotalItems!\r\n        }\r\n      })\r\n    )\r\n  }\r\n\r\n  addNew(ref: any) {\r\n    this.isNew = true;\r\n    this.clearForm()\r\n    this.selectedStatus = this.statusOptions[0]\r\n    this.selectedBuildCase.CStatus = this.statusOptions[0].CValue\r\n    this.selectedIsConfirm = this.isConfirmOptions[0]\r\n    this.selectedBuildCase.CIsConfirm = this.isConfirmOptions[0].CValue\r\n    this.dialogService.open(ref)\r\n  }\r\n\r\n\r\n  getBase64Image(file: File): Promise<string | ArrayBuffer | null> {\r\n    return new Promise((resolve, reject) => {\r\n      const reader = new FileReader();\r\n      reader.readAsDataURL(file);\r\n      reader.onload = () => resolve(reader.result);\r\n      reader.onerror = error => reject(error);\r\n    });\r\n  }\r\n\r\n\r\n  onFileSelected(event: any) {\r\n    const file: File = event.target.files[0];\r\n    const fileRegex = /pdf|jpg|jpeg|png/i;\r\n    if (!fileRegex.test(file.type)) {\r\n      this.message.showErrorMSG('檔案格式錯誤，僅限pdf、圖片檔');\r\n      return;\r\n    }\r\n    if (file) {\r\n      const allowedTypes = ['image/jpeg', 'image/jpg', 'application/pdf'];\r\n      if (allowedTypes.includes(file.type)) {\r\n        this.fileName = file.name;\r\n        const reader = new FileReader();\r\n        reader.onload = (e: any) => {\r\n          this.imageUrl = e.target.result;\r\n          if (this.fileInput) {\r\n            this.fileInput.nativeElement.value = null;\r\n          }\r\n        };\r\n        reader.readAsDataURL(file);\r\n      }\r\n    }\r\n  }\r\n\r\n  clearImage() {\r\n    if (this.imageUrl) {\r\n      this.imageUrl = null;\r\n      this.fileName = null;\r\n      if (this.fileInput) {\r\n        this.fileInput.nativeElement.value = null; // Xóa giá trị input file\r\n      }\r\n    }\r\n  }\r\n\r\n  clearForm() {\r\n    this.selectedBuildCase = { ...this.initBuildCase }\r\n    this.clearImage()\r\n  }\r\n\r\n  onSelectedBuildCase(data: any, ref: any) {\r\n    this.isNew = false;\r\n    this.dialogService.open(ref);\r\n    this.clearImage()\r\n    this.selectedBuildCase = _.cloneDeep(data);\r\n    this.selectedBuildCase.CSystemInstruction = this._utilityService.htmltoText(data.CSystemInstruction)\r\n    this.selectedStatus = this.statusOptions[this.selectedBuildCase.CStatus!]\r\n  }\r\n\r\n  validation() {\r\n    this.valid.clear();\r\n    this.valid.required('[建案名稱]', this.selectedBuildCase.CBuildCaseName)\r\n    this.valid.isStringMaxLength('[建案名稱]', this.selectedBuildCase.CBuildCaseName, 50)\r\n    if (this.isNew) {\r\n      this.valid.required('[前台圖片]', this.selectedBuildCase.CFrontImage)\r\n    }\r\n    this.valid.required('[系統操作說明]', this.selectedBuildCase.CSystemInstruction)\r\n    this.valid.required('[狀態]', this.selectedBuildCase.CStatus?.toString())\r\n  }\r\n\r\n  removeBase64Prefix(base64String: any) {\r\n    const prefixIndex = base64String.indexOf(\",\");\r\n    if (prefixIndex !== -1) {\r\n      return base64String.substring(prefixIndex + 1);\r\n    }\r\n    return base64String;\r\n  }\r\n\r\n  nl2br(str: string) {\r\n    if (typeof str === 'undefined' || str === null) {\r\n      return '';\r\n    }\r\n    return str.replace(/\\n/g, '<br>');;\r\n  }\r\n\r\n  onSubmit(ref: any) {\r\n\r\n    this.selectedBuildCase.CStatus = this.selectedStatus.CValue\r\n    const requestBody: SaveBuildCaseArgs = {\r\n      CBuildCaseName: this.selectedBuildCase.CBuildCaseName,\r\n      CStatus: this.selectedStatus.CValue,\r\n      CSystemInstruction: this.nl2br(this.selectedBuildCase.CSystemInstruction!),\r\n      CIsConfirm: this.selectedIsConfirm.CValue == 0 ? false : true,\r\n      CFinalFileNotice: this.selectedBuildCase.CFinalFileNotice\r\n    }\r\n\r\n    if (this.isNew && this.imageUrl) { // NEW\r\n      this.selectedBuildCase.CFrontImage = this.removeBase64Prefix(this.imageUrl)// as string\r\n      requestBody.CFrontImage = this.removeBase64Prefix(this.imageUrl)\r\n    } else { // EDIT\r\n      if (this.imageUrl) {\r\n        requestBody.CFrontImage = this.removeBase64Prefix(this.imageUrl)\r\n      }\r\n      requestBody.CBuildCaseID = this.selectedBuildCase.cID\r\n    }\r\n    this.validation()\r\n    if (this.valid.errorMessages.length > 0) {\r\n      this.message.showErrorMSGs(this.valid.errorMessages);\r\n      return\r\n    }\r\n\r\n    this._buildCaseService.apiBuildCaseSaveBuildCasePost$Json({ body: requestBody }).pipe(\r\n      tap(res => {\r\n        if (res.StatusCode === 0) {\r\n          this.message.showSucessMSG(\"執行成功\");\r\n        } else {\r\n          this.message.showSucessMSG(\"建案名稱不可重複\")\r\n        }\r\n      }),\r\n      concatMap(() => this.getListBuildCase()),\r\n      finalize(() => ref.close())\r\n    ).subscribe();\r\n  }\r\n\r\n  pageChanged(newPage: number) {\r\n    this.pageIndex = newPage;\r\n    this.getListBuildCase().subscribe()\r\n  }\r\n\r\n  onDelete(data: any, ref: any) {\r\n    if (window.confirm(`確定要刪除【項目${data.CBuildCaseName}】?`)) {\r\n      this._buildCaseService.apiBuildCaseDeleteBuildCasePost$Json({\r\n        body: {\r\n          \"CBuildCaseID\": data.cID\r\n        }\r\n      }).pipe(\r\n        tap(res => {\r\n          if (res.StatusCode === 0) {\r\n            this.message.showSucessMSG(\"執行成功\");\r\n          } else {\r\n            this.message.showErrorMSG(res.Message!)\r\n          }\r\n        }),\r\n        concatMap(() => this.getListBuildCase()),\r\n        finalize(() => ref.close())\r\n      ).subscribe()\r\n    }\r\n  }\r\n\r\n  onClose(ref: any) {\r\n    ref.close();\r\n  }\r\n}\r\n", "<nb-card accent=\"success\">\r\n  <nb-card-header>\r\n    <ngx-breadcrumb></ngx-breadcrumb>\r\n  </nb-card-header>\r\n  <nb-card-body>\r\n    <div class=\"row\">\r\n      <div class=\"col-1\">\r\n        <label for=\"project\" class=\"text-nowrap mr-1 h-full mt-2\">建案名稱\r\n        </label>\r\n      </div>\r\n      <div class=\"col-5\">\r\n        <nb-form-field>\r\n          <input type=\"text\" nbInput [(ngModel)]=\"search\" *ngIf=\"isRead\">\r\n        </nb-form-field>\r\n      </div>\r\n      <div class=\"col-6\">\r\n        <div class=\"d-flex justify-content-end\">\r\n          <button class=\"btn btn-info  mr-2\" (click)=\"getListBuildCase().subscribe()\">\r\n            <i class=\"fas fa-search mr-1\"></i>\r\n            查詢\r\n          </button>\r\n          <button class=\"btn btn-info\" (click)=\"addNew(dialog)\" *ngIf=\"isCreate\">\r\n            <i class=\"fas fa-plus mr-1\"></i> 新增</button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <div class=\"table-responsive mt-4\">\r\n      <table class=\"table table-striped border \" style=\"min-width: 1000px; background-color:#f3f3f3;\">\r\n        <thead>\r\n          <tr style=\"background-color: #27ae60; color: white;\">\r\n            <th scope=\"col\" class=\"col-1 \">ID</th>\r\n            <th scope=\"col\" class=\"col-1\">建案名稱</th>\r\n            <th scope=\"col\" class=\"col-2 text-center\">動作</th>\r\n          </tr>\r\n        </thead>\r\n        <tbody>\r\n          <tr *ngFor=\"let item of listBuildCase; let i = index\">\r\n            <td>{{ item.cID}}</td>\r\n            <td>{{item.CBuildCaseName}}</td>\r\n            <td class=\"text-center w-32\">\r\n              <button class=\"btn btn-outline-primary btn-sm m-1\" (click)=\"onSelectedBuildCase(item, dialog)\"\r\n                *ngIf=\"isRead\">編輯</button>\r\n              <button class=\"btn btn-outline-primary btn-sm m-1\" (click)=\"onRelatedDocument(item.cID)\"\r\n                *ngIf=\"isRead\">相關文件</button>\r\n              <button class=\"btn btn-outline-primary btn-sm m-1 text-red-500 border-red-500\"\r\n                (click)=\"onDelete(item, dialog)\" *ngIf=\"isDelete\">刪除</button>\r\n            </td>\r\n          </tr>\r\n        </tbody>\r\n      </table>\r\n    </div>\r\n  </nb-card-body>\r\n  <nb-card-footer class=\"d-flex justify-content-center\">\r\n    <ngb-pagination [(page)]=\"pageIndex\" [pageSize]=\"pageSize\" [collectionSize]=\"totalRecords\"\r\n      (pageChange)=\"pageChanged($event)\" aria-label=\"Pagination\">\r\n    </ngb-pagination>\r\n  </nb-card-footer>\r\n</nb-card>\r\n\r\n<ng-template #dialog let-dialog let-ref=\"dialogRef\">\r\n  <nb-card style=\"width:550px; max-height: 95vh\">\r\n    <nb-card-header>\r\n      {{isNew ? \"新增建案\": \"編輯建案\"}}\r\n    </nb-card-header>\r\n    <nb-card-body class=\"px-4\">\r\n      <div class=\"form-group\">\r\n        <label for=\"imageName\" class=\"required-field mr-4\" style=\"min-width:100px\" baseLabel>建案名稱\r\n        </label>\r\n        <input type=\"text\" class=\"w-full\" nbInput placeholder=\"建案名稱\" [(ngModel)]=\"selectedBuildCase.CBuildCaseName\" />\r\n      </div>\r\n      <div class=\"form-group d-flex align-items-baseline\">\r\n        <div class=\"d-flex flex-col mr-3\">\r\n          <label for=\"file\" class=\"mb-0\" style=\"min-width:100px\" baseLabel>前台圖片\r\n          </label>\r\n          <span style=\"font-size: 10px; color:red;\">只接受pdf, 圖片檔</span>\r\n          <span style=\"font-size: 10px; color:red;\">建議尺寸:直式 360 x 480 px,\r\n            <p style=\"font-size: 10px; color:red;\">橫式 480 x 360 px</p>\r\n          </span>\r\n        </div>\r\n        <div class=\"flex flex-col items-start space-y-4\">\r\n          <input type=\"file\" id=\"fileInput\" accept=\"image/jpeg, image/jpg, application/pdf\" class=\"hidden\"\r\n            style=\"display: none\" (change)=\"onFileSelected($event)\">\r\n          <label for=\"fileInput\"\r\n            class=\"cursor-pointer bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded\">\r\n            <i class=\"fa-solid fa-cloud-arrow-up mr-2\"></i> 上傳\r\n          </label>\r\n          <div class=\"flex items-center space-x-2\" *ngIf=\"fileName\">\r\n            <span class=\"text-gray-600\">{{ fileName }}</span>\r\n            <button type=\"button\" (click)=\"clearImage()\" class=\"text-red-500 hover:text-red-700\">\r\n              <i class=\"fa-solid fa-trash\"></i>\r\n            </button>\r\n          </div>\r\n          <img [src]=\"imageUrl\" *ngIf=\"imageUrl\" alt=\"Uploaded Image\" class=\"max-w-xs h-auto rounded-md\">\r\n          <img [src]=\"selectedBuildCase.CFrontImage\" *ngIf=\"selectedBuildCase.CFrontImage && !imageUrl\"\r\n            alt=\"Uploaded Image\" class=\"max-w-xs h-auto rounded-md\">\r\n        </div>\r\n      </div>\r\n      <div class=\"form-group d-flex mt-2\">\r\n        <label for=\"remark\" style=\"min-width:100px\" class=\"required-field mr-4\" baseLabel>系統操作說明\r\n        </label>\r\n        <textarea contenteditable name=\"remark\" id=\"remark\" rows=\"5\" class=\"w-full\" style=\"resize: none;\" nbInput\r\n          [(ngModel)]=\"selectedBuildCase.CSystemInstruction!\"></textarea>\r\n      </div>\r\n      <div class=\"form-group d-flex\">\r\n        <label for=\"remark\" style=\"min-width:100px\" class=\"mr-4\" baseLabel>狀態</label>\r\n        <nb-select placeholder=\"Select Status\" [(ngModel)]=\"selectedStatus\" class=\"w-full\">\r\n          <nb-option *ngFor=\"let status of statusOptions\" [value]=\"status\">\r\n            {{ status.CTitle }}\r\n          </nb-option>\r\n        </nb-select>\r\n      </div>\r\n      <!-- <div class=\"form-group d-flex mt-2\">\r\n        <label for=\"remark\" style=\"min-width:100px\" class=\"mr-4\" baseLabel>資料確認</label>\r\n        <nb-select placeholder=\"Select Status\" [(ngModel)]=\"selectedIsConfirm\" class=\"w-full\">\r\n          <nb-option *ngFor=\"let status of isConfirmOptions\" [value]=\"status\">\r\n            {{ status.CTitle }}\r\n          </nb-option>\r\n        </nb-select>\r\n      </div> -->\r\n      <div class=\"form-group d-flex mt-2\">\r\n        <label for=\"finalfilenotice\" style=\"min-width:100px\" class=\"mr-4\" baseLabel>簽署文件注意事項</label>\r\n        <textarea name=\"finalfilenotice\" id=\"finalfilenotice\" rows=\"5\" class=\"w-full\" style=\"resize: none;\" nbInput\r\n          [(ngModel)]=\"selectedBuildCase.CFinalFileNotice\"></textarea>\r\n      </div>\r\n    </nb-card-body>\r\n    <nb-card-footer class=\"d-flex justify-content-center\">\r\n      <button class=\"btn btn-danger btn-sm mr-4\" (click)=\"onClose(ref)\">{{ isNew ? '取消' : '關閉'}}</button>\r\n      <button class=\"btn btn-success btn-sm\" (click)=\"onSubmit(ref)\">儲存</button>\r\n    </nb-card-footer>\r\n  </nb-card>\r\n</ng-template>\r\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAS9C,SAASC,SAAS,EAAEC,QAAQ,EAAEC,GAAG,QAAQ,MAAM;AAC/C,SAASC,aAAa,QAAQ,qCAAqC;AACnE,SAASC,YAAY,QAAQ,gCAAgC;AAG7D,OAAO,KAAKC,CAAC,MAAM,QAAQ;;;;;;;;;;;;;;;;;;ICHjBC,EAAA,CAAAC,cAAA,gBAA+D;IAApCD,EAAA,CAAAE,gBAAA,2BAAAC,4EAAAC,MAAA;MAAAJ,EAAA,CAAAK,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAP,EAAA,CAAAQ,aAAA;MAAAR,EAAA,CAAAS,kBAAA,CAAAF,MAAA,CAAAG,MAAA,EAAAN,MAAA,MAAAG,MAAA,CAAAG,MAAA,GAAAN,MAAA;MAAA,OAAAJ,EAAA,CAAAW,WAAA,CAAAP,MAAA;IAAA,EAAoB;IAA/CJ,EAAA,CAAAY,YAAA,EAA+D;;;;IAApCZ,EAAA,CAAAa,gBAAA,YAAAN,MAAA,CAAAG,MAAA,CAAoB;;;;;;IAS/CV,EAAA,CAAAC,cAAA,iBAAuE;IAA1CD,EAAA,CAAAc,UAAA,mBAAAC,sEAAA;MAAAf,EAAA,CAAAK,aAAA,CAAAW,GAAA;MAAA,MAAAT,MAAA,GAAAP,EAAA,CAAAQ,aAAA;MAAA,MAAAS,SAAA,GAAAjB,EAAA,CAAAkB,WAAA;MAAA,OAAAlB,EAAA,CAAAW,WAAA,CAASJ,MAAA,CAAAY,MAAA,CAAAF,SAAA,CAAc;IAAA,EAAC;IACnDjB,EAAA,CAAAoB,SAAA,YAAgC;IAACpB,EAAA,CAAAqB,MAAA,oBAAE;IAAArB,EAAA,CAAAY,YAAA,EAAS;;;;;;IAmB1CZ,EAAA,CAAAC,cAAA,iBACiB;IADkCD,EAAA,CAAAc,UAAA,mBAAAQ,2EAAA;MAAAtB,EAAA,CAAAK,aAAA,CAAAkB,GAAA;MAAA,MAAAC,OAAA,GAAAxB,EAAA,CAAAQ,aAAA,GAAAiB,SAAA;MAAA,MAAAlB,MAAA,GAAAP,EAAA,CAAAQ,aAAA;MAAA,MAAAS,SAAA,GAAAjB,EAAA,CAAAkB,WAAA;MAAA,OAAAlB,EAAA,CAAAW,WAAA,CAASJ,MAAA,CAAAmB,mBAAA,CAAAF,OAAA,EAAAP,SAAA,CAAiC;IAAA,EAAC;IAC7EjB,EAAA,CAAAqB,MAAA,mBAAE;IAAArB,EAAA,CAAAY,YAAA,EAAS;;;;;;IAC5BZ,EAAA,CAAAC,cAAA,iBACiB;IADkCD,EAAA,CAAAc,UAAA,mBAAAa,2EAAA;MAAA3B,EAAA,CAAAK,aAAA,CAAAuB,GAAA;MAAA,MAAAJ,OAAA,GAAAxB,EAAA,CAAAQ,aAAA,GAAAiB,SAAA;MAAA,MAAAlB,MAAA,GAAAP,EAAA,CAAAQ,aAAA;MAAA,OAAAR,EAAA,CAAAW,WAAA,CAASJ,MAAA,CAAAsB,iBAAA,CAAAL,OAAA,CAAAM,GAAA,CAA2B;IAAA,EAAC;IACvE9B,EAAA,CAAAqB,MAAA,+BAAI;IAAArB,EAAA,CAAAY,YAAA,EAAS;;;;;;IAC9BZ,EAAA,CAAAC,cAAA,iBACoD;IAAlDD,EAAA,CAAAc,UAAA,mBAAAiB,2EAAA;MAAA/B,EAAA,CAAAK,aAAA,CAAA2B,GAAA;MAAA,MAAAR,OAAA,GAAAxB,EAAA,CAAAQ,aAAA,GAAAiB,SAAA;MAAA,MAAAlB,MAAA,GAAAP,EAAA,CAAAQ,aAAA;MAAA,MAAAS,SAAA,GAAAjB,EAAA,CAAAkB,WAAA;MAAA,OAAAlB,EAAA,CAAAW,WAAA,CAASJ,MAAA,CAAA0B,QAAA,CAAAT,OAAA,EAAAP,SAAA,CAAsB;IAAA,EAAC;IAAkBjB,EAAA,CAAAqB,MAAA,mBAAE;IAAArB,EAAA,CAAAY,YAAA,EAAS;;;;;IARjEZ,EADF,CAAAC,cAAA,SAAsD,SAChD;IAAAD,EAAA,CAAAqB,MAAA,GAAa;IAAArB,EAAA,CAAAY,YAAA,EAAK;IACtBZ,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAqB,MAAA,GAAuB;IAAArB,EAAA,CAAAY,YAAA,EAAK;IAChCZ,EAAA,CAAAC,cAAA,aAA6B;IAK3BD,EAJA,CAAAkC,UAAA,IAAAC,kDAAA,qBACiB,IAAAC,kDAAA,qBAEA,IAAAC,kDAAA,qBAEmC;IAExDrC,EADE,CAAAY,YAAA,EAAK,EACF;;;;;IAVCZ,EAAA,CAAAsC,SAAA,GAAa;IAAbtC,EAAA,CAAAuC,iBAAA,CAAAf,OAAA,CAAAM,GAAA,CAAa;IACb9B,EAAA,CAAAsC,SAAA,GAAuB;IAAvBtC,EAAA,CAAAuC,iBAAA,CAAAf,OAAA,CAAAgB,cAAA,CAAuB;IAGtBxC,EAAA,CAAAsC,SAAA,GAAY;IAAZtC,EAAA,CAAAyC,UAAA,SAAAlC,MAAA,CAAAmC,MAAA,CAAY;IAEZ1C,EAAA,CAAAsC,SAAA,EAAY;IAAZtC,EAAA,CAAAyC,UAAA,SAAAlC,MAAA,CAAAmC,MAAA,CAAY;IAEqB1C,EAAA,CAAAsC,SAAA,EAAc;IAAdtC,EAAA,CAAAyC,UAAA,SAAAlC,MAAA,CAAAoC,QAAA,CAAc;;;;;;IA0CpD3C,EADF,CAAAC,cAAA,cAA0D,eAC5B;IAAAD,EAAA,CAAAqB,MAAA,GAAc;IAAArB,EAAA,CAAAY,YAAA,EAAO;IACjDZ,EAAA,CAAAC,cAAA,iBAAqF;IAA/DD,EAAA,CAAAc,UAAA,mBAAA8B,kFAAA;MAAA5C,EAAA,CAAAK,aAAA,CAAAwC,IAAA;MAAA,MAAAtC,MAAA,GAAAP,EAAA,CAAAQ,aAAA;MAAA,OAAAR,EAAA,CAAAW,WAAA,CAASJ,MAAA,CAAAuC,UAAA,EAAY;IAAA,EAAC;IAC1C9C,EAAA,CAAAoB,SAAA,YAAiC;IAErCpB,EADE,CAAAY,YAAA,EAAS,EACL;;;;IAJwBZ,EAAA,CAAAsC,SAAA,GAAc;IAAdtC,EAAA,CAAAuC,iBAAA,CAAAhC,MAAA,CAAAwC,QAAA,CAAc;;;;;IAK5C/C,EAAA,CAAAoB,SAAA,cAA+F;;;;IAA1FpB,EAAA,CAAAyC,UAAA,QAAAlC,MAAA,CAAAyC,QAAA,EAAAhD,EAAA,CAAAiD,aAAA,CAAgB;;;;;IACrBjD,EAAA,CAAAoB,SAAA,cAC0D;;;;IADrDpB,EAAA,CAAAyC,UAAA,QAAAlC,MAAA,CAAA2C,iBAAA,CAAAC,WAAA,EAAAnD,EAAA,CAAAiD,aAAA,CAAqC;;;;;IAa1CjD,EAAA,CAAAC,cAAA,oBAAiE;IAC/DD,EAAA,CAAAqB,MAAA,GACF;IAAArB,EAAA,CAAAY,YAAA,EAAY;;;;IAFoCZ,EAAA,CAAAyC,UAAA,UAAAW,UAAA,CAAgB;IAC9DpD,EAAA,CAAAsC,SAAA,EACF;IADEtC,EAAA,CAAAqD,kBAAA,MAAAD,UAAA,CAAAE,MAAA,MACF;;;;;;IA/CNtD,EADF,CAAAC,cAAA,kBAA+C,qBAC7B;IACdD,EAAA,CAAAqB,MAAA,GACF;IAAArB,EAAA,CAAAY,YAAA,EAAiB;IAGbZ,EAFJ,CAAAC,cAAA,uBAA2B,cACD,gBAC+D;IAAAD,EAAA,CAAAqB,MAAA,gCACrF;IAAArB,EAAA,CAAAY,YAAA,EAAQ;IACRZ,EAAA,CAAAC,cAAA,gBAA8G;IAAjDD,EAAA,CAAAE,gBAAA,2BAAAqD,kFAAAnD,MAAA;MAAAJ,EAAA,CAAAK,aAAA,CAAAmD,IAAA;MAAA,MAAAjD,MAAA,GAAAP,EAAA,CAAAQ,aAAA;MAAAR,EAAA,CAAAS,kBAAA,CAAAF,MAAA,CAAA2C,iBAAA,CAAAV,cAAA,EAAApC,MAAA,MAAAG,MAAA,CAAA2C,iBAAA,CAAAV,cAAA,GAAApC,MAAA;MAAA,OAAAJ,EAAA,CAAAW,WAAA,CAAAP,MAAA;IAAA,EAA8C;IAC7GJ,EADE,CAAAY,YAAA,EAA8G,EAC1G;IAGFZ,EAFJ,CAAAC,cAAA,cAAoD,cAChB,iBACiC;IAAAD,EAAA,CAAAqB,MAAA,iCACjE;IAAArB,EAAA,CAAAY,YAAA,EAAQ;IACRZ,EAAA,CAAAC,cAAA,gBAA0C;IAAAD,EAAA,CAAAqB,MAAA,iDAAW;IAAArB,EAAA,CAAAY,YAAA,EAAO;IAC5DZ,EAAA,CAAAC,cAAA,gBAA0C;IAAAD,EAAA,CAAAqB,MAAA,4DACxC;IAAArB,EAAA,CAAAC,cAAA,aAAuC;IAAAD,EAAA,CAAAqB,MAAA,iCAAe;IAE1DrB,EAF0D,CAAAY,YAAA,EAAI,EACrD,EACH;IAEJZ,EADF,CAAAC,cAAA,eAAiD,iBAEW;IAAlCD,EAAA,CAAAc,UAAA,oBAAA2C,4EAAArD,MAAA;MAAAJ,EAAA,CAAAK,aAAA,CAAAmD,IAAA;MAAA,MAAAjD,MAAA,GAAAP,EAAA,CAAAQ,aAAA;MAAA,OAAAR,EAAA,CAAAW,WAAA,CAAUJ,MAAA,CAAAmD,cAAA,CAAAtD,MAAA,CAAsB;IAAA,EAAC;IADzDJ,EAAA,CAAAY,YAAA,EAC0D;IAC1DZ,EAAA,CAAAC,cAAA,iBAC8F;IAC5FD,EAAA,CAAAoB,SAAA,aAA+C;IAACpB,EAAA,CAAAqB,MAAA,sBAClD;IAAArB,EAAA,CAAAY,YAAA,EAAQ;IAQRZ,EAPA,CAAAkC,UAAA,KAAAyB,yDAAA,kBAA0D,KAAAC,yDAAA,kBAMqC,KAAAC,yDAAA,kBAErC;IAE9D7D,EADE,CAAAY,YAAA,EAAM,EACF;IAEJZ,EADF,CAAAC,cAAA,eAAoC,iBACgD;IAAAD,EAAA,CAAAqB,MAAA,6CAClF;IAAArB,EAAA,CAAAY,YAAA,EAAQ;IACRZ,EAAA,CAAAC,cAAA,oBACsD;IAApDD,EAAA,CAAAE,gBAAA,2BAAA4D,sFAAA1D,MAAA;MAAAJ,EAAA,CAAAK,aAAA,CAAAmD,IAAA;MAAA,MAAAjD,MAAA,GAAAP,EAAA,CAAAQ,aAAA;MAAAR,EAAA,CAAAS,kBAAA,CAAAF,MAAA,CAAA2C,iBAAA,CAAAa,kBAAA,EAAA3D,MAAA,MAAAG,MAAA,CAAA2C,iBAAA,CAAAa,kBAAA,GAAA3D,MAAA;MAAA,OAAAJ,EAAA,CAAAW,WAAA,CAAAP,MAAA;IAAA,EAAmD;IACvDJ,EADwD,CAAAY,YAAA,EAAW,EAC7D;IAEJZ,EADF,CAAAC,cAAA,eAA+B,iBACsC;IAAAD,EAAA,CAAAqB,MAAA,oBAAE;IAAArB,EAAA,CAAAY,YAAA,EAAQ;IAC7EZ,EAAA,CAAAC,cAAA,qBAAmF;IAA5CD,EAAA,CAAAE,gBAAA,2BAAA8D,uFAAA5D,MAAA;MAAAJ,EAAA,CAAAK,aAAA,CAAAmD,IAAA;MAAA,MAAAjD,MAAA,GAAAP,EAAA,CAAAQ,aAAA;MAAAR,EAAA,CAAAS,kBAAA,CAAAF,MAAA,CAAA0D,cAAA,EAAA7D,MAAA,MAAAG,MAAA,CAAA0D,cAAA,GAAA7D,MAAA;MAAA,OAAAJ,EAAA,CAAAW,WAAA,CAAAP,MAAA;IAAA,EAA4B;IACjEJ,EAAA,CAAAkC,UAAA,KAAAgC,+DAAA,wBAAiE;IAIrElE,EADE,CAAAY,YAAA,EAAY,EACR;IAUJZ,EADF,CAAAC,cAAA,eAAoC,iBAC0C;IAAAD,EAAA,CAAAqB,MAAA,wDAAQ;IAAArB,EAAA,CAAAY,YAAA,EAAQ;IAC5FZ,EAAA,CAAAC,cAAA,oBACmD;IAAjDD,EAAA,CAAAE,gBAAA,2BAAAiE,sFAAA/D,MAAA;MAAAJ,EAAA,CAAAK,aAAA,CAAAmD,IAAA;MAAA,MAAAjD,MAAA,GAAAP,EAAA,CAAAQ,aAAA;MAAAR,EAAA,CAAAS,kBAAA,CAAAF,MAAA,CAAA2C,iBAAA,CAAAkB,gBAAA,EAAAhE,MAAA,MAAAG,MAAA,CAAA2C,iBAAA,CAAAkB,gBAAA,GAAAhE,MAAA;MAAA,OAAAJ,EAAA,CAAAW,WAAA,CAAAP,MAAA;IAAA,EAAgD;IAEtDJ,EAFuD,CAAAY,YAAA,EAAW,EAC1D,EACO;IAEbZ,EADF,CAAAC,cAAA,0BAAsD,kBACc;IAAvBD,EAAA,CAAAc,UAAA,mBAAAuD,4EAAA;MAAA,MAAAC,OAAA,GAAAtE,EAAA,CAAAK,aAAA,CAAAmD,IAAA,EAAAe,SAAA;MAAA,MAAAhE,MAAA,GAAAP,EAAA,CAAAQ,aAAA;MAAA,OAAAR,EAAA,CAAAW,WAAA,CAASJ,MAAA,CAAAiE,OAAA,CAAAF,OAAA,CAAY;IAAA,EAAC;IAACtE,EAAA,CAAAqB,MAAA,IAAwB;IAAArB,EAAA,CAAAY,YAAA,EAAS;IACnGZ,EAAA,CAAAC,cAAA,kBAA+D;IAAxBD,EAAA,CAAAc,UAAA,mBAAA2D,4EAAA;MAAA,MAAAH,OAAA,GAAAtE,EAAA,CAAAK,aAAA,CAAAmD,IAAA,EAAAe,SAAA;MAAA,MAAAhE,MAAA,GAAAP,EAAA,CAAAQ,aAAA;MAAA,OAAAR,EAAA,CAAAW,WAAA,CAASJ,MAAA,CAAAmE,QAAA,CAAAJ,OAAA,CAAa;IAAA,EAAC;IAACtE,EAAA,CAAAqB,MAAA,oBAAE;IAErErB,EAFqE,CAAAY,YAAA,EAAS,EAC3D,EACT;;;;IAnENZ,EAAA,CAAAsC,SAAA,GACF;IADEtC,EAAA,CAAAqD,kBAAA,MAAA9C,MAAA,CAAAoE,KAAA,gEACF;IAKiE3E,EAAA,CAAAsC,SAAA,GAA8C;IAA9CtC,EAAA,CAAAa,gBAAA,YAAAN,MAAA,CAAA2C,iBAAA,CAAAV,cAAA,CAA8C;IAkB/DxC,EAAA,CAAAsC,SAAA,IAAc;IAAdtC,EAAA,CAAAyC,UAAA,SAAAlC,MAAA,CAAAwC,QAAA,CAAc;IAMjC/C,EAAA,CAAAsC,SAAA,EAAc;IAAdtC,EAAA,CAAAyC,UAAA,SAAAlC,MAAA,CAAAyC,QAAA,CAAc;IACOhD,EAAA,CAAAsC,SAAA,EAAgD;IAAhDtC,EAAA,CAAAyC,UAAA,SAAAlC,MAAA,CAAA2C,iBAAA,CAAAC,WAAA,KAAA5C,MAAA,CAAAyC,QAAA,CAAgD;IAQ5FhD,EAAA,CAAAsC,SAAA,GAAmD;IAAnDtC,EAAA,CAAAa,gBAAA,YAAAN,MAAA,CAAA2C,iBAAA,CAAAa,kBAAA,CAAmD;IAId/D,EAAA,CAAAsC,SAAA,GAA4B;IAA5BtC,EAAA,CAAAa,gBAAA,YAAAN,MAAA,CAAA0D,cAAA,CAA4B;IACnCjE,EAAA,CAAAsC,SAAA,EAAgB;IAAhBtC,EAAA,CAAAyC,UAAA,YAAAlC,MAAA,CAAAqE,aAAA,CAAgB;IAgB9C5E,EAAA,CAAAsC,SAAA,GAAgD;IAAhDtC,EAAA,CAAAa,gBAAA,YAAAN,MAAA,CAAA2C,iBAAA,CAAAkB,gBAAA,CAAgD;IAIcpE,EAAA,CAAAsC,SAAA,GAAwB;IAAxBtC,EAAA,CAAAuC,iBAAA,CAAAhC,MAAA,CAAAoE,KAAA,mCAAwB;;;ADlGhG,OAAM,MAAOE,0BAA2B,SAAQhF,aAAa;EAC3DiF,YACUC,MAAmB,EACnBC,aAA8B,EAC9BC,OAAuB,EACvBC,KAAuB,EACvBC,iBAAmC,EACnCC,MAAc,EACdC,eAA+B;IAEvC,KAAK,CAACN,MAAM,CAAC;IARL,KAAAA,MAAM,GAANA,MAAM;IACN,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,eAAe,GAAfA,eAAe;IAKhB,KAAAC,SAAS,GAAG,CAAC;IACb,KAAAC,QAAQ,GAAG,EAAE;IACb,KAAAC,SAAS,GAAG,CAAC;IACb,KAAAC,YAAY,GAAG,CAAC;IACzB,KAAAb,aAAa,GAAU,CACrB;MACE9C,GAAG,EAAE,CAAC;MACN4D,MAAM,EAAE,CAAC;MACTpC,MAAM,EAAE;KACT,EACD;MACExB,GAAG,EAAE,CAAC;MACN4D,MAAM,EAAE,CAAC;MACTpC,MAAM,EAAE;KACT,CACF;IAED,KAAAqC,gBAAgB,GAAU,CACxB;MACE7D,GAAG,EAAE,CAAC;MACN4D,MAAM,EAAE,CAAC;MACTpC,MAAM,EAAE;KACT,EACD;MACExB,GAAG,EAAE,CAAC;MACN4D,MAAM,EAAE,CAAC;MACTpC,MAAM,EAAE;KACT,CACF;IAQD;IAEA,KAAAsC,aAAa,GAA8B,EAAE;IAI7C,KAAAlF,MAAM,GAAW,EAAE;IAEnB,KAAAmF,aAAa,GAA4B;MACvCrD,cAAc,EAAE,EAAE;MAClBW,WAAW,EAAE,EAAE;MACfY,kBAAkB,EAAE,EAAE;MACtB+B,SAAS,EAAE,IAAI;MACfhE,GAAG,EAAEiE,SAAS;MACdC,OAAO,EAAED,SAAS;MAClB3B,gBAAgB,EAAE;KACnB;IAED,KAAAO,KAAK,GAAG,IAAI;IAEZ,KAAAsB,MAAM,GAAgC,IAAI;IAG1C,KAAAjD,QAAQ,GAAgC,IAAI;IAC5C,KAAAD,QAAQ,GAAkB,IAAI;EA9D9B;EAgEAlB,iBAAiBA,CAACqE,EAAO;IACvB,IAAI,CAACd,MAAM,CAACe,aAAa,CAAC,4BAA4BD,EAAE,EAAE,CAAC;EAC7D;EAESE,QAAQA,CAAA;IACf,IAAI,CAACC,gBAAgB,EAAE,CAACC,SAAS,EAAE;IACnC,IAAI,CAACrC,cAAc,GAAG,IAAI,CAACW,aAAa,CAAC,CAAC,CAAC;IAC3C,IAAI,CAAC2B,iBAAiB,GAAG,IAAI,CAACZ,gBAAgB,CAAC,CAAC,CAAC;IACjD,IAAI,CAACzC,iBAAiB,GAAG;MAAE,GAAG,IAAI,CAAC2C;IAAa,CAAE;EACpD;EAEAQ,gBAAgBA,CAAA;IACd,OAAO,IAAI,CAAClB,iBAAiB,CAACqB,oCAAoC,CAAC;MAAEC,IAAI,EAAE;QACzEC,SAAS,EAAE,IAAI,CAAClB,SAAS;QACzBmB,QAAQ,EAAE,IAAI,CAACpB,QAAQ;QACvBqB,KAAK,EAAE,IAAI,CAAClG,MAAM;QAClBmG,OAAO,EAAE;;IACV,CAAE,CAAC,CAACC,IAAI,CACPlH,GAAG,CAACmH,GAAG,IAAG;MACR,IAAIA,GAAG,CAACC,OAAO,IAAID,GAAG,CAACE,UAAU,IAAI,CAAC,EAAE;QACtC,IAAI,CAACrB,aAAa,GAAGmB,GAAG,CAACC,OAAQ,IAAI,EAAE;QACvC,IAAI,CAACvB,YAAY,GAAGsB,GAAG,CAACG,UAAW;MACrC;IACF,CAAC,CAAC,CACH;EACH;EAEA/F,MAAMA,CAACgG,GAAQ;IACb,IAAI,CAACxC,KAAK,GAAG,IAAI;IACjB,IAAI,CAACyC,SAAS,EAAE;IAChB,IAAI,CAACnD,cAAc,GAAG,IAAI,CAACW,aAAa,CAAC,CAAC,CAAC;IAC3C,IAAI,CAAC1B,iBAAiB,CAAC8C,OAAO,GAAG,IAAI,CAACpB,aAAa,CAAC,CAAC,CAAC,CAACc,MAAM;IAC7D,IAAI,CAACa,iBAAiB,GAAG,IAAI,CAACZ,gBAAgB,CAAC,CAAC,CAAC;IACjD,IAAI,CAACzC,iBAAiB,CAACmE,UAAU,GAAG,IAAI,CAAC1B,gBAAgB,CAAC,CAAC,CAAC,CAACD,MAAM;IACnE,IAAI,CAACV,aAAa,CAACsC,IAAI,CAACH,GAAG,CAAC;EAC9B;EAGAI,cAAcA,CAACC,IAAU;IACvB,OAAO,IAAIC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAI;MACrC,MAAMC,MAAM,GAAG,IAAIC,UAAU,EAAE;MAC/BD,MAAM,CAACE,aAAa,CAACN,IAAI,CAAC;MAC1BI,MAAM,CAACG,MAAM,GAAG,MAAML,OAAO,CAACE,MAAM,CAACI,MAAM,CAAC;MAC5CJ,MAAM,CAACK,OAAO,GAAGC,KAAK,IAAIP,MAAM,CAACO,KAAK,CAAC;IACzC,CAAC,CAAC;EACJ;EAGAxE,cAAcA,CAACyE,KAAU;IACvB,MAAMX,IAAI,GAASW,KAAK,CAACC,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC;IACxC,MAAMC,SAAS,GAAG,mBAAmB;IACrC,IAAI,CAACA,SAAS,CAACC,IAAI,CAACf,IAAI,CAACgB,IAAI,CAAC,EAAE;MAC9B,IAAI,CAACvD,OAAO,CAACwD,YAAY,CAAC,kBAAkB,CAAC;MAC7C;IACF;IACA,IAAIjB,IAAI,EAAE;MACR,MAAMkB,YAAY,GAAG,CAAC,YAAY,EAAE,WAAW,EAAE,iBAAiB,CAAC;MACnE,IAAIA,YAAY,CAACC,QAAQ,CAACnB,IAAI,CAACgB,IAAI,CAAC,EAAE;QACpC,IAAI,CAACzF,QAAQ,GAAGyE,IAAI,CAACoB,IAAI;QACzB,MAAMhB,MAAM,GAAG,IAAIC,UAAU,EAAE;QAC/BD,MAAM,CAACG,MAAM,GAAIc,CAAM,IAAI;UACzB,IAAI,CAAC7F,QAAQ,GAAG6F,CAAC,CAACT,MAAM,CAACJ,MAAM;UAC/B,IAAI,IAAI,CAACc,SAAS,EAAE;YAClB,IAAI,CAACA,SAAS,CAACC,aAAa,CAACC,KAAK,GAAG,IAAI;UAC3C;QACF,CAAC;QACDpB,MAAM,CAACE,aAAa,CAACN,IAAI,CAAC;MAC5B;IACF;EACF;EAEA1E,UAAUA,CAAA;IACR,IAAI,IAAI,CAACE,QAAQ,EAAE;MACjB,IAAI,CAACA,QAAQ,GAAG,IAAI;MACpB,IAAI,CAACD,QAAQ,GAAG,IAAI;MACpB,IAAI,IAAI,CAAC+F,SAAS,EAAE;QAClB,IAAI,CAACA,SAAS,CAACC,aAAa,CAACC,KAAK,GAAG,IAAI,CAAC,CAAC;MAC7C;IACF;EACF;EAEA5B,SAASA,CAAA;IACP,IAAI,CAAClE,iBAAiB,GAAG;MAAE,GAAG,IAAI,CAAC2C;IAAa,CAAE;IAClD,IAAI,CAAC/C,UAAU,EAAE;EACnB;EAEApB,mBAAmBA,CAACuH,IAAS,EAAE9B,GAAQ;IACrC,IAAI,CAACxC,KAAK,GAAG,KAAK;IAClB,IAAI,CAACK,aAAa,CAACsC,IAAI,CAACH,GAAG,CAAC;IAC5B,IAAI,CAACrE,UAAU,EAAE;IACjB,IAAI,CAACI,iBAAiB,GAAGnD,CAAC,CAACmJ,SAAS,CAACD,IAAI,CAAC;IAC1C,IAAI,CAAC/F,iBAAiB,CAACa,kBAAkB,GAAG,IAAI,CAACsB,eAAe,CAAC8D,UAAU,CAACF,IAAI,CAAClF,kBAAkB,CAAC;IACpG,IAAI,CAACE,cAAc,GAAG,IAAI,CAACW,aAAa,CAAC,IAAI,CAAC1B,iBAAiB,CAAC8C,OAAQ,CAAC;EAC3E;EAEAoD,UAAUA,CAAA;IACR,IAAI,CAAClE,KAAK,CAACmE,KAAK,EAAE;IAClB,IAAI,CAACnE,KAAK,CAACoE,QAAQ,CAAC,QAAQ,EAAE,IAAI,CAACpG,iBAAiB,CAACV,cAAc,CAAC;IACpE,IAAI,CAAC0C,KAAK,CAACqE,iBAAiB,CAAC,QAAQ,EAAE,IAAI,CAACrG,iBAAiB,CAACV,cAAc,EAAE,EAAE,CAAC;IACjF,IAAI,IAAI,CAACmC,KAAK,EAAE;MACd,IAAI,CAACO,KAAK,CAACoE,QAAQ,CAAC,QAAQ,EAAE,IAAI,CAACpG,iBAAiB,CAACC,WAAW,CAAC;IACnE;IACA,IAAI,CAAC+B,KAAK,CAACoE,QAAQ,CAAC,UAAU,EAAE,IAAI,CAACpG,iBAAiB,CAACa,kBAAkB,CAAC;IAC1E,IAAI,CAACmB,KAAK,CAACoE,QAAQ,CAAC,MAAM,EAAE,IAAI,CAACpG,iBAAiB,CAAC8C,OAAO,EAAEwD,QAAQ,EAAE,CAAC;EACzE;EAEAC,kBAAkBA,CAACC,YAAiB;IAClC,MAAMC,WAAW,GAAGD,YAAY,CAACE,OAAO,CAAC,GAAG,CAAC;IAC7C,IAAID,WAAW,KAAK,CAAC,CAAC,EAAE;MACtB,OAAOD,YAAY,CAACG,SAAS,CAACF,WAAW,GAAG,CAAC,CAAC;IAChD;IACA,OAAOD,YAAY;EACrB;EAEAI,KAAKA,CAACC,GAAW;IACf,IAAI,OAAOA,GAAG,KAAK,WAAW,IAAIA,GAAG,KAAK,IAAI,EAAE;MAC9C,OAAO,EAAE;IACX;IACA,OAAOA,GAAG,CAACC,OAAO,CAAC,KAAK,EAAE,MAAM,CAAC;IAAC;EACpC;EAEAtF,QAAQA,CAACyC,GAAQ;IAEf,IAAI,CAACjE,iBAAiB,CAAC8C,OAAO,GAAG,IAAI,CAAC/B,cAAc,CAACyB,MAAM;IAC3D,MAAMuE,WAAW,GAAsB;MACrCzH,cAAc,EAAE,IAAI,CAACU,iBAAiB,CAACV,cAAc;MACrDwD,OAAO,EAAE,IAAI,CAAC/B,cAAc,CAACyB,MAAM;MACnC3B,kBAAkB,EAAE,IAAI,CAAC+F,KAAK,CAAC,IAAI,CAAC5G,iBAAiB,CAACa,kBAAmB,CAAC;MAC1EsD,UAAU,EAAE,IAAI,CAACd,iBAAiB,CAACb,MAAM,IAAI,CAAC,GAAG,KAAK,GAAG,IAAI;MAC7DtB,gBAAgB,EAAE,IAAI,CAAClB,iBAAiB,CAACkB;KAC1C;IAED,IAAI,IAAI,CAACO,KAAK,IAAI,IAAI,CAAC3B,QAAQ,EAAE;MAAE;MACjC,IAAI,CAACE,iBAAiB,CAACC,WAAW,GAAG,IAAI,CAACsG,kBAAkB,CAAC,IAAI,CAACzG,QAAQ,CAAC;MAC3EiH,WAAW,CAAC9G,WAAW,GAAG,IAAI,CAACsG,kBAAkB,CAAC,IAAI,CAACzG,QAAQ,CAAC;IAClE,CAAC,MAAM;MAAE;MACP,IAAI,IAAI,CAACA,QAAQ,EAAE;QACjBiH,WAAW,CAAC9G,WAAW,GAAG,IAAI,CAACsG,kBAAkB,CAAC,IAAI,CAACzG,QAAQ,CAAC;MAClE;MACAiH,WAAW,CAACC,YAAY,GAAG,IAAI,CAAChH,iBAAiB,CAACpB,GAAG;IACvD;IACA,IAAI,CAACsH,UAAU,EAAE;IACjB,IAAI,IAAI,CAAClE,KAAK,CAACiF,aAAa,CAACC,MAAM,GAAG,CAAC,EAAE;MACvC,IAAI,CAACnF,OAAO,CAACoF,aAAa,CAAC,IAAI,CAACnF,KAAK,CAACiF,aAAa,CAAC;MACpD;IACF;IAEA,IAAI,CAAChF,iBAAiB,CAACmF,kCAAkC,CAAC;MAAE7D,IAAI,EAAEwD;IAAW,CAAE,CAAC,CAACnD,IAAI,CACnFlH,GAAG,CAACmH,GAAG,IAAG;MACR,IAAIA,GAAG,CAACE,UAAU,KAAK,CAAC,EAAE;QACxB,IAAI,CAAChC,OAAO,CAACsF,aAAa,CAAC,MAAM,CAAC;MACpC,CAAC,MAAM;QACL,IAAI,CAACtF,OAAO,CAACsF,aAAa,CAAC,UAAU,CAAC;MACxC;IACF,CAAC,CAAC,EACF7K,SAAS,CAAC,MAAM,IAAI,CAAC2G,gBAAgB,EAAE,CAAC,EACxC1G,QAAQ,CAAC,MAAMwH,GAAG,CAACqD,KAAK,EAAE,CAAC,CAC5B,CAAClE,SAAS,EAAE;EACf;EAEAmE,WAAWA,CAACC,OAAe;IACzB,IAAI,CAAClF,SAAS,GAAGkF,OAAO;IACxB,IAAI,CAACrE,gBAAgB,EAAE,CAACC,SAAS,EAAE;EACrC;EAEArE,QAAQA,CAACgH,IAAS,EAAE9B,GAAQ;IAC1B,IAAIwD,MAAM,CAACC,OAAO,CAAC,WAAW3B,IAAI,CAACzG,cAAc,IAAI,CAAC,EAAE;MACtD,IAAI,CAAC2C,iBAAiB,CAAC0F,oCAAoC,CAAC;QAC1DpE,IAAI,EAAE;UACJ,cAAc,EAAEwC,IAAI,CAACnH;;OAExB,CAAC,CAACgF,IAAI,CACLlH,GAAG,CAACmH,GAAG,IAAG;QACR,IAAIA,GAAG,CAACE,UAAU,KAAK,CAAC,EAAE;UACxB,IAAI,CAAChC,OAAO,CAACsF,aAAa,CAAC,MAAM,CAAC;QACpC,CAAC,MAAM;UACL,IAAI,CAACtF,OAAO,CAACwD,YAAY,CAAC1B,GAAG,CAAC+D,OAAQ,CAAC;QACzC;MACF,CAAC,CAAC,EACFpL,SAAS,CAAC,MAAM,IAAI,CAAC2G,gBAAgB,EAAE,CAAC,EACxC1G,QAAQ,CAAC,MAAMwH,GAAG,CAACqD,KAAK,EAAE,CAAC,CAC5B,CAAClE,SAAS,EAAE;IACf;EACF;EAEA9B,OAAOA,CAAC2C,GAAQ;IACdA,GAAG,CAACqD,KAAK,EAAE;EACb;;;uCAtQW3F,0BAA0B,EAAA7E,EAAA,CAAA+K,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAjL,EAAA,CAAA+K,iBAAA,CAAAG,EAAA,CAAAC,eAAA,GAAAnL,EAAA,CAAA+K,iBAAA,CAAAK,EAAA,CAAAC,cAAA,GAAArL,EAAA,CAAA+K,iBAAA,CAAAO,EAAA,CAAAC,gBAAA,GAAAvL,EAAA,CAAA+K,iBAAA,CAAAS,EAAA,CAAAC,gBAAA,GAAAzL,EAAA,CAAA+K,iBAAA,CAAAW,EAAA,CAAAC,MAAA,GAAA3L,EAAA,CAAA+K,iBAAA,CAAAa,EAAA,CAAAC,cAAA;IAAA;EAAA;;;YAA1BhH,0BAA0B;MAAAiH,SAAA;MAAAC,SAAA,WAAAC,iCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;;;;;;;;;;;;;;;UC5BrCjM,EADF,CAAAC,cAAA,iBAA0B,qBACR;UACdD,EAAA,CAAAoB,SAAA,qBAAiC;UACnCpB,EAAA,CAAAY,YAAA,EAAiB;UAIXZ,EAHN,CAAAC,cAAA,mBAAc,aACK,aACI,eACyC;UAAAD,EAAA,CAAAqB,MAAA,gCAC1D;UACFrB,EADE,CAAAY,YAAA,EAAQ,EACJ;UAEJZ,EADF,CAAAC,cAAA,aAAmB,oBACF;UACbD,EAAA,CAAAkC,UAAA,KAAAiK,4CAAA,mBAA+D;UAEnEnM,EADE,CAAAY,YAAA,EAAgB,EACZ;UAGFZ,EAFJ,CAAAC,cAAA,cAAmB,cACuB,iBACsC;UAAzCD,EAAA,CAAAc,UAAA,mBAAAsL,6DAAA;YAAApM,EAAA,CAAAK,aAAA,CAAAgM,GAAA;YAAA,OAAArM,EAAA,CAAAW,WAAA,CAASuL,GAAA,CAAA7F,gBAAA,EAAkB,CAAAC,SAAA,EAAY;UAAA,EAAC;UACzEtG,EAAA,CAAAoB,SAAA,aAAkC;UAClCpB,EAAA,CAAAqB,MAAA,sBACF;UAAArB,EAAA,CAAAY,YAAA,EAAS;UACTZ,EAAA,CAAAkC,UAAA,KAAAoK,6CAAA,qBAAuE;UAI7EtM,EAFI,CAAAY,YAAA,EAAM,EACF,EACF;UAMEZ,EAJR,CAAAC,cAAA,eAAmC,iBAC+D,aACvF,cACgD,cACpB;UAAAD,EAAA,CAAAqB,MAAA,UAAE;UAAArB,EAAA,CAAAY,YAAA,EAAK;UACtCZ,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAqB,MAAA,gCAAI;UAAArB,EAAA,CAAAY,YAAA,EAAK;UACvCZ,EAAA,CAAAC,cAAA,cAA0C;UAAAD,EAAA,CAAAqB,MAAA,oBAAE;UAEhDrB,EAFgD,CAAAY,YAAA,EAAK,EAC9C,EACC;UACRZ,EAAA,CAAAC,cAAA,aAAO;UACLD,EAAA,CAAAkC,UAAA,KAAAqK,yCAAA,iBAAsD;UAe9DvM,EAHM,CAAAY,YAAA,EAAQ,EACF,EACJ,EACO;UAEbZ,EADF,CAAAC,cAAA,0BAAsD,0BAES;UAD7CD,EAAA,CAAAE,gBAAA,wBAAAsM,0EAAApM,MAAA;YAAAJ,EAAA,CAAAK,aAAA,CAAAgM,GAAA;YAAArM,EAAA,CAAAS,kBAAA,CAAAyL,GAAA,CAAA1G,SAAA,EAAApF,MAAA,MAAA8L,GAAA,CAAA1G,SAAA,GAAApF,MAAA;YAAA,OAAAJ,EAAA,CAAAW,WAAA,CAAAP,MAAA;UAAA,EAAoB;UAClCJ,EAAA,CAAAc,UAAA,wBAAA0L,0EAAApM,MAAA;YAAAJ,EAAA,CAAAK,aAAA,CAAAgM,GAAA;YAAA,OAAArM,EAAA,CAAAW,WAAA,CAAcuL,GAAA,CAAAzB,WAAA,CAAArK,MAAA,CAAmB;UAAA,EAAC;UAGxCJ,EAFI,CAAAY,YAAA,EAAiB,EACF,EACT;UAEVZ,EAAA,CAAAkC,UAAA,KAAAuK,kDAAA,kCAAAzM,EAAA,CAAA0M,sBAAA,CAAoD;;;UAhDO1M,EAAA,CAAAsC,SAAA,IAAY;UAAZtC,EAAA,CAAAyC,UAAA,SAAAyJ,GAAA,CAAAxJ,MAAA,CAAY;UASN1C,EAAA,CAAAsC,SAAA,GAAc;UAAdtC,EAAA,CAAAyC,UAAA,SAAAyJ,GAAA,CAAAS,QAAA,CAAc;UAgBhD3M,EAAA,CAAAsC,SAAA,IAAkB;UAAlBtC,EAAA,CAAAyC,UAAA,YAAAyJ,GAAA,CAAAtG,aAAA,CAAkB;UAiB7B5F,EAAA,CAAAsC,SAAA,GAAoB;UAApBtC,EAAA,CAAAa,gBAAA,SAAAqL,GAAA,CAAA1G,SAAA,CAAoB;UAAuBxF,EAAtB,CAAAyC,UAAA,aAAAyJ,GAAA,CAAA3G,QAAA,CAAqB,mBAAA2G,GAAA,CAAAzG,YAAA,CAAgC;;;qBDhC1FhG,YAAY,EAAAmN,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EACZhN,YAAY,EAAAiN,EAAA,CAAAC,oBAAA,EAAAD,EAAA,CAAAE,eAAA,EAAAF,EAAA,CAAAG,OAAA,EAAAhC,EAAA,CAAAiC,eAAA,EAAAjC,EAAA,CAAAkC,mBAAA,EAAAlC,EAAA,CAAAmC,qBAAA,EAAAnC,EAAA,CAAAoC,qBAAA,EAAApC,EAAA,CAAAqC,gBAAA,EAAArC,EAAA,CAAAsC,iBAAA,EAAAtC,EAAA,CAAAuC,iBAAA,EAAAvC,EAAA,CAAAwC,oBAAA,EAAAC,GAAA,CAAAC,aAAA,EAAAC,GAAA,CAAAC,mBAAA,EAAAC,GAAA,CAAAC,kBAAA;MAAAC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}