{"ast": null, "code": "import { of as observableOf } from 'rxjs';\nimport { SecurityCamerasData } from '../data/security-cameras';\nimport * as i0 from \"@angular/core\";\nexport class SecurityCamerasService extends SecurityCamerasData {\n  constructor() {\n    super(...arguments);\n    this.cameras = [{\n      title: 'Camera #1',\n      source: 'assets/images/camera1.jpg'\n    }, {\n      title: 'Camera #2',\n      source: 'assets/images/camera2.jpg'\n    }, {\n      title: 'Camera #3',\n      source: 'assets/images/camera3.jpg'\n    }, {\n      title: 'Camera #4',\n      source: 'assets/images/camera4.jpg'\n    }];\n  }\n  getCamerasData() {\n    return observableOf(this.cameras);\n  }\n  static {\n    this.ɵfac = /*@__PURE__*/(() => {\n      let ɵSecurityCamerasService_BaseFactory;\n      return function SecurityCamerasService_Factory(__ngFactoryType__) {\n        return (ɵSecurityCamerasService_BaseFactory || (ɵSecurityCamerasService_BaseFactory = i0.ɵɵgetInheritedFactory(SecurityCamerasService)))(__ngFactoryType__ || SecurityCamerasService);\n      };\n    })();\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: SecurityCamerasService,\n      factory: SecurityCamerasService.ɵfac\n    });\n  }\n}", "map": {"version": 3, "names": ["of", "observableOf", "SecurityCamerasData", "SecurityCamerasService", "constructor", "cameras", "title", "source", "getCamerasData", "__ngFactoryType__", "factory", "ɵfac"], "sources": ["C:\\Users\\<USER>\\Documents\\jeansalechange\\adminFront\\src\\app\\@core\\mock\\security-cameras.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport { of as observableOf, Observable } from 'rxjs';\r\nimport { Camera, SecurityCamerasData } from '../data/security-cameras';\r\n\r\n@Injectable()\r\nexport class SecurityCamerasService extends SecurityCamerasData {\r\n\r\n  private cameras: Camera[] = [\r\n    {\r\n      title: 'Camera #1',\r\n      source: 'assets/images/camera1.jpg',\r\n    },\r\n    {\r\n      title: 'Camera #2',\r\n      source: 'assets/images/camera2.jpg',\r\n    },\r\n    {\r\n      title: 'Camera #3',\r\n      source: 'assets/images/camera3.jpg',\r\n    },\r\n    {\r\n      title: 'Camera #4',\r\n      source: 'assets/images/camera4.jpg',\r\n    },\r\n  ];\r\n\r\n  getCamerasData(): Observable<Camera[]> {\r\n    return observableOf(this.cameras);\r\n  }\r\n}\r\n"], "mappings": "AACA,SAASA,EAAE,IAAIC,YAAY,QAAoB,MAAM;AACrD,SAAiBC,mBAAmB,QAAQ,0BAA0B;;AAGtE,OAAM,MAAOC,sBAAuB,SAAQD,mBAAmB;EAD/DE,YAAA;;IAGU,KAAAC,OAAO,GAAa,CAC1B;MACEC,KAAK,EAAE,WAAW;MAClBC,MAAM,EAAE;KACT,EACD;MACED,KAAK,EAAE,WAAW;MAClBC,MAAM,EAAE;KACT,EACD;MACED,KAAK,EAAE,WAAW;MAClBC,MAAM,EAAE;KACT,EACD;MACED,KAAK,EAAE,WAAW;MAClBC,MAAM,EAAE;KACT,CACF;;EAEDC,cAAcA,CAAA;IACZ,OAAOP,YAAY,CAAC,IAAI,CAACI,OAAO,CAAC;EACnC;;;;;uHAvBWF,sBAAsB,IAAAM,iBAAA,IAAtBN,sBAAsB;MAAA;IAAA;EAAA;;;aAAtBA,sBAAsB;MAAAO,OAAA,EAAtBP,sBAAsB,CAAAQ;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}