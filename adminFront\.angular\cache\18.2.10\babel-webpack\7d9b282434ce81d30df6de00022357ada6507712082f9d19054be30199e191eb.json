{"ast": null, "code": "import { of as observableOf } from 'rxjs';\nimport { TrafficListData } from '../data/traffic-list';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"./periods.service\";\nexport class TrafficListService extends TrafficListData {\n  constructor(period) {\n    super();\n    this.period = period;\n    this.getRandom = roundTo => Math.round(Math.random() * roundTo);\n    this.data = {};\n    this.data = {\n      week: this.getDataWeek(),\n      month: this.getDataMonth(),\n      year: this.getDataYear()\n    };\n  }\n  getDataWeek() {\n    const getFirstDateInPeriod = () => {\n      const weeks = this.period.getWeeks();\n      return weeks[weeks.length - 1];\n    };\n    return this.reduceData(this.period.getWeeks(), getFirstDateInPeriod);\n  }\n  getDataMonth() {\n    const getFirstDateInPeriod = () => {\n      const months = this.period.getMonths();\n      return months[months.length - 1];\n    };\n    return this.reduceData(this.period.getMonths(), getFirstDateInPeriod);\n  }\n  getDataYear() {\n    const getFirstDateInPeriod = () => {\n      const years = this.period.getYears();\n      return `${parseInt(years[0], 10) - 1}`;\n    };\n    return this.reduceData(this.period.getYears(), getFirstDateInPeriod);\n  }\n  reduceData(timePeriods, getFirstDateInPeriod) {\n    return timePeriods.reduce((result, timePeriod, index) => {\n      const hasResult = result[index - 1];\n      const prevDate = hasResult ? result[index - 1].comparison.nextDate : getFirstDateInPeriod();\n      const prevValue = hasResult ? result[index - 1].comparison.nextValue : this.getRandom(100);\n      const nextValue = this.getRandom(100);\n      const deltaValue = prevValue - nextValue;\n      const item = {\n        date: timePeriod,\n        value: this.getRandom(1000),\n        delta: {\n          up: deltaValue <= 0,\n          value: Math.abs(deltaValue)\n        },\n        comparison: {\n          prevDate,\n          prevValue,\n          nextDate: timePeriod,\n          nextValue\n        }\n      };\n      return [...result, item];\n    }, []);\n  }\n  getTrafficListData(period) {\n    return observableOf(this.data[period]);\n  }\n  static {\n    this.ɵfac = function TrafficListService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || TrafficListService)(i0.ɵɵinject(i1.PeriodsService));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: TrafficListService,\n      factory: TrafficListService.ɵfac\n    });\n  }\n}", "map": {"version": 3, "names": ["of", "observableOf", "TrafficListData", "TrafficListService", "constructor", "period", "getRandom", "roundTo", "Math", "round", "random", "data", "week", "getDataWeek", "month", "getDataMonth", "year", "getDataYear", "getFirstDateInPeriod", "weeks", "getWeeks", "length", "reduceData", "months", "getMonths", "years", "getYears", "parseInt", "timePeriods", "reduce", "result", "timePeriod", "index", "hasResult", "prevDate", "comparison", "nextDate", "prevValue", "nextValue", "deltaValue", "item", "date", "value", "delta", "up", "abs", "getTrafficListData", "i0", "ɵɵinject", "i1", "PeriodsService", "factory", "ɵfac"], "sources": ["C:\\Users\\<USER>\\Documents\\jeansalechange\\adminFront\\src\\app\\@core\\mock\\traffic-list.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport { of as observableOf,  Observable } from 'rxjs';\r\nimport { PeriodsService } from './periods.service';\r\nimport { TrafficList, TrafficListData } from '../data/traffic-list';\r\n\r\n@Injectable()\r\nexport class TrafficListService extends TrafficListData {\r\n\r\n  private getRandom = (roundTo: number) => Math.round(Math.random() * roundTo);\r\n  private data:any = {};\r\n\r\n  constructor(private period: PeriodsService) {\r\n    super();\r\n    this.data = {\r\n      week: this.getDataWeek(),\r\n      month: this.getDataMonth(),\r\n      year: this.getDataYear(),\r\n    };\r\n  }\r\n\r\n  private getDataWeek(): TrafficList[] {\r\n    const getFirstDateInPeriod = () => {\r\n      const weeks = this.period.getWeeks();\r\n\r\n      return weeks[weeks.length - 1];\r\n    };\r\n\r\n    return this.reduceData(this.period.getWeeks(), getFirstDateInPeriod);\r\n  }\r\n\r\n  private getDataMonth(): TrafficList[] {\r\n    const getFirstDateInPeriod = () => {\r\n      const months = this.period.getMonths();\r\n\r\n      return months[months.length - 1];\r\n    };\r\n\r\n    return this.reduceData(this.period.getMonths(), getFirstDateInPeriod);\r\n  }\r\n\r\n  private getDataYear(): TrafficList[] {\r\n    const getFirstDateInPeriod = () => {\r\n      const years = this.period.getYears();\r\n\r\n      return `${parseInt(years[0], 10) - 1}`;\r\n    };\r\n\r\n    return this.reduceData(this.period.getYears(), getFirstDateInPeriod);\r\n  }\r\n\r\n  private reduceData(timePeriods: string[], getFirstDateInPeriod: () => string): TrafficList[] {\r\n    return timePeriods.reduce((result:any, timePeriod, index) => {\r\n      const hasResult = result[index - 1];\r\n      const prevDate = hasResult ?\r\n        result[index - 1].comparison.nextDate :\r\n        getFirstDateInPeriod();\r\n      const prevValue = hasResult ?\r\n        result[index - 1].comparison.nextValue :\r\n        this.getRandom(100);\r\n      const nextValue = this.getRandom(100);\r\n      const deltaValue = prevValue - nextValue;\r\n\r\n      const item = {\r\n        date: timePeriod,\r\n        value: this.getRandom(1000),\r\n        delta: {\r\n          up: deltaValue <= 0,\r\n          value: Math.abs(deltaValue),\r\n        },\r\n        comparison: {\r\n          prevDate,\r\n          prevValue,\r\n          nextDate: timePeriod,\r\n          nextValue,\r\n        },\r\n      };\r\n\r\n      return [...result, item];\r\n    }, []);\r\n  }\r\n\r\n  getTrafficListData(period: string): Observable<TrafficList> {\r\n    return observableOf(this.data[period]);\r\n  }\r\n}\r\n"], "mappings": "AACA,SAASA,EAAE,IAAIC,YAAY,QAAqB,MAAM;AAEtD,SAAsBC,eAAe,QAAQ,sBAAsB;;;AAGnE,OAAM,MAAOC,kBAAmB,SAAQD,eAAe;EAKrDE,YAAoBC,MAAsB;IACxC,KAAK,EAAE;IADW,KAAAA,MAAM,GAANA,MAAM;IAHlB,KAAAC,SAAS,GAAIC,OAAe,IAAKC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,EAAE,GAAGH,OAAO,CAAC;IACpE,KAAAI,IAAI,GAAO,EAAE;IAInB,IAAI,CAACA,IAAI,GAAG;MACVC,IAAI,EAAE,IAAI,CAACC,WAAW,EAAE;MACxBC,KAAK,EAAE,IAAI,CAACC,YAAY,EAAE;MAC1BC,IAAI,EAAE,IAAI,CAACC,WAAW;KACvB;EACH;EAEQJ,WAAWA,CAAA;IACjB,MAAMK,oBAAoB,GAAGA,CAAA,KAAK;MAChC,MAAMC,KAAK,GAAG,IAAI,CAACd,MAAM,CAACe,QAAQ,EAAE;MAEpC,OAAOD,KAAK,CAACA,KAAK,CAACE,MAAM,GAAG,CAAC,CAAC;IAChC,CAAC;IAED,OAAO,IAAI,CAACC,UAAU,CAAC,IAAI,CAACjB,MAAM,CAACe,QAAQ,EAAE,EAAEF,oBAAoB,CAAC;EACtE;EAEQH,YAAYA,CAAA;IAClB,MAAMG,oBAAoB,GAAGA,CAAA,KAAK;MAChC,MAAMK,MAAM,GAAG,IAAI,CAAClB,MAAM,CAACmB,SAAS,EAAE;MAEtC,OAAOD,MAAM,CAACA,MAAM,CAACF,MAAM,GAAG,CAAC,CAAC;IAClC,CAAC;IAED,OAAO,IAAI,CAACC,UAAU,CAAC,IAAI,CAACjB,MAAM,CAACmB,SAAS,EAAE,EAAEN,oBAAoB,CAAC;EACvE;EAEQD,WAAWA,CAAA;IACjB,MAAMC,oBAAoB,GAAGA,CAAA,KAAK;MAChC,MAAMO,KAAK,GAAG,IAAI,CAACpB,MAAM,CAACqB,QAAQ,EAAE;MAEpC,OAAO,GAAGC,QAAQ,CAACF,KAAK,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,CAAC,EAAE;IACxC,CAAC;IAED,OAAO,IAAI,CAACH,UAAU,CAAC,IAAI,CAACjB,MAAM,CAACqB,QAAQ,EAAE,EAAER,oBAAoB,CAAC;EACtE;EAEQI,UAAUA,CAACM,WAAqB,EAAEV,oBAAkC;IAC1E,OAAOU,WAAW,CAACC,MAAM,CAAC,CAACC,MAAU,EAAEC,UAAU,EAAEC,KAAK,KAAI;MAC1D,MAAMC,SAAS,GAAGH,MAAM,CAACE,KAAK,GAAG,CAAC,CAAC;MACnC,MAAME,QAAQ,GAAGD,SAAS,GACxBH,MAAM,CAACE,KAAK,GAAG,CAAC,CAAC,CAACG,UAAU,CAACC,QAAQ,GACrClB,oBAAoB,EAAE;MACxB,MAAMmB,SAAS,GAAGJ,SAAS,GACzBH,MAAM,CAACE,KAAK,GAAG,CAAC,CAAC,CAACG,UAAU,CAACG,SAAS,GACtC,IAAI,CAAChC,SAAS,CAAC,GAAG,CAAC;MACrB,MAAMgC,SAAS,GAAG,IAAI,CAAChC,SAAS,CAAC,GAAG,CAAC;MACrC,MAAMiC,UAAU,GAAGF,SAAS,GAAGC,SAAS;MAExC,MAAME,IAAI,GAAG;QACXC,IAAI,EAAEV,UAAU;QAChBW,KAAK,EAAE,IAAI,CAACpC,SAAS,CAAC,IAAI,CAAC;QAC3BqC,KAAK,EAAE;UACLC,EAAE,EAAEL,UAAU,IAAI,CAAC;UACnBG,KAAK,EAAElC,IAAI,CAACqC,GAAG,CAACN,UAAU;SAC3B;QACDJ,UAAU,EAAE;UACVD,QAAQ;UACRG,SAAS;UACTD,QAAQ,EAAEL,UAAU;UACpBO;;OAEH;MAED,OAAO,CAAC,GAAGR,MAAM,EAAEU,IAAI,CAAC;IAC1B,CAAC,EAAE,EAAE,CAAC;EACR;EAEAM,kBAAkBA,CAACzC,MAAc;IAC/B,OAAOJ,YAAY,CAAC,IAAI,CAACU,IAAI,CAACN,MAAM,CAAC,CAAC;EACxC;;;uCA7EWF,kBAAkB,EAAA4C,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,cAAA;IAAA;EAAA;;;aAAlB/C,kBAAkB;MAAAgD,OAAA,EAAlBhD,kBAAkB,CAAAiD;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}