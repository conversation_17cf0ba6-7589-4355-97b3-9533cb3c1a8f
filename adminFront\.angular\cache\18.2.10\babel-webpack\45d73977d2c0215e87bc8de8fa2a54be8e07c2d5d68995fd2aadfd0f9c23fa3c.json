{"ast": null, "code": "import { SharedModule } from '../components/shared.module';\nimport { CommonModule } from '@angular/common';\nimport { EnumFileType } from 'src/app/shared/enum/enumFileType';\nimport { BaseComponent } from '../components/base/baseComponent';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/shared/helper/allowHelper\";\nimport * as i2 from \"@nebular/theme\";\nimport * as i3 from \"src/app/shared/services/message.service\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"@angular/forms\";\nimport * as i6 from \"../components/breadcrumb/breadcrumb.component\";\nfunction CategoryManagementComponent_div_16_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 18);\n    i0.ɵɵlistener(\"click\", function CategoryManagementComponent_div_16_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      const dialog_r3 = i0.ɵɵreference(35);\n      return i0.ɵɵresetView(ctx_r1.addNew(dialog_r3));\n    });\n    i0.ɵɵelementStart(1, \"button\", 19);\n    i0.ɵɵelement(2, \"i\", 20);\n    i0.ɵɵtext(3, \" ?\\uFFFD\\uFFFD?\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction CategoryManagementComponent_tr_32_button_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 24);\n    i0.ɵɵlistener(\"click\", function CategoryManagementComponent_tr_32_button_10_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      const dialog_r3 = i0.ɵɵreference(35);\n      return i0.ɵɵresetView(ctx_r1.addNew(dialog_r3));\n    });\n    i0.ɵɵtext(1, \"\\u7DE8\\u8F2F\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CategoryManagementComponent_tr_32_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"td\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"td\", 21);\n    i0.ɵɵtemplate(10, CategoryManagementComponent_tr_32_button_10_Template, 2, 0, \"button\", 22);\n    i0.ɵɵelementStart(11, \"button\", 23);\n    i0.ɵɵtext(12, \"?\\uFFFD\\u9664\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const item_r5 = ctx.$implicit;\n    const i_r6 = ctx.index;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i_r6 + 1);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r5.cName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r5.cBuildCaseld);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r5.cCategoryName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isCreate);\n  }\n}\nfunction CategoryManagementComponent_ng_template_34_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"nb-tabset\")(2, \"nb-tab\", 28)(3, \"nb-form-field\");\n    i0.ɵɵelement(4, \"nb-icon\", 29);\n    i0.ɵɵelementStart(5, \"input\", 30);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function CategoryManagementComponent_ng_template_34_ng_container_2_Template_input_ngModelChange_5_listener($event) {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r1.houseTypeName, $event) || (ctx_r1.houseTypeName = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 31)(7, \"span\", 32);\n    i0.ɵɵtext(8, \"\\u6A19\\uFFFD???\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"div\", 33)(10, \"button\", 34);\n    i0.ɵɵelement(11, \"nb-icon\", 35);\n    i0.ɵɵtext(12, \" \\u4E0A\\u50B3 \");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(13, \"button\", 36);\n    i0.ɵɵlistener(\"click\", function CategoryManagementComponent_ng_template_34_ng_container_2_Template_button_click_13_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onSubmitDetail());\n    });\n    i0.ɵɵtext(14, \"?\\uFFFD\\u4EA4\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.houseTypeName);\n  }\n}\nfunction CategoryManagementComponent_ng_template_34_nb_tabset_3_tr_39_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"td\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"td\")(10, \"button\", 50);\n    i0.ɵɵlistener(\"click\", function CategoryManagementComponent_ng_template_34_nb_tabset_3_tr_39_Template_button_click_10_listener() {\n      const building_r10 = i0.ɵɵrestoreView(_r9).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.onEdit(building_r10));\n    });\n    i0.ɵɵtext(11, \" \\u7DE8\\u8F2F \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"button\", 51);\n    i0.ɵɵlistener(\"click\", function CategoryManagementComponent_ng_template_34_nb_tabset_3_tr_39_Template_button_click_12_listener() {\n      const building_r10 = i0.ɵɵrestoreView(_r9).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.onDelete(building_r10));\n    });\n    i0.ɵɵtext(13, \" ?\\uFFFD\\u9664 \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const building_r10 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(building_r10.id);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(building_r10.buildingName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(building_r10.floor);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(building_r10.units);\n  }\n}\nfunction CategoryManagementComponent_ng_template_34_nb_tabset_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-tabset\")(1, \"nb-tab\", 28)(2, \"form\", 37);\n    i0.ɵɵlistener(\"ngSubmit\", function CategoryManagementComponent_ng_template_34_nb_tabset_3_Template_form_ngSubmit_2_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onSubmit());\n    });\n    i0.ɵɵelementStart(3, \"div\", 38)(4, \"label\", 39);\n    i0.ɵɵtext(5, \"\\u68DF\\u5225?\\uFFFD\\u7A31:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"input\", 40);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function CategoryManagementComponent_ng_template_34_nb_tabset_3_Template_input_ngModelChange_6_listener($event) {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r1.buildingName, $event) || (ctx_r1.buildingName = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 38)(8, \"label\", 41);\n    i0.ɵɵtext(9, \"\\u6A13\\u5C64:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"input\", 42);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function CategoryManagementComponent_ng_template_34_nb_tabset_3_Template_input_ngModelChange_10_listener($event) {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r1.floor, $event) || (ctx_r1.floor = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"div\", 38)(12, \"label\", 43);\n    i0.ɵɵtext(13, \"?\\uFFFD\\u6578:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"input\", 44);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function CategoryManagementComponent_ng_template_34_nb_tabset_3_Template_input_ngModelChange_14_listener($event) {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r1.units, $event) || (ctx_r1.units = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(15, \"button\", 45);\n    i0.ɵɵtext(16, \"?\\uFFFD\\u4EA4\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(17, \"nb-tab\", 46)(18, \"div\", 3)(19, \"div\", 6)(20, \"nb-form-field\");\n    i0.ɵɵelement(21, \"input\", 7);\n    i0.ɵɵelementStart(22, \"button\", 8);\n    i0.ɵɵelement(23, \"nb-icon\", 9);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelement(24, \"div\", 47);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(25, \"table\", 48)(26, \"thead\")(27, \"tr\", 49)(28, \"th\");\n    i0.ɵɵtext(29, \"\\u68DF\\u5225\\uFFFD??\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(30, \"th\");\n    i0.ɵɵtext(31, \"\\u68DF\\u5225?\\uFFFD\\u7A31\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(32, \"th\");\n    i0.ɵɵtext(33, \"\\u6A13\\u5C64\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(34, \"th\");\n    i0.ɵɵtext(35, \"?\\uFFFD\\u6578\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(36, \"th\");\n    i0.ɵɵtext(37, \"?\\uFFFD\\uFFFD?\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(38, \"tbody\");\n    i0.ɵɵtemplate(39, CategoryManagementComponent_ng_template_34_nb_tabset_3_tr_39_Template, 14, 4, \"tr\", 16);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.buildingName);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.floor);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.units);\n    i0.ɵɵadvance(25);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.buildings);\n  }\n}\nfunction CategoryManagementComponent_ng_template_34_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-card\", 25)(1, \"nb-card-body\", 26);\n    i0.ɵɵtemplate(2, CategoryManagementComponent_ng_template_34_ng_container_2_Template, 15, 1, \"ng-container\", 27)(3, CategoryManagementComponent_ng_template_34_nb_tabset_3_Template, 40, 4, \"nb-tabset\", 27);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(4, \"nb-card-footer\", 17);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.step == \"edit-detail\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.step !== \"edit-detail\");\n  }\n}\nexport class CategoryManagementComponent extends BaseComponent {\n  onCreate() {\n    return;\n  }\n  onEdit(building) {\n    // Xử lý sự kiện khi nhấn nút \"編輯\"\n    // Ví dụ: mở modal để chỉnh sửa thông tin tòa nhà\n    this.step = 'edit-detail';\n  }\n  onDelete(building) {\n    // Xử lý sự kiện khi nhấn nút \"刪除\"\n    // Ví dụ: xác nhận xóa và xóa tòa nhà khỏi danh sách\n  }\n  constructor(_allow, dialogService, message) {\n    super(_allow);\n    this._allow = _allow;\n    this.dialogService = dialogService;\n    this.message = message;\n    this.buildingName = '';\n    this.floor = null;\n    this.units = null;\n    this.step = \"detail\"; // detail, edit, delete\n    this.buildings = [{\n      id: 1,\n      buildingName: 'A棟',\n      floor: 10,\n      units: 50\n    }, {\n      id: 2,\n      buildingName: 'B棟',\n      floor: 15,\n      units: 80\n    }];\n    this.houseTypeName = ''; // Biến để lưu giá trị của trường nhập liệu\n    this.productList = [{\n      cID: 1,\n      cName: \"Product 1\",\n      cFile: \"https://example.com/product1.file\",\n      cBuildCaseld: 123,\n      cCategoryName: \"Electronics\",\n      cStatus: 1,\n      cCreateDT: \"2024-07-02T00:00:00.000Z\",\n      cCreator: \"user123\"\n    }, {\n      cID: 2,\n      cName: \"Product 2\",\n      cFile: \"https://example.com/product2.file\",\n      cCategoryName: \"Clothing\",\n      cStatus: 0,\n      cCreateDT: \"2024-07-02T00:00:00.000Z\",\n      cCreator: \"user456\"\n    }, {\n      cID: 3,\n      cName: \"Product 3\",\n      cFile: \"https://example.com/product3.file\",\n      cBuildCaseld: 456,\n      cCategoryName: \"Furniture\",\n      cStatus: 1,\n      cCreateDT: \"2024-07-02T00:00:00.000Z\",\n      cCreator: \"user789\"\n    }];\n    this.imgSrc = null;\n    this.uploadedFiles = [];\n  }\n  onSubmitDetail() {\n    // Kiểm tra tính hợp lệ (ví dụ: kiểm tra xem trường có rỗng không)\n    if (this.houseTypeName.trim() === '') {\n      // Xử lý trường hợp không hợp lệ (ví dụ: hiển thị thông báo lỗi)\n      return;\n    }\n    // Xử lý logic submit form tại đây\n    console.log(this.houseTypeName);\n  }\n  ngOnInit() {}\n  addNew(ref) {\n    this.isNew = true;\n    this.uploadedFiles = [];\n    this.project = {\n      cName: '',\n      cFile: '',\n      CStatus: '',\n      CSystemInstruction: ''\n    };\n    this.dialogService.open(ref);\n  }\n  onFileSelected(event) {\n    const input = event.target;\n    if (input.files && input.files[0]) {\n      const file = input.files[0];\n      const reader = new FileReader();\n      reader.onload = () => {\n        this.imgSrc = reader.result;\n      };\n      reader.readAsDataURL(file);\n    }\n  }\n  uploadFile(e) {\n    let files = e.target.files;\n    const fileRegex = /pdf|jpg|jpeg|png/i;\n    for (let i = 0; i < files.length; i++) {\n      if (!fileRegex.test(files[i].type)) {\n        this.message.showErrorMSG('檔案格式錯誤，僅限pdf、圖片檔');\n        return;\n      }\n      let file;\n      if (files[i].name.includes('pdf')) {\n        file = files[i];\n        let reader = new FileReader();\n        reader.onload = e => {\n          this.uploadedFiles.push({\n            cID: i,\n            cName: files[i].name,\n            cFile: e.target?.result?.toString().split(',')[1],\n            cImg: null,\n            cFileType: EnumFileType.PDF\n          });\n        };\n        reader.readAsDataURL(files[i]);\n      } else {\n        file = URL.createObjectURL(files[i]);\n        let reader = new FileReader();\n        reader.onload = e => {\n          this.uploadedFiles.push({\n            cID: i,\n            cName: files[i].name,\n            cFile: e.target?.result?.toString().split(',')[1],\n            cImg: file,\n            cFileType: EnumFileType.JPG\n          });\n        };\n        URL.revokeObjectURL(files[i]);\n        reader.readAsDataURL(files[i]);\n      }\n    }\n  }\n  deleteItem(item) {\n    if (window.confirm(`確定要移除【${item.cName}】?`)) {\n      this.uploadedFiles = this.uploadedFiles.filter(i => i.cID !== item.cID);\n    }\n  }\n  onSubmit() {}\n  static {\n    this.ɵfac = function CategoryManagementComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || CategoryManagementComponent)(i0.ɵɵdirectiveInject(i1.AllowHelper), i0.ɵɵdirectiveInject(i2.NbDialogService), i0.ɵɵdirectiveInject(i3.MessageService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: CategoryManagementComponent,\n      selectors: [[\"ngx-category-management\"]],\n      standalone: true,\n      features: [i0.ɵɵInheritDefinitionFeature, i0.ɵɵStandaloneFeature],\n      decls: 36,\n      vars: 2,\n      consts: [[\"dialog\", \"\"], [\"accent\", \"success\"], [1, \"font-bold\", \"text-[#818181]\"], [1, \"row\"], [1, \"col-1\"], [\"for\", \"project\", 1, \"text-nowrap\", \"mr-1\", \"h-full\", \"mt-2\"], [1, \"col-5\"], [\"type\", \"text\", \"nbInput\", \"\"], [\"nbSuffix\", \"\", \"nbButton\", \"\", \"ghost\", \"\"], [\"icon\", \"search\", \"placeholder\", \"\\u5EFA\\uFFFD??\\uFFFD\\u7A31\"], [1, \"col-6\"], [\"class\", \"d-flex justify-content-end\", 3, \"click\", 4, \"ngIf\"], [1, \"table-responsive\", \"mt-4\"], [1, \"table\", \"table-striped\", \"border\", 2, \"min-width\", \"1000px\", \"background-color\", \"#f3f3f3\"], [2, \"background-color\", \"#AE9B66\", \"color\", \"white\"], [\"scope\", \"col\", 1, \"col-1\"], [4, \"ngFor\", \"ngForOf\"], [1, \"d-flex\", \"justify-content-center\"], [1, \"d-flex\", \"justify-content-end\", 3, \"click\"], [1, \"btn\", \"btn-info\"], [1, \"fas\", \"fa-plus\", \"mr-1\"], [1, \"text-center\"], [\"class\", \"btn btn-outline-primary btn-sm m-1\", 3, \"click\", 4, \"ngIf\"], [1, \"btn\", \"btn-outline-primary\", \"btn-sm\", \"m-1\"], [1, \"btn\", \"btn-outline-primary\", \"btn-sm\", \"m-1\", 3, \"click\"], [2, \"width\", \"800px\", \"max-height\", \"95vh\"], [1, \"px-4\"], [4, \"ngIf\"], [\"tabTitle\", \"\\u7DE8\\u8F2F\"], [\"nbPrefix\", \"\", \"icon\", \"home-outline\"], [\"type\", \"text\", \"nbInput\", \"\", \"fullWidth\", \"\", \"name\", \"houseTypeName\", \"placeholder\", \"xxx1\", \"required\", \"\", 3, \"ngModelChange\", \"ngModel\"], [1, \"upload-section\"], [1, \"label\"], [1, \"upload-button\"], [\"nbButton\", \"\", \"outline\", \"\", \"status\", \"primary\"], [\"icon\", \"plus-outline\"], [\"nbButton\", \"\", \"status\", \"primary\", \"type\", \"submit\", 3, \"click\"], [3, \"ngSubmit\"], [1, \"form-group\"], [\"for\", \"buildingName\"], [\"type\", \"text\", \"nbInput\", \"\", \"fullWidth\", \"\", \"id\", \"buildingName\", \"name\", \"buildingName\", \"required\", \"\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"floor\"], [\"type\", \"number\", \"nbInput\", \"\", \"fullWidth\", \"\", \"id\", \"floor\", \"name\", \"floor\", \"required\", \"\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"units\"], [\"type\", \"number\", \"nbInput\", \"\", \"fullWidth\", \"\", \"id\", \"units\", \"name\", \"units\", \"required\", \"\", 3, \"ngModelChange\", \"ngModel\"], [\"nbButton\", \"\", \"status\", \"primary\", \"type\", \"submit\"], [\"tabTitle\", \"?\\uFFFD\\u7D30\"], [1, \"col-7\"], [1, \"w-full\", \"mt-2\", \"table\", \"table-striped\", \"border\", 2, \"min-width\", \"600px\", \"background-color\", \"#f3f3f3\"], [1, \"bg-[#AE9B66]\", \"text-white\"], [\"nbButton\", \"\", \"ghost\", \"\", \"size\", \"small\", 3, \"click\"], [\"nbButton\", \"\", \"ghost\", \"\", \"status\", \"danger\", \"size\", \"small\", 3, \"click\"]],\n      template: function CategoryManagementComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"nb-card\", 1)(1, \"nb-card-header\");\n          i0.ɵɵelement(2, \"ngx-breadcrumb\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(3, \"nb-card-body\")(4, \"h1\", 2);\n          i0.ɵɵtext(5, \"?\\uFFFD\\u8868??\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(6, \"div\", 3)(7, \"div\", 4)(8, \"label\", 5);\n          i0.ɵɵtext(9, \"\\u5EFA\\uFFFD??\\uFFFD\\u7A31 \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(10, \"div\", 6)(11, \"nb-form-field\");\n          i0.ɵɵelement(12, \"input\", 7);\n          i0.ɵɵelementStart(13, \"button\", 8);\n          i0.ɵɵelement(14, \"nb-icon\", 9);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(15, \"div\", 10);\n          i0.ɵɵtemplate(16, CategoryManagementComponent_div_16_Template, 4, 0, \"div\", 11);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(17, \"div\", 12)(18, \"table\", 13)(19, \"thead\")(20, \"tr\", 14)(21, \"th\", 15);\n          i0.ɵɵtext(22, \"\\u6D41\\u6C34??/th> \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(23, \"th\", 15);\n          i0.ɵɵtext(24, \"\\u68DF\\u5225?\\uFFFD\\u7A31\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(25, \"th\", 15);\n          i0.ɵɵtext(26, \"\\u6A13\\u5C64\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(27, \"th\", 15);\n          i0.ɵɵtext(28, \"?\\uFFFD\\u6578\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(29, \"th\", 15);\n          i0.ɵɵtext(30, \"?\\uFFFD\\uFFFD?\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(31, \"tbody\");\n          i0.ɵɵtemplate(32, CategoryManagementComponent_tr_32_Template, 13, 5, \"tr\", 16);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelement(33, \"nb-card-footer\", 17);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(34, CategoryManagementComponent_ng_template_34_Template, 5, 2, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor);\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(16);\n          i0.ɵɵproperty(\"ngIf\", ctx.isCreate);\n          i0.ɵɵadvance(16);\n          i0.ɵɵproperty(\"ngForOf\", ctx.productList);\n        }\n      },\n      dependencies: [CommonModule, i4.NgForOf, i4.NgIf, SharedModule, i5.ɵNgNoValidate, i5.DefaultValueAccessor, i5.NumberValueAccessor, i5.NgControlStatus, i5.NgControlStatusGroup, i5.RequiredValidator, i5.NgModel, i5.NgForm, i2.NbCardComponent, i2.NbCardBodyComponent, i2.NbCardFooterComponent, i2.NbCardHeaderComponent, i2.NbInputDirective, i2.NbButtonComponent, i2.NbTabsetComponent, i2.NbTabComponent, i2.NbFormFieldComponent, i2.NbPrefixDirective, i2.NbSuffixDirective, i2.NbIconComponent, i6.BreadcrumbComponent],\n      styles: [\"/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsImZpbGUiOiJjYXRlZ29yeS1tYW5hZ2VtZW50LmNvbXBvbmVudC5zY3NzIn0= */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvcGFnZXMvY2F0ZWdvcnktbWFuYWdlbWVudC9jYXRlZ29yeS1tYW5hZ2VtZW50LmNvbXBvbmVudC5zY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiI7QUFDQSxvTEFBb0wiLCJzb3VyY2VSb290IjoiIn0= */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["SharedModule", "CommonModule", "EnumFileType", "BaseComponent", "i0", "ɵɵelementStart", "ɵɵlistener", "CategoryManagementComponent_div_16_Template_div_click_0_listener", "ɵɵrestoreView", "_r1", "ctx_r1", "ɵɵnextContext", "dialog_r3", "ɵɵreference", "ɵɵresetView", "addNew", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd", "CategoryManagementComponent_tr_32_button_10_Template_button_click_0_listener", "_r4", "ɵɵtemplate", "CategoryManagementComponent_tr_32_button_10_Template", "ɵɵadvance", "ɵɵtextInterpolate", "i_r6", "item_r5", "cName", "cBuildCaseld", "cCategoryName", "ɵɵproperty", "isCreate", "ɵɵelementContainerStart", "ɵɵtwoWayListener", "CategoryManagementComponent_ng_template_34_ng_container_2_Template_input_ngModelChange_5_listener", "$event", "_r7", "ɵɵtwoWayBindingSet", "houseTypeName", "CategoryManagementComponent_ng_template_34_ng_container_2_Template_button_click_13_listener", "onSubmitDetail", "ɵɵtwoWayProperty", "CategoryManagementComponent_ng_template_34_nb_tabset_3_tr_39_Template_button_click_10_listener", "building_r10", "_r9", "$implicit", "onEdit", "CategoryManagementComponent_ng_template_34_nb_tabset_3_tr_39_Template_button_click_12_listener", "onDelete", "id", "buildingName", "floor", "units", "CategoryManagementComponent_ng_template_34_nb_tabset_3_Template_form_ngSubmit_2_listener", "_r8", "onSubmit", "CategoryManagementComponent_ng_template_34_nb_tabset_3_Template_input_ngModelChange_6_listener", "CategoryManagementComponent_ng_template_34_nb_tabset_3_Template_input_ngModelChange_10_listener", "CategoryManagementComponent_ng_template_34_nb_tabset_3_Template_input_ngModelChange_14_listener", "CategoryManagementComponent_ng_template_34_nb_tabset_3_tr_39_Template", "buildings", "CategoryManagementComponent_ng_template_34_ng_container_2_Template", "CategoryManagementComponent_ng_template_34_nb_tabset_3_Template", "step", "CategoryManagementComponent", "onCreate", "building", "constructor", "_allow", "dialogService", "message", "productList", "cID", "cFile", "cStatus", "cCreateDT", "cCreator", "imgSrc", "uploadedFiles", "trim", "console", "log", "ngOnInit", "ref", "isNew", "project", "CStatus", "CSystemInstruction", "open", "onFileSelected", "event", "input", "target", "files", "file", "reader", "FileReader", "onload", "result", "readAsDataURL", "uploadFile", "e", "fileRegex", "i", "length", "test", "type", "showErrorMSG", "name", "includes", "push", "toString", "split", "cImg", "cFileType", "PDF", "URL", "createObjectURL", "JPG", "revokeObjectURL", "deleteItem", "item", "window", "confirm", "filter", "ɵɵdirectiveInject", "i1", "AllowHelper", "i2", "NbDialogService", "i3", "MessageService", "selectors", "standalone", "features", "ɵɵInheritDefinitionFeature", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "CategoryManagementComponent_Template", "rf", "ctx", "CategoryManagementComponent_div_16_Template", "CategoryManagementComponent_tr_32_Template", "CategoryManagementComponent_ng_template_34_Template", "ɵɵtemplateRefExtractor", "i4", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "i5", "ɵNgNoValidate", "DefaultValueAccessor", "NumberValueAccessor", "NgControlStatus", "NgControlStatusGroup", "RequiredValidator", "NgModel", "NgForm", "NbCardComponent", "NbCardBodyComponent", "NbCardFooterComponent", "NbCardHeaderComponent", "NbInputDirective", "NbButtonComponent", "NbTabsetComponent", "NbTabComponent", "NbFormFieldComponent", "NbPrefixDirective", "NbSuffixDirective", "NbIconComponent", "i6", "BreadcrumbComponent", "styles"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\category-management\\category-management.component.ts", "C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\category-management\\category-management.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { SharedModule } from '../components/shared.module';\r\nimport { CommonModule } from '@angular/common';\r\nimport { NbDialogService } from '@nebular/theme';\r\nimport { MessageService } from 'src/app/shared/services/message.service';\r\nimport { EnumFileType } from 'src/app/shared/enum/enumFileType';\r\nimport { BaseComponent } from '../components/base/baseComponent';\r\nimport { AllowHelper } from 'src/app/shared/helper/allowHelper';\r\n\r\n@Component({\r\n  selector: 'ngx-category-management',\r\n  templateUrl: './category-management.component.html',\r\n  styleUrls: ['./category-management.component.scss'],\r\n  standalone: true,\r\n  imports: [CommonModule, SharedModule,],\r\n})\r\n\r\nexport class CategoryManagementComponent extends BaseComponent implements OnInit {\r\n  buildingName = '';\r\n  floor = null;\r\n  units = null;\r\n  step = \"detail\" // detail, edit, delete\r\n\r\n  buildings = [\r\n    { id: 1, buildingName: 'A棟', floor: 10, units: 50 },\r\n    { id: 2, buildingName: 'B棟', floor: 15, units: 80 },\r\n  ];\r\n\r\n  onCreate() {\r\n    return\r\n  }\r\n\r\n  onEdit(building: any) {\r\n    // Xử lý sự kiện khi nhấn nút \"編輯\"\r\n    // Ví dụ: mở modal để chỉnh sửa thông tin tòa nhà\r\n    this.step = 'edit-detail'\r\n  }\r\n\r\n  onDelete(building: any) {\r\n    // Xử lý sự kiện khi nhấn nút \"刪除\"\r\n    // Ví dụ: xác nhận xóa và xóa tòa nhà khỏi danh sách\r\n  }\r\n\r\n  constructor(\r\n    private _allow: AllowHelper,\r\n    private dialogService: NbDialogService,\r\n    private message: MessageService,\r\n  ) {\r\n    super(_allow)\r\n  }\r\n  houseTypeName = ''; // Biến để lưu giá trị của trường nhập liệu\r\n  onSubmitDetail() {\r\n    // Kiểm tra tính hợp lệ (ví dụ: kiểm tra xem trường có rỗng không)\r\n    if (this.houseTypeName.trim() === '') {\r\n      // Xử lý trường hợp không hợp lệ (ví dụ: hiển thị thông báo lỗi)\r\n      return;\r\n    }\r\n\r\n    // Xử lý logic submit form tại đây\r\n    console.log(this.houseTypeName);\r\n  }\r\n\r\n  override ngOnInit(): void {\r\n  }\r\n\r\n  search: string\r\n\r\n  productList: any[] = [\r\n    {\r\n      cID: 1,\r\n      cName: \"Product 1\",\r\n      cFile: \"https://example.com/product1.file\",\r\n      cBuildCaseld: 123,\r\n      cCategoryName: \"Electronics\",\r\n      cStatus: 1,\r\n      cCreateDT: \"2024-07-02T00:00:00.000Z\",\r\n      cCreator: \"user123\"\r\n    },\r\n    {\r\n      cID: 2,\r\n      cName: \"Product 2\",\r\n      cFile: \"https://example.com/product2.file\",\r\n      cCategoryName: \"Clothing\",\r\n      cStatus: 0,\r\n      cCreateDT: \"2024-07-02T00:00:00.000Z\",\r\n      cCreator: \"user456\"\r\n    },\r\n    {\r\n      cID: 3,\r\n      cName: \"Product 3\",\r\n      cFile: \"https://example.com/product3.file\",\r\n      cBuildCaseld: 456,\r\n      cCategoryName: \"Furniture\",\r\n      cStatus: 1,\r\n      cCreateDT: \"2024-07-02T00:00:00.000Z\",\r\n      cCreator: \"user789\"\r\n    }\r\n  ];\r\n\r\n\r\n  project: {\r\n    cName: string\r\n    cFile: string\r\n    CStatus: string\r\n    CSystemInstruction: string\r\n  }\r\n\r\n  status: any\r\n  isNew: true\r\n\r\n  addNew(ref: any) {\r\n    this.isNew = true;\r\n    this.uploadedFiles = [];\r\n    this.project = {\r\n      cName: '',\r\n      cFile: '',\r\n      CStatus: '',\r\n      CSystemInstruction: '',\r\n    }\r\n    this.dialogService.open(ref)\r\n  }\r\n\r\n  imgSrc: string | ArrayBuffer | null = null;\r\n\r\n  onFileSelected(event: Event): void {\r\n    const input = event.target as HTMLInputElement;\r\n    if (input.files && input.files[0]) {\r\n      const file = input.files[0];\r\n      const reader = new FileReader();\r\n      reader.onload = () => {\r\n        this.imgSrc = reader.result;\r\n      };\r\n      reader.readAsDataURL(file);\r\n    }\r\n  }\r\n\r\n  uploadedFiles: any[] = [];\r\n\r\n  uploadFile(e: any) {\r\n    let files = e.target.files;\r\n    const fileRegex = /pdf|jpg|jpeg|png/i;\r\n\r\n    for (let i = 0; i < files.length; i++) {\r\n      if (!fileRegex.test(files[i].type)) {\r\n        this.message.showErrorMSG('檔案格式錯誤，僅限pdf、圖片檔')\r\n        return\r\n      }\r\n      let file: string;\r\n      if (files[i].name.includes('pdf')) {\r\n        file = files[i];\r\n        let reader = new FileReader();\r\n        reader.onload = e => {\r\n          this.uploadedFiles.push({\r\n            cID: i,\r\n            cName: files[i].name,\r\n            cFile: e.target?.result?.toString().split(',')[1],\r\n            cImg: null,\r\n            cFileType: EnumFileType.PDF\r\n          })\r\n        }\r\n        reader.readAsDataURL(files[i]);\r\n      } else {\r\n        file = URL.createObjectURL(files[i]);\r\n        let reader = new FileReader();\r\n        reader.onload = e => {\r\n          this.uploadedFiles.push({\r\n            cID: i,\r\n            cName: files[i].name,\r\n            cFile: e.target?.result?.toString().split(',')[1],\r\n            cImg: file,\r\n            cFileType: EnumFileType.JPG\r\n          })\r\n        }\r\n        URL.revokeObjectURL(files[i])\r\n        reader.readAsDataURL(files[i]);\r\n      }\r\n    }\r\n  }\r\n\r\n  deleteItem(item: any) {\r\n    if (window.confirm(`確定要移除【${item.cName}】?`)) {\r\n      this.uploadedFiles = this.uploadedFiles.filter(i => i.cID !== item.cID);\r\n    }\r\n  }\r\n\r\n  onSubmit() {\r\n  }\r\n\r\n}\r\n", "<nb-card accent=\"success\">\r\n  <nb-card-header>\r\n    <ngx-breadcrumb></ngx-breadcrumb>\r\n  </nb-card-header>\r\n  <nb-card-body>\r\n    <h1 class=\"font-bold text-[#818181]\">?�表??</h1>\r\n    <div class=\"row\">\r\n      <div class=\"col-1\">\r\n        <label for=\"project\" class=\"text-nowrap mr-1 h-full mt-2\">建�??�稱\r\n        </label>\r\n      </div>\r\n      <div class=\"col-5\">\r\n        <nb-form-field>\r\n\r\n          <input type=\"text\" nbInput>\r\n          <button nbSuffix nbButton ghost>\r\n            <nb-icon icon=\"search\" placeholder=\"建�??�稱\">\r\n            </nb-icon>\r\n          </button>\r\n        </nb-form-field>\r\n      </div>\r\n      <div class=\"col-6\">\r\n        <div class=\"d-flex justify-content-end\" (click)=\"addNew(dialog)\" *ngIf=\"isCreate\">\r\n          <button class=\"btn btn-info\">\r\n            <i class=\"fas fa-plus mr-1\"></i> ?��?</button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <div class=\"table-responsive mt-4\">\r\n      <table class=\"table table-striped border \" style=\"min-width: 1000px; background-color:#f3f3f3;\">\r\n        <thead>\r\n          <tr style=\"background-color: #AE9B66; color: white;\">\r\n            <th scope=\"col\" class=\"col-1\">流水??/th>\r\n            <th scope=\"col\" class=\"col-1\">棟別?�稱</th>\r\n            <th scope=\"col\" class=\"col-1\">樓層</th>\r\n            <th scope=\"col\" class=\"col-1\">?�數</th>\r\n            <th scope=\"col\" class=\"col-1\">?��?</th>\r\n          </tr>\r\n        </thead>\r\n        <tbody>\r\n          <tr *ngFor=\"let item of productList ; let i = index\">\r\n            <td>{{ i + 1}}</td>\r\n            <td>{{item.cName}}</td>\r\n            <td>{{item.cBuildCaseld}}</td>\r\n            <td>{{item.cCategoryName}}</td>\r\n            <td class=\"text-center\">\r\n              <button class=\"btn btn-outline-primary btn-sm m-1\" (click)=\"addNew(dialog)\" *ngIf=\"isCreate\">編輯</button>\r\n              <button class=\"btn btn-outline-primary btn-sm m-1\">?�除</button>\r\n            </td>\r\n          </tr>\r\n        </tbody>\r\n      </table>\r\n    </div>\r\n  </nb-card-body>\r\n  <nb-card-footer class=\"d-flex justify-content-center\">\r\n  </nb-card-footer>\r\n</nb-card>\r\n\r\n<ng-template #dialog let-dialog let-ref=\"dialogRef\">\r\n  <nb-card style=\"width:800px; max-height: 95vh\">\r\n    <nb-card-body class=\"px-4\">\r\n      <ng-container *ngIf=\"step=='edit-detail'\">\r\n        <nb-tabset>\r\n          <nb-tab tabTitle=\"編輯\">\r\n            <nb-form-field>\r\n              <nb-icon nbPrefix icon=\"home-outline\"></nb-icon>\r\n              <input type=\"text\" nbInput fullWidth [(ngModel)]=\"houseTypeName\" name=\"houseTypeName\" placeholder=\"xxx1\"\r\n                required />\r\n            </nb-form-field>\r\n\r\n            <div class=\"upload-section\">\r\n              <span class=\"label\">標�???</span>\r\n              <div class=\"upload-button\">\r\n                <button nbButton outline status=\"primary\">\r\n                  <nb-icon icon=\"plus-outline\"></nb-icon>\r\n                  上傳\r\n                </button>\r\n              </div>\r\n            </div>\r\n\r\n            <button nbButton status=\"primary\" type=\"submit\" (click)=\"onSubmitDetail()\">?�交</button>\r\n\r\n          </nb-tab>\r\n        </nb-tabset>\r\n      </ng-container>\r\n\r\n      <nb-tabset *ngIf=\"step!=='edit-detail'\">\r\n        <nb-tab tabTitle=\"編輯\">\r\n          <form (ngSubmit)=\"onSubmit()\">\r\n            <div class=\"form-group\">\r\n              <label for=\"buildingName\">棟別?�稱:</label>\r\n              <input type=\"text\" nbInput fullWidth id=\"buildingName\" [(ngModel)]=\"buildingName\" name=\"buildingName\"\r\n                required>\r\n            </div>\r\n\r\n            <div class=\"form-group\">\r\n              <label for=\"floor\">樓層:</label>\r\n              <input type=\"number\" nbInput fullWidth id=\"floor\" [(ngModel)]=\"floor\" name=\"floor\" required>\r\n            </div>\r\n\r\n            <div class=\"form-group\">\r\n              <label for=\"units\">?�數:</label>\r\n              <input type=\"number\" nbInput fullWidth id=\"units\" [(ngModel)]=\"units\" name=\"units\" required>\r\n            </div>\r\n\r\n            <button nbButton status=\"primary\" type=\"submit\">?�交</button>\r\n          </form>\r\n        </nb-tab>\r\n        <nb-tab tabTitle=\"?�細\">\r\n          <div class=\"row\">\r\n            <div class=\"col-5\">\r\n              <nb-form-field>\r\n                <input type=\"text\" nbInput>\r\n                <button nbSuffix nbButton ghost>\r\n                  <nb-icon icon=\"search\" placeholder=\"建�??�稱\">\r\n                  </nb-icon>\r\n                </button>\r\n              </nb-form-field>\r\n            </div>\r\n            <div class=\"col-7\">\r\n            </div>\r\n          </div>\r\n          <table class=\"w-full mt-2 table table-striped border\" style=\"min-width: 600px; background-color:#f3f3f3;\">\r\n            <thead>\r\n              <tr class=\"bg-[#AE9B66] text-white\">\r\n                <th>棟別�??</th>\r\n                <th>棟別?�稱</th>\r\n                <th>樓層</th>\r\n                <th>?�數</th>\r\n                <th>?��?</th>\r\n              </tr>\r\n            </thead>\r\n            <tbody>\r\n              <tr *ngFor=\"let building of buildings\">\r\n                <td>{{ building.id }}</td>\r\n                <td>{{ building.buildingName }}</td>\r\n                <td>{{ building.floor }}</td>\r\n                <td>{{ building.units }}</td>\r\n                <td>\r\n                  <button nbButton ghost size=\"small\" (click)=\"onEdit(building)\">\r\n                    編輯\r\n                  </button>\r\n                  <button nbButton ghost status=\"danger\" size=\"small\" (click)=\"onDelete(building)\">\r\n                    ?�除\r\n                  </button>\r\n                </td>\r\n              </tr>\r\n            </tbody>\r\n          </table>\r\n        </nb-tab>\r\n      </nb-tabset>\r\n    </nb-card-body>\r\n    <nb-card-footer class=\"d-flex justify-content-center\">\r\n      <!-- <button class=\"btn btn-danger btn-sm mr-4\" (click)=\"ref.close()\">{{ isNew ? '?��?' : '?��?'}}</button>\r\n      <button class=\"btn btn-success btn-sm\" [hidden]=\"!isNew\" (click)=\"submit(ref)\">?��?</button> -->\r\n    </nb-card-footer>\r\n  </nb-card>\r\n</ng-template>\r\n\r\n<!-- <ng-template #dialog let-dialog let-ref=\"dialogRef\">\r\n  <nb-card style=\"width:800px; max-height: 95vh\">\r\n    <nb-card-header>\r\n      客�??��???\n    </nb-card-header>\r\n    <nb-card-body class=\"px-4\">\r\n      <nb-tabset>\r\n        <nb-tab tabTitle=\"編輯\">\r\n          <form (ngSubmit)=\"onSubmit()\">\r\n            <div class=\"form-group\">\r\n              <label for=\"buildingName\">棟別?�稱:</label>\r\n              <input type=\"text\" nbInput fullWidth id=\"buildingName\" [(ngModel)]=\"buildingName\" name=\"buildingName\" required>\r\n            </div>\r\n  \r\n            <div class=\"form-group\">\r\n              <label for=\"floor\">樓層:</label>\r\n              <input type=\"number\" nbInput fullWidth id=\"floor\" [(ngModel)]=\"floor\" name=\"floor\" required>\r\n            </div>\r\n  \r\n            <div class=\"form-group\">\r\n              <label for=\"units\">?�數:</label>\r\n              <input type=\"number\" nbInput fullWidth id=\"units\" [(ngModel)]=\"units\" name=\"units\" required>\r\n            </div>\r\n  \r\n            <button nbButton status=\"primary\" type=\"submit\">?�交</button>\r\n          </form>\r\n        </nb-tab>\r\n        <nb-tab tabTitle=\"?�細\">\r\n          <div class=\"row\">\r\n            <div class=\"col-5\">\r\n              <nb-form-field>\r\n                <input type=\"text\" nbInput>\r\n                <button nbSuffix nbButton ghost>\r\n                  <nb-icon icon=\"search\" placeholder=\"建�??�稱\">\r\n                  </nb-icon>\r\n                </button>\r\n              </nb-form-field>\r\n            </div>\r\n            <div class=\"col-7\">\r\n            </div>\r\n          </div>\r\n              <table class=\"w-full mt-2 table table-striped border\" style=\"min-width: 600px; background-color:#f3f3f3;\">\r\n                <thead>\r\n                  <tr  class=\"bg-[#AE9B66] text-white\">\r\n                    <th>棟別�??</th>\r\n                    <th>棟別?�稱</th>\r\n                    <th>樓層</th>\r\n                    <th>?�數</th>\r\n                    <th>?��?</th>\r\n                  </tr>\r\n                </thead>\r\n                <tbody>\r\n                  <tr *ngFor=\"let building of buildings\">\r\n                    <td>{{ building.id }}</td>\r\n                    <td>{{ building.buildingName }}</td>\r\n                    <td>{{ building.floor }}</td>\r\n                    <td>{{ building.units }}</td>\r\n                    <td>\r\n                      <button nbButton ghost size=\"small\" (click)=\"onEdit(building)\">\r\n                        編輯\r\n                      </button>\r\n                      <button nbButton ghost status=\"danger\" size=\"small\" (click)=\"onDelete(building)\">\r\n                        ?�除\r\n                      </button>\r\n                    </td>\r\n                  </tr>\r\n                </tbody>\r\n              </table>\r\n        </nb-tab>\r\n      </nb-tabset>\r\n    </nb-card-body>\r\n    <nb-card-footer class=\"d-flex justify-content-center\">\r\n      <button class=\"btn btn-danger btn-sm mr-4\" (click)=\"ref.close()\">{{ isNew ? '?��?' : '?��?'}}</button>\r\n      <button class=\"btn btn-success btn-sm\" [hidden]=\"!isNew\" (click)=\"submit(ref)\">?��?</button>\r\n    </nb-card-footer>\r\n  </nb-card> \r\n</ng-template> -->\r\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,6BAA6B;AAC1D,SAASC,YAAY,QAAQ,iBAAiB;AAG9C,SAASC,YAAY,QAAQ,kCAAkC;AAC/D,SAASC,aAAa,QAAQ,kCAAkC;;;;;;;;;;;ICgBxDC,EAAA,CAAAC,cAAA,cAAkF;IAA1CD,EAAA,CAAAE,UAAA,mBAAAC,iEAAA;MAAAH,EAAA,CAAAI,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,MAAAC,SAAA,GAAAR,EAAA,CAAAS,WAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASJ,MAAA,CAAAK,MAAA,CAAAH,SAAA,CAAc;IAAA,EAAC;IAC9DR,EAAA,CAAAC,cAAA,iBAA6B;IAC3BD,EAAA,CAAAY,SAAA,YAAgC;IAACZ,EAAA,CAAAa,MAAA,sBAAI;IACzCb,EADyC,CAAAc,YAAA,EAAS,EAC5C;;;;;;IAsBAd,EAAA,CAAAC,cAAA,iBAA6F;IAA1CD,EAAA,CAAAE,UAAA,mBAAAa,6EAAA;MAAAf,EAAA,CAAAI,aAAA,CAAAY,GAAA;MAAA,MAAAV,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,MAAAC,SAAA,GAAAR,EAAA,CAAAS,WAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASJ,MAAA,CAAAK,MAAA,CAAAH,SAAA,CAAc;IAAA,EAAC;IAAkBR,EAAA,CAAAa,MAAA,mBAAE;IAAAb,EAAA,CAAAc,YAAA,EAAS;;;;;IAL1Gd,EADF,CAAAC,cAAA,SAAqD,SAC/C;IAAAD,EAAA,CAAAa,MAAA,GAAU;IAAAb,EAAA,CAAAc,YAAA,EAAK;IACnBd,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAa,MAAA,GAAc;IAAAb,EAAA,CAAAc,YAAA,EAAK;IACvBd,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAa,MAAA,GAAqB;IAAAb,EAAA,CAAAc,YAAA,EAAK;IAC9Bd,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAa,MAAA,GAAsB;IAAAb,EAAA,CAAAc,YAAA,EAAK;IAC/Bd,EAAA,CAAAC,cAAA,aAAwB;IACtBD,EAAA,CAAAiB,UAAA,KAAAC,oDAAA,qBAA6F;IAC7FlB,EAAA,CAAAC,cAAA,kBAAmD;IAAAD,EAAA,CAAAa,MAAA,qBAAG;IAE1Db,EAF0D,CAAAc,YAAA,EAAS,EAC5D,EACF;;;;;;IARCd,EAAA,CAAAmB,SAAA,GAAU;IAAVnB,EAAA,CAAAoB,iBAAA,CAAAC,IAAA,KAAU;IACVrB,EAAA,CAAAmB,SAAA,GAAc;IAAdnB,EAAA,CAAAoB,iBAAA,CAAAE,OAAA,CAAAC,KAAA,CAAc;IACdvB,EAAA,CAAAmB,SAAA,GAAqB;IAArBnB,EAAA,CAAAoB,iBAAA,CAAAE,OAAA,CAAAE,YAAA,CAAqB;IACrBxB,EAAA,CAAAmB,SAAA,GAAsB;IAAtBnB,EAAA,CAAAoB,iBAAA,CAAAE,OAAA,CAAAG,aAAA,CAAsB;IAEqDzB,EAAA,CAAAmB,SAAA,GAAc;IAAdnB,EAAA,CAAA0B,UAAA,SAAApB,MAAA,CAAAqB,QAAA,CAAc;;;;;;IAenG3B,EAAA,CAAA4B,uBAAA,GAA0C;IAGpC5B,EAFJ,CAAAC,cAAA,gBAAW,iBACa,oBACL;IACbD,EAAA,CAAAY,SAAA,kBAAgD;IAChDZ,EAAA,CAAAC,cAAA,gBACa;IADwBD,EAAA,CAAA6B,gBAAA,2BAAAC,kGAAAC,MAAA;MAAA/B,EAAA,CAAAI,aAAA,CAAA4B,GAAA;MAAA,MAAA1B,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAAP,EAAA,CAAAiC,kBAAA,CAAA3B,MAAA,CAAA4B,aAAA,EAAAH,MAAA,MAAAzB,MAAA,CAAA4B,aAAA,GAAAH,MAAA;MAAA,OAAA/B,EAAA,CAAAU,WAAA,CAAAqB,MAAA;IAAA,EAA2B;IAElE/B,EAFE,CAAAc,YAAA,EACa,EACC;IAGdd,EADF,CAAAC,cAAA,cAA4B,eACN;IAAAD,EAAA,CAAAa,MAAA,sBAAK;IAAAb,EAAA,CAAAc,YAAA,EAAO;IAE9Bd,EADF,CAAAC,cAAA,cAA2B,kBACiB;IACxCD,EAAA,CAAAY,SAAA,mBAAuC;IACvCZ,EAAA,CAAAa,MAAA,sBACF;IAEJb,EAFI,CAAAc,YAAA,EAAS,EACL,EACF;IAENd,EAAA,CAAAC,cAAA,kBAA2E;IAA3BD,EAAA,CAAAE,UAAA,mBAAAiC,4FAAA;MAAAnC,EAAA,CAAAI,aAAA,CAAA4B,GAAA;MAAA,MAAA1B,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAU,WAAA,CAASJ,MAAA,CAAA8B,cAAA,EAAgB;IAAA,EAAC;IAACpC,EAAA,CAAAa,MAAA,qBAAG;IAGlFb,EAHkF,CAAAc,YAAA,EAAS,EAEhF,EACC;;;;;IAjB+Bd,EAAA,CAAAmB,SAAA,GAA2B;IAA3BnB,EAAA,CAAAqC,gBAAA,YAAA/B,MAAA,CAAA4B,aAAA,CAA2B;;;;;;IAoE9DlC,EADF,CAAAC,cAAA,SAAuC,SACjC;IAAAD,EAAA,CAAAa,MAAA,GAAiB;IAAAb,EAAA,CAAAc,YAAA,EAAK;IAC1Bd,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAa,MAAA,GAA2B;IAAAb,EAAA,CAAAc,YAAA,EAAK;IACpCd,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAa,MAAA,GAAoB;IAAAb,EAAA,CAAAc,YAAA,EAAK;IAC7Bd,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAa,MAAA,GAAoB;IAAAb,EAAA,CAAAc,YAAA,EAAK;IAE3Bd,EADF,CAAAC,cAAA,SAAI,kBAC6D;IAA3BD,EAAA,CAAAE,UAAA,mBAAAoC,+FAAA;MAAA,MAAAC,YAAA,GAAAvC,EAAA,CAAAI,aAAA,CAAAoC,GAAA,EAAAC,SAAA;MAAA,MAAAnC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAU,WAAA,CAASJ,MAAA,CAAAoC,MAAA,CAAAH,YAAA,CAAgB;IAAA,EAAC;IAC5DvC,EAAA,CAAAa,MAAA,sBACF;IAAAb,EAAA,CAAAc,YAAA,EAAS;IACTd,EAAA,CAAAC,cAAA,kBAAiF;IAA7BD,EAAA,CAAAE,UAAA,mBAAAyC,+FAAA;MAAA,MAAAJ,YAAA,GAAAvC,EAAA,CAAAI,aAAA,CAAAoC,GAAA,EAAAC,SAAA;MAAA,MAAAnC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAU,WAAA,CAASJ,MAAA,CAAAsC,QAAA,CAAAL,YAAA,CAAkB;IAAA,EAAC;IAC9EvC,EAAA,CAAAa,MAAA,uBACF;IAEJb,EAFI,CAAAc,YAAA,EAAS,EACN,EACF;;;;IAZCd,EAAA,CAAAmB,SAAA,GAAiB;IAAjBnB,EAAA,CAAAoB,iBAAA,CAAAmB,YAAA,CAAAM,EAAA,CAAiB;IACjB7C,EAAA,CAAAmB,SAAA,GAA2B;IAA3BnB,EAAA,CAAAoB,iBAAA,CAAAmB,YAAA,CAAAO,YAAA,CAA2B;IAC3B9C,EAAA,CAAAmB,SAAA,GAAoB;IAApBnB,EAAA,CAAAoB,iBAAA,CAAAmB,YAAA,CAAAQ,KAAA,CAAoB;IACpB/C,EAAA,CAAAmB,SAAA,GAAoB;IAApBnB,EAAA,CAAAoB,iBAAA,CAAAmB,YAAA,CAAAS,KAAA,CAAoB;;;;;;IAjD9BhD,EAFJ,CAAAC,cAAA,gBAAwC,iBAChB,eACU;IAAxBD,EAAA,CAAAE,UAAA,sBAAA+C,yFAAA;MAAAjD,EAAA,CAAAI,aAAA,CAAA8C,GAAA;MAAA,MAAA5C,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAU,WAAA,CAAYJ,MAAA,CAAA6C,QAAA,EAAU;IAAA,EAAC;IAEzBnD,EADF,CAAAC,cAAA,cAAwB,gBACI;IAAAD,EAAA,CAAAa,MAAA,iCAAM;IAAAb,EAAA,CAAAc,YAAA,EAAQ;IACxCd,EAAA,CAAAC,cAAA,gBACW;IAD4CD,EAAA,CAAA6B,gBAAA,2BAAAuB,+FAAArB,MAAA;MAAA/B,EAAA,CAAAI,aAAA,CAAA8C,GAAA;MAAA,MAAA5C,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAAP,EAAA,CAAAiC,kBAAA,CAAA3B,MAAA,CAAAwC,YAAA,EAAAf,MAAA,MAAAzB,MAAA,CAAAwC,YAAA,GAAAf,MAAA;MAAA,OAAA/B,EAAA,CAAAU,WAAA,CAAAqB,MAAA;IAAA,EAA0B;IAEnF/B,EAFE,CAAAc,YAAA,EACW,EACP;IAGJd,EADF,CAAAC,cAAA,cAAwB,gBACH;IAAAD,EAAA,CAAAa,MAAA,oBAAG;IAAAb,EAAA,CAAAc,YAAA,EAAQ;IAC9Bd,EAAA,CAAAC,cAAA,iBAA4F;IAA1CD,EAAA,CAAA6B,gBAAA,2BAAAwB,gGAAAtB,MAAA;MAAA/B,EAAA,CAAAI,aAAA,CAAA8C,GAAA;MAAA,MAAA5C,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAAP,EAAA,CAAAiC,kBAAA,CAAA3B,MAAA,CAAAyC,KAAA,EAAAhB,MAAA,MAAAzB,MAAA,CAAAyC,KAAA,GAAAhB,MAAA;MAAA,OAAA/B,EAAA,CAAAU,WAAA,CAAAqB,MAAA;IAAA,EAAmB;IACvE/B,EADE,CAAAc,YAAA,EAA4F,EACxF;IAGJd,EADF,CAAAC,cAAA,eAAwB,iBACH;IAAAD,EAAA,CAAAa,MAAA,sBAAI;IAAAb,EAAA,CAAAc,YAAA,EAAQ;IAC/Bd,EAAA,CAAAC,cAAA,iBAA4F;IAA1CD,EAAA,CAAA6B,gBAAA,2BAAAyB,gGAAAvB,MAAA;MAAA/B,EAAA,CAAAI,aAAA,CAAA8C,GAAA;MAAA,MAAA5C,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAAP,EAAA,CAAAiC,kBAAA,CAAA3B,MAAA,CAAA0C,KAAA,EAAAjB,MAAA,MAAAzB,MAAA,CAAA0C,KAAA,GAAAjB,MAAA;MAAA,OAAA/B,EAAA,CAAAU,WAAA,CAAAqB,MAAA;IAAA,EAAmB;IACvE/B,EADE,CAAAc,YAAA,EAA4F,EACxF;IAENd,EAAA,CAAAC,cAAA,kBAAgD;IAAAD,EAAA,CAAAa,MAAA,qBAAG;IAEvDb,EAFuD,CAAAc,YAAA,EAAS,EACvD,EACA;IAIHd,EAHN,CAAAC,cAAA,kBAAuB,cACJ,cACI,qBACF;IACbD,EAAA,CAAAY,SAAA,gBAA2B;IAC3BZ,EAAA,CAAAC,cAAA,iBAAgC;IAC9BD,EAAA,CAAAY,SAAA,kBACU;IAGhBZ,EAFI,CAAAc,YAAA,EAAS,EACK,EACZ;IACNd,EAAA,CAAAY,SAAA,eACM;IACRZ,EAAA,CAAAc,YAAA,EAAM;IAIAd,EAHN,CAAAC,cAAA,iBAA0G,aACjG,cAC+B,UAC9B;IAAAD,EAAA,CAAAa,MAAA,4BAAK;IAAAb,EAAA,CAAAc,YAAA,EAAK;IACdd,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAa,MAAA,iCAAK;IAAAb,EAAA,CAAAc,YAAA,EAAK;IACdd,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAa,MAAA,oBAAE;IAAAb,EAAA,CAAAc,YAAA,EAAK;IACXd,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAa,MAAA,qBAAG;IAAAb,EAAA,CAAAc,YAAA,EAAK;IACZd,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAa,MAAA,sBAAI;IAEZb,EAFY,CAAAc,YAAA,EAAK,EACV,EACC;IACRd,EAAA,CAAAC,cAAA,aAAO;IACLD,EAAA,CAAAiB,UAAA,KAAAsC,qEAAA,kBAAuC;IAiB/CvD,EAHM,CAAAc,YAAA,EAAQ,EACF,EACD,EACC;;;;IA3DmDd,EAAA,CAAAmB,SAAA,GAA0B;IAA1BnB,EAAA,CAAAqC,gBAAA,YAAA/B,MAAA,CAAAwC,YAAA,CAA0B;IAM/B9C,EAAA,CAAAmB,SAAA,GAAmB;IAAnBnB,EAAA,CAAAqC,gBAAA,YAAA/B,MAAA,CAAAyC,KAAA,CAAmB;IAKnB/C,EAAA,CAAAmB,SAAA,GAAmB;IAAnBnB,EAAA,CAAAqC,gBAAA,YAAA/B,MAAA,CAAA0C,KAAA,CAAmB;IA+B5ChD,EAAA,CAAAmB,SAAA,IAAY;IAAZnB,EAAA,CAAA0B,UAAA,YAAApB,MAAA,CAAAkD,SAAA,CAAY;;;;;IAzE/CxD,EADF,CAAAC,cAAA,kBAA+C,uBAClB;IA0BzBD,EAzBA,CAAAiB,UAAA,IAAAwC,kEAAA,4BAA0C,IAAAC,+DAAA,yBAyBF;IAiE1C1D,EAAA,CAAAc,YAAA,EAAe;IACfd,EAAA,CAAAY,SAAA,yBAGiB;IACnBZ,EAAA,CAAAc,YAAA,EAAU;;;;IA/FSd,EAAA,CAAAmB,SAAA,GAAyB;IAAzBnB,EAAA,CAAA0B,UAAA,SAAApB,MAAA,CAAAqD,IAAA,kBAAyB;IAyB5B3D,EAAA,CAAAmB,SAAA,EAA0B;IAA1BnB,EAAA,CAAA0B,UAAA,SAAApB,MAAA,CAAAqD,IAAA,mBAA0B;;;ADtE5C,OAAM,MAAOC,2BAA4B,SAAQ7D,aAAa;EAW5D8D,QAAQA,CAAA;IACN;EACF;EAEAnB,MAAMA,CAACoB,QAAa;IAClB;IACA;IACA,IAAI,CAACH,IAAI,GAAG,aAAa;EAC3B;EAEAf,QAAQA,CAACkB,QAAa;IACpB;IACA;EAAA;EAGFC,YACUC,MAAmB,EACnBC,aAA8B,EAC9BC,OAAuB;IAE/B,KAAK,CAACF,MAAM,CAAC;IAJL,KAAAA,MAAM,GAANA,MAAM;IACN,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,OAAO,GAAPA,OAAO;IA5BjB,KAAApB,YAAY,GAAG,EAAE;IACjB,KAAAC,KAAK,GAAG,IAAI;IACZ,KAAAC,KAAK,GAAG,IAAI;IACZ,KAAAW,IAAI,GAAG,QAAQ,EAAC;IAEhB,KAAAH,SAAS,GAAG,CACV;MAAEX,EAAE,EAAE,CAAC;MAAEC,YAAY,EAAE,IAAI;MAAEC,KAAK,EAAE,EAAE;MAAEC,KAAK,EAAE;IAAE,CAAE,EACnD;MAAEH,EAAE,EAAE,CAAC;MAAEC,YAAY,EAAE,IAAI;MAAEC,KAAK,EAAE,EAAE;MAAEC,KAAK,EAAE;IAAE,CAAE,CACpD;IAwBD,KAAAd,aAAa,GAAG,EAAE,CAAC,CAAC;IAiBpB,KAAAiC,WAAW,GAAU,CACnB;MACEC,GAAG,EAAE,CAAC;MACN7C,KAAK,EAAE,WAAW;MAClB8C,KAAK,EAAE,mCAAmC;MAC1C7C,YAAY,EAAE,GAAG;MACjBC,aAAa,EAAE,aAAa;MAC5B6C,OAAO,EAAE,CAAC;MACVC,SAAS,EAAE,0BAA0B;MACrCC,QAAQ,EAAE;KACX,EACD;MACEJ,GAAG,EAAE,CAAC;MACN7C,KAAK,EAAE,WAAW;MAClB8C,KAAK,EAAE,mCAAmC;MAC1C5C,aAAa,EAAE,UAAU;MACzB6C,OAAO,EAAE,CAAC;MACVC,SAAS,EAAE,0BAA0B;MACrCC,QAAQ,EAAE;KACX,EACD;MACEJ,GAAG,EAAE,CAAC;MACN7C,KAAK,EAAE,WAAW;MAClB8C,KAAK,EAAE,mCAAmC;MAC1C7C,YAAY,EAAE,GAAG;MACjBC,aAAa,EAAE,WAAW;MAC1B6C,OAAO,EAAE,CAAC;MACVC,SAAS,EAAE,0BAA0B;MACrCC,QAAQ,EAAE;KACX,CACF;IAyBD,KAAAC,MAAM,GAAgC,IAAI;IAc1C,KAAAC,aAAa,GAAU,EAAE;EAvFzB;EAEAtC,cAAcA,CAAA;IACZ;IACA,IAAI,IAAI,CAACF,aAAa,CAACyC,IAAI,EAAE,KAAK,EAAE,EAAE;MACpC;MACA;IACF;IAEA;IACAC,OAAO,CAACC,GAAG,CAAC,IAAI,CAAC3C,aAAa,CAAC;EACjC;EAES4C,QAAQA,CAAA,GACjB;EA+CAnE,MAAMA,CAACoE,GAAQ;IACb,IAAI,CAACC,KAAK,GAAG,IAAI;IACjB,IAAI,CAACN,aAAa,GAAG,EAAE;IACvB,IAAI,CAACO,OAAO,GAAG;MACb1D,KAAK,EAAE,EAAE;MACT8C,KAAK,EAAE,EAAE;MACTa,OAAO,EAAE,EAAE;MACXC,kBAAkB,EAAE;KACrB;IACD,IAAI,CAAClB,aAAa,CAACmB,IAAI,CAACL,GAAG,CAAC;EAC9B;EAIAM,cAAcA,CAACC,KAAY;IACzB,MAAMC,KAAK,GAAGD,KAAK,CAACE,MAA0B;IAC9C,IAAID,KAAK,CAACE,KAAK,IAAIF,KAAK,CAACE,KAAK,CAAC,CAAC,CAAC,EAAE;MACjC,MAAMC,IAAI,GAAGH,KAAK,CAACE,KAAK,CAAC,CAAC,CAAC;MAC3B,MAAME,MAAM,GAAG,IAAIC,UAAU,EAAE;MAC/BD,MAAM,CAACE,MAAM,GAAG,MAAK;QACnB,IAAI,CAACpB,MAAM,GAAGkB,MAAM,CAACG,MAAM;MAC7B,CAAC;MACDH,MAAM,CAACI,aAAa,CAACL,IAAI,CAAC;IAC5B;EACF;EAIAM,UAAUA,CAACC,CAAM;IACf,IAAIR,KAAK,GAAGQ,CAAC,CAACT,MAAM,CAACC,KAAK;IAC1B,MAAMS,SAAS,GAAG,mBAAmB;IAErC,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGV,KAAK,CAACW,MAAM,EAAED,CAAC,EAAE,EAAE;MACrC,IAAI,CAACD,SAAS,CAACG,IAAI,CAACZ,KAAK,CAACU,CAAC,CAAC,CAACG,IAAI,CAAC,EAAE;QAClC,IAAI,CAACpC,OAAO,CAACqC,YAAY,CAAC,kBAAkB,CAAC;QAC7C;MACF;MACA,IAAIb,IAAY;MAChB,IAAID,KAAK,CAACU,CAAC,CAAC,CAACK,IAAI,CAACC,QAAQ,CAAC,KAAK,CAAC,EAAE;QACjCf,IAAI,GAAGD,KAAK,CAACU,CAAC,CAAC;QACf,IAAIR,MAAM,GAAG,IAAIC,UAAU,EAAE;QAC7BD,MAAM,CAACE,MAAM,GAAGI,CAAC,IAAG;UAClB,IAAI,CAACvB,aAAa,CAACgC,IAAI,CAAC;YACtBtC,GAAG,EAAE+B,CAAC;YACN5E,KAAK,EAAEkE,KAAK,CAACU,CAAC,CAAC,CAACK,IAAI;YACpBnC,KAAK,EAAE4B,CAAC,CAACT,MAAM,EAAEM,MAAM,EAAEa,QAAQ,EAAE,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;YACjDC,IAAI,EAAE,IAAI;YACVC,SAAS,EAAEhH,YAAY,CAACiH;WACzB,CAAC;QACJ,CAAC;QACDpB,MAAM,CAACI,aAAa,CAACN,KAAK,CAACU,CAAC,CAAC,CAAC;MAChC,CAAC,MAAM;QACLT,IAAI,GAAGsB,GAAG,CAACC,eAAe,CAACxB,KAAK,CAACU,CAAC,CAAC,CAAC;QACpC,IAAIR,MAAM,GAAG,IAAIC,UAAU,EAAE;QAC7BD,MAAM,CAACE,MAAM,GAAGI,CAAC,IAAG;UAClB,IAAI,CAACvB,aAAa,CAACgC,IAAI,CAAC;YACtBtC,GAAG,EAAE+B,CAAC;YACN5E,KAAK,EAAEkE,KAAK,CAACU,CAAC,CAAC,CAACK,IAAI;YACpBnC,KAAK,EAAE4B,CAAC,CAACT,MAAM,EAAEM,MAAM,EAAEa,QAAQ,EAAE,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;YACjDC,IAAI,EAAEnB,IAAI;YACVoB,SAAS,EAAEhH,YAAY,CAACoH;WACzB,CAAC;QACJ,CAAC;QACDF,GAAG,CAACG,eAAe,CAAC1B,KAAK,CAACU,CAAC,CAAC,CAAC;QAC7BR,MAAM,CAACI,aAAa,CAACN,KAAK,CAACU,CAAC,CAAC,CAAC;MAChC;IACF;EACF;EAEAiB,UAAUA,CAACC,IAAS;IAClB,IAAIC,MAAM,CAACC,OAAO,CAAC,SAASF,IAAI,CAAC9F,KAAK,IAAI,CAAC,EAAE;MAC3C,IAAI,CAACmD,aAAa,GAAG,IAAI,CAACA,aAAa,CAAC8C,MAAM,CAACrB,CAAC,IAAIA,CAAC,CAAC/B,GAAG,KAAKiD,IAAI,CAACjD,GAAG,CAAC;IACzE;EACF;EAEAjB,QAAQA,CAAA,GACR;;;uCAzKWS,2BAA2B,EAAA5D,EAAA,CAAAyH,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAA3H,EAAA,CAAAyH,iBAAA,CAAAG,EAAA,CAAAC,eAAA,GAAA7H,EAAA,CAAAyH,iBAAA,CAAAK,EAAA,CAAAC,cAAA;IAAA;EAAA;;;YAA3BnE,2BAA2B;MAAAoE,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAAlI,EAAA,CAAAmI,0BAAA,EAAAnI,EAAA,CAAAoI,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,qCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UChBtC1I,EADF,CAAAC,cAAA,iBAA0B,qBACR;UACdD,EAAA,CAAAY,SAAA,qBAAiC;UACnCZ,EAAA,CAAAc,YAAA,EAAiB;UAEfd,EADF,CAAAC,cAAA,mBAAc,YACyB;UAAAD,EAAA,CAAAa,MAAA,sBAAK;UAAAb,EAAA,CAAAc,YAAA,EAAK;UAG3Cd,EAFJ,CAAAC,cAAA,aAAiB,aACI,eACyC;UAAAD,EAAA,CAAAa,MAAA,kCAC1D;UACFb,EADE,CAAAc,YAAA,EAAQ,EACJ;UAEJd,EADF,CAAAC,cAAA,cAAmB,qBACF;UAEbD,EAAA,CAAAY,SAAA,gBAA2B;UAC3BZ,EAAA,CAAAC,cAAA,iBAAgC;UAC9BD,EAAA,CAAAY,SAAA,kBACU;UAGhBZ,EAFI,CAAAc,YAAA,EAAS,EACK,EACZ;UACNd,EAAA,CAAAC,cAAA,eAAmB;UACjBD,EAAA,CAAAiB,UAAA,KAAA2H,2CAAA,kBAAkF;UAKtF5I,EADE,CAAAc,YAAA,EAAM,EACF;UAMEd,EAJR,CAAAC,cAAA,eAAmC,iBAC+D,aACvF,cACgD,cACrB;UAAAD,EAAA,CAAAa,MAAA,2BAC9B;UADAb,EAAA,CAAAc,YAAA,EAA8B;UAC9Bd,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAa,MAAA,iCAAK;UAAAb,EAAA,CAAAc,YAAA,EAAK;UACxCd,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAa,MAAA,oBAAE;UAAAb,EAAA,CAAAc,YAAA,EAAK;UACrCd,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAa,MAAA,qBAAG;UAAAb,EAAA,CAAAc,YAAA,EAAK;UACtCd,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAa,MAAA,sBAAI;UAEtCb,EAFsC,CAAAc,YAAA,EAAK,EACpC,EACC;UACRd,EAAA,CAAAC,cAAA,aAAO;UACLD,EAAA,CAAAiB,UAAA,KAAA4H,0CAAA,kBAAqD;UAa7D7I,EAHM,CAAAc,YAAA,EAAQ,EACF,EACJ,EACO;UACfd,EAAA,CAAAY,SAAA,0BACiB;UACnBZ,EAAA,CAAAc,YAAA,EAAU;UAEVd,EAAA,CAAAiB,UAAA,KAAA6H,mDAAA,gCAAA9I,EAAA,CAAA+I,sBAAA,CAAoD;;;UArCsB/I,EAAA,CAAAmB,SAAA,IAAc;UAAdnB,EAAA,CAAA0B,UAAA,SAAAiH,GAAA,CAAAhH,QAAA,CAAc;UAmBzD3B,EAAA,CAAAmB,SAAA,IAAiB;UAAjBnB,EAAA,CAAA0B,UAAA,YAAAiH,GAAA,CAAAxE,WAAA,CAAiB;;;qBD3BpCtE,YAAY,EAAAmJ,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EAAEtJ,YAAY,EAAAuJ,EAAA,CAAAC,aAAA,EAAAD,EAAA,CAAAE,oBAAA,EAAAF,EAAA,CAAAG,mBAAA,EAAAH,EAAA,CAAAI,eAAA,EAAAJ,EAAA,CAAAK,oBAAA,EAAAL,EAAA,CAAAM,iBAAA,EAAAN,EAAA,CAAAO,OAAA,EAAAP,EAAA,CAAAQ,MAAA,EAAA/B,EAAA,CAAAgC,eAAA,EAAAhC,EAAA,CAAAiC,mBAAA,EAAAjC,EAAA,CAAAkC,qBAAA,EAAAlC,EAAA,CAAAmC,qBAAA,EAAAnC,EAAA,CAAAoC,gBAAA,EAAApC,EAAA,CAAAqC,iBAAA,EAAArC,EAAA,CAAAsC,iBAAA,EAAAtC,EAAA,CAAAuC,cAAA,EAAAvC,EAAA,CAAAwC,oBAAA,EAAAxC,EAAA,CAAAyC,iBAAA,EAAAzC,EAAA,CAAA0C,iBAAA,EAAA1C,EAAA,CAAA2C,eAAA,EAAAC,EAAA,CAAAC,mBAAA;MAAAC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}