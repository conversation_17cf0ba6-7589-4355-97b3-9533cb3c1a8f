{"ast": null, "code": "var __assign = this && this.__assign || function () {\n  __assign = Object.assign || function (t) {\n    for (var s, i = 1, n = arguments.length; i < n; i++) {\n      s = arguments[i];\n      for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\n    }\n    return t;\n  };\n  return __assign.apply(this, arguments);\n};\nvar __spreadArray = this && this.__spreadArray || function (to, from, pack) {\n  if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\n    if (ar || !(i in from)) {\n      if (!ar) ar = Array.prototype.slice.call(from, 0, i);\n      ar[i] = from[i];\n    }\n  }\n  return to.concat(ar || Array.prototype.slice.call(from));\n};\nexport var DAYS_OF_WEEK = /*#__PURE__*/function (DAYS_OF_WEEK) {\n  DAYS_OF_WEEK[DAYS_OF_WEEK[\"SUNDAY\"] = 0] = \"SUNDAY\";\n  DAYS_OF_WEEK[DAYS_OF_WEEK[\"MONDAY\"] = 1] = \"MONDAY\";\n  DAYS_OF_WEEK[DAYS_OF_WEEK[\"TUESDAY\"] = 2] = \"TUESDAY\";\n  DAYS_OF_WEEK[DAYS_OF_WEEK[\"WEDNESDAY\"] = 3] = \"WEDNESDAY\";\n  DAYS_OF_WEEK[DAYS_OF_WEEK[\"THURSDAY\"] = 4] = \"THURSDAY\";\n  DAYS_OF_WEEK[DAYS_OF_WEEK[\"FRIDAY\"] = 5] = \"FRIDAY\";\n  DAYS_OF_WEEK[DAYS_OF_WEEK[\"SATURDAY\"] = 6] = \"SATURDAY\";\n  return DAYS_OF_WEEK;\n}(DAYS_OF_WEEK || {});\nvar DEFAULT_WEEKEND_DAYS = [DAYS_OF_WEEK.SUNDAY, DAYS_OF_WEEK.SATURDAY];\nvar DAYS_IN_WEEK = 7;\nvar HOURS_IN_DAY = 24;\nvar MINUTES_IN_HOUR = 60;\nexport var SECONDS_IN_DAY = 60 * 60 * 24;\nfunction getExcludedSeconds(dateAdapter, _a) {\n  var startDate = _a.startDate,\n    seconds = _a.seconds,\n    excluded = _a.excluded,\n    precision = _a.precision;\n  if (excluded.length < 1) {\n    return 0;\n  }\n  var addSeconds = dateAdapter.addSeconds,\n    getDay = dateAdapter.getDay,\n    addDays = dateAdapter.addDays;\n  var endDate = addSeconds(startDate, seconds - 1);\n  var dayStart = getDay(startDate);\n  var dayEnd = getDay(endDate);\n  var result = 0; // Calculated in seconds\n  var current = startDate;\n  var _loop_1 = function () {\n    var day = getDay(current);\n    if (excluded.some(function (excludedDay) {\n      return excludedDay === day;\n    })) {\n      result += calculateExcludedSeconds(dateAdapter, {\n        dayStart: dayStart,\n        dayEnd: dayEnd,\n        day: day,\n        precision: precision,\n        startDate: startDate,\n        endDate: endDate\n      });\n    }\n    current = addDays(current, 1);\n  };\n  while (current < endDate) {\n    _loop_1();\n  }\n  return result;\n}\nfunction calculateExcludedSeconds(dateAdapter, _a) {\n  var precision = _a.precision,\n    day = _a.day,\n    dayStart = _a.dayStart,\n    dayEnd = _a.dayEnd,\n    startDate = _a.startDate,\n    endDate = _a.endDate;\n  var differenceInSeconds = dateAdapter.differenceInSeconds,\n    endOfDay = dateAdapter.endOfDay,\n    startOfDay = dateAdapter.startOfDay;\n  if (precision === 'minutes') {\n    if (day === dayStart) {\n      return differenceInSeconds(endOfDay(startDate), startDate) + 1;\n    } else if (day === dayEnd) {\n      return differenceInSeconds(endDate, startOfDay(endDate)) + 1;\n    }\n  }\n  return SECONDS_IN_DAY;\n}\nfunction getWeekViewEventSpan(dateAdapter, _a) {\n  var event = _a.event,\n    offset = _a.offset,\n    startOfWeekDate = _a.startOfWeekDate,\n    excluded = _a.excluded,\n    precision = _a.precision,\n    totalDaysInView = _a.totalDaysInView;\n  var max = dateAdapter.max,\n    differenceInSeconds = dateAdapter.differenceInSeconds,\n    addDays = dateAdapter.addDays,\n    endOfDay = dateAdapter.endOfDay,\n    differenceInDays = dateAdapter.differenceInDays;\n  var span = SECONDS_IN_DAY;\n  var begin = max([event.start, startOfWeekDate]);\n  if (event.end) {\n    switch (precision) {\n      case 'minutes':\n        span = differenceInSeconds(event.end, begin);\n        break;\n      default:\n        span = differenceInDays(addDays(endOfDay(event.end), 1), begin) * SECONDS_IN_DAY;\n        break;\n    }\n  }\n  var offsetSeconds = offset * SECONDS_IN_DAY;\n  var totalLength = offsetSeconds + span;\n  // the best way to detect if an event is outside the week-view\n  // is to check if the total span beginning (from startOfWeekDay or event start) exceeds the total days in the view\n  var secondsInView = totalDaysInView * SECONDS_IN_DAY;\n  if (totalLength > secondsInView) {\n    span = secondsInView - offsetSeconds;\n  }\n  span -= getExcludedSeconds(dateAdapter, {\n    startDate: begin,\n    seconds: span,\n    excluded: excluded,\n    precision: precision\n  });\n  return span / SECONDS_IN_DAY;\n}\nfunction getWeekViewEventOffset(dateAdapter, _a) {\n  var event = _a.event,\n    startOfWeekDate = _a.startOfWeek,\n    excluded = _a.excluded,\n    precision = _a.precision;\n  var differenceInDays = dateAdapter.differenceInDays,\n    startOfDay = dateAdapter.startOfDay,\n    differenceInSeconds = dateAdapter.differenceInSeconds;\n  if (event.start < startOfWeekDate) {\n    return 0;\n  }\n  var offset = 0;\n  switch (precision) {\n    case 'days':\n      offset = differenceInDays(startOfDay(event.start), startOfWeekDate) * SECONDS_IN_DAY;\n      break;\n    case 'minutes':\n      offset = differenceInSeconds(event.start, startOfWeekDate);\n      break;\n  }\n  offset -= getExcludedSeconds(dateAdapter, {\n    startDate: startOfWeekDate,\n    seconds: offset,\n    excluded: excluded,\n    precision: precision\n  });\n  return Math.abs(offset / SECONDS_IN_DAY);\n}\nfunction isEventIsPeriod(dateAdapter, _a) {\n  var event = _a.event,\n    periodStart = _a.periodStart,\n    periodEnd = _a.periodEnd;\n  var isSameSecond = dateAdapter.isSameSecond;\n  var eventStart = event.start;\n  var eventEnd = event.end || event.start;\n  if (eventStart > periodStart && eventStart < periodEnd) {\n    return true;\n  }\n  if (eventEnd > periodStart && eventEnd < periodEnd) {\n    return true;\n  }\n  if (eventStart < periodStart && eventEnd > periodEnd) {\n    return true;\n  }\n  if (isSameSecond(eventStart, periodStart) || isSameSecond(eventStart, periodEnd)) {\n    return true;\n  }\n  if (isSameSecond(eventEnd, periodStart) || isSameSecond(eventEnd, periodEnd)) {\n    return true;\n  }\n  return false;\n}\nexport function getEventsInPeriod(dateAdapter, _a) {\n  var events = _a.events,\n    periodStart = _a.periodStart,\n    periodEnd = _a.periodEnd;\n  return events.filter(function (event) {\n    return isEventIsPeriod(dateAdapter, {\n      event: event,\n      periodStart: periodStart,\n      periodEnd: periodEnd\n    });\n  });\n}\nfunction getWeekDay(dateAdapter, _a) {\n  var date = _a.date,\n    _b = _a.weekendDays,\n    weekendDays = _b === void 0 ? DEFAULT_WEEKEND_DAYS : _b;\n  var startOfDay = dateAdapter.startOfDay,\n    isSameDay = dateAdapter.isSameDay,\n    getDay = dateAdapter.getDay;\n  var today = startOfDay(new Date());\n  var day = getDay(date);\n  return {\n    date: date,\n    day: day,\n    isPast: date < today,\n    isToday: isSameDay(date, today),\n    isFuture: date > today,\n    isWeekend: weekendDays.indexOf(day) > -1\n  };\n}\nexport function getWeekViewHeader(dateAdapter, _a) {\n  var viewDate = _a.viewDate,\n    weekStartsOn = _a.weekStartsOn,\n    _b = _a.excluded,\n    excluded = _b === void 0 ? [] : _b,\n    weekendDays = _a.weekendDays,\n    _c = _a.viewStart,\n    viewStart = _c === void 0 ? dateAdapter.startOfWeek(viewDate, {\n      weekStartsOn: weekStartsOn\n    }) : _c,\n    _d = _a.viewEnd,\n    viewEnd = _d === void 0 ? dateAdapter.addDays(viewStart, DAYS_IN_WEEK) : _d;\n  var addDays = dateAdapter.addDays,\n    getDay = dateAdapter.getDay;\n  var days = [];\n  var date = viewStart;\n  while (date < viewEnd) {\n    if (!excluded.some(function (e) {\n      return getDay(date) === e;\n    })) {\n      days.push(getWeekDay(dateAdapter, {\n        date: date,\n        weekendDays: weekendDays\n      }));\n    }\n    date = addDays(date, 1);\n  }\n  return days;\n}\nexport function getDifferenceInDaysWithExclusions(dateAdapter, _a) {\n  var date1 = _a.date1,\n    date2 = _a.date2,\n    excluded = _a.excluded;\n  var date = date1;\n  var diff = 0;\n  while (date < date2) {\n    if (excluded.indexOf(dateAdapter.getDay(date)) === -1) {\n      diff++;\n    }\n    date = dateAdapter.addDays(date, 1);\n  }\n  return diff;\n}\nexport function getAllDayWeekEvents(dateAdapter, _a) {\n  var _b = _a.events,\n    events = _b === void 0 ? [] : _b,\n    _c = _a.excluded,\n    excluded = _c === void 0 ? [] : _c,\n    _d = _a.precision,\n    precision = _d === void 0 ? 'days' : _d,\n    _e = _a.absolutePositionedEvents,\n    absolutePositionedEvents = _e === void 0 ? false : _e,\n    viewStart = _a.viewStart,\n    viewEnd = _a.viewEnd;\n  viewStart = dateAdapter.startOfDay(viewStart);\n  viewEnd = dateAdapter.endOfDay(viewEnd);\n  var differenceInSeconds = dateAdapter.differenceInSeconds,\n    differenceInDays = dateAdapter.differenceInDays;\n  var maxRange = getDifferenceInDaysWithExclusions(dateAdapter, {\n    date1: viewStart,\n    date2: viewEnd,\n    excluded: excluded\n  });\n  var totalDaysInView = differenceInDays(viewEnd, viewStart) + 1;\n  var eventsMapped = events.filter(function (event) {\n    return event.allDay;\n  }).map(function (event) {\n    var offset = getWeekViewEventOffset(dateAdapter, {\n      event: event,\n      startOfWeek: viewStart,\n      excluded: excluded,\n      precision: precision\n    });\n    var span = getWeekViewEventSpan(dateAdapter, {\n      event: event,\n      offset: offset,\n      startOfWeekDate: viewStart,\n      excluded: excluded,\n      precision: precision,\n      totalDaysInView: totalDaysInView\n    });\n    return {\n      event: event,\n      offset: offset,\n      span: span\n    };\n  }).filter(function (e) {\n    return e.offset < maxRange;\n  }).filter(function (e) {\n    return e.span > 0;\n  }).map(function (entry) {\n    return {\n      event: entry.event,\n      offset: entry.offset,\n      span: entry.span,\n      startsBeforeWeek: entry.event.start < viewStart,\n      endsAfterWeek: (entry.event.end || entry.event.start) > viewEnd\n    };\n  }).sort(function (itemA, itemB) {\n    var startSecondsDiff = differenceInSeconds(itemA.event.start, itemB.event.start);\n    if (startSecondsDiff === 0) {\n      return differenceInSeconds(itemB.event.end || itemB.event.start, itemA.event.end || itemA.event.start);\n    }\n    return startSecondsDiff;\n  });\n  var allDayEventRows = [];\n  var allocatedEvents = [];\n  eventsMapped.forEach(function (event, index) {\n    if (allocatedEvents.indexOf(event) === -1) {\n      allocatedEvents.push(event);\n      var rowSpan_1 = event.span + event.offset;\n      var otherRowEvents = eventsMapped.slice(index + 1).filter(function (nextEvent) {\n        if (nextEvent.offset >= rowSpan_1 && rowSpan_1 + nextEvent.span <= totalDaysInView && allocatedEvents.indexOf(nextEvent) === -1) {\n          var nextEventOffset = nextEvent.offset - rowSpan_1;\n          if (!absolutePositionedEvents) {\n            nextEvent.offset = nextEventOffset;\n          }\n          rowSpan_1 += nextEvent.span + nextEventOffset;\n          allocatedEvents.push(nextEvent);\n          return true;\n        }\n      });\n      var weekEvents = __spreadArray([event], otherRowEvents, true);\n      var id = weekEvents.filter(function (weekEvent) {\n        return weekEvent.event.id;\n      }).map(function (weekEvent) {\n        return weekEvent.event.id;\n      }).join('-');\n      allDayEventRows.push(__assign({\n        row: weekEvents\n      }, id ? {\n        id: id\n      } : {}));\n    }\n  });\n  return allDayEventRows;\n}\nfunction getWeekViewHourGrid(dateAdapter, _a) {\n  var events = _a.events,\n    viewDate = _a.viewDate,\n    hourSegments = _a.hourSegments,\n    hourDuration = _a.hourDuration,\n    dayStart = _a.dayStart,\n    dayEnd = _a.dayEnd,\n    weekStartsOn = _a.weekStartsOn,\n    excluded = _a.excluded,\n    weekendDays = _a.weekendDays,\n    segmentHeight = _a.segmentHeight,\n    viewStart = _a.viewStart,\n    viewEnd = _a.viewEnd,\n    minimumEventHeight = _a.minimumEventHeight;\n  var dayViewHourGrid = getDayViewHourGrid(dateAdapter, {\n    viewDate: viewDate,\n    hourSegments: hourSegments,\n    hourDuration: hourDuration,\n    dayStart: dayStart,\n    dayEnd: dayEnd\n  });\n  var weekDays = getWeekViewHeader(dateAdapter, {\n    viewDate: viewDate,\n    weekStartsOn: weekStartsOn,\n    excluded: excluded,\n    weekendDays: weekendDays,\n    viewStart: viewStart,\n    viewEnd: viewEnd\n  });\n  var setHours = dateAdapter.setHours,\n    setMinutes = dateAdapter.setMinutes,\n    getHours = dateAdapter.getHours,\n    getMinutes = dateAdapter.getMinutes;\n  return weekDays.map(function (day) {\n    var dayView = getDayView(dateAdapter, {\n      events: events,\n      viewDate: day.date,\n      hourSegments: hourSegments,\n      dayStart: dayStart,\n      dayEnd: dayEnd,\n      segmentHeight: segmentHeight,\n      eventWidth: 1,\n      hourDuration: hourDuration,\n      minimumEventHeight: minimumEventHeight\n    });\n    var hours = dayViewHourGrid.map(function (hour) {\n      var segments = hour.segments.map(function (segment) {\n        var date = setMinutes(setHours(day.date, getHours(segment.date)), getMinutes(segment.date));\n        return __assign(__assign({}, segment), {\n          date: date\n        });\n      });\n      return __assign(__assign({}, hour), {\n        segments: segments\n      });\n    });\n    function getColumnCount(allEvents, prevOverlappingEvents) {\n      var columnCount = Math.max.apply(Math, prevOverlappingEvents.map(function (iEvent) {\n        return iEvent.left + 1;\n      }));\n      var nextOverlappingEvents = allEvents.filter(function (iEvent) {\n        return iEvent.left >= columnCount;\n      }).filter(function (iEvent) {\n        return getOverLappingWeekViewEvents(prevOverlappingEvents, iEvent.top, iEvent.top + iEvent.height).length > 0;\n      });\n      if (nextOverlappingEvents.length > 0) {\n        return getColumnCount(allEvents, nextOverlappingEvents);\n      } else {\n        return columnCount;\n      }\n    }\n    var mappedEvents = dayView.events.map(function (event) {\n      var columnCount = getColumnCount(dayView.events, getOverLappingWeekViewEvents(dayView.events, event.top, event.top + event.height));\n      var width = 100 / columnCount;\n      return __assign(__assign({}, event), {\n        left: event.left * width,\n        width: width\n      });\n    });\n    return {\n      hours: hours,\n      date: day.date,\n      events: mappedEvents.map(function (event) {\n        var overLappingEvents = getOverLappingWeekViewEvents(mappedEvents.filter(function (otherEvent) {\n          return otherEvent.left > event.left;\n        }), event.top, event.top + event.height);\n        if (overLappingEvents.length > 0) {\n          return __assign(__assign({}, event), {\n            width: Math.min.apply(Math, overLappingEvents.map(function (otherEvent) {\n              return otherEvent.left;\n            })) - event.left\n          });\n        }\n        return event;\n      })\n    };\n  });\n}\nexport function getWeekView(dateAdapter, _a) {\n  var _b = _a.events,\n    events = _b === void 0 ? [] : _b,\n    viewDate = _a.viewDate,\n    weekStartsOn = _a.weekStartsOn,\n    _c = _a.excluded,\n    excluded = _c === void 0 ? [] : _c,\n    _d = _a.precision,\n    precision = _d === void 0 ? 'days' : _d,\n    _e = _a.absolutePositionedEvents,\n    absolutePositionedEvents = _e === void 0 ? false : _e,\n    hourSegments = _a.hourSegments,\n    hourDuration = _a.hourDuration,\n    dayStart = _a.dayStart,\n    dayEnd = _a.dayEnd,\n    weekendDays = _a.weekendDays,\n    segmentHeight = _a.segmentHeight,\n    minimumEventHeight = _a.minimumEventHeight,\n    _f = _a.viewStart,\n    viewStart = _f === void 0 ? dateAdapter.startOfWeek(viewDate, {\n      weekStartsOn: weekStartsOn\n    }) : _f,\n    _g = _a.viewEnd,\n    viewEnd = _g === void 0 ? dateAdapter.endOfWeek(viewDate, {\n      weekStartsOn: weekStartsOn\n    }) : _g;\n  if (!events) {\n    events = [];\n  }\n  var startOfDay = dateAdapter.startOfDay,\n    endOfDay = dateAdapter.endOfDay;\n  viewStart = startOfDay(viewStart);\n  viewEnd = endOfDay(viewEnd);\n  var eventsInPeriod = getEventsInPeriod(dateAdapter, {\n    events: events,\n    periodStart: viewStart,\n    periodEnd: viewEnd\n  });\n  var header = getWeekViewHeader(dateAdapter, {\n    viewDate: viewDate,\n    weekStartsOn: weekStartsOn,\n    excluded: excluded,\n    weekendDays: weekendDays,\n    viewStart: viewStart,\n    viewEnd: viewEnd\n  });\n  return {\n    allDayEventRows: getAllDayWeekEvents(dateAdapter, {\n      events: eventsInPeriod,\n      excluded: excluded,\n      precision: precision,\n      absolutePositionedEvents: absolutePositionedEvents,\n      viewStart: viewStart,\n      viewEnd: viewEnd\n    }),\n    period: {\n      events: eventsInPeriod,\n      start: header[0].date,\n      end: endOfDay(header[header.length - 1].date)\n    },\n    hourColumns: getWeekViewHourGrid(dateAdapter, {\n      events: events,\n      viewDate: viewDate,\n      hourSegments: hourSegments,\n      hourDuration: hourDuration,\n      dayStart: dayStart,\n      dayEnd: dayEnd,\n      weekStartsOn: weekStartsOn,\n      excluded: excluded,\n      weekendDays: weekendDays,\n      segmentHeight: segmentHeight,\n      viewStart: viewStart,\n      viewEnd: viewEnd,\n      minimumEventHeight: minimumEventHeight\n    })\n  };\n}\nexport function getMonthView(dateAdapter, _a) {\n  var _b = _a.events,\n    events = _b === void 0 ? [] : _b,\n    viewDate = _a.viewDate,\n    weekStartsOn = _a.weekStartsOn,\n    _c = _a.excluded,\n    excluded = _c === void 0 ? [] : _c,\n    _d = _a.viewStart,\n    viewStart = _d === void 0 ? dateAdapter.startOfMonth(viewDate) : _d,\n    _e = _a.viewEnd,\n    viewEnd = _e === void 0 ? dateAdapter.endOfMonth(viewDate) : _e,\n    weekendDays = _a.weekendDays;\n  if (!events) {\n    events = [];\n  }\n  var startOfWeek = dateAdapter.startOfWeek,\n    endOfWeek = dateAdapter.endOfWeek,\n    differenceInDays = dateAdapter.differenceInDays,\n    startOfDay = dateAdapter.startOfDay,\n    addHours = dateAdapter.addHours,\n    endOfDay = dateAdapter.endOfDay,\n    isSameMonth = dateAdapter.isSameMonth,\n    getDay = dateAdapter.getDay;\n  var start = startOfWeek(viewStart, {\n    weekStartsOn: weekStartsOn\n  });\n  var end = endOfWeek(viewEnd, {\n    weekStartsOn: weekStartsOn\n  });\n  var eventsInMonth = getEventsInPeriod(dateAdapter, {\n    events: events,\n    periodStart: start,\n    periodEnd: end\n  });\n  var initialViewDays = [];\n  var previousDate;\n  var _loop_2 = function (i) {\n    // hacky fix for https://github.com/mattlewis92/angular-calendar/issues/173\n    var date;\n    if (previousDate) {\n      date = startOfDay(addHours(previousDate, HOURS_IN_DAY));\n      if (previousDate.getTime() === date.getTime()) {\n        // DST change, so need to add 25 hours\n        /* istanbul ignore next */\n        date = startOfDay(addHours(previousDate, HOURS_IN_DAY + 1));\n      }\n      previousDate = date;\n    } else {\n      date = previousDate = start;\n    }\n    if (!excluded.some(function (e) {\n      return getDay(date) === e;\n    })) {\n      var day = getWeekDay(dateAdapter, {\n        date: date,\n        weekendDays: weekendDays\n      });\n      var eventsInPeriod = getEventsInPeriod(dateAdapter, {\n        events: eventsInMonth,\n        periodStart: startOfDay(date),\n        periodEnd: endOfDay(date)\n      });\n      day.inMonth = isSameMonth(date, viewDate);\n      day.events = eventsInPeriod;\n      day.badgeTotal = eventsInPeriod.length;\n      initialViewDays.push(day);\n    }\n  };\n  for (var i = 0; i < differenceInDays(end, start) + 1; i++) {\n    _loop_2(i);\n  }\n  var days = [];\n  var totalDaysVisibleInWeek = DAYS_IN_WEEK - excluded.length;\n  if (totalDaysVisibleInWeek < DAYS_IN_WEEK) {\n    for (var i = 0; i < initialViewDays.length; i += totalDaysVisibleInWeek) {\n      var row = initialViewDays.slice(i, i + totalDaysVisibleInWeek);\n      var isRowInMonth = row.some(function (day) {\n        return viewStart <= day.date && day.date < viewEnd;\n      });\n      if (isRowInMonth) {\n        days = __spreadArray(__spreadArray([], days, true), row, true);\n      }\n    }\n  } else {\n    days = initialViewDays;\n  }\n  var rows = Math.floor(days.length / totalDaysVisibleInWeek);\n  var rowOffsets = [];\n  for (var i = 0; i < rows; i++) {\n    rowOffsets.push(i * totalDaysVisibleInWeek);\n  }\n  return {\n    rowOffsets: rowOffsets,\n    totalDaysVisibleInWeek: totalDaysVisibleInWeek,\n    days: days,\n    period: {\n      start: days[0].date,\n      end: endOfDay(days[days.length - 1].date),\n      events: eventsInMonth\n    }\n  };\n}\nfunction getOverLappingWeekViewEvents(events, top, bottom) {\n  return events.filter(function (previousEvent) {\n    var previousEventTop = previousEvent.top;\n    var previousEventBottom = previousEvent.top + previousEvent.height;\n    if (top < previousEventBottom && previousEventBottom < bottom) {\n      return true;\n    } else if (top < previousEventTop && previousEventTop < bottom) {\n      return true;\n    } else if (previousEventTop <= top && bottom <= previousEventBottom) {\n      return true;\n    }\n    return false;\n  });\n}\nfunction getDayView(dateAdapter, _a) {\n  var events = _a.events,\n    viewDate = _a.viewDate,\n    hourSegments = _a.hourSegments,\n    dayStart = _a.dayStart,\n    dayEnd = _a.dayEnd,\n    eventWidth = _a.eventWidth,\n    segmentHeight = _a.segmentHeight,\n    hourDuration = _a.hourDuration,\n    minimumEventHeight = _a.minimumEventHeight;\n  var setMinutes = dateAdapter.setMinutes,\n    setHours = dateAdapter.setHours,\n    startOfDay = dateAdapter.startOfDay,\n    startOfMinute = dateAdapter.startOfMinute,\n    endOfDay = dateAdapter.endOfDay,\n    differenceInMinutes = dateAdapter.differenceInMinutes;\n  var startOfView = setMinutes(setHours(startOfDay(viewDate), sanitiseHours(dayStart.hour)), sanitiseMinutes(dayStart.minute));\n  var endOfView = setMinutes(setHours(startOfMinute(endOfDay(viewDate)), sanitiseHours(dayEnd.hour)), sanitiseMinutes(dayEnd.minute));\n  endOfView.setSeconds(59, 999);\n  var previousDayEvents = [];\n  var eventsInPeriod = getEventsInPeriod(dateAdapter, {\n    events: events.filter(function (event) {\n      return !event.allDay;\n    }),\n    periodStart: startOfView,\n    periodEnd: endOfView\n  });\n  var dayViewEvents = eventsInPeriod.sort(function (eventA, eventB) {\n    return eventA.start.valueOf() - eventB.start.valueOf();\n  }).map(function (event) {\n    var eventStart = event.start;\n    var eventEnd = event.end || eventStart;\n    var startsBeforeDay = eventStart < startOfView;\n    var endsAfterDay = eventEnd > endOfView;\n    var hourHeightModifier = hourSegments * segmentHeight / (hourDuration || MINUTES_IN_HOUR);\n    var top = 0;\n    if (eventStart > startOfView) {\n      // adjust the difference in minutes if the user's offset is different between the start of the day and the event (e.g. when going to or from DST)\n      var eventOffset = dateAdapter.getTimezoneOffset(eventStart);\n      var startOffset = dateAdapter.getTimezoneOffset(startOfView);\n      var diff = startOffset - eventOffset;\n      top += differenceInMinutes(eventStart, startOfView) + diff;\n    }\n    top *= hourHeightModifier;\n    top = Math.floor(top);\n    var startDate = startsBeforeDay ? startOfView : eventStart;\n    var endDate = endsAfterDay ? endOfView : eventEnd;\n    var timezoneOffset = dateAdapter.getTimezoneOffset(startDate) - dateAdapter.getTimezoneOffset(endDate);\n    var height = differenceInMinutes(endDate, startDate) + timezoneOffset;\n    if (!event.end) {\n      height = segmentHeight;\n    } else {\n      height *= hourHeightModifier;\n    }\n    if (minimumEventHeight && height < minimumEventHeight) {\n      height = minimumEventHeight;\n    }\n    height = Math.floor(height);\n    var bottom = top + height;\n    var overlappingPreviousEvents = getOverLappingWeekViewEvents(previousDayEvents, top, bottom);\n    var left = 0;\n    while (overlappingPreviousEvents.some(function (previousEvent) {\n      return previousEvent.left === left;\n    })) {\n      left += eventWidth;\n    }\n    var dayEvent = {\n      event: event,\n      height: height,\n      width: eventWidth,\n      top: top,\n      left: left,\n      startsBeforeDay: startsBeforeDay,\n      endsAfterDay: endsAfterDay\n    };\n    previousDayEvents.push(dayEvent);\n    return dayEvent;\n  });\n  var width = Math.max.apply(Math, dayViewEvents.map(function (event) {\n    return event.left + event.width;\n  }));\n  var allDayEvents = getEventsInPeriod(dateAdapter, {\n    events: events.filter(function (event) {\n      return event.allDay;\n    }),\n    periodStart: startOfDay(startOfView),\n    periodEnd: endOfDay(endOfView)\n  });\n  return {\n    events: dayViewEvents,\n    width: width,\n    allDayEvents: allDayEvents,\n    period: {\n      events: eventsInPeriod,\n      start: startOfView,\n      end: endOfView\n    }\n  };\n}\nfunction sanitiseHours(hours) {\n  return Math.max(Math.min(23, hours), 0);\n}\nfunction sanitiseMinutes(minutes) {\n  return Math.max(Math.min(59, minutes), 0);\n}\nfunction getDayViewHourGrid(dateAdapter, _a) {\n  var viewDate = _a.viewDate,\n    hourSegments = _a.hourSegments,\n    hourDuration = _a.hourDuration,\n    dayStart = _a.dayStart,\n    dayEnd = _a.dayEnd;\n  var setMinutes = dateAdapter.setMinutes,\n    setHours = dateAdapter.setHours,\n    startOfDay = dateAdapter.startOfDay,\n    startOfMinute = dateAdapter.startOfMinute,\n    endOfDay = dateAdapter.endOfDay,\n    addMinutes = dateAdapter.addMinutes,\n    addDays = dateAdapter.addDays;\n  var hours = [];\n  var startOfView = setMinutes(setHours(startOfDay(viewDate), sanitiseHours(dayStart.hour)), sanitiseMinutes(dayStart.minute));\n  var endOfView = setMinutes(setHours(startOfMinute(endOfDay(viewDate)), sanitiseHours(dayEnd.hour)), sanitiseMinutes(dayEnd.minute));\n  var segmentDuration = (hourDuration || MINUTES_IN_HOUR) / hourSegments;\n  var startOfViewDay = startOfDay(viewDate);\n  var endOfViewDay = endOfDay(viewDate);\n  var dateAdjustment = function (d) {\n    return d;\n  };\n  // this means that we change from or to DST on this day and that's going to cause problems so we bump the date\n  if (dateAdapter.getTimezoneOffset(startOfViewDay) !== dateAdapter.getTimezoneOffset(endOfViewDay)) {\n    startOfViewDay = addDays(startOfViewDay, 1);\n    startOfView = addDays(startOfView, 1);\n    endOfView = addDays(endOfView, 1);\n    dateAdjustment = function (d) {\n      return addDays(d, -1);\n    };\n  }\n  var dayDuration = hourDuration ? HOURS_IN_DAY * 60 / hourDuration : MINUTES_IN_HOUR;\n  for (var i = 0; i < dayDuration; i++) {\n    var segments = [];\n    for (var j = 0; j < hourSegments; j++) {\n      var date = addMinutes(addMinutes(startOfView, i * (hourDuration || MINUTES_IN_HOUR)), j * segmentDuration);\n      if (date >= startOfView && date < endOfView) {\n        segments.push({\n          date: dateAdjustment(date),\n          displayDate: date,\n          isStart: j === 0\n        });\n      }\n    }\n    if (segments.length > 0) {\n      hours.push({\n        segments: segments\n      });\n    }\n  }\n  return hours;\n}\nexport var EventValidationErrorMessage = /*#__PURE__*/function (EventValidationErrorMessage) {\n  EventValidationErrorMessage[\"NotArray\"] = \"Events must be an array\";\n  EventValidationErrorMessage[\"StartPropertyMissing\"] = \"Event is missing the `start` property\";\n  EventValidationErrorMessage[\"StartPropertyNotDate\"] = \"Event `start` property should be a javascript date object. Do `new Date(event.start)` to fix it.\";\n  EventValidationErrorMessage[\"EndPropertyNotDate\"] = \"Event `end` property should be a javascript date object. Do `new Date(event.end)` to fix it.\";\n  EventValidationErrorMessage[\"EndsBeforeStart\"] = \"Event `start` property occurs after the `end`\";\n  return EventValidationErrorMessage;\n}(EventValidationErrorMessage || {});\nexport function validateEvents(events, log) {\n  var isValid = true;\n  function isError(msg, event) {\n    log(msg, event);\n    isValid = false;\n  }\n  if (!Array.isArray(events)) {\n    log(EventValidationErrorMessage.NotArray, events);\n    return false;\n  }\n  events.forEach(function (event) {\n    if (!event.start) {\n      isError(EventValidationErrorMessage.StartPropertyMissing, event);\n    } else if (!(event.start instanceof Date)) {\n      isError(EventValidationErrorMessage.StartPropertyNotDate, event);\n    }\n    if (event.end) {\n      if (!(event.end instanceof Date)) {\n        isError(EventValidationErrorMessage.EndPropertyNotDate, event);\n      }\n      if (event.start > event.end) {\n        isError(EventValidationErrorMessage.EndsBeforeStart, event);\n      }\n    }\n  });\n  return isValid;\n}\n//# sourceMappingURL=calendar-utils.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}