{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { CommonModule } from '@angular/common';\nimport { Component, DestroyRef, ViewChild, inject } from '@angular/core';\nimport { FullCalendarComponent, FullCalendarModule } from '@fullcalendar/angular';\nimport dayGridPlugin from '@fullcalendar/daygrid';\nimport timeGridPlugin from '@fullcalendar/timegrid';\nimport listPlugin from '@fullcalendar/list';\nimport { takeUntilDestroyed } from '@angular/core/rxjs-interop';\nimport * as moment from 'moment';\nimport { concatMap, tap } from 'rxjs';\nimport { SharedModule } from '../../components/shared.module';\nimport { BaseComponent } from '../../components/base/baseComponent';\nlet CalendarComponent = class CalendarComponent extends BaseComponent {\n  constructor(allow, buildCaseService, preOrderSettingService) {\n    super(allow);\n    this.allow = allow;\n    this.buildCaseService = buildCaseService;\n    this.preOrderSettingService = preOrderSettingService;\n    this.buildCaseList = [];\n    this.events = [];\n    this.listPreOrder = [];\n    this.calendarOptions = {\n      initialView: 'timeGridWeek',\n      plugins: [dayGridPlugin, timeGridPlugin, listPlugin],\n      headerToolbar: {\n        left: 'prev,next today',\n        center: 'title',\n        right: 'dayGridMonth,timeGridWeek,timeGridDay,listWeek'\n      },\n      views: {\n        timeGrid: {\n          allDaySlot: false // Hide all-day line in time grid view,\n        }\n      },\n      height: 'auto',\n      slotMaxTime: '22:00:00',\n      slotMinTime: '09:00:00',\n      datesSet: agr => {\n        if (this.dateStart !== agr.start || this.dateEnd !== agr.end) {\n          this.dateStart = agr.start;\n          this.dateEnd = agr.end;\n          this.getListPreOrder(agr.start, agr.end).subscribe();\n        }\n      }\n    };\n    this.destroy = inject(DestroyRef);\n  }\n  ngOnInit() {\n    this.initialList();\n  }\n  initialList() {\n    this.buildCaseService.apiBuildCaseGetUserBuildCasePost$Json({}).pipe(takeUntilDestroyed(this.destroy)).pipe(tap(res => {\n      if (res.StatusCode == 0) {\n        this.buildCaseList = res.Entries ?? [];\n        this.currentBuildCase = this.buildCaseList[0]?.cID;\n      }\n    }), concatMap(() => this.getListPreOrder(this.dateStart, this.dateEnd))).subscribe();\n  }\n  getListPreOrder(dateStart, dateEnd) {\n    return this.preOrderSettingService.apiPreOrderSettingGetPreOrderSettingPost$Json({\n      body: {\n        CBuildCaseID: this.currentBuildCase,\n        CDateStart: dateStart.toISOString(),\n        CDateEnd: dateEnd.toISOString()\n      }\n    }).pipe(tap(res => {\n      if (res.StatusCode == 0) {\n        this.listPreOrder = res.Entries;\n        this.initEvent();\n      }\n    }));\n  }\n  initEvent() {\n    this.events = (this.listPreOrder ?? []).map(i => {\n      return {\n        title: i.CStatus ? `${i.CHouseHoldName ?? \"\"}${i.CFloor ? \"-\" + i.CFloor + 'F' : \"\"}\\n${i.CCustomerName ?? \"\"} ${i.CPeoples}P` : '',\n        start: moment(i.CDate).hour(i.CHour ?? 0).toISOString(),\n        end: moment(i.CDate).hour(i.CHour ?? 0).add(1, 'hours').toISOString(),\n        color: i.CStatus ? '#008080' : '#81d3f8',\n        backgroundColor: i.CStatus ? '#008080' : '#81d3f8',\n        display: i.CStatus ? undefined : 'background'\n      };\n    });\n    this.calendarOptions = {\n      ...this.calendarOptions,\n      // Spread the existing options to avoid mutation\n      events: this.events // Update events property with new data\n    };\n  }\n  changeBuildCase(buildCaseId) {\n    this.currentBuildCase = buildCaseId;\n    this.getListPreOrder(this.dateStart, this.dateEnd).subscribe();\n  }\n};\n__decorate([ViewChild(FullCalendarComponent)], CalendarComponent.prototype, \"calendarComponent\", void 0);\nCalendarComponent = __decorate([Component({\n  selector: 'app-calendar',\n  templateUrl: './calendar.component.html',\n  styleUrls: ['./calendar.component.scss'],\n  standalone: true,\n  imports: [CommonModule, SharedModule, FullCalendarModule]\n})], CalendarComponent);\nexport { CalendarComponent };", "map": {"version": 3, "names": ["CommonModule", "Component", "DestroyRef", "ViewChild", "inject", "FullCalendarComponent", "FullCalendarModule", "dayGridPlugin", "timeGridPlugin", "listPlugin", "takeUntilDestroyed", "moment", "concatMap", "tap", "SharedModule", "BaseComponent", "CalendarComponent", "constructor", "allow", "buildCaseService", "preOrderSettingService", "buildCaseList", "events", "listPreOrder", "calendarOptions", "initialView", "plugins", "headerToolbar", "left", "center", "right", "views", "timeGrid", "allDaySlot", "height", "slotMaxTime", "slotMinTime", "datesSet", "agr", "dateStart", "start", "dateEnd", "end", "getListPreOrder", "subscribe", "destroy", "ngOnInit", "initialList", "apiBuildCaseGetUserBuildCasePost$Json", "pipe", "res", "StatusCode", "Entries", "currentBuildCase", "cID", "apiPreOrderSettingGetPreOrderSettingPost$Json", "body", "CBuildCaseID", "CDateStart", "toISOString", "CDateEnd", "initEvent", "map", "i", "title", "CStatus", "CHouseHoldName", "CFloor", "CCustomerName", "CPeoples", "CDate", "hour", "CHour", "add", "color", "backgroundColor", "display", "undefined", "changeBuildCase", "buildCaseId", "__decorate", "selector", "templateUrl", "styleUrls", "standalone", "imports"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\reservation-time-management\\calendar\\calendar.component.ts"], "sourcesContent": ["import { CommonModule } from '@angular/common';\r\nimport { Component, DestroyRef, OnInit, ViewChild, inject, signal } from '@angular/core';\r\nimport { FullCalendarComponent, FullCalendarModule } from '@fullcalendar/angular';\r\nimport { CalendarOptions, EventInput } from '@fullcalendar/core';\r\nimport dayGridPlugin from '@fullcalendar/daygrid';\r\nimport timeGridPlugin from '@fullcalendar/timegrid';\r\nimport listPlugin from '@fullcalendar/list';\r\nimport { AllowHelper } from 'src/app/shared/helper/allowHelper';\r\nimport { BuildCaseService, PreOrderSettingService } from 'src/services/api/services';\r\nimport { takeUntilDestroyed } from '@angular/core/rxjs-interop';\r\nimport { BuildCaseGetListReponse, GetPreOrderSettingResponse } from 'src/services/api/models';\r\nimport * as moment from 'moment';\r\nimport { concatMap, tap } from 'rxjs';\r\nimport { SharedModule } from '../../components/shared.module';\r\nimport { BaseComponent } from '../../components/base/baseComponent';\r\n\r\n@Component({\r\n  selector: 'app-calendar',\r\n  templateUrl: './calendar.component.html',\r\n  styleUrls: ['./calendar.component.scss'],\r\n  standalone: true,\r\n  imports: [CommonModule, SharedModule, FullCalendarModule],\r\n})\r\nexport class CalendarComponent extends BaseComponent implements OnInit {\r\n  currentBuildCase?: number;\r\n  buildCaseList: BuildCaseGetListReponse[] = [];\r\n  events: EventInput[] = [];\r\n\r\n  dateStart: Date;\r\n  dateEnd: Date\r\n\r\n  listPreOrder: GetPreOrderSettingResponse[] = []\r\n\r\n  calendarOptions: CalendarOptions = {\r\n    initialView: 'timeGridWeek',\r\n    plugins: [dayGridPlugin, timeGridPlugin, listPlugin],\r\n    headerToolbar: {\r\n      left: 'prev,next today',\r\n      center: 'title',\r\n      right: 'dayGridMonth,timeGridWeek,timeGridDay,listWeek',\r\n    },\r\n    views: {\r\n      timeGrid: {\r\n        allDaySlot: false, // Hide all-day line in time grid view,\r\n      },\r\n    },\r\n    height: 'auto',\r\n    slotMaxTime: '22:00:00',\r\n    slotMinTime: '09:00:00',\r\n    datesSet: (agr) => {\r\n      if (this.dateStart !== agr.start || this.dateEnd !== agr.end) {\r\n        this.dateStart = agr.start\r\n        this.dateEnd = agr.end\r\n        this.getListPreOrder(agr.start, agr.end).subscribe()\r\n      }\r\n    }\r\n  };\r\n  destroy = inject(DestroyRef)\r\n  @ViewChild(FullCalendarComponent) calendarComponent: FullCalendarComponent;\r\n  constructor(\r\n    protected override allow: AllowHelper,\r\n    private buildCaseService: BuildCaseService,\r\n    private preOrderSettingService: PreOrderSettingService\r\n  ) {\r\n    super(allow)\r\n  }\r\n  override ngOnInit(): void {\r\n    this.initialList();\r\n  }\r\n\r\n  initialList() {\r\n    this.buildCaseService.apiBuildCaseGetUserBuildCasePost$Json({}).pipe(\r\n      takeUntilDestroyed(this.destroy)\r\n    ).pipe(\r\n      tap(res => {\r\n        if (res.StatusCode == 0) {\r\n          this.buildCaseList = res.Entries ?? [];\r\n          this.currentBuildCase = this.buildCaseList[0]?.cID;\r\n        }\r\n      }),\r\n      concatMap(() => this.getListPreOrder(this.dateStart, this.dateEnd))\r\n    ).subscribe();\r\n  }\r\n\r\n  getListPreOrder(dateStart: Date, dateEnd: Date) {\r\n    return this.preOrderSettingService.apiPreOrderSettingGetPreOrderSettingPost$Json({\r\n      body: {\r\n        CBuildCaseID: this.currentBuildCase,\r\n        CDateStart: dateStart.toISOString(),\r\n        CDateEnd: dateEnd.toISOString()\r\n      }\r\n    }).pipe(\r\n      tap(res => {\r\n        if (res.StatusCode == 0) {\r\n          this.listPreOrder = res.Entries!\r\n          this.initEvent();\r\n        }\r\n      })\r\n    )\r\n  }\r\n\r\n  initEvent() {\r\n    this.events = (this.listPreOrder ?? []).map(i => {\r\n      return {\r\n        title: i.CStatus ? `${i.CHouseHoldName ?? \"\"}${i.CFloor ? \"-\" + i.CFloor + 'F' : \"\"}\\n${i.CCustomerName ?? \"\"} ${i.CPeoples}P` : '',\r\n        start: moment(i.CDate).hour(i.CHour ?? 0).toISOString(),\r\n        end: moment(i.CDate).hour(i.CHour ?? 0).add(1, 'hours').toISOString(),\r\n        color: i.CStatus ? '#008080' : '#81d3f8',\r\n        backgroundColor: i.CStatus ? '#008080' : '#81d3f8',\r\n        display: i.CStatus ? undefined : 'background'\r\n      }\r\n    })\r\n    this.calendarOptions = {\r\n      ...this.calendarOptions,  // Spread the existing options to avoid mutation\r\n      events: this.events    // Update events property with new data\r\n    };\r\n  }\r\n\r\n  changeBuildCase(buildCaseId: number) {\r\n    this.currentBuildCase = buildCaseId;\r\n    this.getListPreOrder(this.dateStart, this.dateEnd).subscribe()\r\n  }\r\n}\r\n"], "mappings": ";AAAA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,SAAS,EAAEC,UAAU,EAAUC,SAAS,EAAEC,MAAM,QAAgB,eAAe;AACxF,SAASC,qBAAqB,EAAEC,kBAAkB,QAAQ,uBAAuB;AAEjF,OAAOC,aAAa,MAAM,uBAAuB;AACjD,OAAOC,cAAc,MAAM,wBAAwB;AACnD,OAAOC,UAAU,MAAM,oBAAoB;AAG3C,SAASC,kBAAkB,QAAQ,4BAA4B;AAE/D,OAAO,KAAKC,MAAM,MAAM,QAAQ;AAChC,SAASC,SAAS,EAAEC,GAAG,QAAQ,MAAM;AACrC,SAASC,YAAY,QAAQ,gCAAgC;AAC7D,SAASC,aAAa,QAAQ,qCAAqC;AAS5D,IAAMC,iBAAiB,GAAvB,MAAMA,iBAAkB,SAAQD,aAAa;EAoClDE,YACqBC,KAAkB,EAC7BC,gBAAkC,EAClCC,sBAA8C;IAEtD,KAAK,CAACF,KAAK,CAAC;IAJO,KAAAA,KAAK,GAALA,KAAK;IAChB,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,sBAAsB,GAAtBA,sBAAsB;IArChC,KAAAC,aAAa,GAA8B,EAAE;IAC7C,KAAAC,MAAM,GAAiB,EAAE;IAKzB,KAAAC,YAAY,GAAiC,EAAE;IAE/C,KAAAC,eAAe,GAAoB;MACjCC,WAAW,EAAE,cAAc;MAC3BC,OAAO,EAAE,CAACnB,aAAa,EAAEC,cAAc,EAAEC,UAAU,CAAC;MACpDkB,aAAa,EAAE;QACbC,IAAI,EAAE,iBAAiB;QACvBC,MAAM,EAAE,OAAO;QACfC,KAAK,EAAE;OACR;MACDC,KAAK,EAAE;QACLC,QAAQ,EAAE;UACRC,UAAU,EAAE,KAAK,CAAE;;OAEtB;MACDC,MAAM,EAAE,MAAM;MACdC,WAAW,EAAE,UAAU;MACvBC,WAAW,EAAE,UAAU;MACvBC,QAAQ,EAAGC,GAAG,IAAI;QAChB,IAAI,IAAI,CAACC,SAAS,KAAKD,GAAG,CAACE,KAAK,IAAI,IAAI,CAACC,OAAO,KAAKH,GAAG,CAACI,GAAG,EAAE;UAC5D,IAAI,CAACH,SAAS,GAAGD,GAAG,CAACE,KAAK;UAC1B,IAAI,CAACC,OAAO,GAAGH,GAAG,CAACI,GAAG;UACtB,IAAI,CAACC,eAAe,CAACL,GAAG,CAACE,KAAK,EAAEF,GAAG,CAACI,GAAG,CAAC,CAACE,SAAS,EAAE;QACtD;MACF;KACD;IACD,KAAAC,OAAO,GAAGzC,MAAM,CAACF,UAAU,CAAC;EAQ5B;EACS4C,QAAQA,CAAA;IACf,IAAI,CAACC,WAAW,EAAE;EACpB;EAEAA,WAAWA,CAAA;IACT,IAAI,CAAC5B,gBAAgB,CAAC6B,qCAAqC,CAAC,EAAE,CAAC,CAACC,IAAI,CAClEvC,kBAAkB,CAAC,IAAI,CAACmC,OAAO,CAAC,CACjC,CAACI,IAAI,CACJpC,GAAG,CAACqC,GAAG,IAAG;MACR,IAAIA,GAAG,CAACC,UAAU,IAAI,CAAC,EAAE;QACvB,IAAI,CAAC9B,aAAa,GAAG6B,GAAG,CAACE,OAAO,IAAI,EAAE;QACtC,IAAI,CAACC,gBAAgB,GAAG,IAAI,CAAChC,aAAa,CAAC,CAAC,CAAC,EAAEiC,GAAG;MACpD;IACF,CAAC,CAAC,EACF1C,SAAS,CAAC,MAAM,IAAI,CAAC+B,eAAe,CAAC,IAAI,CAACJ,SAAS,EAAE,IAAI,CAACE,OAAO,CAAC,CAAC,CACpE,CAACG,SAAS,EAAE;EACf;EAEAD,eAAeA,CAACJ,SAAe,EAAEE,OAAa;IAC5C,OAAO,IAAI,CAACrB,sBAAsB,CAACmC,6CAA6C,CAAC;MAC/EC,IAAI,EAAE;QACJC,YAAY,EAAE,IAAI,CAACJ,gBAAgB;QACnCK,UAAU,EAAEnB,SAAS,CAACoB,WAAW,EAAE;QACnCC,QAAQ,EAAEnB,OAAO,CAACkB,WAAW;;KAEhC,CAAC,CAACV,IAAI,CACLpC,GAAG,CAACqC,GAAG,IAAG;MACR,IAAIA,GAAG,CAACC,UAAU,IAAI,CAAC,EAAE;QACvB,IAAI,CAAC5B,YAAY,GAAG2B,GAAG,CAACE,OAAQ;QAChC,IAAI,CAACS,SAAS,EAAE;MAClB;IACF,CAAC,CAAC,CACH;EACH;EAEAA,SAASA,CAAA;IACP,IAAI,CAACvC,MAAM,GAAG,CAAC,IAAI,CAACC,YAAY,IAAI,EAAE,EAAEuC,GAAG,CAACC,CAAC,IAAG;MAC9C,OAAO;QACLC,KAAK,EAAED,CAAC,CAACE,OAAO,GAAG,GAAGF,CAAC,CAACG,cAAc,IAAI,EAAE,GAAGH,CAAC,CAACI,MAAM,GAAG,GAAG,GAAGJ,CAAC,CAACI,MAAM,GAAG,GAAG,GAAG,EAAE,KAAKJ,CAAC,CAACK,aAAa,IAAI,EAAE,IAAIL,CAAC,CAACM,QAAQ,GAAG,GAAG,EAAE;QACnI7B,KAAK,EAAE7B,MAAM,CAACoD,CAAC,CAACO,KAAK,CAAC,CAACC,IAAI,CAACR,CAAC,CAACS,KAAK,IAAI,CAAC,CAAC,CAACb,WAAW,EAAE;QACvDjB,GAAG,EAAE/B,MAAM,CAACoD,CAAC,CAACO,KAAK,CAAC,CAACC,IAAI,CAACR,CAAC,CAACS,KAAK,IAAI,CAAC,CAAC,CAACC,GAAG,CAAC,CAAC,EAAE,OAAO,CAAC,CAACd,WAAW,EAAE;QACrEe,KAAK,EAAEX,CAAC,CAACE,OAAO,GAAG,SAAS,GAAG,SAAS;QACxCU,eAAe,EAAEZ,CAAC,CAACE,OAAO,GAAG,SAAS,GAAG,SAAS;QAClDW,OAAO,EAAEb,CAAC,CAACE,OAAO,GAAGY,SAAS,GAAG;OAClC;IACH,CAAC,CAAC;IACF,IAAI,CAACrD,eAAe,GAAG;MACrB,GAAG,IAAI,CAACA,eAAe;MAAG;MAC1BF,MAAM,EAAE,IAAI,CAACA,MAAM,CAAI;KACxB;EACH;EAEAwD,eAAeA,CAACC,WAAmB;IACjC,IAAI,CAAC1B,gBAAgB,GAAG0B,WAAW;IACnC,IAAI,CAACpC,eAAe,CAAC,IAAI,CAACJ,SAAS,EAAE,IAAI,CAACE,OAAO,CAAC,CAACG,SAAS,EAAE;EAChE;CACD;AAhEmCoC,UAAA,EAAjC7E,SAAS,CAACE,qBAAqB,CAAC,C,2DAA0C;AAnChEW,iBAAiB,GAAAgE,UAAA,EAP7B/E,SAAS,CAAC;EACTgF,QAAQ,EAAE,cAAc;EACxBC,WAAW,EAAE,2BAA2B;EACxCC,SAAS,EAAE,CAAC,2BAA2B,CAAC;EACxCC,UAAU,EAAE,IAAI;EAChBC,OAAO,EAAE,CAACrF,YAAY,EAAEc,YAAY,EAAER,kBAAkB;CACzD,CAAC,C,EACWU,iBAAiB,CAmG7B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}