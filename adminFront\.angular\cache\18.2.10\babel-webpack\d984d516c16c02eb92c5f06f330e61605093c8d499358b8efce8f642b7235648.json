{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { Component, HostListener } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { concatMap, of, tap } from 'rxjs';\nimport { BaseFilePipe } from 'src/app/@theme/pipes/base-file.pipe';\nimport { Base64ImagePipe } from 'src/app/@theme/pipes/base64-image.pipe';\nimport { SharedModule } from '../../components/shared.module';\nimport { BaseComponent } from '../../components/base/baseComponent';\nimport * as JSZip from 'jszip';\n// 圖片類別枚舉\nvar PictureCategory;\n(function (PictureCategory) {\n  PictureCategory[PictureCategory[\"NONE\"] = 0] = \"NONE\";\n  PictureCategory[PictureCategory[\"BUILDING_MATERIAL\"] = 1] = \"BUILDING_MATERIAL\";\n  PictureCategory[PictureCategory[\"SCHEMATIC\"] = 2] = \"SCHEMATIC\"; // 示意圖片\n})(PictureCategory || (PictureCategory = {}));\nlet PictureMaterialComponent = class PictureMaterialComponent extends BaseComponent {\n  get selectedCount() {\n    return this.selectedItems.size;\n  }\n  // 獲取類別標籤的方法\n  getCategoryLabel(category) {\n    const option = this.categoryOptions.find(opt => opt.value === category);\n    return option ? option.label : '未知類別';\n  }\n  constructor(_allow, dialogService, valid, _pictureService, _buildCaseService, message, _utilityService) {\n    super(_allow);\n    this._allow = _allow;\n    this.dialogService = dialogService;\n    this.valid = valid;\n    this._pictureService = _pictureService;\n    this._buildCaseService = _buildCaseService;\n    this.message = message;\n    this._utilityService = _utilityService;\n    this.images = [];\n    this.listUserBuildCases = [];\n    this.selectedCategory = PictureCategory.NONE;\n    this.isCategorySelected = false; // 追蹤用戶是否已經明確選擇類別\n    // 批次選擇相關屬性\n    this.selectedItems = new Set(); // 選中的項目 ID\n    this.selectAll = false; // 全選狀態\n    this.currentImageShowing = \"\";\n    // 輪播預覽相關屬性\n    this.currentPreviewImages = [];\n    this.currentImageIndex = 0;\n    this.isPreviewMode = false;\n    this.listPictures = [];\n    this.isEdit = false;\n    // 類別選項\n    this.categoryOptions = [{\n      value: PictureCategory.BUILDING_MATERIAL,\n      label: '建材圖片'\n    }, {\n      value: PictureCategory.SCHEMATIC,\n      label: '示意圖片'\n    }];\n    // 讓模板可以使用 enum\n    this.PictureCategory = PictureCategory;\n  }\n  ngOnInit() {\n    this.getListBuildCase();\n  }\n  getListBuildCase() {\n    this._buildCaseService.apiBuildCaseGetUserBuildCasePost$Json({}).pipe(tap(res => {\n      if (res.StatusCode === 0) {\n        this.listUserBuildCases = res.Entries ?? [];\n        this.selectedBuildCaseId = this.listUserBuildCases[0].cID;\n      }\n    }), concatMap(() => this.getPicturelList(1))).subscribe();\n  }\n  openPdfInNewTab(CFileUrl) {\n    if (CFileUrl) {\n      this._utilityService.openFileInNewTab(CFileUrl);\n    }\n  }\n  getPicturelList(pageIndex) {\n    // 重置選擇狀態\n    this.selectedItems.clear();\n    this.selectAll = false;\n    if (this.selectedCategory === PictureCategory.BUILDING_MATERIAL) {\n      return this._pictureService.apiPictureGetPicturelListPost$Json({\n        body: {\n          PageIndex: pageIndex,\n          PageSize: this.pageSize,\n          CBuildCaseId: this.selectedBuildCaseId,\n          cPictureType: this.selectedCategory\n        }\n      }).pipe(tap(res => {\n        if (res.StatusCode == 0) {\n          this.images = res.Entries ?? [];\n          this.totalRecords = res.TotalItems;\n          this.updateSelectAllState();\n        }\n      }));\n    } else if (this.selectedCategory === PictureCategory.SCHEMATIC) {\n      return this._pictureService.apiPictureGetPicturelListPost$Json({\n        body: {\n          PageIndex: pageIndex,\n          PageSize: this.pageSize,\n          CBuildCaseId: this.selectedBuildCaseId,\n          cPictureType: this.selectedCategory\n        }\n      }).pipe(tap(res => {\n        if (res.StatusCode == 0) {\n          this.images = res.Entries ?? [];\n          this.totalRecords = res.TotalItems;\n          this.updateSelectAllState();\n        }\n      }));\n    } else {\n      // 如果沒有選擇類別，清空數據並返回預設的空 observable\n      this.images = [];\n      this.totalRecords = 0;\n      return this._pictureService.apiPictureGetPicturelListPost$Json({\n        body: {\n          PageIndex: 1,\n          PageSize: 1,\n          CBuildCaseId: -1,\n          // 使用無效 ID 確保返回空結果\n          cPictureType: PictureCategory.NONE\n        }\n      }).pipe(tap(() => {\n        this.images = [];\n        this.totalRecords = 0;\n      }));\n    }\n  }\n  pageChanged(pageIndex) {\n    // this.pageIndex = newPage;\n    this.getPicturelList(pageIndex).subscribe();\n  }\n  selectedChange(buildCaseId) {\n    this.selectedBuildCaseId = buildCaseId;\n    this.getPicturelList(1).subscribe();\n  }\n  categoryChanged(category) {\n    this.selectedCategory = category;\n    this.isCategorySelected = true; // 標記用戶已經選擇了類別\n    this.getPicturelList(1).subscribe();\n  }\n  addNew(ref, item) {\n    // 如果是新增圖片（沒有傳入 item），則檢查是否已選擇類別\n    if (!item && !this.isCategorySelected) {\n      this.message.showErrorMSG('請先選擇圖片類別');\n      return;\n    }\n    this.listPictures = [];\n    this.dialogService.open(ref);\n    this.isEdit = false;\n    if (!!item) {\n      // 預覽模式 - 顯示當前類別的所有圖片並支持輪播\n      this.isPreviewMode = true;\n      this.currentPreviewImages = [...this.images];\n      this.currentImageIndex = this.images.findIndex(img => img.CId === item.CId);\n      if (this.currentImageIndex === -1) {\n        this.currentImageIndex = 0;\n      }\n      this.currentImageShowing = this.getCurrentPreviewImage();\n    } else {\n      // 上傳模式\n      this.isPreviewMode = false;\n      this.currentPreviewImages = [];\n      this.currentImageIndex = 0;\n      this.currentImageShowing = \"\";\n      this.listPictures = [];\n    }\n  }\n  changePicture(ref, item) {\n    // 檢查是否已選擇類別\n    if (!this.selectedCategory) {\n      this.message.showErrorMSG('請先選擇圖片類別');\n      return;\n    }\n    if (!!item && item.CId) {\n      this.dialogService.open(ref);\n      this.isEdit = true;\n      this.currentEditItem = item.CId;\n      this.listPictures = [];\n    }\n  }\n  validation(CFile) {\n    this.valid.clear();\n    const nameSet = new Set();\n    for (const item of CFile) {\n      if (nameSet.has(item.name)) {\n        this.valid.addErrorMessage('檔名不可重複');\n        return;\n      }\n      nameSet.add(item.name);\n    }\n  }\n  onSubmit(ref) {}\n  detectFiles(event) {\n    for (let index = 0; index < event.target.files.length; index++) {\n      const file = event.target.files[index];\n      if (file) {\n        // 檢查是否為 ZIP 檔案\n        if (file.type === 'application/zip' || file.name.toLowerCase().endsWith('.zip')) {\n          this.processZipFile(file);\n        } else {\n          this.processSingleImageFile(file);\n        }\n      }\n    }\n    // Reset input file to be able to select the old file again\n    event.target.value = null;\n  }\n  processZipFile(zipFile) {\n    const zip = new JSZip();\n    zip.loadAsync(zipFile).then(contents => {\n      const imageFiles = [];\n      contents.forEach((relativePath, file) => {\n        // 只處理圖片檔案，跳過資料夾\n        if (!file.dir && this.isImageFile(relativePath)) {\n          imageFiles.push(file.async('blob').then(blob => {\n            // 只取檔案名稱，移除資料夾路徑\n            const fileName = relativePath.split('/').pop() || relativePath.split('\\\\').pop() || relativePath;\n            // 建立 File 物件\n            const imageFile = new File([blob], fileName, {\n              type: this.getImageMimeType(relativePath)\n            });\n            return this.processImageFileFromZip(imageFile, fileName);\n          }));\n        }\n      });\n      // 處理所有圖片檔案\n      Promise.all(imageFiles).then(() => {\n        this.message.showSucessMSG(`成功從 ZIP 檔案中匯入 ${imageFiles.length} 張圖片`);\n      }).catch(error => {\n        console.error('處理 ZIP 檔案中的圖片時發生錯誤:', error);\n        this.message.showErrorMSG('處理 ZIP 檔案中的圖片時發生錯誤');\n      });\n    }).catch(error => {\n      console.error('讀取 ZIP 檔案時發生錯誤:', error);\n      this.message.showErrorMSG('無法讀取 ZIP 檔案，請確認檔案格式正確');\n    });\n  }\n  processSingleImageFile(file) {\n    let reader = new FileReader();\n    reader.readAsDataURL(file);\n    reader.onload = () => {\n      let base64Str = reader.result;\n      if (!base64Str) {\n        return;\n      }\n      this.addImageToList(file, base64Str);\n    };\n  }\n  processImageFileFromZip(file, originalPath) {\n    return new Promise((resolve, reject) => {\n      let reader = new FileReader();\n      reader.readAsDataURL(file);\n      reader.onload = () => {\n        let base64Str = reader.result;\n        if (!base64Str) {\n          reject('無法讀取圖片檔案');\n          return;\n        }\n        this.addImageToList(file, base64Str, originalPath);\n        resolve();\n      };\n      reader.onerror = () => {\n        reject('讀取圖片檔案時發生錯誤');\n      };\n    });\n  }\n  addImageToList(file, base64Str, originalPath) {\n    // Get name file ( no extension)\n    let fileName = originalPath || file.name;\n    // 如果是從 ZIP 檔案來的，只取檔案名稱，移除資料夾路徑\n    if (originalPath) {\n      fileName = originalPath.split('/').pop() || originalPath.split('\\\\').pop() || originalPath;\n    }\n    const fileNameWithoutExtension = fileName.split('.')[0];\n    // Find files with duplicate names\n    const existingFileIndex = this.listPictures.findIndex(picture => picture.name === fileNameWithoutExtension);\n    if (existingFileIndex !== -1) {\n      // If name is duplicate, update file data\n      this.listPictures[existingFileIndex] = {\n        ...this.listPictures[existingFileIndex],\n        data: base64Str,\n        CFile: file,\n        extension: this._utilityService.getFileExtension(fileName)\n      };\n    } else {\n      // If not duplicate, add new file\n      this.listPictures.push({\n        id: new Date().getTime() + Math.random(),\n        name: fileNameWithoutExtension,\n        data: base64Str,\n        extension: this._utilityService.getFileExtension(fileName),\n        CFile: file\n      });\n    }\n  }\n  isImageFile(fileName) {\n    const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp'];\n    const extension = fileName.toLowerCase().substring(fileName.lastIndexOf('.'));\n    return imageExtensions.includes(extension);\n  }\n  getImageMimeType(fileName) {\n    const extension = fileName.toLowerCase().substring(fileName.lastIndexOf('.'));\n    switch (extension) {\n      case '.jpg':\n      case '.jpeg':\n        return 'image/jpeg';\n      case '.png':\n        return 'image/png';\n      case '.gif':\n        return 'image/gif';\n      case '.bmp':\n        return 'image/bmp';\n      case '.webp':\n        return 'image/webp';\n      default:\n        return 'image/jpeg';\n    }\n  }\n  removeImage(pictureId) {\n    this.listPictures = this.listPictures.filter(x => x.id != pictureId);\n  }\n  uploadImage(ref) {\n    if (!this.isEdit) {\n      const CFile = this.listPictures.map(x => x.CFile);\n      this.validation(CFile);\n      if (this.valid.errorMessages.length > 0) {\n        this.message.showErrorMSGs(this.valid.errorMessages);\n        return;\n      } // 統一使用 PictureService 進行上傳\n      const uploadRequest = this._pictureService.apiPictureUploadListPicturePost$Json({\n        body: {\n          CBuildCaseId: this.selectedBuildCaseId,\n          CPath: this.selectedCategory === PictureCategory.BUILDING_MATERIAL ? \"picture\" : \"infoPicture\",\n          CFile: CFile,\n          cPictureType: this.selectedCategory\n        }\n      });\n      uploadRequest.pipe(tap(res => {\n        if (res.StatusCode == 0) {\n          this.message.showSucessMSG('執行成功');\n          this.listPictures = [];\n        } else {\n          this.message.showErrorMSG(res.Message);\n        }\n        ref.close();\n        this.resetPreviewState();\n      }), concatMap(res => res.StatusCode == 0 ? this.getPicturelList(1) : of(null))).subscribe();\n    } else {\n      if (this.listPictures.length > 0 && this.listPictures[0].CFile) {\n        // 統一使用 PictureService 進行更新\n        const updateRequest = this._pictureService.apiPictureUpdatePicturePost$Json({\n          body: {\n            CBuildCaseID: this.selectedBuildCaseId,\n            CPictureID: this.currentEditItem,\n            CFile: this.listPictures[0].CFile\n          }\n        });\n        updateRequest.pipe(tap(res => {\n          if (res.StatusCode == 0) {\n            this.message.showSucessMSG('執行成功');\n            this.listPictures = [];\n          } else {\n            this.message.showErrorMSG(res.Message);\n          }\n          ref.close();\n          this.resetPreviewState();\n        }), concatMap(res => res.StatusCode == 0 ? this.getPicturelList(1) : of(null))).subscribe();\n      }\n    }\n  }\n  renameFile(event, index) {\n    var blob = this.listPictures[index].CFile.slice(0, this.listPictures[index].CFile.size, this.listPictures[index].CFile.type);\n    var newFile = new File([blob], `${event.target.value + '.' + this.listPictures[index].extension}`, {\n      type: this.listPictures[index].CFile.type\n    });\n    this.listPictures[index].CFile = newFile;\n  }\n  // 批次選擇相關方法\n  toggleSelectItem(itemId) {\n    if (this.selectedItems.has(itemId)) {\n      this.selectedItems.delete(itemId);\n    } else {\n      this.selectedItems.add(itemId);\n    }\n    this.updateSelectAllState();\n  }\n  toggleSelectAll(checked) {\n    this.selectAll = checked;\n    if (checked) {\n      // 全選當前頁面的所有項目\n      this.images.forEach(item => {\n        if (item.CId) {\n          this.selectedItems.add(item.CId);\n        }\n      });\n    } else {\n      // 取消全選 - 只移除當前頁面的項目\n      const currentPageIds = this.images.map(item => item.CId).filter(id => id !== undefined);\n      currentPageIds.forEach(id => this.selectedItems.delete(id));\n    }\n  }\n  updateSelectAllState() {\n    const currentPageIds = this.images.map(item => item.CId).filter(id => id !== undefined);\n    this.selectAll = currentPageIds.length > 0 && currentPageIds.every(id => this.selectedItems.has(id));\n  }\n  // 批次刪除方法\n  batchDelete() {\n    if (this.selectedItems.size === 0) {\n      this.message.showErrorMSG('請選擇要刪除的項目');\n      return;\n    }\n    const selectedIds = Array.from(this.selectedItems);\n    // 顯示確認對話框\n    if (confirm(`確定要刪除選中的 ${selectedIds.length} 個項目嗎？`)) {\n      this._pictureService.apiPictureDeletePicturePost$Json({\n        body: selectedIds\n      }).pipe(tap(res => {\n        if (res.StatusCode === 0) {\n          this.message.showSucessMSG(`成功刪除 ${selectedIds.length} 個項目`);\n          // 清空選擇狀態\n          this.selectedItems.clear();\n          this.selectAll = false;\n        } else {\n          this.message.showErrorMSG(res.Message || '刪除失敗');\n        }\n      }), concatMap(res => res.StatusCode === 0 ? this.getPicturelList(this.pageIndex) : of(null))).subscribe();\n    }\n  }\n  // 檢查項目是否被選中\n  isItemSelected(itemId) {\n    return this.selectedItems.has(itemId);\n  }\n  // 輪播預覽相關方法\n  getCurrentPreviewImage() {\n    if (this.currentPreviewImages.length === 0 || this.currentImageIndex < 0 || this.currentImageIndex >= this.currentPreviewImages.length) {\n      return '';\n    }\n    const currentImage = this.currentPreviewImages[this.currentImageIndex];\n    // 優先使用 CBase64，如果沒有則使用 CFile\n    return currentImage.CBase64 || currentImage.CFile || '';\n  }\n  previousImage() {\n    if (this.currentPreviewImages.length === 0) return;\n    this.currentImageIndex = this.currentImageIndex > 0 ? this.currentImageIndex - 1 : this.currentPreviewImages.length - 1;\n    this.currentImageShowing = this.getCurrentPreviewImage();\n  }\n  nextImage() {\n    if (this.currentPreviewImages.length === 0) return;\n    this.currentImageIndex = this.currentImageIndex < this.currentPreviewImages.length - 1 ? this.currentImageIndex + 1 : 0;\n    this.currentImageShowing = this.getCurrentPreviewImage();\n  }\n  goToImage(index) {\n    if (index >= 0 && index < this.currentPreviewImages.length) {\n      this.currentImageIndex = index;\n      this.currentImageShowing = this.getCurrentPreviewImage();\n    }\n  }\n  get currentImageInfo() {\n    if (this.currentPreviewImages.length === 0 || this.currentImageIndex < 0 || this.currentImageIndex >= this.currentPreviewImages.length) {\n      return null;\n    }\n    return this.currentPreviewImages[this.currentImageIndex];\n  }\n  get imageCounter() {\n    if (this.currentPreviewImages.length === 0) return '';\n    return `${this.currentImageIndex + 1} / ${this.currentPreviewImages.length}`;\n  }\n  // 重置預覽狀態\n  resetPreviewState() {\n    this.isPreviewMode = false;\n    this.currentPreviewImages = [];\n    this.currentImageIndex = 0;\n    this.currentImageShowing = \"\";\n  }\n  // 鍵盤導航支持\n  handleKeyboardEvent(event) {\n    if (!this.isPreviewMode || this.currentPreviewImages.length <= 1) return;\n    switch (event.key) {\n      case 'ArrowLeft':\n        event.preventDefault();\n        this.previousImage();\n        break;\n      case 'ArrowRight':\n        event.preventDefault();\n        this.nextImage();\n        break;\n      case 'Escape':\n        event.preventDefault();\n        // 可以在這裡添加關閉對話框的邏輯\n        break;\n    }\n  }\n};\n__decorate([HostListener('document:keydown', ['$event'])], PictureMaterialComponent.prototype, \"handleKeyboardEvent\", null);\nPictureMaterialComponent = __decorate([Component({\n  selector: 'ngx-picture-material',\n  templateUrl: './picture-material.component.html',\n  styleUrls: ['./picture-material.component.scss'],\n  standalone: true,\n  imports: [CommonModule, SharedModule, BaseFilePipe, Base64ImagePipe]\n})], PictureMaterialComponent);\nexport { PictureMaterialComponent };", "map": {"version": 3, "names": ["Component", "HostListener", "CommonModule", "concatMap", "of", "tap", "BaseFilePipe", "Base64ImagePipe", "SharedModule", "BaseComponent", "JSZip", "PictureCategory", "PictureMaterialComponent", "selectedCount", "selectedItems", "size", "getCategoryLabel", "category", "option", "categoryOptions", "find", "opt", "value", "label", "constructor", "_allow", "dialogService", "valid", "_pictureService", "_buildCaseService", "message", "_utilityService", "images", "listUserBuildCases", "selectedCate<PERSON><PERSON>", "NONE", "isCategorySelected", "Set", "selectAll", "currentImageShowing", "currentPreviewImages", "currentImageIndex", "isPreviewMode", "listPictures", "isEdit", "BUILDING_MATERIAL", "SCHEMATIC", "ngOnInit", "getListBuildCase", "apiBuildCaseGetUserBuildCasePost$Json", "pipe", "res", "StatusCode", "Entries", "selectedBuildCaseId", "cID", "getPicturelList", "subscribe", "openPdfInNewTab", "CFileUrl", "openFileInNewTab", "pageIndex", "clear", "apiPictureGetPicturelListPost$Json", "body", "PageIndex", "PageSize", "pageSize", "CBuildCaseId", "cPictureType", "totalRecords", "TotalItems", "updateSelectAllState", "pageChanged", "<PERSON><PERSON><PERSON><PERSON>", "buildCaseId", "categoryChanged", "addNew", "ref", "item", "showErrorMSG", "open", "findIndex", "img", "CId", "getCurrentPreviewImage", "changePicture", "currentEditItem", "validation", "CFile", "nameSet", "has", "name", "addErrorMessage", "add", "onSubmit", "detectFiles", "event", "index", "target", "files", "length", "file", "type", "toLowerCase", "endsWith", "processZipFile", "processSingleImageFile", "zipFile", "zip", "loadAsync", "then", "contents", "imageFiles", "for<PERSON>ach", "relativePath", "dir", "isImageFile", "push", "async", "blob", "fileName", "split", "pop", "imageFile", "File", "getImageMimeType", "processImageFileFromZip", "Promise", "all", "showSucessMSG", "catch", "error", "console", "reader", "FileReader", "readAsDataURL", "onload", "base64Str", "result", "addImageToList", "originalPath", "resolve", "reject", "onerror", "fileNameWithoutExtension", "existingFileIndex", "picture", "data", "extension", "getFileExtension", "id", "Date", "getTime", "Math", "random", "imageExtensions", "substring", "lastIndexOf", "includes", "removeImage", "pictureId", "filter", "x", "uploadImage", "map", "errorMessages", "showErrorMSGs", "uploadRequest", "apiPictureUploadListPicturePost$Json", "CPath", "Message", "close", "resetPreviewState", "updateRequest", "apiPictureUpdatePicturePost$Json", "CBuildCaseID", "CPictureID", "renameFile", "slice", "newFile", "toggleSelectItem", "itemId", "delete", "toggleSelectAll", "checked", "currentPageIds", "undefined", "every", "batchDelete", "selectedIds", "Array", "from", "confirm", "apiPictureDeletePicturePost$Json", "isItemSelected", "currentImage", "CBase64", "previousImage", "nextImage", "goToImage", "currentImageInfo", "imageCounter", "handleKeyboardEvent", "key", "preventDefault", "__decorate", "selector", "templateUrl", "styleUrls", "standalone", "imports"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\selection-management\\picture-material\\picture-material.component.ts"], "sourcesContent": ["import { Component, OnInit, HostListener } from '@angular/core';\r\nimport { CommonModule, formatDate } from '@angular/common';\r\nimport { NbDialogService } from '@nebular/theme';\r\nimport { ValidationHelper } from 'src/app/shared/helper/validationHelper';\r\nimport { BuildCaseService, PictureService } from 'src/services/api/services';\r\nimport { BuildCaseGetListReponse, GetPictureListResponse, GetInfoPictureListResponse } from 'src/services/api/models';\r\nimport { AllowHelper } from 'src/app/shared/helper/allowHelper';\r\nimport { concatMap, finalize, of, tap } from 'rxjs';\r\nimport { BaseFilePipe } from 'src/app/@theme/pipes/base-file.pipe';\r\nimport { Base64ImagePipe } from 'src/app/@theme/pipes/base64-image.pipe';\r\nimport { MessageService } from 'src/app/shared/services/message.service';\r\nimport { UtilityService } from 'src/app/shared/services/utility.service';\r\nimport { SharedModule } from '../../components/shared.module';\r\nimport { BaseComponent } from '../../components/base/baseComponent';\r\nimport * as JSZip from 'jszip';\r\n\r\n// 圖片類別枚舉\r\nenum PictureCategory {\r\n  NONE = 0,           // 未選擇\r\n  BUILDING_MATERIAL = 1,  // 建材圖片\r\n  SCHEMATIC = 2       // 示意圖片\r\n}\r\n\r\n@Component({\r\n  selector: 'ngx-picture-material',\r\n  templateUrl: './picture-material.component.html',\r\n  styleUrls: ['./picture-material.component.scss'],\r\n  standalone: true,  imports: [\r\n    CommonModule,\r\n    SharedModule,\r\n    BaseFilePipe,\r\n    Base64ImagePipe\r\n  ],\r\n})\r\n\r\nexport class PictureMaterialComponent extends BaseComponent implements OnInit {\r\n  images: (GetPictureListResponse | GetInfoPictureListResponse)[] = [];\r\n  listUserBuildCases: BuildCaseGetListReponse[] = []\r\n  selectedBuildCaseId: number\r\n  selectedCategory: PictureCategory = PictureCategory.NONE;\r\n  isCategorySelected: boolean = false; // 追蹤用戶是否已經明確選擇類別\r\n\r\n  // 批次選擇相關屬性\r\n  selectedItems: Set<number> = new Set<number>(); // 選中的項目 ID\r\n  selectAll: boolean = false; // 全選狀態\r\n  get selectedCount(): number {\r\n    return this.selectedItems.size;\r\n  }\r\n  currentImageShowing: string = \"\"\r\n\r\n  // 輪播預覽相關屬性\r\n  currentPreviewImages: (GetPictureListResponse | GetInfoPictureListResponse)[] = [];\r\n  currentImageIndex: number = 0;\r\n  isPreviewMode: boolean = false;\r\n\r\n  listPictures: any[] = []\r\n\r\n  isEdit: boolean = false;\r\n  currentEditItem: number;\r\n\r\n  // 類別選項\r\n  categoryOptions = [\r\n    { value: PictureCategory.BUILDING_MATERIAL, label: '建材圖片' },\r\n    { value: PictureCategory.SCHEMATIC, label: '示意圖片' }\r\n  ]\r\n\r\n  // 讓模板可以使用 enum\r\n  PictureCategory = PictureCategory;\r\n\r\n  // 獲取類別標籤的方法\r\n  getCategoryLabel(category: number): string {\r\n    const option = this.categoryOptions.find(opt => opt.value === category);\r\n    return option ? option.label : '未知類別';\r\n  }\r\n  constructor(\r\n    private _allow: AllowHelper,\r\n    private dialogService: NbDialogService,\r\n    private valid: ValidationHelper,\r\n    private _pictureService: PictureService,\r\n    private _buildCaseService: BuildCaseService,\r\n    private message: MessageService,\r\n    private _utilityService: UtilityService\r\n  ) {\r\n    super(_allow)\r\n  }\r\n\r\n  override ngOnInit(): void {\r\n    this.getListBuildCase();\r\n  }\r\n\r\n  getListBuildCase() {\r\n    this._buildCaseService.apiBuildCaseGetUserBuildCasePost$Json({})\r\n      .pipe(\r\n        tap(res => {\r\n          if (res.StatusCode === 0) {\r\n            this.listUserBuildCases = res.Entries! ?? []\r\n            this.selectedBuildCaseId = this.listUserBuildCases[0].cID!\r\n          }\r\n        }),\r\n        concatMap(() => this.getPicturelList(1))\r\n      ).subscribe()\r\n  }\r\n\r\n  openPdfInNewTab(CFileUrl?: any) {\r\n    if (CFileUrl) {\r\n      this._utilityService.openFileInNewTab(CFileUrl)\r\n    }\r\n  } getPicturelList(pageIndex: number) {\r\n    // 重置選擇狀態\r\n    this.selectedItems.clear();\r\n    this.selectAll = false;\r\n\r\n    if (this.selectedCategory === PictureCategory.BUILDING_MATERIAL) {\r\n      return this._pictureService.apiPictureGetPicturelListPost$Json({\r\n        body: {\r\n          PageIndex: pageIndex,\r\n          PageSize: this.pageSize,\r\n          CBuildCaseId: this.selectedBuildCaseId,\r\n          cPictureType: this.selectedCategory\r\n        }\r\n      }).pipe(\r\n        tap(res => {\r\n          if (res.StatusCode == 0) {\r\n            this.images = res.Entries! ?? []\r\n            this.totalRecords = res.TotalItems!;\r\n            this.updateSelectAllState();\r\n          }\r\n        })\r\n      )\r\n    } else if (this.selectedCategory === PictureCategory.SCHEMATIC) {\r\n      return this._pictureService.apiPictureGetPicturelListPost$Json({\r\n        body: {\r\n          PageIndex: pageIndex,\r\n          PageSize: this.pageSize,\r\n          CBuildCaseId: this.selectedBuildCaseId,\r\n          cPictureType: this.selectedCategory\r\n        }\r\n      }).pipe(\r\n        tap(res => {\r\n          if (res.StatusCode == 0) {\r\n            this.images = res.Entries! ?? []\r\n            this.totalRecords = res.TotalItems!;\r\n            this.updateSelectAllState();\r\n          }\r\n        })\r\n      )\r\n    } else {\r\n      // 如果沒有選擇類別，清空數據並返回預設的空 observable\r\n      this.images = [];\r\n      this.totalRecords = 0;\r\n      return this._pictureService.apiPictureGetPicturelListPost$Json({\r\n        body: {\r\n          PageIndex: 1,\r\n          PageSize: 1,\r\n          CBuildCaseId: -1, // 使用無效 ID 確保返回空結果\r\n          cPictureType: PictureCategory.NONE\r\n        }\r\n      }).pipe(\r\n        tap(() => {\r\n          this.images = [];\r\n          this.totalRecords = 0;\r\n        })\r\n      )\r\n    }\r\n  }\r\n\r\n  pageChanged(pageIndex: number) {\r\n    // this.pageIndex = newPage;\r\n    this.getPicturelList(pageIndex).subscribe();\r\n  }\r\n  selectedChange(buildCaseId: number) {\r\n    this.selectedBuildCaseId = buildCaseId;\r\n    this.getPicturelList(1).subscribe();\r\n  } categoryChanged(category: PictureCategory) {\r\n    this.selectedCategory = category;\r\n    this.isCategorySelected = true; // 標記用戶已經選擇了類別\r\n    this.getPicturelList(1).subscribe();\r\n  } addNew(ref: any, item?: GetPictureListResponse | GetInfoPictureListResponse) {\r\n    // 如果是新增圖片（沒有傳入 item），則檢查是否已選擇類別\r\n    if (!item && !this.isCategorySelected) {\r\n      this.message.showErrorMSG('請先選擇圖片類別');\r\n      return;\r\n    }\r\n\r\n    this.listPictures = []\r\n    this.dialogService.open(ref)\r\n    this.isEdit = false;\r\n\r\n    if (!!item) {\r\n      // 預覽模式 - 顯示當前類別的所有圖片並支持輪播\r\n      this.isPreviewMode = true;\r\n      this.currentPreviewImages = [...this.images];\r\n      this.currentImageIndex = this.images.findIndex(img => img.CId === item.CId);\r\n      if (this.currentImageIndex === -1) {\r\n        this.currentImageIndex = 0;\r\n      }\r\n      this.currentImageShowing = this.getCurrentPreviewImage();\r\n    } else {\r\n      // 上傳模式\r\n      this.isPreviewMode = false;\r\n      this.currentPreviewImages = [];\r\n      this.currentImageIndex = 0;\r\n      this.currentImageShowing = \"\";\r\n      this.listPictures = [];\r\n    }\r\n  }\r\n  changePicture(ref: any, item?: GetPictureListResponse | GetInfoPictureListResponse) {\r\n    // 檢查是否已選擇類別\r\n    if (!this.selectedCategory) {\r\n      this.message.showErrorMSG('請先選擇圖片類別');\r\n      return;\r\n    }\r\n\r\n    if (!!item && item.CId) {\r\n      this.dialogService.open(ref)\r\n      this.isEdit = true;\r\n      this.currentEditItem = item.CId;\r\n      this.listPictures = [];\r\n    }\r\n  }\r\n\r\n  validation(CFile: any) {\r\n    this.valid.clear();\r\n    const nameSet = new Set();\r\n    for (const item of CFile) {\r\n      if (nameSet.has(item.name)) {\r\n        this.valid.addErrorMessage('檔名不可重複')\r\n        return;\r\n      }\r\n      nameSet.add(item.name);\r\n    }\r\n  }\r\n\r\n  onSubmit(ref: any) {\r\n  }\r\n  detectFiles(event: any) {\r\n    for (let index = 0; index < event.target.files.length; index++) {\r\n      const file = event.target.files[index];\r\n      if (file) {\r\n        // 檢查是否為 ZIP 檔案\r\n        if (file.type === 'application/zip' || file.name.toLowerCase().endsWith('.zip')) {\r\n          this.processZipFile(file);\r\n        } else {\r\n          this.processSingleImageFile(file);\r\n        }\r\n      }\r\n    }\r\n    // Reset input file to be able to select the old file again\r\n    event.target.value = null;\r\n  }\r\n\r\n  processZipFile(zipFile: File) {\r\n    const zip = new JSZip();\r\n\r\n    zip.loadAsync(zipFile).then((contents) => {\r\n      const imageFiles: Promise<any>[] = []; contents.forEach((relativePath, file) => {\r\n        // 只處理圖片檔案，跳過資料夾\r\n        if (!file.dir && this.isImageFile(relativePath)) {\r\n          imageFiles.push(\r\n            file.async('blob').then((blob) => {\r\n              // 只取檔案名稱，移除資料夾路徑\r\n              const fileName = relativePath.split('/').pop() || relativePath.split('\\\\').pop() || relativePath;\r\n              // 建立 File 物件\r\n              const imageFile = new File([blob], fileName, { type: this.getImageMimeType(relativePath) });\r\n              return this.processImageFileFromZip(imageFile, fileName);\r\n            })\r\n          );\r\n        }\r\n      });\r\n\r\n      // 處理所有圖片檔案\r\n      Promise.all(imageFiles).then(() => {\r\n        this.message.showSucessMSG(`成功從 ZIP 檔案中匯入 ${imageFiles.length} 張圖片`);\r\n      }).catch((error) => {\r\n        console.error('處理 ZIP 檔案中的圖片時發生錯誤:', error);\r\n        this.message.showErrorMSG('處理 ZIP 檔案中的圖片時發生錯誤');\r\n      });\r\n\r\n    }).catch((error) => {\r\n      console.error('讀取 ZIP 檔案時發生錯誤:', error);\r\n      this.message.showErrorMSG('無法讀取 ZIP 檔案，請確認檔案格式正確');\r\n    });\r\n  }\r\n\r\n  processSingleImageFile(file: File) {\r\n    let reader = new FileReader();\r\n    reader.readAsDataURL(file);\r\n    reader.onload = () => {\r\n      let base64Str: string = reader.result as string;\r\n      if (!base64Str) {\r\n        return;\r\n      }\r\n      this.addImageToList(file, base64Str);\r\n    };\r\n  }\r\n\r\n  processImageFileFromZip(file: File, originalPath: string): Promise<void> {\r\n    return new Promise((resolve, reject) => {\r\n      let reader = new FileReader();\r\n      reader.readAsDataURL(file);\r\n      reader.onload = () => {\r\n        let base64Str: string = reader.result as string;\r\n        if (!base64Str) {\r\n          reject('無法讀取圖片檔案');\r\n          return;\r\n        }\r\n        this.addImageToList(file, base64Str, originalPath);\r\n        resolve();\r\n      };\r\n      reader.onerror = () => {\r\n        reject('讀取圖片檔案時發生錯誤');\r\n      };\r\n    });\r\n  }\r\n  addImageToList(file: File, base64Str: string, originalPath?: string) {\r\n    // Get name file ( no extension)\r\n    let fileName = originalPath || file.name;\r\n\r\n    // 如果是從 ZIP 檔案來的，只取檔案名稱，移除資料夾路徑\r\n    if (originalPath) {\r\n      fileName = originalPath.split('/').pop() || originalPath.split('\\\\').pop() || originalPath;\r\n    }\r\n\r\n    const fileNameWithoutExtension = fileName.split('.')[0];\r\n\r\n    // Find files with duplicate names\r\n    const existingFileIndex = this.listPictures.findIndex(picture => picture.name === fileNameWithoutExtension);\r\n    if (existingFileIndex !== -1) {\r\n      // If name is duplicate, update file data\r\n      this.listPictures[existingFileIndex] = {\r\n        ...this.listPictures[existingFileIndex],\r\n        data: base64Str,\r\n        CFile: file,\r\n        extension: this._utilityService.getFileExtension(fileName)\r\n      };\r\n    } else {\r\n      // If not duplicate, add new file\r\n      this.listPictures.push({\r\n        id: new Date().getTime() + Math.random(),\r\n        name: fileNameWithoutExtension,\r\n        data: base64Str,\r\n        extension: this._utilityService.getFileExtension(fileName),\r\n        CFile: file\r\n      });\r\n    }\r\n  }\r\n\r\n  isImageFile(fileName: string): boolean {\r\n    const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp'];\r\n    const extension = fileName.toLowerCase().substring(fileName.lastIndexOf('.'));\r\n    return imageExtensions.includes(extension);\r\n  }\r\n\r\n  getImageMimeType(fileName: string): string {\r\n    const extension = fileName.toLowerCase().substring(fileName.lastIndexOf('.'));\r\n    switch (extension) {\r\n      case '.jpg':\r\n      case '.jpeg':\r\n        return 'image/jpeg';\r\n      case '.png':\r\n        return 'image/png';\r\n      case '.gif':\r\n        return 'image/gif';\r\n      case '.bmp':\r\n        return 'image/bmp';\r\n      case '.webp':\r\n        return 'image/webp';\r\n      default:\r\n        return 'image/jpeg';\r\n    }\r\n  }\r\n\r\n  removeImage(pictureId: number) {\r\n    this.listPictures = this.listPictures.filter(x => x.id != pictureId)\r\n  }\r\n  uploadImage(ref: any) {\r\n    if (!this.isEdit) {\r\n      const CFile = this.listPictures.map(x => x.CFile)\r\n      this.validation(CFile)\r\n      if (this.valid.errorMessages.length > 0) {\r\n        this.message.showErrorMSGs(this.valid.errorMessages);\r\n        return\r\n      }      // 統一使用 PictureService 進行上傳\r\n      const uploadRequest = this._pictureService.apiPictureUploadListPicturePost$Json({\r\n        body: {\r\n          CBuildCaseId: this.selectedBuildCaseId,\r\n          CPath: this.selectedCategory === PictureCategory.BUILDING_MATERIAL ? \"picture\" : \"infoPicture\",\r\n          CFile: CFile,\r\n          cPictureType: this.selectedCategory\r\n        }\r\n      }); uploadRequest.pipe(\r\n        tap(res => {\r\n          if (res.StatusCode == 0) {\r\n            this.message.showSucessMSG('執行成功')\r\n            this.listPictures = []\r\n          } else {\r\n            this.message.showErrorMSG(res.Message!)\r\n          }\r\n          ref.close();\r\n          this.resetPreviewState();\r\n        }),\r\n        concatMap((res) => res.StatusCode! == 0 ? this.getPicturelList(1) : of(null)),\r\n      ).subscribe()\r\n    }\r\n    else {\r\n      if (this.listPictures.length > 0 && this.listPictures[0].CFile) {\r\n        // 統一使用 PictureService 進行更新\r\n        const updateRequest = this._pictureService.apiPictureUpdatePicturePost$Json({\r\n          body: {\r\n            CBuildCaseID: this.selectedBuildCaseId,\r\n            CPictureID: this.currentEditItem,\r\n            CFile: this.listPictures[0].CFile\r\n          }\r\n        }); updateRequest.pipe(\r\n          tap(res => {\r\n            if (res.StatusCode == 0) {\r\n              this.message.showSucessMSG('執行成功')\r\n              this.listPictures = []\r\n            } else {\r\n              this.message.showErrorMSG(res.Message!)\r\n            }\r\n            ref.close();\r\n            this.resetPreviewState();\r\n          }),\r\n          concatMap((res) => res.StatusCode! == 0 ? this.getPicturelList(1) : of(null)),\r\n        ).subscribe()\r\n      }\r\n    }\r\n  }\r\n\r\n  renameFile(event: any, index: number) {\r\n    var blob = this.listPictures[index].CFile.slice(0, this.listPictures[index].CFile.size, this.listPictures[index].CFile.type);\r\n    var newFile = new File([blob], `${event.target.value + '.' + this.listPictures[index].extension}`, { type: this.listPictures[index].CFile.type });\r\n\r\n    this.listPictures[index].CFile = newFile\r\n  }\r\n  // 批次選擇相關方法\r\n  toggleSelectItem(itemId: number) {\r\n    if (this.selectedItems.has(itemId)) {\r\n      this.selectedItems.delete(itemId);\r\n    } else {\r\n      this.selectedItems.add(itemId);\r\n    }\r\n    this.updateSelectAllState();\r\n  }\r\n\r\n  toggleSelectAll(checked: boolean) {\r\n    this.selectAll = checked;\r\n    if (checked) {\r\n      // 全選當前頁面的所有項目\r\n      this.images.forEach(item => {\r\n        if (item.CId) {\r\n          this.selectedItems.add(item.CId);\r\n        }\r\n      });\r\n    } else {\r\n      // 取消全選 - 只移除當前頁面的項目\r\n      const currentPageIds = this.images.map(item => item.CId).filter(id => id !== undefined) as number[];\r\n      currentPageIds.forEach(id => this.selectedItems.delete(id));\r\n    }\r\n  }\r\n\r\n  updateSelectAllState() {\r\n    const currentPageIds = this.images.map(item => item.CId).filter(id => id !== undefined) as number[];\r\n    this.selectAll = currentPageIds.length > 0 && currentPageIds.every(id => this.selectedItems.has(id));\r\n  }\r\n  // 批次刪除方法\r\n  batchDelete() {\r\n    if (this.selectedItems.size === 0) {\r\n      this.message.showErrorMSG('請選擇要刪除的項目');\r\n      return;\r\n    }\r\n\r\n    const selectedIds = Array.from(this.selectedItems);\r\n\r\n    // 顯示確認對話框\r\n    if (confirm(`確定要刪除選中的 ${selectedIds.length} 個項目嗎？`)) {\r\n      this._pictureService.apiPictureDeletePicturePost$Json({\r\n        body: selectedIds\r\n      }).pipe(\r\n        tap(res => {\r\n          if (res.StatusCode === 0) {\r\n            this.message.showSucessMSG(`成功刪除 ${selectedIds.length} 個項目`);\r\n            // 清空選擇狀態\r\n            this.selectedItems.clear();\r\n            this.selectAll = false;\r\n          } else {\r\n            this.message.showErrorMSG(res.Message || '刪除失敗');\r\n          }\r\n        }),\r\n        concatMap((res) => res.StatusCode === 0 ? this.getPicturelList(this.pageIndex) : of(null))\r\n      ).subscribe();\r\n    }\r\n  }\r\n  // 檢查項目是否被選中\r\n  isItemSelected(itemId: number): boolean {\r\n    return this.selectedItems.has(itemId);\r\n  }\r\n\r\n  // 輪播預覽相關方法\r\n  getCurrentPreviewImage(): string {\r\n    if (this.currentPreviewImages.length === 0 || this.currentImageIndex < 0 || this.currentImageIndex >= this.currentPreviewImages.length) {\r\n      return '';\r\n    }\r\n    const currentImage = this.currentPreviewImages[this.currentImageIndex];\r\n    // 優先使用 CBase64，如果沒有則使用 CFile\r\n    return currentImage.CBase64 || currentImage.CFile || '';\r\n  }\r\n\r\n  previousImage(): void {\r\n    if (this.currentPreviewImages.length === 0) return;\r\n    this.currentImageIndex = this.currentImageIndex > 0 ? this.currentImageIndex - 1 : this.currentPreviewImages.length - 1;\r\n    this.currentImageShowing = this.getCurrentPreviewImage();\r\n  }\r\n  nextImage(): void {\r\n    if (this.currentPreviewImages.length === 0) return;\r\n    this.currentImageIndex = this.currentImageIndex < this.currentPreviewImages.length - 1 ? this.currentImageIndex + 1 : 0;\r\n    this.currentImageShowing = this.getCurrentPreviewImage();\r\n  }\r\n\r\n  goToImage(index: number): void {\r\n    if (index >= 0 && index < this.currentPreviewImages.length) {\r\n      this.currentImageIndex = index;\r\n      this.currentImageShowing = this.getCurrentPreviewImage();\r\n    }\r\n  }\r\n\r\n  get currentImageInfo() {\r\n    if (this.currentPreviewImages.length === 0 || this.currentImageIndex < 0 || this.currentImageIndex >= this.currentPreviewImages.length) {\r\n      return null;\r\n    }\r\n    return this.currentPreviewImages[this.currentImageIndex];\r\n  }\r\n  get imageCounter(): string {\r\n    if (this.currentPreviewImages.length === 0) return '';\r\n    return `${this.currentImageIndex + 1} / ${this.currentPreviewImages.length}`;\r\n  }\r\n  // 重置預覽狀態\r\n  resetPreviewState(): void {\r\n    this.isPreviewMode = false;\r\n    this.currentPreviewImages = [];\r\n    this.currentImageIndex = 0;\r\n    this.currentImageShowing = \"\";\r\n  }\r\n\r\n  // 鍵盤導航支持\r\n  @HostListener('document:keydown', ['$event'])\r\n  handleKeyboardEvent(event: KeyboardEvent): void {\r\n    if (!this.isPreviewMode || this.currentPreviewImages.length <= 1) return;\r\n\r\n    switch (event.key) {\r\n      case 'ArrowLeft':\r\n        event.preventDefault();\r\n        this.previousImage();\r\n        break;\r\n      case 'ArrowRight':\r\n        event.preventDefault();\r\n        this.nextImage();\r\n        break;\r\n      case 'Escape':\r\n        event.preventDefault();\r\n        // 可以在這裡添加關閉對話框的邏輯\r\n        break;\r\n    }\r\n  }\r\n}\r\n"], "mappings": ";AAAA,SAASA,SAAS,EAAUC,YAAY,QAAQ,eAAe;AAC/D,SAASC,YAAY,QAAoB,iBAAiB;AAM1D,SAASC,SAAS,EAAYC,EAAE,EAAEC,GAAG,QAAQ,MAAM;AACnD,SAASC,YAAY,QAAQ,qCAAqC;AAClE,SAASC,eAAe,QAAQ,wCAAwC;AAGxE,SAASC,YAAY,QAAQ,gCAAgC;AAC7D,SAASC,aAAa,QAAQ,qCAAqC;AACnE,OAAO,KAAKC,KAAK,MAAM,OAAO;AAE9B;AACA,IAAKC,eAIJ;AAJD,WAAKA,eAAe;EAClBA,eAAA,CAAAA,eAAA,sBAAQ;EACRA,eAAA,CAAAA,eAAA,gDAAqB;EACrBA,eAAA,CAAAA,eAAA,gCAAa,EAAO;AACtB,CAAC,EAJIA,eAAe,KAAfA,eAAe;AAkBb,IAAMC,wBAAwB,GAA9B,MAAMA,wBAAyB,SAAQH,aAAa;EAUzD,IAAII,aAAaA,CAAA;IACf,OAAO,IAAI,CAACC,aAAa,CAACC,IAAI;EAChC;EAsBA;EACAC,gBAAgBA,CAACC,QAAgB;IAC/B,MAAMC,MAAM,GAAG,IAAI,CAACC,eAAe,CAACC,IAAI,CAACC,GAAG,IAAIA,GAAG,CAACC,KAAK,KAAKL,QAAQ,CAAC;IACvE,OAAOC,MAAM,GAAGA,MAAM,CAACK,KAAK,GAAG,MAAM;EACvC;EACAC,YACUC,MAAmB,EACnBC,aAA8B,EAC9BC,KAAuB,EACvBC,eAA+B,EAC/BC,iBAAmC,EACnCC,OAAuB,EACvBC,eAA+B;IAEvC,KAAK,CAACN,MAAM,CAAC;IARL,KAAAA,MAAM,GAANA,MAAM;IACN,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAC,eAAe,GAAfA,eAAe;IA7CzB,KAAAC,MAAM,GAA4D,EAAE;IACpE,KAAAC,kBAAkB,GAA8B,EAAE;IAElD,KAAAC,gBAAgB,GAAoBvB,eAAe,CAACwB,IAAI;IACxD,KAAAC,kBAAkB,GAAY,KAAK,CAAC,CAAC;IAErC;IACA,KAAAtB,aAAa,GAAgB,IAAIuB,GAAG,EAAU,CAAC,CAAC;IAChD,KAAAC,SAAS,GAAY,KAAK,CAAC,CAAC;IAI5B,KAAAC,mBAAmB,GAAW,EAAE;IAEhC;IACA,KAAAC,oBAAoB,GAA4D,EAAE;IAClF,KAAAC,iBAAiB,GAAW,CAAC;IAC7B,KAAAC,aAAa,GAAY,KAAK;IAE9B,KAAAC,YAAY,GAAU,EAAE;IAExB,KAAAC,MAAM,GAAY,KAAK;IAGvB;IACA,KAAAzB,eAAe,GAAG,CAChB;MAAEG,KAAK,EAAEX,eAAe,CAACkC,iBAAiB;MAAEtB,KAAK,EAAE;IAAM,CAAE,EAC3D;MAAED,KAAK,EAAEX,eAAe,CAACmC,SAAS;MAAEvB,KAAK,EAAE;IAAM,CAAE,CACpD;IAED;IACA,KAAAZ,eAAe,GAAGA,eAAe;EAiBjC;EAESoC,QAAQA,CAAA;IACf,IAAI,CAACC,gBAAgB,EAAE;EACzB;EAEAA,gBAAgBA,CAAA;IACd,IAAI,CAACnB,iBAAiB,CAACoB,qCAAqC,CAAC,EAAE,CAAC,CAC7DC,IAAI,CACH7C,GAAG,CAAC8C,GAAG,IAAG;MACR,IAAIA,GAAG,CAACC,UAAU,KAAK,CAAC,EAAE;QACxB,IAAI,CAACnB,kBAAkB,GAAGkB,GAAG,CAACE,OAAQ,IAAI,EAAE;QAC5C,IAAI,CAACC,mBAAmB,GAAG,IAAI,CAACrB,kBAAkB,CAAC,CAAC,CAAC,CAACsB,GAAI;MAC5D;IACF,CAAC,CAAC,EACFpD,SAAS,CAAC,MAAM,IAAI,CAACqD,eAAe,CAAC,CAAC,CAAC,CAAC,CACzC,CAACC,SAAS,EAAE;EACjB;EAEAC,eAAeA,CAACC,QAAc;IAC5B,IAAIA,QAAQ,EAAE;MACZ,IAAI,CAAC5B,eAAe,CAAC6B,gBAAgB,CAACD,QAAQ,CAAC;IACjD;EACF;EAAEH,eAAeA,CAACK,SAAiB;IACjC;IACA,IAAI,CAAC/C,aAAa,CAACgD,KAAK,EAAE;IAC1B,IAAI,CAACxB,SAAS,GAAG,KAAK;IAEtB,IAAI,IAAI,CAACJ,gBAAgB,KAAKvB,eAAe,CAACkC,iBAAiB,EAAE;MAC/D,OAAO,IAAI,CAACjB,eAAe,CAACmC,kCAAkC,CAAC;QAC7DC,IAAI,EAAE;UACJC,SAAS,EAAEJ,SAAS;UACpBK,QAAQ,EAAE,IAAI,CAACC,QAAQ;UACvBC,YAAY,EAAE,IAAI,CAACd,mBAAmB;UACtCe,YAAY,EAAE,IAAI,CAACnC;;OAEtB,CAAC,CAACgB,IAAI,CACL7C,GAAG,CAAC8C,GAAG,IAAG;QACR,IAAIA,GAAG,CAACC,UAAU,IAAI,CAAC,EAAE;UACvB,IAAI,CAACpB,MAAM,GAAGmB,GAAG,CAACE,OAAQ,IAAI,EAAE;UAChC,IAAI,CAACiB,YAAY,GAAGnB,GAAG,CAACoB,UAAW;UACnC,IAAI,CAACC,oBAAoB,EAAE;QAC7B;MACF,CAAC,CAAC,CACH;IACH,CAAC,MAAM,IAAI,IAAI,CAACtC,gBAAgB,KAAKvB,eAAe,CAACmC,SAAS,EAAE;MAC9D,OAAO,IAAI,CAAClB,eAAe,CAACmC,kCAAkC,CAAC;QAC7DC,IAAI,EAAE;UACJC,SAAS,EAAEJ,SAAS;UACpBK,QAAQ,EAAE,IAAI,CAACC,QAAQ;UACvBC,YAAY,EAAE,IAAI,CAACd,mBAAmB;UACtCe,YAAY,EAAE,IAAI,CAACnC;;OAEtB,CAAC,CAACgB,IAAI,CACL7C,GAAG,CAAC8C,GAAG,IAAG;QACR,IAAIA,GAAG,CAACC,UAAU,IAAI,CAAC,EAAE;UACvB,IAAI,CAACpB,MAAM,GAAGmB,GAAG,CAACE,OAAQ,IAAI,EAAE;UAChC,IAAI,CAACiB,YAAY,GAAGnB,GAAG,CAACoB,UAAW;UACnC,IAAI,CAACC,oBAAoB,EAAE;QAC7B;MACF,CAAC,CAAC,CACH;IACH,CAAC,MAAM;MACL;MACA,IAAI,CAACxC,MAAM,GAAG,EAAE;MAChB,IAAI,CAACsC,YAAY,GAAG,CAAC;MACrB,OAAO,IAAI,CAAC1C,eAAe,CAACmC,kCAAkC,CAAC;QAC7DC,IAAI,EAAE;UACJC,SAAS,EAAE,CAAC;UACZC,QAAQ,EAAE,CAAC;UACXE,YAAY,EAAE,CAAC,CAAC;UAAE;UAClBC,YAAY,EAAE1D,eAAe,CAACwB;;OAEjC,CAAC,CAACe,IAAI,CACL7C,GAAG,CAAC,MAAK;QACP,IAAI,CAAC2B,MAAM,GAAG,EAAE;QAChB,IAAI,CAACsC,YAAY,GAAG,CAAC;MACvB,CAAC,CAAC,CACH;IACH;EACF;EAEAG,WAAWA,CAACZ,SAAiB;IAC3B;IACA,IAAI,CAACL,eAAe,CAACK,SAAS,CAAC,CAACJ,SAAS,EAAE;EAC7C;EACAiB,cAAcA,CAACC,WAAmB;IAChC,IAAI,CAACrB,mBAAmB,GAAGqB,WAAW;IACtC,IAAI,CAACnB,eAAe,CAAC,CAAC,CAAC,CAACC,SAAS,EAAE;EACrC;EAAEmB,eAAeA,CAAC3D,QAAyB;IACzC,IAAI,CAACiB,gBAAgB,GAAGjB,QAAQ;IAChC,IAAI,CAACmB,kBAAkB,GAAG,IAAI,CAAC,CAAC;IAChC,IAAI,CAACoB,eAAe,CAAC,CAAC,CAAC,CAACC,SAAS,EAAE;EACrC;EAAEoB,MAAMA,CAACC,GAAQ,EAAEC,IAA0D;IAC3E;IACA,IAAI,CAACA,IAAI,IAAI,CAAC,IAAI,CAAC3C,kBAAkB,EAAE;MACrC,IAAI,CAACN,OAAO,CAACkD,YAAY,CAAC,UAAU,CAAC;MACrC;IACF;IAEA,IAAI,CAACrC,YAAY,GAAG,EAAE;IACtB,IAAI,CAACjB,aAAa,CAACuD,IAAI,CAACH,GAAG,CAAC;IAC5B,IAAI,CAAClC,MAAM,GAAG,KAAK;IAEnB,IAAI,CAAC,CAACmC,IAAI,EAAE;MACV;MACA,IAAI,CAACrC,aAAa,GAAG,IAAI;MACzB,IAAI,CAACF,oBAAoB,GAAG,CAAC,GAAG,IAAI,CAACR,MAAM,CAAC;MAC5C,IAAI,CAACS,iBAAiB,GAAG,IAAI,CAACT,MAAM,CAACkD,SAAS,CAACC,GAAG,IAAIA,GAAG,CAACC,GAAG,KAAKL,IAAI,CAACK,GAAG,CAAC;MAC3E,IAAI,IAAI,CAAC3C,iBAAiB,KAAK,CAAC,CAAC,EAAE;QACjC,IAAI,CAACA,iBAAiB,GAAG,CAAC;MAC5B;MACA,IAAI,CAACF,mBAAmB,GAAG,IAAI,CAAC8C,sBAAsB,EAAE;IAC1D,CAAC,MAAM;MACL;MACA,IAAI,CAAC3C,aAAa,GAAG,KAAK;MAC1B,IAAI,CAACF,oBAAoB,GAAG,EAAE;MAC9B,IAAI,CAACC,iBAAiB,GAAG,CAAC;MAC1B,IAAI,CAACF,mBAAmB,GAAG,EAAE;MAC7B,IAAI,CAACI,YAAY,GAAG,EAAE;IACxB;EACF;EACA2C,aAAaA,CAACR,GAAQ,EAAEC,IAA0D;IAChF;IACA,IAAI,CAAC,IAAI,CAAC7C,gBAAgB,EAAE;MAC1B,IAAI,CAACJ,OAAO,CAACkD,YAAY,CAAC,UAAU,CAAC;MACrC;IACF;IAEA,IAAI,CAAC,CAACD,IAAI,IAAIA,IAAI,CAACK,GAAG,EAAE;MACtB,IAAI,CAAC1D,aAAa,CAACuD,IAAI,CAACH,GAAG,CAAC;MAC5B,IAAI,CAAClC,MAAM,GAAG,IAAI;MAClB,IAAI,CAAC2C,eAAe,GAAGR,IAAI,CAACK,GAAG;MAC/B,IAAI,CAACzC,YAAY,GAAG,EAAE;IACxB;EACF;EAEA6C,UAAUA,CAACC,KAAU;IACnB,IAAI,CAAC9D,KAAK,CAACmC,KAAK,EAAE;IAClB,MAAM4B,OAAO,GAAG,IAAIrD,GAAG,EAAE;IACzB,KAAK,MAAM0C,IAAI,IAAIU,KAAK,EAAE;MACxB,IAAIC,OAAO,CAACC,GAAG,CAACZ,IAAI,CAACa,IAAI,CAAC,EAAE;QAC1B,IAAI,CAACjE,KAAK,CAACkE,eAAe,CAAC,QAAQ,CAAC;QACpC;MACF;MACAH,OAAO,CAACI,GAAG,CAACf,IAAI,CAACa,IAAI,CAAC;IACxB;EACF;EAEAG,QAAQA,CAACjB,GAAQ,GACjB;EACAkB,WAAWA,CAACC,KAAU;IACpB,KAAK,IAAIC,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGD,KAAK,CAACE,MAAM,CAACC,KAAK,CAACC,MAAM,EAAEH,KAAK,EAAE,EAAE;MAC9D,MAAMI,IAAI,GAAGL,KAAK,CAACE,MAAM,CAACC,KAAK,CAACF,KAAK,CAAC;MACtC,IAAII,IAAI,EAAE;QACR;QACA,IAAIA,IAAI,CAACC,IAAI,KAAK,iBAAiB,IAAID,IAAI,CAACV,IAAI,CAACY,WAAW,EAAE,CAACC,QAAQ,CAAC,MAAM,CAAC,EAAE;UAC/E,IAAI,CAACC,cAAc,CAACJ,IAAI,CAAC;QAC3B,CAAC,MAAM;UACL,IAAI,CAACK,sBAAsB,CAACL,IAAI,CAAC;QACnC;MACF;IACF;IACA;IACAL,KAAK,CAACE,MAAM,CAAC7E,KAAK,GAAG,IAAI;EAC3B;EAEAoF,cAAcA,CAACE,OAAa;IAC1B,MAAMC,GAAG,GAAG,IAAInG,KAAK,EAAE;IAEvBmG,GAAG,CAACC,SAAS,CAACF,OAAO,CAAC,CAACG,IAAI,CAAEC,QAAQ,IAAI;MACvC,MAAMC,UAAU,GAAmB,EAAE;MAAED,QAAQ,CAACE,OAAO,CAAC,CAACC,YAAY,EAAEb,IAAI,KAAI;QAC7E;QACA,IAAI,CAACA,IAAI,CAACc,GAAG,IAAI,IAAI,CAACC,WAAW,CAACF,YAAY,CAAC,EAAE;UAC/CF,UAAU,CAACK,IAAI,CACbhB,IAAI,CAACiB,KAAK,CAAC,MAAM,CAAC,CAACR,IAAI,CAAES,IAAI,IAAI;YAC/B;YACA,MAAMC,QAAQ,GAAGN,YAAY,CAACO,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,EAAE,IAAIR,YAAY,CAACO,KAAK,CAAC,IAAI,CAAC,CAACC,GAAG,EAAE,IAAIR,YAAY;YAChG;YACA,MAAMS,SAAS,GAAG,IAAIC,IAAI,CAAC,CAACL,IAAI,CAAC,EAAEC,QAAQ,EAAE;cAAElB,IAAI,EAAE,IAAI,CAACuB,gBAAgB,CAACX,YAAY;YAAC,CAAE,CAAC;YAC3F,OAAO,IAAI,CAACY,uBAAuB,CAACH,SAAS,EAAEH,QAAQ,CAAC;UAC1D,CAAC,CAAC,CACH;QACH;MACF,CAAC,CAAC;MAEF;MACAO,OAAO,CAACC,GAAG,CAAChB,UAAU,CAAC,CAACF,IAAI,CAAC,MAAK;QAChC,IAAI,CAACjF,OAAO,CAACoG,aAAa,CAAC,iBAAiBjB,UAAU,CAACZ,MAAM,MAAM,CAAC;MACtE,CAAC,CAAC,CAAC8B,KAAK,CAAEC,KAAK,IAAI;QACjBC,OAAO,CAACD,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;QAC3C,IAAI,CAACtG,OAAO,CAACkD,YAAY,CAAC,oBAAoB,CAAC;MACjD,CAAC,CAAC;IAEJ,CAAC,CAAC,CAACmD,KAAK,CAAEC,KAAK,IAAI;MACjBC,OAAO,CAACD,KAAK,CAAC,iBAAiB,EAAEA,KAAK,CAAC;MACvC,IAAI,CAACtG,OAAO,CAACkD,YAAY,CAAC,uBAAuB,CAAC;IACpD,CAAC,CAAC;EACJ;EAEA2B,sBAAsBA,CAACL,IAAU;IAC/B,IAAIgC,MAAM,GAAG,IAAIC,UAAU,EAAE;IAC7BD,MAAM,CAACE,aAAa,CAAClC,IAAI,CAAC;IAC1BgC,MAAM,CAACG,MAAM,GAAG,MAAK;MACnB,IAAIC,SAAS,GAAWJ,MAAM,CAACK,MAAgB;MAC/C,IAAI,CAACD,SAAS,EAAE;QACd;MACF;MACA,IAAI,CAACE,cAAc,CAACtC,IAAI,EAAEoC,SAAS,CAAC;IACtC,CAAC;EACH;EAEAX,uBAAuBA,CAACzB,IAAU,EAAEuC,YAAoB;IACtD,OAAO,IAAIb,OAAO,CAAC,CAACc,OAAO,EAAEC,MAAM,KAAI;MACrC,IAAIT,MAAM,GAAG,IAAIC,UAAU,EAAE;MAC7BD,MAAM,CAACE,aAAa,CAAClC,IAAI,CAAC;MAC1BgC,MAAM,CAACG,MAAM,GAAG,MAAK;QACnB,IAAIC,SAAS,GAAWJ,MAAM,CAACK,MAAgB;QAC/C,IAAI,CAACD,SAAS,EAAE;UACdK,MAAM,CAAC,UAAU,CAAC;UAClB;QACF;QACA,IAAI,CAACH,cAAc,CAACtC,IAAI,EAAEoC,SAAS,EAAEG,YAAY,CAAC;QAClDC,OAAO,EAAE;MACX,CAAC;MACDR,MAAM,CAACU,OAAO,GAAG,MAAK;QACpBD,MAAM,CAAC,aAAa,CAAC;MACvB,CAAC;IACH,CAAC,CAAC;EACJ;EACAH,cAAcA,CAACtC,IAAU,EAAEoC,SAAiB,EAAEG,YAAqB;IACjE;IACA,IAAIpB,QAAQ,GAAGoB,YAAY,IAAIvC,IAAI,CAACV,IAAI;IAExC;IACA,IAAIiD,YAAY,EAAE;MAChBpB,QAAQ,GAAGoB,YAAY,CAACnB,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,EAAE,IAAIkB,YAAY,CAACnB,KAAK,CAAC,IAAI,CAAC,CAACC,GAAG,EAAE,IAAIkB,YAAY;IAC5F;IAEA,MAAMI,wBAAwB,GAAGxB,QAAQ,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IAEvD;IACA,MAAMwB,iBAAiB,GAAG,IAAI,CAACvG,YAAY,CAACuC,SAAS,CAACiE,OAAO,IAAIA,OAAO,CAACvD,IAAI,KAAKqD,wBAAwB,CAAC;IAC3G,IAAIC,iBAAiB,KAAK,CAAC,CAAC,EAAE;MAC5B;MACA,IAAI,CAACvG,YAAY,CAACuG,iBAAiB,CAAC,GAAG;QACrC,GAAG,IAAI,CAACvG,YAAY,CAACuG,iBAAiB,CAAC;QACvCE,IAAI,EAAEV,SAAS;QACfjD,KAAK,EAAEa,IAAI;QACX+C,SAAS,EAAE,IAAI,CAACtH,eAAe,CAACuH,gBAAgB,CAAC7B,QAAQ;OAC1D;IACH,CAAC,MAAM;MACL;MACA,IAAI,CAAC9E,YAAY,CAAC2E,IAAI,CAAC;QACrBiC,EAAE,EAAE,IAAIC,IAAI,EAAE,CAACC,OAAO,EAAE,GAAGC,IAAI,CAACC,MAAM,EAAE;QACxC/D,IAAI,EAAEqD,wBAAwB;QAC9BG,IAAI,EAAEV,SAAS;QACfW,SAAS,EAAE,IAAI,CAACtH,eAAe,CAACuH,gBAAgB,CAAC7B,QAAQ,CAAC;QAC1DhC,KAAK,EAAEa;OACR,CAAC;IACJ;EACF;EAEAe,WAAWA,CAACI,QAAgB;IAC1B,MAAMmC,eAAe,GAAG,CAAC,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO,CAAC;IAC1E,MAAMP,SAAS,GAAG5B,QAAQ,CAACjB,WAAW,EAAE,CAACqD,SAAS,CAACpC,QAAQ,CAACqC,WAAW,CAAC,GAAG,CAAC,CAAC;IAC7E,OAAOF,eAAe,CAACG,QAAQ,CAACV,SAAS,CAAC;EAC5C;EAEAvB,gBAAgBA,CAACL,QAAgB;IAC/B,MAAM4B,SAAS,GAAG5B,QAAQ,CAACjB,WAAW,EAAE,CAACqD,SAAS,CAACpC,QAAQ,CAACqC,WAAW,CAAC,GAAG,CAAC,CAAC;IAC7E,QAAQT,SAAS;MACf,KAAK,MAAM;MACX,KAAK,OAAO;QACV,OAAO,YAAY;MACrB,KAAK,MAAM;QACT,OAAO,WAAW;MACpB,KAAK,MAAM;QACT,OAAO,WAAW;MACpB,KAAK,MAAM;QACT,OAAO,WAAW;MACpB,KAAK,OAAO;QACV,OAAO,YAAY;MACrB;QACE,OAAO,YAAY;IACvB;EACF;EAEAW,WAAWA,CAACC,SAAiB;IAC3B,IAAI,CAACtH,YAAY,GAAG,IAAI,CAACA,YAAY,CAACuH,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACZ,EAAE,IAAIU,SAAS,CAAC;EACtE;EACAG,WAAWA,CAACtF,GAAQ;IAClB,IAAI,CAAC,IAAI,CAAClC,MAAM,EAAE;MAChB,MAAM6C,KAAK,GAAG,IAAI,CAAC9C,YAAY,CAAC0H,GAAG,CAACF,CAAC,IAAIA,CAAC,CAAC1E,KAAK,CAAC;MACjD,IAAI,CAACD,UAAU,CAACC,KAAK,CAAC;MACtB,IAAI,IAAI,CAAC9D,KAAK,CAAC2I,aAAa,CAACjE,MAAM,GAAG,CAAC,EAAE;QACvC,IAAI,CAACvE,OAAO,CAACyI,aAAa,CAAC,IAAI,CAAC5I,KAAK,CAAC2I,aAAa,CAAC;QACpD;MACF,CAAC,CAAM;MACP,MAAME,aAAa,GAAG,IAAI,CAAC5I,eAAe,CAAC6I,oCAAoC,CAAC;QAC9EzG,IAAI,EAAE;UACJI,YAAY,EAAE,IAAI,CAACd,mBAAmB;UACtCoH,KAAK,EAAE,IAAI,CAACxI,gBAAgB,KAAKvB,eAAe,CAACkC,iBAAiB,GAAG,SAAS,GAAG,aAAa;UAC9F4C,KAAK,EAAEA,KAAK;UACZpB,YAAY,EAAE,IAAI,CAACnC;;OAEtB,CAAC;MAAEsI,aAAa,CAACtH,IAAI,CACpB7C,GAAG,CAAC8C,GAAG,IAAG;QACR,IAAIA,GAAG,CAACC,UAAU,IAAI,CAAC,EAAE;UACvB,IAAI,CAACtB,OAAO,CAACoG,aAAa,CAAC,MAAM,CAAC;UAClC,IAAI,CAACvF,YAAY,GAAG,EAAE;QACxB,CAAC,MAAM;UACL,IAAI,CAACb,OAAO,CAACkD,YAAY,CAAC7B,GAAG,CAACwH,OAAQ,CAAC;QACzC;QACA7F,GAAG,CAAC8F,KAAK,EAAE;QACX,IAAI,CAACC,iBAAiB,EAAE;MAC1B,CAAC,CAAC,EACF1K,SAAS,CAAEgD,GAAG,IAAKA,GAAG,CAACC,UAAW,IAAI,CAAC,GAAG,IAAI,CAACI,eAAe,CAAC,CAAC,CAAC,GAAGpD,EAAE,CAAC,IAAI,CAAC,CAAC,CAC9E,CAACqD,SAAS,EAAE;IACf,CAAC,MACI;MACH,IAAI,IAAI,CAACd,YAAY,CAAC0D,MAAM,GAAG,CAAC,IAAI,IAAI,CAAC1D,YAAY,CAAC,CAAC,CAAC,CAAC8C,KAAK,EAAE;QAC9D;QACA,MAAMqF,aAAa,GAAG,IAAI,CAAClJ,eAAe,CAACmJ,gCAAgC,CAAC;UAC1E/G,IAAI,EAAE;YACJgH,YAAY,EAAE,IAAI,CAAC1H,mBAAmB;YACtC2H,UAAU,EAAE,IAAI,CAAC1F,eAAe;YAChCE,KAAK,EAAE,IAAI,CAAC9C,YAAY,CAAC,CAAC,CAAC,CAAC8C;;SAE/B,CAAC;QAAEqF,aAAa,CAAC5H,IAAI,CACpB7C,GAAG,CAAC8C,GAAG,IAAG;UACR,IAAIA,GAAG,CAACC,UAAU,IAAI,CAAC,EAAE;YACvB,IAAI,CAACtB,OAAO,CAACoG,aAAa,CAAC,MAAM,CAAC;YAClC,IAAI,CAACvF,YAAY,GAAG,EAAE;UACxB,CAAC,MAAM;YACL,IAAI,CAACb,OAAO,CAACkD,YAAY,CAAC7B,GAAG,CAACwH,OAAQ,CAAC;UACzC;UACA7F,GAAG,CAAC8F,KAAK,EAAE;UACX,IAAI,CAACC,iBAAiB,EAAE;QAC1B,CAAC,CAAC,EACF1K,SAAS,CAAEgD,GAAG,IAAKA,GAAG,CAACC,UAAW,IAAI,CAAC,GAAG,IAAI,CAACI,eAAe,CAAC,CAAC,CAAC,GAAGpD,EAAE,CAAC,IAAI,CAAC,CAAC,CAC9E,CAACqD,SAAS,EAAE;MACf;IACF;EACF;EAEAyH,UAAUA,CAACjF,KAAU,EAAEC,KAAa;IAClC,IAAIsB,IAAI,GAAG,IAAI,CAAC7E,YAAY,CAACuD,KAAK,CAAC,CAACT,KAAK,CAAC0F,KAAK,CAAC,CAAC,EAAE,IAAI,CAACxI,YAAY,CAACuD,KAAK,CAAC,CAACT,KAAK,CAAC1E,IAAI,EAAE,IAAI,CAAC4B,YAAY,CAACuD,KAAK,CAAC,CAACT,KAAK,CAACc,IAAI,CAAC;IAC5H,IAAI6E,OAAO,GAAG,IAAIvD,IAAI,CAAC,CAACL,IAAI,CAAC,EAAE,GAAGvB,KAAK,CAACE,MAAM,CAAC7E,KAAK,GAAG,GAAG,GAAG,IAAI,CAACqB,YAAY,CAACuD,KAAK,CAAC,CAACmD,SAAS,EAAE,EAAE;MAAE9C,IAAI,EAAE,IAAI,CAAC5D,YAAY,CAACuD,KAAK,CAAC,CAACT,KAAK,CAACc;IAAI,CAAE,CAAC;IAEjJ,IAAI,CAAC5D,YAAY,CAACuD,KAAK,CAAC,CAACT,KAAK,GAAG2F,OAAO;EAC1C;EACA;EACAC,gBAAgBA,CAACC,MAAc;IAC7B,IAAI,IAAI,CAACxK,aAAa,CAAC6E,GAAG,CAAC2F,MAAM,CAAC,EAAE;MAClC,IAAI,CAACxK,aAAa,CAACyK,MAAM,CAACD,MAAM,CAAC;IACnC,CAAC,MAAM;MACL,IAAI,CAACxK,aAAa,CAACgF,GAAG,CAACwF,MAAM,CAAC;IAChC;IACA,IAAI,CAAC9G,oBAAoB,EAAE;EAC7B;EAEAgH,eAAeA,CAACC,OAAgB;IAC9B,IAAI,CAACnJ,SAAS,GAAGmJ,OAAO;IACxB,IAAIA,OAAO,EAAE;MACX;MACA,IAAI,CAACzJ,MAAM,CAACkF,OAAO,CAACnC,IAAI,IAAG;QACzB,IAAIA,IAAI,CAACK,GAAG,EAAE;UACZ,IAAI,CAACtE,aAAa,CAACgF,GAAG,CAACf,IAAI,CAACK,GAAG,CAAC;QAClC;MACF,CAAC,CAAC;IACJ,CAAC,MAAM;MACL;MACA,MAAMsG,cAAc,GAAG,IAAI,CAAC1J,MAAM,CAACqI,GAAG,CAACtF,IAAI,IAAIA,IAAI,CAACK,GAAG,CAAC,CAAC8E,MAAM,CAACX,EAAE,IAAIA,EAAE,KAAKoC,SAAS,CAAa;MACnGD,cAAc,CAACxE,OAAO,CAACqC,EAAE,IAAI,IAAI,CAACzI,aAAa,CAACyK,MAAM,CAAChC,EAAE,CAAC,CAAC;IAC7D;EACF;EAEA/E,oBAAoBA,CAAA;IAClB,MAAMkH,cAAc,GAAG,IAAI,CAAC1J,MAAM,CAACqI,GAAG,CAACtF,IAAI,IAAIA,IAAI,CAACK,GAAG,CAAC,CAAC8E,MAAM,CAACX,EAAE,IAAIA,EAAE,KAAKoC,SAAS,CAAa;IACnG,IAAI,CAACrJ,SAAS,GAAGoJ,cAAc,CAACrF,MAAM,GAAG,CAAC,IAAIqF,cAAc,CAACE,KAAK,CAACrC,EAAE,IAAI,IAAI,CAACzI,aAAa,CAAC6E,GAAG,CAAC4D,EAAE,CAAC,CAAC;EACtG;EACA;EACAsC,WAAWA,CAAA;IACT,IAAI,IAAI,CAAC/K,aAAa,CAACC,IAAI,KAAK,CAAC,EAAE;MACjC,IAAI,CAACe,OAAO,CAACkD,YAAY,CAAC,WAAW,CAAC;MACtC;IACF;IAEA,MAAM8G,WAAW,GAAGC,KAAK,CAACC,IAAI,CAAC,IAAI,CAAClL,aAAa,CAAC;IAElD;IACA,IAAImL,OAAO,CAAC,YAAYH,WAAW,CAACzF,MAAM,QAAQ,CAAC,EAAE;MACnD,IAAI,CAACzE,eAAe,CAACsK,gCAAgC,CAAC;QACpDlI,IAAI,EAAE8H;OACP,CAAC,CAAC5I,IAAI,CACL7C,GAAG,CAAC8C,GAAG,IAAG;QACR,IAAIA,GAAG,CAACC,UAAU,KAAK,CAAC,EAAE;UACxB,IAAI,CAACtB,OAAO,CAACoG,aAAa,CAAC,QAAQ4D,WAAW,CAACzF,MAAM,MAAM,CAAC;UAC5D;UACA,IAAI,CAACvF,aAAa,CAACgD,KAAK,EAAE;UAC1B,IAAI,CAACxB,SAAS,GAAG,KAAK;QACxB,CAAC,MAAM;UACL,IAAI,CAACR,OAAO,CAACkD,YAAY,CAAC7B,GAAG,CAACwH,OAAO,IAAI,MAAM,CAAC;QAClD;MACF,CAAC,CAAC,EACFxK,SAAS,CAAEgD,GAAG,IAAKA,GAAG,CAACC,UAAU,KAAK,CAAC,GAAG,IAAI,CAACI,eAAe,CAAC,IAAI,CAACK,SAAS,CAAC,GAAGzD,EAAE,CAAC,IAAI,CAAC,CAAC,CAC3F,CAACqD,SAAS,EAAE;IACf;EACF;EACA;EACA0I,cAAcA,CAACb,MAAc;IAC3B,OAAO,IAAI,CAACxK,aAAa,CAAC6E,GAAG,CAAC2F,MAAM,CAAC;EACvC;EAEA;EACAjG,sBAAsBA,CAAA;IACpB,IAAI,IAAI,CAAC7C,oBAAoB,CAAC6D,MAAM,KAAK,CAAC,IAAI,IAAI,CAAC5D,iBAAiB,GAAG,CAAC,IAAI,IAAI,CAACA,iBAAiB,IAAI,IAAI,CAACD,oBAAoB,CAAC6D,MAAM,EAAE;MACtI,OAAO,EAAE;IACX;IACA,MAAM+F,YAAY,GAAG,IAAI,CAAC5J,oBAAoB,CAAC,IAAI,CAACC,iBAAiB,CAAC;IACtE;IACA,OAAO2J,YAAY,CAACC,OAAO,IAAID,YAAY,CAAC3G,KAAK,IAAI,EAAE;EACzD;EAEA6G,aAAaA,CAAA;IACX,IAAI,IAAI,CAAC9J,oBAAoB,CAAC6D,MAAM,KAAK,CAAC,EAAE;IAC5C,IAAI,CAAC5D,iBAAiB,GAAG,IAAI,CAACA,iBAAiB,GAAG,CAAC,GAAG,IAAI,CAACA,iBAAiB,GAAG,CAAC,GAAG,IAAI,CAACD,oBAAoB,CAAC6D,MAAM,GAAG,CAAC;IACvH,IAAI,CAAC9D,mBAAmB,GAAG,IAAI,CAAC8C,sBAAsB,EAAE;EAC1D;EACAkH,SAASA,CAAA;IACP,IAAI,IAAI,CAAC/J,oBAAoB,CAAC6D,MAAM,KAAK,CAAC,EAAE;IAC5C,IAAI,CAAC5D,iBAAiB,GAAG,IAAI,CAACA,iBAAiB,GAAG,IAAI,CAACD,oBAAoB,CAAC6D,MAAM,GAAG,CAAC,GAAG,IAAI,CAAC5D,iBAAiB,GAAG,CAAC,GAAG,CAAC;IACvH,IAAI,CAACF,mBAAmB,GAAG,IAAI,CAAC8C,sBAAsB,EAAE;EAC1D;EAEAmH,SAASA,CAACtG,KAAa;IACrB,IAAIA,KAAK,IAAI,CAAC,IAAIA,KAAK,GAAG,IAAI,CAAC1D,oBAAoB,CAAC6D,MAAM,EAAE;MAC1D,IAAI,CAAC5D,iBAAiB,GAAGyD,KAAK;MAC9B,IAAI,CAAC3D,mBAAmB,GAAG,IAAI,CAAC8C,sBAAsB,EAAE;IAC1D;EACF;EAEA,IAAIoH,gBAAgBA,CAAA;IAClB,IAAI,IAAI,CAACjK,oBAAoB,CAAC6D,MAAM,KAAK,CAAC,IAAI,IAAI,CAAC5D,iBAAiB,GAAG,CAAC,IAAI,IAAI,CAACA,iBAAiB,IAAI,IAAI,CAACD,oBAAoB,CAAC6D,MAAM,EAAE;MACtI,OAAO,IAAI;IACb;IACA,OAAO,IAAI,CAAC7D,oBAAoB,CAAC,IAAI,CAACC,iBAAiB,CAAC;EAC1D;EACA,IAAIiK,YAAYA,CAAA;IACd,IAAI,IAAI,CAAClK,oBAAoB,CAAC6D,MAAM,KAAK,CAAC,EAAE,OAAO,EAAE;IACrD,OAAO,GAAG,IAAI,CAAC5D,iBAAiB,GAAG,CAAC,MAAM,IAAI,CAACD,oBAAoB,CAAC6D,MAAM,EAAE;EAC9E;EACA;EACAwE,iBAAiBA,CAAA;IACf,IAAI,CAACnI,aAAa,GAAG,KAAK;IAC1B,IAAI,CAACF,oBAAoB,GAAG,EAAE;IAC9B,IAAI,CAACC,iBAAiB,GAAG,CAAC;IAC1B,IAAI,CAACF,mBAAmB,GAAG,EAAE;EAC/B;EAEA;EAEAoK,mBAAmBA,CAAC1G,KAAoB;IACtC,IAAI,CAAC,IAAI,CAACvD,aAAa,IAAI,IAAI,CAACF,oBAAoB,CAAC6D,MAAM,IAAI,CAAC,EAAE;IAElE,QAAQJ,KAAK,CAAC2G,GAAG;MACf,KAAK,WAAW;QACd3G,KAAK,CAAC4G,cAAc,EAAE;QACtB,IAAI,CAACP,aAAa,EAAE;QACpB;MACF,KAAK,YAAY;QACfrG,KAAK,CAAC4G,cAAc,EAAE;QACtB,IAAI,CAACN,SAAS,EAAE;QAChB;MACF,KAAK,QAAQ;QACXtG,KAAK,CAAC4G,cAAc,EAAE;QACtB;QACA;IACJ;EACF;CACD;AAlBCC,UAAA,EADC7M,YAAY,CAAC,kBAAkB,EAAE,CAAC,QAAQ,CAAC,CAAC,C,kEAkB5C;AAjhBUW,wBAAwB,GAAAkM,UAAA,EAZpC9M,SAAS,CAAC;EACT+M,QAAQ,EAAE,sBAAsB;EAChCC,WAAW,EAAE,mCAAmC;EAChDC,SAAS,EAAE,CAAC,mCAAmC,CAAC;EAChDC,UAAU,EAAE,IAAI;EAAGC,OAAO,EAAE,CAC1BjN,YAAY,EACZM,YAAY,EACZF,YAAY,EACZC,eAAe;CAElB,CAAC,C,EAEWK,wBAAwB,CAkhBpC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}