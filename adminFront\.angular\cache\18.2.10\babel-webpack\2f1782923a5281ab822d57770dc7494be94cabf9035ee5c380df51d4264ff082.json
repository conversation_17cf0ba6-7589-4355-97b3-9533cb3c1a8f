{"ast": null, "code": "//! moment.js locale configuration\n//! locale : Tibetan [bo]\n//! author : <PERSON><PERSON><PERSON><PERSON> <PERSON> : https://github.com/vajradog\n\n;\n(function (global, factory) {\n  typeof exports === 'object' && typeof module !== 'undefined' && typeof require === 'function' ? factory(require('../moment')) : typeof define === 'function' && define.amd ? define(['../moment'], factory) : factory(global.moment);\n})(this, function (moment) {\n  'use strict';\n\n  //! moment.js locale configuration\n  var symbolMap = {\n      1: '༡',\n      2: '༢',\n      3: '༣',\n      4: '༤',\n      5: '༥',\n      6: '༦',\n      7: '༧',\n      8: '༨',\n      9: '༩',\n      0: '༠'\n    },\n    numberMap = {\n      '༡': '1',\n      '༢': '2',\n      '༣': '3',\n      '༤': '4',\n      '༥': '5',\n      '༦': '6',\n      '༧': '7',\n      '༨': '8',\n      '༩': '9',\n      '༠': '0'\n    };\n  var bo = moment.defineLocale('bo', {\n    months: 'ཟླ་བ་དང་པོ_ཟླ་བ་གཉིས་པ_ཟླ་བ་གསུམ་པ_ཟླ་བ་བཞི་པ_ཟླ་བ་ལྔ་པ_ཟླ་བ་དྲུག་པ_ཟླ་བ་བདུན་པ_ཟླ་བ་བརྒྱད་པ_ཟླ་བ་དགུ་པ_ཟླ་བ་བཅུ་པ_ཟླ་བ་བཅུ་གཅིག་པ_ཟླ་བ་བཅུ་གཉིས་པ'.split('_'),\n    monthsShort: 'ཟླ་1_ཟླ་2_ཟླ་3_ཟླ་4_ཟླ་5_ཟླ་6_ཟླ་7_ཟླ་8_ཟླ་9_ཟླ་10_ཟླ་11_ཟླ་12'.split('_'),\n    monthsShortRegex: /^(ཟླ་\\d{1,2})/,\n    monthsParseExact: true,\n    weekdays: 'གཟའ་ཉི་མ་_གཟའ་ཟླ་བ་_གཟའ་མིག་དམར་_གཟའ་ལྷག་པ་_གཟའ་ཕུར་བུ_གཟའ་པ་སངས་_གཟའ་སྤེན་པ་'.split('_'),\n    weekdaysShort: 'ཉི་མ་_ཟླ་བ་_མིག་དམར་_ལྷག་པ་_ཕུར་བུ_པ་སངས་_སྤེན་པ་'.split('_'),\n    weekdaysMin: 'ཉི_ཟླ_མིག_ལྷག_ཕུར_སངས_སྤེན'.split('_'),\n    longDateFormat: {\n      LT: 'A h:mm',\n      LTS: 'A h:mm:ss',\n      L: 'DD/MM/YYYY',\n      LL: 'D MMMM YYYY',\n      LLL: 'D MMMM YYYY, A h:mm',\n      LLLL: 'dddd, D MMMM YYYY, A h:mm'\n    },\n    calendar: {\n      sameDay: '[དི་རིང] LT',\n      nextDay: '[སང་ཉིན] LT',\n      nextWeek: '[བདུན་ཕྲག་རྗེས་མ], LT',\n      lastDay: '[ཁ་སང] LT',\n      lastWeek: '[བདུན་ཕྲག་མཐའ་མ] dddd, LT',\n      sameElse: 'L'\n    },\n    relativeTime: {\n      future: '%s ལ་',\n      past: '%s སྔན་ལ',\n      s: 'ལམ་སང',\n      ss: '%d སྐར་ཆ།',\n      m: 'སྐར་མ་གཅིག',\n      mm: '%d སྐར་མ',\n      h: 'ཆུ་ཚོད་གཅིག',\n      hh: '%d ཆུ་ཚོད',\n      d: 'ཉིན་གཅིག',\n      dd: '%d ཉིན་',\n      M: 'ཟླ་བ་གཅིག',\n      MM: '%d ཟླ་བ',\n      y: 'ལོ་གཅིག',\n      yy: '%d ལོ'\n    },\n    preparse: function (string) {\n      return string.replace(/[༡༢༣༤༥༦༧༨༩༠]/g, function (match) {\n        return numberMap[match];\n      });\n    },\n    postformat: function (string) {\n      return string.replace(/\\d/g, function (match) {\n        return symbolMap[match];\n      });\n    },\n    meridiemParse: /མཚན་མོ|ཞོགས་ཀས|ཉིན་གུང|དགོང་དག|མཚན་མོ/,\n    meridiemHour: function (hour, meridiem) {\n      if (hour === 12) {\n        hour = 0;\n      }\n      if (meridiem === 'མཚན་མོ' && hour >= 4 || meridiem === 'ཉིན་གུང' && hour < 5 || meridiem === 'དགོང་དག') {\n        return hour + 12;\n      } else {\n        return hour;\n      }\n    },\n    meridiem: function (hour, minute, isLower) {\n      if (hour < 4) {\n        return 'མཚན་མོ';\n      } else if (hour < 10) {\n        return 'ཞོགས་ཀས';\n      } else if (hour < 17) {\n        return 'ཉིན་གུང';\n      } else if (hour < 20) {\n        return 'དགོང་དག';\n      } else {\n        return 'མཚན་མོ';\n      }\n    },\n    week: {\n      dow: 0,\n      // Sunday is the first day of the week.\n      doy: 6 // The week that contains Jan 6th is the first week of the year.\n    }\n  });\n  return bo;\n});", "map": {"version": 3, "names": ["global", "factory", "exports", "module", "require", "define", "amd", "moment", "symbolMap", "numberMap", "bo", "defineLocale", "months", "split", "monthsShort", "monthsShortRegex", "monthsParseExact", "weekdays", "weekdaysShort", "weekdaysMin", "longDateFormat", "LT", "LTS", "L", "LL", "LLL", "LLLL", "calendar", "sameDay", "nextDay", "nextWeek", "lastDay", "lastWeek", "same<PERSON><PERSON><PERSON>", "relativeTime", "future", "past", "s", "ss", "m", "mm", "h", "hh", "d", "dd", "M", "MM", "y", "yy", "preparse", "string", "replace", "match", "postformat", "meridiemParse", "meridiemHour", "hour", "meridiem", "minute", "isLower", "week", "dow", "doy"], "sources": ["C:/Users/<USER>/Documents/salechange-product/adminFront/node_modules/moment/locale/bo.js"], "sourcesContent": ["//! moment.js locale configuration\n//! locale : Tibetan [bo]\n//! author : <PERSON><PERSON><PERSON><PERSON> <PERSON> : https://github.com/vajradog\n\n;(function (global, factory) {\n   typeof exports === 'object' && typeof module !== 'undefined'\n       && typeof require === 'function' ? factory(require('../moment')) :\n   typeof define === 'function' && define.amd ? define(['../moment'], factory) :\n   factory(global.moment)\n}(this, (function (moment) { 'use strict';\n\n    //! moment.js locale configuration\n\n    var symbolMap = {\n            1: '༡',\n            2: '༢',\n            3: '༣',\n            4: '༤',\n            5: '༥',\n            6: '༦',\n            7: '༧',\n            8: '༨',\n            9: '༩',\n            0: '༠',\n        },\n        numberMap = {\n            '༡': '1',\n            '༢': '2',\n            '༣': '3',\n            '༤': '4',\n            '༥': '5',\n            '༦': '6',\n            '༧': '7',\n            '༨': '8',\n            '༩': '9',\n            '༠': '0',\n        };\n\n    var bo = moment.defineLocale('bo', {\n        months: 'ཟླ་བ་དང་པོ_ཟླ་བ་གཉིས་པ_ཟླ་བ་གསུམ་པ_ཟླ་བ་བཞི་པ_ཟླ་བ་ལྔ་པ_ཟླ་བ་དྲུག་པ_ཟླ་བ་བདུན་པ_ཟླ་བ་བརྒྱད་པ_ཟླ་བ་དགུ་པ_ཟླ་བ་བཅུ་པ_ཟླ་བ་བཅུ་གཅིག་པ_ཟླ་བ་བཅུ་གཉིས་པ'.split(\n            '_'\n        ),\n        monthsShort:\n            'ཟླ་1_ཟླ་2_ཟླ་3_ཟླ་4_ཟླ་5_ཟླ་6_ཟླ་7_ཟླ་8_ཟླ་9_ཟླ་10_ཟླ་11_ཟླ་12'.split(\n                '_'\n            ),\n        monthsShortRegex: /^(ཟླ་\\d{1,2})/,\n        monthsParseExact: true,\n        weekdays:\n            'གཟའ་ཉི་མ་_གཟའ་ཟླ་བ་_གཟའ་མིག་དམར་_གཟའ་ལྷག་པ་_གཟའ་ཕུར་བུ_གཟའ་པ་སངས་_གཟའ་སྤེན་པ་'.split(\n                '_'\n            ),\n        weekdaysShort: 'ཉི་མ་_ཟླ་བ་_མིག་དམར་_ལྷག་པ་_ཕུར་བུ_པ་སངས་_སྤེན་པ་'.split(\n            '_'\n        ),\n        weekdaysMin: 'ཉི_ཟླ_མིག_ལྷག_ཕུར_སངས_སྤེན'.split('_'),\n        longDateFormat: {\n            LT: 'A h:mm',\n            LTS: 'A h:mm:ss',\n            L: 'DD/MM/YYYY',\n            LL: 'D MMMM YYYY',\n            LLL: 'D MMMM YYYY, A h:mm',\n            LLLL: 'dddd, D MMMM YYYY, A h:mm',\n        },\n        calendar: {\n            sameDay: '[དི་རིང] LT',\n            nextDay: '[སང་ཉིན] LT',\n            nextWeek: '[བདུན་ཕྲག་རྗེས་མ], LT',\n            lastDay: '[ཁ་སང] LT',\n            lastWeek: '[བདུན་ཕྲག་མཐའ་མ] dddd, LT',\n            sameElse: 'L',\n        },\n        relativeTime: {\n            future: '%s ལ་',\n            past: '%s སྔན་ལ',\n            s: 'ལམ་སང',\n            ss: '%d སྐར་ཆ།',\n            m: 'སྐར་མ་གཅིག',\n            mm: '%d སྐར་མ',\n            h: 'ཆུ་ཚོད་གཅིག',\n            hh: '%d ཆུ་ཚོད',\n            d: 'ཉིན་གཅིག',\n            dd: '%d ཉིན་',\n            M: 'ཟླ་བ་གཅིག',\n            MM: '%d ཟླ་བ',\n            y: 'ལོ་གཅིག',\n            yy: '%d ལོ',\n        },\n        preparse: function (string) {\n            return string.replace(/[༡༢༣༤༥༦༧༨༩༠]/g, function (match) {\n                return numberMap[match];\n            });\n        },\n        postformat: function (string) {\n            return string.replace(/\\d/g, function (match) {\n                return symbolMap[match];\n            });\n        },\n        meridiemParse: /མཚན་མོ|ཞོགས་ཀས|ཉིན་གུང|དགོང་དག|མཚན་མོ/,\n        meridiemHour: function (hour, meridiem) {\n            if (hour === 12) {\n                hour = 0;\n            }\n            if (\n                (meridiem === 'མཚན་མོ' && hour >= 4) ||\n                (meridiem === 'ཉིན་གུང' && hour < 5) ||\n                meridiem === 'དགོང་དག'\n            ) {\n                return hour + 12;\n            } else {\n                return hour;\n            }\n        },\n        meridiem: function (hour, minute, isLower) {\n            if (hour < 4) {\n                return 'མཚན་མོ';\n            } else if (hour < 10) {\n                return 'ཞོགས་ཀས';\n            } else if (hour < 17) {\n                return 'ཉིན་གུང';\n            } else if (hour < 20) {\n                return 'དགོང་དག';\n            } else {\n                return 'མཚན་མོ';\n            }\n        },\n        week: {\n            dow: 0, // Sunday is the first day of the week.\n            doy: 6, // The week that contains Jan 6th is the first week of the year.\n        },\n    });\n\n    return bo;\n\n})));\n"], "mappings": "AAAA;AACA;AACA;;AAEA;AAAE,WAAUA,MAAM,EAAEC,OAAO,EAAE;EAC1B,OAAOC,OAAO,KAAK,QAAQ,IAAI,OAAOC,MAAM,KAAK,WAAW,IACrD,OAAOC,OAAO,KAAK,UAAU,GAAGH,OAAO,CAACG,OAAO,CAAC,WAAW,CAAC,CAAC,GACpE,OAAOC,MAAM,KAAK,UAAU,IAAIA,MAAM,CAACC,GAAG,GAAGD,MAAM,CAAC,CAAC,WAAW,CAAC,EAAEJ,OAAO,CAAC,GAC3EA,OAAO,CAACD,MAAM,CAACO,MAAM,CAAC;AACzB,CAAC,EAAC,IAAI,EAAG,UAAUA,MAAM,EAAE;EAAE,YAAY;;EAErC;EAEA,IAAIC,SAAS,GAAG;MACR,CAAC,EAAE,GAAG;MACN,CAAC,EAAE,GAAG;MACN,CAAC,EAAE,GAAG;MACN,CAAC,EAAE,GAAG;MACN,CAAC,EAAE,GAAG;MACN,CAAC,EAAE,GAAG;MACN,CAAC,EAAE,GAAG;MACN,CAAC,EAAE,GAAG;MACN,CAAC,EAAE,GAAG;MACN,CAAC,EAAE;IACP,CAAC;IACDC,SAAS,GAAG;MACR,GAAG,EAAE,GAAG;MACR,GAAG,EAAE,GAAG;MACR,GAAG,EAAE,GAAG;MACR,GAAG,EAAE,GAAG;MACR,GAAG,EAAE,GAAG;MACR,GAAG,EAAE,GAAG;MACR,GAAG,EAAE,GAAG;MACR,GAAG,EAAE,GAAG;MACR,GAAG,EAAE,GAAG;MACR,GAAG,EAAE;IACT,CAAC;EAEL,IAAIC,EAAE,GAAGH,MAAM,CAACI,YAAY,CAAC,IAAI,EAAE;IAC/BC,MAAM,EAAE,oJAAoJ,CAACC,KAAK,CAC9J,GACJ,CAAC;IACDC,WAAW,EACP,gEAAgE,CAACD,KAAK,CAClE,GACJ,CAAC;IACLE,gBAAgB,EAAE,eAAe;IACjCC,gBAAgB,EAAE,IAAI;IACtBC,QAAQ,EACJ,+EAA+E,CAACJ,KAAK,CACjF,GACJ,CAAC;IACLK,aAAa,EAAE,mDAAmD,CAACL,KAAK,CACpE,GACJ,CAAC;IACDM,WAAW,EAAE,4BAA4B,CAACN,KAAK,CAAC,GAAG,CAAC;IACpDO,cAAc,EAAE;MACZC,EAAE,EAAE,QAAQ;MACZC,GAAG,EAAE,WAAW;MAChBC,CAAC,EAAE,YAAY;MACfC,EAAE,EAAE,aAAa;MACjBC,GAAG,EAAE,qBAAqB;MAC1BC,IAAI,EAAE;IACV,CAAC;IACDC,QAAQ,EAAE;MACNC,OAAO,EAAE,aAAa;MACtBC,OAAO,EAAE,aAAa;MACtBC,QAAQ,EAAE,uBAAuB;MACjCC,OAAO,EAAE,WAAW;MACpBC,QAAQ,EAAE,2BAA2B;MACrCC,QAAQ,EAAE;IACd,CAAC;IACDC,YAAY,EAAE;MACVC,MAAM,EAAE,OAAO;MACfC,IAAI,EAAE,UAAU;MAChBC,CAAC,EAAE,OAAO;MACVC,EAAE,EAAE,WAAW;MACfC,CAAC,EAAE,YAAY;MACfC,EAAE,EAAE,UAAU;MACdC,CAAC,EAAE,aAAa;MAChBC,EAAE,EAAE,WAAW;MACfC,CAAC,EAAE,UAAU;MACbC,EAAE,EAAE,SAAS;MACbC,CAAC,EAAE,WAAW;MACdC,EAAE,EAAE,SAAS;MACbC,CAAC,EAAE,SAAS;MACZC,EAAE,EAAE;IACR,CAAC;IACDC,QAAQ,EAAE,SAAAA,CAAUC,MAAM,EAAE;MACxB,OAAOA,MAAM,CAACC,OAAO,CAAC,eAAe,EAAE,UAAUC,KAAK,EAAE;QACpD,OAAO3C,SAAS,CAAC2C,KAAK,CAAC;MAC3B,CAAC,CAAC;IACN,CAAC;IACDC,UAAU,EAAE,SAAAA,CAAUH,MAAM,EAAE;MAC1B,OAAOA,MAAM,CAACC,OAAO,CAAC,KAAK,EAAE,UAAUC,KAAK,EAAE;QAC1C,OAAO5C,SAAS,CAAC4C,KAAK,CAAC;MAC3B,CAAC,CAAC;IACN,CAAC;IACDE,aAAa,EAAE,uCAAuC;IACtDC,YAAY,EAAE,SAAAA,CAAUC,IAAI,EAAEC,QAAQ,EAAE;MACpC,IAAID,IAAI,KAAK,EAAE,EAAE;QACbA,IAAI,GAAG,CAAC;MACZ;MACA,IACKC,QAAQ,KAAK,QAAQ,IAAID,IAAI,IAAI,CAAC,IAClCC,QAAQ,KAAK,SAAS,IAAID,IAAI,GAAG,CAAE,IACpCC,QAAQ,KAAK,SAAS,EACxB;QACE,OAAOD,IAAI,GAAG,EAAE;MACpB,CAAC,MAAM;QACH,OAAOA,IAAI;MACf;IACJ,CAAC;IACDC,QAAQ,EAAE,SAAAA,CAAUD,IAAI,EAAEE,MAAM,EAAEC,OAAO,EAAE;MACvC,IAAIH,IAAI,GAAG,CAAC,EAAE;QACV,OAAO,QAAQ;MACnB,CAAC,MAAM,IAAIA,IAAI,GAAG,EAAE,EAAE;QAClB,OAAO,SAAS;MACpB,CAAC,MAAM,IAAIA,IAAI,GAAG,EAAE,EAAE;QAClB,OAAO,SAAS;MACpB,CAAC,MAAM,IAAIA,IAAI,GAAG,EAAE,EAAE;QAClB,OAAO,SAAS;MACpB,CAAC,MAAM;QACH,OAAO,QAAQ;MACnB;IACJ,CAAC;IACDI,IAAI,EAAE;MACFC,GAAG,EAAE,CAAC;MAAE;MACRC,GAAG,EAAE,CAAC,CAAE;IACZ;EACJ,CAAC,CAAC;EAEF,OAAOpD,EAAE;AAEb,CAAE,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}