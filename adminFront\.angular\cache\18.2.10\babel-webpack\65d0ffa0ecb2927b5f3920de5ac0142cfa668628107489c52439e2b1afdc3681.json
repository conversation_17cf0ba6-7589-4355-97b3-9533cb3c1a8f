{"ast": null, "code": "import { addDays, addHours, addMinutes, addSeconds, differenceInDays, differenceInMinutes, differenceInSeconds, endOfDay, endOfMonth, endOfWeek, getDay, getMonth, isSameDay, isSameMonth, isSameSecond, max, setHours, setMinutes, startOfDay, startOfMinute, startOfMonth, startOfWeek, getHours, getMinutes } from 'date-fns';\nfunction getTimezoneOffset(date) {\n  return new Date(date).getTimezoneOffset();\n}\nexport function adapterFactory() {\n  return {\n    addDays: addDays,\n    addHours: addHours,\n    addMinutes: addMinutes,\n    addSeconds: addSeconds,\n    differenceInDays: differenceInDays,\n    differenceInMinutes: differenceInMinutes,\n    differenceInSeconds: differenceInSeconds,\n    endOfDay: endOfDay,\n    endOfMonth: endOfMonth,\n    endOfWeek: endOfWeek,\n    getDay: getDay,\n    getMonth: getMonth,\n    isSameDay: isSameDay,\n    isSameMonth: isSameMonth,\n    isSameSecond: isSameSecond,\n    max: max,\n    setHours: setHours,\n    setMinutes: setMinutes,\n    startOfDay: startOfDay,\n    startOfMinute: startOfMinute,\n    startOfMonth: startOfMonth,\n    startOfWeek: startOfWeek,\n    getHours: getHours,\n    getMinutes: getMinutes,\n    getTimezoneOffset: getTimezoneOffset\n  };\n}\n//# sourceMappingURL=index.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}