{"ast": null, "code": "import { createPlugin } from '@fullcalendar/core/index.js';\nimport { BootstrapTheme } from './internal.js';\nimport '@fullcalendar/core/internal.js';\nvar index = createPlugin({\n  name: '@fullcalendar/bootstrap',\n  themeClasses: {\n    bootstrap: BootstrapTheme\n  }\n});\nexport { index as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}