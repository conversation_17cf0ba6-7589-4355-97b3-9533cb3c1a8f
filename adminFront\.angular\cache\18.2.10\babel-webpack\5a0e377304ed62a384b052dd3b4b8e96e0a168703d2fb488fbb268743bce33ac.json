{"ast": null, "code": "import getOppositePlacement from \"../utils/getOppositePlacement.js\";\nimport getBasePlacement from \"../utils/getBasePlacement.js\";\nimport getOppositeVariationPlacement from \"../utils/getOppositeVariationPlacement.js\";\nimport detectOverflow from \"../utils/detectOverflow.js\";\nimport computeAutoPlacement from \"../utils/computeAutoPlacement.js\";\nimport { bottom, top, start, right, left, auto } from \"../enums.js\";\nimport getVariation from \"../utils/getVariation.js\"; // eslint-disable-next-line import/no-unused-modules\n\nfunction getExpandedFallbackPlacements(placement) {\n  if (getBasePlacement(placement) === auto) {\n    return [];\n  }\n  var oppositePlacement = getOppositePlacement(placement);\n  return [getOppositeVariationPlacement(placement), oppositePlacement, getOppositeVariationPlacement(oppositePlacement)];\n}\nfunction flip(_ref) {\n  var state = _ref.state,\n    options = _ref.options,\n    name = _ref.name;\n  if (state.modifiersData[name]._skip) {\n    return;\n  }\n  var _options$mainAxis = options.mainAxis,\n    checkMainAxis = _options$mainAxis === void 0 ? true : _options$mainAxis,\n    _options$altAxis = options.altAxis,\n    checkAltAxis = _options$altAxis === void 0 ? true : _options$altAxis,\n    specifiedFallbackPlacements = options.fallbackPlacements,\n    padding = options.padding,\n    boundary = options.boundary,\n    rootBoundary = options.rootBoundary,\n    altBoundary = options.altBoundary,\n    _options$flipVariatio = options.flipVariations,\n    flipVariations = _options$flipVariatio === void 0 ? true : _options$flipVariatio,\n    allowedAutoPlacements = options.allowedAutoPlacements;\n  var preferredPlacement = state.options.placement;\n  var basePlacement = getBasePlacement(preferredPlacement);\n  var isBasePlacement = basePlacement === preferredPlacement;\n  var fallbackPlacements = specifiedFallbackPlacements || (isBasePlacement || !flipVariations ? [getOppositePlacement(preferredPlacement)] : getExpandedFallbackPlacements(preferredPlacement));\n  var placements = [preferredPlacement].concat(fallbackPlacements).reduce(function (acc, placement) {\n    return acc.concat(getBasePlacement(placement) === auto ? computeAutoPlacement(state, {\n      placement: placement,\n      boundary: boundary,\n      rootBoundary: rootBoundary,\n      padding: padding,\n      flipVariations: flipVariations,\n      allowedAutoPlacements: allowedAutoPlacements\n    }) : placement);\n  }, []);\n  var referenceRect = state.rects.reference;\n  var popperRect = state.rects.popper;\n  var checksMap = new Map();\n  var makeFallbackChecks = true;\n  var firstFittingPlacement = placements[0];\n  for (var i = 0; i < placements.length; i++) {\n    var placement = placements[i];\n    var _basePlacement = getBasePlacement(placement);\n    var isStartVariation = getVariation(placement) === start;\n    var isVertical = [top, bottom].indexOf(_basePlacement) >= 0;\n    var len = isVertical ? 'width' : 'height';\n    var overflow = detectOverflow(state, {\n      placement: placement,\n      boundary: boundary,\n      rootBoundary: rootBoundary,\n      altBoundary: altBoundary,\n      padding: padding\n    });\n    var mainVariationSide = isVertical ? isStartVariation ? right : left : isStartVariation ? bottom : top;\n    if (referenceRect[len] > popperRect[len]) {\n      mainVariationSide = getOppositePlacement(mainVariationSide);\n    }\n    var altVariationSide = getOppositePlacement(mainVariationSide);\n    var checks = [];\n    if (checkMainAxis) {\n      checks.push(overflow[_basePlacement] <= 0);\n    }\n    if (checkAltAxis) {\n      checks.push(overflow[mainVariationSide] <= 0, overflow[altVariationSide] <= 0);\n    }\n    if (checks.every(function (check) {\n      return check;\n    })) {\n      firstFittingPlacement = placement;\n      makeFallbackChecks = false;\n      break;\n    }\n    checksMap.set(placement, checks);\n  }\n  if (makeFallbackChecks) {\n    // `2` may be desired in some cases – research later\n    var numberOfChecks = flipVariations ? 3 : 1;\n    var _loop = function _loop(_i) {\n      var fittingPlacement = placements.find(function (placement) {\n        var checks = checksMap.get(placement);\n        if (checks) {\n          return checks.slice(0, _i).every(function (check) {\n            return check;\n          });\n        }\n      });\n      if (fittingPlacement) {\n        firstFittingPlacement = fittingPlacement;\n        return \"break\";\n      }\n    };\n    for (var _i = numberOfChecks; _i > 0; _i--) {\n      var _ret = _loop(_i);\n      if (_ret === \"break\") break;\n    }\n  }\n  if (state.placement !== firstFittingPlacement) {\n    state.modifiersData[name]._skip = true;\n    state.placement = firstFittingPlacement;\n    state.reset = true;\n  }\n} // eslint-disable-next-line import/no-unused-modules\n\nexport default {\n  name: 'flip',\n  enabled: true,\n  phase: 'main',\n  fn: flip,\n  requiresIfExists: ['offset'],\n  data: {\n    _skip: false\n  }\n};", "map": {"version": 3, "names": ["getOppositePlacement", "getBasePlacement", "getOppositeVariationPlacement", "detectOverflow", "computeAutoPlacement", "bottom", "top", "start", "right", "left", "auto", "getVariation", "getExpandedFallbackPlacements", "placement", "oppositePlacement", "flip", "_ref", "state", "options", "name", "modifiersData", "_skip", "_options$mainAxis", "mainAxis", "checkMainAxis", "_options$altAxis", "altAxis", "checkAltAxis", "specifiedFallbackPlacements", "fallbackPlacements", "padding", "boundary", "rootBoundary", "altBoundary", "_options$flipVariatio", "flipVariations", "allowedAutoPlacements", "preferredPlacement", "basePlacement", "isBasePlacement", "placements", "concat", "reduce", "acc", "referenceRect", "rects", "reference", "popperRect", "popper", "checksMap", "Map", "makeFallbackChecks", "firstFittingPlacement", "i", "length", "_basePlacement", "isStartVariation", "isVertical", "indexOf", "len", "overflow", "mainVariationSide", "altVariationSide", "checks", "push", "every", "check", "set", "numberOfChecks", "_loop", "_i", "fittingPlacement", "find", "get", "slice", "_ret", "reset", "enabled", "phase", "fn", "requiresIfExists", "data"], "sources": ["C:/Users/<USER>/Documents/jeansalechange/adminFront/node_modules/@popperjs/core/lib/modifiers/flip.js"], "sourcesContent": ["import getOppositePlacement from \"../utils/getOppositePlacement.js\";\nimport getBasePlacement from \"../utils/getBasePlacement.js\";\nimport getOppositeVariationPlacement from \"../utils/getOppositeVariationPlacement.js\";\nimport detectOverflow from \"../utils/detectOverflow.js\";\nimport computeAutoPlacement from \"../utils/computeAutoPlacement.js\";\nimport { bottom, top, start, right, left, auto } from \"../enums.js\";\nimport getVariation from \"../utils/getVariation.js\"; // eslint-disable-next-line import/no-unused-modules\n\nfunction getExpandedFallbackPlacements(placement) {\n  if (getBasePlacement(placement) === auto) {\n    return [];\n  }\n\n  var oppositePlacement = getOppositePlacement(placement);\n  return [getOppositeVariationPlacement(placement), oppositePlacement, getOppositeVariationPlacement(oppositePlacement)];\n}\n\nfunction flip(_ref) {\n  var state = _ref.state,\n      options = _ref.options,\n      name = _ref.name;\n\n  if (state.modifiersData[name]._skip) {\n    return;\n  }\n\n  var _options$mainAxis = options.mainAxis,\n      checkMainAxis = _options$mainAxis === void 0 ? true : _options$mainAxis,\n      _options$altAxis = options.altAxis,\n      checkAltAxis = _options$altAxis === void 0 ? true : _options$altAxis,\n      specifiedFallbackPlacements = options.fallbackPlacements,\n      padding = options.padding,\n      boundary = options.boundary,\n      rootBoundary = options.rootBoundary,\n      altBoundary = options.altBoundary,\n      _options$flipVariatio = options.flipVariations,\n      flipVariations = _options$flipVariatio === void 0 ? true : _options$flipVariatio,\n      allowedAutoPlacements = options.allowedAutoPlacements;\n  var preferredPlacement = state.options.placement;\n  var basePlacement = getBasePlacement(preferredPlacement);\n  var isBasePlacement = basePlacement === preferredPlacement;\n  var fallbackPlacements = specifiedFallbackPlacements || (isBasePlacement || !flipVariations ? [getOppositePlacement(preferredPlacement)] : getExpandedFallbackPlacements(preferredPlacement));\n  var placements = [preferredPlacement].concat(fallbackPlacements).reduce(function (acc, placement) {\n    return acc.concat(getBasePlacement(placement) === auto ? computeAutoPlacement(state, {\n      placement: placement,\n      boundary: boundary,\n      rootBoundary: rootBoundary,\n      padding: padding,\n      flipVariations: flipVariations,\n      allowedAutoPlacements: allowedAutoPlacements\n    }) : placement);\n  }, []);\n  var referenceRect = state.rects.reference;\n  var popperRect = state.rects.popper;\n  var checksMap = new Map();\n  var makeFallbackChecks = true;\n  var firstFittingPlacement = placements[0];\n\n  for (var i = 0; i < placements.length; i++) {\n    var placement = placements[i];\n\n    var _basePlacement = getBasePlacement(placement);\n\n    var isStartVariation = getVariation(placement) === start;\n    var isVertical = [top, bottom].indexOf(_basePlacement) >= 0;\n    var len = isVertical ? 'width' : 'height';\n    var overflow = detectOverflow(state, {\n      placement: placement,\n      boundary: boundary,\n      rootBoundary: rootBoundary,\n      altBoundary: altBoundary,\n      padding: padding\n    });\n    var mainVariationSide = isVertical ? isStartVariation ? right : left : isStartVariation ? bottom : top;\n\n    if (referenceRect[len] > popperRect[len]) {\n      mainVariationSide = getOppositePlacement(mainVariationSide);\n    }\n\n    var altVariationSide = getOppositePlacement(mainVariationSide);\n    var checks = [];\n\n    if (checkMainAxis) {\n      checks.push(overflow[_basePlacement] <= 0);\n    }\n\n    if (checkAltAxis) {\n      checks.push(overflow[mainVariationSide] <= 0, overflow[altVariationSide] <= 0);\n    }\n\n    if (checks.every(function (check) {\n      return check;\n    })) {\n      firstFittingPlacement = placement;\n      makeFallbackChecks = false;\n      break;\n    }\n\n    checksMap.set(placement, checks);\n  }\n\n  if (makeFallbackChecks) {\n    // `2` may be desired in some cases – research later\n    var numberOfChecks = flipVariations ? 3 : 1;\n\n    var _loop = function _loop(_i) {\n      var fittingPlacement = placements.find(function (placement) {\n        var checks = checksMap.get(placement);\n\n        if (checks) {\n          return checks.slice(0, _i).every(function (check) {\n            return check;\n          });\n        }\n      });\n\n      if (fittingPlacement) {\n        firstFittingPlacement = fittingPlacement;\n        return \"break\";\n      }\n    };\n\n    for (var _i = numberOfChecks; _i > 0; _i--) {\n      var _ret = _loop(_i);\n\n      if (_ret === \"break\") break;\n    }\n  }\n\n  if (state.placement !== firstFittingPlacement) {\n    state.modifiersData[name]._skip = true;\n    state.placement = firstFittingPlacement;\n    state.reset = true;\n  }\n} // eslint-disable-next-line import/no-unused-modules\n\n\nexport default {\n  name: 'flip',\n  enabled: true,\n  phase: 'main',\n  fn: flip,\n  requiresIfExists: ['offset'],\n  data: {\n    _skip: false\n  }\n};"], "mappings": "AAAA,OAAOA,oBAAoB,MAAM,kCAAkC;AACnE,OAAOC,gBAAgB,MAAM,8BAA8B;AAC3D,OAAOC,6BAA6B,MAAM,2CAA2C;AACrF,OAAOC,cAAc,MAAM,4BAA4B;AACvD,OAAOC,oBAAoB,MAAM,kCAAkC;AACnE,SAASC,MAAM,EAAEC,GAAG,EAAEC,KAAK,EAAEC,KAAK,EAAEC,IAAI,EAAEC,IAAI,QAAQ,aAAa;AACnE,OAAOC,YAAY,MAAM,0BAA0B,CAAC,CAAC;;AAErD,SAASC,6BAA6BA,CAACC,SAAS,EAAE;EAChD,IAAIZ,gBAAgB,CAACY,SAAS,CAAC,KAAKH,IAAI,EAAE;IACxC,OAAO,EAAE;EACX;EAEA,IAAII,iBAAiB,GAAGd,oBAAoB,CAACa,SAAS,CAAC;EACvD,OAAO,CAACX,6BAA6B,CAACW,SAAS,CAAC,EAAEC,iBAAiB,EAAEZ,6BAA6B,CAACY,iBAAiB,CAAC,CAAC;AACxH;AAEA,SAASC,IAAIA,CAACC,IAAI,EAAE;EAClB,IAAIC,KAAK,GAAGD,IAAI,CAACC,KAAK;IAClBC,OAAO,GAAGF,IAAI,CAACE,OAAO;IACtBC,IAAI,GAAGH,IAAI,CAACG,IAAI;EAEpB,IAAIF,KAAK,CAACG,aAAa,CAACD,IAAI,CAAC,CAACE,KAAK,EAAE;IACnC;EACF;EAEA,IAAIC,iBAAiB,GAAGJ,OAAO,CAACK,QAAQ;IACpCC,aAAa,GAAGF,iBAAiB,KAAK,KAAK,CAAC,GAAG,IAAI,GAAGA,iBAAiB;IACvEG,gBAAgB,GAAGP,OAAO,CAACQ,OAAO;IAClCC,YAAY,GAAGF,gBAAgB,KAAK,KAAK,CAAC,GAAG,IAAI,GAAGA,gBAAgB;IACpEG,2BAA2B,GAAGV,OAAO,CAACW,kBAAkB;IACxDC,OAAO,GAAGZ,OAAO,CAACY,OAAO;IACzBC,QAAQ,GAAGb,OAAO,CAACa,QAAQ;IAC3BC,YAAY,GAAGd,OAAO,CAACc,YAAY;IACnCC,WAAW,GAAGf,OAAO,CAACe,WAAW;IACjCC,qBAAqB,GAAGhB,OAAO,CAACiB,cAAc;IAC9CA,cAAc,GAAGD,qBAAqB,KAAK,KAAK,CAAC,GAAG,IAAI,GAAGA,qBAAqB;IAChFE,qBAAqB,GAAGlB,OAAO,CAACkB,qBAAqB;EACzD,IAAIC,kBAAkB,GAAGpB,KAAK,CAACC,OAAO,CAACL,SAAS;EAChD,IAAIyB,aAAa,GAAGrC,gBAAgB,CAACoC,kBAAkB,CAAC;EACxD,IAAIE,eAAe,GAAGD,aAAa,KAAKD,kBAAkB;EAC1D,IAAIR,kBAAkB,GAAGD,2BAA2B,KAAKW,eAAe,IAAI,CAACJ,cAAc,GAAG,CAACnC,oBAAoB,CAACqC,kBAAkB,CAAC,CAAC,GAAGzB,6BAA6B,CAACyB,kBAAkB,CAAC,CAAC;EAC7L,IAAIG,UAAU,GAAG,CAACH,kBAAkB,CAAC,CAACI,MAAM,CAACZ,kBAAkB,CAAC,CAACa,MAAM,CAAC,UAAUC,GAAG,EAAE9B,SAAS,EAAE;IAChG,OAAO8B,GAAG,CAACF,MAAM,CAACxC,gBAAgB,CAACY,SAAS,CAAC,KAAKH,IAAI,GAAGN,oBAAoB,CAACa,KAAK,EAAE;MACnFJ,SAAS,EAAEA,SAAS;MACpBkB,QAAQ,EAAEA,QAAQ;MAClBC,YAAY,EAAEA,YAAY;MAC1BF,OAAO,EAAEA,OAAO;MAChBK,cAAc,EAAEA,cAAc;MAC9BC,qBAAqB,EAAEA;IACzB,CAAC,CAAC,GAAGvB,SAAS,CAAC;EACjB,CAAC,EAAE,EAAE,CAAC;EACN,IAAI+B,aAAa,GAAG3B,KAAK,CAAC4B,KAAK,CAACC,SAAS;EACzC,IAAIC,UAAU,GAAG9B,KAAK,CAAC4B,KAAK,CAACG,MAAM;EACnC,IAAIC,SAAS,GAAG,IAAIC,GAAG,CAAC,CAAC;EACzB,IAAIC,kBAAkB,GAAG,IAAI;EAC7B,IAAIC,qBAAqB,GAAGZ,UAAU,CAAC,CAAC,CAAC;EAEzC,KAAK,IAAIa,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGb,UAAU,CAACc,MAAM,EAAED,CAAC,EAAE,EAAE;IAC1C,IAAIxC,SAAS,GAAG2B,UAAU,CAACa,CAAC,CAAC;IAE7B,IAAIE,cAAc,GAAGtD,gBAAgB,CAACY,SAAS,CAAC;IAEhD,IAAI2C,gBAAgB,GAAG7C,YAAY,CAACE,SAAS,CAAC,KAAKN,KAAK;IACxD,IAAIkD,UAAU,GAAG,CAACnD,GAAG,EAAED,MAAM,CAAC,CAACqD,OAAO,CAACH,cAAc,CAAC,IAAI,CAAC;IAC3D,IAAII,GAAG,GAAGF,UAAU,GAAG,OAAO,GAAG,QAAQ;IACzC,IAAIG,QAAQ,GAAGzD,cAAc,CAACc,KAAK,EAAE;MACnCJ,SAAS,EAAEA,SAAS;MACpBkB,QAAQ,EAAEA,QAAQ;MAClBC,YAAY,EAAEA,YAAY;MAC1BC,WAAW,EAAEA,WAAW;MACxBH,OAAO,EAAEA;IACX,CAAC,CAAC;IACF,IAAI+B,iBAAiB,GAAGJ,UAAU,GAAGD,gBAAgB,GAAGhD,KAAK,GAAGC,IAAI,GAAG+C,gBAAgB,GAAGnD,MAAM,GAAGC,GAAG;IAEtG,IAAIsC,aAAa,CAACe,GAAG,CAAC,GAAGZ,UAAU,CAACY,GAAG,CAAC,EAAE;MACxCE,iBAAiB,GAAG7D,oBAAoB,CAAC6D,iBAAiB,CAAC;IAC7D;IAEA,IAAIC,gBAAgB,GAAG9D,oBAAoB,CAAC6D,iBAAiB,CAAC;IAC9D,IAAIE,MAAM,GAAG,EAAE;IAEf,IAAIvC,aAAa,EAAE;MACjBuC,MAAM,CAACC,IAAI,CAACJ,QAAQ,CAACL,cAAc,CAAC,IAAI,CAAC,CAAC;IAC5C;IAEA,IAAI5B,YAAY,EAAE;MAChBoC,MAAM,CAACC,IAAI,CAACJ,QAAQ,CAACC,iBAAiB,CAAC,IAAI,CAAC,EAAED,QAAQ,CAACE,gBAAgB,CAAC,IAAI,CAAC,CAAC;IAChF;IAEA,IAAIC,MAAM,CAACE,KAAK,CAAC,UAAUC,KAAK,EAAE;MAChC,OAAOA,KAAK;IACd,CAAC,CAAC,EAAE;MACFd,qBAAqB,GAAGvC,SAAS;MACjCsC,kBAAkB,GAAG,KAAK;MAC1B;IACF;IAEAF,SAAS,CAACkB,GAAG,CAACtD,SAAS,EAAEkD,MAAM,CAAC;EAClC;EAEA,IAAIZ,kBAAkB,EAAE;IACtB;IACA,IAAIiB,cAAc,GAAGjC,cAAc,GAAG,CAAC,GAAG,CAAC;IAE3C,IAAIkC,KAAK,GAAG,SAASA,KAAKA,CAACC,EAAE,EAAE;MAC7B,IAAIC,gBAAgB,GAAG/B,UAAU,CAACgC,IAAI,CAAC,UAAU3D,SAAS,EAAE;QAC1D,IAAIkD,MAAM,GAAGd,SAAS,CAACwB,GAAG,CAAC5D,SAAS,CAAC;QAErC,IAAIkD,MAAM,EAAE;UACV,OAAOA,MAAM,CAACW,KAAK,CAAC,CAAC,EAAEJ,EAAE,CAAC,CAACL,KAAK,CAAC,UAAUC,KAAK,EAAE;YAChD,OAAOA,KAAK;UACd,CAAC,CAAC;QACJ;MACF,CAAC,CAAC;MAEF,IAAIK,gBAAgB,EAAE;QACpBnB,qBAAqB,GAAGmB,gBAAgB;QACxC,OAAO,OAAO;MAChB;IACF,CAAC;IAED,KAAK,IAAID,EAAE,GAAGF,cAAc,EAAEE,EAAE,GAAG,CAAC,EAAEA,EAAE,EAAE,EAAE;MAC1C,IAAIK,IAAI,GAAGN,KAAK,CAACC,EAAE,CAAC;MAEpB,IAAIK,IAAI,KAAK,OAAO,EAAE;IACxB;EACF;EAEA,IAAI1D,KAAK,CAACJ,SAAS,KAAKuC,qBAAqB,EAAE;IAC7CnC,KAAK,CAACG,aAAa,CAACD,IAAI,CAAC,CAACE,KAAK,GAAG,IAAI;IACtCJ,KAAK,CAACJ,SAAS,GAAGuC,qBAAqB;IACvCnC,KAAK,CAAC2D,KAAK,GAAG,IAAI;EACpB;AACF,CAAC,CAAC;;AAGF,eAAe;EACbzD,IAAI,EAAE,MAAM;EACZ0D,OAAO,EAAE,IAAI;EACbC,KAAK,EAAE,MAAM;EACbC,EAAE,EAAEhE,IAAI;EACRiE,gBAAgB,EAAE,CAAC,QAAQ,CAAC;EAC5BC,IAAI,EAAE;IACJ5D,KAAK,EAAE;EACT;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}