{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { ChangeDetectionStrategy, Component } from '@angular/core';\nlet ApproveWaiting1Component = class ApproveWaiting1Component {};\nApproveWaiting1Component = __decorate([Component({\n  selector: 'app-approve-waiting1',\n  standalone: true,\n  imports: [ApproveWaitingComponent],\n  templateUrl: './approve-waiting1.component.html',\n  styleUrl: './approve-waiting1.component.css',\n  changeDetection: ChangeDetectionStrategy.OnPush\n})], ApproveWaiting1Component);\nexport { ApproveWaiting1Component };", "map": {"version": 3, "names": ["ChangeDetectionStrategy", "Component", "ApproveWaiting1Component", "__decorate", "selector", "standalone", "imports", "ApproveWaitingComponent", "templateUrl", "styleUrl", "changeDetection", "OnPush"], "sources": ["C:\\Users\\<USER>\\Documents\\jeansalechange\\adminFront\\src\\app\\pages\\approve-waiting1\\approve-waiting1.component.ts"], "sourcesContent": ["import { ChangeDetectionStrategy, Component } from '@angular/core';\n\n@Component({\n  selector: 'app-approve-waiting1',\n  standalone: true,\n  imports: [ApproveWaitingComponent],\n  templateUrl: './approve-waiting1.component.html',\n  styleUrl: './approve-waiting1.component.css',\n  changeDetection: ChangeDetectionStrategy.OnPush,\n})\nexport class ApproveWaiting1Component { }\n"], "mappings": ";AAAA,SAASA,uBAAuB,EAAEC,SAAS,QAAQ,eAAe;AAU3D,IAAMC,wBAAwB,GAA9B,MAAMA,wBAAwB,GAAI;AAA5BA,wBAAwB,GAAAC,UAAA,EARpCF,SAAS,CAAC;EACTG,QAAQ,EAAE,sBAAsB;EAChCC,UAAU,EAAE,IAAI;EAChBC,OAAO,EAAE,CAACC,uBAAuB,CAAC;EAClCC,WAAW,EAAE,mCAAmC;EAChDC,QAAQ,EAAE,kCAAkC;EAC5CC,eAAe,EAAEV,uBAAuB,CAACW;CAC1C,CAAC,C,EACWT,wBAAwB,CAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}