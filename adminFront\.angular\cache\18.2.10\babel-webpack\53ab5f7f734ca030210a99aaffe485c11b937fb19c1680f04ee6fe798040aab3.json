{"ast": null, "code": "import _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name formatISODuration\n * @category Common Helpers\n * @summary Format a duration object according as ISO 8601 duration string\n *\n * @description\n * Format a duration object according to the ISO 8601 duration standard (https://www.digi.com/resources/documentation/digidocs/90001437-13/reference/r_iso_8601_duration_format.htm)\n *\n * @param {Duration} duration - the duration to format\n *\n * @returns {String} The ISO 8601 duration string\n * @throws {TypeError} Requires 1 argument\n * @throws {Error} Argument must be an object\n *\n * @example\n * // Format the given duration as ISO 8601 string\n * const result = formatISODuration({\n *   years: 39,\n *   months: 2,\n *   days: 20,\n *   hours: 7,\n *   minutes: 5,\n *   seconds: 0\n * })\n * //=> 'P39Y2M20DT0H0M0S'\n */\nexport default function formatISODuration(duration) {\n  requiredArgs(1, arguments);\n  if (_typeof(duration) !== 'object') throw new Error('Duration must be an object');\n  var _duration$years = duration.years,\n    years = _duration$years === void 0 ? 0 : _duration$years,\n    _duration$months = duration.months,\n    months = _duration$months === void 0 ? 0 : _duration$months,\n    _duration$days = duration.days,\n    days = _duration$days === void 0 ? 0 : _duration$days,\n    _duration$hours = duration.hours,\n    hours = _duration$hours === void 0 ? 0 : _duration$hours,\n    _duration$minutes = duration.minutes,\n    minutes = _duration$minutes === void 0 ? 0 : _duration$minutes,\n    _duration$seconds = duration.seconds,\n    seconds = _duration$seconds === void 0 ? 0 : _duration$seconds;\n  return \"P\".concat(years, \"Y\").concat(months, \"M\").concat(days, \"DT\").concat(hours, \"H\").concat(minutes, \"M\").concat(seconds, \"S\");\n}", "map": {"version": 3, "names": ["_typeof", "requiredArgs", "formatISODuration", "duration", "arguments", "Error", "_duration$years", "years", "_duration$months", "months", "_duration$days", "days", "_duration$hours", "hours", "_duration$minutes", "minutes", "_duration$seconds", "seconds", "concat"], "sources": ["C:/Users/<USER>/Documents/jeansalechange/adminFront/node_modules/date-fns/esm/formatISODuration/index.js"], "sourcesContent": ["import _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name formatISODuration\n * @category Common Helpers\n * @summary Format a duration object according as ISO 8601 duration string\n *\n * @description\n * Format a duration object according to the ISO 8601 duration standard (https://www.digi.com/resources/documentation/digidocs/90001437-13/reference/r_iso_8601_duration_format.htm)\n *\n * @param {Duration} duration - the duration to format\n *\n * @returns {String} The ISO 8601 duration string\n * @throws {TypeError} Requires 1 argument\n * @throws {Error} Argument must be an object\n *\n * @example\n * // Format the given duration as ISO 8601 string\n * const result = formatISODuration({\n *   years: 39,\n *   months: 2,\n *   days: 20,\n *   hours: 7,\n *   minutes: 5,\n *   seconds: 0\n * })\n * //=> 'P39Y2M20DT0H0M0S'\n */\nexport default function formatISODuration(duration) {\n  requiredArgs(1, arguments);\n  if (_typeof(duration) !== 'object') throw new Error('Duration must be an object');\n  var _duration$years = duration.years,\n    years = _duration$years === void 0 ? 0 : _duration$years,\n    _duration$months = duration.months,\n    months = _duration$months === void 0 ? 0 : _duration$months,\n    _duration$days = duration.days,\n    days = _duration$days === void 0 ? 0 : _duration$days,\n    _duration$hours = duration.hours,\n    hours = _duration$hours === void 0 ? 0 : _duration$hours,\n    _duration$minutes = duration.minutes,\n    minutes = _duration$minutes === void 0 ? 0 : _duration$minutes,\n    _duration$seconds = duration.seconds,\n    seconds = _duration$seconds === void 0 ? 0 : _duration$seconds;\n  return \"P\".concat(years, \"Y\").concat(months, \"M\").concat(days, \"DT\").concat(hours, \"H\").concat(minutes, \"M\").concat(seconds, \"S\");\n}"], "mappings": "AAAA,OAAOA,OAAO,MAAM,mCAAmC;AACvD,OAAOC,YAAY,MAAM,+BAA+B;AACxD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,SAASC,iBAAiBA,CAACC,QAAQ,EAAE;EAClDF,YAAY,CAAC,CAAC,EAAEG,SAAS,CAAC;EAC1B,IAAIJ,OAAO,CAACG,QAAQ,CAAC,KAAK,QAAQ,EAAE,MAAM,IAAIE,KAAK,CAAC,4BAA4B,CAAC;EACjF,IAAIC,eAAe,GAAGH,QAAQ,CAACI,KAAK;IAClCA,KAAK,GAAGD,eAAe,KAAK,KAAK,CAAC,GAAG,CAAC,GAAGA,eAAe;IACxDE,gBAAgB,GAAGL,QAAQ,CAACM,MAAM;IAClCA,MAAM,GAAGD,gBAAgB,KAAK,KAAK,CAAC,GAAG,CAAC,GAAGA,gBAAgB;IAC3DE,cAAc,GAAGP,QAAQ,CAACQ,IAAI;IAC9BA,IAAI,GAAGD,cAAc,KAAK,KAAK,CAAC,GAAG,CAAC,GAAGA,cAAc;IACrDE,eAAe,GAAGT,QAAQ,CAACU,KAAK;IAChCA,KAAK,GAAGD,eAAe,KAAK,KAAK,CAAC,GAAG,CAAC,GAAGA,eAAe;IACxDE,iBAAiB,GAAGX,QAAQ,CAACY,OAAO;IACpCA,OAAO,GAAGD,iBAAiB,KAAK,KAAK,CAAC,GAAG,CAAC,GAAGA,iBAAiB;IAC9DE,iBAAiB,GAAGb,QAAQ,CAACc,OAAO;IACpCA,OAAO,GAAGD,iBAAiB,KAAK,KAAK,CAAC,GAAG,CAAC,GAAGA,iBAAiB;EAChE,OAAO,GAAG,CAACE,MAAM,CAACX,KAAK,EAAE,GAAG,CAAC,CAACW,MAAM,CAACT,MAAM,EAAE,GAAG,CAAC,CAACS,MAAM,CAACP,IAAI,EAAE,IAAI,CAAC,CAACO,MAAM,CAACL,KAAK,EAAE,GAAG,CAAC,CAACK,MAAM,CAACH,OAAO,EAAE,GAAG,CAAC,CAACG,MAAM,CAACD,OAAO,EAAE,GAAG,CAAC;AACnI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}