{"ast": null, "code": "//! moment.js locale configuration\n//! locale : Lao [lo]\n//! author : <PERSON> : https://github.com/ryanhart2\n\n;\n(function (global, factory) {\n  typeof exports === 'object' && typeof module !== 'undefined' && typeof require === 'function' ? factory(require('../moment')) : typeof define === 'function' && define.amd ? define(['../moment'], factory) : factory(global.moment);\n})(this, function (moment) {\n  'use strict';\n\n  //! moment.js locale configuration\n  var lo = moment.defineLocale('lo', {\n    months: 'ມັງກອນ_ກຸມພາ_ມີນາ_ເມສາ_ພຶດສະພາ_ມິຖຸນາ_ກໍລະກົດ_ສິງຫາ_ກັນຍາ_ຕຸລາ_ພະຈິກ_ທັນວາ'.split('_'),\n    monthsShort: 'ມັງກອນ_ກຸມພາ_ມີນາ_ເມສາ_ພຶດສະພາ_ມິຖຸນາ_ກໍລະກົດ_ສິງຫາ_ກັນຍາ_ຕຸລາ_ພະຈິກ_ທັນວາ'.split('_'),\n    weekdays: 'ອາທິດ_ຈັນ_ອັງຄານ_ພຸດ_ພະຫັດ_ສຸກ_ເສົາ'.split('_'),\n    weekdaysShort: 'ທິດ_ຈັນ_ອັງຄານ_ພຸດ_ພະຫັດ_ສຸກ_ເສົາ'.split('_'),\n    weekdaysMin: 'ທ_ຈ_ອຄ_ພ_ພຫ_ສກ_ສ'.split('_'),\n    weekdaysParseExact: true,\n    longDateFormat: {\n      LT: 'HH:mm',\n      LTS: 'HH:mm:ss',\n      L: 'DD/MM/YYYY',\n      LL: 'D MMMM YYYY',\n      LLL: 'D MMMM YYYY HH:mm',\n      LLLL: 'ວັນdddd D MMMM YYYY HH:mm'\n    },\n    meridiemParse: /ຕອນເຊົ້າ|ຕອນແລງ/,\n    isPM: function (input) {\n      return input === 'ຕອນແລງ';\n    },\n    meridiem: function (hour, minute, isLower) {\n      if (hour < 12) {\n        return 'ຕອນເຊົ້າ';\n      } else {\n        return 'ຕອນແລງ';\n      }\n    },\n    calendar: {\n      sameDay: '[ມື້ນີ້ເວລາ] LT',\n      nextDay: '[ມື້ອື່ນເວລາ] LT',\n      nextWeek: '[ວັນ]dddd[ໜ້າເວລາ] LT',\n      lastDay: '[ມື້ວານນີ້ເວລາ] LT',\n      lastWeek: '[ວັນ]dddd[ແລ້ວນີ້ເວລາ] LT',\n      sameElse: 'L'\n    },\n    relativeTime: {\n      future: 'ອີກ %s',\n      past: '%sຜ່ານມາ',\n      s: 'ບໍ່ເທົ່າໃດວິນາທີ',\n      ss: '%d ວິນາທີ',\n      m: '1 ນາທີ',\n      mm: '%d ນາທີ',\n      h: '1 ຊົ່ວໂມງ',\n      hh: '%d ຊົ່ວໂມງ',\n      d: '1 ມື້',\n      dd: '%d ມື້',\n      M: '1 ເດືອນ',\n      MM: '%d ເດືອນ',\n      y: '1 ປີ',\n      yy: '%d ປີ'\n    },\n    dayOfMonthOrdinalParse: /(ທີ່)\\d{1,2}/,\n    ordinal: function (number) {\n      return 'ທີ່' + number;\n    }\n  });\n  return lo;\n});", "map": {"version": 3, "names": ["global", "factory", "exports", "module", "require", "define", "amd", "moment", "lo", "defineLocale", "months", "split", "monthsShort", "weekdays", "weekdaysShort", "weekdaysMin", "weekdaysParseExact", "longDateFormat", "LT", "LTS", "L", "LL", "LLL", "LLLL", "meridiemParse", "isPM", "input", "meridiem", "hour", "minute", "isLower", "calendar", "sameDay", "nextDay", "nextWeek", "lastDay", "lastWeek", "same<PERSON><PERSON><PERSON>", "relativeTime", "future", "past", "s", "ss", "m", "mm", "h", "hh", "d", "dd", "M", "MM", "y", "yy", "dayOfMonthOrdinalParse", "ordinal", "number"], "sources": ["C:/Users/<USER>/Documents/salechange-product/adminFront/node_modules/moment/locale/lo.js"], "sourcesContent": ["//! moment.js locale configuration\n//! locale : Lao [lo]\n//! author : <PERSON> : https://github.com/ryanhart2\n\n;(function (global, factory) {\n   typeof exports === 'object' && typeof module !== 'undefined'\n       && typeof require === 'function' ? factory(require('../moment')) :\n   typeof define === 'function' && define.amd ? define(['../moment'], factory) :\n   factory(global.moment)\n}(this, (function (moment) { 'use strict';\n\n    //! moment.js locale configuration\n\n    var lo = moment.defineLocale('lo', {\n        months: 'ມັງກອນ_ກຸມພາ_ມີນາ_ເມສາ_ພຶດສະພາ_ມິຖຸນາ_ກໍລະກົດ_ສິງຫາ_ກັນຍາ_ຕຸລາ_ພະຈິກ_ທັນວາ'.split(\n            '_'\n        ),\n        monthsShort:\n            'ມັງກອນ_ກຸມພາ_ມີນາ_ເມສາ_ພຶດສະພາ_ມິຖຸນາ_ກໍລະກົດ_ສິງຫາ_ກັນຍາ_ຕຸລາ_ພະຈິກ_ທັນວາ'.split(\n                '_'\n            ),\n        weekdays: 'ອາທິດ_ຈັນ_ອັງຄານ_ພຸດ_ພະຫັດ_ສຸກ_ເສົາ'.split('_'),\n        weekdaysShort: 'ທິດ_ຈັນ_ອັງຄານ_ພຸດ_ພະຫັດ_ສຸກ_ເສົາ'.split('_'),\n        weekdaysMin: 'ທ_ຈ_ອຄ_ພ_ພຫ_ສກ_ສ'.split('_'),\n        weekdaysParseExact: true,\n        longDateFormat: {\n            LT: 'HH:mm',\n            LTS: 'HH:mm:ss',\n            L: 'DD/MM/YYYY',\n            LL: 'D MMMM YYYY',\n            LLL: 'D MMMM YYYY HH:mm',\n            LLLL: 'ວັນdddd D MMMM YYYY HH:mm',\n        },\n        meridiemParse: /ຕອນເຊົ້າ|ຕອນແລງ/,\n        isPM: function (input) {\n            return input === 'ຕອນແລງ';\n        },\n        meridiem: function (hour, minute, isLower) {\n            if (hour < 12) {\n                return 'ຕອນເຊົ້າ';\n            } else {\n                return 'ຕອນແລງ';\n            }\n        },\n        calendar: {\n            sameDay: '[ມື້ນີ້ເວລາ] LT',\n            nextDay: '[ມື້ອື່ນເວລາ] LT',\n            nextWeek: '[ວັນ]dddd[ໜ້າເວລາ] LT',\n            lastDay: '[ມື້ວານນີ້ເວລາ] LT',\n            lastWeek: '[ວັນ]dddd[ແລ້ວນີ້ເວລາ] LT',\n            sameElse: 'L',\n        },\n        relativeTime: {\n            future: 'ອີກ %s',\n            past: '%sຜ່ານມາ',\n            s: 'ບໍ່ເທົ່າໃດວິນາທີ',\n            ss: '%d ວິນາທີ',\n            m: '1 ນາທີ',\n            mm: '%d ນາທີ',\n            h: '1 ຊົ່ວໂມງ',\n            hh: '%d ຊົ່ວໂມງ',\n            d: '1 ມື້',\n            dd: '%d ມື້',\n            M: '1 ເດືອນ',\n            MM: '%d ເດືອນ',\n            y: '1 ປີ',\n            yy: '%d ປີ',\n        },\n        dayOfMonthOrdinalParse: /(ທີ່)\\d{1,2}/,\n        ordinal: function (number) {\n            return 'ທີ່' + number;\n        },\n    });\n\n    return lo;\n\n})));\n"], "mappings": "AAAA;AACA;AACA;;AAEA;AAAE,WAAUA,MAAM,EAAEC,OAAO,EAAE;EAC1B,OAAOC,OAAO,KAAK,QAAQ,IAAI,OAAOC,MAAM,KAAK,WAAW,IACrD,OAAOC,OAAO,KAAK,UAAU,GAAGH,OAAO,CAACG,OAAO,CAAC,WAAW,CAAC,CAAC,GACpE,OAAOC,MAAM,KAAK,UAAU,IAAIA,MAAM,CAACC,GAAG,GAAGD,MAAM,CAAC,CAAC,WAAW,CAAC,EAAEJ,OAAO,CAAC,GAC3EA,OAAO,CAACD,MAAM,CAACO,MAAM,CAAC;AACzB,CAAC,EAAC,IAAI,EAAG,UAAUA,MAAM,EAAE;EAAE,YAAY;;EAErC;EAEA,IAAIC,EAAE,GAAGD,MAAM,CAACE,YAAY,CAAC,IAAI,EAAE;IAC/BC,MAAM,EAAE,4EAA4E,CAACC,KAAK,CACtF,GACJ,CAAC;IACDC,WAAW,EACP,4EAA4E,CAACD,KAAK,CAC9E,GACJ,CAAC;IACLE,QAAQ,EAAE,qCAAqC,CAACF,KAAK,CAAC,GAAG,CAAC;IAC1DG,aAAa,EAAE,mCAAmC,CAACH,KAAK,CAAC,GAAG,CAAC;IAC7DI,WAAW,EAAE,kBAAkB,CAACJ,KAAK,CAAC,GAAG,CAAC;IAC1CK,kBAAkB,EAAE,IAAI;IACxBC,cAAc,EAAE;MACZC,EAAE,EAAE,OAAO;MACXC,GAAG,EAAE,UAAU;MACfC,CAAC,EAAE,YAAY;MACfC,EAAE,EAAE,aAAa;MACjBC,GAAG,EAAE,mBAAmB;MACxBC,IAAI,EAAE;IACV,CAAC;IACDC,aAAa,EAAE,iBAAiB;IAChCC,IAAI,EAAE,SAAAA,CAAUC,KAAK,EAAE;MACnB,OAAOA,KAAK,KAAK,QAAQ;IAC7B,CAAC;IACDC,QAAQ,EAAE,SAAAA,CAAUC,IAAI,EAAEC,MAAM,EAAEC,OAAO,EAAE;MACvC,IAAIF,IAAI,GAAG,EAAE,EAAE;QACX,OAAO,UAAU;MACrB,CAAC,MAAM;QACH,OAAO,QAAQ;MACnB;IACJ,CAAC;IACDG,QAAQ,EAAE;MACNC,OAAO,EAAE,iBAAiB;MAC1BC,OAAO,EAAE,kBAAkB;MAC3BC,QAAQ,EAAE,uBAAuB;MACjCC,OAAO,EAAE,oBAAoB;MAC7BC,QAAQ,EAAE,2BAA2B;MACrCC,QAAQ,EAAE;IACd,CAAC;IACDC,YAAY,EAAE;MACVC,MAAM,EAAE,QAAQ;MAChBC,IAAI,EAAE,UAAU;MAChBC,CAAC,EAAE,kBAAkB;MACrBC,EAAE,EAAE,WAAW;MACfC,CAAC,EAAE,QAAQ;MACXC,EAAE,EAAE,SAAS;MACbC,CAAC,EAAE,WAAW;MACdC,EAAE,EAAE,YAAY;MAChBC,CAAC,EAAE,OAAO;MACVC,EAAE,EAAE,QAAQ;MACZC,CAAC,EAAE,SAAS;MACZC,EAAE,EAAE,UAAU;MACdC,CAAC,EAAE,MAAM;MACTC,EAAE,EAAE;IACR,CAAC;IACDC,sBAAsB,EAAE,cAAc;IACtCC,OAAO,EAAE,SAAAA,CAAUC,MAAM,EAAE;MACvB,OAAO,KAAK,GAAGA,MAAM;IACzB;EACJ,CAAC,CAAC;EAEF,OAAO/C,EAAE;AAEb,CAAE,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}