{"ast": null, "code": "import _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport toDate from \"../toDate/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name min\n * @category Common Helpers\n * @summary Returns the earliest of the given dates.\n *\n * @description\n * Returns the earliest of the given dates.\n *\n * @param {Date[]|Number[]} datesArray - the dates to compare\n * @returns {Date} - the earliest of the dates\n * @throws {TypeError} 1 argument required\n *\n * @example\n * // Which of these dates is the earliest?\n * const result = min([\n *   new Date(1989, 6, 10),\n *   new Date(1987, 1, 11),\n *   new Date(1995, 6, 2),\n *   new Date(1990, 0, 1)\n * ])\n * //=> Wed Feb 11 1987 00:00:00\n */\nexport default function min(dirtyDatesArray) {\n  requiredArgs(1, arguments);\n  var datesArray;\n  // `dirtyDatesArray` is Array, Set or Map, or object with custom `forEach` method\n  if (dirtyDatesArray && typeof dirtyDatesArray.forEach === 'function') {\n    datesArray = dirtyDatesArray;\n    // If `dirtyDatesArray` is Array-like Object, convert to Array.\n  } else if (_typeof(dirtyDatesArray) === 'object' && dirtyDatesArray !== null) {\n    datesArray = Array.prototype.slice.call(dirtyDatesArray);\n  } else {\n    // `dirtyDatesArray` is non-iterable, return Invalid Date\n    return new Date(NaN);\n  }\n  var result;\n  datesArray.forEach(function (dirtyDate) {\n    var currentDate = toDate(dirtyDate);\n    if (result === undefined || result > currentDate || isNaN(currentDate.getDate())) {\n      result = currentDate;\n    }\n  });\n  return result || new Date(NaN);\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}