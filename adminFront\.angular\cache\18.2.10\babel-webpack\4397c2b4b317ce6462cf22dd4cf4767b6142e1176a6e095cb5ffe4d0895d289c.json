{"ast": null, "code": "//! moment.js locale configuration\n//! locale : Hindi [hi]\n//! author : <PERSON><PERSON> : https://github.com/mayanks<PERSON>\n\n;\n(function (global, factory) {\n  typeof exports === 'object' && typeof module !== 'undefined' && typeof require === 'function' ? factory(require('../moment')) : typeof define === 'function' && define.amd ? define(['../moment'], factory) : factory(global.moment);\n})(this, function (moment) {\n  'use strict';\n\n  //! moment.js locale configuration\n  var symbolMap = {\n      1: '१',\n      2: '२',\n      3: '३',\n      4: '४',\n      5: '५',\n      6: '६',\n      7: '७',\n      8: '८',\n      9: '९',\n      0: '०'\n    },\n    numberMap = {\n      '१': '1',\n      '२': '2',\n      '३': '3',\n      '४': '4',\n      '५': '5',\n      '६': '6',\n      '७': '7',\n      '८': '8',\n      '९': '9',\n      '०': '0'\n    },\n    monthsParse = [/^जन/i, /^फ़र|फर/i, /^मार्च/i, /^अप्रै/i, /^मई/i, /^जून/i, /^जुल/i, /^अग/i, /^सितं|सित/i, /^अक्टू/i, /^नव|नवं/i, /^दिसं|दिस/i],\n    shortMonthsParse = [/^जन/i, /^फ़र/i, /^मार्च/i, /^अप्रै/i, /^मई/i, /^जून/i, /^जुल/i, /^अग/i, /^सित/i, /^अक्टू/i, /^नव/i, /^दिस/i];\n  var hi = moment.defineLocale('hi', {\n    months: {\n      format: 'जनवरी_फ़रवरी_मार्च_अप्रैल_मई_जून_जुलाई_अगस्त_सितम्बर_अक्टूबर_नवम्बर_दिसम्बर'.split('_'),\n      standalone: 'जनवरी_फरवरी_मार्च_अप्रैल_मई_जून_जुलाई_अगस्त_सितंबर_अक्टूबर_नवंबर_दिसंबर'.split('_')\n    },\n    monthsShort: 'जन._फ़र._मार्च_अप्रै._मई_जून_जुल._अग._सित._अक्टू._नव._दिस.'.split('_'),\n    weekdays: 'रविवार_सोमवार_मंगलवार_बुधवार_गुरूवार_शुक्रवार_शनिवार'.split('_'),\n    weekdaysShort: 'रवि_सोम_मंगल_बुध_गुरू_शुक्र_शनि'.split('_'),\n    weekdaysMin: 'र_सो_मं_बु_गु_शु_श'.split('_'),\n    longDateFormat: {\n      LT: 'A h:mm बजे',\n      LTS: 'A h:mm:ss बजे',\n      L: 'DD/MM/YYYY',\n      LL: 'D MMMM YYYY',\n      LLL: 'D MMMM YYYY, A h:mm बजे',\n      LLLL: 'dddd, D MMMM YYYY, A h:mm बजे'\n    },\n    monthsParse: monthsParse,\n    longMonthsParse: monthsParse,\n    shortMonthsParse: shortMonthsParse,\n    monthsRegex: /^(जनवरी|जन\\.?|फ़रवरी|फरवरी|फ़र\\.?|मार्च?|अप्रैल|अप्रै\\.?|मई?|जून?|जुलाई|जुल\\.?|अगस्त|अग\\.?|सितम्बर|सितंबर|सित\\.?|अक्टूबर|अक्टू\\.?|नवम्बर|नवंबर|नव\\.?|दिसम्बर|दिसंबर|दिस\\.?)/i,\n    monthsShortRegex: /^(जनवरी|जन\\.?|फ़रवरी|फरवरी|फ़र\\.?|मार्च?|अप्रैल|अप्रै\\.?|मई?|जून?|जुलाई|जुल\\.?|अगस्त|अग\\.?|सितम्बर|सितंबर|सित\\.?|अक्टूबर|अक्टू\\.?|नवम्बर|नवंबर|नव\\.?|दिसम्बर|दिसंबर|दिस\\.?)/i,\n    monthsStrictRegex: /^(जनवरी?|फ़रवरी|फरवरी?|मार्च?|अप्रैल?|मई?|जून?|जुलाई?|अगस्त?|सितम्बर|सितंबर|सित?\\.?|अक्टूबर|अक्टू\\.?|नवम्बर|नवंबर?|दिसम्बर|दिसंबर?)/i,\n    monthsShortStrictRegex: /^(जन\\.?|फ़र\\.?|मार्च?|अप्रै\\.?|मई?|जून?|जुल\\.?|अग\\.?|सित\\.?|अक्टू\\.?|नव\\.?|दिस\\.?)/i,\n    calendar: {\n      sameDay: '[आज] LT',\n      nextDay: '[कल] LT',\n      nextWeek: 'dddd, LT',\n      lastDay: '[कल] LT',\n      lastWeek: '[पिछले] dddd, LT',\n      sameElse: 'L'\n    },\n    relativeTime: {\n      future: '%s में',\n      past: '%s पहले',\n      s: 'कुछ ही क्षण',\n      ss: '%d सेकंड',\n      m: 'एक मिनट',\n      mm: '%d मिनट',\n      h: 'एक घंटा',\n      hh: '%d घंटे',\n      d: 'एक दिन',\n      dd: '%d दिन',\n      M: 'एक महीने',\n      MM: '%d महीने',\n      y: 'एक वर्ष',\n      yy: '%d वर्ष'\n    },\n    preparse: function (string) {\n      return string.replace(/[१२३४५६७८९०]/g, function (match) {\n        return numberMap[match];\n      });\n    },\n    postformat: function (string) {\n      return string.replace(/\\d/g, function (match) {\n        return symbolMap[match];\n      });\n    },\n    // Hindi notation for meridiems are quite fuzzy in practice. While there exists\n    // a rigid notion of a 'Pahar' it is not used as rigidly in modern Hindi.\n    meridiemParse: /रात|सुबह|दोपहर|शाम/,\n    meridiemHour: function (hour, meridiem) {\n      if (hour === 12) {\n        hour = 0;\n      }\n      if (meridiem === 'रात') {\n        return hour < 4 ? hour : hour + 12;\n      } else if (meridiem === 'सुबह') {\n        return hour;\n      } else if (meridiem === 'दोपहर') {\n        return hour >= 10 ? hour : hour + 12;\n      } else if (meridiem === 'शाम') {\n        return hour + 12;\n      }\n    },\n    meridiem: function (hour, minute, isLower) {\n      if (hour < 4) {\n        return 'रात';\n      } else if (hour < 10) {\n        return 'सुबह';\n      } else if (hour < 17) {\n        return 'दोपहर';\n      } else if (hour < 20) {\n        return 'शाम';\n      } else {\n        return 'रात';\n      }\n    },\n    week: {\n      dow: 0,\n      // Sunday is the first day of the week.\n      doy: 6 // The week that contains Jan 6th is the first week of the year.\n    }\n  });\n  return hi;\n});", "map": {"version": 3, "names": ["global", "factory", "exports", "module", "require", "define", "amd", "moment", "symbolMap", "numberMap", "<PERSON><PERSON><PERSON>e", "shortMonthsParse", "hi", "defineLocale", "months", "format", "split", "standalone", "monthsShort", "weekdays", "weekdaysShort", "weekdaysMin", "longDateFormat", "LT", "LTS", "L", "LL", "LLL", "LLLL", "longMonthsParse", "monthsRegex", "monthsShortRegex", "monthsStrictRegex", "monthsShortStrictRegex", "calendar", "sameDay", "nextDay", "nextWeek", "lastDay", "lastWeek", "same<PERSON><PERSON><PERSON>", "relativeTime", "future", "past", "s", "ss", "m", "mm", "h", "hh", "d", "dd", "M", "MM", "y", "yy", "preparse", "string", "replace", "match", "postformat", "meridiemParse", "meridiemHour", "hour", "meridiem", "minute", "isLower", "week", "dow", "doy"], "sources": ["C:/Users/<USER>/Documents/jeansalechange/adminFront/node_modules/moment/locale/hi.js"], "sourcesContent": ["//! moment.js locale configuration\n//! locale : Hindi [hi]\n//! author : <PERSON><PERSON> : https://github.com/mayanks<PERSON>\n\n;(function (global, factory) {\n   typeof exports === 'object' && typeof module !== 'undefined'\n       && typeof require === 'function' ? factory(require('../moment')) :\n   typeof define === 'function' && define.amd ? define(['../moment'], factory) :\n   factory(global.moment)\n}(this, (function (moment) { 'use strict';\n\n    //! moment.js locale configuration\n\n    var symbolMap = {\n            1: '१',\n            2: '२',\n            3: '३',\n            4: '४',\n            5: '५',\n            6: '६',\n            7: '७',\n            8: '८',\n            9: '९',\n            0: '०',\n        },\n        numberMap = {\n            '१': '1',\n            '२': '2',\n            '३': '3',\n            '४': '4',\n            '५': '5',\n            '६': '6',\n            '७': '7',\n            '८': '8',\n            '९': '9',\n            '०': '0',\n        },\n        monthsParse = [\n            /^जन/i,\n            /^फ़र|फर/i,\n            /^मार्च/i,\n            /^अप्रै/i,\n            /^मई/i,\n            /^जून/i,\n            /^जुल/i,\n            /^अग/i,\n            /^सितं|सित/i,\n            /^अक्टू/i,\n            /^नव|नवं/i,\n            /^दिसं|दिस/i,\n        ],\n        shortMonthsParse = [\n            /^जन/i,\n            /^फ़र/i,\n            /^मार्च/i,\n            /^अप्रै/i,\n            /^मई/i,\n            /^जून/i,\n            /^जुल/i,\n            /^अग/i,\n            /^सित/i,\n            /^अक्टू/i,\n            /^नव/i,\n            /^दिस/i,\n        ];\n\n    var hi = moment.defineLocale('hi', {\n        months: {\n            format: 'जनवरी_फ़रवरी_मार्च_अप्रैल_मई_जून_जुलाई_अगस्त_सितम्बर_अक्टूबर_नवम्बर_दिसम्बर'.split(\n                '_'\n            ),\n            standalone:\n                'जनवरी_फरवरी_मार्च_अप्रैल_मई_जून_जुलाई_अगस्त_सितंबर_अक्टूबर_नवंबर_दिसंबर'.split(\n                    '_'\n                ),\n        },\n        monthsShort:\n            'जन._फ़र._मार्च_अप्रै._मई_जून_जुल._अग._सित._अक्टू._नव._दिस.'.split('_'),\n        weekdays: 'रविवार_सोमवार_मंगलवार_बुधवार_गुरूवार_शुक्रवार_शनिवार'.split('_'),\n        weekdaysShort: 'रवि_सोम_मंगल_बुध_गुरू_शुक्र_शनि'.split('_'),\n        weekdaysMin: 'र_सो_मं_बु_गु_शु_श'.split('_'),\n        longDateFormat: {\n            LT: 'A h:mm बजे',\n            LTS: 'A h:mm:ss बजे',\n            L: 'DD/MM/YYYY',\n            LL: 'D MMMM YYYY',\n            LLL: 'D MMMM YYYY, A h:mm बजे',\n            LLLL: 'dddd, D MMMM YYYY, A h:mm बजे',\n        },\n\n        monthsParse: monthsParse,\n        longMonthsParse: monthsParse,\n        shortMonthsParse: shortMonthsParse,\n\n        monthsRegex:\n            /^(जनवरी|जन\\.?|फ़रवरी|फरवरी|फ़र\\.?|मार्च?|अप्रैल|अप्रै\\.?|मई?|जून?|जुलाई|जुल\\.?|अगस्त|अग\\.?|सितम्बर|सितंबर|सित\\.?|अक्टूबर|अक्टू\\.?|नवम्बर|नवंबर|नव\\.?|दिसम्बर|दिसंबर|दिस\\.?)/i,\n\n        monthsShortRegex:\n            /^(जनवरी|जन\\.?|फ़रवरी|फरवरी|फ़र\\.?|मार्च?|अप्रैल|अप्रै\\.?|मई?|जून?|जुलाई|जुल\\.?|अगस्त|अग\\.?|सितम्बर|सितंबर|सित\\.?|अक्टूबर|अक्टू\\.?|नवम्बर|नवंबर|नव\\.?|दिसम्बर|दिसंबर|दिस\\.?)/i,\n\n        monthsStrictRegex:\n            /^(जनवरी?|फ़रवरी|फरवरी?|मार्च?|अप्रैल?|मई?|जून?|जुलाई?|अगस्त?|सितम्बर|सितंबर|सित?\\.?|अक्टूबर|अक्टू\\.?|नवम्बर|नवंबर?|दिसम्बर|दिसंबर?)/i,\n\n        monthsShortStrictRegex:\n            /^(जन\\.?|फ़र\\.?|मार्च?|अप्रै\\.?|मई?|जून?|जुल\\.?|अग\\.?|सित\\.?|अक्टू\\.?|नव\\.?|दिस\\.?)/i,\n\n        calendar: {\n            sameDay: '[आज] LT',\n            nextDay: '[कल] LT',\n            nextWeek: 'dddd, LT',\n            lastDay: '[कल] LT',\n            lastWeek: '[पिछले] dddd, LT',\n            sameElse: 'L',\n        },\n        relativeTime: {\n            future: '%s में',\n            past: '%s पहले',\n            s: 'कुछ ही क्षण',\n            ss: '%d सेकंड',\n            m: 'एक मिनट',\n            mm: '%d मिनट',\n            h: 'एक घंटा',\n            hh: '%d घंटे',\n            d: 'एक दिन',\n            dd: '%d दिन',\n            M: 'एक महीने',\n            MM: '%d महीने',\n            y: 'एक वर्ष',\n            yy: '%d वर्ष',\n        },\n        preparse: function (string) {\n            return string.replace(/[१२३४५६७८९०]/g, function (match) {\n                return numberMap[match];\n            });\n        },\n        postformat: function (string) {\n            return string.replace(/\\d/g, function (match) {\n                return symbolMap[match];\n            });\n        },\n        // Hindi notation for meridiems are quite fuzzy in practice. While there exists\n        // a rigid notion of a 'Pahar' it is not used as rigidly in modern Hindi.\n        meridiemParse: /रात|सुबह|दोपहर|शाम/,\n        meridiemHour: function (hour, meridiem) {\n            if (hour === 12) {\n                hour = 0;\n            }\n            if (meridiem === 'रात') {\n                return hour < 4 ? hour : hour + 12;\n            } else if (meridiem === 'सुबह') {\n                return hour;\n            } else if (meridiem === 'दोपहर') {\n                return hour >= 10 ? hour : hour + 12;\n            } else if (meridiem === 'शाम') {\n                return hour + 12;\n            }\n        },\n        meridiem: function (hour, minute, isLower) {\n            if (hour < 4) {\n                return 'रात';\n            } else if (hour < 10) {\n                return 'सुबह';\n            } else if (hour < 17) {\n                return 'दोपहर';\n            } else if (hour < 20) {\n                return 'शाम';\n            } else {\n                return 'रात';\n            }\n        },\n        week: {\n            dow: 0, // Sunday is the first day of the week.\n            doy: 6, // The week that contains Jan 6th is the first week of the year.\n        },\n    });\n\n    return hi;\n\n})));\n"], "mappings": "AAAA;AACA;AACA;;AAEA;AAAE,WAAUA,MAAM,EAAEC,OAAO,EAAE;EAC1B,OAAOC,OAAO,KAAK,QAAQ,IAAI,OAAOC,MAAM,KAAK,WAAW,IACrD,OAAOC,OAAO,KAAK,UAAU,GAAGH,OAAO,CAACG,OAAO,CAAC,WAAW,CAAC,CAAC,GACpE,OAAOC,MAAM,KAAK,UAAU,IAAIA,MAAM,CAACC,GAAG,GAAGD,MAAM,CAAC,CAAC,WAAW,CAAC,EAAEJ,OAAO,CAAC,GAC3EA,OAAO,CAACD,MAAM,CAACO,MAAM,CAAC;AACzB,CAAC,EAAC,IAAI,EAAG,UAAUA,MAAM,EAAE;EAAE,YAAY;;EAErC;EAEA,IAAIC,SAAS,GAAG;MACR,CAAC,EAAE,GAAG;MACN,CAAC,EAAE,GAAG;MACN,CAAC,EAAE,GAAG;MACN,CAAC,EAAE,GAAG;MACN,CAAC,EAAE,GAAG;MACN,CAAC,EAAE,GAAG;MACN,CAAC,EAAE,GAAG;MACN,CAAC,EAAE,GAAG;MACN,CAAC,EAAE,GAAG;MACN,CAAC,EAAE;IACP,CAAC;IACDC,SAAS,GAAG;MACR,GAAG,EAAE,GAAG;MACR,GAAG,EAAE,GAAG;MACR,GAAG,EAAE,GAAG;MACR,GAAG,EAAE,GAAG;MACR,GAAG,EAAE,GAAG;MACR,GAAG,EAAE,GAAG;MACR,GAAG,EAAE,GAAG;MACR,GAAG,EAAE,GAAG;MACR,GAAG,EAAE,GAAG;MACR,GAAG,EAAE;IACT,CAAC;IACDC,WAAW,GAAG,CACV,MAAM,EACN,UAAU,EACV,SAAS,EACT,SAAS,EACT,MAAM,EACN,OAAO,EACP,OAAO,EACP,MAAM,EACN,YAAY,EACZ,SAAS,EACT,UAAU,EACV,YAAY,CACf;IACDC,gBAAgB,GAAG,CACf,MAAM,EACN,OAAO,EACP,SAAS,EACT,SAAS,EACT,MAAM,EACN,OAAO,EACP,OAAO,EACP,MAAM,EACN,OAAO,EACP,SAAS,EACT,MAAM,EACN,OAAO,CACV;EAEL,IAAIC,EAAE,GAAGL,MAAM,CAACM,YAAY,CAAC,IAAI,EAAE;IAC/BC,MAAM,EAAE;MACJC,MAAM,EAAE,6EAA6E,CAACC,KAAK,CACvF,GACJ,CAAC;MACDC,UAAU,EACN,yEAAyE,CAACD,KAAK,CAC3E,GACJ;IACR,CAAC;IACDE,WAAW,EACP,4DAA4D,CAACF,KAAK,CAAC,GAAG,CAAC;IAC3EG,QAAQ,EAAE,sDAAsD,CAACH,KAAK,CAAC,GAAG,CAAC;IAC3EI,aAAa,EAAE,iCAAiC,CAACJ,KAAK,CAAC,GAAG,CAAC;IAC3DK,WAAW,EAAE,oBAAoB,CAACL,KAAK,CAAC,GAAG,CAAC;IAC5CM,cAAc,EAAE;MACZC,EAAE,EAAE,YAAY;MAChBC,GAAG,EAAE,eAAe;MACpBC,CAAC,EAAE,YAAY;MACfC,EAAE,EAAE,aAAa;MACjBC,GAAG,EAAE,yBAAyB;MAC9BC,IAAI,EAAE;IACV,CAAC;IAEDlB,WAAW,EAAEA,WAAW;IACxBmB,eAAe,EAAEnB,WAAW;IAC5BC,gBAAgB,EAAEA,gBAAgB;IAElCmB,WAAW,EACP,8KAA8K;IAElLC,gBAAgB,EACZ,8KAA8K;IAElLC,iBAAiB,EACb,sIAAsI;IAE1IC,sBAAsB,EAClB,qFAAqF;IAEzFC,QAAQ,EAAE;MACNC,OAAO,EAAE,SAAS;MAClBC,OAAO,EAAE,SAAS;MAClBC,QAAQ,EAAE,UAAU;MACpBC,OAAO,EAAE,SAAS;MAClBC,QAAQ,EAAE,kBAAkB;MAC5BC,QAAQ,EAAE;IACd,CAAC;IACDC,YAAY,EAAE;MACVC,MAAM,EAAE,QAAQ;MAChBC,IAAI,EAAE,SAAS;MACfC,CAAC,EAAE,aAAa;MAChBC,EAAE,EAAE,UAAU;MACdC,CAAC,EAAE,SAAS;MACZC,EAAE,EAAE,SAAS;MACbC,CAAC,EAAE,SAAS;MACZC,EAAE,EAAE,SAAS;MACbC,CAAC,EAAE,QAAQ;MACXC,EAAE,EAAE,QAAQ;MACZC,CAAC,EAAE,UAAU;MACbC,EAAE,EAAE,UAAU;MACdC,CAAC,EAAE,SAAS;MACZC,EAAE,EAAE;IACR,CAAC;IACDC,QAAQ,EAAE,SAAAA,CAAUC,MAAM,EAAE;MACxB,OAAOA,MAAM,CAACC,OAAO,CAAC,eAAe,EAAE,UAAUC,KAAK,EAAE;QACpD,OAAOlD,SAAS,CAACkD,KAAK,CAAC;MAC3B,CAAC,CAAC;IACN,CAAC;IACDC,UAAU,EAAE,SAAAA,CAAUH,MAAM,EAAE;MAC1B,OAAOA,MAAM,CAACC,OAAO,CAAC,KAAK,EAAE,UAAUC,KAAK,EAAE;QAC1C,OAAOnD,SAAS,CAACmD,KAAK,CAAC;MAC3B,CAAC,CAAC;IACN,CAAC;IACD;IACA;IACAE,aAAa,EAAE,oBAAoB;IACnCC,YAAY,EAAE,SAAAA,CAAUC,IAAI,EAAEC,QAAQ,EAAE;MACpC,IAAID,IAAI,KAAK,EAAE,EAAE;QACbA,IAAI,GAAG,CAAC;MACZ;MACA,IAAIC,QAAQ,KAAK,KAAK,EAAE;QACpB,OAAOD,IAAI,GAAG,CAAC,GAAGA,IAAI,GAAGA,IAAI,GAAG,EAAE;MACtC,CAAC,MAAM,IAAIC,QAAQ,KAAK,MAAM,EAAE;QAC5B,OAAOD,IAAI;MACf,CAAC,MAAM,IAAIC,QAAQ,KAAK,OAAO,EAAE;QAC7B,OAAOD,IAAI,IAAI,EAAE,GAAGA,IAAI,GAAGA,IAAI,GAAG,EAAE;MACxC,CAAC,MAAM,IAAIC,QAAQ,KAAK,KAAK,EAAE;QAC3B,OAAOD,IAAI,GAAG,EAAE;MACpB;IACJ,CAAC;IACDC,QAAQ,EAAE,SAAAA,CAAUD,IAAI,EAAEE,MAAM,EAAEC,OAAO,EAAE;MACvC,IAAIH,IAAI,GAAG,CAAC,EAAE;QACV,OAAO,KAAK;MAChB,CAAC,MAAM,IAAIA,IAAI,GAAG,EAAE,EAAE;QAClB,OAAO,MAAM;MACjB,CAAC,MAAM,IAAIA,IAAI,GAAG,EAAE,EAAE;QAClB,OAAO,OAAO;MAClB,CAAC,MAAM,IAAIA,IAAI,GAAG,EAAE,EAAE;QAClB,OAAO,KAAK;MAChB,CAAC,MAAM;QACH,OAAO,KAAK;MAChB;IACJ,CAAC;IACDI,IAAI,EAAE;MACFC,GAAG,EAAE,CAAC;MAAE;MACRC,GAAG,EAAE,CAAC,CAAE;IACZ;EACJ,CAAC,CAAC;EAEF,OAAOzD,EAAE;AAEb,CAAE,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}