{"ast": null, "code": "import { of as observableOf } from 'rxjs';\nimport { TrafficChartData } from '../data/traffic-chart';\nimport * as i0 from \"@angular/core\";\nexport let TrafficChartService = /*#__PURE__*/(() => {\n  class TrafficChartService extends TrafficChartData {\n    constructor() {\n      super(...arguments);\n      this.data = [300, 520, 435, 530, 730, 620, 660, 860];\n    }\n    getTrafficChartData() {\n      return observableOf(this.data);\n    }\n    static {\n      this.ɵfac = /*@__PURE__*/(() => {\n        let ɵTrafficChartService_BaseFactory;\n        return function TrafficChartService_Factory(__ngFactoryType__) {\n          return (ɵTrafficChartService_BaseFactory || (ɵTrafficChartService_BaseFactory = i0.ɵɵgetInheritedFactory(TrafficChartService)))(__ngFactoryType__ || TrafficChartService);\n        };\n      })();\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: TrafficChartService,\n        factory: TrafficChartService.ɵfac\n      });\n    }\n  }\n  return TrafficChartService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}