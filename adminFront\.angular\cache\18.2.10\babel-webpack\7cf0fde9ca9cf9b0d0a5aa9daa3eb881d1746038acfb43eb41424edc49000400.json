{"ast": null, "code": "import { ApproveWaitingComponent } from '../approve-waiting/approve-waiting.component';\nimport * as i0 from \"@angular/core\";\nexport let ApproveWaiting3Component = /*#__PURE__*/(() => {\n  class ApproveWaiting3Component {\n    static {\n      this.ɵfac = function ApproveWaiting3Component_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || ApproveWaiting3Component)();\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: ApproveWaiting3Component,\n        selectors: [[\"app-approve-waiting-buildcasefile\"]],\n        standalone: true,\n        features: [i0.ɵɵStandaloneFeature],\n        decls: 1,\n        vars: 1,\n        consts: [[3, \"type\"]],\n        template: function ApproveWaiting3Component_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelement(0, \"app-approve-waiting\", 0);\n          }\n          if (rf & 2) {\n            i0.ɵɵproperty(\"type\", 3);\n          }\n        },\n        dependencies: [ApproveWaitingComponent],\n        styles: [\"[_nghost-%COMP%]{display:block}\"]\n      });\n    }\n  }\n  return ApproveWaiting3Component;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}