{"ast": null, "code": "import { of as observableOf } from 'rxjs';\nimport { UserActivityData } from '../data/user-activity';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"./periods.service\";\nexport class UserActivityService extends UserActivityData {\n  generateUserActivityRandomData(date) {\n    return {\n      date,\n      pagesVisitCount: this.getRandom(1000),\n      deltaUp: this.getRandom(1) % 2 === 0,\n      newVisits: this.getRandom(100)\n    };\n  }\n  constructor(periods) {\n    super();\n    this.periods = periods;\n    this.getRandom = roundTo => Math.round(Math.random() * roundTo);\n    this.data = {};\n    this.data = {\n      week: this.getDataWeek(),\n      month: this.getDataMonth(),\n      year: this.getDataYear()\n    };\n  }\n  getDataWeek() {\n    return this.periods.getWeeks().map(week => {\n      return this.generateUserActivityRandomData(week);\n    });\n  }\n  getDataMonth() {\n    const currentDate = new Date();\n    const days = currentDate.getDate();\n    const month = this.periods.getMonths()[currentDate.getMonth()];\n    return Array.from(Array(days)).map((_, index) => {\n      const date = `${index + 1} ${month}`;\n      return this.generateUserActivityRandomData(date);\n    });\n  }\n  getDataYear() {\n    return this.periods.getYears().map(year => {\n      return this.generateUserActivityRandomData(year);\n    });\n  }\n  getUserActivityData(period) {\n    return observableOf(this.data[period]);\n  }\n  static {\n    this.ɵfac = function UserActivityService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || UserActivityService)(i0.ɵɵinject(i1.PeriodsService));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: UserActivityService,\n      factory: UserActivityService.ɵfac\n    });\n  }\n}", "map": {"version": 3, "names": ["of", "observableOf", "UserActivityData", "UserActivityService", "generateUserActivityRandomData", "date", "pagesVisitCount", "getRandom", "deltaUp", "newVisits", "constructor", "periods", "roundTo", "Math", "round", "random", "data", "week", "getDataWeek", "month", "getDataMonth", "year", "getDataYear", "getWeeks", "map", "currentDate", "Date", "days", "getDate", "getMonths", "getMonth", "Array", "from", "_", "index", "getYears", "getUserActivityData", "period", "i0", "ɵɵinject", "i1", "PeriodsService", "factory", "ɵfac"], "sources": ["C:\\Users\\<USER>\\Documents\\jeansalechange\\adminFront\\src\\app\\@core\\mock\\user-activity.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport { of as observableOf,  Observable } from 'rxjs';\r\nimport { PeriodsService } from './periods.service';\r\nimport { UserActive, UserActivityData } from '../data/user-activity';\r\n\r\n@Injectable()\r\nexport class UserActivityService extends UserActivityData {\r\n\r\n  private getRandom = (roundTo: number) => Math.round(Math.random() * roundTo);\r\n  private generateUserActivityRandomData(date: any) {\r\n    return {\r\n      date,\r\n      pagesVisitCount: this.getRandom(1000),\r\n      deltaUp: this.getRandom(1) % 2 === 0,\r\n      newVisits: this.getRandom(100),\r\n    };\r\n  }\r\n\r\n  data:any = {};\r\n\r\n  constructor(private periods: PeriodsService) {\r\n    super();\r\n    this.data = {\r\n      week: this.getDataWeek(),\r\n      month: this.getDataMonth(),\r\n      year: this.getDataYear(),\r\n    };\r\n  }\r\n\r\n  private getDataWeek(): UserActive[] {\r\n    return this.periods.getWeeks().map((week) => {\r\n      return this.generateUserActivityRandomData(week);\r\n    });\r\n  }\r\n\r\n  private getDataMonth(): UserActive[] {\r\n    const currentDate = new Date();\r\n    const days = currentDate.getDate();\r\n    const month = this.periods.getMonths()[currentDate.getMonth()];\r\n\r\n    return Array.from(Array(days)).map((_, index) => {\r\n      const date = `${index + 1} ${month}`;\r\n\r\n      return this.generateUserActivityRandomData(date);\r\n    });\r\n  }\r\n\r\n  private getDataYear(): UserActive[] {\r\n    return this.periods.getYears().map((year) => {\r\n      return this.generateUserActivityRandomData(year);\r\n    });\r\n  }\r\n\r\n  getUserActivityData(period: string): Observable<UserActive[]> {\r\n    return observableOf(this.data[period]);\r\n  }\r\n}\r\n"], "mappings": "AACA,SAASA,EAAE,IAAIC,YAAY,QAAqB,MAAM;AAEtD,SAAqBC,gBAAgB,QAAQ,uBAAuB;;;AAGpE,OAAM,MAAOC,mBAAoB,SAAQD,gBAAgB;EAG/CE,8BAA8BA,CAACC,IAAS;IAC9C,OAAO;MACLA,IAAI;MACJC,eAAe,EAAE,IAAI,CAACC,SAAS,CAAC,IAAI,CAAC;MACrCC,OAAO,EAAE,IAAI,CAACD,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC;MACpCE,SAAS,EAAE,IAAI,CAACF,SAAS,CAAC,GAAG;KAC9B;EACH;EAIAG,YAAoBC,OAAuB;IACzC,KAAK,EAAE;IADW,KAAAA,OAAO,GAAPA,OAAO;IAZnB,KAAAJ,SAAS,GAAIK,OAAe,IAAKC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,EAAE,GAAGH,OAAO,CAAC;IAU5E,KAAAI,IAAI,GAAO,EAAE;IAIX,IAAI,CAACA,IAAI,GAAG;MACVC,IAAI,EAAE,IAAI,CAACC,WAAW,EAAE;MACxBC,KAAK,EAAE,IAAI,CAACC,YAAY,EAAE;MAC1BC,IAAI,EAAE,IAAI,CAACC,WAAW;KACvB;EACH;EAEQJ,WAAWA,CAAA;IACjB,OAAO,IAAI,CAACP,OAAO,CAACY,QAAQ,EAAE,CAACC,GAAG,CAAEP,IAAI,IAAI;MAC1C,OAAO,IAAI,CAACb,8BAA8B,CAACa,IAAI,CAAC;IAClD,CAAC,CAAC;EACJ;EAEQG,YAAYA,CAAA;IAClB,MAAMK,WAAW,GAAG,IAAIC,IAAI,EAAE;IAC9B,MAAMC,IAAI,GAAGF,WAAW,CAACG,OAAO,EAAE;IAClC,MAAMT,KAAK,GAAG,IAAI,CAACR,OAAO,CAACkB,SAAS,EAAE,CAACJ,WAAW,CAACK,QAAQ,EAAE,CAAC;IAE9D,OAAOC,KAAK,CAACC,IAAI,CAACD,KAAK,CAACJ,IAAI,CAAC,CAAC,CAACH,GAAG,CAAC,CAACS,CAAC,EAAEC,KAAK,KAAI;MAC9C,MAAM7B,IAAI,GAAG,GAAG6B,KAAK,GAAG,CAAC,IAAIf,KAAK,EAAE;MAEpC,OAAO,IAAI,CAACf,8BAA8B,CAACC,IAAI,CAAC;IAClD,CAAC,CAAC;EACJ;EAEQiB,WAAWA,CAAA;IACjB,OAAO,IAAI,CAACX,OAAO,CAACwB,QAAQ,EAAE,CAACX,GAAG,CAAEH,IAAI,IAAI;MAC1C,OAAO,IAAI,CAACjB,8BAA8B,CAACiB,IAAI,CAAC;IAClD,CAAC,CAAC;EACJ;EAEAe,mBAAmBA,CAACC,MAAc;IAChC,OAAOpC,YAAY,CAAC,IAAI,CAACe,IAAI,CAACqB,MAAM,CAAC,CAAC;EACxC;;;uCAjDWlC,mBAAmB,EAAAmC,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,cAAA;IAAA;EAAA;;;aAAnBtC,mBAAmB;MAAAuC,OAAA,EAAnBvC,mBAAmB,CAAAwC;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}