{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"./api-configuration\";\nimport * as i2 from \"@angular/common/http\";\n/**\n * Base class for services\n */\nexport let BaseService = /*#__PURE__*/(() => {\n  class BaseService {\n    constructor(config, http) {\n      this.config = config;\n      this.http = http;\n    }\n    /**\n     * Returns the root url for all operations in this service. If not set directly in this\n     * service, will fallback to `ApiConfiguration.rootUrl`.\n     */\n    get rootUrl() {\n      return this._rootUrl || this.config.rootUrl;\n    }\n    /**\n     * Sets the root URL for API operations in this service.\n     */\n    set rootUrl(rootUrl) {\n      this._rootUrl = rootUrl;\n    }\n    static {\n      this.ɵfac = function BaseService_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || BaseService)(i0.ɵɵinject(i1.ApiConfiguration), i0.ɵɵinject(i2.HttpClient));\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: BaseService,\n        factory: BaseService.ɵfac\n      });\n    }\n  }\n  return BaseService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}