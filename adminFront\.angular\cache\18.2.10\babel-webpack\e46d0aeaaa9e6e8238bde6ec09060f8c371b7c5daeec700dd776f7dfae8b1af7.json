{"ast": null, "code": "import { <PERSON><PERSON><PERSON><PERSON> } from 'primeng/dom';\nimport { DOCUMENT, isPlatformBrowser, CommonModule } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { inject, PLATFORM_ID, ElementRef, booleanAttribute, Directive, Input, NgModule } from '@angular/core';\n\n/**\n * Focus Trap keeps focus within a certain DOM element while tabbing.\n * @group Components\n */\nlet FocusTrap = /*#__PURE__*/(() => {\n  class FocusTrap {\n    /**\n     * When set as true, focus wouldn't be managed.\n     * @group Props\n     */\n    pFocusTrapDisabled = false;\n    platformId = inject(PLATFORM_ID);\n    host = inject(ElementRef);\n    document = inject(DOCUMENT);\n    firstHiddenFocusableElement;\n    lastHiddenFocusableElement;\n    ngOnInit() {\n      if (isPlatformBrowser(this.platformId) && !this.pFocusTrapDisabled) {\n        !this.firstHiddenFocusableElement && !this.lastHiddenFocusableElement && this.createHiddenFocusableElements();\n      }\n    }\n    ngOnChanges(changes) {\n      if (changes.pFocusTrapDisabled && isPlatformBrowser(this.platformId)) {\n        if (changes.pFocusTrapDisabled.currentValue) {\n          this.removeHiddenFocusableElements();\n        } else {\n          this.createHiddenFocusableElements();\n        }\n      }\n    }\n    removeHiddenFocusableElements() {\n      if (this.firstHiddenFocusableElement && this.firstHiddenFocusableElement.parentNode) {\n        this.firstHiddenFocusableElement.parentNode.removeChild(this.firstHiddenFocusableElement);\n      }\n      if (this.lastHiddenFocusableElement && this.lastHiddenFocusableElement.parentNode) {\n        this.lastHiddenFocusableElement.parentNode.removeChild(this.lastHiddenFocusableElement);\n      }\n    }\n    getComputedSelector(selector) {\n      return `:not(.p-hidden-focusable):not([data-p-hidden-focusable=\"true\"])${selector ?? ''}`;\n    }\n    createHiddenFocusableElements() {\n      const tabindex = '0';\n      const createFocusableElement = onFocus => {\n        return DomHandler.createElement('span', {\n          class: 'p-hidden-accessible p-hidden-focusable',\n          tabindex,\n          role: 'presentation',\n          'data-p-hidden-accessible': true,\n          'data-p-hidden-focusable': true,\n          onFocus: onFocus?.bind(this)\n        });\n      };\n      this.firstHiddenFocusableElement = createFocusableElement(this.onFirstHiddenElementFocus);\n      this.lastHiddenFocusableElement = createFocusableElement(this.onLastHiddenElementFocus);\n      this.firstHiddenFocusableElement.setAttribute('data-pc-section', 'firstfocusableelement');\n      this.lastHiddenFocusableElement.setAttribute('data-pc-section', 'lastfocusableelement');\n      this.host.nativeElement.prepend(this.firstHiddenFocusableElement);\n      this.host.nativeElement.append(this.lastHiddenFocusableElement);\n    }\n    onFirstHiddenElementFocus(event) {\n      const {\n        currentTarget,\n        relatedTarget\n      } = event;\n      const focusableElement = relatedTarget === this.lastHiddenFocusableElement || !this.host.nativeElement?.contains(relatedTarget) ? DomHandler.getFirstFocusableElement(currentTarget.parentElement, ':not(.p-hidden-focusable)') : this.lastHiddenFocusableElement;\n      DomHandler.focus(focusableElement);\n    }\n    onLastHiddenElementFocus(event) {\n      const {\n        currentTarget,\n        relatedTarget\n      } = event;\n      const focusableElement = relatedTarget === this.firstHiddenFocusableElement || !this.host.nativeElement?.contains(relatedTarget) ? DomHandler.getLastFocusableElement(currentTarget.parentElement, ':not(.p-hidden-focusable)') : this.firstHiddenFocusableElement;\n      DomHandler.focus(focusableElement);\n    }\n    static ɵfac = function FocusTrap_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || FocusTrap)();\n    };\n    static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: FocusTrap,\n      selectors: [[\"\", \"pFocusTrap\", \"\"]],\n      hostAttrs: [1, \"p-element\"],\n      inputs: {\n        pFocusTrapDisabled: [2, \"pFocusTrapDisabled\", \"pFocusTrapDisabled\", booleanAttribute]\n      },\n      features: [i0.ɵɵInputTransformsFeature, i0.ɵɵNgOnChangesFeature]\n    });\n  }\n  return FocusTrap;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet FocusTrapModule = /*#__PURE__*/(() => {\n  class FocusTrapModule {\n    static ɵfac = function FocusTrapModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || FocusTrapModule)();\n    };\n    static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: FocusTrapModule\n    });\n    static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      imports: [CommonModule]\n    });\n  }\n  return FocusTrapModule;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { FocusTrap, FocusTrapModule };\n//# sourceMappingURL=primeng-focustrap.mjs.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}