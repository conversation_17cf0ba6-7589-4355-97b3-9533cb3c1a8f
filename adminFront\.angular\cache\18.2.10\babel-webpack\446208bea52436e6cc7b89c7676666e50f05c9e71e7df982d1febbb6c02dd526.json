{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { NbCardModule, NbButtonModule } from '@nebular/theme';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nimport * as i2 from \"@angular/forms\";\nimport * as i3 from \"@nebular/theme\";\nfunction TemplateViewerComponent_div_17_div_30_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 50);\n    i0.ɵɵelement(1, \"i\", 51);\n    i0.ɵɵtext(2, \" \\u66AB\\u7121\\u53EF\\u9078\\u9805\\u76EE \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction TemplateViewerComponent_div_17_div_31_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 52)(1, \"label\", 53)(2, \"input\", 54);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function TemplateViewerComponent_div_17_div_31_Template_input_ngModelChange_2_listener($event) {\n      const item_r4 = i0.ɵɵrestoreView(_r3).$implicit;\n      i0.ɵɵtwoWayBindingSet(item_r4.selected, $event) || (item_r4.selected = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 55)(4, \"div\", 56);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"small\", 57);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const item_r4 = ctx.$implicit;\n    const i_r5 = ctx.index;\n    i0.ɵɵadvance(2);\n    i0.ɵɵpropertyInterpolate1(\"name\", \"item_\", i_r5, \"\");\n    i0.ɵɵtwoWayProperty(\"ngModel\", item_r4.selected);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(item_r4.CRequirement || item_r4.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r4.CGroupName || item_r4.description);\n  }\n}\nfunction TemplateViewerComponent_div_17_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 29)(1, \"div\", 30)(2, \"div\", 31)(3, \"h6\", 32);\n    i0.ɵɵelement(4, \"i\", 33);\n    i0.ɵɵtext(5, \"\\u65B0\\u589E\\u6A21\\u677F \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 34)(7, \"form\", 35);\n    i0.ɵɵlistener(\"ngSubmit\", function TemplateViewerComponent_div_17_Template_form_ngSubmit_7_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.saveNewTemplate());\n    });\n    i0.ɵɵelementStart(8, \"div\", 36)(9, \"div\", 37)(10, \"div\", 38)(11, \"label\", 39)(12, \"strong\");\n    i0.ɵɵtext(13, \"\\u6A21\\u677F\\u540D\\u7A31 \");\n    i0.ɵɵelementStart(14, \"span\", 40);\n    i0.ɵɵtext(15, \"*\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(16, \"input\", 41);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function TemplateViewerComponent_div_17_Template_input_ngModelChange_16_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r1.newTemplate.name, $event) || (ctx_r1.newTemplate.name = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(17, \"div\", 37)(18, \"div\", 38)(19, \"label\", 39)(20, \"strong\");\n    i0.ɵɵtext(21, \"\\u6A21\\u677F\\u63CF\\u8FF0\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(22, \"input\", 42);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function TemplateViewerComponent_div_17_Template_input_ngModelChange_22_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r1.newTemplate.description, $event) || (ctx_r1.newTemplate.description = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(23, \"div\", 38)(24, \"label\", 39)(25, \"strong\");\n    i0.ɵɵtext(26, \"\\u9078\\u64C7\\u8981\\u52A0\\u5165\\u6A21\\u677F\\u7684\\u9805\\u76EE \");\n    i0.ɵɵelementStart(27, \"span\", 40);\n    i0.ɵɵtext(28, \"*\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(29, \"div\", 43);\n    i0.ɵɵtemplate(30, TemplateViewerComponent_div_17_div_30_Template, 3, 0, \"div\", 44)(31, TemplateViewerComponent_div_17_div_31_Template, 8, 5, \"div\", 45);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(32, \"div\", 46)(33, \"button\", 47);\n    i0.ɵɵlistener(\"click\", function TemplateViewerComponent_div_17_Template_button_click_33_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.cancelAddTemplate());\n    });\n    i0.ɵɵelement(34, \"i\", 28);\n    i0.ɵɵtext(35, \"\\u53D6\\u6D88 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(36, \"button\", 48);\n    i0.ɵɵelement(37, \"i\", 49);\n    i0.ɵɵtext(38, \"\\u5132\\u5B58\\u6A21\\u677F \");\n    i0.ɵɵelementEnd()()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(16);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.newTemplate.name);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.newTemplate.description);\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.availableData.length === 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.availableData);\n  }\n}\nfunction TemplateViewerComponent_div_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 58)(1, \"small\", 57);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\" \\u627E\\u5230 \", ctx_r1.filteredTemplates.length, \" \\u500B\\u7B26\\u5408\\u300C\", ctx_r1.searchKeyword, \"\\u300D\\u7684\\u6A21\\u677F \");\n  }\n}\nfunction TemplateViewerComponent_tr_31_button_12_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 63);\n    i0.ɵɵlistener(\"click\", function TemplateViewerComponent_tr_31_button_12_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const tpl_r7 = i0.ɵɵnextContext().$implicit;\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(tpl_r7.TemplateID && ctx_r1.onDeleteTemplate(tpl_r7.TemplateID));\n    });\n    i0.ɵɵelement(1, \"i\", 64);\n    i0.ɵɵtext(2, \" \\u522A\\u9664 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction TemplateViewerComponent_tr_31_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\")(2, \"strong\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"td\")(5, \"span\", 57);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"td\", 26)(8, \"div\", 59)(9, \"button\", 60);\n    i0.ɵɵlistener(\"click\", function TemplateViewerComponent_tr_31_Template_button_click_9_listener() {\n      const tpl_r7 = i0.ɵɵrestoreView(_r6).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onSelectTemplate(tpl_r7));\n    });\n    i0.ɵɵelement(10, \"i\", 61);\n    i0.ɵɵtext(11, \" \\u67E5\\u770B \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(12, TemplateViewerComponent_tr_31_button_12_Template, 3, 0, \"button\", 62);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const tpl_r7 = ctx.$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(tpl_r7.TemplateName);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(tpl_r7.Description || \"\\u7121\\u63CF\\u8FF0\");\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngIf\", tpl_r7.TemplateID);\n  }\n}\nfunction TemplateViewerComponent_tr_32_small_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"small\", 57);\n    i0.ɵɵtext(1, \" \\u8ACB\\u5617\\u8A66\\u5176\\u4ED6\\u95DC\\u9375\\u5B57\\u6216 \");\n    i0.ɵɵelementStart(2, \"a\", 70);\n    i0.ɵɵlistener(\"click\", function TemplateViewerComponent_tr_32_small_6_Template_a_click_2_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.clearSearch());\n    });\n    i0.ɵɵtext(3, \"\\u6E05\\u9664\\u641C\\u5C0B\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction TemplateViewerComponent_tr_32_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 65)(2, \"div\", 66);\n    i0.ɵɵelement(3, \"i\", 67);\n    i0.ɵɵelementStart(4, \"p\", 68);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(6, TemplateViewerComponent_tr_32_small_6_Template, 4, 0, \"small\", 69);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.searchKeyword ? \"\\u627E\\u4E0D\\u5230\\u7B26\\u5408\\u689D\\u4EF6\\u7684\\u6A21\\u677F\" : \"\\u66AB\\u7121\\u6A21\\u677F\", \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.searchKeyword);\n  }\n}\nfunction TemplateViewerComponent_div_33_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 82)(1, \"strong\");\n    i0.ɵɵtext(2, \"\\u63CF\\u8FF0\\uFF1A\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\", 57);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r1.selectedTemplate.Description);\n  }\n}\nfunction TemplateViewerComponent_div_33_div_14_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 85)(1, \"div\", 86)(2, \"span\", 87);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"div\", 88)(5, \"div\", 89)(6, \"strong\");\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 90);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const detail_r11 = ctx.$implicit;\n    const i_r12 = ctx.index;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i_r12 + 1);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\"\", detail_r11.FieldName, \":\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", detail_r11.FieldValue, \" \");\n  }\n}\nfunction TemplateViewerComponent_div_33_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 83);\n    i0.ɵɵtemplate(1, TemplateViewerComponent_div_33_div_14_div_1_Template, 10, 3, \"div\", 84);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.currentTemplateDetails);\n  }\n}\nfunction TemplateViewerComponent_div_33_ng_template_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 91);\n    i0.ɵɵelement(1, \"i\", 92);\n    i0.ɵɵelementStart(2, \"p\", 68);\n    i0.ɵɵtext(3, \"\\u6B64\\u6A21\\u677F\\u66AB\\u7121\\u5167\\u5BB9\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction TemplateViewerComponent_div_33_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 71)(1, \"div\", 72)(2, \"h6\", 32);\n    i0.ɵɵelement(3, \"i\", 73);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 74);\n    i0.ɵɵlistener(\"click\", function TemplateViewerComponent_div_33_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.closeTemplateDetail());\n    });\n    i0.ɵɵelement(6, \"i\", 75);\n    i0.ɵɵtext(7, \" \\u95DC\\u9589 \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 76);\n    i0.ɵɵtemplate(9, TemplateViewerComponent_div_33_div_9_Template, 5, 1, \"div\", 77);\n    i0.ɵɵelementStart(10, \"div\", 78)(11, \"h6\", 79);\n    i0.ɵɵelement(12, \"i\", 80);\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(14, TemplateViewerComponent_div_33_div_14_Template, 2, 1, \"div\", 81)(15, TemplateViewerComponent_div_33_ng_template_15_Template, 4, 0, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const noDetails_r13 = i0.ɵɵreference(16);\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \\u6A21\\u677F\\u7D30\\u7BC0\\uFF1A\", ctx_r1.selectedTemplate.TemplateName, \" \");\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.selectedTemplate.Description);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \\u6A21\\u677F\\u5167\\u5BB9 (\", ctx_r1.currentTemplateDetails.length, \" \\u9805) \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.currentTemplateDetails.length > 0)(\"ngIfElse\", noDetails_r13);\n  }\n}\nexport class TemplateViewerComponent {\n  constructor() {\n    this.availableData = []; // 父元件傳入的可選資料 (包含關聯主檔ID和名稱)\n    this.moduleType = 'Requirement'; // 模組類型，用於區分資料來源\n    this.selectTemplate = new EventEmitter();\n    this.close = new EventEmitter(); // 關閉事件\n    // 內部管理的模板資料\n    this.templates = [];\n    this.templateDetails = [];\n    this.selectedTemplate = null;\n    // 查詢功能\n    this.searchKeyword = '';\n    this.filteredTemplates = [];\n    // 新增模板表單\n    this.showAddForm = false;\n    this.newTemplate = {\n      name: '',\n      description: '',\n      selectedItems: []\n    };\n  }\n  ngOnInit() {\n    this.loadTemplates();\n    this.updateFilteredTemplates();\n  }\n  ngOnChanges() {\n    this.updateFilteredTemplates();\n  }\n  // TODO: 替換為實際的API調用\n  loadTemplates() {\n    // 模擬API調用 - 載入模板列表\n    console.log('載入模板 API 調用:', {\n      moduleType: this.moduleType\n    });\n    this.templates = [{\n      TemplateID: 1,\n      TemplateName: '需求模板A',\n      Description: '包含基本工程項目的模板'\n    }, {\n      TemplateID: 2,\n      TemplateName: '需求模板B',\n      Description: '包含進階工程項目的模板'\n    }];\n    // 載入對應模組類型的模板詳情\n    this.templateDetails = [{\n      TemplateDetailID: 1,\n      TemplateID: 1,\n      RefID: 101,\n      ModuleType: this.moduleType,\n      FieldName: this.getFieldName({}),\n      FieldValue: '工程項目A'\n    }, {\n      TemplateDetailID: 2,\n      TemplateID: 1,\n      RefID: 102,\n      ModuleType: this.moduleType,\n      FieldName: this.getFieldName({}),\n      FieldValue: '工程項目B'\n    }, {\n      TemplateDetailID: 3,\n      TemplateID: 2,\n      RefID: 201,\n      ModuleType: this.moduleType,\n      FieldName: this.getFieldName({}),\n      FieldValue: '工程項目C'\n    }];\n  }\n  // 更新過濾後的模板列表\n  updateFilteredTemplates() {\n    if (!this.searchKeyword.trim()) {\n      this.filteredTemplates = [...this.templates];\n    } else {\n      const keyword = this.searchKeyword.toLowerCase();\n      this.filteredTemplates = this.templates.filter(template => template.TemplateName.toLowerCase().includes(keyword) || template.Description && template.Description.toLowerCase().includes(keyword));\n    }\n  }\n  // 搜尋模板\n  onSearch() {\n    this.updateFilteredTemplates();\n  }\n  // 清除搜尋\n  clearSearch() {\n    this.searchKeyword = '';\n    this.updateFilteredTemplates();\n  }\n  // 顯示新增模板表單\n  onAddTemplate() {\n    this.showAddForm = true;\n    this.newTemplate = {\n      name: '',\n      description: '',\n      selectedItems: []\n    };\n    // 重置可選資料的選擇狀態\n    this.availableData.forEach(item => item.selected = false);\n  }\n  // 取消新增模板\n  cancelAddTemplate() {\n    this.showAddForm = false;\n    this.newTemplate = {\n      name: '',\n      description: '',\n      selectedItems: []\n    };\n  }\n  // 儲存新模板\n  saveNewTemplate() {\n    if (!this.newTemplate.name.trim()) {\n      alert('請輸入模板名稱');\n      return;\n    }\n    const selectedItems = this.availableData.filter(item => item.selected);\n    if (selectedItems.length === 0) {\n      alert('請至少選擇一個項目');\n      return;\n    }\n    // TODO: 替換為實際的API調用\n    this.createTemplate(this.newTemplate.name, this.newTemplate.description, selectedItems);\n  }\n  // TODO: 替換為實際的API調用\n  createTemplate(name, description, selectedItems) {\n    // 模擬API調用 - 創建模板\n    console.log('創建模板 API 調用:', {\n      templateName: name,\n      description: description,\n      moduleType: this.moduleType,\n      selectedItems: selectedItems.map(item => ({\n        refId: this.getRefId(item),\n        fieldName: this.getFieldName(item),\n        fieldValue: this.getFieldValue(item)\n      }))\n    });\n    // 生成新的模板ID (實際應由後端API返回)\n    const newId = Math.max(...this.templates.map(t => t.TemplateID || 0), 0) + 1;\n    // 創建新模板\n    const newTemplate = {\n      TemplateID: newId,\n      TemplateName: name.trim(),\n      Description: description.trim()\n    };\n    // 添加到模板列表\n    this.templates.push(newTemplate);\n    // 創建模板詳情 - 根據選中的項目創建詳情記錄\n    selectedItems.forEach((item, index) => {\n      const detail = {\n        TemplateDetailID: this.templateDetails.length + index + 1,\n        TemplateID: newId,\n        RefID: this.getRefId(item),\n        // 關聯主檔ID\n        ModuleType: this.moduleType,\n        // 模組類型，用於區分資料來源\n        FieldName: this.getFieldName(item),\n        // 欄位名稱\n        FieldValue: this.getFieldValue(item) // 欄位值\n      };\n      this.templateDetails.push(detail);\n    });\n    // 更新過濾列表\n    this.updateFilteredTemplates();\n    // 關閉表單\n    this.showAddForm = false;\n    alert(`模板 \"${name}\" 已成功創建！包含 ${selectedItems.length} 個項目`);\n  }\n  // 獲取關聯主檔ID的輔助方法\n  getRefId(item) {\n    return item.CRequirementID || item.ID || item.id || 0;\n  }\n  // 獲取欄位名稱的輔助方法\n  getFieldName(_item) {\n    // 根據模組類型決定欄位名稱\n    switch (this.moduleType) {\n      case 'Requirement':\n        return 'CRequirement';\n      default:\n        return 'name';\n    }\n  }\n  // 獲取欄位值的輔助方法\n  getFieldValue(item) {\n    return item.CRequirement || item.name || item.title || '';\n  }\n  // 查看模板\n  onSelectTemplate(template) {\n    this.selectedTemplate = template;\n    this.selectTemplate.emit(template);\n  }\n  // 關閉模板查看器\n  onClose() {\n    this.close.emit();\n  }\n  // 刪除模板\n  onDeleteTemplate(templateID) {\n    if (confirm('確定刪除此模板？')) {\n      // TODO: 替換為實際的API調用\n      this.deleteTemplateById(templateID);\n    }\n  }\n  // TODO: 替換為實際的API調用\n  deleteTemplateById(templateID) {\n    // 模擬API調用 - 刪除模板\n    console.log('刪除模板 API 調用:', {\n      templateID: templateID,\n      moduleType: this.moduleType\n    });\n    // 刪除模板和相關詳情\n    this.templates = this.templates.filter(t => t.TemplateID !== templateID);\n    this.templateDetails = this.templateDetails.filter(d => d.TemplateID !== templateID);\n    this.updateFilteredTemplates();\n    // 如果當前查看的模板被刪除，關閉詳情\n    if (this.selectedTemplate?.TemplateID === templateID) {\n      this.selectedTemplate = null;\n    }\n    alert('模板已刪除');\n  }\n  /*\n   * API 設計說明：\n   *\n   * 1. 載入模板列表 API:\n   *    GET /api/templates?moduleType={moduleType}\n   *    返回: { templates: Template[], templateDetails: TemplateDetail[] }\n   *\n   * 2. 創建模板 API:\n   *    POST /api/templates\n   *    請求體: {\n   *      templateName: string,\n   *      description: string,\n   *      moduleType: string,\n   *      details: [{\n   *        refId: number,        // 關聯主檔ID\n   *        fieldName: string,    // 欄位名稱\n   *        fieldValue: string    // 欄位值\n   *      }]\n   *    }\n   *\n   * 3. 刪除模板 API:\n   *    DELETE /api/templates/{templateId}?moduleType={moduleType}\n   *\n   * 資料庫設計重點：\n   * - template 表：存放模板基本資訊 (TemplateID, TemplateName, Description)\n   * - templatedetail 表：存放模板詳情 (TemplateDetailID, TemplateID, RefID, ModuleType, FieldName, FieldValue)\n   * - ModuleType 欄位用於區分不同模組的資料 (如: 'Requirement', 'Product', 'Order' 等)\n   * - RefID 存放關聯主檔的ID，配合 ModuleType 可以找到對應的原始資料\n   */\n  // 關閉模板詳情\n  closeTemplateDetail() {\n    this.selectedTemplate = null;\n  }\n  // 取得當前選中模板的詳情\n  get currentTemplateDetails() {\n    if (!this.selectedTemplate) {\n      return [];\n    }\n    return this.templateDetails.filter(d => d.TemplateID === this.selectedTemplate.TemplateID);\n  }\n  // TrackBy函數用於優化ngFor性能\n  trackByTemplateId(index, template) {\n    return template.TemplateID || index;\n  }\n  static {\n    this.ɵfac = function TemplateViewerComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || TemplateViewerComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: TemplateViewerComponent,\n      selectors: [[\"app-template-viewer\"]],\n      inputs: {\n        availableData: \"availableData\",\n        moduleType: \"moduleType\"\n      },\n      outputs: {\n        selectTemplate: \"selectTemplate\",\n        close: \"close\"\n      },\n      standalone: true,\n      features: [i0.ɵɵNgOnChangesFeature, i0.ɵɵStandaloneFeature],\n      decls: 39,\n      vars: 7,\n      consts: [[\"noDetails\", \"\"], [2, \"width\", \"90vw\", \"max-width\", \"1200px\", \"height\", \"80vh\"], [2, \"overflow\", \"auto\"], [1, \"template-viewer-modal\"], [1, \"template-viewer-header\"], [1, \"d-flex\", \"justify-content-between\", \"align-items-center\", \"mb-3\"], [1, \"search-section\"], [1, \"input-group\"], [\"type\", \"text\", \"placeholder\", \"\\u641C\\u5C0B\\u6A21\\u677F...\", 1, \"form-control\", 3, \"ngModelChange\", \"input\", \"ngModel\"], [1, \"input-group-append\"], [\"type\", \"button\", 1, \"btn\", \"btn-outline-secondary\"], [1, \"fas\", \"fa-search\"], [1, \"btn\", \"btn-success\", \"btn-sm\", 3, \"click\"], [1, \"fas\", \"fa-plus\", \"mr-1\"], [\"class\", \"add-template-form mb-4\", 4, \"ngIf\"], [1, \"template-list\"], [\"class\", \"search-results-info mb-2\", 4, \"ngIf\"], [1, \"table-responsive\"], [1, \"table\", \"table-striped\", \"table-hover\"], [1, \"thead-light\"], [\"width\", \"30%\"], [\"width\", \"50%\"], [\"width\", \"20%\", 1, \"text-center\"], [4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [4, \"ngIf\"], [\"class\", \"template-detail-modal\", 4, \"ngIf\"], [1, \"text-center\"], [1, \"btn\", \"btn-secondary\", 3, \"click\"], [1, \"fas\", \"fa-times\", \"mr-1\"], [1, \"add-template-form\", \"mb-4\"], [1, \"card\"], [1, \"card-header\"], [1, \"mb-0\"], [1, \"fas\", \"fa-plus\", \"mr-2\"], [1, \"card-body\"], [3, \"ngSubmit\"], [1, \"row\"], [1, \"col-md-6\"], [1, \"form-group\", \"mb-3\"], [1, \"form-label\"], [1, \"text-danger\"], [\"type\", \"text\", \"name\", \"templateName\", \"placeholder\", \"\\u8ACB\\u8F38\\u5165\\u6A21\\u677F\\u540D\\u7A31\", \"required\", \"\", \"maxlength\", \"50\", 1, \"form-control\", 3, \"ngModelChange\", \"ngModel\"], [\"type\", \"text\", \"name\", \"templateDescription\", \"placeholder\", \"\\u8ACB\\u8F38\\u5165\\u6A21\\u677F\\u63CF\\u8FF0\\uFF08\\u53EF\\u9078\\uFF09\", \"maxlength\", \"100\", 1, \"form-control\", 3, \"ngModelChange\", \"ngModel\"], [1, \"available-items-container\"], [\"class\", \"text-muted text-center py-3\", 4, \"ngIf\"], [\"class\", \"item-checkbox\", 4, \"ngFor\", \"ngForOf\"], [1, \"form-actions\", \"d-flex\", \"justify-content-end\"], [\"type\", \"button\", 1, \"btn\", \"btn-secondary\", \"mr-2\", 3, \"click\"], [\"type\", \"submit\", 1, \"btn\", \"btn-success\"], [1, \"fas\", \"fa-save\", \"mr-1\"], [1, \"text-muted\", \"text-center\", \"py-3\"], [1, \"fas\", \"fa-info-circle\", \"mr-1\"], [1, \"item-checkbox\"], [1, \"form-check-label\", \"d-flex\", \"align-items-center\"], [\"type\", \"checkbox\", 1, \"form-check-input\", \"mr-2\", 3, \"ngModelChange\", \"ngModel\", \"name\"], [1, \"item-info\"], [1, \"item-name\"], [1, \"text-muted\"], [1, \"search-results-info\", \"mb-2\"], [\"role\", \"group\", 1, \"btn-group\", \"btn-group-sm\"], [\"title\", \"\\u67E5\\u770B\\u8A73\\u60C5\", 1, \"btn\", \"btn-info\", 3, \"click\"], [1, \"fas\", \"fa-eye\"], [\"class\", \"btn btn-danger\", \"title\", \"\\u522A\\u9664\\u6A21\\u677F\", 3, \"click\", 4, \"ngIf\"], [\"title\", \"\\u522A\\u9664\\u6A21\\u677F\", 1, \"btn\", \"btn-danger\", 3, \"click\"], [1, \"fas\", \"fa-trash\"], [\"colspan\", \"3\", 1, \"text-center\", \"py-4\"], [1, \"empty-state\"], [1, \"fas\", \"fa-folder-open\", \"fa-2x\", \"text-muted\", \"mb-2\"], [1, \"text-muted\", \"mb-0\"], [\"class\", \"text-muted\", 4, \"ngIf\"], [\"href\", \"javascript:void(0)\", 3, \"click\"], [1, \"template-detail-modal\"], [1, \"template-detail-header\", \"d-flex\", \"justify-content-between\", \"align-items-center\"], [1, \"fas\", \"fa-file-alt\", \"mr-2\"], [1, \"btn\", \"btn-outline-secondary\", \"btn-sm\", 3, \"click\"], [1, \"fas\", \"fa-times\"], [1, \"template-detail-content\"], [\"class\", \"template-description mb-3\", 4, \"ngIf\"], [1, \"template-items\"], [1, \"mb-2\"], [1, \"fas\", \"fa-list\", \"mr-1\"], [\"class\", \"detail-list\", 4, \"ngIf\", \"ngIfElse\"], [1, \"template-description\", \"mb-3\"], [1, \"detail-list\"], [\"class\", \"detail-item d-flex align-items-center py-2 border-bottom\", 4, \"ngFor\", \"ngForOf\"], [1, \"detail-item\", \"d-flex\", \"align-items-center\", \"py-2\", \"border-bottom\"], [1, \"detail-index\"], [1, \"badge\", \"badge-light\"], [1, \"detail-content\", \"flex-grow-1\", \"ml-2\"], [1, \"detail-field\"], [1, \"detail-value\", \"text-muted\"], [1, \"text-center\", \"py-3\"], [1, \"fas\", \"fa-inbox\", \"fa-2x\", \"text-muted\", \"mb-2\"]],\n      template: function TemplateViewerComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"nb-card\", 1)(1, \"nb-card-header\")(2, \"h5\");\n          i0.ɵɵtext(3, \"\\u6A21\\u677F\\u7BA1\\u7406\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(4, \"nb-card-body\", 2)(5, \"div\", 3)(6, \"div\", 4)(7, \"div\", 5)(8, \"div\", 6)(9, \"div\", 7)(10, \"input\", 8);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function TemplateViewerComponent_Template_input_ngModelChange_10_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.searchKeyword, $event) || (ctx.searchKeyword = $event);\n            return $event;\n          });\n          i0.ɵɵlistener(\"input\", function TemplateViewerComponent_Template_input_input_10_listener() {\n            return ctx.updateFilteredTemplates();\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(11, \"div\", 9)(12, \"button\", 10);\n          i0.ɵɵelement(13, \"i\", 11);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(14, \"button\", 12);\n          i0.ɵɵlistener(\"click\", function TemplateViewerComponent_Template_button_click_14_listener() {\n            return ctx.onAddTemplate();\n          });\n          i0.ɵɵelement(15, \"i\", 13);\n          i0.ɵɵtext(16, \"\\u65B0\\u589E \");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(17, TemplateViewerComponent_div_17_Template, 39, 4, \"div\", 14);\n          i0.ɵɵelementStart(18, \"div\", 15);\n          i0.ɵɵtemplate(19, TemplateViewerComponent_div_19_Template, 3, 2, \"div\", 16);\n          i0.ɵɵelementStart(20, \"div\", 17)(21, \"table\", 18)(22, \"thead\", 19)(23, \"tr\")(24, \"th\", 20);\n          i0.ɵɵtext(25, \"\\u6A21\\u677F\\u540D\\u7A31\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(26, \"th\", 21);\n          i0.ɵɵtext(27, \"\\u63CF\\u8FF0\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(28, \"th\", 22);\n          i0.ɵɵtext(29, \"\\u64CD\\u4F5C\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(30, \"tbody\");\n          i0.ɵɵtemplate(31, TemplateViewerComponent_tr_31_Template, 13, 3, \"tr\", 23)(32, TemplateViewerComponent_tr_32_Template, 7, 2, \"tr\", 24);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵtemplate(33, TemplateViewerComponent_div_33_Template, 17, 5, \"div\", 25);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(34, \"nb-card-footer\")(35, \"div\", 26)(36, \"button\", 27);\n          i0.ɵɵlistener(\"click\", function TemplateViewerComponent_Template_button_click_36_listener() {\n            return ctx.onClose();\n          });\n          i0.ɵɵelement(37, \"i\", 28);\n          i0.ɵɵtext(38, \"\\u95DC\\u9589 \");\n          i0.ɵɵelementEnd()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(10);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchKeyword);\n          i0.ɵɵadvance(7);\n          i0.ɵɵproperty(\"ngIf\", ctx.showAddForm);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.searchKeyword);\n          i0.ɵɵadvance(12);\n          i0.ɵɵproperty(\"ngForOf\", ctx.filteredTemplates)(\"ngForTrackBy\", ctx.trackByTemplateId);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.filteredTemplates || ctx.filteredTemplates.length === 0);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.selectedTemplate);\n        }\n      },\n      dependencies: [CommonModule, i1.NgForOf, i1.NgIf, FormsModule, i2.ɵNgNoValidate, i2.DefaultValueAccessor, i2.CheckboxControlValueAccessor, i2.NgControlStatus, i2.NgControlStatusGroup, i2.RequiredValidator, i2.MaxLengthValidator, i2.NgModel, i2.NgForm, NbCardModule, i3.NbCardComponent, i3.NbCardBodyComponent, i3.NbCardFooterComponent, i3.NbCardHeaderComponent, NbButtonModule],\n      styles: [\".template-viewer-modal[_ngcontent-%COMP%] {\\n  background: #fff;\\n  border-radius: 12px;\\n  padding: 2rem;\\n  min-width: 500px;\\n  max-width: 800px;\\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);\\n}\\n\\n.template-viewer-header[_ngcontent-%COMP%] {\\n  border-bottom: 2px solid #f0f0f0;\\n  padding-bottom: 1rem;\\n  margin-bottom: 1.5rem;\\n}\\n.template-viewer-header[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%] {\\n  color: #333;\\n  font-weight: 600;\\n}\\n\\n.search-container[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%] {\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\\n  border-radius: 8px;\\n  overflow: hidden;\\n}\\n.search-container[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .form-control[_ngcontent-%COMP%] {\\n  border: none;\\n  padding: 0.75rem 1rem;\\n  font-size: 0.95rem;\\n}\\n.search-container[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .form-control[_ngcontent-%COMP%]:focus {\\n  box-shadow: none;\\n  border-color: transparent;\\n}\\n.search-container[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .form-control[_ngcontent-%COMP%]::placeholder {\\n  color: #999;\\n  font-style: italic;\\n}\\n.search-container[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .input-group-append[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%] {\\n  border: none;\\n  background: #f8f9fa;\\n  color: #6c757d;\\n  padding: 0.75rem 1rem;\\n  transition: all 0.2s ease;\\n}\\n.search-container[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .input-group-append[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]:hover {\\n  background: #e9ecef;\\n  color: #495057;\\n}\\n.search-container[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .input-group-append[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]:focus {\\n  box-shadow: none;\\n}\\n\\n.search-results-info[_ngcontent-%COMP%] {\\n  padding: 0.5rem 0;\\n  border-left: 3px solid #007bff;\\n  padding-left: 0.75rem;\\n  background: #f8f9ff;\\n  border-radius: 4px;\\n}\\n\\n.table-responsive[_ngcontent-%COMP%] {\\n  border-radius: 8px;\\n  overflow: hidden;\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);\\n}\\n\\n.table[_ngcontent-%COMP%] {\\n  margin-bottom: 0;\\n}\\n.table[_ngcontent-%COMP%]   thead.thead-light[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);\\n}\\n.table[_ngcontent-%COMP%]   thead.thead-light[_ngcontent-%COMP%]   th[_ngcontent-%COMP%] {\\n  border: none;\\n  font-weight: 600;\\n  color: #495057;\\n  padding: 1rem 0.75rem;\\n  font-size: 0.9rem;\\n  text-transform: uppercase;\\n  letter-spacing: 0.5px;\\n}\\n.table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%] {\\n  transition: all 0.2s ease;\\n}\\n.table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]:hover {\\n  background-color: #f8f9ff;\\n  transform: translateY(-1px);\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\\n}\\n.table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   td[_ngcontent-%COMP%] {\\n  padding: 1rem 0.75rem;\\n  border-color: #f0f0f0;\\n  vertical-align: middle;\\n}\\n.table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%] {\\n  color: #333;\\n  font-weight: 600;\\n}\\n.table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]   .text-muted[_ngcontent-%COMP%] {\\n  font-size: 0.9rem;\\n}\\n\\n.btn-group-sm[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%] {\\n  padding: 0.375rem 0.75rem;\\n  font-size: 0.875rem;\\n  border-radius: 4px;\\n  font-weight: 500;\\n  transition: all 0.2s ease;\\n}\\n.btn-group-sm[_ngcontent-%COMP%]   .btn.btn-info[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);\\n  border: none;\\n}\\n.btn-group-sm[_ngcontent-%COMP%]   .btn.btn-info[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-1px);\\n  box-shadow: 0 4px 12px rgba(23, 162, 184, 0.3);\\n}\\n.btn-group-sm[_ngcontent-%COMP%]   .btn.btn-danger[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);\\n  border: none;\\n}\\n.btn-group-sm[_ngcontent-%COMP%]   .btn.btn-danger[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-1px);\\n  box-shadow: 0 4px 12px rgba(220, 53, 69, 0.3);\\n}\\n.btn-group-sm[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  margin-right: 0.25rem;\\n}\\n\\n.empty-state[_ngcontent-%COMP%] {\\n  padding: 2rem;\\n}\\n.empty-state[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  display: block;\\n  margin: 0 auto 1rem;\\n  opacity: 0.5;\\n}\\n.empty-state[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  font-size: 1.1rem;\\n  margin-bottom: 0.5rem;\\n}\\n.empty-state[_ngcontent-%COMP%]   a[_ngcontent-%COMP%] {\\n  color: #007bff;\\n  text-decoration: none;\\n}\\n.empty-state[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]:hover {\\n  text-decoration: underline;\\n}\\n\\n.template-detail-modal[_ngcontent-%COMP%] {\\n  margin-top: 1.5rem;\\n  background: #fff;\\n  border: 2px solid #e9ecef;\\n  border-radius: 12px;\\n  overflow: hidden;\\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);\\n  animation: _ngcontent-%COMP%_slideDown 0.3s ease-out;\\n}\\n\\n@keyframes _ngcontent-%COMP%_slideDown {\\n  from {\\n    opacity: 0;\\n    transform: translateY(-20px);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: translateY(0);\\n  }\\n}\\n.template-detail-header[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);\\n  padding: 1rem 1.5rem;\\n  border-bottom: 1px solid #dee2e6;\\n}\\n.template-detail-header[_ngcontent-%COMP%]   h6[_ngcontent-%COMP%] {\\n  color: #495057;\\n  font-weight: 600;\\n}\\n.template-detail-header[_ngcontent-%COMP%]   h6[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  color: #6c757d;\\n}\\n.template-detail-header[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  padding: 0.375rem 0.75rem;\\n}\\n\\n.template-detail-content[_ngcontent-%COMP%] {\\n  padding: 1.5rem;\\n  max-height: 400px;\\n  overflow-y: auto;\\n}\\n.template-detail-content[_ngcontent-%COMP%]   .template-description[_ngcontent-%COMP%] {\\n  background: #f8f9ff;\\n  padding: 1rem;\\n  border-radius: 8px;\\n  border-left: 4px solid #007bff;\\n}\\n.template-detail-content[_ngcontent-%COMP%]   .template-description[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%] {\\n  color: #495057;\\n}\\n.template-detail-content[_ngcontent-%COMP%]   .template-items[_ngcontent-%COMP%]   h6[_ngcontent-%COMP%] {\\n  color: #495057;\\n  font-weight: 600;\\n  padding-bottom: 0.5rem;\\n  border-bottom: 1px solid #e9ecef;\\n}\\n.template-detail-content[_ngcontent-%COMP%]   .template-items[_ngcontent-%COMP%]   h6[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  color: #6c757d;\\n}\\n\\n.detail-list[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%] {\\n  transition: all 0.2s ease;\\n}\\n.detail-list[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]:hover {\\n  background: #f8f9ff;\\n  border-radius: 6px;\\n  margin: 0 -0.5rem;\\n  padding-left: 0.5rem !important;\\n  padding-right: 0.5rem !important;\\n}\\n.detail-list[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]:last-child {\\n  border-bottom: none;\\n}\\n.detail-list[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   .detail-index[_ngcontent-%COMP%]   .badge[_ngcontent-%COMP%] {\\n  background: #e9ecef;\\n  color: #6c757d;\\n  font-weight: 500;\\n  padding: 0.375rem 0.5rem;\\n  border-radius: 50%;\\n  min-width: 2rem;\\n  text-align: center;\\n}\\n.detail-list[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   .detail-content[_ngcontent-%COMP%]   .detail-field[_ngcontent-%COMP%] {\\n  font-size: 0.9rem;\\n  margin-bottom: 0.25rem;\\n}\\n.detail-list[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   .detail-content[_ngcontent-%COMP%]   .detail-field[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%] {\\n  color: #495057;\\n}\\n.detail-list[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   .detail-content[_ngcontent-%COMP%]   .detail-value[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  line-height: 1.4;\\n  word-break: break-word;\\n}\\n\\n.add-template-form[_ngcontent-%COMP%]   .card[_ngcontent-%COMP%] {\\n  border: 2px solid #28a745;\\n  border-radius: 12px;\\n  box-shadow: 0 4px 20px rgba(40, 167, 69, 0.1);\\n}\\n.add-template-form[_ngcontent-%COMP%]   .card[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);\\n  color: white;\\n  border-bottom: none;\\n  border-radius: 10px 10px 0 0;\\n}\\n.add-template-form[_ngcontent-%COMP%]   .card[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%]   h6[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  margin: 0;\\n}\\n.add-template-form[_ngcontent-%COMP%]   .card[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%]   h6[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  color: rgba(255, 255, 255, 0.9);\\n}\\n.add-template-form[_ngcontent-%COMP%]   .card[_ngcontent-%COMP%]   .card-body[_ngcontent-%COMP%] {\\n  padding: 1.5rem;\\n}\\n.add-template-form[_ngcontent-%COMP%]   .form-label[_ngcontent-%COMP%] {\\n  color: #495057;\\n  font-size: 0.9rem;\\n  margin-bottom: 0.5rem;\\n}\\n.add-template-form[_ngcontent-%COMP%]   .form-label[_ngcontent-%COMP%]   .text-danger[_ngcontent-%COMP%] {\\n  color: #dc3545 !important;\\n}\\n.add-template-form[_ngcontent-%COMP%]   .form-control[_ngcontent-%COMP%] {\\n  border: 1px solid #e0e0e0;\\n  border-radius: 6px;\\n  padding: 0.75rem;\\n  transition: all 0.2s ease;\\n}\\n.add-template-form[_ngcontent-%COMP%]   .form-control[_ngcontent-%COMP%]:focus {\\n  border-color: #28a745;\\n  box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);\\n}\\n.add-template-form[_ngcontent-%COMP%]   .form-control[_ngcontent-%COMP%]::placeholder {\\n  color: #999;\\n  font-style: italic;\\n}\\n.add-template-form[_ngcontent-%COMP%]   .available-items-container[_ngcontent-%COMP%] {\\n  max-height: 200px;\\n  overflow-y: auto;\\n  border: 1px solid #e0e0e0;\\n  border-radius: 6px;\\n  padding: 0.75rem;\\n  background: #f8f9fa;\\n}\\n.add-template-form[_ngcontent-%COMP%]   .available-items-container[_ngcontent-%COMP%]   .item-checkbox[_ngcontent-%COMP%] {\\n  padding: 0.5rem 0;\\n  border-bottom: 1px solid #e9ecef;\\n  transition: all 0.2s ease;\\n}\\n.add-template-form[_ngcontent-%COMP%]   .available-items-container[_ngcontent-%COMP%]   .item-checkbox[_ngcontent-%COMP%]:last-child {\\n  border-bottom: none;\\n}\\n.add-template-form[_ngcontent-%COMP%]   .available-items-container[_ngcontent-%COMP%]   .item-checkbox[_ngcontent-%COMP%]:hover {\\n  background: rgba(40, 167, 69, 0.05);\\n  border-radius: 4px;\\n  margin: 0 -0.5rem;\\n  padding-left: 0.5rem;\\n  padding-right: 0.5rem;\\n}\\n.add-template-form[_ngcontent-%COMP%]   .available-items-container[_ngcontent-%COMP%]   .item-checkbox[_ngcontent-%COMP%]   .form-check-label[_ngcontent-%COMP%] {\\n  cursor: pointer;\\n  width: 100%;\\n  margin: 0;\\n}\\n.add-template-form[_ngcontent-%COMP%]   .available-items-container[_ngcontent-%COMP%]   .item-checkbox[_ngcontent-%COMP%]   .form-check-label[_ngcontent-%COMP%]   .form-check-input[_ngcontent-%COMP%] {\\n  margin-top: 0.125rem;\\n}\\n.add-template-form[_ngcontent-%COMP%]   .available-items-container[_ngcontent-%COMP%]   .item-checkbox[_ngcontent-%COMP%]   .form-check-label[_ngcontent-%COMP%]   .form-check-input[_ngcontent-%COMP%]:checked {\\n  background-color: #28a745;\\n  border-color: #28a745;\\n}\\n.add-template-form[_ngcontent-%COMP%]   .available-items-container[_ngcontent-%COMP%]   .item-checkbox[_ngcontent-%COMP%]   .form-check-label[_ngcontent-%COMP%]   .item-info[_ngcontent-%COMP%] {\\n  flex-grow: 1;\\n}\\n.add-template-form[_ngcontent-%COMP%]   .available-items-container[_ngcontent-%COMP%]   .item-checkbox[_ngcontent-%COMP%]   .form-check-label[_ngcontent-%COMP%]   .item-info[_ngcontent-%COMP%]   .item-name[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  color: #333;\\n  margin-bottom: 0.125rem;\\n}\\n.add-template-form[_ngcontent-%COMP%]   .available-items-container[_ngcontent-%COMP%]   .item-checkbox[_ngcontent-%COMP%]   .form-check-label[_ngcontent-%COMP%]   .item-info[_ngcontent-%COMP%]   small[_ngcontent-%COMP%] {\\n  font-size: 0.8rem;\\n  color: #6c757d;\\n}\\n.add-template-form[_ngcontent-%COMP%]   .form-actions[_ngcontent-%COMP%] {\\n  margin-top: 1rem;\\n  padding-top: 1rem;\\n  border-top: 1px solid #e9ecef;\\n}\\n.add-template-form[_ngcontent-%COMP%]   .form-actions[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%] {\\n  padding: 0.5rem 1.5rem;\\n  font-weight: 500;\\n  border-radius: 6px;\\n  transition: all 0.2s ease;\\n}\\n.add-template-form[_ngcontent-%COMP%]   .form-actions[_ngcontent-%COMP%]   .btn.btn-secondary[_ngcontent-%COMP%] {\\n  background: #6c757d;\\n  border-color: #6c757d;\\n}\\n.add-template-form[_ngcontent-%COMP%]   .form-actions[_ngcontent-%COMP%]   .btn.btn-secondary[_ngcontent-%COMP%]:hover {\\n  background: #5a6268;\\n  border-color: #545b62;\\n  transform: translateY(-1px);\\n}\\n.add-template-form[_ngcontent-%COMP%]   .form-actions[_ngcontent-%COMP%]   .btn.btn-success[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);\\n  border: none;\\n}\\n.add-template-form[_ngcontent-%COMP%]   .form-actions[_ngcontent-%COMP%]   .btn.btn-success[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-1px);\\n  box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);\\n}\\n.add-template-form[_ngcontent-%COMP%]   .form-actions[_ngcontent-%COMP%]   .btn.btn-success[_ngcontent-%COMP%]:disabled {\\n  opacity: 0.6;\\n  transform: none;\\n  box-shadow: none;\\n}\\n.add-template-form[_ngcontent-%COMP%]   .form-actions[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  margin-right: 0.25rem;\\n}\\n/*# sourceMappingURL=data:application/json;base64,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 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["EventEmitter", "CommonModule", "FormsModule", "NbCardModule", "NbButtonModule", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd", "ɵɵtwoWayListener", "TemplateViewerComponent_div_17_div_31_Template_input_ngModelChange_2_listener", "$event", "item_r4", "ɵɵrestoreView", "_r3", "$implicit", "ɵɵtwoWayBindingSet", "selected", "ɵɵresetView", "ɵɵadvance", "ɵɵpropertyInterpolate1", "i_r5", "ɵɵtwoWayProperty", "ɵɵtextInterpolate", "CRequirement", "name", "CGroupName", "description", "ɵɵlistener", "TemplateViewerComponent_div_17_Template_form_ngSubmit_7_listener", "_r1", "ctx_r1", "ɵɵnextContext", "saveNewTemplate", "TemplateViewerComponent_div_17_Template_input_ngModelChange_16_listener", "newTemplate", "TemplateViewerComponent_div_17_Template_input_ngModelChange_22_listener", "ɵɵtemplate", "TemplateViewerComponent_div_17_div_30_Template", "TemplateViewerComponent_div_17_div_31_Template", "TemplateViewerComponent_div_17_Template_button_click_33_listener", "cancelAddTemplate", "ɵɵproperty", "availableData", "length", "ɵɵtextInterpolate2", "filteredTemplates", "searchKeyword", "TemplateViewerComponent_tr_31_button_12_Template_button_click_0_listener", "_r8", "tpl_r7", "TemplateID", "onDeleteTemplate", "TemplateViewerComponent_tr_31_Template_button_click_9_listener", "_r6", "onSelectTemplate", "TemplateViewerComponent_tr_31_button_12_Template", "TemplateName", "Description", "TemplateViewerComponent_tr_32_small_6_Template_a_click_2_listener", "_r9", "clearSearch", "TemplateViewerComponent_tr_32_small_6_Template", "ɵɵtextInterpolate1", "selectedTemplate", "i_r12", "detail_r11", "FieldName", "FieldValue", "TemplateViewerComponent_div_33_div_14_div_1_Template", "currentTemplateDetails", "TemplateViewerComponent_div_33_Template_button_click_5_listener", "_r10", "closeTemplateDetail", "TemplateViewerComponent_div_33_div_9_Template", "TemplateViewerComponent_div_33_div_14_Template", "TemplateViewerComponent_div_33_ng_template_15_Template", "ɵɵtemplateRefExtractor", "noDetails_r13", "TemplateViewerComponent", "constructor", "moduleType", "selectTemplate", "close", "templates", "templateDetails", "showAddForm", "selectedItems", "ngOnInit", "loadTemplates", "updateFilteredTemplates", "ngOnChanges", "console", "log", "TemplateDetailID", "RefID", "ModuleType", "getFieldName", "trim", "keyword", "toLowerCase", "filter", "template", "includes", "onSearch", "onAddTemplate", "for<PERSON>ach", "item", "alert", "createTemplate", "templateName", "map", "refId", "getRefId", "fieldName", "fieldValue", "getFieldValue", "newId", "Math", "max", "t", "push", "index", "detail", "CRequirementID", "ID", "id", "_item", "title", "emit", "onClose", "templateID", "confirm", "deleteTemplateById", "d", "trackByTemplateId", "selectors", "inputs", "outputs", "standalone", "features", "ɵɵNgOnChangesFeature", "ɵɵStandaloneFeature", "decls", "vars", "consts", "TemplateViewerComponent_Template", "rf", "ctx", "TemplateViewerComponent_Template_input_ngModelChange_10_listener", "TemplateViewerComponent_Template_input_input_10_listener", "TemplateViewerComponent_Template_button_click_14_listener", "TemplateViewerComponent_div_17_Template", "TemplateViewerComponent_div_19_Template", "TemplateViewerComponent_tr_31_Template", "TemplateViewerComponent_tr_32_Template", "TemplateViewerComponent_div_33_Template", "TemplateViewerComponent_Template_button_click_36_listener", "i1", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "i2", "ɵNgNoValidate", "DefaultValueAccessor", "CheckboxControlValueAccessor", "NgControlStatus", "NgControlStatusGroup", "RequiredValidator", "MaxLengthValidator", "NgModel", "NgForm", "i3", "NbCardComponent", "NbCardBodyComponent", "NbCardFooterComponent", "NbCardHeaderComponent", "styles"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\shared\\components\\template-viewer\\template-viewer.component.ts", "C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\shared\\components\\template-viewer\\template-viewer.component.html"], "sourcesContent": ["import { Component, EventEmitter, Input, Output, OnInit, OnChanges } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { FormsModule } from '@angular/forms';\r\nimport { NbCardModule, NbButtonModule } from '@nebular/theme';\r\n\r\n@Component({\r\n  selector: 'app-template-viewer',\r\n  templateUrl: './template-viewer.component.html',\r\n  styleUrls: ['./template-viewer.component.scss'],\r\n  standalone: true,\r\n  imports: [CommonModule, FormsModule, NbCardModule, NbButtonModule],\r\n})\r\nexport class TemplateViewerComponent implements OnInit, OnChanges {\r\n  @Input() availableData: any[] = []; // 父元件傳入的可選資料 (包含關聯主檔ID和名稱)\r\n  @Input() moduleType: string = 'Requirement'; // 模組類型，用於區分資料來源\r\n  @Output() selectTemplate = new EventEmitter<Template>();\r\n  @Output() close = new EventEmitter<void>(); // 關閉事件\r\n\r\n  // 內部管理的模板資料\r\n  templates: Template[] = [];\r\n  templateDetails: TemplateDetail[] = [];\r\n  selectedTemplate: Template | null = null;\r\n\r\n  // 查詢功能\r\n  searchKeyword = '';\r\n  filteredTemplates: Template[] = [];\r\n\r\n  // 新增模板表單\r\n  showAddForm = false;\r\n  newTemplate = {\r\n    name: '',\r\n    description: '',\r\n    selectedItems: [] as any[]\r\n  };\r\n\r\n  ngOnInit() {\r\n    this.loadTemplates();\r\n    this.updateFilteredTemplates();\r\n  }\r\n\r\n  ngOnChanges() {\r\n    this.updateFilteredTemplates();\r\n  }\r\n\r\n  // TODO: 替換為實際的API調用\r\n  loadTemplates() {\r\n    // 模擬API調用 - 載入模板列表\r\n    console.log('載入模板 API 調用:', { moduleType: this.moduleType });\r\n\r\n    this.templates = [\r\n      { TemplateID: 1, TemplateName: '需求模板A', Description: '包含基本工程項目的模板' },\r\n      { TemplateID: 2, TemplateName: '需求模板B', Description: '包含進階工程項目的模板' }\r\n    ];\r\n\r\n    // 載入對應模組類型的模板詳情\r\n    this.templateDetails = [\r\n      {\r\n        TemplateDetailID: 1,\r\n        TemplateID: 1,\r\n        RefID: 101,\r\n        ModuleType: this.moduleType,\r\n        FieldName: this.getFieldName({}),\r\n        FieldValue: '工程項目A'\r\n      },\r\n      {\r\n        TemplateDetailID: 2,\r\n        TemplateID: 1,\r\n        RefID: 102,\r\n        ModuleType: this.moduleType,\r\n        FieldName: this.getFieldName({}),\r\n        FieldValue: '工程項目B'\r\n      },\r\n      {\r\n        TemplateDetailID: 3,\r\n        TemplateID: 2,\r\n        RefID: 201,\r\n        ModuleType: this.moduleType,\r\n        FieldName: this.getFieldName({}),\r\n        FieldValue: '工程項目C'\r\n      }\r\n    ];\r\n  }\r\n\r\n  // 更新過濾後的模板列表\r\n  updateFilteredTemplates() {\r\n    if (!this.searchKeyword.trim()) {\r\n      this.filteredTemplates = [...this.templates];\r\n    } else {\r\n      const keyword = this.searchKeyword.toLowerCase();\r\n      this.filteredTemplates = this.templates.filter(template =>\r\n        template.TemplateName.toLowerCase().includes(keyword) ||\r\n        (template.Description && template.Description.toLowerCase().includes(keyword))\r\n      );\r\n    }\r\n  }\r\n\r\n  // 搜尋模板\r\n  onSearch() {\r\n    this.updateFilteredTemplates();\r\n  }\r\n\r\n  // 清除搜尋\r\n  clearSearch() {\r\n    this.searchKeyword = '';\r\n    this.updateFilteredTemplates();\r\n  }\r\n\r\n\r\n\r\n  // 顯示新增模板表單\r\n  onAddTemplate() {\r\n    this.showAddForm = true;\r\n    this.newTemplate = {\r\n      name: '',\r\n      description: '',\r\n      selectedItems: []\r\n    };\r\n    // 重置可選資料的選擇狀態\r\n    this.availableData.forEach(item => item.selected = false);\r\n  }\r\n\r\n  // 取消新增模板\r\n  cancelAddTemplate() {\r\n    this.showAddForm = false;\r\n    this.newTemplate = {\r\n      name: '',\r\n      description: '',\r\n      selectedItems: []\r\n    };\r\n  }\r\n\r\n  // 儲存新模板\r\n  saveNewTemplate() {\r\n    if (!this.newTemplate.name.trim()) {\r\n      alert('請輸入模板名稱');\r\n      return;\r\n    }\r\n\r\n    const selectedItems = this.availableData.filter(item => item.selected);\r\n    if (selectedItems.length === 0) {\r\n      alert('請至少選擇一個項目');\r\n      return;\r\n    }\r\n\r\n    // TODO: 替換為實際的API調用\r\n    this.createTemplate(this.newTemplate.name, this.newTemplate.description, selectedItems);\r\n  }\r\n\r\n  // TODO: 替換為實際的API調用\r\n  createTemplate(name: string, description: string, selectedItems: any[]) {\r\n    // 模擬API調用 - 創建模板\r\n    console.log('創建模板 API 調用:', {\r\n      templateName: name,\r\n      description: description,\r\n      moduleType: this.moduleType,\r\n      selectedItems: selectedItems.map(item => ({\r\n        refId: this.getRefId(item),\r\n        fieldName: this.getFieldName(item),\r\n        fieldValue: this.getFieldValue(item)\r\n      }))\r\n    });\r\n\r\n    // 生成新的模板ID (實際應由後端API返回)\r\n    const newId = Math.max(...this.templates.map(t => t.TemplateID || 0), 0) + 1;\r\n\r\n    // 創建新模板\r\n    const newTemplate: Template = {\r\n      TemplateID: newId,\r\n      TemplateName: name.trim(),\r\n      Description: description.trim()\r\n    };\r\n\r\n    // 添加到模板列表\r\n    this.templates.push(newTemplate);\r\n\r\n    // 創建模板詳情 - 根據選中的項目創建詳情記錄\r\n    selectedItems.forEach((item, index) => {\r\n      const detail: TemplateDetail = {\r\n        TemplateDetailID: this.templateDetails.length + index + 1,\r\n        TemplateID: newId,\r\n        RefID: this.getRefId(item), // 關聯主檔ID\r\n        ModuleType: this.moduleType, // 模組類型，用於區分資料來源\r\n        FieldName: this.getFieldName(item), // 欄位名稱\r\n        FieldValue: this.getFieldValue(item) // 欄位值\r\n      };\r\n      this.templateDetails.push(detail);\r\n    });\r\n\r\n    // 更新過濾列表\r\n    this.updateFilteredTemplates();\r\n\r\n    // 關閉表單\r\n    this.showAddForm = false;\r\n    alert(`模板 \"${name}\" 已成功創建！包含 ${selectedItems.length} 個項目`);\r\n  }\r\n\r\n  // 獲取關聯主檔ID的輔助方法\r\n  private getRefId(item: any): number {\r\n    return item.CRequirementID || item.ID || item.id || 0;\r\n  }\r\n\r\n  // 獲取欄位名稱的輔助方法\r\n  private getFieldName(_item?: any): string {\r\n    // 根據模組類型決定欄位名稱\r\n    switch (this.moduleType) {\r\n      case 'Requirement':\r\n        return 'CRequirement';\r\n      default:\r\n        return 'name';\r\n    }\r\n  }\r\n\r\n  // 獲取欄位值的輔助方法\r\n  private getFieldValue(item: any): string {\r\n    return item.CRequirement || item.name || item.title || '';\r\n  }\r\n\r\n  // 查看模板\r\n  onSelectTemplate(template: Template) {\r\n    this.selectedTemplate = template;\r\n    this.selectTemplate.emit(template);\r\n  }\r\n\r\n  // 關閉模板查看器\r\n  onClose() {\r\n    this.close.emit();\r\n  }\r\n\r\n  // 刪除模板\r\n  onDeleteTemplate(templateID: number) {\r\n    if (confirm('確定刪除此模板？')) {\r\n      // TODO: 替換為實際的API調用\r\n      this.deleteTemplateById(templateID);\r\n    }\r\n  }\r\n\r\n  // TODO: 替換為實際的API調用\r\n  deleteTemplateById(templateID: number) {\r\n    // 模擬API調用 - 刪除模板\r\n    console.log('刪除模板 API 調用:', {\r\n      templateID: templateID,\r\n      moduleType: this.moduleType\r\n    });\r\n\r\n    // 刪除模板和相關詳情\r\n    this.templates = this.templates.filter(t => t.TemplateID !== templateID);\r\n    this.templateDetails = this.templateDetails.filter(d => d.TemplateID !== templateID);\r\n    this.updateFilteredTemplates();\r\n\r\n    // 如果當前查看的模板被刪除，關閉詳情\r\n    if (this.selectedTemplate?.TemplateID === templateID) {\r\n      this.selectedTemplate = null;\r\n    }\r\n\r\n    alert('模板已刪除');\r\n  }\r\n\r\n  /*\r\n   * API 設計說明：\r\n   *\r\n   * 1. 載入模板列表 API:\r\n   *    GET /api/templates?moduleType={moduleType}\r\n   *    返回: { templates: Template[], templateDetails: TemplateDetail[] }\r\n   *\r\n   * 2. 創建模板 API:\r\n   *    POST /api/templates\r\n   *    請求體: {\r\n   *      templateName: string,\r\n   *      description: string,\r\n   *      moduleType: string,\r\n   *      details: [{\r\n   *        refId: number,        // 關聯主檔ID\r\n   *        fieldName: string,    // 欄位名稱\r\n   *        fieldValue: string    // 欄位值\r\n   *      }]\r\n   *    }\r\n   *\r\n   * 3. 刪除模板 API:\r\n   *    DELETE /api/templates/{templateId}?moduleType={moduleType}\r\n   *\r\n   * 資料庫設計重點：\r\n   * - template 表：存放模板基本資訊 (TemplateID, TemplateName, Description)\r\n   * - templatedetail 表：存放模板詳情 (TemplateDetailID, TemplateID, RefID, ModuleType, FieldName, FieldValue)\r\n   * - ModuleType 欄位用於區分不同模組的資料 (如: 'Requirement', 'Product', 'Order' 等)\r\n   * - RefID 存放關聯主檔的ID，配合 ModuleType 可以找到對應的原始資料\r\n   */\r\n\r\n  // 關閉模板詳情\r\n  closeTemplateDetail() {\r\n    this.selectedTemplate = null;\r\n  }\r\n\r\n  // 取得當前選中模板的詳情\r\n  get currentTemplateDetails(): TemplateDetail[] {\r\n    if (!this.selectedTemplate) {\r\n      return [];\r\n    }\r\n    return this.templateDetails.filter(d => d.TemplateID === this.selectedTemplate!.TemplateID);\r\n  }\r\n\r\n  // TrackBy函數用於優化ngFor性能\r\n  trackByTemplateId(index: number, template: Template): number {\r\n    return template.TemplateID || index;\r\n  }\r\n}\r\n\r\n// DB 對應型別\r\nexport interface Template {\r\n  TemplateID?: number;\r\n  TemplateName: string;\r\n  Description?: string;\r\n}\r\nexport interface TemplateDetail {\r\n  TemplateDetailID?: number;\r\n  TemplateID: number;\r\n  RefID: number; // 關聯主檔ID\r\n  ModuleType: string;\r\n  FieldName: string;\r\n  FieldValue: string;\r\n}\r\n", "<nb-card style=\"width: 90vw; max-width: 1200px; height: 80vh;\">\r\n  <nb-card-header>\r\n    <h5>模板管理</h5>\r\n  </nb-card-header>\r\n  <nb-card-body style=\"overflow: auto;\">\r\n    <div class=\"template-viewer-modal\">\r\n      <div class=\"template-viewer-header\">\r\n        <div class=\"d-flex justify-content-between align-items-center mb-3\">\r\n          <div class=\"search-section\">\r\n            <div class=\"input-group\">\r\n              <input type=\"text\" class=\"form-control\" placeholder=\"搜尋模板...\" [(ngModel)]=\"searchKeyword\"\r\n                (input)=\"updateFilteredTemplates()\">\r\n              <div class=\"input-group-append\">\r\n                <button class=\"btn btn-outline-secondary\" type=\"button\">\r\n                  <i class=\"fas fa-search\"></i>\r\n                </button>\r\n              </div>\r\n            </div>\r\n          </div>\r\n          <button class=\"btn btn-success btn-sm\" (click)=\"onAddTemplate()\">\r\n            <i class=\"fas fa-plus mr-1\"></i>新增\r\n          </button>\r\n        </div>\r\n\r\n\r\n      </div>\r\n\r\n      <!-- 新增模板表單 -->\r\n      <div *ngIf=\"showAddForm\" class=\"add-template-form mb-4\">\r\n        <div class=\"card\">\r\n          <div class=\"card-header\">\r\n            <h6 class=\"mb-0\">\r\n              <i class=\"fas fa-plus mr-2\"></i>新增模板\r\n            </h6>\r\n          </div>\r\n          <div class=\"card-body\">\r\n            <form (ngSubmit)=\"saveNewTemplate()\">\r\n              <div class=\"row\">\r\n                <div class=\"col-md-6\">\r\n                  <div class=\"form-group mb-3\">\r\n                    <label class=\"form-label\">\r\n                      <strong>模板名稱 <span class=\"text-danger\">*</span></strong>\r\n                    </label>\r\n                    <input type=\"text\" class=\"form-control\" [(ngModel)]=\"newTemplate.name\" name=\"templateName\"\r\n                      placeholder=\"請輸入模板名稱\" required maxlength=\"50\">\r\n                  </div>\r\n                </div>\r\n                <div class=\"col-md-6\">\r\n                  <div class=\"form-group mb-3\">\r\n                    <label class=\"form-label\">\r\n                      <strong>模板描述</strong>\r\n                    </label>\r\n                    <input type=\"text\" class=\"form-control\" [(ngModel)]=\"newTemplate.description\"\r\n                      name=\"templateDescription\" placeholder=\"請輸入模板描述（可選）\" maxlength=\"100\">\r\n                  </div>\r\n                </div>\r\n              </div>\r\n\r\n              <div class=\"form-group mb-3\">\r\n                <label class=\"form-label\">\r\n                  <strong>選擇要加入模板的項目 <span class=\"text-danger\">*</span></strong>\r\n                </label>\r\n                <div class=\"available-items-container\">\r\n                  <div *ngIf=\"availableData.length === 0\" class=\"text-muted text-center py-3\">\r\n                    <i class=\"fas fa-info-circle mr-1\"></i>\r\n                    暫無可選項目\r\n                  </div>\r\n                  <div *ngFor=\"let item of availableData; let i = index\" class=\"item-checkbox\">\r\n                    <label class=\"form-check-label d-flex align-items-center\">\r\n                      <input type=\"checkbox\" class=\"form-check-input mr-2\" [(ngModel)]=\"item.selected\"\r\n                        name=\"item_{{i}}\">\r\n                      <div class=\"item-info\">\r\n                        <div class=\"item-name\">{{ item.CRequirement || item.name }}</div>\r\n                        <small class=\"text-muted\">{{ item.CGroupName || item.description }}</small>\r\n                      </div>\r\n                    </label>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n\r\n              <div class=\"form-actions d-flex justify-content-end\">\r\n                <button type=\"button\" class=\"btn btn-secondary mr-2\" (click)=\"cancelAddTemplate()\">\r\n                  <i class=\"fas fa-times mr-1\"></i>取消\r\n                </button>\r\n                <button type=\"submit\" class=\"btn btn-success\">\r\n                  <i class=\"fas fa-save mr-1\"></i>儲存模板\r\n                </button>\r\n              </div>\r\n            </form>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n\r\n\r\n      <div class=\"template-list\">\r\n        <!-- 搜尋結果統計 -->\r\n        <div class=\"search-results-info mb-2\" *ngIf=\"searchKeyword\">\r\n          <small class=\"text-muted\">\r\n            找到 {{ filteredTemplates.length }} 個符合「{{ searchKeyword }}」的模板\r\n          </small>\r\n        </div>\r\n\r\n        <div class=\"table-responsive\">\r\n          <table class=\"table table-striped table-hover\">\r\n            <thead class=\"thead-light\">\r\n              <tr>\r\n                <th width=\"30%\">模板名稱</th>\r\n                <th width=\"50%\">描述</th>\r\n                <th width=\"20%\" class=\"text-center\">操作</th>\r\n              </tr>\r\n            </thead>\r\n            <tbody>\r\n              <tr *ngFor=\"let tpl of filteredTemplates; trackBy: trackByTemplateId\">\r\n                <td>\r\n                  <strong>{{ tpl.TemplateName }}</strong>\r\n                </td>\r\n                <td>\r\n                  <span class=\"text-muted\">{{ tpl.Description || '無描述' }}</span>\r\n                </td>\r\n                <td class=\"text-center\">\r\n                  <div class=\"btn-group btn-group-sm\" role=\"group\">\r\n                    <button class=\"btn btn-info\" (click)=\"onSelectTemplate(tpl)\" title=\"查看詳情\">\r\n                      <i class=\"fas fa-eye\"></i> 查看\r\n                    </button>\r\n                    <button class=\"btn btn-danger\" (click)=\"tpl.TemplateID && onDeleteTemplate(tpl.TemplateID)\"\r\n                      *ngIf=\"tpl.TemplateID\" title=\"刪除模板\">\r\n                      <i class=\"fas fa-trash\"></i> 刪除\r\n                    </button>\r\n                  </div>\r\n                </td>\r\n              </tr>\r\n              <tr *ngIf=\"!filteredTemplates || filteredTemplates.length === 0\">\r\n                <td colspan=\"3\" class=\"text-center py-4\">\r\n                  <div class=\"empty-state\">\r\n                    <i class=\"fas fa-folder-open fa-2x text-muted mb-2\"></i>\r\n                    <p class=\"text-muted mb-0\">\r\n                      {{ searchKeyword ? '找不到符合條件的模板' : '暫無模板' }}\r\n                    </p>\r\n                    <small class=\"text-muted\" *ngIf=\"searchKeyword\">\r\n                      請嘗試其他關鍵字或 <a href=\"javascript:void(0)\" (click)=\"clearSearch()\">清除搜尋</a>\r\n                    </small>\r\n                  </div>\r\n                </td>\r\n              </tr>\r\n            </tbody>\r\n          </table>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 查看模板細節 -->\r\n      <div *ngIf=\"selectedTemplate\" class=\"template-detail-modal\">\r\n        <div class=\"template-detail-header d-flex justify-content-between align-items-center\">\r\n          <h6 class=\"mb-0\">\r\n            <i class=\"fas fa-file-alt mr-2\"></i>\r\n            模板細節：{{ selectedTemplate!.TemplateName }}\r\n          </h6>\r\n          <button class=\"btn btn-outline-secondary btn-sm\" (click)=\"closeTemplateDetail()\">\r\n            <i class=\"fas fa-times\"></i> 關閉\r\n          </button>\r\n        </div>\r\n\r\n        <div class=\"template-detail-content\">\r\n          <div *ngIf=\"selectedTemplate.Description\" class=\"template-description mb-3\">\r\n            <strong>描述：</strong>\r\n            <span class=\"text-muted\">{{ selectedTemplate.Description }}</span>\r\n          </div>\r\n\r\n          <div class=\"template-items\">\r\n            <h6 class=\"mb-2\">\r\n              <i class=\"fas fa-list mr-1\"></i>\r\n              模板內容 ({{ currentTemplateDetails.length }} 項)\r\n            </h6>\r\n\r\n            <div *ngIf=\"currentTemplateDetails.length > 0; else noDetails\" class=\"detail-list\">\r\n              <div *ngFor=\"let detail of currentTemplateDetails; let i = index\"\r\n                class=\"detail-item d-flex align-items-center py-2 border-bottom\">\r\n                <div class=\"detail-index\">\r\n                  <span class=\"badge badge-light\">{{ i + 1 }}</span>\r\n                </div>\r\n                <div class=\"detail-content flex-grow-1 ml-2\">\r\n                  <div class=\"detail-field\">\r\n                    <strong>{{ detail.FieldName }}:</strong>\r\n                  </div>\r\n                  <div class=\"detail-value text-muted\">\r\n                    {{ detail.FieldValue }}\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            <ng-template #noDetails>\r\n              <div class=\"text-center py-3\">\r\n                <i class=\"fas fa-inbox fa-2x text-muted mb-2\"></i>\r\n                <p class=\"text-muted mb-0\">此模板暫無內容</p>\r\n              </div>\r\n            </ng-template>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </nb-card-body>\r\n  <nb-card-footer>\r\n    <div class=\"text-center\">\r\n      <button class=\"btn btn-secondary\" (click)=\"onClose()\">\r\n        <i class=\"fas fa-times mr-1\"></i>關閉\r\n      </button>\r\n    </div>\r\n  </nb-card-footer>\r\n</nb-card>"], "mappings": "AAAA,SAAoBA,YAAY,QAA0C,eAAe;AACzF,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,YAAY,EAAEC,cAAc,QAAQ,gBAAgB;;;;;;;IC4D3CC,EAAA,CAAAC,cAAA,cAA4E;IAC1ED,EAAA,CAAAE,SAAA,YAAuC;IACvCF,EAAA,CAAAG,MAAA,6CACF;IAAAH,EAAA,CAAAI,YAAA,EAAM;;;;;;IAGFJ,EAFJ,CAAAC,cAAA,cAA6E,gBACjB,gBAEpC;IADiCD,EAAA,CAAAK,gBAAA,2BAAAC,8EAAAC,MAAA;MAAA,MAAAC,OAAA,GAAAR,EAAA,CAAAS,aAAA,CAAAC,GAAA,EAAAC,SAAA;MAAAX,EAAA,CAAAY,kBAAA,CAAAJ,OAAA,CAAAK,QAAA,EAAAN,MAAA,MAAAC,OAAA,CAAAK,QAAA,GAAAN,MAAA;MAAA,OAAAP,EAAA,CAAAc,WAAA,CAAAP,MAAA;IAAA,EAA2B;IAAhFP,EAAA,CAAAI,YAAA,EACoB;IAElBJ,EADF,CAAAC,cAAA,cAAuB,cACE;IAAAD,EAAA,CAAAG,MAAA,GAAoC;IAAAH,EAAA,CAAAI,YAAA,EAAM;IACjEJ,EAAA,CAAAC,cAAA,gBAA0B;IAAAD,EAAA,CAAAG,MAAA,GAAyC;IAGzEH,EAHyE,CAAAI,YAAA,EAAQ,EACvE,EACA,EACJ;;;;;IANAJ,EAAA,CAAAe,SAAA,GAAiB;IAAjBf,EAAA,CAAAgB,sBAAA,kBAAAC,IAAA,KAAiB;IADkCjB,EAAA,CAAAkB,gBAAA,YAAAV,OAAA,CAAAK,QAAA,CAA2B;IAGvDb,EAAA,CAAAe,SAAA,GAAoC;IAApCf,EAAA,CAAAmB,iBAAA,CAAAX,OAAA,CAAAY,YAAA,IAAAZ,OAAA,CAAAa,IAAA,CAAoC;IACjCrB,EAAA,CAAAe,SAAA,GAAyC;IAAzCf,EAAA,CAAAmB,iBAAA,CAAAX,OAAA,CAAAc,UAAA,IAAAd,OAAA,CAAAe,WAAA,CAAyC;;;;;;IA1C/EvB,EAHN,CAAAC,cAAA,cAAwD,cACpC,cACS,aACN;IACfD,EAAA,CAAAE,SAAA,YAAgC;IAAAF,EAAA,CAAAG,MAAA,gCAClC;IACFH,EADE,CAAAI,YAAA,EAAK,EACD;IAEJJ,EADF,CAAAC,cAAA,cAAuB,eACgB;IAA/BD,EAAA,CAAAwB,UAAA,sBAAAC,iEAAA;MAAAzB,EAAA,CAAAS,aAAA,CAAAiB,GAAA;MAAA,MAAAC,MAAA,GAAA3B,EAAA,CAAA4B,aAAA;MAAA,OAAA5B,EAAA,CAAAc,WAAA,CAAYa,MAAA,CAAAE,eAAA,EAAiB;IAAA,EAAC;IAK1B7B,EAJR,CAAAC,cAAA,cAAiB,cACO,eACS,iBACD,cAChB;IAAAD,EAAA,CAAAG,MAAA,iCAAK;IAAAH,EAAA,CAAAC,cAAA,gBAA0B;IAAAD,EAAA,CAAAG,MAAA,SAAC;IAC1CH,EAD0C,CAAAI,YAAA,EAAO,EAAS,EAClD;IACRJ,EAAA,CAAAC,cAAA,iBACgD;IADRD,EAAA,CAAAK,gBAAA,2BAAAyB,wEAAAvB,MAAA;MAAAP,EAAA,CAAAS,aAAA,CAAAiB,GAAA;MAAA,MAAAC,MAAA,GAAA3B,EAAA,CAAA4B,aAAA;MAAA5B,EAAA,CAAAY,kBAAA,CAAAe,MAAA,CAAAI,WAAA,CAAAV,IAAA,EAAAd,MAAA,MAAAoB,MAAA,CAAAI,WAAA,CAAAV,IAAA,GAAAd,MAAA;MAAA,OAAAP,EAAA,CAAAc,WAAA,CAAAP,MAAA;IAAA,EAA8B;IAG1EP,EAHI,CAAAI,YAAA,EACgD,EAC5C,EACF;IAIAJ,EAHN,CAAAC,cAAA,eAAsB,eACS,iBACD,cAChB;IAAAD,EAAA,CAAAG,MAAA,gCAAI;IACdH,EADc,CAAAI,YAAA,EAAS,EACf;IACRJ,EAAA,CAAAC,cAAA,iBACuE;IAD/BD,EAAA,CAAAK,gBAAA,2BAAA2B,wEAAAzB,MAAA;MAAAP,EAAA,CAAAS,aAAA,CAAAiB,GAAA;MAAA,MAAAC,MAAA,GAAA3B,EAAA,CAAA4B,aAAA;MAAA5B,EAAA,CAAAY,kBAAA,CAAAe,MAAA,CAAAI,WAAA,CAAAR,WAAA,EAAAhB,MAAA,MAAAoB,MAAA,CAAAI,WAAA,CAAAR,WAAA,GAAAhB,MAAA;MAAA,OAAAP,EAAA,CAAAc,WAAA,CAAAP,MAAA;IAAA,EAAqC;IAInFP,EAJM,CAAAI,YAAA,EACuE,EACnE,EACF,EACF;IAIFJ,EAFJ,CAAAC,cAAA,eAA6B,iBACD,cAChB;IAAAD,EAAA,CAAAG,MAAA,qEAAW;IAAAH,EAAA,CAAAC,cAAA,gBAA0B;IAAAD,EAAA,CAAAG,MAAA,SAAC;IAChDH,EADgD,CAAAI,YAAA,EAAO,EAAS,EACxD;IACRJ,EAAA,CAAAC,cAAA,eAAuC;IAKrCD,EAJA,CAAAiC,UAAA,KAAAC,8CAAA,kBAA4E,KAAAC,8CAAA,kBAIC;IAWjFnC,EADE,CAAAI,YAAA,EAAM,EACF;IAGJJ,EADF,CAAAC,cAAA,eAAqD,kBACgC;IAA9BD,EAAA,CAAAwB,UAAA,mBAAAY,iEAAA;MAAApC,EAAA,CAAAS,aAAA,CAAAiB,GAAA;MAAA,MAAAC,MAAA,GAAA3B,EAAA,CAAA4B,aAAA;MAAA,OAAA5B,EAAA,CAAAc,WAAA,CAASa,MAAA,CAAAU,iBAAA,EAAmB;IAAA,EAAC;IAChFrC,EAAA,CAAAE,SAAA,aAAiC;IAAAF,EAAA,CAAAG,MAAA,qBACnC;IAAAH,EAAA,CAAAI,YAAA,EAAS;IACTJ,EAAA,CAAAC,cAAA,kBAA8C;IAC5CD,EAAA,CAAAE,SAAA,aAAgC;IAAAF,EAAA,CAAAG,MAAA,iCAClC;IAKVH,EALU,CAAAI,YAAA,EAAS,EACL,EACD,EACH,EACF,EACF;;;;IAhDgDJ,EAAA,CAAAe,SAAA,IAA8B;IAA9Bf,EAAA,CAAAkB,gBAAA,YAAAS,MAAA,CAAAI,WAAA,CAAAV,IAAA,CAA8B;IAS9BrB,EAAA,CAAAe,SAAA,GAAqC;IAArCf,EAAA,CAAAkB,gBAAA,YAAAS,MAAA,CAAAI,WAAA,CAAAR,WAAA,CAAqC;IAWzEvB,EAAA,CAAAe,SAAA,GAAgC;IAAhCf,EAAA,CAAAsC,UAAA,SAAAX,MAAA,CAAAY,aAAA,CAAAC,MAAA,OAAgC;IAIhBxC,EAAA,CAAAe,SAAA,EAAkB;IAAlBf,EAAA,CAAAsC,UAAA,YAAAX,MAAA,CAAAY,aAAA,CAAkB;;;;;IA+BhDvC,EADF,CAAAC,cAAA,cAA4D,gBAChC;IACxBD,EAAA,CAAAG,MAAA,GACF;IACFH,EADE,CAAAI,YAAA,EAAQ,EACJ;;;;IAFFJ,EAAA,CAAAe,SAAA,GACF;IADEf,EAAA,CAAAyC,kBAAA,mBAAAd,MAAA,CAAAe,iBAAA,CAAAF,MAAA,+BAAAb,MAAA,CAAAgB,aAAA,8BACF;;;;;;IAyBU3C,EAAA,CAAAC,cAAA,iBACsC;IADPD,EAAA,CAAAwB,UAAA,mBAAAoB,yEAAA;MAAA5C,EAAA,CAAAS,aAAA,CAAAoC,GAAA;MAAA,MAAAC,MAAA,GAAA9C,EAAA,CAAA4B,aAAA,GAAAjB,SAAA;MAAA,MAAAgB,MAAA,GAAA3B,EAAA,CAAA4B,aAAA;MAAA,OAAA5B,EAAA,CAAAc,WAAA,CAAAgC,MAAA,CAAAC,UAAA,IAA2BpB,MAAA,CAAAqB,gBAAA,CAAAF,MAAA,CAAAC,UAAA,CAAgC;IAAA,EAAC;IAEzF/C,EAAA,CAAAE,SAAA,YAA4B;IAACF,EAAA,CAAAG,MAAA,qBAC/B;IAAAH,EAAA,CAAAI,YAAA,EAAS;;;;;;IAbXJ,EAFJ,CAAAC,cAAA,SAAsE,SAChE,aACM;IAAAD,EAAA,CAAAG,MAAA,GAAsB;IAChCH,EADgC,CAAAI,YAAA,EAAS,EACpC;IAEHJ,EADF,CAAAC,cAAA,SAAI,eACuB;IAAAD,EAAA,CAAAG,MAAA,GAA8B;IACzDH,EADyD,CAAAI,YAAA,EAAO,EAC3D;IAGDJ,EAFJ,CAAAC,cAAA,aAAwB,cAC2B,iBAC2B;IAA7CD,EAAA,CAAAwB,UAAA,mBAAAyB,+DAAA;MAAA,MAAAH,MAAA,GAAA9C,EAAA,CAAAS,aAAA,CAAAyC,GAAA,EAAAvC,SAAA;MAAA,MAAAgB,MAAA,GAAA3B,EAAA,CAAA4B,aAAA;MAAA,OAAA5B,EAAA,CAAAc,WAAA,CAASa,MAAA,CAAAwB,gBAAA,CAAAL,MAAA,CAAqB;IAAA,EAAC;IAC1D9C,EAAA,CAAAE,SAAA,aAA0B;IAACF,EAAA,CAAAG,MAAA,sBAC7B;IAAAH,EAAA,CAAAI,YAAA,EAAS;IACTJ,EAAA,CAAAiC,UAAA,KAAAmB,gDAAA,qBACsC;IAK5CpD,EAFI,CAAAI,YAAA,EAAM,EACH,EACF;;;;IAhBOJ,EAAA,CAAAe,SAAA,GAAsB;IAAtBf,EAAA,CAAAmB,iBAAA,CAAA2B,MAAA,CAAAO,YAAA,CAAsB;IAGLrD,EAAA,CAAAe,SAAA,GAA8B;IAA9Bf,EAAA,CAAAmB,iBAAA,CAAA2B,MAAA,CAAAQ,WAAA,yBAA8B;IAQlDtD,EAAA,CAAAe,SAAA,GAAoB;IAApBf,EAAA,CAAAsC,UAAA,SAAAQ,MAAA,CAAAC,UAAA,CAAoB;;;;;;IAavB/C,EAAA,CAAAC,cAAA,gBAAgD;IAC9CD,EAAA,CAAAG,MAAA,+DAAU;IAAAH,EAAA,CAAAC,cAAA,YAAqD;IAAxBD,EAAA,CAAAwB,UAAA,mBAAA+B,kEAAA;MAAAvD,EAAA,CAAAS,aAAA,CAAA+C,GAAA;MAAA,MAAA7B,MAAA,GAAA3B,EAAA,CAAA4B,aAAA;MAAA,OAAA5B,EAAA,CAAAc,WAAA,CAASa,MAAA,CAAA8B,WAAA,EAAa;IAAA,EAAC;IAACzD,EAAA,CAAAG,MAAA,+BAAI;IACrEH,EADqE,CAAAI,YAAA,EAAI,EACjE;;;;;IAPVJ,EAFJ,CAAAC,cAAA,SAAiE,aACtB,cACd;IACvBD,EAAA,CAAAE,SAAA,YAAwD;IACxDF,EAAA,CAAAC,cAAA,YAA2B;IACzBD,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAI;IACJJ,EAAA,CAAAiC,UAAA,IAAAyB,8CAAA,oBAAgD;IAKtD1D,EAFI,CAAAI,YAAA,EAAM,EACH,EACF;;;;IAPGJ,EAAA,CAAAe,SAAA,GACF;IADEf,EAAA,CAAA2D,kBAAA,MAAAhC,MAAA,CAAAgB,aAAA,oGACF;IAC2B3C,EAAA,CAAAe,SAAA,EAAmB;IAAnBf,EAAA,CAAAsC,UAAA,SAAAX,MAAA,CAAAgB,aAAA,CAAmB;;;;;IAyBtD3C,EADF,CAAAC,cAAA,cAA4E,aAClE;IAAAD,EAAA,CAAAG,MAAA,yBAAG;IAAAH,EAAA,CAAAI,YAAA,EAAS;IACpBJ,EAAA,CAAAC,cAAA,eAAyB;IAAAD,EAAA,CAAAG,MAAA,GAAkC;IAC7DH,EAD6D,CAAAI,YAAA,EAAO,EAC9D;;;;IADqBJ,EAAA,CAAAe,SAAA,GAAkC;IAAlCf,EAAA,CAAAmB,iBAAA,CAAAQ,MAAA,CAAAiC,gBAAA,CAAAN,WAAA,CAAkC;;;;;IAarDtD,EAHJ,CAAAC,cAAA,cACmE,cACvC,eACQ;IAAAD,EAAA,CAAAG,MAAA,GAAW;IAC7CH,EAD6C,CAAAI,YAAA,EAAO,EAC9C;IAGFJ,EAFJ,CAAAC,cAAA,cAA6C,cACjB,aAChB;IAAAD,EAAA,CAAAG,MAAA,GAAuB;IACjCH,EADiC,CAAAI,YAAA,EAAS,EACpC;IACNJ,EAAA,CAAAC,cAAA,cAAqC;IACnCD,EAAA,CAAAG,MAAA,GACF;IAEJH,EAFI,CAAAI,YAAA,EAAM,EACF,EACF;;;;;IAV8BJ,EAAA,CAAAe,SAAA,GAAW;IAAXf,EAAA,CAAAmB,iBAAA,CAAA0C,KAAA,KAAW;IAIjC7D,EAAA,CAAAe,SAAA,GAAuB;IAAvBf,EAAA,CAAA2D,kBAAA,KAAAG,UAAA,CAAAC,SAAA,MAAuB;IAG/B/D,EAAA,CAAAe,SAAA,GACF;IADEf,EAAA,CAAA2D,kBAAA,MAAAG,UAAA,CAAAE,UAAA,MACF;;;;;IAZNhE,EAAA,CAAAC,cAAA,cAAmF;IACjFD,EAAA,CAAAiC,UAAA,IAAAgC,oDAAA,mBACmE;IAarEjE,EAAA,CAAAI,YAAA,EAAM;;;;IAdoBJ,EAAA,CAAAe,SAAA,EAA2B;IAA3Bf,EAAA,CAAAsC,UAAA,YAAAX,MAAA,CAAAuC,sBAAA,CAA2B;;;;;IAiBnDlE,EAAA,CAAAC,cAAA,cAA8B;IAC5BD,EAAA,CAAAE,SAAA,YAAkD;IAClDF,EAAA,CAAAC,cAAA,YAA2B;IAAAD,EAAA,CAAAG,MAAA,iDAAO;IACpCH,EADoC,CAAAI,YAAA,EAAI,EAClC;;;;;;IA1CVJ,EAFJ,CAAAC,cAAA,cAA4D,cAC4B,aACnE;IACfD,EAAA,CAAAE,SAAA,YAAoC;IACpCF,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAK;IACLJ,EAAA,CAAAC,cAAA,iBAAiF;IAAhCD,EAAA,CAAAwB,UAAA,mBAAA2C,gEAAA;MAAAnE,EAAA,CAAAS,aAAA,CAAA2D,IAAA;MAAA,MAAAzC,MAAA,GAAA3B,EAAA,CAAA4B,aAAA;MAAA,OAAA5B,EAAA,CAAAc,WAAA,CAASa,MAAA,CAAA0C,mBAAA,EAAqB;IAAA,EAAC;IAC9ErE,EAAA,CAAAE,SAAA,YAA4B;IAACF,EAAA,CAAAG,MAAA,qBAC/B;IACFH,EADE,CAAAI,YAAA,EAAS,EACL;IAENJ,EAAA,CAAAC,cAAA,cAAqC;IACnCD,EAAA,CAAAiC,UAAA,IAAAqC,6CAAA,kBAA4E;IAM1EtE,EADF,CAAAC,cAAA,eAA4B,cACT;IACfD,EAAA,CAAAE,SAAA,aAAgC;IAChCF,EAAA,CAAAG,MAAA,IACF;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAmBLJ,EAjBA,CAAAiC,UAAA,KAAAsC,8CAAA,kBAAmF,KAAAC,sDAAA,gCAAAxE,EAAA,CAAAyE,sBAAA,CAiB3D;IAQ9BzE,EAFI,CAAAI,YAAA,EAAM,EACF,EACF;;;;;IA5CAJ,EAAA,CAAAe,SAAA,GACF;IADEf,EAAA,CAAA2D,kBAAA,oCAAAhC,MAAA,CAAAiC,gBAAA,CAAAP,YAAA,MACF;IAOMrD,EAAA,CAAAe,SAAA,GAAkC;IAAlCf,EAAA,CAAAsC,UAAA,SAAAX,MAAA,CAAAiC,gBAAA,CAAAN,WAAA,CAAkC;IAQpCtD,EAAA,CAAAe,SAAA,GACF;IADEf,EAAA,CAAA2D,kBAAA,gCAAAhC,MAAA,CAAAuC,sBAAA,CAAA1B,MAAA,cACF;IAEMxC,EAAA,CAAAe,SAAA,EAAyC;IAAAf,EAAzC,CAAAsC,UAAA,SAAAX,MAAA,CAAAuC,sBAAA,CAAA1B,MAAA,KAAyC,aAAAkC,aAAA,CAAc;;;ADlKzE,OAAM,MAAOC,uBAAuB;EAPpCC,YAAA;IAQW,KAAArC,aAAa,GAAU,EAAE,CAAC,CAAC;IAC3B,KAAAsC,UAAU,GAAW,aAAa,CAAC,CAAC;IACnC,KAAAC,cAAc,GAAG,IAAInF,YAAY,EAAY;IAC7C,KAAAoF,KAAK,GAAG,IAAIpF,YAAY,EAAQ,CAAC,CAAC;IAE5C;IACA,KAAAqF,SAAS,GAAe,EAAE;IAC1B,KAAAC,eAAe,GAAqB,EAAE;IACtC,KAAArB,gBAAgB,GAAoB,IAAI;IAExC;IACA,KAAAjB,aAAa,GAAG,EAAE;IAClB,KAAAD,iBAAiB,GAAe,EAAE;IAElC;IACA,KAAAwC,WAAW,GAAG,KAAK;IACnB,KAAAnD,WAAW,GAAG;MACZV,IAAI,EAAE,EAAE;MACRE,WAAW,EAAE,EAAE;MACf4D,aAAa,EAAE;KAChB;;EAEDC,QAAQA,CAAA;IACN,IAAI,CAACC,aAAa,EAAE;IACpB,IAAI,CAACC,uBAAuB,EAAE;EAChC;EAEAC,WAAWA,CAAA;IACT,IAAI,CAACD,uBAAuB,EAAE;EAChC;EAEA;EACAD,aAAaA,CAAA;IACX;IACAG,OAAO,CAACC,GAAG,CAAC,cAAc,EAAE;MAAEZ,UAAU,EAAE,IAAI,CAACA;IAAU,CAAE,CAAC;IAE5D,IAAI,CAACG,SAAS,GAAG,CACf;MAAEjC,UAAU,EAAE,CAAC;MAAEM,YAAY,EAAE,OAAO;MAAEC,WAAW,EAAE;IAAa,CAAE,EACpE;MAAEP,UAAU,EAAE,CAAC;MAAEM,YAAY,EAAE,OAAO;MAAEC,WAAW,EAAE;IAAa,CAAE,CACrE;IAED;IACA,IAAI,CAAC2B,eAAe,GAAG,CACrB;MACES,gBAAgB,EAAE,CAAC;MACnB3C,UAAU,EAAE,CAAC;MACb4C,KAAK,EAAE,GAAG;MACVC,UAAU,EAAE,IAAI,CAACf,UAAU;MAC3Bd,SAAS,EAAE,IAAI,CAAC8B,YAAY,CAAC,EAAE,CAAC;MAChC7B,UAAU,EAAE;KACb,EACD;MACE0B,gBAAgB,EAAE,CAAC;MACnB3C,UAAU,EAAE,CAAC;MACb4C,KAAK,EAAE,GAAG;MACVC,UAAU,EAAE,IAAI,CAACf,UAAU;MAC3Bd,SAAS,EAAE,IAAI,CAAC8B,YAAY,CAAC,EAAE,CAAC;MAChC7B,UAAU,EAAE;KACb,EACD;MACE0B,gBAAgB,EAAE,CAAC;MACnB3C,UAAU,EAAE,CAAC;MACb4C,KAAK,EAAE,GAAG;MACVC,UAAU,EAAE,IAAI,CAACf,UAAU;MAC3Bd,SAAS,EAAE,IAAI,CAAC8B,YAAY,CAAC,EAAE,CAAC;MAChC7B,UAAU,EAAE;KACb,CACF;EACH;EAEA;EACAsB,uBAAuBA,CAAA;IACrB,IAAI,CAAC,IAAI,CAAC3C,aAAa,CAACmD,IAAI,EAAE,EAAE;MAC9B,IAAI,CAACpD,iBAAiB,GAAG,CAAC,GAAG,IAAI,CAACsC,SAAS,CAAC;IAC9C,CAAC,MAAM;MACL,MAAMe,OAAO,GAAG,IAAI,CAACpD,aAAa,CAACqD,WAAW,EAAE;MAChD,IAAI,CAACtD,iBAAiB,GAAG,IAAI,CAACsC,SAAS,CAACiB,MAAM,CAACC,QAAQ,IACrDA,QAAQ,CAAC7C,YAAY,CAAC2C,WAAW,EAAE,CAACG,QAAQ,CAACJ,OAAO,CAAC,IACpDG,QAAQ,CAAC5C,WAAW,IAAI4C,QAAQ,CAAC5C,WAAW,CAAC0C,WAAW,EAAE,CAACG,QAAQ,CAACJ,OAAO,CAAE,CAC/E;IACH;EACF;EAEA;EACAK,QAAQA,CAAA;IACN,IAAI,CAACd,uBAAuB,EAAE;EAChC;EAEA;EACA7B,WAAWA,CAAA;IACT,IAAI,CAACd,aAAa,GAAG,EAAE;IACvB,IAAI,CAAC2C,uBAAuB,EAAE;EAChC;EAIA;EACAe,aAAaA,CAAA;IACX,IAAI,CAACnB,WAAW,GAAG,IAAI;IACvB,IAAI,CAACnD,WAAW,GAAG;MACjBV,IAAI,EAAE,EAAE;MACRE,WAAW,EAAE,EAAE;MACf4D,aAAa,EAAE;KAChB;IACD;IACA,IAAI,CAAC5C,aAAa,CAAC+D,OAAO,CAACC,IAAI,IAAIA,IAAI,CAAC1F,QAAQ,GAAG,KAAK,CAAC;EAC3D;EAEA;EACAwB,iBAAiBA,CAAA;IACf,IAAI,CAAC6C,WAAW,GAAG,KAAK;IACxB,IAAI,CAACnD,WAAW,GAAG;MACjBV,IAAI,EAAE,EAAE;MACRE,WAAW,EAAE,EAAE;MACf4D,aAAa,EAAE;KAChB;EACH;EAEA;EACAtD,eAAeA,CAAA;IACb,IAAI,CAAC,IAAI,CAACE,WAAW,CAACV,IAAI,CAACyE,IAAI,EAAE,EAAE;MACjCU,KAAK,CAAC,SAAS,CAAC;MAChB;IACF;IAEA,MAAMrB,aAAa,GAAG,IAAI,CAAC5C,aAAa,CAAC0D,MAAM,CAACM,IAAI,IAAIA,IAAI,CAAC1F,QAAQ,CAAC;IACtE,IAAIsE,aAAa,CAAC3C,MAAM,KAAK,CAAC,EAAE;MAC9BgE,KAAK,CAAC,WAAW,CAAC;MAClB;IACF;IAEA;IACA,IAAI,CAACC,cAAc,CAAC,IAAI,CAAC1E,WAAW,CAACV,IAAI,EAAE,IAAI,CAACU,WAAW,CAACR,WAAW,EAAE4D,aAAa,CAAC;EACzF;EAEA;EACAsB,cAAcA,CAACpF,IAAY,EAAEE,WAAmB,EAAE4D,aAAoB;IACpE;IACAK,OAAO,CAACC,GAAG,CAAC,cAAc,EAAE;MAC1BiB,YAAY,EAAErF,IAAI;MAClBE,WAAW,EAAEA,WAAW;MACxBsD,UAAU,EAAE,IAAI,CAACA,UAAU;MAC3BM,aAAa,EAAEA,aAAa,CAACwB,GAAG,CAACJ,IAAI,KAAK;QACxCK,KAAK,EAAE,IAAI,CAACC,QAAQ,CAACN,IAAI,CAAC;QAC1BO,SAAS,EAAE,IAAI,CAACjB,YAAY,CAACU,IAAI,CAAC;QAClCQ,UAAU,EAAE,IAAI,CAACC,aAAa,CAACT,IAAI;OACpC,CAAC;KACH,CAAC;IAEF;IACA,MAAMU,KAAK,GAAGC,IAAI,CAACC,GAAG,CAAC,GAAG,IAAI,CAACnC,SAAS,CAAC2B,GAAG,CAACS,CAAC,IAAIA,CAAC,CAACrE,UAAU,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC;IAE5E;IACA,MAAMhB,WAAW,GAAa;MAC5BgB,UAAU,EAAEkE,KAAK;MACjB5D,YAAY,EAAEhC,IAAI,CAACyE,IAAI,EAAE;MACzBxC,WAAW,EAAE/B,WAAW,CAACuE,IAAI;KAC9B;IAED;IACA,IAAI,CAACd,SAAS,CAACqC,IAAI,CAACtF,WAAW,CAAC;IAEhC;IACAoD,aAAa,CAACmB,OAAO,CAAC,CAACC,IAAI,EAAEe,KAAK,KAAI;MACpC,MAAMC,MAAM,GAAmB;QAC7B7B,gBAAgB,EAAE,IAAI,CAACT,eAAe,CAACzC,MAAM,GAAG8E,KAAK,GAAG,CAAC;QACzDvE,UAAU,EAAEkE,KAAK;QACjBtB,KAAK,EAAE,IAAI,CAACkB,QAAQ,CAACN,IAAI,CAAC;QAAE;QAC5BX,UAAU,EAAE,IAAI,CAACf,UAAU;QAAE;QAC7Bd,SAAS,EAAE,IAAI,CAAC8B,YAAY,CAACU,IAAI,CAAC;QAAE;QACpCvC,UAAU,EAAE,IAAI,CAACgD,aAAa,CAACT,IAAI,CAAC,CAAC;OACtC;MACD,IAAI,CAACtB,eAAe,CAACoC,IAAI,CAACE,MAAM,CAAC;IACnC,CAAC,CAAC;IAEF;IACA,IAAI,CAACjC,uBAAuB,EAAE;IAE9B;IACA,IAAI,CAACJ,WAAW,GAAG,KAAK;IACxBsB,KAAK,CAAC,OAAOnF,IAAI,cAAc8D,aAAa,CAAC3C,MAAM,MAAM,CAAC;EAC5D;EAEA;EACQqE,QAAQA,CAACN,IAAS;IACxB,OAAOA,IAAI,CAACiB,cAAc,IAAIjB,IAAI,CAACkB,EAAE,IAAIlB,IAAI,CAACmB,EAAE,IAAI,CAAC;EACvD;EAEA;EACQ7B,YAAYA,CAAC8B,KAAW;IAC9B;IACA,QAAQ,IAAI,CAAC9C,UAAU;MACrB,KAAK,aAAa;QAChB,OAAO,cAAc;MACvB;QACE,OAAO,MAAM;IACjB;EACF;EAEA;EACQmC,aAAaA,CAACT,IAAS;IAC7B,OAAOA,IAAI,CAACnF,YAAY,IAAImF,IAAI,CAAClF,IAAI,IAAIkF,IAAI,CAACqB,KAAK,IAAI,EAAE;EAC3D;EAEA;EACAzE,gBAAgBA,CAAC+C,QAAkB;IACjC,IAAI,CAACtC,gBAAgB,GAAGsC,QAAQ;IAChC,IAAI,CAACpB,cAAc,CAAC+C,IAAI,CAAC3B,QAAQ,CAAC;EACpC;EAEA;EACA4B,OAAOA,CAAA;IACL,IAAI,CAAC/C,KAAK,CAAC8C,IAAI,EAAE;EACnB;EAEA;EACA7E,gBAAgBA,CAAC+E,UAAkB;IACjC,IAAIC,OAAO,CAAC,UAAU,CAAC,EAAE;MACvB;MACA,IAAI,CAACC,kBAAkB,CAACF,UAAU,CAAC;IACrC;EACF;EAEA;EACAE,kBAAkBA,CAACF,UAAkB;IACnC;IACAvC,OAAO,CAACC,GAAG,CAAC,cAAc,EAAE;MAC1BsC,UAAU,EAAEA,UAAU;MACtBlD,UAAU,EAAE,IAAI,CAACA;KAClB,CAAC;IAEF;IACA,IAAI,CAACG,SAAS,GAAG,IAAI,CAACA,SAAS,CAACiB,MAAM,CAACmB,CAAC,IAAIA,CAAC,CAACrE,UAAU,KAAKgF,UAAU,CAAC;IACxE,IAAI,CAAC9C,eAAe,GAAG,IAAI,CAACA,eAAe,CAACgB,MAAM,CAACiC,CAAC,IAAIA,CAAC,CAACnF,UAAU,KAAKgF,UAAU,CAAC;IACpF,IAAI,CAACzC,uBAAuB,EAAE;IAE9B;IACA,IAAI,IAAI,CAAC1B,gBAAgB,EAAEb,UAAU,KAAKgF,UAAU,EAAE;MACpD,IAAI,CAACnE,gBAAgB,GAAG,IAAI;IAC9B;IAEA4C,KAAK,CAAC,OAAO,CAAC;EAChB;EAEA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EA8BA;EACAnC,mBAAmBA,CAAA;IACjB,IAAI,CAACT,gBAAgB,GAAG,IAAI;EAC9B;EAEA;EACA,IAAIM,sBAAsBA,CAAA;IACxB,IAAI,CAAC,IAAI,CAACN,gBAAgB,EAAE;MAC1B,OAAO,EAAE;IACX;IACA,OAAO,IAAI,CAACqB,eAAe,CAACgB,MAAM,CAACiC,CAAC,IAAIA,CAAC,CAACnF,UAAU,KAAK,IAAI,CAACa,gBAAiB,CAACb,UAAU,CAAC;EAC7F;EAEA;EACAoF,iBAAiBA,CAACb,KAAa,EAAEpB,QAAkB;IACjD,OAAOA,QAAQ,CAACnD,UAAU,IAAIuE,KAAK;EACrC;;;uCAnSW3C,uBAAuB;IAAA;EAAA;;;YAAvBA,uBAAuB;MAAAyD,SAAA;MAAAC,MAAA;QAAA9F,aAAA;QAAAsC,UAAA;MAAA;MAAAyD,OAAA;QAAAxD,cAAA;QAAAC,KAAA;MAAA;MAAAwD,UAAA;MAAAC,QAAA,GAAAxI,EAAA,CAAAyI,oBAAA,EAAAzI,EAAA,CAAA0I,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAA3C,QAAA,WAAA4C,iCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCVhC/I,EAFJ,CAAAC,cAAA,iBAA+D,qBAC7C,SACV;UAAAD,EAAA,CAAAG,MAAA,+BAAI;UACVH,EADU,CAAAI,YAAA,EAAK,EACE;UAOLJ,EANZ,CAAAC,cAAA,sBAAsC,aACD,aACG,aACkC,aACtC,aACD,gBAEe;UADwBD,EAAA,CAAAK,gBAAA,2BAAA4I,iEAAA1I,MAAA;YAAAP,EAAA,CAAAY,kBAAA,CAAAoI,GAAA,CAAArG,aAAA,EAAApC,MAAA,MAAAyI,GAAA,CAAArG,aAAA,GAAApC,MAAA;YAAA,OAAAA,MAAA;UAAA,EAA2B;UACvFP,EAAA,CAAAwB,UAAA,mBAAA0H,yDAAA;YAAA,OAASF,GAAA,CAAA1D,uBAAA,EAAyB;UAAA,EAAC;UADrCtF,EAAA,CAAAI,YAAA,EACsC;UAEpCJ,EADF,CAAAC,cAAA,cAAgC,kBAC0B;UACtDD,EAAA,CAAAE,SAAA,aAA6B;UAIrCF,EAHM,CAAAI,YAAA,EAAS,EACL,EACF,EACF;UACNJ,EAAA,CAAAC,cAAA,kBAAiE;UAA1BD,EAAA,CAAAwB,UAAA,mBAAA2H,0DAAA;YAAA,OAASH,GAAA,CAAA3C,aAAA,EAAe;UAAA,EAAC;UAC9DrG,EAAA,CAAAE,SAAA,aAAgC;UAAAF,EAAA,CAAAG,MAAA,qBAClC;UAIJH,EAJI,CAAAI,YAAA,EAAS,EACL,EAGF;UAGNJ,EAAA,CAAAiC,UAAA,KAAAmH,uCAAA,mBAAwD;UAmExDpJ,EAAA,CAAAC,cAAA,eAA2B;UAEzBD,EAAA,CAAAiC,UAAA,KAAAoH,uCAAA,kBAA4D;UAUpDrJ,EAJR,CAAAC,cAAA,eAA8B,iBACmB,iBAClB,UACrB,cACc;UAAAD,EAAA,CAAAG,MAAA,gCAAI;UAAAH,EAAA,CAAAI,YAAA,EAAK;UACzBJ,EAAA,CAAAC,cAAA,cAAgB;UAAAD,EAAA,CAAAG,MAAA,oBAAE;UAAAH,EAAA,CAAAI,YAAA,EAAK;UACvBJ,EAAA,CAAAC,cAAA,cAAoC;UAAAD,EAAA,CAAAG,MAAA,oBAAE;UAE1CH,EAF0C,CAAAI,YAAA,EAAK,EACxC,EACC;UACRJ,EAAA,CAAAC,cAAA,aAAO;UAoBLD,EAnBA,CAAAiC,UAAA,KAAAqH,sCAAA,kBAAsE,KAAAC,sCAAA,iBAmBL;UAgBzEvJ,EAHM,CAAAI,YAAA,EAAQ,EACF,EACJ,EACF;UAGNJ,EAAA,CAAAiC,UAAA,KAAAuH,uCAAA,mBAA4D;UAkDhExJ,EADE,CAAAI,YAAA,EAAM,EACO;UAGXJ,EAFJ,CAAAC,cAAA,sBAAgB,eACW,kBAC+B;UAApBD,EAAA,CAAAwB,UAAA,mBAAAiI,0DAAA;YAAA,OAAST,GAAA,CAAAlB,OAAA,EAAS;UAAA,EAAC;UACnD9H,EAAA,CAAAE,SAAA,aAAiC;UAAAF,EAAA,CAAAG,MAAA,qBACnC;UAGNH,EAHM,CAAAI,YAAA,EAAS,EACL,EACS,EACT;;;UAvMkEJ,EAAA,CAAAe,SAAA,IAA2B;UAA3Bf,EAAA,CAAAkB,gBAAA,YAAA8H,GAAA,CAAArG,aAAA,CAA2B;UAkB3F3C,EAAA,CAAAe,SAAA,GAAiB;UAAjBf,EAAA,CAAAsC,UAAA,SAAA0G,GAAA,CAAA9D,WAAA,CAAiB;UAqEkBlF,EAAA,CAAAe,SAAA,GAAmB;UAAnBf,EAAA,CAAAsC,UAAA,SAAA0G,GAAA,CAAArG,aAAA,CAAmB;UAgBhC3C,EAAA,CAAAe,SAAA,IAAsB;UAAAf,EAAtB,CAAAsC,UAAA,YAAA0G,GAAA,CAAAtG,iBAAA,CAAsB,iBAAAsG,GAAA,CAAAb,iBAAA,CAA0B;UAmB/DnI,EAAA,CAAAe,SAAA,EAA0D;UAA1Df,EAAA,CAAAsC,UAAA,UAAA0G,GAAA,CAAAtG,iBAAA,IAAAsG,GAAA,CAAAtG,iBAAA,CAAAF,MAAA,OAA0D;UAmBjExC,EAAA,CAAAe,SAAA,EAAsB;UAAtBf,EAAA,CAAAsC,UAAA,SAAA0G,GAAA,CAAApF,gBAAA,CAAsB;;;qBD7ItBhE,YAAY,EAAA8J,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EAAE/J,WAAW,EAAAgK,EAAA,CAAAC,aAAA,EAAAD,EAAA,CAAAE,oBAAA,EAAAF,EAAA,CAAAG,4BAAA,EAAAH,EAAA,CAAAI,eAAA,EAAAJ,EAAA,CAAAK,oBAAA,EAAAL,EAAA,CAAAM,iBAAA,EAAAN,EAAA,CAAAO,kBAAA,EAAAP,EAAA,CAAAQ,OAAA,EAAAR,EAAA,CAAAS,MAAA,EAAExK,YAAY,EAAAyK,EAAA,CAAAC,eAAA,EAAAD,EAAA,CAAAE,mBAAA,EAAAF,EAAA,CAAAG,qBAAA,EAAAH,EAAA,CAAAI,qBAAA,EAAE5K,cAAc;MAAA6K,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}