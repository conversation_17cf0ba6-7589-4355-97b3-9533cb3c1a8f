{"ast": null, "code": "/* tslint:disable */\n/* eslint-disable */\n/* Code generated by ng-openapi-gen DO NOT EDIT. */\nimport { HttpResponse } from '@angular/common/http';\nimport { filter, map } from 'rxjs/operators';\nimport { RequestBuilder } from '../../request-builder';\nexport function apiRegularChangeItemSaveRegularChangeDetailPost$Plain(http, rootUrl, params, context) {\n  const rb = new RequestBuilder(rootUrl, apiRegularChangeItemSaveRegularChangeDetailPost$Plain.PATH, 'post');\n  if (params) {\n    rb.body(params.body, 'application/*+json');\n  }\n  return http.request(rb.build({\n    responseType: 'text',\n    accept: 'text/plain',\n    context\n  })).pipe(filter(r => r instanceof HttpResponse), map(r => {\n    return r;\n  }));\n}\napiRegularChangeItemSaveRegularChangeDetailPost$Plain.PATH = '/api/RegularChangeItem/SaveRegularChangeDetail';", "map": {"version": 3, "names": ["HttpResponse", "filter", "map", "RequestBuilder", "apiRegularChangeItemSaveRegularChangeDetailPost$Plain", "http", "rootUrl", "params", "context", "rb", "PATH", "body", "request", "build", "responseType", "accept", "pipe", "r"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\services\\api\\fn\\regular-change-item\\api-regular-change-item-save-regular-change-detail-post-plain.ts"], "sourcesContent": ["/* tslint:disable */\r\n/* eslint-disable */\r\n/* Code generated by ng-openapi-gen DO NOT EDIT. */\r\n\r\nimport { HttpClient, HttpContext, HttpResponse } from '@angular/common/http';\r\nimport { Observable } from 'rxjs';\r\nimport { filter, map } from 'rxjs/operators';\r\nimport { StrictHttpResponse } from '../../strict-http-response';\r\nimport { RequestBuilder } from '../../request-builder';\r\n\r\nimport { SaveRegularChangeDetailRequest } from '../../models/save-regular-change-detail-request';\r\nimport { StringResponseBase } from '../../models/string-response-base';\r\n\r\nexport interface ApiRegularChangeItemSaveRegularChangeDetailPost$Plain$Params {\r\n      body?: Array<SaveRegularChangeDetailRequest>\r\n}\r\n\r\nexport function apiRegularChangeItemSaveRegularChangeDetailPost$Plain(http: HttpClient, rootUrl: string, params?: ApiRegularChangeItemSaveRegularChangeDetailPost$Plain$Params, context?: HttpContext): Observable<StrictHttpResponse<StringResponseBase>> {\r\n  const rb = new RequestBuilder(rootUrl, apiRegularChangeItemSaveRegularChangeDetailPost$Plain.PATH, 'post');\r\n  if (params) {\r\n    rb.body(params.body, 'application/*+json');\r\n  }\r\n\r\n  return http.request(\r\n    rb.build({ responseType: 'text', accept: 'text/plain', context })\r\n  ).pipe(\r\n    filter((r: any): r is HttpResponse<any> => r instanceof HttpResponse),\r\n    map((r: HttpResponse<any>) => {\r\n      return r as StrictHttpResponse<StringResponseBase>;\r\n    })\r\n  );\r\n}\r\n\r\napiRegularChangeItemSaveRegularChangeDetailPost$Plain.PATH = '/api/RegularChangeItem/SaveRegularChangeDetail';\r\n"], "mappings": "AAAA;AACA;AACA;AAEA,SAAkCA,YAAY,QAAQ,sBAAsB;AAE5E,SAASC,MAAM,EAAEC,GAAG,QAAQ,gBAAgB;AAE5C,SAASC,cAAc,QAAQ,uBAAuB;AAStD,OAAM,SAAUC,qDAAqDA,CAACC,IAAgB,EAAEC,OAAe,EAAEC,MAAqE,EAAEC,OAAqB;EACnM,MAAMC,EAAE,GAAG,IAAIN,cAAc,CAACG,OAAO,EAAEF,qDAAqD,CAACM,IAAI,EAAE,MAAM,CAAC;EAC1G,IAAIH,MAAM,EAAE;IACVE,EAAE,CAACE,IAAI,CAACJ,MAAM,CAACI,IAAI,EAAE,oBAAoB,CAAC;EAC5C;EAEA,OAAON,IAAI,CAACO,OAAO,CACjBH,EAAE,CAACI,KAAK,CAAC;IAAEC,YAAY,EAAE,MAAM;IAAEC,MAAM,EAAE,YAAY;IAAEP;EAAO,CAAE,CAAC,CAClE,CAACQ,IAAI,CACJf,MAAM,CAAEgB,CAAM,IAA6BA,CAAC,YAAYjB,YAAY,CAAC,EACrEE,GAAG,CAAEe,CAAoB,IAAI;IAC3B,OAAOA,CAA2C;EACpD,CAAC,CAAC,CACH;AACH;AAEAb,qDAAqD,CAACM,IAAI,GAAG,gDAAgD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}