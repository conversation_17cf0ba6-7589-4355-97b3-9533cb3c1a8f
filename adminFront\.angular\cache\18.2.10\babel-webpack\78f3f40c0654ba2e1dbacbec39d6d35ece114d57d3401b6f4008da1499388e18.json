{"ast": null, "code": "import getComputedStyle from \"./getComputedStyle.js\";\nexport default function isScrollParent(element) {\n  // Firefox wants us to check `-x` and `-y` variations as well\n  var _getComputedStyle = getComputedStyle(element),\n    overflow = _getComputedStyle.overflow,\n    overflowX = _getComputedStyle.overflowX,\n    overflowY = _getComputedStyle.overflowY;\n  return /auto|scroll|overlay|hidden/.test(overflow + overflowY + overflowX);\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}