{"ast": null, "code": "import { SharedModule } from '../../../components/shared.module';\nimport { SharedModule as AppSharedModule } from 'src/app/shared/shared.module';\nimport { CommonModule } from '@angular/common';\nimport { NbDatepickerModule } from '@nebular/theme';\nimport { LabelInOptionsPipe } from 'src/app/@theme/pipes/mapping.pipe';\nimport { BaseComponent } from '../../../components/base/baseComponent';\nimport { tap } from 'rxjs';\nimport { NbDateFnsDateModule } from '@nebular/date-fns';\nimport * as moment from 'moment';\nimport { DateFormatHourPipe } from 'src/app/@theme/pipes';\nimport { LocalStorageService } from 'src/app/shared/services/local-storage.service';\nimport { STORAGE_KEY } from 'src/app/shared/constant/constant';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/shared/helper/allowHelper\";\nimport * as i2 from \"@nebular/theme\";\nimport * as i3 from \"src/app/shared/services/message.service\";\nimport * as i4 from \"src/app/shared/helper/validationHelper\";\nimport * as i5 from \"src/services/api/services\";\nimport * as i6 from \"src/app/@core/service/review.service\";\nimport * as i7 from \"src/app/shared/services/utility.service\";\nimport * as i8 from \"src/services/api/services/HouseCustom.service\";\nimport * as i9 from \"@angular/common\";\nimport * as i10 from \"@angular/forms\";\nimport * as i11 from \"@ng-bootstrap/ng-bootstrap\";\nimport * as i12 from \"../../../components/breadcrumb/breadcrumb.component\";\nimport * as i13 from \"../../../../@theme/directives/label.directive\";\nimport * as i14 from \"../../../components/file-upload/file-upload.component\";\nimport * as i15 from \"../../../../@theme/pipes/date-format.pipe\";\nimport * as i16 from \"../../../../shared/components/household-binding/household-binding.component\";\nconst _c0 = [\"fileInput\"];\nconst _c1 = a0 => ({\n  \"!text-red\": a0\n});\nfunction ReviewDocumentManagementComponent_nb_option_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 31);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const case_r2 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", case_r2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", case_r2.label, \" \");\n  }\n}\nfunction ReviewDocumentManagementComponent_nb_option_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 31);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const case_r3 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", case_r3);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", case_r3.label, \" \");\n  }\n}\nfunction ReviewDocumentManagementComponent_nb_option_30_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 31);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const case_r4 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", case_r4);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", case_r4.label, \" \");\n  }\n}\nfunction ReviewDocumentManagementComponent_nb_option_36_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 31);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const case_r5 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", case_r5);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", case_r5.label, \" \");\n  }\n}\nfunction ReviewDocumentManagementComponent_button_39_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 32);\n    i0.ɵɵlistener(\"click\", function ReviewDocumentManagementComponent_button_39_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r6 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r6.onSearch());\n    });\n    i0.ɵɵtext(1, \" \\u67E5\\u8A62 \");\n    i0.ɵɵelement(2, \"i\", 33);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ReviewDocumentManagementComponent_button_40_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 34);\n    i0.ɵɵlistener(\"click\", function ReviewDocumentManagementComponent_button_40_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r6 = i0.ɵɵnextContext();\n      const dialog_r9 = i0.ɵɵreference(64);\n      return i0.ɵɵresetView(ctx_r6.openModel(dialog_r9));\n    });\n    i0.ɵɵtext(1, \" \\u65B0\\u589E \");\n    i0.ɵɵelement(2, \"i\", 35);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ReviewDocumentManagementComponent_tr_60_button_18_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 39);\n    i0.ɵɵlistener(\"click\", function ReviewDocumentManagementComponent_tr_60_button_18_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const item_r11 = i0.ɵɵnextContext().$implicit;\n      const ctx_r6 = i0.ɵɵnextContext();\n      const dialog_r9 = i0.ɵɵreference(64);\n      return i0.ɵɵresetView(ctx_r6.openModel(dialog_r9, item_r11));\n    });\n    i0.ɵɵtext(1, \" \\u7DE8\\u8F2F \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ReviewDocumentManagementComponent_tr_60_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\");\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"getLabelInOptions\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"td\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"td\");\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"td\");\n    i0.ɵɵtext(9);\n    i0.ɵɵpipe(10, \"getLabelInOptions\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"td\");\n    i0.ɵɵtext(12);\n    i0.ɵɵpipe(13, \"getLabelInOptions\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"td\", 36);\n    i0.ɵɵtext(15);\n    i0.ɵɵpipe(16, \"dateFormat\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"td\", 37);\n    i0.ɵɵtemplate(18, ReviewDocumentManagementComponent_tr_60_button_18_Template, 2, 0, \"button\", 38);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const item_r11 = ctx.$implicit;\n    const ctx_r6 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(3, 7, item_r11.CReviewType, ctx_r6.reviewTypeOptions), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", item_r11.CReviewName, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", item_r11.CHouse, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(10, 10, item_r11.CStatus, ctx_r6.statusOptions), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(13, 13, item_r11.CExamineStatus, ctx_r6.examineStatusOptionsQuery), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", item_r11.CActionDate ? i0.ɵɵpipeBind1(16, 16, item_r11.CActionDate) : \"\", \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r6.isUpdate);\n  }\n}\nfunction ReviewDocumentManagementComponent_ng_template_63_nb_option_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 31);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const case_r13 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", case_r13);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", case_r13.label, \" \");\n  }\n}\nfunction ReviewDocumentManagementComponent_ng_template_63_nb_option_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 31);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const case_r14 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", case_r14);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", case_r14.label, \" \");\n  }\n}\nfunction ReviewDocumentManagementComponent_ng_template_63_div_27_tr_3_th_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r15 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"th\")(1, \"nb-checkbox\", 61);\n    i0.ɵɵlistener(\"checkedChange\", function ReviewDocumentManagementComponent_ng_template_63_div_27_tr_3_th_2_Template_nb_checkbox_checkedChange_1_listener($event) {\n      const idx_r16 = i0.ɵɵrestoreView(_r15).index;\n      const ctx_r6 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r6.enableAllAtIndex($event, idx_r16));\n    });\n    i0.ɵɵelementStart(2, \"span\", 62);\n    i0.ɵɵtext(3, \"\\u5168\\u9078\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const house_r17 = ctx.$implicit;\n    const idx_r16 = ctx.index;\n    const ctx_r6 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"checked\", ctx_r6.isCheckAllColumnChecked(idx_r16))(\"disabled\", ctx_r6.latestAction === 1 || ctx_r6.checkAllHouseIsValid(house_r17.CHouseHold, null));\n  }\n}\nfunction ReviewDocumentManagementComponent_ng_template_63_div_27_tr_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\");\n    i0.ɵɵelement(1, \"th\");\n    i0.ɵɵtemplate(2, ReviewDocumentManagementComponent_ng_template_63_div_27_tr_3_th_2_Template, 4, 2, \"th\", 28);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r6 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r6.houseList2D[0]);\n  }\n}\nfunction ReviewDocumentManagementComponent_ng_template_63_div_27_tr_5_td_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r20 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"td\");\n    i0.ɵɵelementContainerStart(1);\n    i0.ɵɵelementStart(2, \"nb-checkbox\", 61);\n    i0.ɵɵtwoWayListener(\"checkedChange\", function ReviewDocumentManagementComponent_ng_template_63_div_27_tr_5_td_5_Template_nb_checkbox_checkedChange_2_listener($event) {\n      const house_r21 = i0.ɵɵrestoreView(_r20).$implicit;\n      i0.ɵɵtwoWayBindingSet(house_r21.CIsSelect, $event) || (house_r21.CIsSelect = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementStart(3, \"span\", 63);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const house_r21 = ctx.$implicit;\n    const ctx_r6 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtwoWayProperty(\"checked\", house_r21.CIsSelect);\n    i0.ɵɵproperty(\"disabled\", !house_r21.CHouseHold || !house_r21.CIsEnable || ctx_r6.latestAction === 1);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(5, _c1, house_r21.CID));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate2(\"\", house_r21.CHouseHold || \"null\", \" - \", house_r21.CFloor, \" \");\n  }\n}\nfunction ReviewDocumentManagementComponent_ng_template_63_div_27_tr_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r18 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\")(2, \"nb-checkbox\", 61);\n    i0.ɵɵlistener(\"checkedChange\", function ReviewDocumentManagementComponent_ng_template_63_div_27_tr_5_Template_nb_checkbox_checkedChange_2_listener($event) {\n      const row_r19 = i0.ɵɵrestoreView(_r18).$implicit;\n      const ctx_r6 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r6.enableAllRow($event, row_r19));\n    });\n    i0.ɵɵelementStart(3, \"span\", 62);\n    i0.ɵɵtext(4, \"\\u5168\\u9078\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(5, ReviewDocumentManagementComponent_ng_template_63_div_27_tr_5_td_5_Template, 5, 7, \"td\", 28);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const row_r19 = ctx.$implicit;\n    const ctx_r6 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"checked\", ctx_r6.isCheckAllRowChecked(row_r19))(\"disabled\", ctx_r6.latestAction === 1 || ctx_r6.checkAllHouseIsValid(null, row_r19[0].CFloor));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", row_r19);\n  }\n}\nfunction ReviewDocumentManagementComponent_ng_template_63_div_27_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 58)(1, \"table\", 59)(2, \"thead\");\n    i0.ɵɵtemplate(3, ReviewDocumentManagementComponent_ng_template_63_div_27_tr_3_Template, 3, 1, \"tr\", 60);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"tbody\");\n    i0.ɵɵtemplate(5, ReviewDocumentManagementComponent_ng_template_63_div_27_tr_5_Template, 6, 3, \"tr\", 28);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r6 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r6.houseList2D.length);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r6.houseList2D);\n  }\n}\nfunction ReviewDocumentManagementComponent_ng_template_63_div_33_tr_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\");\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"dateFormatHour\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"td\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"td\");\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"td\");\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const row_r23 = ctx.$implicit;\n    const ctx_r6 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(3, 4, row_r23.CCreateDt));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(row_r23.CCreator);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r6.getActionName(row_r23.CAction));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(row_r23.CExamineNote);\n  }\n}\nfunction ReviewDocumentManagementComponent_ng_template_63_div_33_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 64)(1, \"div\", 44)(2, \"label\", 65);\n    i0.ɵɵtext(3, \"\\u5BE9\\u6838\\u6B77\\u7A0B\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"table\", 59)(5, \"thead\")(6, \"tr\")(7, \"th\");\n    i0.ɵɵtext(8, \"\\u6642\\u9593\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"th\");\n    i0.ɵɵtext(10, \"\\u4F7F\\u7528\\u8005\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"th\");\n    i0.ɵɵtext(12, \"\\u52D5\\u4F5C\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"th\");\n    i0.ɵɵtext(14, \"\\u8AAA\\u660E\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(15, \"tbody\");\n    i0.ɵɵtemplate(16, ReviewDocumentManagementComponent_ng_template_63_div_33_tr_16_Template, 10, 6, \"tr\", 28);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r6 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(16);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r6.selectedReview.tblExamineLogs);\n  }\n}\nfunction ReviewDocumentManagementComponent_ng_template_63_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-card\", 40)(1, \"nb-card-header\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"nb-card-body\", 41)(4, \"div\", 5)(5, \"label\", 42);\n    i0.ɵɵtext(6, \"\\u985E\\u578B \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"nb-select\", 43);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function ReviewDocumentManagementComponent_ng_template_63_Template_nb_select_ngModelChange_7_listener($event) {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r6 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r6.selectedReview.selectedReviewType, $event) || (ctx_r6.selectedReview.selectedReviewType = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtemplate(8, ReviewDocumentManagementComponent_ng_template_63_nb_option_8_Template, 2, 2, \"nb-option\", 8);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"div\", 44)(10, \"app-file-upload\", 45);\n    i0.ɵɵlistener(\"fileSelected\", function ReviewDocumentManagementComponent_ng_template_63_Template_app_file_upload_fileSelected_10_listener($event) {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r6 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r6.onFileUploaded($event));\n    })(\"fileCleared\", function ReviewDocumentManagementComponent_ng_template_63_Template_app_file_upload_fileCleared_10_listener() {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r6 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r6.onFileCleared());\n    })(\"nameAutoFilled\", function ReviewDocumentManagementComponent_ng_template_63_Template_app_file_upload_nameAutoFilled_10_listener($event) {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r6 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r6.onNameAutoFilled($event));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"div\", 44)(12, \"div\", 5)(13, \"label\", 46);\n    i0.ɵɵtext(14, \" \\u72C0\\u614B \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"nb-select\", 47);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function ReviewDocumentManagementComponent_ng_template_63_Template_nb_select_ngModelChange_15_listener($event) {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r6 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r6.selectedReview.seletedStatus, $event) || (ctx_r6.selectedReview.seletedStatus = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtemplate(16, ReviewDocumentManagementComponent_ng_template_63_nb_option_16_Template, 2, 2, \"nb-option\", 8);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(17, \"div\", 44)(18, \"div\", 5)(19, \"label\", 48);\n    i0.ɵɵtext(20, \" \\u5BE9\\u6838\\u8AAA\\u660E \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"textarea\", 49);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function ReviewDocumentManagementComponent_ng_template_63_Template_textarea_ngModelChange_21_listener($event) {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r6 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r6.selectedReview.CExamineNote, $event) || (ctx_r6.selectedReview.CExamineNote = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(22, \"div\", 50)(23, \"label\", 51);\n    i0.ɵɵtext(24, \"\\u9069\\u7528\\u6236\\u5225\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(25, \"div\", 52)(26, \"app-household-binding\", 53);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function ReviewDocumentManagementComponent_ng_template_63_Template_app_household_binding_ngModelChange_26_listener($event) {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r6 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r6.selectedHouseholds, $event) || (ctx_r6.selectedHouseholds = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"houseIdChange\", function ReviewDocumentManagementComponent_ng_template_63_Template_app_household_binding_houseIdChange_26_listener($event) {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r6 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r6.onHouseholdIdChange($event));\n    })(\"selectionChange\", function ReviewDocumentManagementComponent_ng_template_63_Template_app_household_binding_selectionChange_26_listener($event) {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r6 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r6.onHouseholdSelectionChange($event));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(27, ReviewDocumentManagementComponent_ng_template_63_div_27_Template, 6, 2, \"div\", 54);\n    i0.ɵɵelementStart(28, \"div\", 29)(29, \"button\", 55);\n    i0.ɵɵlistener(\"click\", function ReviewDocumentManagementComponent_ng_template_63_Template_button_click_29_listener() {\n      const ref_r22 = i0.ɵɵrestoreView(_r12).dialogRef;\n      const ctx_r6 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r6.onClose(ref_r22));\n    });\n    i0.ɵɵtext(30, \" \\u53D6\\u6D88 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(31, \"button\", 56);\n    i0.ɵɵlistener(\"click\", function ReviewDocumentManagementComponent_ng_template_63_Template_button_click_31_listener() {\n      const ref_r22 = i0.ɵɵrestoreView(_r12).dialogRef;\n      const ctx_r6 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r6.onSaveReview(ref_r22));\n    });\n    i0.ɵɵtext(32);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(33, ReviewDocumentManagementComponent_ng_template_63_div_33_Template, 17, 1, \"div\", 57);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r6 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r6.isNew ? \"\\u65B0\\u589E\\u5BE9\\u95B1\\u6587\\u4EF6\" : \"\\u7DE8\\u8F2F\\u5BE9\\u95B1\\u6587\\u4EF6\", \" \");\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"disabled\", ctx_r6.latestAction === 1);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r6.selectedReview.selectedReviewType);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r6.reviewTypeOptions);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"config\", ctx_r6.fileUploadConfigWithState)(\"currentFileName\", ctx_r6.fileName)(\"currentFileUrl\", ctx_r6.selectedReview.CFileUrl || null);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"disabled\", ctx_r6.latestAction === 1);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r6.selectedReview.seletedStatus);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r6.statusOptions);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r6.selectedReview.CExamineNote);\n    i0.ɵɵproperty(\"rows\", 4)(\"disabled\", ctx_r6.latestAction === 1);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r6.selectedHouseholds);\n    i0.ɵɵproperty(\"buildCaseId\", ctx_r6.searchQuery.selectedBuildCase == null ? null : ctx_r6.searchQuery.selectedBuildCase.value)(\"buildingData\", ctx_r6.buildingData)(\"maxSelections\", 50)(\"disabled\", ctx_r6.latestAction === 1)(\"allowSearch\", true)(\"allowBatchSelect\", true)(\"useHouseNameMode\", false);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r6.isHouseList);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"disabled\", ctx_r6.latestAction === 1);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r6.isNew ? \"\\u65B0\\u589E\\u4E26\\u9001\\u51FA\\u5BE9\\u6838\" : \"\\u9001\\u51FA\\u5BE9\\u6838\", \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r6.isNew);\n  }\n}\nexport class ReviewDocumentManagementComponent extends BaseComponent {\n  constructor(_allow, dialogService, message, valid, _houseService, _buildCaseService, _reviewService, reviewService, utilityService, _houseCustomService) {\n    super(_allow);\n    this._allow = _allow;\n    this.dialogService = dialogService;\n    this.message = message;\n    this.valid = valid;\n    this._houseService = _houseService;\n    this._buildCaseService = _buildCaseService;\n    this._reviewService = _reviewService;\n    this.reviewService = reviewService;\n    this.utilityService = utilityService;\n    this._houseCustomService = _houseCustomService;\n    this.pageFirst = 1;\n    this.pageSize = 10;\n    this.pageIndex = 1;\n    this.totalRecords = 0;\n    this.reviewTypeOptions = [{\n      value: 1,\n      label: '標準圖' //standard drawing\n    }, {\n      value: 2,\n      label: '設備圖' //equipment drawing\n    }];\n    this.reviewTypeOptionsQuery = [{\n      value: -1,\n      label: '全部'\n    }, {\n      value: 1,\n      label: '標準圖' //standard drawing\n    }, {\n      value: 2,\n      label: '設備圖' //equipment drawing\n    }];\n    this.examineStatusOptions = [{\n      value: -1,\n      label: '待審核' //Pending review\n    }, {\n      value: 1,\n      label: '已通過' //passed\n    }, {\n      value: 2,\n      label: '已駁回' //rejected\n    }];\n    this.examineStatusOptionsQuery = [{\n      value: -1,\n      label: '全部'\n    }, {\n      value: 0,\n      label: '待審核' //Pending review\n    }, {\n      value: 1,\n      label: '已通過' //passed\n    }, {\n      value: 2,\n      label: '已駁回' //rejected\n    }];\n    this.statusOptions = [{\n      value: 1,\n      //0停用 1啟用 9刪除\n      label: '啟用' //enable\n    }, {\n      value: 2,\n      label: '停用' //Disable\n    }];\n    this.statusOptionsQuery = [{\n      value: -1,\n      label: '全部'\n    }, {\n      value: 1,\n      //0停用 1啟用 9刪除\n      label: '啟用' //enable\n    }, {\n      value: 2,\n      label: '停用' //Disable\n    }];\n    this.buildCaseOptions = [{\n      label: '全部',\n      value: ''\n    }];\n    // 新增：新戶別選擇器相關屬性\n    this.buildingData = {}; // 存放建築物戶別資料\n    this.selectedHouseholds = []; // 選中的戶別ID (使用 houseId)\n    this.fileName = null;\n    this.imageUrl = undefined;\n    this.fileUploadConfig = {\n      acceptedTypes: ['image/jpeg', 'image/jpg', 'application/pdf', '.dwg', '.dxf'],\n      acceptedFileRegex: /pdf|jpg|jpeg|png|dwg|dxf/i,\n      acceptAttribute: 'image/jpeg, image/jpg, application/pdf, .dwg, .dxf',\n      label: '上傳檔案',\n      helpText: '*請上傳PDF格式或CAD檔案（.dwg, .dxf）',\n      required: true,\n      disabled: false,\n      autoFillName: true,\n      buttonText: '上傳',\n      buttonIcon: 'fa-solid fa-cloud-arrow-up',\n      maxFileSize: 10,\n      multiple: false,\n      showPreview: false\n    };\n    // 舊的文件上傳方法 - 已被共用元件取代\n    /*\n    onFileSelected(event: any) {\n      const file: File = event.target.files[0];\n      const fileRegex = /pdf|jpg|jpeg|png|dwg|dxf/i;\n      if (!fileRegex.test(file.name)) {\n        this.message.showErrorMSG('檔案格式錯誤，僅限pdf或CAD檔案（dwg, dxf）');\n        return;\n      }\n      if (file) {\n        this.fileName = file.name;\n               // 如果名稱欄位為空，自動填入檔案名稱（去除副檔名）\n        if (!this.selectedReview.CReviewName) {\n          const fileName = file.name;\n          const fileNameWithoutExtension = fileName.substring(0, fileName.lastIndexOf('.')) || fileName;\n          this.selectedReview.CReviewName = fileNameWithoutExtension;\n        }\n               const reader = new FileReader();\n        reader.onload = (e: any) => {        // 判斷檔案類型\n          let fileType: number;\n          if (file.type.startsWith('image/')) {\n            fileType = EnumFileType.JPG; // 圖片\n          } else if (file.name.toLowerCase().includes('.dwg') || file.name.toLowerCase().includes('.dxf')) {\n            fileType = 3; // CAD檔案\n          } else {\n            fileType = EnumFileType.PDF; // PDF\n          }\n                 this.imageUrl = {\n            CName: file.name,\n            CFile: e.target?.result?.toString().split(',')[1],\n            Cimg: file.name.includes('pdf') ? file : file,\n            CFileUpload: file,\n            CFileType: fileType,\n          };\n          if (this.fileInput) {\n            this.fileInput.nativeElement.value = null;\n          }\n        };\n        reader.readAsDataURL(file);\n      }\n    }\n    */\n    this.isHouseList = false;\n    this.latestAction = 0;\n    this.isNew = true;\n  }\n  ngOnInit() {\n    if (LocalStorageService.GetSessionStorage(STORAGE_KEY.REVIEW_SEARCH) != null && LocalStorageService.GetSessionStorage(STORAGE_KEY.REVIEW_SEARCH) != undefined && LocalStorageService.GetSessionStorage(STORAGE_KEY.REVIEW_SEARCH) != \"\") {\n      let previous_search = JSON.parse(LocalStorageService.GetSessionStorage(STORAGE_KEY.REVIEW_SEARCH));\n      this.searchQuery = {\n        selectedBuildCase: null,\n        selectedReviewType: this.reviewTypeOptionsQuery.find(x => x.value == previous_search.CReviewType.value) ? this.reviewTypeOptionsQuery.find(x => x.value == previous_search.CReviewType.value) : this.reviewTypeOptionsQuery[0],\n        selectedExamineStatus: this.examineStatusOptionsQuery.find(x => x.value == previous_search.CExamineStatus.value) ? this.examineStatusOptionsQuery.find(x => x.value == previous_search.CExamineStatus.value) : this.examineStatusOptionsQuery[0],\n        seletedStatus: this.statusOptionsQuery.find(x => x.value == previous_search.CSeletedStatus.value) ? this.statusOptionsQuery.find(x => x.value == previous_search.CSeletedStatus.value) : this.statusOptionsQuery[0],\n        CReviewName: previous_search.CReviewName\n      };\n    } else {\n      this.searchQuery = {\n        selectedBuildCase: null,\n        selectedReviewType: this.reviewTypeOptionsQuery[0],\n        selectedExamineStatus: this.examineStatusOptionsQuery[0],\n        seletedStatus: this.statusOptionsQuery[0],\n        CReviewName: ''\n      };\n    }\n    this.getUserBuildCase();\n  }\n  // 文件上傳配置\n  get fileUploadConfigWithState() {\n    return {\n      ...this.fileUploadConfig,\n      disabled: this.latestAction === 1\n    };\n  }\n  clearImage() {\n    if (this.imageUrl) {\n      this.imageUrl = null;\n      this.fileName = null;\n      if (this.fileInput) {\n        this.fileInput.nativeElement.value = null; // Xóa giá trị input file\n      }\n    }\n  }\n  // 處理共用元件的文件選擇事件\n  onFileUploaded(result) {\n    this.fileName = result.fileName;\n    this.imageUrl = {\n      CName: result.CName,\n      CFile: result.CFile,\n      Cimg: result.Cimg,\n      CFileUpload: result.CFileUpload,\n      CFileType: result.CFileType\n    };\n  }\n  // 處理共用元件的文件清除事件\n  onFileCleared() {\n    this.fileName = null;\n    this.imageUrl = null;\n  }\n  // 處理共用元件的自動填入名稱事件\n  onNameAutoFilled(fileName) {\n    if (!this.selectedReview.CReviewName) {\n      this.selectedReview.CReviewName = fileName;\n    }\n  }\n  // 更新 disabled 狀態\n  get isFileUploadDisabled() {\n    return this.latestAction === 1;\n  }\n  isCheckAllRowChecked(row) {\n    return row.every(item => item.CIsSelect);\n  }\n  isCheckAllColumnChecked(index) {\n    if (this.isHouseList) {\n      if (index < 0 || index >= this.houseList2D[0].length) {\n        throw new Error(\"Invalid index. Index must be within the bounds of the array.\");\n      }\n      for (const floorData of this.houseList2D) {\n        if (index >= floorData.length || !floorData[index].CIsSelect) {\n          return false; // Found a customer with CIsEnable not true (or missing)\n        }\n      }\n      return true; // All customers at the given index have CIsEnable as true\n    }\n    return false;\n  }\n  enableAllAtIndex(checked, index) {\n    if (index < 0) {\n      throw new Error(\"Invalid index. Index must be a non-negative number.\");\n    }\n    for (const floorData of this.houseList2D) {\n      if (index < floorData.length) {\n        // Check if index is valid for this floor\n        floorData[index].CIsSelect = checked;\n      }\n    }\n  }\n  enableAllRow(checked, row) {\n    for (const item of row) {\n      item.CIsSelect = checked;\n    }\n  }\n  getItemByValue(value, options) {\n    for (const item of options) {\n      if (item.value === value) {\n        return item;\n      }\n    }\n    return null;\n  }\n  getReviewById(item, ref) {\n    this._reviewService.apiReviewGetReviewByIdPost$Json({\n      body: item.CReviewId\n    }).subscribe(res => {\n      if (res.Entries && res.StatusCode == 0) {\n        const data = res.Entries;\n        this.selectedReview = {\n          CBuildCaseId: data.tblReview?.CBuildCaseId,\n          CReviewId: data.tblReview?.CReviewId,\n          CReviewType: data.tblReview?.CReviewType,\n          CReviewName: data.tblReview?.CReviewName ? data.tblReview?.CReviewName : '',\n          CSort: data.tblReview?.CSort,\n          CStatus: data.tblReview?.CStatus,\n          CFileUrl: data.tblReview?.CFileUrl,\n          CExamineNote: data?.CExamineNote ? data?.CExamineNote : '',\n          seletedStatus: data.tblReview?.CStatus ? this.getItemByValue(data.tblReview?.CStatus, this.statusOptions) : this.statusOptions[0],\n          selectedReviewType: data.tblReview?.CReviewType ? this.getItemByValue(data.tblReview?.CReviewType, this.reviewTypeOptions) : this.reviewTypeOptions[0],\n          tblExamineLogs: data.tblExamineLogs,\n          reviewHouseHolds: data?.reviewHouseHolds?.filter(i => i.CIsSelect),\n          tblReview: data.tblReview\n        };\n        if (data && data?.tblExamineLogs && data?.tblExamineLogs.length) {\n          if (data?.tblExamineLogs.length === 0) return undefined;\n          this.latestAction = data?.tblExamineLogs[0].CAction;\n          let latestDate = data?.tblExamineLogs[0].CCreateDt ? new Date(data?.tblExamineLogs[0].CCreateDt) : '';\n          for (let i = 1; i < data.tblExamineLogs.length; i++) {\n            if (data.tblExamineLogs[i].CCreateDt) {\n              const currentDate = new Date(data.tblExamineLogs[i].CCreateDt);\n              if (currentDate > latestDate) {\n                latestDate = currentDate;\n                this.latestAction = data?.tblExamineLogs[i].CAction;\n              }\n            }\n          }\n        }\n        // 載入建築物資料\n        this.loadBuildingDataFromAPI();\n        // 設置選中的戶別ID\n        if (data?.reviewHouseHolds) {\n          this.selectedHouseholds = data.reviewHouseHolds.filter(item => item.CIsSelect).map(item => item.CHouseID).filter(id => id !== undefined);\n        }\n        this.getHouseList();\n        this.dialogService.open(ref);\n      }\n    });\n  }\n  flattenAndFilter(data) {\n    const flattened = [];\n    for (const floorData of data) {\n      for (const house of floorData) {\n        if (house.CIsSelect && house.CIsEnable) {\n          flattened.push({\n            CHouseID: house.CHouseID,\n            CIsSelect: house.CIsSelect,\n            CFloor: house.CFloor,\n            CHouseHold: house.CHouseHold\n          });\n        }\n      }\n    }\n    return flattened;\n  }\n  onSaveReview(ref) {\n    this.validation();\n    if (this.valid.errorMessages.length > 0) {\n      this.message.showErrorMSGs(this.valid.errorMessages);\n      return;\n    }\n    // 優先使用新戶別選擇器的結果，如果沒有選擇則回退到傳統表格\n    let houseReviews = [];\n    if (this.selectedHouseholds && this.selectedHouseholds.length > 0) {\n      // 使用新戶別選擇器的結果\n      houseReviews = this.convertSelectedHouseholdsToHouseReview();\n    } else if (this.houseList2D != null && this.houseList2D != undefined && this.houseList2D.length > 0) {\n      // 回退到傳統表格的結果\n      houseReviews = this.flattenAndFilter(this.houseList2D);\n    }\n    this.saveReviewPostRes = {\n      CBuildCaseId: this.searchQuery.selectedBuildCase.value,\n      CReviewId: this.selectedReview.CReviewId,\n      CReviewType: this.selectedReview.selectedReviewType.value,\n      CReviewName: this.selectedReview.CReviewName,\n      CSort: this.selectedReview?.CSort,\n      CStatus: this.selectedReview.seletedStatus.value,\n      CFile: this.imageUrl ? this.imageUrl.CFileUpload : undefined,\n      CExamineNote: this.selectedReview.CExamineNote,\n      HouseReviews: houseReviews\n    };\n    this.reviewService.SaveReview(this.saveReviewPostRes).subscribe(res => {\n      if (res && res.body && res.body.StatusCode === 0) {\n        this.message.showSucessMSG(\"執行成功\");\n        this.clearImage();\n        this.getReviewList();\n        ref.close();\n      } else {\n        this.message.showErrorMSG(res && res.body && res.body.Message);\n      }\n    });\n  }\n  onSearch() {\n    let previous_search = {\n      CReviewName: this.searchQuery.CReviewName,\n      CSelectedBuildCase: this.searchQuery.selectedBuildCase,\n      CSeletedStatus: this.searchQuery.seletedStatus,\n      CReviewType: this.searchQuery.selectedReviewType,\n      CExamineStatus: this.searchQuery.selectedExamineStatus\n    };\n    LocalStorageService.AddSessionStorage(STORAGE_KEY.REVIEW_SEARCH, JSON.stringify(previous_search));\n    this.getReviewList();\n  }\n  pageChanged(newPage) {\n    this.pageIndex = newPage;\n    this.getReviewList();\n  }\n  groupByFloor(customerData, isDefaut) {\n    const groupedData = [];\n    const uniqueFloors = Array.from(new Set(customerData.map(customer => customer.CFloor).filter(floor => floor !== null)));\n    for (const floor of uniqueFloors) {\n      groupedData.push([]);\n    }\n    for (const customer of customerData) {\n      const floorIndex = uniqueFloors.indexOf(customer.CFloor);\n      if (floorIndex !== -1) {\n        groupedData[floorIndex].push({\n          CIsSelect: customer?.CIsSelect || false,\n          CHouseID: customer.CID,\n          CHouseType: customer.CHouseType,\n          CFloor: customer.CFloor,\n          CHouseHold: customer.CHouseHold,\n          CIsEnable: customer.CIsEnable\n        });\n      }\n    }\n    return groupedData;\n  }\n  addCIsSelectToA(A, B) {\n    const mapB = new Map(B.map(item => [`${item.CHouseHold}-${item.CFloor}`, item.CIsSelect]));\n    return A.map(item => {\n      const key = `${item.CHouseHold}-${item.CFloor}`;\n      return {\n        ...item,\n        CIsSelect: mapB.has(key) ? mapB.get(key) : false\n      };\n    });\n  }\n  sortByFloorDescending(arr) {\n    return arr.sort((a, b) => (b.CFloor ?? 0) - (a.CFloor ?? 0));\n  }\n  getHouseList() {\n    return this._houseService.apiHouseGetHouseListPost$Json({\n      body: {\n        CBuildCaseID: this.searchQuery.selectedBuildCase.value,\n        CIsPagi: false\n      }\n    }).pipe(tap(res => {\n      if (res && res.StatusCode === 0 && res.Entries) {\n        const rest = this.sortByFloorDescending(res.Entries);\n        this.houseListEnable = [...rest];\n        if (this.selectedReview.CReviewId) {\n          this.houseList2D = this.groupByFloor(this.addCIsSelectToA([...this.houseListEnable], this.selectedReview.reviewHouseHolds ? [...this.selectedReview.reviewHouseHolds] : []));\n        } else {\n          this.houseList2D = this.groupByFloor([...rest]);\n        }\n        this.isHouseList = true;\n      }\n    })).subscribe();\n  }\n  openPdfInNewTab(CFileUrl) {\n    if (CFileUrl) {\n      this.utilityService.openFileNewTab(CFileUrl);\n    }\n  }\n  getReviewList() {\n    return this._reviewService.apiReviewGetReviewListPost$Json({\n      body: {\n        PageIndex: this.pageIndex,\n        PageSize: this.pageSize,\n        CReviewName: this.searchQuery.CReviewName,\n        CBuildCaseID: this.searchQuery.selectedBuildCase.value,\n        CStatus: this.searchQuery.seletedStatus.value,\n        CReviewType: this.searchQuery.selectedReviewType.value,\n        CExamineStatus: this.searchQuery.selectedExamineStatus.value\n      }\n    }).pipe(tap(res => {\n      if (res.Entries && res.StatusCode == 0) {\n        this.reviewList = res.Entries;\n        this.totalRecords = res.TotalItems;\n      }\n    })).subscribe();\n  }\n  onSelectionChangeBuildCase() {\n    if (this.searchQuery.selectedBuildCase.value) {\n      this.getReviewList();\n      // 載入新戶別選擇器的建築物資料\n      this.loadBuildingDataFromAPI();\n    }\n  }\n  getUserBuildCase() {\n    this._buildCaseService.apiBuildCaseGetUserBuildCasePost$Json({\n      body: {}\n    }).pipe(tap(res => {\n      if (res.Entries && res.StatusCode == 0) {\n        this.userBuildCaseOptions = res.Entries.map(res => {\n          return {\n            label: res.CBuildCaseName,\n            value: res.cID\n          };\n        });\n        if (LocalStorageService.GetSessionStorage(STORAGE_KEY.REVIEW_SEARCH) != null && LocalStorageService.GetSessionStorage(STORAGE_KEY.REVIEW_SEARCH) != undefined && LocalStorageService.GetSessionStorage(STORAGE_KEY.REVIEW_SEARCH) != \"\") {\n          let previous_search = JSON.parse(LocalStorageService.GetSessionStorage(STORAGE_KEY.REVIEW_SEARCH));\n          if (previous_search.CSelectedBuildCase != null && previous_search.CSelectedBuildCase != undefined) {\n            let index = this.userBuildCaseOptions.findIndex(x => x.value == previous_search.CSelectedBuildCase.value);\n            this.searchQuery.selectedBuildCase = this.userBuildCaseOptions[index];\n          } else {\n            this.searchQuery.selectedBuildCase = this.userBuildCaseOptions[0];\n          }\n        } else {\n          this.searchQuery.selectedBuildCase = this.userBuildCaseOptions[0];\n        }\n        if (this.searchQuery.selectedBuildCase.value) {\n          this.getReviewList();\n        }\n      }\n    })).subscribe();\n  }\n  openModel(ref, item) {\n    this.latestAction = 0;\n    this.isHouseList = false;\n    this.isNew = true;\n    this.clearImage();\n    // 初始化新戶別選擇器\n    this.selectedHouseholds = [];\n    this.selectedReview = {\n      selectedReviewType: this.reviewTypeOptions[0],\n      seletedStatus: this.statusOptions[0],\n      selectedExamineStatus: this.examineStatusOptions[0],\n      CReviewName: '',\n      CSort: 0,\n      CFileUrl: '',\n      CExamineNote: '',\n      CIsSelectAll: false\n    };\n    if (item) {\n      this.isNew = false;\n      this.getReviewById(item, ref);\n    } else {\n      this.isNew = true;\n      this.getHouseList();\n      // 載入新戶別選擇器的建築物資料\n      this.loadBuildingDataFromAPI();\n      this.dialogService.open(ref);\n    }\n  }\n  formatDate(CChangeDate) {\n    if (CChangeDate) {\n      return moment(CChangeDate).format('YYYY-MM-DDTHH:mm:ss');\n    }\n    return '';\n  }\n  onSubmit(ref) {}\n  onClose(ref) {\n    ref.close();\n  }\n  validation() {\n    this.valid.clear();\n    if (this.isNew && !this.imageUrl) {\n      this.valid.addErrorMessage(`前台圖片`);\n    }\n    this.valid.required('[送審說明]', this.selectedReview.CExamineNote);\n  }\n  getActionName(actionID) {\n    let textR = \"\";\n    if (actionID != undefined) {\n      switch (actionID) {\n        case 1:\n          textR = \"傳送\";\n          break;\n        case 2:\n          textR = \"通過\";\n          break;\n        case 3:\n          textR = \"駁回\";\n          break;\n        default:\n          break;\n      }\n    }\n    return textR;\n  }\n  checkAllHouseIsValid(household, floor) {\n    let count = 0;\n    let total = 0;\n    if (household != null) {\n      for (let i = 1; i < this.houseList2D.length; i++) {\n        this.houseList2D[i].map(val => {\n          if (val.CHouseHold && val.CHouseHold == household) {\n            total++;\n            if (val.CIsEnable == true) {\n              count++;\n            }\n          }\n        });\n      }\n    }\n    if (floor != null) {\n      for (let i = 0; i < this.houseList2D.length; i++) {\n        this.houseList2D[i].map(val => {\n          if (val.CFloor == floor) {\n            total++;\n            if (val.CIsEnable == true) {\n              count++;\n            }\n          }\n        });\n      }\n    }\n    if (count == total) {\n      return false;\n    }\n    return true;\n  }\n  // 新增：載入建築物戶別資料 (使用 GetDropDown API)\n  loadBuildingDataFromAPI() {\n    if (!this.searchQuery.selectedBuildCase?.value) return;\n    this._houseCustomService.getDropDown(this.searchQuery.selectedBuildCase.value).subscribe({\n      next: response => {\n        if (response && response.Entries) {\n          this.buildingData = this.convertApiResponseToBuildingData(response.Entries);\n        } else {\n          // 如果 API 失敗，使用現有的 houseList2D 轉換\n          this.buildingData = this.convertHouseList2DToBuildingData();\n        }\n      },\n      error: error => {\n        console.error('載入建築物戶別資料失敗:', error);\n        // 發生錯誤時，使用現有的 houseList2D 轉換\n        this.buildingData = this.convertHouseList2DToBuildingData();\n      }\n    });\n  }\n  // 新增：將 API 回應轉換為建築物資料格式\n  convertApiResponseToBuildingData(entries) {\n    const buildingData = {};\n    Object.entries(entries).forEach(([building, houses]) => {\n      buildingData[building] = houses.map(house => ({\n        houseName: house.CHouseHold || '',\n        building: building,\n        floor: house.CFloor?.toString() || '',\n        houseId: house.CHouseID,\n        houseType: house.CHouseType || undefined,\n        isSelected: false,\n        isDisabled: false\n      }));\n    });\n    return buildingData;\n  }\n  // 新增：將現有的 houseList2D 轉換為新戶別選擇器的格式\n  convertHouseList2DToBuildingData() {\n    const buildingData = {};\n    if (!this.houseList2D || this.houseList2D.length === 0) {\n      return buildingData;\n    }\n    this.houseList2D.forEach(row => {\n      row.forEach(house => {\n        if (house.CHouseHold && house.CFloor) {\n          const building = 'Building'; // 預設建築物名稱，可以根據實際需求調整\n          if (!buildingData[building]) {\n            buildingData[building] = [];\n          }\n          buildingData[building].push({\n            houseName: house.CHouseHold,\n            building: building,\n            floor: house.CFloor?.toString() || '',\n            houseId: house.CHouseID,\n            houseType: house.CHouseType || undefined,\n            isSelected: house.CIsSelect || false,\n            isDisabled: !house.CIsEnable\n          });\n        }\n      });\n    });\n    return buildingData;\n  }\n  // 新增：處理戶別選擇變更\n  onHouseholdSelectionChange(selectedItems) {\n    // 更新 selectedHouseholds (使用 houseId)\n    this.selectedHouseholds = selectedItems.map(item => item.houseId).filter(id => id !== undefined);\n    // 更新原有的 houseList2D 選擇狀態以保持相容性\n    this.updateHouseListSelectionByIds(this.selectedHouseholds);\n  }\n  // 新增：處理戶別ID變更事件\n  onHouseholdIdChange(selectedIds) {\n    this.selectedHouseholds = selectedIds;\n    // 更新原有的 houseList2D 選擇狀態以保持相容性\n    this.updateHouseListSelectionByIds(this.selectedHouseholds);\n  }\n  // 新增：更新原有資料結構的選擇狀態 (使用戶別ID)\n  updateHouseListSelectionByIds(selectedIds) {\n    if (!this.houseList2D) return;\n    this.houseList2D.forEach(row => {\n      row.forEach(house => {\n        house.CIsSelect = selectedIds.includes(house.CHouseID || 0);\n      });\n    });\n  }\n  // 新增：從新戶別選擇器的選擇結果轉換為原有格式\n  convertSelectedHouseholdsToHouseReview() {\n    const result = [];\n    // 從 houseList2D 中找到對應的戶別資訊\n    if (this.houseList2D && this.selectedHouseholds.length > 0) {\n      this.houseList2D.forEach(row => {\n        row.forEach(house => {\n          if (house.CHouseID && this.selectedHouseholds.includes(house.CHouseID)) {\n            result.push({\n              CHouseID: house.CHouseID,\n              CIsSelect: true,\n              CFloor: house.CFloor || undefined,\n              CHouseHold: house.CHouseHold\n            });\n          }\n        });\n      });\n    }\n    return result;\n  }\n  static {\n    this.ɵfac = function ReviewDocumentManagementComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || ReviewDocumentManagementComponent)(i0.ɵɵdirectiveInject(i1.AllowHelper), i0.ɵɵdirectiveInject(i2.NbDialogService), i0.ɵɵdirectiveInject(i3.MessageService), i0.ɵɵdirectiveInject(i4.ValidationHelper), i0.ɵɵdirectiveInject(i5.HouseService), i0.ɵɵdirectiveInject(i5.BuildCaseService), i0.ɵɵdirectiveInject(i5.ReviewService), i0.ɵɵdirectiveInject(i6.ReviewServiceCustom), i0.ɵɵdirectiveInject(i7.UtilityService), i0.ɵɵdirectiveInject(i8.HouseCustomService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ReviewDocumentManagementComponent,\n      selectors: [[\"ngx-review-document-management\"]],\n      viewQuery: function ReviewDocumentManagementComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.fileInput = _t.first);\n        }\n      },\n      standalone: true,\n      features: [i0.ɵɵInheritDefinitionFeature, i0.ɵɵStandaloneFeature],\n      decls: 65,\n      vars: 15,\n      consts: [[\"dialog\", \"\"], [\"accent\", \"success\"], [1, \"font-bold\", \"text-[#818181]\"], [1, \"d-flex\", \"flex-wrap\"], [1, \"col-md-6\"], [1, \"form-group\", \"d-flex\", \"align-items-center\", \"w-full\"], [\"for\", \"buildingName\", 1, \"label\", \"col-3\"], [\"placeholder\", \"\\u5EFA\\u6848\", 1, \"col-9\", 3, \"ngModelChange\", \"selectedChange\", \"ngModel\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"for\", \"cReviewType\", 1, \"label\", \"col-3\"], [\"placeholder\", \"\\u985E\\u578B\", 1, \"col-9\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"cReviewName\", 1, \"label\", \"col-3\"], [1, \"col-9\"], [\"type\", \"text\", \"id\", \"cReviewName\", \"nbInput\", \"\", 1, \"w-full\", \"!max-w-[290px]\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"cStatus\", 1, \"label\", \"col-3\"], [\"placeholder\", \"\\u72C0\\u614B\", 1, \"col-9\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"CExamineStatus\", 1, \"label\", \"col-3\"], [\"placeholder\", \"\\u7C3D\\u56DE\\u72C0\\u614B\", 1, \"col-9\", 3, \"ngModelChange\", \"ngModel\"], [1, \"form-group\", \"d-flex\", \"align-items-end\", \"justify-end\", \"w-full\"], [\"class\", \"btn btn-secondary btn-sm\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"btn btn-info mx-1 btn-sm\", 3, \"click\", 4, \"ngIf\"], [1, \"table-responsive\", \"mt-4\"], [1, \"table\", \"table-striped\", \"border\", 2, \"background-color\", \"#f3f3f3\"], [2, \"background-color\", \"#27ae60\", \"color\", \"white\"], [\"scope\", \"col\", 1, \"col-1\"], [\"scope\", \"col\", 1, \"col-2\"], [\"scope\", \"col\", 1, \"col-2\", \"text-center\"], [\"scope\", \"col\", 1, \"col-1\", \"text-center\"], [4, \"ngFor\", \"ngForOf\"], [1, \"d-flex\", \"justify-content-center\"], [\"aria-label\", \"Pagination\", 3, \"pageChange\", \"page\", \"pageSize\", \"collectionSize\"], [3, \"value\"], [1, \"btn\", \"btn-secondary\", \"btn-sm\", 3, \"click\"], [1, \"fas\", \"fa-search\"], [1, \"btn\", \"btn-info\", \"mx-1\", \"btn-sm\", 3, \"click\"], [1, \"fas\", \"fa-plus\"], [1, \"text-center\"], [1, \"text-center\", \"w-32\", \"px-0\"], [\"class\", \"btn btn-outline-success btn-sm text-left m-[2px]\", 3, \"click\", 4, \"ngIf\"], [1, \"btn\", \"btn-outline-success\", \"btn-sm\", \"text-left\", \"m-[2px]\", 3, \"click\"], [2, \"width\", \"800px\", \"max-height\", \"95vh\"], [1, \"px-4\"], [\"for\", \"ReviewType\", 1, \"required-field\", \"label\", \"col-3\"], [\"placeholder\", \"\\u985E\\u578B\", 1, \"col-9\", \"px-0\", 3, \"ngModelChange\", \"disabled\", \"ngModel\"], [1, \"form-group\", \"d-flex\", \"align-items-center\"], [3, \"fileSelected\", \"fileCleared\", \"nameAutoFilled\", \"config\", \"currentFileName\", \"currentFileUrl\"], [\"for\", \"CExamineStatus\", 1, \"label\", \"col-3\", \"required-field\"], [\"placeholder\", \"\\u72C0\\u614B\", 1, \"col-9\", \"px-0\", 3, \"ngModelChange\", \"disabled\", \"ngModel\"], [\"for\", \"cExamineNote\", 1, \"label\", \"col-3\", \"required-field\"], [\"nbInput\", \"\", 1, \"resize-none\", \"w-full\", \"col-9\", \"!max-w-[320px]\", 3, \"ngModelChange\", \"ngModel\", \"rows\", \"disabled\"], [1, \"form-group\", \"d-flex\", \"mb-0\"], [\"for\", \"houseList2D\", \"baseLabel\", \"\", 1, \"required-field\", \"mr-3\", 2, \"min-width\", \"75px\"], [1, \"mt-1\", \"mb-3\"], [\"placeholder\", \"\\u8ACB\\u9078\\u64C7\\u9069\\u7528\\u6236\\u5225\", 3, \"ngModelChange\", \"houseIdChange\", \"selectionChange\", \"ngModel\", \"buildCaseId\", \"buildingData\", \"maxSelections\", \"disabled\", \"allowSearch\", \"allowBatchSelect\", \"useHouseNameMode\"], [\"class\", \"table-responsive mt-1\", 4, \"ngIf\"], [1, \"btn\", \"btn-danger\", \"btn-sm\", \"mr-4\", \"min-w-[90px]\", 3, \"click\"], [1, \"btn\", \"btn-success\", \"btn-sm\", \"min-w-[90px]\", 3, \"click\", \"disabled\"], [\"class\", \"w-full\", 4, \"ngIf\"], [1, \"table-responsive\", \"mt-1\"], [1, \"table\", \"table-bordered\", 2, \"background-color\", \"#f3f3f3\"], [4, \"ngIf\"], [\"status\", \"basic\", 3, \"checkedChange\", \"checked\", \"disabled\"], [1, \"font-medium\"], [1, \"font-bold\", 3, \"ngClass\"], [1, \"w-full\"], [\"for\", \"status\", \"baseLabel\", \"\", 1, \"mr-3\", 2, \"min-width\", \"75px\"]],\n      template: function ReviewDocumentManagementComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"nb-card\", 1)(1, \"nb-card-header\");\n          i0.ɵɵelement(2, \"ngx-breadcrumb\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(3, \"nb-card-body\")(4, \"h1\", 2);\n          i0.ɵɵtext(5, \"\\u53EF\\u4E0A\\u50B3\\u8981\\u63D0\\u4F9B\\u5BA2\\u6236\\u6A19\\u6E96\\u5716\\u8AAA\\uFF0C\\u6587\\u4EF6\\u5167\\u578B\\u5206\\u70BA\\u6A19\\u6E96\\u5716\\u53CA\\u8A2D\\u5099\\uFF0C\\u4E26\\u8A2D\\u5B9A\\u8A72\\u6A94\\u6848\\u9069\\u7528\\u7684\\u6236\\u5225\\u3002\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(6, \"div\", 3)(7, \"div\", 4)(8, \"div\", 5)(9, \"label\", 6);\n          i0.ɵɵtext(10, \"\\u5EFA\\u6848\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(11, \"nb-select\", 7);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function ReviewDocumentManagementComponent_Template_nb_select_ngModelChange_11_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.searchQuery.selectedBuildCase, $event) || (ctx.searchQuery.selectedBuildCase = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵlistener(\"selectedChange\", function ReviewDocumentManagementComponent_Template_nb_select_selectedChange_11_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onSelectionChangeBuildCase());\n          });\n          i0.ɵɵtemplate(12, ReviewDocumentManagementComponent_nb_option_12_Template, 2, 2, \"nb-option\", 8);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(13, \"div\", 4)(14, \"div\", 5)(15, \"label\", 9);\n          i0.ɵɵtext(16, \"\\u985E\\u578B\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(17, \"nb-select\", 10);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function ReviewDocumentManagementComponent_Template_nb_select_ngModelChange_17_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.searchQuery.selectedReviewType, $event) || (ctx.searchQuery.selectedReviewType = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵtemplate(18, ReviewDocumentManagementComponent_nb_option_18_Template, 2, 2, \"nb-option\", 8);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(19, \"div\", 4)(20, \"div\", 5)(21, \"label\", 11);\n          i0.ɵɵtext(22, \" \\u540D\\u7A31 \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(23, \"div\", 12)(24, \"input\", 13);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function ReviewDocumentManagementComponent_Template_input_ngModelChange_24_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.searchQuery.CReviewName, $event) || (ctx.searchQuery.CReviewName = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(25, \"div\", 4)(26, \"div\", 5)(27, \"label\", 14);\n          i0.ɵɵtext(28, \" \\u72C0\\u614B \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(29, \"nb-select\", 15);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function ReviewDocumentManagementComponent_Template_nb_select_ngModelChange_29_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.searchQuery.seletedStatus, $event) || (ctx.searchQuery.seletedStatus = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵtemplate(30, ReviewDocumentManagementComponent_nb_option_30_Template, 2, 2, \"nb-option\", 8);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(31, \"div\", 4)(32, \"div\", 5)(33, \"label\", 16);\n          i0.ɵɵtext(34, \" \\u7C3D\\u56DE\\u72C0\\u614B \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(35, \"nb-select\", 17);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function ReviewDocumentManagementComponent_Template_nb_select_ngModelChange_35_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.searchQuery.selectedExamineStatus, $event) || (ctx.searchQuery.selectedExamineStatus = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵtemplate(36, ReviewDocumentManagementComponent_nb_option_36_Template, 2, 2, \"nb-option\", 8);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(37, \"div\", 4)(38, \"div\", 18);\n          i0.ɵɵtemplate(39, ReviewDocumentManagementComponent_button_39_Template, 3, 0, \"button\", 19)(40, ReviewDocumentManagementComponent_button_40_Template, 3, 0, \"button\", 20);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(41, \"div\", 21)(42, \"table\", 22)(43, \"thead\")(44, \"tr\", 23)(45, \"th\", 24);\n          i0.ɵɵtext(46, \"\\u985E\\u578B\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(47, \"th\", 24);\n          i0.ɵɵtext(48, \"\\u540D\\u7A31\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(49, \"th\", 25);\n          i0.ɵɵtext(50, \"\\u9069\\u7528\\u6236\\u578B\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(51, \"th\", 24);\n          i0.ɵɵtext(52, \"\\u72C0\\u614B\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(53, \"th\", 24);\n          i0.ɵɵtext(54, \"\\u5BE9\\u6838\\u72C0\\u614B\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(55, \"th\", 26);\n          i0.ɵɵtext(56, \"\\u5BE9\\u6838\\u65E5\\u671F\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(57, \"th\", 27);\n          i0.ɵɵtext(58, \"\\u52D5\\u4F5C\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(59, \"tbody\");\n          i0.ɵɵtemplate(60, ReviewDocumentManagementComponent_tr_60_Template, 19, 18, \"tr\", 28);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(61, \"nb-card-footer\", 29)(62, \"ngb-pagination\", 30);\n          i0.ɵɵtwoWayListener(\"pageChange\", function ReviewDocumentManagementComponent_Template_ngb_pagination_pageChange_62_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.pageIndex, $event) || (ctx.pageIndex = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵlistener(\"pageChange\", function ReviewDocumentManagementComponent_Template_ngb_pagination_pageChange_62_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.pageChanged($event));\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(63, ReviewDocumentManagementComponent_ng_template_63_Template, 34, 25, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor);\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(11);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchQuery.selectedBuildCase);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngForOf\", ctx.userBuildCaseOptions);\n          i0.ɵɵadvance(5);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchQuery.selectedReviewType);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngForOf\", ctx.reviewTypeOptionsQuery);\n          i0.ɵɵadvance(6);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchQuery.CReviewName);\n          i0.ɵɵadvance(5);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchQuery.seletedStatus);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngForOf\", ctx.statusOptionsQuery);\n          i0.ɵɵadvance(5);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchQuery.selectedExamineStatus);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngForOf\", ctx.examineStatusOptionsQuery);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", ctx.isRead);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isCreate);\n          i0.ɵɵadvance(20);\n          i0.ɵɵproperty(\"ngForOf\", ctx.reviewList);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtwoWayProperty(\"page\", ctx.pageIndex);\n          i0.ɵɵproperty(\"pageSize\", ctx.pageSize)(\"collectionSize\", ctx.totalRecords);\n        }\n      },\n      dependencies: [CommonModule, i9.NgClass, i9.NgForOf, i9.NgIf, SharedModule, i10.DefaultValueAccessor, i10.NgControlStatus, i10.NgModel, i2.NbCardComponent, i2.NbCardBodyComponent, i2.NbCardFooterComponent, i2.NbCardHeaderComponent, i2.NbCheckboxComponent, i2.NbInputDirective, i2.NbSelectComponent, i2.NbOptionComponent, i11.NgbPagination, i12.BreadcrumbComponent, i13.BaseLabelDirective, i14.FileUploadComponent, i15.DateFormatPipe, AppSharedModule, i16.HouseholdBindingComponent, NbDatepickerModule, NbDateFnsDateModule, LabelInOptionsPipe, DateFormatHourPipe],\n      styles: [\"/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsImZpbGUiOiJyZXZpZXctZG9jdW1lbnQtbWFuYWdlbWVudC5jb21wb25lbnQuc2NzcyJ9 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvcGFnZXMvY29uc3RydWN0aW9uLXByb2plY3QtbWFuYWdlbWVudC9ub3RpY2UtbWFuYWdlbWVudC9yZXZpZXctZG9jdW1lbnQtbWFuYWdlbWVudC9yZXZpZXctZG9jdW1lbnQtbWFuYWdlbWVudC5jb21wb25lbnQuc2NzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiO0FBQ0EsNExBQTRMIiwic291cmNlUm9vdCI6IiJ9 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["SharedModule", "AppSharedModule", "CommonModule", "NbDatepickerModule", "LabelInOptionsPipe", "BaseComponent", "tap", "NbDateFnsDateModule", "moment", "DateFormatHourPipe", "LocalStorageService", "STORAGE_KEY", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵproperty", "case_r2", "ɵɵadvance", "ɵɵtextInterpolate1", "label", "case_r3", "case_r4", "case_r5", "ɵɵlistener", "ReviewDocumentManagementComponent_button_39_Template_button_click_0_listener", "ɵɵrestoreView", "_r6", "ctx_r6", "ɵɵnextContext", "ɵɵresetView", "onSearch", "ɵɵelement", "ReviewDocumentManagementComponent_button_40_Template_button_click_0_listener", "_r8", "dialog_r9", "ɵɵreference", "openModel", "ReviewDocumentManagementComponent_tr_60_button_18_Template_button_click_0_listener", "_r10", "item_r11", "$implicit", "ɵɵtemplate", "ReviewDocumentManagementComponent_tr_60_button_18_Template", "ɵɵpipeBind2", "CReviewType", "reviewTypeOptions", "CReviewName", "CHouse", "CStatus", "statusOptions", "CExamineStatus", "examineStatusOptionsQuery", "CActionDate", "ɵɵpipeBind1", "isUpdate", "case_r13", "case_r14", "ReviewDocumentManagementComponent_ng_template_63_div_27_tr_3_th_2_Template_nb_checkbox_checkedChange_1_listener", "$event", "idx_r16", "_r15", "index", "enableAllAtIndex", "isCheckAllColumnChecked", "latestAction", "checkAllHouseIsValid", "house_r17", "CHouseHold", "ReviewDocumentManagementComponent_ng_template_63_div_27_tr_3_th_2_Template", "houseList2D", "ɵɵelementContainerStart", "ɵɵtwoWayListener", "ReviewDocumentManagementComponent_ng_template_63_div_27_tr_5_td_5_Template_nb_checkbox_checkedChange_2_listener", "house_r21", "_r20", "ɵɵtwoWayBindingSet", "CIsSelect", "ɵɵtwoWayProperty", "CIsEnable", "ɵɵpureFunction1", "_c1", "CID", "ɵɵtextInterpolate2", "CFloor", "ReviewDocumentManagementComponent_ng_template_63_div_27_tr_5_Template_nb_checkbox_checkedChange_2_listener", "row_r19", "_r18", "enableAllRow", "ReviewDocumentManagementComponent_ng_template_63_div_27_tr_5_td_5_Template", "isCheckAllRowChecked", "ReviewDocumentManagementComponent_ng_template_63_div_27_tr_3_Template", "ReviewDocumentManagementComponent_ng_template_63_div_27_tr_5_Template", "length", "ɵɵtextInterpolate", "row_r23", "CCreateDt", "CCreator", "getActionName", "CAction", "CExamineNote", "ReviewDocumentManagementComponent_ng_template_63_div_33_tr_16_Template", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "tblExamineLogs", "ReviewDocumentManagementComponent_ng_template_63_Template_nb_select_ngModelChange_7_listener", "_r12", "selectedReviewType", "ReviewDocumentManagementComponent_ng_template_63_nb_option_8_Template", "ReviewDocumentManagementComponent_ng_template_63_Template_app_file_upload_fileSelected_10_listener", "onFileUploaded", "ReviewDocumentManagementComponent_ng_template_63_Template_app_file_upload_fileCleared_10_listener", "onFileCleared", "ReviewDocumentManagementComponent_ng_template_63_Template_app_file_upload_nameAutoFilled_10_listener", "onNameAutoFilled", "ReviewDocumentManagementComponent_ng_template_63_Template_nb_select_ngModelChange_15_listener", "se<PERSON><PERSON><PERSON><PERSON><PERSON>", "ReviewDocumentManagementComponent_ng_template_63_nb_option_16_Template", "ReviewDocumentManagementComponent_ng_template_63_Template_textarea_ngModelChange_21_listener", "ReviewDocumentManagementComponent_ng_template_63_Template_app_household_binding_ngModelChange_26_listener", "selectedHouseholds", "ReviewDocumentManagementComponent_ng_template_63_Template_app_household_binding_houseIdChange_26_listener", "onHouseholdIdChange", "ReviewDocumentManagementComponent_ng_template_63_Template_app_household_binding_selectionChange_26_listener", "onHouseholdSelectionChange", "ReviewDocumentManagementComponent_ng_template_63_div_27_Template", "ReviewDocumentManagementComponent_ng_template_63_Template_button_click_29_listener", "ref_r22", "dialogRef", "onClose", "ReviewDocumentManagementComponent_ng_template_63_Template_button_click_31_listener", "onSaveReview", "ReviewDocumentManagementComponent_ng_template_63_div_33_Template", "isNew", "fileUploadConfigWithState", "fileName", "CFileUrl", "searchQuery", "selectedBuildCase", "value", "buildingData", "isHouseList", "ReviewDocumentManagementComponent", "constructor", "_allow", "dialogService", "message", "valid", "_houseService", "_buildCaseService", "_reviewService", "reviewService", "utilityService", "_houseCustomService", "pageFirst", "pageSize", "pageIndex", "totalRecords", "reviewTypeOptionsQuery", "examineStatusOptions", "statusOptionsQuery", "buildCaseOptions", "imageUrl", "undefined", "fileUploadConfig", "acceptedTypes", "acceptedFileRegex", "acceptAttribute", "helpText", "required", "disabled", "autoFillName", "buttonText", "buttonIcon", "maxFileSize", "multiple", "showPreview", "ngOnInit", "GetSessionStorage", "REVIEW_SEARCH", "previous_search", "JSON", "parse", "find", "x", "selectedExamineStatus", "CSeletedStatus", "getUserBuildCase", "clearImage", "fileInput", "nativeElement", "result", "CName", "CFile", "Cimg", "CFileUpload", "CFileType", "isFileUploadDisabled", "row", "every", "item", "Error", "floorData", "checked", "getItemByValue", "options", "getReviewById", "ref", "apiReviewGetReviewByIdPost$Json", "body", "CReviewId", "subscribe", "res", "Entries", "StatusCode", "data", "CBuildCaseId", "tblReview", "CSort", "reviewHouseHolds", "filter", "i", "latestDate", "Date", "currentDate", "loadBuildingDataFromAPI", "map", "CHouseID", "id", "getHouseList", "open", "flattenAndFilter", "flattened", "house", "push", "validation", "errorMessages", "showErrorMSGs", "houseReviews", "convertSelectedHouseholdsToHouseReview", "saveReviewPostRes", "HouseReviews", "SaveReview", "showSucessMSG", "getReviewList", "close", "showErrorMSG", "Message", "CSelectedBuildCase", "AddSessionStorage", "stringify", "pageChanged", "newPage", "groupByFloor", "customerData", "isDefaut", "groupedData", "uniqueFloors", "Array", "from", "Set", "customer", "floor", "floorIndex", "indexOf", "CHouseType", "addCIsSelectToA", "A", "B", "mapB", "Map", "key", "has", "get", "sortByFloorDescending", "arr", "sort", "a", "b", "apiHouseGetHouseListPost$Json", "CBuildCaseID", "CIsPagi", "pipe", "rest", "houseListEnable", "openPdfInNewTab", "openFileNewTab", "apiReviewGetReviewListPost$Json", "PageIndex", "PageSize", "reviewList", "TotalItems", "onSelectionChangeBuildCase", "apiBuildCaseGetUserBuildCasePost$Json", "userBuildCaseOptions", "CBuildCaseName", "cID", "findIndex", "CIsSelectAll", "formatDate", "CChangeDate", "format", "onSubmit", "clear", "addErrorMessage", "actionID", "textR", "household", "count", "total", "val", "getDropDown", "next", "response", "convertApiResponseToBuildingData", "convertHouseList2DToBuildingData", "error", "console", "entries", "Object", "for<PERSON>ach", "building", "houses", "houseName", "toString", "houseId", "houseType", "isSelected", "isDisabled", "selectedItems", "updateHouseListSelectionByIds", "selectedIds", "includes", "ɵɵdirectiveInject", "i1", "AllowHelper", "i2", "NbDialogService", "i3", "MessageService", "i4", "ValidationHelper", "i5", "HouseService", "BuildCaseService", "ReviewService", "i6", "ReviewServiceCustom", "i7", "UtilityService", "i8", "HouseCustomService", "selectors", "viewQuery", "ReviewDocumentManagementComponent_Query", "rf", "ctx", "ReviewDocumentManagementComponent_Template_nb_select_ngModelChange_11_listener", "_r1", "ReviewDocumentManagementComponent_Template_nb_select_selected<PERSON><PERSON>e_11_listener", "ReviewDocumentManagementComponent_nb_option_12_Template", "ReviewDocumentManagementComponent_Template_nb_select_ngModelChange_17_listener", "ReviewDocumentManagementComponent_nb_option_18_Template", "ReviewDocumentManagementComponent_Template_input_ngModelChange_24_listener", "ReviewDocumentManagementComponent_Template_nb_select_ngModelChange_29_listener", "ReviewDocumentManagementComponent_nb_option_30_Template", "ReviewDocumentManagementComponent_Template_nb_select_ngModelChange_35_listener", "ReviewDocumentManagementComponent_nb_option_36_Template", "ReviewDocumentManagementComponent_button_39_Template", "ReviewDocumentManagementComponent_button_40_Template", "ReviewDocumentManagementComponent_tr_60_Template", "ReviewDocumentManagementComponent_Template_ngb_pagination_pageChange_62_listener", "ReviewDocumentManagementComponent_ng_template_63_Template", "ɵɵtemplateRefExtractor", "isRead", "isCreate", "i9", "Ng<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "i10", "DefaultValueAccessor", "NgControlStatus", "NgModel", "NbCardComponent", "NbCardBodyComponent", "NbCardFooterComponent", "NbCardHeaderComponent", "NbCheckboxComponent", "NbInputDirective", "NbSelectComponent", "NbOptionComponent", "i11", "NgbPagination", "i12", "BreadcrumbComponent", "i13", "BaseLabelDirective", "i14", "FileUploadComponent", "i15", "DateFormatPipe", "i16", "HouseholdBindingComponent", "styles"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\construction-project-management\\notice-management\\review-document-management\\review-document-management.component.ts", "C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\construction-project-management\\notice-management\\review-document-management\\review-document-management.component.html"], "sourcesContent": ["import { Component, ElementRef, OnInit, ViewChild } from '@angular/core';\r\nimport { SharedModule } from '../../../components/shared.module';\r\nimport { SharedModule as AppSharedModule } from 'src/app/shared/shared.module';\r\nimport { CommonModule } from '@angular/common';\r\nimport { NbDatepickerModule, NbDialogService } from '@nebular/theme';\r\nimport { MessageService } from 'src/app/shared/services/message.service';\r\nimport { ValidationHelper } from 'src/app/shared/helper/validationHelper';\r\nimport { BuildCaseService, HouseService, ReviewService } from 'src/services/api/services';\r\nimport { HouseCustomService } from 'src/services/api/services/HouseCustom.service';\r\nimport { LabelInOptionsPipe } from 'src/app/@theme/pipes/mapping.pipe';\r\nimport { BaseComponent } from '../../../components/base/baseComponent';\r\nimport { AllowHelper } from 'src/app/shared/helper/allowHelper';\r\nimport { GetHouseListArgs, GetHouseListRes, GetReviewListRes, HouseReview, ReviewHouseHold, TblExamineLog, TblReview } from 'src/services/api/models';\r\nimport { tap } from 'rxjs';\r\nimport { NbDateFnsDateModule } from '@nebular/date-fns';\r\nimport * as moment from 'moment';\r\nimport { EnumFileType } from 'src/app/shared/enum/enumFileType';\r\nimport { ReviewServiceCustom } from 'src/app/@core/service/review.service';\r\nimport { DateFormatHourPipe, DateFormatPipe } from 'src/app/@theme/pipes';\r\nimport { UtilityService } from 'src/app/shared/services/utility.service';\r\nimport { LocalStorageService } from 'src/app/shared/services/local-storage.service';\r\nimport { STORAGE_KEY } from 'src/app/shared/constant/constant';\r\nimport { co } from '@fullcalendar/core/internal-common';\r\nimport { FileUploadComponent, FileUploadConfig, FileUploadResult } from '../../../components/file-upload/file-upload.component';\r\nimport { HouseholdItem, BuildingData } from 'src/app/shared/components/household-binding/household-binding.component';\r\n\r\nexport interface selectItem {\r\n  label: string,\r\n  value: number | string,\r\n}\r\n\r\nexport interface HouseList {\r\n  CFloor?: number | null;\r\n  CHouseHold?: string | null;\r\n  CHouseID?: number;\r\n  CID?: number;\r\n  CIsSelect?: boolean | null;\r\n  CHouseType?: number | null;\r\n  CIsEnable?: boolean | null;\r\n}\r\n\r\nexport interface SaveReviewPostParam {\r\n  CBuildCaseId?: number;\r\n  CReviewId?: number;\r\n  CReviewType?: number;\r\n  CReviewName?: string;\r\n  CSort?: number;\r\n  CStatus?: number;\r\n  CFile?: Blob;\r\n  CExamineNote?: string;\r\n  HouseReviews?: Array<HouseReview>;\r\n  CIsSelectAll?: boolean;\r\n  selectedCNoticeType?: any\r\n}\r\n\r\nexport interface ReviewType {\r\n  CBuildCaseId?: number;\r\n  CReviewId?: number;\r\n  CReviewType?: number | null;\r\n  CReviewName?: string;\r\n  CSort?: number;\r\n  CStatus?: number;\r\n  CFile?: Blob;\r\n  CExamineNote?: string;\r\n  HouseReviews?: Array<HouseReview>;\r\n  CIsSelectAll?: boolean;\r\n  selectedCNoticeType?: any,\r\n  CFileUrl?: string | null;\r\n  selectedBuildCase?: any | null;\r\n  selectedReviewType?: any | null;\r\n  selectedExamineStatus?: any | null;\r\n  seletedStatus?: any | null;\r\n  reviewHouseHolds?: Array<ReviewHouseHold>;\r\n  tblExamineLogs?: Array<TblExamineLog> | null;\r\n  tblReview?: TblReview;\r\n}\r\nexport interface SearchQuery {\r\n  selectedBuildCase?: any | null;\r\n  selectedReviewType?: any | null;\r\n  selectedExamineStatus?: any | null;\r\n  seletedStatus?: any | null;\r\n  CReviewName?: any | null;\r\n}\r\n@Component({\r\n  selector: 'ngx-review-document-management',\r\n  templateUrl: './review-document-management.component.html',\r\n  styleUrls: ['./review-document-management.component.scss'],\r\n  standalone: true,\r\n  imports: [CommonModule, SharedModule, AppSharedModule, NbDatepickerModule, NbDateFnsDateModule, DateFormatPipe, LabelInOptionsPipe, DateFormatHourPipe, FileUploadComponent],\r\n})\r\n\r\nexport class ReviewDocumentManagementComponent extends BaseComponent implements OnInit {\r\n  constructor(\r\n    private _allow: AllowHelper,\r\n    private dialogService: NbDialogService,\r\n    private message: MessageService,\r\n    private valid: ValidationHelper,\r\n    private _houseService: HouseService,\r\n    private _buildCaseService: BuildCaseService,\r\n    private _reviewService: ReviewService,\r\n    private reviewService: ReviewServiceCustom,\r\n    private utilityService: UtilityService,\r\n    private _houseCustomService: HouseCustomService,\r\n  ) { super(_allow) }\r\n\r\n  override pageFirst = 1;\r\n  override pageSize = 10;\r\n  override pageIndex = 1;\r\n  override totalRecords = 0;\r\n\r\n  reviewTypeOptions = [{\r\n    value: 1,\r\n    label: '標準圖', //standard drawing\r\n  }, {\r\n    value: 2,\r\n    label: '設備圖', //equipment drawing\r\n  }\r\n  ]\r\n\r\n  reviewTypeOptionsQuery = [\r\n    {\r\n      value: -1,\r\n      label: '全部'\r\n    },\r\n    {\r\n      value: 1,\r\n      label: '標準圖', //standard drawing\r\n    }, {\r\n      value: 2,\r\n      label: '設備圖', //equipment drawing\r\n    }\r\n  ]\r\n\r\n  examineStatusOptions = [\r\n    {\r\n      value: -1,\r\n      label: '待審核' //Pending review\r\n\r\n    }, {\r\n      value: 1,\r\n      label: '已通過' //passed\r\n\r\n    },\r\n    {\r\n      value: 2,\r\n      label: '已駁回' //rejected\r\n\r\n    }\r\n  ]\r\n\r\n  examineStatusOptionsQuery = [{\r\n    value: -1,\r\n    label: '全部'\r\n  },\r\n  {\r\n    value: 0,\r\n    label: '待審核' //Pending review\r\n\r\n  }, {\r\n    value: 1,\r\n    label: '已通過' //passed\r\n\r\n  },\r\n  {\r\n    value: 2,\r\n    label: '已駁回' //rejected\r\n  }\r\n  ]\r\n\r\n  statusOptions = [{\r\n    value: 1, //0停用 1啟用 9刪除\r\n    label: '啟用' //enable\r\n  }, {\r\n    value: 2,\r\n    label: '停用' //Disable\r\n  },]\r\n\r\n  statusOptionsQuery = [{\r\n    value: -1,\r\n    label: '全部'\r\n  }, {\r\n    value: 1, //0停用 1啟用 9刪除\r\n    label: '啟用' //enable\r\n  }, {\r\n    value: 2,\r\n    label: '停用' //Disable\r\n  },]\r\n\r\n\r\n  searchQuery: SearchQuery\r\n\r\n  buildCaseOptions: any[] = [{ label: '全部', value: '' }]\r\n\r\n  userBuildCaseOptions: any\r\n\r\n  // 新增：新戶別選擇器相關屬性\r\n  buildingData: BuildingData = {} // 存放建築物戶別資料\r\n  selectedHouseholds: number[] = [] // 選中的戶別ID (使用 houseId)\r\n\r\n\r\n  override ngOnInit(): void {\r\n    if (LocalStorageService.GetSessionStorage(STORAGE_KEY.REVIEW_SEARCH) != null\r\n      && LocalStorageService.GetSessionStorage(STORAGE_KEY.REVIEW_SEARCH) != undefined\r\n      && LocalStorageService.GetSessionStorage(STORAGE_KEY.REVIEW_SEARCH) != \"\") {\r\n      let previous_search = JSON.parse(LocalStorageService.GetSessionStorage(STORAGE_KEY.REVIEW_SEARCH));\r\n      this.searchQuery = {\r\n        selectedBuildCase: null,\r\n        selectedReviewType: this.reviewTypeOptionsQuery.find(x => x.value == previous_search.CReviewType.value)\r\n          ? this.reviewTypeOptionsQuery.find(x => x.value == previous_search.CReviewType.value)\r\n          : this.reviewTypeOptionsQuery[0],\r\n        selectedExamineStatus: this.examineStatusOptionsQuery.find(x => x.value == previous_search.CExamineStatus.value)\r\n          ? this.examineStatusOptionsQuery.find(x => x.value == previous_search.CExamineStatus.value)\r\n          : this.examineStatusOptionsQuery[0],\r\n        seletedStatus: this.statusOptionsQuery.find(x => x.value == previous_search.CSeletedStatus.value)\r\n          ? this.statusOptionsQuery.find(x => x.value == previous_search.CSeletedStatus.value)\r\n          : this.statusOptionsQuery[0],\r\n        CReviewName: previous_search.CReviewName\r\n      }\r\n    }\r\n    else {\r\n      this.searchQuery = {\r\n        selectedBuildCase: null,\r\n        selectedReviewType: this.reviewTypeOptionsQuery[0],\r\n        selectedExamineStatus: this.examineStatusOptionsQuery[0],\r\n        seletedStatus: this.statusOptionsQuery[0],\r\n        CReviewName: ''\r\n      }\r\n    }\r\n    this.getUserBuildCase()\r\n  }\r\n  @ViewChild('fileInput') fileInput!: ElementRef;\r\n  fileName: string | null = null;\r\n  imageUrl: any = undefined;\r\n  // 文件上傳配置\r\n  get fileUploadConfigWithState(): FileUploadConfig {\r\n    return {\r\n      ...this.fileUploadConfig,\r\n      disabled: this.latestAction === 1\r\n    };\r\n  }\r\n  fileUploadConfig: FileUploadConfig = {\r\n    acceptedTypes: ['image/jpeg', 'image/jpg', 'application/pdf', '.dwg', '.dxf'],\r\n    acceptedFileRegex: /pdf|jpg|jpeg|png|dwg|dxf/i,\r\n    acceptAttribute: 'image/jpeg, image/jpg, application/pdf, .dwg, .dxf',\r\n    label: '上傳檔案',\r\n    helpText: '*請上傳PDF格式或CAD檔案（.dwg, .dxf）',\r\n    required: true,\r\n    disabled: false,\r\n    autoFillName: true,\r\n    buttonText: '上傳',\r\n    buttonIcon: 'fa-solid fa-cloud-arrow-up',\r\n    maxFileSize: 10,\r\n    multiple: false,\r\n    showPreview: false\r\n  };\r\n  clearImage() {\r\n    if (this.imageUrl) {\r\n      this.imageUrl = null;\r\n      this.fileName = null;\r\n      if (this.fileInput) {\r\n        this.fileInput.nativeElement.value = null; // Xóa giá trị input file\r\n      }\r\n    }\r\n  }\r\n\r\n  // 處理共用元件的文件選擇事件\r\n  onFileUploaded(result: FileUploadResult) {\r\n    this.fileName = result.fileName;\r\n    this.imageUrl = {\r\n      CName: result.CName,\r\n      CFile: result.CFile,\r\n      Cimg: result.Cimg,\r\n      CFileUpload: result.CFileUpload,\r\n      CFileType: result.CFileType,\r\n    };\r\n  }\r\n\r\n  // 處理共用元件的文件清除事件\r\n  onFileCleared() {\r\n    this.fileName = null;\r\n    this.imageUrl = null;\r\n  }\r\n\r\n  // 處理共用元件的自動填入名稱事件\r\n  onNameAutoFilled(fileName: string) {\r\n    if (!this.selectedReview.CReviewName) {\r\n      this.selectedReview.CReviewName = fileName;\r\n    }\r\n  }\r\n\r\n  // 更新 disabled 狀態\r\n  get isFileUploadDisabled(): boolean {\r\n    return this.latestAction === 1;\r\n  }\r\n\r\n  // 舊的文件上傳方法 - 已被共用元件取代\r\n  /*\r\n  onFileSelected(event: any) {\r\n    const file: File = event.target.files[0];\r\n    const fileRegex = /pdf|jpg|jpeg|png|dwg|dxf/i;\r\n    if (!fileRegex.test(file.name)) {\r\n      this.message.showErrorMSG('檔案格式錯誤，僅限pdf或CAD檔案（dwg, dxf）');\r\n      return;\r\n    }\r\n    if (file) {\r\n      this.fileName = file.name;\r\n\r\n      // 如果名稱欄位為空，自動填入檔案名稱（去除副檔名）\r\n      if (!this.selectedReview.CReviewName) {\r\n        const fileName = file.name;\r\n        const fileNameWithoutExtension = fileName.substring(0, fileName.lastIndexOf('.')) || fileName;\r\n        this.selectedReview.CReviewName = fileNameWithoutExtension;\r\n      }\r\n\r\n      const reader = new FileReader();\r\n      reader.onload = (e: any) => {        // 判斷檔案類型\r\n        let fileType: number;\r\n        if (file.type.startsWith('image/')) {\r\n          fileType = EnumFileType.JPG; // 圖片\r\n        } else if (file.name.toLowerCase().includes('.dwg') || file.name.toLowerCase().includes('.dxf')) {\r\n          fileType = 3; // CAD檔案\r\n        } else {\r\n          fileType = EnumFileType.PDF; // PDF\r\n        }\r\n\r\n        this.imageUrl = {\r\n          CName: file.name,\r\n          CFile: e.target?.result?.toString().split(',')[1],\r\n          Cimg: file.name.includes('pdf') ? file : file,\r\n          CFileUpload: file,\r\n          CFileType: fileType,\r\n        };\r\n        if (this.fileInput) {\r\n          this.fileInput.nativeElement.value = null;\r\n        }\r\n      };\r\n      reader.readAsDataURL(file);\r\n    }\r\n  }\r\n  */\r\n\r\n  isHouseList = false\r\n\r\n  houseListDefault: any\r\n\r\n  isCheckAllRowChecked(row: any[]): boolean {\r\n    return row.every((item: { CIsSelect: any; }) => item.CIsSelect);\r\n  }\r\n\r\n  isCheckAllColumnChecked(index: number): boolean {\r\n    if (this.isHouseList) {\r\n      if (index < 0 || index >= this.houseList2D[0].length) {\r\n        throw new Error(\"Invalid index. Index must be within the bounds of the array.\");\r\n      }\r\n      for (const floorData of this.houseList2D) {\r\n        if (index >= floorData.length || !floorData[index].CIsSelect) {\r\n          return false; // Found a customer with CIsEnable not true (or missing)\r\n        }\r\n      }\r\n      return true; // All customers at the given index have CIsEnable as true\r\n    }\r\n    return false\r\n  }\r\n\r\n  enableAllAtIndex(checked: boolean, index: number): void {\r\n    if (index < 0) {\r\n      throw new Error(\"Invalid index. Index must be a non-negative number.\");\r\n    }\r\n    for (const floorData of this.houseList2D) {\r\n      if (index < floorData.length) { // Check if index is valid for this floor\r\n        floorData[index].CIsSelect = checked;\r\n      }\r\n    }\r\n  }\r\n\r\n  enableAllRow(checked: boolean, row: any[]) {\r\n    for (const item of row) {\r\n      item.CIsSelect = checked;\r\n    }\r\n  }\r\n  selectedReview: ReviewType\r\n\r\n  getItemByValue(value: any, options: any[]) {\r\n    for (const item of options) {\r\n      if (item.value === value) {\r\n        return item;\r\n      }\r\n    }\r\n    return null;\r\n  }\r\n  latestAction: any = 0\r\n\r\n  getReviewById(item: any, ref: any) {\r\n\r\n    this._reviewService.apiReviewGetReviewByIdPost$Json({\r\n      body: item.CReviewId\r\n    }).subscribe(res => {\r\n      if (res.Entries && res.StatusCode == 0) {\r\n        const data = res.Entries\r\n        this.selectedReview = {\r\n          CBuildCaseId: data.tblReview?.CBuildCaseId,\r\n          CReviewId: data.tblReview?.CReviewId,\r\n          CReviewType: data.tblReview?.CReviewType,\r\n          CReviewName: data.tblReview?.CReviewName ? data.tblReview?.CReviewName : '',\r\n          CSort: data.tblReview?.CSort,\r\n          CStatus: data.tblReview?.CStatus,\r\n          CFileUrl: data.tblReview?.CFileUrl,\r\n          CExamineNote: data?.CExamineNote ? data?.CExamineNote : '',\r\n          seletedStatus: data.tblReview?.CStatus ? this.getItemByValue(data.tblReview?.CStatus, this.statusOptions) : this.statusOptions[0],\r\n          selectedReviewType: data.tblReview?.CReviewType ? this.getItemByValue(data.tblReview?.CReviewType, this.reviewTypeOptions) : this.reviewTypeOptions[0],\r\n          tblExamineLogs: data.tblExamineLogs,\r\n          reviewHouseHolds: data?.reviewHouseHolds?.filter((i: any) => i.CIsSelect),\r\n          tblReview: data.tblReview\r\n        }\r\n\r\n        if (data && data?.tblExamineLogs && data?.tblExamineLogs.length) {\r\n          if (data?.tblExamineLogs.length === 0) return undefined;\r\n          this.latestAction = data?.tblExamineLogs[0].CAction;\r\n          let latestDate = data?.tblExamineLogs[0].CCreateDt ? new Date(data?.tblExamineLogs[0].CCreateDt) : ''\r\n          for (let i = 1; i < data.tblExamineLogs.length; i++) {\r\n            if (data.tblExamineLogs[i].CCreateDt) {\r\n              const currentDate = new Date(data.tblExamineLogs[i].CCreateDt!)\r\n              if (currentDate > latestDate) {\r\n                latestDate = currentDate;\r\n                this.latestAction = data?.tblExamineLogs[i].CAction;\r\n              }\r\n            }\r\n          }\r\n        }\r\n\r\n        // 載入建築物資料\r\n        this.loadBuildingDataFromAPI()\r\n\r\n        // 設置選中的戶別ID\r\n        if (data?.reviewHouseHolds) {\r\n          this.selectedHouseholds = data.reviewHouseHolds\r\n            .filter((item: any) => item.CIsSelect)\r\n            .map((item: any) => item.CHouseID)\r\n            .filter((id: any) => id !== undefined);\r\n        }\r\n\r\n        this.getHouseList()\r\n        this.dialogService.open(ref)\r\n      }\r\n    })\r\n  }\r\n\r\n  saveReviewPostRes: SaveReviewPostParam\r\n\r\n  flattenAndFilter(data: any[][]): HouseReview[] {\r\n    const flattened: HouseReview[] = [];\r\n    for (const floorData of data) {\r\n      for (const house of floorData) {\r\n        if (house.CIsSelect && house.CIsEnable) {\r\n          flattened.push({\r\n            CHouseID: house.CHouseID,\r\n            CIsSelect: house.CIsSelect,\r\n            CFloor: house.CFloor,\r\n            CHouseHold: house.CHouseHold,\r\n          });\r\n        }\r\n      }\r\n    }\r\n    return flattened;\r\n  }\r\n\r\n  houseList2D: HouseList[][]\r\n\r\n  onSaveReview(ref: any) {\r\n    this.validation()\r\n    if (this.valid.errorMessages.length > 0) {\r\n      this.message.showErrorMSGs(this.valid.errorMessages);\r\n      return\r\n    }\r\n\r\n    // 優先使用新戶別選擇器的結果，如果沒有選擇則回退到傳統表格\r\n    let houseReviews: HouseReview[] = [];\r\n\r\n    if (this.selectedHouseholds && this.selectedHouseholds.length > 0) {\r\n      // 使用新戶別選擇器的結果\r\n      houseReviews = this.convertSelectedHouseholdsToHouseReview();\r\n    } else if (this.houseList2D != null && this.houseList2D != undefined && this.houseList2D.length > 0) {\r\n      // 回退到傳統表格的結果\r\n      houseReviews = this.flattenAndFilter(this.houseList2D);\r\n    }\r\n\r\n    this.saveReviewPostRes = {\r\n      CBuildCaseId: this.searchQuery.selectedBuildCase.value,\r\n      CReviewId: this.selectedReview.CReviewId,\r\n      CReviewType: this.selectedReview.selectedReviewType.value,\r\n      CReviewName: this.selectedReview.CReviewName,\r\n      CSort: this.selectedReview?.CSort,\r\n      CStatus: this.selectedReview.seletedStatus.value,\r\n      CFile: this.imageUrl ? this.imageUrl.CFileUpload : undefined,\r\n      CExamineNote: this.selectedReview.CExamineNote,\r\n      HouseReviews: houseReviews,\r\n    }\r\n    this.reviewService.SaveReview(this.saveReviewPostRes).subscribe(res => {\r\n      if (res && res.body! && res.body.StatusCode! === 0) {\r\n        this.message.showSucessMSG(\"執行成功\");\r\n        this.clearImage()\r\n        this.getReviewList()\r\n        ref.close();\r\n      } else {\r\n        this.message.showErrorMSG(res && res.body && res.body.Message!);\r\n      }\r\n    })\r\n  }\r\n\r\n  onSearch() {\r\n    let previous_search = {\r\n      CReviewName: this.searchQuery.CReviewName,\r\n      CSelectedBuildCase: this.searchQuery.selectedBuildCase,\r\n      CSeletedStatus: this.searchQuery.seletedStatus,\r\n      CReviewType: this.searchQuery.selectedReviewType,\r\n      CExamineStatus: this.searchQuery.selectedExamineStatus,\r\n    }\r\n    LocalStorageService.AddSessionStorage(STORAGE_KEY.REVIEW_SEARCH, JSON.stringify(previous_search));\r\n    this.getReviewList()\r\n  }\r\n\r\n  pageChanged(newPage: number) {\r\n    this.pageIndex = newPage;\r\n    this.getReviewList()\r\n  }\r\n\r\n  bodyRequest: GetHouseListArgs\r\n\r\n  groupByFloor(customerData: HouseList[], isDefaut?: any): HouseList[][] {\r\n    const groupedData: HouseList[][] = [];\r\n    const uniqueFloors = Array.from(new Set(\r\n      customerData.map(customer => customer.CFloor).filter(floor => floor !== null) as number[]\r\n    ));\r\n    for (const floor of uniqueFloors) {\r\n      groupedData.push([]);\r\n    }\r\n    for (const customer of customerData) {\r\n      const floorIndex = uniqueFloors.indexOf(customer.CFloor as number);\r\n      if (floorIndex !== -1) {\r\n        groupedData[floorIndex].push({\r\n          CIsSelect: customer?.CIsSelect || false,\r\n          CHouseID: customer.CID,\r\n          CHouseType: customer.CHouseType,\r\n          CFloor: customer.CFloor,\r\n          CHouseHold: customer.CHouseHold,\r\n          CIsEnable: customer.CIsEnable,\r\n        });\r\n      }\r\n    }\r\n    return groupedData;\r\n  }\r\n\r\n\r\n  houseListEnable: any[]\r\n\r\n\r\n  addCIsSelectToA(A: any[], B: any[]): any[] {\r\n    const mapB = new Map(B.map(item => [`${item.CHouseHold}-${item.CFloor}`, item.CIsSelect]));\r\n    return A.map(item => {\r\n      const key = `${item.CHouseHold}-${item.CFloor}`;\r\n      return {\r\n        ...item,\r\n        CIsSelect: mapB.has(key) ? mapB.get(key) : false\r\n      };\r\n    });\r\n  }\r\n\r\n  sortByFloorDescending(arr: GetHouseListRes[]): GetHouseListRes[] {\r\n    return arr.sort((a, b) => (b.CFloor ?? 0) - (a.CFloor ?? 0));\r\n  }\r\n\r\n  getHouseList() {\r\n    return this._houseService.apiHouseGetHouseListPost$Json({\r\n      body: { CBuildCaseID: this.searchQuery.selectedBuildCase.value, CIsPagi: false }\r\n    }).pipe(\r\n      tap(res => {\r\n        if (res && res.StatusCode === 0 && res.Entries) {\r\n          const rest = this.sortByFloorDescending(res.Entries)\r\n          this.houseListEnable = [...rest]\r\n          if (this.selectedReview.CReviewId) {\r\n            this.houseList2D = this.groupByFloor(this.addCIsSelectToA([...this.houseListEnable], this.selectedReview.reviewHouseHolds ? [...this.selectedReview.reviewHouseHolds] : []))\r\n          } else {\r\n            this.houseList2D = this.groupByFloor([...rest])\r\n          }\r\n          this.isHouseList = true\r\n        }\r\n      }),\r\n\r\n    ).subscribe();\r\n  }\r\n\r\n\r\n  reviewList: GetReviewListRes[] | undefined\r\n\r\n  isNew = true\r\n\r\n  openPdfInNewTab(CFileUrl?: any) {\r\n    if (CFileUrl) {\r\n      this.utilityService.openFileNewTab(CFileUrl)\r\n    }\r\n  }\r\n\r\n  getReviewList() {\r\n    return this._reviewService.apiReviewGetReviewListPost$Json({\r\n      body: {\r\n        PageIndex: this.pageIndex,\r\n        PageSize: this.pageSize,\r\n        CReviewName: this.searchQuery.CReviewName,\r\n        CBuildCaseID: this.searchQuery.selectedBuildCase.value,\r\n        CStatus: this.searchQuery.seletedStatus.value,\r\n        CReviewType: this.searchQuery.selectedReviewType.value,\r\n        CExamineStatus: this.searchQuery.selectedExamineStatus.value,\r\n      }\r\n    }).pipe(\r\n      tap(res => {\r\n        if (res.Entries && res.StatusCode == 0) {\r\n          this.reviewList = res.Entries\r\n          this.totalRecords = res.TotalItems!\r\n        }\r\n      })\r\n    ).subscribe()\r\n  }\r\n\r\n\r\n  onSelectionChangeBuildCase() {\r\n    if (this.searchQuery.selectedBuildCase.value) {\r\n      this.getReviewList()\r\n      // 載入新戶別選擇器的建築物資料\r\n      this.loadBuildingDataFromAPI()\r\n    }\r\n  }\r\n\r\n  getUserBuildCase() {\r\n    this._buildCaseService.apiBuildCaseGetUserBuildCasePost$Json({ body: {} }).pipe(\r\n      tap(res => {\r\n        if (res.Entries && res.StatusCode == 0) {\r\n          this.userBuildCaseOptions = res.Entries.map(res => {\r\n            return {\r\n              label: res.CBuildCaseName,\r\n              value: res.cID\r\n            }\r\n          })\r\n          if (LocalStorageService.GetSessionStorage(STORAGE_KEY.REVIEW_SEARCH) != null\r\n            && LocalStorageService.GetSessionStorage(STORAGE_KEY.REVIEW_SEARCH) != undefined\r\n            && LocalStorageService.GetSessionStorage(STORAGE_KEY.REVIEW_SEARCH) != \"\") {\r\n            let previous_search = JSON.parse(LocalStorageService.GetSessionStorage(STORAGE_KEY.REVIEW_SEARCH));\r\n            if (previous_search.CSelectedBuildCase != null && previous_search.CSelectedBuildCase != undefined) {\r\n              let index = this.userBuildCaseOptions.findIndex((x: any) => x.value == previous_search.CSelectedBuildCase.value)\r\n              this.searchQuery.selectedBuildCase = this.userBuildCaseOptions[index]\r\n            } else {\r\n              this.searchQuery.selectedBuildCase = this.userBuildCaseOptions[0]\r\n            }\r\n          }\r\n          else {\r\n            this.searchQuery.selectedBuildCase = this.userBuildCaseOptions[0]\r\n          }\r\n          if (this.searchQuery.selectedBuildCase.value) {\r\n            this.getReviewList()\r\n          }\r\n        }\r\n      }),\r\n    ).subscribe()\r\n  }\r\n\r\n  openModel(ref: any, item?: any) {\r\n    this.latestAction = 0\r\n    this.isHouseList = false\r\n    this.isNew = true\r\n    this.clearImage()\r\n\r\n    // 初始化新戶別選擇器\r\n    this.selectedHouseholds = []\r\n\r\n    this.selectedReview = {\r\n      selectedReviewType: this.reviewTypeOptions[0],\r\n      seletedStatus: this.statusOptions[0],\r\n      selectedExamineStatus: this.examineStatusOptions[0],\r\n      CReviewName: '',\r\n      CSort: 0,\r\n      CFileUrl: '',\r\n      CExamineNote: '',\r\n      CIsSelectAll: false\r\n    }\r\n\r\n    if (item) {\r\n      this.isNew = false\r\n      this.getReviewById(item, ref)\r\n    } else {\r\n      this.isNew = true\r\n      this.getHouseList()\r\n      // 載入新戶別選擇器的建築物資料\r\n      this.loadBuildingDataFromAPI()\r\n      this.dialogService.open(ref)\r\n    }\r\n  }\r\n\r\n  formatDate(CChangeDate: string): string {\r\n    if (CChangeDate) {\r\n      return moment(CChangeDate).format('YYYY-MM-DDTHH:mm:ss')\r\n    }\r\n    return ''\r\n  }\r\n\r\n  onSubmit(ref: any) {\r\n\r\n  }\r\n\r\n  onClose(ref: any) {\r\n    ref.close();\r\n  }\r\n\r\n\r\n  validation() {\r\n    this.valid.clear();\r\n    if (this.isNew && !this.imageUrl) {\r\n      this.valid.addErrorMessage(`前台圖片`);\r\n    }\r\n    this.valid.required('[送審說明]', this.selectedReview.CExamineNote)\r\n  }\r\n\r\n  getActionName(actionID: number | undefined) {\r\n    let textR = \"\";\r\n    if (actionID != undefined) {\r\n      switch (actionID) {\r\n        case 1:\r\n          textR = \"傳送\";\r\n          break;\r\n        case 2:\r\n          textR = \"通過\";\r\n          break;\r\n        case 3:\r\n          textR = \"駁回\";\r\n          break;\r\n        default:\r\n          break;\r\n      }\r\n    }\r\n    return textR;\r\n  }\r\n\r\n  checkAllHouseIsValid(household: string | null | undefined, floor: number | null | undefined) {\r\n    let count = 0;\r\n    let total = 0;\r\n    if (household != null) {\r\n      for (let i = 1; i < this.houseList2D.length; i++) {\r\n        this.houseList2D[i].map(val => {\r\n          if (val.CHouseHold && val.CHouseHold == household) {\r\n            total++;\r\n            if (val.CIsEnable == true) {\r\n              count++;\r\n            }\r\n          }\r\n        });\r\n      }\r\n    }\r\n    if (floor != null) {\r\n      for (let i = 0; i < this.houseList2D.length; i++) {\r\n        this.houseList2D[i].map(val => {\r\n          if (val.CFloor == floor) {\r\n            total++;\r\n            if (val.CIsEnable == true) {\r\n              count++;\r\n            }\r\n          }\r\n        });\r\n      }\r\n    }\r\n\r\n    if (count == total) {\r\n      return false;\r\n    }\r\n    return true;\r\n  }\r\n\r\n  // 新增：載入建築物戶別資料 (使用 GetDropDown API)\r\n  private loadBuildingDataFromAPI(): void {\r\n    if (!this.searchQuery.selectedBuildCase?.value) return;\r\n\r\n    this._houseCustomService.getDropDown(this.searchQuery.selectedBuildCase.value).subscribe({\r\n      next: (response) => {\r\n        if (response && response.Entries) {\r\n          this.buildingData = this.convertApiResponseToBuildingData(response.Entries);\r\n        } else {\r\n          // 如果 API 失敗，使用現有的 houseList2D 轉換\r\n          this.buildingData = this.convertHouseList2DToBuildingData();\r\n        }\r\n      },\r\n      error: (error) => {\r\n        console.error('載入建築物戶別資料失敗:', error);\r\n        // 發生錯誤時，使用現有的 houseList2D 轉換\r\n        this.buildingData = this.convertHouseList2DToBuildingData();\r\n      }\r\n    });\r\n  }\r\n\r\n  // 新增：將 API 回應轉換為建築物資料格式\r\n  private convertApiResponseToBuildingData(entries: any): BuildingData {\r\n    const buildingData: BuildingData = {};\r\n\r\n    Object.entries(entries).forEach(([building, houses]: [string, any]) => {\r\n      buildingData[building] = houses.map((house: any) => ({\r\n        houseName: house.CHouseHold || '',\r\n        building: building,\r\n        floor: house.CFloor?.toString() || '',\r\n        houseId: house.CHouseID,\r\n        houseType: house.CHouseType || undefined,\r\n        isSelected: false,\r\n        isDisabled: false\r\n      }));\r\n    });\r\n\r\n    return buildingData;\r\n  }\r\n\r\n  // 新增：將現有的 houseList2D 轉換為新戶別選擇器的格式\r\n  convertHouseList2DToBuildingData(): BuildingData {\r\n    const buildingData: BuildingData = {};\r\n\r\n    if (!this.houseList2D || this.houseList2D.length === 0) {\r\n      return buildingData;\r\n    }\r\n\r\n    this.houseList2D.forEach(row => {\r\n      row.forEach(house => {\r\n        if (house.CHouseHold && house.CFloor) {\r\n          const building = 'Building'; // 預設建築物名稱，可以根據實際需求調整\r\n\r\n          if (!buildingData[building]) {\r\n            buildingData[building] = [];\r\n          }\r\n\r\n          buildingData[building].push({\r\n            houseName: house.CHouseHold,\r\n            building: building,\r\n            floor: house.CFloor?.toString() || '',\r\n            houseId: house.CHouseID,\r\n            houseType: house.CHouseType || undefined,\r\n            isSelected: house.CIsSelect || false,\r\n            isDisabled: !house.CIsEnable\r\n          });\r\n        }\r\n      });\r\n    });\r\n\r\n    return buildingData;\r\n  }\r\n\r\n  // 新增：處理戶別選擇變更\r\n  onHouseholdSelectionChange(selectedItems: HouseholdItem[]) {\r\n    // 更新 selectedHouseholds (使用 houseId)\r\n    this.selectedHouseholds = selectedItems.map(item => item.houseId).filter(id => id !== undefined) as number[];\r\n\r\n    // 更新原有的 houseList2D 選擇狀態以保持相容性\r\n    this.updateHouseListSelectionByIds(this.selectedHouseholds);\r\n  }\r\n\r\n  // 新增：處理戶別ID變更事件\r\n  onHouseholdIdChange(selectedIds: number[]) {\r\n    this.selectedHouseholds = selectedIds;\r\n\r\n    // 更新原有的 houseList2D 選擇狀態以保持相容性\r\n    this.updateHouseListSelectionByIds(this.selectedHouseholds);\r\n  }\r\n\r\n  // 新增：更新原有資料結構的選擇狀態 (使用戶別ID)\r\n  private updateHouseListSelectionByIds(selectedIds: number[]) {\r\n    if (!this.houseList2D) return;\r\n\r\n    this.houseList2D.forEach(row => {\r\n      row.forEach(house => {\r\n        house.CIsSelect = selectedIds.includes(house.CHouseID || 0);\r\n      });\r\n    });\r\n  }\r\n\r\n  // 新增：從新戶別選擇器的選擇結果轉換為原有格式\r\n  convertSelectedHouseholdsToHouseReview(): HouseReview[] {\r\n    const result: HouseReview[] = [];\r\n\r\n    // 從 houseList2D 中找到對應的戶別資訊\r\n    if (this.houseList2D && this.selectedHouseholds.length > 0) {\r\n      this.houseList2D.forEach(row => {\r\n        row.forEach(house => {\r\n          if (house.CHouseID && this.selectedHouseholds.includes(house.CHouseID)) {\r\n            result.push({\r\n              CHouseID: house.CHouseID,\r\n              CIsSelect: true,\r\n              CFloor: house.CFloor || undefined,\r\n              CHouseHold: house.CHouseHold,\r\n            });\r\n          }\r\n        });\r\n      });\r\n    }\r\n\r\n    return result;\r\n  }\r\n\r\n}\r\n", "<nb-card accent=\"success\">\r\n  <nb-card-header>\r\n    <ngx-breadcrumb></ngx-breadcrumb>\r\n  </nb-card-header>\r\n  <nb-card-body>\r\n    <h1 class=\"font-bold text-[#818181]\">可上傳要提供客戶標準圖說，文件內型分為標準圖及設備，並設定該檔案適用的戶別。</h1>\r\n    <div class=\"d-flex flex-wrap\">\r\n      <div class=\"col-md-6\">\r\n        <div class=\"form-group d-flex align-items-center w-full\">\r\n          <label for=\"buildingName\" class=\"label col-3\">建案</label>\r\n          <nb-select placeholder=\"建案\" [(ngModel)]=\"searchQuery.selectedBuildCase\" class=\"col-9\"\r\n            (selectedChange)=\"onSelectionChangeBuildCase()\">\r\n            <nb-option *ngFor=\"let case of userBuildCaseOptions\" [value]=\"case\">\r\n              {{ case.label }}\r\n            </nb-option>\r\n          </nb-select>\r\n        </div>\r\n      </div>\r\n      <div class=\"col-md-6\">\r\n        <div class=\"form-group d-flex align-items-center w-full\">\r\n          <label for=\"cReviewType\" class=\"label col-3\">類型</label>\r\n          <nb-select placeholder=\"類型\" [(ngModel)]=\"searchQuery.selectedReviewType\" class=\"col-9\">\r\n            <nb-option *ngFor=\"let case of reviewTypeOptionsQuery\" [value]=\"case\">\r\n              {{ case.label }}\r\n            </nb-option>\r\n          </nb-select>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"col-md-6\">\r\n        <div class=\"form-group d-flex align-items-center w-full\">\r\n          <label for=\"cReviewName\" class=\"label col-3\"> 名稱 </label>\r\n          <div class=\"col-9\">\r\n            <input type=\"text\" id=\"cReviewName\" nbInput class=\"w-full !max-w-[290px]\"\r\n              [(ngModel)]=\"searchQuery.CReviewName\">\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <div class=\"col-md-6\">\r\n        <div class=\"form-group d-flex align-items-center w-full\">\r\n          <label for=\"cStatus\" class=\"label col-3\">\r\n            狀態\r\n          </label>\r\n          <nb-select placeholder=\"狀態\" [(ngModel)]=\"searchQuery.seletedStatus\" class=\"col-9\">\r\n            <nb-option *ngFor=\"let case of statusOptionsQuery\" [value]=\"case\">\r\n              {{ case.label }}\r\n            </nb-option>\r\n          </nb-select>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"col-md-6\">\r\n        <div class=\"form-group d-flex align-items-center w-full\">\r\n          <label for=\"CExamineStatus\" class=\"label col-3\">\r\n            簽回狀態\r\n          </label>\r\n          <nb-select placeholder=\"簽回狀態\" [(ngModel)]=\"searchQuery.selectedExamineStatus\" class=\"col-9\">\r\n            <nb-option *ngFor=\"let case of examineStatusOptionsQuery\" [value]=\"case\">\r\n              {{ case.label }}\r\n            </nb-option>\r\n          </nb-select>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"col-md-6\">\r\n        <div class=\"form-group d-flex align-items-end justify-end w-full\">\r\n          <button class=\"btn btn-secondary btn-sm\" *ngIf=\"isRead\" (click)=\"onSearch()\">\r\n            查詢 <i class=\"fas fa-search\"></i>\r\n          </button>\r\n          <button class=\"btn btn-info mx-1 btn-sm\" *ngIf=\"isCreate\" (click)=\"openModel(dialog)\">\r\n            新增 <i class=\"fas fa-plus\"></i>\r\n          </button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <div class=\"table-responsive mt-4\">\r\n      <table class=\"table table-striped border \" style=\"background-color:#f3f3f3;\">\r\n        <thead>\r\n          <tr style=\"background-color: #27ae60; color: white;\">\r\n            <th scope=\"col\" class=\"col-1 \">類型</th>\r\n            <th scope=\"col\" class=\"col-1\">名稱</th>\r\n            <th scope=\"col\" class=\"col-2\">適用戶型</th>\r\n            <th scope=\"col\" class=\"col-1\">狀態</th>\r\n            <th scope=\"col\" class=\"col-1\">審核狀態</th>\r\n            <th scope=\"col\" class=\"col-2 text-center\">審核日期</th>\r\n            <th scope=\"col\" class=\"col-1 text-center\">動作</th>\r\n          </tr>\r\n        </thead>\r\n        <tbody>\r\n          <tr *ngFor=\"let item of reviewList ; let i = index\">\r\n            <td>\r\n              {{item.CReviewType | getLabelInOptions : reviewTypeOptions}}\r\n            </td>\r\n            <td>\r\n              {{ item.CReviewName}}\r\n            </td>\r\n            <td>\r\n              {{ item.CHouse }}\r\n            </td>\r\n            <td>\r\n              {{item.CStatus | getLabelInOptions : statusOptions}}\r\n            </td>\r\n            <td>\r\n              {{item.CExamineStatus | getLabelInOptions : examineStatusOptionsQuery}}\r\n            </td>\r\n            <td class=\"text-center\">\r\n              {{ item.CActionDate ? (item.CActionDate | dateFormat) : ''}}\r\n            </td>\r\n            <td class=\"text-center w-32 px-0\">\r\n              <button *ngIf=\"isUpdate\" (click)=\"openModel(dialog, item)\"\r\n                class=\"btn btn-outline-success btn-sm text-left m-[2px]\">\r\n                編輯\r\n              </button>\r\n            </td>\r\n          </tr>\r\n        </tbody>\r\n      </table>\r\n    </div>\r\n  </nb-card-body>\r\n  <nb-card-footer class=\"d-flex justify-content-center\">\r\n    <ngb-pagination [(page)]=\"pageIndex\" [pageSize]=\"pageSize\" [collectionSize]=\"totalRecords\"\r\n      (pageChange)=\"pageChanged($event)\" aria-label=\"Pagination\">\r\n    </ngb-pagination>\r\n  </nb-card-footer>\r\n</nb-card>\r\n\r\n\r\n<ng-template #dialog let-dialog let-ref=\"dialogRef\">\r\n  <nb-card style=\"width:800px; max-height: 95vh\">\r\n    <nb-card-header> {{isNew? '新增審閱文件': '編輯審閱文件'}} </nb-card-header>\r\n    <nb-card-body class=\"px-4\">\r\n      <div class=\"form-group d-flex align-items-center w-full\">\r\n        <label for=\"ReviewType\" class=\"required-field label col-3\">類型\r\n        </label>\r\n        <nb-select [disabled]=\"latestAction===1\" placeholder=\"類型\" [(ngModel)]=\"selectedReview.selectedReviewType\"\r\n          class=\"col-9 px-0\">\r\n          <nb-option *ngFor=\"let case of reviewTypeOptions\" [value]=\"case\">\r\n            {{ case.label }}\r\n          </nb-option>\r\n        </nb-select>\r\n      </div>\r\n      <div class=\"form-group d-flex align-items-center\">\r\n        <!-- 使用共用文件上傳元件 -->\r\n        <app-file-upload [config]=\"fileUploadConfigWithState\" [currentFileName]=\"fileName\"\r\n          [currentFileUrl]=\"selectedReview.CFileUrl || null\" (fileSelected)=\"onFileUploaded($event)\"\r\n          (fileCleared)=\"onFileCleared()\" (nameAutoFilled)=\"onNameAutoFilled($event)\">\r\n        </app-file-upload>\r\n      </div>\r\n      <div class=\"form-group d-flex align-items-center\">\r\n        <div class=\"form-group d-flex align-items-center w-full\">\r\n          <label for=\"CExamineStatus\" class=\"label col-3 required-field\">\r\n            狀態\r\n          </label>\r\n          <nb-select [disabled]=\"latestAction===1\" placeholder=\"狀態\" [(ngModel)]=\"selectedReview.seletedStatus\"\r\n            class=\"col-9 px-0\">\r\n            <nb-option *ngFor=\"let case of statusOptions\" [value]=\"case\">\r\n              {{ case.label }}\r\n            </nb-option>\r\n          </nb-select>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"form-group d-flex align-items-center\">\r\n        <div class=\"form-group d-flex align-items-center w-full\">\r\n          <label for=\"cExamineNote\" class=\"label col-3 required-field\">\r\n            審核說明\r\n          </label>\r\n          <textarea nbInput [(ngModel)]=\"selectedReview.CExamineNote\" [rows]=\"4\" [disabled]=\"latestAction===1\"\r\n            class=\"resize-none w-full col-9 !max-w-[320px]\"></textarea>\r\n        </div>\r\n      </div>\r\n      <div class=\"form-group d-flex mb-0\">\r\n        <label for=\"houseList2D\" baseLabel class=\"required-field mr-3\" style=\"min-width:75px\">適用戶別</label>\r\n      </div>\r\n\r\n      <!-- 新的戶別選擇器 -->\r\n      <div class=\"mt-1 mb-3\">\r\n        <app-household-binding [(ngModel)]=\"selectedHouseholds\" [buildCaseId]=\"searchQuery.selectedBuildCase?.value\"\r\n          [buildingData]=\"buildingData\" [maxSelections]=\"50\" placeholder=\"請選擇適用戶別\" [disabled]=\"latestAction === 1\"\r\n          [allowSearch]=\"true\" [allowBatchSelect]=\"true\" [useHouseNameMode]=\"false\"\r\n          (houseIdChange)=\"onHouseholdIdChange($event)\" (selectionChange)=\"onHouseholdSelectionChange($event)\">\r\n        </app-household-binding>\r\n      </div>\r\n\r\n      <!-- 傳統表格式戶別選擇器 (作為備選) -->\r\n      <div class=\"table-responsive mt-1\" *ngIf=\"isHouseList\">\r\n        <table class=\"table table-bordered\" style=\"background-color:#f3f3f3;\">\r\n          <thead>\r\n            <tr *ngIf=\"houseList2D.length\">\r\n              <th></th>\r\n              <th *ngFor=\"let house of houseList2D[0]; let idx = index;\">\r\n                <nb-checkbox status=\"basic\" (checkedChange)=\"enableAllAtIndex($event, idx)\"\r\n                  [checked]=\"isCheckAllColumnChecked(idx)\"\r\n                  [disabled]=\"latestAction===1 || checkAllHouseIsValid(house.CHouseHold, null)\">\r\n                  <span class=\"font-medium\">全選</span>\r\n                </nb-checkbox>\r\n              </th>\r\n            </tr>\r\n          </thead>\r\n          <tbody>\r\n            <tr *ngFor=\"let row of houseList2D\">\r\n              <td>\r\n                <nb-checkbox status=\"basic\" [checked]=\"isCheckAllRowChecked(row)\"\r\n                  (checkedChange)=\"enableAllRow($event,row)\"\r\n                  [disabled]=\"latestAction===1 || checkAllHouseIsValid(null, row[0].CFloor)\">\r\n                  <span class=\"font-medium\">全選</span>\r\n                </nb-checkbox>\r\n              </td>\r\n              <td *ngFor=\"let house of row\">\r\n                <ng-container>\r\n                  <nb-checkbox status=\"basic\" [(checked)]=\"house.CIsSelect\"\r\n                    [disabled]=\"!house.CHouseHold || !house.CIsEnable || latestAction===1\">\r\n                    <span class=\"font-bold\" [ngClass]=\"{ '!text-red': house.CID }\">{{ house.CHouseHold || 'null' }} - {{\r\n                      house.CFloor }} </span>\r\n                  </nb-checkbox>\r\n                  <!-- <span class=\"font-bold\" *ngIf=\"!house.CHouseHold || !house.CIsEnable\">{{ house.CHouseHold || 'null' }} - {{\r\n                    house.CFloor }}</span> -->\r\n                </ng-container>\r\n              </td>\r\n            </tr>\r\n          </tbody>\r\n        </table>\r\n      </div>\r\n      <div class=\"d-flex justify-content-center\">\r\n        <button class=\"btn btn-danger btn-sm mr-4 min-w-[90px]\" (click)=\"onClose(ref)\">\r\n          取消\r\n        </button>\r\n        <button class=\"btn btn-success btn-sm min-w-[90px]\" (click)=\"onSaveReview(ref)\" [disabled]=\"latestAction===1\">\r\n          {{isNew? '新增並送出審核': '送出審核'}}\r\n        </button>\r\n      </div>\r\n      <div class=\"w-full\" *ngIf=\"!isNew\">\r\n        <div class=\"form-group d-flex align-items-center\">\r\n          <label for=\"status\" baseLabel class=\"mr-3\" style=\"min-width:75px\">審核歷程</label>\r\n        </div>\r\n        <table class=\"table table-bordered\" style=\"background-color:#f3f3f3;\">\r\n          <thead>\r\n            <tr>\r\n              <th>時間</th>\r\n              <th>使用者</th>\r\n              <th>動作</th>\r\n              <th>說明</th>\r\n            </tr>\r\n          </thead>\r\n          <tbody>\r\n            <tr *ngFor=\"let row of selectedReview.tblExamineLogs\">\r\n              <td>{{row.CCreateDt | dateFormatHour}}</td>\r\n              <td>{{row.CCreator}}</td>\r\n              <td>{{getActionName(row.CAction)}}</td>\r\n              <td>{{row.CExamineNote}}</td>\r\n            </tr>\r\n          </tbody>\r\n        </table>\r\n      </div>\r\n    </nb-card-body>\r\n  </nb-card>\r\n</ng-template>"], "mappings": "AACA,SAASA,YAAY,QAAQ,mCAAmC;AAChE,SAASA,YAAY,IAAIC,eAAe,QAAQ,8BAA8B;AAC9E,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,kBAAkB,QAAyB,gBAAgB;AAKpE,SAASC,kBAAkB,QAAQ,mCAAmC;AACtE,SAASC,aAAa,QAAQ,wCAAwC;AAGtE,SAASC,GAAG,QAAQ,MAAM;AAC1B,SAASC,mBAAmB,QAAQ,mBAAmB;AACvD,OAAO,KAAKC,MAAM,MAAM,QAAQ;AAGhC,SAASC,kBAAkB,QAAwB,sBAAsB;AAEzE,SAASC,mBAAmB,QAAQ,+CAA+C;AACnF,SAASC,WAAW,QAAQ,kCAAkC;;;;;;;;;;;;;;;;;;;;;;;;ICTlDC,EAAA,CAAAC,cAAA,oBAAoE;IAClED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAFyCH,EAAA,CAAAI,UAAA,UAAAC,OAAA,CAAc;IACjEL,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAAO,kBAAA,MAAAF,OAAA,CAAAG,KAAA,MACF;;;;;IAQAR,EAAA,CAAAC,cAAA,oBAAsE;IACpED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAF2CH,EAAA,CAAAI,UAAA,UAAAK,OAAA,CAAc;IACnET,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAAO,kBAAA,MAAAE,OAAA,CAAAD,KAAA,MACF;;;;;IAoBAR,EAAA,CAAAC,cAAA,oBAAkE;IAChED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAFuCH,EAAA,CAAAI,UAAA,UAAAM,OAAA,CAAc;IAC/DV,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAAO,kBAAA,MAAAG,OAAA,CAAAF,KAAA,MACF;;;;;IAWAR,EAAA,CAAAC,cAAA,oBAAyE;IACvED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAF8CH,EAAA,CAAAI,UAAA,UAAAO,OAAA,CAAc;IACtEX,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAAO,kBAAA,MAAAI,OAAA,CAAAH,KAAA,MACF;;;;;;IAOFR,EAAA,CAAAC,cAAA,iBAA6E;IAArBD,EAAA,CAAAY,UAAA,mBAAAC,6EAAA;MAAAb,EAAA,CAAAc,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAhB,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAASF,MAAA,CAAAG,QAAA,EAAU;IAAA,EAAC;IAC1EnB,EAAA,CAAAE,MAAA,qBAAG;IAAAF,EAAA,CAAAoB,SAAA,YAA6B;IAClCpB,EAAA,CAAAG,YAAA,EAAS;;;;;;IACTH,EAAA,CAAAC,cAAA,iBAAsF;IAA5BD,EAAA,CAAAY,UAAA,mBAAAS,6EAAA;MAAArB,EAAA,CAAAc,aAAA,CAAAQ,GAAA;MAAA,MAAAN,MAAA,GAAAhB,EAAA,CAAAiB,aAAA;MAAA,MAAAM,SAAA,GAAAvB,EAAA,CAAAwB,WAAA;MAAA,OAAAxB,EAAA,CAAAkB,WAAA,CAASF,MAAA,CAAAS,SAAA,CAAAF,SAAA,CAAiB;IAAA,EAAC;IACnFvB,EAAA,CAAAE,MAAA,qBAAG;IAAAF,EAAA,CAAAoB,SAAA,YAA2B;IAChCpB,EAAA,CAAAG,YAAA,EAAS;;;;;;IAsCLH,EAAA,CAAAC,cAAA,iBAC2D;IADlCD,EAAA,CAAAY,UAAA,mBAAAc,mFAAA;MAAA1B,EAAA,CAAAc,aAAA,CAAAa,IAAA;MAAA,MAAAC,QAAA,GAAA5B,EAAA,CAAAiB,aAAA,GAAAY,SAAA;MAAA,MAAAb,MAAA,GAAAhB,EAAA,CAAAiB,aAAA;MAAA,MAAAM,SAAA,GAAAvB,EAAA,CAAAwB,WAAA;MAAA,OAAAxB,EAAA,CAAAkB,WAAA,CAASF,MAAA,CAAAS,SAAA,CAAAF,SAAA,EAAAK,QAAA,CAAuB;IAAA,EAAC;IAExD5B,EAAA,CAAAE,MAAA,qBACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;IAtBXH,EADF,CAAAC,cAAA,SAAoD,SAC9C;IACFD,EAAA,CAAAE,MAAA,GACF;;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,SAAI;IACFD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,SAAI;IACFD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,SAAI;IACFD,EAAA,CAAAE,MAAA,GACF;;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,UAAI;IACFD,EAAA,CAAAE,MAAA,IACF;;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,cAAwB;IACtBD,EAAA,CAAAE,MAAA,IACF;;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,cAAkC;IAChCD,EAAA,CAAA8B,UAAA,KAAAC,0DAAA,qBAC2D;IAI/D/B,EADE,CAAAG,YAAA,EAAK,EACF;;;;;IAvBDH,EAAA,CAAAM,SAAA,GACF;IADEN,EAAA,CAAAO,kBAAA,MAAAP,EAAA,CAAAgC,WAAA,OAAAJ,QAAA,CAAAK,WAAA,EAAAjB,MAAA,CAAAkB,iBAAA,OACF;IAEElC,EAAA,CAAAM,SAAA,GACF;IADEN,EAAA,CAAAO,kBAAA,MAAAqB,QAAA,CAAAO,WAAA,MACF;IAEEnC,EAAA,CAAAM,SAAA,GACF;IADEN,EAAA,CAAAO,kBAAA,MAAAqB,QAAA,CAAAQ,MAAA,MACF;IAEEpC,EAAA,CAAAM,SAAA,GACF;IADEN,EAAA,CAAAO,kBAAA,MAAAP,EAAA,CAAAgC,WAAA,SAAAJ,QAAA,CAAAS,OAAA,EAAArB,MAAA,CAAAsB,aAAA,OACF;IAEEtC,EAAA,CAAAM,SAAA,GACF;IADEN,EAAA,CAAAO,kBAAA,MAAAP,EAAA,CAAAgC,WAAA,SAAAJ,QAAA,CAAAW,cAAA,EAAAvB,MAAA,CAAAwB,yBAAA,OACF;IAEExC,EAAA,CAAAM,SAAA,GACF;IADEN,EAAA,CAAAO,kBAAA,MAAAqB,QAAA,CAAAa,WAAA,GAAAzC,EAAA,CAAA0C,WAAA,SAAAd,QAAA,CAAAa,WAAA,YACF;IAEWzC,EAAA,CAAAM,SAAA,GAAc;IAAdN,EAAA,CAAAI,UAAA,SAAAY,MAAA,CAAA2B,QAAA,CAAc;;;;;IA2B3B3C,EAAA,CAAAC,cAAA,oBAAiE;IAC/DD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAFsCH,EAAA,CAAAI,UAAA,UAAAwC,QAAA,CAAc;IAC9D5C,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAAO,kBAAA,MAAAqC,QAAA,CAAApC,KAAA,MACF;;;;;IAiBER,EAAA,CAAAC,cAAA,oBAA6D;IAC3DD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAFkCH,EAAA,CAAAI,UAAA,UAAAyC,QAAA,CAAc;IAC1D7C,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAAO,kBAAA,MAAAsC,QAAA,CAAArC,KAAA,MACF;;;;;;IAkCIR,EADF,CAAAC,cAAA,SAA2D,sBAGuB;IAFpDD,EAAA,CAAAY,UAAA,2BAAAkC,gHAAAC,MAAA;MAAA,MAAAC,OAAA,GAAAhD,EAAA,CAAAc,aAAA,CAAAmC,IAAA,EAAAC,KAAA;MAAA,MAAAlC,MAAA,GAAAhB,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAAiBF,MAAA,CAAAmC,gBAAA,CAAAJ,MAAA,EAAAC,OAAA,CAA6B;IAAA,EAAC;IAGzEhD,EAAA,CAAAC,cAAA,eAA0B;IAAAD,EAAA,CAAAE,MAAA,mBAAE;IAEhCF,EAFgC,CAAAG,YAAA,EAAO,EACvB,EACX;;;;;;IAJDH,EAAA,CAAAM,SAAA,EAAwC;IACxCN,EADA,CAAAI,UAAA,YAAAY,MAAA,CAAAoC,uBAAA,CAAAJ,OAAA,EAAwC,aAAAhC,MAAA,CAAAqC,YAAA,UAAArC,MAAA,CAAAsC,oBAAA,CAAAC,SAAA,CAAAC,UAAA,QACqC;;;;;IALnFxD,EAAA,CAAAC,cAAA,SAA+B;IAC7BD,EAAA,CAAAoB,SAAA,SAAS;IACTpB,EAAA,CAAA8B,UAAA,IAAA2B,0EAAA,iBAA2D;IAO7DzD,EAAA,CAAAG,YAAA,EAAK;;;;IAPmBH,EAAA,CAAAM,SAAA,GAAmB;IAAnBN,EAAA,CAAAI,UAAA,YAAAY,MAAA,CAAA0C,WAAA,IAAmB;;;;;;IAkBzC1D,EAAA,CAAAC,cAAA,SAA8B;IAC5BD,EAAA,CAAA2D,uBAAA,GAAc;IACZ3D,EAAA,CAAAC,cAAA,sBACyE;IAD7CD,EAAA,CAAA4D,gBAAA,2BAAAC,gHAAAd,MAAA;MAAA,MAAAe,SAAA,GAAA9D,EAAA,CAAAc,aAAA,CAAAiD,IAAA,EAAAlC,SAAA;MAAA7B,EAAA,CAAAgE,kBAAA,CAAAF,SAAA,CAAAG,SAAA,EAAAlB,MAAA,MAAAe,SAAA,CAAAG,SAAA,GAAAlB,MAAA;MAAA,OAAA/C,EAAA,CAAAkB,WAAA,CAAA6B,MAAA;IAAA,EAA6B;IAEvD/C,EAAA,CAAAC,cAAA,eAA+D;IAAAD,EAAA,CAAAE,MAAA,GAC7C;IACpBF,EADoB,CAAAG,YAAA,EAAO,EACb;;IAIlBH,EAAA,CAAAG,YAAA,EAAK;;;;;IAR2BH,EAAA,CAAAM,SAAA,GAA6B;IAA7BN,EAAA,CAAAkE,gBAAA,YAAAJ,SAAA,CAAAG,SAAA,CAA6B;IACvDjE,EAAA,CAAAI,UAAA,cAAA0D,SAAA,CAAAN,UAAA,KAAAM,SAAA,CAAAK,SAAA,IAAAnD,MAAA,CAAAqC,YAAA,OAAsE;IAC9CrD,EAAA,CAAAM,SAAA,EAAsC;IAAtCN,EAAA,CAAAI,UAAA,YAAAJ,EAAA,CAAAoE,eAAA,IAAAC,GAAA,EAAAP,SAAA,CAAAQ,GAAA,EAAsC;IAACtE,EAAA,CAAAM,SAAA,EAC7C;IAD6CN,EAAA,CAAAuE,kBAAA,KAAAT,SAAA,CAAAN,UAAA,mBAAAM,SAAA,CAAAU,MAAA,MAC7C;;;;;;IAXtBxE,EAFJ,CAAAC,cAAA,SAAoC,SAC9B,sBAG2E;IAD3ED,EAAA,CAAAY,UAAA,2BAAA6D,2GAAA1B,MAAA;MAAA,MAAA2B,OAAA,GAAA1E,EAAA,CAAAc,aAAA,CAAA6D,IAAA,EAAA9C,SAAA;MAAA,MAAAb,MAAA,GAAAhB,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAAiBF,MAAA,CAAA4D,YAAA,CAAA7B,MAAA,EAAA2B,OAAA,CAAwB;IAAA,EAAC;IAE1C1E,EAAA,CAAAC,cAAA,eAA0B;IAAAD,EAAA,CAAAE,MAAA,mBAAE;IAEhCF,EAFgC,CAAAG,YAAA,EAAO,EACvB,EACX;IACLH,EAAA,CAAA8B,UAAA,IAAA+C,0EAAA,iBAA8B;IAWhC7E,EAAA,CAAAG,YAAA,EAAK;;;;;IAjB2BH,EAAA,CAAAM,SAAA,GAAqC;IAE/DN,EAF0B,CAAAI,UAAA,YAAAY,MAAA,CAAA8D,oBAAA,CAAAJ,OAAA,EAAqC,aAAA1D,MAAA,CAAAqC,YAAA,UAAArC,MAAA,CAAAsC,oBAAA,OAAAoB,OAAA,IAAAF,MAAA,EAEW;IAIxDxE,EAAA,CAAAM,SAAA,GAAM;IAANN,EAAA,CAAAI,UAAA,YAAAsE,OAAA,CAAM;;;;;IArBhC1E,EAFJ,CAAAC,cAAA,cAAuD,gBACiB,YAC7D;IACLD,EAAA,CAAA8B,UAAA,IAAAiD,qEAAA,iBAA+B;IAUjC/E,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,YAAO;IACLD,EAAA,CAAA8B,UAAA,IAAAkD,qEAAA,iBAAoC;IAsB1ChF,EAFI,CAAAG,YAAA,EAAQ,EACF,EACJ;;;;IAlCKH,EAAA,CAAAM,SAAA,GAAwB;IAAxBN,EAAA,CAAAI,UAAA,SAAAY,MAAA,CAAA0C,WAAA,CAAAuB,MAAA,CAAwB;IAYTjF,EAAA,CAAAM,SAAA,GAAc;IAAdN,EAAA,CAAAI,UAAA,YAAAY,MAAA,CAAA0C,WAAA,CAAc;;;;;IA8ChC1D,EADF,CAAAC,cAAA,SAAsD,SAChD;IAAAD,EAAA,CAAAE,MAAA,GAAkC;;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC3CH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,GAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACzBH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,GAA8B;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACvCH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,GAAoB;IAC1BF,EAD0B,CAAAG,YAAA,EAAK,EAC1B;;;;;IAJCH,EAAA,CAAAM,SAAA,GAAkC;IAAlCN,EAAA,CAAAkF,iBAAA,CAAAlF,EAAA,CAAA0C,WAAA,OAAAyC,OAAA,CAAAC,SAAA,EAAkC;IAClCpF,EAAA,CAAAM,SAAA,GAAgB;IAAhBN,EAAA,CAAAkF,iBAAA,CAAAC,OAAA,CAAAE,QAAA,CAAgB;IAChBrF,EAAA,CAAAM,SAAA,GAA8B;IAA9BN,EAAA,CAAAkF,iBAAA,CAAAlE,MAAA,CAAAsE,aAAA,CAAAH,OAAA,CAAAI,OAAA,EAA8B;IAC9BvF,EAAA,CAAAM,SAAA,GAAoB;IAApBN,EAAA,CAAAkF,iBAAA,CAAAC,OAAA,CAAAK,YAAA,CAAoB;;;;;IAhB5BxF,EAFJ,CAAAC,cAAA,cAAmC,cACiB,gBACkB;IAAAD,EAAA,CAAAE,MAAA,+BAAI;IACxEF,EADwE,CAAAG,YAAA,EAAQ,EAC1E;IAIAH,EAHN,CAAAC,cAAA,gBAAsE,YAC7D,SACD,SACE;IAAAD,EAAA,CAAAE,MAAA,mBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACXH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,0BAAG;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACZH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAE,MAAA,oBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACXH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAE,MAAA,oBAAE;IAEVF,EAFU,CAAAG,YAAA,EAAK,EACR,EACC;IACRH,EAAA,CAAAC,cAAA,aAAO;IACLD,EAAA,CAAA8B,UAAA,KAAA2D,sEAAA,kBAAsD;IAQ5DzF,EAFI,CAAAG,YAAA,EAAQ,EACF,EACJ;;;;IARoBH,EAAA,CAAAM,SAAA,IAAgC;IAAhCN,EAAA,CAAAI,UAAA,YAAAY,MAAA,CAAA0E,cAAA,CAAAC,cAAA,CAAgC;;;;;;IApH5D3F,EADF,CAAAC,cAAA,kBAA+C,qBAC7B;IAACD,EAAA,CAAAE,MAAA,GAA8B;IAAAF,EAAA,CAAAG,YAAA,EAAiB;IAG5DH,EAFJ,CAAAC,cAAA,uBAA2B,aACgC,gBACI;IAAAD,EAAA,CAAAE,MAAA,oBAC3D;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,oBACqB;IADqCD,EAAA,CAAA4D,gBAAA,2BAAAgC,6FAAA7C,MAAA;MAAA/C,EAAA,CAAAc,aAAA,CAAA+E,IAAA;MAAA,MAAA7E,MAAA,GAAAhB,EAAA,CAAAiB,aAAA;MAAAjB,EAAA,CAAAgE,kBAAA,CAAAhD,MAAA,CAAA0E,cAAA,CAAAI,kBAAA,EAAA/C,MAAA,MAAA/B,MAAA,CAAA0E,cAAA,CAAAI,kBAAA,GAAA/C,MAAA;MAAA,OAAA/C,EAAA,CAAAkB,WAAA,CAAA6B,MAAA;IAAA,EAA+C;IAEvG/C,EAAA,CAAA8B,UAAA,IAAAiE,qEAAA,uBAAiE;IAIrE/F,EADE,CAAAG,YAAA,EAAY,EACR;IAGJH,EAFF,CAAAC,cAAA,cAAkD,2BAI8B;IAA5CD,EADmB,CAAAY,UAAA,0BAAAoF,mGAAAjD,MAAA;MAAA/C,EAAA,CAAAc,aAAA,CAAA+E,IAAA;MAAA,MAAA7E,MAAA,GAAAhB,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAAgBF,MAAA,CAAAiF,cAAA,CAAAlD,MAAA,CAAsB;IAAA,EAAC,yBAAAmD,kGAAA;MAAAlG,EAAA,CAAAc,aAAA,CAAA+E,IAAA;MAAA,MAAA7E,MAAA,GAAAhB,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAC3EF,MAAA,CAAAmF,aAAA,EAAe;IAAA,EAAC,4BAAAC,qGAAArD,MAAA;MAAA/C,EAAA,CAAAc,aAAA,CAAA+E,IAAA;MAAA,MAAA7E,MAAA,GAAAhB,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAAmBF,MAAA,CAAAqF,gBAAA,CAAAtD,MAAA,CAAwB;IAAA,EAAC;IAE/E/C,EADE,CAAAG,YAAA,EAAkB,EACd;IAGFH,EAFJ,CAAAC,cAAA,eAAkD,cACS,iBACQ;IAC7DD,EAAA,CAAAE,MAAA,sBACF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,qBACqB;IADqCD,EAAA,CAAA4D,gBAAA,2BAAA0C,8FAAAvD,MAAA;MAAA/C,EAAA,CAAAc,aAAA,CAAA+E,IAAA;MAAA,MAAA7E,MAAA,GAAAhB,EAAA,CAAAiB,aAAA;MAAAjB,EAAA,CAAAgE,kBAAA,CAAAhD,MAAA,CAAA0E,cAAA,CAAAa,aAAA,EAAAxD,MAAA,MAAA/B,MAAA,CAAA0E,cAAA,CAAAa,aAAA,GAAAxD,MAAA;MAAA,OAAA/C,EAAA,CAAAkB,WAAA,CAAA6B,MAAA;IAAA,EAA0C;IAElG/C,EAAA,CAAA8B,UAAA,KAAA0E,sEAAA,uBAA6D;IAKnExG,EAFI,CAAAG,YAAA,EAAY,EACR,EACF;IAIFH,EAFJ,CAAAC,cAAA,eAAkD,cACS,iBACM;IAC3DD,EAAA,CAAAE,MAAA,kCACF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,oBACkD;IADhCD,EAAA,CAAA4D,gBAAA,2BAAA6C,6FAAA1D,MAAA;MAAA/C,EAAA,CAAAc,aAAA,CAAA+E,IAAA;MAAA,MAAA7E,MAAA,GAAAhB,EAAA,CAAAiB,aAAA;MAAAjB,EAAA,CAAAgE,kBAAA,CAAAhD,MAAA,CAAA0E,cAAA,CAAAF,YAAA,EAAAzC,MAAA,MAAA/B,MAAA,CAAA0E,cAAA,CAAAF,YAAA,GAAAzC,MAAA;MAAA,OAAA/C,EAAA,CAAAkB,WAAA,CAAA6B,MAAA;IAAA,EAAyC;IAG/D/C,EAFsD,CAAAG,YAAA,EAAW,EACzD,EACF;IAEJH,EADF,CAAAC,cAAA,eAAoC,iBACoD;IAAAD,EAAA,CAAAE,MAAA,gCAAI;IAC5FF,EAD4F,CAAAG,YAAA,EAAQ,EAC9F;IAIJH,EADF,CAAAC,cAAA,eAAuB,iCAIkF;IAHhFD,EAAA,CAAA4D,gBAAA,2BAAA8C,0GAAA3D,MAAA;MAAA/C,EAAA,CAAAc,aAAA,CAAA+E,IAAA;MAAA,MAAA7E,MAAA,GAAAhB,EAAA,CAAAiB,aAAA;MAAAjB,EAAA,CAAAgE,kBAAA,CAAAhD,MAAA,CAAA2F,kBAAA,EAAA5D,MAAA,MAAA/B,MAAA,CAAA2F,kBAAA,GAAA5D,MAAA;MAAA,OAAA/C,EAAA,CAAAkB,WAAA,CAAA6B,MAAA;IAAA,EAAgC;IAGP/C,EAA9C,CAAAY,UAAA,2BAAAgG,0GAAA7D,MAAA;MAAA/C,EAAA,CAAAc,aAAA,CAAA+E,IAAA;MAAA,MAAA7E,MAAA,GAAAhB,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAAiBF,MAAA,CAAA6F,mBAAA,CAAA9D,MAAA,CAA2B;IAAA,EAAC,6BAAA+D,4GAAA/D,MAAA;MAAA/C,EAAA,CAAAc,aAAA,CAAA+E,IAAA;MAAA,MAAA7E,MAAA,GAAAhB,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAAoBF,MAAA,CAAA+F,0BAAA,CAAAhE,MAAA,CAAkC;IAAA,EAAC;IAExG/C,EADE,CAAAG,YAAA,EAAwB,EACpB;IAGNH,EAAA,CAAA8B,UAAA,KAAAkF,gEAAA,kBAAuD;IAuCrDhH,EADF,CAAAC,cAAA,eAA2C,kBACsC;IAAvBD,EAAA,CAAAY,UAAA,mBAAAqG,mFAAA;MAAA,MAAAC,OAAA,GAAAlH,EAAA,CAAAc,aAAA,CAAA+E,IAAA,EAAAsB,SAAA;MAAA,MAAAnG,MAAA,GAAAhB,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAASF,MAAA,CAAAoG,OAAA,CAAAF,OAAA,CAAY;IAAA,EAAC;IAC5ElH,EAAA,CAAAE,MAAA,sBACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,kBAA8G;IAA1DD,EAAA,CAAAY,UAAA,mBAAAyG,mFAAA;MAAA,MAAAH,OAAA,GAAAlH,EAAA,CAAAc,aAAA,CAAA+E,IAAA,EAAAsB,SAAA;MAAA,MAAAnG,MAAA,GAAAhB,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAASF,MAAA,CAAAsG,YAAA,CAAAJ,OAAA,CAAiB;IAAA,EAAC;IAC7ElH,EAAA,CAAAE,MAAA,IACF;IACFF,EADE,CAAAG,YAAA,EAAS,EACL;IACNH,EAAA,CAAA8B,UAAA,KAAAyF,gEAAA,mBAAmC;IAwBvCvH,EADE,CAAAG,YAAA,EAAe,EACP;;;;IA9HSH,EAAA,CAAAM,SAAA,GAA8B;IAA9BN,EAAA,CAAAO,kBAAA,MAAAS,MAAA,CAAAwG,KAAA,wFAA8B;IAKhCxH,EAAA,CAAAM,SAAA,GAA6B;IAA7BN,EAAA,CAAAI,UAAA,aAAAY,MAAA,CAAAqC,YAAA,OAA6B;IAAkBrD,EAAA,CAAAkE,gBAAA,YAAAlD,MAAA,CAAA0E,cAAA,CAAAI,kBAAA,CAA+C;IAE3E9F,EAAA,CAAAM,SAAA,EAAoB;IAApBN,EAAA,CAAAI,UAAA,YAAAY,MAAA,CAAAkB,iBAAA,CAAoB;IAOjClC,EAAA,CAAAM,SAAA,GAAoC;IACnDN,EADe,CAAAI,UAAA,WAAAY,MAAA,CAAAyG,yBAAA,CAAoC,oBAAAzG,MAAA,CAAA0G,QAAA,CAA6B,mBAAA1G,MAAA,CAAA0E,cAAA,CAAAiC,QAAA,SAC9B;IASvC3H,EAAA,CAAAM,SAAA,GAA6B;IAA7BN,EAAA,CAAAI,UAAA,aAAAY,MAAA,CAAAqC,YAAA,OAA6B;IAAkBrD,EAAA,CAAAkE,gBAAA,YAAAlD,MAAA,CAAA0E,cAAA,CAAAa,aAAA,CAA0C;IAEtEvG,EAAA,CAAAM,SAAA,EAAgB;IAAhBN,EAAA,CAAAI,UAAA,YAAAY,MAAA,CAAAsB,aAAA,CAAgB;IAY5BtC,EAAA,CAAAM,SAAA,GAAyC;IAAzCN,EAAA,CAAAkE,gBAAA,YAAAlD,MAAA,CAAA0E,cAAA,CAAAF,YAAA,CAAyC;IAAYxF,EAAX,CAAAI,UAAA,WAAU,aAAAY,MAAA,CAAAqC,YAAA,OAA8B;IAU/ErD,EAAA,CAAAM,SAAA,GAAgC;IAAhCN,EAAA,CAAAkE,gBAAA,YAAAlD,MAAA,CAAA2F,kBAAA,CAAgC;IAEN3G,EAFO,CAAAI,UAAA,gBAAAY,MAAA,CAAA4G,WAAA,CAAAC,iBAAA,kBAAA7G,MAAA,CAAA4G,WAAA,CAAAC,iBAAA,CAAAC,KAAA,CAAoD,iBAAA9G,MAAA,CAAA+G,YAAA,CAC7E,qBAAqB,aAAA/G,MAAA,CAAAqC,YAAA,OAAsD,qBACpF,0BAA0B,2BAA2B;IAMzCrD,EAAA,CAAAM,SAAA,EAAiB;IAAjBN,EAAA,CAAAI,UAAA,SAAAY,MAAA,CAAAgH,WAAA,CAAiB;IA0C6BhI,EAAA,CAAAM,SAAA,GAA6B;IAA7BN,EAAA,CAAAI,UAAA,aAAAY,MAAA,CAAAqC,YAAA,OAA6B;IAC3GrD,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAAO,kBAAA,MAAAS,MAAA,CAAAwG,KAAA,kFACF;IAEmBxH,EAAA,CAAAM,SAAA,EAAY;IAAZN,EAAA,CAAAI,UAAA,UAAAY,MAAA,CAAAwG,KAAA,CAAY;;;AD5IvC,OAAM,MAAOS,iCAAkC,SAAQxI,aAAa;EAClEyI,YACUC,MAAmB,EACnBC,aAA8B,EAC9BC,OAAuB,EACvBC,KAAuB,EACvBC,aAA2B,EAC3BC,iBAAmC,EACnCC,cAA6B,EAC7BC,aAAkC,EAClCC,cAA8B,EAC9BC,mBAAuC;IAC7C,KAAK,CAACT,MAAM,CAAC;IAVP,KAAAA,MAAM,GAANA,MAAM;IACN,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,mBAAmB,GAAnBA,mBAAmB;IAGpB,KAAAC,SAAS,GAAG,CAAC;IACb,KAAAC,QAAQ,GAAG,EAAE;IACb,KAAAC,SAAS,GAAG,CAAC;IACb,KAAAC,YAAY,GAAG,CAAC;IAEzB,KAAA9G,iBAAiB,GAAG,CAAC;MACnB4F,KAAK,EAAE,CAAC;MACRtH,KAAK,EAAE,KAAK,CAAE;KACf,EAAE;MACDsH,KAAK,EAAE,CAAC;MACRtH,KAAK,EAAE,KAAK,CAAE;KACf,CACA;IAED,KAAAyI,sBAAsB,GAAG,CACvB;MACEnB,KAAK,EAAE,CAAC,CAAC;MACTtH,KAAK,EAAE;KACR,EACD;MACEsH,KAAK,EAAE,CAAC;MACRtH,KAAK,EAAE,KAAK,CAAE;KACf,EAAE;MACDsH,KAAK,EAAE,CAAC;MACRtH,KAAK,EAAE,KAAK,CAAE;KACf,CACF;IAED,KAAA0I,oBAAoB,GAAG,CACrB;MACEpB,KAAK,EAAE,CAAC,CAAC;MACTtH,KAAK,EAAE,KAAK,CAAC;KAEd,EAAE;MACDsH,KAAK,EAAE,CAAC;MACRtH,KAAK,EAAE,KAAK,CAAC;KAEd,EACD;MACEsH,KAAK,EAAE,CAAC;MACRtH,KAAK,EAAE,KAAK,CAAC;KAEd,CACF;IAED,KAAAgC,yBAAyB,GAAG,CAAC;MAC3BsF,KAAK,EAAE,CAAC,CAAC;MACTtH,KAAK,EAAE;KACR,EACD;MACEsH,KAAK,EAAE,CAAC;MACRtH,KAAK,EAAE,KAAK,CAAC;KAEd,EAAE;MACDsH,KAAK,EAAE,CAAC;MACRtH,KAAK,EAAE,KAAK,CAAC;KAEd,EACD;MACEsH,KAAK,EAAE,CAAC;MACRtH,KAAK,EAAE,KAAK,CAAC;KACd,CACA;IAED,KAAA8B,aAAa,GAAG,CAAC;MACfwF,KAAK,EAAE,CAAC;MAAE;MACVtH,KAAK,EAAE,IAAI,CAAC;KACb,EAAE;MACDsH,KAAK,EAAE,CAAC;MACRtH,KAAK,EAAE,IAAI,CAAC;KACb,CAAE;IAEH,KAAA2I,kBAAkB,GAAG,CAAC;MACpBrB,KAAK,EAAE,CAAC,CAAC;MACTtH,KAAK,EAAE;KACR,EAAE;MACDsH,KAAK,EAAE,CAAC;MAAE;MACVtH,KAAK,EAAE,IAAI,CAAC;KACb,EAAE;MACDsH,KAAK,EAAE,CAAC;MACRtH,KAAK,EAAE,IAAI,CAAC;KACb,CAAE;IAKH,KAAA4I,gBAAgB,GAAU,CAAC;MAAE5I,KAAK,EAAE,IAAI;MAAEsH,KAAK,EAAE;IAAE,CAAE,CAAC;IAItD;IACA,KAAAC,YAAY,GAAiB,EAAE,EAAC;IAChC,KAAApB,kBAAkB,GAAa,EAAE,EAAC;IAkClC,KAAAe,QAAQ,GAAkB,IAAI;IAC9B,KAAA2B,QAAQ,GAAQC,SAAS;IAQzB,KAAAC,gBAAgB,GAAqB;MACnCC,aAAa,EAAE,CAAC,YAAY,EAAE,WAAW,EAAE,iBAAiB,EAAE,MAAM,EAAE,MAAM,CAAC;MAC7EC,iBAAiB,EAAE,2BAA2B;MAC9CC,eAAe,EAAE,oDAAoD;MACrElJ,KAAK,EAAE,MAAM;MACbmJ,QAAQ,EAAE,6BAA6B;MACvCC,QAAQ,EAAE,IAAI;MACdC,QAAQ,EAAE,KAAK;MACfC,YAAY,EAAE,IAAI;MAClBC,UAAU,EAAE,IAAI;MAChBC,UAAU,EAAE,4BAA4B;MACxCC,WAAW,EAAE,EAAE;MACfC,QAAQ,EAAE,KAAK;MACfC,WAAW,EAAE;KACd;IAyCD;IACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IA6CA,KAAAnC,WAAW,GAAG,KAAK;IAiDnB,KAAA3E,YAAY,GAAQ,CAAC;IA4MrB,KAAAmE,KAAK,GAAG,IAAI;EA3eM;EAiGT4C,QAAQA,CAAA;IACf,IAAItK,mBAAmB,CAACuK,iBAAiB,CAACtK,WAAW,CAACuK,aAAa,CAAC,IAAI,IAAI,IACvExK,mBAAmB,CAACuK,iBAAiB,CAACtK,WAAW,CAACuK,aAAa,CAAC,IAAIhB,SAAS,IAC7ExJ,mBAAmB,CAACuK,iBAAiB,CAACtK,WAAW,CAACuK,aAAa,CAAC,IAAI,EAAE,EAAE;MAC3E,IAAIC,eAAe,GAAGC,IAAI,CAACC,KAAK,CAAC3K,mBAAmB,CAACuK,iBAAiB,CAACtK,WAAW,CAACuK,aAAa,CAAC,CAAC;MAClG,IAAI,CAAC1C,WAAW,GAAG;QACjBC,iBAAiB,EAAE,IAAI;QACvB/B,kBAAkB,EAAE,IAAI,CAACmD,sBAAsB,CAACyB,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC7C,KAAK,IAAIyC,eAAe,CAACtI,WAAW,CAAC6F,KAAK,CAAC,GACnG,IAAI,CAACmB,sBAAsB,CAACyB,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC7C,KAAK,IAAIyC,eAAe,CAACtI,WAAW,CAAC6F,KAAK,CAAC,GACnF,IAAI,CAACmB,sBAAsB,CAAC,CAAC,CAAC;QAClC2B,qBAAqB,EAAE,IAAI,CAACpI,yBAAyB,CAACkI,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC7C,KAAK,IAAIyC,eAAe,CAAChI,cAAc,CAACuF,KAAK,CAAC,GAC5G,IAAI,CAACtF,yBAAyB,CAACkI,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC7C,KAAK,IAAIyC,eAAe,CAAChI,cAAc,CAACuF,KAAK,CAAC,GACzF,IAAI,CAACtF,yBAAyB,CAAC,CAAC,CAAC;QACrC+D,aAAa,EAAE,IAAI,CAAC4C,kBAAkB,CAACuB,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC7C,KAAK,IAAIyC,eAAe,CAACM,cAAc,CAAC/C,KAAK,CAAC,GAC7F,IAAI,CAACqB,kBAAkB,CAACuB,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC7C,KAAK,IAAIyC,eAAe,CAACM,cAAc,CAAC/C,KAAK,CAAC,GAClF,IAAI,CAACqB,kBAAkB,CAAC,CAAC,CAAC;QAC9BhH,WAAW,EAAEoI,eAAe,CAACpI;OAC9B;IACH,CAAC,MACI;MACH,IAAI,CAACyF,WAAW,GAAG;QACjBC,iBAAiB,EAAE,IAAI;QACvB/B,kBAAkB,EAAE,IAAI,CAACmD,sBAAsB,CAAC,CAAC,CAAC;QAClD2B,qBAAqB,EAAE,IAAI,CAACpI,yBAAyB,CAAC,CAAC,CAAC;QACxD+D,aAAa,EAAE,IAAI,CAAC4C,kBAAkB,CAAC,CAAC,CAAC;QACzChH,WAAW,EAAE;OACd;IACH;IACA,IAAI,CAAC2I,gBAAgB,EAAE;EACzB;EAIA;EACA,IAAIrD,yBAAyBA,CAAA;IAC3B,OAAO;MACL,GAAG,IAAI,CAAC8B,gBAAgB;MACxBM,QAAQ,EAAE,IAAI,CAACxG,YAAY,KAAK;KACjC;EACH;EAgBA0H,UAAUA,CAAA;IACR,IAAI,IAAI,CAAC1B,QAAQ,EAAE;MACjB,IAAI,CAACA,QAAQ,GAAG,IAAI;MACpB,IAAI,CAAC3B,QAAQ,GAAG,IAAI;MACpB,IAAI,IAAI,CAACsD,SAAS,EAAE;QAClB,IAAI,CAACA,SAAS,CAACC,aAAa,CAACnD,KAAK,GAAG,IAAI,CAAC,CAAC;MAC7C;IACF;EACF;EAEA;EACA7B,cAAcA,CAACiF,MAAwB;IACrC,IAAI,CAACxD,QAAQ,GAAGwD,MAAM,CAACxD,QAAQ;IAC/B,IAAI,CAAC2B,QAAQ,GAAG;MACd8B,KAAK,EAAED,MAAM,CAACC,KAAK;MACnBC,KAAK,EAAEF,MAAM,CAACE,KAAK;MACnBC,IAAI,EAAEH,MAAM,CAACG,IAAI;MACjBC,WAAW,EAAEJ,MAAM,CAACI,WAAW;MAC/BC,SAAS,EAAEL,MAAM,CAACK;KACnB;EACH;EAEA;EACApF,aAAaA,CAAA;IACX,IAAI,CAACuB,QAAQ,GAAG,IAAI;IACpB,IAAI,CAAC2B,QAAQ,GAAG,IAAI;EACtB;EAEA;EACAhD,gBAAgBA,CAACqB,QAAgB;IAC/B,IAAI,CAAC,IAAI,CAAChC,cAAc,CAACvD,WAAW,EAAE;MACpC,IAAI,CAACuD,cAAc,CAACvD,WAAW,GAAGuF,QAAQ;IAC5C;EACF;EAEA;EACA,IAAI8D,oBAAoBA,CAAA;IACtB,OAAO,IAAI,CAACnI,YAAY,KAAK,CAAC;EAChC;EAoDAyB,oBAAoBA,CAAC2G,GAAU;IAC7B,OAAOA,GAAG,CAACC,KAAK,CAAEC,IAAyB,IAAKA,IAAI,CAAC1H,SAAS,CAAC;EACjE;EAEAb,uBAAuBA,CAACF,KAAa;IACnC,IAAI,IAAI,CAAC8E,WAAW,EAAE;MACpB,IAAI9E,KAAK,GAAG,CAAC,IAAIA,KAAK,IAAI,IAAI,CAACQ,WAAW,CAAC,CAAC,CAAC,CAACuB,MAAM,EAAE;QACpD,MAAM,IAAI2G,KAAK,CAAC,8DAA8D,CAAC;MACjF;MACA,KAAK,MAAMC,SAAS,IAAI,IAAI,CAACnI,WAAW,EAAE;QACxC,IAAIR,KAAK,IAAI2I,SAAS,CAAC5G,MAAM,IAAI,CAAC4G,SAAS,CAAC3I,KAAK,CAAC,CAACe,SAAS,EAAE;UAC5D,OAAO,KAAK,CAAC,CAAC;QAChB;MACF;MACA,OAAO,IAAI,CAAC,CAAC;IACf;IACA,OAAO,KAAK;EACd;EAEAd,gBAAgBA,CAAC2I,OAAgB,EAAE5I,KAAa;IAC9C,IAAIA,KAAK,GAAG,CAAC,EAAE;MACb,MAAM,IAAI0I,KAAK,CAAC,qDAAqD,CAAC;IACxE;IACA,KAAK,MAAMC,SAAS,IAAI,IAAI,CAACnI,WAAW,EAAE;MACxC,IAAIR,KAAK,GAAG2I,SAAS,CAAC5G,MAAM,EAAE;QAAE;QAC9B4G,SAAS,CAAC3I,KAAK,CAAC,CAACe,SAAS,GAAG6H,OAAO;MACtC;IACF;EACF;EAEAlH,YAAYA,CAACkH,OAAgB,EAAEL,GAAU;IACvC,KAAK,MAAME,IAAI,IAAIF,GAAG,EAAE;MACtBE,IAAI,CAAC1H,SAAS,GAAG6H,OAAO;IAC1B;EACF;EAGAC,cAAcA,CAACjE,KAAU,EAAEkE,OAAc;IACvC,KAAK,MAAML,IAAI,IAAIK,OAAO,EAAE;MAC1B,IAAIL,IAAI,CAAC7D,KAAK,KAAKA,KAAK,EAAE;QACxB,OAAO6D,IAAI;MACb;IACF;IACA,OAAO,IAAI;EACb;EAGAM,aAAaA,CAACN,IAAS,EAAEO,GAAQ;IAE/B,IAAI,CAACzD,cAAc,CAAC0D,+BAA+B,CAAC;MAClDC,IAAI,EAAET,IAAI,CAACU;KACZ,CAAC,CAACC,SAAS,CAACC,GAAG,IAAG;MACjB,IAAIA,GAAG,CAACC,OAAO,IAAID,GAAG,CAACE,UAAU,IAAI,CAAC,EAAE;QACtC,MAAMC,IAAI,GAAGH,GAAG,CAACC,OAAO;QACxB,IAAI,CAAC9G,cAAc,GAAG;UACpBiH,YAAY,EAAED,IAAI,CAACE,SAAS,EAAED,YAAY;UAC1CN,SAAS,EAAEK,IAAI,CAACE,SAAS,EAAEP,SAAS;UACpCpK,WAAW,EAAEyK,IAAI,CAACE,SAAS,EAAE3K,WAAW;UACxCE,WAAW,EAAEuK,IAAI,CAACE,SAAS,EAAEzK,WAAW,GAAGuK,IAAI,CAACE,SAAS,EAAEzK,WAAW,GAAG,EAAE;UAC3E0K,KAAK,EAAEH,IAAI,CAACE,SAAS,EAAEC,KAAK;UAC5BxK,OAAO,EAAEqK,IAAI,CAACE,SAAS,EAAEvK,OAAO;UAChCsF,QAAQ,EAAE+E,IAAI,CAACE,SAAS,EAAEjF,QAAQ;UAClCnC,YAAY,EAAEkH,IAAI,EAAElH,YAAY,GAAGkH,IAAI,EAAElH,YAAY,GAAG,EAAE;UAC1De,aAAa,EAAEmG,IAAI,CAACE,SAAS,EAAEvK,OAAO,GAAG,IAAI,CAAC0J,cAAc,CAACW,IAAI,CAACE,SAAS,EAAEvK,OAAO,EAAE,IAAI,CAACC,aAAa,CAAC,GAAG,IAAI,CAACA,aAAa,CAAC,CAAC,CAAC;UACjIwD,kBAAkB,EAAE4G,IAAI,CAACE,SAAS,EAAE3K,WAAW,GAAG,IAAI,CAAC8J,cAAc,CAACW,IAAI,CAACE,SAAS,EAAE3K,WAAW,EAAE,IAAI,CAACC,iBAAiB,CAAC,GAAG,IAAI,CAACA,iBAAiB,CAAC,CAAC,CAAC;UACtJyD,cAAc,EAAE+G,IAAI,CAAC/G,cAAc;UACnCmH,gBAAgB,EAAEJ,IAAI,EAAEI,gBAAgB,EAAEC,MAAM,CAAEC,CAAM,IAAKA,CAAC,CAAC/I,SAAS,CAAC;UACzE2I,SAAS,EAAEF,IAAI,CAACE;SACjB;QAED,IAAIF,IAAI,IAAIA,IAAI,EAAE/G,cAAc,IAAI+G,IAAI,EAAE/G,cAAc,CAACV,MAAM,EAAE;UAC/D,IAAIyH,IAAI,EAAE/G,cAAc,CAACV,MAAM,KAAK,CAAC,EAAE,OAAOqE,SAAS;UACvD,IAAI,CAACjG,YAAY,GAAGqJ,IAAI,EAAE/G,cAAc,CAAC,CAAC,CAAC,CAACJ,OAAO;UACnD,IAAI0H,UAAU,GAAGP,IAAI,EAAE/G,cAAc,CAAC,CAAC,CAAC,CAACP,SAAS,GAAG,IAAI8H,IAAI,CAACR,IAAI,EAAE/G,cAAc,CAAC,CAAC,CAAC,CAACP,SAAS,CAAC,GAAG,EAAE;UACrG,KAAK,IAAI4H,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGN,IAAI,CAAC/G,cAAc,CAACV,MAAM,EAAE+H,CAAC,EAAE,EAAE;YACnD,IAAIN,IAAI,CAAC/G,cAAc,CAACqH,CAAC,CAAC,CAAC5H,SAAS,EAAE;cACpC,MAAM+H,WAAW,GAAG,IAAID,IAAI,CAACR,IAAI,CAAC/G,cAAc,CAACqH,CAAC,CAAC,CAAC5H,SAAU,CAAC;cAC/D,IAAI+H,WAAW,GAAGF,UAAU,EAAE;gBAC5BA,UAAU,GAAGE,WAAW;gBACxB,IAAI,CAAC9J,YAAY,GAAGqJ,IAAI,EAAE/G,cAAc,CAACqH,CAAC,CAAC,CAACzH,OAAO;cACrD;YACF;UACF;QACF;QAEA;QACA,IAAI,CAAC6H,uBAAuB,EAAE;QAE9B;QACA,IAAIV,IAAI,EAAEI,gBAAgB,EAAE;UAC1B,IAAI,CAACnG,kBAAkB,GAAG+F,IAAI,CAACI,gBAAgB,CAC5CC,MAAM,CAAEpB,IAAS,IAAKA,IAAI,CAAC1H,SAAS,CAAC,CACrCoJ,GAAG,CAAE1B,IAAS,IAAKA,IAAI,CAAC2B,QAAQ,CAAC,CACjCP,MAAM,CAAEQ,EAAO,IAAKA,EAAE,KAAKjE,SAAS,CAAC;QAC1C;QAEA,IAAI,CAACkE,YAAY,EAAE;QACnB,IAAI,CAACpF,aAAa,CAACqF,IAAI,CAACvB,GAAG,CAAC;MAC9B;IACF,CAAC,CAAC;EACJ;EAIAwB,gBAAgBA,CAAChB,IAAa;IAC5B,MAAMiB,SAAS,GAAkB,EAAE;IACnC,KAAK,MAAM9B,SAAS,IAAIa,IAAI,EAAE;MAC5B,KAAK,MAAMkB,KAAK,IAAI/B,SAAS,EAAE;QAC7B,IAAI+B,KAAK,CAAC3J,SAAS,IAAI2J,KAAK,CAACzJ,SAAS,EAAE;UACtCwJ,SAAS,CAACE,IAAI,CAAC;YACbP,QAAQ,EAAEM,KAAK,CAACN,QAAQ;YACxBrJ,SAAS,EAAE2J,KAAK,CAAC3J,SAAS;YAC1BO,MAAM,EAAEoJ,KAAK,CAACpJ,MAAM;YACpBhB,UAAU,EAAEoK,KAAK,CAACpK;WACnB,CAAC;QACJ;MACF;IACF;IACA,OAAOmK,SAAS;EAClB;EAIArG,YAAYA,CAAC4E,GAAQ;IACnB,IAAI,CAAC4B,UAAU,EAAE;IACjB,IAAI,IAAI,CAACxF,KAAK,CAACyF,aAAa,CAAC9I,MAAM,GAAG,CAAC,EAAE;MACvC,IAAI,CAACoD,OAAO,CAAC2F,aAAa,CAAC,IAAI,CAAC1F,KAAK,CAACyF,aAAa,CAAC;MACpD;IACF;IAEA;IACA,IAAIE,YAAY,GAAkB,EAAE;IAEpC,IAAI,IAAI,CAACtH,kBAAkB,IAAI,IAAI,CAACA,kBAAkB,CAAC1B,MAAM,GAAG,CAAC,EAAE;MACjE;MACAgJ,YAAY,GAAG,IAAI,CAACC,sCAAsC,EAAE;IAC9D,CAAC,MAAM,IAAI,IAAI,CAACxK,WAAW,IAAI,IAAI,IAAI,IAAI,CAACA,WAAW,IAAI4F,SAAS,IAAI,IAAI,CAAC5F,WAAW,CAACuB,MAAM,GAAG,CAAC,EAAE;MACnG;MACAgJ,YAAY,GAAG,IAAI,CAACP,gBAAgB,CAAC,IAAI,CAAChK,WAAW,CAAC;IACxD;IAEA,IAAI,CAACyK,iBAAiB,GAAG;MACvBxB,YAAY,EAAE,IAAI,CAAC/E,WAAW,CAACC,iBAAiB,CAACC,KAAK;MACtDuE,SAAS,EAAE,IAAI,CAAC3G,cAAc,CAAC2G,SAAS;MACxCpK,WAAW,EAAE,IAAI,CAACyD,cAAc,CAACI,kBAAkB,CAACgC,KAAK;MACzD3F,WAAW,EAAE,IAAI,CAACuD,cAAc,CAACvD,WAAW;MAC5C0K,KAAK,EAAE,IAAI,CAACnH,cAAc,EAAEmH,KAAK;MACjCxK,OAAO,EAAE,IAAI,CAACqD,cAAc,CAACa,aAAa,CAACuB,KAAK;MAChDsD,KAAK,EAAE,IAAI,CAAC/B,QAAQ,GAAG,IAAI,CAACA,QAAQ,CAACiC,WAAW,GAAGhC,SAAS;MAC5D9D,YAAY,EAAE,IAAI,CAACE,cAAc,CAACF,YAAY;MAC9C4I,YAAY,EAAEH;KACf;IACD,IAAI,CAACvF,aAAa,CAAC2F,UAAU,CAAC,IAAI,CAACF,iBAAiB,CAAC,CAAC7B,SAAS,CAACC,GAAG,IAAG;MACpE,IAAIA,GAAG,IAAIA,GAAG,CAACH,IAAK,IAAIG,GAAG,CAACH,IAAI,CAACK,UAAW,KAAK,CAAC,EAAE;QAClD,IAAI,CAACpE,OAAO,CAACiG,aAAa,CAAC,MAAM,CAAC;QAClC,IAAI,CAACvD,UAAU,EAAE;QACjB,IAAI,CAACwD,aAAa,EAAE;QACpBrC,GAAG,CAACsC,KAAK,EAAE;MACb,CAAC,MAAM;QACL,IAAI,CAACnG,OAAO,CAACoG,YAAY,CAAClC,GAAG,IAAIA,GAAG,CAACH,IAAI,IAAIG,GAAG,CAACH,IAAI,CAACsC,OAAQ,CAAC;MACjE;IACF,CAAC,CAAC;EACJ;EAEAvN,QAAQA,CAAA;IACN,IAAIoJ,eAAe,GAAG;MACpBpI,WAAW,EAAE,IAAI,CAACyF,WAAW,CAACzF,WAAW;MACzCwM,kBAAkB,EAAE,IAAI,CAAC/G,WAAW,CAACC,iBAAiB;MACtDgD,cAAc,EAAE,IAAI,CAACjD,WAAW,CAACrB,aAAa;MAC9CtE,WAAW,EAAE,IAAI,CAAC2F,WAAW,CAAC9B,kBAAkB;MAChDvD,cAAc,EAAE,IAAI,CAACqF,WAAW,CAACgD;KAClC;IACD9K,mBAAmB,CAAC8O,iBAAiB,CAAC7O,WAAW,CAACuK,aAAa,EAAEE,IAAI,CAACqE,SAAS,CAACtE,eAAe,CAAC,CAAC;IACjG,IAAI,CAACgE,aAAa,EAAE;EACtB;EAEAO,WAAWA,CAACC,OAAe;IACzB,IAAI,CAAChG,SAAS,GAAGgG,OAAO;IACxB,IAAI,CAACR,aAAa,EAAE;EACtB;EAIAS,YAAYA,CAACC,YAAyB,EAAEC,QAAc;IACpD,MAAMC,WAAW,GAAkB,EAAE;IACrC,MAAMC,YAAY,GAAGC,KAAK,CAACC,IAAI,CAAC,IAAIC,GAAG,CACrCN,YAAY,CAAC5B,GAAG,CAACmC,QAAQ,IAAIA,QAAQ,CAAChL,MAAM,CAAC,CAACuI,MAAM,CAAC0C,KAAK,IAAIA,KAAK,KAAK,IAAI,CAAa,CAC1F,CAAC;IACF,KAAK,MAAMA,KAAK,IAAIL,YAAY,EAAE;MAChCD,WAAW,CAACtB,IAAI,CAAC,EAAE,CAAC;IACtB;IACA,KAAK,MAAM2B,QAAQ,IAAIP,YAAY,EAAE;MACnC,MAAMS,UAAU,GAAGN,YAAY,CAACO,OAAO,CAACH,QAAQ,CAAChL,MAAgB,CAAC;MAClE,IAAIkL,UAAU,KAAK,CAAC,CAAC,EAAE;QACrBP,WAAW,CAACO,UAAU,CAAC,CAAC7B,IAAI,CAAC;UAC3B5J,SAAS,EAAEuL,QAAQ,EAAEvL,SAAS,IAAI,KAAK;UACvCqJ,QAAQ,EAAEkC,QAAQ,CAAClL,GAAG;UACtBsL,UAAU,EAAEJ,QAAQ,CAACI,UAAU;UAC/BpL,MAAM,EAAEgL,QAAQ,CAAChL,MAAM;UACvBhB,UAAU,EAAEgM,QAAQ,CAAChM,UAAU;UAC/BW,SAAS,EAAEqL,QAAQ,CAACrL;SACrB,CAAC;MACJ;IACF;IACA,OAAOgL,WAAW;EACpB;EAMAU,eAAeA,CAACC,CAAQ,EAAEC,CAAQ;IAChC,MAAMC,IAAI,GAAG,IAAIC,GAAG,CAACF,CAAC,CAAC1C,GAAG,CAAC1B,IAAI,IAAI,CAAC,GAAGA,IAAI,CAACnI,UAAU,IAAImI,IAAI,CAACnH,MAAM,EAAE,EAAEmH,IAAI,CAAC1H,SAAS,CAAC,CAAC,CAAC;IAC1F,OAAO6L,CAAC,CAACzC,GAAG,CAAC1B,IAAI,IAAG;MAClB,MAAMuE,GAAG,GAAG,GAAGvE,IAAI,CAACnI,UAAU,IAAImI,IAAI,CAACnH,MAAM,EAAE;MAC/C,OAAO;QACL,GAAGmH,IAAI;QACP1H,SAAS,EAAE+L,IAAI,CAACG,GAAG,CAACD,GAAG,CAAC,GAAGF,IAAI,CAACI,GAAG,CAACF,GAAG,CAAC,GAAG;OAC5C;IACH,CAAC,CAAC;EACJ;EAEAG,qBAAqBA,CAACC,GAAsB;IAC1C,OAAOA,GAAG,CAACC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK,CAACA,CAAC,CAACjM,MAAM,IAAI,CAAC,KAAKgM,CAAC,CAAChM,MAAM,IAAI,CAAC,CAAC,CAAC;EAC9D;EAEAgJ,YAAYA,CAAA;IACV,OAAO,IAAI,CAACjF,aAAa,CAACmI,6BAA6B,CAAC;MACtDtE,IAAI,EAAE;QAAEuE,YAAY,EAAE,IAAI,CAAC/I,WAAW,CAACC,iBAAiB,CAACC,KAAK;QAAE8I,OAAO,EAAE;MAAK;KAC/E,CAAC,CAACC,IAAI,CACLnR,GAAG,CAAC6M,GAAG,IAAG;MACR,IAAIA,GAAG,IAAIA,GAAG,CAACE,UAAU,KAAK,CAAC,IAAIF,GAAG,CAACC,OAAO,EAAE;QAC9C,MAAMsE,IAAI,GAAG,IAAI,CAACT,qBAAqB,CAAC9D,GAAG,CAACC,OAAO,CAAC;QACpD,IAAI,CAACuE,eAAe,GAAG,CAAC,GAAGD,IAAI,CAAC;QAChC,IAAI,IAAI,CAACpL,cAAc,CAAC2G,SAAS,EAAE;UACjC,IAAI,CAAC3I,WAAW,GAAG,IAAI,CAACsL,YAAY,CAAC,IAAI,CAACa,eAAe,CAAC,CAAC,GAAG,IAAI,CAACkB,eAAe,CAAC,EAAE,IAAI,CAACrL,cAAc,CAACoH,gBAAgB,GAAG,CAAC,GAAG,IAAI,CAACpH,cAAc,CAACoH,gBAAgB,CAAC,GAAG,EAAE,CAAC,CAAC;QAC9K,CAAC,MAAM;UACL,IAAI,CAACpJ,WAAW,GAAG,IAAI,CAACsL,YAAY,CAAC,CAAC,GAAG8B,IAAI,CAAC,CAAC;QACjD;QACA,IAAI,CAAC9I,WAAW,GAAG,IAAI;MACzB;IACF,CAAC,CAAC,CAEH,CAACsE,SAAS,EAAE;EACf;EAOA0E,eAAeA,CAACrJ,QAAc;IAC5B,IAAIA,QAAQ,EAAE;MACZ,IAAI,CAACgB,cAAc,CAACsI,cAAc,CAACtJ,QAAQ,CAAC;IAC9C;EACF;EAEA4G,aAAaA,CAAA;IACX,OAAO,IAAI,CAAC9F,cAAc,CAACyI,+BAA+B,CAAC;MACzD9E,IAAI,EAAE;QACJ+E,SAAS,EAAE,IAAI,CAACpI,SAAS;QACzBqI,QAAQ,EAAE,IAAI,CAACtI,QAAQ;QACvB3G,WAAW,EAAE,IAAI,CAACyF,WAAW,CAACzF,WAAW;QACzCwO,YAAY,EAAE,IAAI,CAAC/I,WAAW,CAACC,iBAAiB,CAACC,KAAK;QACtDzF,OAAO,EAAE,IAAI,CAACuF,WAAW,CAACrB,aAAa,CAACuB,KAAK;QAC7C7F,WAAW,EAAE,IAAI,CAAC2F,WAAW,CAAC9B,kBAAkB,CAACgC,KAAK;QACtDvF,cAAc,EAAE,IAAI,CAACqF,WAAW,CAACgD,qBAAqB,CAAC9C;;KAE1D,CAAC,CAAC+I,IAAI,CACLnR,GAAG,CAAC6M,GAAG,IAAG;MACR,IAAIA,GAAG,CAACC,OAAO,IAAID,GAAG,CAACE,UAAU,IAAI,CAAC,EAAE;QACtC,IAAI,CAAC4E,UAAU,GAAG9E,GAAG,CAACC,OAAO;QAC7B,IAAI,CAACxD,YAAY,GAAGuD,GAAG,CAAC+E,UAAW;MACrC;IACF,CAAC,CAAC,CACH,CAAChF,SAAS,EAAE;EACf;EAGAiF,0BAA0BA,CAAA;IACxB,IAAI,IAAI,CAAC3J,WAAW,CAACC,iBAAiB,CAACC,KAAK,EAAE;MAC5C,IAAI,CAACyG,aAAa,EAAE;MACpB;MACA,IAAI,CAACnB,uBAAuB,EAAE;IAChC;EACF;EAEAtC,gBAAgBA,CAAA;IACd,IAAI,CAACtC,iBAAiB,CAACgJ,qCAAqC,CAAC;MAAEpF,IAAI,EAAE;IAAE,CAAE,CAAC,CAACyE,IAAI,CAC7EnR,GAAG,CAAC6M,GAAG,IAAG;MACR,IAAIA,GAAG,CAACC,OAAO,IAAID,GAAG,CAACE,UAAU,IAAI,CAAC,EAAE;QACtC,IAAI,CAACgF,oBAAoB,GAAGlF,GAAG,CAACC,OAAO,CAACa,GAAG,CAACd,GAAG,IAAG;UAChD,OAAO;YACL/L,KAAK,EAAE+L,GAAG,CAACmF,cAAc;YACzB5J,KAAK,EAAEyE,GAAG,CAACoF;WACZ;QACH,CAAC,CAAC;QACF,IAAI7R,mBAAmB,CAACuK,iBAAiB,CAACtK,WAAW,CAACuK,aAAa,CAAC,IAAI,IAAI,IACvExK,mBAAmB,CAACuK,iBAAiB,CAACtK,WAAW,CAACuK,aAAa,CAAC,IAAIhB,SAAS,IAC7ExJ,mBAAmB,CAACuK,iBAAiB,CAACtK,WAAW,CAACuK,aAAa,CAAC,IAAI,EAAE,EAAE;UAC3E,IAAIC,eAAe,GAAGC,IAAI,CAACC,KAAK,CAAC3K,mBAAmB,CAACuK,iBAAiB,CAACtK,WAAW,CAACuK,aAAa,CAAC,CAAC;UAClG,IAAIC,eAAe,CAACoE,kBAAkB,IAAI,IAAI,IAAIpE,eAAe,CAACoE,kBAAkB,IAAIrF,SAAS,EAAE;YACjG,IAAIpG,KAAK,GAAG,IAAI,CAACuO,oBAAoB,CAACG,SAAS,CAAEjH,CAAM,IAAKA,CAAC,CAAC7C,KAAK,IAAIyC,eAAe,CAACoE,kBAAkB,CAAC7G,KAAK,CAAC;YAChH,IAAI,CAACF,WAAW,CAACC,iBAAiB,GAAG,IAAI,CAAC4J,oBAAoB,CAACvO,KAAK,CAAC;UACvE,CAAC,MAAM;YACL,IAAI,CAAC0E,WAAW,CAACC,iBAAiB,GAAG,IAAI,CAAC4J,oBAAoB,CAAC,CAAC,CAAC;UACnE;QACF,CAAC,MACI;UACH,IAAI,CAAC7J,WAAW,CAACC,iBAAiB,GAAG,IAAI,CAAC4J,oBAAoB,CAAC,CAAC,CAAC;QACnE;QACA,IAAI,IAAI,CAAC7J,WAAW,CAACC,iBAAiB,CAACC,KAAK,EAAE;UAC5C,IAAI,CAACyG,aAAa,EAAE;QACtB;MACF;IACF,CAAC,CAAC,CACH,CAACjC,SAAS,EAAE;EACf;EAEA7K,SAASA,CAACyK,GAAQ,EAAEP,IAAU;IAC5B,IAAI,CAACtI,YAAY,GAAG,CAAC;IACrB,IAAI,CAAC2E,WAAW,GAAG,KAAK;IACxB,IAAI,CAACR,KAAK,GAAG,IAAI;IACjB,IAAI,CAACuD,UAAU,EAAE;IAEjB;IACA,IAAI,CAACpE,kBAAkB,GAAG,EAAE;IAE5B,IAAI,CAACjB,cAAc,GAAG;MACpBI,kBAAkB,EAAE,IAAI,CAAC5D,iBAAiB,CAAC,CAAC,CAAC;MAC7CqE,aAAa,EAAE,IAAI,CAACjE,aAAa,CAAC,CAAC,CAAC;MACpCsI,qBAAqB,EAAE,IAAI,CAAC1B,oBAAoB,CAAC,CAAC,CAAC;MACnD/G,WAAW,EAAE,EAAE;MACf0K,KAAK,EAAE,CAAC;MACRlF,QAAQ,EAAE,EAAE;MACZnC,YAAY,EAAE,EAAE;MAChBqM,YAAY,EAAE;KACf;IAED,IAAIlG,IAAI,EAAE;MACR,IAAI,CAACnE,KAAK,GAAG,KAAK;MAClB,IAAI,CAACyE,aAAa,CAACN,IAAI,EAAEO,GAAG,CAAC;IAC/B,CAAC,MAAM;MACL,IAAI,CAAC1E,KAAK,GAAG,IAAI;MACjB,IAAI,CAACgG,YAAY,EAAE;MACnB;MACA,IAAI,CAACJ,uBAAuB,EAAE;MAC9B,IAAI,CAAChF,aAAa,CAACqF,IAAI,CAACvB,GAAG,CAAC;IAC9B;EACF;EAEA4F,UAAUA,CAACC,WAAmB;IAC5B,IAAIA,WAAW,EAAE;MACf,OAAOnS,MAAM,CAACmS,WAAW,CAAC,CAACC,MAAM,CAAC,qBAAqB,CAAC;IAC1D;IACA,OAAO,EAAE;EACX;EAEAC,QAAQA,CAAC/F,GAAQ,GAEjB;EAEA9E,OAAOA,CAAC8E,GAAQ;IACdA,GAAG,CAACsC,KAAK,EAAE;EACb;EAGAV,UAAUA,CAAA;IACR,IAAI,CAACxF,KAAK,CAAC4J,KAAK,EAAE;IAClB,IAAI,IAAI,CAAC1K,KAAK,IAAI,CAAC,IAAI,CAAC6B,QAAQ,EAAE;MAChC,IAAI,CAACf,KAAK,CAAC6J,eAAe,CAAC,MAAM,CAAC;IACpC;IACA,IAAI,CAAC7J,KAAK,CAACsB,QAAQ,CAAC,QAAQ,EAAE,IAAI,CAAClE,cAAc,CAACF,YAAY,CAAC;EACjE;EAEAF,aAAaA,CAAC8M,QAA4B;IACxC,IAAIC,KAAK,GAAG,EAAE;IACd,IAAID,QAAQ,IAAI9I,SAAS,EAAE;MACzB,QAAQ8I,QAAQ;QACd,KAAK,CAAC;UACJC,KAAK,GAAG,IAAI;UACZ;QACF,KAAK,CAAC;UACJA,KAAK,GAAG,IAAI;UACZ;QACF,KAAK,CAAC;UACJA,KAAK,GAAG,IAAI;UACZ;QACF;UACE;MACJ;IACF;IACA,OAAOA,KAAK;EACd;EAEA/O,oBAAoBA,CAACgP,SAAoC,EAAE7C,KAAgC;IACzF,IAAI8C,KAAK,GAAG,CAAC;IACb,IAAIC,KAAK,GAAG,CAAC;IACb,IAAIF,SAAS,IAAI,IAAI,EAAE;MACrB,KAAK,IAAItF,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACtJ,WAAW,CAACuB,MAAM,EAAE+H,CAAC,EAAE,EAAE;QAChD,IAAI,CAACtJ,WAAW,CAACsJ,CAAC,CAAC,CAACK,GAAG,CAACoF,GAAG,IAAG;UAC5B,IAAIA,GAAG,CAACjP,UAAU,IAAIiP,GAAG,CAACjP,UAAU,IAAI8O,SAAS,EAAE;YACjDE,KAAK,EAAE;YACP,IAAIC,GAAG,CAACtO,SAAS,IAAI,IAAI,EAAE;cACzBoO,KAAK,EAAE;YACT;UACF;QACF,CAAC,CAAC;MACJ;IACF;IACA,IAAI9C,KAAK,IAAI,IAAI,EAAE;MACjB,KAAK,IAAIzC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACtJ,WAAW,CAACuB,MAAM,EAAE+H,CAAC,EAAE,EAAE;QAChD,IAAI,CAACtJ,WAAW,CAACsJ,CAAC,CAAC,CAACK,GAAG,CAACoF,GAAG,IAAG;UAC5B,IAAIA,GAAG,CAACjO,MAAM,IAAIiL,KAAK,EAAE;YACvB+C,KAAK,EAAE;YACP,IAAIC,GAAG,CAACtO,SAAS,IAAI,IAAI,EAAE;cACzBoO,KAAK,EAAE;YACT;UACF;QACF,CAAC,CAAC;MACJ;IACF;IAEA,IAAIA,KAAK,IAAIC,KAAK,EAAE;MAClB,OAAO,KAAK;IACd;IACA,OAAO,IAAI;EACb;EAEA;EACQpF,uBAAuBA,CAAA;IAC7B,IAAI,CAAC,IAAI,CAACxF,WAAW,CAACC,iBAAiB,EAAEC,KAAK,EAAE;IAEhD,IAAI,CAACc,mBAAmB,CAAC8J,WAAW,CAAC,IAAI,CAAC9K,WAAW,CAACC,iBAAiB,CAACC,KAAK,CAAC,CAACwE,SAAS,CAAC;MACvFqG,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAIA,QAAQ,IAAIA,QAAQ,CAACpG,OAAO,EAAE;UAChC,IAAI,CAACzE,YAAY,GAAG,IAAI,CAAC8K,gCAAgC,CAACD,QAAQ,CAACpG,OAAO,CAAC;QAC7E,CAAC,MAAM;UACL;UACA,IAAI,CAACzE,YAAY,GAAG,IAAI,CAAC+K,gCAAgC,EAAE;QAC7D;MACF,CAAC;MACDC,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACD,KAAK,CAAC,cAAc,EAAEA,KAAK,CAAC;QACpC;QACA,IAAI,CAAChL,YAAY,GAAG,IAAI,CAAC+K,gCAAgC,EAAE;MAC7D;KACD,CAAC;EACJ;EAEA;EACQD,gCAAgCA,CAACI,OAAY;IACnD,MAAMlL,YAAY,GAAiB,EAAE;IAErCmL,MAAM,CAACD,OAAO,CAACA,OAAO,CAAC,CAACE,OAAO,CAAC,CAAC,CAACC,QAAQ,EAAEC,MAAM,CAAgB,KAAI;MACpEtL,YAAY,CAACqL,QAAQ,CAAC,GAAGC,MAAM,CAAChG,GAAG,CAAEO,KAAU,KAAM;QACnD0F,SAAS,EAAE1F,KAAK,CAACpK,UAAU,IAAI,EAAE;QACjC4P,QAAQ,EAAEA,QAAQ;QAClB3D,KAAK,EAAE7B,KAAK,CAACpJ,MAAM,EAAE+O,QAAQ,EAAE,IAAI,EAAE;QACrCC,OAAO,EAAE5F,KAAK,CAACN,QAAQ;QACvBmG,SAAS,EAAE7F,KAAK,CAACgC,UAAU,IAAItG,SAAS;QACxCoK,UAAU,EAAE,KAAK;QACjBC,UAAU,EAAE;OACb,CAAC,CAAC;IACL,CAAC,CAAC;IAEF,OAAO5L,YAAY;EACrB;EAEA;EACA+K,gCAAgCA,CAAA;IAC9B,MAAM/K,YAAY,GAAiB,EAAE;IAErC,IAAI,CAAC,IAAI,CAACrE,WAAW,IAAI,IAAI,CAACA,WAAW,CAACuB,MAAM,KAAK,CAAC,EAAE;MACtD,OAAO8C,YAAY;IACrB;IAEA,IAAI,CAACrE,WAAW,CAACyP,OAAO,CAAC1H,GAAG,IAAG;MAC7BA,GAAG,CAAC0H,OAAO,CAACvF,KAAK,IAAG;QAClB,IAAIA,KAAK,CAACpK,UAAU,IAAIoK,KAAK,CAACpJ,MAAM,EAAE;UACpC,MAAM4O,QAAQ,GAAG,UAAU,CAAC,CAAC;UAE7B,IAAI,CAACrL,YAAY,CAACqL,QAAQ,CAAC,EAAE;YAC3BrL,YAAY,CAACqL,QAAQ,CAAC,GAAG,EAAE;UAC7B;UAEArL,YAAY,CAACqL,QAAQ,CAAC,CAACvF,IAAI,CAAC;YAC1ByF,SAAS,EAAE1F,KAAK,CAACpK,UAAU;YAC3B4P,QAAQ,EAAEA,QAAQ;YAClB3D,KAAK,EAAE7B,KAAK,CAACpJ,MAAM,EAAE+O,QAAQ,EAAE,IAAI,EAAE;YACrCC,OAAO,EAAE5F,KAAK,CAACN,QAAQ;YACvBmG,SAAS,EAAE7F,KAAK,CAACgC,UAAU,IAAItG,SAAS;YACxCoK,UAAU,EAAE9F,KAAK,CAAC3J,SAAS,IAAI,KAAK;YACpC0P,UAAU,EAAE,CAAC/F,KAAK,CAACzJ;WACpB,CAAC;QACJ;MACF,CAAC,CAAC;IACJ,CAAC,CAAC;IAEF,OAAO4D,YAAY;EACrB;EAEA;EACAhB,0BAA0BA,CAAC6M,aAA8B;IACvD;IACA,IAAI,CAACjN,kBAAkB,GAAGiN,aAAa,CAACvG,GAAG,CAAC1B,IAAI,IAAIA,IAAI,CAAC6H,OAAO,CAAC,CAACzG,MAAM,CAACQ,EAAE,IAAIA,EAAE,KAAKjE,SAAS,CAAa;IAE5G;IACA,IAAI,CAACuK,6BAA6B,CAAC,IAAI,CAAClN,kBAAkB,CAAC;EAC7D;EAEA;EACAE,mBAAmBA,CAACiN,WAAqB;IACvC,IAAI,CAACnN,kBAAkB,GAAGmN,WAAW;IAErC;IACA,IAAI,CAACD,6BAA6B,CAAC,IAAI,CAAClN,kBAAkB,CAAC;EAC7D;EAEA;EACQkN,6BAA6BA,CAACC,WAAqB;IACzD,IAAI,CAAC,IAAI,CAACpQ,WAAW,EAAE;IAEvB,IAAI,CAACA,WAAW,CAACyP,OAAO,CAAC1H,GAAG,IAAG;MAC7BA,GAAG,CAAC0H,OAAO,CAACvF,KAAK,IAAG;QAClBA,KAAK,CAAC3J,SAAS,GAAG6P,WAAW,CAACC,QAAQ,CAACnG,KAAK,CAACN,QAAQ,IAAI,CAAC,CAAC;MAC7D,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ;EAEA;EACAY,sCAAsCA,CAAA;IACpC,MAAMhD,MAAM,GAAkB,EAAE;IAEhC;IACA,IAAI,IAAI,CAACxH,WAAW,IAAI,IAAI,CAACiD,kBAAkB,CAAC1B,MAAM,GAAG,CAAC,EAAE;MAC1D,IAAI,CAACvB,WAAW,CAACyP,OAAO,CAAC1H,GAAG,IAAG;QAC7BA,GAAG,CAAC0H,OAAO,CAACvF,KAAK,IAAG;UAClB,IAAIA,KAAK,CAACN,QAAQ,IAAI,IAAI,CAAC3G,kBAAkB,CAACoN,QAAQ,CAACnG,KAAK,CAACN,QAAQ,CAAC,EAAE;YACtEpC,MAAM,CAAC2C,IAAI,CAAC;cACVP,QAAQ,EAAEM,KAAK,CAACN,QAAQ;cACxBrJ,SAAS,EAAE,IAAI;cACfO,MAAM,EAAEoJ,KAAK,CAACpJ,MAAM,IAAI8E,SAAS;cACjC9F,UAAU,EAAEoK,KAAK,CAACpK;aACnB,CAAC;UACJ;QACF,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ;IAEA,OAAO0H,MAAM;EACf;;;uCAryBWjD,iCAAiC,EAAAjI,EAAA,CAAAgU,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAlU,EAAA,CAAAgU,iBAAA,CAAAG,EAAA,CAAAC,eAAA,GAAApU,EAAA,CAAAgU,iBAAA,CAAAK,EAAA,CAAAC,cAAA,GAAAtU,EAAA,CAAAgU,iBAAA,CAAAO,EAAA,CAAAC,gBAAA,GAAAxU,EAAA,CAAAgU,iBAAA,CAAAS,EAAA,CAAAC,YAAA,GAAA1U,EAAA,CAAAgU,iBAAA,CAAAS,EAAA,CAAAE,gBAAA,GAAA3U,EAAA,CAAAgU,iBAAA,CAAAS,EAAA,CAAAG,aAAA,GAAA5U,EAAA,CAAAgU,iBAAA,CAAAa,EAAA,CAAAC,mBAAA,GAAA9U,EAAA,CAAAgU,iBAAA,CAAAe,EAAA,CAAAC,cAAA,GAAAhV,EAAA,CAAAgU,iBAAA,CAAAiB,EAAA,CAAAC,kBAAA;IAAA;EAAA;;;YAAjCjN,iCAAiC;MAAAkN,SAAA;MAAAC,SAAA,WAAAC,wCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;;;;;;;;;;;;;;;UC1F5CtV,EADF,CAAAC,cAAA,iBAA0B,qBACR;UACdD,EAAA,CAAAoB,SAAA,qBAAiC;UACnCpB,EAAA,CAAAG,YAAA,EAAiB;UAEfH,EADF,CAAAC,cAAA,mBAAc,YACyB;UAAAD,EAAA,CAAAE,MAAA,2OAAsC;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAI1EH,EAHN,CAAAC,cAAA,aAA8B,aACN,aACqC,eACT;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACxDH,EAAA,CAAAC,cAAA,oBACkD;UADtBD,EAAA,CAAA4D,gBAAA,2BAAA4R,+EAAAzS,MAAA;YAAA/C,EAAA,CAAAc,aAAA,CAAA2U,GAAA;YAAAzV,EAAA,CAAAgE,kBAAA,CAAAuR,GAAA,CAAA3N,WAAA,CAAAC,iBAAA,EAAA9E,MAAA,MAAAwS,GAAA,CAAA3N,WAAA,CAAAC,iBAAA,GAAA9E,MAAA;YAAA,OAAA/C,EAAA,CAAAkB,WAAA,CAAA6B,MAAA;UAAA,EAA2C;UACrE/C,EAAA,CAAAY,UAAA,4BAAA8U,gFAAA;YAAA1V,EAAA,CAAAc,aAAA,CAAA2U,GAAA;YAAA,OAAAzV,EAAA,CAAAkB,WAAA,CAAkBqU,GAAA,CAAAhE,0BAAA,EAA4B;UAAA,EAAC;UAC/CvR,EAAA,CAAA8B,UAAA,KAAA6T,uDAAA,uBAAoE;UAK1E3V,EAFI,CAAAG,YAAA,EAAY,EACR,EACF;UAGFH,EAFJ,CAAAC,cAAA,cAAsB,cACqC,gBACV;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACvDH,EAAA,CAAAC,cAAA,qBAAuF;UAA3DD,EAAA,CAAA4D,gBAAA,2BAAAgS,+EAAA7S,MAAA;YAAA/C,EAAA,CAAAc,aAAA,CAAA2U,GAAA;YAAAzV,EAAA,CAAAgE,kBAAA,CAAAuR,GAAA,CAAA3N,WAAA,CAAA9B,kBAAA,EAAA/C,MAAA,MAAAwS,GAAA,CAAA3N,WAAA,CAAA9B,kBAAA,GAAA/C,MAAA;YAAA,OAAA/C,EAAA,CAAAkB,WAAA,CAAA6B,MAAA;UAAA,EAA4C;UACtE/C,EAAA,CAAA8B,UAAA,KAAA+T,uDAAA,uBAAsE;UAK5E7V,EAFI,CAAAG,YAAA,EAAY,EACR,EACF;UAIFH,EAFJ,CAAAC,cAAA,cAAsB,cACqC,iBACV;UAACD,EAAA,CAAAE,MAAA,sBAAG;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAEvDH,EADF,CAAAC,cAAA,eAAmB,iBAEuB;UAAtCD,EAAA,CAAA4D,gBAAA,2BAAAkS,2EAAA/S,MAAA;YAAA/C,EAAA,CAAAc,aAAA,CAAA2U,GAAA;YAAAzV,EAAA,CAAAgE,kBAAA,CAAAuR,GAAA,CAAA3N,WAAA,CAAAzF,WAAA,EAAAY,MAAA,MAAAwS,GAAA,CAAA3N,WAAA,CAAAzF,WAAA,GAAAY,MAAA;YAAA,OAAA/C,EAAA,CAAAkB,WAAA,CAAA6B,MAAA;UAAA,EAAqC;UAG7C/C,EAJM,CAAAG,YAAA,EACwC,EACpC,EACF,EACF;UAGFH,EAFJ,CAAAC,cAAA,cAAsB,cACqC,iBACd;UACvCD,EAAA,CAAAE,MAAA,sBACF;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACRH,EAAA,CAAAC,cAAA,qBAAkF;UAAtDD,EAAA,CAAA4D,gBAAA,2BAAAmS,+EAAAhT,MAAA;YAAA/C,EAAA,CAAAc,aAAA,CAAA2U,GAAA;YAAAzV,EAAA,CAAAgE,kBAAA,CAAAuR,GAAA,CAAA3N,WAAA,CAAArB,aAAA,EAAAxD,MAAA,MAAAwS,GAAA,CAAA3N,WAAA,CAAArB,aAAA,GAAAxD,MAAA;YAAA,OAAA/C,EAAA,CAAAkB,WAAA,CAAA6B,MAAA;UAAA,EAAuC;UACjE/C,EAAA,CAAA8B,UAAA,KAAAkU,uDAAA,uBAAkE;UAKxEhW,EAFI,CAAAG,YAAA,EAAY,EACR,EACF;UAIFH,EAFJ,CAAAC,cAAA,cAAsB,cACqC,iBACP;UAC9CD,EAAA,CAAAE,MAAA,kCACF;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACRH,EAAA,CAAAC,cAAA,qBAA4F;UAA9DD,EAAA,CAAA4D,gBAAA,2BAAAqS,+EAAAlT,MAAA;YAAA/C,EAAA,CAAAc,aAAA,CAAA2U,GAAA;YAAAzV,EAAA,CAAAgE,kBAAA,CAAAuR,GAAA,CAAA3N,WAAA,CAAAgD,qBAAA,EAAA7H,MAAA,MAAAwS,GAAA,CAAA3N,WAAA,CAAAgD,qBAAA,GAAA7H,MAAA;YAAA,OAAA/C,EAAA,CAAAkB,WAAA,CAAA6B,MAAA;UAAA,EAA+C;UAC3E/C,EAAA,CAAA8B,UAAA,KAAAoU,uDAAA,uBAAyE;UAK/ElW,EAFI,CAAAG,YAAA,EAAY,EACR,EACF;UAGJH,EADF,CAAAC,cAAA,cAAsB,eAC8C;UAIhED,EAHA,CAAA8B,UAAA,KAAAqU,oDAAA,qBAA6E,KAAAC,oDAAA,qBAGS;UAK5FpW,EAFI,CAAAG,YAAA,EAAM,EACF,EACF;UAKEH,EAJR,CAAAC,cAAA,eAAmC,iBAC4C,aACpE,cACgD,cACpB;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACtCH,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACrCH,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAE,MAAA,gCAAI;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACvCH,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACrCH,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAE,MAAA,gCAAI;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACvCH,EAAA,CAAAC,cAAA,cAA0C;UAAAD,EAAA,CAAAE,MAAA,gCAAI;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACnDH,EAAA,CAAAC,cAAA,cAA0C;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAEhDF,EAFgD,CAAAG,YAAA,EAAK,EAC9C,EACC;UACRH,EAAA,CAAAC,cAAA,aAAO;UACLD,EAAA,CAAA8B,UAAA,KAAAuU,gDAAA,mBAAoD;UA6B5DrW,EAHM,CAAAG,YAAA,EAAQ,EACF,EACJ,EACO;UAEbH,EADF,CAAAC,cAAA,0BAAsD,0BAES;UAD7CD,EAAA,CAAA4D,gBAAA,wBAAA0S,iFAAAvT,MAAA;YAAA/C,EAAA,CAAAc,aAAA,CAAA2U,GAAA;YAAAzV,EAAA,CAAAgE,kBAAA,CAAAuR,GAAA,CAAAxM,SAAA,EAAAhG,MAAA,MAAAwS,GAAA,CAAAxM,SAAA,GAAAhG,MAAA;YAAA,OAAA/C,EAAA,CAAAkB,WAAA,CAAA6B,MAAA;UAAA,EAAoB;UAClC/C,EAAA,CAAAY,UAAA,wBAAA0V,iFAAAvT,MAAA;YAAA/C,EAAA,CAAAc,aAAA,CAAA2U,GAAA;YAAA,OAAAzV,EAAA,CAAAkB,WAAA,CAAcqU,GAAA,CAAAzG,WAAA,CAAA/L,MAAA,CAAmB;UAAA,EAAC;UAGxC/C,EAFI,CAAAG,YAAA,EAAiB,EACF,EACT;UAGVH,EAAA,CAAA8B,UAAA,KAAAyU,yDAAA,kCAAAvW,EAAA,CAAAwW,sBAAA,CAAoD;;;UArHdxW,EAAA,CAAAM,SAAA,IAA2C;UAA3CN,EAAA,CAAAkE,gBAAA,YAAAqR,GAAA,CAAA3N,WAAA,CAAAC,iBAAA,CAA2C;UAEzC7H,EAAA,CAAAM,SAAA,EAAuB;UAAvBN,EAAA,CAAAI,UAAA,YAAAmV,GAAA,CAAA9D,oBAAA,CAAuB;UASzBzR,EAAA,CAAAM,SAAA,GAA4C;UAA5CN,EAAA,CAAAkE,gBAAA,YAAAqR,GAAA,CAAA3N,WAAA,CAAA9B,kBAAA,CAA4C;UAC1C9F,EAAA,CAAAM,SAAA,EAAyB;UAAzBN,EAAA,CAAAI,UAAA,YAAAmV,GAAA,CAAAtM,sBAAA,CAAyB;UAYnDjJ,EAAA,CAAAM,SAAA,GAAqC;UAArCN,EAAA,CAAAkE,gBAAA,YAAAqR,GAAA,CAAA3N,WAAA,CAAAzF,WAAA,CAAqC;UASbnC,EAAA,CAAAM,SAAA,GAAuC;UAAvCN,EAAA,CAAAkE,gBAAA,YAAAqR,GAAA,CAAA3N,WAAA,CAAArB,aAAA,CAAuC;UACrCvG,EAAA,CAAAM,SAAA,EAAqB;UAArBN,EAAA,CAAAI,UAAA,YAAAmV,GAAA,CAAApM,kBAAA,CAAqB;UAYrBnJ,EAAA,CAAAM,SAAA,GAA+C;UAA/CN,EAAA,CAAAkE,gBAAA,YAAAqR,GAAA,CAAA3N,WAAA,CAAAgD,qBAAA,CAA+C;UAC/C5K,EAAA,CAAAM,SAAA,EAA4B;UAA5BN,EAAA,CAAAI,UAAA,YAAAmV,GAAA,CAAA/S,yBAAA,CAA4B;UAShBxC,EAAA,CAAAM,SAAA,GAAY;UAAZN,EAAA,CAAAI,UAAA,SAAAmV,GAAA,CAAAkB,MAAA,CAAY;UAGZzW,EAAA,CAAAM,SAAA,EAAc;UAAdN,EAAA,CAAAI,UAAA,SAAAmV,GAAA,CAAAmB,QAAA,CAAc;UAoBnC1W,EAAA,CAAAM,SAAA,IAAgB;UAAhBN,EAAA,CAAAI,UAAA,YAAAmV,GAAA,CAAAlE,UAAA,CAAgB;UA+B3BrR,EAAA,CAAAM,SAAA,GAAoB;UAApBN,EAAA,CAAAkE,gBAAA,SAAAqR,GAAA,CAAAxM,SAAA,CAAoB;UAAuB/I,EAAtB,CAAAI,UAAA,aAAAmV,GAAA,CAAAzM,QAAA,CAAqB,mBAAAyM,GAAA,CAAAvM,YAAA,CAAgC;;;qBDhClF1J,YAAY,EAAAqX,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,OAAA,EAAAF,EAAA,CAAAG,IAAA,EAAE1X,YAAY,EAAA2X,GAAA,CAAAC,oBAAA,EAAAD,GAAA,CAAAE,eAAA,EAAAF,GAAA,CAAAG,OAAA,EAAA/C,EAAA,CAAAgD,eAAA,EAAAhD,EAAA,CAAAiD,mBAAA,EAAAjD,EAAA,CAAAkD,qBAAA,EAAAlD,EAAA,CAAAmD,qBAAA,EAAAnD,EAAA,CAAAoD,mBAAA,EAAApD,EAAA,CAAAqD,gBAAA,EAAArD,EAAA,CAAAsD,iBAAA,EAAAtD,EAAA,CAAAuD,iBAAA,EAAAC,GAAA,CAAAC,aAAA,EAAAC,GAAA,CAAAC,mBAAA,EAAAC,GAAA,CAAAC,kBAAA,EAAAC,GAAA,CAAAC,mBAAA,EAAAC,GAAA,CAAAC,cAAA,EAAE/Y,eAAe,EAAAgZ,GAAA,CAAAC,yBAAA,EAAE/Y,kBAAkB,EAAEI,mBAAmB,EAAkBH,kBAAkB,EAAEK,kBAAkB;MAAA0Y,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}