{"ast": null, "code": "export class StatsProgressBarData {}", "map": {"version": 3, "names": ["StatsProgressBarData"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\@core\\data\\stats-progress-bar.ts"], "sourcesContent": ["import { Observable } from 'rxjs';\r\n\r\nexport interface ProgressInfo {\r\n  title: string;\r\n  value: number;\r\n  activeProgress: number;\r\n  description: string;\r\n}\r\n\r\nexport abstract class StatsProgressBarData {\r\n  abstract getProgressInfoData(): Observable<ProgressInfo[]>;\r\n}\r\n"], "mappings": "AASA,OAAM,MAAgBA,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}