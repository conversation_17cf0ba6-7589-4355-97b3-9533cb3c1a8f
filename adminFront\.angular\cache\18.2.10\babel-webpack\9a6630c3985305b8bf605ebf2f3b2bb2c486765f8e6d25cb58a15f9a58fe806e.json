{"ast": null, "code": "import * as CryptoJ<PERSON> from \"crypto-js\";\nimport * as i0 from \"@angular/core\";\nexport class CryptoService {\n  static {\n    this._key = CryptoJS.enc.Utf8.parse(\"DVROrKiHmncUgWBj\" /* CRYPTO.TOKEN_KEY */);\n  }\n  static {\n    this._iv = CryptoJS.enc.Utf8.parse(\"xcsJYPvUkxjdLi8b\" /* CRYPTO.TOKEN_IV */);\n  }\n  static EncryptUsingAES256(req) {\n    let result = CryptoJS.AES.encrypt(JSON.stringify(req), this._key, {\n      keySize: 16,\n      iv: this._iv,\n      mode: CryptoJS.mode.ECB,\n      padding: CryptoJS.pad.Pkcs7\n    }).toString();\n    return result;\n  }\n  static DecryptUsingAES256(encrypted) {\n    let result = CryptoJS.AES.decrypt(encrypted, this._key, {\n      keySize: 16,\n      iv: this._iv,\n      mode: CryptoJS.mode.ECB,\n      padding: CryptoJS.pad.Pkcs7\n    }).toString(CryptoJS.enc.Utf8);\n    return result;\n  }\n  static {\n    this.ɵfac = function CryptoService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || CryptoService)();\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: CryptoService,\n      factory: CryptoService.ɵfac,\n      providedIn: \"root\"\n    });\n  }\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}