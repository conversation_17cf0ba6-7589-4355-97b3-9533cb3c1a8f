{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { NgModule } from '@angular/core';\nimport * as i1 from '@nebular/theme';\nimport { NbSvgIcon } from '@nebular/theme';\nimport { icons } from 'eva-icons';\n\n/*\n * @license\n * Copyright Akveo. All Rights Reserved.\n * Licensed under the MIT License. See License.txt in the project root for license information.\n */\nclass NbEvaSvgIcon extends NbSvgIcon {\n  constructor(name, content) {\n    super(name, '');\n    this.name = name;\n    this.content = content;\n  }\n  getContent(options) {\n    return this.content.toSvg({\n      width: '100%',\n      height: '100%',\n      fill: 'currentColor',\n      ...options\n    });\n  }\n}\nlet NbEvaIconsModule = /*#__PURE__*/(() => {\n  class NbEvaIconsModule {\n    constructor(iconLibrary) {\n      this.NAME = 'eva';\n      iconLibrary.registerSvgPack(this.NAME, this.createIcons());\n      iconLibrary.setDefaultPack(this.NAME);\n    }\n    createIcons() {\n      return Object.entries(icons).map(([name, icon]) => {\n        return [name, new NbEvaSvgIcon(name, icon)];\n      }).reduce((newIcons, [name, icon]) => {\n        newIcons[name] = icon;\n        return newIcons;\n      }, {});\n    }\n    static {\n      this.ɵfac = function NbEvaIconsModule_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || NbEvaIconsModule)(i0.ɵɵinject(i1.NbIconLibraries));\n      };\n    }\n    static {\n      this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n        type: NbEvaIconsModule\n      });\n    }\n    static {\n      this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({});\n    }\n  }\n  return NbEvaIconsModule;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/*\n * @license\n * Copyright Akveo. All Rights Reserved.\n * Licensed under the MIT License. See License.txt in the project root for license information.\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { NbEvaIconsModule, NbEvaSvgIcon };\n//# sourceMappingURL=nebular-eva-icons.mjs.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}