{"ast": null, "code": "import { EmptyError } from './util/EmptyError';\nexport function lastValueFrom(source, config) {\n  const hasConfig = typeof config === 'object';\n  return new Promise((resolve, reject) => {\n    let _hasValue = false;\n    let _value;\n    source.subscribe({\n      next: value => {\n        _value = value;\n        _hasValue = true;\n      },\n      error: reject,\n      complete: () => {\n        if (_hasValue) {\n          resolve(_value);\n        } else if (hasConfig) {\n          resolve(config.defaultValue);\n        } else {\n          reject(new EmptyError());\n        }\n      }\n    });\n  });\n}\n//# sourceMappingURL=lastValueFrom.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}