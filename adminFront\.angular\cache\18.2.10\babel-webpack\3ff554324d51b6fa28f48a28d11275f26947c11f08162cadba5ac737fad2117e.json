{"ast": null, "code": "import { of as observableOf } from 'rxjs';\nimport { OrdersProfitChartData } from '../data/orders-profit-chart';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../data/orders-chart\";\nimport * as i2 from \"../data/profit-chart\";\nexport let OrdersProfitChartService = /*#__PURE__*/(() => {\n  class OrdersProfitChartService extends OrdersProfitChartData {\n    constructor(ordersChartService, profitChartService) {\n      super();\n      this.ordersChartService = ordersChartService;\n      this.profitChartService = profitChartService;\n      this.summary = [{\n        title: 'Marketplace',\n        value: 3654\n      }, {\n        title: 'Last Month',\n        value: 946\n      }, {\n        title: 'Last Week',\n        value: 654\n      }, {\n        title: 'Today',\n        value: 230\n      }];\n    }\n    getOrderProfitChartSummary() {\n      return observableOf(this.summary);\n    }\n    getOrdersChartData(period) {\n      return observableOf(this.ordersChartService.getOrdersChartData(period));\n    }\n    getProfitChartData(period) {\n      return observableOf(this.profitChartService.getProfitChartData(period));\n    }\n    static {\n      this.ɵfac = function OrdersProfitChartService_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || OrdersProfitChartService)(i0.ɵɵinject(i1.OrdersChartData), i0.ɵɵinject(i2.ProfitChartData));\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: OrdersProfitChartService,\n        factory: OrdersProfitChartService.ɵfac\n      });\n    }\n  }\n  return OrdersProfitChartService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}