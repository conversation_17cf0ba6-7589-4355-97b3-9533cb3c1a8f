// 使用 nb-card 結構，移除不必要的包裝樣式

// nb-card-header 按鈕樣式
nb-card-header {
    .btn-success {
        background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        border: none;
        font-weight: 500;
        transition: all 0.2s ease;

        &:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
        }
    }
}

// 搜尋容器樣式
.search-container {
    .input-group {
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        border-radius: 8px;
        overflow: hidden;

        .form-control {
            border: none;
            padding: 0.75rem 1rem;
            font-size: 0.95rem;

            &:focus {
                box-shadow: none;
                border-color: transparent;
            }

            &::placeholder {
                color: #999;
                font-style: italic;
            }
        }

        .input-group-append {
            .btn {
                border: none;
                background: #f8f9fa;
                color: #6c757d;
                padding: 0.75rem 1rem;
                transition: all 0.2s ease;

                &:hover {
                    background: #e9ecef;
                    color: #495057;
                }

                &:focus {
                    box-shadow: none;
                }
            }
        }
    }
}

// 搜尋結果資訊
.search-results-info {
    padding: 0.5rem 0;
    border-left: 3px solid #007bff;
    padding-left: 0.75rem;
    background: #f8f9ff;
    border-radius: 4px;
}

// 分頁資訊
.pagination-info {
    padding: 0.5rem 0;
    border-left: 3px solid #28a745;
    padding-left: 0.75rem;
    background: #f8fff8;
    border-radius: 4px;
}

// 表格樣式
.table-responsive {
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.table {
    margin-bottom: 0;

    thead.thead-light {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);

        th {
            border: none;
            font-weight: 600;
            color: #495057;
            padding: 1rem 0.75rem;
            font-size: 0.9rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
    }

    tbody {
        tr {
            transition: all 0.2s ease;

            &:hover {
                background-color: #f8f9ff;
                transform: translateY(-1px);
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            }

            td {
                padding: 1rem 0.75rem;
                border-color: #f0f0f0;
                vertical-align: middle;

                strong {
                    color: #333;
                    font-weight: 600;
                }

                .text-muted {
                    font-size: 0.9rem;
                }
            }
        }
    }
}

// 按鈕組樣式
.btn-group-sm {
    .btn {
        padding: 0.375rem 0.75rem;
        font-size: 0.875rem;
        border-radius: 4px;
        font-weight: 500;
        transition: all 0.2s ease;

        &.btn-info {
            background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
            border: none;

            &:hover {
                transform: translateY(-1px);
                box-shadow: 0 4px 12px rgba(23, 162, 184, 0.3);
            }
        }

        &.btn-danger {
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
            border: none;

            &:hover {
                transform: translateY(-1px);
                box-shadow: 0 4px 12px rgba(220, 53, 69, 0.3);
            }
        }

        i {
            margin-right: 0.25rem;
        }
    }
}

// 空狀態樣式
.empty-state {
    padding: 2rem;

    i {
        display: block;
        margin: 0 auto 1rem;
        opacity: 0.5;
    }

    p {
        font-size: 1.1rem;
        margin-bottom: 0.5rem;
    }

    a {
        color: #007bff;
        text-decoration: none;

        &:hover {
            text-decoration: underline;
        }
    }
}

// 模板詳情模態框
.template-detail-modal {
    margin-top: 1.5rem;
    background: #fff;
    border: 2px solid #e9ecef;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    animation: slideDown 0.3s ease-out;
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.template-detail-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    padding: 1rem 1.5rem;
    border-bottom: 1px solid #dee2e6;

    h6 {
        color: #495057;
        font-weight: 600;

        i {
            color: #6c757d;
        }
    }

    .btn {
        font-size: 0.875rem;
        padding: 0.375rem 0.75rem;
    }
}

.template-detail-content {
    padding: 1.5rem;
    max-height: 400px;
    overflow-y: auto;

    .template-description {
        background: #f8f9ff;
        padding: 1rem;
        border-radius: 8px;
        border-left: 4px solid #007bff;

        strong {
            color: #495057;
        }
    }

    .template-items {
        h6 {
            color: #495057;
            font-weight: 600;
            padding-bottom: 0.5rem;
            border-bottom: 1px solid #e9ecef;

            i {
                color: #6c757d;
            }
        }
    }
}

.detail-list {
    .detail-item {
        transition: all 0.2s ease;

        &:hover {
            background: #f8f9ff;
            border-radius: 6px;
            margin: 0 -0.5rem;
            padding-left: 0.5rem !important;
            padding-right: 0.5rem !important;
        }

        &:last-child {
            border-bottom: none;
        }

        .detail-index {
            .badge {
                background: #e9ecef;
                color: #6c757d;
                font-weight: 500;
                padding: 0.375rem 0.5rem;
                border-radius: 50%;
                min-width: 2rem;
                text-align: center;
            }
        }

        .detail-content {
            .detail-field {
                font-size: 0.9rem;
                margin-bottom: 0.25rem;

                strong {
                    color: #495057;
                }
            }

            .detail-value {
                font-size: 0.875rem;
                line-height: 1.4;
                word-break: break-word;
            }
        }
    }
}

// 新增模板表單樣式 - 簡潔版
.add-template-form {
    .form-container {
        background: #ffffff;
        border: 1px solid #e8ecef;
        border-radius: 12px;
        overflow: hidden;
        box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
        transition: all 0.3s ease;

        &:hover {
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.12);
        }
    }

    .form-header {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        padding: 1rem 1.5rem;
        border-bottom: 1px solid #e8ecef;

        .form-title {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 1rem;
            font-weight: 600;
            color: #495057;

            i {
                color: #28a745;
                font-size: 0.9rem;
            }
        }
    }

    .form-content {
        padding: 1.5rem;
    }

    .input-row {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 1rem;
        margin-bottom: 1.5rem;

        @media (max-width: 768px) {
            grid-template-columns: 1fr;
        }
    }

    .input-group {
        display: flex;
        flex-direction: column;

        &.full-width {
            grid-column: 1 / -1;
        }
    }

    .input-label {
        font-size: 0.875rem;
        font-weight: 500;
        color: #374151;
        margin-bottom: 0.5rem;

        .required {
            color: #ef4444;
            margin-left: 0.125rem;
        }
    }

    .input-field {
        padding: 0.75rem 1rem;
        border: 1px solid #d1d5db;
        border-radius: 8px;
        font-size: 0.875rem;
        transition: all 0.2s ease;
        background: #ffffff;

        &:focus {
            outline: none;
            border-color: #28a745;
            box-shadow: 0 0 0 3px rgba(40, 167, 69, 0.1);
        }

        &::placeholder {
            color: #9ca3af;
        }
    }

    .items-selector {
        border: 1px solid #d1d5db;
        border-radius: 8px;
        background: #f9fafb;
        max-height: 200px;
        overflow-y: auto;
    }

    .empty-items {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 0.5rem;
        padding: 2rem;
        color: #6b7280;
        font-size: 0.875rem;

        i {
            color: #9ca3af;
        }
    }

    .item-option {
        border-bottom: 1px solid #e5e7eb;

        &:last-child {
            border-bottom: none;
        }

        &:hover {
            background: #f3f4f6;
        }
    }

    .item-label {
        display: flex;
        align-items: flex-start;
        gap: 0.75rem;
        padding: 0.875rem 1rem;
        cursor: pointer;
        margin: 0;
        width: 100%;
    }

    .item-checkbox {
        margin: 0;
        margin-top: 0.125rem;
        width: 1rem;
        height: 1rem;
        accent-color: #28a745;
    }

    .item-content {
        flex: 1;
        min-width: 0;
    }

    .item-title {
        font-size: 0.875rem;
        font-weight: 500;
        color: #374151;
        margin-bottom: 0.25rem;
        line-height: 1.4;
    }

    .item-desc {
        font-size: 0.8rem;
        color: #6b7280;
        line-height: 1.3;
    }

    .form-actions {
        display: flex;
        justify-content: flex-end;
        gap: 0.75rem;
        margin-top: 1.5rem;
        padding-top: 1.5rem;
        border-top: 1px solid #e5e7eb;
    }

    .btn-cancel,
    .btn-save {
        padding: 0.625rem 1.25rem;
        border-radius: 8px;
        font-size: 0.875rem;
        font-weight: 500;
        border: none;
        cursor: pointer;
        transition: all 0.2s ease;
        min-width: 80px;
    }

    .btn-cancel {
        background: #f3f4f6;
        color: #374151;

        &:hover {
            background: #e5e7eb;
            transform: translateY(-1px);
        }
    }

    .btn-save {
        background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        color: white;

        &:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(40, 167, 69, 0.25);
        }

        &:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }
    }
}

// 分頁控制器樣式
.pagination-container {
    .pagination {
        .page-item {
            .page-link {
                border: 1px solid #dee2e6;
                color: #6c757d;
                padding: 0.375rem 0.75rem;
                font-size: 0.875rem;
                transition: all 0.2s ease;
                border-radius: 4px;
                margin: 0 0.125rem;

                &:hover {
                    background-color: #e9ecef;
                    border-color: #adb5bd;
                    color: #495057;
                    transform: translateY(-1px);
                }

                &:focus {
                    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
                }

                i {
                    font-size: 0.75rem;
                }
            }

            &.active .page-link {
                background-color: #007bff;
                border-color: #007bff;
                color: white;
                font-weight: 600;
                box-shadow: 0 2px 4px rgba(0, 123, 255, 0.3);
            }

            &.disabled .page-link {
                color: #6c757d;
                background-color: #fff;
                border-color: #dee2e6;
                opacity: 0.5;
                cursor: not-allowed;

                &:hover {
                    transform: none;
                }
            }
        }
    }
}

// 詳情分頁樣式
.detail-pagination {
    .pagination {
        .page-item {
            .page-link {
                padding: 0.25rem 0.5rem;
                font-size: 0.8rem;
                border-radius: 3px;
                margin: 0 0.0625rem;

                &:hover {
                    background-color: #f8f9fa;
                }
            }

            &.active .page-link {
                background-color: #28a745;
                border-color: #28a745;
                box-shadow: 0 2px 4px rgba(40, 167, 69, 0.3);
            }
        }
    }
}