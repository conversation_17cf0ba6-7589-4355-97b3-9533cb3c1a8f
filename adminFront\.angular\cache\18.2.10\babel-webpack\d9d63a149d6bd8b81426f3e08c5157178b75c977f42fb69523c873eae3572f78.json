{"ast": null, "code": "//! moment.js locale configuration\n//! locale : Ukrainian [uk]\n//! author : zemlanin : https://github.com/zemlanin\n//! Author : <PERSON><PERSON><PERSON> : https://github.com/Oire\n\n;\n(function (global, factory) {\n  typeof exports === 'object' && typeof module !== 'undefined' && typeof require === 'function' ? factory(require('../moment')) : typeof define === 'function' && define.amd ? define(['../moment'], factory) : factory(global.moment);\n})(this, function (moment) {\n  'use strict';\n\n  //! moment.js locale configuration\n  function plural(word, num) {\n    var forms = word.split('_');\n    return num % 10 === 1 && num % 100 !== 11 ? forms[0] : num % 10 >= 2 && num % 10 <= 4 && (num % 100 < 10 || num % 100 >= 20) ? forms[1] : forms[2];\n  }\n  function relativeTimeWithPlural(number, withoutSuffix, key) {\n    var format = {\n      ss: withoutSuffix ? 'секунда_секунди_секунд' : 'секунду_секунди_секунд',\n      mm: withoutSuffix ? 'хвилина_хвилини_хвилин' : 'хвилину_хвилини_хвилин',\n      hh: withoutSuffix ? 'година_години_годин' : 'годину_години_годин',\n      dd: 'день_дні_днів',\n      MM: 'місяць_місяці_місяців',\n      yy: 'рік_роки_років'\n    };\n    if (key === 'm') {\n      return withoutSuffix ? 'хвилина' : 'хвилину';\n    } else if (key === 'h') {\n      return withoutSuffix ? 'година' : 'годину';\n    } else {\n      return number + ' ' + plural(format[key], +number);\n    }\n  }\n  function weekdaysCaseReplace(m, format) {\n    var weekdays = {\n        nominative: 'неділя_понеділок_вівторок_середа_четвер_п’ятниця_субота'.split('_'),\n        accusative: 'неділю_понеділок_вівторок_середу_четвер_п’ятницю_суботу'.split('_'),\n        genitive: 'неділі_понеділка_вівторка_середи_четверга_п’ятниці_суботи'.split('_')\n      },\n      nounCase;\n    if (m === true) {\n      return weekdays['nominative'].slice(1, 7).concat(weekdays['nominative'].slice(0, 1));\n    }\n    if (!m) {\n      return weekdays['nominative'];\n    }\n    nounCase = /(\\[[ВвУу]\\]) ?dddd/.test(format) ? 'accusative' : /\\[?(?:минулої|наступної)? ?\\] ?dddd/.test(format) ? 'genitive' : 'nominative';\n    return weekdays[nounCase][m.day()];\n  }\n  function processHoursFunction(str) {\n    return function () {\n      return str + 'о' + (this.hours() === 11 ? 'б' : '') + '] LT';\n    };\n  }\n  var uk = moment.defineLocale('uk', {\n    months: {\n      format: 'січня_лютого_березня_квітня_травня_червня_липня_серпня_вересня_жовтня_листопада_грудня'.split('_'),\n      standalone: 'січень_лютий_березень_квітень_травень_червень_липень_серпень_вересень_жовтень_листопад_грудень'.split('_')\n    },\n    monthsShort: 'січ_лют_бер_квіт_трав_черв_лип_серп_вер_жовт_лист_груд'.split('_'),\n    weekdays: weekdaysCaseReplace,\n    weekdaysShort: 'нд_пн_вт_ср_чт_пт_сб'.split('_'),\n    weekdaysMin: 'нд_пн_вт_ср_чт_пт_сб'.split('_'),\n    longDateFormat: {\n      LT: 'HH:mm',\n      LTS: 'HH:mm:ss',\n      L: 'DD.MM.YYYY',\n      LL: 'D MMMM YYYY р.',\n      LLL: 'D MMMM YYYY р., HH:mm',\n      LLLL: 'dddd, D MMMM YYYY р., HH:mm'\n    },\n    calendar: {\n      sameDay: processHoursFunction('[Сьогодні '),\n      nextDay: processHoursFunction('[Завтра '),\n      lastDay: processHoursFunction('[Вчора '),\n      nextWeek: processHoursFunction('[У] dddd ['),\n      lastWeek: function () {\n        switch (this.day()) {\n          case 0:\n          case 3:\n          case 5:\n          case 6:\n            return processHoursFunction('[Минулої] dddd [').call(this);\n          case 1:\n          case 2:\n          case 4:\n            return processHoursFunction('[Минулого] dddd [').call(this);\n        }\n      },\n      sameElse: 'L'\n    },\n    relativeTime: {\n      future: 'за %s',\n      past: '%s тому',\n      s: 'декілька секунд',\n      ss: relativeTimeWithPlural,\n      m: relativeTimeWithPlural,\n      mm: relativeTimeWithPlural,\n      h: 'годину',\n      hh: relativeTimeWithPlural,\n      d: 'день',\n      dd: relativeTimeWithPlural,\n      M: 'місяць',\n      MM: relativeTimeWithPlural,\n      y: 'рік',\n      yy: relativeTimeWithPlural\n    },\n    // M. E.: those two are virtually unused but a user might want to implement them for his/her website for some reason\n    meridiemParse: /ночі|ранку|дня|вечора/,\n    isPM: function (input) {\n      return /^(дня|вечора)$/.test(input);\n    },\n    meridiem: function (hour, minute, isLower) {\n      if (hour < 4) {\n        return 'ночі';\n      } else if (hour < 12) {\n        return 'ранку';\n      } else if (hour < 17) {\n        return 'дня';\n      } else {\n        return 'вечора';\n      }\n    },\n    dayOfMonthOrdinalParse: /\\d{1,2}-(й|го)/,\n    ordinal: function (number, period) {\n      switch (period) {\n        case 'M':\n        case 'd':\n        case 'DDD':\n        case 'w':\n        case 'W':\n          return number + '-й';\n        case 'D':\n          return number + '-го';\n        default:\n          return number;\n      }\n    },\n    week: {\n      dow: 1,\n      // Monday is the first day of the week.\n      doy: 7 // The week that contains Jan 7th is the first week of the year.\n    }\n  });\n  return uk;\n});", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}