{"ast": null, "code": "import requiredArgs from \"../_lib/requiredArgs/index.js\";\nimport getDay from \"../getDay/index.js\";\nimport subDays from \"../subDays/index.js\";\n/**\n * @name previousDay\n * @category Weekday Helpers\n * @summary When is the previous day of the week?\n *\n * @description\n * When is the previous day of the week? 0-6 the day of the week, 0 represents Sunday.\n *\n * @param {Date | number} date - the date to check\n * @param {number} day - day of the week\n * @returns {Date} - the date is the previous day of week\n * @throws {TypeError} - 2 arguments required\n *\n * @example\n * // When is the previous Monday before Mar, 20, 2020?\n * const result = previousDay(new Date(2020, 2, 20), 1)\n * //=> Mon Mar 16 2020 00:00:00\n *\n * @example\n * // When is the previous Tuesday before Mar, 21, 2020?\n * const result = previousDay(new Date(2020, 2, 21), 2)\n * //=> Tue Mar 17 2020 00:00:00\n */\nexport default function previousDay(date, day) {\n  requiredArgs(2, arguments);\n  var delta = getDay(date) - day;\n  if (delta <= 0) delta += 7;\n  return subDays(date, delta);\n}", "map": {"version": 3, "names": ["requiredArgs", "getDay", "subDays", "previousDay", "date", "day", "arguments", "delta"], "sources": ["C:/Users/<USER>/Documents/jeansalechange/adminFront/node_modules/date-fns/esm/previousDay/index.js"], "sourcesContent": ["import requiredArgs from \"../_lib/requiredArgs/index.js\";\nimport getDay from \"../getDay/index.js\";\nimport subDays from \"../subDays/index.js\";\n/**\n * @name previousDay\n * @category Weekday Helpers\n * @summary When is the previous day of the week?\n *\n * @description\n * When is the previous day of the week? 0-6 the day of the week, 0 represents Sunday.\n *\n * @param {Date | number} date - the date to check\n * @param {number} day - day of the week\n * @returns {Date} - the date is the previous day of week\n * @throws {TypeError} - 2 arguments required\n *\n * @example\n * // When is the previous Monday before Mar, 20, 2020?\n * const result = previousDay(new Date(2020, 2, 20), 1)\n * //=> Mon Mar 16 2020 00:00:00\n *\n * @example\n * // When is the previous Tuesday before Mar, 21, 2020?\n * const result = previousDay(new Date(2020, 2, 21), 2)\n * //=> Tue Mar 17 2020 00:00:00\n */\nexport default function previousDay(date, day) {\n  requiredArgs(2, arguments);\n  var delta = getDay(date) - day;\n  if (delta <= 0) delta += 7;\n  return subDays(date, delta);\n}"], "mappings": "AAAA,OAAOA,YAAY,MAAM,+BAA+B;AACxD,OAAOC,MAAM,MAAM,oBAAoB;AACvC,OAAOC,OAAO,MAAM,qBAAqB;AACzC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,SAASC,WAAWA,CAACC,IAAI,EAAEC,GAAG,EAAE;EAC7CL,YAAY,CAAC,CAAC,EAAEM,SAAS,CAAC;EAC1B,IAAIC,KAAK,GAAGN,MAAM,CAACG,IAAI,CAAC,GAAGC,GAAG;EAC9B,IAAIE,KAAK,IAAI,CAAC,EAAEA,KAAK,IAAI,CAAC;EAC1B,OAAOL,OAAO,CAACE,IAAI,EAAEG,KAAK,CAAC;AAC7B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}