{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { concatMap, of, tap } from 'rxjs';\nimport { SharedModule } from '../../components/shared.module';\nimport { BaseComponent } from '../../components/base/baseComponent';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/shared/helper/allowHelper\";\nimport * as i2 from \"@nebular/theme\";\nimport * as i3 from \"src/app/shared/helper/validationHelper\";\nimport * as i4 from \"src/services/api/services\";\nimport * as i5 from \"src/app/shared/services/utility.service\";\nimport * as i6 from \"src/app/shared/services/message.service\";\nimport * as i7 from \"@angular/common\";\nimport * as i8 from \"@angular/forms\";\nimport * as i9 from \"../../components/breadcrumb/breadcrumb.component\";\nimport * as i10 from \"../../components/pagination/pagination.component\";\nimport * as i11 from \"../../../@theme/pipes/base-file.pipe\";\nfunction SchematicPictureComponent_nb_option_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 19);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const buildCase_r2 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", buildCase_r2.cID);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", buildCase_r2.CBuildCaseName, \" \");\n  }\n}\nfunction SchematicPictureComponent_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 20);\n    i0.ɵɵlistener(\"click\", function SchematicPictureComponent_div_14_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r3 = i0.ɵɵnextContext();\n      const dialog_r5 = i0.ɵɵreference(39);\n      return i0.ɵɵresetView(ctx_r3.addNew(dialog_r5));\n    });\n    i0.ɵɵelementStart(1, \"button\", 21);\n    i0.ɵɵtext(2, \" \\u5716\\u7247\\u4E0A\\u50B3\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction SchematicPictureComponent_tr_35_button_17_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 24);\n    i0.ɵɵlistener(\"click\", function SchematicPictureComponent_tr_35_button_17_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const item_r7 = i0.ɵɵnextContext().$implicit;\n      const ctx_r3 = i0.ɵɵnextContext();\n      const dialog_r5 = i0.ɵɵreference(39);\n      return i0.ɵɵresetView(ctx_r3.addNew(dialog_r5, item_r7));\n    });\n    i0.ɵɵtext(1, \"\\u6AA2\\u8996\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SchematicPictureComponent_tr_35_button_18_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 24);\n    i0.ɵɵlistener(\"click\", function SchematicPictureComponent_tr_35_button_18_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const item_r7 = i0.ɵɵnextContext().$implicit;\n      const ctx_r3 = i0.ɵɵnextContext();\n      const dialog_r5 = i0.ɵɵreference(39);\n      return i0.ɵɵresetView(ctx_r3.changePicture(dialog_r5, item_r7));\n    });\n    i0.ɵɵtext(1, \"\\u6539\\u8B8A\\u5716\\u7247\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SchematicPictureComponent_tr_35_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"td\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"td\");\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"td\");\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"td\");\n    i0.ɵɵtext(14);\n    i0.ɵɵpipe(15, \"date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"td\", 22);\n    i0.ɵɵtemplate(17, SchematicPictureComponent_tr_35_button_17_Template, 2, 0, \"button\", 23)(18, SchematicPictureComponent_tr_35_button_18_Template, 2, 0, \"button\", 23);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const item_r7 = ctx.$implicit;\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r7.CId);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r7.CName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r7.CPart);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r7.CLocation);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r7.CSelectName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r7.CPictureCode);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(15, 9, item_r7.CUpdateDT, \"yyyy/MM/dd HH:mm:ss\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.isRead);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.isRead);\n  }\n}\nfunction SchematicPictureComponent_ng_template_38_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 31);\n    i0.ɵɵelement(1, \"img\", 32);\n    i0.ɵɵpipe(2, \"addBaseFile\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"src\", i0.ɵɵpipeBind1(2, 1, ctx_r3.currentImageShowing), i0.ɵɵsanitizeUrl);\n  }\n}\nfunction SchematicPictureComponent_ng_template_38_ng_template_6_tr_14_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\")(2, \"input\", 38);\n    i0.ɵɵlistener(\"blur\", function SchematicPictureComponent_ng_template_38_ng_template_6_tr_14_Template_input_blur_2_listener($event) {\n      const i_r13 = i0.ɵɵrestoreView(_r12).index;\n      const ctx_r3 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r3.renameFile($event, i_r13));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(3, \"td\", 39);\n    i0.ɵɵelement(4, \"img\", 32);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\", 22)(6, \"button\", 40);\n    i0.ɵɵlistener(\"click\", function SchematicPictureComponent_ng_template_38_ng_template_6_tr_14_Template_button_click_6_listener() {\n      const picture_r14 = i0.ɵɵrestoreView(_r12).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r3.removeImage(picture_r14.id));\n    });\n    i0.ɵɵtext(7, \"\\u5220\\u9664\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const picture_r14 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"value\", picture_r14.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", picture_r14.data, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction SchematicPictureComponent_ng_template_38_ng_template_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 33);\n    i0.ɵɵlistener(\"click\", function SchematicPictureComponent_ng_template_38_ng_template_6_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const inputFile_r11 = i0.ɵɵreference(3);\n      return i0.ɵɵresetView(inputFile_r11.click());\n    });\n    i0.ɵɵtext(1, \"\\u5716\\u7247\\u4E0A\\u50B3\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(2, \"input\", 34, 2);\n    i0.ɵɵlistener(\"change\", function SchematicPictureComponent_ng_template_38_ng_template_6_Template_input_change_2_listener($event) {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r3.detectFiles($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 35)(5, \"table\", 36)(6, \"thead\")(7, \"tr\", 14)(8, \"th\", 37);\n    i0.ɵɵtext(9, \"\\u6587\\u4EF6\\u540D\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"th\", 15);\n    i0.ɵɵtext(11, \"\\u6AA2\\u8996\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(12, \"th\", 15);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"tbody\");\n    i0.ɵɵtemplate(14, SchematicPictureComponent_ng_template_38_ng_template_6_tr_14_Template, 8, 2, \"tr\", 16);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassMap(!ctx_r3.isEdit ? \"btn btn-info\" : ctx_r3.listPictures.length < 1 ? \"btn btn-info\" : \"btn btn-info disable\");\n    i0.ɵɵadvance(14);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.listPictures);\n  }\n}\nfunction SchematicPictureComponent_ng_template_38_button_12_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r16 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 41);\n    i0.ɵɵlistener(\"click\", function SchematicPictureComponent_ng_template_38_button_12_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r16);\n      const ref_r15 = i0.ɵɵnextContext().dialogRef;\n      const ctx_r3 = i0.ɵɵnextContext();\n      const dialog_r5 = i0.ɵɵreference(39);\n      ctx_r3.uploadImage(dialog_r5);\n      return i0.ɵɵresetView(ref_r15.close());\n    });\n    i0.ɵɵtext(1, \"\\u5132\\u5B58\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SchematicPictureComponent_ng_template_38_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-card\", 25)(1, \"nb-card-header\")(2, \"span\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"nb-card-body\", 26);\n    i0.ɵɵtemplate(5, SchematicPictureComponent_ng_template_38_div_5_Template, 3, 3, \"div\", 27)(6, SchematicPictureComponent_ng_template_38_ng_template_6_Template, 15, 3, \"ng-template\", null, 1, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"nb-card-footer\")(9, \"div\", 28)(10, \"button\", 29);\n    i0.ɵɵlistener(\"click\", function SchematicPictureComponent_ng_template_38_Template_button_click_10_listener() {\n      const ref_r15 = i0.ɵɵrestoreView(_r9).dialogRef;\n      const ctx_r3 = i0.ɵɵnextContext();\n      ref_r15.close();\n      return i0.ɵɵresetView(ctx_r3.currentImageShowing = \"\");\n    });\n    i0.ɵɵtext(11, \"\\u53D6\\u6D88\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(12, SchematicPictureComponent_ng_template_38_button_12_Template, 2, 0, \"button\", 30);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const upload_r17 = i0.ɵɵreference(7);\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.currentImageShowing ? \"\\u6AA2\\u8996\" : \"\\u5716\\u7247\\u4E0A\\u50B3\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.currentImageShowing)(\"ngIfElse\", upload_r17);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r3.currentImageShowing);\n  }\n}\nexport let SchematicPictureComponent = /*#__PURE__*/(() => {\n  class SchematicPictureComponent extends BaseComponent {\n    constructor(_allow, dialogService, valid, _infoPictureService, _buildCaseService, _pictureService, _utilityService, message) {\n      super(_allow);\n      this._allow = _allow;\n      this.dialogService = dialogService;\n      this.valid = valid;\n      this._infoPictureService = _infoPictureService;\n      this._buildCaseService = _buildCaseService;\n      this._pictureService = _pictureService;\n      this._utilityService = _utilityService;\n      this.message = message;\n      this.images = [];\n      this.listUserBuildCases = [];\n      this.currentImageShowing = \"\";\n      this.listPictures = [];\n      this.isEdit = false;\n    }\n    ngOnInit() {\n      this.getListBuildCase();\n    }\n    getListBuildCase() {\n      this._buildCaseService.apiBuildCaseGetUserBuildCasePost$Json({}).pipe(tap(res => {\n        if (res.StatusCode === 0) {\n          this.listUserBuildCases = res.Entries ?? [];\n          this.selectedBuildCaseId = this.listUserBuildCases[0].cID;\n        }\n      }), concatMap(() => this.getInfoPicturelList(1))).subscribe();\n    }\n    getInfoPicturelList(pageIndex) {\n      return this._infoPictureService.apiInfoPictureGetInfoPicturelListPost$Json({\n        body: {\n          PageIndex: pageIndex,\n          PageSize: this.pageSize,\n          CBuildCaseId: this.selectedBuildCaseId\n        }\n      }).pipe(tap(res => {\n        if (res.StatusCode == 0) {\n          this.images = res.Entries ?? [];\n          this.totalRecords = res.TotalItems;\n        }\n      }));\n    }\n    pageChanged(newPage) {\n      this.getInfoPicturelList(newPage).subscribe();\n    }\n    selectedChange(buildCaseId) {\n      this.selectedBuildCaseId = buildCaseId;\n      this.getInfoPicturelList(1).subscribe();\n    }\n    addNew(ref, item) {\n      this.dialogService.open(ref);\n      this.isEdit = false;\n      if (!!item) {\n        this.currentImageShowing = item.CFile;\n      } else {\n        this.listPictures = [];\n      }\n    }\n    changePicture(ref, item) {\n      if (!!item && item.CId) {\n        this.dialogService.open(ref);\n        this.isEdit = true;\n        this.currentEditItem = item.CId;\n        this.listPictures = [];\n      }\n    }\n    validation() {\n      this.valid.clear();\n    }\n    onSubmit(ref) {}\n    detectFiles(event) {\n      for (let index = 0; index < event.target.files.length; index++) {\n        const file = event.target.files[index];\n        if (file) {\n          let reader = new FileReader();\n          reader.readAsDataURL(file);\n          reader.onload = () => {\n            let base64Str = reader.result;\n            if (!base64Str) {\n              return;\n            }\n            // Get name file ( no extension)\n            const fileNameWithoutExtension = file.name.split('.')[0];\n            // Find files with duplicate names\n            const existingFileIndex = this.listPictures.findIndex(picture => picture.name === fileNameWithoutExtension);\n            if (existingFileIndex !== -1) {\n              // If name is duplicate, update file data\n              this.listPictures[existingFileIndex] = {\n                ...this.listPictures[existingFileIndex],\n                data: base64Str,\n                CFile: file,\n                extension: this._utilityService.getFileExtension(file.name)\n              };\n            } else {\n              // If not duplicate, add new file\n              file.id = new Date().getTime();\n              this.listPictures.push({\n                id: new Date().getTime(),\n                name: fileNameWithoutExtension,\n                data: base64Str,\n                extension: this._utilityService.getFileExtension(file.name),\n                CFile: file\n              });\n            }\n            // Reset input file to be able to select the old file again\n            event.target.value = null;\n          };\n        }\n      }\n    }\n    removeImage(pictureId) {\n      this.listPictures = this.listPictures.filter(x => x.id != pictureId);\n    }\n    uploadImage(ref) {\n      if (!this.isEdit) {\n        this._infoPictureService.apiInfoPictureUploadListInfoPicturePost$Json({\n          body: {\n            CBuildCaseId: this.selectedBuildCaseId,\n            CPath: \"infoPicture\",\n            CFile: this.listPictures.map(x => x.CFile)\n          }\n        }).pipe(tap(res => {\n          if (res.StatusCode == 0) {\n            this.message.showSucessMSG('執行成功');\n            this.listPictures = [];\n          } else {\n            this.message.showErrorMSG(res.Message);\n          }\n          ref.close();\n        }), concatMap(res => res.StatusCode == 0 ? this.getInfoPicturelList(1) : of(null))).subscribe();\n      } else {\n        if (this.listPictures.length > 0 && this.listPictures[0].CFile) {\n          this._infoPictureService.apiInfoPictureUpdateInfoPicturePost$Json({\n            body: {\n              CBuildCaseId: this.selectedBuildCaseId,\n              CInfoPictureID: this.currentEditItem,\n              CFile: this.listPictures[0].CFile\n            }\n          }).pipe(tap(res => {\n            if (res.StatusCode == 0) {\n              this.message.showSucessMSG('執行成功');\n              this.listPictures = [];\n            } else {\n              this.message.showErrorMSG(res.Message);\n            }\n            ref.close();\n          }), concatMap(res => res.StatusCode == 0 ? this.getInfoPicturelList(1) : of(null))).subscribe();\n        }\n      }\n    }\n    renameFile(event, index) {\n      var blob = this.listPictures[index].CFile.slice(0, this.listPictures[index].CFile.size, this.listPictures[index].CFile.type);\n      var newFile = new File([blob], `${event.target.value + '.' + this.listPictures[index].extension}`, {\n        type: this.listPictures[index].CFile.type\n      });\n      this.listPictures[index].CFile = newFile;\n    }\n    static {\n      this.ɵfac = function SchematicPictureComponent_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || SchematicPictureComponent)(i0.ɵɵdirectiveInject(i1.AllowHelper), i0.ɵɵdirectiveInject(i2.NbDialogService), i0.ɵɵdirectiveInject(i3.ValidationHelper), i0.ɵɵdirectiveInject(i4.InfoPictureService), i0.ɵɵdirectiveInject(i4.BuildCaseService), i0.ɵɵdirectiveInject(i4.PictureService), i0.ɵɵdirectiveInject(i5.UtilityService), i0.ɵɵdirectiveInject(i6.MessageService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: SchematicPictureComponent,\n        selectors: [[\"ngx-schematic-picture\"]],\n        standalone: true,\n        features: [i0.ɵɵInheritDefinitionFeature, i0.ɵɵStandaloneFeature],\n        decls: 40,\n        vars: 7,\n        consts: [[\"dialog\", \"\"], [\"upload\", \"\"], [\"inputFile\", \"\"], [\"accent\", \"success\"], [1, \"font-bold\", \"text-[#818181]\"], [1, \"d-flex\", \"flex-wrap\"], [1, \"col-md-6\"], [1, \"form-group\", \"d-flex\", \"align-items-center\", \"w-full\"], [\"for\", \"buildingName\", 1, \"label\", \"mr-2\"], [\"placeholder\", \"\\u5EFA\\u6848\", 1, \"w-full\", 3, \"ngModelChange\", \"selectedChange\", \"ngModel\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"d-flex justify-content-end w-full\", 3, \"click\", 4, \"ngIf\"], [1, \"table-responsive\", \"mt-4\"], [1, \"table\", \"table-striped\", \"border\", 2, \"min-width\", \"1000px\", \"background-color\", \"#f3f3f3\"], [2, \"background-color\", \"#27ae60\", \"color\", \"white\"], [\"scope\", \"col\", 1, \"col-1\"], [4, \"ngFor\", \"ngForOf\"], [1, \"d-flex\", \"justify-content-center\"], [\"aria-label\", \"Pagination\", 3, \"PageChange\", \"PageSizeChange\", \"CollectionSizeChange\", \"Page\", \"PageSize\", \"CollectionSize\"], [3, \"value\"], [1, \"d-flex\", \"justify-content-end\", \"w-full\", 3, \"click\"], [1, \"btn\", \"btn-info\"], [1, \"text-center\", \"w-32\"], [\"class\", \"btn btn-outline-primary btn-sm m-1\", 3, \"click\", 4, \"ngIf\"], [1, \"btn\", \"btn-outline-primary\", \"btn-sm\", \"m-1\", 3, \"click\"], [1, \"mr-md-5\", \"ml-md-5\", 2, \"height\", \"100%\", \"overflow\", \"auto\", \"width\", \"700px\"], [2, \"padding\", \"1rem 2rem\"], [\"class\", \"w-full h-auto\", 4, \"ngIf\", \"ngIfElse\"], [1, \"flex\", \"justify-center\", \"items-center\"], [1, \"btn\", \"btn-danger\", \"mr-2\", 3, \"click\"], [\"class\", \"btn btn-success\", 3, \"click\", 4, \"ngIf\"], [1, \"w-full\", \"h-auto\"], [1, \"fit-size\", 3, \"src\"], [3, \"click\"], [\"type\", \"file\", \"accept\", \"image/png, image/gif, image/jpeg\", \"multiple\", \"\", 1, \"hidden\", 3, \"change\"], [1, \"mt-3\", \"w-full\", \"flex\", \"flex-col\"], [1, \"table\", \"table-striped\", \"border\", 2, \"background-color\", \"#f3f3f3\"], [\"scope\", \"col\", 1, \"col-4\"], [\"nbInput\", \"\", \"type\", \"text\", 1, \"w-100\", \"p-2\", \"text-[13px]\", 3, \"blur\", \"value\"], [1, \"w-[100px]\", \"h-auto\"], [1, \"btn\", \"btn-outline-danger\", \"btn-sm\", \"m-1\", 3, \"click\"], [1, \"btn\", \"btn-success\", 3, \"click\"]],\n        template: function SchematicPictureComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            const _r1 = i0.ɵɵgetCurrentView();\n            i0.ɵɵelementStart(0, \"nb-card\", 3)(1, \"nb-card-header\");\n            i0.ɵɵelement(2, \"ngx-breadcrumb\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(3, \"nb-card-body\")(4, \"h1\", 4);\n            i0.ɵɵtext(5, \"\\u53EF\\u8A2D\\u5B9A\\u4E0A\\u50B3\\u5EFA\\u6750\\u793A\\u610F\\u5716\\u7247\\uFF0C\\u4E0A\\u50B3\\u524D\\u8ACB\\u5C07\\u5716\\u7247\\u6A94\\u6848\\u6539\\u70BA\\u793A\\u610F\\u5716\\u7247\\u6A94\\u540D\\u3002\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(6, \"div\", 5)(7, \"div\", 6)(8, \"div\", 7)(9, \"label\", 8);\n            i0.ɵɵtext(10, \"\\u5EFA\\u6848\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(11, \"nb-select\", 9);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function SchematicPictureComponent_Template_nb_select_ngModelChange_11_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.selectedBuildCaseId, $event) || (ctx.selectedBuildCaseId = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵlistener(\"selectedChange\", function SchematicPictureComponent_Template_nb_select_selectedChange_11_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.selectedChange($event));\n            });\n            i0.ɵɵtemplate(12, SchematicPictureComponent_nb_option_12_Template, 2, 2, \"nb-option\", 10);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(13, \"div\", 6);\n            i0.ɵɵtemplate(14, SchematicPictureComponent_div_14_Template, 3, 0, \"div\", 11);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(15, \"div\", 12)(16, \"table\", 13)(17, \"thead\")(18, \"tr\", 14)(19, \"th\", 15);\n            i0.ɵɵtext(20, \"\\u9805\\u6B21\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(21, \"th\", 15);\n            i0.ɵɵtext(22, \"\\u540D\\u7A31\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(23, \"th\", 15);\n            i0.ɵɵtext(24, \"\\u9805\\u76EE\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(25, \"th\", 15);\n            i0.ɵɵtext(26, \"\\u4F4D\\u7F6E\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(27, \"th\", 15);\n            i0.ɵɵtext(28, \"\\u5EFA\\u6750\\u9078\\u9805\\u540D\\u7A31\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(29, \"th\", 15);\n            i0.ɵɵtext(30, \"\\u793A\\u610F\\u5716\\u7247\\u6A94\\u540D\\uFF08\\u76F8\\u540C\\u5EFA\\u6848\\u4E0D\\u53EF\\u91CD\\u8907\\uFF09\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(31, \"th\", 15);\n            i0.ɵɵtext(32, \"\\u6700\\u65B0\\u5716\\u7247\\u4E0A\\u50B3\\u6642\\u9593\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(33, \"th\", 15);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(34, \"tbody\");\n            i0.ɵɵtemplate(35, SchematicPictureComponent_tr_35_Template, 19, 12, \"tr\", 16);\n            i0.ɵɵelementEnd()()()();\n            i0.ɵɵelementStart(36, \"nb-card-footer\", 17)(37, \"ngx-pagination\", 18);\n            i0.ɵɵtwoWayListener(\"PageChange\", function SchematicPictureComponent_Template_ngx_pagination_PageChange_37_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.pageIndex, $event) || (ctx.pageIndex = $event);\n              return i0.ɵɵresetView($event);\n            })(\"PageSizeChange\", function SchematicPictureComponent_Template_ngx_pagination_PageSizeChange_37_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.pageSize, $event) || (ctx.pageSize = $event);\n              return i0.ɵɵresetView($event);\n            })(\"CollectionSizeChange\", function SchematicPictureComponent_Template_ngx_pagination_CollectionSizeChange_37_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.totalRecords, $event) || (ctx.totalRecords = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵlistener(\"PageChange\", function SchematicPictureComponent_Template_ngx_pagination_PageChange_37_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.pageChanged($event));\n            });\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵtemplate(38, SchematicPictureComponent_ng_template_38_Template, 13, 4, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor);\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(11);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.selectedBuildCaseId);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngForOf\", ctx.listUserBuildCases);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"ngIf\", ctx.isCreate);\n            i0.ɵɵadvance(21);\n            i0.ɵɵproperty(\"ngForOf\", ctx.images);\n            i0.ɵɵadvance(2);\n            i0.ɵɵtwoWayProperty(\"Page\", ctx.pageIndex)(\"PageSize\", ctx.pageSize)(\"CollectionSize\", ctx.totalRecords);\n          }\n        },\n        dependencies: [CommonModule, i7.NgForOf, i7.NgIf, i7.DatePipe, SharedModule, i8.NgControlStatus, i8.NgModel, i2.NbCardComponent, i2.NbCardBodyComponent, i2.NbCardFooterComponent, i2.NbCardHeaderComponent, i2.NbInputDirective, i2.NbSelectComponent, i2.NbOptionComponent, i9.BreadcrumbComponent, i10.PaginationComponent, i11.BaseFilePipe],\n        styles: [\".disable[_ngcontent-%COMP%]{opacity:.5;pointer-events:none}\"]\n      });\n    }\n  }\n  return SchematicPictureComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}