{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { finalize, mergeMap, tap } from 'rxjs';\nimport * as XLSX from 'xlsx';\nimport { SharedModule } from '../../components/shared.module';\nimport { BaseComponent } from '../../components/base/baseComponent';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/shared/helper/allowHelper\";\nimport * as i2 from \"@nebular/theme\";\nimport * as i3 from \"src/app/shared/services/message.service\";\nimport * as i4 from \"src/app/shared/helper/validationHelper\";\nimport * as i5 from \"src/services/api/services\";\nimport * as i6 from \"src/app/shared/services/utility.service\";\nimport * as i7 from \"@angular/common\";\nimport * as i8 from \"@angular/forms\";\nimport * as i9 from \"../../components/breadcrumb/breadcrumb.component\";\nimport * as i10 from \"../../components/pagination/pagination.component\";\nfunction BuildingMaterialComponent_nb_option_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 34);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const buildCase_r2 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", buildCase_r2.cID);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", buildCase_r2.CBuildCaseName, \" \");\n  }\n}\nfunction BuildingMaterialComponent_button_22_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 35);\n    i0.ɵɵlistener(\"click\", function BuildingMaterialComponent_button_22_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.exportExelMaterialList());\n    });\n    i0.ɵɵtext(1, \"\\u532F\\u51FA \");\n    i0.ɵɵelement(2, \"i\", 23);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction BuildingMaterialComponent_button_23_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 36);\n    i0.ɵɵlistener(\"click\", function BuildingMaterialComponent_button_23_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.search());\n    });\n    i0.ɵɵtext(1, \" \\u67E5\\u8A62 \");\n    i0.ɵɵelement(2, \"i\", 37);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction BuildingMaterialComponent_button_24_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 38);\n    i0.ɵɵlistener(\"click\", function BuildingMaterialComponent_button_24_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r3 = i0.ɵɵnextContext();\n      const dialog_r7 = i0.ɵɵreference(54);\n      return i0.ɵɵresetView(ctx_r3.addNew(dialog_r7));\n    });\n    i0.ɵɵtext(1, \"\\u55AE\\u7B46\\u65B0\\u589E \");\n    i0.ɵɵelement(2, \"i\", 39);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction BuildingMaterialComponent_button_25_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 40);\n    i0.ɵɵlistener(\"click\", function BuildingMaterialComponent_button_25_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r8);\n      i0.ɵɵnextContext();\n      const inputFile_r9 = i0.ɵɵreference(27);\n      return i0.ɵɵresetView(inputFile_r9.click());\n    });\n    i0.ɵɵtext(1, \" \\u6279\\u6B21\\u532F\\u5165 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction BuildingMaterialComponent_th_47_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 27);\n    i0.ɵɵtext(1, \"\\u50F9\\u683C\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction BuildingMaterialComponent_tbody_50_tr_1_td_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r10 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(item_r10.CPrice);\n  }\n}\nfunction BuildingMaterialComponent_tbody_50_tr_1_button_15_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 45);\n    i0.ɵɵlistener(\"click\", function BuildingMaterialComponent_tbody_50_tr_1_button_15_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const item_r10 = i0.ɵɵnextContext().$implicit;\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      const dialog_r7 = i0.ɵɵreference(54);\n      return i0.ɵɵresetView(ctx_r3.onSelectedMaterial(item_r10, dialog_r7));\n    });\n    i0.ɵɵtext(1, \"\\u7DE8\\u8F2F\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction BuildingMaterialComponent_tbody_50_tr_1_button_16_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 46);\n    i0.ɵɵlistener(\"click\", function BuildingMaterialComponent_tbody_50_tr_1_button_16_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r12);\n      const item_r10 = i0.ɵɵnextContext().$implicit;\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      const imageBinder_r13 = i0.ɵɵreference(56);\n      return i0.ɵɵresetView(ctx_r3.bindImageForMaterial(item_r10, imageBinder_r13));\n    });\n    i0.ɵɵelement(1, \"i\", 47);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r10 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵproperty(\"title\", \"\\u70BA \" + item_r10.CName + \" \\u7D81\\u5B9A\\u5716\\u7247\");\n  }\n}\nfunction BuildingMaterialComponent_tbody_50_tr_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"td\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"td\");\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"td\");\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(13, BuildingMaterialComponent_tbody_50_tr_1_td_13_Template, 2, 1, \"td\", 31);\n    i0.ɵɵelementStart(14, \"td\", 42);\n    i0.ɵɵtemplate(15, BuildingMaterialComponent_tbody_50_tr_1_button_15_Template, 2, 0, \"button\", 43)(16, BuildingMaterialComponent_tbody_50_tr_1_button_16_Template, 2, 1, \"button\", 44);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const item_r10 = ctx.$implicit;\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r10.CId);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r10.CName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r10.CPart);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r10.CLocation);\n    i0.ɵɵadvance();\n    i0.ɵɵstyleMap(!item_r10.CIsMapping ? \"color: red\" : \"\");\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(item_r10.CSelectName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r10.CDescription);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", item_r10.CShowPrice == true);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.isRead);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.isRead);\n  }\n}\nfunction BuildingMaterialComponent_tbody_50_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tbody\");\n    i0.ɵɵtemplate(1, BuildingMaterialComponent_tbody_50_tr_1_Template, 17, 11, \"tr\", 41);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.materialList);\n  }\n}\nfunction BuildingMaterialComponent_ng_template_53_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r14 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-card\", 48)(1, \"nb-card-header\");\n    i0.ɵɵtext(2, \" \\u5EFA\\u6750\\u7BA1\\u7406 > \\u65B0\\u589E\\u5EFA\\u6750 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"nb-card-body\", 49)(4, \"h5\", 50);\n    i0.ɵɵtext(5, \"\\u8ACB\\u8F38\\u5165\\u4E0B\\u65B9\\u5167\\u5BB9\\u65B0\\u589E\\u5EFA\\u6750\\u3002\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 51)(7, \"div\", 52)(8, \"label\", 53);\n    i0.ɵɵtext(9, \"\\u540D\\u7A31\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"input\", 54);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function BuildingMaterialComponent_ng_template_53_Template_input_ngModelChange_10_listener($event) {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r3 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r3.selectedMaterial.CName, $event) || (ctx_r3.selectedMaterial.CName = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"div\", 52)(12, \"label\", 53);\n    i0.ɵɵtext(13, \"\\u9805\\u76EE\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"input\", 54);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function BuildingMaterialComponent_ng_template_53_Template_input_ngModelChange_14_listener($event) {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r3 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r3.selectedMaterial.CPart, $event) || (ctx_r3.selectedMaterial.CPart = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(15, \"div\", 52)(16, \"label\", 53);\n    i0.ɵɵtext(17, \"\\u4F4D\\u7F6E\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"input\", 54);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function BuildingMaterialComponent_ng_template_53_Template_input_ngModelChange_18_listener($event) {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r3 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r3.selectedMaterial.CLocation, $event) || (ctx_r3.selectedMaterial.CLocation = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(19, \"div\", 52)(20, \"label\", 53);\n    i0.ɵɵtext(21, \"\\u5EFA\\u6750\\u9078\\u9805\\u540D\\u7A31\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"input\", 54);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function BuildingMaterialComponent_ng_template_53_Template_input_ngModelChange_22_listener($event) {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r3 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r3.selectedMaterial.CSelectName, $event) || (ctx_r3.selectedMaterial.CSelectName = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(23, \"div\", 52)(24, \"label\", 55);\n    i0.ɵɵtext(25, \"\\u5EFA\\u6750\\u8AAA\\u660E\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"textarea\", 56);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function BuildingMaterialComponent_ng_template_53_Template_textarea_ngModelChange_26_listener($event) {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r3 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r3.selectedMaterial.CDescription, $event) || (ctx_r3.selectedMaterial.CDescription = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(27, \"div\", 52)(28, \"label\", 55);\n    i0.ɵɵtext(29, \"\\u50F9\\u683C\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(30, \"input\", 57);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function BuildingMaterialComponent_ng_template_53_Template_input_ngModelChange_30_listener($event) {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r3 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r3.selectedMaterial.CPrice, $event) || (ctx_r3.selectedMaterial.CPrice = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(31, \"nb-card-footer\", 32)(32, \"button\", 58);\n    i0.ɵɵlistener(\"click\", function BuildingMaterialComponent_ng_template_53_Template_button_click_32_listener() {\n      const ref_r15 = i0.ɵɵrestoreView(_r14).dialogRef;\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.onClose(ref_r15));\n    });\n    i0.ɵɵtext(33, \"\\u95DC\\u9589\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(34, \"button\", 59);\n    i0.ɵɵlistener(\"click\", function BuildingMaterialComponent_ng_template_53_Template_button_click_34_listener() {\n      const ref_r15 = i0.ɵɵrestoreView(_r14).dialogRef;\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.onSubmit(ref_r15));\n    });\n    i0.ɵɵtext(35, \"\\u5132\\u5B58\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(10);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r3.selectedMaterial.CName);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r3.selectedMaterial.CPart);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r3.selectedMaterial.CLocation);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r3.selectedMaterial.CSelectName);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r3.selectedMaterial.CDescription);\n    i0.ɵɵproperty(\"rows\", 4);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r3.selectedMaterial.CPrice);\n  }\n}\nfunction BuildingMaterialComponent_ng_template_55_div_21_i_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 94);\n  }\n}\nfunction BuildingMaterialComponent_ng_template_55_div_21_img_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 95);\n  }\n  if (rf & 2) {\n    const image_r18 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵproperty(\"src\", image_r18.thumbnailUrl, i0.ɵɵsanitizeUrl)(\"alt\", image_r18.name);\n  }\n}\nfunction BuildingMaterialComponent_ng_template_55_div_21_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 96);\n    i0.ɵɵelement(1, \"i\", 97);\n    i0.ɵɵelementStart(2, \"div\", 98);\n    i0.ɵɵtext(3, \"\\u7121\\u9810\\u89BD\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction BuildingMaterialComponent_ng_template_55_div_21_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r17 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 82);\n    i0.ɵɵlistener(\"click\", function BuildingMaterialComponent_ng_template_55_div_21_Template_div_click_0_listener() {\n      const image_r18 = i0.ɵɵrestoreView(_r17).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r3.toggleImageSelection(image_r18));\n    });\n    i0.ɵɵelementStart(1, \"div\", 83)(2, \"div\", 84);\n    i0.ɵɵtemplate(3, BuildingMaterialComponent_ng_template_55_div_21_i_3_Template, 1, 0, \"i\", 85);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"button\", 86);\n    i0.ɵɵlistener(\"click\", function BuildingMaterialComponent_ng_template_55_div_21_Template_button_click_4_listener($event) {\n      const image_r18 = i0.ɵɵrestoreView(_r17).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      const imagePreview_r19 = i0.ɵɵreference(58);\n      return i0.ɵɵresetView(ctx_r3.previewImage(image_r18, imagePreview_r19, $event));\n    });\n    i0.ɵɵelement(5, \"i\", 87);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 88);\n    i0.ɵɵtemplate(7, BuildingMaterialComponent_ng_template_55_div_21_img_7_Template, 1, 2, \"img\", 89)(8, BuildingMaterialComponent_ng_template_55_div_21_div_8_Template, 4, 0, \"div\", 90);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"div\", 91)(10, \"div\", 92);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"div\", 93);\n    i0.ɵɵtext(13);\n    i0.ɵɵpipe(14, \"number\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const image_r18 = ctx.$implicit;\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassProp(\"selected\", ctx_r3.isImageSelected(image_r18));\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"checked\", ctx_r3.isImageSelected(image_r18));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.isImageSelected(image_r18));\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", image_r18.thumbnailUrl);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !image_r18.thumbnailUrl);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"title\", image_r18.name);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(image_r18.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", i0.ɵɵpipeBind1(14, 10, image_r18.size), \" KB\");\n  }\n}\nfunction BuildingMaterialComponent_ng_template_55_div_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 99);\n    i0.ɵɵelement(1, \"i\", 100);\n    i0.ɵɵelementStart(2, \"div\");\n    i0.ɵɵtext(3, \"\\u627E\\u4E0D\\u5230\\u76F8\\u7B26\\u7684\\u5716\\u7247\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction BuildingMaterialComponent_ng_template_55_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r16 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-card\", 60)(1, \"nb-card-header\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"nb-card-body\", 61)(4, \"div\", 62)(5, \"div\", 63)(6, \"input\", 64);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function BuildingMaterialComponent_ng_template_55_Template_input_ngModelChange_6_listener($event) {\n      i0.ɵɵrestoreView(_r16);\n      const ctx_r3 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r3.imageSearchTerm, $event) || (ctx_r3.imageSearchTerm = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"input\", function BuildingMaterialComponent_ng_template_55_Template_input_input_6_listener() {\n      i0.ɵɵrestoreView(_r16);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.filterImages());\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"button\", 65);\n    i0.ɵɵlistener(\"click\", function BuildingMaterialComponent_ng_template_55_Template_button_click_7_listener() {\n      i0.ɵɵrestoreView(_r16);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.loadImages());\n    });\n    i0.ɵɵtext(8, \" \\u91CD\\u65B0\\u8F09\\u5165 \");\n    i0.ɵɵelement(9, \"i\", 66);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"div\", 67)(11, \"button\", 68);\n    i0.ɵɵlistener(\"click\", function BuildingMaterialComponent_ng_template_55_Template_button_click_11_listener() {\n      i0.ɵɵrestoreView(_r16);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.selectAllImages());\n    });\n    i0.ɵɵtext(12, \" \\u5168\\u9078 \");\n    i0.ɵɵelement(13, \"i\", 69);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"button\", 70);\n    i0.ɵɵlistener(\"click\", function BuildingMaterialComponent_ng_template_55_Template_button_click_14_listener() {\n      i0.ɵɵrestoreView(_r16);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.clearAllSelection());\n    });\n    i0.ɵɵtext(15, \" \\u6E05\\u9664\\u9078\\u53D6 \");\n    i0.ɵɵelement(16, \"i\", 71);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"div\", 72);\n    i0.ɵɵtext(18);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(19, \"div\", 73)(20, \"div\", 74);\n    i0.ɵɵtemplate(21, BuildingMaterialComponent_ng_template_55_div_21_Template, 15, 12, \"div\", 75);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(22, BuildingMaterialComponent_ng_template_55_div_22_Template, 4, 0, \"div\", 76);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(23, \"nb-card-footer\", 77)(24, \"div\", 78);\n    i0.ɵɵtext(25);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"div\", 79)(27, \"button\", 80);\n    i0.ɵɵlistener(\"click\", function BuildingMaterialComponent_ng_template_55_Template_button_click_27_listener() {\n      const ref_r20 = i0.ɵɵrestoreView(_r16).dialogRef;\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.onCloseImageBinder(ref_r20));\n    });\n    i0.ɵɵtext(28, \"\\u53D6\\u6D88\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(29, \"button\", 81);\n    i0.ɵɵlistener(\"click\", function BuildingMaterialComponent_ng_template_55_Template_button_click_29_listener() {\n      const ref_r20 = i0.ɵɵrestoreView(_r16).dialogRef;\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.onConfirmImageSelection(ref_r20));\n    });\n    i0.ɵɵtext(30);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \\u5716\\u7247\\u7D81\\u5B9A - \", ctx_r3.selectedMaterial.CName ? \"\\u70BA \" + ctx_r3.selectedMaterial.CName + \" \\u9078\\u64C7\\u5EFA\\u6750\\u5716\\u7247\" : \"\\u9078\\u64C7\\u5EFA\\u6750\\u5716\\u7247\", \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r3.imageSearchTerm);\n    i0.ɵɵadvance(12);\n    i0.ɵɵtextInterpolate1(\" \\u5DF2\\u9078\\u53D6: \", ctx_r3.selectedImages.length, \" \\u5F35\\u5716\\u7247 \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.filteredImages);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.filteredImages.length === 0);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \\u5171 \", ctx_r3.availableImages.length, \" \\u5F35\\u5716\\u7247 \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"disabled\", ctx_r3.selectedImages.length === 0);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \\u78BA\\u5B9A\\u9078\\u64C7 (\", ctx_r3.selectedImages.length, \") \");\n  }\n}\nfunction BuildingMaterialComponent_ng_template_57_img_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 108);\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"src\", ctx_r3.previewingImage.fullUrl, i0.ɵɵsanitizeUrl)(\"alt\", ctx_r3.previewingImage.name);\n  }\n}\nfunction BuildingMaterialComponent_ng_template_57_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 96);\n    i0.ɵɵelement(1, \"i\", 109);\n    i0.ɵɵelementStart(2, \"div\");\n    i0.ɵɵtext(3, \"\\u5716\\u7247\\u8F09\\u5165\\u5931\\u6557\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction BuildingMaterialComponent_ng_template_57_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r21 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-card\", 101)(1, \"nb-card-header\", 77)(2, \"span\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 79)(5, \"button\", 102);\n    i0.ɵɵlistener(\"click\", function BuildingMaterialComponent_ng_template_57_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r21);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.previousImage());\n    });\n    i0.ɵɵelement(6, \"i\", 103);\n    i0.ɵɵtext(7, \" \\u4E0A\\u4E00\\u5F35 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"button\", 102);\n    i0.ɵɵlistener(\"click\", function BuildingMaterialComponent_ng_template_57_Template_button_click_8_listener() {\n      i0.ɵɵrestoreView(_r21);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.nextImage());\n    });\n    i0.ɵɵtext(9, \" \\u4E0B\\u4E00\\u5F35 \");\n    i0.ɵɵelement(10, \"i\", 104);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(11, \"nb-card-body\", 105);\n    i0.ɵɵtemplate(12, BuildingMaterialComponent_ng_template_57_img_12_Template, 1, 2, \"img\", 106)(13, BuildingMaterialComponent_ng_template_57_div_13_Template, 4, 0, \"div\", 90);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"nb-card-footer\", 77)(15, \"div\", 78);\n    i0.ɵɵtext(16);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"div\", 79)(18, \"button\", 107);\n    i0.ɵɵlistener(\"click\", function BuildingMaterialComponent_ng_template_57_Template_button_click_18_listener() {\n      i0.ɵɵrestoreView(_r21);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.toggleImageSelectionInPreview());\n    });\n    i0.ɵɵtext(19);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"button\", 80);\n    i0.ɵɵlistener(\"click\", function BuildingMaterialComponent_ng_template_57_Template_button_click_20_listener() {\n      const ref_r22 = i0.ɵɵrestoreView(_r21).dialogRef;\n      return i0.ɵɵresetView(ref_r22.close());\n    });\n    i0.ɵɵtext(21, \"\\u95DC\\u9589\");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\\u5716\\u7247\\u9810\\u89BD - \", ctx_r3.previewingImage == null ? null : ctx_r3.previewingImage.name, \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", ctx_r3.currentPreviewIndex <= 0);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"disabled\", ctx_r3.currentPreviewIndex >= ctx_r3.filteredImages.length - 1);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.previewingImage && ctx_r3.previewingImage.fullUrl);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r3.previewingImage || !ctx_r3.previewingImage.fullUrl);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate2(\" \", ctx_r3.currentPreviewIndex + 1, \" / \", ctx_r3.filteredImages.length, \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.previewingImage && ctx_r3.isImageSelected(ctx_r3.previewingImage) ? \"\\u53D6\\u6D88\\u9078\\u53D6\" : \"\\u9078\\u53D6\\u6B64\\u5716\\u7247\", \" \");\n  }\n}\nfunction BuildingMaterialComponent_ng_template_59_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r23 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-card\", 110)(1, \"nb-card-header\")(2, \"span\");\n    i0.ɵɵtext(3, \" \\u6AA2\\u8996 \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"nb-card-body\", 111)(5, \"div\", 112);\n    i0.ɵɵelement(6, \"img\", 113);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"nb-card-footer\")(8, \"div\", 114)(9, \"button\", 115);\n    i0.ɵɵlistener(\"click\", function BuildingMaterialComponent_ng_template_59_Template_button_click_9_listener() {\n      const ref_r24 = i0.ɵɵrestoreView(_r23).dialogRef;\n      return i0.ɵɵresetView(ref_r24.close());\n    });\n    i0.ɵɵtext(10, \"\\u53D6\\u6D88\");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"src\", ctx_r3.currentImageShowing, i0.ɵɵsanitizeUrl);\n  }\n}\n// 圖片類別枚舉\nvar PictureCategory;\n(function (PictureCategory) {\n  PictureCategory[PictureCategory[\"NONE\"] = 0] = \"NONE\";\n  PictureCategory[PictureCategory[\"BUILDING_MATERIAL\"] = 1] = \"BUILDING_MATERIAL\";\n  PictureCategory[PictureCategory[\"SCHEMATIC\"] = 2] = \"SCHEMATIC\"; // 示意圖片\n})(PictureCategory || (PictureCategory = {}));\nexport class BuildingMaterialComponent extends BaseComponent {\n  constructor(_allow, dialogService, message, valid, _buildCaseService, _materialService, _utilityService) {\n    super(_allow);\n    this._allow = _allow;\n    this.dialogService = dialogService;\n    this.message = message;\n    this.valid = valid;\n    this._buildCaseService = _buildCaseService;\n    this._materialService = _materialService;\n    this._utilityService = _utilityService;\n    this.isNew = true;\n    this.listBuildCases = [];\n    this.materialOptions = [{\n      value: null,\n      label: '全部'\n    }, {\n      value: false,\n      label: '方案'\n    }, {\n      value: true,\n      label: '選樣'\n    }];\n    this.materialOptionsId = null;\n    this.CSelectName = \"\";\n    // 移除圖片檔名相關欄位\n    // CImageCode: string = \"\"\n    // CInfoImageCode: string = \"\"\n    // 預留建材代號欄位，等後端API支援後再啟用\n    // CMaterialCode: string = \"\"\n    this.ShowPrice = false;\n    this.currentImageShowing = \"\";\n    this.filterMapping = false;\n    this.CIsMapping = true;\n    // 圖片綁定相關屬性\n    this.availableImages = [];\n    this.filteredImages = [];\n    this.selectedImages = [];\n    this.imageSearchTerm = \"\";\n    this.previewingImage = null;\n    this.currentPreviewIndex = 0;\n  }\n  ngOnInit() {\n    this.getListBuildCase();\n  }\n  getListBuildCase() {\n    this._buildCaseService.apiBuildCaseGetAllBuildCaseForSelectPost$Json({\n      body: {\n        CIsPagi: false,\n        CStatus: 1\n      }\n    }).pipe(tap(res => {\n      if (res.StatusCode == 0) {\n        this.listBuildCases = res.Entries?.length ? res.Entries : [];\n        this.selectedBuildCaseId = this.listBuildCases[0].cID;\n      }\n    }), mergeMap(() => this.getMaterialList())).subscribe();\n  }\n  getMaterialList(pageIndex = 1) {\n    return this._materialService.apiMaterialGetMaterialListPost$Json({\n      body: {\n        CBuildCaseId: this.selectedBuildCaseId,\n        CPlanUse: this.materialOptionsId,\n        CSelectName: this.CSelectName,\n        // 移除圖片檔名相關查詢條件\n        // CImageCode: this.CImageCode,\n        // CInfoImageCode: this.CInfoImageCode,\n        PageSize: this.pageSize,\n        PageIndex: pageIndex,\n        CIsMapping: this.CIsMapping\n      }\n    }).pipe(tap(res => {\n      if (res.StatusCode == 0) {\n        this.materialList = res.Entries ?? [];\n        this.totalRecords = res.TotalItems;\n        if (this.materialList.length > 0) {\n          this.ShowPrice = this.materialList[0].CShowPrice;\n        }\n      }\n    }));\n  }\n  search() {\n    this.getMaterialList().subscribe();\n  }\n  pageChanged(pageIndex) {\n    this.getMaterialList(pageIndex).subscribe();\n  }\n  exportExelMaterialList() {\n    this._materialService.apiMaterialExportExcelMaterialListPost$Json({\n      body: this.selectedBuildCaseId\n    }).subscribe(res => {\n      if (res.StatusCode == 0) {\n        if (res.Entries.FileByte) {\n          this._utilityService.downloadExcelFile(res.Entries.FileByte, '建材管理');\n        }\n      }\n    });\n  }\n  exportExelMaterialTemplate() {\n    this._materialService.apiMaterialExportExcelMaterialTemplatePost$Json({\n      body: this.selectedBuildCaseId\n    }).subscribe(res => {\n      if (res.StatusCode == 0) {\n        if (res.Entries.FileByte) {\n          this._utilityService.downloadExcelFile(res.Entries.FileByte, '建材管理');\n        }\n      }\n    });\n  }\n  addNew(ref) {\n    this.isNew = true;\n    this.selectedMaterial = {};\n    this.dialogService.open(ref);\n  }\n  onSelectedMaterial(data, ref) {\n    this.isNew = false;\n    this.selectedMaterial = {\n      ...data\n    };\n    this.dialogService.open(ref);\n  }\n  bindImageForMaterial(data, ref) {\n    this.selectedMaterial = {\n      ...data\n    };\n    this.loadImages();\n    this.dialogService.open(ref, {\n      closeOnBackdropClick: false\n    });\n  }\n  validation() {\n    this.valid.clear();\n    this.valid.required('[名稱]', this.selectedMaterial.CName);\n    this.valid.required('[項目]', this.selectedMaterial.CPart);\n    this.valid.required('[位置]', this.selectedMaterial.CLocation);\n    this.valid.required('[建材選項名稱]', this.selectedMaterial.CSelectName);\n    // 移除圖片檔名驗證\n    // this.valid.required('[建材圖片編號]', this.selectedMaterial.CImageCode)\n    this.valid.isStringMaxLength('[名稱]', this.selectedMaterial.CName, 30);\n    this.valid.isStringMaxLength('[項目]', this.selectedMaterial.CPart, 30);\n    this.valid.isStringMaxLength('[位置]', this.selectedMaterial.CLocation, 30);\n    this.valid.isStringMaxLength('[建材選項名稱]', this.selectedMaterial.CSelectName, 30);\n    // 移除圖片檔名長度驗證\n    // this.valid.isStringMaxLength('[建材圖片編號]', this.selectedMaterial.CImageCode, 30)\n  }\n  onSubmit(ref) {\n    this.validation();\n    if (this.valid.errorMessages.length > 0) {\n      this.message.showErrorMSGs(this.valid.errorMessages);\n      return;\n    }\n    this._materialService.apiMaterialSaveMaterialAdminPost$Json({\n      body: {\n        CBuildCaseId: this.selectedBuildCaseId,\n        // 暫時保留 CImageCode 給圖片綁定功能使用\n        CImageCode: this.selectedMaterial.CImageCode,\n        CName: this.selectedMaterial.CName,\n        CPart: this.selectedMaterial.CPart,\n        CLocation: this.selectedMaterial.CLocation,\n        CSelectName: this.selectedMaterial.CSelectName,\n        CDescription: this.selectedMaterial.CDescription,\n        CMaterialId: this.isNew ? null : this.selectedMaterial.CId,\n        CPrice: this.selectedMaterial.CPrice\n      }\n    }).pipe(tap(res => {\n      if (res.StatusCode === 0) {\n        this.message.showSucessMSG(\"執行成功\");\n      } else {\n        this.message.showErrorMSG(res.Message);\n      }\n    }), mergeMap(() => this.getMaterialList()), finalize(() => ref.close())).subscribe();\n  }\n  onClose(ref) {\n    ref.close();\n  }\n  detectFileExcel(event) {\n    const target = event.target;\n    const reader = new FileReader();\n    reader.readAsBinaryString(target.files[0]);\n    reader.onload = e => {\n      const binarystr = e.target.result;\n      const wb = XLSX.read(binarystr, {\n        type: 'binary'\n      });\n      const wsname = wb.SheetNames[0];\n      const ws = wb.Sheets[wsname];\n      let isValidFile = true;\n      const data = XLSX.utils.sheet_to_json(ws);\n      if (data && data.length > 0) {\n        data.forEach(x => {\n          if (!(x['名稱'] && x['項目'] && x['位置'] && x['建材選項名稱'] && (x['建材圖片檔名'] || x['建材說明'] || x['示意圖片檔名']))) {\n            isValidFile = false;\n          }\n        });\n        if (!isValidFile) {\n          this.message.showErrorMSG(\"导入文件时出现错误\");\n        } else {\n          this._materialService.apiMaterialImportExcelMaterialListPost$Json({\n            body: {\n              CBuildCaseId: this.selectedBuildCaseId,\n              CFile: target.files[0]\n            }\n          }).pipe(tap(res => {\n            if (res.StatusCode == 0) {\n              this.message.showSucessMSG(\"執行成功\");\n            } else {\n              this.message.showErrorMSG(res.Message);\n            }\n          }), mergeMap(() => this.getMaterialList(1))).subscribe();\n        }\n      } else {\n        this.message.showErrorMSG(\"匯入的檔案內容為空，請檢查檔案並重新上傳。\");\n      }\n      event.target.value = null;\n    };\n  }\n  showImage(imageUrl, dialog) {\n    this.currentImageShowing = imageUrl;\n    this.dialogService.open(dialog);\n  }\n  changeFilter() {\n    if (this.filterMapping) {\n      this.CIsMapping = false;\n      this.getMaterialList().subscribe();\n    } else {\n      this.CIsMapping = true;\n      this.getMaterialList().subscribe();\n    }\n  }\n  // 圖片綁定功能方法\n  openImageBinder(ref) {\n    this.loadImages();\n    this.dialogService.open(ref, {\n      closeOnBackdropClick: false\n    });\n  }\n  loadImages() {\n    // 模擬載入更多圖片資料來測試滑動功能\n    this.availableImages = [{\n      id: '1',\n      name: 'floor_tile_001.jpg',\n      size: 245,\n      thumbnailUrl: 'assets/images/materials/thumbs/floor_tile_001.jpg',\n      fullUrl: 'assets/images/materials/floor_tile_001.jpg',\n      lastModified: new Date()\n    }, {\n      id: '2',\n      name: 'wall_paint_002.jpg',\n      size: 189,\n      thumbnailUrl: 'assets/images/materials/thumbs/wall_paint_002.jpg',\n      fullUrl: 'assets/images/materials/wall_paint_002.jpg',\n      lastModified: new Date()\n    }, {\n      id: '3',\n      name: 'cabinet_wood_003.jpg',\n      size: 312,\n      thumbnailUrl: 'assets/images/materials/thumbs/cabinet_wood_003.jpg',\n      fullUrl: 'assets/images/materials/cabinet_wood_003.jpg',\n      lastModified: new Date()\n    }, {\n      id: '4',\n      name: 'lighting_fixture_004.jpg',\n      size: 156,\n      thumbnailUrl: 'assets/images/materials/thumbs/lighting_fixture_004.jpg',\n      fullUrl: 'assets/images/materials/lighting_fixture_004.jpg',\n      lastModified: new Date()\n    }, {\n      id: '5',\n      name: 'door_handle_005.jpg',\n      size: 78,\n      thumbnailUrl: 'assets/images/materials/thumbs/door_handle_005.jpg',\n      fullUrl: 'assets/images/materials/door_handle_005.jpg',\n      lastModified: new Date()\n    }, {\n      id: '6',\n      name: 'window_frame_006.jpg',\n      size: 267,\n      thumbnailUrl: 'assets/images/materials/thumbs/window_frame_006.jpg',\n      fullUrl: 'assets/images/materials/window_frame_006.jpg',\n      lastModified: new Date()\n    },\n    // 新增更多測試圖片來驗證滑動功能\n    {\n      id: '7',\n      name: 'ceramic_tile_007.jpg',\n      size: 234,\n      thumbnailUrl: 'assets/images/materials/thumbs/ceramic_tile_007.jpg',\n      fullUrl: 'assets/images/materials/ceramic_tile_007.jpg',\n      lastModified: new Date()\n    }, {\n      id: '8',\n      name: 'wooden_floor_008.jpg',\n      size: 298,\n      thumbnailUrl: 'assets/images/materials/thumbs/wooden_floor_008.jpg',\n      fullUrl: 'assets/images/materials/wooden_floor_008.jpg',\n      lastModified: new Date()\n    }, {\n      id: '9',\n      name: 'marble_counter_009.jpg',\n      size: 345,\n      thumbnailUrl: 'assets/images/materials/thumbs/marble_counter_009.jpg',\n      fullUrl: 'assets/images/materials/marble_counter_009.jpg',\n      lastModified: new Date()\n    }, {\n      id: '10',\n      name: 'glass_panel_010.jpg',\n      size: 187,\n      thumbnailUrl: 'assets/images/materials/thumbs/glass_panel_010.jpg',\n      fullUrl: 'assets/images/materials/glass_panel_010.jpg',\n      lastModified: new Date()\n    }, {\n      id: '11',\n      name: 'metal_fixture_011.jpg',\n      size: 156,\n      thumbnailUrl: 'assets/images/materials/thumbs/metal_fixture_011.jpg',\n      fullUrl: 'assets/images/materials/metal_fixture_011.jpg',\n      lastModified: new Date()\n    }, {\n      id: '12',\n      name: 'fabric_curtain_012.jpg',\n      size: 201,\n      thumbnailUrl: 'assets/images/materials/thumbs/fabric_curtain_012.jpg',\n      fullUrl: 'assets/images/materials/fabric_curtain_012.jpg',\n      lastModified: new Date()\n    }];\n    this.filteredImages = [...this.availableImages];\n    // TODO: 實際實作時，這裡應該呼叫 API 取得圖片列表\n    // this._materialService.apiMaterialGetImageListPost$Json({\n    //   body: { CBuildCaseId: this.selectedBuildCaseId }\n    // }).subscribe(res => {\n    //   if (res.StatusCode === 0) {\n    //     this.availableImages = res.Entries || [];\n    //     this.filteredImages = [...this.availableImages];\n    //   }\n    // });\n  }\n  filterImages() {\n    if (!this.imageSearchTerm.trim()) {\n      this.filteredImages = [...this.availableImages];\n    } else {\n      const searchTerm = this.imageSearchTerm.toLowerCase();\n      this.filteredImages = this.availableImages.filter(image => image.name.toLowerCase().includes(searchTerm));\n    }\n  }\n  toggleImageSelection(image) {\n    const index = this.selectedImages.findIndex(selected => selected.id === image.id);\n    if (index > -1) {\n      this.selectedImages.splice(index, 1);\n    } else {\n      this.selectedImages.push(image);\n    }\n  }\n  isImageSelected(image) {\n    return this.selectedImages.some(selected => selected.id === image.id);\n  }\n  selectAllImages() {\n    this.selectedImages = [...this.filteredImages];\n  }\n  clearAllSelection() {\n    this.selectedImages = [];\n  }\n  previewImage(image, imagePreviewRef, event) {\n    event.stopPropagation();\n    this.previewingImage = image;\n    this.currentPreviewIndex = this.filteredImages.findIndex(img => img.id === image.id);\n    this.dialogService.open(imagePreviewRef);\n  }\n  previousImage() {\n    if (this.currentPreviewIndex > 0) {\n      this.currentPreviewIndex--;\n      this.previewingImage = this.filteredImages[this.currentPreviewIndex];\n    }\n  }\n  nextImage() {\n    if (this.currentPreviewIndex < this.filteredImages.length - 1) {\n      this.currentPreviewIndex++;\n      this.previewingImage = this.filteredImages[this.currentPreviewIndex];\n    }\n  }\n  toggleImageSelectionInPreview() {\n    if (this.previewingImage) {\n      this.toggleImageSelection(this.previewingImage);\n    }\n  }\n  onConfirmImageSelection(ref) {\n    if (this.selectedImages.length > 0) {\n      // 如果只選取一張圖片，直接設定到建材圖片檔名\n      if (this.selectedImages.length === 1) {\n        this.selectedMaterial.CImageCode = this.selectedImages[0].name;\n      } else {\n        // 多張圖片的話，可以設定第一張，或者讓使用者選擇主要圖片\n        const imageNames = this.selectedImages.map(img => img.name).join(', ');\n        this.message.showSucessMSG(`已選取 ${this.selectedImages.length} 張圖片: ${imageNames}`);\n        this.selectedMaterial.CImageCode = this.selectedImages[0].name;\n      }\n      // 如果是從表格直接綁定圖片（有 CId），則直接保存到資料庫\n      if (this.selectedMaterial.CId) {\n        this.saveImageBinding();\n      }\n    }\n    this.clearAllSelection();\n    ref.close();\n  }\n  // 新增方法：保存圖片綁定\n  saveImageBinding() {\n    this._materialService.apiMaterialSaveMaterialAdminPost$Json({\n      body: {\n        CBuildCaseId: this.selectedBuildCaseId,\n        CImageCode: this.selectedMaterial.CImageCode,\n        CName: this.selectedMaterial.CName,\n        CPart: this.selectedMaterial.CPart,\n        CLocation: this.selectedMaterial.CLocation,\n        CSelectName: this.selectedMaterial.CSelectName,\n        CDescription: this.selectedMaterial.CDescription,\n        CMaterialId: this.selectedMaterial.CId,\n        CPrice: this.selectedMaterial.CPrice\n      }\n    }).pipe(tap(res => {\n      if (res.StatusCode === 0) {\n        this.message.showSucessMSG(`圖片綁定成功: ${this.selectedMaterial.CImageCode}`);\n      } else {\n        this.message.showErrorMSG(res.Message);\n      }\n    }), mergeMap(() => this.getMaterialList()), finalize(() => {\n      // 清空選取的建材\n      this.selectedMaterial = {};\n    })).subscribe();\n  }\n  onCloseImageBinder(ref) {\n    this.clearAllSelection();\n    this.imageSearchTerm = \"\";\n    ref.close();\n  }\n  static {\n    this.ɵfac = function BuildingMaterialComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || BuildingMaterialComponent)(i0.ɵɵdirectiveInject(i1.AllowHelper), i0.ɵɵdirectiveInject(i2.NbDialogService), i0.ɵɵdirectiveInject(i3.MessageService), i0.ɵɵdirectiveInject(i4.ValidationHelper), i0.ɵɵdirectiveInject(i5.BuildCaseService), i0.ɵɵdirectiveInject(i5.MaterialService), i0.ɵɵdirectiveInject(i6.UtilityService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: BuildingMaterialComponent,\n      selectors: [[\"ngx-building-material\"]],\n      standalone: true,\n      features: [i0.ɵɵInheritDefinitionFeature, i0.ɵɵStandaloneFeature],\n      decls: 61,\n      vars: 13,\n      consts: [[\"inputFile\", \"\"], [\"dialog\", \"\"], [\"imageBinder\", \"\"], [\"imagePreview\", \"\"], [\"dialogImage\", \"\"], [\"accent\", \"success\"], [1, \"font-bold\", \"text-[#818181]\"], [1, \"d-flex\", \"flex-wrap\"], [1, \"col-md-6\"], [1, \"form-group\", \"d-flex\", \"align-items-center\", \"w-full\"], [\"for\", \"buildingName\", 1, \"label\", \"mr-2\", \"w-[22%]\"], [\"placeholder\", \"\\u5EFA\\u6848\", 1, \"w-full\", 3, \"ngModelChange\", \"ngModel\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"type\", \"text\", \"nbInput\", \"\", \"placeholder\", \"\\u5EFA\\u6750\\u9078\\u9805\\u540D\\u7A31\", 1, \"w-full\", 3, \"ngModelChange\", \"ngModel\"], [1, \"col-md-12\"], [1, \"d-flex\", \"justify-content-end\", \"w-full\"], [\"status\", \"basic\", 1, \"flex\", 2, \"flex\", \"auto\", 3, \"checkedChange\", \"change\", \"checked\"], [\"class\", \"btn btn-success mr-2\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"btn btn-info mr-2 text-white ml-2\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"btn btn-info mx-1 ml-2 mr-2\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"btn btn-info mx-1\", 3, \"click\", 4, \"ngIf\"], [\"type\", \"file\", \"accept\", \".xls, .xlsx\", 1, \"hidden\", 3, \"change\"], [1, \"btn\", \"btn-success\", \"ml-2\", 3, \"click\"], [1, \"fas\", \"fa-file-download\"], [1, \"table-responsive\", \"mt-4\"], [1, \"table\", \"table-striped\", \"border\", 2, \"min-width\", \"1200px\", \"background-color\", \"#f3f3f3\"], [2, \"background-color\", \"#27ae60\", \"color\", \"white\"], [\"scope\", \"col\", 1, \"col-1\"], [\"scope\", \"col\", 1, \"col-3\"], [\"scope\", \"col\", \"class\", \"col-1\", 4, \"ngIf\"], [\"scope\", \"col\", 1, \"col-1\", \"text-center\"], [4, \"ngIf\"], [1, \"d-flex\", \"justify-content-center\"], [3, \"CollectionSizeChange\", \"PageSizeChange\", \"PageChange\", \"CollectionSize\", \"PageSize\", \"Page\"], [3, \"value\"], [1, \"btn\", \"btn-success\", \"mr-2\", 3, \"click\"], [1, \"btn\", \"btn-info\", \"mr-2\", \"text-white\", \"ml-2\", 3, \"click\"], [1, \"fas\", \"fa-search\"], [1, \"btn\", \"btn-info\", \"mx-1\", \"ml-2\", \"mr-2\", 3, \"click\"], [1, \"fas\", \"fa-plus\"], [1, \"btn\", \"btn-info\", \"mx-1\", 3, \"click\"], [4, \"ngFor\", \"ngForOf\"], [1, \"text-center\", \"w-32\"], [\"class\", \"btn btn-outline-primary btn-sm m-1\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"btn btn-outline-info btn-sm m-1\", 3, \"title\", \"click\", 4, \"ngIf\"], [1, \"btn\", \"btn-outline-primary\", \"btn-sm\", \"m-1\", 3, \"click\"], [1, \"btn\", \"btn-outline-info\", \"btn-sm\", \"m-1\", 3, \"click\", \"title\"], [1, \"fas\", \"fa-images\"], [1, \"w-[700px]\"], [1, \"px-4\"], [1, \"text-base\"], [1, \"w-full\", \"mt-3\"], [1, \"flex\", \"items-center\", \"mt-3\"], [1, \"required-field\", \"w-[150px]\"], [\"type\", \"text\", \"nbInput\", \"\", \"maxlength\", \"30\", 1, \"w-full\", \"!max-w-full\", \"p-2\", \"rounded\", \"text-[13px]\", 3, \"ngModelChange\", \"ngModel\"], [1, \"w-[150px]\"], [\"nbInput\", \"\", 1, \"resize-none\", \"w-full\", \"!max-w-full\", \"p-2\", \"rounded\", \"text-[13px]\", 3, \"ngModelChange\", \"ngModel\", \"rows\"], [\"type\", \"number\", \"nbInput\", \"\", \"maxlength\", \"30\", 1, \"w-full\", \"!max-w-full\", \"p-2\", \"rounded\", \"text-[13px]\", 3, \"ngModelChange\", \"ngModel\"], [1, \"btn\", \"btn-danger\", \"btn-sm\", \"mr-4\", 3, \"click\"], [1, \"btn\", \"btn-success\", \"btn-sm\", 3, \"click\"], [1, \"w-[900px]\", \"h-[700px]\"], [1, \"px-4\", \"d-flex\", \"flex-column\", 2, \"height\", \"calc(700px - 120px)\", \"overflow\", \"hidden\", \"padding-bottom\", \"0\"], [1, \"flex\", \"gap-3\", \"mb-4\", \"flex-shrink-0\"], [1, \"flex-1\"], [\"type\", \"text\", \"placeholder\", \"\\u641C\\u5C0B\\u5716\\u7247\\u540D\\u7A31...\", 1, \"w-full\", \"search-input\", 3, \"ngModelChange\", \"input\", \"ngModel\"], [1, \"btn\", \"btn-info\", \"btn-image-action\", 3, \"click\"], [1, \"fas\", \"fa-refresh\"], [1, \"flex\", \"gap-2\", \"mb-3\", \"flex-shrink-0\"], [1, \"btn\", \"btn-outline-primary\", \"btn-sm\", 3, \"click\"], [1, \"fas\", \"fa-check-square\"], [1, \"btn\", \"btn-outline-secondary\", \"btn-sm\", 3, \"click\"], [1, \"fas\", \"fa-square\"], [1, \"ml-auto\", \"text-sm\", \"text-gray-600\"], [1, \"image-preview-container\", \"border\", \"rounded\", \"p-3\", \"flex-1\"], [1, \"grid\", \"grid-cols-4\", \"gap-3\"], [\"class\", \"image-grid-item border rounded p-2 cursor-pointer\", 3, \"selected\", \"click\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"text-center text-gray-500 py-20\", 4, \"ngIf\"], [1, \"d-flex\", \"justify-content-between\", \"align-items-center\"], [1, \"text-sm\", \"text-gray-600\"], [1, \"d-flex\", \"gap-2\"], [1, \"btn\", \"btn-danger\", \"btn-sm\", 3, \"click\"], [1, \"btn\", \"btn-success\", \"btn-sm\", 3, \"click\", \"disabled\"], [1, \"image-grid-item\", \"border\", \"rounded\", \"p-2\", \"cursor-pointer\", 3, \"click\"], [1, \"flex\", \"justify-between\", \"items-center\", \"mb-2\"], [1, \"image-checkbox\", \"w-5\", \"h-5\", \"border-2\", \"rounded\", \"flex\", \"items-center\", \"justify-center\"], [\"class\", \"fas fa-check text-white text-xs\", 4, \"ngIf\"], [1, \"btn\", \"btn-outline-info\", \"btn-xs\", \"btn-image-action\", 3, \"click\"], [1, \"fas\", \"fa-eye\"], [1, \"w-full\", \"h-32\", \"bg-gray-100\", \"rounded\", \"mb-2\", \"flex\", \"items-center\", \"justify-center\", \"overflow-hidden\"], [\"class\", \"image-thumbnail max-w-full max-h-full object-contain\", 3, \"src\", \"alt\", 4, \"ngIf\"], [\"class\", \"text-gray-400 text-center\", 4, \"ngIf\"], [1, \"text-xs\", \"text-gray-600\"], [1, \"font-medium\", \"truncate\", 3, \"title\"], [1, \"text-gray-400\"], [1, \"fas\", \"fa-check\", \"text-white\", \"text-xs\"], [1, \"image-thumbnail\", \"max-w-full\", \"max-h-full\", \"object-contain\", 3, \"src\", \"alt\"], [1, \"text-gray-400\", \"text-center\"], [1, \"fas\", \"fa-image\", \"text-2xl\", \"mb-1\"], [1, \"text-xs\"], [1, \"text-center\", \"text-gray-500\", \"py-20\"], [1, \"fas\", \"fa-images\", \"text-4xl\", \"mb-3\"], [1, \"w-[800px]\", \"h-[600px]\"], [1, \"btn\", \"btn-outline-primary\", \"btn-sm\", 3, \"click\", \"disabled\"], [1, \"fas\", \"fa-chevron-left\"], [1, \"fas\", \"fa-chevron-right\"], [1, \"p-0\", \"d-flex\", \"justify-content-center\", \"align-items-center\", 2, \"height\", \"500px\"], [\"class\", \"max-w-full max-h-full object-contain\", 3, \"src\", \"alt\", 4, \"ngIf\"], [1, \"btn\", \"btn-outline-info\", \"btn-sm\", 3, \"click\"], [1, \"max-w-full\", \"max-h-full\", \"object-contain\", 3, \"src\", \"alt\"], [1, \"fas\", \"fa-image\", \"text-4xl\", \"mb-3\"], [1, \"mr-md-5\", \"ml-md-5\", 2, \"height\", \"100%\", \"overflow\", \"auto\", \"width\", \"700px\"], [2, \"padding\", \"1rem 2rem\"], [1, \"w-full\", \"h-auto\"], [1, \"fit-size\", 3, \"src\"], [1, \"flex\", \"justify-center\", \"items-center\"], [1, \"btn\", \"btn-danger\", \"mr-2\", 3, \"click\"]],\n      template: function BuildingMaterialComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"nb-card\", 5)(1, \"nb-card-header\");\n          i0.ɵɵelement(2, \"ngx-breadcrumb\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(3, \"nb-card-body\")(4, \"h1\", 6);\n          i0.ɵɵtext(5, \" \\u53EF\\u8A2D\\u5B9A\\u55AE\\u7B46\\u6216\\u6279\\u6B21\\u532F\\u5165\\u8A2D\\u5B9A\\u5404\\u5340\\u57DF\\u53CA\\u65B9\\u6848\\u5C0D\\u61C9\\u4E4B\\u5EFA\\u6750\\u3002 \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(6, \"div\", 7)(7, \"div\", 8)(8, \"div\", 9)(9, \"label\", 10);\n          i0.ɵɵtext(10, \"\\u5EFA\\u6848\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(11, \"nb-select\", 11);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function BuildingMaterialComponent_Template_nb_select_ngModelChange_11_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.selectedBuildCaseId, $event) || (ctx.selectedBuildCaseId = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵtemplate(12, BuildingMaterialComponent_nb_option_12_Template, 2, 2, \"nb-option\", 12);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(13, \"div\", 8)(14, \"div\", 9)(15, \"label\", 10);\n          i0.ɵɵtext(16, \"\\u5EFA\\u6750\\u9078\\u9805\\u540D\\u7A31 \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(17, \"input\", 13);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function BuildingMaterialComponent_Template_input_ngModelChange_17_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.CSelectName, $event) || (ctx.CSelectName = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(18, \"div\", 14)(19, \"div\", 15)(20, \"nb-checkbox\", 16);\n          i0.ɵɵtwoWayListener(\"checkedChange\", function BuildingMaterialComponent_Template_nb_checkbox_checkedChange_20_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.filterMapping, $event) || (ctx.filterMapping = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵlistener(\"change\", function BuildingMaterialComponent_Template_nb_checkbox_change_20_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.changeFilter());\n          });\n          i0.ɵɵtext(21, \" \\u53EA\\u986F\\u793A\\u7F3A\\u5C11\\u5EFA\\u6750\\u5716\\u7247\\u6216\\u793A\\u610F\\u5716\\u7247\\u7684\\u5EFA\\u6750 \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(22, BuildingMaterialComponent_button_22_Template, 3, 0, \"button\", 17)(23, BuildingMaterialComponent_button_23_Template, 3, 0, \"button\", 18)(24, BuildingMaterialComponent_button_24_Template, 3, 0, \"button\", 19)(25, BuildingMaterialComponent_button_25_Template, 2, 0, \"button\", 20);\n          i0.ɵɵelementStart(26, \"input\", 21, 0);\n          i0.ɵɵlistener(\"change\", function BuildingMaterialComponent_Template_input_change_26_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.detectFileExcel($event));\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(28, \"button\", 22);\n          i0.ɵɵlistener(\"click\", function BuildingMaterialComponent_Template_button_click_28_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.exportExelMaterialTemplate());\n          });\n          i0.ɵɵtext(29, \"\\u4E0B\\u8F09\\u7BC4\\u4F8B\\u6A94\\u6848 \");\n          i0.ɵɵelement(30, \"i\", 23);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(31, \"div\", 24)(32, \"table\", 25)(33, \"thead\")(34, \"tr\", 26)(35, \"th\", 27);\n          i0.ɵɵtext(36, \"\\u9805\\u6B21\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(37, \"th\", 27);\n          i0.ɵɵtext(38, \"\\u540D\\u7A31\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(39, \"th\", 27);\n          i0.ɵɵtext(40, \"\\u9805\\u76EE\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(41, \"th\", 27);\n          i0.ɵɵtext(42, \"\\u4F4D\\u7F6E\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(43, \"th\", 27);\n          i0.ɵɵtext(44, \"\\u9078\\u9805\\u540D\\u7A31\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(45, \"th\", 28);\n          i0.ɵɵtext(46, \"\\u5EFA\\u6750\\u8AAA\\u660E\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(47, BuildingMaterialComponent_th_47_Template, 2, 0, \"th\", 29);\n          i0.ɵɵelementStart(48, \"th\", 30);\n          i0.ɵɵtext(49, \"\\u64CD\\u4F5C\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(50, BuildingMaterialComponent_tbody_50_Template, 2, 1, \"tbody\", 31);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(51, \"nb-card-footer\", 32)(52, \"ngx-pagination\", 33);\n          i0.ɵɵtwoWayListener(\"CollectionSizeChange\", function BuildingMaterialComponent_Template_ngx_pagination_CollectionSizeChange_52_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.totalRecords, $event) || (ctx.totalRecords = $event);\n            return i0.ɵɵresetView($event);\n          })(\"PageSizeChange\", function BuildingMaterialComponent_Template_ngx_pagination_PageSizeChange_52_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.pageSize, $event) || (ctx.pageSize = $event);\n            return i0.ɵɵresetView($event);\n          })(\"PageChange\", function BuildingMaterialComponent_Template_ngx_pagination_PageChange_52_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.pageIndex, $event) || (ctx.pageIndex = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵlistener(\"PageChange\", function BuildingMaterialComponent_Template_ngx_pagination_PageChange_52_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.pageChanged($event));\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(53, BuildingMaterialComponent_ng_template_53_Template, 36, 7, \"ng-template\", null, 1, i0.ɵɵtemplateRefExtractor)(55, BuildingMaterialComponent_ng_template_55_Template, 31, 8, \"ng-template\", null, 2, i0.ɵɵtemplateRefExtractor)(57, BuildingMaterialComponent_ng_template_57_Template, 22, 8, \"ng-template\", null, 3, i0.ɵɵtemplateRefExtractor)(59, BuildingMaterialComponent_ng_template_59_Template, 11, 1, \"ng-template\", null, 4, i0.ɵɵtemplateRefExtractor);\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(11);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.selectedBuildCaseId);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngForOf\", ctx.listBuildCases);\n          i0.ɵɵadvance(5);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.CSelectName);\n          i0.ɵɵadvance(3);\n          i0.ɵɵtwoWayProperty(\"checked\", ctx.filterMapping);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.isExcelExport);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isRead);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isCreate);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isExcelImport);\n          i0.ɵɵadvance(22);\n          i0.ɵɵproperty(\"ngIf\", ctx.ShowPrice == true);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", ctx.materialList != null && ctx.materialList.length > 0);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtwoWayProperty(\"CollectionSize\", ctx.totalRecords)(\"PageSize\", ctx.pageSize)(\"Page\", ctx.pageIndex);\n        }\n      },\n      dependencies: [CommonModule, i7.NgForOf, i7.NgIf, i7.DecimalPipe, SharedModule, i8.DefaultValueAccessor, i8.NumberValueAccessor, i8.NgControlStatus, i8.MaxLengthValidator, i8.NgModel, i2.NbCardComponent, i2.NbCardBodyComponent, i2.NbCardFooterComponent, i2.NbCardHeaderComponent, i2.NbCheckboxComponent, i2.NbInputDirective, i2.NbSelectComponent, i2.NbOptionComponent, i9.BreadcrumbComponent, i10.PaginationComponent],\n      styles: [\"@charset \\\"UTF-8\\\";\\n.image-table[_ngcontent-%COMP%] {\\n  width: 50px;\\n  height: 50px;\\n  cursor: pointer;\\n}\\n\\n.empty-image[_ngcontent-%COMP%] {\\n  color: red;\\n}\\n\\n.fit-size[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: auto;\\n  max-width: 100%;\\n  max-height: 500px;\\n  object-fit: contain;\\n}\\n\\n.image-grid-item[_ngcontent-%COMP%] {\\n  transition: all 0.3s ease;\\n}\\n.image-grid-item[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-2px);\\n  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);\\n}\\n.image-grid-item.selected[_ngcontent-%COMP%] {\\n  border-color: #3366ff;\\n  background-color: #f0f7ff;\\n  box-shadow: 0 0 0 2px rgba(51, 102, 255, 0.2);\\n}\\n\\n.image-checkbox[_ngcontent-%COMP%] {\\n  border-color: #ccc;\\n  background-color: white;\\n  transition: all 0.2s ease;\\n}\\n.image-checkbox.checked[_ngcontent-%COMP%] {\\n  background-color: #3366ff;\\n  border-color: #3366ff;\\n}\\n\\n.image-thumbnail[_ngcontent-%COMP%] {\\n  transition: transform 0.2s ease;\\n}\\n.image-thumbnail[_ngcontent-%COMP%]:hover {\\n  transform: scale(1.05);\\n}\\n\\n.image-preview-container[_ngcontent-%COMP%] {\\n  max-height: 480px;\\n  overflow-y: auto !important;\\n  overflow-x: hidden;\\n  \\n\\n  \\n\\n  -webkit-overflow-scrolling: touch;\\n  scrollbar-width: thin;\\n  scrollbar-color: #c1c1c1 #f1f1f1;\\n}\\n.image-preview-container[_ngcontent-%COMP%]::-webkit-scrollbar {\\n  width: 8px;\\n}\\n.image-preview-container[_ngcontent-%COMP%]::-webkit-scrollbar-track {\\n  background: #f1f1f1;\\n  border-radius: 4px;\\n}\\n.image-preview-container[_ngcontent-%COMP%]::-webkit-scrollbar-thumb {\\n  background: #c1c1c1;\\n  border-radius: 4px;\\n}\\n.image-preview-container[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover {\\n  background: #a8a8a8;\\n}\\n\\n.search-input[_ngcontent-%COMP%] {\\n  padding: 8px 12px;\\n  border: 1px solid #ddd;\\n  border-radius: 4px;\\n  font-size: 14px;\\n  transition: border-color 0.2s ease-in-out;\\n}\\n.search-input[_ngcontent-%COMP%]:focus {\\n  outline: none;\\n  border-color: #3366ff;\\n  box-shadow: 0 0 0 2px rgba(51, 102, 255, 0.1);\\n}\\n\\n.btn-image-action[_ngcontent-%COMP%] {\\n  padding: 4px 8px;\\n  font-size: 12px;\\n  border-radius: 4px;\\n  transition: all 0.2s ease-in-out;\\n}\\n.btn-image-action[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-1px);\\n}\\n.btn-image-action.btn-xs[_ngcontent-%COMP%] {\\n  padding: 2px 6px;\\n  font-size: 10px;\\n}\\n\\n\\n\\n.d-flex.flex-column[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n}\\n\\n.flex-1[_ngcontent-%COMP%] {\\n  flex: 1;\\n  min-height: 0;\\n}\\n\\n\\n\\nnb-card.w-\\\\__ph-0__[_ngcontent-%COMP%]   .nb-card-body[_ngcontent-%COMP%] {\\n  height: 580px !important;\\n  overflow: hidden !important;\\n  display: flex !important;\\n  flex-direction: column !important;\\n}\\n\\n\\n\\n.flex-shrink-0[_ngcontent-%COMP%] {\\n  flex-shrink: 0;\\n}\\n\\n\\n\\n.image-preview-container.flex-1[_ngcontent-%COMP%] {\\n  flex: 1 1 auto;\\n  min-height: 0;\\n  height: auto;\\n  overflow-y: scroll !important;\\n  overflow-x: hidden !important;\\n}\\n\\n\\n\\n.grid.grid-cols-4[_ngcontent-%COMP%] {\\n  min-height: min-content;\\n}\\n\\n\\n\\n  nb-card-body .image-preview-container {\\n  max-height: none !important;\\n  height: 100% !important;\\n}\\n/*# sourceMappingURL=data:application/json;base64,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 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "finalize", "mergeMap", "tap", "XLSX", "SharedModule", "BaseComponent", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵproperty", "buildCase_r2", "cID", "ɵɵadvance", "ɵɵtextInterpolate1", "CBuildCaseName", "ɵɵlistener", "BuildingMaterialComponent_button_22_Template_button_click_0_listener", "ɵɵrestoreView", "_r3", "ctx_r3", "ɵɵnextContext", "ɵɵresetView", "exportExelMaterialList", "ɵɵelement", "BuildingMaterialComponent_button_23_Template_button_click_0_listener", "_r5", "search", "BuildingMaterialComponent_button_24_Template_button_click_0_listener", "_r6", "dialog_r7", "ɵɵreference", "addNew", "BuildingMaterialComponent_button_25_Template_button_click_0_listener", "_r8", "inputFile_r9", "click", "ɵɵtextInterpolate", "item_r10", "CPrice", "BuildingMaterialComponent_tbody_50_tr_1_button_15_Template_button_click_0_listener", "_r11", "$implicit", "onSelectedMaterial", "BuildingMaterialComponent_tbody_50_tr_1_button_16_Template_button_click_0_listener", "_r12", "imageBinder_r13", "bindImageForMaterial", "CName", "ɵɵtemplate", "BuildingMaterialComponent_tbody_50_tr_1_td_13_Template", "BuildingMaterialComponent_tbody_50_tr_1_button_15_Template", "BuildingMaterialComponent_tbody_50_tr_1_button_16_Template", "CId", "<PERSON>art", "CLocation", "ɵɵstyleMap", "CIsMapping", "CSelectName", "CDescription", "CShowPrice", "isRead", "BuildingMaterialComponent_tbody_50_tr_1_Template", "materialList", "ɵɵtwoWayListener", "BuildingMaterialComponent_ng_template_53_Template_input_ngModelChange_10_listener", "$event", "_r14", "ɵɵtwoWayBindingSet", "selectedMaterial", "BuildingMaterialComponent_ng_template_53_Template_input_ngModelChange_14_listener", "BuildingMaterialComponent_ng_template_53_Template_input_ngModelChange_18_listener", "BuildingMaterialComponent_ng_template_53_Template_input_ngModelChange_22_listener", "BuildingMaterialComponent_ng_template_53_Template_textarea_ngModelChange_26_listener", "BuildingMaterialComponent_ng_template_53_Template_input_ngModelChange_30_listener", "BuildingMaterialComponent_ng_template_53_Template_button_click_32_listener", "ref_r15", "dialogRef", "onClose", "BuildingMaterialComponent_ng_template_53_Template_button_click_34_listener", "onSubmit", "ɵɵtwoWayProperty", "image_r18", "thumbnailUrl", "ɵɵsanitizeUrl", "name", "BuildingMaterialComponent_ng_template_55_div_21_Template_div_click_0_listener", "_r17", "toggleImageSelection", "BuildingMaterialComponent_ng_template_55_div_21_i_3_Template", "BuildingMaterialComponent_ng_template_55_div_21_Template_button_click_4_listener", "imagePreview_r19", "previewImage", "BuildingMaterialComponent_ng_template_55_div_21_img_7_Template", "BuildingMaterialComponent_ng_template_55_div_21_div_8_Template", "ɵɵclassProp", "isImageSelected", "ɵɵpipeBind1", "size", "BuildingMaterialComponent_ng_template_55_Template_input_ngModelChange_6_listener", "_r16", "imageSearchTerm", "BuildingMaterialComponent_ng_template_55_Template_input_input_6_listener", "filterImages", "BuildingMaterialComponent_ng_template_55_Template_button_click_7_listener", "loadImages", "BuildingMaterialComponent_ng_template_55_Template_button_click_11_listener", "selectAllImages", "BuildingMaterialComponent_ng_template_55_Template_button_click_14_listener", "clearAllSelection", "BuildingMaterialComponent_ng_template_55_div_21_Template", "BuildingMaterialComponent_ng_template_55_div_22_Template", "BuildingMaterialComponent_ng_template_55_Template_button_click_27_listener", "ref_r20", "onCloseImageBinder", "BuildingMaterialComponent_ng_template_55_Template_button_click_29_listener", "onConfirmImageSelection", "selectedImages", "length", "filteredImages", "availableImages", "previewingImage", "fullUrl", "BuildingMaterialComponent_ng_template_57_Template_button_click_5_listener", "_r21", "previousImage", "BuildingMaterialComponent_ng_template_57_Template_button_click_8_listener", "nextImage", "BuildingMaterialComponent_ng_template_57_img_12_Template", "BuildingMaterialComponent_ng_template_57_div_13_Template", "BuildingMaterialComponent_ng_template_57_Template_button_click_18_listener", "toggleImageSelectionInPreview", "BuildingMaterialComponent_ng_template_57_Template_button_click_20_listener", "ref_r22", "close", "currentPreviewIndex", "ɵɵtextInterpolate2", "BuildingMaterialComponent_ng_template_59_Template_button_click_9_listener", "ref_r24", "_r23", "currentImageShowing", "PictureCategory", "BuildingMaterialComponent", "constructor", "_allow", "dialogService", "message", "valid", "_buildCaseService", "_materialService", "_utilityService", "isNew", "listBuildCases", "materialOptions", "value", "label", "materialOptionsId", "ShowPrice", "filterMapping", "ngOnInit", "getListBuildCase", "apiBuildCaseGetAllBuildCaseForSelectPost$Json", "body", "CIsPagi", "CStatus", "pipe", "res", "StatusCode", "Entries", "selectedBuildCaseId", "getMaterialList", "subscribe", "pageIndex", "apiMaterialGetMaterialListPost$Json", "CBuildCaseId", "CPlanUse", "PageSize", "pageSize", "PageIndex", "totalRecords", "TotalItems", "pageChanged", "apiMaterialExportExcelMaterialListPost$Json", "FileByte", "downloadExcelFile", "exportExelMaterialTemplate", "apiMaterialExportExcelMaterialTemplatePost$Json", "ref", "open", "data", "closeOnBackdropClick", "validation", "clear", "required", "isStringMaxLength", "errorMessages", "showErrorMSGs", "apiMaterialSaveMaterialAdminPost$Json", "CImageCode", "CMaterialId", "showSucessMSG", "showErrorMSG", "Message", "detectFileExcel", "event", "target", "reader", "FileReader", "readAsBinaryString", "files", "onload", "e", "binarystr", "result", "wb", "read", "type", "wsname", "SheetNames", "ws", "Sheets", "isValidFile", "utils", "sheet_to_json", "for<PERSON>ach", "x", "apiMaterialImportExcelMaterialListPost$Json", "CFile", "showImage", "imageUrl", "dialog", "changeFilter", "openImageBinder", "id", "lastModified", "Date", "trim", "searchTerm", "toLowerCase", "filter", "image", "includes", "index", "findIndex", "selected", "splice", "push", "some", "imagePreviewRef", "stopPropagation", "img", "imageNames", "map", "join", "saveImageBinding", "ɵɵdirectiveInject", "i1", "AllowHelper", "i2", "NbDialogService", "i3", "MessageService", "i4", "ValidationHelper", "i5", "BuildCaseService", "MaterialService", "i6", "UtilityService", "selectors", "standalone", "features", "ɵɵInheritDefinitionFeature", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "BuildingMaterialComponent_Template", "rf", "ctx", "BuildingMaterialComponent_Template_nb_select_ngModelChange_11_listener", "_r1", "BuildingMaterialComponent_nb_option_12_Template", "BuildingMaterialComponent_Template_input_ngModelChange_17_listener", "BuildingMaterialComponent_Template_nb_checkbox_checkedChange_20_listener", "BuildingMaterialComponent_Template_nb_checkbox_change_20_listener", "BuildingMaterialComponent_button_22_Template", "BuildingMaterialComponent_button_23_Template", "BuildingMaterialComponent_button_24_Template", "BuildingMaterialComponent_button_25_Template", "BuildingMaterialComponent_Template_input_change_26_listener", "BuildingMaterialComponent_Template_button_click_28_listener", "BuildingMaterialComponent_th_47_Template", "BuildingMaterialComponent_tbody_50_Template", "BuildingMaterialComponent_Template_ngx_pagination_CollectionSizeChange_52_listener", "BuildingMaterialComponent_Template_ngx_pagination_PageSizeChange_52_listener", "BuildingMaterialComponent_Template_ngx_pagination_PageChange_52_listener", "BuildingMaterialComponent_ng_template_53_Template", "ɵɵtemplateRefExtractor", "BuildingMaterialComponent_ng_template_55_Template", "BuildingMaterialComponent_ng_template_57_Template", "BuildingMaterialComponent_ng_template_59_Template", "isExcelExport", "isCreate", "isExcelImport", "i7", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "DecimalPipe", "i8", "DefaultValueAccessor", "NumberValueAccessor", "NgControlStatus", "MaxLengthValidator", "NgModel", "NbCardComponent", "NbCardBodyComponent", "NbCardFooterComponent", "NbCardHeaderComponent", "NbCheckboxComponent", "NbInputDirective", "NbSelectComponent", "NbOptionComponent", "i9", "BreadcrumbComponent", "i10", "PaginationComponent", "styles"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\selection-management\\building-material\\building-material.component.ts", "C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\selection-management\\building-material\\building-material.component.html"], "sourcesContent": ["import { Component, OnInit, TemplateRef, } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { NbDialogService } from '@nebular/theme';\r\nimport { MessageService } from 'src/app/shared/services/message.service';\r\nimport { ValidationHelper } from 'src/app/shared/helper/validationHelper';\r\nimport { BuildCaseService, MaterialService, PictureService } from 'src/services/api/services';\r\nimport { BuildCaseGetListReponse, SaveMaterialArgs, GetMaterialListResponse } from 'src/services/api/models';\r\nimport { finalize, mergeMap, tap } from 'rxjs';\r\nimport { AllowHelper } from 'src/app/shared/helper/allowHelper';\r\nimport { UtilityService } from 'src/app/shared/services/utility.service';\r\nimport * as XLSX from 'xlsx';\r\nimport { SharedModule } from '../../components/shared.module';\r\nimport { BaseComponent } from '../../components/base/baseComponent';\r\n\r\n// 圖片類別枚舉\r\nenum PictureCategory {\r\n  NONE = 0,           // 未選擇\r\n  BUILDING_MATERIAL = 1,  // 建材圖片\r\n  SCHEMATIC = 2       // 示意圖片\r\n}\r\n\r\n// 圖片項目介面\r\ninterface ImageItem {\r\n  id: string;\r\n  name: string;\r\n  size: number;\r\n  thumbnailUrl?: string;\r\n  fullUrl?: string;\r\n  lastModified?: Date;\r\n}\r\n\r\n@Component({\r\n  selector: 'ngx-building-material',\r\n  templateUrl: './building-material.component.html',\r\n  styleUrls: ['./building-material.component.scss'],\r\n  standalone: true,\r\n  imports: [CommonModule, SharedModule],\r\n})\r\n\r\nexport class BuildingMaterialComponent extends BaseComponent implements OnInit {\r\n  isNew = true\r\n\r\n  materialList: GetMaterialListResponse[]\r\n  selectedMaterial: GetMaterialListResponse\r\n\r\n  listBuildCases: BuildCaseGetListReponse[] = []\r\n  selectedBuildCaseId: number\r\n\r\n  materialOptions = [\r\n    {\r\n      value: null,\r\n      label: '全部',\r\n    },\r\n    {\r\n      value: false,\r\n      label: '方案',\r\n    },\r\n    {\r\n      value: true,\r\n      label: '選樣',\r\n    }];\r\n  materialOptionsId = null;\r\n  CSelectName: string = \"\"\r\n  // 移除圖片檔名相關欄位\r\n  // CImageCode: string = \"\"\r\n  // CInfoImageCode: string = \"\"\r\n  // 預留建材代號欄位，等後端API支援後再啟用\r\n  // CMaterialCode: string = \"\"\r\n  ShowPrice: boolean = false\r\n  currentImageShowing: string = \"\"\r\n  filterMapping: boolean = false\r\n  CIsMapping: boolean = true\r\n\r\n  // 圖片綁定相關屬性\r\n  availableImages: ImageItem[] = []\r\n  filteredImages: ImageItem[] = []\r\n  selectedImages: ImageItem[] = []\r\n  imageSearchTerm: string = \"\"\r\n  previewingImage: ImageItem | null = null\r\n  currentPreviewIndex: number = 0\r\n\r\n  constructor(\r\n    private _allow: AllowHelper,\r\n    private dialogService: NbDialogService,\r\n    private message: MessageService,\r\n    private valid: ValidationHelper,\r\n    private _buildCaseService: BuildCaseService,\r\n    private _materialService: MaterialService,\r\n    private _utilityService: UtilityService\r\n  ) {\r\n    super(_allow)\r\n  }\r\n\r\n  override ngOnInit(): void {\r\n    this.getListBuildCase()\r\n  }\r\n\r\n  getListBuildCase() {\r\n    this._buildCaseService.apiBuildCaseGetAllBuildCaseForSelectPost$Json({\r\n      body: {\r\n        CIsPagi: false,\r\n        CStatus: 1,\r\n      }\r\n    })\r\n      .pipe(\r\n        tap(res => {\r\n          if (res.StatusCode == 0) {\r\n            this.listBuildCases = res.Entries?.length ? res.Entries : []\r\n            this.selectedBuildCaseId = this.listBuildCases[0].cID!\r\n          }\r\n        }),\r\n        mergeMap(() => this.getMaterialList())\r\n      ).subscribe()\r\n  }\r\n  getMaterialList(pageIndex: number = 1) {\r\n    return this._materialService.apiMaterialGetMaterialListPost$Json({\r\n      body: {\r\n        CBuildCaseId: this.selectedBuildCaseId,\r\n        CPlanUse: this.materialOptionsId,\r\n        CSelectName: this.CSelectName,\r\n        // 移除圖片檔名相關查詢條件\r\n        // CImageCode: this.CImageCode,\r\n        // CInfoImageCode: this.CInfoImageCode,\r\n        PageSize: this.pageSize,\r\n        PageIndex: pageIndex,\r\n        CIsMapping: this.CIsMapping\r\n      }\r\n    }).pipe(\r\n      tap(res => {\r\n        if (res.StatusCode == 0) {\r\n          this.materialList = res.Entries! ?? []\r\n          this.totalRecords = res.TotalItems!\r\n\r\n          if (this.materialList.length > 0) {\r\n            this.ShowPrice = this.materialList[0].CShowPrice!;\r\n          }\r\n        }\r\n      })\r\n    )\r\n  }\r\n\r\n  search() {\r\n    this.getMaterialList().subscribe()\r\n  }\r\n\r\n  pageChanged(pageIndex: number) {\r\n    this.getMaterialList(pageIndex).subscribe()\r\n  }\r\n\r\n  exportExelMaterialList() {\r\n    this._materialService.apiMaterialExportExcelMaterialListPost$Json({\r\n      body: this.selectedBuildCaseId\r\n    }).subscribe(res => {\r\n      if (res.StatusCode == 0) {\r\n        if (res.Entries!.FileByte) {\r\n          this._utilityService.downloadExcelFile(res.Entries!.FileByte, '建材管理')\r\n        }\r\n      }\r\n    })\r\n  }\r\n\r\n  exportExelMaterialTemplate() {\r\n    this._materialService.apiMaterialExportExcelMaterialTemplatePost$Json({\r\n      body: this.selectedBuildCaseId\r\n    }).subscribe(res => {\r\n      if (res.StatusCode == 0) {\r\n        if (res.Entries!.FileByte) {\r\n          this._utilityService.downloadExcelFile(res.Entries!.FileByte, '建材管理')\r\n        }\r\n      }\r\n    })\r\n  }\r\n\r\n  addNew(ref: any) {\r\n    this.isNew = true\r\n    this.selectedMaterial = {}\r\n    this.dialogService.open(ref)\r\n  }\r\n  onSelectedMaterial(data: GetMaterialListResponse, ref: any) {\r\n    this.isNew = false\r\n    this.selectedMaterial = { ...data }\r\n    this.dialogService.open(ref)\r\n  }\r\n\r\n  bindImageForMaterial(data: GetMaterialListResponse, ref: TemplateRef<any>) {\r\n    this.selectedMaterial = { ...data }\r\n    this.loadImages()\r\n    this.dialogService.open(ref, { closeOnBackdropClick: false })\r\n  }\r\n  validation() {\r\n    this.valid.clear();\r\n    this.valid.required('[名稱]', this.selectedMaterial.CName)\r\n    this.valid.required('[項目]', this.selectedMaterial.CPart)\r\n    this.valid.required('[位置]', this.selectedMaterial.CLocation)\r\n    this.valid.required('[建材選項名稱]', this.selectedMaterial.CSelectName)\r\n    // 移除圖片檔名驗證\r\n    // this.valid.required('[建材圖片編號]', this.selectedMaterial.CImageCode)\r\n    this.valid.isStringMaxLength('[名稱]', this.selectedMaterial.CName, 30)\r\n    this.valid.isStringMaxLength('[項目]', this.selectedMaterial.CPart, 30)\r\n    this.valid.isStringMaxLength('[位置]', this.selectedMaterial.CLocation, 30)\r\n    this.valid.isStringMaxLength('[建材選項名稱]', this.selectedMaterial.CSelectName, 30)\r\n    // 移除圖片檔名長度驗證\r\n    // this.valid.isStringMaxLength('[建材圖片編號]', this.selectedMaterial.CImageCode, 30)\r\n  }\r\n\r\n  onSubmit(ref: any) {\r\n    this.validation()\r\n    if (this.valid.errorMessages.length > 0) {\r\n      this.message.showErrorMSGs(this.valid.errorMessages);\r\n      return\r\n    } this._materialService.apiMaterialSaveMaterialAdminPost$Json({\r\n      body: {\r\n        CBuildCaseId: this.selectedBuildCaseId,\r\n        // 暫時保留 CImageCode 給圖片綁定功能使用\r\n        CImageCode: this.selectedMaterial.CImageCode,\r\n        CName: this.selectedMaterial.CName,\r\n        CPart: this.selectedMaterial.CPart,\r\n        CLocation: this.selectedMaterial.CLocation,\r\n        CSelectName: this.selectedMaterial.CSelectName,\r\n        CDescription: this.selectedMaterial.CDescription,\r\n        CMaterialId: this.isNew ? null : this.selectedMaterial.CId!,\r\n        CPrice: this.selectedMaterial.CPrice\r\n      }\r\n    })\r\n      .pipe(\r\n        tap(res => {\r\n          if (res.StatusCode === 0) {\r\n            this.message.showSucessMSG(\"執行成功\");\r\n          } else {\r\n            this.message.showErrorMSG(res.Message!);\r\n          }\r\n        }),\r\n        mergeMap(() => this.getMaterialList()),\r\n        finalize(() => ref.close())\r\n      ).subscribe()\r\n  }\r\n\r\n  onClose(ref: any) {\r\n    ref.close();\r\n  }\r\n\r\n  detectFileExcel(event: any) {\r\n    const target: DataTransfer = <DataTransfer>(event.target);\r\n    const reader: FileReader = new FileReader();\r\n    reader.readAsBinaryString(target.files[0]);\r\n    reader.onload = (e: any) => {\r\n      const binarystr: string = e.target.result;\r\n      const wb: XLSX.WorkBook = XLSX.read(binarystr, { type: 'binary' });\r\n\r\n      const wsname: string = wb.SheetNames[0];\r\n      const ws: XLSX.WorkSheet = wb.Sheets[wsname];\r\n\r\n      let isValidFile: boolean = true;\r\n      const data = XLSX.utils.sheet_to_json(ws);\r\n      if (data && data.length > 0) {\r\n        data.forEach((x: any) => {\r\n          if (!(x['名稱'] && x['項目'] && x['位置'] && x['建材選項名稱'] &&\r\n            (x['建材圖片檔名'] || x['建材說明'] || x['示意圖片檔名']))) {\r\n            isValidFile = false;\r\n          }\r\n        })\r\n\r\n        if (!isValidFile) {\r\n          this.message.showErrorMSG(\"导入文件时出现错误\")\r\n        } else {\r\n          this._materialService.apiMaterialImportExcelMaterialListPost$Json({\r\n            body: {\r\n              CBuildCaseId: this.selectedBuildCaseId,\r\n              CFile: target.files[0]\r\n            }\r\n          }).pipe(\r\n            tap(res => {\r\n              if (res.StatusCode == 0) {\r\n                this.message.showSucessMSG(\"執行成功\")\r\n              } else {\r\n                this.message.showErrorMSG(res.Message!)\r\n              }\r\n            }),\r\n            mergeMap(() => this.getMaterialList(1))\r\n          ).subscribe();\r\n        }\r\n      } else {\r\n        this.message.showErrorMSG(\"匯入的檔案內容為空，請檢查檔案並重新上傳。\")\r\n      }\r\n      event.target.value = null;\r\n    };\r\n  }\r\n\r\n  showImage(imageUrl: string, dialog: TemplateRef<any>) {\r\n    this.currentImageShowing = imageUrl;\r\n    this.dialogService.open(dialog);\r\n  }\r\n  changeFilter() {\r\n    if (this.filterMapping) {\r\n      this.CIsMapping = false;\r\n      this.getMaterialList().subscribe();\r\n    }\r\n    else {\r\n      this.CIsMapping = true;\r\n      this.getMaterialList().subscribe();\r\n    }\r\n  }\r\n\r\n  // 圖片綁定功能方法\r\n  openImageBinder(ref: TemplateRef<any>) {\r\n    this.loadImages();\r\n    this.dialogService.open(ref, { closeOnBackdropClick: false });\r\n  }\r\n  loadImages() {\r\n    // 模擬載入更多圖片資料來測試滑動功能\r\n    this.availableImages = [\r\n      {\r\n        id: '1',\r\n        name: 'floor_tile_001.jpg',\r\n        size: 245,\r\n        thumbnailUrl: 'assets/images/materials/thumbs/floor_tile_001.jpg',\r\n        fullUrl: 'assets/images/materials/floor_tile_001.jpg',\r\n        lastModified: new Date()\r\n      },\r\n      {\r\n        id: '2',\r\n        name: 'wall_paint_002.jpg',\r\n        size: 189,\r\n        thumbnailUrl: 'assets/images/materials/thumbs/wall_paint_002.jpg',\r\n        fullUrl: 'assets/images/materials/wall_paint_002.jpg',\r\n        lastModified: new Date()\r\n      },\r\n      {\r\n        id: '3',\r\n        name: 'cabinet_wood_003.jpg',\r\n        size: 312,\r\n        thumbnailUrl: 'assets/images/materials/thumbs/cabinet_wood_003.jpg',\r\n        fullUrl: 'assets/images/materials/cabinet_wood_003.jpg',\r\n        lastModified: new Date()\r\n      },\r\n      {\r\n        id: '4',\r\n        name: 'lighting_fixture_004.jpg',\r\n        size: 156,\r\n        thumbnailUrl: 'assets/images/materials/thumbs/lighting_fixture_004.jpg',\r\n        fullUrl: 'assets/images/materials/lighting_fixture_004.jpg',\r\n        lastModified: new Date()\r\n      },\r\n      {\r\n        id: '5',\r\n        name: 'door_handle_005.jpg',\r\n        size: 78,\r\n        thumbnailUrl: 'assets/images/materials/thumbs/door_handle_005.jpg',\r\n        fullUrl: 'assets/images/materials/door_handle_005.jpg',\r\n        lastModified: new Date()\r\n      },\r\n      {\r\n        id: '6',\r\n        name: 'window_frame_006.jpg',\r\n        size: 267,\r\n        thumbnailUrl: 'assets/images/materials/thumbs/window_frame_006.jpg',\r\n        fullUrl: 'assets/images/materials/window_frame_006.jpg',\r\n        lastModified: new Date()\r\n      },\r\n      // 新增更多測試圖片來驗證滑動功能\r\n      {\r\n        id: '7',\r\n        name: 'ceramic_tile_007.jpg',\r\n        size: 234,\r\n        thumbnailUrl: 'assets/images/materials/thumbs/ceramic_tile_007.jpg',\r\n        fullUrl: 'assets/images/materials/ceramic_tile_007.jpg',\r\n        lastModified: new Date()\r\n      },\r\n      {\r\n        id: '8',\r\n        name: 'wooden_floor_008.jpg',\r\n        size: 298,\r\n        thumbnailUrl: 'assets/images/materials/thumbs/wooden_floor_008.jpg',\r\n        fullUrl: 'assets/images/materials/wooden_floor_008.jpg',\r\n        lastModified: new Date()\r\n      },\r\n      {\r\n        id: '9',\r\n        name: 'marble_counter_009.jpg',\r\n        size: 345,\r\n        thumbnailUrl: 'assets/images/materials/thumbs/marble_counter_009.jpg',\r\n        fullUrl: 'assets/images/materials/marble_counter_009.jpg',\r\n        lastModified: new Date()\r\n      },\r\n      {\r\n        id: '10',\r\n        name: 'glass_panel_010.jpg',\r\n        size: 187,\r\n        thumbnailUrl: 'assets/images/materials/thumbs/glass_panel_010.jpg',\r\n        fullUrl: 'assets/images/materials/glass_panel_010.jpg',\r\n        lastModified: new Date()\r\n      },\r\n      {\r\n        id: '11',\r\n        name: 'metal_fixture_011.jpg',\r\n        size: 156,\r\n        thumbnailUrl: 'assets/images/materials/thumbs/metal_fixture_011.jpg',\r\n        fullUrl: 'assets/images/materials/metal_fixture_011.jpg',\r\n        lastModified: new Date()\r\n      },\r\n      {\r\n        id: '12',\r\n        name: 'fabric_curtain_012.jpg',\r\n        size: 201,\r\n        thumbnailUrl: 'assets/images/materials/thumbs/fabric_curtain_012.jpg',\r\n        fullUrl: 'assets/images/materials/fabric_curtain_012.jpg',\r\n        lastModified: new Date()\r\n      }\r\n    ];\r\n\r\n    this.filteredImages = [...this.availableImages];\r\n\r\n    // TODO: 實際實作時，這裡應該呼叫 API 取得圖片列表\r\n    // this._materialService.apiMaterialGetImageListPost$Json({\r\n    //   body: { CBuildCaseId: this.selectedBuildCaseId }\r\n    // }).subscribe(res => {\r\n    //   if (res.StatusCode === 0) {\r\n    //     this.availableImages = res.Entries || [];\r\n    //     this.filteredImages = [...this.availableImages];\r\n    //   }\r\n    // });\r\n  }\r\n\r\n  filterImages() {\r\n    if (!this.imageSearchTerm.trim()) {\r\n      this.filteredImages = [...this.availableImages];\r\n    } else {\r\n      const searchTerm = this.imageSearchTerm.toLowerCase();\r\n      this.filteredImages = this.availableImages.filter(image =>\r\n        image.name.toLowerCase().includes(searchTerm)\r\n      );\r\n    }\r\n  }\r\n\r\n  toggleImageSelection(image: ImageItem) {\r\n    const index = this.selectedImages.findIndex(selected => selected.id === image.id);\r\n    if (index > -1) {\r\n      this.selectedImages.splice(index, 1);\r\n    } else {\r\n      this.selectedImages.push(image);\r\n    }\r\n  }\r\n\r\n  isImageSelected(image: ImageItem): boolean {\r\n    return this.selectedImages.some(selected => selected.id === image.id);\r\n  }\r\n\r\n  selectAllImages() {\r\n    this.selectedImages = [...this.filteredImages];\r\n  }\r\n\r\n  clearAllSelection() {\r\n    this.selectedImages = [];\r\n  } previewImage(image: ImageItem, imagePreviewRef: TemplateRef<any>, event: Event) {\r\n    event.stopPropagation();\r\n    this.previewingImage = image;\r\n    this.currentPreviewIndex = this.filteredImages.findIndex(img => img.id === image.id);\r\n    this.dialogService.open(imagePreviewRef);\r\n  }\r\n\r\n  previousImage() {\r\n    if (this.currentPreviewIndex > 0) {\r\n      this.currentPreviewIndex--;\r\n      this.previewingImage = this.filteredImages[this.currentPreviewIndex];\r\n    }\r\n  }\r\n\r\n  nextImage() {\r\n    if (this.currentPreviewIndex < this.filteredImages.length - 1) {\r\n      this.currentPreviewIndex++;\r\n      this.previewingImage = this.filteredImages[this.currentPreviewIndex];\r\n    }\r\n  }\r\n\r\n  toggleImageSelectionInPreview() {\r\n    if (this.previewingImage) {\r\n      this.toggleImageSelection(this.previewingImage);\r\n    }\r\n  } onConfirmImageSelection(ref: any) {\r\n    if (this.selectedImages.length > 0) {\r\n      // 如果只選取一張圖片，直接設定到建材圖片檔名\r\n      if (this.selectedImages.length === 1) {\r\n        this.selectedMaterial.CImageCode = this.selectedImages[0].name;\r\n      } else {\r\n        // 多張圖片的話，可以設定第一張，或者讓使用者選擇主要圖片\r\n        const imageNames = this.selectedImages.map(img => img.name).join(', ');\r\n        this.message.showSucessMSG(`已選取 ${this.selectedImages.length} 張圖片: ${imageNames}`);\r\n        this.selectedMaterial.CImageCode = this.selectedImages[0].name;\r\n      }\r\n\r\n      // 如果是從表格直接綁定圖片（有 CId），則直接保存到資料庫\r\n      if (this.selectedMaterial.CId) {\r\n        this.saveImageBinding();\r\n      }\r\n    }\r\n\r\n    this.clearAllSelection();\r\n    ref.close();\r\n  }\r\n\r\n  // 新增方法：保存圖片綁定\r\n  saveImageBinding() {\r\n    this._materialService.apiMaterialSaveMaterialAdminPost$Json({\r\n      body: {\r\n        CBuildCaseId: this.selectedBuildCaseId,\r\n        CImageCode: this.selectedMaterial.CImageCode,\r\n        CName: this.selectedMaterial.CName,\r\n        CPart: this.selectedMaterial.CPart,\r\n        CLocation: this.selectedMaterial.CLocation,\r\n        CSelectName: this.selectedMaterial.CSelectName,\r\n        CDescription: this.selectedMaterial.CDescription,\r\n        CMaterialId: this.selectedMaterial.CId!,\r\n        CPrice: this.selectedMaterial.CPrice\r\n      }\r\n    })\r\n      .pipe(\r\n        tap(res => {\r\n          if (res.StatusCode === 0) {\r\n            this.message.showSucessMSG(`圖片綁定成功: ${this.selectedMaterial.CImageCode}`);\r\n          } else {\r\n            this.message.showErrorMSG(res.Message!);\r\n          }\r\n        }),\r\n        mergeMap(() => this.getMaterialList()),\r\n        finalize(() => {\r\n          // 清空選取的建材\r\n          this.selectedMaterial = {};\r\n        })\r\n      ).subscribe()\r\n  }\r\n\r\n  onCloseImageBinder(ref: any) {\r\n    this.clearAllSelection();\r\n    this.imageSearchTerm = \"\";\r\n    ref.close();\r\n  }\r\n}\r\n", "<nb-card accent=\"success\">\r\n  <nb-card-header>\r\n    <ngx-breadcrumb></ngx-breadcrumb>\r\n  </nb-card-header>\r\n  <nb-card-body>\r\n    <h1 class=\"font-bold text-[#818181]\"> 可設定單筆或批次匯入設定各區域及方案對應之建材。\r\n    </h1>\r\n    <div class=\"d-flex flex-wrap\">\r\n      <div class=\"col-md-6\">\r\n        <div class=\"form-group d-flex align-items-center w-full\">\r\n          <label for=\"buildingName\" class=\"label mr-2 w-[22%]\">建案</label>\r\n          <nb-select placeholder=\"建案\" [(ngModel)]=\"selectedBuildCaseId\" class=\"w-full\">\r\n            <nb-option *ngFor=\"let buildCase of listBuildCases\" [value]=\"buildCase.cID\">\r\n              {{ buildCase.CBuildCaseName }}\r\n            </nb-option>\r\n          </nb-select>\r\n        </div>\r\n      </div>\r\n      <!-- <div class=\"col-md-6\">\r\n        <div class=\"form-group d-flex align-items-center w-full\">\r\n          <label for=\"buildingName\" class=\"label mr-2  w-[22%]\">建材類別</label>\r\n          <nb-select placeholder=\"建材類別\" [(ngModel)]=\"materialOptionsId\" class=\"w-full\">\r\n            <nb-option *ngFor=\"let option of materialOptions\" [value]=\"option.value\">\r\n              {{ option.label }}\r\n            </nb-option>\r\n          </nb-select>\r\n        </div>\r\n      </div> -->\r\n      <div class=\"col-md-6\">\r\n        <div class=\"form-group d-flex align-items-center w-full\">\r\n          <label for=\"buildingName\" class=\"label mr-2 w-[22%]\">建材選項名稱 </label>\r\n          <input type=\"text\" nbInput placeholder=\"建材選項名稱\" [(ngModel)]=\"CSelectName\" class=\"w-full\">\r\n        </div>\r\n      </div> <!-- 移除建材圖片檔名和示意圖片檔名欄位 -->\r\n      <!-- 預留建材代號欄位位置，等後端API支援後再啟用 -->\r\n      <!--\r\n      <div class=\"col-md-6\">\r\n        <div class=\"form-group d-flex align-items-center w-full\">\r\n          <label for=\"buildingName\" class=\"label mr-2 w-[22%]\">建材代號\r\n          </label>\r\n          <input type=\"text\" nbInput placeholder=\"建材代號\" [(ngModel)]=\"CMaterialCode\" class=\"w-full\">\r\n        </div>\r\n      </div>\r\n      -->\r\n      <div class=\"col-md-12\">\r\n        <div class=\"d-flex justify-content-end w-full\">\r\n          <nb-checkbox status=\"basic\" class=\"flex\" style=\"flex:auto\" [(checked)]=\"filterMapping\"\r\n            (change)=\"changeFilter()\">\r\n            只顯示缺少建材圖片或示意圖片的建材\r\n          </nb-checkbox>\r\n          <button *ngIf=\"isExcelExport\" class=\"btn btn-success mr-2\" (click)=\"exportExelMaterialList()\">匯出 <i\r\n              class=\"fas fa-file-download\"></i></button>\r\n          <button *ngIf=\"isRead\" class=\"btn btn-info mr-2 text-white ml-2\" (click)=\"search()\">\r\n            查詢 <i class=\"fas fa-search\"></i></button>\r\n          <button *ngIf=\"isCreate\" class=\"btn btn-info mx-1 ml-2 mr-2\" (click)=\"addNew(dialog)\">單筆新增 <i\r\n              class=\"fas fa-plus\"></i></button>\r\n          <button class=\"btn btn-info mx-1\" *ngIf=\"isExcelImport\" (click)=\"inputFile.click()\"> 批次匯入 </button>\r\n          <input class=\"hidden\" type=\"file\" accept=\".xls, .xlsx\" #inputFile (change)=\"detectFileExcel($event)\">\r\n          <button class=\"btn btn-success ml-2\" (click)=\"exportExelMaterialTemplate()\">下載範例檔案 <i\r\n              class=\"fas fa-file-download\"></i></button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <div class=\"table-responsive mt-4\">\r\n      <table class=\"table table-striped border \" style=\"min-width: 1200px; background-color:#f3f3f3;\">\r\n        <thead>\r\n          <tr style=\"background-color: #27ae60; color: white;\">\r\n            <th scope=\"col\" class=\"col-1\">項次</th>\r\n            <!-- 預留建材代號欄位，等後端API支援後再啟用 -->\r\n            <!-- <th scope=\"col\" class=\"col-1\">建材代號</th> -->\r\n            <th scope=\"col\" class=\"col-1\">名稱</th>\r\n            <th scope=\"col\" class=\"col-1\">項目</th>\r\n            <th scope=\"col\" class=\"col-1\">位置</th>\r\n            <th scope=\"col\" class=\"col-1\">選項名稱</th>\r\n            <th scope=\"col\" class=\"col-3\">建材說明</th>\r\n            <th scope=\"col\" class=\"col-1\" *ngIf=\"ShowPrice == true\">價格</th>\r\n            <th scope=\"col\" class=\"col-1 text-center\">操作</th>\r\n          </tr>\r\n        </thead>\r\n        <tbody *ngIf=\"materialList != null && materialList.length > 0\">\r\n          <tr *ngFor=\"let item of materialList ; let i = index\">\r\n            <td>{{ item.CId}}</td>\r\n            <!-- 預留建材代號欄位，等後端API支援後再啟用 -->\r\n            <!-- <td>{{ item.CMaterialCode}}</td> -->\r\n            <td>{{ item.CName}}</td>\r\n            <td>{{ item.CPart}}</td>\r\n            <td>{{ item.CLocation}}</td>\r\n            <td [style]=\"!item.CIsMapping ? 'color: red' : ''\">{{ item.CSelectName}}</td>\r\n            <td>{{ item.CDescription}}</td>\r\n            <td *ngIf=\"item.CShowPrice == true\">{{ item.CPrice}}</td>\r\n            <td class=\"text-center w-32\">\r\n              <button class=\"btn btn-outline-primary btn-sm m-1\" (click)=\"onSelectedMaterial(item, dialog)\"\r\n                *ngIf=\"isRead\">編輯</button>\r\n              <button class=\"btn btn-outline-info btn-sm m-1\" (click)=\"bindImageForMaterial(item, imageBinder)\"\r\n                *ngIf=\"isRead\" [title]=\"'為 ' + item.CName + ' 綁定圖片'\">\r\n                <i class=\"fas fa-images\"></i>\r\n              </button>\r\n            </td>\r\n          </tr>\r\n        </tbody>\r\n      </table>\r\n    </div>\r\n  </nb-card-body>\r\n  <nb-card-footer class=\"d-flex justify-content-center\">\r\n    <ngx-pagination [(CollectionSize)]=\"totalRecords\" [(PageSize)]=\"pageSize\" [(Page)]=\"pageIndex\"\r\n      (PageChange)=\"pageChanged($event)\">\r\n    </ngx-pagination>\r\n  </nb-card-footer>\r\n</nb-card>\r\n\r\n<ng-template #dialog let-dialog let-ref=\"dialogRef\">\r\n  <nb-card class=\"w-[700px]\">\r\n    <nb-card-header>\r\n      建材管理 > 新增建材\r\n    </nb-card-header>\r\n    <nb-card-body class=\"px-4\">\r\n      <h5 class=\"text-base\">請輸入下方內容新增建材。</h5>\r\n      <div class=\"w-full mt-3\">\r\n        <!-- 預留建材代號欄位，等後端API支援後再啟用 -->\r\n        <!--\r\n        <div class=\"flex items-center\">\r\n          <label class=\"required-field w-[150px]\">建材代號</label>\r\n          <input type=\"text\" class=\"w-full !max-w-full p-2 rounded text-[13px]\" nbInput maxlength=\"30\"\r\n            [(ngModel)]=\"selectedMaterial.CMaterialCode\" />\r\n        </div>\r\n        -->\r\n\r\n        <div class=\"flex items-center mt-3\">\r\n          <label class=\"required-field w-[150px]\">名稱</label>\r\n          <input type=\"text\" class=\"w-full !max-w-full p-2 rounded text-[13px]\" nbInput maxlength=\"30\"\r\n            [(ngModel)]=\"selectedMaterial.CName\" />\r\n        </div>\r\n\r\n        <div class=\"flex items-center mt-3\">\r\n          <label class=\"required-field w-[150px]\">項目</label>\r\n          <input type=\"text\" class=\"w-full !max-w-full p-2 rounded text-[13px]\" nbInput maxlength=\"30\"\r\n            [(ngModel)]=\"selectedMaterial.CPart\" />\r\n        </div>\r\n        <div class=\"flex items-center mt-3\">\r\n          <label class=\"required-field w-[150px]\">位置</label>\r\n          <input type=\"text\" class=\"w-full !max-w-full p-2 rounded text-[13px]\" nbInput maxlength=\"30\"\r\n            [(ngModel)]=\"selectedMaterial.CLocation\" />\r\n        </div>\r\n\r\n        <div class=\"flex items-center mt-3\">\r\n          <label class=\"required-field w-[150px]\">建材選項名稱</label>\r\n          <input type=\"text\" nbInput class=\"w-full !max-w-full p-2 rounded text-[13px]\" maxlength=\"30\"\r\n            [(ngModel)]=\"selectedMaterial.CSelectName\" />\r\n        </div>\r\n\r\n        <div class=\"flex items-center mt-3\">\r\n          <label class=\"w-[150px]\">建材說明</label>\r\n          <textarea nbInput [(ngModel)]=\"selectedMaterial.CDescription\" [rows]=\"4\"\r\n            class=\"resize-none w-full !max-w-full p-2 rounded text-[13px]\"></textarea>\r\n        </div>\r\n\r\n        <div class=\"flex items-center mt-3\">\r\n          <label class=\"w-[150px]\">價格</label>\r\n          <input type=\"number\" nbInput class=\"w-full !max-w-full p-2 rounded text-[13px]\" maxlength=\"30\"\r\n            [(ngModel)]=\"selectedMaterial.CPrice\" />\r\n        </div>\r\n      </div>\r\n    </nb-card-body>\r\n    <nb-card-footer class=\"d-flex justify-content-center\">\r\n      <button class=\"btn btn-danger btn-sm mr-4\" (click)=\"onClose(ref)\">關閉</button>\r\n      <button class=\"btn btn-success btn-sm\" (click)=\"onSubmit(ref)\">儲存</button>\r\n    </nb-card-footer>\r\n  </nb-card>\r\n</ng-template>\r\n\r\n<ng-template #imageBinder let-dialog let-ref=\"dialogRef\"> <nb-card class=\"w-[900px] h-[700px]\">\r\n    <nb-card-header>\r\n      圖片綁定 - {{ selectedMaterial.CName ? '為 ' + selectedMaterial.CName + ' 選擇建材圖片' : '選擇建材圖片' }}\r\n    </nb-card-header>\r\n    <nb-card-body class=\"px-4 d-flex flex-column\"\r\n      style=\"height: calc(700px - 120px); overflow: hidden; padding-bottom: 0;\">\r\n      <!-- 搜尋功能 -->\r\n      <div class=\"flex gap-3 mb-4 flex-shrink-0\">\r\n        <div class=\"flex-1\">\r\n          <input type=\"text\" class=\"w-full search-input\" placeholder=\"搜尋圖片名稱...\" [(ngModel)]=\"imageSearchTerm\"\r\n            (input)=\"filterImages()\" />\r\n        </div>\r\n        <button class=\"btn btn-info btn-image-action\" (click)=\"loadImages()\">\r\n          重新載入 <i class=\"fas fa-refresh\"></i>\r\n        </button>\r\n      </div> <!-- 批次操作按鈕 -->\r\n      <div class=\"flex gap-2 mb-3 flex-shrink-0\">\r\n        <button class=\"btn btn-outline-primary btn-sm\" (click)=\"selectAllImages()\">\r\n          全選 <i class=\"fas fa-check-square\"></i>\r\n        </button>\r\n        <button class=\"btn btn-outline-secondary btn-sm\" (click)=\"clearAllSelection()\">\r\n          清除選取 <i class=\"fas fa-square\"></i>\r\n        </button>\r\n        <div class=\"ml-auto text-sm text-gray-600\">\r\n          已選取: {{ selectedImages.length }} 張圖片\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 圖片列表 -->\r\n      <div class=\"image-preview-container border rounded p-3 flex-1\">\r\n        <div class=\"grid grid-cols-4 gap-3\">\r\n          <div *ngFor=\"let image of filteredImages\" class=\"image-grid-item border rounded p-2 cursor-pointer\"\r\n            [class.selected]=\"isImageSelected(image)\" (click)=\"toggleImageSelection(image)\">\r\n\r\n            <!-- 選取指示器 -->\r\n            <div class=\"flex justify-between items-center mb-2\">\r\n              <div class=\"image-checkbox w-5 h-5 border-2 rounded flex items-center justify-center\"\r\n                [class.checked]=\"isImageSelected(image)\">\r\n                <i *ngIf=\"isImageSelected(image)\" class=\"fas fa-check text-white text-xs\"></i>\r\n              </div>\r\n              <button class=\"btn btn-outline-info btn-xs btn-image-action\"\r\n                (click)=\"previewImage(image, imagePreview, $event)\">\r\n                <i class=\"fas fa-eye\"></i>\r\n              </button>\r\n            </div>\r\n\r\n            <!-- 圖片預覽 -->\r\n            <div class=\"w-full h-32 bg-gray-100 rounded mb-2 flex items-center justify-center overflow-hidden\">\r\n              <img *ngIf=\"image.thumbnailUrl\" [src]=\"image.thumbnailUrl\" [alt]=\"image.name\"\r\n                class=\"image-thumbnail max-w-full max-h-full object-contain\" />\r\n              <div *ngIf=\"!image.thumbnailUrl\" class=\"text-gray-400 text-center\">\r\n                <i class=\"fas fa-image text-2xl mb-1\"></i>\r\n                <div class=\"text-xs\">無預覽</div>\r\n              </div>\r\n            </div>\r\n\r\n            <!-- 圖片資訊 -->\r\n            <div class=\"text-xs text-gray-600\">\r\n              <div class=\"font-medium truncate\" [title]=\"image.name\">{{ image.name }}</div>\r\n              <div class=\"text-gray-400\">{{ image.size | number }} KB</div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 空狀態 -->\r\n        <div *ngIf=\"filteredImages.length === 0\" class=\"text-center text-gray-500 py-20\">\r\n          <i class=\"fas fa-images text-4xl mb-3\"></i>\r\n          <div>找不到相符的圖片</div>\r\n        </div>\r\n      </div>\r\n    </nb-card-body>\r\n    <nb-card-footer class=\"d-flex justify-content-between align-items-center\">\r\n      <div class=\"text-sm text-gray-600\">\r\n        共 {{ availableImages.length }} 張圖片\r\n      </div>\r\n      <div class=\"d-flex gap-2\">\r\n        <button class=\"btn btn-danger btn-sm\" (click)=\"onCloseImageBinder(ref)\">取消</button>\r\n        <button class=\"btn btn-success btn-sm\" [disabled]=\"selectedImages.length === 0\"\r\n          (click)=\"onConfirmImageSelection(ref)\">\r\n          確定選擇 ({{ selectedImages.length }})\r\n        </button>\r\n      </div>\r\n    </nb-card-footer>\r\n  </nb-card>\r\n</ng-template>\r\n\r\n<!-- 圖片預覽對話框 -->\r\n<ng-template #imagePreview let-dialog let-ref=\"dialogRef\">\r\n  <nb-card class=\"w-[800px] h-[600px]\">\r\n    <nb-card-header class=\"d-flex justify-content-between align-items-center\">\r\n      <span>圖片預覽 - {{ previewingImage?.name }}</span>\r\n      <div class=\"d-flex gap-2\">\r\n        <button class=\"btn btn-outline-primary btn-sm\" [disabled]=\"currentPreviewIndex <= 0\" (click)=\"previousImage()\">\r\n          <i class=\"fas fa-chevron-left\"></i> 上一張\r\n        </button>\r\n        <button class=\"btn btn-outline-primary btn-sm\" [disabled]=\"currentPreviewIndex >= filteredImages.length - 1\"\r\n          (click)=\"nextImage()\">\r\n          下一張 <i class=\"fas fa-chevron-right\"></i>\r\n        </button>\r\n      </div>\r\n    </nb-card-header> <nb-card-body class=\"p-0 d-flex justify-content-center align-items-center\" style=\"height: 500px;\">\r\n      <img *ngIf=\"previewingImage && previewingImage.fullUrl\" [src]=\"previewingImage.fullUrl\"\r\n        [alt]=\"previewingImage.name\" class=\"max-w-full max-h-full object-contain\" />\r\n      <div *ngIf=\"!previewingImage || !previewingImage.fullUrl\" class=\"text-gray-400 text-center\">\r\n        <i class=\"fas fa-image text-4xl mb-3\"></i>\r\n        <div>圖片載入失敗</div>\r\n      </div>\r\n    </nb-card-body>\r\n    <nb-card-footer class=\"d-flex justify-content-between align-items-center\">\r\n      <div class=\"text-sm text-gray-600\">\r\n        {{ currentPreviewIndex + 1 }} / {{ filteredImages.length }}\r\n      </div>\r\n      <div class=\"d-flex gap-2\"> <button class=\"btn btn-outline-info btn-sm\" (click)=\"toggleImageSelectionInPreview()\">\r\n          {{ (previewingImage && isImageSelected(previewingImage)) ? '取消選取' : '選取此圖片' }}\r\n        </button>\r\n        <button class=\"btn btn-danger btn-sm\" (click)=\"ref.close()\">關閉</button>\r\n      </div>\r\n    </nb-card-footer>\r\n  </nb-card>\r\n</ng-template>\r\n\r\n<!-- 原有的圖片檢視對話框 -->\r\n<ng-template #dialogImage let-data let-ref=\"dialogRef\">\r\n  <nb-card style=\"height: 100%; overflow: auto; width: 700px;\" class=\"mr-md-5 ml-md-5\">\r\n    <nb-card-header>\r\n      <span>\r\n        檢視\r\n      </span>\r\n    </nb-card-header>\r\n    <nb-card-body style=\"padding:1rem 2rem\">\r\n      <div class=\"w-full h-auto\">\r\n        <img class=\"fit-size\" [src]=\"currentImageShowing\">\r\n      </div>\r\n    </nb-card-body>\r\n    <nb-card-footer>\r\n      <div class=\"flex justify-center items-center\">\r\n        <button class=\"btn btn-danger mr-2\" (click)=\"ref.close()\">取消</button>\r\n      </div>\r\n    </nb-card-footer>\r\n  </nb-card>\r\n</ng-template>\r\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAM9C,SAASC,QAAQ,EAAEC,QAAQ,EAAEC,GAAG,QAAQ,MAAM;AAG9C,OAAO,KAAKC,IAAI,MAAM,MAAM;AAC5B,SAASC,YAAY,QAAQ,gCAAgC;AAC7D,SAASC,aAAa,QAAQ,qCAAqC;;;;;;;;;;;;;;ICAvDC,EAAA,CAAAC,cAAA,oBAA4E;IAC1ED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAFwCH,EAAA,CAAAI,UAAA,UAAAC,YAAA,CAAAC,GAAA,CAAuB;IACzEN,EAAA,CAAAO,SAAA,EACF;IADEP,EAAA,CAAAQ,kBAAA,MAAAH,YAAA,CAAAI,cAAA,MACF;;;;;;IAoCFT,EAAA,CAAAC,cAAA,iBAA8F;IAAnCD,EAAA,CAAAU,UAAA,mBAAAC,qEAAA;MAAAX,EAAA,CAAAY,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CAASF,MAAA,CAAAG,sBAAA,EAAwB;IAAA,EAAC;IAACjB,EAAA,CAAAE,MAAA,oBAAG;IAAAF,EAAA,CAAAkB,SAAA,YAC5D;IAAAlB,EAAA,CAAAG,YAAA,EAAS;;;;;;IAC9CH,EAAA,CAAAC,cAAA,iBAAoF;IAAnBD,EAAA,CAAAU,UAAA,mBAAAS,qEAAA;MAAAnB,EAAA,CAAAY,aAAA,CAAAQ,GAAA;MAAA,MAAAN,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CAASF,MAAA,CAAAO,MAAA,EAAQ;IAAA,EAAC;IACjFrB,EAAA,CAAAE,MAAA,qBAAG;IAAAF,EAAA,CAAAkB,SAAA,YAA6B;IAAAlB,EAAA,CAAAG,YAAA,EAAS;;;;;;IAC3CH,EAAA,CAAAC,cAAA,iBAAsF;IAAzBD,EAAA,CAAAU,UAAA,mBAAAY,qEAAA;MAAAtB,EAAA,CAAAY,aAAA,CAAAW,GAAA;MAAA,MAAAT,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,MAAAS,SAAA,GAAAxB,EAAA,CAAAyB,WAAA;MAAA,OAAAzB,EAAA,CAAAgB,WAAA,CAASF,MAAA,CAAAY,MAAA,CAAAF,SAAA,CAAc;IAAA,EAAC;IAACxB,EAAA,CAAAE,MAAA,gCAAK;IAAAF,EAAA,CAAAkB,SAAA,YAC/D;IAAAlB,EAAA,CAAAG,YAAA,EAAS;;;;;;IACrCH,EAAA,CAAAC,cAAA,iBAAoF;IAA5BD,EAAA,CAAAU,UAAA,mBAAAiB,qEAAA;MAAA3B,EAAA,CAAAY,aAAA,CAAAgB,GAAA;MAAA5B,EAAA,CAAAe,aAAA;MAAA,MAAAc,YAAA,GAAA7B,EAAA,CAAAyB,WAAA;MAAA,OAAAzB,EAAA,CAAAgB,WAAA,CAASa,YAAA,CAAAC,KAAA,EAAiB;IAAA,EAAC;IAAE9B,EAAA,CAAAE,MAAA,iCAAK;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;IAmBjGH,EAAA,CAAAC,cAAA,aAAwD;IAAAD,EAAA,CAAAE,MAAA,mBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;;IAc/DH,EAAA,CAAAC,cAAA,SAAoC;IAAAD,EAAA,CAAAE,MAAA,GAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;IAArBH,EAAA,CAAAO,SAAA,EAAgB;IAAhBP,EAAA,CAAA+B,iBAAA,CAAAC,QAAA,CAAAC,MAAA,CAAgB;;;;;;IAElDjC,EAAA,CAAAC,cAAA,iBACiB;IADkCD,EAAA,CAAAU,UAAA,mBAAAwB,mFAAA;MAAAlC,EAAA,CAAAY,aAAA,CAAAuB,IAAA;MAAA,MAAAH,QAAA,GAAAhC,EAAA,CAAAe,aAAA,GAAAqB,SAAA;MAAA,MAAAtB,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,MAAAS,SAAA,GAAAxB,EAAA,CAAAyB,WAAA;MAAA,OAAAzB,EAAA,CAAAgB,WAAA,CAASF,MAAA,CAAAuB,kBAAA,CAAAL,QAAA,EAAAR,SAAA,CAAgC;IAAA,EAAC;IAC5ExB,EAAA,CAAAE,MAAA,mBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;IAC5BH,EAAA,CAAAC,cAAA,iBACuD;IADPD,EAAA,CAAAU,UAAA,mBAAA4B,mFAAA;MAAAtC,EAAA,CAAAY,aAAA,CAAA2B,IAAA;MAAA,MAAAP,QAAA,GAAAhC,EAAA,CAAAe,aAAA,GAAAqB,SAAA;MAAA,MAAAtB,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,MAAAyB,eAAA,GAAAxC,EAAA,CAAAyB,WAAA;MAAA,OAAAzB,EAAA,CAAAgB,WAAA,CAASF,MAAA,CAAA2B,oBAAA,CAAAT,QAAA,EAAAQ,eAAA,CAAuC;IAAA,EAAC;IAE/FxC,EAAA,CAAAkB,SAAA,YAA6B;IAC/BlB,EAAA,CAAAG,YAAA,EAAS;;;;IAFQH,EAAA,CAAAI,UAAA,sBAAA4B,QAAA,CAAAU,KAAA,+BAAqC;;;;;IAbxD1C,EADF,CAAAC,cAAA,SAAsD,SAChD;IAAAD,EAAA,CAAAE,MAAA,GAAa;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAGtBH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,GAAe;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACxBH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,GAAe;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACxBH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,GAAmB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC5BH,EAAA,CAAAC,cAAA,SAAmD;IAAAD,EAAA,CAAAE,MAAA,IAAqB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC7EH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAE,MAAA,IAAsB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC/BH,EAAA,CAAA2C,UAAA,KAAAC,sDAAA,iBAAoC;IACpC5C,EAAA,CAAAC,cAAA,cAA6B;IAG3BD,EAFA,CAAA2C,UAAA,KAAAE,0DAAA,qBACiB,KAAAC,0DAAA,qBAEsC;IAI3D9C,EADE,CAAAG,YAAA,EAAK,EACF;;;;;IAjBCH,EAAA,CAAAO,SAAA,GAAa;IAAbP,EAAA,CAAA+B,iBAAA,CAAAC,QAAA,CAAAe,GAAA,CAAa;IAGb/C,EAAA,CAAAO,SAAA,GAAe;IAAfP,EAAA,CAAA+B,iBAAA,CAAAC,QAAA,CAAAU,KAAA,CAAe;IACf1C,EAAA,CAAAO,SAAA,GAAe;IAAfP,EAAA,CAAA+B,iBAAA,CAAAC,QAAA,CAAAgB,KAAA,CAAe;IACfhD,EAAA,CAAAO,SAAA,GAAmB;IAAnBP,EAAA,CAAA+B,iBAAA,CAAAC,QAAA,CAAAiB,SAAA,CAAmB;IACnBjD,EAAA,CAAAO,SAAA,EAA8C;IAA9CP,EAAA,CAAAkD,UAAA,EAAAlB,QAAA,CAAAmB,UAAA,qBAA8C;IAACnD,EAAA,CAAAO,SAAA,EAAqB;IAArBP,EAAA,CAAA+B,iBAAA,CAAAC,QAAA,CAAAoB,WAAA,CAAqB;IACpEpD,EAAA,CAAAO,SAAA,GAAsB;IAAtBP,EAAA,CAAA+B,iBAAA,CAAAC,QAAA,CAAAqB,YAAA,CAAsB;IACrBrD,EAAA,CAAAO,SAAA,EAA6B;IAA7BP,EAAA,CAAAI,UAAA,SAAA4B,QAAA,CAAAsB,UAAA,SAA6B;IAG7BtD,EAAA,CAAAO,SAAA,GAAY;IAAZP,EAAA,CAAAI,UAAA,SAAAU,MAAA,CAAAyC,MAAA,CAAY;IAEZvD,EAAA,CAAAO,SAAA,EAAY;IAAZP,EAAA,CAAAI,UAAA,SAAAU,MAAA,CAAAyC,MAAA,CAAY;;;;;IAfrBvD,EAAA,CAAAC,cAAA,YAA+D;IAC7DD,EAAA,CAAA2C,UAAA,IAAAa,gDAAA,mBAAsD;IAmBxDxD,EAAA,CAAAG,YAAA,EAAQ;;;;IAnBeH,EAAA,CAAAO,SAAA,EAAkB;IAAlBP,EAAA,CAAAI,UAAA,YAAAU,MAAA,CAAA2C,YAAA,CAAkB;;;;;;IAgC7CzD,EADF,CAAAC,cAAA,kBAA2B,qBACT;IACdD,EAAA,CAAAE,MAAA,4DACF;IAAAF,EAAA,CAAAG,YAAA,EAAiB;IAEfH,EADF,CAAAC,cAAA,uBAA2B,aACH;IAAAD,EAAA,CAAAE,MAAA,+EAAY;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAYnCH,EAXJ,CAAAC,cAAA,cAAyB,cAUa,gBACM;IAAAD,EAAA,CAAAE,MAAA,mBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAClDH,EAAA,CAAAC,cAAA,iBACyC;IAAvCD,EAAA,CAAA0D,gBAAA,2BAAAC,kFAAAC,MAAA;MAAA5D,EAAA,CAAAY,aAAA,CAAAiD,IAAA;MAAA,MAAA/C,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAAf,EAAA,CAAA8D,kBAAA,CAAAhD,MAAA,CAAAiD,gBAAA,CAAArB,KAAA,EAAAkB,MAAA,MAAA9C,MAAA,CAAAiD,gBAAA,CAAArB,KAAA,GAAAkB,MAAA;MAAA,OAAA5D,EAAA,CAAAgB,WAAA,CAAA4C,MAAA;IAAA,EAAoC;IACxC5D,EAFE,CAAAG,YAAA,EACyC,EACrC;IAGJH,EADF,CAAAC,cAAA,eAAoC,iBACM;IAAAD,EAAA,CAAAE,MAAA,oBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAClDH,EAAA,CAAAC,cAAA,iBACyC;IAAvCD,EAAA,CAAA0D,gBAAA,2BAAAM,kFAAAJ,MAAA;MAAA5D,EAAA,CAAAY,aAAA,CAAAiD,IAAA;MAAA,MAAA/C,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAAf,EAAA,CAAA8D,kBAAA,CAAAhD,MAAA,CAAAiD,gBAAA,CAAAf,KAAA,EAAAY,MAAA,MAAA9C,MAAA,CAAAiD,gBAAA,CAAAf,KAAA,GAAAY,MAAA;MAAA,OAAA5D,EAAA,CAAAgB,WAAA,CAAA4C,MAAA;IAAA,EAAoC;IACxC5D,EAFE,CAAAG,YAAA,EACyC,EACrC;IAEJH,EADF,CAAAC,cAAA,eAAoC,iBACM;IAAAD,EAAA,CAAAE,MAAA,oBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAClDH,EAAA,CAAAC,cAAA,iBAC6C;IAA3CD,EAAA,CAAA0D,gBAAA,2BAAAO,kFAAAL,MAAA;MAAA5D,EAAA,CAAAY,aAAA,CAAAiD,IAAA;MAAA,MAAA/C,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAAf,EAAA,CAAA8D,kBAAA,CAAAhD,MAAA,CAAAiD,gBAAA,CAAAd,SAAA,EAAAW,MAAA,MAAA9C,MAAA,CAAAiD,gBAAA,CAAAd,SAAA,GAAAW,MAAA;MAAA,OAAA5D,EAAA,CAAAgB,WAAA,CAAA4C,MAAA;IAAA,EAAwC;IAC5C5D,EAFE,CAAAG,YAAA,EAC6C,EACzC;IAGJH,EADF,CAAAC,cAAA,eAAoC,iBACM;IAAAD,EAAA,CAAAE,MAAA,4CAAM;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACtDH,EAAA,CAAAC,cAAA,iBAC+C;IAA7CD,EAAA,CAAA0D,gBAAA,2BAAAQ,kFAAAN,MAAA;MAAA5D,EAAA,CAAAY,aAAA,CAAAiD,IAAA;MAAA,MAAA/C,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAAf,EAAA,CAAA8D,kBAAA,CAAAhD,MAAA,CAAAiD,gBAAA,CAAAX,WAAA,EAAAQ,MAAA,MAAA9C,MAAA,CAAAiD,gBAAA,CAAAX,WAAA,GAAAQ,MAAA;MAAA,OAAA5D,EAAA,CAAAgB,WAAA,CAAA4C,MAAA;IAAA,EAA0C;IAC9C5D,EAFE,CAAAG,YAAA,EAC+C,EAC3C;IAGJH,EADF,CAAAC,cAAA,eAAoC,iBACT;IAAAD,EAAA,CAAAE,MAAA,gCAAI;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACrCH,EAAA,CAAAC,cAAA,oBACiE;IAD/CD,EAAA,CAAA0D,gBAAA,2BAAAS,qFAAAP,MAAA;MAAA5D,EAAA,CAAAY,aAAA,CAAAiD,IAAA;MAAA,MAAA/C,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAAf,EAAA,CAAA8D,kBAAA,CAAAhD,MAAA,CAAAiD,gBAAA,CAAAV,YAAA,EAAAO,MAAA,MAAA9C,MAAA,CAAAiD,gBAAA,CAAAV,YAAA,GAAAO,MAAA;MAAA,OAAA5D,EAAA,CAAAgB,WAAA,CAAA4C,MAAA;IAAA,EAA2C;IAE/D5D,EADmE,CAAAG,YAAA,EAAW,EACxE;IAGJH,EADF,CAAAC,cAAA,eAAoC,iBACT;IAAAD,EAAA,CAAAE,MAAA,oBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACnCH,EAAA,CAAAC,cAAA,iBAC0C;IAAxCD,EAAA,CAAA0D,gBAAA,2BAAAU,kFAAAR,MAAA;MAAA5D,EAAA,CAAAY,aAAA,CAAAiD,IAAA;MAAA,MAAA/C,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAAf,EAAA,CAAA8D,kBAAA,CAAAhD,MAAA,CAAAiD,gBAAA,CAAA9B,MAAA,EAAA2B,MAAA,MAAA9C,MAAA,CAAAiD,gBAAA,CAAA9B,MAAA,GAAA2B,MAAA;MAAA,OAAA5D,EAAA,CAAAgB,WAAA,CAAA4C,MAAA;IAAA,EAAqC;IAG7C5D,EAJM,CAAAG,YAAA,EAC0C,EACtC,EACF,EACO;IAEbH,EADF,CAAAC,cAAA,0BAAsD,kBACc;IAAvBD,EAAA,CAAAU,UAAA,mBAAA2D,2EAAA;MAAA,MAAAC,OAAA,GAAAtE,EAAA,CAAAY,aAAA,CAAAiD,IAAA,EAAAU,SAAA;MAAA,MAAAzD,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CAASF,MAAA,CAAA0D,OAAA,CAAAF,OAAA,CAAY;IAAA,EAAC;IAACtE,EAAA,CAAAE,MAAA,oBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAC7EH,EAAA,CAAAC,cAAA,kBAA+D;IAAxBD,EAAA,CAAAU,UAAA,mBAAA+D,2EAAA;MAAA,MAAAH,OAAA,GAAAtE,EAAA,CAAAY,aAAA,CAAAiD,IAAA,EAAAU,SAAA;MAAA,MAAAzD,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CAASF,MAAA,CAAA4D,QAAA,CAAAJ,OAAA,CAAa;IAAA,EAAC;IAACtE,EAAA,CAAAE,MAAA,oBAAE;IAErEF,EAFqE,CAAAG,YAAA,EAAS,EAC3D,EACT;;;;IArCAH,EAAA,CAAAO,SAAA,IAAoC;IAApCP,EAAA,CAAA2E,gBAAA,YAAA7D,MAAA,CAAAiD,gBAAA,CAAArB,KAAA,CAAoC;IAMpC1C,EAAA,CAAAO,SAAA,GAAoC;IAApCP,EAAA,CAAA2E,gBAAA,YAAA7D,MAAA,CAAAiD,gBAAA,CAAAf,KAAA,CAAoC;IAKpChD,EAAA,CAAAO,SAAA,GAAwC;IAAxCP,EAAA,CAAA2E,gBAAA,YAAA7D,MAAA,CAAAiD,gBAAA,CAAAd,SAAA,CAAwC;IAMxCjD,EAAA,CAAAO,SAAA,GAA0C;IAA1CP,EAAA,CAAA2E,gBAAA,YAAA7D,MAAA,CAAAiD,gBAAA,CAAAX,WAAA,CAA0C;IAK1BpD,EAAA,CAAAO,SAAA,GAA2C;IAA3CP,EAAA,CAAA2E,gBAAA,YAAA7D,MAAA,CAAAiD,gBAAA,CAAAV,YAAA,CAA2C;IAACrD,EAAA,CAAAI,UAAA,WAAU;IAOtEJ,EAAA,CAAAO,SAAA,GAAqC;IAArCP,EAAA,CAAA2E,gBAAA,YAAA7D,MAAA,CAAAiD,gBAAA,CAAA9B,MAAA,CAAqC;;;;;IAiDjCjC,EAAA,CAAAkB,SAAA,YAA8E;;;;;IAUhFlB,EAAA,CAAAkB,SAAA,cACiE;;;;IADNlB,EAA3B,CAAAI,UAAA,QAAAwE,SAAA,CAAAC,YAAA,EAAA7E,EAAA,CAAA8E,aAAA,CAA0B,QAAAF,SAAA,CAAAG,IAAA,CAAmB;;;;;IAE7E/E,EAAA,CAAAC,cAAA,cAAmE;IACjED,EAAA,CAAAkB,SAAA,YAA0C;IAC1ClB,EAAA,CAAAC,cAAA,cAAqB;IAAAD,EAAA,CAAAE,MAAA,yBAAG;IAC1BF,EAD0B,CAAAG,YAAA,EAAM,EAC1B;;;;;;IAtBVH,EAAA,CAAAC,cAAA,cACkF;IAAtCD,EAAA,CAAAU,UAAA,mBAAAsE,8EAAA;MAAA,MAAAJ,SAAA,GAAA5E,EAAA,CAAAY,aAAA,CAAAqE,IAAA,EAAA7C,SAAA;MAAA,MAAAtB,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CAASF,MAAA,CAAAoE,oBAAA,CAAAN,SAAA,CAA2B;IAAA,EAAC;IAI7E5E,EADF,CAAAC,cAAA,cAAoD,cAEP;IACzCD,EAAA,CAAA2C,UAAA,IAAAwC,4DAAA,gBAA0E;IAC5EnF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,iBACsD;IAApDD,EAAA,CAAAU,UAAA,mBAAA0E,iFAAAxB,MAAA;MAAA,MAAAgB,SAAA,GAAA5E,EAAA,CAAAY,aAAA,CAAAqE,IAAA,EAAA7C,SAAA;MAAA,MAAAtB,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,MAAAsE,gBAAA,GAAArF,EAAA,CAAAyB,WAAA;MAAA,OAAAzB,EAAA,CAAAgB,WAAA,CAASF,MAAA,CAAAwE,YAAA,CAAAV,SAAA,EAAAS,gBAAA,EAAAzB,MAAA,CAAyC;IAAA,EAAC;IACnD5D,EAAA,CAAAkB,SAAA,YAA0B;IAE9BlB,EADE,CAAAG,YAAA,EAAS,EACL;IAGNH,EAAA,CAAAC,cAAA,cAAmG;IAGjGD,EAFA,CAAA2C,UAAA,IAAA4C,8DAAA,kBACiE,IAAAC,8DAAA,kBACE;IAIrExF,EAAA,CAAAG,YAAA,EAAM;IAIJH,EADF,CAAAC,cAAA,cAAmC,eACsB;IAAAD,EAAA,CAAAE,MAAA,IAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAC7EH,EAAA,CAAAC,cAAA,eAA2B;IAAAD,EAAA,CAAAE,MAAA,IAA4B;;IAE3DF,EAF2D,CAAAG,YAAA,EAAM,EACzD,EACF;;;;;IA7BJH,EAAA,CAAAyF,WAAA,aAAA3E,MAAA,CAAA4E,eAAA,CAAAd,SAAA,EAAyC;IAKrC5E,EAAA,CAAAO,SAAA,GAAwC;IAAxCP,EAAA,CAAAyF,WAAA,YAAA3E,MAAA,CAAA4E,eAAA,CAAAd,SAAA,EAAwC;IACpC5E,EAAA,CAAAO,SAAA,EAA4B;IAA5BP,EAAA,CAAAI,UAAA,SAAAU,MAAA,CAAA4E,eAAA,CAAAd,SAAA,EAA4B;IAU5B5E,EAAA,CAAAO,SAAA,GAAwB;IAAxBP,EAAA,CAAAI,UAAA,SAAAwE,SAAA,CAAAC,YAAA,CAAwB;IAExB7E,EAAA,CAAAO,SAAA,EAAyB;IAAzBP,EAAA,CAAAI,UAAA,UAAAwE,SAAA,CAAAC,YAAA,CAAyB;IAQG7E,EAAA,CAAAO,SAAA,GAAoB;IAApBP,EAAA,CAAAI,UAAA,UAAAwE,SAAA,CAAAG,IAAA,CAAoB;IAAC/E,EAAA,CAAAO,SAAA,EAAgB;IAAhBP,EAAA,CAAA+B,iBAAA,CAAA6C,SAAA,CAAAG,IAAA,CAAgB;IAC5C/E,EAAA,CAAAO,SAAA,GAA4B;IAA5BP,EAAA,CAAAQ,kBAAA,KAAAR,EAAA,CAAA2F,WAAA,SAAAf,SAAA,CAAAgB,IAAA,SAA4B;;;;;IAM7D5F,EAAA,CAAAC,cAAA,cAAiF;IAC/ED,EAAA,CAAAkB,SAAA,aAA2C;IAC3ClB,EAAA,CAAAC,cAAA,UAAK;IAAAD,EAAA,CAAAE,MAAA,uDAAQ;IACfF,EADe,CAAAG,YAAA,EAAM,EACf;;;;;;IAnEVH,EADsD,CAAAC,cAAA,kBAAqC,qBAC3E;IACdD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAiB;IAMXH,EALN,CAAAC,cAAA,uBAC4E,cAE/B,cACrB,gBAEW;IAD0CD,EAAA,CAAA0D,gBAAA,2BAAAmC,iFAAAjC,MAAA;MAAA5D,EAAA,CAAAY,aAAA,CAAAkF,IAAA;MAAA,MAAAhF,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAAf,EAAA,CAAA8D,kBAAA,CAAAhD,MAAA,CAAAiF,eAAA,EAAAnC,MAAA,MAAA9C,MAAA,CAAAiF,eAAA,GAAAnC,MAAA;MAAA,OAAA5D,EAAA,CAAAgB,WAAA,CAAA4C,MAAA;IAAA,EAA6B;IAClG5D,EAAA,CAAAU,UAAA,mBAAAsF,yEAAA;MAAAhG,EAAA,CAAAY,aAAA,CAAAkF,IAAA;MAAA,MAAAhF,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CAASF,MAAA,CAAAmF,YAAA,EAAc;IAAA,EAAC;IAC5BjG,EAFE,CAAAG,YAAA,EAC6B,EACzB;IACNH,EAAA,CAAAC,cAAA,iBAAqE;IAAvBD,EAAA,CAAAU,UAAA,mBAAAwF,0EAAA;MAAAlG,EAAA,CAAAY,aAAA,CAAAkF,IAAA;MAAA,MAAAhF,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CAASF,MAAA,CAAAqF,UAAA,EAAY;IAAA,EAAC;IAClEnG,EAAA,CAAAE,MAAA,iCAAK;IAAAF,EAAA,CAAAkB,SAAA,YAA8B;IAEvClB,EADE,CAAAG,YAAA,EAAS,EACL;IAEJH,EADF,CAAAC,cAAA,eAA2C,kBACkC;IAA5BD,EAAA,CAAAU,UAAA,mBAAA0F,2EAAA;MAAApG,EAAA,CAAAY,aAAA,CAAAkF,IAAA;MAAA,MAAAhF,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CAASF,MAAA,CAAAuF,eAAA,EAAiB;IAAA,EAAC;IACxErG,EAAA,CAAAE,MAAA,sBAAG;IAAAF,EAAA,CAAAkB,SAAA,aAAmC;IACxClB,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,kBAA+E;IAA9BD,EAAA,CAAAU,UAAA,mBAAA4F,2EAAA;MAAAtG,EAAA,CAAAY,aAAA,CAAAkF,IAAA;MAAA,MAAAhF,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CAASF,MAAA,CAAAyF,iBAAA,EAAmB;IAAA,EAAC;IAC5EvG,EAAA,CAAAE,MAAA,kCAAK;IAAAF,EAAA,CAAAkB,SAAA,aAA6B;IACpClB,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,eAA2C;IACzCD,EAAA,CAAAE,MAAA,IACF;IACFF,EADE,CAAAG,YAAA,EAAM,EACF;IAIJH,EADF,CAAAC,cAAA,eAA+D,eACzB;IAClCD,EAAA,CAAA2C,UAAA,KAAA6D,wDAAA,oBACkF;IA8BpFxG,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAA2C,UAAA,KAAA8D,wDAAA,kBAAiF;IAKrFzG,EADE,CAAAG,YAAA,EAAM,EACO;IAEbH,EADF,CAAAC,cAAA,0BAA0E,eACrC;IACjCD,EAAA,CAAAE,MAAA,IACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAEJH,EADF,CAAAC,cAAA,eAA0B,kBACgD;IAAlCD,EAAA,CAAAU,UAAA,mBAAAgG,2EAAA;MAAA,MAAAC,OAAA,GAAA3G,EAAA,CAAAY,aAAA,CAAAkF,IAAA,EAAAvB,SAAA;MAAA,MAAAzD,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CAASF,MAAA,CAAA8F,kBAAA,CAAAD,OAAA,CAAuB;IAAA,EAAC;IAAC3G,EAAA,CAAAE,MAAA,oBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACnFH,EAAA,CAAAC,cAAA,kBACyC;IAAvCD,EAAA,CAAAU,UAAA,mBAAAmG,2EAAA;MAAA,MAAAF,OAAA,GAAA3G,EAAA,CAAAY,aAAA,CAAAkF,IAAA,EAAAvB,SAAA;MAAA,MAAAzD,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CAASF,MAAA,CAAAgG,uBAAA,CAAAH,OAAA,CAA4B;IAAA,EAAC;IACtC3G,EAAA,CAAAE,MAAA,IACF;IAGNF,EAHM,CAAAG,YAAA,EAAS,EACL,EACS,EACT;;;;IAjFNH,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAAQ,kBAAA,iCAAAM,MAAA,CAAAiD,gBAAA,CAAArB,KAAA,eAAA5B,MAAA,CAAAiD,gBAAA,CAAArB,KAAA,yFACF;IAM6E1C,EAAA,CAAAO,SAAA,GAA6B;IAA7BP,EAAA,CAAA2E,gBAAA,YAAA7D,MAAA,CAAAiF,eAAA,CAA6B;IAepG/F,EAAA,CAAAO,SAAA,IACF;IADEP,EAAA,CAAAQ,kBAAA,0BAAAM,MAAA,CAAAiG,cAAA,CAAAC,MAAA,yBACF;IAMyBhH,EAAA,CAAAO,SAAA,GAAiB;IAAjBP,EAAA,CAAAI,UAAA,YAAAU,MAAA,CAAAmG,cAAA,CAAiB;IAkCpCjH,EAAA,CAAAO,SAAA,EAAiC;IAAjCP,EAAA,CAAAI,UAAA,SAAAU,MAAA,CAAAmG,cAAA,CAAAD,MAAA,OAAiC;IAQvChH,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAAQ,kBAAA,aAAAM,MAAA,CAAAoG,eAAA,CAAAF,MAAA,yBACF;IAGyChH,EAAA,CAAAO,SAAA,GAAwC;IAAxCP,EAAA,CAAAI,UAAA,aAAAU,MAAA,CAAAiG,cAAA,CAAAC,MAAA,OAAwC;IAE7EhH,EAAA,CAAAO,SAAA,EACF;IADEP,EAAA,CAAAQ,kBAAA,gCAAAM,MAAA,CAAAiG,cAAA,CAAAC,MAAA,OACF;;;;;IAqBFhH,EAAA,CAAAkB,SAAA,eAC8E;;;;IAA5ElB,EADsD,CAAAI,UAAA,QAAAU,MAAA,CAAAqG,eAAA,CAAAC,OAAA,EAAApH,EAAA,CAAA8E,aAAA,CAA+B,QAAAhE,MAAA,CAAAqG,eAAA,CAAApC,IAAA,CACzD;;;;;IAC9B/E,EAAA,CAAAC,cAAA,cAA4F;IAC1FD,EAAA,CAAAkB,SAAA,aAA0C;IAC1ClB,EAAA,CAAAC,cAAA,UAAK;IAAAD,EAAA,CAAAE,MAAA,2CAAM;IACbF,EADa,CAAAG,YAAA,EAAM,EACb;;;;;;IAhBNH,EAFJ,CAAAC,cAAA,mBAAqC,yBACuC,WAClE;IAAAD,EAAA,CAAAE,MAAA,GAAkC;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAE7CH,EADF,CAAAC,cAAA,cAA0B,kBACuF;IAA1BD,EAAA,CAAAU,UAAA,mBAAA2G,0EAAA;MAAArH,EAAA,CAAAY,aAAA,CAAA0G,IAAA;MAAA,MAAAxG,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CAASF,MAAA,CAAAyG,aAAA,EAAe;IAAA,EAAC;IAC5GvH,EAAA,CAAAkB,SAAA,aAAmC;IAAClB,EAAA,CAAAE,MAAA,2BACtC;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,kBACwB;IAAtBD,EAAA,CAAAU,UAAA,mBAAA8G,0EAAA;MAAAxH,EAAA,CAAAY,aAAA,CAAA0G,IAAA;MAAA,MAAAxG,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CAASF,MAAA,CAAA2G,SAAA,EAAW;IAAA,EAAC;IACrBzH,EAAA,CAAAE,MAAA,2BAAI;IAAAF,EAAA,CAAAkB,SAAA,cAAoC;IAG9ClB,EAFI,CAAAG,YAAA,EAAS,EACL,EACS;IAACH,EAAA,CAAAC,cAAA,yBAAkG;IAGlHD,EAFA,CAAA2C,UAAA,KAAA+E,wDAAA,mBAC8E,KAAAC,wDAAA,kBACc;IAI9F3H,EAAA,CAAAG,YAAA,EAAe;IAEbH,EADF,CAAAC,cAAA,0BAA0E,eACrC;IACjCD,EAAA,CAAAE,MAAA,IACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACqBH,EAA3B,CAAAC,cAAA,eAA0B,mBAAuF;IAA1CD,EAAA,CAAAU,UAAA,mBAAAkH,2EAAA;MAAA5H,EAAA,CAAAY,aAAA,CAAA0G,IAAA;MAAA,MAAAxG,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CAASF,MAAA,CAAA+G,6BAAA,EAA+B;IAAA,EAAC;IAC5G7H,EAAA,CAAAE,MAAA,IACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,kBAA4D;IAAtBD,EAAA,CAAAU,UAAA,mBAAAoH,2EAAA;MAAA,MAAAC,OAAA,GAAA/H,EAAA,CAAAY,aAAA,CAAA0G,IAAA,EAAA/C,SAAA;MAAA,OAAAvE,EAAA,CAAAgB,WAAA,CAAS+G,OAAA,CAAAC,KAAA,EAAW;IAAA,EAAC;IAAChI,EAAA,CAAAE,MAAA,oBAAE;IAGpEF,EAHoE,CAAAG,YAAA,EAAS,EACnE,EACS,EACT;;;;IA5BAH,EAAA,CAAAO,SAAA,GAAkC;IAAlCP,EAAA,CAAAQ,kBAAA,gCAAAM,MAAA,CAAAqG,eAAA,kBAAArG,MAAA,CAAAqG,eAAA,CAAApC,IAAA,KAAkC;IAES/E,EAAA,CAAAO,SAAA,GAAqC;IAArCP,EAAA,CAAAI,UAAA,aAAAU,MAAA,CAAAmH,mBAAA,MAAqC;IAGrCjI,EAAA,CAAAO,SAAA,GAA6D;IAA7DP,EAAA,CAAAI,UAAA,aAAAU,MAAA,CAAAmH,mBAAA,IAAAnH,MAAA,CAAAmG,cAAA,CAAAD,MAAA,KAA6D;IAMxGhH,EAAA,CAAAO,SAAA,GAAgD;IAAhDP,EAAA,CAAAI,UAAA,SAAAU,MAAA,CAAAqG,eAAA,IAAArG,MAAA,CAAAqG,eAAA,CAAAC,OAAA,CAAgD;IAEhDpH,EAAA,CAAAO,SAAA,EAAkD;IAAlDP,EAAA,CAAAI,UAAA,UAAAU,MAAA,CAAAqG,eAAA,KAAArG,MAAA,CAAAqG,eAAA,CAAAC,OAAA,CAAkD;IAOtDpH,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAAkI,kBAAA,MAAApH,MAAA,CAAAmH,mBAAA,aAAAnH,MAAA,CAAAmG,cAAA,CAAAD,MAAA,MACF;IAEIhH,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAAQ,kBAAA,MAAAM,MAAA,CAAAqG,eAAA,IAAArG,MAAA,CAAA4E,eAAA,CAAA5E,MAAA,CAAAqG,eAAA,uEACF;;;;;;IAWFnH,EAFJ,CAAAC,cAAA,mBAAqF,qBACnE,WACR;IACJD,EAAA,CAAAE,MAAA,qBACF;IACFF,EADE,CAAAG,YAAA,EAAO,EACQ;IAEfH,EADF,CAAAC,cAAA,wBAAwC,eACX;IACzBD,EAAA,CAAAkB,SAAA,eAAkD;IAEtDlB,EADE,CAAAG,YAAA,EAAM,EACO;IAGXH,EAFJ,CAAAC,cAAA,qBAAgB,eACgC,kBACc;IAAtBD,EAAA,CAAAU,UAAA,mBAAAyH,0EAAA;MAAA,MAAAC,OAAA,GAAApI,EAAA,CAAAY,aAAA,CAAAyH,IAAA,EAAA9D,SAAA;MAAA,OAAAvE,EAAA,CAAAgB,WAAA,CAASoH,OAAA,CAAAJ,KAAA,EAAW;IAAA,EAAC;IAAChI,EAAA,CAAAE,MAAA,oBAAE;IAGlEF,EAHkE,CAAAG,YAAA,EAAS,EACjE,EACS,EACT;;;;IARkBH,EAAA,CAAAO,SAAA,GAA2B;IAA3BP,EAAA,CAAAI,UAAA,QAAAU,MAAA,CAAAwH,mBAAA,EAAAtI,EAAA,CAAA8E,aAAA,CAA2B;;;AD/RzD;AACA,IAAKyD,eAIJ;AAJD,WAAKA,eAAe;EAClBA,eAAA,CAAAA,eAAA,sBAAQ;EACRA,eAAA,CAAAA,eAAA,gDAAqB;EACrBA,eAAA,CAAAA,eAAA,gCAAa,EAAO;AACtB,CAAC,EAJIA,eAAe,KAAfA,eAAe;AAwBpB,OAAM,MAAOC,yBAA0B,SAAQzI,aAAa;EA0C1D0I,YACUC,MAAmB,EACnBC,aAA8B,EAC9BC,OAAuB,EACvBC,KAAuB,EACvBC,iBAAmC,EACnCC,gBAAiC,EACjCC,eAA+B;IAEvC,KAAK,CAACN,MAAM,CAAC;IARL,KAAAA,MAAM,GAANA,MAAM;IACN,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,eAAe,GAAfA,eAAe;IAhDzB,KAAAC,KAAK,GAAG,IAAI;IAKZ,KAAAC,cAAc,GAA8B,EAAE;IAG9C,KAAAC,eAAe,GAAG,CAChB;MACEC,KAAK,EAAE,IAAI;MACXC,KAAK,EAAE;KACR,EACD;MACED,KAAK,EAAE,KAAK;MACZC,KAAK,EAAE;KACR,EACD;MACED,KAAK,EAAE,IAAI;MACXC,KAAK,EAAE;KACR,CAAC;IACJ,KAAAC,iBAAiB,GAAG,IAAI;IACxB,KAAAlG,WAAW,GAAW,EAAE;IACxB;IACA;IACA;IACA;IACA;IACA,KAAAmG,SAAS,GAAY,KAAK;IAC1B,KAAAjB,mBAAmB,GAAW,EAAE;IAChC,KAAAkB,aAAa,GAAY,KAAK;IAC9B,KAAArG,UAAU,GAAY,IAAI;IAE1B;IACA,KAAA+D,eAAe,GAAgB,EAAE;IACjC,KAAAD,cAAc,GAAgB,EAAE;IAChC,KAAAF,cAAc,GAAgB,EAAE;IAChC,KAAAhB,eAAe,GAAW,EAAE;IAC5B,KAAAoB,eAAe,GAAqB,IAAI;IACxC,KAAAc,mBAAmB,GAAW,CAAC;EAY/B;EAESwB,QAAQA,CAAA;IACf,IAAI,CAACC,gBAAgB,EAAE;EACzB;EAEAA,gBAAgBA,CAAA;IACd,IAAI,CAACZ,iBAAiB,CAACa,6CAA6C,CAAC;MACnEC,IAAI,EAAE;QACJC,OAAO,EAAE,KAAK;QACdC,OAAO,EAAE;;KAEZ,CAAC,CACCC,IAAI,CACHnK,GAAG,CAACoK,GAAG,IAAG;MACR,IAAIA,GAAG,CAACC,UAAU,IAAI,CAAC,EAAE;QACvB,IAAI,CAACf,cAAc,GAAGc,GAAG,CAACE,OAAO,EAAElD,MAAM,GAAGgD,GAAG,CAACE,OAAO,GAAG,EAAE;QAC5D,IAAI,CAACC,mBAAmB,GAAG,IAAI,CAACjB,cAAc,CAAC,CAAC,CAAC,CAAC5I,GAAI;MACxD;IACF,CAAC,CAAC,EACFX,QAAQ,CAAC,MAAM,IAAI,CAACyK,eAAe,EAAE,CAAC,CACvC,CAACC,SAAS,EAAE;EACjB;EACAD,eAAeA,CAACE,SAAA,GAAoB,CAAC;IACnC,OAAO,IAAI,CAACvB,gBAAgB,CAACwB,mCAAmC,CAAC;MAC/DX,IAAI,EAAE;QACJY,YAAY,EAAE,IAAI,CAACL,mBAAmB;QACtCM,QAAQ,EAAE,IAAI,CAACnB,iBAAiB;QAChClG,WAAW,EAAE,IAAI,CAACA,WAAW;QAC7B;QACA;QACA;QACAsH,QAAQ,EAAE,IAAI,CAACC,QAAQ;QACvBC,SAAS,EAAEN,SAAS;QACpBnH,UAAU,EAAE,IAAI,CAACA;;KAEpB,CAAC,CAAC4G,IAAI,CACLnK,GAAG,CAACoK,GAAG,IAAG;MACR,IAAIA,GAAG,CAACC,UAAU,IAAI,CAAC,EAAE;QACvB,IAAI,CAACxG,YAAY,GAAGuG,GAAG,CAACE,OAAQ,IAAI,EAAE;QACtC,IAAI,CAACW,YAAY,GAAGb,GAAG,CAACc,UAAW;QAEnC,IAAI,IAAI,CAACrH,YAAY,CAACuD,MAAM,GAAG,CAAC,EAAE;UAChC,IAAI,CAACuC,SAAS,GAAG,IAAI,CAAC9F,YAAY,CAAC,CAAC,CAAC,CAACH,UAAW;QACnD;MACF;IACF,CAAC,CAAC,CACH;EACH;EAEAjC,MAAMA,CAAA;IACJ,IAAI,CAAC+I,eAAe,EAAE,CAACC,SAAS,EAAE;EACpC;EAEAU,WAAWA,CAACT,SAAiB;IAC3B,IAAI,CAACF,eAAe,CAACE,SAAS,CAAC,CAACD,SAAS,EAAE;EAC7C;EAEApJ,sBAAsBA,CAAA;IACpB,IAAI,CAAC8H,gBAAgB,CAACiC,2CAA2C,CAAC;MAChEpB,IAAI,EAAE,IAAI,CAACO;KACZ,CAAC,CAACE,SAAS,CAACL,GAAG,IAAG;MACjB,IAAIA,GAAG,CAACC,UAAU,IAAI,CAAC,EAAE;QACvB,IAAID,GAAG,CAACE,OAAQ,CAACe,QAAQ,EAAE;UACzB,IAAI,CAACjC,eAAe,CAACkC,iBAAiB,CAAClB,GAAG,CAACE,OAAQ,CAACe,QAAQ,EAAE,MAAM,CAAC;QACvE;MACF;IACF,CAAC,CAAC;EACJ;EAEAE,0BAA0BA,CAAA;IACxB,IAAI,CAACpC,gBAAgB,CAACqC,+CAA+C,CAAC;MACpExB,IAAI,EAAE,IAAI,CAACO;KACZ,CAAC,CAACE,SAAS,CAACL,GAAG,IAAG;MACjB,IAAIA,GAAG,CAACC,UAAU,IAAI,CAAC,EAAE;QACvB,IAAID,GAAG,CAACE,OAAQ,CAACe,QAAQ,EAAE;UACzB,IAAI,CAACjC,eAAe,CAACkC,iBAAiB,CAAClB,GAAG,CAACE,OAAQ,CAACe,QAAQ,EAAE,MAAM,CAAC;QACvE;MACF;IACF,CAAC,CAAC;EACJ;EAEAvJ,MAAMA,CAAC2J,GAAQ;IACb,IAAI,CAACpC,KAAK,GAAG,IAAI;IACjB,IAAI,CAAClF,gBAAgB,GAAG,EAAE;IAC1B,IAAI,CAAC4E,aAAa,CAAC2C,IAAI,CAACD,GAAG,CAAC;EAC9B;EACAhJ,kBAAkBA,CAACkJ,IAA6B,EAAEF,GAAQ;IACxD,IAAI,CAACpC,KAAK,GAAG,KAAK;IAClB,IAAI,CAAClF,gBAAgB,GAAG;MAAE,GAAGwH;IAAI,CAAE;IACnC,IAAI,CAAC5C,aAAa,CAAC2C,IAAI,CAACD,GAAG,CAAC;EAC9B;EAEA5I,oBAAoBA,CAAC8I,IAA6B,EAAEF,GAAqB;IACvE,IAAI,CAACtH,gBAAgB,GAAG;MAAE,GAAGwH;IAAI,CAAE;IACnC,IAAI,CAACpF,UAAU,EAAE;IACjB,IAAI,CAACwC,aAAa,CAAC2C,IAAI,CAACD,GAAG,EAAE;MAAEG,oBAAoB,EAAE;IAAK,CAAE,CAAC;EAC/D;EACAC,UAAUA,CAAA;IACR,IAAI,CAAC5C,KAAK,CAAC6C,KAAK,EAAE;IAClB,IAAI,CAAC7C,KAAK,CAAC8C,QAAQ,CAAC,MAAM,EAAE,IAAI,CAAC5H,gBAAgB,CAACrB,KAAK,CAAC;IACxD,IAAI,CAACmG,KAAK,CAAC8C,QAAQ,CAAC,MAAM,EAAE,IAAI,CAAC5H,gBAAgB,CAACf,KAAK,CAAC;IACxD,IAAI,CAAC6F,KAAK,CAAC8C,QAAQ,CAAC,MAAM,EAAE,IAAI,CAAC5H,gBAAgB,CAACd,SAAS,CAAC;IAC5D,IAAI,CAAC4F,KAAK,CAAC8C,QAAQ,CAAC,UAAU,EAAE,IAAI,CAAC5H,gBAAgB,CAACX,WAAW,CAAC;IAClE;IACA;IACA,IAAI,CAACyF,KAAK,CAAC+C,iBAAiB,CAAC,MAAM,EAAE,IAAI,CAAC7H,gBAAgB,CAACrB,KAAK,EAAE,EAAE,CAAC;IACrE,IAAI,CAACmG,KAAK,CAAC+C,iBAAiB,CAAC,MAAM,EAAE,IAAI,CAAC7H,gBAAgB,CAACf,KAAK,EAAE,EAAE,CAAC;IACrE,IAAI,CAAC6F,KAAK,CAAC+C,iBAAiB,CAAC,MAAM,EAAE,IAAI,CAAC7H,gBAAgB,CAACd,SAAS,EAAE,EAAE,CAAC;IACzE,IAAI,CAAC4F,KAAK,CAAC+C,iBAAiB,CAAC,UAAU,EAAE,IAAI,CAAC7H,gBAAgB,CAACX,WAAW,EAAE,EAAE,CAAC;IAC/E;IACA;EACF;EAEAsB,QAAQA,CAAC2G,GAAQ;IACf,IAAI,CAACI,UAAU,EAAE;IACjB,IAAI,IAAI,CAAC5C,KAAK,CAACgD,aAAa,CAAC7E,MAAM,GAAG,CAAC,EAAE;MACvC,IAAI,CAAC4B,OAAO,CAACkD,aAAa,CAAC,IAAI,CAACjD,KAAK,CAACgD,aAAa,CAAC;MACpD;IACF;IAAE,IAAI,CAAC9C,gBAAgB,CAACgD,qCAAqC,CAAC;MAC5DnC,IAAI,EAAE;QACJY,YAAY,EAAE,IAAI,CAACL,mBAAmB;QACtC;QACA6B,UAAU,EAAE,IAAI,CAACjI,gBAAgB,CAACiI,UAAU;QAC5CtJ,KAAK,EAAE,IAAI,CAACqB,gBAAgB,CAACrB,KAAK;QAClCM,KAAK,EAAE,IAAI,CAACe,gBAAgB,CAACf,KAAK;QAClCC,SAAS,EAAE,IAAI,CAACc,gBAAgB,CAACd,SAAS;QAC1CG,WAAW,EAAE,IAAI,CAACW,gBAAgB,CAACX,WAAW;QAC9CC,YAAY,EAAE,IAAI,CAACU,gBAAgB,CAACV,YAAY;QAChD4I,WAAW,EAAE,IAAI,CAAChD,KAAK,GAAG,IAAI,GAAG,IAAI,CAAClF,gBAAgB,CAAChB,GAAI;QAC3Dd,MAAM,EAAE,IAAI,CAAC8B,gBAAgB,CAAC9B;;KAEjC,CAAC,CACC8H,IAAI,CACHnK,GAAG,CAACoK,GAAG,IAAG;MACR,IAAIA,GAAG,CAACC,UAAU,KAAK,CAAC,EAAE;QACxB,IAAI,CAACrB,OAAO,CAACsD,aAAa,CAAC,MAAM,CAAC;MACpC,CAAC,MAAM;QACL,IAAI,CAACtD,OAAO,CAACuD,YAAY,CAACnC,GAAG,CAACoC,OAAQ,CAAC;MACzC;IACF,CAAC,CAAC,EACFzM,QAAQ,CAAC,MAAM,IAAI,CAACyK,eAAe,EAAE,CAAC,EACtC1K,QAAQ,CAAC,MAAM2L,GAAG,CAACrD,KAAK,EAAE,CAAC,CAC5B,CAACqC,SAAS,EAAE;EACjB;EAEA7F,OAAOA,CAAC6G,GAAQ;IACdA,GAAG,CAACrD,KAAK,EAAE;EACb;EAEAqE,eAAeA,CAACC,KAAU;IACxB,MAAMC,MAAM,GAAgCD,KAAK,CAACC,MAAO;IACzD,MAAMC,MAAM,GAAe,IAAIC,UAAU,EAAE;IAC3CD,MAAM,CAACE,kBAAkB,CAACH,MAAM,CAACI,KAAK,CAAC,CAAC,CAAC,CAAC;IAC1CH,MAAM,CAACI,MAAM,GAAIC,CAAM,IAAI;MACzB,MAAMC,SAAS,GAAWD,CAAC,CAACN,MAAM,CAACQ,MAAM;MACzC,MAAMC,EAAE,GAAkBnN,IAAI,CAACoN,IAAI,CAACH,SAAS,EAAE;QAAEI,IAAI,EAAE;MAAQ,CAAE,CAAC;MAElE,MAAMC,MAAM,GAAWH,EAAE,CAACI,UAAU,CAAC,CAAC,CAAC;MACvC,MAAMC,EAAE,GAAmBL,EAAE,CAACM,MAAM,CAACH,MAAM,CAAC;MAE5C,IAAII,WAAW,GAAY,IAAI;MAC/B,MAAMhC,IAAI,GAAG1L,IAAI,CAAC2N,KAAK,CAACC,aAAa,CAACJ,EAAE,CAAC;MACzC,IAAI9B,IAAI,IAAIA,IAAI,CAACvE,MAAM,GAAG,CAAC,EAAE;QAC3BuE,IAAI,CAACmC,OAAO,CAAEC,CAAM,IAAI;UACtB,IAAI,EAAEA,CAAC,CAAC,IAAI,CAAC,IAAIA,CAAC,CAAC,IAAI,CAAC,IAAIA,CAAC,CAAC,IAAI,CAAC,IAAIA,CAAC,CAAC,QAAQ,CAAC,KAC/CA,CAAC,CAAC,QAAQ,CAAC,IAAIA,CAAC,CAAC,MAAM,CAAC,IAAIA,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE;YAC5CJ,WAAW,GAAG,KAAK;UACrB;QACF,CAAC,CAAC;QAEF,IAAI,CAACA,WAAW,EAAE;UAChB,IAAI,CAAC3E,OAAO,CAACuD,YAAY,CAAC,WAAW,CAAC;QACxC,CAAC,MAAM;UACL,IAAI,CAACpD,gBAAgB,CAAC6E,2CAA2C,CAAC;YAChEhE,IAAI,EAAE;cACJY,YAAY,EAAE,IAAI,CAACL,mBAAmB;cACtC0D,KAAK,EAAEtB,MAAM,CAACI,KAAK,CAAC,CAAC;;WAExB,CAAC,CAAC5C,IAAI,CACLnK,GAAG,CAACoK,GAAG,IAAG;YACR,IAAIA,GAAG,CAACC,UAAU,IAAI,CAAC,EAAE;cACvB,IAAI,CAACrB,OAAO,CAACsD,aAAa,CAAC,MAAM,CAAC;YACpC,CAAC,MAAM;cACL,IAAI,CAACtD,OAAO,CAACuD,YAAY,CAACnC,GAAG,CAACoC,OAAQ,CAAC;YACzC;UACF,CAAC,CAAC,EACFzM,QAAQ,CAAC,MAAM,IAAI,CAACyK,eAAe,CAAC,CAAC,CAAC,CAAC,CACxC,CAACC,SAAS,EAAE;QACf;MACF,CAAC,MAAM;QACL,IAAI,CAACzB,OAAO,CAACuD,YAAY,CAAC,uBAAuB,CAAC;MACpD;MACAG,KAAK,CAACC,MAAM,CAACnD,KAAK,GAAG,IAAI;IAC3B,CAAC;EACH;EAEA0E,SAASA,CAACC,QAAgB,EAAEC,MAAwB;IAClD,IAAI,CAAC1F,mBAAmB,GAAGyF,QAAQ;IACnC,IAAI,CAACpF,aAAa,CAAC2C,IAAI,CAAC0C,MAAM,CAAC;EACjC;EACAC,YAAYA,CAAA;IACV,IAAI,IAAI,CAACzE,aAAa,EAAE;MACtB,IAAI,CAACrG,UAAU,GAAG,KAAK;MACvB,IAAI,CAACiH,eAAe,EAAE,CAACC,SAAS,EAAE;IACpC,CAAC,MACI;MACH,IAAI,CAAClH,UAAU,GAAG,IAAI;MACtB,IAAI,CAACiH,eAAe,EAAE,CAACC,SAAS,EAAE;IACpC;EACF;EAEA;EACA6D,eAAeA,CAAC7C,GAAqB;IACnC,IAAI,CAAClF,UAAU,EAAE;IACjB,IAAI,CAACwC,aAAa,CAAC2C,IAAI,CAACD,GAAG,EAAE;MAAEG,oBAAoB,EAAE;IAAK,CAAE,CAAC;EAC/D;EACArF,UAAUA,CAAA;IACR;IACA,IAAI,CAACe,eAAe,GAAG,CACrB;MACEiH,EAAE,EAAE,GAAG;MACPpJ,IAAI,EAAE,oBAAoB;MAC1Ba,IAAI,EAAE,GAAG;MACTf,YAAY,EAAE,mDAAmD;MACjEuC,OAAO,EAAE,4CAA4C;MACrDgH,YAAY,EAAE,IAAIC,IAAI;KACvB,EACD;MACEF,EAAE,EAAE,GAAG;MACPpJ,IAAI,EAAE,oBAAoB;MAC1Ba,IAAI,EAAE,GAAG;MACTf,YAAY,EAAE,mDAAmD;MACjEuC,OAAO,EAAE,4CAA4C;MACrDgH,YAAY,EAAE,IAAIC,IAAI;KACvB,EACD;MACEF,EAAE,EAAE,GAAG;MACPpJ,IAAI,EAAE,sBAAsB;MAC5Ba,IAAI,EAAE,GAAG;MACTf,YAAY,EAAE,qDAAqD;MACnEuC,OAAO,EAAE,8CAA8C;MACvDgH,YAAY,EAAE,IAAIC,IAAI;KACvB,EACD;MACEF,EAAE,EAAE,GAAG;MACPpJ,IAAI,EAAE,0BAA0B;MAChCa,IAAI,EAAE,GAAG;MACTf,YAAY,EAAE,yDAAyD;MACvEuC,OAAO,EAAE,kDAAkD;MAC3DgH,YAAY,EAAE,IAAIC,IAAI;KACvB,EACD;MACEF,EAAE,EAAE,GAAG;MACPpJ,IAAI,EAAE,qBAAqB;MAC3Ba,IAAI,EAAE,EAAE;MACRf,YAAY,EAAE,oDAAoD;MAClEuC,OAAO,EAAE,6CAA6C;MACtDgH,YAAY,EAAE,IAAIC,IAAI;KACvB,EACD;MACEF,EAAE,EAAE,GAAG;MACPpJ,IAAI,EAAE,sBAAsB;MAC5Ba,IAAI,EAAE,GAAG;MACTf,YAAY,EAAE,qDAAqD;MACnEuC,OAAO,EAAE,8CAA8C;MACvDgH,YAAY,EAAE,IAAIC,IAAI;KACvB;IACD;IACA;MACEF,EAAE,EAAE,GAAG;MACPpJ,IAAI,EAAE,sBAAsB;MAC5Ba,IAAI,EAAE,GAAG;MACTf,YAAY,EAAE,qDAAqD;MACnEuC,OAAO,EAAE,8CAA8C;MACvDgH,YAAY,EAAE,IAAIC,IAAI;KACvB,EACD;MACEF,EAAE,EAAE,GAAG;MACPpJ,IAAI,EAAE,sBAAsB;MAC5Ba,IAAI,EAAE,GAAG;MACTf,YAAY,EAAE,qDAAqD;MACnEuC,OAAO,EAAE,8CAA8C;MACvDgH,YAAY,EAAE,IAAIC,IAAI;KACvB,EACD;MACEF,EAAE,EAAE,GAAG;MACPpJ,IAAI,EAAE,wBAAwB;MAC9Ba,IAAI,EAAE,GAAG;MACTf,YAAY,EAAE,uDAAuD;MACrEuC,OAAO,EAAE,gDAAgD;MACzDgH,YAAY,EAAE,IAAIC,IAAI;KACvB,EACD;MACEF,EAAE,EAAE,IAAI;MACRpJ,IAAI,EAAE,qBAAqB;MAC3Ba,IAAI,EAAE,GAAG;MACTf,YAAY,EAAE,oDAAoD;MAClEuC,OAAO,EAAE,6CAA6C;MACtDgH,YAAY,EAAE,IAAIC,IAAI;KACvB,EACD;MACEF,EAAE,EAAE,IAAI;MACRpJ,IAAI,EAAE,uBAAuB;MAC7Ba,IAAI,EAAE,GAAG;MACTf,YAAY,EAAE,sDAAsD;MACpEuC,OAAO,EAAE,+CAA+C;MACxDgH,YAAY,EAAE,IAAIC,IAAI;KACvB,EACD;MACEF,EAAE,EAAE,IAAI;MACRpJ,IAAI,EAAE,wBAAwB;MAC9Ba,IAAI,EAAE,GAAG;MACTf,YAAY,EAAE,uDAAuD;MACrEuC,OAAO,EAAE,gDAAgD;MACzDgH,YAAY,EAAE,IAAIC,IAAI;KACvB,CACF;IAED,IAAI,CAACpH,cAAc,GAAG,CAAC,GAAG,IAAI,CAACC,eAAe,CAAC;IAE/C;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;EACF;EAEAjB,YAAYA,CAAA;IACV,IAAI,CAAC,IAAI,CAACF,eAAe,CAACuI,IAAI,EAAE,EAAE;MAChC,IAAI,CAACrH,cAAc,GAAG,CAAC,GAAG,IAAI,CAACC,eAAe,CAAC;IACjD,CAAC,MAAM;MACL,MAAMqH,UAAU,GAAG,IAAI,CAACxI,eAAe,CAACyI,WAAW,EAAE;MACrD,IAAI,CAACvH,cAAc,GAAG,IAAI,CAACC,eAAe,CAACuH,MAAM,CAACC,KAAK,IACrDA,KAAK,CAAC3J,IAAI,CAACyJ,WAAW,EAAE,CAACG,QAAQ,CAACJ,UAAU,CAAC,CAC9C;IACH;EACF;EAEArJ,oBAAoBA,CAACwJ,KAAgB;IACnC,MAAME,KAAK,GAAG,IAAI,CAAC7H,cAAc,CAAC8H,SAAS,CAACC,QAAQ,IAAIA,QAAQ,CAACX,EAAE,KAAKO,KAAK,CAACP,EAAE,CAAC;IACjF,IAAIS,KAAK,GAAG,CAAC,CAAC,EAAE;MACd,IAAI,CAAC7H,cAAc,CAACgI,MAAM,CAACH,KAAK,EAAE,CAAC,CAAC;IACtC,CAAC,MAAM;MACL,IAAI,CAAC7H,cAAc,CAACiI,IAAI,CAACN,KAAK,CAAC;IACjC;EACF;EAEAhJ,eAAeA,CAACgJ,KAAgB;IAC9B,OAAO,IAAI,CAAC3H,cAAc,CAACkI,IAAI,CAACH,QAAQ,IAAIA,QAAQ,CAACX,EAAE,KAAKO,KAAK,CAACP,EAAE,CAAC;EACvE;EAEA9H,eAAeA,CAAA;IACb,IAAI,CAACU,cAAc,GAAG,CAAC,GAAG,IAAI,CAACE,cAAc,CAAC;EAChD;EAEAV,iBAAiBA,CAAA;IACf,IAAI,CAACQ,cAAc,GAAG,EAAE;EAC1B;EAAEzB,YAAYA,CAACoJ,KAAgB,EAAEQ,eAAiC,EAAE5C,KAAY;IAC9EA,KAAK,CAAC6C,eAAe,EAAE;IACvB,IAAI,CAAChI,eAAe,GAAGuH,KAAK;IAC5B,IAAI,CAACzG,mBAAmB,GAAG,IAAI,CAAChB,cAAc,CAAC4H,SAAS,CAACO,GAAG,IAAIA,GAAG,CAACjB,EAAE,KAAKO,KAAK,CAACP,EAAE,CAAC;IACpF,IAAI,CAACxF,aAAa,CAAC2C,IAAI,CAAC4D,eAAe,CAAC;EAC1C;EAEA3H,aAAaA,CAAA;IACX,IAAI,IAAI,CAACU,mBAAmB,GAAG,CAAC,EAAE;MAChC,IAAI,CAACA,mBAAmB,EAAE;MAC1B,IAAI,CAACd,eAAe,GAAG,IAAI,CAACF,cAAc,CAAC,IAAI,CAACgB,mBAAmB,CAAC;IACtE;EACF;EAEAR,SAASA,CAAA;IACP,IAAI,IAAI,CAACQ,mBAAmB,GAAG,IAAI,CAAChB,cAAc,CAACD,MAAM,GAAG,CAAC,EAAE;MAC7D,IAAI,CAACiB,mBAAmB,EAAE;MAC1B,IAAI,CAACd,eAAe,GAAG,IAAI,CAACF,cAAc,CAAC,IAAI,CAACgB,mBAAmB,CAAC;IACtE;EACF;EAEAJ,6BAA6BA,CAAA;IAC3B,IAAI,IAAI,CAACV,eAAe,EAAE;MACxB,IAAI,CAACjC,oBAAoB,CAAC,IAAI,CAACiC,eAAe,CAAC;IACjD;EACF;EAAEL,uBAAuBA,CAACuE,GAAQ;IAChC,IAAI,IAAI,CAACtE,cAAc,CAACC,MAAM,GAAG,CAAC,EAAE;MAClC;MACA,IAAI,IAAI,CAACD,cAAc,CAACC,MAAM,KAAK,CAAC,EAAE;QACpC,IAAI,CAACjD,gBAAgB,CAACiI,UAAU,GAAG,IAAI,CAACjF,cAAc,CAAC,CAAC,CAAC,CAAChC,IAAI;MAChE,CAAC,MAAM;QACL;QACA,MAAMsK,UAAU,GAAG,IAAI,CAACtI,cAAc,CAACuI,GAAG,CAACF,GAAG,IAAIA,GAAG,CAACrK,IAAI,CAAC,CAACwK,IAAI,CAAC,IAAI,CAAC;QACtE,IAAI,CAAC3G,OAAO,CAACsD,aAAa,CAAC,OAAO,IAAI,CAACnF,cAAc,CAACC,MAAM,SAASqI,UAAU,EAAE,CAAC;QAClF,IAAI,CAACtL,gBAAgB,CAACiI,UAAU,GAAG,IAAI,CAACjF,cAAc,CAAC,CAAC,CAAC,CAAChC,IAAI;MAChE;MAEA;MACA,IAAI,IAAI,CAAChB,gBAAgB,CAAChB,GAAG,EAAE;QAC7B,IAAI,CAACyM,gBAAgB,EAAE;MACzB;IACF;IAEA,IAAI,CAACjJ,iBAAiB,EAAE;IACxB8E,GAAG,CAACrD,KAAK,EAAE;EACb;EAEA;EACAwH,gBAAgBA,CAAA;IACd,IAAI,CAACzG,gBAAgB,CAACgD,qCAAqC,CAAC;MAC1DnC,IAAI,EAAE;QACJY,YAAY,EAAE,IAAI,CAACL,mBAAmB;QACtC6B,UAAU,EAAE,IAAI,CAACjI,gBAAgB,CAACiI,UAAU;QAC5CtJ,KAAK,EAAE,IAAI,CAACqB,gBAAgB,CAACrB,KAAK;QAClCM,KAAK,EAAE,IAAI,CAACe,gBAAgB,CAACf,KAAK;QAClCC,SAAS,EAAE,IAAI,CAACc,gBAAgB,CAACd,SAAS;QAC1CG,WAAW,EAAE,IAAI,CAACW,gBAAgB,CAACX,WAAW;QAC9CC,YAAY,EAAE,IAAI,CAACU,gBAAgB,CAACV,YAAY;QAChD4I,WAAW,EAAE,IAAI,CAAClI,gBAAgB,CAAChB,GAAI;QACvCd,MAAM,EAAE,IAAI,CAAC8B,gBAAgB,CAAC9B;;KAEjC,CAAC,CACC8H,IAAI,CACHnK,GAAG,CAACoK,GAAG,IAAG;MACR,IAAIA,GAAG,CAACC,UAAU,KAAK,CAAC,EAAE;QACxB,IAAI,CAACrB,OAAO,CAACsD,aAAa,CAAC,WAAW,IAAI,CAACnI,gBAAgB,CAACiI,UAAU,EAAE,CAAC;MAC3E,CAAC,MAAM;QACL,IAAI,CAACpD,OAAO,CAACuD,YAAY,CAACnC,GAAG,CAACoC,OAAQ,CAAC;MACzC;IACF,CAAC,CAAC,EACFzM,QAAQ,CAAC,MAAM,IAAI,CAACyK,eAAe,EAAE,CAAC,EACtC1K,QAAQ,CAAC,MAAK;MACZ;MACA,IAAI,CAACqE,gBAAgB,GAAG,EAAE;IAC5B,CAAC,CAAC,CACH,CAACsG,SAAS,EAAE;EACjB;EAEAzD,kBAAkBA,CAACyE,GAAQ;IACzB,IAAI,CAAC9E,iBAAiB,EAAE;IACxB,IAAI,CAACR,eAAe,GAAG,EAAE;IACzBsF,GAAG,CAACrD,KAAK,EAAE;EACb;;;uCAhfWQ,yBAAyB,EAAAxI,EAAA,CAAAyP,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAA3P,EAAA,CAAAyP,iBAAA,CAAAG,EAAA,CAAAC,eAAA,GAAA7P,EAAA,CAAAyP,iBAAA,CAAAK,EAAA,CAAAC,cAAA,GAAA/P,EAAA,CAAAyP,iBAAA,CAAAO,EAAA,CAAAC,gBAAA,GAAAjQ,EAAA,CAAAyP,iBAAA,CAAAS,EAAA,CAAAC,gBAAA,GAAAnQ,EAAA,CAAAyP,iBAAA,CAAAS,EAAA,CAAAE,eAAA,GAAApQ,EAAA,CAAAyP,iBAAA,CAAAY,EAAA,CAAAC,cAAA;IAAA;EAAA;;;YAAzB9H,yBAAyB;MAAA+H,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAAzQ,EAAA,CAAA0Q,0BAAA,EAAA1Q,EAAA,CAAA2Q,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,mCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;UCtCpCjR,EADF,CAAAC,cAAA,iBAA0B,qBACR;UACdD,EAAA,CAAAkB,SAAA,qBAAiC;UACnClB,EAAA,CAAAG,YAAA,EAAiB;UAEfH,EADF,CAAAC,cAAA,mBAAc,YACyB;UAACD,EAAA,CAAAE,MAAA,yJACtC;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAICH,EAHN,CAAAC,cAAA,aAA8B,aACN,aACqC,gBACF;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAC/DH,EAAA,CAAAC,cAAA,qBAA6E;UAAjDD,EAAA,CAAA0D,gBAAA,2BAAAyN,uEAAAvN,MAAA;YAAA5D,EAAA,CAAAY,aAAA,CAAAwQ,GAAA;YAAApR,EAAA,CAAA8D,kBAAA,CAAAoN,GAAA,CAAA/G,mBAAA,EAAAvG,MAAA,MAAAsN,GAAA,CAAA/G,mBAAA,GAAAvG,MAAA;YAAA,OAAA5D,EAAA,CAAAgB,WAAA,CAAA4C,MAAA;UAAA,EAAiC;UAC3D5D,EAAA,CAAA2C,UAAA,KAAA0O,+CAAA,wBAA4E;UAKlFrR,EAFI,CAAAG,YAAA,EAAY,EACR,EACF;UAaFH,EAFJ,CAAAC,cAAA,cAAsB,cACqC,iBACF;UAAAD,EAAA,CAAAE,MAAA,6CAAO;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACpEH,EAAA,CAAAC,cAAA,iBAAyF;UAAzCD,EAAA,CAAA0D,gBAAA,2BAAA4N,mEAAA1N,MAAA;YAAA5D,EAAA,CAAAY,aAAA,CAAAwQ,GAAA;YAAApR,EAAA,CAAA8D,kBAAA,CAAAoN,GAAA,CAAA9N,WAAA,EAAAQ,MAAA,MAAAsN,GAAA,CAAA9N,WAAA,GAAAQ,MAAA;YAAA,OAAA5D,EAAA,CAAAgB,WAAA,CAAA4C,MAAA;UAAA,EAAyB;UAE7E5D,EAFI,CAAAG,YAAA,EAAyF,EACrF,EACF;UAaFH,EAFJ,CAAAC,cAAA,eAAuB,eAC0B,uBAEjB;UAD+BD,EAAA,CAAA0D,gBAAA,2BAAA6N,yEAAA3N,MAAA;YAAA5D,EAAA,CAAAY,aAAA,CAAAwQ,GAAA;YAAApR,EAAA,CAAA8D,kBAAA,CAAAoN,GAAA,CAAA1H,aAAA,EAAA5F,MAAA,MAAAsN,GAAA,CAAA1H,aAAA,GAAA5F,MAAA;YAAA,OAAA5D,EAAA,CAAAgB,WAAA,CAAA4C,MAAA;UAAA,EAA2B;UACpF5D,EAAA,CAAAU,UAAA,oBAAA8Q,kEAAA;YAAAxR,EAAA,CAAAY,aAAA,CAAAwQ,GAAA;YAAA,OAAApR,EAAA,CAAAgB,WAAA,CAAUkQ,GAAA,CAAAjD,YAAA,EAAc;UAAA,EAAC;UACzBjO,EAAA,CAAAE,MAAA,gHACF;UAAAF,EAAA,CAAAG,YAAA,EAAc;UAOdH,EANA,CAAA2C,UAAA,KAAA8O,4CAAA,qBAA8F,KAAAC,4CAAA,qBAEV,KAAAC,4CAAA,qBAEE,KAAAC,4CAAA,qBAEF;UACpF5R,EAAA,CAAAC,cAAA,oBAAqG;UAAnCD,EAAA,CAAAU,UAAA,oBAAAmR,4DAAAjO,MAAA;YAAA5D,EAAA,CAAAY,aAAA,CAAAwQ,GAAA;YAAA,OAAApR,EAAA,CAAAgB,WAAA,CAAUkQ,GAAA,CAAA7E,eAAA,CAAAzI,MAAA,CAAuB;UAAA,EAAC;UAApG5D,EAAA,CAAAG,YAAA,EAAqG;UACrGH,EAAA,CAAAC,cAAA,kBAA4E;UAAvCD,EAAA,CAAAU,UAAA,mBAAAoR,4DAAA;YAAA9R,EAAA,CAAAY,aAAA,CAAAwQ,GAAA;YAAA,OAAApR,EAAA,CAAAgB,WAAA,CAASkQ,GAAA,CAAA/F,0BAAA,EAA4B;UAAA,EAAC;UAACnL,EAAA,CAAAE,MAAA,6CAAO;UAAAF,EAAA,CAAAkB,SAAA,aAC9C;UAG3ClB,EAH2C,CAAAG,YAAA,EAAS,EAC1C,EACF,EACF;UAKEH,EAJR,CAAAC,cAAA,eAAmC,iBAC+D,aACvF,cACgD,cACrB;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAGrCH,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACrCH,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACrCH,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACrCH,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAE,MAAA,gCAAI;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACvCH,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAE,MAAA,gCAAI;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACvCH,EAAA,CAAA2C,UAAA,KAAAoP,wCAAA,iBAAwD;UACxD/R,EAAA,CAAAC,cAAA,cAA0C;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAEhDF,EAFgD,CAAAG,YAAA,EAAK,EAC9C,EACC;UACRH,EAAA,CAAA2C,UAAA,KAAAqP,2CAAA,oBAA+D;UAuBrEhS,EAFI,CAAAG,YAAA,EAAQ,EACJ,EACO;UAEbH,EADF,CAAAC,cAAA,0BAAsD,0BAEf;UADqCD,EAA1D,CAAA0D,gBAAA,kCAAAuO,mFAAArO,MAAA;YAAA5D,EAAA,CAAAY,aAAA,CAAAwQ,GAAA;YAAApR,EAAA,CAAA8D,kBAAA,CAAAoN,GAAA,CAAArG,YAAA,EAAAjH,MAAA,MAAAsN,GAAA,CAAArG,YAAA,GAAAjH,MAAA;YAAA,OAAA5D,EAAA,CAAAgB,WAAA,CAAA4C,MAAA;UAAA,EAAiC,4BAAAsO,6EAAAtO,MAAA;YAAA5D,EAAA,CAAAY,aAAA,CAAAwQ,GAAA;YAAApR,EAAA,CAAA8D,kBAAA,CAAAoN,GAAA,CAAAvG,QAAA,EAAA/G,MAAA,MAAAsN,GAAA,CAAAvG,QAAA,GAAA/G,MAAA;YAAA,OAAA5D,EAAA,CAAAgB,WAAA,CAAA4C,MAAA;UAAA,EAAwB,wBAAAuO,yEAAAvO,MAAA;YAAA5D,EAAA,CAAAY,aAAA,CAAAwQ,GAAA;YAAApR,EAAA,CAAA8D,kBAAA,CAAAoN,GAAA,CAAA5G,SAAA,EAAA1G,MAAA,MAAAsN,GAAA,CAAA5G,SAAA,GAAA1G,MAAA;YAAA,OAAA5D,EAAA,CAAAgB,WAAA,CAAA4C,MAAA;UAAA,EAAqB;UAC5F5D,EAAA,CAAAU,UAAA,wBAAAyR,yEAAAvO,MAAA;YAAA5D,EAAA,CAAAY,aAAA,CAAAwQ,GAAA;YAAA,OAAApR,EAAA,CAAAgB,WAAA,CAAckQ,GAAA,CAAAnG,WAAA,CAAAnH,MAAA,CAAmB;UAAA,EAAC;UAGxC5D,EAFI,CAAAG,YAAA,EAAiB,EACF,EACT;UAwLVH,EAtLA,CAAA2C,UAAA,KAAAyP,iDAAA,iCAAApS,EAAA,CAAAqS,sBAAA,CAAoD,KAAAC,iDAAA,iCAAAtS,EAAA,CAAAqS,sBAAA,CA4DK,KAAAE,iDAAA,iCAAAvS,EAAA,CAAAqS,sBAAA,CAuFC,KAAAG,iDAAA,iCAAAxS,EAAA,CAAAqS,sBAAA,CAmCH;;;UAzRjBrS,EAAA,CAAAO,SAAA,IAAiC;UAAjCP,EAAA,CAAA2E,gBAAA,YAAAuM,GAAA,CAAA/G,mBAAA,CAAiC;UAC1BnK,EAAA,CAAAO,SAAA,EAAiB;UAAjBP,EAAA,CAAAI,UAAA,YAAA8Q,GAAA,CAAAhI,cAAA,CAAiB;UAmBJlJ,EAAA,CAAAO,SAAA,GAAyB;UAAzBP,EAAA,CAAA2E,gBAAA,YAAAuM,GAAA,CAAA9N,WAAA,CAAyB;UAedpD,EAAA,CAAAO,SAAA,GAA2B;UAA3BP,EAAA,CAAA2E,gBAAA,YAAAuM,GAAA,CAAA1H,aAAA,CAA2B;UAI7ExJ,EAAA,CAAAO,SAAA,GAAmB;UAAnBP,EAAA,CAAAI,UAAA,SAAA8Q,GAAA,CAAAuB,aAAA,CAAmB;UAEnBzS,EAAA,CAAAO,SAAA,EAAY;UAAZP,EAAA,CAAAI,UAAA,SAAA8Q,GAAA,CAAA3N,MAAA,CAAY;UAEZvD,EAAA,CAAAO,SAAA,EAAc;UAAdP,EAAA,CAAAI,UAAA,SAAA8Q,GAAA,CAAAwB,QAAA,CAAc;UAEY1S,EAAA,CAAAO,SAAA,EAAmB;UAAnBP,EAAA,CAAAI,UAAA,SAAA8Q,GAAA,CAAAyB,aAAA,CAAmB;UAmBrB3S,EAAA,CAAAO,SAAA,IAAuB;UAAvBP,EAAA,CAAAI,UAAA,SAAA8Q,GAAA,CAAA3H,SAAA,SAAuB;UAIlDvJ,EAAA,CAAAO,SAAA,GAAqD;UAArDP,EAAA,CAAAI,UAAA,SAAA8Q,GAAA,CAAAzN,YAAA,YAAAyN,GAAA,CAAAzN,YAAA,CAAAuD,MAAA,KAAqD;UAyBjDhH,EAAA,CAAAO,SAAA,GAAiC;UAAyBP,EAA1D,CAAA2E,gBAAA,mBAAAuM,GAAA,CAAArG,YAAA,CAAiC,aAAAqG,GAAA,CAAAvG,QAAA,CAAwB,SAAAuG,GAAA,CAAA5G,SAAA,CAAqB;;;qBDpEtF7K,YAAY,EAAAmT,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EAAAF,EAAA,CAAAG,WAAA,EAAEjT,YAAY,EAAAkT,EAAA,CAAAC,oBAAA,EAAAD,EAAA,CAAAE,mBAAA,EAAAF,EAAA,CAAAG,eAAA,EAAAH,EAAA,CAAAI,kBAAA,EAAAJ,EAAA,CAAAK,OAAA,EAAAzD,EAAA,CAAA0D,eAAA,EAAA1D,EAAA,CAAA2D,mBAAA,EAAA3D,EAAA,CAAA4D,qBAAA,EAAA5D,EAAA,CAAA6D,qBAAA,EAAA7D,EAAA,CAAA8D,mBAAA,EAAA9D,EAAA,CAAA+D,gBAAA,EAAA/D,EAAA,CAAAgE,iBAAA,EAAAhE,EAAA,CAAAiE,iBAAA,EAAAC,EAAA,CAAAC,mBAAA,EAAAC,GAAA,CAAAC,mBAAA;MAAAC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}