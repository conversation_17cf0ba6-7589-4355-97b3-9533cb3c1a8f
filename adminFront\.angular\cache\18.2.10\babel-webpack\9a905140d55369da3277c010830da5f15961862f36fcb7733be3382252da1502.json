{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/Documents/jeansalechange/adminFront/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { BaseComponent } from '../../components/base/baseComponent';\nimport { NbCardModule, NbInputModule, NbSelectModule, NbOptionModule, NbIconModule, NbCheckboxModule } from '@nebular/theme';\nimport { StatusPipe } from '../../../@theme/pipes/mapping.pipe';\nimport { MomentPipe } from '../../../@theme/pipes/moment.pipe';\nimport { NgIf, NgFor } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { ShareRequest } from 'src/app/shared/model/requests/shareRequest';\nimport { BreadcrumbComponent } from '../../components/breadcrumb/breadcrumb.component';\nimport { PaginationComponent } from '../../components/pagination/pagination.component';\nimport { lastValueFrom } from 'rxjs';\nimport { EEvent } from 'src/app/shared/services/event.service';\nimport { LocalStorageService } from 'src/app/shared/services/local-storage.service';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@nebular/theme\";\nimport * as i2 from \"src/services/api/services\";\nimport * as i3 from \"src/app/shared/services/message.service\";\nimport * as i4 from \"../../components/shared.observable\";\nimport * as i5 from \"src/app/shared/helper/allowHelper\";\nimport * as i6 from \"src/app/shared/helper/validationHelper\";\nimport * as i7 from \"src/app/shared/services/event.service\";\nimport * as i8 from \"@angular/router\";\nimport * as i9 from \"@angular/forms\";\nfunction RolePermissionsComponent_button_24_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 25);\n    i0.ɵɵlistener(\"click\", function RolePermissionsComponent_button_24_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      const dialog_r4 = i0.ɵɵreference(45);\n      return i0.ɵɵresetView(ctx_r2.add(dialog_r4));\n    });\n    i0.ɵɵelement(1, \"i\", 26);\n    i0.ɵɵtext(2, \"\\u65B0\\u589E\\u89D2\\u8272\\u6B0A\\u9650\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RolePermissionsComponent_tr_42_button_12_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 34);\n    i0.ɵɵlistener(\"click\", function RolePermissionsComponent_tr_42_button_12_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const data_r6 = i0.ɵɵnextContext().$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      const dialog_r4 = i0.ɵɵreference(45);\n      return i0.ɵɵresetView(ctx_r2.onEdit(data_r6, dialog_r4));\n    });\n    i0.ɵɵelement(1, \"i\", 35);\n    i0.ɵɵtext(2, \"\\u7DE8\\u8F2F\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RolePermissionsComponent_tr_42_button_13_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 36);\n    i0.ɵɵlistener(\"click\", function RolePermissionsComponent_tr_42_button_13_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const data_r6 = i0.ɵɵnextContext().$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onDelete(data_r6));\n    });\n    i0.ɵɵelement(1, \"i\", 37);\n    i0.ɵɵtext(2, \"\\u522A\\u9664\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RolePermissionsComponent_tr_42_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\", 27)(1, \"td\", 28);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\", 29);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\", 28);\n    i0.ɵɵtext(6);\n    i0.ɵɵpipe(7, \"getStatusName\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"td\", 30);\n    i0.ɵɵtext(9);\n    i0.ɵɵpipe(10, \"localDate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"td\", 31);\n    i0.ɵɵtemplate(12, RolePermissionsComponent_tr_42_button_12_Template, 3, 0, \"button\", 32)(13, RolePermissionsComponent_tr_42_button_13_Template, 3, 0, \"button\", 33);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const data_r6 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(data_r6.CId);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(data_r6.CName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(7, 6, data_r6.CStatus));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(10, 8, data_r6.CUpdateDt, \"yyyy-MM-DD HH:mm:ss\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isUpdate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isDelete);\n  }\n}\nfunction RolePermissionsComponent_ng_template_44_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"\\u65B0\\u589E\\u89D2\\u8272\\u6B0A\\u9650\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RolePermissionsComponent_ng_template_44_span_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"\\u7DE8\\u8F2F\\u89D2\\u8272\\u6B0A\\u9650\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RolePermissionsComponent_ng_template_44_div_14_div_1_div_4_div_1_label_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"label\", 57)(1, \"nb-checkbox\", 58);\n    i0.ɵɵtwoWayListener(\"checkedChange\", function RolePermissionsComponent_ng_template_44_div_14_div_1_div_4_div_1_label_4_Template_nb_checkbox_checkedChange_1_listener($event) {\n      const auth_r10 = i0.ɵɵrestoreView(_r9).$implicit;\n      i0.ɵɵtwoWayBindingSet(auth_r10.IsChecked, $event) || (auth_r10.IsChecked = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const auth_r10 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"checked\", auth_r10.IsChecked);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\"\", auth_r10.CName, \" \");\n  }\n}\nfunction RolePermissionsComponent_ng_template_44_div_14_div_1_div_4_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 54)(1, \"div\", 55);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 44);\n    i0.ɵɵtemplate(4, RolePermissionsComponent_ng_template_44_div_14_div_1_div_4_div_1_label_4_Template, 3, 2, \"label\", 56);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const v2_r11 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(v2_r11.CName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", v2_r11.Authority);\n  }\n}\nfunction RolePermissionsComponent_ng_template_44_div_14_div_1_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 3);\n    i0.ɵɵtemplate(1, RolePermissionsComponent_ng_template_44_div_14_div_1_div_4_div_1_Template, 5, 2, \"div\", 53);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const v2_r11 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", v2_r11.Authority.length > 0);\n  }\n}\nfunction RolePermissionsComponent_ng_template_44_div_14_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 49)(1, \"div\", 50);\n    i0.ɵɵelement(2, \"nb-icon\", 51);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, RolePermissionsComponent_ng_template_44_div_14_div_1_div_4_Template, 2, 1, \"div\", 52);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const v1_r12 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\", v1_r12.CName, \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", v1_r12.FunctionLv2);\n  }\n}\nfunction RolePermissionsComponent_ng_template_44_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 3);\n    i0.ɵɵtemplate(1, RolePermissionsComponent_ng_template_44_div_14_div_1_Template, 5, 2, \"div\", 48);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const v1_r12 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", v1_r12.FunctionLv2.length > 0);\n  }\n}\nfunction RolePermissionsComponent_ng_template_44_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-card\", 38)(1, \"nb-card-header\");\n    i0.ɵɵtemplate(2, RolePermissionsComponent_ng_template_44_span_2_Template, 2, 0, \"span\", 39)(3, RolePermissionsComponent_ng_template_44_span_3_Template, 2, 0, \"span\", 39);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"nb-card-body\")(5, \"div\", 40)(6, \"div\", 41)(7, \"div\", 4)(8, \"div\", 42)(9, \"label\", 43);\n    i0.ɵɵtext(10, \"\\u89D2\\u8272\\u540D\\u7A31\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"div\", 44)(12, \"input\", 7);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function RolePermissionsComponent_ng_template_44_Template_input_ngModelChange_12_listener($event) {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.userGroupFunction.CName, $event) || (ctx_r2.userGroupFunction.CName = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(13, \"div\", 4);\n    i0.ɵɵtemplate(14, RolePermissionsComponent_ng_template_44_div_14_Template, 2, 1, \"div\", 45);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(15, \"nb-card-footer\")(16, \"div\", 4)(17, \"div\", 46)(18, \"button\", 25);\n    i0.ɵɵlistener(\"click\", function RolePermissionsComponent_ng_template_44_Template_button_click_18_listener() {\n      const ref_r13 = i0.ɵɵrestoreView(_r8).dialogRef;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.save(ref_r13));\n    });\n    i0.ɵɵtext(19, \"\\u78BA\\u5B9A\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"button\", 47);\n    i0.ɵɵlistener(\"click\", function RolePermissionsComponent_ng_template_44_Template_button_click_20_listener() {\n      const ref_r13 = i0.ɵɵrestoreView(_r8).dialogRef;\n      return i0.ɵɵresetView(ref_r13.close());\n    });\n    i0.ɵɵtext(21, \"\\u53D6\\u6D88\");\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isNew === true);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isNew === false);\n    i0.ɵɵadvance(9);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.userGroupFunction.CName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.userGroupFunction.FunctionLv1);\n  }\n}\nexport let RolePermissionsComponent = /*#__PURE__*/(() => {\n  class RolePermissionsComponent extends BaseComponent {\n    constructor(dialogService, userGroupService, message, share, allow, valid, _eventService, _router) {\n      super(allow);\n      this.dialogService = dialogService;\n      this.userGroupService = userGroupService;\n      this.message = message;\n      this.share = share;\n      this.allow = allow;\n      this.valid = valid;\n      this._eventService = _eventService;\n      this._router = _router;\n      this.userGroups = [];\n      this.userGroupFunction = {};\n      this.request = new ShareRequest();\n      this.isNew = false;\n      this.selectedItem = '';\n      this.share.SharedUserGroup.subscribe(res => {\n        this.userGroups = res;\n      });\n      this.share.SharedUserGroupFunction.subscribe(res => {\n        this.userGroupFunction = res;\n      });\n      this.getList();\n    }\n    ngOnInit() {}\n    getList() {\n      this.request.PageSize = this.pageSize;\n      this.request.PageIndex = this.pageIndex;\n      this.userGroupService.apiUserGroupGetListPost$Json({\n        body: {\n          ...this.request\n        }\n      }).subscribe(res => {\n        this.userGroups = res.Entries;\n        this.totalRecords = res.TotalItems;\n        this.share.SetUserGroup(this.userGroups);\n      });\n    }\n    getFunction() {\n      return lastValueFrom(this.userGroupService.apiUserGroupGetDataPost$Json({\n        body: {\n          CId: this.request.CId\n        }\n      })).then(res => {\n        this.userGroupFunction = res.Entries;\n        this.share.SetUserGroupFunction(this.userGroupFunction);\n      }).catch(error => {\n        console.log(error);\n      });\n    }\n    onDelete(data) {\n      this.request.CId = data.CId;\n      this.isNew = false;\n      if (window.confirm('是否確定刪除?')) {\n        this.remove();\n      } else {\n        return;\n      }\n    }\n    onEdit(data, dialog) {\n      var _this = this;\n      return _asyncToGenerator(function* () {\n        _this.request.CId = data.CId;\n        _this.isNew = false;\n        try {\n          yield _this.getFunction();\n          _this.dialogService.open(dialog);\n        } catch (error) {\n          console.log(\"Failed to get function data\", error);\n        }\n      })();\n    }\n    add(dialog) {\n      this.isNew = true;\n      this.request.CId = null;\n      this.getFunction();\n      this.dialogService.open(dialog);\n    }\n    save(ref) {\n      this.validation();\n      if (this.valid.errorMessages.length > 0) {\n        this.message.showErrorMSGs(this.valid.errorMessages);\n        return;\n      }\n      if (this.isNew) {\n        this.userGroupService.apiUserGroupAddDataPost$Json({\n          body: {\n            CId: this.userGroupFunction.CId,\n            CName: this.userGroupFunction.CName,\n            FunctionLv1: this.userGroupFunction.FunctionLv1\n          }\n        }).subscribe(res => {\n          if (res.StatusCode === 0) {\n            this.message.showSucessMSG('執行成功');\n            setTimeout(() => {\n              this._router.navigate([\"logout\"]).then(() => {\n                ref.close();\n                LocalStorageService.ClearLocalStorage();\n              });\n            }, 1500);\n            this.getList();\n            setTimeout(() => {\n              this._eventService.push({\n                action: \"CHANGE_ROLE\" /* EEvent.CHANGE_ROLE */,\n                payload: true\n              });\n            }, 500);\n          } else {\n            this.message.showErrorMSG(res.Message);\n          }\n        });\n      } else {\n        this.userGroupService.apiUserGroupSaveDataPost$Json({\n          body: {\n            CId: this.userGroupFunction.CId,\n            CName: this.userGroupFunction.CName,\n            FunctionLv1: this.userGroupFunction.FunctionLv1\n          }\n        }).subscribe(res => {\n          if (res.StatusCode === 0) {\n            this.message.showSucessMSG('執行成功');\n            // setTimeout(() => {\n            //   this._router.navigate([\"logout\"]).then(() => {\n            //     ref.close();\n            //     LocalStorageService.ClearLocalStorage();\n            //   })\n            // }, 1500);\n            setTimeout(() => {\n              this._eventService.push({\n                action: \"CHANGE_ROLE\" /* EEvent.CHANGE_ROLE */,\n                payload: true\n              });\n            }, 500);\n          } else {\n            this.message.showErrorMSG(res.Message);\n          }\n        });\n      }\n    }\n    remove() {\n      this.userGroupService.apiUserGroupRemoveDataPost$Json({\n        body: {\n          CId: this.request.CId\n        }\n      }).subscribe(res => {\n        this.message.showSucessMSG('執行成功');\n        this.getList();\n      });\n    }\n    validation() {\n      this.valid.clear();\n      this.valid.required('[角色名稱]', this.userGroupFunction.CName);\n    }\n    static {\n      this.ɵfac = function RolePermissionsComponent_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || RolePermissionsComponent)(i0.ɵɵdirectiveInject(i1.NbDialogService), i0.ɵɵdirectiveInject(i2.UserGroupService), i0.ɵɵdirectiveInject(i3.MessageService), i0.ɵɵdirectiveInject(i4.SharedObservable), i0.ɵɵdirectiveInject(i5.AllowHelper), i0.ɵɵdirectiveInject(i6.ValidationHelper), i0.ɵɵdirectiveInject(i7.EventService), i0.ɵɵdirectiveInject(i8.Router));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: RolePermissionsComponent,\n        selectors: [[\"ngx-role-permissions\"]],\n        standalone: true,\n        features: [i0.ɵɵInheritDefinitionFeature, i0.ɵɵStandaloneFeature],\n        decls: 46,\n        vars: 10,\n        consts: [[\"dialog\", \"\"], [\"accent\", \"success\"], [1, \"bg-white\"], [1, \"col-12\"], [1, \"row\"], [1, \"form-group\", \"col-12\", \"col-md-4\"], [\"for\", \"keyWord\", 1, \"label\", \"mr-2\"], [\"type\", \"text\", \"nbInput\", \"\", \"id\", \"l1\", \"placeholder\", \"\\u8ACB\\u8F38\\u5165\\u89D2\\u8272\\u6B0A\\u9650\\u540D\\u7A31\", \"name\", \"Name\", 3, \"ngModelChange\", \"ngModel\"], [1, \"form-group\", \"col-12\", \"col-md-3\"], [\"for\", \"status\", 1, \"label\", \"mr-2\"], [3, \"selectedChange\", \"selected\"], [3, \"value\"], [1, \"form-group\", \"col-12\", \"col-md-5\", \"text-right\"], [1, \"btn\", \"btn-info\", \"mr-2\", 3, \"click\"], [1, \"fas\", \"fa-search\", \"mr-1\"], [\"class\", \"btn btn-success mr-2\", 3, \"click\", 4, \"ngIf\"], [1, \"table-responsive\"], [1, \"table\", \"table-striped\", \"border\", 2, \"min-width\", \"800px\", \"background-color\", \"#f3f3f3\"], [1, \"d-flex\", \"text-white\", 2, \"background-color\", \"#27ae60\"], [\"scope\", \"col\", 1, \"col-1\"], [\"scope\", \"col\", 1, \"col-5\"], [\"scope\", \"col\", 1, \"col-3\"], [\"scope\", \"col\", 1, \"col-2\"], [\"class\", \"d-flex\", 4, \"ngFor\", \"ngForOf\"], [3, \"PageChange\", \"CollectionSize\", \"Page\", \"PageSize\"], [1, \"btn\", \"btn-success\", \"mr-2\", 3, \"click\"], [1, \"fas\", \"fa-plus\", \"mr-1\"], [1, \"d-flex\"], [1, \"col-1\"], [1, \"col-5\"], [1, \"col-3\"], [1, \"col-2\"], [\"type\", \"button\", \"class\", \"btn btn-outline-success m-1  btn-sm\", 3, \"click\", 4, \"ngIf\"], [\"type\", \"button\", \"class\", \"btn btn-outline-danger m-1  btn-sm\", 3, \"click\", 4, \"ngIf\"], [\"type\", \"button\", 1, \"btn\", \"btn-outline-success\", \"m-1\", \"btn-sm\", 3, \"click\"], [1, \"fas\", \"fa-edit\", \"mr-1\"], [\"type\", \"button\", 1, \"btn\", \"btn-outline-danger\", \"m-1\", \"btn-sm\", 3, \"click\"], [1, \"far\", \"fa-trash-alt\", \"mr-1\"], [1, \"mr-md-5\", \"ml-md-5\", 2, \"height\", \"100%\", \"overflow\", \"auto\", \"max-width\", \"800px\"], [4, \"ngIf\"], [1, \"row\", \"p-2\"], [1, \"col-12\", \"col-md-12\"], [1, \"form-group\", \"row\", \"col-12\"], [1, \"col-md-4\", \"col-form-label\", \"required-field\"], [1, \"col-md-8\"], [\"class\", \"col-12 \", 4, \"ngFor\", \"ngForOf\"], [1, \"col-12\", \"text-center\"], [1, \"btn\", \"btn-danger\", \"mr-2\", 3, \"click\"], [\"class\", \"row border\", 4, \"ngIf\"], [1, \"row\", \"border\"], [1, \"col-12\", \"p-2\", \"border\"], [\"icon\", \"arrow-right-outline\", \"status\", \"basic\"], [\"class\", \"col-12\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"row pl-4 pt-3\", 4, \"ngIf\"], [1, \"row\", \"pl-4\", \"pt-3\"], [1, \"col-md-3\"], [\"class\", \"mr-2\", 4, \"ngFor\", \"ngForOf\"], [1, \"mr-2\"], [\"status\", \"basic\", 3, \"checkedChange\", \"checked\"]],\n        template: function RolePermissionsComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            const _r1 = i0.ɵɵgetCurrentView();\n            i0.ɵɵelementStart(0, \"nb-card\", 1)(1, \"nb-card-header\");\n            i0.ɵɵelement(2, \"ngx-breadcrumb\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(3, \"nb-card-body\", 2)(4, \"div\", 3)(5, \"div\", 4)(6, \"div\", 5)(7, \"label\", 6);\n            i0.ɵɵtext(8, \"\\u641C\\u5C0B\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(9, \"input\", 7);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function RolePermissionsComponent_Template_input_ngModelChange_9_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.request.CName, $event) || (ctx.request.CName = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(10, \"div\", 8)(11, \"label\", 9);\n            i0.ɵɵtext(12, \"\\u72C0\\u614B\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(13, \"nb-select\", 10);\n            i0.ɵɵtwoWayListener(\"selectedChange\", function RolePermissionsComponent_Template_nb_select_selectedChange_13_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.request.CStatus, $event) || (ctx.request.CStatus = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵelementStart(14, \"nb-option\", 11);\n            i0.ɵɵtext(15, \"\\u5168\\u90E8\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(16, \"nb-option\", 11);\n            i0.ɵɵtext(17, \"\\u555F\\u7528\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(18, \"nb-option\", 11);\n            i0.ɵɵtext(19, \"\\u505C\\u7528\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(20, \"div\", 12)(21, \"button\", 13);\n            i0.ɵɵlistener(\"click\", function RolePermissionsComponent_Template_button_click_21_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.getList());\n            });\n            i0.ɵɵelement(22, \"i\", 14);\n            i0.ɵɵtext(23, \"\\u67E5\\u8A62\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(24, RolePermissionsComponent_button_24_Template, 3, 0, \"button\", 15);\n            i0.ɵɵelementEnd()()()();\n            i0.ɵɵelementStart(25, \"nb-card-body\", 2)(26, \"div\", 3)(27, \"div\", 16)(28, \"table\", 17)(29, \"thead\")(30, \"tr\", 18)(31, \"th\", 19);\n            i0.ɵɵtext(32, \"ID\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(33, \"th\", 20);\n            i0.ɵɵtext(34, \"\\u89D2\\u8272\\u6B0A\\u9650\\u540D\\u7A31\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(35, \"th\", 19);\n            i0.ɵɵtext(36, \"\\u72C0\\u614B\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(37, \"th\", 21);\n            i0.ɵɵtext(38, \"\\u7570\\u52D5\\u6642\\u9593\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(39, \"th\", 22);\n            i0.ɵɵtext(40, \"\\u64CD\\u4F5C\\u529F\\u80FD\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(41, \"tbody\");\n            i0.ɵɵtemplate(42, RolePermissionsComponent_tr_42_Template, 14, 11, \"tr\", 23);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(43, \"ngx-pagination\", 24);\n            i0.ɵɵtwoWayListener(\"PageChange\", function RolePermissionsComponent_Template_ngx_pagination_PageChange_43_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.pageIndex, $event) || (ctx.pageIndex = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵlistener(\"PageChange\", function RolePermissionsComponent_Template_ngx_pagination_PageChange_43_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.getList());\n            });\n            i0.ɵɵelementEnd()()()();\n            i0.ɵɵtemplate(44, RolePermissionsComponent_ng_template_44_Template, 22, 4, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor);\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(9);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.request.CName);\n            i0.ɵɵadvance(4);\n            i0.ɵɵtwoWayProperty(\"selected\", ctx.request.CStatus);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"value\", -1);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"value\", 1);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"value\", 0);\n            i0.ɵɵadvance(6);\n            i0.ɵɵproperty(\"ngIf\", ctx.isCreate);\n            i0.ɵɵadvance(18);\n            i0.ɵɵproperty(\"ngForOf\", ctx.userGroups);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"CollectionSize\", ctx.totalRecords);\n            i0.ɵɵtwoWayProperty(\"Page\", ctx.pageIndex);\n            i0.ɵɵproperty(\"PageSize\", ctx.pageSize);\n          }\n        },\n        dependencies: [NbCardModule, i1.NbCardComponent, i1.NbCardBodyComponent, i1.NbCardFooterComponent, i1.NbCardHeaderComponent, BreadcrumbComponent, NbInputModule, i1.NbInputDirective, FormsModule, i9.DefaultValueAccessor, i9.NgControlStatus, i9.NgModel, NbSelectModule, i1.NbSelectComponent, i1.NbOptionComponent, NbOptionModule, NgIf, NgFor, PaginationComponent, NbIconModule, i1.NbIconComponent, NbCheckboxModule, i1.NbCheckboxComponent, MomentPipe, StatusPipe]\n      });\n    }\n  }\n  return RolePermissionsComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}