{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { Injectable, Directive, EventEmitter, Optional, Inject, Input, Output, NgModule } from '@angular/core';\nimport { Subject, Observable, ReplaySubject, merge, combineLatest, fromEvent } from 'rxjs';\nimport { filter, mergeMap, startWith, map, share, takeUntil, take, takeLast, count, pairwise, distinctUntilChanged } from 'rxjs/operators';\nimport { DOCUMENT } from '@angular/common';\nimport autoScroll from '@mattlewis92/dom-autoscroller';\nfunction addClass(renderer, element, classToAdd) {\n  if (classToAdd) {\n    classToAdd.split(' ').forEach(className => renderer.addClass(element.nativeElement, className));\n  }\n}\nfunction removeClass(renderer, element, classToRemove) {\n  if (classToRemove) {\n    classToRemove.split(' ').forEach(className => renderer.removeClass(element.nativeElement, className));\n  }\n}\nclass DraggableHelper {\n  constructor() {\n    this.currentDrag = new Subject();\n  }\n}\nDraggableHelper.ɵfac = function DraggableHelper_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || DraggableHelper)();\n};\nDraggableHelper.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: DraggableHelper,\n  factory: DraggableHelper.ɵfac,\n  providedIn: 'root'\n});\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(DraggableHelper, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\n\n/**\n * If the window isn't scrollable, then place this on the scrollable container that draggable elements are inside. e.g.\n * ```html\n  <div style=\"overflow: scroll\" mwlDraggableScrollContainer>\n    <div mwlDraggable>Drag me!</div>\n  </div>\n  ```\n */\nclass DraggableScrollContainerDirective {\n  /**\n   * @hidden\n   */\n  constructor(elementRef) {\n    this.elementRef = elementRef;\n  }\n}\nDraggableScrollContainerDirective.ɵfac = function DraggableScrollContainerDirective_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || DraggableScrollContainerDirective)(i0.ɵɵdirectiveInject(i0.ElementRef));\n};\nDraggableScrollContainerDirective.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: DraggableScrollContainerDirective,\n  selectors: [[\"\", \"mwlDraggableScrollContainer\", \"\"]]\n});\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(DraggableScrollContainerDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[mwlDraggableScrollContainer]'\n    }]\n  }], function () {\n    return [{\n      type: i0.ElementRef\n    }];\n  }, null);\n})();\nclass DraggableDirective {\n  /**\n   * @hidden\n   */\n  constructor(element, renderer, draggableHelper, zone, vcr, scrollContainer, document) {\n    this.element = element;\n    this.renderer = renderer;\n    this.draggableHelper = draggableHelper;\n    this.zone = zone;\n    this.vcr = vcr;\n    this.scrollContainer = scrollContainer;\n    this.document = document;\n    /**\n     * The axis along which the element is draggable\n     */\n    this.dragAxis = {\n      x: true,\n      y: true\n    };\n    /**\n     * Snap all drags to an x / y grid\n     */\n    this.dragSnapGrid = {};\n    /**\n     * Show a ghost element that shows the drag when dragging\n     */\n    this.ghostDragEnabled = true;\n    /**\n     * Show the original element when ghostDragEnabled is true\n     */\n    this.showOriginalElementWhileDragging = false;\n    /**\n     * The cursor to use when hovering over a draggable element\n     */\n    this.dragCursor = '';\n    /*\n     * Options used to control the behaviour of auto scrolling: https://www.npmjs.com/package/dom-autoscroller\n     */\n    this.autoScroll = {\n      margin: 20\n    };\n    /**\n     * Called when the element can be dragged along one axis and has the mouse or pointer device pressed on it\n     */\n    this.dragPointerDown = new EventEmitter();\n    /**\n     * Called when the element has started to be dragged.\n     * Only called after at least one mouse or touch move event.\n     * If you call $event.cancelDrag$.emit() it will cancel the current drag\n     */\n    this.dragStart = new EventEmitter();\n    /**\n     * Called after the ghost element has been created\n     */\n    this.ghostElementCreated = new EventEmitter();\n    /**\n     * Called when the element is being dragged\n     */\n    this.dragging = new EventEmitter();\n    /**\n     * Called after the element is dragged\n     */\n    this.dragEnd = new EventEmitter();\n    /**\n     * @hidden\n     */\n    this.pointerDown$ = new Subject();\n    /**\n     * @hidden\n     */\n    this.pointerMove$ = new Subject();\n    /**\n     * @hidden\n     */\n    this.pointerUp$ = new Subject();\n    this.eventListenerSubscriptions = {};\n    this.destroy$ = new Subject();\n    this.timeLongPress = {\n      timerBegin: 0,\n      timerEnd: 0\n    };\n  }\n  ngOnInit() {\n    this.checkEventListeners();\n    const pointerDragged$ = this.pointerDown$.pipe(filter(() => this.canDrag()), mergeMap(pointerDownEvent => {\n      // fix for https://github.com/mattlewis92/angular-draggable-droppable/issues/61\n      // stop mouse events propagating up the chain\n      if (pointerDownEvent.event.stopPropagation && !this.scrollContainer) {\n        pointerDownEvent.event.stopPropagation();\n      }\n      // hack to prevent text getting selected in safari while dragging\n      const globalDragStyle = this.renderer.createElement('style');\n      this.renderer.setAttribute(globalDragStyle, 'type', 'text/css');\n      this.renderer.appendChild(globalDragStyle, this.renderer.createText(`\n          body * {\n           -moz-user-select: none;\n           -ms-user-select: none;\n           -webkit-user-select: none;\n           user-select: none;\n          }\n        `));\n      requestAnimationFrame(() => {\n        this.document.head.appendChild(globalDragStyle);\n      });\n      const startScrollPosition = this.getScrollPosition();\n      const scrollContainerScroll$ = new Observable(observer => {\n        const scrollContainer = this.scrollContainer ? this.scrollContainer.elementRef.nativeElement : 'window';\n        return this.renderer.listen(scrollContainer, 'scroll', e => observer.next(e));\n      }).pipe(startWith(startScrollPosition), map(() => this.getScrollPosition()));\n      const currentDrag$ = new Subject();\n      const cancelDrag$ = new ReplaySubject();\n      if (this.dragPointerDown.observers.length > 0) {\n        this.zone.run(() => {\n          this.dragPointerDown.next({\n            x: 0,\n            y: 0\n          });\n        });\n      }\n      const dragComplete$ = merge(this.pointerUp$, this.pointerDown$, cancelDrag$, this.destroy$).pipe(share());\n      const pointerMove = combineLatest([this.pointerMove$, scrollContainerScroll$]).pipe(map(([pointerMoveEvent, scroll]) => {\n        return {\n          currentDrag$,\n          transformX: pointerMoveEvent.clientX - pointerDownEvent.clientX,\n          transformY: pointerMoveEvent.clientY - pointerDownEvent.clientY,\n          clientX: pointerMoveEvent.clientX,\n          clientY: pointerMoveEvent.clientY,\n          scrollLeft: scroll.left,\n          scrollTop: scroll.top,\n          target: pointerMoveEvent.event.target\n        };\n      }), map(moveData => {\n        if (this.dragSnapGrid.x) {\n          moveData.transformX = Math.round(moveData.transformX / this.dragSnapGrid.x) * this.dragSnapGrid.x;\n        }\n        if (this.dragSnapGrid.y) {\n          moveData.transformY = Math.round(moveData.transformY / this.dragSnapGrid.y) * this.dragSnapGrid.y;\n        }\n        return moveData;\n      }), map(moveData => {\n        if (!this.dragAxis.x) {\n          moveData.transformX = 0;\n        }\n        if (!this.dragAxis.y) {\n          moveData.transformY = 0;\n        }\n        return moveData;\n      }), map(moveData => {\n        const scrollX = moveData.scrollLeft - startScrollPosition.left;\n        const scrollY = moveData.scrollTop - startScrollPosition.top;\n        return {\n          ...moveData,\n          x: moveData.transformX + scrollX,\n          y: moveData.transformY + scrollY\n        };\n      }), filter(({\n        x,\n        y,\n        transformX,\n        transformY\n      }) => !this.validateDrag || this.validateDrag({\n        x,\n        y,\n        transform: {\n          x: transformX,\n          y: transformY\n        }\n      })), takeUntil(dragComplete$), share());\n      const dragStarted$ = pointerMove.pipe(take(1), share());\n      const dragEnded$ = pointerMove.pipe(takeLast(1), share());\n      dragStarted$.subscribe(({\n        clientX,\n        clientY,\n        x,\n        y\n      }) => {\n        if (this.dragStart.observers.length > 0) {\n          this.zone.run(() => {\n            this.dragStart.next({\n              cancelDrag$\n            });\n          });\n        }\n        this.scroller = autoScroll([this.scrollContainer ? this.scrollContainer.elementRef.nativeElement : this.document.defaultView], {\n          ...this.autoScroll,\n          autoScroll() {\n            return true;\n          }\n        });\n        addClass(this.renderer, this.element, this.dragActiveClass);\n        if (this.ghostDragEnabled) {\n          const rect = this.element.nativeElement.getBoundingClientRect();\n          const clone = this.element.nativeElement.cloneNode(true);\n          if (!this.showOriginalElementWhileDragging) {\n            this.renderer.setStyle(this.element.nativeElement, 'visibility', 'hidden');\n          }\n          if (this.ghostElementAppendTo) {\n            this.ghostElementAppendTo.appendChild(clone);\n          } else {\n            this.element.nativeElement.parentNode.insertBefore(clone, this.element.nativeElement.nextSibling);\n          }\n          this.ghostElement = clone;\n          this.document.body.style.cursor = this.dragCursor;\n          this.setElementStyles(clone, {\n            position: 'fixed',\n            top: `${rect.top}px`,\n            left: `${rect.left}px`,\n            width: `${rect.width}px`,\n            height: `${rect.height}px`,\n            cursor: this.dragCursor,\n            margin: '0',\n            willChange: 'transform',\n            pointerEvents: 'none'\n          });\n          if (this.ghostElementTemplate) {\n            const viewRef = this.vcr.createEmbeddedView(this.ghostElementTemplate);\n            clone.innerHTML = '';\n            viewRef.rootNodes.filter(node => node instanceof Node).forEach(node => {\n              clone.appendChild(node);\n            });\n            dragEnded$.subscribe(() => {\n              this.vcr.remove(this.vcr.indexOf(viewRef));\n            });\n          }\n          if (this.ghostElementCreated.observers.length > 0) {\n            this.zone.run(() => {\n              this.ghostElementCreated.emit({\n                clientX: clientX - x,\n                clientY: clientY - y,\n                element: clone\n              });\n            });\n          }\n          dragEnded$.subscribe(() => {\n            clone.parentElement.removeChild(clone);\n            this.ghostElement = null;\n            this.renderer.setStyle(this.element.nativeElement, 'visibility', '');\n          });\n        }\n        this.draggableHelper.currentDrag.next(currentDrag$);\n      });\n      dragEnded$.pipe(mergeMap(dragEndData => {\n        const dragEndData$ = cancelDrag$.pipe(count(), take(1), map(calledCount => ({\n          ...dragEndData,\n          dragCancelled: calledCount > 0\n        })));\n        cancelDrag$.complete();\n        return dragEndData$;\n      })).subscribe(({\n        x,\n        y,\n        dragCancelled\n      }) => {\n        this.scroller.destroy();\n        if (this.dragEnd.observers.length > 0) {\n          this.zone.run(() => {\n            this.dragEnd.next({\n              x,\n              y,\n              dragCancelled\n            });\n          });\n        }\n        removeClass(this.renderer, this.element, this.dragActiveClass);\n        currentDrag$.complete();\n      });\n      merge(dragComplete$, dragEnded$).pipe(take(1)).subscribe(() => {\n        requestAnimationFrame(() => {\n          this.document.head.removeChild(globalDragStyle);\n        });\n      });\n      return pointerMove;\n    }), share());\n    merge(pointerDragged$.pipe(take(1), map(value => [, value])), pointerDragged$.pipe(pairwise())).pipe(filter(([previous, next]) => {\n      if (!previous) {\n        return true;\n      }\n      return previous.x !== next.x || previous.y !== next.y;\n    }), map(([previous, next]) => next)).subscribe(({\n      x,\n      y,\n      currentDrag$,\n      clientX,\n      clientY,\n      transformX,\n      transformY,\n      target\n    }) => {\n      if (this.dragging.observers.length > 0) {\n        this.zone.run(() => {\n          this.dragging.next({\n            x,\n            y\n          });\n        });\n      }\n      requestAnimationFrame(() => {\n        if (this.ghostElement) {\n          const transform = `translate3d(${transformX}px, ${transformY}px, 0px)`;\n          this.setElementStyles(this.ghostElement, {\n            transform,\n            '-webkit-transform': transform,\n            '-ms-transform': transform,\n            '-moz-transform': transform,\n            '-o-transform': transform\n          });\n        }\n      });\n      currentDrag$.next({\n        clientX,\n        clientY,\n        dropData: this.dropData,\n        target\n      });\n    });\n  }\n  ngOnChanges(changes) {\n    if (changes.dragAxis) {\n      this.checkEventListeners();\n    }\n  }\n  ngOnDestroy() {\n    this.unsubscribeEventListeners();\n    this.pointerDown$.complete();\n    this.pointerMove$.complete();\n    this.pointerUp$.complete();\n    this.destroy$.next();\n  }\n  checkEventListeners() {\n    const canDrag = this.canDrag();\n    const hasEventListeners = Object.keys(this.eventListenerSubscriptions).length > 0;\n    if (canDrag && !hasEventListeners) {\n      this.zone.runOutsideAngular(() => {\n        this.eventListenerSubscriptions.mousedown = this.renderer.listen(this.element.nativeElement, 'mousedown', event => {\n          this.onMouseDown(event);\n        });\n        this.eventListenerSubscriptions.mouseup = this.renderer.listen('document', 'mouseup', event => {\n          this.onMouseUp(event);\n        });\n        this.eventListenerSubscriptions.touchstart = this.renderer.listen(this.element.nativeElement, 'touchstart', event => {\n          this.onTouchStart(event);\n        });\n        this.eventListenerSubscriptions.touchend = this.renderer.listen('document', 'touchend', event => {\n          this.onTouchEnd(event);\n        });\n        this.eventListenerSubscriptions.touchcancel = this.renderer.listen('document', 'touchcancel', event => {\n          this.onTouchEnd(event);\n        });\n        this.eventListenerSubscriptions.mouseenter = this.renderer.listen(this.element.nativeElement, 'mouseenter', () => {\n          this.onMouseEnter();\n        });\n        this.eventListenerSubscriptions.mouseleave = this.renderer.listen(this.element.nativeElement, 'mouseleave', () => {\n          this.onMouseLeave();\n        });\n      });\n    } else if (!canDrag && hasEventListeners) {\n      this.unsubscribeEventListeners();\n    }\n  }\n  onMouseDown(event) {\n    if (event.button === 0) {\n      if (!this.eventListenerSubscriptions.mousemove) {\n        this.eventListenerSubscriptions.mousemove = this.renderer.listen('document', 'mousemove', mouseMoveEvent => {\n          this.pointerMove$.next({\n            event: mouseMoveEvent,\n            clientX: mouseMoveEvent.clientX,\n            clientY: mouseMoveEvent.clientY\n          });\n        });\n      }\n      this.pointerDown$.next({\n        event,\n        clientX: event.clientX,\n        clientY: event.clientY\n      });\n    }\n  }\n  onMouseUp(event) {\n    if (event.button === 0) {\n      if (this.eventListenerSubscriptions.mousemove) {\n        this.eventListenerSubscriptions.mousemove();\n        delete this.eventListenerSubscriptions.mousemove;\n      }\n      this.pointerUp$.next({\n        event,\n        clientX: event.clientX,\n        clientY: event.clientY\n      });\n    }\n  }\n  onTouchStart(event) {\n    let startScrollPosition;\n    let isDragActivated;\n    let hasContainerScrollbar;\n    if (this.touchStartLongPress) {\n      this.timeLongPress.timerBegin = Date.now();\n      isDragActivated = false;\n      hasContainerScrollbar = this.hasScrollbar();\n      startScrollPosition = this.getScrollPosition();\n    }\n    if (!this.eventListenerSubscriptions.touchmove) {\n      const contextMenuListener = fromEvent(this.document, 'contextmenu').subscribe(e => {\n        e.preventDefault();\n      });\n      const touchMoveListener = fromEvent(this.document, 'touchmove', {\n        passive: false\n      }).subscribe(touchMoveEvent => {\n        if (this.touchStartLongPress && !isDragActivated && hasContainerScrollbar) {\n          isDragActivated = this.shouldBeginDrag(event, touchMoveEvent, startScrollPosition);\n        }\n        if (!this.touchStartLongPress || !hasContainerScrollbar || isDragActivated) {\n          touchMoveEvent.preventDefault();\n          this.pointerMove$.next({\n            event: touchMoveEvent,\n            clientX: touchMoveEvent.targetTouches[0].clientX,\n            clientY: touchMoveEvent.targetTouches[0].clientY\n          });\n        }\n      });\n      this.eventListenerSubscriptions.touchmove = () => {\n        contextMenuListener.unsubscribe();\n        touchMoveListener.unsubscribe();\n      };\n    }\n    this.pointerDown$.next({\n      event,\n      clientX: event.touches[0].clientX,\n      clientY: event.touches[0].clientY\n    });\n  }\n  onTouchEnd(event) {\n    if (this.eventListenerSubscriptions.touchmove) {\n      this.eventListenerSubscriptions.touchmove();\n      delete this.eventListenerSubscriptions.touchmove;\n      if (this.touchStartLongPress) {\n        this.enableScroll();\n      }\n    }\n    this.pointerUp$.next({\n      event,\n      clientX: event.changedTouches[0].clientX,\n      clientY: event.changedTouches[0].clientY\n    });\n  }\n  onMouseEnter() {\n    this.setCursor(this.dragCursor);\n  }\n  onMouseLeave() {\n    this.setCursor('');\n  }\n  canDrag() {\n    return this.dragAxis.x || this.dragAxis.y;\n  }\n  setCursor(value) {\n    if (!this.eventListenerSubscriptions.mousemove) {\n      this.renderer.setStyle(this.element.nativeElement, 'cursor', value);\n    }\n  }\n  unsubscribeEventListeners() {\n    Object.keys(this.eventListenerSubscriptions).forEach(type => {\n      this.eventListenerSubscriptions[type]();\n      delete this.eventListenerSubscriptions[type];\n    });\n  }\n  setElementStyles(element, styles) {\n    Object.keys(styles).forEach(key => {\n      this.renderer.setStyle(element, key, styles[key]);\n    });\n  }\n  getScrollElement() {\n    if (this.scrollContainer) {\n      return this.scrollContainer.elementRef.nativeElement;\n    } else {\n      return this.document.body;\n    }\n  }\n  getScrollPosition() {\n    if (this.scrollContainer) {\n      return {\n        top: this.scrollContainer.elementRef.nativeElement.scrollTop,\n        left: this.scrollContainer.elementRef.nativeElement.scrollLeft\n      };\n    } else {\n      return {\n        top: window.pageYOffset || this.document.documentElement.scrollTop,\n        left: window.pageXOffset || this.document.documentElement.scrollLeft\n      };\n    }\n  }\n  shouldBeginDrag(event, touchMoveEvent, startScrollPosition) {\n    const moveScrollPosition = this.getScrollPosition();\n    const deltaScroll = {\n      top: Math.abs(moveScrollPosition.top - startScrollPosition.top),\n      left: Math.abs(moveScrollPosition.left - startScrollPosition.left)\n    };\n    const deltaX = Math.abs(touchMoveEvent.targetTouches[0].clientX - event.touches[0].clientX) - deltaScroll.left;\n    const deltaY = Math.abs(touchMoveEvent.targetTouches[0].clientY - event.touches[0].clientY) - deltaScroll.top;\n    const deltaTotal = deltaX + deltaY;\n    const longPressConfig = this.touchStartLongPress;\n    if (deltaTotal > longPressConfig.delta || deltaScroll.top > 0 || deltaScroll.left > 0) {\n      this.timeLongPress.timerBegin = Date.now();\n    }\n    this.timeLongPress.timerEnd = Date.now();\n    const duration = this.timeLongPress.timerEnd - this.timeLongPress.timerBegin;\n    if (duration >= longPressConfig.delay) {\n      this.disableScroll();\n      return true;\n    }\n    return false;\n  }\n  enableScroll() {\n    if (this.scrollContainer) {\n      this.renderer.setStyle(this.scrollContainer.elementRef.nativeElement, 'overflow', '');\n    }\n    this.renderer.setStyle(this.document.body, 'overflow', '');\n  }\n  disableScroll() {\n    /* istanbul ignore next */\n    if (this.scrollContainer) {\n      this.renderer.setStyle(this.scrollContainer.elementRef.nativeElement, 'overflow', 'hidden');\n    }\n    this.renderer.setStyle(this.document.body, 'overflow', 'hidden');\n  }\n  hasScrollbar() {\n    const scrollContainer = this.getScrollElement();\n    const containerHasHorizontalScroll = scrollContainer.scrollWidth > scrollContainer.clientWidth;\n    const containerHasVerticalScroll = scrollContainer.scrollHeight > scrollContainer.clientHeight;\n    return containerHasHorizontalScroll || containerHasVerticalScroll;\n  }\n}\nDraggableDirective.ɵfac = function DraggableDirective_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || DraggableDirective)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(DraggableHelper), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(i0.ViewContainerRef), i0.ɵɵdirectiveInject(DraggableScrollContainerDirective, 8), i0.ɵɵdirectiveInject(DOCUMENT));\n};\nDraggableDirective.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: DraggableDirective,\n  selectors: [[\"\", \"mwlDraggable\", \"\"]],\n  inputs: {\n    dropData: \"dropData\",\n    dragAxis: \"dragAxis\",\n    dragSnapGrid: \"dragSnapGrid\",\n    ghostDragEnabled: \"ghostDragEnabled\",\n    showOriginalElementWhileDragging: \"showOriginalElementWhileDragging\",\n    validateDrag: \"validateDrag\",\n    dragCursor: \"dragCursor\",\n    dragActiveClass: \"dragActiveClass\",\n    ghostElementAppendTo: \"ghostElementAppendTo\",\n    ghostElementTemplate: \"ghostElementTemplate\",\n    touchStartLongPress: \"touchStartLongPress\",\n    autoScroll: \"autoScroll\"\n  },\n  outputs: {\n    dragPointerDown: \"dragPointerDown\",\n    dragStart: \"dragStart\",\n    ghostElementCreated: \"ghostElementCreated\",\n    dragging: \"dragging\",\n    dragEnd: \"dragEnd\"\n  },\n  features: [i0.ɵɵNgOnChangesFeature]\n});\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(DraggableDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[mwlDraggable]'\n    }]\n  }], function () {\n    return [{\n      type: i0.ElementRef\n    }, {\n      type: i0.Renderer2\n    }, {\n      type: DraggableHelper\n    }, {\n      type: i0.NgZone\n    }, {\n      type: i0.ViewContainerRef\n    }, {\n      type: DraggableScrollContainerDirective,\n      decorators: [{\n        type: Optional\n      }]\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [DOCUMENT]\n      }]\n    }];\n  }, {\n    dropData: [{\n      type: Input\n    }],\n    dragAxis: [{\n      type: Input\n    }],\n    dragSnapGrid: [{\n      type: Input\n    }],\n    ghostDragEnabled: [{\n      type: Input\n    }],\n    showOriginalElementWhileDragging: [{\n      type: Input\n    }],\n    validateDrag: [{\n      type: Input\n    }],\n    dragCursor: [{\n      type: Input\n    }],\n    dragActiveClass: [{\n      type: Input\n    }],\n    ghostElementAppendTo: [{\n      type: Input\n    }],\n    ghostElementTemplate: [{\n      type: Input\n    }],\n    touchStartLongPress: [{\n      type: Input\n    }],\n    autoScroll: [{\n      type: Input\n    }],\n    dragPointerDown: [{\n      type: Output\n    }],\n    dragStart: [{\n      type: Output\n    }],\n    ghostElementCreated: [{\n      type: Output\n    }],\n    dragging: [{\n      type: Output\n    }],\n    dragEnd: [{\n      type: Output\n    }]\n  });\n})();\nfunction isCoordinateWithinRectangle(clientX, clientY, rect) {\n  return clientX >= rect.left && clientX <= rect.right && clientY >= rect.top && clientY <= rect.bottom;\n}\nclass DroppableDirective {\n  constructor(element, draggableHelper, zone, renderer, scrollContainer) {\n    this.element = element;\n    this.draggableHelper = draggableHelper;\n    this.zone = zone;\n    this.renderer = renderer;\n    this.scrollContainer = scrollContainer;\n    /**\n     * Called when a draggable element starts overlapping the element\n     */\n    this.dragEnter = new EventEmitter();\n    /**\n     * Called when a draggable element stops overlapping the element\n     */\n    this.dragLeave = new EventEmitter();\n    /**\n     * Called when a draggable element is moved over the element\n     */\n    this.dragOver = new EventEmitter();\n    /**\n     * Called when a draggable element is dropped on this element\n     */\n    this.drop = new EventEmitter(); // eslint-disable-line  @angular-eslint/no-output-native\n  }\n  ngOnInit() {\n    this.currentDragSubscription = this.draggableHelper.currentDrag.subscribe(drag$ => {\n      addClass(this.renderer, this.element, this.dragActiveClass);\n      const droppableElement = {\n        updateCache: true\n      };\n      const deregisterScrollListener = this.renderer.listen(this.scrollContainer ? this.scrollContainer.elementRef.nativeElement : 'window', 'scroll', () => {\n        droppableElement.updateCache = true;\n      });\n      let currentDragEvent;\n      const overlaps$ = drag$.pipe(map(({\n        clientX,\n        clientY,\n        dropData,\n        target\n      }) => {\n        currentDragEvent = {\n          clientX,\n          clientY,\n          dropData,\n          target\n        };\n        if (droppableElement.updateCache) {\n          droppableElement.rect = this.element.nativeElement.getBoundingClientRect();\n          if (this.scrollContainer) {\n            droppableElement.scrollContainerRect = this.scrollContainer.elementRef.nativeElement.getBoundingClientRect();\n          }\n          droppableElement.updateCache = false;\n        }\n        const isWithinElement = isCoordinateWithinRectangle(clientX, clientY, droppableElement.rect);\n        const isDropAllowed = !this.validateDrop || this.validateDrop({\n          clientX,\n          clientY,\n          target,\n          dropData\n        });\n        if (droppableElement.scrollContainerRect) {\n          return isWithinElement && isDropAllowed && isCoordinateWithinRectangle(clientX, clientY, droppableElement.scrollContainerRect);\n        } else {\n          return isWithinElement && isDropAllowed;\n        }\n      }));\n      const overlapsChanged$ = overlaps$.pipe(distinctUntilChanged());\n      let dragOverActive; // TODO - see if there's a way of doing this via rxjs\n      overlapsChanged$.pipe(filter(overlapsNow => overlapsNow)).subscribe(() => {\n        dragOverActive = true;\n        addClass(this.renderer, this.element, this.dragOverClass);\n        if (this.dragEnter.observers.length > 0) {\n          this.zone.run(() => {\n            this.dragEnter.next(currentDragEvent);\n          });\n        }\n      });\n      overlaps$.pipe(filter(overlapsNow => overlapsNow)).subscribe(() => {\n        if (this.dragOver.observers.length > 0) {\n          this.zone.run(() => {\n            this.dragOver.next(currentDragEvent);\n          });\n        }\n      });\n      overlapsChanged$.pipe(pairwise(), filter(([didOverlap, overlapsNow]) => didOverlap && !overlapsNow)).subscribe(() => {\n        dragOverActive = false;\n        removeClass(this.renderer, this.element, this.dragOverClass);\n        if (this.dragLeave.observers.length > 0) {\n          this.zone.run(() => {\n            this.dragLeave.next(currentDragEvent);\n          });\n        }\n      });\n      drag$.subscribe({\n        complete: () => {\n          deregisterScrollListener();\n          removeClass(this.renderer, this.element, this.dragActiveClass);\n          if (dragOverActive) {\n            removeClass(this.renderer, this.element, this.dragOverClass);\n            if (this.drop.observers.length > 0) {\n              this.zone.run(() => {\n                this.drop.next(currentDragEvent);\n              });\n            }\n          }\n        }\n      });\n    });\n  }\n  ngOnDestroy() {\n    if (this.currentDragSubscription) {\n      this.currentDragSubscription.unsubscribe();\n    }\n  }\n}\nDroppableDirective.ɵfac = function DroppableDirective_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || DroppableDirective)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(DraggableHelper), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(DraggableScrollContainerDirective, 8));\n};\nDroppableDirective.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: DroppableDirective,\n  selectors: [[\"\", \"mwlDroppable\", \"\"]],\n  inputs: {\n    dragOverClass: \"dragOverClass\",\n    dragActiveClass: \"dragActiveClass\",\n    validateDrop: \"validateDrop\"\n  },\n  outputs: {\n    dragEnter: \"dragEnter\",\n    dragLeave: \"dragLeave\",\n    dragOver: \"dragOver\",\n    drop: \"drop\"\n  }\n});\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(DroppableDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[mwlDroppable]'\n    }]\n  }], function () {\n    return [{\n      type: i0.ElementRef\n    }, {\n      type: DraggableHelper\n    }, {\n      type: i0.NgZone\n    }, {\n      type: i0.Renderer2\n    }, {\n      type: DraggableScrollContainerDirective,\n      decorators: [{\n        type: Optional\n      }]\n    }];\n  }, {\n    dragOverClass: [{\n      type: Input\n    }],\n    dragActiveClass: [{\n      type: Input\n    }],\n    validateDrop: [{\n      type: Input\n    }],\n    dragEnter: [{\n      type: Output\n    }],\n    dragLeave: [{\n      type: Output\n    }],\n    dragOver: [{\n      type: Output\n    }],\n    drop: [{\n      type: Output\n    }]\n  });\n})();\nclass DragAndDropModule {}\nDragAndDropModule.ɵfac = function DragAndDropModule_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || DragAndDropModule)();\n};\nDragAndDropModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: DragAndDropModule\n});\nDragAndDropModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({});\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(DragAndDropModule, [{\n    type: NgModule,\n    args: [{\n      declarations: [DraggableDirective, DroppableDirective, DraggableScrollContainerDirective],\n      exports: [DraggableDirective, DroppableDirective, DraggableScrollContainerDirective]\n    }]\n  }], null, null);\n})();\n\n/*\n * Public API Surface of angular-draggable-droppable\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { DragAndDropModule, DraggableDirective, DraggableScrollContainerDirective, DroppableDirective };\n//# sourceMappingURL=angular-draggable-droppable.mjs.map", "map": {"version": 3, "names": ["i0", "Injectable", "Directive", "EventEmitter", "Optional", "Inject", "Input", "Output", "NgModule", "Subject", "Observable", "ReplaySubject", "merge", "combineLatest", "fromEvent", "filter", "mergeMap", "startWith", "map", "share", "takeUntil", "take", "takeLast", "count", "pairwise", "distinctUntilChanged", "DOCUMENT", "autoScroll", "addClass", "renderer", "element", "classToAdd", "split", "for<PERSON>ach", "className", "nativeElement", "removeClass", "classToRemove", "DraggableHelper", "constructor", "currentDrag", "ɵfac", "DraggableHelper_Factory", "__ngFactoryType__", "ɵprov", "ɵɵdefineInjectable", "token", "factory", "providedIn", "ngDevMode", "ɵsetClassMetadata", "type", "args", "DraggableScrollContainerDirective", "elementRef", "DraggableScrollContainerDirective_Factory", "ɵɵdirectiveInject", "ElementRef", "ɵdir", "ɵɵdefineDirective", "selectors", "selector", "DraggableDirective", "draggableHelper", "zone", "vcr", "scrollContainer", "document", "dragAxis", "x", "y", "dragSnapGrid", "ghostDragEnabled", "showOriginalElementWhileDragging", "dragCursor", "margin", "dragPointerDown", "dragStart", "ghostElementCreated", "dragging", "dragEnd", "pointerDown$", "pointerMove$", "pointerUp$", "eventListenerSubscriptions", "destroy$", "timeLongPress", "timer<PERSON><PERSON><PERSON>", "timerEnd", "ngOnInit", "checkEventListeners", "pointerDragged$", "pipe", "canDrag", "pointerDownEvent", "event", "stopPropagation", "globalDragStyle", "createElement", "setAttribute", "append<PERSON><PERSON><PERSON>", "createText", "requestAnimationFrame", "head", "startScrollPosition", "getScrollPosition", "scrollContainerScroll$", "observer", "listen", "e", "next", "currentDrag$", "cancelDrag$", "observers", "length", "run", "dragComplete$", "pointer<PERSON><PERSON>", "pointerMoveEvent", "scroll", "transformX", "clientX", "transformY", "clientY", "scrollLeft", "left", "scrollTop", "top", "target", "moveData", "Math", "round", "scrollX", "scrollY", "validateDrag", "transform", "dragStarted$", "dragEnded$", "subscribe", "scroller", "defaultView", "dragActiveClass", "rect", "getBoundingClientRect", "clone", "cloneNode", "setStyle", "ghostElementAppendTo", "parentNode", "insertBefore", "nextS<PERSON>ling", "ghostElement", "body", "style", "cursor", "setElementStyles", "position", "width", "height", "<PERSON><PERSON><PERSON><PERSON>", "pointerEvents", "ghostElementTemplate", "viewRef", "createEmbeddedView", "innerHTML", "rootNodes", "node", "Node", "remove", "indexOf", "emit", "parentElement", "<PERSON><PERSON><PERSON><PERSON>", "dragEndData", "dragEndData$", "calledCount", "dragCancelled", "complete", "destroy", "value", "previous", "dropData", "ngOnChanges", "changes", "ngOnDestroy", "unsubscribeEventListeners", "hasEventListeners", "Object", "keys", "runOutsideAngular", "mousedown", "onMouseDown", "mouseup", "onMouseUp", "touchstart", "onTouchStart", "touchend", "onTouchEnd", "touchcancel", "mouseenter", "onMouseEnter", "mouseleave", "onMouseLeave", "button", "mousemove", "mouseMoveEvent", "isDragActivated", "hasContainerScrollbar", "touchStartLongPress", "Date", "now", "hasScrollbar", "touchmove", "contextMenuListener", "preventDefault", "touchMoveListener", "passive", "touchMoveEvent", "shouldBeginDrag", "targetTouches", "unsubscribe", "touches", "enableScroll", "changedTouches", "setCursor", "styles", "key", "getScrollElement", "window", "pageYOffset", "documentElement", "pageXOffset", "moveScrollPosition", "deltaScroll", "abs", "deltaX", "deltaY", "deltaTotal", "longPressConfig", "delta", "duration", "delay", "disableScroll", "containerHasHorizontalScroll", "scrollWidth", "clientWidth", "containerHasVerticalScroll", "scrollHeight", "clientHeight", "DraggableDirective_Factory", "Renderer2", "NgZone", "ViewContainerRef", "inputs", "outputs", "features", "ɵɵNgOnChangesFeature", "decorators", "undefined", "isCoordinateWithinRectangle", "right", "bottom", "DroppableDirective", "dragEnter", "dragLeave", "dragOver", "drop", "currentDragSubscription", "drag$", "droppableElement", "updateCache", "deregisterScrollListener", "currentDragEvent", "overlaps$", "scrollContainerRect", "isWithinElement", "isDropAllowed", "validateDrop", "overlapsChanged$", "dragOverActive", "overlapsNow", "dragOverClass", "didOverlap", "DroppableDirective_Factory", "DragAndDropModule", "DragAndDropModule_Factory", "ɵmod", "ɵɵdefineNgModule", "ɵinj", "ɵɵdefineInjector", "declarations", "exports"], "sources": ["C:/Users/<USER>/Documents/jeansalechange/adminFront/node_modules/angular-draggable-droppable/fesm2020/angular-draggable-droppable.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { Injectable, Directive, EventEmitter, Optional, Inject, Input, Output, NgModule } from '@angular/core';\nimport { Subject, Observable, ReplaySubject, merge, combineLatest, fromEvent } from 'rxjs';\nimport { filter, mergeMap, startWith, map, share, takeUntil, take, takeLast, count, pairwise, distinctUntilChanged } from 'rxjs/operators';\nimport { DOCUMENT } from '@angular/common';\nimport autoScroll from '@mattlewis92/dom-autoscroller';\n\nfunction addClass(renderer, element, classToAdd) {\n    if (classToAdd) {\n        classToAdd\n            .split(' ')\n            .forEach((className) => renderer.addClass(element.nativeElement, className));\n    }\n}\nfunction removeClass(renderer, element, classToRemove) {\n    if (classToRemove) {\n        classToRemove\n            .split(' ')\n            .forEach((className) => renderer.removeClass(element.nativeElement, className));\n    }\n}\n\nclass DraggableHelper {\n    constructor() {\n        this.currentDrag = new Subject();\n    }\n}\nDraggableHelper.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.0.3\", ngImport: i0, type: DraggableHelper, deps: [], target: i0.ɵɵFactoryTarget.Injectable });\nDraggableHelper.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"15.0.3\", ngImport: i0, type: DraggableHelper, providedIn: 'root' });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.0.3\", ngImport: i0, type: DraggableHelper, decorators: [{\n            type: Injectable,\n            args: [{\n                    providedIn: 'root',\n                }]\n        }] });\n\n/**\n * If the window isn't scrollable, then place this on the scrollable container that draggable elements are inside. e.g.\n * ```html\n  <div style=\"overflow: scroll\" mwlDraggableScrollContainer>\n    <div mwlDraggable>Drag me!</div>\n  </div>\n  ```\n */\nclass DraggableScrollContainerDirective {\n    /**\n     * @hidden\n     */\n    constructor(elementRef) {\n        this.elementRef = elementRef;\n    }\n}\nDraggableScrollContainerDirective.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.0.3\", ngImport: i0, type: DraggableScrollContainerDirective, deps: [{ token: i0.ElementRef }], target: i0.ɵɵFactoryTarget.Directive });\nDraggableScrollContainerDirective.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"15.0.3\", type: DraggableScrollContainerDirective, selector: \"[mwlDraggableScrollContainer]\", ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.0.3\", ngImport: i0, type: DraggableScrollContainerDirective, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[mwlDraggableScrollContainer]',\n                }]\n        }], ctorParameters: function () { return [{ type: i0.ElementRef }]; } });\n\nclass DraggableDirective {\n    /**\n     * @hidden\n     */\n    constructor(element, renderer, draggableHelper, zone, vcr, scrollContainer, document) {\n        this.element = element;\n        this.renderer = renderer;\n        this.draggableHelper = draggableHelper;\n        this.zone = zone;\n        this.vcr = vcr;\n        this.scrollContainer = scrollContainer;\n        this.document = document;\n        /**\n         * The axis along which the element is draggable\n         */\n        this.dragAxis = { x: true, y: true };\n        /**\n         * Snap all drags to an x / y grid\n         */\n        this.dragSnapGrid = {};\n        /**\n         * Show a ghost element that shows the drag when dragging\n         */\n        this.ghostDragEnabled = true;\n        /**\n         * Show the original element when ghostDragEnabled is true\n         */\n        this.showOriginalElementWhileDragging = false;\n        /**\n         * The cursor to use when hovering over a draggable element\n         */\n        this.dragCursor = '';\n        /*\n         * Options used to control the behaviour of auto scrolling: https://www.npmjs.com/package/dom-autoscroller\n         */\n        this.autoScroll = {\n            margin: 20,\n        };\n        /**\n         * Called when the element can be dragged along one axis and has the mouse or pointer device pressed on it\n         */\n        this.dragPointerDown = new EventEmitter();\n        /**\n         * Called when the element has started to be dragged.\n         * Only called after at least one mouse or touch move event.\n         * If you call $event.cancelDrag$.emit() it will cancel the current drag\n         */\n        this.dragStart = new EventEmitter();\n        /**\n         * Called after the ghost element has been created\n         */\n        this.ghostElementCreated = new EventEmitter();\n        /**\n         * Called when the element is being dragged\n         */\n        this.dragging = new EventEmitter();\n        /**\n         * Called after the element is dragged\n         */\n        this.dragEnd = new EventEmitter();\n        /**\n         * @hidden\n         */\n        this.pointerDown$ = new Subject();\n        /**\n         * @hidden\n         */\n        this.pointerMove$ = new Subject();\n        /**\n         * @hidden\n         */\n        this.pointerUp$ = new Subject();\n        this.eventListenerSubscriptions = {};\n        this.destroy$ = new Subject();\n        this.timeLongPress = { timerBegin: 0, timerEnd: 0 };\n    }\n    ngOnInit() {\n        this.checkEventListeners();\n        const pointerDragged$ = this.pointerDown$.pipe(filter(() => this.canDrag()), mergeMap((pointerDownEvent) => {\n            // fix for https://github.com/mattlewis92/angular-draggable-droppable/issues/61\n            // stop mouse events propagating up the chain\n            if (pointerDownEvent.event.stopPropagation && !this.scrollContainer) {\n                pointerDownEvent.event.stopPropagation();\n            }\n            // hack to prevent text getting selected in safari while dragging\n            const globalDragStyle = this.renderer.createElement('style');\n            this.renderer.setAttribute(globalDragStyle, 'type', 'text/css');\n            this.renderer.appendChild(globalDragStyle, this.renderer.createText(`\n          body * {\n           -moz-user-select: none;\n           -ms-user-select: none;\n           -webkit-user-select: none;\n           user-select: none;\n          }\n        `));\n            requestAnimationFrame(() => {\n                this.document.head.appendChild(globalDragStyle);\n            });\n            const startScrollPosition = this.getScrollPosition();\n            const scrollContainerScroll$ = new Observable((observer) => {\n                const scrollContainer = this.scrollContainer\n                    ? this.scrollContainer.elementRef.nativeElement\n                    : 'window';\n                return this.renderer.listen(scrollContainer, 'scroll', (e) => observer.next(e));\n            }).pipe(startWith(startScrollPosition), map(() => this.getScrollPosition()));\n            const currentDrag$ = new Subject();\n            const cancelDrag$ = new ReplaySubject();\n            if (this.dragPointerDown.observers.length > 0) {\n                this.zone.run(() => {\n                    this.dragPointerDown.next({ x: 0, y: 0 });\n                });\n            }\n            const dragComplete$ = merge(this.pointerUp$, this.pointerDown$, cancelDrag$, this.destroy$).pipe(share());\n            const pointerMove = combineLatest([\n                this.pointerMove$,\n                scrollContainerScroll$,\n            ]).pipe(map(([pointerMoveEvent, scroll]) => {\n                return {\n                    currentDrag$,\n                    transformX: pointerMoveEvent.clientX - pointerDownEvent.clientX,\n                    transformY: pointerMoveEvent.clientY - pointerDownEvent.clientY,\n                    clientX: pointerMoveEvent.clientX,\n                    clientY: pointerMoveEvent.clientY,\n                    scrollLeft: scroll.left,\n                    scrollTop: scroll.top,\n                    target: pointerMoveEvent.event.target,\n                };\n            }), map((moveData) => {\n                if (this.dragSnapGrid.x) {\n                    moveData.transformX =\n                        Math.round(moveData.transformX / this.dragSnapGrid.x) *\n                            this.dragSnapGrid.x;\n                }\n                if (this.dragSnapGrid.y) {\n                    moveData.transformY =\n                        Math.round(moveData.transformY / this.dragSnapGrid.y) *\n                            this.dragSnapGrid.y;\n                }\n                return moveData;\n            }), map((moveData) => {\n                if (!this.dragAxis.x) {\n                    moveData.transformX = 0;\n                }\n                if (!this.dragAxis.y) {\n                    moveData.transformY = 0;\n                }\n                return moveData;\n            }), map((moveData) => {\n                const scrollX = moveData.scrollLeft - startScrollPosition.left;\n                const scrollY = moveData.scrollTop - startScrollPosition.top;\n                return {\n                    ...moveData,\n                    x: moveData.transformX + scrollX,\n                    y: moveData.transformY + scrollY,\n                };\n            }), filter(({ x, y, transformX, transformY }) => !this.validateDrag ||\n                this.validateDrag({\n                    x,\n                    y,\n                    transform: { x: transformX, y: transformY },\n                })), takeUntil(dragComplete$), share());\n            const dragStarted$ = pointerMove.pipe(take(1), share());\n            const dragEnded$ = pointerMove.pipe(takeLast(1), share());\n            dragStarted$.subscribe(({ clientX, clientY, x, y }) => {\n                if (this.dragStart.observers.length > 0) {\n                    this.zone.run(() => {\n                        this.dragStart.next({ cancelDrag$ });\n                    });\n                }\n                this.scroller = autoScroll([\n                    this.scrollContainer\n                        ? this.scrollContainer.elementRef.nativeElement\n                        : this.document.defaultView,\n                ], {\n                    ...this.autoScroll,\n                    autoScroll() {\n                        return true;\n                    },\n                });\n                addClass(this.renderer, this.element, this.dragActiveClass);\n                if (this.ghostDragEnabled) {\n                    const rect = this.element.nativeElement.getBoundingClientRect();\n                    const clone = this.element.nativeElement.cloneNode(true);\n                    if (!this.showOriginalElementWhileDragging) {\n                        this.renderer.setStyle(this.element.nativeElement, 'visibility', 'hidden');\n                    }\n                    if (this.ghostElementAppendTo) {\n                        this.ghostElementAppendTo.appendChild(clone);\n                    }\n                    else {\n                        this.element.nativeElement.parentNode.insertBefore(clone, this.element.nativeElement.nextSibling);\n                    }\n                    this.ghostElement = clone;\n                    this.document.body.style.cursor = this.dragCursor;\n                    this.setElementStyles(clone, {\n                        position: 'fixed',\n                        top: `${rect.top}px`,\n                        left: `${rect.left}px`,\n                        width: `${rect.width}px`,\n                        height: `${rect.height}px`,\n                        cursor: this.dragCursor,\n                        margin: '0',\n                        willChange: 'transform',\n                        pointerEvents: 'none',\n                    });\n                    if (this.ghostElementTemplate) {\n                        const viewRef = this.vcr.createEmbeddedView(this.ghostElementTemplate);\n                        clone.innerHTML = '';\n                        viewRef.rootNodes\n                            .filter((node) => node instanceof Node)\n                            .forEach((node) => {\n                            clone.appendChild(node);\n                        });\n                        dragEnded$.subscribe(() => {\n                            this.vcr.remove(this.vcr.indexOf(viewRef));\n                        });\n                    }\n                    if (this.ghostElementCreated.observers.length > 0) {\n                        this.zone.run(() => {\n                            this.ghostElementCreated.emit({\n                                clientX: clientX - x,\n                                clientY: clientY - y,\n                                element: clone,\n                            });\n                        });\n                    }\n                    dragEnded$.subscribe(() => {\n                        clone.parentElement.removeChild(clone);\n                        this.ghostElement = null;\n                        this.renderer.setStyle(this.element.nativeElement, 'visibility', '');\n                    });\n                }\n                this.draggableHelper.currentDrag.next(currentDrag$);\n            });\n            dragEnded$\n                .pipe(mergeMap((dragEndData) => {\n                const dragEndData$ = cancelDrag$.pipe(count(), take(1), map((calledCount) => ({\n                    ...dragEndData,\n                    dragCancelled: calledCount > 0,\n                })));\n                cancelDrag$.complete();\n                return dragEndData$;\n            }))\n                .subscribe(({ x, y, dragCancelled }) => {\n                this.scroller.destroy();\n                if (this.dragEnd.observers.length > 0) {\n                    this.zone.run(() => {\n                        this.dragEnd.next({ x, y, dragCancelled });\n                    });\n                }\n                removeClass(this.renderer, this.element, this.dragActiveClass);\n                currentDrag$.complete();\n            });\n            merge(dragComplete$, dragEnded$)\n                .pipe(take(1))\n                .subscribe(() => {\n                requestAnimationFrame(() => {\n                    this.document.head.removeChild(globalDragStyle);\n                });\n            });\n            return pointerMove;\n        }), share());\n        merge(pointerDragged$.pipe(take(1), map((value) => [, value])), pointerDragged$.pipe(pairwise()))\n            .pipe(filter(([previous, next]) => {\n            if (!previous) {\n                return true;\n            }\n            return previous.x !== next.x || previous.y !== next.y;\n        }), map(([previous, next]) => next))\n            .subscribe(({ x, y, currentDrag$, clientX, clientY, transformX, transformY, target, }) => {\n            if (this.dragging.observers.length > 0) {\n                this.zone.run(() => {\n                    this.dragging.next({ x, y });\n                });\n            }\n            requestAnimationFrame(() => {\n                if (this.ghostElement) {\n                    const transform = `translate3d(${transformX}px, ${transformY}px, 0px)`;\n                    this.setElementStyles(this.ghostElement, {\n                        transform,\n                        '-webkit-transform': transform,\n                        '-ms-transform': transform,\n                        '-moz-transform': transform,\n                        '-o-transform': transform,\n                    });\n                }\n            });\n            currentDrag$.next({\n                clientX,\n                clientY,\n                dropData: this.dropData,\n                target,\n            });\n        });\n    }\n    ngOnChanges(changes) {\n        if (changes.dragAxis) {\n            this.checkEventListeners();\n        }\n    }\n    ngOnDestroy() {\n        this.unsubscribeEventListeners();\n        this.pointerDown$.complete();\n        this.pointerMove$.complete();\n        this.pointerUp$.complete();\n        this.destroy$.next();\n    }\n    checkEventListeners() {\n        const canDrag = this.canDrag();\n        const hasEventListeners = Object.keys(this.eventListenerSubscriptions).length > 0;\n        if (canDrag && !hasEventListeners) {\n            this.zone.runOutsideAngular(() => {\n                this.eventListenerSubscriptions.mousedown = this.renderer.listen(this.element.nativeElement, 'mousedown', (event) => {\n                    this.onMouseDown(event);\n                });\n                this.eventListenerSubscriptions.mouseup = this.renderer.listen('document', 'mouseup', (event) => {\n                    this.onMouseUp(event);\n                });\n                this.eventListenerSubscriptions.touchstart = this.renderer.listen(this.element.nativeElement, 'touchstart', (event) => {\n                    this.onTouchStart(event);\n                });\n                this.eventListenerSubscriptions.touchend = this.renderer.listen('document', 'touchend', (event) => {\n                    this.onTouchEnd(event);\n                });\n                this.eventListenerSubscriptions.touchcancel = this.renderer.listen('document', 'touchcancel', (event) => {\n                    this.onTouchEnd(event);\n                });\n                this.eventListenerSubscriptions.mouseenter = this.renderer.listen(this.element.nativeElement, 'mouseenter', () => {\n                    this.onMouseEnter();\n                });\n                this.eventListenerSubscriptions.mouseleave = this.renderer.listen(this.element.nativeElement, 'mouseleave', () => {\n                    this.onMouseLeave();\n                });\n            });\n        }\n        else if (!canDrag && hasEventListeners) {\n            this.unsubscribeEventListeners();\n        }\n    }\n    onMouseDown(event) {\n        if (event.button === 0) {\n            if (!this.eventListenerSubscriptions.mousemove) {\n                this.eventListenerSubscriptions.mousemove = this.renderer.listen('document', 'mousemove', (mouseMoveEvent) => {\n                    this.pointerMove$.next({\n                        event: mouseMoveEvent,\n                        clientX: mouseMoveEvent.clientX,\n                        clientY: mouseMoveEvent.clientY,\n                    });\n                });\n            }\n            this.pointerDown$.next({\n                event,\n                clientX: event.clientX,\n                clientY: event.clientY,\n            });\n        }\n    }\n    onMouseUp(event) {\n        if (event.button === 0) {\n            if (this.eventListenerSubscriptions.mousemove) {\n                this.eventListenerSubscriptions.mousemove();\n                delete this.eventListenerSubscriptions.mousemove;\n            }\n            this.pointerUp$.next({\n                event,\n                clientX: event.clientX,\n                clientY: event.clientY,\n            });\n        }\n    }\n    onTouchStart(event) {\n        let startScrollPosition;\n        let isDragActivated;\n        let hasContainerScrollbar;\n        if (this.touchStartLongPress) {\n            this.timeLongPress.timerBegin = Date.now();\n            isDragActivated = false;\n            hasContainerScrollbar = this.hasScrollbar();\n            startScrollPosition = this.getScrollPosition();\n        }\n        if (!this.eventListenerSubscriptions.touchmove) {\n            const contextMenuListener = fromEvent(this.document, 'contextmenu').subscribe((e) => {\n                e.preventDefault();\n            });\n            const touchMoveListener = fromEvent(this.document, 'touchmove', {\n                passive: false,\n            }).subscribe((touchMoveEvent) => {\n                if (this.touchStartLongPress &&\n                    !isDragActivated &&\n                    hasContainerScrollbar) {\n                    isDragActivated = this.shouldBeginDrag(event, touchMoveEvent, startScrollPosition);\n                }\n                if (!this.touchStartLongPress ||\n                    !hasContainerScrollbar ||\n                    isDragActivated) {\n                    touchMoveEvent.preventDefault();\n                    this.pointerMove$.next({\n                        event: touchMoveEvent,\n                        clientX: touchMoveEvent.targetTouches[0].clientX,\n                        clientY: touchMoveEvent.targetTouches[0].clientY,\n                    });\n                }\n            });\n            this.eventListenerSubscriptions.touchmove = () => {\n                contextMenuListener.unsubscribe();\n                touchMoveListener.unsubscribe();\n            };\n        }\n        this.pointerDown$.next({\n            event,\n            clientX: event.touches[0].clientX,\n            clientY: event.touches[0].clientY,\n        });\n    }\n    onTouchEnd(event) {\n        if (this.eventListenerSubscriptions.touchmove) {\n            this.eventListenerSubscriptions.touchmove();\n            delete this.eventListenerSubscriptions.touchmove;\n            if (this.touchStartLongPress) {\n                this.enableScroll();\n            }\n        }\n        this.pointerUp$.next({\n            event,\n            clientX: event.changedTouches[0].clientX,\n            clientY: event.changedTouches[0].clientY,\n        });\n    }\n    onMouseEnter() {\n        this.setCursor(this.dragCursor);\n    }\n    onMouseLeave() {\n        this.setCursor('');\n    }\n    canDrag() {\n        return this.dragAxis.x || this.dragAxis.y;\n    }\n    setCursor(value) {\n        if (!this.eventListenerSubscriptions.mousemove) {\n            this.renderer.setStyle(this.element.nativeElement, 'cursor', value);\n        }\n    }\n    unsubscribeEventListeners() {\n        Object.keys(this.eventListenerSubscriptions).forEach((type) => {\n            this.eventListenerSubscriptions[type]();\n            delete this.eventListenerSubscriptions[type];\n        });\n    }\n    setElementStyles(element, styles) {\n        Object.keys(styles).forEach((key) => {\n            this.renderer.setStyle(element, key, styles[key]);\n        });\n    }\n    getScrollElement() {\n        if (this.scrollContainer) {\n            return this.scrollContainer.elementRef.nativeElement;\n        }\n        else {\n            return this.document.body;\n        }\n    }\n    getScrollPosition() {\n        if (this.scrollContainer) {\n            return {\n                top: this.scrollContainer.elementRef.nativeElement.scrollTop,\n                left: this.scrollContainer.elementRef.nativeElement.scrollLeft,\n            };\n        }\n        else {\n            return {\n                top: window.pageYOffset || this.document.documentElement.scrollTop,\n                left: window.pageXOffset || this.document.documentElement.scrollLeft,\n            };\n        }\n    }\n    shouldBeginDrag(event, touchMoveEvent, startScrollPosition) {\n        const moveScrollPosition = this.getScrollPosition();\n        const deltaScroll = {\n            top: Math.abs(moveScrollPosition.top - startScrollPosition.top),\n            left: Math.abs(moveScrollPosition.left - startScrollPosition.left),\n        };\n        const deltaX = Math.abs(touchMoveEvent.targetTouches[0].clientX - event.touches[0].clientX) - deltaScroll.left;\n        const deltaY = Math.abs(touchMoveEvent.targetTouches[0].clientY - event.touches[0].clientY) - deltaScroll.top;\n        const deltaTotal = deltaX + deltaY;\n        const longPressConfig = this.touchStartLongPress;\n        if (deltaTotal > longPressConfig.delta ||\n            deltaScroll.top > 0 ||\n            deltaScroll.left > 0) {\n            this.timeLongPress.timerBegin = Date.now();\n        }\n        this.timeLongPress.timerEnd = Date.now();\n        const duration = this.timeLongPress.timerEnd - this.timeLongPress.timerBegin;\n        if (duration >= longPressConfig.delay) {\n            this.disableScroll();\n            return true;\n        }\n        return false;\n    }\n    enableScroll() {\n        if (this.scrollContainer) {\n            this.renderer.setStyle(this.scrollContainer.elementRef.nativeElement, 'overflow', '');\n        }\n        this.renderer.setStyle(this.document.body, 'overflow', '');\n    }\n    disableScroll() {\n        /* istanbul ignore next */\n        if (this.scrollContainer) {\n            this.renderer.setStyle(this.scrollContainer.elementRef.nativeElement, 'overflow', 'hidden');\n        }\n        this.renderer.setStyle(this.document.body, 'overflow', 'hidden');\n    }\n    hasScrollbar() {\n        const scrollContainer = this.getScrollElement();\n        const containerHasHorizontalScroll = scrollContainer.scrollWidth > scrollContainer.clientWidth;\n        const containerHasVerticalScroll = scrollContainer.scrollHeight > scrollContainer.clientHeight;\n        return containerHasHorizontalScroll || containerHasVerticalScroll;\n    }\n}\nDraggableDirective.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.0.3\", ngImport: i0, type: DraggableDirective, deps: [{ token: i0.ElementRef }, { token: i0.Renderer2 }, { token: DraggableHelper }, { token: i0.NgZone }, { token: i0.ViewContainerRef }, { token: DraggableScrollContainerDirective, optional: true }, { token: DOCUMENT }], target: i0.ɵɵFactoryTarget.Directive });\nDraggableDirective.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"15.0.3\", type: DraggableDirective, selector: \"[mwlDraggable]\", inputs: { dropData: \"dropData\", dragAxis: \"dragAxis\", dragSnapGrid: \"dragSnapGrid\", ghostDragEnabled: \"ghostDragEnabled\", showOriginalElementWhileDragging: \"showOriginalElementWhileDragging\", validateDrag: \"validateDrag\", dragCursor: \"dragCursor\", dragActiveClass: \"dragActiveClass\", ghostElementAppendTo: \"ghostElementAppendTo\", ghostElementTemplate: \"ghostElementTemplate\", touchStartLongPress: \"touchStartLongPress\", autoScroll: \"autoScroll\" }, outputs: { dragPointerDown: \"dragPointerDown\", dragStart: \"dragStart\", ghostElementCreated: \"ghostElementCreated\", dragging: \"dragging\", dragEnd: \"dragEnd\" }, usesOnChanges: true, ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.0.3\", ngImport: i0, type: DraggableDirective, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[mwlDraggable]',\n                }]\n        }], ctorParameters: function () { return [{ type: i0.ElementRef }, { type: i0.Renderer2 }, { type: DraggableHelper }, { type: i0.NgZone }, { type: i0.ViewContainerRef }, { type: DraggableScrollContainerDirective, decorators: [{\n                    type: Optional\n                }] }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }]; }, propDecorators: { dropData: [{\n                type: Input\n            }], dragAxis: [{\n                type: Input\n            }], dragSnapGrid: [{\n                type: Input\n            }], ghostDragEnabled: [{\n                type: Input\n            }], showOriginalElementWhileDragging: [{\n                type: Input\n            }], validateDrag: [{\n                type: Input\n            }], dragCursor: [{\n                type: Input\n            }], dragActiveClass: [{\n                type: Input\n            }], ghostElementAppendTo: [{\n                type: Input\n            }], ghostElementTemplate: [{\n                type: Input\n            }], touchStartLongPress: [{\n                type: Input\n            }], autoScroll: [{\n                type: Input\n            }], dragPointerDown: [{\n                type: Output\n            }], dragStart: [{\n                type: Output\n            }], ghostElementCreated: [{\n                type: Output\n            }], dragging: [{\n                type: Output\n            }], dragEnd: [{\n                type: Output\n            }] } });\n\nfunction isCoordinateWithinRectangle(clientX, clientY, rect) {\n    return (clientX >= rect.left &&\n        clientX <= rect.right &&\n        clientY >= rect.top &&\n        clientY <= rect.bottom);\n}\nclass DroppableDirective {\n    constructor(element, draggableHelper, zone, renderer, scrollContainer) {\n        this.element = element;\n        this.draggableHelper = draggableHelper;\n        this.zone = zone;\n        this.renderer = renderer;\n        this.scrollContainer = scrollContainer;\n        /**\n         * Called when a draggable element starts overlapping the element\n         */\n        this.dragEnter = new EventEmitter();\n        /**\n         * Called when a draggable element stops overlapping the element\n         */\n        this.dragLeave = new EventEmitter();\n        /**\n         * Called when a draggable element is moved over the element\n         */\n        this.dragOver = new EventEmitter();\n        /**\n         * Called when a draggable element is dropped on this element\n         */\n        this.drop = new EventEmitter(); // eslint-disable-line  @angular-eslint/no-output-native\n    }\n    ngOnInit() {\n        this.currentDragSubscription = this.draggableHelper.currentDrag.subscribe((drag$) => {\n            addClass(this.renderer, this.element, this.dragActiveClass);\n            const droppableElement = {\n                updateCache: true,\n            };\n            const deregisterScrollListener = this.renderer.listen(this.scrollContainer\n                ? this.scrollContainer.elementRef.nativeElement\n                : 'window', 'scroll', () => {\n                droppableElement.updateCache = true;\n            });\n            let currentDragEvent;\n            const overlaps$ = drag$.pipe(map(({ clientX, clientY, dropData, target }) => {\n                currentDragEvent = { clientX, clientY, dropData, target };\n                if (droppableElement.updateCache) {\n                    droppableElement.rect =\n                        this.element.nativeElement.getBoundingClientRect();\n                    if (this.scrollContainer) {\n                        droppableElement.scrollContainerRect =\n                            this.scrollContainer.elementRef.nativeElement.getBoundingClientRect();\n                    }\n                    droppableElement.updateCache = false;\n                }\n                const isWithinElement = isCoordinateWithinRectangle(clientX, clientY, droppableElement.rect);\n                const isDropAllowed = !this.validateDrop ||\n                    this.validateDrop({ clientX, clientY, target, dropData });\n                if (droppableElement.scrollContainerRect) {\n                    return (isWithinElement &&\n                        isDropAllowed &&\n                        isCoordinateWithinRectangle(clientX, clientY, droppableElement.scrollContainerRect));\n                }\n                else {\n                    return isWithinElement && isDropAllowed;\n                }\n            }));\n            const overlapsChanged$ = overlaps$.pipe(distinctUntilChanged());\n            let dragOverActive; // TODO - see if there's a way of doing this via rxjs\n            overlapsChanged$\n                .pipe(filter((overlapsNow) => overlapsNow))\n                .subscribe(() => {\n                dragOverActive = true;\n                addClass(this.renderer, this.element, this.dragOverClass);\n                if (this.dragEnter.observers.length > 0) {\n                    this.zone.run(() => {\n                        this.dragEnter.next(currentDragEvent);\n                    });\n                }\n            });\n            overlaps$.pipe(filter((overlapsNow) => overlapsNow)).subscribe(() => {\n                if (this.dragOver.observers.length > 0) {\n                    this.zone.run(() => {\n                        this.dragOver.next(currentDragEvent);\n                    });\n                }\n            });\n            overlapsChanged$\n                .pipe(pairwise(), filter(([didOverlap, overlapsNow]) => didOverlap && !overlapsNow))\n                .subscribe(() => {\n                dragOverActive = false;\n                removeClass(this.renderer, this.element, this.dragOverClass);\n                if (this.dragLeave.observers.length > 0) {\n                    this.zone.run(() => {\n                        this.dragLeave.next(currentDragEvent);\n                    });\n                }\n            });\n            drag$.subscribe({\n                complete: () => {\n                    deregisterScrollListener();\n                    removeClass(this.renderer, this.element, this.dragActiveClass);\n                    if (dragOverActive) {\n                        removeClass(this.renderer, this.element, this.dragOverClass);\n                        if (this.drop.observers.length > 0) {\n                            this.zone.run(() => {\n                                this.drop.next(currentDragEvent);\n                            });\n                        }\n                    }\n                },\n            });\n        });\n    }\n    ngOnDestroy() {\n        if (this.currentDragSubscription) {\n            this.currentDragSubscription.unsubscribe();\n        }\n    }\n}\nDroppableDirective.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.0.3\", ngImport: i0, type: DroppableDirective, deps: [{ token: i0.ElementRef }, { token: DraggableHelper }, { token: i0.NgZone }, { token: i0.Renderer2 }, { token: DraggableScrollContainerDirective, optional: true }], target: i0.ɵɵFactoryTarget.Directive });\nDroppableDirective.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"15.0.3\", type: DroppableDirective, selector: \"[mwlDroppable]\", inputs: { dragOverClass: \"dragOverClass\", dragActiveClass: \"dragActiveClass\", validateDrop: \"validateDrop\" }, outputs: { dragEnter: \"dragEnter\", dragLeave: \"dragLeave\", dragOver: \"dragOver\", drop: \"drop\" }, ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.0.3\", ngImport: i0, type: DroppableDirective, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[mwlDroppable]',\n                }]\n        }], ctorParameters: function () { return [{ type: i0.ElementRef }, { type: DraggableHelper }, { type: i0.NgZone }, { type: i0.Renderer2 }, { type: DraggableScrollContainerDirective, decorators: [{\n                    type: Optional\n                }] }]; }, propDecorators: { dragOverClass: [{\n                type: Input\n            }], dragActiveClass: [{\n                type: Input\n            }], validateDrop: [{\n                type: Input\n            }], dragEnter: [{\n                type: Output\n            }], dragLeave: [{\n                type: Output\n            }], dragOver: [{\n                type: Output\n            }], drop: [{\n                type: Output\n            }] } });\n\nclass DragAndDropModule {\n}\nDragAndDropModule.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"15.0.3\", ngImport: i0, type: DragAndDropModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\nDragAndDropModule.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"15.0.3\", ngImport: i0, type: DragAndDropModule, declarations: [DraggableDirective,\n        DroppableDirective,\n        DraggableScrollContainerDirective], exports: [DraggableDirective,\n        DroppableDirective,\n        DraggableScrollContainerDirective] });\nDragAndDropModule.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"15.0.3\", ngImport: i0, type: DragAndDropModule });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"15.0.3\", ngImport: i0, type: DragAndDropModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    declarations: [\n                        DraggableDirective,\n                        DroppableDirective,\n                        DraggableScrollContainerDirective,\n                    ],\n                    exports: [\n                        DraggableDirective,\n                        DroppableDirective,\n                        DraggableScrollContainerDirective,\n                    ],\n                }]\n        }] });\n\n/*\n * Public API Surface of angular-draggable-droppable\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { DragAndDropModule, DraggableDirective, DraggableScrollContainerDirective, DroppableDirective };\n//# sourceMappingURL=angular-draggable-droppable.mjs.map\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,UAAU,EAAEC,SAAS,EAAEC,YAAY,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,KAAK,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,eAAe;AAC9G,SAASC,OAAO,EAAEC,UAAU,EAAEC,aAAa,EAAEC,KAAK,EAAEC,aAAa,EAAEC,SAAS,QAAQ,MAAM;AAC1F,SAASC,MAAM,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,GAAG,EAAEC,KAAK,EAAEC,SAAS,EAAEC,IAAI,EAAEC,QAAQ,EAAEC,KAAK,EAAEC,QAAQ,EAAEC,oBAAoB,QAAQ,gBAAgB;AAC1I,SAASC,QAAQ,QAAQ,iBAAiB;AAC1C,OAAOC,UAAU,MAAM,+BAA+B;AAEtD,SAASC,QAAQA,CAACC,QAAQ,EAAEC,OAAO,EAAEC,UAAU,EAAE;EAC7C,IAAIA,UAAU,EAAE;IACZA,UAAU,CACLC,KAAK,CAAC,GAAG,CAAC,CACVC,OAAO,CAAEC,SAAS,IAAKL,QAAQ,CAACD,QAAQ,CAACE,OAAO,CAACK,aAAa,EAAED,SAAS,CAAC,CAAC;EACpF;AACJ;AACA,SAASE,WAAWA,CAACP,QAAQ,EAAEC,OAAO,EAAEO,aAAa,EAAE;EACnD,IAAIA,aAAa,EAAE;IACfA,aAAa,CACRL,KAAK,CAAC,GAAG,CAAC,CACVC,OAAO,CAAEC,SAAS,IAAKL,QAAQ,CAACO,WAAW,CAACN,OAAO,CAACK,aAAa,EAAED,SAAS,CAAC,CAAC;EACvF;AACJ;AAEA,MAAMI,eAAe,CAAC;EAClBC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,WAAW,GAAG,IAAI/B,OAAO,CAAC,CAAC;EACpC;AACJ;AACA6B,eAAe,CAACG,IAAI,YAAAC,wBAAAC,iBAAA;EAAA,YAAAA,iBAAA,IAAwFL,eAAe;AAAA,CAAoD;AAC/KA,eAAe,CAACM,KAAK,kBAD6E5C,EAAE,CAAA6C,kBAAA;EAAAC,KAAA,EACYR,eAAe;EAAAS,OAAA,EAAfT,eAAe,CAAAG,IAAA;EAAAO,UAAA,EAAc;AAAM,EAAG;AACtJ;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAFkGjD,EAAE,CAAAkD,iBAAA,CAETZ,eAAe,EAAc,CAAC;IAC7Ga,IAAI,EAAElD,UAAU;IAChBmD,IAAI,EAAE,CAAC;MACCJ,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMK,iCAAiC,CAAC;EACpC;AACJ;AACA;EACId,WAAWA,CAACe,UAAU,EAAE;IACpB,IAAI,CAACA,UAAU,GAAGA,UAAU;EAChC;AACJ;AACAD,iCAAiC,CAACZ,IAAI,YAAAc,0CAAAZ,iBAAA;EAAA,YAAAA,iBAAA,IAAwFU,iCAAiC,EAzB7DrD,EAAE,CAAAwD,iBAAA,CAyB6ExD,EAAE,CAACyD,UAAU;AAAA,CAA4C;AAC1OJ,iCAAiC,CAACK,IAAI,kBA1B4D1D,EAAE,CAAA2D,iBAAA;EAAAR,IAAA,EA0BcE,iCAAiC;EAAAO,SAAA;AAAA,EAA4D;AAC/M;EAAA,QAAAX,SAAA,oBAAAA,SAAA,KA3BkGjD,EAAE,CAAAkD,iBAAA,CA2BTG,iCAAiC,EAAc,CAAC;IAC/HF,IAAI,EAAEjD,SAAS;IACfkD,IAAI,EAAE,CAAC;MACCS,QAAQ,EAAE;IACd,CAAC;EACT,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEV,IAAI,EAAEnD,EAAE,CAACyD;IAAW,CAAC,CAAC;EAAE,CAAC;AAAA;AAE7E,MAAMK,kBAAkB,CAAC;EACrB;AACJ;AACA;EACIvB,WAAWA,CAACT,OAAO,EAAED,QAAQ,EAAEkC,eAAe,EAAEC,IAAI,EAAEC,GAAG,EAAEC,eAAe,EAAEC,QAAQ,EAAE;IAClF,IAAI,CAACrC,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACD,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACkC,eAAe,GAAGA,eAAe;IACtC,IAAI,CAACC,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACC,GAAG,GAAGA,GAAG;IACd,IAAI,CAACC,eAAe,GAAGA,eAAe;IACtC,IAAI,CAACC,QAAQ,GAAGA,QAAQ;IACxB;AACR;AACA;IACQ,IAAI,CAACC,QAAQ,GAAG;MAAEC,CAAC,EAAE,IAAI;MAAEC,CAAC,EAAE;IAAK,CAAC;IACpC;AACR;AACA;IACQ,IAAI,CAACC,YAAY,GAAG,CAAC,CAAC;IACtB;AACR;AACA;IACQ,IAAI,CAACC,gBAAgB,GAAG,IAAI;IAC5B;AACR;AACA;IACQ,IAAI,CAACC,gCAAgC,GAAG,KAAK;IAC7C;AACR;AACA;IACQ,IAAI,CAACC,UAAU,GAAG,EAAE;IACpB;AACR;AACA;IACQ,IAAI,CAAC/C,UAAU,GAAG;MACdgD,MAAM,EAAE;IACZ,CAAC;IACD;AACR;AACA;IACQ,IAAI,CAACC,eAAe,GAAG,IAAIzE,YAAY,CAAC,CAAC;IACzC;AACR;AACA;AACA;AACA;IACQ,IAAI,CAAC0E,SAAS,GAAG,IAAI1E,YAAY,CAAC,CAAC;IACnC;AACR;AACA;IACQ,IAAI,CAAC2E,mBAAmB,GAAG,IAAI3E,YAAY,CAAC,CAAC;IAC7C;AACR;AACA;IACQ,IAAI,CAAC4E,QAAQ,GAAG,IAAI5E,YAAY,CAAC,CAAC;IAClC;AACR;AACA;IACQ,IAAI,CAAC6E,OAAO,GAAG,IAAI7E,YAAY,CAAC,CAAC;IACjC;AACR;AACA;IACQ,IAAI,CAAC8E,YAAY,GAAG,IAAIxE,OAAO,CAAC,CAAC;IACjC;AACR;AACA;IACQ,IAAI,CAACyE,YAAY,GAAG,IAAIzE,OAAO,CAAC,CAAC;IACjC;AACR;AACA;IACQ,IAAI,CAAC0E,UAAU,GAAG,IAAI1E,OAAO,CAAC,CAAC;IAC/B,IAAI,CAAC2E,0BAA0B,GAAG,CAAC,CAAC;IACpC,IAAI,CAACC,QAAQ,GAAG,IAAI5E,OAAO,CAAC,CAAC;IAC7B,IAAI,CAAC6E,aAAa,GAAG;MAAEC,UAAU,EAAE,CAAC;MAAEC,QAAQ,EAAE;IAAE,CAAC;EACvD;EACAC,QAAQA,CAAA,EAAG;IACP,IAAI,CAACC,mBAAmB,CAAC,CAAC;IAC1B,MAAMC,eAAe,GAAG,IAAI,CAACV,YAAY,CAACW,IAAI,CAAC7E,MAAM,CAAC,MAAM,IAAI,CAAC8E,OAAO,CAAC,CAAC,CAAC,EAAE7E,QAAQ,CAAE8E,gBAAgB,IAAK;MACxG;MACA;MACA,IAAIA,gBAAgB,CAACC,KAAK,CAACC,eAAe,IAAI,CAAC,IAAI,CAAC9B,eAAe,EAAE;QACjE4B,gBAAgB,CAACC,KAAK,CAACC,eAAe,CAAC,CAAC;MAC5C;MACA;MACA,MAAMC,eAAe,GAAG,IAAI,CAACpE,QAAQ,CAACqE,aAAa,CAAC,OAAO,CAAC;MAC5D,IAAI,CAACrE,QAAQ,CAACsE,YAAY,CAACF,eAAe,EAAE,MAAM,EAAE,UAAU,CAAC;MAC/D,IAAI,CAACpE,QAAQ,CAACuE,WAAW,CAACH,eAAe,EAAE,IAAI,CAACpE,QAAQ,CAACwE,UAAU,CAAC;AAChF;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,CAAC,CAAC;MACCC,qBAAqB,CAAC,MAAM;QACxB,IAAI,CAACnC,QAAQ,CAACoC,IAAI,CAACH,WAAW,CAACH,eAAe,CAAC;MACnD,CAAC,CAAC;MACF,MAAMO,mBAAmB,GAAG,IAAI,CAACC,iBAAiB,CAAC,CAAC;MACpD,MAAMC,sBAAsB,GAAG,IAAIhG,UAAU,CAAEiG,QAAQ,IAAK;QACxD,MAAMzC,eAAe,GAAG,IAAI,CAACA,eAAe,GACtC,IAAI,CAACA,eAAe,CAACZ,UAAU,CAACnB,aAAa,GAC7C,QAAQ;QACd,OAAO,IAAI,CAACN,QAAQ,CAAC+E,MAAM,CAAC1C,eAAe,EAAE,QAAQ,EAAG2C,CAAC,IAAKF,QAAQ,CAACG,IAAI,CAACD,CAAC,CAAC,CAAC;MACnF,CAAC,CAAC,CAACjB,IAAI,CAAC3E,SAAS,CAACuF,mBAAmB,CAAC,EAAEtF,GAAG,CAAC,MAAM,IAAI,CAACuF,iBAAiB,CAAC,CAAC,CAAC,CAAC;MAC5E,MAAMM,YAAY,GAAG,IAAItG,OAAO,CAAC,CAAC;MAClC,MAAMuG,WAAW,GAAG,IAAIrG,aAAa,CAAC,CAAC;MACvC,IAAI,IAAI,CAACiE,eAAe,CAACqC,SAAS,CAACC,MAAM,GAAG,CAAC,EAAE;QAC3C,IAAI,CAAClD,IAAI,CAACmD,GAAG,CAAC,MAAM;UAChB,IAAI,CAACvC,eAAe,CAACkC,IAAI,CAAC;YAAEzC,CAAC,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAE,CAAC,CAAC;QAC7C,CAAC,CAAC;MACN;MACA,MAAM8C,aAAa,GAAGxG,KAAK,CAAC,IAAI,CAACuE,UAAU,EAAE,IAAI,CAACF,YAAY,EAAE+B,WAAW,EAAE,IAAI,CAAC3B,QAAQ,CAAC,CAACO,IAAI,CAACzE,KAAK,CAAC,CAAC,CAAC;MACzG,MAAMkG,WAAW,GAAGxG,aAAa,CAAC,CAC9B,IAAI,CAACqE,YAAY,EACjBwB,sBAAsB,CACzB,CAAC,CAACd,IAAI,CAAC1E,GAAG,CAAC,CAAC,CAACoG,gBAAgB,EAAEC,MAAM,CAAC,KAAK;QACxC,OAAO;UACHR,YAAY;UACZS,UAAU,EAAEF,gBAAgB,CAACG,OAAO,GAAG3B,gBAAgB,CAAC2B,OAAO;UAC/DC,UAAU,EAAEJ,gBAAgB,CAACK,OAAO,GAAG7B,gBAAgB,CAAC6B,OAAO;UAC/DF,OAAO,EAAEH,gBAAgB,CAACG,OAAO;UACjCE,OAAO,EAAEL,gBAAgB,CAACK,OAAO;UACjCC,UAAU,EAAEL,MAAM,CAACM,IAAI;UACvBC,SAAS,EAAEP,MAAM,CAACQ,GAAG;UACrBC,MAAM,EAAEV,gBAAgB,CAACvB,KAAK,CAACiC;QACnC,CAAC;MACL,CAAC,CAAC,EAAE9G,GAAG,CAAE+G,QAAQ,IAAK;QAClB,IAAI,IAAI,CAAC1D,YAAY,CAACF,CAAC,EAAE;UACrB4D,QAAQ,CAACT,UAAU,GACfU,IAAI,CAACC,KAAK,CAACF,QAAQ,CAACT,UAAU,GAAG,IAAI,CAACjD,YAAY,CAACF,CAAC,CAAC,GACjD,IAAI,CAACE,YAAY,CAACF,CAAC;QAC/B;QACA,IAAI,IAAI,CAACE,YAAY,CAACD,CAAC,EAAE;UACrB2D,QAAQ,CAACP,UAAU,GACfQ,IAAI,CAACC,KAAK,CAACF,QAAQ,CAACP,UAAU,GAAG,IAAI,CAACnD,YAAY,CAACD,CAAC,CAAC,GACjD,IAAI,CAACC,YAAY,CAACD,CAAC;QAC/B;QACA,OAAO2D,QAAQ;MACnB,CAAC,CAAC,EAAE/G,GAAG,CAAE+G,QAAQ,IAAK;QAClB,IAAI,CAAC,IAAI,CAAC7D,QAAQ,CAACC,CAAC,EAAE;UAClB4D,QAAQ,CAACT,UAAU,GAAG,CAAC;QAC3B;QACA,IAAI,CAAC,IAAI,CAACpD,QAAQ,CAACE,CAAC,EAAE;UAClB2D,QAAQ,CAACP,UAAU,GAAG,CAAC;QAC3B;QACA,OAAOO,QAAQ;MACnB,CAAC,CAAC,EAAE/G,GAAG,CAAE+G,QAAQ,IAAK;QAClB,MAAMG,OAAO,GAAGH,QAAQ,CAACL,UAAU,GAAGpB,mBAAmB,CAACqB,IAAI;QAC9D,MAAMQ,OAAO,GAAGJ,QAAQ,CAACH,SAAS,GAAGtB,mBAAmB,CAACuB,GAAG;QAC5D,OAAO;UACH,GAAGE,QAAQ;UACX5D,CAAC,EAAE4D,QAAQ,CAACT,UAAU,GAAGY,OAAO;UAChC9D,CAAC,EAAE2D,QAAQ,CAACP,UAAU,GAAGW;QAC7B,CAAC;MACL,CAAC,CAAC,EAAEtH,MAAM,CAAC,CAAC;QAAEsD,CAAC;QAAEC,CAAC;QAAEkD,UAAU;QAAEE;MAAW,CAAC,KAAK,CAAC,IAAI,CAACY,YAAY,IAC/D,IAAI,CAACA,YAAY,CAAC;QACdjE,CAAC;QACDC,CAAC;QACDiE,SAAS,EAAE;UAAElE,CAAC,EAAEmD,UAAU;UAAElD,CAAC,EAAEoD;QAAW;MAC9C,CAAC,CAAC,CAAC,EAAEtG,SAAS,CAACgG,aAAa,CAAC,EAAEjG,KAAK,CAAC,CAAC,CAAC;MAC3C,MAAMqH,YAAY,GAAGnB,WAAW,CAACzB,IAAI,CAACvE,IAAI,CAAC,CAAC,CAAC,EAAEF,KAAK,CAAC,CAAC,CAAC;MACvD,MAAMsH,UAAU,GAAGpB,WAAW,CAACzB,IAAI,CAACtE,QAAQ,CAAC,CAAC,CAAC,EAAEH,KAAK,CAAC,CAAC,CAAC;MACzDqH,YAAY,CAACE,SAAS,CAAC,CAAC;QAAEjB,OAAO;QAAEE,OAAO;QAAEtD,CAAC;QAAEC;MAAE,CAAC,KAAK;QACnD,IAAI,IAAI,CAACO,SAAS,CAACoC,SAAS,CAACC,MAAM,GAAG,CAAC,EAAE;UACrC,IAAI,CAAClD,IAAI,CAACmD,GAAG,CAAC,MAAM;YAChB,IAAI,CAACtC,SAAS,CAACiC,IAAI,CAAC;cAAEE;YAAY,CAAC,CAAC;UACxC,CAAC,CAAC;QACN;QACA,IAAI,CAAC2B,QAAQ,GAAGhH,UAAU,CAAC,CACvB,IAAI,CAACuC,eAAe,GACd,IAAI,CAACA,eAAe,CAACZ,UAAU,CAACnB,aAAa,GAC7C,IAAI,CAACgC,QAAQ,CAACyE,WAAW,CAClC,EAAE;UACC,GAAG,IAAI,CAACjH,UAAU;UAClBA,UAAUA,CAAA,EAAG;YACT,OAAO,IAAI;UACf;QACJ,CAAC,CAAC;QACFC,QAAQ,CAAC,IAAI,CAACC,QAAQ,EAAE,IAAI,CAACC,OAAO,EAAE,IAAI,CAAC+G,eAAe,CAAC;QAC3D,IAAI,IAAI,CAACrE,gBAAgB,EAAE;UACvB,MAAMsE,IAAI,GAAG,IAAI,CAAChH,OAAO,CAACK,aAAa,CAAC4G,qBAAqB,CAAC,CAAC;UAC/D,MAAMC,KAAK,GAAG,IAAI,CAAClH,OAAO,CAACK,aAAa,CAAC8G,SAAS,CAAC,IAAI,CAAC;UACxD,IAAI,CAAC,IAAI,CAACxE,gCAAgC,EAAE;YACxC,IAAI,CAAC5C,QAAQ,CAACqH,QAAQ,CAAC,IAAI,CAACpH,OAAO,CAACK,aAAa,EAAE,YAAY,EAAE,QAAQ,CAAC;UAC9E;UACA,IAAI,IAAI,CAACgH,oBAAoB,EAAE;YAC3B,IAAI,CAACA,oBAAoB,CAAC/C,WAAW,CAAC4C,KAAK,CAAC;UAChD,CAAC,MACI;YACD,IAAI,CAAClH,OAAO,CAACK,aAAa,CAACiH,UAAU,CAACC,YAAY,CAACL,KAAK,EAAE,IAAI,CAAClH,OAAO,CAACK,aAAa,CAACmH,WAAW,CAAC;UACrG;UACA,IAAI,CAACC,YAAY,GAAGP,KAAK;UACzB,IAAI,CAAC7E,QAAQ,CAACqF,IAAI,CAACC,KAAK,CAACC,MAAM,GAAG,IAAI,CAAChF,UAAU;UACjD,IAAI,CAACiF,gBAAgB,CAACX,KAAK,EAAE;YACzBY,QAAQ,EAAE,OAAO;YACjB7B,GAAG,EAAE,GAAGe,IAAI,CAACf,GAAG,IAAI;YACpBF,IAAI,EAAE,GAAGiB,IAAI,CAACjB,IAAI,IAAI;YACtBgC,KAAK,EAAE,GAAGf,IAAI,CAACe,KAAK,IAAI;YACxBC,MAAM,EAAE,GAAGhB,IAAI,CAACgB,MAAM,IAAI;YAC1BJ,MAAM,EAAE,IAAI,CAAChF,UAAU;YACvBC,MAAM,EAAE,GAAG;YACXoF,UAAU,EAAE,WAAW;YACvBC,aAAa,EAAE;UACnB,CAAC,CAAC;UACF,IAAI,IAAI,CAACC,oBAAoB,EAAE;YAC3B,MAAMC,OAAO,GAAG,IAAI,CAACjG,GAAG,CAACkG,kBAAkB,CAAC,IAAI,CAACF,oBAAoB,CAAC;YACtEjB,KAAK,CAACoB,SAAS,GAAG,EAAE;YACpBF,OAAO,CAACG,SAAS,CACZtJ,MAAM,CAAEuJ,IAAI,IAAKA,IAAI,YAAYC,IAAI,CAAC,CACtCtI,OAAO,CAAEqI,IAAI,IAAK;cACnBtB,KAAK,CAAC5C,WAAW,CAACkE,IAAI,CAAC;YAC3B,CAAC,CAAC;YACF7B,UAAU,CAACC,SAAS,CAAC,MAAM;cACvB,IAAI,CAACzE,GAAG,CAACuG,MAAM,CAAC,IAAI,CAACvG,GAAG,CAACwG,OAAO,CAACP,OAAO,CAAC,CAAC;YAC9C,CAAC,CAAC;UACN;UACA,IAAI,IAAI,CAACpF,mBAAmB,CAACmC,SAAS,CAACC,MAAM,GAAG,CAAC,EAAE;YAC/C,IAAI,CAAClD,IAAI,CAACmD,GAAG,CAAC,MAAM;cAChB,IAAI,CAACrC,mBAAmB,CAAC4F,IAAI,CAAC;gBAC1BjD,OAAO,EAAEA,OAAO,GAAGpD,CAAC;gBACpBsD,OAAO,EAAEA,OAAO,GAAGrD,CAAC;gBACpBxC,OAAO,EAAEkH;cACb,CAAC,CAAC;YACN,CAAC,CAAC;UACN;UACAP,UAAU,CAACC,SAAS,CAAC,MAAM;YACvBM,KAAK,CAAC2B,aAAa,CAACC,WAAW,CAAC5B,KAAK,CAAC;YACtC,IAAI,CAACO,YAAY,GAAG,IAAI;YACxB,IAAI,CAAC1H,QAAQ,CAACqH,QAAQ,CAAC,IAAI,CAACpH,OAAO,CAACK,aAAa,EAAE,YAAY,EAAE,EAAE,CAAC;UACxE,CAAC,CAAC;QACN;QACA,IAAI,CAAC4B,eAAe,CAACvB,WAAW,CAACsE,IAAI,CAACC,YAAY,CAAC;MACvD,CAAC,CAAC;MACF0B,UAAU,CACL7C,IAAI,CAAC5E,QAAQ,CAAE6J,WAAW,IAAK;QAChC,MAAMC,YAAY,GAAG9D,WAAW,CAACpB,IAAI,CAACrE,KAAK,CAAC,CAAC,EAAEF,IAAI,CAAC,CAAC,CAAC,EAAEH,GAAG,CAAE6J,WAAW,KAAM;UAC1E,GAAGF,WAAW;UACdG,aAAa,EAAED,WAAW,GAAG;QACjC,CAAC,CAAC,CAAC,CAAC;QACJ/D,WAAW,CAACiE,QAAQ,CAAC,CAAC;QACtB,OAAOH,YAAY;MACvB,CAAC,CAAC,CAAC,CACEpC,SAAS,CAAC,CAAC;QAAErE,CAAC;QAAEC,CAAC;QAAE0G;MAAc,CAAC,KAAK;QACxC,IAAI,CAACrC,QAAQ,CAACuC,OAAO,CAAC,CAAC;QACvB,IAAI,IAAI,CAAClG,OAAO,CAACiC,SAAS,CAACC,MAAM,GAAG,CAAC,EAAE;UACnC,IAAI,CAAClD,IAAI,CAACmD,GAAG,CAAC,MAAM;YAChB,IAAI,CAACnC,OAAO,CAAC8B,IAAI,CAAC;cAAEzC,CAAC;cAAEC,CAAC;cAAE0G;YAAc,CAAC,CAAC;UAC9C,CAAC,CAAC;QACN;QACA5I,WAAW,CAAC,IAAI,CAACP,QAAQ,EAAE,IAAI,CAACC,OAAO,EAAE,IAAI,CAAC+G,eAAe,CAAC;QAC9D9B,YAAY,CAACkE,QAAQ,CAAC,CAAC;MAC3B,CAAC,CAAC;MACFrK,KAAK,CAACwG,aAAa,EAAEqB,UAAU,CAAC,CAC3B7C,IAAI,CAACvE,IAAI,CAAC,CAAC,CAAC,CAAC,CACbqH,SAAS,CAAC,MAAM;QACjBpC,qBAAqB,CAAC,MAAM;UACxB,IAAI,CAACnC,QAAQ,CAACoC,IAAI,CAACqE,WAAW,CAAC3E,eAAe,CAAC;QACnD,CAAC,CAAC;MACN,CAAC,CAAC;MACF,OAAOoB,WAAW;IACtB,CAAC,CAAC,EAAElG,KAAK,CAAC,CAAC,CAAC;IACZP,KAAK,CAAC+E,eAAe,CAACC,IAAI,CAACvE,IAAI,CAAC,CAAC,CAAC,EAAEH,GAAG,CAAEiK,KAAK,IAAK,GAAGA,KAAK,CAAC,CAAC,CAAC,EAAExF,eAAe,CAACC,IAAI,CAACpE,QAAQ,CAAC,CAAC,CAAC,CAAC,CAC5FoE,IAAI,CAAC7E,MAAM,CAAC,CAAC,CAACqK,QAAQ,EAAEtE,IAAI,CAAC,KAAK;MACnC,IAAI,CAACsE,QAAQ,EAAE;QACX,OAAO,IAAI;MACf;MACA,OAAOA,QAAQ,CAAC/G,CAAC,KAAKyC,IAAI,CAACzC,CAAC,IAAI+G,QAAQ,CAAC9G,CAAC,KAAKwC,IAAI,CAACxC,CAAC;IACzD,CAAC,CAAC,EAAEpD,GAAG,CAAC,CAAC,CAACkK,QAAQ,EAAEtE,IAAI,CAAC,KAAKA,IAAI,CAAC,CAAC,CAC/B4B,SAAS,CAAC,CAAC;MAAErE,CAAC;MAAEC,CAAC;MAAEyC,YAAY;MAAEU,OAAO;MAAEE,OAAO;MAAEH,UAAU;MAAEE,UAAU;MAAEM;IAAQ,CAAC,KAAK;MAC1F,IAAI,IAAI,CAACjD,QAAQ,CAACkC,SAAS,CAACC,MAAM,GAAG,CAAC,EAAE;QACpC,IAAI,CAAClD,IAAI,CAACmD,GAAG,CAAC,MAAM;UAChB,IAAI,CAACpC,QAAQ,CAAC+B,IAAI,CAAC;YAAEzC,CAAC;YAAEC;UAAE,CAAC,CAAC;QAChC,CAAC,CAAC;MACN;MACAgC,qBAAqB,CAAC,MAAM;QACxB,IAAI,IAAI,CAACiD,YAAY,EAAE;UACnB,MAAMhB,SAAS,GAAG,eAAef,UAAU,OAAOE,UAAU,UAAU;UACtE,IAAI,CAACiC,gBAAgB,CAAC,IAAI,CAACJ,YAAY,EAAE;YACrChB,SAAS;YACT,mBAAmB,EAAEA,SAAS;YAC9B,eAAe,EAAEA,SAAS;YAC1B,gBAAgB,EAAEA,SAAS;YAC3B,cAAc,EAAEA;UACpB,CAAC,CAAC;QACN;MACJ,CAAC,CAAC;MACFxB,YAAY,CAACD,IAAI,CAAC;QACdW,OAAO;QACPE,OAAO;QACP0D,QAAQ,EAAE,IAAI,CAACA,QAAQ;QACvBrD;MACJ,CAAC,CAAC;IACN,CAAC,CAAC;EACN;EACAsD,WAAWA,CAACC,OAAO,EAAE;IACjB,IAAIA,OAAO,CAACnH,QAAQ,EAAE;MAClB,IAAI,CAACsB,mBAAmB,CAAC,CAAC;IAC9B;EACJ;EACA8F,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,yBAAyB,CAAC,CAAC;IAChC,IAAI,CAACxG,YAAY,CAACgG,QAAQ,CAAC,CAAC;IAC5B,IAAI,CAAC/F,YAAY,CAAC+F,QAAQ,CAAC,CAAC;IAC5B,IAAI,CAAC9F,UAAU,CAAC8F,QAAQ,CAAC,CAAC;IAC1B,IAAI,CAAC5F,QAAQ,CAACyB,IAAI,CAAC,CAAC;EACxB;EACApB,mBAAmBA,CAAA,EAAG;IAClB,MAAMG,OAAO,GAAG,IAAI,CAACA,OAAO,CAAC,CAAC;IAC9B,MAAM6F,iBAAiB,GAAGC,MAAM,CAACC,IAAI,CAAC,IAAI,CAACxG,0BAA0B,CAAC,CAAC8B,MAAM,GAAG,CAAC;IACjF,IAAIrB,OAAO,IAAI,CAAC6F,iBAAiB,EAAE;MAC/B,IAAI,CAAC1H,IAAI,CAAC6H,iBAAiB,CAAC,MAAM;QAC9B,IAAI,CAACzG,0BAA0B,CAAC0G,SAAS,GAAG,IAAI,CAACjK,QAAQ,CAAC+E,MAAM,CAAC,IAAI,CAAC9E,OAAO,CAACK,aAAa,EAAE,WAAW,EAAG4D,KAAK,IAAK;UACjH,IAAI,CAACgG,WAAW,CAAChG,KAAK,CAAC;QAC3B,CAAC,CAAC;QACF,IAAI,CAACX,0BAA0B,CAAC4G,OAAO,GAAG,IAAI,CAACnK,QAAQ,CAAC+E,MAAM,CAAC,UAAU,EAAE,SAAS,EAAGb,KAAK,IAAK;UAC7F,IAAI,CAACkG,SAAS,CAAClG,KAAK,CAAC;QACzB,CAAC,CAAC;QACF,IAAI,CAACX,0BAA0B,CAAC8G,UAAU,GAAG,IAAI,CAACrK,QAAQ,CAAC+E,MAAM,CAAC,IAAI,CAAC9E,OAAO,CAACK,aAAa,EAAE,YAAY,EAAG4D,KAAK,IAAK;UACnH,IAAI,CAACoG,YAAY,CAACpG,KAAK,CAAC;QAC5B,CAAC,CAAC;QACF,IAAI,CAACX,0BAA0B,CAACgH,QAAQ,GAAG,IAAI,CAACvK,QAAQ,CAAC+E,MAAM,CAAC,UAAU,EAAE,UAAU,EAAGb,KAAK,IAAK;UAC/F,IAAI,CAACsG,UAAU,CAACtG,KAAK,CAAC;QAC1B,CAAC,CAAC;QACF,IAAI,CAACX,0BAA0B,CAACkH,WAAW,GAAG,IAAI,CAACzK,QAAQ,CAAC+E,MAAM,CAAC,UAAU,EAAE,aAAa,EAAGb,KAAK,IAAK;UACrG,IAAI,CAACsG,UAAU,CAACtG,KAAK,CAAC;QAC1B,CAAC,CAAC;QACF,IAAI,CAACX,0BAA0B,CAACmH,UAAU,GAAG,IAAI,CAAC1K,QAAQ,CAAC+E,MAAM,CAAC,IAAI,CAAC9E,OAAO,CAACK,aAAa,EAAE,YAAY,EAAE,MAAM;UAC9G,IAAI,CAACqK,YAAY,CAAC,CAAC;QACvB,CAAC,CAAC;QACF,IAAI,CAACpH,0BAA0B,CAACqH,UAAU,GAAG,IAAI,CAAC5K,QAAQ,CAAC+E,MAAM,CAAC,IAAI,CAAC9E,OAAO,CAACK,aAAa,EAAE,YAAY,EAAE,MAAM;UAC9G,IAAI,CAACuK,YAAY,CAAC,CAAC;QACvB,CAAC,CAAC;MACN,CAAC,CAAC;IACN,CAAC,MACI,IAAI,CAAC7G,OAAO,IAAI6F,iBAAiB,EAAE;MACpC,IAAI,CAACD,yBAAyB,CAAC,CAAC;IACpC;EACJ;EACAM,WAAWA,CAAChG,KAAK,EAAE;IACf,IAAIA,KAAK,CAAC4G,MAAM,KAAK,CAAC,EAAE;MACpB,IAAI,CAAC,IAAI,CAACvH,0BAA0B,CAACwH,SAAS,EAAE;QAC5C,IAAI,CAACxH,0BAA0B,CAACwH,SAAS,GAAG,IAAI,CAAC/K,QAAQ,CAAC+E,MAAM,CAAC,UAAU,EAAE,WAAW,EAAGiG,cAAc,IAAK;UAC1G,IAAI,CAAC3H,YAAY,CAAC4B,IAAI,CAAC;YACnBf,KAAK,EAAE8G,cAAc;YACrBpF,OAAO,EAAEoF,cAAc,CAACpF,OAAO;YAC/BE,OAAO,EAAEkF,cAAc,CAAClF;UAC5B,CAAC,CAAC;QACN,CAAC,CAAC;MACN;MACA,IAAI,CAAC1C,YAAY,CAAC6B,IAAI,CAAC;QACnBf,KAAK;QACL0B,OAAO,EAAE1B,KAAK,CAAC0B,OAAO;QACtBE,OAAO,EAAE5B,KAAK,CAAC4B;MACnB,CAAC,CAAC;IACN;EACJ;EACAsE,SAASA,CAAClG,KAAK,EAAE;IACb,IAAIA,KAAK,CAAC4G,MAAM,KAAK,CAAC,EAAE;MACpB,IAAI,IAAI,CAACvH,0BAA0B,CAACwH,SAAS,EAAE;QAC3C,IAAI,CAACxH,0BAA0B,CAACwH,SAAS,CAAC,CAAC;QAC3C,OAAO,IAAI,CAACxH,0BAA0B,CAACwH,SAAS;MACpD;MACA,IAAI,CAACzH,UAAU,CAAC2B,IAAI,CAAC;QACjBf,KAAK;QACL0B,OAAO,EAAE1B,KAAK,CAAC0B,OAAO;QACtBE,OAAO,EAAE5B,KAAK,CAAC4B;MACnB,CAAC,CAAC;IACN;EACJ;EACAwE,YAAYA,CAACpG,KAAK,EAAE;IAChB,IAAIS,mBAAmB;IACvB,IAAIsG,eAAe;IACnB,IAAIC,qBAAqB;IACzB,IAAI,IAAI,CAACC,mBAAmB,EAAE;MAC1B,IAAI,CAAC1H,aAAa,CAACC,UAAU,GAAG0H,IAAI,CAACC,GAAG,CAAC,CAAC;MAC1CJ,eAAe,GAAG,KAAK;MACvBC,qBAAqB,GAAG,IAAI,CAACI,YAAY,CAAC,CAAC;MAC3C3G,mBAAmB,GAAG,IAAI,CAACC,iBAAiB,CAAC,CAAC;IAClD;IACA,IAAI,CAAC,IAAI,CAACrB,0BAA0B,CAACgI,SAAS,EAAE;MAC5C,MAAMC,mBAAmB,GAAGvM,SAAS,CAAC,IAAI,CAACqD,QAAQ,EAAE,aAAa,CAAC,CAACuE,SAAS,CAAE7B,CAAC,IAAK;QACjFA,CAAC,CAACyG,cAAc,CAAC,CAAC;MACtB,CAAC,CAAC;MACF,MAAMC,iBAAiB,GAAGzM,SAAS,CAAC,IAAI,CAACqD,QAAQ,EAAE,WAAW,EAAE;QAC5DqJ,OAAO,EAAE;MACb,CAAC,CAAC,CAAC9E,SAAS,CAAE+E,cAAc,IAAK;QAC7B,IAAI,IAAI,CAACT,mBAAmB,IACxB,CAACF,eAAe,IAChBC,qBAAqB,EAAE;UACvBD,eAAe,GAAG,IAAI,CAACY,eAAe,CAAC3H,KAAK,EAAE0H,cAAc,EAAEjH,mBAAmB,CAAC;QACtF;QACA,IAAI,CAAC,IAAI,CAACwG,mBAAmB,IACzB,CAACD,qBAAqB,IACtBD,eAAe,EAAE;UACjBW,cAAc,CAACH,cAAc,CAAC,CAAC;UAC/B,IAAI,CAACpI,YAAY,CAAC4B,IAAI,CAAC;YACnBf,KAAK,EAAE0H,cAAc;YACrBhG,OAAO,EAAEgG,cAAc,CAACE,aAAa,CAAC,CAAC,CAAC,CAAClG,OAAO;YAChDE,OAAO,EAAE8F,cAAc,CAACE,aAAa,CAAC,CAAC,CAAC,CAAChG;UAC7C,CAAC,CAAC;QACN;MACJ,CAAC,CAAC;MACF,IAAI,CAACvC,0BAA0B,CAACgI,SAAS,GAAG,MAAM;QAC9CC,mBAAmB,CAACO,WAAW,CAAC,CAAC;QACjCL,iBAAiB,CAACK,WAAW,CAAC,CAAC;MACnC,CAAC;IACL;IACA,IAAI,CAAC3I,YAAY,CAAC6B,IAAI,CAAC;MACnBf,KAAK;MACL0B,OAAO,EAAE1B,KAAK,CAAC8H,OAAO,CAAC,CAAC,CAAC,CAACpG,OAAO;MACjCE,OAAO,EAAE5B,KAAK,CAAC8H,OAAO,CAAC,CAAC,CAAC,CAAClG;IAC9B,CAAC,CAAC;EACN;EACA0E,UAAUA,CAACtG,KAAK,EAAE;IACd,IAAI,IAAI,CAACX,0BAA0B,CAACgI,SAAS,EAAE;MAC3C,IAAI,CAAChI,0BAA0B,CAACgI,SAAS,CAAC,CAAC;MAC3C,OAAO,IAAI,CAAChI,0BAA0B,CAACgI,SAAS;MAChD,IAAI,IAAI,CAACJ,mBAAmB,EAAE;QAC1B,IAAI,CAACc,YAAY,CAAC,CAAC;MACvB;IACJ;IACA,IAAI,CAAC3I,UAAU,CAAC2B,IAAI,CAAC;MACjBf,KAAK;MACL0B,OAAO,EAAE1B,KAAK,CAACgI,cAAc,CAAC,CAAC,CAAC,CAACtG,OAAO;MACxCE,OAAO,EAAE5B,KAAK,CAACgI,cAAc,CAAC,CAAC,CAAC,CAACpG;IACrC,CAAC,CAAC;EACN;EACA6E,YAAYA,CAAA,EAAG;IACX,IAAI,CAACwB,SAAS,CAAC,IAAI,CAACtJ,UAAU,CAAC;EACnC;EACAgI,YAAYA,CAAA,EAAG;IACX,IAAI,CAACsB,SAAS,CAAC,EAAE,CAAC;EACtB;EACAnI,OAAOA,CAAA,EAAG;IACN,OAAO,IAAI,CAACzB,QAAQ,CAACC,CAAC,IAAI,IAAI,CAACD,QAAQ,CAACE,CAAC;EAC7C;EACA0J,SAASA,CAAC7C,KAAK,EAAE;IACb,IAAI,CAAC,IAAI,CAAC/F,0BAA0B,CAACwH,SAAS,EAAE;MAC5C,IAAI,CAAC/K,QAAQ,CAACqH,QAAQ,CAAC,IAAI,CAACpH,OAAO,CAACK,aAAa,EAAE,QAAQ,EAAEgJ,KAAK,CAAC;IACvE;EACJ;EACAM,yBAAyBA,CAAA,EAAG;IACxBE,MAAM,CAACC,IAAI,CAAC,IAAI,CAACxG,0BAA0B,CAAC,CAACnD,OAAO,CAAEkB,IAAI,IAAK;MAC3D,IAAI,CAACiC,0BAA0B,CAACjC,IAAI,CAAC,CAAC,CAAC;MACvC,OAAO,IAAI,CAACiC,0BAA0B,CAACjC,IAAI,CAAC;IAChD,CAAC,CAAC;EACN;EACAwG,gBAAgBA,CAAC7H,OAAO,EAAEmM,MAAM,EAAE;IAC9BtC,MAAM,CAACC,IAAI,CAACqC,MAAM,CAAC,CAAChM,OAAO,CAAEiM,GAAG,IAAK;MACjC,IAAI,CAACrM,QAAQ,CAACqH,QAAQ,CAACpH,OAAO,EAAEoM,GAAG,EAAED,MAAM,CAACC,GAAG,CAAC,CAAC;IACrD,CAAC,CAAC;EACN;EACAC,gBAAgBA,CAAA,EAAG;IACf,IAAI,IAAI,CAACjK,eAAe,EAAE;MACtB,OAAO,IAAI,CAACA,eAAe,CAACZ,UAAU,CAACnB,aAAa;IACxD,CAAC,MACI;MACD,OAAO,IAAI,CAACgC,QAAQ,CAACqF,IAAI;IAC7B;EACJ;EACA/C,iBAAiBA,CAAA,EAAG;IAChB,IAAI,IAAI,CAACvC,eAAe,EAAE;MACtB,OAAO;QACH6D,GAAG,EAAE,IAAI,CAAC7D,eAAe,CAACZ,UAAU,CAACnB,aAAa,CAAC2F,SAAS;QAC5DD,IAAI,EAAE,IAAI,CAAC3D,eAAe,CAACZ,UAAU,CAACnB,aAAa,CAACyF;MACxD,CAAC;IACL,CAAC,MACI;MACD,OAAO;QACHG,GAAG,EAAEqG,MAAM,CAACC,WAAW,IAAI,IAAI,CAAClK,QAAQ,CAACmK,eAAe,CAACxG,SAAS;QAClED,IAAI,EAAEuG,MAAM,CAACG,WAAW,IAAI,IAAI,CAACpK,QAAQ,CAACmK,eAAe,CAAC1G;MAC9D,CAAC;IACL;EACJ;EACA8F,eAAeA,CAAC3H,KAAK,EAAE0H,cAAc,EAAEjH,mBAAmB,EAAE;IACxD,MAAMgI,kBAAkB,GAAG,IAAI,CAAC/H,iBAAiB,CAAC,CAAC;IACnD,MAAMgI,WAAW,GAAG;MAChB1G,GAAG,EAAEG,IAAI,CAACwG,GAAG,CAACF,kBAAkB,CAACzG,GAAG,GAAGvB,mBAAmB,CAACuB,GAAG,CAAC;MAC/DF,IAAI,EAAEK,IAAI,CAACwG,GAAG,CAACF,kBAAkB,CAAC3G,IAAI,GAAGrB,mBAAmB,CAACqB,IAAI;IACrE,CAAC;IACD,MAAM8G,MAAM,GAAGzG,IAAI,CAACwG,GAAG,CAACjB,cAAc,CAACE,aAAa,CAAC,CAAC,CAAC,CAAClG,OAAO,GAAG1B,KAAK,CAAC8H,OAAO,CAAC,CAAC,CAAC,CAACpG,OAAO,CAAC,GAAGgH,WAAW,CAAC5G,IAAI;IAC9G,MAAM+G,MAAM,GAAG1G,IAAI,CAACwG,GAAG,CAACjB,cAAc,CAACE,aAAa,CAAC,CAAC,CAAC,CAAChG,OAAO,GAAG5B,KAAK,CAAC8H,OAAO,CAAC,CAAC,CAAC,CAAClG,OAAO,CAAC,GAAG8G,WAAW,CAAC1G,GAAG;IAC7G,MAAM8G,UAAU,GAAGF,MAAM,GAAGC,MAAM;IAClC,MAAME,eAAe,GAAG,IAAI,CAAC9B,mBAAmB;IAChD,IAAI6B,UAAU,GAAGC,eAAe,CAACC,KAAK,IAClCN,WAAW,CAAC1G,GAAG,GAAG,CAAC,IACnB0G,WAAW,CAAC5G,IAAI,GAAG,CAAC,EAAE;MACtB,IAAI,CAACvC,aAAa,CAACC,UAAU,GAAG0H,IAAI,CAACC,GAAG,CAAC,CAAC;IAC9C;IACA,IAAI,CAAC5H,aAAa,CAACE,QAAQ,GAAGyH,IAAI,CAACC,GAAG,CAAC,CAAC;IACxC,MAAM8B,QAAQ,GAAG,IAAI,CAAC1J,aAAa,CAACE,QAAQ,GAAG,IAAI,CAACF,aAAa,CAACC,UAAU;IAC5E,IAAIyJ,QAAQ,IAAIF,eAAe,CAACG,KAAK,EAAE;MACnC,IAAI,CAACC,aAAa,CAAC,CAAC;MACpB,OAAO,IAAI;IACf;IACA,OAAO,KAAK;EAChB;EACApB,YAAYA,CAAA,EAAG;IACX,IAAI,IAAI,CAAC5J,eAAe,EAAE;MACtB,IAAI,CAACrC,QAAQ,CAACqH,QAAQ,CAAC,IAAI,CAAChF,eAAe,CAACZ,UAAU,CAACnB,aAAa,EAAE,UAAU,EAAE,EAAE,CAAC;IACzF;IACA,IAAI,CAACN,QAAQ,CAACqH,QAAQ,CAAC,IAAI,CAAC/E,QAAQ,CAACqF,IAAI,EAAE,UAAU,EAAE,EAAE,CAAC;EAC9D;EACA0F,aAAaA,CAAA,EAAG;IACZ;IACA,IAAI,IAAI,CAAChL,eAAe,EAAE;MACtB,IAAI,CAACrC,QAAQ,CAACqH,QAAQ,CAAC,IAAI,CAAChF,eAAe,CAACZ,UAAU,CAACnB,aAAa,EAAE,UAAU,EAAE,QAAQ,CAAC;IAC/F;IACA,IAAI,CAACN,QAAQ,CAACqH,QAAQ,CAAC,IAAI,CAAC/E,QAAQ,CAACqF,IAAI,EAAE,UAAU,EAAE,QAAQ,CAAC;EACpE;EACA2D,YAAYA,CAAA,EAAG;IACX,MAAMjJ,eAAe,GAAG,IAAI,CAACiK,gBAAgB,CAAC,CAAC;IAC/C,MAAMgB,4BAA4B,GAAGjL,eAAe,CAACkL,WAAW,GAAGlL,eAAe,CAACmL,WAAW;IAC9F,MAAMC,0BAA0B,GAAGpL,eAAe,CAACqL,YAAY,GAAGrL,eAAe,CAACsL,YAAY;IAC9F,OAAOL,4BAA4B,IAAIG,0BAA0B;EACrE;AACJ;AACAxL,kBAAkB,CAACrB,IAAI,YAAAgN,2BAAA9M,iBAAA;EAAA,YAAAA,iBAAA,IAAwFmB,kBAAkB,EAxiB/B9D,EAAE,CAAAwD,iBAAA,CAwiB+CxD,EAAE,CAACyD,UAAU,GAxiB9DzD,EAAE,CAAAwD,iBAAA,CAwiByExD,EAAE,CAAC0P,SAAS,GAxiBvF1P,EAAE,CAAAwD,iBAAA,CAwiBkGlB,eAAe,GAxiBnHtC,EAAE,CAAAwD,iBAAA,CAwiB8HxD,EAAE,CAAC2P,MAAM,GAxiBzI3P,EAAE,CAAAwD,iBAAA,CAwiBoJxD,EAAE,CAAC4P,gBAAgB,GAxiBzK5P,EAAE,CAAAwD,iBAAA,CAwiBoLH,iCAAiC,MAxiBvNrD,EAAE,CAAAwD,iBAAA,CAwiBkP9B,QAAQ;AAAA,CAA4C;AAC1YoC,kBAAkB,CAACJ,IAAI,kBAziB2E1D,EAAE,CAAA2D,iBAAA;EAAAR,IAAA,EAyiBDW,kBAAkB;EAAAF,SAAA;EAAAiM,MAAA;IAAAxE,QAAA;IAAAjH,QAAA;IAAAG,YAAA;IAAAC,gBAAA;IAAAC,gCAAA;IAAA6D,YAAA;IAAA5D,UAAA;IAAAmE,eAAA;IAAAM,oBAAA;IAAAc,oBAAA;IAAA+C,mBAAA;IAAArL,UAAA;EAAA;EAAAmO,OAAA;IAAAlL,eAAA;IAAAC,SAAA;IAAAC,mBAAA;IAAAC,QAAA;IAAAC,OAAA;EAAA;EAAA+K,QAAA,GAziBnB/P,EAAE,CAAAgQ,oBAAA;AAAA,EAyiBkrB;AACtxB;EAAA,QAAA/M,SAAA,oBAAAA,SAAA,KA1iBkGjD,EAAE,CAAAkD,iBAAA,CA0iBTY,kBAAkB,EAAc,CAAC;IAChHX,IAAI,EAAEjD,SAAS;IACfkD,IAAI,EAAE,CAAC;MACCS,QAAQ,EAAE;IACd,CAAC;EACT,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEV,IAAI,EAAEnD,EAAE,CAACyD;IAAW,CAAC,EAAE;MAAEN,IAAI,EAAEnD,EAAE,CAAC0P;IAAU,CAAC,EAAE;MAAEvM,IAAI,EAAEb;IAAgB,CAAC,EAAE;MAAEa,IAAI,EAAEnD,EAAE,CAAC2P;IAAO,CAAC,EAAE;MAAExM,IAAI,EAAEnD,EAAE,CAAC4P;IAAiB,CAAC,EAAE;MAAEzM,IAAI,EAAEE,iCAAiC;MAAE4M,UAAU,EAAE,CAAC;QACtN9M,IAAI,EAAE/C;MACV,CAAC;IAAE,CAAC,EAAE;MAAE+C,IAAI,EAAE+M,SAAS;MAAED,UAAU,EAAE,CAAC;QAClC9M,IAAI,EAAE9C,MAAM;QACZ+C,IAAI,EAAE,CAAC1B,QAAQ;MACnB,CAAC;IAAE,CAAC,CAAC;EAAE,CAAC,EAAkB;IAAE2J,QAAQ,EAAE,CAAC;MACvClI,IAAI,EAAE7C;IACV,CAAC,CAAC;IAAE8D,QAAQ,EAAE,CAAC;MACXjB,IAAI,EAAE7C;IACV,CAAC,CAAC;IAAEiE,YAAY,EAAE,CAAC;MACfpB,IAAI,EAAE7C;IACV,CAAC,CAAC;IAAEkE,gBAAgB,EAAE,CAAC;MACnBrB,IAAI,EAAE7C;IACV,CAAC,CAAC;IAAEmE,gCAAgC,EAAE,CAAC;MACnCtB,IAAI,EAAE7C;IACV,CAAC,CAAC;IAAEgI,YAAY,EAAE,CAAC;MACfnF,IAAI,EAAE7C;IACV,CAAC,CAAC;IAAEoE,UAAU,EAAE,CAAC;MACbvB,IAAI,EAAE7C;IACV,CAAC,CAAC;IAAEuI,eAAe,EAAE,CAAC;MAClB1F,IAAI,EAAE7C;IACV,CAAC,CAAC;IAAE6I,oBAAoB,EAAE,CAAC;MACvBhG,IAAI,EAAE7C;IACV,CAAC,CAAC;IAAE2J,oBAAoB,EAAE,CAAC;MACvB9G,IAAI,EAAE7C;IACV,CAAC,CAAC;IAAE0M,mBAAmB,EAAE,CAAC;MACtB7J,IAAI,EAAE7C;IACV,CAAC,CAAC;IAAEqB,UAAU,EAAE,CAAC;MACbwB,IAAI,EAAE7C;IACV,CAAC,CAAC;IAAEsE,eAAe,EAAE,CAAC;MAClBzB,IAAI,EAAE5C;IACV,CAAC,CAAC;IAAEsE,SAAS,EAAE,CAAC;MACZ1B,IAAI,EAAE5C;IACV,CAAC,CAAC;IAAEuE,mBAAmB,EAAE,CAAC;MACtB3B,IAAI,EAAE5C;IACV,CAAC,CAAC;IAAEwE,QAAQ,EAAE,CAAC;MACX5B,IAAI,EAAE5C;IACV,CAAC,CAAC;IAAEyE,OAAO,EAAE,CAAC;MACV7B,IAAI,EAAE5C;IACV,CAAC;EAAE,CAAC;AAAA;AAEhB,SAAS4P,2BAA2BA,CAAC1I,OAAO,EAAEE,OAAO,EAAEmB,IAAI,EAAE;EACzD,OAAQrB,OAAO,IAAIqB,IAAI,CAACjB,IAAI,IACxBJ,OAAO,IAAIqB,IAAI,CAACsH,KAAK,IACrBzI,OAAO,IAAImB,IAAI,CAACf,GAAG,IACnBJ,OAAO,IAAImB,IAAI,CAACuH,MAAM;AAC9B;AACA,MAAMC,kBAAkB,CAAC;EACrB/N,WAAWA,CAACT,OAAO,EAAEiC,eAAe,EAAEC,IAAI,EAAEnC,QAAQ,EAAEqC,eAAe,EAAE;IACnE,IAAI,CAACpC,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACiC,eAAe,GAAGA,eAAe;IACtC,IAAI,CAACC,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACnC,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACqC,eAAe,GAAGA,eAAe;IACtC;AACR;AACA;IACQ,IAAI,CAACqM,SAAS,GAAG,IAAIpQ,YAAY,CAAC,CAAC;IACnC;AACR;AACA;IACQ,IAAI,CAACqQ,SAAS,GAAG,IAAIrQ,YAAY,CAAC,CAAC;IACnC;AACR;AACA;IACQ,IAAI,CAACsQ,QAAQ,GAAG,IAAItQ,YAAY,CAAC,CAAC;IAClC;AACR;AACA;IACQ,IAAI,CAACuQ,IAAI,GAAG,IAAIvQ,YAAY,CAAC,CAAC,CAAC,CAAC;EACpC;EACAsF,QAAQA,CAAA,EAAG;IACP,IAAI,CAACkL,uBAAuB,GAAG,IAAI,CAAC5M,eAAe,CAACvB,WAAW,CAACkG,SAAS,CAAEkI,KAAK,IAAK;MACjFhP,QAAQ,CAAC,IAAI,CAACC,QAAQ,EAAE,IAAI,CAACC,OAAO,EAAE,IAAI,CAAC+G,eAAe,CAAC;MAC3D,MAAMgI,gBAAgB,GAAG;QACrBC,WAAW,EAAE;MACjB,CAAC;MACD,MAAMC,wBAAwB,GAAG,IAAI,CAAClP,QAAQ,CAAC+E,MAAM,CAAC,IAAI,CAAC1C,eAAe,GACpE,IAAI,CAACA,eAAe,CAACZ,UAAU,CAACnB,aAAa,GAC7C,QAAQ,EAAE,QAAQ,EAAE,MAAM;QAC5B0O,gBAAgB,CAACC,WAAW,GAAG,IAAI;MACvC,CAAC,CAAC;MACF,IAAIE,gBAAgB;MACpB,MAAMC,SAAS,GAAGL,KAAK,CAAChL,IAAI,CAAC1E,GAAG,CAAC,CAAC;QAAEuG,OAAO;QAAEE,OAAO;QAAE0D,QAAQ;QAAErD;MAAO,CAAC,KAAK;QACzEgJ,gBAAgB,GAAG;UAAEvJ,OAAO;UAAEE,OAAO;UAAE0D,QAAQ;UAAErD;QAAO,CAAC;QACzD,IAAI6I,gBAAgB,CAACC,WAAW,EAAE;UAC9BD,gBAAgB,CAAC/H,IAAI,GACjB,IAAI,CAAChH,OAAO,CAACK,aAAa,CAAC4G,qBAAqB,CAAC,CAAC;UACtD,IAAI,IAAI,CAAC7E,eAAe,EAAE;YACtB2M,gBAAgB,CAACK,mBAAmB,GAChC,IAAI,CAAChN,eAAe,CAACZ,UAAU,CAACnB,aAAa,CAAC4G,qBAAqB,CAAC,CAAC;UAC7E;UACA8H,gBAAgB,CAACC,WAAW,GAAG,KAAK;QACxC;QACA,MAAMK,eAAe,GAAGhB,2BAA2B,CAAC1I,OAAO,EAAEE,OAAO,EAAEkJ,gBAAgB,CAAC/H,IAAI,CAAC;QAC5F,MAAMsI,aAAa,GAAG,CAAC,IAAI,CAACC,YAAY,IACpC,IAAI,CAACA,YAAY,CAAC;UAAE5J,OAAO;UAAEE,OAAO;UAAEK,MAAM;UAAEqD;QAAS,CAAC,CAAC;QAC7D,IAAIwF,gBAAgB,CAACK,mBAAmB,EAAE;UACtC,OAAQC,eAAe,IACnBC,aAAa,IACbjB,2BAA2B,CAAC1I,OAAO,EAAEE,OAAO,EAAEkJ,gBAAgB,CAACK,mBAAmB,CAAC;QAC3F,CAAC,MACI;UACD,OAAOC,eAAe,IAAIC,aAAa;QAC3C;MACJ,CAAC,CAAC,CAAC;MACH,MAAME,gBAAgB,GAAGL,SAAS,CAACrL,IAAI,CAACnE,oBAAoB,CAAC,CAAC,CAAC;MAC/D,IAAI8P,cAAc,CAAC,CAAC;MACpBD,gBAAgB,CACX1L,IAAI,CAAC7E,MAAM,CAAEyQ,WAAW,IAAKA,WAAW,CAAC,CAAC,CAC1C9I,SAAS,CAAC,MAAM;QACjB6I,cAAc,GAAG,IAAI;QACrB3P,QAAQ,CAAC,IAAI,CAACC,QAAQ,EAAE,IAAI,CAACC,OAAO,EAAE,IAAI,CAAC2P,aAAa,CAAC;QACzD,IAAI,IAAI,CAAClB,SAAS,CAACtJ,SAAS,CAACC,MAAM,GAAG,CAAC,EAAE;UACrC,IAAI,CAAClD,IAAI,CAACmD,GAAG,CAAC,MAAM;YAChB,IAAI,CAACoJ,SAAS,CAACzJ,IAAI,CAACkK,gBAAgB,CAAC;UACzC,CAAC,CAAC;QACN;MACJ,CAAC,CAAC;MACFC,SAAS,CAACrL,IAAI,CAAC7E,MAAM,CAAEyQ,WAAW,IAAKA,WAAW,CAAC,CAAC,CAAC9I,SAAS,CAAC,MAAM;QACjE,IAAI,IAAI,CAAC+H,QAAQ,CAACxJ,SAAS,CAACC,MAAM,GAAG,CAAC,EAAE;UACpC,IAAI,CAAClD,IAAI,CAACmD,GAAG,CAAC,MAAM;YAChB,IAAI,CAACsJ,QAAQ,CAAC3J,IAAI,CAACkK,gBAAgB,CAAC;UACxC,CAAC,CAAC;QACN;MACJ,CAAC,CAAC;MACFM,gBAAgB,CACX1L,IAAI,CAACpE,QAAQ,CAAC,CAAC,EAAET,MAAM,CAAC,CAAC,CAAC2Q,UAAU,EAAEF,WAAW,CAAC,KAAKE,UAAU,IAAI,CAACF,WAAW,CAAC,CAAC,CACnF9I,SAAS,CAAC,MAAM;QACjB6I,cAAc,GAAG,KAAK;QACtBnP,WAAW,CAAC,IAAI,CAACP,QAAQ,EAAE,IAAI,CAACC,OAAO,EAAE,IAAI,CAAC2P,aAAa,CAAC;QAC5D,IAAI,IAAI,CAACjB,SAAS,CAACvJ,SAAS,CAACC,MAAM,GAAG,CAAC,EAAE;UACrC,IAAI,CAAClD,IAAI,CAACmD,GAAG,CAAC,MAAM;YAChB,IAAI,CAACqJ,SAAS,CAAC1J,IAAI,CAACkK,gBAAgB,CAAC;UACzC,CAAC,CAAC;QACN;MACJ,CAAC,CAAC;MACFJ,KAAK,CAAClI,SAAS,CAAC;QACZuC,QAAQ,EAAEA,CAAA,KAAM;UACZ8F,wBAAwB,CAAC,CAAC;UAC1B3O,WAAW,CAAC,IAAI,CAACP,QAAQ,EAAE,IAAI,CAACC,OAAO,EAAE,IAAI,CAAC+G,eAAe,CAAC;UAC9D,IAAI0I,cAAc,EAAE;YAChBnP,WAAW,CAAC,IAAI,CAACP,QAAQ,EAAE,IAAI,CAACC,OAAO,EAAE,IAAI,CAAC2P,aAAa,CAAC;YAC5D,IAAI,IAAI,CAACf,IAAI,CAACzJ,SAAS,CAACC,MAAM,GAAG,CAAC,EAAE;cAChC,IAAI,CAAClD,IAAI,CAACmD,GAAG,CAAC,MAAM;gBAChB,IAAI,CAACuJ,IAAI,CAAC5J,IAAI,CAACkK,gBAAgB,CAAC;cACpC,CAAC,CAAC;YACN;UACJ;QACJ;MACJ,CAAC,CAAC;IACN,CAAC,CAAC;EACN;EACAxF,WAAWA,CAAA,EAAG;IACV,IAAI,IAAI,CAACmF,uBAAuB,EAAE;MAC9B,IAAI,CAACA,uBAAuB,CAAC/C,WAAW,CAAC,CAAC;IAC9C;EACJ;AACJ;AACA0C,kBAAkB,CAAC7N,IAAI,YAAAkP,2BAAAhP,iBAAA;EAAA,YAAAA,iBAAA,IAAwF2N,kBAAkB,EA9sB/BtQ,EAAE,CAAAwD,iBAAA,CA8sB+CxD,EAAE,CAACyD,UAAU,GA9sB9DzD,EAAE,CAAAwD,iBAAA,CA8sByElB,eAAe,GA9sB1FtC,EAAE,CAAAwD,iBAAA,CA8sBqGxD,EAAE,CAAC2P,MAAM,GA9sBhH3P,EAAE,CAAAwD,iBAAA,CA8sB2HxD,EAAE,CAAC0P,SAAS,GA9sBzI1P,EAAE,CAAAwD,iBAAA,CA8sBoJH,iCAAiC;AAAA,CAA4D;AACrViN,kBAAkB,CAAC5M,IAAI,kBA/sB2E1D,EAAE,CAAA2D,iBAAA;EAAAR,IAAA,EA+sBDmN,kBAAkB;EAAA1M,SAAA;EAAAiM,MAAA;IAAA4B,aAAA;IAAA5I,eAAA;IAAAwI,YAAA;EAAA;EAAAvB,OAAA;IAAAS,SAAA;IAAAC,SAAA;IAAAC,QAAA;IAAAC,IAAA;EAAA;AAAA,EAA4P;AACjX;EAAA,QAAAzN,SAAA,oBAAAA,SAAA,KAhtBkGjD,EAAE,CAAAkD,iBAAA,CAgtBToN,kBAAkB,EAAc,CAAC;IAChHnN,IAAI,EAAEjD,SAAS;IACfkD,IAAI,EAAE,CAAC;MACCS,QAAQ,EAAE;IACd,CAAC;EACT,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEV,IAAI,EAAEnD,EAAE,CAACyD;IAAW,CAAC,EAAE;MAAEN,IAAI,EAAEb;IAAgB,CAAC,EAAE;MAAEa,IAAI,EAAEnD,EAAE,CAAC2P;IAAO,CAAC,EAAE;MAAExM,IAAI,EAAEnD,EAAE,CAAC0P;IAAU,CAAC,EAAE;MAAEvM,IAAI,EAAEE,iCAAiC;MAAE4M,UAAU,EAAE,CAAC;QACvL9M,IAAI,EAAE/C;MACV,CAAC;IAAE,CAAC,CAAC;EAAE,CAAC,EAAkB;IAAEqR,aAAa,EAAE,CAAC;MAC5CtO,IAAI,EAAE7C;IACV,CAAC,CAAC;IAAEuI,eAAe,EAAE,CAAC;MAClB1F,IAAI,EAAE7C;IACV,CAAC,CAAC;IAAE+Q,YAAY,EAAE,CAAC;MACflO,IAAI,EAAE7C;IACV,CAAC,CAAC;IAAEiQ,SAAS,EAAE,CAAC;MACZpN,IAAI,EAAE5C;IACV,CAAC,CAAC;IAAEiQ,SAAS,EAAE,CAAC;MACZrN,IAAI,EAAE5C;IACV,CAAC,CAAC;IAAEkQ,QAAQ,EAAE,CAAC;MACXtN,IAAI,EAAE5C;IACV,CAAC,CAAC;IAAEmQ,IAAI,EAAE,CAAC;MACPvN,IAAI,EAAE5C;IACV,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAMqR,iBAAiB,CAAC;AAExBA,iBAAiB,CAACnP,IAAI,YAAAoP,0BAAAlP,iBAAA;EAAA,YAAAA,iBAAA,IAAwFiP,iBAAiB;AAAA,CAAkD;AACjLA,iBAAiB,CAACE,IAAI,kBA1uB4E9R,EAAE,CAAA+R,gBAAA;EAAA5O,IAAA,EA0uBWyO;AAAiB,EAInF;AAC7CA,iBAAiB,CAACI,IAAI,kBA/uB4EhS,EAAE,CAAAiS,gBAAA,IA+uB+B;AACnI;EAAA,QAAAhP,SAAA,oBAAAA,SAAA,KAhvBkGjD,EAAE,CAAAkD,iBAAA,CAgvBT0O,iBAAiB,EAAc,CAAC;IAC/GzO,IAAI,EAAE3C,QAAQ;IACd4C,IAAI,EAAE,CAAC;MACC8O,YAAY,EAAE,CACVpO,kBAAkB,EAClBwM,kBAAkB,EAClBjN,iCAAiC,CACpC;MACD8O,OAAO,EAAE,CACLrO,kBAAkB,EAClBwM,kBAAkB,EAClBjN,iCAAiC;IAEzC,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;;AAEA;AACA;AACA;;AAEA,SAASuO,iBAAiB,EAAE9N,kBAAkB,EAAET,iCAAiC,EAAEiN,kBAAkB;AACrG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}