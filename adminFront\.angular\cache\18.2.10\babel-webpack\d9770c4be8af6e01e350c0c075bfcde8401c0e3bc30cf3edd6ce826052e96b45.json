{"ast": null, "code": "import { dateTimestampProvider } from './scheduler/dateTimestampProvider';\nexport class Scheduler {\n  constructor(schedulerActionCtor, now = Scheduler.now) {\n    this.schedulerActionCtor = schedulerActionCtor;\n    this.now = now;\n  }\n  schedule(work, delay = 0, state) {\n    return new this.schedulerActionCtor(this, work).schedule(state, delay);\n  }\n}\nScheduler.now = dateTimestampProvider.now;", "map": {"version": 3, "names": ["dateTimestampProvider", "Scheduler", "constructor", "schedulerActionCtor", "now", "schedule", "work", "delay", "state"], "sources": ["C:/Users/<USER>/Documents/jeansalechange/adminFront/node_modules/rxjs/dist/esm/internal/Scheduler.js"], "sourcesContent": ["import { dateTimestampProvider } from './scheduler/dateTimestampProvider';\nexport class Scheduler {\n    constructor(schedulerActionCtor, now = Scheduler.now) {\n        this.schedulerActionCtor = schedulerActionCtor;\n        this.now = now;\n    }\n    schedule(work, delay = 0, state) {\n        return new this.schedulerActionCtor(this, work).schedule(state, delay);\n    }\n}\nScheduler.now = dateTimestampProvider.now;\n"], "mappings": "AAAA,SAASA,qBAAqB,QAAQ,mCAAmC;AACzE,OAAO,MAAMC,SAAS,CAAC;EACnBC,WAAWA,CAACC,mBAAmB,EAAEC,GAAG,GAAGH,SAAS,CAACG,GAAG,EAAE;IAClD,IAAI,CAACD,mBAAmB,GAAGA,mBAAmB;IAC9C,IAAI,CAACC,GAAG,GAAGA,GAAG;EAClB;EACAC,QAAQA,CAACC,IAAI,EAAEC,KAAK,GAAG,CAAC,EAAEC,KAAK,EAAE;IAC7B,OAAO,IAAI,IAAI,CAACL,mBAAmB,CAAC,IAAI,EAAEG,IAAI,CAAC,CAACD,QAAQ,CAACG,KAAK,EAAED,KAAK,CAAC;EAC1E;AACJ;AACAN,SAAS,CAACG,GAAG,GAAGJ,qBAAqB,CAACI,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}