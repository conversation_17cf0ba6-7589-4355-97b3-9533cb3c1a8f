{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/Documents/salechange-product/adminFront/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { NbCardModule, NbCheckboxModule, NbInputModule, NbOptionModule, NbSelectModule } from '@nebular/theme';\nimport { BaseComponent } from '../components/base/baseComponent';\nimport { takeUntilDestroyed } from '@angular/core/rxjs-interop';\nimport { BreadcrumbComponent } from '../components/breadcrumb/breadcrumb.component';\nimport { FormsModule } from '@angular/forms';\nimport { NgFor, NgIf } from '@angular/common';\nimport { PaginationComponent } from '../components/pagination/pagination.component';\nimport { StatusPipe } from 'src/app/@theme/pipes/mapping.pipe';\nimport { FormGroupComponent } from 'src/app/@theme/components/form-group/form-group.component';\nimport { NumberWithCommasPipe } from 'src/app/@theme/pipes/number-with-commas.pipe';\nimport { EnumHouseType } from 'src/app/shared/enum/enumHouseType';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/shared/helper/allowHelper\";\nimport * as i2 from \"src/app/shared/helper/enumHelper\";\nimport * as i3 from \"@nebular/theme\";\nimport * as i4 from \"src/app/shared/services/message.service\";\nimport * as i5 from \"src/app/shared/helper/validationHelper\";\nimport * as i6 from \"src/services/api/services\";\nimport * as i7 from \"src/app/shared/helper/petternHelper\";\nimport * as i8 from \"@angular/router\";\nimport * as i9 from \"@angular/forms\";\nconst _c0 = () => [];\nfunction RequirementManagementComponent_nb_option_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 8);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const case_r2 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", case_r2.cID);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", case_r2.CBuildCaseName, \" \");\n  }\n}\nfunction RequirementManagementComponent_nb_option_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 8);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const type_r3 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", type_r3.value);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", type_r3.label, \" \");\n  }\n}\nfunction RequirementManagementComponent_button_46_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 31);\n    i0.ɵɵlistener(\"click\", function RequirementManagementComponent_button_46_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r4 = i0.ɵɵnextContext();\n      const dialog_r6 = i0.ɵɵreference(73);\n      return i0.ɵɵresetView(ctx_r4.add(dialog_r6));\n    });\n    i0.ɵɵelement(1, \"i\", 32);\n    i0.ɵɵtext(2, \"\\u65B0\\u589E\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RequirementManagementComponent_tr_70_button_18_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 38);\n    i0.ɵɵlistener(\"click\", function RequirementManagementComponent_tr_70_button_18_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const data_r8 = i0.ɵɵnextContext().$implicit;\n      const ctx_r4 = i0.ɵɵnextContext();\n      const dialog_r6 = i0.ɵɵreference(73);\n      return i0.ɵɵresetView(ctx_r4.onEdit(data_r8, dialog_r6));\n    });\n    i0.ɵɵelement(1, \"i\", 39);\n    i0.ɵɵtext(2, \"\\u7DE8\\u8F2F\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RequirementManagementComponent_tr_70_button_19_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 40);\n    i0.ɵɵlistener(\"click\", function RequirementManagementComponent_tr_70_button_19_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const data_r8 = i0.ɵɵnextContext().$implicit;\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.onDelete(data_r8));\n    });\n    i0.ɵɵelement(1, \"i\", 41);\n    i0.ɵɵtext(2, \"\\u522A\\u9664\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RequirementManagementComponent_tr_70_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\", 33)(1, \"td\", 34);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\", 34);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\", 34);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"td\", 35);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"td\", 35);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"td\", 35);\n    i0.ɵɵtext(12);\n    i0.ɵɵpipe(13, \"getStatusName\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"td\", 35);\n    i0.ɵɵtext(15);\n    i0.ɵɵpipe(16, \"ngxNumberWithCommas\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"td\", 34);\n    i0.ɵɵtemplate(18, RequirementManagementComponent_tr_70_button_18_Template, 3, 0, \"button\", 36)(19, RequirementManagementComponent_tr_70_button_19_Template, 3, 0, \"button\", 37);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const data_r8 = ctx.$implicit;\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(data_r8.CBuildCaseName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(data_r8.CRequirement);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(data_r8.CGroupName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r4.getHouseType(data_r8.CHouseType || i0.ɵɵpureFunction0(13, _c0)));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(data_r8.CSort);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(13, 9, data_r8.CStatus));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(16, 11, data_r8.CUnitPrice || 0));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.isUpdate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.isDelete);\n  }\n}\nfunction RequirementManagementComponent_ng_template_72_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"\\u65B0\\u589E\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RequirementManagementComponent_ng_template_72_span_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"\\u7DE8\\u8F2F\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RequirementManagementComponent_ng_template_72_nb_option_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 58);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const b_r11 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", b_r11.cID);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", b_r11.CBuildCaseName, \"\");\n  }\n}\nfunction RequirementManagementComponent_ng_template_72_nb_option_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 58);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const type_r12 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", type_r12.value);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", type_r12.label, \"\");\n  }\n}\nfunction RequirementManagementComponent_ng_template_72_nb_option_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 58);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const status_r13 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", status_r13.value);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", status_r13.label, \"\");\n  }\n}\nfunction RequirementManagementComponent_ng_template_72_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-card\", 42)(1, \"nb-card-header\");\n    i0.ɵɵtemplate(2, RequirementManagementComponent_ng_template_72_span_2_Template, 2, 0, \"span\", 43)(3, RequirementManagementComponent_ng_template_72_span_3_Template, 2, 0, \"span\", 43);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"nb-card-body\", 44)(5, \"div\", 4)(6, \"div\", 45)(7, \"div\", 4)(8, \"app-form-group\", 46)(9, \"nb-select\", 47);\n    i0.ɵɵtwoWayListener(\"selectedChange\", function RequirementManagementComponent_ng_template_72_Template_nb_select_selectedChange_9_listener($event) {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r4 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r4.saveRequirement.CBuildCaseID, $event) || (ctx_r4.saveRequirement.CBuildCaseID = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtemplate(10, RequirementManagementComponent_ng_template_72_nb_option_10_Template, 2, 2, \"nb-option\", 48);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"app-form-group\", 46)(12, \"input\", 49);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_ng_template_72_Template_input_ngModelChange_12_listener($event) {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r4 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r4.saveRequirement.CRequirement, $event) || (ctx_r4.saveRequirement.CRequirement = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"app-form-group\", 46)(14, \"input\", 50);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_ng_template_72_Template_input_ngModelChange_14_listener($event) {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r4 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r4.saveRequirement.CGroupName, $event) || (ctx_r4.saveRequirement.CGroupName = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(15, \"app-form-group\", 46)(16, \"input\", 51);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_ng_template_72_Template_input_ngModelChange_16_listener($event) {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r4 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r4.saveRequirement.CSort, $event) || (ctx_r4.saveRequirement.CSort = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(17, \"app-form-group\", 46)(18, \"nb-select\", 52);\n    i0.ɵɵtwoWayListener(\"selectedChange\", function RequirementManagementComponent_ng_template_72_Template_nb_select_selectedChange_18_listener($event) {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r4 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r4.saveRequirement.CHouseType, $event) || (ctx_r4.saveRequirement.CHouseType = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtemplate(19, RequirementManagementComponent_ng_template_72_nb_option_19_Template, 2, 2, \"nb-option\", 48);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(20, \"app-form-group\", 46)(21, \"nb-select\", 53);\n    i0.ɵɵtwoWayListener(\"selectedChange\", function RequirementManagementComponent_ng_template_72_Template_nb_select_selectedChange_21_listener($event) {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r4 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r4.saveRequirement.CStatus, $event) || (ctx_r4.saveRequirement.CStatus = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtemplate(22, RequirementManagementComponent_ng_template_72_nb_option_22_Template, 2, 2, \"nb-option\", 48);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(23, \"app-form-group\", 46)(24, \"input\", 54);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_ng_template_72_Template_input_ngModelChange_24_listener($event) {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r4 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r4.saveRequirement.CUnitPrice, $event) || (ctx_r4.saveRequirement.CUnitPrice = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(25, \"app-form-group\", 46)(26, \"textarea\", 55);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_ng_template_72_Template_textarea_ngModelChange_26_listener($event) {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r4 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r4.saveRequirement.CRemark, $event) || (ctx_r4.saveRequirement.CRemark = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()()()()()();\n    i0.ɵɵelementStart(27, \"nb-card-footer\")(28, \"div\", 4)(29, \"div\", 56)(30, \"button\", 31);\n    i0.ɵɵlistener(\"click\", function RequirementManagementComponent_ng_template_72_Template_button_click_30_listener() {\n      const ref_r14 = i0.ɵɵrestoreView(_r10).dialogRef;\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.save(ref_r14));\n    });\n    i0.ɵɵtext(31, \"\\u78BA\\u5B9A\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(32, \"button\", 57);\n    i0.ɵɵlistener(\"click\", function RequirementManagementComponent_ng_template_72_Template_button_click_32_listener() {\n      const ref_r14 = i0.ɵɵrestoreView(_r10).dialogRef;\n      return i0.ɵɵresetView(ref_r14.close());\n    });\n    i0.ɵɵtext(33, \"\\u53D6\\u6D88\");\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.isNew === true);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.isNew === false);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"label\", \"\\u5EFA\\u6848\\u540D\\u7A31\")(\"labelFor\", \"CBuildCaseID\")(\"isRequired\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"selected\", ctx_r4.saveRequirement.CBuildCaseID);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r4.buildCaseList);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"label\", \"\\u9700\\u6C42\")(\"labelFor\", \"CRequirement\")(\"isRequired\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r4.saveRequirement.CRequirement);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"label\", \"\\u7FA4\\u7D44\\u540D\\u7A31\")(\"labelFor\", \"CGroupName\")(\"isRequired\", false);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r4.saveRequirement.CGroupName);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"label\", \"\\u6392\\u5E8F\")(\"labelFor\", \"CSort\")(\"isRequired\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r4.saveRequirement.CSort);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"label\", \"\\u985E\\u578B\")(\"labelFor\", \"CHouseType\")(\"isRequired\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"selected\", ctx_r4.saveRequirement.CHouseType);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r4.houseType);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"label\", \"\\u72C0\\u614B\")(\"labelFor\", \"CStatus\")(\"isRequired\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"selected\", ctx_r4.saveRequirement.CStatus);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r4.statusOptions);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"label\", \"\\u55AE\\u50F9\")(\"labelFor\", \"CUnitPrice\")(\"isRequired\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r4.saveRequirement.CUnitPrice);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"label\", \"\\u5099\\u8A3B\\u8AAA\\u660E\")(\"labelFor\", \"CRemark\")(\"isRequired\", false);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r4.saveRequirement.CRemark);\n  }\n}\nexport class RequirementManagementComponent extends BaseComponent {\n  constructor(_allow, enumHelper, dialogService, message, valid, buildCaseService, requirementService, pettern, router, destroyref) {\n    super(_allow);\n    this._allow = _allow;\n    this.enumHelper = enumHelper;\n    this.dialogService = dialogService;\n    this.message = message;\n    this.valid = valid;\n    this.buildCaseService = buildCaseService;\n    this.requirementService = requirementService;\n    this.pettern = pettern;\n    this.router = router;\n    this.destroyref = destroyref;\n    // request\n    this.getListRequirementRequest = {};\n    this.getRequirementRequest = {};\n    // response\n    this.buildCaseList = [];\n    this.requirementList = [];\n    this.saveRequirement = {\n      CHouseType: []\n    };\n    // 搜尋欄位\n    this.searchRequirement = '';\n    this.searchGroupName = '';\n    this.searchHouseType = -1;\n    this.statusOptions = [{\n      value: 0,\n      label: '停用'\n    }, {\n      value: 1,\n      label: '啟用'\n    }];\n    this.houseType = this.enumHelper.getEnumOptions(EnumHouseType);\n    this.isNew = false;\n    this.currentBuildCase = 0;\n    this.getBuildCaseList();\n    this.getListRequirementRequest.CBuildCaseID = -1;\n    this.getListRequirementRequest.CStatus = -1;\n    this.getList();\n  }\n  ngOnInit() {}\n  getHouseType(hTypes) {\n    if (!hTypes) {\n      return '';\n    }\n    if (!Array.isArray(hTypes)) {\n      hTypes = [hTypes];\n    }\n    let labels = [];\n    hTypes.forEach(htype => {\n      let findH = this.houseType.find(x => x.value == htype);\n      if (findH) {\n        labels.push(findH.label);\n      }\n    });\n    return labels.join(', ');\n  }\n  validation() {\n    this.valid.clear();\n    this.valid.required('[建案名稱]', this.saveRequirement.CBuildCaseID);\n    this.valid.required('[需求]', this.saveRequirement.CRequirement);\n    this.valid.required('[地主戶]', this.saveRequirement.CHouseType);\n    this.valid.required('[安排]', this.saveRequirement.CSort);\n    this.valid.required('[狀態]', this.saveRequirement.CStatus);\n    this.valid.required('[單價]', this.saveRequirement.CUnitPrice);\n    // 群組名稱長度驗證\n    if (this.saveRequirement.CGroupName && this.saveRequirement.CGroupName.length > 20) {\n      this.valid.errorMessages.push('[群組名稱] 不能超過20個字');\n    }\n    // 備註說明長度驗證\n    if (this.saveRequirement.CRemark && this.saveRequirement.CRemark.length > 100) {\n      this.valid.errorMessages.push('[備註說明] 不能超過100個字');\n    }\n  }\n  add(dialog) {\n    this.isNew = true;\n    this.saveRequirement = {\n      CHouseType: []\n    };\n    this.saveRequirement.CStatus = 1;\n    this.saveRequirement.CUnitPrice = 0;\n    if (this.currentBuildCase != 0) {\n      this.saveRequirement.CBuildCaseID = this.currentBuildCase;\n    }\n    this.getBuildCaseList();\n    this.dialogService.open(dialog);\n  }\n  onEdit(data, dialog) {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      _this.getRequirementRequest.CRequirementID = data.CRequirementID;\n      _this.isNew = false;\n      try {\n        yield _this.getData();\n        _this.dialogService.open(dialog);\n      } catch (error) {\n        console.log(\"Failed to get function data\", error);\n      }\n    })();\n  }\n  save(ref) {\n    this.validation();\n    if (this.valid.errorMessages.length > 0) {\n      this.message.showErrorMSGs(this.valid.errorMessages);\n      return;\n    }\n    this.requirementService.apiRequirementSaveDataPost$Json({\n      body: this.saveRequirement\n    }).subscribe(res => {\n      if (res.StatusCode === 0) {\n        this.message.showSucessMSG('執行成功');\n        this.getList();\n      } else {\n        this.message.showErrorMSG(res.Message);\n      }\n    });\n    ref.close();\n  }\n  onDelete(data) {\n    this.saveRequirement.CRequirementID = data.CRequirementID;\n    this.isNew = false;\n    if (window.confirm('是否確定刪除?')) {\n      this.remove();\n    } else {\n      return;\n    }\n  }\n  remove() {\n    this.requirementService.apiRequirementDeleteDataPost$Json({\n      body: {\n        CRequirementID: this.saveRequirement.CRequirementID\n      }\n    }).subscribe(res => {\n      this.message.showSucessMSG('執行成功');\n      this.getList();\n    });\n  }\n  getBuildCaseList() {\n    this.buildCaseService.apiBuildCaseGetUserBuildCasePost$Json({\n      body: {}\n    }).pipe(takeUntilDestroyed(this.destroyref)).subscribe(res => {\n      this.buildCaseList = res.Entries;\n    });\n  }\n  getList() {\n    this.getListRequirementRequest.PageSize = this.pageSize;\n    this.getListRequirementRequest.PageIndex = this.pageIndex;\n    if (this.getListRequirementRequest.CBuildCaseID && this.getListRequirementRequest.CBuildCaseID != 0) {\n      this.currentBuildCase = this.getListRequirementRequest.CBuildCaseID;\n    }\n    // 設定 API 支援的搜尋參數\n    this.getListRequirementRequest.CRequirement = this.searchRequirement || undefined;\n    this.requirementService.apiRequirementGetListPost$Json({\n      body: this.getListRequirementRequest\n    }).pipe().subscribe(res => {\n      if (res.StatusCode == 0) {\n        if (res.Entries) {\n          let filteredData = res.Entries;\n          // 客戶端過濾群組名稱\n          if (this.searchGroupName && this.searchGroupName.trim()) {\n            filteredData = filteredData.filter(item => item.CGroupName && item.CGroupName.toLowerCase().includes(this.searchGroupName.toLowerCase()));\n          }\n          // 客戶端過濾類型\n          if (this.searchHouseType && this.searchHouseType !== -1) {\n            filteredData = filteredData.filter(item => {\n              if (!item.CHouseType) return false;\n              const houseTypes = Array.isArray(item.CHouseType) ? item.CHouseType : [item.CHouseType];\n              return houseTypes.includes(this.searchHouseType);\n            });\n          }\n          this.requirementList = filteredData;\n          this.totalRecords = filteredData.length;\n        }\n      }\n    });\n  }\n  getData() {\n    this.requirementService.apiRequirementGetDataPost$Json({\n      body: this.getRequirementRequest\n    }).pipe().subscribe(res => {\n      if (res.StatusCode == 0) {\n        if (res.Entries) {\n          this.saveRequirement = {\n            CHouseType: []\n          };\n          this.saveRequirement.CBuildCaseID = res.Entries.CBuildCaseID;\n          this.saveRequirement.CGroupName = res.Entries.CGroupName;\n          this.saveRequirement.CHouseType = res.Entries.CHouseType ? Array.isArray(res.Entries.CHouseType) ? res.Entries.CHouseType : [res.Entries.CHouseType] : [];\n          this.saveRequirement.CRemark = res.Entries.CRemark;\n          this.saveRequirement.CRequirement = res.Entries.CRequirement;\n          this.saveRequirement.CRequirementID = res.Entries.CRequirementID;\n          this.saveRequirement.CSort = res.Entries.CSort;\n          this.saveRequirement.CStatus = res.Entries.CStatus;\n          this.saveRequirement.CUnitPrice = res.Entries.CUnitPrice;\n        }\n      }\n    });\n  }\n  onHouseTypeChange(value, checked) {\n    console.log(checked);\n    if (checked) {\n      if (!this.saveRequirement.CHouseType?.includes(value)) {\n        this.saveRequirement.CHouseType?.push(value);\n      }\n      console.log(this.saveRequirement.CHouseType);\n    } else {\n      this.saveRequirement.CHouseType = this.saveRequirement.CHouseType?.filter(v => v !== value);\n    }\n  }\n  static {\n    this.ɵfac = function RequirementManagementComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || RequirementManagementComponent)(i0.ɵɵdirectiveInject(i1.AllowHelper), i0.ɵɵdirectiveInject(i2.EnumHelper), i0.ɵɵdirectiveInject(i3.NbDialogService), i0.ɵɵdirectiveInject(i4.MessageService), i0.ɵɵdirectiveInject(i5.ValidationHelper), i0.ɵɵdirectiveInject(i6.BuildCaseService), i0.ɵɵdirectiveInject(i6.RequirementService), i0.ɵɵdirectiveInject(i7.PetternHelper), i0.ɵɵdirectiveInject(i8.Router), i0.ɵɵdirectiveInject(i0.DestroyRef));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: RequirementManagementComponent,\n      selectors: [[\"app-requirement-management\"]],\n      standalone: true,\n      features: [i0.ɵɵInheritDefinitionFeature, i0.ɵɵStandaloneFeature],\n      decls: 74,\n      vars: 16,\n      consts: [[\"dialog\", \"\"], [\"accent\", \"success\"], [1, \"bg-white\"], [1, \"col-12\"], [1, \"row\"], [1, \"form-group\", \"col-12\", \"col-md-4\"], [\"for\", \"buildCase\", 1, \"label\", \"mr-2\"], [1, \"col-9\", 3, \"ngModelChange\", \"ngModel\"], [3, \"value\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"for\", \"requirement\", 1, \"label\", \"mr-2\"], [\"type\", \"text\", \"nbInput\", \"\", \"id\", \"requirement\", \"name\", \"requirement\", \"placeholder\", \"\\u9700\\u6C42\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"groupName\", 1, \"label\", \"mr-2\"], [\"type\", \"text\", \"nbInput\", \"\", \"id\", \"groupName\", \"name\", \"groupName\", \"placeholder\", \"\\u7FA4\\u7D44\\u540D\\u7A31\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"houseType\", 1, \"label\", \"mr-2\"], [\"multiple\", \"\", 1, \"col-9\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"status\", 1, \"label\", \"mr-2\"], [1, \"col-md-6\"], [1, \"form-group\", \"col-12\", \"col-md-6\", \"text-right\"], [1, \"btn\", \"btn-secondary\", \"mr-2\", 3, \"click\"], [1, \"fas\", \"fa-undo\", \"mr-1\"], [1, \"btn\", \"btn-info\", \"mr-2\", 3, \"click\"], [1, \"fas\", \"fa-search\", \"mr-1\"], [\"class\", \"btn btn-success mr-2\", 3, \"click\", 4, \"ngIf\"], [1, \"table-responsive\"], [1, \"table\", \"table-striped\", \"border\", 2, \"min-width\", \"800px\", \"background-color\", \"#f3f3f3\"], [1, \"d-flex\", \"text-white\", 2, \"background-color\", \"#27ae60\"], [\"scope\", \"col\", 1, \"col-2\"], [\"scope\", \"col\", 1, \"col-1\"], [\"class\", \"d-flex\", 4, \"ngFor\", \"ngForOf\"], [3, \"PageChange\", \"CollectionSize\", \"Page\", \"PageSize\"], [1, \"btn\", \"btn-success\", \"mr-2\", 3, \"click\"], [1, \"fas\", \"fa-plus\", \"mr-1\"], [1, \"d-flex\"], [1, \"col-2\"], [1, \"col-1\"], [\"type\", \"button\", \"class\", \"btn btn-outline-success m-1  btn-sm\", 3, \"click\", 4, \"ngIf\"], [\"type\", \"button\", \"class\", \"btn btn-outline-danger m-1  btn-sm\", 3, \"click\", 4, \"ngIf\"], [\"type\", \"button\", 1, \"btn\", \"btn-outline-success\", \"m-1\", \"btn-sm\", 3, \"click\"], [1, \"fas\", \"fa-edit\", \"mr-1\"], [\"type\", \"button\", 1, \"btn\", \"btn-outline-danger\", \"m-1\", \"btn-sm\", 3, \"click\"], [1, \"far\", \"fa-trash-alt\", \"mr-1\"], [1, \"mr-md-5\", \"ml-md-5\", 2, \"height\", \"100%\", \"overflow\", \"auto\", \"max-width\", \"500px\"], [4, \"ngIf\"], [2, \"padding\", \"1rem 2rem\"], [1, \"col-12\", \"col-md-12\"], [3, \"label\", \"labelFor\", \"isRequired\"], [\"placeholder\", \"\\u8ACB\\u9078\\u64C7\", \"id\", \"CBuildCaseID\", \"name\", \"CBuildCaseID\", 1, \"flex-grow-1\", 3, \"selectedChange\", \"selected\"], [\"langg\", \"\", 3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"type\", \"text\", \"nbInput\", \"\", \"id\", \"CRequirement\", \"name\", \"CRequirement\", \"placeholder\", \"\\u9700\\u6C42\", 1, \"flex-grow-1\", 3, \"ngModelChange\", \"ngModel\"], [\"type\", \"text\", \"nbInput\", \"\", \"id\", \"CGroupName\", \"name\", \"CGroupName\", \"placeholder\", \"\\u7FA4\\u7D44\\u540D\\u7A31\", \"maxlength\", \"20\", 1, \"flex-grow-1\", 3, \"ngModelChange\", \"ngModel\"], [\"type\", \"number\", \"nbInput\", \"\", \"id\", \"CRequirement\", \"name\", \"CRequirement\", \"placeholder\", \"\\u6392\\u5E8F\", 1, \"flex-grow-1\", 3, \"ngModelChange\", \"ngModel\"], [\"placeholder\", \"\\u8ACB\\u9078\\u64C7\", \"id\", \"CHouseType\", \"name\", \"CHouseType\", \"multiple\", \"\", 1, \"flex-grow-1\", 3, \"selectedChange\", \"selected\"], [\"placeholder\", \"\\u8ACB\\u9078\\u64C7\", \"id\", \"CStatus\", \"name\", \"CStatus\", 1, \"flex-grow-1\", 3, \"selectedChange\", \"selected\"], [\"type\", \"number\", \"nbInput\", \"\", \"id\", \"CUnitPrice\", \"name\", \"CUnitPrice\", \"placeholder\", \"\\u55AE\\u50F9\", 1, \"flex-grow-1\", 3, \"ngModelChange\", \"ngModel\"], [\"nbInput\", \"\", \"id\", \"CRemark\", \"name\", \"CRemark\", \"placeholder\", \"\\u5099\\u8A3B\\u8AAA\\u660E\", \"maxlength\", \"100\", \"rows\", \"3\", 1, \"flex-grow-1\", 3, \"ngModelChange\", \"ngModel\"], [1, \"col-12\", \"text-center\"], [1, \"btn\", \"btn-danger\", \"mr-2\", 3, \"click\"], [\"langg\", \"\", 3, \"value\"]],\n      template: function RequirementManagementComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"nb-card\", 1)(1, \"nb-card-header\");\n          i0.ɵɵelement(2, \"ngx-breadcrumb\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(3, \"nb-card-body\", 2)(4, \"div\", 3)(5, \"div\", 4)(6, \"div\", 5)(7, \"label\", 6);\n          i0.ɵɵtext(8, \"\\u5EFA\\u6848\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(9, \"nb-select\", 7);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_Template_nb_select_ngModelChange_9_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.getListRequirementRequest.CBuildCaseID, $event) || (ctx.getListRequirementRequest.CBuildCaseID = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵelementStart(10, \"nb-option\", 8);\n          i0.ɵɵtext(11, \"\\u5168\\u90E8\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(12, RequirementManagementComponent_nb_option_12_Template, 2, 2, \"nb-option\", 9);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(13, \"div\", 5)(14, \"label\", 10);\n          i0.ɵɵtext(15, \"\\u9700\\u6C42\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(16, \"input\", 11);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_Template_input_ngModelChange_16_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.getListRequirementRequest.CRequirement, $event) || (ctx.getListRequirementRequest.CRequirement = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(17, \"div\", 5)(18, \"label\", 12);\n          i0.ɵɵtext(19, \"\\u7FA4\\u7D44\\u540D\\u7A31\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(20, \"input\", 13);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_Template_input_ngModelChange_20_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.getListRequirementRequest.CGroupName, $event) || (ctx.getListRequirementRequest.CGroupName = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(21, \"div\", 4)(22, \"div\", 5)(23, \"label\", 14);\n          i0.ɵɵtext(24, \"\\u985E\\u578B\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(25, \"nb-select\", 15);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_Template_nb_select_ngModelChange_25_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.getListRequirementRequest.CHouseType, $event) || (ctx.getListRequirementRequest.CHouseType = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵtemplate(26, RequirementManagementComponent_nb_option_26_Template, 2, 2, \"nb-option\", 9);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(27, \"div\", 5)(28, \"label\", 16);\n          i0.ɵɵtext(29, \"\\u72C0\\u614B\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(30, \"nb-select\", 7);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_Template_nb_select_ngModelChange_30_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.getListRequirementRequest.CStatus, $event) || (ctx.getListRequirementRequest.CStatus = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵelementStart(31, \"nb-option\", 8);\n          i0.ɵɵtext(32, \"\\u5168\\u90E8\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(33, \"nb-option\", 8);\n          i0.ɵɵtext(34, \"\\u555F\\u7528\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(35, \"nb-option\", 8);\n          i0.ɵɵtext(36, \"\\u505C\\u7528\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(37, \"div\", 4);\n          i0.ɵɵelement(38, \"div\", 17);\n          i0.ɵɵelementStart(39, \"div\", 18)(40, \"button\", 19);\n          i0.ɵɵlistener(\"click\", function RequirementManagementComponent_Template_button_click_40_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.resetSearch());\n          });\n          i0.ɵɵelement(41, \"i\", 20);\n          i0.ɵɵtext(42, \"\\u91CD\\u7F6E\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(43, \"button\", 21);\n          i0.ɵɵlistener(\"click\", function RequirementManagementComponent_Template_button_click_43_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.getList());\n          });\n          i0.ɵɵelement(44, \"i\", 22);\n          i0.ɵɵtext(45, \"\\u67E5\\u8A62\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(46, RequirementManagementComponent_button_46_Template, 3, 0, \"button\", 23);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(47, \"nb-card-body\", 2)(48, \"div\", 3)(49, \"div\", 24)(50, \"table\", 25)(51, \"thead\")(52, \"tr\", 26)(53, \"th\", 27);\n          i0.ɵɵtext(54, \"\\u5EFA\\u6848\\u540D\\u7A31\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(55, \"th\", 27);\n          i0.ɵɵtext(56, \"\\u9700\\u6C42\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(57, \"th\", 27);\n          i0.ɵɵtext(58, \"\\u7FA4\\u7D44\\u540D\\u7A31\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(59, \"th\", 28);\n          i0.ɵɵtext(60, \"\\u985E\\u578B\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(61, \"th\", 28);\n          i0.ɵɵtext(62, \"\\u6392\\u5E8F\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(63, \"th\", 28);\n          i0.ɵɵtext(64, \"\\u72C0\\u614B\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(65, \"th\", 28);\n          i0.ɵɵtext(66, \"\\u55AE\\u50F9\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(67, \"th\", 27);\n          i0.ɵɵtext(68, \"\\u64CD\\u4F5C\\u529F\\u80FD\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(69, \"tbody\");\n          i0.ɵɵtemplate(70, RequirementManagementComponent_tr_70_Template, 20, 14, \"tr\", 29);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(71, \"ngx-pagination\", 30);\n          i0.ɵɵtwoWayListener(\"PageChange\", function RequirementManagementComponent_Template_ngx_pagination_PageChange_71_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.pageIndex, $event) || (ctx.pageIndex = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵlistener(\"PageChange\", function RequirementManagementComponent_Template_ngx_pagination_PageChange_71_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.getList());\n          });\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵtemplate(72, RequirementManagementComponent_ng_template_72_Template, 34, 37, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor);\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(9);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.getListRequirementRequest.CBuildCaseID);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"value\", -1);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngForOf\", ctx.buildCaseList);\n          i0.ɵɵadvance(4);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.getListRequirementRequest.CRequirement);\n          i0.ɵɵadvance(4);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.getListRequirementRequest.CGroupName);\n          i0.ɵɵadvance(5);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.getListRequirementRequest.CHouseType);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngForOf\", ctx.houseType);\n          i0.ɵɵadvance(4);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.getListRequirementRequest.CStatus);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"value\", -1);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"value\", 1);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"value\", 0);\n          i0.ɵɵadvance(11);\n          i0.ɵɵproperty(\"ngIf\", ctx.isCreate);\n          i0.ɵɵadvance(24);\n          i0.ɵɵproperty(\"ngForOf\", ctx.requirementList);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"CollectionSize\", ctx.totalRecords);\n          i0.ɵɵtwoWayProperty(\"Page\", ctx.pageIndex);\n          i0.ɵɵproperty(\"PageSize\", ctx.pageSize);\n        }\n      },\n      dependencies: [NbCardModule, i3.NbCardComponent, i3.NbCardBodyComponent, i3.NbCardFooterComponent, i3.NbCardHeaderComponent, BreadcrumbComponent, NbInputModule, i3.NbInputDirective, FormsModule, i9.DefaultValueAccessor, i9.NumberValueAccessor, i9.NgControlStatus, i9.MaxLengthValidator, i9.NgModel, NbSelectModule, i3.NbSelectComponent, i3.NbOptionComponent, NbOptionModule, NgIf, NgFor, PaginationComponent, StatusPipe, NbCheckboxModule, FormGroupComponent, NumberWithCommasPipe],\n      styles: [\"/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsImZpbGUiOiJyZXF1aXJlbWVudC1tYW5hZ2VtZW50LmNvbXBvbmVudC5zY3NzIn0= */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvcGFnZXMvcmVxdWlyZW1lbnQtbWFuYWdlbWVudC9yZXF1aXJlbWVudC1tYW5hZ2VtZW50LmNvbXBvbmVudC5zY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiI7QUFDQSx3TEFBd0wiLCJzb3VyY2VSb290IjoiIn0= */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["NbCardModule", "NbCheckboxModule", "NbInputModule", "NbOptionModule", "NbSelectModule", "BaseComponent", "takeUntilDestroyed", "BreadcrumbComponent", "FormsModule", "<PERSON><PERSON><PERSON>", "NgIf", "PaginationComponent", "StatusPipe", "FormGroupComponent", "NumberWithCommasPipe", "EnumHouseType", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵproperty", "case_r2", "cID", "ɵɵadvance", "ɵɵtextInterpolate1", "CBuildCaseName", "type_r3", "value", "label", "ɵɵlistener", "RequirementManagementComponent_button_46_Template_button_click_0_listener", "ɵɵrestoreView", "_r4", "ctx_r4", "ɵɵnextContext", "dialog_r6", "ɵɵreference", "ɵɵresetView", "add", "ɵɵelement", "RequirementManagementComponent_tr_70_button_18_Template_button_click_0_listener", "_r7", "data_r8", "$implicit", "onEdit", "RequirementManagementComponent_tr_70_button_19_Template_button_click_0_listener", "_r9", "onDelete", "ɵɵtemplate", "RequirementManagementComponent_tr_70_button_18_Template", "RequirementManagementComponent_tr_70_button_19_Template", "ɵɵtextInterpolate", "CRequirement", "CGroupName", "getHouseType", "CHouseType", "ɵɵpureFunction0", "_c0", "CSort", "ɵɵpipeBind1", "CStatus", "CUnitPrice", "isUpdate", "isDelete", "b_r11", "type_r12", "status_r13", "RequirementManagementComponent_ng_template_72_span_2_Template", "RequirementManagementComponent_ng_template_72_span_3_Template", "ɵɵtwoWayListener", "RequirementManagementComponent_ng_template_72_Template_nb_select_selectedChange_9_listener", "$event", "_r10", "ɵɵtwoWayBindingSet", "saveRequirement", "CBuildCaseID", "RequirementManagementComponent_ng_template_72_nb_option_10_Template", "RequirementManagementComponent_ng_template_72_Template_input_ngModelChange_12_listener", "RequirementManagementComponent_ng_template_72_Template_input_ngModelChange_14_listener", "RequirementManagementComponent_ng_template_72_Template_input_ngModelChange_16_listener", "RequirementManagementComponent_ng_template_72_Template_nb_select_selectedChange_18_listener", "RequirementManagementComponent_ng_template_72_nb_option_19_Template", "RequirementManagementComponent_ng_template_72_Template_nb_select_selectedChange_21_listener", "RequirementManagementComponent_ng_template_72_nb_option_22_Template", "RequirementManagementComponent_ng_template_72_Template_input_ngModelChange_24_listener", "RequirementManagementComponent_ng_template_72_Template_textarea_ngModelChange_26_listener", "CRemark", "RequirementManagementComponent_ng_template_72_Template_button_click_30_listener", "ref_r14", "dialogRef", "save", "RequirementManagementComponent_ng_template_72_Template_button_click_32_listener", "close", "isNew", "ɵɵtwoWayProperty", "buildCaseList", "houseType", "statusOptions", "RequirementManagementComponent", "constructor", "_allow", "enum<PERSON>elper", "dialogService", "message", "valid", "buildCaseService", "requirementService", "pettern", "router", "destroyref", "getListRequirementRequest", "getRequirementRequest", "requirementList", "searchRequirement", "searchGroupName", "searchHouseType", "getEnumOptions", "currentBuildCase", "getBuildCaseList", "getList", "ngOnInit", "hTypes", "Array", "isArray", "labels", "for<PERSON>ach", "htype", "findH", "find", "x", "push", "join", "validation", "clear", "required", "length", "errorMessages", "dialog", "open", "data", "_this", "_asyncToGenerator", "CRequirementID", "getData", "error", "console", "log", "ref", "showErrorMSGs", "apiRequirementSaveDataPost$Json", "body", "subscribe", "res", "StatusCode", "showSucessMSG", "showErrorMSG", "Message", "window", "confirm", "remove", "apiRequirementDeleteDataPost$Json", "apiBuildCaseGetUserBuildCasePost$Json", "pipe", "Entries", "PageSize", "pageSize", "PageIndex", "pageIndex", "undefined", "apiRequirementGetListPost$Json", "filteredData", "trim", "filter", "item", "toLowerCase", "includes", "houseTypes", "totalRecords", "apiRequirementGetDataPost$Json", "onHouseTypeChange", "checked", "v", "ɵɵdirectiveInject", "i1", "AllowHelper", "i2", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "i3", "NbDialogService", "i4", "MessageService", "i5", "ValidationHelper", "i6", "BuildCaseService", "RequirementService", "i7", "PetternHelper", "i8", "Router", "DestroyRef", "selectors", "standalone", "features", "ɵɵInheritDefinitionFeature", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "RequirementManagementComponent_Template", "rf", "ctx", "RequirementManagementComponent_Template_nb_select_ngModelChange_9_listener", "_r1", "RequirementManagementComponent_nb_option_12_Template", "RequirementManagementComponent_Template_input_ngModelChange_16_listener", "RequirementManagementComponent_Template_input_ngModelChange_20_listener", "RequirementManagementComponent_Template_nb_select_ngModelChange_25_listener", "RequirementManagementComponent_nb_option_26_Template", "RequirementManagementComponent_Template_nb_select_ngModelChange_30_listener", "RequirementManagementComponent_Template_button_click_40_listener", "resetSearch", "RequirementManagementComponent_Template_button_click_43_listener", "RequirementManagementComponent_button_46_Template", "RequirementManagementComponent_tr_70_Template", "RequirementManagementComponent_Template_ngx_pagination_PageChange_71_listener", "RequirementManagementComponent_ng_template_72_Template", "ɵɵtemplateRefExtractor", "isCreate", "NbCardComponent", "NbCardBodyComponent", "NbCardFooterComponent", "NbCardHeaderComponent", "NbInputDirective", "i9", "DefaultValueAccessor", "NumberValueAccessor", "NgControlStatus", "MaxLengthValidator", "NgModel", "NbSelectComponent", "NbOptionComponent", "styles"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\requirement-management\\requirement-management.component.ts", "C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\requirement-management\\requirement-management.component.html"], "sourcesContent": ["import { Component, DestroyRef, OnInit, TemplateRef } from '@angular/core';\r\nimport { Router } from '@angular/router';\r\nimport { NbCardModule, NbCheckboxModule, NbDialogService, NbInputModule, NbOptionModule, NbSelectModule } from '@nebular/theme';\r\nimport { MessageService } from 'src/app/shared/services/message.service';\r\nimport { AllowHelper } from 'src/app/shared/helper/allowHelper';\r\nimport { EnumHelper } from 'src/app/shared/helper/enumHelper';\r\nimport { PetternHelper } from 'src/app/shared/helper/petternHelper';\r\nimport { ValidationHelper } from 'src/app/shared/helper/validationHelper';\r\nimport { BuildCaseService, RequirementService } from 'src/services/api/services';\r\nimport { BaseComponent } from '../components/base/baseComponent';\r\nimport { takeUntilDestroyed } from '@angular/core/rxjs-interop';\r\nimport { BuildCaseGetListReponse, GetListRequirementRequest, GetRequirement, GetRequirementByIdRequest, SaveDataRequirement } from 'src/services/api/models';\r\nimport { BreadcrumbComponent } from '../components/breadcrumb/breadcrumb.component';\r\nimport { FormsModule } from '@angular/forms';\r\nimport { NgFor, NgIf } from '@angular/common';\r\nimport { PaginationComponent } from '../components/pagination/pagination.component';\r\nimport { StatusPipe } from 'src/app/@theme/pipes/mapping.pipe';\r\nimport { FormGroupComponent } from 'src/app/@theme/components/form-group/form-group.component';\r\nimport { NumberWithCommasPipe } from 'src/app/@theme/pipes/number-with-commas.pipe';\r\nimport { EnumHouseType } from 'src/app/shared/enum/enumHouseType';\r\n\r\n@Component({\r\n  selector: 'app-requirement-management',\r\n  standalone: true, imports: [\r\n    NbCardModule,\r\n    BreadcrumbComponent,\r\n    NbInputModule,\r\n    FormsModule,\r\n    NbSelectModule,\r\n    NbOptionModule,\r\n    NgIf,\r\n    NgFor,\r\n    PaginationComponent,\r\n    StatusPipe,\r\n    NbCheckboxModule,\r\n    FormGroupComponent,\r\n    NumberWithCommasPipe\r\n  ],\r\n  templateUrl: './requirement-management.component.html',\r\n  styleUrl: './requirement-management.component.scss'\r\n})\r\nexport class RequirementManagementComponent extends BaseComponent implements OnInit {\r\n  constructor(\r\n    private _allow: AllowHelper,\r\n    private enumHelper: EnumHelper,\r\n    private dialogService: NbDialogService,\r\n    private message: MessageService,\r\n    private valid: ValidationHelper,\r\n    private buildCaseService: BuildCaseService,\r\n    private requirementService: RequirementService,\r\n    private pettern: PetternHelper,\r\n    private router: Router,\r\n    private destroyref: DestroyRef\r\n  ) {\r\n    super(_allow);\r\n    this.getBuildCaseList();\r\n    this.getListRequirementRequest.CBuildCaseID = -1;\r\n    this.getListRequirementRequest.CStatus = -1;\r\n    this.getList();\r\n  }\r\n  // request\r\n  getListRequirementRequest = {} as GetListRequirementRequest;\r\n  getRequirementRequest: GetRequirementByIdRequest = {};\r\n\r\n  // response\r\n  buildCaseList: BuildCaseGetListReponse[] = [];\r\n  requirementList: GetRequirement[] = [];\r\n  saveRequirement: SaveDataRequirement = { CHouseType: [] };\r\n\r\n  // 搜尋欄位\r\n  searchRequirement: string = '';\r\n  searchGroupName: string = '';\r\n  searchHouseType: number = -1;\r\n\r\n  statusOptions = [\r\n    { value: 0, label: '停用' },\r\n    { value: 1, label: '啟用' },\r\n  ];\r\n  houseType = this.enumHelper.getEnumOptions(EnumHouseType);\r\n  isNew = false;\r\n  currentBuildCase = 0;\r\n\r\n  override ngOnInit(): void { }\r\n\r\n  getHouseType(hTypes: number[] | number | null | undefined): string {\r\n    if (!hTypes) {\r\n      return '';\r\n    }\r\n\r\n    if (!Array.isArray(hTypes)) {\r\n      hTypes = [hTypes];\r\n    }\r\n\r\n    let labels: string[] = [];\r\n    hTypes.forEach(htype => {\r\n      let findH = this.houseType.find(x => x.value == htype);\r\n      if (findH) {\r\n        labels.push(findH.label);\r\n      }\r\n    });\r\n    return labels.join(', ');\r\n  } validation() {\r\n    this.valid.clear();\r\n    this.valid.required('[建案名稱]', this.saveRequirement.CBuildCaseID);\r\n    this.valid.required('[需求]', this.saveRequirement.CRequirement);\r\n    this.valid.required('[地主戶]', this.saveRequirement.CHouseType);\r\n    this.valid.required('[安排]', this.saveRequirement.CSort);\r\n    this.valid.required('[狀態]', this.saveRequirement.CStatus);\r\n    this.valid.required('[單價]', this.saveRequirement.CUnitPrice);\r\n\r\n    // 群組名稱長度驗證\r\n    if (this.saveRequirement.CGroupName && this.saveRequirement.CGroupName.length > 20) {\r\n      this.valid.errorMessages.push('[群組名稱] 不能超過20個字');\r\n    }\r\n\r\n    // 備註說明長度驗證\r\n    if (this.saveRequirement.CRemark && this.saveRequirement.CRemark.length > 100) {\r\n      this.valid.errorMessages.push('[備註說明] 不能超過100個字');\r\n    }\r\n  }\r\n\r\n  add(dialog: TemplateRef<any>) {\r\n    this.isNew = true;\r\n    this.saveRequirement = { CHouseType: [] };\r\n    this.saveRequirement.CStatus = 1;\r\n    this.saveRequirement.CUnitPrice = 0;\r\n    if (this.currentBuildCase != 0) {\r\n      this.saveRequirement.CBuildCaseID = this.currentBuildCase;\r\n    }\r\n    this.getBuildCaseList();\r\n    this.dialogService.open(dialog);\r\n  }\r\n\r\n  async onEdit(data: GetRequirement, dialog: TemplateRef<any>) {\r\n    this.getRequirementRequest.CRequirementID = data.CRequirementID!;\r\n    this.isNew = false;\r\n    try {\r\n      await this.getData();\r\n      this.dialogService.open(dialog);\r\n    } catch (error) {\r\n      console.log(\"Failed to get function data\", error)\r\n    }\r\n  }\r\n\r\n  save(ref: any) {\r\n    this.validation();\r\n    if (this.valid.errorMessages.length > 0) {\r\n      this.message.showErrorMSGs(this.valid.errorMessages);\r\n      return;\r\n    }\r\n\r\n    this.requirementService.apiRequirementSaveDataPost$Json({\r\n      body: this.saveRequirement\r\n    }).subscribe(res => {\r\n      if (res.StatusCode === 0) {\r\n        this.message.showSucessMSG('執行成功');\r\n        this.getList();\r\n      } else {\r\n        this.message.showErrorMSG(res.Message!)\r\n      }\r\n    });\r\n    ref.close();\r\n  }\r\n\r\n  onDelete(data: GetRequirement) {\r\n    this.saveRequirement.CRequirementID = data.CRequirementID!;\r\n    this.isNew = false;\r\n    if (window.confirm('是否確定刪除?')) {\r\n      this.remove();\r\n    } else {\r\n      return;\r\n    }\r\n  }\r\n\r\n  remove() {\r\n    this.requirementService.apiRequirementDeleteDataPost$Json({\r\n      body: {\r\n        CRequirementID: this.saveRequirement.CRequirementID!\r\n      }\r\n    }).subscribe(res => {\r\n      this.message.showSucessMSG('執行成功');\r\n      this.getList();\r\n    });\r\n  }\r\n\r\n  getBuildCaseList() {\r\n    this.buildCaseService.apiBuildCaseGetUserBuildCasePost$Json({ body: {} })\r\n      .pipe(takeUntilDestroyed(this.destroyref)).subscribe(res => {\r\n        this.buildCaseList = res.Entries!;\r\n      })\r\n  }\r\n  getList() {\r\n    this.getListRequirementRequest.PageSize = this.pageSize;\r\n    this.getListRequirementRequest.PageIndex = this.pageIndex;\r\n    if (this.getListRequirementRequest.CBuildCaseID && this.getListRequirementRequest.CBuildCaseID != 0) {\r\n      this.currentBuildCase = this.getListRequirementRequest.CBuildCaseID;\r\n    }\r\n\r\n    // 設定 API 支援的搜尋參數\r\n    this.getListRequirementRequest.CRequirement = this.searchRequirement || undefined;\r\n\r\n    this.requirementService.apiRequirementGetListPost$Json({ body: this.getListRequirementRequest })\r\n      .pipe()\r\n      .subscribe(res => {\r\n        if (res.StatusCode == 0) {\r\n          if (res.Entries) {\r\n            let filteredData = res.Entries;\r\n\r\n            // 客戶端過濾群組名稱\r\n            if (this.searchGroupName && this.searchGroupName.trim()) {\r\n              filteredData = filteredData.filter(item =>\r\n                item.CGroupName && item.CGroupName.toLowerCase().includes(this.searchGroupName.toLowerCase())\r\n              );\r\n            }\r\n\r\n            // 客戶端過濾類型\r\n            if (this.searchHouseType && this.searchHouseType !== -1) {\r\n              filteredData = filteredData.filter(item => {\r\n                if (!item.CHouseType) return false;\r\n                const houseTypes = Array.isArray(item.CHouseType) ? item.CHouseType : [item.CHouseType];\r\n                return houseTypes.includes(this.searchHouseType);\r\n              });\r\n            }\r\n\r\n            this.requirementList = filteredData;\r\n            this.totalRecords = filteredData.length;\r\n          }\r\n        }\r\n      })\r\n  } getData() {\r\n    this.requirementService.apiRequirementGetDataPost$Json({ body: this.getRequirementRequest })\r\n      .pipe()\r\n      .subscribe(res => {\r\n        if (res.StatusCode == 0) {\r\n          if (res.Entries) {\r\n            this.saveRequirement = { CHouseType: [] };\r\n            this.saveRequirement.CBuildCaseID = res.Entries.CBuildCaseID;\r\n            this.saveRequirement.CGroupName = res.Entries.CGroupName;\r\n            this.saveRequirement.CHouseType = res.Entries.CHouseType ? (Array.isArray(res.Entries.CHouseType) ? res.Entries.CHouseType : [res.Entries.CHouseType]) : [];\r\n            this.saveRequirement.CRemark = res.Entries.CRemark;\r\n            this.saveRequirement.CRequirement = res.Entries.CRequirement;\r\n            this.saveRequirement.CRequirementID = res.Entries.CRequirementID;\r\n            this.saveRequirement.CSort = res.Entries.CSort;\r\n            this.saveRequirement.CStatus = res.Entries.CStatus;\r\n            this.saveRequirement.CUnitPrice = res.Entries.CUnitPrice;\r\n          }\r\n        }\r\n      })\r\n  }\r\n\r\n  onHouseTypeChange(value: number, checked: any) {\r\n    console.log(checked);\r\n\r\n    if (checked) {\r\n      if (!this.saveRequirement.CHouseType?.includes(value)) {\r\n        this.saveRequirement.CHouseType?.push(value);\r\n      }\r\n      console.log(this.saveRequirement.CHouseType);\r\n    } else {\r\n      this.saveRequirement.CHouseType = this.saveRequirement.CHouseType?.filter(v => v !== value);\r\n    }\r\n  }\r\n}\r\n", "<nb-card accent=\"success\">\r\n  <nb-card-header>\r\n    <ngx-breadcrumb></ngx-breadcrumb>\r\n  </nb-card-header>\r\n  <nb-card-body class=\"bg-white\">\r\n    <div class=\"col-12\">\r\n      <div class=\"row\">\r\n        <div class=\"form-group col-12 col-md-4\">\r\n          <label for=\"buildCase\" class=\"label mr-2\">建案</label>\r\n          <nb-select [(ngModel)]=\"getListRequirementRequest.CBuildCaseID\" class=\"col-9\">\r\n            <nb-option [value]=\"-1\">全部</nb-option>\r\n            <nb-option *ngFor=\"let case of buildCaseList\" [value]=\"case.cID\">\r\n              {{ case.CBuildCaseName }}\r\n            </nb-option>\r\n          </nb-select>\r\n        </div>\r\n        <div class=\"form-group col-12 col-md-4\">\r\n          <label for=\"requirement\" class=\"label mr-2\">需求</label>\r\n          <input type=\"text\" nbInput id=\"requirement\" name=\"requirement\" placeholder=\"需求\"\r\n            [(ngModel)]=\"getListRequirementRequest.CRequirement\">\r\n        </div>        <div class=\"form-group col-12 col-md-4\">\r\n          <label for=\"groupName\" class=\"label mr-2\">群組名稱</label>\r\n          <input type=\"text\" nbInput id=\"groupName\" name=\"groupName\" placeholder=\"群組名稱\"\r\n            [(ngModel)]=\"getListRequirementRequest.CGroupName\">\r\n        </div>\r\n      </div>\r\n      <div class=\"row\">\r\n        <div class=\"form-group col-12 col-md-4\">\r\n          <label for=\"houseType\" class=\"label mr-2\">類型</label>\r\n          <nb-select [(ngModel)]=\"getListRequirementRequest.CHouseType\" class=\"col-9\" multiple>\r\n            <nb-option *ngFor=\"let type of houseType\" [value]=\"type.value\">\r\n              {{ type.label }}\r\n            </nb-option>\r\n          </nb-select>\r\n        </div>\r\n        <div class=\"form-group col-12 col-md-4\">\r\n          <label for=\"status\" class=\"label mr-2\">狀態</label>\r\n          <nb-select [(ngModel)]=\"getListRequirementRequest.CStatus\" class=\"col-9\">\r\n            <nb-option [value]=\"-1\">全部</nb-option>\r\n            <nb-option [value]=\"1\">啟用</nb-option>\r\n            <nb-option [value]=\"0\">停用</nb-option>\r\n          </nb-select>\r\n        </div>\r\n      </div>      <div class=\"row\">\r\n        <div class=\"col-md-6\"></div>\r\n        <div class=\"form-group col-12 col-md-6 text-right\">\r\n          <button class=\"btn btn-secondary mr-2\" (click)=\"resetSearch()\"><i class=\"fas fa-undo mr-1\"></i>重置</button>\r\n          <button class=\"btn btn-info mr-2\" (click)=\"getList()\"><i class=\"fas fa-search mr-1\"></i>查詢</button>\r\n          <button class=\"btn btn-success mr-2\" (click)=\"add(dialog)\" *ngIf=\"isCreate\"><i\r\n              class=\"fas fa-plus mr-1\"></i>新增</button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </nb-card-body>\r\n\r\n  <nb-card-body class=\"bg-white\">\r\n    <div class=\"col-12\">\r\n      <div class=\"table-responsive\">\r\n        <table class=\"table table-striped border \" style=\"min-width: 800px; background-color:#f3f3f3;\">\r\n          <thead>\r\n            <tr style=\"background-color: #27ae60;\" class=\"d-flex text-white\">\r\n              <th scope=\"col\" class=\"col-2\">建案名稱</th>\r\n              <th scope=\"col\" class=\"col-2\">需求</th>\r\n              <th scope=\"col\" class=\"col-2\">群組名稱</th>\r\n              <th scope=\"col\" class=\"col-1\">類型</th>\r\n              <th scope=\"col\" class=\"col-1\">排序</th>\r\n              <th scope=\"col\" class=\"col-1\">狀態</th>\r\n              <th scope=\"col\" class=\"col-1\">單價</th>\r\n              <th scope=\"col\" class=\"col-2\">操作功能</th>\r\n            </tr>\r\n          </thead>\r\n          <tbody>\r\n            <tr *ngFor=\"let data of requirementList; let i = index\" class=\"d-flex\">\r\n              <td class=\"col-2\">{{ data.CBuildCaseName }}</td>\r\n              <td class=\"col-2\">{{ data.CRequirement }}</td>\r\n              <td class=\"col-2\">{{ data.CGroupName }}</td>\r\n              <td class=\"col-1\">{{ getHouseType(data.CHouseType || []) }}</td>\r\n              <td class=\"col-1\">{{ data.CSort }}</td>\r\n              <td class=\"col-1\">{{ data.CStatus | getStatusName }}</td>\r\n              <td class=\"col-1\">{{ (data.CUnitPrice || 0) | ngxNumberWithCommas }}</td>\r\n              <td class=\"col-2\">\r\n                <button *ngIf=\"isUpdate\" type=\"button\" class=\"btn btn-outline-success m-1  btn-sm\"\r\n                  (click)=\"onEdit(data,dialog)\"><i class=\"fas fa-edit mr-1\"></i>編輯</button>\r\n                <button *ngIf=\"isDelete\" type=\"button\" class=\"btn btn-outline-danger m-1  btn-sm\"\r\n                  (click)=\"onDelete(data)\"><i class=\"far fa-trash-alt mr-1\"></i>刪除</button>\r\n              </td>\r\n            </tr>\r\n          </tbody>\r\n        </table>\r\n      </div>\r\n      <ngx-pagination [CollectionSize]=\"totalRecords\" [(Page)]=\"pageIndex\" [PageSize]=\"pageSize\"\r\n        (PageChange)=\"getList()\">\r\n      </ngx-pagination>\r\n    </div>\r\n  </nb-card-body>\r\n</nb-card>\r\n\r\n\r\n<ng-template #dialog let-data let-ref=\"dialogRef\">\r\n  <nb-card style=\"height: 100%; overflow: auto; max-width: 500px;\" class=\"mr-md-5 ml-md-5\">\r\n    <nb-card-header>\r\n      <span *ngIf=\"isNew===true\">新增</span>\r\n      <span *ngIf=\"isNew===false\">編輯</span>\r\n    </nb-card-header>\r\n    <nb-card-body style=\"padding:1rem 2rem\">\r\n      <div class=\"row\">\r\n        <div class=\"col-12 col-md-12\">\r\n          <div class=\"row\">\r\n            <app-form-group [label]=\"'建案名稱'\" [labelFor]=\"'CBuildCaseID'\" [isRequired]=\"true\">\r\n              <nb-select class=\"flex-grow-1\" placeholder=\"請選擇\" id=\"CBuildCaseID\" name=\"CBuildCaseID\"\r\n                [(selected)]=\"saveRequirement.CBuildCaseID\">\r\n                <nb-option langg *ngFor=\"let b of buildCaseList\" [value]=\"b.cID\"> {{b.CBuildCaseName}}</nb-option>\r\n              </nb-select>\r\n            </app-form-group> <app-form-group [label]=\"'需求'\" [labelFor]=\"'CRequirement'\" [isRequired]=\"true\">\r\n              <input type=\"text\" nbInput class=\"flex-grow-1\" id=\"CRequirement\" name=\"CRequirement\" placeholder=\"需求\"\r\n                [(ngModel)]=\"saveRequirement.CRequirement\">\r\n            </app-form-group> <app-form-group [label]=\"'群組名稱'\" [labelFor]=\"'CGroupName'\" [isRequired]=\"false\">\r\n              <input type=\"text\" nbInput class=\"flex-grow-1\" id=\"CGroupName\" name=\"CGroupName\" placeholder=\"群組名稱\"\r\n                [(ngModel)]=\"saveRequirement.CGroupName\" maxlength=\"20\">\r\n            </app-form-group>\r\n            <app-form-group [label]=\"'排序'\" [labelFor]=\"'CSort'\" [isRequired]=\"true\">\r\n              <input type=\"number\" nbInput class=\"flex-grow-1\" id=\"CRequirement\" name=\"CRequirement\" placeholder=\"排序\"\r\n                [(ngModel)]=\"saveRequirement.CSort\">\r\n            </app-form-group>\r\n            <app-form-group [label]=\"'類型'\" [labelFor]=\"'CHouseType'\" [isRequired]=\"true\">\r\n              <nb-select class=\"flex-grow-1\" placeholder=\"請選擇\" id=\"CHouseType\" name=\"CHouseType\"\r\n                [(selected)]=\"saveRequirement.CHouseType\" multiple>\r\n                <nb-option langg *ngFor=\"let type of houseType\" [value]=\"type.value\"> {{type.label}}</nb-option>\r\n              </nb-select>\r\n            </app-form-group>\r\n            <app-form-group [label]=\"'狀態'\" [labelFor]=\"'CStatus'\" [isRequired]=\"true\">\r\n              <nb-select class=\"flex-grow-1\" placeholder=\"請選擇\" id=\"CStatus\" name=\"CStatus\"\r\n                [(selected)]=\"saveRequirement.CStatus\">\r\n                <nb-option langg *ngFor=\"let status of statusOptions\" [value]=\"status.value\">\r\n                  {{status.label}}</nb-option>\r\n              </nb-select>\r\n            </app-form-group> <app-form-group [label]=\"'單價'\" [labelFor]=\"'CUnitPrice'\" [isRequired]=\"true\">\r\n              <input type=\"number\" nbInput class=\"flex-grow-1\" id=\"CUnitPrice\" name=\"CUnitPrice\" placeholder=\"單價\"\r\n                [(ngModel)]=\"saveRequirement.CUnitPrice\">\r\n            </app-form-group>\r\n            <app-form-group [label]=\"'備註說明'\" [labelFor]=\"'CRemark'\" [isRequired]=\"false\">\r\n              <textarea nbInput class=\"flex-grow-1\" id=\"CRemark\" name=\"CRemark\" placeholder=\"備註說明\"\r\n                [(ngModel)]=\"saveRequirement.CRemark\" maxlength=\"100\" rows=\"3\"></textarea>\r\n            </app-form-group>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </nb-card-body>\r\n    <nb-card-footer>\r\n      <div class=\"row\">\r\n        <div class=\"col-12 text-center\">\r\n          <button class=\"btn btn-success mr-2\" (click)=\"save(ref)\">確定</button>\r\n          <button class=\"btn btn-danger mr-2\" (click)=\"ref.close()\">取消</button>\r\n        </div>\r\n      </div>\r\n    </nb-card-footer>\r\n  </nb-card>\r\n</ng-template>"], "mappings": ";AAEA,SAASA,YAAY,EAAEC,gBAAgB,EAAmBC,aAAa,EAAEC,cAAc,EAAEC,cAAc,QAAQ,gBAAgB;AAO/H,SAASC,aAAa,QAAQ,kCAAkC;AAChE,SAASC,kBAAkB,QAAQ,4BAA4B;AAE/D,SAASC,mBAAmB,QAAQ,+CAA+C;AACnF,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,KAAK,EAAEC,IAAI,QAAQ,iBAAiB;AAC7C,SAASC,mBAAmB,QAAQ,+CAA+C;AACnF,SAASC,UAAU,QAAQ,mCAAmC;AAC9D,SAASC,kBAAkB,QAAQ,2DAA2D;AAC9F,SAASC,oBAAoB,QAAQ,8CAA8C;AACnF,SAASC,aAAa,QAAQ,mCAAmC;;;;;;;;;;;;;;ICRrDC,EAAA,CAAAC,cAAA,mBAAiE;IAC/DD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAFkCH,EAAA,CAAAI,UAAA,UAAAC,OAAA,CAAAC,GAAA,CAAkB;IAC9DN,EAAA,CAAAO,SAAA,EACF;IADEP,EAAA,CAAAQ,kBAAA,MAAAH,OAAA,CAAAI,cAAA,MACF;;;;;IAiBAT,EAAA,CAAAC,cAAA,mBAA+D;IAC7DD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAF8BH,EAAA,CAAAI,UAAA,UAAAM,OAAA,CAAAC,KAAA,CAAoB;IAC5DX,EAAA,CAAAO,SAAA,EACF;IADEP,EAAA,CAAAQ,kBAAA,MAAAE,OAAA,CAAAE,KAAA,MACF;;;;;;IAgBFZ,EAAA,CAAAC,cAAA,iBAA4E;IAAvCD,EAAA,CAAAa,UAAA,mBAAAC,0EAAA;MAAAd,EAAA,CAAAe,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAAA,MAAAC,SAAA,GAAAnB,EAAA,CAAAoB,WAAA;MAAA,OAAApB,EAAA,CAAAqB,WAAA,CAASJ,MAAA,CAAAK,GAAA,CAAAH,SAAA,CAAW;IAAA,EAAC;IAAkBnB,EAAA,CAAAuB,SAAA,YAC3C;IAAAvB,EAAA,CAAAE,MAAA,mBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;IAgCtCH,EAAA,CAAAC,cAAA,iBACgC;IAA9BD,EAAA,CAAAa,UAAA,mBAAAW,gFAAA;MAAAxB,EAAA,CAAAe,aAAA,CAAAU,GAAA;MAAA,MAAAC,OAAA,GAAA1B,EAAA,CAAAkB,aAAA,GAAAS,SAAA;MAAA,MAAAV,MAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAAA,MAAAC,SAAA,GAAAnB,EAAA,CAAAoB,WAAA;MAAA,OAAApB,EAAA,CAAAqB,WAAA,CAASJ,MAAA,CAAAW,MAAA,CAAAF,OAAA,EAAAP,SAAA,CAAmB;IAAA,EAAC;IAACnB,EAAA,CAAAuB,SAAA,YAAgC;IAAAvB,EAAA,CAAAE,MAAA,mBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;IAC3EH,EAAA,CAAAC,cAAA,iBAC2B;IAAzBD,EAAA,CAAAa,UAAA,mBAAAgB,gFAAA;MAAA7B,EAAA,CAAAe,aAAA,CAAAe,GAAA;MAAA,MAAAJ,OAAA,GAAA1B,EAAA,CAAAkB,aAAA,GAAAS,SAAA;MAAA,MAAAV,MAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAAA,OAAAlB,EAAA,CAAAqB,WAAA,CAASJ,MAAA,CAAAc,QAAA,CAAAL,OAAA,CAAc;IAAA,EAAC;IAAC1B,EAAA,CAAAuB,SAAA,YAAqC;IAAAvB,EAAA,CAAAE,MAAA,mBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;IAX7EH,EADF,CAAAC,cAAA,aAAuE,aACnD;IAAAD,EAAA,CAAAE,MAAA,GAAyB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAChDH,EAAA,CAAAC,cAAA,aAAkB;IAAAD,EAAA,CAAAE,MAAA,GAAuB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC9CH,EAAA,CAAAC,cAAA,aAAkB;IAAAD,EAAA,CAAAE,MAAA,GAAqB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC5CH,EAAA,CAAAC,cAAA,aAAkB;IAAAD,EAAA,CAAAE,MAAA,GAAyC;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAChEH,EAAA,CAAAC,cAAA,aAAkB;IAAAD,EAAA,CAAAE,MAAA,IAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACvCH,EAAA,CAAAC,cAAA,cAAkB;IAAAD,EAAA,CAAAE,MAAA,IAAkC;;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACzDH,EAAA,CAAAC,cAAA,cAAkB;IAAAD,EAAA,CAAAE,MAAA,IAAkD;;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACzEH,EAAA,CAAAC,cAAA,cAAkB;IAGhBD,EAFA,CAAAgC,UAAA,KAAAC,uDAAA,qBACgC,KAAAC,uDAAA,qBAEL;IAE/BlC,EADE,CAAAG,YAAA,EAAK,EACF;;;;;IAbeH,EAAA,CAAAO,SAAA,GAAyB;IAAzBP,EAAA,CAAAmC,iBAAA,CAAAT,OAAA,CAAAjB,cAAA,CAAyB;IACzBT,EAAA,CAAAO,SAAA,GAAuB;IAAvBP,EAAA,CAAAmC,iBAAA,CAAAT,OAAA,CAAAU,YAAA,CAAuB;IACvBpC,EAAA,CAAAO,SAAA,GAAqB;IAArBP,EAAA,CAAAmC,iBAAA,CAAAT,OAAA,CAAAW,UAAA,CAAqB;IACrBrC,EAAA,CAAAO,SAAA,GAAyC;IAAzCP,EAAA,CAAAmC,iBAAA,CAAAlB,MAAA,CAAAqB,YAAA,CAAAZ,OAAA,CAAAa,UAAA,IAAAvC,EAAA,CAAAwC,eAAA,KAAAC,GAAA,GAAyC;IACzCzC,EAAA,CAAAO,SAAA,GAAgB;IAAhBP,EAAA,CAAAmC,iBAAA,CAAAT,OAAA,CAAAgB,KAAA,CAAgB;IAChB1C,EAAA,CAAAO,SAAA,GAAkC;IAAlCP,EAAA,CAAAmC,iBAAA,CAAAnC,EAAA,CAAA2C,WAAA,QAAAjB,OAAA,CAAAkB,OAAA,EAAkC;IAClC5C,EAAA,CAAAO,SAAA,GAAkD;IAAlDP,EAAA,CAAAmC,iBAAA,CAAAnC,EAAA,CAAA2C,WAAA,SAAAjB,OAAA,CAAAmB,UAAA,OAAkD;IAEzD7C,EAAA,CAAAO,SAAA,GAAc;IAAdP,EAAA,CAAAI,UAAA,SAAAa,MAAA,CAAA6B,QAAA,CAAc;IAEd9C,EAAA,CAAAO,SAAA,EAAc;IAAdP,EAAA,CAAAI,UAAA,SAAAa,MAAA,CAAA8B,QAAA,CAAc;;;;;IAkBjC/C,EAAA,CAAAC,cAAA,WAA2B;IAAAD,EAAA,CAAAE,MAAA,mBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IACpCH,EAAA,CAAAC,cAAA,WAA4B;IAAAD,EAAA,CAAAE,MAAA,mBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAS3BH,EAAA,CAAAC,cAAA,oBAAiE;IAACD,EAAA,CAAAE,MAAA,GAAoB;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAAjDH,EAAA,CAAAI,UAAA,UAAA4C,KAAA,CAAA1C,GAAA,CAAe;IAAEN,EAAA,CAAAO,SAAA,EAAoB;IAApBP,EAAA,CAAAQ,kBAAA,MAAAwC,KAAA,CAAAvC,cAAA,KAAoB;;;;;IAgBtFT,EAAA,CAAAC,cAAA,oBAAqE;IAACD,EAAA,CAAAE,MAAA,GAAc;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAAhDH,EAAA,CAAAI,UAAA,UAAA6C,QAAA,CAAAtC,KAAA,CAAoB;IAAEX,EAAA,CAAAO,SAAA,EAAc;IAAdP,EAAA,CAAAQ,kBAAA,MAAAyC,QAAA,CAAArC,KAAA,KAAc;;;;;IAMpFZ,EAAA,CAAAC,cAAA,oBAA6E;IAC3ED,EAAA,CAAAE,MAAA,GAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IADwBH,EAAA,CAAAI,UAAA,UAAA8C,UAAA,CAAAvC,KAAA,CAAsB;IAC1EX,EAAA,CAAAO,SAAA,EAAgB;IAAhBP,EAAA,CAAAQ,kBAAA,MAAA0C,UAAA,CAAAtC,KAAA,KAAgB;;;;;;IAlC9BZ,EADF,CAAAC,cAAA,kBAAyF,qBACvE;IAEdD,EADA,CAAAgC,UAAA,IAAAmB,6DAAA,mBAA2B,IAAAC,6DAAA,mBACC;IAC9BpD,EAAA,CAAAG,YAAA,EAAiB;IAMPH,EALV,CAAAC,cAAA,uBAAwC,aACrB,cACe,aACX,yBACkE,oBAEjC;IAA5CD,EAAA,CAAAqD,gBAAA,4BAAAC,2FAAAC,MAAA;MAAAvD,EAAA,CAAAe,aAAA,CAAAyC,IAAA;MAAA,MAAAvC,MAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAAAlB,EAAA,CAAAyD,kBAAA,CAAAxC,MAAA,CAAAyC,eAAA,CAAAC,YAAA,EAAAJ,MAAA,MAAAtC,MAAA,CAAAyC,eAAA,CAAAC,YAAA,GAAAJ,MAAA;MAAA,OAAAvD,EAAA,CAAAqB,WAAA,CAAAkC,MAAA;IAAA,EAA2C;IAC3CvD,EAAA,CAAAgC,UAAA,KAAA4B,mEAAA,wBAAiE;IAErE5D,EADE,CAAAG,YAAA,EAAY,EACG;IACfH,EADgB,CAAAC,cAAA,0BAA+E,iBAElD;IAA3CD,EAAA,CAAAqD,gBAAA,2BAAAQ,uFAAAN,MAAA;MAAAvD,EAAA,CAAAe,aAAA,CAAAyC,IAAA;MAAA,MAAAvC,MAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAAAlB,EAAA,CAAAyD,kBAAA,CAAAxC,MAAA,CAAAyC,eAAA,CAAAtB,YAAA,EAAAmB,MAAA,MAAAtC,MAAA,CAAAyC,eAAA,CAAAtB,YAAA,GAAAmB,MAAA;MAAA,OAAAvD,EAAA,CAAAqB,WAAA,CAAAkC,MAAA;IAAA,EAA0C;IAC9CvD,EAFE,CAAAG,YAAA,EAC6C,EAC9B;IACfH,EADgB,CAAAC,cAAA,0BAAgF,iBAEtC;IAAxDD,EAAA,CAAAqD,gBAAA,2BAAAS,uFAAAP,MAAA;MAAAvD,EAAA,CAAAe,aAAA,CAAAyC,IAAA;MAAA,MAAAvC,MAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAAAlB,EAAA,CAAAyD,kBAAA,CAAAxC,MAAA,CAAAyC,eAAA,CAAArB,UAAA,EAAAkB,MAAA,MAAAtC,MAAA,CAAAyC,eAAA,CAAArB,UAAA,GAAAkB,MAAA;MAAA,OAAAvD,EAAA,CAAAqB,WAAA,CAAAkC,MAAA;IAAA,EAAwC;IAC5CvD,EAFE,CAAAG,YAAA,EAC0D,EAC3C;IAEfH,EADF,CAAAC,cAAA,0BAAwE,iBAEhC;IAApCD,EAAA,CAAAqD,gBAAA,2BAAAU,uFAAAR,MAAA;MAAAvD,EAAA,CAAAe,aAAA,CAAAyC,IAAA;MAAA,MAAAvC,MAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAAAlB,EAAA,CAAAyD,kBAAA,CAAAxC,MAAA,CAAAyC,eAAA,CAAAhB,KAAA,EAAAa,MAAA,MAAAtC,MAAA,CAAAyC,eAAA,CAAAhB,KAAA,GAAAa,MAAA;MAAA,OAAAvD,EAAA,CAAAqB,WAAA,CAAAkC,MAAA;IAAA,EAAmC;IACvCvD,EAFE,CAAAG,YAAA,EACsC,EACvB;IAEfH,EADF,CAAAC,cAAA,0BAA6E,qBAEtB;IAAnDD,EAAA,CAAAqD,gBAAA,4BAAAW,4FAAAT,MAAA;MAAAvD,EAAA,CAAAe,aAAA,CAAAyC,IAAA;MAAA,MAAAvC,MAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAAAlB,EAAA,CAAAyD,kBAAA,CAAAxC,MAAA,CAAAyC,eAAA,CAAAnB,UAAA,EAAAgB,MAAA,MAAAtC,MAAA,CAAAyC,eAAA,CAAAnB,UAAA,GAAAgB,MAAA;MAAA,OAAAvD,EAAA,CAAAqB,WAAA,CAAAkC,MAAA;IAAA,EAAyC;IACzCvD,EAAA,CAAAgC,UAAA,KAAAiC,mEAAA,wBAAqE;IAEzEjE,EADE,CAAAG,YAAA,EAAY,EACG;IAEfH,EADF,CAAAC,cAAA,0BAA0E,qBAE/B;IAAvCD,EAAA,CAAAqD,gBAAA,4BAAAa,4FAAAX,MAAA;MAAAvD,EAAA,CAAAe,aAAA,CAAAyC,IAAA;MAAA,MAAAvC,MAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAAAlB,EAAA,CAAAyD,kBAAA,CAAAxC,MAAA,CAAAyC,eAAA,CAAAd,OAAA,EAAAW,MAAA,MAAAtC,MAAA,CAAAyC,eAAA,CAAAd,OAAA,GAAAW,MAAA;MAAA,OAAAvD,EAAA,CAAAqB,WAAA,CAAAkC,MAAA;IAAA,EAAsC;IACtCvD,EAAA,CAAAgC,UAAA,KAAAmC,mEAAA,wBAA6E;IAGjFnE,EADE,CAAAG,YAAA,EAAY,EACG;IACfH,EADgB,CAAAC,cAAA,0BAA6E,iBAElD;IAAzCD,EAAA,CAAAqD,gBAAA,2BAAAe,uFAAAb,MAAA;MAAAvD,EAAA,CAAAe,aAAA,CAAAyC,IAAA;MAAA,MAAAvC,MAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAAAlB,EAAA,CAAAyD,kBAAA,CAAAxC,MAAA,CAAAyC,eAAA,CAAAb,UAAA,EAAAU,MAAA,MAAAtC,MAAA,CAAAyC,eAAA,CAAAb,UAAA,GAAAU,MAAA;MAAA,OAAAvD,EAAA,CAAAqB,WAAA,CAAAkC,MAAA;IAAA,EAAwC;IAC5CvD,EAFE,CAAAG,YAAA,EAC2C,EAC5B;IAEfH,EADF,CAAAC,cAAA,0BAA6E,oBAEV;IAA/DD,EAAA,CAAAqD,gBAAA,2BAAAgB,0FAAAd,MAAA;MAAAvD,EAAA,CAAAe,aAAA,CAAAyC,IAAA;MAAA,MAAAvC,MAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAAAlB,EAAA,CAAAyD,kBAAA,CAAAxC,MAAA,CAAAyC,eAAA,CAAAY,OAAA,EAAAf,MAAA,MAAAtC,MAAA,CAAAyC,eAAA,CAAAY,OAAA,GAAAf,MAAA;MAAA,OAAAvD,EAAA,CAAAqB,WAAA,CAAAkC,MAAA;IAAA,EAAqC;IAKjDvD,EAL2E,CAAAG,YAAA,EAAW,EAC7D,EACb,EACF,EACF,EACO;IAITH,EAHN,CAAAC,cAAA,sBAAgB,cACG,eACiB,kBAC2B;IAApBD,EAAA,CAAAa,UAAA,mBAAA0D,gFAAA;MAAA,MAAAC,OAAA,GAAAxE,EAAA,CAAAe,aAAA,CAAAyC,IAAA,EAAAiB,SAAA;MAAA,MAAAxD,MAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAAA,OAAAlB,EAAA,CAAAqB,WAAA,CAASJ,MAAA,CAAAyD,IAAA,CAAAF,OAAA,CAAS;IAAA,EAAC;IAACxE,EAAA,CAAAE,MAAA,oBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACpEH,EAAA,CAAAC,cAAA,kBAA0D;IAAtBD,EAAA,CAAAa,UAAA,mBAAA8D,gFAAA;MAAA,MAAAH,OAAA,GAAAxE,EAAA,CAAAe,aAAA,CAAAyC,IAAA,EAAAiB,SAAA;MAAA,OAAAzE,EAAA,CAAAqB,WAAA,CAASmD,OAAA,CAAAI,KAAA,EAAW;IAAA,EAAC;IAAC5E,EAAA,CAAAE,MAAA,oBAAE;IAIpEF,EAJoE,CAAAG,YAAA,EAAS,EACjE,EACF,EACS,EACT;;;;IAvDCH,EAAA,CAAAO,SAAA,GAAkB;IAAlBP,EAAA,CAAAI,UAAA,SAAAa,MAAA,CAAA4D,KAAA,UAAkB;IAClB7E,EAAA,CAAAO,SAAA,EAAmB;IAAnBP,EAAA,CAAAI,UAAA,SAAAa,MAAA,CAAA4D,KAAA,WAAmB;IAMJ7E,EAAA,CAAAO,SAAA,GAAgB;IAA6BP,EAA7C,CAAAI,UAAA,qCAAgB,4BAA4B,oBAAoB;IAE5EJ,EAAA,CAAAO,SAAA,EAA2C;IAA3CP,EAAA,CAAA8E,gBAAA,aAAA7D,MAAA,CAAAyC,eAAA,CAAAC,YAAA,CAA2C;IACZ3D,EAAA,CAAAO,SAAA,EAAgB;IAAhBP,EAAA,CAAAI,UAAA,YAAAa,MAAA,CAAA8D,aAAA,CAAgB;IAEjB/E,EAAA,CAAAO,SAAA,EAAc;IAA6BP,EAA3C,CAAAI,UAAA,yBAAc,4BAA4B,oBAAoB;IAE5FJ,EAAA,CAAAO,SAAA,EAA0C;IAA1CP,EAAA,CAAA8E,gBAAA,YAAA7D,MAAA,CAAAyC,eAAA,CAAAtB,YAAA,CAA0C;IACZpC,EAAA,CAAAO,SAAA,EAAgB;IAA2BP,EAA3C,CAAAI,UAAA,qCAAgB,0BAA0B,qBAAqB;IAE7FJ,EAAA,CAAAO,SAAA,EAAwC;IAAxCP,EAAA,CAAA8E,gBAAA,YAAA7D,MAAA,CAAAyC,eAAA,CAAArB,UAAA,CAAwC;IAE5BrC,EAAA,CAAAO,SAAA,EAAc;IAAsBP,EAApC,CAAAI,UAAA,yBAAc,qBAAqB,oBAAoB;IAEnEJ,EAAA,CAAAO,SAAA,EAAmC;IAAnCP,EAAA,CAAA8E,gBAAA,YAAA7D,MAAA,CAAAyC,eAAA,CAAAhB,KAAA,CAAmC;IAEvB1C,EAAA,CAAAO,SAAA,EAAc;IAA2BP,EAAzC,CAAAI,UAAA,yBAAc,0BAA0B,oBAAoB;IAExEJ,EAAA,CAAAO,SAAA,EAAyC;IAAzCP,EAAA,CAAA8E,gBAAA,aAAA7D,MAAA,CAAAyC,eAAA,CAAAnB,UAAA,CAAyC;IACPvC,EAAA,CAAAO,SAAA,EAAY;IAAZP,EAAA,CAAAI,UAAA,YAAAa,MAAA,CAAA+D,SAAA,CAAY;IAGlChF,EAAA,CAAAO,SAAA,EAAc;IAAwBP,EAAtC,CAAAI,UAAA,yBAAc,uBAAuB,oBAAoB;IAErEJ,EAAA,CAAAO,SAAA,EAAsC;IAAtCP,EAAA,CAAA8E,gBAAA,aAAA7D,MAAA,CAAAyC,eAAA,CAAAd,OAAA,CAAsC;IACF5C,EAAA,CAAAO,SAAA,EAAgB;IAAhBP,EAAA,CAAAI,UAAA,YAAAa,MAAA,CAAAgE,aAAA,CAAgB;IAGtBjF,EAAA,CAAAO,SAAA,EAAc;IAA2BP,EAAzC,CAAAI,UAAA,yBAAc,0BAA0B,oBAAoB;IAE1FJ,EAAA,CAAAO,SAAA,EAAwC;IAAxCP,EAAA,CAAA8E,gBAAA,YAAA7D,MAAA,CAAAyC,eAAA,CAAAb,UAAA,CAAwC;IAE5B7C,EAAA,CAAAO,SAAA,EAAgB;IAAwBP,EAAxC,CAAAI,UAAA,qCAAgB,uBAAuB,qBAAqB;IAExEJ,EAAA,CAAAO,SAAA,EAAqC;IAArCP,EAAA,CAAA8E,gBAAA,YAAA7D,MAAA,CAAAyC,eAAA,CAAAY,OAAA,CAAqC;;;ADrGrD,OAAM,MAAOY,8BAA+B,SAAQ7F,aAAa;EAC/D8F,YACUC,MAAmB,EACnBC,UAAsB,EACtBC,aAA8B,EAC9BC,OAAuB,EACvBC,KAAuB,EACvBC,gBAAkC,EAClCC,kBAAsC,EACtCC,OAAsB,EACtBC,MAAc,EACdC,UAAsB;IAE9B,KAAK,CAACT,MAAM,CAAC;IAXL,KAAAA,MAAM,GAANA,MAAM;IACN,KAAAC,UAAU,GAAVA,UAAU;IACV,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,kBAAkB,GAAlBA,kBAAkB;IAClB,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,UAAU,GAAVA,UAAU;IAQpB;IACA,KAAAC,yBAAyB,GAAG,EAA+B;IAC3D,KAAAC,qBAAqB,GAA8B,EAAE;IAErD;IACA,KAAAhB,aAAa,GAA8B,EAAE;IAC7C,KAAAiB,eAAe,GAAqB,EAAE;IACtC,KAAAtC,eAAe,GAAwB;MAAEnB,UAAU,EAAE;IAAE,CAAE;IAEzD;IACA,KAAA0D,iBAAiB,GAAW,EAAE;IAC9B,KAAAC,eAAe,GAAW,EAAE;IAC5B,KAAAC,eAAe,GAAW,CAAC,CAAC;IAE5B,KAAAlB,aAAa,GAAG,CACd;MAAEtE,KAAK,EAAE,CAAC;MAAEC,KAAK,EAAE;IAAI,CAAE,EACzB;MAAED,KAAK,EAAE,CAAC;MAAEC,KAAK,EAAE;IAAI,CAAE,CAC1B;IACD,KAAAoE,SAAS,GAAG,IAAI,CAACK,UAAU,CAACe,cAAc,CAACrG,aAAa,CAAC;IACzD,KAAA8E,KAAK,GAAG,KAAK;IACb,KAAAwB,gBAAgB,GAAG,CAAC;IAzBlB,IAAI,CAACC,gBAAgB,EAAE;IACvB,IAAI,CAACR,yBAAyB,CAACnC,YAAY,GAAG,CAAC,CAAC;IAChD,IAAI,CAACmC,yBAAyB,CAAClD,OAAO,GAAG,CAAC,CAAC;IAC3C,IAAI,CAAC2D,OAAO,EAAE;EAChB;EAuBSC,QAAQA,CAAA,GAAW;EAE5BlE,YAAYA,CAACmE,MAA4C;IACvD,IAAI,CAACA,MAAM,EAAE;MACX,OAAO,EAAE;IACX;IAEA,IAAI,CAACC,KAAK,CAACC,OAAO,CAACF,MAAM,CAAC,EAAE;MAC1BA,MAAM,GAAG,CAACA,MAAM,CAAC;IACnB;IAEA,IAAIG,MAAM,GAAa,EAAE;IACzBH,MAAM,CAACI,OAAO,CAACC,KAAK,IAAG;MACrB,IAAIC,KAAK,GAAG,IAAI,CAAC/B,SAAS,CAACgC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACtG,KAAK,IAAImG,KAAK,CAAC;MACtD,IAAIC,KAAK,EAAE;QACTH,MAAM,CAACM,IAAI,CAACH,KAAK,CAACnG,KAAK,CAAC;MAC1B;IACF,CAAC,CAAC;IACF,OAAOgG,MAAM,CAACO,IAAI,CAAC,IAAI,CAAC;EAC1B;EAAEC,UAAUA,CAAA;IACV,IAAI,CAAC5B,KAAK,CAAC6B,KAAK,EAAE;IAClB,IAAI,CAAC7B,KAAK,CAAC8B,QAAQ,CAAC,QAAQ,EAAE,IAAI,CAAC5D,eAAe,CAACC,YAAY,CAAC;IAChE,IAAI,CAAC6B,KAAK,CAAC8B,QAAQ,CAAC,MAAM,EAAE,IAAI,CAAC5D,eAAe,CAACtB,YAAY,CAAC;IAC9D,IAAI,CAACoD,KAAK,CAAC8B,QAAQ,CAAC,OAAO,EAAE,IAAI,CAAC5D,eAAe,CAACnB,UAAU,CAAC;IAC7D,IAAI,CAACiD,KAAK,CAAC8B,QAAQ,CAAC,MAAM,EAAE,IAAI,CAAC5D,eAAe,CAAChB,KAAK,CAAC;IACvD,IAAI,CAAC8C,KAAK,CAAC8B,QAAQ,CAAC,MAAM,EAAE,IAAI,CAAC5D,eAAe,CAACd,OAAO,CAAC;IACzD,IAAI,CAAC4C,KAAK,CAAC8B,QAAQ,CAAC,MAAM,EAAE,IAAI,CAAC5D,eAAe,CAACb,UAAU,CAAC;IAE5D;IACA,IAAI,IAAI,CAACa,eAAe,CAACrB,UAAU,IAAI,IAAI,CAACqB,eAAe,CAACrB,UAAU,CAACkF,MAAM,GAAG,EAAE,EAAE;MAClF,IAAI,CAAC/B,KAAK,CAACgC,aAAa,CAACN,IAAI,CAAC,iBAAiB,CAAC;IAClD;IAEA;IACA,IAAI,IAAI,CAACxD,eAAe,CAACY,OAAO,IAAI,IAAI,CAACZ,eAAe,CAACY,OAAO,CAACiD,MAAM,GAAG,GAAG,EAAE;MAC7E,IAAI,CAAC/B,KAAK,CAACgC,aAAa,CAACN,IAAI,CAAC,kBAAkB,CAAC;IACnD;EACF;EAEA5F,GAAGA,CAACmG,MAAwB;IAC1B,IAAI,CAAC5C,KAAK,GAAG,IAAI;IACjB,IAAI,CAACnB,eAAe,GAAG;MAAEnB,UAAU,EAAE;IAAE,CAAE;IACzC,IAAI,CAACmB,eAAe,CAACd,OAAO,GAAG,CAAC;IAChC,IAAI,CAACc,eAAe,CAACb,UAAU,GAAG,CAAC;IACnC,IAAI,IAAI,CAACwD,gBAAgB,IAAI,CAAC,EAAE;MAC9B,IAAI,CAAC3C,eAAe,CAACC,YAAY,GAAG,IAAI,CAAC0C,gBAAgB;IAC3D;IACA,IAAI,CAACC,gBAAgB,EAAE;IACvB,IAAI,CAAChB,aAAa,CAACoC,IAAI,CAACD,MAAM,CAAC;EACjC;EAEM7F,MAAMA,CAAC+F,IAAoB,EAAEF,MAAwB;IAAA,IAAAG,KAAA;IAAA,OAAAC,iBAAA;MACzDD,KAAI,CAAC7B,qBAAqB,CAAC+B,cAAc,GAAGH,IAAI,CAACG,cAAe;MAChEF,KAAI,CAAC/C,KAAK,GAAG,KAAK;MAClB,IAAI;QACF,MAAM+C,KAAI,CAACG,OAAO,EAAE;QACpBH,KAAI,CAACtC,aAAa,CAACoC,IAAI,CAACD,MAAM,CAAC;MACjC,CAAC,CAAC,OAAOO,KAAK,EAAE;QACdC,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAEF,KAAK,CAAC;MACnD;IAAC;EACH;EAEAtD,IAAIA,CAACyD,GAAQ;IACX,IAAI,CAACf,UAAU,EAAE;IACjB,IAAI,IAAI,CAAC5B,KAAK,CAACgC,aAAa,CAACD,MAAM,GAAG,CAAC,EAAE;MACvC,IAAI,CAAChC,OAAO,CAAC6C,aAAa,CAAC,IAAI,CAAC5C,KAAK,CAACgC,aAAa,CAAC;MACpD;IACF;IAEA,IAAI,CAAC9B,kBAAkB,CAAC2C,+BAA+B,CAAC;MACtDC,IAAI,EAAE,IAAI,CAAC5E;KACZ,CAAC,CAAC6E,SAAS,CAACC,GAAG,IAAG;MACjB,IAAIA,GAAG,CAACC,UAAU,KAAK,CAAC,EAAE;QACxB,IAAI,CAAClD,OAAO,CAACmD,aAAa,CAAC,MAAM,CAAC;QAClC,IAAI,CAACnC,OAAO,EAAE;MAChB,CAAC,MAAM;QACL,IAAI,CAAChB,OAAO,CAACoD,YAAY,CAACH,GAAG,CAACI,OAAQ,CAAC;MACzC;IACF,CAAC,CAAC;IACFT,GAAG,CAACvD,KAAK,EAAE;EACb;EAEA7C,QAAQA,CAAC4F,IAAoB;IAC3B,IAAI,CAACjE,eAAe,CAACoE,cAAc,GAAGH,IAAI,CAACG,cAAe;IAC1D,IAAI,CAACjD,KAAK,GAAG,KAAK;IAClB,IAAIgE,MAAM,CAACC,OAAO,CAAC,SAAS,CAAC,EAAE;MAC7B,IAAI,CAACC,MAAM,EAAE;IACf,CAAC,MAAM;MACL;IACF;EACF;EAEAA,MAAMA,CAAA;IACJ,IAAI,CAACrD,kBAAkB,CAACsD,iCAAiC,CAAC;MACxDV,IAAI,EAAE;QACJR,cAAc,EAAE,IAAI,CAACpE,eAAe,CAACoE;;KAExC,CAAC,CAACS,SAAS,CAACC,GAAG,IAAG;MACjB,IAAI,CAACjD,OAAO,CAACmD,aAAa,CAAC,MAAM,CAAC;MAClC,IAAI,CAACnC,OAAO,EAAE;IAChB,CAAC,CAAC;EACJ;EAEAD,gBAAgBA,CAAA;IACd,IAAI,CAACb,gBAAgB,CAACwD,qCAAqC,CAAC;MAAEX,IAAI,EAAE;IAAE,CAAE,CAAC,CACtEY,IAAI,CAAC5J,kBAAkB,CAAC,IAAI,CAACuG,UAAU,CAAC,CAAC,CAAC0C,SAAS,CAACC,GAAG,IAAG;MACzD,IAAI,CAACzD,aAAa,GAAGyD,GAAG,CAACW,OAAQ;IACnC,CAAC,CAAC;EACN;EACA5C,OAAOA,CAAA;IACL,IAAI,CAACT,yBAAyB,CAACsD,QAAQ,GAAG,IAAI,CAACC,QAAQ;IACvD,IAAI,CAACvD,yBAAyB,CAACwD,SAAS,GAAG,IAAI,CAACC,SAAS;IACzD,IAAI,IAAI,CAACzD,yBAAyB,CAACnC,YAAY,IAAI,IAAI,CAACmC,yBAAyB,CAACnC,YAAY,IAAI,CAAC,EAAE;MACnG,IAAI,CAAC0C,gBAAgB,GAAG,IAAI,CAACP,yBAAyB,CAACnC,YAAY;IACrE;IAEA;IACA,IAAI,CAACmC,yBAAyB,CAAC1D,YAAY,GAAG,IAAI,CAAC6D,iBAAiB,IAAIuD,SAAS;IAEjF,IAAI,CAAC9D,kBAAkB,CAAC+D,8BAA8B,CAAC;MAAEnB,IAAI,EAAE,IAAI,CAACxC;IAAyB,CAAE,CAAC,CAC7FoD,IAAI,EAAE,CACNX,SAAS,CAACC,GAAG,IAAG;MACf,IAAIA,GAAG,CAACC,UAAU,IAAI,CAAC,EAAE;QACvB,IAAID,GAAG,CAACW,OAAO,EAAE;UACf,IAAIO,YAAY,GAAGlB,GAAG,CAACW,OAAO;UAE9B;UACA,IAAI,IAAI,CAACjD,eAAe,IAAI,IAAI,CAACA,eAAe,CAACyD,IAAI,EAAE,EAAE;YACvDD,YAAY,GAAGA,YAAY,CAACE,MAAM,CAACC,IAAI,IACrCA,IAAI,CAACxH,UAAU,IAAIwH,IAAI,CAACxH,UAAU,CAACyH,WAAW,EAAE,CAACC,QAAQ,CAAC,IAAI,CAAC7D,eAAe,CAAC4D,WAAW,EAAE,CAAC,CAC9F;UACH;UAEA;UACA,IAAI,IAAI,CAAC3D,eAAe,IAAI,IAAI,CAACA,eAAe,KAAK,CAAC,CAAC,EAAE;YACvDuD,YAAY,GAAGA,YAAY,CAACE,MAAM,CAACC,IAAI,IAAG;cACxC,IAAI,CAACA,IAAI,CAACtH,UAAU,EAAE,OAAO,KAAK;cAClC,MAAMyH,UAAU,GAAGtD,KAAK,CAACC,OAAO,CAACkD,IAAI,CAACtH,UAAU,CAAC,GAAGsH,IAAI,CAACtH,UAAU,GAAG,CAACsH,IAAI,CAACtH,UAAU,CAAC;cACvF,OAAOyH,UAAU,CAACD,QAAQ,CAAC,IAAI,CAAC5D,eAAe,CAAC;YAClD,CAAC,CAAC;UACJ;UAEA,IAAI,CAACH,eAAe,GAAG0D,YAAY;UACnC,IAAI,CAACO,YAAY,GAAGP,YAAY,CAACnC,MAAM;QACzC;MACF;IACF,CAAC,CAAC;EACN;EAAEQ,OAAOA,CAAA;IACP,IAAI,CAACrC,kBAAkB,CAACwE,8BAA8B,CAAC;MAAE5B,IAAI,EAAE,IAAI,CAACvC;IAAqB,CAAE,CAAC,CACzFmD,IAAI,EAAE,CACNX,SAAS,CAACC,GAAG,IAAG;MACf,IAAIA,GAAG,CAACC,UAAU,IAAI,CAAC,EAAE;QACvB,IAAID,GAAG,CAACW,OAAO,EAAE;UACf,IAAI,CAACzF,eAAe,GAAG;YAAEnB,UAAU,EAAE;UAAE,CAAE;UACzC,IAAI,CAACmB,eAAe,CAACC,YAAY,GAAG6E,GAAG,CAACW,OAAO,CAACxF,YAAY;UAC5D,IAAI,CAACD,eAAe,CAACrB,UAAU,GAAGmG,GAAG,CAACW,OAAO,CAAC9G,UAAU;UACxD,IAAI,CAACqB,eAAe,CAACnB,UAAU,GAAGiG,GAAG,CAACW,OAAO,CAAC5G,UAAU,GAAImE,KAAK,CAACC,OAAO,CAAC6B,GAAG,CAACW,OAAO,CAAC5G,UAAU,CAAC,GAAGiG,GAAG,CAACW,OAAO,CAAC5G,UAAU,GAAG,CAACiG,GAAG,CAACW,OAAO,CAAC5G,UAAU,CAAC,GAAI,EAAE;UAC3J,IAAI,CAACmB,eAAe,CAACY,OAAO,GAAGkE,GAAG,CAACW,OAAO,CAAC7E,OAAO;UAClD,IAAI,CAACZ,eAAe,CAACtB,YAAY,GAAGoG,GAAG,CAACW,OAAO,CAAC/G,YAAY;UAC5D,IAAI,CAACsB,eAAe,CAACoE,cAAc,GAAGU,GAAG,CAACW,OAAO,CAACrB,cAAc;UAChE,IAAI,CAACpE,eAAe,CAAChB,KAAK,GAAG8F,GAAG,CAACW,OAAO,CAACzG,KAAK;UAC9C,IAAI,CAACgB,eAAe,CAACd,OAAO,GAAG4F,GAAG,CAACW,OAAO,CAACvG,OAAO;UAClD,IAAI,CAACc,eAAe,CAACb,UAAU,GAAG2F,GAAG,CAACW,OAAO,CAACtG,UAAU;QAC1D;MACF;IACF,CAAC,CAAC;EACN;EAEAsH,iBAAiBA,CAACxJ,KAAa,EAAEyJ,OAAY;IAC3CnC,OAAO,CAACC,GAAG,CAACkC,OAAO,CAAC;IAEpB,IAAIA,OAAO,EAAE;MACX,IAAI,CAAC,IAAI,CAAC1G,eAAe,CAACnB,UAAU,EAAEwH,QAAQ,CAACpJ,KAAK,CAAC,EAAE;QACrD,IAAI,CAAC+C,eAAe,CAACnB,UAAU,EAAE2E,IAAI,CAACvG,KAAK,CAAC;MAC9C;MACAsH,OAAO,CAACC,GAAG,CAAC,IAAI,CAACxE,eAAe,CAACnB,UAAU,CAAC;IAC9C,CAAC,MAAM;MACL,IAAI,CAACmB,eAAe,CAACnB,UAAU,GAAG,IAAI,CAACmB,eAAe,CAACnB,UAAU,EAAEqH,MAAM,CAACS,CAAC,IAAIA,CAAC,KAAK1J,KAAK,CAAC;IAC7F;EACF;;;uCA5NWuE,8BAA8B,EAAAlF,EAAA,CAAAsK,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAxK,EAAA,CAAAsK,iBAAA,CAAAG,EAAA,CAAAC,UAAA,GAAA1K,EAAA,CAAAsK,iBAAA,CAAAK,EAAA,CAAAC,eAAA,GAAA5K,EAAA,CAAAsK,iBAAA,CAAAO,EAAA,CAAAC,cAAA,GAAA9K,EAAA,CAAAsK,iBAAA,CAAAS,EAAA,CAAAC,gBAAA,GAAAhL,EAAA,CAAAsK,iBAAA,CAAAW,EAAA,CAAAC,gBAAA,GAAAlL,EAAA,CAAAsK,iBAAA,CAAAW,EAAA,CAAAE,kBAAA,GAAAnL,EAAA,CAAAsK,iBAAA,CAAAc,EAAA,CAAAC,aAAA,GAAArL,EAAA,CAAAsK,iBAAA,CAAAgB,EAAA,CAAAC,MAAA,GAAAvL,EAAA,CAAAsK,iBAAA,CAAAtK,EAAA,CAAAwL,UAAA;IAAA;EAAA;;;YAA9BtG,8BAA8B;MAAAuG,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAA3L,EAAA,CAAA4L,0BAAA,EAAA5L,EAAA,CAAA6L,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,wCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;UCxCzCnM,EADF,CAAAC,cAAA,iBAA0B,qBACR;UACdD,EAAA,CAAAuB,SAAA,qBAAiC;UACnCvB,EAAA,CAAAG,YAAA,EAAiB;UAKTH,EAJR,CAAAC,cAAA,sBAA+B,aACT,aACD,aACyB,eACI;UAAAD,EAAA,CAAAE,MAAA,mBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACpDH,EAAA,CAAAC,cAAA,mBAA8E;UAAnED,EAAA,CAAAqD,gBAAA,2BAAAgJ,2EAAA9I,MAAA;YAAAvD,EAAA,CAAAe,aAAA,CAAAuL,GAAA;YAAAtM,EAAA,CAAAyD,kBAAA,CAAA2I,GAAA,CAAAtG,yBAAA,CAAAnC,YAAA,EAAAJ,MAAA,MAAA6I,GAAA,CAAAtG,yBAAA,CAAAnC,YAAA,GAAAJ,MAAA;YAAA,OAAAvD,EAAA,CAAAqB,WAAA,CAAAkC,MAAA;UAAA,EAAoD;UAC7DvD,EAAA,CAAAC,cAAA,oBAAwB;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAY;UACtCH,EAAA,CAAAgC,UAAA,KAAAuK,oDAAA,uBAAiE;UAIrEvM,EADE,CAAAG,YAAA,EAAY,EACR;UAEJH,EADF,CAAAC,cAAA,cAAwC,iBACM;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACtDH,EAAA,CAAAC,cAAA,iBACuD;UAArDD,EAAA,CAAAqD,gBAAA,2BAAAmJ,wEAAAjJ,MAAA;YAAAvD,EAAA,CAAAe,aAAA,CAAAuL,GAAA;YAAAtM,EAAA,CAAAyD,kBAAA,CAAA2I,GAAA,CAAAtG,yBAAA,CAAA1D,YAAA,EAAAmB,MAAA,MAAA6I,GAAA,CAAAtG,yBAAA,CAAA1D,YAAA,GAAAmB,MAAA;YAAA,OAAAvD,EAAA,CAAAqB,WAAA,CAAAkC,MAAA;UAAA,EAAoD;UACxDvD,EAFE,CAAAG,YAAA,EACuD,EACnD;UACJH,EADY,CAAAC,cAAA,cAAwC,iBACV;UAAAD,EAAA,CAAAE,MAAA,gCAAI;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACtDH,EAAA,CAAAC,cAAA,iBACqD;UAAnDD,EAAA,CAAAqD,gBAAA,2BAAAoJ,wEAAAlJ,MAAA;YAAAvD,EAAA,CAAAe,aAAA,CAAAuL,GAAA;YAAAtM,EAAA,CAAAyD,kBAAA,CAAA2I,GAAA,CAAAtG,yBAAA,CAAAzD,UAAA,EAAAkB,MAAA,MAAA6I,GAAA,CAAAtG,yBAAA,CAAAzD,UAAA,GAAAkB,MAAA;YAAA,OAAAvD,EAAA,CAAAqB,WAAA,CAAAkC,MAAA;UAAA,EAAkD;UAExDvD,EAHI,CAAAG,YAAA,EACqD,EACjD,EACF;UAGFH,EAFJ,CAAAC,cAAA,cAAiB,cACyB,iBACI;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACpDH,EAAA,CAAAC,cAAA,qBAAqF;UAA1ED,EAAA,CAAAqD,gBAAA,2BAAAqJ,4EAAAnJ,MAAA;YAAAvD,EAAA,CAAAe,aAAA,CAAAuL,GAAA;YAAAtM,EAAA,CAAAyD,kBAAA,CAAA2I,GAAA,CAAAtG,yBAAA,CAAAvD,UAAA,EAAAgB,MAAA,MAAA6I,GAAA,CAAAtG,yBAAA,CAAAvD,UAAA,GAAAgB,MAAA;YAAA,OAAAvD,EAAA,CAAAqB,WAAA,CAAAkC,MAAA;UAAA,EAAkD;UAC3DvD,EAAA,CAAAgC,UAAA,KAAA2K,oDAAA,uBAA+D;UAInE3M,EADE,CAAAG,YAAA,EAAY,EACR;UAEJH,EADF,CAAAC,cAAA,cAAwC,iBACC;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACjDH,EAAA,CAAAC,cAAA,oBAAyE;UAA9DD,EAAA,CAAAqD,gBAAA,2BAAAuJ,4EAAArJ,MAAA;YAAAvD,EAAA,CAAAe,aAAA,CAAAuL,GAAA;YAAAtM,EAAA,CAAAyD,kBAAA,CAAA2I,GAAA,CAAAtG,yBAAA,CAAAlD,OAAA,EAAAW,MAAA,MAAA6I,GAAA,CAAAtG,yBAAA,CAAAlD,OAAA,GAAAW,MAAA;YAAA,OAAAvD,EAAA,CAAAqB,WAAA,CAAAkC,MAAA;UAAA,EAA+C;UACxDvD,EAAA,CAAAC,cAAA,oBAAwB;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAY;UACtCH,EAAA,CAAAC,cAAA,oBAAuB;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAY;UACrCH,EAAA,CAAAC,cAAA,oBAAuB;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAG/BF,EAH+B,CAAAG,YAAA,EAAY,EAC3B,EACR,EACF;UAAMH,EAAA,CAAAC,cAAA,cAAiB;UAC3BD,EAAA,CAAAuB,SAAA,eAA4B;UAE1BvB,EADF,CAAAC,cAAA,eAAmD,kBACc;UAAxBD,EAAA,CAAAa,UAAA,mBAAAgM,iEAAA;YAAA7M,EAAA,CAAAe,aAAA,CAAAuL,GAAA;YAAA,OAAAtM,EAAA,CAAAqB,WAAA,CAAS+K,GAAA,CAAAU,WAAA,EAAa;UAAA,EAAC;UAAC9M,EAAA,CAAAuB,SAAA,aAAgC;UAAAvB,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAC1GH,EAAA,CAAAC,cAAA,kBAAsD;UAApBD,EAAA,CAAAa,UAAA,mBAAAkM,iEAAA;YAAA/M,EAAA,CAAAe,aAAA,CAAAuL,GAAA;YAAA,OAAAtM,EAAA,CAAAqB,WAAA,CAAS+K,GAAA,CAAA7F,OAAA,EAAS;UAAA,EAAC;UAACvG,EAAA,CAAAuB,SAAA,aAAkC;UAAAvB,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACnGH,EAAA,CAAAgC,UAAA,KAAAgL,iDAAA,qBAA4E;UAKpFhN,EAHM,CAAAG,YAAA,EAAM,EACF,EACF,EACO;UAQHH,EANZ,CAAAC,cAAA,uBAA+B,cACT,eACY,iBACmE,aACtF,cAC4D,cACjC;UAAAD,EAAA,CAAAE,MAAA,gCAAI;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACvCH,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACrCH,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAE,MAAA,gCAAI;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACvCH,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACrCH,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACrCH,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACrCH,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACrCH,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAE,MAAA,gCAAI;UAEtCF,EAFsC,CAAAG,YAAA,EAAK,EACpC,EACC;UACRH,EAAA,CAAAC,cAAA,aAAO;UACLD,EAAA,CAAAgC,UAAA,KAAAiL,6CAAA,mBAAuE;UAiB7EjN,EAFI,CAAAG,YAAA,EAAQ,EACF,EACJ;UACNH,EAAA,CAAAC,cAAA,0BAC2B;UADqBD,EAAA,CAAAqD,gBAAA,wBAAA6J,8EAAA3J,MAAA;YAAAvD,EAAA,CAAAe,aAAA,CAAAuL,GAAA;YAAAtM,EAAA,CAAAyD,kBAAA,CAAA2I,GAAA,CAAA7C,SAAA,EAAAhG,MAAA,MAAA6I,GAAA,CAAA7C,SAAA,GAAAhG,MAAA;YAAA,OAAAvD,EAAA,CAAAqB,WAAA,CAAAkC,MAAA;UAAA,EAAoB;UAClEvD,EAAA,CAAAa,UAAA,wBAAAqM,8EAAA;YAAAlN,EAAA,CAAAe,aAAA,CAAAuL,GAAA;YAAA,OAAAtM,EAAA,CAAAqB,WAAA,CAAc+K,GAAA,CAAA7F,OAAA,EAAS;UAAA,EAAC;UAIhCvG,EAHM,CAAAG,YAAA,EAAiB,EACb,EACO,EACP;UAGVH,EAAA,CAAAgC,UAAA,KAAAmL,sDAAA,kCAAAnN,EAAA,CAAAoN,sBAAA,CAAkD;;;UAzF7BpN,EAAA,CAAAO,SAAA,GAAoD;UAApDP,EAAA,CAAA8E,gBAAA,YAAAsH,GAAA,CAAAtG,yBAAA,CAAAnC,YAAA,CAAoD;UAClD3D,EAAA,CAAAO,SAAA,EAAY;UAAZP,EAAA,CAAAI,UAAA,aAAY;UACKJ,EAAA,CAAAO,SAAA,GAAgB;UAAhBP,EAAA,CAAAI,UAAA,YAAAgM,GAAA,CAAArH,aAAA,CAAgB;UAQ5C/E,EAAA,CAAAO,SAAA,GAAoD;UAApDP,EAAA,CAAA8E,gBAAA,YAAAsH,GAAA,CAAAtG,yBAAA,CAAA1D,YAAA,CAAoD;UAIpDpC,EAAA,CAAAO,SAAA,GAAkD;UAAlDP,EAAA,CAAA8E,gBAAA,YAAAsH,GAAA,CAAAtG,yBAAA,CAAAzD,UAAA,CAAkD;UAMzCrC,EAAA,CAAAO,SAAA,GAAkD;UAAlDP,EAAA,CAAA8E,gBAAA,YAAAsH,GAAA,CAAAtG,yBAAA,CAAAvD,UAAA,CAAkD;UAC/BvC,EAAA,CAAAO,SAAA,EAAY;UAAZP,EAAA,CAAAI,UAAA,YAAAgM,GAAA,CAAApH,SAAA,CAAY;UAO/BhF,EAAA,CAAAO,SAAA,GAA+C;UAA/CP,EAAA,CAAA8E,gBAAA,YAAAsH,GAAA,CAAAtG,yBAAA,CAAAlD,OAAA,CAA+C;UAC7C5C,EAAA,CAAAO,SAAA,EAAY;UAAZP,EAAA,CAAAI,UAAA,aAAY;UACZJ,EAAA,CAAAO,SAAA,GAAW;UAAXP,EAAA,CAAAI,UAAA,YAAW;UACXJ,EAAA,CAAAO,SAAA,GAAW;UAAXP,EAAA,CAAAI,UAAA,YAAW;UAQoCJ,EAAA,CAAAO,SAAA,IAAc;UAAdP,EAAA,CAAAI,UAAA,SAAAgM,GAAA,CAAAiB,QAAA,CAAc;UAwBnDrN,EAAA,CAAAO,SAAA,IAAoB;UAApBP,EAAA,CAAAI,UAAA,YAAAgM,GAAA,CAAApG,eAAA,CAAoB;UAkB/BhG,EAAA,CAAAO,SAAA,EAA+B;UAA/BP,EAAA,CAAAI,UAAA,mBAAAgM,GAAA,CAAAnC,YAAA,CAA+B;UAACjK,EAAA,CAAA8E,gBAAA,SAAAsH,GAAA,CAAA7C,SAAA,CAAoB;UAACvJ,EAAA,CAAAI,UAAA,aAAAgM,GAAA,CAAA/C,QAAA,CAAqB;;;qBDlE5FrK,YAAY,EAAA2L,EAAA,CAAA2C,eAAA,EAAA3C,EAAA,CAAA4C,mBAAA,EAAA5C,EAAA,CAAA6C,qBAAA,EAAA7C,EAAA,CAAA8C,qBAAA,EACZlO,mBAAmB,EACnBL,aAAa,EAAAyL,EAAA,CAAA+C,gBAAA,EACblO,WAAW,EAAAmO,EAAA,CAAAC,oBAAA,EAAAD,EAAA,CAAAE,mBAAA,EAAAF,EAAA,CAAAG,eAAA,EAAAH,EAAA,CAAAI,kBAAA,EAAAJ,EAAA,CAAAK,OAAA,EACX5O,cAAc,EAAAuL,EAAA,CAAAsD,iBAAA,EAAAtD,EAAA,CAAAuD,iBAAA,EACd/O,cAAc,EACdO,IAAI,EACJD,KAAK,EACLE,mBAAmB,EACnBC,UAAU,EACVX,gBAAgB,EAChBY,kBAAkB,EAClBC,oBAAoB;MAAAqO,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}