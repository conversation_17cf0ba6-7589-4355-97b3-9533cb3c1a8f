{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { of } from 'rxjs';\nimport { map } from 'rxjs/operators';\nimport { NgFor, AsyncPipe } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { NbInputModule, NbAutocompleteModule, NbOptionModule } from '@nebular/theme';\nimport { ShareRequest } from 'src/app/shared/model/requests/shareRequest';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@nebular/theme\";\nimport * as i2 from \"@angular/forms\";\nconst _c0 = [\"autoInput\"];\nfunction DefaultTagAutoCompleteComponent_nb_option_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 5);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const option_r2 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", option_r2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", option_r2, \" \");\n  }\n}\nexport let DefaultTagAutoCompleteComponent = /*#__PURE__*/(() => {\n  class DefaultTagAutoCompleteComponent {\n    constructor() {\n      this.IsAutocomplete = true;\n      this.IsDefault = false;\n      this.OutputTag = new EventEmitter();\n      this.request = new ShareRequest();\n      this.options = [];\n      // autocompleteTag = [] as AutocompleteTag[];\n      this.isAutocomplete = false;\n      this.tagValue = '';\n    }\n    ngOnInit() {}\n    filter(value) {\n      const filterValue = value.toLowerCase();\n      return this.options.filter(optionValue => optionValue.toLowerCase().includes(filterValue));\n    }\n    getFilteredOptions(value) {\n      return of(value).pipe(map(filterString => this.filter(filterString)));\n    }\n    onAutocompleteChange() {\n      if (this.IsAutocomplete === false) {\n        return;\n      }\n      this.request.CName = this.tagValue;\n      if (this.isAutocomplete === false) {\n        this.isAutocomplete = true;\n        setTimeout(() => {\n          this.getAutocomplete();\n        }, 100);\n      }\n    }\n    getAutocomplete() {\n      this.request.IsDefault = this.IsDefault;\n      // this.tagService.getAutocompleteList(this.request).subscribe(res => {\n      //   this.autocompleteTag = res.Entries!;\n      //   this.options = [...this.autocompleteTag.map(x => x.CTagValue!)];\n      //   this.filteredOptions$ = this.getFilteredOptions(this.input.nativeElement.value);\n      //   const result = new AutocompleteTag();\n      //   result.CTagKeyId = 0;\n      //   result.CTagValue = this.tagValue;\n      //   this.OutputTag.emit(result);\n      //   this.isAutocomplete = false;\n      // });\n    }\n    onAutocompleteSelectionChange($event) {\n      this.filteredOptions$ = this.getFilteredOptions($event);\n      // const tag = this.autocompleteTag.find(x => x.CTagValue === $event);\n      // if (tag !== undefined) {\n      //   const result = new AutocompleteTag();\n      //   result.CTagKeyId = tag.CTagKeyId;\n      //   result.CTagValue = tag.CTagValue;\n      //   this.OutputTag.emit(result);\n      // }\n    }\n    static {\n      this.ɵfac = function DefaultTagAutoCompleteComponent_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || DefaultTagAutoCompleteComponent)();\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: DefaultTagAutoCompleteComponent,\n        selectors: [[\"ngx-default-tag-auto-complete\"]],\n        viewQuery: function DefaultTagAutoCompleteComponent_Query(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵviewQuery(_c0, 5);\n          }\n          if (rf & 2) {\n            let _t;\n            i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.input = _t.first);\n          }\n        },\n        inputs: {\n          IsAutocomplete: \"IsAutocomplete\",\n          IsDefault: \"IsDefault\"\n        },\n        outputs: {\n          OutputTag: \"OutputTag\"\n        },\n        standalone: true,\n        features: [i0.ɵɵStandaloneFeature],\n        decls: 6,\n        vars: 5,\n        consts: [[\"autoInput\", \"\"], [\"auto\", \"\"], [\"nbInput\", \"\", \"type\", \"text\", \"placeholder\", \"\\u6A19\\u7C64\\u540D\\u7A31\", 3, \"input\", \"ngModelChange\", \"nbAutocomplete\", \"ngModel\"], [3, \"selectedChange\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [3, \"value\"]],\n        template: function DefaultTagAutoCompleteComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            const _r1 = i0.ɵɵgetCurrentView();\n            i0.ɵɵelementStart(0, \"input\", 2, 0);\n            i0.ɵɵlistener(\"input\", function DefaultTagAutoCompleteComponent_Template_input_input_0_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.onAutocompleteChange());\n            });\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function DefaultTagAutoCompleteComponent_Template_input_ngModelChange_0_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.tagValue, $event) || (ctx.tagValue = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(2, \"nb-autocomplete\", 3, 1);\n            i0.ɵɵlistener(\"selectedChange\", function DefaultTagAutoCompleteComponent_Template_nb_autocomplete_selectedChange_2_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.onAutocompleteSelectionChange($event));\n            });\n            i0.ɵɵtemplate(4, DefaultTagAutoCompleteComponent_nb_option_4_Template, 2, 2, \"nb-option\", 4);\n            i0.ɵɵpipe(5, \"async\");\n            i0.ɵɵelementEnd();\n          }\n          if (rf & 2) {\n            const auto_r3 = i0.ɵɵreference(3);\n            i0.ɵɵproperty(\"nbAutocomplete\", auto_r3);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.tagValue);\n            i0.ɵɵadvance(4);\n            i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpipeBind1(5, 3, ctx.filteredOptions$));\n          }\n        },\n        dependencies: [NbInputModule, i1.NbInputDirective, NbAutocompleteModule, i1.NbAutocompleteComponent, i1.NbAutocompleteDirective, i1.NbOptionComponent, FormsModule, i2.DefaultValueAccessor, i2.NgControlStatus, i2.NgModel, NgFor, NbOptionModule, AsyncPipe]\n      });\n    }\n  }\n  return DefaultTagAutoCompleteComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}