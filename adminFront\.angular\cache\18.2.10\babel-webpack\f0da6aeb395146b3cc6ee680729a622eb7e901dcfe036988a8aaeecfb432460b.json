{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { ChangeDetectionStrategy, Component } from '@angular/core';\nlet ApproveWaiting4Component = class ApproveWaiting4Component {};\nApproveWaiting4Component = __decorate([Component({\n  selector: 'app-approve-waiting4',\n  standalone: true,\n  imports: [ApproveWaitingComponent],\n  templateUrl: './approve-waiting4.component.html',\n  styleUrl: './approve-waiting4.component.css',\n  changeDetection: ChangeDetectionStrategy.OnPush\n})], ApproveWaiting4Component);\nexport { ApproveWaiting4Component };", "map": {"version": 3, "names": ["ChangeDetectionStrategy", "Component", "ApproveWaiting4Component", "__decorate", "selector", "standalone", "imports", "ApproveWaitingComponent", "templateUrl", "styleUrl", "changeDetection", "OnPush"], "sources": ["C:\\Users\\<USER>\\Documents\\jeansalechange\\adminFront\\src\\app\\pages\\approve-waiting4\\approve-waiting4.component.ts"], "sourcesContent": ["import { ChangeDetectionStrategy, Component } from '@angular/core';\n\n@Component({\n  selector: 'app-approve-waiting4',\n  standalone: true,\n  imports: [ApproveWaitingComponent],\n  templateUrl: './approve-waiting4.component.html',\n  styleUrl: './approve-waiting4.component.css',\n  changeDetection: ChangeDetectionStrategy.OnPush,\n})\nexport class ApproveWaiting4Component { }\n"], "mappings": ";AAAA,SAASA,uBAAuB,EAAEC,SAAS,QAAQ,eAAe;AAU3D,IAAMC,wBAAwB,GAA9B,MAAMA,wBAAwB,GAAI;AAA5BA,wBAAwB,GAAAC,UAAA,EARpCF,SAAS,CAAC;EACTG,QAAQ,EAAE,sBAAsB;EAChCC,UAAU,EAAE,IAAI;EAChBC,OAAO,EAAE,CAACC,uBAAuB,CAAC;EAClCC,WAAW,EAAE,mCAAmC;EAChDC,QAAQ,EAAE,kCAAkC;EAC5CC,eAAe,EAAEV,uBAAuB,CAACW;CAC1C,CAAC,C,EACWT,wBAAwB,CAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}