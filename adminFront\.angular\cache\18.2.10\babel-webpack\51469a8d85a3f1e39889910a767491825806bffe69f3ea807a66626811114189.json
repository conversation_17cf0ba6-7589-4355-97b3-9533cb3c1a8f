{"ast": null, "code": "//! moment.js locale configuration\n//! locale : Hindi [hi]\n//! author : <PERSON><PERSON> : https://github.com/mayanks<PERSON>\n\n;\n(function (global, factory) {\n  typeof exports === 'object' && typeof module !== 'undefined' && typeof require === 'function' ? factory(require('../moment')) : typeof define === 'function' && define.amd ? define(['../moment'], factory) : factory(global.moment);\n})(this, function (moment) {\n  'use strict';\n\n  //! moment.js locale configuration\n  var symbolMap = {\n      1: '१',\n      2: '२',\n      3: '३',\n      4: '४',\n      5: '५',\n      6: '६',\n      7: '७',\n      8: '८',\n      9: '९',\n      0: '०'\n    },\n    numberMap = {\n      '१': '1',\n      '२': '2',\n      '३': '3',\n      '४': '4',\n      '५': '5',\n      '६': '6',\n      '७': '7',\n      '८': '8',\n      '९': '9',\n      '०': '0'\n    },\n    monthsParse = [/^जन/i, /^फ़र|फर/i, /^मार्च/i, /^अप्रै/i, /^मई/i, /^जून/i, /^जुल/i, /^अग/i, /^सितं|सित/i, /^अक्टू/i, /^नव|नवं/i, /^दिसं|दिस/i],\n    shortMonthsParse = [/^जन/i, /^फ़र/i, /^मार्च/i, /^अप्रै/i, /^मई/i, /^जून/i, /^जुल/i, /^अग/i, /^सित/i, /^अक्टू/i, /^नव/i, /^दिस/i];\n  var hi = moment.defineLocale('hi', {\n    months: {\n      format: 'जनवरी_फ़रवरी_मार्च_अप्रैल_मई_जून_जुलाई_अगस्त_सितम्बर_अक्टूबर_नवम्बर_दिसम्बर'.split('_'),\n      standalone: 'जनवरी_फरवरी_मार्च_अप्रैल_मई_जून_जुलाई_अगस्त_सितंबर_अक्टूबर_नवंबर_दिसंबर'.split('_')\n    },\n    monthsShort: 'जन._फ़र._मार्च_अप्रै._मई_जून_जुल._अग._सित._अक्टू._नव._दिस.'.split('_'),\n    weekdays: 'रविवार_सोमवार_मंगलवार_बुधवार_गुरूवार_शुक्रवार_शनिवार'.split('_'),\n    weekdaysShort: 'रवि_सोम_मंगल_बुध_गुरू_शुक्र_शनि'.split('_'),\n    weekdaysMin: 'र_सो_मं_बु_गु_शु_श'.split('_'),\n    longDateFormat: {\n      LT: 'A h:mm बजे',\n      LTS: 'A h:mm:ss बजे',\n      L: 'DD/MM/YYYY',\n      LL: 'D MMMM YYYY',\n      LLL: 'D MMMM YYYY, A h:mm बजे',\n      LLLL: 'dddd, D MMMM YYYY, A h:mm बजे'\n    },\n    monthsParse: monthsParse,\n    longMonthsParse: monthsParse,\n    shortMonthsParse: shortMonthsParse,\n    monthsRegex: /^(जनवरी|जन\\.?|फ़रवरी|फरवरी|फ़र\\.?|मार्च?|अप्रैल|अप्रै\\.?|मई?|जून?|जुलाई|जुल\\.?|अगस्त|अग\\.?|सितम्बर|सितंबर|सित\\.?|अक्टूबर|अक्टू\\.?|नवम्बर|नवंबर|नव\\.?|दिसम्बर|दिसंबर|दिस\\.?)/i,\n    monthsShortRegex: /^(जनवरी|जन\\.?|फ़रवरी|फरवरी|फ़र\\.?|मार्च?|अप्रैल|अप्रै\\.?|मई?|जून?|जुलाई|जुल\\.?|अगस्त|अग\\.?|सितम्बर|सितंबर|सित\\.?|अक्टूबर|अक्टू\\.?|नवम्बर|नवंबर|नव\\.?|दिसम्बर|दिसंबर|दिस\\.?)/i,\n    monthsStrictRegex: /^(जनवरी?|फ़रवरी|फरवरी?|मार्च?|अप्रैल?|मई?|जून?|जुलाई?|अगस्त?|सितम्बर|सितंबर|सित?\\.?|अक्टूबर|अक्टू\\.?|नवम्बर|नवंबर?|दिसम्बर|दिसंबर?)/i,\n    monthsShortStrictRegex: /^(जन\\.?|फ़र\\.?|मार्च?|अप्रै\\.?|मई?|जून?|जुल\\.?|अग\\.?|सित\\.?|अक्टू\\.?|नव\\.?|दिस\\.?)/i,\n    calendar: {\n      sameDay: '[आज] LT',\n      nextDay: '[कल] LT',\n      nextWeek: 'dddd, LT',\n      lastDay: '[कल] LT',\n      lastWeek: '[पिछले] dddd, LT',\n      sameElse: 'L'\n    },\n    relativeTime: {\n      future: '%s में',\n      past: '%s पहले',\n      s: 'कुछ ही क्षण',\n      ss: '%d सेकंड',\n      m: 'एक मिनट',\n      mm: '%d मिनट',\n      h: 'एक घंटा',\n      hh: '%d घंटे',\n      d: 'एक दिन',\n      dd: '%d दिन',\n      M: 'एक महीने',\n      MM: '%d महीने',\n      y: 'एक वर्ष',\n      yy: '%d वर्ष'\n    },\n    preparse: function (string) {\n      return string.replace(/[१२३४५६७८९०]/g, function (match) {\n        return numberMap[match];\n      });\n    },\n    postformat: function (string) {\n      return string.replace(/\\d/g, function (match) {\n        return symbolMap[match];\n      });\n    },\n    // Hindi notation for meridiems are quite fuzzy in practice. While there exists\n    // a rigid notion of a 'Pahar' it is not used as rigidly in modern Hindi.\n    meridiemParse: /रात|सुबह|दोपहर|शाम/,\n    meridiemHour: function (hour, meridiem) {\n      if (hour === 12) {\n        hour = 0;\n      }\n      if (meridiem === 'रात') {\n        return hour < 4 ? hour : hour + 12;\n      } else if (meridiem === 'सुबह') {\n        return hour;\n      } else if (meridiem === 'दोपहर') {\n        return hour >= 10 ? hour : hour + 12;\n      } else if (meridiem === 'शाम') {\n        return hour + 12;\n      }\n    },\n    meridiem: function (hour, minute, isLower) {\n      if (hour < 4) {\n        return 'रात';\n      } else if (hour < 10) {\n        return 'सुबह';\n      } else if (hour < 17) {\n        return 'दोपहर';\n      } else if (hour < 20) {\n        return 'शाम';\n      } else {\n        return 'रात';\n      }\n    },\n    week: {\n      dow: 0,\n      // Sunday is the first day of the week.\n      doy: 6 // The week that contains Jan 6th is the first week of the year.\n    }\n  });\n  return hi;\n});", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}