{"ast": null, "code": ";\n(function (root, factory) {\n  if (typeof exports === \"object\") {\n    // CommonJS\n    module.exports = exports = factory(require(\"./core\"));\n  } else if (typeof define === \"function\" && define.amd) {\n    // AMD\n    define([\"./core\"], factory);\n  } else {\n    // Global (browser)\n    factory(root.CryptoJS);\n  }\n})(this, function (CryptoJS) {\n  (function () {\n    // Shortcuts\n    var C = CryptoJS;\n    var C_lib = C.lib;\n    var WordArray = C_lib.WordArray;\n    var C_enc = C.enc;\n\n    /**\n     * UTF-16 BE encoding strategy.\n     */\n    var Utf16BE = C_enc.Utf16 = C_enc.Utf16BE = {\n      /**\n       * Converts a word array to a UTF-16 BE string.\n       *\n       * @param {WordArray} wordArray The word array.\n       *\n       * @return {string} The UTF-16 BE string.\n       *\n       * @static\n       *\n       * @example\n       *\n       *     var utf16String = CryptoJS.enc.Utf16.stringify(wordArray);\n       */\n      stringify: function (wordArray) {\n        // Shortcuts\n        var words = wordArray.words;\n        var sigBytes = wordArray.sigBytes;\n\n        // Convert\n        var utf16Chars = [];\n        for (var i = 0; i < sigBytes; i += 2) {\n          var codePoint = words[i >>> 2] >>> 16 - i % 4 * 8 & 0xffff;\n          utf16Chars.push(String.fromCharCode(codePoint));\n        }\n        return utf16Chars.join('');\n      },\n      /**\n       * Converts a UTF-16 BE string to a word array.\n       *\n       * @param {string} utf16Str The UTF-16 BE string.\n       *\n       * @return {WordArray} The word array.\n       *\n       * @static\n       *\n       * @example\n       *\n       *     var wordArray = CryptoJS.enc.Utf16.parse(utf16String);\n       */\n      parse: function (utf16Str) {\n        // Shortcut\n        var utf16StrLength = utf16Str.length;\n\n        // Convert\n        var words = [];\n        for (var i = 0; i < utf16StrLength; i++) {\n          words[i >>> 1] |= utf16Str.charCodeAt(i) << 16 - i % 2 * 16;\n        }\n        return WordArray.create(words, utf16StrLength * 2);\n      }\n    };\n\n    /**\n     * UTF-16 LE encoding strategy.\n     */\n    C_enc.Utf16LE = {\n      /**\n       * Converts a word array to a UTF-16 LE string.\n       *\n       * @param {WordArray} wordArray The word array.\n       *\n       * @return {string} The UTF-16 LE string.\n       *\n       * @static\n       *\n       * @example\n       *\n       *     var utf16Str = CryptoJS.enc.Utf16LE.stringify(wordArray);\n       */\n      stringify: function (wordArray) {\n        // Shortcuts\n        var words = wordArray.words;\n        var sigBytes = wordArray.sigBytes;\n\n        // Convert\n        var utf16Chars = [];\n        for (var i = 0; i < sigBytes; i += 2) {\n          var codePoint = swapEndian(words[i >>> 2] >>> 16 - i % 4 * 8 & 0xffff);\n          utf16Chars.push(String.fromCharCode(codePoint));\n        }\n        return utf16Chars.join('');\n      },\n      /**\n       * Converts a UTF-16 LE string to a word array.\n       *\n       * @param {string} utf16Str The UTF-16 LE string.\n       *\n       * @return {WordArray} The word array.\n       *\n       * @static\n       *\n       * @example\n       *\n       *     var wordArray = CryptoJS.enc.Utf16LE.parse(utf16Str);\n       */\n      parse: function (utf16Str) {\n        // Shortcut\n        var utf16StrLength = utf16Str.length;\n\n        // Convert\n        var words = [];\n        for (var i = 0; i < utf16StrLength; i++) {\n          words[i >>> 1] |= swapEndian(utf16Str.charCodeAt(i) << 16 - i % 2 * 16);\n        }\n        return WordArray.create(words, utf16StrLength * 2);\n      }\n    };\n    function swapEndian(word) {\n      return word << 8 & 0xff00ff00 | word >>> 8 & 0x00ff00ff;\n    }\n  })();\n  return CryptoJS.enc.Utf16;\n});", "map": {"version": 3, "names": ["root", "factory", "exports", "module", "require", "define", "amd", "CryptoJS", "C", "C_lib", "lib", "WordArray", "C_enc", "enc", "Utf16BE", "Utf16", "stringify", "wordArray", "words", "sigBytes", "utf16Chars", "i", "codePoint", "push", "String", "fromCharCode", "join", "parse", "utf16Str", "utf16StrLength", "length", "charCodeAt", "create", "Utf16LE", "swapEndian", "word"], "sources": ["C:/Users/<USER>/Documents/salechange-product/adminFront/node_modules/crypto-js/enc-utf16.js"], "sourcesContent": [";(function (root, factory) {\n\tif (typeof exports === \"object\") {\n\t\t// CommonJS\n\t\tmodule.exports = exports = factory(require(\"./core\"));\n\t}\n\telse if (typeof define === \"function\" && define.amd) {\n\t\t// AMD\n\t\tdefine([\"./core\"], factory);\n\t}\n\telse {\n\t\t// Global (browser)\n\t\tfactory(root.CryptoJS);\n\t}\n}(this, function (CryptoJS) {\n\n\t(function () {\n\t    // Shortcuts\n\t    var C = CryptoJS;\n\t    var C_lib = C.lib;\n\t    var WordArray = C_lib.WordArray;\n\t    var C_enc = C.enc;\n\n\t    /**\n\t     * UTF-16 BE encoding strategy.\n\t     */\n\t    var Utf16BE = C_enc.Utf16 = C_enc.Utf16BE = {\n\t        /**\n\t         * Converts a word array to a UTF-16 BE string.\n\t         *\n\t         * @param {WordArray} wordArray The word array.\n\t         *\n\t         * @return {string} The UTF-16 BE string.\n\t         *\n\t         * @static\n\t         *\n\t         * @example\n\t         *\n\t         *     var utf16String = CryptoJS.enc.Utf16.stringify(wordArray);\n\t         */\n\t        stringify: function (wordArray) {\n\t            // Shortcuts\n\t            var words = wordArray.words;\n\t            var sigBytes = wordArray.sigBytes;\n\n\t            // Convert\n\t            var utf16Chars = [];\n\t            for (var i = 0; i < sigBytes; i += 2) {\n\t                var codePoint = (words[i >>> 2] >>> (16 - (i % 4) * 8)) & 0xffff;\n\t                utf16Chars.push(String.fromCharCode(codePoint));\n\t            }\n\n\t            return utf16Chars.join('');\n\t        },\n\n\t        /**\n\t         * Converts a UTF-16 BE string to a word array.\n\t         *\n\t         * @param {string} utf16Str The UTF-16 BE string.\n\t         *\n\t         * @return {WordArray} The word array.\n\t         *\n\t         * @static\n\t         *\n\t         * @example\n\t         *\n\t         *     var wordArray = CryptoJS.enc.Utf16.parse(utf16String);\n\t         */\n\t        parse: function (utf16Str) {\n\t            // Shortcut\n\t            var utf16StrLength = utf16Str.length;\n\n\t            // Convert\n\t            var words = [];\n\t            for (var i = 0; i < utf16StrLength; i++) {\n\t                words[i >>> 1] |= utf16Str.charCodeAt(i) << (16 - (i % 2) * 16);\n\t            }\n\n\t            return WordArray.create(words, utf16StrLength * 2);\n\t        }\n\t    };\n\n\t    /**\n\t     * UTF-16 LE encoding strategy.\n\t     */\n\t    C_enc.Utf16LE = {\n\t        /**\n\t         * Converts a word array to a UTF-16 LE string.\n\t         *\n\t         * @param {WordArray} wordArray The word array.\n\t         *\n\t         * @return {string} The UTF-16 LE string.\n\t         *\n\t         * @static\n\t         *\n\t         * @example\n\t         *\n\t         *     var utf16Str = CryptoJS.enc.Utf16LE.stringify(wordArray);\n\t         */\n\t        stringify: function (wordArray) {\n\t            // Shortcuts\n\t            var words = wordArray.words;\n\t            var sigBytes = wordArray.sigBytes;\n\n\t            // Convert\n\t            var utf16Chars = [];\n\t            for (var i = 0; i < sigBytes; i += 2) {\n\t                var codePoint = swapEndian((words[i >>> 2] >>> (16 - (i % 4) * 8)) & 0xffff);\n\t                utf16Chars.push(String.fromCharCode(codePoint));\n\t            }\n\n\t            return utf16Chars.join('');\n\t        },\n\n\t        /**\n\t         * Converts a UTF-16 LE string to a word array.\n\t         *\n\t         * @param {string} utf16Str The UTF-16 LE string.\n\t         *\n\t         * @return {WordArray} The word array.\n\t         *\n\t         * @static\n\t         *\n\t         * @example\n\t         *\n\t         *     var wordArray = CryptoJS.enc.Utf16LE.parse(utf16Str);\n\t         */\n\t        parse: function (utf16Str) {\n\t            // Shortcut\n\t            var utf16StrLength = utf16Str.length;\n\n\t            // Convert\n\t            var words = [];\n\t            for (var i = 0; i < utf16StrLength; i++) {\n\t                words[i >>> 1] |= swapEndian(utf16Str.charCodeAt(i) << (16 - (i % 2) * 16));\n\t            }\n\n\t            return WordArray.create(words, utf16StrLength * 2);\n\t        }\n\t    };\n\n\t    function swapEndian(word) {\n\t        return ((word << 8) & 0xff00ff00) | ((word >>> 8) & 0x00ff00ff);\n\t    }\n\t}());\n\n\n\treturn CryptoJS.enc.Utf16;\n\n}));"], "mappings": "AAAA;AAAE,WAAUA,IAAI,EAAEC,OAAO,EAAE;EAC1B,IAAI,OAAOC,OAAO,KAAK,QAAQ,EAAE;IAChC;IACAC,MAAM,CAACD,OAAO,GAAGA,OAAO,GAAGD,OAAO,CAACG,OAAO,CAAC,QAAQ,CAAC,CAAC;EACtD,CAAC,MACI,IAAI,OAAOC,MAAM,KAAK,UAAU,IAAIA,MAAM,CAACC,GAAG,EAAE;IACpD;IACAD,MAAM,CAAC,CAAC,QAAQ,CAAC,EAAEJ,OAAO,CAAC;EAC5B,CAAC,MACI;IACJ;IACAA,OAAO,CAACD,IAAI,CAACO,QAAQ,CAAC;EACvB;AACD,CAAC,EAAC,IAAI,EAAE,UAAUA,QAAQ,EAAE;EAE1B,aAAY;IACT;IACA,IAAIC,CAAC,GAAGD,QAAQ;IAChB,IAAIE,KAAK,GAAGD,CAAC,CAACE,GAAG;IACjB,IAAIC,SAAS,GAAGF,KAAK,CAACE,SAAS;IAC/B,IAAIC,KAAK,GAAGJ,CAAC,CAACK,GAAG;;IAEjB;AACL;AACA;IACK,IAAIC,OAAO,GAAGF,KAAK,CAACG,KAAK,GAAGH,KAAK,CAACE,OAAO,GAAG;MACxC;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACSE,SAAS,EAAE,SAAAA,CAAUC,SAAS,EAAE;QAC5B;QACA,IAAIC,KAAK,GAAGD,SAAS,CAACC,KAAK;QAC3B,IAAIC,QAAQ,GAAGF,SAAS,CAACE,QAAQ;;QAEjC;QACA,IAAIC,UAAU,GAAG,EAAE;QACnB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,QAAQ,EAAEE,CAAC,IAAI,CAAC,EAAE;UAClC,IAAIC,SAAS,GAAIJ,KAAK,CAACG,CAAC,KAAK,CAAC,CAAC,KAAM,EAAE,GAAIA,CAAC,GAAG,CAAC,GAAI,CAAE,GAAI,MAAM;UAChED,UAAU,CAACG,IAAI,CAACC,MAAM,CAACC,YAAY,CAACH,SAAS,CAAC,CAAC;QACnD;QAEA,OAAOF,UAAU,CAACM,IAAI,CAAC,EAAE,CAAC;MAC9B,CAAC;MAED;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACSC,KAAK,EAAE,SAAAA,CAAUC,QAAQ,EAAE;QACvB;QACA,IAAIC,cAAc,GAAGD,QAAQ,CAACE,MAAM;;QAEpC;QACA,IAAIZ,KAAK,GAAG,EAAE;QACd,KAAK,IAAIG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGQ,cAAc,EAAER,CAAC,EAAE,EAAE;UACrCH,KAAK,CAACG,CAAC,KAAK,CAAC,CAAC,IAAIO,QAAQ,CAACG,UAAU,CAACV,CAAC,CAAC,IAAK,EAAE,GAAIA,CAAC,GAAG,CAAC,GAAI,EAAG;QACnE;QAEA,OAAOV,SAAS,CAACqB,MAAM,CAACd,KAAK,EAAEW,cAAc,GAAG,CAAC,CAAC;MACtD;IACJ,CAAC;;IAED;AACL;AACA;IACKjB,KAAK,CAACqB,OAAO,GAAG;MACZ;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACSjB,SAAS,EAAE,SAAAA,CAAUC,SAAS,EAAE;QAC5B;QACA,IAAIC,KAAK,GAAGD,SAAS,CAACC,KAAK;QAC3B,IAAIC,QAAQ,GAAGF,SAAS,CAACE,QAAQ;;QAEjC;QACA,IAAIC,UAAU,GAAG,EAAE;QACnB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,QAAQ,EAAEE,CAAC,IAAI,CAAC,EAAE;UAClC,IAAIC,SAAS,GAAGY,UAAU,CAAEhB,KAAK,CAACG,CAAC,KAAK,CAAC,CAAC,KAAM,EAAE,GAAIA,CAAC,GAAG,CAAC,GAAI,CAAE,GAAI,MAAM,CAAC;UAC5ED,UAAU,CAACG,IAAI,CAACC,MAAM,CAACC,YAAY,CAACH,SAAS,CAAC,CAAC;QACnD;QAEA,OAAOF,UAAU,CAACM,IAAI,CAAC,EAAE,CAAC;MAC9B,CAAC;MAED;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACSC,KAAK,EAAE,SAAAA,CAAUC,QAAQ,EAAE;QACvB;QACA,IAAIC,cAAc,GAAGD,QAAQ,CAACE,MAAM;;QAEpC;QACA,IAAIZ,KAAK,GAAG,EAAE;QACd,KAAK,IAAIG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGQ,cAAc,EAAER,CAAC,EAAE,EAAE;UACrCH,KAAK,CAACG,CAAC,KAAK,CAAC,CAAC,IAAIa,UAAU,CAACN,QAAQ,CAACG,UAAU,CAACV,CAAC,CAAC,IAAK,EAAE,GAAIA,CAAC,GAAG,CAAC,GAAI,EAAG,CAAC;QAC/E;QAEA,OAAOV,SAAS,CAACqB,MAAM,CAACd,KAAK,EAAEW,cAAc,GAAG,CAAC,CAAC;MACtD;IACJ,CAAC;IAED,SAASK,UAAUA,CAACC,IAAI,EAAE;MACtB,OAASA,IAAI,IAAI,CAAC,GAAI,UAAU,GAAMA,IAAI,KAAK,CAAC,GAAI,UAAW;IACnE;EACJ,CAAC,EAAC,CAAC;EAGH,OAAO5B,QAAQ,CAACM,GAAG,CAACE,KAAK;AAE1B,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}