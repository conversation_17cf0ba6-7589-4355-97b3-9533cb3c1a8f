{"ast": null, "code": "export class ProfitBarAnimationChartData {}", "map": {"version": 3, "names": ["ProfitBarAnimationChartData"], "sources": ["C:\\Users\\<USER>\\Documents\\jeansalechange\\adminFront\\src\\app\\@core\\data\\profit-bar-animation-chart.ts"], "sourcesContent": ["import { Observable } from 'rxjs';\r\n\r\nexport abstract class ProfitBarAnimationChartData {\r\n  abstract getChartData(): Observable<{ firstLine: number[]; secondLine: number[]; }>;\r\n}\r\n"], "mappings": "AAEA,OAAM,MAAgBA,2BAA2B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}