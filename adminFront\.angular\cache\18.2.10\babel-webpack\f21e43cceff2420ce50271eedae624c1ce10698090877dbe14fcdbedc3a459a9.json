{"ast": null, "code": "const {\n  isArray\n} = Array;\nexport function argsOrArgArray(args) {\n  return args.length === 1 && isArray(args[0]) ? args[0] : args;\n}", "map": {"version": 3, "names": ["isArray", "Array", "argsOrArgArray", "args", "length"], "sources": ["C:/Users/<USER>/Documents/salechange-product/adminFront/node_modules/rxjs/dist/esm/internal/util/argsOrArgArray.js"], "sourcesContent": ["const { isArray } = Array;\nexport function argsOrArgArray(args) {\n    return args.length === 1 && isArray(args[0]) ? args[0] : args;\n}\n"], "mappings": "AAAA,MAAM;EAAEA;AAAQ,CAAC,GAAGC,KAAK;AACzB,OAAO,SAASC,cAAcA,CAACC,IAAI,EAAE;EACjC,OAAOA,IAAI,CAACC,MAAM,KAAK,CAAC,IAAIJ,OAAO,CAACG,IAAI,CAAC,CAAC,CAAC,CAAC,GAAGA,IAAI,CAAC,CAAC,CAAC,GAAGA,IAAI;AACjE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}