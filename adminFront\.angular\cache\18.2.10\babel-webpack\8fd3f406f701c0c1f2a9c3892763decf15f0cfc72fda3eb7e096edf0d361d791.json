{"ast": null, "code": "import * as moment from 'moment';\nimport { BaseComponent } from '../../components/base/baseComponent';\nimport { EEvent } from 'src/app/shared/services/event.service';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/shared/helper/allowHelper\";\nimport * as i2 from \"@nebular/theme\";\nimport * as i3 from \"src/app/shared/helper/validationHelper\";\nimport * as i4 from \"src/services/api/services\";\nimport * as i5 from \"@angular/router\";\nimport * as i6 from \"src/app/shared/services/message.service\";\nimport * as i7 from \"@angular/common\";\nimport * as i8 from \"src/app/shared/services/event.service\";\nimport * as i9 from \"@angular/forms\";\nimport * as i10 from \"@ng-bootstrap/ng-bootstrap\";\nimport * as i11 from \"../../../@theme/directives/label.directive\";\nimport * as i12 from \"primeng/calendar\";\nconst _c0 = [\"fileInput\"];\nfunction CustomerChangePictureComponent_button_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 17);\n    i0.ɵɵlistener(\"click\", function CustomerChangePictureComponent_button_9_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      const dialogUploadDrawing_r4 = i0.ɵɵreference(33);\n      return i0.ɵɵresetView(ctx_r2.addNew(dialogUploadDrawing_r4));\n    });\n    i0.ɵɵtext(1, \" \\u4E0A\\u50B3\\u5716\\u9762\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CustomerChangePictureComponent_tr_25_button_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 21);\n    i0.ɵɵlistener(\"click\", function CustomerChangePictureComponent_tr_25_button_10_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const item_r6 = i0.ɵɵnextContext().$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      const dialogUploadDrawing_r4 = i0.ɵɵreference(33);\n      return i0.ɵɵresetView(ctx_r2.onEdit(dialogUploadDrawing_r4, item_r6));\n    });\n    i0.ɵɵtext(1, \"\\u6AA2\\u8996\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CustomerChangePictureComponent_tr_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\", 18)(1, \"td\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"td\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"td\", 19);\n    i0.ɵɵtemplate(10, CustomerChangePictureComponent_tr_25_button_10_Template, 2, 0, \"button\", 20);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const item_r6 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r2.formatDate(item_r6.CChangeDate));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r6.CDrawingName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r2.formatDate(item_r6.CCreateDT));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r6.CIsApprove == null ? \"\\u5F85\\u5BE9\\u6838\" : item_r6.CIsApprove ? \"\\u901A\\u904E\" : \"\\u99C1\\u56DE\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isUpdate);\n  }\n}\nfunction CustomerChangePictureComponent_ng_template_32_button_16_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 17);\n    i0.ɵɵlistener(\"click\", function CustomerChangePictureComponent_ng_template_32_button_16_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r8);\n      i0.ɵɵnextContext();\n      const inputFile_r9 = i0.ɵɵreference(18);\n      return i0.ɵɵresetView(inputFile_r9.click());\n    });\n    i0.ɵɵtext(1, \"\\u9078\\u64C7\\u6A94\\u6848\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CustomerChangePictureComponent_ng_template_32_div_21_div_1_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 43);\n    i0.ɵɵelement(1, \"img\", 44);\n    i0.ɵɵelementStart(2, \"p\", 45);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\", 46);\n    i0.ɵɵlistener(\"click\", function CustomerChangePictureComponent_ng_template_32_div_21_div_1_div_1_Template_span_click_4_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const i_r11 = i0.ɵɵnextContext().index;\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r2.removeFile(i_r11));\n    });\n    i0.ɵɵelement(5, \"i\", 47);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const file_r12 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"src\", file_r12.data, i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(file_r12.CFileName);\n  }\n}\nfunction CustomerChangePictureComponent_ng_template_32_div_21_div_1_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r13 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 48)(1, \"button\", 49);\n    i0.ɵɵlistener(\"click\", function CustomerChangePictureComponent_ng_template_32_div_21_div_1_div_2_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r13);\n      const file_r12 = i0.ɵɵnextContext().$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r2.downloadFileFromBlob(file_r12));\n    });\n    i0.ɵɵelement(2, \"i\", 50);\n    i0.ɵɵtext(3, \"\\u4E0B\\u8F09 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\", 51);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"p\", 52);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"span\", 46);\n    i0.ɵɵlistener(\"click\", function CustomerChangePictureComponent_ng_template_32_div_21_div_1_div_2_Template_span_click_8_listener() {\n      i0.ɵɵrestoreView(_r13);\n      const i_r11 = i0.ɵɵnextContext().index;\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r2.removeFile(i_r11));\n    });\n    i0.ɵɵelement(9, \"i\", 47);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const file_r12 = i0.ɵɵnextContext().$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", !ctx_r2.isCad(file_r12.CFileType) ? \"PDF\" : \"CAD\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(file_r12.CFileName);\n  }\n}\nfunction CustomerChangePictureComponent_ng_template_32_div_21_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 40);\n    i0.ɵɵtemplate(1, CustomerChangePictureComponent_ng_template_32_div_21_div_1_div_1_Template, 6, 2, \"div\", 41)(2, CustomerChangePictureComponent_ng_template_32_div_21_div_1_div_2_Template, 10, 2, \"div\", 42);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const file_r12 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isImage(file_r12.CFileType));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.isImage(file_r12.CFileType));\n  }\n}\nfunction CustomerChangePictureComponent_ng_template_32_div_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 38);\n    i0.ɵɵtemplate(1, CustomerChangePictureComponent_ng_template_32_div_21_div_1_Template, 3, 2, \"div\", 39);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.imageUrlList);\n  }\n}\nfunction CustomerChangePictureComponent_ng_template_32_div_22_div_1_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r14 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 43)(1, \"img\", 53);\n    i0.ɵɵlistener(\"click\", function CustomerChangePictureComponent_ng_template_32_div_22_div_1_div_1_Template_img_click_1_listener() {\n      i0.ɵɵrestoreView(_r14);\n      const file_r15 = i0.ɵɵnextContext().$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r2.openNewTab(file_r15.CFile));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(2, \"p\", 45);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const file_r15 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"src\", file_r15.CFile, i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(file_r15.CFileName);\n  }\n}\nfunction CustomerChangePictureComponent_ng_template_32_div_22_div_1_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r16 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 48)(1, \"button\", 49);\n    i0.ɵɵlistener(\"click\", function CustomerChangePictureComponent_ng_template_32_div_22_div_1_div_2_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r16);\n      const file_r15 = i0.ɵɵnextContext().$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r2.openNewTab(file_r15.CFile));\n    });\n    i0.ɵɵelement(2, \"i\", 50);\n    i0.ɵɵtext(3, \"\\u4E0B\\u8F09 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\", 51);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"p\", 52);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const file_r15 = i0.ɵɵnextContext().$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.isPDFString(file_r15.CFile) ? \"PDF\" : \"CAD\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(file_r15.CFileName);\n  }\n}\nfunction CustomerChangePictureComponent_ng_template_32_div_22_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 40);\n    i0.ɵɵtemplate(1, CustomerChangePictureComponent_ng_template_32_div_22_div_1_div_1_Template, 4, 2, \"div\", 41)(2, CustomerChangePictureComponent_ng_template_32_div_22_div_1_div_2_Template, 8, 2, \"div\", 42);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const file_r15 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.isPDFString(file_r15.CFile) && !ctx_r2.isCadString(file_r15.CFile));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isPDFString(file_r15.CFile) || ctx_r2.isCadString(file_r15.CFile));\n  }\n}\nfunction CustomerChangePictureComponent_ng_template_32_div_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 38);\n    i0.ɵɵtemplate(1, CustomerChangePictureComponent_ng_template_32_div_22_div_1_Template, 3, 2, \"div\", 39);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.SpecialChange.CFileRes);\n  }\n}\nfunction CustomerChangePictureComponent_ng_template_32_button_30_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r18 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 54);\n    i0.ɵɵlistener(\"click\", function CustomerChangePictureComponent_ng_template_32_button_30_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r18);\n      const ref_r17 = i0.ɵɵnextContext().dialogRef;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onSaveSpecialChange(ref_r17));\n    });\n    i0.ɵɵtext(1, \"\\u9001\\u51FA\\u5BE9\\u6838\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CustomerChangePictureComponent_ng_template_32_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-card\", 22)(1, \"nb-card-header\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"nb-card-body\", 23)(4, \"div\", 24)(5, \"label\", 25, 1);\n    i0.ɵɵtext(7, \" \\u8A0E\\u8AD6\\u65E5\\u671F \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"p-calendar\", 26);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function CustomerChangePictureComponent_ng_template_32_Template_p_calendar_ngModelChange_8_listener($event) {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.formSpecialChange.CChangeDate, $event) || (ctx_r2.formSpecialChange.CChangeDate = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"div\", 24)(10, \"label\", 27);\n    i0.ɵɵtext(11, \" \\u5716\\u9762\\u540D\\u7A31 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"input\", 28);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function CustomerChangePictureComponent_ng_template_32_Template_input_ngModelChange_12_listener($event) {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.formSpecialChange.CDrawingName, $event) || (ctx_r2.formSpecialChange.CDrawingName = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"div\", 24)(14, \"label\", 29);\n    i0.ɵɵtext(15, \" \\u9078\\u6A23\\u7D50\\u679C \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(16, CustomerChangePictureComponent_ng_template_32_button_16_Template, 2, 0, \"button\", 8);\n    i0.ɵɵelementStart(17, \"input\", 30, 2);\n    i0.ɵɵlistener(\"change\", function CustomerChangePictureComponent_ng_template_32_Template_input_change_17_listener($event) {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.detectFiles($event));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(19, \"div\", 31);\n    i0.ɵɵelement(20, \"label\", 32);\n    i0.ɵɵtemplate(21, CustomerChangePictureComponent_ng_template_32_div_21_Template, 2, 1, \"div\", 33)(22, CustomerChangePictureComponent_ng_template_32_div_22_Template, 2, 1, \"div\", 33);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"div\", 31)(24, \"label\", 34);\n    i0.ɵɵtext(25, \"\\u5BE9\\u6838\\u8AAA\\u660E\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"textarea\", 35);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function CustomerChangePictureComponent_ng_template_32_Template_textarea_ngModelChange_26_listener($event) {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.formSpecialChange.CApproveRemark, $event) || (ctx_r2.formSpecialChange.CApproveRemark = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(27, \"div\", 14)(28, \"button\", 36);\n    i0.ɵɵlistener(\"click\", function CustomerChangePictureComponent_ng_template_32_Template_button_click_28_listener() {\n      const ref_r17 = i0.ɵɵrestoreView(_r7).dialogRef;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onClose(ref_r17));\n    });\n    i0.ɵɵtext(29, \"\\u53D6\\u6D88\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(30, CustomerChangePictureComponent_ng_template_32_button_30_Template, 2, 0, \"button\", 37);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\" \\u6236\\u5225\\u7BA1\\u7406 > \\u6D3D\\u8AC7\\u7D00\\u9304\\u4E0A\\u50B3 > \", ctx_r2.house.CHousehold, \" \\u00A0 \", ctx_r2.house.CFloor, \"F \");\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"appendTo\", \"CChangeDate\")(\"iconDisplay\", \"input\")(\"showIcon\", true);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.formSpecialChange.CChangeDate);\n    i0.ɵɵproperty(\"disabled\", ctx_r2.isEdit)(\"showButtonBar\", true);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.formSpecialChange.CDrawingName);\n    i0.ɵɵproperty(\"disabled\", ctx_r2.isEdit);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", !(ctx_r2.isEdit && ctx_r2.SpecialChange.CIsApprove === null));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r2.isEdit);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.isEdit);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isEdit);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"disabled\", ctx_r2.isEdit);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.formSpecialChange.CApproveRemark);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.isEdit);\n  }\n}\nexport class CustomerChangePictureComponent extends BaseComponent {\n  constructor(_allow, dialogService, valid, _specialChangeService, _houseService, route, message, location, _eventService) {\n    super(_allow);\n    this._allow = _allow;\n    this.dialogService = dialogService;\n    this.valid = valid;\n    this._specialChangeService = _specialChangeService;\n    this._houseService = _houseService;\n    this.route = route;\n    this.message = message;\n    this.location = location;\n    this._eventService = _eventService;\n    this.imageUrlList = [];\n    this.isEdit = false;\n    this.statusOptions = [{\n      value: 0,\n      key: 'allow',\n      label: '允許'\n    }, {\n      value: 1,\n      key: 'not allowed',\n      label: '不允許'\n    }];\n    this.pageFirst = 1;\n    this.pageSize = 10;\n    this.pageIndex = 1;\n    this.totalRecords = 0;\n    this.listPictures = [];\n  }\n  ngOnInit() {\n    this.route.paramMap.subscribe(params => {\n      if (params) {\n        const idParam = params.get('id1');\n        const id = idParam ? +idParam : 0;\n        this.buildCaseId = id;\n        const idParam2 = params.get('id2');\n        const id2 = idParam2 ? +idParam2 : 0;\n        this.houseId = id2;\n        this.getListSpecialChange();\n        this.getHouseById();\n      }\n    });\n  }\n  openPdfInNewTab(data) {\n    if (data && data.CFileRes.CFile) window.open(data.CFileRes.CFile, '_blank');\n  }\n  getListSpecialChange() {\n    this._specialChangeService.apiSpecialChangeGetListSpecialChangePost$Json({\n      body: {\n        CHouseId: this.houseId,\n        PageIndex: this.pageIndex,\n        PageSize: this.pageSize\n      }\n    }).subscribe(res => {\n      if (res.TotalItems && res.Entries && res.StatusCode == 0) {\n        this.listSpecialChange = res.Entries ?? [];\n        this.totalRecords = res.TotalItems;\n      }\n    });\n  }\n  getHouseById() {\n    this._houseService.apiHouseGetHouseByIdPost$Json({\n      body: {\n        CHouseID: this.houseId\n      }\n    }).subscribe(res => {\n      if (res.TotalItems && res.Entries && res.StatusCode == 0) {\n        this.house = res.Entries;\n        this.houseTitle = `${this.house.CHousehold} ${this.house.CFloor}F`;\n      }\n    });\n  }\n  getSpecialChangeById(ref, CSpecialChangeID) {\n    this._specialChangeService.apiSpecialChangeGetSpecialChangeByIdPost$Json({\n      body: CSpecialChangeID\n    }).subscribe(res => {\n      if (res.TotalItems && res.Entries && res.StatusCode == 0) {\n        this.SpecialChange = res.Entries;\n        this.formSpecialChange = {\n          CApproveRemark: this.SpecialChange.CApproveRemark,\n          CBuildCaseID: this.buildCaseId,\n          CDrawingName: this.SpecialChange.CDrawingName,\n          CHouseID: this.houseId,\n          SpecialChangeFiles: null\n        };\n        if (this.SpecialChange.CChangeDate) {\n          this.formSpecialChange.CChangeDate = new Date(this.SpecialChange.CChangeDate);\n        }\n        this.dialogService.open(ref);\n      }\n    });\n  }\n  onSaveSpecialChange(ref) {\n    this.validation();\n    if (this.valid.errorMessages.length > 0) {\n      this.message.showErrorMSGs(this.valid.errorMessages);\n      return;\n    }\n    this._specialChangeService.apiSpecialChangeSaveSpecialChangePost$Json({\n      body: this.formatParam()\n    }).subscribe(res => {\n      if (res.StatusCode === 0) {\n        this.message.showSucessMSG(\"執行成功\");\n        this.getListSpecialChange();\n        ref.close();\n      }\n    });\n  }\n  pageChanged(newPage) {\n    this.pageIndex = newPage;\n    this.getListSpecialChange();\n  }\n  addNew(ref) {\n    this.imageUrlList = [];\n    this.isEdit = false;\n    this.formSpecialChange = {\n      CApproveRemark: '',\n      CBuildCaseID: this.buildCaseId,\n      CChangeDate: '',\n      CDrawingName: '',\n      CHouseID: this.houseId,\n      SpecialChangeFiles: null\n    };\n    this.dialogService.open(ref);\n  }\n  onEdit(ref, specialChange) {\n    this.imageUrlList = [];\n    this.isEdit = true;\n    this.getSpecialChangeById(ref, specialChange.CSpecialChangeID);\n  }\n  validation() {\n    this.valid.clear();\n    this.valid.required('[討論日期]', this.formSpecialChange.CChangeDate);\n    this.valid.required('[圖面名稱]', this.formSpecialChange.CDrawingName);\n    this.valid.required('[審核說明]', this.formSpecialChange.CApproveRemark);\n  }\n  formatDate(CChangeDate) {\n    if (CChangeDate) {\n      return moment(CChangeDate).format('YYYY-MM-DD');\n    }\n    return '';\n  }\n  deleteDataFields(array) {\n    for (const item of array) {\n      delete item.data;\n    }\n    return array;\n  }\n  formatParam() {\n    const result = {\n      ...this.formSpecialChange,\n      SpecialChangeFiles: this.imageUrlList\n    };\n    this.deleteDataFields(result.SpecialChangeFiles);\n    if (this.formSpecialChange.CChangeDate) {\n      result.CChangeDate = this.formatDate(this.formSpecialChange.CChangeDate);\n    }\n    return result;\n  }\n  onClose(ref) {\n    ref.close();\n  }\n  removeBase64Prefix(base64String) {\n    const prefixIndex = base64String.indexOf(\",\");\n    if (prefixIndex !== -1) {\n      return base64String.substring(prefixIndex + 1);\n    }\n    return base64String;\n  }\n  detectFiles(event) {\n    const files = event.target.files;\n    if (files && files.length > 0) {\n      const allowedTypes = ['image/jpeg', 'image/jpg', 'application/pdf', 'application/acad', 'application/x-autocad', 'image/vnd.dwg', 'image/x-dwg'];\n      const fileRegex = /pdf|jpg|jpeg|png|dwg|dxf/i;\n      // 如果圖面名稱為空且是第一次選擇檔案，自動填入第一個檔案的檔名（去除副檔名）\n      if (!this.formSpecialChange.CDrawingName && files.length > 0) {\n        const firstFile = files[0];\n        const fileName = firstFile.name;\n        const fileNameWithoutExtension = fileName.substring(0, fileName.lastIndexOf('.')) || fileName;\n        this.formSpecialChange.CDrawingName = fileNameWithoutExtension;\n      }\n      for (let i = 0; i < files.length; i++) {\n        const file = files[i];\n        if (!fileRegex.test(file.name)) {\n          this.message.showErrorMSG('檔案格式錯誤，僅限pdf、圖片檔、CAD檔案（dwg, dxf）');\n          continue;\n        }\n        if (allowedTypes.includes(file.type) || fileRegex.test(file.name)) {\n          const reader = new FileReader();\n          reader.onload = e => {\n            let fileType;\n            if (file.type.startsWith('image/')) {\n              fileType = 2; // 圖片\n            } else if (file.name.toLowerCase().includes('.dwg') || file.name.toLowerCase().includes('.dxf')) {\n              fileType = 3; // CAD檔案\n            } else {\n              fileType = 1; // PDF\n            }\n            this.imageUrlList.push({\n              data: e.target.result,\n              CFileBlood: this.removeBase64Prefix(e.target.result),\n              CFileName: file.name,\n              CFileType: fileType\n            });\n            if (this.imageUrlList.length === files.length) {\n              console.log('this.imageUrlList', this.imageUrlList);\n              if (this.fileInput) {\n                this.fileInput.nativeElement.value = null;\n              }\n            }\n          };\n          reader.readAsDataURL(file);\n        }\n      }\n    }\n  }\n  isPDFString(str) {\n    if (str) {\n      return str.toLowerCase().endsWith(\".pdf\");\n    }\n    return false;\n  }\n  isCadString(str) {\n    if (str) {\n      const lowerStr = str.toLowerCase();\n      return lowerStr.endsWith(\".dwg\") || lowerStr.endsWith(\".dxf\");\n    }\n    return false;\n  }\n  isImage(fileType) {\n    return fileType === 2;\n  }\n  isCad(fileType) {\n    return fileType === 3;\n  }\n  isPdf(extension) {\n    return extension.toLowerCase() === 'pdf';\n  }\n  removeFile(index) {\n    this.imageUrlList.splice(index, 1);\n  }\n  removeImage(pictureId) {\n    this.listPictures = this.listPictures.filter(x => x.id != pictureId);\n  }\n  uploadImage(ref) {}\n  renameFile(event, index) {\n    var blob = this.listPictures[index].CFile.slice(0, this.listPictures[index].CFile.size, this.listPictures[index].CFile.type);\n    var newFile = new File([blob], `${event.target.value + '.' + this.listPictures[index].extension}`, {\n      type: this.listPictures[index].CFile.type\n    });\n    this.listPictures[index].CFile = newFile;\n  }\n  goBack() {\n    this._eventService.push({\n      action: \"GET_BUILDCASE\" /* EEvent.GET_BUILDCASE */,\n      payload: this.buildCaseId\n    });\n    this.location.back();\n  }\n  openNewTab(url) {\n    if (url) window.open(url, \"_blank\");\n  }\n  downloadFileFromBlob(file) {\n    if (file && file.data && file.CFileName) {\n      // 將 base64 轉換為 blob\n      const byteCharacters = atob(file.CFileBlood || file.data.split(',')[1]);\n      const byteNumbers = new Array(byteCharacters.length);\n      for (let i = 0; i < byteCharacters.length; i++) {\n        byteNumbers[i] = byteCharacters.charCodeAt(i);\n      }\n      const byteArray = new Uint8Array(byteNumbers);\n      // 設定 MIME 類型\n      let mimeType = 'application/octet-stream';\n      if (file.CFileType === 1) {\n        mimeType = 'application/pdf';\n      } else if (file.CFileType === 3) {\n        mimeType = 'application/x-autocad';\n      }\n      const blob = new Blob([byteArray], {\n        type: mimeType\n      });\n      // 建立下載連結\n      const url = window.URL.createObjectURL(blob);\n      const link = document.createElement('a');\n      link.href = url;\n      link.download = file.CFileName;\n      document.body.appendChild(link);\n      link.click();\n      document.body.removeChild(link);\n      window.URL.revokeObjectURL(url);\n    }\n  }\n  static {\n    this.ɵfac = function CustomerChangePictureComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || CustomerChangePictureComponent)(i0.ɵɵdirectiveInject(i1.AllowHelper), i0.ɵɵdirectiveInject(i2.NbDialogService), i0.ɵɵdirectiveInject(i3.ValidationHelper), i0.ɵɵdirectiveInject(i4.SpecialChangeService), i0.ɵɵdirectiveInject(i4.HouseService), i0.ɵɵdirectiveInject(i5.ActivatedRoute), i0.ɵɵdirectiveInject(i6.MessageService), i0.ɵɵdirectiveInject(i7.Location), i0.ɵɵdirectiveInject(i8.EventService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: CustomerChangePictureComponent,\n      selectors: [[\"ngx-customer-change-picture\"]],\n      viewQuery: function CustomerChangePictureComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.fileInput = _t.first);\n        }\n      },\n      features: [i0.ɵɵInheritDefinitionFeature],\n      decls: 34,\n      vars: 6,\n      consts: [[\"dialogUploadDrawing\", \"\"], [\"CChangeDate\", \"\"], [\"inputFile\", \"\"], [\"accent\", \"success\"], [1, \"font-bold\", \"text-[#818181]\"], [1, \"d-flex\", \"flex-wrap\"], [1, \"col-md-12\"], [1, \"d-flex\", \"justify-content-end\", \"w-full\"], [\"class\", \"btn btn-info\", 3, \"click\", 4, \"ngIf\"], [1, \"table-responsive\", \"mt-4\"], [1, \"table\", \"table-striped\", \"border\", 2, \"min-width\", \"1000px\", \"background-color\", \"#f3f3f3\"], [1, \"text-center\", 2, \"background-color\", \"#27ae60\", \"color\", \"white\"], [\"scope\", \"col\", 1, \"col-1\"], [\"class\", \"text-center\", 4, \"ngFor\", \"ngForOf\"], [1, \"d-flex\", \"justify-content-center\"], [\"aria-label\", \"Pagination\", 3, \"pageChange\", \"page\", \"pageSize\", \"collectionSize\"], [1, \"btn\", \"btn-secondary\", \"btn-sm\", 3, \"click\"], [1, \"btn\", \"btn-info\", 3, \"click\"], [1, \"text-center\"], [1, \"text-center\", \"w-32\"], [\"class\", \"btn btn-outline-primary btn-sm m-1\", 3, \"click\", 4, \"ngIf\"], [1, \"btn\", \"btn-outline-primary\", \"btn-sm\", \"m-1\", 3, \"click\"], [2, \"min-width\", \"600px\", \"max-height\", \"95vh\"], [1, \"px-4\"], [1, \"form-group\"], [\"for\", \"CChangeDate\", \"baseLabel\", \"\", 1, \"required-field\", \"mr-4\", 2, \"min-width\", \"75px\"], [\"placeholder\", \"\\u5E74/\\u6708/\\u65E5\", \"inputId\", \"icondisplay\", \"dateFormat\", \"yy/mm/dd\", 1, \"!w-[400px]\", 3, \"ngModelChange\", \"appendTo\", \"iconDisplay\", \"showIcon\", \"ngModel\", \"disabled\", \"showButtonBar\"], [\"for\", \"cDrawingName\", \"baseLabel\", \"\", 1, \"required-field\", \"mr-4\", 2, \"min-width\", \"75px\"], [\"type\", \"text\", \"nbInput\", \"\", \"placeholder\", \"\\u5716\\u9762\\u540D\\u7A31\", 1, \"w-full\", 3, \"ngModelChange\", \"ngModel\", \"disabled\"], [\"for\", \"cIsEnable\", \"baseLabel\", \"\", 1, \"mr-4\", 2, \"min-width\", \"75px\"], [\"type\", \"file\", \"id\", \"fileInput\", \"accept\", \"image/jpeg, image/jpg, application/pdf, .dwg, .dxf\", \"multiple\", \"\", 1, \"hidden\", 3, \"change\", \"disabled\"], [1, \"form-group\", \"d-flex\", \"align-items-center\"], [\"baseLabel\", \"\", 1, \"align-self-start\", \"mr-4\", 2, \"min-width\", \"75px\"], [\"class\", \"flex flex-wrap mt-2\", 4, \"ngIf\"], [\"for\", \"cApproveRemark\", \"baseLabel\", \"\", 1, \"required-field\", \"align-self-start\", \"mr-4\", 2, \"min-width\", \"75px\"], [\"name\", \"remark\", \"id\", \"cApproveRemark\", \"rows\", \"5\", \"nbInput\", \"\", 1, \"w-full\", 2, \"resize\", \"none\", 3, \"ngModelChange\", \"disabled\", \"ngModel\"], [1, \"btn\", \"btn-outline-secondary\", \"m-2\", 3, \"click\"], [\"class\", \"btn btn-success m-2\", 3, \"click\", 4, \"ngIf\"], [1, \"flex\", \"flex-wrap\", \"mt-2\"], [\"class\", \"relative mr-2 mb-2\", 4, \"ngFor\", \"ngForOf\"], [1, \"relative\", \"mr-2\", \"mb-2\"], [\"class\", \"relative w-24 h-24 border\", 4, \"ngIf\"], [\"class\", \"flex flex-col items-center p-2 border\", 4, \"ngIf\"], [1, \"relative\", \"w-24\", \"h-24\", \"border\"], [1, \"w-full\", \"h-full\", \"object-contain\", \"cursor-pointer\", 3, \"src\"], [1, \"absolute\", \"-bottom-4\", \"left-0\", \"w-full\", \"text-xs\", \"truncate\", \"px-1\", \"text-center\"], [1, \"absolute\", \"top-0\", \"right-0\", \"cursor-pointer\", \"bg-white\", \"rounded-full\", 3, \"click\"], [1, \"fa\", \"fa-times\", \"text-red-600\"], [1, \"flex\", \"flex-col\", \"items-center\", \"p-2\", \"border\"], [1, \"btn\", \"btn-sm\", \"btn-primary\", \"mb-1\", 3, \"click\"], [1, \"fa\", \"fa-download\", \"mr-1\"], [1, \"text-xs\", \"mb-1\"], [1, \"text-xs\", \"truncate\", \"text-center\", \"max-w-20\"], [1, \"w-full\", \"h-full\", \"object-contain\", \"cursor-pointer\", 3, \"click\", \"src\"], [1, \"btn\", \"btn-success\", \"m-2\", 3, \"click\"]],\n      template: function CustomerChangePictureComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"nb-card\", 3)(1, \"nb-card-header\");\n          i0.ɵɵtext(2);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(3, \"nb-card-body\")(4, \"h1\", 4);\n          i0.ɵɵtext(5, \"\\u60A8\\u53EF\\u8207\\u6B64\\u4E0A\\u50B3\\u8207\\u8A72\\u6236\\u5225\\u5BA2\\u6236\\u8A0E\\u8AD6\\u7684\\u5BA2\\u6236\\u5716\\u9762\\uFF0C\\u5BE9\\u6838\\u901A\\u904E\\u5F8C\\u5BA2\\u6236\\u5C31\\u53EF\\u4EE5\\u5728\\u524D\\u53F0\\u6AA2\\u8996\\u8A72\\u5716\\u9762\\u3002\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(6, \"div\", 5)(7, \"div\", 6)(8, \"div\", 7);\n          i0.ɵɵtemplate(9, CustomerChangePictureComponent_button_9_Template, 2, 0, \"button\", 8);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(10, \"div\", 9)(11, \"table\", 10)(12, \"thead\")(13, \"tr\", 11)(14, \"th\", 12);\n          i0.ɵɵtext(15, \"\\u8A0E\\u8AD6\\u65E5\\u671F\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(16, \"th\", 12);\n          i0.ɵɵtext(17, \"\\u5716\\u9762\\u540D\\u7A31\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(18, \"th\", 12);\n          i0.ɵɵtext(19, \"\\u4E0A\\u50B3\\u65E5\\u671F\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(20, \"th\", 12);\n          i0.ɵɵtext(21, \"\\u5BE9\\u6838\\u72C0\\u614B\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(22, \"th\", 12);\n          i0.ɵɵtext(23, \"\\u64CD\\u4F5C\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(24, \"tbody\");\n          i0.ɵɵtemplate(25, CustomerChangePictureComponent_tr_25_Template, 11, 5, \"tr\", 13);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(26, \"nb-card-footer\", 14)(27, \"ngb-pagination\", 15);\n          i0.ɵɵtwoWayListener(\"pageChange\", function CustomerChangePictureComponent_Template_ngb_pagination_pageChange_27_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.pageIndex, $event) || (ctx.pageIndex = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵlistener(\"pageChange\", function CustomerChangePictureComponent_Template_ngb_pagination_pageChange_27_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.pageChanged($event));\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(28, \"nb-card-footer\")(29, \"div\", 14)(30, \"button\", 16);\n          i0.ɵɵlistener(\"click\", function CustomerChangePictureComponent_Template_button_click_30_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.goBack());\n          });\n          i0.ɵɵtext(31, \" \\u8FD4\\u56DE\\u4E0A\\u4E00\\u9801 \");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵtemplate(32, CustomerChangePictureComponent_ng_template_32_Template, 31, 17, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor);\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate1(\" \\u6236\\u5225\\u7BA1\\u7406 > \\u6D3D\\u8AC7\\u7D00\\u9304\\u4E0A\\u50B3 > \", ctx.houseTitle, \" \");\n          i0.ɵɵadvance(7);\n          i0.ɵɵproperty(\"ngIf\", ctx.isCreate);\n          i0.ɵɵadvance(16);\n          i0.ɵɵproperty(\"ngForOf\", ctx.listSpecialChange);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtwoWayProperty(\"page\", ctx.pageIndex);\n          i0.ɵɵproperty(\"pageSize\", ctx.pageSize)(\"collectionSize\", ctx.totalRecords);\n        }\n      },\n      dependencies: [i7.NgForOf, i7.NgIf, i9.DefaultValueAccessor, i9.NgControlStatus, i9.NgModel, i2.NbCardComponent, i2.NbCardBodyComponent, i2.NbCardFooterComponent, i2.NbCardHeaderComponent, i2.NbInputDirective, i10.NgbPagination, i11.BaseLabelDirective, i12.Calendar],\n      styles: [\"#icondisplay {\\n  width: 318px;\\n}\\n\\n  [id^=pn_id_] {\\n  z-index: 10;\\n}\\n/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImN1c3RvbWVyLWNoYW5nZS1waWN0dXJlLmNvbXBvbmVudC5zY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUFBO0VBQ0ksWUFBQTtBQUNKOztBQUVBO0VBQ0ksV0FBQTtBQUNKIiwiZmlsZSI6ImN1c3RvbWVyLWNoYW5nZS1waWN0dXJlLmNvbXBvbmVudC5zY3NzIiwic291cmNlc0NvbnRlbnQiOlsiOjpuZy1kZWVwICNpY29uZGlzcGxheSB7XHJcbiAgICB3aWR0aDogMzE4cHg7XHJcbn1cclxuXHJcbjo6bmctZGVlcCBbaWRePVwicG5faWRfXCJdIHtcclxuICAgIHotaW5kZXg6IDEwO1xyXG59XHJcbiJdfQ== */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvcGFnZXMvaG91c2Vob2xkLW1hbmFnZW1lbnQvY3VzdG9tZXItY2hhbmdlLXBpY3R1cmUvY3VzdG9tZXItY2hhbmdlLXBpY3R1cmUuY29tcG9uZW50LnNjc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUE7RUFDSSxZQUFBO0FBQ0o7O0FBRUE7RUFDSSxXQUFBO0FBQ0o7QUFDQSw0ZEFBNGQiLCJzb3VyY2VzQ29udGVudCI6WyI6Om5nLWRlZXAgI2ljb25kaXNwbGF5IHtcclxuICAgIHdpZHRoOiAzMThweDtcclxufVxyXG5cclxuOjpuZy1kZWVwIFtpZF49XCJwbl9pZF9cIl0ge1xyXG4gICAgei1pbmRleDogMTA7XHJcbn1cclxuIl0sInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["moment", "BaseComponent", "EEvent", "i0", "ɵɵelementStart", "ɵɵlistener", "CustomerChangePictureComponent_button_9_Template_button_click_0_listener", "ɵɵrestoreView", "_r2", "ctx_r2", "ɵɵnextContext", "dialogUploadDrawing_r4", "ɵɵreference", "ɵɵresetView", "addNew", "ɵɵtext", "ɵɵelementEnd", "CustomerChangePictureComponent_tr_25_button_10_Template_button_click_0_listener", "_r5", "item_r6", "$implicit", "onEdit", "ɵɵtemplate", "CustomerChangePictureComponent_tr_25_button_10_Template", "ɵɵadvance", "ɵɵtextInterpolate", "formatDate", "CChangeDate", "CDrawingName", "CCreateDT", "CIsApprove", "ɵɵproperty", "isUpdate", "CustomerChangePictureComponent_ng_template_32_button_16_Template_button_click_0_listener", "_r8", "inputFile_r9", "click", "ɵɵelement", "CustomerChangePictureComponent_ng_template_32_div_21_div_1_div_1_Template_span_click_4_listener", "_r10", "i_r11", "index", "removeFile", "file_r12", "data", "ɵɵsanitizeUrl", "CFileName", "CustomerChangePictureComponent_ng_template_32_div_21_div_1_div_2_Template_button_click_1_listener", "_r13", "downloadFileFromBlob", "CustomerChangePictureComponent_ng_template_32_div_21_div_1_div_2_Template_span_click_8_listener", "ɵɵtextInterpolate1", "isCad", "CFileType", "CustomerChangePictureComponent_ng_template_32_div_21_div_1_div_1_Template", "CustomerChangePictureComponent_ng_template_32_div_21_div_1_div_2_Template", "isImage", "CustomerChangePictureComponent_ng_template_32_div_21_div_1_Template", "imageUrlList", "CustomerChangePictureComponent_ng_template_32_div_22_div_1_div_1_Template_img_click_1_listener", "_r14", "file_r15", "openNewTab", "CFile", "CustomerChangePictureComponent_ng_template_32_div_22_div_1_div_2_Template_button_click_1_listener", "_r16", "isPDFString", "CustomerChangePictureComponent_ng_template_32_div_22_div_1_div_1_Template", "CustomerChangePictureComponent_ng_template_32_div_22_div_1_div_2_Template", "isCadString", "CustomerChangePictureComponent_ng_template_32_div_22_div_1_Template", "SpecialChange", "CFileRes", "CustomerChangePictureComponent_ng_template_32_button_30_Template_button_click_0_listener", "_r18", "ref_r17", "dialogRef", "onSaveSpecialChange", "ɵɵtwoWayListener", "CustomerChangePictureComponent_ng_template_32_Template_p_calendar_ngModelChange_8_listener", "$event", "_r7", "ɵɵtwoWayBindingSet", "formSpecialChange", "CustomerChangePictureComponent_ng_template_32_Template_input_ngModelChange_12_listener", "CustomerChangePictureComponent_ng_template_32_button_16_Template", "CustomerChangePictureComponent_ng_template_32_Template_input_change_17_listener", "detectFiles", "CustomerChangePictureComponent_ng_template_32_div_21_Template", "CustomerChangePictureComponent_ng_template_32_div_22_Template", "CustomerChangePictureComponent_ng_template_32_Template_textarea_ngModelChange_26_listener", "CApproveRemark", "CustomerChangePictureComponent_ng_template_32_Template_button_click_28_listener", "onClose", "CustomerChangePictureComponent_ng_template_32_button_30_Template", "ɵɵtextInterpolate2", "house", "CHousehold", "CFloor", "ɵɵtwoWayProperty", "isEdit", "CustomerChangePictureComponent", "constructor", "_allow", "dialogService", "valid", "_specialChangeService", "_houseService", "route", "message", "location", "_eventService", "statusOptions", "value", "key", "label", "pageFirst", "pageSize", "pageIndex", "totalRecords", "listPictures", "ngOnInit", "paramMap", "subscribe", "params", "idParam", "get", "id", "buildCaseId", "idParam2", "id2", "houseId", "getListSpecialChange", "getHouseById", "openPdfInNewTab", "window", "open", "apiSpecialChangeGetListSpecialChangePost$Json", "body", "CHouseId", "PageIndex", "PageSize", "res", "TotalItems", "Entries", "StatusCode", "listSpecialChange", "apiHouseGetHouseByIdPost$Json", "CHouseID", "houseTitle", "getSpecialChangeById", "ref", "CSpecialChangeID", "apiSpecialChangeGetSpecialChangeByIdPost$Json", "CBuildCaseID", "SpecialChangeFiles", "Date", "validation", "errorMessages", "length", "showErrorMSGs", "apiSpecialChangeSaveSpecialChangePost$Json", "formatParam", "showSucessMSG", "close", "pageChanged", "newPage", "specialChange", "clear", "required", "format", "deleteDataFields", "array", "item", "result", "removeBase64Prefix", "base64String", "prefixIndex", "indexOf", "substring", "event", "files", "target", "allowedTypes", "fileRegex", "firstFile", "fileName", "name", "fileNameWithoutExtension", "lastIndexOf", "i", "file", "test", "showErrorMSG", "includes", "type", "reader", "FileReader", "onload", "e", "fileType", "startsWith", "toLowerCase", "push", "CFileBlood", "console", "log", "fileInput", "nativeElement", "readAsDataURL", "str", "endsWith", "lowerStr", "isPdf", "extension", "splice", "removeImage", "pictureId", "filter", "x", "uploadImage", "renameFile", "blob", "slice", "size", "newFile", "File", "goBack", "action", "payload", "back", "url", "byteCharacters", "atob", "split", "byteNumbers", "Array", "charCodeAt", "byteArray", "Uint8Array", "mimeType", "Blob", "URL", "createObjectURL", "link", "document", "createElement", "href", "download", "append<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "revokeObjectURL", "ɵɵdirectiveInject", "i1", "AllowHelper", "i2", "NbDialogService", "i3", "ValidationHelper", "i4", "SpecialChangeService", "HouseService", "i5", "ActivatedRoute", "i6", "MessageService", "i7", "Location", "i8", "EventService", "selectors", "viewQuery", "CustomerChangePictureComponent_Query", "rf", "ctx", "CustomerChangePictureComponent_button_9_Template", "CustomerChangePictureComponent_tr_25_Template", "CustomerChangePictureComponent_Template_ngb_pagination_pageChange_27_listener", "_r1", "CustomerChangePictureComponent_Template_button_click_30_listener", "CustomerChangePictureComponent_ng_template_32_Template", "ɵɵtemplateRefExtractor", "isCreate"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\household-management\\customer-change-picture\\customer-change-picture.component.ts", "C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\household-management\\customer-change-picture\\customer-change-picture.component.html"], "sourcesContent": ["import { Component, ElementRef, OnInit, ViewChild } from '@angular/core';\r\nimport { Location } from '@angular/common';\r\nimport { NbDialogService } from '@nebular/theme';\r\nimport { ValidationHelper } from 'src/app/shared/helper/validationHelper';\r\nimport { HouseService, SpecialChangeService } from 'src/services/api/services';\r\nimport { TblHouse, SpecialChangeRes } from 'src/services/api/models';\r\nimport { AllowHelper } from 'src/app/shared/helper/allowHelper';\r\nimport { ActivatedRoute } from '@angular/router';\r\nimport { MessageService } from 'src/app/shared/services/message.service';\r\nimport * as moment from 'moment';\r\nimport { BaseComponent } from '../../components/base/baseComponent';\r\nimport { EventService, EEvent } from 'src/app/shared/services/event.service';\r\n\r\nexport interface selectItem {\r\n  label: string,\r\n  value: number,\r\n  key?: string\r\n}\r\n\r\n@Component({\r\n  selector: 'ngx-customer-change-picture',\r\n  templateUrl: './customer-change-picture.component.html',\r\n  styleUrls: ['./customer-change-picture.component.scss'],\r\n})\r\n\r\nexport class CustomerChangePictureComponent extends BaseComponent implements OnInit {\r\n  constructor(\r\n    private _allow: AllowHelper,\r\n    private dialogService: NbDialogService,\r\n    private valid: ValidationHelper,\r\n    private _specialChangeService: SpecialChangeService,\r\n    private _houseService: HouseService,\r\n\r\n    private route: ActivatedRoute,\r\n    private message: MessageService,\r\n    private location: Location,\r\n    private _eventService: EventService\r\n  ) { super(_allow) }\r\n\r\n  @ViewChild('fileInput') fileInput: ElementRef;\r\n  imageUrlList: any[] = [];\r\n  isEdit = false\r\n  statusOptions: selectItem[] = [\r\n    {\r\n      value: 0,\r\n      key: 'allow',\r\n      label: '允許',\r\n    },\r\n    {\r\n      value: 1,\r\n      key: 'not allowed',\r\n      label: '不允許',\r\n    }\r\n  ]\r\n\r\n  override pageFirst = 1;\r\n  override pageSize = 10;\r\n  override pageIndex = 1;\r\n  override totalRecords = 0;\r\n\r\n  selectedBuildCase: selectItem\r\n\r\n  buildCaseId: number\r\n  houseId: number\r\n  house: TblHouse\r\n  houseTitle: string\r\n\r\n  override ngOnInit(): void {\r\n\r\n    this.route.paramMap.subscribe(params => {\r\n      if (params) {\r\n        const idParam = params.get('id1');\r\n        const id = idParam ? +idParam : 0;\r\n        this.buildCaseId = id\r\n        const idParam2 = params.get('id2');\r\n        const id2 = idParam2 ? +idParam2 : 0;\r\n        this.houseId = id2\r\n        this.getListSpecialChange()\r\n        this.getHouseById()\r\n      }\r\n    });\r\n  }\r\n\r\n\r\n  openPdfInNewTab(data?: any) {\r\n    if (data && data.CFileRes.CFile) window.open(data.CFileRes.CFile, '_blank');\r\n  }\r\n\r\n\r\n  listSpecialChange: any[]\r\n\r\n  getListSpecialChange() {\r\n    this._specialChangeService.apiSpecialChangeGetListSpecialChangePost$Json({\r\n      body: {\r\n        CHouseId: this.houseId,\r\n        PageIndex: this.pageIndex,\r\n        PageSize: this.pageSize\r\n      }\r\n    }).subscribe(res => {\r\n      if (res.TotalItems && res.Entries && res.StatusCode == 0) {\r\n        this.listSpecialChange = res.Entries! ?? []\r\n        this.totalRecords = res.TotalItems;\r\n      }\r\n    })\r\n  }\r\n\r\n  getHouseById() {\r\n    this._houseService.apiHouseGetHouseByIdPost$Json({\r\n      body: {\r\n        CHouseID: this.houseId\r\n      }\r\n    }).subscribe(res => {\r\n      if (res.TotalItems && res.Entries && res.StatusCode == 0) {\r\n        this.house = res.Entries\r\n        this.houseTitle = `${this.house.CHousehold} ${this.house.CFloor}F`\r\n      }\r\n    })\r\n  }\r\n\r\n  SpecialChange: SpecialChangeRes\r\n  fileUrl: any\r\n  getSpecialChangeById(ref: any, CSpecialChangeID: any) {\r\n    this._specialChangeService.apiSpecialChangeGetSpecialChangeByIdPost$Json({ body: CSpecialChangeID }).subscribe(res => {\r\n      if (res.TotalItems && res.Entries && res.StatusCode == 0) {\r\n        this.SpecialChange = res.Entries\r\n        this.formSpecialChange = {\r\n          CApproveRemark: this.SpecialChange.CApproveRemark,\r\n          CBuildCaseID: this.buildCaseId,\r\n          CDrawingName: this.SpecialChange.CDrawingName,\r\n          CHouseID: this.houseId,\r\n          SpecialChangeFiles: null\r\n        }\r\n        if (this.SpecialChange.CChangeDate) {\r\n          this.formSpecialChange.CChangeDate = new Date(this.SpecialChange.CChangeDate);\r\n        }\r\n        this.dialogService.open(ref)\r\n      }\r\n    })\r\n  }\r\n\r\n  formSpecialChange: any\r\n\r\n  onSaveSpecialChange(ref: any) {\r\n    this.validation()\r\n    if (this.valid.errorMessages.length > 0) {\r\n      this.message.showErrorMSGs(this.valid.errorMessages);\r\n      return\r\n    }\r\n\r\n    this._specialChangeService.apiSpecialChangeSaveSpecialChangePost$Json({ body: this.formatParam() }).subscribe(res => {\r\n      if (res.StatusCode === 0) {\r\n        this.message.showSucessMSG(\"執行成功\");\r\n        this.getListSpecialChange()\r\n        ref.close();\r\n      }\r\n    })\r\n  }\r\n\r\n  pageChanged(newPage: number) {\r\n    this.pageIndex = newPage;\r\n    this.getListSpecialChange();\r\n  }\r\n\r\n\r\n  addNew(ref: any) {\r\n    this.imageUrlList = [];\r\n    this.isEdit = false\r\n    this.formSpecialChange = {\r\n      CApproveRemark: '',\r\n      CBuildCaseID: this.buildCaseId,\r\n      CChangeDate: '',\r\n      CDrawingName: '',\r\n      CHouseID: this.houseId,\r\n      SpecialChangeFiles: null\r\n    }\r\n    this.dialogService.open(ref)\r\n  }\r\n\r\n  onEdit(ref: any, specialChange: any) {\r\n    this.imageUrlList = [];\r\n    this.isEdit = true\r\n    this.getSpecialChangeById(ref, specialChange.CSpecialChangeID)\r\n  }\r\n\r\n  validation() {\r\n    this.valid.clear();\r\n    this.valid.required('[討論日期]', this.formSpecialChange.CChangeDate)\r\n    this.valid.required('[圖面名稱]', this.formSpecialChange.CDrawingName)\r\n    this.valid.required('[審核說明]', this.formSpecialChange.CApproveRemark)\r\n  }\r\n\r\n  formatDate(CChangeDate: string): string {\r\n    if (CChangeDate) {\r\n      return moment(CChangeDate).format('YYYY-MM-DD');\r\n    }\r\n    return ''\r\n  }\r\n\r\n  deleteDataFields(array: any[]) {\r\n    for (const item of array) {\r\n      delete item.data;\r\n    }\r\n    return array;\r\n  }\r\n\r\n  formatParam() {\r\n    const result = {\r\n      ...this.formSpecialChange,\r\n      SpecialChangeFiles: this.imageUrlList\r\n    }\r\n    this.deleteDataFields(result.SpecialChangeFiles)\r\n\r\n    if (this.formSpecialChange.CChangeDate) {\r\n      result.CChangeDate = this.formatDate(this.formSpecialChange.CChangeDate)\r\n    }\r\n    return result\r\n  }\r\n\r\n  onClose(ref: any) {\r\n    ref.close();\r\n  }\r\n\r\n  removeBase64Prefix(base64String: any) {\r\n    const prefixIndex = base64String.indexOf(\",\");\r\n    if (prefixIndex !== -1) {\r\n      return base64String.substring(prefixIndex + 1);\r\n    }\r\n    return base64String;\r\n  } detectFiles(event: any) {\r\n    const files: FileList = event.target.files;\r\n    if (files && files.length > 0) {\r\n      const allowedTypes = ['image/jpeg', 'image/jpg', 'application/pdf', 'application/acad', 'application/x-autocad', 'image/vnd.dwg', 'image/x-dwg'];\r\n      const fileRegex = /pdf|jpg|jpeg|png|dwg|dxf/i;\r\n\r\n      // 如果圖面名稱為空且是第一次選擇檔案，自動填入第一個檔案的檔名（去除副檔名）\r\n      if (!this.formSpecialChange.CDrawingName && files.length > 0) {\r\n        const firstFile = files[0];\r\n        const fileName = firstFile.name;\r\n        const fileNameWithoutExtension = fileName.substring(0, fileName.lastIndexOf('.')) || fileName;\r\n        this.formSpecialChange.CDrawingName = fileNameWithoutExtension;\r\n      }\r\n\r\n      for (let i = 0; i < files.length; i++) {\r\n        const file = files[i];\r\n        if (!fileRegex.test(file.name)) {\r\n          this.message.showErrorMSG('檔案格式錯誤，僅限pdf、圖片檔、CAD檔案（dwg, dxf）');\r\n          continue;\r\n        }\r\n        if (allowedTypes.includes(file.type) || fileRegex.test(file.name)) {\r\n          const reader = new FileReader();\r\n\r\n          reader.onload = (e: any) => {\r\n            let fileType: number;\r\n            if (file.type.startsWith('image/')) {\r\n              fileType = 2; // 圖片\r\n            } else if (file.name.toLowerCase().includes('.dwg') || file.name.toLowerCase().includes('.dxf')) {\r\n              fileType = 3; // CAD檔案\r\n            } else {\r\n              fileType = 1; // PDF\r\n            }\r\n\r\n            this.imageUrlList.push({\r\n              data: e.target.result,\r\n              CFileBlood: this.removeBase64Prefix(e.target.result),\r\n              CFileName: file.name,\r\n              CFileType: fileType\r\n            });\r\n\r\n            if (this.imageUrlList.length === files.length) {\r\n              console.log('this.imageUrlList', this.imageUrlList);\r\n              if (this.fileInput) {\r\n                this.fileInput.nativeElement.value = null;\r\n              }\r\n            }\r\n          };\r\n          reader.readAsDataURL(file);\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  isPDFString(str: any): boolean {\r\n    if (str) {\r\n      return str.toLowerCase().endsWith(\".pdf\")\r\n    }\r\n    return false\r\n  }\r\n\r\n  isCadString(str: any): boolean {\r\n    if (str) {\r\n      const lowerStr = str.toLowerCase();\r\n      return lowerStr.endsWith(\".dwg\") || lowerStr.endsWith(\".dxf\");\r\n    }\r\n    return false\r\n  }\r\n  isImage(fileType: number): boolean {\r\n    return fileType === 2;\r\n  }\r\n\r\n  isCad(fileType: number): boolean {\r\n    return fileType === 3;\r\n  }\r\n\r\n  isPdf(extension: string): boolean {\r\n    return extension.toLowerCase() === 'pdf';\r\n  }\r\n\r\n  listPictures: any[] = []\r\n\r\n  removeFile(index: number) {\r\n    this.imageUrlList.splice(index, 1);\r\n  }\r\n\r\n  removeImage(pictureId: number) {\r\n    this.listPictures = this.listPictures.filter(x => x.id != pictureId)\r\n  }\r\n\r\n  uploadImage(ref: any) {\r\n  }\r\n\r\n  renameFile(event: any, index: number) {\r\n    var blob = this.listPictures[index].CFile.slice(0, this.listPictures[index].CFile.size, this.listPictures[index].CFile.type);\r\n    var newFile = new File([blob], `${event.target.value + '.' + this.listPictures[index].extension}`, { type: this.listPictures[index].CFile.type });\r\n    this.listPictures[index].CFile = newFile\r\n  }\r\n\r\n  goBack() {\r\n    this._eventService.push({\r\n      action: EEvent.GET_BUILDCASE,\r\n      payload: this.buildCaseId\r\n    })\r\n    this.location.back()\r\n  }\r\n  openNewTab(url: any) {\r\n    if (url) window.open(url, \"_blank\");\r\n  }\r\n\r\n  downloadFileFromBlob(file: any) {\r\n    if (file && file.data && file.CFileName) {\r\n      // 將 base64 轉換為 blob\r\n      const byteCharacters = atob(file.CFileBlood || file.data.split(',')[1]);\r\n      const byteNumbers = new Array(byteCharacters.length);\r\n      for (let i = 0; i < byteCharacters.length; i++) {\r\n        byteNumbers[i] = byteCharacters.charCodeAt(i);\r\n      }\r\n      const byteArray = new Uint8Array(byteNumbers);\r\n      \r\n      // 設定 MIME 類型\r\n      let mimeType = 'application/octet-stream';\r\n      if (file.CFileType === 1) {\r\n        mimeType = 'application/pdf';\r\n      } else if (file.CFileType === 3) {\r\n        mimeType = 'application/x-autocad';\r\n      }\r\n      \r\n      const blob = new Blob([byteArray], { type: mimeType });\r\n      \r\n      // 建立下載連結\r\n      const url = window.URL.createObjectURL(blob);\r\n      const link = document.createElement('a');\r\n      link.href = url;\r\n      link.download = file.CFileName;\r\n      document.body.appendChild(link);\r\n      link.click();\r\n      document.body.removeChild(link);\r\n      window.URL.revokeObjectURL(url);\r\n    }\r\n  }\r\n\r\n}\r\n", "<nb-card accent=\"success\">\r\n  <nb-card-header>\r\n    戶別管理 > 洽談紀錄上傳 > {{houseTitle}}\r\n  </nb-card-header>\r\n  <nb-card-body>\r\n    <h1 class=\"font-bold text-[#818181]\">您可與此上傳與該戶別客戶討論的客戶圖面，審核通過後客戶就可以在前台檢視該圖面。</h1>\r\n\r\n    <div class=\"d-flex flex-wrap\">\r\n\r\n      <div class=\"col-md-12\">\r\n        <div class=\"d-flex justify-content-end w-full\">\r\n          <button class=\"btn btn-info\" (click)=\"addNew(dialogUploadDrawing)\" *ngIf=\"isCreate\">\r\n            上傳圖面</button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <div class=\"table-responsive mt-4\">\r\n      <table class=\"table table-striped border \" style=\"min-width: 1000px; background-color:#f3f3f3;\">\r\n        <thead>\r\n          <tr style=\"background-color: #27ae60; color: white;\" class=\"text-center\">\r\n            <th scope=\"col\" class=\"col-1\">討論日期</th>\r\n            <th scope=\"col\" class=\"col-1\">圖面名稱</th>\r\n            <th scope=\"col\" class=\"col-1\">上傳日期</th>\r\n            <th scope=\"col\" class=\"col-1\">審核狀態</th>\r\n            <th scope=\"col\" class=\"col-1\">操作</th>\r\n          </tr>\r\n        </thead>\r\n        <tbody>\r\n          <tr *ngFor=\"let item of listSpecialChange ; let i = index\" class=\"text-center\">\r\n            <td>{{ formatDate(item.CChangeDate)}}</td>\r\n            <td>{{ item.CDrawingName}}</td>\r\n            <td>{{formatDate(item.CCreateDT)}}</td>\r\n            <td>{{ item.CIsApprove == null ? '待審核' : ( item.CIsApprove ? \"通過\" : \"駁回\")}}</td>\r\n            <td class=\"text-center w-32\">\r\n              <button class=\"btn btn-outline-primary btn-sm m-1\" *ngIf=\"isUpdate\"\r\n                (click)=\"onEdit(dialogUploadDrawing, item)\">檢視</button>\r\n            </td>\r\n          </tr>\r\n        </tbody>\r\n      </table>\r\n    </div>\r\n  </nb-card-body>\r\n  <nb-card-footer class=\"d-flex justify-content-center\">\r\n    <ngb-pagination [(page)]=\"pageIndex\" [pageSize]=\"pageSize\" [collectionSize]=\"totalRecords\"\r\n      (pageChange)=\"pageChanged($event)\" aria-label=\"Pagination\">\r\n    </ngb-pagination>\r\n  </nb-card-footer>\r\n  <nb-card-footer>\r\n    <div class=\"d-flex justify-content-center\">\r\n      <button class=\"btn btn-secondary btn-sm\" (click)=\"goBack()\">\r\n        返回上一頁\r\n      </button>\r\n    </div>\r\n  </nb-card-footer>\r\n</nb-card>\r\n\r\n\r\n<ng-template #dialogUploadDrawing let-dialog let-ref=\"dialogRef\">\r\n  <nb-card style=\"min-width:600px; max-height: 95vh\">\r\n    <nb-card-header>\r\n      戶別管理 > 洽談紀錄上傳 > {{house.CHousehold}} &nbsp; {{house.CFloor}}F\r\n    </nb-card-header>\r\n    <nb-card-body class=\"px-4\">\r\n\r\n      <div class=\"form-group\">\r\n        <label for=\"CChangeDate\" #CChangeDate class=\"required-field mr-4\" style=\"min-width:75px\" baseLabel>\r\n          討論日期\r\n        </label>\r\n        <p-calendar [appendTo]=\"'CChangeDate'\" placeholder=\"年/月/日\" [iconDisplay]=\"'input'\" [showIcon]=\"true\"\r\n          inputId=\"icondisplay\" dateFormat=\"yy/mm/dd\" [(ngModel)]=\"formSpecialChange.CChangeDate\" [disabled]=\"isEdit\"\r\n          [showButtonBar]=\"true\" class=\"!w-[400px]\"></p-calendar>\r\n      </div>\r\n      <div class=\"form-group\">\r\n        <label for=\"cDrawingName\" class=\"required-field mr-4\" style=\"min-width:75px\" baseLabel>\r\n          圖面名稱\r\n        </label>\r\n        <input type=\"text\" class=\"w-full\" nbInput placeholder=\"圖面名稱\" [(ngModel)]=\"formSpecialChange.CDrawingName\"\r\n          [disabled]=\"isEdit\" />\r\n      </div>\r\n      <div class=\"form-group\">\r\n        <label for=\"cIsEnable\" class=\" mr-4\" style=\"min-width:75px\" baseLabel>\r\n          選樣結果\r\n        </label>\r\n        <button *ngIf=\"!(isEdit && SpecialChange.CIsApprove === null)\" class=\"btn btn-info\"\r\n          (click)=\"inputFile.click()\">選擇檔案</button> <input #inputFile type=\"file\" id=\"fileInput\" class=\"hidden\"\r\n          (change)=\"detectFiles($event)\" [disabled]=\"isEdit\" accept=\"image/jpeg, image/jpg, application/pdf, .dwg, .dxf\"\r\n          multiple>\r\n      </div>\r\n\r\n      <div class=\"form-group d-flex align-items-center\">\r\n        <label baseLabel class=\"align-self-start mr-4\" style=\"min-width:75px\">\r\n        </label>\r\n        <div class=\"flex flex-wrap mt-2\" *ngIf=\"!isEdit\">\r\n          <div *ngFor=\"let file of imageUrlList; let i = index\" class=\"relative mr-2 mb-2\">\r\n            <!-- 圖片檔案顯示 -->\r\n            <div *ngIf=\"isImage(file.CFileType)\" class=\"relative w-24 h-24 border\">\r\n              <img class=\"w-full h-full object-contain cursor-pointer\" [src]=\"file.data\">\r\n              <p class=\"absolute -bottom-4 left-0 w-full text-xs truncate px-1 text-center\">{{ file.CFileName }}</p>\r\n              <span class=\"absolute top-0 right-0 cursor-pointer bg-white rounded-full\" (click)=\"removeFile(i)\">\r\n                <i class=\"fa fa-times text-red-600\"></i>\r\n              </span>\r\n            </div> <!-- 非圖片檔案顯示下載按鈕 -->\r\n            <div *ngIf=\"!isImage(file.CFileType)\" class=\"flex flex-col items-center p-2 border\">\r\n              <button class=\"btn btn-sm btn-primary mb-1\" (click)=\"downloadFileFromBlob(file)\">\r\n                <i class=\"fa fa-download mr-1\"></i>下載\r\n              </button>\r\n              <span class=\"text-xs mb-1\">\r\n                {{ !isCad(file.CFileType) ? 'PDF' : 'CAD' }}\r\n              </span>\r\n              <p class=\"text-xs truncate text-center max-w-20\">{{ file.CFileName }}</p>\r\n              <span class=\"absolute top-0 right-0 cursor-pointer bg-white rounded-full\" (click)=\"removeFile(i)\">\r\n                <i class=\"fa fa-times text-red-600\"></i>\r\n              </span>\r\n            </div>\r\n          </div>\r\n        </div>\r\n        <div class=\"flex flex-wrap mt-2\" *ngIf=\"isEdit\">\r\n          <div *ngFor=\"let file of SpecialChange.CFileRes; let i = index\" class=\"relative mr-2 mb-2\">\r\n            <!-- 圖片檔案顯示 -->\r\n            <div *ngIf=\"!isPDFString(file.CFile) && !isCadString(file.CFile)\" class=\"relative w-24 h-24 border\">\r\n              <img class=\"w-full h-full object-contain cursor-pointer\" [src]=\"file.CFile\"\r\n                (click)=\"openNewTab(file.CFile)\">\r\n              <p class=\"absolute -bottom-4 left-0 w-full text-xs truncate px-1 text-center\">{{ file.CFileName }}</p>\r\n            </div>\r\n            <!-- 非圖片檔案顯示下載按鈕 -->\r\n            <div *ngIf=\"isPDFString(file.CFile) || isCadString(file.CFile)\"\r\n              class=\"flex flex-col items-center p-2 border\">\r\n              <button class=\"btn btn-sm btn-primary mb-1\" (click)=\"openNewTab(file.CFile)\">\r\n                <i class=\"fa fa-download mr-1\"></i>下載\r\n              </button>\r\n              <span class=\"text-xs mb-1\">\r\n                {{ isPDFString(file.CFile) ? 'PDF' : 'CAD' }}\r\n              </span>\r\n              <p class=\"text-xs truncate text-center max-w-20\">{{ file.CFileName }}</p>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <div class=\"form-group d-flex align-items-center\">\r\n        <label for=\"cApproveRemark\" baseLabel class=\"required-field align-self-start mr-4\"\r\n          style=\"min-width:75px\">審核說明</label>\r\n        <textarea name=\"remark\" id=\"cApproveRemark\" rows=\"5\" nbInput style=\"resize: none;\" class=\"w-full\"\r\n          [disabled]=\"isEdit\" class=\"w-full\" [(ngModel)]=\"formSpecialChange.CApproveRemark\"></textarea>\r\n      </div>\r\n\r\n      <div class=\"d-flex justify-content-center\">\r\n        <button class=\"btn btn-outline-secondary m-2\" (click)=\"onClose(ref)\">取消</button>\r\n        <button class=\"btn btn-success m-2\" *ngIf=\"!isEdit\" (click)=\"onSaveSpecialChange(ref)\">送出審核</button>\r\n      </div>\r\n    </nb-card-body>\r\n  </nb-card>\r\n</ng-template>\r\n"], "mappings": "AASA,OAAO,KAAKA,MAAM,MAAM,QAAQ;AAChC,SAASC,aAAa,QAAQ,qCAAqC;AACnE,SAAuBC,MAAM,QAAQ,uCAAuC;;;;;;;;;;;;;;;;;;ICAlEC,EAAA,CAAAC,cAAA,iBAAoF;IAAvDD,EAAA,CAAAE,UAAA,mBAAAC,yEAAA;MAAAH,EAAA,CAAAI,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,MAAAC,sBAAA,GAAAR,EAAA,CAAAS,WAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASJ,MAAA,CAAAK,MAAA,CAAAH,sBAAA,CAA2B;IAAA,EAAC;IAChER,EAAA,CAAAY,MAAA,gCAAI;IAAAZ,EAAA,CAAAa,YAAA,EAAS;;;;;;IAuBXb,EAAA,CAAAC,cAAA,iBAC8C;IAA5CD,EAAA,CAAAE,UAAA,mBAAAY,gFAAA;MAAAd,EAAA,CAAAI,aAAA,CAAAW,GAAA;MAAA,MAAAC,OAAA,GAAAhB,EAAA,CAAAO,aAAA,GAAAU,SAAA;MAAA,MAAAX,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,MAAAC,sBAAA,GAAAR,EAAA,CAAAS,WAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASJ,MAAA,CAAAY,MAAA,CAAAV,sBAAA,EAAAQ,OAAA,CAAiC;IAAA,EAAC;IAAChB,EAAA,CAAAY,MAAA,mBAAE;IAAAZ,EAAA,CAAAa,YAAA,EAAS;;;;;IAN3Db,EADF,CAAAC,cAAA,aAA+E,SACzE;IAAAD,EAAA,CAAAY,MAAA,GAAiC;IAAAZ,EAAA,CAAAa,YAAA,EAAK;IAC1Cb,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAY,MAAA,GAAsB;IAAAZ,EAAA,CAAAa,YAAA,EAAK;IAC/Bb,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAY,MAAA,GAA8B;IAAAZ,EAAA,CAAAa,YAAA,EAAK;IACvCb,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAY,MAAA,GAAuE;IAAAZ,EAAA,CAAAa,YAAA,EAAK;IAChFb,EAAA,CAAAC,cAAA,aAA6B;IAC3BD,EAAA,CAAAmB,UAAA,KAAAC,uDAAA,qBAC8C;IAElDpB,EADE,CAAAa,YAAA,EAAK,EACF;;;;;IARCb,EAAA,CAAAqB,SAAA,GAAiC;IAAjCrB,EAAA,CAAAsB,iBAAA,CAAAhB,MAAA,CAAAiB,UAAA,CAAAP,OAAA,CAAAQ,WAAA,EAAiC;IACjCxB,EAAA,CAAAqB,SAAA,GAAsB;IAAtBrB,EAAA,CAAAsB,iBAAA,CAAAN,OAAA,CAAAS,YAAA,CAAsB;IACtBzB,EAAA,CAAAqB,SAAA,GAA8B;IAA9BrB,EAAA,CAAAsB,iBAAA,CAAAhB,MAAA,CAAAiB,UAAA,CAAAP,OAAA,CAAAU,SAAA,EAA8B;IAC9B1B,EAAA,CAAAqB,SAAA,GAAuE;IAAvErB,EAAA,CAAAsB,iBAAA,CAAAN,OAAA,CAAAW,UAAA,kCAAAX,OAAA,CAAAW,UAAA,mCAAuE;IAErB3B,EAAA,CAAAqB,SAAA,GAAc;IAAdrB,EAAA,CAAA4B,UAAA,SAAAtB,MAAA,CAAAuB,QAAA,CAAc;;;;;;IAiDxE7B,EAAA,CAAAC,cAAA,iBAC8B;IAA5BD,EAAA,CAAAE,UAAA,mBAAA4B,yFAAA;MAAA9B,EAAA,CAAAI,aAAA,CAAA2B,GAAA;MAAA/B,EAAA,CAAAO,aAAA;MAAA,MAAAyB,YAAA,GAAAhC,EAAA,CAAAS,WAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASsB,YAAA,CAAAC,KAAA,EAAiB;IAAA,EAAC;IAACjC,EAAA,CAAAY,MAAA,+BAAI;IAAAZ,EAAA,CAAAa,YAAA,EAAS;;;;;;IAWvCb,EAAA,CAAAC,cAAA,cAAuE;IACrED,EAAA,CAAAkC,SAAA,cAA2E;IAC3ElC,EAAA,CAAAC,cAAA,YAA8E;IAAAD,EAAA,CAAAY,MAAA,GAAoB;IAAAZ,EAAA,CAAAa,YAAA,EAAI;IACtGb,EAAA,CAAAC,cAAA,eAAkG;IAAxBD,EAAA,CAAAE,UAAA,mBAAAiC,gGAAA;MAAAnC,EAAA,CAAAI,aAAA,CAAAgC,IAAA;MAAA,MAAAC,KAAA,GAAArC,EAAA,CAAAO,aAAA,GAAA+B,KAAA;MAAA,MAAAhC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAU,WAAA,CAASJ,MAAA,CAAAiC,UAAA,CAAAF,KAAA,CAAa;IAAA,EAAC;IAC/FrC,EAAA,CAAAkC,SAAA,YAAwC;IAE5ClC,EADE,CAAAa,YAAA,EAAO,EACH;;;;IALqDb,EAAA,CAAAqB,SAAA,EAAiB;IAAjBrB,EAAA,CAAA4B,UAAA,QAAAY,QAAA,CAAAC,IAAA,EAAAzC,EAAA,CAAA0C,aAAA,CAAiB;IACI1C,EAAA,CAAAqB,SAAA,GAAoB;IAApBrB,EAAA,CAAAsB,iBAAA,CAAAkB,QAAA,CAAAG,SAAA,CAAoB;;;;;;IAMlG3C,EADF,CAAAC,cAAA,cAAoF,iBACD;IAArCD,EAAA,CAAAE,UAAA,mBAAA0C,kGAAA;MAAA5C,EAAA,CAAAI,aAAA,CAAAyC,IAAA;MAAA,MAAAL,QAAA,GAAAxC,EAAA,CAAAO,aAAA,GAAAU,SAAA;MAAA,MAAAX,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAU,WAAA,CAASJ,MAAA,CAAAwC,oBAAA,CAAAN,QAAA,CAA0B;IAAA,EAAC;IAC9ExC,EAAA,CAAAkC,SAAA,YAAmC;IAAAlC,EAAA,CAAAY,MAAA,oBACrC;IAAAZ,EAAA,CAAAa,YAAA,EAAS;IACTb,EAAA,CAAAC,cAAA,eAA2B;IACzBD,EAAA,CAAAY,MAAA,GACF;IAAAZ,EAAA,CAAAa,YAAA,EAAO;IACPb,EAAA,CAAAC,cAAA,YAAiD;IAAAD,EAAA,CAAAY,MAAA,GAAoB;IAAAZ,EAAA,CAAAa,YAAA,EAAI;IACzEb,EAAA,CAAAC,cAAA,eAAkG;IAAxBD,EAAA,CAAAE,UAAA,mBAAA6C,gGAAA;MAAA/C,EAAA,CAAAI,aAAA,CAAAyC,IAAA;MAAA,MAAAR,KAAA,GAAArC,EAAA,CAAAO,aAAA,GAAA+B,KAAA;MAAA,MAAAhC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAU,WAAA,CAASJ,MAAA,CAAAiC,UAAA,CAAAF,KAAA,CAAa;IAAA,EAAC;IAC/FrC,EAAA,CAAAkC,SAAA,YAAwC;IAE5ClC,EADE,CAAAa,YAAA,EAAO,EACH;;;;;IANFb,EAAA,CAAAqB,SAAA,GACF;IADErB,EAAA,CAAAgD,kBAAA,OAAA1C,MAAA,CAAA2C,KAAA,CAAAT,QAAA,CAAAU,SAAA,uBACF;IACiDlD,EAAA,CAAAqB,SAAA,GAAoB;IAApBrB,EAAA,CAAAsB,iBAAA,CAAAkB,QAAA,CAAAG,SAAA,CAAoB;;;;;IAhBzE3C,EAAA,CAAAC,cAAA,cAAiF;IAS/ED,EAPA,CAAAmB,UAAA,IAAAgC,yEAAA,kBAAuE,IAAAC,yEAAA,mBAOa;IAYtFpD,EAAA,CAAAa,YAAA,EAAM;;;;;IAnBEb,EAAA,CAAAqB,SAAA,EAA6B;IAA7BrB,EAAA,CAAA4B,UAAA,SAAAtB,MAAA,CAAA+C,OAAA,CAAAb,QAAA,CAAAU,SAAA,EAA6B;IAO7BlD,EAAA,CAAAqB,SAAA,EAA8B;IAA9BrB,EAAA,CAAA4B,UAAA,UAAAtB,MAAA,CAAA+C,OAAA,CAAAb,QAAA,CAAAU,SAAA,EAA8B;;;;;IAVxClD,EAAA,CAAAC,cAAA,cAAiD;IAC/CD,EAAA,CAAAmB,UAAA,IAAAmC,mEAAA,kBAAiF;IAsBnFtD,EAAA,CAAAa,YAAA,EAAM;;;;IAtBkBb,EAAA,CAAAqB,SAAA,EAAiB;IAAjBrB,EAAA,CAAA4B,UAAA,YAAAtB,MAAA,CAAAiD,YAAA,CAAiB;;;;;;IA2BnCvD,EADF,CAAAC,cAAA,cAAoG,cAE/D;IAAjCD,EAAA,CAAAE,UAAA,mBAAAsD,+FAAA;MAAAxD,EAAA,CAAAI,aAAA,CAAAqD,IAAA;MAAA,MAAAC,QAAA,GAAA1D,EAAA,CAAAO,aAAA,GAAAU,SAAA;MAAA,MAAAX,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAU,WAAA,CAASJ,MAAA,CAAAqD,UAAA,CAAAD,QAAA,CAAAE,KAAA,CAAsB;IAAA,EAAC;IADlC5D,EAAA,CAAAa,YAAA,EACmC;IACnCb,EAAA,CAAAC,cAAA,YAA8E;IAAAD,EAAA,CAAAY,MAAA,GAAoB;IACpGZ,EADoG,CAAAa,YAAA,EAAI,EAClG;;;;IAHqDb,EAAA,CAAAqB,SAAA,EAAkB;IAAlBrB,EAAA,CAAA4B,UAAA,QAAA8B,QAAA,CAAAE,KAAA,EAAA5D,EAAA,CAAA0C,aAAA,CAAkB;IAEG1C,EAAA,CAAAqB,SAAA,GAAoB;IAApBrB,EAAA,CAAAsB,iBAAA,CAAAoC,QAAA,CAAAf,SAAA,CAAoB;;;;;;IAKlG3C,EAFF,CAAAC,cAAA,cACgD,iBAC+B;IAAjCD,EAAA,CAAAE,UAAA,mBAAA2D,kGAAA;MAAA7D,EAAA,CAAAI,aAAA,CAAA0D,IAAA;MAAA,MAAAJ,QAAA,GAAA1D,EAAA,CAAAO,aAAA,GAAAU,SAAA;MAAA,MAAAX,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAU,WAAA,CAASJ,MAAA,CAAAqD,UAAA,CAAAD,QAAA,CAAAE,KAAA,CAAsB;IAAA,EAAC;IAC1E5D,EAAA,CAAAkC,SAAA,YAAmC;IAAAlC,EAAA,CAAAY,MAAA,oBACrC;IAAAZ,EAAA,CAAAa,YAAA,EAAS;IACTb,EAAA,CAAAC,cAAA,eAA2B;IACzBD,EAAA,CAAAY,MAAA,GACF;IAAAZ,EAAA,CAAAa,YAAA,EAAO;IACPb,EAAA,CAAAC,cAAA,YAAiD;IAAAD,EAAA,CAAAY,MAAA,GAAoB;IACvEZ,EADuE,CAAAa,YAAA,EAAI,EACrE;;;;;IAHFb,EAAA,CAAAqB,SAAA,GACF;IADErB,EAAA,CAAAgD,kBAAA,MAAA1C,MAAA,CAAAyD,WAAA,CAAAL,QAAA,CAAAE,KAAA,uBACF;IACiD5D,EAAA,CAAAqB,SAAA,GAAoB;IAApBrB,EAAA,CAAAsB,iBAAA,CAAAoC,QAAA,CAAAf,SAAA,CAAoB;;;;;IAhBzE3C,EAAA,CAAAC,cAAA,cAA2F;IAQzFD,EANA,CAAAmB,UAAA,IAAA6C,yEAAA,kBAAoG,IAAAC,yEAAA,kBAOpD;IASlDjE,EAAA,CAAAa,YAAA,EAAM;;;;;IAhBEb,EAAA,CAAAqB,SAAA,EAA0D;IAA1DrB,EAAA,CAAA4B,UAAA,UAAAtB,MAAA,CAAAyD,WAAA,CAAAL,QAAA,CAAAE,KAAA,MAAAtD,MAAA,CAAA4D,WAAA,CAAAR,QAAA,CAAAE,KAAA,EAA0D;IAM1D5D,EAAA,CAAAqB,SAAA,EAAwD;IAAxDrB,EAAA,CAAA4B,UAAA,SAAAtB,MAAA,CAAAyD,WAAA,CAAAL,QAAA,CAAAE,KAAA,KAAAtD,MAAA,CAAA4D,WAAA,CAAAR,QAAA,CAAAE,KAAA,EAAwD;;;;;IATlE5D,EAAA,CAAAC,cAAA,cAAgD;IAC9CD,EAAA,CAAAmB,UAAA,IAAAgD,mEAAA,kBAA2F;IAmB7FnE,EAAA,CAAAa,YAAA,EAAM;;;;IAnBkBb,EAAA,CAAAqB,SAAA,EAA2B;IAA3BrB,EAAA,CAAA4B,UAAA,YAAAtB,MAAA,CAAA8D,aAAA,CAAAC,QAAA,CAA2B;;;;;;IA8BnDrE,EAAA,CAAAC,cAAA,iBAAuF;IAAnCD,EAAA,CAAAE,UAAA,mBAAAoE,yFAAA;MAAAtE,EAAA,CAAAI,aAAA,CAAAmE,IAAA;MAAA,MAAAC,OAAA,GAAAxE,EAAA,CAAAO,aAAA,GAAAkE,SAAA;MAAA,MAAAnE,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAU,WAAA,CAASJ,MAAA,CAAAoE,mBAAA,CAAAF,OAAA,CAAwB;IAAA,EAAC;IAACxE,EAAA,CAAAY,MAAA,+BAAI;IAAAZ,EAAA,CAAAa,YAAA,EAAS;;;;;;IAxFxGb,EADF,CAAAC,cAAA,kBAAmD,qBACjC;IACdD,EAAA,CAAAY,MAAA,GACF;IAAAZ,EAAA,CAAAa,YAAA,EAAiB;IAIbb,EAHJ,CAAAC,cAAA,uBAA2B,cAED,mBAC6E;IACjGD,EAAA,CAAAY,MAAA,iCACF;IAAAZ,EAAA,CAAAa,YAAA,EAAQ;IACRb,EAAA,CAAAC,cAAA,qBAE4C;IADED,EAAA,CAAA2E,gBAAA,2BAAAC,2FAAAC,MAAA;MAAA7E,EAAA,CAAAI,aAAA,CAAA0E,GAAA;MAAA,MAAAxE,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAAP,EAAA,CAAA+E,kBAAA,CAAAzE,MAAA,CAAA0E,iBAAA,CAAAxD,WAAA,EAAAqD,MAAA,MAAAvE,MAAA,CAAA0E,iBAAA,CAAAxD,WAAA,GAAAqD,MAAA;MAAA,OAAA7E,EAAA,CAAAU,WAAA,CAAAmE,MAAA;IAAA,EAA2C;IAE3F7E,EAD8C,CAAAa,YAAA,EAAa,EACrD;IAEJb,EADF,CAAAC,cAAA,cAAwB,iBACiE;IACrFD,EAAA,CAAAY,MAAA,kCACF;IAAAZ,EAAA,CAAAa,YAAA,EAAQ;IACRb,EAAA,CAAAC,cAAA,iBACwB;IADqCD,EAAA,CAAA2E,gBAAA,2BAAAM,uFAAAJ,MAAA;MAAA7E,EAAA,CAAAI,aAAA,CAAA0E,GAAA;MAAA,MAAAxE,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAAP,EAAA,CAAA+E,kBAAA,CAAAzE,MAAA,CAAA0E,iBAAA,CAAAvD,YAAA,EAAAoD,MAAA,MAAAvE,MAAA,CAAA0E,iBAAA,CAAAvD,YAAA,GAAAoD,MAAA;MAAA,OAAA7E,EAAA,CAAAU,WAAA,CAAAmE,MAAA;IAAA,EAA4C;IAE3G7E,EAFE,CAAAa,YAAA,EACwB,EACpB;IAEJb,EADF,CAAAC,cAAA,eAAwB,iBACgD;IACpED,EAAA,CAAAY,MAAA,kCACF;IAAAZ,EAAA,CAAAa,YAAA,EAAQ;IACRb,EAAA,CAAAmB,UAAA,KAAA+D,gEAAA,oBAC8B;IAAclF,EAAA,CAAAC,cAAA,oBAEjC;IADTD,EAAA,CAAAE,UAAA,oBAAAiF,gFAAAN,MAAA;MAAA7E,EAAA,CAAAI,aAAA,CAAA0E,GAAA;MAAA,MAAAxE,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAU,WAAA,CAAUJ,MAAA,CAAA8E,WAAA,CAAAP,MAAA,CAAmB;IAAA,EAAC;IAElC7E,EAH8C,CAAAa,YAAA,EAEjC,EACP;IAENb,EAAA,CAAAC,cAAA,eAAkD;IAChDD,EAAA,CAAAkC,SAAA,iBACQ;IAyBRlC,EAxBA,CAAAmB,UAAA,KAAAkE,6DAAA,kBAAiD,KAAAC,6DAAA,kBAwBD;IAqBlDtF,EAAA,CAAAa,YAAA,EAAM;IAEJb,EADF,CAAAC,cAAA,eAAkD,iBAEvB;IAAAD,EAAA,CAAAY,MAAA,gCAAI;IAAAZ,EAAA,CAAAa,YAAA,EAAQ;IACrCb,EAAA,CAAAC,cAAA,oBACoF;IAA/CD,EAAA,CAAA2E,gBAAA,2BAAAY,0FAAAV,MAAA;MAAA7E,EAAA,CAAAI,aAAA,CAAA0E,GAAA;MAAA,MAAAxE,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAAP,EAAA,CAAA+E,kBAAA,CAAAzE,MAAA,CAAA0E,iBAAA,CAAAQ,cAAA,EAAAX,MAAA,MAAAvE,MAAA,CAAA0E,iBAAA,CAAAQ,cAAA,GAAAX,MAAA;MAAA,OAAA7E,EAAA,CAAAU,WAAA,CAAAmE,MAAA;IAAA,EAA8C;IACrF7E,EADsF,CAAAa,YAAA,EAAW,EAC3F;IAGJb,EADF,CAAAC,cAAA,eAA2C,kBAC4B;IAAvBD,EAAA,CAAAE,UAAA,mBAAAuF,gFAAA;MAAA,MAAAjB,OAAA,GAAAxE,EAAA,CAAAI,aAAA,CAAA0E,GAAA,EAAAL,SAAA;MAAA,MAAAnE,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAU,WAAA,CAASJ,MAAA,CAAAoF,OAAA,CAAAlB,OAAA,CAAY;IAAA,EAAC;IAACxE,EAAA,CAAAY,MAAA,oBAAE;IAAAZ,EAAA,CAAAa,YAAA,EAAS;IAChFb,EAAA,CAAAmB,UAAA,KAAAwE,gEAAA,qBAAuF;IAG7F3F,EAFI,CAAAa,YAAA,EAAM,EACO,EACP;;;;IA1FNb,EAAA,CAAAqB,SAAA,GACF;IADErB,EAAA,CAAA4F,kBAAA,wEAAAtF,MAAA,CAAAuF,KAAA,CAAAC,UAAA,cAAAxF,MAAA,CAAAuF,KAAA,CAAAE,MAAA,OACF;IAOgB/F,EAAA,CAAAqB,SAAA,GAA0B;IAA6CrB,EAAvE,CAAA4B,UAAA,2BAA0B,wBAA4C,kBAAkB;IACtD5B,EAAA,CAAAgG,gBAAA,YAAA1F,MAAA,CAAA0E,iBAAA,CAAAxD,WAAA,CAA2C;IACvFxB,EADwF,CAAA4B,UAAA,aAAAtB,MAAA,CAAA2F,MAAA,CAAmB,uBACrF;IAMqCjG,EAAA,CAAAqB,SAAA,GAA4C;IAA5CrB,EAAA,CAAAgG,gBAAA,YAAA1F,MAAA,CAAA0E,iBAAA,CAAAvD,YAAA,CAA4C;IACvGzB,EAAA,CAAA4B,UAAA,aAAAtB,MAAA,CAAA2F,MAAA,CAAmB;IAMZjG,EAAA,CAAAqB,SAAA,GAAoD;IAApDrB,EAAA,CAAA4B,UAAA,WAAAtB,MAAA,CAAA2F,MAAA,IAAA3F,MAAA,CAAA8D,aAAA,CAAAzC,UAAA,WAAoD;IAE5B3B,EAAA,CAAAqB,SAAA,EAAmB;IAAnBrB,EAAA,CAAA4B,UAAA,aAAAtB,MAAA,CAAA2F,MAAA,CAAmB;IAOlBjG,EAAA,CAAAqB,SAAA,GAAa;IAAbrB,EAAA,CAAA4B,UAAA,UAAAtB,MAAA,CAAA2F,MAAA,CAAa;IAwBbjG,EAAA,CAAAqB,SAAA,EAAY;IAAZrB,EAAA,CAAA4B,UAAA,SAAAtB,MAAA,CAAA2F,MAAA,CAAY;IA0B5CjG,EAAA,CAAAqB,SAAA,GAAmB;IAAnBrB,EAAA,CAAA4B,UAAA,aAAAtB,MAAA,CAAA2F,MAAA,CAAmB;IAAgBjG,EAAA,CAAAgG,gBAAA,YAAA1F,MAAA,CAAA0E,iBAAA,CAAAQ,cAAA,CAA8C;IAK9CxF,EAAA,CAAAqB,SAAA,GAAa;IAAbrB,EAAA,CAAA4B,UAAA,UAAAtB,MAAA,CAAA2F,MAAA,CAAa;;;AD3H1D,OAAM,MAAOC,8BAA+B,SAAQpG,aAAa;EAC/DqG,YACUC,MAAmB,EACnBC,aAA8B,EAC9BC,KAAuB,EACvBC,qBAA2C,EAC3CC,aAA2B,EAE3BC,KAAqB,EACrBC,OAAuB,EACvBC,QAAkB,EAClBC,aAA2B;IACjC,KAAK,CAACR,MAAM,CAAC;IAVP,KAAAA,MAAM,GAANA,MAAM;IACN,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,qBAAqB,GAArBA,qBAAqB;IACrB,KAAAC,aAAa,GAAbA,aAAa;IAEb,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAC,QAAQ,GAARA,QAAQ;IACR,KAAAC,aAAa,GAAbA,aAAa;IAIvB,KAAArD,YAAY,GAAU,EAAE;IACxB,KAAA0C,MAAM,GAAG,KAAK;IACd,KAAAY,aAAa,GAAiB,CAC5B;MACEC,KAAK,EAAE,CAAC;MACRC,GAAG,EAAE,OAAO;MACZC,KAAK,EAAE;KACR,EACD;MACEF,KAAK,EAAE,CAAC;MACRC,GAAG,EAAE,aAAa;MAClBC,KAAK,EAAE;KACR,CACF;IAEQ,KAAAC,SAAS,GAAG,CAAC;IACb,KAAAC,QAAQ,GAAG,EAAE;IACb,KAAAC,SAAS,GAAG,CAAC;IACb,KAAAC,YAAY,GAAG,CAAC;IAyPzB,KAAAC,YAAY,GAAU,EAAE;EA9QN;EA8BTC,QAAQA,CAAA;IAEf,IAAI,CAACb,KAAK,CAACc,QAAQ,CAACC,SAAS,CAACC,MAAM,IAAG;MACrC,IAAIA,MAAM,EAAE;QACV,MAAMC,OAAO,GAAGD,MAAM,CAACE,GAAG,CAAC,KAAK,CAAC;QACjC,MAAMC,EAAE,GAAGF,OAAO,GAAG,CAACA,OAAO,GAAG,CAAC;QACjC,IAAI,CAACG,WAAW,GAAGD,EAAE;QACrB,MAAME,QAAQ,GAAGL,MAAM,CAACE,GAAG,CAAC,KAAK,CAAC;QAClC,MAAMI,GAAG,GAAGD,QAAQ,GAAG,CAACA,QAAQ,GAAG,CAAC;QACpC,IAAI,CAACE,OAAO,GAAGD,GAAG;QAClB,IAAI,CAACE,oBAAoB,EAAE;QAC3B,IAAI,CAACC,YAAY,EAAE;MACrB;IACF,CAAC,CAAC;EACJ;EAGAC,eAAeA,CAAC1F,IAAU;IACxB,IAAIA,IAAI,IAAIA,IAAI,CAAC4B,QAAQ,CAACT,KAAK,EAAEwE,MAAM,CAACC,IAAI,CAAC5F,IAAI,CAAC4B,QAAQ,CAACT,KAAK,EAAE,QAAQ,CAAC;EAC7E;EAKAqE,oBAAoBA,CAAA;IAClB,IAAI,CAAC1B,qBAAqB,CAAC+B,6CAA6C,CAAC;MACvEC,IAAI,EAAE;QACJC,QAAQ,EAAE,IAAI,CAACR,OAAO;QACtBS,SAAS,EAAE,IAAI,CAACtB,SAAS;QACzBuB,QAAQ,EAAE,IAAI,CAACxB;;KAElB,CAAC,CAACM,SAAS,CAACmB,GAAG,IAAG;MACjB,IAAIA,GAAG,CAACC,UAAU,IAAID,GAAG,CAACE,OAAO,IAAIF,GAAG,CAACG,UAAU,IAAI,CAAC,EAAE;QACxD,IAAI,CAACC,iBAAiB,GAAGJ,GAAG,CAACE,OAAQ,IAAI,EAAE;QAC3C,IAAI,CAACzB,YAAY,GAAGuB,GAAG,CAACC,UAAU;MACpC;IACF,CAAC,CAAC;EACJ;EAEAV,YAAYA,CAAA;IACV,IAAI,CAAC1B,aAAa,CAACwC,6BAA6B,CAAC;MAC/CT,IAAI,EAAE;QACJU,QAAQ,EAAE,IAAI,CAACjB;;KAElB,CAAC,CAACR,SAAS,CAACmB,GAAG,IAAG;MACjB,IAAIA,GAAG,CAACC,UAAU,IAAID,GAAG,CAACE,OAAO,IAAIF,GAAG,CAACG,UAAU,IAAI,CAAC,EAAE;QACxD,IAAI,CAACjD,KAAK,GAAG8C,GAAG,CAACE,OAAO;QACxB,IAAI,CAACK,UAAU,GAAG,GAAG,IAAI,CAACrD,KAAK,CAACC,UAAU,IAAI,IAAI,CAACD,KAAK,CAACE,MAAM,GAAG;MACpE;IACF,CAAC,CAAC;EACJ;EAIAoD,oBAAoBA,CAACC,GAAQ,EAAEC,gBAAqB;IAClD,IAAI,CAAC9C,qBAAqB,CAAC+C,6CAA6C,CAAC;MAAEf,IAAI,EAAEc;IAAgB,CAAE,CAAC,CAAC7B,SAAS,CAACmB,GAAG,IAAG;MACnH,IAAIA,GAAG,CAACC,UAAU,IAAID,GAAG,CAACE,OAAO,IAAIF,GAAG,CAACG,UAAU,IAAI,CAAC,EAAE;QACxD,IAAI,CAAC1E,aAAa,GAAGuE,GAAG,CAACE,OAAO;QAChC,IAAI,CAAC7D,iBAAiB,GAAG;UACvBQ,cAAc,EAAE,IAAI,CAACpB,aAAa,CAACoB,cAAc;UACjD+D,YAAY,EAAE,IAAI,CAAC1B,WAAW;UAC9BpG,YAAY,EAAE,IAAI,CAAC2C,aAAa,CAAC3C,YAAY;UAC7CwH,QAAQ,EAAE,IAAI,CAACjB,OAAO;UACtBwB,kBAAkB,EAAE;SACrB;QACD,IAAI,IAAI,CAACpF,aAAa,CAAC5C,WAAW,EAAE;UAClC,IAAI,CAACwD,iBAAiB,CAACxD,WAAW,GAAG,IAAIiI,IAAI,CAAC,IAAI,CAACrF,aAAa,CAAC5C,WAAW,CAAC;QAC/E;QACA,IAAI,CAAC6E,aAAa,CAACgC,IAAI,CAACe,GAAG,CAAC;MAC9B;IACF,CAAC,CAAC;EACJ;EAIA1E,mBAAmBA,CAAC0E,GAAQ;IAC1B,IAAI,CAACM,UAAU,EAAE;IACjB,IAAI,IAAI,CAACpD,KAAK,CAACqD,aAAa,CAACC,MAAM,GAAG,CAAC,EAAE;MACvC,IAAI,CAAClD,OAAO,CAACmD,aAAa,CAAC,IAAI,CAACvD,KAAK,CAACqD,aAAa,CAAC;MACpD;IACF;IAEA,IAAI,CAACpD,qBAAqB,CAACuD,0CAA0C,CAAC;MAAEvB,IAAI,EAAE,IAAI,CAACwB,WAAW;IAAE,CAAE,CAAC,CAACvC,SAAS,CAACmB,GAAG,IAAG;MAClH,IAAIA,GAAG,CAACG,UAAU,KAAK,CAAC,EAAE;QACxB,IAAI,CAACpC,OAAO,CAACsD,aAAa,CAAC,MAAM,CAAC;QAClC,IAAI,CAAC/B,oBAAoB,EAAE;QAC3BmB,GAAG,CAACa,KAAK,EAAE;MACb;IACF,CAAC,CAAC;EACJ;EAEAC,WAAWA,CAACC,OAAe;IACzB,IAAI,CAAChD,SAAS,GAAGgD,OAAO;IACxB,IAAI,CAAClC,oBAAoB,EAAE;EAC7B;EAGAtH,MAAMA,CAACyI,GAAQ;IACb,IAAI,CAAC7F,YAAY,GAAG,EAAE;IACtB,IAAI,CAAC0C,MAAM,GAAG,KAAK;IACnB,IAAI,CAACjB,iBAAiB,GAAG;MACvBQ,cAAc,EAAE,EAAE;MAClB+D,YAAY,EAAE,IAAI,CAAC1B,WAAW;MAC9BrG,WAAW,EAAE,EAAE;MACfC,YAAY,EAAE,EAAE;MAChBwH,QAAQ,EAAE,IAAI,CAACjB,OAAO;MACtBwB,kBAAkB,EAAE;KACrB;IACD,IAAI,CAACnD,aAAa,CAACgC,IAAI,CAACe,GAAG,CAAC;EAC9B;EAEAlI,MAAMA,CAACkI,GAAQ,EAAEgB,aAAkB;IACjC,IAAI,CAAC7G,YAAY,GAAG,EAAE;IACtB,IAAI,CAAC0C,MAAM,GAAG,IAAI;IAClB,IAAI,CAACkD,oBAAoB,CAACC,GAAG,EAAEgB,aAAa,CAACf,gBAAgB,CAAC;EAChE;EAEAK,UAAUA,CAAA;IACR,IAAI,CAACpD,KAAK,CAAC+D,KAAK,EAAE;IAClB,IAAI,CAAC/D,KAAK,CAACgE,QAAQ,CAAC,QAAQ,EAAE,IAAI,CAACtF,iBAAiB,CAACxD,WAAW,CAAC;IACjE,IAAI,CAAC8E,KAAK,CAACgE,QAAQ,CAAC,QAAQ,EAAE,IAAI,CAACtF,iBAAiB,CAACvD,YAAY,CAAC;IAClE,IAAI,CAAC6E,KAAK,CAACgE,QAAQ,CAAC,QAAQ,EAAE,IAAI,CAACtF,iBAAiB,CAACQ,cAAc,CAAC;EACtE;EAEAjE,UAAUA,CAACC,WAAmB;IAC5B,IAAIA,WAAW,EAAE;MACf,OAAO3B,MAAM,CAAC2B,WAAW,CAAC,CAAC+I,MAAM,CAAC,YAAY,CAAC;IACjD;IACA,OAAO,EAAE;EACX;EAEAC,gBAAgBA,CAACC,KAAY;IAC3B,KAAK,MAAMC,IAAI,IAAID,KAAK,EAAE;MACxB,OAAOC,IAAI,CAACjI,IAAI;IAClB;IACA,OAAOgI,KAAK;EACd;EAEAV,WAAWA,CAAA;IACT,MAAMY,MAAM,GAAG;MACb,GAAG,IAAI,CAAC3F,iBAAiB;MACzBwE,kBAAkB,EAAE,IAAI,CAACjG;KAC1B;IACD,IAAI,CAACiH,gBAAgB,CAACG,MAAM,CAACnB,kBAAkB,CAAC;IAEhD,IAAI,IAAI,CAACxE,iBAAiB,CAACxD,WAAW,EAAE;MACtCmJ,MAAM,CAACnJ,WAAW,GAAG,IAAI,CAACD,UAAU,CAAC,IAAI,CAACyD,iBAAiB,CAACxD,WAAW,CAAC;IAC1E;IACA,OAAOmJ,MAAM;EACf;EAEAjF,OAAOA,CAAC0D,GAAQ;IACdA,GAAG,CAACa,KAAK,EAAE;EACb;EAEAW,kBAAkBA,CAACC,YAAiB;IAClC,MAAMC,WAAW,GAAGD,YAAY,CAACE,OAAO,CAAC,GAAG,CAAC;IAC7C,IAAID,WAAW,KAAK,CAAC,CAAC,EAAE;MACtB,OAAOD,YAAY,CAACG,SAAS,CAACF,WAAW,GAAG,CAAC,CAAC;IAChD;IACA,OAAOD,YAAY;EACrB;EAAEzF,WAAWA,CAAC6F,KAAU;IACtB,MAAMC,KAAK,GAAaD,KAAK,CAACE,MAAM,CAACD,KAAK;IAC1C,IAAIA,KAAK,IAAIA,KAAK,CAACtB,MAAM,GAAG,CAAC,EAAE;MAC7B,MAAMwB,YAAY,GAAG,CAAC,YAAY,EAAE,WAAW,EAAE,iBAAiB,EAAE,kBAAkB,EAAE,uBAAuB,EAAE,eAAe,EAAE,aAAa,CAAC;MAChJ,MAAMC,SAAS,GAAG,2BAA2B;MAE7C;MACA,IAAI,CAAC,IAAI,CAACrG,iBAAiB,CAACvD,YAAY,IAAIyJ,KAAK,CAACtB,MAAM,GAAG,CAAC,EAAE;QAC5D,MAAM0B,SAAS,GAAGJ,KAAK,CAAC,CAAC,CAAC;QAC1B,MAAMK,QAAQ,GAAGD,SAAS,CAACE,IAAI;QAC/B,MAAMC,wBAAwB,GAAGF,QAAQ,CAACP,SAAS,CAAC,CAAC,EAAEO,QAAQ,CAACG,WAAW,CAAC,GAAG,CAAC,CAAC,IAAIH,QAAQ;QAC7F,IAAI,CAACvG,iBAAiB,CAACvD,YAAY,GAAGgK,wBAAwB;MAChE;MAEA,KAAK,IAAIE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGT,KAAK,CAACtB,MAAM,EAAE+B,CAAC,EAAE,EAAE;QACrC,MAAMC,IAAI,GAAGV,KAAK,CAACS,CAAC,CAAC;QACrB,IAAI,CAACN,SAAS,CAACQ,IAAI,CAACD,IAAI,CAACJ,IAAI,CAAC,EAAE;UAC9B,IAAI,CAAC9E,OAAO,CAACoF,YAAY,CAAC,kCAAkC,CAAC;UAC7D;QACF;QACA,IAAIV,YAAY,CAACW,QAAQ,CAACH,IAAI,CAACI,IAAI,CAAC,IAAIX,SAAS,CAACQ,IAAI,CAACD,IAAI,CAACJ,IAAI,CAAC,EAAE;UACjE,MAAMS,MAAM,GAAG,IAAIC,UAAU,EAAE;UAE/BD,MAAM,CAACE,MAAM,GAAIC,CAAM,IAAI;YACzB,IAAIC,QAAgB;YACpB,IAAIT,IAAI,CAACI,IAAI,CAACM,UAAU,CAAC,QAAQ,CAAC,EAAE;cAClCD,QAAQ,GAAG,CAAC,CAAC,CAAC;YAChB,CAAC,MAAM,IAAIT,IAAI,CAACJ,IAAI,CAACe,WAAW,EAAE,CAACR,QAAQ,CAAC,MAAM,CAAC,IAAIH,IAAI,CAACJ,IAAI,CAACe,WAAW,EAAE,CAACR,QAAQ,CAAC,MAAM,CAAC,EAAE;cAC/FM,QAAQ,GAAG,CAAC,CAAC,CAAC;YAChB,CAAC,MAAM;cACLA,QAAQ,GAAG,CAAC,CAAC,CAAC;YAChB;YAEA,IAAI,CAAC9I,YAAY,CAACiJ,IAAI,CAAC;cACrB/J,IAAI,EAAE2J,CAAC,CAACjB,MAAM,CAACR,MAAM;cACrB8B,UAAU,EAAE,IAAI,CAAC7B,kBAAkB,CAACwB,CAAC,CAACjB,MAAM,CAACR,MAAM,CAAC;cACpDhI,SAAS,EAAEiJ,IAAI,CAACJ,IAAI;cACpBtI,SAAS,EAAEmJ;aACZ,CAAC;YAEF,IAAI,IAAI,CAAC9I,YAAY,CAACqG,MAAM,KAAKsB,KAAK,CAACtB,MAAM,EAAE;cAC7C8C,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAE,IAAI,CAACpJ,YAAY,CAAC;cACnD,IAAI,IAAI,CAACqJ,SAAS,EAAE;gBAClB,IAAI,CAACA,SAAS,CAACC,aAAa,CAAC/F,KAAK,GAAG,IAAI;cAC3C;YACF;UACF,CAAC;UACDmF,MAAM,CAACa,aAAa,CAAClB,IAAI,CAAC;QAC5B;MACF;IACF;EACF;EAEA7H,WAAWA,CAACgJ,GAAQ;IAClB,IAAIA,GAAG,EAAE;MACP,OAAOA,GAAG,CAACR,WAAW,EAAE,CAACS,QAAQ,CAAC,MAAM,CAAC;IAC3C;IACA,OAAO,KAAK;EACd;EAEA9I,WAAWA,CAAC6I,GAAQ;IAClB,IAAIA,GAAG,EAAE;MACP,MAAME,QAAQ,GAAGF,GAAG,CAACR,WAAW,EAAE;MAClC,OAAOU,QAAQ,CAACD,QAAQ,CAAC,MAAM,CAAC,IAAIC,QAAQ,CAACD,QAAQ,CAAC,MAAM,CAAC;IAC/D;IACA,OAAO,KAAK;EACd;EACA3J,OAAOA,CAACgJ,QAAgB;IACtB,OAAOA,QAAQ,KAAK,CAAC;EACvB;EAEApJ,KAAKA,CAACoJ,QAAgB;IACpB,OAAOA,QAAQ,KAAK,CAAC;EACvB;EAEAa,KAAKA,CAACC,SAAiB;IACrB,OAAOA,SAAS,CAACZ,WAAW,EAAE,KAAK,KAAK;EAC1C;EAIAhK,UAAUA,CAACD,KAAa;IACtB,IAAI,CAACiB,YAAY,CAAC6J,MAAM,CAAC9K,KAAK,EAAE,CAAC,CAAC;EACpC;EAEA+K,WAAWA,CAACC,SAAiB;IAC3B,IAAI,CAACjG,YAAY,GAAG,IAAI,CAACA,YAAY,CAACkG,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAC5F,EAAE,IAAI0F,SAAS,CAAC;EACtE;EAEAG,WAAWA,CAACrE,GAAQ,GACpB;EAEAsE,UAAUA,CAACzC,KAAU,EAAE3I,KAAa;IAClC,IAAIqL,IAAI,GAAG,IAAI,CAACtG,YAAY,CAAC/E,KAAK,CAAC,CAACsB,KAAK,CAACgK,KAAK,CAAC,CAAC,EAAE,IAAI,CAACvG,YAAY,CAAC/E,KAAK,CAAC,CAACsB,KAAK,CAACiK,IAAI,EAAE,IAAI,CAACxG,YAAY,CAAC/E,KAAK,CAAC,CAACsB,KAAK,CAACoI,IAAI,CAAC;IAC5H,IAAI8B,OAAO,GAAG,IAAIC,IAAI,CAAC,CAACJ,IAAI,CAAC,EAAE,GAAG1C,KAAK,CAACE,MAAM,CAACrE,KAAK,GAAG,GAAG,GAAG,IAAI,CAACO,YAAY,CAAC/E,KAAK,CAAC,CAAC6K,SAAS,EAAE,EAAE;MAAEnB,IAAI,EAAE,IAAI,CAAC3E,YAAY,CAAC/E,KAAK,CAAC,CAACsB,KAAK,CAACoI;IAAI,CAAE,CAAC;IACjJ,IAAI,CAAC3E,YAAY,CAAC/E,KAAK,CAAC,CAACsB,KAAK,GAAGkK,OAAO;EAC1C;EAEAE,MAAMA,CAAA;IACJ,IAAI,CAACpH,aAAa,CAAC4F,IAAI,CAAC;MACtByB,MAAM;MACNC,OAAO,EAAE,IAAI,CAACrG;KACf,CAAC;IACF,IAAI,CAAClB,QAAQ,CAACwH,IAAI,EAAE;EACtB;EACAxK,UAAUA,CAACyK,GAAQ;IACjB,IAAIA,GAAG,EAAEhG,MAAM,CAACC,IAAI,CAAC+F,GAAG,EAAE,QAAQ,CAAC;EACrC;EAEAtL,oBAAoBA,CAAC8I,IAAS;IAC5B,IAAIA,IAAI,IAAIA,IAAI,CAACnJ,IAAI,IAAImJ,IAAI,CAACjJ,SAAS,EAAE;MACvC;MACA,MAAM0L,cAAc,GAAGC,IAAI,CAAC1C,IAAI,CAACa,UAAU,IAAIb,IAAI,CAACnJ,IAAI,CAAC8L,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;MACvE,MAAMC,WAAW,GAAG,IAAIC,KAAK,CAACJ,cAAc,CAACzE,MAAM,CAAC;MACpD,KAAK,IAAI+B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG0C,cAAc,CAACzE,MAAM,EAAE+B,CAAC,EAAE,EAAE;QAC9C6C,WAAW,CAAC7C,CAAC,CAAC,GAAG0C,cAAc,CAACK,UAAU,CAAC/C,CAAC,CAAC;MAC/C;MACA,MAAMgD,SAAS,GAAG,IAAIC,UAAU,CAACJ,WAAW,CAAC;MAE7C;MACA,IAAIK,QAAQ,GAAG,0BAA0B;MACzC,IAAIjD,IAAI,CAAC1I,SAAS,KAAK,CAAC,EAAE;QACxB2L,QAAQ,GAAG,iBAAiB;MAC9B,CAAC,MAAM,IAAIjD,IAAI,CAAC1I,SAAS,KAAK,CAAC,EAAE;QAC/B2L,QAAQ,GAAG,uBAAuB;MACpC;MAEA,MAAMlB,IAAI,GAAG,IAAImB,IAAI,CAAC,CAACH,SAAS,CAAC,EAAE;QAAE3C,IAAI,EAAE6C;MAAQ,CAAE,CAAC;MAEtD;MACA,MAAMT,GAAG,GAAGhG,MAAM,CAAC2G,GAAG,CAACC,eAAe,CAACrB,IAAI,CAAC;MAC5C,MAAMsB,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;MACxCF,IAAI,CAACG,IAAI,GAAGhB,GAAG;MACfa,IAAI,CAACI,QAAQ,GAAGzD,IAAI,CAACjJ,SAAS;MAC9BuM,QAAQ,CAAC3G,IAAI,CAAC+G,WAAW,CAACL,IAAI,CAAC;MAC/BA,IAAI,CAAChN,KAAK,EAAE;MACZiN,QAAQ,CAAC3G,IAAI,CAACgH,WAAW,CAACN,IAAI,CAAC;MAC/B7G,MAAM,CAAC2G,GAAG,CAACS,eAAe,CAACpB,GAAG,CAAC;IACjC;EACF;;;uCAtVWlI,8BAA8B,EAAAlG,EAAA,CAAAyP,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAA3P,EAAA,CAAAyP,iBAAA,CAAAG,EAAA,CAAAC,eAAA,GAAA7P,EAAA,CAAAyP,iBAAA,CAAAK,EAAA,CAAAC,gBAAA,GAAA/P,EAAA,CAAAyP,iBAAA,CAAAO,EAAA,CAAAC,oBAAA,GAAAjQ,EAAA,CAAAyP,iBAAA,CAAAO,EAAA,CAAAE,YAAA,GAAAlQ,EAAA,CAAAyP,iBAAA,CAAAU,EAAA,CAAAC,cAAA,GAAApQ,EAAA,CAAAyP,iBAAA,CAAAY,EAAA,CAAAC,cAAA,GAAAtQ,EAAA,CAAAyP,iBAAA,CAAAc,EAAA,CAAAC,QAAA,GAAAxQ,EAAA,CAAAyP,iBAAA,CAAAgB,EAAA,CAAAC,YAAA;IAAA;EAAA;;;YAA9BxK,8BAA8B;MAAAyK,SAAA;MAAAC,SAAA,WAAAC,qCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;;;;;;;;;;;;;;UCxBzC9Q,EADF,CAAAC,cAAA,iBAA0B,qBACR;UACdD,EAAA,CAAAY,MAAA,GACF;UAAAZ,EAAA,CAAAa,YAAA,EAAiB;UAEfb,EADF,CAAAC,cAAA,mBAAc,YACyB;UAAAD,EAAA,CAAAY,MAAA,iPAAuC;UAAAZ,EAAA,CAAAa,YAAA,EAAK;UAK7Eb,EAHJ,CAAAC,cAAA,aAA8B,aAEL,aAC0B;UAC7CD,EAAA,CAAAmB,UAAA,IAAA6P,gDAAA,oBAAoF;UAI1FhR,EAFI,CAAAa,YAAA,EAAM,EACF,EACF;UAMEb,EAJR,CAAAC,cAAA,cAAmC,iBAC+D,aACvF,cACoE,cACzC;UAAAD,EAAA,CAAAY,MAAA,gCAAI;UAAAZ,EAAA,CAAAa,YAAA,EAAK;UACvCb,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAY,MAAA,gCAAI;UAAAZ,EAAA,CAAAa,YAAA,EAAK;UACvCb,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAY,MAAA,gCAAI;UAAAZ,EAAA,CAAAa,YAAA,EAAK;UACvCb,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAY,MAAA,gCAAI;UAAAZ,EAAA,CAAAa,YAAA,EAAK;UACvCb,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAY,MAAA,oBAAE;UAEpCZ,EAFoC,CAAAa,YAAA,EAAK,EAClC,EACC;UACRb,EAAA,CAAAC,cAAA,aAAO;UACLD,EAAA,CAAAmB,UAAA,KAAA8P,6CAAA,kBAA+E;UAavFjR,EAHM,CAAAa,YAAA,EAAQ,EACF,EACJ,EACO;UAEbb,EADF,CAAAC,cAAA,0BAAsD,0BAES;UAD7CD,EAAA,CAAA2E,gBAAA,wBAAAuM,8EAAArM,MAAA;YAAA7E,EAAA,CAAAI,aAAA,CAAA+Q,GAAA;YAAAnR,EAAA,CAAA+E,kBAAA,CAAAgM,GAAA,CAAA5J,SAAA,EAAAtC,MAAA,MAAAkM,GAAA,CAAA5J,SAAA,GAAAtC,MAAA;YAAA,OAAA7E,EAAA,CAAAU,WAAA,CAAAmE,MAAA;UAAA,EAAoB;UAClC7E,EAAA,CAAAE,UAAA,wBAAAgR,8EAAArM,MAAA;YAAA7E,EAAA,CAAAI,aAAA,CAAA+Q,GAAA;YAAA,OAAAnR,EAAA,CAAAU,WAAA,CAAcqQ,GAAA,CAAA7G,WAAA,CAAArF,MAAA,CAAmB;UAAA,EAAC;UAEtC7E,EADE,CAAAa,YAAA,EAAiB,EACF;UAGbb,EAFJ,CAAAC,cAAA,sBAAgB,eAC6B,kBACmB;UAAnBD,EAAA,CAAAE,UAAA,mBAAAkR,iEAAA;YAAApR,EAAA,CAAAI,aAAA,CAAA+Q,GAAA;YAAA,OAAAnR,EAAA,CAAAU,WAAA,CAASqQ,GAAA,CAAA/C,MAAA,EAAQ;UAAA,EAAC;UACzDhO,EAAA,CAAAY,MAAA,wCACF;UAGNZ,EAHM,CAAAa,YAAA,EAAS,EACL,EACS,EACT;UAGVb,EAAA,CAAAmB,UAAA,KAAAkQ,sDAAA,kCAAArR,EAAA,CAAAsR,sBAAA,CAAiE;;;UAxD7DtR,EAAA,CAAAqB,SAAA,GACF;UADErB,EAAA,CAAAgD,kBAAA,wEAAA+N,GAAA,CAAA7H,UAAA,MACF;UAQ4ElJ,EAAA,CAAAqB,SAAA,GAAc;UAAdrB,EAAA,CAAA4B,UAAA,SAAAmP,GAAA,CAAAQ,QAAA,CAAc;UAkB7DvR,EAAA,CAAAqB,SAAA,IAAuB;UAAvBrB,EAAA,CAAA4B,UAAA,YAAAmP,GAAA,CAAAhI,iBAAA,CAAuB;UAelC/I,EAAA,CAAAqB,SAAA,GAAoB;UAApBrB,EAAA,CAAAgG,gBAAA,SAAA+K,GAAA,CAAA5J,SAAA,CAAoB;UAAuBnH,EAAtB,CAAA4B,UAAA,aAAAmP,GAAA,CAAA7J,QAAA,CAAqB,mBAAA6J,GAAA,CAAA3J,YAAA,CAAgC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}