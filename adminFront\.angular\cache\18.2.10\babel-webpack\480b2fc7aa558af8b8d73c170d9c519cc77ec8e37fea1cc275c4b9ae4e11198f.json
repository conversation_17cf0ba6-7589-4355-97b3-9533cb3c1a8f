{"ast": null, "code": "/* tslint:disable */\n/* eslint-disable */\nimport { HttpResponse } from '@angular/common/http';\nimport { filter, map } from 'rxjs/operators';\nimport { RequestBuilder } from '../../request-builder';\nexport function apiRegularChangeItemCheckRegularChangePost$Plain(http, rootUrl, params, context) {\n  const rb = new RequestBuilder(rootUrl, apiRegularChangeItemCheckRegularChangePost$Plain.PATH, 'post');\n  if (params) {}\n  return http.request(rb.build({\n    responseType: 'text',\n    accept: 'text/plain',\n    context\n  })).pipe(filter(r => r instanceof HttpResponse), map(r => {\n    return r;\n  }));\n}\napiRegularChangeItemCheckRegularChangePost$Plain.PATH = '/api/RegularChangeItem/CheckRegularChange';", "map": {"version": 3, "names": ["HttpResponse", "filter", "map", "RequestBuilder", "apiRegularChangeItemCheckRegularChangePost$Plain", "http", "rootUrl", "params", "context", "rb", "PATH", "request", "build", "responseType", "accept", "pipe", "r"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\services\\api\\fn\\regular-change-item\\api-regular-change-item-check-regular-change-post-plain.ts"], "sourcesContent": ["/* tslint:disable */\r\n/* eslint-disable */\r\nimport { HttpClient, HttpContext, HttpResponse } from '@angular/common/http';\r\nimport { Observable } from 'rxjs';\r\nimport { filter, map } from 'rxjs/operators';\r\nimport { StrictHttpResponse } from '../../strict-http-response';\r\nimport { RequestBuilder } from '../../request-builder';\r\n\r\nimport { BooleanResponseBase } from '../../models/boolean-response-base';\r\n\r\nexport interface ApiRegularChangeItemCheckRegularChangePost$Plain$Params {\r\n}\r\n\r\nexport function apiRegularChangeItemCheckRegularChangePost$Plain(http: HttpClient, rootUrl: string, params?: ApiRegularChangeItemCheckRegularChangePost$Plain$Params, context?: HttpContext): Observable<StrictHttpResponse<BooleanResponseBase>> {\r\n  const rb = new RequestBuilder(rootUrl, apiRegularChangeItemCheckRegularChangePost$Plain.PATH, 'post');\r\n  if (params) {\r\n  }\r\n\r\n  return http.request(\r\n    rb.build({ responseType: 'text', accept: 'text/plain', context })\r\n  ).pipe(\r\n    filter((r: any): r is HttpResponse<any> => r instanceof HttpResponse),\r\n    map((r: HttpResponse<any>) => {\r\n      return r as StrictHttpResponse<BooleanResponseBase>;\r\n    })\r\n  );\r\n}\r\n\r\napiRegularChangeItemCheckRegularChangePost$Plain.PATH = '/api/RegularChangeItem/CheckRegularChange';\r\n"], "mappings": "AAAA;AACA;AACA,SAAkCA,YAAY,QAAQ,sBAAsB;AAE5E,SAASC,MAAM,EAAEC,GAAG,QAAQ,gBAAgB;AAE5C,SAASC,cAAc,QAAQ,uBAAuB;AAOtD,OAAM,SAAUC,gDAAgDA,CAACC,IAAgB,EAAEC,OAAe,EAAEC,MAAgE,EAAEC,OAAqB;EACzL,MAAMC,EAAE,GAAG,IAAIN,cAAc,CAACG,OAAO,EAAEF,gDAAgD,CAACM,IAAI,EAAE,MAAM,CAAC;EACrG,IAAIH,MAAM,EAAE,CACZ;EAEA,OAAOF,IAAI,CAACM,OAAO,CACjBF,EAAE,CAACG,KAAK,CAAC;IAAEC,YAAY,EAAE,MAAM;IAAEC,MAAM,EAAE,YAAY;IAAEN;EAAO,CAAE,CAAC,CAClE,CAACO,IAAI,CACJd,MAAM,CAAEe,CAAM,IAA6BA,CAAC,YAAYhB,YAAY,CAAC,EACrEE,GAAG,CAAEc,CAAoB,IAAI;IAC3B,OAAOA,CAA4C;EACrD,CAAC,CAAC,CACH;AACH;AAEAZ,gDAAgD,CAACM,IAAI,GAAG,2CAA2C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}