{"ast": null, "code": "//! moment.js locale configuration\n//! locale : Armenian [hy-am]\n//! author : <PERSON>end<PERSON><PERSON><PERSON> : https://github.com/armendarabyan\n\n;\n(function (global, factory) {\n  typeof exports === 'object' && typeof module !== 'undefined' && typeof require === 'function' ? factory(require('../moment')) : typeof define === 'function' && define.amd ? define(['../moment'], factory) : factory(global.moment);\n})(this, function (moment) {\n  'use strict';\n\n  //! moment.js locale configuration\n  var hyAm = moment.defineLocale('hy-am', {\n    months: {\n      format: 'հունվարի_փետրվարի_մարտի_ապրիլի_մայիսի_հունիսի_հուլիսի_օգոստոսի_սեպտեմբերի_հոկտեմբերի_նոյեմբերի_դեկտեմբերի'.split('_'),\n      standalone: 'հունվար_փետրվար_մարտ_ապրիլ_մայիս_հունիս_հուլիս_օգոստոս_սեպտեմբեր_հոկտեմբեր_նոյեմբեր_դեկտեմբեր'.split('_')\n    },\n    monthsShort: 'հնվ_փտր_մրտ_ապր_մյս_հնս_հլս_օգս_սպտ_հկտ_նմբ_դկտ'.split('_'),\n    weekdays: 'կիրակի_երկուշաբթի_երեքշաբթի_չորեքշաբթի_հինգշաբթի_ուրբաթ_շաբաթ'.split('_'),\n    weekdaysShort: 'կրկ_երկ_երք_չրք_հնգ_ուրբ_շբթ'.split('_'),\n    weekdaysMin: 'կրկ_երկ_երք_չրք_հնգ_ուրբ_շբթ'.split('_'),\n    longDateFormat: {\n      LT: 'HH:mm',\n      LTS: 'HH:mm:ss',\n      L: 'DD.MM.YYYY',\n      LL: 'D MMMM YYYY թ.',\n      LLL: 'D MMMM YYYY թ., HH:mm',\n      LLLL: 'dddd, D MMMM YYYY թ., HH:mm'\n    },\n    calendar: {\n      sameDay: '[այսօր] LT',\n      nextDay: '[վաղը] LT',\n      lastDay: '[երեկ] LT',\n      nextWeek: function () {\n        return 'dddd [օրը ժամը] LT';\n      },\n      lastWeek: function () {\n        return '[անցած] dddd [օրը ժամը] LT';\n      },\n      sameElse: 'L'\n    },\n    relativeTime: {\n      future: '%s հետո',\n      past: '%s առաջ',\n      s: 'մի քանի վայրկյան',\n      ss: '%d վայրկյան',\n      m: 'րոպե',\n      mm: '%d րոպե',\n      h: 'ժամ',\n      hh: '%d ժամ',\n      d: 'օր',\n      dd: '%d օր',\n      M: 'ամիս',\n      MM: '%d ամիս',\n      y: 'տարի',\n      yy: '%d տարի'\n    },\n    meridiemParse: /գիշերվա|առավոտվա|ցերեկվա|երեկոյան/,\n    isPM: function (input) {\n      return /^(ցերեկվա|երեկոյան)$/.test(input);\n    },\n    meridiem: function (hour) {\n      if (hour < 4) {\n        return 'գիշերվա';\n      } else if (hour < 12) {\n        return 'առավոտվա';\n      } else if (hour < 17) {\n        return 'ցերեկվա';\n      } else {\n        return 'երեկոյան';\n      }\n    },\n    dayOfMonthOrdinalParse: /\\d{1,2}|\\d{1,2}-(ին|րդ)/,\n    ordinal: function (number, period) {\n      switch (period) {\n        case 'DDD':\n        case 'w':\n        case 'W':\n        case 'DDDo':\n          if (number === 1) {\n            return number + '-ին';\n          }\n          return number + '-րդ';\n        default:\n          return number;\n      }\n    },\n    week: {\n      dow: 1,\n      // Monday is the first day of the week.\n      doy: 7 // The week that contains Jan 7th is the first week of the year.\n    }\n  });\n  return hyAm;\n});", "map": {"version": 3, "names": ["global", "factory", "exports", "module", "require", "define", "amd", "moment", "hyAm", "defineLocale", "months", "format", "split", "standalone", "monthsShort", "weekdays", "weekdaysShort", "weekdaysMin", "longDateFormat", "LT", "LTS", "L", "LL", "LLL", "LLLL", "calendar", "sameDay", "nextDay", "lastDay", "nextWeek", "lastWeek", "same<PERSON><PERSON><PERSON>", "relativeTime", "future", "past", "s", "ss", "m", "mm", "h", "hh", "d", "dd", "M", "MM", "y", "yy", "meridiemParse", "isPM", "input", "test", "meridiem", "hour", "dayOfMonthOrdinalParse", "ordinal", "number", "period", "week", "dow", "doy"], "sources": ["C:/Users/<USER>/Documents/jeansalechange/adminFront/node_modules/moment/locale/hy-am.js"], "sourcesContent": ["//! moment.js locale configuration\n//! locale : Armenian [hy-am]\n//! author : Armend<PERSON><PERSON><PERSON> : https://github.com/armendarabyan\n\n;(function (global, factory) {\n   typeof exports === 'object' && typeof module !== 'undefined'\n       && typeof require === 'function' ? factory(require('../moment')) :\n   typeof define === 'function' && define.amd ? define(['../moment'], factory) :\n   factory(global.moment)\n}(this, (function (moment) { 'use strict';\n\n    //! moment.js locale configuration\n\n    var hyAm = moment.defineLocale('hy-am', {\n        months: {\n            format: 'հունվարի_փետրվարի_մարտի_ապրիլի_մայիսի_հունիսի_հուլիսի_օգոստոսի_սեպտեմբերի_հոկտեմբերի_նոյեմբերի_դեկտեմբերի'.split(\n                '_'\n            ),\n            standalone:\n                'հունվար_փետրվար_մարտ_ապրիլ_մայիս_հունիս_հուլիս_օգոստոս_սեպտեմբեր_հոկտեմբեր_նոյեմբեր_դեկտեմբեր'.split(\n                    '_'\n                ),\n        },\n        monthsShort: 'հնվ_փտր_մրտ_ապր_մյս_հնս_հլս_օգս_սպտ_հկտ_նմբ_դկտ'.split('_'),\n        weekdays:\n            'կիրակի_երկուշաբթի_երեքշաբթի_չորեքշաբթի_հինգշաբթի_ուրբաթ_շաբաթ'.split(\n                '_'\n            ),\n        weekdaysShort: 'կրկ_երկ_երք_չրք_հնգ_ուրբ_շբթ'.split('_'),\n        weekdaysMin: 'կրկ_երկ_երք_չրք_հնգ_ուրբ_շբթ'.split('_'),\n        longDateFormat: {\n            LT: 'HH:mm',\n            LTS: 'HH:mm:ss',\n            L: 'DD.MM.YYYY',\n            LL: 'D MMMM YYYY թ.',\n            LLL: 'D MMMM YYYY թ., HH:mm',\n            LLLL: 'dddd, D MMMM YYYY թ., HH:mm',\n        },\n        calendar: {\n            sameDay: '[այսօր] LT',\n            nextDay: '[վաղը] LT',\n            lastDay: '[երեկ] LT',\n            nextWeek: function () {\n                return 'dddd [օրը ժամը] LT';\n            },\n            lastWeek: function () {\n                return '[անցած] dddd [օրը ժամը] LT';\n            },\n            sameElse: 'L',\n        },\n        relativeTime: {\n            future: '%s հետո',\n            past: '%s առաջ',\n            s: 'մի քանի վայրկյան',\n            ss: '%d վայրկյան',\n            m: 'րոպե',\n            mm: '%d րոպե',\n            h: 'ժամ',\n            hh: '%d ժամ',\n            d: 'օր',\n            dd: '%d օր',\n            M: 'ամիս',\n            MM: '%d ամիս',\n            y: 'տարի',\n            yy: '%d տարի',\n        },\n        meridiemParse: /գիշերվա|առավոտվա|ցերեկվա|երեկոյան/,\n        isPM: function (input) {\n            return /^(ցերեկվա|երեկոյան)$/.test(input);\n        },\n        meridiem: function (hour) {\n            if (hour < 4) {\n                return 'գիշերվա';\n            } else if (hour < 12) {\n                return 'առավոտվա';\n            } else if (hour < 17) {\n                return 'ցերեկվա';\n            } else {\n                return 'երեկոյան';\n            }\n        },\n        dayOfMonthOrdinalParse: /\\d{1,2}|\\d{1,2}-(ին|րդ)/,\n        ordinal: function (number, period) {\n            switch (period) {\n                case 'DDD':\n                case 'w':\n                case 'W':\n                case 'DDDo':\n                    if (number === 1) {\n                        return number + '-ին';\n                    }\n                    return number + '-րդ';\n                default:\n                    return number;\n            }\n        },\n        week: {\n            dow: 1, // Monday is the first day of the week.\n            doy: 7, // The week that contains Jan 7th is the first week of the year.\n        },\n    });\n\n    return hyAm;\n\n})));\n"], "mappings": "AAAA;AACA;AACA;;AAEA;AAAE,WAAUA,MAAM,EAAEC,OAAO,EAAE;EAC1B,OAAOC,OAAO,KAAK,QAAQ,IAAI,OAAOC,MAAM,KAAK,WAAW,IACrD,OAAOC,OAAO,KAAK,UAAU,GAAGH,OAAO,CAACG,OAAO,CAAC,WAAW,CAAC,CAAC,GACpE,OAAOC,MAAM,KAAK,UAAU,IAAIA,MAAM,CAACC,GAAG,GAAGD,MAAM,CAAC,CAAC,WAAW,CAAC,EAAEJ,OAAO,CAAC,GAC3EA,OAAO,CAACD,MAAM,CAACO,MAAM,CAAC;AACzB,CAAC,EAAC,IAAI,EAAG,UAAUA,MAAM,EAAE;EAAE,YAAY;;EAErC;EAEA,IAAIC,IAAI,GAAGD,MAAM,CAACE,YAAY,CAAC,OAAO,EAAE;IACpCC,MAAM,EAAE;MACJC,MAAM,EAAE,2GAA2G,CAACC,KAAK,CACrH,GACJ,CAAC;MACDC,UAAU,EACN,+FAA+F,CAACD,KAAK,CACjG,GACJ;IACR,CAAC;IACDE,WAAW,EAAE,iDAAiD,CAACF,KAAK,CAAC,GAAG,CAAC;IACzEG,QAAQ,EACJ,+DAA+D,CAACH,KAAK,CACjE,GACJ,CAAC;IACLI,aAAa,EAAE,8BAA8B,CAACJ,KAAK,CAAC,GAAG,CAAC;IACxDK,WAAW,EAAE,8BAA8B,CAACL,KAAK,CAAC,GAAG,CAAC;IACtDM,cAAc,EAAE;MACZC,EAAE,EAAE,OAAO;MACXC,GAAG,EAAE,UAAU;MACfC,CAAC,EAAE,YAAY;MACfC,EAAE,EAAE,gBAAgB;MACpBC,GAAG,EAAE,uBAAuB;MAC5BC,IAAI,EAAE;IACV,CAAC;IACDC,QAAQ,EAAE;MACNC,OAAO,EAAE,YAAY;MACrBC,OAAO,EAAE,WAAW;MACpBC,OAAO,EAAE,WAAW;MACpBC,QAAQ,EAAE,SAAAA,CAAA,EAAY;QAClB,OAAO,oBAAoB;MAC/B,CAAC;MACDC,QAAQ,EAAE,SAAAA,CAAA,EAAY;QAClB,OAAO,4BAA4B;MACvC,CAAC;MACDC,QAAQ,EAAE;IACd,CAAC;IACDC,YAAY,EAAE;MACVC,MAAM,EAAE,SAAS;MACjBC,IAAI,EAAE,SAAS;MACfC,CAAC,EAAE,kBAAkB;MACrBC,EAAE,EAAE,aAAa;MACjBC,CAAC,EAAE,MAAM;MACTC,EAAE,EAAE,SAAS;MACbC,CAAC,EAAE,KAAK;MACRC,EAAE,EAAE,QAAQ;MACZC,CAAC,EAAE,IAAI;MACPC,EAAE,EAAE,OAAO;MACXC,CAAC,EAAE,MAAM;MACTC,EAAE,EAAE,SAAS;MACbC,CAAC,EAAE,MAAM;MACTC,EAAE,EAAE;IACR,CAAC;IACDC,aAAa,EAAE,mCAAmC;IAClDC,IAAI,EAAE,SAAAA,CAAUC,KAAK,EAAE;MACnB,OAAO,sBAAsB,CAACC,IAAI,CAACD,KAAK,CAAC;IAC7C,CAAC;IACDE,QAAQ,EAAE,SAAAA,CAAUC,IAAI,EAAE;MACtB,IAAIA,IAAI,GAAG,CAAC,EAAE;QACV,OAAO,SAAS;MACpB,CAAC,MAAM,IAAIA,IAAI,GAAG,EAAE,EAAE;QAClB,OAAO,UAAU;MACrB,CAAC,MAAM,IAAIA,IAAI,GAAG,EAAE,EAAE;QAClB,OAAO,SAAS;MACpB,CAAC,MAAM;QACH,OAAO,UAAU;MACrB;IACJ,CAAC;IACDC,sBAAsB,EAAE,yBAAyB;IACjDC,OAAO,EAAE,SAAAA,CAAUC,MAAM,EAAEC,MAAM,EAAE;MAC/B,QAAQA,MAAM;QACV,KAAK,KAAK;QACV,KAAK,GAAG;QACR,KAAK,GAAG;QACR,KAAK,MAAM;UACP,IAAID,MAAM,KAAK,CAAC,EAAE;YACd,OAAOA,MAAM,GAAG,KAAK;UACzB;UACA,OAAOA,MAAM,GAAG,KAAK;QACzB;UACI,OAAOA,MAAM;MACrB;IACJ,CAAC;IACDE,IAAI,EAAE;MACFC,GAAG,EAAE,CAAC;MAAE;MACRC,GAAG,EAAE,CAAC,CAAE;IACZ;EACJ,CAAC,CAAC;EAEF,OAAOnD,IAAI;AAEf,CAAE,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}