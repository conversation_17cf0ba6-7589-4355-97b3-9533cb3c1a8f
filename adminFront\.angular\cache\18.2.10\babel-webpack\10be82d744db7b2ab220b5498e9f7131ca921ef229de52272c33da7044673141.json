{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { CommonModule } from '@angular/common';\nimport { Component } from '@angular/core';\nimport { tap } from 'rxjs';\nimport { SharedModule } from '../../components/shared.module';\nimport { decodeJwtPayload } from '@nebular/auth';\nimport { LocalStorageService } from 'src/app/shared/services/local-storage.service';\nimport { STORAGE_KEY } from 'src/app/shared/constant/constant';\nlet DetailApprovalWaitingComponent = class DetailApprovalWaitingComponent {\n  constructor(_specialChangeService, _activatedRoute, _ultilityService, _location, message, _validationHelper, _eventService, fileService) {\n    this._specialChangeService = _specialChangeService;\n    this._activatedRoute = _activatedRoute;\n    this._ultilityService = _ultilityService;\n    this._location = _location;\n    this.message = message;\n    this._validationHelper = _validationHelper;\n    this._eventService = _eventService;\n    this.fileService = fileService;\n    this.CType = 1;\n    this.remark = \"\";\n    this.decodeJWT = decodeJwtPayload(LocalStorageService.GetLocalStorage(STORAGE_KEY.TOKEN));\n    this.CID = parseInt(this._activatedRoute.snapshot.paramMap.get(\"id\"));\n    this.buildCaseID = parseInt(this._activatedRoute.snapshot.paramMap.get(\"buildCaseId\"));\n    this._activatedRoute.queryParams.pipe(tap(p => {\n      this.CType = p[\"type\"];\n    })).subscribe();\n  }\n  ngOnInit() {\n    this.getApprovalWaitingById();\n  }\n  getApprovalWaitingById() {\n    this._specialChangeService.apiSpecialChangeGetApproveWaitingByIdPost$Json({\n      body: {\n        CID: this.CID,\n        CType: this.CType.toString()\n      }\n    }).pipe(tap(res => {\n      if (res.StatusCode === 0) {\n        this.approvalWaiting = res.Entries;\n        console.log('載入審核資料:', this.approvalWaiting);\n        console.log('檔案陣列:', this.approvalWaiting.CFileApproves);\n        // 檢查檔案資料\n        if (this.approvalWaiting.CFileApproves) {\n          this.approvalWaiting.CFileApproves.forEach((file, index) => {\n            console.log(`檔案 ${index}:`, {\n              fileName: file.CFileName,\n              hasFile: !!file.CFile,\n              fileLength: file.CFile?.length,\n              isBase64: this.isBase64String(file.CFile || ''),\n              fileStart: file.CFile?.substring(0, 50)\n            });\n          });\n        }\n      }\n    })).subscribe();\n  }\n  // 檔案處理方法 - 參考 FileUploadComponent 的邏輯\n  handleFileClick(file) {\n    const fileName = file.CFileName || '';\n    const displayName = fileName;\n    // 判斷檔案類型\n    const isImageByName = this.isImageFile(fileName);\n    const isPdfByName = this.isPDFString(fileName);\n    const isCadByName = this.isCadString(fileName);\n    console.log('handleFileClick - 處理檔案:', fileName, 'file:', file);\n    // 統一使用 GetFile API 取得檔案（非 base64 才呼叫）\n    const relativePath = file.relativePath || file.CFile;\n    const serverFileName = file.fileName || file.CFileName;\n    if (relativePath && serverFileName && !this.isBase64String(relativePath)) {\n      this.getFileFromServer(relativePath, serverFileName, displayName, isImageByName, isPdfByName, isCadByName);\n    } else {\n      // 如果沒有路徑資訊或是 base64，使用本地檔案處理邏輯作為後備方案\n      console.warn('檔案缺少路徑資訊或為 base64，使用本地處理:', file);\n      this.handleLocalFile(file, isImageByName, isPdfByName, isCadByName);\n    }\n  }\n  // 判斷是否為 base64 字串\n  isBase64String(str) {\n    if (!str) return false;\n    // 簡單檢查是否為 base64 格式\n    return str.startsWith('data:') || str.length > 100 && /^[A-Za-z0-9+/=]+$/.test(str);\n  }\n  // 處理本地檔案的後備方法\n  handleLocalFile(file, isImage, isPdf, isCad) {\n    const fileUrl = file.CFile || file.data;\n    const fileName = file.CFileName || file.fileName || '';\n    if (isImage) {\n      const imageUrl = this.getImageSrc(file);\n      this.openImagePreview(imageUrl, fileName);\n    } else if (isPdf) {\n      // 使用 base64 處理 PDF\n      this.openPdfWithBase64(fileUrl, fileName);\n    } else {\n      this.downloadFileDirectly(fileUrl, fileName);\n    }\n  }\n  // 從後端取得檔案 blob\n  getFileFromServer(relativePath, fileName, displayName, isImage, isPdf, isCad) {\n    this.fileService.getFile(relativePath, fileName).subscribe({\n      next: blob => {\n        const url = URL.createObjectURL(blob);\n        if (isImage) {\n          // 圖片預覽\n          this.openImagePreview(url, displayName);\n        } else if (isPdf) {\n          // PDF 檔案另開視窗顯示\n          this.openPdfInNewWindow(url, displayName);\n        } else {\n          // 其他檔案直接下載\n          this.downloadBlobFile(blob, displayName);\n        }\n        // 清理 URL (延遲清理以確保檔案能正確下載/預覽)\n        setTimeout(() => URL.revokeObjectURL(url), 10000);\n      },\n      error: error => {\n        console.error('取得檔案失敗:', error);\n        this.message.showErrorMSG('取得檔案失敗，請稍後再試');\n      }\n    });\n  }\n  // 在新視窗中開啟 PDF\n  openPdfInNewWindow(blobUrl, fileName) {\n    try {\n      const newWindow = window.open('', '_blank');\n      if (newWindow) {\n        newWindow.document.write(`\n          <html>\n            <head>\n              <title>${fileName}</title>\n              <style>\n                body { margin: 0; padding: 0; }\n                iframe { width: 100vw; height: 100vh; border: none; }\n              </style>\n            </head>\n            <body>\n              <iframe src=\"${blobUrl}\" type=\"application/pdf\"></iframe>\n            </body>\n          </html>\n        `);\n        newWindow.document.close();\n      } else {\n        // 如果彈出視窗被阻擋，直接開啟 URL\n        window.location.href = blobUrl;\n      }\n    } catch (error) {\n      console.error('開啟 PDF 視窗失敗:', error);\n      // 後備方案：直接開啟 URL\n      window.open(blobUrl, '_blank');\n    }\n  }\n  // 使用 base64 開啟 PDF（本地檔案後備方案）\n  openPdfWithBase64(fileData, fileName) {\n    try {\n      let pdfUrl = fileData;\n      // 如果是 base64，需要轉換為 blob URL\n      if (!fileData.startsWith('http')) {\n        if (fileData.startsWith('data:application/pdf')) {\n          pdfUrl = fileData;\n        } else {\n          pdfUrl = `data:application/pdf;base64,${fileData}`;\n        }\n      }\n      const newWindow = window.open('', '_blank');\n      if (newWindow) {\n        newWindow.document.write(`\n          <html>\n            <head>\n              <title>${fileName}</title>\n              <style>\n                body { margin: 0; padding: 0; }\n                iframe { width: 100vw; height: 100vh; border: none; }\n              </style>\n            </head>\n            <body>\n              <iframe src=\"${pdfUrl}\" type=\"application/pdf\"></iframe>\n            </body>\n          </html>\n        `);\n        newWindow.document.close();\n      } else {\n        window.open(pdfUrl, '_blank');\n      }\n    } catch (error) {\n      console.error('打開 PDF 時發生錯誤:', error);\n      this.message.showErrorMSG('打開 PDF 失敗');\n    }\n  }\n  // 下載 blob 檔案\n  downloadBlobFile(blob, fileName) {\n    const url = URL.createObjectURL(blob);\n    const link = document.createElement('a');\n    link.href = url;\n    link.download = fileName;\n    document.body.appendChild(link);\n    link.click();\n    document.body.removeChild(link);\n    // 清理 URL\n    setTimeout(() => URL.revokeObjectURL(url), 1000);\n  }\n  // 下載檔案（處理 base64 或 URL）\n  downloadFileDirectly(fileData, fileName) {\n    if (!fileData) {\n      this.message.showErrorMSG('檔案資料不存在');\n      return;\n    }\n    if (this.isBase64String(fileData)) {\n      this.downloadBase64File(fileData, fileName);\n    } else {\n      // 假設是 URL，嘗試直接下載\n      window.open(fileData, '_blank');\n    }\n  }\n  // 下載 base64 檔案\n  downloadBase64File(base64Data, fileName) {\n    try {\n      // 如果 base64Data 包含 data:type/subtype;base64, 前綴，需要提取\n      const base64Content = base64Data.includes(',') ? base64Data.split(',')[1] : base64Data;\n      // 從檔案名稱判斷 MIME 類型\n      let mimeType = 'application/octet-stream';\n      const extension = fileName.split('.').pop()?.toLowerCase();\n      switch (extension) {\n        case 'pdf':\n          mimeType = 'application/pdf';\n          break;\n        case 'jpg':\n        case 'jpeg':\n          mimeType = 'image/jpeg';\n          break;\n        case 'png':\n          mimeType = 'image/png';\n          break;\n        case 'dwg':\n          mimeType = 'application/acad';\n          break;\n        case 'dxf':\n          mimeType = 'application/dxf';\n          break;\n      }\n      // 將 base64 轉換為 Blob\n      const byteCharacters = atob(base64Content);\n      const byteNumbers = new Array(byteCharacters.length);\n      for (let i = 0; i < byteCharacters.length; i++) {\n        byteNumbers[i] = byteCharacters.charCodeAt(i);\n      }\n      const byteArray = new Uint8Array(byteNumbers);\n      const blob = new Blob([byteArray], {\n        type: mimeType\n      });\n      // 創建下載連結\n      const url = URL.createObjectURL(blob);\n      const link = document.createElement('a');\n      link.href = url;\n      link.download = fileName;\n      document.body.appendChild(link);\n      link.click();\n      document.body.removeChild(link);\n      // 清理 URL\n      setTimeout(() => URL.revokeObjectURL(url), 1000);\n    } catch (error) {\n      console.error('處理 base64 檔案時發生錯誤:', error);\n      this.message.showErrorMSG('處理檔案時發生錯誤');\n    }\n  }\n  // 判斷檔案是否為圖片（根據檔名）\n  isImageFile(fileName) {\n    if (!fileName) return false;\n    const imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'];\n    const extension = fileName.split('.').pop()?.toLowerCase();\n    return imageExtensions.includes(extension || '');\n  }\n  // 判斷是否為 PDF\n  isPDFString(str) {\n    if (str) {\n      return str.toLowerCase().endsWith(\".pdf\");\n    }\n    return false;\n  }\n  // 判斷是否為 CAD 檔案\n  isCadString(str) {\n    if (str) {\n      const lowerStr = str.toLowerCase();\n      return lowerStr.endsWith(\".dwg\") || lowerStr.endsWith(\".dxf\");\n    }\n    return false;\n  }\n  // 取得檔案類型圖標\n  getFileIcon(fileName) {\n    const name = fileName || '';\n    if (this.isImageFile(name)) {\n      return 'fa fa-image text-green-500';\n    } else if (this.isPDFString(name)) {\n      return 'fa fa-file-pdf text-red-500';\n    } else if (this.isCadString(name)) {\n      return 'fa fa-cube text-blue-500';\n    } else {\n      return 'fa fa-file text-gray-500';\n    }\n  }\n  // 取得檔案類型文字\n  getFileTypeText(fileName) {\n    const name = fileName || '';\n    if (this.isImageFile(name)) {\n      return '圖片';\n    } else if (this.isPDFString(name)) {\n      return 'PDF';\n    } else if (this.isCadString(name)) {\n      return 'CAD';\n    } else {\n      return '檔案';\n    }\n  }\n  // 開啟圖片預覽\n  openImagePreview(fileUrl, fileName) {\n    try {\n      const imageUrl = this.getImageSrc(fileUrl);\n      const newWindow = window.open('', '_blank');\n      if (newWindow) {\n        newWindow.document.write(`\n          <html>\n            <head>\n              <title>${fileName}</title>\n              <style>\n                body {\n                  margin: 0;\n                  padding: 20px;\n                  background: #f0f0f0;\n                  display: flex;\n                  flex-direction: column;\n                  align-items: center;\n                  font-family: Arial, sans-serif;\n                }\n                .header {\n                  background: white;\n                  padding: 10px 20px;\n                  border-radius: 8px;\n                  box-shadow: 0 2px 4px rgba(0,0,0,0.1);\n                  margin-bottom: 20px;\n                  font-weight: bold;\n                }\n                .image-container {\n                  background: white;\n                  padding: 20px;\n                  border-radius: 8px;\n                  box-shadow: 0 2px 4px rgba(0,0,0,0.1);\n                  max-width: 90vw;\n                  max-height: 80vh;\n                  overflow: auto;\n                }\n                img {\n                  max-width: 100%;\n                  height: auto;\n                  display: block;\n                }\n              </style>\n            </head>\n            <body>\n              <div class=\"header\">${fileName}</div>\n              <div class=\"image-container\">\n                <img src=\"${imageUrl}\" alt=\"${fileName}\" onload=\"console.log('圖片載入成功')\" onerror=\"console.error('圖片載入失敗'); this.style.display='none'; this.parentElement.innerHTML='<p>圖片載入失敗</p>';\">\n              </div>\n            </body>\n          </html>\n        `);\n        newWindow.document.close();\n      } else {\n        // 如果彈出視窗被阻擋，直接開啟 URL\n        window.open(imageUrl, '_blank');\n      }\n    } catch (error) {\n      console.error('開啟圖片預覽失敗:', error);\n      this.message.showErrorMSG('開啟圖片預覽失敗');\n    }\n  }\n  // 取得正確的圖片 src\n  getImageSrc(fileDataOrUrl) {\n    if (!fileDataOrUrl) return '';\n    // 如果已經是完整的 data URL，直接返回\n    if (fileDataOrUrl.startsWith('data:')) {\n      return fileDataOrUrl;\n    }\n    // 如果是 HTTP URL，直接返回\n    if (fileDataOrUrl.startsWith('http')) {\n      return fileDataOrUrl;\n    }\n    // 如果是純 base64 字串，需要添加前綴\n    return `data:image/jpeg;base64,${fileDataOrUrl}`;\n  }\n  downloadFile(CFile, CFileName) {\n    // if (CFile && CFileName) {\n    //   this._ultilityService.downloadFileFullUrl(\n    //     CFile, CFileName\n    //   )\n    // }\n    window.open(CFile, \"_blank\");\n  }\n  handleAction(isApprove) {\n    if (!isApprove) {\n      this.validation();\n      if (this._validationHelper.errorMessages.length > 0) {\n        this.message.showErrorMSGs(this._validationHelper.errorMessages);\n        return;\n      }\n    }\n    this._specialChangeService.apiSpecialChangeUpdateApproveWaitingPost$Json({\n      body: {\n        CID: this.CID,\n        CType: this.CType,\n        CIsApprove: isApprove,\n        CRemark: this.remark\n      }\n    }).pipe(tap(res => {\n      if (res.StatusCode === 0) {\n        this.message.showSucessMSG(\"執行成功\");\n        this.getApprovalWaitingById();\n        if (this.approvalWaiting.CApproveRecord?.length == 0) {\n          this.approvalWaiting.CApproveRecord?.push({\n            CCreator: this.decodeJWT.userName,\n            CRecordDate: new Date().toISOString(),\n            CRemark: this.remark\n          });\n        }\n        this.remark = \"\";\n      }\n      this.goBack();\n    })).subscribe();\n  }\n  goBack() {\n    this._eventService.push({\n      action: \"GET_BUILDCASE\" /* EEvent.GET_BUILDCASE */,\n      payload: this.buildCaseID\n    });\n    this._location.back();\n  }\n  validation() {\n    this._validationHelper.clear();\n    this._validationHelper.required(\"[備註]\", this.remark);\n  }\n  // 檢查是否有有效的檔案\n  hasValidFiles() {\n    if (!this.approvalWaiting?.CFileApproves) {\n      return false;\n    }\n    return this.approvalWaiting.CFileApproves.some(file => file.CFile && (file.CFileName || file.CFile.length > 0));\n  }\n};\nDetailApprovalWaitingComponent = __decorate([Component({\n  selector: 'app-detail-approval-waiting',\n  standalone: true,\n  imports: [CommonModule, SharedModule],\n  templateUrl: './detail-approval-waiting.component.html',\n  styleUrls: ['./detail-approval-waiting.component.scss']\n})], DetailApprovalWaitingComponent);\nexport { DetailApprovalWaitingComponent };", "map": {"version": 3, "names": ["CommonModule", "Component", "tap", "SharedModule", "decodeJwtPayload", "LocalStorageService", "STORAGE_KEY", "DetailApprovalWaitingComponent", "constructor", "_specialChangeService", "_activatedRoute", "_ultilityService", "_location", "message", "_validationHelper", "_eventService", "fileService", "CType", "remark", "decodeJWT", "GetLocalStorage", "TOKEN", "CID", "parseInt", "snapshot", "paramMap", "get", "buildCaseID", "queryParams", "pipe", "p", "subscribe", "ngOnInit", "getApprovalWaitingById", "apiSpecialChangeGetApproveWaitingByIdPost$Json", "body", "toString", "res", "StatusCode", "approvalWaiting", "Entries", "console", "log", "CFileApproves", "for<PERSON>ach", "file", "index", "fileName", "CFileName", "hasFile", "CFile", "fileLength", "length", "isBase64", "isBase64String", "fileStart", "substring", "handleFileClick", "displayName", "isImageByName", "isImageFile", "isPdfByName", "isPDFString", "isCadByName", "isCadString", "relativePath", "serverFileName", "getFileFromServer", "warn", "handleLocalFile", "str", "startsWith", "test", "isImage", "isPdf", "isCad", "fileUrl", "data", "imageUrl", "getImageSrc", "openImagePreview", "openPdfWithBase64", "downloadFileDirectly", "getFile", "next", "blob", "url", "URL", "createObjectURL", "openPdfInNewWindow", "downloadBlobFile", "setTimeout", "revokeObjectURL", "error", "showErrorMSG", "blobUrl", "newWindow", "window", "open", "document", "write", "close", "location", "href", "fileData", "pdfUrl", "link", "createElement", "download", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "downloadBase64File", "base64Data", "base64Content", "includes", "split", "mimeType", "extension", "pop", "toLowerCase", "byteCharacters", "atob", "byteNumbers", "Array", "i", "charCodeAt", "byteArray", "Uint8Array", "Blob", "type", "imageExtensions", "endsWith", "lowerStr", "getFileIcon", "name", "getFileTypeText", "fileDataOrUrl", "downloadFile", "handleAction", "isApprove", "validation", "errorMessages", "showErrorMSGs", "apiSpecialChangeUpdateApproveWaitingPost$Json", "CIsApprove", "CRemark", "showSucessMSG", "CApproveRecord", "push", "CCreator", "userName", "CRecordDate", "Date", "toISOString", "goBack", "action", "payload", "back", "clear", "required", "hasValidFiles", "some", "__decorate", "selector", "standalone", "imports", "templateUrl", "styleUrls"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\approve-waiting\\detail-approval-waiting\\detail-approval-waiting.component.ts"], "sourcesContent": ["import { CommonModule, Location } from '@angular/common';\r\nimport { Component, OnInit } from '@angular/core';\r\nimport { ActivatedRoute } from '@angular/router';\r\nimport { tap } from 'rxjs';\r\nimport { ApproveWaitingByIdRes } from 'src/services/api/models';\r\nimport { SpecialChangeService } from 'src/services/api/services';\r\nimport { SharedModule } from '../../components/shared.module';\r\nimport { UtilityService } from 'src/app/shared/services/utility.service';\r\nimport { decodeJwtPayload } from '@nebular/auth';\r\nimport { LocalStorageService } from 'src/app/shared/services/local-storage.service';\r\nimport { STORAGE_KEY } from 'src/app/shared/constant/constant';\r\nimport { BaseFilePipe } from 'src/app/@theme/pipes/base-file.pipe';\r\nimport { MessageService } from 'src/app/shared/services/message.service';\r\nimport { ValidationHelper } from 'src/app/shared/helper/validationHelper';\r\nimport { EEvent, EventService } from 'src/app/shared/services/event.service';\r\nimport { FileUploadComponent } from '../../components/file-upload/file-upload.component';\r\nimport { FileService } from 'src/services/api/services/File.service';\r\n\r\n@Component({\r\n  selector: 'app-detail-approval-waiting',\r\n  standalone: true,\r\n  imports: [\r\n    CommonModule,\r\n    SharedModule\r\n  ],\r\n  templateUrl: './detail-approval-waiting.component.html',\r\n  styleUrls: ['./detail-approval-waiting.component.scss']\r\n})\r\nexport class DetailApprovalWaitingComponent implements OnInit {\r\n\r\n  CType: number = 1;\r\n  CID: number\r\n  remark: string = \"\"\r\n  buildCaseID: number\r\n\r\n  approvalWaiting: ApproveWaitingByIdRes\r\n  decodeJWT: any\r\n  constructor(\r\n    private _specialChangeService: SpecialChangeService,\r\n    private _activatedRoute: ActivatedRoute,\r\n    private _ultilityService: UtilityService,\r\n    private _location: Location,\r\n    private message: MessageService,\r\n    private _validationHelper: ValidationHelper,\r\n    private _eventService: EventService,\r\n    private fileService: FileService,\r\n  ) {\r\n    this.decodeJWT = decodeJwtPayload(LocalStorageService.GetLocalStorage(STORAGE_KEY.TOKEN))\r\n    this.CID = parseInt(this._activatedRoute.snapshot.paramMap!.get(\"id\")!);\r\n    this.buildCaseID = parseInt(this._activatedRoute.snapshot.paramMap!.get(\"buildCaseId\")!)\r\n    this._activatedRoute.queryParams.pipe(\r\n      tap(p => {\r\n        this.CType = p[\"type\"]\r\n      })\r\n    ).subscribe();\r\n  }\r\n\r\n  ngOnInit(): void {\r\n    this.getApprovalWaitingById()\r\n  }\r\n  getApprovalWaitingById() {\r\n    this._specialChangeService.apiSpecialChangeGetApproveWaitingByIdPost$Json({\r\n      body: {\r\n        CID: this.CID,\r\n        CType: this.CType.toString()\r\n      }\r\n    }).pipe(\r\n      tap(res => {\r\n        if (res.StatusCode === 0) {\r\n          this.approvalWaiting = res.Entries!\r\n          console.log('載入審核資料:', this.approvalWaiting);\r\n          console.log('檔案陣列:', this.approvalWaiting.CFileApproves);\r\n          \r\n          // 檢查檔案資料\r\n          if (this.approvalWaiting.CFileApproves) {\r\n            this.approvalWaiting.CFileApproves.forEach((file, index) => {\r\n              console.log(`檔案 ${index}:`, {\r\n                fileName: file.CFileName,\r\n                hasFile: !!file.CFile,\r\n                fileLength: file.CFile?.length,\r\n                isBase64: this.isBase64String(file.CFile || ''),\r\n                fileStart: file.CFile?.substring(0, 50)\r\n              });\r\n            });\r\n          }\r\n        }\r\n      })\r\n    ).subscribe();\r\n  }\r\n  // 檔案處理方法 - 參考 FileUploadComponent 的邏輯\r\n  handleFileClick(file: any) {\r\n    const fileName = file.CFileName || '';\r\n    const displayName = fileName;\r\n\r\n    // 判斷檔案類型\r\n    const isImageByName = this.isImageFile(fileName);\r\n    const isPdfByName = this.isPDFString(fileName);\r\n    const isCadByName = this.isCadString(fileName);\r\n\r\n    console.log('handleFileClick - 處理檔案:', fileName, 'file:', file);\r\n\r\n    // 統一使用 GetFile API 取得檔案（非 base64 才呼叫）\r\n    const relativePath = file.relativePath || file.CFile;\r\n    const serverFileName = file.fileName || file.CFileName;\r\n\r\n    if (relativePath && serverFileName && !this.isBase64String(relativePath)) {\r\n      this.getFileFromServer(relativePath, serverFileName, displayName, isImageByName, isPdfByName, isCadByName);\r\n    } else {\r\n      // 如果沒有路徑資訊或是 base64，使用本地檔案處理邏輯作為後備方案\r\n      console.warn('檔案缺少路徑資訊或為 base64，使用本地處理:', file);\r\n      this.handleLocalFile(file, isImageByName, isPdfByName, isCadByName);\r\n    }\r\n  }\r\n\r\n  // 判斷是否為 base64 字串\r\n  private isBase64String(str: string): boolean {\r\n    if (!str) return false;\r\n    // 簡單檢查是否為 base64 格式\r\n    return str.startsWith('data:') || (str.length > 100 && /^[A-Za-z0-9+/=]+$/.test(str));\r\n  }\r\n  // 處理本地檔案的後備方法\r\n  private handleLocalFile(file: any, isImage: boolean, isPdf: boolean, isCad: boolean) {\r\n    const fileUrl = file.CFile || file.data;\r\n    const fileName = file.CFileName || file.fileName || '';\r\n\r\n    if (isImage) {\r\n      const imageUrl = this.getImageSrc(file);\r\n      this.openImagePreview(imageUrl, fileName);\r\n    } else if (isPdf) {\r\n      // 使用 base64 處理 PDF\r\n      this.openPdfWithBase64(fileUrl, fileName);\r\n    } else {\r\n      this.downloadFileDirectly(fileUrl, fileName);\r\n    }\r\n  }\r\n\r\n  // 從後端取得檔案 blob\r\n  private getFileFromServer(relativePath: string, fileName: string, displayName: string, isImage: boolean, isPdf: boolean, isCad: boolean) {\r\n    this.fileService.getFile(relativePath, fileName).subscribe({\r\n      next: (blob: Blob) => {\r\n        const url = URL.createObjectURL(blob);\r\n\r\n        if (isImage) {\r\n          // 圖片預覽\r\n          this.openImagePreview(url, displayName);\r\n        } else if (isPdf) {\r\n          // PDF 檔案另開視窗顯示\r\n          this.openPdfInNewWindow(url, displayName);\r\n        } else {\r\n          // 其他檔案直接下載\r\n          this.downloadBlobFile(blob, displayName);\r\n        }\r\n\r\n        // 清理 URL (延遲清理以確保檔案能正確下載/預覽)\r\n        setTimeout(() => URL.revokeObjectURL(url), 10000);\r\n      },\r\n      error: (error) => {\r\n        console.error('取得檔案失敗:', error);\r\n        this.message.showErrorMSG('取得檔案失敗，請稍後再試');\r\n      }\r\n    });\r\n  }\r\n\r\n  // 在新視窗中開啟 PDF\r\n  private openPdfInNewWindow(blobUrl: string, fileName: string) {\r\n    try {\r\n      const newWindow = window.open('', '_blank');\r\n      if (newWindow) {\r\n        newWindow.document.write(`\r\n          <html>\r\n            <head>\r\n              <title>${fileName}</title>\r\n              <style>\r\n                body { margin: 0; padding: 0; }\r\n                iframe { width: 100vw; height: 100vh; border: none; }\r\n              </style>\r\n            </head>\r\n            <body>\r\n              <iframe src=\"${blobUrl}\" type=\"application/pdf\"></iframe>\r\n            </body>\r\n          </html>\r\n        `);\r\n        newWindow.document.close();\r\n      } else {\r\n        // 如果彈出視窗被阻擋，直接開啟 URL\r\n        window.location.href = blobUrl;\r\n      }\r\n    } catch (error) {\r\n      console.error('開啟 PDF 視窗失敗:', error);\r\n      // 後備方案：直接開啟 URL\r\n      window.open(blobUrl, '_blank');\r\n    }\r\n  }\r\n\r\n  // 使用 base64 開啟 PDF（本地檔案後備方案）\r\n  private openPdfWithBase64(fileData: string, fileName: string) {\r\n    try {\r\n      let pdfUrl = fileData;\r\n\r\n      // 如果是 base64，需要轉換為 blob URL\r\n      if (!fileData.startsWith('http')) {\r\n        if (fileData.startsWith('data:application/pdf')) {\r\n          pdfUrl = fileData;\r\n        } else {\r\n          pdfUrl = `data:application/pdf;base64,${fileData}`;\r\n        }\r\n      }\r\n\r\n      const newWindow = window.open('', '_blank');\r\n      if (newWindow) {\r\n        newWindow.document.write(`\r\n          <html>\r\n            <head>\r\n              <title>${fileName}</title>\r\n              <style>\r\n                body { margin: 0; padding: 0; }\r\n                iframe { width: 100vw; height: 100vh; border: none; }\r\n              </style>\r\n            </head>\r\n            <body>\r\n              <iframe src=\"${pdfUrl}\" type=\"application/pdf\"></iframe>\r\n            </body>\r\n          </html>\r\n        `);\r\n        newWindow.document.close();\r\n      } else {\r\n        window.open(pdfUrl, '_blank');\r\n      }\r\n    } catch (error) {\r\n      console.error('打開 PDF 時發生錯誤:', error);\r\n      this.message.showErrorMSG('打開 PDF 失敗');\r\n    }\r\n  }\r\n\r\n  // 下載 blob 檔案\r\n  private downloadBlobFile(blob: Blob, fileName: string) {\r\n    const url = URL.createObjectURL(blob);\r\n    const link = document.createElement('a');\r\n    link.href = url;\r\n    link.download = fileName;\r\n    document.body.appendChild(link);\r\n    link.click();\r\n    document.body.removeChild(link);\r\n\r\n    // 清理 URL\r\n    setTimeout(() => URL.revokeObjectURL(url), 1000);\r\n  }\r\n\r\n  // 下載檔案（處理 base64 或 URL）\r\n  private downloadFileDirectly(fileData: string, fileName: string) {\r\n    if (!fileData) {\r\n      this.message.showErrorMSG('檔案資料不存在');\r\n      return;\r\n    }\r\n\r\n    if (this.isBase64String(fileData)) {\r\n      this.downloadBase64File(fileData, fileName);\r\n    } else {\r\n      // 假設是 URL，嘗試直接下載\r\n      window.open(fileData, '_blank');\r\n    }\r\n  }\r\n\r\n  // 下載 base64 檔案\r\n  private downloadBase64File(base64Data: string, fileName: string) {\r\n    try {\r\n      // 如果 base64Data 包含 data:type/subtype;base64, 前綴，需要提取\r\n      const base64Content = base64Data.includes(',') ? base64Data.split(',')[1] : base64Data;\r\n\r\n      // 從檔案名稱判斷 MIME 類型\r\n      let mimeType = 'application/octet-stream';\r\n      const extension = fileName.split('.').pop()?.toLowerCase();\r\n\r\n      switch (extension) {\r\n        case 'pdf':\r\n          mimeType = 'application/pdf';\r\n          break;\r\n        case 'jpg':\r\n        case 'jpeg':\r\n          mimeType = 'image/jpeg';\r\n          break;\r\n        case 'png':\r\n          mimeType = 'image/png';\r\n          break;\r\n        case 'dwg':\r\n          mimeType = 'application/acad';\r\n          break;\r\n        case 'dxf':\r\n          mimeType = 'application/dxf';\r\n          break;\r\n      }\r\n\r\n      // 將 base64 轉換為 Blob\r\n      const byteCharacters = atob(base64Content);\r\n      const byteNumbers = new Array(byteCharacters.length);\r\n      for (let i = 0; i < byteCharacters.length; i++) {\r\n        byteNumbers[i] = byteCharacters.charCodeAt(i);\r\n      }\r\n      const byteArray = new Uint8Array(byteNumbers);\r\n      const blob = new Blob([byteArray], { type: mimeType });\r\n\r\n      // 創建下載連結\r\n      const url = URL.createObjectURL(blob);\r\n      const link = document.createElement('a');\r\n      link.href = url;\r\n      link.download = fileName;\r\n      document.body.appendChild(link);\r\n      link.click();\r\n      document.body.removeChild(link);\r\n\r\n      // 清理 URL\r\n      setTimeout(() => URL.revokeObjectURL(url), 1000);\r\n    } catch (error) {\r\n      console.error('處理 base64 檔案時發生錯誤:', error);\r\n      this.message.showErrorMSG('處理檔案時發生錯誤');\r\n    }\r\n  }\r\n\r\n  // 判斷檔案是否為圖片（根據檔名）\r\n  isImageFile(fileName: string): boolean {\r\n    if (!fileName) return false;\r\n    const imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'];\r\n    const extension = fileName.split('.').pop()?.toLowerCase();\r\n    return imageExtensions.includes(extension || '');\r\n  }\r\n\r\n  // 判斷是否為 PDF\r\n  isPDFString(str: string): boolean {\r\n    if (str) {\r\n      return str.toLowerCase().endsWith(\".pdf\");\r\n    }\r\n    return false;\r\n  }\r\n\r\n  // 判斷是否為 CAD 檔案\r\n  isCadString(str: string): boolean {\r\n    if (str) {\r\n      const lowerStr = str.toLowerCase();\r\n      return lowerStr.endsWith(\".dwg\") || lowerStr.endsWith(\".dxf\");\r\n    }\r\n    return false;\r\n  }\r\n  // 取得檔案類型圖標\r\n  getFileIcon(fileName: string | null | undefined): string {\r\n    const name = fileName || '';\r\n    if (this.isImageFile(name)) {\r\n      return 'fa fa-image text-green-500';\r\n    } else if (this.isPDFString(name)) {\r\n      return 'fa fa-file-pdf text-red-500';\r\n    } else if (this.isCadString(name)) {\r\n      return 'fa fa-cube text-blue-500';\r\n    } else {\r\n      return 'fa fa-file text-gray-500';\r\n    }\r\n  }\r\n\r\n  // 取得檔案類型文字\r\n  getFileTypeText(fileName: string | null | undefined): string {\r\n    const name = fileName || '';\r\n    if (this.isImageFile(name)) {\r\n      return '圖片';\r\n    } else if (this.isPDFString(name)) {\r\n      return 'PDF';\r\n    } else if (this.isCadString(name)) {\r\n      return 'CAD';\r\n    } else {\r\n      return '檔案';\r\n    }\r\n  }\r\n\r\n  // 開啟圖片預覽\r\n  private openImagePreview(fileUrl: string, fileName: string) {\r\n    try {\r\n      const imageUrl = this.getImageSrc(fileUrl);\r\n      const newWindow = window.open('', '_blank');\r\n      if (newWindow) {\r\n        newWindow.document.write(`\r\n          <html>\r\n            <head>\r\n              <title>${fileName}</title>\r\n              <style>\r\n                body {\r\n                  margin: 0;\r\n                  padding: 20px;\r\n                  background: #f0f0f0;\r\n                  display: flex;\r\n                  flex-direction: column;\r\n                  align-items: center;\r\n                  font-family: Arial, sans-serif;\r\n                }\r\n                .header {\r\n                  background: white;\r\n                  padding: 10px 20px;\r\n                  border-radius: 8px;\r\n                  box-shadow: 0 2px 4px rgba(0,0,0,0.1);\r\n                  margin-bottom: 20px;\r\n                  font-weight: bold;\r\n                }\r\n                .image-container {\r\n                  background: white;\r\n                  padding: 20px;\r\n                  border-radius: 8px;\r\n                  box-shadow: 0 2px 4px rgba(0,0,0,0.1);\r\n                  max-width: 90vw;\r\n                  max-height: 80vh;\r\n                  overflow: auto;\r\n                }\r\n                img {\r\n                  max-width: 100%;\r\n                  height: auto;\r\n                  display: block;\r\n                }\r\n              </style>\r\n            </head>\r\n            <body>\r\n              <div class=\"header\">${fileName}</div>\r\n              <div class=\"image-container\">\r\n                <img src=\"${imageUrl}\" alt=\"${fileName}\" onload=\"console.log('圖片載入成功')\" onerror=\"console.error('圖片載入失敗'); this.style.display='none'; this.parentElement.innerHTML='<p>圖片載入失敗</p>';\">\r\n              </div>\r\n            </body>\r\n          </html>\r\n        `);\r\n        newWindow.document.close();\r\n      } else {\r\n        // 如果彈出視窗被阻擋，直接開啟 URL\r\n        window.open(imageUrl, '_blank');\r\n      }\r\n    } catch (error) {\r\n      console.error('開啟圖片預覽失敗:', error);\r\n      this.message.showErrorMSG('開啟圖片預覽失敗');\r\n    }\r\n  }\r\n  // 取得正確的圖片 src\r\n  private getImageSrc(fileDataOrUrl: string): string {\r\n    if (!fileDataOrUrl) return '';\r\n\r\n    // 如果已經是完整的 data URL，直接返回\r\n    if (fileDataOrUrl.startsWith('data:')) {\r\n      return fileDataOrUrl;\r\n    }\r\n\r\n    // 如果是 HTTP URL，直接返回\r\n    if (fileDataOrUrl.startsWith('http')) {\r\n      return fileDataOrUrl;\r\n    }\r\n\r\n    // 如果是純 base64 字串，需要添加前綴\r\n    return `data:image/jpeg;base64,${fileDataOrUrl}`;\r\n  }\r\n\r\n  downloadFile(CFile: any, CFileName: any) {\r\n    // if (CFile && CFileName) {\r\n    //   this._ultilityService.downloadFileFullUrl(\r\n    //     CFile, CFileName\r\n    //   )\r\n    // }\r\n    window.open(CFile, \"_blank\");\r\n  }\r\n\r\n  handleAction(isApprove: boolean) {\r\n    if (!isApprove) {\r\n      this.validation()\r\n      if (this._validationHelper.errorMessages.length > 0) {\r\n        this.message.showErrorMSGs(this._validationHelper.errorMessages);\r\n        return\r\n      }\r\n    }\r\n    this._specialChangeService.apiSpecialChangeUpdateApproveWaitingPost$Json({\r\n      body: {\r\n        CID: this.CID,\r\n        CType: this.CType,\r\n        CIsApprove: isApprove,\r\n        CRemark: this.remark\r\n      }\r\n    }).pipe(\r\n      tap(res => {\r\n        if (res.StatusCode === 0) {\r\n          this.message.showSucessMSG(\"執行成功\");\r\n          this.getApprovalWaitingById()\r\n          if (this.approvalWaiting.CApproveRecord?.length == 0) {\r\n            this.approvalWaiting.CApproveRecord?.push({\r\n              CCreator: this.decodeJWT.userName,\r\n              CRecordDate: new Date().toISOString(),\r\n              CRemark: this.remark\r\n            })\r\n          }\r\n          this.remark = \"\"\r\n        }\r\n        this.goBack();\r\n      })\r\n    ).subscribe();\r\n  }\r\n\r\n  goBack() {\r\n    this._eventService.push({\r\n      action: EEvent.GET_BUILDCASE,\r\n      payload: this.buildCaseID\r\n    })\r\n    this._location.back()\r\n  }\r\n\r\n  validation() {\r\n    this._validationHelper.clear();\r\n    this._validationHelper.required(\"[備註]\", this.remark)\r\n  }\r\n\r\n  // 檢查是否有有效的檔案\r\n  hasValidFiles(): boolean {\r\n    if (!this.approvalWaiting?.CFileApproves) {\r\n      return false;\r\n    }\r\n    \r\n    return this.approvalWaiting.CFileApproves.some(file => \r\n      file.CFile && (file.CFileName || file.CFile.length > 0)\r\n    );\r\n  }\r\n}\r\n"], "mappings": ";AAAA,SAASA,YAAY,QAAkB,iBAAiB;AACxD,SAASC,SAAS,QAAgB,eAAe;AAEjD,SAASC,GAAG,QAAQ,MAAM;AAG1B,SAASC,YAAY,QAAQ,gCAAgC;AAE7D,SAASC,gBAAgB,QAAQ,eAAe;AAChD,SAASC,mBAAmB,QAAQ,+CAA+C;AACnF,SAASC,WAAW,QAAQ,kCAAkC;AAkBvD,IAAMC,8BAA8B,GAApC,MAAMA,8BAA8B;EASzCC,YACUC,qBAA2C,EAC3CC,eAA+B,EAC/BC,gBAAgC,EAChCC,SAAmB,EACnBC,OAAuB,EACvBC,iBAAmC,EACnCC,aAA2B,EAC3BC,WAAwB;IAPxB,KAAAP,qBAAqB,GAArBA,qBAAqB;IACrB,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,SAAS,GAATA,SAAS;IACT,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAC,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,WAAW,GAAXA,WAAW;IAfrB,KAAAC,KAAK,GAAW,CAAC;IAEjB,KAAAC,MAAM,GAAW,EAAE;IAejB,IAAI,CAACC,SAAS,GAAGf,gBAAgB,CAACC,mBAAmB,CAACe,eAAe,CAACd,WAAW,CAACe,KAAK,CAAC,CAAC;IACzF,IAAI,CAACC,GAAG,GAAGC,QAAQ,CAAC,IAAI,CAACb,eAAe,CAACc,QAAQ,CAACC,QAAS,CAACC,GAAG,CAAC,IAAI,CAAE,CAAC;IACvE,IAAI,CAACC,WAAW,GAAGJ,QAAQ,CAAC,IAAI,CAACb,eAAe,CAACc,QAAQ,CAACC,QAAS,CAACC,GAAG,CAAC,aAAa,CAAE,CAAC;IACxF,IAAI,CAAChB,eAAe,CAACkB,WAAW,CAACC,IAAI,CACnC3B,GAAG,CAAC4B,CAAC,IAAG;MACN,IAAI,CAACb,KAAK,GAAGa,CAAC,CAAC,MAAM,CAAC;IACxB,CAAC,CAAC,CACH,CAACC,SAAS,EAAE;EACf;EAEAC,QAAQA,CAAA;IACN,IAAI,CAACC,sBAAsB,EAAE;EAC/B;EACAA,sBAAsBA,CAAA;IACpB,IAAI,CAACxB,qBAAqB,CAACyB,8CAA8C,CAAC;MACxEC,IAAI,EAAE;QACJb,GAAG,EAAE,IAAI,CAACA,GAAG;QACbL,KAAK,EAAE,IAAI,CAACA,KAAK,CAACmB,QAAQ;;KAE7B,CAAC,CAACP,IAAI,CACL3B,GAAG,CAACmC,GAAG,IAAG;MACR,IAAIA,GAAG,CAACC,UAAU,KAAK,CAAC,EAAE;QACxB,IAAI,CAACC,eAAe,GAAGF,GAAG,CAACG,OAAQ;QACnCC,OAAO,CAACC,GAAG,CAAC,SAAS,EAAE,IAAI,CAACH,eAAe,CAAC;QAC5CE,OAAO,CAACC,GAAG,CAAC,OAAO,EAAE,IAAI,CAACH,eAAe,CAACI,aAAa,CAAC;QAExD;QACA,IAAI,IAAI,CAACJ,eAAe,CAACI,aAAa,EAAE;UACtC,IAAI,CAACJ,eAAe,CAACI,aAAa,CAACC,OAAO,CAAC,CAACC,IAAI,EAAEC,KAAK,KAAI;YACzDL,OAAO,CAACC,GAAG,CAAC,MAAMI,KAAK,GAAG,EAAE;cAC1BC,QAAQ,EAAEF,IAAI,CAACG,SAAS;cACxBC,OAAO,EAAE,CAAC,CAACJ,IAAI,CAACK,KAAK;cACrBC,UAAU,EAAEN,IAAI,CAACK,KAAK,EAAEE,MAAM;cAC9BC,QAAQ,EAAE,IAAI,CAACC,cAAc,CAACT,IAAI,CAACK,KAAK,IAAI,EAAE,CAAC;cAC/CK,SAAS,EAAEV,IAAI,CAACK,KAAK,EAAEM,SAAS,CAAC,CAAC,EAAE,EAAE;aACvC,CAAC;UACJ,CAAC,CAAC;QACJ;MACF;IACF,CAAC,CAAC,CACH,CAACzB,SAAS,EAAE;EACf;EACA;EACA0B,eAAeA,CAACZ,IAAS;IACvB,MAAME,QAAQ,GAAGF,IAAI,CAACG,SAAS,IAAI,EAAE;IACrC,MAAMU,WAAW,GAAGX,QAAQ;IAE5B;IACA,MAAMY,aAAa,GAAG,IAAI,CAACC,WAAW,CAACb,QAAQ,CAAC;IAChD,MAAMc,WAAW,GAAG,IAAI,CAACC,WAAW,CAACf,QAAQ,CAAC;IAC9C,MAAMgB,WAAW,GAAG,IAAI,CAACC,WAAW,CAACjB,QAAQ,CAAC;IAE9CN,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEK,QAAQ,EAAE,OAAO,EAAEF,IAAI,CAAC;IAE/D;IACA,MAAMoB,YAAY,GAAGpB,IAAI,CAACoB,YAAY,IAAIpB,IAAI,CAACK,KAAK;IACpD,MAAMgB,cAAc,GAAGrB,IAAI,CAACE,QAAQ,IAAIF,IAAI,CAACG,SAAS;IAEtD,IAAIiB,YAAY,IAAIC,cAAc,IAAI,CAAC,IAAI,CAACZ,cAAc,CAACW,YAAY,CAAC,EAAE;MACxE,IAAI,CAACE,iBAAiB,CAACF,YAAY,EAAEC,cAAc,EAAER,WAAW,EAAEC,aAAa,EAAEE,WAAW,EAAEE,WAAW,CAAC;IAC5G,CAAC,MAAM;MACL;MACAtB,OAAO,CAAC2B,IAAI,CAAC,2BAA2B,EAAEvB,IAAI,CAAC;MAC/C,IAAI,CAACwB,eAAe,CAACxB,IAAI,EAAEc,aAAa,EAAEE,WAAW,EAAEE,WAAW,CAAC;IACrE;EACF;EAEA;EACQT,cAAcA,CAACgB,GAAW;IAChC,IAAI,CAACA,GAAG,EAAE,OAAO,KAAK;IACtB;IACA,OAAOA,GAAG,CAACC,UAAU,CAAC,OAAO,CAAC,IAAKD,GAAG,CAAClB,MAAM,GAAG,GAAG,IAAI,mBAAmB,CAACoB,IAAI,CAACF,GAAG,CAAE;EACvF;EACA;EACQD,eAAeA,CAACxB,IAAS,EAAE4B,OAAgB,EAAEC,KAAc,EAAEC,KAAc;IACjF,MAAMC,OAAO,GAAG/B,IAAI,CAACK,KAAK,IAAIL,IAAI,CAACgC,IAAI;IACvC,MAAM9B,QAAQ,GAAGF,IAAI,CAACG,SAAS,IAAIH,IAAI,CAACE,QAAQ,IAAI,EAAE;IAEtD,IAAI0B,OAAO,EAAE;MACX,MAAMK,QAAQ,GAAG,IAAI,CAACC,WAAW,CAAClC,IAAI,CAAC;MACvC,IAAI,CAACmC,gBAAgB,CAACF,QAAQ,EAAE/B,QAAQ,CAAC;IAC3C,CAAC,MAAM,IAAI2B,KAAK,EAAE;MAChB;MACA,IAAI,CAACO,iBAAiB,CAACL,OAAO,EAAE7B,QAAQ,CAAC;IAC3C,CAAC,MAAM;MACL,IAAI,CAACmC,oBAAoB,CAACN,OAAO,EAAE7B,QAAQ,CAAC;IAC9C;EACF;EAEA;EACQoB,iBAAiBA,CAACF,YAAoB,EAAElB,QAAgB,EAAEW,WAAmB,EAAEe,OAAgB,EAAEC,KAAc,EAAEC,KAAc;IACrI,IAAI,CAAC3D,WAAW,CAACmE,OAAO,CAAClB,YAAY,EAAElB,QAAQ,CAAC,CAAChB,SAAS,CAAC;MACzDqD,IAAI,EAAGC,IAAU,IAAI;QACnB,MAAMC,GAAG,GAAGC,GAAG,CAACC,eAAe,CAACH,IAAI,CAAC;QAErC,IAAIZ,OAAO,EAAE;UACX;UACA,IAAI,CAACO,gBAAgB,CAACM,GAAG,EAAE5B,WAAW,CAAC;QACzC,CAAC,MAAM,IAAIgB,KAAK,EAAE;UAChB;UACA,IAAI,CAACe,kBAAkB,CAACH,GAAG,EAAE5B,WAAW,CAAC;QAC3C,CAAC,MAAM;UACL;UACA,IAAI,CAACgC,gBAAgB,CAACL,IAAI,EAAE3B,WAAW,CAAC;QAC1C;QAEA;QACAiC,UAAU,CAAC,MAAMJ,GAAG,CAACK,eAAe,CAACN,GAAG,CAAC,EAAE,KAAK,CAAC;MACnD,CAAC;MACDO,KAAK,EAAGA,KAAK,IAAI;QACfpD,OAAO,CAACoD,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;QAC/B,IAAI,CAAChF,OAAO,CAACiF,YAAY,CAAC,cAAc,CAAC;MAC3C;KACD,CAAC;EACJ;EAEA;EACQL,kBAAkBA,CAACM,OAAe,EAAEhD,QAAgB;IAC1D,IAAI;MACF,MAAMiD,SAAS,GAAGC,MAAM,CAACC,IAAI,CAAC,EAAE,EAAE,QAAQ,CAAC;MAC3C,IAAIF,SAAS,EAAE;QACbA,SAAS,CAACG,QAAQ,CAACC,KAAK,CAAC;;;uBAGVrD,QAAQ;;;;;;;6BAOFgD,OAAO;;;SAG3B,CAAC;QACFC,SAAS,CAACG,QAAQ,CAACE,KAAK,EAAE;MAC5B,CAAC,MAAM;QACL;QACAJ,MAAM,CAACK,QAAQ,CAACC,IAAI,GAAGR,OAAO;MAChC;IACF,CAAC,CAAC,OAAOF,KAAK,EAAE;MACdpD,OAAO,CAACoD,KAAK,CAAC,cAAc,EAAEA,KAAK,CAAC;MACpC;MACAI,MAAM,CAACC,IAAI,CAACH,OAAO,EAAE,QAAQ,CAAC;IAChC;EACF;EAEA;EACQd,iBAAiBA,CAACuB,QAAgB,EAAEzD,QAAgB;IAC1D,IAAI;MACF,IAAI0D,MAAM,GAAGD,QAAQ;MAErB;MACA,IAAI,CAACA,QAAQ,CAACjC,UAAU,CAAC,MAAM,CAAC,EAAE;QAChC,IAAIiC,QAAQ,CAACjC,UAAU,CAAC,sBAAsB,CAAC,EAAE;UAC/CkC,MAAM,GAAGD,QAAQ;QACnB,CAAC,MAAM;UACLC,MAAM,GAAG,+BAA+BD,QAAQ,EAAE;QACpD;MACF;MAEA,MAAMR,SAAS,GAAGC,MAAM,CAACC,IAAI,CAAC,EAAE,EAAE,QAAQ,CAAC;MAC3C,IAAIF,SAAS,EAAE;QACbA,SAAS,CAACG,QAAQ,CAACC,KAAK,CAAC;;;uBAGVrD,QAAQ;;;;;;;6BAOF0D,MAAM;;;SAG1B,CAAC;QACFT,SAAS,CAACG,QAAQ,CAACE,KAAK,EAAE;MAC5B,CAAC,MAAM;QACLJ,MAAM,CAACC,IAAI,CAACO,MAAM,EAAE,QAAQ,CAAC;MAC/B;IACF,CAAC,CAAC,OAAOZ,KAAK,EAAE;MACdpD,OAAO,CAACoD,KAAK,CAAC,eAAe,EAAEA,KAAK,CAAC;MACrC,IAAI,CAAChF,OAAO,CAACiF,YAAY,CAAC,WAAW,CAAC;IACxC;EACF;EAEA;EACQJ,gBAAgBA,CAACL,IAAU,EAAEtC,QAAgB;IACnD,MAAMuC,GAAG,GAAGC,GAAG,CAACC,eAAe,CAACH,IAAI,CAAC;IACrC,MAAMqB,IAAI,GAAGP,QAAQ,CAACQ,aAAa,CAAC,GAAG,CAAC;IACxCD,IAAI,CAACH,IAAI,GAAGjB,GAAG;IACfoB,IAAI,CAACE,QAAQ,GAAG7D,QAAQ;IACxBoD,QAAQ,CAAChE,IAAI,CAAC0E,WAAW,CAACH,IAAI,CAAC;IAC/BA,IAAI,CAACI,KAAK,EAAE;IACZX,QAAQ,CAAChE,IAAI,CAAC4E,WAAW,CAACL,IAAI,CAAC;IAE/B;IACAf,UAAU,CAAC,MAAMJ,GAAG,CAACK,eAAe,CAACN,GAAG,CAAC,EAAE,IAAI,CAAC;EAClD;EAEA;EACQJ,oBAAoBA,CAACsB,QAAgB,EAAEzD,QAAgB;IAC7D,IAAI,CAACyD,QAAQ,EAAE;MACb,IAAI,CAAC3F,OAAO,CAACiF,YAAY,CAAC,SAAS,CAAC;MACpC;IACF;IAEA,IAAI,IAAI,CAACxC,cAAc,CAACkD,QAAQ,CAAC,EAAE;MACjC,IAAI,CAACQ,kBAAkB,CAACR,QAAQ,EAAEzD,QAAQ,CAAC;IAC7C,CAAC,MAAM;MACL;MACAkD,MAAM,CAACC,IAAI,CAACM,QAAQ,EAAE,QAAQ,CAAC;IACjC;EACF;EAEA;EACQQ,kBAAkBA,CAACC,UAAkB,EAAElE,QAAgB;IAC7D,IAAI;MACF;MACA,MAAMmE,aAAa,GAAGD,UAAU,CAACE,QAAQ,CAAC,GAAG,CAAC,GAAGF,UAAU,CAACG,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAGH,UAAU;MAEtF;MACA,IAAII,QAAQ,GAAG,0BAA0B;MACzC,MAAMC,SAAS,GAAGvE,QAAQ,CAACqE,KAAK,CAAC,GAAG,CAAC,CAACG,GAAG,EAAE,EAAEC,WAAW,EAAE;MAE1D,QAAQF,SAAS;QACf,KAAK,KAAK;UACRD,QAAQ,GAAG,iBAAiB;UAC5B;QACF,KAAK,KAAK;QACV,KAAK,MAAM;UACTA,QAAQ,GAAG,YAAY;UACvB;QACF,KAAK,KAAK;UACRA,QAAQ,GAAG,WAAW;UACtB;QACF,KAAK,KAAK;UACRA,QAAQ,GAAG,kBAAkB;UAC7B;QACF,KAAK,KAAK;UACRA,QAAQ,GAAG,iBAAiB;UAC5B;MACJ;MAEA;MACA,MAAMI,cAAc,GAAGC,IAAI,CAACR,aAAa,CAAC;MAC1C,MAAMS,WAAW,GAAG,IAAIC,KAAK,CAACH,cAAc,CAACrE,MAAM,CAAC;MACpD,KAAK,IAAIyE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,cAAc,CAACrE,MAAM,EAAEyE,CAAC,EAAE,EAAE;QAC9CF,WAAW,CAACE,CAAC,CAAC,GAAGJ,cAAc,CAACK,UAAU,CAACD,CAAC,CAAC;MAC/C;MACA,MAAME,SAAS,GAAG,IAAIC,UAAU,CAACL,WAAW,CAAC;MAC7C,MAAMtC,IAAI,GAAG,IAAI4C,IAAI,CAAC,CAACF,SAAS,CAAC,EAAE;QAAEG,IAAI,EAAEb;MAAQ,CAAE,CAAC;MAEtD;MACA,MAAM/B,GAAG,GAAGC,GAAG,CAACC,eAAe,CAACH,IAAI,CAAC;MACrC,MAAMqB,IAAI,GAAGP,QAAQ,CAACQ,aAAa,CAAC,GAAG,CAAC;MACxCD,IAAI,CAACH,IAAI,GAAGjB,GAAG;MACfoB,IAAI,CAACE,QAAQ,GAAG7D,QAAQ;MACxBoD,QAAQ,CAAChE,IAAI,CAAC0E,WAAW,CAACH,IAAI,CAAC;MAC/BA,IAAI,CAACI,KAAK,EAAE;MACZX,QAAQ,CAAChE,IAAI,CAAC4E,WAAW,CAACL,IAAI,CAAC;MAE/B;MACAf,UAAU,CAAC,MAAMJ,GAAG,CAACK,eAAe,CAACN,GAAG,CAAC,EAAE,IAAI,CAAC;IAClD,CAAC,CAAC,OAAOO,KAAK,EAAE;MACdpD,OAAO,CAACoD,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;MAC1C,IAAI,CAAChF,OAAO,CAACiF,YAAY,CAAC,WAAW,CAAC;IACxC;EACF;EAEA;EACAlC,WAAWA,CAACb,QAAgB;IAC1B,IAAI,CAACA,QAAQ,EAAE,OAAO,KAAK;IAC3B,MAAMoF,eAAe,GAAG,CAAC,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,CAAC;IACpE,MAAMb,SAAS,GAAGvE,QAAQ,CAACqE,KAAK,CAAC,GAAG,CAAC,CAACG,GAAG,EAAE,EAAEC,WAAW,EAAE;IAC1D,OAAOW,eAAe,CAAChB,QAAQ,CAACG,SAAS,IAAI,EAAE,CAAC;EAClD;EAEA;EACAxD,WAAWA,CAACQ,GAAW;IACrB,IAAIA,GAAG,EAAE;MACP,OAAOA,GAAG,CAACkD,WAAW,EAAE,CAACY,QAAQ,CAAC,MAAM,CAAC;IAC3C;IACA,OAAO,KAAK;EACd;EAEA;EACApE,WAAWA,CAACM,GAAW;IACrB,IAAIA,GAAG,EAAE;MACP,MAAM+D,QAAQ,GAAG/D,GAAG,CAACkD,WAAW,EAAE;MAClC,OAAOa,QAAQ,CAACD,QAAQ,CAAC,MAAM,CAAC,IAAIC,QAAQ,CAACD,QAAQ,CAAC,MAAM,CAAC;IAC/D;IACA,OAAO,KAAK;EACd;EACA;EACAE,WAAWA,CAACvF,QAAmC;IAC7C,MAAMwF,IAAI,GAAGxF,QAAQ,IAAI,EAAE;IAC3B,IAAI,IAAI,CAACa,WAAW,CAAC2E,IAAI,CAAC,EAAE;MAC1B,OAAO,4BAA4B;IACrC,CAAC,MAAM,IAAI,IAAI,CAACzE,WAAW,CAACyE,IAAI,CAAC,EAAE;MACjC,OAAO,6BAA6B;IACtC,CAAC,MAAM,IAAI,IAAI,CAACvE,WAAW,CAACuE,IAAI,CAAC,EAAE;MACjC,OAAO,0BAA0B;IACnC,CAAC,MAAM;MACL,OAAO,0BAA0B;IACnC;EACF;EAEA;EACAC,eAAeA,CAACzF,QAAmC;IACjD,MAAMwF,IAAI,GAAGxF,QAAQ,IAAI,EAAE;IAC3B,IAAI,IAAI,CAACa,WAAW,CAAC2E,IAAI,CAAC,EAAE;MAC1B,OAAO,IAAI;IACb,CAAC,MAAM,IAAI,IAAI,CAACzE,WAAW,CAACyE,IAAI,CAAC,EAAE;MACjC,OAAO,KAAK;IACd,CAAC,MAAM,IAAI,IAAI,CAACvE,WAAW,CAACuE,IAAI,CAAC,EAAE;MACjC,OAAO,KAAK;IACd,CAAC,MAAM;MACL,OAAO,IAAI;IACb;EACF;EAEA;EACQvD,gBAAgBA,CAACJ,OAAe,EAAE7B,QAAgB;IACxD,IAAI;MACF,MAAM+B,QAAQ,GAAG,IAAI,CAACC,WAAW,CAACH,OAAO,CAAC;MAC1C,MAAMoB,SAAS,GAAGC,MAAM,CAACC,IAAI,CAAC,EAAE,EAAE,QAAQ,CAAC;MAC3C,IAAIF,SAAS,EAAE;QACbA,SAAS,CAACG,QAAQ,CAACC,KAAK,CAAC;;;uBAGVrD,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;oCAoCKA,QAAQ;;4BAEhB+B,QAAQ,UAAU/B,QAAQ;;;;SAI7C,CAAC;QACFiD,SAAS,CAACG,QAAQ,CAACE,KAAK,EAAE;MAC5B,CAAC,MAAM;QACL;QACAJ,MAAM,CAACC,IAAI,CAACpB,QAAQ,EAAE,QAAQ,CAAC;MACjC;IACF,CAAC,CAAC,OAAOe,KAAK,EAAE;MACdpD,OAAO,CAACoD,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;MACjC,IAAI,CAAChF,OAAO,CAACiF,YAAY,CAAC,UAAU,CAAC;IACvC;EACF;EACA;EACQf,WAAWA,CAAC0D,aAAqB;IACvC,IAAI,CAACA,aAAa,EAAE,OAAO,EAAE;IAE7B;IACA,IAAIA,aAAa,CAAClE,UAAU,CAAC,OAAO,CAAC,EAAE;MACrC,OAAOkE,aAAa;IACtB;IAEA;IACA,IAAIA,aAAa,CAAClE,UAAU,CAAC,MAAM,CAAC,EAAE;MACpC,OAAOkE,aAAa;IACtB;IAEA;IACA,OAAO,0BAA0BA,aAAa,EAAE;EAClD;EAEAC,YAAYA,CAACxF,KAAU,EAAEF,SAAc;IACrC;IACA;IACA;IACA;IACA;IACAiD,MAAM,CAACC,IAAI,CAAChD,KAAK,EAAE,QAAQ,CAAC;EAC9B;EAEAyF,YAAYA,CAACC,SAAkB;IAC7B,IAAI,CAACA,SAAS,EAAE;MACd,IAAI,CAACC,UAAU,EAAE;MACjB,IAAI,IAAI,CAAC/H,iBAAiB,CAACgI,aAAa,CAAC1F,MAAM,GAAG,CAAC,EAAE;QACnD,IAAI,CAACvC,OAAO,CAACkI,aAAa,CAAC,IAAI,CAACjI,iBAAiB,CAACgI,aAAa,CAAC;QAChE;MACF;IACF;IACA,IAAI,CAACrI,qBAAqB,CAACuI,6CAA6C,CAAC;MACvE7G,IAAI,EAAE;QACJb,GAAG,EAAE,IAAI,CAACA,GAAG;QACbL,KAAK,EAAE,IAAI,CAACA,KAAK;QACjBgI,UAAU,EAAEL,SAAS;QACrBM,OAAO,EAAE,IAAI,CAAChI;;KAEjB,CAAC,CAACW,IAAI,CACL3B,GAAG,CAACmC,GAAG,IAAG;MACR,IAAIA,GAAG,CAACC,UAAU,KAAK,CAAC,EAAE;QACxB,IAAI,CAACzB,OAAO,CAACsI,aAAa,CAAC,MAAM,CAAC;QAClC,IAAI,CAAClH,sBAAsB,EAAE;QAC7B,IAAI,IAAI,CAACM,eAAe,CAAC6G,cAAc,EAAEhG,MAAM,IAAI,CAAC,EAAE;UACpD,IAAI,CAACb,eAAe,CAAC6G,cAAc,EAAEC,IAAI,CAAC;YACxCC,QAAQ,EAAE,IAAI,CAACnI,SAAS,CAACoI,QAAQ;YACjCC,WAAW,EAAE,IAAIC,IAAI,EAAE,CAACC,WAAW,EAAE;YACrCR,OAAO,EAAE,IAAI,CAAChI;WACf,CAAC;QACJ;QACA,IAAI,CAACA,MAAM,GAAG,EAAE;MAClB;MACA,IAAI,CAACyI,MAAM,EAAE;IACf,CAAC,CAAC,CACH,CAAC5H,SAAS,EAAE;EACf;EAEA4H,MAAMA,CAAA;IACJ,IAAI,CAAC5I,aAAa,CAACsI,IAAI,CAAC;MACtBO,MAAM;MACNC,OAAO,EAAE,IAAI,CAAClI;KACf,CAAC;IACF,IAAI,CAACf,SAAS,CAACkJ,IAAI,EAAE;EACvB;EAEAjB,UAAUA,CAAA;IACR,IAAI,CAAC/H,iBAAiB,CAACiJ,KAAK,EAAE;IAC9B,IAAI,CAACjJ,iBAAiB,CAACkJ,QAAQ,CAAC,MAAM,EAAE,IAAI,CAAC9I,MAAM,CAAC;EACtD;EAEA;EACA+I,aAAaA,CAAA;IACX,IAAI,CAAC,IAAI,CAAC1H,eAAe,EAAEI,aAAa,EAAE;MACxC,OAAO,KAAK;IACd;IAEA,OAAO,IAAI,CAACJ,eAAe,CAACI,aAAa,CAACuH,IAAI,CAACrH,IAAI,IACjDA,IAAI,CAACK,KAAK,KAAKL,IAAI,CAACG,SAAS,IAAIH,IAAI,CAACK,KAAK,CAACE,MAAM,GAAG,CAAC,CAAC,CACxD;EACH;CACD;AAxeY7C,8BAA8B,GAAA4J,UAAA,EAV1ClK,SAAS,CAAC;EACTmK,QAAQ,EAAE,6BAA6B;EACvCC,UAAU,EAAE,IAAI;EAChBC,OAAO,EAAE,CACPtK,YAAY,EACZG,YAAY,CACb;EACDoK,WAAW,EAAE,0CAA0C;EACvDC,SAAS,EAAE,CAAC,0CAA0C;CACvD,CAAC,C,EACWjK,8BAA8B,CAwe1C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}