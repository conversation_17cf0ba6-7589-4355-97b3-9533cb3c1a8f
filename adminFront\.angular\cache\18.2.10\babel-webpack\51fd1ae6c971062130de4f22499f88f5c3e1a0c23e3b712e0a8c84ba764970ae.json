{"ast": null, "code": "import { DestroyRef, inject } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { takeUntilDestroyed } from '@angular/core/rxjs-interop';\nimport { EnumFileType } from 'src/app/shared/enum/enumFileType';\nimport { DateFormatHourPipe } from 'src/app/@theme/pipes';\nimport { SharedModule } from '../../components/shared.module';\nimport { BaseComponent } from '../../components/base/baseComponent';\nimport { cApproveStatusPipe } from 'src/app/@theme/pipes/mapping.pipe';\nimport { tap } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/shared/helper/allowHelper\";\nimport * as i2 from \"@nebular/theme\";\nimport * as i3 from \"src/app/shared/helper/validationHelper\";\nimport * as i4 from \"src/app/shared/services/message.service\";\nimport * as i5 from \"src/services/api/services\";\nimport * as i6 from \"@angular/router\";\nimport * as i7 from \"src/app/shared/services/utility.service\";\nimport * as i8 from \"@angular/common\";\nimport * as i9 from \"@angular/forms\";\nimport * as i10 from \"../../components/pagination/pagination.component\";\nimport * as i11 from \"../../../@theme/directives/label.directive\";\nconst _c0 = a0 => ({\n  \"required-field\": a0\n});\nconst _c1 = a0 => ({\n  \"download\": a0\n});\nfunction RelatedDocumentsComponent_tr_39_ng_container_15_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"button\", 23);\n    i0.ɵɵlistener(\"click\", function RelatedDocumentsComponent_tr_39_ng_container_15_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const item_r5 = i0.ɵɵnextContext().$implicit;\n      const ctx_r5 = i0.ɵɵnextContext();\n      const dialog_r3 = i0.ɵɵreference(47);\n      return i0.ɵɵresetView(ctx_r5.onEdit(dialog_r3, item_r5));\n    });\n    i0.ɵɵelement(2, \"i\", 24);\n    i0.ɵɵtext(3, \"\\u7DE8\\u8F2F \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"button\", 25);\n    i0.ɵɵlistener(\"click\", function RelatedDocumentsComponent_tr_39_ng_container_15_Template_button_click_4_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r6 = i0.ɵɵnextContext();\n      const item_r5 = ctx_r6.$implicit;\n      const i_r8 = ctx_r6.index;\n      const ctx_r5 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r5.onDelete(item_r5, i_r8));\n    });\n    i0.ɵɵelement(5, \"i\", 26);\n    i0.ɵɵtext(6, \"\\u522A\\u9664 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n}\nfunction RelatedDocumentsComponent_tr_39_ng_container_16_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"button\", 25);\n    i0.ɵɵlistener(\"click\", function RelatedDocumentsComponent_tr_39_ng_container_16_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r6 = i0.ɵɵnextContext();\n      const item_r5 = ctx_r6.$implicit;\n      const i_r8 = ctx_r6.index;\n      const ctx_r5 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r5.onDelete(item_r5, i_r8));\n    });\n    i0.ɵɵelement(2, \"i\", 26);\n    i0.ɵɵtext(3, \"\\u522A\\u9664 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n}\nfunction RelatedDocumentsComponent_tr_39_ng_container_17_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"button\", 27);\n    i0.ɵɵlistener(\"click\", function RelatedDocumentsComponent_tr_39_ng_container_17_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const item_r5 = i0.ɵɵnextContext().$implicit;\n      const ctx_r5 = i0.ɵɵnextContext();\n      const reasonReject_r11 = i0.ɵɵreference(43);\n      return i0.ɵɵresetView(ctx_r5.showReason(reasonReject_r11, item_r5));\n    });\n    i0.ɵɵelement(2, \"i\", 28);\n    i0.ɵɵtext(3, \"\\u9000\\u56DE\\u539F\\u56E0 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"button\", 25);\n    i0.ɵɵlistener(\"click\", function RelatedDocumentsComponent_tr_39_ng_container_17_Template_button_click_4_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r6 = i0.ɵɵnextContext();\n      const item_r5 = ctx_r6.$implicit;\n      const i_r8 = ctx_r6.index;\n      const ctx_r5 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r5.onDelete(item_r5, i_r8));\n    });\n    i0.ɵɵelement(5, \"i\", 26);\n    i0.ɵɵtext(6, \"\\u522A\\u9664 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n}\nfunction RelatedDocumentsComponent_tr_39_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"td\");\n    i0.ɵɵtext(8);\n    i0.ɵɵpipe(9, \"cApproveStatus\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"td\");\n    i0.ɵɵtext(11);\n    i0.ɵɵpipe(12, \"dateFormatHour\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"td\");\n    i0.ɵɵelementContainerStart(14, 21);\n    i0.ɵɵtemplate(15, RelatedDocumentsComponent_tr_39_ng_container_15_Template, 7, 0, \"ng-container\", 22)(16, RelatedDocumentsComponent_tr_39_ng_container_16_Template, 4, 0, \"ng-container\", 22)(17, RelatedDocumentsComponent_tr_39_ng_container_17_Template, 7, 0, \"ng-container\", 22);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const item_r5 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r5.CID);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r5.CCategoryName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r5.CName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(9, 9, item_r5.CApproveStatus));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(item_r5.CApproveDate ? i0.ɵɵpipeBind1(12, 11, item_r5.CApproveDate) : \"\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngSwitch\", item_r5.CApproveStatus);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", 1);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", 2);\n  }\n}\nfunction RelatedDocumentsComponent_ng_template_42_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-card\", 29)(1, \"nb-card-body\")(2, \"div\", 30)(3, \"span\", 31);\n    i0.ɵɵtext(4, \"\\u9000\\u56DE\\u539F\\u56E0\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(5, \"textarea\", 32);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"nb-card-footer\", 33)(7, \"button\", 34);\n    i0.ɵɵlistener(\"click\", function RelatedDocumentsComponent_ng_template_42_Template_button_click_7_listener() {\n      const ref_r13 = i0.ɵɵrestoreView(_r12).dialogRef;\n      const ctx_r5 = i0.ɵɵnextContext();\n      ref_r13.close();\n      return i0.ɵɵresetView(ctx_r5.currentItem = {});\n    });\n    i0.ɵɵtext(8, \"\\u95DC\\u9589\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"value\", ctx_r5.currentItem.CExamineRejectNote)(\"rows\", 4)(\"disabled\", true);\n  }\n}\nfunction RelatedDocumentsComponent_ng_template_44_div_17_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r16 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 47)(1, \"div\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\", 48)(4, \"button\", 49);\n    i0.ɵɵlistener(\"click\", function RelatedDocumentsComponent_ng_template_44_div_17_div_1_Template_button_click_4_listener() {\n      const file_r17 = i0.ɵɵrestoreView(_r16).$implicit;\n      const ctx_r5 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r5.removeFile(file_r17.id));\n    });\n    i0.ɵɵtext(5, \"\\u5220\\u9664\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const file_r17 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", file_r17.name, \" \");\n  }\n}\nfunction RelatedDocumentsComponent_ng_template_44_div_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 45);\n    i0.ɵɵtemplate(1, RelatedDocumentsComponent_ng_template_44_div_17_div_1_Template, 6, 1, \"div\", 46);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r5.listFiles);\n  }\n}\nfunction RelatedDocumentsComponent_ng_template_44_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r14 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-card\", 35)(1, \"nb-card-header\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"nb-card-body\")(4, \"div\", 7)(5, \"label\", 36);\n    i0.ɵɵtext(6, \"\\u5206\\u985E\\u540D\\u7A31\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"input\", 9);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function RelatedDocumentsComponent_ng_template_44_Template_input_ngModelChange_7_listener($event) {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r5 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r5.currentItem.CCategoryName, $event) || (ctx_r5.currentItem.CCategoryName = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 37)(9, \"div\", 38)(10, \"label\", 39);\n    i0.ɵɵtext(11, \"\\u4E0A\\u50B3\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(12, \"input\", 40, 3);\n    i0.ɵɵlistener(\"change\", function RelatedDocumentsComponent_ng_template_44_Template_input_change_12_listener($event) {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r5 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r5.onMultipleFile($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"button\", 41);\n    i0.ɵɵlistener(\"click\", function RelatedDocumentsComponent_ng_template_44_Template_button_click_14_listener() {\n      i0.ɵɵrestoreView(_r14);\n      const uploadFileRef_r15 = i0.ɵɵreference(13);\n      return i0.ɵɵresetView(uploadFileRef_r15.click());\n    });\n    i0.ɵɵelement(15, \"i\", 42);\n    i0.ɵɵtext(16, \"\\u4E0A\\u50B3\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(17, RelatedDocumentsComponent_ng_template_44_div_17_Template, 2, 1, \"div\", 43);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"nb-card-footer\", 33)(19, \"button\", 44);\n    i0.ɵɵlistener(\"click\", function RelatedDocumentsComponent_ng_template_44_Template_button_click_19_listener() {\n      const ref_r18 = i0.ɵɵrestoreView(_r14).dialogRef;\n      return i0.ɵɵresetView(ref_r18.close());\n    });\n    i0.ɵɵtext(20, \"\\u53D6\\u6D88\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"button\", 13);\n    i0.ɵɵlistener(\"click\", function RelatedDocumentsComponent_ng_template_44_Template_button_click_21_listener() {\n      const ref_r18 = i0.ɵɵrestoreView(_r14).dialogRef;\n      const ctx_r5 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r5.onSaveMultiple(ref_r18));\n    });\n    i0.ɵɵtext(22, \"\\u5132\\u5B58\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r5.isAddNew ? \"\\u65B0\\u589E\" : \"\\u7DE8\\u8F2F\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r5.currentItem.CCategoryName);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(4, _c0, ctx_r5.isAddNew));\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngIf\", ctx_r5.listFiles.length > 0);\n  }\n}\nfunction RelatedDocumentsComponent_ng_template_46_div_8_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 60);\n    i0.ɵɵelement(1, \"img\", 61);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"src\", ctx_r5.currentItem.CFile, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction RelatedDocumentsComponent_ng_template_46_div_8_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r20 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 62);\n    i0.ɵɵlistener(\"click\", function RelatedDocumentsComponent_ng_template_46_div_8_div_4_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r20);\n      const ctx_r5 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r5.openNewFile(ctx_r5.currentItem.CFile));\n    });\n    i0.ɵɵelement(1, \"nb-icon\", 63);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RelatedDocumentsComponent_ng_template_46_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 7)(1, \"label\", 57);\n    i0.ɵɵtext(2, \"\\u6A94\\u6848\\u9023\\u7D50\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, RelatedDocumentsComponent_ng_template_46_div_8_div_3_Template, 2, 1, \"div\", 58)(4, RelatedDocumentsComponent_ng_template_46_div_8_div_4_Template, 2, 0, \"div\", 59);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r5.checkImage(ctx_r5.currentItem.CFile));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r5.checkImage(ctx_r5.currentItem.CFile));\n  }\n}\nfunction RelatedDocumentsComponent_ng_template_46_div_18_img_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 70);\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"src\", ctx_r5.uploadedFile.Cimg, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction RelatedDocumentsComponent_ng_template_46_div_18_i_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 71);\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(1, _c1, !ctx_r5.isAddNew));\n  }\n}\nfunction RelatedDocumentsComponent_ng_template_46_div_18_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r22 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\")(1, \"ul\", 64)(2, \"li\", 65);\n    i0.ɵɵtemplate(3, RelatedDocumentsComponent_ng_template_46_div_18_img_3_Template, 1, 1, \"img\", 66)(4, RelatedDocumentsComponent_ng_template_46_div_18_i_4_Template, 1, 3, \"i\", 67);\n    i0.ɵɵelementStart(5, \"a\", 68);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"nb-icon\", 69);\n    i0.ɵɵlistener(\"click\", function RelatedDocumentsComponent_ng_template_46_div_18_Template_nb_icon_click_7_listener() {\n      i0.ɵɵrestoreView(_r22);\n      const ctx_r5 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r5.deleteItem(ctx_r5.uploadedFile));\n    });\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", !(ctx_r5.uploadedFile.CName == null ? null : ctx_r5.uploadedFile.CName.includes(\"pdf\")));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r5.uploadedFile.CName == null ? null : ctx_r5.uploadedFile.CName.includes(\"pdf\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"href\", ctx_r5.uploadedFile.Cimg ? ctx_r5.uploadedFile.Cimg : ctx_r5.uploadedFile.CFile, i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r5.uploadedFile.CName);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"hidden\", !ctx_r5.isAddNew);\n  }\n}\nfunction RelatedDocumentsComponent_ng_template_46_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r19 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-card\", 35)(1, \"nb-card-header\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"nb-card-body\")(4, \"div\", 7)(5, \"label\", 36);\n    i0.ɵɵtext(6, \"\\u5206\\u985E\\u540D\\u7A31\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"input\", 9);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function RelatedDocumentsComponent_ng_template_46_Template_input_ngModelChange_7_listener($event) {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r5 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r5.currentItem.CCategoryName, $event) || (ctx_r5.currentItem.CCategoryName = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(8, RelatedDocumentsComponent_ng_template_46_div_8_Template, 5, 2, \"div\", 50);\n    i0.ɵɵelementStart(9, \"div\", 37)(10, \"div\", 38)(11, \"label\", 39);\n    i0.ɵɵtext(12, \"\\u4E0A\\u50B3\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"input\", 51, 3);\n    i0.ɵɵlistener(\"change\", function RelatedDocumentsComponent_ng_template_46_Template_input_change_13_listener($event) {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r5 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r5.onChooseFile($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"button\", 41);\n    i0.ɵɵlistener(\"click\", function RelatedDocumentsComponent_ng_template_46_Template_button_click_15_listener() {\n      i0.ɵɵrestoreView(_r19);\n      const uploadFileRef_r21 = i0.ɵɵreference(14);\n      return i0.ɵɵresetView(uploadFileRef_r21.click());\n    });\n    i0.ɵɵelement(16, \"i\", 42);\n    i0.ɵɵtext(17, \"\\u4E0A\\u50B3\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(18, RelatedDocumentsComponent_ng_template_46_div_18_Template, 8, 5, \"div\", 52);\n    i0.ɵɵelementStart(19, \"div\", 7)(20, \"label\", 53);\n    i0.ɵɵtext(21, \"\\u72C0\\u614B\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"nb-select\", 54);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function RelatedDocumentsComponent_ng_template_46_Template_nb_select_ngModelChange_22_listener($event) {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r5 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r5.currentItem.CStatus, $event) || (ctx_r5.currentItem.CStatus = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementStart(23, \"nb-option\", 55);\n    i0.ɵɵtext(24, \"\\u555F\\u7528\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(25, \"nb-option\", 55);\n    i0.ɵɵtext(26, \"\\u505C\\u7528\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(27, \"div\", 7)(28, \"label\", 53);\n    i0.ɵɵtext(29, \"\\u9001\\u5BE9\\u8AAA\\u660E\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(30, \"textarea\", 56);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function RelatedDocumentsComponent_ng_template_46_Template_textarea_ngModelChange_30_listener($event) {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r5 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r5.currentItem.CSubmitRemark, $event) || (ctx_r5.currentItem.CSubmitRemark = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(31, \"nb-card-footer\", 33)(32, \"button\", 44);\n    i0.ɵɵlistener(\"click\", function RelatedDocumentsComponent_ng_template_46_Template_button_click_32_listener() {\n      const ref_r23 = i0.ɵɵrestoreView(_r19).dialogRef;\n      return i0.ɵɵresetView(ref_r23.close());\n    });\n    i0.ɵɵtext(33, \"\\u53D6\\u6D88\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(34, \"button\", 13);\n    i0.ɵɵlistener(\"click\", function RelatedDocumentsComponent_ng_template_46_Template_button_click_34_listener() {\n      const ref_r23 = i0.ɵɵrestoreView(_r19).dialogRef;\n      const ctx_r5 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r5.onSave(ref_r23));\n    });\n    i0.ɵɵtext(35, \"\\u5132\\u5B58\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r5.isAddNew ? \"\\u65B0\\u589E\" : \"\\u7DE8\\u8F2F\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r5.currentItem.CCategoryName);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r5.isAddNew);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(10, _c0, ctx_r5.isAddNew));\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngIf\", ctx_r5.uploadedFile);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r5.currentItem.CStatus);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"value\", 1);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"value\", 0);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r5.currentItem.CSubmitRemark);\n    i0.ɵɵproperty(\"rows\", 4);\n  }\n}\nexport class RelatedDocumentsComponent extends BaseComponent {\n  constructor(allow, dialogService, valid, message, buildCaseFileService, activatedRoute, _utilityService) {\n    super(allow);\n    this.allow = allow;\n    this.dialogService = dialogService;\n    this.valid = valid;\n    this.message = message;\n    this.buildCaseFileService = buildCaseFileService;\n    this.activatedRoute = activatedRoute;\n    this._utilityService = _utilityService;\n    this.filterCategoryName = '';\n    this.filterFileName = '';\n    this.buildCaseId = 1;\n    this.listBuildCaseFile = [];\n    this.isAddNew = false;\n    this.uploadedFile = undefined;\n    this.listFiles = [];\n    this.destroy = inject(DestroyRef);\n    this.currentPage = -1;\n  }\n  ngOnInit() {\n    this.buildCaseId = 1;\n    this.buildCaseId = parseInt(this.activatedRoute.snapshot.paramMap.get(\"id\"));\n    this.getListBuildCaseFile(1);\n  }\n  getListBuildCaseFile(page) {\n    this.buildCaseFileService.apiBuildCaseFileGetListBuildCaseFilePost$Json({\n      body: {\n        CBuildCaseID: this.buildCaseId,\n        CName: this.filterFileName ? this.filterFileName : undefined,\n        CCategoryName: this.filterCategoryName ? this.filterCategoryName : undefined,\n        PageIndex: page,\n        PageSize: 10\n      }\n    }).pipe(takeUntilDestroyed(this.destroy)).subscribe(res => {\n      this.listBuildCaseFile = res.Entries ?? [];\n      this.totalRecords = res.TotalItems;\n    });\n  }\n  onAddNew(dialog) {\n    this.isAddNew = true;\n    this.uploadedFile = undefined;\n    this.currentItem = {\n      CStatus: 0\n    };\n    this.dialogService.open(dialog);\n  }\n  onAddMultiplesFile(dialog) {\n    this.isAddNew = true;\n    this.uploadedFile = undefined;\n    this.currentItem = {\n      CCategoryName: \"\"\n    };\n    this.dialogService.open(dialog);\n  }\n  onEdit(ref, item) {\n    this.isAddNew = false;\n    this.uploadedFile = undefined;\n    this.currentItem = {};\n    this.buildCaseFileService.apiBuildCaseFileGetBuildCaseFileByIdPost$Json({\n      body: {\n        CBuildCaseFileID: item.CID\n      }\n    }).pipe(takeUntilDestroyed(this.destroy)).subscribe(res => {\n      this.currentItem = res.Entries;\n    });\n    this.dialogService.open(ref);\n  }\n  onDelete(item, index) {\n    if (window.confirm(`確定要刪除【項目${item.CID}】?`)) {\n      this.buildCaseFileService.apiBuildCaseFileDeleteBuildCaseFilePost$Json({\n        body: {\n          CBuildCaseFileID: item.CID\n        }\n      }).pipe(takeUntilDestroyed(this.destroy)).subscribe(res => {\n        if (res.StatusCode === 0) {\n          this.message.showSucessMSG('執行成功');\n          this.getListBuildCaseFile(this.currentPage);\n        }\n      });\n    }\n  }\n  showReason(ref, item) {\n    this.dialogService.open(ref);\n    this.currentItem = item;\n  }\n  base64ToBlob(base64, contentType = '') {\n    const base64Data = base64.split(',')[1] || base64;\n    // Decode base64 string\n    const byteCharacters = atob(base64Data);\n    // Create a byte array with length equal to the number of bytes in the string\n    const byteArrays = [];\n    for (let offset = 0; offset < byteCharacters.length; offset += 512) {\n      const slice = byteCharacters.slice(offset, offset + 512);\n      const byteNumbers = new Array(slice.length);\n      for (let i = 0; i < slice.length; i++) {\n        byteNumbers[i] = slice.charCodeAt(i);\n      }\n      const byteArray = new Uint8Array(byteNumbers);\n      byteArrays.push(byteArray);\n    }\n    return new Blob(byteArrays, {\n      type: contentType\n    });\n  }\n  onSave(ref) {\n    this.validation();\n    if (this.valid.errorMessages.length > 0) {\n      this.message.showErrorMSGs(this.valid.errorMessages);\n      return;\n    }\n    let request = {\n      body: {\n        CBuildCaseID: this.isAddNew ? this.buildCaseId : undefined,\n        CBuildCaseFileID: this.isAddNew ? undefined : this.currentItem.CID,\n        CStatus: this.currentItem.CStatus,\n        CSubmitRemark: this.currentItem.CSubmitRemark ? this.currentItem.CSubmitRemark : undefined,\n        CFile: this.uploadedFile.CFileUpload,\n        CCategoryName: this.currentItem.CCategoryName ? this.currentItem.CCategoryName : undefined\n      }\n    };\n    this.buildCaseFileService.apiBuildCaseFileSaveBuildCaseFilePost$Json(request).subscribe(res => {\n      if (res.StatusCode === 0) {\n        this.message.showSucessMSG('儲存成功');\n      } else {\n        this.message.showErrorMSG(res.Message);\n      }\n      ref.close();\n      this.getListBuildCaseFile(1);\n    });\n  }\n  validation() {\n    this.valid.clear();\n    this.valid.required(`送審說明`, this.currentItem.CSubmitRemark);\n    this.valid.required(`分類名稱`, this.currentItem.CCategoryName);\n    if (this.isAddNew && !this.uploadedFile) {\n      this.valid.addErrorMessage(`上傳 必填`);\n    }\n  }\n  onChooseFile(event) {\n    let files = event.target.files;\n    const fileRegex = /pdf|jpg|jpeg|png/i;\n    if (!fileRegex.test(files[0].type)) {\n      this.message.showErrorMSG('檔案格式錯誤，僅限pdf、圖片檔');\n      return;\n    }\n    let reader = new FileReader();\n    reader.readAsDataURL(files[0]);\n    reader.onload = e => {\n      let file = \"\";\n      if (!files[0].name.includes('pdf')) {\n        file = URL.createObjectURL(files[0]);\n      }\n      this.uploadedFile = {\n        CName: files[0].name,\n        CFile: e.target?.result?.toString().split(',')[1],\n        Cimg: files[0].name.includes('pdf') ? files[0] : file,\n        CFileUpload: files[0],\n        CFileType: EnumFileType.PDF\n      };\n      event.target.value = null;\n    };\n  }\n  onMultipleFile(event) {\n    for (let index = 0; index < event.target.files.length; index++) {\n      const file = event.target.files[index];\n      if (file) {\n        let reader = new FileReader();\n        reader.readAsDataURL(file);\n        reader.onload = () => {\n          let base64Str = reader.result;\n          if (!base64Str) {\n            return;\n          }\n          file.id = new Date().getTime();\n          this.listFiles.push({\n            id: new Date().getTime(),\n            name: file.name,\n            data: base64Str,\n            extension: this._utilityService.getFileExtension(file.name),\n            CFile: file\n          });\n          event.target.value = null;\n        };\n      }\n    }\n  }\n  deleteItem(item) {\n    if (window.confirm(`確定要移除【${item.CName}】?`)) {\n      this.uploadedFile = undefined;\n    }\n  }\n  removeFile(id) {\n    this.listFiles = this.listFiles.filter(x => x.id != id);\n  }\n  validationMultiple() {\n    this.valid.clear();\n    this.valid.required(`分類名稱`, this.currentItem.CCategoryName);\n    if (this.isAddNew && this.listFiles.length == 0) {\n      this.valid.addErrorMessage(`上傳 必填`);\n    }\n  }\n  onSaveMultiple(ref) {\n    this.validationMultiple();\n    if (this.valid.errorMessages.length > 0) {\n      this.message.showErrorMSGs(this.valid.errorMessages);\n      return;\n    }\n    let listFilesUpload = this.listFiles.map(x => x.CFile);\n    this.buildCaseFileService.apiBuildCaseFileSaveMultipleBuildCaseFilePost$Json({\n      body: {\n        CBuildCaseID: this.buildCaseId,\n        CCategoryName: this.currentItem.CCategoryName,\n        CFiles: listFilesUpload\n      }\n    }).pipe(tap(res => {\n      if (res.StatusCode === 0) {\n        this.message.showSucessMSG('儲存成功');\n        ref.close();\n        this.getListBuildCaseFile(1);\n        this.listFiles = [];\n        this.currentItem = {};\n      } else {\n        this.message.showErrorMSG(res.Message);\n      }\n    })).subscribe();\n  }\n  getListPageChange(page) {\n    this.currentPage = page;\n    this.getListBuildCaseFile(page);\n  }\n  checkImage(fileName) {\n    return this._utilityService.getFileExtension(fileName).includes('png') || this._utilityService.getFileExtension(fileName).includes('jpeg') || this._utilityService.getFileExtension(fileName).includes('jpg');\n  }\n  openNewFile(fileName) {\n    console.log(fileName);\n    window.open(`${fileName}`, '_blank');\n  }\n  static {\n    this.ɵfac = function RelatedDocumentsComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || RelatedDocumentsComponent)(i0.ɵɵdirectiveInject(i1.AllowHelper), i0.ɵɵdirectiveInject(i2.NbDialogService), i0.ɵɵdirectiveInject(i3.ValidationHelper), i0.ɵɵdirectiveInject(i4.MessageService), i0.ɵɵdirectiveInject(i5.BuildCaseFileService), i0.ɵɵdirectiveInject(i6.ActivatedRoute), i0.ɵɵdirectiveInject(i7.UtilityService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: RelatedDocumentsComponent,\n      selectors: [[\"app-related-documents\"]],\n      standalone: true,\n      features: [i0.ɵɵInheritDefinitionFeature, i0.ɵɵStandaloneFeature],\n      decls: 48,\n      vars: 6,\n      consts: [[\"reasonReject\", \"\"], [\"multiple\", \"\"], [\"dialog\", \"\"], [\"uploadFileRef\", \"\"], [\"accent\", \"success\"], [1, \"row\"], [1, \"col-md-6\", \"col-xl-4\"], [1, \"form-group\", \"d-flex\", \"align-items-center\"], [\"for\", \"name\", 1, \"label\", \"mr-2\"], [\"type\", \"text\", \"nbInput\", \"\", \"fullWidth\", \"\", \"placeholder\", \"\\u8ACB\\u8F38\\u5165\\u5206\\u985E\\u540D\\u7A31\", 3, \"ngModelChange\", \"ngModel\"], [\"type\", \"text\", \"nbInput\", \"\", \"fullWidth\", \"\", \"placeholder\", \"\\u8ACB\\u8F38\\u5165\\u6A94\\u540D\", 3, \"ngModelChange\", \"ngModel\"], [1, \"d-flex\", \"justify-end\", \"gap-2\"], [1, \"btn\", \"btn-info\", 3, \"click\"], [1, \"btn\", \"btn-success\", 3, \"click\"], [1, \"table-responsive\", \"mt-4\"], [1, \"table\", \"table-striped\", \"border\", 2, \"min-width\", \"1000px\", \"background-color\", \"#f3f3f3\"], [2, \"background-color\", \"#27ae60\", \"color\", \"white\"], [2, \"min-width\", \"5%\"], [2, \"min-width\", \"15%\"], [4, \"ngFor\", \"ngForOf\"], [3, \"CollectionSizeChange\", \"PageChange\", \"PageSizeChange\", \"CollectionSize\", \"Page\", \"PageSize\"], [3, \"ngSwitch\"], [4, \"ngSwitchCase\"], [\"type\", \"button\", 1, \"btn\", \"btn-outline-success\", \"m-1\", \"btn-sm\", 3, \"click\"], [1, \"fas\", \"fa-edit\", \"mr-1\"], [\"type\", \"button\", 1, \"btn\", \"btn-outline-danger\", \"m-1\", \"btn-sm\", 3, \"click\"], [1, \"far\", \"fa-trash-alt\", \"mr-1\"], [\"type\", \"button\", 1, \"btn\", \"btn-outline-info\", \"m-1\", \"btn-sm\", 3, \"click\"], [1, \"fas\", \"fa-search\", \"mr-1\"], [2, \"width\", \"500px\"], [1, \"flex\", \"items-start\"], [1, \"w-[20%]\"], [\"nbInput\", \"\", 1, \"resize-none\", \"!max-w-full\", \"w-full\", 3, \"value\", \"rows\", \"disabled\"], [1, \"d-flex\", \"justify-content-center\"], [1, \"btn\", \"btn-info\", \"mr-2\", 3, \"click\"], [2, \"max-height\", \"95vh\", \"min-width\", \"400px\"], [\"for\", \"categoryName\", \"baseLabel\", \"\", 1, \"required-field\", \"mr-4\", 2, \"min-width\", \"75px\"], [1, \"form-group\", \"d-flex\", \"align-items-baseline\"], [1, \"d-flex\", \"flex-col\", \"mr-3\"], [\"for\", \"file\", \"baseLabel\", \"\", 1, \"mb-0\", 2, \"min-width\", \"75px\", 3, \"ngClass\"], [\"id\", \"file\", \"type\", \"file\", \"multiple\", \"\", \"hidden\", \"\", \"accept\", \".jpg,.jpeg,.png, .pdf\", 3, \"change\"], [1, \"btn\", \"btn-success\", \"btn-sm\", 3, \"click\"], [1, \"fas\", \"fa-plus\", \"mr-1\"], [\"class\", \"w-full\", 4, \"ngIf\"], [1, \"btn\", \"btn-danger\", \"mr-2\", 3, \"click\"], [1, \"w-full\"], [\"class\", \"flex items-center justify-between\", 4, \"ngFor\", \"ngForOf\"], [1, \"flex\", \"items-center\", \"justify-between\"], [1, \"text-center\"], [1, \"btn\", \"btn-outline-danger\", \"btn-sm\", \"m-1\", 3, \"click\"], [\"class\", \"form-group d-flex align-items-center\", 4, \"ngIf\"], [\"id\", \"file\", \"type\", \"file\", \"hidden\", \"\", \"accept\", \".jpg,.jpeg,.png, .pdf\", 3, \"change\"], [4, \"ngIf\"], [\"for\", \"status\", \"baseLabel\", \"\", 1, \"required-field\", \"mr-4\", 2, \"min-width\", \"75px\"], [\"fullWidth\", \"\", \"placeholder\", \"\\u8ACB\\u9078\\u64C7\\u6A13\\u5C64\", 3, \"ngModelChange\", \"ngModel\"], [3, \"value\"], [\"nbInput\", \"\", 1, \"resize-none\", \"!max-w-full\", \"w-full\", 3, \"ngModelChange\", \"ngModel\", \"rows\"], [\"for\", \"categoryName\", \"baseLabel\", \"\", 1, \"mr-4\", 2, \"min-width\", \"75px\"], [\"style\", \"width: 150px; height: 150px;\", 4, \"ngIf\"], [\"style\", \"cursor: pointer; width: 50px; height: 50px; border: 1px solid gray; border-radius: 10px; display: flex; align-items: center; justify-content: center;\", 3, \"click\", 4, \"ngIf\"], [2, \"width\", \"150px\", \"height\", \"150px\"], [1, \"fit-size\", 3, \"src\"], [2, \"cursor\", \"pointer\", \"width\", \"50px\", \"height\", \"50px\", \"border\", \"1px solid gray\", \"border-radius\", \"10px\", \"display\", \"flex\", \"align-items\", \"center\", \"justify-content\", \"center\", 3, \"click\"], [\"icon\", \"file-add-outline\"], [2, \"margin-left\", \"100px\"], [1, \"file-item\"], [\"alt\", \"\", \"style\", \"max-width:150px; width: 100%\", \"class\", \"mr-2\", 3, \"src\", 4, \"ngIf\"], [\"class\", \"fa-solid fa-file-pdf text-primary\", 3, \"ngClass\", 4, \"ngIf\"], [\"target\", \"_blank\", 1, \"text-primary\", \"download\", 3, \"href\"], [\"icon\", \"trash-2-outline\", 1, \"trash-icon\", 3, \"click\", \"hidden\"], [\"alt\", \"\", 1, \"mr-2\", 2, \"max-width\", \"150px\", \"width\", \"100%\", 3, \"src\"], [1, \"fa-solid\", \"fa-file-pdf\", \"text-primary\", 3, \"ngClass\"]],\n      template: function RelatedDocumentsComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"nb-card\", 4)(1, \"nb-card-header\");\n          i0.ɵɵtext(2, \" \\u76F8\\u95DC\\u6587\\u4EF6 \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(3, \"nb-card-body\")(4, \"div\", 5)(5, \"div\", 6)(6, \"div\", 7)(7, \"label\", 8);\n          i0.ɵɵtext(8, \"\\u5206\\u985E\\u540D\\u7A31\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(9, \"input\", 9);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function RelatedDocumentsComponent_Template_input_ngModelChange_9_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.filterCategoryName, $event) || (ctx.filterCategoryName = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(10, \"div\", 6)(11, \"div\", 7)(12, \"label\", 8);\n          i0.ɵɵtext(13, \"\\u6A94\\u540D\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(14, \"input\", 10);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function RelatedDocumentsComponent_Template_input_ngModelChange_14_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.filterFileName, $event) || (ctx.filterFileName = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(15, \"div\", 11)(16, \"button\", 12);\n          i0.ɵɵlistener(\"click\", function RelatedDocumentsComponent_Template_button_click_16_listener() {\n            i0.ɵɵrestoreView(_r1);\n            const multiple_r2 = i0.ɵɵreference(45);\n            return i0.ɵɵresetView(ctx.onAddMultiplesFile(multiple_r2));\n          });\n          i0.ɵɵtext(17, \"\\u6279\\u6B21\\u532F\\u5165\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(18, \"button\", 12);\n          i0.ɵɵlistener(\"click\", function RelatedDocumentsComponent_Template_button_click_18_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.getListBuildCaseFile(1));\n          });\n          i0.ɵɵtext(19, \"\\u67E5\\u8A62\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(20, \"button\", 13);\n          i0.ɵɵlistener(\"click\", function RelatedDocumentsComponent_Template_button_click_20_listener() {\n            i0.ɵɵrestoreView(_r1);\n            const dialog_r3 = i0.ɵɵreference(47);\n            return i0.ɵɵresetView(ctx.onAddNew(dialog_r3));\n          });\n          i0.ɵɵtext(21, \"\\u65B0\\u589E\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(22, \"div\", 14)(23, \"table\", 15)(24, \"thead\")(25, \"tr\", 16)(26, \"th\", 17);\n          i0.ɵɵtext(27, \"\\u6D41\\u6C34\\u865F\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(28, \"th\", 18);\n          i0.ɵɵtext(29, \"\\u5206\\u985E\\u540D\\u7A31\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(30, \"th\", 18);\n          i0.ɵɵtext(31, \"\\u6A94\\u540D\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(32, \"th\", 18);\n          i0.ɵɵtext(33, \"\\u5BE9\\u6838\\u72C0\\u614B \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(34, \"th\", 18);\n          i0.ɵɵtext(35, \"\\u5BE9\\u6838\\u65E5\\u671F \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(36, \"th\", 17);\n          i0.ɵɵtext(37, \"\\u52D5\\u4F5C\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(38, \"tbody\");\n          i0.ɵɵtemplate(39, RelatedDocumentsComponent_tr_39_Template, 18, 13, \"tr\", 19);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(40, \"nb-card-footer\")(41, \"ngx-pagination\", 20);\n          i0.ɵɵtwoWayListener(\"CollectionSizeChange\", function RelatedDocumentsComponent_Template_ngx_pagination_CollectionSizeChange_41_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.totalRecords, $event) || (ctx.totalRecords = $event);\n            return i0.ɵɵresetView($event);\n          })(\"PageChange\", function RelatedDocumentsComponent_Template_ngx_pagination_PageChange_41_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.pageIndex, $event) || (ctx.pageIndex = $event);\n            return i0.ɵɵresetView($event);\n          })(\"PageSizeChange\", function RelatedDocumentsComponent_Template_ngx_pagination_PageSizeChange_41_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.pageSize, $event) || (ctx.pageSize = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵlistener(\"PageChange\", function RelatedDocumentsComponent_Template_ngx_pagination_PageChange_41_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.getListPageChange($event));\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(42, RelatedDocumentsComponent_ng_template_42_Template, 9, 3, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor)(44, RelatedDocumentsComponent_ng_template_44_Template, 23, 6, \"ng-template\", null, 1, i0.ɵɵtemplateRefExtractor)(46, RelatedDocumentsComponent_ng_template_46_Template, 36, 12, \"ng-template\", null, 2, i0.ɵɵtemplateRefExtractor);\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(9);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.filterCategoryName);\n          i0.ɵɵadvance(5);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.filterFileName);\n          i0.ɵɵadvance(25);\n          i0.ɵɵproperty(\"ngForOf\", ctx.listBuildCaseFile);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtwoWayProperty(\"CollectionSize\", ctx.totalRecords)(\"Page\", ctx.pageIndex)(\"PageSize\", ctx.pageSize);\n        }\n      },\n      dependencies: [CommonModule, i8.NgClass, i8.NgForOf, i8.NgIf, i8.NgSwitch, i8.NgSwitchCase, SharedModule, i9.DefaultValueAccessor, i9.NgControlStatus, i9.NgModel, i2.NbCardComponent, i2.NbCardBodyComponent, i2.NbCardFooterComponent, i2.NbCardHeaderComponent, i2.NbInputDirective, i2.NbSelectComponent, i2.NbOptionComponent, i2.NbIconComponent, i10.PaginationComponent, i11.BaseLabelDirective, DateFormatHourPipe, cApproveStatusPipe],\n      styles: [\"label[_ngcontent-%COMP%] {\\n  min-width: 75px;\\n  margin: 0;\\n}\\n/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbInJlbGF0ZWQtZG9jdW1lbnRzLmNvbXBvbmVudC5zY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUFBO0VBQ0UsZUFBQTtFQUNBLFNBQUE7QUFDRiIsImZpbGUiOiJyZWxhdGVkLWRvY3VtZW50cy5jb21wb25lbnQuc2NzcyIsInNvdXJjZXNDb250ZW50IjpbImxhYmVsIHtcclxuICBtaW4td2lkdGg6IDc1cHg7XHJcbiAgbWFyZ2luOiAwO1xyXG59XHJcbiJdfQ== */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvcGFnZXMvY29uc3RydWN0aW9uLXByb2plY3QtbWFuYWdlbWVudC9yZWxhdGVkLWRvY3VtZW50cy9yZWxhdGVkLWRvY3VtZW50cy5jb21wb25lbnQuc2NzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFBQTtFQUNFLGVBQUE7RUFDQSxTQUFBO0FBQ0Y7QUFDQSw0V0FBNFciLCJzb3VyY2VzQ29udGVudCI6WyJsYWJlbCB7XHJcbiAgbWluLXdpZHRoOiA3NXB4O1xyXG4gIG1hcmdpbjogMDtcclxufVxyXG4iXSwic291cmNlUm9vdCI6IiJ9 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["DestroyRef", "inject", "CommonModule", "takeUntilDestroyed", "EnumFileType", "DateFormatHourPipe", "SharedModule", "BaseComponent", "cApproveStatusPipe", "tap", "i0", "ɵɵelementContainerStart", "ɵɵelementStart", "ɵɵlistener", "RelatedDocumentsComponent_tr_39_ng_container_15_Template_button_click_1_listener", "ɵɵrestoreView", "_r4", "item_r5", "ɵɵnextContext", "$implicit", "ctx_r5", "dialog_r3", "ɵɵreference", "ɵɵresetView", "onEdit", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd", "RelatedDocumentsComponent_tr_39_ng_container_15_Template_button_click_4_listener", "ctx_r6", "i_r8", "index", "onDelete", "RelatedDocumentsComponent_tr_39_ng_container_16_Template_button_click_1_listener", "_r9", "RelatedDocumentsComponent_tr_39_ng_container_17_Template_button_click_1_listener", "_r10", "reasonReject_r11", "showReason", "RelatedDocumentsComponent_tr_39_ng_container_17_Template_button_click_4_listener", "ɵɵtemplate", "RelatedDocumentsComponent_tr_39_ng_container_15_Template", "RelatedDocumentsComponent_tr_39_ng_container_16_Template", "RelatedDocumentsComponent_tr_39_ng_container_17_Template", "ɵɵadvance", "ɵɵtextInterpolate", "CID", "CCategoryName", "CName", "ɵɵpipeBind1", "CApproveStatus", "CApproveDate", "ɵɵproperty", "RelatedDocumentsComponent_ng_template_42_Template_button_click_7_listener", "ref_r13", "_r12", "dialogRef", "close", "currentItem", "CExamineRejectNote", "RelatedDocumentsComponent_ng_template_44_div_17_div_1_Template_button_click_4_listener", "file_r17", "_r16", "removeFile", "id", "ɵɵtextInterpolate1", "name", "RelatedDocumentsComponent_ng_template_44_div_17_div_1_Template", "listFiles", "ɵɵtwoWayListener", "RelatedDocumentsComponent_ng_template_44_Template_input_ngModelChange_7_listener", "$event", "_r14", "ɵɵtwoWayBindingSet", "RelatedDocumentsComponent_ng_template_44_Template_input_change_12_listener", "onMultipleFile", "RelatedDocumentsComponent_ng_template_44_Template_button_click_14_listener", "uploadFileRef_r15", "click", "RelatedDocumentsComponent_ng_template_44_div_17_Template", "RelatedDocumentsComponent_ng_template_44_Template_button_click_19_listener", "ref_r18", "RelatedDocumentsComponent_ng_template_44_Template_button_click_21_listener", "onSaveMultiple", "isAddNew", "ɵɵtwoWayProperty", "ɵɵpureFunction1", "_c0", "length", "CFile", "ɵɵsanitizeUrl", "RelatedDocumentsComponent_ng_template_46_div_8_div_4_Template_div_click_0_listener", "_r20", "openNewFile", "RelatedDocumentsComponent_ng_template_46_div_8_div_3_Template", "RelatedDocumentsComponent_ng_template_46_div_8_div_4_Template", "checkImage", "uploadedFile", "Cimg", "_c1", "RelatedDocumentsComponent_ng_template_46_div_18_img_3_Template", "RelatedDocumentsComponent_ng_template_46_div_18_i_4_Template", "RelatedDocumentsComponent_ng_template_46_div_18_Template_nb_icon_click_7_listener", "_r22", "deleteItem", "includes", "RelatedDocumentsComponent_ng_template_46_Template_input_ngModelChange_7_listener", "_r19", "RelatedDocumentsComponent_ng_template_46_div_8_Template", "RelatedDocumentsComponent_ng_template_46_Template_input_change_13_listener", "onChooseFile", "RelatedDocumentsComponent_ng_template_46_Template_button_click_15_listener", "uploadFileRef_r21", "RelatedDocumentsComponent_ng_template_46_div_18_Template", "RelatedDocumentsComponent_ng_template_46_Template_nb_select_ngModelChange_22_listener", "CStatus", "RelatedDocumentsComponent_ng_template_46_Template_textarea_ngModelChange_30_listener", "CSubmitRemark", "RelatedDocumentsComponent_ng_template_46_Template_button_click_32_listener", "ref_r23", "RelatedDocumentsComponent_ng_template_46_Template_button_click_34_listener", "onSave", "RelatedDocumentsComponent", "constructor", "allow", "dialogService", "valid", "message", "buildCaseFileService", "activatedRoute", "_utilityService", "filterCategoryName", "filterFileName", "buildCaseId", "listBuildCaseFile", "undefined", "destroy", "currentPage", "ngOnInit", "parseInt", "snapshot", "paramMap", "get", "getListBuildCaseFile", "page", "apiBuildCaseFileGetListBuildCaseFilePost$Json", "body", "CBuildCaseID", "PageIndex", "PageSize", "pipe", "subscribe", "res", "Entries", "totalRecords", "TotalItems", "onAddNew", "dialog", "open", "onAddMultiplesFile", "ref", "item", "apiBuildCaseFileGetBuildCaseFileByIdPost$Json", "CBuildCaseFileID", "window", "confirm", "apiBuildCaseFileDeleteBuildCaseFilePost$Json", "StatusCode", "showSucessMSG", "base64ToBlob", "base64", "contentType", "base64Data", "split", "byteCharacters", "atob", "byteArrays", "offset", "slice", "byteNumbers", "Array", "i", "charCodeAt", "byteArray", "Uint8Array", "push", "Blob", "type", "validation", "errorMessages", "showErrorMSGs", "request", "CFileUpload", "apiBuildCaseFileSaveBuildCaseFilePost$Json", "showErrorMSG", "Message", "clear", "required", "addErrorMessage", "event", "files", "target", "fileRegex", "test", "reader", "FileReader", "readAsDataURL", "onload", "e", "file", "URL", "createObjectURL", "result", "toString", "CFileType", "PDF", "value", "base64Str", "Date", "getTime", "data", "extension", "getFileExtension", "filter", "x", "validationMultiple", "listFilesUpload", "map", "apiBuildCaseFileSaveMultipleBuildCaseFilePost$Json", "CFiles", "getListPageChange", "fileName", "console", "log", "ɵɵdirectiveInject", "i1", "AllowHelper", "i2", "NbDialogService", "i3", "ValidationHelper", "i4", "MessageService", "i5", "BuildCaseFileService", "i6", "ActivatedRoute", "i7", "UtilityService", "selectors", "standalone", "features", "ɵɵInheritDefinitionFeature", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "RelatedDocumentsComponent_Template", "rf", "ctx", "RelatedDocumentsComponent_Template_input_ngModelChange_9_listener", "_r1", "RelatedDocumentsComponent_Template_input_ngModelChange_14_listener", "RelatedDocumentsComponent_Template_button_click_16_listener", "multiple_r2", "RelatedDocumentsComponent_Template_button_click_18_listener", "RelatedDocumentsComponent_Template_button_click_20_listener", "RelatedDocumentsComponent_tr_39_Template", "RelatedDocumentsComponent_Template_ngx_pagination_CollectionSizeChange_41_listener", "RelatedDocumentsComponent_Template_ngx_pagination_PageChange_41_listener", "pageIndex", "RelatedDocumentsComponent_Template_ngx_pagination_PageSizeChange_41_listener", "pageSize", "RelatedDocumentsComponent_ng_template_42_Template", "ɵɵtemplateRefExtractor", "RelatedDocumentsComponent_ng_template_44_Template", "RelatedDocumentsComponent_ng_template_46_Template", "i8", "Ng<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "NgSwitch", "NgSwitchCase", "i9", "DefaultValueAccessor", "NgControlStatus", "NgModel", "NbCardComponent", "NbCardBodyComponent", "NbCardFooterComponent", "NbCardHeaderComponent", "NbInputDirective", "NbSelectComponent", "NbOptionComponent", "NbIconComponent", "i10", "PaginationComponent", "i11", "BaseLabelDirective", "styles"], "sources": ["C:\\Users\\<USER>\\Documents\\jeansalechange\\adminFront\\src\\app\\pages\\construction-project-management\\related-documents\\related-documents.component.ts", "C:\\Users\\<USER>\\Documents\\jeansalechange\\adminFront\\src\\app\\pages\\construction-project-management\\related-documents\\related-documents.component.html"], "sourcesContent": ["import { Component, DestroyRef, OnInit, inject } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { AllowHelper } from 'src/app/shared/helper/allowHelper';\r\nimport { NbDialogService } from '@nebular/theme';\r\nimport { ValidationHelper } from 'src/app/shared/helper/validationHelper';\r\nimport { MessageService } from 'src/app/shared/services/message.service';\r\nimport { BuildCaseFileService } from 'src/services/api/services';\r\nimport { takeUntilDestroyed } from '@angular/core/rxjs-interop';\r\nimport { BuildCaseFileRes, EnumStatusCode } from 'src/services/api/models';\r\nimport { EnumFileType } from 'src/app/shared/enum/enumFileType';\r\nimport { ApiBuildCaseFileSaveBuildCaseFilePost$Json$Params } from 'src/services/api/fn/build-case-file/api-build-case-file-save-build-case-file-post-json';\r\nimport * as moment from 'moment';\r\nimport { ActivatedRoute } from '@angular/router';\r\nimport { DateFormatHourPipe } from 'src/app/@theme/pipes';\r\nimport { SharedModule } from '../../components/shared.module';\r\nimport { BaseComponent } from '../../components/base/baseComponent';\r\nimport { cApproveStatusPipe } from 'src/app/@theme/pipes/mapping.pipe';\r\nimport { UtilityService } from 'src/app/shared/services/utility.service';\r\nimport { tap } from 'rxjs';\r\n\r\n@Component({\r\n  selector: 'app-related-documents',\r\n  standalone: true,\r\n  imports: [CommonModule, SharedModule, DateFormatHourPipe, cApproveStatusPipe],\r\n  templateUrl: './related-documents.component.html',\r\n  styleUrls: ['./related-documents.component.scss'],\r\n})\r\nexport class RelatedDocumentsComponent extends BaseComponent implements OnInit {\r\n  filterCategoryName: string = '';\r\n  filterFileName: string = '';\r\n  buildCaseId: number = 1;\r\n\r\n  listBuildCaseFile: BuildCaseFileRes[] = [];\r\n\r\n  isAddNew = false;\r\n  currentItem: BuildCaseFileRes;\r\n  uploadedFile: any = undefined;\r\n\r\n  listFiles: any[] = []\r\n\r\n  destroy = inject(DestroyRef);\r\n\r\n  currentPage: number = -1;\r\n  constructor(\r\n    protected override allow: AllowHelper,\r\n    private dialogService: NbDialogService,\r\n    private valid: ValidationHelper,\r\n    private message: MessageService,\r\n    private buildCaseFileService: BuildCaseFileService,\r\n    private activatedRoute: ActivatedRoute,\r\n    private _utilityService: UtilityService\r\n  ) {\r\n    super(allow);\r\n  }\r\n\r\n  override ngOnInit(): void {\r\n    this.buildCaseId = 1;\r\n    this.buildCaseId = parseInt(this.activatedRoute.snapshot.paramMap!.get(\"id\")!);\r\n    this.getListBuildCaseFile(1);\r\n  }\r\n\r\n  getListBuildCaseFile(page: number) {\r\n    this.buildCaseFileService\r\n      .apiBuildCaseFileGetListBuildCaseFilePost$Json({\r\n        body: {\r\n          CBuildCaseID: this.buildCaseId,\r\n          CName: this.filterFileName ? this.filterFileName : undefined,\r\n          CCategoryName: this.filterCategoryName\r\n            ? this.filterCategoryName\r\n            : undefined,\r\n          PageIndex: page,\r\n          PageSize: 10\r\n        },\r\n      })\r\n      .pipe(takeUntilDestroyed(this.destroy))\r\n      .subscribe((res) => {\r\n        this.listBuildCaseFile = res.Entries ?? [];\r\n        this.totalRecords = res.TotalItems!\r\n      });\r\n  }\r\n  onAddNew(dialog: any) {\r\n    this.isAddNew = true;\r\n    this.uploadedFile = undefined;\r\n    this.currentItem = {\r\n      CStatus: 0,\r\n    };\r\n    this.dialogService.open(dialog);\r\n  }\r\n  onAddMultiplesFile(dialog: any) {\r\n    this.isAddNew = true;\r\n    this.uploadedFile = undefined;\r\n    this.currentItem = {\r\n      CCategoryName: \"\"\r\n    };\r\n    this.dialogService.open(dialog);\r\n  }\r\n\r\n  onEdit(ref: any, item?: any) {\r\n    this.isAddNew = false;\r\n    this.uploadedFile = undefined;\r\n    this.currentItem = {};\r\n    this.buildCaseFileService\r\n      .apiBuildCaseFileGetBuildCaseFileByIdPost$Json({\r\n        body: {\r\n          CBuildCaseFileID: item.CID,\r\n        },\r\n      })\r\n      .pipe(takeUntilDestroyed(this.destroy))\r\n      .subscribe((res) => {\r\n        this.currentItem = res.Entries!;\r\n      });\r\n\r\n    this.dialogService.open(ref);\r\n  }\r\n\r\n  onDelete(item: BuildCaseFileRes, index: number) {\r\n    if (window.confirm(`確定要刪除【項目${item.CID}】?`)) {\r\n      this.buildCaseFileService\r\n        .apiBuildCaseFileDeleteBuildCaseFilePost$Json({\r\n          body: {\r\n            CBuildCaseFileID: item.CID,\r\n          },\r\n        })\r\n        .pipe(takeUntilDestroyed(this.destroy))\r\n        .subscribe((res) => {\r\n          if (res.StatusCode === 0) {\r\n            this.message.showSucessMSG('執行成功');\r\n            this.getListBuildCaseFile(this.currentPage);\r\n          }\r\n        });\r\n    }\r\n  }\r\n\r\n  showReason(ref: any, item: BuildCaseFileRes) {\r\n    this.dialogService.open(ref);\r\n    this.currentItem = item;\r\n  }\r\n\r\n  base64ToBlob(base64: string, contentType: string = ''): Blob {\r\n    const base64Data = base64.split(',')[1] || base64;\r\n    // Decode base64 string\r\n    const byteCharacters = atob(base64Data);\r\n    // Create a byte array with length equal to the number of bytes in the string\r\n    const byteArrays: Uint8Array[] = [];\r\n    for (let offset = 0; offset < byteCharacters.length; offset += 512) {\r\n      const slice = byteCharacters.slice(offset, offset + 512);\r\n      const byteNumbers = new Array(slice.length);\r\n      for (let i = 0; i < slice.length; i++) {\r\n        byteNumbers[i] = slice.charCodeAt(i);\r\n      }\r\n      const byteArray = new Uint8Array(byteNumbers);\r\n      byteArrays.push(byteArray);\r\n    }\r\n    return new Blob(byteArrays, { type: contentType });\r\n  }\r\n\r\n  onSave(ref: any) {\r\n    this.validation();\r\n    if (this.valid.errorMessages.length > 0) {\r\n      this.message.showErrorMSGs(this.valid.errorMessages);\r\n      return;\r\n    }\r\n\r\n    let request: ApiBuildCaseFileSaveBuildCaseFilePost$Json$Params = {\r\n      body: {\r\n        CBuildCaseID: this.isAddNew ? this.buildCaseId : undefined,\r\n        CBuildCaseFileID: this.isAddNew ? undefined : this.currentItem.CID,\r\n        CStatus: this.currentItem.CStatus,\r\n        CSubmitRemark: this.currentItem.CSubmitRemark ? this.currentItem.CSubmitRemark : undefined,\r\n        CFile: this.uploadedFile.CFileUpload,\r\n        CCategoryName: this.currentItem.CCategoryName ? this.currentItem.CCategoryName : undefined,\r\n      },\r\n    };\r\n    this.buildCaseFileService\r\n      .apiBuildCaseFileSaveBuildCaseFilePost$Json(request)\r\n      .subscribe((res) => {\r\n        if (res.StatusCode === 0) {\r\n          this.message.showSucessMSG('儲存成功');\r\n        } else {\r\n          this.message.showErrorMSG(res.Message!);\r\n        }\r\n        ref.close();\r\n        this.getListBuildCaseFile(1);\r\n      });\r\n  }\r\n\r\n  validation() {\r\n    this.valid.clear();\r\n    this.valid.required(`送審說明`, this.currentItem.CSubmitRemark);\r\n    this.valid.required(`分類名稱`, this.currentItem.CCategoryName);\r\n    if (this.isAddNew && !this.uploadedFile) {\r\n      this.valid.addErrorMessage(`上傳 必填`);\r\n    }\r\n  }\r\n\r\n  onChooseFile(event: any) {\r\n    let files = event.target.files;\r\n    const fileRegex = /pdf|jpg|jpeg|png/i;\r\n\r\n    if (!fileRegex.test(files[0].type)) {\r\n      this.message.showErrorMSG('檔案格式錯誤，僅限pdf、圖片檔');\r\n      return;\r\n    }\r\n\r\n    let reader = new FileReader();\r\n    reader.readAsDataURL(files[0]);\r\n    reader.onload = (e) => {\r\n      let file: string = \"\"\r\n      if (!files[0].name.includes('pdf')) {\r\n        file = URL.createObjectURL(files[0]);\r\n      }\r\n      this.uploadedFile = {\r\n        CName: files[0].name,\r\n        CFile: e.target?.result?.toString().split(',')[1],\r\n        Cimg: files[0].name.includes('pdf') ? files[0] : file,\r\n        CFileUpload: files[0],\r\n        CFileType: EnumFileType.PDF,\r\n      };\r\n      event.target.value = null;\r\n    };\r\n  }\r\n\r\n  onMultipleFile(event: any) {\r\n    for (let index = 0; index < event.target.files.length; index++) {\r\n      const file = event.target.files[index];\r\n      if (file) {\r\n        let reader = new FileReader();\r\n        reader.readAsDataURL(file);\r\n        reader.onload = () => {\r\n          let base64Str: string = reader.result as string;\r\n          if (!base64Str) {\r\n            return;\r\n          }\r\n          file.id = new Date().getTime();\r\n          this.listFiles.push({\r\n            id: new Date().getTime(),\r\n            name: file.name,\r\n            data: base64Str,\r\n            extension: this._utilityService.getFileExtension(file.name),\r\n            CFile: file\r\n          });\r\n          event.target.value = null;\r\n        };\r\n      }\r\n    }\r\n  }\r\n\r\n  deleteItem(item: any) {\r\n    if (window.confirm(`確定要移除【${item.CName}】?`)) {\r\n      this.uploadedFile = undefined;\r\n    }\r\n  }\r\n\r\n  removeFile(id: number) {\r\n    this.listFiles = this.listFiles.filter(x => x.id != id);\r\n  }\r\n\r\n  validationMultiple() {\r\n    this.valid.clear();\r\n    this.valid.required(`分類名稱`, this.currentItem.CCategoryName);\r\n    if (this.isAddNew && this.listFiles.length! == 0) {\r\n      this.valid.addErrorMessage(`上傳 必填`);\r\n    }\r\n  }\r\n\r\n  onSaveMultiple(ref: any) {\r\n    this.validationMultiple();\r\n    if (this.valid.errorMessages.length > 0) {\r\n      this.message.showErrorMSGs(this.valid.errorMessages);\r\n      return;\r\n    }\r\n\r\n    let listFilesUpload = this.listFiles.map(x => x.CFile)\r\n\r\n    this.buildCaseFileService.apiBuildCaseFileSaveMultipleBuildCaseFilePost$Json({\r\n      body: {\r\n        CBuildCaseID: this.buildCaseId,\r\n        CCategoryName: this.currentItem.CCategoryName!,\r\n        CFiles: listFilesUpload\r\n      }\r\n    }).pipe(\r\n      tap(res => {\r\n        if (res.StatusCode === 0) {\r\n          this.message.showSucessMSG('儲存成功');\r\n          ref.close();\r\n          this.getListBuildCaseFile(1);\r\n          this.listFiles = []\r\n          this.currentItem = {}\r\n        } else {\r\n          this.message.showErrorMSG(res.Message!);\r\n        }\r\n      })\r\n    ).subscribe();\r\n  }\r\n\r\n  getListPageChange(page: number) {\r\n    this.currentPage = page;\r\n    this.getListBuildCaseFile(page)\r\n  }\r\n\r\n  checkImage(fileName: string) {\r\n    return this._utilityService.getFileExtension(fileName).includes('png') || this._utilityService.getFileExtension(fileName).includes('jpeg') || this._utilityService.getFileExtension(fileName).includes('jpg')\r\n  }\r\n\r\n  openNewFile(fileName: string) {\r\n    console.log(fileName);\r\n    \r\n    window.open(`${fileName}`, '_blank');\r\n  }\r\n}\r\n", "<nb-card accent=\"success\">\r\n  <nb-card-header>\r\n    <!-- <ngx-breadcrumb></ngx-breadcrumb> -->\r\n    相關文件\r\n  </nb-card-header>\r\n  <nb-card-body>\r\n    <div class=\"row\">\r\n      <div class=\"col-md-6 col-xl-4\">\r\n        <div class=\"form-group d-flex align-items-center\">\r\n          <label for=\"name\" class=\"label mr-2\">分類名稱</label>\r\n          <input type=\"text\" nbInput fullWidth placeholder=\"請輸入分類名稱\" [(ngModel)]=\"filterCategoryName\" />\r\n        </div>\r\n      </div>\r\n      <div class=\"col-md-6 col-xl-4\">\r\n        <div class=\"form-group d-flex align-items-center\">\r\n          <label for=\"name\" class=\"label mr-2\">檔名</label>\r\n          <input type=\"text\" nbInput fullWidth placeholder=\"請輸入檔名\" [(ngModel)]=\"filterFileName\" />\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <div class=\"d-flex justify-end gap-2\">\r\n      <button class=\"btn btn-info\" (click)=\"onAddMultiplesFile(multiple)\">批次匯入</button>\r\n      <button class=\"btn btn-info\" (click)=\"getListBuildCaseFile(1)\">查詢</button>\r\n      <button class=\"btn btn-success\" (click)=\"onAddNew(dialog)\">新增</button>\r\n    </div>\r\n    <div class=\"table-responsive mt-4\">\r\n      <table class=\"table table-striped border\" style=\"min-width: 1000px; background-color: #f3f3f3\">\r\n        <thead>\r\n          <tr style=\"background-color: #27ae60; color: white\">\r\n            <th style=\"min-width: 5%\">流水號</th>\r\n            <th style=\"min-width: 15%\">分類名稱</th>\r\n            <th style=\"min-width: 15%\">檔名</th>\r\n            <th style=\"min-width: 15%\">審核狀態 </th>\r\n            <th style=\"min-width: 15%\">審核日期 </th>\r\n            <th style=\"min-width: 5%\">動作</th>\r\n          </tr>\r\n        </thead>\r\n        <tbody>\r\n          <tr *ngFor=\"let item of listBuildCaseFile; let i = index\">\r\n            <td>{{ item.CID }}</td>\r\n            <td>{{ item.CCategoryName }}</td>\r\n            <td>{{ item.CName }}</td>\r\n            <td>{{ item.CApproveStatus! | cApproveStatus }}</td>\r\n            <td>{{ item.CApproveDate ? (item.CApproveDate | dateFormatHour) : '' }}</td>\r\n            <!-- <td>{{ item.CFile }}</td> -->\r\n            <td>\r\n              <ng-container [ngSwitch]=\"item.CApproveStatus!\">\r\n                <ng-container *ngSwitchCase=\"0\">\r\n                  <button type=\"button\" class=\"btn btn-outline-success m-1 btn-sm\" (click)=\"onEdit(dialog, item)\">\r\n                    <i class=\"fas fa-edit mr-1\"></i>編輯\r\n                  </button>\r\n                  <button type=\"button\" class=\"btn btn-outline-danger m-1 btn-sm\" (click)=\"onDelete(item, i)\">\r\n                    <i class=\"far fa-trash-alt mr-1\"></i>刪除\r\n                  </button>\r\n                </ng-container>\r\n                <ng-container *ngSwitchCase=\"1\">\r\n                  <button type=\"button\" class=\"btn btn-outline-danger m-1 btn-sm\" (click)=\"onDelete(item, i)\">\r\n                    <i class=\"far fa-trash-alt mr-1\"></i>刪除\r\n                  </button>\r\n                </ng-container>\r\n                <ng-container *ngSwitchCase=\"2\">\r\n                  <button type=\"button\" class=\"btn btn-outline-info m-1 btn-sm\"\r\n                    (click)=\"showReason(reasonReject, item)\">\r\n                    <i class=\"fas fa-search mr-1\"></i>退回原因\r\n                  </button>\r\n                  <button type=\"button\" class=\"btn btn-outline-danger m-1 btn-sm\" (click)=\"onDelete(item, i)\">\r\n                    <i class=\"far fa-trash-alt mr-1\"></i>刪除\r\n                  </button>\r\n                </ng-container>\r\n              </ng-container>\r\n            </td>\r\n          </tr>\r\n        </tbody>\r\n      </table>\r\n    </div>\r\n  </nb-card-body>\r\n  <nb-card-footer>\r\n    <ngx-pagination [(CollectionSize)]=\"totalRecords\" [(Page)]=\"pageIndex\" [(PageSize)]=\"pageSize\"\r\n      (PageChange)=\"getListPageChange($event)\"></ngx-pagination>\r\n  </nb-card-footer>\r\n</nb-card>\r\n\r\n<ng-template #reasonReject let-ref=\"dialogRef\">\r\n  <nb-card style=\"width: 500px;\">\r\n    <nb-card-body>\r\n      <div class=\"flex items-start\">\r\n        <span class=\"w-[20%]\">退回原因</span>\r\n        <textarea nbInput class=\"resize-none !max-w-full w-full\" [value]=\"currentItem.CExamineRejectNote\" [rows]=\"4\"\r\n          [disabled]=\"true\"></textarea>\r\n      </div>\r\n    </nb-card-body>\r\n    <nb-card-footer class=\"d-flex justify-content-center\">\r\n      <button class=\"btn btn-info mr-2\" (click)=\"ref.close(); this.currentItem = {}\">關閉</button>\r\n    </nb-card-footer>\r\n  </nb-card>\r\n</ng-template>\r\n\r\n<ng-template #multiple let-ref=\"dialogRef\">\r\n  <nb-card style=\"max-height: 95vh; min-width: 400px;\">\r\n    <nb-card-header>{{ isAddNew ? '新增' : '編輯'}}</nb-card-header>\r\n    <nb-card-body>\r\n\r\n      <div class=\"form-group d-flex align-items-center\">\r\n        <label for=\"categoryName\" class=\"required-field mr-4\" style=\"min-width:75px\" baseLabel>分類名稱</label>\r\n        <input type=\"text\" nbInput fullWidth placeholder=\"請輸入分類名稱\" [(ngModel)]=\"currentItem.CCategoryName\" />\r\n      </div>\r\n\r\n      <div class=\"form-group d-flex align-items-baseline\">\r\n        <div class=\"d-flex flex-col mr-3\">\r\n          <label for=\"file\" class=\"mb-0\" [ngClass]=\"{'required-field': isAddNew}\" style=\"min-width:75px\"\r\n            baseLabel>上傳</label>\r\n        </div>\r\n        <input id=\"file\" type=\"file\" multiple hidden accept=\".jpg,.jpeg,.png, .pdf\" (change)=\"onMultipleFile($event)\"\r\n          #uploadFileRef />\r\n        <button class=\"btn btn-success btn-sm\" (click)=\"uploadFileRef.click()\">\r\n          <i class=\"fas fa-plus mr-1\"></i>上傳</button>\r\n      </div>\r\n      <!-- 上傳檔案預覽 -->\r\n      <div *ngIf=\"listFiles.length > 0\" class=\"w-full\">\r\n        <div class=\"flex items-center justify-between\" *ngFor=\"let file of listFiles; let i = index\">\r\n          <div>\r\n            {{file.name}}\r\n          </div>\r\n          <td class=\"text-center\">\r\n            <button class=\"btn btn-outline-danger btn-sm m-1\" (click)=\"removeFile(file.id)\">删除</button>\r\n          </td>\r\n        </div>\r\n      </div>\r\n    </nb-card-body>\r\n    <nb-card-footer class=\"d-flex justify-content-center\">\r\n      <button class=\"btn btn-danger mr-2\" (click)=\"ref.close()\">取消</button>\r\n      <button class=\"btn btn-success\" (click)=\"onSaveMultiple(ref)\">儲存</button>\r\n    </nb-card-footer>\r\n  </nb-card>\r\n</ng-template>\r\n\r\n<ng-template #dialog let-data let-ref=\"dialogRef\">\r\n  <nb-card style=\"max-height: 95vh; min-width: 400px;\">\r\n    <nb-card-header>{{ isAddNew ? '新增' : '編輯'}}</nb-card-header>\r\n    <nb-card-body>\r\n      <!-- <div class=\"form-group d-flex align-items-center\">\r\n        <label for=\"name\" class=\"required-field mr-4\" style=\"min-width:75px\" baseLabel>檔名</label>\r\n        <input type=\"text\" fullWidth nbInput placeholder=\"請輸入檔名\" [(ngModel)]=\"currentItem.CName\"\r\n          [disabled]=\"!isAddNew\" />\r\n      </div> -->\r\n      <div class=\"form-group d-flex align-items-center\">\r\n        <label for=\"categoryName\" class=\"required-field mr-4\" style=\"min-width:75px\" baseLabel>分類名稱</label>\r\n        <input type=\"text\" nbInput fullWidth placeholder=\"請輸入分類名稱\" [(ngModel)]=\"currentItem.CCategoryName\" />\r\n      </div>\r\n      <div class=\"form-group d-flex align-items-center\" *ngIf=\"!isAddNew\">\r\n        <label for=\"categoryName\" class=\"mr-4\" style=\"min-width:75px\" baseLabel>檔案連結</label>\r\n        <div style=\"width: 150px; height: 150px;\" *ngIf=\"checkImage(currentItem.CFile!)\">\r\n          <img class=\"fit-size\" [src]=\"currentItem.CFile!\">\r\n        </div>\r\n        <div *ngIf=\"!checkImage(currentItem.CFile!)\" (click)=\"openNewFile(currentItem.CFile!)\"\r\n          style=\"cursor: pointer; width: 50px; height: 50px; border: 1px solid gray; border-radius: 10px; display: flex; align-items: center; justify-content: center;\">\r\n          <nb-icon icon=\"file-add-outline\"></nb-icon>\r\n        </div>\r\n      </div>\r\n      <div class=\"form-group d-flex align-items-baseline\">\r\n        <div class=\"d-flex flex-col mr-3\">\r\n          <label for=\"file\" class=\"mb-0\" [ngClass]=\"{'required-field': isAddNew}\" style=\"min-width:75px\"\r\n            baseLabel>上傳</label>\r\n        </div>\r\n        <input id=\"file\" type=\"file\" hidden accept=\".jpg,.jpeg,.png, .pdf\" (change)=\"onChooseFile($event)\"\r\n          #uploadFileRef />\r\n        <button class=\"btn btn-success btn-sm\" (click)=\"uploadFileRef.click()\">\r\n          <i class=\"fas fa-plus mr-1\"></i>上傳</button>\r\n      </div>\r\n      <!-- 上傳檔案預覽 -->\r\n      <div *ngIf=\"uploadedFile\">\r\n        <ul style=\"margin-left:100px\">\r\n          <li class=\"file-item\">\r\n            <img [src]=\"uploadedFile.Cimg\" alt=\"\" style=\"max-width:150px; width: 100%\" class=\"mr-2\"\r\n              *ngIf=\"!uploadedFile.CName?.includes('pdf')\">\r\n            <i class=\"fa-solid fa-file-pdf text-primary\" *ngIf=\"uploadedFile.CName?.includes('pdf')\"\r\n              [ngClass]=\"{'download': !isAddNew}\"></i>\r\n            <a class=\"text-primary download\" [href]=\"uploadedFile.Cimg ? uploadedFile.Cimg : uploadedFile.CFile\"\r\n              target=\"_blank\">{{uploadedFile.CName}}</a>\r\n            <nb-icon icon=\"trash-2-outline\" class=\"trash-icon\" (click)=\"deleteItem(uploadedFile)\"\r\n              [hidden]=\"!isAddNew\"></nb-icon>\r\n          </li>\r\n        </ul>\r\n      </div>\r\n      <div class=\"form-group d-flex align-items-center\">\r\n        <label for=\"status\" baseLabel class=\"required-field mr-4\" style=\"min-width:75px\">狀態</label>\r\n        <nb-select fullWidth placeholder=\"請選擇樓層\" [(ngModel)]=\"currentItem.CStatus\">\r\n          <nb-option [value]=\"1\">啟用</nb-option>\r\n          <nb-option [value]=\"0\">停用</nb-option>\r\n        </nb-select>\r\n\r\n      </div>\r\n\r\n      <div class=\"form-group d-flex align-items-center\">\r\n        <label for=\"status\" baseLabel class=\"required-field mr-4\" style=\"min-width:75px\">送審說明</label>\r\n        <textarea nbInput [(ngModel)]=\"currentItem.CSubmitRemark\" [rows]=\"4\"\r\n          class=\"resize-none !max-w-full w-full\"></textarea>\r\n\r\n      </div>\r\n    </nb-card-body>\r\n    <nb-card-footer class=\"d-flex justify-content-center\">\r\n      <button class=\"btn btn-danger mr-2\" (click)=\"ref.close()\">取消</button>\r\n      <button class=\"btn btn-success\" (click)=\"onSave(ref)\">儲存</button>\r\n    </nb-card-footer>\r\n  </nb-card>\r\n</ng-template>"], "mappings": "AAAA,SAAoBA,UAAU,EAAUC,MAAM,QAAQ,eAAe;AACrE,SAASC,YAAY,QAAQ,iBAAiB;AAM9C,SAASC,kBAAkB,QAAQ,4BAA4B;AAE/D,SAASC,YAAY,QAAQ,kCAAkC;AAI/D,SAASC,kBAAkB,QAAQ,sBAAsB;AACzD,SAASC,YAAY,QAAQ,gCAAgC;AAC7D,SAASC,aAAa,QAAQ,qCAAqC;AACnE,SAASC,kBAAkB,QAAQ,mCAAmC;AAEtE,SAASC,GAAG,QAAQ,MAAM;;;;;;;;;;;;;;;;;;;;;;IC6BVC,EAAA,CAAAC,uBAAA,GAAgC;IAC9BD,EAAA,CAAAE,cAAA,iBAAgG;IAA/BF,EAAA,CAAAG,UAAA,mBAAAC,iFAAA;MAAAJ,EAAA,CAAAK,aAAA,CAAAC,GAAA;MAAA,MAAAC,OAAA,GAAAP,EAAA,CAAAQ,aAAA,GAAAC,SAAA;MAAA,MAAAC,MAAA,GAAAV,EAAA,CAAAQ,aAAA;MAAA,MAAAG,SAAA,GAAAX,EAAA,CAAAY,WAAA;MAAA,OAAAZ,EAAA,CAAAa,WAAA,CAASH,MAAA,CAAAI,MAAA,CAAAH,SAAA,EAAAJ,OAAA,CAAoB;IAAA,EAAC;IAC7FP,EAAA,CAAAe,SAAA,YAAgC;IAAAf,EAAA,CAAAgB,MAAA,oBAClC;IAAAhB,EAAA,CAAAiB,YAAA,EAAS;IACTjB,EAAA,CAAAE,cAAA,iBAA4F;IAA5BF,EAAA,CAAAG,UAAA,mBAAAe,iFAAA;MAAAlB,EAAA,CAAAK,aAAA,CAAAC,GAAA;MAAA,MAAAa,MAAA,GAAAnB,EAAA,CAAAQ,aAAA;MAAA,MAAAD,OAAA,GAAAY,MAAA,CAAAV,SAAA;MAAA,MAAAW,IAAA,GAAAD,MAAA,CAAAE,KAAA;MAAA,MAAAX,MAAA,GAAAV,EAAA,CAAAQ,aAAA;MAAA,OAAAR,EAAA,CAAAa,WAAA,CAASH,MAAA,CAAAY,QAAA,CAAAf,OAAA,EAAAa,IAAA,CAAiB;IAAA,EAAC;IACzFpB,EAAA,CAAAe,SAAA,YAAqC;IAAAf,EAAA,CAAAgB,MAAA,oBACvC;IAAAhB,EAAA,CAAAiB,YAAA,EAAS;;;;;;;IAEXjB,EAAA,CAAAC,uBAAA,GAAgC;IAC9BD,EAAA,CAAAE,cAAA,iBAA4F;IAA5BF,EAAA,CAAAG,UAAA,mBAAAoB,iFAAA;MAAAvB,EAAA,CAAAK,aAAA,CAAAmB,GAAA;MAAA,MAAAL,MAAA,GAAAnB,EAAA,CAAAQ,aAAA;MAAA,MAAAD,OAAA,GAAAY,MAAA,CAAAV,SAAA;MAAA,MAAAW,IAAA,GAAAD,MAAA,CAAAE,KAAA;MAAA,MAAAX,MAAA,GAAAV,EAAA,CAAAQ,aAAA;MAAA,OAAAR,EAAA,CAAAa,WAAA,CAASH,MAAA,CAAAY,QAAA,CAAAf,OAAA,EAAAa,IAAA,CAAiB;IAAA,EAAC;IACzFpB,EAAA,CAAAe,SAAA,YAAqC;IAAAf,EAAA,CAAAgB,MAAA,oBACvC;IAAAhB,EAAA,CAAAiB,YAAA,EAAS;;;;;;;IAEXjB,EAAA,CAAAC,uBAAA,GAAgC;IAC9BD,EAAA,CAAAE,cAAA,iBAC2C;IAAzCF,EAAA,CAAAG,UAAA,mBAAAsB,iFAAA;MAAAzB,EAAA,CAAAK,aAAA,CAAAqB,IAAA;MAAA,MAAAnB,OAAA,GAAAP,EAAA,CAAAQ,aAAA,GAAAC,SAAA;MAAA,MAAAC,MAAA,GAAAV,EAAA,CAAAQ,aAAA;MAAA,MAAAmB,gBAAA,GAAA3B,EAAA,CAAAY,WAAA;MAAA,OAAAZ,EAAA,CAAAa,WAAA,CAASH,MAAA,CAAAkB,UAAA,CAAAD,gBAAA,EAAApB,OAAA,CAA8B;IAAA,EAAC;IACxCP,EAAA,CAAAe,SAAA,YAAkC;IAAAf,EAAA,CAAAgB,MAAA,gCACpC;IAAAhB,EAAA,CAAAiB,YAAA,EAAS;IACTjB,EAAA,CAAAE,cAAA,iBAA4F;IAA5BF,EAAA,CAAAG,UAAA,mBAAA0B,iFAAA;MAAA7B,EAAA,CAAAK,aAAA,CAAAqB,IAAA;MAAA,MAAAP,MAAA,GAAAnB,EAAA,CAAAQ,aAAA;MAAA,MAAAD,OAAA,GAAAY,MAAA,CAAAV,SAAA;MAAA,MAAAW,IAAA,GAAAD,MAAA,CAAAE,KAAA;MAAA,MAAAX,MAAA,GAAAV,EAAA,CAAAQ,aAAA;MAAA,OAAAR,EAAA,CAAAa,WAAA,CAASH,MAAA,CAAAY,QAAA,CAAAf,OAAA,EAAAa,IAAA,CAAiB;IAAA,EAAC;IACzFpB,EAAA,CAAAe,SAAA,YAAqC;IAAAf,EAAA,CAAAgB,MAAA,oBACvC;IAAAhB,EAAA,CAAAiB,YAAA,EAAS;;;;;;IA5BfjB,EADF,CAAAE,cAAA,SAA0D,SACpD;IAAAF,EAAA,CAAAgB,MAAA,GAAc;IAAAhB,EAAA,CAAAiB,YAAA,EAAK;IACvBjB,EAAA,CAAAE,cAAA,SAAI;IAAAF,EAAA,CAAAgB,MAAA,GAAwB;IAAAhB,EAAA,CAAAiB,YAAA,EAAK;IACjCjB,EAAA,CAAAE,cAAA,SAAI;IAAAF,EAAA,CAAAgB,MAAA,GAAgB;IAAAhB,EAAA,CAAAiB,YAAA,EAAK;IACzBjB,EAAA,CAAAE,cAAA,SAAI;IAAAF,EAAA,CAAAgB,MAAA,GAA2C;;IAAAhB,EAAA,CAAAiB,YAAA,EAAK;IACpDjB,EAAA,CAAAE,cAAA,UAAI;IAAAF,EAAA,CAAAgB,MAAA,IAAmE;;IAAAhB,EAAA,CAAAiB,YAAA,EAAK;IAE5EjB,EAAA,CAAAE,cAAA,UAAI;IACFF,EAAA,CAAAC,uBAAA,QAAgD;IAc9CD,EAbA,CAAA8B,UAAA,KAAAC,wDAAA,2BAAgC,KAAAC,wDAAA,2BAQA,KAAAC,wDAAA,2BAKA;;IAWtCjC,EADE,CAAAiB,YAAA,EAAK,EACF;;;;IAhCCjB,EAAA,CAAAkC,SAAA,GAAc;IAAdlC,EAAA,CAAAmC,iBAAA,CAAA5B,OAAA,CAAA6B,GAAA,CAAc;IACdpC,EAAA,CAAAkC,SAAA,GAAwB;IAAxBlC,EAAA,CAAAmC,iBAAA,CAAA5B,OAAA,CAAA8B,aAAA,CAAwB;IACxBrC,EAAA,CAAAkC,SAAA,GAAgB;IAAhBlC,EAAA,CAAAmC,iBAAA,CAAA5B,OAAA,CAAA+B,KAAA,CAAgB;IAChBtC,EAAA,CAAAkC,SAAA,GAA2C;IAA3ClC,EAAA,CAAAmC,iBAAA,CAAAnC,EAAA,CAAAuC,WAAA,OAAAhC,OAAA,CAAAiC,cAAA,EAA2C;IAC3CxC,EAAA,CAAAkC,SAAA,GAAmE;IAAnElC,EAAA,CAAAmC,iBAAA,CAAA5B,OAAA,CAAAkC,YAAA,GAAAzC,EAAA,CAAAuC,WAAA,SAAAhC,OAAA,CAAAkC,YAAA,OAAmE;IAGvDzC,EAAA,CAAAkC,SAAA,GAAiC;IAAjClC,EAAA,CAAA0C,UAAA,aAAAnC,OAAA,CAAAiC,cAAA,CAAiC;IAC9BxC,EAAA,CAAAkC,SAAA,EAAe;IAAflC,EAAA,CAAA0C,UAAA,mBAAe;IAQf1C,EAAA,CAAAkC,SAAA,EAAe;IAAflC,EAAA,CAAA0C,UAAA,mBAAe;IAKf1C,EAAA,CAAAkC,SAAA,EAAe;IAAflC,EAAA,CAAA0C,UAAA,mBAAe;;;;;;IA0BtC1C,EAHN,CAAAE,cAAA,kBAA+B,mBACf,cACkB,eACN;IAAAF,EAAA,CAAAgB,MAAA,+BAAI;IAAAhB,EAAA,CAAAiB,YAAA,EAAO;IACjCjB,EAAA,CAAAe,SAAA,mBAC+B;IAEnCf,EADE,CAAAiB,YAAA,EAAM,EACO;IAEbjB,EADF,CAAAE,cAAA,yBAAsD,iBAC2B;IAA7CF,EAAA,CAAAG,UAAA,mBAAAwC,0EAAA;MAAA,MAAAC,OAAA,GAAA5C,EAAA,CAAAK,aAAA,CAAAwC,IAAA,EAAAC,SAAA;MAAA,MAAApC,MAAA,GAAAV,EAAA,CAAAQ,aAAA;MAASoC,OAAA,CAAAG,KAAA,EAAW;MAAA,OAAA/C,EAAA,CAAAa,WAAA,CAAAH,MAAA,CAAAsC,WAAA,GAAqB,EAAE;IAAA,EAAC;IAAChD,EAAA,CAAAgB,MAAA,mBAAE;IAErFhB,EAFqF,CAAAiB,YAAA,EAAS,EAC3E,EACT;;;;IAPqDjB,EAAA,CAAAkC,SAAA,GAAwC;IAC/FlC,EADuD,CAAA0C,UAAA,UAAAhC,MAAA,CAAAsC,WAAA,CAAAC,kBAAA,CAAwC,WAAW,kBACzF;;;;;;IAgCjBjD,EADF,CAAAE,cAAA,cAA6F,UACtF;IACHF,EAAA,CAAAgB,MAAA,GACF;IAAAhB,EAAA,CAAAiB,YAAA,EAAM;IAEJjB,EADF,CAAAE,cAAA,aAAwB,iBAC0D;IAA9BF,EAAA,CAAAG,UAAA,mBAAA+C,uFAAA;MAAA,MAAAC,QAAA,GAAAnD,EAAA,CAAAK,aAAA,CAAA+C,IAAA,EAAA3C,SAAA;MAAA,MAAAC,MAAA,GAAAV,EAAA,CAAAQ,aAAA;MAAA,OAAAR,EAAA,CAAAa,WAAA,CAASH,MAAA,CAAA2C,UAAA,CAAAF,QAAA,CAAAG,EAAA,CAAmB;IAAA,EAAC;IAACtD,EAAA,CAAAgB,MAAA,mBAAE;IAEtFhB,EAFsF,CAAAiB,YAAA,EAAS,EACxF,EACD;;;;IALFjB,EAAA,CAAAkC,SAAA,GACF;IADElC,EAAA,CAAAuD,kBAAA,MAAAJ,QAAA,CAAAK,IAAA,MACF;;;;;IAJJxD,EAAA,CAAAE,cAAA,cAAiD;IAC/CF,EAAA,CAAA8B,UAAA,IAAA2B,8DAAA,kBAA6F;IAQ/FzD,EAAA,CAAAiB,YAAA,EAAM;;;;IAR4DjB,EAAA,CAAAkC,SAAA,EAAc;IAAdlC,EAAA,CAAA0C,UAAA,YAAAhC,MAAA,CAAAgD,SAAA,CAAc;;;;;;IApBlF1D,EADF,CAAAE,cAAA,kBAAqD,qBACnC;IAAAF,EAAA,CAAAgB,MAAA,GAA2B;IAAAhB,EAAA,CAAAiB,YAAA,EAAiB;IAIxDjB,EAHJ,CAAAE,cAAA,mBAAc,aAEsC,gBACuC;IAAAF,EAAA,CAAAgB,MAAA,+BAAI;IAAAhB,EAAA,CAAAiB,YAAA,EAAQ;IACnGjB,EAAA,CAAAE,cAAA,eAAqG;IAA1CF,EAAA,CAAA2D,gBAAA,2BAAAC,iFAAAC,MAAA;MAAA7D,EAAA,CAAAK,aAAA,CAAAyD,IAAA;MAAA,MAAApD,MAAA,GAAAV,EAAA,CAAAQ,aAAA;MAAAR,EAAA,CAAA+D,kBAAA,CAAArD,MAAA,CAAAsC,WAAA,CAAAX,aAAA,EAAAwB,MAAA,MAAAnD,MAAA,CAAAsC,WAAA,CAAAX,aAAA,GAAAwB,MAAA;MAAA,OAAA7D,EAAA,CAAAa,WAAA,CAAAgD,MAAA;IAAA,EAAuC;IACpG7D,EADE,CAAAiB,YAAA,EAAqG,EACjG;IAIFjB,EAFJ,CAAAE,cAAA,cAAoD,cAChB,iBAEpB;IAAAF,EAAA,CAAAgB,MAAA,oBAAE;IAChBhB,EADgB,CAAAiB,YAAA,EAAQ,EAClB;IACNjB,EAAA,CAAAE,cAAA,oBACmB;IADyDF,EAAA,CAAAG,UAAA,oBAAA6D,2EAAAH,MAAA;MAAA7D,EAAA,CAAAK,aAAA,CAAAyD,IAAA;MAAA,MAAApD,MAAA,GAAAV,EAAA,CAAAQ,aAAA;MAAA,OAAAR,EAAA,CAAAa,WAAA,CAAUH,MAAA,CAAAuD,cAAA,CAAAJ,MAAA,CAAsB;IAAA,EAAC;IAA7G7D,EAAA,CAAAiB,YAAA,EACmB;IACnBjB,EAAA,CAAAE,cAAA,kBAAuE;IAAhCF,EAAA,CAAAG,UAAA,mBAAA+D,2EAAA;MAAAlE,EAAA,CAAAK,aAAA,CAAAyD,IAAA;MAAA,MAAAK,iBAAA,GAAAnE,EAAA,CAAAY,WAAA;MAAA,OAAAZ,EAAA,CAAAa,WAAA,CAASsD,iBAAA,CAAAC,KAAA,EAAqB;IAAA,EAAC;IACpEpE,EAAA,CAAAe,SAAA,aAAgC;IAAAf,EAAA,CAAAgB,MAAA,oBAAE;IACtChB,EADsC,CAAAiB,YAAA,EAAS,EACzC;IAENjB,EAAA,CAAA8B,UAAA,KAAAuC,wDAAA,kBAAiD;IAUnDrE,EAAA,CAAAiB,YAAA,EAAe;IAEbjB,EADF,CAAAE,cAAA,0BAAsD,kBACM;IAAtBF,EAAA,CAAAG,UAAA,mBAAAmE,2EAAA;MAAA,MAAAC,OAAA,GAAAvE,EAAA,CAAAK,aAAA,CAAAyD,IAAA,EAAAhB,SAAA;MAAA,OAAA9C,EAAA,CAAAa,WAAA,CAAS0D,OAAA,CAAAxB,KAAA,EAAW;IAAA,EAAC;IAAC/C,EAAA,CAAAgB,MAAA,oBAAE;IAAAhB,EAAA,CAAAiB,YAAA,EAAS;IACrEjB,EAAA,CAAAE,cAAA,kBAA8D;IAA9BF,EAAA,CAAAG,UAAA,mBAAAqE,2EAAA;MAAA,MAAAD,OAAA,GAAAvE,EAAA,CAAAK,aAAA,CAAAyD,IAAA,EAAAhB,SAAA;MAAA,MAAApC,MAAA,GAAAV,EAAA,CAAAQ,aAAA;MAAA,OAAAR,EAAA,CAAAa,WAAA,CAASH,MAAA,CAAA+D,cAAA,CAAAF,OAAA,CAAmB;IAAA,EAAC;IAACvE,EAAA,CAAAgB,MAAA,oBAAE;IAEpEhB,EAFoE,CAAAiB,YAAA,EAAS,EAC1D,EACT;;;;IAlCQjB,EAAA,CAAAkC,SAAA,GAA2B;IAA3BlC,EAAA,CAAAmC,iBAAA,CAAAzB,MAAA,CAAAgE,QAAA,mCAA2B;IAKoB1E,EAAA,CAAAkC,SAAA,GAAuC;IAAvClC,EAAA,CAAA2E,gBAAA,YAAAjE,MAAA,CAAAsC,WAAA,CAAAX,aAAA,CAAuC;IAKjErC,EAAA,CAAAkC,SAAA,GAAwC;IAAxClC,EAAA,CAAA0C,UAAA,YAAA1C,EAAA,CAAA4E,eAAA,IAAAC,GAAA,EAAAnE,MAAA,CAAAgE,QAAA,EAAwC;IASrE1E,EAAA,CAAAkC,SAAA,GAA0B;IAA1BlC,EAAA,CAAA0C,UAAA,SAAAhC,MAAA,CAAAgD,SAAA,CAAAoB,MAAA,KAA0B;;;;;IAiC9B9E,EAAA,CAAAE,cAAA,cAAiF;IAC/EF,EAAA,CAAAe,SAAA,cAAiD;IACnDf,EAAA,CAAAiB,YAAA,EAAM;;;;IADkBjB,EAAA,CAAAkC,SAAA,EAA0B;IAA1BlC,EAAA,CAAA0C,UAAA,QAAAhC,MAAA,CAAAsC,WAAA,CAAA+B,KAAA,EAAA/E,EAAA,CAAAgF,aAAA,CAA0B;;;;;;IAElDhF,EAAA,CAAAE,cAAA,cACgK;IADnHF,EAAA,CAAAG,UAAA,mBAAA8E,mFAAA;MAAAjF,EAAA,CAAAK,aAAA,CAAA6E,IAAA;MAAA,MAAAxE,MAAA,GAAAV,EAAA,CAAAQ,aAAA;MAAA,OAAAR,EAAA,CAAAa,WAAA,CAASH,MAAA,CAAAyE,WAAA,CAAAzE,MAAA,CAAAsC,WAAA,CAAA+B,KAAA,CAA+B;IAAA,EAAC;IAEpF/E,EAAA,CAAAe,SAAA,kBAA2C;IAC7Cf,EAAA,CAAAiB,YAAA,EAAM;;;;;IAPNjB,EADF,CAAAE,cAAA,aAAoE,gBACM;IAAAF,EAAA,CAAAgB,MAAA,+BAAI;IAAAhB,EAAA,CAAAiB,YAAA,EAAQ;IAIpFjB,EAHA,CAAA8B,UAAA,IAAAsD,6DAAA,kBAAiF,IAAAC,6DAAA,kBAI+E;IAGlKrF,EAAA,CAAAiB,YAAA,EAAM;;;;IAPuCjB,EAAA,CAAAkC,SAAA,GAAoC;IAApClC,EAAA,CAAA0C,UAAA,SAAAhC,MAAA,CAAA4E,UAAA,CAAA5E,MAAA,CAAAsC,WAAA,CAAA+B,KAAA,EAAoC;IAGzE/E,EAAA,CAAAkC,SAAA,EAAqC;IAArClC,EAAA,CAAA0C,UAAA,UAAAhC,MAAA,CAAA4E,UAAA,CAAA5E,MAAA,CAAAsC,WAAA,CAAA+B,KAAA,EAAqC;;;;;IAmBvC/E,EAAA,CAAAe,SAAA,cAC+C;;;;IAD1Cf,EAAA,CAAA0C,UAAA,QAAAhC,MAAA,CAAA6E,YAAA,CAAAC,IAAA,EAAAxF,EAAA,CAAAgF,aAAA,CAAyB;;;;;IAE9BhF,EAAA,CAAAe,SAAA,YAC0C;;;;IAAxCf,EAAA,CAAA0C,UAAA,YAAA1C,EAAA,CAAA4E,eAAA,IAAAa,GAAA,GAAA/E,MAAA,CAAAgE,QAAA,EAAmC;;;;;;IAJvC1E,EAFJ,CAAAE,cAAA,UAA0B,aACM,aACN;IAGpBF,EAFA,CAAA8B,UAAA,IAAA4D,8DAAA,kBAC+C,IAAAC,4DAAA,gBAET;IACtC3F,EAAA,CAAAE,cAAA,YACkB;IAAAF,EAAA,CAAAgB,MAAA,GAAsB;IAAAhB,EAAA,CAAAiB,YAAA,EAAI;IAC5CjB,EAAA,CAAAE,cAAA,kBACuB;IAD4BF,EAAA,CAAAG,UAAA,mBAAAyF,kFAAA;MAAA5F,EAAA,CAAAK,aAAA,CAAAwF,IAAA;MAAA,MAAAnF,MAAA,GAAAV,EAAA,CAAAQ,aAAA;MAAA,OAAAR,EAAA,CAAAa,WAAA,CAASH,MAAA,CAAAoF,UAAA,CAAApF,MAAA,CAAA6E,YAAA,CAAwB;IAAA,EAAC;IAI3FvF,EAH6B,CAAAiB,YAAA,EAAU,EAC9B,EACF,EACD;;;;IATGjB,EAAA,CAAAkC,SAAA,GAA0C;IAA1ClC,EAAA,CAAA0C,UAAA,WAAAhC,MAAA,CAAA6E,YAAA,CAAAjD,KAAA,kBAAA5B,MAAA,CAAA6E,YAAA,CAAAjD,KAAA,CAAAyD,QAAA,SAA0C;IACC/F,EAAA,CAAAkC,SAAA,EAAyC;IAAzClC,EAAA,CAAA0C,UAAA,SAAAhC,MAAA,CAAA6E,YAAA,CAAAjD,KAAA,kBAAA5B,MAAA,CAAA6E,YAAA,CAAAjD,KAAA,CAAAyD,QAAA,QAAyC;IAEtD/F,EAAA,CAAAkC,SAAA,EAAmE;IAAnElC,EAAA,CAAA0C,UAAA,SAAAhC,MAAA,CAAA6E,YAAA,CAAAC,IAAA,GAAA9E,MAAA,CAAA6E,YAAA,CAAAC,IAAA,GAAA9E,MAAA,CAAA6E,YAAA,CAAAR,KAAA,EAAA/E,EAAA,CAAAgF,aAAA,CAAmE;IAClFhF,EAAA,CAAAkC,SAAA,EAAsB;IAAtBlC,EAAA,CAAAmC,iBAAA,CAAAzB,MAAA,CAAA6E,YAAA,CAAAjD,KAAA,CAAsB;IAEtCtC,EAAA,CAAAkC,SAAA,EAAoB;IAApBlC,EAAA,CAAA0C,UAAA,YAAAhC,MAAA,CAAAgE,QAAA,CAAoB;;;;;;IA1C9B1E,EADF,CAAAE,cAAA,kBAAqD,qBACnC;IAAAF,EAAA,CAAAgB,MAAA,GAA2B;IAAAhB,EAAA,CAAAiB,YAAA,EAAiB;IAQxDjB,EAPJ,CAAAE,cAAA,mBAAc,aAMsC,gBACuC;IAAAF,EAAA,CAAAgB,MAAA,+BAAI;IAAAhB,EAAA,CAAAiB,YAAA,EAAQ;IACnGjB,EAAA,CAAAE,cAAA,eAAqG;IAA1CF,EAAA,CAAA2D,gBAAA,2BAAAqC,iFAAAnC,MAAA;MAAA7D,EAAA,CAAAK,aAAA,CAAA4F,IAAA;MAAA,MAAAvF,MAAA,GAAAV,EAAA,CAAAQ,aAAA;MAAAR,EAAA,CAAA+D,kBAAA,CAAArD,MAAA,CAAAsC,WAAA,CAAAX,aAAA,EAAAwB,MAAA,MAAAnD,MAAA,CAAAsC,WAAA,CAAAX,aAAA,GAAAwB,MAAA;MAAA,OAAA7D,EAAA,CAAAa,WAAA,CAAAgD,MAAA;IAAA,EAAuC;IACpG7D,EADE,CAAAiB,YAAA,EAAqG,EACjG;IACNjB,EAAA,CAAA8B,UAAA,IAAAoE,uDAAA,kBAAoE;IAYhElG,EAFJ,CAAAE,cAAA,cAAoD,eAChB,iBAEpB;IAAAF,EAAA,CAAAgB,MAAA,oBAAE;IAChBhB,EADgB,CAAAiB,YAAA,EAAQ,EAClB;IACNjB,EAAA,CAAAE,cAAA,oBACmB;IADgDF,EAAA,CAAAG,UAAA,oBAAAgG,2EAAAtC,MAAA;MAAA7D,EAAA,CAAAK,aAAA,CAAA4F,IAAA;MAAA,MAAAvF,MAAA,GAAAV,EAAA,CAAAQ,aAAA;MAAA,OAAAR,EAAA,CAAAa,WAAA,CAAUH,MAAA,CAAA0F,YAAA,CAAAvC,MAAA,CAAoB;IAAA,EAAC;IAAlG7D,EAAA,CAAAiB,YAAA,EACmB;IACnBjB,EAAA,CAAAE,cAAA,kBAAuE;IAAhCF,EAAA,CAAAG,UAAA,mBAAAkG,2EAAA;MAAArG,EAAA,CAAAK,aAAA,CAAA4F,IAAA;MAAA,MAAAK,iBAAA,GAAAtG,EAAA,CAAAY,WAAA;MAAA,OAAAZ,EAAA,CAAAa,WAAA,CAASyF,iBAAA,CAAAlC,KAAA,EAAqB;IAAA,EAAC;IACpEpE,EAAA,CAAAe,SAAA,aAAgC;IAAAf,EAAA,CAAAgB,MAAA,oBAAE;IACtChB,EADsC,CAAAiB,YAAA,EAAS,EACzC;IAENjB,EAAA,CAAA8B,UAAA,KAAAyE,wDAAA,kBAA0B;IAexBvG,EADF,CAAAE,cAAA,cAAkD,iBACiC;IAAAF,EAAA,CAAAgB,MAAA,oBAAE;IAAAhB,EAAA,CAAAiB,YAAA,EAAQ;IAC3FjB,EAAA,CAAAE,cAAA,qBAA2E;IAAlCF,EAAA,CAAA2D,gBAAA,2BAAA6C,sFAAA3C,MAAA;MAAA7D,EAAA,CAAAK,aAAA,CAAA4F,IAAA;MAAA,MAAAvF,MAAA,GAAAV,EAAA,CAAAQ,aAAA;MAAAR,EAAA,CAAA+D,kBAAA,CAAArD,MAAA,CAAAsC,WAAA,CAAAyD,OAAA,EAAA5C,MAAA,MAAAnD,MAAA,CAAAsC,WAAA,CAAAyD,OAAA,GAAA5C,MAAA;MAAA,OAAA7D,EAAA,CAAAa,WAAA,CAAAgD,MAAA;IAAA,EAAiC;IACxE7D,EAAA,CAAAE,cAAA,qBAAuB;IAAAF,EAAA,CAAAgB,MAAA,oBAAE;IAAAhB,EAAA,CAAAiB,YAAA,EAAY;IACrCjB,EAAA,CAAAE,cAAA,qBAAuB;IAAAF,EAAA,CAAAgB,MAAA,oBAAE;IAG7BhB,EAH6B,CAAAiB,YAAA,EAAY,EAC3B,EAER;IAGJjB,EADF,CAAAE,cAAA,cAAkD,iBACiC;IAAAF,EAAA,CAAAgB,MAAA,gCAAI;IAAAhB,EAAA,CAAAiB,YAAA,EAAQ;IAC7FjB,EAAA,CAAAE,cAAA,oBACyC;IADvBF,EAAA,CAAA2D,gBAAA,2BAAA+C,qFAAA7C,MAAA;MAAA7D,EAAA,CAAAK,aAAA,CAAA4F,IAAA;MAAA,MAAAvF,MAAA,GAAAV,EAAA,CAAAQ,aAAA;MAAAR,EAAA,CAAA+D,kBAAA,CAAArD,MAAA,CAAAsC,WAAA,CAAA2D,aAAA,EAAA9C,MAAA,MAAAnD,MAAA,CAAAsC,WAAA,CAAA2D,aAAA,GAAA9C,MAAA;MAAA,OAAA7D,EAAA,CAAAa,WAAA,CAAAgD,MAAA;IAAA,EAAuC;IAI7D7D,EAH6C,CAAAiB,YAAA,EAAW,EAEhD,EACO;IAEbjB,EADF,CAAAE,cAAA,0BAAsD,kBACM;IAAtBF,EAAA,CAAAG,UAAA,mBAAAyG,2EAAA;MAAA,MAAAC,OAAA,GAAA7G,EAAA,CAAAK,aAAA,CAAA4F,IAAA,EAAAnD,SAAA;MAAA,OAAA9C,EAAA,CAAAa,WAAA,CAASgG,OAAA,CAAA9D,KAAA,EAAW;IAAA,EAAC;IAAC/C,EAAA,CAAAgB,MAAA,oBAAE;IAAAhB,EAAA,CAAAiB,YAAA,EAAS;IACrEjB,EAAA,CAAAE,cAAA,kBAAsD;IAAtBF,EAAA,CAAAG,UAAA,mBAAA2G,2EAAA;MAAA,MAAAD,OAAA,GAAA7G,EAAA,CAAAK,aAAA,CAAA4F,IAAA,EAAAnD,SAAA;MAAA,MAAApC,MAAA,GAAAV,EAAA,CAAAQ,aAAA;MAAA,OAAAR,EAAA,CAAAa,WAAA,CAASH,MAAA,CAAAqG,MAAA,CAAAF,OAAA,CAAW;IAAA,EAAC;IAAC7G,EAAA,CAAAgB,MAAA,oBAAE;IAE5DhB,EAF4D,CAAAiB,YAAA,EAAS,EAClD,EACT;;;;IAlEQjB,EAAA,CAAAkC,SAAA,GAA2B;IAA3BlC,EAAA,CAAAmC,iBAAA,CAAAzB,MAAA,CAAAgE,QAAA,mCAA2B;IASoB1E,EAAA,CAAAkC,SAAA,GAAuC;IAAvClC,EAAA,CAAA2E,gBAAA,YAAAjE,MAAA,CAAAsC,WAAA,CAAAX,aAAA,CAAuC;IAEjDrC,EAAA,CAAAkC,SAAA,EAAe;IAAflC,EAAA,CAAA0C,UAAA,UAAAhC,MAAA,CAAAgE,QAAA,CAAe;IAY/B1E,EAAA,CAAAkC,SAAA,GAAwC;IAAxClC,EAAA,CAAA0C,UAAA,YAAA1C,EAAA,CAAA4E,eAAA,KAAAC,GAAA,EAAAnE,MAAA,CAAAgE,QAAA,EAAwC;IASrE1E,EAAA,CAAAkC,SAAA,GAAkB;IAAlBlC,EAAA,CAAA0C,UAAA,SAAAhC,MAAA,CAAA6E,YAAA,CAAkB;IAgBmBvF,EAAA,CAAAkC,SAAA,GAAiC;IAAjClC,EAAA,CAAA2E,gBAAA,YAAAjE,MAAA,CAAAsC,WAAA,CAAAyD,OAAA,CAAiC;IAC7DzG,EAAA,CAAAkC,SAAA,EAAW;IAAXlC,EAAA,CAAA0C,UAAA,YAAW;IACX1C,EAAA,CAAAkC,SAAA,GAAW;IAAXlC,EAAA,CAAA0C,UAAA,YAAW;IAON1C,EAAA,CAAAkC,SAAA,GAAuC;IAAvClC,EAAA,CAAA2E,gBAAA,YAAAjE,MAAA,CAAAsC,WAAA,CAAA2D,aAAA,CAAuC;IAAC3G,EAAA,CAAA0C,UAAA,WAAU;;;ADxK5E,OAAM,MAAOsE,yBAA0B,SAAQnH,aAAa;EAgB1DoH,YACqBC,KAAkB,EAC7BC,aAA8B,EAC9BC,KAAuB,EACvBC,OAAuB,EACvBC,oBAA0C,EAC1CC,cAA8B,EAC9BC,eAA+B;IAEvC,KAAK,CAACN,KAAK,CAAC;IARO,KAAAA,KAAK,GAALA,KAAK;IAChB,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAC,oBAAoB,GAApBA,oBAAoB;IACpB,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,eAAe,GAAfA,eAAe;IAtBzB,KAAAC,kBAAkB,GAAW,EAAE;IAC/B,KAAAC,cAAc,GAAW,EAAE;IAC3B,KAAAC,WAAW,GAAW,CAAC;IAEvB,KAAAC,iBAAiB,GAAuB,EAAE;IAE1C,KAAAlD,QAAQ,GAAG,KAAK;IAEhB,KAAAa,YAAY,GAAQsC,SAAS;IAE7B,KAAAnE,SAAS,GAAU,EAAE;IAErB,KAAAoE,OAAO,GAAGvI,MAAM,CAACD,UAAU,CAAC;IAE5B,KAAAyI,WAAW,GAAW,CAAC,CAAC;EAWxB;EAESC,QAAQA,CAAA;IACf,IAAI,CAACL,WAAW,GAAG,CAAC;IACpB,IAAI,CAACA,WAAW,GAAGM,QAAQ,CAAC,IAAI,CAACV,cAAc,CAACW,QAAQ,CAACC,QAAS,CAACC,GAAG,CAAC,IAAI,CAAE,CAAC;IAC9E,IAAI,CAACC,oBAAoB,CAAC,CAAC,CAAC;EAC9B;EAEAA,oBAAoBA,CAACC,IAAY;IAC/B,IAAI,CAAChB,oBAAoB,CACtBiB,6CAA6C,CAAC;MAC7CC,IAAI,EAAE;QACJC,YAAY,EAAE,IAAI,CAACd,WAAW;QAC9BrF,KAAK,EAAE,IAAI,CAACoF,cAAc,GAAG,IAAI,CAACA,cAAc,GAAGG,SAAS;QAC5DxF,aAAa,EAAE,IAAI,CAACoF,kBAAkB,GAClC,IAAI,CAACA,kBAAkB,GACvBI,SAAS;QACba,SAAS,EAAEJ,IAAI;QACfK,QAAQ,EAAE;;KAEb,CAAC,CACDC,IAAI,CAACnJ,kBAAkB,CAAC,IAAI,CAACqI,OAAO,CAAC,CAAC,CACtCe,SAAS,CAAEC,GAAG,IAAI;MACjB,IAAI,CAAClB,iBAAiB,GAAGkB,GAAG,CAACC,OAAO,IAAI,EAAE;MAC1C,IAAI,CAACC,YAAY,GAAGF,GAAG,CAACG,UAAW;IACrC,CAAC,CAAC;EACN;EACAC,QAAQA,CAACC,MAAW;IAClB,IAAI,CAACzE,QAAQ,GAAG,IAAI;IACpB,IAAI,CAACa,YAAY,GAAGsC,SAAS;IAC7B,IAAI,CAAC7E,WAAW,GAAG;MACjByD,OAAO,EAAE;KACV;IACD,IAAI,CAACU,aAAa,CAACiC,IAAI,CAACD,MAAM,CAAC;EACjC;EACAE,kBAAkBA,CAACF,MAAW;IAC5B,IAAI,CAACzE,QAAQ,GAAG,IAAI;IACpB,IAAI,CAACa,YAAY,GAAGsC,SAAS;IAC7B,IAAI,CAAC7E,WAAW,GAAG;MACjBX,aAAa,EAAE;KAChB;IACD,IAAI,CAAC8E,aAAa,CAACiC,IAAI,CAACD,MAAM,CAAC;EACjC;EAEArI,MAAMA,CAACwI,GAAQ,EAAEC,IAAU;IACzB,IAAI,CAAC7E,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACa,YAAY,GAAGsC,SAAS;IAC7B,IAAI,CAAC7E,WAAW,GAAG,EAAE;IACrB,IAAI,CAACsE,oBAAoB,CACtBkC,6CAA6C,CAAC;MAC7ChB,IAAI,EAAE;QACJiB,gBAAgB,EAAEF,IAAI,CAACnH;;KAE1B,CAAC,CACDwG,IAAI,CAACnJ,kBAAkB,CAAC,IAAI,CAACqI,OAAO,CAAC,CAAC,CACtCe,SAAS,CAAEC,GAAG,IAAI;MACjB,IAAI,CAAC9F,WAAW,GAAG8F,GAAG,CAACC,OAAQ;IACjC,CAAC,CAAC;IAEJ,IAAI,CAAC5B,aAAa,CAACiC,IAAI,CAACE,GAAG,CAAC;EAC9B;EAEAhI,QAAQA,CAACiI,IAAsB,EAAElI,KAAa;IAC5C,IAAIqI,MAAM,CAACC,OAAO,CAAC,WAAWJ,IAAI,CAACnH,GAAG,IAAI,CAAC,EAAE;MAC3C,IAAI,CAACkF,oBAAoB,CACtBsC,4CAA4C,CAAC;QAC5CpB,IAAI,EAAE;UACJiB,gBAAgB,EAAEF,IAAI,CAACnH;;OAE1B,CAAC,CACDwG,IAAI,CAACnJ,kBAAkB,CAAC,IAAI,CAACqI,OAAO,CAAC,CAAC,CACtCe,SAAS,CAAEC,GAAG,IAAI;QACjB,IAAIA,GAAG,CAACe,UAAU,KAAK,CAAC,EAAE;UACxB,IAAI,CAACxC,OAAO,CAACyC,aAAa,CAAC,MAAM,CAAC;UAClC,IAAI,CAACzB,oBAAoB,CAAC,IAAI,CAACN,WAAW,CAAC;QAC7C;MACF,CAAC,CAAC;IACN;EACF;EAEAnG,UAAUA,CAAC0H,GAAQ,EAAEC,IAAsB;IACzC,IAAI,CAACpC,aAAa,CAACiC,IAAI,CAACE,GAAG,CAAC;IAC5B,IAAI,CAACtG,WAAW,GAAGuG,IAAI;EACzB;EAEAQ,YAAYA,CAACC,MAAc,EAAEC,WAAA,GAAsB,EAAE;IACnD,MAAMC,UAAU,GAAGF,MAAM,CAACG,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAIH,MAAM;IACjD;IACA,MAAMI,cAAc,GAAGC,IAAI,CAACH,UAAU,CAAC;IACvC;IACA,MAAMI,UAAU,GAAiB,EAAE;IACnC,KAAK,IAAIC,MAAM,GAAG,CAAC,EAAEA,MAAM,GAAGH,cAAc,CAACtF,MAAM,EAAEyF,MAAM,IAAI,GAAG,EAAE;MAClE,MAAMC,KAAK,GAAGJ,cAAc,CAACI,KAAK,CAACD,MAAM,EAAEA,MAAM,GAAG,GAAG,CAAC;MACxD,MAAME,WAAW,GAAG,IAAIC,KAAK,CAACF,KAAK,CAAC1F,MAAM,CAAC;MAC3C,KAAK,IAAI6F,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,KAAK,CAAC1F,MAAM,EAAE6F,CAAC,EAAE,EAAE;QACrCF,WAAW,CAACE,CAAC,CAAC,GAAGH,KAAK,CAACI,UAAU,CAACD,CAAC,CAAC;MACtC;MACA,MAAME,SAAS,GAAG,IAAIC,UAAU,CAACL,WAAW,CAAC;MAC7CH,UAAU,CAACS,IAAI,CAACF,SAAS,CAAC;IAC5B;IACA,OAAO,IAAIG,IAAI,CAACV,UAAU,EAAE;MAAEW,IAAI,EAAEhB;IAAW,CAAE,CAAC;EACpD;EAEAlD,MAAMA,CAACuC,GAAQ;IACb,IAAI,CAAC4B,UAAU,EAAE;IACjB,IAAI,IAAI,CAAC9D,KAAK,CAAC+D,aAAa,CAACrG,MAAM,GAAG,CAAC,EAAE;MACvC,IAAI,CAACuC,OAAO,CAAC+D,aAAa,CAAC,IAAI,CAAChE,KAAK,CAAC+D,aAAa,CAAC;MACpD;IACF;IAEA,IAAIE,OAAO,GAAsD;MAC/D7C,IAAI,EAAE;QACJC,YAAY,EAAE,IAAI,CAAC/D,QAAQ,GAAG,IAAI,CAACiD,WAAW,GAAGE,SAAS;QAC1D4B,gBAAgB,EAAE,IAAI,CAAC/E,QAAQ,GAAGmD,SAAS,GAAG,IAAI,CAAC7E,WAAW,CAACZ,GAAG;QAClEqE,OAAO,EAAE,IAAI,CAACzD,WAAW,CAACyD,OAAO;QACjCE,aAAa,EAAE,IAAI,CAAC3D,WAAW,CAAC2D,aAAa,GAAG,IAAI,CAAC3D,WAAW,CAAC2D,aAAa,GAAGkB,SAAS;QAC1F9C,KAAK,EAAE,IAAI,CAACQ,YAAY,CAAC+F,WAAW;QACpCjJ,aAAa,EAAE,IAAI,CAACW,WAAW,CAACX,aAAa,GAAG,IAAI,CAACW,WAAW,CAACX,aAAa,GAAGwF;;KAEpF;IACD,IAAI,CAACP,oBAAoB,CACtBiE,0CAA0C,CAACF,OAAO,CAAC,CACnDxC,SAAS,CAAEC,GAAG,IAAI;MACjB,IAAIA,GAAG,CAACe,UAAU,KAAK,CAAC,EAAE;QACxB,IAAI,CAACxC,OAAO,CAACyC,aAAa,CAAC,MAAM,CAAC;MACpC,CAAC,MAAM;QACL,IAAI,CAACzC,OAAO,CAACmE,YAAY,CAAC1C,GAAG,CAAC2C,OAAQ,CAAC;MACzC;MACAnC,GAAG,CAACvG,KAAK,EAAE;MACX,IAAI,CAACsF,oBAAoB,CAAC,CAAC,CAAC;IAC9B,CAAC,CAAC;EACN;EAEA6C,UAAUA,CAAA;IACR,IAAI,CAAC9D,KAAK,CAACsE,KAAK,EAAE;IAClB,IAAI,CAACtE,KAAK,CAACuE,QAAQ,CAAC,MAAM,EAAE,IAAI,CAAC3I,WAAW,CAAC2D,aAAa,CAAC;IAC3D,IAAI,CAACS,KAAK,CAACuE,QAAQ,CAAC,MAAM,EAAE,IAAI,CAAC3I,WAAW,CAACX,aAAa,CAAC;IAC3D,IAAI,IAAI,CAACqC,QAAQ,IAAI,CAAC,IAAI,CAACa,YAAY,EAAE;MACvC,IAAI,CAAC6B,KAAK,CAACwE,eAAe,CAAC,OAAO,CAAC;IACrC;EACF;EAEAxF,YAAYA,CAACyF,KAAU;IACrB,IAAIC,KAAK,GAAGD,KAAK,CAACE,MAAM,CAACD,KAAK;IAC9B,MAAME,SAAS,GAAG,mBAAmB;IAErC,IAAI,CAACA,SAAS,CAACC,IAAI,CAACH,KAAK,CAAC,CAAC,CAAC,CAACb,IAAI,CAAC,EAAE;MAClC,IAAI,CAAC5D,OAAO,CAACmE,YAAY,CAAC,kBAAkB,CAAC;MAC7C;IACF;IAEA,IAAIU,MAAM,GAAG,IAAIC,UAAU,EAAE;IAC7BD,MAAM,CAACE,aAAa,CAACN,KAAK,CAAC,CAAC,CAAC,CAAC;IAC9BI,MAAM,CAACG,MAAM,GAAIC,CAAC,IAAI;MACpB,IAAIC,IAAI,GAAW,EAAE;MACrB,IAAI,CAACT,KAAK,CAAC,CAAC,CAAC,CAACtI,IAAI,CAACuC,QAAQ,CAAC,KAAK,CAAC,EAAE;QAClCwG,IAAI,GAAGC,GAAG,CAACC,eAAe,CAACX,KAAK,CAAC,CAAC,CAAC,CAAC;MACtC;MACA,IAAI,CAACvG,YAAY,GAAG;QAClBjD,KAAK,EAAEwJ,KAAK,CAAC,CAAC,CAAC,CAACtI,IAAI;QACpBuB,KAAK,EAAEuH,CAAC,CAACP,MAAM,EAAEW,MAAM,EAAEC,QAAQ,EAAE,CAACxC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QACjD3E,IAAI,EAAEsG,KAAK,CAAC,CAAC,CAAC,CAACtI,IAAI,CAACuC,QAAQ,CAAC,KAAK,CAAC,GAAG+F,KAAK,CAAC,CAAC,CAAC,GAAGS,IAAI;QACrDjB,WAAW,EAAEQ,KAAK,CAAC,CAAC,CAAC;QACrBc,SAAS,EAAElN,YAAY,CAACmN;OACzB;MACDhB,KAAK,CAACE,MAAM,CAACe,KAAK,GAAG,IAAI;IAC3B,CAAC;EACH;EAEA7I,cAAcA,CAAC4H,KAAU;IACvB,KAAK,IAAIxK,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGwK,KAAK,CAACE,MAAM,CAACD,KAAK,CAAChH,MAAM,EAAEzD,KAAK,EAAE,EAAE;MAC9D,MAAMkL,IAAI,GAAGV,KAAK,CAACE,MAAM,CAACD,KAAK,CAACzK,KAAK,CAAC;MACtC,IAAIkL,IAAI,EAAE;QACR,IAAIL,MAAM,GAAG,IAAIC,UAAU,EAAE;QAC7BD,MAAM,CAACE,aAAa,CAACG,IAAI,CAAC;QAC1BL,MAAM,CAACG,MAAM,GAAG,MAAK;UACnB,IAAIU,SAAS,GAAWb,MAAM,CAACQ,MAAgB;UAC/C,IAAI,CAACK,SAAS,EAAE;YACd;UACF;UACAR,IAAI,CAACjJ,EAAE,GAAG,IAAI0J,IAAI,EAAE,CAACC,OAAO,EAAE;UAC9B,IAAI,CAACvJ,SAAS,CAACqH,IAAI,CAAC;YAClBzH,EAAE,EAAE,IAAI0J,IAAI,EAAE,CAACC,OAAO,EAAE;YACxBzJ,IAAI,EAAE+I,IAAI,CAAC/I,IAAI;YACf0J,IAAI,EAAEH,SAAS;YACfI,SAAS,EAAE,IAAI,CAAC3F,eAAe,CAAC4F,gBAAgB,CAACb,IAAI,CAAC/I,IAAI,CAAC;YAC3DuB,KAAK,EAAEwH;WACR,CAAC;UACFV,KAAK,CAACE,MAAM,CAACe,KAAK,GAAG,IAAI;QAC3B,CAAC;MACH;IACF;EACF;EAEAhH,UAAUA,CAACyD,IAAS;IAClB,IAAIG,MAAM,CAACC,OAAO,CAAC,SAASJ,IAAI,CAACjH,KAAK,IAAI,CAAC,EAAE;MAC3C,IAAI,CAACiD,YAAY,GAAGsC,SAAS;IAC/B;EACF;EAEAxE,UAAUA,CAACC,EAAU;IACnB,IAAI,CAACI,SAAS,GAAG,IAAI,CAACA,SAAS,CAAC2J,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAChK,EAAE,IAAIA,EAAE,CAAC;EACzD;EAEAiK,kBAAkBA,CAAA;IAChB,IAAI,CAACnG,KAAK,CAACsE,KAAK,EAAE;IAClB,IAAI,CAACtE,KAAK,CAACuE,QAAQ,CAAC,MAAM,EAAE,IAAI,CAAC3I,WAAW,CAACX,aAAa,CAAC;IAC3D,IAAI,IAAI,CAACqC,QAAQ,IAAI,IAAI,CAAChB,SAAS,CAACoB,MAAO,IAAI,CAAC,EAAE;MAChD,IAAI,CAACsC,KAAK,CAACwE,eAAe,CAAC,OAAO,CAAC;IACrC;EACF;EAEAnH,cAAcA,CAAC6E,GAAQ;IACrB,IAAI,CAACiE,kBAAkB,EAAE;IACzB,IAAI,IAAI,CAACnG,KAAK,CAAC+D,aAAa,CAACrG,MAAM,GAAG,CAAC,EAAE;MACvC,IAAI,CAACuC,OAAO,CAAC+D,aAAa,CAAC,IAAI,CAAChE,KAAK,CAAC+D,aAAa,CAAC;MACpD;IACF;IAEA,IAAIqC,eAAe,GAAG,IAAI,CAAC9J,SAAS,CAAC+J,GAAG,CAACH,CAAC,IAAIA,CAAC,CAACvI,KAAK,CAAC;IAEtD,IAAI,CAACuC,oBAAoB,CAACoG,kDAAkD,CAAC;MAC3ElF,IAAI,EAAE;QACJC,YAAY,EAAE,IAAI,CAACd,WAAW;QAC9BtF,aAAa,EAAE,IAAI,CAACW,WAAW,CAACX,aAAc;QAC9CsL,MAAM,EAAEH;;KAEX,CAAC,CAAC5E,IAAI,CACL7I,GAAG,CAAC+I,GAAG,IAAG;MACR,IAAIA,GAAG,CAACe,UAAU,KAAK,CAAC,EAAE;QACxB,IAAI,CAACxC,OAAO,CAACyC,aAAa,CAAC,MAAM,CAAC;QAClCR,GAAG,CAACvG,KAAK,EAAE;QACX,IAAI,CAACsF,oBAAoB,CAAC,CAAC,CAAC;QAC5B,IAAI,CAAC3E,SAAS,GAAG,EAAE;QACnB,IAAI,CAACV,WAAW,GAAG,EAAE;MACvB,CAAC,MAAM;QACL,IAAI,CAACqE,OAAO,CAACmE,YAAY,CAAC1C,GAAG,CAAC2C,OAAQ,CAAC;MACzC;IACF,CAAC,CAAC,CACH,CAAC5C,SAAS,EAAE;EACf;EAEA+E,iBAAiBA,CAACtF,IAAY;IAC5B,IAAI,CAACP,WAAW,GAAGO,IAAI;IACvB,IAAI,CAACD,oBAAoB,CAACC,IAAI,CAAC;EACjC;EAEAhD,UAAUA,CAACuI,QAAgB;IACzB,OAAO,IAAI,CAACrG,eAAe,CAAC4F,gBAAgB,CAACS,QAAQ,CAAC,CAAC9H,QAAQ,CAAC,KAAK,CAAC,IAAI,IAAI,CAACyB,eAAe,CAAC4F,gBAAgB,CAACS,QAAQ,CAAC,CAAC9H,QAAQ,CAAC,MAAM,CAAC,IAAI,IAAI,CAACyB,eAAe,CAAC4F,gBAAgB,CAACS,QAAQ,CAAC,CAAC9H,QAAQ,CAAC,KAAK,CAAC;EAC/M;EAEAZ,WAAWA,CAAC0I,QAAgB;IAC1BC,OAAO,CAACC,GAAG,CAACF,QAAQ,CAAC;IAErBnE,MAAM,CAACN,IAAI,CAAC,GAAGyE,QAAQ,EAAE,EAAE,QAAQ,CAAC;EACtC;;;uCAzRW7G,yBAAyB,EAAAhH,EAAA,CAAAgO,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAlO,EAAA,CAAAgO,iBAAA,CAAAG,EAAA,CAAAC,eAAA,GAAApO,EAAA,CAAAgO,iBAAA,CAAAK,EAAA,CAAAC,gBAAA,GAAAtO,EAAA,CAAAgO,iBAAA,CAAAO,EAAA,CAAAC,cAAA,GAAAxO,EAAA,CAAAgO,iBAAA,CAAAS,EAAA,CAAAC,oBAAA,GAAA1O,EAAA,CAAAgO,iBAAA,CAAAW,EAAA,CAAAC,cAAA,GAAA5O,EAAA,CAAAgO,iBAAA,CAAAa,EAAA,CAAAC,cAAA;IAAA;EAAA;;;YAAzB9H,yBAAyB;MAAA+H,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAAjP,EAAA,CAAAkP,0BAAA,EAAAlP,EAAA,CAAAmP,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,mCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;UC1BpCzP,EADF,CAAAE,cAAA,iBAA0B,qBACR;UAEdF,EAAA,CAAAgB,MAAA,iCACF;UAAAhB,EAAA,CAAAiB,YAAA,EAAiB;UAKTjB,EAJR,CAAAE,cAAA,mBAAc,aACK,aACgB,aACqB,eACX;UAAAF,EAAA,CAAAgB,MAAA,+BAAI;UAAAhB,EAAA,CAAAiB,YAAA,EAAQ;UACjDjB,EAAA,CAAAE,cAAA,eAA8F;UAAnCF,EAAA,CAAA2D,gBAAA,2BAAAgM,kEAAA9L,MAAA;YAAA7D,EAAA,CAAAK,aAAA,CAAAuP,GAAA;YAAA5P,EAAA,CAAA+D,kBAAA,CAAA2L,GAAA,CAAAjI,kBAAA,EAAA5D,MAAA,MAAA6L,GAAA,CAAAjI,kBAAA,GAAA5D,MAAA;YAAA,OAAA7D,EAAA,CAAAa,WAAA,CAAAgD,MAAA;UAAA,EAAgC;UAE/F7D,EAFI,CAAAiB,YAAA,EAA8F,EAC1F,EACF;UAGFjB,EAFJ,CAAAE,cAAA,cAA+B,cACqB,gBACX;UAAAF,EAAA,CAAAgB,MAAA,oBAAE;UAAAhB,EAAA,CAAAiB,YAAA,EAAQ;UAC/CjB,EAAA,CAAAE,cAAA,iBAAwF;UAA/BF,EAAA,CAAA2D,gBAAA,2BAAAkM,mEAAAhM,MAAA;YAAA7D,EAAA,CAAAK,aAAA,CAAAuP,GAAA;YAAA5P,EAAA,CAAA+D,kBAAA,CAAA2L,GAAA,CAAAhI,cAAA,EAAA7D,MAAA,MAAA6L,GAAA,CAAAhI,cAAA,GAAA7D,MAAA;YAAA,OAAA7D,EAAA,CAAAa,WAAA,CAAAgD,MAAA;UAAA,EAA4B;UAG3F7D,EAHM,CAAAiB,YAAA,EAAwF,EACpF,EACF,EACF;UAEJjB,EADF,CAAAE,cAAA,eAAsC,kBACgC;UAAvCF,EAAA,CAAAG,UAAA,mBAAA2P,4DAAA;YAAA9P,EAAA,CAAAK,aAAA,CAAAuP,GAAA;YAAA,MAAAG,WAAA,GAAA/P,EAAA,CAAAY,WAAA;YAAA,OAAAZ,EAAA,CAAAa,WAAA,CAAS6O,GAAA,CAAArG,kBAAA,CAAA0G,WAAA,CAA4B;UAAA,EAAC;UAAC/P,EAAA,CAAAgB,MAAA,gCAAI;UAAAhB,EAAA,CAAAiB,YAAA,EAAS;UACjFjB,EAAA,CAAAE,cAAA,kBAA+D;UAAlCF,EAAA,CAAAG,UAAA,mBAAA6P,4DAAA;YAAAhQ,EAAA,CAAAK,aAAA,CAAAuP,GAAA;YAAA,OAAA5P,EAAA,CAAAa,WAAA,CAAS6O,GAAA,CAAArH,oBAAA,CAAqB,CAAC,CAAC;UAAA,EAAC;UAACrI,EAAA,CAAAgB,MAAA,oBAAE;UAAAhB,EAAA,CAAAiB,YAAA,EAAS;UAC1EjB,EAAA,CAAAE,cAAA,kBAA2D;UAA3BF,EAAA,CAAAG,UAAA,mBAAA8P,4DAAA;YAAAjQ,EAAA,CAAAK,aAAA,CAAAuP,GAAA;YAAA,MAAAjP,SAAA,GAAAX,EAAA,CAAAY,WAAA;YAAA,OAAAZ,EAAA,CAAAa,WAAA,CAAS6O,GAAA,CAAAxG,QAAA,CAAAvI,SAAA,CAAgB;UAAA,EAAC;UAACX,EAAA,CAAAgB,MAAA,oBAAE;UAC/DhB,EAD+D,CAAAiB,YAAA,EAAS,EAClE;UAKEjB,EAJR,CAAAE,cAAA,eAAmC,iBAC8D,aACtF,cAC+C,cACxB;UAAAF,EAAA,CAAAgB,MAAA,0BAAG;UAAAhB,EAAA,CAAAiB,YAAA,EAAK;UAClCjB,EAAA,CAAAE,cAAA,cAA2B;UAAAF,EAAA,CAAAgB,MAAA,gCAAI;UAAAhB,EAAA,CAAAiB,YAAA,EAAK;UACpCjB,EAAA,CAAAE,cAAA,cAA2B;UAAAF,EAAA,CAAAgB,MAAA,oBAAE;UAAAhB,EAAA,CAAAiB,YAAA,EAAK;UAClCjB,EAAA,CAAAE,cAAA,cAA2B;UAAAF,EAAA,CAAAgB,MAAA,iCAAK;UAAAhB,EAAA,CAAAiB,YAAA,EAAK;UACrCjB,EAAA,CAAAE,cAAA,cAA2B;UAAAF,EAAA,CAAAgB,MAAA,iCAAK;UAAAhB,EAAA,CAAAiB,YAAA,EAAK;UACrCjB,EAAA,CAAAE,cAAA,cAA0B;UAAAF,EAAA,CAAAgB,MAAA,oBAAE;UAEhChB,EAFgC,CAAAiB,YAAA,EAAK,EAC9B,EACC;UACRjB,EAAA,CAAAE,cAAA,aAAO;UACLF,EAAA,CAAA8B,UAAA,KAAAoO,wCAAA,mBAA0D;UAqClElQ,EAHM,CAAAiB,YAAA,EAAQ,EACF,EACJ,EACO;UAEbjB,EADF,CAAAE,cAAA,sBAAgB,0BAE6B;UAD4BF,EAAvD,CAAA2D,gBAAA,kCAAAwM,mFAAAtM,MAAA;YAAA7D,EAAA,CAAAK,aAAA,CAAAuP,GAAA;YAAA5P,EAAA,CAAA+D,kBAAA,CAAA2L,GAAA,CAAA1G,YAAA,EAAAnF,MAAA,MAAA6L,GAAA,CAAA1G,YAAA,GAAAnF,MAAA;YAAA,OAAA7D,EAAA,CAAAa,WAAA,CAAAgD,MAAA;UAAA,EAAiC,wBAAAuM,yEAAAvM,MAAA;YAAA7D,EAAA,CAAAK,aAAA,CAAAuP,GAAA;YAAA5P,EAAA,CAAA+D,kBAAA,CAAA2L,GAAA,CAAAW,SAAA,EAAAxM,MAAA,MAAA6L,GAAA,CAAAW,SAAA,GAAAxM,MAAA;YAAA,OAAA7D,EAAA,CAAAa,WAAA,CAAAgD,MAAA;UAAA,EAAqB,4BAAAyM,6EAAAzM,MAAA;YAAA7D,EAAA,CAAAK,aAAA,CAAAuP,GAAA;YAAA5P,EAAA,CAAA+D,kBAAA,CAAA2L,GAAA,CAAAa,QAAA,EAAA1M,MAAA,MAAA6L,GAAA,CAAAa,QAAA,GAAA1M,MAAA;YAAA,OAAA7D,EAAA,CAAAa,WAAA,CAAAgD,MAAA;UAAA,EAAwB;UAC5F7D,EAAA,CAAAG,UAAA,wBAAAiQ,yEAAAvM,MAAA;YAAA7D,EAAA,CAAAK,aAAA,CAAAuP,GAAA;YAAA,OAAA5P,EAAA,CAAAa,WAAA,CAAc6O,GAAA,CAAA9B,iBAAA,CAAA/J,MAAA,CAAyB;UAAA,EAAC;UAE9C7D,EAF+C,CAAAiB,YAAA,EAAiB,EAC7C,EACT;UAwDVjB,EAtDA,CAAA8B,UAAA,KAAA0O,iDAAA,gCAAAxQ,EAAA,CAAAyQ,sBAAA,CAA+C,KAAAC,iDAAA,iCAAA1Q,EAAA,CAAAyQ,sBAAA,CAeJ,KAAAE,iDAAA,kCAAA3Q,EAAA,CAAAyQ,sBAAA,CAuCO;;;UA9HmBzQ,EAAA,CAAAkC,SAAA,GAAgC;UAAhClC,EAAA,CAAA2E,gBAAA,YAAA+K,GAAA,CAAAjI,kBAAA,CAAgC;UAMlCzH,EAAA,CAAAkC,SAAA,GAA4B;UAA5BlC,EAAA,CAAA2E,gBAAA,YAAA+K,GAAA,CAAAhI,cAAA,CAA4B;UAsBhE1H,EAAA,CAAAkC,SAAA,IAAsB;UAAtBlC,EAAA,CAAA0C,UAAA,YAAAgN,GAAA,CAAA9H,iBAAA,CAAsB;UAuCjC5H,EAAA,CAAAkC,SAAA,GAAiC;UAAsBlC,EAAvD,CAAA2E,gBAAA,mBAAA+K,GAAA,CAAA1G,YAAA,CAAiC,SAAA0G,GAAA,CAAAW,SAAA,CAAqB,aAAAX,GAAA,CAAAa,QAAA,CAAwB;;;qBDtDtF/Q,YAAY,EAAAoR,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,OAAA,EAAAF,EAAA,CAAAG,IAAA,EAAAH,EAAA,CAAAI,QAAA,EAAAJ,EAAA,CAAAK,YAAA,EAAErR,YAAY,EAAAsR,EAAA,CAAAC,oBAAA,EAAAD,EAAA,CAAAE,eAAA,EAAAF,EAAA,CAAAG,OAAA,EAAAlD,EAAA,CAAAmD,eAAA,EAAAnD,EAAA,CAAAoD,mBAAA,EAAApD,EAAA,CAAAqD,qBAAA,EAAArD,EAAA,CAAAsD,qBAAA,EAAAtD,EAAA,CAAAuD,gBAAA,EAAAvD,EAAA,CAAAwD,iBAAA,EAAAxD,EAAA,CAAAyD,iBAAA,EAAAzD,EAAA,CAAA0D,eAAA,EAAAC,GAAA,CAAAC,mBAAA,EAAAC,GAAA,CAAAC,kBAAA,EAAEtS,kBAAkB,EAAEG,kBAAkB;MAAAoS,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}