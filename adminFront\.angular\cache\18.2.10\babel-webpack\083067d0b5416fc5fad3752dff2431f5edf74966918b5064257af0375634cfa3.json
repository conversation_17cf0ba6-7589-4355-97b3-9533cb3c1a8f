{"ast": null, "code": "import { BrowserModule } from '@angular/platform-browser';\nimport { BrowserAnimationsModule } from '@angular/platform-browser/animations';\nimport { AppRoutingModule } from './app-routing.module';\nimport { AppComponent } from './app.component';\nimport { NbDatepickerModule, NbDialogModule, NbMenuModule, NbSidebarModule, NbThemeModule, NbTimepickerModule, NbToastrModule, NbWindowModule } from '@nebular/theme';\nimport { ThemeModule } from './@theme/theme.module';\nimport { PagesComponent } from './pages/pages.component';\nimport { NgxSpinnerModule } from 'ngx-spinner';\nimport { NgbModule } from '@ng-bootstrap/ng-bootstrap';\nimport { FormsModule } from '@angular/forms';\nimport { HomeComponent } from './pages/home/<USER>';\nimport { LoginComponent } from './pages/login/login.component';\nimport { LogoutComponent } from './pages/logout/logout.component';\nimport { HTTP_INTERCEPTORS, HttpClientModule } from '@angular/common/http';\nimport { CoreModule } from './@core/core.module';\nimport { appConfig } from './app.config';\nimport { TokenInterceptor } from './shared/auth/token.interceptor';\nimport { FullCalendarModule } from '@fullcalendar/angular';\nimport { SharedModule } from './pages/components/shared.module';\nimport { CalendarModule, DateAdapter } from 'angular-calendar';\nimport { adapterFactory } from 'angular-calendar/date-adapters/date-fns';\nimport { registerLocaleData } from '@angular/common';\nimport localeZh from '@angular/common/locales/zh';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@nebular/theme\";\nimport * as i2 from \"./@theme/theme.module\";\nimport * as i3 from \"./@core/core.module\";\nimport * as i4 from \"angular-calendar\";\nregisterLocaleData(localeZh);\nexport class AppModule {\n  static {\n    this.ɵfac = function AppModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || AppModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: AppModule,\n      bootstrap: [AppComponent]\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      providers: [{\n        provide: HTTP_INTERCEPTORS,\n        useClass: TokenInterceptor,\n        multi: true\n      }, appConfig.providers],\n      imports: [NgxSpinnerModule, BrowserModule, BrowserAnimationsModule, AppRoutingModule, FormsModule, SharedModule, HttpClientModule, NbThemeModule.forRoot(), ThemeModule.forRoot(), NbSidebarModule.forRoot(), NbMenuModule.forRoot(), NbDatepickerModule.forRoot(), NbDialogModule.forRoot(), NbWindowModule.forRoot(), NbToastrModule.forRoot(), NbTimepickerModule.forRoot(), NgbModule, CoreModule.forRoot(), PagesComponent, HomeComponent, LoginComponent, FullCalendarModule, CalendarModule.forRoot({\n        provide: DateAdapter,\n        useFactory: adapterFactory\n      })]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(AppModule, {\n    declarations: [AppComponent],\n    imports: [NgxSpinnerModule, BrowserModule, BrowserAnimationsModule, AppRoutingModule, FormsModule, SharedModule, HttpClientModule, i1.NbThemeModule, i2.ThemeModule, i1.NbSidebarModule, i1.NbMenuModule, i1.NbDatepickerModule, i1.NbDialogModule, i1.NbWindowModule, i1.NbToastrModule, i1.NbTimepickerModule, NgbModule, i3.CoreModule, PagesComponent, HomeComponent, LoginComponent, LogoutComponent, FullCalendarModule, i4.CalendarModule]\n  });\n})();", "map": {"version": 3, "names": ["BrowserModule", "BrowserAnimationsModule", "AppRoutingModule", "AppComponent", "NbDatepickerModule", "NbDialogModule", "NbMenuModule", "NbSidebarModule", "NbThemeModule", "NbTimepickerModule", "NbToastrModule", "NbWindowModule", "ThemeModule", "PagesComponent", "NgxSpinnerModule", "NgbModule", "FormsModule", "HomeComponent", "LoginComponent", "LogoutComponent", "HTTP_INTERCEPTORS", "HttpClientModule", "CoreModule", "appConfig", "TokenInterceptor", "FullCalendarModule", "SharedModule", "CalendarModule", "DateAdapter", "adapterFactory", "registerLocaleData", "localeZh", "AppModule", "bootstrap", "provide", "useClass", "multi", "providers", "imports", "forRoot", "useFactory", "declarations", "i1", "i2", "i3", "i4"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\app.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { BrowserModule } from '@angular/platform-browser';\r\nimport { BrowserAnimationsModule } from '@angular/platform-browser/animations';\r\nimport { AppRoutingModule } from './app-routing.module';\r\nimport { AppComponent } from './app.component';\r\nimport { NbDatepickerModule, NbDialogModule, NbMenuModule, NbSidebarModule, NbThemeModule, NbTimepickerModule, NbToastrModule, NbWindowModule } from '@nebular/theme';\r\nimport { ThemeModule } from './@theme/theme.module';\r\nimport { PagesComponent } from './pages/pages.component';\r\nimport { NgxSpinnerModule } from 'ngx-spinner';\r\nimport { NgbModule } from '@ng-bootstrap/ng-bootstrap';\r\nimport { FormsModule } from '@angular/forms';\r\nimport { HomeComponent } from './pages/home/<USER>';\r\nimport { LoginComponent } from './pages/login/login.component';\r\nimport { LogoutComponent } from './pages/logout/logout.component';\r\nimport { HTTP_INTERCEPTORS, HttpClientModule } from '@angular/common/http';\r\nimport { CoreModule } from './@core/core.module';\r\nimport { appConfig } from './app.config';\r\nimport { TokenInterceptor } from './shared/auth/token.interceptor';\r\nimport { FullCalendarModule } from '@fullcalendar/angular';\r\nimport { SharedModule } from './pages/components/shared.module';\r\nimport { CalendarModule, DateAdapter } from 'angular-calendar';\r\nimport { adapterFactory } from 'angular-calendar/date-adapters/date-fns';\r\nimport { registerLocaleData } from '@angular/common';\r\nimport  localeZh  from '@angular/common/locales/zh';\r\n\r\nregisterLocaleData(localeZh);\r\n\r\n@NgModule({\r\n    declarations: [AppComponent],\r\n    imports: [\r\n        NgxSpinnerModule,\r\n        BrowserModule,\r\n        BrowserAnimationsModule,\r\n        AppRoutingModule,\r\n        FormsModule,\r\n        SharedModule,\r\n        HttpClientModule,\r\n        NbThemeModule.forRoot(),\r\n        ThemeModule.forRoot(),\r\n        NbSidebarModule.forRoot(),\r\n        NbMenuModule.forRoot(),\r\n        NbDatepickerModule.forRoot(),\r\n        NbDialogModule.forRoot(),\r\n        NbWindowModule.forRoot(),\r\n        NbToastrModule.forRoot(),\r\n        NbTimepickerModule.forRoot(),\r\n        NgbModule,\r\n        CoreModule.forRoot(),\r\n        PagesComponent,\r\n        HomeComponent,\r\n        LoginComponent,\r\n        LogoutComponent,\r\n        FullCalendarModule,\r\n        CalendarModule.forRoot({\r\n            provide: DateAdapter,\r\n            useFactory: adapterFactory,\r\n        }),\r\n    ],\r\n    providers: [\r\n        { provide: HTTP_INTERCEPTORS, useClass: TokenInterceptor, multi: true },\r\n        appConfig.providers,\r\n    ],\r\n    bootstrap: [AppComponent]\r\n})\r\nexport class AppModule { }\r\n"], "mappings": "AACA,SAASA,aAAa,QAAQ,2BAA2B;AACzD,SAASC,uBAAuB,QAAQ,sCAAsC;AAC9E,SAASC,gBAAgB,QAAQ,sBAAsB;AACvD,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,kBAAkB,EAAEC,cAAc,EAAEC,YAAY,EAAEC,eAAe,EAAEC,aAAa,EAAEC,kBAAkB,EAAEC,cAAc,EAAEC,cAAc,QAAQ,gBAAgB;AACrK,SAASC,WAAW,QAAQ,uBAAuB;AACnD,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,gBAAgB,QAAQ,aAAa;AAC9C,SAASC,SAAS,QAAQ,4BAA4B;AACtD,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,aAAa,QAAQ,6BAA6B;AAC3D,SAASC,cAAc,QAAQ,+BAA+B;AAC9D,SAASC,eAAe,QAAQ,iCAAiC;AACjE,SAASC,iBAAiB,EAAEC,gBAAgB,QAAQ,sBAAsB;AAC1E,SAASC,UAAU,QAAQ,qBAAqB;AAChD,SAASC,SAAS,QAAQ,cAAc;AACxC,SAASC,gBAAgB,QAAQ,iCAAiC;AAClE,SAASC,kBAAkB,QAAQ,uBAAuB;AAC1D,SAASC,YAAY,QAAQ,kCAAkC;AAC/D,SAASC,cAAc,EAAEC,WAAW,QAAQ,kBAAkB;AAC9D,SAASC,cAAc,QAAQ,yCAAyC;AACxE,SAASC,kBAAkB,QAAQ,iBAAiB;AACpD,OAAQC,QAAQ,MAAO,4BAA4B;;;;;;AAEnDD,kBAAkB,CAACC,QAAQ,CAAC;AAuC5B,OAAM,MAAOC,SAAS;;;uCAATA,SAAS;IAAA;EAAA;;;YAATA,SAAS;MAAAC,SAAA,GAFN9B,YAAY;IAAA;EAAA;;;iBAJb,CACP;QAAE+B,OAAO,EAAEd,iBAAiB;QAAEe,QAAQ,EAAEX,gBAAgB;QAAEY,KAAK,EAAE;MAAI,CAAE,EACvEb,SAAS,CAACc,SAAS,CACtB;MAAAC,OAAA,GA/BGxB,gBAAgB,EAChBd,aAAa,EACbC,uBAAuB,EACvBC,gBAAgB,EAChBc,WAAW,EACXU,YAAY,EACZL,gBAAgB,EAChBb,aAAa,CAAC+B,OAAO,EAAE,EACvB3B,WAAW,CAAC2B,OAAO,EAAE,EACrBhC,eAAe,CAACgC,OAAO,EAAE,EACzBjC,YAAY,CAACiC,OAAO,EAAE,EACtBnC,kBAAkB,CAACmC,OAAO,EAAE,EAC5BlC,cAAc,CAACkC,OAAO,EAAE,EACxB5B,cAAc,CAAC4B,OAAO,EAAE,EACxB7B,cAAc,CAAC6B,OAAO,EAAE,EACxB9B,kBAAkB,CAAC8B,OAAO,EAAE,EAC5BxB,SAAS,EACTO,UAAU,CAACiB,OAAO,EAAE,EACpB1B,cAAc,EACdI,aAAa,EACbC,cAAc,EAEdO,kBAAkB,EAClBE,cAAc,CAACY,OAAO,CAAC;QACnBL,OAAO,EAAEN,WAAW;QACpBY,UAAU,EAAEX;OACf,CAAC;IAAA;EAAA;;;2EAQGG,SAAS;IAAAS,YAAA,GApCHtC,YAAY;IAAAmC,OAAA,GAEvBxB,gBAAgB,EAChBd,aAAa,EACbC,uBAAuB,EACvBC,gBAAgB,EAChBc,WAAW,EACXU,YAAY,EACZL,gBAAgB,EAAAqB,EAAA,CAAAlC,aAAA,EAAAmC,EAAA,CAAA/B,WAAA,EAAA8B,EAAA,CAAAnC,eAAA,EAAAmC,EAAA,CAAApC,YAAA,EAAAoC,EAAA,CAAAtC,kBAAA,EAAAsC,EAAA,CAAArC,cAAA,EAAAqC,EAAA,CAAA/B,cAAA,EAAA+B,EAAA,CAAAhC,cAAA,EAAAgC,EAAA,CAAAjC,kBAAA,EAUhBM,SAAS,EAAA6B,EAAA,CAAAtB,UAAA,EAETT,cAAc,EACdI,aAAa,EACbC,cAAc,EACdC,eAAe,EACfM,kBAAkB,EAAAoB,EAAA,CAAAlB,cAAA;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}