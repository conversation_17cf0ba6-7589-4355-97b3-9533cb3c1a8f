{"ast": null, "code": "import toDate from \"../../toDate/index.js\";\nimport requiredArgs from \"../requiredArgs/index.js\";\nvar MILLISECONDS_IN_DAY = 86400000;\nexport default function getUTCDayOfYear(dirtyDate) {\n  requiredArgs(1, arguments);\n  var date = toDate(dirtyDate);\n  var timestamp = date.getTime();\n  date.setUTCMonth(0, 1);\n  date.setUTCHours(0, 0, 0, 0);\n  var startOfYearTimestamp = date.getTime();\n  var difference = timestamp - startOfYearTimestamp;\n  return Math.floor(difference / MILLISECONDS_IN_DAY) + 1;\n}", "map": {"version": 3, "names": ["toDate", "requiredArgs", "MILLISECONDS_IN_DAY", "getUTCDayOfYear", "dirtyDate", "arguments", "date", "timestamp", "getTime", "setUTCMonth", "setUTCHours", "startOfYearTimestamp", "difference", "Math", "floor"], "sources": ["C:/Users/<USER>/Documents/salechange-product/adminFront/node_modules/date-fns/esm/_lib/getUTCDayOfYear/index.js"], "sourcesContent": ["import toDate from \"../../toDate/index.js\";\nimport requiredArgs from \"../requiredArgs/index.js\";\nvar MILLISECONDS_IN_DAY = 86400000;\nexport default function getUTCDayOfYear(dirtyDate) {\n  requiredArgs(1, arguments);\n  var date = toDate(dirtyDate);\n  var timestamp = date.getTime();\n  date.setUTCMonth(0, 1);\n  date.setUTCHours(0, 0, 0, 0);\n  var startOfYearTimestamp = date.getTime();\n  var difference = timestamp - startOfYearTimestamp;\n  return Math.floor(difference / MILLISECONDS_IN_DAY) + 1;\n}"], "mappings": "AAAA,OAAOA,MAAM,MAAM,uBAAuB;AAC1C,OAAOC,YAAY,MAAM,0BAA0B;AACnD,IAAIC,mBAAmB,GAAG,QAAQ;AAClC,eAAe,SAASC,eAAeA,CAACC,SAAS,EAAE;EACjDH,YAAY,CAAC,CAAC,EAAEI,SAAS,CAAC;EAC1B,IAAIC,IAAI,GAAGN,MAAM,CAACI,SAAS,CAAC;EAC5B,IAAIG,SAAS,GAAGD,IAAI,CAACE,OAAO,CAAC,CAAC;EAC9BF,IAAI,CAACG,WAAW,CAAC,CAAC,EAAE,CAAC,CAAC;EACtBH,IAAI,CAACI,WAAW,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;EAC5B,IAAIC,oBAAoB,GAAGL,IAAI,CAACE,OAAO,CAAC,CAAC;EACzC,IAAII,UAAU,GAAGL,SAAS,GAAGI,oBAAoB;EACjD,OAAOE,IAAI,CAACC,KAAK,CAACF,UAAU,GAAGV,mBAAmB,CAAC,GAAG,CAAC;AACzD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}