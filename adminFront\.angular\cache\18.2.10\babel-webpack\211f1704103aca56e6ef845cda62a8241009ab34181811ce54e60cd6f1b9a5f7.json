{"ast": null, "code": "import { EventEmitter, forwardRef } from '@angular/core';\nimport { NG_VALUE_ACCESSOR } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../../../services/api/services/HouseCustom.service\";\nimport * as i2 from \"@nebular/theme\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"@angular/forms\";\nconst _c0 = [\"householdDialog\"];\nfunction HouseholdBindingComponent_div_1_div_9_ng_container_1_span_4_span_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 26);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const houseId_r5 = i0.ɵɵnextContext().$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.getHouseholdInfoById(houseId_r5).floor, \" \");\n  }\n}\nfunction HouseholdBindingComponent_div_1_div_9_ng_container_1_span_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"span\", 20)(1, \"div\", 21)(2, \"span\", 22);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, HouseholdBindingComponent_div_1_div_9_ng_container_1_span_4_span_4_Template, 2, 1, \"span\", 23);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 24);\n    i0.ɵɵlistener(\"click\", function HouseholdBindingComponent_div_1_div_9_ng_container_1_span_4_Template_button_click_5_listener() {\n      const houseId_r5 = i0.ɵɵrestoreView(_r4).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r2.onRemoveHousehold(houseId_r5));\n    });\n    i0.ɵɵelement(6, \"nb-icon\", 25);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const houseId_r5 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r2.getHouseholdInfoById(houseId_r5).houseName);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.getHouseholdInfoById(houseId_r5).floor);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r2.disabled);\n  }\n}\nfunction HouseholdBindingComponent_div_1_div_9_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 17);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 18);\n    i0.ɵɵtemplate(4, HouseholdBindingComponent_div_1_div_9_ng_container_1_span_4_Template, 7, 3, \"span\", 19);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const building_r6 = i0.ɵɵnextContext().$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", building_r6, \":\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.getBuildingSelectedHouseIds(building_r6));\n  }\n}\nfunction HouseholdBindingComponent_div_1_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 16);\n    i0.ɵɵtemplate(1, HouseholdBindingComponent_div_1_div_9_ng_container_1_Template, 5, 2, \"ng-container\", 6);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const building_r6 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.hasBuildingSelected(building_r6));\n  }\n}\nfunction HouseholdBindingComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 8)(1, \"div\", 9)(2, \"div\", 10);\n    i0.ɵɵelement(3, \"nb-icon\", 11);\n    i0.ɵɵelementStart(4, \"span\", 12);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"button\", 13);\n    i0.ɵɵlistener(\"click\", function HouseholdBindingComponent_div_1_Template_button_click_6_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onClearAll());\n    });\n    i0.ɵɵtext(7, \" \\u6E05\\u7A7A\\u5168\\u90E8 \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 14);\n    i0.ɵɵtemplate(9, HouseholdBindingComponent_div_1_div_9_Template, 2, 1, \"div\", 15);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\"\\u5DF2\\u9078\\u64C7\\u6236\\u5225 (\", ctx_r2.getSelectedCount(), \")\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r2.disabled);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.buildings);\n  }\n}\nfunction HouseholdBindingComponent_ng_container_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelement(1, \"nb-icon\", 27);\n    i0.ɵɵtext(2, \" \\u8F09\\u5165\\u4E2D... \");\n    i0.ɵɵelementContainerEnd();\n  }\n}\nfunction HouseholdBindingComponent_ng_container_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.getSelectedCount() > 0 ? \"\\u5DF2\\u9078\\u64C7 \" + ctx_r2.getSelectedCount() + \" \\u500B\\u6236\\u5225\" : ctx_r2.placeholder, \" \");\n  }\n}\nfunction HouseholdBindingComponent_ng_template_8_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 52)(1, \"div\", 53);\n    i0.ɵɵelement(2, \"nb-icon\", 54);\n    i0.ɵɵelementStart(3, \"p\", 55);\n    i0.ɵɵtext(4, \"\\u8F09\\u5165\\u6236\\u5225\\u8CC7\\u6599\\u4E2D...\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction HouseholdBindingComponent_ng_template_8_div_13_div_10_div_9_span_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 94);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const building_r9 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \\u53EF\\u9078\\uFF1A\", ctx_r2.getBuildingAvailableCount(building_r9), \" \");\n  }\n}\nfunction HouseholdBindingComponent_ng_template_8_div_13_div_10_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 88)(1, \"div\", 89);\n    i0.ɵɵelement(2, \"div\", 90);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 91)(4, \"span\", 92);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(6, HouseholdBindingComponent_ng_template_8_div_13_div_10_div_9_span_6_Template, 2, 1, \"span\", 93);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const building_r9 = i0.ɵɵnextContext().$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵstyleProp(\"width\", ctx_r2.getBuildingSelectionProgress(building_r9), \"%\");\n    i0.ɵɵclassProp(\"full-selection\", ctx_r2.isBuildingFullySelected(building_r9));\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"fully-selected\", ctx_r2.isBuildingFullySelected(building_r9))(\"no-selection\", ctx_r2.getBuildingSelectedHouseIds(building_r9).length === 0);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.getBuildingSelectionStatus(building_r9), \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.getBuildingAvailableCount(building_r9) !== ctx_r2.getBuildingCount(building_r9));\n  }\n}\nfunction HouseholdBindingComponent_ng_template_8_div_13_div_10_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 95);\n    i0.ɵɵelement(1, \"nb-icon\", 96);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction HouseholdBindingComponent_ng_template_8_div_13_div_10_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 97)(1, \"span\", 98);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const building_r9 = i0.ɵɵnextContext().$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r2.getBuildingSelectedHouseIds(building_r9).length);\n  }\n}\nfunction HouseholdBindingComponent_ng_template_8_div_13_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 78);\n    i0.ɵɵlistener(\"click\", function HouseholdBindingComponent_ng_template_8_div_13_div_10_Template_div_click_0_listener() {\n      const building_r9 = i0.ɵɵrestoreView(_r8).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r2.onBuildingSelect(building_r9));\n    });\n    i0.ɵɵelementStart(1, \"div\", 79)(2, \"div\", 80);\n    i0.ɵɵelement(3, \"nb-icon\", 81);\n    i0.ɵɵelementStart(4, \"span\", 82);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 83)(7, \"span\", 84);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(9, HouseholdBindingComponent_ng_template_8_div_13_div_10_div_9_Template, 7, 10, \"div\", 85)(10, HouseholdBindingComponent_ng_template_8_div_13_div_10_div_10_Template, 2, 0, \"div\", 86)(11, HouseholdBindingComponent_ng_template_8_div_13_div_10_div_11_Template, 3, 1, \"div\", 87);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const building_r9 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵclassProp(\"selected\", ctx_r2.selectedBuilding === building_r9)(\"has-selection\", ctx_r2.hasBuildingSelected(building_r9));\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(building_r9);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r2.getBuildingCount(building_r9), \"\\u6236\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.getBuildingCount(building_r9) > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.selectedBuilding === building_r9);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.getBuildingSelectedHouseIds(building_r9).length > 0);\n  }\n}\nfunction HouseholdBindingComponent_ng_template_8_div_13_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 99);\n    i0.ɵɵelement(1, \"nb-icon\", 100);\n    i0.ɵɵelementStart(2, \"p\", 101);\n    i0.ɵɵtext(3, \"\\u66AB\\u7121\\u53EF\\u7528\\u68DF\\u5225\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction HouseholdBindingComponent_ng_template_8_div_13_span_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 102);\n    i0.ɵɵelement(1, \"nb-icon\", 103);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.selectedBuilding, \" \");\n  }\n}\nfunction HouseholdBindingComponent_ng_template_8_div_13_span_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 104);\n    i0.ɵɵelement(1, \"nb-icon\", 105);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.selectedFloor, \" \");\n  }\n}\nfunction HouseholdBindingComponent_ng_template_8_div_13_div_20_button_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 110);\n    i0.ɵɵlistener(\"click\", function HouseholdBindingComponent_ng_template_8_div_13_div_20_button_5_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r2 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r2.onUnselectAllBuilding());\n    });\n    i0.ɵɵtext(1, \" \\u6E05\\u9664 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction HouseholdBindingComponent_ng_template_8_div_13_div_20_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 106)(1, \"button\", 107);\n    i0.ɵɵlistener(\"click\", function HouseholdBindingComponent_ng_template_8_div_13_div_20_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r2.onSelectAllFiltered());\n    });\n    i0.ɵɵtext(2, \" \\u5168\\u9078\\u7576\\u524D \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"button\", 108);\n    i0.ɵɵlistener(\"click\", function HouseholdBindingComponent_ng_template_8_div_13_div_20_Template_button_click_3_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r2.onSelectAllBuilding());\n    });\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(5, HouseholdBindingComponent_ng_template_8_div_13_div_20_button_5_Template, 2, 0, \"button\", 109);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵstyleProp(\"opacity\", ctx_r2.canSelectMore() ? \"1\" : \"0.5\");\n    i0.ɵɵproperty(\"disabled\", !ctx_r2.canSelectMore());\n    i0.ɵɵadvance(2);\n    i0.ɵɵstyleProp(\"opacity\", ctx_r2.canSelectMore() ? \"1\" : \"0.5\");\n    i0.ɵɵproperty(\"disabled\", !ctx_r2.canSelectMore());\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \\u5168\\u9078\", ctx_r2.selectedBuilding, \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isSomeBuildingSelected());\n  }\n}\nfunction HouseholdBindingComponent_ng_template_8_div_13_div_21_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 116);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \\u627E\\u4E0D\\u5230\\u7B26\\u5408 \\\"\", ctx_r2.searchTerm, \"\\\" \\u7684\\u6236\\u5225 \");\n  }\n}\nfunction HouseholdBindingComponent_ng_template_8_div_13_div_21_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 111)(1, \"div\", 112)(2, \"input\", 113);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdBindingComponent_ng_template_8_div_13_div_21_Template_input_ngModelChange_2_listener($event) {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      i0.ɵɵtwoWayBindingSet(ctx_r2.searchTerm, $event) || (ctx_r2.searchTerm = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"input\", function HouseholdBindingComponent_ng_template_8_div_13_div_21_Template_input_input_2_listener($event) {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r2.onSearchChange($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(3, \"nb-icon\", 114);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, HouseholdBindingComponent_ng_template_8_div_13_div_21_div_4_Template, 2, 1, \"div\", 115);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.searchTerm);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.searchTerm && ctx_r2.hasNoSearchResults());\n  }\n}\nfunction HouseholdBindingComponent_ng_template_8_div_13_div_22_span_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 104);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.selectedFloor, \" \");\n  }\n}\nfunction HouseholdBindingComponent_ng_template_8_div_13_div_22_button_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r13 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 124);\n    i0.ɵɵlistener(\"click\", function HouseholdBindingComponent_ng_template_8_div_13_div_22_button_6_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r2 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r2.onFloorSelect(\"\"));\n    });\n    i0.ɵɵtext(1, \" \\u6E05\\u9664\\u7BE9\\u9078 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction HouseholdBindingComponent_ng_template_8_div_13_div_22_button_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r14 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 125);\n    i0.ɵɵlistener(\"click\", function HouseholdBindingComponent_ng_template_8_div_13_div_22_button_8_Template_button_click_0_listener() {\n      const floor_r15 = i0.ɵɵrestoreView(_r14).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r2.onFloorSelect(floor_r15));\n    });\n    i0.ɵɵelement(1, \"nb-icon\", 126);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementStart(3, \"span\", 127);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const floor_r15 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(4);\n    i0.ɵɵstyleProp(\"background-color\", ctx_r2.selectedFloor === floor_r15 ? \"#007bff\" : \"#f8f9fa\")(\"color\", ctx_r2.selectedFloor === floor_r15 ? \"#fff\" : \"#495057\")(\"border\", ctx_r2.selectedFloor === floor_r15 ? \"2px solid #007bff\" : \"1px solid #dee2e6\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", floor_r15, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"(\", ctx_r2.getFloorCount(floor_r15), \")\");\n  }\n}\nfunction HouseholdBindingComponent_ng_template_8_div_13_div_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 117)(1, \"div\", 118);\n    i0.ɵɵelement(2, \"nb-icon\", 119);\n    i0.ɵɵelementStart(3, \"span\", 120);\n    i0.ɵɵtext(4, \"\\u6A13\\u5C64\\u7BE9\\u9078:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(5, HouseholdBindingComponent_ng_template_8_div_13_div_22_span_5_Template, 2, 1, \"span\", 71)(6, HouseholdBindingComponent_ng_template_8_div_13_div_22_button_6_Template, 2, 0, \"button\", 121);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 122);\n    i0.ɵɵtemplate(8, HouseholdBindingComponent_ng_template_8_div_13_div_22_button_8_Template, 5, 8, \"button\", 123);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.selectedFloor);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.selectedFloor);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.floors);\n  }\n}\nfunction HouseholdBindingComponent_ng_template_8_div_13_div_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 128);\n    i0.ɵɵelement(1, \"nb-icon\", 129);\n    i0.ɵɵelementStart(2, \"p\", 55);\n    i0.ɵɵtext(3, \"\\u8ACB\\u5148\\u9078\\u64C7\\u68DF\\u5225\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction HouseholdBindingComponent_ng_template_8_div_13_div_25_ng_container_1_button_1_span_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 137);\n    i0.ɵɵelement(1, \"nb-icon\", 138);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const household_r17 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(4);\n    i0.ɵɵstyleProp(\"background-color\", ctx_r2.isHouseholdSelected(household_r17.houseId) ? \"rgba(255,255,255,0.9)\" : \"#28a745\")(\"color\", ctx_r2.isHouseholdSelected(household_r17.houseId) ? \"#007bff\" : \"#fff\")(\"border\", ctx_r2.isHouseholdSelected(household_r17.houseId) ? \"1px solid rgba(0,123,255,0.3)\" : \"none\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", household_r17.floor, \" \");\n  }\n}\nfunction HouseholdBindingComponent_ng_template_8_div_13_div_25_ng_container_1_button_1_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 139);\n    i0.ɵɵtext(1, \" \\u2715 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction HouseholdBindingComponent_ng_template_8_div_13_div_25_ng_container_1_button_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r16 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 133);\n    i0.ɵɵlistener(\"click\", function HouseholdBindingComponent_ng_template_8_div_13_div_25_ng_container_1_button_1_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r16);\n      const household_r17 = i0.ɵɵnextContext().$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r2.onHouseholdToggle(household_r17.houseId));\n    });\n    i0.ɵɵelementStart(1, \"span\", 134);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, HouseholdBindingComponent_ng_template_8_div_13_div_25_ng_container_1_button_1_span_3_Template, 3, 7, \"span\", 135)(4, HouseholdBindingComponent_ng_template_8_div_13_div_25_ng_container_1_button_1_div_4_Template, 2, 0, \"div\", 136);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const household_r17 = i0.ɵɵnextContext().$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(4);\n    i0.ɵɵstyleProp(\"background-color\", ctx_r2.isHouseholdSelected(household_r17.houseId) ? \"#007bff\" : ctx_r2.isHouseholdExcluded(household_r17.houseId) ? \"#f8f9fa\" : \"#fff\")(\"color\", ctx_r2.isHouseholdSelected(household_r17.houseId) ? \"#fff\" : ctx_r2.isHouseholdExcluded(household_r17.houseId) ? \"#6c757d\" : \"#495057\")(\"border\", ctx_r2.isHouseholdSelected(household_r17.houseId) ? \"2px solid #007bff\" : ctx_r2.isHouseholdExcluded(household_r17.houseId) ? \"1px solid #dee2e6\" : \"1px solid #ced4da\")(\"opacity\", ctx_r2.isHouseholdDisabled(household_r17.houseId) ? \"0.6\" : \"1\")(\"cursor\", ctx_r2.isHouseholdDisabled(household_r17.houseId) ? \"not-allowed\" : \"pointer\");\n    i0.ɵɵproperty(\"disabled\", ctx_r2.isHouseholdDisabled(household_r17.houseId));\n    i0.ɵɵadvance();\n    i0.ɵɵstyleProp(\"text-decoration\", ctx_r2.isHouseholdExcluded(household_r17.houseId) ? \"line-through\" : \"none\");\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", household_r17.houseName, \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", household_r17.floor);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isHouseholdExcluded(household_r17.houseId));\n  }\n}\nfunction HouseholdBindingComponent_ng_template_8_div_13_div_25_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, HouseholdBindingComponent_ng_template_8_div_13_div_25_ng_container_1_button_1_Template, 5, 16, \"button\", 132);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const household_r17 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (!ctx_r2.selectedFloor || household_r17.floor === ctx_r2.selectedFloor) && (!ctx_r2.searchTerm || household_r17.houseName.toLowerCase().includes(ctx_r2.searchTerm.toLowerCase())));\n  }\n}\nfunction HouseholdBindingComponent_ng_template_8_div_13_div_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 130);\n    i0.ɵɵtemplate(1, HouseholdBindingComponent_ng_template_8_div_13_div_25_ng_container_1_Template, 2, 1, \"ng-container\", 131);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.buildingData[ctx_r2.selectedBuilding]);\n  }\n}\nfunction HouseholdBindingComponent_ng_template_8_div_13_div_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 128);\n    i0.ɵɵelement(1, \"nb-icon\", 140);\n    i0.ɵɵelementStart(2, \"p\", 55);\n    i0.ɵɵtext(3, \"\\u6B64\\u68DF\\u5225\\u6C92\\u6709\\u53EF\\u7528\\u7684\\u6236\\u5225\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction HouseholdBindingComponent_ng_template_8_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 56)(1, \"div\", 57)(2, \"div\", 58)(3, \"div\", 59);\n    i0.ɵɵelement(4, \"nb-icon\", 60);\n    i0.ɵɵelementStart(5, \"h6\", 61);\n    i0.ɵɵtext(6, \"\\u68DF\\u5225\\u5217\\u8868\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"span\", 62);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(9, \"div\", 63);\n    i0.ɵɵtemplate(10, HouseholdBindingComponent_ng_template_8_div_13_div_10_Template, 12, 9, \"div\", 64)(11, HouseholdBindingComponent_ng_template_8_div_13_div_11_Template, 4, 0, \"div\", 65);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(12, \"div\", 66)(13, \"div\", 67)(14, \"div\", 68)(15, \"div\", 30)(16, \"h6\", 69);\n    i0.ɵɵtext(17, \"\\u6236\\u5225\\u9078\\u64C7\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(18, HouseholdBindingComponent_ng_template_8_div_13_span_18_Template, 3, 1, \"span\", 70)(19, HouseholdBindingComponent_ng_template_8_div_13_span_19_Template, 3, 1, \"span\", 71);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(20, HouseholdBindingComponent_ng_template_8_div_13_div_20_Template, 6, 8, \"div\", 72);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(21, HouseholdBindingComponent_ng_template_8_div_13_div_21_Template, 5, 2, \"div\", 73)(22, HouseholdBindingComponent_ng_template_8_div_13_div_22_Template, 9, 3, \"div\", 74);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"div\", 75);\n    i0.ɵɵtemplate(24, HouseholdBindingComponent_ng_template_8_div_13_div_24_Template, 4, 0, \"div\", 76)(25, HouseholdBindingComponent_ng_template_8_div_13_div_25_Template, 2, 1, \"div\", 77)(26, HouseholdBindingComponent_ng_template_8_div_13_div_26_Template, 4, 0, \"div\", 76);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r2.buildings.length, \" \\u68DF\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.buildings);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.buildings.length === 0);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.selectedBuilding);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.selectedFloor);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.allowBatchSelect && ctx_r2.selectedBuilding && ctx_r2.buildingData[ctx_r2.selectedBuilding] && ctx_r2.buildingData[ctx_r2.selectedBuilding].length > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.allowSearch && ctx_r2.selectedBuilding);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.selectedBuilding && ctx_r2.floors.length > 1);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.selectedBuilding);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.selectedBuilding && ctx_r2.buildingData[ctx_r2.selectedBuilding] && ctx_r2.buildingData[ctx_r2.selectedBuilding].length > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.selectedBuilding && (!ctx_r2.buildingData[ctx_r2.selectedBuilding] || ctx_r2.buildingData[ctx_r2.selectedBuilding].length === 0) && !ctx_r2.searchTerm);\n  }\n}\nfunction HouseholdBindingComponent_ng_template_8_div_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 40);\n    i0.ɵɵelement(1, \"nb-icon\", 141);\n    i0.ɵɵelementStart(2, \"span\");\n    i0.ɵɵtext(3, \"\\u9650\\u5236: \\u6700\\u591A \");\n    i0.ɵɵelementStart(4, \"strong\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(6, \" \\u500B\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r2.maxSelections);\n  }\n}\nfunction HouseholdBindingComponent_ng_template_8_div_25_span_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 145);\n    i0.ɵɵelement(1, \"nb-icon\", 105);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.selectedFloor, \" \");\n  }\n}\nfunction HouseholdBindingComponent_ng_template_8_div_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 30);\n    i0.ɵɵelement(1, \"nb-icon\", 142);\n    i0.ɵɵelementStart(2, \"span\");\n    i0.ɵɵtext(3, \"\\u7576\\u524D\\u68DF\\u5225: \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\", 143);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(6, HouseholdBindingComponent_ng_template_8_div_25_span_6_Template, 3, 1, \"span\", 144);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.selectedBuilding, \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.selectedFloor);\n  }\n}\nfunction HouseholdBindingComponent_ng_template_8_div_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 146);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate2(\" \\u641C\\u5C0B: \\\"\", ctx_r2.searchTerm, \"\\\" (\", ctx_r2.getFilteredHouseholdsCount(), \" \\u500B\\u7D50\\u679C) \");\n  }\n}\nfunction HouseholdBindingComponent_ng_template_8_button_29_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r18 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 147);\n    i0.ɵɵlistener(\"click\", function HouseholdBindingComponent_ng_template_8_button_29_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r18);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.onClearAll());\n    });\n    i0.ɵɵelement(1, \"nb-icon\", 148);\n    i0.ɵɵtext(2, \" \\u6E05\\u7A7A\\u5168\\u90E8 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction HouseholdBindingComponent_ng_template_8_button_30_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r19 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 149);\n    i0.ɵɵlistener(\"click\", function HouseholdBindingComponent_ng_template_8_button_30_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.resetSearch());\n    });\n    i0.ɵɵelement(1, \"nb-icon\", 150);\n    i0.ɵɵtext(2, \" \\u91CD\\u7F6E\\u641C\\u5C0B \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction HouseholdBindingComponent_ng_template_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-card\", 28)(1, \"nb-card-header\")(2, \"div\", 29)(3, \"div\", 30);\n    i0.ɵɵelement(4, \"nb-icon\", 31);\n    i0.ɵɵelementStart(5, \"span\", 32);\n    i0.ɵɵtext(6, \"\\u9078\\u64C7\\u6236\\u5225\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"span\", 33);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"span\", 33);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(11, \"nb-card-body\", 34);\n    i0.ɵɵtemplate(12, HouseholdBindingComponent_ng_template_8_div_12_Template, 5, 0, \"div\", 35)(13, HouseholdBindingComponent_ng_template_8_div_13_Template, 27, 11, \"div\", 36);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"nb-card-footer\", 37)(15, \"div\", 38)(16, \"div\", 39)(17, \"div\", 40);\n    i0.ɵɵelement(18, \"nb-icon\", 41);\n    i0.ɵɵelementStart(19, \"span\");\n    i0.ɵɵtext(20, \"\\u5DF2\\u9078\\u64C7: \");\n    i0.ɵɵelementStart(21, \"strong\");\n    i0.ɵɵtext(22);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(23, \" \\u500B\\u6236\\u5225\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(24, HouseholdBindingComponent_ng_template_8_div_24_Template, 7, 1, \"div\", 42)(25, HouseholdBindingComponent_ng_template_8_div_25_Template, 7, 2, \"div\", 43);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(26, HouseholdBindingComponent_ng_template_8_div_26_Template, 2, 2, \"div\", 44);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(27, \"div\", 45)(28, \"div\", 46);\n    i0.ɵɵtemplate(29, HouseholdBindingComponent_ng_template_8_button_29_Template, 3, 0, \"button\", 47)(30, HouseholdBindingComponent_ng_template_8_button_30_Template, 3, 0, \"button\", 48);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(31, \"div\", 46)(32, \"button\", 49);\n    i0.ɵɵlistener(\"click\", function HouseholdBindingComponent_ng_template_8_Template_button_click_32_listener() {\n      const ref_r20 = i0.ɵɵrestoreView(_r7).dialogRef;\n      return i0.ɵɵresetView(ref_r20.close());\n    });\n    i0.ɵɵtext(33, \" \\u53D6\\u6D88 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(34, \"button\", 50);\n    i0.ɵɵlistener(\"click\", function HouseholdBindingComponent_ng_template_8_Template_button_click_34_listener() {\n      const ref_r20 = i0.ɵɵrestoreView(_r7).dialogRef;\n      return i0.ɵɵresetView(ref_r20.close());\n    });\n    i0.ɵɵelement(35, \"nb-icon\", 51);\n    i0.ɵɵtext(36);\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate1(\"(\", ctx_r2.buildings.length, \" \\u500B\\u68DF\\u5225)\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\\u5DF2\\u9078\\u64C7: \", ctx_r2.getSelectedCount(), \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isLoading);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.isLoading);\n    i0.ɵɵadvance(9);\n    i0.ɵɵtextInterpolate(ctx_r2.getSelectedCount());\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.maxSelections);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.selectedBuilding);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.searchTerm);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.selectedHouseIds.length > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.allowSearch && ctx_r2.searchTerm);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\" \\u78BA\\u5B9A\\u9078\\u64C7 (\", ctx_r2.getSelectedCount(), \") \");\n  }\n}\nexport class HouseholdBindingComponent {\n  constructor(cdr, houseCustomService, dialogService) {\n    this.cdr = cdr;\n    this.houseCustomService = houseCustomService;\n    this.dialogService = dialogService;\n    this.placeholder = '請選擇戶別';\n    this.maxSelections = null;\n    this.disabled = false;\n    this.buildCaseId = null; // 新增：建案ID\n    this.buildingData = {};\n    this.showSelectedArea = true;\n    this.allowSearch = true;\n    this.allowBatchSelect = true;\n    this.excludedHouseIds = []; // 改為：排除的戶別ID（已被其他元件選擇）\n    this.selectionChange = new EventEmitter();\n    this.houseIdChange = new EventEmitter(); // 新增：回傳 houseId 陣列\n    this.isOpen = false;\n    this.selectedBuilding = '';\n    this.searchTerm = '';\n    this.selectedFloor = ''; // 新增：選中的樓層\n    this.selectedHouseIds = []; // 改為：使用 houseId 作為選擇的key\n    this.buildings = [];\n    this.floors = []; // 新增：當前棧別的樓層列表\n    this.filteredHouseholds = []; // 保持為字串陣列用於UI顯示\n    this.selectedByBuilding = {}; // 改為：儲存 houseId\n    this.isLoading = false; // 新增：載入狀態\n    // ControlValueAccessor implementation\n    this.onChange = value => {};\n    this.onTouched = () => {};\n  }\n  writeValue(value) {\n    console.log('writeValue called with:', value, 'type:', typeof value?.[0]);\n    if (!value || value.length === 0) {\n      this.selectedHouseIds = [];\n    } else {\n      // 檢查傳入的是 houseId 還是 houseName\n      const firstItem = value[0];\n      if (typeof firstItem === 'number') {\n        // 如果是數字，直接使用\n        this.selectedHouseIds = value;\n        console.log('使用傳入的 houseId 陣列');\n      } else if (typeof firstItem === 'string') {\n        // 如果是字串（houseName），這是一個不推薦的使用方式\n        console.error('⚠️ 警告：收到 houseName 陣列而不是 houseId 陣列！');\n        console.error('⚠️ 這會導致同名戶別的選擇問題！');\n        console.error('⚠️ 建議父元件改用 houseId 陣列:', value);\n        // 暫時跳過轉換，保持當前選擇不變\n        console.error('⚠️ 跳過此次 writeValue 以避免錯誤選擇');\n        return;\n      } else {\n        console.error('writeValue 收到未知格式的資料:', value);\n        this.selectedHouseIds = [];\n      }\n    }\n    console.log('selectedHouseIds set to:', this.selectedHouseIds);\n    this.updateSelectedByBuilding();\n  }\n  registerOnChange(fn) {\n    this.onChange = fn;\n  }\n  registerOnTouched(fn) {\n    this.onTouched = fn;\n  }\n  setDisabledState(isDisabled) {\n    this.disabled = isDisabled;\n  }\n  ngOnInit() {\n    this.initializeData();\n  }\n  ngOnChanges(changes) {\n    if (changes['buildCaseId'] && this.buildCaseId) {\n      // 當建案ID變更時，重新載入資料\n      this.initializeData();\n    }\n    if (changes['buildingData']) {\n      // 當 buildingData 變更時，重新初始化\n      this.buildings = Object.keys(this.buildingData || {});\n      console.log('buildingData updated:', this.buildingData);\n      this.updateFilteredHouseholds();\n      this.updateSelectedByBuilding();\n    }\n    if (changes['excludedHouseIds']) {\n      // 當排除列表變化時，重新更新顯示\n      this.updateFilteredHouseholds();\n      console.log('Excluded house IDs updated:', this.excludedHouseIds);\n    }\n  }\n  initializeData() {\n    // 優先檢查是否有傳入 buildingData\n    if (this.buildingData && Object.keys(this.buildingData).length > 0) {\n      // 使用傳入的 buildingData\n      this.buildings = Object.keys(this.buildingData);\n      console.log('Component initialized with provided buildingData:', this.buildings);\n      this.updateSelectedByBuilding();\n    } else if (this.buildCaseId) {\n      // 只有在沒有傳入 buildingData 且有建案ID時才呼叫API\n      this.loadBuildingDataFromApi();\n    } else {\n      // 既沒有 buildingData 也沒有 buildCaseId，保持空狀態\n      this.buildings = [];\n      console.log('No buildingData or buildCaseId provided');\n    }\n  }\n  loadBuildingDataFromApi() {\n    if (!this.buildCaseId) return;\n    this.isLoading = true;\n    this.houseCustomService.getDropDown(this.buildCaseId).subscribe({\n      next: response => {\n        console.log('API response:', response);\n        this.buildingData = this.convertApiResponseToBuildingData(response.Entries);\n        this.buildings = Object.keys(this.buildingData);\n        this.updateSelectedByBuilding();\n        this.isLoading = false;\n        this.cdr.detectChanges();\n      },\n      error: error => {\n        console.error('Error loading building data:', error);\n        // API載入失敗時，不使用備援資料，保持空狀態\n        this.buildingData = {};\n        this.buildings = [];\n        this.isLoading = false;\n        this.cdr.detectChanges();\n      }\n    });\n  }\n  convertApiResponseToBuildingData(entries) {\n    const buildingData = {};\n    Object.entries(entries).forEach(([building, houses]) => {\n      buildingData[building] = houses.map(house => ({\n        houseName: house.houseName || house.HouseName || house.code,\n        building: house.building || house.Building || building,\n        floor: house.floor || house.Floor,\n        houseId: house.houseId || house.HouseId,\n        isSelected: false,\n        isDisabled: false\n      }));\n    });\n    return buildingData;\n  }\n  updateSelectedByBuilding() {\n    const grouped = {};\n    this.selectedHouseIds.forEach(houseId => {\n      for (const building of this.buildings) {\n        const item = this.buildingData[building]?.find(h => h.houseId === houseId);\n        if (item) {\n          if (!grouped[building]) grouped[building] = [];\n          grouped[building].push(houseId);\n          break;\n        }\n      }\n    });\n    this.selectedByBuilding = grouped;\n  }\n  onBuildingSelect(building) {\n    console.log('Building selected:', building);\n    this.selectedBuilding = building;\n    this.selectedFloor = ''; // 重置樓層選擇\n    this.searchTerm = '';\n    this.updateFloorsForBuilding(); // 更新樓層列表\n    this.updateFilteredHouseholds();\n    console.log('Filtered households count:', this.filteredHouseholds.length);\n    // 手動觸發變更偵測\n    this.cdr.detectChanges();\n  }\n  onBuildingClick(building) {\n    console.log('Building clicked (mousedown):', building);\n  }\n  updateFilteredHouseholds() {\n    console.log('Updating filtered households for building:', this.selectedBuilding, 'floor:', this.selectedFloor);\n    if (!this.selectedBuilding) {\n      this.filteredHouseholds = [];\n      return;\n    }\n    const households = this.buildingData[this.selectedBuilding] || [];\n    console.log('Available households for building:', households.length);\n    // 提取戶別代碼並進行搜尋和樓層過濾\n    const filteredItems = households.filter(h => {\n      // 樓層篩選：如果有選擇樓層，只顯示該樓層的戶別\n      const floorMatch = !this.selectedFloor || h.floor === this.selectedFloor;\n      // 搜尋篩選：戶別代碼包含搜尋詞\n      const searchMatch = !this.searchTerm || h.houseName.toLowerCase().includes(this.searchTerm.toLowerCase());\n      return floorMatch && searchMatch;\n    });\n    // 只提取戶別名稱用於UI顯示（但onSelectAllFiltered會直接使用物件）\n    this.filteredHouseholds = filteredItems.map(h => h.houseName);\n    console.log('Filtered households result:', this.filteredHouseholds.length);\n    console.log('Filtered items details:', filteredItems.map(h => `${h.houseName}-${h.floor} (ID:${h.houseId})`));\n  }\n  onSearchChange(event) {\n    this.searchTerm = event.target.value;\n    this.updateFilteredHouseholds();\n    console.log('Search term changed:', this.searchTerm);\n  }\n  resetSearch() {\n    this.searchTerm = '';\n    this.updateFilteredHouseholds();\n    console.log('Search reset');\n  }\n  onHouseholdToggle(houseId) {\n    console.log('onHouseholdToggle called with houseId:', houseId);\n    console.log('Current selectedHouseIds:', this.selectedHouseIds);\n    if (!houseId) {\n      console.log(`無效的 houseId: ${houseId}`);\n      return;\n    }\n    // 防止選擇已排除的戶別\n    if (this.isHouseIdExcluded(houseId)) {\n      console.log(`戶別 ID ${houseId} 已被其他元件選擇，無法重複選擇`);\n      return;\n    }\n    const isSelected = this.isHouseIdSelected(houseId);\n    let newSelection;\n    if (isSelected) {\n      newSelection = this.selectedHouseIds.filter(id => id !== houseId);\n    } else {\n      if (this.maxSelections && this.selectedHouseIds.length >= this.maxSelections) {\n        console.log('已達到最大選擇數量');\n        return; // 達到最大選擇數量\n      }\n      newSelection = [...this.selectedHouseIds, houseId];\n    }\n    this.selectedHouseIds = newSelection;\n    this.emitChanges();\n  }\n  onRemoveHousehold(houseId) {\n    this.selectedHouseIds = this.selectedHouseIds.filter(id => id !== houseId);\n    this.emitChanges();\n  }\n  onSelectAllFiltered() {\n    console.log('onSelectAllFiltered called');\n    console.log('selectedBuilding:', this.selectedBuilding);\n    console.log('selectedFloor:', this.selectedFloor);\n    console.log('searchTerm:', this.searchTerm);\n    if (!this.selectedBuilding) {\n      console.log('No building selected');\n      return;\n    }\n    const households = this.buildingData[this.selectedBuilding] || [];\n    console.log('Available households for building:', households.length);\n    // 直接過濾戶別物件，而不是使用 filteredHouseholds 字串陣列\n    const filteredHouseholdItems = households.filter(h => {\n      // 樓層篩選：如果有選擇樓層，只顯示該樓層的戶別\n      const floorMatch = !this.selectedFloor || h.floor === this.selectedFloor;\n      // 搜尋篩選：戶別代碼包含搜尋詞\n      const searchMatch = !this.searchTerm || h.houseName.toLowerCase().includes(this.searchTerm.toLowerCase());\n      return floorMatch && searchMatch;\n    });\n    console.log('Filtered household items:', filteredHouseholdItems.map(h => `${h.houseName}-${h.floor} (ID:${h.houseId})`));\n    if (filteredHouseholdItems.length === 0) {\n      console.log('No filtered households found');\n      return;\n    }\n    // 計算可以新增的戶別數量\n    const currentCount = this.selectedHouseIds.length;\n    const maxAllowed = this.maxSelections || Infinity;\n    const remainingSlots = maxAllowed - currentCount;\n    // 取得尚未選擇且未被排除的過濾戶別ID\n    const unselectedFilteredIds = [];\n    for (const household of filteredHouseholdItems) {\n      if (household.houseId && !this.isHouseIdSelected(household.houseId) && !this.isHouseIdExcluded(household.houseId)) {\n        unselectedFilteredIds.push(household.houseId);\n      }\n    }\n    console.log('Unselected filtered IDs:', unselectedFilteredIds);\n    // 根據剩餘空間決定要新增的戶別\n    const toAdd = unselectedFilteredIds.slice(0, remainingSlots);\n    if (toAdd.length > 0) {\n      this.selectedHouseIds = [...this.selectedHouseIds, ...toAdd];\n      this.emitChanges();\n      console.log(`全選當前: 新增了 ${toAdd.length} 個戶別，IDs:`, toAdd);\n    } else {\n      console.log('No households to add');\n    }\n  }\n  onSelectAllBuilding() {\n    if (!this.selectedBuilding) return;\n    // 取得當前棟別的所有戶別\n    const buildingHouseholds = this.buildingData[this.selectedBuilding] || [];\n    // 計算可以新增的戶別數量\n    const currentCount = this.selectedHouseIds.length;\n    const maxAllowed = this.maxSelections || Infinity;\n    const remainingSlots = maxAllowed - currentCount;\n    // 取得尚未選擇且未被排除的棟別戶別 ID\n    const unselectedBuildingIds = [];\n    for (const household of buildingHouseholds) {\n      if (household.houseId && !this.selectedHouseIds.includes(household.houseId) && !this.isHouseholdExcluded(household.houseId)) {\n        unselectedBuildingIds.push(household.houseId);\n      }\n    }\n    // 根據剩餘空間決定要新增的戶別\n    const toAdd = unselectedBuildingIds.slice(0, remainingSlots);\n    if (toAdd.length > 0) {\n      this.selectedHouseIds = [...this.selectedHouseIds, ...toAdd];\n      this.emitChanges();\n      console.log(`全選棟別: 新增了 ${toAdd.length} 個戶別`);\n    }\n  }\n  onUnselectAllBuilding() {\n    if (!this.selectedBuilding) return;\n    const buildingHouseholds = this.buildingData[this.selectedBuilding] || [];\n    const buildingHouseIds = buildingHouseholds.map(h => h.houseId).filter(id => id !== undefined);\n    this.selectedHouseIds = this.selectedHouseIds.filter(id => !buildingHouseIds.includes(id));\n    this.emitChanges();\n  }\n  onClearAll() {\n    this.selectedHouseIds = [];\n    this.emitChanges();\n  }\n  emitChanges() {\n    this.updateSelectedByBuilding();\n    console.log('Emitting changes - selectedHouseIds:', this.selectedHouseIds);\n    this.onChange([...this.selectedHouseIds]);\n    this.onTouched();\n    const selectedItems = this.selectedHouseIds.map(houseId => {\n      for (const building of this.buildings) {\n        const item = this.buildingData[building]?.find(h => h.houseId === houseId);\n        if (item) return item;\n      }\n      return null;\n    }).filter(item => item !== null);\n    console.log('Selected items to emit:', selectedItems);\n    this.selectionChange.emit(selectedItems);\n    // 回傳 houseId 陣列\n    const houseIds = selectedItems.map(item => item.houseId).filter(id => id !== undefined);\n    console.log('House IDs to emit:', houseIds);\n    this.houseIdChange.emit(houseIds);\n  }\n  toggleDropdown() {\n    if (!this.disabled) {\n      this.openDialog();\n      console.log('Opening household selection dialog');\n      console.log('Buildings available:', this.buildings);\n    }\n  }\n  openDialog() {\n    this.dialogService.open(this.householdDialog, {\n      context: {},\n      closeOnBackdropClick: false,\n      closeOnEsc: true,\n      autoFocus: false\n    });\n  }\n  closeDropdown() {\n    // 這個方法現在用於關閉對話框\n    // 對話框的關閉將由 NbDialogRef 處理\n  }\n  isHouseholdSelected(houseId) {\n    if (!houseId) return false;\n    return this.selectedHouseIds.includes(houseId);\n  }\n  isHouseholdExcluded(houseId) {\n    if (!houseId) return false;\n    return this.excludedHouseIds.includes(houseId);\n  }\n  isHouseholdDisabled(houseId) {\n    if (!houseId) return true;\n    return this.isHouseholdExcluded(houseId) || !this.canSelectMore() && !this.isHouseholdSelected(houseId);\n  }\n  canSelectMore() {\n    return !this.maxSelections || this.selectedHouseIds.length < this.maxSelections;\n  }\n  isAllBuildingSelected() {\n    if (!this.selectedBuilding) return false;\n    const buildingHouseholds = this.buildingData[this.selectedBuilding].filter(h => !h.isDisabled && h.houseId !== undefined);\n    return buildingHouseholds.length > 0 && buildingHouseholds.every(household => household.houseId && this.selectedHouseIds.includes(household.houseId));\n  }\n  isSomeBuildingSelected() {\n    if (!this.selectedBuilding) return false;\n    const buildingHouseholds = this.buildingData[this.selectedBuilding] || [];\n    return buildingHouseholds.some(household => household.houseId && this.selectedHouseIds.includes(household.houseId));\n  }\n  getSelectedByBuilding() {\n    return this.selectedByBuilding;\n  }\n  getBuildingCount(building) {\n    return this.buildingData[building]?.length || 0;\n  }\n  getSelectedCount() {\n    return this.selectedHouseIds.length;\n  }\n  // 輔助方法：安全地獲取建築物的已選戶別 ID\n  getBuildingSelectedHouseIds(building) {\n    return this.selectedByBuilding[building] || [];\n  }\n  // 輔助方法：檢查建築物是否有已選戶別\n  hasBuildingSelected(building) {\n    return !!(this.selectedByBuilding[building] && this.selectedByBuilding[building].length > 0);\n  }\n  // 新增：更新當前棧別的樓層計數\n  updateFloorsForBuilding() {\n    if (!this.selectedBuilding) {\n      this.floors = [];\n      return;\n    }\n    const households = this.buildingData[this.selectedBuilding] || [];\n    const floorSet = new Set();\n    households.forEach(household => {\n      if (household.floor) {\n        floorSet.add(household.floor);\n      }\n    });\n    // 對樓層進行自然排序\n    this.floors = Array.from(floorSet).sort((a, b) => {\n      const numA = parseInt(a.replace(/[^0-9]/g, ''));\n      const numB = parseInt(b.replace(/[^0-9]/g, ''));\n      return numA - numB;\n    });\n    console.log('Updated floors for building:', this.selectedBuilding, this.floors);\n  }\n  // 新增：樓層選擇處理\n  onFloorSelect(floor) {\n    console.log('Floor selected:', floor);\n    this.selectedFloor = this.selectedFloor === floor ? '' : floor; // 切換選擇\n    this.updateFilteredHouseholds();\n    this.cdr.detectChanges();\n  }\n  // 新增：取得當前棧別的樓層計數\n  getFloorCount(floor) {\n    if (!this.selectedBuilding) return 0;\n    const households = this.buildingData[this.selectedBuilding] || [];\n    return households.filter(h => h.floor === floor).length;\n  }\n  // 新增：取得戶別的樓層資訊\n  getHouseholdFloor(householdCode) {\n    if (!this.selectedBuilding) return '';\n    const households = this.buildingData[this.selectedBuilding] || [];\n    const household = households.find(h => h.houseName === householdCode);\n    return household?.floor || '';\n  }\n  // 新增：取得戶別的完整資訊（包含樓層）\n  getHouseholdInfo(householdCode) {\n    for (const building of this.buildings) {\n      const households = this.buildingData[building] || [];\n      const household = households.find(h => h.houseName === householdCode);\n      if (household) {\n        return {\n          houseName: household.houseName,\n          floor: household.floor || ''\n        };\n      }\n    }\n    return {\n      houseName: householdCode,\n      floor: ''\n    };\n  }\n  // 新增：根據 houseId 取得戶別的完整資訊\n  getHouseholdInfoById(houseId) {\n    for (const building of this.buildings) {\n      const households = this.buildingData[building] || [];\n      const household = households.find(h => h.houseId === houseId);\n      if (household) {\n        return {\n          houseName: household.houseName,\n          floor: household.floor || ''\n        };\n      }\n    }\n    return {\n      houseName: `ID:${houseId}`,\n      floor: ''\n    };\n  }\n  // 新增：檢查搜尋是否有結果\n  hasNoSearchResults() {\n    if (!this.searchTerm || !this.selectedBuilding || !this.buildingData[this.selectedBuilding]) {\n      return false;\n    }\n    const filtered = this.buildingData[this.selectedBuilding].filter(h => {\n      const floorMatch = !this.selectedFloor || h.floor === this.selectedFloor;\n      const searchMatch = h.houseName.toLowerCase().includes(this.searchTerm.toLowerCase());\n      return floorMatch && searchMatch;\n    });\n    return filtered.length === 0;\n  }\n  // 新增：取得過濾後的戶別數量\n  getFilteredHouseholdsCount() {\n    if (!this.selectedBuilding || !this.buildingData[this.selectedBuilding]) {\n      return 0;\n    }\n    const filtered = this.buildingData[this.selectedBuilding].filter(h => {\n      const floorMatch = !this.selectedFloor || h.floor === this.selectedFloor;\n      const searchMatch = !this.searchTerm || h.houseName.toLowerCase().includes(this.searchTerm.toLowerCase());\n      return floorMatch && searchMatch;\n    });\n    return filtered.length;\n  }\n  // 新增：產生戶別的唯一識別符\n  getHouseholdUniqueId(household) {\n    return household.houseId ? household.houseId.toString() : `${household.houseName}_${household.floor}`;\n  }\n  // 新增：輔助方法 - 根據 houseId 查找 HouseholdItem\n  getHouseholdByHouseId(houseId) {\n    for (const building of this.buildings) {\n      const households = this.buildingData[building] || [];\n      const household = households.find(h => h.houseId === houseId);\n      if (household) return household;\n    }\n    return null;\n  }\n  // 新增：輔助方法 - 根據 houseName 查找 houseId\n  getHouseIdByHouseName(houseName) {\n    const matchingHouseholds = [];\n    // 收集所有符合名稱的戶別\n    for (const building of this.buildings) {\n      const households = this.buildingData[building] || [];\n      const matches = households.filter(h => h.houseName === houseName);\n      matches.forEach(household => {\n        matchingHouseholds.push({\n          building,\n          household\n        });\n      });\n    }\n    console.log(`查找 houseName \"${houseName}\" 的結果:`, matchingHouseholds);\n    if (matchingHouseholds.length === 0) {\n      console.warn(`找不到 houseName \"${houseName}\" 對應的戶別`);\n      return null;\n    }\n    if (matchingHouseholds.length > 1) {\n      console.warn(`發現多個同名戶別 \"${houseName}\":`, matchingHouseholds.map(m => `${m.building}-${m.household.floor}`));\n      console.warn(`將返回第一個找到的: ${matchingHouseholds[0].building}-${matchingHouseholds[0].household.floor}`);\n    }\n    const firstMatch = matchingHouseholds[0];\n    return firstMatch.household.houseId || null;\n  }\n  // 新增：輔助方法 - 檢查 houseId 是否被選中\n  isHouseIdSelected(houseId) {\n    return this.selectedHouseIds.includes(houseId);\n  }\n  // 新增：輔助方法 - 檢查 houseId 是否被排除\n  isHouseIdExcluded(houseId) {\n    return this.excludedHouseIds.includes(houseId);\n  }\n  // 新增：從唯一識別符獲取戶別物件\n  getHouseholdFromUniqueId(uniqueId) {\n    for (const building of this.buildings) {\n      const households = this.buildingData[building] || [];\n      const household = households.find(h => this.getHouseholdUniqueId(h) === uniqueId);\n      if (household) return household;\n    }\n    return null;\n  }\n  // 新增：獲取棟別選擇進度百分比\n  getBuildingSelectionProgress(building) {\n    const totalUnits = this.getBuildingCount(building);\n    const selectedUnits = this.getBuildingSelectedHouseIds(building).length;\n    return totalUnits > 0 ? selectedUnits / totalUnits * 100 : 0;\n  }\n  // 新增：檢查棟別是否全部選中\n  isBuildingFullySelected(building) {\n    const totalUnits = this.getBuildingCount(building);\n    const selectedUnits = this.getBuildingSelectedHouseIds(building).length;\n    return totalUnits > 0 && selectedUnits === totalUnits;\n  }\n  // 新增：獲取棟別選擇狀態文字\n  getBuildingSelectionStatus(building) {\n    const totalUnits = this.getBuildingCount(building);\n    const selectedUnits = this.getBuildingSelectedHouseIds(building).length;\n    if (selectedUnits === 0) {\n      return '未選擇';\n    } else if (selectedUnits === totalUnits) {\n      return '全部選中';\n    } else {\n      return `${selectedUnits}/${totalUnits}`;\n    }\n  }\n  // 新增：獲取棟別的可用戶別數量（排除被其他元件選擇的）\n  getBuildingAvailableCount(building) {\n    const households = this.buildingData[building] || [];\n    return households.filter(h => h.houseId && !this.isHouseIdExcluded(h.houseId)).length;\n  }\n  static {\n    this.ɵfac = function HouseholdBindingComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || HouseholdBindingComponent)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i1.HouseCustomService), i0.ɵɵdirectiveInject(i2.NbDialogService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: HouseholdBindingComponent,\n      selectors: [[\"app-household-binding\"]],\n      viewQuery: function HouseholdBindingComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.householdDialog = _t.first);\n        }\n      },\n      inputs: {\n        placeholder: \"placeholder\",\n        maxSelections: \"maxSelections\",\n        disabled: \"disabled\",\n        buildCaseId: \"buildCaseId\",\n        buildingData: \"buildingData\",\n        showSelectedArea: \"showSelectedArea\",\n        allowSearch: \"allowSearch\",\n        allowBatchSelect: \"allowBatchSelect\",\n        excludedHouseIds: \"excludedHouseIds\"\n      },\n      outputs: {\n        selectionChange: \"selectionChange\",\n        houseIdChange: \"houseIdChange\"\n      },\n      features: [i0.ɵɵProvidersFeature([{\n        provide: NG_VALUE_ACCESSOR,\n        useExisting: forwardRef(() => HouseholdBindingComponent),\n        multi: true\n      }]), i0.ɵɵNgOnChangesFeature],\n      decls: 10,\n      vars: 6,\n      consts: [[\"householdDialog\", \"\"], [1, \"household-binding-container\"], [\"class\", \"selected-households-area\", 4, \"ngIf\"], [1, \"selector-container\"], [\"type\", \"button\", 1, \"selector-button\", 2, \"width\", \"100%\", \"display\", \"flex\", \"align-items\", \"center\", \"justify-content\", \"space-between\", \"padding\", \"0.5rem 0.75rem\", \"border\", \"1px solid #ced4da\", \"border-radius\", \"0.375rem\", \"background-color\", \"#fff\", \"cursor\", \"pointer\", 3, \"click\", \"disabled\"], [1, \"selector-text\"], [4, \"ngIf\"], [\"icon\", \"home-outline\", 1, \"chevron-icon\"], [1, \"selected-households-area\"], [1, \"selected-header\"], [1, \"selected-info\"], [\"icon\", \"people-outline\", 1, \"text-primary\"], [1, \"selected-count\"], [\"type\", \"button\", 1, \"btn\", \"btn-outline-danger\", \"btn-sm\", 3, \"click\", \"disabled\"], [1, \"selected-content\"], [\"class\", \"building-group\", 4, \"ngFor\", \"ngForOf\"], [1, \"building-group\"], [1, \"building-label\"], [1, \"households-tags\"], [\"class\", \"household-tag\", 4, \"ngFor\", \"ngForOf\"], [1, \"household-tag\"], [1, \"household-info\"], [1, \"household-code\"], [\"class\", \"household-floor\", 4, \"ngIf\"], [\"type\", \"button\", 1, \"remove-btn\", 3, \"click\", \"disabled\"], [\"icon\", \"close-outline\"], [1, \"household-floor\"], [\"icon\", \"loader-outline\", 1, \"spin\"], [2, \"width\", \"95vw\", \"max-width\", \"1200px\", \"max-height\", \"90vh\"], [2, \"display\", \"flex\", \"align-items\", \"center\", \"justify-content\", \"space-between\"], [2, \"display\", \"flex\", \"align-items\", \"center\", \"gap\", \"8px\"], [\"icon\", \"home-outline\", 2, \"color\", \"#007bff\", \"font-size\", \"1.5rem\"], [2, \"font-weight\", \"500\", \"color\", \"#495057\", \"font-size\", \"1.25rem\"], [2, \"font-size\", \"0.875rem\", \"color\", \"#6c757d\"], [2, \"padding\", \"0\", \"overflow\", \"hidden\"], [\"style\", \"width: 100%; display: flex; align-items: center; justify-content: center; padding: 40px;\", 4, \"ngIf\"], [\"style\", \"display: flex; height: 60vh; min-height: 400px;\", 4, \"ngIf\"], [2, \"padding\", \"16px\", \"border-top\", \"1px solid #e9ecef\", \"background-color\", \"#f8f9fa\"], [2, \"display\", \"flex\", \"align-items\", \"center\", \"justify-content\", \"space-between\", \"margin-bottom\", \"12px\"], [2, \"display\", \"flex\", \"align-items\", \"center\", \"gap\", \"16px\", \"font-size\", \"0.875rem\", \"color\", \"#495057\"], [2, \"display\", \"flex\", \"align-items\", \"center\", \"gap\", \"4px\"], [\"icon\", \"checkmark-circle-outline\", 2, \"color\", \"#28a745\"], [\"style\", \"display: flex; align-items: center; gap: 4px;\", 4, \"ngIf\"], [\"style\", \"display: flex; align-items: center; gap: 8px;\", 4, \"ngIf\"], [\"style\", \"font-size: 0.75rem; color: #6c757d; background-color: #e9ecef; padding: 4px 8px; border-radius: 4px;\", 4, \"ngIf\"], [2, \"display\", \"flex\", \"align-items\", \"center\", \"justify-content\", \"space-between\", \"gap\", \"8px\"], [2, \"display\", \"flex\", \"gap\", \"8px\"], [\"type\", \"button\", \"style\", \"padding: 8px 12px; background: #dc3545; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 0.875rem; display: flex; align-items: center; gap: 4px;\", 3, \"click\", 4, \"ngIf\"], [\"type\", \"button\", \"style\", \"padding: 8px 12px; background: #6c757d; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 0.875rem; display: flex; align-items: center; gap: 4px;\", 3, \"click\", 4, \"ngIf\"], [\"type\", \"button\", 2, \"padding\", \"8px 16px\", \"background\", \"#6c757d\", \"color\", \"white\", \"border\", \"none\", \"border-radius\", \"4px\", \"cursor\", \"pointer\", \"font-size\", \"0.875rem\", 3, \"click\"], [\"type\", \"button\", 2, \"padding\", \"8px 20px\", \"background\", \"#007bff\", \"color\", \"white\", \"border\", \"none\", \"border-radius\", \"4px\", \"cursor\", \"pointer\", \"font-size\", \"0.875rem\", \"font-weight\", \"500\", \"display\", \"flex\", \"align-items\", \"center\", \"gap\", \"4px\", 3, \"click\"], [\"icon\", \"checkmark-outline\"], [2, \"width\", \"100%\", \"display\", \"flex\", \"align-items\", \"center\", \"justify-content\", \"center\", \"padding\", \"40px\"], [2, \"text-align\", \"center\", \"color\", \"#6c757d\"], [\"icon\", \"loader-outline\", 1, \"spin\", 2, \"font-size\", \"2rem\", \"margin-bottom\", \"8px\"], [2, \"margin\", \"0\", \"font-size\", \"0.875rem\"], [2, \"display\", \"flex\", \"height\", \"60vh\", \"min-height\", \"400px\"], [1, \"building-sidebar\"], [1, \"building-sidebar-header\"], [1, \"header-content\"], [\"icon\", \"layers-outline\", 1, \"header-icon\"], [1, \"header-title\"], [1, \"building-count-badge\"], [1, \"building-list-container\"], [\"class\", \"building-card\", 3, \"selected\", \"has-selection\", \"click\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"empty-state\", 4, \"ngIf\"], [2, \"flex\", \"1\", \"display\", \"flex\", \"flex-direction\", \"column\"], [2, \"padding\", \"12px 16px\", \"border-bottom\", \"1px solid #e9ecef\"], [2, \"display\", \"flex\", \"align-items\", \"center\", \"justify-content\", \"space-between\", \"margin-bottom\", \"8px\"], [2, \"margin\", \"0\", \"font-size\", \"0.875rem\", \"font-weight\", \"600\", \"color\", \"#495057\"], [\"style\", \"background-color: #007bff; color: white; padding: 3px 8px; border-radius: 3px; font-size: 0.75rem; font-weight: 600;\", 4, \"ngIf\"], [\"style\", \"background-color: #28a745; color: white; padding: 3px 8px; border-radius: 3px; font-size: 0.75rem; font-weight: 600;\", 4, \"ngIf\"], [\"style\", \"display: flex; gap: 4px;\", 4, \"ngIf\"], [\"style\", \"margin-top: 8px;\", 4, \"ngIf\"], [\"style\", \"margin-top: 12px;\", 4, \"ngIf\"], [2, \"flex\", \"1\", \"padding\", \"16px\", \"overflow-y\", \"auto\"], [\"style\", \"text-align: center; padding: 40px 20px; color: #6c757d;\", 4, \"ngIf\"], [\"style\", \"display: grid; grid-template-columns: repeat(auto-fill, minmax(90px, 1fr)); gap: 8px;\", 4, \"ngIf\"], [1, \"building-card\", 3, \"click\"], [1, \"building-header\"], [1, \"building-name-section\"], [\"icon\", \"business-outline\", 1, \"building-icon\"], [1, \"building-name\"], [1, \"building-badge-section\"], [1, \"total-units-badge\"], [\"class\", \"building-progress\", 4, \"ngIf\"], [\"class\", \"selection-indicator\", 4, \"ngIf\"], [\"class\", \"selected-units-indicator\", 4, \"ngIf\"], [1, \"building-progress\"], [1, \"progress-bar\"], [1, \"progress-fill\"], [1, \"progress-text\"], [1, \"status-text\"], [\"class\", \"available-count\", 4, \"ngIf\"], [1, \"available-count\"], [1, \"selection-indicator\"], [\"icon\", \"checkmark-circle-2-outline\"], [1, \"selected-units-indicator\"], [1, \"selected-units-count\"], [1, \"empty-state\"], [\"icon\", \"home-outline\", 1, \"empty-icon\"], [1, \"empty-text\"], [2, \"background-color\", \"#007bff\", \"color\", \"white\", \"padding\", \"3px 8px\", \"border-radius\", \"3px\", \"font-size\", \"0.75rem\", \"font-weight\", \"600\"], [\"icon\", \"home-outline\", 2, \"margin-right\", \"4px\", \"font-size\", \"0.7rem\"], [2, \"background-color\", \"#28a745\", \"color\", \"white\", \"padding\", \"3px 8px\", \"border-radius\", \"3px\", \"font-size\", \"0.75rem\", \"font-weight\", \"600\"], [\"icon\", \"layers-outline\", 2, \"margin-right\", \"4px\", \"font-size\", \"0.7rem\"], [2, \"display\", \"flex\", \"gap\", \"4px\"], [\"type\", \"button\", 2, \"padding\", \"4px 8px\", \"font-size\", \"0.75rem\", \"background\", \"#007bff\", \"color\", \"white\", \"border\", \"none\", \"border-radius\", \"4px\", \"cursor\", \"pointer\", 3, \"click\", \"disabled\"], [\"type\", \"button\", 2, \"padding\", \"4px 8px\", \"font-size\", \"0.75rem\", \"background\", \"#28a745\", \"color\", \"white\", \"border\", \"none\", \"border-radius\", \"4px\", \"cursor\", \"pointer\", 3, \"click\", \"disabled\"], [\"type\", \"button\", \"style\", \"padding: 4px 8px; font-size: 0.75rem; background: #6c757d; color: white; border: none; border-radius: 4px; cursor: pointer;\", 3, \"click\", 4, \"ngIf\"], [\"type\", \"button\", 2, \"padding\", \"4px 8px\", \"font-size\", \"0.75rem\", \"background\", \"#6c757d\", \"color\", \"white\", \"border\", \"none\", \"border-radius\", \"4px\", \"cursor\", \"pointer\", 3, \"click\"], [2, \"margin-top\", \"8px\"], [2, \"position\", \"relative\"], [\"type\", \"text\", \"placeholder\", \"\\u641C\\u5C0B\\u6236\\u5225\\u4EE3\\u78BC...\", 2, \"width\", \"100%\", \"padding\", \"6px 32px 6px 12px\", \"border\", \"1px solid #ced4da\", \"border-radius\", \"4px\", \"font-size\", \"0.875rem\", \"outline\", \"none\", 3, \"ngModelChange\", \"input\", \"ngModel\"], [\"icon\", \"search-outline\", 2, \"position\", \"absolute\", \"right\", \"10px\", \"top\", \"50%\", \"transform\", \"translateY(-50%)\", \"color\", \"#6c757d\", \"font-size\", \"0.875rem\"], [\"style\", \"font-size: 0.75rem; color: #dc3545; margin-top: 4px;\", 4, \"ngIf\"], [2, \"font-size\", \"0.75rem\", \"color\", \"#dc3545\", \"margin-top\", \"4px\"], [2, \"margin-top\", \"12px\"], [2, \"display\", \"flex\", \"align-items\", \"center\", \"gap\", \"8px\", \"margin-bottom\", \"8px\"], [\"icon\", \"layers-outline\", 2, \"color\", \"#6c757d\", \"font-size\", \"1rem\"], [2, \"font-size\", \"0.875rem\", \"font-weight\", \"600\", \"color\", \"#495057\"], [\"type\", \"button\", \"style\", \"font-size: 0.75rem; color: #007bff; background: none; border: none; text-decoration: underline; cursor: pointer;\", 3, \"click\", 4, \"ngIf\"], [2, \"display\", \"flex\", \"flex-wrap\", \"wrap\", \"gap\", \"4px\", \"max-height\", \"100px\", \"overflow-y\", \"auto\"], [\"type\", \"button\", \"style\", \"padding: 6px 10px; border-radius: 3px; font-size: 0.75rem; font-weight: 500; cursor: pointer; transition: all 0.15s ease; white-space: nowrap;\", 3, \"background-color\", \"color\", \"border\", \"click\", 4, \"ngFor\", \"ngForOf\"], [\"type\", \"button\", 2, \"font-size\", \"0.75rem\", \"color\", \"#007bff\", \"background\", \"none\", \"border\", \"none\", \"text-decoration\", \"underline\", \"cursor\", \"pointer\", 3, \"click\"], [\"type\", \"button\", 2, \"padding\", \"6px 10px\", \"border-radius\", \"3px\", \"font-size\", \"0.75rem\", \"font-weight\", \"500\", \"cursor\", \"pointer\", \"transition\", \"all 0.15s ease\", \"white-space\", \"nowrap\", 3, \"click\"], [\"icon\", \"layers-outline\", 2, \"margin-right\", \"3px\", \"font-size\", \"0.7rem\"], [2, \"font-size\", \"0.7rem\", \"opacity\", \"0.7\"], [2, \"text-align\", \"center\", \"padding\", \"40px 20px\", \"color\", \"#6c757d\"], [\"icon\", \"home-outline\", 2, \"font-size\", \"2rem\", \"margin-bottom\", \"8px\", \"opacity\", \"0.5\"], [2, \"display\", \"grid\", \"grid-template-columns\", \"repeat(auto-fill, minmax(90px, 1fr))\", \"gap\", \"8px\"], [4, \"ngFor\", \"ngForOf\"], [\"type\", \"button\", \"style\", \"padding: 8px 6px; border-radius: 4px; transition: all 0.15s ease; font-size: 0.75rem; text-align: center; min-height: 55px; position: relative; display: flex; flex-direction: column; justify-content: center; align-items: center; gap: 3px;\", 3, \"disabled\", \"background-color\", \"color\", \"border\", \"opacity\", \"cursor\", \"click\", 4, \"ngIf\"], [\"type\", \"button\", 2, \"padding\", \"8px 6px\", \"border-radius\", \"4px\", \"transition\", \"all 0.15s ease\", \"font-size\", \"0.75rem\", \"text-align\", \"center\", \"min-height\", \"55px\", \"position\", \"relative\", \"display\", \"flex\", \"flex-direction\", \"column\", \"justify-content\", \"center\", \"align-items\", \"center\", \"gap\", \"3px\", 3, \"click\", \"disabled\"], [2, \"font-weight\", \"600\", \"line-height\", \"1.2\", \"font-size\", \"0.85rem\"], [\"style\", \"font-size: 0.7rem; font-weight: 600; padding: 2px 6px; border-radius: 3px; display: inline-flex; align-items: center; justify-content: center; min-width: 22px;\", 3, \"background-color\", \"color\", \"border\", 4, \"ngIf\"], [\"style\", \"position: absolute; top: -8px; right: -8px; background: #dc3545; color: white; border-radius: 50%; width: 16px; height: 16px; font-size: 10px; display: flex; align-items: center; justify-content: center;\", 4, \"ngIf\"], [2, \"font-size\", \"0.7rem\", \"font-weight\", \"600\", \"padding\", \"2px 6px\", \"border-radius\", \"3px\", \"display\", \"inline-flex\", \"align-items\", \"center\", \"justify-content\", \"center\", \"min-width\", \"22px\"], [\"icon\", \"layers-outline\", 2, \"margin-right\", \"2px\", \"font-size\", \"0.6rem\"], [2, \"position\", \"absolute\", \"top\", \"-8px\", \"right\", \"-8px\", \"background\", \"#dc3545\", \"color\", \"white\", \"border-radius\", \"50%\", \"width\", \"16px\", \"height\", \"16px\", \"font-size\", \"10px\", \"display\", \"flex\", \"align-items\", \"center\", \"justify-content\", \"center\"], [\"icon\", \"alert-circle-outline\", 2, \"font-size\", \"2rem\", \"margin-bottom\", \"8px\", \"opacity\", \"0.5\"], [\"icon\", \"alert-circle-outline\", 2, \"color\", \"#ffc107\"], [\"icon\", \"home-outline\", 2, \"color\", \"#007bff\"], [2, \"background-color\", \"#007bff\", \"color\", \"white\", \"padding\", \"4px 8px\", \"border-radius\", \"4px\", \"font-size\", \"0.75rem\", \"font-weight\", \"600\"], [\"style\", \"background-color: #28a745; color: white; padding: 4px 8px; border-radius: 4px; font-size: 0.75rem; font-weight: 600; margin-left: 4px;\", 4, \"ngIf\"], [2, \"background-color\", \"#28a745\", \"color\", \"white\", \"padding\", \"4px 8px\", \"border-radius\", \"4px\", \"font-size\", \"0.75rem\", \"font-weight\", \"600\", \"margin-left\", \"4px\"], [2, \"font-size\", \"0.75rem\", \"color\", \"#6c757d\", \"background-color\", \"#e9ecef\", \"padding\", \"4px 8px\", \"border-radius\", \"4px\"], [\"type\", \"button\", 2, \"padding\", \"8px 12px\", \"background\", \"#dc3545\", \"color\", \"white\", \"border\", \"none\", \"border-radius\", \"4px\", \"cursor\", \"pointer\", \"font-size\", \"0.875rem\", \"display\", \"flex\", \"align-items\", \"center\", \"gap\", \"4px\", 3, \"click\"], [\"icon\", \"trash-2-outline\"], [\"type\", \"button\", 2, \"padding\", \"8px 12px\", \"background\", \"#6c757d\", \"color\", \"white\", \"border\", \"none\", \"border-radius\", \"4px\", \"cursor\", \"pointer\", \"font-size\", \"0.875rem\", \"display\", \"flex\", \"align-items\", \"center\", \"gap\", \"4px\", 3, \"click\"], [\"icon\", \"refresh-outline\"]],\n      template: function HouseholdBindingComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"div\", 1);\n          i0.ɵɵtemplate(1, HouseholdBindingComponent_div_1_Template, 10, 3, \"div\", 2);\n          i0.ɵɵelementStart(2, \"div\", 3)(3, \"button\", 4);\n          i0.ɵɵlistener(\"click\", function HouseholdBindingComponent_Template_button_click_3_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.toggleDropdown());\n          });\n          i0.ɵɵelementStart(4, \"span\", 5);\n          i0.ɵɵtemplate(5, HouseholdBindingComponent_ng_container_5_Template, 3, 0, \"ng-container\", 6)(6, HouseholdBindingComponent_ng_container_6_Template, 2, 1, \"ng-container\", 6);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(7, \"nb-icon\", 7);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(8, HouseholdBindingComponent_ng_template_8_Template, 37, 11, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor);\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.showSelectedArea && ctx.selectedHouseIds.length > 0);\n          i0.ɵɵadvance(2);\n          i0.ɵɵclassProp(\"disabled\", ctx.disabled || ctx.isLoading);\n          i0.ɵɵproperty(\"disabled\", ctx.disabled || ctx.isLoading);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading);\n        }\n      },\n      dependencies: [i3.NgForOf, i3.NgIf, i4.DefaultValueAccessor, i4.NgControlStatus, i4.NgModel, i2.NbCardComponent, i2.NbCardBodyComponent, i2.NbCardFooterComponent, i2.NbCardHeaderComponent, i2.NbIconComponent],\n      styles: [\".household-binding-container[_ngcontent-%COMP%] {\\n  position: relative;\\n  width: 100%;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .spin[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_spin 1s linear infinite;\\n}\\n@keyframes _ngcontent-%COMP%_spin {\\n  from {\\n    transform: rotate(0deg);\\n  }\\n  to {\\n    transform: rotate(360deg);\\n  }\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%] {\\n  margin-bottom: 1rem;\\n  padding: 1rem;\\n  background-color: #f8f9fa;\\n  border: 1px solid #e9ecef;\\n  border-radius: 0.375rem;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%]   .selected-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 0.75rem;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%]   .selected-header[_ngcontent-%COMP%]   .selected-info[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.5rem;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%]   .selected-header[_ngcontent-%COMP%]   .selected-info[_ngcontent-%COMP%]   .selected-count[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  font-weight: 500;\\n  color: #495057;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%]   .selected-content[_ngcontent-%COMP%]   .building-group[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: flex-start;\\n  gap: 0.5rem;\\n  margin-bottom: 0.5rem;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%]   .selected-content[_ngcontent-%COMP%]   .building-group[_ngcontent-%COMP%]:last-child {\\n  margin-bottom: 0;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%]   .selected-content[_ngcontent-%COMP%]   .building-group[_ngcontent-%COMP%]   .building-label[_ngcontent-%COMP%] {\\n  min-width: 80px;\\n  font-weight: 500;\\n  color: #495057;\\n  font-size: 0.875rem;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%]   .selected-content[_ngcontent-%COMP%]   .building-group[_ngcontent-%COMP%]   .households-tags[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-wrap: wrap;\\n  gap: 0.25rem;\\n  flex: 1;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%]   .selected-content[_ngcontent-%COMP%]   .building-group[_ngcontent-%COMP%]   .households-tags[_ngcontent-%COMP%]   .household-tag[_ngcontent-%COMP%] {\\n  display: inline-flex;\\n  align-items: center;\\n  gap: 0.375rem;\\n  padding: 0.375rem 0.75rem;\\n  background-color: #e3f2fd;\\n  color: #1976d2;\\n  font-size: 0.75rem;\\n  border-radius: 0.25rem;\\n  border: 1px solid #bbdefb;\\n  transition: all 0.2s ease;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%]   .selected-content[_ngcontent-%COMP%]   .building-group[_ngcontent-%COMP%]   .households-tags[_ngcontent-%COMP%]   .household-tag[_ngcontent-%COMP%]:hover {\\n  background-color: #bbdefb;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%]   .selected-content[_ngcontent-%COMP%]   .building-group[_ngcontent-%COMP%]   .households-tags[_ngcontent-%COMP%]   .household-tag[_ngcontent-%COMP%]   .household-info[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: flex-start;\\n  gap: 0.1rem;\\n  line-height: 1.2;\\n  min-width: 0;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%]   .selected-content[_ngcontent-%COMP%]   .building-group[_ngcontent-%COMP%]   .households-tags[_ngcontent-%COMP%]   .household-tag[_ngcontent-%COMP%]   .household-info[_ngcontent-%COMP%]   .household-code[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  color: #0d47a1;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%]   .selected-content[_ngcontent-%COMP%]   .building-group[_ngcontent-%COMP%]   .households-tags[_ngcontent-%COMP%]   .household-tag[_ngcontent-%COMP%]   .household-info[_ngcontent-%COMP%]   .household-floor[_ngcontent-%COMP%] {\\n  font-size: 0.7rem;\\n  font-weight: 600;\\n  color: #ffffff;\\n  background-color: #28a745;\\n  padding: 0.15rem 0.4rem;\\n  border-radius: 0.25rem;\\n  min-width: fit-content;\\n  text-align: center;\\n  letter-spacing: 0.02em;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%]   .selected-content[_ngcontent-%COMP%]   .building-group[_ngcontent-%COMP%]   .households-tags[_ngcontent-%COMP%]   .household-tag[_ngcontent-%COMP%]   .remove-btn[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.9);\\n  border: 1px solid #90caf9;\\n  padding: 0.1rem;\\n  margin: 0;\\n  cursor: pointer;\\n  color: #0d47a1;\\n  border-radius: 0.25rem;\\n  width: 1.2rem;\\n  height: 1.2rem;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  transition: all 0.2s ease;\\n  flex-shrink: 0;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%]   .selected-content[_ngcontent-%COMP%]   .building-group[_ngcontent-%COMP%]   .households-tags[_ngcontent-%COMP%]   .household-tag[_ngcontent-%COMP%]   .remove-btn[_ngcontent-%COMP%]:hover {\\n  background-color: #f44336;\\n  border-color: #f44336;\\n  color: white;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%]   .selected-content[_ngcontent-%COMP%]   .building-group[_ngcontent-%COMP%]   .households-tags[_ngcontent-%COMP%]   .household-tag[_ngcontent-%COMP%]   .remove-btn[_ngcontent-%COMP%]:disabled {\\n  opacity: 0.5;\\n  cursor: not-allowed;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%]   .selected-content[_ngcontent-%COMP%]   .building-group[_ngcontent-%COMP%]   .households-tags[_ngcontent-%COMP%]   .household-tag[_ngcontent-%COMP%]   .remove-btn[_ngcontent-%COMP%]:disabled:hover {\\n  background-color: rgba(255, 255, 255, 0.9);\\n  border-color: #90caf9;\\n  color: #0d47a1;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%]   .selected-content[_ngcontent-%COMP%]   .building-group[_ngcontent-%COMP%]   .households-tags[_ngcontent-%COMP%]   .household-tag[_ngcontent-%COMP%]   .remove-btn[_ngcontent-%COMP%]   nb-icon[_ngcontent-%COMP%] {\\n  font-size: 0.75rem;\\n  line-height: 1;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .building-sidebar[_ngcontent-%COMP%] {\\n  width: 320px;\\n  border-right: 1px solid #e9ecef;\\n  background: linear-gradient(135deg, #f8f9fa 0%, #f1f3f5 100%);\\n  display: flex;\\n  flex-direction: column;\\n  box-shadow: inset -1px 0 0 rgba(0, 0, 0, 0.05);\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .building-sidebar[_ngcontent-%COMP%]   .building-sidebar-header[_ngcontent-%COMP%] {\\n  padding: 16px;\\n  border-bottom: 1px solid #e9ecef;\\n  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);\\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.02);\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .building-sidebar[_ngcontent-%COMP%]   .building-sidebar-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .building-sidebar[_ngcontent-%COMP%]   .building-sidebar-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]   .header-icon[_ngcontent-%COMP%] {\\n  color: #007bff;\\n  font-size: 1.2rem;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .building-sidebar[_ngcontent-%COMP%]   .building-sidebar-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]   .header-title[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 0.9rem;\\n  font-weight: 600;\\n  color: #2c3e50;\\n  flex: 1;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .building-sidebar[_ngcontent-%COMP%]   .building-sidebar-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]   .building-count-badge[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);\\n  color: white;\\n  padding: 4px 8px;\\n  border-radius: 12px;\\n  font-size: 0.7rem;\\n  font-weight: 600;\\n  box-shadow: 0 2px 4px rgba(0, 123, 255, 0.3);\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .building-sidebar[_ngcontent-%COMP%]   .building-list-container[_ngcontent-%COMP%] {\\n  flex: 1;\\n  overflow-y: auto;\\n  padding: 8px;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .building-sidebar[_ngcontent-%COMP%]   .building-list-container[_ngcontent-%COMP%]   .building-card[_ngcontent-%COMP%] {\\n  position: relative;\\n  margin-bottom: 8px;\\n  padding: 14px;\\n  background: #ffffff;\\n  border: 2px solid #e9ecef;\\n  border-radius: 8px;\\n  cursor: pointer;\\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .building-sidebar[_ngcontent-%COMP%]   .building-list-container[_ngcontent-%COMP%]   .building-card[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-1px);\\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\\n  border-color: #007bff;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .building-sidebar[_ngcontent-%COMP%]   .building-list-container[_ngcontent-%COMP%]   .building-card.selected[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);\\n  border-color: #007bff;\\n  box-shadow: 0 4px 16px rgba(0, 123, 255, 0.25);\\n  transform: translateY(-2px);\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .building-sidebar[_ngcontent-%COMP%]   .building-list-container[_ngcontent-%COMP%]   .building-card.selected[_ngcontent-%COMP%]   .building-name[_ngcontent-%COMP%] {\\n  color: #0d47a1;\\n  font-weight: 700;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .building-sidebar[_ngcontent-%COMP%]   .building-list-container[_ngcontent-%COMP%]   .building-card.selected[_ngcontent-%COMP%]   .building-icon[_ngcontent-%COMP%] {\\n  color: #007bff;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .building-sidebar[_ngcontent-%COMP%]   .building-list-container[_ngcontent-%COMP%]   .building-card.has-selection[_ngcontent-%COMP%] {\\n  border-left: 4px solid #28a745;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .building-sidebar[_ngcontent-%COMP%]   .building-list-container[_ngcontent-%COMP%]   .building-card.has-selection[_ngcontent-%COMP%]   .selected-units-indicator[_ngcontent-%COMP%] {\\n  display: flex;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .building-sidebar[_ngcontent-%COMP%]   .building-list-container[_ngcontent-%COMP%]   .building-card[_ngcontent-%COMP%]   .building-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between;\\n  margin-bottom: 10px;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .building-sidebar[_ngcontent-%COMP%]   .building-list-container[_ngcontent-%COMP%]   .building-card[_ngcontent-%COMP%]   .building-header[_ngcontent-%COMP%]   .building-name-section[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .building-sidebar[_ngcontent-%COMP%]   .building-list-container[_ngcontent-%COMP%]   .building-card[_ngcontent-%COMP%]   .building-header[_ngcontent-%COMP%]   .building-name-section[_ngcontent-%COMP%]   .building-icon[_ngcontent-%COMP%] {\\n  color: #6c757d;\\n  font-size: 1.1rem;\\n  transition: all 0.3s ease;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .building-sidebar[_ngcontent-%COMP%]   .building-list-container[_ngcontent-%COMP%]   .building-card[_ngcontent-%COMP%]   .building-header[_ngcontent-%COMP%]   .building-name-section[_ngcontent-%COMP%]   .building-name[_ngcontent-%COMP%] {\\n  font-size: 1rem;\\n  font-weight: 600;\\n  color: #2c3e50;\\n  transition: all 0.3s ease;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .building-sidebar[_ngcontent-%COMP%]   .building-list-container[_ngcontent-%COMP%]   .building-card[_ngcontent-%COMP%]   .building-header[_ngcontent-%COMP%]   .building-badge-section[_ngcontent-%COMP%]   .total-units-badge[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #6c757d 0%, #495057 100%);\\n  color: white;\\n  padding: 4px 8px;\\n  border-radius: 10px;\\n  font-size: 0.7rem;\\n  font-weight: 600;\\n  box-shadow: 0 2px 4px rgba(108, 117, 125, 0.3);\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .building-sidebar[_ngcontent-%COMP%]   .building-list-container[_ngcontent-%COMP%]   .building-card[_ngcontent-%COMP%]   .building-progress[_ngcontent-%COMP%] {\\n  margin-bottom: 8px;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .building-sidebar[_ngcontent-%COMP%]   .building-list-container[_ngcontent-%COMP%]   .building-card[_ngcontent-%COMP%]   .building-progress[_ngcontent-%COMP%]   .progress-bar[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 6px;\\n  background-color: #e9ecef;\\n  border-radius: 3px;\\n  overflow: hidden;\\n  margin-bottom: 6px;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .building-sidebar[_ngcontent-%COMP%]   .building-list-container[_ngcontent-%COMP%]   .building-card[_ngcontent-%COMP%]   .building-progress[_ngcontent-%COMP%]   .progress-bar[_ngcontent-%COMP%]   .progress-fill[_ngcontent-%COMP%] {\\n  height: 100%;\\n  background: linear-gradient(90deg, #28a745 0%, #20c997 100%);\\n  border-radius: 3px;\\n  transition: width 0.4s ease;\\n  box-shadow: 0 1px 2px rgba(40, 167, 69, 0.3);\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .building-sidebar[_ngcontent-%COMP%]   .building-list-container[_ngcontent-%COMP%]   .building-card[_ngcontent-%COMP%]   .building-progress[_ngcontent-%COMP%]   .progress-bar[_ngcontent-%COMP%]   .progress-fill.full-selection[_ngcontent-%COMP%] {\\n  background: linear-gradient(90deg, #007bff 0%, #0056b3 100%);\\n  box-shadow: 0 1px 2px rgba(0, 123, 255, 0.3);\\n  animation: completePulse 2s infinite;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .building-sidebar[_ngcontent-%COMP%]   .building-list-container[_ngcontent-%COMP%]   .building-card[_ngcontent-%COMP%]   .building-progress[_ngcontent-%COMP%]   .progress-text[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .building-sidebar[_ngcontent-%COMP%]   .building-list-container[_ngcontent-%COMP%]   .building-card[_ngcontent-%COMP%]   .building-progress[_ngcontent-%COMP%]   .progress-text[_ngcontent-%COMP%]   .status-text[_ngcontent-%COMP%] {\\n  font-size: 0.75rem;\\n  font-weight: 600;\\n  color: #28a745;\\n  transition: all 0.3s ease;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .building-sidebar[_ngcontent-%COMP%]   .building-list-container[_ngcontent-%COMP%]   .building-card[_ngcontent-%COMP%]   .building-progress[_ngcontent-%COMP%]   .progress-text[_ngcontent-%COMP%]   .status-text.fully-selected[_ngcontent-%COMP%] {\\n  color: #007bff;\\n  animation: textGlow 2s infinite;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .building-sidebar[_ngcontent-%COMP%]   .building-list-container[_ngcontent-%COMP%]   .building-card[_ngcontent-%COMP%]   .building-progress[_ngcontent-%COMP%]   .progress-text[_ngcontent-%COMP%]   .status-text.no-selection[_ngcontent-%COMP%] {\\n  color: #6c757d;\\n  font-style: italic;\\n  font-weight: 500;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .building-sidebar[_ngcontent-%COMP%]   .building-list-container[_ngcontent-%COMP%]   .building-card[_ngcontent-%COMP%]   .building-progress[_ngcontent-%COMP%]   .progress-text[_ngcontent-%COMP%]   .available-count[_ngcontent-%COMP%] {\\n  font-size: 0.7rem;\\n  color: #ffc107;\\n  font-weight: 600;\\n  background: rgba(255, 193, 7, 0.1);\\n  padding: 2px 6px;\\n  border-radius: 8px;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .building-sidebar[_ngcontent-%COMP%]   .building-list-container[_ngcontent-%COMP%]   .building-card[_ngcontent-%COMP%]   .selection-indicator[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: -2px;\\n  right: -2px;\\n  background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);\\n  color: white;\\n  border-radius: 50%;\\n  width: 24px;\\n  height: 24px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  box-shadow: 0 2px 6px rgba(0, 123, 255, 0.4);\\n  animation: _ngcontent-%COMP%_pulseSelection 2s infinite;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .building-sidebar[_ngcontent-%COMP%]   .building-list-container[_ngcontent-%COMP%]   .building-card[_ngcontent-%COMP%]   .selection-indicator[_ngcontent-%COMP%]   nb-icon[_ngcontent-%COMP%] {\\n  font-size: 0.9rem;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .building-sidebar[_ngcontent-%COMP%]   .building-list-container[_ngcontent-%COMP%]   .building-card[_ngcontent-%COMP%]   .selected-units-indicator[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: -6px;\\n  left: -6px;\\n  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);\\n  color: white;\\n  border-radius: 50%;\\n  width: 22px;\\n  height: 22px;\\n  display: none;\\n  align-items: center;\\n  justify-content: center;\\n  box-shadow: 0 2px 6px rgba(40, 167, 69, 0.4);\\n  animation: _ngcontent-%COMP%_bounceIn 0.5s ease;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .building-sidebar[_ngcontent-%COMP%]   .building-list-container[_ngcontent-%COMP%]   .building-card[_ngcontent-%COMP%]   .selected-units-indicator[_ngcontent-%COMP%]   .selected-units-count[_ngcontent-%COMP%] {\\n  font-size: 0.7rem;\\n  font-weight: 700;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .building-sidebar[_ngcontent-%COMP%]   .building-list-container[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%] {\\n  text-align: center;\\n  padding: 40px 20px;\\n  color: #6c757d;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .building-sidebar[_ngcontent-%COMP%]   .building-list-container[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%]   .empty-icon[_ngcontent-%COMP%] {\\n  font-size: 2.5rem;\\n  margin-bottom: 12px;\\n  opacity: 0.4;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .building-sidebar[_ngcontent-%COMP%]   .building-list-container[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%]   .empty-text[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 0.875rem;\\n  font-style: italic;\\n}\\n@keyframes _ngcontent-%COMP%_pulseSelection {\\n  0% {\\n    transform: scale(1);\\n    box-shadow: 0 2px 6px rgba(0, 123, 255, 0.4);\\n  }\\n  50% {\\n    transform: scale(1.1);\\n    box-shadow: 0 4px 12px rgba(0, 123, 255, 0.6);\\n  }\\n  100% {\\n    transform: scale(1);\\n    box-shadow: 0 2px 6px rgba(0, 123, 255, 0.4);\\n  }\\n}\\n@keyframes _ngcontent-%COMP%_bounceIn {\\n  0% {\\n    transform: scale(0);\\n    opacity: 0;\\n  }\\n  50% {\\n    transform: scale(1.2);\\n    opacity: 1;\\n  }\\n  100% {\\n    transform: scale(1);\\n    opacity: 1;\\n  }\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%] {\\n  position: relative;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .selector-button[_ngcontent-%COMP%] {\\n  width: 100%;\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between;\\n  padding: 0.5rem 0.75rem;\\n  border: 1px solid #ced4da;\\n  border-radius: 0.375rem;\\n  background-color: #fff;\\n  cursor: pointer;\\n  transition: all 0.15s ease;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .selector-button[_ngcontent-%COMP%]:hover:not(.disabled) {\\n  border-color: #007bff;\\n  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .selector-button[_ngcontent-%COMP%]:focus {\\n  outline: none;\\n  border-color: #80bdff;\\n  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .selector-button.disabled[_ngcontent-%COMP%] {\\n  opacity: 0.6;\\n  cursor: not-allowed !important;\\n  background-color: #e9ecef;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .selector-button[_ngcontent-%COMP%]   .selector-text[_ngcontent-%COMP%] {\\n  color: #495057;\\n  font-size: 0.875rem;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .selector-button[_ngcontent-%COMP%]   .chevron-icon[_ngcontent-%COMP%] {\\n  transition: transform 0.15s ease;\\n  color: #6c757d;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .selector-button[_ngcontent-%COMP%]   .chevron-icon.rotated[_ngcontent-%COMP%] {\\n  transform: rotate(180deg);\\n}\\n\\n.flat-badge[_ngcontent-%COMP%] {\\n  padding: 4px 8px;\\n  border-radius: 4px;\\n  font-size: 0.75rem;\\n  font-weight: 600;\\n  display: inline-flex;\\n  align-items: center;\\n  gap: 4px;\\n}\\n.flat-badge.building[_ngcontent-%COMP%] {\\n  background-color: #007bff;\\n  color: white;\\n}\\n.flat-badge.floor[_ngcontent-%COMP%] {\\n  background-color: #28a745;\\n  color: white;\\n}\\n.flat-badge.search[_ngcontent-%COMP%] {\\n  background-color: #e9ecef;\\n  color: #6c757d;\\n}\\n.flat-badge.selected[_ngcontent-%COMP%] {\\n  background-color: #e3f2fd;\\n  color: #1976d2;\\n  border: 1px solid #bbdefb;\\n}\\n\\n.flat-household-tag[_ngcontent-%COMP%] {\\n  display: inline-flex;\\n  align-items: center;\\n  gap: 0.375rem;\\n  padding: 0.375rem 0.75rem;\\n  background-color: #e3f2fd;\\n  color: #1976d2;\\n  font-size: 0.75rem;\\n  border-radius: 4px;\\n  border: 1px solid #bbdefb;\\n  transition: background-color 0.2s ease;\\n}\\n.flat-household-tag[_ngcontent-%COMP%]:hover {\\n  background-color: #bbdefb;\\n}\\n.flat-household-tag[_ngcontent-%COMP%]   .household-info[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: flex-start;\\n  gap: 0.1rem;\\n  line-height: 1.2;\\n  min-width: 0;\\n}\\n.flat-household-tag[_ngcontent-%COMP%]   .household-info[_ngcontent-%COMP%]   .household-code[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  color: #0d47a1;\\n}\\n.flat-household-tag[_ngcontent-%COMP%]   .household-info[_ngcontent-%COMP%]   .household-floor[_ngcontent-%COMP%] {\\n  font-size: 0.7rem;\\n  font-weight: 600;\\n  color: #ffffff;\\n  background-color: #28a745;\\n  padding: 0.15rem 0.4rem;\\n  border-radius: 4px;\\n  min-width: fit-content;\\n  text-align: center;\\n  letter-spacing: 0.02em;\\n}\\n.flat-household-tag[_ngcontent-%COMP%]   .remove-btn[_ngcontent-%COMP%] {\\n  background: #ffffff;\\n  border: 1px solid #90caf9;\\n  padding: 0.1rem;\\n  margin: 0;\\n  cursor: pointer;\\n  color: #0d47a1;\\n  border-radius: 4px;\\n  width: 1.2rem;\\n  height: 1.2rem;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  transition: all 0.2s ease;\\n  flex-shrink: 0;\\n}\\n.flat-household-tag[_ngcontent-%COMP%]   .remove-btn[_ngcontent-%COMP%]:hover {\\n  background-color: #f44336;\\n  border-color: #f44336;\\n  color: white;\\n}\\n.flat-household-tag[_ngcontent-%COMP%]   .remove-btn[_ngcontent-%COMP%]:disabled {\\n  opacity: 0.5;\\n  cursor: not-allowed;\\n}\\n.flat-household-tag[_ngcontent-%COMP%]   .remove-btn[_ngcontent-%COMP%]:disabled:hover {\\n  background-color: #ffffff;\\n  border-color: #90caf9;\\n  color: #0d47a1;\\n}\\n.flat-household-tag[_ngcontent-%COMP%]   .remove-btn[_ngcontent-%COMP%]   nb-icon[_ngcontent-%COMP%] {\\n  font-size: 0.75rem;\\n  line-height: 1;\\n}\\n\\n.flat-button[_ngcontent-%COMP%] {\\n  border: 1px solid;\\n  border-radius: 4px;\\n  transition: all 0.2s ease;\\n  cursor: pointer;\\n}\\n.flat-button.primary[_ngcontent-%COMP%] {\\n  background-color: #007bff;\\n  color: white;\\n  border-color: #007bff;\\n}\\n.flat-button.primary[_ngcontent-%COMP%]:hover {\\n  background-color: #0056b3;\\n  border-color: #0056b3;\\n}\\n.flat-button.primary.selected[_ngcontent-%COMP%] {\\n  background-color: #0056b3;\\n  border-color: #0056b3;\\n}\\n.flat-button.success[_ngcontent-%COMP%] {\\n  background-color: #28a745;\\n  color: white;\\n  border-color: #28a745;\\n}\\n.flat-button.success[_ngcontent-%COMP%]:hover {\\n  background-color: #1e7e34;\\n  border-color: #1e7e34;\\n}\\n.flat-button.success.selected[_ngcontent-%COMP%] {\\n  background-color: #1e7e34;\\n  border-color: #1e7e34;\\n}\\n.flat-button.light[_ngcontent-%COMP%] {\\n  background-color: #f8f9fa;\\n  color: #495057;\\n  border-color: #ced4da;\\n}\\n.flat-button.light[_ngcontent-%COMP%]:hover {\\n  background-color: #e9ecef;\\n  border-color: #adb5bd;\\n}\\n.flat-button.light.selected[_ngcontent-%COMP%] {\\n  background-color: #007bff;\\n  color: white;\\n  border-color: #007bff;\\n}\\n.flat-button[_ngcontent-%COMP%]:disabled {\\n  opacity: 0.6;\\n  cursor: not-allowed;\\n}\\n.flat-button[_ngcontent-%COMP%]:disabled:hover {\\n  opacity: 0.6;\\n}\\n\\n@media (max-width: 768px) {\\n  .household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%]   .selected-content[_ngcontent-%COMP%]   .building-group[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    align-items: flex-start;\\n    gap: 0.25rem;\\n  }\\n  .household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%]   .selected-content[_ngcontent-%COMP%]   .building-group[_ngcontent-%COMP%]   .building-label[_ngcontent-%COMP%] {\\n    min-width: auto;\\n  }\\n  .household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%]   .selected-content[_ngcontent-%COMP%]   .building-group[_ngcontent-%COMP%]   .households-tags[_ngcontent-%COMP%] {\\n    width: 100%;\\n  }\\n  .household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%]   .selected-content[_ngcontent-%COMP%]   .building-group[_ngcontent-%COMP%]   .households-tags[_ngcontent-%COMP%]   .household-tag[_ngcontent-%COMP%]   .household-info[_ngcontent-%COMP%]   .household-floor[_ngcontent-%COMP%] {\\n    font-size: 0.6rem;\\n  }\\n  .household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .selector-button[_ngcontent-%COMP%] {\\n    padding: 0.75rem;\\n  }\\n  .household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .selector-button[_ngcontent-%COMP%]   .selector-text[_ngcontent-%COMP%] {\\n    font-size: 1rem;\\n  }\\n}\\n@media (prefers-color-scheme: dark) {\\n  .household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%] {\\n    background-color: #343a40;\\n    border-color: #495057;\\n    color: #f8f9fa;\\n  }\\n  .household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%]   .selected-header[_ngcontent-%COMP%]   .selected-info[_ngcontent-%COMP%]   .selected-count[_ngcontent-%COMP%] {\\n    color: #f8f9fa;\\n  }\\n  .household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%]   .selected-content[_ngcontent-%COMP%]   .building-group[_ngcontent-%COMP%]   .building-label[_ngcontent-%COMP%] {\\n    color: #adb5bd;\\n  }\\n  .household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%]   .selected-content[_ngcontent-%COMP%]   .building-group[_ngcontent-%COMP%]   .households-tags[_ngcontent-%COMP%]   .household-tag[_ngcontent-%COMP%] {\\n    background-color: #495057;\\n    color: #bbdefb;\\n    border-color: #6c757d;\\n  }\\n  .household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%]   .selected-content[_ngcontent-%COMP%]   .building-group[_ngcontent-%COMP%]   .households-tags[_ngcontent-%COMP%]   .household-tag[_ngcontent-%COMP%]:hover {\\n    background-color: #5a6268;\\n  }\\n  .household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%]   .selected-content[_ngcontent-%COMP%]   .building-group[_ngcontent-%COMP%]   .households-tags[_ngcontent-%COMP%]   .household-tag[_ngcontent-%COMP%]   .household-info[_ngcontent-%COMP%]   .household-code[_ngcontent-%COMP%] {\\n    color: #bbdefb;\\n  }\\n  .household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%]   .selected-content[_ngcontent-%COMP%]   .building-group[_ngcontent-%COMP%]   .households-tags[_ngcontent-%COMP%]   .household-tag[_ngcontent-%COMP%]   .household-info[_ngcontent-%COMP%]   .household-floor[_ngcontent-%COMP%] {\\n    color: #90caf9;\\n    background-color: rgba(0, 0, 0, 0.2);\\n  }\\n  .household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%]   .selected-content[_ngcontent-%COMP%]   .building-group[_ngcontent-%COMP%]   .households-tags[_ngcontent-%COMP%]   .household-tag[_ngcontent-%COMP%]   .remove-btn[_ngcontent-%COMP%] {\\n    background: rgba(0, 0, 0, 0.3);\\n    border-color: #6c757d;\\n    color: #bbdefb;\\n  }\\n  .household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%]   .selected-content[_ngcontent-%COMP%]   .building-group[_ngcontent-%COMP%]   .households-tags[_ngcontent-%COMP%]   .household-tag[_ngcontent-%COMP%]   .remove-btn[_ngcontent-%COMP%]:hover {\\n    background-color: #f44336;\\n    border-color: #f44336;\\n    color: white;\\n  }\\n  .household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .selector-button[_ngcontent-%COMP%] {\\n    background-color: #495057;\\n    border-color: #6c757d;\\n    color: #f8f9fa;\\n  }\\n  .household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .selector-button[_ngcontent-%COMP%]:hover:not(.disabled) {\\n    background-color: #5a6268;\\n  }\\n  .household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .selector-button.disabled[_ngcontent-%COMP%] {\\n    background-color: #6c757d;\\n  }\\n  .household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .selector-button[_ngcontent-%COMP%]   .selector-text[_ngcontent-%COMP%] {\\n    color: #f8f9fa;\\n  }\\n  .household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .selector-button[_ngcontent-%COMP%]   .chevron-icon[_ngcontent-%COMP%] {\\n    color: #adb5bd;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;base64,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 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["EventEmitter", "forwardRef", "NG_VALUE_ACCESSOR", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate1", "ctx_r2", "getHouseholdInfoById", "houseId_r5", "floor", "ɵɵtemplate", "HouseholdBindingComponent_div_1_div_9_ng_container_1_span_4_span_4_Template", "ɵɵlistener", "HouseholdBindingComponent_div_1_div_9_ng_container_1_span_4_Template_button_click_5_listener", "ɵɵrestoreView", "_r4", "$implicit", "ɵɵnextContext", "ɵɵresetView", "onRemoveHousehold", "ɵɵelement", "ɵɵtextInterpolate", "houseName", "ɵɵproperty", "disabled", "ɵɵelementContainerStart", "HouseholdBindingComponent_div_1_div_9_ng_container_1_span_4_Template", "building_r6", "getBuildingSelectedHouseIds", "HouseholdBindingComponent_div_1_div_9_ng_container_1_Template", "hasBuildingSelected", "HouseholdBindingComponent_div_1_Template_button_click_6_listener", "_r2", "onClearAll", "HouseholdBindingComponent_div_1_div_9_Template", "getSelectedCount", "buildings", "placeholder", "getBuildingAvailableCount", "building_r9", "HouseholdBindingComponent_ng_template_8_div_13_div_10_div_9_span_6_Template", "ɵɵstyleProp", "getBuildingSelectionProgress", "ɵɵclassProp", "isBuildingFullySelected", "length", "getBuildingSelectionStatus", "getBuildingCount", "HouseholdBindingComponent_ng_template_8_div_13_div_10_Template_div_click_0_listener", "_r8", "onBuildingSelect", "HouseholdBindingComponent_ng_template_8_div_13_div_10_div_9_Template", "HouseholdBindingComponent_ng_template_8_div_13_div_10_div_10_Template", "HouseholdBindingComponent_ng_template_8_div_13_div_10_div_11_Template", "selectedBuilding", "selectedF<PERSON>or", "HouseholdBindingComponent_ng_template_8_div_13_div_20_button_5_Template_button_click_0_listener", "_r11", "onUnselectAllBuilding", "HouseholdBindingComponent_ng_template_8_div_13_div_20_Template_button_click_1_listener", "_r10", "onSelectAllFiltered", "HouseholdBindingComponent_ng_template_8_div_13_div_20_Template_button_click_3_listener", "onSelectAllBuilding", "HouseholdBindingComponent_ng_template_8_div_13_div_20_button_5_Template", "canSelectMore", "isSomeBuildingSelected", "searchTerm", "ɵɵtwoWayListener", "HouseholdBindingComponent_ng_template_8_div_13_div_21_Template_input_ngModelChange_2_listener", "$event", "_r12", "ɵɵtwoWayBindingSet", "HouseholdBindingComponent_ng_template_8_div_13_div_21_Template_input_input_2_listener", "onSearchChange", "HouseholdBindingComponent_ng_template_8_div_13_div_21_div_4_Template", "ɵɵtwoWayProperty", "hasNoSearchResults", "HouseholdBindingComponent_ng_template_8_div_13_div_22_button_6_Template_button_click_0_listener", "_r13", "onFloorSelect", "HouseholdBindingComponent_ng_template_8_div_13_div_22_button_8_Template_button_click_0_listener", "floor_r15", "_r14", "getFloorCount", "HouseholdBindingComponent_ng_template_8_div_13_div_22_span_5_Template", "HouseholdBindingComponent_ng_template_8_div_13_div_22_button_6_Template", "HouseholdBindingComponent_ng_template_8_div_13_div_22_button_8_Template", "floors", "isHouseholdSelected", "household_r17", "houseId", "HouseholdBindingComponent_ng_template_8_div_13_div_25_ng_container_1_button_1_Template_button_click_0_listener", "_r16", "onHouseholdToggle", "HouseholdBindingComponent_ng_template_8_div_13_div_25_ng_container_1_button_1_span_3_Template", "HouseholdBindingComponent_ng_template_8_div_13_div_25_ng_container_1_button_1_div_4_Template", "isHouseholdExcluded", "isHouseholdDisabled", "HouseholdBindingComponent_ng_template_8_div_13_div_25_ng_container_1_button_1_Template", "toLowerCase", "includes", "HouseholdBindingComponent_ng_template_8_div_13_div_25_ng_container_1_Template", "buildingData", "HouseholdBindingComponent_ng_template_8_div_13_div_10_Template", "HouseholdBindingComponent_ng_template_8_div_13_div_11_Template", "HouseholdBindingComponent_ng_template_8_div_13_span_18_Template", "HouseholdBindingComponent_ng_template_8_div_13_span_19_Template", "HouseholdBindingComponent_ng_template_8_div_13_div_20_Template", "HouseholdBindingComponent_ng_template_8_div_13_div_21_Template", "HouseholdBindingComponent_ng_template_8_div_13_div_22_Template", "HouseholdBindingComponent_ng_template_8_div_13_div_24_Template", "HouseholdBindingComponent_ng_template_8_div_13_div_25_Template", "HouseholdBindingComponent_ng_template_8_div_13_div_26_Template", "allowBatchSelect", "allowSearch", "maxSelections", "HouseholdBindingComponent_ng_template_8_div_25_span_6_Template", "ɵɵtextInterpolate2", "getFilteredHouseholdsCount", "HouseholdBindingComponent_ng_template_8_button_29_Template_button_click_0_listener", "_r18", "HouseholdBindingComponent_ng_template_8_button_30_Template_button_click_0_listener", "_r19", "resetSearch", "HouseholdBindingComponent_ng_template_8_div_12_Template", "HouseholdBindingComponent_ng_template_8_div_13_Template", "HouseholdBindingComponent_ng_template_8_div_24_Template", "HouseholdBindingComponent_ng_template_8_div_25_Template", "HouseholdBindingComponent_ng_template_8_div_26_Template", "HouseholdBindingComponent_ng_template_8_button_29_Template", "HouseholdBindingComponent_ng_template_8_button_30_Template", "HouseholdBindingComponent_ng_template_8_Template_button_click_32_listener", "ref_r20", "_r7", "dialogRef", "close", "HouseholdBindingComponent_ng_template_8_Template_button_click_34_listener", "isLoading", "selectedHouseIds", "HouseholdBindingComponent", "constructor", "cdr", "houseCustomService", "dialogService", "buildCaseId", "showSelectedArea", "excludedHouseIds", "selectionChange", "houseIdChange", "isOpen", "filteredHouseholds", "selectedByBuilding", "onChange", "value", "onTouched", "writeValue", "console", "log", "firstItem", "error", "updateSelectedByBuilding", "registerOnChange", "fn", "registerOnTouched", "setDisabledState", "isDisabled", "ngOnInit", "initializeData", "ngOnChanges", "changes", "Object", "keys", "updateFilteredHouseholds", "loadBuildingDataFromApi", "getDropDown", "subscribe", "next", "response", "convertApiResponseToBuildingData", "Entries", "detectChanges", "entries", "for<PERSON>ach", "building", "houses", "map", "house", "HouseName", "code", "Building", "Floor", "HouseId", "isSelected", "grouped", "item", "find", "h", "push", "updateFloorsForBuilding", "onBuildingClick", "households", "filteredItems", "filter", "floorMatch", "searchMatch", "event", "target", "isHouseIdExcluded", "isHouseIdSelected", "newSelection", "id", "emitChanges", "filteredHouseholdItems", "currentCount", "maxAllowed", "Infinity", "remainingSlots", "unselectedFilteredIds", "household", "toAdd", "slice", "buildingHouseholds", "unselectedBuildingIds", "buildingHouseIds", "undefined", "selectedItems", "emit", "houseIds", "toggleDropdown", "openDialog", "open", "householdDialog", "context", "closeOnBackdropClick", "closeOnEsc", "autoFocus", "closeDropdown", "isAllBuildingSelected", "every", "some", "getSelectedByBuilding", "floorSet", "Set", "add", "Array", "from", "sort", "a", "b", "numA", "parseInt", "replace", "numB", "getHouseholdFloor", "householdCode", "getHouseholdInfo", "filtered", "getHouseholdUniqueId", "toString", "getHouseholdByHouseId", "getHouseIdByHouseName", "matchingHouseholds", "matches", "warn", "m", "firstMatch", "getHouseholdFromUniqueId", "uniqueId", "totalUnits", "selected<PERSON><PERSON><PERSON>", "ɵɵdirectiveInject", "ChangeDetectorRef", "i1", "HouseCustomService", "i2", "NbDialogService", "selectors", "viewQuery", "HouseholdBindingComponent_Query", "rf", "ctx", "provide", "useExisting", "multi", "ɵɵNgOnChangesFeature", "decls", "vars", "consts", "template", "HouseholdBindingComponent_Template", "HouseholdBindingComponent_div_1_Template", "HouseholdBindingComponent_Template_button_click_3_listener", "_r1", "HouseholdBindingComponent_ng_container_5_Template", "HouseholdBindingComponent_ng_container_6_Template", "HouseholdBindingComponent_ng_template_8_Template", "ɵɵtemplateRefExtractor"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\shared\\components\\household-binding\\household-binding.component.ts", "C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\shared\\components\\household-binding\\household-binding.component.html"], "sourcesContent": ["import { Component, Input, Output, EventEmitter, OnInit, OnChanges, SimpleChanges, forwardRef, ChangeDetectorRef, TemplateRef, ViewChild } from '@angular/core';\r\nimport { ControlValueAccessor, NG_VALUE_ACCESSOR } from '@angular/forms';\r\nimport { NbDialogService } from '@nebular/theme';\r\nimport { HouseCustomService } from '../../../../services/api/services/HouseCustom.service';\r\nimport { HouseItem } from '../../../../services/api/models/house.model';\r\n\r\nexport interface HouseholdItem {\r\n  houseName: string;\r\n  building: string;\r\n  floor?: string;\r\n  houseId?: number;\r\n  isSelected?: boolean;\r\n  isDisabled?: boolean;\r\n}\r\n\r\nexport interface BuildingData {\r\n  [key: string]: HouseholdItem[];\r\n}\r\n\r\n@Component({\r\n  selector: 'app-household-binding',\r\n  templateUrl: './household-binding.component.html',\r\n  styleUrls: ['./household-binding.component.scss'],\r\n  providers: [\r\n    {\r\n      provide: NG_VALUE_ACCESSOR,\r\n      useExisting: forwardRef(() => HouseholdBindingComponent),\r\n      multi: true,\r\n    },\r\n  ],\r\n})\r\nexport class HouseholdBindingComponent implements OnInit, OnChanges, ControlValueAccessor {\r\n  @ViewChild('householdDialog', { static: false }) householdDialog!: TemplateRef<any>;\r\n\r\n  @Input() placeholder: string = '請選擇戶別';\r\n  @Input() maxSelections: number | null = null;\r\n  @Input() disabled: boolean = false;\r\n  @Input() buildCaseId: number | null = null; // 新增：建案ID\r\n  @Input() buildingData: BuildingData = {}; @Input() showSelectedArea: boolean = true;\r\n  @Input() allowSearch: boolean = true;\r\n  @Input() allowBatchSelect: boolean = true;\r\n  @Input() excludedHouseIds: number[] = []; // 改為：排除的戶別ID（已被其他元件選擇）\r\n\r\n  @Output() selectionChange = new EventEmitter<HouseholdItem[]>();\r\n  @Output() houseIdChange = new EventEmitter<number[]>(); // 新增：回傳 houseId 陣列\r\n  isOpen = false;\r\n  selectedBuilding = '';\r\n  searchTerm = '';\r\n  selectedFloor = ''; // 新增：選中的樓層\r\n  selectedHouseIds: number[] = []; // 改為：使用 houseId 作為選擇的key\r\n  buildings: string[] = [];\r\n  floors: string[] = []; // 新增：當前棧別的樓層列表\r\n  filteredHouseholds: string[] = [];  // 保持為字串陣列用於UI顯示\r\n  selectedByBuilding: { [building: string]: number[] } = {}; // 改為：儲存 houseId\r\n  isLoading: boolean = false; // 新增：載入狀態\r\n  // ControlValueAccessor implementation\r\n  private onChange = (value: number[]) => { };\r\n  private onTouched = () => { };\r\n  constructor(\r\n    private cdr: ChangeDetectorRef,\r\n    private houseCustomService: HouseCustomService,\r\n    private dialogService: NbDialogService\r\n  ) { } writeValue(value: any[]): void {\r\n    console.log('writeValue called with:', value, 'type:', typeof value?.[0]);\r\n\r\n    if (!value || value.length === 0) {\r\n      this.selectedHouseIds = [];\r\n    } else {\r\n      // 檢查傳入的是 houseId 還是 houseName\r\n      const firstItem = value[0];\r\n      if (typeof firstItem === 'number') {\r\n        // 如果是數字，直接使用\r\n        this.selectedHouseIds = value as number[];\r\n        console.log('使用傳入的 houseId 陣列');\r\n      } else if (typeof firstItem === 'string') {\r\n        // 如果是字串（houseName），這是一個不推薦的使用方式\r\n        console.error('⚠️ 警告：收到 houseName 陣列而不是 houseId 陣列！');\r\n        console.error('⚠️ 這會導致同名戶別的選擇問題！');\r\n        console.error('⚠️ 建議父元件改用 houseId 陣列:', value);\r\n\r\n        // 暫時跳過轉換，保持當前選擇不變\r\n        console.error('⚠️ 跳過此次 writeValue 以避免錯誤選擇');\r\n        return;\r\n      } else {\r\n        console.error('writeValue 收到未知格式的資料:', value);\r\n        this.selectedHouseIds = [];\r\n      }\r\n    }\r\n\r\n    console.log('selectedHouseIds set to:', this.selectedHouseIds);\r\n    this.updateSelectedByBuilding();\r\n  }\r\n\r\n  registerOnChange(fn: (value: number[]) => void): void {\r\n    this.onChange = fn;\r\n  }\r\n\r\n  registerOnTouched(fn: () => void): void {\r\n    this.onTouched = fn;\r\n  }\r\n  setDisabledState(isDisabled: boolean): void {\r\n    this.disabled = isDisabled;\r\n  } ngOnInit() {\r\n    this.initializeData();\r\n  }\r\n  ngOnChanges(changes: SimpleChanges) {\r\n    if (changes['buildCaseId'] && this.buildCaseId) {\r\n      // 當建案ID變更時，重新載入資料\r\n      this.initializeData();\r\n    }\r\n    if (changes['buildingData']) {\r\n      // 當 buildingData 變更時，重新初始化\r\n      this.buildings = Object.keys(this.buildingData || {});\r\n      console.log('buildingData updated:', this.buildingData);\r\n      this.updateFilteredHouseholds();\r\n      this.updateSelectedByBuilding();\r\n    } if (changes['excludedHouseIds']) {\r\n      // 當排除列表變化時，重新更新顯示\r\n      this.updateFilteredHouseholds();\r\n      console.log('Excluded house IDs updated:', this.excludedHouseIds);\r\n    }\r\n  } private initializeData() {\r\n    // 優先檢查是否有傳入 buildingData\r\n    if (this.buildingData && Object.keys(this.buildingData).length > 0) {\r\n      // 使用傳入的 buildingData\r\n      this.buildings = Object.keys(this.buildingData);\r\n      console.log('Component initialized with provided buildingData:', this.buildings);\r\n      this.updateSelectedByBuilding();\r\n    } else if (this.buildCaseId) {\r\n      // 只有在沒有傳入 buildingData 且有建案ID時才呼叫API\r\n      this.loadBuildingDataFromApi();\r\n    } else {\r\n      // 既沒有 buildingData 也沒有 buildCaseId，保持空狀態\r\n      this.buildings = [];\r\n      console.log('No buildingData or buildCaseId provided');\r\n    }\r\n  }\r\n\r\n  private loadBuildingDataFromApi() {\r\n    if (!this.buildCaseId) return;\r\n\r\n    this.isLoading = true;\r\n    this.houseCustomService.getDropDown(this.buildCaseId).subscribe({\r\n      next: (response) => {\r\n        console.log('API response:', response);\r\n        this.buildingData = this.convertApiResponseToBuildingData(response.Entries);\r\n        this.buildings = Object.keys(this.buildingData);\r\n        this.updateSelectedByBuilding();\r\n        this.isLoading = false;\r\n        this.cdr.detectChanges();\r\n      }, error: (error) => {\r\n        console.error('Error loading building data:', error);\r\n        // API載入失敗時，不使用備援資料，保持空狀態\r\n        this.buildingData = {};\r\n        this.buildings = [];\r\n        this.isLoading = false;\r\n        this.cdr.detectChanges();\r\n      }\r\n    });\r\n  }\r\n  private convertApiResponseToBuildingData(entries: { [key: string]: any[] }): BuildingData {\r\n    const buildingData: BuildingData = {};\r\n\r\n    Object.entries(entries).forEach(([building, houses]) => {\r\n      buildingData[building] = houses.map(house => ({\r\n        houseName: house.houseName || house.HouseName || house.code,\r\n        building: house.building || house.Building || building,\r\n        floor: house.floor || house.Floor,\r\n        houseId: house.houseId || house.HouseId,\r\n        isSelected: false,\r\n        isDisabled: false\r\n      }));\r\n    });\r\n\r\n    return buildingData;\r\n  } private updateSelectedByBuilding() {\r\n    const grouped: { [building: string]: number[] } = {};\r\n\r\n    this.selectedHouseIds.forEach(houseId => {\r\n      for (const building of this.buildings) {\r\n        const item = this.buildingData[building]?.find(h => h.houseId === houseId);\r\n        if (item) {\r\n          if (!grouped[building]) grouped[building] = [];\r\n          grouped[building].push(houseId);\r\n          break;\r\n        }\r\n      }\r\n    });\r\n\r\n    this.selectedByBuilding = grouped;\r\n  } onBuildingSelect(building: string) {\r\n    console.log('Building selected:', building);\r\n    this.selectedBuilding = building;\r\n    this.selectedFloor = ''; // 重置樓層選擇\r\n    this.searchTerm = '';\r\n    this.updateFloorsForBuilding(); // 更新樓層列表\r\n    this.updateFilteredHouseholds();\r\n    console.log('Filtered households count:', this.filteredHouseholds.length);\r\n    // 手動觸發變更偵測\r\n    this.cdr.detectChanges();\r\n  }\r\n\r\n  onBuildingClick(building: string) {\r\n    console.log('Building clicked (mousedown):', building);\r\n  } updateFilteredHouseholds() {\r\n    console.log('Updating filtered households for building:', this.selectedBuilding, 'floor:', this.selectedFloor);\r\n    if (!this.selectedBuilding) {\r\n      this.filteredHouseholds = [];\r\n      return;\r\n    }\r\n\r\n    const households = this.buildingData[this.selectedBuilding] || [];\r\n    console.log('Available households for building:', households.length);\r\n\r\n    // 提取戶別代碼並進行搜尋和樓層過濾\r\n    const filteredItems = households.filter(h => {\r\n      // 樓層篩選：如果有選擇樓層，只顯示該樓層的戶別\r\n      const floorMatch = !this.selectedFloor || h.floor === this.selectedFloor;\r\n      // 搜尋篩選：戶別代碼包含搜尋詞\r\n      const searchMatch = !this.searchTerm || h.houseName.toLowerCase().includes(this.searchTerm.toLowerCase());\r\n      return floorMatch && searchMatch;\r\n    });\r\n\r\n    // 只提取戶別名稱用於UI顯示（但onSelectAllFiltered會直接使用物件）\r\n    this.filteredHouseholds = filteredItems.map(h => h.houseName);\r\n\r\n    console.log('Filtered households result:', this.filteredHouseholds.length);\r\n    console.log('Filtered items details:', filteredItems.map(h => `${h.houseName}-${h.floor} (ID:${h.houseId})`));\r\n  }\r\n\r\n  onSearchChange(event: any) {\r\n    this.searchTerm = event.target.value;\r\n    this.updateFilteredHouseholds();\r\n    console.log('Search term changed:', this.searchTerm);\r\n  }\r\n  resetSearch() {\r\n    this.searchTerm = '';\r\n    this.updateFilteredHouseholds();\r\n    console.log('Search reset');\r\n  }\r\n  onHouseholdToggle(houseId: number | undefined) {\r\n    console.log('onHouseholdToggle called with houseId:', houseId);\r\n    console.log('Current selectedHouseIds:', this.selectedHouseIds);\r\n\r\n    if (!houseId) {\r\n      console.log(`無效的 houseId: ${houseId}`);\r\n      return;\r\n    }\r\n\r\n    // 防止選擇已排除的戶別\r\n    if (this.isHouseIdExcluded(houseId)) {\r\n      console.log(`戶別 ID ${houseId} 已被其他元件選擇，無法重複選擇`);\r\n      return;\r\n    }\r\n\r\n    const isSelected = this.isHouseIdSelected(houseId);\r\n    let newSelection: number[];\r\n\r\n    if (isSelected) {\r\n      newSelection = this.selectedHouseIds.filter(id => id !== houseId);\r\n    } else {\r\n      if (this.maxSelections && this.selectedHouseIds.length >= this.maxSelections) {\r\n        console.log('已達到最大選擇數量');\r\n        return; // 達到最大選擇數量\r\n      }\r\n      newSelection = [...this.selectedHouseIds, houseId];\r\n    }\r\n\r\n    this.selectedHouseIds = newSelection;\r\n    this.emitChanges();\r\n  }\r\n  onRemoveHousehold(houseId: number) {\r\n    this.selectedHouseIds = this.selectedHouseIds.filter(id => id !== houseId);\r\n    this.emitChanges();\r\n  } onSelectAllFiltered() {\r\n    console.log('onSelectAllFiltered called');\r\n    console.log('selectedBuilding:', this.selectedBuilding);\r\n    console.log('selectedFloor:', this.selectedFloor);\r\n    console.log('searchTerm:', this.searchTerm);\r\n\r\n    if (!this.selectedBuilding) {\r\n      console.log('No building selected');\r\n      return;\r\n    }\r\n\r\n    const households = this.buildingData[this.selectedBuilding] || [];\r\n    console.log('Available households for building:', households.length);\r\n\r\n    // 直接過濾戶別物件，而不是使用 filteredHouseholds 字串陣列\r\n    const filteredHouseholdItems = households.filter(h => {\r\n      // 樓層篩選：如果有選擇樓層，只顯示該樓層的戶別\r\n      const floorMatch = !this.selectedFloor || h.floor === this.selectedFloor;\r\n      // 搜尋篩選：戶別代碼包含搜尋詞\r\n      const searchMatch = !this.searchTerm || h.houseName.toLowerCase().includes(this.searchTerm.toLowerCase());\r\n      return floorMatch && searchMatch;\r\n    });\r\n\r\n    console.log('Filtered household items:', filteredHouseholdItems.map(h => `${h.houseName}-${h.floor} (ID:${h.houseId})`));\r\n\r\n    if (filteredHouseholdItems.length === 0) {\r\n      console.log('No filtered households found');\r\n      return;\r\n    }\r\n\r\n    // 計算可以新增的戶別數量\r\n    const currentCount = this.selectedHouseIds.length;\r\n    const maxAllowed = this.maxSelections || Infinity;\r\n    const remainingSlots = maxAllowed - currentCount;\r\n\r\n    // 取得尚未選擇且未被排除的過濾戶別ID\r\n    const unselectedFilteredIds: number[] = [];\r\n    for (const household of filteredHouseholdItems) {\r\n      if (household.houseId &&\r\n        !this.isHouseIdSelected(household.houseId) &&\r\n        !this.isHouseIdExcluded(household.houseId)) {\r\n        unselectedFilteredIds.push(household.houseId);\r\n      }\r\n    }\r\n\r\n    console.log('Unselected filtered IDs:', unselectedFilteredIds);\r\n\r\n    // 根據剩餘空間決定要新增的戶別\r\n    const toAdd = unselectedFilteredIds.slice(0, remainingSlots);\r\n\r\n    if (toAdd.length > 0) {\r\n      this.selectedHouseIds = [...this.selectedHouseIds, ...toAdd];\r\n      this.emitChanges();\r\n      console.log(`全選當前: 新增了 ${toAdd.length} 個戶別，IDs:`, toAdd);\r\n    } else {\r\n      console.log('No households to add');\r\n    }\r\n  } onSelectAllBuilding() {\r\n    if (!this.selectedBuilding) return;\r\n\r\n    // 取得當前棟別的所有戶別\r\n    const buildingHouseholds = this.buildingData[this.selectedBuilding] || [];\r\n\r\n    // 計算可以新增的戶別數量\r\n    const currentCount = this.selectedHouseIds.length;\r\n    const maxAllowed = this.maxSelections || Infinity;\r\n    const remainingSlots = maxAllowed - currentCount;\r\n\r\n    // 取得尚未選擇且未被排除的棟別戶別 ID\r\n    const unselectedBuildingIds: number[] = [];\r\n    for (const household of buildingHouseholds) {\r\n      if (household.houseId &&\r\n        !this.selectedHouseIds.includes(household.houseId) &&\r\n        !this.isHouseholdExcluded(household.houseId)) {\r\n        unselectedBuildingIds.push(household.houseId);\r\n      }\r\n    }\r\n\r\n    // 根據剩餘空間決定要新增的戶別\r\n    const toAdd = unselectedBuildingIds.slice(0, remainingSlots); if (toAdd.length > 0) {\r\n      this.selectedHouseIds = [...this.selectedHouseIds, ...toAdd];\r\n      this.emitChanges();\r\n      console.log(`全選棟別: 新增了 ${toAdd.length} 個戶別`);\r\n    }\r\n  }\r\n\r\n  onUnselectAllBuilding() {\r\n    if (!this.selectedBuilding) return;\r\n\r\n    const buildingHouseholds = this.buildingData[this.selectedBuilding] || [];\r\n    const buildingHouseIds = buildingHouseholds.map(h => h.houseId).filter(id => id !== undefined) as number[];\r\n    this.selectedHouseIds = this.selectedHouseIds.filter(id => !buildingHouseIds.includes(id));\r\n    this.emitChanges();\r\n  }\r\n\r\n  onClearAll() {\r\n    this.selectedHouseIds = [];\r\n    this.emitChanges();\r\n  } private emitChanges() {\r\n    this.updateSelectedByBuilding();\r\n    console.log('Emitting changes - selectedHouseIds:', this.selectedHouseIds);\r\n    this.onChange([...this.selectedHouseIds]);\r\n    this.onTouched();\r\n\r\n    const selectedItems = this.selectedHouseIds.map(houseId => {\r\n      for (const building of this.buildings) {\r\n        const item = this.buildingData[building]?.find(h => h.houseId === houseId);\r\n        if (item) return item;\r\n      }\r\n      return null;\r\n    }).filter(item => item !== null) as HouseholdItem[];\r\n\r\n    console.log('Selected items to emit:', selectedItems);\r\n    this.selectionChange.emit(selectedItems);\r\n\r\n    // 回傳 houseId 陣列\r\n    const houseIds = selectedItems.map(item => item.houseId!).filter(id => id !== undefined);\r\n    console.log('House IDs to emit:', houseIds);\r\n    this.houseIdChange.emit(houseIds);\r\n  } toggleDropdown() {\r\n    if (!this.disabled) {\r\n      this.openDialog();\r\n      console.log('Opening household selection dialog');\r\n      console.log('Buildings available:', this.buildings);\r\n    }\r\n  }\r\n\r\n  openDialog() {\r\n    this.dialogService.open(this.householdDialog, {\r\n      context: {},\r\n      closeOnBackdropClick: false,\r\n      closeOnEsc: true,\r\n      autoFocus: false,\r\n    });\r\n  }\r\n\r\n  closeDropdown() {\r\n    // 這個方法現在用於關閉對話框\r\n    // 對話框的關閉將由 NbDialogRef 處理\r\n  }\r\n  isHouseholdSelected(houseId: number | undefined): boolean {\r\n    if (!houseId) return false;\r\n    return this.selectedHouseIds.includes(houseId);\r\n  }\r\n\r\n  isHouseholdExcluded(houseId: number | undefined): boolean {\r\n    if (!houseId) return false;\r\n    return this.excludedHouseIds.includes(houseId);\r\n  }\r\n\r\n  isHouseholdDisabled(houseId: number | undefined): boolean {\r\n    if (!houseId) return true;\r\n    return this.isHouseholdExcluded(houseId) ||\r\n      (!this.canSelectMore() && !this.isHouseholdSelected(houseId));\r\n  }\r\n  canSelectMore(): boolean {\r\n    return !this.maxSelections || this.selectedHouseIds.length < this.maxSelections;\r\n  } isAllBuildingSelected(): boolean {\r\n    if (!this.selectedBuilding) return false;\r\n    const buildingHouseholds = this.buildingData[this.selectedBuilding]\r\n      .filter(h => !h.isDisabled && h.houseId !== undefined);\r\n    return buildingHouseholds.length > 0 &&\r\n      buildingHouseholds.every(household => household.houseId && this.selectedHouseIds.includes(household.houseId));\r\n  }\r\n\r\n  isSomeBuildingSelected(): boolean {\r\n    if (!this.selectedBuilding) return false;\r\n    const buildingHouseholds = this.buildingData[this.selectedBuilding] || [];\r\n    return buildingHouseholds.some(household => household.houseId && this.selectedHouseIds.includes(household.houseId));\r\n  } getSelectedByBuilding(): { [building: string]: number[] } {\r\n    return this.selectedByBuilding;\r\n  }\r\n\r\n  getBuildingCount(building: string): number {\r\n    return this.buildingData[building]?.length || 0;\r\n  }\r\n\r\n  getSelectedCount(): number {\r\n    return this.selectedHouseIds.length;\r\n  }\r\n\r\n  // 輔助方法：安全地獲取建築物的已選戶別 ID\r\n  getBuildingSelectedHouseIds(building: string): number[] {\r\n    return this.selectedByBuilding[building] || [];\r\n  }\r\n\r\n  // 輔助方法：檢查建築物是否有已選戶別\r\n  hasBuildingSelected(building: string): boolean {\r\n    return !!(this.selectedByBuilding[building] && this.selectedByBuilding[building].length > 0);\r\n  }\r\n\r\n  // 新增：更新當前棧別的樓層計數\r\n  private updateFloorsForBuilding() {\r\n    if (!this.selectedBuilding) {\r\n      this.floors = [];\r\n      return;\r\n    }\r\n\r\n    const households = this.buildingData[this.selectedBuilding] || [];\r\n    const floorSet = new Set<string>();\r\n\r\n    households.forEach(household => {\r\n      if (household.floor) {\r\n        floorSet.add(household.floor);\r\n      }\r\n    });\r\n\r\n    // 對樓層進行自然排序\r\n    this.floors = Array.from(floorSet).sort((a, b) => {\r\n      const numA = parseInt(a.replace(/[^0-9]/g, ''));\r\n      const numB = parseInt(b.replace(/[^0-9]/g, ''));\r\n      return numA - numB;\r\n    });\r\n\r\n    console.log('Updated floors for building:', this.selectedBuilding, this.floors);\r\n  }\r\n\r\n  // 新增：樓層選擇處理\r\n  onFloorSelect(floor: string) {\r\n    console.log('Floor selected:', floor);\r\n    this.selectedFloor = this.selectedFloor === floor ? '' : floor; // 切換選擇\r\n    this.updateFilteredHouseholds();\r\n    this.cdr.detectChanges();\r\n  }\r\n\r\n  // 新增：取得當前棧別的樓層計數\r\n  getFloorCount(floor: string): number {\r\n    if (!this.selectedBuilding) return 0;\r\n    const households = this.buildingData[this.selectedBuilding] || [];\r\n    return households.filter(h => h.floor === floor).length;\r\n  }\r\n  // 新增：取得戶別的樓層資訊\r\n  getHouseholdFloor(householdCode: string): string {\r\n    if (!this.selectedBuilding) return '';\r\n    const households = this.buildingData[this.selectedBuilding] || [];\r\n    const household = households.find(h => h.houseName === householdCode);\r\n    return household?.floor || '';\r\n  }\r\n  // 新增：取得戶別的完整資訊（包含樓層）\r\n  getHouseholdInfo(householdCode: string): { houseName: string, floor: string } {\r\n    for (const building of this.buildings) {\r\n      const households = this.buildingData[building] || [];\r\n      const household = households.find(h => h.houseName === householdCode);\r\n      if (household) {\r\n        return {\r\n          houseName: household.houseName,\r\n          floor: household.floor || ''\r\n        };\r\n      }\r\n    }\r\n    return { houseName: householdCode, floor: '' };\r\n  }\r\n\r\n  // 新增：根據 houseId 取得戶別的完整資訊\r\n  getHouseholdInfoById(houseId: number): { houseName: string, floor: string } {\r\n    for (const building of this.buildings) {\r\n      const households = this.buildingData[building] || [];\r\n      const household = households.find(h => h.houseId === houseId);\r\n      if (household) {\r\n        return {\r\n          houseName: household.houseName,\r\n          floor: household.floor || ''\r\n        };\r\n      }\r\n    }\r\n    return { houseName: `ID:${houseId}`, floor: '' };\r\n  }\r\n\r\n  // 新增：檢查搜尋是否有結果\r\n  hasNoSearchResults(): boolean {\r\n    if (!this.searchTerm || !this.selectedBuilding || !this.buildingData[this.selectedBuilding]) {\r\n      return false;\r\n    }\r\n\r\n    const filtered = this.buildingData[this.selectedBuilding].filter(h => {\r\n      const floorMatch = !this.selectedFloor || h.floor === this.selectedFloor;\r\n      const searchMatch = h.houseName.toLowerCase().includes(this.searchTerm.toLowerCase());\r\n      return floorMatch && searchMatch;\r\n    });\r\n\r\n    return filtered.length === 0;\r\n  }\r\n\r\n  // 新增：取得過濾後的戶別數量\r\n  getFilteredHouseholdsCount(): number {\r\n    if (!this.selectedBuilding || !this.buildingData[this.selectedBuilding]) {\r\n      return 0;\r\n    }\r\n\r\n    const filtered = this.buildingData[this.selectedBuilding].filter(h => {\r\n      const floorMatch = !this.selectedFloor || h.floor === this.selectedFloor;\r\n      const searchMatch = !this.searchTerm || h.houseName.toLowerCase().includes(this.searchTerm.toLowerCase());\r\n      return floorMatch && searchMatch;\r\n    });\r\n\r\n    return filtered.length;\r\n  }\r\n\r\n  // 新增：產生戶別的唯一識別符\r\n  getHouseholdUniqueId(household: HouseholdItem): string {\r\n    return household.houseId ? household.houseId.toString() : `${household.houseName}_${household.floor}`;\r\n  }\r\n  // 新增：輔助方法 - 根據 houseId 查找 HouseholdItem\r\n  private getHouseholdByHouseId(houseId: number): HouseholdItem | null {\r\n    for (const building of this.buildings) {\r\n      const households = this.buildingData[building] || [];\r\n      const household = households.find(h => h.houseId === houseId);\r\n      if (household) return household;\r\n    }\r\n    return null;\r\n  }\r\n  // 新增：輔助方法 - 根據 houseName 查找 houseId\r\n  private getHouseIdByHouseName(houseName: string): number | null {\r\n    const matchingHouseholds: { building: string, household: HouseholdItem }[] = [];\r\n\r\n    // 收集所有符合名稱的戶別\r\n    for (const building of this.buildings) {\r\n      const households = this.buildingData[building] || [];\r\n      const matches = households.filter(h => h.houseName === houseName);\r\n      matches.forEach(household => {\r\n        matchingHouseholds.push({ building, household });\r\n      });\r\n    }\r\n\r\n    console.log(`查找 houseName \"${houseName}\" 的結果:`, matchingHouseholds);\r\n\r\n    if (matchingHouseholds.length === 0) {\r\n      console.warn(`找不到 houseName \"${houseName}\" 對應的戶別`);\r\n      return null;\r\n    }\r\n\r\n    if (matchingHouseholds.length > 1) {\r\n      console.warn(`發現多個同名戶別 \"${houseName}\":`, matchingHouseholds.map(m => `${m.building}-${m.household.floor}`));\r\n      console.warn(`將返回第一個找到的: ${matchingHouseholds[0].building}-${matchingHouseholds[0].household.floor}`);\r\n    }\r\n\r\n    const firstMatch = matchingHouseholds[0];\r\n    return firstMatch.household.houseId || null;\r\n  }\r\n\r\n  // 新增：輔助方法 - 檢查 houseId 是否被選中\r\n  private isHouseIdSelected(houseId: number): boolean {\r\n    return this.selectedHouseIds.includes(houseId);\r\n  }\r\n\r\n  // 新增：輔助方法 - 檢查 houseId 是否被排除\r\n  private isHouseIdExcluded(houseId: number): boolean {\r\n    return this.excludedHouseIds.includes(houseId);\r\n  }\r\n\r\n  // 新增：從唯一識別符獲取戶別物件\r\n  getHouseholdFromUniqueId(uniqueId: string): HouseholdItem | null {\r\n    for (const building of this.buildings) {\r\n      const households = this.buildingData[building] || [];\r\n      const household = households.find(h => this.getHouseholdUniqueId(h) === uniqueId);\r\n      if (household) return household;\r\n    }\r\n    return null;\r\n  }\r\n\r\n  // 新增：獲取棟別選擇進度百分比\r\n  getBuildingSelectionProgress(building: string): number {\r\n    const totalUnits = this.getBuildingCount(building);\r\n    const selectedUnits = this.getBuildingSelectedHouseIds(building).length;\r\n    return totalUnits > 0 ? (selectedUnits / totalUnits) * 100 : 0;\r\n  }\r\n\r\n  // 新增：檢查棟別是否全部選中\r\n  isBuildingFullySelected(building: string): boolean {\r\n    const totalUnits = this.getBuildingCount(building);\r\n    const selectedUnits = this.getBuildingSelectedHouseIds(building).length;\r\n    return totalUnits > 0 && selectedUnits === totalUnits;\r\n  }\r\n\r\n  // 新增：獲取棟別選擇狀態文字\r\n  getBuildingSelectionStatus(building: string): string {\r\n    const totalUnits = this.getBuildingCount(building);\r\n    const selectedUnits = this.getBuildingSelectedHouseIds(building).length;\r\n    \r\n    if (selectedUnits === 0) {\r\n      return '未選擇';\r\n    } else if (selectedUnits === totalUnits) {\r\n      return '全部選中';\r\n    } else {\r\n      return `${selectedUnits}/${totalUnits}`;\r\n    }\r\n  }\r\n\r\n  // 新增：獲取棟別的可用戶別數量（排除被其他元件選擇的）\r\n  getBuildingAvailableCount(building: string): number {\r\n    const households = this.buildingData[building] || [];\r\n    return households.filter(h => h.houseId && !this.isHouseIdExcluded(h.houseId)).length;\r\n  }\r\n}\r\n", "<div class=\"household-binding-container\">\r\n  <!-- 已選擇戶別顯示區域 -->\r\n  <div *ngIf=\"showSelectedArea && selectedHouseIds.length > 0\" class=\"selected-households-area\">\r\n    <div class=\"selected-header\">\r\n      <div class=\"selected-info\">\r\n        <nb-icon icon=\"people-outline\" class=\"text-primary\"></nb-icon>\r\n        <span class=\"selected-count\">已選擇戶別 ({{getSelectedCount()}})</span>\r\n      </div>\r\n      <button type=\"button\" class=\"btn btn-outline-danger btn-sm\" [disabled]=\"disabled\" (click)=\"onClearAll()\">\r\n        清空全部\r\n      </button>\r\n    </div>\r\n    <div class=\"selected-content\">\r\n      <div *ngFor=\"let building of buildings\" class=\"building-group\">\r\n        <ng-container *ngIf=\"hasBuildingSelected(building)\">\r\n          <div class=\"building-label\">{{building}}:</div>\r\n          <div class=\"households-tags\">\r\n            <span *ngFor=\"let houseId of getBuildingSelectedHouseIds(building)\" class=\"household-tag\">\r\n              <div class=\"household-info\">\r\n                <span class=\"household-code\">{{getHouseholdInfoById(houseId).houseName}}</span>\r\n                <span *ngIf=\"getHouseholdInfoById(houseId).floor\" class=\"household-floor\">\r\n                  {{getHouseholdInfoById(houseId).floor}}\r\n                </span>\r\n              </div>\r\n              <button type=\"button\" class=\"remove-btn\" [disabled]=\"disabled\" (click)=\"onRemoveHousehold(houseId)\">\r\n                <nb-icon icon=\"close-outline\"></nb-icon>\r\n              </button>\r\n            </span>\r\n          </div>\r\n        </ng-container>\r\n      </div>\r\n    </div>\r\n  </div> <!-- 選擇器 -->\r\n  <div class=\"selector-container\">\r\n    <button type=\"button\" class=\"selector-button\" [class.disabled]=\"disabled || isLoading\"\r\n      [disabled]=\"disabled || isLoading\" (click)=\"toggleDropdown()\"\r\n      style=\"width: 100%; display: flex; align-items: center; justify-content: space-between; padding: 0.5rem 0.75rem; border: 1px solid #ced4da; border-radius: 0.375rem; background-color: #fff; cursor: pointer;\">\r\n      <span class=\"selector-text\">\r\n        <ng-container *ngIf=\"isLoading\">\r\n          <nb-icon icon=\"loader-outline\" class=\"spin\"></nb-icon>\r\n          載入中...\r\n        </ng-container>\r\n        <ng-container *ngIf=\"!isLoading\">\r\n          {{getSelectedCount() > 0 ? '已選擇 ' + getSelectedCount() + ' 個戶別' : placeholder}}\r\n        </ng-container>\r\n      </span>\r\n      <nb-icon icon=\"home-outline\" class=\"chevron-icon\"></nb-icon>\r\n    </button>\r\n  </div>\r\n</div>\r\n\r\n<!-- 戶別選擇對話框 -->\r\n<ng-template #householdDialog let-dialog let-ref=\"dialogRef\">\r\n  <nb-card style=\"width: 95vw; max-width: 1200px; max-height: 90vh;\">\r\n    <nb-card-header>\r\n      <div style=\"display: flex; align-items: center; justify-content: space-between;\">\r\n        <div style=\"display: flex; align-items: center; gap: 8px;\">\r\n          <nb-icon icon=\"home-outline\" style=\"color: #007bff; font-size: 1.5rem;\"></nb-icon>\r\n          <span style=\"font-weight: 500; color: #495057; font-size: 1.25rem;\">選擇戶別</span>\r\n          <span style=\"font-size: 0.875rem; color: #6c757d;\">({{buildings.length}} 個棟別)</span>\r\n        </div>\r\n        <span style=\"font-size: 0.875rem; color: #6c757d;\">已選擇: {{getSelectedCount()}}</span>\r\n      </div>\r\n    </nb-card-header>\r\n\r\n    <nb-card-body style=\"padding: 0; overflow: hidden;\">\r\n      <!-- 載入狀態 -->\r\n      <div *ngIf=\"isLoading\"\r\n        style=\"width: 100%; display: flex; align-items: center; justify-content: center; padding: 40px;\">\r\n        <div style=\"text-align: center; color: #6c757d;\">\r\n          <nb-icon icon=\"loader-outline\" class=\"spin\" style=\"font-size: 2rem; margin-bottom: 8px;\"></nb-icon>\r\n          <p style=\"margin: 0; font-size: 0.875rem;\">載入戶別資料中...</p>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 主要內容區域 -->\r\n      <div *ngIf=\"!isLoading\" style=\"display: flex; height: 60vh; min-height: 400px;\">        <!-- 棟別選擇側邊欄 -->\r\n        <div class=\"building-sidebar\">\r\n          <div class=\"building-sidebar-header\">\r\n            <div class=\"header-content\">\r\n              <nb-icon icon=\"layers-outline\" class=\"header-icon\"></nb-icon>\r\n              <h6 class=\"header-title\">棟別列表</h6>\r\n              <span class=\"building-count-badge\">{{buildings.length}} 棟</span>\r\n            </div>\r\n          </div>\r\n          <div class=\"building-list-container\">\r\n            <div *ngFor=\"let building of buildings\" class=\"building-card\"\r\n              [class.selected]=\"selectedBuilding === building\"\r\n              [class.has-selection]=\"hasBuildingSelected(building)\"\r\n              (click)=\"onBuildingSelect(building)\">\r\n              \r\n              <!-- 棟別主要信息 -->\r\n              <div class=\"building-header\">\r\n                <div class=\"building-name-section\">\r\n                  <nb-icon icon=\"business-outline\" class=\"building-icon\"></nb-icon>\r\n                  <span class=\"building-name\">{{building}}</span>\r\n                </div>\r\n                <div class=\"building-badge-section\">\r\n                  <span class=\"total-units-badge\">{{getBuildingCount(building)}}戶</span>\r\n                </div>\r\n              </div>              <!-- 選擇進度條 -->\r\n              <div class=\"building-progress\" *ngIf=\"getBuildingCount(building) > 0\">\r\n                <div class=\"progress-bar\">\r\n                  <div class=\"progress-fill\" \r\n                    [style.width.%]=\"getBuildingSelectionProgress(building)\"\r\n                    [class.full-selection]=\"isBuildingFullySelected(building)\">\r\n                  </div>\r\n                </div>\r\n                <div class=\"progress-text\">\r\n                  <span class=\"status-text\" \r\n                    [class.fully-selected]=\"isBuildingFullySelected(building)\"\r\n                    [class.no-selection]=\"getBuildingSelectedHouseIds(building).length === 0\">\r\n                    {{getBuildingSelectionStatus(building)}}\r\n                  </span>\r\n                  <span class=\"available-count\" *ngIf=\"getBuildingAvailableCount(building) !== getBuildingCount(building)\">\r\n                    可選：{{getBuildingAvailableCount(building)}}\r\n                  </span>\r\n                </div>\r\n              </div>\r\n\r\n              <!-- 選中狀態指示器 -->\r\n              <div class=\"selection-indicator\" *ngIf=\"selectedBuilding === building\">\r\n                <nb-icon icon=\"checkmark-circle-2-outline\"></nb-icon>\r\n              </div>\r\n\r\n              <!-- 已選戶別數量提示 -->\r\n              <div class=\"selected-units-indicator\" *ngIf=\"getBuildingSelectedHouseIds(building).length > 0\">\r\n                <span class=\"selected-units-count\">{{getBuildingSelectedHouseIds(building).length}}</span>\r\n              </div>\r\n            </div>\r\n\r\n            <!-- 空狀態 -->\r\n            <div *ngIf=\"buildings.length === 0\" class=\"empty-state\">\r\n              <nb-icon icon=\"home-outline\" class=\"empty-icon\"></nb-icon>\r\n              <p class=\"empty-text\">暫無可用棟別</p>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 戶別選擇主區域 -->\r\n        <div style=\"flex: 1; display: flex; flex-direction: column;\">\r\n          <div style=\"padding: 12px 16px; border-bottom: 1px solid #e9ecef;\">\r\n            <div style=\"display: flex; align-items: center; justify-content: space-between; margin-bottom: 8px;\">\r\n              <div style=\"display: flex; align-items: center; gap: 8px;\">\r\n                <h6 style=\"margin: 0; font-size: 0.875rem; font-weight: 600; color: #495057;\">戶別選擇</h6>\r\n                <span *ngIf=\"selectedBuilding\"\r\n                  style=\"background-color: #007bff; color: white; padding: 3px 8px; border-radius: 3px; font-size: 0.75rem; font-weight: 600;\">\r\n                  <nb-icon icon=\"home-outline\" style=\"margin-right: 4px; font-size: 0.7rem;\"></nb-icon>\r\n                  {{selectedBuilding}}\r\n                </span>\r\n                <span *ngIf=\"selectedFloor\"\r\n                  style=\"background-color: #28a745; color: white; padding: 3px 8px; border-radius: 3px; font-size: 0.75rem; font-weight: 600;\">\r\n                  <nb-icon icon=\"layers-outline\" style=\"margin-right: 4px; font-size: 0.7rem;\"></nb-icon>\r\n                  {{selectedFloor}}\r\n                </span>\r\n              </div>\r\n              <div\r\n                *ngIf=\"allowBatchSelect && selectedBuilding && buildingData[selectedBuilding] && buildingData[selectedBuilding].length > 0\"\r\n                style=\"display: flex; gap: 4px;\">\r\n                <button type=\"button\" [disabled]=\"!canSelectMore()\" (click)=\"onSelectAllFiltered()\"\r\n                  [style.opacity]=\"canSelectMore() ? '1' : '0.5'\"\r\n                  style=\"padding: 4px 8px; font-size: 0.75rem; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer;\">\r\n                  全選當前\r\n                </button>\r\n                <button type=\"button\" [disabled]=\"!canSelectMore()\" (click)=\"onSelectAllBuilding()\"\r\n                  [style.opacity]=\"canSelectMore() ? '1' : '0.5'\"\r\n                  style=\"padding: 4px 8px; font-size: 0.75rem; background: #28a745; color: white; border: none; border-radius: 4px; cursor: pointer;\">\r\n                  全選{{selectedBuilding}}\r\n                </button>\r\n                <button type=\"button\" *ngIf=\"isSomeBuildingSelected()\" (click)=\"onUnselectAllBuilding()\"\r\n                  style=\"padding: 4px 8px; font-size: 0.75rem; background: #6c757d; color: white; border: none; border-radius: 4px; cursor: pointer;\">\r\n                  清除\r\n                </button>\r\n              </div>\r\n            </div>\r\n\r\n            <!-- 搜尋框 -->\r\n            <div *ngIf=\"allowSearch && selectedBuilding\" style=\"margin-top: 8px;\">\r\n              <div style=\"position: relative;\">\r\n                <input type=\"text\" [(ngModel)]=\"searchTerm\" (input)=\"onSearchChange($event)\" placeholder=\"搜尋戶別代碼...\"\r\n                  style=\"width: 100%; padding: 6px 32px 6px 12px; border: 1px solid #ced4da; border-radius: 4px; font-size: 0.875rem; outline: none;\">\r\n                <nb-icon icon=\"search-outline\"\r\n                  style=\"position: absolute; right: 10px; top: 50%; transform: translateY(-50%); color: #6c757d; font-size: 0.875rem;\"></nb-icon>\r\n              </div>\r\n              <div *ngIf=\"searchTerm && hasNoSearchResults()\"\r\n                style=\"font-size: 0.75rem; color: #dc3545; margin-top: 4px;\">\r\n                找不到符合 \"{{searchTerm}}\" 的戶別\r\n              </div>\r\n            </div> <!-- 樓層篩選器 -->\r\n            <div *ngIf=\"selectedBuilding && floors.length > 1\" style=\"margin-top: 12px;\">\r\n              <div style=\"display: flex; align-items: center; gap: 8px; margin-bottom: 8px;\">\r\n                <nb-icon icon=\"layers-outline\" style=\"color: #6c757d; font-size: 1rem;\"></nb-icon>\r\n                <span style=\"font-size: 0.875rem; font-weight: 600; color: #495057;\">樓層篩選:</span>\r\n                <span *ngIf=\"selectedFloor\"\r\n                  style=\"background-color: #28a745; color: white; padding: 3px 8px; border-radius: 3px; font-size: 0.75rem; font-weight: 600;\">\r\n                  {{selectedFloor}}\r\n                </span>\r\n                <button type=\"button\" *ngIf=\"selectedFloor\" (click)=\"onFloorSelect('')\"\r\n                  style=\"font-size: 0.75rem; color: #007bff; background: none; border: none; text-decoration: underline; cursor: pointer;\">\r\n                  清除篩選\r\n                </button>\r\n              </div>\r\n              <div style=\"display: flex; flex-wrap: wrap; gap: 4px; max-height: 100px; overflow-y: auto;\">\r\n                <button type=\"button\" *ngFor=\"let floor of floors\" (click)=\"onFloorSelect(floor)\"\r\n                  [style.background-color]=\"selectedFloor === floor ? '#007bff' : '#f8f9fa'\"\r\n                  [style.color]=\"selectedFloor === floor ? '#fff' : '#495057'\"\r\n                  [style.border]=\"selectedFloor === floor ? '2px solid #007bff' : '1px solid #dee2e6'\"\r\n                  style=\"padding: 6px 10px; border-radius: 3px; font-size: 0.75rem; font-weight: 500; cursor: pointer; transition: all 0.15s ease; white-space: nowrap;\">\r\n                  <nb-icon icon=\"layers-outline\" style=\"margin-right: 3px; font-size: 0.7rem;\"></nb-icon>\r\n                  {{floor}} <span style=\"font-size: 0.7rem; opacity: 0.7;\">({{getFloorCount(floor)}})</span>\r\n                </button>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 戶別網格或空狀態 -->\r\n          <div style=\"flex: 1; padding: 16px; overflow-y: auto;\">\r\n            <div *ngIf=\"!selectedBuilding\" style=\"text-align: center; padding: 40px 20px; color: #6c757d;\">\r\n              <nb-icon icon=\"home-outline\" style=\"font-size: 2rem; margin-bottom: 8px; opacity: 0.5;\"></nb-icon>\r\n              <p style=\"margin: 0; font-size: 0.875rem;\">請先選擇棟別</p>\r\n            </div>\r\n            <div *ngIf=\"selectedBuilding && buildingData[selectedBuilding] && buildingData[selectedBuilding].length > 0\"\r\n              style=\"display: grid; grid-template-columns: repeat(auto-fill, minmax(90px, 1fr)); gap: 8px;\">\r\n              <ng-container *ngFor=\"let household of buildingData[selectedBuilding]\"> <button *ngIf=\"(!selectedFloor || household.floor === selectedFloor) &&\r\n                              (!searchTerm || household.houseName.toLowerCase().includes(searchTerm.toLowerCase()))\"\r\n                  type=\"button\" (click)=\"onHouseholdToggle(household.houseId)\"\r\n                  [disabled]=\"isHouseholdDisabled(household.houseId)\"\r\n                  [style.background-color]=\"isHouseholdSelected(household.houseId) ? '#007bff' : (isHouseholdExcluded(household.houseId) ? '#f8f9fa' : '#fff')\"\r\n                  [style.color]=\"isHouseholdSelected(household.houseId) ? '#fff' : (isHouseholdExcluded(household.houseId) ? '#6c757d' : '#495057')\"\r\n                  [style.border]=\"isHouseholdSelected(household.houseId) ? '2px solid #007bff' : (isHouseholdExcluded(household.houseId) ? '1px solid #dee2e6' : '1px solid #ced4da')\"\r\n                  [style.opacity]=\"isHouseholdDisabled(household.houseId) ? '0.6' : '1'\"\r\n                  [style.cursor]=\"isHouseholdDisabled(household.houseId) ? 'not-allowed' : 'pointer'\"\r\n                  style=\"padding: 8px 6px; border-radius: 4px; transition: all 0.15s ease; font-size: 0.75rem; text-align: center; min-height: 55px; position: relative; display: flex; flex-direction: column; justify-content: center; align-items: center; gap: 3px;\">\r\n                  <span [style.text-decoration]=\"isHouseholdExcluded(household.houseId) ? 'line-through' : 'none'\"\r\n                    style=\"font-weight: 600; line-height: 1.2; font-size: 0.85rem;\">\r\n                    {{household.houseName}}\r\n                  </span> <span *ngIf=\"household.floor\"\r\n                    [style.background-color]=\"isHouseholdSelected(household.houseId) ? 'rgba(255,255,255,0.9)' : '#28a745'\"\r\n                    [style.color]=\"isHouseholdSelected(household.houseId) ? '#007bff' : '#fff'\"\r\n                    [style.border]=\"isHouseholdSelected(household.houseId) ? '1px solid rgba(0,123,255,0.3)' : 'none'\"\r\n                    style=\"font-size: 0.7rem; font-weight: 600; padding: 2px 6px; border-radius: 3px; display: inline-flex; align-items: center; justify-content: center; min-width: 22px;\">\r\n                    <nb-icon icon=\"layers-outline\" style=\"margin-right: 2px; font-size: 0.6rem;\"></nb-icon>\r\n                    {{household.floor}}\r\n                  </span>\r\n                  <div *ngIf=\"isHouseholdExcluded(household.houseId)\"\r\n                    style=\"position: absolute; top: -8px; right: -8px; background: #dc3545; color: white; border-radius: 50%; width: 16px; height: 16px; font-size: 10px; display: flex; align-items: center; justify-content: center;\">\r\n                    ✕\r\n                  </div>\r\n                </button>\r\n              </ng-container>\r\n            </div>\r\n            <div\r\n              *ngIf=\"selectedBuilding && (!buildingData[selectedBuilding] || buildingData[selectedBuilding].length === 0) && !searchTerm\"\r\n              style=\"text-align: center; padding: 40px 20px; color: #6c757d;\">\r\n              <nb-icon icon=\"alert-circle-outline\" style=\"font-size: 2rem; margin-bottom: 8px; opacity: 0.5;\"></nb-icon>\r\n              <p style=\"margin: 0; font-size: 0.875rem;\">此棟別沒有可用的戶別</p>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </nb-card-body>\r\n\r\n    <nb-card-footer style=\"padding: 16px; border-top: 1px solid #e9ecef; background-color: #f8f9fa;\">\r\n      <!-- 統計資訊行 -->\r\n      <div style=\"display: flex; align-items: center; justify-content: space-between; margin-bottom: 12px;\">\r\n        <div style=\"display: flex; align-items: center; gap: 16px; font-size: 0.875rem; color: #495057;\">\r\n          <div style=\"display: flex; align-items: center; gap: 4px;\">\r\n            <nb-icon icon=\"checkmark-circle-outline\" style=\"color: #28a745;\"></nb-icon>\r\n            <span>已選擇: <strong>{{getSelectedCount()}}</strong> 個戶別</span>\r\n          </div>\r\n          <div *ngIf=\"maxSelections\" style=\"display: flex; align-items: center; gap: 4px;\">\r\n            <nb-icon icon=\"alert-circle-outline\" style=\"color: #ffc107;\"></nb-icon>\r\n            <span>限制: 最多 <strong>{{maxSelections}}</strong> 個</span>\r\n          </div>\r\n          <div *ngIf=\"selectedBuilding\" style=\"display: flex; align-items: center; gap: 8px;\">\r\n            <nb-icon icon=\"home-outline\" style=\"color: #007bff;\"></nb-icon>\r\n            <span>當前棟別: </span>\r\n            <span\r\n              style=\"background-color: #007bff; color: white; padding: 4px 8px; border-radius: 4px; font-size: 0.75rem; font-weight: 600;\">\r\n              {{selectedBuilding}}\r\n            </span>\r\n            <span *ngIf=\"selectedFloor\"\r\n              style=\"background-color: #28a745; color: white; padding: 4px 8px; border-radius: 4px; font-size: 0.75rem; font-weight: 600; margin-left: 4px;\">\r\n              <nb-icon icon=\"layers-outline\" style=\"margin-right: 4px; font-size: 0.7rem;\"></nb-icon>\r\n              {{selectedFloor}}\r\n            </span>\r\n          </div>\r\n        </div>\r\n        <div *ngIf=\"searchTerm\"\r\n          style=\"font-size: 0.75rem; color: #6c757d; background-color: #e9ecef; padding: 4px 8px; border-radius: 4px;\">\r\n          搜尋: \"{{searchTerm}}\" ({{getFilteredHouseholdsCount()}} 個結果)\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 操作按鈕行 -->\r\n      <div style=\"display: flex; align-items: center; justify-content: space-between; gap: 8px;\">\r\n        <div style=\"display: flex; gap: 8px;\">\r\n          <button type=\"button\" *ngIf=\"selectedHouseIds.length > 0\" (click)=\"onClearAll()\"\r\n            style=\"padding: 8px 12px; background: #dc3545; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 0.875rem; display: flex; align-items: center; gap: 4px;\">\r\n            <nb-icon icon=\"trash-2-outline\"></nb-icon>\r\n            清空全部\r\n          </button>\r\n          <button type=\"button\" *ngIf=\"allowSearch && searchTerm\" (click)=\"resetSearch()\"\r\n            style=\"padding: 8px 12px; background: #6c757d; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 0.875rem; display: flex; align-items: center; gap: 4px;\">\r\n            <nb-icon icon=\"refresh-outline\"></nb-icon>\r\n            重置搜尋\r\n          </button>\r\n        </div>\r\n\r\n        <div style=\"display: flex; gap: 8px;\">\r\n          <button type=\"button\" (click)=\"ref.close()\"\r\n            style=\"padding: 8px 16px; background: #6c757d; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 0.875rem;\">\r\n            取消\r\n          </button>\r\n          <button type=\"button\" (click)=\"ref.close()\"\r\n            style=\"padding: 8px 20px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 0.875rem; font-weight: 500; display: flex; align-items: center; gap: 4px;\">\r\n            <nb-icon icon=\"checkmark-outline\"></nb-icon>\r\n            確定選擇 ({{getSelectedCount()}})\r\n          </button>\r\n        </div>\r\n      </div>\r\n    </nb-card-footer>\r\n  </nb-card>\r\n</ng-template>"], "mappings": "AAAA,SAAmCA,YAAY,EAAoCC,UAAU,QAAmD,eAAe;AAC/J,SAA+BC,iBAAiB,QAAQ,gBAAgB;;;;;;;;;ICmBxDC,EAAA,CAAAC,cAAA,eAA0E;IACxED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IADLH,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAC,MAAA,CAAAC,oBAAA,CAAAC,UAAA,EAAAC,KAAA,MACF;;;;;;IAHAT,EAFJ,CAAAC,cAAA,eAA0F,cAC5D,eACG;IAAAD,EAAA,CAAAE,MAAA,GAA2C;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC/EH,EAAA,CAAAU,UAAA,IAAAC,2EAAA,mBAA0E;IAG5EX,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,iBAAoG;IAArCD,EAAA,CAAAY,UAAA,mBAAAC,6FAAA;MAAA,MAAAL,UAAA,GAAAR,EAAA,CAAAc,aAAA,CAAAC,GAAA,EAAAC,SAAA;MAAA,MAAAV,MAAA,GAAAN,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAASZ,MAAA,CAAAa,iBAAA,CAAAX,UAAA,CAA0B;IAAA,EAAC;IACjGR,EAAA,CAAAoB,SAAA,kBAAwC;IAE5CpB,EADE,CAAAG,YAAA,EAAS,EACJ;;;;;IAR0BH,EAAA,CAAAI,SAAA,GAA2C;IAA3CJ,EAAA,CAAAqB,iBAAA,CAAAf,MAAA,CAAAC,oBAAA,CAAAC,UAAA,EAAAc,SAAA,CAA2C;IACjEtB,EAAA,CAAAI,SAAA,EAAyC;IAAzCJ,EAAA,CAAAuB,UAAA,SAAAjB,MAAA,CAAAC,oBAAA,CAAAC,UAAA,EAAAC,KAAA,CAAyC;IAITT,EAAA,CAAAI,SAAA,EAAqB;IAArBJ,EAAA,CAAAuB,UAAA,aAAAjB,MAAA,CAAAkB,QAAA,CAAqB;;;;;IAVpExB,EAAA,CAAAyB,uBAAA,GAAoD;IAClDzB,EAAA,CAAAC,cAAA,cAA4B;IAAAD,EAAA,CAAAE,MAAA,GAAa;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAC/CH,EAAA,CAAAC,cAAA,cAA6B;IAC3BD,EAAA,CAAAU,UAAA,IAAAgB,oEAAA,mBAA0F;IAW5F1B,EAAA,CAAAG,YAAA,EAAM;;;;;;IAbsBH,EAAA,CAAAI,SAAA,GAAa;IAAbJ,EAAA,CAAAK,kBAAA,KAAAsB,WAAA,MAAa;IAEb3B,EAAA,CAAAI,SAAA,GAAwC;IAAxCJ,EAAA,CAAAuB,UAAA,YAAAjB,MAAA,CAAAsB,2BAAA,CAAAD,WAAA,EAAwC;;;;;IAJxE3B,EAAA,CAAAC,cAAA,cAA+D;IAC7DD,EAAA,CAAAU,UAAA,IAAAmB,6DAAA,0BAAoD;IAgBtD7B,EAAA,CAAAG,YAAA,EAAM;;;;;IAhBWH,EAAA,CAAAI,SAAA,EAAmC;IAAnCJ,EAAA,CAAAuB,UAAA,SAAAjB,MAAA,CAAAwB,mBAAA,CAAAH,WAAA,EAAmC;;;;;;IAVpD3B,EAFJ,CAAAC,cAAA,aAA8F,aAC/D,cACA;IACzBD,EAAA,CAAAoB,SAAA,kBAA8D;IAC9DpB,EAAA,CAAAC,cAAA,eAA6B;IAAAD,EAAA,CAAAE,MAAA,GAA8B;IAC7DF,EAD6D,CAAAG,YAAA,EAAO,EAC9D;IACNH,EAAA,CAAAC,cAAA,iBAAyG;IAAvBD,EAAA,CAAAY,UAAA,mBAAAmB,iEAAA;MAAA/B,EAAA,CAAAc,aAAA,CAAAkB,GAAA;MAAA,MAAA1B,MAAA,GAAAN,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAASZ,MAAA,CAAA2B,UAAA,EAAY;IAAA,EAAC;IACtGjC,EAAA,CAAAE,MAAA,iCACF;IACFF,EADE,CAAAG,YAAA,EAAS,EACL;IACNH,EAAA,CAAAC,cAAA,cAA8B;IAC5BD,EAAA,CAAAU,UAAA,IAAAwB,8CAAA,kBAA+D;IAmBnElC,EADE,CAAAG,YAAA,EAAM,EACF;;;;IA1B6BH,EAAA,CAAAI,SAAA,GAA8B;IAA9BJ,EAAA,CAAAK,kBAAA,qCAAAC,MAAA,CAAA6B,gBAAA,QAA8B;IAEDnC,EAAA,CAAAI,SAAA,EAAqB;IAArBJ,EAAA,CAAAuB,UAAA,aAAAjB,MAAA,CAAAkB,QAAA,CAAqB;IAKvDxB,EAAA,CAAAI,SAAA,GAAY;IAAZJ,EAAA,CAAAuB,UAAA,YAAAjB,MAAA,CAAA8B,SAAA,CAAY;;;;;IAyBpCpC,EAAA,CAAAyB,uBAAA,GAAgC;IAC9BzB,EAAA,CAAAoB,SAAA,kBAAsD;IACtDpB,EAAA,CAAAE,MAAA,8BACF;;;;;;IACAF,EAAA,CAAAyB,uBAAA,GAAiC;IAC/BzB,EAAA,CAAAE,MAAA,GACF;;;;;IADEF,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAC,MAAA,CAAA6B,gBAAA,iCAAA7B,MAAA,CAAA6B,gBAAA,6BAAA7B,MAAA,CAAA+B,WAAA,MACF;;;;;IAyBArC,EAFF,CAAAC,cAAA,cACmG,cAChD;IAC/CD,EAAA,CAAAoB,SAAA,kBAAmG;IACnGpB,EAAA,CAAAC,cAAA,YAA2C;IAAAD,EAAA,CAAAE,MAAA,oDAAU;IAEzDF,EAFyD,CAAAG,YAAA,EAAI,EACrD,EACF;;;;;IAyCMH,EAAA,CAAAC,cAAA,eAAyG;IACvGD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IADLH,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAK,kBAAA,wBAAAC,MAAA,CAAAgC,yBAAA,CAAAC,WAAA,OACF;;;;;IAdFvC,EADF,CAAAC,cAAA,cAAsE,cAC1C;IACxBD,EAAA,CAAAoB,SAAA,cAGM;IACRpB,EAAA,CAAAG,YAAA,EAAM;IAEJH,EADF,CAAAC,cAAA,cAA2B,eAGmD;IAC1ED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACPH,EAAA,CAAAU,UAAA,IAAA8B,2EAAA,mBAAyG;IAI7GxC,EADE,CAAAG,YAAA,EAAM,EACF;;;;;IAdAH,EAAA,CAAAI,SAAA,GAAwD;IAAxDJ,EAAA,CAAAyC,WAAA,UAAAnC,MAAA,CAAAoC,4BAAA,CAAAH,WAAA,OAAwD;IACxDvC,EAAA,CAAA2C,WAAA,mBAAArC,MAAA,CAAAsC,uBAAA,CAAAL,WAAA,EAA0D;IAK1DvC,EAAA,CAAAI,SAAA,GAA0D;IAC1DJ,EADA,CAAA2C,WAAA,mBAAArC,MAAA,CAAAsC,uBAAA,CAAAL,WAAA,EAA0D,iBAAAjC,MAAA,CAAAsB,2BAAA,CAAAW,WAAA,EAAAM,MAAA,OACe;IACzE7C,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAC,MAAA,CAAAwC,0BAAA,CAAAP,WAAA,OACF;IAC+BvC,EAAA,CAAAI,SAAA,EAAwE;IAAxEJ,EAAA,CAAAuB,UAAA,SAAAjB,MAAA,CAAAgC,yBAAA,CAAAC,WAAA,MAAAjC,MAAA,CAAAyC,gBAAA,CAAAR,WAAA,EAAwE;;;;;IAO3GvC,EAAA,CAAAC,cAAA,cAAuE;IACrED,EAAA,CAAAoB,SAAA,kBAAqD;IACvDpB,EAAA,CAAAG,YAAA,EAAM;;;;;IAIJH,EADF,CAAAC,cAAA,cAA+F,eAC1D;IAAAD,EAAA,CAAAE,MAAA,GAAgD;IACrFF,EADqF,CAAAG,YAAA,EAAO,EACtF;;;;;IAD+BH,EAAA,CAAAI,SAAA,GAAgD;IAAhDJ,EAAA,CAAAqB,iBAAA,CAAAf,MAAA,CAAAsB,2BAAA,CAAAW,WAAA,EAAAM,MAAA,CAAgD;;;;;;IAzCvF7C,EAAA,CAAAC,cAAA,cAGuC;IAArCD,EAAA,CAAAY,UAAA,mBAAAoC,oFAAA;MAAA,MAAAT,WAAA,GAAAvC,EAAA,CAAAc,aAAA,CAAAmC,GAAA,EAAAjC,SAAA;MAAA,MAAAV,MAAA,GAAAN,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAASZ,MAAA,CAAA4C,gBAAA,CAAAX,WAAA,CAA0B;IAAA,EAAC;IAIlCvC,EADF,CAAAC,cAAA,cAA6B,cACQ;IACjCD,EAAA,CAAAoB,SAAA,kBAAiE;IACjEpB,EAAA,CAAAC,cAAA,eAA4B;IAAAD,EAAA,CAAAE,MAAA,GAAY;IAC1CF,EAD0C,CAAAG,YAAA,EAAO,EAC3C;IAEJH,EADF,CAAAC,cAAA,cAAoC,eACF;IAAAD,EAAA,CAAAE,MAAA,GAA+B;IAEnEF,EAFmE,CAAAG,YAAA,EAAO,EAClE,EACF;IA0BNH,EAzBA,CAAAU,UAAA,IAAAyC,oEAAA,mBAAsE,KAAAC,qEAAA,kBAoBC,KAAAC,qEAAA,kBAKwB;IAGjGrD,EAAA,CAAAG,YAAA,EAAM;;;;;IAzCJH,EADA,CAAA2C,WAAA,aAAArC,MAAA,CAAAgD,gBAAA,KAAAf,WAAA,CAAgD,kBAAAjC,MAAA,CAAAwB,mBAAA,CAAAS,WAAA,EACK;IAOrBvC,EAAA,CAAAI,SAAA,GAAY;IAAZJ,EAAA,CAAAqB,iBAAA,CAAAkB,WAAA,CAAY;IAGRvC,EAAA,CAAAI,SAAA,GAA+B;IAA/BJ,EAAA,CAAAK,kBAAA,KAAAC,MAAA,CAAAyC,gBAAA,CAAAR,WAAA,YAA+B;IAGnCvC,EAAA,CAAAI,SAAA,EAAoC;IAApCJ,EAAA,CAAAuB,UAAA,SAAAjB,MAAA,CAAAyC,gBAAA,CAAAR,WAAA,MAAoC;IAoBlCvC,EAAA,CAAAI,SAAA,EAAmC;IAAnCJ,EAAA,CAAAuB,UAAA,SAAAjB,MAAA,CAAAgD,gBAAA,KAAAf,WAAA,CAAmC;IAK9BvC,EAAA,CAAAI,SAAA,EAAsD;IAAtDJ,EAAA,CAAAuB,UAAA,SAAAjB,MAAA,CAAAsB,2BAAA,CAAAW,WAAA,EAAAM,MAAA,KAAsD;;;;;IAM/F7C,EAAA,CAAAC,cAAA,cAAwD;IACtDD,EAAA,CAAAoB,SAAA,mBAA0D;IAC1DpB,EAAA,CAAAC,cAAA,aAAsB;IAAAD,EAAA,CAAAE,MAAA,2CAAM;IAC9BF,EAD8B,CAAAG,YAAA,EAAI,EAC5B;;;;;IAUFH,EAAA,CAAAC,cAAA,gBAC+H;IAC7HD,EAAA,CAAAoB,SAAA,mBAAqF;IACrFpB,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IADLH,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAC,MAAA,CAAAgD,gBAAA,MACF;;;;;IACAtD,EAAA,CAAAC,cAAA,gBAC+H;IAC7HD,EAAA,CAAAoB,SAAA,mBAAuF;IACvFpB,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IADLH,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAC,MAAA,CAAAiD,aAAA,MACF;;;;;;IAeAvD,EAAA,CAAAC,cAAA,kBACsI;IAD/ED,EAAA,CAAAY,UAAA,mBAAA4C,gGAAA;MAAAxD,EAAA,CAAAc,aAAA,CAAA2C,IAAA;MAAA,MAAAnD,MAAA,GAAAN,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAASZ,MAAA,CAAAoD,qBAAA,EAAuB;IAAA,EAAC;IAEtF1D,EAAA,CAAAE,MAAA,qBACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;IAbTH,EAHF,CAAAC,cAAA,eAEmC,kBAGqG;IAFlFD,EAAA,CAAAY,UAAA,mBAAA+C,uFAAA;MAAA3D,EAAA,CAAAc,aAAA,CAAA8C,IAAA;MAAA,MAAAtD,MAAA,GAAAN,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAASZ,MAAA,CAAAuD,mBAAA,EAAqB;IAAA,EAAC;IAGjF7D,EAAA,CAAAE,MAAA,iCACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,kBAEsI;IAFlFD,EAAA,CAAAY,UAAA,mBAAAkD,uFAAA;MAAA9D,EAAA,CAAAc,aAAA,CAAA8C,IAAA;MAAA,MAAAtD,MAAA,GAAAN,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAASZ,MAAA,CAAAyD,mBAAA,EAAqB;IAAA,EAAC;IAGjF/D,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAU,UAAA,IAAAsD,uEAAA,sBACsI;IAGxIhE,EAAA,CAAAG,YAAA,EAAM;;;;IAbFH,EAAA,CAAAI,SAAA,EAA+C;IAA/CJ,EAAA,CAAAyC,WAAA,YAAAnC,MAAA,CAAA2D,aAAA,iBAA+C;IAD3BjE,EAAA,CAAAuB,UAAA,cAAAjB,MAAA,CAAA2D,aAAA,GAA6B;IAMjDjE,EAAA,CAAAI,SAAA,GAA+C;IAA/CJ,EAAA,CAAAyC,WAAA,YAAAnC,MAAA,CAAA2D,aAAA,iBAA+C;IAD3BjE,EAAA,CAAAuB,UAAA,cAAAjB,MAAA,CAAA2D,aAAA,GAA6B;IAGjDjE,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAK,kBAAA,kBAAAC,MAAA,CAAAgD,gBAAA,MACF;IACuBtD,EAAA,CAAAI,SAAA,EAA8B;IAA9BJ,EAAA,CAAAuB,UAAA,SAAAjB,MAAA,CAAA4D,sBAAA,GAA8B;;;;;IAevDlE,EAAA,CAAAC,cAAA,eAC+D;IAC7DD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADJH,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAK,kBAAA,uCAAAC,MAAA,CAAA6D,UAAA,2BACF;;;;;;IAREnE,EAFJ,CAAAC,cAAA,eAAsE,eACnC,iBAEuG;IADnHD,EAAA,CAAAoE,gBAAA,2BAAAC,8FAAAC,MAAA;MAAAtE,EAAA,CAAAc,aAAA,CAAAyD,IAAA;MAAA,MAAAjE,MAAA,GAAAN,EAAA,CAAAiB,aAAA;MAAAjB,EAAA,CAAAwE,kBAAA,CAAAlE,MAAA,CAAA6D,UAAA,EAAAG,MAAA,MAAAhE,MAAA,CAAA6D,UAAA,GAAAG,MAAA;MAAA,OAAAtE,EAAA,CAAAkB,WAAA,CAAAoD,MAAA;IAAA,EAAwB;IAACtE,EAAA,CAAAY,UAAA,mBAAA6D,sFAAAH,MAAA;MAAAtE,EAAA,CAAAc,aAAA,CAAAyD,IAAA;MAAA,MAAAjE,MAAA,GAAAN,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAASZ,MAAA,CAAAoE,cAAA,CAAAJ,MAAA,CAAsB;IAAA,EAAC;IAA5EtE,EAAA,CAAAG,YAAA,EACsI;IACtIH,EAAA,CAAAoB,SAAA,mBACiI;IACnIpB,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAU,UAAA,IAAAiE,oEAAA,mBAC+D;IAGjE3E,EAAA,CAAAG,YAAA,EAAM;;;;IATiBH,EAAA,CAAAI,SAAA,GAAwB;IAAxBJ,EAAA,CAAA4E,gBAAA,YAAAtE,MAAA,CAAA6D,UAAA,CAAwB;IAKvCnE,EAAA,CAAAI,SAAA,GAAwC;IAAxCJ,EAAA,CAAAuB,UAAA,SAAAjB,MAAA,CAAA6D,UAAA,IAAA7D,MAAA,CAAAuE,kBAAA,GAAwC;;;;;IAS5C7E,EAAA,CAAAC,cAAA,gBAC+H;IAC7HD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IADLH,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAC,MAAA,CAAAiD,aAAA,MACF;;;;;;IACAvD,EAAA,CAAAC,cAAA,kBAC2H;IAD/ED,EAAA,CAAAY,UAAA,mBAAAkE,gGAAA;MAAA9E,EAAA,CAAAc,aAAA,CAAAiE,IAAA;MAAA,MAAAzE,MAAA,GAAAN,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAASZ,MAAA,CAAA0E,aAAA,CAAc,EAAE,CAAC;IAAA,EAAC;IAErEhF,EAAA,CAAAE,MAAA,iCACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;IAGTH,EAAA,CAAAC,cAAA,kBAIyJ;IAJtGD,EAAA,CAAAY,UAAA,mBAAAqE,gGAAA;MAAA,MAAAC,SAAA,GAAAlF,EAAA,CAAAc,aAAA,CAAAqE,IAAA,EAAAnE,SAAA;MAAA,MAAAV,MAAA,GAAAN,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAASZ,MAAA,CAAA0E,aAAA,CAAAE,SAAA,CAAoB;IAAA,EAAC;IAK/ElF,EAAA,CAAAoB,SAAA,mBAAuF;IACvFpB,EAAA,CAAAE,MAAA,GAAU;IAAAF,EAAA,CAAAC,cAAA,gBAA+C;IAAAD,EAAA,CAAAE,MAAA,GAA0B;IACrFF,EADqF,CAAAG,YAAA,EAAO,EACnF;;;;;IAJPH,EAFA,CAAAyC,WAAA,qBAAAnC,MAAA,CAAAiD,aAAA,KAAA2B,SAAA,yBAA0E,UAAA5E,MAAA,CAAAiD,aAAA,KAAA2B,SAAA,sBACd,WAAA5E,MAAA,CAAAiD,aAAA,KAAA2B,SAAA,6CACwB;IAGpFlF,EAAA,CAAAI,SAAA,GAAU;IAAVJ,EAAA,CAAAK,kBAAA,MAAA6E,SAAA,MAAU;IAA+ClF,EAAA,CAAAI,SAAA,GAA0B;IAA1BJ,EAAA,CAAAK,kBAAA,MAAAC,MAAA,CAAA8E,aAAA,CAAAF,SAAA,OAA0B;;;;;IAnBvFlF,EADF,CAAAC,cAAA,eAA6E,eACI;IAC7ED,EAAA,CAAAoB,SAAA,mBAAkF;IAClFpB,EAAA,CAAAC,cAAA,gBAAqE;IAAAD,EAAA,CAAAE,MAAA,gCAAK;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAKjFH,EAJA,CAAAU,UAAA,IAAA2E,qEAAA,mBAC+H,IAAAC,uEAAA,sBAIJ;IAG7HtF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,eAA4F;IAC1FD,EAAA,CAAAU,UAAA,IAAA6E,uEAAA,sBAIyJ;IAK7JvF,EADE,CAAAG,YAAA,EAAM,EACF;;;;IAnBKH,EAAA,CAAAI,SAAA,GAAmB;IAAnBJ,EAAA,CAAAuB,UAAA,SAAAjB,MAAA,CAAAiD,aAAA,CAAmB;IAIHvD,EAAA,CAAAI,SAAA,EAAmB;IAAnBJ,EAAA,CAAAuB,UAAA,SAAAjB,MAAA,CAAAiD,aAAA,CAAmB;IAMFvD,EAAA,CAAAI,SAAA,GAAS;IAATJ,EAAA,CAAAuB,UAAA,YAAAjB,MAAA,CAAAkF,MAAA,CAAS;;;;;IAcrDxF,EAAA,CAAAC,cAAA,eAA+F;IAC7FD,EAAA,CAAAoB,SAAA,mBAAkG;IAClGpB,EAAA,CAAAC,cAAA,YAA2C;IAAAD,EAAA,CAAAE,MAAA,2CAAM;IACnDF,EADmD,CAAAG,YAAA,EAAI,EACjD;;;;;IAgBQH,EAAA,CAAAC,cAAA,gBAIkK;IACxKD,EAAA,CAAAoB,SAAA,mBAAuF;IACvFpB,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAJLH,EAFA,CAAAyC,WAAA,qBAAAnC,MAAA,CAAAmF,mBAAA,CAAAC,aAAA,CAAAC,OAAA,wCAAuG,UAAArF,MAAA,CAAAmF,mBAAA,CAAAC,aAAA,CAAAC,OAAA,uBAC5B,WAAArF,MAAA,CAAAmF,mBAAA,CAAAC,aAAA,CAAAC,OAAA,6CACuB;IAGlG3F,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAqF,aAAA,CAAAjF,KAAA,MACF;;;;;IACAT,EAAA,CAAAC,cAAA,eACsN;IACpND,EAAA,CAAAE,MAAA,eACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;;IAxB8DH,EAAA,CAAAC,cAAA,kBASmL;IAPzOD,EAAA,CAAAY,UAAA,mBAAAgF,+GAAA;MAAA5F,EAAA,CAAAc,aAAA,CAAA+E,IAAA;MAAA,MAAAH,aAAA,GAAA1F,EAAA,CAAAiB,aAAA,GAAAD,SAAA;MAAA,MAAAV,MAAA,GAAAN,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAASZ,MAAA,CAAAwF,iBAAA,CAAAJ,aAAA,CAAAC,OAAA,CAAoC;IAAA,EAAC;IAQ5D3F,EAAA,CAAAC,cAAA,gBACkE;IAChED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAQPH,EARQ,CAAAU,UAAA,IAAAqF,6FAAA,oBAIkK,IAAAC,4FAAA,mBAK4C;IAGxNhG,EAAA,CAAAG,YAAA,EAAS;;;;;IAjBPH,EAJA,CAAAyC,WAAA,qBAAAnC,MAAA,CAAAmF,mBAAA,CAAAC,aAAA,CAAAC,OAAA,gBAAArF,MAAA,CAAA2F,mBAAA,CAAAP,aAAA,CAAAC,OAAA,uBAA6I,UAAArF,MAAA,CAAAmF,mBAAA,CAAAC,aAAA,CAAAC,OAAA,aAAArF,MAAA,CAAA2F,mBAAA,CAAAP,aAAA,CAAAC,OAAA,0BACX,WAAArF,MAAA,CAAAmF,mBAAA,CAAAC,aAAA,CAAAC,OAAA,0BAAArF,MAAA,CAAA2F,mBAAA,CAAAP,aAAA,CAAAC,OAAA,8CACkC,YAAArF,MAAA,CAAA4F,mBAAA,CAAAR,aAAA,CAAAC,OAAA,gBAC9F,WAAArF,MAAA,CAAA4F,mBAAA,CAAAR,aAAA,CAAAC,OAAA,8BACa;IALnF3F,EAAA,CAAAuB,UAAA,aAAAjB,MAAA,CAAA4F,mBAAA,CAAAR,aAAA,CAAAC,OAAA,EAAmD;IAO7C3F,EAAA,CAAAI,SAAA,EAA0F;IAA1FJ,EAAA,CAAAyC,WAAA,oBAAAnC,MAAA,CAAA2F,mBAAA,CAAAP,aAAA,CAAAC,OAAA,4BAA0F;IAE9F3F,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAqF,aAAA,CAAApE,SAAA,MACF;IAAetB,EAAA,CAAAI,SAAA,EAAqB;IAArBJ,EAAA,CAAAuB,UAAA,SAAAmE,aAAA,CAAAjF,KAAA,CAAqB;IAQ9BT,EAAA,CAAAI,SAAA,EAA4C;IAA5CJ,EAAA,CAAAuB,UAAA,SAAAjB,MAAA,CAAA2F,mBAAA,CAAAP,aAAA,CAAAC,OAAA,EAA4C;;;;;IArBtD3F,EAAA,CAAAyB,uBAAA,GAAuE;IAACzB,EAAA,CAAAU,UAAA,IAAAyF,sFAAA,uBASmL;;;;;;IAT1KnG,EAAA,CAAAI,SAAA,EACmB;IADnBJ,EAAA,CAAAuB,UAAA,WAAAjB,MAAA,CAAAiD,aAAA,IAAAmC,aAAA,CAAAjF,KAAA,KAAAH,MAAA,CAAAiD,aAAA,OAAAjD,MAAA,CAAA6D,UAAA,IAAAuB,aAAA,CAAApE,SAAA,CAAA8E,WAAA,GAAAC,QAAA,CAAA/F,MAAA,CAAA6D,UAAA,CAAAiC,WAAA,KACmB;;;;;IAHtGpG,EAAA,CAAAC,cAAA,eACgG;IAC9FD,EAAA,CAAAU,UAAA,IAAA4F,6EAAA,4BAAuE;IA2BzEtG,EAAA,CAAAG,YAAA,EAAM;;;;IA3BgCH,EAAA,CAAAI,SAAA,EAAiC;IAAjCJ,EAAA,CAAAuB,UAAA,YAAAjB,MAAA,CAAAiG,YAAA,CAAAjG,MAAA,CAAAgD,gBAAA,EAAiC;;;;;IA4BvEtD,EAAA,CAAAC,cAAA,eAEkE;IAChED,EAAA,CAAAoB,SAAA,mBAA0G;IAC1GpB,EAAA,CAAAC,cAAA,YAA2C;IAAAD,EAAA,CAAAE,MAAA,mEAAU;IACvDF,EADuD,CAAAG,YAAA,EAAI,EACrD;;;;;IAjLNH,EAHN,CAAAC,cAAA,cAAgF,cAChD,cACS,cACP;IAC1BD,EAAA,CAAAoB,SAAA,kBAA6D;IAC7DpB,EAAA,CAAAC,cAAA,aAAyB;IAAAD,EAAA,CAAAE,MAAA,+BAAI;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAClCH,EAAA,CAAAC,cAAA,eAAmC;IAAAD,EAAA,CAAAE,MAAA,GAAsB;IAE7DF,EAF6D,CAAAG,YAAA,EAAO,EAC5D,EACF;IACNH,EAAA,CAAAC,cAAA,cAAqC;IA+CnCD,EA9CA,CAAAU,UAAA,KAAA8F,8DAAA,mBAGuC,KAAAC,8DAAA,kBA2CiB;IAK5DzG,EADE,CAAAG,YAAA,EAAM,EACF;IAOEH,EAJR,CAAAC,cAAA,eAA6D,eACQ,eACoC,eACxC,cACqB;IAAAD,EAAA,CAAAE,MAAA,gCAAI;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAMvFH,EALA,CAAAU,UAAA,KAAAgG,+DAAA,mBAC+H,KAAAC,+DAAA,mBAKA;IAIjI3G,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAU,UAAA,KAAAkG,8DAAA,kBAEmC;IAgBrC5G,EAAA,CAAAG,YAAA,EAAM;IAeNH,EAZA,CAAAU,UAAA,KAAAmG,8DAAA,kBAAsE,KAAAC,8DAAA,kBAYO;IAwB/E9G,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAAC,cAAA,eAAuD;IAmCrDD,EAlCA,CAAAU,UAAA,KAAAqG,8DAAA,kBAA+F,KAAAC,8DAAA,kBAKC,KAAAC,8DAAA,kBA+B9B;IAMxEjH,EAFI,CAAAG,YAAA,EAAM,EACF,EACF;;;;IAjLqCH,EAAA,CAAAI,SAAA,GAAsB;IAAtBJ,EAAA,CAAAK,kBAAA,KAAAC,MAAA,CAAA8B,SAAA,CAAAS,MAAA,YAAsB;IAIjC7C,EAAA,CAAAI,SAAA,GAAY;IAAZJ,EAAA,CAAAuB,UAAA,YAAAjB,MAAA,CAAA8B,SAAA,CAAY;IA8ChCpC,EAAA,CAAAI,SAAA,EAA4B;IAA5BJ,EAAA,CAAAuB,UAAA,SAAAjB,MAAA,CAAA8B,SAAA,CAAAS,MAAA,OAA4B;IAavB7C,EAAA,CAAAI,SAAA,GAAsB;IAAtBJ,EAAA,CAAAuB,UAAA,SAAAjB,MAAA,CAAAgD,gBAAA,CAAsB;IAKtBtD,EAAA,CAAAI,SAAA,EAAmB;IAAnBJ,EAAA,CAAAuB,UAAA,SAAAjB,MAAA,CAAAiD,aAAA,CAAmB;IAOzBvD,EAAA,CAAAI,SAAA,EAAyH;IAAzHJ,EAAA,CAAAuB,UAAA,SAAAjB,MAAA,CAAA4G,gBAAA,IAAA5G,MAAA,CAAAgD,gBAAA,IAAAhD,MAAA,CAAAiG,YAAA,CAAAjG,MAAA,CAAAgD,gBAAA,KAAAhD,MAAA,CAAAiG,YAAA,CAAAjG,MAAA,CAAAgD,gBAAA,EAAAT,MAAA,KAAyH;IAoBxH7C,EAAA,CAAAI,SAAA,EAAqC;IAArCJ,EAAA,CAAAuB,UAAA,SAAAjB,MAAA,CAAA6G,WAAA,IAAA7G,MAAA,CAAAgD,gBAAA,CAAqC;IAYrCtD,EAAA,CAAAI,SAAA,EAA2C;IAA3CJ,EAAA,CAAAuB,UAAA,SAAAjB,MAAA,CAAAgD,gBAAA,IAAAhD,MAAA,CAAAkF,MAAA,CAAA3C,MAAA,KAA2C;IA4B3C7C,EAAA,CAAAI,SAAA,GAAuB;IAAvBJ,EAAA,CAAAuB,UAAA,UAAAjB,MAAA,CAAAgD,gBAAA,CAAuB;IAIvBtD,EAAA,CAAAI,SAAA,EAAqG;IAArGJ,EAAA,CAAAuB,UAAA,SAAAjB,MAAA,CAAAgD,gBAAA,IAAAhD,MAAA,CAAAiG,YAAA,CAAAjG,MAAA,CAAAgD,gBAAA,KAAAhD,MAAA,CAAAiG,YAAA,CAAAjG,MAAA,CAAAgD,gBAAA,EAAAT,MAAA,KAAqG;IA+BxG7C,EAAA,CAAAI,SAAA,EAAyH;IAAzHJ,EAAA,CAAAuB,UAAA,SAAAjB,MAAA,CAAAgD,gBAAA,MAAAhD,MAAA,CAAAiG,YAAA,CAAAjG,MAAA,CAAAgD,gBAAA,KAAAhD,MAAA,CAAAiG,YAAA,CAAAjG,MAAA,CAAAgD,gBAAA,EAAAT,MAAA,YAAAvC,MAAA,CAAA6D,UAAA,CAAyH;;;;;IAkB9HnE,EAAA,CAAAC,cAAA,cAAiF;IAC/ED,EAAA,CAAAoB,SAAA,mBAAuE;IACvEpB,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,kCAAO;IAAAF,EAAA,CAAAC,cAAA,aAAQ;IAAAD,EAAA,CAAAE,MAAA,GAAiB;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAACH,EAAA,CAAAE,MAAA,cAAC;IACnDF,EADmD,CAAAG,YAAA,EAAO,EACpD;;;;IADiBH,EAAA,CAAAI,SAAA,GAAiB;IAAjBJ,EAAA,CAAAqB,iBAAA,CAAAf,MAAA,CAAA8G,aAAA,CAAiB;;;;;IAStCpH,EAAA,CAAAC,cAAA,gBACiJ;IAC/ID,EAAA,CAAAoB,SAAA,mBAAuF;IACvFpB,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IADLH,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAC,MAAA,CAAAiD,aAAA,MACF;;;;;IAXFvD,EAAA,CAAAC,cAAA,cAAoF;IAClFD,EAAA,CAAAoB,SAAA,mBAA+D;IAC/DpB,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,iCAAM;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACnBH,EAAA,CAAAC,cAAA,gBAC+H;IAC7HD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACPH,EAAA,CAAAU,UAAA,IAAA2G,8DAAA,oBACiJ;IAInJrH,EAAA,CAAAG,YAAA,EAAM;;;;IAPFH,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAC,MAAA,CAAAgD,gBAAA,MACF;IACOtD,EAAA,CAAAI,SAAA,EAAmB;IAAnBJ,EAAA,CAAAuB,UAAA,SAAAjB,MAAA,CAAAiD,aAAA,CAAmB;;;;;IAO9BvD,EAAA,CAAAC,cAAA,eAC+G;IAC7GD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADJH,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAsH,kBAAA,sBAAAhH,MAAA,CAAA6D,UAAA,UAAA7D,MAAA,CAAAiH,0BAAA,4BACF;;;;;;IAMEvH,EAAA,CAAAC,cAAA,kBACsL;IAD5HD,EAAA,CAAAY,UAAA,mBAAA4G,mFAAA;MAAAxH,EAAA,CAAAc,aAAA,CAAA2G,IAAA;MAAA,MAAAnH,MAAA,GAAAN,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAASZ,MAAA,CAAA2B,UAAA,EAAY;IAAA,EAAC;IAE9EjC,EAAA,CAAAoB,SAAA,mBAA0C;IAC1CpB,EAAA,CAAAE,MAAA,iCACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;IACTH,EAAA,CAAAC,cAAA,kBACsL;IAD9HD,EAAA,CAAAY,UAAA,mBAAA8G,mFAAA;MAAA1H,EAAA,CAAAc,aAAA,CAAA6G,IAAA;MAAA,MAAArH,MAAA,GAAAN,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAASZ,MAAA,CAAAsH,WAAA,EAAa;IAAA,EAAC;IAE7E5H,EAAA,CAAAoB,SAAA,mBAA0C;IAC1CpB,EAAA,CAAAE,MAAA,iCACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;IA1PXH,EAHN,CAAAC,cAAA,kBAAmE,qBACjD,cACmE,cACpB;IACzDD,EAAA,CAAAoB,SAAA,kBAAkF;IAClFpB,EAAA,CAAAC,cAAA,eAAoE;IAAAD,EAAA,CAAAE,MAAA,+BAAI;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC/EH,EAAA,CAAAC,cAAA,eAAmD;IAAAD,EAAA,CAAAE,MAAA,GAA0B;IAC/EF,EAD+E,CAAAG,YAAA,EAAO,EAChF;IACNH,EAAA,CAAAC,cAAA,eAAmD;IAAAD,EAAA,CAAAE,MAAA,IAA2B;IAElFF,EAFkF,CAAAG,YAAA,EAAO,EACjF,EACS;IAEjBH,EAAA,CAAAC,cAAA,wBAAoD;IAWlDD,EATA,CAAAU,UAAA,KAAAmH,uDAAA,kBACmG,KAAAC,uDAAA,oBAQnB;IAwLlF9H,EAAA,CAAAG,YAAA,EAAe;IAMTH,EAJN,CAAAC,cAAA,0BAAiG,eAEO,eACH,eACpC;IACzDD,EAAA,CAAAoB,SAAA,mBAA2E;IAC3EpB,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAE,MAAA,4BAAK;IAAAF,EAAA,CAAAC,cAAA,cAAQ;IAAAD,EAAA,CAAAE,MAAA,IAAsB;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAACH,EAAA,CAAAE,MAAA,2BAAG;IACxDF,EADwD,CAAAG,YAAA,EAAO,EACzD;IAKNH,EAJA,CAAAU,UAAA,KAAAqH,uDAAA,kBAAiF,KAAAC,uDAAA,kBAIG;IAatFhI,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAU,UAAA,KAAAuH,uDAAA,kBAC+G;IAGjHjI,EAAA,CAAAG,YAAA,EAAM;IAIJH,EADF,CAAAC,cAAA,eAA2F,eACnD;IAMpCD,EALA,CAAAU,UAAA,KAAAwH,0DAAA,qBACsL,KAAAC,0DAAA,qBAKA;IAIxLnI,EAAA,CAAAG,YAAA,EAAM;IAGJH,EADF,CAAAC,cAAA,eAAsC,kBAEoG;IADlHD,EAAA,CAAAY,UAAA,mBAAAwH,0EAAA;MAAA,MAAAC,OAAA,GAAArI,EAAA,CAAAc,aAAA,CAAAwH,GAAA,EAAAC,SAAA;MAAA,OAAAvI,EAAA,CAAAkB,WAAA,CAASmH,OAAA,CAAAG,KAAA,EAAW;IAAA,EAAC;IAEzCxI,EAAA,CAAAE,MAAA,sBACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,kBACwM;IADlLD,EAAA,CAAAY,UAAA,mBAAA6H,0EAAA;MAAA,MAAAJ,OAAA,GAAArI,EAAA,CAAAc,aAAA,CAAAwH,GAAA,EAAAC,SAAA;MAAA,OAAAvI,EAAA,CAAAkB,WAAA,CAASmH,OAAA,CAAAG,KAAA,EAAW;IAAA,EAAC;IAEzCxI,EAAA,CAAAoB,SAAA,mBAA4C;IAC5CpB,EAAA,CAAAE,MAAA,IACF;IAIRF,EAJQ,CAAAG,YAAA,EAAS,EACL,EACF,EACS,EACT;;;;IAvQiDH,EAAA,CAAAI,SAAA,GAA0B;IAA1BJ,EAAA,CAAAK,kBAAA,MAAAC,MAAA,CAAA8B,SAAA,CAAAS,MAAA,yBAA0B;IAE5B7C,EAAA,CAAAI,SAAA,GAA2B;IAA3BJ,EAAA,CAAAK,kBAAA,yBAAAC,MAAA,CAAA6B,gBAAA,OAA2B;IAM1EnC,EAAA,CAAAI,SAAA,GAAe;IAAfJ,EAAA,CAAAuB,UAAA,SAAAjB,MAAA,CAAAoI,SAAA,CAAe;IASf1I,EAAA,CAAAI,SAAA,EAAgB;IAAhBJ,EAAA,CAAAuB,UAAA,UAAAjB,MAAA,CAAAoI,SAAA,CAAgB;IAgMG1I,EAAA,CAAAI,SAAA,GAAsB;IAAtBJ,EAAA,CAAAqB,iBAAA,CAAAf,MAAA,CAAA6B,gBAAA,GAAsB;IAErCnC,EAAA,CAAAI,SAAA,GAAmB;IAAnBJ,EAAA,CAAAuB,UAAA,SAAAjB,MAAA,CAAA8G,aAAA,CAAmB;IAInBpH,EAAA,CAAAI,SAAA,EAAsB;IAAtBJ,EAAA,CAAAuB,UAAA,SAAAjB,MAAA,CAAAgD,gBAAA,CAAsB;IAcxBtD,EAAA,CAAAI,SAAA,EAAgB;IAAhBJ,EAAA,CAAAuB,UAAA,SAAAjB,MAAA,CAAA6D,UAAA,CAAgB;IASGnE,EAAA,CAAAI,SAAA,GAAiC;IAAjCJ,EAAA,CAAAuB,UAAA,SAAAjB,MAAA,CAAAqI,gBAAA,CAAA9F,MAAA,KAAiC;IAKjC7C,EAAA,CAAAI,SAAA,EAA+B;IAA/BJ,EAAA,CAAAuB,UAAA,SAAAjB,MAAA,CAAA6G,WAAA,IAAA7G,MAAA,CAAA6D,UAAA,CAA+B;IAepDnE,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAK,kBAAA,gCAAAC,MAAA,CAAA6B,gBAAA,SACF;;;AD/RV,OAAM,MAAOyG,yBAAyB;EA2BpCC,YACUC,GAAsB,EACtBC,kBAAsC,EACtCC,aAA8B;IAF9B,KAAAF,GAAG,GAAHA,GAAG;IACH,KAAAC,kBAAkB,GAAlBA,kBAAkB;IAClB,KAAAC,aAAa,GAAbA,aAAa;IA3Bd,KAAA3G,WAAW,GAAW,OAAO;IAC7B,KAAA+E,aAAa,GAAkB,IAAI;IACnC,KAAA5F,QAAQ,GAAY,KAAK;IACzB,KAAAyH,WAAW,GAAkB,IAAI,CAAC,CAAC;IACnC,KAAA1C,YAAY,GAAiB,EAAE;IAAW,KAAA2C,gBAAgB,GAAY,IAAI;IAC1E,KAAA/B,WAAW,GAAY,IAAI;IAC3B,KAAAD,gBAAgB,GAAY,IAAI;IAChC,KAAAiC,gBAAgB,GAAa,EAAE,CAAC,CAAC;IAEhC,KAAAC,eAAe,GAAG,IAAIvJ,YAAY,EAAmB;IACrD,KAAAwJ,aAAa,GAAG,IAAIxJ,YAAY,EAAY,CAAC,CAAC;IACxD,KAAAyJ,MAAM,GAAG,KAAK;IACd,KAAAhG,gBAAgB,GAAG,EAAE;IACrB,KAAAa,UAAU,GAAG,EAAE;IACf,KAAAZ,aAAa,GAAG,EAAE,CAAC,CAAC;IACpB,KAAAoF,gBAAgB,GAAa,EAAE,CAAC,CAAC;IACjC,KAAAvG,SAAS,GAAa,EAAE;IACxB,KAAAoD,MAAM,GAAa,EAAE,CAAC,CAAC;IACvB,KAAA+D,kBAAkB,GAAa,EAAE,CAAC,CAAE;IACpC,KAAAC,kBAAkB,GAAqC,EAAE,CAAC,CAAC;IAC3D,KAAAd,SAAS,GAAY,KAAK,CAAC,CAAC;IAC5B;IACQ,KAAAe,QAAQ,GAAIC,KAAe,IAAI,CAAG,CAAC;IACnC,KAAAC,SAAS,GAAG,MAAK,CAAG,CAAC;EAKzB;EAAEC,UAAUA,CAACF,KAAY;IAC3BG,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEJ,KAAK,EAAE,OAAO,EAAE,OAAOA,KAAK,GAAG,CAAC,CAAC,CAAC;IAEzE,IAAI,CAACA,KAAK,IAAIA,KAAK,CAAC7G,MAAM,KAAK,CAAC,EAAE;MAChC,IAAI,CAAC8F,gBAAgB,GAAG,EAAE;IAC5B,CAAC,MAAM;MACL;MACA,MAAMoB,SAAS,GAAGL,KAAK,CAAC,CAAC,CAAC;MAC1B,IAAI,OAAOK,SAAS,KAAK,QAAQ,EAAE;QACjC;QACA,IAAI,CAACpB,gBAAgB,GAAGe,KAAiB;QACzCG,OAAO,CAACC,GAAG,CAAC,kBAAkB,CAAC;MACjC,CAAC,MAAM,IAAI,OAAOC,SAAS,KAAK,QAAQ,EAAE;QACxC;QACAF,OAAO,CAACG,KAAK,CAAC,sCAAsC,CAAC;QACrDH,OAAO,CAACG,KAAK,CAAC,mBAAmB,CAAC;QAClCH,OAAO,CAACG,KAAK,CAAC,wBAAwB,EAAEN,KAAK,CAAC;QAE9C;QACAG,OAAO,CAACG,KAAK,CAAC,4BAA4B,CAAC;QAC3C;MACF,CAAC,MAAM;QACLH,OAAO,CAACG,KAAK,CAAC,uBAAuB,EAAEN,KAAK,CAAC;QAC7C,IAAI,CAACf,gBAAgB,GAAG,EAAE;MAC5B;IACF;IAEAkB,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAE,IAAI,CAACnB,gBAAgB,CAAC;IAC9D,IAAI,CAACsB,wBAAwB,EAAE;EACjC;EAEAC,gBAAgBA,CAACC,EAA6B;IAC5C,IAAI,CAACV,QAAQ,GAAGU,EAAE;EACpB;EAEAC,iBAAiBA,CAACD,EAAc;IAC9B,IAAI,CAACR,SAAS,GAAGQ,EAAE;EACrB;EACAE,gBAAgBA,CAACC,UAAmB;IAClC,IAAI,CAAC9I,QAAQ,GAAG8I,UAAU;EAC5B;EAAEC,QAAQA,CAAA;IACR,IAAI,CAACC,cAAc,EAAE;EACvB;EACAC,WAAWA,CAACC,OAAsB;IAChC,IAAIA,OAAO,CAAC,aAAa,CAAC,IAAI,IAAI,CAACzB,WAAW,EAAE;MAC9C;MACA,IAAI,CAACuB,cAAc,EAAE;IACvB;IACA,IAAIE,OAAO,CAAC,cAAc,CAAC,EAAE;MAC3B;MACA,IAAI,CAACtI,SAAS,GAAGuI,MAAM,CAACC,IAAI,CAAC,IAAI,CAACrE,YAAY,IAAI,EAAE,CAAC;MACrDsD,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAE,IAAI,CAACvD,YAAY,CAAC;MACvD,IAAI,CAACsE,wBAAwB,EAAE;MAC/B,IAAI,CAACZ,wBAAwB,EAAE;IACjC;IAAE,IAAIS,OAAO,CAAC,kBAAkB,CAAC,EAAE;MACjC;MACA,IAAI,CAACG,wBAAwB,EAAE;MAC/BhB,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAE,IAAI,CAACX,gBAAgB,CAAC;IACnE;EACF;EAAUqB,cAAcA,CAAA;IACtB;IACA,IAAI,IAAI,CAACjE,YAAY,IAAIoE,MAAM,CAACC,IAAI,CAAC,IAAI,CAACrE,YAAY,CAAC,CAAC1D,MAAM,GAAG,CAAC,EAAE;MAClE;MACA,IAAI,CAACT,SAAS,GAAGuI,MAAM,CAACC,IAAI,CAAC,IAAI,CAACrE,YAAY,CAAC;MAC/CsD,OAAO,CAACC,GAAG,CAAC,mDAAmD,EAAE,IAAI,CAAC1H,SAAS,CAAC;MAChF,IAAI,CAAC6H,wBAAwB,EAAE;IACjC,CAAC,MAAM,IAAI,IAAI,CAAChB,WAAW,EAAE;MAC3B;MACA,IAAI,CAAC6B,uBAAuB,EAAE;IAChC,CAAC,MAAM;MACL;MACA,IAAI,CAAC1I,SAAS,GAAG,EAAE;MACnByH,OAAO,CAACC,GAAG,CAAC,yCAAyC,CAAC;IACxD;EACF;EAEQgB,uBAAuBA,CAAA;IAC7B,IAAI,CAAC,IAAI,CAAC7B,WAAW,EAAE;IAEvB,IAAI,CAACP,SAAS,GAAG,IAAI;IACrB,IAAI,CAACK,kBAAkB,CAACgC,WAAW,CAAC,IAAI,CAAC9B,WAAW,CAAC,CAAC+B,SAAS,CAAC;MAC9DC,IAAI,EAAGC,QAAQ,IAAI;QACjBrB,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEoB,QAAQ,CAAC;QACtC,IAAI,CAAC3E,YAAY,GAAG,IAAI,CAAC4E,gCAAgC,CAACD,QAAQ,CAACE,OAAO,CAAC;QAC3E,IAAI,CAAChJ,SAAS,GAAGuI,MAAM,CAACC,IAAI,CAAC,IAAI,CAACrE,YAAY,CAAC;QAC/C,IAAI,CAAC0D,wBAAwB,EAAE;QAC/B,IAAI,CAACvB,SAAS,GAAG,KAAK;QACtB,IAAI,CAACI,GAAG,CAACuC,aAAa,EAAE;MAC1B,CAAC;MAAErB,KAAK,EAAGA,KAAK,IAAI;QAClBH,OAAO,CAACG,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;QACpD;QACA,IAAI,CAACzD,YAAY,GAAG,EAAE;QACtB,IAAI,CAACnE,SAAS,GAAG,EAAE;QACnB,IAAI,CAACsG,SAAS,GAAG,KAAK;QACtB,IAAI,CAACI,GAAG,CAACuC,aAAa,EAAE;MAC1B;KACD,CAAC;EACJ;EACQF,gCAAgCA,CAACG,OAAiC;IACxE,MAAM/E,YAAY,GAAiB,EAAE;IAErCoE,MAAM,CAACW,OAAO,CAACA,OAAO,CAAC,CAACC,OAAO,CAAC,CAAC,CAACC,QAAQ,EAAEC,MAAM,CAAC,KAAI;MACrDlF,YAAY,CAACiF,QAAQ,CAAC,GAAGC,MAAM,CAACC,GAAG,CAACC,KAAK,KAAK;QAC5CrK,SAAS,EAAEqK,KAAK,CAACrK,SAAS,IAAIqK,KAAK,CAACC,SAAS,IAAID,KAAK,CAACE,IAAI;QAC3DL,QAAQ,EAAEG,KAAK,CAACH,QAAQ,IAAIG,KAAK,CAACG,QAAQ,IAAIN,QAAQ;QACtD/K,KAAK,EAAEkL,KAAK,CAAClL,KAAK,IAAIkL,KAAK,CAACI,KAAK;QACjCpG,OAAO,EAAEgG,KAAK,CAAChG,OAAO,IAAIgG,KAAK,CAACK,OAAO;QACvCC,UAAU,EAAE,KAAK;QACjB3B,UAAU,EAAE;OACb,CAAC,CAAC;IACL,CAAC,CAAC;IAEF,OAAO/D,YAAY;EACrB;EAAU0D,wBAAwBA,CAAA;IAChC,MAAMiC,OAAO,GAAqC,EAAE;IAEpD,IAAI,CAACvD,gBAAgB,CAAC4C,OAAO,CAAC5F,OAAO,IAAG;MACtC,KAAK,MAAM6F,QAAQ,IAAI,IAAI,CAACpJ,SAAS,EAAE;QACrC,MAAM+J,IAAI,GAAG,IAAI,CAAC5F,YAAY,CAACiF,QAAQ,CAAC,EAAEY,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC1G,OAAO,KAAKA,OAAO,CAAC;QAC1E,IAAIwG,IAAI,EAAE;UACR,IAAI,CAACD,OAAO,CAACV,QAAQ,CAAC,EAAEU,OAAO,CAACV,QAAQ,CAAC,GAAG,EAAE;UAC9CU,OAAO,CAACV,QAAQ,CAAC,CAACc,IAAI,CAAC3G,OAAO,CAAC;UAC/B;QACF;MACF;IACF,CAAC,CAAC;IAEF,IAAI,CAAC6D,kBAAkB,GAAG0C,OAAO;EACnC;EAAEhJ,gBAAgBA,CAACsI,QAAgB;IACjC3B,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAE0B,QAAQ,CAAC;IAC3C,IAAI,CAAClI,gBAAgB,GAAGkI,QAAQ;IAChC,IAAI,CAACjI,aAAa,GAAG,EAAE,CAAC,CAAC;IACzB,IAAI,CAACY,UAAU,GAAG,EAAE;IACpB,IAAI,CAACoI,uBAAuB,EAAE,CAAC,CAAC;IAChC,IAAI,CAAC1B,wBAAwB,EAAE;IAC/BhB,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAE,IAAI,CAACP,kBAAkB,CAAC1G,MAAM,CAAC;IACzE;IACA,IAAI,CAACiG,GAAG,CAACuC,aAAa,EAAE;EAC1B;EAEAmB,eAAeA,CAAChB,QAAgB;IAC9B3B,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAE0B,QAAQ,CAAC;EACxD;EAAEX,wBAAwBA,CAAA;IACxBhB,OAAO,CAACC,GAAG,CAAC,4CAA4C,EAAE,IAAI,CAACxG,gBAAgB,EAAE,QAAQ,EAAE,IAAI,CAACC,aAAa,CAAC;IAC9G,IAAI,CAAC,IAAI,CAACD,gBAAgB,EAAE;MAC1B,IAAI,CAACiG,kBAAkB,GAAG,EAAE;MAC5B;IACF;IAEA,MAAMkD,UAAU,GAAG,IAAI,CAAClG,YAAY,CAAC,IAAI,CAACjD,gBAAgB,CAAC,IAAI,EAAE;IACjEuG,OAAO,CAACC,GAAG,CAAC,oCAAoC,EAAE2C,UAAU,CAAC5J,MAAM,CAAC;IAEpE;IACA,MAAM6J,aAAa,GAAGD,UAAU,CAACE,MAAM,CAACN,CAAC,IAAG;MAC1C;MACA,MAAMO,UAAU,GAAG,CAAC,IAAI,CAACrJ,aAAa,IAAI8I,CAAC,CAAC5L,KAAK,KAAK,IAAI,CAAC8C,aAAa;MACxE;MACA,MAAMsJ,WAAW,GAAG,CAAC,IAAI,CAAC1I,UAAU,IAAIkI,CAAC,CAAC/K,SAAS,CAAC8E,WAAW,EAAE,CAACC,QAAQ,CAAC,IAAI,CAAClC,UAAU,CAACiC,WAAW,EAAE,CAAC;MACzG,OAAOwG,UAAU,IAAIC,WAAW;IAClC,CAAC,CAAC;IAEF;IACA,IAAI,CAACtD,kBAAkB,GAAGmD,aAAa,CAAChB,GAAG,CAACW,CAAC,IAAIA,CAAC,CAAC/K,SAAS,CAAC;IAE7DuI,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAE,IAAI,CAACP,kBAAkB,CAAC1G,MAAM,CAAC;IAC1EgH,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAE4C,aAAa,CAAChB,GAAG,CAACW,CAAC,IAAI,GAAGA,CAAC,CAAC/K,SAAS,IAAI+K,CAAC,CAAC5L,KAAK,QAAQ4L,CAAC,CAAC1G,OAAO,GAAG,CAAC,CAAC;EAC/G;EAEAjB,cAAcA,CAACoI,KAAU;IACvB,IAAI,CAAC3I,UAAU,GAAG2I,KAAK,CAACC,MAAM,CAACrD,KAAK;IACpC,IAAI,CAACmB,wBAAwB,EAAE;IAC/BhB,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAE,IAAI,CAAC3F,UAAU,CAAC;EACtD;EACAyD,WAAWA,CAAA;IACT,IAAI,CAACzD,UAAU,GAAG,EAAE;IACpB,IAAI,CAAC0G,wBAAwB,EAAE;IAC/BhB,OAAO,CAACC,GAAG,CAAC,cAAc,CAAC;EAC7B;EACAhE,iBAAiBA,CAACH,OAA2B;IAC3CkE,OAAO,CAACC,GAAG,CAAC,wCAAwC,EAAEnE,OAAO,CAAC;IAC9DkE,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAE,IAAI,CAACnB,gBAAgB,CAAC;IAE/D,IAAI,CAAChD,OAAO,EAAE;MACZkE,OAAO,CAACC,GAAG,CAAC,gBAAgBnE,OAAO,EAAE,CAAC;MACtC;IACF;IAEA;IACA,IAAI,IAAI,CAACqH,iBAAiB,CAACrH,OAAO,CAAC,EAAE;MACnCkE,OAAO,CAACC,GAAG,CAAC,SAASnE,OAAO,kBAAkB,CAAC;MAC/C;IACF;IAEA,MAAMsG,UAAU,GAAG,IAAI,CAACgB,iBAAiB,CAACtH,OAAO,CAAC;IAClD,IAAIuH,YAAsB;IAE1B,IAAIjB,UAAU,EAAE;MACdiB,YAAY,GAAG,IAAI,CAACvE,gBAAgB,CAACgE,MAAM,CAACQ,EAAE,IAAIA,EAAE,KAAKxH,OAAO,CAAC;IACnE,CAAC,MAAM;MACL,IAAI,IAAI,CAACyB,aAAa,IAAI,IAAI,CAACuB,gBAAgB,CAAC9F,MAAM,IAAI,IAAI,CAACuE,aAAa,EAAE;QAC5EyC,OAAO,CAACC,GAAG,CAAC,WAAW,CAAC;QACxB,OAAO,CAAC;MACV;MACAoD,YAAY,GAAG,CAAC,GAAG,IAAI,CAACvE,gBAAgB,EAAEhD,OAAO,CAAC;IACpD;IAEA,IAAI,CAACgD,gBAAgB,GAAGuE,YAAY;IACpC,IAAI,CAACE,WAAW,EAAE;EACpB;EACAjM,iBAAiBA,CAACwE,OAAe;IAC/B,IAAI,CAACgD,gBAAgB,GAAG,IAAI,CAACA,gBAAgB,CAACgE,MAAM,CAACQ,EAAE,IAAIA,EAAE,KAAKxH,OAAO,CAAC;IAC1E,IAAI,CAACyH,WAAW,EAAE;EACpB;EAAEvJ,mBAAmBA,CAAA;IACnBgG,OAAO,CAACC,GAAG,CAAC,4BAA4B,CAAC;IACzCD,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAE,IAAI,CAACxG,gBAAgB,CAAC;IACvDuG,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAE,IAAI,CAACvG,aAAa,CAAC;IACjDsG,OAAO,CAACC,GAAG,CAAC,aAAa,EAAE,IAAI,CAAC3F,UAAU,CAAC;IAE3C,IAAI,CAAC,IAAI,CAACb,gBAAgB,EAAE;MAC1BuG,OAAO,CAACC,GAAG,CAAC,sBAAsB,CAAC;MACnC;IACF;IAEA,MAAM2C,UAAU,GAAG,IAAI,CAAClG,YAAY,CAAC,IAAI,CAACjD,gBAAgB,CAAC,IAAI,EAAE;IACjEuG,OAAO,CAACC,GAAG,CAAC,oCAAoC,EAAE2C,UAAU,CAAC5J,MAAM,CAAC;IAEpE;IACA,MAAMwK,sBAAsB,GAAGZ,UAAU,CAACE,MAAM,CAACN,CAAC,IAAG;MACnD;MACA,MAAMO,UAAU,GAAG,CAAC,IAAI,CAACrJ,aAAa,IAAI8I,CAAC,CAAC5L,KAAK,KAAK,IAAI,CAAC8C,aAAa;MACxE;MACA,MAAMsJ,WAAW,GAAG,CAAC,IAAI,CAAC1I,UAAU,IAAIkI,CAAC,CAAC/K,SAAS,CAAC8E,WAAW,EAAE,CAACC,QAAQ,CAAC,IAAI,CAAClC,UAAU,CAACiC,WAAW,EAAE,CAAC;MACzG,OAAOwG,UAAU,IAAIC,WAAW;IAClC,CAAC,CAAC;IAEFhD,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAEuD,sBAAsB,CAAC3B,GAAG,CAACW,CAAC,IAAI,GAAGA,CAAC,CAAC/K,SAAS,IAAI+K,CAAC,CAAC5L,KAAK,QAAQ4L,CAAC,CAAC1G,OAAO,GAAG,CAAC,CAAC;IAExH,IAAI0H,sBAAsB,CAACxK,MAAM,KAAK,CAAC,EAAE;MACvCgH,OAAO,CAACC,GAAG,CAAC,8BAA8B,CAAC;MAC3C;IACF;IAEA;IACA,MAAMwD,YAAY,GAAG,IAAI,CAAC3E,gBAAgB,CAAC9F,MAAM;IACjD,MAAM0K,UAAU,GAAG,IAAI,CAACnG,aAAa,IAAIoG,QAAQ;IACjD,MAAMC,cAAc,GAAGF,UAAU,GAAGD,YAAY;IAEhD;IACA,MAAMI,qBAAqB,GAAa,EAAE;IAC1C,KAAK,MAAMC,SAAS,IAAIN,sBAAsB,EAAE;MAC9C,IAAIM,SAAS,CAAChI,OAAO,IACnB,CAAC,IAAI,CAACsH,iBAAiB,CAACU,SAAS,CAAChI,OAAO,CAAC,IAC1C,CAAC,IAAI,CAACqH,iBAAiB,CAACW,SAAS,CAAChI,OAAO,CAAC,EAAE;QAC5C+H,qBAAqB,CAACpB,IAAI,CAACqB,SAAS,CAAChI,OAAO,CAAC;MAC/C;IACF;IAEAkE,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAE4D,qBAAqB,CAAC;IAE9D;IACA,MAAME,KAAK,GAAGF,qBAAqB,CAACG,KAAK,CAAC,CAAC,EAAEJ,cAAc,CAAC;IAE5D,IAAIG,KAAK,CAAC/K,MAAM,GAAG,CAAC,EAAE;MACpB,IAAI,CAAC8F,gBAAgB,GAAG,CAAC,GAAG,IAAI,CAACA,gBAAgB,EAAE,GAAGiF,KAAK,CAAC;MAC5D,IAAI,CAACR,WAAW,EAAE;MAClBvD,OAAO,CAACC,GAAG,CAAC,aAAa8D,KAAK,CAAC/K,MAAM,WAAW,EAAE+K,KAAK,CAAC;IAC1D,CAAC,MAAM;MACL/D,OAAO,CAACC,GAAG,CAAC,sBAAsB,CAAC;IACrC;EACF;EAAE/F,mBAAmBA,CAAA;IACnB,IAAI,CAAC,IAAI,CAACT,gBAAgB,EAAE;IAE5B;IACA,MAAMwK,kBAAkB,GAAG,IAAI,CAACvH,YAAY,CAAC,IAAI,CAACjD,gBAAgB,CAAC,IAAI,EAAE;IAEzE;IACA,MAAMgK,YAAY,GAAG,IAAI,CAAC3E,gBAAgB,CAAC9F,MAAM;IACjD,MAAM0K,UAAU,GAAG,IAAI,CAACnG,aAAa,IAAIoG,QAAQ;IACjD,MAAMC,cAAc,GAAGF,UAAU,GAAGD,YAAY;IAEhD;IACA,MAAMS,qBAAqB,GAAa,EAAE;IAC1C,KAAK,MAAMJ,SAAS,IAAIG,kBAAkB,EAAE;MAC1C,IAAIH,SAAS,CAAChI,OAAO,IACnB,CAAC,IAAI,CAACgD,gBAAgB,CAACtC,QAAQ,CAACsH,SAAS,CAAChI,OAAO,CAAC,IAClD,CAAC,IAAI,CAACM,mBAAmB,CAAC0H,SAAS,CAAChI,OAAO,CAAC,EAAE;QAC9CoI,qBAAqB,CAACzB,IAAI,CAACqB,SAAS,CAAChI,OAAO,CAAC;MAC/C;IACF;IAEA;IACA,MAAMiI,KAAK,GAAGG,qBAAqB,CAACF,KAAK,CAAC,CAAC,EAAEJ,cAAc,CAAC;IAAE,IAAIG,KAAK,CAAC/K,MAAM,GAAG,CAAC,EAAE;MAClF,IAAI,CAAC8F,gBAAgB,GAAG,CAAC,GAAG,IAAI,CAACA,gBAAgB,EAAE,GAAGiF,KAAK,CAAC;MAC5D,IAAI,CAACR,WAAW,EAAE;MAClBvD,OAAO,CAACC,GAAG,CAAC,aAAa8D,KAAK,CAAC/K,MAAM,MAAM,CAAC;IAC9C;EACF;EAEAa,qBAAqBA,CAAA;IACnB,IAAI,CAAC,IAAI,CAACJ,gBAAgB,EAAE;IAE5B,MAAMwK,kBAAkB,GAAG,IAAI,CAACvH,YAAY,CAAC,IAAI,CAACjD,gBAAgB,CAAC,IAAI,EAAE;IACzE,MAAM0K,gBAAgB,GAAGF,kBAAkB,CAACpC,GAAG,CAACW,CAAC,IAAIA,CAAC,CAAC1G,OAAO,CAAC,CAACgH,MAAM,CAACQ,EAAE,IAAIA,EAAE,KAAKc,SAAS,CAAa;IAC1G,IAAI,CAACtF,gBAAgB,GAAG,IAAI,CAACA,gBAAgB,CAACgE,MAAM,CAACQ,EAAE,IAAI,CAACa,gBAAgB,CAAC3H,QAAQ,CAAC8G,EAAE,CAAC,CAAC;IAC1F,IAAI,CAACC,WAAW,EAAE;EACpB;EAEAnL,UAAUA,CAAA;IACR,IAAI,CAAC0G,gBAAgB,GAAG,EAAE;IAC1B,IAAI,CAACyE,WAAW,EAAE;EACpB;EAAUA,WAAWA,CAAA;IACnB,IAAI,CAACnD,wBAAwB,EAAE;IAC/BJ,OAAO,CAACC,GAAG,CAAC,sCAAsC,EAAE,IAAI,CAACnB,gBAAgB,CAAC;IAC1E,IAAI,CAACc,QAAQ,CAAC,CAAC,GAAG,IAAI,CAACd,gBAAgB,CAAC,CAAC;IACzC,IAAI,CAACgB,SAAS,EAAE;IAEhB,MAAMuE,aAAa,GAAG,IAAI,CAACvF,gBAAgB,CAAC+C,GAAG,CAAC/F,OAAO,IAAG;MACxD,KAAK,MAAM6F,QAAQ,IAAI,IAAI,CAACpJ,SAAS,EAAE;QACrC,MAAM+J,IAAI,GAAG,IAAI,CAAC5F,YAAY,CAACiF,QAAQ,CAAC,EAAEY,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC1G,OAAO,KAAKA,OAAO,CAAC;QAC1E,IAAIwG,IAAI,EAAE,OAAOA,IAAI;MACvB;MACA,OAAO,IAAI;IACb,CAAC,CAAC,CAACQ,MAAM,CAACR,IAAI,IAAIA,IAAI,KAAK,IAAI,CAAoB;IAEnDtC,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEoE,aAAa,CAAC;IACrD,IAAI,CAAC9E,eAAe,CAAC+E,IAAI,CAACD,aAAa,CAAC;IAExC;IACA,MAAME,QAAQ,GAAGF,aAAa,CAACxC,GAAG,CAACS,IAAI,IAAIA,IAAI,CAACxG,OAAQ,CAAC,CAACgH,MAAM,CAACQ,EAAE,IAAIA,EAAE,KAAKc,SAAS,CAAC;IACxFpE,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEsE,QAAQ,CAAC;IAC3C,IAAI,CAAC/E,aAAa,CAAC8E,IAAI,CAACC,QAAQ,CAAC;EACnC;EAAEC,cAAcA,CAAA;IACd,IAAI,CAAC,IAAI,CAAC7M,QAAQ,EAAE;MAClB,IAAI,CAAC8M,UAAU,EAAE;MACjBzE,OAAO,CAACC,GAAG,CAAC,oCAAoC,CAAC;MACjDD,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAE,IAAI,CAAC1H,SAAS,CAAC;IACrD;EACF;EAEAkM,UAAUA,CAAA;IACR,IAAI,CAACtF,aAAa,CAACuF,IAAI,CAAC,IAAI,CAACC,eAAe,EAAE;MAC5CC,OAAO,EAAE,EAAE;MACXC,oBAAoB,EAAE,KAAK;MAC3BC,UAAU,EAAE,IAAI;MAChBC,SAAS,EAAE;KACZ,CAAC;EACJ;EAEAC,aAAaA,CAAA;IACX;IACA;EAAA;EAEFpJ,mBAAmBA,CAACE,OAA2B;IAC7C,IAAI,CAACA,OAAO,EAAE,OAAO,KAAK;IAC1B,OAAO,IAAI,CAACgD,gBAAgB,CAACtC,QAAQ,CAACV,OAAO,CAAC;EAChD;EAEAM,mBAAmBA,CAACN,OAA2B;IAC7C,IAAI,CAACA,OAAO,EAAE,OAAO,KAAK;IAC1B,OAAO,IAAI,CAACwD,gBAAgB,CAAC9C,QAAQ,CAACV,OAAO,CAAC;EAChD;EAEAO,mBAAmBA,CAACP,OAA2B;IAC7C,IAAI,CAACA,OAAO,EAAE,OAAO,IAAI;IACzB,OAAO,IAAI,CAACM,mBAAmB,CAACN,OAAO,CAAC,IACrC,CAAC,IAAI,CAAC1B,aAAa,EAAE,IAAI,CAAC,IAAI,CAACwB,mBAAmB,CAACE,OAAO,CAAE;EACjE;EACA1B,aAAaA,CAAA;IACX,OAAO,CAAC,IAAI,CAACmD,aAAa,IAAI,IAAI,CAACuB,gBAAgB,CAAC9F,MAAM,GAAG,IAAI,CAACuE,aAAa;EACjF;EAAE0H,qBAAqBA,CAAA;IACrB,IAAI,CAAC,IAAI,CAACxL,gBAAgB,EAAE,OAAO,KAAK;IACxC,MAAMwK,kBAAkB,GAAG,IAAI,CAACvH,YAAY,CAAC,IAAI,CAACjD,gBAAgB,CAAC,CAChEqJ,MAAM,CAACN,CAAC,IAAI,CAACA,CAAC,CAAC/B,UAAU,IAAI+B,CAAC,CAAC1G,OAAO,KAAKsI,SAAS,CAAC;IACxD,OAAOH,kBAAkB,CAACjL,MAAM,GAAG,CAAC,IAClCiL,kBAAkB,CAACiB,KAAK,CAACpB,SAAS,IAAIA,SAAS,CAAChI,OAAO,IAAI,IAAI,CAACgD,gBAAgB,CAACtC,QAAQ,CAACsH,SAAS,CAAChI,OAAO,CAAC,CAAC;EACjH;EAEAzB,sBAAsBA,CAAA;IACpB,IAAI,CAAC,IAAI,CAACZ,gBAAgB,EAAE,OAAO,KAAK;IACxC,MAAMwK,kBAAkB,GAAG,IAAI,CAACvH,YAAY,CAAC,IAAI,CAACjD,gBAAgB,CAAC,IAAI,EAAE;IACzE,OAAOwK,kBAAkB,CAACkB,IAAI,CAACrB,SAAS,IAAIA,SAAS,CAAChI,OAAO,IAAI,IAAI,CAACgD,gBAAgB,CAACtC,QAAQ,CAACsH,SAAS,CAAChI,OAAO,CAAC,CAAC;EACrH;EAAEsJ,qBAAqBA,CAAA;IACrB,OAAO,IAAI,CAACzF,kBAAkB;EAChC;EAEAzG,gBAAgBA,CAACyI,QAAgB;IAC/B,OAAO,IAAI,CAACjF,YAAY,CAACiF,QAAQ,CAAC,EAAE3I,MAAM,IAAI,CAAC;EACjD;EAEAV,gBAAgBA,CAAA;IACd,OAAO,IAAI,CAACwG,gBAAgB,CAAC9F,MAAM;EACrC;EAEA;EACAjB,2BAA2BA,CAAC4J,QAAgB;IAC1C,OAAO,IAAI,CAAChC,kBAAkB,CAACgC,QAAQ,CAAC,IAAI,EAAE;EAChD;EAEA;EACA1J,mBAAmBA,CAAC0J,QAAgB;IAClC,OAAO,CAAC,EAAE,IAAI,CAAChC,kBAAkB,CAACgC,QAAQ,CAAC,IAAI,IAAI,CAAChC,kBAAkB,CAACgC,QAAQ,CAAC,CAAC3I,MAAM,GAAG,CAAC,CAAC;EAC9F;EAEA;EACQ0J,uBAAuBA,CAAA;IAC7B,IAAI,CAAC,IAAI,CAACjJ,gBAAgB,EAAE;MAC1B,IAAI,CAACkC,MAAM,GAAG,EAAE;MAChB;IACF;IAEA,MAAMiH,UAAU,GAAG,IAAI,CAAClG,YAAY,CAAC,IAAI,CAACjD,gBAAgB,CAAC,IAAI,EAAE;IACjE,MAAM4L,QAAQ,GAAG,IAAIC,GAAG,EAAU;IAElC1C,UAAU,CAAClB,OAAO,CAACoC,SAAS,IAAG;MAC7B,IAAIA,SAAS,CAAClN,KAAK,EAAE;QACnByO,QAAQ,CAACE,GAAG,CAACzB,SAAS,CAAClN,KAAK,CAAC;MAC/B;IACF,CAAC,CAAC;IAEF;IACA,IAAI,CAAC+E,MAAM,GAAG6J,KAAK,CAACC,IAAI,CAACJ,QAAQ,CAAC,CAACK,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAI;MAC/C,MAAMC,IAAI,GAAGC,QAAQ,CAACH,CAAC,CAACI,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC,CAAC;MAC/C,MAAMC,IAAI,GAAGF,QAAQ,CAACF,CAAC,CAACG,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC,CAAC;MAC/C,OAAOF,IAAI,GAAGG,IAAI;IACpB,CAAC,CAAC;IAEFhG,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAE,IAAI,CAACxG,gBAAgB,EAAE,IAAI,CAACkC,MAAM,CAAC;EACjF;EAEA;EACAR,aAAaA,CAACvE,KAAa;IACzBoJ,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAErJ,KAAK,CAAC;IACrC,IAAI,CAAC8C,aAAa,GAAG,IAAI,CAACA,aAAa,KAAK9C,KAAK,GAAG,EAAE,GAAGA,KAAK,CAAC,CAAC;IAChE,IAAI,CAACoK,wBAAwB,EAAE;IAC/B,IAAI,CAAC/B,GAAG,CAACuC,aAAa,EAAE;EAC1B;EAEA;EACAjG,aAAaA,CAAC3E,KAAa;IACzB,IAAI,CAAC,IAAI,CAAC6C,gBAAgB,EAAE,OAAO,CAAC;IACpC,MAAMmJ,UAAU,GAAG,IAAI,CAAClG,YAAY,CAAC,IAAI,CAACjD,gBAAgB,CAAC,IAAI,EAAE;IACjE,OAAOmJ,UAAU,CAACE,MAAM,CAACN,CAAC,IAAIA,CAAC,CAAC5L,KAAK,KAAKA,KAAK,CAAC,CAACoC,MAAM;EACzD;EACA;EACAiN,iBAAiBA,CAACC,aAAqB;IACrC,IAAI,CAAC,IAAI,CAACzM,gBAAgB,EAAE,OAAO,EAAE;IACrC,MAAMmJ,UAAU,GAAG,IAAI,CAAClG,YAAY,CAAC,IAAI,CAACjD,gBAAgB,CAAC,IAAI,EAAE;IACjE,MAAMqK,SAAS,GAAGlB,UAAU,CAACL,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC/K,SAAS,KAAKyO,aAAa,CAAC;IACrE,OAAOpC,SAAS,EAAElN,KAAK,IAAI,EAAE;EAC/B;EACA;EACAuP,gBAAgBA,CAACD,aAAqB;IACpC,KAAK,MAAMvE,QAAQ,IAAI,IAAI,CAACpJ,SAAS,EAAE;MACrC,MAAMqK,UAAU,GAAG,IAAI,CAAClG,YAAY,CAACiF,QAAQ,CAAC,IAAI,EAAE;MACpD,MAAMmC,SAAS,GAAGlB,UAAU,CAACL,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC/K,SAAS,KAAKyO,aAAa,CAAC;MACrE,IAAIpC,SAAS,EAAE;QACb,OAAO;UACLrM,SAAS,EAAEqM,SAAS,CAACrM,SAAS;UAC9Bb,KAAK,EAAEkN,SAAS,CAAClN,KAAK,IAAI;SAC3B;MACH;IACF;IACA,OAAO;MAAEa,SAAS,EAAEyO,aAAa;MAAEtP,KAAK,EAAE;IAAE,CAAE;EAChD;EAEA;EACAF,oBAAoBA,CAACoF,OAAe;IAClC,KAAK,MAAM6F,QAAQ,IAAI,IAAI,CAACpJ,SAAS,EAAE;MACrC,MAAMqK,UAAU,GAAG,IAAI,CAAClG,YAAY,CAACiF,QAAQ,CAAC,IAAI,EAAE;MACpD,MAAMmC,SAAS,GAAGlB,UAAU,CAACL,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC1G,OAAO,KAAKA,OAAO,CAAC;MAC7D,IAAIgI,SAAS,EAAE;QACb,OAAO;UACLrM,SAAS,EAAEqM,SAAS,CAACrM,SAAS;UAC9Bb,KAAK,EAAEkN,SAAS,CAAClN,KAAK,IAAI;SAC3B;MACH;IACF;IACA,OAAO;MAAEa,SAAS,EAAE,MAAMqE,OAAO,EAAE;MAAElF,KAAK,EAAE;IAAE,CAAE;EAClD;EAEA;EACAoE,kBAAkBA,CAAA;IAChB,IAAI,CAAC,IAAI,CAACV,UAAU,IAAI,CAAC,IAAI,CAACb,gBAAgB,IAAI,CAAC,IAAI,CAACiD,YAAY,CAAC,IAAI,CAACjD,gBAAgB,CAAC,EAAE;MAC3F,OAAO,KAAK;IACd;IAEA,MAAM2M,QAAQ,GAAG,IAAI,CAAC1J,YAAY,CAAC,IAAI,CAACjD,gBAAgB,CAAC,CAACqJ,MAAM,CAACN,CAAC,IAAG;MACnE,MAAMO,UAAU,GAAG,CAAC,IAAI,CAACrJ,aAAa,IAAI8I,CAAC,CAAC5L,KAAK,KAAK,IAAI,CAAC8C,aAAa;MACxE,MAAMsJ,WAAW,GAAGR,CAAC,CAAC/K,SAAS,CAAC8E,WAAW,EAAE,CAACC,QAAQ,CAAC,IAAI,CAAClC,UAAU,CAACiC,WAAW,EAAE,CAAC;MACrF,OAAOwG,UAAU,IAAIC,WAAW;IAClC,CAAC,CAAC;IAEF,OAAOoD,QAAQ,CAACpN,MAAM,KAAK,CAAC;EAC9B;EAEA;EACA0E,0BAA0BA,CAAA;IACxB,IAAI,CAAC,IAAI,CAACjE,gBAAgB,IAAI,CAAC,IAAI,CAACiD,YAAY,CAAC,IAAI,CAACjD,gBAAgB,CAAC,EAAE;MACvE,OAAO,CAAC;IACV;IAEA,MAAM2M,QAAQ,GAAG,IAAI,CAAC1J,YAAY,CAAC,IAAI,CAACjD,gBAAgB,CAAC,CAACqJ,MAAM,CAACN,CAAC,IAAG;MACnE,MAAMO,UAAU,GAAG,CAAC,IAAI,CAACrJ,aAAa,IAAI8I,CAAC,CAAC5L,KAAK,KAAK,IAAI,CAAC8C,aAAa;MACxE,MAAMsJ,WAAW,GAAG,CAAC,IAAI,CAAC1I,UAAU,IAAIkI,CAAC,CAAC/K,SAAS,CAAC8E,WAAW,EAAE,CAACC,QAAQ,CAAC,IAAI,CAAClC,UAAU,CAACiC,WAAW,EAAE,CAAC;MACzG,OAAOwG,UAAU,IAAIC,WAAW;IAClC,CAAC,CAAC;IAEF,OAAOoD,QAAQ,CAACpN,MAAM;EACxB;EAEA;EACAqN,oBAAoBA,CAACvC,SAAwB;IAC3C,OAAOA,SAAS,CAAChI,OAAO,GAAGgI,SAAS,CAAChI,OAAO,CAACwK,QAAQ,EAAE,GAAG,GAAGxC,SAAS,CAACrM,SAAS,IAAIqM,SAAS,CAAClN,KAAK,EAAE;EACvG;EACA;EACQ2P,qBAAqBA,CAACzK,OAAe;IAC3C,KAAK,MAAM6F,QAAQ,IAAI,IAAI,CAACpJ,SAAS,EAAE;MACrC,MAAMqK,UAAU,GAAG,IAAI,CAAClG,YAAY,CAACiF,QAAQ,CAAC,IAAI,EAAE;MACpD,MAAMmC,SAAS,GAAGlB,UAAU,CAACL,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC1G,OAAO,KAAKA,OAAO,CAAC;MAC7D,IAAIgI,SAAS,EAAE,OAAOA,SAAS;IACjC;IACA,OAAO,IAAI;EACb;EACA;EACQ0C,qBAAqBA,CAAC/O,SAAiB;IAC7C,MAAMgP,kBAAkB,GAAqD,EAAE;IAE/E;IACA,KAAK,MAAM9E,QAAQ,IAAI,IAAI,CAACpJ,SAAS,EAAE;MACrC,MAAMqK,UAAU,GAAG,IAAI,CAAClG,YAAY,CAACiF,QAAQ,CAAC,IAAI,EAAE;MACpD,MAAM+E,OAAO,GAAG9D,UAAU,CAACE,MAAM,CAACN,CAAC,IAAIA,CAAC,CAAC/K,SAAS,KAAKA,SAAS,CAAC;MACjEiP,OAAO,CAAChF,OAAO,CAACoC,SAAS,IAAG;QAC1B2C,kBAAkB,CAAChE,IAAI,CAAC;UAAEd,QAAQ;UAAEmC;QAAS,CAAE,CAAC;MAClD,CAAC,CAAC;IACJ;IAEA9D,OAAO,CAACC,GAAG,CAAC,iBAAiBxI,SAAS,QAAQ,EAAEgP,kBAAkB,CAAC;IAEnE,IAAIA,kBAAkB,CAACzN,MAAM,KAAK,CAAC,EAAE;MACnCgH,OAAO,CAAC2G,IAAI,CAAC,kBAAkBlP,SAAS,SAAS,CAAC;MAClD,OAAO,IAAI;IACb;IAEA,IAAIgP,kBAAkB,CAACzN,MAAM,GAAG,CAAC,EAAE;MACjCgH,OAAO,CAAC2G,IAAI,CAAC,aAAalP,SAAS,IAAI,EAAEgP,kBAAkB,CAAC5E,GAAG,CAAC+E,CAAC,IAAI,GAAGA,CAAC,CAACjF,QAAQ,IAAIiF,CAAC,CAAC9C,SAAS,CAAClN,KAAK,EAAE,CAAC,CAAC;MAC3GoJ,OAAO,CAAC2G,IAAI,CAAC,cAAcF,kBAAkB,CAAC,CAAC,CAAC,CAAC9E,QAAQ,IAAI8E,kBAAkB,CAAC,CAAC,CAAC,CAAC3C,SAAS,CAAClN,KAAK,EAAE,CAAC;IACvG;IAEA,MAAMiQ,UAAU,GAAGJ,kBAAkB,CAAC,CAAC,CAAC;IACxC,OAAOI,UAAU,CAAC/C,SAAS,CAAChI,OAAO,IAAI,IAAI;EAC7C;EAEA;EACQsH,iBAAiBA,CAACtH,OAAe;IACvC,OAAO,IAAI,CAACgD,gBAAgB,CAACtC,QAAQ,CAACV,OAAO,CAAC;EAChD;EAEA;EACQqH,iBAAiBA,CAACrH,OAAe;IACvC,OAAO,IAAI,CAACwD,gBAAgB,CAAC9C,QAAQ,CAACV,OAAO,CAAC;EAChD;EAEA;EACAgL,wBAAwBA,CAACC,QAAgB;IACvC,KAAK,MAAMpF,QAAQ,IAAI,IAAI,CAACpJ,SAAS,EAAE;MACrC,MAAMqK,UAAU,GAAG,IAAI,CAAClG,YAAY,CAACiF,QAAQ,CAAC,IAAI,EAAE;MACpD,MAAMmC,SAAS,GAAGlB,UAAU,CAACL,IAAI,CAACC,CAAC,IAAI,IAAI,CAAC6D,oBAAoB,CAAC7D,CAAC,CAAC,KAAKuE,QAAQ,CAAC;MACjF,IAAIjD,SAAS,EAAE,OAAOA,SAAS;IACjC;IACA,OAAO,IAAI;EACb;EAEA;EACAjL,4BAA4BA,CAAC8I,QAAgB;IAC3C,MAAMqF,UAAU,GAAG,IAAI,CAAC9N,gBAAgB,CAACyI,QAAQ,CAAC;IAClD,MAAMsF,aAAa,GAAG,IAAI,CAAClP,2BAA2B,CAAC4J,QAAQ,CAAC,CAAC3I,MAAM;IACvE,OAAOgO,UAAU,GAAG,CAAC,GAAIC,aAAa,GAAGD,UAAU,GAAI,GAAG,GAAG,CAAC;EAChE;EAEA;EACAjO,uBAAuBA,CAAC4I,QAAgB;IACtC,MAAMqF,UAAU,GAAG,IAAI,CAAC9N,gBAAgB,CAACyI,QAAQ,CAAC;IAClD,MAAMsF,aAAa,GAAG,IAAI,CAAClP,2BAA2B,CAAC4J,QAAQ,CAAC,CAAC3I,MAAM;IACvE,OAAOgO,UAAU,GAAG,CAAC,IAAIC,aAAa,KAAKD,UAAU;EACvD;EAEA;EACA/N,0BAA0BA,CAAC0I,QAAgB;IACzC,MAAMqF,UAAU,GAAG,IAAI,CAAC9N,gBAAgB,CAACyI,QAAQ,CAAC;IAClD,MAAMsF,aAAa,GAAG,IAAI,CAAClP,2BAA2B,CAAC4J,QAAQ,CAAC,CAAC3I,MAAM;IAEvE,IAAIiO,aAAa,KAAK,CAAC,EAAE;MACvB,OAAO,KAAK;IACd,CAAC,MAAM,IAAIA,aAAa,KAAKD,UAAU,EAAE;MACvC,OAAO,MAAM;IACf,CAAC,MAAM;MACL,OAAO,GAAGC,aAAa,IAAID,UAAU,EAAE;IACzC;EACF;EAEA;EACAvO,yBAAyBA,CAACkJ,QAAgB;IACxC,MAAMiB,UAAU,GAAG,IAAI,CAAClG,YAAY,CAACiF,QAAQ,CAAC,IAAI,EAAE;IACpD,OAAOiB,UAAU,CAACE,MAAM,CAACN,CAAC,IAAIA,CAAC,CAAC1G,OAAO,IAAI,CAAC,IAAI,CAACqH,iBAAiB,CAACX,CAAC,CAAC1G,OAAO,CAAC,CAAC,CAAC9C,MAAM;EACvF;;;uCA3nBW+F,yBAAyB,EAAA5I,EAAA,CAAA+Q,iBAAA,CAAA/Q,EAAA,CAAAgR,iBAAA,GAAAhR,EAAA,CAAA+Q,iBAAA,CAAAE,EAAA,CAAAC,kBAAA,GAAAlR,EAAA,CAAA+Q,iBAAA,CAAAI,EAAA,CAAAC,eAAA;IAAA;EAAA;;;YAAzBxI,yBAAyB;MAAAyI,SAAA;MAAAC,SAAA,WAAAC,gCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;;;;;;;;;;;;;;;;;;;;;;uCARzB,CACT;QACEE,OAAO,EAAE3R,iBAAiB;QAC1B4R,WAAW,EAAE7R,UAAU,CAAC,MAAM8I,yBAAyB,CAAC;QACxDgJ,KAAK,EAAE;OACR,CACF,GAAA5R,EAAA,CAAA6R,oBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,mCAAAV,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;UC7BHxR,EAAA,CAAAC,cAAA,aAAyC;UAEvCD,EAAA,CAAAU,UAAA,IAAAyR,wCAAA,kBAA8F;UAgC5FnS,EADF,CAAAC,cAAA,aAAgC,gBAGmL;UAD5KD,EAAA,CAAAY,UAAA,mBAAAwR,2DAAA;YAAApS,EAAA,CAAAc,aAAA,CAAAuR,GAAA;YAAA,OAAArS,EAAA,CAAAkB,WAAA,CAASuQ,GAAA,CAAApD,cAAA,EAAgB;UAAA,EAAC;UAE7DrO,EAAA,CAAAC,cAAA,cAA4B;UAK1BD,EAJA,CAAAU,UAAA,IAAA4R,iDAAA,0BAAgC,IAAAC,iDAAA,0BAIC;UAGnCvS,EAAA,CAAAG,YAAA,EAAO;UACPH,EAAA,CAAAoB,SAAA,iBAA4D;UAGlEpB,EAFI,CAAAG,YAAA,EAAS,EACL,EACF;UAGNH,EAAA,CAAAU,UAAA,IAAA8R,gDAAA,kCAAAxS,EAAA,CAAAyS,sBAAA,CAA6D;;;UAlDrDzS,EAAA,CAAAI,SAAA,EAAqD;UAArDJ,EAAA,CAAAuB,UAAA,SAAAkQ,GAAA,CAAAvI,gBAAA,IAAAuI,GAAA,CAAA9I,gBAAA,CAAA9F,MAAA,KAAqD;UAgCX7C,EAAA,CAAAI,SAAA,GAAwC;UAAxCJ,EAAA,CAAA2C,WAAA,aAAA8O,GAAA,CAAAjQ,QAAA,IAAAiQ,GAAA,CAAA/I,SAAA,CAAwC;UACpF1I,EAAA,CAAAuB,UAAA,aAAAkQ,GAAA,CAAAjQ,QAAA,IAAAiQ,GAAA,CAAA/I,SAAA,CAAkC;UAGjB1I,EAAA,CAAAI,SAAA,GAAe;UAAfJ,EAAA,CAAAuB,UAAA,SAAAkQ,GAAA,CAAA/I,SAAA,CAAe;UAIf1I,EAAA,CAAAI,SAAA,EAAgB;UAAhBJ,EAAA,CAAAuB,UAAA,UAAAkQ,GAAA,CAAA/I,SAAA,CAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}