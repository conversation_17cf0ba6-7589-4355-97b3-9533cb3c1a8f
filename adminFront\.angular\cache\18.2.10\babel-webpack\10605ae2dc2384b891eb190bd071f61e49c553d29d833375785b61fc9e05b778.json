{"ast": null, "code": "import { getDefaultOptions } from \"../_lib/defaultOptions/index.js\";\nimport getTimezoneOffsetInMilliseconds from \"../_lib/getTimezoneOffsetInMilliseconds/index.js\";\nimport compareAsc from \"../compareAsc/index.js\";\nimport toDate from \"../toDate/index.js\";\nimport cloneObject from \"../_lib/cloneObject/index.js\";\nimport assign from \"../_lib/assign/index.js\";\nimport defaultLocale from \"../_lib/defaultLocale/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\nvar MILLISECONDS_IN_MINUTE = 1000 * 60;\nvar MINUTES_IN_DAY = 60 * 24;\nvar MINUTES_IN_MONTH = MINUTES_IN_DAY * 30;\nvar MINUTES_IN_YEAR = MINUTES_IN_DAY * 365;\n\n/**\n * @name formatDistanceStrict\n * @category Common Helpers\n * @summary Return the distance between the given dates in words.\n *\n * @description\n * Return the distance between the given dates in words, using strict units.\n * This is like `formatDistance`, but does not use helpers like 'almost', 'over',\n * 'less than' and the like.\n *\n * | Distance between dates | Result              |\n * |------------------------|---------------------|\n * | 0 ... 59 secs          | [0..59] seconds     |\n * | 1 ... 59 mins          | [1..59] minutes     |\n * | 1 ... 23 hrs           | [1..23] hours       |\n * | 1 ... 29 days          | [1..29] days        |\n * | 1 ... 11 months        | [1..11] months      |\n * | 1 ... N years          | [1..N]  years       |\n *\n * @param {Date|Number} date - the date\n * @param {Date|Number} baseDate - the date to compare with\n * @param {Object} [options] - an object with options.\n * @param {Boolean} [options.addSuffix=false] - result indicates if the second date is earlier or later than the first\n * @param {'second'|'minute'|'hour'|'day'|'month'|'year'} [options.unit] - if specified, will force a unit\n * @param {'floor'|'ceil'|'round'} [options.roundingMethod='round'] - which way to round partial units\n * @param {Locale} [options.locale=defaultLocale] - the locale object. See [Locale]{@link https://date-fns.org/docs/Locale}\n * @returns {String} the distance in words\n * @throws {TypeError} 2 arguments required\n * @throws {RangeError} `date` must not be Invalid Date\n * @throws {RangeError} `baseDate` must not be Invalid Date\n * @throws {RangeError} `options.roundingMethod` must be 'floor', 'ceil' or 'round'\n * @throws {RangeError} `options.unit` must be 'second', 'minute', 'hour', 'day', 'month' or 'year'\n * @throws {RangeError} `options.locale` must contain `formatDistance` property\n *\n * @example\n * // What is the distance between 2 July 2014 and 1 January 2015?\n * const result = formatDistanceStrict(new Date(2014, 6, 2), new Date(2015, 0, 2))\n * //=> '6 months'\n *\n * @example\n * // What is the distance between 1 January 2015 00:00:15\n * // and 1 January 2015 00:00:00?\n * const result = formatDistanceStrict(\n *   new Date(2015, 0, 1, 0, 0, 15),\n *   new Date(2015, 0, 1, 0, 0, 0)\n * )\n * //=> '15 seconds'\n *\n * @example\n * // What is the distance from 1 January 2016\n * // to 1 January 2015, with a suffix?\n * const result = formatDistanceStrict(new Date(2015, 0, 1), new Date(2016, 0, 1), {\n *   addSuffix: true\n * })\n * //=> '1 year ago'\n *\n * @example\n * // What is the distance from 1 January 2016\n * // to 1 January 2015, in minutes?\n * const result = formatDistanceStrict(new Date(2016, 0, 1), new Date(2015, 0, 1), {\n *   unit: 'minute'\n * })\n * //=> '525600 minutes'\n *\n * @example\n * // What is the distance from 1 January 2015\n * // to 28 January 2015, in months, rounded up?\n * const result = formatDistanceStrict(new Date(2015, 0, 28), new Date(2015, 0, 1), {\n *   unit: 'month',\n *   roundingMethod: 'ceil'\n * })\n * //=> '1 month'\n *\n * @example\n * // What is the distance between 1 August 2016 and 1 January 2015 in Esperanto?\n * import { eoLocale } from 'date-fns/locale/eo'\n * const result = formatDistanceStrict(new Date(2016, 7, 1), new Date(2015, 0, 1), {\n *   locale: eoLocale\n * })\n * //=> '1 jaro'\n */\n\nexport default function formatDistanceStrict(dirtyDate, dirtyBaseDate, options) {\n  var _ref, _options$locale, _options$roundingMeth;\n  requiredArgs(2, arguments);\n  var defaultOptions = getDefaultOptions();\n  var locale = (_ref = (_options$locale = options === null || options === void 0 ? void 0 : options.locale) !== null && _options$locale !== void 0 ? _options$locale : defaultOptions.locale) !== null && _ref !== void 0 ? _ref : defaultLocale;\n  if (!locale.formatDistance) {\n    throw new RangeError('locale must contain localize.formatDistance property');\n  }\n  var comparison = compareAsc(dirtyDate, dirtyBaseDate);\n  if (isNaN(comparison)) {\n    throw new RangeError('Invalid time value');\n  }\n  var localizeOptions = assign(cloneObject(options), {\n    addSuffix: Boolean(options === null || options === void 0 ? void 0 : options.addSuffix),\n    comparison: comparison\n  });\n  var dateLeft;\n  var dateRight;\n  if (comparison > 0) {\n    dateLeft = toDate(dirtyBaseDate);\n    dateRight = toDate(dirtyDate);\n  } else {\n    dateLeft = toDate(dirtyDate);\n    dateRight = toDate(dirtyBaseDate);\n  }\n  var roundingMethod = String((_options$roundingMeth = options === null || options === void 0 ? void 0 : options.roundingMethod) !== null && _options$roundingMeth !== void 0 ? _options$roundingMeth : 'round');\n  var roundingMethodFn;\n  if (roundingMethod === 'floor') {\n    roundingMethodFn = Math.floor;\n  } else if (roundingMethod === 'ceil') {\n    roundingMethodFn = Math.ceil;\n  } else if (roundingMethod === 'round') {\n    roundingMethodFn = Math.round;\n  } else {\n    throw new RangeError(\"roundingMethod must be 'floor', 'ceil' or 'round'\");\n  }\n  var milliseconds = dateRight.getTime() - dateLeft.getTime();\n  var minutes = milliseconds / MILLISECONDS_IN_MINUTE;\n  var timezoneOffset = getTimezoneOffsetInMilliseconds(dateRight) - getTimezoneOffsetInMilliseconds(dateLeft);\n\n  // Use DST-normalized difference in minutes for years, months and days;\n  // use regular difference in minutes for hours, minutes and seconds.\n  var dstNormalizedMinutes = (milliseconds - timezoneOffset) / MILLISECONDS_IN_MINUTE;\n  var defaultUnit = options === null || options === void 0 ? void 0 : options.unit;\n  var unit;\n  if (!defaultUnit) {\n    if (minutes < 1) {\n      unit = 'second';\n    } else if (minutes < 60) {\n      unit = 'minute';\n    } else if (minutes < MINUTES_IN_DAY) {\n      unit = 'hour';\n    } else if (dstNormalizedMinutes < MINUTES_IN_MONTH) {\n      unit = 'day';\n    } else if (dstNormalizedMinutes < MINUTES_IN_YEAR) {\n      unit = 'month';\n    } else {\n      unit = 'year';\n    }\n  } else {\n    unit = String(defaultUnit);\n  }\n\n  // 0 up to 60 seconds\n  if (unit === 'second') {\n    var seconds = roundingMethodFn(milliseconds / 1000);\n    return locale.formatDistance('xSeconds', seconds, localizeOptions);\n\n    // 1 up to 60 mins\n  } else if (unit === 'minute') {\n    var roundedMinutes = roundingMethodFn(minutes);\n    return locale.formatDistance('xMinutes', roundedMinutes, localizeOptions);\n\n    // 1 up to 24 hours\n  } else if (unit === 'hour') {\n    var hours = roundingMethodFn(minutes / 60);\n    return locale.formatDistance('xHours', hours, localizeOptions);\n\n    // 1 up to 30 days\n  } else if (unit === 'day') {\n    var days = roundingMethodFn(dstNormalizedMinutes / MINUTES_IN_DAY);\n    return locale.formatDistance('xDays', days, localizeOptions);\n\n    // 1 up to 12 months\n  } else if (unit === 'month') {\n    var months = roundingMethodFn(dstNormalizedMinutes / MINUTES_IN_MONTH);\n    return months === 12 && defaultUnit !== 'month' ? locale.formatDistance('xYears', 1, localizeOptions) : locale.formatDistance('xMonths', months, localizeOptions);\n\n    // 1 year up to max Date\n  } else if (unit === 'year') {\n    var years = roundingMethodFn(dstNormalizedMinutes / MINUTES_IN_YEAR);\n    return locale.formatDistance('xYears', years, localizeOptions);\n  }\n  throw new RangeError(\"unit must be 'second', 'minute', 'hour', 'day', 'month' or 'year'\");\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}