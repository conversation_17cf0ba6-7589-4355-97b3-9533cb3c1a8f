{"ast": null, "code": "import { last, map } from 'rxjs';\nimport { FormBodyBuilder } from 'src/app/shared/constant/constant';\nimport { environment } from 'src/environments/environment';\nimport { RequestBuilder } from 'src/services/api/request-builder';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport let NoticeServiceCustom = /*#__PURE__*/(() => {\n  class NoticeServiceCustom {\n    constructor(http) {\n      this.http = http;\n    }\n    SaveSpecialNoticeFile(body) {\n      const endPoint = '/api/SpecialNoticeFile/SaveSpecialNoticeFile';\n      // const body = {\n      //     CNoticeType,\n      //     CBuildCaseId,\n      //     CFile,\n      //     CHouse,\n      //     CSpecialNoticeFileId,\n      //     CIsSelectAll,\n      //     CExamineNote\n      // };\n      return this._request(body, endPoint, 'post').pipe(last(), map(res => res) // Explicitly cast the body to StringResponseBase\n      );\n    }\n    _request(body, endPoint, method, context) {\n      const rb = new RequestBuilder(environment.BASE_URL_API, endPoint, method);\n      rb._bodyContent = FormBodyBuilder.BuildBodyContent(body);\n      return this.http.request(rb.build({\n        responseType: 'json',\n        accept: 'text/json',\n        reportProgress: false\n      }));\n    }\n    static {\n      this.ɵfac = function NoticeServiceCustom_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || NoticeServiceCustom)(i0.ɵɵinject(i1.HttpClient));\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: NoticeServiceCustom,\n        factory: NoticeServiceCustom.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return NoticeServiceCustom;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}