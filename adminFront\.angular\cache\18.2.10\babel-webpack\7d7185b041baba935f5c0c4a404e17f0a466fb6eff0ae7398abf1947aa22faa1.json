{"ast": null, "code": "export class TemperatureHumidityData {}", "map": {"version": 3, "names": ["TemperatureHumidityData"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\@core\\data\\temperature-humidity.ts"], "sourcesContent": ["import { Observable } from 'rxjs';\r\n\r\nexport interface Temperature {\r\n  value: number;\r\n  min: number;\r\n  max: number;\r\n}\r\n\r\nexport abstract class TemperatureHumidityData {\r\n  abstract getTemperatureData(): Observable<Temperature>;\r\n  abstract getHumidityData(): Observable<Temperature>;\r\n}\r\n"], "mappings": "AAQA,OAAM,MAAgBA,uBAAuB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}