{"ast": null, "code": "/* tslint:disable */\n/* eslint-disable */\n/* Code generated by ng-openapi-gen DO NOT EDIT. */\nimport { HttpResponse } from '@angular/common/http';\nimport { filter, map } from 'rxjs/operators';\nimport { RequestBuilder } from '../../request-builder';\nexport function apiSpecialChangeGetApproveWaitingListPost$Json(http, rootUrl, params, context) {\n  const rb = new RequestBuilder(rootUrl, apiSpecialChangeGetApproveWaitingListPost$Json.PATH, 'post');\n  if (params) {\n    rb.body(params.body, 'application/*+json');\n  }\n  return http.request(rb.build({\n    responseType: 'json',\n    accept: 'text/json',\n    context\n  })).pipe(filter(r => r instanceof HttpResponse), map(r => {\n    return r;\n  }));\n}\napiSpecialChangeGetApproveWaitingListPost$Json.PATH = '/api/SpecialChange/GetApproveWaitingList';", "map": {"version": 3, "names": ["HttpResponse", "filter", "map", "RequestBuilder", "apiSpecialChangeGetApproveWaitingListPost$Json", "http", "rootUrl", "params", "context", "rb", "PATH", "body", "request", "build", "responseType", "accept", "pipe", "r"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\services\\api\\fn\\special-change\\api-special-change-get-approve-waiting-list-post-json.ts"], "sourcesContent": ["/* tslint:disable */\r\n/* eslint-disable */\r\n/* Code generated by ng-openapi-gen DO NOT EDIT. */\r\n\r\nimport { HttpClient, HttpContext, HttpResponse } from '@angular/common/http';\r\nimport { Observable } from 'rxjs';\r\nimport { filter, map } from 'rxjs/operators';\r\nimport { StrictHttpResponse } from '../../strict-http-response';\r\nimport { RequestBuilder } from '../../request-builder';\r\n\r\nimport { ApproveWaitingArgs } from '../../models/approve-waiting-args';\r\nimport { ApproveWaitingResListResponseBase } from '../../models/approve-waiting-res-list-response-base';\r\n\r\nexport interface ApiSpecialChangeGetApproveWaitingListPost$Json$Params {\r\n      body?: ApproveWaitingArgs\r\n}\r\n\r\nexport function apiSpecialChangeGetApproveWaitingListPost$Json(http: HttpClient, rootUrl: string, params?: ApiSpecialChangeGetApproveWaitingListPost$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<ApproveWaitingResListResponseBase>> {\r\n  const rb = new RequestBuilder(rootUrl, apiSpecialChangeGetApproveWaitingListPost$Json.PATH, 'post');\r\n  if (params) {\r\n    rb.body(params.body, 'application/*+json');\r\n  }\r\n\r\n  return http.request(\r\n    rb.build({ responseType: 'json', accept: 'text/json', context })\r\n  ).pipe(\r\n    filter((r: any): r is HttpResponse<any> => r instanceof HttpResponse),\r\n    map((r: HttpResponse<any>) => {\r\n      return r as StrictHttpResponse<ApproveWaitingResListResponseBase>;\r\n    })\r\n  );\r\n}\r\n\r\napiSpecialChangeGetApproveWaitingListPost$Json.PATH = '/api/SpecialChange/GetApproveWaitingList';\r\n"], "mappings": "AAAA;AACA;AACA;AAEA,SAAkCA,YAAY,QAAQ,sBAAsB;AAE5E,SAASC,MAAM,EAAEC,GAAG,QAAQ,gBAAgB;AAE5C,SAASC,cAAc,QAAQ,uBAAuB;AAStD,OAAM,SAAUC,8CAA8CA,CAACC,IAAgB,EAAEC,OAAe,EAAEC,MAA8D,EAAEC,OAAqB;EACrL,MAAMC,EAAE,GAAG,IAAIN,cAAc,CAACG,OAAO,EAAEF,8CAA8C,CAACM,IAAI,EAAE,MAAM,CAAC;EACnG,IAAIH,MAAM,EAAE;IACVE,EAAE,CAACE,IAAI,CAACJ,MAAM,CAACI,IAAI,EAAE,oBAAoB,CAAC;EAC5C;EAEA,OAAON,IAAI,CAACO,OAAO,CACjBH,EAAE,CAACI,KAAK,CAAC;IAAEC,YAAY,EAAE,MAAM;IAAEC,MAAM,EAAE,WAAW;IAAEP;EAAO,CAAE,CAAC,CACjE,CAACQ,IAAI,CACJf,MAAM,CAAEgB,CAAM,IAA6BA,CAAC,YAAYjB,YAAY,CAAC,EACrEE,GAAG,CAAEe,CAAoB,IAAI;IAC3B,OAAOA,CAA0D;EACnE,CAAC,CAAC,CACH;AACH;AAEAb,8CAA8C,CAACM,IAAI,GAAG,0CAA0C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}