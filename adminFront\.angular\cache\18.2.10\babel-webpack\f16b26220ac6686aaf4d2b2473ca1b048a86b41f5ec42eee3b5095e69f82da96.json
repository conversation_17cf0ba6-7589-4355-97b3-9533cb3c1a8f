{"ast": null, "code": "export var EnumAllowType = /*#__PURE__*/function (EnumAllowType) {\n  EnumAllowType[EnumAllowType[\"Read\"] = 1] = \"Read\";\n  EnumAllowType[EnumAllowType[\"Create\"] = 2] = \"Create\";\n  EnumAllowType[EnumAllowType[\"Update\"] = 3] = \"Update\";\n  EnumAllowType[EnumAllowType[\"Delete\"] = 4] = \"Delete\";\n  EnumAllowType[EnumAllowType[\"ExcelImport\"] = 5] = \"ExcelImport\";\n  EnumAllowType[EnumAllowType[\"ExcelExport\"] = 6] = \"ExcelExport\";\n  EnumAllowType[EnumAllowType[\"Report\"] = 7] = \"Report\";\n  EnumAllowType[EnumAllowType[\"ApiImport\"] = 8] = \"ApiImport\";\n  EnumAllowType[EnumAllowType[\"ChangePayStatus\"] = 9] = \"ChangePayStatus\";\n  EnumAllowType[EnumAllowType[\"ChangeProgress\"] = 10] = \"ChangeProgress\";\n  return EnumAllowType;\n}(EnumAllowType || {});", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}