{"ast": null, "code": ";\n(function (root, factory) {\n  if (typeof exports === \"object\") {\n    // CommonJS\n    module.exports = exports = factory(require(\"./core\"));\n  } else if (typeof define === \"function\" && define.amd) {\n    // AMD\n    define([\"./core\"], factory);\n  } else {\n    // Global (browser)\n    factory(root.CryptoJS);\n  }\n})(this, function (CryptoJS) {\n  (function (Math) {\n    // Shortcuts\n    var C = CryptoJS;\n    var C_lib = C.lib;\n    var WordArray = C_lib.WordArray;\n    var Hasher = C_lib.Hasher;\n    var C_algo = C.algo;\n\n    // Initialization and round constants tables\n    var H = [];\n    var K = [];\n\n    // Compute constants\n    (function () {\n      function isPrime(n) {\n        var sqrtN = Math.sqrt(n);\n        for (var factor = 2; factor <= sqrtN; factor++) {\n          if (!(n % factor)) {\n            return false;\n          }\n        }\n        return true;\n      }\n      function getFractionalBits(n) {\n        return (n - (n | 0)) * 0x100000000 | 0;\n      }\n      var n = 2;\n      var nPrime = 0;\n      while (nPrime < 64) {\n        if (isPrime(n)) {\n          if (nPrime < 8) {\n            H[nPrime] = getFractionalBits(Math.pow(n, 1 / 2));\n          }\n          K[nPrime] = getFractionalBits(Math.pow(n, 1 / 3));\n          nPrime++;\n        }\n        n++;\n      }\n    })();\n\n    // Reusable object\n    var W = [];\n\n    /**\n     * SHA-256 hash algorithm.\n     */\n    var SHA256 = C_algo.SHA256 = Hasher.extend({\n      _doReset: function () {\n        this._hash = new WordArray.init(H.slice(0));\n      },\n      _doProcessBlock: function (M, offset) {\n        // Shortcut\n        var H = this._hash.words;\n\n        // Working variables\n        var a = H[0];\n        var b = H[1];\n        var c = H[2];\n        var d = H[3];\n        var e = H[4];\n        var f = H[5];\n        var g = H[6];\n        var h = H[7];\n\n        // Computation\n        for (var i = 0; i < 64; i++) {\n          if (i < 16) {\n            W[i] = M[offset + i] | 0;\n          } else {\n            var gamma0x = W[i - 15];\n            var gamma0 = (gamma0x << 25 | gamma0x >>> 7) ^ (gamma0x << 14 | gamma0x >>> 18) ^ gamma0x >>> 3;\n            var gamma1x = W[i - 2];\n            var gamma1 = (gamma1x << 15 | gamma1x >>> 17) ^ (gamma1x << 13 | gamma1x >>> 19) ^ gamma1x >>> 10;\n            W[i] = gamma0 + W[i - 7] + gamma1 + W[i - 16];\n          }\n          var ch = e & f ^ ~e & g;\n          var maj = a & b ^ a & c ^ b & c;\n          var sigma0 = (a << 30 | a >>> 2) ^ (a << 19 | a >>> 13) ^ (a << 10 | a >>> 22);\n          var sigma1 = (e << 26 | e >>> 6) ^ (e << 21 | e >>> 11) ^ (e << 7 | e >>> 25);\n          var t1 = h + sigma1 + ch + K[i] + W[i];\n          var t2 = sigma0 + maj;\n          h = g;\n          g = f;\n          f = e;\n          e = d + t1 | 0;\n          d = c;\n          c = b;\n          b = a;\n          a = t1 + t2 | 0;\n        }\n\n        // Intermediate hash value\n        H[0] = H[0] + a | 0;\n        H[1] = H[1] + b | 0;\n        H[2] = H[2] + c | 0;\n        H[3] = H[3] + d | 0;\n        H[4] = H[4] + e | 0;\n        H[5] = H[5] + f | 0;\n        H[6] = H[6] + g | 0;\n        H[7] = H[7] + h | 0;\n      },\n      _doFinalize: function () {\n        // Shortcuts\n        var data = this._data;\n        var dataWords = data.words;\n        var nBitsTotal = this._nDataBytes * 8;\n        var nBitsLeft = data.sigBytes * 8;\n\n        // Add padding\n        dataWords[nBitsLeft >>> 5] |= 0x80 << 24 - nBitsLeft % 32;\n        dataWords[(nBitsLeft + 64 >>> 9 << 4) + 14] = Math.floor(nBitsTotal / 0x100000000);\n        dataWords[(nBitsLeft + 64 >>> 9 << 4) + 15] = nBitsTotal;\n        data.sigBytes = dataWords.length * 4;\n\n        // Hash final blocks\n        this._process();\n\n        // Return final computed hash\n        return this._hash;\n      },\n      clone: function () {\n        var clone = Hasher.clone.call(this);\n        clone._hash = this._hash.clone();\n        return clone;\n      }\n    });\n\n    /**\n     * Shortcut function to the hasher's object interface.\n     *\n     * @param {WordArray|string} message The message to hash.\n     *\n     * @return {WordArray} The hash.\n     *\n     * @static\n     *\n     * @example\n     *\n     *     var hash = CryptoJS.SHA256('message');\n     *     var hash = CryptoJS.SHA256(wordArray);\n     */\n    C.SHA256 = Hasher._createHelper(SHA256);\n\n    /**\n     * Shortcut function to the HMAC's object interface.\n     *\n     * @param {WordArray|string} message The message to hash.\n     * @param {WordArray|string} key The secret key.\n     *\n     * @return {WordArray} The HMAC.\n     *\n     * @static\n     *\n     * @example\n     *\n     *     var hmac = CryptoJS.HmacSHA256(message, key);\n     */\n    C.HmacSHA256 = Hasher._createHmacHelper(SHA256);\n  })(Math);\n  return CryptoJS.SHA256;\n});", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}