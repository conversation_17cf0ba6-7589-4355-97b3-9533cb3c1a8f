{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nimport * as i2 from \"@angular/forms\";\nimport * as i3 from \"@nebular/theme\";\nimport * as i4 from \"./household-binding.component\";\nfunction HouseholdBindingDemoComponent_div_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 0)(1, \"h5\");\n    i0.ɵɵtext(2, \"\\u9078\\u64C7\\u7D50\\u679C (selectedHouseholds1)\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"pre\", 9);\n    i0.ɵɵtext(4);\n    i0.ɵɵpipe(5, \"json\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(5, 1, ctx_r0.selectedHouseholds1));\n  }\n}\nfunction HouseholdBindingDemoComponent_div_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 0)(1, \"h5\");\n    i0.ɵɵtext(2, \"\\u8A73\\u7D30\\u9078\\u64C7\\u9805\\u76EE\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"pre\", 9);\n    i0.ɵɵtext(4);\n    i0.ɵɵpipe(5, \"json\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(5, 1, ctx_r0.selectedItems));\n  }\n}\nfunction HouseholdBindingDemoComponent_div_30_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 10)(1, \"small\", 11);\n    i0.ɵɵtext(2, \"\\u8CC7\\u6599\\u72C0\\u614B\\uFF1A\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"pre\", 9);\n    i0.ɵɵtext(4);\n    i0.ɵɵpipe(5, \"json\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(5, 1, ctx_r0.debugData));\n  }\n}\nexport class HouseholdBindingDemoComponent {\n  constructor() {\n    this.selectedHouseholds1 = [];\n    this.selectedHouseholds2 = [];\n    this.selectedHouseholds3 = [];\n    this.selectedHouseholds4 = [];\n    this.selectedItems = [];\n    this.debugData = null;\n    this.customBuildingData = {\n      '總統套房': [{\n        code: 'P001',\n        building: '總統套房',\n        floor: '50F',\n        isSelected: false,\n        isDisabled: false\n      }, {\n        code: 'P002',\n        building: '總統套房',\n        floor: '51F',\n        isSelected: false,\n        isDisabled: false\n      }],\n      '景觀樓層': Array.from({\n        length: 20\n      }, (_, i) => ({\n        code: `V${String(i + 1).padStart(3, '0')}`,\n        building: '景觀樓層',\n        floor: `${30 + Math.floor(i / 2)}F`,\n        isSelected: false,\n        isDisabled: i % 5 === 0 // 每五個禁用一個作為示例\n      }))\n    };\n  }\n  ngOnInit() {\n    // 初始化一些預選的戶別\n    this.selectedHouseholds1 = ['A001', 'A002', 'B001'];\n  }\n  onSelectionChange(selectedItems) {\n    this.selectedItems = selectedItems;\n    console.log('Selection changed:', selectedItems);\n  }\n  debugInfo() {\n    this.debugData = {\n      selectedHouseholds1: this.selectedHouseholds1,\n      selectedHouseholds2: this.selectedHouseholds2,\n      selectedHouseholds3: this.selectedHouseholds3,\n      selectedHouseholds4: this.selectedHouseholds4,\n      customBuildingData: this.customBuildingData,\n      selectedItems: this.selectedItems\n    };\n    console.log('Debug info:', this.debugData);\n  }\n  testBuildingSelect() {\n    console.log('Testing building selection...');\n    // 這個方法只是為了測試，實際上我們無法直接呼叫子元件的方法\n    // 主要是為了觸發除錯訊息\n    console.log('請在下拉選單中點擊 A棟，然後查看 Console 訊息');\n  }\n  static {\n    this.ɵfac = function HouseholdBindingDemoComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || HouseholdBindingDemoComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: HouseholdBindingDemoComponent,\n      selectors: [[\"app-household-binding-demo\"]],\n      decls: 31,\n      vars: 13,\n      consts: [[1, \"demo-section\"], [\"placeholder\", \"\\u8ACB\\u9078\\u64C7\\u6236\\u5225\", 3, \"ngModelChange\", \"selectionChange\", \"ngModel\", \"maxSelections\"], [\"placeholder\", \"\\u8ACB\\u9078\\u64C7\\u6236\\u5225\", 3, \"ngModelChange\", \"ngModel\", \"showSelectedArea\", \"maxSelections\"], [\"placeholder\", \"\\u8ACB\\u9078\\u64C7\\u6236\\u5225\", 3, \"ngModelChange\", \"ngModel\", \"allowSearch\", \"allowBatchSelect\"], [\"placeholder\", \"\\u8ACB\\u9078\\u64C7\\u6236\\u5225\", 3, \"ngModelChange\", \"ngModel\", \"buildingData\"], [\"class\", \"demo-section\", 4, \"ngIf\"], [\"type\", \"button\", 1, \"btn\", \"btn-secondary\", \"btn-sm\", \"me-2\", 3, \"click\"], [\"type\", \"button\", 1, \"btn\", \"btn-info\", \"btn-sm\", \"me-2\", 3, \"click\"], [\"class\", \"mt-2\", 4, \"ngIf\"], [1, \"result-display\"], [1, \"mt-2\"], [1, \"text-muted\"]],\n      template: function HouseholdBindingDemoComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"nb-card\")(1, \"nb-card-header\")(2, \"h4\");\n          i0.ɵɵtext(3, \"\\u6236\\u5225\\u7D81\\u5B9A\\u5143\\u4EF6\\u793A\\u4F8B\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(4, \"nb-card-body\")(5, \"div\", 0)(6, \"h5\");\n          i0.ɵɵtext(7, \"\\u57FA\\u672C\\u4F7F\\u7528\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(8, \"app-household-binding\", 1);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdBindingDemoComponent_Template_app_household_binding_ngModelChange_8_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.selectedHouseholds1, $event) || (ctx.selectedHouseholds1 = $event);\n            return $event;\n          });\n          i0.ɵɵlistener(\"selectionChange\", function HouseholdBindingDemoComponent_Template_app_household_binding_selectionChange_8_listener($event) {\n            return ctx.onSelectionChange($event);\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(9, \"div\", 0)(10, \"h5\");\n          i0.ɵɵtext(11, \"\\u4E0D\\u986F\\u793A\\u5DF2\\u9078\\u64C7\\u5340\\u57DF\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(12, \"app-household-binding\", 2);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdBindingDemoComponent_Template_app_household_binding_ngModelChange_12_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.selectedHouseholds2, $event) || (ctx.selectedHouseholds2 = $event);\n            return $event;\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(13, \"div\", 0)(14, \"h5\");\n          i0.ɵɵtext(15, \"\\u7981\\u7528\\u641C\\u5C0B\\u548C\\u6279\\u6B21\\u9078\\u64C7\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(16, \"app-household-binding\", 3);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdBindingDemoComponent_Template_app_household_binding_ngModelChange_16_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.selectedHouseholds3, $event) || (ctx.selectedHouseholds3 = $event);\n            return $event;\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(17, \"div\", 0)(18, \"h5\");\n          i0.ɵɵtext(19, \"\\u81EA\\u5B9A\\u7FA9\\u6236\\u5225\\u8CC7\\u6599\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(20, \"app-household-binding\", 4);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdBindingDemoComponent_Template_app_household_binding_ngModelChange_20_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.selectedHouseholds4, $event) || (ctx.selectedHouseholds4 = $event);\n            return $event;\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(21, HouseholdBindingDemoComponent_div_21_Template, 6, 3, \"div\", 5)(22, HouseholdBindingDemoComponent_div_22_Template, 6, 3, \"div\", 5);\n          i0.ɵɵelementStart(23, \"div\", 0)(24, \"h5\");\n          i0.ɵɵtext(25, \"\\u9664\\u932F\\u8CC7\\u8A0A\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(26, \"button\", 6);\n          i0.ɵɵlistener(\"click\", function HouseholdBindingDemoComponent_Template_button_click_26_listener() {\n            return ctx.debugInfo();\n          });\n          i0.ɵɵtext(27, \" \\u6AA2\\u67E5\\u8CC7\\u6599 \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(28, \"button\", 7);\n          i0.ɵɵlistener(\"click\", function HouseholdBindingDemoComponent_Template_button_click_28_listener() {\n            return ctx.testBuildingSelect();\n          });\n          i0.ɵɵtext(29, \" \\u6E2C\\u8A66\\u9078\\u64C7 A\\u68DF \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(30, HouseholdBindingDemoComponent_div_30_Template, 6, 3, \"div\", 8);\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(8);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.selectedHouseholds1);\n          i0.ɵɵproperty(\"maxSelections\", 20);\n          i0.ɵɵadvance(4);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.selectedHouseholds2);\n          i0.ɵɵproperty(\"showSelectedArea\", false)(\"maxSelections\", 10);\n          i0.ɵɵadvance(4);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.selectedHouseholds3);\n          i0.ɵɵproperty(\"allowSearch\", false)(\"allowBatchSelect\", false);\n          i0.ɵɵadvance(4);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.selectedHouseholds4);\n          i0.ɵɵproperty(\"buildingData\", ctx.customBuildingData);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.selectedHouseholds1.length > 0);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.selectedItems.length > 0);\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"ngIf\", ctx.debugData);\n        }\n      },\n      dependencies: [i1.NgIf, i2.NgControlStatus, i2.NgModel, i3.NbCardComponent, i3.NbCardBodyComponent, i3.NbCardHeaderComponent, i4.HouseholdBindingComponent, i1.JsonPipe],\n      styles: [\".demo-section[_ngcontent-%COMP%] {\\n  margin-bottom: 2rem;\\n  padding: 1rem;\\n  border: 1px solid #e9ecef;\\n  border-radius: 0.375rem;\\n  background-color: #f8f9fa;\\n}\\n\\n.demo-section[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%] {\\n  margin-bottom: 1rem;\\n  color: #495057;\\n  font-weight: 600;\\n}\\n\\n.result-display[_ngcontent-%COMP%] {\\n  background-color: #fff;\\n  border: 1px solid #ced4da;\\n  border-radius: 0.25rem;\\n  padding: 1rem;\\n  font-size: 0.875rem;\\n  max-height: 200px;\\n  overflow-y: auto;\\n}\\n/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImhvdXNlaG9sZC1iaW5kaW5nLWRlbW8uY29tcG9uZW50LnRzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUNJO0VBQ0UsbUJBQUE7RUFDQSxhQUFBO0VBQ0EseUJBQUE7RUFDQSx1QkFBQTtFQUNBLHlCQUFBO0FBQU47O0FBR0k7RUFDRSxtQkFBQTtFQUNBLGNBQUE7RUFDQSxnQkFBQTtBQUFOOztBQUdJO0VBQ0Usc0JBQUE7RUFDQSx5QkFBQTtFQUNBLHNCQUFBO0VBQ0EsYUFBQTtFQUNBLG1CQUFBO0VBQ0EsaUJBQUE7RUFDQSxnQkFBQTtBQUFOIiwiZmlsZSI6ImhvdXNlaG9sZC1iaW5kaW5nLWRlbW8uY29tcG9uZW50LnRzIiwic291cmNlc0NvbnRlbnQiOlsiXG4gICAgLmRlbW8tc2VjdGlvbiB7XG4gICAgICBtYXJnaW4tYm90dG9tOiAycmVtO1xuICAgICAgcGFkZGluZzogMXJlbTtcbiAgICAgIGJvcmRlcjogMXB4IHNvbGlkICNlOWVjZWY7XG4gICAgICBib3JkZXItcmFkaXVzOiAwLjM3NXJlbTtcbiAgICAgIGJhY2tncm91bmQtY29sb3I6ICNmOGY5ZmE7XG4gICAgfVxuXG4gICAgLmRlbW8tc2VjdGlvbiBoNSB7XG4gICAgICBtYXJnaW4tYm90dG9tOiAxcmVtO1xuICAgICAgY29sb3I6ICM0OTUwNTc7XG4gICAgICBmb250LXdlaWdodDogNjAwO1xuICAgIH1cblxuICAgIC5yZXN1bHQtZGlzcGxheSB7XG4gICAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjZmZmO1xuICAgICAgYm9yZGVyOiAxcHggc29saWQgI2NlZDRkYTtcbiAgICAgIGJvcmRlci1yYWRpdXM6IDAuMjVyZW07XG4gICAgICBwYWRkaW5nOiAxcmVtO1xuICAgICAgZm9udC1zaXplOiAwLjg3NXJlbTtcbiAgICAgIG1heC1oZWlnaHQ6IDIwMHB4O1xuICAgICAgb3ZlcmZsb3cteTogYXV0bztcbiAgICB9XG4gICJdfQ== */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvc2hhcmVkL2NvbXBvbmVudHMvaG91c2Vob2xkLWJpbmRpbmcvaG91c2Vob2xkLWJpbmRpbmctZGVtby5jb21wb25lbnQudHMiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQ0k7RUFDRSxtQkFBQTtFQUNBLGFBQUE7RUFDQSx5QkFBQTtFQUNBLHVCQUFBO0VBQ0EseUJBQUE7QUFBTjs7QUFHSTtFQUNFLG1CQUFBO0VBQ0EsY0FBQTtFQUNBLGdCQUFBO0FBQU47O0FBR0k7RUFDRSxzQkFBQTtFQUNBLHlCQUFBO0VBQ0Esc0JBQUE7RUFDQSxhQUFBO0VBQ0EsbUJBQUE7RUFDQSxpQkFBQTtFQUNBLGdCQUFBO0FBQU47QUFDQSw0ckNBQTRyQyIsInNvdXJjZXNDb250ZW50IjpbIlxuICAgIC5kZW1vLXNlY3Rpb24ge1xuICAgICAgbWFyZ2luLWJvdHRvbTogMnJlbTtcbiAgICAgIHBhZGRpbmc6IDFyZW07XG4gICAgICBib3JkZXI6IDFweCBzb2xpZCAjZTllY2VmO1xuICAgICAgYm9yZGVyLXJhZGl1czogMC4zNzVyZW07XG4gICAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjZjhmOWZhO1xuICAgIH1cblxuICAgIC5kZW1vLXNlY3Rpb24gaDUge1xuICAgICAgbWFyZ2luLWJvdHRvbTogMXJlbTtcbiAgICAgIGNvbG9yOiAjNDk1MDU3O1xuICAgICAgZm9udC13ZWlnaHQ6IDYwMDtcbiAgICB9XG5cbiAgICAucmVzdWx0LWRpc3BsYXkge1xuICAgICAgYmFja2dyb3VuZC1jb2xvcjogI2ZmZjtcbiAgICAgIGJvcmRlcjogMXB4IHNvbGlkICNjZWQ0ZGE7XG4gICAgICBib3JkZXItcmFkaXVzOiAwLjI1cmVtO1xuICAgICAgcGFkZGluZzogMXJlbTtcbiAgICAgIGZvbnQtc2l6ZTogMC44NzVyZW07XG4gICAgICBtYXgtaGVpZ2h0OiAyMDBweDtcbiAgICAgIG92ZXJmbG93LXk6IGF1dG87XG4gICAgfVxuICAiXSwic291cmNlUm9vdCI6IiJ9 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate", "ɵɵpipeBind1", "ctx_r0", "selectedHouseholds1", "selectedItems", "debugData", "HouseholdBindingDemoComponent", "constructor", "selectedHouseholds2", "selectedHouseholds3", "selectedHouseholds4", "customBuildingData", "code", "building", "floor", "isSelected", "isDisabled", "Array", "from", "length", "_", "i", "String", "padStart", "Math", "ngOnInit", "onSelectionChange", "console", "log", "debugInfo", "testBuildingSelect", "selectors", "decls", "vars", "consts", "template", "HouseholdBindingDemoComponent_Template", "rf", "ctx", "ɵɵtwoWayListener", "HouseholdBindingDemoComponent_Template_app_household_binding_ngModelChange_8_listener", "$event", "ɵɵtwoWayBindingSet", "ɵɵlistener", "HouseholdBindingDemoComponent_Template_app_household_binding_selectionChange_8_listener", "HouseholdBindingDemoComponent_Template_app_household_binding_ngModelChange_12_listener", "HouseholdBindingDemoComponent_Template_app_household_binding_ngModelChange_16_listener", "HouseholdBindingDemoComponent_Template_app_household_binding_ngModelChange_20_listener", "ɵɵtemplate", "HouseholdBindingDemoComponent_div_21_Template", "HouseholdBindingDemoComponent_div_22_Template", "HouseholdBindingDemoComponent_Template_button_click_26_listener", "HouseholdBindingDemoComponent_Template_button_click_28_listener", "HouseholdBindingDemoComponent_div_30_Template", "ɵɵtwoWayProperty", "ɵɵproperty"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\shared\\components\\household-binding\\household-binding-demo.component.ts"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { HouseholdItem, BuildingData } from './household-binding.component';\r\n\r\n@Component({\r\n  selector: 'app-household-binding-demo',\r\n  template: `\r\n    <nb-card>\r\n      <nb-card-header>\r\n        <h4>戶別綁定元件示例</h4>\r\n      </nb-card-header>\r\n      <nb-card-body>        <div class=\"demo-section\">\r\n          <h5>基本使用</h5>\r\n          <app-household-binding\r\n            [(ngModel)]=\"selectedHouseholds1\"\r\n            placeholder=\"請選擇戶別\"\r\n            [maxSelections]=\"20\"\r\n            (selectionChange)=\"onSelectionChange($event)\">\r\n          </app-household-binding>\r\n        </div>\r\n\r\n        <div class=\"demo-section\">\r\n          <h5>不顯示已選擇區域</h5>\r\n          <app-household-binding\r\n            [(ngModel)]=\"selectedHouseholds2\"\r\n            placeholder=\"請選擇戶別\"\r\n            [showSelectedArea]=\"false\"\r\n            [maxSelections]=\"10\">\r\n          </app-household-binding>\r\n        </div>\r\n\r\n        <div class=\"demo-section\">\r\n          <h5>禁用搜尋和批次選擇</h5>\r\n          <app-household-binding\r\n            [(ngModel)]=\"selectedHouseholds3\"\r\n            placeholder=\"請選擇戶別\"\r\n            [allowSearch]=\"false\"\r\n            [allowBatchSelect]=\"false\">\r\n          </app-household-binding>\r\n        </div>\r\n\r\n        <div class=\"demo-section\">\r\n          <h5>自定義戶別資料</h5>\r\n          <app-household-binding\r\n            [(ngModel)]=\"selectedHouseholds4\"\r\n            placeholder=\"請選擇戶別\"\r\n            [buildingData]=\"customBuildingData\">\r\n          </app-household-binding>\r\n        </div>\r\n\r\n        <div class=\"demo-section\" *ngIf=\"selectedHouseholds1.length > 0\">\r\n          <h5>選擇結果 (selectedHouseholds1)</h5>\r\n          <pre class=\"result-display\">{{ selectedHouseholds1 | json }}</pre>\r\n        </div>        <div class=\"demo-section\" *ngIf=\"selectedItems.length > 0\">\r\n          <h5>詳細選擇項目</h5>\r\n          <pre class=\"result-display\">{{ selectedItems | json }}</pre>\r\n        </div>        <div class=\"demo-section\">\r\n          <h5>除錯資訊</h5>\r\n          <button type=\"button\" class=\"btn btn-secondary btn-sm me-2\" (click)=\"debugInfo()\">\r\n            檢查資料\r\n          </button>\r\n          <button type=\"button\" class=\"btn btn-info btn-sm me-2\" (click)=\"testBuildingSelect()\">\r\n            測試選擇 A棟\r\n          </button>\r\n          <div *ngIf=\"debugData\" class=\"mt-2\">\r\n            <small class=\"text-muted\">資料狀態：</small>\r\n            <pre class=\"result-display\">{{ debugData | json }}</pre>\r\n          </div>\r\n        </div>\r\n      </nb-card-body>\r\n    </nb-card>\r\n  `,\r\n  styles: [`\r\n    .demo-section {\r\n      margin-bottom: 2rem;\r\n      padding: 1rem;\r\n      border: 1px solid #e9ecef;\r\n      border-radius: 0.375rem;\r\n      background-color: #f8f9fa;\r\n    }\r\n\r\n    .demo-section h5 {\r\n      margin-bottom: 1rem;\r\n      color: #495057;\r\n      font-weight: 600;\r\n    }\r\n\r\n    .result-display {\r\n      background-color: #fff;\r\n      border: 1px solid #ced4da;\r\n      border-radius: 0.25rem;\r\n      padding: 1rem;\r\n      font-size: 0.875rem;\r\n      max-height: 200px;\r\n      overflow-y: auto;\r\n    }\r\n  `]\r\n})\r\nexport class HouseholdBindingDemoComponent implements OnInit {\r\n  selectedHouseholds1: string[] = [];\r\n  selectedHouseholds2: string[] = [];\r\n  selectedHouseholds3: string[] = [];\r\n  selectedHouseholds4: string[] = [];\r\n  selectedItems: HouseholdItem[] = [];\r\n  debugData: any = null;\r\n\r\n  customBuildingData: BuildingData = {\r\n    '總統套房': [\r\n      { code: 'P001', building: '總統套房', floor: '50F', isSelected: false, isDisabled: false },\r\n      { code: 'P002', building: '總統套房', floor: '51F', isSelected: false, isDisabled: false }\r\n    ],\r\n    '景觀樓層': Array.from({ length: 20 }, (_, i) => ({\r\n      code: `V${String(i + 1).padStart(3, '0')}`,\r\n      building: '景觀樓層',\r\n      floor: `${30 + Math.floor(i / 2)}F`,\r\n      isSelected: false,\r\n      isDisabled: i % 5 === 0 // 每五個禁用一個作為示例\r\n    }))\r\n  };\r\n\r\n  ngOnInit() {\r\n    // 初始化一些預選的戶別\r\n    this.selectedHouseholds1 = ['A001', 'A002', 'B001'];\r\n  }\r\n  onSelectionChange(selectedItems: HouseholdItem[]) {\r\n    this.selectedItems = selectedItems;\r\n    console.log('Selection changed:', selectedItems);\r\n  }\r\n  debugInfo() {\r\n    this.debugData = {\r\n      selectedHouseholds1: this.selectedHouseholds1,\r\n      selectedHouseholds2: this.selectedHouseholds2,\r\n      selectedHouseholds3: this.selectedHouseholds3,\r\n      selectedHouseholds4: this.selectedHouseholds4,\r\n      customBuildingData: this.customBuildingData,\r\n      selectedItems: this.selectedItems\r\n    };\r\n    console.log('Debug info:', this.debugData);\r\n  }\r\n\r\n  testBuildingSelect() {\r\n    console.log('Testing building selection...');\r\n    // 這個方法只是為了測試，實際上我們無法直接呼叫子元件的方法\r\n    // 主要是為了觸發除錯訊息\r\n    console.log('請在下拉選單中點擊 A棟，然後查看 Console 訊息');\r\n  }\r\n}\r\n"], "mappings": ";;;;;;;IAkDUA,EADF,CAAAC,cAAA,aAAiE,SAC3D;IAAAD,EAAA,CAAAE,MAAA,qDAA0B;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACnCH,EAAA,CAAAC,cAAA,aAA4B;IAAAD,EAAA,CAAAE,MAAA,GAAgC;;IAC9DF,EAD8D,CAAAG,YAAA,EAAM,EAC9D;;;;IADwBH,EAAA,CAAAI,SAAA,GAAgC;IAAhCJ,EAAA,CAAAK,iBAAA,CAAAL,EAAA,CAAAM,WAAA,OAAAC,MAAA,CAAAC,mBAAA,EAAgC;;;;;IAE5DR,EADY,CAAAC,cAAA,aAA2D,SACnE;IAAAD,EAAA,CAAAE,MAAA,2CAAM;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACfH,EAAA,CAAAC,cAAA,aAA4B;IAAAD,EAAA,CAAAE,MAAA,GAA0B;;IACxDF,EADwD,CAAAG,YAAA,EAAM,EACxD;;;;IADwBH,EAAA,CAAAI,SAAA,GAA0B;IAA1BJ,EAAA,CAAAK,iBAAA,CAAAL,EAAA,CAAAM,WAAA,OAAAC,MAAA,CAAAE,aAAA,EAA0B;;;;;IAUpDT,EADF,CAAAC,cAAA,cAAoC,gBACR;IAAAD,EAAA,CAAAE,MAAA,qCAAK;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACvCH,EAAA,CAAAC,cAAA,aAA4B;IAAAD,EAAA,CAAAE,MAAA,GAAsB;;IACpDF,EADoD,CAAAG,YAAA,EAAM,EACpD;;;;IADwBH,EAAA,CAAAI,SAAA,GAAsB;IAAtBJ,EAAA,CAAAK,iBAAA,CAAAL,EAAA,CAAAM,WAAA,OAAAC,MAAA,CAAAG,SAAA,EAAsB;;;AAgC9D,OAAM,MAAOC,6BAA6B;EA9F1CC,YAAA;IA+FE,KAAAJ,mBAAmB,GAAa,EAAE;IAClC,KAAAK,mBAAmB,GAAa,EAAE;IAClC,KAAAC,mBAAmB,GAAa,EAAE;IAClC,KAAAC,mBAAmB,GAAa,EAAE;IAClC,KAAAN,aAAa,GAAoB,EAAE;IACnC,KAAAC,SAAS,GAAQ,IAAI;IAErB,KAAAM,kBAAkB,GAAiB;MACjC,MAAM,EAAE,CACN;QAAEC,IAAI,EAAE,MAAM;QAAEC,QAAQ,EAAE,MAAM;QAAEC,KAAK,EAAE,KAAK;QAAEC,UAAU,EAAE,KAAK;QAAEC,UAAU,EAAE;MAAK,CAAE,EACtF;QAAEJ,IAAI,EAAE,MAAM;QAAEC,QAAQ,EAAE,MAAM;QAAEC,KAAK,EAAE,KAAK;QAAEC,UAAU,EAAE,KAAK;QAAEC,UAAU,EAAE;MAAK,CAAE,CACvF;MACD,MAAM,EAAEC,KAAK,CAACC,IAAI,CAAC;QAAEC,MAAM,EAAE;MAAE,CAAE,EAAE,CAACC,CAAC,EAAEC,CAAC,MAAM;QAC5CT,IAAI,EAAE,IAAIU,MAAM,CAACD,CAAC,GAAG,CAAC,CAAC,CAACE,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;QAC1CV,QAAQ,EAAE,MAAM;QAChBC,KAAK,EAAE,GAAG,EAAE,GAAGU,IAAI,CAACV,KAAK,CAACO,CAAC,GAAG,CAAC,CAAC,GAAG;QACnCN,UAAU,EAAE,KAAK;QACjBC,UAAU,EAAEK,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;OACzB,CAAC;KACH;;EAEDI,QAAQA,CAAA;IACN;IACA,IAAI,CAACtB,mBAAmB,GAAG,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC;EACrD;EACAuB,iBAAiBA,CAACtB,aAA8B;IAC9C,IAAI,CAACA,aAAa,GAAGA,aAAa;IAClCuB,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAExB,aAAa,CAAC;EAClD;EACAyB,SAASA,CAAA;IACP,IAAI,CAACxB,SAAS,GAAG;MACfF,mBAAmB,EAAE,IAAI,CAACA,mBAAmB;MAC7CK,mBAAmB,EAAE,IAAI,CAACA,mBAAmB;MAC7CC,mBAAmB,EAAE,IAAI,CAACA,mBAAmB;MAC7CC,mBAAmB,EAAE,IAAI,CAACA,mBAAmB;MAC7CC,kBAAkB,EAAE,IAAI,CAACA,kBAAkB;MAC3CP,aAAa,EAAE,IAAI,CAACA;KACrB;IACDuB,OAAO,CAACC,GAAG,CAAC,aAAa,EAAE,IAAI,CAACvB,SAAS,CAAC;EAC5C;EAEAyB,kBAAkBA,CAAA;IAChBH,OAAO,CAACC,GAAG,CAAC,+BAA+B,CAAC;IAC5C;IACA;IACAD,OAAO,CAACC,GAAG,CAAC,8BAA8B,CAAC;EAC7C;;;uCA/CWtB,6BAA6B;IAAA;EAAA;;;YAA7BA,6BAA6B;MAAAyB,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,uCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAzFlC1C,EAFJ,CAAAC,cAAA,cAAS,qBACS,SACV;UAAAD,EAAA,CAAAE,MAAA,uDAAQ;UACdF,EADc,CAAAG,YAAA,EAAK,EACF;UAEbH,EADJ,CAAAC,cAAA,mBAAc,aAAkC,SACxC;UAAAD,EAAA,CAAAE,MAAA,+BAAI;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACbH,EAAA,CAAAC,cAAA,+BAIgD;UAH9CD,EAAA,CAAA4C,gBAAA,2BAAAC,sFAAAC,MAAA;YAAA9C,EAAA,CAAA+C,kBAAA,CAAAJ,GAAA,CAAAnC,mBAAA,EAAAsC,MAAA,MAAAH,GAAA,CAAAnC,mBAAA,GAAAsC,MAAA;YAAA,OAAAA,MAAA;UAAA,EAAiC;UAGjC9C,EAAA,CAAAgD,UAAA,6BAAAC,wFAAAH,MAAA;YAAA,OAAmBH,GAAA,CAAAZ,iBAAA,CAAAe,MAAA,CAAyB;UAAA,EAAC;UAEjD9C,EADE,CAAAG,YAAA,EAAwB,EACpB;UAGJH,EADF,CAAAC,cAAA,aAA0B,UACpB;UAAAD,EAAA,CAAAE,MAAA,wDAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACjBH,EAAA,CAAAC,cAAA,gCAIuB;UAHrBD,EAAA,CAAA4C,gBAAA,2BAAAM,uFAAAJ,MAAA;YAAA9C,EAAA,CAAA+C,kBAAA,CAAAJ,GAAA,CAAA9B,mBAAA,EAAAiC,MAAA,MAAAH,GAAA,CAAA9B,mBAAA,GAAAiC,MAAA;YAAA,OAAAA,MAAA;UAAA,EAAiC;UAKrC9C,EADE,CAAAG,YAAA,EAAwB,EACpB;UAGJH,EADF,CAAAC,cAAA,cAA0B,UACpB;UAAAD,EAAA,CAAAE,MAAA,8DAAS;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAClBH,EAAA,CAAAC,cAAA,gCAI6B;UAH3BD,EAAA,CAAA4C,gBAAA,2BAAAO,uFAAAL,MAAA;YAAA9C,EAAA,CAAA+C,kBAAA,CAAAJ,GAAA,CAAA7B,mBAAA,EAAAgC,MAAA,MAAAH,GAAA,CAAA7B,mBAAA,GAAAgC,MAAA;YAAA,OAAAA,MAAA;UAAA,EAAiC;UAKrC9C,EADE,CAAAG,YAAA,EAAwB,EACpB;UAGJH,EADF,CAAAC,cAAA,cAA0B,UACpB;UAAAD,EAAA,CAAAE,MAAA,kDAAO;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAChBH,EAAA,CAAAC,cAAA,gCAGsC;UAFpCD,EAAA,CAAA4C,gBAAA,2BAAAQ,uFAAAN,MAAA;YAAA9C,EAAA,CAAA+C,kBAAA,CAAAJ,GAAA,CAAA5B,mBAAA,EAAA+B,MAAA,MAAAH,GAAA,CAAA5B,mBAAA,GAAA+B,MAAA;YAAA,OAAAA,MAAA;UAAA,EAAiC;UAIrC9C,EADE,CAAAG,YAAA,EAAwB,EACpB;UAKQH,EAHd,CAAAqD,UAAA,KAAAC,6CAAA,iBAAiE,KAAAC,6CAAA,iBAGQ;UAIvEvD,EADY,CAAAC,cAAA,cAA0B,UAClC;UAAAD,EAAA,CAAAE,MAAA,gCAAI;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACbH,EAAA,CAAAC,cAAA,iBAAkF;UAAtBD,EAAA,CAAAgD,UAAA,mBAAAQ,gEAAA;YAAA,OAASb,GAAA,CAAAT,SAAA,EAAW;UAAA,EAAC;UAC/ElC,EAAA,CAAAE,MAAA,kCACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACTH,EAAA,CAAAC,cAAA,iBAAsF;UAA/BD,EAAA,CAAAgD,UAAA,mBAAAS,gEAAA;YAAA,OAASd,GAAA,CAAAR,kBAAA,EAAoB;UAAA,EAAC;UACnFnC,EAAA,CAAAE,MAAA,0CACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACTH,EAAA,CAAAqD,UAAA,KAAAK,6CAAA,iBAAoC;UAM1C1D,EAFI,CAAAG,YAAA,EAAM,EACO,EACP;;;UAxDFH,EAAA,CAAAI,SAAA,GAAiC;UAAjCJ,EAAA,CAAA2D,gBAAA,YAAAhB,GAAA,CAAAnC,mBAAA,CAAiC;UAEjCR,EAAA,CAAA4D,UAAA,qBAAoB;UAQpB5D,EAAA,CAAAI,SAAA,GAAiC;UAAjCJ,EAAA,CAAA2D,gBAAA,YAAAhB,GAAA,CAAA9B,mBAAA,CAAiC;UAGjCb,EADA,CAAA4D,UAAA,2BAA0B,qBACN;UAOpB5D,EAAA,CAAAI,SAAA,GAAiC;UAAjCJ,EAAA,CAAA2D,gBAAA,YAAAhB,GAAA,CAAA7B,mBAAA,CAAiC;UAGjCd,EADA,CAAA4D,UAAA,sBAAqB,2BACK;UAO1B5D,EAAA,CAAAI,SAAA,GAAiC;UAAjCJ,EAAA,CAAA2D,gBAAA,YAAAhB,GAAA,CAAA5B,mBAAA,CAAiC;UAEjCf,EAAA,CAAA4D,UAAA,iBAAAjB,GAAA,CAAA3B,kBAAA,CAAmC;UAIZhB,EAAA,CAAAI,SAAA,EAAoC;UAApCJ,EAAA,CAAA4D,UAAA,SAAAjB,GAAA,CAAAnC,mBAAA,CAAAgB,MAAA,KAAoC;UAGtBxB,EAAA,CAAAI,SAAA,EAA8B;UAA9BJ,EAAA,CAAA4D,UAAA,SAAAjB,GAAA,CAAAlC,aAAA,CAAAe,MAAA,KAA8B;UAW/DxB,EAAA,CAAAI,SAAA,GAAe;UAAfJ,EAAA,CAAA4D,UAAA,SAAAjB,GAAA,CAAAjC,SAAA,CAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}