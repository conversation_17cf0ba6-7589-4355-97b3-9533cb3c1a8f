{"ast": null, "code": "import { map } from 'rxjs/operators';\nimport { BaseService } from '../base-service';\nimport { apiSpecialNoticeFileDeleteSpecialNoticeFilePost$Json } from '../fn/special-notice-file/api-special-notice-file-delete-special-notice-file-post-json';\nimport { apiSpecialNoticeFileDeleteSpecialNoticeFilePost$Plain } from '../fn/special-notice-file/api-special-notice-file-delete-special-notice-file-post-plain';\nimport { apiSpecialNoticeFileGetSpecialNoticeFileByIdPost$Json } from '../fn/special-notice-file/api-special-notice-file-get-special-notice-file-by-id-post-json';\nimport { apiSpecialNoticeFileGetSpecialNoticeFileByIdPost$Plain } from '../fn/special-notice-file/api-special-notice-file-get-special-notice-file-by-id-post-plain';\nimport { apiSpecialNoticeFileGetSpecialNoticeFileListPost$Json } from '../fn/special-notice-file/api-special-notice-file-get-special-notice-file-list-post-json';\nimport { apiSpecialNoticeFileGetSpecialNoticeFileListPost$Plain } from '../fn/special-notice-file/api-special-notice-file-get-special-notice-file-list-post-plain';\nimport { apiSpecialNoticeFileSaveSpecialNoticeFilePost$Json } from '../fn/special-notice-file/api-special-notice-file-save-special-notice-file-post-json';\nimport { apiSpecialNoticeFileSaveSpecialNoticeFilePost$Plain } from '../fn/special-notice-file/api-special-notice-file-save-special-notice-file-post-plain';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../api-configuration\";\nimport * as i2 from \"@angular/common/http\";\nexport class SpecialNoticeFileService extends BaseService {\n  constructor(config, http) {\n    super(config, http);\n  }\n  /** Path part for operation `apiSpecialNoticeFileDeleteSpecialNoticeFilePost()` */\n  static {\n    this.ApiSpecialNoticeFileDeleteSpecialNoticeFilePostPath = '/api/SpecialNoticeFile/DeleteSpecialNoticeFile';\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiSpecialNoticeFileDeleteSpecialNoticeFilePost$Plain()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiSpecialNoticeFileDeleteSpecialNoticeFilePost$Plain$Response(params, context) {\n    return apiSpecialNoticeFileDeleteSpecialNoticeFilePost$Plain(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiSpecialNoticeFileDeleteSpecialNoticeFilePost$Plain$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiSpecialNoticeFileDeleteSpecialNoticeFilePost$Plain(params, context) {\n    return this.apiSpecialNoticeFileDeleteSpecialNoticeFilePost$Plain$Response(params, context).pipe(map(r => r.body));\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiSpecialNoticeFileDeleteSpecialNoticeFilePost$Json()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiSpecialNoticeFileDeleteSpecialNoticeFilePost$Json$Response(params, context) {\n    return apiSpecialNoticeFileDeleteSpecialNoticeFilePost$Json(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiSpecialNoticeFileDeleteSpecialNoticeFilePost$Json$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiSpecialNoticeFileDeleteSpecialNoticeFilePost$Json(params, context) {\n    return this.apiSpecialNoticeFileDeleteSpecialNoticeFilePost$Json$Response(params, context).pipe(map(r => r.body));\n  }\n  /** Path part for operation `apiSpecialNoticeFileGetSpecialNoticeFileListPost()` */\n  static {\n    this.ApiSpecialNoticeFileGetSpecialNoticeFileListPostPath = '/api/SpecialNoticeFile/GetSpecialNoticeFileList';\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiSpecialNoticeFileGetSpecialNoticeFileListPost$Plain()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiSpecialNoticeFileGetSpecialNoticeFileListPost$Plain$Response(params, context) {\n    return apiSpecialNoticeFileGetSpecialNoticeFileListPost$Plain(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiSpecialNoticeFileGetSpecialNoticeFileListPost$Plain$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiSpecialNoticeFileGetSpecialNoticeFileListPost$Plain(params, context) {\n    return this.apiSpecialNoticeFileGetSpecialNoticeFileListPost$Plain$Response(params, context).pipe(map(r => r.body));\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiSpecialNoticeFileGetSpecialNoticeFileListPost$Json()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiSpecialNoticeFileGetSpecialNoticeFileListPost$Json$Response(params, context) {\n    return apiSpecialNoticeFileGetSpecialNoticeFileListPost$Json(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiSpecialNoticeFileGetSpecialNoticeFileListPost$Json$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiSpecialNoticeFileGetSpecialNoticeFileListPost$Json(params, context) {\n    return this.apiSpecialNoticeFileGetSpecialNoticeFileListPost$Json$Response(params, context).pipe(map(r => r.body));\n  }\n  /** Path part for operation `apiSpecialNoticeFileGetSpecialNoticeFileByIdPost()` */\n  static {\n    this.ApiSpecialNoticeFileGetSpecialNoticeFileByIdPostPath = '/api/SpecialNoticeFile/GetSpecialNoticeFileById';\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiSpecialNoticeFileGetSpecialNoticeFileByIdPost$Plain()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiSpecialNoticeFileGetSpecialNoticeFileByIdPost$Plain$Response(params, context) {\n    return apiSpecialNoticeFileGetSpecialNoticeFileByIdPost$Plain(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiSpecialNoticeFileGetSpecialNoticeFileByIdPost$Plain$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiSpecialNoticeFileGetSpecialNoticeFileByIdPost$Plain(params, context) {\n    return this.apiSpecialNoticeFileGetSpecialNoticeFileByIdPost$Plain$Response(params, context).pipe(map(r => r.body));\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiSpecialNoticeFileGetSpecialNoticeFileByIdPost$Json()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiSpecialNoticeFileGetSpecialNoticeFileByIdPost$Json$Response(params, context) {\n    return apiSpecialNoticeFileGetSpecialNoticeFileByIdPost$Json(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiSpecialNoticeFileGetSpecialNoticeFileByIdPost$Json$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiSpecialNoticeFileGetSpecialNoticeFileByIdPost$Json(params, context) {\n    return this.apiSpecialNoticeFileGetSpecialNoticeFileByIdPost$Json$Response(params, context).pipe(map(r => r.body));\n  }\n  /** Path part for operation `apiSpecialNoticeFileSaveSpecialNoticeFilePost()` */\n  static {\n    this.ApiSpecialNoticeFileSaveSpecialNoticeFilePostPath = '/api/SpecialNoticeFile/SaveSpecialNoticeFile';\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiSpecialNoticeFileSaveSpecialNoticeFilePost$Plain()` instead.\n   *\n   * This method sends `multipart/form-data` and handles request body of type `multipart/form-data`.\n   */\n  apiSpecialNoticeFileSaveSpecialNoticeFilePost$Plain$Response(params, context) {\n    return apiSpecialNoticeFileSaveSpecialNoticeFilePost$Plain(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiSpecialNoticeFileSaveSpecialNoticeFilePost$Plain$Response()` instead.\n   *\n   * This method sends `multipart/form-data` and handles request body of type `multipart/form-data`.\n   */\n  apiSpecialNoticeFileSaveSpecialNoticeFilePost$Plain(params, context) {\n    return this.apiSpecialNoticeFileSaveSpecialNoticeFilePost$Plain$Response(params, context).pipe(map(r => r.body));\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiSpecialNoticeFileSaveSpecialNoticeFilePost$Json()` instead.\n   *\n   * This method sends `multipart/form-data` and handles request body of type `multipart/form-data`.\n   */\n  apiSpecialNoticeFileSaveSpecialNoticeFilePost$Json$Response(params, context) {\n    return apiSpecialNoticeFileSaveSpecialNoticeFilePost$Json(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiSpecialNoticeFileSaveSpecialNoticeFilePost$Json$Response()` instead.\n   *\n   * This method sends `multipart/form-data` and handles request body of type `multipart/form-data`.\n   */\n  apiSpecialNoticeFileSaveSpecialNoticeFilePost$Json(params, context) {\n    return this.apiSpecialNoticeFileSaveSpecialNoticeFilePost$Json$Response(params, context).pipe(map(r => r.body));\n  }\n  static {\n    this.ɵfac = function SpecialNoticeFileService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || SpecialNoticeFileService)(i0.ɵɵinject(i1.ApiConfiguration), i0.ɵɵinject(i2.HttpClient));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: SpecialNoticeFileService,\n      factory: SpecialNoticeFileService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["map", "BaseService", "apiSpecialNoticeFileDeleteSpecialNoticeFilePost$Json", "apiSpecialNoticeFileDeleteSpecialNoticeFilePost$Plain", "apiSpecialNoticeFileGetSpecialNoticeFileByIdPost$Json", "apiSpecialNoticeFileGetSpecialNoticeFileByIdPost$Plain", "apiSpecialNoticeFileGetSpecialNoticeFileListPost$Json", "apiSpecialNoticeFileGetSpecialNoticeFileListPost$Plain", "apiSpecialNoticeFileSaveSpecialNoticeFilePost$Json", "apiSpecialNoticeFileSaveSpecialNoticeFilePost$Plain", "SpecialNoticeFileService", "constructor", "config", "http", "ApiSpecialNoticeFileDeleteSpecialNoticeFilePostPath", "apiSpecialNoticeFileDeleteSpecialNoticeFilePost$Plain$Response", "params", "context", "rootUrl", "pipe", "r", "body", "apiSpecialNoticeFileDeleteSpecialNoticeFilePost$Json$Response", "ApiSpecialNoticeFileGetSpecialNoticeFileListPostPath", "apiSpecialNoticeFileGetSpecialNoticeFileListPost$Plain$Response", "apiSpecialNoticeFileGetSpecialNoticeFileListPost$Json$Response", "ApiSpecialNoticeFileGetSpecialNoticeFileByIdPostPath", "apiSpecialNoticeFileGetSpecialNoticeFileByIdPost$Plain$Response", "apiSpecialNoticeFileGetSpecialNoticeFileByIdPost$Json$Response", "ApiSpecialNoticeFileSaveSpecialNoticeFilePostPath", "apiSpecialNoticeFileSaveSpecialNoticeFilePost$Plain$Response", "apiSpecialNoticeFileSaveSpecialNoticeFilePost$Json$Response", "i0", "ɵɵinject", "i1", "ApiConfiguration", "i2", "HttpClient", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\services\\api\\services\\special-notice-file.service.ts"], "sourcesContent": ["/* tslint:disable */\r\n/* eslint-disable */\r\nimport { HttpClient, HttpContext } from '@angular/common/http';\r\nimport { Injectable } from '@angular/core';\r\nimport { Observable } from 'rxjs';\r\nimport { map } from 'rxjs/operators';\r\n\r\nimport { BaseService } from '../base-service';\r\nimport { ApiConfiguration } from '../api-configuration';\r\nimport { StrictHttpResponse } from '../strict-http-response';\r\n\r\nimport { apiSpecialNoticeFileDeleteSpecialNoticeFilePost$Json } from '../fn/special-notice-file/api-special-notice-file-delete-special-notice-file-post-json';\r\nimport { ApiSpecialNoticeFileDeleteSpecialNoticeFilePost$Json$Params } from '../fn/special-notice-file/api-special-notice-file-delete-special-notice-file-post-json';\r\nimport { apiSpecialNoticeFileDeleteSpecialNoticeFilePost$Plain } from '../fn/special-notice-file/api-special-notice-file-delete-special-notice-file-post-plain';\r\nimport { ApiSpecialNoticeFileDeleteSpecialNoticeFilePost$Plain$Params } from '../fn/special-notice-file/api-special-notice-file-delete-special-notice-file-post-plain';\r\nimport { apiSpecialNoticeFileGetSpecialNoticeFileByIdPost$Json } from '../fn/special-notice-file/api-special-notice-file-get-special-notice-file-by-id-post-json';\r\nimport { ApiSpecialNoticeFileGetSpecialNoticeFileByIdPost$Json$Params } from '../fn/special-notice-file/api-special-notice-file-get-special-notice-file-by-id-post-json';\r\nimport { apiSpecialNoticeFileGetSpecialNoticeFileByIdPost$Plain } from '../fn/special-notice-file/api-special-notice-file-get-special-notice-file-by-id-post-plain';\r\nimport { ApiSpecialNoticeFileGetSpecialNoticeFileByIdPost$Plain$Params } from '../fn/special-notice-file/api-special-notice-file-get-special-notice-file-by-id-post-plain';\r\nimport { apiSpecialNoticeFileGetSpecialNoticeFileListPost$Json } from '../fn/special-notice-file/api-special-notice-file-get-special-notice-file-list-post-json';\r\nimport { ApiSpecialNoticeFileGetSpecialNoticeFileListPost$Json$Params } from '../fn/special-notice-file/api-special-notice-file-get-special-notice-file-list-post-json';\r\nimport { apiSpecialNoticeFileGetSpecialNoticeFileListPost$Plain } from '../fn/special-notice-file/api-special-notice-file-get-special-notice-file-list-post-plain';\r\nimport { ApiSpecialNoticeFileGetSpecialNoticeFileListPost$Plain$Params } from '../fn/special-notice-file/api-special-notice-file-get-special-notice-file-list-post-plain';\r\nimport { apiSpecialNoticeFileSaveSpecialNoticeFilePost$Json } from '../fn/special-notice-file/api-special-notice-file-save-special-notice-file-post-json';\r\nimport { ApiSpecialNoticeFileSaveSpecialNoticeFilePost$Json$Params } from '../fn/special-notice-file/api-special-notice-file-save-special-notice-file-post-json';\r\nimport { apiSpecialNoticeFileSaveSpecialNoticeFilePost$Plain } from '../fn/special-notice-file/api-special-notice-file-save-special-notice-file-post-plain';\r\nimport { ApiSpecialNoticeFileSaveSpecialNoticeFilePost$Plain$Params } from '../fn/special-notice-file/api-special-notice-file-save-special-notice-file-post-plain';\r\nimport { GetSpecialNoticeFileByIdResResponseBase } from '../models/get-special-notice-file-by-id-res-response-base';\r\nimport { GetSpecialNoticeFileListResResponseBase } from '../models/get-special-notice-file-list-res-response-base';\r\nimport { StringResponseBase } from '../models/string-response-base';\r\n\r\n@Injectable({ providedIn: 'root' })\r\nexport class SpecialNoticeFileService extends BaseService {\r\n  constructor(config: ApiConfiguration, http: HttpClient) {\r\n    super(config, http);\r\n  }\r\n\r\n  /** Path part for operation `apiSpecialNoticeFileDeleteSpecialNoticeFilePost()` */\r\n  static readonly ApiSpecialNoticeFileDeleteSpecialNoticeFilePostPath = '/api/SpecialNoticeFile/DeleteSpecialNoticeFile';\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiSpecialNoticeFileDeleteSpecialNoticeFilePost$Plain()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiSpecialNoticeFileDeleteSpecialNoticeFilePost$Plain$Response(params?: ApiSpecialNoticeFileDeleteSpecialNoticeFilePost$Plain$Params, context?: HttpContext): Observable<StrictHttpResponse<StringResponseBase>> {\r\n    return apiSpecialNoticeFileDeleteSpecialNoticeFilePost$Plain(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiSpecialNoticeFileDeleteSpecialNoticeFilePost$Plain$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiSpecialNoticeFileDeleteSpecialNoticeFilePost$Plain(params?: ApiSpecialNoticeFileDeleteSpecialNoticeFilePost$Plain$Params, context?: HttpContext): Observable<StringResponseBase> {\r\n    return this.apiSpecialNoticeFileDeleteSpecialNoticeFilePost$Plain$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<StringResponseBase>): StringResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiSpecialNoticeFileDeleteSpecialNoticeFilePost$Json()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiSpecialNoticeFileDeleteSpecialNoticeFilePost$Json$Response(params?: ApiSpecialNoticeFileDeleteSpecialNoticeFilePost$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<StringResponseBase>> {\r\n    return apiSpecialNoticeFileDeleteSpecialNoticeFilePost$Json(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiSpecialNoticeFileDeleteSpecialNoticeFilePost$Json$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiSpecialNoticeFileDeleteSpecialNoticeFilePost$Json(params?: ApiSpecialNoticeFileDeleteSpecialNoticeFilePost$Json$Params, context?: HttpContext): Observable<StringResponseBase> {\r\n    return this.apiSpecialNoticeFileDeleteSpecialNoticeFilePost$Json$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<StringResponseBase>): StringResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /** Path part for operation `apiSpecialNoticeFileGetSpecialNoticeFileListPost()` */\r\n  static readonly ApiSpecialNoticeFileGetSpecialNoticeFileListPostPath = '/api/SpecialNoticeFile/GetSpecialNoticeFileList';\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiSpecialNoticeFileGetSpecialNoticeFileListPost$Plain()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiSpecialNoticeFileGetSpecialNoticeFileListPost$Plain$Response(params?: ApiSpecialNoticeFileGetSpecialNoticeFileListPost$Plain$Params, context?: HttpContext): Observable<StrictHttpResponse<GetSpecialNoticeFileListResResponseBase>> {\r\n    return apiSpecialNoticeFileGetSpecialNoticeFileListPost$Plain(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiSpecialNoticeFileGetSpecialNoticeFileListPost$Plain$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiSpecialNoticeFileGetSpecialNoticeFileListPost$Plain(params?: ApiSpecialNoticeFileGetSpecialNoticeFileListPost$Plain$Params, context?: HttpContext): Observable<GetSpecialNoticeFileListResResponseBase> {\r\n    return this.apiSpecialNoticeFileGetSpecialNoticeFileListPost$Plain$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<GetSpecialNoticeFileListResResponseBase>): GetSpecialNoticeFileListResResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiSpecialNoticeFileGetSpecialNoticeFileListPost$Json()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiSpecialNoticeFileGetSpecialNoticeFileListPost$Json$Response(params?: ApiSpecialNoticeFileGetSpecialNoticeFileListPost$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<GetSpecialNoticeFileListResResponseBase>> {\r\n    return apiSpecialNoticeFileGetSpecialNoticeFileListPost$Json(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiSpecialNoticeFileGetSpecialNoticeFileListPost$Json$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiSpecialNoticeFileGetSpecialNoticeFileListPost$Json(params?: ApiSpecialNoticeFileGetSpecialNoticeFileListPost$Json$Params, context?: HttpContext): Observable<GetSpecialNoticeFileListResResponseBase> {\r\n    return this.apiSpecialNoticeFileGetSpecialNoticeFileListPost$Json$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<GetSpecialNoticeFileListResResponseBase>): GetSpecialNoticeFileListResResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /** Path part for operation `apiSpecialNoticeFileGetSpecialNoticeFileByIdPost()` */\r\n  static readonly ApiSpecialNoticeFileGetSpecialNoticeFileByIdPostPath = '/api/SpecialNoticeFile/GetSpecialNoticeFileById';\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiSpecialNoticeFileGetSpecialNoticeFileByIdPost$Plain()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiSpecialNoticeFileGetSpecialNoticeFileByIdPost$Plain$Response(params?: ApiSpecialNoticeFileGetSpecialNoticeFileByIdPost$Plain$Params, context?: HttpContext): Observable<StrictHttpResponse<GetSpecialNoticeFileByIdResResponseBase>> {\r\n    return apiSpecialNoticeFileGetSpecialNoticeFileByIdPost$Plain(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiSpecialNoticeFileGetSpecialNoticeFileByIdPost$Plain$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiSpecialNoticeFileGetSpecialNoticeFileByIdPost$Plain(params?: ApiSpecialNoticeFileGetSpecialNoticeFileByIdPost$Plain$Params, context?: HttpContext): Observable<GetSpecialNoticeFileByIdResResponseBase> {\r\n    return this.apiSpecialNoticeFileGetSpecialNoticeFileByIdPost$Plain$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<GetSpecialNoticeFileByIdResResponseBase>): GetSpecialNoticeFileByIdResResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiSpecialNoticeFileGetSpecialNoticeFileByIdPost$Json()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiSpecialNoticeFileGetSpecialNoticeFileByIdPost$Json$Response(params?: ApiSpecialNoticeFileGetSpecialNoticeFileByIdPost$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<GetSpecialNoticeFileByIdResResponseBase>> {\r\n    return apiSpecialNoticeFileGetSpecialNoticeFileByIdPost$Json(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiSpecialNoticeFileGetSpecialNoticeFileByIdPost$Json$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiSpecialNoticeFileGetSpecialNoticeFileByIdPost$Json(params?: ApiSpecialNoticeFileGetSpecialNoticeFileByIdPost$Json$Params, context?: HttpContext): Observable<GetSpecialNoticeFileByIdResResponseBase> {\r\n    return this.apiSpecialNoticeFileGetSpecialNoticeFileByIdPost$Json$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<GetSpecialNoticeFileByIdResResponseBase>): GetSpecialNoticeFileByIdResResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /** Path part for operation `apiSpecialNoticeFileSaveSpecialNoticeFilePost()` */\r\n  static readonly ApiSpecialNoticeFileSaveSpecialNoticeFilePostPath = '/api/SpecialNoticeFile/SaveSpecialNoticeFile';\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiSpecialNoticeFileSaveSpecialNoticeFilePost$Plain()` instead.\r\n   *\r\n   * This method sends `multipart/form-data` and handles request body of type `multipart/form-data`.\r\n   */\r\n  apiSpecialNoticeFileSaveSpecialNoticeFilePost$Plain$Response(params?: ApiSpecialNoticeFileSaveSpecialNoticeFilePost$Plain$Params, context?: HttpContext): Observable<StrictHttpResponse<StringResponseBase>> {\r\n    return apiSpecialNoticeFileSaveSpecialNoticeFilePost$Plain(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiSpecialNoticeFileSaveSpecialNoticeFilePost$Plain$Response()` instead.\r\n   *\r\n   * This method sends `multipart/form-data` and handles request body of type `multipart/form-data`.\r\n   */\r\n  apiSpecialNoticeFileSaveSpecialNoticeFilePost$Plain(params?: ApiSpecialNoticeFileSaveSpecialNoticeFilePost$Plain$Params, context?: HttpContext): Observable<StringResponseBase> {\r\n    return this.apiSpecialNoticeFileSaveSpecialNoticeFilePost$Plain$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<StringResponseBase>): StringResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiSpecialNoticeFileSaveSpecialNoticeFilePost$Json()` instead.\r\n   *\r\n   * This method sends `multipart/form-data` and handles request body of type `multipart/form-data`.\r\n   */\r\n  apiSpecialNoticeFileSaveSpecialNoticeFilePost$Json$Response(params?: ApiSpecialNoticeFileSaveSpecialNoticeFilePost$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<StringResponseBase>> {\r\n    return apiSpecialNoticeFileSaveSpecialNoticeFilePost$Json(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiSpecialNoticeFileSaveSpecialNoticeFilePost$Json$Response()` instead.\r\n   *\r\n   * This method sends `multipart/form-data` and handles request body of type `multipart/form-data`.\r\n   */\r\n  apiSpecialNoticeFileSaveSpecialNoticeFilePost$Json(params?: ApiSpecialNoticeFileSaveSpecialNoticeFilePost$Json$Params, context?: HttpContext): Observable<StringResponseBase> {\r\n    return this.apiSpecialNoticeFileSaveSpecialNoticeFilePost$Json$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<StringResponseBase>): StringResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n}\r\n"], "mappings": "AAKA,SAASA,GAAG,QAAQ,gBAAgB;AAEpC,SAASC,WAAW,QAAQ,iBAAiB;AAI7C,SAASC,oDAAoD,QAAQ,wFAAwF;AAE7J,SAASC,qDAAqD,QAAQ,yFAAyF;AAE/J,SAASC,qDAAqD,QAAQ,2FAA2F;AAEjK,SAASC,sDAAsD,QAAQ,4FAA4F;AAEnK,SAASC,qDAAqD,QAAQ,0FAA0F;AAEhK,SAASC,sDAAsD,QAAQ,2FAA2F;AAElK,SAASC,kDAAkD,QAAQ,sFAAsF;AAEzJ,SAASC,mDAAmD,QAAQ,uFAAuF;;;;AAO3J,OAAM,MAAOC,wBAAyB,SAAQT,WAAW;EACvDU,YAAYC,MAAwB,EAAEC,IAAgB;IACpD,KAAK,CAACD,MAAM,EAAEC,IAAI,CAAC;EACrB;EAEA;;IACgB,KAAAC,mDAAmD,GAAG,gDAAgD;EAAC;EAEvH;;;;;;EAMAC,8DAA8DA,CAACC,MAAqE,EAAEC,OAAqB;IACzJ,OAAOd,qDAAqD,CAAC,IAAI,CAACU,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EACxG;EAEA;;;;;;EAMAd,qDAAqDA,CAACa,MAAqE,EAAEC,OAAqB;IAChJ,OAAO,IAAI,CAACF,8DAA8D,CAACC,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CAC9FnB,GAAG,CAAEoB,CAAyC,IAAyBA,CAAC,CAACC,IAAI,CAAC,CAC/E;EACH;EAEA;;;;;;EAMAC,6DAA6DA,CAACN,MAAoE,EAAEC,OAAqB;IACvJ,OAAOf,oDAAoD,CAAC,IAAI,CAACW,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EACvG;EAEA;;;;;;EAMAf,oDAAoDA,CAACc,MAAoE,EAAEC,OAAqB;IAC9I,OAAO,IAAI,CAACK,6DAA6D,CAACN,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CAC7FnB,GAAG,CAAEoB,CAAyC,IAAyBA,CAAC,CAACC,IAAI,CAAC,CAC/E;EACH;EAEA;;IACgB,KAAAE,oDAAoD,GAAG,iDAAiD;EAAC;EAEzH;;;;;;EAMAC,+DAA+DA,CAACR,MAAsE,EAAEC,OAAqB;IAC3J,OAAOV,sDAAsD,CAAC,IAAI,CAACM,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EACzG;EAEA;;;;;;EAMAV,sDAAsDA,CAACS,MAAsE,EAAEC,OAAqB;IAClJ,OAAO,IAAI,CAACO,+DAA+D,CAACR,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CAC/FnB,GAAG,CAAEoB,CAA8D,IAA8CA,CAAC,CAACC,IAAI,CAAC,CACzH;EACH;EAEA;;;;;;EAMAI,8DAA8DA,CAACT,MAAqE,EAAEC,OAAqB;IACzJ,OAAOX,qDAAqD,CAAC,IAAI,CAACO,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EACxG;EAEA;;;;;;EAMAX,qDAAqDA,CAACU,MAAqE,EAAEC,OAAqB;IAChJ,OAAO,IAAI,CAACQ,8DAA8D,CAACT,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CAC9FnB,GAAG,CAAEoB,CAA8D,IAA8CA,CAAC,CAACC,IAAI,CAAC,CACzH;EACH;EAEA;;IACgB,KAAAK,oDAAoD,GAAG,iDAAiD;EAAC;EAEzH;;;;;;EAMAC,+DAA+DA,CAACX,MAAsE,EAAEC,OAAqB;IAC3J,OAAOZ,sDAAsD,CAAC,IAAI,CAACQ,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EACzG;EAEA;;;;;;EAMAZ,sDAAsDA,CAACW,MAAsE,EAAEC,OAAqB;IAClJ,OAAO,IAAI,CAACU,+DAA+D,CAACX,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CAC/FnB,GAAG,CAAEoB,CAA8D,IAA8CA,CAAC,CAACC,IAAI,CAAC,CACzH;EACH;EAEA;;;;;;EAMAO,8DAA8DA,CAACZ,MAAqE,EAAEC,OAAqB;IACzJ,OAAOb,qDAAqD,CAAC,IAAI,CAACS,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EACxG;EAEA;;;;;;EAMAb,qDAAqDA,CAACY,MAAqE,EAAEC,OAAqB;IAChJ,OAAO,IAAI,CAACW,8DAA8D,CAACZ,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CAC9FnB,GAAG,CAAEoB,CAA8D,IAA8CA,CAAC,CAACC,IAAI,CAAC,CACzH;EACH;EAEA;;IACgB,KAAAQ,iDAAiD,GAAG,8CAA8C;EAAC;EAEnH;;;;;;EAMAC,4DAA4DA,CAACd,MAAmE,EAAEC,OAAqB;IACrJ,OAAOR,mDAAmD,CAAC,IAAI,CAACI,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EACtG;EAEA;;;;;;EAMAR,mDAAmDA,CAACO,MAAmE,EAAEC,OAAqB;IAC5I,OAAO,IAAI,CAACa,4DAA4D,CAACd,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CAC5FnB,GAAG,CAAEoB,CAAyC,IAAyBA,CAAC,CAACC,IAAI,CAAC,CAC/E;EACH;EAEA;;;;;;EAMAU,2DAA2DA,CAACf,MAAkE,EAAEC,OAAqB;IACnJ,OAAOT,kDAAkD,CAAC,IAAI,CAACK,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EACrG;EAEA;;;;;;EAMAT,kDAAkDA,CAACQ,MAAkE,EAAEC,OAAqB;IAC1I,OAAO,IAAI,CAACc,2DAA2D,CAACf,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CAC3FnB,GAAG,CAAEoB,CAAyC,IAAyBA,CAAC,CAACC,IAAI,CAAC,CAC/E;EACH;;;uCA/LWX,wBAAwB,EAAAsB,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,gBAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,UAAA;IAAA;EAAA;;;aAAxB3B,wBAAwB;MAAA4B,OAAA,EAAxB5B,wBAAwB,CAAA6B,IAAA;MAAAC,UAAA,EADX;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}