{"ast": null, "code": "//! moment.js locale configuration\n//! locale : Basque [eu]\n//! author : <PERSON><PERSON><PERSON> : https://github.com/eillarra\n\n;\n(function (global, factory) {\n  typeof exports === 'object' && typeof module !== 'undefined' && typeof require === 'function' ? factory(require('../moment')) : typeof define === 'function' && define.amd ? define(['../moment'], factory) : factory(global.moment);\n})(this, function (moment) {\n  'use strict';\n\n  //! moment.js locale configuration\n  var eu = moment.defineLocale('eu', {\n    months: 'urtarrila_otsaila_martxoa_apirila_maiatza_ekaina_uztaila_abuztua_iraila_urria_azaroa_abendua'.split('_'),\n    monthsShort: 'urt._ots._mar._api._mai._eka._uzt._abu._ira._urr._aza._abe.'.split('_'),\n    monthsParseExact: true,\n    weekdays: 'igandea_astelehena_asteartea_asteazkena_osteguna_ostirala_larunbata'.split('_'),\n    weekdaysShort: 'ig._al._ar._az._og._ol._lr.'.split('_'),\n    weekdaysMin: 'ig_al_ar_az_og_ol_lr'.split('_'),\n    weekdaysParseExact: true,\n    longDateFormat: {\n      LT: 'HH:mm',\n      LTS: 'HH:mm:ss',\n      L: 'YYYY-MM-DD',\n      LL: 'YYYY[ko] MMMM[ren] D[a]',\n      LLL: 'YYYY[ko] MMMM[ren] D[a] HH:mm',\n      LLLL: 'dddd, YYYY[ko] MMMM[ren] D[a] HH:mm',\n      l: 'YYYY-M-D',\n      ll: 'YYYY[ko] MMM D[a]',\n      lll: 'YYYY[ko] MMM D[a] HH:mm',\n      llll: 'ddd, YYYY[ko] MMM D[a] HH:mm'\n    },\n    calendar: {\n      sameDay: '[gaur] LT[etan]',\n      nextDay: '[bihar] LT[etan]',\n      nextWeek: 'dddd LT[etan]',\n      lastDay: '[atzo] LT[etan]',\n      lastWeek: '[aurreko] dddd LT[etan]',\n      sameElse: 'L'\n    },\n    relativeTime: {\n      future: '%s barru',\n      past: 'duela %s',\n      s: 'segundo batzuk',\n      ss: '%d segundo',\n      m: 'minutu bat',\n      mm: '%d minutu',\n      h: 'ordu bat',\n      hh: '%d ordu',\n      d: 'egun bat',\n      dd: '%d egun',\n      M: 'hilabete bat',\n      MM: '%d hilabete',\n      y: 'urte bat',\n      yy: '%d urte'\n    },\n    dayOfMonthOrdinalParse: /\\d{1,2}\\./,\n    ordinal: '%d.',\n    week: {\n      dow: 1,\n      // Monday is the first day of the week.\n      doy: 7 // The week that contains Jan 7th is the first week of the year.\n    }\n  });\n  return eu;\n});", "map": {"version": 3, "names": ["global", "factory", "exports", "module", "require", "define", "amd", "moment", "eu", "defineLocale", "months", "split", "monthsShort", "monthsParseExact", "weekdays", "weekdaysShort", "weekdaysMin", "weekdaysParseExact", "longDateFormat", "LT", "LTS", "L", "LL", "LLL", "LLLL", "l", "ll", "lll", "llll", "calendar", "sameDay", "nextDay", "nextWeek", "lastDay", "lastWeek", "same<PERSON><PERSON><PERSON>", "relativeTime", "future", "past", "s", "ss", "m", "mm", "h", "hh", "d", "dd", "M", "MM", "y", "yy", "dayOfMonthOrdinalParse", "ordinal", "week", "dow", "doy"], "sources": ["C:/Users/<USER>/Documents/salechange-product/adminFront/node_modules/moment/locale/eu.js"], "sourcesContent": ["//! moment.js locale configuration\n//! locale : Basque [eu]\n//! author : <PERSON><PERSON><PERSON> : https://github.com/eillarra\n\n;(function (global, factory) {\n   typeof exports === 'object' && typeof module !== 'undefined'\n       && typeof require === 'function' ? factory(require('../moment')) :\n   typeof define === 'function' && define.amd ? define(['../moment'], factory) :\n   factory(global.moment)\n}(this, (function (moment) { 'use strict';\n\n    //! moment.js locale configuration\n\n    var eu = moment.defineLocale('eu', {\n        months: 'urtarrila_otsaila_martxoa_apirila_maiatza_ekaina_uztaila_abuztua_iraila_urria_azaroa_abendua'.split(\n            '_'\n        ),\n        monthsShort:\n            'urt._ots._mar._api._mai._eka._uzt._abu._ira._urr._aza._abe.'.split(\n                '_'\n            ),\n        monthsParseExact: true,\n        weekdays:\n            'igandea_astelehena_asteartea_asteazkena_osteguna_ostirala_larunbata'.split(\n                '_'\n            ),\n        weekdaysShort: 'ig._al._ar._az._og._ol._lr.'.split('_'),\n        weekdaysMin: 'ig_al_ar_az_og_ol_lr'.split('_'),\n        weekdaysParseExact: true,\n        longDateFormat: {\n            LT: 'HH:mm',\n            LTS: 'HH:mm:ss',\n            L: 'YYYY-MM-DD',\n            LL: 'YYYY[ko] MMMM[ren] D[a]',\n            LLL: 'YYYY[ko] MMMM[ren] D[a] HH:mm',\n            LLLL: 'dddd, YYYY[ko] MMMM[ren] D[a] HH:mm',\n            l: 'YYYY-M-D',\n            ll: 'YYYY[ko] MMM D[a]',\n            lll: 'YYYY[ko] MMM D[a] HH:mm',\n            llll: 'ddd, YYYY[ko] MMM D[a] HH:mm',\n        },\n        calendar: {\n            sameDay: '[gaur] LT[etan]',\n            nextDay: '[bihar] LT[etan]',\n            nextWeek: 'dddd LT[etan]',\n            lastDay: '[atzo] LT[etan]',\n            lastWeek: '[aurreko] dddd LT[etan]',\n            sameElse: 'L',\n        },\n        relativeTime: {\n            future: '%s barru',\n            past: 'duela %s',\n            s: 'segundo batzuk',\n            ss: '%d segundo',\n            m: 'minutu bat',\n            mm: '%d minutu',\n            h: 'ordu bat',\n            hh: '%d ordu',\n            d: 'egun bat',\n            dd: '%d egun',\n            M: 'hilabete bat',\n            MM: '%d hilabete',\n            y: 'urte bat',\n            yy: '%d urte',\n        },\n        dayOfMonthOrdinalParse: /\\d{1,2}\\./,\n        ordinal: '%d.',\n        week: {\n            dow: 1, // Monday is the first day of the week.\n            doy: 7, // The week that contains Jan 7th is the first week of the year.\n        },\n    });\n\n    return eu;\n\n})));\n"], "mappings": "AAAA;AACA;AACA;;AAEA;AAAE,WAAUA,MAAM,EAAEC,OAAO,EAAE;EAC1B,OAAOC,OAAO,KAAK,QAAQ,IAAI,OAAOC,MAAM,KAAK,WAAW,IACrD,OAAOC,OAAO,KAAK,UAAU,GAAGH,OAAO,CAACG,OAAO,CAAC,WAAW,CAAC,CAAC,GACpE,OAAOC,MAAM,KAAK,UAAU,IAAIA,MAAM,CAACC,GAAG,GAAGD,MAAM,CAAC,CAAC,WAAW,CAAC,EAAEJ,OAAO,CAAC,GAC3EA,OAAO,CAACD,MAAM,CAACO,MAAM,CAAC;AACzB,CAAC,EAAC,IAAI,EAAG,UAAUA,MAAM,EAAE;EAAE,YAAY;;EAErC;EAEA,IAAIC,EAAE,GAAGD,MAAM,CAACE,YAAY,CAAC,IAAI,EAAE;IAC/BC,MAAM,EAAE,8FAA8F,CAACC,KAAK,CACxG,GACJ,CAAC;IACDC,WAAW,EACP,6DAA6D,CAACD,KAAK,CAC/D,GACJ,CAAC;IACLE,gBAAgB,EAAE,IAAI;IACtBC,QAAQ,EACJ,qEAAqE,CAACH,KAAK,CACvE,GACJ,CAAC;IACLI,aAAa,EAAE,6BAA6B,CAACJ,KAAK,CAAC,GAAG,CAAC;IACvDK,WAAW,EAAE,sBAAsB,CAACL,KAAK,CAAC,GAAG,CAAC;IAC9CM,kBAAkB,EAAE,IAAI;IACxBC,cAAc,EAAE;MACZC,EAAE,EAAE,OAAO;MACXC,GAAG,EAAE,UAAU;MACfC,CAAC,EAAE,YAAY;MACfC,EAAE,EAAE,yBAAyB;MAC7BC,GAAG,EAAE,+BAA+B;MACpCC,IAAI,EAAE,qCAAqC;MAC3CC,CAAC,EAAE,UAAU;MACbC,EAAE,EAAE,mBAAmB;MACvBC,GAAG,EAAE,yBAAyB;MAC9BC,IAAI,EAAE;IACV,CAAC;IACDC,QAAQ,EAAE;MACNC,OAAO,EAAE,iBAAiB;MAC1BC,OAAO,EAAE,kBAAkB;MAC3BC,QAAQ,EAAE,eAAe;MACzBC,OAAO,EAAE,iBAAiB;MAC1BC,QAAQ,EAAE,yBAAyB;MACnCC,QAAQ,EAAE;IACd,CAAC;IACDC,YAAY,EAAE;MACVC,MAAM,EAAE,UAAU;MAClBC,IAAI,EAAE,UAAU;MAChBC,CAAC,EAAE,gBAAgB;MACnBC,EAAE,EAAE,YAAY;MAChBC,CAAC,EAAE,YAAY;MACfC,EAAE,EAAE,WAAW;MACfC,CAAC,EAAE,UAAU;MACbC,EAAE,EAAE,SAAS;MACbC,CAAC,EAAE,UAAU;MACbC,EAAE,EAAE,SAAS;MACbC,CAAC,EAAE,cAAc;MACjBC,EAAE,EAAE,aAAa;MACjBC,CAAC,EAAE,UAAU;MACbC,EAAE,EAAE;IACR,CAAC;IACDC,sBAAsB,EAAE,WAAW;IACnCC,OAAO,EAAE,KAAK;IACdC,IAAI,EAAE;MACFC,GAAG,EAAE,CAAC;MAAE;MACRC,GAAG,EAAE,CAAC,CAAE;IACZ;EACJ,CAAC,CAAC;EAEF,OAAO/C,EAAE;AAEb,CAAE,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}