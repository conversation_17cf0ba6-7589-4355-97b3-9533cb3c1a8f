{"ast": null, "code": "//! moment.js locale configuration\n//! locale : Faroese [fo]\n//! author : <PERSON><PERSON><PERSON> : https://github.com/ragnar123\n//! author : <PERSON><PERSON> : https://github.com/sa<PERSON><PERSON>\n\n;\n(function (global, factory) {\n  typeof exports === 'object' && typeof module !== 'undefined' && typeof require === 'function' ? factory(require('../moment')) : typeof define === 'function' && define.amd ? define(['../moment'], factory) : factory(global.moment);\n})(this, function (moment) {\n  'use strict';\n\n  //! moment.js locale configuration\n  var fo = moment.defineLocale('fo', {\n    months: 'januar_februar_mars_apríl_mai_juni_juli_august_september_oktober_november_desember'.split('_'),\n    monthsShort: 'jan_feb_mar_apr_mai_jun_jul_aug_sep_okt_nov_des'.split('_'),\n    weekdays: 'sunnudagur_mánadagur_týsdagur_mikudagur_hósdagur_fríggjadagur_leygardagur'.split('_'),\n    weekdaysShort: 'sun_mán_týs_mik_hós_frí_ley'.split('_'),\n    weekdaysMin: 'su_má_tý_mi_hó_fr_le'.split('_'),\n    longDateFormat: {\n      LT: 'HH:mm',\n      LTS: 'HH:mm:ss',\n      L: 'DD/MM/YYYY',\n      LL: 'D MMMM YYYY',\n      LLL: 'D MMMM YYYY HH:mm',\n      LLLL: 'dddd D. MMMM, YYYY HH:mm'\n    },\n    calendar: {\n      sameDay: '[Í dag kl.] LT',\n      nextDay: '[Í morgin kl.] LT',\n      nextWeek: 'dddd [kl.] LT',\n      lastDay: '[Í gjár kl.] LT',\n      lastWeek: '[síðstu] dddd [kl] LT',\n      sameElse: 'L'\n    },\n    relativeTime: {\n      future: 'um %s',\n      past: '%s síðani',\n      s: 'fá sekund',\n      ss: '%d sekundir',\n      m: 'ein minuttur',\n      mm: '%d minuttir',\n      h: 'ein tími',\n      hh: '%d tímar',\n      d: 'ein dagur',\n      dd: '%d dagar',\n      M: 'ein mánaður',\n      MM: '%d mánaðir',\n      y: 'eitt ár',\n      yy: '%d ár'\n    },\n    dayOfMonthOrdinalParse: /\\d{1,2}\\./,\n    ordinal: '%d.',\n    week: {\n      dow: 1,\n      // Monday is the first day of the week.\n      doy: 4 // The week that contains Jan 4th is the first week of the year.\n    }\n  });\n  return fo;\n});", "map": {"version": 3, "names": ["global", "factory", "exports", "module", "require", "define", "amd", "moment", "fo", "defineLocale", "months", "split", "monthsShort", "weekdays", "weekdaysShort", "weekdaysMin", "longDateFormat", "LT", "LTS", "L", "LL", "LLL", "LLLL", "calendar", "sameDay", "nextDay", "nextWeek", "lastDay", "lastWeek", "same<PERSON><PERSON><PERSON>", "relativeTime", "future", "past", "s", "ss", "m", "mm", "h", "hh", "d", "dd", "M", "MM", "y", "yy", "dayOfMonthOrdinalParse", "ordinal", "week", "dow", "doy"], "sources": ["C:/Users/<USER>/Documents/salechange-product/adminFront/node_modules/moment/locale/fo.js"], "sourcesContent": ["//! moment.js locale configuration\n//! locale : Faroese [fo]\n//! author : <PERSON><PERSON><PERSON> : https://github.com/ragnar123\n//! author : <PERSON><PERSON> : https://github.com/sa<PERSON><PERSON>\n\n;(function (global, factory) {\n   typeof exports === 'object' && typeof module !== 'undefined'\n       && typeof require === 'function' ? factory(require('../moment')) :\n   typeof define === 'function' && define.amd ? define(['../moment'], factory) :\n   factory(global.moment)\n}(this, (function (moment) { 'use strict';\n\n    //! moment.js locale configuration\n\n    var fo = moment.defineLocale('fo', {\n        months: 'januar_februar_mars_apríl_mai_juni_juli_august_september_oktober_november_desember'.split(\n            '_'\n        ),\n        monthsShort: 'jan_feb_mar_apr_mai_jun_jul_aug_sep_okt_nov_des'.split('_'),\n        weekdays:\n            'sunnudagur_mánadagur_týsdagur_mikudagur_hósdagur_fríggjadagur_leygardagur'.split(\n                '_'\n            ),\n        weekdaysShort: 'sun_mán_týs_mik_hós_frí_ley'.split('_'),\n        weekdaysMin: 'su_má_tý_mi_hó_fr_le'.split('_'),\n        longDateFormat: {\n            LT: 'HH:mm',\n            LTS: 'HH:mm:ss',\n            L: 'DD/MM/YYYY',\n            LL: 'D MMMM YYYY',\n            LLL: 'D MMMM YYYY HH:mm',\n            LLLL: 'dddd D. MMMM, YYYY HH:mm',\n        },\n        calendar: {\n            sameDay: '[Í dag kl.] LT',\n            nextDay: '[Í morgin kl.] LT',\n            nextWeek: 'dddd [kl.] LT',\n            lastDay: '[Í gjár kl.] LT',\n            lastWeek: '[síðstu] dddd [kl] LT',\n            sameElse: 'L',\n        },\n        relativeTime: {\n            future: 'um %s',\n            past: '%s síðani',\n            s: 'fá sekund',\n            ss: '%d sekundir',\n            m: 'ein minuttur',\n            mm: '%d minuttir',\n            h: 'ein tími',\n            hh: '%d tímar',\n            d: 'ein dagur',\n            dd: '%d dagar',\n            M: 'ein mánaður',\n            MM: '%d mánaðir',\n            y: 'eitt ár',\n            yy: '%d ár',\n        },\n        dayOfMonthOrdinalParse: /\\d{1,2}\\./,\n        ordinal: '%d.',\n        week: {\n            dow: 1, // Monday is the first day of the week.\n            doy: 4, // The week that contains Jan 4th is the first week of the year.\n        },\n    });\n\n    return fo;\n\n})));\n"], "mappings": "AAAA;AACA;AACA;AACA;;AAEA;AAAE,WAAUA,MAAM,EAAEC,OAAO,EAAE;EAC1B,OAAOC,OAAO,KAAK,QAAQ,IAAI,OAAOC,MAAM,KAAK,WAAW,IACrD,OAAOC,OAAO,KAAK,UAAU,GAAGH,OAAO,CAACG,OAAO,CAAC,WAAW,CAAC,CAAC,GACpE,OAAOC,MAAM,KAAK,UAAU,IAAIA,MAAM,CAACC,GAAG,GAAGD,MAAM,CAAC,CAAC,WAAW,CAAC,EAAEJ,OAAO,CAAC,GAC3EA,OAAO,CAACD,MAAM,CAACO,MAAM,CAAC;AACzB,CAAC,EAAC,IAAI,EAAG,UAAUA,MAAM,EAAE;EAAE,YAAY;;EAErC;EAEA,IAAIC,EAAE,GAAGD,MAAM,CAACE,YAAY,CAAC,IAAI,EAAE;IAC/BC,MAAM,EAAE,oFAAoF,CAACC,KAAK,CAC9F,GACJ,CAAC;IACDC,WAAW,EAAE,iDAAiD,CAACD,KAAK,CAAC,GAAG,CAAC;IACzEE,QAAQ,EACJ,2EAA2E,CAACF,KAAK,CAC7E,GACJ,CAAC;IACLG,aAAa,EAAE,6BAA6B,CAACH,KAAK,CAAC,GAAG,CAAC;IACvDI,WAAW,EAAE,sBAAsB,CAACJ,KAAK,CAAC,GAAG,CAAC;IAC9CK,cAAc,EAAE;MACZC,EAAE,EAAE,OAAO;MACXC,GAAG,EAAE,UAAU;MACfC,CAAC,EAAE,YAAY;MACfC,EAAE,EAAE,aAAa;MACjBC,GAAG,EAAE,mBAAmB;MACxBC,IAAI,EAAE;IACV,CAAC;IACDC,QAAQ,EAAE;MACNC,OAAO,EAAE,gBAAgB;MACzBC,OAAO,EAAE,mBAAmB;MAC5BC,QAAQ,EAAE,eAAe;MACzBC,OAAO,EAAE,iBAAiB;MAC1BC,QAAQ,EAAE,uBAAuB;MACjCC,QAAQ,EAAE;IACd,CAAC;IACDC,YAAY,EAAE;MACVC,MAAM,EAAE,OAAO;MACfC,IAAI,EAAE,WAAW;MACjBC,CAAC,EAAE,WAAW;MACdC,EAAE,EAAE,aAAa;MACjBC,CAAC,EAAE,cAAc;MACjBC,EAAE,EAAE,aAAa;MACjBC,CAAC,EAAE,UAAU;MACbC,EAAE,EAAE,UAAU;MACdC,CAAC,EAAE,WAAW;MACdC,EAAE,EAAE,UAAU;MACdC,CAAC,EAAE,aAAa;MAChBC,EAAE,EAAE,YAAY;MAChBC,CAAC,EAAE,SAAS;MACZC,EAAE,EAAE;IACR,CAAC;IACDC,sBAAsB,EAAE,WAAW;IACnCC,OAAO,EAAE,KAAK;IACdC,IAAI,EAAE;MACFC,GAAG,EAAE,CAAC;MAAE;MACRC,GAAG,EAAE,CAAC,CAAE;IACZ;EACJ,CAAC,CAAC;EAEF,OAAOzC,EAAE;AAEb,CAAE,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}