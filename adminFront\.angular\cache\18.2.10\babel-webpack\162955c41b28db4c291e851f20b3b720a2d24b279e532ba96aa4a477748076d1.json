{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { NbCheckboxModule } from '@nebular/theme';\nimport { tap } from 'rxjs';\nimport { SharedModule } from 'src/app/pages/components/shared.module';\nimport { BaseComponent } from 'src/app/pages/components/base/baseComponent';\nimport { EEvent } from 'src/app/shared/services/event.service';\nimport { Base64ImagePipe } from \"../../../../@theme/pipes/base64-image.pipe\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/shared/helper/allowHelper\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"src/app/shared/services/message.service\";\nimport * as i4 from \"src/services/api/services\";\nimport * as i5 from \"src/app/shared/services/utility.service\";\nimport * as i6 from \"src/app/shared/helper/validationHelper\";\nimport * as i7 from \"@angular/common\";\nimport * as i8 from \"src/app/shared/services/event.service\";\nimport * as i9 from \"@angular/forms\";\nimport * as i10 from \"@nebular/theme\";\nimport * as i11 from \"../../../components/breadcrumb/breadcrumb.component\";\nimport * as i12 from \"../../../../@theme/pipes/base-file.pipe\";\nfunction DetailContentManagementSalesAccountComponent_ng_container_8_div_8_img_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 53);\n    i0.ɵɵpipe(1, \"base64Image\");\n  }\n  if (rf & 2) {\n    const formItemReq_r2 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"src\", i0.ɵɵpipeBind1(1, 1, ctx_r2.getCurrentImage(formItemReq_r2)), i0.ɵɵsanitizeUrl);\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_8_div_8_button_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 54);\n    i0.ɵɵlistener(\"click\", function DetailContentManagementSalesAccountComponent_ng_container_8_div_8_button_3_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const formItemReq_r2 = i0.ɵɵnextContext(2).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.prevImage(formItemReq_r2));\n    });\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(1, \"svg\", 55);\n    i0.ɵɵelement(2, \"path\", 56);\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_8_div_8_button_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 57);\n    i0.ɵɵlistener(\"click\", function DetailContentManagementSalesAccountComponent_ng_container_8_div_8_button_4_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const formItemReq_r2 = i0.ɵɵnextContext(2).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.nextImage(formItemReq_r2));\n    });\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(1, \"svg\", 55);\n    i0.ɵɵelement(2, \"path\", 58);\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_8_div_8_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 59);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const formItemReq_r2 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate2(\" \", (formItemReq_r2.currentImageIndex || 0) + 1, \" / \", formItemReq_r2.CMatrialUrl.length, \" \");\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_8_div_8_div_6_button_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 62);\n    i0.ɵɵlistener(\"click\", function DetailContentManagementSalesAccountComponent_ng_container_8_div_8_div_6_button_1_Template_button_click_0_listener() {\n      const i_r7 = i0.ɵɵrestoreView(_r6).index;\n      const formItemReq_r2 = i0.ɵɵnextContext(3).$implicit;\n      return i0.ɵɵresetView(formItemReq_r2.currentImageIndex = i_r7);\n    });\n    i0.ɵɵelement(1, \"img\", 63);\n    i0.ɵɵpipe(2, \"base64Image\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const imageUrl_r8 = ctx.$implicit;\n    const i_r7 = ctx.index;\n    const formItemReq_r2 = i0.ɵɵnextContext(3).$implicit;\n    i0.ɵɵclassProp(\"border-blue-500\", i_r7 === (formItemReq_r2.currentImageIndex || 0))(\"border-gray-300\", i_r7 !== (formItemReq_r2.currentImageIndex || 0));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"src\", i0.ɵɵpipeBind1(2, 5, imageUrl_r8), i0.ɵɵsanitizeUrl);\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_8_div_8_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 60);\n    i0.ɵɵtemplate(1, DetailContentManagementSalesAccountComponent_ng_container_8_div_8_div_6_button_1_Template, 3, 7, \"button\", 61);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const formItemReq_r2 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", formItemReq_r2.CMatrialUrl);\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_8_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 46)(1, \"div\", 47);\n    i0.ɵɵtemplate(2, DetailContentManagementSalesAccountComponent_ng_container_8_div_8_img_2_Template, 2, 3, \"img\", 48)(3, DetailContentManagementSalesAccountComponent_ng_container_8_div_8_button_3_Template, 3, 0, \"button\", 49)(4, DetailContentManagementSalesAccountComponent_ng_container_8_div_8_button_4_Template, 3, 0, \"button\", 50)(5, DetailContentManagementSalesAccountComponent_ng_container_8_div_8_div_5_Template, 2, 2, \"div\", 51);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(6, DetailContentManagementSalesAccountComponent_ng_container_8_div_8_div_6_Template, 2, 1, \"div\", 52);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const formItemReq_r2 = i0.ɵɵnextContext().$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.getCurrentImage(formItemReq_r2));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", formItemReq_r2.CMatrialUrl.length > 1);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", formItemReq_r2.CMatrialUrl.length > 1);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", formItemReq_r2.CMatrialUrl.length > 1);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", formItemReq_r2.CMatrialUrl.length > 1);\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_8_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 64);\n    i0.ɵɵtext(1, \" \\u7121\\u4E3B\\u8981\\u6750\\u6599\\u793A\\u610F \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_8_nb_option_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 65);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const case_r9 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", case_r9);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", case_r9.label, \" \");\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_8_div_34_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 68);\n    i0.ɵɵelement(1, \"img\", 69);\n    i0.ɵɵelementStart(2, \"input\", 70);\n    i0.ɵɵlistener(\"blur\", function DetailContentManagementSalesAccountComponent_ng_container_8_div_34_div_3_Template_input_blur_2_listener($event) {\n      const i_r12 = i0.ɵɵrestoreView(_r11).index;\n      const formItemReq_r2 = i0.ɵɵnextContext(2).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.renameFile($event, i_r12, formItemReq_r2));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"button\", 71);\n    i0.ɵɵlistener(\"click\", function DetailContentManagementSalesAccountComponent_ng_container_8_div_34_div_3_Template_button_click_3_listener() {\n      const picture_r13 = i0.ɵɵrestoreView(_r11).$implicit;\n      const formItemReq_r2 = i0.ɵɵnextContext(2).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.removeImage(picture_r13.id, formItemReq_r2));\n    });\n    i0.ɵɵtext(4, \"\\u5220\\u9664\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    let tmp_11_0;\n    let tmp_12_0;\n    const picture_r13 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"src\", picture_r13.data, i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"value\", picture_r13.name)(\"disabled\", (tmp_11_0 = ctx_r2.listFormItem.CIsLock) !== null && tmp_11_0 !== undefined ? tmp_11_0 : false);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", (tmp_12_0 = ctx_r2.listFormItem.CIsLock) !== null && tmp_12_0 !== undefined ? tmp_12_0 : false);\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_8_div_34_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 66)(1, \"label\", 17);\n    i0.ɵɵtext(2, \"\\u5DF2\\u4E0A\\u50B3\\u6982\\u5FF5\\u5716\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, DetailContentManagementSalesAccountComponent_ng_container_8_div_34_div_3_Template, 5, 4, \"div\", 67);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const formItemReq_r2 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", formItemReq_r2.listPictures);\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_8_div_35_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 72)(1, \"label\", 17);\n    i0.ɵɵtext(2, \"\\u9810\\u8A2D\\u6982\\u5FF5\\u5716\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(3, \"img\", 73);\n    i0.ɵɵpipe(4, \"addBaseFile\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const formItemReq_r2 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"src\", i0.ɵɵpipeBind1(4, 1, formItemReq_r2.CDesignFileUrl), i0.ɵɵsanitizeUrl);\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_8_div_36_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 74);\n    i0.ɵɵtext(1, \" \\u7121\\u6982\\u5FF5\\u8A2D\\u8A08\\u5716 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_8_label_42_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r14 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"label\", 75)(1, \"nb-checkbox\", 76);\n    i0.ɵɵtwoWayListener(\"checkedChange\", function DetailContentManagementSalesAccountComponent_ng_container_8_label_42_Template_nb_checkbox_checkedChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r14);\n      const formItemReq_r2 = i0.ɵɵnextContext().$implicit;\n      i0.ɵɵtwoWayBindingSet(formItemReq_r2.allSelected, $event) || (formItemReq_r2.allSelected = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"checkedChange\", function DetailContentManagementSalesAccountComponent_ng_container_8_label_42_Template_nb_checkbox_checkedChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r14);\n      const formItemReq_r2 = i0.ɵɵnextContext().$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onCheckAllChange($event, formItemReq_r2));\n    });\n    i0.ɵɵtext(2, \" \\u5168\\u9078 \");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const formItemReq_r2 = i0.ɵɵnextContext().$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"checked\", formItemReq_r2.allSelected);\n    i0.ɵɵproperty(\"disabled\", ctx_r2.listFormItem.CIsLock);\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_8_ng_container_43_label_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r15 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"label\", 75)(1, \"nb-checkbox\", 78);\n    i0.ɵɵtwoWayListener(\"checkedChange\", function DetailContentManagementSalesAccountComponent_ng_container_8_ng_container_43_label_1_Template_nb_checkbox_checkedChange_1_listener($event) {\n      const item_r16 = i0.ɵɵrestoreView(_r15).$implicit;\n      const formItemReq_r2 = i0.ɵɵnextContext(2).$implicit;\n      i0.ɵɵtwoWayBindingSet(formItemReq_r2.selectedItems[item_r16], $event) || (formItemReq_r2.selectedItems[item_r16] = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"checkedChange\", function DetailContentManagementSalesAccountComponent_ng_container_8_ng_container_43_label_1_Template_nb_checkbox_checkedChange_1_listener($event) {\n      const item_r16 = i0.ɵɵrestoreView(_r15).$implicit;\n      const formItemReq_r2 = i0.ɵɵnextContext(2).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onCheckboxHouseHoldListChange($event, item_r16, formItemReq_r2));\n    });\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const item_r16 = ctx.$implicit;\n    const formItemReq_r2 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"checked\", formItemReq_r2.selectedItems[item_r16]);\n    i0.ɵɵproperty(\"disabled\", ctx_r2.listFormItem.CIsLock);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", item_r16, \" \");\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_8_ng_container_43_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, DetailContentManagementSalesAccountComponent_ng_container_8_ng_container_43_label_1_Template, 3, 3, \"label\", 77);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.houseHoldList);\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_8_ng_template_44_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 79);\n    i0.ɵɵtext(1, \"\\u5C1A\\u7121\\u6236\\u5225\\u8CC7\\u6599\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_8_div_46_ng_container_4_label_1_nb_checkbox_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r17 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-checkbox\", 78);\n    i0.ɵɵtwoWayListener(\"checkedChange\", function DetailContentManagementSalesAccountComponent_ng_container_8_div_46_ng_container_4_label_1_nb_checkbox_1_Template_nb_checkbox_checkedChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r17);\n      const remark_r18 = i0.ɵɵnextContext().$implicit;\n      const formItemReq_r2 = i0.ɵɵnextContext(3).$implicit;\n      i0.ɵɵtwoWayBindingSet(formItemReq_r2.selectedRemarkType[remark_r18], $event) || (formItemReq_r2.selectedRemarkType[remark_r18] = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"checkedChange\", function DetailContentManagementSalesAccountComponent_ng_container_8_div_46_ng_container_4_label_1_nb_checkbox_1_Template_nb_checkbox_checkedChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r17);\n      const remark_r18 = i0.ɵɵnextContext().$implicit;\n      const formItemReq_r2 = i0.ɵɵnextContext(3).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onCheckboxRemarkChange($event, remark_r18, formItemReq_r2));\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const remark_r18 = i0.ɵɵnextContext().$implicit;\n    const formItemReq_r2 = i0.ɵɵnextContext(3).$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵtwoWayProperty(\"checked\", formItemReq_r2.selectedRemarkType[remark_r18]);\n    i0.ɵɵproperty(\"disabled\", ctx_r2.listFormItem.CIsLock);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", remark_r18, \" \");\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_8_div_46_ng_container_4_label_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"label\", 75);\n    i0.ɵɵtemplate(1, DetailContentManagementSalesAccountComponent_ng_container_8_div_46_ng_container_4_label_1_nb_checkbox_1_Template, 2, 3, \"nb-checkbox\", 81);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const formItemReq_r2 = i0.ɵɵnextContext(3).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", formItemReq_r2.selectedRemarkType);\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_8_div_46_ng_container_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, DetailContentManagementSalesAccountComponent_ng_container_8_div_46_ng_container_4_label_1_Template, 2, 1, \"label\", 77);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.CRemarkTypeOptions);\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_8_div_46_ng_template_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 79);\n    i0.ɵɵtext(1, \"\\u5C1A\\u7121\\u5099\\u8A3B\\u9078\\u9805\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_8_div_46_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 80)(1, \"div\", 41);\n    i0.ɵɵtext(2, \"\\u5099\\u8A3B\\u9078\\u9805\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 42);\n    i0.ɵɵtemplate(4, DetailContentManagementSalesAccountComponent_ng_container_8_div_46_ng_container_4_Template, 2, 1, \"ng-container\", 44)(5, DetailContentManagementSalesAccountComponent_ng_container_8_div_46_ng_template_5_Template, 2, 0, \"ng-template\", null, 2, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const noRemarkOptions_r19 = i0.ɵɵreference(6);\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.CRemarkTypeOptions && ctx_r2.CRemarkTypeOptions.length > 0)(\"ngIfElse\", noRemarkOptions_r19);\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 12)(2, \"div\", 13)(3, \"div\", 14)(4, \"div\", 15)(5, \"div\", 16)(6, \"label\", 17);\n    i0.ɵɵtext(7, \"\\u4E3B\\u8981\\u6750\\u6599\\u793A\\u610F\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(8, DetailContentManagementSalesAccountComponent_ng_container_8_div_8_Template, 7, 5, \"div\", 18)(9, DetailContentManagementSalesAccountComponent_ng_container_8_div_9_Template, 2, 0, \"div\", 19);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"div\", 20)(11, \"div\", 21)(12, \"label\", 22);\n    i0.ɵɵtext(13, \"\\u9805\\u76EE\\u540D\\u7A31\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"div\", 23)(15, \"span\", 24);\n    i0.ɵɵtext(16);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"input\", 25);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function DetailContentManagementSalesAccountComponent_ng_container_8_Template_input_ngModelChange_17_listener($event) {\n      const formItemReq_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      i0.ɵɵtwoWayBindingSet(formItemReq_r2.CItemName, $event) || (formItemReq_r2.CItemName = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(18, \"div\", 21)(19, \"label\", 26);\n    i0.ɵɵtext(20, \"\\u5FC5\\u586B\\u6578\\u91CF\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"input\", 27);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function DetailContentManagementSalesAccountComponent_ng_container_8_Template_input_ngModelChange_21_listener($event) {\n      const formItemReq_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      i0.ɵɵtwoWayBindingSet(formItemReq_r2.CRequireAnswer, $event) || (formItemReq_r2.CRequireAnswer = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(22, \"div\", 28)(23, \"label\", 26);\n    i0.ɵɵtext(24, \"\\u524D\\u53F0UI\\u985E\\u578B\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(25, \"nb-select\", 29);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function DetailContentManagementSalesAccountComponent_ng_container_8_Template_nb_select_ngModelChange_25_listener($event) {\n      const formItemReq_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      i0.ɵɵtwoWayBindingSet(formItemReq_r2.selectedCUiType, $event) || (formItemReq_r2.selectedCUiType = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"selectedChange\", function DetailContentManagementSalesAccountComponent_ng_container_8_Template_nb_select_selectedChange_25_listener() {\n      const formItemReq_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.changeSelectCUiType(formItemReq_r2));\n    });\n    i0.ɵɵtemplate(26, DetailContentManagementSalesAccountComponent_ng_container_8_nb_option_26_Template, 2, 2, \"nb-option\", 30);\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(27, \"div\", 31)(28, \"div\", 32)(29, \"div\", 33)(30, \"button\", 34);\n    i0.ɵɵlistener(\"click\", function DetailContentManagementSalesAccountComponent_ng_container_8_Template_button_click_30_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const inputFile_r10 = i0.ɵɵreference(33);\n      return i0.ɵɵresetView(inputFile_r10.click());\n    });\n    i0.ɵɵtext(31, \"\\u4E0A\\u50B3\\u6982\\u5FF5\\u8A2D\\u8A08\\u5716\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(32, \"input\", 35, 0);\n    i0.ɵɵlistener(\"change\", function DetailContentManagementSalesAccountComponent_ng_container_8_Template_input_change_32_listener($event) {\n      const formItemReq_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.detectFiles($event, formItemReq_r2));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(34, DetailContentManagementSalesAccountComponent_ng_container_8_div_34_Template, 4, 1, \"div\", 36)(35, DetailContentManagementSalesAccountComponent_ng_container_8_div_35_Template, 5, 3, \"div\", 37)(36, DetailContentManagementSalesAccountComponent_ng_container_8_div_36_Template, 2, 0, \"div\", 38);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelement(37, \"hr\", 39);\n    i0.ɵɵelementStart(38, \"div\", 40)(39, \"div\", 41);\n    i0.ɵɵtext(40, \"\\u9069\\u7528\\u6236\\u5225\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(41, \"div\", 42);\n    i0.ɵɵtemplate(42, DetailContentManagementSalesAccountComponent_ng_container_8_label_42_Template, 3, 2, \"label\", 43)(43, DetailContentManagementSalesAccountComponent_ng_container_8_ng_container_43_Template, 2, 1, \"ng-container\", 44)(44, DetailContentManagementSalesAccountComponent_ng_container_8_ng_template_44_Template, 2, 0, \"ng-template\", null, 1, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(46, DetailContentManagementSalesAccountComponent_ng_container_8_div_46_Template, 7, 2, \"div\", 45);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    let tmp_11_0;\n    let tmp_15_0;\n    let tmp_19_0;\n    const formItemReq_r2 = ctx.$implicit;\n    const idx_r20 = ctx.index;\n    const noHouseholds_r21 = i0.ɵɵreference(45);\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"ngIf\", formItemReq_r2.CMatrialUrl && formItemReq_r2.CMatrialUrl.length > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !formItemReq_r2.CMatrialUrl || formItemReq_r2.CMatrialUrl.length === 0);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"for\", \"CItemName_\" + idx_r20);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate3(\"\", formItemReq_r2.CName, \"-\", formItemReq_r2.CPart, \"-\", formItemReq_r2.CLocation, \":\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"id\", \"CItemName_\" + idx_r20);\n    i0.ɵɵtwoWayProperty(\"ngModel\", formItemReq_r2.CItemName);\n    i0.ɵɵproperty(\"disabled\", (tmp_11_0 = ctx_r2.listFormItem.CIsLock) !== null && tmp_11_0 !== undefined ? tmp_11_0 : false);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"for\", \"cRequireAnswer_\" + idx_r20);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"id\", \"cRequireAnswer_\" + idx_r20);\n    i0.ɵɵtwoWayProperty(\"ngModel\", formItemReq_r2.CRequireAnswer);\n    i0.ɵɵproperty(\"disabled\", formItemReq_r2.selectedCUiType.value === 3 || ((tmp_15_0 = ctx_r2.listFormItem.CIsLock) !== null && tmp_15_0 !== undefined ? tmp_15_0 : false));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"for\", \"uiType_\" + idx_r20);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"id\", \"uiType_\" + idx_r20);\n    i0.ɵɵtwoWayProperty(\"ngModel\", formItemReq_r2.selectedCUiType);\n    i0.ɵɵproperty(\"disabled\", (tmp_19_0 = ctx_r2.listFormItem.CIsLock) !== null && tmp_19_0 !== undefined ? tmp_19_0 : false);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.CUiTypeOptions);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"disabled\", ctx_r2.listFormItem.CIsLock);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", formItemReq_r2.listPictures && formItemReq_r2.listPictures.length > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", formItemReq_r2.CDesignFileUrl && (!formItemReq_r2.listPictures || formItemReq_r2.listPictures.length === 0));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !formItemReq_r2.CDesignFileUrl && (!formItemReq_r2.listPictures || formItemReq_r2.listPictures.length === 0));\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.houseHoldList.length);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.houseHoldList.length > 0)(\"ngIfElse\", noHouseholds_r21);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", formItemReq_r2.selectedCUiType.value === 3);\n  }\n}\nexport class DetailContentManagementSalesAccountComponent extends BaseComponent {\n  constructor(_allow, route, message, _formItemService, _regularNoticeFileService, _utilityService, valid, location, _materialService, _eventService) {\n    super(_allow);\n    this._allow = _allow;\n    this.route = route;\n    this.message = message;\n    this._formItemService = _formItemService;\n    this._regularNoticeFileService = _regularNoticeFileService;\n    this._utilityService = _utilityService;\n    this.valid = valid;\n    this.location = location;\n    this._materialService = _materialService;\n    this._eventService = _eventService;\n    this.typeContentManagementSalesAccount = {\n      CFormType: 2,\n      CNoticeType: 2\n    };\n    this.CUiTypeOptions = [{\n      value: 1,\n      label: '建材選色'\n    }, {\n      value: 2,\n      label: '群組選樣_選色'\n    }, {\n      value: 3,\n      label: '建材選樣'\n    }];\n    this.CRemarkTypeOptions = [\"正常\", \"留料\"];\n    this.selectedItems = {};\n    this.selectedRemarkType = {};\n    this.isNew = true;\n  }\n  ngOnInit() {\n    this.route.paramMap.subscribe(params => {\n      if (params) {\n        const idParam = params.get('id');\n        const id = idParam ? +idParam : 0;\n        this.buildCaseId = id;\n        if (this.buildCaseId) {\n          this.getListRegularNoticeFileHouseHold();\n        }\n      }\n    });\n  }\n  getItemByValue(value, options) {\n    for (const item of options) {\n      if (item.value === value) {\n        return item;\n      }\n    }\n    return null;\n  }\n  detectFiles(event, formItemReq_) {\n    const file = event.target.files[0];\n    if (file) {\n      let reader = new FileReader();\n      reader.readAsDataURL(file);\n      reader.onload = () => {\n        let base64Str = reader.result;\n        if (!base64Str) {\n          return;\n        }\n        if (formItemReq_.listPictures.length > 0) {\n          formItemReq_.listPictures[0] = {\n            id: new Date().getTime(),\n            name: file.name.split('.')[0],\n            data: base64Str,\n            extension: this._utilityService.getFileExtension(file.name),\n            CFile: file\n          };\n        } else {\n          formItemReq_.listPictures.push({\n            id: new Date().getTime(),\n            name: file.name.split('.')[0],\n            data: base64Str,\n            extension: this._utilityService.getFileExtension(file.name),\n            CFile: file\n          });\n        }\n        event.target.value = null;\n      };\n    }\n  }\n  removeImage(pictureId, formItemReq_) {\n    if (formItemReq_.listPictures.length) {\n      formItemReq_.listPictures = formItemReq_.listPictures.filter(x => x.id != pictureId);\n    }\n  }\n  renameFile(event, index, formItemReq_) {\n    var blob = formItemReq_.listPictures[index].CFile.slice(0, formItemReq_.listPictures[index].CFile.size, formItemReq_.listPictures[index].CFile.type);\n    var newFile = new File([blob], `${event.target.value + '.' + formItemReq_.listPictures[index].extension}`, {\n      type: formItemReq_.listPictures[index].CFile.type\n    });\n    formItemReq_.listPictures[index].CFile = newFile;\n  }\n  // 輪播功能方法\n  nextImage(formItemReq) {\n    if (formItemReq.CMatrialUrl && formItemReq.CMatrialUrl.length > 0) {\n      formItemReq.currentImageIndex = (formItemReq.currentImageIndex + 1) % formItemReq.CMatrialUrl.length;\n    }\n  }\n  prevImage(formItemReq) {\n    if (formItemReq.CMatrialUrl && formItemReq.CMatrialUrl.length > 0) {\n      formItemReq.currentImageIndex = formItemReq.currentImageIndex === 0 ? formItemReq.CMatrialUrl.length - 1 : formItemReq.currentImageIndex - 1;\n    }\n  }\n  getCurrentImage(formItemReq) {\n    if (formItemReq.CMatrialUrl && formItemReq.CMatrialUrl.length > 0 && formItemReq.currentImageIndex !== undefined) {\n      return formItemReq.CMatrialUrl[formItemReq.currentImageIndex];\n    }\n    return null;\n  }\n  // 放大功能方法\n  openImageModal(formItemReq, imageIndex) {\n    if (formItemReq.CMatrialUrl && formItemReq.CMatrialUrl.length > 0) {\n      if (imageIndex !== undefined) {\n        formItemReq.currentImageIndex = imageIndex;\n      }\n      formItemReq.isModalOpen = true;\n      // 防止背景滾動\n      document.body.style.overflow = 'hidden';\n    }\n  }\n  closeImageModal(formItemReq) {\n    formItemReq.isModalOpen = false;\n    // 恢復背景滾動\n    document.body.style.overflow = 'auto';\n  }\n  // 模態窗口中的輪播方法\n  nextImageModal(formItemReq) {\n    this.nextImage(formItemReq);\n  }\n  prevImageModal(formItemReq) {\n    this.prevImage(formItemReq);\n  }\n  // 鍵盤事件處理\n  onKeydown(event, formItemReq) {\n    if (formItemReq.isModalOpen) {\n      switch (event.key) {\n        case 'ArrowLeft':\n          event.preventDefault();\n          this.prevImageModal(formItemReq);\n          break;\n        case 'ArrowRight':\n          event.preventDefault();\n          this.nextImageModal(formItemReq);\n          break;\n        case 'Escape':\n          event.preventDefault();\n          this.closeImageModal(formItemReq);\n          break;\n      }\n    }\n  }\n  onCheckAllChange(checked, formItemReq_) {\n    formItemReq_.allSelected = checked;\n    this.houseHoldList.forEach(item => {\n      formItemReq_.selectedItems[item] = checked;\n    });\n  }\n  onCheckboxHouseHoldListChange(checked, item, formItemReq_) {\n    if (checked) {\n      formItemReq_.selectedItems[item] = checked;\n      formItemReq_.allSelected = this.houseHoldList.every(item => formItemReq_.selectedItems[item] == checked);\n    } else {\n      formItemReq_.allSelected = false;\n    }\n  }\n  onCheckboxRemarkChange(checked, item, formItemReq_) {\n    formItemReq_.selectedRemarkType[item] = checked;\n  }\n  createRemarkObject(CRemarkTypeOptions, CRemarkType) {\n    const remarkObject = {};\n    for (const option of CRemarkTypeOptions) {\n      remarkObject[option] = false;\n    }\n    const remarkTypes = CRemarkType.split('-');\n    for (const type of remarkTypes) {\n      if (CRemarkTypeOptions.includes(type)) {\n        remarkObject[type] = true;\n      }\n    }\n    return remarkObject;\n  }\n  mergeItems(items) {\n    const map = new Map();\n    items.forEach(item => {\n      const key = `${item.CLocation}_${item.CName}_${item.CPart}`;\n      if (map.has(key)) {\n        const existing = map.get(key);\n        existing.count += 1;\n      } else {\n        map.set(key, {\n          item: {\n            ...item\n          },\n          count: 1\n        });\n      }\n    });\n    return Array.from(map.values()).map(({\n      item,\n      count\n    }) => ({\n      ...item,\n      CTotalAnswer: count\n    }));\n  }\n  getMaterialList() {\n    this._materialService.apiMaterialGetMaterialListPost$Json({\n      body: {\n        CBuildCaseId: this.buildCaseId,\n        CPagi: false\n      }\n    }).pipe(tap(res => {\n      if (res.Entries && res.StatusCode == 0) {\n        this.houseHoldList.forEach(item => this.selectedItems[item] = false);\n        this.CRemarkTypeOptions.forEach(item => this.selectedRemarkType[item] = false);\n        this.arrListFormItemReq = res.Entries.map(o => {\n          return {\n            CDesignFileUrl: null,\n            CFormItemHouseHold: null,\n            CFormId: null,\n            CLocation: o.CLocation,\n            CName: o.CName,\n            CPart: o.CPart,\n            CItemName: `${o.CName}-${o.CPart}-${o.CLocation}`,\n            CRemarkType: null,\n            CTotalAnswer: 0,\n            CRequireAnswer: 1,\n            CUiType: 0,\n            selectedItems: {},\n            selectedRemarkType: this.selectedRemarkType,\n            allSelected: false,\n            listPictures: [],\n            selectedCUiType: this.CUiTypeOptions[0],\n            CMatrialUrl: o.CPicture ? [o.CPicture] : [],\n            currentImageIndex: 0,\n            isModalOpen: false\n          };\n        });\n        this.arrListFormItemReq = [...this.mergeItems(this.arrListFormItemReq)];\n      }\n    })).subscribe();\n  }\n  getListFormItem() {\n    this._formItemService.apiFormItemGetListFormItemPost$Json({\n      body: {\n        CBuildCaseId: this.buildCaseId,\n        CFormType: this.typeContentManagementSalesAccount.CFormType,\n        CIsPaging: false\n      }\n    }).pipe(tap(res => {\n      if (res.Entries && res.StatusCode == 0) {\n        this.listFormItem = res.Entries;\n        this.isNew = res.Entries.formItems ? false : true;\n        if (res.Entries.formItems) {\n          this.houseHoldList.forEach(item => this.selectedItems[item] = false);\n          this.CRemarkTypeOptions.forEach(item => this.selectedRemarkType[item] = false);\n          this.arrListFormItemReq = res.Entries.formItems.map(o => {\n            return {\n              CFormId: this.listFormItem.CFormId,\n              CDesignFileUrl: o.CDesignFileUrl,\n              CMatrialUrl: o.CMatrialUrl || (o.CFirstMatrialUrl ? [o.CFirstMatrialUrl] : []),\n              CFile: o.CFile,\n              CFormItemHouseHold: o.CFormItemHouseHold,\n              CFormItemId: o.CFormItemId,\n              CLocation: o.CLocation,\n              CName: o.CName,\n              CPart: o.CPart,\n              CItemName: o.CItemName ? o.CItemName : `${o.CName}-${o.CPart}-${o.CLocation}`,\n              CRemarkType: o.CRemarkType,\n              CTotalAnswer: o.CTotalAnswer,\n              CRequireAnswer: o.CUiType === 3 ? 1 : o.CRequireAnswer,\n              CUiType: o.CUiType,\n              selectedItems: o.tblFormItemHouseholds.length ? this.createArrayObjectFromArray(this.houseHoldList, o.tblFormItemHouseholds) : {\n                ...this.selectedItems\n              },\n              selectedRemarkType: o.CRemarkType ? this.createRemarkObject(this.CRemarkTypeOptions, o.CRemarkType) : {\n                ...this.selectedRemarkType\n              },\n              allSelected: o.tblFormItemHouseholds.length === this.houseHoldList.length,\n              listPictures: [],\n              selectedCUiType: o.CUiType ? this.getItemByValue(o.CUiType, this.CUiTypeOptions) : this.CUiTypeOptions[0],\n              currentImageIndex: 0\n            };\n          });\n        } else {\n          this.getMaterialList();\n        }\n      }\n    })).subscribe();\n  }\n  changeSelectCUiType(formItemReq) {\n    if (formItemReq.selectedCUiType && formItemReq.selectedCUiType.value === 3) {\n      formItemReq.CRequireAnswer = 1;\n    }\n  }\n  getHouseHoldListByNoticeType(data) {\n    for (let item of data) {\n      if (item.CNoticeType === this.typeContentManagementSalesAccount.CNoticeType) {\n        return item.CHouseHoldList;\n      }\n    }\n    return [];\n  }\n  getKeysWithTrueValue(obj) {\n    return Object.keys(obj).filter(key => obj[key]);\n  }\n  getKeysWithTrueValueJoined(obj) {\n    return Object.keys(obj).filter(key => obj[key]).join('-');\n  }\n  getCRemarkType(selectedCUiType, selectedRemarkType) {\n    if (selectedCUiType && selectedCUiType.value == 3) {\n      return this.getKeysWithTrueValueJoined(selectedRemarkType);\n    }\n  }\n  getStringAfterComma(inputString) {\n    const parts = inputString.split(',');\n    if (parts.length > 1) {\n      return parts[1];\n    } else return \"\";\n  }\n  formatFile(listPictures) {\n    if (listPictures && listPictures.length > 0) {\n      return {\n        Base64String: this.getStringAfterComma(listPictures[0].data) || null,\n        FileExtension: listPictures[0].extension || null,\n        FileName: listPictures[0].CFile.name || listPictures[0].name || null\n      };\n    } else return undefined;\n  }\n  validation() {\n    this.valid.clear();\n    let hasInvalidCUiType = false;\n    let hasInvalidCRequireAnswer = false;\n    let hasInvalidItemName = false;\n    for (const item of this.saveListFormItemReq) {\n      if (!hasInvalidCUiType && !item.CUiType) {\n        hasInvalidCUiType = true;\n      }\n      if (!hasInvalidCRequireAnswer && !item.CRequireAnswer) {\n        hasInvalidCRequireAnswer = true;\n      }\n      if (item.CTotalAnswer && item.CRequireAnswer) {\n        if (item.CRequireAnswer > item.CTotalAnswer || item.CRequireAnswer < 0) {\n          this.valid.addErrorMessage('[必填數量]' + ' <= ' + item.CTotalAnswer + ` (${item.CItemName}) `);\n        }\n      }\n      if (!hasInvalidItemName && !item.CItemName) {\n        hasInvalidItemName = true;\n      }\n    }\n    if (hasInvalidCUiType) {\n      this.valid.addErrorMessage('[前台UI類型]' + ' 必填');\n    }\n    if (hasInvalidCRequireAnswer) {\n      this.valid.addErrorMessage('[必填數量]' + ' 必填且>0');\n    }\n    if (hasInvalidItemName) {\n      this.valid.addErrorMessage('[廚房-廚具-櫃體]' + ' 必填');\n    }\n  }\n  onSubmit() {\n    this.saveListFormItemReq = this.arrListFormItemReq.map(e => {\n      return {\n        CDesignFileUrl: e.CDesignFileUrl ? e.CDesignFileUrl : null,\n        CFile: e.listPictures ? this.formatFile(e.listPictures) : undefined,\n        CFormItemHouseHold: this.getKeysWithTrueValue(e.selectedItems),\n        CFormItemId: e.CFormItemId ? e.CFormItemId : null,\n        CFormID: this.isNew ? null : this.listFormItem.CFormId,\n        CName: e.CName,\n        CPart: e.CPart,\n        CLocation: e.CLocation,\n        CItemName: e.CItemName,\n        //? e.CItemName : `${e.CName}-${e.CPart}-${e.CLocation}`,\n        CRemarkType: e.selectedCUiType.value !== 3 ? null : this.getCRemarkType(e.selectedCUiType, e.selectedRemarkType) || null,\n        CTotalAnswer: e.CTotalAnswer,\n        CRequireAnswer: e.CRequireAnswer,\n        CUiType: e.selectedCUiType.value\n      };\n    });\n    this.validation();\n    if (this.valid.errorMessages.length > 0) {\n      this.message.showErrorMSGs(this.valid.errorMessages);\n      return;\n    }\n    if (this.isNew) {\n      this.createListFormItem();\n    } else {\n      this.saveListFormItem();\n    }\n  }\n  saveListFormItem() {\n    this._formItemService.apiFormItemSaveListFormItemPost$Json({\n      body: this.saveListFormItemReq\n    }).subscribe(res => {\n      if (res.StatusCode == 0) {\n        this.message.showSucessMSG(\"執行成功\");\n        // this.getListFormItem()\n        this.goBack();\n      }\n    });\n  }\n  createListFormItem() {\n    this.creatListFormItem = {\n      CBuildCaseId: this.buildCaseId,\n      CFormItem: this.saveListFormItemReq || null,\n      CFormType: this.typeContentManagementSalesAccount.CFormType\n    };\n    this._formItemService.apiFormItemCreateListFormItemPost$Json({\n      body: this.creatListFormItem\n    }).subscribe(res => {\n      if (res.StatusCode == 0) {\n        this.message.showSucessMSG(\"執行成功\");\n        // this.getListFormItem()\n        this.goBack();\n      }\n    });\n  }\n  createArrayObjectFromArray(a, b) {\n    const c = {};\n    for (const item of a) {\n      const matchingItem = b.find(bItem => bItem.CHousehold === item && bItem.CIsSelect);\n      c[item] = !!matchingItem;\n    }\n    return c;\n  } //[\"House1\", \"House2\", \"House3\"] => [{CHousehold: \"House1\", CIsSelect: true,... }, ... ]\n  getListRegularNoticeFileHouseHold() {\n    this._regularNoticeFileService.apiRegularNoticeFileGetListRegularNoticeFileHouseHoldPost$Json({\n      body: this.buildCaseId\n    }).pipe(tap(res => {\n      if (res.Entries && res.StatusCode == 0) {\n        this.houseHoldList = this.getHouseHoldListByNoticeType(res.Entries);\n        this.getListFormItem();\n      }\n    })).subscribe();\n  }\n  goBack() {\n    this._eventService.push({\n      action: \"GET_BUILDCASE\" /* EEvent.GET_BUILDCASE */,\n      payload: this.buildCaseId\n    });\n    this.location.back();\n  }\n  static {\n    this.ɵfac = function DetailContentManagementSalesAccountComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || DetailContentManagementSalesAccountComponent)(i0.ɵɵdirectiveInject(i1.AllowHelper), i0.ɵɵdirectiveInject(i2.ActivatedRoute), i0.ɵɵdirectiveInject(i3.MessageService), i0.ɵɵdirectiveInject(i4.FormItemService), i0.ɵɵdirectiveInject(i4.RegularNoticeFileService), i0.ɵɵdirectiveInject(i5.UtilityService), i0.ɵɵdirectiveInject(i6.ValidationHelper), i0.ɵɵdirectiveInject(i7.Location), i0.ɵɵdirectiveInject(i4.MaterialService), i0.ɵɵdirectiveInject(i8.EventService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: DetailContentManagementSalesAccountComponent,\n      selectors: [[\"ngx-detail-content-management-sales-account\"]],\n      standalone: true,\n      features: [i0.ɵɵInheritDefinitionFeature, i0.ɵɵStandaloneFeature],\n      decls: 14,\n      vars: 3,\n      consts: [[\"inputFile\", \"\"], [\"noHouseholds\", \"\"], [\"noRemarkOptions\", \"\"], [\"accent\", \"success\"], [1, \"pb-4\"], [1, \"font-bold\", \"text-[#818181]\", \"mb-4\"], [1, \"space-y-6\"], [1, \"font-bold\", \"text-xl\", \"pb-2\", \"border-b\", \"mb-4\"], [4, \"ngFor\", \"ngForOf\"], [1, \"d-flex\", \"justify-content-end\", \"p-3\", \"sticky\", \"bottom-0\", \"bg-white\", \"border-t\"], [1, \"btn\", \"btn-secondary\", \"mx-2\", 3, \"click\", \"disabled\"], [1, \"btn\", \"btn-primary\", 3, \"click\", \"disabled\"], [1, \"p-4\", \"border\", \"rounded-lg\", \"shadow-sm\", \"bg-white\"], [1, \"flex\", \"flex-wrap\", \"-mx-2\"], [1, \"w-full\", \"md:w-3/4\", \"px-2\"], [1, \"flex\", \"flex-wrap\"], [1, \"w-full\", \"md:w-1/3\", \"pr-0\", \"md:pr-3\", \"mb-4\", \"md:mb-0\"], [1, \"block\", \"text-sm\", \"font-medium\", \"text-gray-700\", \"mb-1\"], [\"class\", \"relative\", 4, \"ngIf\"], [\"class\", \"h-[160px] w-full flex items-center justify-center border rounded bg-gray-50 text-gray-400\", 4, \"ngIf\"], [1, \"w-full\", \"md:w-2/3\", \"md:pl-3\"], [1, \"form-group\", \"flex\", \"items-center\", \"w-full\", \"mb-3\"], [1, \"label\", \"w-1/3\", \"text-base\", \"pr-2\", \"shrink-0\", 3, \"for\"], [1, \"input-group\", \"items-baseline\", \"w-2/3\"], [1, \"text-gray-600\", \"text-sm\", \"mr-1\", \"shrink-0\"], [\"type\", \"text\", \"nbInput\", \"\", \"placeholder\", \"\\u4F8B\\u5982\\uFF1A\\u5EDA\\u623F\\u6AAF\\u9762\", 1, \"w-full\", 3, \"ngModelChange\", \"id\", \"ngModel\", \"disabled\"], [1, \"label\", \"w-1/3\", \"pr-2\", \"shrink-0\", 3, \"for\"], [\"type\", \"number\", \"nbInput\", \"\", \"placeholder\", \"\\u8F38\\u5165\\u6578\\u91CF\", 1, \"w-2/3\", 3, \"ngModelChange\", \"id\", \"ngModel\", \"disabled\"], [1, \"form-group\", \"flex\", \"items-center\", \"w-full\"], [\"placeholder\", \"\\u9078\\u64C7UI\\u985E\\u578B\", 1, \"w-2/3\", 3, \"ngModelChange\", \"selectedChange\", \"id\", \"ngModel\", \"disabled\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [1, \"w-full\", \"md:w-1/4\", \"px-2\", \"mt-4\", \"md:mt-0\"], [1, \"w-full\", \"flex\", \"flex-col\"], [1, \"flex\", \"justify-end\", \"w-full\", \"mb-2\"], [1, \"btn\", \"btn-info\", \"h-fit\", \"w-full\", 3, \"click\", \"disabled\"], [\"type\", \"file\", \"accept\", \"image/png, image/gif, image/jpeg\", 1, \"hidden\", 3, \"change\"], [\"class\", \"mt-2 space-y-3\", 4, \"ngIf\"], [\"class\", \"w-full text-center mt-2\", 4, \"ngIf\"], [\"class\", \"h-32 w-full flex items-center justify-center border rounded bg-gray-50 text-gray-400 mt-2\", 4, \"ngIf\"], [1, \"my-4\"], [1, \"flex\", \"flex-wrap\", \"w-full\", \"items-center\", \"mb-3\"], [1, \"w-full\", \"md:w-1/5\", \"px-2\", \"pb-1\", \"md:pb-0\", \"font-medium\", \"text-gray-700\"], [1, \"w-full\", \"md:w-4/5\", \"px-2\"], [\"class\", \"mr-3 cursor-pointer\", 4, \"ngIf\"], [4, \"ngIf\", \"ngIfElse\"], [\"class\", \"flex flex-wrap w-full items-center\", 4, \"ngIf\"], [1, \"relative\"], [1, \"h-[160px]\", \"w-full\", \"relative\", \"overflow-hidden\", \"rounded\", \"border\"], [\"class\", \"h-full w-full object-cover transition-all duration-300\", 3, \"src\", 4, \"ngIf\"], [\"class\", \"absolute left-2 top-1/2 transform -translate-y-1/2 bg-black bg-opacity-50 text-white rounded-full w-8 h-8 flex items-center justify-center hover:bg-opacity-70 transition-all\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"absolute right-2 top-1/2 transform -translate-y-1/2 bg-black bg-opacity-50 text-white rounded-full w-8 h-8 flex items-center justify-center hover:bg-opacity-70 transition-all\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"absolute bottom-2 right-2 bg-black bg-opacity-50 text-white text-xs px-2 py-1 rounded\", 4, \"ngIf\"], [\"class\", \"flex gap-1 mt-2 overflow-x-auto\", 4, \"ngIf\"], [1, \"h-full\", \"w-full\", \"object-cover\", \"transition-all\", \"duration-300\", 3, \"src\"], [1, \"absolute\", \"left-2\", \"top-1/2\", \"transform\", \"-translate-y-1/2\", \"bg-black\", \"bg-opacity-50\", \"text-white\", \"rounded-full\", \"w-8\", \"h-8\", \"flex\", \"items-center\", \"justify-center\", \"hover:bg-opacity-70\", \"transition-all\", 3, \"click\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-4\", \"h-4\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M15 19l-7-7 7-7\"], [1, \"absolute\", \"right-2\", \"top-1/2\", \"transform\", \"-translate-y-1/2\", \"bg-black\", \"bg-opacity-50\", \"text-white\", \"rounded-full\", \"w-8\", \"h-8\", \"flex\", \"items-center\", \"justify-center\", \"hover:bg-opacity-70\", \"transition-all\", 3, \"click\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M9 5l7 7-7 7\"], [1, \"absolute\", \"bottom-2\", \"right-2\", \"bg-black\", \"bg-opacity-50\", \"text-white\", \"text-xs\", \"px-2\", \"py-1\", \"rounded\"], [1, \"flex\", \"gap-1\", \"mt-2\", \"overflow-x-auto\"], [\"class\", \"flex-shrink-0 w-12 h-12 border-2 rounded overflow-hidden\", 3, \"border-blue-500\", \"border-gray-300\", \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"flex-shrink-0\", \"w-12\", \"h-12\", \"border-2\", \"rounded\", \"overflow-hidden\", 3, \"click\"], [1, \"w-full\", \"h-full\", \"object-cover\", 3, \"src\"], [1, \"h-[160px]\", \"w-full\", \"flex\", \"items-center\", \"justify-center\", \"border\", \"rounded\", \"bg-gray-50\", \"text-gray-400\"], [3, \"value\"], [1, \"mt-2\", \"space-y-3\"], [\"class\", \"border p-2 rounded-md bg-gray-50\", 4, \"ngFor\", \"ngForOf\"], [1, \"border\", \"p-2\", \"rounded-md\", \"bg-gray-50\"], [1, \"h-32\", \"w-full\", \"object-cover\", \"rounded\", \"mb-2\", \"border\", 3, \"src\"], [\"nbInput\", \"\", \"type\", \"text\", \"placeholder\", \"\\u5716\\u7247\\u8AAA\\u660E/\\u6A94\\u540D\", 1, \"w-full\", \"p-1\", \"text-xs\", \"mb-1\", 3, \"blur\", \"value\", \"disabled\"], [1, \"btn\", \"btn-outline-danger\", \"btn-xs\", \"w-full\", 3, \"click\", \"disabled\"], [1, \"w-full\", \"text-center\", \"mt-2\"], [1, \"h-32\", \"w-full\", \"object-cover\", \"rounded\", \"border\", 3, \"src\"], [1, \"h-32\", \"w-full\", \"flex\", \"items-center\", \"justify-center\", \"border\", \"rounded\", \"bg-gray-50\", \"text-gray-400\", \"mt-2\"], [1, \"mr-3\", \"cursor-pointer\"], [3, \"checkedChange\", \"checked\", \"disabled\"], [\"class\", \"mr-3 cursor-pointer\", 4, \"ngFor\", \"ngForOf\"], [\"value\", \"item\", 3, \"checkedChange\", \"checked\", \"disabled\"], [1, \"text-gray-500\", \"text-sm\"], [1, \"flex\", \"flex-wrap\", \"w-full\", \"items-center\"], [\"value\", \"item\", 3, \"checked\", \"disabled\", \"checkedChange\", 4, \"ngIf\"]],\n      template: function DetailContentManagementSalesAccountComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"nb-card\", 3)(1, \"nb-card-header\");\n          i0.ɵɵelement(2, \"ngx-breadcrumb\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(3, \"nb-card-body\", 4);\n          i0.ɵɵelement(4, \"h1\", 5);\n          i0.ɵɵelementStart(5, \"div\", 6)(6, \"h4\", 7);\n          i0.ɵɵtext(7, \"\\u985E\\u578B-\\u7368\\u7ACB\\u9078\\u6A23\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(8, DetailContentManagementSalesAccountComponent_ng_container_8_Template, 47, 26, \"ng-container\", 8);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(9, \"nb-card-footer\", 9)(10, \"button\", 10);\n          i0.ɵɵlistener(\"click\", function DetailContentManagementSalesAccountComponent_Template_button_click_10_listener() {\n            return ctx.goBack();\n          });\n          i0.ɵɵtext(11, \" \\u53D6\\u6D88 \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(12, \"button\", 11);\n          i0.ɵɵlistener(\"click\", function DetailContentManagementSalesAccountComponent_Template_button_click_12_listener() {\n            return ctx.onSubmit();\n          });\n          i0.ɵɵtext(13, \" \\u5132\\u5B58 \");\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          let tmp_1_0;\n          let tmp_2_0;\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"ngForOf\", ctx.arrListFormItemReq);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"disabled\", (tmp_1_0 = ctx.listFormItem.CIsLock) !== null && tmp_1_0 !== undefined ? tmp_1_0 : false);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"disabled\", (tmp_2_0 = ctx.listFormItem.CIsLock) !== null && tmp_2_0 !== undefined ? tmp_2_0 : false);\n        }\n      },\n      dependencies: [CommonModule, i7.NgForOf, i7.NgIf, SharedModule, i9.DefaultValueAccessor, i9.NumberValueAccessor, i9.NgControlStatus, i9.NgModel, i10.NbCardComponent, i10.NbCardBodyComponent, i10.NbCardFooterComponent, i10.NbCardHeaderComponent, i10.NbCheckboxComponent, i10.NbInputDirective, i10.NbSelectComponent, i10.NbOptionComponent, i11.BreadcrumbComponent, i12.BaseFilePipe, NbCheckboxModule, Base64ImagePipe],\n      styles: [\"/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsImZpbGUiOiJkZXRhaWwtY29udGVudC1tYW5hZ2VtZW50LXNhbGVzLWFjY291bnQuY29tcG9uZW50LnNjc3MifQ== */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvcGFnZXMvc2VsZWN0aW9uLW1hbmFnZW1lbnQvY29udGVudC1tYW5hZ2VtZW50LXNhbGVzLWFjY291bnQvZGV0YWlsLWNvbnRlbnQtbWFuYWdlbWVudC1zYWxlcy1hY2NvdW50L2RldGFpbC1jb250ZW50LW1hbmFnZW1lbnQtc2FsZXMtYWNjb3VudC5jb21wb25lbnQuc2NzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiO0FBQ0EsZ05BQWdOIiwic291cmNlUm9vdCI6IiJ9 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "NbCheckboxModule", "tap", "SharedModule", "BaseComponent", "EEvent", "Base64ImagePipe", "i0", "ɵɵelement", "ɵɵproperty", "ɵɵpipeBind1", "ctx_r2", "getCurrentImage", "formItemReq_r2", "ɵɵsanitizeUrl", "ɵɵelementStart", "ɵɵlistener", "DetailContentManagementSalesAccountComponent_ng_container_8_div_8_button_3_Template_button_click_0_listener", "ɵɵrestoreView", "_r4", "ɵɵnextContext", "$implicit", "ɵɵresetView", "prevImage", "ɵɵelementEnd", "DetailContentManagementSalesAccountComponent_ng_container_8_div_8_button_4_Template_button_click_0_listener", "_r5", "nextImage", "ɵɵtext", "ɵɵadvance", "ɵɵtextInterpolate2", "currentImageIndex", "CMatrialUrl", "length", "DetailContentManagementSalesAccountComponent_ng_container_8_div_8_div_6_button_1_Template_button_click_0_listener", "i_r7", "_r6", "index", "ɵɵclassProp", "imageUrl_r8", "ɵɵtemplate", "DetailContentManagementSalesAccountComponent_ng_container_8_div_8_div_6_button_1_Template", "DetailContentManagementSalesAccountComponent_ng_container_8_div_8_img_2_Template", "DetailContentManagementSalesAccountComponent_ng_container_8_div_8_button_3_Template", "DetailContentManagementSalesAccountComponent_ng_container_8_div_8_button_4_Template", "DetailContentManagementSalesAccountComponent_ng_container_8_div_8_div_5_Template", "DetailContentManagementSalesAccountComponent_ng_container_8_div_8_div_6_Template", "case_r9", "ɵɵtextInterpolate1", "label", "DetailContentManagementSalesAccountComponent_ng_container_8_div_34_div_3_Template_input_blur_2_listener", "$event", "i_r12", "_r11", "renameFile", "DetailContentManagementSalesAccountComponent_ng_container_8_div_34_div_3_Template_button_click_3_listener", "picture_r13", "removeImage", "id", "data", "name", "tmp_11_0", "listFormItem", "CIsLock", "undefined", "tmp_12_0", "DetailContentManagementSalesAccountComponent_ng_container_8_div_34_div_3_Template", "listPictures", "CDesignFileUrl", "ɵɵtwoWayListener", "DetailContentManagementSalesAccountComponent_ng_container_8_label_42_Template_nb_checkbox_checkedChange_1_listener", "_r14", "ɵɵtwoWayBindingSet", "allSelected", "onCheckAllChange", "ɵɵtwoWayProperty", "DetailContentManagementSalesAccountComponent_ng_container_8_ng_container_43_label_1_Template_nb_checkbox_checkedChange_1_listener", "item_r16", "_r15", "selectedItems", "onCheckboxHouseHoldListChange", "ɵɵelementContainerStart", "DetailContentManagementSalesAccountComponent_ng_container_8_ng_container_43_label_1_Template", "houseHoldList", "DetailContentManagementSalesAccountComponent_ng_container_8_div_46_ng_container_4_label_1_nb_checkbox_1_Template_nb_checkbox_checkedChange_0_listener", "_r17", "remark_r18", "selectedRemarkType", "onCheckboxRemarkChange", "DetailContentManagementSalesAccountComponent_ng_container_8_div_46_ng_container_4_label_1_nb_checkbox_1_Template", "DetailContentManagementSalesAccountComponent_ng_container_8_div_46_ng_container_4_label_1_Template", "CRemarkTypeOptions", "DetailContentManagementSalesAccountComponent_ng_container_8_div_46_ng_container_4_Template", "DetailContentManagementSalesAccountComponent_ng_container_8_div_46_ng_template_5_Template", "ɵɵtemplateRefExtractor", "noRemarkOptions_r19", "DetailContentManagementSalesAccountComponent_ng_container_8_div_8_Template", "DetailContentManagementSalesAccountComponent_ng_container_8_div_9_Template", "DetailContentManagementSalesAccountComponent_ng_container_8_Template_input_ngModelChange_17_listener", "_r1", "CItemName", "DetailContentManagementSalesAccountComponent_ng_container_8_Template_input_ngModelChange_21_listener", "CRequireAnswer", "DetailContentManagementSalesAccountComponent_ng_container_8_Template_nb_select_ngModelChange_25_listener", "selectedCUiType", "DetailContentManagementSalesAccountComponent_ng_container_8_Template_nb_select_selectedChange_25_listener", "changeSelectCUiType", "DetailContentManagementSalesAccountComponent_ng_container_8_nb_option_26_Template", "DetailContentManagementSalesAccountComponent_ng_container_8_Template_button_click_30_listener", "inputFile_r10", "ɵɵreference", "click", "DetailContentManagementSalesAccountComponent_ng_container_8_Template_input_change_32_listener", "detectFiles", "DetailContentManagementSalesAccountComponent_ng_container_8_div_34_Template", "DetailContentManagementSalesAccountComponent_ng_container_8_div_35_Template", "DetailContentManagementSalesAccountComponent_ng_container_8_div_36_Template", "DetailContentManagementSalesAccountComponent_ng_container_8_label_42_Template", "DetailContentManagementSalesAccountComponent_ng_container_8_ng_container_43_Template", "DetailContentManagementSalesAccountComponent_ng_container_8_ng_template_44_Template", "DetailContentManagementSalesAccountComponent_ng_container_8_div_46_Template", "idx_r20", "ɵɵtextInterpolate3", "CName", "<PERSON>art", "CLocation", "value", "tmp_15_0", "tmp_19_0", "CUiTypeOptions", "noHouseholds_r21", "DetailContentManagementSalesAccountComponent", "constructor", "_allow", "route", "message", "_formItemService", "_regularNoticeFileService", "_utilityService", "valid", "location", "_materialService", "_eventService", "typeContentManagementSalesAccount", "CFormType", "CNoticeType", "isNew", "ngOnInit", "paramMap", "subscribe", "params", "idParam", "get", "buildCaseId", "getListRegularNoticeFileHouseHold", "getItemByValue", "options", "item", "event", "formItemReq_", "file", "target", "files", "reader", "FileReader", "readAsDataURL", "onload", "base64Str", "result", "Date", "getTime", "split", "extension", "getFileExtension", "CFile", "push", "pictureId", "filter", "x", "blob", "slice", "size", "type", "newFile", "File", "formItemReq", "openImageModal", "imageIndex", "isModalOpen", "document", "body", "style", "overflow", "closeImageModal", "nextImageModal", "prevImageModal", "onKeydown", "key", "preventDefault", "checked", "for<PERSON>ach", "every", "createRemarkObject", "CRemarkType", "remarkObject", "option", "remarkTypes", "includes", "mergeItems", "items", "map", "Map", "has", "existing", "count", "set", "Array", "from", "values", "CTotalAnswer", "getMaterialList", "apiMaterialGetMaterialListPost$Json", "CBuildCaseId", "CPagi", "pipe", "res", "Entries", "StatusCode", "arrListFormItemReq", "o", "CFormItemHouseHold", "CFormId", "CUiType", "CPicture", "getListFormItem", "apiFormItemGetListFormItemPost$Json", "CIsPaging", "formItems", "CFirstMatrialUrl", "CFormItemId", "tblFormItemHouseholds", "createArrayObjectFromArray", "getHouseHoldListByNoticeType", "CHouseHoldList", "getKeysWithTrueValue", "obj", "Object", "keys", "getKeysWithTrueValueJoined", "join", "getCRemarkType", "getStringAfterComma", "inputString", "parts", "formatFile", "Base64String", "FileExtension", "FileName", "validation", "clear", "hasInvalidCUiType", "hasInvalidCRequireAnswer", "hasInvalidItemName", "saveListFormItemReq", "addErrorMessage", "onSubmit", "e", "CFormID", "errorMessages", "showErrorMSGs", "createListFormItem", "saveListFormItem", "apiFormItemSaveListFormItemPost$Json", "showSucessMSG", "goBack", "creatListFormItem", "CFormItem", "apiFormItemCreateListFormItemPost$Json", "a", "b", "c", "matchingItem", "find", "bItem", "CHousehold", "CIsSelect", "apiRegularNoticeFileGetListRegularNoticeFileHouseHoldPost$Json", "action", "payload", "back", "ɵɵdirectiveInject", "i1", "AllowHelper", "i2", "ActivatedRoute", "i3", "MessageService", "i4", "FormItemService", "RegularNoticeFileService", "i5", "UtilityService", "i6", "ValidationHelper", "i7", "Location", "MaterialService", "i8", "EventService", "selectors", "standalone", "features", "ɵɵInheritDefinitionFeature", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "DetailContentManagementSalesAccountComponent_Template", "rf", "ctx", "DetailContentManagementSalesAccountComponent_ng_container_8_Template", "DetailContentManagementSalesAccountComponent_Template_button_click_10_listener", "DetailContentManagementSalesAccountComponent_Template_button_click_12_listener", "tmp_1_0", "tmp_2_0", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "i9", "DefaultValueAccessor", "NumberValueAccessor", "NgControlStatus", "NgModel", "i10", "NbCardComponent", "NbCardBodyComponent", "NbCardFooterComponent", "NbCardHeaderComponent", "NbCheckboxComponent", "NbInputDirective", "NbSelectComponent", "NbOptionComponent", "i11", "BreadcrumbComponent", "i12", "BaseFilePipe", "styles"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\selection-management\\content-management-sales-account\\detail-content-management-sales-account\\detail-content-management-sales-account.component.ts", "C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\selection-management\\content-management-sales-account\\detail-content-management-sales-account\\detail-content-management-sales-account.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { CommonModule, Location } from '@angular/common';\r\nimport { NbCheckboxModule } from '@nebular/theme';\r\nimport { AllowHelper } from 'src/app/shared/helper/allowHelper';\r\nimport { FormItemService, MaterialService, RegularNoticeFileService } from 'src/services/api/services';\r\nimport { ActivatedRoute } from '@angular/router';\r\nimport { MessageService } from 'src/app/shared/services/message.service';\r\nimport { tap } from 'rxjs';\r\nimport { CreateListFormItem, FileViewModel, GetListFormItemRes, SaveListFormItemReq } from 'src/services/api/models';\r\nimport { BaseFilePipe } from 'src/app/@theme/pipes/base-file.pipe';\r\nimport { UtilityService } from 'src/app/shared/services/utility.service';\r\nimport { ValidationHelper } from 'src/app/shared/helper/validationHelper';\r\nimport { SharedModule } from 'src/app/pages/components/shared.module';\r\nimport { BaseComponent } from 'src/app/pages/components/base/baseComponent';\r\nimport { EventService, EEvent } from 'src/app/shared/services/event.service';\r\nimport { Base64ImagePipe } from \"../../../../@theme/pipes/base64-image.pipe\";\r\n\r\n\r\nexport interface ExtendedSaveListFormItemReq {\r\n  CDesignFileUrl?: string | null;\r\n  CMatrialUrl?: string[] | null;\r\n  CFile?: FileViewModel,\r\n  CFormItemHouseHold?: Array<string> | null;\r\n  CFormItemId?: number;\r\n  CItemName?: string;\r\n  CFormID?: number;\r\n  CPart?: string | null;\r\n  CName?: string | null;\r\n  CLocation?: string | null;\r\n  CRemarkType?: string | null;\r\n  CTotalAnswer?: number;\r\n  CRequireAnswer?: number;\r\n  CUiType?: number;\r\n  selectedCUiType: any | null;\r\n  selectedItems: { [key: string]: boolean }\r\n  selectedRemarkType?: { [key: string]: boolean }\r\n  allSelected: boolean,\r\n  listPictures: any[],\r\n  currentImageIndex?: number; // 當前顯示的圖片索引\r\n  isModalOpen?: boolean; // 是否打開放大模態窗口\r\n}\r\n\r\n@Component({\r\n  selector: 'ngx-detail-content-management-sales-account',\r\n  templateUrl: './detail-content-management-sales-account.component.html',\r\n  styleUrls: ['./detail-content-management-sales-account.component.scss'],\r\n  standalone: true,\r\n  imports: [CommonModule, SharedModule, NbCheckboxModule, BaseFilePipe, Base64ImagePipe],\r\n})\r\n\r\nexport class DetailContentManagementSalesAccountComponent extends BaseComponent implements OnInit {\r\n  constructor(\r\n    private _allow: AllowHelper,\r\n    private route: ActivatedRoute,\r\n    private message: MessageService,\r\n    private _formItemService: FormItemService,\r\n    private _regularNoticeFileService: RegularNoticeFileService,\r\n    private _utilityService: UtilityService,\r\n    private valid: ValidationHelper,\r\n    private location: Location,\r\n    private _materialService: MaterialService,\r\n    private _eventService: EventService\r\n  ) {\r\n    super(_allow)\r\n  }\r\n\r\n  typeContentManagementSalesAccount = {\r\n    CFormType: 2,\r\n    CNoticeType: 2\r\n  }\r\n\r\n  CUiTypeOptions: any[] = [\r\n    {\r\n      value: 1, label: '建材選色'\r\n    },\r\n    {\r\n      value: 2, label: '群組選樣_選色'\r\n    }, {\r\n      value: 3, label: '建材選樣'\r\n    }\r\n  ]\r\n  CRemarkTypeOptions = [\"正常\", \"留料\"]\r\n  buildCaseId: number\r\n\r\n  override ngOnInit(): void {\r\n    this.route.paramMap.subscribe(params => {\r\n      if (params) {\r\n        const idParam = params.get('id');\r\n        const id = idParam ? +idParam : 0;\r\n        this.buildCaseId = id\r\n        if (this.buildCaseId) {\r\n          this.getListRegularNoticeFileHouseHold()\r\n        }\r\n      }\r\n    })\r\n  }\r\n\r\n\r\n  getItemByValue(value: any, options: any[]) {\r\n    for (const item of options) {\r\n      if (item.value === value) {\r\n        return item;\r\n      }\r\n    }\r\n    return null;\r\n  }\r\n\r\n\r\n  selectedItems: { [key: string]: boolean } = {};\r\n  selectedRemarkType: { [key: string]: boolean } = {};\r\n\r\n\r\n\r\n  detectFiles(event: any, formItemReq_: any) {\r\n    const file = event.target.files[0]\r\n    if (file) {\r\n      let reader = new FileReader();\r\n      reader.readAsDataURL(file);\r\n      reader.onload = () => {\r\n        let base64Str: string = reader.result as string;\r\n        if (!base64Str) {\r\n          return;\r\n        }\r\n        if (formItemReq_.listPictures.length > 0) {\r\n          formItemReq_.listPictures[0] = {\r\n            id: new Date().getTime(),\r\n            name: file.name.split('.')[0],\r\n            data: base64Str,\r\n            extension: this._utilityService.getFileExtension(file.name),\r\n            CFile: file\r\n          };\r\n        } else {\r\n          formItemReq_.listPictures.push({\r\n            id: new Date().getTime(),\r\n            name: file.name.split('.')[0],\r\n            data: base64Str,\r\n            extension: this._utilityService.getFileExtension(file.name),\r\n            CFile: file\r\n          });\r\n        }\r\n        event.target.value = null;\r\n      };\r\n    }\r\n  }\r\n\r\n  removeImage(pictureId: number, formItemReq_: any) {\r\n    if (formItemReq_.listPictures.length) {\r\n      formItemReq_.listPictures = formItemReq_.listPictures.filter((x: any) => x.id != pictureId)\r\n    }\r\n  }\r\n  renameFile(event: any, index: number, formItemReq_: any) {\r\n    var blob = formItemReq_.listPictures[index].CFile.slice(0, formItemReq_.listPictures[index].CFile.size, formItemReq_.listPictures[index].CFile.type);\r\n    var newFile = new File([blob], `${event.target.value + '.' + formItemReq_.listPictures[index].extension}`, { type: formItemReq_.listPictures[index].CFile.type });\r\n    formItemReq_.listPictures[index].CFile = newFile\r\n  }\r\n\r\n  // 輪播功能方法\r\n  nextImage(formItemReq: any) {\r\n    if (formItemReq.CMatrialUrl && formItemReq.CMatrialUrl.length > 0) {\r\n      formItemReq.currentImageIndex = (formItemReq.currentImageIndex + 1) % formItemReq.CMatrialUrl.length;\r\n    }\r\n  }\r\n\r\n  prevImage(formItemReq: any) {\r\n    if (formItemReq.CMatrialUrl && formItemReq.CMatrialUrl.length > 0) {\r\n      formItemReq.currentImageIndex = formItemReq.currentImageIndex === 0\r\n        ? formItemReq.CMatrialUrl.length - 1\r\n        : formItemReq.currentImageIndex - 1;\r\n    }\r\n  }\r\n  getCurrentImage(formItemReq: any): string | null {\r\n    if (formItemReq.CMatrialUrl && formItemReq.CMatrialUrl.length > 0 && formItemReq.currentImageIndex !== undefined) {\r\n      return formItemReq.CMatrialUrl[formItemReq.currentImageIndex];\r\n    }\r\n    return null;\r\n  }\r\n\r\n  // 放大功能方法\r\n  openImageModal(formItemReq: any, imageIndex?: number) {\r\n    if (formItemReq.CMatrialUrl && formItemReq.CMatrialUrl.length > 0) {\r\n      if (imageIndex !== undefined) {\r\n        formItemReq.currentImageIndex = imageIndex;\r\n      }\r\n      formItemReq.isModalOpen = true;\r\n      // 防止背景滾動\r\n      document.body.style.overflow = 'hidden';\r\n    }\r\n  }\r\n\r\n  closeImageModal(formItemReq: any) {\r\n    formItemReq.isModalOpen = false;\r\n    // 恢復背景滾動\r\n    document.body.style.overflow = 'auto';\r\n  }\r\n\r\n  // 模態窗口中的輪播方法\r\n  nextImageModal(formItemReq: any) {\r\n    this.nextImage(formItemReq);\r\n  }\r\n\r\n  prevImageModal(formItemReq: any) {\r\n    this.prevImage(formItemReq);\r\n  }\r\n\r\n  // 鍵盤事件處理\r\n  onKeydown(event: KeyboardEvent, formItemReq: any) {\r\n    if (formItemReq.isModalOpen) {\r\n      switch (event.key) {\r\n        case 'ArrowLeft':\r\n          event.preventDefault();\r\n          this.prevImageModal(formItemReq);\r\n          break;\r\n        case 'ArrowRight':\r\n          event.preventDefault();\r\n          this.nextImageModal(formItemReq);\r\n          break;\r\n        case 'Escape':\r\n          event.preventDefault();\r\n          this.closeImageModal(formItemReq);\r\n          break;\r\n      }\r\n    }\r\n  }\r\n\r\n  onCheckAllChange(checked: boolean, formItemReq_: any) {\r\n    formItemReq_.allSelected = checked;\r\n    this.houseHoldList.forEach(item => {\r\n      formItemReq_.selectedItems[item] = checked;\r\n    });\r\n  }\r\n\r\n  onCheckboxHouseHoldListChange(checked: boolean, item: string, formItemReq_: any) {\r\n    if (checked) {\r\n      formItemReq_.selectedItems[item] = checked;\r\n      formItemReq_.allSelected = this.houseHoldList.every(item => formItemReq_.selectedItems[item] == checked);\r\n    } else {\r\n      formItemReq_.allSelected = false\r\n    }\r\n  }\r\n\r\n\r\n\r\n  onCheckboxRemarkChange(checked: boolean, item: string, formItemReq_: any) {\r\n    formItemReq_.selectedRemarkType[item] = checked;\r\n  }\r\n\r\n  createRemarkObject(CRemarkTypeOptions: string[], CRemarkType: string): { [key: string]: boolean } {\r\n    const remarkObject: { [key: string]: boolean } = {};\r\n    for (const option of CRemarkTypeOptions) {\r\n      remarkObject[option] = false;\r\n    }\r\n    const remarkTypes = CRemarkType.split('-');\r\n    for (const type of remarkTypes) {\r\n      if (CRemarkTypeOptions.includes(type)) {\r\n        remarkObject[type] = true;\r\n      }\r\n    }\r\n    return remarkObject;\r\n  }\r\n\r\n  mergeItems(items: ExtendedSaveListFormItemReq[]): ExtendedSaveListFormItemReq[] {\r\n    const map = new Map<string, { item: ExtendedSaveListFormItemReq, count: number }>();\r\n\r\n    items.forEach(item => {\r\n      const key = `${item.CLocation}_${item.CName}_${item.CPart}`;\r\n      if (map.has(key)) {\r\n        const existing = map.get(key)!;\r\n        existing.count += 1;\r\n      } else {\r\n        map.set(key, { item: { ...item }, count: 1 });\r\n      }\r\n    });\r\n\r\n    return Array.from(map.values()).map(({ item, count }) => ({\r\n      ...item,\r\n      CTotalAnswer: count\r\n    }));\r\n  }\r\n\r\n\r\n  getMaterialList() { // call when create\r\n    this._materialService.apiMaterialGetMaterialListPost$Json({\r\n      body: {\r\n        CBuildCaseId: this.buildCaseId,\r\n        CPagi: false\r\n      }\r\n    }).pipe(\r\n      tap(res => {\r\n        if (res.Entries && res.StatusCode == 0) {\r\n\r\n          this.houseHoldList.forEach(item => this.selectedItems[item] = false);\r\n\r\n          this.CRemarkTypeOptions.forEach(item => this.selectedRemarkType[item] = false);\r\n\r\n          this.arrListFormItemReq = res.Entries.map((o: any) => {\r\n            return {\r\n              CDesignFileUrl: null,\r\n              CFormItemHouseHold: null,\r\n              CFormId: null,\r\n              CLocation: o.CLocation,\r\n              CName: o.CName,\r\n              CPart: o.CPart,\r\n              CItemName: `${o.CName}-${o.CPart}-${o.CLocation}`,\r\n              CRemarkType: null,\r\n              CTotalAnswer: 0,\r\n              CRequireAnswer: 1,\r\n              CUiType: 0, selectedItems: {},\r\n              selectedRemarkType: this.selectedRemarkType,\r\n              allSelected: false,\r\n              listPictures: [],              selectedCUiType: this.CUiTypeOptions[0],\r\n              CMatrialUrl: o.CPicture ? [o.CPicture] : [],\r\n              currentImageIndex: 0,\r\n              isModalOpen: false,\r\n            }\r\n          })\r\n          this.arrListFormItemReq = [...this.mergeItems(this.arrListFormItemReq)]\r\n        }\r\n      })\r\n    ).subscribe();\r\n  }\r\n\r\n  listFormItem: GetListFormItemRes\r\n  isNew: boolean = true\r\n\r\n  getListFormItem() {\r\n    this._formItemService.apiFormItemGetListFormItemPost$Json({\r\n      body: {\r\n        CBuildCaseId: this.buildCaseId,\r\n        CFormType: this.typeContentManagementSalesAccount.CFormType,\r\n        CIsPaging: false\r\n      }\r\n    }).pipe(\r\n      tap(res => {\r\n        if (res.Entries && res.StatusCode == 0) {\r\n          this.listFormItem = res.Entries\r\n          this.isNew = res.Entries.formItems ? false : true\r\n          if (res.Entries.formItems) {\r\n            this.houseHoldList.forEach(item => this.selectedItems[item] = false);\r\n            this.CRemarkTypeOptions.forEach(item => this.selectedRemarkType[item] = false);\r\n\r\n            this.arrListFormItemReq = res.Entries.formItems.map((o: any) => {\r\n              return {\r\n                CFormId: this.listFormItem.CFormId,\r\n                CDesignFileUrl: o.CDesignFileUrl,\r\n                CMatrialUrl: o.CMatrialUrl || (o.CFirstMatrialUrl ? [o.CFirstMatrialUrl] : []),\r\n                CFile: o.CFile,\r\n                CFormItemHouseHold: o.CFormItemHouseHold,\r\n                CFormItemId: o.CFormItemId,\r\n                CLocation: o.CLocation,\r\n                CName: o.CName,\r\n                CPart: o.CPart,\r\n                CItemName: o.CItemName ? o.CItemName : `${o.CName}-${o.CPart}-${o.CLocation}`,\r\n                CRemarkType: o.CRemarkType,\r\n                CTotalAnswer: o.CTotalAnswer,\r\n                CRequireAnswer: o.CUiType === 3 ? 1 : o.CRequireAnswer,\r\n                CUiType: o.CUiType,\r\n                selectedItems: o.tblFormItemHouseholds.length ? this.createArrayObjectFromArray(this.houseHoldList, o.tblFormItemHouseholds) : { ...this.selectedItems }, selectedRemarkType: o.CRemarkType ? this.createRemarkObject(this.CRemarkTypeOptions, o.CRemarkType) : { ...this.selectedRemarkType },\r\n                allSelected: o.tblFormItemHouseholds.length === this.houseHoldList.length,\r\n                listPictures: [],\r\n                selectedCUiType: o.CUiType ? this.getItemByValue(o.CUiType, this.CUiTypeOptions) : this.CUiTypeOptions[0],\r\n                currentImageIndex: 0\r\n              }\r\n            })\r\n          } else {\r\n            this.getMaterialList()\r\n          }\r\n        }\r\n      })\r\n    ).subscribe()\r\n  }\r\n\r\n  changeSelectCUiType(formItemReq: any) {\r\n    if (formItemReq.selectedCUiType && formItemReq.selectedCUiType.value === 3) {\r\n      formItemReq.CRequireAnswer = 1\r\n    }\r\n  }\r\n  getHouseHoldListByNoticeType(data: any[]) {\r\n    for (let item of data) {\r\n      if (item.CNoticeType === this.typeContentManagementSalesAccount.CNoticeType) {\r\n        return item.CHouseHoldList;\r\n      }\r\n    }\r\n    return [];\r\n  }\r\n\r\n  arrListFormItemReq: ExtendedSaveListFormItemReq[]\r\n\r\n  getKeysWithTrueValue(obj: Record<string, boolean>): string[] { // {\"key\": true } => [\"key\"]\r\n    return Object.keys(obj).filter(key => obj[key]);\r\n  }\r\n\r\n  getKeysWithTrueValueJoined(obj: Record<string, boolean>): string {\r\n    return Object.keys(obj)\r\n      .filter(key => obj[key])\r\n      .join('-');\r\n  }\r\n\r\n  getCRemarkType(selectedCUiType: any, selectedRemarkType: any): any {\r\n    if (selectedCUiType && selectedCUiType.value == 3) {\r\n      return this.getKeysWithTrueValueJoined(selectedRemarkType)\r\n    }\r\n  }\r\n\r\n  getStringAfterComma(inputString: string): string {\r\n    const parts = inputString.split(',');\r\n    if (parts.length > 1) {\r\n      return parts[1];\r\n    } else return \"\"\r\n  }\r\n\r\n  formatFile(listPictures: any) {\r\n    if (listPictures && listPictures.length > 0) {\r\n      return {\r\n        Base64String: this.getStringAfterComma(listPictures[0].data) || null,\r\n        FileExtension: listPictures[0].extension || null,\r\n        FileName: listPictures[0].CFile.name || listPictures[0].name || null,\r\n      }\r\n    } else return undefined\r\n\r\n  }\r\n\r\n\r\n  validation() {\r\n    this.valid.clear();\r\n    let hasInvalidCUiType = false;\r\n    let hasInvalidCRequireAnswer = false;\r\n    let hasInvalidItemName = false;\r\n\r\n    for (const item of this.saveListFormItemReq) {\r\n      if (!hasInvalidCUiType && (!item.CUiType)) {\r\n        hasInvalidCUiType = true;\r\n      }\r\n      if (!hasInvalidCRequireAnswer && (!item.CRequireAnswer)) {\r\n        hasInvalidCRequireAnswer = true;\r\n      }\r\n      if (item.CTotalAnswer && item.CRequireAnswer) {\r\n        if (item.CRequireAnswer > item.CTotalAnswer || item.CRequireAnswer < 0) {\r\n          this.valid.addErrorMessage('[必填數量]' + ' <= ' + item.CTotalAnswer + ` (${item.CItemName}) `);\r\n        }\r\n      }\r\n\r\n      if (!hasInvalidItemName && (!item.CItemName)) {\r\n        hasInvalidItemName = true;\r\n      }\r\n    }\r\n    if (hasInvalidCUiType) {\r\n      this.valid.addErrorMessage('[前台UI類型]' + ' 必填');\r\n    }\r\n    if (hasInvalidCRequireAnswer) {\r\n      this.valid.addErrorMessage('[必填數量]' + ' 必填且>0');\r\n    }\r\n    if (hasInvalidItemName) {\r\n      this.valid.addErrorMessage('[廚房-廚具-櫃體]' + ' 必填');\r\n    }\r\n  }\r\n\r\n  saveListFormItemReq: SaveListFormItemReq[]\r\n\r\n  onSubmit() {\r\n    this.saveListFormItemReq = this.arrListFormItemReq.map((e: any) => {\r\n      return {\r\n        CDesignFileUrl: e.CDesignFileUrl ? e.CDesignFileUrl : null,\r\n        CFile: e.listPictures ? this.formatFile(e.listPictures) : undefined,\r\n        CFormItemHouseHold: this.getKeysWithTrueValue(e.selectedItems),\r\n        CFormItemId: e.CFormItemId ? e.CFormItemId : null,\r\n        CFormID: this.isNew ? null : this.listFormItem.CFormId,\r\n        CName: e.CName,\r\n        CPart: e.CPart,\r\n        CLocation: e.CLocation,\r\n        CItemName: e.CItemName, //? e.CItemName : `${e.CName}-${e.CPart}-${e.CLocation}`,\r\n        CRemarkType: e.selectedCUiType.value !== 3 ? null : this.getCRemarkType(e.selectedCUiType, e.selectedRemarkType) || null,\r\n        CTotalAnswer: e.CTotalAnswer,\r\n        CRequireAnswer: e.CRequireAnswer,\r\n        CUiType: e.selectedCUiType.value,\r\n      }\r\n    })\r\n    this.validation()\r\n    if (this.valid.errorMessages.length > 0) {\r\n      this.message.showErrorMSGs(this.valid.errorMessages);\r\n      return\r\n    }\r\n    if (this.isNew) {\r\n      this.createListFormItem()\r\n\r\n    } else {\r\n      this.saveListFormItem()\r\n    }\r\n  }\r\n\r\n  saveListFormItem() {\r\n    this._formItemService.apiFormItemSaveListFormItemPost$Json({\r\n      body: this.saveListFormItemReq\r\n    }).subscribe(res => {\r\n      if (res.StatusCode == 0) {\r\n        this.message.showSucessMSG(\"執行成功\");\r\n        // this.getListFormItem()\r\n        this.goBack()\r\n      }\r\n    })\r\n  }\r\n\r\n  creatListFormItem: CreateListFormItem\r\n\r\n  createListFormItem() {\r\n    this.creatListFormItem = {\r\n      CBuildCaseId: this.buildCaseId,\r\n      CFormItem: this.saveListFormItemReq || null,\r\n      CFormType: this.typeContentManagementSalesAccount.CFormType,\r\n    }\r\n\r\n    this._formItemService.apiFormItemCreateListFormItemPost$Json({\r\n      body: this.creatListFormItem\r\n    }).subscribe(res => {\r\n      if (res.StatusCode == 0) {\r\n        this.message.showSucessMSG(\"執行成功\");\r\n        // this.getListFormItem()\r\n        this.goBack()\r\n      }\r\n    })\r\n  }\r\n\r\n  createArrayObjectFromArray(a: string[], b: { CFormItemHouseholdId: number, CFormItemId: number, CHousehold: string, CIsSelect: boolean }[]): { [key: string]: boolean } {\r\n    const c: { [key: string]: boolean } = {};\r\n    for (const item of a) {\r\n      const matchingItem = b.find(bItem => bItem.CHousehold === item && bItem.CIsSelect);\r\n      c[item] = !!matchingItem;\r\n    }\r\n    return c;\r\n  } //[\"House1\", \"House2\", \"House3\"] => [{CHousehold: \"House1\", CIsSelect: true,... }, ... ]\r\n\r\n\r\n  houseHoldList: any[]\r\n\r\n  getListRegularNoticeFileHouseHold() {\r\n    this._regularNoticeFileService.apiRegularNoticeFileGetListRegularNoticeFileHouseHoldPost$Json({\r\n      body: this.buildCaseId\r\n    }).pipe(\r\n      tap(res => {\r\n        if (res.Entries && res.StatusCode == 0) {\r\n          this.houseHoldList = this.getHouseHoldListByNoticeType(res.Entries)\r\n          this.getListFormItem()\r\n        }\r\n      })\r\n    ).subscribe()\r\n  }\r\n  goBack() {\r\n    this._eventService.push({\r\n      action: EEvent.GET_BUILDCASE,\r\n      payload: this.buildCaseId\r\n    })\r\n    this.location.back()\r\n  }\r\n}\r\n", "<!-- 3.7.1  CNoticeType = 1 -->\r\n<nb-card accent=\"success\">\r\n  <nb-card-header>\r\n    <ngx-breadcrumb></ngx-breadcrumb>\r\n  </nb-card-header>\r\n  <nb-card-body class=\"pb-4\"> <!-- Added padding to bottom of card body -->\r\n    <h1 class=\"font-bold text-[#818181] mb-4\"></h1> <!-- Retained original empty h1, added margin-bottom -->\r\n    <div class=\"space-y-6\"> <!-- Add vertical spacing between main sections -->\r\n      <h4 class=\"font-bold text-xl pb-2 border-b mb-4\">類型-獨立選樣</h4> <!-- Styled section title -->\r\n\r\n      <ng-container *ngFor=\"let formItemReq of arrListFormItemReq; let idx = index\">\r\n        <!-- Enhanced container for each form item -->\r\n        <div class=\"p-4 border rounded-lg shadow-sm bg-white\">\r\n          <div class=\"flex flex-wrap -mx-2\"> <!-- Row container with negative margin for column padding -->\r\n\r\n            <!-- Left Column: Main Material Image + Form Fields -->\r\n            <div class=\"w-full md:w-3/4 px-2\">\r\n              <div class=\"flex flex-wrap\"> <!-- Main Material Images (CMatrialUrl) -->\r\n                <div class=\"w-full md:w-1/3 pr-0 md:pr-3 mb-4 md:mb-0\">\r\n                  <label class=\"block text-sm font-medium text-gray-700 mb-1\">主要材料示意</label>\r\n                  <div *ngIf=\"formItemReq.CMatrialUrl && formItemReq.CMatrialUrl.length > 0\" class=\"relative\">\r\n                    <!-- Image carousel container -->\r\n                    <div class=\"h-[160px] w-full relative overflow-hidden rounded border\">\r\n                      <img class=\"h-full w-full object-cover transition-all duration-300\"\r\n                        [src]=\"getCurrentImage(formItemReq) | base64Image\" *ngIf=\"getCurrentImage(formItemReq)\">\r\n\r\n                      <!-- Previous button -->\r\n                      <button *ngIf=\"formItemReq.CMatrialUrl.length > 1\"\r\n                        class=\"absolute left-2 top-1/2 transform -translate-y-1/2 bg-black bg-opacity-50 text-white rounded-full w-8 h-8 flex items-center justify-center hover:bg-opacity-70 transition-all\"\r\n                        (click)=\"prevImage(formItemReq)\">\r\n                        <svg class=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                          <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M15 19l-7-7 7-7\">\r\n                          </path>\r\n                        </svg>\r\n                      </button>\r\n\r\n                      <!-- Next button -->\r\n                      <button *ngIf=\"formItemReq.CMatrialUrl.length > 1\"\r\n                        class=\"absolute right-2 top-1/2 transform -translate-y-1/2 bg-black bg-opacity-50 text-white rounded-full w-8 h-8 flex items-center justify-center hover:bg-opacity-70 transition-all\"\r\n                        (click)=\"nextImage(formItemReq)\">\r\n                        <svg class=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                          <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 5l7 7-7 7\"></path>\r\n                        </svg>\r\n                      </button>\r\n\r\n                      <!-- Image counter -->\r\n                      <div *ngIf=\"formItemReq.CMatrialUrl.length > 1\"\r\n                        class=\"absolute bottom-2 right-2 bg-black bg-opacity-50 text-white text-xs px-2 py-1 rounded\">\r\n                        {{(formItemReq.currentImageIndex || 0) + 1}} / {{formItemReq.CMatrialUrl.length}}\r\n                      </div>\r\n                    </div>\r\n\r\n                    <!-- Thumbnail navigation for multiple images -->\r\n                    <div *ngIf=\"formItemReq.CMatrialUrl.length > 1\" class=\"flex gap-1 mt-2 overflow-x-auto\">\r\n                      <button *ngFor=\"let imageUrl of formItemReq.CMatrialUrl; let i = index\"\r\n                        class=\"flex-shrink-0 w-12 h-12 border-2 rounded overflow-hidden\"\r\n                        [class.border-blue-500]=\"i === (formItemReq.currentImageIndex || 0)\"\r\n                        [class.border-gray-300]=\"i !== (formItemReq.currentImageIndex || 0)\"\r\n                        (click)=\"formItemReq.currentImageIndex = i\">\r\n                        <img class=\"w-full h-full object-cover\" [src]=\"imageUrl | base64Image\">\r\n                      </button>\r\n                    </div>\r\n                  </div>\r\n                  <div *ngIf=\"!formItemReq.CMatrialUrl || formItemReq.CMatrialUrl.length === 0\"\r\n                    class=\"h-[160px] w-full flex items-center justify-center border rounded bg-gray-50 text-gray-400\">\r\n                    無主要材料示意\r\n                  </div>\r\n                </div>\r\n\r\n                <!-- Form Fields -->\r\n                <div class=\"w-full md:w-2/3 md:pl-3\">\r\n                  <div class=\"form-group flex items-center w-full mb-3\">\r\n                    <label [for]=\"'CItemName_' + idx\" class=\"label w-1/3 text-base pr-2 shrink-0\">項目名稱</label>\r\n                    <div class=\"input-group items-baseline w-2/3\">\r\n                      <span\r\n                        class=\"text-gray-600 text-sm mr-1 shrink-0\">{{formItemReq.CName}}-{{formItemReq.CPart}}-{{formItemReq.CLocation}}:</span>\r\n                      <input type=\"text\" [id]=\"'CItemName_' + idx\" class=\"w-full\" nbInput\r\n                        [(ngModel)]=\"formItemReq.CItemName\" placeholder=\"例如：廚房檯面\"\r\n                        [disabled]=\"listFormItem.CIsLock ?? false\" />\r\n                    </div>\r\n                  </div>\r\n                  <div class=\"form-group flex items-center w-full mb-3\">\r\n                    <label [for]=\"'cRequireAnswer_' + idx\" class=\"label w-1/3 pr-2 shrink-0\">必填數量</label>\r\n                    <input type=\"number\" [id]=\"'cRequireAnswer_' + idx\" class=\"w-2/3\" nbInput placeholder=\"輸入數量\"\r\n                      [(ngModel)]=\"formItemReq.CRequireAnswer\"\r\n                      [disabled]=\"formItemReq.selectedCUiType.value === 3 || (listFormItem.CIsLock ?? false)\" />\r\n                  </div>\r\n                  <div class=\"form-group flex items-center w-full\">\r\n                    <label [for]=\"'uiType_' + idx\" class=\"label w-1/3 pr-2 shrink-0\">前台UI類型</label>\r\n                    <nb-select placeholder=\"選擇UI類型\" [id]=\"'uiType_' + idx\" [(ngModel)]=\"formItemReq.selectedCUiType\"\r\n                      class=\"w-2/3\" (selectedChange)=\"changeSelectCUiType(formItemReq)\"\r\n                      [disabled]=\"listFormItem.CIsLock ?? false\">\r\n                      <nb-option *ngFor=\"let case of CUiTypeOptions\" [value]=\"case\">\r\n                        {{ case.label }}\r\n                      </nb-option>\r\n                    </nb-select>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            <!-- Right Column: Concept Design Image Upload and Preview -->\r\n            <div class=\"w-full md:w-1/4 px-2 mt-4 md:mt-0\">\r\n              <div class=\"w-full flex flex-col\">\r\n                <div class=\"flex justify-end w-full mb-2\">\r\n                  <button class=\"btn btn-info h-fit w-full\" [disabled]=\"listFormItem.CIsLock\"\r\n                    (click)=\"inputFile.click()\">上傳概念設計圖</button>\r\n                  <!-- Note: #inputFile in *ngFor creates a local template variable for each iteration -->\r\n                  <input #inputFile type=\"file\" class=\"hidden\" (change)=\"detectFiles($event, formItemReq)\"\r\n                    accept=\"image/png, image/gif, image/jpeg\">\r\n                </div>\r\n\r\n                <!-- Uploaded Pictures List -->\r\n                <div *ngIf=\"formItemReq.listPictures && formItemReq.listPictures.length > 0\" class=\"mt-2 space-y-3\">\r\n                  <label class=\"block text-sm font-medium text-gray-700 mb-1\">已上傳概念圖</label>\r\n                  <div *ngFor=\"let picture of formItemReq.listPictures; let i = index\"\r\n                    class=\"border p-2 rounded-md bg-gray-50\">\r\n                    <img class=\"h-32 w-full object-cover rounded mb-2 border\" [src]=\"picture.data\">\r\n                    <input nbInput class=\"w-full p-1 text-xs mb-1\" type=\"text\" placeholder=\"圖片說明/檔名\"\r\n                      [value]=\"picture.name\" (blur)=\"renameFile($event, i, formItemReq)\"\r\n                      [disabled]=\"listFormItem.CIsLock ?? false\">\r\n                    <button class=\"btn btn-outline-danger btn-xs w-full\" (click)=\"removeImage(picture.id, formItemReq)\"\r\n                      [disabled]=\"listFormItem.CIsLock ?? false\">删除</button>\r\n                  </div>\r\n                </div>\r\n\r\n                <!-- Default Concept Design Image (if no uploaded list) -->\r\n                <div class=\"w-full text-center mt-2\"\r\n                  *ngIf=\"formItemReq.CDesignFileUrl && (!formItemReq.listPictures || formItemReq.listPictures.length === 0)\">\r\n                  <label class=\"block text-sm font-medium text-gray-700 mb-1\">預設概念圖</label>\r\n                  <img class=\"h-32 w-full object-cover rounded border\" [src]=\"formItemReq.CDesignFileUrl | addBaseFile\">\r\n                </div>\r\n\r\n                <div\r\n                  *ngIf=\"!formItemReq.CDesignFileUrl && (!formItemReq.listPictures || formItemReq.listPictures.length === 0)\"\r\n                  class=\"h-32 w-full flex items-center justify-center border rounded bg-gray-50 text-gray-400 mt-2\">\r\n                  無概念設計圖\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- Separator Line -->\r\n          <hr class=\"my-4\">\r\n\r\n          <!-- Applicable Households -->\r\n          <div class=\"flex flex-wrap w-full items-center mb-3\">\r\n            <div class=\"w-full md:w-1/5 px-2 pb-1 md:pb-0 font-medium text-gray-700\">適用戶別</div>\r\n            <div class=\"w-full md:w-4/5 px-2\">\r\n              <label class=\"mr-3 cursor-pointer\" *ngIf=\"houseHoldList.length\">\r\n                <nb-checkbox [(checked)]=\"formItemReq.allSelected\" [disabled]=\"listFormItem.CIsLock\"\r\n                  (checkedChange)=\"onCheckAllChange($event, formItemReq)\">\r\n                  全選\r\n                </nb-checkbox>\r\n              </label>\r\n              <ng-container *ngIf=\"houseHoldList.length > 0; else noHouseholds\">\r\n                <label *ngFor=\"let item of houseHoldList\" class=\"mr-3 cursor-pointer\">\r\n                  <nb-checkbox [(checked)]=\"formItemReq.selectedItems[item]\" value=\"item\"\r\n                    [disabled]=\"listFormItem.CIsLock\"\r\n                    (checkedChange)=\"onCheckboxHouseHoldListChange($event, item, formItemReq)\">\r\n                    {{ item }}\r\n                  </nb-checkbox>\r\n                </label>\r\n              </ng-container>\r\n              <ng-template #noHouseholds>\r\n                <span class=\"text-gray-500 text-sm\">尚無戶別資料</span>\r\n              </ng-template>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- Remark Options (Conditional) -->\r\n          <div class=\"flex flex-wrap w-full items-center\" *ngIf=\"formItemReq.selectedCUiType.value === 3\">\r\n            <div class=\"w-full md:w-1/5 px-2 pb-1 md:pb-0 font-medium text-gray-700\">備註選項</div>\r\n            <div class=\"w-full md:w-4/5 px-2\">\r\n              <ng-container *ngIf=\"CRemarkTypeOptions && CRemarkTypeOptions.length > 0; else noRemarkOptions\">\r\n                <label *ngFor=\"let remark of CRemarkTypeOptions\" class=\"mr-3 cursor-pointer\">\r\n                  <nb-checkbox *ngIf=\"formItemReq.selectedRemarkType\"\r\n                    [(checked)]=\"formItemReq.selectedRemarkType[remark]\" [disabled]=\"listFormItem.CIsLock\" value=\"item\"\r\n                    (checkedChange)=\"onCheckboxRemarkChange($event, remark, formItemReq)\">\r\n                    {{ remark }}\r\n                  </nb-checkbox>\r\n                </label>\r\n              </ng-container>\r\n              <ng-template #noRemarkOptions>\r\n                <span class=\"text-gray-500 text-sm\">尚無備註選項</span>\r\n              </ng-template>\r\n            </div>\r\n          </div>\r\n\r\n        </div> <!-- End of enhanced container for each form item -->\r\n      </ng-container>\r\n\r\n      <!-- Removed commented out old ngFor structure -->\r\n\r\n    </div>\r\n  </nb-card-body>\r\n  <nb-card-footer class=\"d-flex justify-content-end p-3 sticky bottom-0 bg-white border-t\"> <!-- Made footer sticky -->\r\n    <button class=\"btn btn-secondary mx-2\" (click)=\"goBack()\" [disabled]=\"listFormItem.CIsLock ?? false\">\r\n      取消\r\n    </button>\r\n    <button class=\"btn btn-primary\" (click)=\"onSubmit()\" [disabled]=\"listFormItem.CIsLock ?? false\">\r\n      儲存\r\n    </button>\r\n  </nb-card-footer>\r\n</nb-card>"], "mappings": "AACA,SAASA,YAAY,QAAkB,iBAAiB;AACxD,SAASC,gBAAgB,QAAQ,gBAAgB;AAKjD,SAASC,GAAG,QAAQ,MAAM;AAK1B,SAASC,YAAY,QAAQ,wCAAwC;AACrE,SAASC,aAAa,QAAQ,6CAA6C;AAC3E,SAAuBC,MAAM,QAAQ,uCAAuC;AAC5E,SAASC,eAAe,QAAQ,4CAA4C;;;;;;;;;;;;;;;;ICQtDC,EAAA,CAAAC,SAAA,cAC0F;;;;;;IAAxFD,EAAA,CAAAE,UAAA,QAAAF,EAAA,CAAAG,WAAA,OAAAC,MAAA,CAAAC,eAAA,CAAAC,cAAA,IAAAN,EAAA,CAAAO,aAAA,CAAkD;;;;;;IAGpDP,EAAA,CAAAQ,cAAA,iBAEmC;IAAjCR,EAAA,CAAAS,UAAA,mBAAAC,4GAAA;MAAAV,EAAA,CAAAW,aAAA,CAAAC,GAAA;MAAA,MAAAN,cAAA,GAAAN,EAAA,CAAAa,aAAA,IAAAC,SAAA;MAAA,MAAAV,MAAA,GAAAJ,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAe,WAAA,CAASX,MAAA,CAAAY,SAAA,CAAAV,cAAA,CAAsB;IAAA,EAAC;;IAChCN,EAAA,CAAAQ,cAAA,cAA2E;IACzER,EAAA,CAAAC,SAAA,eACO;IAEXD,EADE,CAAAiB,YAAA,EAAM,EACC;;;;;;IAGTjB,EAAA,CAAAQ,cAAA,iBAEmC;IAAjCR,EAAA,CAAAS,UAAA,mBAAAS,4GAAA;MAAAlB,EAAA,CAAAW,aAAA,CAAAQ,GAAA;MAAA,MAAAb,cAAA,GAAAN,EAAA,CAAAa,aAAA,IAAAC,SAAA;MAAA,MAAAV,MAAA,GAAAJ,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAe,WAAA,CAASX,MAAA,CAAAgB,SAAA,CAAAd,cAAA,CAAsB;IAAA,EAAC;;IAChCN,EAAA,CAAAQ,cAAA,cAA2E;IACzER,EAAA,CAAAC,SAAA,eAA8F;IAElGD,EADE,CAAAiB,YAAA,EAAM,EACC;;;;;IAGTjB,EAAA,CAAAQ,cAAA,cACgG;IAC9FR,EAAA,CAAAqB,MAAA,GACF;IAAArB,EAAA,CAAAiB,YAAA,EAAM;;;;IADJjB,EAAA,CAAAsB,SAAA,EACF;IADEtB,EAAA,CAAAuB,kBAAA,OAAAjB,cAAA,CAAAkB,iBAAA,mBAAAlB,cAAA,CAAAmB,WAAA,CAAAC,MAAA,MACF;;;;;;IAKA1B,EAAA,CAAAQ,cAAA,iBAI8C;IAA5CR,EAAA,CAAAS,UAAA,mBAAAkB,kHAAA;MAAA,MAAAC,IAAA,GAAA5B,EAAA,CAAAW,aAAA,CAAAkB,GAAA,EAAAC,KAAA;MAAA,MAAAxB,cAAA,GAAAN,EAAA,CAAAa,aAAA,IAAAC,SAAA;MAAA,OAAAd,EAAA,CAAAe,WAAA,CAAAT,cAAA,CAAAkB,iBAAA,GAAAI,IAAA;IAAA,EAA2C;IAC3C5B,EAAA,CAAAC,SAAA,cAAuE;;IACzED,EAAA,CAAAiB,YAAA,EAAS;;;;;;IAHPjB,EADA,CAAA+B,WAAA,oBAAAH,IAAA,MAAAtB,cAAA,CAAAkB,iBAAA,OAAoE,oBAAAI,IAAA,MAAAtB,cAAA,CAAAkB,iBAAA,OACA;IAE5BxB,EAAA,CAAAsB,SAAA,EAA8B;IAA9BtB,EAAA,CAAAE,UAAA,QAAAF,EAAA,CAAAG,WAAA,OAAA6B,WAAA,GAAAhC,EAAA,CAAAO,aAAA,CAA8B;;;;;IAN1EP,EAAA,CAAAQ,cAAA,cAAwF;IACtFR,EAAA,CAAAiC,UAAA,IAAAC,yFAAA,qBAI8C;IAGhDlC,EAAA,CAAAiB,YAAA,EAAM;;;;IAPyBjB,EAAA,CAAAsB,SAAA,EAA4B;IAA5BtB,EAAA,CAAAE,UAAA,YAAAI,cAAA,CAAAmB,WAAA,CAA4B;;;;;IAhC3DzB,EAFF,CAAAQ,cAAA,cAA4F,cAEpB;IAwBpER,EAvBA,CAAAiC,UAAA,IAAAE,gFAAA,kBAC0F,IAAAC,mFAAA,qBAKvD,IAAAC,mFAAA,qBAUA,IAAAC,gFAAA,kBAQ6D;IAGlGtC,EAAA,CAAAiB,YAAA,EAAM;IAGNjB,EAAA,CAAAiC,UAAA,IAAAM,gFAAA,kBAAwF;IAS1FvC,EAAA,CAAAiB,YAAA,EAAM;;;;;IAtCoDjB,EAAA,CAAAsB,SAAA,GAAkC;IAAlCtB,EAAA,CAAAE,UAAA,SAAAE,MAAA,CAAAC,eAAA,CAAAC,cAAA,EAAkC;IAG/EN,EAAA,CAAAsB,SAAA,EAAwC;IAAxCtB,EAAA,CAAAE,UAAA,SAAAI,cAAA,CAAAmB,WAAA,CAAAC,MAAA,KAAwC;IAUxC1B,EAAA,CAAAsB,SAAA,EAAwC;IAAxCtB,EAAA,CAAAE,UAAA,SAAAI,cAAA,CAAAmB,WAAA,CAAAC,MAAA,KAAwC;IAS3C1B,EAAA,CAAAsB,SAAA,EAAwC;IAAxCtB,EAAA,CAAAE,UAAA,SAAAI,cAAA,CAAAmB,WAAA,CAAAC,MAAA,KAAwC;IAO1C1B,EAAA,CAAAsB,SAAA,EAAwC;IAAxCtB,EAAA,CAAAE,UAAA,SAAAI,cAAA,CAAAmB,WAAA,CAAAC,MAAA,KAAwC;;;;;IAUhD1B,EAAA,CAAAQ,cAAA,cACoG;IAClGR,EAAA,CAAAqB,MAAA,mDACF;IAAArB,EAAA,CAAAiB,YAAA,EAAM;;;;;IA0BFjB,EAAA,CAAAQ,cAAA,oBAA8D;IAC5DR,EAAA,CAAAqB,MAAA,GACF;IAAArB,EAAA,CAAAiB,YAAA,EAAY;;;;IAFmCjB,EAAA,CAAAE,UAAA,UAAAsC,OAAA,CAAc;IAC3DxC,EAAA,CAAAsB,SAAA,EACF;IADEtB,EAAA,CAAAyC,kBAAA,MAAAD,OAAA,CAAAE,KAAA,MACF;;;;;;IAqBJ1C,EAAA,CAAAQ,cAAA,cAC2C;IACzCR,EAAA,CAAAC,SAAA,cAA+E;IAC/ED,EAAA,CAAAQ,cAAA,gBAE6C;IADpBR,EAAA,CAAAS,UAAA,kBAAAkC,wGAAAC,MAAA;MAAA,MAAAC,KAAA,GAAA7C,EAAA,CAAAW,aAAA,CAAAmC,IAAA,EAAAhB,KAAA;MAAA,MAAAxB,cAAA,GAAAN,EAAA,CAAAa,aAAA,IAAAC,SAAA;MAAA,MAAAV,MAAA,GAAAJ,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAe,WAAA,CAAQX,MAAA,CAAA2C,UAAA,CAAAH,MAAA,EAAAC,KAAA,EAAAvC,cAAA,CAAkC;IAAA,EAAC;IADpEN,EAAA,CAAAiB,YAAA,EAE6C;IAC7CjB,EAAA,CAAAQ,cAAA,iBAC6C;IADQR,EAAA,CAAAS,UAAA,mBAAAuC,0GAAA;MAAA,MAAAC,WAAA,GAAAjD,EAAA,CAAAW,aAAA,CAAAmC,IAAA,EAAAhC,SAAA;MAAA,MAAAR,cAAA,GAAAN,EAAA,CAAAa,aAAA,IAAAC,SAAA;MAAA,MAAAV,MAAA,GAAAJ,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAe,WAAA,CAASX,MAAA,CAAA8C,WAAA,CAAAD,WAAA,CAAAE,EAAA,EAAA7C,cAAA,CAAoC;IAAA,EAAC;IACtDN,EAAA,CAAAqB,MAAA,mBAAE;IACjDrB,EADiD,CAAAiB,YAAA,EAAS,EACpD;;;;;;;IANsDjB,EAAA,CAAAsB,SAAA,EAAoB;IAApBtB,EAAA,CAAAE,UAAA,QAAA+C,WAAA,CAAAG,IAAA,EAAApD,EAAA,CAAAO,aAAA,CAAoB;IAE5EP,EAAA,CAAAsB,SAAA,EAAsB;IACtBtB,EADA,CAAAE,UAAA,UAAA+C,WAAA,CAAAI,IAAA,CAAsB,cAAAC,QAAA,GAAAlD,MAAA,CAAAmD,YAAA,CAAAC,OAAA,cAAAF,QAAA,KAAAG,SAAA,GAAAH,QAAA,SACoB;IAE1CtD,EAAA,CAAAsB,SAAA,EAA0C;IAA1CtB,EAAA,CAAAE,UAAA,cAAAwD,QAAA,GAAAtD,MAAA,CAAAmD,YAAA,CAAAC,OAAA,cAAAE,QAAA,KAAAD,SAAA,GAAAC,QAAA,SAA0C;;;;;IAR9C1D,EADF,CAAAQ,cAAA,cAAoG,gBACtC;IAAAR,EAAA,CAAAqB,MAAA,2CAAM;IAAArB,EAAA,CAAAiB,YAAA,EAAQ;IAC1EjB,EAAA,CAAAiC,UAAA,IAAA0B,iFAAA,kBAC2C;IAQ7C3D,EAAA,CAAAiB,YAAA,EAAM;;;;IATqBjB,EAAA,CAAAsB,SAAA,GAA6B;IAA7BtB,EAAA,CAAAE,UAAA,YAAAI,cAAA,CAAAsD,YAAA,CAA6B;;;;;IActD5D,EAFF,CAAAQ,cAAA,cAC6G,gBAC/C;IAAAR,EAAA,CAAAqB,MAAA,qCAAK;IAAArB,EAAA,CAAAiB,YAAA,EAAQ;IACzEjB,EAAA,CAAAC,SAAA,cAAsG;;IACxGD,EAAA,CAAAiB,YAAA,EAAM;;;;IADiDjB,EAAA,CAAAsB,SAAA,GAAgD;IAAhDtB,EAAA,CAAAE,UAAA,QAAAF,EAAA,CAAAG,WAAA,OAAAG,cAAA,CAAAuD,cAAA,GAAA7D,EAAA,CAAAO,aAAA,CAAgD;;;;;IAGvGP,EAAA,CAAAQ,cAAA,cAEoG;IAClGR,EAAA,CAAAqB,MAAA,6CACF;IAAArB,EAAA,CAAAiB,YAAA,EAAM;;;;;;IAaNjB,EADF,CAAAQ,cAAA,gBAAgE,sBAEJ;IAD7CR,EAAA,CAAA8D,gBAAA,2BAAAC,mHAAAnB,MAAA;MAAA5C,EAAA,CAAAW,aAAA,CAAAqD,IAAA;MAAA,MAAA1D,cAAA,GAAAN,EAAA,CAAAa,aAAA,GAAAC,SAAA;MAAAd,EAAA,CAAAiE,kBAAA,CAAA3D,cAAA,CAAA4D,WAAA,EAAAtB,MAAA,MAAAtC,cAAA,CAAA4D,WAAA,GAAAtB,MAAA;MAAA,OAAA5C,EAAA,CAAAe,WAAA,CAAA6B,MAAA;IAAA,EAAqC;IAChD5C,EAAA,CAAAS,UAAA,2BAAAsD,mHAAAnB,MAAA;MAAA5C,EAAA,CAAAW,aAAA,CAAAqD,IAAA;MAAA,MAAA1D,cAAA,GAAAN,EAAA,CAAAa,aAAA,GAAAC,SAAA;MAAA,MAAAV,MAAA,GAAAJ,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAe,WAAA,CAAiBX,MAAA,CAAA+D,gBAAA,CAAAvB,MAAA,EAAAtC,cAAA,CAAqC;IAAA,EAAC;IACvDN,EAAA,CAAAqB,MAAA,qBACF;IACFrB,EADE,CAAAiB,YAAA,EAAc,EACR;;;;;IAJOjB,EAAA,CAAAsB,SAAA,EAAqC;IAArCtB,EAAA,CAAAoE,gBAAA,YAAA9D,cAAA,CAAA4D,WAAA,CAAqC;IAAClE,EAAA,CAAAE,UAAA,aAAAE,MAAA,CAAAmD,YAAA,CAAAC,OAAA,CAAiC;;;;;;IAOlFxD,EADF,CAAAQ,cAAA,gBAAsE,sBAGS;IAFhER,EAAA,CAAA8D,gBAAA,2BAAAO,kIAAAzB,MAAA;MAAA,MAAA0B,QAAA,GAAAtE,EAAA,CAAAW,aAAA,CAAA4D,IAAA,EAAAzD,SAAA;MAAA,MAAAR,cAAA,GAAAN,EAAA,CAAAa,aAAA,IAAAC,SAAA;MAAAd,EAAA,CAAAiE,kBAAA,CAAA3D,cAAA,CAAAkE,aAAA,CAAAF,QAAA,GAAA1B,MAAA,MAAAtC,cAAA,CAAAkE,aAAA,CAAAF,QAAA,IAAA1B,MAAA;MAAA,OAAA5C,EAAA,CAAAe,WAAA,CAAA6B,MAAA;IAAA,EAA6C;IAExD5C,EAAA,CAAAS,UAAA,2BAAA4D,kIAAAzB,MAAA;MAAA,MAAA0B,QAAA,GAAAtE,EAAA,CAAAW,aAAA,CAAA4D,IAAA,EAAAzD,SAAA;MAAA,MAAAR,cAAA,GAAAN,EAAA,CAAAa,aAAA,IAAAC,SAAA;MAAA,MAAAV,MAAA,GAAAJ,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAe,WAAA,CAAiBX,MAAA,CAAAqE,6BAAA,CAAA7B,MAAA,EAAA0B,QAAA,EAAAhE,cAAA,CAAwD;IAAA,EAAC;IAC1EN,EAAA,CAAAqB,MAAA,GACF;IACFrB,EADE,CAAAiB,YAAA,EAAc,EACR;;;;;;IALOjB,EAAA,CAAAsB,SAAA,EAA6C;IAA7CtB,EAAA,CAAAoE,gBAAA,YAAA9D,cAAA,CAAAkE,aAAA,CAAAF,QAAA,EAA6C;IACxDtE,EAAA,CAAAE,UAAA,aAAAE,MAAA,CAAAmD,YAAA,CAAAC,OAAA,CAAiC;IAEjCxD,EAAA,CAAAsB,SAAA,EACF;IADEtB,EAAA,CAAAyC,kBAAA,MAAA6B,QAAA,MACF;;;;;IANJtE,EAAA,CAAA0E,uBAAA,GAAkE;IAChE1E,EAAA,CAAAiC,UAAA,IAAA0C,4FAAA,oBAAsE;;;;;IAA9C3E,EAAA,CAAAsB,SAAA,EAAgB;IAAhBtB,EAAA,CAAAE,UAAA,YAAAE,MAAA,CAAAwE,aAAA,CAAgB;;;;;IASxC5E,EAAA,CAAAQ,cAAA,eAAoC;IAAAR,EAAA,CAAAqB,MAAA,2CAAM;IAAArB,EAAA,CAAAiB,YAAA,EAAO;;;;;;IAW/CjB,EAAA,CAAAQ,cAAA,sBAEwE;IADtER,EAAA,CAAA8D,gBAAA,2BAAAe,sJAAAjC,MAAA;MAAA5C,EAAA,CAAAW,aAAA,CAAAmE,IAAA;MAAA,MAAAC,UAAA,GAAA/E,EAAA,CAAAa,aAAA,GAAAC,SAAA;MAAA,MAAAR,cAAA,GAAAN,EAAA,CAAAa,aAAA,IAAAC,SAAA;MAAAd,EAAA,CAAAiE,kBAAA,CAAA3D,cAAA,CAAA0E,kBAAA,CAAAD,UAAA,GAAAnC,MAAA,MAAAtC,cAAA,CAAA0E,kBAAA,CAAAD,UAAA,IAAAnC,MAAA;MAAA,OAAA5C,EAAA,CAAAe,WAAA,CAAA6B,MAAA;IAAA,EAAoD;IACpD5C,EAAA,CAAAS,UAAA,2BAAAoE,sJAAAjC,MAAA;MAAA5C,EAAA,CAAAW,aAAA,CAAAmE,IAAA;MAAA,MAAAC,UAAA,GAAA/E,EAAA,CAAAa,aAAA,GAAAC,SAAA;MAAA,MAAAR,cAAA,GAAAN,EAAA,CAAAa,aAAA,IAAAC,SAAA;MAAA,MAAAV,MAAA,GAAAJ,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAe,WAAA,CAAiBX,MAAA,CAAA6E,sBAAA,CAAArC,MAAA,EAAAmC,UAAA,EAAAzE,cAAA,CAAmD;IAAA,EAAC;IACrEN,EAAA,CAAAqB,MAAA,GACF;IAAArB,EAAA,CAAAiB,YAAA,EAAc;;;;;;IAHZjB,EAAA,CAAAoE,gBAAA,YAAA9D,cAAA,CAAA0E,kBAAA,CAAAD,UAAA,EAAoD;IAAC/E,EAAA,CAAAE,UAAA,aAAAE,MAAA,CAAAmD,YAAA,CAAAC,OAAA,CAAiC;IAEtFxD,EAAA,CAAAsB,SAAA,EACF;IADEtB,EAAA,CAAAyC,kBAAA,MAAAsC,UAAA,MACF;;;;;IALF/E,EAAA,CAAAQ,cAAA,gBAA6E;IAC3ER,EAAA,CAAAiC,UAAA,IAAAiD,gHAAA,0BAEwE;IAG1ElF,EAAA,CAAAiB,YAAA,EAAQ;;;;IALQjB,EAAA,CAAAsB,SAAA,EAAoC;IAApCtB,EAAA,CAAAE,UAAA,SAAAI,cAAA,CAAA0E,kBAAA,CAAoC;;;;;IAFtDhF,EAAA,CAAA0E,uBAAA,GAAgG;IAC9F1E,EAAA,CAAAiC,UAAA,IAAAkD,kGAAA,oBAA6E;;;;;IAAnDnF,EAAA,CAAAsB,SAAA,EAAqB;IAArBtB,EAAA,CAAAE,UAAA,YAAAE,MAAA,CAAAgF,kBAAA,CAAqB;;;;;IAS/CpF,EAAA,CAAAQ,cAAA,eAAoC;IAAAR,EAAA,CAAAqB,MAAA,2CAAM;IAAArB,EAAA,CAAAiB,YAAA,EAAO;;;;;IAZrDjB,EADF,CAAAQ,cAAA,cAAgG,cACrB;IAAAR,EAAA,CAAAqB,MAAA,+BAAI;IAAArB,EAAA,CAAAiB,YAAA,EAAM;IACnFjB,EAAA,CAAAQ,cAAA,cAAkC;IAUhCR,EATA,CAAAiC,UAAA,IAAAoD,0FAAA,2BAAgG,IAAAC,yFAAA,gCAAAtF,EAAA,CAAAuF,sBAAA,CASlE;IAIlCvF,EADE,CAAAiB,YAAA,EAAM,EACF;;;;;IAbajB,EAAA,CAAAsB,SAAA,GAA2D;IAAAtB,EAA3D,CAAAE,UAAA,SAAAE,MAAA,CAAAgF,kBAAA,IAAAhF,MAAA,CAAAgF,kBAAA,CAAA1D,MAAA,KAA2D,aAAA8D,mBAAA,CAAoB;;;;;;IApKtGxF,EAAA,CAAA0E,uBAAA,GAA8E;IASlE1E,EAPV,CAAAQ,cAAA,cAAsD,cAClB,cAGE,cACJ,cAC6B,gBACO;IAAAR,EAAA,CAAAqB,MAAA,2CAAM;IAAArB,EAAA,CAAAiB,YAAA,EAAQ;IA4C1EjB,EA3CA,CAAAiC,UAAA,IAAAwD,0EAAA,kBAA4F,IAAAC,0EAAA,kBA4CQ;IAGtG1F,EAAA,CAAAiB,YAAA,EAAM;IAKFjB,EAFJ,CAAAQ,cAAA,eAAqC,eACmB,iBAC0B;IAAAR,EAAA,CAAAqB,MAAA,gCAAI;IAAArB,EAAA,CAAAiB,YAAA,EAAQ;IAExFjB,EADF,CAAAQ,cAAA,eAA8C,gBAEE;IAAAR,EAAA,CAAAqB,MAAA,IAAsE;IAAArB,EAAA,CAAAiB,YAAA,EAAO;IAC3HjB,EAAA,CAAAQ,cAAA,iBAE+C;IAD7CR,EAAA,CAAA8D,gBAAA,2BAAA6B,qGAAA/C,MAAA;MAAA,MAAAtC,cAAA,GAAAN,EAAA,CAAAW,aAAA,CAAAiF,GAAA,EAAA9E,SAAA;MAAAd,EAAA,CAAAiE,kBAAA,CAAA3D,cAAA,CAAAuF,SAAA,EAAAjD,MAAA,MAAAtC,cAAA,CAAAuF,SAAA,GAAAjD,MAAA;MAAA,OAAA5C,EAAA,CAAAe,WAAA,CAAA6B,MAAA;IAAA,EAAmC;IAGzC5C,EAJI,CAAAiB,YAAA,EAE+C,EAC3C,EACF;IAEJjB,EADF,CAAAQ,cAAA,eAAsD,iBACqB;IAAAR,EAAA,CAAAqB,MAAA,gCAAI;IAAArB,EAAA,CAAAiB,YAAA,EAAQ;IACrFjB,EAAA,CAAAQ,cAAA,iBAE4F;IAD1FR,EAAA,CAAA8D,gBAAA,2BAAAgC,qGAAAlD,MAAA;MAAA,MAAAtC,cAAA,GAAAN,EAAA,CAAAW,aAAA,CAAAiF,GAAA,EAAA9E,SAAA;MAAAd,EAAA,CAAAiE,kBAAA,CAAA3D,cAAA,CAAAyF,cAAA,EAAAnD,MAAA,MAAAtC,cAAA,CAAAyF,cAAA,GAAAnD,MAAA;MAAA,OAAA5C,EAAA,CAAAe,WAAA,CAAA6B,MAAA;IAAA,EAAwC;IAE5C5C,EAHE,CAAAiB,YAAA,EAE4F,EACxF;IAEJjB,EADF,CAAAQ,cAAA,eAAiD,iBACkB;IAAAR,EAAA,CAAAqB,MAAA,kCAAM;IAAArB,EAAA,CAAAiB,YAAA,EAAQ;IAC/EjB,EAAA,CAAAQ,cAAA,qBAE6C;IAFUR,EAAA,CAAA8D,gBAAA,2BAAAkC,yGAAApD,MAAA;MAAA,MAAAtC,cAAA,GAAAN,EAAA,CAAAW,aAAA,CAAAiF,GAAA,EAAA9E,SAAA;MAAAd,EAAA,CAAAiE,kBAAA,CAAA3D,cAAA,CAAA2F,eAAA,EAAArD,MAAA,MAAAtC,cAAA,CAAA2F,eAAA,GAAArD,MAAA;MAAA,OAAA5C,EAAA,CAAAe,WAAA,CAAA6B,MAAA;IAAA,EAAyC;IAChF5C,EAAA,CAAAS,UAAA,4BAAAyF,0GAAA;MAAA,MAAA5F,cAAA,GAAAN,EAAA,CAAAW,aAAA,CAAAiF,GAAA,EAAA9E,SAAA;MAAA,MAAAV,MAAA,GAAAJ,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAe,WAAA,CAAkBX,MAAA,CAAA+F,mBAAA,CAAA7F,cAAA,CAAgC;IAAA,EAAC;IAEjEN,EAAA,CAAAiC,UAAA,KAAAmE,iFAAA,wBAA8D;IAOxEpG,EAJQ,CAAAiB,YAAA,EAAY,EACR,EACF,EACF,EACF;IAMAjB,EAHN,CAAAQ,cAAA,eAA+C,eACX,eACU,kBAEV;IAA5BR,EAAA,CAAAS,UAAA,mBAAA4F,8FAAA;MAAArG,EAAA,CAAAW,aAAA,CAAAiF,GAAA;MAAA,MAAAU,aAAA,GAAAtG,EAAA,CAAAuG,WAAA;MAAA,OAAAvG,EAAA,CAAAe,WAAA,CAASuF,aAAA,CAAAE,KAAA,EAAiB;IAAA,EAAC;IAACxG,EAAA,CAAAqB,MAAA,kDAAO;IAAArB,EAAA,CAAAiB,YAAA,EAAS;IAE9CjB,EAAA,CAAAQ,cAAA,oBAC4C;IADCR,EAAA,CAAAS,UAAA,oBAAAgG,8FAAA7D,MAAA;MAAA,MAAAtC,cAAA,GAAAN,EAAA,CAAAW,aAAA,CAAAiF,GAAA,EAAA9E,SAAA;MAAA,MAAAV,MAAA,GAAAJ,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAe,WAAA,CAAUX,MAAA,CAAAsG,WAAA,CAAA9D,MAAA,EAAAtC,cAAA,CAAgC;IAAA,EAAC;IAE1FN,EAFE,CAAAiB,YAAA,EAC4C,EACxC;IAuBNjB,EApBA,CAAAiC,UAAA,KAAA0E,2EAAA,kBAAoG,KAAAC,2EAAA,kBAeS,KAAAC,2EAAA,kBAOT;IAK1G7G,EAFI,CAAAiB,YAAA,EAAM,EACF,EACF;IAGNjB,EAAA,CAAAC,SAAA,cAAiB;IAIfD,EADF,CAAAQ,cAAA,eAAqD,eACsB;IAAAR,EAAA,CAAAqB,MAAA,gCAAI;IAAArB,EAAA,CAAAiB,YAAA,EAAM;IACnFjB,EAAA,CAAAQ,cAAA,eAAkC;IAgBhCR,EAfA,CAAAiC,UAAA,KAAA6E,6EAAA,oBAAgE,KAAAC,oFAAA,2BAME,KAAAC,mFAAA,gCAAAhH,EAAA,CAAAuF,sBAAA,CASvC;IAI/BvF,EADE,CAAAiB,YAAA,EAAM,EACF;IAGNjB,EAAA,CAAAiC,UAAA,KAAAgF,2EAAA,kBAAgG;IAkBlGjH,EAAA,CAAAiB,YAAA,EAAM;;;;;;;;;;;IAzKUjB,EAAA,CAAAsB,SAAA,GAAmE;IAAnEtB,EAAA,CAAAE,UAAA,SAAAI,cAAA,CAAAmB,WAAA,IAAAnB,cAAA,CAAAmB,WAAA,CAAAC,MAAA,KAAmE;IA2CnE1B,EAAA,CAAAsB,SAAA,EAAsE;IAAtEtB,EAAA,CAAAE,UAAA,UAAAI,cAAA,CAAAmB,WAAA,IAAAnB,cAAA,CAAAmB,WAAA,CAAAC,MAAA,OAAsE;IASnE1B,EAAA,CAAAsB,SAAA,GAA0B;IAA1BtB,EAAA,CAAAE,UAAA,uBAAAgH,OAAA,CAA0B;IAGelH,EAAA,CAAAsB,SAAA,GAAsE;IAAtEtB,EAAA,CAAAmH,kBAAA,KAAA7G,cAAA,CAAA8G,KAAA,OAAA9G,cAAA,CAAA+G,KAAA,OAAA/G,cAAA,CAAAgH,SAAA,MAAsE;IACjGtH,EAAA,CAAAsB,SAAA,EAAyB;IAAzBtB,EAAA,CAAAE,UAAA,sBAAAgH,OAAA,CAAyB;IAC1ClH,EAAA,CAAAoE,gBAAA,YAAA9D,cAAA,CAAAuF,SAAA,CAAmC;IACnC7F,EAAA,CAAAE,UAAA,cAAAoD,QAAA,GAAAlD,MAAA,CAAAmD,YAAA,CAAAC,OAAA,cAAAF,QAAA,KAAAG,SAAA,GAAAH,QAAA,SAA0C;IAIvCtD,EAAA,CAAAsB,SAAA,GAA+B;IAA/BtB,EAAA,CAAAE,UAAA,4BAAAgH,OAAA,CAA+B;IACjBlH,EAAA,CAAAsB,SAAA,GAA8B;IAA9BtB,EAAA,CAAAE,UAAA,2BAAAgH,OAAA,CAA8B;IACjDlH,EAAA,CAAAoE,gBAAA,YAAA9D,cAAA,CAAAyF,cAAA,CAAwC;IACxC/F,EAAA,CAAAE,UAAA,aAAAI,cAAA,CAAA2F,eAAA,CAAAsB,KAAA,YAAAC,QAAA,GAAApH,MAAA,CAAAmD,YAAA,CAAAC,OAAA,cAAAgE,QAAA,KAAA/D,SAAA,GAAA+D,QAAA,UAAuF;IAGlFxH,EAAA,CAAAsB,SAAA,GAAuB;IAAvBtB,EAAA,CAAAE,UAAA,oBAAAgH,OAAA,CAAuB;IACElH,EAAA,CAAAsB,SAAA,GAAsB;IAAtBtB,EAAA,CAAAE,UAAA,mBAAAgH,OAAA,CAAsB;IAAClH,EAAA,CAAAoE,gBAAA,YAAA9D,cAAA,CAAA2F,eAAA,CAAyC;IAE9FjG,EAAA,CAAAE,UAAA,cAAAuH,QAAA,GAAArH,MAAA,CAAAmD,YAAA,CAAAC,OAAA,cAAAiE,QAAA,KAAAhE,SAAA,GAAAgE,QAAA,SAA0C;IACdzH,EAAA,CAAAsB,SAAA,EAAiB;IAAjBtB,EAAA,CAAAE,UAAA,YAAAE,MAAA,CAAAsH,cAAA,CAAiB;IAaP1H,EAAA,CAAAsB,SAAA,GAAiC;IAAjCtB,EAAA,CAAAE,UAAA,aAAAE,MAAA,CAAAmD,YAAA,CAAAC,OAAA,CAAiC;IAQvExD,EAAA,CAAAsB,SAAA,GAAqE;IAArEtB,EAAA,CAAAE,UAAA,SAAAI,cAAA,CAAAsD,YAAA,IAAAtD,cAAA,CAAAsD,YAAA,CAAAlC,MAAA,KAAqE;IAexE1B,EAAA,CAAAsB,SAAA,EAAwG;IAAxGtB,EAAA,CAAAE,UAAA,SAAAI,cAAA,CAAAuD,cAAA,MAAAvD,cAAA,CAAAsD,YAAA,IAAAtD,cAAA,CAAAsD,YAAA,CAAAlC,MAAA,QAAwG;IAMxG1B,EAAA,CAAAsB,SAAA,EAAyG;IAAzGtB,EAAA,CAAAE,UAAA,UAAAI,cAAA,CAAAuD,cAAA,MAAAvD,cAAA,CAAAsD,YAAA,IAAAtD,cAAA,CAAAsD,YAAA,CAAAlC,MAAA,QAAyG;IAe1E1B,EAAA,CAAAsB,SAAA,GAA0B;IAA1BtB,EAAA,CAAAE,UAAA,SAAAE,MAAA,CAAAwE,aAAA,CAAAlD,MAAA,CAA0B;IAM/C1B,EAAA,CAAAsB,SAAA,EAAgC;IAAAtB,EAAhC,CAAAE,UAAA,SAAAE,MAAA,CAAAwE,aAAA,CAAAlD,MAAA,KAAgC,aAAAiG,gBAAA,CAAiB;IAgBnB3H,EAAA,CAAAsB,SAAA,GAA6C;IAA7CtB,EAAA,CAAAE,UAAA,SAAAI,cAAA,CAAA2F,eAAA,CAAAsB,KAAA,OAA6C;;;ADzHxG,OAAM,MAAOK,4CAA6C,SAAQ/H,aAAa;EAC7EgI,YACUC,MAAmB,EACnBC,KAAqB,EACrBC,OAAuB,EACvBC,gBAAiC,EACjCC,yBAAmD,EACnDC,eAA+B,EAC/BC,KAAuB,EACvBC,QAAkB,EAClBC,gBAAiC,EACjCC,aAA2B;IAEnC,KAAK,CAACT,MAAM,CAAC;IAXL,KAAAA,MAAM,GAANA,MAAM;IACN,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,yBAAyB,GAAzBA,yBAAyB;IACzB,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,QAAQ,GAARA,QAAQ;IACR,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,aAAa,GAAbA,aAAa;IAKvB,KAAAC,iCAAiC,GAAG;MAClCC,SAAS,EAAE,CAAC;MACZC,WAAW,EAAE;KACd;IAED,KAAAhB,cAAc,GAAU,CACtB;MACEH,KAAK,EAAE,CAAC;MAAE7E,KAAK,EAAE;KAClB,EACD;MACE6E,KAAK,EAAE,CAAC;MAAE7E,KAAK,EAAE;KAClB,EAAE;MACD6E,KAAK,EAAE,CAAC;MAAE7E,KAAK,EAAE;KAClB,CACF;IACD,KAAA0C,kBAAkB,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC;IA2BjC,KAAAZ,aAAa,GAA+B,EAAE;IAC9C,KAAAQ,kBAAkB,GAA+B,EAAE;IAqNnD,KAAA2D,KAAK,GAAY,IAAI;EAlQrB;EAoBSC,QAAQA,CAAA;IACf,IAAI,CAACb,KAAK,CAACc,QAAQ,CAACC,SAAS,CAACC,MAAM,IAAG;MACrC,IAAIA,MAAM,EAAE;QACV,MAAMC,OAAO,GAAGD,MAAM,CAACE,GAAG,CAAC,IAAI,CAAC;QAChC,MAAM9F,EAAE,GAAG6F,OAAO,GAAG,CAACA,OAAO,GAAG,CAAC;QACjC,IAAI,CAACE,WAAW,GAAG/F,EAAE;QACrB,IAAI,IAAI,CAAC+F,WAAW,EAAE;UACpB,IAAI,CAACC,iCAAiC,EAAE;QAC1C;MACF;IACF,CAAC,CAAC;EACJ;EAGAC,cAAcA,CAAC7B,KAAU,EAAE8B,OAAc;IACvC,KAAK,MAAMC,IAAI,IAAID,OAAO,EAAE;MAC1B,IAAIC,IAAI,CAAC/B,KAAK,KAAKA,KAAK,EAAE;QACxB,OAAO+B,IAAI;MACb;IACF;IACA,OAAO,IAAI;EACb;EAQA5C,WAAWA,CAAC6C,KAAU,EAAEC,YAAiB;IACvC,MAAMC,IAAI,GAAGF,KAAK,CAACG,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC;IAClC,IAAIF,IAAI,EAAE;MACR,IAAIG,MAAM,GAAG,IAAIC,UAAU,EAAE;MAC7BD,MAAM,CAACE,aAAa,CAACL,IAAI,CAAC;MAC1BG,MAAM,CAACG,MAAM,GAAG,MAAK;QACnB,IAAIC,SAAS,GAAWJ,MAAM,CAACK,MAAgB;QAC/C,IAAI,CAACD,SAAS,EAAE;UACd;QACF;QACA,IAAIR,YAAY,CAAC5F,YAAY,CAAClC,MAAM,GAAG,CAAC,EAAE;UACxC8H,YAAY,CAAC5F,YAAY,CAAC,CAAC,CAAC,GAAG;YAC7BT,EAAE,EAAE,IAAI+G,IAAI,EAAE,CAACC,OAAO,EAAE;YACxB9G,IAAI,EAAEoG,IAAI,CAACpG,IAAI,CAAC+G,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;YAC7BhH,IAAI,EAAE4G,SAAS;YACfK,SAAS,EAAE,IAAI,CAAClC,eAAe,CAACmC,gBAAgB,CAACb,IAAI,CAACpG,IAAI,CAAC;YAC3DkH,KAAK,EAAEd;WACR;QACH,CAAC,MAAM;UACLD,YAAY,CAAC5F,YAAY,CAAC4G,IAAI,CAAC;YAC7BrH,EAAE,EAAE,IAAI+G,IAAI,EAAE,CAACC,OAAO,EAAE;YACxB9G,IAAI,EAAEoG,IAAI,CAACpG,IAAI,CAAC+G,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;YAC7BhH,IAAI,EAAE4G,SAAS;YACfK,SAAS,EAAE,IAAI,CAAClC,eAAe,CAACmC,gBAAgB,CAACb,IAAI,CAACpG,IAAI,CAAC;YAC3DkH,KAAK,EAAEd;WACR,CAAC;QACJ;QACAF,KAAK,CAACG,MAAM,CAACnC,KAAK,GAAG,IAAI;MAC3B,CAAC;IACH;EACF;EAEArE,WAAWA,CAACuH,SAAiB,EAAEjB,YAAiB;IAC9C,IAAIA,YAAY,CAAC5F,YAAY,CAAClC,MAAM,EAAE;MACpC8H,YAAY,CAAC5F,YAAY,GAAG4F,YAAY,CAAC5F,YAAY,CAAC8G,MAAM,CAAEC,CAAM,IAAKA,CAAC,CAACxH,EAAE,IAAIsH,SAAS,CAAC;IAC7F;EACF;EACA1H,UAAUA,CAACwG,KAAU,EAAEzH,KAAa,EAAE0H,YAAiB;IACrD,IAAIoB,IAAI,GAAGpB,YAAY,CAAC5F,YAAY,CAAC9B,KAAK,CAAC,CAACyI,KAAK,CAACM,KAAK,CAAC,CAAC,EAAErB,YAAY,CAAC5F,YAAY,CAAC9B,KAAK,CAAC,CAACyI,KAAK,CAACO,IAAI,EAAEtB,YAAY,CAAC5F,YAAY,CAAC9B,KAAK,CAAC,CAACyI,KAAK,CAACQ,IAAI,CAAC;IACpJ,IAAIC,OAAO,GAAG,IAAIC,IAAI,CAAC,CAACL,IAAI,CAAC,EAAE,GAAGrB,KAAK,CAACG,MAAM,CAACnC,KAAK,GAAG,GAAG,GAAGiC,YAAY,CAAC5F,YAAY,CAAC9B,KAAK,CAAC,CAACuI,SAAS,EAAE,EAAE;MAAEU,IAAI,EAAEvB,YAAY,CAAC5F,YAAY,CAAC9B,KAAK,CAAC,CAACyI,KAAK,CAACQ;IAAI,CAAE,CAAC;IACjKvB,YAAY,CAAC5F,YAAY,CAAC9B,KAAK,CAAC,CAACyI,KAAK,GAAGS,OAAO;EAClD;EAEA;EACA5J,SAASA,CAAC8J,WAAgB;IACxB,IAAIA,WAAW,CAACzJ,WAAW,IAAIyJ,WAAW,CAACzJ,WAAW,CAACC,MAAM,GAAG,CAAC,EAAE;MACjEwJ,WAAW,CAAC1J,iBAAiB,GAAG,CAAC0J,WAAW,CAAC1J,iBAAiB,GAAG,CAAC,IAAI0J,WAAW,CAACzJ,WAAW,CAACC,MAAM;IACtG;EACF;EAEAV,SAASA,CAACkK,WAAgB;IACxB,IAAIA,WAAW,CAACzJ,WAAW,IAAIyJ,WAAW,CAACzJ,WAAW,CAACC,MAAM,GAAG,CAAC,EAAE;MACjEwJ,WAAW,CAAC1J,iBAAiB,GAAG0J,WAAW,CAAC1J,iBAAiB,KAAK,CAAC,GAC/D0J,WAAW,CAACzJ,WAAW,CAACC,MAAM,GAAG,CAAC,GAClCwJ,WAAW,CAAC1J,iBAAiB,GAAG,CAAC;IACvC;EACF;EACAnB,eAAeA,CAAC6K,WAAgB;IAC9B,IAAIA,WAAW,CAACzJ,WAAW,IAAIyJ,WAAW,CAACzJ,WAAW,CAACC,MAAM,GAAG,CAAC,IAAIwJ,WAAW,CAAC1J,iBAAiB,KAAKiC,SAAS,EAAE;MAChH,OAAOyH,WAAW,CAACzJ,WAAW,CAACyJ,WAAW,CAAC1J,iBAAiB,CAAC;IAC/D;IACA,OAAO,IAAI;EACb;EAEA;EACA2J,cAAcA,CAACD,WAAgB,EAAEE,UAAmB;IAClD,IAAIF,WAAW,CAACzJ,WAAW,IAAIyJ,WAAW,CAACzJ,WAAW,CAACC,MAAM,GAAG,CAAC,EAAE;MACjE,IAAI0J,UAAU,KAAK3H,SAAS,EAAE;QAC5ByH,WAAW,CAAC1J,iBAAiB,GAAG4J,UAAU;MAC5C;MACAF,WAAW,CAACG,WAAW,GAAG,IAAI;MAC9B;MACAC,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,QAAQ;IACzC;EACF;EAEAC,eAAeA,CAACR,WAAgB;IAC9BA,WAAW,CAACG,WAAW,GAAG,KAAK;IAC/B;IACAC,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,MAAM;EACvC;EAEA;EACAE,cAAcA,CAACT,WAAgB;IAC7B,IAAI,CAAC9J,SAAS,CAAC8J,WAAW,CAAC;EAC7B;EAEAU,cAAcA,CAACV,WAAgB;IAC7B,IAAI,CAAClK,SAAS,CAACkK,WAAW,CAAC;EAC7B;EAEA;EACAW,SAASA,CAACtC,KAAoB,EAAE2B,WAAgB;IAC9C,IAAIA,WAAW,CAACG,WAAW,EAAE;MAC3B,QAAQ9B,KAAK,CAACuC,GAAG;QACf,KAAK,WAAW;UACdvC,KAAK,CAACwC,cAAc,EAAE;UACtB,IAAI,CAACH,cAAc,CAACV,WAAW,CAAC;UAChC;QACF,KAAK,YAAY;UACf3B,KAAK,CAACwC,cAAc,EAAE;UACtB,IAAI,CAACJ,cAAc,CAACT,WAAW,CAAC;UAChC;QACF,KAAK,QAAQ;UACX3B,KAAK,CAACwC,cAAc,EAAE;UACtB,IAAI,CAACL,eAAe,CAACR,WAAW,CAAC;UACjC;MACJ;IACF;EACF;EAEA/G,gBAAgBA,CAAC6H,OAAgB,EAAExC,YAAiB;IAClDA,YAAY,CAACtF,WAAW,GAAG8H,OAAO;IAClC,IAAI,CAACpH,aAAa,CAACqH,OAAO,CAAC3C,IAAI,IAAG;MAChCE,YAAY,CAAChF,aAAa,CAAC8E,IAAI,CAAC,GAAG0C,OAAO;IAC5C,CAAC,CAAC;EACJ;EAEAvH,6BAA6BA,CAACuH,OAAgB,EAAE1C,IAAY,EAAEE,YAAiB;IAC7E,IAAIwC,OAAO,EAAE;MACXxC,YAAY,CAAChF,aAAa,CAAC8E,IAAI,CAAC,GAAG0C,OAAO;MAC1CxC,YAAY,CAACtF,WAAW,GAAG,IAAI,CAACU,aAAa,CAACsH,KAAK,CAAC5C,IAAI,IAAIE,YAAY,CAAChF,aAAa,CAAC8E,IAAI,CAAC,IAAI0C,OAAO,CAAC;IAC1G,CAAC,MAAM;MACLxC,YAAY,CAACtF,WAAW,GAAG,KAAK;IAClC;EACF;EAIAe,sBAAsBA,CAAC+G,OAAgB,EAAE1C,IAAY,EAAEE,YAAiB;IACtEA,YAAY,CAACxE,kBAAkB,CAACsE,IAAI,CAAC,GAAG0C,OAAO;EACjD;EAEAG,kBAAkBA,CAAC/G,kBAA4B,EAAEgH,WAAmB;IAClE,MAAMC,YAAY,GAA+B,EAAE;IACnD,KAAK,MAAMC,MAAM,IAAIlH,kBAAkB,EAAE;MACvCiH,YAAY,CAACC,MAAM,CAAC,GAAG,KAAK;IAC9B;IACA,MAAMC,WAAW,GAAGH,WAAW,CAAChC,KAAK,CAAC,GAAG,CAAC;IAC1C,KAAK,MAAMW,IAAI,IAAIwB,WAAW,EAAE;MAC9B,IAAInH,kBAAkB,CAACoH,QAAQ,CAACzB,IAAI,CAAC,EAAE;QACrCsB,YAAY,CAACtB,IAAI,CAAC,GAAG,IAAI;MAC3B;IACF;IACA,OAAOsB,YAAY;EACrB;EAEAI,UAAUA,CAACC,KAAoC;IAC7C,MAAMC,GAAG,GAAG,IAAIC,GAAG,EAAgE;IAEnFF,KAAK,CAACT,OAAO,CAAC3C,IAAI,IAAG;MACnB,MAAMwC,GAAG,GAAG,GAAGxC,IAAI,CAAChC,SAAS,IAAIgC,IAAI,CAAClC,KAAK,IAAIkC,IAAI,CAACjC,KAAK,EAAE;MAC3D,IAAIsF,GAAG,CAACE,GAAG,CAACf,GAAG,CAAC,EAAE;QAChB,MAAMgB,QAAQ,GAAGH,GAAG,CAAC1D,GAAG,CAAC6C,GAAG,CAAE;QAC9BgB,QAAQ,CAACC,KAAK,IAAI,CAAC;MACrB,CAAC,MAAM;QACLJ,GAAG,CAACK,GAAG,CAAClB,GAAG,EAAE;UAAExC,IAAI,EAAE;YAAE,GAAGA;UAAI,CAAE;UAAEyD,KAAK,EAAE;QAAC,CAAE,CAAC;MAC/C;IACF,CAAC,CAAC;IAEF,OAAOE,KAAK,CAACC,IAAI,CAACP,GAAG,CAACQ,MAAM,EAAE,CAAC,CAACR,GAAG,CAAC,CAAC;MAAErD,IAAI;MAAEyD;IAAK,CAAE,MAAM;MACxD,GAAGzD,IAAI;MACP8D,YAAY,EAAEL;KACf,CAAC,CAAC;EACL;EAGAM,eAAeA,CAAA;IACb,IAAI,CAAC/E,gBAAgB,CAACgF,mCAAmC,CAAC;MACxD/B,IAAI,EAAE;QACJgC,YAAY,EAAE,IAAI,CAACrE,WAAW;QAC9BsE,KAAK,EAAE;;KAEV,CAAC,CAACC,IAAI,CACL9N,GAAG,CAAC+N,GAAG,IAAG;MACR,IAAIA,GAAG,CAACC,OAAO,IAAID,GAAG,CAACE,UAAU,IAAI,CAAC,EAAE;QAEtC,IAAI,CAAChJ,aAAa,CAACqH,OAAO,CAAC3C,IAAI,IAAI,IAAI,CAAC9E,aAAa,CAAC8E,IAAI,CAAC,GAAG,KAAK,CAAC;QAEpE,IAAI,CAAClE,kBAAkB,CAAC6G,OAAO,CAAC3C,IAAI,IAAI,IAAI,CAACtE,kBAAkB,CAACsE,IAAI,CAAC,GAAG,KAAK,CAAC;QAE9E,IAAI,CAACuE,kBAAkB,GAAGH,GAAG,CAACC,OAAO,CAAChB,GAAG,CAAEmB,CAAM,IAAI;UACnD,OAAO;YACLjK,cAAc,EAAE,IAAI;YACpBkK,kBAAkB,EAAE,IAAI;YACxBC,OAAO,EAAE,IAAI;YACb1G,SAAS,EAAEwG,CAAC,CAACxG,SAAS;YACtBF,KAAK,EAAE0G,CAAC,CAAC1G,KAAK;YACdC,KAAK,EAAEyG,CAAC,CAACzG,KAAK;YACdxB,SAAS,EAAE,GAAGiI,CAAC,CAAC1G,KAAK,IAAI0G,CAAC,CAACzG,KAAK,IAAIyG,CAAC,CAACxG,SAAS,EAAE;YACjD8E,WAAW,EAAE,IAAI;YACjBgB,YAAY,EAAE,CAAC;YACfrH,cAAc,EAAE,CAAC;YACjBkI,OAAO,EAAE,CAAC;YAAEzJ,aAAa,EAAE,EAAE;YAC7BQ,kBAAkB,EAAE,IAAI,CAACA,kBAAkB;YAC3Cd,WAAW,EAAE,KAAK;YAClBN,YAAY,EAAE,EAAE;YAAeqC,eAAe,EAAE,IAAI,CAACyB,cAAc,CAAC,CAAC,CAAC;YACtEjG,WAAW,EAAEqM,CAAC,CAACI,QAAQ,GAAG,CAACJ,CAAC,CAACI,QAAQ,CAAC,GAAG,EAAE;YAC3C1M,iBAAiB,EAAE,CAAC;YACpB6J,WAAW,EAAE;WACd;QACH,CAAC,CAAC;QACF,IAAI,CAACwC,kBAAkB,GAAG,CAAC,GAAG,IAAI,CAACpB,UAAU,CAAC,IAAI,CAACoB,kBAAkB,CAAC,CAAC;MACzE;IACF,CAAC,CAAC,CACH,CAAC/E,SAAS,EAAE;EACf;EAKAqF,eAAeA,CAAA;IACb,IAAI,CAAClG,gBAAgB,CAACmG,mCAAmC,CAAC;MACxD7C,IAAI,EAAE;QACJgC,YAAY,EAAE,IAAI,CAACrE,WAAW;QAC9BT,SAAS,EAAE,IAAI,CAACD,iCAAiC,CAACC,SAAS;QAC3D4F,SAAS,EAAE;;KAEd,CAAC,CAACZ,IAAI,CACL9N,GAAG,CAAC+N,GAAG,IAAG;MACR,IAAIA,GAAG,CAACC,OAAO,IAAID,GAAG,CAACE,UAAU,IAAI,CAAC,EAAE;QACtC,IAAI,CAACrK,YAAY,GAAGmK,GAAG,CAACC,OAAO;QAC/B,IAAI,CAAChF,KAAK,GAAG+E,GAAG,CAACC,OAAO,CAACW,SAAS,GAAG,KAAK,GAAG,IAAI;QACjD,IAAIZ,GAAG,CAACC,OAAO,CAACW,SAAS,EAAE;UACzB,IAAI,CAAC1J,aAAa,CAACqH,OAAO,CAAC3C,IAAI,IAAI,IAAI,CAAC9E,aAAa,CAAC8E,IAAI,CAAC,GAAG,KAAK,CAAC;UACpE,IAAI,CAAClE,kBAAkB,CAAC6G,OAAO,CAAC3C,IAAI,IAAI,IAAI,CAACtE,kBAAkB,CAACsE,IAAI,CAAC,GAAG,KAAK,CAAC;UAE9E,IAAI,CAACuE,kBAAkB,GAAGH,GAAG,CAACC,OAAO,CAACW,SAAS,CAAC3B,GAAG,CAAEmB,CAAM,IAAI;YAC7D,OAAO;cACLE,OAAO,EAAE,IAAI,CAACzK,YAAY,CAACyK,OAAO;cAClCnK,cAAc,EAAEiK,CAAC,CAACjK,cAAc;cAChCpC,WAAW,EAAEqM,CAAC,CAACrM,WAAW,KAAKqM,CAAC,CAACS,gBAAgB,GAAG,CAACT,CAAC,CAACS,gBAAgB,CAAC,GAAG,EAAE,CAAC;cAC9EhE,KAAK,EAAEuD,CAAC,CAACvD,KAAK;cACdwD,kBAAkB,EAAED,CAAC,CAACC,kBAAkB;cACxCS,WAAW,EAAEV,CAAC,CAACU,WAAW;cAC1BlH,SAAS,EAAEwG,CAAC,CAACxG,SAAS;cACtBF,KAAK,EAAE0G,CAAC,CAAC1G,KAAK;cACdC,KAAK,EAAEyG,CAAC,CAACzG,KAAK;cACdxB,SAAS,EAAEiI,CAAC,CAACjI,SAAS,GAAGiI,CAAC,CAACjI,SAAS,GAAG,GAAGiI,CAAC,CAAC1G,KAAK,IAAI0G,CAAC,CAACzG,KAAK,IAAIyG,CAAC,CAACxG,SAAS,EAAE;cAC7E8E,WAAW,EAAE0B,CAAC,CAAC1B,WAAW;cAC1BgB,YAAY,EAAEU,CAAC,CAACV,YAAY;cAC5BrH,cAAc,EAAE+H,CAAC,CAACG,OAAO,KAAK,CAAC,GAAG,CAAC,GAAGH,CAAC,CAAC/H,cAAc;cACtDkI,OAAO,EAAEH,CAAC,CAACG,OAAO;cAClBzJ,aAAa,EAAEsJ,CAAC,CAACW,qBAAqB,CAAC/M,MAAM,GAAG,IAAI,CAACgN,0BAA0B,CAAC,IAAI,CAAC9J,aAAa,EAAEkJ,CAAC,CAACW,qBAAqB,CAAC,GAAG;gBAAE,GAAG,IAAI,CAACjK;cAAa,CAAE;cAAEQ,kBAAkB,EAAE8I,CAAC,CAAC1B,WAAW,GAAG,IAAI,CAACD,kBAAkB,CAAC,IAAI,CAAC/G,kBAAkB,EAAE0I,CAAC,CAAC1B,WAAW,CAAC,GAAG;gBAAE,GAAG,IAAI,CAACpH;cAAkB,CAAE;cAC9Rd,WAAW,EAAE4J,CAAC,CAACW,qBAAqB,CAAC/M,MAAM,KAAK,IAAI,CAACkD,aAAa,CAAClD,MAAM;cACzEkC,YAAY,EAAE,EAAE;cAChBqC,eAAe,EAAE6H,CAAC,CAACG,OAAO,GAAG,IAAI,CAAC7E,cAAc,CAAC0E,CAAC,CAACG,OAAO,EAAE,IAAI,CAACvG,cAAc,CAAC,GAAG,IAAI,CAACA,cAAc,CAAC,CAAC,CAAC;cACzGlG,iBAAiB,EAAE;aACpB;UACH,CAAC,CAAC;QACJ,CAAC,MAAM;UACL,IAAI,CAAC6L,eAAe,EAAE;QACxB;MACF;IACF,CAAC,CAAC,CACH,CAACvE,SAAS,EAAE;EACf;EAEA3C,mBAAmBA,CAAC+E,WAAgB;IAClC,IAAIA,WAAW,CAACjF,eAAe,IAAIiF,WAAW,CAACjF,eAAe,CAACsB,KAAK,KAAK,CAAC,EAAE;MAC1E2D,WAAW,CAACnF,cAAc,GAAG,CAAC;IAChC;EACF;EACA4I,4BAA4BA,CAACvL,IAAW;IACtC,KAAK,IAAIkG,IAAI,IAAIlG,IAAI,EAAE;MACrB,IAAIkG,IAAI,CAACZ,WAAW,KAAK,IAAI,CAACF,iCAAiC,CAACE,WAAW,EAAE;QAC3E,OAAOY,IAAI,CAACsF,cAAc;MAC5B;IACF;IACA,OAAO,EAAE;EACX;EAIAC,oBAAoBA,CAACC,GAA4B;IAC/C,OAAOC,MAAM,CAACC,IAAI,CAACF,GAAG,CAAC,CAACpE,MAAM,CAACoB,GAAG,IAAIgD,GAAG,CAAChD,GAAG,CAAC,CAAC;EACjD;EAEAmD,0BAA0BA,CAACH,GAA4B;IACrD,OAAOC,MAAM,CAACC,IAAI,CAACF,GAAG,CAAC,CACpBpE,MAAM,CAACoB,GAAG,IAAIgD,GAAG,CAAChD,GAAG,CAAC,CAAC,CACvBoD,IAAI,CAAC,GAAG,CAAC;EACd;EAEAC,cAAcA,CAAClJ,eAAoB,EAAEjB,kBAAuB;IAC1D,IAAIiB,eAAe,IAAIA,eAAe,CAACsB,KAAK,IAAI,CAAC,EAAE;MACjD,OAAO,IAAI,CAAC0H,0BAA0B,CAACjK,kBAAkB,CAAC;IAC5D;EACF;EAEAoK,mBAAmBA,CAACC,WAAmB;IACrC,MAAMC,KAAK,GAAGD,WAAW,CAACjF,KAAK,CAAC,GAAG,CAAC;IACpC,IAAIkF,KAAK,CAAC5N,MAAM,GAAG,CAAC,EAAE;MACpB,OAAO4N,KAAK,CAAC,CAAC,CAAC;IACjB,CAAC,MAAM,OAAO,EAAE;EAClB;EAEAC,UAAUA,CAAC3L,YAAiB;IAC1B,IAAIA,YAAY,IAAIA,YAAY,CAAClC,MAAM,GAAG,CAAC,EAAE;MAC3C,OAAO;QACL8N,YAAY,EAAE,IAAI,CAACJ,mBAAmB,CAACxL,YAAY,CAAC,CAAC,CAAC,CAACR,IAAI,CAAC,IAAI,IAAI;QACpEqM,aAAa,EAAE7L,YAAY,CAAC,CAAC,CAAC,CAACyG,SAAS,IAAI,IAAI;QAChDqF,QAAQ,EAAE9L,YAAY,CAAC,CAAC,CAAC,CAAC2G,KAAK,CAAClH,IAAI,IAAIO,YAAY,CAAC,CAAC,CAAC,CAACP,IAAI,IAAI;OACjE;IACH,CAAC,MAAM,OAAOI,SAAS;EAEzB;EAGAkM,UAAUA,CAAA;IACR,IAAI,CAACvH,KAAK,CAACwH,KAAK,EAAE;IAClB,IAAIC,iBAAiB,GAAG,KAAK;IAC7B,IAAIC,wBAAwB,GAAG,KAAK;IACpC,IAAIC,kBAAkB,GAAG,KAAK;IAE9B,KAAK,MAAMzG,IAAI,IAAI,IAAI,CAAC0G,mBAAmB,EAAE;MAC3C,IAAI,CAACH,iBAAiB,IAAK,CAACvG,IAAI,CAAC2E,OAAQ,EAAE;QACzC4B,iBAAiB,GAAG,IAAI;MAC1B;MACA,IAAI,CAACC,wBAAwB,IAAK,CAACxG,IAAI,CAACvD,cAAe,EAAE;QACvD+J,wBAAwB,GAAG,IAAI;MACjC;MACA,IAAIxG,IAAI,CAAC8D,YAAY,IAAI9D,IAAI,CAACvD,cAAc,EAAE;QAC5C,IAAIuD,IAAI,CAACvD,cAAc,GAAGuD,IAAI,CAAC8D,YAAY,IAAI9D,IAAI,CAACvD,cAAc,GAAG,CAAC,EAAE;UACtE,IAAI,CAACqC,KAAK,CAAC6H,eAAe,CAAC,QAAQ,GAAG,MAAM,GAAG3G,IAAI,CAAC8D,YAAY,GAAG,KAAK9D,IAAI,CAACzD,SAAS,IAAI,CAAC;QAC7F;MACF;MAEA,IAAI,CAACkK,kBAAkB,IAAK,CAACzG,IAAI,CAACzD,SAAU,EAAE;QAC5CkK,kBAAkB,GAAG,IAAI;MAC3B;IACF;IACA,IAAIF,iBAAiB,EAAE;MACrB,IAAI,CAACzH,KAAK,CAAC6H,eAAe,CAAC,UAAU,GAAG,KAAK,CAAC;IAChD;IACA,IAAIH,wBAAwB,EAAE;MAC5B,IAAI,CAAC1H,KAAK,CAAC6H,eAAe,CAAC,QAAQ,GAAG,QAAQ,CAAC;IACjD;IACA,IAAIF,kBAAkB,EAAE;MACtB,IAAI,CAAC3H,KAAK,CAAC6H,eAAe,CAAC,YAAY,GAAG,KAAK,CAAC;IAClD;EACF;EAIAC,QAAQA,CAAA;IACN,IAAI,CAACF,mBAAmB,GAAG,IAAI,CAACnC,kBAAkB,CAAClB,GAAG,CAAEwD,CAAM,IAAI;MAChE,OAAO;QACLtM,cAAc,EAAEsM,CAAC,CAACtM,cAAc,GAAGsM,CAAC,CAACtM,cAAc,GAAG,IAAI;QAC1D0G,KAAK,EAAE4F,CAAC,CAACvM,YAAY,GAAG,IAAI,CAAC2L,UAAU,CAACY,CAAC,CAACvM,YAAY,CAAC,GAAGH,SAAS;QACnEsK,kBAAkB,EAAE,IAAI,CAACc,oBAAoB,CAACsB,CAAC,CAAC3L,aAAa,CAAC;QAC9DgK,WAAW,EAAE2B,CAAC,CAAC3B,WAAW,GAAG2B,CAAC,CAAC3B,WAAW,GAAG,IAAI;QACjD4B,OAAO,EAAE,IAAI,CAACzH,KAAK,GAAG,IAAI,GAAG,IAAI,CAACpF,YAAY,CAACyK,OAAO;QACtD5G,KAAK,EAAE+I,CAAC,CAAC/I,KAAK;QACdC,KAAK,EAAE8I,CAAC,CAAC9I,KAAK;QACdC,SAAS,EAAE6I,CAAC,CAAC7I,SAAS;QACtBzB,SAAS,EAAEsK,CAAC,CAACtK,SAAS;QAAE;QACxBuG,WAAW,EAAE+D,CAAC,CAAClK,eAAe,CAACsB,KAAK,KAAK,CAAC,GAAG,IAAI,GAAG,IAAI,CAAC4H,cAAc,CAACgB,CAAC,CAAClK,eAAe,EAAEkK,CAAC,CAACnL,kBAAkB,CAAC,IAAI,IAAI;QACxHoI,YAAY,EAAE+C,CAAC,CAAC/C,YAAY;QAC5BrH,cAAc,EAAEoK,CAAC,CAACpK,cAAc;QAChCkI,OAAO,EAAEkC,CAAC,CAAClK,eAAe,CAACsB;OAC5B;IACH,CAAC,CAAC;IACF,IAAI,CAACoI,UAAU,EAAE;IACjB,IAAI,IAAI,CAACvH,KAAK,CAACiI,aAAa,CAAC3O,MAAM,GAAG,CAAC,EAAE;MACvC,IAAI,CAACsG,OAAO,CAACsI,aAAa,CAAC,IAAI,CAAClI,KAAK,CAACiI,aAAa,CAAC;MACpD;IACF;IACA,IAAI,IAAI,CAAC1H,KAAK,EAAE;MACd,IAAI,CAAC4H,kBAAkB,EAAE;IAE3B,CAAC,MAAM;MACL,IAAI,CAACC,gBAAgB,EAAE;IACzB;EACF;EAEAA,gBAAgBA,CAAA;IACd,IAAI,CAACvI,gBAAgB,CAACwI,oCAAoC,CAAC;MACzDlF,IAAI,EAAE,IAAI,CAACyE;KACZ,CAAC,CAAClH,SAAS,CAAC4E,GAAG,IAAG;MACjB,IAAIA,GAAG,CAACE,UAAU,IAAI,CAAC,EAAE;QACvB,IAAI,CAAC5F,OAAO,CAAC0I,aAAa,CAAC,MAAM,CAAC;QAClC;QACA,IAAI,CAACC,MAAM,EAAE;MACf;IACF,CAAC,CAAC;EACJ;EAIAJ,kBAAkBA,CAAA;IAChB,IAAI,CAACK,iBAAiB,GAAG;MACvBrD,YAAY,EAAE,IAAI,CAACrE,WAAW;MAC9B2H,SAAS,EAAE,IAAI,CAACb,mBAAmB,IAAI,IAAI;MAC3CvH,SAAS,EAAE,IAAI,CAACD,iCAAiC,CAACC;KACnD;IAED,IAAI,CAACR,gBAAgB,CAAC6I,sCAAsC,CAAC;MAC3DvF,IAAI,EAAE,IAAI,CAACqF;KACZ,CAAC,CAAC9H,SAAS,CAAC4E,GAAG,IAAG;MACjB,IAAIA,GAAG,CAACE,UAAU,IAAI,CAAC,EAAE;QACvB,IAAI,CAAC5F,OAAO,CAAC0I,aAAa,CAAC,MAAM,CAAC;QAClC;QACA,IAAI,CAACC,MAAM,EAAE;MACf;IACF,CAAC,CAAC;EACJ;EAEAjC,0BAA0BA,CAACqC,CAAW,EAAEC,CAAkG;IACxI,MAAMC,CAAC,GAA+B,EAAE;IACxC,KAAK,MAAM3H,IAAI,IAAIyH,CAAC,EAAE;MACpB,MAAMG,YAAY,GAAGF,CAAC,CAACG,IAAI,CAACC,KAAK,IAAIA,KAAK,CAACC,UAAU,KAAK/H,IAAI,IAAI8H,KAAK,CAACE,SAAS,CAAC;MAClFL,CAAC,CAAC3H,IAAI,CAAC,GAAG,CAAC,CAAC4H,YAAY;IAC1B;IACA,OAAOD,CAAC;EACV,CAAC,CAAC;EAKF9H,iCAAiCA,CAAA;IAC/B,IAAI,CAACjB,yBAAyB,CAACqJ,8DAA8D,CAAC;MAC5FhG,IAAI,EAAE,IAAI,CAACrC;KACZ,CAAC,CAACuE,IAAI,CACL9N,GAAG,CAAC+N,GAAG,IAAG;MACR,IAAIA,GAAG,CAACC,OAAO,IAAID,GAAG,CAACE,UAAU,IAAI,CAAC,EAAE;QACtC,IAAI,CAAChJ,aAAa,GAAG,IAAI,CAAC+J,4BAA4B,CAACjB,GAAG,CAACC,OAAO,CAAC;QACnE,IAAI,CAACQ,eAAe,EAAE;MACxB;IACF,CAAC,CAAC,CACH,CAACrF,SAAS,EAAE;EACf;EACA6H,MAAMA,CAAA;IACJ,IAAI,CAACpI,aAAa,CAACiC,IAAI,CAAC;MACtBgH,MAAM;MACNC,OAAO,EAAE,IAAI,CAACvI;KACf,CAAC;IACF,IAAI,CAACb,QAAQ,CAACqJ,IAAI,EAAE;EACtB;;;uCArfW9J,4CAA4C,EAAA5H,EAAA,CAAA2R,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAA7R,EAAA,CAAA2R,iBAAA,CAAAG,EAAA,CAAAC,cAAA,GAAA/R,EAAA,CAAA2R,iBAAA,CAAAK,EAAA,CAAAC,cAAA,GAAAjS,EAAA,CAAA2R,iBAAA,CAAAO,EAAA,CAAAC,eAAA,GAAAnS,EAAA,CAAA2R,iBAAA,CAAAO,EAAA,CAAAE,wBAAA,GAAApS,EAAA,CAAA2R,iBAAA,CAAAU,EAAA,CAAAC,cAAA,GAAAtS,EAAA,CAAA2R,iBAAA,CAAAY,EAAA,CAAAC,gBAAA,GAAAxS,EAAA,CAAA2R,iBAAA,CAAAc,EAAA,CAAAC,QAAA,GAAA1S,EAAA,CAAA2R,iBAAA,CAAAO,EAAA,CAAAS,eAAA,GAAA3S,EAAA,CAAA2R,iBAAA,CAAAiB,EAAA,CAAAC,YAAA;IAAA;EAAA;;;YAA5CjL,4CAA4C;MAAAkL,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAAhT,EAAA,CAAAiT,0BAAA,EAAAjT,EAAA,CAAAkT,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,sDAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UChDvDxT,EADF,CAAAQ,cAAA,iBAA0B,qBACR;UACdR,EAAA,CAAAC,SAAA,qBAAiC;UACnCD,EAAA,CAAAiB,YAAA,EAAiB;UACjBjB,EAAA,CAAAQ,cAAA,sBAA2B;UACzBR,EAAA,CAAAC,SAAA,YAA+C;UAE7CD,EADF,CAAAQ,cAAA,aAAuB,YAC4B;UAAAR,EAAA,CAAAqB,MAAA,4CAAO;UAAArB,EAAA,CAAAiB,YAAA,EAAK;UAE7DjB,EAAA,CAAAiC,UAAA,IAAAyR,oEAAA,4BAA8E;UAyLlF1T,EADE,CAAAiB,YAAA,EAAM,EACO;UAEbjB,EADF,CAAAQ,cAAA,wBAAyF,kBACc;UAA9DR,EAAA,CAAAS,UAAA,mBAAAkT,+EAAA;YAAA,OAASF,GAAA,CAAA9C,MAAA,EAAQ;UAAA,EAAC;UACvD3Q,EAAA,CAAAqB,MAAA,sBACF;UAAArB,EAAA,CAAAiB,YAAA,EAAS;UACTjB,EAAA,CAAAQ,cAAA,kBAAgG;UAAhER,EAAA,CAAAS,UAAA,mBAAAmT,+EAAA;YAAA,OAASH,GAAA,CAAAvD,QAAA,EAAU;UAAA,EAAC;UAClDlQ,EAAA,CAAAqB,MAAA,sBACF;UAEJrB,EAFI,CAAAiB,YAAA,EAAS,EACM,EACT;;;;;UAlMkCjB,EAAA,CAAAsB,SAAA,GAAuB;UAAvBtB,EAAA,CAAAE,UAAA,YAAAuT,GAAA,CAAA5F,kBAAA,CAAuB;UA2LL7N,EAAA,CAAAsB,SAAA,GAA0C;UAA1CtB,EAAA,CAAAE,UAAA,cAAA2T,OAAA,GAAAJ,GAAA,CAAAlQ,YAAA,CAAAC,OAAA,cAAAqQ,OAAA,KAAApQ,SAAA,GAAAoQ,OAAA,SAA0C;UAG/C7T,EAAA,CAAAsB,SAAA,GAA0C;UAA1CtB,EAAA,CAAAE,UAAA,cAAA4T,OAAA,GAAAL,GAAA,CAAAlQ,YAAA,CAAAC,OAAA,cAAAsQ,OAAA,KAAArQ,SAAA,GAAAqQ,OAAA,SAA0C;;;qBDzJvFrU,YAAY,EAAAgT,EAAA,CAAAsB,OAAA,EAAAtB,EAAA,CAAAuB,IAAA,EAAEpU,YAAY,EAAAqU,EAAA,CAAAC,oBAAA,EAAAD,EAAA,CAAAE,mBAAA,EAAAF,EAAA,CAAAG,eAAA,EAAAH,EAAA,CAAAI,OAAA,EAAAC,GAAA,CAAAC,eAAA,EAAAD,GAAA,CAAAE,mBAAA,EAAAF,GAAA,CAAAG,qBAAA,EAAAH,GAAA,CAAAI,qBAAA,EAAAJ,GAAA,CAAAK,mBAAA,EAAAL,GAAA,CAAAM,gBAAA,EAAAN,GAAA,CAAAO,iBAAA,EAAAP,GAAA,CAAAQ,iBAAA,EAAAC,GAAA,CAAAC,mBAAA,EAAAC,GAAA,CAAAC,YAAA,EAAExV,gBAAgB,EAAgBK,eAAe;MAAAoV,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}