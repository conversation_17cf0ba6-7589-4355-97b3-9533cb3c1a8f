{"ast": null, "code": "//! moment.js locale configuration\n//! locale : Tamil [ta]\n//! author : <PERSON><PERSON><PERSON><PERSON> : https://github.com/tk120404\n\n;\n(function (global, factory) {\n  typeof exports === 'object' && typeof module !== 'undefined' && typeof require === 'function' ? factory(require('../moment')) : typeof define === 'function' && define.amd ? define(['../moment'], factory) : factory(global.moment);\n})(this, function (moment) {\n  'use strict';\n\n  //! moment.js locale configuration\n  var symbolMap = {\n      1: '௧',\n      2: '௨',\n      3: '௩',\n      4: '௪',\n      5: '௫',\n      6: '௬',\n      7: '௭',\n      8: '௮',\n      9: '௯',\n      0: '௦'\n    },\n    numberMap = {\n      '௧': '1',\n      '௨': '2',\n      '௩': '3',\n      '௪': '4',\n      '௫': '5',\n      '௬': '6',\n      '௭': '7',\n      '௮': '8',\n      '௯': '9',\n      '௦': '0'\n    };\n  var ta = moment.define<PERSON>ocale('ta', {\n    months: 'ஜனவரி_பிப்ரவரி_மார்ச்_ஏப்ரல்_மே_ஜூன்_ஜூலை_ஆகஸ்ட்_செப்டெம்பர்_அக்டோபர்_நவம்பர்_டிசம்பர்'.split('_'),\n    monthsShort: 'ஜனவரி_பிப்ரவரி_மார்ச்_ஏப்ரல்_மே_ஜூன்_ஜூலை_ஆகஸ்ட்_செப்டெம்பர்_அக்டோபர்_நவம்பர்_டிசம்பர்'.split('_'),\n    weekdays: 'ஞாயிற்றுக்கிழமை_திங்கட்கிழமை_செவ்வாய்கிழமை_புதன்கிழமை_வியாழக்கிழமை_வெள்ளிக்கிழமை_சனிக்கிழமை'.split('_'),\n    weekdaysShort: 'ஞாயிறு_திங்கள்_செவ்வாய்_புதன்_வியாழன்_வெள்ளி_சனி'.split('_'),\n    weekdaysMin: 'ஞா_தி_செ_பு_வி_வெ_ச'.split('_'),\n    longDateFormat: {\n      LT: 'HH:mm',\n      LTS: 'HH:mm:ss',\n      L: 'DD/MM/YYYY',\n      LL: 'D MMMM YYYY',\n      LLL: 'D MMMM YYYY, HH:mm',\n      LLLL: 'dddd, D MMMM YYYY, HH:mm'\n    },\n    calendar: {\n      sameDay: '[இன்று] LT',\n      nextDay: '[நாளை] LT',\n      nextWeek: 'dddd, LT',\n      lastDay: '[நேற்று] LT',\n      lastWeek: '[கடந்த வாரம்] dddd, LT',\n      sameElse: 'L'\n    },\n    relativeTime: {\n      future: '%s இல்',\n      past: '%s முன்',\n      s: 'ஒரு சில விநாடிகள்',\n      ss: '%d விநாடிகள்',\n      m: 'ஒரு நிமிடம்',\n      mm: '%d நிமிடங்கள்',\n      h: 'ஒரு மணி நேரம்',\n      hh: '%d மணி நேரம்',\n      d: 'ஒரு நாள்',\n      dd: '%d நாட்கள்',\n      M: 'ஒரு மாதம்',\n      MM: '%d மாதங்கள்',\n      y: 'ஒரு வருடம்',\n      yy: '%d ஆண்டுகள்'\n    },\n    dayOfMonthOrdinalParse: /\\d{1,2}வது/,\n    ordinal: function (number) {\n      return number + 'வது';\n    },\n    preparse: function (string) {\n      return string.replace(/[௧௨௩௪௫௬௭௮௯௦]/g, function (match) {\n        return numberMap[match];\n      });\n    },\n    postformat: function (string) {\n      return string.replace(/\\d/g, function (match) {\n        return symbolMap[match];\n      });\n    },\n    // refer http://ta.wikipedia.org/s/1er1\n    meridiemParse: /யாமம்|வைகறை|காலை|நண்பகல்|எற்பாடு|மாலை/,\n    meridiem: function (hour, minute, isLower) {\n      if (hour < 2) {\n        return ' யாமம்';\n      } else if (hour < 6) {\n        return ' வைகறை'; // வைகறை\n      } else if (hour < 10) {\n        return ' காலை'; // காலை\n      } else if (hour < 14) {\n        return ' நண்பகல்'; // நண்பகல்\n      } else if (hour < 18) {\n        return ' எற்பாடு'; // எற்பாடு\n      } else if (hour < 22) {\n        return ' மாலை'; // மாலை\n      } else {\n        return ' யாமம்';\n      }\n    },\n    meridiemHour: function (hour, meridiem) {\n      if (hour === 12) {\n        hour = 0;\n      }\n      if (meridiem === 'யாமம்') {\n        return hour < 2 ? hour : hour + 12;\n      } else if (meridiem === 'வைகறை' || meridiem === 'காலை') {\n        return hour;\n      } else if (meridiem === 'நண்பகல்') {\n        return hour >= 10 ? hour : hour + 12;\n      } else {\n        return hour + 12;\n      }\n    },\n    week: {\n      dow: 0,\n      // Sunday is the first day of the week.\n      doy: 6 // The week that contains Jan 6th is the first week of the year.\n    }\n  });\n  return ta;\n});", "map": {"version": 3, "names": ["global", "factory", "exports", "module", "require", "define", "amd", "moment", "symbolMap", "numberMap", "ta", "defineLocale", "months", "split", "monthsShort", "weekdays", "weekdaysShort", "weekdaysMin", "longDateFormat", "LT", "LTS", "L", "LL", "LLL", "LLLL", "calendar", "sameDay", "nextDay", "nextWeek", "lastDay", "lastWeek", "same<PERSON><PERSON><PERSON>", "relativeTime", "future", "past", "s", "ss", "m", "mm", "h", "hh", "d", "dd", "M", "MM", "y", "yy", "dayOfMonthOrdinalParse", "ordinal", "number", "preparse", "string", "replace", "match", "postformat", "meridiemParse", "meridiem", "hour", "minute", "isLower", "meridiemHour", "week", "dow", "doy"], "sources": ["C:/Users/<USER>/Documents/jeansalechange/adminFront/node_modules/moment/locale/ta.js"], "sourcesContent": ["//! moment.js locale configuration\n//! locale : Tamil [ta]\n//! author : <PERSON><PERSON><PERSON><PERSON> : https://github.com/tk120404\n\n;(function (global, factory) {\n   typeof exports === 'object' && typeof module !== 'undefined'\n       && typeof require === 'function' ? factory(require('../moment')) :\n   typeof define === 'function' && define.amd ? define(['../moment'], factory) :\n   factory(global.moment)\n}(this, (function (moment) { 'use strict';\n\n    //! moment.js locale configuration\n\n    var symbolMap = {\n            1: '௧',\n            2: '௨',\n            3: '௩',\n            4: '௪',\n            5: '௫',\n            6: '௬',\n            7: '௭',\n            8: '௮',\n            9: '௯',\n            0: '௦',\n        },\n        numberMap = {\n            '௧': '1',\n            '௨': '2',\n            '௩': '3',\n            '௪': '4',\n            '௫': '5',\n            '௬': '6',\n            '௭': '7',\n            '௮': '8',\n            '௯': '9',\n            '௦': '0',\n        };\n\n    var ta = moment.defineLocale('ta', {\n        months: 'ஜனவரி_பிப்ரவரி_மார்ச்_ஏப்ரல்_மே_ஜூன்_ஜூலை_ஆகஸ்ட்_செப்டெம்பர்_அக்டோபர்_நவம்பர்_டிசம்பர்'.split(\n            '_'\n        ),\n        monthsShort:\n            'ஜனவரி_பிப்ரவரி_மார்ச்_ஏப்ரல்_மே_ஜூன்_ஜூலை_ஆகஸ்ட்_செப்டெம்பர்_அக்டோபர்_நவம்பர்_டிசம்பர்'.split(\n                '_'\n            ),\n        weekdays:\n            'ஞாயிற்றுக்கிழமை_திங்கட்கிழமை_செவ்வாய்கிழமை_புதன்கிழமை_வியாழக்கிழமை_வெள்ளிக்கிழமை_சனிக்கிழமை'.split(\n                '_'\n            ),\n        weekdaysShort: 'ஞாயிறு_திங்கள்_செவ்வாய்_புதன்_வியாழன்_வெள்ளி_சனி'.split(\n            '_'\n        ),\n        weekdaysMin: 'ஞா_தி_செ_பு_வி_வெ_ச'.split('_'),\n        longDateFormat: {\n            LT: 'HH:mm',\n            LTS: 'HH:mm:ss',\n            L: 'DD/MM/YYYY',\n            LL: 'D MMMM YYYY',\n            LLL: 'D MMMM YYYY, HH:mm',\n            LLLL: 'dddd, D MMMM YYYY, HH:mm',\n        },\n        calendar: {\n            sameDay: '[இன்று] LT',\n            nextDay: '[நாளை] LT',\n            nextWeek: 'dddd, LT',\n            lastDay: '[நேற்று] LT',\n            lastWeek: '[கடந்த வாரம்] dddd, LT',\n            sameElse: 'L',\n        },\n        relativeTime: {\n            future: '%s இல்',\n            past: '%s முன்',\n            s: 'ஒரு சில விநாடிகள்',\n            ss: '%d விநாடிகள்',\n            m: 'ஒரு நிமிடம்',\n            mm: '%d நிமிடங்கள்',\n            h: 'ஒரு மணி நேரம்',\n            hh: '%d மணி நேரம்',\n            d: 'ஒரு நாள்',\n            dd: '%d நாட்கள்',\n            M: 'ஒரு மாதம்',\n            MM: '%d மாதங்கள்',\n            y: 'ஒரு வருடம்',\n            yy: '%d ஆண்டுகள்',\n        },\n        dayOfMonthOrdinalParse: /\\d{1,2}வது/,\n        ordinal: function (number) {\n            return number + 'வது';\n        },\n        preparse: function (string) {\n            return string.replace(/[௧௨௩௪௫௬௭௮௯௦]/g, function (match) {\n                return numberMap[match];\n            });\n        },\n        postformat: function (string) {\n            return string.replace(/\\d/g, function (match) {\n                return symbolMap[match];\n            });\n        },\n        // refer http://ta.wikipedia.org/s/1er1\n        meridiemParse: /யாமம்|வைகறை|காலை|நண்பகல்|எற்பாடு|மாலை/,\n        meridiem: function (hour, minute, isLower) {\n            if (hour < 2) {\n                return ' யாமம்';\n            } else if (hour < 6) {\n                return ' வைகறை'; // வைகறை\n            } else if (hour < 10) {\n                return ' காலை'; // காலை\n            } else if (hour < 14) {\n                return ' நண்பகல்'; // நண்பகல்\n            } else if (hour < 18) {\n                return ' எற்பாடு'; // எற்பாடு\n            } else if (hour < 22) {\n                return ' மாலை'; // மாலை\n            } else {\n                return ' யாமம்';\n            }\n        },\n        meridiemHour: function (hour, meridiem) {\n            if (hour === 12) {\n                hour = 0;\n            }\n            if (meridiem === 'யாமம்') {\n                return hour < 2 ? hour : hour + 12;\n            } else if (meridiem === 'வைகறை' || meridiem === 'காலை') {\n                return hour;\n            } else if (meridiem === 'நண்பகல்') {\n                return hour >= 10 ? hour : hour + 12;\n            } else {\n                return hour + 12;\n            }\n        },\n        week: {\n            dow: 0, // Sunday is the first day of the week.\n            doy: 6, // The week that contains Jan 6th is the first week of the year.\n        },\n    });\n\n    return ta;\n\n})));\n"], "mappings": "AAAA;AACA;AACA;;AAEA;AAAE,WAAUA,MAAM,EAAEC,OAAO,EAAE;EAC1B,OAAOC,OAAO,KAAK,QAAQ,IAAI,OAAOC,MAAM,KAAK,WAAW,IACrD,OAAOC,OAAO,KAAK,UAAU,GAAGH,OAAO,CAACG,OAAO,CAAC,WAAW,CAAC,CAAC,GACpE,OAAOC,MAAM,KAAK,UAAU,IAAIA,MAAM,CAACC,GAAG,GAAGD,MAAM,CAAC,CAAC,WAAW,CAAC,EAAEJ,OAAO,CAAC,GAC3EA,OAAO,CAACD,MAAM,CAACO,MAAM,CAAC;AACzB,CAAC,EAAC,IAAI,EAAG,UAAUA,MAAM,EAAE;EAAE,YAAY;;EAErC;EAEA,IAAIC,SAAS,GAAG;MACR,CAAC,EAAE,GAAG;MACN,CAAC,EAAE,GAAG;MACN,CAAC,EAAE,GAAG;MACN,CAAC,EAAE,GAAG;MACN,CAAC,EAAE,GAAG;MACN,CAAC,EAAE,GAAG;MACN,CAAC,EAAE,GAAG;MACN,CAAC,EAAE,GAAG;MACN,CAAC,EAAE,GAAG;MACN,CAAC,EAAE;IACP,CAAC;IACDC,SAAS,GAAG;MACR,GAAG,EAAE,GAAG;MACR,GAAG,EAAE,GAAG;MACR,GAAG,EAAE,GAAG;MACR,GAAG,EAAE,GAAG;MACR,GAAG,EAAE,GAAG;MACR,GAAG,EAAE,GAAG;MACR,GAAG,EAAE,GAAG;MACR,GAAG,EAAE,GAAG;MACR,GAAG,EAAE,GAAG;MACR,GAAG,EAAE;IACT,CAAC;EAEL,IAAIC,EAAE,GAAGH,MAAM,CAACI,YAAY,CAAC,IAAI,EAAE;IAC/BC,MAAM,EAAE,yFAAyF,CAACC,KAAK,CACnG,GACJ,CAAC;IACDC,WAAW,EACP,yFAAyF,CAACD,KAAK,CAC3F,GACJ,CAAC;IACLE,QAAQ,EACJ,6FAA6F,CAACF,KAAK,CAC/F,GACJ,CAAC;IACLG,aAAa,EAAE,kDAAkD,CAACH,KAAK,CACnE,GACJ,CAAC;IACDI,WAAW,EAAE,qBAAqB,CAACJ,KAAK,CAAC,GAAG,CAAC;IAC7CK,cAAc,EAAE;MACZC,EAAE,EAAE,OAAO;MACXC,GAAG,EAAE,UAAU;MACfC,CAAC,EAAE,YAAY;MACfC,EAAE,EAAE,aAAa;MACjBC,GAAG,EAAE,oBAAoB;MACzBC,IAAI,EAAE;IACV,CAAC;IACDC,QAAQ,EAAE;MACNC,OAAO,EAAE,YAAY;MACrBC,OAAO,EAAE,WAAW;MACpBC,QAAQ,EAAE,UAAU;MACpBC,OAAO,EAAE,aAAa;MACtBC,QAAQ,EAAE,wBAAwB;MAClCC,QAAQ,EAAE;IACd,CAAC;IACDC,YAAY,EAAE;MACVC,MAAM,EAAE,QAAQ;MAChBC,IAAI,EAAE,SAAS;MACfC,CAAC,EAAE,mBAAmB;MACtBC,EAAE,EAAE,cAAc;MAClBC,CAAC,EAAE,aAAa;MAChBC,EAAE,EAAE,eAAe;MACnBC,CAAC,EAAE,eAAe;MAClBC,EAAE,EAAE,cAAc;MAClBC,CAAC,EAAE,UAAU;MACbC,EAAE,EAAE,YAAY;MAChBC,CAAC,EAAE,WAAW;MACdC,EAAE,EAAE,aAAa;MACjBC,CAAC,EAAE,YAAY;MACfC,EAAE,EAAE;IACR,CAAC;IACDC,sBAAsB,EAAE,YAAY;IACpCC,OAAO,EAAE,SAAAA,CAAUC,MAAM,EAAE;MACvB,OAAOA,MAAM,GAAG,KAAK;IACzB,CAAC;IACDC,QAAQ,EAAE,SAAAA,CAAUC,MAAM,EAAE;MACxB,OAAOA,MAAM,CAACC,OAAO,CAAC,eAAe,EAAE,UAAUC,KAAK,EAAE;QACpD,OAAO5C,SAAS,CAAC4C,KAAK,CAAC;MAC3B,CAAC,CAAC;IACN,CAAC;IACDC,UAAU,EAAE,SAAAA,CAAUH,MAAM,EAAE;MAC1B,OAAOA,MAAM,CAACC,OAAO,CAAC,KAAK,EAAE,UAAUC,KAAK,EAAE;QAC1C,OAAO7C,SAAS,CAAC6C,KAAK,CAAC;MAC3B,CAAC,CAAC;IACN,CAAC;IACD;IACAE,aAAa,EAAE,uCAAuC;IACtDC,QAAQ,EAAE,SAAAA,CAAUC,IAAI,EAAEC,MAAM,EAAEC,OAAO,EAAE;MACvC,IAAIF,IAAI,GAAG,CAAC,EAAE;QACV,OAAO,QAAQ;MACnB,CAAC,MAAM,IAAIA,IAAI,GAAG,CAAC,EAAE;QACjB,OAAO,QAAQ,CAAC,CAAC;MACrB,CAAC,MAAM,IAAIA,IAAI,GAAG,EAAE,EAAE;QAClB,OAAO,OAAO,CAAC,CAAC;MACpB,CAAC,MAAM,IAAIA,IAAI,GAAG,EAAE,EAAE;QAClB,OAAO,UAAU,CAAC,CAAC;MACvB,CAAC,MAAM,IAAIA,IAAI,GAAG,EAAE,EAAE;QAClB,OAAO,UAAU,CAAC,CAAC;MACvB,CAAC,MAAM,IAAIA,IAAI,GAAG,EAAE,EAAE;QAClB,OAAO,OAAO,CAAC,CAAC;MACpB,CAAC,MAAM;QACH,OAAO,QAAQ;MACnB;IACJ,CAAC;IACDG,YAAY,EAAE,SAAAA,CAAUH,IAAI,EAAED,QAAQ,EAAE;MACpC,IAAIC,IAAI,KAAK,EAAE,EAAE;QACbA,IAAI,GAAG,CAAC;MACZ;MACA,IAAID,QAAQ,KAAK,OAAO,EAAE;QACtB,OAAOC,IAAI,GAAG,CAAC,GAAGA,IAAI,GAAGA,IAAI,GAAG,EAAE;MACtC,CAAC,MAAM,IAAID,QAAQ,KAAK,OAAO,IAAIA,QAAQ,KAAK,MAAM,EAAE;QACpD,OAAOC,IAAI;MACf,CAAC,MAAM,IAAID,QAAQ,KAAK,SAAS,EAAE;QAC/B,OAAOC,IAAI,IAAI,EAAE,GAAGA,IAAI,GAAGA,IAAI,GAAG,EAAE;MACxC,CAAC,MAAM;QACH,OAAOA,IAAI,GAAG,EAAE;MACpB;IACJ,CAAC;IACDI,IAAI,EAAE;MACFC,GAAG,EAAE,CAAC;MAAE;MACRC,GAAG,EAAE,CAAC,CAAE;IACZ;EACJ,CAAC,CAAC;EAEF,OAAOrD,EAAE;AAEb,CAAE,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}