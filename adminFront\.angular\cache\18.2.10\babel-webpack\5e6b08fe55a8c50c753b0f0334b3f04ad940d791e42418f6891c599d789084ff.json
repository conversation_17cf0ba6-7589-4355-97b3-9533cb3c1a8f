{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { BaseComponent } from '../../components/base/baseComponent';\nimport { EnumFileType } from 'src/app/shared/enum/enumFileType';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/shared/helper/allowHelper\";\nimport * as i2 from \"src/app/shared/services/message.service\";\nimport * as i3 from \"@angular/common\";\nconst _c0 = [\"fileInput\"];\nfunction FileUploadComponent_h3_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"h3\", 10);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r1.config.helpText);\n  }\n}\nfunction FileUploadComponent_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 11)(1, \"span\", 12);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"button\", 13);\n    i0.ɵɵlistener(\"click\", function FileUploadComponent_div_12_Template_button_click_3_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.clearFile());\n    });\n    i0.ɵɵelement(4, \"i\", 14);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.fileName);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r1.config.disabled);\n  }\n}\nfunction FileUploadComponent_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 11)(1, \"span\", 15)(2, \"a\", 16);\n    i0.ɵɵlistener(\"click\", function FileUploadComponent_div_13_Template_a_click_2_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.openFile(ctx_r1.currentFileUrl));\n    });\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.currentFileUrl, \" \");\n  }\n}\nexport class FileUploadComponent extends BaseComponent {\n  constructor(_allow, message) {\n    super(_allow);\n    this._allow = _allow;\n    this.message = message;\n    this.config = {\n      acceptedTypes: ['image/jpeg', 'image/jpg', 'application/pdf', '.dwg', '.dxf'],\n      acceptedFileRegex: /pdf|jpg|jpeg|png|dwg|dxf/i,\n      acceptAttribute: 'image/jpeg, image/jpg, application/pdf, .dwg, .dxf',\n      label: '上傳檔案',\n      helpText: '支援格式：圖片 (JPG, JPEG)、PDF、CAD (DWG, DXF)',\n      required: false,\n      disabled: false,\n      autoFillName: false,\n      buttonText: '選擇檔案',\n      buttonIcon: 'fa fa-upload',\n      maxFileSize: 10,\n      multiple: true,\n      visualMode: true,\n      isViewMode: false\n    };\n    this.currentFileName = null;\n    this.currentFileUrl = null;\n    this.labelMinWidth = '75px';\n    this.fileList = [];\n    this.existingFiles = [];\n    this.fileSelected = new EventEmitter();\n    this.fileCleared = new EventEmitter();\n    this.nameAutoFilled = new EventEmitter();\n    this.fileListChange = new EventEmitter();\n    this.filesSelected = new EventEmitter();\n    this.fileRemoved = new EventEmitter();\n    this.fileClicked = new EventEmitter();\n    this.fileName = null;\n  }\n  ngOnInit() {\n    this.fileName = this.currentFileName;\n  }\n  onFileButtonClick() {\n    if (!this.config.disabled) {\n      this.fileInput.nativeElement.click();\n    }\n  }\n  onFileSelected(event) {\n    if (this.config.visualMode && this.config.multiple) {\n      this.handleMultipleFiles(event);\n    } else {\n      this.handleSingleFile(event);\n    }\n  }\n  handleMultipleFiles(event) {\n    const files = event.target.files;\n    if (files && files.length > 0) {\n      const newFiles = [];\n      if (this.config.autoFillName && files.length > 0) {\n        const firstFile = files[0];\n        const fileNameWithoutExtension = firstFile.name.substring(0, firstFile.name.lastIndexOf('.')) || firstFile.name;\n        this.nameAutoFilled.emit(fileNameWithoutExtension);\n      }\n      for (let i = 0; i < files.length; i++) {\n        const file = files[i];\n        if (!this.config.acceptedFileRegex.test(file.name)) {\n          this.message.showErrorMSG('檔案格式錯誤，僅限pdf、圖片檔、CAD檔案（dwg, dxf）');\n          continue;\n        }\n        const maxSizeInBytes = (this.config.maxFileSize || 10) * 1024 * 1024;\n        if (file.size > maxSizeInBytes) {\n          this.message.showErrorMSG(`檔案大小超過限制（${this.config.maxFileSize}MB）`);\n          continue;\n        }\n        const reader = new FileReader();\n        reader.onload = e => {\n          let fileType = 1;\n          const fileName = file.name.toLowerCase();\n          if (fileName.match(/\\.(jpg|jpeg|png)$/)) {\n            fileType = 2;\n          } else if (fileName.endsWith('.pdf')) {\n            fileType = 1;\n          } else if (fileName.match(/\\.(dwg|dxf)$/)) {\n            fileType = 3;\n          }\n          const fileItem = {\n            data: e.target.result,\n            CFileBlood: this.removeBase64Prefix(e.target.result),\n            CFileName: file.name,\n            CFileType: fileType\n          };\n          newFiles.push(fileItem);\n          if (newFiles.length === files.length) {\n            this.fileList = [...this.fileList, ...newFiles];\n            this.fileListChange.emit(this.fileList);\n            this.filesSelected.emit(newFiles);\n            if (this.fileInput) {\n              this.fileInput.nativeElement.value = null;\n            }\n          }\n        };\n        reader.readAsDataURL(file);\n      }\n    }\n  }\n  handleSingleFile(event) {\n    const file = event.target.files[0];\n    if (!file) {\n      return;\n    }\n    if (!this.config.acceptedFileRegex.test(file.name)) {\n      this.message.showErrorMSG('檔案格式錯誤');\n      return;\n    }\n    const maxSizeInBytes = (this.config.maxFileSize || 10) * 1024 * 1024;\n    if (file.size > maxSizeInBytes) {\n      this.message.showErrorMSG(`檔案大小超過限制（${this.config.maxFileSize}MB）`);\n      return;\n    }\n    this.fileName = file.name;\n    if (this.config.autoFillName) {\n      const fileNameWithoutExtension = file.name.substring(0, file.name.lastIndexOf('.')) || file.name;\n      this.nameAutoFilled.emit(fileNameWithoutExtension);\n    }\n    const reader = new FileReader();\n    reader.onload = e => {\n      let fileType;\n      if (file.type.startsWith('image/')) {\n        fileType = EnumFileType.JPG;\n      } else if (file.name.toLowerCase().includes('.dwg') || file.name.toLowerCase().includes('.dxf')) {\n        fileType = 3;\n      } else {\n        fileType = EnumFileType.PDF;\n      }\n      const result = {\n        CName: file.name,\n        CFile: e.target?.result?.toString().split(',')[1],\n        Cimg: file,\n        CFileUpload: file,\n        CFileType: fileType,\n        fileName: file.name\n      };\n      this.fileSelected.emit(result);\n      if (this.fileInput) {\n        this.fileInput.nativeElement.value = null;\n      }\n    };\n    reader.readAsDataURL(file);\n  }\n  removeBase64Prefix(base64String) {\n    const prefixIndex = base64String.indexOf(',');\n    if (prefixIndex !== -1) {\n      return base64String.substring(prefixIndex + 1);\n    }\n    return base64String;\n  }\n  removeFile(index) {\n    this.fileList.splice(index, 1);\n    this.fileListChange.emit(this.fileList);\n    this.fileRemoved.emit(index);\n  }\n  onFileClick(fileUrl) {\n    this.fileClicked.emit(fileUrl);\n  }\n  clearFile() {\n    this.fileName = null;\n    this.fileCleared.emit();\n    if (this.fileInput) {\n      this.fileInput.nativeElement.value = null;\n    }\n  }\n  openFile(url) {\n    if (url) {\n      window.open(url, '_blank');\n    }\n  }\n  isImage(fileType) {\n    return fileType === 2;\n  }\n  isCad(fileType) {\n    return fileType === 3;\n  }\n  isPDFString(str) {\n    if (str) {\n      return str.toLowerCase().endsWith(\".pdf\");\n    }\n    return false;\n  }\n  isCadString(str) {\n    if (str) {\n      const lowerStr = str.toLowerCase();\n      return lowerStr.endsWith(\".dwg\") || lowerStr.endsWith(\".dxf\");\n    }\n    return false;\n  }\n  static {\n    this.ɵfac = function FileUploadComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || FileUploadComponent)(i0.ɵɵdirectiveInject(i1.AllowHelper), i0.ɵɵdirectiveInject(i2.MessageService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: FileUploadComponent,\n      selectors: [[\"app-file-upload\"]],\n      viewQuery: function FileUploadComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.fileInput = _t.first);\n        }\n      },\n      inputs: {\n        config: \"config\",\n        currentFileName: \"currentFileName\",\n        currentFileUrl: \"currentFileUrl\",\n        labelMinWidth: \"labelMinWidth\",\n        fileList: \"fileList\",\n        existingFiles: \"existingFiles\"\n      },\n      outputs: {\n        fileSelected: \"fileSelected\",\n        fileCleared: \"fileCleared\",\n        nameAutoFilled: \"nameAutoFilled\",\n        fileListChange: \"fileListChange\",\n        filesSelected: \"filesSelected\",\n        fileRemoved: \"fileRemoved\",\n        fileClicked: \"fileClicked\"\n      },\n      standalone: true,\n      features: [i0.ɵɵInheritDefinitionFeature, i0.ɵɵStandaloneFeature],\n      decls: 14,\n      vars: 17,\n      consts: [[\"fileInput\", \"\"], [1, \"form-group\", \"d-flex\", \"align-items-center\"], [1, \"form-group\", \"d-flex\", \"align-items-center\", \"w-full\"], [1, \"d-flex\", \"flex-col\", \"mr-3\"], [\"for\", \"file\", 1, \"label\", \"col-3\"], [\"style\", \"color:red;\", 4, \"ngIf\"], [1, \"flex\", \"flex-col\", \"col-9\", \"px-0\", \"items-start\"], [\"type\", \"file\", \"id\", \"fileInput\", 1, \"hidden\", 2, \"display\", \"none\", 3, \"change\", \"accept\", \"disabled\"], [\"for\", \"fileInput\", 1, \"bg-blue-500\", \"hover:bg-blue-700\", \"text-white\", \"font-bold\", \"py-2\", \"px-4\", \"rounded\"], [\"class\", \"flex items-center space-x-2 mt-2\", 4, \"ngIf\"], [2, \"color\", \"red\"], [1, \"flex\", \"items-center\", \"space-x-2\", \"mt-2\"], [1, \"text-gray-600\"], [\"type\", \"button\", 1, \"text-red-500\", \"hover:text-red-700\", 3, \"click\", \"disabled\"], [1, \"fa-solid\", \"fa-trash\"], [1, \"text-sm\"], [1, \"cursor-pointer\", \"text-blue-500\", 3, \"click\"]],\n      template: function FileUploadComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"div\", 1)(1, \"div\", 2)(2, \"div\", 3)(3, \"label\", 4);\n          i0.ɵɵtext(4);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(5, FileUploadComponent_h3_5_Template, 2, 1, \"h3\", 5);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(6, \"div\", 6)(7, \"input\", 7, 0);\n          i0.ɵɵlistener(\"change\", function FileUploadComponent_Template_input_change_7_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onFileSelected($event));\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(9, \"label\", 8);\n          i0.ɵɵelement(10, \"i\");\n          i0.ɵɵtext(11);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(12, FileUploadComponent_div_12_Template, 5, 2, \"div\", 9)(13, FileUploadComponent_div_13_Template, 4, 1, \"div\", 9);\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(3);\n          i0.ɵɵstyleProp(\"min-width\", ctx.labelMinWidth);\n          i0.ɵɵclassProp(\"required-field\", ctx.config.required);\n          i0.ɵɵadvance();\n          i0.ɵɵtextInterpolate1(\" \", ctx.config.label, \" \");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.config.helpText);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"accept\", ctx.config.acceptAttribute)(\"disabled\", ctx.config.disabled);\n          i0.ɵɵadvance(2);\n          i0.ɵɵclassProp(\"opacity-50\", ctx.config.disabled)(\"cursor-pointer\", !ctx.config.disabled);\n          i0.ɵɵadvance();\n          i0.ɵɵclassMap(ctx.config.buttonIcon + \" mr-2\");\n          i0.ɵɵadvance();\n          i0.ɵɵtextInterpolate1(\" \", ctx.config.buttonText, \" \");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.fileName);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.currentFileUrl && !ctx.fileName);\n        }\n      },\n      dependencies: [CommonModule, i3.NgIf],\n      styles: [\"\\n (()[_ngcontent-%COMP%]   =[_ngcontent-%COMP%] >  { // webpackBootstrap\\n\\n \\t\\\"use strict\\\";\\n\\n \\t\\n\\n \\t\\n\\n })()[_ngcontent-%COMP%]\\n;\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["EventEmitter", "CommonModule", "BaseComponent", "EnumFileType", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate", "ctx_r1", "config", "helpText", "ɵɵlistener", "FileUploadComponent_div_12_Template_button_click_3_listener", "ɵɵrestoreView", "_r3", "ɵɵnextContext", "ɵɵresetView", "clearFile", "ɵɵelement", "fileName", "ɵɵproperty", "disabled", "FileUploadComponent_div_13_Template_a_click_2_listener", "_r4", "openFile", "currentFileUrl", "ɵɵtextInterpolate1", "FileUploadComponent", "constructor", "_allow", "message", "acceptedTypes", "acceptedFileRegex", "acceptAttribute", "label", "required", "autoFillName", "buttonText", "buttonIcon", "maxFileSize", "multiple", "visualMode", "isViewMode", "currentFileName", "labelMinWidth", "fileList", "existingFiles", "fileSelected", "fileCleared", "nameAutoFilled", "fileListChange", "filesSelected", "fileRemoved", "fileClicked", "ngOnInit", "onFileButtonClick", "fileInput", "nativeElement", "click", "onFileSelected", "event", "handleMultipleFiles", "handleSingleFile", "files", "target", "length", "newFiles", "firstFile", "fileNameWithoutExtension", "name", "substring", "lastIndexOf", "emit", "i", "file", "test", "showErrorMSG", "maxSizeInBytes", "size", "reader", "FileReader", "onload", "e", "fileType", "toLowerCase", "match", "endsWith", "fileItem", "data", "result", "CFileBlood", "removeBase64Prefix", "CFileName", "CFileType", "push", "value", "readAsDataURL", "type", "startsWith", "JPG", "includes", "PDF", "CName", "CFile", "toString", "split", "Cimg", "CFileUpload", "base64String", "prefixIndex", "indexOf", "removeFile", "index", "splice", "onFileClick", "fileUrl", "url", "window", "open", "isImage", "isCad", "isPDFString", "str", "isCadString", "lowerStr", "ɵɵdirectiveInject", "i1", "AllowHelper", "i2", "MessageService", "selectors", "viewQuery", "FileUploadComponent_Query", "rf", "ctx", "ɵɵtemplate", "FileUploadComponent_h3_5_Template", "FileUploadComponent_Template_input_change_7_listener", "$event", "_r1", "FileUploadComponent_div_12_Template", "FileUploadComponent_div_13_Template", "ɵɵstyleProp", "ɵɵclassProp", "ɵɵclassMap", "i3", "NgIf", "styles"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\components\\file-upload\\file-upload-enhanced.component.ts", "C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\components\\file-upload\\file-upload.component.html"], "sourcesContent": ["import { Component, ElementRef, EventEmitter, Input, OnInit, Output, ViewChild } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { AllowHelper } from 'src/app/shared/helper/allowHelper';\r\nimport { BaseComponent } from '../../components/base/baseComponent';\r\nimport { MessageService } from 'src/app/shared/services/message.service';\r\nimport { EnumFileType } from 'src/app/shared/enum/enumFileType';\r\n\r\nexport interface FileUploadResult {\r\n    CName: string;\r\n    CFile: string;\r\n    Cimg: File;\r\n    CFileUpload: File;\r\n    CFileType: number;\r\n    fileName: string;\r\n}\r\n\r\nexport interface FileUploadItem {\r\n    data: string;\r\n    CFileBlood: string;\r\n    CFileName: string;\r\n    CFileType: number;\r\n}\r\n\r\nexport interface ExistingFileItem {\r\n    CFile: string;\r\n    CFileName: string;\r\n}\r\n\r\nexport interface FileUploadConfig {\r\n    acceptedTypes?: string[];\r\n    acceptedFileRegex?: RegExp;\r\n    acceptAttribute?: string;\r\n    label?: string;\r\n    helpText?: string;\r\n    required?: boolean;\r\n    disabled?: boolean;\r\n    autoFillName?: boolean;\r\n    buttonText?: string;\r\n    buttonIcon?: string;\r\n    maxFileSize?: number;\r\n    multiple?: boolean;\r\n    visualMode?: boolean;\r\n    isViewMode?: boolean;\r\n}\r\n\r\n@Component({\r\n    selector: 'app-file-upload',\r\n    templateUrl: './file-upload.component.html',\r\n    styleUrls: ['./file-upload.component.css'],\r\n    standalone: true,\r\n    imports: [CommonModule],\r\n})\r\nexport class FileUploadComponent extends BaseComponent implements OnInit {\r\n    @ViewChild('fileInput') fileInput!: ElementRef;\r\n\r\n    @Input() config: FileUploadConfig = {\r\n        acceptedTypes: ['image/jpeg', 'image/jpg', 'application/pdf', '.dwg', '.dxf'],\r\n        acceptedFileRegex: /pdf|jpg|jpeg|png|dwg|dxf/i,\r\n        acceptAttribute: 'image/jpeg, image/jpg, application/pdf, .dwg, .dxf',\r\n        label: '上傳檔案',\r\n        helpText: '支援格式：圖片 (JPG, JPEG)、PDF、CAD (DWG, DXF)',\r\n        required: false,\r\n        disabled: false,\r\n        autoFillName: false,\r\n        buttonText: '選擇檔案',\r\n        buttonIcon: 'fa fa-upload',\r\n        maxFileSize: 10,\r\n        multiple: true,\r\n        visualMode: true,\r\n        isViewMode: false\r\n    };\r\n\r\n    @Input() currentFileName: string | null = null;\r\n    @Input() currentFileUrl: string | null = null;\r\n    @Input() labelMinWidth: string = '75px';\r\n    @Input() fileList: FileUploadItem[] = [];\r\n    @Input() existingFiles: ExistingFileItem[] = [];\r\n\r\n    @Output() fileSelected = new EventEmitter<FileUploadResult>();\r\n    @Output() fileCleared = new EventEmitter<void>();\r\n    @Output() nameAutoFilled = new EventEmitter<string>();\r\n    @Output() fileListChange = new EventEmitter<FileUploadItem[]>();\r\n    @Output() filesSelected = new EventEmitter<FileUploadItem[]>();\r\n    @Output() fileRemoved = new EventEmitter<number>();\r\n    @Output() fileClicked = new EventEmitter<string>();\r\n\r\n    fileName: string | null = null;\r\n\r\n    constructor(\r\n        private _allow: AllowHelper,\r\n        private message: MessageService\r\n    ) {\r\n        super(_allow);\r\n    }\r\n\r\n    override ngOnInit(): void {\r\n        this.fileName = this.currentFileName;\r\n    }\r\n\r\n    onFileButtonClick() {\r\n        if (!this.config.disabled) {\r\n            this.fileInput.nativeElement.click();\r\n        }\r\n    }\r\n\r\n    onFileSelected(event: any) {\r\n        if (this.config.visualMode && this.config.multiple) {\r\n            this.handleMultipleFiles(event);\r\n        } else {\r\n            this.handleSingleFile(event);\r\n        }\r\n    }\r\n\r\n    private handleMultipleFiles(event: any) {\r\n        const files: FileList = event.target.files;\r\n        if (files && files.length > 0) {\r\n            const newFiles: FileUploadItem[] = [];\r\n\r\n            if (this.config.autoFillName && files.length > 0) {\r\n                const firstFile = files[0];\r\n                const fileNameWithoutExtension = firstFile.name.substring(0, firstFile.name.lastIndexOf('.')) || firstFile.name;\r\n                this.nameAutoFilled.emit(fileNameWithoutExtension);\r\n            }\r\n\r\n            for (let i = 0; i < files.length; i++) {\r\n                const file = files[i];\r\n\r\n                if (!this.config.acceptedFileRegex!.test(file.name)) {\r\n                    this.message.showErrorMSG('檔案格式錯誤，僅限pdf、圖片檔、CAD檔案（dwg, dxf）');\r\n                    continue;\r\n                }\r\n\r\n                const maxSizeInBytes = (this.config.maxFileSize || 10) * 1024 * 1024;\r\n                if (file.size > maxSizeInBytes) {\r\n                    this.message.showErrorMSG(`檔案大小超過限制（${this.config.maxFileSize}MB）`);\r\n                    continue;\r\n                }\r\n\r\n                const reader = new FileReader();\r\n                reader.onload = (e: any) => {\r\n                    let fileType = 1;\r\n                    const fileName = file.name.toLowerCase();\r\n\r\n                    if (fileName.match(/\\.(jpg|jpeg|png)$/)) {\r\n                        fileType = 2;\r\n                    } else if (fileName.endsWith('.pdf')) {\r\n                        fileType = 1;\r\n                    } else if (fileName.match(/\\.(dwg|dxf)$/)) {\r\n                        fileType = 3;\r\n                    }\r\n\r\n                    const fileItem: FileUploadItem = {\r\n                        data: e.target.result,\r\n                        CFileBlood: this.removeBase64Prefix(e.target.result),\r\n                        CFileName: file.name,\r\n                        CFileType: fileType\r\n                    };\r\n\r\n                    newFiles.push(fileItem);\r\n\r\n                    if (newFiles.length === files.length) {\r\n                        this.fileList = [...this.fileList, ...newFiles];\r\n                        this.fileListChange.emit(this.fileList);\r\n                        this.filesSelected.emit(newFiles);\r\n\r\n                        if (this.fileInput) {\r\n                            this.fileInput.nativeElement.value = null;\r\n                        }\r\n                    }\r\n                };\r\n                reader.readAsDataURL(file);\r\n            }\r\n        }\r\n    }\r\n\r\n    private handleSingleFile(event: any) {\r\n        const file: File = event.target.files[0];\r\n\r\n        if (!file) {\r\n            return;\r\n        }\r\n\r\n        if (!this.config.acceptedFileRegex!.test(file.name)) {\r\n            this.message.showErrorMSG('檔案格式錯誤');\r\n            return;\r\n        }\r\n\r\n        const maxSizeInBytes = (this.config.maxFileSize || 10) * 1024 * 1024;\r\n        if (file.size > maxSizeInBytes) {\r\n            this.message.showErrorMSG(`檔案大小超過限制（${this.config.maxFileSize}MB）`);\r\n            return;\r\n        }\r\n\r\n        this.fileName = file.name;\r\n\r\n        if (this.config.autoFillName) {\r\n            const fileNameWithoutExtension = file.name.substring(0, file.name.lastIndexOf('.')) || file.name;\r\n            this.nameAutoFilled.emit(fileNameWithoutExtension);\r\n        }\r\n\r\n        const reader = new FileReader();\r\n        reader.onload = (e: any) => {\r\n            let fileType: number;\r\n            if (file.type.startsWith('image/')) {\r\n                fileType = EnumFileType.JPG;\r\n            } else if (file.name.toLowerCase().includes('.dwg') || file.name.toLowerCase().includes('.dxf')) {\r\n                fileType = 3;\r\n            } else {\r\n                fileType = EnumFileType.PDF;\r\n            }\r\n\r\n            const result: FileUploadResult = {\r\n                CName: file.name,\r\n                CFile: e.target?.result?.toString().split(',')[1],\r\n                Cimg: file,\r\n                CFileUpload: file,\r\n                CFileType: fileType,\r\n                fileName: file.name\r\n            };\r\n\r\n            this.fileSelected.emit(result);\r\n\r\n            if (this.fileInput) {\r\n                this.fileInput.nativeElement.value = null;\r\n            }\r\n        };\r\n        reader.readAsDataURL(file);\r\n    }\r\n\r\n    removeBase64Prefix(base64String: string): string {\r\n        const prefixIndex = base64String.indexOf(',');\r\n        if (prefixIndex !== -1) {\r\n            return base64String.substring(prefixIndex + 1);\r\n        }\r\n        return base64String;\r\n    }\r\n\r\n    removeFile(index: number) {\r\n        this.fileList.splice(index, 1);\r\n        this.fileListChange.emit(this.fileList);\r\n        this.fileRemoved.emit(index);\r\n    }\r\n\r\n    onFileClick(fileUrl: string) {\r\n        this.fileClicked.emit(fileUrl);\r\n    }\r\n\r\n    clearFile() {\r\n        this.fileName = null;\r\n        this.fileCleared.emit();\r\n        if (this.fileInput) {\r\n            this.fileInput.nativeElement.value = null;\r\n        }\r\n    }\r\n\r\n    openFile(url: string) {\r\n        if (url) {\r\n            window.open(url, '_blank');\r\n        }\r\n    }\r\n\r\n    isImage(fileType: number): boolean {\r\n        return fileType === 2;\r\n    }\r\n\r\n    isCad(fileType: number): boolean {\r\n        return fileType === 3;\r\n    }\r\n\r\n    isPDFString(str: any): boolean {\r\n        if (str) {\r\n            return str.toLowerCase().endsWith(\".pdf\")\r\n        }\r\n        return false;\r\n    }\r\n\r\n    isCadString(str: any): boolean {\r\n        if (str) {\r\n            const lowerStr = str.toLowerCase();\r\n            return lowerStr.endsWith(\".dwg\") || lowerStr.endsWith(\".dxf\");\r\n        }\r\n        return false;\r\n    }\r\n}\r\n", "<div class=\"form-group d-flex align-items-center\">\r\n  <div class=\"form-group d-flex align-items-center w-full\">\r\n    <div class=\"d-flex flex-col mr-3\">\r\n      <label for=\"file\" class=\"label col-3\" [class.required-field]=\"config.required\" [style.min-width]=\"labelMinWidth\">\r\n        {{ config.label }}\r\n      </label>\r\n      <h3 style=\"color:red;\" *ngIf=\"config.helpText\">{{ config.helpText }}</h3>\r\n    </div>\r\n\r\n    <div class=\"flex flex-col col-9 px-0 items-start\">\r\n      <input #fileInput type=\"file\" id=\"fileInput\" [accept]=\"config.acceptAttribute\" class=\"hidden\"\r\n        style=\"display: none\" (change)=\"onFileSelected($event)\" [disabled]=\"config.disabled\">\r\n\r\n      <label for=\"fileInput\" [class.opacity-50]=\"config.disabled\" [class.cursor-pointer]=\"!config.disabled\"\r\n        class=\"bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded\">\r\n        <i [class]=\"config.buttonIcon + ' mr-2'\"></i> {{ config.buttonText }}\r\n      </label>\r\n\r\n      <!-- 已選擇的檔案顯示 -->\r\n      <div class=\"flex items-center space-x-2 mt-2\" *ngIf=\"fileName\">\r\n        <span class=\"text-gray-600\">{{ fileName }}</span>\r\n        <button type=\"button\" (click)=\"clearFile()\" class=\"text-red-500 hover:text-red-700\"\r\n          [disabled]=\"config.disabled\">\r\n          <i class=\"fa-solid fa-trash\"></i>\r\n        </button>\r\n      </div>\r\n\r\n      <!-- 已存在的檔案連結 -->\r\n      <div class=\"flex items-center space-x-2 mt-2\" *ngIf=\"currentFileUrl && !fileName\">\r\n        <span class=\"text-sm\">\r\n          <a (click)=\"openFile(currentFileUrl)\" class=\"cursor-pointer text-blue-500\">\r\n            {{ currentFileUrl }}\r\n          </a>\r\n        </span>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</div>"], "mappings": "AAAA,SAAgCA,YAAY,QAA0C,eAAe;AACrG,SAASC,YAAY,QAAQ,iBAAiB;AAE9C,SAASC,aAAa,QAAQ,qCAAqC;AAEnE,SAASC,YAAY,QAAQ,kCAAkC;;;;;;;;ICCzDC,EAAA,CAAAC,cAAA,aAA+C;IAAAD,EAAA,CAAAE,MAAA,GAAqB;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;IAA1BH,EAAA,CAAAI,SAAA,EAAqB;IAArBJ,EAAA,CAAAK,iBAAA,CAAAC,MAAA,CAAAC,MAAA,CAAAC,QAAA,CAAqB;;;;;;IAclER,EADF,CAAAC,cAAA,cAA+D,eACjC;IAAAD,EAAA,CAAAE,MAAA,GAAc;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACjDH,EAAA,CAAAC,cAAA,iBAC+B;IADTD,EAAA,CAAAS,UAAA,mBAAAC,4DAAA;MAAAV,EAAA,CAAAW,aAAA,CAAAC,GAAA;MAAA,MAAAN,MAAA,GAAAN,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAc,WAAA,CAASR,MAAA,CAAAS,SAAA,EAAW;IAAA,EAAC;IAEzCf,EAAA,CAAAgB,SAAA,YAAiC;IAErChB,EADE,CAAAG,YAAA,EAAS,EACL;;;;IALwBH,EAAA,CAAAI,SAAA,GAAc;IAAdJ,EAAA,CAAAK,iBAAA,CAAAC,MAAA,CAAAW,QAAA,CAAc;IAExCjB,EAAA,CAAAI,SAAA,EAA4B;IAA5BJ,EAAA,CAAAkB,UAAA,aAAAZ,MAAA,CAAAC,MAAA,CAAAY,QAAA,CAA4B;;;;;;IAQ5BnB,EAFJ,CAAAC,cAAA,cAAkF,eAC1D,YACuD;IAAxED,EAAA,CAAAS,UAAA,mBAAAW,uDAAA;MAAApB,EAAA,CAAAW,aAAA,CAAAU,GAAA;MAAA,MAAAf,MAAA,GAAAN,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAc,WAAA,CAASR,MAAA,CAAAgB,QAAA,CAAAhB,MAAA,CAAAiB,cAAA,CAAwB;IAAA,EAAC;IACnCvB,EAAA,CAAAE,MAAA,GACF;IAEJF,EAFI,CAAAG,YAAA,EAAI,EACC,EACH;;;;IAHAH,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAwB,kBAAA,MAAAlB,MAAA,CAAAiB,cAAA,MACF;;;ADoBV,OAAM,MAAOE,mBAAoB,SAAQ3B,aAAa;EAoClD4B,YACYC,MAAmB,EACnBC,OAAuB;IAE/B,KAAK,CAACD,MAAM,CAAC;IAHL,KAAAA,MAAM,GAANA,MAAM;IACN,KAAAC,OAAO,GAAPA,OAAO;IAnCV,KAAArB,MAAM,GAAqB;MAChCsB,aAAa,EAAE,CAAC,YAAY,EAAE,WAAW,EAAE,iBAAiB,EAAE,MAAM,EAAE,MAAM,CAAC;MAC7EC,iBAAiB,EAAE,2BAA2B;MAC9CC,eAAe,EAAE,oDAAoD;MACrEC,KAAK,EAAE,MAAM;MACbxB,QAAQ,EAAE,wCAAwC;MAClDyB,QAAQ,EAAE,KAAK;MACfd,QAAQ,EAAE,KAAK;MACfe,YAAY,EAAE,KAAK;MACnBC,UAAU,EAAE,MAAM;MAClBC,UAAU,EAAE,cAAc;MAC1BC,WAAW,EAAE,EAAE;MACfC,QAAQ,EAAE,IAAI;MACdC,UAAU,EAAE,IAAI;MAChBC,UAAU,EAAE;KACf;IAEQ,KAAAC,eAAe,GAAkB,IAAI;IACrC,KAAAlB,cAAc,GAAkB,IAAI;IACpC,KAAAmB,aAAa,GAAW,MAAM;IAC9B,KAAAC,QAAQ,GAAqB,EAAE;IAC/B,KAAAC,aAAa,GAAuB,EAAE;IAErC,KAAAC,YAAY,GAAG,IAAIjD,YAAY,EAAoB;IACnD,KAAAkD,WAAW,GAAG,IAAIlD,YAAY,EAAQ;IACtC,KAAAmD,cAAc,GAAG,IAAInD,YAAY,EAAU;IAC3C,KAAAoD,cAAc,GAAG,IAAIpD,YAAY,EAAoB;IACrD,KAAAqD,aAAa,GAAG,IAAIrD,YAAY,EAAoB;IACpD,KAAAsD,WAAW,GAAG,IAAItD,YAAY,EAAU;IACxC,KAAAuD,WAAW,GAAG,IAAIvD,YAAY,EAAU;IAElD,KAAAqB,QAAQ,GAAkB,IAAI;EAO9B;EAESmC,QAAQA,CAAA;IACb,IAAI,CAACnC,QAAQ,GAAG,IAAI,CAACwB,eAAe;EACxC;EAEAY,iBAAiBA,CAAA;IACb,IAAI,CAAC,IAAI,CAAC9C,MAAM,CAACY,QAAQ,EAAE;MACvB,IAAI,CAACmC,SAAS,CAACC,aAAa,CAACC,KAAK,EAAE;IACxC;EACJ;EAEAC,cAAcA,CAACC,KAAU;IACrB,IAAI,IAAI,CAACnD,MAAM,CAACgC,UAAU,IAAI,IAAI,CAAChC,MAAM,CAAC+B,QAAQ,EAAE;MAChD,IAAI,CAACqB,mBAAmB,CAACD,KAAK,CAAC;IACnC,CAAC,MAAM;MACH,IAAI,CAACE,gBAAgB,CAACF,KAAK,CAAC;IAChC;EACJ;EAEQC,mBAAmBA,CAACD,KAAU;IAClC,MAAMG,KAAK,GAAaH,KAAK,CAACI,MAAM,CAACD,KAAK;IAC1C,IAAIA,KAAK,IAAIA,KAAK,CAACE,MAAM,GAAG,CAAC,EAAE;MAC3B,MAAMC,QAAQ,GAAqB,EAAE;MAErC,IAAI,IAAI,CAACzD,MAAM,CAAC2B,YAAY,IAAI2B,KAAK,CAACE,MAAM,GAAG,CAAC,EAAE;QAC9C,MAAME,SAAS,GAAGJ,KAAK,CAAC,CAAC,CAAC;QAC1B,MAAMK,wBAAwB,GAAGD,SAAS,CAACE,IAAI,CAACC,SAAS,CAAC,CAAC,EAAEH,SAAS,CAACE,IAAI,CAACE,WAAW,CAAC,GAAG,CAAC,CAAC,IAAIJ,SAAS,CAACE,IAAI;QAC/G,IAAI,CAACpB,cAAc,CAACuB,IAAI,CAACJ,wBAAwB,CAAC;MACtD;MAEA,KAAK,IAAIK,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGV,KAAK,CAACE,MAAM,EAAEQ,CAAC,EAAE,EAAE;QACnC,MAAMC,IAAI,GAAGX,KAAK,CAACU,CAAC,CAAC;QAErB,IAAI,CAAC,IAAI,CAAChE,MAAM,CAACuB,iBAAkB,CAAC2C,IAAI,CAACD,IAAI,CAACL,IAAI,CAAC,EAAE;UACjD,IAAI,CAACvC,OAAO,CAAC8C,YAAY,CAAC,kCAAkC,CAAC;UAC7D;QACJ;QAEA,MAAMC,cAAc,GAAG,CAAC,IAAI,CAACpE,MAAM,CAAC8B,WAAW,IAAI,EAAE,IAAI,IAAI,GAAG,IAAI;QACpE,IAAImC,IAAI,CAACI,IAAI,GAAGD,cAAc,EAAE;UAC5B,IAAI,CAAC/C,OAAO,CAAC8C,YAAY,CAAC,YAAY,IAAI,CAACnE,MAAM,CAAC8B,WAAW,KAAK,CAAC;UACnE;QACJ;QAEA,MAAMwC,MAAM,GAAG,IAAIC,UAAU,EAAE;QAC/BD,MAAM,CAACE,MAAM,GAAIC,CAAM,IAAI;UACvB,IAAIC,QAAQ,GAAG,CAAC;UAChB,MAAMhE,QAAQ,GAAGuD,IAAI,CAACL,IAAI,CAACe,WAAW,EAAE;UAExC,IAAIjE,QAAQ,CAACkE,KAAK,CAAC,mBAAmB,CAAC,EAAE;YACrCF,QAAQ,GAAG,CAAC;UAChB,CAAC,MAAM,IAAIhE,QAAQ,CAACmE,QAAQ,CAAC,MAAM,CAAC,EAAE;YAClCH,QAAQ,GAAG,CAAC;UAChB,CAAC,MAAM,IAAIhE,QAAQ,CAACkE,KAAK,CAAC,cAAc,CAAC,EAAE;YACvCF,QAAQ,GAAG,CAAC;UAChB;UAEA,MAAMI,QAAQ,GAAmB;YAC7BC,IAAI,EAAEN,CAAC,CAAClB,MAAM,CAACyB,MAAM;YACrBC,UAAU,EAAE,IAAI,CAACC,kBAAkB,CAACT,CAAC,CAAClB,MAAM,CAACyB,MAAM,CAAC;YACpDG,SAAS,EAAElB,IAAI,CAACL,IAAI;YACpBwB,SAAS,EAAEV;WACd;UAEDjB,QAAQ,CAAC4B,IAAI,CAACP,QAAQ,CAAC;UAEvB,IAAIrB,QAAQ,CAACD,MAAM,KAAKF,KAAK,CAACE,MAAM,EAAE;YAClC,IAAI,CAACpB,QAAQ,GAAG,CAAC,GAAG,IAAI,CAACA,QAAQ,EAAE,GAAGqB,QAAQ,CAAC;YAC/C,IAAI,CAAChB,cAAc,CAACsB,IAAI,CAAC,IAAI,CAAC3B,QAAQ,CAAC;YACvC,IAAI,CAACM,aAAa,CAACqB,IAAI,CAACN,QAAQ,CAAC;YAEjC,IAAI,IAAI,CAACV,SAAS,EAAE;cAChB,IAAI,CAACA,SAAS,CAACC,aAAa,CAACsC,KAAK,GAAG,IAAI;YAC7C;UACJ;QACJ,CAAC;QACDhB,MAAM,CAACiB,aAAa,CAACtB,IAAI,CAAC;MAC9B;IACJ;EACJ;EAEQZ,gBAAgBA,CAACF,KAAU;IAC/B,MAAMc,IAAI,GAASd,KAAK,CAACI,MAAM,CAACD,KAAK,CAAC,CAAC,CAAC;IAExC,IAAI,CAACW,IAAI,EAAE;MACP;IACJ;IAEA,IAAI,CAAC,IAAI,CAACjE,MAAM,CAACuB,iBAAkB,CAAC2C,IAAI,CAACD,IAAI,CAACL,IAAI,CAAC,EAAE;MACjD,IAAI,CAACvC,OAAO,CAAC8C,YAAY,CAAC,QAAQ,CAAC;MACnC;IACJ;IAEA,MAAMC,cAAc,GAAG,CAAC,IAAI,CAACpE,MAAM,CAAC8B,WAAW,IAAI,EAAE,IAAI,IAAI,GAAG,IAAI;IACpE,IAAImC,IAAI,CAACI,IAAI,GAAGD,cAAc,EAAE;MAC5B,IAAI,CAAC/C,OAAO,CAAC8C,YAAY,CAAC,YAAY,IAAI,CAACnE,MAAM,CAAC8B,WAAW,KAAK,CAAC;MACnE;IACJ;IAEA,IAAI,CAACpB,QAAQ,GAAGuD,IAAI,CAACL,IAAI;IAEzB,IAAI,IAAI,CAAC5D,MAAM,CAAC2B,YAAY,EAAE;MAC1B,MAAMgC,wBAAwB,GAAGM,IAAI,CAACL,IAAI,CAACC,SAAS,CAAC,CAAC,EAAEI,IAAI,CAACL,IAAI,CAACE,WAAW,CAAC,GAAG,CAAC,CAAC,IAAIG,IAAI,CAACL,IAAI;MAChG,IAAI,CAACpB,cAAc,CAACuB,IAAI,CAACJ,wBAAwB,CAAC;IACtD;IAEA,MAAMW,MAAM,GAAG,IAAIC,UAAU,EAAE;IAC/BD,MAAM,CAACE,MAAM,GAAIC,CAAM,IAAI;MACvB,IAAIC,QAAgB;MACpB,IAAIT,IAAI,CAACuB,IAAI,CAACC,UAAU,CAAC,QAAQ,CAAC,EAAE;QAChCf,QAAQ,GAAGlF,YAAY,CAACkG,GAAG;MAC/B,CAAC,MAAM,IAAIzB,IAAI,CAACL,IAAI,CAACe,WAAW,EAAE,CAACgB,QAAQ,CAAC,MAAM,CAAC,IAAI1B,IAAI,CAACL,IAAI,CAACe,WAAW,EAAE,CAACgB,QAAQ,CAAC,MAAM,CAAC,EAAE;QAC7FjB,QAAQ,GAAG,CAAC;MAChB,CAAC,MAAM;QACHA,QAAQ,GAAGlF,YAAY,CAACoG,GAAG;MAC/B;MAEA,MAAMZ,MAAM,GAAqB;QAC7Ba,KAAK,EAAE5B,IAAI,CAACL,IAAI;QAChBkC,KAAK,EAAErB,CAAC,CAAClB,MAAM,EAAEyB,MAAM,EAAEe,QAAQ,EAAE,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QACjDC,IAAI,EAAEhC,IAAI;QACViC,WAAW,EAAEjC,IAAI;QACjBmB,SAAS,EAAEV,QAAQ;QACnBhE,QAAQ,EAAEuD,IAAI,CAACL;OAClB;MAED,IAAI,CAACtB,YAAY,CAACyB,IAAI,CAACiB,MAAM,CAAC;MAE9B,IAAI,IAAI,CAACjC,SAAS,EAAE;QAChB,IAAI,CAACA,SAAS,CAACC,aAAa,CAACsC,KAAK,GAAG,IAAI;MAC7C;IACJ,CAAC;IACDhB,MAAM,CAACiB,aAAa,CAACtB,IAAI,CAAC;EAC9B;EAEAiB,kBAAkBA,CAACiB,YAAoB;IACnC,MAAMC,WAAW,GAAGD,YAAY,CAACE,OAAO,CAAC,GAAG,CAAC;IAC7C,IAAID,WAAW,KAAK,CAAC,CAAC,EAAE;MACpB,OAAOD,YAAY,CAACtC,SAAS,CAACuC,WAAW,GAAG,CAAC,CAAC;IAClD;IACA,OAAOD,YAAY;EACvB;EAEAG,UAAUA,CAACC,KAAa;IACpB,IAAI,CAACnE,QAAQ,CAACoE,MAAM,CAACD,KAAK,EAAE,CAAC,CAAC;IAC9B,IAAI,CAAC9D,cAAc,CAACsB,IAAI,CAAC,IAAI,CAAC3B,QAAQ,CAAC;IACvC,IAAI,CAACO,WAAW,CAACoB,IAAI,CAACwC,KAAK,CAAC;EAChC;EAEAE,WAAWA,CAACC,OAAe;IACvB,IAAI,CAAC9D,WAAW,CAACmB,IAAI,CAAC2C,OAAO,CAAC;EAClC;EAEAlG,SAASA,CAAA;IACL,IAAI,CAACE,QAAQ,GAAG,IAAI;IACpB,IAAI,CAAC6B,WAAW,CAACwB,IAAI,EAAE;IACvB,IAAI,IAAI,CAAChB,SAAS,EAAE;MAChB,IAAI,CAACA,SAAS,CAACC,aAAa,CAACsC,KAAK,GAAG,IAAI;IAC7C;EACJ;EAEAvE,QAAQA,CAAC4F,GAAW;IAChB,IAAIA,GAAG,EAAE;MACLC,MAAM,CAACC,IAAI,CAACF,GAAG,EAAE,QAAQ,CAAC;IAC9B;EACJ;EAEAG,OAAOA,CAACpC,QAAgB;IACpB,OAAOA,QAAQ,KAAK,CAAC;EACzB;EAEAqC,KAAKA,CAACrC,QAAgB;IAClB,OAAOA,QAAQ,KAAK,CAAC;EACzB;EAEAsC,WAAWA,CAACC,GAAQ;IAChB,IAAIA,GAAG,EAAE;MACL,OAAOA,GAAG,CAACtC,WAAW,EAAE,CAACE,QAAQ,CAAC,MAAM,CAAC;IAC7C;IACA,OAAO,KAAK;EAChB;EAEAqC,WAAWA,CAACD,GAAQ;IAChB,IAAIA,GAAG,EAAE;MACL,MAAME,QAAQ,GAAGF,GAAG,CAACtC,WAAW,EAAE;MAClC,OAAOwC,QAAQ,CAACtC,QAAQ,CAAC,MAAM,CAAC,IAAIsC,QAAQ,CAACtC,QAAQ,CAAC,MAAM,CAAC;IACjE;IACA,OAAO,KAAK;EAChB;;;uCAtOS3D,mBAAmB,EAAAzB,EAAA,CAAA2H,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAA7H,EAAA,CAAA2H,iBAAA,CAAAG,EAAA,CAAAC,cAAA;IAAA;EAAA;;;YAAnBtG,mBAAmB;MAAAuG,SAAA;MAAAC,SAAA,WAAAC,0BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;UCjD1BnI,EAHN,CAAAC,cAAA,aAAkD,aACS,aACrB,eACiF;UAC/GD,EAAA,CAAAE,MAAA,GACF;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACRH,EAAA,CAAAqI,UAAA,IAAAC,iCAAA,gBAA+C;UACjDtI,EAAA,CAAAG,YAAA,EAAM;UAGJH,EADF,CAAAC,cAAA,aAAkD,kBAEuC;UAA/DD,EAAA,CAAAS,UAAA,oBAAA8H,qDAAAC,MAAA;YAAAxI,EAAA,CAAAW,aAAA,CAAA8H,GAAA;YAAA,OAAAzI,EAAA,CAAAc,WAAA,CAAUsH,GAAA,CAAA3E,cAAA,CAAA+E,MAAA,CAAsB;UAAA,EAAC;UADzDxI,EAAA,CAAAG,YAAA,EACuF;UAEvFH,EAAA,CAAAC,cAAA,eAC+E;UAC7ED,EAAA,CAAAgB,SAAA,SAA6C;UAAChB,EAAA,CAAAE,MAAA,IAChD;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAYRH,EATA,CAAAqI,UAAA,KAAAK,mCAAA,iBAA+D,KAAAC,mCAAA,iBASmB;UASxF3I,EAFI,CAAAG,YAAA,EAAM,EACF,EACF;;;UAlC+EH,EAAA,CAAAI,SAAA,GAAiC;UAAjCJ,EAAA,CAAA4I,WAAA,cAAAR,GAAA,CAAA1F,aAAA,CAAiC;UAA1E1C,EAAA,CAAA6I,WAAA,mBAAAT,GAAA,CAAA7H,MAAA,CAAA0B,QAAA,CAAwC;UAC5EjC,EAAA,CAAAI,SAAA,EACF;UADEJ,EAAA,CAAAwB,kBAAA,MAAA4G,GAAA,CAAA7H,MAAA,CAAAyB,KAAA,MACF;UACwBhC,EAAA,CAAAI,SAAA,EAAqB;UAArBJ,EAAA,CAAAkB,UAAA,SAAAkH,GAAA,CAAA7H,MAAA,CAAAC,QAAA,CAAqB;UAIAR,EAAA,CAAAI,SAAA,GAAiC;UACpBJ,EADb,CAAAkB,UAAA,WAAAkH,GAAA,CAAA7H,MAAA,CAAAwB,eAAA,CAAiC,aAAAqG,GAAA,CAAA7H,MAAA,CAAAY,QAAA,CACQ;UAE/DnB,EAAA,CAAAI,SAAA,GAAoC;UAACJ,EAArC,CAAA6I,WAAA,eAAAT,GAAA,CAAA7H,MAAA,CAAAY,QAAA,CAAoC,oBAAAiH,GAAA,CAAA7H,MAAA,CAAAY,QAAA,CAA0C;UAEhGnB,EAAA,CAAAI,SAAA,EAAqC;UAArCJ,EAAA,CAAA8I,UAAA,CAAAV,GAAA,CAAA7H,MAAA,CAAA6B,UAAA,WAAqC;UAAMpC,EAAA,CAAAI,SAAA,EAChD;UADgDJ,EAAA,CAAAwB,kBAAA,MAAA4G,GAAA,CAAA7H,MAAA,CAAA4B,UAAA,MAChD;UAG+CnC,EAAA,CAAAI,SAAA,EAAc;UAAdJ,EAAA,CAAAkB,UAAA,SAAAkH,GAAA,CAAAnH,QAAA,CAAc;UASdjB,EAAA,CAAAI,SAAA,EAAiC;UAAjCJ,EAAA,CAAAkB,UAAA,SAAAkH,GAAA,CAAA7G,cAAA,KAAA6G,GAAA,CAAAnH,QAAA,CAAiC;;;qBDsBxEpB,YAAY,EAAAkJ,EAAA,CAAAC,IAAA;MAAAC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}