{"ast": null, "code": "import { last, map } from 'rxjs';\nimport { FormBodyBuilder } from 'src/app/shared/constant/constant';\nimport { environment } from 'src/environments/environment';\nimport { RequestBuilder } from 'src/services/api/request-builder';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport class NoticeServiceCustom {\n  constructor(http) {\n    this.http = http;\n  }\n  SaveSpecialNoticeFile(body) {\n    const endPoint = '/api/SpecialNoticeFile/SaveSpecialNoticeFile';\n    // const body = {\n    //     CNoticeType,\n    //     CBuildCaseId,\n    //     CFile,\n    //     CHouse,\n    //     CSpecialNoticeFileId,\n    //     CIsSelectAll,\n    //     CExamineNote\n    // };\n    return this._request(body, endPoint, 'post').pipe(last(), map(res => res) // Explicitly cast the body to StringResponseBase\n    );\n  }\n  _request(body, endPoint, method, context) {\n    const rb = new RequestBuilder(environment.BASE_URL_API, endPoint, method);\n    rb._bodyContent = FormBodyBuilder.BuildBodyContent(body);\n    return this.http.request(rb.build({\n      responseType: 'json',\n      accept: 'text/json',\n      reportProgress: false\n    }));\n  }\n  static {\n    this.ɵfac = function NoticeServiceCustom_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || NoticeServiceCustom)(i0.ɵɵinject(i1.HttpClient));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: NoticeServiceCustom,\n      factory: NoticeServiceCustom.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["last", "map", "FormBodyBuilder", "environment", "RequestBuilder", "NoticeServiceCustom", "constructor", "http", "SaveSpecialNoticeFile", "body", "endPoint", "_request", "pipe", "res", "method", "context", "rb", "BASE_URL_API", "_bodyContent", "BuildBodyContent", "request", "build", "responseType", "accept", "reportProgress", "i0", "ɵɵinject", "i1", "HttpClient", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\@core\\service\\notice.service.ts"], "sourcesContent": ["import { HttpClient, HttpContext, HttpRequest } from '@angular/common/http';\r\nimport { Injectable } from '@angular/core';\r\nimport { last, map, Observable } from 'rxjs';\r\nimport { FormBodyBuilder } from 'src/app/shared/constant/constant';\r\nimport { environment } from 'src/environments/environment';\r\nimport { EnumStatusCode, HouseSpecialNoticeFile } from 'src/services/api/models';\r\nimport { RequestBuilder } from 'src/services/api/request-builder';\r\n\r\nexport interface StringResponseBase {\r\n  body: any;\r\n  Entries?: string | null;\r\n  Message?: string | null;\r\n  StatusCode?: EnumStatusCode;\r\n  TotalItems?: number;\r\n}\r\n@Injectable({\r\n    providedIn: 'root',\r\n})\r\nexport class NoticeServiceCustom {\r\n    constructor(\r\n        private http: HttpClient,\r\n    ) { }\r\n\r\n    public SaveSpecialNoticeFile(body:{\r\n        CNoticeType?: string,\r\n        CBuildCaseId?: string,\r\n        CFile?: any,\r\n        CHouse?: Array<HouseSpecialNoticeFile>,\r\n        CSpecialNoticeFileId?: number,\r\n        CIsSelectAll?: boolean,\r\n        CExamineNote?: string| null\r\n    }\r\n    ): Observable<StringResponseBase> { // Specify the return type\r\n        const endPoint = '/api/SpecialNoticeFile/SaveSpecialNoticeFile';\r\n        // const body = {\r\n        //     CNoticeType,\r\n        //     CBuildCaseId,\r\n        //     CFile,\r\n        //     CHouse,\r\n        //     CSpecialNoticeFileId,\r\n        //     CIsSelectAll,\r\n        //     CExamineNote\r\n        // };\r\n    \r\n        return this._request(body, endPoint, 'post').pipe(\r\n            last(), \r\n            map(res => res as StringResponseBase) // Explicitly cast the body to StringResponseBase\r\n        ); \r\n    }\r\n    private _request(\r\n        body: any,\r\n        endPoint: string,\r\n        method: string,\r\n        context?: HttpContext\r\n    ) {\r\n        const rb = new RequestBuilder(environment.BASE_URL_API, endPoint, method);\r\n        rb._bodyContent = FormBodyBuilder.BuildBodyContent(body);\r\n\r\n        return this.http.request(\r\n            rb.build({\r\n                responseType: 'json',\r\n                accept: 'text/json',\r\n                reportProgress: false,\r\n            })\r\n        );\r\n    }\r\n}\r\n\r\n"], "mappings": "AAEA,SAASA,IAAI,EAAEC,GAAG,QAAoB,MAAM;AAC5C,SAASC,eAAe,QAAQ,kCAAkC;AAClE,SAASC,WAAW,QAAQ,8BAA8B;AAE1D,SAASC,cAAc,QAAQ,kCAAkC;;;AAYjE,OAAM,MAAOC,mBAAmB;EAC5BC,YACYC,IAAgB;IAAhB,KAAAA,IAAI,GAAJA,IAAI;EACZ;EAEGC,qBAAqBA,CAACC,IAQ5B;IAEG,MAAMC,QAAQ,GAAG,8CAA8C;IAC/D;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IAEA,OAAO,IAAI,CAACC,QAAQ,CAACF,IAAI,EAAEC,QAAQ,EAAE,MAAM,CAAC,CAACE,IAAI,CAC7CZ,IAAI,EAAE,EACNC,GAAG,CAACY,GAAG,IAAIA,GAAyB,CAAC,CAAC;KACzC;EACL;EACQF,QAAQA,CACZF,IAAS,EACTC,QAAgB,EAChBI,MAAc,EACdC,OAAqB;IAErB,MAAMC,EAAE,GAAG,IAAIZ,cAAc,CAACD,WAAW,CAACc,YAAY,EAAEP,QAAQ,EAAEI,MAAM,CAAC;IACzEE,EAAE,CAACE,YAAY,GAAGhB,eAAe,CAACiB,gBAAgB,CAACV,IAAI,CAAC;IAExD,OAAO,IAAI,CAACF,IAAI,CAACa,OAAO,CACpBJ,EAAE,CAACK,KAAK,CAAC;MACLC,YAAY,EAAE,MAAM;MACpBC,MAAM,EAAE,WAAW;MACnBC,cAAc,EAAE;KACnB,CAAC,CACL;EACL;;;uCA/CSnB,mBAAmB,EAAAoB,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;IAAA;EAAA;;;aAAnBvB,mBAAmB;MAAAwB,OAAA,EAAnBxB,mBAAmB,CAAAyB,IAAA;MAAAC,UAAA,EAFhB;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}