{"ast": null, "code": "import { Subject } from '../Subject';\nimport { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nimport { innerFrom } from '../observable/innerFrom';\nexport function windowWhen(closingSelector) {\n  return operate((source, subscriber) => {\n    let window;\n    let closingSubscriber;\n    const handleError = err => {\n      window.error(err);\n      subscriber.error(err);\n    };\n    const openWindow = () => {\n      closingSubscriber === null || closingSubscriber === void 0 ? void 0 : closingSubscriber.unsubscribe();\n      window === null || window === void 0 ? void 0 : window.complete();\n      window = new Subject();\n      subscriber.next(window.asObservable());\n      let closingNotifier;\n      try {\n        closingNotifier = innerFrom(closingSelector());\n      } catch (err) {\n        handleError(err);\n        return;\n      }\n      closingNotifier.subscribe(closingSubscriber = createOperatorSubscriber(subscriber, openWindow, openWindow, handleError));\n    };\n    openWindow();\n    source.subscribe(createOperatorSubscriber(subscriber, value => window.next(value), () => {\n      window.complete();\n      subscriber.complete();\n    }, handleError, () => {\n      closingSubscriber === null || closingSubscriber === void 0 ? void 0 : closingSubscriber.unsubscribe();\n      window = null;\n    }));\n  });\n}\n//# sourceMappingURL=windowWhen.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}