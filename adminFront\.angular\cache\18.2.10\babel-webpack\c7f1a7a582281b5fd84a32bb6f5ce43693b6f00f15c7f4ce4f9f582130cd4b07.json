{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport * as i0 from \"@angular/core\";\nconst _c0 = [\"input\"];\nexport let SearchInputComponent = /*#__PURE__*/(() => {\n  class SearchInputComponent {\n    constructor() {\n      this.search = new EventEmitter();\n      this.isInputShown = false;\n    }\n    showInput() {\n      this.isInputShown = true;\n      this.input.nativeElement.focus();\n    }\n    hideInput() {\n      this.isInputShown = false;\n    }\n    onInput(val) {\n      this.search.emit(val);\n    }\n    static {\n      this.ɵfac = function SearchInputComponent_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || SearchInputComponent)();\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: SearchInputComponent,\n        selectors: [[\"ngx-search-input\"]],\n        viewQuery: function SearchInputComponent_Query(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵviewQuery(_c0, 7);\n          }\n          if (rf & 2) {\n            let _t;\n            i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.input = _t.first);\n          }\n        },\n        outputs: {\n          search: \"search\"\n        },\n        standalone: true,\n        features: [i0.ɵɵStandaloneFeature],\n        decls: 3,\n        vars: 2,\n        consts: [[\"input\", \"\"], [1, \"control-icon\", \"ion\", \"ion-ios-search\", 3, \"click\"], [\"placeholder\", \"Type your search request here...\", 3, \"blur\", \"input\"]],\n        template: function SearchInputComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            const _r1 = i0.ɵɵgetCurrentView();\n            i0.ɵɵelementStart(0, \"i\", 1);\n            i0.ɵɵlistener(\"click\", function SearchInputComponent_Template_i_click_0_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.showInput());\n            });\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(1, \"input\", 2, 0);\n            i0.ɵɵlistener(\"blur\", function SearchInputComponent_Template_input_blur_1_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.hideInput());\n            })(\"input\", function SearchInputComponent_Template_input_input_1_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.onInput($event));\n            });\n            i0.ɵɵelementEnd();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance();\n            i0.ɵɵclassProp(\"hidden\", !ctx.isInputShown);\n          }\n        },\n        styles: [\"[_nghost-%COMP%]{display:flex;align-items:center}[_nghost-%COMP%]   i.control-icon[_ngcontent-%COMP%]:before{font-size:2.3rem}[_nghost-%COMP%]   i.control-icon[_ngcontent-%COMP%]:hover{cursor:pointer}[_nghost-%COMP%]   input[_ngcontent-%COMP%]{border:none;outline:none;margin-left:1rem;width:15rem;transition:width .2s ease}[_nghost-%COMP%]   input.hidden[_ngcontent-%COMP%]{width:0;margin:0}[_nghost-%COMP%]     search-input input{background:transparent}\"]\n      });\n    }\n  }\n  return SearchInputComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}