{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/shared/helper/allowHelper\";\nexport class BaseComponent {\n  constructor(allow) {\n    this.allow = allow;\n    this.pageIndex = 1;\n    this.pageSize = 10;\n    this.totalRecords = 0;\n    this.pageFirst = 0;\n    this.isRead = this.allow.isRead();\n    this.isCreate = this.allow.isCreate();\n    this.isUpdate = this.allow.isUpdate();\n    this.isDelete = this.allow.isDelete();\n    this.isExcelImport = this.allow.isExcelImport();\n    this.isExcelExport = this.allow.isExcelExport();\n  }\n  ngOnInit() {}\n  getSubMenu(menu, routerUrl) {\n    return menu.Menu.map(function (p) {\n      return p.Child;\n    }).reduce(function (a, b) {\n      return a.concat(b);\n    }).find(x => routerUrl.indexOf(x.CPageUrl) !== -1);\n  }\n  isNullOrEmpty(value) {\n    if (value === undefined) {\n      return true;\n    }\n    if (value == null) {\n      return true;\n    }\n    if (value === '') {\n      return true;\n    }\n    return false;\n  }\n  static {\n    this.ɵfac = function BaseComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || BaseComponent)(i0.ɵɵinject(i1.AllowHelper));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: BaseComponent,\n      factory: BaseComponent.ɵfac\n    });\n  }\n}", "map": {"version": 3, "names": ["BaseComponent", "constructor", "allow", "pageIndex", "pageSize", "totalRecords", "pageFirst", "isRead", "isCreate", "isUpdate", "isDelete", "isExcelImport", "isExcelExport", "ngOnInit", "getSubMenu", "menu", "routerUrl", "<PERSON><PERSON>", "map", "p", "Child", "reduce", "a", "b", "concat", "find", "x", "indexOf", "CPageUrl", "isNullOrEmpty", "value", "undefined", "i0", "ɵɵinject", "i1", "AllowHelper", "factory", "ɵfac"], "sources": ["C:\\Users\\<USER>\\Documents\\jeansalechange\\adminFront\\src\\app\\pages\\components\\base\\baseComponent.ts"], "sourcesContent": ["import { formatDate } from '@angular/common';\r\nimport { Injectable, OnInit } from '@angular/core';\r\nimport { AllowHelper } from 'src/app/shared/helper/allowHelper';\r\nimport { Menu } from 'src/app/shared/model/menu.model';\r\nimport { GetMenuResponse } from 'src/services/api/models';\r\n\r\n@Injectable()\r\nexport class BaseComponent implements OnInit {\r\n\r\n\r\n  pageIndex = 1;\r\n  pageSize = 10;\r\n  totalRecords = 0;\r\n  pageFirst = 0;\r\n\r\n  isRead = this.allow.isRead();\r\n  isCreate = this.allow.isCreate();\r\n  isUpdate = this.allow.isUpdate();\r\n  isDelete = this.allow.isDelete();\r\n  isExcelImport = this.allow.isExcelImport();\r\n  isExcelExport = this.allow.isExcelExport();\r\n\r\n  constructor(\r\n    protected allow: AllowHelper\r\n  ) {\r\n\r\n  }\r\n\r\n  ngOnInit() {\r\n\r\n  }\r\n\r\n  getSubMenu(menu: GetMenuResponse, routerUrl: string) {\r\n    return menu.Menu!.map(function (p) { return p.Child!; })\r\n      .reduce(function (a, b) { return a.concat(b); })\r\n      .find(x => routerUrl.indexOf(x.CPageUrl!) !== -1);\r\n  }\r\n\r\n  isNullOrEmpty(value: string | null | undefined) {\r\n    if (value === undefined) {\r\n      return true;\r\n    }\r\n    if (value == null) {\r\n      return true;\r\n    }\r\n    if (value === '') {\r\n      return true;\r\n    }\r\n    return false;\r\n  }\r\n}\r\n"], "mappings": ";;AAOA,OAAM,MAAOA,aAAa;EAexBC,YACYC,KAAkB;IAAlB,KAAAA,KAAK,GAALA,KAAK;IAbjB,KAAAC,SAAS,GAAG,CAAC;IACb,KAAAC,QAAQ,GAAG,EAAE;IACb,KAAAC,YAAY,GAAG,CAAC;IAChB,KAAAC,SAAS,GAAG,CAAC;IAEb,KAAAC,MAAM,GAAG,IAAI,CAACL,KAAK,CAACK,MAAM,EAAE;IAC5B,KAAAC,QAAQ,GAAG,IAAI,CAACN,KAAK,CAACM,QAAQ,EAAE;IAChC,KAAAC,QAAQ,GAAG,IAAI,CAACP,KAAK,CAACO,QAAQ,EAAE;IAChC,KAAAC,QAAQ,GAAG,IAAI,CAACR,KAAK,CAACQ,QAAQ,EAAE;IAChC,KAAAC,aAAa,GAAG,IAAI,CAACT,KAAK,CAACS,aAAa,EAAE;IAC1C,KAAAC,aAAa,GAAG,IAAI,CAACV,KAAK,CAACU,aAAa,EAAE;EAM1C;EAEAC,QAAQA,CAAA,GAER;EAEAC,UAAUA,CAACC,IAAqB,EAAEC,SAAiB;IACjD,OAAOD,IAAI,CAACE,IAAK,CAACC,GAAG,CAAC,UAAUC,CAAC;MAAI,OAAOA,CAAC,CAACC,KAAM;IAAE,CAAC,CAAC,CACrDC,MAAM,CAAC,UAAUC,CAAC,EAAEC,CAAC;MAAI,OAAOD,CAAC,CAACE,MAAM,CAACD,CAAC,CAAC;IAAE,CAAC,CAAC,CAC/CE,IAAI,CAACC,CAAC,IAAIV,SAAS,CAACW,OAAO,CAACD,CAAC,CAACE,QAAS,CAAC,KAAK,CAAC,CAAC,CAAC;EACrD;EAEAC,aAAaA,CAACC,KAAgC;IAC5C,IAAIA,KAAK,KAAKC,SAAS,EAAE;MACvB,OAAO,IAAI;IACb;IACA,IAAID,KAAK,IAAI,IAAI,EAAE;MACjB,OAAO,IAAI;IACb;IACA,IAAIA,KAAK,KAAK,EAAE,EAAE;MAChB,OAAO,IAAI;IACb;IACA,OAAO,KAAK;EACd;;;uCA1CW9B,aAAa,EAAAgC,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,WAAA;IAAA;EAAA;;;aAAbnC,aAAa;MAAAoC,OAAA,EAAbpC,aAAa,CAAAqC;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}