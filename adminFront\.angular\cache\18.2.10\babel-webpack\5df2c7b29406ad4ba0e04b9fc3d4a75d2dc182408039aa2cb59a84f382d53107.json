{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport * as i0 from \"@angular/core\";\nfunction TemplateViewerComponent_div_11_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\")(1, \"input\", 18);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function TemplateViewerComponent_div_11_div_10_Template_input_ngModelChange_1_listener($event) {\n      const item_r4 = i0.ɵɵrestoreView(_r3).$implicit;\n      i0.ɵɵtwoWayBindingSet(item_r4.selected, $event) || (item_r4.selected = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r4 = ctx.$implicit;\n    const i_r5 = ctx.index;\n    i0.ɵɵadvance();\n    i0.ɵɵpropertyInterpolate1(\"name\", \"item\", i_r5, \"\");\n    i0.ɵɵtwoWayProperty(\"ngModel\", item_r4.selected);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate2(\" \", item_r4.CRequirement, \" (\", item_r4.CGroupName, \") \");\n  }\n}\nfunction TemplateViewerComponent_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 11)(1, \"form\", 12);\n    i0.ɵɵlistener(\"ngSubmit\", function TemplateViewerComponent_div_11_Template_form_ngSubmit_1_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.createTemplate());\n    });\n    i0.ɵɵelementStart(2, \"div\", 13)(3, \"label\");\n    i0.ɵɵtext(4, \"\\u6A21\\u677F\\u540D\\u7A31\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"input\", 14);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function TemplateViewerComponent_div_11_Template_input_ngModelChange_5_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r1.newTemplateName, $event) || (ctx_r1.newTemplateName = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 13)(7, \"label\");\n    i0.ɵɵtext(8, \"\\u9078\\u64C7\\u8981\\u5B58\\u6210\\u6A21\\u677F\\u7684\\u8CC7\\u6599\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"div\", 15);\n    i0.ɵɵtemplate(10, TemplateViewerComponent_div_11_div_10_Template, 3, 5, \"div\", 9);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"button\", 16);\n    i0.ɵɵtext(12, \"\\u5132\\u5B58\\u6A21\\u677F\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"button\", 17);\n    i0.ɵɵlistener(\"click\", function TemplateViewerComponent_div_11_Template_button_click_13_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.showCreateTemplate = false);\n    });\n    i0.ɵɵtext(14, \"\\u53D6\\u6D88\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.newTemplateName);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.sharedData);\n  }\n}\nfunction TemplateViewerComponent_tr_23_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\")(6, \"button\", 19);\n    i0.ɵɵlistener(\"click\", function TemplateViewerComponent_tr_23_Template_button_click_6_listener() {\n      const tpl_r7 = i0.ɵɵrestoreView(_r6).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onSelectTemplate(tpl_r7));\n    });\n    i0.ɵɵtext(7, \"\\u67E5\\u770B\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const tpl_r7 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(tpl_r7.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(tpl_r7.description);\n  }\n}\nfunction TemplateViewerComponent_tr_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 20);\n    i0.ɵɵtext(2, \"\\u66AB\\u7121\\u6A21\\u677F\");\n    i0.ɵɵelementEnd()();\n  }\n}\nexport class TemplateViewerComponent {\n  constructor() {\n    this.templates = [];\n    this.sharedData = [];\n    this.addTemplate = new EventEmitter();\n    this.selectTemplate = new EventEmitter();\n    this.saveTemplate = new EventEmitter();\n    this.showCreateTemplate = false;\n    this.newTemplateName = '';\n  }\n  onAddTemplate() {\n    this.addTemplate.emit();\n  }\n  onSelectTemplate(template) {\n    this.selectTemplate.emit(template);\n  }\n  createTemplate() {\n    const selected = (this.sharedData || []).filter(x => x.selected);\n    if (!this.newTemplateName || selected.length === 0) {\n      alert('請輸入模板名稱並選擇資料');\n      return;\n    }\n    const tpl = {\n      name: this.newTemplateName,\n      description: `由${selected.length}筆資料建立`,\n      items: selected.map(x => ({\n        ...x\n      }))\n    };\n    this.saveTemplate.emit(tpl);\n    this.showCreateTemplate = false;\n    this.newTemplateName = '';\n    (this.sharedData || []).forEach(x => x.selected = false);\n  }\n  static {\n    this.ɵfac = function TemplateViewerComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || TemplateViewerComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: TemplateViewerComponent,\n      selectors: [[\"app-template-viewer\"]],\n      inputs: {\n        templates: \"templates\",\n        sharedData: \"sharedData\"\n      },\n      outputs: {\n        addTemplate: \"addTemplate\",\n        selectTemplate: \"selectTemplate\",\n        saveTemplate: \"saveTemplate\"\n      },\n      decls: 25,\n      vars: 3,\n      consts: [[1, \"template-viewer-modal\"], [1, \"template-viewer-header\", \"d-flex\", \"justify-content-between\", \"align-items-center\", \"mb-3\"], [1, \"mb-0\"], [1, \"btn\", \"btn-outline-primary\", \"btn-sm\", \"mr-2\", 3, \"click\"], [1, \"fas\", \"fa-plus\", \"mr-1\"], [1, \"btn\", \"btn-success\", \"btn-sm\", 3, \"click\"], [\"class\", \"mb-3 p-2 border rounded bg-light\", 4, \"ngIf\"], [1, \"template-list\"], [1, \"table\", \"table-bordered\", \"table-hover\"], [4, \"ngFor\", \"ngForOf\"], [4, \"ngIf\"], [1, \"mb-3\", \"p-2\", \"border\", \"rounded\", \"bg-light\"], [3, \"ngSubmit\"], [1, \"form-group\", \"mb-2\"], [\"type\", \"text\", \"name\", \"templateName\", \"required\", \"\", \"maxlength\", \"30\", 1, \"form-control\", 3, \"ngModelChange\", \"ngModel\"], [2, \"max-height\", \"120px\", \"overflow\", \"auto\"], [\"type\", \"submit\", 1, \"btn\", \"btn-primary\", \"btn-sm\", \"mr-2\"], [\"type\", \"button\", 1, \"btn\", \"btn-secondary\", \"btn-sm\", 3, \"click\"], [\"type\", \"checkbox\", 3, \"ngModelChange\", \"ngModel\", \"name\"], [1, \"btn\", \"btn-info\", \"btn-sm\", 3, \"click\"], [\"colspan\", \"3\", 1, \"text-center\"]],\n      template: function TemplateViewerComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"h5\", 2);\n          i0.ɵɵtext(3, \"\\u6A21\\u677F\\u6E05\\u55AE\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(4, \"div\")(5, \"button\", 3);\n          i0.ɵɵlistener(\"click\", function TemplateViewerComponent_Template_button_click_5_listener() {\n            return ctx.showCreateTemplate = !ctx.showCreateTemplate;\n          });\n          i0.ɵɵelement(6, \"i\", 4);\n          i0.ɵɵtext(7, \"\\u5F9E\\u5171\\u7528\\u8CC7\\u6599\\u5EFA\\u7ACB\\u6A21\\u677F \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(8, \"button\", 5);\n          i0.ɵɵlistener(\"click\", function TemplateViewerComponent_Template_button_click_8_listener() {\n            return ctx.onAddTemplate();\n          });\n          i0.ɵɵelement(9, \"i\", 4);\n          i0.ɵɵtext(10, \"\\u65B0\\u589E \");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(11, TemplateViewerComponent_div_11_Template, 15, 2, \"div\", 6);\n          i0.ɵɵelementStart(12, \"div\", 7)(13, \"table\", 8)(14, \"thead\")(15, \"tr\")(16, \"th\");\n          i0.ɵɵtext(17, \"\\u6A21\\u677F\\u540D\\u7A31\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(18, \"th\");\n          i0.ɵɵtext(19, \"\\u63CF\\u8FF0\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(20, \"th\");\n          i0.ɵɵtext(21, \"\\u64CD\\u4F5C\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(22, \"tbody\");\n          i0.ɵɵtemplate(23, TemplateViewerComponent_tr_23_Template, 8, 2, \"tr\", 9)(24, TemplateViewerComponent_tr_24_Template, 3, 0, \"tr\", 10);\n          i0.ɵɵelementEnd()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(11);\n          i0.ɵɵproperty(\"ngIf\", ctx.showCreateTemplate);\n          i0.ɵɵadvance(12);\n          i0.ɵɵproperty(\"ngForOf\", ctx.templates);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.templates || ctx.templates.length === 0);\n        }\n      },\n      styles: [\".template-viewer-modal[_ngcontent-%COMP%] {\\n  background: #fff;\\n  border-radius: 8px;\\n  padding: 1.5rem;\\n  min-width: 400px;\\n  max-width: 600px;\\n}\\n\\n.template-viewer-header[_ngcontent-%COMP%] {\\n  border-bottom: 1px solid #e0e0e0;\\n  padding-bottom: 0.5rem;\\n  margin-bottom: 1rem;\\n}\\n\\n.table[_ngcontent-%COMP%] {\\n  background: #f9f9f9;\\n}\\n/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbInRlbXBsYXRlLXZpZXdlci5jb21wb25lbnQuc2NzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFBQTtFQUNJLGdCQUFBO0VBQ0Esa0JBQUE7RUFDQSxlQUFBO0VBQ0EsZ0JBQUE7RUFDQSxnQkFBQTtBQUNKOztBQUVBO0VBQ0ksZ0NBQUE7RUFDQSxzQkFBQTtFQUNBLG1CQUFBO0FBQ0o7O0FBRUE7RUFDSSxtQkFBQTtBQUNKIiwiZmlsZSI6InRlbXBsYXRlLXZpZXdlci5jb21wb25lbnQuc2NzcyIsInNvdXJjZXNDb250ZW50IjpbIi50ZW1wbGF0ZS12aWV3ZXItbW9kYWwge1xyXG4gICAgYmFja2dyb3VuZDogI2ZmZjtcclxuICAgIGJvcmRlci1yYWRpdXM6IDhweDtcclxuICAgIHBhZGRpbmc6IDEuNXJlbTtcclxuICAgIG1pbi13aWR0aDogNDAwcHg7XHJcbiAgICBtYXgtd2lkdGg6IDYwMHB4O1xyXG59XHJcblxyXG4udGVtcGxhdGUtdmlld2VyLWhlYWRlciB7XHJcbiAgICBib3JkZXItYm90dG9tOiAxcHggc29saWQgI2UwZTBlMDtcclxuICAgIHBhZGRpbmctYm90dG9tOiAwLjVyZW07XHJcbiAgICBtYXJnaW4tYm90dG9tOiAxcmVtO1xyXG59XHJcblxyXG4udGFibGUge1xyXG4gICAgYmFja2dyb3VuZDogI2Y5ZjlmOTtcclxufSJdfQ== */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvc2hhcmVkL2NvbXBvbmVudHMvdGVtcGxhdGUtdmlld2VyL3RlbXBsYXRlLXZpZXdlci5jb21wb25lbnQuc2NzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFBQTtFQUNJLGdCQUFBO0VBQ0Esa0JBQUE7RUFDQSxlQUFBO0VBQ0EsZ0JBQUE7RUFDQSxnQkFBQTtBQUNKOztBQUVBO0VBQ0ksZ0NBQUE7RUFDQSxzQkFBQTtFQUNBLG1CQUFBO0FBQ0o7O0FBRUE7RUFDSSxtQkFBQTtBQUNKO0FBQ0EsNDJCQUE0MkIiLCJzb3VyY2VzQ29udGVudCI6WyIudGVtcGxhdGUtdmlld2VyLW1vZGFsIHtcclxuICAgIGJhY2tncm91bmQ6ICNmZmY7XHJcbiAgICBib3JkZXItcmFkaXVzOiA4cHg7XHJcbiAgICBwYWRkaW5nOiAxLjVyZW07XHJcbiAgICBtaW4td2lkdGg6IDQwMHB4O1xyXG4gICAgbWF4LXdpZHRoOiA2MDBweDtcclxufVxyXG5cclxuLnRlbXBsYXRlLXZpZXdlci1oZWFkZXIge1xyXG4gICAgYm9yZGVyLWJvdHRvbTogMXB4IHNvbGlkICNlMGUwZTA7XHJcbiAgICBwYWRkaW5nLWJvdHRvbTogMC41cmVtO1xyXG4gICAgbWFyZ2luLWJvdHRvbTogMXJlbTtcclxufVxyXG5cclxuLnRhYmxlIHtcclxuICAgIGJhY2tncm91bmQ6ICNmOWY5Zjk7XHJcbn0iXSwic291cmNlUm9vdCI6IiJ9 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["EventEmitter", "i0", "ɵɵelementStart", "ɵɵtwoWayListener", "TemplateViewerComponent_div_11_div_10_Template_input_ngModelChange_1_listener", "$event", "item_r4", "ɵɵrestoreView", "_r3", "$implicit", "ɵɵtwoWayBindingSet", "selected", "ɵɵresetView", "ɵɵelementEnd", "ɵɵtext", "ɵɵadvance", "ɵɵpropertyInterpolate1", "i_r5", "ɵɵtwoWayProperty", "ɵɵtextInterpolate2", "CRequirement", "CGroupName", "ɵɵlistener", "TemplateViewerComponent_div_11_Template_form_ngSubmit_1_listener", "_r1", "ctx_r1", "ɵɵnextContext", "createTemplate", "TemplateViewerComponent_div_11_Template_input_ngModelChange_5_listener", "newTemplateName", "ɵɵtemplate", "TemplateViewerComponent_div_11_div_10_Template", "TemplateViewerComponent_div_11_Template_button_click_13_listener", "showCreateTemplate", "ɵɵproperty", "sharedData", "TemplateViewerComponent_tr_23_Template_button_click_6_listener", "tpl_r7", "_r6", "onSelectTemplate", "ɵɵtextInterpolate", "name", "description", "TemplateViewerComponent", "constructor", "templates", "addTemplate", "selectTemplate", "saveTemplate", "onAddTemplate", "emit", "template", "filter", "x", "length", "alert", "tpl", "items", "map", "for<PERSON>ach", "selectors", "inputs", "outputs", "decls", "vars", "consts", "TemplateViewerComponent_Template", "rf", "ctx", "TemplateViewerComponent_Template_button_click_5_listener", "ɵɵelement", "TemplateViewerComponent_Template_button_click_8_listener", "TemplateViewerComponent_div_11_Template", "TemplateViewerComponent_tr_23_Template", "TemplateViewerComponent_tr_24_Template"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\shared\\components\\template-viewer\\template-viewer.component.ts", "C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\shared\\components\\template-viewer\\template-viewer.component.html"], "sourcesContent": ["import { Component, EventEmitter, Input, Output } from '@angular/core';\r\n\r\n@Component({\r\n  selector: 'app-template-viewer',\r\n  templateUrl: './template-viewer.component.html',\r\n  styleUrls: ['./template-viewer.component.scss']\r\n})\r\nexport class TemplateViewerComponent {\r\n  @Input() templates: any[] = [];\r\n  @Input() sharedData: any[] = [];\r\n  @Output() addTemplate = new EventEmitter<void>();\r\n  @Output() selectTemplate = new EventEmitter<any>();\r\n  @Output() saveTemplate = new EventEmitter<any>();\r\n\r\n  showCreateTemplate = false;\r\n  newTemplateName = '';\r\n\r\n  onAddTemplate() {\r\n    this.addTemplate.emit();\r\n  }\r\n\r\n  onSelectTemplate(template: any) {\r\n    this.selectTemplate.emit(template);\r\n  }\r\n\r\n  createTemplate() {\r\n    const selected = (this.sharedData || []).filter(x => x.selected);\r\n    if (!this.newTemplateName || selected.length === 0) {\r\n      alert('請輸入模板名稱並選擇資料');\r\n      return;\r\n    }\r\n    const tpl = {\r\n      name: this.newTemplateName,\r\n      description: `由${selected.length}筆資料建立`,\r\n      items: selected.map(x => ({ ...x }))\r\n    };\r\n    this.saveTemplate.emit(tpl);\r\n    this.showCreateTemplate = false;\r\n    this.newTemplateName = '';\r\n    (this.sharedData || []).forEach(x => x.selected = false);\r\n  }\r\n}\r\n\r\n// DB 對應型別\r\nexport interface Template {\r\n  TemplateID?: number;\r\n  TemplateName: string;\r\n  Description?: string;\r\n}\r\nexport interface TemplateDetail {\r\n  TemplateDetailID?: number;\r\n  TemplateID: number;\r\n  RefID: number; // 關聯主檔ID\r\n  ModuleType: string;\r\n  FieldName: string;\r\n  FieldValue: string;\r\n}\r\n", "<div class=\"template-viewer-modal\">\r\n  <div class=\"template-viewer-header d-flex justify-content-between align-items-center mb-3\">\r\n    <h5 class=\"mb-0\">模板清單</h5>\r\n    <div>\r\n      <button class=\"btn btn-outline-primary btn-sm mr-2\" (click)=\"showCreateTemplate = !showCreateTemplate\">\r\n        <i class=\"fas fa-plus mr-1\"></i>從共用資料建立模板\r\n      </button>\r\n      <button class=\"btn btn-success btn-sm\" (click)=\"onAddTemplate()\">\r\n        <i class=\"fas fa-plus mr-1\"></i>新增\r\n      </button>\r\n    </div>\r\n  </div>\r\n\r\n  <!-- 建立模板區塊 -->\r\n  <div *ngIf=\"showCreateTemplate\" class=\"mb-3 p-2 border rounded bg-light\">\r\n    <form (ngSubmit)=\"createTemplate()\">\r\n      <div class=\"form-group mb-2\">\r\n        <label>模板名稱</label>\r\n        <input type=\"text\" class=\"form-control\" [(ngModel)]=\"newTemplateName\" name=\"templateName\" required\r\n          maxlength=\"30\">\r\n      </div>\r\n      <div class=\"form-group mb-2\">\r\n        <label>選擇要存成模板的資料</label>\r\n        <div style=\"max-height:120px;overflow:auto;\">\r\n          <div *ngFor=\"let item of sharedData; let i = index\">\r\n            <input type=\"checkbox\" [(ngModel)]=\"item.selected\" name=\"item{{i}}\"> {{item.CRequirement}}\r\n            ({{item.CGroupName}})\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <button class=\"btn btn-primary btn-sm mr-2\" type=\"submit\">儲存模板</button>\r\n      <button class=\"btn btn-secondary btn-sm\" type=\"button\" (click)=\"showCreateTemplate = false\">取消</button>\r\n    </form>\r\n  </div>\r\n\r\n  <div class=\"template-list\">\r\n    <table class=\"table table-bordered table-hover\">\r\n      <thead>\r\n        <tr>\r\n          <th>模板名稱</th>\r\n          <th>描述</th>\r\n          <th>操作</th>\r\n        </tr>\r\n      </thead>\r\n      <tbody>\r\n        <tr *ngFor=\"let tpl of templates\">\r\n          <td>{{ tpl.name }}</td>\r\n          <td>{{ tpl.description }}</td>\r\n          <td>\r\n            <button class=\"btn btn-info btn-sm\" (click)=\"onSelectTemplate(tpl)\">查看</button>\r\n          </td>\r\n        </tr>\r\n        <tr *ngIf=\"!templates || templates.length === 0\">\r\n          <td colspan=\"3\" class=\"text-center\">暫無模板</td>\r\n        </tr>\r\n      </tbody>\r\n    </table>\r\n  </div>\r\n</div>"], "mappings": "AAAA,SAAoBA,YAAY,QAAuB,eAAe;;;;;ICyB1DC,EADF,CAAAC,cAAA,UAAoD,gBACkB;IAA7CD,EAAA,CAAAE,gBAAA,2BAAAC,8EAAAC,MAAA;MAAA,MAAAC,OAAA,GAAAL,EAAA,CAAAM,aAAA,CAAAC,GAAA,EAAAC,SAAA;MAAAR,EAAA,CAAAS,kBAAA,CAAAJ,OAAA,CAAAK,QAAA,EAAAN,MAAA,MAAAC,OAAA,CAAAK,QAAA,GAAAN,MAAA;MAAA,OAAAJ,EAAA,CAAAW,WAAA,CAAAP,MAAA;IAAA,EAA2B;IAAlDJ,EAAA,CAAAY,YAAA,EAAoE;IAACZ,EAAA,CAAAa,MAAA,GAEvE;IAAAb,EAAA,CAAAY,YAAA,EAAM;;;;;IAF+CZ,EAAA,CAAAc,SAAA,EAAgB;IAAhBd,EAAA,CAAAe,sBAAA,iBAAAC,IAAA,KAAgB;IAA5ChB,EAAA,CAAAiB,gBAAA,YAAAZ,OAAA,CAAAK,QAAA,CAA2B;IAAmBV,EAAA,CAAAc,SAAA,EAEvE;IAFuEd,EAAA,CAAAkB,kBAAA,MAAAb,OAAA,CAAAc,YAAA,QAAAd,OAAA,CAAAe,UAAA,OAEvE;;;;;;IAZNpB,EADF,CAAAC,cAAA,cAAyE,eACnC;IAA9BD,EAAA,CAAAqB,UAAA,sBAAAC,iEAAA;MAAAtB,EAAA,CAAAM,aAAA,CAAAiB,GAAA;MAAA,MAAAC,MAAA,GAAAxB,EAAA,CAAAyB,aAAA;MAAA,OAAAzB,EAAA,CAAAW,WAAA,CAAYa,MAAA,CAAAE,cAAA,EAAgB;IAAA,EAAC;IAE/B1B,EADF,CAAAC,cAAA,cAA6B,YACpB;IAAAD,EAAA,CAAAa,MAAA,+BAAI;IAAAb,EAAA,CAAAY,YAAA,EAAQ;IACnBZ,EAAA,CAAAC,cAAA,gBACiB;IADuBD,EAAA,CAAAE,gBAAA,2BAAAyB,uEAAAvB,MAAA;MAAAJ,EAAA,CAAAM,aAAA,CAAAiB,GAAA;MAAA,MAAAC,MAAA,GAAAxB,EAAA,CAAAyB,aAAA;MAAAzB,EAAA,CAAAS,kBAAA,CAAAe,MAAA,CAAAI,eAAA,EAAAxB,MAAA,MAAAoB,MAAA,CAAAI,eAAA,GAAAxB,MAAA;MAAA,OAAAJ,EAAA,CAAAW,WAAA,CAAAP,MAAA;IAAA,EAA6B;IAEvEJ,EAFE,CAAAY,YAAA,EACiB,EACb;IAEJZ,EADF,CAAAC,cAAA,cAA6B,YACpB;IAAAD,EAAA,CAAAa,MAAA,mEAAU;IAAAb,EAAA,CAAAY,YAAA,EAAQ;IACzBZ,EAAA,CAAAC,cAAA,cAA6C;IAC3CD,EAAA,CAAA6B,UAAA,KAAAC,8CAAA,iBAAoD;IAKxD9B,EADE,CAAAY,YAAA,EAAM,EACF;IACNZ,EAAA,CAAAC,cAAA,kBAA0D;IAAAD,EAAA,CAAAa,MAAA,gCAAI;IAAAb,EAAA,CAAAY,YAAA,EAAS;IACvEZ,EAAA,CAAAC,cAAA,kBAA4F;IAArCD,EAAA,CAAAqB,UAAA,mBAAAU,iEAAA;MAAA/B,EAAA,CAAAM,aAAA,CAAAiB,GAAA;MAAA,MAAAC,MAAA,GAAAxB,EAAA,CAAAyB,aAAA;MAAA,OAAAzB,EAAA,CAAAW,WAAA,CAAAa,MAAA,CAAAQ,kBAAA,GAA8B,KAAK;IAAA,EAAC;IAAChC,EAAA,CAAAa,MAAA,oBAAE;IAElGb,EAFkG,CAAAY,YAAA,EAAS,EAClG,EACH;;;;IAfwCZ,EAAA,CAAAc,SAAA,GAA6B;IAA7Bd,EAAA,CAAAiB,gBAAA,YAAAO,MAAA,CAAAI,eAAA,CAA6B;IAM7C5B,EAAA,CAAAc,SAAA,GAAe;IAAfd,EAAA,CAAAiC,UAAA,YAAAT,MAAA,CAAAU,UAAA,CAAe;;;;;;IAsBrClC,EADF,CAAAC,cAAA,SAAkC,SAC5B;IAAAD,EAAA,CAAAa,MAAA,GAAc;IAAAb,EAAA,CAAAY,YAAA,EAAK;IACvBZ,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAa,MAAA,GAAqB;IAAAb,EAAA,CAAAY,YAAA,EAAK;IAE5BZ,EADF,CAAAC,cAAA,SAAI,iBACkE;IAAhCD,EAAA,CAAAqB,UAAA,mBAAAc,+DAAA;MAAA,MAAAC,MAAA,GAAApC,EAAA,CAAAM,aAAA,CAAA+B,GAAA,EAAA7B,SAAA;MAAA,MAAAgB,MAAA,GAAAxB,EAAA,CAAAyB,aAAA;MAAA,OAAAzB,EAAA,CAAAW,WAAA,CAASa,MAAA,CAAAc,gBAAA,CAAAF,MAAA,CAAqB;IAAA,EAAC;IAACpC,EAAA,CAAAa,MAAA,mBAAE;IAE1Eb,EAF0E,CAAAY,YAAA,EAAS,EAC5E,EACF;;;;IALCZ,EAAA,CAAAc,SAAA,GAAc;IAAdd,EAAA,CAAAuC,iBAAA,CAAAH,MAAA,CAAAI,IAAA,CAAc;IACdxC,EAAA,CAAAc,SAAA,GAAqB;IAArBd,EAAA,CAAAuC,iBAAA,CAAAH,MAAA,CAAAK,WAAA,CAAqB;;;;;IAMzBzC,EADF,CAAAC,cAAA,SAAiD,aACX;IAAAD,EAAA,CAAAa,MAAA,+BAAI;IAC1Cb,EAD0C,CAAAY,YAAA,EAAK,EAC1C;;;AD/Cb,OAAM,MAAO8B,uBAAuB;EALpCC,YAAA;IAMW,KAAAC,SAAS,GAAU,EAAE;IACrB,KAAAV,UAAU,GAAU,EAAE;IACrB,KAAAW,WAAW,GAAG,IAAI9C,YAAY,EAAQ;IACtC,KAAA+C,cAAc,GAAG,IAAI/C,YAAY,EAAO;IACxC,KAAAgD,YAAY,GAAG,IAAIhD,YAAY,EAAO;IAEhD,KAAAiC,kBAAkB,GAAG,KAAK;IAC1B,KAAAJ,eAAe,GAAG,EAAE;;EAEpBoB,aAAaA,CAAA;IACX,IAAI,CAACH,WAAW,CAACI,IAAI,EAAE;EACzB;EAEAX,gBAAgBA,CAACY,QAAa;IAC5B,IAAI,CAACJ,cAAc,CAACG,IAAI,CAACC,QAAQ,CAAC;EACpC;EAEAxB,cAAcA,CAAA;IACZ,MAAMhB,QAAQ,GAAG,CAAC,IAAI,CAACwB,UAAU,IAAI,EAAE,EAAEiB,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAC1C,QAAQ,CAAC;IAChE,IAAI,CAAC,IAAI,CAACkB,eAAe,IAAIlB,QAAQ,CAAC2C,MAAM,KAAK,CAAC,EAAE;MAClDC,KAAK,CAAC,cAAc,CAAC;MACrB;IACF;IACA,MAAMC,GAAG,GAAG;MACVf,IAAI,EAAE,IAAI,CAACZ,eAAe;MAC1Ba,WAAW,EAAE,IAAI/B,QAAQ,CAAC2C,MAAM,OAAO;MACvCG,KAAK,EAAE9C,QAAQ,CAAC+C,GAAG,CAACL,CAAC,KAAK;QAAE,GAAGA;MAAC,CAAE,CAAC;KACpC;IACD,IAAI,CAACL,YAAY,CAACE,IAAI,CAACM,GAAG,CAAC;IAC3B,IAAI,CAACvB,kBAAkB,GAAG,KAAK;IAC/B,IAAI,CAACJ,eAAe,GAAG,EAAE;IACzB,CAAC,IAAI,CAACM,UAAU,IAAI,EAAE,EAAEwB,OAAO,CAACN,CAAC,IAAIA,CAAC,CAAC1C,QAAQ,GAAG,KAAK,CAAC;EAC1D;;;uCAjCWgC,uBAAuB;IAAA;EAAA;;;YAAvBA,uBAAuB;MAAAiB,SAAA;MAAAC,MAAA;QAAAhB,SAAA;QAAAV,UAAA;MAAA;MAAA2B,OAAA;QAAAhB,WAAA;QAAAC,cAAA;QAAAC,YAAA;MAAA;MAAAe,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAd,QAAA,WAAAe,iCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCLhClE,EAFJ,CAAAC,cAAA,aAAmC,aAC0D,YACxE;UAAAD,EAAA,CAAAa,MAAA,+BAAI;UAAAb,EAAA,CAAAY,YAAA,EAAK;UAExBZ,EADF,CAAAC,cAAA,UAAK,gBACoG;UAAnDD,EAAA,CAAAqB,UAAA,mBAAA+C,yDAAA;YAAA,OAAAD,GAAA,CAAAnC,kBAAA,IAAAmC,GAAA,CAAAnC,kBAAA;UAAA,EAAkD;UACpGhC,EAAA,CAAAqE,SAAA,WAAgC;UAAArE,EAAA,CAAAa,MAAA,8DAClC;UAAAb,EAAA,CAAAY,YAAA,EAAS;UACTZ,EAAA,CAAAC,cAAA,gBAAiE;UAA1BD,EAAA,CAAAqB,UAAA,mBAAAiD,yDAAA;YAAA,OAASH,GAAA,CAAAnB,aAAA,EAAe;UAAA,EAAC;UAC9DhD,EAAA,CAAAqE,SAAA,WAAgC;UAAArE,EAAA,CAAAa,MAAA,qBAClC;UAEJb,EAFI,CAAAY,YAAA,EAAS,EACL,EACF;UAGNZ,EAAA,CAAA6B,UAAA,KAAA0C,uCAAA,kBAAyE;UAyBjEvE,EAJR,CAAAC,cAAA,cAA2B,gBACuB,aACvC,UACD,UACE;UAAAD,EAAA,CAAAa,MAAA,gCAAI;UAAAb,EAAA,CAAAY,YAAA,EAAK;UACbZ,EAAA,CAAAC,cAAA,UAAI;UAAAD,EAAA,CAAAa,MAAA,oBAAE;UAAAb,EAAA,CAAAY,YAAA,EAAK;UACXZ,EAAA,CAAAC,cAAA,UAAI;UAAAD,EAAA,CAAAa,MAAA,oBAAE;UAEVb,EAFU,CAAAY,YAAA,EAAK,EACR,EACC;UACRZ,EAAA,CAAAC,cAAA,aAAO;UAQLD,EAPA,CAAA6B,UAAA,KAAA2C,sCAAA,gBAAkC,KAAAC,sCAAA,iBAOe;UAMzDzE,EAHM,CAAAY,YAAA,EAAQ,EACF,EACJ,EACF;;;UA5CEZ,EAAA,CAAAc,SAAA,IAAwB;UAAxBd,EAAA,CAAAiC,UAAA,SAAAkC,GAAA,CAAAnC,kBAAA,CAAwB;UA+BJhC,EAAA,CAAAc,SAAA,IAAY;UAAZd,EAAA,CAAAiC,UAAA,YAAAkC,GAAA,CAAAvB,SAAA,CAAY;UAO3B5C,EAAA,CAAAc,SAAA,EAA0C;UAA1Cd,EAAA,CAAAiC,UAAA,UAAAkC,GAAA,CAAAvB,SAAA,IAAAuB,GAAA,CAAAvB,SAAA,CAAAS,MAAA,OAA0C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}