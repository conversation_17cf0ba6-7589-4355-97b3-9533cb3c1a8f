{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { BaseLabelDirective } from '../../directives/label.directive';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nconst _c0 = [\"*\"];\nconst _c1 = a0 => ({\n  \"required-field\": a0\n});\nexport class FormGroupComponent {\n  static {\n    this.ɵfac = function FormGroupComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || FormGroupComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: FormGroupComponent,\n      selectors: [[\"app-form-group\"]],\n      inputs: {\n        label: \"label\",\n        labelFor: \"labelFor\",\n        isRequired: \"isRequired\"\n      },\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      ngContentSelectors: _c0,\n      decls: 3,\n      vars: 5,\n      consts: [[\"baseLabel\", \"\", 1, \"col-4\", 3, \"for\", \"ngClass\"]],\n      template: function FormGroupComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef();\n          i0.ɵɵelementStart(0, \"label\", 0);\n          i0.ɵɵtext(1);\n          i0.ɵɵelementEnd();\n          i0.ɵɵprojection(2);\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"for\", ctx.labelFor)(\"ngClass\", i0.ɵɵpureFunction1(3, _c1, ctx.isRequired));\n          i0.ɵɵadvance();\n          i0.ɵɵtextInterpolate(ctx.label);\n        }\n      },\n      dependencies: [CommonModule, i1.NgClass, FormsModule, BaseLabelDirective],\n      styles: [\"[_nghost-%COMP%] {\\n  margin-bottom: 1rem;\\n  display: flex;\\n  align-items: center;\\n  width: 100%;\\n}\\n/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImZvcm0tZ3JvdXAuY29tcG9uZW50LnNjc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUE7RUFDRSxtQkFBQTtFQUNBLGFBQUE7RUFDQSxtQkFBQTtFQUNBLFdBQUE7QUFDRiIsImZpbGUiOiJmb3JtLWdyb3VwLmNvbXBvbmVudC5zY3NzIiwic291cmNlc0NvbnRlbnQiOlsiOmhvc3Qge1xyXG4gIG1hcmdpbi1ib3R0b206IDFyZW07XHJcbiAgZGlzcGxheTogZmxleDtcclxuICBhbGlnbi1pdGVtczogY2VudGVyO1xyXG4gIHdpZHRoOiAxMDAlO1xyXG59Il19 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvQHRoZW1lL2NvbXBvbmVudHMvZm9ybS1ncm91cC9mb3JtLWdyb3VwLmNvbXBvbmVudC5zY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUFBO0VBQ0UsbUJBQUE7RUFDQSxhQUFBO0VBQ0EsbUJBQUE7RUFDQSxXQUFBO0FBQ0Y7QUFDQSxvYkFBb2IiLCJzb3VyY2VzQ29udGVudCI6WyI6aG9zdCB7XHJcbiAgbWFyZ2luLWJvdHRvbTogMXJlbTtcclxuICBkaXNwbGF5OiBmbGV4O1xyXG4gIGFsaWduLWl0ZW1zOiBjZW50ZXI7XHJcbiAgd2lkdGg6IDEwMCU7XHJcbn0iXSwic291cmNlUm9vdCI6IiJ9 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "FormsModule", "BaseLabelDirective", "FormGroupComponent", "selectors", "inputs", "label", "labelFor", "isRequired", "standalone", "features", "i0", "ɵɵStandaloneFeature", "ngContentSelectors", "_c0", "decls", "vars", "consts", "template", "FormGroupComponent_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵprojection", "ɵɵproperty", "ɵɵpureFunction1", "_c1", "ɵɵadvance", "ɵɵtextInterpolate", "i1", "Ng<PERSON><PERSON>", "styles"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\@theme\\components\\form-group\\form-group.component.ts", "C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\@theme\\components\\form-group\\form-group.component.html"], "sourcesContent": ["import { Component, EventEmitter, Input, Output } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { FormsModule } from '@angular/forms';\r\nimport { BaseLabelDirective } from '../../directives/label.directive';\r\n\r\n@Component({\r\n  selector: 'app-form-group',\r\n  standalone: true,\r\n  imports: [CommonModule, FormsModule, BaseLabelDirective],\r\n  templateUrl: './form-group.component.html',\r\n  styleUrls: ['./form-group.component.scss']\r\n})\r\nexport class FormGroupComponent {\r\n\r\n  @Input() label: string;\r\n  @Input() labelFor: string;\r\n  @Input() isRequired?: boolean;\r\n\r\n  \r\n}\r\n", "<label [for]=\"labelFor\" class=\"mr-2\" [ngClass]=\"{'required-field': isRequired}\" class=\"col-4\"\r\n  baseLabel>{{label}}</label>\r\n<ng-content></ng-content>"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,kBAAkB,QAAQ,kCAAkC;;;;;;;AASrE,OAAM,MAAOC,kBAAkB;;;uCAAlBA,kBAAkB;IAAA;EAAA;;;YAAlBA,kBAAkB;MAAAC,SAAA;MAAAC,MAAA;QAAAC,KAAA;QAAAC,QAAA;QAAAC,UAAA;MAAA;MAAAC,UAAA;MAAAC,QAAA,GAAAC,EAAA,CAAAC,mBAAA;MAAAC,kBAAA,EAAAC,GAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,4BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;UCZ/BT,EAAA,CAAAW,cAAA,eACY;UAAAX,EAAA,CAAAY,MAAA,GAAS;UAAAZ,EAAA,CAAAa,YAAA,EAAQ;UAC7Bb,EAAA,CAAAc,YAAA,GAAyB;;;UAFYd,EAA9B,CAAAe,UAAA,QAAAL,GAAA,CAAAd,QAAA,CAAgB,YAAAI,EAAA,CAAAgB,eAAA,IAAAC,GAAA,EAAAP,GAAA,CAAAb,UAAA,EAAwD;UACnEG,EAAA,CAAAkB,SAAA,EAAS;UAATlB,EAAA,CAAAmB,iBAAA,CAAAT,GAAA,CAAAf,KAAA,CAAS;;;qBDOTN,YAAY,EAAA+B,EAAA,CAAAC,OAAA,EAAE/B,WAAW,EAAEC,kBAAkB;MAAA+B,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}