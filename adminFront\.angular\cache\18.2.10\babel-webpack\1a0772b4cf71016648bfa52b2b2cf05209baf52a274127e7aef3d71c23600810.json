{"ast": null, "code": "//! moment.js locale configuration\n//! locale : Persian [fa]\n//! author : <PERSON><PERSON><PERSON> : https://github.com/ebraminio\n\n;\n(function (global, factory) {\n  typeof exports === 'object' && typeof module !== 'undefined' && typeof require === 'function' ? factory(require('../moment')) : typeof define === 'function' && define.amd ? define(['../moment'], factory) : factory(global.moment);\n})(this, function (moment) {\n  'use strict';\n\n  //! moment.js locale configuration\n  var symbolMap = {\n      1: '۱',\n      2: '۲',\n      3: '۳',\n      4: '۴',\n      5: '۵',\n      6: '۶',\n      7: '۷',\n      8: '۸',\n      9: '۹',\n      0: '۰'\n    },\n    numberMap = {\n      '۱': '1',\n      '۲': '2',\n      '۳': '3',\n      '۴': '4',\n      '۵': '5',\n      '۶': '6',\n      '۷': '7',\n      '۸': '8',\n      '۹': '9',\n      '۰': '0'\n    };\n  var fa = moment.define<PERSON><PERSON>ale('fa', {\n    months: 'ژانویه_فوریه_مارس_آوریل_مه_ژوئن_ژوئیه_اوت_سپتامبر_اکتبر_نوامبر_دسامبر'.split('_'),\n    monthsShort: 'ژانویه_فوریه_مارس_آوریل_مه_ژوئن_ژوئیه_اوت_سپتامبر_اکتبر_نوامبر_دسامبر'.split('_'),\n    weekdays: 'یک\\u200cشنبه_دوشنبه_سه\\u200cشنبه_چهارشنبه_پنج\\u200cشنبه_جمعه_شنبه'.split('_'),\n    weekdaysShort: 'یک\\u200cشنبه_دوشنبه_سه\\u200cشنبه_چهارشنبه_پنج\\u200cشنبه_جمعه_شنبه'.split('_'),\n    weekdaysMin: 'ی_د_س_چ_پ_ج_ش'.split('_'),\n    weekdaysParseExact: true,\n    longDateFormat: {\n      LT: 'HH:mm',\n      LTS: 'HH:mm:ss',\n      L: 'DD/MM/YYYY',\n      LL: 'D MMMM YYYY',\n      LLL: 'D MMMM YYYY HH:mm',\n      LLLL: 'dddd, D MMMM YYYY HH:mm'\n    },\n    meridiemParse: /قبل از ظهر|بعد از ظهر/,\n    isPM: function (input) {\n      return /بعد از ظهر/.test(input);\n    },\n    meridiem: function (hour, minute, isLower) {\n      if (hour < 12) {\n        return 'قبل از ظهر';\n      } else {\n        return 'بعد از ظهر';\n      }\n    },\n    calendar: {\n      sameDay: '[امروز ساعت] LT',\n      nextDay: '[فردا ساعت] LT',\n      nextWeek: 'dddd [ساعت] LT',\n      lastDay: '[دیروز ساعت] LT',\n      lastWeek: 'dddd [پیش] [ساعت] LT',\n      sameElse: 'L'\n    },\n    relativeTime: {\n      future: 'در %s',\n      past: '%s پیش',\n      s: 'چند ثانیه',\n      ss: '%d ثانیه',\n      m: 'یک دقیقه',\n      mm: '%d دقیقه',\n      h: 'یک ساعت',\n      hh: '%d ساعت',\n      d: 'یک روز',\n      dd: '%d روز',\n      M: 'یک ماه',\n      MM: '%d ماه',\n      y: 'یک سال',\n      yy: '%d سال'\n    },\n    preparse: function (string) {\n      return string.replace(/[۰-۹]/g, function (match) {\n        return numberMap[match];\n      }).replace(/،/g, ',');\n    },\n    postformat: function (string) {\n      return string.replace(/\\d/g, function (match) {\n        return symbolMap[match];\n      }).replace(/,/g, '،');\n    },\n    dayOfMonthOrdinalParse: /\\d{1,2}م/,\n    ordinal: '%dم',\n    week: {\n      dow: 6,\n      // Saturday is the first day of the week.\n      doy: 12 // The week that contains Jan 12th is the first week of the year.\n    }\n  });\n  return fa;\n});", "map": {"version": 3, "names": ["global", "factory", "exports", "module", "require", "define", "amd", "moment", "symbolMap", "numberMap", "fa", "defineLocale", "months", "split", "monthsShort", "weekdays", "weekdaysShort", "weekdaysMin", "weekdaysParseExact", "longDateFormat", "LT", "LTS", "L", "LL", "LLL", "LLLL", "meridiemParse", "isPM", "input", "test", "meridiem", "hour", "minute", "isLower", "calendar", "sameDay", "nextDay", "nextWeek", "lastDay", "lastWeek", "same<PERSON><PERSON><PERSON>", "relativeTime", "future", "past", "s", "ss", "m", "mm", "h", "hh", "d", "dd", "M", "MM", "y", "yy", "preparse", "string", "replace", "match", "postformat", "dayOfMonthOrdinalParse", "ordinal", "week", "dow", "doy"], "sources": ["C:/Users/<USER>/Documents/salechange-product/adminFront/node_modules/moment/locale/fa.js"], "sourcesContent": ["//! moment.js locale configuration\n//! locale : Persian [fa]\n//! author : <PERSON><PERSON><PERSON> : https://github.com/ebraminio\n\n;(function (global, factory) {\n   typeof exports === 'object' && typeof module !== 'undefined'\n       && typeof require === 'function' ? factory(require('../moment')) :\n   typeof define === 'function' && define.amd ? define(['../moment'], factory) :\n   factory(global.moment)\n}(this, (function (moment) { 'use strict';\n\n    //! moment.js locale configuration\n\n    var symbolMap = {\n            1: '۱',\n            2: '۲',\n            3: '۳',\n            4: '۴',\n            5: '۵',\n            6: '۶',\n            7: '۷',\n            8: '۸',\n            9: '۹',\n            0: '۰',\n        },\n        numberMap = {\n            '۱': '1',\n            '۲': '2',\n            '۳': '3',\n            '۴': '4',\n            '۵': '5',\n            '۶': '6',\n            '۷': '7',\n            '۸': '8',\n            '۹': '9',\n            '۰': '0',\n        };\n\n    var fa = moment.define<PERSON><PERSON>ale('fa', {\n        months: 'ژانویه_فوریه_مارس_آوریل_مه_ژوئن_ژوئیه_اوت_سپتامبر_اکتبر_نوامبر_دسامبر'.split(\n            '_'\n        ),\n        monthsShort:\n            'ژانویه_فوریه_مارس_آوریل_مه_ژوئن_ژوئیه_اوت_سپتامبر_اکتبر_نوامبر_دسامبر'.split(\n                '_'\n            ),\n        weekdays:\n            'یک\\u200cشنبه_دوشنبه_سه\\u200cشنبه_چهارشنبه_پنج\\u200cشنبه_جمعه_شنبه'.split(\n                '_'\n            ),\n        weekdaysShort:\n            'یک\\u200cشنبه_دوشنبه_سه\\u200cشنبه_چهارشنبه_پنج\\u200cشنبه_جمعه_شنبه'.split(\n                '_'\n            ),\n        weekdaysMin: 'ی_د_س_چ_پ_ج_ش'.split('_'),\n        weekdaysParseExact: true,\n        longDateFormat: {\n            LT: 'HH:mm',\n            LTS: 'HH:mm:ss',\n            L: 'DD/MM/YYYY',\n            LL: 'D MMMM YYYY',\n            LLL: 'D MMMM YYYY HH:mm',\n            LLLL: 'dddd, D MMMM YYYY HH:mm',\n        },\n        meridiemParse: /قبل از ظهر|بعد از ظهر/,\n        isPM: function (input) {\n            return /بعد از ظهر/.test(input);\n        },\n        meridiem: function (hour, minute, isLower) {\n            if (hour < 12) {\n                return 'قبل از ظهر';\n            } else {\n                return 'بعد از ظهر';\n            }\n        },\n        calendar: {\n            sameDay: '[امروز ساعت] LT',\n            nextDay: '[فردا ساعت] LT',\n            nextWeek: 'dddd [ساعت] LT',\n            lastDay: '[دیروز ساعت] LT',\n            lastWeek: 'dddd [پیش] [ساعت] LT',\n            sameElse: 'L',\n        },\n        relativeTime: {\n            future: 'در %s',\n            past: '%s پیش',\n            s: 'چند ثانیه',\n            ss: '%d ثانیه',\n            m: 'یک دقیقه',\n            mm: '%d دقیقه',\n            h: 'یک ساعت',\n            hh: '%d ساعت',\n            d: 'یک روز',\n            dd: '%d روز',\n            M: 'یک ماه',\n            MM: '%d ماه',\n            y: 'یک سال',\n            yy: '%d سال',\n        },\n        preparse: function (string) {\n            return string\n                .replace(/[۰-۹]/g, function (match) {\n                    return numberMap[match];\n                })\n                .replace(/،/g, ',');\n        },\n        postformat: function (string) {\n            return string\n                .replace(/\\d/g, function (match) {\n                    return symbolMap[match];\n                })\n                .replace(/,/g, '،');\n        },\n        dayOfMonthOrdinalParse: /\\d{1,2}م/,\n        ordinal: '%dم',\n        week: {\n            dow: 6, // Saturday is the first day of the week.\n            doy: 12, // The week that contains Jan 12th is the first week of the year.\n        },\n    });\n\n    return fa;\n\n})));\n"], "mappings": "AAAA;AACA;AACA;;AAEA;AAAE,WAAUA,MAAM,EAAEC,OAAO,EAAE;EAC1B,OAAOC,OAAO,KAAK,QAAQ,IAAI,OAAOC,MAAM,KAAK,WAAW,IACrD,OAAOC,OAAO,KAAK,UAAU,GAAGH,OAAO,CAACG,OAAO,CAAC,WAAW,CAAC,CAAC,GACpE,OAAOC,MAAM,KAAK,UAAU,IAAIA,MAAM,CAACC,GAAG,GAAGD,MAAM,CAAC,CAAC,WAAW,CAAC,EAAEJ,OAAO,CAAC,GAC3EA,OAAO,CAACD,MAAM,CAACO,MAAM,CAAC;AACzB,CAAC,EAAC,IAAI,EAAG,UAAUA,MAAM,EAAE;EAAE,YAAY;;EAErC;EAEA,IAAIC,SAAS,GAAG;MACR,CAAC,EAAE,GAAG;MACN,CAAC,EAAE,GAAG;MACN,CAAC,EAAE,GAAG;MACN,CAAC,EAAE,GAAG;MACN,CAAC,EAAE,GAAG;MACN,CAAC,EAAE,GAAG;MACN,CAAC,EAAE,GAAG;MACN,CAAC,EAAE,GAAG;MACN,CAAC,EAAE,GAAG;MACN,CAAC,EAAE;IACP,CAAC;IACDC,SAAS,GAAG;MACR,GAAG,EAAE,GAAG;MACR,GAAG,EAAE,GAAG;MACR,GAAG,EAAE,GAAG;MACR,GAAG,EAAE,GAAG;MACR,GAAG,EAAE,GAAG;MACR,GAAG,EAAE,GAAG;MACR,GAAG,EAAE,GAAG;MACR,GAAG,EAAE,GAAG;MACR,GAAG,EAAE,GAAG;MACR,GAAG,EAAE;IACT,CAAC;EAEL,IAAIC,EAAE,GAAGH,MAAM,CAACI,YAAY,CAAC,IAAI,EAAE;IAC/BC,MAAM,EAAE,uEAAuE,CAACC,KAAK,CACjF,GACJ,CAAC;IACDC,WAAW,EACP,uEAAuE,CAACD,KAAK,CACzE,GACJ,CAAC;IACLE,QAAQ,EACJ,mEAAmE,CAACF,KAAK,CACrE,GACJ,CAAC;IACLG,aAAa,EACT,mEAAmE,CAACH,KAAK,CACrE,GACJ,CAAC;IACLI,WAAW,EAAE,eAAe,CAACJ,KAAK,CAAC,GAAG,CAAC;IACvCK,kBAAkB,EAAE,IAAI;IACxBC,cAAc,EAAE;MACZC,EAAE,EAAE,OAAO;MACXC,GAAG,EAAE,UAAU;MACfC,CAAC,EAAE,YAAY;MACfC,EAAE,EAAE,aAAa;MACjBC,GAAG,EAAE,mBAAmB;MACxBC,IAAI,EAAE;IACV,CAAC;IACDC,aAAa,EAAE,uBAAuB;IACtCC,IAAI,EAAE,SAAAA,CAAUC,KAAK,EAAE;MACnB,OAAO,YAAY,CAACC,IAAI,CAACD,KAAK,CAAC;IACnC,CAAC;IACDE,QAAQ,EAAE,SAAAA,CAAUC,IAAI,EAAEC,MAAM,EAAEC,OAAO,EAAE;MACvC,IAAIF,IAAI,GAAG,EAAE,EAAE;QACX,OAAO,YAAY;MACvB,CAAC,MAAM;QACH,OAAO,YAAY;MACvB;IACJ,CAAC;IACDG,QAAQ,EAAE;MACNC,OAAO,EAAE,iBAAiB;MAC1BC,OAAO,EAAE,gBAAgB;MACzBC,QAAQ,EAAE,gBAAgB;MAC1BC,OAAO,EAAE,iBAAiB;MAC1BC,QAAQ,EAAE,sBAAsB;MAChCC,QAAQ,EAAE;IACd,CAAC;IACDC,YAAY,EAAE;MACVC,MAAM,EAAE,OAAO;MACfC,IAAI,EAAE,QAAQ;MACdC,CAAC,EAAE,WAAW;MACdC,EAAE,EAAE,UAAU;MACdC,CAAC,EAAE,UAAU;MACbC,EAAE,EAAE,UAAU;MACdC,CAAC,EAAE,SAAS;MACZC,EAAE,EAAE,SAAS;MACbC,CAAC,EAAE,QAAQ;MACXC,EAAE,EAAE,QAAQ;MACZC,CAAC,EAAE,QAAQ;MACXC,EAAE,EAAE,QAAQ;MACZC,CAAC,EAAE,QAAQ;MACXC,EAAE,EAAE;IACR,CAAC;IACDC,QAAQ,EAAE,SAAAA,CAAUC,MAAM,EAAE;MACxB,OAAOA,MAAM,CACRC,OAAO,CAAC,QAAQ,EAAE,UAAUC,KAAK,EAAE;QAChC,OAAOlD,SAAS,CAACkD,KAAK,CAAC;MAC3B,CAAC,CAAC,CACDD,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC;IAC3B,CAAC;IACDE,UAAU,EAAE,SAAAA,CAAUH,MAAM,EAAE;MAC1B,OAAOA,MAAM,CACRC,OAAO,CAAC,KAAK,EAAE,UAAUC,KAAK,EAAE;QAC7B,OAAOnD,SAAS,CAACmD,KAAK,CAAC;MAC3B,CAAC,CAAC,CACDD,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC;IAC3B,CAAC;IACDG,sBAAsB,EAAE,UAAU;IAClCC,OAAO,EAAE,KAAK;IACdC,IAAI,EAAE;MACFC,GAAG,EAAE,CAAC;MAAE;MACRC,GAAG,EAAE,EAAE,CAAE;IACb;EACJ,CAAC,CAAC;EAEF,OAAOvD,EAAE;AAEb,CAAE,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}