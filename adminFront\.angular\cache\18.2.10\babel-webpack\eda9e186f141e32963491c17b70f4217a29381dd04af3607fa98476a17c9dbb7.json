{"ast": null, "code": "/* tslint:disable */\n/* eslint-disable */\n/* Code generated by ng-openapi-gen DO NOT EDIT. */\nimport { HttpRequest, HttpParams, HttpHeaders } from '@angular/common/http';\n/**\n * Custom parameter codec to correctly handle the plus sign in parameter\n * values. See https://github.com/angular/angular/issues/18261\n */\nclass ParameterCodec {\n  encodeKey(key) {\n    return encodeURIComponent(key);\n  }\n  encodeValue(value) {\n    return encodeURIComponent(value);\n  }\n  decodeKey(key) {\n    return decodeURIComponent(key);\n  }\n  decodeValue(value) {\n    return decodeURIComponent(value);\n  }\n}\nconst ParameterCodecInstance = new ParameterCodec();\n/**\n * Base class for a parameter\n */\nclass Parameter {\n  constructor(name, value, options, defaultStyle, defaultExplode) {\n    this.name = name;\n    this.value = value;\n    this.options = options;\n    this.options = options || {};\n    if (this.options.style === null || this.options.style === undefined) {\n      this.options.style = defaultStyle;\n    }\n    if (this.options.explode === null || this.options.explode === undefined) {\n      this.options.explode = defaultExplode;\n    }\n  }\n  serializeValue(value, separator = ',') {\n    if (value === null || value === undefined) {\n      return '';\n    } else if (value instanceof Array) {\n      return value.map(v => this.serializeValue(v).split(separator).join(encodeURIComponent(separator))).join(separator);\n    } else if (typeof value === 'object') {\n      const array = [];\n      for (const key of Object.keys(value)) {\n        let propVal = value[key];\n        if (propVal !== null && propVal !== undefined) {\n          propVal = this.serializeValue(propVal).split(separator).join(encodeURIComponent(separator));\n          if (this.options.explode) {\n            array.push(`${key}=${propVal}`);\n          } else {\n            array.push(key);\n            array.push(propVal);\n          }\n        }\n      }\n      return array.join(separator);\n    } else {\n      return String(value);\n    }\n  }\n}\n/**\n * A parameter in the operation path\n */\nclass PathParameter extends Parameter {\n  constructor(name, value, options) {\n    super(name, value, options, 'simple', false);\n  }\n  append(path) {\n    let value = this.value;\n    if (value === null || value === undefined) {\n      value = '';\n    }\n    let prefix = this.options.style === 'label' ? '.' : '';\n    let separator = this.options.explode ? prefix === '' ? ',' : prefix : ',';\n    let alreadySerialized = false;\n    if (this.options.style === 'matrix') {\n      // The parameter name is just used as prefix, except in some cases...\n      prefix = `;${this.name}=`;\n      if (this.options.explode && typeof value === 'object') {\n        prefix = ';';\n        if (value instanceof Array) {\n          // For arrays we have to repeat the name for each element\n          value = value.map(v => `${this.name}=${this.serializeValue(v, ';')}`);\n          value = value.join(';');\n          alreadySerialized = true;\n        } else {\n          // For objects we have to put each the key / value pairs\n          value = this.serializeValue(value, ';');\n          alreadySerialized = true;\n        }\n      }\n    }\n    value = prefix + (alreadySerialized ? value : this.serializeValue(value, separator));\n    // Replace both the plain variable and the corresponding variant taking in the prefix and explode into account\n    path = path.replace(`{${this.name}}`, value);\n    path = path.replace(`{${prefix}${this.name}${this.options.explode ? '*' : ''}}`, value);\n    return path;\n  }\n  // @ts-ignore\n  serializeValue(value, separator = ',') {\n    var result = typeof value === 'string' ? encodeURIComponent(value) : super.serializeValue(value, separator);\n    result = result.replace(/%3D/g, '=');\n    result = result.replace(/%3B/g, ';');\n    result = result.replace(/%2C/g, ',');\n    return result;\n  }\n}\n/**\n * A parameter in the query\n */\nclass QueryParameter extends Parameter {\n  constructor(name, value, options) {\n    super(name, value, options, 'form', true);\n  }\n  append(params) {\n    if (this.value instanceof Array) {\n      // Array serialization\n      if (this.options.explode) {\n        for (const v of this.value) {\n          params = params.append(this.name, this.serializeValue(v));\n        }\n      } else {\n        const separator = this.options.style === 'spaceDelimited' ? ' ' : this.options.style === 'pipeDelimited' ? '|' : ',';\n        return params.append(this.name, this.serializeValue(this.value, separator));\n      }\n    } else if (this.value !== null && typeof this.value === 'object') {\n      // Object serialization\n      if (this.options.style === 'deepObject') {\n        // Append a parameter for each key, in the form `name[key]`\n        for (const key of Object.keys(this.value)) {\n          const propVal = this.value[key];\n          if (propVal !== null && propVal !== undefined) {\n            params = params.append(`${this.name}[${key}]`, this.serializeValue(propVal));\n          }\n        }\n      } else if (this.options.explode) {\n        // Append a parameter for each key without using the parameter name\n        for (const key of Object.keys(this.value)) {\n          const propVal = this.value[key];\n          if (propVal !== null && propVal !== undefined) {\n            params = params.append(key, this.serializeValue(propVal));\n          }\n        }\n      } else {\n        // Append a single parameter whose values are a comma-separated list of key,value,key,value...\n        const array = [];\n        for (const key of Object.keys(this.value)) {\n          const propVal = this.value[key];\n          if (propVal !== null && propVal !== undefined) {\n            array.push(key);\n            array.push(propVal);\n          }\n        }\n        params = params.append(this.name, this.serializeValue(array));\n      }\n    } else if (this.value !== null && this.value !== undefined) {\n      // Plain value\n      params = params.append(this.name, this.serializeValue(this.value));\n    }\n    return params;\n  }\n}\n/**\n * A parameter in the HTTP request header\n */\nclass HeaderParameter extends Parameter {\n  constructor(name, value, options) {\n    super(name, value, options, 'simple', false);\n  }\n  append(headers) {\n    if (this.value !== null && this.value !== undefined) {\n      if (this.value instanceof Array) {\n        for (const v of this.value) {\n          headers = headers.append(this.name, this.serializeValue(v));\n        }\n      } else {\n        headers = headers.append(this.name, this.serializeValue(this.value));\n      }\n    }\n    return headers;\n  }\n}\n/**\n * Helper to build http requests from parameters\n */\nexport class RequestBuilder {\n  constructor(rootUrl, operationPath, method) {\n    this.rootUrl = rootUrl;\n    this.operationPath = operationPath;\n    this.method = method;\n    this._path = new Map();\n    this._query = new Map();\n    this._header = new Map();\n  }\n  /**\n   * Sets a path parameter\n   */\n  path(name, value, options) {\n    this._path.set(name, new PathParameter(name, value, options || {}));\n  }\n  /**\n   * Sets a query parameter\n   */\n  query(name, value, options) {\n    this._query.set(name, new QueryParameter(name, value, options || {}));\n  }\n  /**\n   * Sets a header parameter\n   */\n  header(name, value, options) {\n    this._header.set(name, new HeaderParameter(name, value, options || {}));\n  }\n  /**\n   * Sets the body content, along with the content type\n   */\n  body(value, contentType = 'application/json') {\n    if (value instanceof Blob) {\n      this._bodyContentType = value.type;\n    } else {\n      this._bodyContentType = contentType;\n    }\n    if (this._bodyContentType === 'application/x-www-form-urlencoded' && value !== null && typeof value === 'object') {\n      // Handle URL-encoded data\n      const pairs = [];\n      for (const key of Object.keys(value)) {\n        let val = value[key];\n        if (!(val instanceof Array)) {\n          val = [val];\n        }\n        for (const v of val) {\n          const formValue = this.formDataValue(v);\n          if (formValue !== null) {\n            pairs.push([key, formValue]);\n          }\n        }\n      }\n      this._bodyContent = pairs.map(p => `${encodeURIComponent(p[0])}=${encodeURIComponent(p[1])}`).join('&');\n    } else if (this._bodyContentType === 'multipart/form-data') {\n      // Handle multipart form data\n      const formData = new FormData();\n      if (value !== null && value !== undefined) {\n        for (const key of Object.keys(value)) {\n          const val = value[key];\n          if (val instanceof Array) {\n            for (const v of val) {\n              const toAppend = this.formDataValue(v);\n              if (toAppend !== null) {\n                formData.append(key, toAppend);\n              }\n            }\n          } else {\n            const toAppend = this.formDataValue(val);\n            if (toAppend !== null) {\n              formData.set(key, toAppend);\n            }\n          }\n        }\n      }\n      this._bodyContent = formData;\n    } else {\n      // The body is the plain content\n      this._bodyContent = value;\n    }\n  }\n  formDataValue(value) {\n    if (value === null || value === undefined) {\n      return null;\n    }\n    if (value instanceof Blob) {\n      return value;\n    }\n    if (typeof value === 'object') {\n      return new Blob([JSON.stringify(value)], {\n        type: 'application/json'\n      });\n    }\n    return String(value);\n  }\n  /**\n   * Builds the request with the current set parameters\n   */\n  build(options) {\n    options = options || {};\n    // Path parameters\n    let path = this.operationPath;\n    for (const pathParam of this._path.values()) {\n      path = pathParam.append(path);\n    }\n    const url = this.rootUrl + path;\n    // Query parameters\n    let httpParams = new HttpParams({\n      encoder: ParameterCodecInstance\n    });\n    for (const queryParam of this._query.values()) {\n      httpParams = queryParam.append(httpParams);\n    }\n    // Header parameters\n    let httpHeaders = new HttpHeaders();\n    if (options.accept) {\n      httpHeaders = httpHeaders.append('Accept', options.accept);\n    }\n    for (const headerParam of this._header.values()) {\n      httpHeaders = headerParam.append(httpHeaders);\n    }\n    // Request content headers\n    if (this._bodyContentType && !(this._bodyContent instanceof FormData)) {\n      httpHeaders = httpHeaders.set('Content-Type', this._bodyContentType);\n    }\n    // Perform the request\n    return new HttpRequest(this.method.toUpperCase(), url, this._bodyContent, {\n      params: httpParams,\n      headers: httpHeaders,\n      responseType: options.responseType,\n      reportProgress: options.reportProgress,\n      context: options.context\n    });\n  }\n}", "map": {"version": 3, "names": ["HttpRequest", "HttpParams", "HttpHeaders", "ParameterCodec", "encodeKey", "key", "encodeURIComponent", "encodeValue", "value", "decodeKey", "decodeURIComponent", "decodeValue", "ParameterCodecInstance", "Parameter", "constructor", "name", "options", "defaultStyle", "defaultExplode", "style", "undefined", "explode", "serializeValue", "separator", "Array", "map", "v", "split", "join", "array", "Object", "keys", "propVal", "push", "String", "PathParameter", "append", "path", "prefix", "alreadySerialized", "replace", "result", "QueryParameter", "params", "HeaderParameter", "headers", "RequestBuilder", "rootUrl", "operationPath", "method", "_path", "Map", "_query", "_header", "set", "query", "header", "body", "contentType", "Blob", "_bodyContentType", "type", "pairs", "val", "formValue", "formDataValue", "_bodyContent", "p", "formData", "FormData", "toAppend", "JSON", "stringify", "build", "pathParam", "values", "url", "httpParams", "encoder", "queryParam", "httpHeaders", "accept", "headerParam", "toUpperCase", "responseType", "reportProgress", "context"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\services\\api\\request-builder.ts"], "sourcesContent": ["/* tslint:disable */\r\n/* eslint-disable */\r\n/* Code generated by ng-openapi-gen DO NOT EDIT. */\r\n\r\nimport { HttpRequest, HttpParameterCodec, HttpParams, HttpHeaders, HttpContext } from '@angular/common/http';\r\n\r\n/**\r\n * Custom parameter codec to correctly handle the plus sign in parameter\r\n * values. See https://github.com/angular/angular/issues/18261\r\n */\r\nclass ParameterCodec implements HttpParameterCodec {\r\n  encodeKey(key: string): string {\r\n    return encodeURIComponent(key);\r\n  }\r\n\r\n  encodeValue(value: string): string {\r\n    return encodeURIComponent(value);\r\n  }\r\n\r\n  decodeKey(key: string): string {\r\n    return decodeURIComponent(key);\r\n  }\r\n\r\n  decodeValue(value: string): string {\r\n    return decodeURIComponent(value);\r\n  }\r\n}\r\nconst ParameterCodecInstance = new ParameterCodec();\r\n\r\n/**\r\n * Defines the options for appending a parameter\r\n */\r\ninterface ParameterOptions {\r\n  style?: string;\r\n  explode?: boolean;\r\n}\r\n\r\n/**\r\n * Base class for a parameter\r\n */\r\nabstract class Parameter {\r\n  constructor(public name: string, public value: any, public options: ParameterOptions, defaultStyle: string, defaultExplode: boolean) {\r\n    this.options = options || {};\r\n    if (this.options.style === null || this.options.style === undefined) {\r\n      this.options.style = defaultStyle;\r\n    }\r\n    if (this.options.explode === null || this.options.explode === undefined) {\r\n      this.options.explode = defaultExplode;\r\n    }\r\n  }\r\n\r\n  serializeValue(value: any, separator = ','): string {\r\n    if (value === null || value === undefined) {\r\n      return '';\r\n    } else if (value instanceof Array) {\r\n      return value.map(v => this.serializeValue(v).split(separator).join(encodeURIComponent(separator))).join(separator);\r\n    } else if (typeof value === 'object') {\r\n      const array: string[] = [];\r\n      for (const key of Object.keys(value)) {\r\n        let propVal = value[key];\r\n        if (propVal !== null && propVal !== undefined) {\r\n          propVal = this.serializeValue(propVal).split(separator).join(encodeURIComponent(separator));\r\n          if (this.options.explode) {\r\n            array.push(`${key}=${propVal}`);\r\n          } else {\r\n            array.push(key);\r\n            array.push(propVal);\r\n          }\r\n        }\r\n      }\r\n      return array.join(separator);\r\n    } else {\r\n      return String(value);\r\n    }\r\n  }\r\n}\r\n\r\n/**\r\n * A parameter in the operation path\r\n */\r\nclass PathParameter extends Parameter {\r\n  constructor(name: string, value: any, options: ParameterOptions) {\r\n    super(name, value, options, 'simple', false);\r\n  }\r\n\r\n  append(path: string): string {\r\n    let value = this.value;\r\n    if (value === null || value === undefined) {\r\n      value = '';\r\n    }\r\n    let prefix = this.options.style === 'label' ? '.' : '';\r\n    let separator = this.options.explode ? prefix === '' ? ',' : prefix : ',';\r\n    let alreadySerialized = false;\r\n    if (this.options.style === 'matrix') {\r\n      // The parameter name is just used as prefix, except in some cases...\r\n      prefix = `;${this.name}=`;\r\n      if (this.options.explode && typeof value === 'object') {\r\n        prefix = ';';\r\n        if (value instanceof Array) {\r\n          // For arrays we have to repeat the name for each element\r\n          value = value.map(v => `${this.name}=${this.serializeValue(v, ';')}`);\r\n          value = value.join(';');\r\n          alreadySerialized = true;\r\n        } else {\r\n          // For objects we have to put each the key / value pairs\r\n          value = this.serializeValue(value, ';');\r\n          alreadySerialized = true\r\n        }\r\n      }\r\n    }\r\n    value = prefix + (alreadySerialized ? value : this.serializeValue(value, separator));\r\n    // Replace both the plain variable and the corresponding variant taking in the prefix and explode into account\r\n    path = path.replace(`{${this.name}}`, value);\r\n    path = path.replace(`{${prefix}${this.name}${this.options.explode ? '*' : ''}}`, value);\r\n    return path;\r\n  }\r\n\r\n  // @ts-ignore\r\n  serializeValue(value: any, separator = ','): string {\r\n    var result = typeof value === 'string' ? encodeURIComponent(value) : super.serializeValue(value, separator);\r\n    result = result.replace(/%3D/g, '=');\r\n    result = result.replace(/%3B/g, ';');\r\n    result = result.replace(/%2C/g, ',');\r\n    return result;\r\n  }\r\n}\r\n\r\n/**\r\n * A parameter in the query\r\n */\r\nclass QueryParameter extends Parameter {\r\n  constructor(name: string, value: any, options: ParameterOptions) {\r\n    super(name, value, options, 'form', true);\r\n  }\r\n\r\n  append(params: HttpParams): HttpParams {\r\n    if (this.value instanceof Array) {\r\n      // Array serialization\r\n      if (this.options.explode) {\r\n        for (const v of this.value) {\r\n          params = params.append(this.name, this.serializeValue(v));\r\n        }\r\n      } else {\r\n        const separator = this.options.style === 'spaceDelimited'\r\n          ? ' ' : this.options.style === 'pipeDelimited'\r\n            ? '|' : ',';\r\n        return params.append(this.name, this.serializeValue(this.value, separator));\r\n      }\r\n    } else if (this.value !== null && typeof this.value === 'object') {\r\n      // Object serialization\r\n      if (this.options.style === 'deepObject') {\r\n        // Append a parameter for each key, in the form `name[key]`\r\n        for (const key of Object.keys(this.value)) {\r\n          const propVal = this.value[key];\r\n          if (propVal !== null && propVal !== undefined) {\r\n            params = params.append(`${this.name}[${key}]`, this.serializeValue(propVal));\r\n          }\r\n        }\r\n      } else if (this.options.explode) {\r\n        // Append a parameter for each key without using the parameter name\r\n        for (const key of Object.keys(this.value)) {\r\n          const propVal = this.value[key];\r\n          if (propVal !== null && propVal !== undefined) {\r\n            params = params.append(key, this.serializeValue(propVal));\r\n          }\r\n        }\r\n      } else {\r\n        // Append a single parameter whose values are a comma-separated list of key,value,key,value...\r\n        const array: any[] = [];\r\n        for (const key of Object.keys(this.value)) {\r\n          const propVal = this.value[key];\r\n          if (propVal !== null && propVal !== undefined) {\r\n            array.push(key);\r\n            array.push(propVal);\r\n          }\r\n        }\r\n        params = params.append(this.name, this.serializeValue(array));\r\n      }\r\n    } else if (this.value !== null && this.value !== undefined) {\r\n      // Plain value\r\n      params = params.append(this.name, this.serializeValue(this.value));\r\n    }\r\n    return params;\r\n  }\r\n}\r\n\r\n/**\r\n * A parameter in the HTTP request header\r\n */\r\nclass HeaderParameter extends Parameter {\r\n  constructor(name: string, value: any, options: ParameterOptions) {\r\n    super(name, value, options, 'simple', false);\r\n  }\r\n\r\n  append(headers: HttpHeaders): HttpHeaders {\r\n    if (this.value !== null && this.value !== undefined) {\r\n      if (this.value instanceof Array) {\r\n        for (const v of this.value) {\r\n          headers = headers.append(this.name, this.serializeValue(v));\r\n        }\r\n      } else {\r\n        headers = headers.append(this.name, this.serializeValue(this.value));\r\n      }\r\n    }\r\n    return headers;\r\n  }\r\n}\r\n\r\n/**\r\n * Helper to build http requests from parameters\r\n */\r\nexport class RequestBuilder {\r\n\r\n  private _path = new Map<string, PathParameter>();\r\n  private _query = new Map<string, QueryParameter>();\r\n  private _header = new Map<string, HeaderParameter>();\r\n  _bodyContent: any | null;\r\n  _bodyContentType?: string;\r\n\r\n  constructor(\r\n    public rootUrl: string,\r\n    public operationPath: string,\r\n    public method: string) {\r\n  }\r\n\r\n  /**\r\n   * Sets a path parameter\r\n   */\r\n  path(name: string, value: any, options?: ParameterOptions): void {\r\n    this._path.set(name, new PathParameter(name, value, options || {}));\r\n  }\r\n\r\n  /**\r\n   * Sets a query parameter\r\n   */\r\n  query(name: string, value: any, options?: ParameterOptions): void {\r\n    this._query.set(name, new QueryParameter(name, value, options || {}));\r\n  }\r\n\r\n  /**\r\n   * Sets a header parameter\r\n   */\r\n  header(name: string, value: any, options?: ParameterOptions): void {\r\n    this._header.set(name, new HeaderParameter(name, value, options || {}));\r\n  }\r\n\r\n  /**\r\n   * Sets the body content, along with the content type\r\n   */\r\n  body(value: any, contentType = 'application/json'): void {\r\n    if (value instanceof Blob) {\r\n      this._bodyContentType = value.type;\r\n    } else {\r\n      this._bodyContentType = contentType;\r\n    }\r\n    if (this._bodyContentType === 'application/x-www-form-urlencoded' && value !== null && typeof value === 'object') {\r\n      // Handle URL-encoded data\r\n      const pairs: Array<[string, string]> = [];\r\n      for (const key of Object.keys(value)) {\r\n        let val = value[key];\r\n        if (!(val instanceof Array)) {\r\n          val = [val];\r\n        }\r\n        for (const v of val) {\r\n          const formValue = this.formDataValue(v);\r\n          if (formValue !== null) {\r\n            pairs.push([key, formValue]);\r\n          }\r\n        }\r\n      }\r\n      this._bodyContent = pairs.map(p => `${encodeURIComponent(p[0])}=${encodeURIComponent(p[1])}`).join('&');\r\n    } else if (this._bodyContentType === 'multipart/form-data') {\r\n      // Handle multipart form data\r\n      const formData = new FormData();\r\n      if (value !== null && value !== undefined) {\r\n        for (const key of Object.keys(value)) {\r\n          const val = value[key];\r\n          if (val instanceof Array) {\r\n            for (const v of val) {\r\n              const toAppend = this.formDataValue(v);\r\n              if (toAppend !== null) {\r\n                formData.append(key, toAppend);\r\n              }\r\n            }\r\n          } else {\r\n            const toAppend = this.formDataValue(val);\r\n            if (toAppend !== null) {\r\n              formData.set(key, toAppend);\r\n            }\r\n          }\r\n        }\r\n      }\r\n      this._bodyContent = formData;\r\n    } else {\r\n      // The body is the plain content\r\n      this._bodyContent = value;\r\n    }\r\n  }\r\n\r\n  private formDataValue(value: any): any {\r\n    if (value === null || value === undefined) {\r\n      return null;\r\n    }\r\n    if (value instanceof Blob) {\r\n      return value;\r\n    }\r\n    if (typeof value === 'object') {\r\n      return new Blob([JSON.stringify(value)], {type: 'application/json'})\r\n    }\r\n    return String(value);\r\n  }\r\n\r\n  /**\r\n   * Builds the request with the current set parameters\r\n   */\r\n  build<T = any>(options?: {\r\n    /** Which content types to accept */\r\n    accept?: string;\r\n\r\n    /** The expected response type */\r\n    responseType?: 'json' | 'text' | 'blob' | 'arraybuffer';\r\n\r\n    /** Whether to report progress on uploads / downloads */\r\n    reportProgress?: boolean;\r\n\r\n    /** Allow passing HttpContext for HttpClient */\r\n    context?: HttpContext;\r\n  }): HttpRequest<T> {\r\n\r\n    options = options || {};\r\n\r\n    // Path parameters\r\n    let path = this.operationPath;\r\n    for (const pathParam of this._path.values()) {\r\n      path = pathParam.append(path);\r\n    }\r\n    const url = this.rootUrl + path;\r\n\r\n    // Query parameters\r\n    let httpParams = new HttpParams({\r\n      encoder: ParameterCodecInstance\r\n    });\r\n    for (const queryParam of this._query.values()) {\r\n      httpParams = queryParam.append(httpParams);\r\n    }\r\n\r\n    // Header parameters\r\n    let httpHeaders = new HttpHeaders();\r\n    if (options.accept) {\r\n      httpHeaders = httpHeaders.append('Accept', options.accept);\r\n    }\r\n    for (const headerParam of this._header.values()) {\r\n      httpHeaders = headerParam.append(httpHeaders);\r\n    }\r\n\r\n    // Request content headers\r\n    if (this._bodyContentType && !(this._bodyContent instanceof FormData)) {\r\n      httpHeaders = httpHeaders.set('Content-Type', this._bodyContentType);\r\n    }\r\n\r\n    // Perform the request\r\n    return new HttpRequest<T>(this.method.toUpperCase(), url, this._bodyContent, {\r\n      params: httpParams,\r\n      headers: httpHeaders,\r\n      responseType: options.responseType,\r\n      reportProgress: options.reportProgress,\r\n      context: options.context\r\n    });\r\n  }\r\n}\r\n"], "mappings": "AAAA;AACA;AACA;AAEA,SAASA,WAAW,EAAsBC,UAAU,EAAEC,WAAW,QAAqB,sBAAsB;AAE5G;;;;AAIA,MAAMC,cAAc;EAClBC,SAASA,CAACC,GAAW;IACnB,OAAOC,kBAAkB,CAACD,GAAG,CAAC;EAChC;EAEAE,WAAWA,CAACC,KAAa;IACvB,OAAOF,kBAAkB,CAACE,KAAK,CAAC;EAClC;EAEAC,SAASA,CAACJ,GAAW;IACnB,OAAOK,kBAAkB,CAACL,GAAG,CAAC;EAChC;EAEAM,WAAWA,CAACH,KAAa;IACvB,OAAOE,kBAAkB,CAACF,KAAK,CAAC;EAClC;;AAEF,MAAMI,sBAAsB,GAAG,IAAIT,cAAc,EAAE;AAUnD;;;AAGA,MAAeU,SAAS;EACtBC,YAAmBC,IAAY,EAASP,KAAU,EAASQ,OAAyB,EAAEC,YAAoB,EAAEC,cAAuB;IAAhH,KAAAH,IAAI,GAAJA,IAAI;IAAiB,KAAAP,KAAK,GAALA,KAAK;IAAc,KAAAQ,OAAO,GAAPA,OAAO;IAChE,IAAI,CAACA,OAAO,GAAGA,OAAO,IAAI,EAAE;IAC5B,IAAI,IAAI,CAACA,OAAO,CAACG,KAAK,KAAK,IAAI,IAAI,IAAI,CAACH,OAAO,CAACG,KAAK,KAAKC,SAAS,EAAE;MACnE,IAAI,CAACJ,OAAO,CAACG,KAAK,GAAGF,YAAY;IACnC;IACA,IAAI,IAAI,CAACD,OAAO,CAACK,OAAO,KAAK,IAAI,IAAI,IAAI,CAACL,OAAO,CAACK,OAAO,KAAKD,SAAS,EAAE;MACvE,IAAI,CAACJ,OAAO,CAACK,OAAO,GAAGH,cAAc;IACvC;EACF;EAEAI,cAAcA,CAACd,KAAU,EAAEe,SAAS,GAAG,GAAG;IACxC,IAAIf,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAKY,SAAS,EAAE;MACzC,OAAO,EAAE;IACX,CAAC,MAAM,IAAIZ,KAAK,YAAYgB,KAAK,EAAE;MACjC,OAAOhB,KAAK,CAACiB,GAAG,CAACC,CAAC,IAAI,IAAI,CAACJ,cAAc,CAACI,CAAC,CAAC,CAACC,KAAK,CAACJ,SAAS,CAAC,CAACK,IAAI,CAACtB,kBAAkB,CAACiB,SAAS,CAAC,CAAC,CAAC,CAACK,IAAI,CAACL,SAAS,CAAC;IACpH,CAAC,MAAM,IAAI,OAAOf,KAAK,KAAK,QAAQ,EAAE;MACpC,MAAMqB,KAAK,GAAa,EAAE;MAC1B,KAAK,MAAMxB,GAAG,IAAIyB,MAAM,CAACC,IAAI,CAACvB,KAAK,CAAC,EAAE;QACpC,IAAIwB,OAAO,GAAGxB,KAAK,CAACH,GAAG,CAAC;QACxB,IAAI2B,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAKZ,SAAS,EAAE;UAC7CY,OAAO,GAAG,IAAI,CAACV,cAAc,CAACU,OAAO,CAAC,CAACL,KAAK,CAACJ,SAAS,CAAC,CAACK,IAAI,CAACtB,kBAAkB,CAACiB,SAAS,CAAC,CAAC;UAC3F,IAAI,IAAI,CAACP,OAAO,CAACK,OAAO,EAAE;YACxBQ,KAAK,CAACI,IAAI,CAAC,GAAG5B,GAAG,IAAI2B,OAAO,EAAE,CAAC;UACjC,CAAC,MAAM;YACLH,KAAK,CAACI,IAAI,CAAC5B,GAAG,CAAC;YACfwB,KAAK,CAACI,IAAI,CAACD,OAAO,CAAC;UACrB;QACF;MACF;MACA,OAAOH,KAAK,CAACD,IAAI,CAACL,SAAS,CAAC;IAC9B,CAAC,MAAM;MACL,OAAOW,MAAM,CAAC1B,KAAK,CAAC;IACtB;EACF;;AAGF;;;AAGA,MAAM2B,aAAc,SAAQtB,SAAS;EACnCC,YAAYC,IAAY,EAAEP,KAAU,EAAEQ,OAAyB;IAC7D,KAAK,CAACD,IAAI,EAAEP,KAAK,EAAEQ,OAAO,EAAE,QAAQ,EAAE,KAAK,CAAC;EAC9C;EAEAoB,MAAMA,CAACC,IAAY;IACjB,IAAI7B,KAAK,GAAG,IAAI,CAACA,KAAK;IACtB,IAAIA,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAKY,SAAS,EAAE;MACzCZ,KAAK,GAAG,EAAE;IACZ;IACA,IAAI8B,MAAM,GAAG,IAAI,CAACtB,OAAO,CAACG,KAAK,KAAK,OAAO,GAAG,GAAG,GAAG,EAAE;IACtD,IAAII,SAAS,GAAG,IAAI,CAACP,OAAO,CAACK,OAAO,GAAGiB,MAAM,KAAK,EAAE,GAAG,GAAG,GAAGA,MAAM,GAAG,GAAG;IACzE,IAAIC,iBAAiB,GAAG,KAAK;IAC7B,IAAI,IAAI,CAACvB,OAAO,CAACG,KAAK,KAAK,QAAQ,EAAE;MACnC;MACAmB,MAAM,GAAG,IAAI,IAAI,CAACvB,IAAI,GAAG;MACzB,IAAI,IAAI,CAACC,OAAO,CAACK,OAAO,IAAI,OAAOb,KAAK,KAAK,QAAQ,EAAE;QACrD8B,MAAM,GAAG,GAAG;QACZ,IAAI9B,KAAK,YAAYgB,KAAK,EAAE;UAC1B;UACAhB,KAAK,GAAGA,KAAK,CAACiB,GAAG,CAACC,CAAC,IAAI,GAAG,IAAI,CAACX,IAAI,IAAI,IAAI,CAACO,cAAc,CAACI,CAAC,EAAE,GAAG,CAAC,EAAE,CAAC;UACrElB,KAAK,GAAGA,KAAK,CAACoB,IAAI,CAAC,GAAG,CAAC;UACvBW,iBAAiB,GAAG,IAAI;QAC1B,CAAC,MAAM;UACL;UACA/B,KAAK,GAAG,IAAI,CAACc,cAAc,CAACd,KAAK,EAAE,GAAG,CAAC;UACvC+B,iBAAiB,GAAG,IAAI;QAC1B;MACF;IACF;IACA/B,KAAK,GAAG8B,MAAM,IAAIC,iBAAiB,GAAG/B,KAAK,GAAG,IAAI,CAACc,cAAc,CAACd,KAAK,EAAEe,SAAS,CAAC,CAAC;IACpF;IACAc,IAAI,GAAGA,IAAI,CAACG,OAAO,CAAC,IAAI,IAAI,CAACzB,IAAI,GAAG,EAAEP,KAAK,CAAC;IAC5C6B,IAAI,GAAGA,IAAI,CAACG,OAAO,CAAC,IAAIF,MAAM,GAAG,IAAI,CAACvB,IAAI,GAAG,IAAI,CAACC,OAAO,CAACK,OAAO,GAAG,GAAG,GAAG,EAAE,GAAG,EAAEb,KAAK,CAAC;IACvF,OAAO6B,IAAI;EACb;EAEA;EACAf,cAAcA,CAACd,KAAU,EAAEe,SAAS,GAAG,GAAG;IACxC,IAAIkB,MAAM,GAAG,OAAOjC,KAAK,KAAK,QAAQ,GAAGF,kBAAkB,CAACE,KAAK,CAAC,GAAG,KAAK,CAACc,cAAc,CAACd,KAAK,EAAEe,SAAS,CAAC;IAC3GkB,MAAM,GAAGA,MAAM,CAACD,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC;IACpCC,MAAM,GAAGA,MAAM,CAACD,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC;IACpCC,MAAM,GAAGA,MAAM,CAACD,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC;IACpC,OAAOC,MAAM;EACf;;AAGF;;;AAGA,MAAMC,cAAe,SAAQ7B,SAAS;EACpCC,YAAYC,IAAY,EAAEP,KAAU,EAAEQ,OAAyB;IAC7D,KAAK,CAACD,IAAI,EAAEP,KAAK,EAAEQ,OAAO,EAAE,MAAM,EAAE,IAAI,CAAC;EAC3C;EAEAoB,MAAMA,CAACO,MAAkB;IACvB,IAAI,IAAI,CAACnC,KAAK,YAAYgB,KAAK,EAAE;MAC/B;MACA,IAAI,IAAI,CAACR,OAAO,CAACK,OAAO,EAAE;QACxB,KAAK,MAAMK,CAAC,IAAI,IAAI,CAAClB,KAAK,EAAE;UAC1BmC,MAAM,GAAGA,MAAM,CAACP,MAAM,CAAC,IAAI,CAACrB,IAAI,EAAE,IAAI,CAACO,cAAc,CAACI,CAAC,CAAC,CAAC;QAC3D;MACF,CAAC,MAAM;QACL,MAAMH,SAAS,GAAG,IAAI,CAACP,OAAO,CAACG,KAAK,KAAK,gBAAgB,GACrD,GAAG,GAAG,IAAI,CAACH,OAAO,CAACG,KAAK,KAAK,eAAe,GAC1C,GAAG,GAAG,GAAG;QACf,OAAOwB,MAAM,CAACP,MAAM,CAAC,IAAI,CAACrB,IAAI,EAAE,IAAI,CAACO,cAAc,CAAC,IAAI,CAACd,KAAK,EAAEe,SAAS,CAAC,CAAC;MAC7E;IACF,CAAC,MAAM,IAAI,IAAI,CAACf,KAAK,KAAK,IAAI,IAAI,OAAO,IAAI,CAACA,KAAK,KAAK,QAAQ,EAAE;MAChE;MACA,IAAI,IAAI,CAACQ,OAAO,CAACG,KAAK,KAAK,YAAY,EAAE;QACvC;QACA,KAAK,MAAMd,GAAG,IAAIyB,MAAM,CAACC,IAAI,CAAC,IAAI,CAACvB,KAAK,CAAC,EAAE;UACzC,MAAMwB,OAAO,GAAG,IAAI,CAACxB,KAAK,CAACH,GAAG,CAAC;UAC/B,IAAI2B,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAKZ,SAAS,EAAE;YAC7CuB,MAAM,GAAGA,MAAM,CAACP,MAAM,CAAC,GAAG,IAAI,CAACrB,IAAI,IAAIV,GAAG,GAAG,EAAE,IAAI,CAACiB,cAAc,CAACU,OAAO,CAAC,CAAC;UAC9E;QACF;MACF,CAAC,MAAM,IAAI,IAAI,CAAChB,OAAO,CAACK,OAAO,EAAE;QAC/B;QACA,KAAK,MAAMhB,GAAG,IAAIyB,MAAM,CAACC,IAAI,CAAC,IAAI,CAACvB,KAAK,CAAC,EAAE;UACzC,MAAMwB,OAAO,GAAG,IAAI,CAACxB,KAAK,CAACH,GAAG,CAAC;UAC/B,IAAI2B,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAKZ,SAAS,EAAE;YAC7CuB,MAAM,GAAGA,MAAM,CAACP,MAAM,CAAC/B,GAAG,EAAE,IAAI,CAACiB,cAAc,CAACU,OAAO,CAAC,CAAC;UAC3D;QACF;MACF,CAAC,MAAM;QACL;QACA,MAAMH,KAAK,GAAU,EAAE;QACvB,KAAK,MAAMxB,GAAG,IAAIyB,MAAM,CAACC,IAAI,CAAC,IAAI,CAACvB,KAAK,CAAC,EAAE;UACzC,MAAMwB,OAAO,GAAG,IAAI,CAACxB,KAAK,CAACH,GAAG,CAAC;UAC/B,IAAI2B,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAKZ,SAAS,EAAE;YAC7CS,KAAK,CAACI,IAAI,CAAC5B,GAAG,CAAC;YACfwB,KAAK,CAACI,IAAI,CAACD,OAAO,CAAC;UACrB;QACF;QACAW,MAAM,GAAGA,MAAM,CAACP,MAAM,CAAC,IAAI,CAACrB,IAAI,EAAE,IAAI,CAACO,cAAc,CAACO,KAAK,CAAC,CAAC;MAC/D;IACF,CAAC,MAAM,IAAI,IAAI,CAACrB,KAAK,KAAK,IAAI,IAAI,IAAI,CAACA,KAAK,KAAKY,SAAS,EAAE;MAC1D;MACAuB,MAAM,GAAGA,MAAM,CAACP,MAAM,CAAC,IAAI,CAACrB,IAAI,EAAE,IAAI,CAACO,cAAc,CAAC,IAAI,CAACd,KAAK,CAAC,CAAC;IACpE;IACA,OAAOmC,MAAM;EACf;;AAGF;;;AAGA,MAAMC,eAAgB,SAAQ/B,SAAS;EACrCC,YAAYC,IAAY,EAAEP,KAAU,EAAEQ,OAAyB;IAC7D,KAAK,CAACD,IAAI,EAAEP,KAAK,EAAEQ,OAAO,EAAE,QAAQ,EAAE,KAAK,CAAC;EAC9C;EAEAoB,MAAMA,CAACS,OAAoB;IACzB,IAAI,IAAI,CAACrC,KAAK,KAAK,IAAI,IAAI,IAAI,CAACA,KAAK,KAAKY,SAAS,EAAE;MACnD,IAAI,IAAI,CAACZ,KAAK,YAAYgB,KAAK,EAAE;QAC/B,KAAK,MAAME,CAAC,IAAI,IAAI,CAAClB,KAAK,EAAE;UAC1BqC,OAAO,GAAGA,OAAO,CAACT,MAAM,CAAC,IAAI,CAACrB,IAAI,EAAE,IAAI,CAACO,cAAc,CAACI,CAAC,CAAC,CAAC;QAC7D;MACF,CAAC,MAAM;QACLmB,OAAO,GAAGA,OAAO,CAACT,MAAM,CAAC,IAAI,CAACrB,IAAI,EAAE,IAAI,CAACO,cAAc,CAAC,IAAI,CAACd,KAAK,CAAC,CAAC;MACtE;IACF;IACA,OAAOqC,OAAO;EAChB;;AAGF;;;AAGA,OAAM,MAAOC,cAAc;EAQzBhC,YACSiC,OAAe,EACfC,aAAqB,EACrBC,MAAc;IAFd,KAAAF,OAAO,GAAPA,OAAO;IACP,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,MAAM,GAANA,MAAM;IATP,KAAAC,KAAK,GAAG,IAAIC,GAAG,EAAyB;IACxC,KAAAC,MAAM,GAAG,IAAID,GAAG,EAA0B;IAC1C,KAAAE,OAAO,GAAG,IAAIF,GAAG,EAA2B;EAQpD;EAEA;;;EAGAd,IAAIA,CAACtB,IAAY,EAAEP,KAAU,EAAEQ,OAA0B;IACvD,IAAI,CAACkC,KAAK,CAACI,GAAG,CAACvC,IAAI,EAAE,IAAIoB,aAAa,CAACpB,IAAI,EAAEP,KAAK,EAAEQ,OAAO,IAAI,EAAE,CAAC,CAAC;EACrE;EAEA;;;EAGAuC,KAAKA,CAACxC,IAAY,EAAEP,KAAU,EAAEQ,OAA0B;IACxD,IAAI,CAACoC,MAAM,CAACE,GAAG,CAACvC,IAAI,EAAE,IAAI2B,cAAc,CAAC3B,IAAI,EAAEP,KAAK,EAAEQ,OAAO,IAAI,EAAE,CAAC,CAAC;EACvE;EAEA;;;EAGAwC,MAAMA,CAACzC,IAAY,EAAEP,KAAU,EAAEQ,OAA0B;IACzD,IAAI,CAACqC,OAAO,CAACC,GAAG,CAACvC,IAAI,EAAE,IAAI6B,eAAe,CAAC7B,IAAI,EAAEP,KAAK,EAAEQ,OAAO,IAAI,EAAE,CAAC,CAAC;EACzE;EAEA;;;EAGAyC,IAAIA,CAACjD,KAAU,EAAEkD,WAAW,GAAG,kBAAkB;IAC/C,IAAIlD,KAAK,YAAYmD,IAAI,EAAE;MACzB,IAAI,CAACC,gBAAgB,GAAGpD,KAAK,CAACqD,IAAI;IACpC,CAAC,MAAM;MACL,IAAI,CAACD,gBAAgB,GAAGF,WAAW;IACrC;IACA,IAAI,IAAI,CAACE,gBAAgB,KAAK,mCAAmC,IAAIpD,KAAK,KAAK,IAAI,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;MAChH;MACA,MAAMsD,KAAK,GAA4B,EAAE;MACzC,KAAK,MAAMzD,GAAG,IAAIyB,MAAM,CAACC,IAAI,CAACvB,KAAK,CAAC,EAAE;QACpC,IAAIuD,GAAG,GAAGvD,KAAK,CAACH,GAAG,CAAC;QACpB,IAAI,EAAE0D,GAAG,YAAYvC,KAAK,CAAC,EAAE;UAC3BuC,GAAG,GAAG,CAACA,GAAG,CAAC;QACb;QACA,KAAK,MAAMrC,CAAC,IAAIqC,GAAG,EAAE;UACnB,MAAMC,SAAS,GAAG,IAAI,CAACC,aAAa,CAACvC,CAAC,CAAC;UACvC,IAAIsC,SAAS,KAAK,IAAI,EAAE;YACtBF,KAAK,CAAC7B,IAAI,CAAC,CAAC5B,GAAG,EAAE2D,SAAS,CAAC,CAAC;UAC9B;QACF;MACF;MACA,IAAI,CAACE,YAAY,GAAGJ,KAAK,CAACrC,GAAG,CAAC0C,CAAC,IAAI,GAAG7D,kBAAkB,CAAC6D,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI7D,kBAAkB,CAAC6D,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAACvC,IAAI,CAAC,GAAG,CAAC;IACzG,CAAC,MAAM,IAAI,IAAI,CAACgC,gBAAgB,KAAK,qBAAqB,EAAE;MAC1D;MACA,MAAMQ,QAAQ,GAAG,IAAIC,QAAQ,EAAE;MAC/B,IAAI7D,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAKY,SAAS,EAAE;QACzC,KAAK,MAAMf,GAAG,IAAIyB,MAAM,CAACC,IAAI,CAACvB,KAAK,CAAC,EAAE;UACpC,MAAMuD,GAAG,GAAGvD,KAAK,CAACH,GAAG,CAAC;UACtB,IAAI0D,GAAG,YAAYvC,KAAK,EAAE;YACxB,KAAK,MAAME,CAAC,IAAIqC,GAAG,EAAE;cACnB,MAAMO,QAAQ,GAAG,IAAI,CAACL,aAAa,CAACvC,CAAC,CAAC;cACtC,IAAI4C,QAAQ,KAAK,IAAI,EAAE;gBACrBF,QAAQ,CAAChC,MAAM,CAAC/B,GAAG,EAAEiE,QAAQ,CAAC;cAChC;YACF;UACF,CAAC,MAAM;YACL,MAAMA,QAAQ,GAAG,IAAI,CAACL,aAAa,CAACF,GAAG,CAAC;YACxC,IAAIO,QAAQ,KAAK,IAAI,EAAE;cACrBF,QAAQ,CAACd,GAAG,CAACjD,GAAG,EAAEiE,QAAQ,CAAC;YAC7B;UACF;QACF;MACF;MACA,IAAI,CAACJ,YAAY,GAAGE,QAAQ;IAC9B,CAAC,MAAM;MACL;MACA,IAAI,CAACF,YAAY,GAAG1D,KAAK;IAC3B;EACF;EAEQyD,aAAaA,CAACzD,KAAU;IAC9B,IAAIA,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAKY,SAAS,EAAE;MACzC,OAAO,IAAI;IACb;IACA,IAAIZ,KAAK,YAAYmD,IAAI,EAAE;MACzB,OAAOnD,KAAK;IACd;IACA,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;MAC7B,OAAO,IAAImD,IAAI,CAAC,CAACY,IAAI,CAACC,SAAS,CAAChE,KAAK,CAAC,CAAC,EAAE;QAACqD,IAAI,EAAE;MAAkB,CAAC,CAAC;IACtE;IACA,OAAO3B,MAAM,CAAC1B,KAAK,CAAC;EACtB;EAEA;;;EAGAiE,KAAKA,CAAUzD,OAYd;IAECA,OAAO,GAAGA,OAAO,IAAI,EAAE;IAEvB;IACA,IAAIqB,IAAI,GAAG,IAAI,CAACW,aAAa;IAC7B,KAAK,MAAM0B,SAAS,IAAI,IAAI,CAACxB,KAAK,CAACyB,MAAM,EAAE,EAAE;MAC3CtC,IAAI,GAAGqC,SAAS,CAACtC,MAAM,CAACC,IAAI,CAAC;IAC/B;IACA,MAAMuC,GAAG,GAAG,IAAI,CAAC7B,OAAO,GAAGV,IAAI;IAE/B;IACA,IAAIwC,UAAU,GAAG,IAAI5E,UAAU,CAAC;MAC9B6E,OAAO,EAAElE;KACV,CAAC;IACF,KAAK,MAAMmE,UAAU,IAAI,IAAI,CAAC3B,MAAM,CAACuB,MAAM,EAAE,EAAE;MAC7CE,UAAU,GAAGE,UAAU,CAAC3C,MAAM,CAACyC,UAAU,CAAC;IAC5C;IAEA;IACA,IAAIG,WAAW,GAAG,IAAI9E,WAAW,EAAE;IACnC,IAAIc,OAAO,CAACiE,MAAM,EAAE;MAClBD,WAAW,GAAGA,WAAW,CAAC5C,MAAM,CAAC,QAAQ,EAAEpB,OAAO,CAACiE,MAAM,CAAC;IAC5D;IACA,KAAK,MAAMC,WAAW,IAAI,IAAI,CAAC7B,OAAO,CAACsB,MAAM,EAAE,EAAE;MAC/CK,WAAW,GAAGE,WAAW,CAAC9C,MAAM,CAAC4C,WAAW,CAAC;IAC/C;IAEA;IACA,IAAI,IAAI,CAACpB,gBAAgB,IAAI,EAAE,IAAI,CAACM,YAAY,YAAYG,QAAQ,CAAC,EAAE;MACrEW,WAAW,GAAGA,WAAW,CAAC1B,GAAG,CAAC,cAAc,EAAE,IAAI,CAACM,gBAAgB,CAAC;IACtE;IAEA;IACA,OAAO,IAAI5D,WAAW,CAAI,IAAI,CAACiD,MAAM,CAACkC,WAAW,EAAE,EAAEP,GAAG,EAAE,IAAI,CAACV,YAAY,EAAE;MAC3EvB,MAAM,EAAEkC,UAAU;MAClBhC,OAAO,EAAEmC,WAAW;MACpBI,YAAY,EAAEpE,OAAO,CAACoE,YAAY;MAClCC,cAAc,EAAErE,OAAO,CAACqE,cAAc;MACtCC,OAAO,EAAEtE,OAAO,CAACsE;KAClB,CAAC;EACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}