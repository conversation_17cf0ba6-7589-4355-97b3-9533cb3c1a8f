{"ast": null, "code": "import toDate from \"../toDate/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name isWithinInterval\n * @category Interval Helpers\n * @summary Is the given date within the interval?\n *\n * @description\n * Is the given date within the interval? (Including start and end.)\n *\n * @param {Date|Number} date - the date to check\n * @param {Interval} interval - the interval to check\n * @returns {Boolean} the date is within the interval\n * @throws {TypeError} 2 arguments required\n * @throws {RangeError} The start of an interval cannot be after its end\n * @throws {RangeError} Date in interval cannot be `Invalid Date`\n *\n * @example\n * // For the date within the interval:\n * isWithinInterval(new Date(2014, 0, 3), {\n *   start: new Date(2014, 0, 1),\n *   end: new Date(2014, 0, 7)\n * })\n * //=> true\n *\n * @example\n * // For the date outside of the interval:\n * isWithinInterval(new Date(2014, 0, 10), {\n *   start: new Date(2014, 0, 1),\n *   end: new Date(2014, 0, 7)\n * })\n * //=> false\n *\n * @example\n * // For date equal to interval start:\n * isWithinInterval(date, { start, end: date }) // => true\n *\n * @example\n * // For date equal to interval end:\n * isWithinInterval(date, { start: date, end }) // => true\n */\nexport default function isWithinInterval(dirtyDate, interval) {\n  requiredArgs(2, arguments);\n  var time = toDate(dirtyDate).getTime();\n  var startTime = toDate(interval.start).getTime();\n  var endTime = toDate(interval.end).getTime();\n\n  // Throw an exception if start date is after end date or if any date is `Invalid Date`\n  if (!(startTime <= endTime)) {\n    throw new RangeError('Invalid interval');\n  }\n  return time >= startTime && time <= endTime;\n}", "map": {"version": 3, "names": ["toDate", "requiredArgs", "isWithinInterval", "dirtyDate", "interval", "arguments", "time", "getTime", "startTime", "start", "endTime", "end", "RangeError"], "sources": ["C:/Users/<USER>/Documents/jeansalechange/adminFront/node_modules/date-fns/esm/isWithinInterval/index.js"], "sourcesContent": ["import toDate from \"../toDate/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name isWithinInterval\n * @category Interval Helpers\n * @summary Is the given date within the interval?\n *\n * @description\n * Is the given date within the interval? (Including start and end.)\n *\n * @param {Date|Number} date - the date to check\n * @param {Interval} interval - the interval to check\n * @returns {Boolean} the date is within the interval\n * @throws {TypeError} 2 arguments required\n * @throws {RangeError} The start of an interval cannot be after its end\n * @throws {RangeError} Date in interval cannot be `Invalid Date`\n *\n * @example\n * // For the date within the interval:\n * isWithinInterval(new Date(2014, 0, 3), {\n *   start: new Date(2014, 0, 1),\n *   end: new Date(2014, 0, 7)\n * })\n * //=> true\n *\n * @example\n * // For the date outside of the interval:\n * isWithinInterval(new Date(2014, 0, 10), {\n *   start: new Date(2014, 0, 1),\n *   end: new Date(2014, 0, 7)\n * })\n * //=> false\n *\n * @example\n * // For date equal to interval start:\n * isWithinInterval(date, { start, end: date }) // => true\n *\n * @example\n * // For date equal to interval end:\n * isWithinInterval(date, { start: date, end }) // => true\n */\nexport default function isWithinInterval(dirtyDate, interval) {\n  requiredArgs(2, arguments);\n  var time = toDate(dirtyDate).getTime();\n  var startTime = toDate(interval.start).getTime();\n  var endTime = toDate(interval.end).getTime();\n\n  // Throw an exception if start date is after end date or if any date is `Invalid Date`\n  if (!(startTime <= endTime)) {\n    throw new RangeError('Invalid interval');\n  }\n  return time >= startTime && time <= endTime;\n}"], "mappings": "AAAA,OAAOA,MAAM,MAAM,oBAAoB;AACvC,OAAOC,YAAY,MAAM,+BAA+B;AACxD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,SAASC,gBAAgBA,CAACC,SAAS,EAAEC,QAAQ,EAAE;EAC5DH,YAAY,CAAC,CAAC,EAAEI,SAAS,CAAC;EAC1B,IAAIC,IAAI,GAAGN,MAAM,CAACG,SAAS,CAAC,CAACI,OAAO,CAAC,CAAC;EACtC,IAAIC,SAAS,GAAGR,MAAM,CAACI,QAAQ,CAACK,KAAK,CAAC,CAACF,OAAO,CAAC,CAAC;EAChD,IAAIG,OAAO,GAAGV,MAAM,CAACI,QAAQ,CAACO,GAAG,CAAC,CAACJ,OAAO,CAAC,CAAC;;EAE5C;EACA,IAAI,EAAEC,SAAS,IAAIE,OAAO,CAAC,EAAE;IAC3B,MAAM,IAAIE,UAAU,CAAC,kBAAkB,CAAC;EAC1C;EACA,OAAON,IAAI,IAAIE,SAAS,IAAIF,IAAI,IAAII,OAAO;AAC7C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}