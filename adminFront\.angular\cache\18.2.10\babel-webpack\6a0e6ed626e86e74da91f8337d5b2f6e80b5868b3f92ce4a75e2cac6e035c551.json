{"ast": null, "code": "import { of as observableOf } from 'rxjs';\nimport { SolarData } from '../data/solar';\nimport * as i0 from \"@angular/core\";\nexport class SolarService extends SolarData {\n  constructor() {\n    super(...arguments);\n    this.value = 42;\n  }\n  getSolarData() {\n    return observableOf(this.value);\n  }\n  static {\n    this.ɵfac = /*@__PURE__*/(() => {\n      let ɵSolarService_BaseFactory;\n      return function SolarService_Factory(__ngFactoryType__) {\n        return (ɵSolarService_BaseFactory || (ɵSolarService_BaseFactory = i0.ɵɵgetInheritedFactory(SolarService)))(__ngFactoryType__ || SolarService);\n      };\n    })();\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: SolarService,\n      factory: SolarService.ɵfac\n    });\n  }\n}", "map": {"version": 3, "names": ["of", "observableOf", "SolarData", "SolarService", "constructor", "value", "getSolarData", "__ngFactoryType__", "factory", "ɵfac"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\@core\\mock\\solar.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport { of as observableOf,  Observable } from 'rxjs';\r\nimport { SolarData } from '../data/solar';\r\n\r\n@Injectable()\r\nexport class SolarService extends SolarData {\r\n  private value = 42;\r\n\r\n  getSolarData(): Observable<number> {\r\n    return observableOf(this.value);\r\n  }\r\n}\r\n"], "mappings": "AACA,SAASA,EAAE,IAAIC,YAAY,QAAqB,MAAM;AACtD,SAASC,SAAS,QAAQ,eAAe;;AAGzC,OAAM,MAAOC,YAAa,SAAQD,SAAS;EAD3CE,YAAA;;IAEU,KAAAC,KAAK,GAAG,EAAE;;EAElBC,YAAYA,CAAA;IACV,OAAOL,YAAY,CAAC,IAAI,CAACI,KAAK,CAAC;EACjC;;;;;mGALWF,YAAY,IAAAI,iBAAA,IAAZJ,YAAY;MAAA;IAAA;EAAA;;;aAAZA,YAAY;MAAAK,OAAA,EAAZL,YAAY,CAAAM;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}