{"ast": null, "code": "import { DestroyRef, inject } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { takeUntilDestroyed } from '@angular/core/rxjs-interop';\nimport { EnumFileType } from 'src/app/shared/enum/enumFileType';\nimport { DateFormatHourPipe } from 'src/app/@theme/pipes';\nimport { SharedModule } from '../../components/shared.module';\nimport { BaseComponent } from '../../components/base/baseComponent';\nimport { cApproveStatusPipe } from 'src/app/@theme/pipes/mapping.pipe';\nimport { tap } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/shared/helper/allowHelper\";\nimport * as i2 from \"@nebular/theme\";\nimport * as i3 from \"src/app/shared/helper/validationHelper\";\nimport * as i4 from \"src/app/shared/services/message.service\";\nimport * as i5 from \"src/services/api/services\";\nimport * as i6 from \"@angular/router\";\nimport * as i7 from \"src/app/shared/services/utility.service\";\nimport * as i8 from \"@angular/common\";\nimport * as i9 from \"@angular/forms\";\nimport * as i10 from \"../../components/pagination/pagination.component\";\nimport * as i11 from \"../../../@theme/directives/label.directive\";\nconst _c0 = a0 => ({\n  \"required-field\": a0\n});\nconst _c1 = a0 => ({\n  \"download\": a0\n});\nfunction RelatedDocumentsComponent_tr_39_ng_container_15_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"button\", 23);\n    i0.ɵɵlistener(\"click\", function RelatedDocumentsComponent_tr_39_ng_container_15_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const item_r5 = i0.ɵɵnextContext().$implicit;\n      const ctx_r5 = i0.ɵɵnextContext();\n      const dialog_r3 = i0.ɵɵreference(47);\n      return i0.ɵɵresetView(ctx_r5.onEdit(dialog_r3, item_r5));\n    });\n    i0.ɵɵelement(2, \"i\", 24);\n    i0.ɵɵtext(3, \"\\u7DE8\\u8F2F \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"button\", 25);\n    i0.ɵɵlistener(\"click\", function RelatedDocumentsComponent_tr_39_ng_container_15_Template_button_click_4_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r6 = i0.ɵɵnextContext();\n      const item_r5 = ctx_r6.$implicit;\n      const i_r8 = ctx_r6.index;\n      const ctx_r5 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r5.onDelete(item_r5, i_r8));\n    });\n    i0.ɵɵelement(5, \"i\", 26);\n    i0.ɵɵtext(6, \"\\u522A\\u9664 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n}\nfunction RelatedDocumentsComponent_tr_39_ng_container_16_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"button\", 25);\n    i0.ɵɵlistener(\"click\", function RelatedDocumentsComponent_tr_39_ng_container_16_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r6 = i0.ɵɵnextContext();\n      const item_r5 = ctx_r6.$implicit;\n      const i_r8 = ctx_r6.index;\n      const ctx_r5 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r5.onDelete(item_r5, i_r8));\n    });\n    i0.ɵɵelement(2, \"i\", 26);\n    i0.ɵɵtext(3, \"\\u522A\\u9664 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n}\nfunction RelatedDocumentsComponent_tr_39_ng_container_17_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"button\", 27);\n    i0.ɵɵlistener(\"click\", function RelatedDocumentsComponent_tr_39_ng_container_17_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const item_r5 = i0.ɵɵnextContext().$implicit;\n      const ctx_r5 = i0.ɵɵnextContext();\n      const reasonReject_r11 = i0.ɵɵreference(43);\n      return i0.ɵɵresetView(ctx_r5.showReason(reasonReject_r11, item_r5));\n    });\n    i0.ɵɵelement(2, \"i\", 28);\n    i0.ɵɵtext(3, \"\\u9000\\u56DE\\u539F\\u56E0 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"button\", 25);\n    i0.ɵɵlistener(\"click\", function RelatedDocumentsComponent_tr_39_ng_container_17_Template_button_click_4_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r6 = i0.ɵɵnextContext();\n      const item_r5 = ctx_r6.$implicit;\n      const i_r8 = ctx_r6.index;\n      const ctx_r5 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r5.onDelete(item_r5, i_r8));\n    });\n    i0.ɵɵelement(5, \"i\", 26);\n    i0.ɵɵtext(6, \"\\u522A\\u9664 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n}\nfunction RelatedDocumentsComponent_tr_39_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"td\");\n    i0.ɵɵtext(8);\n    i0.ɵɵpipe(9, \"cApproveStatus\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"td\");\n    i0.ɵɵtext(11);\n    i0.ɵɵpipe(12, \"dateFormatHour\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"td\");\n    i0.ɵɵelementContainerStart(14, 21);\n    i0.ɵɵtemplate(15, RelatedDocumentsComponent_tr_39_ng_container_15_Template, 7, 0, \"ng-container\", 22)(16, RelatedDocumentsComponent_tr_39_ng_container_16_Template, 4, 0, \"ng-container\", 22)(17, RelatedDocumentsComponent_tr_39_ng_container_17_Template, 7, 0, \"ng-container\", 22);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const item_r5 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r5.CID);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r5.CCategoryName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r5.CName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(9, 9, item_r5.CApproveStatus));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(item_r5.CApproveDate ? i0.ɵɵpipeBind1(12, 11, item_r5.CApproveDate) : \"\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngSwitch\", item_r5.CApproveStatus);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", 1);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", 2);\n  }\n}\nfunction RelatedDocumentsComponent_ng_template_42_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-card\", 29)(1, \"nb-card-body\")(2, \"div\", 30)(3, \"span\", 31);\n    i0.ɵɵtext(4, \"\\u9000\\u56DE\\u539F\\u56E0\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(5, \"textarea\", 32);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"nb-card-footer\", 33)(7, \"button\", 34);\n    i0.ɵɵlistener(\"click\", function RelatedDocumentsComponent_ng_template_42_Template_button_click_7_listener() {\n      const ref_r13 = i0.ɵɵrestoreView(_r12).dialogRef;\n      const ctx_r5 = i0.ɵɵnextContext();\n      ref_r13.close();\n      return i0.ɵɵresetView(ctx_r5.currentItem = {});\n    });\n    i0.ɵɵtext(8, \"\\u95DC\\u9589\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"value\", ctx_r5.currentItem.CExamineRejectNote)(\"rows\", 4)(\"disabled\", true);\n  }\n}\nfunction RelatedDocumentsComponent_ng_template_44_div_17_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r16 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 47)(1, \"div\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\", 48)(4, \"button\", 49);\n    i0.ɵɵlistener(\"click\", function RelatedDocumentsComponent_ng_template_44_div_17_div_1_Template_button_click_4_listener() {\n      const file_r17 = i0.ɵɵrestoreView(_r16).$implicit;\n      const ctx_r5 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r5.removeFile(file_r17.id));\n    });\n    i0.ɵɵtext(5, \"\\u5220\\u9664\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const file_r17 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", file_r17.name, \" \");\n  }\n}\nfunction RelatedDocumentsComponent_ng_template_44_div_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 45);\n    i0.ɵɵtemplate(1, RelatedDocumentsComponent_ng_template_44_div_17_div_1_Template, 6, 1, \"div\", 46);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r5.listFiles);\n  }\n}\nfunction RelatedDocumentsComponent_ng_template_44_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r14 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-card\", 35)(1, \"nb-card-header\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"nb-card-body\")(4, \"div\", 7)(5, \"label\", 36);\n    i0.ɵɵtext(6, \"\\u5206\\u985E\\u540D\\u7A31\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"input\", 9);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function RelatedDocumentsComponent_ng_template_44_Template_input_ngModelChange_7_listener($event) {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r5 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r5.currentItem.CCategoryName, $event) || (ctx_r5.currentItem.CCategoryName = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 37)(9, \"div\", 38)(10, \"label\", 39);\n    i0.ɵɵtext(11, \"\\u4E0A\\u50B3\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(12, \"input\", 40, 3);\n    i0.ɵɵlistener(\"change\", function RelatedDocumentsComponent_ng_template_44_Template_input_change_12_listener($event) {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r5 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r5.onMultipleFile($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"button\", 41);\n    i0.ɵɵlistener(\"click\", function RelatedDocumentsComponent_ng_template_44_Template_button_click_14_listener() {\n      i0.ɵɵrestoreView(_r14);\n      const uploadFileRef_r15 = i0.ɵɵreference(13);\n      return i0.ɵɵresetView(uploadFileRef_r15.click());\n    });\n    i0.ɵɵelement(15, \"i\", 42);\n    i0.ɵɵtext(16, \"\\u4E0A\\u50B3\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(17, RelatedDocumentsComponent_ng_template_44_div_17_Template, 2, 1, \"div\", 43);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"nb-card-footer\", 33)(19, \"button\", 44);\n    i0.ɵɵlistener(\"click\", function RelatedDocumentsComponent_ng_template_44_Template_button_click_19_listener() {\n      const ref_r18 = i0.ɵɵrestoreView(_r14).dialogRef;\n      return i0.ɵɵresetView(ref_r18.close());\n    });\n    i0.ɵɵtext(20, \"\\u53D6\\u6D88\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"button\", 13);\n    i0.ɵɵlistener(\"click\", function RelatedDocumentsComponent_ng_template_44_Template_button_click_21_listener() {\n      const ref_r18 = i0.ɵɵrestoreView(_r14).dialogRef;\n      const ctx_r5 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r5.onSaveMultiple(ref_r18));\n    });\n    i0.ɵɵtext(22, \"\\u5132\\u5B58\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r5.isAddNew ? \"\\u65B0\\u589E\" : \"\\u7DE8\\u8F2F\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r5.currentItem.CCategoryName);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(4, _c0, ctx_r5.isAddNew));\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngIf\", ctx_r5.listFiles.length > 0);\n  }\n}\nfunction RelatedDocumentsComponent_ng_template_46_div_8_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 60);\n    i0.ɵɵelement(1, \"img\", 61);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"src\", ctx_r5.currentItem.CFile, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction RelatedDocumentsComponent_ng_template_46_div_8_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r20 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 62);\n    i0.ɵɵlistener(\"click\", function RelatedDocumentsComponent_ng_template_46_div_8_div_4_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r20);\n      const ctx_r5 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r5.openNewFile(ctx_r5.currentItem.CFile));\n    });\n    i0.ɵɵelement(1, \"nb-icon\", 63);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RelatedDocumentsComponent_ng_template_46_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 7)(1, \"label\", 57);\n    i0.ɵɵtext(2, \"\\u6A94\\u6848\\u9023\\u7D50\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, RelatedDocumentsComponent_ng_template_46_div_8_div_3_Template, 2, 1, \"div\", 58)(4, RelatedDocumentsComponent_ng_template_46_div_8_div_4_Template, 2, 0, \"div\", 59);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r5.checkImage(ctx_r5.currentItem.CFile));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r5.checkImage(ctx_r5.currentItem.CFile));\n  }\n}\nfunction RelatedDocumentsComponent_ng_template_46_div_18_img_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 70);\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"src\", ctx_r5.uploadedFile.Cimg, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction RelatedDocumentsComponent_ng_template_46_div_18_i_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 71);\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(1, _c1, !ctx_r5.isAddNew));\n  }\n}\nfunction RelatedDocumentsComponent_ng_template_46_div_18_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r22 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\")(1, \"ul\", 64)(2, \"li\", 65);\n    i0.ɵɵtemplate(3, RelatedDocumentsComponent_ng_template_46_div_18_img_3_Template, 1, 1, \"img\", 66)(4, RelatedDocumentsComponent_ng_template_46_div_18_i_4_Template, 1, 3, \"i\", 67);\n    i0.ɵɵelementStart(5, \"a\", 68);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"nb-icon\", 69);\n    i0.ɵɵlistener(\"click\", function RelatedDocumentsComponent_ng_template_46_div_18_Template_nb_icon_click_7_listener() {\n      i0.ɵɵrestoreView(_r22);\n      const ctx_r5 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r5.deleteItem(ctx_r5.uploadedFile));\n    });\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", !(ctx_r5.uploadedFile.CName == null ? null : ctx_r5.uploadedFile.CName.includes(\"pdf\")));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r5.uploadedFile.CName == null ? null : ctx_r5.uploadedFile.CName.includes(\"pdf\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"href\", ctx_r5.uploadedFile.Cimg ? ctx_r5.uploadedFile.Cimg : ctx_r5.uploadedFile.CFile, i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r5.uploadedFile.CName);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"hidden\", !ctx_r5.isAddNew);\n  }\n}\nfunction RelatedDocumentsComponent_ng_template_46_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r19 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-card\", 35)(1, \"nb-card-header\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"nb-card-body\")(4, \"div\", 7)(5, \"label\", 36);\n    i0.ɵɵtext(6, \"\\u5206\\u985E\\u540D\\u7A31\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"input\", 9);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function RelatedDocumentsComponent_ng_template_46_Template_input_ngModelChange_7_listener($event) {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r5 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r5.currentItem.CCategoryName, $event) || (ctx_r5.currentItem.CCategoryName = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(8, RelatedDocumentsComponent_ng_template_46_div_8_Template, 5, 2, \"div\", 50);\n    i0.ɵɵelementStart(9, \"div\", 37)(10, \"div\", 38)(11, \"label\", 39);\n    i0.ɵɵtext(12, \"\\u4E0A\\u50B3\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"input\", 51, 3);\n    i0.ɵɵlistener(\"change\", function RelatedDocumentsComponent_ng_template_46_Template_input_change_13_listener($event) {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r5 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r5.onChooseFile($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"button\", 41);\n    i0.ɵɵlistener(\"click\", function RelatedDocumentsComponent_ng_template_46_Template_button_click_15_listener() {\n      i0.ɵɵrestoreView(_r19);\n      const uploadFileRef_r21 = i0.ɵɵreference(14);\n      return i0.ɵɵresetView(uploadFileRef_r21.click());\n    });\n    i0.ɵɵelement(16, \"i\", 42);\n    i0.ɵɵtext(17, \"\\u4E0A\\u50B3\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(18, RelatedDocumentsComponent_ng_template_46_div_18_Template, 8, 5, \"div\", 52);\n    i0.ɵɵelementStart(19, \"div\", 7)(20, \"label\", 53);\n    i0.ɵɵtext(21, \"\\u72C0\\u614B\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"nb-select\", 54);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function RelatedDocumentsComponent_ng_template_46_Template_nb_select_ngModelChange_22_listener($event) {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r5 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r5.currentItem.CStatus, $event) || (ctx_r5.currentItem.CStatus = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementStart(23, \"nb-option\", 55);\n    i0.ɵɵtext(24, \"\\u555F\\u7528\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(25, \"nb-option\", 55);\n    i0.ɵɵtext(26, \"\\u505C\\u7528\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(27, \"div\", 7)(28, \"label\", 53);\n    i0.ɵɵtext(29, \"\\u9001\\u5BE9\\u8AAA\\u660E\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(30, \"textarea\", 56);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function RelatedDocumentsComponent_ng_template_46_Template_textarea_ngModelChange_30_listener($event) {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r5 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r5.currentItem.CSubmitRemark, $event) || (ctx_r5.currentItem.CSubmitRemark = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(31, \"nb-card-footer\", 33)(32, \"button\", 44);\n    i0.ɵɵlistener(\"click\", function RelatedDocumentsComponent_ng_template_46_Template_button_click_32_listener() {\n      const ref_r23 = i0.ɵɵrestoreView(_r19).dialogRef;\n      return i0.ɵɵresetView(ref_r23.close());\n    });\n    i0.ɵɵtext(33, \"\\u53D6\\u6D88\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(34, \"button\", 13);\n    i0.ɵɵlistener(\"click\", function RelatedDocumentsComponent_ng_template_46_Template_button_click_34_listener() {\n      const ref_r23 = i0.ɵɵrestoreView(_r19).dialogRef;\n      const ctx_r5 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r5.onSave(ref_r23));\n    });\n    i0.ɵɵtext(35, \"\\u5132\\u5B58\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r5.isAddNew ? \"\\u65B0\\u589E\" : \"\\u7DE8\\u8F2F\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r5.currentItem.CCategoryName);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r5.isAddNew);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(10, _c0, ctx_r5.isAddNew));\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngIf\", ctx_r5.uploadedFile);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r5.currentItem.CStatus);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"value\", 1);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"value\", 0);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r5.currentItem.CSubmitRemark);\n    i0.ɵɵproperty(\"rows\", 4);\n  }\n}\nexport let RelatedDocumentsComponent = /*#__PURE__*/(() => {\n  class RelatedDocumentsComponent extends BaseComponent {\n    constructor(allow, dialogService, valid, message, buildCaseFileService, activatedRoute, _utilityService) {\n      super(allow);\n      this.allow = allow;\n      this.dialogService = dialogService;\n      this.valid = valid;\n      this.message = message;\n      this.buildCaseFileService = buildCaseFileService;\n      this.activatedRoute = activatedRoute;\n      this._utilityService = _utilityService;\n      this.filterCategoryName = '';\n      this.filterFileName = '';\n      this.buildCaseId = 1;\n      this.listBuildCaseFile = [];\n      this.isAddNew = false;\n      this.uploadedFile = undefined;\n      this.listFiles = [];\n      this.destroy = inject(DestroyRef);\n      this.currentPage = -1;\n    }\n    ngOnInit() {\n      this.buildCaseId = 1;\n      this.buildCaseId = parseInt(this.activatedRoute.snapshot.paramMap.get(\"id\"));\n      this.getListBuildCaseFile(1);\n    }\n    getListBuildCaseFile(page) {\n      this.buildCaseFileService.apiBuildCaseFileGetListBuildCaseFilePost$Json({\n        body: {\n          CBuildCaseID: this.buildCaseId,\n          CName: this.filterFileName ? this.filterFileName : undefined,\n          CCategoryName: this.filterCategoryName ? this.filterCategoryName : undefined,\n          PageIndex: page,\n          PageSize: 10\n        }\n      }).pipe(takeUntilDestroyed(this.destroy)).subscribe(res => {\n        this.listBuildCaseFile = res.Entries ?? [];\n        this.totalRecords = res.TotalItems;\n      });\n    }\n    onAddNew(dialog) {\n      this.isAddNew = true;\n      this.uploadedFile = undefined;\n      this.currentItem = {\n        CStatus: 0\n      };\n      this.dialogService.open(dialog);\n    }\n    onAddMultiplesFile(dialog) {\n      this.isAddNew = true;\n      this.uploadedFile = undefined;\n      this.currentItem = {\n        CCategoryName: \"\"\n      };\n      this.dialogService.open(dialog);\n    }\n    onEdit(ref, item) {\n      this.isAddNew = false;\n      this.uploadedFile = undefined;\n      this.currentItem = {};\n      this.buildCaseFileService.apiBuildCaseFileGetBuildCaseFileByIdPost$Json({\n        body: {\n          CBuildCaseFileID: item.CID\n        }\n      }).pipe(takeUntilDestroyed(this.destroy)).subscribe(res => {\n        this.currentItem = res.Entries;\n      });\n      this.dialogService.open(ref);\n    }\n    onDelete(item, index) {\n      if (window.confirm(`確定要刪除【項目${item.CID}】?`)) {\n        this.buildCaseFileService.apiBuildCaseFileDeleteBuildCaseFilePost$Json({\n          body: {\n            CBuildCaseFileID: item.CID\n          }\n        }).pipe(takeUntilDestroyed(this.destroy)).subscribe(res => {\n          if (res.StatusCode === 0) {\n            this.message.showSucessMSG('執行成功');\n            this.getListBuildCaseFile(this.currentPage);\n          }\n        });\n      }\n    }\n    showReason(ref, item) {\n      this.dialogService.open(ref);\n      this.currentItem = item;\n    }\n    base64ToBlob(base64, contentType = '') {\n      const base64Data = base64.split(',')[1] || base64;\n      // Decode base64 string\n      const byteCharacters = atob(base64Data);\n      // Create a byte array with length equal to the number of bytes in the string\n      const byteArrays = [];\n      for (let offset = 0; offset < byteCharacters.length; offset += 512) {\n        const slice = byteCharacters.slice(offset, offset + 512);\n        const byteNumbers = new Array(slice.length);\n        for (let i = 0; i < slice.length; i++) {\n          byteNumbers[i] = slice.charCodeAt(i);\n        }\n        const byteArray = new Uint8Array(byteNumbers);\n        byteArrays.push(byteArray);\n      }\n      return new Blob(byteArrays, {\n        type: contentType\n      });\n    }\n    onSave(ref) {\n      this.validation();\n      if (this.valid.errorMessages.length > 0) {\n        this.message.showErrorMSGs(this.valid.errorMessages);\n        return;\n      }\n      let request = {\n        body: {\n          CBuildCaseID: this.isAddNew ? this.buildCaseId : undefined,\n          CBuildCaseFileID: this.isAddNew ? undefined : this.currentItem.CID,\n          CStatus: this.currentItem.CStatus,\n          CSubmitRemark: this.currentItem.CSubmitRemark ? this.currentItem.CSubmitRemark : undefined,\n          CFile: this.isAddNew == false ? null : this.uploadedFile.CFileUpload,\n          CCategoryName: this.currentItem.CCategoryName ? this.currentItem.CCategoryName : undefined\n        }\n      };\n      this.buildCaseFileService.apiBuildCaseFileSaveBuildCaseFilePost$Json(request).subscribe(res => {\n        if (res.StatusCode === 0) {\n          this.message.showSucessMSG('儲存成功');\n        } else {\n          this.message.showErrorMSG(res.Message);\n        }\n        ref.close();\n        this.getListBuildCaseFile(1);\n      });\n    }\n    validation() {\n      this.valid.clear();\n      this.valid.required(`送審說明`, this.currentItem.CSubmitRemark);\n      this.valid.required(`分類名稱`, this.currentItem.CCategoryName);\n      if (this.isAddNew && !this.uploadedFile) {\n        this.valid.addErrorMessage(`上傳 必填`);\n      }\n    }\n    onChooseFile(event) {\n      let files = event.target.files;\n      const fileRegex = /pdf|jpg|jpeg|png/i;\n      if (!fileRegex.test(files[0].type)) {\n        this.message.showErrorMSG('檔案格式錯誤，僅限pdf、圖片檔');\n        return;\n      }\n      let reader = new FileReader();\n      reader.readAsDataURL(files[0]);\n      reader.onload = e => {\n        let file = \"\";\n        if (!files[0].name.includes('pdf')) {\n          file = URL.createObjectURL(files[0]);\n        }\n        this.uploadedFile = {\n          CName: files[0].name,\n          CFile: e.target?.result?.toString().split(',')[1],\n          Cimg: files[0].name.includes('pdf') ? files[0] : file,\n          CFileUpload: files[0],\n          CFileType: EnumFileType.PDF\n        };\n        event.target.value = null;\n      };\n    }\n    onMultipleFile(event) {\n      for (let index = 0; index < event.target.files.length; index++) {\n        const file = event.target.files[index];\n        if (file) {\n          let reader = new FileReader();\n          reader.readAsDataURL(file);\n          reader.onload = () => {\n            let base64Str = reader.result;\n            if (!base64Str) {\n              return;\n            }\n            file.id = new Date().getTime();\n            this.listFiles.push({\n              id: new Date().getTime(),\n              name: file.name,\n              data: base64Str,\n              extension: this._utilityService.getFileExtension(file.name),\n              CFile: file\n            });\n            event.target.value = null;\n          };\n        }\n      }\n    }\n    deleteItem(item) {\n      if (window.confirm(`確定要移除【${item.CName}】?`)) {\n        this.uploadedFile = undefined;\n      }\n    }\n    removeFile(id) {\n      this.listFiles = this.listFiles.filter(x => x.id != id);\n    }\n    validationMultiple() {\n      this.valid.clear();\n      this.valid.required(`分類名稱`, this.currentItem.CCategoryName);\n      if (this.isAddNew && this.listFiles.length == 0) {\n        this.valid.addErrorMessage(`上傳 必填`);\n      }\n    }\n    onSaveMultiple(ref) {\n      this.validationMultiple();\n      if (this.valid.errorMessages.length > 0) {\n        this.message.showErrorMSGs(this.valid.errorMessages);\n        return;\n      }\n      let listFilesUpload = this.listFiles.map(x => x.CFile);\n      this.buildCaseFileService.apiBuildCaseFileSaveMultipleBuildCaseFilePost$Json({\n        body: {\n          CBuildCaseID: this.buildCaseId,\n          CCategoryName: this.currentItem.CCategoryName,\n          CFiles: listFilesUpload\n        }\n      }).pipe(tap(res => {\n        if (res.StatusCode === 0) {\n          this.message.showSucessMSG('儲存成功');\n          ref.close();\n          this.getListBuildCaseFile(1);\n          this.listFiles = [];\n          this.currentItem = {};\n        } else {\n          this.message.showErrorMSG(res.Message);\n        }\n      })).subscribe();\n    }\n    getListPageChange(page) {\n      this.currentPage = page;\n      this.getListBuildCaseFile(page);\n    }\n    checkImage(fileName) {\n      return this._utilityService.getFileExtension(fileName).includes('png') || this._utilityService.getFileExtension(fileName).includes('jpeg') || this._utilityService.getFileExtension(fileName).includes('jpg');\n    }\n    openNewFile(fileName) {\n      console.log(fileName);\n      window.open(`${fileName}`, '_blank');\n    }\n    static {\n      this.ɵfac = function RelatedDocumentsComponent_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || RelatedDocumentsComponent)(i0.ɵɵdirectiveInject(i1.AllowHelper), i0.ɵɵdirectiveInject(i2.NbDialogService), i0.ɵɵdirectiveInject(i3.ValidationHelper), i0.ɵɵdirectiveInject(i4.MessageService), i0.ɵɵdirectiveInject(i5.BuildCaseFileService), i0.ɵɵdirectiveInject(i6.ActivatedRoute), i0.ɵɵdirectiveInject(i7.UtilityService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: RelatedDocumentsComponent,\n        selectors: [[\"app-related-documents\"]],\n        standalone: true,\n        features: [i0.ɵɵInheritDefinitionFeature, i0.ɵɵStandaloneFeature],\n        decls: 48,\n        vars: 6,\n        consts: [[\"reasonReject\", \"\"], [\"multiple\", \"\"], [\"dialog\", \"\"], [\"uploadFileRef\", \"\"], [\"accent\", \"success\"], [1, \"row\"], [1, \"col-md-6\", \"col-xl-4\"], [1, \"form-group\", \"d-flex\", \"align-items-center\"], [\"for\", \"name\", 1, \"label\", \"mr-2\"], [\"type\", \"text\", \"nbInput\", \"\", \"fullWidth\", \"\", \"placeholder\", \"\\u8ACB\\u8F38\\u5165\\u5206\\u985E\\u540D\\u7A31\", 3, \"ngModelChange\", \"ngModel\"], [\"type\", \"text\", \"nbInput\", \"\", \"fullWidth\", \"\", \"placeholder\", \"\\u8ACB\\u8F38\\u5165\\u6A94\\u540D\", 3, \"ngModelChange\", \"ngModel\"], [1, \"d-flex\", \"justify-end\", \"gap-2\"], [1, \"btn\", \"btn-info\", 3, \"click\"], [1, \"btn\", \"btn-success\", 3, \"click\"], [1, \"table-responsive\", \"mt-4\"], [1, \"table\", \"table-striped\", \"border\", 2, \"min-width\", \"1000px\", \"background-color\", \"#f3f3f3\"], [2, \"background-color\", \"#27ae60\", \"color\", \"white\"], [2, \"min-width\", \"5%\"], [2, \"min-width\", \"15%\"], [4, \"ngFor\", \"ngForOf\"], [3, \"CollectionSizeChange\", \"PageChange\", \"PageSizeChange\", \"CollectionSize\", \"Page\", \"PageSize\"], [3, \"ngSwitch\"], [4, \"ngSwitchCase\"], [\"type\", \"button\", 1, \"btn\", \"btn-outline-success\", \"m-1\", \"btn-sm\", 3, \"click\"], [1, \"fas\", \"fa-edit\", \"mr-1\"], [\"type\", \"button\", 1, \"btn\", \"btn-outline-danger\", \"m-1\", \"btn-sm\", 3, \"click\"], [1, \"far\", \"fa-trash-alt\", \"mr-1\"], [\"type\", \"button\", 1, \"btn\", \"btn-outline-info\", \"m-1\", \"btn-sm\", 3, \"click\"], [1, \"fas\", \"fa-search\", \"mr-1\"], [2, \"width\", \"500px\"], [1, \"flex\", \"items-start\"], [1, \"w-[20%]\"], [\"nbInput\", \"\", 1, \"resize-none\", \"!max-w-full\", \"w-full\", 3, \"value\", \"rows\", \"disabled\"], [1, \"d-flex\", \"justify-content-center\"], [1, \"btn\", \"btn-info\", \"mr-2\", 3, \"click\"], [2, \"max-height\", \"95vh\", \"min-width\", \"400px\"], [\"for\", \"categoryName\", \"baseLabel\", \"\", 1, \"required-field\", \"mr-4\", 2, \"min-width\", \"75px\"], [1, \"form-group\", \"d-flex\", \"align-items-baseline\"], [1, \"d-flex\", \"flex-col\", \"mr-3\"], [\"for\", \"file\", \"baseLabel\", \"\", 1, \"mb-0\", 2, \"min-width\", \"75px\", 3, \"ngClass\"], [\"id\", \"file\", \"type\", \"file\", \"multiple\", \"\", \"hidden\", \"\", \"accept\", \".jpg,.jpeg,.png, .pdf\", 3, \"change\"], [1, \"btn\", \"btn-success\", \"btn-sm\", 3, \"click\"], [1, \"fas\", \"fa-plus\", \"mr-1\"], [\"class\", \"w-full\", 4, \"ngIf\"], [1, \"btn\", \"btn-danger\", \"mr-2\", 3, \"click\"], [1, \"w-full\"], [\"class\", \"flex items-center justify-between\", 4, \"ngFor\", \"ngForOf\"], [1, \"flex\", \"items-center\", \"justify-between\"], [1, \"text-center\"], [1, \"btn\", \"btn-outline-danger\", \"btn-sm\", \"m-1\", 3, \"click\"], [\"class\", \"form-group d-flex align-items-center\", 4, \"ngIf\"], [\"id\", \"file\", \"type\", \"file\", \"hidden\", \"\", \"accept\", \".jpg,.jpeg,.png, .pdf\", 3, \"change\"], [4, \"ngIf\"], [\"for\", \"status\", \"baseLabel\", \"\", 1, \"required-field\", \"mr-4\", 2, \"min-width\", \"75px\"], [\"fullWidth\", \"\", \"placeholder\", \"\\u8ACB\\u9078\\u64C7\\u6A13\\u5C64\", 3, \"ngModelChange\", \"ngModel\"], [3, \"value\"], [\"nbInput\", \"\", 1, \"resize-none\", \"!max-w-full\", \"w-full\", 3, \"ngModelChange\", \"ngModel\", \"rows\"], [\"for\", \"categoryName\", \"baseLabel\", \"\", 1, \"mr-4\", 2, \"min-width\", \"75px\"], [\"style\", \"width: 150px; height: 150px;\", 4, \"ngIf\"], [\"style\", \"cursor: pointer; width: 50px; height: 50px; border: 1px solid gray; border-radius: 10px; display: flex; align-items: center; justify-content: center;\", 3, \"click\", 4, \"ngIf\"], [2, \"width\", \"150px\", \"height\", \"150px\"], [1, \"fit-size\", 3, \"src\"], [2, \"cursor\", \"pointer\", \"width\", \"50px\", \"height\", \"50px\", \"border\", \"1px solid gray\", \"border-radius\", \"10px\", \"display\", \"flex\", \"align-items\", \"center\", \"justify-content\", \"center\", 3, \"click\"], [\"icon\", \"file-add-outline\"], [2, \"margin-left\", \"100px\"], [1, \"file-item\"], [\"alt\", \"\", \"style\", \"max-width:150px; width: 100%\", \"class\", \"mr-2\", 3, \"src\", 4, \"ngIf\"], [\"class\", \"fa-solid fa-file-pdf text-primary\", 3, \"ngClass\", 4, \"ngIf\"], [\"target\", \"_blank\", 1, \"text-primary\", \"download\", 3, \"href\"], [\"icon\", \"trash-2-outline\", 1, \"trash-icon\", 3, \"click\", \"hidden\"], [\"alt\", \"\", 1, \"mr-2\", 2, \"max-width\", \"150px\", \"width\", \"100%\", 3, \"src\"], [1, \"fa-solid\", \"fa-file-pdf\", \"text-primary\", 3, \"ngClass\"]],\n        template: function RelatedDocumentsComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            const _r1 = i0.ɵɵgetCurrentView();\n            i0.ɵɵelementStart(0, \"nb-card\", 4)(1, \"nb-card-header\");\n            i0.ɵɵtext(2, \" \\u76F8\\u95DC\\u6587\\u4EF6 \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(3, \"nb-card-body\")(4, \"div\", 5)(5, \"div\", 6)(6, \"div\", 7)(7, \"label\", 8);\n            i0.ɵɵtext(8, \"\\u5206\\u985E\\u540D\\u7A31\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(9, \"input\", 9);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function RelatedDocumentsComponent_Template_input_ngModelChange_9_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.filterCategoryName, $event) || (ctx.filterCategoryName = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(10, \"div\", 6)(11, \"div\", 7)(12, \"label\", 8);\n            i0.ɵɵtext(13, \"\\u6A94\\u540D\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(14, \"input\", 10);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function RelatedDocumentsComponent_Template_input_ngModelChange_14_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.filterFileName, $event) || (ctx.filterFileName = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵelementEnd()()()();\n            i0.ɵɵelementStart(15, \"div\", 11)(16, \"button\", 12);\n            i0.ɵɵlistener(\"click\", function RelatedDocumentsComponent_Template_button_click_16_listener() {\n              i0.ɵɵrestoreView(_r1);\n              const multiple_r2 = i0.ɵɵreference(45);\n              return i0.ɵɵresetView(ctx.onAddMultiplesFile(multiple_r2));\n            });\n            i0.ɵɵtext(17, \"\\u6279\\u6B21\\u532F\\u5165\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(18, \"button\", 12);\n            i0.ɵɵlistener(\"click\", function RelatedDocumentsComponent_Template_button_click_18_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.getListBuildCaseFile(1));\n            });\n            i0.ɵɵtext(19, \"\\u67E5\\u8A62\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(20, \"button\", 13);\n            i0.ɵɵlistener(\"click\", function RelatedDocumentsComponent_Template_button_click_20_listener() {\n              i0.ɵɵrestoreView(_r1);\n              const dialog_r3 = i0.ɵɵreference(47);\n              return i0.ɵɵresetView(ctx.onAddNew(dialog_r3));\n            });\n            i0.ɵɵtext(21, \"\\u65B0\\u589E\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(22, \"div\", 14)(23, \"table\", 15)(24, \"thead\")(25, \"tr\", 16)(26, \"th\", 17);\n            i0.ɵɵtext(27, \"\\u6D41\\u6C34\\u865F\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(28, \"th\", 18);\n            i0.ɵɵtext(29, \"\\u5206\\u985E\\u540D\\u7A31\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(30, \"th\", 18);\n            i0.ɵɵtext(31, \"\\u6A94\\u540D\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(32, \"th\", 18);\n            i0.ɵɵtext(33, \"\\u5BE9\\u6838\\u72C0\\u614B \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(34, \"th\", 18);\n            i0.ɵɵtext(35, \"\\u5BE9\\u6838\\u65E5\\u671F \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(36, \"th\", 17);\n            i0.ɵɵtext(37, \"\\u52D5\\u4F5C\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(38, \"tbody\");\n            i0.ɵɵtemplate(39, RelatedDocumentsComponent_tr_39_Template, 18, 13, \"tr\", 19);\n            i0.ɵɵelementEnd()()()();\n            i0.ɵɵelementStart(40, \"nb-card-footer\")(41, \"ngx-pagination\", 20);\n            i0.ɵɵtwoWayListener(\"CollectionSizeChange\", function RelatedDocumentsComponent_Template_ngx_pagination_CollectionSizeChange_41_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.totalRecords, $event) || (ctx.totalRecords = $event);\n              return i0.ɵɵresetView($event);\n            })(\"PageChange\", function RelatedDocumentsComponent_Template_ngx_pagination_PageChange_41_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.pageIndex, $event) || (ctx.pageIndex = $event);\n              return i0.ɵɵresetView($event);\n            })(\"PageSizeChange\", function RelatedDocumentsComponent_Template_ngx_pagination_PageSizeChange_41_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.pageSize, $event) || (ctx.pageSize = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵlistener(\"PageChange\", function RelatedDocumentsComponent_Template_ngx_pagination_PageChange_41_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.getListPageChange($event));\n            });\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵtemplate(42, RelatedDocumentsComponent_ng_template_42_Template, 9, 3, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor)(44, RelatedDocumentsComponent_ng_template_44_Template, 23, 6, \"ng-template\", null, 1, i0.ɵɵtemplateRefExtractor)(46, RelatedDocumentsComponent_ng_template_46_Template, 36, 12, \"ng-template\", null, 2, i0.ɵɵtemplateRefExtractor);\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(9);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.filterCategoryName);\n            i0.ɵɵadvance(5);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.filterFileName);\n            i0.ɵɵadvance(25);\n            i0.ɵɵproperty(\"ngForOf\", ctx.listBuildCaseFile);\n            i0.ɵɵadvance(2);\n            i0.ɵɵtwoWayProperty(\"CollectionSize\", ctx.totalRecords)(\"Page\", ctx.pageIndex)(\"PageSize\", ctx.pageSize);\n          }\n        },\n        dependencies: [CommonModule, i8.NgClass, i8.NgForOf, i8.NgIf, i8.NgSwitch, i8.NgSwitchCase, SharedModule, i9.DefaultValueAccessor, i9.NgControlStatus, i9.NgModel, i2.NbCardComponent, i2.NbCardBodyComponent, i2.NbCardFooterComponent, i2.NbCardHeaderComponent, i2.NbInputDirective, i2.NbSelectComponent, i2.NbOptionComponent, i2.NbIconComponent, i10.PaginationComponent, i11.BaseLabelDirective, DateFormatHourPipe, cApproveStatusPipe],\n        styles: [\"label[_ngcontent-%COMP%]{min-width:75px;margin:0}\"]\n      });\n    }\n  }\n  return RelatedDocumentsComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}