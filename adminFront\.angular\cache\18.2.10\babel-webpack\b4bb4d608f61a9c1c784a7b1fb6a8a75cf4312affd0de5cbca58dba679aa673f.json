{"ast": null, "code": "/* tslint:disable */\n/* eslint-disable */\nimport { HttpResponse } from '@angular/common/http';\nimport { filter, map } from 'rxjs/operators';\nimport { RequestBuilder } from '../../request-builder';\nexport function apiMaterialExportExcelMaterialListPost$Json(http, rootUrl, params, context) {\n  const rb = new RequestBuilder(rootUrl, apiMaterialExportExcelMaterialListPost$Json.PATH, 'post');\n  if (params) {}\n  return http.request(rb.build({\n    responseType: 'json',\n    accept: 'text/json',\n    context\n  })).pipe(filter(r => r instanceof HttpResponse), map(r => {\n    return r;\n  }));\n}\napiMaterialExportExcelMaterialListPost$Json.PATH = '/api/Material/ExportExcelMaterialList';", "map": {"version": 3, "names": ["HttpResponse", "filter", "map", "RequestBuilder", "apiMaterialExportExcelMaterialListPost$Json", "http", "rootUrl", "params", "context", "rb", "PATH", "request", "build", "responseType", "accept", "pipe", "r"], "sources": ["C:\\Users\\<USER>\\Documents\\jeansalechange\\adminFront\\src\\services\\api\\fn\\material\\api-material-export-excel-material-list-post-json.ts"], "sourcesContent": ["/* tslint:disable */\r\n/* eslint-disable */\r\nimport { HttpClient, HttpContext, HttpResponse } from '@angular/common/http';\r\nimport { Observable } from 'rxjs';\r\nimport { filter, map } from 'rxjs/operators';\r\nimport { StrictHttpResponse } from '../../strict-http-response';\r\nimport { RequestBuilder } from '../../request-builder';\r\n\r\nimport { ExportExcelMaterialsResponseBase } from '../../models/export-excel-materials-response-base';\r\n\r\nexport interface ApiMaterialExportExcelMaterialListPost$Json$Params {\r\n}\r\n\r\nexport function apiMaterialExportExcelMaterialListPost$Json(http: HttpClient, rootUrl: string, params?: ApiMaterialExportExcelMaterialListPost$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<ExportExcelMaterialsResponseBase>> {\r\n  const rb = new RequestBuilder(rootUrl, apiMaterialExportExcelMaterialListPost$Json.PATH, 'post');\r\n  if (params) {\r\n  }\r\n\r\n  return http.request(\r\n    rb.build({ responseType: 'json', accept: 'text/json', context })\r\n  ).pipe(\r\n    filter((r: any): r is HttpResponse<any> => r instanceof HttpResponse),\r\n    map((r: HttpResponse<any>) => {\r\n      return r as StrictHttpResponse<ExportExcelMaterialsResponseBase>;\r\n    })\r\n  );\r\n}\r\n\r\napiMaterialExportExcelMaterialListPost$Json.PATH = '/api/Material/ExportExcelMaterialList';\r\n"], "mappings": "AAAA;AACA;AACA,SAAkCA,YAAY,QAAQ,sBAAsB;AAE5E,SAASC,MAAM,EAAEC,GAAG,QAAQ,gBAAgB;AAE5C,SAASC,cAAc,QAAQ,uBAAuB;AAOtD,OAAM,SAAUC,2CAA2CA,CAACC,IAAgB,EAAEC,OAAe,EAAEC,MAA2D,EAAEC,OAAqB;EAC/K,MAAMC,EAAE,GAAG,IAAIN,cAAc,CAACG,OAAO,EAAEF,2CAA2C,CAACM,IAAI,EAAE,MAAM,CAAC;EAChG,IAAIH,MAAM,EAAE,CACZ;EAEA,OAAOF,IAAI,CAACM,OAAO,CACjBF,EAAE,CAACG,KAAK,CAAC;IAAEC,YAAY,EAAE,MAAM;IAAEC,MAAM,EAAE,WAAW;IAAEN;EAAO,CAAE,CAAC,CACjE,CAACO,IAAI,CACJd,MAAM,CAAEe,CAAM,IAA6BA,CAAC,YAAYhB,YAAY,CAAC,EACrEE,GAAG,CAAEc,CAAoB,IAAI;IAC3B,OAAOA,CAAyD;EAClE,CAAC,CAAC,CACH;AACH;AAEAZ,2CAA2C,CAACM,IAAI,GAAG,uCAAuC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}