{"ast": null, "code": "import { map } from 'rxjs/operators';\nimport { BaseService } from '../base-service';\nimport { apiMaterialExportExcelMaterialListPost$Json } from '../fn/material/api-material-export-excel-material-list-post-json';\nimport { apiMaterialExportExcelMaterialListPost$Plain } from '../fn/material/api-material-export-excel-material-list-post-plain';\nimport { apiMaterialExportExcelMaterialTemplatePost$Json } from '../fn/material/api-material-export-excel-material-template-post-json';\nimport { apiMaterialExportExcelMaterialTemplatePost$Plain } from '../fn/material/api-material-export-excel-material-template-post-plain';\nimport { apiMaterialGetMaterialListPost$Json } from '../fn/material/api-material-get-material-list-post-json';\nimport { apiMaterialGetMaterialListPost$Plain } from '../fn/material/api-material-get-material-list-post-plain';\nimport { apiMaterialImportExcelMaterialListPost$Json } from '../fn/material/api-material-import-excel-material-list-post-json';\nimport { apiMaterialImportExcelMaterialListPost$Plain } from '../fn/material/api-material-import-excel-material-list-post-plain';\nimport { apiMaterialSaveMaterialAdminPost$Json } from '../fn/material/api-material-save-material-admin-post-json';\nimport { apiMaterialSaveMaterialAdminPost$Plain } from '../fn/material/api-material-save-material-admin-post-plain';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../api-configuration\";\nimport * as i2 from \"@angular/common/http\";\nexport class MaterialService extends BaseService {\n  constructor(config, http) {\n    super(config, http);\n  }\n  /** Path part for operation `apiMaterialGetMaterialListPost()` */\n  static {\n    this.ApiMaterialGetMaterialListPostPath = '/api/Material/GetMaterialList';\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiMaterialGetMaterialListPost$Plain()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiMaterialGetMaterialListPost$Plain$Response(params, context) {\n    return apiMaterialGetMaterialListPost$Plain(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiMaterialGetMaterialListPost$Plain$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiMaterialGetMaterialListPost$Plain(params, context) {\n    return this.apiMaterialGetMaterialListPost$Plain$Response(params, context).pipe(map(r => r.body));\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiMaterialGetMaterialListPost$Json()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiMaterialGetMaterialListPost$Json$Response(params, context) {\n    return apiMaterialGetMaterialListPost$Json(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiMaterialGetMaterialListPost$Json$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiMaterialGetMaterialListPost$Json(params, context) {\n    return this.apiMaterialGetMaterialListPost$Json$Response(params, context).pipe(map(r => r.body));\n  }\n  /** Path part for operation `apiMaterialSaveMaterialAdminPost()` */\n  static {\n    this.ApiMaterialSaveMaterialAdminPostPath = '/api/Material/SaveMaterialAdmin';\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiMaterialSaveMaterialAdminPost$Plain()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiMaterialSaveMaterialAdminPost$Plain$Response(params, context) {\n    return apiMaterialSaveMaterialAdminPost$Plain(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiMaterialSaveMaterialAdminPost$Plain$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiMaterialSaveMaterialAdminPost$Plain(params, context) {\n    return this.apiMaterialSaveMaterialAdminPost$Plain$Response(params, context).pipe(map(r => r.body));\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiMaterialSaveMaterialAdminPost$Json()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiMaterialSaveMaterialAdminPost$Json$Response(params, context) {\n    return apiMaterialSaveMaterialAdminPost$Json(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiMaterialSaveMaterialAdminPost$Json$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiMaterialSaveMaterialAdminPost$Json(params, context) {\n    return this.apiMaterialSaveMaterialAdminPost$Json$Response(params, context).pipe(map(r => r.body));\n  }\n  /** Path part for operation `apiMaterialExportExcelMaterialTemplatePost()` */\n  static {\n    this.ApiMaterialExportExcelMaterialTemplatePostPath = '/api/Material/ExportExcelMaterialTemplate';\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiMaterialExportExcelMaterialTemplatePost$Plain()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiMaterialExportExcelMaterialTemplatePost$Plain$Response(params, context) {\n    return apiMaterialExportExcelMaterialTemplatePost$Plain(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiMaterialExportExcelMaterialTemplatePost$Plain$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiMaterialExportExcelMaterialTemplatePost$Plain(params, context) {\n    return this.apiMaterialExportExcelMaterialTemplatePost$Plain$Response(params, context).pipe(map(r => r.body));\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiMaterialExportExcelMaterialTemplatePost$Json()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiMaterialExportExcelMaterialTemplatePost$Json$Response(params, context) {\n    return apiMaterialExportExcelMaterialTemplatePost$Json(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiMaterialExportExcelMaterialTemplatePost$Json$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiMaterialExportExcelMaterialTemplatePost$Json(params, context) {\n    return this.apiMaterialExportExcelMaterialTemplatePost$Json$Response(params, context).pipe(map(r => r.body));\n  }\n  /** Path part for operation `apiMaterialExportExcelMaterialListPost()` */\n  static {\n    this.ApiMaterialExportExcelMaterialListPostPath = '/api/Material/ExportExcelMaterialList';\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiMaterialExportExcelMaterialListPost$Plain()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiMaterialExportExcelMaterialListPost$Plain$Response(params, context) {\n    return apiMaterialExportExcelMaterialListPost$Plain(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiMaterialExportExcelMaterialListPost$Plain$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiMaterialExportExcelMaterialListPost$Plain(params, context) {\n    return this.apiMaterialExportExcelMaterialListPost$Plain$Response(params, context).pipe(map(r => r.body));\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiMaterialExportExcelMaterialListPost$Json()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiMaterialExportExcelMaterialListPost$Json$Response(params, context) {\n    return apiMaterialExportExcelMaterialListPost$Json(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiMaterialExportExcelMaterialListPost$Json$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiMaterialExportExcelMaterialListPost$Json(params, context) {\n    return this.apiMaterialExportExcelMaterialListPost$Json$Response(params, context).pipe(map(r => r.body));\n  }\n  /** Path part for operation `apiMaterialImportExcelMaterialListPost()` */\n  static {\n    this.ApiMaterialImportExcelMaterialListPostPath = '/api/Material/ImportExcelMaterialList';\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiMaterialImportExcelMaterialListPost$Plain()` instead.\n   *\n   * This method sends `multipart/form-data` and handles request body of type `multipart/form-data`.\n   */\n  apiMaterialImportExcelMaterialListPost$Plain$Response(params, context) {\n    return apiMaterialImportExcelMaterialListPost$Plain(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiMaterialImportExcelMaterialListPost$Plain$Response()` instead.\n   *\n   * This method sends `multipart/form-data` and handles request body of type `multipart/form-data`.\n   */\n  apiMaterialImportExcelMaterialListPost$Plain(params, context) {\n    return this.apiMaterialImportExcelMaterialListPost$Plain$Response(params, context).pipe(map(r => r.body));\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiMaterialImportExcelMaterialListPost$Json()` instead.\n   *\n   * This method sends `multipart/form-data` and handles request body of type `multipart/form-data`.\n   */\n  apiMaterialImportExcelMaterialListPost$Json$Response(params, context) {\n    return apiMaterialImportExcelMaterialListPost$Json(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiMaterialImportExcelMaterialListPost$Json$Response()` instead.\n   *\n   * This method sends `multipart/form-data` and handles request body of type `multipart/form-data`.\n   */\n  apiMaterialImportExcelMaterialListPost$Json(params, context) {\n    return this.apiMaterialImportExcelMaterialListPost$Json$Response(params, context).pipe(map(r => r.body));\n  }\n  static {\n    this.ɵfac = function MaterialService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MaterialService)(i0.ɵɵinject(i1.ApiConfiguration), i0.ɵɵinject(i2.HttpClient));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: MaterialService,\n      factory: MaterialService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["map", "BaseService", "apiMaterialExportExcelMaterialListPost$Json", "apiMaterialExportExcelMaterialListPost$Plain", "apiMaterialExportExcelMaterialTemplatePost$Json", "apiMaterialExportExcelMaterialTemplatePost$Plain", "apiMaterialGetMaterialListPost$Json", "apiMaterialGetMaterialListPost$Plain", "apiMaterialImportExcelMaterialListPost$Json", "apiMaterialImportExcelMaterialListPost$Plain", "apiMaterialSaveMaterialAdminPost$Json", "apiMaterialSaveMaterialAdminPost$Plain", "MaterialService", "constructor", "config", "http", "ApiMaterialGetMaterialListPostPath", "apiMaterialGetMaterialListPost$Plain$Response", "params", "context", "rootUrl", "pipe", "r", "body", "apiMaterialGetMaterialListPost$Json$Response", "ApiMaterialSaveMaterialAdminPostPath", "apiMaterialSaveMaterialAdminPost$Plain$Response", "apiMaterialSaveMaterialAdminPost$Json$Response", "ApiMaterialExportExcelMaterialTemplatePostPath", "apiMaterialExportExcelMaterialTemplatePost$Plain$Response", "apiMaterialExportExcelMaterialTemplatePost$Json$Response", "ApiMaterialExportExcelMaterialListPostPath", "apiMaterialExportExcelMaterialListPost$Plain$Response", "apiMaterialExportExcelMaterialListPost$Json$Response", "ApiMaterialImportExcelMaterialListPostPath", "apiMaterialImportExcelMaterialListPost$Plain$Response", "apiMaterialImportExcelMaterialListPost$Json$Response", "i0", "ɵɵinject", "i1", "ApiConfiguration", "i2", "HttpClient", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\Documents\\jeansalechange\\adminFront\\src\\services\\api\\services\\material.service.ts"], "sourcesContent": ["/* tslint:disable */\r\n/* eslint-disable */\r\nimport { HttpClient, HttpContext } from '@angular/common/http';\r\nimport { Injectable } from '@angular/core';\r\nimport { Observable } from 'rxjs';\r\nimport { map } from 'rxjs/operators';\r\n\r\nimport { BaseService } from '../base-service';\r\nimport { ApiConfiguration } from '../api-configuration';\r\nimport { StrictHttpResponse } from '../strict-http-response';\r\n\r\nimport { apiMaterialExportExcelMaterialListPost$Json } from '../fn/material/api-material-export-excel-material-list-post-json';\r\nimport { ApiMaterialExportExcelMaterialListPost$Json$Params } from '../fn/material/api-material-export-excel-material-list-post-json';\r\nimport { apiMaterialExportExcelMaterialListPost$Plain } from '../fn/material/api-material-export-excel-material-list-post-plain';\r\nimport { ApiMaterialExportExcelMaterialListPost$Plain$Params } from '../fn/material/api-material-export-excel-material-list-post-plain';\r\nimport { apiMaterialExportExcelMaterialTemplatePost$Json } from '../fn/material/api-material-export-excel-material-template-post-json';\r\nimport { ApiMaterialExportExcelMaterialTemplatePost$Json$Params } from '../fn/material/api-material-export-excel-material-template-post-json';\r\nimport { apiMaterialExportExcelMaterialTemplatePost$Plain } from '../fn/material/api-material-export-excel-material-template-post-plain';\r\nimport { ApiMaterialExportExcelMaterialTemplatePost$Plain$Params } from '../fn/material/api-material-export-excel-material-template-post-plain';\r\nimport { apiMaterialGetMaterialListPost$Json } from '../fn/material/api-material-get-material-list-post-json';\r\nimport { ApiMaterialGetMaterialListPost$Json$Params } from '../fn/material/api-material-get-material-list-post-json';\r\nimport { apiMaterialGetMaterialListPost$Plain } from '../fn/material/api-material-get-material-list-post-plain';\r\nimport { ApiMaterialGetMaterialListPost$Plain$Params } from '../fn/material/api-material-get-material-list-post-plain';\r\nimport { apiMaterialImportExcelMaterialListPost$Json } from '../fn/material/api-material-import-excel-material-list-post-json';\r\nimport { ApiMaterialImportExcelMaterialListPost$Json$Params } from '../fn/material/api-material-import-excel-material-list-post-json';\r\nimport { apiMaterialImportExcelMaterialListPost$Plain } from '../fn/material/api-material-import-excel-material-list-post-plain';\r\nimport { ApiMaterialImportExcelMaterialListPost$Plain$Params } from '../fn/material/api-material-import-excel-material-list-post-plain';\r\nimport { apiMaterialSaveMaterialAdminPost$Json } from '../fn/material/api-material-save-material-admin-post-json';\r\nimport { ApiMaterialSaveMaterialAdminPost$Json$Params } from '../fn/material/api-material-save-material-admin-post-json';\r\nimport { apiMaterialSaveMaterialAdminPost$Plain } from '../fn/material/api-material-save-material-admin-post-plain';\r\nimport { ApiMaterialSaveMaterialAdminPost$Plain$Params } from '../fn/material/api-material-save-material-admin-post-plain';\r\nimport { ExportExcelMaterialsResponseBase } from '../models/export-excel-materials-response-base';\r\nimport { GetMaterialListResponseListResponseBase } from '../models/get-material-list-response-list-response-base';\r\nimport { StringResponseBase } from '../models/string-response-base';\r\n\r\n@Injectable({ providedIn: 'root' })\r\nexport class MaterialService extends BaseService {\r\n  constructor(config: ApiConfiguration, http: HttpClient) {\r\n    super(config, http);\r\n  }\r\n\r\n  /** Path part for operation `apiMaterialGetMaterialListPost()` */\r\n  static readonly ApiMaterialGetMaterialListPostPath = '/api/Material/GetMaterialList';\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiMaterialGetMaterialListPost$Plain()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiMaterialGetMaterialListPost$Plain$Response(params?: ApiMaterialGetMaterialListPost$Plain$Params, context?: HttpContext): Observable<StrictHttpResponse<GetMaterialListResponseListResponseBase>> {\r\n    return apiMaterialGetMaterialListPost$Plain(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiMaterialGetMaterialListPost$Plain$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiMaterialGetMaterialListPost$Plain(params?: ApiMaterialGetMaterialListPost$Plain$Params, context?: HttpContext): Observable<GetMaterialListResponseListResponseBase> {\r\n    return this.apiMaterialGetMaterialListPost$Plain$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<GetMaterialListResponseListResponseBase>): GetMaterialListResponseListResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiMaterialGetMaterialListPost$Json()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiMaterialGetMaterialListPost$Json$Response(params?: ApiMaterialGetMaterialListPost$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<GetMaterialListResponseListResponseBase>> {\r\n    return apiMaterialGetMaterialListPost$Json(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiMaterialGetMaterialListPost$Json$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiMaterialGetMaterialListPost$Json(params?: ApiMaterialGetMaterialListPost$Json$Params, context?: HttpContext): Observable<GetMaterialListResponseListResponseBase> {\r\n    return this.apiMaterialGetMaterialListPost$Json$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<GetMaterialListResponseListResponseBase>): GetMaterialListResponseListResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /** Path part for operation `apiMaterialSaveMaterialAdminPost()` */\r\n  static readonly ApiMaterialSaveMaterialAdminPostPath = '/api/Material/SaveMaterialAdmin';\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiMaterialSaveMaterialAdminPost$Plain()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiMaterialSaveMaterialAdminPost$Plain$Response(params?: ApiMaterialSaveMaterialAdminPost$Plain$Params, context?: HttpContext): Observable<StrictHttpResponse<StringResponseBase>> {\r\n    return apiMaterialSaveMaterialAdminPost$Plain(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiMaterialSaveMaterialAdminPost$Plain$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiMaterialSaveMaterialAdminPost$Plain(params?: ApiMaterialSaveMaterialAdminPost$Plain$Params, context?: HttpContext): Observable<StringResponseBase> {\r\n    return this.apiMaterialSaveMaterialAdminPost$Plain$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<StringResponseBase>): StringResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiMaterialSaveMaterialAdminPost$Json()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiMaterialSaveMaterialAdminPost$Json$Response(params?: ApiMaterialSaveMaterialAdminPost$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<StringResponseBase>> {\r\n    return apiMaterialSaveMaterialAdminPost$Json(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiMaterialSaveMaterialAdminPost$Json$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiMaterialSaveMaterialAdminPost$Json(params?: ApiMaterialSaveMaterialAdminPost$Json$Params, context?: HttpContext): Observable<StringResponseBase> {\r\n    return this.apiMaterialSaveMaterialAdminPost$Json$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<StringResponseBase>): StringResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /** Path part for operation `apiMaterialExportExcelMaterialTemplatePost()` */\r\n  static readonly ApiMaterialExportExcelMaterialTemplatePostPath = '/api/Material/ExportExcelMaterialTemplate';\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiMaterialExportExcelMaterialTemplatePost$Plain()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiMaterialExportExcelMaterialTemplatePost$Plain$Response(params?: ApiMaterialExportExcelMaterialTemplatePost$Plain$Params, context?: HttpContext): Observable<StrictHttpResponse<ExportExcelMaterialsResponseBase>> {\r\n    return apiMaterialExportExcelMaterialTemplatePost$Plain(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiMaterialExportExcelMaterialTemplatePost$Plain$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiMaterialExportExcelMaterialTemplatePost$Plain(params?: ApiMaterialExportExcelMaterialTemplatePost$Plain$Params, context?: HttpContext): Observable<ExportExcelMaterialsResponseBase> {\r\n    return this.apiMaterialExportExcelMaterialTemplatePost$Plain$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<ExportExcelMaterialsResponseBase>): ExportExcelMaterialsResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiMaterialExportExcelMaterialTemplatePost$Json()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiMaterialExportExcelMaterialTemplatePost$Json$Response(params?: ApiMaterialExportExcelMaterialTemplatePost$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<ExportExcelMaterialsResponseBase>> {\r\n    return apiMaterialExportExcelMaterialTemplatePost$Json(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiMaterialExportExcelMaterialTemplatePost$Json$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiMaterialExportExcelMaterialTemplatePost$Json(params?: ApiMaterialExportExcelMaterialTemplatePost$Json$Params, context?: HttpContext): Observable<ExportExcelMaterialsResponseBase> {\r\n    return this.apiMaterialExportExcelMaterialTemplatePost$Json$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<ExportExcelMaterialsResponseBase>): ExportExcelMaterialsResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /** Path part for operation `apiMaterialExportExcelMaterialListPost()` */\r\n  static readonly ApiMaterialExportExcelMaterialListPostPath = '/api/Material/ExportExcelMaterialList';\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiMaterialExportExcelMaterialListPost$Plain()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiMaterialExportExcelMaterialListPost$Plain$Response(params?: ApiMaterialExportExcelMaterialListPost$Plain$Params, context?: HttpContext): Observable<StrictHttpResponse<ExportExcelMaterialsResponseBase>> {\r\n    return apiMaterialExportExcelMaterialListPost$Plain(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiMaterialExportExcelMaterialListPost$Plain$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiMaterialExportExcelMaterialListPost$Plain(params?: ApiMaterialExportExcelMaterialListPost$Plain$Params, context?: HttpContext): Observable<ExportExcelMaterialsResponseBase> {\r\n    return this.apiMaterialExportExcelMaterialListPost$Plain$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<ExportExcelMaterialsResponseBase>): ExportExcelMaterialsResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiMaterialExportExcelMaterialListPost$Json()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiMaterialExportExcelMaterialListPost$Json$Response(params?: ApiMaterialExportExcelMaterialListPost$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<ExportExcelMaterialsResponseBase>> {\r\n    return apiMaterialExportExcelMaterialListPost$Json(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiMaterialExportExcelMaterialListPost$Json$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiMaterialExportExcelMaterialListPost$Json(params?: ApiMaterialExportExcelMaterialListPost$Json$Params, context?: HttpContext): Observable<ExportExcelMaterialsResponseBase> {\r\n    return this.apiMaterialExportExcelMaterialListPost$Json$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<ExportExcelMaterialsResponseBase>): ExportExcelMaterialsResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /** Path part for operation `apiMaterialImportExcelMaterialListPost()` */\r\n  static readonly ApiMaterialImportExcelMaterialListPostPath = '/api/Material/ImportExcelMaterialList';\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiMaterialImportExcelMaterialListPost$Plain()` instead.\r\n   *\r\n   * This method sends `multipart/form-data` and handles request body of type `multipart/form-data`.\r\n   */\r\n  apiMaterialImportExcelMaterialListPost$Plain$Response(params?: ApiMaterialImportExcelMaterialListPost$Plain$Params, context?: HttpContext): Observable<StrictHttpResponse<StringResponseBase>> {\r\n    return apiMaterialImportExcelMaterialListPost$Plain(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiMaterialImportExcelMaterialListPost$Plain$Response()` instead.\r\n   *\r\n   * This method sends `multipart/form-data` and handles request body of type `multipart/form-data`.\r\n   */\r\n  apiMaterialImportExcelMaterialListPost$Plain(params?: ApiMaterialImportExcelMaterialListPost$Plain$Params, context?: HttpContext): Observable<StringResponseBase> {\r\n    return this.apiMaterialImportExcelMaterialListPost$Plain$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<StringResponseBase>): StringResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiMaterialImportExcelMaterialListPost$Json()` instead.\r\n   *\r\n   * This method sends `multipart/form-data` and handles request body of type `multipart/form-data`.\r\n   */\r\n  apiMaterialImportExcelMaterialListPost$Json$Response(params?: ApiMaterialImportExcelMaterialListPost$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<StringResponseBase>> {\r\n    return apiMaterialImportExcelMaterialListPost$Json(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiMaterialImportExcelMaterialListPost$Json$Response()` instead.\r\n   *\r\n   * This method sends `multipart/form-data` and handles request body of type `multipart/form-data`.\r\n   */\r\n  apiMaterialImportExcelMaterialListPost$Json(params?: ApiMaterialImportExcelMaterialListPost$Json$Params, context?: HttpContext): Observable<StringResponseBase> {\r\n    return this.apiMaterialImportExcelMaterialListPost$Json$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<StringResponseBase>): StringResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n}\r\n"], "mappings": "AAKA,SAASA,GAAG,QAAQ,gBAAgB;AAEpC,SAASC,WAAW,QAAQ,iBAAiB;AAI7C,SAASC,2CAA2C,QAAQ,kEAAkE;AAE9H,SAASC,4CAA4C,QAAQ,mEAAmE;AAEhI,SAASC,+CAA+C,QAAQ,sEAAsE;AAEtI,SAASC,gDAAgD,QAAQ,uEAAuE;AAExI,SAASC,mCAAmC,QAAQ,yDAAyD;AAE7G,SAASC,oCAAoC,QAAQ,0DAA0D;AAE/G,SAASC,2CAA2C,QAAQ,kEAAkE;AAE9H,SAASC,4CAA4C,QAAQ,mEAAmE;AAEhI,SAASC,qCAAqC,QAAQ,2DAA2D;AAEjH,SAASC,sCAAsC,QAAQ,4DAA4D;;;;AAOnH,OAAM,MAAOC,eAAgB,SAAQX,WAAW;EAC9CY,YAAYC,MAAwB,EAAEC,IAAgB;IACpD,KAAK,CAACD,MAAM,EAAEC,IAAI,CAAC;EACrB;EAEA;;IACgB,KAAAC,kCAAkC,GAAG,+BAA+B;EAAC;EAErF;;;;;;EAMAC,6CAA6CA,CAACC,MAAoD,EAAEC,OAAqB;IACvH,OAAOZ,oCAAoC,CAAC,IAAI,CAACQ,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EACvF;EAEA;;;;;;EAMAZ,oCAAoCA,CAACW,MAAoD,EAAEC,OAAqB;IAC9G,OAAO,IAAI,CAACF,6CAA6C,CAACC,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CAC7ErB,GAAG,CAAEsB,CAA8D,IAA8CA,CAAC,CAACC,IAAI,CAAC,CACzH;EACH;EAEA;;;;;;EAMAC,4CAA4CA,CAACN,MAAmD,EAAEC,OAAqB;IACrH,OAAOb,mCAAmC,CAAC,IAAI,CAACS,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EACtF;EAEA;;;;;;EAMAb,mCAAmCA,CAACY,MAAmD,EAAEC,OAAqB;IAC5G,OAAO,IAAI,CAACK,4CAA4C,CAACN,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CAC5ErB,GAAG,CAAEsB,CAA8D,IAA8CA,CAAC,CAACC,IAAI,CAAC,CACzH;EACH;EAEA;;IACgB,KAAAE,oCAAoC,GAAG,iCAAiC;EAAC;EAEzF;;;;;;EAMAC,+CAA+CA,CAACR,MAAsD,EAAEC,OAAqB;IAC3H,OAAOR,sCAAsC,CAAC,IAAI,CAACI,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EACzF;EAEA;;;;;;EAMAR,sCAAsCA,CAACO,MAAsD,EAAEC,OAAqB;IAClH,OAAO,IAAI,CAACO,+CAA+C,CAACR,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CAC/ErB,GAAG,CAAEsB,CAAyC,IAAyBA,CAAC,CAACC,IAAI,CAAC,CAC/E;EACH;EAEA;;;;;;EAMAI,8CAA8CA,CAACT,MAAqD,EAAEC,OAAqB;IACzH,OAAOT,qCAAqC,CAAC,IAAI,CAACK,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EACxF;EAEA;;;;;;EAMAT,qCAAqCA,CAACQ,MAAqD,EAAEC,OAAqB;IAChH,OAAO,IAAI,CAACQ,8CAA8C,CAACT,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CAC9ErB,GAAG,CAAEsB,CAAyC,IAAyBA,CAAC,CAACC,IAAI,CAAC,CAC/E;EACH;EAEA;;IACgB,KAAAK,8CAA8C,GAAG,2CAA2C;EAAC;EAE7G;;;;;;EAMAC,yDAAyDA,CAACX,MAAgE,EAAEC,OAAqB;IAC/I,OAAOd,gDAAgD,CAAC,IAAI,CAACU,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EACnG;EAEA;;;;;;EAMAd,gDAAgDA,CAACa,MAAgE,EAAEC,OAAqB;IACtI,OAAO,IAAI,CAACU,yDAAyD,CAACX,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CACzFrB,GAAG,CAAEsB,CAAuD,IAAuCA,CAAC,CAACC,IAAI,CAAC,CAC3G;EACH;EAEA;;;;;;EAMAO,wDAAwDA,CAACZ,MAA+D,EAAEC,OAAqB;IAC7I,OAAOf,+CAA+C,CAAC,IAAI,CAACW,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EAClG;EAEA;;;;;;EAMAf,+CAA+CA,CAACc,MAA+D,EAAEC,OAAqB;IACpI,OAAO,IAAI,CAACW,wDAAwD,CAACZ,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CACxFrB,GAAG,CAAEsB,CAAuD,IAAuCA,CAAC,CAACC,IAAI,CAAC,CAC3G;EACH;EAEA;;IACgB,KAAAQ,0CAA0C,GAAG,uCAAuC;EAAC;EAErG;;;;;;EAMAC,qDAAqDA,CAACd,MAA4D,EAAEC,OAAqB;IACvI,OAAOhB,4CAA4C,CAAC,IAAI,CAACY,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EAC/F;EAEA;;;;;;EAMAhB,4CAA4CA,CAACe,MAA4D,EAAEC,OAAqB;IAC9H,OAAO,IAAI,CAACa,qDAAqD,CAACd,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CACrFrB,GAAG,CAAEsB,CAAuD,IAAuCA,CAAC,CAACC,IAAI,CAAC,CAC3G;EACH;EAEA;;;;;;EAMAU,oDAAoDA,CAACf,MAA2D,EAAEC,OAAqB;IACrI,OAAOjB,2CAA2C,CAAC,IAAI,CAACa,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EAC9F;EAEA;;;;;;EAMAjB,2CAA2CA,CAACgB,MAA2D,EAAEC,OAAqB;IAC5H,OAAO,IAAI,CAACc,oDAAoD,CAACf,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CACpFrB,GAAG,CAAEsB,CAAuD,IAAuCA,CAAC,CAACC,IAAI,CAAC,CAC3G;EACH;EAEA;;IACgB,KAAAW,0CAA0C,GAAG,uCAAuC;EAAC;EAErG;;;;;;EAMAC,qDAAqDA,CAACjB,MAA4D,EAAEC,OAAqB;IACvI,OAAOV,4CAA4C,CAAC,IAAI,CAACM,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EAC/F;EAEA;;;;;;EAMAV,4CAA4CA,CAACS,MAA4D,EAAEC,OAAqB;IAC9H,OAAO,IAAI,CAACgB,qDAAqD,CAACjB,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CACrFrB,GAAG,CAAEsB,CAAyC,IAAyBA,CAAC,CAACC,IAAI,CAAC,CAC/E;EACH;EAEA;;;;;;EAMAa,oDAAoDA,CAAClB,MAA2D,EAAEC,OAAqB;IACrI,OAAOX,2CAA2C,CAAC,IAAI,CAACO,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EAC9F;EAEA;;;;;;EAMAX,2CAA2CA,CAACU,MAA2D,EAAEC,OAAqB;IAC5H,OAAO,IAAI,CAACiB,oDAAoD,CAAClB,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CACpFrB,GAAG,CAAEsB,CAAyC,IAAyBA,CAAC,CAACC,IAAI,CAAC,CAC/E;EACH;;;uCA9OWX,eAAe,EAAAyB,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,gBAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,UAAA;IAAA;EAAA;;;aAAf9B,eAAe;MAAA+B,OAAA,EAAf/B,eAAe,CAAAgC,IAAA;MAAAC,UAAA,EADF;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}