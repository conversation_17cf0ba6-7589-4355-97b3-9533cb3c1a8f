{"ast": null, "code": "import { LogsManagementComponent } from './logs-management/logs-management.component';\nimport { UserManagementComponent } from './user-management/user-management.component';\nimport { RouterModule } from '@angular/router';\nimport { RolePermissionsComponent } from './role-permissions/role-permissions.component';\nimport { ApproveWaitingComponent } from '../approve-waiting/approve-waiting.component';\nimport { DetailApprovalWaitingComponent } from '../approve-waiting/detail-approval-waiting/detail-approval-waiting.component';\nimport { CategoryManagementComponent } from '../category-management/category-management.component';\nimport { ReviewDocumentManagementComponent } from '../construction-project-management/notice-management/review-document-management/review-document-management.component';\nimport { NotificationSettingComponent } from './notification-setting/notification-setting.component';\nimport { ProjectManagementComponent } from '../construction-project-management/project-management/project-management.component';\nimport { SettingTimePeriodComponent } from '../selection-management/setting-time-period/setting-time-period.component';\nimport { EditSettingTimePeriodComponent } from '../selection-management/setting-time-period/edit-setting-time-period/edit-setting-time-period.component';\nimport { ContentManagementLandownerComponent } from '../selection-management/content-management-landowner/content-management-landowner.component';\nimport { DetailContentManagementLandownerComponent } from '../selection-management/content-management-landowner/detail-content-management-landowner/detail-content-management-landowner.component';\nimport { ContentManagementSalesAccountComponent } from '../selection-management/content-management-sales-account/content-management-sales-account.component';\nimport { DetailContentManagementSalesAccountComponent } from '../selection-management/content-management-sales-account/detail-content-management-sales-account/detail-content-management-sales-account.component';\nimport { SchematicPictureComponent } from '../selection-management/schematic-picture/schematic-picture.component';\nimport { PictureMaterialComponent } from '../selection-management/picture-material/picture-material.component';\nimport { AvailableTimeSlotComponent } from '../reservation-time-management/available-time-slot/available-time-slot.component';\nimport { EditAvailableTimeSlotComponent } from '../reservation-time-management/available-time-slot/edit-available-time-slot/edit-available-time-slot.component';\nimport { CalendarComponent } from '../reservation-time-management/calendar/calendar.component';\nimport { PreOrderComponent } from '../reservation-time-management/pre-order/pre-order.component';\nimport { RelatedDocumentsComponent } from '../construction-project-management/related-documents/related-documents.component';\nimport { NoticeManagementComponent } from '../construction-project-management/notice-management/notice-management.component';\n// import { ReviewDocumentComponent } from '../construction-project-management/notice-management/review-document/review-document.component';\nimport { RegularNoticeComponent } from '../construction-project-management/notice-management/regular-notice/regular-notice.component';\nimport { RequirementManagementComponent } from '../requirement-management/requirement-management.component';\nimport { ApproveWaiting1Component } from '../approve-waiting-specialchange/approve-waiting-specialchange.component';\nimport { ApproveWaiting2Component } from '../approve-waiting-finaldoc/approve-waiting-finaldoc.component';\nimport { ApproveWaiting3Component } from '../approve-waiting-buildcasefile/approve-waiting-buildcasefile.component';\nimport { ApproveWaiting4Component } from '../approve-waiting-specialnoticefile/approve-waiting-specialnoticefile.component';\nimport { ApproveWaiting5Component } from '../approve-waiting-review/approve-waiting-review.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [\n//system module\n{\n  path: 'user-management',\n  component: UserManagementComponent\n}, {\n  path: 'notification-setting',\n  component: NotificationSettingComponent\n}, {\n  path: 'role-permissions',\n  component: RolePermissionsComponent\n}, {\n  path: 'logs-management',\n  component: LogsManagementComponent\n},\n//construction project module\n{\n  path: 'project-management',\n  component: ProjectManagementComponent\n}, {\n  path: 'review-document',\n  component: ReviewDocumentManagementComponent\n}, {\n  path: 'related-documents/:id',\n  component: RelatedDocumentsComponent\n}, {\n  path: 'notice-management',\n  component: NoticeManagementComponent\n}, {\n  path: 'notice-management/regular-notice',\n  component: RegularNoticeComponent\n}, {\n  path: 'requirement-management',\n  component: RequirementManagementComponent\n},\n//household module\n{\n  path: \"household-management\",\n  loadChildren: () => import('../household-management/household-management.module').then(mod => mod.HouseholdManagementModule)\n},\n//selection management\n{\n  path: 'setting-time-period',\n  component: SettingTimePeriodComponent\n}, {\n  path: 'setting-time-period/:id',\n  component: EditSettingTimePeriodComponent\n}, {\n  path: 'content-management-landowner',\n  component: ContentManagementLandownerComponent\n}, {\n  path: 'content-management-landowner/:id',\n  component: DetailContentManagementLandownerComponent\n}, {\n  path: 'content-management-sales-account',\n  component: ContentManagementSalesAccountComponent\n}, {\n  path: 'content-management-sales-account/:id',\n  component: DetailContentManagementSalesAccountComponent\n}, {\n  path: 'schematic-picture',\n  component: SchematicPictureComponent\n}, {\n  path: 'picture-material',\n  component: PictureMaterialComponent\n},\n//reservation management\n{\n  path: 'available-time-slot',\n  component: AvailableTimeSlotComponent\n}, {\n  path: 'available-time-slot/:id',\n  component: EditAvailableTimeSlotComponent\n}, {\n  path: 'approve-waiting',\n  component: ApproveWaitingComponent\n}, {\n  path: 'approve-waiting/:buildCaseId/:id',\n  component: DetailApprovalWaitingComponent\n}, {\n  path: 'calendar',\n  component: CalendarComponent\n}, {\n  path: 'preOder',\n  component: PreOrderComponent\n}, {\n  path: 'category-management',\n  component: CategoryManagementComponent\n}, {\n  path: 'approve-waiting-specialchange',\n  component: ApproveWaiting1Component\n}, {\n  path: 'approve-waiting-finaldoc',\n  component: ApproveWaiting2Component\n}, {\n  path: 'approve-waiting-buildcasefile',\n  component: ApproveWaiting3Component\n}, {\n  path: 'approve-waiting-specialnoticefile',\n  component: ApproveWaiting4Component\n}, {\n  path: 'approve-waiting-review',\n  component: ApproveWaiting5Component\n}];\nexport class SystemManagementRoutingModule {\n  static {\n    this.ɵfac = function SystemManagementRoutingModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || SystemManagementRoutingModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: SystemManagementRoutingModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [RouterModule.forChild(routes), RouterModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(SystemManagementRoutingModule, {\n    imports: [i1.RouterModule],\n    exports: [RouterModule]\n  });\n})();", "map": {"version": 3, "names": ["LogsManagementComponent", "UserManagementComponent", "RouterModule", "RolePermissionsComponent", "ApproveWaitingComponent", "DetailApprovalWaitingComponent", "CategoryManagementComponent", "ReviewDocumentManagementComponent", "NotificationSettingComponent", "ProjectManagementComponent", "SettingTimePeriodComponent", "EditSettingTimePeriodComponent", "ContentManagementLandownerComponent", "DetailContentManagementLandownerComponent", "ContentManagementSalesAccountComponent", "DetailContentManagementSalesAccountComponent", "SchematicPictureComponent", "PictureMaterialComponent", "AvailableTimeSlotComponent", "EditAvailableTimeSlotComponent", "CalendarComponent", "PreOrderComponent", "RelatedDocumentsComponent", "NoticeManagementComponent", "RegularNoticeComponent", "RequirementManagementComponent", "ApproveWaiting1Component", "ApproveWaiting2Component", "ApproveWaiting3Component", "ApproveWaiting4Component", "ApproveWaiting5Component", "routes", "path", "component", "loadChildren", "then", "mod", "HouseholdManagementModule", "SystemManagementRoutingModule", "<PERSON><PERSON><PERSON><PERSON>", "imports", "i1", "exports"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\system-management\\system-management-routing.module.ts"], "sourcesContent": ["import { LogsManagementComponent } from './logs-management/logs-management.component';\r\nimport { UserManagementComponent } from './user-management/user-management.component';\r\nimport { NgModule } from '@angular/core';\r\nimport { Routes, RouterModule } from '@angular/router';\r\nimport { RolePermissionsComponent } from './role-permissions/role-permissions.component';\r\nimport { ApproveWaitingComponent } from '../approve-waiting/approve-waiting.component';\r\nimport { DetailApprovalWaitingComponent } from '../approve-waiting/detail-approval-waiting/detail-approval-waiting.component';\r\nimport { CategoryManagementComponent } from '../category-management/category-management.component';\r\nimport { ReviewDocumentManagementComponent } from '../construction-project-management/notice-management/review-document-management/review-document-management.component';\r\nimport { NotificationSettingComponent } from './notification-setting/notification-setting.component';\r\nimport { ProjectManagementComponent } from '../construction-project-management/project-management/project-management.component';\r\nimport { SettingTimePeriodComponent } from '../selection-management/setting-time-period/setting-time-period.component';\r\nimport { EditSettingTimePeriodComponent } from '../selection-management/setting-time-period/edit-setting-time-period/edit-setting-time-period.component';\r\nimport { ContentManagementLandownerComponent } from '../selection-management/content-management-landowner/content-management-landowner.component';\r\nimport { DetailContentManagementLandownerComponent } from '../selection-management/content-management-landowner/detail-content-management-landowner/detail-content-management-landowner.component';\r\nimport { ContentManagementSalesAccountComponent } from '../selection-management/content-management-sales-account/content-management-sales-account.component';\r\nimport { DetailContentManagementSalesAccountComponent } from '../selection-management/content-management-sales-account/detail-content-management-sales-account/detail-content-management-sales-account.component';\r\nimport { SchematicPictureComponent } from '../selection-management/schematic-picture/schematic-picture.component';\r\nimport { PictureMaterialComponent } from '../selection-management/picture-material/picture-material.component';\r\nimport { AvailableTimeSlotComponent } from '../reservation-time-management/available-time-slot/available-time-slot.component';\r\nimport { EditAvailableTimeSlotComponent } from '../reservation-time-management/available-time-slot/edit-available-time-slot/edit-available-time-slot.component';\r\nimport { CalendarComponent } from '../reservation-time-management/calendar/calendar.component';\r\nimport { PreOrderComponent } from '../reservation-time-management/pre-order/pre-order.component';\r\nimport { RelatedDocumentsComponent } from '../construction-project-management/related-documents/related-documents.component';\r\nimport { NoticeManagementComponent } from '../construction-project-management/notice-management/notice-management.component';\r\n// import { ReviewDocumentComponent } from '../construction-project-management/notice-management/review-document/review-document.component';\r\nimport { RegularNoticeComponent } from '../construction-project-management/notice-management/regular-notice/regular-notice.component';\r\nimport { RequirementManagementComponent } from '../requirement-management/requirement-management.component';\r\nimport { ApproveWaiting1Component } from '../approve-waiting-specialchange/approve-waiting-specialchange.component';\r\nimport { ApproveWaiting2Component } from '../approve-waiting-finaldoc/approve-waiting-finaldoc.component';\r\nimport { ApproveWaiting3Component } from '../approve-waiting-buildcasefile/approve-waiting-buildcasefile.component';\r\nimport { ApproveWaiting4Component } from '../approve-waiting-specialnoticefile/approve-waiting-specialnoticefile.component';\r\nimport { ApproveWaiting5Component } from '../approve-waiting-review/approve-waiting-review.component';\r\n\r\n\r\nconst routes: Routes = [\r\n  //system module\r\n  { path: 'user-management', component: UserManagementComponent },\r\n  { path: 'notification-setting', component: NotificationSettingComponent },\r\n  { path: 'role-permissions', component: RolePermissionsComponent },\r\n  { path: 'logs-management', component: LogsManagementComponent },\r\n\r\n  //construction project module\r\n  { path: 'project-management', component: ProjectManagementComponent },\r\n  { path: 'review-document', component: ReviewDocumentManagementComponent },\r\n  { path: 'related-documents/:id', component: RelatedDocumentsComponent },\r\n  { path: 'notice-management', component: NoticeManagementComponent },\r\n  { path: 'notice-management/regular-notice', component: RegularNoticeComponent },\r\n  { path: 'requirement-management', component: RequirementManagementComponent },\r\n\r\n  //household module\r\n  {\r\n    path: \"household-management\",\r\n    loadChildren: () => import('../household-management/household-management.module')\r\n      .then(mod => mod.HouseholdManagementModule),\r\n  },\r\n\r\n  //selection management\r\n  { path: 'setting-time-period', component: SettingTimePeriodComponent },\r\n  { path: 'setting-time-period/:id', component: EditSettingTimePeriodComponent },\r\n  { path: 'content-management-landowner', component: ContentManagementLandownerComponent },\r\n  { path: 'content-management-landowner/:id', component: DetailContentManagementLandownerComponent },\r\n  { path: 'content-management-sales-account', component: ContentManagementSalesAccountComponent },\r\n  { path: 'content-management-sales-account/:id', component: DetailContentManagementSalesAccountComponent },\r\n  { path: 'schematic-picture', component: SchematicPictureComponent },\r\n  { path: 'picture-material', component: PictureMaterialComponent },\r\n\r\n  //reservation management\r\n  { path: 'available-time-slot', component: AvailableTimeSlotComponent },\r\n  { path: 'available-time-slot/:id', component: EditAvailableTimeSlotComponent },\r\n  { path: 'approve-waiting', component: ApproveWaitingComponent },\r\n  { path: 'approve-waiting/:buildCaseId/:id', component: DetailApprovalWaitingComponent },\r\n  { path: 'calendar', component: CalendarComponent },\r\n  { path: 'preOder', component: PreOrderComponent },\r\n  { path: 'category-management', component: CategoryManagementComponent },\r\n  { path: 'approve-waiting-specialchange', component: ApproveWaiting1Component },\r\n  { path: 'approve-waiting-finaldoc', component: ApproveWaiting2Component },\r\n  { path: 'approve-waiting-buildcasefile', component: ApproveWaiting3Component },\r\n  { path: 'approve-waiting-specialnoticefile', component: ApproveWaiting4Component },\r\n  { path: 'approve-waiting-review', component: ApproveWaiting5Component },\r\n];\r\n\r\n@NgModule({\r\n  imports: [RouterModule.forChild(routes)],\r\n  exports: [RouterModule],\r\n})\r\nexport class SystemManagementRoutingModule { }\r\n"], "mappings": "AAAA,SAASA,uBAAuB,QAAQ,6CAA6C;AACrF,SAASC,uBAAuB,QAAQ,6CAA6C;AAErF,SAAiBC,YAAY,QAAQ,iBAAiB;AACtD,SAASC,wBAAwB,QAAQ,+CAA+C;AACxF,SAASC,uBAAuB,QAAQ,8CAA8C;AACtF,SAASC,8BAA8B,QAAQ,8EAA8E;AAC7H,SAASC,2BAA2B,QAAQ,sDAAsD;AAClG,SAASC,iCAAiC,QAAQ,sHAAsH;AACxK,SAASC,4BAA4B,QAAQ,uDAAuD;AACpG,SAASC,0BAA0B,QAAQ,oFAAoF;AAC/H,SAASC,0BAA0B,QAAQ,2EAA2E;AACtH,SAASC,8BAA8B,QAAQ,yGAAyG;AACxJ,SAASC,mCAAmC,QAAQ,6FAA6F;AACjJ,SAASC,yCAAyC,QAAQ,wIAAwI;AAClM,SAASC,sCAAsC,QAAQ,qGAAqG;AAC5J,SAASC,4CAA4C,QAAQ,oJAAoJ;AACjN,SAASC,yBAAyB,QAAQ,uEAAuE;AACjH,SAASC,wBAAwB,QAAQ,qEAAqE;AAC9G,SAASC,0BAA0B,QAAQ,kFAAkF;AAC7H,SAASC,8BAA8B,QAAQ,gHAAgH;AAC/J,SAASC,iBAAiB,QAAQ,4DAA4D;AAC9F,SAASC,iBAAiB,QAAQ,8DAA8D;AAChG,SAASC,yBAAyB,QAAQ,kFAAkF;AAC5H,SAASC,yBAAyB,QAAQ,kFAAkF;AAC5H;AACA,SAASC,sBAAsB,QAAQ,8FAA8F;AACrI,SAASC,8BAA8B,QAAQ,4DAA4D;AAC3G,SAASC,wBAAwB,QAAQ,0EAA0E;AACnH,SAASC,wBAAwB,QAAQ,gEAAgE;AACzG,SAASC,wBAAwB,QAAQ,0EAA0E;AACnH,SAASC,wBAAwB,QAAQ,kFAAkF;AAC3H,SAASC,wBAAwB,QAAQ,4DAA4D;;;AAGrG,MAAMC,MAAM,GAAW;AACrB;AACA;EAAEC,IAAI,EAAE,iBAAiB;EAAEC,SAAS,EAAEhC;AAAuB,CAAE,EAC/D;EAAE+B,IAAI,EAAE,sBAAsB;EAAEC,SAAS,EAAEzB;AAA4B,CAAE,EACzE;EAAEwB,IAAI,EAAE,kBAAkB;EAAEC,SAAS,EAAE9B;AAAwB,CAAE,EACjE;EAAE6B,IAAI,EAAE,iBAAiB;EAAEC,SAAS,EAAEjC;AAAuB,CAAE;AAE/D;AACA;EAAEgC,IAAI,EAAE,oBAAoB;EAAEC,SAAS,EAAExB;AAA0B,CAAE,EACrE;EAAEuB,IAAI,EAAE,iBAAiB;EAAEC,SAAS,EAAE1B;AAAiC,CAAE,EACzE;EAAEyB,IAAI,EAAE,uBAAuB;EAAEC,SAAS,EAAEX;AAAyB,CAAE,EACvE;EAAEU,IAAI,EAAE,mBAAmB;EAAEC,SAAS,EAAEV;AAAyB,CAAE,EACnE;EAAES,IAAI,EAAE,kCAAkC;EAAEC,SAAS,EAAET;AAAsB,CAAE,EAC/E;EAAEQ,IAAI,EAAE,wBAAwB;EAAEC,SAAS,EAAER;AAA8B,CAAE;AAE7E;AACA;EACEO,IAAI,EAAE,sBAAsB;EAC5BE,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,qDAAqD,CAAC,CAC9EC,IAAI,CAACC,GAAG,IAAIA,GAAG,CAACC,yBAAyB;CAC7C;AAED;AACA;EAAEL,IAAI,EAAE,qBAAqB;EAAEC,SAAS,EAAEvB;AAA0B,CAAE,EACtE;EAAEsB,IAAI,EAAE,yBAAyB;EAAEC,SAAS,EAAEtB;AAA8B,CAAE,EAC9E;EAAEqB,IAAI,EAAE,8BAA8B;EAAEC,SAAS,EAAErB;AAAmC,CAAE,EACxF;EAAEoB,IAAI,EAAE,kCAAkC;EAAEC,SAAS,EAAEpB;AAAyC,CAAE,EAClG;EAAEmB,IAAI,EAAE,kCAAkC;EAAEC,SAAS,EAAEnB;AAAsC,CAAE,EAC/F;EAAEkB,IAAI,EAAE,sCAAsC;EAAEC,SAAS,EAAElB;AAA4C,CAAE,EACzG;EAAEiB,IAAI,EAAE,mBAAmB;EAAEC,SAAS,EAAEjB;AAAyB,CAAE,EACnE;EAAEgB,IAAI,EAAE,kBAAkB;EAAEC,SAAS,EAAEhB;AAAwB,CAAE;AAEjE;AACA;EAAEe,IAAI,EAAE,qBAAqB;EAAEC,SAAS,EAAEf;AAA0B,CAAE,EACtE;EAAEc,IAAI,EAAE,yBAAyB;EAAEC,SAAS,EAAEd;AAA8B,CAAE,EAC9E;EAAEa,IAAI,EAAE,iBAAiB;EAAEC,SAAS,EAAE7B;AAAuB,CAAE,EAC/D;EAAE4B,IAAI,EAAE,kCAAkC;EAAEC,SAAS,EAAE5B;AAA8B,CAAE,EACvF;EAAE2B,IAAI,EAAE,UAAU;EAAEC,SAAS,EAAEb;AAAiB,CAAE,EAClD;EAAEY,IAAI,EAAE,SAAS;EAAEC,SAAS,EAAEZ;AAAiB,CAAE,EACjD;EAAEW,IAAI,EAAE,qBAAqB;EAAEC,SAAS,EAAE3B;AAA2B,CAAE,EACvE;EAAE0B,IAAI,EAAE,+BAA+B;EAAEC,SAAS,EAAEP;AAAwB,CAAE,EAC9E;EAAEM,IAAI,EAAE,0BAA0B;EAAEC,SAAS,EAAEN;AAAwB,CAAE,EACzE;EAAEK,IAAI,EAAE,+BAA+B;EAAEC,SAAS,EAAEL;AAAwB,CAAE,EAC9E;EAAEI,IAAI,EAAE,mCAAmC;EAAEC,SAAS,EAAEJ;AAAwB,CAAE,EAClF;EAAEG,IAAI,EAAE,wBAAwB;EAAEC,SAAS,EAAEH;AAAwB,CAAE,CACxE;AAMD,OAAM,MAAOQ,6BAA6B;;;uCAA7BA,6BAA6B;IAAA;EAAA;;;YAA7BA;IAA6B;EAAA;;;gBAH9BpC,YAAY,CAACqC,QAAQ,CAACR,MAAM,CAAC,EAC7B7B,YAAY;IAAA;EAAA;;;2EAEXoC,6BAA6B;IAAAE,OAAA,GAAAC,EAAA,CAAAvC,YAAA;IAAAwC,OAAA,GAF9BxC,YAAY;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}