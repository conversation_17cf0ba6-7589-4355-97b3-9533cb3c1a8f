{"ast": null, "code": "import { Observable } from '../Observable';\nimport { argsArgArrayOrObject } from '../util/argsArgArrayOrObject';\nimport { from } from './from';\nimport { identity } from '../util/identity';\nimport { mapOneOrManyArgs } from '../util/mapOneOrManyArgs';\nimport { popResultSelector, popScheduler } from '../util/args';\nimport { createObject } from '../util/createObject';\nimport { createOperatorSubscriber } from '../operators/OperatorSubscriber';\nimport { executeSchedule } from '../util/executeSchedule';\nexport function combineLatest(...args) {\n  const scheduler = popScheduler(args);\n  const resultSelector = popResultSelector(args);\n  const {\n    args: observables,\n    keys\n  } = argsArgArrayOrObject(args);\n  if (observables.length === 0) {\n    return from([], scheduler);\n  }\n  const result = new Observable(combineLatestInit(observables, scheduler, keys ? values => createObject(keys, values) : identity));\n  return resultSelector ? result.pipe(mapOneOrManyArgs(resultSelector)) : result;\n}\nexport function combineLatestInit(observables, scheduler, valueTransform = identity) {\n  return subscriber => {\n    maybeSchedule(scheduler, () => {\n      const {\n        length\n      } = observables;\n      const values = new Array(length);\n      let active = length;\n      let remainingFirstValues = length;\n      for (let i = 0; i < length; i++) {\n        maybeSchedule(scheduler, () => {\n          const source = from(observables[i], scheduler);\n          let hasFirstValue = false;\n          source.subscribe(createOperatorSubscriber(subscriber, value => {\n            values[i] = value;\n            if (!hasFirstValue) {\n              hasFirstValue = true;\n              remainingFirstValues--;\n            }\n            if (!remainingFirstValues) {\n              subscriber.next(valueTransform(values.slice()));\n            }\n          }, () => {\n            if (! --active) {\n              subscriber.complete();\n            }\n          }));\n        }, subscriber);\n      }\n    }, subscriber);\n  };\n}\nfunction maybeSchedule(scheduler, execute, subscription) {\n  if (scheduler) {\n    executeSchedule(subscription, scheduler, execute);\n  } else {\n    execute();\n  }\n}\n//# sourceMappingURL=combineLatest.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}