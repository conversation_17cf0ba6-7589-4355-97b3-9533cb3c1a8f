{"ast": null, "code": "import { environment } from '../../../environments/environment';\nimport * as i0 from \"@angular/core\";\nexport let UtilityService = /*#__PURE__*/(() => {\n  class UtilityService {\n    constructor() {\n      this.BASE_FILE = environment.BASE_FILE;\n    }\n    isAbsoluteUrl(url) {\n      return /^(?:[a-z]+:)?\\/\\//i.test(url);\n    }\n    openFileNewTab(CFileUrl) {\n      if (CFileUrl) {\n        if (this.isAbsoluteUrl(CFileUrl)) {\n          window.open(CFileUrl, '_blank');\n        } else {\n          window.open(environment.BASE_WITHOUT_FILEROOT + CFileUrl, '_blank');\n        }\n      }\n    }\n    openFileInNewTab(CFileUrl) {\n      if (CFileUrl) window.open(`${this.BASE_FILE}${CFileUrl}`, '_blank');\n    }\n    downloadFileFromUrl(filePath, fileName) {\n      const xhr = new XMLHttpRequest();\n      xhr.open('GET', `${this.BASE_FILE}${filePath}`, true);\n      xhr.responseType = 'blob';\n      xhr.onload = () => {\n        if (xhr.status === 200) {\n          const url = window.URL.createObjectURL(xhr.response);\n          const link = document.createElement('a');\n          link.href = url;\n          link.download = fileName;\n          link.style.display = 'none';\n          document.body.appendChild(link);\n          link.click();\n          document.body.removeChild(link);\n          window.URL.revokeObjectURL(url);\n        }\n      };\n      xhr.send();\n    }\n    getFileNameFromUrl(url) {\n      const parts = url.split('/');\n      const fileName = parts.pop();\n      return fileName;\n    }\n    downloadFileFullUrl(filePath, fileName) {\n      const xhr = new XMLHttpRequest();\n      xhr.open('GET', `${filePath}`, true);\n      xhr.responseType = 'blob';\n      xhr.onload = () => {\n        if (xhr.status === 200) {\n          const url = window.URL.createObjectURL(xhr.response);\n          const link = document.createElement('a');\n          link.href = url;\n          link.download = fileName;\n          link.style.display = 'none';\n          document.body.appendChild(link);\n          link.click();\n          document.body.removeChild(link);\n          window.URL.revokeObjectURL(url);\n        }\n      };\n      xhr.send();\n    }\n    downloadExcelFile(fileByte, fileName) {\n      const base64Data = fileByte;\n      const byteCharacters = atob(base64Data);\n      const byteNumbers = new Array(byteCharacters.length);\n      for (let i = 0; i < byteCharacters.length; i++) {\n        byteNumbers[i] = byteCharacters.charCodeAt(i);\n      }\n      const byteArray = new Uint8Array(byteNumbers);\n      const blob = new Blob([byteArray], {\n        type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'\n      });\n      const url = URL.createObjectURL(blob);\n      const link = document.createElement('a');\n      link.href = url;\n      link.setAttribute('download', `${fileName}` + '.xlsx');\n      document.body.appendChild(link);\n      link.click();\n      URL.revokeObjectURL(url);\n      document.body.removeChild(link);\n    }\n    base64ToBlob(base64, contentType = '') {\n      const base64Data = base64.split(',')[1] || base64;\n      // Decode base64 string\n      const byteCharacters = atob(base64Data);\n      // Create a byte array with length equal to the number of bytes in the string\n      const byteArrays = [];\n      for (let offset = 0; offset < byteCharacters.length; offset += 512) {\n        const slice = byteCharacters.slice(offset, offset + 512);\n        const byteNumbers = new Array(slice.length);\n        for (let i = 0; i < slice.length; i++) {\n          byteNumbers[i] = slice.charCodeAt(i);\n        }\n        const byteArray = new Uint8Array(byteNumbers);\n        byteArrays.push(byteArray);\n      }\n      return new Blob(byteArrays, {\n        type: contentType\n      });\n    }\n    getFileExtension(filename) {\n      var ext = /^.+\\.([^.]+)$/.exec(filename);\n      return ext == null ? '' : ext[1];\n    }\n    htmltoText(html) {\n      let text = html;\n      text = text.replace(/\\n/gi, \"\");\n      text = text.replace(/<style([\\s\\S]*?)<\\/style>/gi, \"\");\n      text = text.replace(/<script([\\s\\S]*?)<\\/script>/gi, \"\");\n      text = text.replace(/<a.*?href=\"(.*?)[\\?\\\"].*?>(.*?)<\\/a.*?>/gi, \" $2 $1 \");\n      text = text.replace(/<\\/div>/gi, \"\\n\\n\");\n      text = text.replace(/<\\/li>/gi, \"\\n\");\n      text = text.replace(/<li.*?>/gi, \"  *  \");\n      text = text.replace(/<\\/ul>/gi, \"\\n\\n\");\n      text = text.replace(/<\\/p>/gi, \"\\n\\n\");\n      text = text.replace(/<br\\s*[\\/]?>/gi, \"\\n\");\n      text = text.replace(/<[^>]+>/gi, \"\");\n      text = text.replace(/^\\s*/gim, \"\");\n      text = text.replace(/ ,/gi, \",\");\n      text = text.replace(/ +/gi, \" \");\n      text = text.replace(/\\n+/gi, \"\\n\\n\");\n      return text;\n    }\n    static {\n      this.ɵfac = function UtilityService_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || UtilityService)();\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: UtilityService,\n        factory: UtilityService.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return UtilityService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}