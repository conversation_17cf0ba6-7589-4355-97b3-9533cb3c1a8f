{"ast": null, "code": "export class SmartTableData {}", "map": {"version": 3, "names": ["SmartTableData"], "sources": ["C:\\Users\\<USER>\\Documents\\jeansalechange\\adminFront\\src\\app\\@core\\data\\smart-table.ts"], "sourcesContent": ["\r\nexport abstract class SmartTableData {\r\n  abstract getData(): any[];\r\n}\r\n"], "mappings": "AACA,OAAM,MAAgBA,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}