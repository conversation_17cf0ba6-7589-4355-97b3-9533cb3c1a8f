{"ast": null, "code": "/* tslint:disable */\n/* eslint-disable */\n/* Code generated by ng-openapi-gen DO NOT EDIT. */\nimport { HttpResponse } from '@angular/common/http';\nimport { filter, map } from 'rxjs/operators';\nimport { RequestBuilder } from '../../request-builder';\nexport function apiRegularChangeItemGetListRegularChangeItemPost$Plain(http, rootUrl, params, context) {\n  const rb = new RequestBuilder(rootUrl, apiRegularChangeItemGetListRegularChangeItemPost$Plain.PATH, 'post');\n  if (params) {}\n  return http.request(rb.build({\n    responseType: 'text',\n    accept: 'text/plain',\n    context\n  })).pipe(filter(r => r instanceof HttpResponse), map(r => {\n    return r;\n  }));\n}\napiRegularChangeItemGetListRegularChangeItemPost$Plain.PATH = '/api/RegularChangeItem/GetListRegularChangeItem';", "map": {"version": 3, "names": ["HttpResponse", "filter", "map", "RequestBuilder", "apiRegularChangeItemGetListRegularChangeItemPost$Plain", "http", "rootUrl", "params", "context", "rb", "PATH", "request", "build", "responseType", "accept", "pipe", "r"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\services\\api\\fn\\regular-change-item\\api-regular-change-item-get-list-regular-change-item-post-plain.ts"], "sourcesContent": ["/* tslint:disable */\r\n/* eslint-disable */\r\n/* Code generated by ng-openapi-gen DO NOT EDIT. */\r\n\r\nimport { HttpClient, HttpContext, HttpResponse } from '@angular/common/http';\r\nimport { Observable } from 'rxjs';\r\nimport { filter, map } from 'rxjs/operators';\r\nimport { StrictHttpResponse } from '../../strict-http-response';\r\nimport { RequestBuilder } from '../../request-builder';\r\n\r\nimport { GetListRegularChangeItemResListResponseBase } from '../../models/get-list-regular-change-item-res-list-response-base';\r\n\r\nexport interface ApiRegularChangeItemGetListRegularChangeItemPost$Plain$Params {\r\n}\r\n\r\nexport function apiRegularChangeItemGetListRegularChangeItemPost$Plain(http: HttpClient, rootUrl: string, params?: ApiRegularChangeItemGetListRegularChangeItemPost$Plain$Params, context?: HttpContext): Observable<StrictHttpResponse<GetListRegularChangeItemResListResponseBase>> {\r\n  const rb = new RequestBuilder(rootUrl, apiRegularChangeItemGetListRegularChangeItemPost$Plain.PATH, 'post');\r\n  if (params) {\r\n  }\r\n\r\n  return http.request(\r\n    rb.build({ responseType: 'text', accept: 'text/plain', context })\r\n  ).pipe(\r\n    filter((r: any): r is HttpResponse<any> => r instanceof HttpResponse),\r\n    map((r: HttpResponse<any>) => {\r\n      return r as StrictHttpResponse<GetListRegularChangeItemResListResponseBase>;\r\n    })\r\n  );\r\n}\r\n\r\napiRegularChangeItemGetListRegularChangeItemPost$Plain.PATH = '/api/RegularChangeItem/GetListRegularChangeItem';\r\n"], "mappings": "AAAA;AACA;AACA;AAEA,SAAkCA,YAAY,QAAQ,sBAAsB;AAE5E,SAASC,MAAM,EAAEC,GAAG,QAAQ,gBAAgB;AAE5C,SAASC,cAAc,QAAQ,uBAAuB;AAOtD,OAAM,SAAUC,sDAAsDA,CAACC,IAAgB,EAAEC,OAAe,EAAEC,MAAsE,EAAEC,OAAqB;EACrM,MAAMC,EAAE,GAAG,IAAIN,cAAc,CAACG,OAAO,EAAEF,sDAAsD,CAACM,IAAI,EAAE,MAAM,CAAC;EAC3G,IAAIH,MAAM,EAAE,CACZ;EAEA,OAAOF,IAAI,CAACM,OAAO,CACjBF,EAAE,CAACG,KAAK,CAAC;IAAEC,YAAY,EAAE,MAAM;IAAEC,MAAM,EAAE,YAAY;IAAEN;EAAO,CAAE,CAAC,CAClE,CAACO,IAAI,CACJd,MAAM,CAAEe,CAAM,IAA6BA,CAAC,YAAYhB,YAAY,CAAC,EACrEE,GAAG,CAAEc,CAAoB,IAAI;IAC3B,OAAOA,CAAoE;EAC7E,CAAC,CAAC,CACH;AACH;AAEAZ,sDAAsD,CAACM,IAAI,GAAG,iDAAiD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}