{"ast": null, "code": "//! moment.js locale configuration\n//! locale : Central Atlas Tamazight [tzm]\n//! author : <PERSON><PERSON> : https://github.com/abdelsaid\n\n;\n(function (global, factory) {\n  typeof exports === 'object' && typeof module !== 'undefined' && typeof require === 'function' ? factory(require('../moment')) : typeof define === 'function' && define.amd ? define(['../moment'], factory) : factory(global.moment);\n})(this, function (moment) {\n  'use strict';\n\n  //! moment.js locale configuration\n  var tzm = moment.defineLocale('tzm', {\n    months: 'ⵉⵏⵏⴰⵢⵔ_ⴱⵕⴰⵢⵕ_ⵎⴰⵕⵚ_ⵉⴱⵔⵉⵔ_ⵎⴰⵢⵢⵓ_ⵢⵓⵏⵢⵓ_ⵢⵓⵍⵢⵓⵣ_ⵖⵓⵛⵜ_ⵛⵓⵜⴰⵏⴱⵉⵔ_ⴽⵟⵓⴱⵕ_ⵏⵓⵡⴰⵏⴱⵉⵔ_ⴷⵓⵊⵏⴱⵉⵔ'.split('_'),\n    monthsShort: 'ⵉⵏⵏⴰⵢⵔ_ⴱⵕⴰⵢⵕ_ⵎⴰⵕⵚ_ⵉⴱⵔⵉⵔ_ⵎⴰⵢⵢⵓ_ⵢⵓⵏⵢⵓ_ⵢⵓⵍⵢⵓⵣ_ⵖⵓⵛⵜ_ⵛⵓⵜⴰⵏⴱⵉⵔ_ⴽⵟⵓⴱⵕ_ⵏⵓⵡⴰⵏⴱⵉⵔ_ⴷⵓⵊⵏⴱⵉⵔ'.split('_'),\n    weekdays: 'ⴰⵙⴰⵎⴰⵙ_ⴰⵢⵏⴰⵙ_ⴰⵙⵉⵏⴰⵙ_ⴰⴽⵔⴰⵙ_ⴰⴽⵡⴰⵙ_ⴰⵙⵉⵎⵡⴰⵙ_ⴰⵙⵉⴹⵢⴰⵙ'.split('_'),\n    weekdaysShort: 'ⴰⵙⴰⵎⴰⵙ_ⴰⵢⵏⴰⵙ_ⴰⵙⵉⵏⴰⵙ_ⴰⴽⵔⴰⵙ_ⴰⴽⵡⴰⵙ_ⴰⵙⵉⵎⵡⴰⵙ_ⴰⵙⵉⴹⵢⴰⵙ'.split('_'),\n    weekdaysMin: 'ⴰⵙⴰⵎⴰⵙ_ⴰⵢⵏⴰⵙ_ⴰⵙⵉⵏⴰⵙ_ⴰⴽⵔⴰⵙ_ⴰⴽⵡⴰⵙ_ⴰⵙⵉⵎⵡⴰⵙ_ⴰⵙⵉⴹⵢⴰⵙ'.split('_'),\n    longDateFormat: {\n      LT: 'HH:mm',\n      LTS: 'HH:mm:ss',\n      L: 'DD/MM/YYYY',\n      LL: 'D MMMM YYYY',\n      LLL: 'D MMMM YYYY HH:mm',\n      LLLL: 'dddd D MMMM YYYY HH:mm'\n    },\n    calendar: {\n      sameDay: '[ⴰⵙⴷⵅ ⴴ] LT',\n      nextDay: '[ⴰⵙⴽⴰ ⴴ] LT',\n      nextWeek: 'dddd [ⴴ] LT',\n      lastDay: '[ⴰⵚⴰⵏⵜ ⴴ] LT',\n      lastWeek: 'dddd [ⴴ] LT',\n      sameElse: 'L'\n    },\n    relativeTime: {\n      future: 'ⴷⴰⴷⵅ ⵙ ⵢⴰⵏ %s',\n      past: 'ⵢⴰⵏ %s',\n      s: 'ⵉⵎⵉⴽ',\n      ss: '%d ⵉⵎⵉⴽ',\n      m: 'ⵎⵉⵏⵓⴺ',\n      mm: '%d ⵎⵉⵏⵓⴺ',\n      h: 'ⵙⴰⵄⴰ',\n      hh: '%d ⵜⴰⵙⵙⴰⵄⵉⵏ',\n      d: 'ⴰⵙⵙ',\n      dd: '%d oⵙⵙⴰⵏ',\n      M: 'ⴰⵢoⵓⵔ',\n      MM: '%d ⵉⵢⵢⵉⵔⵏ',\n      y: 'ⴰⵙⴳⴰⵙ',\n      yy: '%d ⵉⵙⴳⴰⵙⵏ'\n    },\n    week: {\n      dow: 6,\n      // Saturday is the first day of the week.\n      doy: 12 // The week that contains Jan 12th is the first week of the year.\n    }\n  });\n  return tzm;\n});", "map": {"version": 3, "names": ["global", "factory", "exports", "module", "require", "define", "amd", "moment", "tzm", "defineLocale", "months", "split", "monthsShort", "weekdays", "weekdaysShort", "weekdaysMin", "longDateFormat", "LT", "LTS", "L", "LL", "LLL", "LLLL", "calendar", "sameDay", "nextDay", "nextWeek", "lastDay", "lastWeek", "same<PERSON><PERSON><PERSON>", "relativeTime", "future", "past", "s", "ss", "m", "mm", "h", "hh", "d", "dd", "M", "MM", "y", "yy", "week", "dow", "doy"], "sources": ["C:/Users/<USER>/Documents/salechange-product/adminFront/node_modules/moment/locale/tzm.js"], "sourcesContent": ["//! moment.js locale configuration\n//! locale : Central Atlas Tamazight [tzm]\n//! author : <PERSON><PERSON> : https://github.com/abdelsaid\n\n;(function (global, factory) {\n   typeof exports === 'object' && typeof module !== 'undefined'\n       && typeof require === 'function' ? factory(require('../moment')) :\n   typeof define === 'function' && define.amd ? define(['../moment'], factory) :\n   factory(global.moment)\n}(this, (function (moment) { 'use strict';\n\n    //! moment.js locale configuration\n\n    var tzm = moment.defineLocale('tzm', {\n        months: 'ⵉⵏⵏⴰⵢⵔ_ⴱⵕⴰⵢⵕ_ⵎⴰⵕⵚ_ⵉⴱⵔⵉⵔ_ⵎⴰⵢⵢⵓ_ⵢⵓⵏⵢⵓ_ⵢⵓⵍⵢⵓⵣ_ⵖⵓⵛⵜ_ⵛⵓⵜⴰⵏⴱⵉⵔ_ⴽⵟⵓⴱⵕ_ⵏⵓⵡⴰⵏⴱⵉⵔ_ⴷⵓⵊⵏⴱⵉⵔ'.split(\n            '_'\n        ),\n        monthsShort:\n            'ⵉⵏⵏⴰⵢⵔ_ⴱⵕⴰⵢⵕ_ⵎⴰⵕⵚ_ⵉⴱⵔⵉⵔ_ⵎⴰⵢⵢⵓ_ⵢⵓⵏⵢⵓ_ⵢⵓⵍⵢⵓⵣ_ⵖⵓⵛⵜ_ⵛⵓⵜⴰⵏⴱⵉⵔ_ⴽⵟⵓⴱⵕ_ⵏⵓⵡⴰⵏⴱⵉⵔ_ⴷⵓⵊⵏⴱⵉⵔ'.split(\n                '_'\n            ),\n        weekdays: 'ⴰⵙⴰⵎⴰⵙ_ⴰⵢⵏⴰⵙ_ⴰⵙⵉⵏⴰⵙ_ⴰⴽⵔⴰⵙ_ⴰⴽⵡⴰⵙ_ⴰⵙⵉⵎⵡⴰⵙ_ⴰⵙⵉⴹⵢⴰⵙ'.split('_'),\n        weekdaysShort: 'ⴰⵙⴰⵎⴰⵙ_ⴰⵢⵏⴰⵙ_ⴰⵙⵉⵏⴰⵙ_ⴰⴽⵔⴰⵙ_ⴰⴽⵡⴰⵙ_ⴰⵙⵉⵎⵡⴰⵙ_ⴰⵙⵉⴹⵢⴰⵙ'.split('_'),\n        weekdaysMin: 'ⴰⵙⴰⵎⴰⵙ_ⴰⵢⵏⴰⵙ_ⴰⵙⵉⵏⴰⵙ_ⴰⴽⵔⴰⵙ_ⴰⴽⵡⴰⵙ_ⴰⵙⵉⵎⵡⴰⵙ_ⴰⵙⵉⴹⵢⴰⵙ'.split('_'),\n        longDateFormat: {\n            LT: 'HH:mm',\n            LTS: 'HH:mm:ss',\n            L: 'DD/MM/YYYY',\n            LL: 'D MMMM YYYY',\n            LLL: 'D MMMM YYYY HH:mm',\n            LLLL: 'dddd D MMMM YYYY HH:mm',\n        },\n        calendar: {\n            sameDay: '[ⴰⵙⴷⵅ ⴴ] LT',\n            nextDay: '[ⴰⵙⴽⴰ ⴴ] LT',\n            nextWeek: 'dddd [ⴴ] LT',\n            lastDay: '[ⴰⵚⴰⵏⵜ ⴴ] LT',\n            lastWeek: 'dddd [ⴴ] LT',\n            sameElse: 'L',\n        },\n        relativeTime: {\n            future: 'ⴷⴰⴷⵅ ⵙ ⵢⴰⵏ %s',\n            past: 'ⵢⴰⵏ %s',\n            s: 'ⵉⵎⵉⴽ',\n            ss: '%d ⵉⵎⵉⴽ',\n            m: 'ⵎⵉⵏⵓⴺ',\n            mm: '%d ⵎⵉⵏⵓⴺ',\n            h: 'ⵙⴰⵄⴰ',\n            hh: '%d ⵜⴰⵙⵙⴰⵄⵉⵏ',\n            d: 'ⴰⵙⵙ',\n            dd: '%d oⵙⵙⴰⵏ',\n            M: 'ⴰⵢoⵓⵔ',\n            MM: '%d ⵉⵢⵢⵉⵔⵏ',\n            y: 'ⴰⵙⴳⴰⵙ',\n            yy: '%d ⵉⵙⴳⴰⵙⵏ',\n        },\n        week: {\n            dow: 6, // Saturday is the first day of the week.\n            doy: 12, // The week that contains Jan 12th is the first week of the year.\n        },\n    });\n\n    return tzm;\n\n})));\n"], "mappings": "AAAA;AACA;AACA;;AAEA;AAAE,WAAUA,MAAM,EAAEC,OAAO,EAAE;EAC1B,OAAOC,OAAO,KAAK,QAAQ,IAAI,OAAOC,MAAM,KAAK,WAAW,IACrD,OAAOC,OAAO,KAAK,UAAU,GAAGH,OAAO,CAACG,OAAO,CAAC,WAAW,CAAC,CAAC,GACpE,OAAOC,MAAM,KAAK,UAAU,IAAIA,MAAM,CAACC,GAAG,GAAGD,MAAM,CAAC,CAAC,WAAW,CAAC,EAAEJ,OAAO,CAAC,GAC3EA,OAAO,CAACD,MAAM,CAACO,MAAM,CAAC;AACzB,CAAC,EAAC,IAAI,EAAG,UAAUA,MAAM,EAAE;EAAE,YAAY;;EAErC;EAEA,IAAIC,GAAG,GAAGD,MAAM,CAACE,YAAY,CAAC,KAAK,EAAE;IACjCC,MAAM,EAAE,iFAAiF,CAACC,KAAK,CAC3F,GACJ,CAAC;IACDC,WAAW,EACP,iFAAiF,CAACD,KAAK,CACnF,GACJ,CAAC;IACLE,QAAQ,EAAE,iDAAiD,CAACF,KAAK,CAAC,GAAG,CAAC;IACtEG,aAAa,EAAE,iDAAiD,CAACH,KAAK,CAAC,GAAG,CAAC;IAC3EI,WAAW,EAAE,iDAAiD,CAACJ,KAAK,CAAC,GAAG,CAAC;IACzEK,cAAc,EAAE;MACZC,EAAE,EAAE,OAAO;MACXC,GAAG,EAAE,UAAU;MACfC,CAAC,EAAE,YAAY;MACfC,EAAE,EAAE,aAAa;MACjBC,GAAG,EAAE,mBAAmB;MACxBC,IAAI,EAAE;IACV,CAAC;IACDC,QAAQ,EAAE;MACNC,OAAO,EAAE,aAAa;MACtBC,OAAO,EAAE,aAAa;MACtBC,QAAQ,EAAE,aAAa;MACvBC,OAAO,EAAE,cAAc;MACvBC,QAAQ,EAAE,aAAa;MACvBC,QAAQ,EAAE;IACd,CAAC;IACDC,YAAY,EAAE;MACVC,MAAM,EAAE,eAAe;MACvBC,IAAI,EAAE,QAAQ;MACdC,CAAC,EAAE,MAAM;MACTC,EAAE,EAAE,SAAS;MACbC,CAAC,EAAE,OAAO;MACVC,EAAE,EAAE,UAAU;MACdC,CAAC,EAAE,MAAM;MACTC,EAAE,EAAE,aAAa;MACjBC,CAAC,EAAE,KAAK;MACRC,EAAE,EAAE,UAAU;MACdC,CAAC,EAAE,OAAO;MACVC,EAAE,EAAE,WAAW;MACfC,CAAC,EAAE,OAAO;MACVC,EAAE,EAAE;IACR,CAAC;IACDC,IAAI,EAAE;MACFC,GAAG,EAAE,CAAC;MAAE;MACRC,GAAG,EAAE,EAAE,CAAE;IACb;EACJ,CAAC,CAAC;EAEF,OAAOvC,GAAG;AAEd,CAAE,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}