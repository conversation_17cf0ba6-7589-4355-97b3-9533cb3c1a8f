{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { BaseComponent } from '../../components/base/baseComponent';\nimport { Component, ViewChild, ViewChildren } from '@angular/core';\nimport { NbCardModule, NbInputModule, NbSelectModule, NbOptionModule, NbCheckboxModule } from '@nebular/theme';\nimport { FormsModule } from '@angular/forms';\nimport { StatusPipe } from '../../../@theme/pipes/mapping.pipe';\nimport { NgIf, NgFor } from '@angular/common';\nimport { ShareRequest } from 'src/app/shared/model/requests/shareRequest';\nimport { BreadcrumbComponent } from '../../components/breadcrumb/breadcrumb.component';\nimport { PaginationComponent } from '../../components/pagination/pagination.component';\nimport { FormGroupComponent } from 'src/app/@theme/components/form-group/form-group.component';\nimport { lastValueFrom } from 'rxjs';\nimport { takeUntilDestroyed } from '@angular/core/rxjs-interop';\nimport { BaseLabelDirective } from 'src/app/@theme/directives/label.directive';\nlet UserManagementComponent = class UserManagementComponent extends BaseComponent {\n  constructor(dialogService, share, userService, userGroupService, message, allow, valid, router, pettern, buildCaseService, destroyref) {\n    super(allow);\n    this.dialogService = dialogService;\n    this.share = share;\n    this.userService = userService;\n    this.userGroupService = userGroupService;\n    this.message = message;\n    this.allow = allow;\n    this.valid = valid;\n    this.router = router;\n    this.pettern = pettern;\n    this.buildCaseService = buildCaseService;\n    this.destroyref = destroyref;\n    this.pageFirst = 1;\n    this.request = new ShareRequest();\n    this.userList = [];\n    this.userLisrPerPage = [];\n    this.userGroups = [];\n    this.isNew = false;\n    this.statusOptions = [{\n      value: 0,\n      label: '停用'\n    }, {\n      value: 1,\n      label: '啟用'\n    }];\n    this.userType = [{\n      value: 0,\n      name: '系統管理員'\n    }, {\n      value: 1,\n      name: '一般使用者'\n    }];\n    this.selectedBuildcaseList = [];\n    this.buildCaseList = [];\n    this.share.SharedUser.subscribe(res => {\n      this.userList = res;\n    });\n    this.share.SharedUserGroup.subscribe(res => {\n      this.userGroups = res;\n    });\n  }\n  ngOnInit() {\n    this.getList();\n    this.getUserGroup();\n    this.getBuildCaseList();\n  }\n  // 取得群組列表\n  getUserGroup() {\n    this.userGroupService.apiUserGroupGetListPost$Json({\n      body: {\n        CName: \"\",\n        CStatus: 1,\n        PageIndex: 1,\n        PageSize: 1000\n      }\n    }).pipe(takeUntilDestroyed(this.destroyref)).subscribe(res => {\n      this.userGroups = res.Entries;\n      this.share.SetUserGroup(this.userGroups);\n    });\n  }\n  // 取得使用者列表\n  getList() {\n    this.userService.apiUserGetListPost$Json({\n      body: {\n        CStatus: this.request.CStatus,\n        CUserName: this.request.CUserName\n      }\n    }).pipe(takeUntilDestroyed(this.destroyref)).subscribe(res => {\n      this.userList = res.Entries;\n      this.totalRecords = res.TotalItems;\n      this.handlepagination();\n      this.share.SetUser(this.userList);\n    });\n  }\n  handlepagination() {\n    this.pageFirst = (this.pageIndex - 1) * this.pageSize + 1;\n    let lastIndex = this.totalRecords < this.pageIndex * this.pageSize ? this.totalRecords + 1 : this.pageIndex * this.pageSize;\n    this.userLisrPerPage = this.userList.slice(this.pageFirst - 1, lastIndex);\n  }\n  // 取得使用者資料\n  getData() {\n    return lastValueFrom(this.userService.apiUserGetDataPost$Json({\n      body: {\n        CUserId: this.request.CUserId.toString()\n      }\n    })).then(res => {\n      this.user = res.Entries;\n    });\n  }\n  getBuildCaseList() {\n    this.buildCaseService.apiBuildCaseGetBuildCaseListPost$Json().pipe(takeUntilDestroyed(this.destroyref)).subscribe(res => {\n      this.buildCaseList = res.Entries;\n    });\n  }\n  useTypeSelect(e) {\n    this.user.CUserType = e;\n  }\n  isCheck(e, item) {\n    let isSelected = this.selectedBuildcaseList.find(i => i.CBuilCaseId === item.cID);\n    if (!isSelected) {\n      this.selectedBuildcaseList.push({\n        CBuilCaseId: item.cID,\n        CBuilCaseName: item.CBuildCaseName\n      });\n    } else {\n      this.selectedBuildcaseList = this.selectedBuildcaseList.filter(i => i.CBuilCaseId !== item.cID);\n    }\n  }\n  toggleSelectAll(checked) {\n    // toggle all option status\n    this.checkItemEl.forEach(i => {\n      i.nativeElement.checked = checked.target.checked ? true : false;\n    });\n    // set all buildcase in request \n    if (checked.target.checked) {\n      this.selectedBuildcaseList = this.buildCaseList.map(i => {\n        return {\n          CBuilCaseId: i.cID,\n          CBuilCaseName: i.CBuildCaseName\n        };\n      });\n    } else {\n      this.selectedBuildcaseList = [];\n    }\n  }\n  add(dialog) {\n    this.user = {\n      CAccount: '',\n      CfmPassword: '',\n      CName: '',\n      CStatus: undefined,\n      CUserId: '',\n      ListUserGroup: [],\n      OldPassword: '',\n      Password: ''\n    };\n    this.isNew = true;\n    this.dialogService.open(dialog);\n  }\n  setUserBuildCase() {\n    switch (this.user.CUserType) {\n      case 0:\n        this.user.UserBuildCases = [];\n        break;\n      case 1:\n        this.user.UserBuildCases = this.selectedBuildcaseList.map(i => {\n          return {\n            CBuilCaseId: i.CBuilCaseId,\n            CBuilCaseName: i.CBuilCaseName\n          };\n        });\n        break;\n      default:\n    }\n  }\n  save(ref) {\n    this.validation();\n    if (this.valid.errorMessages.length > 0) {\n      this.message.showErrorMSGs(this.valid.errorMessages);\n      return;\n    }\n    this.setUserBuildCase();\n    if (this.isNew) {\n      this.userService.apiUserAddDataPost$Json({\n        body: this.user\n      }).pipe(takeUntilDestroyed(this.destroyref)).subscribe(res => {\n        if (res.StatusCode === 0) {\n          this.message.showSucessMSG('執行成功');\n          this.getList();\n          ref.close();\n        } else {\n          this.message.showErrorMSG(`${res.Message}`);\n        }\n      });\n    } else {\n      this.userService.apiUserSaveDataPost$Json({\n        body: this.user\n      }).pipe(takeUntilDestroyed(this.destroyref)).subscribe(res => {\n        if (res.StatusCode === 0) {\n          this.message.showSucessMSG('執行成功');\n          this.getList();\n          ref.close();\n        } else {\n          this.message.showErrorMSG(`${res.Message}`);\n        }\n      });\n    }\n  }\n  remove() {\n    this.userService.apiUserRemoveUserPost$Json({\n      body: {\n        CUserId: this.request.CUserId.toString()\n      }\n    }).pipe(takeUntilDestroyed(this.destroyref)).subscribe(res => {\n      if (res.StatusCode === 0) {\n        this.message.showSucessMSG('執行成功');\n        this.getList();\n      }\n    });\n  }\n  onDelete(data) {\n    if (window.confirm('是否確定刪除?')) {\n      this.userService.apiUserRemoveUserPost$Json({\n        body: {\n          CUserId: data.CUserId\n        }\n      }).pipe(takeUntilDestroyed(this.destroyref)).subscribe(res => {\n        if (res.StatusCode === 0) {\n          this.message.showSucessMSG('執行成功');\n          this.getList();\n        }\n      });\n    } else {\n      return;\n    }\n  }\n  onEdit(data, dialog) {\n    this.isNew = false;\n    this.userService.apiUserGetDataPost$Json({\n      body: {\n        CUserId: data.CUserId\n      }\n    }).pipe(takeUntilDestroyed(this.destroyref)).subscribe(res => {\n      this.user = res.Entries;\n      this.selectedBuildcaseList = [...(this.user.UserBuildCases ?? [])];\n      setTimeout(() => {\n        this.checkAllItemEl.nativeElement.checked = true;\n        this.checkItemEl.forEach(i => {\n          let findBuildcae = res.Entries?.UserBuildCases?.find(item => item.CBuilCaseId?.toString() === i.nativeElement.id);\n          if (findBuildcae) {\n            i.nativeElement.checked = true;\n          } else {\n            this.checkAllItemEl.nativeElement.checked = false;\n          }\n        });\n      }, 0);\n      this.dialogService.open(dialog);\n    });\n  }\n  validation() {\n    this.valid.clear();\n    this.valid.required('[帳號]', this.user.CAccount);\n    this.valid.pattern('[帳號]', this.user.CAccount, this.pettern.AccountPettern, '只限英文字母及數字，長度介於3~20個字元!');\n    this.valid.required('[電子郵件]', this.user.CMail);\n    this.valid.pattern('[電子郵件]', this.user.CMail, this.pettern.MailPettern);\n    this.valid.required('[名稱]', this.user.CName);\n    if (this.isNew) {\n      this.valid.required('[密碼]', this.user.Password);\n      this.valid.required('[確認密碼]', this.user.CfmPassword);\n      this.valid.pattern('[確認密碼]', this.user.CfmPassword, this.pettern.PasswordPettern);\n      this.valid.pattern('[密碼]', this.user.Password, this.pettern.PasswordPettern);\n    }\n    this.valid.equal('[密碼]', '[確認密碼]', this.user.Password, this.user.CfmPassword);\n    if (this.user.ListUserGroup.length < 1) {\n      this.valid.required('[權限]', null);\n    }\n    this.valid.required('[狀態]', this.user.CStatus);\n    this.valid.required('[身分]', this.user.CUserType);\n    if (this.user.CUserType === 1 && this.selectedBuildcaseList.length < 1) {\n      this.valid.addErrorMessage('[建案] 必填');\n    }\n  }\n};\n__decorate([ViewChildren(\"checkItem\")], UserManagementComponent.prototype, \"checkItemEl\", void 0);\n__decorate([ViewChild('checkAllItem')], UserManagementComponent.prototype, \"checkAllItemEl\", void 0);\nUserManagementComponent = __decorate([Component({\n  selector: 'ngx-user-management',\n  templateUrl: './user-management.component.html',\n  styleUrls: ['./user-management.component.scss'],\n  standalone: true,\n  imports: [NbCardModule, BreadcrumbComponent, NbInputModule, FormsModule, NbSelectModule, NbOptionModule, NgIf, NgFor, PaginationComponent, StatusPipe, BaseLabelDirective, NbCheckboxModule, FormGroupComponent]\n})], UserManagementComponent);\nexport { UserManagementComponent };", "map": {"version": 3, "names": ["BaseComponent", "Component", "ViewChild", "ViewChildren", "NbCardModule", "NbInputModule", "NbSelectModule", "NbOptionModule", "NbCheckboxModule", "FormsModule", "StatusPipe", "NgIf", "<PERSON><PERSON><PERSON>", "ShareRequest", "BreadcrumbComponent", "PaginationComponent", "FormGroupComponent", "lastValueFrom", "takeUntilDestroyed", "BaseLabelDirective", "UserManagementComponent", "constructor", "dialogService", "share", "userService", "userGroupService", "message", "allow", "valid", "router", "pettern", "buildCaseService", "destroyref", "pageFirst", "request", "userList", "userLisrPerPage", "userGroups", "isNew", "statusOptions", "value", "label", "userType", "name", "selectedBuildcaseList", "buildCaseList", "SharedUser", "subscribe", "res", "SharedUserGroup", "ngOnInit", "getList", "getUserGroup", "getBuildCaseList", "apiUserGroupGetListPost$Json", "body", "CName", "CStatus", "PageIndex", "PageSize", "pipe", "Entries", "SetUserGroup", "apiUserGetListPost$Json", "CUserName", "totalRecords", "TotalItems", "handlepagination", "SetUser", "pageIndex", "pageSize", "lastIndex", "slice", "getData", "apiUserGetDataPost$Json", "CUserId", "toString", "then", "user", "apiBuildCaseGetBuildCaseListPost$Json", "useTypeSelect", "e", "CUserType", "is<PERSON><PERSON><PERSON>", "item", "isSelected", "find", "i", "CBuilCaseId", "cID", "push", "CBuilCaseName", "CBuildCaseName", "filter", "toggleSelectAll", "checked", "checkItemEl", "for<PERSON>ach", "nativeElement", "target", "map", "add", "dialog", "CAccount", "CfmPassword", "undefined", "ListUserGroup", "OldPassword", "Password", "open", "setUserBuildCase", "UserBuildCases", "save", "ref", "validation", "errorMessages", "length", "showErrorMSGs", "apiUserAddDataPost$Json", "StatusCode", "showSucessMSG", "close", "showErrorMSG", "Message", "apiUserSaveDataPost$Json", "remove", "apiUserRemoveUserPost$Json", "onDelete", "data", "window", "confirm", "onEdit", "setTimeout", "checkAllItemEl", "findBuildcae", "id", "clear", "required", "pattern", "Account<PERSON><PERSON><PERSON>", "CMail", "MailPettern", "PasswordPettern", "equal", "addErrorMessage", "__decorate", "selector", "templateUrl", "styleUrls", "standalone", "imports"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\system-management\\user-management\\user-management.component.ts"], "sourcesContent": ["\r\nimport { BaseComponent } from '../../components/base/baseComponent';\r\nimport { Component, DestroyRef, ElementRef, OnInit, QueryList, TemplateRef, ViewChild, ViewChildren } from '@angular/core';\r\nimport { NbDialogService, NbCardModule, NbInputModule, NbSelectModule, NbOptionModule, NbCheckboxComponent, NbCheckboxModule } from '@nebular/theme';\r\n\r\nimport { EnumStatusCode } from '../../../shared/enum/enumStatusCode';\r\nimport { FormGroup, Validators, FormsModule } from '@angular/forms';\r\n\r\nimport { Router } from '@angular/router';\r\n\r\nimport { StatusPipe } from '../../../@theme/pipes/mapping.pipe';\r\nimport { NgIf, NgFor } from '@angular/common';\r\nimport { ShareRequest } from 'src/app/shared/model/requests/shareRequest';\r\nimport { BuildCaseService, UserGroupService, UserService } from 'src/services/api/services';\r\nimport { MessageService } from 'src/app/shared/services/message.service';\r\nimport { AllowHelper } from 'src/app/shared/helper/allowHelper';\r\nimport { ValidationHelper } from 'src/app/shared/helper/validationHelper';\r\nimport { PetternHelper } from 'src/app/shared/helper/petternHelper';\r\nimport { BuildCaseGetListReponse, UserBuildCase, UserGetDataResponse, UserGetListResponse, UserGroupGetListResponse, UserSaveDataArgs } from 'src/services/api/models';\r\nimport { User } from 'src/app/shared/model/user.model';\r\nimport { BreadcrumbComponent } from '../../components/breadcrumb/breadcrumb.component';\r\nimport { PaginationComponent } from '../../components/pagination/pagination.component';\r\nimport { SharedObservable } from '../../components/shared.observable';\r\nimport { FormGroupComponent } from 'src/app/@theme/components/form-group/form-group.component';\r\nimport { lastValueFrom } from 'rxjs';\r\nimport { takeUntilDestroyed } from '@angular/core/rxjs-interop';\r\nimport { BaseLabelDirective } from 'src/app/@theme/directives/label.directive';\r\n\r\n@Component({\r\n  selector: 'ngx-user-management',\r\n  templateUrl: './user-management.component.html',\r\n  styleUrls: ['./user-management.component.scss'],\r\n  standalone: true,\r\n  imports: [\r\n    NbCardModule,\r\n    BreadcrumbComponent,\r\n    NbInputModule,\r\n    FormsModule,\r\n    NbSelectModule,\r\n    NbOptionModule,\r\n    NgIf,\r\n    NgFor,\r\n    PaginationComponent,\r\n    StatusPipe,\r\n    BaseLabelDirective,\r\n    NbCheckboxModule,\r\n    FormGroupComponent\r\n  ],\r\n})\r\nexport class UserManagementComponent extends BaseComponent implements OnInit {\r\n  override pageFirst = 1\r\n  request = new ShareRequest();\r\n  userList = [] as UserGetListResponse[];\r\n  userLisrPerPage = [] as UserGetListResponse[];\r\n  user: UserSaveDataArgs;\r\n  userGroups = [] as UserGroupGetListResponse[];\r\n\r\n  isNew = false;\r\n\r\n  statusOptions = [\r\n    { value: 0, label: '停用' },\r\n    { value: 1, label: '啟用' },\r\n  ];\r\n\r\n  userType = [\r\n    { value: 0, name: '系統管理員' },\r\n    { value: 1, name: '一般使用者' },\r\n  ];\r\n  selectedBuildcaseList:UserBuildCase[]=[];\r\n\r\n  buildCaseList:BuildCaseGetListReponse[] | null | undefined = []\r\n  @ViewChildren(\"checkItem\") checkItemEl: QueryList<any>;\r\n  @ViewChild('checkAllItem') checkAllItemEl: ElementRef;\r\n\r\n  constructor(\r\n    private dialogService: NbDialogService,\r\n    private share: SharedObservable,\r\n    private userService: UserService,\r\n    private userGroupService: UserGroupService,\r\n    private message: MessageService,\r\n    protected override allow: AllowHelper,\r\n    private valid: ValidationHelper,\r\n    private router: Router,\r\n    private pettern: PetternHelper,\r\n    private buildCaseService: BuildCaseService,\r\n    private destroyref: DestroyRef\r\n  ) {\r\n    super(allow);\r\n\r\n    this.share.SharedUser.subscribe(res => {\r\n      this.userList = res;\r\n    });\r\n    this.share.SharedUserGroup.subscribe(res => {\r\n      this.userGroups = res;\r\n    });\r\n    \r\n  }\r\n\r\n  override ngOnInit(): void {\r\n    this.getList();\r\n    this.getUserGroup();\r\n    this.getBuildCaseList();\r\n  }\r\n\r\n  // 取得群組列表\r\n  getUserGroup() {\r\n    this.userGroupService.apiUserGroupGetListPost$Json({\r\n      body: {\r\n        CName: \"\",\r\n        CStatus: 1,\r\n        PageIndex: 1,\r\n        PageSize: 1000\r\n      }\r\n    })\r\n    .pipe(takeUntilDestroyed(this.destroyref))\r\n    .subscribe(res => {\r\n      this.userGroups = res.Entries!;\r\n      this.share.SetUserGroup(this.userGroups);\r\n    });\r\n  }\r\n\r\n  // 取得使用者列表\r\n  getList() {\r\n    this.userService.apiUserGetListPost$Json({\r\n      body: {\r\n        CStatus: this.request.CStatus,\r\n        CUserName: this.request.CUserName,\r\n      }\r\n    })\r\n    .pipe(takeUntilDestroyed(this.destroyref))\r\n    .subscribe(res => {\r\n      this.userList = res.Entries!;\r\n      this.totalRecords = res.TotalItems!;\r\n      this.handlepagination();\r\n      this.share.SetUser(this.userList);\r\n    });\r\n  }\r\n\r\n  handlepagination(){\r\n    this.pageFirst = (this.pageIndex - 1) * this.pageSize + 1;\r\n    let lastIndex = (this.totalRecords < this.pageIndex * this.pageSize) ? this.totalRecords + 1 : (this.pageIndex * this.pageSize);\r\n    this.userLisrPerPage = this.userList.slice(this.pageFirst - 1, lastIndex);\r\n  }\r\n\r\n\r\n  // 取得使用者資料\r\n  getData(): Promise<void> {\r\n    return lastValueFrom(this.userService.apiUserGetDataPost$Json({\r\n      body: {\r\n        CUserId: this.request.CUserId!.toString()\r\n      }\r\n    })).then(res => {\r\n      this.user = res.Entries!;\r\n    }); \r\n  }\r\n\r\n  getBuildCaseList(){\r\n    this.buildCaseService.apiBuildCaseGetBuildCaseListPost$Json()\r\n    .pipe(takeUntilDestroyed(this.destroyref)).subscribe(res => {\r\n      this.buildCaseList = res.Entries;\r\n    })\r\n  }\r\n\r\n  useTypeSelect(e:any){\r\n    this.user.CUserType = e;\r\n  }\r\n\r\n  isCheck(e:any, item:BuildCaseGetListReponse){\r\n    let isSelected = this.selectedBuildcaseList.find(i => i.CBuilCaseId === item.cID);\r\n    if(!isSelected){\r\n      this.selectedBuildcaseList.push({\r\n        CBuilCaseId: item.cID,\r\n        CBuilCaseName: item.CBuildCaseName\r\n      })\r\n    } else {\r\n      this.selectedBuildcaseList = this.selectedBuildcaseList.filter(i => i.CBuilCaseId !== item.cID)\r\n    }\r\n  }\r\n\r\n  toggleSelectAll(checked:any){\r\n    // toggle all option status\r\n    this.checkItemEl.forEach(i => {\r\n      i.nativeElement.checked = checked.target.checked ? true : false;\r\n    });\r\n\r\n    // set all buildcase in request \r\n    if(checked.target.checked){\r\n      this.selectedBuildcaseList = this.buildCaseList!.map(i => {\r\n        return {\r\n          CBuilCaseId: i.cID,\r\n          CBuilCaseName: i.CBuildCaseName\r\n        }\r\n      })\r\n    } else {\r\n      this.selectedBuildcaseList = [];\r\n    }\r\n  }\r\n\r\n  add(dialog: TemplateRef<any>) {\r\n    this.user = {\r\n      CAccount: '',\r\n      CfmPassword: '',\r\n      CName: '',\r\n      CStatus: undefined,\r\n      CUserId: '',\r\n      ListUserGroup: [],\r\n      OldPassword: '',\r\n      Password: ''\r\n    }\r\n    this.isNew = true;\r\n    this.dialogService.open(dialog);\r\n  }\r\n\r\n  setUserBuildCase(){\r\n    switch(this.user.CUserType){\r\n      case 0:\r\n        this.user.UserBuildCases = [];\r\n        break\r\n      case 1:\r\n        this.user.UserBuildCases = this.selectedBuildcaseList.map(i => {\r\n          return {\r\n            CBuilCaseId: i.CBuilCaseId,\r\n            CBuilCaseName: i.CBuilCaseName\r\n          }\r\n        })\r\n        break \r\n      default:\r\n    }\r\n  }\r\n\r\n  save(ref: any) {\r\n\r\n    this.validation();\r\n    if (this.valid.errorMessages.length > 0) {\r\n      this.message.showErrorMSGs(this.valid.errorMessages);\r\n      return;\r\n    }\r\n\r\n    this.setUserBuildCase();\r\n\r\n    if (this.isNew) {\r\n      this.userService.apiUserAddDataPost$Json({\r\n        body: this.user\r\n      })\r\n      .pipe(takeUntilDestroyed(this.destroyref))\r\n      .subscribe(res => {\r\n        if (res.StatusCode === 0) {\r\n          this.message.showSucessMSG('執行成功');\r\n          this.getList();\r\n          ref.close();\r\n        } else {\r\n          this.message.showErrorMSG(`${res.Message}`)\r\n        }\r\n      });\r\n    } else {\r\n      this.userService.apiUserSaveDataPost$Json({\r\n        body: this.user\r\n      }).pipe(takeUntilDestroyed(this.destroyref)).subscribe(res => {\r\n        if (res.StatusCode === 0) {\r\n          this.message.showSucessMSG('執行成功');\r\n          this.getList();\r\n          ref.close();\r\n        }else {\r\n          this.message.showErrorMSG(`${res.Message}`)\r\n        }\r\n      });\r\n    }\r\n\r\n  }\r\n\r\n  remove() {\r\n    this.userService.apiUserRemoveUserPost$Json({\r\n      body: {\r\n        CUserId: this.request.CUserId.toString()\r\n      }\r\n    }).pipe(takeUntilDestroyed(this.destroyref)).subscribe(res => {\r\n      if(res.StatusCode === 0) {\r\n        this.message.showSucessMSG('執行成功');\r\n        this.getList();\r\n      }\r\n    });\r\n  }\r\n\r\n  onDelete(data: UserGetDataResponse) {\r\n    if (window.confirm('是否確定刪除?')) {\r\n      this.userService.apiUserRemoveUserPost$Json({\r\n        body: {\r\n          CUserId: data.CUserId\r\n        }\r\n      }).pipe(takeUntilDestroyed(this.destroyref)).subscribe(res => {\r\n        if(res.StatusCode === 0) {\r\n          this.message.showSucessMSG('執行成功');\r\n          this.getList();\r\n        }\r\n      });\r\n    } else {\r\n      return;\r\n    }\r\n  }\r\n\r\n  onEdit(data: UserGetDataResponse, dialog: TemplateRef<any>) {\r\n    this.isNew = false;\r\n    this.userService.apiUserGetDataPost$Json({\r\n      body:{\r\n        CUserId: data.CUserId\r\n      }\r\n    }).pipe(takeUntilDestroyed(this.destroyref)).subscribe(res => {\r\n      this.user = res.Entries!;\r\n      this.selectedBuildcaseList = [...(this.user.UserBuildCases ?? [])];\r\n      \r\n      setTimeout(() => {\r\n        this.checkAllItemEl.nativeElement.checked = true\r\n        this.checkItemEl.forEach(i => {          \r\n          let findBuildcae = res.Entries?.UserBuildCases?.find(item => item.CBuilCaseId?.toString() === i.nativeElement.id);\r\n          if(findBuildcae){\r\n            i.nativeElement.checked = true\r\n          } else {\r\n            this.checkAllItemEl.nativeElement.checked = false\r\n          }\r\n        })\r\n      }, 0);\r\n           \r\n      this.dialogService.open(dialog);\r\n    })\r\n  }\r\n\r\n  validation() {\r\n    this.valid.clear();\r\n    this.valid.required('[帳號]', this.user.CAccount);\r\n    this.valid.pattern('[帳號]', this.user.CAccount, this.pettern.AccountPettern, '只限英文字母及數字，長度介於3~20個字元!');\r\n    this.valid.required('[電子郵件]', this.user.CMail);\r\n    this.valid.pattern('[電子郵件]',this.user.CMail, this.pettern.MailPettern)\r\n    this.valid.required('[名稱]', this.user.CName);\r\n    if (this.isNew) {\r\n      this.valid.required('[密碼]', this.user.Password);\r\n      this.valid.required('[確認密碼]', this.user.CfmPassword);\r\n      this.valid.pattern('[確認密碼]', this.user.CfmPassword, this.pettern.PasswordPettern);\r\n      this.valid.pattern('[密碼]', this.user.Password, this.pettern.PasswordPettern);\r\n    }\r\n    this.valid.equal('[密碼]', '[確認密碼]', this.user.Password, this.user.CfmPassword);\r\n    if(this.user.ListUserGroup!.length < 1){\r\n      this.valid.required('[權限]', null);\r\n    }\r\n    this.valid.required('[狀態]', this.user.CStatus);\r\n    this.valid.required('[身分]', this.user.CUserType);\r\n\r\n    if(this.user.CUserType === 1 && this.selectedBuildcaseList.length < 1){\r\n      this.valid.addErrorMessage('[建案] 必填' )\r\n    }\r\n  }\r\n\r\n}\r\n"], "mappings": ";AACA,SAASA,aAAa,QAAQ,qCAAqC;AACnE,SAASC,SAAS,EAA0DC,SAAS,EAAEC,YAAY,QAAQ,eAAe;AAC1H,SAA0BC,YAAY,EAAEC,aAAa,EAAEC,cAAc,EAAEC,cAAc,EAAuBC,gBAAgB,QAAQ,gBAAgB;AAGpJ,SAAgCC,WAAW,QAAQ,gBAAgB;AAInE,SAASC,UAAU,QAAQ,oCAAoC;AAC/D,SAASC,IAAI,EAAEC,KAAK,QAAQ,iBAAiB;AAC7C,SAASC,YAAY,QAAQ,4CAA4C;AAQzE,SAASC,mBAAmB,QAAQ,kDAAkD;AACtF,SAASC,mBAAmB,QAAQ,kDAAkD;AAEtF,SAASC,kBAAkB,QAAQ,2DAA2D;AAC9F,SAASC,aAAa,QAAQ,MAAM;AACpC,SAASC,kBAAkB,QAAQ,4BAA4B;AAC/D,SAASC,kBAAkB,QAAQ,2CAA2C;AAuBvE,IAAMC,uBAAuB,GAA7B,MAAMA,uBAAwB,SAAQpB,aAAa;EAyBxDqB,YACUC,aAA8B,EAC9BC,KAAuB,EACvBC,WAAwB,EACxBC,gBAAkC,EAClCC,OAAuB,EACZC,KAAkB,EAC7BC,KAAuB,EACvBC,MAAc,EACdC,OAAsB,EACtBC,gBAAkC,EAClCC,UAAsB;IAE9B,KAAK,CAACL,KAAK,CAAC;IAZJ,KAAAL,aAAa,GAAbA,aAAa;IACb,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,OAAO,GAAPA,OAAO;IACI,KAAAC,KAAK,GAALA,KAAK;IAChB,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,UAAU,GAAVA,UAAU;IAnCX,KAAAC,SAAS,GAAG,CAAC;IACtB,KAAAC,OAAO,GAAG,IAAIrB,YAAY,EAAE;IAC5B,KAAAsB,QAAQ,GAAG,EAA2B;IACtC,KAAAC,eAAe,GAAG,EAA2B;IAE7C,KAAAC,UAAU,GAAG,EAAgC;IAE7C,KAAAC,KAAK,GAAG,KAAK;IAEb,KAAAC,aAAa,GAAG,CACd;MAAEC,KAAK,EAAE,CAAC;MAAEC,KAAK,EAAE;IAAI,CAAE,EACzB;MAAED,KAAK,EAAE,CAAC;MAAEC,KAAK,EAAE;IAAI,CAAE,CAC1B;IAED,KAAAC,QAAQ,GAAG,CACT;MAAEF,KAAK,EAAE,CAAC;MAAEG,IAAI,EAAE;IAAO,CAAE,EAC3B;MAAEH,KAAK,EAAE,CAAC;MAAEG,IAAI,EAAE;IAAO,CAAE,CAC5B;IACD,KAAAC,qBAAqB,GAAiB,EAAE;IAExC,KAAAC,aAAa,GAAgD,EAAE;IAmB7D,IAAI,CAACtB,KAAK,CAACuB,UAAU,CAACC,SAAS,CAACC,GAAG,IAAG;MACpC,IAAI,CAACb,QAAQ,GAAGa,GAAG;IACrB,CAAC,CAAC;IACF,IAAI,CAACzB,KAAK,CAAC0B,eAAe,CAACF,SAAS,CAACC,GAAG,IAAG;MACzC,IAAI,CAACX,UAAU,GAAGW,GAAG;IACvB,CAAC,CAAC;EAEJ;EAESE,QAAQA,CAAA;IACf,IAAI,CAACC,OAAO,EAAE;IACd,IAAI,CAACC,YAAY,EAAE;IACnB,IAAI,CAACC,gBAAgB,EAAE;EACzB;EAEA;EACAD,YAAYA,CAAA;IACV,IAAI,CAAC3B,gBAAgB,CAAC6B,4BAA4B,CAAC;MACjDC,IAAI,EAAE;QACJC,KAAK,EAAE,EAAE;QACTC,OAAO,EAAE,CAAC;QACVC,SAAS,EAAE,CAAC;QACZC,QAAQ,EAAE;;KAEb,CAAC,CACDC,IAAI,CAAC1C,kBAAkB,CAAC,IAAI,CAACc,UAAU,CAAC,CAAC,CACzCe,SAAS,CAACC,GAAG,IAAG;MACf,IAAI,CAACX,UAAU,GAAGW,GAAG,CAACa,OAAQ;MAC9B,IAAI,CAACtC,KAAK,CAACuC,YAAY,CAAC,IAAI,CAACzB,UAAU,CAAC;IAC1C,CAAC,CAAC;EACJ;EAEA;EACAc,OAAOA,CAAA;IACL,IAAI,CAAC3B,WAAW,CAACuC,uBAAuB,CAAC;MACvCR,IAAI,EAAE;QACJE,OAAO,EAAE,IAAI,CAACvB,OAAO,CAACuB,OAAO;QAC7BO,SAAS,EAAE,IAAI,CAAC9B,OAAO,CAAC8B;;KAE3B,CAAC,CACDJ,IAAI,CAAC1C,kBAAkB,CAAC,IAAI,CAACc,UAAU,CAAC,CAAC,CACzCe,SAAS,CAACC,GAAG,IAAG;MACf,IAAI,CAACb,QAAQ,GAAGa,GAAG,CAACa,OAAQ;MAC5B,IAAI,CAACI,YAAY,GAAGjB,GAAG,CAACkB,UAAW;MACnC,IAAI,CAACC,gBAAgB,EAAE;MACvB,IAAI,CAAC5C,KAAK,CAAC6C,OAAO,CAAC,IAAI,CAACjC,QAAQ,CAAC;IACnC,CAAC,CAAC;EACJ;EAEAgC,gBAAgBA,CAAA;IACd,IAAI,CAAClC,SAAS,GAAG,CAAC,IAAI,CAACoC,SAAS,GAAG,CAAC,IAAI,IAAI,CAACC,QAAQ,GAAG,CAAC;IACzD,IAAIC,SAAS,GAAI,IAAI,CAACN,YAAY,GAAG,IAAI,CAACI,SAAS,GAAG,IAAI,CAACC,QAAQ,GAAI,IAAI,CAACL,YAAY,GAAG,CAAC,GAAI,IAAI,CAACI,SAAS,GAAG,IAAI,CAACC,QAAS;IAC/H,IAAI,CAAClC,eAAe,GAAG,IAAI,CAACD,QAAQ,CAACqC,KAAK,CAAC,IAAI,CAACvC,SAAS,GAAG,CAAC,EAAEsC,SAAS,CAAC;EAC3E;EAGA;EACAE,OAAOA,CAAA;IACL,OAAOxD,aAAa,CAAC,IAAI,CAACO,WAAW,CAACkD,uBAAuB,CAAC;MAC5DnB,IAAI,EAAE;QACJoB,OAAO,EAAE,IAAI,CAACzC,OAAO,CAACyC,OAAQ,CAACC,QAAQ;;KAE1C,CAAC,CAAC,CAACC,IAAI,CAAC7B,GAAG,IAAG;MACb,IAAI,CAAC8B,IAAI,GAAG9B,GAAG,CAACa,OAAQ;IAC1B,CAAC,CAAC;EACJ;EAEAR,gBAAgBA,CAAA;IACd,IAAI,CAACtB,gBAAgB,CAACgD,qCAAqC,EAAE,CAC5DnB,IAAI,CAAC1C,kBAAkB,CAAC,IAAI,CAACc,UAAU,CAAC,CAAC,CAACe,SAAS,CAACC,GAAG,IAAG;MACzD,IAAI,CAACH,aAAa,GAAGG,GAAG,CAACa,OAAO;IAClC,CAAC,CAAC;EACJ;EAEAmB,aAAaA,CAACC,CAAK;IACjB,IAAI,CAACH,IAAI,CAACI,SAAS,GAAGD,CAAC;EACzB;EAEAE,OAAOA,CAACF,CAAK,EAAEG,IAA4B;IACzC,IAAIC,UAAU,GAAG,IAAI,CAACzC,qBAAqB,CAAC0C,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,WAAW,KAAKJ,IAAI,CAACK,GAAG,CAAC;IACjF,IAAG,CAACJ,UAAU,EAAC;MACb,IAAI,CAACzC,qBAAqB,CAAC8C,IAAI,CAAC;QAC9BF,WAAW,EAAEJ,IAAI,CAACK,GAAG;QACrBE,aAAa,EAAEP,IAAI,CAACQ;OACrB,CAAC;IACJ,CAAC,MAAM;MACL,IAAI,CAAChD,qBAAqB,GAAG,IAAI,CAACA,qBAAqB,CAACiD,MAAM,CAACN,CAAC,IAAIA,CAAC,CAACC,WAAW,KAAKJ,IAAI,CAACK,GAAG,CAAC;IACjG;EACF;EAEAK,eAAeA,CAACC,OAAW;IACzB;IACA,IAAI,CAACC,WAAW,CAACC,OAAO,CAACV,CAAC,IAAG;MAC3BA,CAAC,CAACW,aAAa,CAACH,OAAO,GAAGA,OAAO,CAACI,MAAM,CAACJ,OAAO,GAAG,IAAI,GAAG,KAAK;IACjE,CAAC,CAAC;IAEF;IACA,IAAGA,OAAO,CAACI,MAAM,CAACJ,OAAO,EAAC;MACxB,IAAI,CAACnD,qBAAqB,GAAG,IAAI,CAACC,aAAc,CAACuD,GAAG,CAACb,CAAC,IAAG;QACvD,OAAO;UACLC,WAAW,EAAED,CAAC,CAACE,GAAG;UAClBE,aAAa,EAAEJ,CAAC,CAACK;SAClB;MACH,CAAC,CAAC;IACJ,CAAC,MAAM;MACL,IAAI,CAAChD,qBAAqB,GAAG,EAAE;IACjC;EACF;EAEAyD,GAAGA,CAACC,MAAwB;IAC1B,IAAI,CAACxB,IAAI,GAAG;MACVyB,QAAQ,EAAE,EAAE;MACZC,WAAW,EAAE,EAAE;MACfhD,KAAK,EAAE,EAAE;MACTC,OAAO,EAAEgD,SAAS;MAClB9B,OAAO,EAAE,EAAE;MACX+B,aAAa,EAAE,EAAE;MACjBC,WAAW,EAAE,EAAE;MACfC,QAAQ,EAAE;KACX;IACD,IAAI,CAACtE,KAAK,GAAG,IAAI;IACjB,IAAI,CAAChB,aAAa,CAACuF,IAAI,CAACP,MAAM,CAAC;EACjC;EAEAQ,gBAAgBA,CAAA;IACd,QAAO,IAAI,CAAChC,IAAI,CAACI,SAAS;MACxB,KAAK,CAAC;QACJ,IAAI,CAACJ,IAAI,CAACiC,cAAc,GAAG,EAAE;QAC7B;MACF,KAAK,CAAC;QACJ,IAAI,CAACjC,IAAI,CAACiC,cAAc,GAAG,IAAI,CAACnE,qBAAqB,CAACwD,GAAG,CAACb,CAAC,IAAG;UAC5D,OAAO;YACLC,WAAW,EAAED,CAAC,CAACC,WAAW;YAC1BG,aAAa,EAAEJ,CAAC,CAACI;WAClB;QACH,CAAC,CAAC;QACF;MACF;IACF;EACF;EAEAqB,IAAIA,CAACC,GAAQ;IAEX,IAAI,CAACC,UAAU,EAAE;IACjB,IAAI,IAAI,CAACtF,KAAK,CAACuF,aAAa,CAACC,MAAM,GAAG,CAAC,EAAE;MACvC,IAAI,CAAC1F,OAAO,CAAC2F,aAAa,CAAC,IAAI,CAACzF,KAAK,CAACuF,aAAa,CAAC;MACpD;IACF;IAEA,IAAI,CAACL,gBAAgB,EAAE;IAEvB,IAAI,IAAI,CAACxE,KAAK,EAAE;MACd,IAAI,CAACd,WAAW,CAAC8F,uBAAuB,CAAC;QACvC/D,IAAI,EAAE,IAAI,CAACuB;OACZ,CAAC,CACDlB,IAAI,CAAC1C,kBAAkB,CAAC,IAAI,CAACc,UAAU,CAAC,CAAC,CACzCe,SAAS,CAACC,GAAG,IAAG;QACf,IAAIA,GAAG,CAACuE,UAAU,KAAK,CAAC,EAAE;UACxB,IAAI,CAAC7F,OAAO,CAAC8F,aAAa,CAAC,MAAM,CAAC;UAClC,IAAI,CAACrE,OAAO,EAAE;UACd8D,GAAG,CAACQ,KAAK,EAAE;QACb,CAAC,MAAM;UACL,IAAI,CAAC/F,OAAO,CAACgG,YAAY,CAAC,GAAG1E,GAAG,CAAC2E,OAAO,EAAE,CAAC;QAC7C;MACF,CAAC,CAAC;IACJ,CAAC,MAAM;MACL,IAAI,CAACnG,WAAW,CAACoG,wBAAwB,CAAC;QACxCrE,IAAI,EAAE,IAAI,CAACuB;OACZ,CAAC,CAAClB,IAAI,CAAC1C,kBAAkB,CAAC,IAAI,CAACc,UAAU,CAAC,CAAC,CAACe,SAAS,CAACC,GAAG,IAAG;QAC3D,IAAIA,GAAG,CAACuE,UAAU,KAAK,CAAC,EAAE;UACxB,IAAI,CAAC7F,OAAO,CAAC8F,aAAa,CAAC,MAAM,CAAC;UAClC,IAAI,CAACrE,OAAO,EAAE;UACd8D,GAAG,CAACQ,KAAK,EAAE;QACb,CAAC,MAAK;UACJ,IAAI,CAAC/F,OAAO,CAACgG,YAAY,CAAC,GAAG1E,GAAG,CAAC2E,OAAO,EAAE,CAAC;QAC7C;MACF,CAAC,CAAC;IACJ;EAEF;EAEAE,MAAMA,CAAA;IACJ,IAAI,CAACrG,WAAW,CAACsG,0BAA0B,CAAC;MAC1CvE,IAAI,EAAE;QACJoB,OAAO,EAAE,IAAI,CAACzC,OAAO,CAACyC,OAAO,CAACC,QAAQ;;KAEzC,CAAC,CAAChB,IAAI,CAAC1C,kBAAkB,CAAC,IAAI,CAACc,UAAU,CAAC,CAAC,CAACe,SAAS,CAACC,GAAG,IAAG;MAC3D,IAAGA,GAAG,CAACuE,UAAU,KAAK,CAAC,EAAE;QACvB,IAAI,CAAC7F,OAAO,CAAC8F,aAAa,CAAC,MAAM,CAAC;QAClC,IAAI,CAACrE,OAAO,EAAE;MAChB;IACF,CAAC,CAAC;EACJ;EAEA4E,QAAQA,CAACC,IAAyB;IAChC,IAAIC,MAAM,CAACC,OAAO,CAAC,SAAS,CAAC,EAAE;MAC7B,IAAI,CAAC1G,WAAW,CAACsG,0BAA0B,CAAC;QAC1CvE,IAAI,EAAE;UACJoB,OAAO,EAAEqD,IAAI,CAACrD;;OAEjB,CAAC,CAACf,IAAI,CAAC1C,kBAAkB,CAAC,IAAI,CAACc,UAAU,CAAC,CAAC,CAACe,SAAS,CAACC,GAAG,IAAG;QAC3D,IAAGA,GAAG,CAACuE,UAAU,KAAK,CAAC,EAAE;UACvB,IAAI,CAAC7F,OAAO,CAAC8F,aAAa,CAAC,MAAM,CAAC;UAClC,IAAI,CAACrE,OAAO,EAAE;QAChB;MACF,CAAC,CAAC;IACJ,CAAC,MAAM;MACL;IACF;EACF;EAEAgF,MAAMA,CAACH,IAAyB,EAAE1B,MAAwB;IACxD,IAAI,CAAChE,KAAK,GAAG,KAAK;IAClB,IAAI,CAACd,WAAW,CAACkD,uBAAuB,CAAC;MACvCnB,IAAI,EAAC;QACHoB,OAAO,EAAEqD,IAAI,CAACrD;;KAEjB,CAAC,CAACf,IAAI,CAAC1C,kBAAkB,CAAC,IAAI,CAACc,UAAU,CAAC,CAAC,CAACe,SAAS,CAACC,GAAG,IAAG;MAC3D,IAAI,CAAC8B,IAAI,GAAG9B,GAAG,CAACa,OAAQ;MACxB,IAAI,CAACjB,qBAAqB,GAAG,CAAC,IAAI,IAAI,CAACkC,IAAI,CAACiC,cAAc,IAAI,EAAE,CAAC,CAAC;MAElEqB,UAAU,CAAC,MAAK;QACd,IAAI,CAACC,cAAc,CAACnC,aAAa,CAACH,OAAO,GAAG,IAAI;QAChD,IAAI,CAACC,WAAW,CAACC,OAAO,CAACV,CAAC,IAAG;UAC3B,IAAI+C,YAAY,GAAGtF,GAAG,CAACa,OAAO,EAAEkD,cAAc,EAAEzB,IAAI,CAACF,IAAI,IAAIA,IAAI,CAACI,WAAW,EAAEZ,QAAQ,EAAE,KAAKW,CAAC,CAACW,aAAa,CAACqC,EAAE,CAAC;UACjH,IAAGD,YAAY,EAAC;YACd/C,CAAC,CAACW,aAAa,CAACH,OAAO,GAAG,IAAI;UAChC,CAAC,MAAM;YACL,IAAI,CAACsC,cAAc,CAACnC,aAAa,CAACH,OAAO,GAAG,KAAK;UACnD;QACF,CAAC,CAAC;MACJ,CAAC,EAAE,CAAC,CAAC;MAEL,IAAI,CAACzE,aAAa,CAACuF,IAAI,CAACP,MAAM,CAAC;IACjC,CAAC,CAAC;EACJ;EAEAY,UAAUA,CAAA;IACR,IAAI,CAACtF,KAAK,CAAC4G,KAAK,EAAE;IAClB,IAAI,CAAC5G,KAAK,CAAC6G,QAAQ,CAAC,MAAM,EAAE,IAAI,CAAC3D,IAAI,CAACyB,QAAQ,CAAC;IAC/C,IAAI,CAAC3E,KAAK,CAAC8G,OAAO,CAAC,MAAM,EAAE,IAAI,CAAC5D,IAAI,CAACyB,QAAQ,EAAE,IAAI,CAACzE,OAAO,CAAC6G,cAAc,EAAE,wBAAwB,CAAC;IACrG,IAAI,CAAC/G,KAAK,CAAC6G,QAAQ,CAAC,QAAQ,EAAE,IAAI,CAAC3D,IAAI,CAAC8D,KAAK,CAAC;IAC9C,IAAI,CAAChH,KAAK,CAAC8G,OAAO,CAAC,QAAQ,EAAC,IAAI,CAAC5D,IAAI,CAAC8D,KAAK,EAAE,IAAI,CAAC9G,OAAO,CAAC+G,WAAW,CAAC;IACtE,IAAI,CAACjH,KAAK,CAAC6G,QAAQ,CAAC,MAAM,EAAE,IAAI,CAAC3D,IAAI,CAACtB,KAAK,CAAC;IAC5C,IAAI,IAAI,CAAClB,KAAK,EAAE;MACd,IAAI,CAACV,KAAK,CAAC6G,QAAQ,CAAC,MAAM,EAAE,IAAI,CAAC3D,IAAI,CAAC8B,QAAQ,CAAC;MAC/C,IAAI,CAAChF,KAAK,CAAC6G,QAAQ,CAAC,QAAQ,EAAE,IAAI,CAAC3D,IAAI,CAAC0B,WAAW,CAAC;MACpD,IAAI,CAAC5E,KAAK,CAAC8G,OAAO,CAAC,QAAQ,EAAE,IAAI,CAAC5D,IAAI,CAAC0B,WAAW,EAAE,IAAI,CAAC1E,OAAO,CAACgH,eAAe,CAAC;MACjF,IAAI,CAAClH,KAAK,CAAC8G,OAAO,CAAC,MAAM,EAAE,IAAI,CAAC5D,IAAI,CAAC8B,QAAQ,EAAE,IAAI,CAAC9E,OAAO,CAACgH,eAAe,CAAC;IAC9E;IACA,IAAI,CAAClH,KAAK,CAACmH,KAAK,CAAC,MAAM,EAAE,QAAQ,EAAE,IAAI,CAACjE,IAAI,CAAC8B,QAAQ,EAAE,IAAI,CAAC9B,IAAI,CAAC0B,WAAW,CAAC;IAC7E,IAAG,IAAI,CAAC1B,IAAI,CAAC4B,aAAc,CAACU,MAAM,GAAG,CAAC,EAAC;MACrC,IAAI,CAACxF,KAAK,CAAC6G,QAAQ,CAAC,MAAM,EAAE,IAAI,CAAC;IACnC;IACA,IAAI,CAAC7G,KAAK,CAAC6G,QAAQ,CAAC,MAAM,EAAE,IAAI,CAAC3D,IAAI,CAACrB,OAAO,CAAC;IAC9C,IAAI,CAAC7B,KAAK,CAAC6G,QAAQ,CAAC,MAAM,EAAE,IAAI,CAAC3D,IAAI,CAACI,SAAS,CAAC;IAEhD,IAAG,IAAI,CAACJ,IAAI,CAACI,SAAS,KAAK,CAAC,IAAI,IAAI,CAACtC,qBAAqB,CAACwE,MAAM,GAAG,CAAC,EAAC;MACpE,IAAI,CAACxF,KAAK,CAACoH,eAAe,CAAC,SAAS,CAAE;IACxC;EACF;CAED;AAxR4BC,UAAA,EAA1B9I,YAAY,CAAC,WAAW,CAAC,C,2DAA6B;AAC5B8I,UAAA,EAA1B/I,SAAS,CAAC,cAAc,CAAC,C,8DAA4B;AAvB3CkB,uBAAuB,GAAA6H,UAAA,EArBnChJ,SAAS,CAAC;EACTiJ,QAAQ,EAAE,qBAAqB;EAC/BC,WAAW,EAAE,kCAAkC;EAC/CC,SAAS,EAAE,CAAC,kCAAkC,CAAC;EAC/CC,UAAU,EAAE,IAAI;EAChBC,OAAO,EAAE,CACPlJ,YAAY,EACZU,mBAAmB,EACnBT,aAAa,EACbI,WAAW,EACXH,cAAc,EACdC,cAAc,EACdI,IAAI,EACJC,KAAK,EACLG,mBAAmB,EACnBL,UAAU,EACVS,kBAAkB,EAClBX,gBAAgB,EAChBQ,kBAAkB;CAErB,CAAC,C,EACWI,uBAAuB,CA8SnC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}